package com.jd.international.service.fulfillment.impl;

import com.jd.fastjson.JSON;
import com.jd.fastjson.JSONObject;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.service.fulfillment.FulfillmentReadService;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.wisp.api.delivery.resp.DeliveryAgingResp;
import com.jdi.isc.fulfillment.soa.api.common.SystemInfoFulfillmentReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.promiseTime.PromiseTimeReadApiService;
import com.jdi.isc.fulfillment.soa.api.promiseTime.req.PromiseTimeMkuReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.promiseTime.req.PromiseTimeTextByMkuBatchReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2025/3/13 01:13
 **/
@Slf4j
@Service
public class FulfillmentReadServiceImpl implements FulfillmentReadService {

    @Resource
    private PromiseTimeReadApiService promiseTimeReadApiService;
    @Resource
    private AsyncTaskExecutor searchExecutorService;


    @Override
    public String getDeliveryDateText(BaseInfo baseInfo, Long mkuId, Integer reqNum){
        if (mkuId == null) {
            return null;
        }
        PromiseTimeTextByMkuBatchReqApiDTO req =new PromiseTimeTextByMkuBatchReqApiDTO();
        req.setClientCode(baseInfo.getClientId());
        PromiseTimeMkuReqApiDTO mku = new PromiseTimeMkuReqApiDTO();
        mku.setMkuId(mkuId);
        mku.setNum(reqNum);
        req.setMkuReqs(Arrays.asList(mku));
        SystemInfoFulfillmentReqApiDTO reqApiDTO = new SystemInfoFulfillmentReqApiDTO();
        reqApiDTO.setLang(baseInfo.getEnv());
        Set<String> langs = new HashSet<>();
        langs.add(baseInfo.getEnv());
        reqApiDTO.setLangList(langs);
        reqApiDTO.setUserIp(baseInfo.getUserIp());
        reqApiDTO.setSystemCode("international-api");
        req.setSystemInfoReq(reqApiDTO);
        try {
            DataResponse<Map<Long, String>> mapDataResponse = promiseTimeReadApiService.queryTextSingleLangByMkuBatch(req);

            if (mapDataResponse != null && mapDataResponse.getSuccess() && MapUtils.isNotEmpty(mapDataResponse.getData())) {
                return mapDataResponse.getData().get(mkuId);
            } else {
                log.warn("getDeliveryDateText failed, req:{}, resp:{}", JSON.toJSONString(req), JSON.toJSONString(mapDataResponse));
            }
        } catch (Exception e) {
            log.error("getDeliveryDateText error, req:{}", JSON.toJSONString(req), e);
        }
        return null;
    }

    @Override
    public String getDeliveryDateTextAsync(BaseInfo baseInfo, Long mkuId, Integer reqNum) {
        try {
            CompletableFuture<String> future = CompletableFuture
                    .supplyAsync(() -> {
                        try {
                            return this.getDeliveryDateText(baseInfo, mkuId, reqNum);
                        } catch (Exception e) {
                            log.warn("获取履约时效文案异常, baseInfo: {}, mkuId: {}, reqNum: {}",
                                    JSONObject.toJSONString(baseInfo), mkuId, reqNum, e);
                            return "";
                        }
                    }, searchExecutorService);

            return future.get(100, TimeUnit.MILLISECONDS);

        } catch (TimeoutException e) {
            log.warn("获取履约时效文案超时100ms, baseInfo: {}, mkuId: {}, reqNum: {}",
                    JSONObject.toJSONString(baseInfo), mkuId, reqNum);
            return "";
        } catch (Exception e) {
            log.warn("获取履约时效文案异常, baseInfo: {}, mkuId: {}, reqNum: {}",
                    JSONObject.toJSONString(baseInfo), mkuId, reqNum, e);
            return "";
        }
    }

}
