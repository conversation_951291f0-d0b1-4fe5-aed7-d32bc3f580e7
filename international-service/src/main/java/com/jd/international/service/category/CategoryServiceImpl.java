package com.jd.international.service.category;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.exception.BizException;
import com.jd.international.domain.category.CategoryDTO;
import com.jd.international.domain.category.GetCategoryInfoVo;
import com.jd.international.rpc.category.RpcCategoryService;
import com.jd.international.rpc.isc.mku.RpcProductReadService;
import com.jd.international.soa.sdk.common.category.res.CategoryTreeRes;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientDetailReqDTO;
import com.jd.k2.gd.boost.dto.ReflectDataDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务逻辑层实现类
 *
 * <AUTHOR>
 * @since 2022/10/14 16:35
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CategoryServiceImpl implements CategoryService {

    private final RpcCategoryService rpcCategoryService;
//    private final RpcIscMkuService rpcIscMkuService;
    private final RpcProductReadService rpcProductReadService;

    @Override
    public List<CategoryDTO> getTreeCategory(String pin, Boolean isEn) {
        List<CategoryTreeRes> categoryTree;
        try {
            categoryTree = rpcCategoryService.getCategoryTree(pin);
        } catch (BizException e) {
            log.error("CategoryServiceImpl.getTreeCategory has error 【{}】", e.getMessage());
            throw e;

        }
        List<CategoryDTO> categoryDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(categoryTree)) {
            for (CategoryTreeRes rootCategory : categoryTree) {
                CategoryDTO root = new CategoryDTO();
                root.setCatId(rootCategory.getCatId());
                root.setCatName(isEn ? rootCategory.getCatNameEn() : rootCategory.getCatNameCn());
                List<CategoryDTO> secCategoryList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(rootCategory.getChildCat())) {
                    for (CategoryTreeRes secCategory : rootCategory.getChildCat()) {
                        CategoryDTO secCategoryDto = new CategoryDTO();
                        secCategoryDto.setCatId(secCategory.getCatId());
                        secCategoryDto.setCatName(isEn ? secCategory.getCatNameEn() : secCategory.getCatNameCn());
                        List<CategoryDTO> thirdCategoryList = new ArrayList<>();
                        if (!CollectionUtils.isEmpty(secCategory.getChildCat())) {
                            for (CategoryTreeRes thirdCategory : secCategory.getChildCat()) {
                                CategoryDTO thirdCategoryDto = new CategoryDTO();
                                thirdCategoryDto.setCatId(thirdCategory.getCatId());
                                thirdCategoryDto.setCatName(isEn ? thirdCategory.getCatNameEn() : thirdCategory.getCatNameCn());
                                List<CategoryDTO> fourthCategoryList = new ArrayList<>();
                                if (!CollectionUtils.isEmpty(thirdCategory.getChildCat())) {
                                    for (CategoryTreeRes fourthCategoryTree : thirdCategory.getChildCat()) {
                                        CategoryDTO fourthCategory = new CategoryDTO();
                                        fourthCategory.setCatId(fourthCategoryTree.getCatId());
                                        fourthCategory.setCatName(isEn ? fourthCategoryTree.getCatNameEn() : fourthCategoryTree.getCatNameCn());
                                        fourthCategoryList.add(fourthCategory);
                                    }
                                }
                                thirdCategoryDto.setCategoryDTOList(fourthCategoryList);
                                thirdCategoryList.add(thirdCategoryDto);
                            }
                        }
                        secCategoryDto.setCategoryDTOList(thirdCategoryList);
                        secCategoryList.add(secCategoryDto);
                    }
                }
                root.setCategoryDTOList(secCategoryList);
                categoryDTOList.add(root);
            }
        }
        return categoryDTOList;
    }

    @Override
    public GetCategoryInfoVo getCategoryInfo(Long categoryId, BaseInfo baseInfo) {
        GetCategoryInfoVo getCategoryInfoVo = new GetCategoryInfoVo();
        String env = baseInfo.getEnv();
        CategoryDTO targetCategory = new CategoryDTO();
        Boolean isEn = StringUtils.equals("en", env.toUpperCase());
        List<CategoryTreeRes> categoryTree = rpcCategoryService.getCategoryTree(baseInfo.getPin());
        if (CollectionUtils.isNotEmpty(categoryTree)) {
            for (CategoryTreeRes categoryRoot : categoryTree) {
                // 便利子集类目
                if (CollectionUtils.isNotEmpty(categoryRoot.getChildCat())) {
                    // 二级类目不为空
                    for (CategoryTreeRes categorySecond : categoryRoot.getChildCat()) {
                        if (categorySecond.getCatId().equals(categoryId)) {
                            // 匹配到对应的数据
                            List<CategoryDTO> secondTree = new ArrayList<>();
                            for (CategoryTreeRes categoryTreeRes : categoryRoot.getChildCat()) {
                                CategoryDTO categoryDTO = new CategoryDTO();
                                categoryDTO.setCatName(isEn ? categoryTreeRes.getCatNameEn() : categoryTreeRes.getCatNameCn());
                                categoryDTO.setCatId(categoryTreeRes.getCatId());
                                secondTree.add(categoryDTO);
                            }
                            targetCategory.setCatName(isEn ? categoryRoot.getCatNameEn() : categoryRoot.getCatNameCn());
                            targetCategory.setCatId(categoryRoot.getCatId());
                            targetCategory.setCategoryDTOList(secondTree);
                        } else {
                            // 未匹配到 使用子集类目
                            List<CategoryTreeRes> thirdCategory = categorySecond.getChildCat();
                            if (CollectionUtils.isNotEmpty(thirdCategory)) {
                                for (CategoryTreeRes categoryThirdRes : thirdCategory) {
                                    // 开始遍历三级类目
                                    List<CategoryDTO> thirdCategoryDto = new ArrayList<>();
                                    if (categoryThirdRes.getCatId().equals(categoryId)) {
                                        // 如果匹配到 ， 开始组装数据 当前三级类目全部
                                        for (CategoryTreeRes categoryTreeRes : thirdCategory) {
                                            CategoryDTO categoryDTO = new CategoryDTO();
                                            categoryDTO.setCatName(isEn ? categoryTreeRes.getCatNameEn() : categoryTreeRes.getCatNameCn());
                                            categoryDTO.setCatId(categoryTreeRes.getCatId());
                                            thirdCategoryDto.add(categoryDTO);
                                        }
                                        List<CategoryDTO> secondCategoryList = new ArrayList<>();
                                        // 组装二级类目
                                        CategoryDTO secondCategoryDto = new CategoryDTO();
                                        secondCategoryDto.setCatId(categorySecond.getCatId());
                                        secondCategoryDto.setCatName(isEn ? categorySecond.getCatNameEn() : categorySecond.getCatNameCn());
                                        secondCategoryDto.setCategoryDTOList(thirdCategoryDto);
                                        for (CategoryTreeRes categoryTreeRes : categoryRoot.getChildCat()) {
                                            CategoryDTO secondTree = new CategoryDTO();
                                            secondTree.setCatId(categoryTreeRes.getCatId());
                                            secondTree.setCatName(isEn ? categoryTreeRes.getCatNameEn() : categoryTreeRes.getCatNameCn());
                                            secondCategoryList.add(secondTree);
                                        }
                                        secondCategoryList.add(secondCategoryDto);
                                        targetCategory.setCatName(isEn ? categoryRoot.getCatNameEn() : categoryRoot.getCatNameCn());
                                        targetCategory.setCatId(categoryRoot.getCatId());
                                        targetCategory.setCategoryDTOList(secondCategoryList);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        getCategoryInfoVo.setCategoryDTO(targetCategory);
        getCategoryInfoVo.setCateId(categoryId);
        return getCategoryInfoVo;
    }

    @Override
    public List<Map<String, String>> categoryName(String category, BaseInfo baseInfo) {
        List<CategoryTreeRes> categoryTree = rpcCategoryService.getCategoryTree(baseInfo.getPin());
        List<Map<String, String>> res = new ArrayList<>();
        Boolean isEn = StringUtils.equals("EN", baseInfo.getEnv().toUpperCase());
        if (CollectionUtils.isNotEmpty(categoryTree)) {
            String[] categories = category.split(",");
            for (int i = 0, size = categories.length; i < size; i++) {
                if (CollectionUtils.isNotEmpty(categoryTree)) {
                    String categoryId = categories[i];
                    Optional<CategoryTreeRes> cat = categoryTree.stream().filter(cate -> categoryId.equals(cate.getCatId().toString())).findFirst();
                    if (cat.isPresent()) {
                        Map<String, String> map = new HashMap<>();
                        map.put(categories[i], (isEn && StringUtils.isNotBlank(cat.get().getCatNameEn())) ? cat.get().getCatNameEn() : cat.get().getCatNameCn());
                        categoryTree = cat.get().getChildCat();
                        res.add(map);
                    }
                }
            }
        }
        return res;
    }

    @Override
    public List<CategoryTreeRes> nextCategory(String category, BaseInfo baseInfo) {
        List<CategoryTreeRes> categoryTree = rpcCategoryService.getCategoryTree(baseInfo.getPin());
        if (CollectionUtils.isNotEmpty(categoryTree)) {
            String[] categories = category.split(",");
            for (int i = 0, size = categories.length; i < size; i++) {
                if (CollectionUtils.isNotEmpty(categoryTree)) {
                    String a = categories[i];
                    Optional<CategoryTreeRes> catId = categoryTree.stream().filter(cate -> a.equals(cate.getCatId().toString())).findFirst();
                    if (catId.isPresent()) {
                        categoryTree = catId.get().getChildCat();
                        //取英文信息
                        if ("en".equals(baseInfo.getEnv())) {
                            categoryTree.forEach(item -> item.setCatNameCn(item.getCatNameEn()));
                        }
                    } else {
                        categoryTree = null;
                    }
                }
            }
            return categoryTree;
        }
        return null;
    }

    @Override
    public Map<String, String> batchQueryReflectData(List<String> mkuIdList, String contractNum, BaseInfo baseInfo) {
        Map<Long, Long> skuIdCatIdMap = mkuIdList.stream().map(mkuId -> {
                    IscMkuClientDetailReqDTO mkuDto = new IscMkuClientDetailReqDTO();
                    mkuDto.setMkuId(Long.valueOf(mkuId));
                    mkuDto.setClientCode(baseInfo.getClientId());
                    mkuDto.setStationType(baseInfo.getStationType());
                    mkuDto.setLang(baseInfo.getEnv());
                    return rpcProductReadService.baseInfo(mkuDto);
                }
        ).filter(Objects::nonNull).collect(Collectors.toMap(IscMkuClientDTO::getMkuId, IscMkuClientDTO::getCatId));
        log.info("CategoryServiceImpl.batchQueryReflectData, skuIdCatIdMap={}", JSON.toJSONString(skuIdCatIdMap));

        Set<Long> catIdSet = new HashSet<>(skuIdCatIdMap.values());

        ReflectDataDto dto = new ReflectDataDto();
        dto.setQueryKey(catIdSet.stream().map(String::valueOf).collect(Collectors.joining(",")));
        dto.setContractNo(contractNum);
        dto.setDataType(1);
        dto.setReflectType("other");
        dto.setOtherType("0");

        List<ReflectDataDto> reflectDataDtos = null;
        try {
            reflectDataDtos = rpcCategoryService.batchQueryReflectData(dto);
        } catch (Exception e) {
            log.error("CategoryServiceImpl.batchQueryReflectData has error, mkuIdList={}, contractNum={}", mkuIdList, contractNum, e);
        }
        if (CollectionUtils.isEmpty(reflectDataDtos)) {
            return skuIdCatIdMap.entrySet().stream()
                    .collect(Collectors.toMap(
                            e -> String.valueOf(e.getKey()),
                            e -> String.valueOf(e.getValue())));
        }
        log.info("CategoryServiceImpl.batchQueryReflectData, reflectDataDtos={}", JSONArray.toJSONString(reflectDataDtos));
        Map<String, ReflectDataDto> catReflectMap = reflectDataDtos.stream().collect(Collectors.toMap(ReflectDataDto::getQueryKey, item -> item));
        return skuIdCatIdMap.entrySet().stream()
                .collect(Collectors.toMap(e -> String.valueOf(e.getKey()),
                                e -> {
                                    ReflectDataDto reflectDataDto = catReflectMap.getOrDefault(String.valueOf(e.getValue()), new ReflectDataDto());
                                    if (StringUtils.isBlank(reflectDataDto.getResultExt())) {
                                        return String.valueOf(e.getValue());
                                    }
                                    return parseCustomerClassId(reflectDataDto);
                                }
                        )
                );
    }

    private String parseCustomerClassId(ReflectDataDto dto) {
        String resultExt = dto.getResultExt();
        JSONObject jsonObject = JSON.parseObject(resultExt);
        return jsonObject.getString("客户类目ID");
    }
}
