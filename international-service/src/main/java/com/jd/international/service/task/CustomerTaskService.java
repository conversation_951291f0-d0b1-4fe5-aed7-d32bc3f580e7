package com.jd.international.service.task;

import com.jd.international.common.bean.BaseInfo;
import com.jd.international.domain.task.biz.CustomerTaskPageVO;
import com.jd.international.domain.task.req.CustomerTaskDetailReqVO;
import com.jd.international.domain.task.req.CustomerTaskSaveUpdateReqVO;
import com.jd.international.domain.task.res.CustomerTaskResVO;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.task.center.api.common.enums.TaskCreateTypeEnum;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Description: 销端异步任务服务
 * @Author: zhaojianguo21
 * @Date: 2024/08/16 13:33
 **/

public interface CustomerTaskService {

    /**
     * 文件导入
     * @param file
     * @param importType
     * @param baseInfo
     * @return
     */
    DataResponse<String> importData(BaseInfo baseInfo, MultipartFile file, Integer importType, TaskCreateTypeEnum taskCreateTypeEnum);

    /**
     * 保存更新
     * @param baseInfo
     * @param input 查询条件
     * @return 查询结果
     */
    DataResponse<CustomerTaskResVO> saveOrUpdate(BaseInfo baseInfo, CustomerTaskSaveUpdateReqVO input);

    /**
     * 详情查询
     * @param input 查询条件
     * @return 查询结果
     */
    CustomerTaskResVO detail(CustomerTaskDetailReqVO input);

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    PageInfo<CustomerTaskPageVO.Response> pageSearch(CustomerTaskPageVO.Request input);

}
