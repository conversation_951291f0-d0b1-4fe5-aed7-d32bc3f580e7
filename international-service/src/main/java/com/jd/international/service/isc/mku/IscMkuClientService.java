package com.jd.international.service.isc.mku;

import com.jd.international.common.bean.BaseInfo;
import com.jd.international.domain.isc.mku.*;
import com.jd.international.soa.sdk.common.isc.mku.req.MkuReplenishmentSoaDTO;
import com.jd.international.soa.sdk.common.search.req.SearchReq;
import com.jd.international.soa.sdk.common.search.resp.BusinessCardResp;
import com.jd.international.soa.sdk.common.search.resp.SearchResp;
import com.jdi.common.domain.rpc.bean.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * @Description: 销端服务接口
 * @Author: zhaojianguo21
 * @Date: 2023/12/15 14:38
 **/
public interface IscMkuClientService {

//    /**
//     * 商品列表
//     * @param input 列表参数
//     * @param baseInfo 请求基本参数
//     * @return 商品列表
//     */
//    @Deprecated
//    PageInfo<IscAppMkuClientPageResDTO> page(IscAppMkuClientPageReqDTO input, BaseInfo baseInfo);
//
//    /**
//     * 商品列表
//     * @param searchReq 列表参数
//     * @param baseInfo 请求基本参数
//     * @return 商品列表
//     */
//    SearchResp page(SearchReq searchReq, BaseInfo baseInfo);

    /**
     * mku信息
     * @param input 请求参数
     * @param baseInfo 请求基本参数
     * @return mku描述
     */
    IscAppMkuAggreDTO baseInfo(IscAppMkuClientDetailReqDTO input, BaseInfo baseInfo);

//    /**
//     * 是否存在mku
//     * @param input 请求参数
//     * @return true/false
//     */
//    Boolean existsMku(IscAppMkuClientDetailReqDTO input, BaseInfo baseInfo);

//    /**
//     * 查同mku的同组聚堆信息
//     * @param input 请求参数
//     * @param baseInfo 请求基本参数
//     * @return 同组商品聚堆信息
//     */
//    IscAppMkuClientGroupDTO groupInfo(IscAppMkuClientDetailReqDTO input, BaseInfo baseInfo);
//
//    /**
//     * 商品列表页单个商品卡片信息
//     * @param brandId 品牌ID
//     * @return 商品卡片信息
//     */
//    BusinessCardResp cardInfo(String brandId, BaseInfo baseInfo);

    /** 申请补货*/
    Boolean replenishment(MkuReplenishmentSoaDTO req);

    /**
     * 商品列表
     * @param searchReq 列表参数
     * @param baseInfo 请求基本参数
     * @return 商品列表
     */
    SearchResp pageEs(SearchReq searchReq, BaseInfo baseInfo);

    /**
     * 新入池商品
     * @param baseInfo
     * @return
     */
    SearchResp latestWares(BaseInfo baseInfo);

    /**
     * 根据条件查询 MKU 特殊属性。
     * @param reqDTO 查询请求对象，包含查询条件。
     * @return 返回一个 Map，键为 MKU ID，值为该 MKU 的特殊属性 Map。
     */
    Map<Long, Map<String, String>> queryMkuSpecialAttr(IscAppMkuClientSpecialAttrReqDTO reqDTO);

    /**
     * 根据基本信息和 MKU ID 列表获取浏览数据。
     * @param baseInfo 基本信息对象，包含必要的上下文信息。
     * @param mkuIds MKU ID 列表，用于筛选需要获取的浏览数据。
     * @return 包含浏览数据的对象。
     */
    SearchResp getBrowsing(BaseInfo baseInfo, List<String> mkuIds);

    /**
     * 推荐商品
     * @param baseInfo
     * @return
     */
    SearchResp recommendWares(BaseInfo baseInfo);
}
