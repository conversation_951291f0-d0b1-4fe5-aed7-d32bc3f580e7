package com.jd.international.service.isc.mku.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.fastjson.JSON;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.config.LoginFreeDuccConfig;
import com.jd.international.common.config.ProductDUCCConfig;
import com.jd.international.common.constants.CommonConstant;
import com.jd.international.common.utils.S3Utils;
import com.jd.international.domain.customer.CustomerVO;
import com.jd.international.domain.isc.mku.*;
import com.jd.international.domain.material.res.MkuMaterialResVO;
import com.jd.international.domain.product.*;
import com.jd.international.domain.stock.ProductStockInfoRes;
import com.jd.international.rpc.isc.mku.RpcIscMkuService;
import com.jd.international.rpc.isc.mku.RpcProductReadService;
import com.jd.international.rpc.material.MkuMaterialRpcService;
import com.jd.international.service.adapter.isc.mku.MkuConvert;
import com.jd.international.service.adapter.isc.mku.MkuStockConvert;
import com.jd.international.service.adapter.material.MkuMaterialConvert;
import com.jd.international.service.adapter.product.ProductConvert;
import com.jd.international.service.authority.AuthorityService;
import com.jd.international.service.browsing.BrowsingHistoryService;
import com.jd.international.service.currency.CurrencySymbolService;
import com.jd.international.service.customer.CustomerService;
import com.jd.international.service.fulfillment.FulfillmentReadService;
import com.jd.international.service.isc.mku.IscMkuClientService;
import com.jd.international.service.isc.utils.ReqBaseInfoUtil;
import com.jd.international.service.stock.StockReadService;
import com.jd.international.service.wares.RecommendMkuService;
import com.jd.international.soa.base.skuSale.SkuSaleStateResp;
import com.jd.international.soa.sdk.common.common.ProductPricesDTO;
import com.jd.international.soa.sdk.common.isc.brand.domain.biz.IscBrandClientDTO;
import com.jd.international.soa.sdk.common.isc.category.domain.biz.IscCategoryTreeNodeDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.*;
import com.jd.international.soa.sdk.common.isc.mku.req.MkuReplenishmentSoaDTO;
import com.jd.international.soa.sdk.common.search.req.SearchReq;
import com.jd.international.soa.sdk.common.search.resp.BusinessCardResp;
import com.jd.international.soa.sdk.common.search.resp.SearchPageResp;
import com.jd.international.soa.sdk.common.search.resp.SearchResp;
import com.jd.international.soa.sdk.common.search.resp.SearchWaresResp;
import com.jd.org.msgpack.type.ValueType;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.fulfillment.soa.api.promiseTime.PromiseTimeReadApiService;
import com.jdi.isc.fulfillment.soa.api.promiseTime.req.PromiseTimeTagBatchReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.promiseTime.req.PromiseTimeTagReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.promiseTime.res.PromiseTimeTagResApiDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.material.res.MkuMaterialApiDTO;
import com.jdi.isc.product.soa.api.mku.req.IscMkuAvailableSaleReq;
import com.jdi.isc.product.soa.api.mku.req.SpecialAttrMkuClientReqDTO;
import com.jdi.isc.product.soa.api.mku.IscProductSoaMkuReadApiService;
import com.jdi.isc.product.soa.api.mku.res.IscMkuAvailableSaleResDTO;
import com.jdi.isc.product.soa.api.wisp.brand.biz.BrandClientApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientLatestWareReqApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientStockResApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuListInfoReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2023/12/16 17:48
 **/
@Slf4j
@Service
public class IscMkuClientServiceImpl implements IscMkuClientService {

    @Resource
    private RpcIscMkuService rpcIscMkuService;
    @Resource
    private LoginFreeDuccConfig loginFreeDUCCConfig;
    @Resource
    private ProductDUCCConfig productDUCCConfig;
    @Resource
    private CurrencySymbolService currencySymbolService;
    @Resource
    private CustomerService customerService;
    @Resource
    private AuthorityService authorityService;
    @Resource
    private MkuMaterialRpcService mkuMaterialRpcService;
    @Resource
    private S3Utils s3Utils;
    @Value("${config.mkuContext}")
    private String mkuContext;
    @Value("${es.search}")
    private String esSearch;
    @Value("${es.search.filterRegex}")
    private String filterRegex;

    @Resource
    private BrowsingHistoryService browsingHistoryService;
    @Resource
    private RecommendMkuService recommendMkuService;
    @Resource
    private StockReadService stockReadService;
    @Resource
    private FulfillmentReadService fulfillmentReadService;

    @Resource
    private PromiseTimeReadApiService promiseTimeReadApiService;
    @Resource
    private RpcProductReadService rpcProductReadService;
    @Resource
    private IscProductSoaMkuReadApiService iscProductSoaMkuReadApiService;
//    @Override
//    public PageInfo<IscAppMkuClientPageResDTO> page(IscAppMkuClientPageReqDTO input, BaseInfo baseInfo) {
//        IscMkuClientPageReqDTO reqDTO = MkuConvert.INSTANCE.iscAppMkuClientPageReqDTO2ReqDTO(input);
//        ReqBaseInfoUtil.copyBaseInfo(baseInfo, reqDTO);
//
//        PageInfo<IscMkuClientDTO> response = rpcIscMkuService.page(reqDTO);
//        if (null == response) {
//            log.warn("response fail. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//        return MkuConvert.INSTANCE.iscMkuClientDTO2PageResAppDTO(response);
//    }
//
//    @Override
//    public SearchResp page(SearchReq searchReq, BaseInfo baseInfo) {
//
//        if (baseInfo.isJinMen()) {
//            return this.pageEs(searchReq, baseInfo);
//        }
//
//        // 条件符合后进入es搜索
//        if (esSearch != null && esSearch.equals(CommonConstant.STRING_NUMBER_ONE)) {
//            List<String> noEsList = Arrays.asList(CountryConstant.COUNTRY_ZH);
//            CustomerVO customerVO = customerService.getCustomerByClientCode(baseInfo.getClientId());
//            if (null != customerVO
//                    && StringUtils.isNotBlank(customerVO.getCountry())
//                    && !noEsList.contains(customerVO.getCountry())) {
//                return this.pageEs(searchReq, baseInfo);
//            }
//        }
//
//        int pageSize = (null != productDUCCConfig && null != productDUCCConfig.getPageSize() && productDUCCConfig.getPageSize() > 0 && productDUCCConfig.getPageSize() < 100)
//                ? productDUCCConfig.getPageSize() : 48;
//
//        if (null == searchReq.getPageNo() || null == searchReq.getPageSize()) {
//            log.info("page, param null, set default val. searchReq={}", JSONObject.toJSONString(searchReq));
//            searchReq.setPageNo(1);
//            searchReq.setPageSize(pageSize);
//        }
//
//        if (searchReq.getPageSize() > pageSize) {
//            log.info("page, pageSize invalid, pageSize={}", searchReq.getPageSize());
//            searchReq.setPageSize(pageSize);
//        }
//
//        int loginFreeMaxPageNo = null != loginFreeDUCCConfig && null != loginFreeDUCCConfig.getMaxPageNo() ? loginFreeDUCCConfig.getMaxPageNo() : 10;
//        if (StringUtils.isNotBlank(baseInfo.getOpenArea())) {
//            if (searchReq.getPageNo() > loginFreeMaxPageNo) {
//                log.info("page, open visit, pageNo invalid, openArea={}, pageNo={}", baseInfo.getOpenArea(), searchReq.getPageNo());
//                searchReq.setPageNo(1);
//            }
//        }
//
//        IscMkuClientPageReqDTO reqDTO = new IscMkuClientPageReqDTO();
//        reqDTO.setIndex(searchReq.getPageNo().longValue());
//        reqDTO.setSize(searchReq.getPageSize().longValue());
//        reqDTO.setName(StringUtils.isNotBlank(searchReq.getKey()) ? searchReq.getKey().trim() : null);
//        log.info("page, reqDTO={}, pageSizeDucc={}, loginFreeMaxPageNoDucc={}", JSONObject.toJSONString(reqDTO), pageSize, loginFreeMaxPageNo);
//
//        if (StringUtils.isNotBlank(searchReq.getCat())) {
//            String cat = searchReq.getCat();
//            String catId = cat.split(",")[0];
//            reqDTO.setCatId(Long.parseLong(catId));
//        }
//
//        ReqBaseInfoUtil.copyBaseInfo(baseInfo, reqDTO);
//
//        PageInfo<IscMkuClientDTO> pageInfo = rpcIscMkuService.page(reqDTO);
//        if (null == pageInfo) {
//            log.warn("page, response fail. input={}, baseInfo={}", JSONObject.toJSONString(searchReq), JSONObject.toJSONString(baseInfo));
//            return null;
//        }
//
//        SearchResp searchResp = new SearchResp();
//        copyPageInfo(baseInfo, pageInfo, searchResp, loginFreeMaxPageNo, pageSize);
//        log.info("searchWares, searchReq={}, baseInfo={}, resp={}", JSON.toJSONString(searchReq), JSON.toJSONString(baseInfo), JSON.toJSONString(searchResp));
//        return searchResp;
//    }
//
//    @Override
//    public Boolean existsMku(IscAppMkuClientDetailReqDTO input, BaseInfo baseInfo) {
//        IscMkuClientDetailReqDTO reqDTO = MkuConvert.INSTANCE.iscAppMkuClientDetailReqDTO2ReqDTO(input);
//        ReqBaseInfoUtil.copyBaseInfo(baseInfo, reqDTO);
//
//        log.info("existsMku, reqDTO={}", JSONObject.toJSONString(reqDTO));
//        Boolean existsMku = rpcIscMkuService.existsMku(reqDTO);
//        log.info("existsMku, existsMku={}, reqDTO={}", existsMku, JSONObject.toJSONString(reqDTO));
//        return existsMku;
//    }

    @Override
    public IscAppMkuAggreDTO baseInfo(IscAppMkuClientDetailReqDTO input, BaseInfo baseInfo) {
        IscMkuClientDetailReqDTO reqDTO = MkuConvert.INSTANCE.iscAppMkuClientDetailReqDTO2ReqDTO(input);
        ReqBaseInfoUtil.copyBaseInfo(baseInfo, reqDTO);

        IscMkuClientDTO iscMkuClientDTO = rpcProductReadService.baseInfo(reqDTO);
        if (null == iscMkuClientDTO) {
            log.warn("IscMkuClientDTO not exists. input={}", JSONObject.toJSONString(input));
            return null;
        }
        dealBrowsingHistory(iscMkuClientDTO.getMkuId(), StringUtils.isNotBlank(baseInfo.getSubAccount()) ? baseInfo.getSubAccount() : baseInfo.getPin());
        //外接逻辑，进入详情的时候 处理浏览记录
        return copyToIscAppMkuAggreDTO(baseInfo, iscMkuClientDTO);
    }

    /**
     * 记录用户的浏览历史。
     * @param mkuId 商品ID。
     * @param redisKey 客户唯一标识。
     */
    private void dealBrowsingHistory(Long mkuId, String redisKey) {
        try {
            if (StringUtils.isBlank(redisKey)) {
                log.info("redisKey 客户唯一标识 为空，暂不记录");
                return;
            }
            browsingHistoryService.recordProductView(redisKey, String.valueOf(mkuId));
        } catch (Exception e) {
            log.error("存储浏览记录异常clientId:{} mkuId:{}", redisKey, mkuId);
        }
    }

//    @Override
//    public IscAppMkuClientGroupDTO groupInfo(IscAppMkuClientDetailReqDTO input, BaseInfo baseInfo) {
//        IscMkuClientDetailReqDTO reqDTO = MkuConvert.INSTANCE.iscAppMkuClientDetailReqDTO2ReqDTO(input);
//        ReqBaseInfoUtil.copyBaseInfo(baseInfo, reqDTO);
//
//        IscMkuClientGroupDTO iscMkuClientGroupVO = rpcIscMkuService.groupInfo(reqDTO);
//        if (null == iscMkuClientGroupVO) {
//            log.warn("IscMkuClientGroupVO not exists. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//
//        return MkuConvert.INSTANCE.iscMkuClientGroupDTO2AppDTO(iscMkuClientGroupVO);
//    }
//
//    @Override
//    public BusinessCardResp cardInfo(String brandId, BaseInfo baseInfo) {
//        if (StringUtils.isBlank(brandId)) {
//            return null;
//        }
//        IscMkuClientCardInfoReqDTO reqDTO = new IscMkuClientCardInfoReqDTO();
//        ReqBaseInfoUtil.copyBaseInfo(baseInfo, reqDTO);
//        reqDTO.setBrandId(Long.parseLong(brandId));
//
//        IscMkuClientCardInfoResDTO resDTO = rpcIscMkuService.cardInfo(reqDTO);
//
//        if (null == resDTO || null == resDTO.getBrandInfo()) {
//            log.warn("IscMkuClientCardInfoResDTO null. brandId={}, resDTO={}", brandId, JSONObject.toJSONString(resDTO));
//            return null;
//        }
//
//        BusinessCardResp cardResp = new BusinessCardResp();
//        cardResp.setBrand(resDTO.getBrandInfo().getName());
//        return cardResp;
//    }

    /**
     * 申请补货
     */
    @Override
    public Boolean replenishment(MkuReplenishmentSoaDTO req) {
        return rpcIscMkuService.replenishment(req);
    }

    @Override
    public SearchResp pageEs(SearchReq searchReq, BaseInfo baseInfo) {
        log.info("IscMkuClientServiceImpl.pageEs start");
        int pageSize = (null != productDUCCConfig && null != productDUCCConfig.getPageSize() && productDUCCConfig.getPageSize() > 0 && productDUCCConfig.getPageSize() < 100)
                ? productDUCCConfig.getPageSize() : 48;

        if (null == searchReq.getPageNo() || null == searchReq.getPageSize()) {
            log.info("page, param null, set default val. searchReq={}", JSONObject.toJSONString(searchReq));
            searchReq.setPageNo(1);
            searchReq.setPageSize(pageSize);
        }

        if (searchReq.getPageSize() > pageSize) {
            log.info("page, pageSize invalid, pageSize={}", searchReq.getPageSize());
            searchReq.setPageSize(pageSize);
        }

        int loginFreeMaxPageNo = null != loginFreeDUCCConfig && null != loginFreeDUCCConfig.getMaxPageNo() ? loginFreeDUCCConfig.getMaxPageNo() : 10;
        if (StringUtils.isNotBlank(baseInfo.getOpenArea())) {
            if (searchReq.getPageNo() > loginFreeMaxPageNo) {
                log.info("page, open visit, pageNo invalid, openArea={}, pageNo={}", baseInfo.getOpenArea(), searchReq.getPageNo());
                searchReq.setPageNo(1);
            }
        }

//        MkuClientPageReqApiDTO reqApiDTO = new MkuClientPageReqApiDTO();
//        reqApiDTO.setIndex(searchReq.getPageNo().longValue());
//        reqApiDTO.setSize(searchReq.getPageSize().longValue());
////        String key = this.filterSpecialChar(StringUtils.trimToNull(searchReq.getKey()));
//        reqApiDTO.setKey(searchReq.getKey());
//        reqApiDTO.setFeatureTags(searchReq.getFeatureTags());
//        if (StringUtils.isNotBlank(searchReq.getCat())) {
//            String cat = searchReq.getCat();
//            String catId = cat.split(",")[0];
//            reqApiDTO.setCatIds(Arrays.asList(Long.parseLong(catId)));
//        }
//        reqApiDTO.setStockFlag(searchReq.getStockFlag());
//        ReqBaseInfoUtil.copyBaseInfo(baseInfo, reqApiDTO);
//
//        PageInfo<MkuClientApiDTO> pageInfo = rpcIscMkuService.page(reqApiDTO);
//        if (null == pageInfo) {
//            log.warn("page, response fail. input={}, baseInfo={}", JSONObject.toJSONString(searchReq), JSONObject.toJSONString(baseInfo));
//            return null;
//        }
//
//        SearchResp searchResp = new SearchResp();
//        copyPageInfoEs(baseInfo, pageInfo, searchResp, loginFreeMaxPageNo, pageSize);

        IscMkuClientPageReqDTO reqDTO = new IscMkuClientPageReqDTO();
        reqDTO.setIndex(searchReq.getPageNo().longValue());
        reqDTO.setSize(searchReq.getPageSize().longValue());
        reqDTO.setName(StringUtils.isNotBlank(searchReq.getKey()) ? searchReq.getKey().trim() : null);
        reqDTO.setKey(StringUtils.isNotBlank(searchReq.getKey()) ? searchReq.getKey().trim() : null);
        log.info("page, reqDTO={}, pageSizeDucc={}, loginFreeMaxPageNoDucc={}", JSONObject.toJSONString(reqDTO), pageSize, loginFreeMaxPageNo);

        if (StringUtils.isNotBlank(searchReq.getCat())) {
            String cat = searchReq.getCat();
            String catId = cat.split(",")[0];
            reqDTO.setCatId(Long.parseLong(catId));
        }
        reqDTO.setStockFlag(searchReq.getStockFlag());
        ReqBaseInfoUtil.copyBaseInfo(baseInfo, reqDTO);

        PageInfo<IscMkuClientDTO> pageInfo = rpcProductReadService.page(reqDTO);
        if (null == pageInfo) {
            log.warn("page, response fail. input={}, baseInfo={}", JSONObject.toJSONString(searchReq), JSONObject.toJSONString(baseInfo));
            return null;
        }

        SearchResp searchResp = new SearchResp();
        copyPageInfo(baseInfo, pageInfo, searchResp, loginFreeMaxPageNo, pageSize);
        log.info("searchWares, searchReq={}, baseInfo={}, resp={}", JSON.toJSONString(searchReq), JSON.toJSONString(baseInfo), JSON.toJSONString(searchResp));
        return searchResp;
    }

    @Override
    public SearchResp latestWares(BaseInfo baseInfo) {
        log.info("IscMkuClientServiceImpl.latestWares start");
        MkuClientLatestWareReqApiDTO reqApiDTO = new MkuClientLatestWareReqApiDTO();
        ReqBaseInfoUtil.copyBaseInfo(baseInfo, reqApiDTO);
        List<MkuClientApiDTO> rpcDataList = rpcIscMkuService.latestWares(reqApiDTO);

        List<SearchWaresResp> wares = buildShowInfo(baseInfo, rpcDataList);

        SearchResp searchResp = new SearchResp();
        searchResp.setWares(wares);
        return searchResp;
    }

    @Override
    public SearchResp getBrowsing(BaseInfo baseInfo, List<String> mkuIds) {
        log.info("IscMkuClientServiceImpl.getBrowsing, start");
        if (CollectionUtils.isEmpty(mkuIds)){
            log.info("IscMkuClientServiceImpl.getBrowsing, mkuIds empty.");
            return new SearchResp();
        }
        Set<Long> skuIdLong = mkuIds.stream().map(Long::parseLong).collect(Collectors.toSet());
        MkuListInfoReqApiDTO reqApiDTO = new MkuListInfoReqApiDTO();
        reqApiDTO.setMkuIds(skuIdLong);
        ReqBaseInfoUtil.copyBaseInfo(baseInfo, reqApiDTO);
        List<MkuClientApiDTO> rpcDataList = rpcIscMkuService.queryWaresInfo(reqApiDTO);
        List<SearchWaresResp> wares = buildShowInfo(baseInfo, rpcDataList);
        SearchResp searchResp = new SearchResp();
        searchResp.setWares(sortWaresByMkuIds(mkuIds,wares));
        return searchResp;
    }

    /**
     * 根据提供的 MKU Ids 对商品列表进行排序。
     * @param mkuIds 需要排序的 MKU Ids 列表。
     * @param wares 待排序的商品列表。
     * @return 排序后的商品列表。
     */
    public  List<SearchWaresResp> sortWaresByMkuIds(List<String> mkuIds, List<SearchWaresResp> wares) {
        Map<String, Integer> indexMap = new HashMap<>();
        for (int i = 0; i < mkuIds.size(); i++) {
            indexMap.put(mkuIds.get(i), i);
        }
        return wares.stream()
                .sorted(Comparator.comparingInt(ware -> indexMap.getOrDefault(ware.getSku(), Integer.MAX_VALUE)))
                .collect(Collectors.toList());
    }

    @Override
    public SearchResp recommendWares(BaseInfo baseInfo) {
        log.info("IscMkuClientServiceImpl.recommendWares, start");
        Set<Long> recommendMkuIds = recommendMkuService.queryRecommendMkuConfig(baseInfo.getClientId());
        if (CollectionUtils.isEmpty(recommendMkuIds)){
            log.info("IscMkuClientServiceImpl.recommendWares, recommendMkuIds empty.");
            return new SearchResp();
        }

        MkuListInfoReqApiDTO reqApiDTO = new MkuListInfoReqApiDTO();
        reqApiDTO.setMkuIds(recommendMkuIds);
        ReqBaseInfoUtil.copyBaseInfo(baseInfo, reqApiDTO);
        List<MkuClientApiDTO> rpcDataList = rpcIscMkuService.queryWaresInfo(reqApiDTO);

        List<SearchWaresResp> wares = buildShowInfo(baseInfo, rpcDataList);

        SearchResp searchResp = new SearchResp();
        searchResp.setWares(wares);
        return searchResp;
    }

//    /**
//     * 复制对象信息
//     *
//     * @param pageInfo   源对象
//     * @param searchResp 目标对象
//     */
//    private void copyPageInfoEs(BaseInfo baseInfo, PageInfo<MkuClientApiDTO> pageInfo, SearchResp searchResp, int loginFreeMaxPageNo, int pageSize) {
//        if (null == pageInfo || null == searchResp) {
//            log.info("copyPageInfo, param null.");
//            return;
//        }
//
//        SearchPageResp pageResp = new SearchPageResp();
//        pageResp.setPageIndex(Long.valueOf(pageInfo.getIndex()).intValue());
//        long pageCount = pageInfo.getTotal() % pageInfo.getSize() == 0 ? pageInfo.getTotal() / pageInfo.getSize() : (pageInfo.getTotal() / pageInfo.getSize() + 1);
//        pageResp.setPageCount(Long.valueOf(pageCount).intValue());
//        pageResp.setResultCount(Long.valueOf(pageInfo.getTotal()).intValue());
//        pageResp.setMaxResultCount(Long.valueOf(pageInfo.getTotal()).intValue());
//        if (StringUtils.isNotBlank(baseInfo.getOpenArea()) && pageResp.getPageCount() > loginFreeMaxPageNo) {
//            log.info("copyPageInfo, pageCount over loginFreeMaxPageNo. reset pageResult. pageCount={}, loginFreeMaxPageNo={}", pageResp.getPageCount(), loginFreeMaxPageNo);
//            // 免登录访问时超出了允许搜索的最大页数。重置页数
//            pageResp.setPageCount(loginFreeMaxPageNo);
//            int total = pageSize * loginFreeMaxPageNo;
//            pageResp.setResultCount(total);
//            pageResp.setMaxResultCount(total);
//        }
//        searchResp.setPage(pageResp);
//
//        if (CollectionUtils.isEmpty(pageInfo.getRecords())) {
//            return;
//        }
//
//        List<MkuClientApiDTO> pageRecords = pageInfo.getRecords();
//        List<SearchWaresResp> wares = buildShowInfo(baseInfo, pageRecords);
//        searchResp.setWares(wares);
//    }

    private List<SearchWaresResp> buildShowInfo(BaseInfo baseInfo, List<MkuClientApiDTO> mkuClientApiDTOList){
        List<SearchWaresResp> wares = new ArrayList<>();
        if (CollectionUtils.isEmpty(mkuClientApiDTOList)){
            log.info("buildShowInfo, mkuClientApiDTOList empty.");
            return  wares;
        }

        for (MkuClientApiDTO dto : mkuClientApiDTOList) {
            SearchWaresResp resp = new SearchWaresResp();
            resp.setWareName(dto.getMkuName());
            resp.setSku(null != dto.getMkuId() ? dto.getMkuId().toString() : "");

            resp.setImg(s3Utils.replaceHost(dto.getMkuImage()));
            resp.setSourceCountryCode(dto.getSourceCountryCode());
            if (null != dto.getBrand()) {
                BrandClientApiDTO brandClientDTO = dto.getBrand();
                resp.setBrand(null != brandClientDTO.getId() ? brandClientDTO.getId().toString() : "");
            }

            // 状态目前为固定值
            SkuSaleStateResp skuSaleState = new SkuSaleStateResp();

            skuSaleState.setSaleState(1);
            resp.setSaleState(skuSaleState);
            resp.setCurrenciesPrices(MkuConvert.INSTANCE.apiDTOMap2DTO(dto.getCurrenciesPrices()));
            resp.setShowCurrency(MkuConvert.INSTANCE.apiDTO2DTO(dto.getShowCurrency()));
            resp.setMoq(dto.getMoq());
            resp.setRemainNum(dto.getRemainNum());
            resp.setDeliveryDate(dto.getDeliveryDate());

            buildStock(baseInfo, resp, dto, 1);
            resp.setFeatureTagMap(dto.getFeatureTagMap());
            wares.add(resp);
        }

        // 填充币种符号
        Map<String, String> symbolMap = currencySymbolService.allSymbols();
        // 遍历商品
        for (SearchWaresResp waresResp : wares) {
            fillCurrencySymbol(waresResp, symbolMap);
            waresResp.setMoqText(getMoqText(waresResp.getMoq(), baseInfo.getEnv()));
        }
        
        // 批量填充承诺时间标签到featureTagMap（性能优化：单次API调用处理所有商品）
        fillLatestWaresBatch(wares, baseInfo);

        // 批量填充可售状态
        fillAvailableSaleBatch(wares, baseInfo);

        return wares;
    }


    /**
     * 复制对象信息
     *
     * @param pageInfo   源对象
     * @param searchResp 目标对象
     */
    private void copyPageInfo(BaseInfo baseInfo, PageInfo<IscMkuClientDTO> pageInfo, SearchResp searchResp, int loginFreeMaxPageNo, int pageSize) {
        if (pageInfo == null || searchResp == null) {
            log.info("copyPageInfo, param null.");
            return;
        }

        // 设置分页信息
        SearchPageResp pageResp = buildPageResp(baseInfo, pageInfo, loginFreeMaxPageNo, pageSize);
        searchResp.setPage(pageResp);

        List<IscMkuClientDTO> recordList = pageInfo.getRecords();
        if (CollectionUtils.isEmpty(recordList)) {
            return;
        }

        log.info("searchWares, pageInfo.records={}", JSON.toJSONString(recordList));
        List<SearchWaresResp> wares = new ArrayList<>(recordList.size());

        for (IscMkuClientDTO dto : recordList) {
            // 主商品
            SearchWaresResp mainResp = buildSearchWaresResp(baseInfo, dto);

            // 子商品
            if (!CollectionUtils.isEmpty(dto.getSubList())) {
                List<SearchWaresResp> subList = new ArrayList<>();
                for (IscMkuClientDTO subDto : dto.getSubList()) {
                    SearchWaresResp subResp = buildSearchWaresResp(baseInfo, subDto);
                    subList.add(subResp);
                }
                mainResp.setSubList(subList);
            }
            wares.add(mainResp);
        }

        // 批量填充承诺时间标签到featureTagMap（性能优化：单次API调用处理所有商品）
        fillLatestWaresBatch(wares, baseInfo);

        searchResp.setWares(wares);
    }

    /**
     * 构建分页响应
     */
    private SearchPageResp buildPageResp(BaseInfo baseInfo, PageInfo<IscMkuClientDTO> pageInfo, int loginFreeMaxPageNo, int pageSize) {
        SearchPageResp pageResp = new SearchPageResp();
        pageResp.setPageIndex(Long.valueOf(pageInfo.getIndex()).intValue());
        long total = pageInfo.getTotal();
        long size = pageInfo.getSize();
        int pageCount = (int) ((total + size - 1) / size);
        pageResp.setPageCount(pageCount);
        int resultCount = (int) total;
        pageResp.setResultCount(resultCount);
        pageResp.setMaxResultCount(resultCount);

        // 免登录限制
        if (StringUtils.isNotBlank(baseInfo.getOpenArea()) && pageCount > loginFreeMaxPageNo) {
            log.info("copyPageInfo, pageCount over loginFreeMaxPageNo. reset pageResult. pageCount={}, loginFreeMaxPageNo={}", pageCount, loginFreeMaxPageNo);
            pageResp.setPageCount(loginFreeMaxPageNo);
            int limitedTotal = pageSize * loginFreeMaxPageNo;
            pageResp.setResultCount(limitedTotal);
            pageResp.setMaxResultCount(limitedTotal);
        }
        return pageResp;
    }

    /**
     * 构建商品响应对象
     */
    private SearchWaresResp buildSearchWaresResp(BaseInfo baseInfo, IscMkuClientDTO dto) {
        SearchWaresResp resp = new SearchWaresResp();
        resp.setWareName(dto.getMkuName());
        resp.setSku(dto.getMkuId() != null ? dto.getMkuId().toString() : "");
        resp.setImg(s3Utils.replaceHost(dto.getMkuImage()));
        resp.setSourceCountryCode(dto.getSourceCountryCode());

        if (dto.getBrand() != null) {
            IscBrandClientDTO brandClientDTO = dto.getBrand();
            resp.setBrand(brandClientDTO.getId() != null ? brandClientDTO.getId().toString() : "");
        }

        // 状态目前为固定值
        SkuSaleStateResp skuSaleState = new SkuSaleStateResp();
        skuSaleState.setSaleState(1);
        resp.setSaleState(skuSaleState);
        resp.setIsAvailableSale(dto.getIsAvailableSale());

        resp.setCurrenciesPrices(dto.getCurrenciesPrices());
        resp.setShowCurrency(dto.getShowCurrency());
        resp.setMoq(dto.getMoq());
        resp.setMoqText(dto.getMoqText());
        resp.setRemainNum(dto.getRemainNum());
        resp.setDeliveryDate(dto.getDeliveryDate());
        resp.setSaleAttributes(dto.getSaleAttributes());

        buildStock(baseInfo, resp, dto, 1);

        return resp;
    }

    /**
     * 复制详情页信息
     *
     * @param dto 源对象
     * @return 目标对象
     */
    private IscAppMkuAggreDTO copyToIscAppMkuAggreDTO(BaseInfo baseInfo, IscMkuClientDTO dto) {
        IscAppMkuAggreDTO res = new IscAppMkuAggreDTO();

        Map<String, String> bigField = new HashMap<>();
        bigField.put("pcWdis", dto.getDescription());
        res.setBigField(bigField);

        // 设置基本信息
        ProductBaseInfoVo baseInfoVo = new ProductBaseInfoVo();
        baseInfoVo.setSkuId(dto.getMkuId());
        baseInfoVo.setSourceCountryCode(dto.getSourceCountryCode());
        baseInfoVo.setProductName(dto.getMkuName());

        baseInfoVo.setShowCurrency(this.convertProductPriceVo(dto, dto.getShowCurrency()));

        Map<String, ProductPricesVO> pricesVOMap = new HashMap<>();
        if (MapUtils.isNotEmpty(dto.getCurrenciesPrices())){
            dto.getCurrenciesPrices().forEach((k, v) -> pricesVOMap.put(k, this.convertProductPriceVo(dto, v)));
        }else {
            log.warn("copyToIscAppMkuAggreDTO, currenciesPrices is empty, skuId={}", dto.getMkuId());
        }
        baseInfoVo.setCurrenciesPrices(pricesVOMap);

        baseInfoVo.setCurrency(dto.getCurrency());
        baseInfoVo.setPrice(dto.getSalePrice());

        baseInfoVo.setModel(dto.getModel());  // 设置型号
        baseInfoVo.setUnit(dto.getSaleUnit());  // 设置销售单位

        // 商品图片列表
        List<SkuImageVo> imageVoList = new ArrayList<>();
        // 主图
        SkuImageVo skuImageVo = new SkuImageVo();
        skuImageVo.setSkuId(dto.getMkuId());
        skuImageVo.setPath(s3Utils.replaceHost(dto.getMkuImage()));
        skuImageVo.setIsPrimary(1);
        skuImageVo.setOrderSort(1);
        imageVoList.add(skuImageVo);
        // 细节图
        if (StringUtils.isNotBlank(dto.getDetailImg())) {
            String[] detailImages = dto.getDetailImg().split(CommonConstant.HASHTAG);
            for (int i = 0; i < detailImages.length; i++) {
                SkuImageVo detailImgVO = new SkuImageVo();
                detailImgVO.setSkuId(dto.getMkuId());
                detailImgVO.setPath(s3Utils.replaceHost(detailImages[i]));
                detailImgVO.setIsPrimary(0);
                // 主图已经占了一个顺序位置，i从0开始，所以此处加2
                detailImgVO.setOrderSort(i + 2);

                imageVoList.add(detailImgVO);
            }
        }
        baseInfoVo.setImageDTOList(imageVoList);
        baseInfoVo.setImagePath(s3Utils.replaceHost(dto.getMkuImage()));
        baseInfoVo.setLowestBuy(dto.getMoq() != null ? dto.getMoq() : 1);
        baseInfoVo.setMoq(dto.getMoq());
        baseInfoVo.setMoqText(dto.getMoqText());

        // 设置库存信息
        buildStock(baseInfo, baseInfoVo, dto, 1);

        //设置商品时效信息
        String productAgingText = fulfillmentReadService.getDeliveryDateText(baseInfo, dto.getMkuId(), 1);
        baseInfoVo.setDeliveryDate(productAgingText);

        // 设置品牌信息
        IscBrandClientDTO brand = dto.getBrand();
        if (null != brand) {
            BrandInfoVo brandInfoVo = new BrandInfoVo();
            brandInfoVo.setBrandId(null != brand.getId() ? brand.getId().intValue() : null);
            brandInfoVo.setCnName(brand.getName());
            brandInfoVo.setEnName(brand.getName());
            brandInfoVo.setName(brand.getName());    // 设置品牌名称
            brandInfoVo.setLogoUrl(StringUtils.isNotBlank(brand.getImgUrl())?brand.getImgUrl().trim():"");
            baseInfoVo.setBrandInfoVo(brandInfoVo);
        }

        // 状态目前为固定值
        SkuSaleStateResp skuSaleState = new SkuSaleStateResp();
        skuSaleState.setSaleState(dto.getSaleStatus());
        baseInfoVo.setSkuSaleState(skuSaleState);

        res.setBaseInfo(baseInfoVo);
        // 设置基本信息完毕

        // 设置扩展属性
        if (CollectionUtils.isNotEmpty(dto.getExtentAttributes())) {
            List<IscMkuClientPropertyDTO> attrs = dto.getExtentAttributes();
            if(CollectionUtils.isNotEmpty(attrs)){
                List<Pair<String, String>> list = new ArrayList<>();
                for (IscMkuClientPropertyDTO attr : attrs) {
                    Pair<String, String> pair = new ImmutablePair<>(attr.getName(), attr.getValues());
                    list.add(pair);
                }
                res.setProductAttr(list);
            } else {
                res.setProductAttr(new ArrayList<>());
            }
        } else {
            res.setProductAttr(new ArrayList<>());
        }

        // 设置销售属性
        if (CollectionUtils.isNotEmpty(dto.getSaleAttributes())) {
            res.setSaleAttributes(ProductConvert.INSTANCE.listPropertyDTO2PropertyDTO(dto.getSaleAttributes()));
        }

        // 设置类目路径
        ProductCategoryVo categoryVo = new ProductCategoryVo();
        if (CollectionUtils.isNotEmpty(dto.getCatePath())) {
            List<IscCategoryTreeNodeDTO> catDtoList = dto.getCatePath();

            IscCategoryTreeNodeDTO level1 = catDtoList.size() > 0 ? catDtoList.get(0) : null;
            categoryVo.setFirstCategoryCode(level1 != null && level1.getId() != null ? level1.getId().toString() : null);
            categoryVo.setFirstCategoryName(level1 != null ? level1.getName() : null);

            IscCategoryTreeNodeDTO level2 = catDtoList.size() > 1 ? catDtoList.get(1) : null;
            categoryVo.setSecondCategoryCode(level2 != null && level2.getId() != null ? level2.getId().toString() : null);
            categoryVo.setSecondCategoryName(level2 != null ? level2.getName() : null);

            IscCategoryTreeNodeDTO level3 = catDtoList.size() > 2 ? catDtoList.get(2) : null;
            categoryVo.setThirdCategoryCode(level3 != null && level3.getId() != null ? level3.getId().toString() : null);
            categoryVo.setThirdCategoryName(level3 != null ? level3.getName() : null);

            // 目前三、四级类目名称相同，不需要展示四级类目
            IscCategoryTreeNodeDTO level4 = catDtoList.size() > 3 ? catDtoList.get(3) : null;
            categoryVo.setFourthCategoryCode(level4 != null && level4.getId() != null ? level4.getId().toString() : null);
            categoryVo.setFourthCategoryName(level4 != null ? level4.getName() : null);
        } else {
            log.warn("copyToIscAppMkuAggreDTO, catePath error. catePath={}", JSONObject.toJSONString(dto.getCatePath()));
        }
        res.setProductCategory(categoryVo);

        // 设置物料信息
        fillMaterialInfo(baseInfo, res);

        // 填充可售状态
        fillAvailableSale(baseInfo, res, dto.getMkuId());

        res.setMkuSaleAttributes(this.convertMkuSaleList(dto.getMkuSaleAttributes()));

        return res;
    }


    /**
     * 格式化BigDecimal值为指定的小数位数或整数。
     * @param value 要格式化的BigDecimal值。
     * @return 格式化后的字符串表示。
     */
    private String formatBigDecimal(BigDecimal value) {
        if (value.scale() > 0 && value.stripTrailingZeros().scale() > 0) {
            DecimalFormat df = new DecimalFormat("#.##");
            return df.format(value);
        } else {
            return String.valueOf(value.intValue());
        }
    }


    /**
     * 填充物料信息
     *
     * @param baseInfo
     * @param iscAppMkuAggreDTO
     */
    private void fillMaterialInfo(BaseInfo baseInfo, IscAppMkuAggreDTO iscAppMkuAggreDTO) {
        if (!authorityService.haseMaterialAuthority(baseInfo)) {
            log.info("fillMaterialInfo, no MaterialAuthority. clientCode={}", baseInfo.getClientId());
            return;
        }

        if (null == iscAppMkuAggreDTO || null == iscAppMkuAggreDTO.getBaseInfo()) {
            log.info("fillMaterialInfo, iscAppMkuAggreDTO null. clientCode={}", baseInfo.getClientId());
            return;
        }

        try {
            Long skuId = iscAppMkuAggreDTO.getBaseInfo().getSkuId();
            Set<Long> mkuIds = new HashSet<>();
            mkuIds.add(skuId);

            Map<Long, MkuMaterialApiDTO> mkuMaterialApiDTOMap = mkuMaterialRpcService.queryMkuMaterialDTOsByMkuIdClient(new ArrayList<>(mkuIds), baseInfo.getClientId());
            if (MapUtils.isEmpty(mkuMaterialApiDTOMap)) {
                return;
            }
            MkuMaterialResVO mkuMaterialResVO = MkuMaterialConvert.INSTANCE.mkuMaterialApiDTO2ResVo(mkuMaterialApiDTOMap.get(skuId));
            iscAppMkuAggreDTO.setMkuMaterial(mkuMaterialResVO);
        } catch (Exception e) {
            log.error("", e);
        }
    }

    private List<IscAppMkuClientPropertyValueRelationDTO> convertMkuSaleList(List<IscMkuClientMkuPropertyValueDTO> inputs) {
       if(CollectionUtils.isEmpty(inputs)){
           return Collections.emptyList();
       }

       List<IscAppMkuClientPropertyValueRelationDTO> results = new ArrayList<>();
       for(IscMkuClientMkuPropertyValueDTO input : inputs){
           IscAppMkuClientPropertyValueRelationDTO result = new IscAppMkuClientPropertyValueRelationDTO();
           result.setMkuId(input.getMkuId());
           result.setPicAttrValueId(input.getPicAttrValueId());
           result.setTextAttrValueId(input.getTextAttrValueId());
           results.add(result);
       }
       return results;
    }

    private ProductPricesVO convertProductPriceVo(IscMkuClientDTO dto, ProductPricesDTO showCurrency) {
        ProductPricesVO productPricesVO = new ProductPricesVO();
        if (null==showCurrency){
            log.warn("convertProductPriceVo, showCurrency is null. mkuId={}", dto.getMkuId());
            return productPricesVO;
        }
        productPricesVO.setCurrency(showCurrency.getCurrency());
        //productPricesVO.setSalePrice(showCurrency.getSalePrice());
        productPricesVO.setSymbol(showCurrency.getSymbol());
        productPricesVO.setExchangeRate(showCurrency.getExchangeRate());
        productPricesVO.setCurrencySource(showCurrency.getCurrencySource());
        productPricesVO.setValueAddedTax(showCurrency.getValueAddedTax());
        productPricesVO.setValueAddedTaxRate(showCurrency.getValueAddedTaxRate());
        productPricesVO.setIncludeTaxPrice(showCurrency.getIncludeTaxPrice());
        productPricesVO.setShowPrice(showCurrency.getShowPrice());
        productPricesVO.setPrice(showCurrency.getPrice());
        productPricesVO.setSalePrice(showCurrency.getSalePrice());
        return productPricesVO;
    }

    /**
     * 复制相似品信息
     *
     * @param groupDTO 源对象
     * @return 目标对象
     */
    private SkuSimilarProductVo copyToSkuSimilarProductVo(IscMkuClientGroupDTO groupDTO) {
        if (null == groupDTO || CollectionUtils.isEmpty(groupDTO.getMembers())) {
            log.info("IscMkuClientGroupDTO null.");
            return null;
        }
        List<SimilarProductVo> similarProductVos = new ArrayList<>();
        for (IscMkuClientSimpleDTO simpleDTO : groupDTO.getMembers()) {
            SimilarProductVo productVo = new SimilarProductVo();
            productVo.setSaleName(simpleDTO.getName());
//            productVo.setDim(simpleDTO.getMkuId());
            similarProductVos.add(productVo);
        }

        SkuSimilarProductVo vo = new SkuSimilarProductVo();
        vo.setSimilarProductVos(similarProductVos);
        return vo;
    }

    /**
     * 填充币种符号
     *
     * @param waresResp
     * @param symbolMap
     */
    private void fillCurrencySymbol(SearchWaresResp waresResp, Map<String, String> symbolMap) {
        if (MapUtils.isNotEmpty(waresResp.getCurrenciesPrices())) {
            // 遍历币种
            waresResp.getCurrenciesPrices().entrySet().forEach(o -> {
                ProductPricesDTO dto = o.getValue();
                dto.setSymbol(symbolMap.get(o.getKey()));
            });
        }

        ProductPricesDTO showCurrency = waresResp.getShowCurrency();
        if (null != showCurrency) {
            showCurrency.setSymbol(symbolMap.get(showCurrency.getCurrency()));
        }
    }

    public String getMoqText(Integer moq, String lang) {
        if (moq == null) {
            return null;
        }
        JSONObject mkuContextObject = JSONObject.parseObject(mkuContext);
        MkuContextDTO mkuContextDTO = mkuContextObject.getObject(lang, MkuContextDTO.class);
        if (mkuContextDTO == null) {
            mkuContextDTO = mkuContextObject.getObject(LangConstant.LANG_ZH, MkuContextDTO.class);
        }
        return String.format(mkuContextDTO.getMoqText(), moq);
    }

    /**
     * 批量填充商品承诺时间标签到featureTagMap
     * @param waresList 商品响应对象列表
     * @param baseInfo 基础信息
     */
    private void fillLatestWaresBatch(List<SearchWaresResp> waresList, BaseInfo baseInfo) {
        if (CollectionUtils.isEmpty(waresList) || baseInfo == null) {
            log.debug("fillLatestWaresBatch skipped: waresList is empty or baseInfo is null");
            return;
        }

        try {
            // 1. 收集所有商品（主商品和子商品）的mkuId和SearchWaresResp映射
            List<PromiseTimeTagReqApiDTO> mkuReqs = new ArrayList<>();
            Map<Long, List<SearchWaresResp>> mkuIdToWaresMap = new HashMap<>();

            for (SearchWaresResp waresResp : waresList) {
                collectMkuReqAndMap(waresResp, mkuReqs, mkuIdToWaresMap);
                // 如果有子商品，也处理
                if (!CollectionUtils.isEmpty(waresResp.getSubList())) {
                    for (SearchWaresResp subResp : waresResp.getSubList()) {
                        collectMkuReqAndMap(subResp, mkuReqs, mkuIdToWaresMap);
                    }
                }
            }

            if (CollectionUtils.isEmpty(mkuReqs)) {
                log.debug("fillLatestWaresBatch: no valid mkuIds found");
                return;
            }

            // 2. 远程批量查询
            PromiseTimeTagBatchReqApiDTO req = new PromiseTimeTagBatchReqApiDTO();
            req.setMkuReqs(mkuReqs);
            req.setClientCode(baseInfo.getClientId());

            DataResponse<Map<Long, PromiseTimeTagResApiDTO>> response = promiseTimeReadApiService.queryTagBatch(req);

            if (response == null) {
                log.warn("fillLatestWaresBatch: promiseTimeReadApiService returned null response for clientCode: {}", baseInfo.getClientId());
                return;
            }

            if (!Boolean.TRUE.equals(response.getSuccess())) {
                log.warn("fillLatestWaresBatch: promiseTimeReadApiService returned failure response, code: {}, message: {}", response.getCode(), response.getMessage());
                return;
            }

            Map<Long, PromiseTimeTagResApiDTO> tagData = response.getData();
            if (MapUtils.isEmpty(tagData)) {
                log.debug("fillLatestWaresBatch: no tag data returned");
                return;
            }

            // 3. 批量处理返回的标签数据
            int processedCount = 0;
            for (Map.Entry<Long, PromiseTimeTagResApiDTO> entry : tagData.entrySet()) {
                Long mkuId = entry.getKey();
                PromiseTimeTagResApiDTO tagResult = entry.getValue();
                if (tagResult == null || tagResult.getTagVal() == null) {
                    continue;
                }
                List<SearchWaresResp> respList = mkuIdToWaresMap.get(mkuId);
                if (CollectionUtils.isEmpty(respList)) {
                    continue;
                }
                for (SearchWaresResp resp : respList) {
                    if (setShippingTag(resp, tagResult.getTagVal())) {
                        processedCount++;
                    }
                }
            }

            log.debug("fillLatestWaresBatch completed: processed {} tags for {} products", processedCount, waresList.size());

        } catch (Exception e) {
            log.error("fillLatestWaresBatch error for clientId: {}, error: {}", baseInfo.getClientId(), e.getMessage(), e);
        }
    }

    /**
     * 收集 mkuId 请求和 waresResp 映射
     */
    private void collectMkuReqAndMap(SearchWaresResp waresResp, List<PromiseTimeTagReqApiDTO> mkuReqs, Map<Long, List<SearchWaresResp>> mkuIdToWaresMap) {
        if (waresResp == null || StringUtils.isBlank(waresResp.getSku())) {
            return;
        }
        Long mkuId;
        try {
            mkuId = Long.valueOf(waresResp.getSku());
        } catch (NumberFormatException e) {
            log.warn("fillLatestWaresBatch skipped invalid sku format: {}", waresResp.getSku());
            return;
        }
        PromiseTimeTagReqApiDTO reqApiDTO = new PromiseTimeTagReqApiDTO();
        reqApiDTO.setMkuId(mkuId);
        reqApiDTO.setNum(1);
        mkuReqs.add(reqApiDTO);

        mkuIdToWaresMap.computeIfAbsent(mkuId, k -> new ArrayList<>()).add(waresResp);
    }

    /**
     * 根据tagVal设置承诺发货标签
     * @return 是否设置成功
     */
    private boolean setShippingTag(SearchWaresResp waresResp, Integer tagVal) {
        if (waresResp == null || tagVal == null) {
            return false;
        }
        if (waresResp.getFeatureTagMap() == null) {
            waresResp.setFeatureTagMap(new HashMap<>());
        }
        switch (tagVal) {
            case 1:
                waresResp.getFeatureTagMap().put("48-hour-shipping", "1");
                return true;
            case 4:
                waresResp.getFeatureTagMap().put("7-day-shipping", "1");
                return true;
            default:
                log.debug("fillLatestWaresBatch: unhandled tagVal {} for sku: {}", tagVal, waresResp.getSku());
                return false;
        }
    }

    /**
     * 填充商品承诺时间标签到featureTagMap (保留原方法作为兼容)
     * @param waresResp 商品响应对象
     * @param baseInfo 基础信息
     */
    private void fillLatestWares(SearchWaresResp waresResp, BaseInfo baseInfo) {
        fillLatestWaresBatch(Arrays.asList(waresResp), baseInfo);
    }

    private String filterSpecialChar(String input) {
        if (StringUtils.isBlank(input)) {
            return null;
        }
        return input.replaceAll(filterRegex, "");
    }

    @Override
    public Map<Long, Map<String, String>> queryMkuSpecialAttr(IscAppMkuClientSpecialAttrReqDTO reqDTO) {
        Map<Long,Map<String,String>> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(reqDTO.getMkuIds())) {
            log.warn("IscMkuClientServiceImpl.queryMkuSpecialAttr mkuIds is empty,reqDTO={}",JSON.toJSONString(reqDTO));
            return resultMap;
        }

        List<List<Long>> partition = Lists.partition(reqDTO.getMkuIds(), 50);
        for (List<Long> subMkuIds : partition) {
            SpecialAttrMkuClientReqDTO mkuClientReqDTO = new SpecialAttrMkuClientReqDTO();
            mkuClientReqDTO.setMkuIds(Sets.newHashSet(subMkuIds));
            mkuClientReqDTO.setClientCode(reqDTO.getClientCode());
            resultMap.putAll(rpcIscMkuService.queryMkuSpecialAttr(mkuClientReqDTO));
        }
        
        // 补充承诺时间标签逻辑，覆盖FulfilPromise字段
        fillPromiseTagsForSpecialAttr(resultMap, reqDTO.getClientCode());
        
        return resultMap;
    }
    
    /**
     * 为特殊属性填充承诺时间标签，覆盖FulfilPromise字段
     * @param resultMap 特殊属性结果Map
     * @param clientCode 客户编码
     */
    private void fillPromiseTagsForSpecialAttr(Map<Long, Map<String, String>> resultMap, String clientCode) {
        if (MapUtils.isEmpty(resultMap) || StringUtils.isBlank(clientCode)) {
            log.debug("fillPromiseTagsForSpecialAttr skipped: resultMap is empty or clientCode is blank");
            return;
        }
        
        try {
            // 构建批量查询请求
            PromiseTimeTagBatchReqApiDTO req = new PromiseTimeTagBatchReqApiDTO();
            List<PromiseTimeTagReqApiDTO> mkuReqs = new ArrayList<>();
            
            for (Long mkuId : resultMap.keySet()) {
                PromiseTimeTagReqApiDTO reqApiDTO = new PromiseTimeTagReqApiDTO();
                reqApiDTO.setMkuId(mkuId);
                reqApiDTO.setNum(1);
                mkuReqs.add(reqApiDTO);
            }
            
            req.setMkuReqs(mkuReqs);
            req.setClientCode(clientCode);
            
            DataResponse<Map<Long, PromiseTimeTagResApiDTO>> response = promiseTimeReadApiService.queryTagBatch(req);
            
            if (response == null) {
                log.warn("fillPromiseTagsForSpecialAttr: promiseTimeReadApiService returned null response for clientCode: {}", clientCode);
                return;
            }
            
            if (!Boolean.TRUE.equals(response.getSuccess())) {
                log.warn("fillPromiseTagsForSpecialAttr: promiseTimeReadApiService returned failure response for clientCode: {}, code: {}, message: {}", 
                    clientCode, response.getCode(), response.getMessage());
                return;
            }
            
            Map<Long, PromiseTimeTagResApiDTO> tagData = response.getData();
            if (MapUtils.isEmpty(tagData)) {
                log.debug("fillPromiseTagsForSpecialAttr: no tag data returned for clientCode: {}", clientCode);
                return;
            }
            
            // 遍历返回的标签数据，覆盖FulfilPromise字段
            for (Map.Entry<Long, PromiseTimeTagResApiDTO> entry : tagData.entrySet()) {
                Long mkuId = entry.getKey();
                PromiseTimeTagResApiDTO tagResult = entry.getValue();
                
                if (tagResult == null) {
                    log.debug("fillPromiseTagsForSpecialAttr: tagResult is null for mkuId: {}", mkuId);
                    continue;
                }
                
                Integer tagVal = tagResult.getTagVal();
                if (tagVal == null) {
                    log.debug("fillPromiseTagsForSpecialAttr: tagVal is null for mkuId: {}", mkuId);
                    continue;
                }
                
                // 获取或创建该MKU的属性Map
                Map<String, String> attrMap = resultMap.computeIfAbsent(mkuId, k -> new HashMap<>());
                
                // 根据tagVal覆盖FulfilPromise字段
                switch (tagVal) {
                    case 1:
                        attrMap.put("FulfilPromise", "1");  // 48小时达
                        log.debug("fillPromiseTagsForSpecialAttr: set FulfilPromise=1 (48-hour) for mkuId: {}", mkuId);
                        break;
                    case 4:
                        attrMap.put("FulfilPromise", "4");  // 7日达
                        log.debug("fillPromiseTagsForSpecialAttr: set FulfilPromise=4 (7-day) for mkuId: {}", mkuId);
                        break;
                    default:
                        log.debug("fillPromiseTagsForSpecialAttr: unhandled tagVal {} for mkuId: {}", tagVal, mkuId);
                        break;
                }
            }
            
            log.debug("fillPromiseTagsForSpecialAttr completed for {} mkus, clientCode: {}", resultMap.size(), clientCode);
            
        } catch (Exception e) {
            log.error("fillPromiseTagsForSpecialAttr error for clientCode: {}, error: {}", clientCode, e.getMessage(), e);
        }
    }

    private void buildStock(BaseInfo baseInfo, ProductBaseInfoVo baseInfoVo, IscMkuClientDTO dto, Integer reqNum){
        IscMkuClientStockDTO stock = dto.getStock();

        ProductStockInfoRes stockInfoRes = stockReadService.calculateStock(baseInfo, baseInfoVo.getSkuId(), stock, reqNum);
        baseInfoVo.setRemainNum(stockInfoRes.getRemainNum());
        baseInfoVo.setStockFlag(stockInfoRes.getStockFlag());
        baseInfoVo.setStockStateType(stockInfoRes.getStockStateType());
    }

    private void buildStock(BaseInfo baseInfo, SearchWaresResp resp, IscMkuClientDTO dto, Integer reqNum){
        IscMkuClientStockDTO stock = dto.getStock();
        Long mkuId = null!=resp.getSku()?Long.valueOf(resp.getSku()):null;

        ProductStockInfoRes stockInfoRes = stockReadService.calculateStock(baseInfo, mkuId, stock, reqNum);
        resp.setRemainNum(stockInfoRes.getRemainNum());
        resp.setStockFlag(stockInfoRes.getStockFlag());
        resp.setStockStateType(stockInfoRes.getStockStateType());
    }

    private void buildStock(BaseInfo baseInfo, SearchWaresResp resp, MkuClientApiDTO dto, Integer reqNum){
        MkuClientStockResApiDTO stock = dto.getStock();
        IscMkuClientStockDTO iscMkuClientStockDTO = MkuStockConvert.INSTANCE.MkuClientStockResApiDTO2DTO(stock);
        Long mkuId = null!=resp.getSku()?Long.valueOf(resp.getSku()):null;

        ProductStockInfoRes stockInfoRes = stockReadService.calculateStock(baseInfo, mkuId, iscMkuClientStockDTO, reqNum);
        resp.setRemainNum(stockInfoRes.getRemainNum());
        resp.setStockFlag(stockInfoRes.getStockFlag());
        resp.setStockStateType(stockInfoRes.getStockStateType());
    }

    /**
     * 批量填充可售状态
     * @param waresList 商品列表
     * @param baseInfo 基础信息
     */
    private void fillAvailableSaleBatch(List<SearchWaresResp> waresList, BaseInfo baseInfo) {
        if (CollectionUtils.isEmpty(waresList) || baseInfo == null) {
            log.debug("fillAvailableSaleBatch skipped: waresList is empty or baseInfo is null");
            return;
        }

        try {
            // 收集所有有效的MKU ID
            Set<Long> mkuIds = new HashSet<>();
            Map<Long, SearchWaresResp> mkuIdToWaresMap = new HashMap<>();

            for (SearchWaresResp waresResp : waresList) {
                if (waresResp == null || StringUtils.isBlank(waresResp.getSku())) {
                    continue;
                }

                Long mkuId = null;
                try {
                    mkuId = Long.valueOf(waresResp.getSku());
                    mkuIds.add(mkuId);
                    mkuIdToWaresMap.put(mkuId, waresResp);
                } catch (NumberFormatException e) {
                    log.warn("fillAvailableSaleBatch skipped invalid sku format: {}", waresResp.getSku());
                    continue;
                }
            }

            if (CollectionUtils.isEmpty(mkuIds)) {
                log.debug("fillAvailableSaleBatch: no valid mkuIds found");
                return;
            }

            // 构建可售查询请求
            IscMkuAvailableSaleReq req = new IscMkuAvailableSaleReq();
            req.setClientCode( baseInfo.getClientId());
            req.setCountryCode( baseInfo.getCountry());
            req.setMkuIds( mkuIds);
            req.setLang( baseInfo.getEnv());
            log.debug("fillAvailableSaleBatch: calling queryMkuAvailable with req: {}", JSONObject.toJSONString(req));
            DataResponse<Map<Long, IscMkuAvailableSaleResDTO>> response = iscProductSoaMkuReadApiService.queryMkuAvailable(req);

            if (response == null) {
                log.warn("fillAvailableSaleBatch: iscProductSoaMkuReadApiService returned null response for clientCode: {}", baseInfo.getClientId());
                return;
            }

            if (!Boolean.TRUE.equals(response.getSuccess())) {
                log.warn("fillAvailableSaleBatch: iscProductSoaMkuReadApiService returned failure response, code: {}, message: {}",
                    response.getCode(), response.getMessage());
                return;
            }

            Map<Long, IscMkuAvailableSaleResDTO> availableData = response.getData();
            if (MapUtils.isEmpty(availableData)) {
                log.debug("fillAvailableSaleBatch: no available data returned");
                return;
            }

            // 批量处理返回的可售数据
            int processedCount = 0;
            for (Map.Entry<Long, IscMkuAvailableSaleResDTO> entry : availableData.entrySet()) {
                Long mkuId = entry.getKey();
                IscMkuAvailableSaleResDTO availableInfo = entry.getValue();
                SearchWaresResp waresResp = mkuIdToWaresMap.get(mkuId);

                if (waresResp == null || availableInfo == null) {
                    continue;
                }

                // 设置可售状态
                Boolean isAvailableSale = availableInfo.getIsAvailableSale();
                waresResp.setIsAvailableSale(isAvailableSale);

                log.debug("fillAvailableSaleBatch: mkuId={}, isAvailableSale={}", mkuId, isAvailableSale);
                processedCount++;
            }

            log.debug("fillAvailableSaleBatch completed: processed {} out of {} products", processedCount, waresList.size());

        } catch (Exception e) {
            log.error("fillAvailableSaleBatch error for clientId: {}, error: {}",
                baseInfo.getClientId(), e.getMessage(), e);
        }
    }

    /**
     * 为单个商品填充可售状态
     * @param baseInfo 基础信息
     * @param iscAppMkuAggreDTO 商品聚合DTO
     * @param mkuId 商品ID
     */
    private void fillAvailableSale(BaseInfo baseInfo, IscAppMkuAggreDTO iscAppMkuAggreDTO, Long mkuId) {
        if (baseInfo == null || iscAppMkuAggreDTO == null || mkuId == null) {
            log.debug("fillAvailableSale skipped: baseInfo, iscAppMkuAggreDTO or mkuId is null");
            return;
        }

        try {
            // 构建可售查询请求
            IscMkuAvailableSaleReq req = new IscMkuAvailableSaleReq();
            req.setClientCode(baseInfo.getClientId());
            req.setCountryCode(baseInfo.getCountry());
            req.setMkuIds(Sets.newHashSet(mkuId));
            req.setLang(baseInfo.getEnv());

            log.debug("fillAvailableSale: calling queryMkuAvailable with req: {}", JSONObject.toJSONString(req));

            DataResponse<Map<Long, IscMkuAvailableSaleResDTO>> response = iscProductSoaMkuReadApiService.queryMkuAvailable(req);

            if (response == null) {
                log.warn("fillAvailableSale: iscProductSoaMkuReadApiService returned null response for mkuId: {}", mkuId);
                return;
            }

            if (!Boolean.TRUE.equals(response.getSuccess())) {
                log.warn("fillAvailableSale: iscProductSoaMkuReadApiService returned failure response, code: {}, message: {}",
                    response.getCode(), response.getMessage());
                return;
            }

            Map<Long, IscMkuAvailableSaleResDTO> availableData = response.getData();
            if (MapUtils.isEmpty(availableData)) {
                log.debug("fillAvailableSale: no available data returned for mkuId: {}", mkuId);
                return;
            }

            IscMkuAvailableSaleResDTO availableInfo = availableData.get(mkuId);
            if (availableInfo != null && iscAppMkuAggreDTO.getBaseInfo() != null) {
                Boolean isAvailableSale = availableInfo.getIsAvailableSale();
                iscAppMkuAggreDTO.getBaseInfo().setIsAvailableSale(isAvailableSale);

                log.debug("fillAvailableSale: mkuId={}, isAvailableSale={}", mkuId, isAvailableSale);
            }

        } catch (Exception e) {
            log.error("fillAvailableSale error for mkuId: {}, error: {}",
                mkuId, e.getMessage(), e);
        }
    }

}
