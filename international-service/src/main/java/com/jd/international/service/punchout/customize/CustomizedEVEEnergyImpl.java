package com.jd.international.service.punchout.customize;

import com.alibaba.fastjson.JSONObject;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.enums.PunchoutFieldEnum;
import com.jd.international.common.exception.BizException;
import com.jd.international.common.utils.Aes256Cipher;
import com.jd.international.common.utils.HttpClientUtil;
import com.jd.international.common.utils.SignUtils;
import com.jd.international.service.customer.CustomerManageService;
import com.jd.international.soa.sdk.order.orderList.res.OrderRes;
import com.jd.international.soa.sdk.order.orderList.res.OrderWaresRes;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.isc.aggregate.read.api.customer.res.CustomerReadResp;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CustomizedEVEEnergyImpl implements CustomizedOrderCallbackService {

    private static final String SECRET_KEY = "12345678901234567890123456789012";
    private static final String SALT = "";

    @Resource
    private CustomerManageService customerManageService;
    @Value(value = "${spring.profiles.active}")
    private String activeProfile;

    @Override
    public JSONObject orderData(OrderRes orderRes, BaseInfo baseInfo) {
        String orderNo = orderRes.getOrderNo();
        Map<String, Object> map = new HashMap<>();
        // 订单号
        map.put(PunchoutFieldEnum.NEW_ITEM_LONGTEXT_n_132.getPrefix(), orderNo);
        // 下单账号（京东PIN）
        map.put(PunchoutFieldEnum.NEW_ITEM_SLD_SYS_NAME.getPrefix(), orderRes.getPin());
        // 订单运费
        map.put(PunchoutFieldEnum.NEW_ITEM_ORDER_FREIGHT.getPrefix(), orderRes.getOrderFreightPrice());
        // 纳税人识别码(税号)
        CustomerReadResp detail = customerManageService.detail(baseInfo.getClientId());
        map.put(PunchoutFieldEnum.NEW_ITEM_PAYERID.getPrefix(), detail.getCustomerTax());
        // 找产品确认下下发票抬头？(先不传)
//        map.put(PunchoutFieldEnum.NEW_ITEM_BILLTO.getPrefix(), "");
        // 收货人
        map.put(PunchoutFieldEnum.NEW_ITEM_CONTRACT.getPrefix(), orderRes.getConsignee());
        // 收货地址
        map.put(PunchoutFieldEnum.NEW_ITEM_CONTRACT_ITEM.getPrefix(), orderRes.getDeliveryAddress());
        // 联系电话
        map.put(PunchoutFieldEnum.NEW_ITEM_EXT_QUOTE_ID.getPrefix(), orderRes.getDeliveryPhone());
        //获取MD5加密签名
        String sign = null;
        try {
            sign = SignUtils.generateSignature(map, SALT);
        } catch (Exception e) {
            log.error("亿纬锂能加密签名失败", e);
            throw new RuntimeException(e);
        }
        map.put("sign", sign);
        List<Map<String, Object>> skuList = new ArrayList<>();
        List<OrderWaresRes> waresReqs = orderRes.getWaresReqs();
        for (OrderWaresRes waresReq : waresReqs) {
            Map<String, Object> skuMap = getSkuMap(waresReq);
            skuList.add(skuMap);
            map.put("sku", skuList);
        }
        return new JSONObject(map);
    }

    @NotNull
    private static Map<String, Object> getSkuMap(OrderWaresRes waresReq) {
        Map<String, Object> skuMap = new HashMap<>();
        // mkuId
        skuMap.put(PunchoutFieldEnum.NEW_ITEM_VENDORMAT.getPrefix(), waresReq.getSku());
        // 商品名
        skuMap.put(PunchoutFieldEnum.NEW_ITEM_DESCRIPTION.getPrefix(), waresReq.getSkuName());
        // 商品图片
        skuMap.put(PunchoutFieldEnum.NEW_ITEM_LONGTEXT.getPrefix(), waresReq.getSkuImg());
        // 商品税额
        if (waresReq.getValueAddedTaxes() != null) {
            skuMap.put(PunchoutFieldEnum.NEW_ITEM_PRICEUNIT.getPrefix(), waresReq.getValueAddedTaxes().toPlainString());
        }
        // 商品税率
        if (waresReq.getValueAddedTaxRate() != null) {
            skuMap.put(PunchoutFieldEnum.NEW_ITEM_MATNR.getPrefix(), waresReq.getValueAddedTaxRate().toPlainString());
        }
        // 商品数量
        skuMap.put(PunchoutFieldEnum.NEW_ITEM_QUANTITY.getPrefix(), waresReq.getSkuNum());
        // 商品价格
        skuMap.put(PunchoutFieldEnum.NEW_ITEM_PRICE.getPrefix(), waresReq.getSkuPrice().toPlainString());

        return skuMap;
    }

    @Override
    public String sendData(String data, BaseInfo baseInfo) {
        try {
            log.info("加密前：{}", data);
            String encodeData = Aes256Cipher.AESEncode(data, SECRET_KEY);
            log.info("加密后：{}", encodeData);
            Map<String, Object> extMaps = baseInfo.getExtMaps();
            String url = String.valueOf(extMaps.get("hookUrl"));
            JSONObject requestData = new JSONObject();
            requestData.put("data", encodeData);
            JSONObject jsonObject = doPost(requestData, url);
            if (jsonObject == null) {
                log.error("亿纬锂能发送订单回传失败。data:{} ,baseInfo:{}", data, baseInfo);
                throw new RuntimeException("亿纬锂能发送订单回传失败。");
            }
            return encodeData;
        } catch (Exception e) {
            log.error("[" + activeProfile + "]" + "亿纬锂能发送订单回传失败。", e);
            throw new BizException(e.getMessage());
        }
    }

    @Override
    public String orderDataDecode(String content, BaseInfo baseInfo) {
        try {
            log.info("orderDataDecode, content: {}", content);
            String decoded = Aes256Cipher.AESDecode(content, SECRET_KEY);
            log.info("orderDataDecode, decoded: {}", decoded);
            return decoded;
        } catch (Exception e) {
            log.error("decode error", e);
            return null;
        }
    }

    public JSONObject doPost(Object encodeData, String url) {
        if (url.contains("https")) {
            try {
                return HttpClientUtil.sendHttps(encodeData, url, "utf-8");
            } catch (Exception e) {
                log.error("亿纬锂能发送订单回传失败。", e);
                return null;
            }
        } else if (url.contains("http")) {
            return HttpClientUtil.httpPostWithJson(encodeData, url, "utf-8");
        }
        return null;
    }
}
