package com.jd.international.service.cart;

import com.jd.common.util.StringUtils;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.enums.TradeModelEnum;
import com.jd.international.rpc.cart.RpcCartService;
import com.jd.international.service.isc.utils.ReqBaseInfoUtil;
import com.jd.international.soa.sdk.common.cart.req.*;
import com.jd.international.soa.sdk.common.cart.resp.CartResp;
import com.jd.international.soa.sdk.common.cart.resp.CartWaresResp;
import com.jd.international.soa.sdk.common.sale.SkuSaleAttrDTO;
import com.jdi.isc.product.soa.api.mku.IscProductSoaMkuReadApiService;
import com.jdi.isc.product.soa.api.mku.req.IscMkuAvailableSaleReq;
import com.jdi.isc.product.soa.api.mku.res.IscMkuAvailableSaleResDTO;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CartServiceImpl implements CartService {
    private final RpcCartService rpcCartService;
    private final IscProductSoaMkuReadApiService iscProductSoaMkuReadApiService;

    @Override
    public int getCartNum(BaseInfo baseInfo) {
        CartReq cartReq = new CartReq();
        cartReq.setPin(baseInfo.getPin());
        cartReq.setContractNum(baseInfo.getContractNum());
        String clientStaffId = baseInfo.getSubAccount();
        if (StringUtils.isNotBlank(clientStaffId)) {
            cartReq.setCode(getCartCode(baseInfo.getPin(), clientStaffId));
        } else {
            cartReq.setCode(getCartCode(baseInfo.getPin()));
        }
        ReqBaseInfoUtil.copyBaseInfo(baseInfo, cartReq);

        return rpcCartService.getCartNum(cartReq);
    }

    @Override
    public CartResp getCartList(BaseInfo baseInfo, Boolean checked) {
        CartReq cartReq = new CartReq();
        cartReq.setPin(baseInfo.getPin());
        cartReq.setContractNum(baseInfo.getContractNum());
        String clientStaffId = baseInfo.getSubAccount();
        if (StringUtils.isNotBlank(clientStaffId)) {
            cartReq.setCode(getCartCode(baseInfo.getPin(), clientStaffId));
        } else {
            cartReq.setCode(getCartCode(baseInfo.getPin()));
        }
        cartReq.setEnv(baseInfo.getEnv());
        cartReq.setClientCode(baseInfo.getClientId());
        cartReq.setChecked(checked);
        ReqBaseInfoUtil.copyBaseInfo(baseInfo, cartReq);
        CartResp cartResp = rpcCartService.getCartList(cartReq);
//        dealSaleAttr(cartResp,baseInfo.getEnv());
        // 批量填充可售状态
//        fillCartAvailableSaleBatch(cartResp, baseInfo);
        return cartResp;
    }

    @Override
    public CartResp addProducts(BaseInfo baseInfo, List<AddCartWaresReq> addCartWaresReqList) {
        AddCartReq addCartReq = new AddCartReq();
        String clientStaffId = baseInfo.getSubAccount();
        if (StringUtils.isNotBlank(clientStaffId)) {
            addCartReq.setCode(getCartCode(baseInfo.getPin(), clientStaffId));
        } else {
            addCartReq.setCode(getCartCode(baseInfo.getPin()));
        }
        addCartReq.setPin(baseInfo.getPin());
        addCartReq.setContractNum(baseInfo.getContractNum());
        addCartReq.setWares(addCartWaresReqList);
        addCartReq.setClientCode(baseInfo.getClientId());
        ReqBaseInfoUtil.copyBaseInfo(baseInfo, addCartReq);

        return rpcCartService.addProducts(addCartReq);
    }

    @Override
    public CartResp updateProductNum(BaseInfo baseInfo, String sku, Integer num) {
        UpdateCartReq updateCartReq = new UpdateCartReq();
        String clientStaffId = baseInfo.getSubAccount();
        if (StringUtils.isNotBlank(clientStaffId)) {
            updateCartReq.setCode(getCartCode(baseInfo.getPin(), clientStaffId));
        } else {
            updateCartReq.setCode(getCartCode(baseInfo.getPin()));
        }
        updateCartReq.setPin(baseInfo.getPin());
        updateCartReq.setContractNum(baseInfo.getContractNum());
        updateCartReq.setSku(sku);
        updateCartReq.setNum(num);
        updateCartReq.setEnv(baseInfo.getEnv());
        ReqBaseInfoUtil.copyBaseInfo(baseInfo, updateCartReq);

        CartResp cartResp = rpcCartService.updateProductNum(updateCartReq);
//        dealSaleAttr(cartResp, baseInfo.getEnv());
        fillCartAvailableSaleBatch(cartResp, baseInfo);
        return cartResp;
    }

    @Override
    public CartResp checkProduct(BaseInfo baseInfo, List<String> skus, Boolean checked) {
        CartReq cartReq = new CartReq();
        cartReq.setPin(baseInfo.getPin());
        cartReq.setContractNum(baseInfo.getContractNum());
        String clientStaffId = baseInfo.getSubAccount();
        if (StringUtils.isNotBlank(clientStaffId)) {
            cartReq.setCode(getCartCode(baseInfo.getPin(), clientStaffId));
        } else {
            cartReq.setCode(getCartCode(baseInfo.getPin()));
        }

        cartReq.setSkus(skus);
        cartReq.setChecked(checked);
        ReqBaseInfoUtil.copyBaseInfo(baseInfo, cartReq);

        return rpcCartService.checkProduct(cartReq);
    }

    @Override
    public CartResp delProducts(BaseInfo baseInfo, List<String> skuIds) {
        DelCartReq delCartReq = new DelCartReq();
        String clientStaffId = baseInfo.getSubAccount();
        if (StringUtils.isNotBlank(clientStaffId)) {
            delCartReq.setCode(getCartCode(baseInfo.getPin(), clientStaffId));
        } else {
            delCartReq.setCode(getCartCode(baseInfo.getPin()));
        }

        delCartReq.setPin(baseInfo.getPin());
        delCartReq.setContractNum(baseInfo.getContractNum());
        delCartReq.setSkuIds(skuIds);
        ReqBaseInfoUtil.copyBaseInfo(baseInfo, delCartReq);

        return rpcCartService.delProducts(delCartReq);
    }

    @Override
    public CartResp getBatchCartList(BatchCartReq batchCartReq) {
        return rpcCartService.getBatchCartList(batchCartReq);
    }

    private String getCartCode(String pin, String clientStaffId) {
        String suffix = clientStaffId == null ? "" : "_" + clientStaffId;
        return TradeModelEnum.ADD_CART_MODEL.getKey() + pin + suffix;
    }

    private String getCartCode(String pin) {
        return TradeModelEnum.ADD_CART_MODEL.getKey() + pin;
    }

//    private void dealSaleAttr(CartResp cartResp, String env){
//        List<CartWaresResp> cartWares = cartResp.getCartWares();
//        if(null != cartResp && !cartWares.isEmpty()){
//            cartWares.forEach(cart ->{
//                List<SkuSaleAttrDTO> saleAttributes = cart.getSaleAttributes();
//                Map<String, List<SkuSaleAttrDTO>> stringListMap = convertToMap(saleAttributes);
//                List<SkuSaleAttrDTO> skuSaleAttrDTOS = processLangData(stringListMap, env);
//                cart.setSaleAttributes(skuSaleAttrDTOS);
//            });
//        }
//
//    }
//
//    private  List<SkuSaleAttrDTO> processLangData(Map<String, List<SkuSaleAttrDTO>> groupedData, String userLang) {
//        List<SkuSaleAttrDTO> result = new ArrayList<>();
//        if(StringUtils.isBlank(userLang)){
//            log.error("销售属性，获取用户语言异常 processLangData");
//            return result;
//        }
//        // 按照 Map 的 key 逐一遍历
//        for (List<SkuSaleAttrDTO> dtoList : groupedData.values()) {
//            // 判断是否有中文的 "其他"
//            boolean hasOther = dtoList.stream()
//                    .anyMatch(dto -> "zh".equals(dto.getAttrLang()) && "其它".equals(dto.getValLangName()));
//
//            // 根据是否有 "其他" 来处理数据
//            dtoList.stream()
//                    .filter(dto -> dto.getAttrLang().equals(userLang)) // 筛选出符合用户语言的属性
//                    .forEach(dto -> {
//                        if (hasOther) {
//                            // 如果有 "其他" 属性，清空当前匹配的语言属性
//                            dto.setValLangName("");
//                            dto.setAttrLangName("");
//                        }
//                        result.add(dto);
//                    });
//        }
//        return result;
//    }
//
//
//
//    public Map<String, List<SkuSaleAttrDTO>> convertToMap(List<SkuSaleAttrDTO> saleAttrDTOList) {
//        if (saleAttrDTOList == null || saleAttrDTOList.isEmpty()) {
//            return Collections.emptyMap();
//        }
//
//        // 使用 groupingBy 进行分组，复合键由 attrAttributeId 和 valAttributeValueId 组成
//        return saleAttrDTOList.stream()
//                .collect(Collectors.groupingBy(dto -> dto.getAttrAttributeId() + ":" + dto.getValAttributeValueId()));
//    }

    /**
     * 批量填充购物车商品可售状态
     * @param cartResp 购物车返回数据
     * @param baseInfo 基础信息
     */
    private void fillCartAvailableSaleBatch(CartResp cartResp, BaseInfo baseInfo) {
        if (cartResp == null || CollectionUtils.isEmpty(cartResp.getCartWares()) || baseInfo == null) {
            log.debug("fillCartAvailableSaleBatch skipped: cartResp, cartWares or baseInfo is null/empty");
            return;
        }

        try {
            // 收集所有有效的MKU ID
            Set<Long> mkuIds = new HashSet<>();
            Map<Long, CartWaresResp> mkuIdToWaresMap = new HashMap<>();

            for (CartWaresResp cartWare : cartResp.getCartWares()) {
                if (cartWare == null || StringUtils.isBlank(cartWare.getSku())) {
                    continue;
                }

                Long mkuId = null;
                try {
                    mkuId = Long.valueOf(cartWare.getSku());
                    mkuIds.add(mkuId);
                    mkuIdToWaresMap.put(mkuId, cartWare);
                } catch (NumberFormatException e) {
                    log.warn("fillCartAvailableSaleBatch skipped invalid sku format: {}", cartWare.getSku());
                    continue;
                }
            }

            if (CollectionUtils.isEmpty(mkuIds)) {
                log.debug("fillCartAvailableSaleBatch: no valid mkuIds found");
                return;
            }

            // 构建可售查询请求
            IscMkuAvailableSaleReq req = new IscMkuAvailableSaleReq();
            req.setClientCode(baseInfo.getClientId());
            req.setCountryCode(baseInfo.getCountry());
            req.setMkuIds(mkuIds);
            req.setLang(baseInfo.getEnv());

            log.debug("fillCartAvailableSaleBatch: calling queryMkuAvailable with req: {}", JSONObject.toJSONString(req));

            DataResponse<Map<Long, IscMkuAvailableSaleResDTO>> response = iscProductSoaMkuReadApiService.queryMkuAvailable(req);

            if (response == null) {
                log.warn("fillCartAvailableSaleBatch: iscProductSoaMkuReadApiService returned null response for clientCode: {}", baseInfo.getClientId());
                return;
            }

            if (!Boolean.TRUE.equals(response.getSuccess())) {
                log.warn("fillCartAvailableSaleBatch: iscProductSoaMkuReadApiService returned failure response, code: {}, message: {}",
                    response.getCode(), response.getMessage());
                return;
            }

            Map<Long, IscMkuAvailableSaleResDTO> availableData = response.getData();
            if (MapUtils.isEmpty(availableData)) {
                log.debug("fillCartAvailableSaleBatch: no available data returned");
                return;
            }

            // 批量处理返回的可售数据
            int processedCount = 0;
            for (Map.Entry<Long, IscMkuAvailableSaleResDTO> entry : availableData.entrySet()) {
                Long mkuId = entry.getKey();
                IscMkuAvailableSaleResDTO availableInfo = entry.getValue();
                CartWaresResp cartWare = mkuIdToWaresMap.get(mkuId);

                if (cartWare == null || availableInfo == null) {
                    continue;
                }

                // 设置可售状态
                Boolean isAvailableSale = availableInfo.getIsAvailableSale();
                cartWare.setIsAvailableSale(isAvailableSale);

                log.debug("fillCartAvailableSaleBatch: mkuId={}, isAvailableSale={}", mkuId, isAvailableSale);
                processedCount++;
            }

            log.debug("fillCartAvailableSaleBatch completed: processed {} out of {} cart items", processedCount, cartResp.getCartWares().size());

        } catch (Exception e) {
            log.error("fillCartAvailableSaleBatch error for clientId: {}, error: {}",
                baseInfo.getClientId(), e.getMessage(), e);
        }
    }

}
