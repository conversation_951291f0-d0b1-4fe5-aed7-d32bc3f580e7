package com.jd.international.service.approval;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.common.util.DateFormatUtils;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.config.ApprovalDUCCConfig;
import com.jd.international.common.config.CommonDUCCConfig;
import com.jd.international.common.config.InternationalConfig;
import com.jd.international.common.config.InternationalDuccConfig;
import com.jd.international.common.constants.ApprovalProcessConstants;
import com.jd.international.common.constants.CommonConstant;
import com.jd.international.common.constants.UMPFunctionKeyConstant;
import com.jd.international.common.enums.AccountTypeEnum;
import com.jd.international.common.enums.NodeStatusEnum;
import com.jd.international.common.enums.OrderStateEnum;
import com.jd.international.common.enums.approval.OrderAuditStatusMergeEnum;
import com.jd.international.common.utils.DateUtils;
import com.jd.international.common.utils.MoneyUtils;
import com.jd.international.common.utils.S3Utils;
import com.jd.international.domain.approval.ApprovalHistoryRecordQuery;
import com.jd.international.domain.approval.ApprovalOrder;
import com.jd.international.domain.approval.multi.ApprovalNodeDomain;
import com.jd.international.domain.approval.multi.IspOrderConfigInfo;
import com.jd.international.domain.message.IscOrderApproveInfo;
import com.jd.international.domain.message.IscOrderApproveMsg;
import com.jd.international.domain.order.OrderSaveInfo;
import com.jd.international.domain.settleconfig.WispConfigurationFunction;
import com.jd.international.rpc.account.RpcAccountService;
import com.jd.international.rpc.approval.IRpcRoleProcessService;
import com.jd.international.rpc.approval.RpcApprovalOrderProcess;
import com.jd.international.rpc.approval.RpcMultiMoreConditionApprovalService;
import com.jd.international.rpc.mail.RpcMailService;
import com.jd.international.rpc.message.JmqProducerService;
import com.jd.international.rpc.product.RpcIJdiStandardProductQueryService;
import com.jd.international.service.adapter.isc.approval.ProcessNodeConvert;
import com.jd.international.service.approval.base.BaseService;
import com.jd.international.service.settleconfig.OrderConfigService;
import com.jd.international.service.settleconfig.SettleConfigService;
import com.jd.international.soa.approval.sdk.resp.ApprovalFileInfo;
import com.jd.international.soa.sdk.common.address.res.PageRes;
import com.jd.international.soa.sdk.order.order.TradeProvider;
import com.jd.international.soa.sdk.order.order.req.SubmitOrderReq;
import com.jd.international.soa.sdk.order.orderList.req.OrderReq;
import com.jd.international.soa.sdk.order.orderList.res.OrderRes;
import com.jd.ka.mro.workflow.soa.sdk.dto.multi.ApprovalNode;
import com.jd.ka.mro.workflow.soa.sdk.dto.multi.MultiApprovalDefine;
import com.jd.ka.mro.workflow.soa.sdk.dto.multi.NodeCondition;
import com.jd.ka.mro.workflow.soa.sdk.dto.multi.ProcessRecordVO;
import com.jd.ka.mro.workflow.soa.sdk.enums.ApprovalNodeType;
import com.jd.ka.mro.workflow.soa.sdk.enums.ApprovalStatusEnum;
import com.jd.ka.mro.workflow.soa.sdk.enums.SymbolEnum;
import com.jd.ka.mro.workflow.soa.sdk.service.MultiConditionProcessService;
import com.jd.ka.mro.workflow.soa.sdk.utils.ApproveStatus;
import com.jd.ka.mro.workflow.soa.sdk.utils.PaginatedResult;
import com.jd.ka.mro.workflow.soa.sdk.utils.ProcessType;
import com.jd.ka.mro.workflow.soa.sdk.vo.common.RPCResult;
import com.jd.ka.mro.workflow.soa.sdk.vo.common.RolePinRelVO;
import com.jd.ka.mro.workflow.soa.sdk.vo.req.OrderApvNodeVO;
import com.jd.ka.mro.workflow.soa.sdk.vo.req.OrderTaskReqVO;
import com.jd.ka.mro.workflow.soa.sdk.vo.res.*;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.library.common.constants.i18n.I18nKeyConstant;
import com.jdi.isc.library.common.exception.BizI18nException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderApprovalServiceImpl extends BaseService implements OrderApprovalService {

    private final ApprovalDUCCConfig approvalDUCCConfig;
    private final RpcIJdiStandardProductQueryService rpcIJdiStandardProductQueryService;
    private final ApprovalFileService approvalFileService;
    private final RpcApprovalOrderProcess rpcApprovalOrderProcess;
    private final IRpcRoleProcessService iRpcRoleProcessService;
    private final CommonDUCCConfig commonDUCCConfig;
    private final RpcMailService rpcMailService;
    private final RpcAccountService rpcAccountService;
    private final TradeProvider tradeProvider;
    private final RpcMultiMoreConditionApprovalService rpcMultiMoreConditionApprovalService;
    private final TransApprovalNodeHandler transApprovalNodeHandler;
    private final SettleConfigService settleConfigService;
    private final MultiMoreConditionApprovalService multiMoreConditionApprovalService;
    private final MultiConditionProcessService multiConditionProcessService;
    private final OrderConfigService orderConfigService;
    private final InternationalDuccConfig internationalDuccConfig;
    private final InternationalConfig internationalConfig;
    private final S3Utils s3Utils;

    @Value("${user.bind.mail.mailType}")
    private int bindMailType;

    @Autowired
    private JmqProducerService jmqProducerService;

    public static final String orderDetailFlowNodesField = "node";

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public boolean isNeedMultiApprove(Map<String, Object> workParam, String processKey) {
        log.info("isNeedMultiApprove -> 判断是否能触发审批并补全参数-> workParam = " + com.alibaba.fastjson.JSON.toJSONString(workParam) + ";  processKey=" + processKey);
        MultiApprovalDefine approvalDefine = null;
        try {
            approvalDefine = rpcMultiMoreConditionApprovalService.getDetail(processKey);
        } catch (Exception e) {
            log.error("查询审批流失败捕捉异常, needApproval=false", e);
            return false;
        }
        if (approvalDefine == null) {
            log.info("判断是否触发审批，查询审批流失败,默认为不触发审批 approvalDefine = null ;processKey=" + processKey);
            return false;
        }

        boolean needApproval = false;
        workParam.put(processKey, approvalDefine.getProcessName());
        if (ProcessType.parseProcessType(approvalDefine.getProcessType()) == ProcessType.NO_AMOUNT_TYPE) {// 无条件审批流
            needApproval = true;
        }
        if(ProcessType.parseProcessType(approvalDefine.getProcessType()) == ProcessType.MULTI_TYPE){
            List<ProcessNodeVO> nodeVoS = rpcMultiMoreConditionApprovalService.getProcessNode(processKey);
            List<ApprovalNodeDomain> approvalNodes = ProcessNodeConvert.INSTANCE.nodeListToApprovalNodeList(nodeVoS);
            List<ApprovalNode> approvalNodeList = transApprovalNodeHandler.transApprovalNodeList(approvalNodes);
            if(CollectionUtils.isEmpty(approvalNodeList)){
                return false;
            }
            transApprovalNodeHandler.transApprovalCondition(approvalNodeList);
            ApprovalNode startNode = approvalNodeList.stream().filter(new Predicate<ApprovalNode>() {
                @Override
                public boolean test(ApprovalNode approvalNode) {
                    return approvalNode != null && ApprovalNodeType.parseApprovalType(approvalNode.getNodeType()) == ApprovalNodeType.PURCHASE_TYPE;
                }
            }).findAny().orElse(null);
            if(startNode == null){
                log.info(" startNode is null");
                return false;
            }
            needApproval = buildNodePriority(startNode,approvalNodeList,workParam);
            log.info("TradeServiceImpl.isNeedMultiApprove -> 判断多条件审批-> workParam ="+ com.alibaba.fastjson.JSON.toJSONString(workParam)+";判断结果:needApproval="+needApproval);
        }
        return needApproval;
//        if (ProcessType.parseProcessType(approvalDefine.getProcessType()) == ProcessType.MULTI_TYPE) {
//            workParam.put("amount",orderPayTotalMoney);
//            List<ProcessNodeVO> nodeVoS = rpcMultiMoreConditionApprovalService.getProcessNode(processKey);
//            List<ApprovalNodeDomain> approvalNodes = ProcessNodeConvert.INSTANCE.nodeListToApprovalNodeList(nodeVoS);
//            List<ApprovalNode> approvalNodeList = transApprovalNodeHandler.transApprovalNodeList(approvalNodes);
//            if (CollectionUtils.isEmpty(approvalNodeList)) {
//                log.info("isNeedMultiApprove, approvalNodeList empty. needApproval=false. processKey={}", processKey);
//                return false;
//            }
//            transApprovalNodeHandler.transApprovalCondition(approvalNodeList);
//            ApprovalNode startNode = approvalNodeList.stream().filter(new Predicate<ApprovalNode>() {
//                @Override
//                public boolean test(ApprovalNode approvalNode) {
//                    return approvalNode != null && ApprovalNodeType.parseApprovalType(approvalNode.getNodeType()) == ApprovalNodeType.PURCHASE_TYPE;
//                }
//            }).findAny().orElse(null);
//
//            if (startNode == null) {
//                log.info("isNeedMultiApprove, startNode is null. needApproval=false. processKey={}", processKey);
//                return false;
//            }
//
//            needApproval = buildNodePriority(startNode, approvalNodeList, workParam);
//            log.info("isNeedMultiApprove, 判断多条件审批 workParam =" + com.alibaba.fastjson.JSON.toJSONString(workParam) + ";判断结果:needApproval=" + needApproval);
//        }

//        log.info("isNeedMultiApprove, needApproval={}, processKey={}", needApproval, processKey);
//        return needApproval;
    }

    @Override
    public Map<String, Object> startOrderApprovalWorkParam(BaseInfo baseInfo, Map<String, Object> orderSaveInfo, BigDecimal totalPrice) {
        log.info("startOrderApprovalWorkParam, baseInfo={}, orderSaveInfo={}", JSONObject.toJSONString(baseInfo), JSONObject.toJSONString(orderSaveInfo));
        Map<String, Object> workParam = this.getBaseWorkParam(totalPrice);

        List<WispConfigurationFunction> configuration = this.getConfiguration(baseInfo);
        if (null!=commonDUCCConfig && Boolean.TRUE.equals(commonDUCCConfig.getLogDebugSwitch())){
            log.info("startOrderApprovalWorkParam, List<WispConfigurationFunction>={}. orderApprovalListReq={}, baseInfo={}",
                    JSONObject.toJSONString(configuration), JSONObject.toJSONString(baseInfo));
        }

        Map<String, Object> extWorkParam = this.getWorkParam(orderSaveInfo, configuration);
        if (MapUtils.isNotEmpty(extWorkParam)) {
            workParam.putAll(extWorkParam);
        }
        log.info("startOrderApprovalWorkParam, workParam={}", JSONObject.toJSONString(workParam));
        return workParam;
    }

    @Override
    public Boolean orderIsNeedApproval(BaseInfo baseInfo, Map<String, Object> workParam, String processKey, Date createOrderTime) {
        if (StringUtils.isBlank(processKey)) {
            log.warn("orderIsNeedApproval, processKey null. baseInfo={}", JSONObject.toJSONString(baseInfo));
            return false;
        }
        return isNeedMultiApprove(workParam, processKey);
    }

    @Override
    public void startMultiProcess(BaseInfo baseInfo, OrderRes orderRes, Map<String, Object> workParam, String processKey) {
        if (MapUtils.isEmpty(workParam) || StringUtils.isBlank(processKey)) {
            log.warn("startMultiProcess, 下单成功，但启动审批失败，原因:workParam=" + (MapUtils.isEmpty(workParam) ? null : JSONObject.toJSONString(workParam) + ";processKey=" + processKey));
            Profiler.businessAlarm(UMPFunctionKeyConstant.UMP_SELF_START_PROCESS+"."+ active, active + LevelCode.P1.getMessage() + "下单成功，但启动审批失败，原因:参数为空;workParam=" + (MapUtils.isEmpty(workParam) ? null : com.alibaba.fastjson.JSON.toJSONString(workParam))
                    + ";processKey=" + processKey + ";订单号orderNo=" + orderRes.getOrderNo());
            return;
        }

        try {
            OrderTaskReqVO taskReqVO = new OrderTaskReqVO();
            taskReqVO.setAmount(orderRes!=null?orderRes.getTotalPrice().multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_UP):null);
            taskReqVO.setJdOrderId(Long.valueOf(orderRes.getOrderNo()));
            taskReqVO.setApplyPin(baseInfo.getPin());
            taskReqVO.setOrderTime(new Date());
            taskReqVO.setSource(1);
            taskReqVO.setParamMap(workParam);
            taskReqVO.setProcessKey(processKey);
            log.info("startMultiProcess, 提单成功启动多条件审批入参:taskReqVO = " + JSONObject.toJSONString(taskReqVO));

            ResultVO<String> resultVO = multiConditionProcessService.startProcess(taskReqVO);
            log.info("startMultiProcess, startProcess result={}", JSONObject.toJSONString(resultVO));
            if (resultVO == null) {
                Profiler.businessAlarm(UMPFunctionKeyConstant.UMP_SELF_START_PROCESS+"."+ active, active + LevelCode.P1.getMessage() + "启动多条件审批响应为空;taskReqVO=" + JSONObject.toJSONString(taskReqVO));
                return;
            }
            if (!resultVO.isSuccess()) {
                Profiler.businessAlarm(UMPFunctionKeyConstant.UMP_SELF_START_PROCESS+"."+ active, active + LevelCode.P1.getMessage() + "启动多条件审批失败;taskReqVO=" + JSONObject.toJSONString(taskReqVO) + ";resultVO=" + JSONObject.toJSONString(resultVO));
                return;
            }
        } catch (Exception e) {
            log.error("startMultiProcess, 提单成功但启动多条件审批失败，原因:" + e.getMessage(), e);
            Profiler.businessAlarm(UMPFunctionKeyConstant.UMP_SELF_START_PROCESS+"."+ active, active + LevelCode.P1.getMessage() + "下单成功，但启动审批失败，原因:消息发送异常;workParam=" + (MapUtils.isEmpty(workParam) ? null : JSONObject.toJSONString(workParam)) +
                    ";processKey=" + processKey + ";订单号:" + orderRes.getOrderNo());
        }
    }

    @Override
    public Map<String, Object> initApprovalQuery(Integer queryType, BaseInfo baseInfo) {
        if (baseInfo == null || baseInfo.getUserType() == null || !super.checkIsApprovalAccount(baseInfo)) {
            throw new BizI18nException("***********", "您当前登录的账号无权进行此操作", I18nKeyConstant.COMMON_NO_PERMISSION);
        }
        if (queryType == null) {
            queryType = 0;
        }
        Map<String, Object> result = Maps.newHashMap();
        Map<String, String> statusMap = Maps.newHashMap();
        if (queryType == 1) {// 待审批没有相应的审批状态查询，只有待审批。审批记录中分为通过和驳回两种
            statusMap.put("", internationalConfig.queryLanValOrDefault(baseInfo.getEnv(), I18nKeyConstant.ALL));
            statusMap.put("2", internationalConfig.queryLanValOrDefault(baseInfo.getEnv(), I18nKeyConstant.APPROVED));
            statusMap.put("3", internationalConfig.queryLanValOrDefault(baseInfo.getEnv(), I18nKeyConstant.REJECTED));
            result.put("approvalStatusSelect", statusMap);
        }
        result.put("accountSelectType", "multiple");
        result.put("showRecordTab", true);
        return result;
    }

    @Override
    public Map<String, Object> approvalList(Long orderErpId, String purchasePin, String batchNumber, String sTimeStr, String eTimeStr, Integer page, String pins, Integer approvalStatus, Integer queryType, Integer pageSize, BaseInfo baseInfo) {
        if (!AccountTypeEnum.APPROVAL_ACCOUNT.getKey().equals(baseInfo.getUserType())) {
            throw new BizI18nException("***********", "您当前登录的账号无权进行此操作", I18nKeyConstant.COMMON_NO_PERMISSION);
        }
        String lang = baseInfo.getEnv();
        Map<String, Object> result = Maps.newHashMap();
        if (queryType == null) {
            queryType = 0;
        }
        if (null == page || page <= 0) {
            page = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = ApprovalProcessConstants.DEFAULT_PAGE_SIZE;
        }
        Date startTime = null, endTime = null;
        if (StringUtils.isNotBlank(eTimeStr)) {
            endTime = DateUtils.parse("yyyy-MM-dd HH:mm:ss", eTimeStr + " 23:59:59");
            if (endTime == null) {
                endTime = new Date();
            }
        }
        if (StringUtils.isNotBlank(sTimeStr)) {
            startTime = DateUtils.parse("yyyy-MM-dd HH:mm:ss", sTimeStr + " 00:00:00");
            if (startTime == null) {
                startTime = DateUtils.getMonthsAgo(3, true);
            }
        }
        List<Integer> auditStatus = OrderAuditStatusMergeEnum.transApprovalStatus(approvalStatus);
        Map<String, Object> queryScheme = Maps.newHashMap();
        PageRes<ApprovalOrder> list = new PageRes<>(page, pageSize);
        if (queryType == 0) {
            PageRes<ApprovalOrder> waitOrderList = getWaitOrderList(orderErpId, pins, batchNumber, startTime, endTime, page, pageSize, baseInfo);
            replaceImgPath(waitOrderList, lang);
            list = addOperate(waitOrderList, baseInfo);
            queryScheme.put("orderErpId", orderErpId);
            queryScheme.put("canUseBatchOrder", true);
        } else if (queryType == 1) {
            PageRes<ApprovalOrder> approvalHistory = getApprovalHistory(orderErpId, pins, batchNumber, auditStatus, startTime, endTime, page, pageSize, baseInfo);
            replaceImgPath(approvalHistory, lang);
            list = addOperate(approvalHistory, baseInfo);
            queryScheme.put("approvalStatus", approvalStatus);
            queryScheme.put("orderId", orderErpId);
            queryScheme.put("pins", pins);
        }
        List<ApprovalOrder> items = list != null ? list.getItems() : null;
        Set<String> skuSet = new HashSet<>();
        for (int i = 0, size = items != null ? items.size() : 0; i < size; i++) {
            skuSet.addAll(items.get(i).getOrderSkus().stream().filter(sku -> sku.getSkuId() != null).map(sku -> String.valueOf(sku.getSkuId())).collect(Collectors.toSet()));
        }
//        Map<String, Boolean> productExtendSign = rpcIJdiStandardProductQueryService.getProductExtendSign(skuSet);
//        Map<String, Object> marking = new HashMap<>();
//        for (int i = 0, size = items != null ? items.size() : 0; i < size; i++) {
//            Map<String, Boolean> skuMarking = new HashMap<>();
//            items.get(i).getOrderSkus().stream().forEach(sku -> {
//                if (sku.getSkuId() != null) {
//                    skuMarking.put(sku.getSkuId()
//                            , (productExtendSign.containsKey(sku.getSkuId())
//                                    ? productExtendSign.get(sku.getSkuId()) : false) && approvalDUCCConfig.getMarkingSwitch());
//                }
//            });
//            marking.put(items.get(i).getJdOrderId(), skuMarking);
//        }
        result.put("orderList", list);
        queryScheme.put("purchasePin", purchasePin);
        queryScheme.put("batchNumber", batchNumber);
        queryScheme.put("queryType", queryType);
//        result.put("marking", marking);
        if (startTime != null) {
            queryScheme.put("startDate", DateFormatUtils.format(startTime, "yyyy-MM-dd"));
        }
        if (endTime != null) {
            queryScheme.put("endDate", DateFormatUtils.format(endTime, "yyyy-MM-dd"));
        }
        result.put("queryScheme", queryScheme);
        return result;
    }

    private void replaceImgPath(PageRes<ApprovalOrder> orderList, String lang) {
        Optional.ofNullable(orderList)
                .map(PageRes::getItems)
                .ifPresent(list -> {
                    list.forEach(approvalOrder -> {
                        Optional.ofNullable(approvalOrder).map(ApprovalOrder::getOrderSkus)
                                .ifPresent(skus -> {
                                    skus.forEach(sku -> {
                                        String imgUrl = sku.getImgUrl();
                                        String replaceHostImgUrl = s3Utils.replaceHost(imgUrl);
                                        sku.setImgUrl(replaceHostImgUrl);
                                    });
                                });
                    });
                });
    }


    private PageRes<ApprovalOrder> getWaitOrderList(Long orderErpId, String pins, String batchNumber, Date startTime, Date endTime, Integer page, Integer pageSize, BaseInfo baseInfo) {
        try {
            if (StringUtils.isNotBlank(pins)) {
                pins = URLDecoder.decode(pins, "utf-8");
            }
        } catch (Exception e) {
            log.error("getWaitOrderList Exception.", e);
            throw new BizI18nException(e.getMessage(), e.getMessage(), I18nKeyConstant.COMMON_ERROR);
        }
        return super.getCurrentApproveList(baseInfo, orderErpId, pins, batchNumber, startTime, endTime, page, pageSize);
    }

    private PageRes<ApprovalOrder> getApprovalHistory(Long orderErpId, String pins, String batchNumber, List<Integer> approvalStatus, Date startTime, Date endTime, Integer page, Integer pageSize, BaseInfo baseInfo) {
        ApprovalHistoryRecordQuery query = new ApprovalHistoryRecordQuery();
        if (CollectionUtils.isNotEmpty(approvalStatus)) {
            query.setApprovalStatus(approvalStatus.get(0));
        }
        query.setIndex(page);
        query.setPageSize(pageSize);
        query.setStartTime(startTime);
        query.setEndTime(endTime);
        query.setJdOrderId(orderErpId);
        query.setBatchId(batchNumber);
        query.setApprovalPin(baseInfo.getPin());
        try {
            if (StringUtils.isNotBlank(pins)) {
                pins = URLDecoder.decode(pins, "utf-8");
                query.setPinList(Lists.newArrayList(pins.split(",")));
            }
        } catch (Exception e) {
            log.error("getWaitOrderList Exception.", e);
            throw new BizI18nException(e.getMessage(), e.getMessage(), I18nKeyConstant.COMMON_ERROR);
        }
        return super.getApprovalHistoryRecordHasChildrenOrder(query, baseInfo);
    }

    private PageRes<ApprovalOrder> addOperate(PageRes<ApprovalOrder> list, BaseInfo baseInfo) {
        if (list == null || CollectionUtils.isEmpty(list.getItems())) {
            return list;
        }
        AccountTypeEnum userType = AccountTypeEnum.parse(baseInfo.getUserType());
        log.info("com.jd.international.service.approval.OrderApprovalServiceImpl.addOperate->list=" + JSON.toJSONString(list));
        list.getItems().stream().forEach(orderItem -> {
            log.info("com.jd.international.service.approval.OrderApprovalServiceImpl.addOperate->orderItem = " + JSON.toJSONString(orderItem));
            Map<String, Object> operateMap = new HashMap<String, Object>(CommonConstant.HASHMAP_INITIAL_CAPACITY);
            operateMap.put("needApproval", false);// 是否走审批流
            operateMap.put("inProcess", false);// 是否在审批中
            operateMap.put("hasFile", false);// 是否有附件信息
            boolean needApproval = false, hasFile = false, inProcess = false;
            if (AccountTypeEnum.MANAGE_ACCOUNT.getKey().equals(userType.getKey())
                    || AccountTypeEnum.PURCHASE_ACCOUNT.getKey().equals(userType.getKey())
                    || AccountTypeEnum.APPROVAL_ACCOUNT.getKey().equals(userType.getKey())) {
                OrderApvNodeVO multiProcess = this.getMultiProcessByOrderId(Long.parseLong(orderItem.getJdOrderId()));
                if (null != multiProcess) {
                    operateMap.put("needApproval", true);
                    needApproval = true;
                    boolean isInProcess = isInProcess(multiProcess);
                    operateMap.put("inProcess", isInProcess);
                    inProcess = isInProcess;
                }
            }
            List<ApprovalFileInfo> fileList = approvalFileService.queryByOrderId(Long.parseLong(orderItem.getJdOrderId()), baseInfo);
            if (CollectionUtils.isNotEmpty(fileList)) {
                // 是否有附件信息
                operateMap.put("hasFile", true);
                hasFile = true;
            }
            boolean showLookFileBtn = false, showUpLoadFileBtn = false, showPassBtn = false, showRejectBtn = false, showFileText = false;
            if (userType != null && needApproval && hasFile &&
                    (userType.getKey() == 6
                            || (userType.getKey() == 5 && inProcess && orderItem.getOrderState().intValue() == OrderStateEnum.WAIT_APPROVEL.getCode().intValue()))) {
                showLookFileBtn = true;
            }
            if (needApproval
                    && (((userType.getKey() == 6
                    || (userType.getKey() == 5 && inProcess && orderItem.getOrderState().intValue() == OrderStateEnum.WAIT_APPROVEL.getCode().intValue()))
                    && !hasFile))) {
                showFileText = true;
            }
            if (needApproval && (userType.getKey() == 5 && inProcess && orderItem.getOrderState().intValue() == OrderStateEnum.WAIT_APPROVEL.getCode().intValue())) {
                showUpLoadFileBtn = true;
            }
            operateMap.put("showLookFileBtn", showLookFileBtn);
            operateMap.put("showUpLoadFileBtn", showUpLoadFileBtn);
            if (userType.getKey() == 6
                    && orderItem.getApprovalStatus() == 1
                    && orderItem.getOrderState().intValue() == OrderStateEnum.WAIT_APPROVEL.getCode().intValue()) {// 多级审批账号控制通过驳回按钮
                showPassBtn = true;
                showRejectBtn = true;
            }
            operateMap.put("showPassBtn", showPassBtn);
            operateMap.put("showRejectBtn", showRejectBtn);
            operateMap.put("showFileText", showFileText);
            orderItem.setOperateMap(operateMap);
            OrderAuditStatusMergeEnum mergeEnum = OrderAuditStatusMergeEnum.getOrderAuditStatus(userType.getKey(), orderItem.getApprovalStatus());
            if (mergeEnum == null) {
                orderItem.setApprovalStatus(OrderAuditStatusMergeEnum.NO_AUDIT.getCode());
            } else {
                orderItem.setApprovalStatus(mergeEnum.getCode());
            }
        });
        return list;
    }

    /**
     * @Description 根据查询出的节点判断是否在审批中，如果没有获取的applyPin就是在审批中
     **/
    private boolean isInProcess(OrderApvNodeVO multiProcess) {
        if (null == multiProcess.getApproveStatus() || com.jd.common.util.StringUtils.isBlank(multiProcess.getApprovePin())) {
            return true;
        }
        if (ApproveStatus.PROCESSING.getType() == multiProcess.getApproveStatus()) {
            return true;
        }
        return false;
    }

    private OrderApvNodeVO getMultiProcessByOrderId(Long jdOrderId) {
        if (null == jdOrderId) {
            log.error("OrderApprovalServiceImpl.getMultiProcessByOrderId.参数异常，直接返回空;jdorderid：" + jdOrderId);
            return null;
        }
        ResultVO<OrderProgressVO> orderApvProgress = rpcMultiApprovalService.getOrderApvProgress(jdOrderId);
        if (orderApvProgress == null || !orderApvProgress.isSuccess() || orderApvProgress.getModel() == null) {
            return null;
        }
        List<OrderApvNodeVO> approveNodeList = orderApvProgress.getModel().getApproveNodeList();
        if (null == approveNodeList || approveNodeList.size() <= 0) {
            return null;
        }
        return approveNodeList.get(0);
    }

    @Override
    public Map<String, Object> getProcessWorkParam(BaseInfo baseInfo, Long jdOrderId) {
        Map<String, Object> workParamMap = Maps.newHashMap();
        try {
            OrderTaskResVO taskResVO = this.getOrderTaskResVO(jdOrderId);
            if (taskResVO == null
                    || StringUtils.isBlank(taskResVO.getProcessId())
                    || StringUtils.isBlank(taskResVO.getProcessKey())) {
                throw new BizI18nException("审批失败,请检查!", I18nKeyConstant.APPROVAL_FAILED);
            }

            List<IspOrderConfigInfo> configInfoList = this.getFunctionByOrderId(baseInfo, jdOrderId);
            if (CollectionUtils.isNotEmpty(configInfoList)) {
                for (IspOrderConfigInfo configInfo : configInfoList) {
                    if (configInfo == null) {
                        continue;
                    }
                    if ("amount".equals(configInfo.getFieldName()) || "skuPrice".equals(configInfo.getFieldName())) {
                        workParamMap.put(configInfo.getFieldName(), new BigDecimal(configInfo.getFieldValue()).setScale(0));
                    } else if (StringUtils.isBlank(configInfo.getFieldValue())) {
                        workParamMap.put(configInfo.getFieldName(), "null");
                    } else {
                        workParamMap.put(configInfo.getFieldName(), configInfo.getFieldValue());
                    }
                }
            }
            //todo:获取总价格
            isNeedMultiApprove(workParamMap, taskResVO.getProcessKey());// 补充审批参数优先级
        } catch (Exception e) {
            log.error("审批执行时，构建审批入参异常 jdOrderId=" + jdOrderId + ":" + e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
        return workParamMap;
    }

    /**
     * 审批通过/驳回动作
     *
     * @param orders
     * @param operate
     * @param reasone
     * @param baseInfo
     * @return
     */
    @Override
    public Boolean approval(String orders, Integer operate, String reasone, BaseInfo baseInfo) {
        log.info("approval, orders={}, operate={}", orders, operate);
        if (StringUtils.isBlank(orders)) {
            throw new BizI18nException("13000000051", "请至少选择一个订单", I18nKeyConstant.SELECT_AT_LEAST_ONE_ORDER);
        }
        try {
            List<ApprovalOrder> orderList = JSON.parseArray(URLDecoder.decode(orders, "UTF-8"), ApprovalOrder.class);
            if (CollectionUtils.isEmpty(orderList)) {
                throw new BizI18nException("13000000051", "请至少选择一个订单", I18nKeyConstant.SELECT_AT_LEAST_ONE_ORDER);
            }
            Set<String> jdOrderIdSet = Sets.newHashSet();
            for (ApprovalOrder approvalOrder : orderList) {
                if (StringUtils.isBlank(approvalOrder.getJdOrderId())) {
                    continue;
                }
                jdOrderIdSet.add(approvalOrder.getJdOrderId());
            }

            Map<Long, OrderRes> orderResMap = super.getOrderMap(jdOrderIdSet, baseInfo);
            if (MapUtils.isEmpty(orderResMap)) {
                throw new BizI18nException("13000000054", "获取订单失败，请稍后重试", I18nKeyConstant.FETCH_ORDER_FAILED);
            }

            Integer approvalType = ApproveStatus.APPROVED.getType();
            if (operate == null || operate != 1) {
                if (StringUtils.isBlank(reasone)) {
                    throw new BizI18nException("13000000052", "驳回原因不能为空", I18nKeyConstant.REJECT_REASON_CANNOT_BE_EMPTY);
                }
                if (reasone.length() > ApprovalProcessConstants.MAX_REASON_LENGTH) {
                    String i18nVal = internationalConfig.queryLanValOrDefault(baseInfo.getEnv(), I18nKeyConstant.REJECT_REASON_TOO_LONG);
                    throw new BizI18nException("13000000053:" + ApprovalProcessConstants.MAX_REASON_LENGTH, String.format(i18nVal, 200));
                }
                approvalType = ApproveStatus.DISAPPROVED.getType();
            }

            return passOrRejectBatch(orderList,approvalType,reasone,baseInfo, orderResMap, orders);
        } catch (Exception e) {
            log.error("approval Exception. orders={}, operate={}", orders, operate, e);
            if (e instanceof BizI18nException) {
                throw new BizI18nException(((BizI18nException) e).getCode(), e.getMessage(), I18nKeyConstant.COMMON_ERROR);
            }
            throw new BizI18nException(I18nKeyConstant.COMMON_ERROR, I18nKeyConstant.COMMON_ERROR);
        }
    }

    private void putOrderApproveMsg(OrderRes orderRes, String orderPin, String approvePin, Map<String, IscOrderApproveMsg> orderApproveMsgMap, OrderTaskResVO orderTaskResVO, Date now){

        String keyPin = orderTaskResVO.getApproveStatus() == ApproveStatus.PROCESSING.getType() ? approvePin : orderPin;
        IscOrderApproveMsg iscOrderApproveMsg = orderApproveMsgMap.get(keyPin);
        if(iscOrderApproveMsg == null){
            iscOrderApproveMsg = new IscOrderApproveMsg();
        }
        iscOrderApproveMsg.setOrderPin(orderPin);
        iscOrderApproveMsg.setApprovePin(approvePin);
        iscOrderApproveMsg.setApproveStatus(orderTaskResVO.getApproveStatus());
        iscOrderApproveMsg.setOperateTime(now.getTime());

        List<IscOrderApproveInfo> iscOrderApproveInfoList = iscOrderApproveMsg.getIscOrderApproveInfoList();
        iscOrderApproveInfoList = CollectionUtils.isEmpty(iscOrderApproveInfoList) ? new ArrayList<>() : iscOrderApproveInfoList;

        iscOrderApproveInfoList.add(new IscOrderApproveInfo(Long.valueOf(orderRes.getOrderNo()), orderRes.getContractNum(), String.valueOf(orderTaskResVO.getApproveRoleId())));
        iscOrderApproveMsg.setIscOrderApproveInfoList(iscOrderApproveInfoList);

        orderApproveMsgMap.put(keyPin, iscOrderApproveMsg);
    }
    private Boolean passOrRejectBatch(List<ApprovalOrder> orderList,Integer approvalType, String reasone,BaseInfo baseInfo, Map<Long, OrderRes> orderResMap, String orders) {
        Map<String, IscOrderApproveMsg> orderApproveMsgListMap = new HashMap<>();
        Map<String, IscOrderApproveMsg> approveProgressListMap = new HashMap<>();
        Date now = new Date();
        for (ApprovalOrder order : orderList) {
            ApproveResultVO task = new ApproveResultVO();
            task.setProcessId(order.getProcessId());
            task.setApprovePin(baseInfo.getPin());
            task.setApproveStatus(approvalType);
            task.setJdOrderId(Long.parseLong(order.getJdOrderId()));
            if(approvalType == ApproveStatus.DISAPPROVED.getType()){
                task.setReason(reasone);
            }
            OrderRes orderRes = orderResMap.get(Long.valueOf(order.getJdOrderId()));
            OrderTaskResVO orderTaskResVO = approveProcessForBatch(baseInfo, task,orderRes);
            //需要判断当前订单审批任务是否是已完成
            if (null != orderTaskResVO && ApproveStatus.APPROVED.getType() == orderTaskResVO.getApproveStatus()) {
                try {
                    OrderReq orderReq=new OrderReq();
                    orderReq.setOrderNo(order.getJdOrderId());
                    orderReq.setOrderStatus(OrderStateEnum.SHIPPING.getCode());
                    rpcOrderListService.updateOrderStatus(orderReq);

                    SubmitOrderReq input = new SubmitOrderReq();
                    input.setOrderNo(order.getJdOrderId());
                    // 审批流完成-通过-发送邮件
                    //this.sendEmail(order.getPin(),order.getJdOrderId());
                    putOrderApproveMsg(orderRes, orderRes.getPin(), baseInfo.getPin(), orderApproveMsgListMap, orderTaskResVO, now);
                } catch (Exception e) {
                    log.error("多级审批|执行审批通过-确认订单失败，订单：" + order.getJdOrderId() + "下单账号：" + order.getPin() + ";审批人：" + baseInfo.getPin());
                    throw new BizI18nException(e.getMessage(),e.getMessage(), I18nKeyConstant.COMMON_ERROR);
                }
            }
            // 任务处于驳回或终止时
            if (null != orderTaskResVO && (ApproveStatus.DISAPPROVED.getType() == orderTaskResVO.getApproveStatus()
                    || ApproveStatus.INTERRUPT.getType() == orderTaskResVO.getApproveStatus())) {
                try {
                    rpcOrderQueryProvider.cancelOrderById(order.getJdOrderId(), baseInfo);
                    //this.sendEmailCancel(order.getPin(),order.getJdOrderId());
                    putOrderApproveMsg(orderRes, orderRes.getPin(), baseInfo.getPin(), orderApproveMsgListMap, orderTaskResVO, now);
                }catch (Exception e){
                    log.error("多级审批|执行订单驳回-取消订单失败，订单：" + order.getJdOrderId() + "下单账号：" + order.getPin() + ";审批人：" + baseInfo.getPin() + "。原因：" + e.getMessage());
                    throw new BizI18nException(e.getMessage(),e.getMessage(), I18nKeyConstant.COMMON_ERROR);
                }
            }
            //下级审批提醒
            if(null != orderTaskResVO && ApproveStatus.PROCESSING.getType() == orderTaskResVO.getApproveStatus()){
                try {
                    String nextApprovalPin = nextApprovalPin(orderTaskResVO.getApproveRoleId(), baseInfo.getContractNum());
                    putOrderApproveMsg(orderRes, baseInfo.getPin(), nextApprovalPin, approveProgressListMap, orderTaskResVO, now);
                }catch (Exception e){
                    log.error("发送提醒邮件失败:"+e.getMessage(),e);
                }
            }
        }
        // 发送mq
        sendMail(baseInfo, orderApproveMsgListMap);
        log.info("发送邮件信息 one :{} , list:{}",baseInfo.getPin(),JSONObject.toJSONString(orderApproveMsgListMap));
        sendMail(baseInfo, approveProgressListMap);
        log.info("发送邮件信息 two :{} , list:{}",baseInfo.getPin(),JSONObject.toJSONString(approveProgressListMap));
        return Boolean.TRUE;
    }

    private void sendMail(BaseInfo baseInfo, Map<String, IscOrderApproveMsg> orderApproveMsgListMap){
        if(MapUtils.isNotEmpty(orderApproveMsgListMap)){
            orderApproveMsgListMap.entrySet().forEach(entry -> {
                IscOrderApproveMsg iscOrderApproveMsg = entry.getValue();
                jmqProducerService.sendOrderApproveMsg(baseInfo, iscOrderApproveMsg, iscOrderApproveMsg.getOrderPin() + "," + iscOrderApproveMsg.getOperateTime());
            });
        }
    }

    private String nextApprovalPin(Long current_role_id, String contract_number) {
        Set<String> nextApprovalPin = this.getApprovalPinByRoleId(current_role_id, contract_number);
        return StringUtils.join(nextApprovalPin, ",");
//        return nextApprovalPin.stream().findFirst().orElse(null);
    }


//    private Boolean passOrReject(List<ApprovalOrder> orderList,Integer approvalType, String reasone,BaseInfo baseInfo) {
//        for (ApprovalOrder order : orderList) {
//            ApproveResultVO task = new ApproveResultVO();
//            task.setProcessId(order.getProcessId());
//            task.setApprovePin(baseInfo.getPin());
//            task.setApproveStatus(approvalType);
//            task.setJdOrderId(Long.parseLong(order.getJdOrderId()));
//            if (approvalType == ApproveStatus.DISAPPROVED.getType()) {
//                task.setReason(reasone);
//            }
//
//            OrderTaskResVO orderTaskResVO = approveProcessForBatch(baseInfo, task);
//
//            if (null != orderTaskResVO && ApproveStatus.APPROVED.getType() == orderTaskResVO.getApproveStatus()) {// 需要判断当前订单审批任务是否是已完成
//                try {
//                    OrderReq orderRes = new OrderReq();
//                    orderRes.setOrderNo(order.getJdOrderId());
//                    orderRes.setOrderStatus(OrderStateEnum.SHIPPING.getCode());
//                    rpcOrderListService.updateOrderStatus(orderRes);
//
//                    SubmitOrderReq input = new SubmitOrderReq();
//                    input.setOrderNo(order.getJdOrderId());
//                    // 审批流完成-通过-发送邮件
//                    this.sendEmail(order.getPin(), order.getJdOrderId());
//                    /*if(tradeProvider.triggerEptOrder(input).isSuccess()){
//                        //触发异步ept提单
//                        this.sendEmail(order.getPin(),order.getJdOrderId());
//                    }else {
//                        log.error("OrderApprovalServiceImpl.passOrReject 触发ept异步提单失败, target:{} ", order.getJdOrderId());
//                        throw new RuntimeException(order.getJdOrderId()+" ept异步提单失败");
//                    }*/
//                } catch (Exception e) {
//                    log.error("多级审批|执行审批通过-确认订单失败，订单：" + order.getJdOrderId() + "下单账号：" + order.getPin() + ";审批人：" + baseInfo.getPin());
//                    throw new BizException(e.getMessage(), e.getMessage());
//                }
//            }
//
//            if (null != orderTaskResVO && (ApproveStatus.DISAPPROVED.getType() == orderTaskResVO.getApproveStatus()
//                    || ApproveStatus.INTERRUPT.getType() == orderTaskResVO.getApproveStatus())) { // 任务处于驳回或终止时
//                try {
//                    rpcOrderQueryProvider.cancelOrderById(order.getJdOrderId(), baseInfo);
//                    this.sendEmailCancel(order.getPin(), order.getJdOrderId());
//                } catch (Exception e) {
//                    log.error("多级审批|执行订单驳回-取消订单失败，订单：" + order.getJdOrderId() + "下单账号：" + order.getPin() + ";审批人：" + baseInfo.getPin() + "。原因：" + e.getMessage());
//                    throw new BizException(e.getMessage(), e.getMessage());
//                }
//            }
//
//            if (null != orderTaskResVO && ApproveStatus.PROCESSING.getType() == orderTaskResVO.getApproveStatus()) {// 下级审批提醒
//                try {
//                    this.sendApprovalEmail(String.valueOf(orderTaskResVO.getJdOrderId()), orderTaskResVO.getApproveRoleId(), orderTaskResVO.getContractNumber());
//                } catch (Exception e) {
//                    log.error("发送提醒邮件失败:" + e.getMessage(), e);
//                }
//            }
//        }
//        return Boolean.TRUE;
//    }

    private OrderTaskResVO approveProcessForBatch(BaseInfo baseInfo, ApproveResultVO task,OrderRes orderRes) {
        if (null == task || null == task.getProcessId()) {
            throw new BizI18nException("13000000055", "未指定要审批的订单，无法审批。", I18nKeyConstant.NO_ORDER_SPECIFIED_FOR_APPROVAL);
        }
        if (ApproveStatus.DISAPPROVED.getType() == task.getApproveStatus() && StringUtils.isBlank(task.getReason())) {
            throw new BizI18nException("13000000052", "驳回订单请指明原因。", I18nKeyConstant.REJECT_ORDER_PLEASE_SPECIFY_REASON);
        }
        if (ApproveStatus.DISAPPROVED.getType() == task.getApproveStatus() && task.getReason().length() > ApprovalProcessConstants.MAX_REASON_LENGTH) {
            String i18nVal = internationalConfig.queryLanValOrDefault(baseInfo.getEnv(), I18nKeyConstant.REJECT_REASON_TOO_LONG);
            throw new BizI18nException("13000000053:", String.format(i18nVal, String.valueOf(ApprovalProcessConstants.MAX_REASON_LENGTH)));
        }

        Map<String, Object> workParam = this.getProcessWorkParam(baseInfo, task.getJdOrderId());
        if (MapUtils.isNotEmpty(workParam)) {
            if (MapUtils.isNotEmpty(task.getParamMap())) {
                task.getParamMap().putAll(workParam);
            } else {
                task.setParamMap(workParam);
            }
        }

        ResultVO<OrderTaskResVO> resultVO = rpcApprovalOrderProcess.approveProcess(task);
        if (resultVO == null) {
            throw new BizI18nException("13000000056", "审批失败", I18nKeyConstant.APPROVAL_FAILED);
        }
        if (!resultVO.isSuccess()) {
            throw new BizI18nException(resultVO.getMessage(), resultVO.getMessage(), I18nKeyConstant.COMMON_OPERATE_FAIL);
        }
        return resultVO.getModel();
    }

//    /**
//     * 审批通过发送邮件给下级审批人
//     *
//     * @param jdOrderId       申请人pin
//     * @param current_role_id 当前审批角色
//     */
//    private void sendApprovalEmail(String jdOrderId, Long current_role_id, String contract_number) {
//        Set<String> approvalPin = this.getApprovalPinByRoleId(current_role_id, contract_number);
//        if (CollectionUtils.isEmpty(approvalPin)) {
//            return;
//        }
//        for (String apvPin : approvalPin) {
//            AccountInfoDTO accountInfoDTO = rpcAccountService.queryAccountByPin(apvPin);
//            if (accountInfoDTO == null || StringUtils.isBlank(accountInfoDTO.getEmail())) {
//                continue;
//            }
//            SendMailReq customMail = new SendMailReq();
//            customMail.setEmailAddress(accountInfoDTO.getEmail());
//            customMail.setPin(apvPin);
//            customMail.setType(bindMailType);
//            Map<Object, Object> keyValues = new HashMap<>(CommonConstant.HASHMAP_INITIAL_CAPACITY);
//            keyValues.put("title", "京东工采国际平台提醒您：有订单确认完成。");
//            keyValues.put("pin", apvPin);
//            Map<String, Object> model = new HashMap<>(CommonConstant.HASHMAP_INITIAL_CAPACITY);
//            model.put("approvalPin", apvPin);
//            model.put("orderNum", jdOrderId);
//            TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig("template", TemplateConfig.ResourceMode.CLASSPATH));
//            Template template = engine.getTemplate("orderApprovalMailTemplate.ftl");
//            String htmlContent = template.render(model);
//            keyValues.put("content", htmlContent);
//            customMail.setKeyValues(keyValues);
//            rpcMailService.sendMail(customMail);
//        }
//    }

//    /**
//     * 订单完成邮件发送
//     *
//     * @param pin
//     * @param orderNo
//     */
//    private void sendEmail(String pin, String orderNo) {
//        log.info("OrderServiceImpl.sendEmail -> pin:{} , orderNo:{}", pin, orderNo);
//        List<String> operatorEmail = StringUtils.isNotBlank(commonDUCCConfig.getOperatorEmail()) ? Lists.newArrayList(commonDUCCConfig.operatorEmail.split(",")) : Lists.newArrayList();
//        log.info("operatorEmail List -> {} ", operatorEmail);
//        // 判断参数
//        if (StringUtils.isNotBlank(orderNo) && CollectionUtils.isNotEmpty(operatorEmail)) {
//            // 准备发送邮件
//            for (String email : operatorEmail) {
//                SendMailReq customMail = new SendMailReq();
//                customMail.setEmailAddress(email);
//                customMail.setPin(pin);
//                customMail.setType(bindMailType);
//                Map<Object, Object> keyValues = new HashMap<Object, Object>(CommonConstant.HASHMAP_INITIAL_CAPACITY);
//                keyValues.put("title", "京东工采国际平台提醒您：有订单确认完成。");
//                keyValues.put("pin", pin);
//                Map<String, Object> model = new HashMap<String, Object>(CommonConstant.HASHMAP_INITIAL_CAPACITY);
//                model.put("pin", pin);
//                model.put("orderNum", orderNo);
//                TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig("template", TemplateConfig.ResourceMode.CLASSPATH));
//                Template template = engine.getTemplate("orderCompletion.ftl");
//                String htmlContent = template.render(model);
//                keyValues.put("content", htmlContent);
//                customMail.setKeyValues(keyValues);
//                rpcMailService.sendMail(customMail);
//            }
//        }
//    }

//    /**
//     * 订单取消邮件发送
//     *
//     * @param pin     pin
//     * @param orderNo 订单号
//     */
//    private void sendEmailCancel(String pin, String orderNo) {
//        log.info("OrderServiceImpl.sendEmail -> pin:{} , orderNo:{}", pin, orderNo);
//        List<String> operatorEmail = StringUtils.isNotBlank(commonDUCCConfig.getOperatorEmail()) ? Lists.newArrayList(commonDUCCConfig.operatorEmail.split(",")) : Lists.newArrayList();
//        log.info("operatorEmail List -> {} ", operatorEmail);
//        // 判断参数
//        if (StringUtils.isNotBlank(orderNo) && CollectionUtils.isNotEmpty(operatorEmail)) {
//            // 准备发送邮件
//            for (String email : operatorEmail) {
//                SendMailReq customMail = new SendMailReq();
//                customMail.setEmailAddress(email);
//                customMail.setPin(pin);
//                customMail.setType(bindMailType);
//                Map<Object, Object> keyValues = new HashMap<Object, Object>(CommonConstant.HASHMAP_INITIAL_CAPACITY);
//                keyValues.put("title", "京东工采国际平台提醒您：有订单取消。");
//                keyValues.put("pin", pin);
//                Map<String, Object> model = new HashMap<String, Object>(CommonConstant.HASHMAP_INITIAL_CAPACITY);
//                model.put("pin", pin);
//                model.put("orderNum", orderNo);
//                TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig("template", TemplateConfig.ResourceMode.CLASSPATH));
//                Template template = engine.getTemplate("orderCancel.ftl");
//                String htmlContent = template.render(model);
//                keyValues.put("content", htmlContent);
//                customMail.setKeyValues(keyValues);
//                rpcMailService.sendMail(customMail);
//            }
//        }
//    }

    @Override
    public OrderApprovalNodeVO getWaitApprovalNode(Long jdOrderId, BaseInfo baseInfo){
        log.info("getWaitApprovalNode orderId = {}, baseInfo = {}", jdOrderId, JSON.toJSONString(baseInfo));
        if (jdOrderId == null || jdOrderId <= 0L) {
            throw new BizI18nException("13000000064", "查询订单审批流程失败", I18nKeyConstant.QUERY_ORDER_APPROVAL_PROCESS_FAILED);
        }

        OrderApprovalProgressVO progressVO = getProgressVO(baseInfo, jdOrderId);
        log.info("getWaitApprovalNode, progressVO = {} orderId = {}, baseInfo = {}", JSON.toJSONString(progressVO), jdOrderId, JSON.toJSONString(baseInfo));
        if (progressVO == null) {
            return null;
        }

        List<OrderApprovalNodeVO> nodeVOList = progressVO.getNodeVOList();
        if(ApprovalStatusEnum.PROCESSING.getStatus() != progressVO.getApprovalStatus() || CollectionUtils.isEmpty(nodeVOList)){
            return null;
        }
        Optional<OrderApprovalNodeVO> reduce = progressVO.getNodeVOList().stream().filter(node -> node.getApprovalStatus() != null && node.getApprovalStatus() == ApprovalStatusEnum.PROCESSING.getStatus()).findFirst();
        return reduce.orElse(null);
    }

    @Override
    public Map<String, Object> flow(Long jdOrderId, BaseInfo baseInfo) {
        if (jdOrderId == null || jdOrderId <= 0L) {
            throw new BizI18nException("13000000064", "查询订单审批流程失败", I18nKeyConstant.QUERY_ORDER_APPROVAL_PROCESS_FAILED);
        }

        OrderApprovalProgressVO progressVO = getProgressVO(baseInfo, jdOrderId);
        Map<String, Object> res = Maps.newHashMap();
        if (!(Optional.ofNullable(progressVO).isPresent() && Optional.ofNullable(progressVO.getNodeVOList()).isPresent())) {
            return res;
        }
        List<OrderApprovalNodeVO> nodeVOList = progressVO.getNodeVOList();
        res.put("showRemind", false);
        if (ApprovalStatusEnum.DISAPPROVED.getStatus() == progressVO.getApprovalStatus() || ApprovalStatusEnum.INTERRUPT.getStatus() == progressVO.getApprovalStatus()) {
            nodeVOList = nodeVOList.stream().filter(node -> node.getNodeStatus() != 2).collect(Collectors.toList());
        } else if (ApprovalStatusEnum.PROCESSING.getStatus() == progressVO.getApprovalStatus()) {
            Optional<OrderApprovalNodeVO> reduce = progressVO.getNodeVOList().stream().filter(node -> node.getNodeStatus() == 0).reduce((a, b) -> b);
            if (reduce.isPresent()) {
                LocalDateTime localDate = reduce.get().getDealTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                Duration duration = Duration.between(localDate, LocalDateTime.now());
                if (duration.toDays() >= 1) {
                    RPCResult<ApprovalTaskVO> approvalTaskVO = rpcMultiApprovalService.queryApprovalTask(jdOrderId);
                    if (approvalTaskVO.isSuccess() && approvalTaskVO.getResult() != null && approvalTaskVO.getResult().getTipStatus() == 1) {
                        res.put("showRemind", true);
                    }
                }
            }
        }

        handleMultilingual(nodeVOList, baseInfo);
        res.put(orderDetailFlowNodesField, nodeVOList);
        res.put("approvalStatus", progressVO.getApprovalStatus());
        return res;
    }

    /**
     * 处理多语种信息
     */
    private void handleMultilingual(List<OrderApprovalNodeVO> nodeVOList, BaseInfo baseInfo) {
    }

    @Override
    public OrderApprovalProgressVO getProgressVO(BaseInfo baseInfo, Long approvalOrderId) throws BizI18nException {
        OrderApprovalProgressVO orderApprovalProgressVO = this.getOrderProgress(baseInfo, approvalOrderId);
        OrderApprovalProgressVO result = supOrderApprovalProgressVO(orderApprovalProgressVO, baseInfo);
        log.info("getProgressVO, OrderApprovalProgressVO={}, approvalOrderId={}", JSONObject.toJSONString(result), approvalOrderId);
        return result;
    }

    private OrderApprovalProgressVO supOrderApprovalProgressVO(OrderApprovalProgressVO orderApprovalProgressVO, BaseInfo baseInfo) {
        String contractNumber = baseInfo.getContractNum();
        if (orderApprovalProgressVO == null || CollectionUtils.isEmpty(orderApprovalProgressVO.getNodeVOList())) {
            return orderApprovalProgressVO;
        }
        for (OrderApprovalNodeVO orderApprovalNodeVO : orderApprovalProgressVO.getNodeVOList()) {
            // 添加审批状态str
            if (orderApprovalNodeVO.getNodeStatus() != null && ApprovalProcessConstants.WAIT_NODESTATUS.equals(orderApprovalNodeVO.getNodeStatus())) {
                orderApprovalNodeVO.setApprovalStatusStr(internationalConfig.queryLanValOrDefault(baseInfo.getEnv(), I18nKeyConstant.PENDING_APPROVAL));
            } else {
                orderApprovalNodeVO.setApprovalStatusStr(parseApprovalStatus(orderApprovalNodeVO.getApprovalStatus()));
            }
            // 补充操作人
            fillApprovePin(orderApprovalNodeVO, contractNumber);
        }
        return orderApprovalProgressVO;
    }

    /**
     * 补充操作人
     *
     * @param orderApprovalNodeVO
     * @return
     */
    private void fillApprovePin(OrderApprovalNodeVO orderApprovalNodeVO, String contractNumber) {
        if (StringUtils.isNotBlank(orderApprovalNodeVO.getCurrentApprovePin())) {
            return;
        }
        if (orderApprovalNodeVO.getRoleId() == null || orderApprovalNodeVO.getRoleId() < 1L) {
            return;
        }
        Set<String> apvPinSet = this.getApprovalPinByRoleId(orderApprovalNodeVO.getRoleId(), contractNumber);
        if (CollectionUtils.isNotEmpty(apvPinSet)) {
            orderApprovalNodeVO.setCurrentApprovePin(StringUtils.join(apvPinSet, CommonConstant.PRODUCT_ATTR_BRAND_SPLIT));
        }
    }

    private Set<String> getApprovalPinByRoleId(Long roleId, String contract_number) {
        Set<String> apvPinSet = null;
        PaginatedResult<RolePinRelVO> result = iRpcRoleProcessService.getApprovalByRoleId(roleId, contract_number);
        if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getList())) {
            return apvPinSet;
        }
        List<RolePinRelVO> rolePinRelVOlist = result.getList();
        if (CollectionUtils.isNotEmpty(rolePinRelVOlist)) {
            apvPinSet = Sets.newHashSet(Lists.transform(rolePinRelVOlist, new Function<RolePinRelVO, String>() {
                @Override
                public String apply(RolePinRelVO relVO) {
                    return relVO.getPin();
                }
            }));
        }
        return apvPinSet;
    }

    /**
     * parseApprovalStatus
     *
     * @param approvalStatus
     * @return
     */
    private String parseApprovalStatus(Integer approvalStatus) {
        if (approvalStatus == null) {
            return null;
        }
        ApprovalStatusEnum[] approvalStatusEnums = ApprovalStatusEnum.values();
        for (ApprovalStatusEnum e : approvalStatusEnums) {
            if (e.getStatus() == approvalStatus) {
                return e.getDesc();
            }
        }
        return null;
    }

    @Override
    public OrderApprovalProgressVO getOrderProgress(BaseInfo baseInfo, Long approvalOrderId) throws BizI18nException {
        OrderTaskResVO taskResVO = this.getOrderTaskResVO(approvalOrderId);
        if (taskResVO == null
                || StringUtils.isBlank(taskResVO.getProcessId())
                || StringUtils.isBlank(taskResVO.getProcessKey())) {
            log.info("getOrderProgress, param null. approvalOrderId={}, taskResVO={}", approvalOrderId, com.alibaba.fastjson.JSONObject.toJSONString(taskResVO));
            return null;
        }
        OrderApprovalProgressVO progressVO = null;
        try {
            MultiApprovalDefine define = rpcMultiMoreConditionApprovalService.getDetail(taskResVO.getProcessKey());
            if (define == null) {
                log.info("getOrderProgress, MultiApprovalDefine null. approvalOrderId={}, taskResVO={}", approvalOrderId, com.alibaba.fastjson.JSONObject.toJSONString(taskResVO));
                return null;
            }

            if (ProcessType.MULTI_TYPE != ProcessType.parseProcessType(define.getProcessType())) {// 非多条件审批时 走原来的查询逻辑
                RPCResult<OrderApprovalProgressVO> rpcResult = rpcMultiApprovalService.queryOrderProgress(approvalOrderId);
                if (null != rpcResult && rpcResult.isSuccess()) {
                    log.info("getOrderProgress, OrderApprovalProgressVO null. approvalOrderId={}, rpcResult={}", approvalOrderId, com.alibaba.fastjson.JSONObject.toJSONString(rpcResult));
                    progressVO = rpcResult.getResult();
                }
            } else {
                // 多条件审批流
                List<ProcessNodeVO> nodeVoS = rpcMultiMoreConditionApprovalService.getProcessNode(define.getProcessKey());
                List<ApprovalNodeDomain> approvalNodes = ProcessNodeConvert.INSTANCE.nodeListToApprovalNodeList(nodeVoS);
                List<ApprovalNode> approvalNodeList = transApprovalNodeHandler.transApprovalNodeList(approvalNodes);
                if (CollectionUtils.isEmpty(approvalNodeList)) {
                    log.info("getOrderProgress, approvalNodeList null. approvalOrderId={}, define={}, nodeVoS={}, approvalNodes={}",
                            approvalOrderId, com.alibaba.fastjson.JSONObject.toJSONString(define), com.alibaba.fastjson.JSONObject.toJSONString(nodeVoS), com.alibaba.fastjson.JSONObject.toJSONString(approvalNodes));
                    return null;
                }
                transApprovalNodeHandler.transApprovalCondition(approvalNodeList);
                List<IspOrderConfigInfo> configInfoList = this.getFunctionByOrderId(baseInfo, approvalOrderId);
                if (CollectionUtils.isEmpty(configInfoList)) {
                    configInfoList = Lists.newArrayList();
                }
                progressVO = this.checkOrderProgress(baseInfo, taskResVO, approvalNodeList, configInfoList);
                progressVO.setProcessType(define.getProcessType());
            }
        } catch (Exception e) {
            log.error("查询流程失败, approvalOrderId=" + approvalOrderId, e);
            throw new BizI18nException(e.getMessage(), I18nKeyConstant.COMMON_ERROR);
        }

        log.info("getOrderProgress, progressVO={}, approvalOrderId={}", com.alibaba.fastjson.JSONObject.toJSONString(progressVO), approvalOrderId);
        return progressVO;
    }

    /**
     * 获取当前审批
     *
     * @param approvalOrderId
     * @return
     */
    @Override
    public OrderTaskResVO getOrderTaskResVO(Long approvalOrderId) {
        ResultVO<List<OrderTaskResVO>> resultVO = rpcMultiApprovalService.getOrderListByJdOrderIds(Sets.newHashSet(approvalOrderId));
        if (resultVO == null || !resultVO.isSuccess()) {
            return null;
        }
        List<OrderTaskResVO> taskResVOList = resultVO.getModel();
        if (CollectionUtils.isEmpty(taskResVOList)) {
            return null;
        }
        return taskResVOList.get(0);
    }

    @Override
    public List<OrderTaskResVO> getOrderTaskResVoList(Set<Long> approvalOrderIds) {
        ResultVO<List<OrderTaskResVO>> resultVO = rpcMultiApprovalService.getOrderListByJdOrderIds(approvalOrderIds);
        if (resultVO == null || !resultVO.isSuccess()) {
            return null;
        }
        return resultVO.getModel();
    }

    private Map<String, Object> getBaseWorkParam(BigDecimal totalPrice) {
        Map<String, Object> workParamMap = Maps.newHashMap();
        if(null != totalPrice){
            workParamMap.put("amount", totalPrice.multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_UP));//添加订单总金额
        }
//        Map<Long, BigDecimal> skuTotalPriceMap = orderPriceInfo.getSkuPriceMap();
//        BigDecimal skuPrice = BigDecimal.ZERO;
//        for (Map.Entry<Long, BigDecimal> entry : skuTotalPriceMap.entrySet()) {
//            if (entry.getValue() == null || entry.getValue().compareTo(BigDecimal.ZERO) <= 0) {
//                continue;
//            }
//            if (skuPrice.compareTo(entry.getValue()) < 0) {
//                skuPrice = entry.getValue();
//            }
//        }
//        workParamMap.put("skuPrice", (skuPrice.multiply(new BigDecimal(100))).setScale(0, BigDecimal.ROUND_UP));//筛选最高的商品价格
        return workParamMap;
    }

    /**
     * 获取自定义下拉
     *
     * @param baseInfo
     * @return
     */
    private List<WispConfigurationFunction> getConfiguration(BaseInfo baseInfo) {
        WispConfigurationFunction ispConfigurationFunction = new WispConfigurationFunction();
        ispConfigurationFunction.setExt1(CommonConstant.VALID);// 1 显示 其他:不显示
        List<WispConfigurationFunction> ispConfigurationFunctions = settleConfigService.queryConfigurationFunctionListByCondition(ispConfigurationFunction, baseInfo);
        return ispConfigurationFunctions;
    }

    /**
     * 补充审批流参数
     *
     * @param orderSaveInfo
     * @param configuration
     * @return
     */
    private Map<String, Object> getWorkParam(Map<String, Object> orderSaveInfo, List<WispConfigurationFunction> configuration) {
        Map<String, Object> workParamMap = Maps.newHashMap();
        if (MapUtils.isEmpty(orderSaveInfo) || orderSaveInfo.get(CommonConstant.SETTLE_INFO) == null || !(orderSaveInfo.get(CommonConstant.SETTLE_INFO) instanceof String)) {
            log.info("getWorkParam, orderSaveInfo error. orderSaveInfo={}", JSONObject.toJSONString(orderSaveInfo));
            return workParamMap;
        }
        List<OrderSaveInfo> settleInfo = JSON.parseArray(MapUtils.getString(orderSaveInfo, CommonConstant.SETTLE_INFO), OrderSaveInfo.class);
        if (CollectionUtils.isEmpty(settleInfo)) {
            log.info("getWorkParam, List<OrderSaveInfo> empty. orderSaveInfo={}", JSONObject.toJSONString(orderSaveInfo));
            return workParamMap;
        }
        Map<Long, WispConfigurationFunction> functionMap = this.getConfigFuncMap(configuration);
        if (MapUtils.isEmpty(functionMap)) {
            log.info("getWorkParam, Map<Long, WispConfigurationFunction> empty. configuration={}", JSONObject.toJSONString(configuration));
            return workParamMap;
        }

        for (OrderSaveInfo saveInfo : settleInfo) {
            if (saveInfo == null || StringUtils.isBlank(saveInfo.getValue()) || functionMap.get(saveInfo.getId()) == null) {
                continue;
            }
            workParamMap.put(("ext_" + functionMap.get(saveInfo.getId()).getId().longValue()), saveInfo.getValue());
        }
        return workParamMap;
    }

    /**
     * 转换参数
     *
     * @param configuration
     * @return
     */
    private Map<Long, WispConfigurationFunction> getConfigFuncMap(List<WispConfigurationFunction> configuration) {
        if (CollectionUtils.isEmpty(configuration)) {
            return null;
        }
        Map<Long, WispConfigurationFunction> functionMap = Maps.newHashMap();
        for (WispConfigurationFunction function : configuration) {
            if (function == null) {
                continue;
            }
            functionMap.put(function.getId(), function);
        }
        return functionMap;
    }

    /**
     * 查询订单自定义属性
     *
     * @param baseInfo
     * @param orderId
     * @return
     */
    private List<IspOrderConfigInfo> getFunctionByOrderId(BaseInfo baseInfo, Long orderId) {
        List<IspOrderConfigInfo> configInfoList = new ArrayList<>();
        //new amount
        OrderRes orderRes = rpcOrderListService.queryByOrderId(String.valueOf(orderId), baseInfo);
        configInfoList.add(getOrderAmountConfigInfo(orderRes.getTotalPrice()));
        //  查询订单配置信息
        List<IspOrderConfigInfo> infos = orderConfigService.queryIspOrderConfigInfo(orderId);
        Map<String, IspOrderConfigInfo> orderConfigInfoMap = this.orderConfigInfoMap(infos);
        WispConfigurationFunction ispConfigurationFunction = new WispConfigurationFunction();
        ispConfigurationFunction.setExt1(CommonConstant.VALID);// 1 显示 其他:不显示
        List<WispConfigurationFunction> functionList = settleConfigService.queryConfigurationFunctionListByCondition(ispConfigurationFunction, baseInfo);
        if (CollectionUtils.isEmpty(functionList)) {
            return configInfoList;
        }

        // 暂时不用project_name
//        IspProject project = orderProjectNameMapper.queryProjectByOrderId(parentOrderId);
        for (WispConfigurationFunction function : functionList) {
            if (function == null
                    || function.getId() == null
                    || StringUtils.isBlank(function.getCategory())) {
                continue;
            }
            IspOrderConfigInfo configInfo = new IspOrderConfigInfo();
            configInfo.setFieldName("ext_" + function.getId().longValue());
            configInfo.setOrderId(orderId);
//            if (project != null && PROJECT_NAME.equals(function.getCategory())) {
//                configInfo.setFieldValue(project.getProjectName());
//            } else {
            if (MapUtils.isEmpty(orderConfigInfoMap) || orderConfigInfoMap.get(function.getCategory()) == null) {
                configInfo.setFieldValue("null");
            } else {
                configInfo.setFieldValue(orderConfigInfoMap.get(function.getCategory()).getFieldValue());
            }
//            }
            configInfoList.add(configInfo);
        }
        return configInfoList;
    }

    private IspOrderConfigInfo getOrderAmountConfigInfo(BigDecimal orderAmount){
        IspOrderConfigInfo configInfo = new IspOrderConfigInfo();
        configInfo.setFieldName("amount");

        configInfo.setFieldValue(MoneyUtils.getString((orderAmount.multiply(new BigDecimal(100))).setScale(0,BigDecimal.ROUND_UP)));
        return configInfo;
    }

    /**
     * 获取当前订单的多条件审批进度
     *
     * @param taskResVO
     * @param approvalNodeList
     * @param configInfoList
     * @return
     */
    private OrderApprovalProgressVO checkOrderProgress(BaseInfo baseInfo, OrderTaskResVO taskResVO, List<ApprovalNode> approvalNodeList, List<IspOrderConfigInfo> configInfoList) {
        ApprovalNode startNode = this.getStartNode(approvalNodeList);// 采购人节点(既流程的开始节点)

        OrderApprovalProgressVO progressVO = new OrderApprovalProgressVO();
        progressVO.setJdOrderId(taskResVO.getJdOrderId());
        progressVO.setProcessId(taskResVO.getProcessId());
        progressVO.setProcessKey(taskResVO.getProcessKey());
        progressVO.setApprovalStatus(taskResVO.getApproveStatus());
        progressVO.setReason(taskResVO.getReason());

        List<OrderApprovalNodeVO> nodeVOList = Lists.newArrayList();
        // 添加采购员
        OrderApprovalNodeVO nodeVO = new OrderApprovalNodeVO();
        nodeVO.setNodeName(internationalConfig.queryLanValOrDefault(baseInfo.getEnv(), I18nKeyConstant.PURCHASE_USER));
        nodeVO.setNodeStatus(NodeStatusEnum.PROCESSED.getStatus());
        nodeVO.setApprovalStatus(ApprovalStatusEnum.APPLY.getStatus());
        nodeVO.setCurrentApprovePin(taskResVO.getApplyPin());
        nodeVO.setDealTime(taskResVO.getOrderTime());
        nodeVOList.add(nodeVO);

        // 添加审批节点(递归运算出符合条件的节点)
        this.buildOrderApprovalNodeVO(startNode, nodeVOList, taskResVO, approvalNodeList, this.configInfoMap(configInfoList));

        if (CollectionUtils.isNotEmpty(nodeVOList)) {
            List<ProcessRecordVO> apvRecordVOList = null;
            try {
                apvRecordVOList = rpcMultiMoreConditionApprovalService.queryProcessHistoryByJdOrderId(taskResVO.getJdOrderId());
            } catch (Exception e) {
                log.error("查询审批记录失败:" + e.getMessage(), e);
            }
            Map<Long, ProcessRecordVO> recordVOMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(apvRecordVOList)) {
                recordVOMap = this.buildProcessRecordVO(apvRecordVOList);
            }

            boolean currentTaskProcessing = ApprovalStatusEnum.PROCESSING.getStatus() == taskResVO.getApproveStatus();
            for (int index = 1; index < nodeVOList.size(); index++) {// 采购人无需重复赋值
                OrderApprovalNodeVO approvalNode = nodeVOList.get(index);
                if (approvalNode == null || approvalNode.getRoleId() == null) {
                    continue;
                }
                if (approvalNode.getRoleId().equals(taskResVO.getApproveRoleId()) && currentTaskProcessing) {
                    // 执行中的节点
                    approvalNode.setNodeStatus(NodeStatusEnum.PROCESSING.getStatus());
                    continue;
                }
                ProcessRecordVO processRecord = recordVOMap.get(approvalNode.getRoleId());
                if (processRecord == null) {
                    // 未执行的节点
                    approvalNode.setNodeStatus(NodeStatusEnum.UNPROCESSED.getStatus());
                    continue;
                }
                approvalNode.setApprovalStatus(processRecord.getApproveStatus());
                approvalNode.setDealTime(processRecord.getCreated());
                approvalNode.setOperateTime(processRecord.getCreated());
                approvalNode.setReason(processRecord.getReason());
                approvalNode.setApprovalTaskId(taskResVO.getCurrentTaskId());
                if (ApprovalStatusEnum.PROCESSING.getStatus() != approvalNode.getApprovalStatus()) {
                    // 已执行的节点
                    approvalNode.setNodeStatus(NodeStatusEnum.PROCESSED.getStatus());
                }
                approvalNode.setCurrentApprovePin(processRecord.getApprovePin());
            }
        }

        progressVO.setNodeVOList(nodeVOList);
        return progressVO;
    }

    /**
     * 寻找start节点
     *
     * @param nodeList
     * @return
     */
    private ApprovalNode getStartNode(List<ApprovalNode> nodeList) {
        return nodeList.stream().filter(new Predicate<ApprovalNode>() {
            @Override
            public boolean test(ApprovalNode approvalNode) {
                return approvalNode != null && approvalNode.getIsRoot().intValue() == ApprovalNodeType.PURCHASE_TYPE.getType().intValue();
            }
        }).findAny().orElse(null);
    }

    /**
     * 将订单中的自定义内容转换为Map，为接下来的运算做准备
     *
     * @param configInfoList
     * @return
     */
    private Map<String, String> configInfoMap(List<IspOrderConfigInfo> configInfoList) {
        if (CollectionUtils.isEmpty(configInfoList)) {
            return Maps.newHashMap();
        }
        Map<String, String> configInfoMap = Maps.newHashMap();
        for (IspOrderConfigInfo configInfo : configInfoList) {
            if (configInfo == null
                    || StringUtils.isBlank(configInfo.getFieldName())
                    || StringUtils.isBlank(configInfo.getFieldValue())) {
                continue;
            }
            configInfoMap.put(configInfo.getFieldName(), configInfo.getFieldValue());
        }
        return configInfoMap;
    }

    /**
     * 递归计算出触发审批的链路
     *
     * @param currentNode
     * @param nodeVOList
     * @param taskResVO
     * @param approvalNodeList
     * @param configInfoMap
     */
    private void buildOrderApprovalNodeVO(ApprovalNode currentNode, List<OrderApprovalNodeVO> nodeVOList, final OrderTaskResVO taskResVO, final List<ApprovalNode> approvalNodeList, final Map<String, String> configInfoMap) {
        if (currentNode != null) {
            List<ApprovalNode> nextNodeList = this.getNextApprovalNodes(currentNode, approvalNodeList);
            if (CollectionUtils.isNotEmpty(nextNodeList)) {
                nextNodeList.sort(new Comparator<ApprovalNode>() {// 按照优先级先进行排序
                    @Override
                    public int compare(ApprovalNode node1, ApprovalNode node2) {
                        return node1.getPriority().compareTo(node2.getPriority());
                    }
                });
                ApprovalNode approvalNode = null;
                for (ApprovalNode node : nextNodeList) {
                    if (node == null || ApprovalNodeType.parseApprovalType(node.getNodeType()) == ApprovalNodeType.PURCHASE_TYPE) {// 采购节点或空节点直接返回
                        continue;
                    }
                    if (approvalNode == null) {
                        if (ApprovalNodeType.parseApprovalType(node.getNodeType()) == ApprovalNodeType.CONDITION_TYPE) {// 条件节点
                            List<NodeCondition> conditionList = node.getConditionList();
                            if (calculationCondition(conditionList, configInfoMap)) {// 符合当前条件节点的条件之后，递归当前节点的下级节点，并记录到nodeVOList里
                                approvalNode = node;
                                break;
                            }
                        } else if (ApprovalNodeType.parseApprovalType(node.getNodeType()) == ApprovalNodeType.APPROVAL_TYPE) {
                            approvalNode = node;
                            break;
                        }
                    }
                }
                if (approvalNode != null) {
                    if (ApprovalNodeType.APPROVAL_TYPE.getType().intValue() == approvalNode.getNodeType().intValue()) {
                        OrderApprovalNodeVO apvNode = new OrderApprovalNodeVO();
                        apvNode.setNodeName(approvalNode.getNodeName());
                        apvNode.setRoleId(approvalNode.getRoleId());
                        apvNode.setSpecifyApprover(approvalNode.getSpecifyApprover());
                        if (approvalNode.getRoleId() != null && taskResVO.getApproveRoleId().longValue() == approvalNode.getRoleId().longValue()) {
                            apvNode.setApprovalStatus(taskResVO.getApproveStatus());
                            // 如果有指定当前审批人则直接取指定的审批人
                            if (StringUtils.isNotBlank(taskResVO.getCurrentApprovePin())) {
                                apvNode.setCurrentApprovePin(taskResVO.getCurrentApprovePin());
                            }
                        }
                        nodeVOList.add(apvNode);
                    }
                    this.buildOrderApprovalNodeVO(approvalNode, nodeVOList, taskResVO, approvalNodeList, configInfoMap);
                }
            }
        }
    }

    /**
     * 计算条件
     *
     * @param conditionList
     * @param configInfoMap
     * @return
     */
    private static boolean calculationCondition(List<NodeCondition> conditionList, Map<String, String> configInfoMap) {
        if (CollectionUtils.isEmpty(conditionList) || MapUtils.isEmpty(configInfoMap)) {
            return false;
        }
        boolean calculation = true;
        for (NodeCondition condition : conditionList) {
            if (condition == null
                    || StringUtils.isBlank(condition.getFieldKey())
                    || StringUtils.isBlank(configInfoMap.get(condition.getFieldKey()))) {
                calculation = false;
                break;
            }
            if (SymbolEnum.BETWEEN.name().equals(condition.getOperation())
                    && (StringUtils.isBlank(condition.getMinFieldValue())
                    && StringUtils.isBlank(condition.getMaxFieldValue()))) {// 介于的时候
                calculation = false;
                break;
            } else {
                if (StringUtils.isBlank(condition.getFieldValue())) {
                    calculation = false;
                    break;
                }
            }
            calculation = (calculation && calculation(condition, configInfoMap));
        }
        log.info("MultiMoreConditionApprovalServiceImpl.calculationCondition 审批进度计算-> conditionList= " + com.alibaba.fastjson.JSON.toJSONString(conditionList) + ";configInfoMap=" + com.alibaba.fastjson.JSON.toJSONString(configInfoMap) + ";calculation=" + calculation);
        return calculation;
    }

    /**
     * 各个操作符的运算
     *
     * @param condition
     * @param configInfoMap
     * @return
     */
    private static boolean calculation(NodeCondition condition, Map<String, String> configInfoMap) {
        if (StringUtils.isBlank(configInfoMap.get(condition.getFieldKey()))) {
            return false;
        }
        if (SymbolEnum.EQ.name().equals(condition.getOperation())) {// 等于
            if (condition.getFieldKey().equals("amount") || condition.getFieldKey().equals("skuPrice")) {// BigDecimal格式计算
                return (new BigDecimal(configInfoMap.get(condition.getFieldKey()))).compareTo(new BigDecimal(condition.getFieldValue())) == 0;
            } else {
                return configInfoMap.get(condition.getFieldKey()).compareTo(condition.getFieldValue()) == 0;
            }
        } else if (SymbolEnum.BETWEEN.name().equals(condition.getOperation())) {
            if (condition.getFieldKey().equals("amount") || condition.getFieldKey().equals("skuPrice")) {// BigDecimal格式计算
                return (new BigDecimal(configInfoMap.get(condition.getFieldKey())).compareTo(new BigDecimal(condition.getMinFieldValue()))) >= 0
                        && (new BigDecimal(configInfoMap.get(condition.getFieldKey())).compareTo(new BigDecimal(condition.getMaxFieldValue()))) <= 0;
            } else {
                return (configInfoMap.get(condition.getFieldKey()).compareTo(condition.getMinFieldValue())) >= 0
                        && (configInfoMap.get(condition.getFieldKey()).compareTo(condition.getMaxFieldValue())) <= 0;
            }
        } else if (SymbolEnum.GR.name().equals(condition.getOperation())) {
            if (condition.getFieldKey().equals("amount") || condition.getFieldKey().equals("skuPrice")) {// BigDecimal格式计算
                return (new BigDecimal(configInfoMap.get(condition.getFieldKey()))).compareTo(new BigDecimal(condition.getFieldValue())) > 0;
            } else {
                return configInfoMap.get(condition.getFieldKey()).compareTo(condition.getFieldValue()) > 0;
            }
        } else if (SymbolEnum.GR_EQ.name().equals(condition.getOperation())) {
            if (condition.getFieldKey().equals("amount") || condition.getFieldKey().equals("skuPrice")) {// BigDecimal格式计算
                return (new BigDecimal(configInfoMap.get(condition.getFieldKey()))).compareTo(new BigDecimal(condition.getFieldValue())) >= 0;
            } else {
                return configInfoMap.get(condition.getFieldKey()).compareTo(condition.getFieldValue()) >= 0;
            }
        } else if (SymbolEnum.LT.name().equals(condition.getOperation())) {
            if (condition.getFieldKey().equals("amount") || condition.getFieldKey().equals("skuPrice")) {// BigDecimal格式计算
                return (new BigDecimal(configInfoMap.get(condition.getFieldKey()))).compareTo(new BigDecimal(condition.getFieldValue())) < 0;
            } else {
                return configInfoMap.get(condition.getFieldKey()).compareTo(condition.getFieldValue()) < 0;
            }
        } else if (SymbolEnum.LT_EQ.name().equals(condition.getOperation())) {
            if (condition.getFieldKey().equals("amount") || condition.getFieldKey().equals("skuPrice")) {// BigDecimal格式计算
                return (new BigDecimal(configInfoMap.get(condition.getFieldKey()))).compareTo(new BigDecimal(condition.getFieldValue())) <= 0;
            } else {
                return configInfoMap.get(condition.getFieldKey()).compareTo(condition.getFieldValue()) <= 0;
            }
        }
        return false;
    }

    /**
     * 获取当前节点的所有下级节点(没有下级了，则表示是结束节点)
     *
     * @param currentNode
     * @param nodeList
     * @return
     */
    private static List<ApprovalNode> getNextApprovalNodes(ApprovalNode currentNode, List<ApprovalNode> nodeList) {
        if (currentNode == null) {
            return null;
        }
        List<ApprovalNode> nextNodeList = Lists.newArrayList();
        for (ApprovalNode node : nodeList) {
            if (node == null || StringUtils.isBlank(node.getPids())) {
                continue;
            }
            if (Lists.newArrayList(node.getPids().split(CommonConstant.DEFAULT_SPLIT_CHAR)).contains(currentNode.getId())) {// node节点的pids包含currentNode节点的id时，node为currentNode的下级节点
                nextNodeList.add(node);
            }
        }
        return nextNodeList;
    }

    /**
     * 构建recordMap
     *
     * @param apvRecordVOList
     * @return
     */
    private Map<Long, ProcessRecordVO> buildProcessRecordVO(List<ProcessRecordVO> apvRecordVOList) {
        Map<Long, ProcessRecordVO> recordVOMap = Maps.newHashMap();
        for (ProcessRecordVO recordVO : apvRecordVOList) {
            if (recordVO == null || recordVO.getApproveRoleId() == null) {
                continue;
            }
            recordVOMap.put(recordVO.getApproveRoleId(), recordVO);
        }
        return recordVOMap;
    }

    /**
     * 转换
     *
     * @param configInfoList
     * @return
     */
    private Map<String, IspOrderConfigInfo> orderConfigInfoMap(List<IspOrderConfigInfo> configInfoList) {
        if (CollectionUtils.isEmpty(configInfoList)) {
            return null;
        }
        Map<String, IspOrderConfigInfo> configInfoMap = Maps.newHashMap();
        for (IspOrderConfigInfo orderConfig : configInfoList) {
            if (orderConfig == null || StringUtils.isBlank(orderConfig.getFieldName())) {
                continue;
            }
            configInfoMap.put(orderConfig.getFieldName(), orderConfig);
        }
        return configInfoMap;
    }


    /**
     * 递归构建优先级和审批roleId
     *
     * @param approvalNode
     * @param approvalNodeList
     * @param workParam
     * @return
     */
    private static boolean buildNodePriority(ApprovalNode approvalNode, List<ApprovalNode> approvalNodeList, Map<String, Object> workParam) {
        boolean needApproval = Boolean.FALSE;
        if (approvalNode != null) {
            List<ApprovalNode> childNodeList = getNextApprovalNodes(approvalNode, approvalNodeList);
            if (CollectionUtils.isNotEmpty(childNodeList)) {// 不为空表示还有子节点，为空表示已经到达结尾节点
                ApprovalNode node = buildApprovalWorkParam(childNodeList, workParam);
                if (node != null) {// 不为空表示当前审批存在能触发的审节点，为空表示当前的childNodeList都不能触发 无需继续构建参数
                    needApproval = Boolean.TRUE;
                    buildNodePriority(node, approvalNodeList, workParam);// 向深层递归  当前层结束
                }
                for (ApprovalNode childNode : childNodeList) {
                    if (node != null && childNode.getId().equals(node.getId())) {
                        continue;// 已触发的不走这里
                    }
                    putOtherPriority(childNode, workParam, approvalNodeList);
                }
            }else {
                log.info("buildNodePriority, getNextApprovalNodes empty. needApproval=false.");
            }
        }
        return needApproval;
    }

    /**
     * 构建优先级以及审批节点RoleId
     *
     * @param childNodeList
     * @param workParam
     * @return
     */
    private static ApprovalNode buildApprovalWorkParam(List<ApprovalNode> childNodeList, Map<String, Object> workParam) {
        childNodeList.sort(new Comparator<ApprovalNode>() {
            @Override
            public int compare(ApprovalNode node1, ApprovalNode node2) {
                return (node1 != null && node2 != null) ? node1.getPriority().compareTo(node2.getPriority()) : 0;
            }
        });

        ApprovalNode canApprovalNode = null;
        for (ApprovalNode node : childNodeList) {
            if (node == null) {
                continue;
            }
            if (node.getNodeType().intValue() == ApprovalNodeType.CONDITION_TYPE.getType().intValue()) {// 条件节点的处理
                if (canApprovalNode == null) { // 为空表示还未到能触发的节点，不为空表示已有触发审批的节点，其余通过优先级控制不触发
                    boolean needApproval = calculationOrderCondition(workParam, node.getConditionList());
                    if (needApproval) {
                        workParam.put(calPriority(node.getId()), node.getPriority());
                        canApprovalNode = node;
                    } else {
                        workParam.put(calPriority(node.getId()), -1);
                    }
                } else {// 为空表示还未到能触发的节点，不为空表示已有触发审批的节点，其余通过优先级控制不触发
                    workParam.put(calPriority(node.getId()), -1);
                }
            } else if (node.getNodeType().intValue() == ApprovalNodeType.APPROVAL_TYPE.getType().intValue()) {// 审批节点默认走审批
                if (canApprovalNode == null) {
                    canApprovalNode = node;
                }
            }
        }
        return canApprovalNode;
    }

    /**
     * 计算是否触发审批
     *
     * @param workParam
     * @param conditionList
     * @return
     */
    private static boolean calculationOrderCondition(Map<String, Object> workParam, List<NodeCondition> conditionList) {
        log.info("calculationOrderCondition, workParam={}. conditionList={}.", JSONObject.toJSONString(workParam), JSONObject.toJSONString(conditionList));
        if (CollectionUtils.isEmpty(conditionList) || MapUtils.isEmpty(workParam)) {
            log.info("calculationOrderCondition, parma empty. return false.");
            return false;
        }
        boolean needApproval = true;
        for (NodeCondition condition : conditionList) {
            if (condition == null) {
                continue;
            }
            if (workParam.get(condition.getFieldKey()) == null || StringUtils.isBlank(condition.getOperation())) {
                needApproval = false;
                log.info("calculationOrderCondition, parma null, break. needApproval={}", needApproval);
                break;
            }
            Integer compare = null;
            if (StringUtils.isNotBlank(condition.getFieldValue())) {
                compare = fieldValueCompare(workParam, condition.getFieldKey(), condition.getFieldValue());
            }
            log.info("calculationOrderCondition, condition={}, compare={}", JSONObject.toJSONString(condition) ,compare);

            if (condition.getOperation().equals(SymbolEnum.EQ.name())
                    && StringUtils.isNotBlank(condition.getFieldValue())) {// 等于
                needApproval = (needApproval && compare == 0);
            }
            if (condition.getOperation().equals(SymbolEnum.BETWEEN.name())
                    && StringUtils.isNotBlank(condition.getMinFieldValue())
                    && StringUtils.isNotBlank(condition.getMaxFieldValue())) {// 介于
                needApproval = (needApproval
                        && fieldValueCompare(workParam, condition.getFieldKey(), condition.getMinFieldValue()) >= 0
                        && fieldValueCompare(workParam, condition.getFieldKey(), condition.getMaxFieldValue()) <= 0);
            }
            if (condition.getOperation().equals(SymbolEnum.GR.name()) && StringUtils.isNotBlank(condition.getFieldValue())) {
                needApproval = (needApproval && compare > 0);
            }
            if (condition.getOperation().equals(SymbolEnum.GR_EQ.name()) && StringUtils.isNotBlank(condition.getFieldValue())) {
                needApproval = (needApproval && compare >= 0);
            }
            if (condition.getOperation().equals(SymbolEnum.LT.name()) && StringUtils.isNotBlank(condition.getFieldValue())) {
                needApproval = (needApproval && compare < 0);
            }
            if (condition.getOperation().equals(SymbolEnum.LT_EQ.name()) && StringUtils.isNotBlank(condition.getFieldValue())) {
                needApproval = (needApproval && compare <= 0);
            }
            if (!needApproval) {// 结果为false 直接跳出 不再继续计算
                log.info("calculationOrderCondition, needApproval={}, condition={}, compare={}", needApproval, JSONObject.toJSONString(condition) ,compare);
                break;
            }
        }
        log.info("calculationOrderCondition, needApproval={}, workParam={}. conditionList={}.", needApproval, JSONObject.toJSONString(workParam), JSONObject.toJSONString(conditionList));
        return needApproval;
    }

    /**
     * 对比值
     *
     * @param workParam
     * @param fieldKey
     * @param conditionValue
     * @return
     */
    private static Integer fieldValueCompare(Map<String, Object> workParam, String fieldKey, String conditionValue) {
        if(workParam.get(fieldKey) instanceof BigDecimal){
            log.info("TradeServiceImpl.fieldValueCompare-> BigDecimal workParam.get(fieldKey)="+workParam.get(fieldKey) + ";conditionValue="+conditionValue);
            return ((BigDecimal)workParam.get(fieldKey)).compareTo(new BigDecimal(conditionValue));
        }else if(workParam.get(fieldKey) instanceof Integer){
            log.info("TradeServiceImpl.fieldValueCompare-> Integer workParam.get(fieldKey)="+workParam.get(fieldKey) + ";conditionValue="+conditionValue);
            return ((Integer)workParam.get(fieldKey)).compareTo(Integer.parseInt(conditionValue));
        }
        log.info("TradeServiceImpl.fieldValueCompare-> String workParam.get(fieldKey)="+workParam.get(fieldKey) + ";conditionValue="+conditionValue);
        return String.valueOf(workParam.get(fieldKey)).compareTo(conditionValue);
    }

    /**
     * 每一级审批节点都有对应的优先级
     *
     * @param id
     * @return
     */
    private static String calPriority(String id) {
        return "priority_" + id;
    }

    /**
     * 不符合条件的划线节点都需要补充优先级参数，否则会审批报错
     *
     * @param approvalNode
     * @param workParam
     * @param approvalNodeList
     */
    private static void putOtherPriority(ApprovalNode approvalNode, Map<String, Object> workParam, List<ApprovalNode> approvalNodeList) {
        if (approvalNode != null) {
            List<ApprovalNode> childNodeList = getNextApprovalNodes(approvalNode, approvalNodeList);
            if (CollectionUtils.isNotEmpty(childNodeList)) {
                for (ApprovalNode childNode : childNodeList) {
                    if (childNode == null) {
                        continue;
                    }
                    if (childNode.getNodeType().intValue() == ApprovalNodeType.CONDITION_TYPE.getType().intValue()) {
                        workParam.put(calPriority(childNode.getId()), -1);
                    }
                    putOtherPriority(childNode, workParam, approvalNodeList);
                }
            }
        }
    }
}
