package com.jd.international.service.user.impl;

import com.alibaba.fastjson2.JSONObject;
import com.jd.international.common.constants.CacheKeyConstant;
import com.jd.international.common.constants.LoginConstant;
import com.jd.international.rpc.user.RpcUserService;
import com.jd.international.service.cache.JimCacheService;
import com.jd.international.service.user.UserService;
import com.jd.international.soa.sdk.common.user.res.ContractInfoRes;
import com.jd.international.soa.sdk.common.user.res.EnterpriseUserAllRes;
import com.jd.user.sdk.export.UserPassportExportService;
import com.jd.user.sdk.export.constant.Constants;
import com.jd.user.sdk.export.domain.passport.LoginPageParam;
import com.jd.user.sdk.export.domain.passport.LoginParam;
import com.jd.user.sdk.export.domain.passport.LoginResult;
import com.jd.user.sdk.export.domain.passport.LogoutResult;
import com.jdi.isc.library.common.exception.BizI18nException;
import com.jdi.isc.library.i18n.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Consts;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.jd.international.common.constants.LoginConstant.PASSPORT_SERVICE_EXCEPTION;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2024/8/28 13:18
 **/

@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final RpcUserService rpcUserService;
    private final JimCacheService jimCacheService;
    /**
     * 用户服务，用于处理用户登陆登出
     */
    @Resource
    private UserPassportExportService userPassportExportService;

    @Value("${spring.profiles.active}")
    private String env;

    /**
     * 根据pin查询企业用户信息。
     *
     * @param pin 用户的pin码。
     * @return 企业用户的详细信息。
     */
    @Override
    public EnterpriseUserAllRes queryEnterpriseUserByPin(String pin) {
        String key = CacheKeyConstant.getKey(CacheKeyConstant.USER_INFO, pin);
        String value = jimCacheService.getStr(key);
        if (StringUtils.isBlank(value)) {
            value = jimCacheService.getStr(key);
            if (StringUtils.isBlank(value)) {
                EnterpriseUserAllRes enterpriseUserByPin = rpcUserService.queryEnterpriseUserByPin(pin);
                jimCacheService.setStr(key, 600, JSONObject.toJSONString(enterpriseUserByPin));
                return enterpriseUserByPin;
            }
        }
        return JSONObject.parseObject(value, EnterpriseUserAllRes.class);
    }

    /**
     * 根据pin查询国际企业用户信息
     *
     * @param pin 用户的pin码
     * @return 企业用户的详细信息，若不是国际企业用户则返回null
     */
    @Override
    public EnterpriseUserAllRes queryInternationEnterpriseUserByPin(String pin) {
        EnterpriseUserAllRes enterpriseUserAllRes = queryEnterpriseUserByPin(pin);
        if (checkIsInternationUser(enterpriseUserAllRes)) {
            return enterpriseUserAllRes;
        }
        log.warn("queryInternationEnterpriseUserByPin, not EnterpriseUser. pin={}", pin);
        return null;
    }

    /**
     * 判断企业用户是否为工业国际用户。
     *
     * @param enterpriseUser 企业用户信息对象。
     * @return true 如果是工业国际用户，false 否则。
     */
    @Override
    public boolean checkIsInternationUser(EnterpriseUserAllRes enterpriseUser) {
        if (null == enterpriseUser || null == enterpriseUser.getContractInfoRes()) {
            log.warn("checkIsInternationUser, EnterpriseUserAllRes null. enterpriseUser={}", JSONObject.toJSONString(enterpriseUser));
            return Boolean.FALSE;
        }

        ContractInfoRes contractInfoRes = enterpriseUser.getContractInfoRes();
        Integer contractType = contractInfoRes.getContractType();
        if (contractType == 48) {
            // 工业国际用户
            return Boolean.TRUE;
        } else {
            log.warn("checkIsInternationUser, not Internation User null. enterpriseUser={}, contractInfoRes={}"
                    , JSONObject.toJSONString(enterpriseUser));
            return Boolean.FALSE;
        }
    }

    /**
     * 检查是否为国际用户
     *
     * @param pin 用户的PIN码
     * @return true表示是国际用户，false表示不是国际用户
     */
    @Override
    public boolean checkIsInternationUser(String pin) {
        EnterpriseUserAllRes enterpriseUser = queryInternationEnterpriseUserByPin(pin);
        return null != enterpriseUser;
    }

    /**
     * 登录方法，根据提供的登录参数进行身份验证并返回登录结果。
     *
     * @param loginParam 登录参数对象，包含用户名、密码、设备信息等。
     * @return 登录结果对象，包含登录状态、用户信息等。
     */
    @Override
    public LoginResult login(LoginParam loginParam) {
        LoginResult loginResult = null;
        try {
            loginParam.setAuthType(LoginConstant.AUTH_TYPE);
            loginParam.setDeviceName(LoginConstant.DEFAULT_VALUE);
            loginParam.setDeviceOS(LoginConstant.DEFAULT_VALUE);
            loginParam.setDeviceOSVersion(LoginConstant.DEFAULT_VALUE);
            loginParam.setDeviceVersion(LoginConstant.DEFAULT_VALUE);
            loginParam.setEquipmentId(LoginConstant.DEFAULT_VALUE);
            HashMap<String, String> map = new HashMap<>();
            map.put(Constants.LoginParam.CHANNEL, LoginConstant.CHANNEL);
            map.put(Constants.LoginParam.APP_ID, LoginConstant.APP_ID);
            map.put(Constants.LoginParam.EQUIPMNET_ID, LoginConstant.DEFAULT_VALUE);
            loginParam.setExtInfo(map);
            loginResult = userPassportExportService.login(loginParam);
            log.info("Login result: {} parameters :{}", JSONObject.toJSONString(loginResult), JSONObject.toJSONString(loginParam));
            if (null == loginResult) {
                log.error("Login result is null. Failed login attempt for parameters: {}", JSONObject.toJSONString(loginParam));
                throw new BizI18nException(PASSPORT_SERVICE_EXCEPTION, PASSPORT_SERVICE_EXCEPTION);
            }
            return loginResult;
        } catch (Exception e) {
            log.error("Exception occurred during login process. loginParam: {}, loginResult: {}, exception: {}",
                    JSONObject.toJSONString(loginParam),
                    JSONObject.toJSONString(loginResult),
                    e.getMessage(),
                    e);
            throw new BizI18nException(PASSPORT_SERVICE_EXCEPTION, PASSPORT_SERVICE_EXCEPTION);
        }
    }

    /**
     * 用户退出登录
     *
     * @param loginPageParam 登录页面参数对象，包含必要的退出登录信息
     * @return 退出登录结果对象
     */
    @Override
    public LogoutResult loginOut(LoginPageParam loginPageParam) {
        return userPassportExportService.logout(loginPageParam);
    }

    @Override
    public Boolean verifyVt(String sessionId, String vt, String ua, String realIp) {
        String result = verifyToken("1000813", "slcywdtf58l7q0ljir9qe0ybrhwlf7x8", vt, sessionId, realIp, ua);
        log.info("获取信息:{}", result);
        if (StringUtils.isNotBlank(result)) {
            JSONObject json = JSONObject.parseObject(result);
            if (json.getInteger("code") == 0) {
                return true;
            }
        }
        throw new BusinessException("验证登陆异常");
    }

    @Override
    public String getSessionId()  {
        String result;
        try {
            result = getSessionId("1000813", "slcywdtf58l7q0ljir9qe0ybrhwlf7x8", "pc", System.currentTimeMillis() + "", "dzHdg!axOg537gYr3zf&dSrvm@t4a+8F");
        } catch (IOException e) {
            throw new BusinessException("调用验证码服务异常");
        }
        log.info("获取信息:{}", result);
        if (StringUtils.isNotBlank(result)) {
            JSONObject json = JSONObject.parseObject(result);
            if (json.getInteger("code") == 0) {
                String sessionId = json.getString("sessionid");
                if (StringUtils.isNotBlank(sessionId)) {
                    return sessionId;
                }
            }
        }
        throw new BusinessException("获取验证码异常");
    }


    public String verifyToken(String appid, String secret, String verifyToken, String sessionid, String ip, String ua) {
        String url =  sessionUrl()+"/cgi-bin/api/verify";
        String ts = String.valueOf(System.currentTimeMillis());
        String version = "1";
        String salt = "erfhg!axOg698gYr3zf&a#dem@t4a+8E";
        String sign = generateSign(appid, secret, ts, salt);
        List<BasicNameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("appid", appid));
        params.add(new BasicNameValuePair("verify_token", verifyToken));
        params.add(new BasicNameValuePair("sessionid", sessionid));
        params.add(new BasicNameValuePair("ts", ts));
        params.add(new BasicNameValuePair("sign", sign));
        params.add(new BasicNameValuePair("version", version));
        params.add(new BasicNameValuePair("ip", ip));
        params.add(new BasicNameValuePair("ua", ua));
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-type", "application/x-www-form-urlencoded");
        post.setEntity(new UrlEncodedFormEntity(params, Consts.UTF_8));
        try (CloseableHttpClient clientHttp = HttpClients.createDefault();
             CloseableHttpResponse response = clientHttp.execute(post)) {
            return EntityUtils.toString(response.getEntity());
        } catch (ClientProtocolException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    public String getSessionId(String appid, String secret, String client, String ip, String ua) throws IOException {
        String url = sessionUrl()+"/cgi-bin/api/getsessionid";
        String ts = String.valueOf(System.currentTimeMillis());
        String version = "1";
        String salt = "dzHdg!axOg537gYr3zf&dSrvm@t4a+8F";
        String sign = generateSign(appid, secret, ts, salt);
        List<BasicNameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("appid", appid));
        params.add(new BasicNameValuePair("client", client));
        params.add(new BasicNameValuePair("ts", ts));
        params.add(new BasicNameValuePair("sign", sign));
        params.add(new BasicNameValuePair("version", version));
        params.add(new BasicNameValuePair("ip", ip));
        params.add(new BasicNameValuePair("ua", ua));

        HttpPost post = new HttpPost(url);
        post.setHeader("Content-type", "application/x-www-form-urlencoded");
        post.setEntity(new UrlEncodedFormEntity(params, Consts.UTF_8));

        try (CloseableHttpClient clientHttp = HttpClients.createDefault();
             CloseableHttpResponse response = clientHttp.execute(post)) {
            String result = EntityUtils.toString(response.getEntity());
            return result;
        }
    }


    public String generateSign(String appid, String secret, String ts, String salt) {
        String toSign = "appid=" + appid + "&secret=" + secret + "&ts=" + ts + salt;
        return DigestUtils.md5Hex(toSign).toLowerCase();
    }


    public String sessionUrl() {
        if ("dev".equals(env)) {
            return "http://jcap-m.jdtest.local";
        } else if ("test".equals(env)) {
            return "http://jcap-m.jdtest.local";
        } else if ("prod".equals(env)) {
            return "http://jcap.m.jd.local";
        } else {
            return "http://beta-jcap.m.jd.com";
        }
    }

}
