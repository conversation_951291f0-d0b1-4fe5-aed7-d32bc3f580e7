package com.jd.international.service.cart;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.SyncReadListener;
import com.jd.common.util.StringUtils;
import com.jd.fastjson.JSON;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.bean.SimpleResult;
import com.jd.international.common.config.InternationalConfig;
import com.jd.international.common.constants.CommonConstant;
import com.jd.international.common.enums.file.FileTypeEnum;
import com.jd.international.common.utils.S3Utils;
import com.jd.international.domain.cart.batch.*;
import com.jd.international.rpc.isc.mku.RpcIscMkuService;
import com.jd.international.rpc.material.MkuMaterialRpcService;
import com.jd.international.service.isc.utils.ReqBaseInfoUtil;
import com.jd.international.soa.sdk.common.cart.req.AddCartWaresReq;
import com.jd.international.soa.sdk.common.cart.req.BatchCartReq;
import com.jd.international.soa.sdk.common.cart.resp.CartResp;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientDetailReqDTO;
import com.jdi.isc.library.common.constants.i18n.I18nKeyConstant;
import com.jdi.isc.product.soa.api.material.res.MkuMaterialApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: chengliwei7
 * @Date: 2024/9/6 13:49
 */
@Slf4j
@Service
public class BatchCartServiceImpl implements BatchCartService {

    @Resource
    private MkuMaterialRpcService mkuMaterialRpcService;
    @Resource
    private RpcIscMkuService rpcIscMkuService;
    @Resource
    private CartService cartService;
    @Resource
    private S3Utils s3Utils;
    @Resource
    private InternationalConfig internationalConfig;

    private static final String DOT = ".";

    @Override
    public SimpleResult<String> template(String lang) {
        InputStream inputStream = BatchCartServiceImpl.class.getClassLoader().getResourceAsStream(String.format("excel/batch_cart_template_%s.xlsx", lang));
        if (inputStream == null) {
            return SimpleResult.errorI18n("该模板不存在", I18nKeyConstant.TEMPLATE_NOT_FOUND);
        }

        String name = internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.BULK_ADD_TO_CART_TEMPLATE );
        String filePath = s3Utils.upload(inputStream, FileTypeEnum.FILE.getCode(), name + DOT + CommonConstant.XLSX);
        log.info("BatchCartServiceImpl.template, filePath:{}", filePath);
        if (StringUtils.isBlank(filePath)) {
            return SimpleResult.errorI18n("模板下载失败", I18nKeyConstant.TEMPLATE_DOWNLOAD_FAIL);
        }
        return SimpleResult.ok(filePath);
    }

    @Override
    public SimpleResult parseExcel(MultipartFile file, BatchCartParseReq req, BaseInfo baseInfo) {
        // 文件后缀校验
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.endsWith(".xlsx")) {
            return SimpleResult.errorI18n("2002", "无效文件", I18nKeyConstant.INVALID_FILE);
        }
        // 文件size校验
        long excelFileSize = file.getSize();
        if (excelFileSize > CommonConstant.MAX_UPLOAD_FILE_SIZE) {
            return SimpleResult.errorI18n("2003", "文件过大", I18nKeyConstant.FILE_TOO_LARGE);
        }
        // 读取excel文件
        List<MatchMkuInputDTO> excelDataList = null;
        try {
            InputStream inputStream = file.getInputStream();
            excelDataList = parseExcel(inputStream);
            if (CollectionUtils.isEmpty(excelDataList)) {
                return SimpleResult.errorI18n("2002", "无效文件", I18nKeyConstant.INVALID_FILE);
            }
        } catch (Exception e) {
            log.error("BatchCartServiceImpl.parseExcel, error: ", e);
            return SimpleResult.errorI18n("2001", "未知错误", I18nKeyConstant.UNKNOWN_ERROR);
        }
        if (excelDataList.size() > 100) {
            String i18nVal = internationalConfig.queryLanValOrDefault(baseInfo.getEnv(), I18nKeyConstant.DOCUMENT_REQUIREMENT_EXCEEDS_TOTAL_REQUIREMENT);
            return SimpleResult.error("2004", String.format(i18nVal, 100));
        }
        excelDataList.forEach(matchMkuInputDTO -> {
            if (matchMkuInputDTO.getNum() == null) {
                matchMkuInputDTO.setNum(1);
            }
        });

        List<String> frontFailItems = req.getFailItems();
        List<AddCartWaresReq> successItems = req.getSuccessItems();
        int frontSuccessSize = CollectionUtils.isEmpty(successItems) ? 0 : successItems.size();

        MatchMkuDTO matchMkuDTO = matchMku(excelDataList, baseInfo, successItems);

        // 匹配成功的，去请求详情
        BatchCartParseResp resp = batchCartDetailInfo(baseInfo, matchMkuDTO, frontFailItems, frontSuccessSize);
        return SimpleResult.ok(resp);
    }

    @NotNull
    private BatchCartParseResp batchCartDetailInfo(BaseInfo baseInfo, MatchMkuDTO matchMkuDTO, List<String> frontFailItems, int frontSuccessSize) {
        log.info("BatchCartServiceImpl.batchCartDetailInfo, matchMkuDTO:{}", JSON.toJSONString(matchMkuDTO));
        Map<Long, MatchMkuInputDTO> successes = matchMkuDTO.getSuccesses();
        List<AddCartWaresReq> addCartWaresReqs = successes.entrySet().stream()
                .map(entry -> {
                    AddCartWaresReq addCartWaresReq = new AddCartWaresReq();
                    addCartWaresReq.setSku(String.valueOf(entry.getKey()));
                    addCartWaresReq.setNum(entry.getValue().getNum());
                    return addCartWaresReq;
                }).collect(Collectors.toList());

        BatchCartReq batchCartReq = new BatchCartReq();
        ReqBaseInfoUtil.copyBaseInfo(baseInfo, batchCartReq);
        batchCartReq.setSkus(addCartWaresReqs);
        batchCartReq.setClientCode(baseInfo.getClientId());

        BatchCartParseResp resp = new BatchCartParseResp();
        CartResp batchCartList = cartService.getBatchCartList(batchCartReq);
        if (batchCartList == null) {
            log.info("getBatchCartList return null, req: {}", JSON.toJSONString(batchCartReq));
        }
        resp.setCartResp(batchCartList);
        // 失败串构建
        List<MatchMkuInputDTO> fails = matchMkuDTO.getFails();
        List<String> failRecordStrList = fails.stream().map(MatchMkuInputDTO::showString).collect(Collectors.toList());
        // 成功串构建
        Map<Long, String> successItems = successes.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> {
                            MatchMkuInputDTO inputDTO = entry.getValue();
                            return inputDTO.showString();
                        }));

        if (frontFailItems != null) {
            failRecordStrList.addAll(frontFailItems);
        }
        List<MatchMkuInputDTO> exists = matchMkuDTO.getExists();
        if (CollectionUtils.isNotEmpty(exists)) {
            resp.setExistsCount(exists.size());
        }
        resp.setTotalCount(matchMkuDTO.getTotal());
        resp.setSuccessItems(successItems);
        resp.setFailItems(failRecordStrList);
        resp.setFailedCount(fails.size());
        resp.setSuccessCount(successes.size() - frontSuccessSize);
        log.info("BatchCartServiceImpl.batchCartDetailInfo, resp:{}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public SimpleResult smartMatch(BatchSmartMatchReq req, BaseInfo baseInfo) {
        String input = req.getInput();
        log.info("BatchCartServiceImpl.smartMatch, baseInfo:{}, req:{}", JSON.toJSONString(baseInfo), JSON.toJSONString(req));
        if (StringUtils.isBlank(input)) {
            return SimpleResult.errorI18n("2006", "商品列表不能为空", I18nKeyConstant.PRODUCT_LIST_CANNOT_BE_EMPTY);
        }
        String[] inputLines = input.split("\\n");
        List<MatchMkuInputDTO> inputDTOS = Arrays.stream(inputLines).map(l -> {
            String[] arr = l.split(",");
            Long mkuId = null;
            String materialCode = null;
            try {
                mkuId = Long.valueOf(arr[0]);
            } catch (Exception e) {
                // ignore
                // 解析不了的，就是不合法的mkuId，直接跳过,当做物料编码去处理
                materialCode = arr[0];
            }
            Integer num = 0;
            if (arr.length == 1) {
                num = 1;
            }
            if (arr.length > 1) {
                String numTmp = arr[1];
                num = StringUtils.isEmpty(numTmp) ? 1 : Integer.valueOf(arr[1]);
            }
            return MatchMkuInputDTO.builder()
                    .mkuId(mkuId)
                    .materialCode(materialCode)
                    .num(num)
                    .build();
        }).filter(Objects::nonNull).collect(Collectors.toList());

        inputDTOS.forEach(matchMkuInputDTO -> {
            if (matchMkuInputDTO.getNum() == null) {
                matchMkuInputDTO.setNum(1);
            }
        });

        List<String> frontFailItems = req.getFailItems();
        List<AddCartWaresReq> successItems = req.getSuccessItems();
        int frontSuccessSize = CollectionUtils.isEmpty(successItems) ? 0 : successItems.size();

        log.info("smartMatch, successItems:{}, failItems:{}", JSON.toJSONString(successItems), JSON.toJSONString(frontFailItems));

        MatchMkuDTO matchMkuDTO = matchMku(inputDTOS, baseInfo, successItems);
        BatchCartParseResp resp = batchCartDetailInfo(baseInfo, matchMkuDTO, frontFailItems, frontSuccessSize);
        log.info("BatchCartServiceImpl.smartMatch, resp:{}", JSON.toJSONString(resp));
        return SimpleResult.ok(resp);
    }

    @Override
    public SimpleResult checkedItemsPrice(List<AddCartWaresReq> items) {
        if (CollectionUtils.isEmpty(items)) {
            return SimpleResult.ok(BigDecimal.ZERO);
        }
        Optional<BigDecimal> sum = items.stream().map(item -> {
            Integer num = item.getNum();
            String price = item.getPrice();
            return new BigDecimal(price).multiply(new BigDecimal(num));
        }).reduce(BigDecimal::add);
        if (sum.isPresent()) {
            return SimpleResult.ok(sum);
        }
        return SimpleResult.errorI18n("计算失败", I18nKeyConstant.CALCULATION_FAILED);
    }

    private MatchMkuDTO matchMku(List<MatchMkuInputDTO> matchMkuInputDTOList, BaseInfo baseInfo, List<AddCartWaresReq> successItems) {
        log.info("BatchCartServiceImpl.matchMku, req:{}, baseInfo:{}", JSON.toJSONString(matchMkuInputDTOList), JSON.toJSONString(baseInfo));
        log.info("BatchCartServiceImpl.matchMku, successItems: {}", JSON.toJSONString(successItems));
        int excelTotal = matchMkuInputDTOList.size();
        Map<Long, IscMkuClientDTO> mkus = new HashMap<>();
        Map<Long, MatchMkuInputDTO> successes = new HashMap<>();
        List<MatchMkuInputDTO> fails = new ArrayList<>();
        List<MatchMkuInputDTO> exists = new ArrayList<>();
        Set<Long> mkuSet = successItems == null ? new HashSet<>() : successItems.stream().map(AddCartWaresReq::getSku).map(Long::valueOf).collect(Collectors.toSet());

        for (MatchMkuInputDTO dto : matchMkuInputDTOList) {
            Long mkuId = dto.getMkuId();
            if (dto.getNum() == null) {
                dto.setNum(1);
            }
            if (mkuSet.contains(mkuId)) {
                exists.add(dto);
                continue;
            }
            Boolean existsMku = existsMku(mkuId, baseInfo);
            log.info("BatchCartServiceImpl.matchMku, existsMku:{}, dto:{}", existsMku, JSON.toJSONString(dto));
            // 通过mkuId找不到，就通过物料编码找
            if (!existsMku) {
                String materialCode = dto.getMaterialCode();
                Map<String, MkuMaterialApiDTO> materialCodeMap = mkuMaterialRpcService.queryMkuMaterialVOsByMaterialIds(new HashSet<>(Arrays.asList(materialCode)), baseInfo.getClientId());
                MkuMaterialApiDTO mkuMaterialApiDTO = materialCodeMap.get(materialCode);
                if (mkuMaterialApiDTO == null) {
                    // 通过物料编码找不到，标记为失败
                    fails.add(dto);
                    continue;
                }
                Long mkuIdFromMaterial = mkuMaterialApiDTO.getMkuId();
                if (mkuSet.contains(mkuIdFromMaterial)) {
                    exists.add(dto);
                    continue;
                }
                Boolean existsFromMaterial = existsMku(mkuIdFromMaterial, baseInfo);
                if (!existsFromMaterial) {
                    // 通过物料编码找到mkuId，但是mku不存在，标记为失败
                    fails.add(dto);
                } else {
                    // 通过物料编码能找到的，标记成功
                    tagSuccess(mkuIdFromMaterial, mkus, baseInfo);
                    successes.put(mkuIdFromMaterial, dto);
                }
                continue;
            }
            // 通过mkuId找到的，直接标记成功
            tagSuccess(mkuId, mkus, baseInfo);
            successes.put(mkuId, dto);
        }

        if (successItems != null) {
            for (AddCartWaresReq item : successItems) {
                String sku = item.getSku();
                Long skuId = Long.valueOf(sku);
                if (successes.containsKey(skuId)) {
                    continue;
                }
                successes.put(skuId, MatchMkuInputDTO.builder()
                        .mkuId(skuId)
                        .num(item.getNum())
                        .build());
            }
        }

        MatchMkuDTO result = MatchMkuDTO.builder()
                .matchMkuMap(mkus)
                .fails(fails)
                .successes(successes)
                .total(excelTotal)
                .exists(exists)
                .build();
        log.info("BatchCartServiceImpl.matchMku, resp:{}", JSON.toJSONString(result));
        return result;
    }

    private void tagSuccess(Long mkuIdFromMaterial, Map<Long, IscMkuClientDTO> mkus, BaseInfo baseInfo) {
        // 此方法的查询结果没用到，先屏蔽掉
//        IscMkuClientDTO iscMkuClientDTO = mkuClientDTO(mkuIdFromMaterial);
//        if (iscMkuClientDTO != null) {
//            mkus.put(mkuIdFromMaterial, iscMkuClientDTO);
//        }
    }

    private Boolean existsMku(Long mkuId, BaseInfo baseInfo) {
        if (mkuId == null) {
            return false;
        }
        IscMkuClientDetailReqDTO mkuClientDetailReqDTO = new IscMkuClientDetailReqDTO();
        mkuClientDetailReqDTO.setMkuId(mkuId);
        mkuClientDetailReqDTO.setClientCode(baseInfo.getClientId());
        mkuClientDetailReqDTO.setStationType(baseInfo.getStationType());
        mkuClientDetailReqDTO.setLang(baseInfo.getEnv());
        mkuClientDetailReqDTO.setPin(baseInfo.getPin());
        return rpcIscMkuService.existsValidMku(mkuClientDetailReqDTO);
    }

    private List<MatchMkuInputDTO> parseExcel(InputStream inputStream) {
        if (inputStream == null) {
            return Collections.emptyList();
        }
        List<MatchMkuInputDTO> excelData = EasyExcel.read(inputStream, MatchMkuInputDTO.class, new SyncReadListener())
                .headRowNumber(2)
                .sheet()
                .doReadSync();
        log.info("parseExcel, parsed data size: {}", CollectionUtils.isEmpty(excelData) ? 0 : excelData.size());
        return excelData;
    }
}
