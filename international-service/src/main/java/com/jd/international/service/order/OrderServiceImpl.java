package com.jd.international.service.order;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.config.CommonDUCCConfig;
import com.jd.international.common.config.InternationalConfig;
import com.jd.international.common.config.OrderDUCCConfig;
import com.jd.international.common.constants.CommonConstant;
import com.jd.international.common.enums.TradeModelEnum;
import com.jd.international.common.enums.address.AddressManagerAuthorityEnum;
import com.jd.international.common.exception.BizException;
import com.jd.international.common.exception.ResponseErrorCode;
import com.jd.international.domain.address.AddressManagerDTO;
import com.jd.international.domain.approval.multi.MultiApprovalSelectDTO;
import com.jd.international.domain.enums.OrderTypeEnum;
import com.jd.international.domain.enums.StationTypeEnum;
import com.jd.international.domain.order.*;
import com.jd.international.domain.settlement.SettlementDTO;
import com.jd.international.rpc.address.RpcAddressManagerService;
import com.jd.international.rpc.cart.RpcCartService;
import com.jd.international.rpc.mail.RpcMailService;
import com.jd.international.rpc.trade.RpcTradeService;
import com.jd.international.service.adapter.isc.OrderConvert;
import com.jd.international.service.adapter.isc.approval.ProcessNodeConvert;
import com.jd.international.service.address.AddressManagerAuthorityService;
import com.jd.international.service.approval.MultiMoreConditionApprovalService;
import com.jd.international.service.approval.OrderApprovalService;
import com.jd.international.service.cache.JimCacheService;
import com.jd.international.service.isc.freight.IscFreightService;
import com.jd.international.service.isc.utils.ReqBaseInfoUtil;
import com.jd.international.service.orderList.OrderListService;
import com.jd.international.service.punchout.customize.CustomizedOrderCallbackService;
import com.jd.international.service.settleconfig.SettleConfigService;
import com.jd.international.soa.sdk.common.cart.req.AddCartReq;
import com.jd.international.soa.sdk.common.cart.req.AddCartWaresReq;
import com.jd.international.soa.sdk.common.cart.req.CartReq;
import com.jd.international.soa.sdk.common.cart.resp.CartResp;
import com.jd.international.soa.sdk.common.cart.resp.CartWaresResp;
import com.jd.international.soa.sdk.common.mail.req.SendMailReq;
import com.jd.international.soa.sdk.common.sale.SkuSaleAttrDTO;
import com.jd.international.soa.sdk.order.order.req.SubmitOrderReq;
import com.jd.international.soa.sdk.order.order.req.SubmitOrderWaresReq;
import com.jd.international.soa.sdk.order.orderList.res.OrderRes;
import com.jd.ka.mro.workflow.soa.sdk.dto.multi.MultiApprovalSelect;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.wisp.api.area.AreaApiService;
import com.jdi.isc.aggregate.read.wisp.api.area.biz.req.AreaInfoReadReq;
import com.jdi.isc.aggregate.read.wisp.api.area.biz.res.AreaInfoReadResp;
import com.jdi.isc.library.common.constants.i18n.I18nKeyConstant;
import com.jdi.isc.library.common.response.ResponseCode;
import com.jdi.isc.library.i18n.exception.BusinessException;
import com.jdi.isc.order.center.api.OrderWriteManageApiService;
import com.jdi.isc.order.center.api.biz.req.UpdateOrderThirdIdReq;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.material.IscMkuMaterialReadApiService;
import com.jdi.isc.product.soa.api.material.res.MkuMaterialApiDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderServiceImpl implements OrderService {
    private final RpcAddressManagerService rpcAddressManagerService;
    private final RpcTradeService tradeService;
    private final RpcCartService rpcCartService;
    private final JimCacheService jimCacheService;
    private final RpcMailService rpcMailService;
    private final CommonDUCCConfig commonDUCCConfig;
    private final OrderApprovalService orderApprovalService;
    private final AddressManagerAuthorityService addressManagerAuthorityService;
    private final InternationalConfig internationalConfig;
    private final OrderListService orderListService;
    private final OrderDUCCConfig orderDUCCConfig;
    private final Map<String, CustomizedOrderCallbackService> customizedOrderCallbackServiceMap;
    // 可售
    private final static String VENDIBILITY = "1";
    private final static String SUBMIT_CACHE_KEY = "international:submit:%s:%s";

    @Value("${user.bind.mail.mailType}")
    private int bindMailType;

    @Autowired
    private IscFreightService iscFreightService;

    private final MultiMoreConditionApprovalService multiMoreConditionApprovalService;

    private final SettleConfigService settleConfigService;

    @Autowired
    private Map<String, OrderSettlementPageHandle> orderSettlementPageHandleMap;

    @Resource
    private IscMkuMaterialReadApiService iscMkuMaterialReadApiService;

    @Resource
    private AreaApiService areaApiService;
    @Resource
    private  OrderWriteManageApiService orderWriteManageApiService;

    @Override
    public void ispBuyNow(OrderBuyNowRequest orderBuyNowRequest, BaseInfo baseInfo) {
        log.info("ispBuyNow orderBuyNowRequest{}", JSON.toJSONString(orderBuyNowRequest));
        String sku = orderBuyNowRequest.getSku();
        Integer num = orderBuyNowRequest.getNum();
        if (StringUtils.isBlank(sku) || num == null || num < 1) {
            throw new BizException(ResponseCode.COMMON_PARAM_NOT_ALLOW_EMPTY, sku);
        }
//        Map<String, Map<String, String>> skuSaleState = rpcProductQueryService.querySkuSaleState(baseInfo.getPin(), Collections.singletonList(sku));
//        if(MapUtil.isEmpty(skuSaleState.get(sku))){
//            throw new BizException(ResponseCode.MERCHANDISE_NOT_AVAILABLE_FOR_SALE,sku);
//        }
//        Map<String, String> saleState = skuSaleState.get(sku);
//        if(!VENDIBILITY.equals(String.valueOf(saleState.get("saleState")))){
//            log.error(sku+"不可售，不可售原因："+ JSON.toJSONString(saleState));
//            throw new BizException(ResponseCode.MERCHANDISE_NOT_AVAILABLE_FOR_SALE.getCode(),ResponseCode.MERCHANDISE_NOT_AVAILABLE_FOR_SALE.getMessage(),sku);
//        }
        AddCartWaresReq wares = new AddCartWaresReq();
        wares.setSku(sku);
        wares.setNum(num);
        wares.setPrice(orderBuyNowRequest.getSkuPrice());
        AddCartReq addCartReq = new AddCartReq();
        String clientStaffId = baseInfo.getSubAccount();
        String suffix = clientStaffId == null ? "" : "_" + clientStaffId;
        addCartReq.setCode(TradeModelEnum.IMMEDIATELY_MODEL.getKey() + baseInfo.getPin() + suffix);
        addCartReq.setPin(baseInfo.getPin());
        addCartReq.setWares(Collections.singletonList(wares));
        log.info("ispBuyNow replaceCart{}", JSON.toJSONString(addCartReq));
        rpcCartService.replaceCart(addCartReq);
    }

    @Override
    public void ispBatchBuyNow(List<OrderBuyNowRequest> orderBuyNowRequest, BaseInfo baseInfo) {
        if (CollectionUtils.isEmpty(orderBuyNowRequest)) {
            throw new BizException(ResponseCode.COMMON_PARAM_NOT_ALLOW_EMPTY);
        }

        List<AddCartWaresReq> waresReqs = new ArrayList<>();
        for (OrderBuyNowRequest req : orderBuyNowRequest) {
            AddCartWaresReq ware = new AddCartWaresReq();
            String sku = req.getSku();
            Integer num = req.getNum();
            ware.setSku(sku);
            ware.setNum(num);
            ware.setPrice(req.getSkuPrice());
            waresReqs.add(ware);
        }

        AddCartReq addCartReq = new AddCartReq();
        addCartReq.setCode(TradeModelEnum.IMMEDIATELY_MODEL.getKey() + baseInfo.getPin());
        addCartReq.setPin(baseInfo.getPin());
        addCartReq.setWares(waresReqs);
        log.info("ispBatchBuyNow replaceCart{}", JSON.toJSONString(addCartReq));
        rpcCartService.replaceCart(addCartReq);
    }

/*    @Override
    public CartResp settlementPage(OrderSettlementRequest orderSettlementRequest, BaseInfo baseInfo) {
        String tradeModel = orderSettlementRequest.getTradeModel();
        CartResp cart = getCart(tradeModel, baseInfo);
        if (null!=commonDUCCConfig && Boolean.TRUE.equals(commonDUCCConfig.getLogDebugSwitch())){
            log.info("settlementPage, CartResp={}, orderSettlementRequest={}", JSONObject.toJSONString(cart), JSONObject.toJSONString(orderSettlementRequest));
        }
        jimCacheService.setStr(String.format(SUBMIT_CACHE_KEY,baseInfo.getPin(),orderSettlementRequest.getTradeModel()),baseInfo.getPin());
        return cart;
    }*/

    @Override
    public SettlementDTO settlementPage(OrderSettlementRequest orderSettlementRequest, BaseInfo baseInfo) {
        SettlementDTO settlementDTO = new SettlementDTO();
        boolean addressAuth = addressManagerAuthorityService.hasAuthority(baseInfo.getPin());
        settlementDTO.setAddressAuthority(AddressManagerAuthorityEnum.booleanToCode(addressAuth));

        // 购物车
        final String tradeModel = orderSettlementRequest.getTradeModel();
        CartResp cart = getCartWithValidWares(tradeModel, baseInfo);

        settlementDTO.setCartResp(cart);

        // 查询显示的配置列表
//        WispConfigurationFunction configurationFunction = new WispConfigurationFunction();
//        configurationFunction.setExt1(CommonConstant.VALID);
//        configurationFunction.setContractNum(baseInfo.getContractNum());
//        List<WispConfigurationFunction> functionList = settleConfigService.queryConfigurationFunctionListByCondition(configurationFunction, baseInfo);
//        settlementDTO.setSettleFunctionList(functionList);

        // 组装楼层
        orderSettlementPageHandleMap.entrySet().forEach(entry -> {
            entry.getValue().process(settlementDTO, baseInfo);
        });

        // 日志开关
        if (null != commonDUCCConfig && Boolean.TRUE.equals(commonDUCCConfig.getLogDebugSwitch())) {
            log.info("settlementPage, settlementDTO={}, orderSettlementRequest={}", JSONObject.toJSONString(settlementDTO), JSONObject.toJSONString(orderSettlementRequest));
        }

        // 下单防抖
        jimCacheService.setStr(String.format(SUBMIT_CACHE_KEY, baseInfo.getPin(), orderSettlementRequest.getTradeModel()), baseInfo.getPin());
        return settlementDTO;
    }

    /**
     * 获取购物车中有效的商品
     */
    private CartResp getCartWithValidWares(String tradeModel, BaseInfo baseInfo) {
        CartResp cart = getCart(tradeModel, baseInfo);
        List<CartWaresResp> validWares = cart.getCartWares().stream().filter(line -> line.getInPool()
                && (StationTypeEnum.CROSS_BORDER.equals(baseInfo.getStationType()) ? line.getRemainNum() != null && line.getRemainNum() != 0 : Boolean.TRUE)).collect(Collectors.toList());
        cart.setCartWares(validWares);
        return cart;
    }

    @Override
    public List<MultiApprovalSelectDTO> approvalList(OrderApprovalListReq orderApprovalListReq, BaseInfo baseInfo) {
        if (null == orderApprovalListReq) {
            log.info("approvalList, param empty. baseInfo={}", JSONObject.toJSONString(baseInfo));
            return new ArrayList<>();
        }
        // 查询当前用户下的所有审批流程
        List<MultiApprovalSelect> multiApprovalSelectList = multiMoreConditionApprovalService.queryProcessSelectByPin(baseInfo.getPin());
        if (CollectionUtils.isEmpty(multiApprovalSelectList)) {
            log.info("approvalList, List<MultiApprovalSelect> is empty. orderApprovalListReq={}, baseInfo={}",
                    JSONObject.toJSONString(orderApprovalListReq), JSONObject.toJSONString(baseInfo));
            return new ArrayList<>();
        }
        if (null != commonDUCCConfig && Boolean.TRUE.equals(commonDUCCConfig.getLogDebugSwitch())) {
            log.info("approvalList, List<MultiApprovalSelect>={}. orderApprovalListReq={}, baseInfo={}",
                    JSONObject.toJSONString(multiApprovalSelectList), JSONObject.toJSONString(orderApprovalListReq), JSONObject.toJSONString(baseInfo));
        }

        // 构造参数
        Map<String, Object> orderExtInfoMap = new HashMap<>();
        // 转成String存入map
        orderExtInfoMap.put(CommonConstant.SETTLE_INFO, JSONObject.toJSONString(orderApprovalListReq.getCondition()));
        Map<String, Object> workParam = orderApprovalService.startOrderApprovalWorkParam(baseInfo, orderExtInfoMap, orderApprovalListReq.getAmount());
        if (null != commonDUCCConfig && Boolean.TRUE.equals(commonDUCCConfig.getLogDebugSwitch())) {
            log.info("approvalList, workParam. baseInfo={}",
                    JSONObject.toJSONString(workParam), JSONObject.toJSONString(baseInfo));
        }

        // 过滤能匹配条件的审批流
        List<MultiApprovalSelect> result = multiApprovalSelectList.stream().filter(o -> {
            boolean isNeed = orderApprovalService.orderIsNeedApproval(baseInfo, workParam, o.getProcessKey(), null);
            if (null != commonDUCCConfig && Boolean.TRUE.equals(commonDUCCConfig.getLogDebugSwitch())) {
                log.info("approvalList, isNeed={}, MultiApprovalSelect={}, baseInfo={}",
                        isNeed, JSONObject.toJSONString(o), JSONObject.toJSONString(baseInfo));
            }
            return isNeed;
        }).collect(Collectors.toList());

        return ProcessNodeConvert.INSTANCE.listApproveVo2DTO(result);
    }

    @Override
    public String submit(SubmitOrderInfo submitOrderInfo, BaseInfo baseInfo) {
        log.info("OrderServiceImpl.submit 入参:[submitOrderInfo={},baseInfo={}]", JSON.toJSONString(submitOrderInfo), JSON.toJSONString(baseInfo));
        Long receiverId = submitOrderInfo.getReceiverId();
        if (receiverId == null) {
            throw new BizException(ResponseErrorCode.SHIP_ADDRESS_NULL);
        }
        // 获取地址
        AddressManagerDTO addressManagerDTO = rpcAddressManagerService.queryAddressDetailByIdAndPin(receiverId, baseInfo.getPin());
        if (addressManagerDTO == null) {
            throw new BizException(ResponseErrorCode.ABNORMAL_RECEIVING_ADDRESS);
        }
        fillCountryCode(addressManagerDTO);
        String tradeModel = submitOrderInfo.getTradeModel();
        CartResp cart = getCartWithValidWares(tradeModel, baseInfo);
        List<CartWaresResp> cartWares = cart.getCartWares();
        if (CollectionUtils.isEmpty(cartWares)) {
            throw new BizException(ResponseErrorCode.SETTLEMENT_WARES_ERROR);
        }
        boolean cad = jimCacheService.cad(String.format(SUBMIT_CACHE_KEY, baseInfo.getPin(), submitOrderInfo.getTradeModel()), baseInfo.getPin());
        if (!cad) {
            throw new BizException(ResponseErrorCode.REPEAT_ORDER);
        }
        List<SubmitOrderWaresReq> subWares = new ArrayList<>();
        BigDecimal exchangeRate = BigDecimal.ONE;
        BigDecimal waresTotalPrice = BigDecimal.ZERO;

        List<OrderWareInfoDTO> orderWareInfoDTOList = submitOrderInfo.getMkuList();
        Map<String, OrderWareInfoDTO> orderWareInfoDTOMap = CollectionUtils.isNotEmpty(orderWareInfoDTOList) ? orderWareInfoDTOList.stream().collect(Collectors.toMap(OrderWareInfoDTO::getSku, Function.identity())) : new HashMap<>();
        for (CartWaresResp wares : cartWares) {
            SubmitOrderWaresReq submitOrderWaresReq = buildSubmitOrderWaresReq(wares);
            exchangeRate = wares.getExchangeRate();
            // waresTotalPrice = waresTotalPrice.add(wares.getOriginalPrice().multiply(new BigDecimal(wares.getSkuNum())));
            waresTotalPrice = waresTotalPrice.add(wares.getPrice().multiply(new BigDecimal(wares.getSkuNum())));
            OrderWareInfoDTO orderWareInfoDTO = orderWareInfoDTOMap.get(submitOrderWaresReq.getSku());
            submitOrderWaresReq.setCustomsClearance(orderWareInfoDTO != null && orderWareInfoDTO.isCustomsDeclaration() ? 1 : 0);
            submitOrderWaresReq.setScheduledDeliveryTime(orderWareInfoDTO == null ? null : orderWareInfoDTO.getScheduledDeliveryTime());
            subWares.add(submitOrderWaresReq);
            subWares.addAll(buildGifts(wares.getGifts(), wares.getSku(), wares.getSkuNum()));
        }
        SubmitOrderReq submitOrderReq = buildSubmitOrder(baseInfo, addressManagerDTO, cart, subWares, exchangeRate, waresTotalPrice, submitOrderInfo);
        log.info("OrderServiceImpl.submit 调用SOA接口 入参:[submitOrderReq={}]", JSON.toJSONString(submitOrderReq));
        String orderNo = tradeService.submit(submitOrderReq);
        log.info("OrderServiceImpl.submit 调用SOA接口 出参:[orderNo={}]", orderNo);
        // 发送邮件
        sendEmail(baseInfo, orderNo);

        // punchout模式，根据具体业务方进行订单回传
        if (commonDUCCConfig.allowOrderCallback(baseInfo.getClientId())) {
            CompletableFuture.runAsync(() -> orderCallback(orderNo, baseInfo));
        }
        return orderNo;
    }

    private void fillCountryCode(AddressManagerDTO addressManagerDTO) {
        if(StringUtils.isNotBlank(addressManagerDTO.getCountry())){
            return;
        }
        AreaInfoReadReq req = new AreaInfoReadReq();
        req.setLang("zh");
        Set<Long> areas = new HashSet<>();
        areas.add(addressManagerDTO.getCountryId());
        req.setAreaIds(areas);
        DataResponse<Map<Long, AreaInfoReadResp>> info = areaApiService.info(req);
        if(info.getSuccess()){
            Map<Long, AreaInfoReadResp> data = info.getData();
            if(data!=null && data.size()>0){
                AreaInfoReadResp areaInfoReadResp = data.get(addressManagerDTO.getCountryId());
                if(areaInfoReadResp!=null){
                    addressManagerDTO.setCountry(areaInfoReadResp.getCode());
                }
            }
        }
    }

    @Override
    public String orderCallback(String orderNo, BaseInfo baseInfo) {
        OrderRes orderRes = orderListService.queryByOrderId(orderNo, baseInfo);
        String customizedImpl = orderDUCCConfig.getPunchoutImplByContractNum(baseInfo.getContractNum());
        if (StringUtils.isBlank(customizedImpl)) {
            log.info("OrderServiceImpl.orderCallback 未找到对应的回调实现类配置, orderNo={}, baseInfo={}", orderNo, baseInfo);
            return "";
        }
        CustomizedOrderCallbackService customizedOrderCallbackService = customizedOrderCallbackServiceMap.get(customizedImpl);
        if (customizedOrderCallbackService == null) {
            log.info("OrderServiceImpl.orderCallback 未找到对应的回调实现类, orderNo={}, baseInfo={}", orderNo, baseInfo);
            return "";
        }
        JSONObject jsonObject = customizedOrderCallbackService.orderData(orderRes, baseInfo);
        if (jsonObject == null) {
            log.info("OrderServiceImpl.orderCallback 回调实现类返回数据为空, orderNo={}, baseInfo={}", orderNo, baseInfo);
            return "";
        }
        String data = JSONObject.toJSONString(jsonObject);
        return customizedOrderCallbackService.sendData(data, baseInfo);
    }

    @Override
    public String orderCallbackDecode(String content, BaseInfo baseInfo) {
        String customizedImpl = orderDUCCConfig.getPunchoutImplByContractNum(baseInfo.getContractNum());
        CustomizedOrderCallbackService customizedOrderCallbackService = customizedOrderCallbackServiceMap.get(customizedImpl);
        return customizedOrderCallbackService.orderDataDecode(content, baseInfo);
    }

    @Override
    public void addPo(OrderPoDTO orderPoDTO, String pin) {
        log.info("开始更新订单第三方ID，订单信息：{}, Pin：{}", orderPoDTO, pin);

        UpdateOrderThirdIdReq thirdIdReq= OrderConvert.INSTANCE.dtoToReq(orderPoDTO);
        thirdIdReq.setPin(pin);
        try {
            DataResponse<List<Long>> result = orderWriteManageApiService.updateOrderThirdId(thirdIdReq);
            if (result == null || !result.getSuccess()) {
                log.error("调用外部服务更新订单第三方ID失败，响应信息: {}", result != null ? result.getMessage() : "未知错误");
                throw new BusinessException("调用外部服务更新订单第三方ID失败，响应信息: " + (result != null ? result.getMessage() : "未知错误"));
            }
            List<Long> updatedIds = result.getData();
            if (updatedIds == null || updatedIds.isEmpty()) {
                log.warn("订单第三方ID更新成功，但未返回有效的ID");
            } else {
                log.info("订单第三方ID更新成功，更新的ID: {}", updatedIds);
            }
        } catch (Exception e) {
            log.error("更新订单第三方ID时发生异常", e);
            throw new BusinessException("更新订单第三方ID时发生异常");
        }
    }

    private void sendEmail(BaseInfo baseInfo, String orderNo) {
        log.info("OrderServiceImpl.sendEmail -> baseInfo:{} , orderNo:{}", baseInfo, orderNo);
        List<String> operatorEmail = StringUtils.isNotBlank(commonDUCCConfig.getOperatorEmail()) ? Lists.newArrayList(commonDUCCConfig.operatorEmail.split(",")) : Lists.newArrayList();
        log.info("operatorEmail List -> {} ", operatorEmail);
        // 判断参数
        if (StringUtils.isNotBlank(orderNo) && CollectionUtils.isNotEmpty(operatorEmail)) {
            // 准备发送邮件
            for (String email : operatorEmail) {
                SendMailReq customMail = new SendMailReq();
                customMail.setEmailAddress(email);
                customMail.setPin(baseInfo.getPin());
                customMail.setType(bindMailType);
                Map<Object, Object> keyValues = new HashMap<Object, Object>(CommonConstant.HASHMAP_INITIAL_CAPACITY);
                String lang = baseInfo.getEnv();
                String i18nVal = internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.EMAIL_TITLE_ORDER_SUBMIT);
                keyValues.put("title", i18nVal);
                keyValues.put("pin", baseInfo.getPin());
                Map<String, Object> model = new HashMap<String, Object>(CommonConstant.HASHMAP_INITIAL_CAPACITY);
                model.put("pin", baseInfo.getPin());
                model.put("orderNum", orderNo);
                if (LangConstant.LANG_EN.equals(lang)) {
                    lang = LangConstant.LANG_ZH;
                }
                model.put("please_approve", internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.PLEASE_APPROVE));
                model.put("hello", internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.HELLO));
                model.put("order", internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.ORDER));
                model.put("auto_send_not_replay", internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.AUTO_SEND_NOT_REPLAY));
                model.put("industry_international", internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.INDUSTRY_INTERNATIONAL));
                model.put("JD", internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.JD));
                model.put("copyright", internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.COPYRIGHT));
                model.put("privacy", internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.PRIVACY));
                TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig("template", TemplateConfig.ResourceMode.CLASSPATH));
                Template template = engine.getTemplate("submitOrderMail.ftl");
                String htmlContent = template.render(model);
                keyValues.put("content", htmlContent);
                customMail.setKeyValues(keyValues);
                rpcMailService.sendMail(customMail);
            }
        }
    }

    private List<SubmitOrderWaresReq> buildGifts(Map<String, Map<String, String>> gifts, String sku, Integer skuNum) {
        List<SubmitOrderWaresReq> subWares = new ArrayList<>();
        if (MapUtils.isNotEmpty(gifts)) {
            gifts.forEach((k, v) -> {
                SubmitOrderWaresReq submitOrderWaresReq = new SubmitOrderWaresReq();
                submitOrderWaresReq.setSku(k);
                submitOrderWaresReq.setSkuNum(v.get("num") != null ? Integer.valueOf(v.get("num")) * skuNum : skuNum);
                submitOrderWaresReq.setSkuName(v.get("product_name"));
                submitOrderWaresReq.setSkuPrice(BigDecimal.ZERO);
                submitOrderWaresReq.setSkuImg(v.get("img_dfs_url"));
                submitOrderWaresReq.setSpecifications(v.get("unit"));
                submitOrderWaresReq.setLanguage("zh");
                submitOrderWaresReq.setSellingPrice(BigDecimal.ZERO);
                submitOrderWaresReq.setParentSku(sku);
                submitOrderWaresReq.setSkuType(Integer.valueOf(v.get("giftType")));
                subWares.add(submitOrderWaresReq);
            });
        }
        return subWares;
    }

    private CartResp getCart(String tradeModel, BaseInfo baseInfo) {
        CartReq cartReq = new CartReq();
        cartReq.setPin(baseInfo.getPin());
        cartReq.setContractNum(baseInfo.getContractNum());
        String clientStaffId = baseInfo.getSubAccount();
        String suffix = clientStaffId == null ? "" : "_" + clientStaffId;
        cartReq.setCode(TradeModelEnum.parse(tradeModel).getKey() + baseInfo.getPin() + suffix);
        cartReq.setEnv(baseInfo.getEnv());
        cartReq.setChecked(true);
        cartReq.setRequestSource(CartReq.REQUEST_SOURCE_SETTLEMENT);

        ReqBaseInfoUtil.copyBaseInfo(baseInfo, cartReq);
        log.info("OrderServiceImpl.getCart 入参:[cartReq={}]", JSON.toJSONString(cartReq));
        CartResp cartList = rpcCartService.getCartList(cartReq);
        log.info("OrderServiceImpl.getCart 出参:[cartList={}]", JSON.toJSONString(cartList));
        if (!Optional.ofNullable(cartList).isPresent()
                || CollectionUtil.isEmpty(cartList.getCartWares())) {
            throw new BizException(ResponseErrorCode.SETTLEMENT_WARES_ERROR.getCode()
                    , ResponseErrorCode.SETTLEMENT_WARES_ERROR.getMessage());
        }
        return cartList;
    }

    private SubmitOrderReq buildSubmitOrder(BaseInfo baseInfo, AddressManagerDTO addressManagerDTO, CartResp cart, List<SubmitOrderWaresReq> subWares, BigDecimal exchangeRate, BigDecimal waresTotalPrice, SubmitOrderInfo submitOrderInfo) {
        SubmitOrderReq submitOrderReq = new SubmitOrderReq();
        ReqBaseInfoUtil.copyBaseInfo(baseInfo, submitOrderReq);
        submitOrderReq.setPin(baseInfo.getPin());
        submitOrderReq.setContractNum(baseInfo.getContractNum());
        submitOrderReq.setConsigneeId(addressManagerDTO.getId());
        submitOrderReq.setConsignee(addressManagerDTO.getReceiveName());
        submitOrderReq.setConsigneeProvinceId(addressManagerDTO.getProvinceId());
        submitOrderReq.setConsigneeCityId(addressManagerDTO.getCityId());
        submitOrderReq.setConsigneeCountyId(addressManagerDTO.getCountyId());
        submitOrderReq.setConsigneeCountry(addressManagerDTO.getCountry());
        submitOrderReq.setConsigneeTownId(0L); // 默认传0
        submitOrderReq.setDeliveryAddress(addressManagerDTO.getDetailAddress());
        submitOrderReq.setDeliveryCountry(addressManagerDTO.getCountry());
        submitOrderReq.setDeliveryPhone(addressManagerDTO.getReceivePhone());
        submitOrderReq.setPostalCode(addressManagerDTO.getPostalCode());
        submitOrderReq.setOrderSkuNum(cart.getTotalCheckCount());
        submitOrderReq.setOrderSkuKindNum(cart.getTotalCheckKindCount());
        submitOrderReq.setExchangeRate(exchangeRate);
        submitOrderReq.setTotalPrice(cart.getTotalPrice());
        submitOrderReq.setWaresTotalPrice(waresTotalPrice);
        submitOrderReq.setWaresSaleTotalPrice(cart.getTotalPrice());
        //物料信息拼装
        List<SubmitOrderWaresReq> waresReqs = buildMaterialDetail(subWares,baseInfo.getClientId());
        submitOrderReq.setWaresReqs(waresReqs);
        submitOrderReq.setCartCode(cart.getCode());
        submitOrderReq.setRemark(submitOrderInfo.getRemark());
        String freightTradeType = iscFreightService.freightTradeType2ShowType(submitOrderInfo.getTradeType());
        submitOrderReq.setTradeType(freightTradeType);
        submitOrderReq.setShippingType(submitOrderInfo.getShippingType());
        if (StationTypeEnum.LOCAL.getCode().intValue() == baseInfo.getStationType().intValue()) {
            submitOrderReq.setOrderType(OrderTypeEnum.LOCAL.getType());
        } else {
            submitOrderReq.setOrderType(OrderTypeEnum.CROSS_BORDER.getType());
        }
        submitOrderReq.setStationType(baseInfo.getStationType());
        submitOrderReq.setOrderTime(new Date());
        submitOrderReq.setEnv(baseInfo.getEnv());
        // 订单扩展信息
        Map<String, String> orderExtMap = Maps.newHashMap();
        orderExtMap.put(CommonConstant.PROCESS_KEY, submitOrderInfo.getProcessKey());
        orderExtMap.put(CommonConstant.SETTLE_INFO, JSON.toJSONString(submitOrderInfo.getConfigList()));
        submitOrderReq.setOrderExtInfo(orderExtMap);
        submitOrderReq.setThirdOrderId(submitOrderInfo.getThirdOrderId());
        if (StringUtils.isNotBlank(baseInfo.getSubAccount())) {
            submitOrderReq.setClientStaffId(baseInfo.getSubAccount());
            submitOrderReq.setSourceType(baseInfo.getLoginSource());
        }
        return submitOrderReq;
    }


    private SubmitOrderWaresReq buildSubmitOrderWaresReq(CartWaresResp wares) {
        SubmitOrderWaresReq submitOrderWaresReq = new SubmitOrderWaresReq();
        submitOrderWaresReq.setSku(wares.getSku());
        submitOrderWaresReq.setSkuNum(wares.getSkuNum());
        submitOrderWaresReq.setSkuName(wares.getSkuName());
        submitOrderWaresReq.setSkuPrice(wares.getOriginalPrice());
        submitOrderWaresReq.setSkuImg(wares.getSkuImg());
        submitOrderWaresReq.setSpecifications(wares.getModel());
        submitOrderWaresReq.setLanguage("zh");
        submitOrderWaresReq.setSellingPrice(wares.getPrice());
        submitOrderWaresReq.setParentSku("0");
        submitOrderWaresReq.setSkuType(0);
        return submitOrderWaresReq;
    }

//    private void dealSaleAttr(CartResp cartResp, String env) {
//        List<CartWaresResp> cartWares = cartResp.getCartWares();
//        if (null != cartResp && !cartWares.isEmpty()) {
//            cartWares.forEach(cart -> {
//                List<SkuSaleAttrDTO> saleAttributes = cart.getSaleAttributes();
//                Map<String, List<SkuSaleAttrDTO>> stringListMap = convertToMap(saleAttributes);
//                List<SkuSaleAttrDTO> skuSaleAttrDTOS = processLangData(stringListMap, env);
//                cart.setSaleAttributes(skuSaleAttrDTOS);
//            });
//        }
//
//    }
//
//    private List<SkuSaleAttrDTO> processLangData(Map<String, List<SkuSaleAttrDTO>> groupedData, String userLang) {
//        List<SkuSaleAttrDTO> result = new ArrayList<>();
//        if (StringUtils.isBlank(userLang)) {
//            log.error("销售属性，获取用户语言异常 processLangData");
//            return result;
//        }
//        // 按照 Map 的 key 逐一遍历
//        for (List<SkuSaleAttrDTO> dtoList : groupedData.values()) {
//            // 判断是否有中文的 "其他"
//            boolean hasOther = dtoList.stream()
//                    .anyMatch(dto -> "zh".equals(dto.getAttrLang()) && "其它".equals(dto.getValLangName()));
//
//            // 根据是否有 "其他" 来处理数据
//            dtoList.stream()
//                    .filter(dto -> dto.getAttrLang().equals(userLang)) // 筛选出符合用户语言的属性
//                    .forEach(dto -> {
//                        if (hasOther) {
//                            // 如果有 "其他" 属性，清空当前匹配的语言属性
//                            dto.setValLangName("");
//                            dto.setAttrLangName("");
//                        }
//                        result.add(dto);
//                    });
//        }
//        return result;
//    }
//
//
//    public Map<String, List<SkuSaleAttrDTO>> convertToMap(List<SkuSaleAttrDTO> saleAttrDTOList) {
//        if (saleAttrDTOList == null || saleAttrDTOList.isEmpty()) {
//            return Collections.emptyMap();
//        }
//
//        // 使用 groupingBy 进行分组，复合键由 attrAttributeId 和 valAttributeValueId 组成
//        return saleAttrDTOList.stream()
//                .collect(Collectors.groupingBy(dto -> dto.getAttrAttributeId() + ":" + dto.getValAttributeValueId()));
//    }


    private List<SubmitOrderWaresReq> buildMaterialDetail(List<SubmitOrderWaresReq> subWares, String clientId) {
        //获取所有sku对应的Long值列表
        List<Long> skuList = subWares.stream()
                .map(SubmitOrderWaresReq::getSku)
                .filter(Objects::nonNull)
                .map(s -> {
                    try {
                        return Long.valueOf(s);
                    } catch (NumberFormatException e) {
                        return null; // 过滤掉无法转换的值
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //按照mkuIds 获取 物料信息
        DataResponse<List<MkuMaterialApiDTO>> listDataResponse = iscMkuMaterialReadApiService.queryMkuMaterialDTOsByMkuIdClient(skuList, clientId);
        if (listDataResponse.getSuccess()) {
            //物料信息映射
            List<MkuMaterialApiDTO> data = listDataResponse.getData();
            Map<Long, MkuMaterialApiDTO> materialMap = data.stream()
                    .collect(Collectors.toMap(MkuMaterialApiDTO::getMkuId, Function.identity(), (a, b) -> a));
            // 遍历 orderWaresList，匹配 sku 并赋值
            subWares.forEach(orderWares ->
                    Optional.ofNullable(materialMap.get(Long.valueOf(orderWares.getSku())))
                            .ifPresent(material -> {
                                orderWares.setMaterialCode(material.getMaterialId());
                                orderWares.setMaterialName(material.getMaterialName());
                            })
            );

        }
        return subWares;
    }
}
