package com.jd.international.service.task.impl;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.config.InternationalConfig;
import com.jd.international.common.constants.UMPFunctionKeyConstant;
import com.jd.international.common.enums.file.FileTypeEnum;
import com.jd.international.common.utils.AssertValidation;
import com.jd.international.domain.task.biz.CustomerTaskPageVO;
import com.jd.international.domain.task.req.CustomerTaskDetailReqVO;
import com.jd.international.domain.task.req.CustomerTaskReqVO;
import com.jd.international.domain.task.req.CustomerTaskSaveUpdateReqVO;
import com.jd.international.domain.task.res.CustomerTaskResVO;
import com.jd.international.rpc.task.CustomerTaskRpcService;
import com.jd.international.service.authority.AuthorityService;
import com.jd.international.service.file.FileManageService;
import com.jd.international.service.support.excel.ExcelRowCountListener;
import com.jd.international.service.task.CustomerTaskService;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.library.common.constants.i18n.I18nKeyConstant;
import com.jdi.isc.library.common.exception.BizI18nException;
import com.jdi.isc.library.common.response.ResponseCode;
import com.jdi.isc.task.center.api.common.enums.CustomerTaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskCreateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;

/**
 * @Description: 销端异步任务服务实现
 * @Author: zhaojianguo21
 * @Date: 2024/08/16 13:33
 **/

@Slf4j
@Service
public class CustomerTaskServiceImpl implements CustomerTaskService {

    @Resource
    private FileManageService fileManageService;
    @Resource
    private CustomerTaskRpcService customerTaskRpcService;
    @Resource
    private InternationalConfig internationalConfig;

    @Resource
    private AsyncTaskExecutor importBaseTaskExecutor;
    @Resource
    private AuthorityService authorityService;

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public DataResponse<String> importData(BaseInfo baseInfo, MultipartFile file, Integer importType, TaskCreateTypeEnum taskCreateTypeEnum) {
        String lang = baseInfo.getEnv();
        String pin = baseInfo.getPin();
        try {
            log.info("CustomerTaskService.importData, param importType={}, baseInfo={}", importType, JSONObject.toJSONString(baseInfo));
            boolean hasAuthority = hasAuthority(baseInfo, importType);
            if (!hasAuthority){
                log.warn("CustomerTaskService.importData, no Authority. importType={}, baseInfo={}", JSONObject.toJSONString(baseInfo), importType);
                return DataResponse.error(ResponseCode.COMMON_NO_AUTHORIZED.getCode(), ResponseCode.COMMON_NO_AUTHORIZED.getMessage());
            }

            CustomerTaskBizTypeEnum taskBizTypeEnum =  CustomerTaskBizTypeEnum.forCode(importType);
            AssertValidation.isEmpty(taskBizTypeEnum, String.format(internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.FILE_IMPORT_TASK_TYPE_NOT_EXISTS), importType));

            ExcelRowCountListener excelRowCountListener = new ExcelRowCountListener();
            EasyExcelFactory.read(file.getInputStream(), excelRowCountListener).sheet(0).doReadSync();
            int totalRow = excelRowCountListener.getRowCount();
            log.info("CustomerTaskService.importData, totalRow={}", totalRow);
            // 校验最大条数
            AssertValidation.isTrue(totalRow > taskBizTypeEnum.getMax(), String.format(internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.FILE_DATA_EXCEED_LIMIT), taskBizTypeEnum.getMax()));
            // 校验为空
            AssertValidation.isTrue(totalRow <= 0, internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.FILE_NOT_ALLOW_EMPTY));

            FileTypeEnum fileTypeEnum = FileTypeEnum.BATCH_FILE;
            log.info("CustomerTaskService.importData upload file. pin ={},taskType={},totalRow={}",pin, importType, totalRow);
            DataResponse<String> urlResponse = fileManageService.upload(file, fileTypeEnum.getCode(), null, null, baseInfo);
            if (Boolean.FALSE.equals(urlResponse.getSuccess())) {
                throw new BizI18nException(urlResponse.getMessage(), com.jdi.isc.library.common.constants.i18n.I18nKeyConstant.COMMON_OPERATE_FAIL);
            }

            log.info("CustomerTaskService.importData save task start. pin ={},url={},taskType={}",pin, JSON.toJSONString(urlResponse), taskBizTypeEnum);
            CustomerTaskSaveUpdateReqVO reqDTO = this.getTaskReqDTO(baseInfo, file, urlResponse.getData(), fileTypeEnum, importType, taskCreateTypeEnum);
            DataResponse<CustomerTaskResVO> response =  this.saveOrUpdate(baseInfo, reqDTO);
            log.info("CustomerTaskService.importData save task end. pin ={},url={},taskType={},taskPo={}",pin, JSON.toJSONString(urlResponse),importType,JSON.toJSONString(response));
            if (!response.getSuccess()){
                log.info("CustomerTaskService.importData save task fail. pin ={},url={},taskType={},taskPo={}",pin, JSON.toJSONString(urlResponse),importType,JSON.toJSONString(response));
                return DataResponse.error(response.getMessage());
            }

            return DataResponse.success();
        } catch (BizI18nException biz) {
            log.error("CustomerTaskService.importData 导入文件业务异常,importType={} ", importType, biz);
            String i18nVal = internationalConfig.queryLanValOrDefault(baseInfo.getEnv(), biz.getMsgI18nEntry());
            return DataResponse.error(biz.getCode(), i18nVal);
        } catch (Exception e) {
            log.error("CustomerTaskService.importData 导入文件异常,importType={} ", importType, e);
            Profiler.businessAlarm(UMPFunctionKeyConstant.BUSINESS_KEY_TASK_IMPORT_WARNING+"_"+ active
                    , active + LevelCode.P0.getMessage() + String.format(internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.FILE_UPLOAD_EXCEPTION_101),  pin));
        }
        return DataResponse.error(ResponseCode.COMMON_RESPONSE_FAIL.getCode(), ResponseCode.COMMON_RESPONSE_FAIL.getMessage());
    }

    @Override
    public DataResponse<CustomerTaskResVO> saveOrUpdate(BaseInfo baseInfo, CustomerTaskSaveUpdateReqVO input){
        DataResponse<CustomerTaskResVO> result = customerTaskRpcService.saveOrUpdate(baseInfo, input);
        return result;
    }

    @Override
    public CustomerTaskResVO detail(CustomerTaskDetailReqVO input){
        CustomerTaskResVO result = customerTaskRpcService.detail(input);
        return result;
    }

    @Override
    public PageInfo<CustomerTaskPageVO.Response> pageSearch(CustomerTaskPageVO.Request input) {
        PageInfo<CustomerTaskPageVO.Response> result = customerTaskRpcService.pageSearch(input);
        return result;
    }

    private CustomerTaskSaveUpdateReqVO getTaskReqDTO(BaseInfo baseInfo, MultipartFile file, String url, FileTypeEnum fileTypeEnum, Integer bizTypeCode, TaskCreateTypeEnum taskCreateTypeEnum) {
        CustomerTaskReqVO bizData = new CustomerTaskReqVO();
        bizData.setReqFileName(file.getOriginalFilename());
        bizData.setReqFileUrl(url);
        bizData.setBizTypeCode(bizTypeCode);
        bizData.setCreateTypeCode(taskCreateTypeEnum.getCode());
        bizData.setReqParam(TaskCreateTypeEnum.IMPORT.equals(taskCreateTypeEnum)?(fileTypeEnum.getPath() + File.separator + FileUtil.getName(url)):"");

        CustomerTaskSaveUpdateReqVO reqParam = new CustomerTaskSaveUpdateReqVO();
        reqParam.setBizData(bizData);
        reqParam.setBaseInfo(baseInfo);
        return reqParam;
    }

    private boolean hasAuthority(BaseInfo baseInfo, Integer importType ){
        CustomerTaskBizTypeEnum bizTypeEnum = CustomerTaskBizTypeEnum.forCode(importType);
        boolean result = false;
        switch (bizTypeEnum){
            case IMPORT_MATERIAL:{
                result = authorityService.hasMaterialMenuAuthority(baseInfo);
                break;
            }
            default:{
                result = false;
                break;
            }
        }
        return result;
    }
}
