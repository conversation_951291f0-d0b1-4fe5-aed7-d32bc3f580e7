package com.jd.international.service.adapter.product;


import com.jd.international.domain.product.ProductPropertyDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientPropertyDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description:
 * @Author: zhaokun51
 * @Date: 2025/07/05 16:45
 **/
@Mapper
public interface ProductConvert {

    ProductConvert INSTANCE = Mappers.getMapper(ProductConvert.class);

    List<ProductPropertyDTO> listPropertyDTO2PropertyDTO(List<IscMkuClientPropertyDTO> propertyDTOS);

}
