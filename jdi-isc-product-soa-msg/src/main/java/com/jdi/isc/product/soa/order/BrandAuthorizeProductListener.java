package com.jdi.isc.product.soa.order;

import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/26 6:01 下午
 */
@Slf4j
@Component
public class BrandAuthorizeProductListener  {


    @JmqListener(id= "iscProductSoaJmq4Consumer", topics = {"${topic.jmq4.consumer.isc.order.submit}"})
    public void onMessage(List<Message> messages) {
        if (CollectionUtils.isEmpty(messages)) {
            return;
        }
        for (Message message : messages) {
            String text = null;
            try {
                text = message.getText();
                if (Objects.isNull(text)) {
                    continue;
                }

            } catch (Exception e) {
                log.error("BrandAuthorizeProductListener#onMessage msg:{},e:",text,e);
                Profiler.businessAlarm("com.jdi.isc.product.soa.order.BrandAuthorizeProductListener.onMessage", String.format("%s msg %s", LevelCode.P1.getMessage(),text));
                throw new RuntimeException(e);
            }

        }
    }

}
