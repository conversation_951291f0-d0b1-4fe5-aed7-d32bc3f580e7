package com.jdi.isc.product.soa.joySky;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.biz.component.api.enums.JoySkyBizFlowTypeEnum;
import com.jdi.isc.product.soa.api.approveorder.mq.JoySkyBizApprovalResultMqDTO;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.joySky.biz.ApprovalResultMsg;
import com.jdi.isc.product.soa.service.manage.approveorder.ApproveOrderManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceDraftManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceDraftManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceDraftManageService;
import com.jdi.isc.product.soa.service.manage.spu.impl.tax.BrSpuTaxManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 审批中心事件监听消费
 * @Author: zhaokun51
 * @Date: 2024/11/07 09:11
 **/
@Slf4j
@Component
public class JoySkyApproveListener {

    @Resource
    private CustomerSkuPriceDraftManageService customerSkuPriceDraftManageService;
    @Resource
    private BrSpuTaxManageService brSpuTaxManageService;
    @Resource
    private CountryAgreementPriceDraftManageService countryAgreementPriceDraftManageService;
    @Resource
    private SkuPriceDraftManageService skuPriceDraftManageService;
    @Value("${spring.profiles.active}")
    private String systemProfile;

    @Resource
    private ApproveOrderManageService approveOrderManageService;

    @JmqListener(id = "iscIntlApprove", topics = {"${topic.jmq4.consumer.approve.event}"})
    public void onMessage(List<Message> messages) {
        Integer processType = null;
        for (Message msg : messages) {
            if (Objects.isNull(msg) || Strings.isBlank(msg.getText())) {
                log.error("消息为空，自动过滤");
                return;
            }
            // 处理一步审核流回调
            this.handleApproveMessage(msg.getText());

            String processInstanceId = null;
            JoySkyBizFlowTypeEnum flowTypeEnum = null;
            try {

                ApprovalResultMsg approve = JSONObject.parseObject(msg.getText(), ApprovalResultMsg.class);
                processType = approve.getProcessType();
                processInstanceId = approve.getProcessInstanceId();
                Integer processStatus = approve.getProcessStatus();
                String rejectReason = approve.getRejectReason();
                String erp = approve.getOperator();

                flowTypeEnum = JoySkyBizFlowTypeEnum.getByFlowTypeCode(processType);

                log.info("JoySkyApproveListener.onMessage processType:{},processInstanceId:{}" +
                        ",processStatus:{},rejectReason:{},erp:{}",processType,processInstanceId
                        ,processStatus,rejectReason,erp);
                switch (flowTypeEnum) {
                    case PRODUCT_SALE_PRICE_FLOW:
                        customerSkuPriceDraftManageService.batchJoySkyApprove(erp,processStatus,processInstanceId,rejectReason);
                        break;
                    case PURCHASE_RATE_BR_FLOW:
                        brSpuTaxManageService.batchJoySkyApprove(erp,processStatus,processInstanceId,rejectReason);
                        break;
                    case SALE_RATE_FAILED_EFFECTIVE_FLOW:
                        customerSkuPriceDraftManageService.batchJoySkyEnableApprove(erp,processStatus,processInstanceId,rejectReason);
                        break;
                    case COUNTRY_AGREEMENT_EFFECTIVE_FLOW:
                        countryAgreementPriceDraftManageService.batchJoySkyApprove(erp,processStatus,processInstanceId,rejectReason);
                        break;
                    case PURCHASE_EFFECTIVE_FLOW:
                        skuPriceDraftManageService.batchJoySkyApprove(erp,processStatus,processInstanceId,rejectReason);
                        break;
                    default:
                        log.error("无匹配流程类型, processType:{}", flowTypeEnum.getFlowTypeCode());
                        break;
                }

            } catch (Exception e) {
                Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s %s 审批消息异常,processInstanceId: %s, 异常信息:%s"
                        , systemProfile
                        , LevelCode.P1.getMessage()
                        , flowTypeEnum != null ? flowTypeEnum.getProcessTitle() : processType
                        , processInstanceId
                        , e.getMessage()));
                log.error("JoySkyApprovalResultListener.onMessage Error param:{},e:", msg.getText(), e);
            }
        }
    }

    /**
     * 处理审核消息
     * <br/>
     * 这里独立出来主要因为需要加分布式锁，锁碰撞需要重试，而原方法不支持重试
     */
    private void handleApproveMessage(String text) {
        String processInstanceId = null;
        JoySkyBizFlowTypeEnum flowTypeEnum = null;

        Integer processType = null;

        try {
            ApprovalResultMsg approve = JSONObject.parseObject(text, ApprovalResultMsg.class);

            processType = approve.getProcessType();
            processInstanceId = approve.getProcessInstanceId();
            flowTypeEnum = JoySkyBizFlowTypeEnum.getByFlowTypeCode(processType);

            log.info("JoySkyApproveListener.onMessage message={}", text);
            switch (flowTypeEnum) {
                case COUNTRY_AGREEMENT_PRICE_LINE_FLOW:
                case CUSTOMER_SKU_PRICE_LINE_FLOW:
                    approveOrderManageService.batchJoySkyApprove(BeanUtil.toBean(approve, JoySkyBizApprovalResultMqDTO.class));
                    break;
                default:
                    log.error("handleApproveOrder. 无匹配流程类型, processType:{}", flowTypeEnum.getFlowTypeCode());
                    break;
            }

        } catch (Exception e) {
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s %s 审批消息异常,processInstanceId: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , flowTypeEnum != null ? flowTypeEnum.getProcessTitle() : processType
                    , processInstanceId
                    , e.getMessage()));
            log.error("JoySkyApprovalResultListener.onMessage Error param:{},e:", text, e);
            throw e;
        }
    }
}
