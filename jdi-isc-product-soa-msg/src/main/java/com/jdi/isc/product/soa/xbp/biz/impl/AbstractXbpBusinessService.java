package com.jdi.isc.product.soa.xbp.biz.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.xbp.jmq4.Mq4Message;
import com.jd.xbp.jmq4.data.Mq4ApproveBO;
import com.jd.xbp.jmq4.data.Mq4FlowBO;
import com.jd.xbp.jmq4.data.Mq4TicketBO;
import com.jd.xbp.jmq4.data.UserFullInfoBO;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.xbp.biz.XbpBusinessService;
import com.jdi.isc.product.soa.xbp.domain.vo.XbpMsgContextParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2024/4/24
 **/
@Slf4j
@Service
public abstract class AbstractXbpBusinessService implements XbpBusinessService {

    @Override
    public void ticketClose(XbpMsgContextParam xbpMsgContextParam, Mq4TicketBO mq4TicketBO) {
    }

    @Override
    public void ticketReject(XbpMsgContextParam xbpMsgContextParam, Mq4ApproveBO mq4ApproveBO) {

    }

    /**
     * 最后节点的审批人
     * @param mq4TicketBO
     * @return
     */
    public String lastOperator(Mq4TicketBO mq4TicketBO) {
        String updater = null;
        List<Mq4FlowBO> flowBOS =  mq4TicketBO.getFlows();
        if (CollectionUtils.isNotEmpty(flowBOS)){
            Mq4FlowBO mq4FlowBO = flowBOS.stream().max(Comparator.comparingInt(Mq4FlowBO::getStage)).get();
            updater = null!=mq4FlowBO?mq4FlowBO.getApprovers():null;
        }

        if (StringUtils.isBlank(updater)){
            updater = Constant.PIN_SYSTEM;
        }

        if(updater.contains(",")){
            updater = updater.split(",")[0];
        }

        return updater;
    }

    /**
     * 审批驳回人
     * @param mq4ApproveBO
     * @return
     */
    public String ticketRejectUser(Mq4ApproveBO mq4ApproveBO) {
        String updater = null;
        UserFullInfoBO userFullInfoBO = mq4ApproveBO.getApprover();
        if (null!=userFullInfoBO && StringUtils.isNotBlank(userFullInfoBO.getErp())){
            updater = userFullInfoBO.getErp();
        }

        if (StringUtils.isBlank(updater)){
            updater = Constant.PIN_SYSTEM;
        }

        return updater;
    }

    /**
     * 获取表单中某个属性数据
     * @param mq4Message,field
     * @return
     */
    public String getFieldData(Mq4Message mq4Message,String field) {
        JSONArray jsonArray = this.getFormData(mq4Message);
        for (Object item : jsonArray){
            JSONObject item1 = (JSONObject) item;
            String title = item1.getString("title");
            if(StringUtils.equals(field,title)){
               return item1.getString("value");
            }
        }
        return "";
    }

    /**
     * 获取表单数据
     * @param mq4Message
     * @return
     */
    public JSONArray getFormData(Mq4Message mq4Message) {
        Object obj = mq4Message.getData();
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(obj);
        String fields = jsonObject.getString("mainFields");
        if(StringUtils.isBlank(fields)){
            return null;
        }
        return JSONArray.parseArray(fields);
    }

    /**
     * 设置线程局部变量，用于存储当前操作员的pin码。
     * @param mq4TicketBO Mq4TicketBO对象，包含了当前操作员的信息。
     */
    public void setApproveLocal(Mq4TicketBO mq4TicketBO){
        String pin = lastOperator(mq4TicketBO);
        LangContextHolder.init(LangConstant.LANG_ZH);
        LoginContextHolder loginContextHolder = new LoginContextHolder();
        loginContextHolder.setPin(pin);
        LoginContextHolder.setLoginContextHolder(loginContextHolder);
    }

    /**
     * 设置线程局部变量，用于存储当前操作员的pin码。
     * @param approveBO Mq4ApproveBO对象，包含了当前操作员的信息。
     */
    public void setRejectLocal(Mq4ApproveBO approveBO){
        String pin = ticketRejectUser(approveBO);
        LangContextHolder.init(LangConstant.LANG_ZH);
        LoginContextHolder loginContextHolder = new LoginContextHolder();
        loginContextHolder.setPin(pin);
        LoginContextHolder.setLoginContextHolder(loginContextHolder);
    }
}
