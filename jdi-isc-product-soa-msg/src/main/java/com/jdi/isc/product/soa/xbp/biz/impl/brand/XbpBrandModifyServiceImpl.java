package com.jdi.isc.product.soa.xbp.biz.impl.brand;

import com.jd.xbp.jmq4.data.Mq4ApproveBO;
import com.jd.xbp.jmq4.data.Mq4TicketBO;
import com.jdi.isc.product.soa.service.manage.brand.xbp.XbpTicketBrandService;
import com.jdi.isc.product.soa.xbp.biz.impl.AbstractXbpBusinessService;
import com.jdi.isc.product.soa.xbp.domain.vo.XbpMsgContextParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/8/2
 **/
@Slf4j
@Service
public class XbpBrandModifyServiceImpl extends AbstractXbpBusinessService {

    @Resource
    private XbpTicketBrandService xbpTicketBrandService;

    @Override
    public void ticketClose(XbpMsgContextParam xbpMsgContextParam, Mq4TicketBO mq4TicketBO) {
    }

    @Override
    public void ticketReject(XbpMsgContextParam xbpMsgContextParam, Mq4ApproveBO mq4ApproveBO) {

    }
}
