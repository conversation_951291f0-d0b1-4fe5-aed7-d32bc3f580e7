<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd">


    <!-- 新类目接口 -->
    <jsf:consumer id="userLimitCategoryMroService"
                  interface="com.jd.mro.user.soa.sdk.service.UserLimitCategoryMroService" protocol="jsf"
                  alias="${jsf.userLimitCategoryMroService.alias}" timeout="5000" retries="0"/>

    <!-- 账户信息查询接口 -->
    <jsf:consumer id="accountInfoQueryService" interface="com.jd.ka.buser.sdk.service.AccountInfoQueryService"
                  alias="${jsf.accountInfoQueryService.alias}" timeout="5000"/>

    <!-- 账户信息修改接口 -->
    <jsf:consumer  id="accountOriginInfoService" interface="com.jd.ka.buser.sdk.service.AccountOriginInfoService"
                   alias="${jsf.accountInfoQueryService.alias}" timeout="5000"/>

    <jsf:consumer id="customMailService" interface="com.jd.leo.mail.product.produce.rpc.service.CustomMailService"
                  alias="${jsf.customMailService.alias}" timeout="5000">
        <jsf:parameter key="token" value="${jsf.customMailService.token}" hide="true"/>
    </jsf:consumer>


    <!-- 主站商品查询接口 -->
    <jsf:consumer id="productRpc" interface="com.jd.gms.crs.rpc.ProductRpc"
                  protocol="jsf" alias="${jsf.ProductRpc.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jsf.ProductRpc.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-one-ipc-soa" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jsf.ProductRpc.alias}" hide="true" />
    </jsf:consumer>
    <!-- 墨卡托sku与mku互转 -->
<!--    <jsf:consumer id="iJdiMkuQuerySoaService" interface="com.jd.jdi.product.center.core.boost.sdk.relation.IJdiMkuQuerySoaService"-->
<!--                  protocol="jsf" alias="${jsf.mkuQuery.alias}" timeout="5000" retries="0" check="false">-->
<!--    </jsf:consumer>-->

    <jsf:consumer id="userPassportExportService" interface="com.jd.user.sdk.export.UserPassportExportService"
                  protocol="jsf" alias="${jsf.user.alias}" timeout="5000" retries="0" check="false">
        <jsf:parameter  key="source" value="iep-oauth" hide="true" />
    </jsf:consumer>

    <!-- 促销接口 -->
    <jsf:consumer id="b2bPromotionService" alias="${jsf.b2bPromotionService.alias}"
                  interface="com.jd.b2b.promotion.engine.sdk.B2bPromotionService"
                  protocol="jsf" timeout="800">
    </jsf:consumer>

    <!-- 用户主数据 接口负责人(蒲腾飞 张士彬)-->
    <jsf:consumer id="enterpriseUserService" interface="com.jd.ka.user.soa.service.enterpriseUser.EnterpriseUserService"
                  alias="${jsf.enterpriseUserService.alias}" protocol="jsf" timeout="5000">
    </jsf:consumer>

    <!-- 工鼎商品查询服务 -->
    <jsf:consumer id="iJdiK2GdProductService" interface="com.jd.k2.gd.boost.sdk.soa.IJdiK2GdProductService"
                  protocol="jsf" alias="${jsf.iJdiK2GdProductService.alias}" timeout="5000"/>

    <!-- 藏经阁商品大字段查询接口  -->
    <jsf:consumer id="assemblyRpc" interface="com.jd.gms.crs.rpc.AssemblyRpc"
                  protocol="jsf" alias="${jsf.AssemblyRpc.alias}" timeout="1000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${consumer.alias.com.jd.gms.crs.rpc.AssemblyRpc.token}" hide="true"/>
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-one-ipc-soa" hide="true"/>
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${consumer.alias.com.jd.gms.crs.rpc.AssemblyRpc}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="iJdiGdGmsProductService" interface="com.jd.k2.gd.boost.sdk.soa.IJdiGdGmsProductService" protocol="jsf"
                  alias="${jsf.gd.wanfang.sdk.product.dscp.alias}" timeout="3000" retries="0" check="false"/>

    <!--品牌接口 -->
    <jsf:consumer id="brandInfoService" interface="jd.gms.category.dubbo.service.BrandInfoService"
                  alias="${jsf.brandInfoService.alias}"
                  retries="0" protocol="jsf" timeout="5000" />

    <jsf:consumer id="b2bGroupMemberQueryService" interface="com.jd.b2b.user.svr.sdk.service.B2bGroupMemberQueryService"
                  alias="${jsf.b2bGroupMemberQueryService.alias}"
                  retries="0" protocol="jsf" timeout="5000" />

    <!-- 装吧css查询-->
    <jsf:consumer id="popWareDetailService" alias="${jsf.PopWareDetailService.alias}"
                  interface="com.jd.sdd.mkt.sdk.service.PopWareDetailService" protocol="jsf" timeout="3000"/>

    <jsf:consumer id="iJdiK2GdCategoryService" alias="${jsf.IJdiK2GdCategoryService.alias}"
                  interface="com.jd.k2.gd.boost.sdk.soa.IJdiK2GdCategoryService" protocol="jsf" timeout="3000"/>
    <!-- 藏经阁促销读接口  -->
    <jsf:consumer id="giftInfoService" interface="com.jd.promo.client.GiftInfoService"
                  protocol="jsf" alias="${jfs.giftInfoService.alias}" timeout="1000" serialization="hessian">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jfs.giftInfoService.token}" hide="true"/>
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="(J-one)vxp-commons-biz" hide="true"/>
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jfs.giftInfoService.alias}" hide="true"/>
    </jsf:consumer>
    <jsf:consumer id="getAccountService" interface="com.jd.ka.user.soa.sdk.service.GetAccountService"
                  protocol="jsf" alias="${jsf.getAccountService.alias}" timeout="2000000" retries="1">
    </jsf:consumer>
    <jsf:consumer id="wareRpcService" interface="com.jd.ept.warecenter.api.ware.WareRpcService" timeout="1000" protocol="jsf" alias="${jsf.wareRpcService.alias}">
        <!--使用dpg图片压缩，可选，默认false-->
        <jsf:parameter key="dpg.use" value="true" hide="false"/>
    </jsf:consumer>
    <jsf:consumer id="wareBigFieldRpcService" interface="com.jd.ept.warecenter.api.ware.WareBigFieldRpcService" timeout="1000" protocol="jsf" alias="${jsf.wareRpcService.alias}">
        <!--大字段（商品介绍）替换国际图片域名，可选，默认false-->
        <jsf:parameter key="imgx.joybuy.use" value="true" hide="false"/>
        <!--使用dpg图片压缩，可选，默认false-->
        <jsf:parameter key="dpg.use" value="true" hide="false"/>
    </jsf:consumer>
<!--    <jsf:consumer id="translateSdkService" interface="com.jd.international.ware.center.service.TranslateSdkService"-->
<!--                  protocol="jsf" alias="${jsf.translateDemonstrateSdkService.alias}">-->
<!--    </jsf:consumer>-->
    <!--综合可售-->
    <jsf:consumer id="availableSaleService" interface="com.available.sale.api.service.AvailableSaleService"
                  protocol="jsf" alias="${jsf.availableSaleService.alias}" timeout="5000" retries="1">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jsf.availableSaleService.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-isp-product-server" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jsf.availableSaleService.alias}" hide="true" />
    </jsf:consumer>

    <!-- iop订单服务 -->
    <jsf:consumer id="operaOrderOpenProvider" interface="com.jd.ka.gpt.soa.client.StandardOperaOrderOpenProvider"
                  alias="${provider.iop.order.alias}" protocol="jsf" timeout="5000" serialization="hessian"/>
    <!-- iop标品服务 -->
    <jsf:consumer id="standardQueryGoodsOpenProvider" interface="com.jd.ka.gpt.soa.client.StandardQueryGoodsOpenProvider"
                  alias="${provider.iop.standard.goods.alias}" protocol="jsf" timeout="5000" serialization="hessian"/>
    <!-- iop标品查询服务 -->
    <jsf:consumer id="queryOrderOpenProvider" interface="com.jd.ka.gpt.soa.client.StandardQueryOrderOpenProvider"
                  alias="${provider.iop.order.query.alias}" protocol="jsf" timeout="5000" serialization="hessian"/>
    <!-- ept订单服务 -->
    <jsf:consumer id="b2bOrderService" interface="com.jd.ept.order.sdk.B2BOrderService"
                  alias="${provider.ept.order.alias}" protocol="jsf" timeout="30000" serialization="hessian"/>

    <!-- 工采国际类目服务-->
    <jsf:consumer id="categoryApiService" interface="com.jdi.isc.aggregate.read.wisp.api.category.CategoryApiService"
                  alias="${jd.jsf.consumer.wisp.category.alias}" protocol="jsf" serialization="hessian"/>
    <!-- 工采国际商品服务-->
    <jsf:consumer id="mkuClientApiService" interface="com.jdi.isc.aggregate.read.wisp.api.mku.MkuClientApiService"
                  alias="${jd.jsf.consumer.wisp.mku.alias}" timeout="${jd.jsf.consumer.common.long.timeout}" protocol="jsf" serialization="hessian"/>

    <!-- 工采国际商品服务-->
    <jsf:consumer id="orderReadApiService" interface="com.jdi.isc.aggregate.read.wisp.api.order.OrderReadApiService"
                  alias="${jd.jsf.consumer.wisp.order.alias}" timeout="${jd.jsf.consumer.wisp.order.timeout}" protocol="jsf" serialization="hessian"/>
    <jsf:consumer id="orderWriteApiService"
                  interface="com.jdi.isc.order.center.api.OrderWriteApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.order.center.write.alias}"  timeout="15000" serialization="hessian"/>
    <!--  工采国际agg服务 运费计算-->
    <jsf:consumer id="calculateFreightApiService"
                  interface="com.jdi.isc.aggregate.read.wisp.api.freight.CalculateFreightApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.wisp.freight.calculate.alias}"  serialization="hessian"/>
    <!--  工采国际agg服务 地址服务-->
    <jsf:consumer id="areaApiService"
                  interface="com.jdi.isc.aggregate.read.wisp.api.area.AreaApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.wisp.area.alias}"  serialization="hessian"/>
    <!--  工采国际agg服务 客户信息-->
    <jsf:consumer id="wispCustomerReadService"
                  interface="com.jdi.isc.aggregate.read.wisp.api.customer.WispCustomerReadService"
                  protocol="jsf" alias="${jd.jsf.consumer.wisp.customer.alias}"  serialization="hessian"/>

    <!--配置功能-->
    <jsf:consumer id="configFunctionService" interface="com.jd.ka.mro.workflow.soa.sdk.service.ConfigFunctionService"
                  alias="${jd.jsf.consumer.config.alias}" timeout="5000" check="false">
    </jsf:consumer>
    <!--配置项-->
    <jsf:consumer id="functionEnumService" interface="com.jd.ka.mro.workflow.soa.sdk.service.FunctionEnumService"
                  alias="${jd.jsf.consumer.config.alias}" timeout="5000" check="false">
    </jsf:consumer>
    <!--订单配置项-->
    <jsf:consumer id="mroOrderConfigInfoService" interface="com.jd.ka.mro.workflow.soa.sdk.service.OrderConfigInfoService"
                  alias="${jd.jsf.consumer.config.alias}" timeout="5000" check="false">
    </jsf:consumer>
    <!--工作流订单审批-->
    <jsf:consumer id="multiOrderService" interface="com.jd.ka.mro.workflow.soa.sdk.service.MultiOrderService"
                  alias="${jd.jsf.consumer.workflow.alias}" timeout="5000" check="false">
    </jsf:consumer>
    <jsf:consumer id="multiApprovalQueryService" interface="com.jd.ka.mro.workflow.soa.sdk.service.approval.MultiApprovalQueryService"
                  alias="${jd.jsf.consumer.workflow.alias}" timeout="5000" check="false">
    </jsf:consumer>
    <jsf:consumer id="roleInfoService" interface="com.jd.ka.mro.workflow.soa.sdk.service.RoleInfoService"
                  alias="${jd.jsf.consumer.workflow.alias}" timeout="5000" check="false">
    </jsf:consumer>
    <jsf:consumer id="processRoleRelService" interface="com.jd.ka.mro.workflow.soa.sdk.service.ProcessRoleRelService"
                  alias="${jd.jsf.consumer.workflow.alias}" timeout="5000" check="false">
    </jsf:consumer>
    <!--多条件审批-->
    <jsf:consumer id="multiConditionProcessService" interface="com.jd.ka.mro.workflow.soa.sdk.service.MultiConditionProcessService"
                  alias="${jd.jsf.consumer.workflow.multiConditionProcess.alias}" protocol="jsf" timeout="5000">
    </jsf:consumer>

    <!--  工采国际agg服务 运费计算-->
    <jsf:consumer id="orderCostApiService"
                  interface="com.jdi.isc.aggregate.read.wisp.api.orderCost.OrderCostApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.wisp.freight.calculate.alias}"  serialization="hessian"/>
<!--    &lt;!&ndash;订单读服务&ndash;&gt;-->
<!--    <jsf:consumer id="orderReadBaseService" interface="com.jdi.isc.aggregate.read.service.manage.order.OrderReadBaseService"-->
<!--                  alias="${jd.jsf.consumer.workflow.alias}" timeout="5000" check="false">-->
<!--    </jsf:consumer>-->
    <!--订单物流轨迹接口-->
    <jsf:consumer id="deliveryWispApiService"
                  interface="com.jdi.isc.aggregate.read.wisp.api.delivery.DeliveryWispApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.wisp.deliveryApiService.alias}"
                  serialization="hessian">
    </jsf:consumer>

    <!--履约时效接口-->
    <jsf:consumer id="promiseApiService"
                  interface="com.jdi.isc.aggregate.read.wisp.api.promise.PromiseApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.wisp.promise.alias}" timeout="10000"
                  serialization="hessian">
    </jsf:consumer>

    <!--通用商品查询服务-->
    <jsf:consumer id="skuReadApiService"
                  interface="com.jdi.isc.aggregate.read.api.sku.SkuReadApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.common.sku.alias}" timeout="10000"
                  serialization="hessian">
    </jsf:consumer>

    <!--  xbp申请单相关  -->
    <jsf:consumer
            id="ticketService" interface="com.jd.xbp.jsf.api.TicketService"
            protocol="jsf" alias="${jd.jsf.consumer.xbp.common.alias}" timeout="10000" retries="0" serialization="hessian">
    </jsf:consumer>

    <!--  xbp流程相关  -->
    <jsf:consumer
            id="processService" interface="com.jd.xbp.jsf.api.ProcessService"
            protocol="jsf" alias="${jd.jsf.consumer.xbp.common.alias}" timeout="10000" retries="0" serialization="hessian">
    </jsf:consumer>

    <!--  订单打印  -->
    <jsf:consumer
            id="orderPrintPDFApiService" interface="com.jdi.isc.order.center.api.orderRead.OrderPrintPDFApiService"
            protocol="jsf" alias="${jd.jsf.consumer.order.print.alias}" timeout="100000" retries="0" serialization="hessian">
    </jsf:consumer>

    <!--  查询物料编码  -->
    <jsf:consumer
            id="iscMkuMaterialReadApiService" interface="com.jdi.isc.product.soa.api.material.IscMkuMaterialReadApiService"
            protocol="jsf" alias="${jd.jsf.consumer.product.IscMkuMaterialReadApiService.alias}" timeout="10000" retries="0" serialization="hessian">
    </jsf:consumer>

    <!-- 语言 -->
    <jsf:consumer id="iscProductSoaLangApiService" interface="com.jdi.isc.product.soa.api.wimp.lang.IscProductSoaLangApiService"
                  protocol="jsf" alias="${jsf.provider.common.alias}" timeout="10000" serialization="hessian" />

    <!-- 售后 -->
    <jsf:consumer id="afterSalesApiService" interface="com.jdi.isc.order.center.api.aftersales.AfterSalesApiService"
                  protocol="jsf" alias="${jsf.provider.common.alias}" timeout="10000" />

    <!-- 查询Mku库存服务 -->
    <jsf:consumer id="iscMkuStockReadApiService" interface="com.jdi.isc.product.soa.stock.mku.IscMkuStockReadApiService"
                  protocol="jsf" alias="${jsf.provider.common.alias}" timeout="10000" serialization="hessian" />

    <!-- 销端价格服务 -->
    <jsf:consumer id="clientPriceReadApiService" interface="com.jdi.isc.product.soa.price.api.clientPrice.ClientPriceReadApiService"
                  protocol="jsf" alias="${jsf.provider.common.alias}" timeout="10000" serialization="hessian" />

    <!-- MKU读服务 -->
    <jsf:consumer id="iscProductSoaMkuReadApiService" interface="com.jdi.isc.product.soa.api.mku.IscProductSoaMkuReadApiService"
                  protocol="jsf" alias="${jsf.provider.common.alias}" timeout="10000" serialization="hessian"/>

    <!-- 币种 -->
    <jsf:consumer id="iscProductSoaCurrencyApiService" interface="com.jdi.isc.product.soa.api.wimp.currency.IscProductSoaCurrencyApiService"
                  protocol="jsf" alias="${jsf.provider.common.alias}" timeout="10000" serialization="hessian"/>

    <!--金鹏商详页话术接口 https://cf.jd.com/pages/viewpage.action?pageId=766926862 -->
    <jsf:consumer id="promiseSdkService"
                  interface="com.jd.jp.strategy.sdk.service.PromiseSdkService"
                  protocol="jsf" alias="${jd.jsf.consumer.PromiseSdkService.alias}" timeout="10000"  serialization="hessian" retries="1"/>

    <!--品牌服务-->
    <jsf:consumer id="brandApiService" interface="com.jdi.isc.product.soa.api.wimp.brand.BrandApiService"
                  protocol="jsf" alias="${jsf.provider.common.alias}" timeout="10000" serialization="hessian"/>

    <!--类目服务-->
    <jsf:consumer id="jdiIscSoaCategoryApiService" interface="com.jdi.isc.product.soa.api.wimp.category.CategoryApiService"
                  protocol="jsf" alias="${jsf.provider.common.alias}" timeout="10000" serialization="hessian"/>

    <!--查询属性服务-->
    <jsf:consumer id="iscAttributeReadApiService" interface="com.jdi.isc.product.soa.api.attribute.IscAttributeReadApiService"
                  protocol="jsf" alias="${jsf.provider.common.alias}" timeout="10000" serialization="hessian"/>


</beans>