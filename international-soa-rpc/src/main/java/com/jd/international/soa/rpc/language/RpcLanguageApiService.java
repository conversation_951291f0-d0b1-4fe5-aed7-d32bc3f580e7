package com.jd.international.soa.rpc.language;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;

import java.util.Map;

/**
 * @Description:
 * @Author: chengliwei7
 */
public interface RpcLanguageApiService {
    /**
     * 根据语言获取对应的语言翻译配置
     * @param lang 为 null 表示获取所有语言的配置
     * @return
     */
    DataResponse<JSONObject> getLangLibraryByLang(String lang);

    /**
     * 获取某个语言的配置
     * @param lang 语言
     * @return base64编码键 -> 翻译 的映射
     */
    DataResponse<Map<String, String>> getLanguageConfigByLang(String lang);
}
