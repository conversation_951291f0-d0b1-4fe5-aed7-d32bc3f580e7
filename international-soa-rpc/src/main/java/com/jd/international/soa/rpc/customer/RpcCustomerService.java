package com.jd.international.soa.rpc.customer;

import com.jdi.isc.aggregate.read.wisp.api.customer.res.CustomerReadResp;

/**
 * <AUTHOR>
 * @date 2024/3/14
 **/
public interface RpcCustomerService {

    /**
     * 根据客户端代码获取客户读取响应
     *
     * @param clientCode 客户端代码
     * @return CustomerReadResp 客户读取响应
     */
    CustomerReadResp getCustomerByClientCode(String clientCode);


    /**
     * 根据合同号获取客户信息
     * @param contractNum
     * @return
     */
    CustomerReadResp getCustomerByContractNum(String contractNum);

    /**
     * 获取某个地区国家的游客配置
     * @param area 地区国家
     */
    CustomerReadResp getGuestByOpenArea(String area);

    /**
     * 获取某个客户的国家或地区
     * @param clientCode
     * @return
     */
    String getCountryCodeByClientCode(String clientCode);
}
