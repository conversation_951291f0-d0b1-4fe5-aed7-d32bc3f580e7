package com.jd.international.soa.rpc.isc.mku;


import com.jdi.isc.aggregate.read.wisp.api.delivery.resp.DeliveryAgingResp;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.*;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuLangsReqDTO;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuReqDTO;
import com.jdi.isc.product.soa.api.mku.req.IscMkuAvailableSaleReq;
import com.jdi.isc.product.soa.api.mku.res.IscMkuAvailableSaleResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuLangsResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuResDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 商品服务接口
 * @Author: zhaojianguo21
 * @Date: 2023/12/13 16:26
 **/
public interface RpcMkuService {

//    /**
//     * 获取商品列表简要信息
//     * @param input
//     * @return
//     */
//    public MkuClientListInfoResApiDTO listSimpleInfo(MkuClientListInfoReqApiDTO input);

//    /**
//     * 商品列表
//     * @param input 列表参数
//     * @return 商品列表
//     */
//    PageInfo<MkuClientApiDTO> page(MkuClientPageReqApiDTO input);
//
//
//    /**
//     * 是否存在mku
//     * @param input 请求参数
//     * @return true/false
//     */
//    Boolean existsMku(MkuClientDetailReqApiDTO input);

//    /**
//     * mku信息
//     * @param input 请求参数
//     * @return mku描述
//     */
//    MkuClientApiDTO baseInfo(MkuClientDetailReqApiDTO input);
//
//
//    /**
//     * 查同mku的同组聚堆信息
//     * @param input 请求参数
//     * @return 同组商品聚堆信息
//     */
//    MkuClientGroupApiDTO groupInfo(MkuClientDetailReqApiDTO input);
//
//    /**
//     * 商品列表页单个商品卡片信息
//     * @param input 请求参数
//     * @return 商品卡片信息
//     */
//    MkuClientCardInfoResApiDTO cardInfo(MkuClientCardInfoReqApiDTO input);
//
//
//    /**
//     * 获取在池mku
//     */
//    Set<Long> queryPoolMkuIds(MkuClientListInfoReqApiDTO input);

    /**
     * 获取现货库存信息
     */
    List<MkuClientStockApiDTO> getStock(MkuClientStockReqApiDTO req);

    /**
     * 获取现货库存Map
     */
    Map<Long,MkuClientStockApiDTO> getStockMap(MkuClientStockReqApiDTO req);

    /**
     * 是否存在有效mku
     * @param input 请求参数
     * @return true/false
     */
    Boolean existsValidMku(MkuClientDetailReqApiDTO input);

    /**
     * 获取商品的配送时效
     */
    Map<Long, DeliveryAgingResp> getDeliveryAging(List<Long> mkuIds, String clientCode);

    /**
     * 根据批量查询请求获取ISC MKU资源映射信息。
     * @param req 批量查询MKU的请求参数，包含需要查询的MKU列表。
     * @return 一个Map对象，key为MKU的ID，value为对应的IscMkuResDTO对象。
     */
    Map<Long, IscMkuResDTO> getIscMku(BatchQueryMkuReqDTO req);

    /**
     * 根据批量查询条件获取多语言商品信息。
     * @param req 批量查询多语言商品信息的请求参数。
     * @return 包含多语言商品信息的 Map 对象，键为商品 ID，值为对应的多语言商品信息。
     */
    Map<Long, IscMkuLangsResDTO> getIscMkuLangs(BatchQueryMkuLangsReqDTO req);


    /**
     * 根据 MKU ID 批量查询对应的 ISC SKU ID。
     * @param req 包含 MKU ID 的请求对象。
     * @return 每个 MKU ID 对应的 ISC SKU ID，键为 MKU ID，值为 ISC SKU ID。
     */
    Map<Long, Long> getIscSkuIdByMkuId(BatchQueryMkuReqDTO req);

    /**
     * 根据批量查询请求获取ISC MKU的销售状态。
     * @param req 批量查询MKU的请求对象，包含需要查询的MKU列表。
     * @return 一个Map对象，其中key为MKU的ID，value为对应的销售状态。
     */
    Map<Long, IscMkuAvailableSaleResDTO> getMkuAvailable(IscMkuAvailableSaleReq req);

    /**
     * 根据 MKU ID 批量查询对应的 ISC JD SKU ID
     * @param req 批量查询 MKU ID 的请求对象
     * @return ISC JD SKU ID 与 MKU ID 的映射关系
     */
    Map<Long, Long> getIscJdSkuIdByMkuId(BatchQueryMkuReqDTO req);

    /**
     * 根据 MKU ID 批量查询 ISC SPu SKU 关系。
     * @param req 包含 MKU ID 的请求对象。
     * @return 每个 MKU ID 对应的 ISC SPu SKU 关系的映射，键为 MKU ID，值为是否存在 ISC SPu SKU 关系的布尔值。
     */
    Map<Long, Boolean> getIscSpuSkuRelationByMkuId(BatchQueryMkuReqDTO req);


    /**
     * 异步获取指定 MKU 的交付信息。
     * @param mkuIds 需要查询的 MKU ID 列表。
     * @param clientCode 客户代码，用于区分不同客户的请求。
     * @return 一个 Map 对象，其中键是 MKU ID，值是对应的 DeliveryAgingResp 对象。
     */
    Map<Long, DeliveryAgingResp> getDeliveryAgingAsync(List<Long> mkuIds, String clientCode);
}
