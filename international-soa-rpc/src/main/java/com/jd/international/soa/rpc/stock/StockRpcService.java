package com.jd.international.soa.rpc.stock;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.stock.mku.IscMkuStockReadApiService;
import com.jdi.isc.product.soa.stock.mku.req.IscMkuStockItemReadReqDTO;
import com.jdi.isc.product.soa.stock.mku.req.IscMkuStockReadReqDTO;
import com.jdi.isc.product.soa.stock.mku.res.IscMkuStockResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StockRpcService {

    @Resource
    private IscMkuStockReadApiService iscMkuStockReadApiService;

    /**
     * 获取MKU库存信息。
     * @param reqDTO MKU库存读取请求参数。
     * @return MKU库存信息映射，key为MKU ID，value为MKU库存响应数据。
     */
    public Map<Long, IscMkuStockResDTO> getMkuStock(IscMkuStockReadReqDTO reqDTO){
        log.error("MkuStockRpcService.getMkuStock start");
        Map<Long, IscMkuStockResDTO> resultMap = Maps.newHashMap();
        try {
            // 分批查询
            List<IscMkuStockItemReadReqDTO> stockItems = reqDTO.getStockItem();
            List<List<IscMkuStockItemReadReqDTO>> splitStockItems = Lists.partition(stockItems, 50);
            for (List<IscMkuStockItemReadReqDTO> subStockItemList : splitStockItems){
                IscMkuStockReadReqDTO subReqDto = new IscMkuStockReadReqDTO();
                subReqDto.setClientCode(reqDTO.getClientCode());
                subReqDto.setStockItem(subStockItemList);

                Map<Long, IscMkuStockResDTO> subRes = getMkuStockSub(subReqDto);
                // 汇总分批查询结果
                resultMap.putAll(subRes);
            }
        } catch (Exception e) {
            log.error("MkuStockRpcService.getMkuStock error 入参:[{}]", JSON.toJSONString(reqDTO), e);
        }finally {
            log.info("MkuStockRpcService.getMkuStock info 入参:[{}] 出参:[{}]", JSON.toJSONString(reqDTO), JSON.toJSONString(resultMap));
        }
        return resultMap;
    }

    /**
     * 查询库存
     * @param reqDTO
     * @return
     */
    private Map<Long, IscMkuStockResDTO> getMkuStockSub(IscMkuStockReadReqDTO reqDTO){
        log.error("MkuStockRpcService.getMkuStockSub start");
        Map<Long, IscMkuStockResDTO> resultMap = Maps.newHashMap();
        try {
            DataResponse<Map<Long, IscMkuStockResDTO>> response = iscMkuStockReadApiService.getMkuStock(reqDTO);
            if (null != response && response.getSuccess()) {
                resultMap = response.getData();
            }
        } catch (Exception e) {
            log.error("MkuStockRpcService.getMkuStockSub error 入参:[{}]", JSON.toJSONString(reqDTO), e);
        }finally {
            log.info("MkuStockRpcService.getMkuStockSub info 入参:[{}] 出参:[{}]", JSON.toJSONString(reqDTO), JSON.toJSONString(resultMap));
        }
        return resultMap;
    }

    /**
     * 构建库存map
     */
    public Map<Long, IscMkuStockResDTO> buildStockMap(String clientCode, Set<Long> mkuIds,Map<Long, Integer> mkuMoqMap,Map<Long,Long> mkuSkuMap) {
        IscMkuStockReadReqDTO input = new IscMkuStockReadReqDTO();
        input.setClientCode(clientCode);
        input.setStockItem(mkuIds.stream()
                .map(mkuId -> new IscMkuStockItemReadReqDTO(mkuMoqMap.getOrDefault(mkuId, 1),mkuId,mkuSkuMap.get(mkuId)))
                .collect(Collectors.toList()));
        return this.getStock(input);
    }

    /** 查询库存数量*/
    public Map<Long, IscMkuStockResDTO> getStock(IscMkuStockReadReqDTO input) {
        List<IscMkuStockItemReadReqDTO> stockItem = Lists.newArrayList();
        for (IscMkuStockItemReadReqDTO stockItemReqVO : input.getStockItem()) {
            stockItem.add(new IscMkuStockItemReadReqDTO(stockItemReqVO.getNum(),stockItemReqVO.getMkuId(),stockItemReqVO.getSkuId()));
        }
        Map<Long, IscMkuStockResDTO> mkuStock = this.getMkuStock(new IscMkuStockReadReqDTO(stockItem, input.getClientCode()));
        if (MapUtils.isEmpty(mkuStock)) {
            return Maps.newHashMap();
        }
        return mkuStock;
    }
}
