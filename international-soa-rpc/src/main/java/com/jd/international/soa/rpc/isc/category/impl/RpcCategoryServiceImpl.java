package com.jd.international.soa.rpc.isc.category.impl;

import com.alibaba.fastjson.JSONObject;
import com.jd.international.soa.rpc.isc.category.RpcCategoryService;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.wisp.api.category.CategoryApiService;
import com.jdi.isc.aggregate.read.wisp.api.category.biz.CategoryReqDTO;
import com.jdi.isc.aggregate.read.wisp.api.category.biz.CategoryTreeNodeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2023/12/13 16:27
 **/
@Slf4j
@Service
public class RpcCategoryServiceImpl implements RpcCategoryService {

    @Autowired
    @Qualifier(value = "categoryApiService")
    private CategoryApiService categoryApiService;

    @Override
    public CategoryTreeNodeDTO categoryTree(CategoryReqDTO categoryReqDTO) {
        DataResponse<CategoryTreeNodeDTO> response = categoryApiService.categoryTree(categoryReqDTO);
        if (null==response || !response.getSuccess()){
            log.warn("response fail. categoryReqDTO={}", JSONObject.toJSONString(categoryReqDTO));
            return null;
        }
        return response.getData();
    }
}
