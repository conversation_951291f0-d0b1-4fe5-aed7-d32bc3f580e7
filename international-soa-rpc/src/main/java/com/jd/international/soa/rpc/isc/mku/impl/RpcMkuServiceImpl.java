package com.jd.international.soa.rpc.isc.mku.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jd.international.soa.rpc.isc.mku.RpcMkuService;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.wisp.api.delivery.resp.DeliveryAgingResp;
import com.jdi.isc.aggregate.read.wisp.api.mku.MkuClientApiService;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.*;
import com.jdi.isc.product.soa.api.mku.IscProductSoaMkuReadApiService;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuLangsReqDTO;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuReqDTO;
import com.jdi.isc.product.soa.api.mku.req.IscMkuAvailableSaleReq;
import com.jdi.isc.product.soa.api.mku.res.IscMkuAvailableSaleResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuLangsResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2023/12/16 17:35
 **/
@Slf4j
@Service
public class RpcMkuServiceImpl implements RpcMkuService {

    @Resource
    private MkuClientApiService mkuClientApiService;
    @Resource
    private ExecutorService searchExecutorService;
    @Resource
    private IscProductSoaMkuReadApiService iscProductSoaMkuReadApiService;

//    @Override
//    public MkuClientListInfoResApiDTO listSimpleInfo(MkuClientListInfoReqApiDTO input){
//        DataResponse<MkuClientListInfoResApiDTO> result = mkuClientApiService.listSimpleInfo(input);
//        // todo 删除日志
//        log.info("listSimpleInfo, result={}", JSONObject.toJSONString(result));
//        return result.getData();
//    }

//    @Override
//    public PageInfo<MkuClientApiDTO> page(MkuClientPageReqApiDTO input){
//        DataResponse<PageInfo<MkuClientApiDTO>> response = mkuClientApiService.page(input);
//        if (null==response || !response.getSuccess()){
//            log.warn("page, response fail. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//        return response.getData();
//    }
//
//    @Override
//    public Boolean existsMku(MkuClientDetailReqApiDTO input) {
//        DataResponse<Boolean> response = mkuClientApiService.existsMku(input);
//        if (null==response || !response.getSuccess()){
//            log.warn("existsMku, response fail. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//        return response.getData();
//    }

//    @Override
//    public MkuClientApiDTO baseInfo(MkuClientDetailReqApiDTO input) {
//        DataResponse<MkuClientApiDTO> response = mkuClientApiService.baseInfo(input);
//        if (null==response || !response.getSuccess()){
//            log.warn("baseInfo, response fail. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//        return response.getData();
//    }
//
//    @Override
//    public MkuClientGroupApiDTO groupInfo(MkuClientDetailReqApiDTO input) {
//        DataResponse<MkuClientGroupApiDTO> response = mkuClientApiService.groupInfo(input);
//        if (null==response || !response.getSuccess()){
//            log.warn("groupInfo, response fail. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//        return response.getData();
//    }
//
//    @Override
//    public MkuClientCardInfoResApiDTO cardInfo(MkuClientCardInfoReqApiDTO input) {
//        DataResponse<MkuClientCardInfoResApiDTO> response = mkuClientApiService.cardInfo(input);
//        if (null==response || !response.getSuccess()){
//            log.warn("cardInfo, response fail. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//        return response.getData();
//    }
//
//    @Override
//    public Set<Long> queryPoolMkuIds(MkuClientListInfoReqApiDTO input) {
//        DataResponse<Set<Long>> response = mkuClientApiService.queryPoolMkuIds(input);
//        if (null==response || !response.getSuccess()){
//            log.warn("cardInfo, response fail. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//        return response.getData();
//    }

    @Override
    public List<MkuClientStockApiDTO> getStock(MkuClientStockReqApiDTO req) {
        DataResponse<List<MkuClientStockApiDTO>> res = null;
        try {
            res = mkuClientApiService.getStock(req);
            if(res.getSuccess()){
                return res.getData();
            }
        }finally {
            log.info("RpcMkuServiceImpl.getStock req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(res));
        }
        return null;
    }

    @Override
    public Map<Long,MkuClientStockApiDTO> getStockMap(MkuClientStockReqApiDTO req) {
        DataResponse<List<MkuClientStockApiDTO>> res = null;
        try {
            res = mkuClientApiService.getStock(req);
            if(res.getSuccess()){
                return res.getData().stream().collect(Collectors.toMap(MkuClientStockApiDTO::getMkuId, Function.identity()));
            }
        }finally {
            log.info("RpcMkuServiceImpl.getStockMap req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(res));
        }
        return null;
    }

    @Override
    public Boolean existsValidMku(MkuClientDetailReqApiDTO input) {
        DataResponse<Boolean> response = mkuClientApiService.existsValidMku(input);
        if (null==response || !response.getSuccess()){
            log.warn("existsValidMku, response fail. input={}", JSONObject.toJSONString(input));
            return null;
        }
        return response.getData();
    }

    @Override
    public Map<Long, DeliveryAgingResp> getDeliveryAging(List<Long> mkuIds, String clientCode) {
        DataResponse<Map<Long, DeliveryAgingResp>> deliveryInfo = mkuClientApiService.getDeliveryInfo(mkuIds, clientCode);
        if (deliveryInfo == null || !deliveryInfo.getSuccess()) {
            return Collections.emptyMap();
        }
        return deliveryInfo.getData();
    }

    @Override
    public Map<Long, IscMkuResDTO> getIscMku(BatchQueryMkuReqDTO req){
        DataResponse<Map<Long, IscMkuResDTO>> res = null;
        try {
            res = iscProductSoaMkuReadApiService.getIscMku(req);
            if(res!=null && res.getSuccess()){
                return res.getData();
            }
        }finally {
            log.info("RpcMkuServiceImpl.getIscMku req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(res));
        }
        return null;
    }

    @Override
    public Map<Long, IscMkuLangsResDTO> getIscMkuLangs(BatchQueryMkuLangsReqDTO req){
        DataResponse<Map<Long, IscMkuLangsResDTO>> res = null;
        try {
            res = iscProductSoaMkuReadApiService.getIscMkuLangs(req);
            if(res!=null && res.getSuccess()){
                return res.getData();
            }
        }finally {
            log.info("RpcMkuServiceImpl.getIscMkuLangs req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(res));
        }
        return null;
    }

    @Override
    public Map<Long, Long> getIscSkuIdByMkuId(BatchQueryMkuReqDTO req){
        DataResponse<Map<Long, Long>> res = null;
        try {
            res = iscProductSoaMkuReadApiService.getIscSkuIdByMkuId(req);
            if(res!=null && res.getSuccess()){
                return res.getData();
            }
        }finally {
            log.info("RpcMkuServiceImpl.getIscSkuIdByMkuId req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(res));
        }
        return null;
    }

    @Override
    public Map<Long, IscMkuAvailableSaleResDTO> getMkuAvailable(IscMkuAvailableSaleReq req) {
        DataResponse<Map<Long, IscMkuAvailableSaleResDTO>> res = null;
        try {
            res = iscProductSoaMkuReadApiService.queryMkuAvailable(req);
            if(res!=null && res.getSuccess()){
                return res.getData();
            }
        }finally {
            log.info("RpcMkuServiceImpl.getIscMkuSaleStatus req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(res));
        }
        return null;
    }

    @Override
    public Map<Long, Long> getIscJdSkuIdByMkuId(BatchQueryMkuReqDTO req){
        DataResponse<Map<Long, Long>> res = null;
        try {
            res = iscProductSoaMkuReadApiService.getIscJdSkuIdByMkuId(req);
            if(res!=null && res.getSuccess()){
                return res.getData();
            }
        }finally {
            log.info("RpcMkuServiceImpl.getIscJdSkuIdByMkuId req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(res));
        }
        return null;
    }

    @Override
    public Map<Long, Boolean> getIscSpuSkuRelationByMkuId(BatchQueryMkuReqDTO req){
        DataResponse<Map<Long, Boolean>> res = null;
        try {
            res = iscProductSoaMkuReadApiService.getIscSpuSkuRelationByMkuId(req);
            if(res!=null && res.getSuccess()){
                return res.getData();
            }
        }finally {
            log.info("RpcMkuServiceImpl.getIscSpuSkuRelationByMkuId req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(res));
        }
        return null;
    }

    @Override
    public Map<Long, DeliveryAgingResp> getDeliveryAgingAsync(List<Long> mkuIds, String clientCode) {
        try {
            CompletableFuture<Map<Long, DeliveryAgingResp>> future = CompletableFuture
                    .supplyAsync(() -> {
                        try {
                            return this.getDeliveryAging(mkuIds, clientCode);
                        } catch (Exception e) {
                            log.warn("获取发货时常异常, mkuIds: {}, clientCode: {}", mkuIds, clientCode, e);
                            return Collections.emptyMap();
                        }
                    }, searchExecutorService);

            return future.get(100, TimeUnit.MILLISECONDS);

        } catch (TimeoutException e) {
            log.warn("获取发货时常超时200ms, mkuIds: {}, clientCode: {}", mkuIds, clientCode);
            return Collections.emptyMap();
        } catch (Exception e) {
            log.warn("获取发货时常超时或异常, mkuIds: {}, clientCode: {}", mkuIds, clientCode, e);
            return Collections.emptyMap();
        }
    }
}
