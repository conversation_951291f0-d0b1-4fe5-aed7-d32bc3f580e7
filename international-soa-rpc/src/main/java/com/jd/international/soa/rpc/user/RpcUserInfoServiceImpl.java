package com.jd.international.soa.rpc.user;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.jd.international.soa.common.exception.BizException;
import com.jd.international.soa.common.exception.ResponseErrorCode;
import com.jd.international.soa.domain.common.account.UserLoginInfoDTO;
import com.jd.jim.cli.util.MD5Utils;
import com.jd.user.sdk.export.UserPassportExportService;
import com.jd.user.sdk.export.constant.Constants;
import com.jd.user.sdk.export.domain.passport.LoginParam;
import com.jd.user.sdk.export.domain.passport.LoginResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 远程服务实现类
 *
 * <AUTHOR>
 * @since 2022/10/20 10:33
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class RpcUserInfoServiceImpl implements RpcUserInfoService {

    private final UserPassportExportService userPassportExportService;

    @Override
    public void checkLoginForCustomer(UserLoginInfoDTO dto) {
        log.info("RpcUserInfoServiceImpl.checkLoginForCustomer is running , param {}", dto);
        String md5Pwd = MD5Utils.shaHex(dto.getPwd());
        LoginParam loginParam = new LoginParam();
        loginParam.setSource("vsp");
        loginParam.setAuthType(1);
        loginParam.setLoginName(dto.getLoginName());
        loginParam.setPassword(md5Pwd);
        loginParam.setUserIp(dto.getRemoteIp());
        loginParam.setDeviceName("NONE");
        loginParam.setDeviceVersion("NONE");
        loginParam.setDeviceOS("NONE");
        loginParam.setDeviceOSVersion("NONE");
        Map<String, String> extInfos = Maps.newLinkedHashMap();
        extInfos.put(Constants.LoginParam.EQUIPMNET_ID, dto.getEid());
        extInfos.put(Constants.LoginParam.FINGER_PRINT, dto.getFp());
        extInfos.put(Constants.LoginParam.CHANNEL, "8");
        loginParam.setExtInfo(extInfos);
        log.info("开始准备模拟用户登录 , 登录参数 : {}", JSON.toJSONString(loginParam));
        try {
            LoginResult login = userPassportExportService.login(loginParam);
            if (login != null) {
                if (login.isSuccess()) {
                    log.info("登录成功");
                } else {
                    log.error("登录失败 ， 具体原因：");
                    processException(login.getResultCode());
                }
            } else {
                log.error("登录失败，登录接口返回的信息为空");
                throw new BizException(ResponseErrorCode.FAILED_TO_CALL_RPC_INTERFACE);
            }
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("RpcUserInfoServiceImpl.checkLoginForCustomer has error , information : {}", e);
            throw new BizException(ResponseErrorCode.FAILED_TO_CALL_RPC_INTERFACE);
        }

    }

    private void processException(Integer returnCode) {
        if (returnCode == null) {
            log.error("登录结果返回码为空");
            throw new BizException("10000000010", "登录结果返回码为空");
        }
        switch (returnCode) {
            case 1:
                break;
            case 2:
                log.error("用户不存在");
                throw new BizException("10000000011", "用户不存在");
            case 3:
                throw new BizException("10000000012", "密码错误超过10次pin被锁");
            case 6:
                throw new BizException("10000000013", "原密码错误");
            case 7:
                throw new BizException("10000000014", "未验证的企业用户");
            case 8:
                throw new BizException("10000000015", "账号被锁");
            case 10:
                throw new BizException("10000000016", "ept用户登录主站");
            case 11:
                throw new BizException("10000000017", "普通用户注册类型无效");
            case 12:
                throw new BizException("10000000018", "bss用户注册类型无效");
            case 13:
                throw new BizException("10000000019", "申请注销");
            case 14:
                throw new BizException("10000000020", "密码非md5格式");
            case 15:
                throw new BizException("10000000020", "密码非md5格式");
            case 16:
                throw new BizException("10000000021", "缺少必要参数");
            case 17:
                throw new BizException("10000000022", "source非法");
            case 18:
                throw new BizException("10000000023", "source为空");
            case 100:
                throw new BizException("10000000024", "系统繁忙");
            case 101:
                throw new BizException("10000000025", "alc首次无效");
            case 102:
                throw new BizException("10000000026", "alc无效次数大于1");
            case 103:
                throw new BizException("10000000027", "loginToken非法");
            case 104:
                throw new BizException("10000000028", "loginToken非法（不能正常解密）");
            case 105:
                throw new BizException("10000000029", "ip在黑名单中");
            case 106:
                throw new BizException("10000000030", "用户名为空");
            case 107:
                throw new BizException("10000000031", "设备不合法");
            case 112:
                throw new BizException("10000000032", "登录ip为空");
            case 201:
                throw new BizException("10000000033", "密码为空");
            case 202:
                throw new BizException("10000000034", "uuid为空（非设备id）");
            case 1100:
                throw new BizException("10000000035", "需要验证（命中风险检查）");
            case 1110:
                throw new BizException("10000000036", "无可用验证方式（命中风险检查）");
            default:
                throw new BizException("10000000037", "未解析异常");
        }
    }
}
