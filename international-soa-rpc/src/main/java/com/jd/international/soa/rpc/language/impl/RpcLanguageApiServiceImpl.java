package com.jd.international.soa.rpc.language.impl;

import com.alibaba.fastjson.JSONObject;
import com.jd.international.soa.rpc.language.RpcLanguageApiService;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.wimp.lang.IscProductSoaLangApiService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Description:
 * @Author: chengliwei7
 * @Date: 2024/9/25 13:14
 */
@Service
public class RpcLanguageApiServiceImpl implements RpcLanguageApiService {

    @Resource
    private IscProductSoaLangApiService iscProductSoaLangApiService;
    @Override
    public DataResponse<JSONObject> getLangLibraryByLang(String lang) {
        return iscProductSoaLangApiService.getLangLibraryByLang(lang);
    }

    @Override
    public DataResponse<Map<String, String>> getLanguageConfigByLang(String lang) {
        return iscProductSoaLangApiService.getLangMapByLang(lang);
    }
}
