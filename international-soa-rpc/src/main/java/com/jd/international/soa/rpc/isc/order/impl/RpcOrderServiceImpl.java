package com.jd.international.soa.rpc.isc.order.impl;

import com.alibaba.fastjson2.JSON;
import com.jd.international.soa.base.bean.RPCResult;
import com.jd.international.soa.common.exception.BizException;
import com.jd.international.soa.common.exception.ResponseErrorCode;
import com.jd.international.soa.rpc.isc.order.RpcOrderService;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.wisp.api.order.OrderReadApiService;
import com.jdi.isc.aggregate.read.wisp.api.order.resp.OrderConsigneeInfoReadResp;
import com.jdi.isc.order.center.api.OrderWriteApiService;
import com.jdi.isc.order.center.api.biz.req.SubmitOrderApiReq;
import com.jdi.isc.order.center.api.biz.resp.OrderInfoOrderApiResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Set;


/**
 * 订单中心服务
 * <AUTHOR>
 * @date 2024/2/4
 **/
@Slf4j
@Service
public class RpcOrderServiceImpl implements RpcOrderService {

    @Resource
    private OrderWriteApiService writeApiService;
    @Resource
    private OrderReadApiService orderReadApiService;

    @Override
    public RPCResult<OrderInfoOrderApiResp> submitOrder(SubmitOrderApiReq submitOrderReqDTO) {
        log.info("RpcOrderServiceImpl.submitOrder 入参:[submitOrderReqDTO={}]", JSON.toJSONString(submitOrderReqDTO));
        DataResponse<OrderInfoOrderApiResp> dataResponse = writeApiService.submitOrder(submitOrderReqDTO);
        log.info("RpcOrderServiceImpl.submitOrder 出参:[dataResponse={}]", JSON.toJSONString(dataResponse));
        if (Boolean.FALSE.equals(dataResponse.getSuccess()) || Objects.isNull(dataResponse.getData())) {
            RPCResult<OrderInfoOrderApiResp> rpcResult = new RPCResult<>();
            rpcResult.setCode(dataResponse.getCode());
            rpcResult.setMessage(dataResponse.getMessage());
            return rpcResult;
        }
        return RPCResult.success(dataResponse.getData());
    }

}
