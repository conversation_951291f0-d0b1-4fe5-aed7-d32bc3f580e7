package com.jd.international.soa.rpc.lang;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.wimp.lang.IscProductSoaLangApiService;
import com.jdi.isc.product.soa.api.wimp.lang.res.LangResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 语言
 * <AUTHOR>
 * @date 2024/7/10
 */
@Service
@Slf4j
public class LangRpcService {

    @Resource
    private IscProductSoaLangApiService iscProductSoaLangApiService;

    /**
     * 查询语言
     * */
    public List<LangResDTO> queryList(){
        List<LangResDTO> result = null;
        try {
            DataResponse<List<LangResDTO>> res = iscProductSoaLangApiService.queryList();
            result = res.getData();
        } finally {
            log.info("LangRpcService.queryList res:{}" , JSON.toJSONString(result));
        }
        return result;
    }

    /**
     * 查询语言编码
     * */
    public List<String> getLangCodeList(){
        List<String> result = null;
        try {
            DataResponse<List<String>> res = iscProductSoaLangApiService.getLangCodeList();
            result = res.getData();
        } finally {
            log.info("LangRpcService.getLangCodeList res:{}" , JSON.toJSONString(result));
        }
        return result;
    }

    /**
     * 查询语言
     * */
    public Map<String, String> getLangMap(){
        Map<String, String> result = null;
        try {
            DataResponse<Map<String, String>> res = iscProductSoaLangApiService.getLangMap();
            result = res.getData();
        } finally {
            log.info("LangRpcService.getLangMap res:{}" , JSON.toJSONString(result));
        }
        return result;
    }

    /**
     * 查询语言名称
     * */
    public String getLangNameByLangCode(String langCode){
        String result = null;
        try {
            DataResponse<String> res = iscProductSoaLangApiService.getLangNameByLangCode(langCode);
            result = res.getData();
        } finally {
            log.info("LangRpcService.getLangNameByLangCode res:{}" , result);
        }
        return result;
    }
}
