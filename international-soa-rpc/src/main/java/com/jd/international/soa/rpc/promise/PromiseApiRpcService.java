package com.jd.international.soa.rpc.promise;

import com.alibaba.fastjson.JSON;
import com.jd.jp.strategy.sdk.dto.*;
import com.jd.jp.strategy.sdk.service.PromiseSdkService;
import com.jd.jp.strategy.sdk.vo.RpcResult;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.wisp.api.promise.PromiseApiService;
import com.jdi.isc.aggregate.read.wisp.api.promise.req.SkuDetailedPageInfoApiReq;
import com.jdi.isc.aggregate.read.wisp.api.promise.res.SkuBizDetailApiResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 履约时效查询接口
 */
@Service
@Slf4j
public class PromiseApiRpcService {

    @Resource
    private PromiseApiService promiseApiService;

    @Resource
    private PromiseSdkService promiseSdkService;

    private final static Integer DEFAULT_REQ_SOURCE = 17;

//    https://cf.jd.com/pages/viewpage.action?pageId=766926862

    //获取商详页货期信息(商详页话术)
    public SkuDetailedPageInfoResp getSkuDetailedPageInfo(SkuDetailedPageInfoReq input){
        RpcResult<SkuDetailedPageInfoResp> res = null;
        try {
            res = promiseSdkService.getSkuDetailedPageInfo(input);
            if(res.isSuccess()){
                return res.getData();
            }
        }finally {
            log.info("PromiseRpcService.getSkuDetailedPageInfo req:{} , res:{}" , JSON.toJSONString(input), JSON.toJSONString(res));
        }
        return null;
    }

    //获取商详页货期信息(商详页话术)
    public Map<Long, SkuPromsieResp> batchGetSkuPromise(SkuPromiseReq req){

        RpcResult<List<SkuPromsieResp>> res = null;
        try {
            res = promiseSdkService.batchGetSkuPromise(req);
            if(res.isSuccess()){
                return res.getData().stream().collect(Collectors.toMap(SkuPromsieResp::getSkuId, Function.identity()));
            }
        }finally {
            log.info("PromiseRpcService.batchGetSkuPromise req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(res));
        }
        return null;
    }

    /**
     * 处理承诺信息并获取商品详细页信息。
     * @param jdSkuId 京东商品ID。
     * @param num 数量。
     * @param address 收货地址。
     * @param iopPin IOP PIN码。
     * @return SkuDetailedPageInfoResp 对象，包含商品详细页信息。
     */
    public SkuDetailedPageInfoResp processPromiseInfo(Long jdSkuId, Integer num, Address address, String iopPin){
        SkuDetailedPageInfoReq target = new SkuDetailedPageInfoReq();
        target.setSkuId(jdSkuId);
        target.setAddress(address);
        target.setReqSource(DEFAULT_REQ_SOURCE);
        target.setPin(iopPin);
        target.setNum(num);
        return this.getSkuDetailedPageInfo(target);
    }


    public SkuBizDetailApiResp getSkuDetailedPageInfo(SkuDetailedPageInfoApiReq req){
        DataResponse<SkuBizDetailApiResp> res = null;
        try {
            res = promiseApiService.getSkuBizInfo(req);
            if(res.getSuccess()){
                return res.getData();
            }
        }finally {
            log.info("PromiseApiRpcService.getSkuDetailedPageInfo req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(res));
        }
        return null;
    }

}
