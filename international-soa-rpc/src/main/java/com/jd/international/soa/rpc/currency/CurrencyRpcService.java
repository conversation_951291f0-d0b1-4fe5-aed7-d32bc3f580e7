package com.jd.international.soa.rpc.currency;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.wimp.currency.IscProductSoaCurrencyApiService;
import com.jdi.isc.product.soa.api.wimp.currency.res.CurrencyResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 语言
 * <AUTHOR>
 * @date 2024/8/16
 */
@Service
@Slf4j
public class CurrencyRpcService {

    @Resource
    private IscProductSoaCurrencyApiService iscProductSoaCurrencyApiService;

    /**
     * 查询币种
     * */
    public List<CurrencyResDTO> queryList(String lang){
        List<CurrencyResDTO> result = null;
        try {
            DataResponse<List<CurrencyResDTO>> res = iscProductSoaCurrencyApiService.queryListByLang(lang);
            result = res.getData();
        } finally {
            log.info("CurrencyRpcService.queryList res:{}" , JSON.toJSONString(result));
        }
        return result;
    }

    /**
     * 查询币种编码
     * */
    public List<String> getCurrencyCodeList(){
        List<String> result = null;
        try {
            DataResponse<List<String>> res = iscProductSoaCurrencyApiService.getCurrencyCodeList();
            result = res.getData();
        } finally {
            log.info("CurrencyRpcService.getCurrencyCodeList res:{}" , JSON.toJSONString(result));
        }
        return result;
    }

    /**
     * 查询币种
     * */
    public Map<String,String> getCurrencyMap(String lang){
        Map<String,String> result = null;
        try {
            DataResponse<Map<String,String>> res = iscProductSoaCurrencyApiService.getCurrencyMapByLang(lang);
            result = res.getData();
        } finally {
            log.info("CurrencyRpcService.getCurrencyMap res:{}" , JSON.toJSONString(result));
        }
        return result;
    }

    /**
     * 获取所有货币的符号映射
     * @return key为货币代码，value为货币符号的Map对象
     */
    public Map<String,String> getCurrencySymbol() {
        List<CurrencyResDTO> currencyResDTOS = this.queryList(LangConstant.LANG_EN);
        Map<String,String> res = new HashMap<>();
        for (CurrencyResDTO currencyResDTO : currencyResDTOS) {
            res.put(currencyResDTO.getCurrencyCode(),currencyResDTO.getSymbol());
        }
        return res;
    }
}
