package com.jd.international.soa.rpc.attribute;

import com.alibaba.fastjson.JSON;
import com.jd.international.soa.common.constants.UmpKeyConstant;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.product.soa.api.attribute.IscAttributeReadApiService;
import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class AttributeRpcService {

    @Resource
    private IscAttributeReadApiService iscAttributeReadApiService;

    @Value("${spring.profiles.active}")
    private String systemProfile;

    public List<PropertyApiDTO> getCategorySaleAttributesByJdCatId(Long catId, String lang) {
        List<PropertyApiDTO> result = new ArrayList<>();
        try {
            log.info("AttributeRpcServiceImpl.getCategorySaleAttributesByJdCatId categoryId:{},lang:{}", catId,lang);
            DataResponse<List<PropertyApiDTO>> resp = iscAttributeReadApiService.getCategorySaleAttributesByJdCatId(catId,lang);
            if(resp.getSuccess()){
                result = resp.getData();
            }
        }catch (Exception e){
            log.error("【系统异常】AttributeRpcServiceImpl.getCategorySaleAttributesByJdCatId error:{}", e.getMessage(), e);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_WARNING, String.format("【%s】 %s 获取销售属性值多语报错, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , e.getMessage()));
        } finally {
            log.info("AttributeRpcServiceImpl.getCategorySaleAttributesByJdCatId res:{}" , JSON.toJSONString(result));
        }
        return result;
    }

}
