<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jd.international.soa</groupId>
        <artifactId>international-soa</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>international-soa-rpc</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-soa-domain</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-soa-dao</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--主站商祥接口-->
        <dependency>
            <groupId>gms.jd.component.crs.proxy</groupId>
            <artifactId>component-crs-proxy-rpc</artifactId>
            <version>${component-crs-proxy-rpc.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>user-soa-sdk</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jd.jdi</groupId>
            <artifactId>jdi-product-center-core-boost-sdk</artifactId>
            <version>${jdi-product-center-core-boost-sdk.version}</version>
        </dependency>
        <!-- 促销接口 -->
        <dependency>
            <groupId>com.jd.b2b.promotion</groupId>
            <artifactId>b2b-promotion-engine-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.b2b.price</groupId>
            <artifactId>b2b-price-common</artifactId>
        </dependency>
        <!-- 价格平台 -->
        <dependency>
            <groupId>com.jd.b2b.price</groupId>
            <artifactId>b2b-price-biz-sdk</artifactId>
        </dependency>
        <!-- 用户组接口-->
        <dependency>
            <groupId>ka-user-soa-service</groupId>
            <artifactId>ka-user-soa-service</artifactId>
            <version>${ka.user.soa.service.version}</version>
        </dependency>
        <!--工鼎万方-->
        <dependency>
            <groupId>com.jd.wanfang</groupId>
            <artifactId>wanfang-support-soa-client</artifactId>
            <version>${wanfang.version}</version>
        </dependency>
        <!-- 类目系统包 -->
        <dependency>
            <groupId>jd.gms</groupId>
            <artifactId>category-soa-dubbo-client</artifactId>
            <version>${category.soa.dubbo.client.version}</version>
        </dependency>
        <!-- 商品组公共包 -->
        <dependency>
            <groupId>jd.gms</groupId>
            <artifactId>gms-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.b2b</groupId>
            <artifactId>b2b-user-svr-sdk</artifactId>
            <version>${b2b-user-svr-sdk.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.jd.pap.jprice</groupId>-->
<!--            <artifactId>jprice-sdk-module</artifactId>-->
<!--        </dependency>-->
        <!-- 促销服务 -->
        <dependency>
            <groupId>com.jd.promotion</groupId>
            <artifactId>promotion-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.international.ware.center</groupId>
            <artifactId>international-ware-center-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <!--			iop订单服务-->
        <dependency>
            <groupId>com.jd.ka.gpt.soa</groupId>
            <artifactId>gpt-soa-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.wanfang</groupId>
                    <artifactId>wanfang-support-soa-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.annotation</groupId>
                    <artifactId>jsr250-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd</groupId>
                    <artifactId>jsf-lite</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--			ept订单服务-->
        <dependency>
            <groupId>com.jd.ept</groupId>
            <artifactId>ept-order-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.aggregate.read</groupId>
            <artifactId>jdi-isc-aggregate-read-wisp-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.order.center</groupId>
            <artifactId>jdi-isc-order-center-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.aggregate.read</groupId>
            <artifactId>jdi-isc-aggregate-read-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.xbp</groupId>
            <artifactId>xbp-jmq4-data</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.xbp</groupId>
            <artifactId>jsf-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.product.soa</groupId>
            <artifactId>jdi-isc-product-soa-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.product.soa</groupId>
            <artifactId>jdi-isc-product-soa-price-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.product.soa</groupId>
            <artifactId>jdi-isc-product-soa-stock-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.jp</groupId>
            <artifactId>strategy-center-sdk</artifactId>
        </dependency>

    </dependencies>
</project>