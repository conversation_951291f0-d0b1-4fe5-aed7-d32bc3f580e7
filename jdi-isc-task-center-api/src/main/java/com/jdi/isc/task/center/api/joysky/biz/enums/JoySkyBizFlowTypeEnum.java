package com.jdi.isc.task.center.api.joysky.biz.enums;

import lombok.Getter;
import lombok.ToString;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/11/4
 * @Description 国际业务审批流枚举配置类
 */
@Getter
@ToString
public enum JoySkyBizFlowTypeEnum {

    /**
     * 审批流测试流程(测试流程连通性)，非业务使用
     */
    UAT_APPLY_TEST_FLOW(0, new HashMap<String, String>() {{
        put(UAT_ENV, "process1730707552315");
        put(PROD_ENV, "");
    }},
            "审批流测试流程"),

    /**
     * 商品销售价审批流
     */
    PRODUCT_SALE_PRICE_FLOW(101, new HashMap<String, String>() {{
        put(UAT_ENV, "process1730357535848");
        put(PROD_ENV, "");
    }},
            "商品销售价审批流");

    /**
     * 流程类型编码，用户根据规则自定义，用于统一管理国际业务审批流
     * 各端编码规则：
     * -WIMP: 1xx
     * -WISP: 2xx
     * -WIVC: 3xx
     * -集运中心：4xx
     * -其他：5xx
     */
    public final Integer flowTypeCode;

    /**
     * joySky平台的流程key，例如：process1730268963448
     */
    public final Map<String, String> processKeyMap;

    /**
     * 审批流流程名称，简短概括描述出审批流的任务概要
     */
    public final String processTitle;

    /**
     * 构造一个 JoySkyBizFlowTypeEnum 对象。
     *
     * @param flowTypeCode  流程类型代码
     * @param processKeyMap 流程键集合
     * @param processTitle  流程名称
     */
    JoySkyBizFlowTypeEnum(Integer flowTypeCode, Map<String, String> processKeyMap, String processTitle) {
        this.flowTypeCode = flowTypeCode;
        this.processKeyMap = processKeyMap;
        this.processTitle = processTitle;
    }

    /**
     * 定义UAT环境的常量字符串。
     */
    public static final String UAT_ENV = "uat";

    /**
     * 定义生产环境的常量字符串。
     */
    public static final String PROD_ENV = "prod";

    /**
     * 根据流程类型代码获取对应的 JoySkyBizFlowTypeEnum 枚举值。
     *
     * @param flowTypeCode 流程类型代码
     * @return 对应的 JoySkyBizFlowTypeEnum 枚举值
     * @throws IllegalArgumentException 如果没有找到与给定流程类型代码匹配的枚举值
     */
    public static JoySkyBizFlowTypeEnum getByFlowTypeCode(Integer flowTypeCode) {
        for (JoySkyBizFlowTypeEnum type : JoySkyBizFlowTypeEnum.values()) {
            if (type.getFlowTypeCode().equals(flowTypeCode)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with flowTypeCode " + flowTypeCode);
    }

    /**
     * 根据流程键获取对应的业务流程类型枚举。
     *
     * @param processKey 流程键。
     * @return 对应的业务流程类型枚举。
     */
    public static JoySkyBizFlowTypeEnum getByProcessKey(String processKey) {
        for (JoySkyBizFlowTypeEnum type : JoySkyBizFlowTypeEnum.values()) {
            if (type.getProcessKeyMap().containsValue(processKey)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with processKey " + processKey);
    }

    /**
     * 获取所有业务流程的键值集合。
     *
     * @return 业务流程键值集合。
     */
    public static Set<String> getAllProcessKeys() {
        Set<String> processKeys = new HashSet<>();
        for (JoySkyBizFlowTypeEnum type : JoySkyBizFlowTypeEnum.values()) {
            processKeys.addAll(type.getProcessKeyMap().values());
        }
        return processKeys;
    }

    /**
     * 根据环境获取业务流程的过程键。
     *
     * @param env 环境名称。
     * @return 对应环境的业务流程的过程键。
     */
    public static String getProcessKeyByEnv(Integer flowTypeCode, String env) {
        for (JoySkyBizFlowTypeEnum type : JoySkyBizFlowTypeEnum.values()) {
            if (Objects.equals(type.getFlowTypeCode(), flowTypeCode) && type.getProcessKeyMap().containsKey(env)) {
                return type.getProcessKeyMap().get(env);
            }
        }
        throw new IllegalArgumentException("No process key found for environment " + env);
    }

}
