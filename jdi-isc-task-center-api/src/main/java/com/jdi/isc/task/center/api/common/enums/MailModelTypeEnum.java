package com.jdi.isc.task.center.api.common.enums;

import java.util.Arrays;
import java.util.Optional;

public enum MailModelTypeEnum {

    /**
     * 采购单确认邮件模板类型。
     */
    PURCHASE_CONFIRM("purchaseConfirm","采购单确认"),
    /**
     * 采购单发货邮件模板类型。
     */
    PURCHASE_SHIP("purchaseShip","采购单发货"),
    /**
     * 采购单入仓邮件模板类型。
     */
    PURCHASE_ENTRY_WAREHOUSE("purchaseEntryWarehouse","采购单入仓"),
    /**
     * 采购单完成邮件模板类型。
     */
    PURCHASE_FINISH("purchaseFinish","采购单完成"),
    /**
     * 通知出账的邮件模板类型。
     */
    BILLING_NOTIFICATION("billingNotification","通知出账"),
    /**
     * 发票驳回邮件模板类型。
     */
    INVOICE_REJECTION("invoiceRejection","发票驳回"),
    /**
     * 结算单创建的邮件模板类型。
     */
    SETTLEMENT_CREATE("settlementCreate","结算单创建"),

    /**
     * 商品sku定制价格的邮件模板类型。
     */
    PRODUCT_CUSTOMER_SKU_AUDIT("productCustomerSkuAudit","商品sku定制价格"),

    /**
     * 供应商密码审核通过的邮件模板类型。
     */
    SUPPLIER_PWD("supplierPwd","供应商密码审核通过"),

    /**
     * 结算单创建的邮件模板类型。
     */
    PURCHASE_ORDER_PRICE_CHANGE("purchaseOrderPriceChange","采购单价格变更"),

    /**
     * 商品库存预警的邮件模板类型。
     */
    PRODUCT_STOCK_THRESHOLD_ALERT("productStockThresholdAlert","商品库存预警"),

    /**
     * 商品库存无货预警的邮件模板类型。
     */
    PRODUCT_OUT_STOCK_ALERT("productOutStockAlert","商品无货预警"),

    /*
    给各国供应链运营的发送订单类型的邮件模板类型。victor add 2025/08/20
     */
    SEND_ORDER_STATUS_NOTICE("SendOrderStatusNotice","客户认订单后状态提醒"),

    ;






    /**
     * 邮件模板类型的唯一标识符。
     */
    private String code;

    /**
     * 邮件模板类型的描述信息。
     */
    private String desc;

    MailModelTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean checkCode(String modelType) {
        if (null == modelType) {
            return false;
        }

        Optional<MailModelTypeEnum> typeEnum = Arrays.stream(values()).filter(a -> a.getCode().equals(modelType)).findFirst();
        return typeEnum.isPresent();
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}