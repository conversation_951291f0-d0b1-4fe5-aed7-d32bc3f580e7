package com.jdi.isc.task.center.api.joysky.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/10/31
 * @Description joySky审批流创建申请参数信息
 */
@Data
public class JoySkyCreateReqApiDTO implements Serializable {

    private static final long serialVersionUID = 6188661356757544836L;

    /**
     * 审批流业务类型
     * 由业务方自行按照规则进行定义，具体定义方式见：
     *
     * @see com.jdi.isc.task.center.api.joysky.biz.enums.JoySkyBizFlowTypeEnum
     */
    @NotBlank(message = "流程类型不能为空")
    private Integer joySkyFlowType;

    /**
     * 审批流发起人ERP
     */
    @NotBlank(message = "创建人不能为空")
    private String erp;

    /**
     * 审批流流程表单数据
     * key: 表单字段，value: 字段值
     */
    @NotEmpty
    private Map<String, String> processFromData;

    /**
     * 审批流流程控制变量
     * key: 流程变量名称， value: 流程变量值
     */
    private Map<String, Object> processControlData;

}
