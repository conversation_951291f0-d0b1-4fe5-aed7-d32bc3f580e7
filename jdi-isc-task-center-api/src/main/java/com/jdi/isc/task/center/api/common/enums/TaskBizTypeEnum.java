package com.jdi.isc.task.center.api.common.enums;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务类型枚举
 * 10000-40000商品  40001-70000   70001-100000
 */
public enum TaskBizTypeEnum {
    NONE(-1,1,"空任务"),
    CATE_BATCH_PUBLISH(0, 5000,"类目批量新增"),
    EXT_ATTRIBUTE_BATCH_PUBLISH(1, 5000,"扩展属性批量新增"),
    SALE_ATTRIBUTE_BATCH_PUBLISH(2,5000, "销售属性批量新增"),
    CATE_EXT_ATTRIBUTE_BATCH_PUBLISH(3,5000, "类目扩展属性批量新增"),
    CATE_SALE_ATTRIBUTE_BATCH_PUBLISH(4, 5000,"类目销售属性批量新增"),
    CH_PRODUCT_BATCH_PUBLISH(5, 1000,"跨境商品批量新增"),
    VN_PRODUCT_BATCH_PUBLISH(6, 1000,"越南商品批量新增"),
    PRODUCT_BATCH_DOWNLOAD(7, -1,"商品批量下载"),
    SKU_CUSTOM_PRICE_BATCH_MODIFY(8, 5000,"SKU客制化价格批量修改"),
    SKU_CUSTOM_PRICE_BATCH_DOWNLOAD(9, -1,"SKU客制化价格批量下载"),
    LOCAL_SKU_STOCK_BATCH_MODIFY(10, 5000,"本土SKU库存批量修改"),
    LOCAL_SKU_STOCK_BATCH_DOWNLOAD(11, -1,"本土SKU库存批量下载"),
    MKU_DETAIL_BATCH_DOWNLOAD(12, -1,"MKU详情批量下载"),
    MKU_CUSTOMER_REF_BATCH_DOWNLOAD(13, -1,"MKU客户关系批量下载"),
    MKU_CUSTOMER_PRICE_BATCH_DOWNLOAD(14, -1,"MKU客制化价格批量下载"),
    MKU_CUSTOMER_SKU_BATCH_BIND(15, 5000,"客户MKU绑定SKU"),
    MKU_CUSTOMER_BATCH_BIND(16, 5000,"客户绑定MKU"),
    MKU_PROMISE_PREDICT_DAY_BATCH_IMPORT(17, 5000,"MKU设置配送时长"),
    SKU_BASE_BATCH_MODIFY(18, 5000,"批量更新SKU基本信息"),
    //https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/pmethctab1/批量新增泰国HSCODE税率模板.xlsx
    TH_HSCODE_TAX_BATCH_ADD(19, 5000,"批量新增泰国HSCODE税率"),
    //https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/pmethctab1/批量新增泰国商品税率模版.xlsx
    TH_SKU_TAX_BATCH_ADD(20, 5000,"批量新增泰国商品税率"),
    //https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/pmethctab1/批量更新泰国商品关税率模版.xlsx
    TH_SKU_TAX_BATCH_MODIFY(21, 5000,"批量更新泰国商品关税率"),
    //https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/pmethctab1/%E6%89%B9%E9%87%8F%E6%96%B0%E5%A2%9E%E6%9B%B4%E6%96%B0%E8%AE%A2%E5%8D%95%E5%90%8C%E6%AD%A5%E8%B4%A2%E5%8A%A1%E5%8A%A0%E4%BB%B7%E8%A1%A8.xlsx
    ORDER_FINANCE_BATCH_PUBLISH(22, 5000,"批量新增更新核算加价表"),
    //https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/pmethctab1/%E6%B3%B0%E5%9B%BD%E5%95%86%E5%93%81%E9%9D%A2%E4%BB%B7%E8%AE%A1%E7%AE%97%E6%A8%A1%E6%9D%BF.xlsx
    SKU_PRICE_TOOL_BATCH(23, 1000,"批量计算SKU公域面价"),
    VN_CATEGORY_RATE_BATCH(24, 1000,"批量新增越南类目税率"),
    SKU_PRICE_CUSTOMER_TOOL_BATCH(25, 1000,"批量计算SKU客户未税销售价"),
    SKU_EXCLUDE_TAX_PRICE_BATCH(26, 1000,"批量设置客户SKU未税价"),
    TH_PRODUCT_BATCH_PUBLISH(27, 1000,"泰国商品批量新增"),
    TASK_EXPORT_PURCHASE_ORDER(28, 1000, "导出采购单信息"),
    TASK_EXPORT_ORDER(29, 1000, "导出订单信息"),
    TASK_EXPORT_CATEGORY(30, 1000, "导出类目信息"),
    EXPORT_ORDER_WAYBILL_PDF(31, 1000, "导出二段运单交接单"),
    EXPORT_FIRST_WAYBILL_PDF(32, 1000, "导出一段运单交接单"),
    EXPORT_MARK_PDF(33, 1000, "导出唛头"),
    ORDER_DELIVERY_IMPORT_BATCH(34, 1000, "批量新增承运人信息"),
    ORDER_DELIVERY_TRACK_IMPORT_BATCH(35, 1000, "批量新增物流轨迹"),
    VN_PRODUCT_ALL_CATEGORY_BATCH_PUBLISH(36, 1000,"越南全类目商品批量新增"),
    TH_PRODUCT_ALL_CATEGORY_BATCH_PUBLISH(37, 1000,"泰国全类目商品批量新增"),
    GLOBAL_ATTRIBUTE(38, 5000,"跨境属性更新模板"),
    MKU_MATERIAL_BATCH_PUBLISH(39, 1000,"MKU物料管理批量新增"),
    MKU_MATERIAL_BATCH_DELETE(40, 1000,"MKU物料管理批量删除"),
    MKU_MATERIAL_BATCH_EXPORT(41, 1000,"MKU物料管理批量导出"),
    SKU_STOCK_BATCH_IMPORT(42, 1000, "批量更新商品库存"),
    SKU_AMEND_BATCH_UPDATE_INT(43, 1000,"国际商品批量修改"),
    SKU_AMEND_BATCH_UPDATE_CN(44, 1000,"中国商品批量修改"),
    CN_PRODUCT_AMEND(45,1000,"中国商品修改模版"),
    INT_PRODUCT_AMEND(46,1000,"国际商品修改模版"),

    CN_CREATE_PRODUCT_BATCH_IMPORT(47,1000,"中国商品创建模版"),
    INT_CREATE_PRODUCT_BATCH_IMPORT(48,1000,"国际商品创建模版"),

    ORDER_OUT_VERIFY_BATCH_EXPORT(49, 1000,"订单履约信息批量导出"),
    TASK_EXPORT_STOCK_UP_ORDER(51, 1000, "导出备货订单信息"),
//    WISP_MATERIAL_BATCH_ADD(52, 1000,"批量新增物料模板"),
    WISP_BATCH_CONFIG(53, 1000, "批量配置枚举值"),
    WIMP_PROFIT_RATE(54, 5000, "批量利润率阈值"),
    WIMP_FULFILLMENT_RATE(55, 2000, "批量履约费率"),
    WIMP_CUSTOMER_SKU_CALCULATE(56, 1000, "批量试算sku客制化价格"),
    WIMP_EXPORT_TAX_RATE_IMPORT(57,20000,"批量导入出口税率"),
    WIMP_BR_CUSTOMER_PRICE_IMPORT(58,1000,"批量导入巴西客制化价格"),
    TASK_UPDATE_ORDER_THIRD_ORDER_ID(52, 1000, "修改客户内部采购单号"),
    BUSINESS_LINE_BATCH_APPEND(61, 1000,"批量新增供应商产品线"),

    INT_CREATE_PRE_PRODUCT_BATCH_IMPORT(62,5000,"国际预选池商品创建模版"),
    SKU_STOCK_THRESHOLD_IMPORT(63,5000,"SKU库存批量更新"),
    COUNTRY_MKU_BATCH_EXPORT(64,5000,"国家池商品明细导出"),

    COUNTRY_MARKUP_RATE_IMPORT(65,5000,"国家协议加价率导入"),

    PRODUCT_IDENTIFY(71, 2000, "批量识别商品类目品牌信息"),
    PRODUCT_TRANSFER_CATEGORY(72, 10000, "批量SPU类目迁移"),
    CLIENT_CATEGORY_RELATION(73, 5000, "批量导入客户类目关系"),
    SKU_WAREHOUSE_BIND(74, 5000, "批量SKU绑定仓库"),

    PRODUCT_IDENTIFY_RPC(75, 2000, "批量识别商品类目品牌信息-RPC"),

    CUSTOMS_CONTRACT_EXPORT(81, 2000, "报关总合同导出"),

    CUSTOMS_INVOICE_EXPORT(82, 2000, "报关总发票导出"),

    CUSTOMS_PACKING_LIST_EXPORT(83, 2000, "报关总装箱单导出"),

    CUSTOMS_DECLARATION_DRAFT_EXPORT(84, 2000, "报关草单导出"),

    ORDER_PALLET_RECORD_IMPORT(85, 1000, "订单装托记录导入"),

    ORDER_INVOICE_URL_EXPORT(86, 1000, "订单开票信息导出"),

    CATEGORY_LANG_BATCH_IMPORT(101, 5000,"类目名称多语言导入"),
    TASK_EXPORT_CATEGORY_BUYER(102, 5000, "导出类目采销信息"),
    CATEGORY_BUYER_BATCH_IMPORT(103, 5000,"类目采销关系导入"),

    LOCAL_ORDER_WAY_BILL_EXPORT(110, 1000, "本对本二段运单批量导出"),
    WAREHOUSE_ORDER_WAY_BILL_EXPORT(111, 1000, "备货仓二段运单批量导出"),
    CROSS_ORDER_WAY_BILL_EXPORT(112, 1000, "跨境二段运单批量导出"),
    WIMP_PRODUCT_SPECIAL_ATTR(113, -1,"商品特殊属性操作导入"),
    WIMP_PRODUCT_SPECIAL_EXPORT(114, -1,"商品特殊属性操作导出"),
    WIMP_CUSTOMER_SKU_BR_EXPORT(115,10000,"巴西sku客制化价格导出"),


    VI_HS_CODE_TAX_RATE_IMPORT(120, 10000, "越南跨境税率导入"),
    VI_HS_CODE_IMPORT_TAX_RATE_IMPORT(121, 1000, "越南进口税率导入"),
    VI_HS_CODE_RESTRICT_IMPORT(122, 1000, "越南进口限制导入"),
    ORDER_UPDATE_AND_CONFIRM_IMPORT(123, 1000,"三方订单号回填及客户审批完成"),

    TH_SKU_IMPORT_TAX_BATCH_ADD(131, 200000, "批量新增泰国商品进口税率"),
    VN_SKU_IMPORT_TAX_BATCH_ADD(132, 100000, "批量新增越南商品进口税率"),
    BR_SKU_IMPORT_TAX_BATCH_ADD(133, 100000, "批量新增巴西商品进口税率"),
    MY_SKU_IMPORT_TAX_BATCH_ADD(134, 100000, "批量新增马来商品进口税率"),
    ID_SKU_IMPORT_TAX_BATCH_ADD(135, 100000, "批量新增印尼商品进口税率"),
    HU_SKU_IMPORT_TAX_BATCH_ADD(136, 100000, "批量新增匈牙利商品进口税率"),

    UPDATE_SKU_COUNTRY_PRICE_BATCH(137, 100000, "批量更新SKU国家协议、京东价"),
    UPDATE_MKU_SUBTITLE_BATCH(138, 100000, "批量更新MKU短标题"),
    UPDATE_APPLY_FOR_SKU_COUNTRY_PRICE_BATCH(139, 100000, "批量申请更新SKU国家协议、京东价"),
    THIRD_ARCHIVE_EXPORT(140, 100000, "归档数据导出"),

    CUSTOMER_SKU_WARNING_EXPORT(141, 100000, "sku客制化价格预警导出"),
    COUNTRY_AGREEMENT_WARNING_EXPORT(142, 100000, "国家协议价预警导出"),

    UPDATE_SKU_PRICE_IMPORT(146,100000,"申请修改采购价"),

    MKU_SUBTITLE_EXPORT(143, 100000, "MKU短标题导出"),
    MKU_SUBTITLE_IMPORT(144, 100000, "MKU短标题导入"),
    PRODUCT_CUSTOMS_IMPORT(150, 100000, "商品报关信息导入"),
    PRODUCT_CUSTOMS_PAGE_EXPORT(151, 100000, "商品报关列表信息导出"),
    TAX_REFUND_RATE_IMPORT(147,100000,"历史实际退税成功率导入"),
    PRODUCT_IDENTIFY_V2(152,100000,"商品识别类目导入"),
    VIP_PRICE_LINE_AUDIT_IMPORT(148,100000,"VIP价不可售阈值导入"),
    AGREEMENT_PRICE_LINE_AUDIT_IMPORT(149,100000,"协议价不可售阈值导入"),
    ID_CATEGORY_RATE_BATCH(153, 1000,"批量新增印尼类目税率"),
    COUNTRY_POOL_TRANSLATION_EXPORT(154, -1,"国家池翻译确认导出"),
    COUNTRY_POOL_TRANSLATION_IMPORT(155, 10000,"国家池翻译确认导入"),
    ;

    private Integer code;
    /**
     * 导入最大数量,默认1000，导出-1，不设置上限
     */
    private Integer max;
    private String name;

    TaskBizTypeEnum(Integer code,Integer max,String name) {
        this.code = code;
        this.max = max;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static TaskBizTypeEnum forCode(Integer code) {
        if (code == null) {
            return null;
        }
        TaskBizTypeEnum[] values = values();
        for (TaskBizTypeEnum enumObj : values) {
            if (code.equals(enumObj.getCode())) {
                return enumObj;
            }
        }
        return null;
    }

    public static TaskBizTypeEnum forName(String name) {
        if (null == name || name.length() == 0 ) {
            return null;
        }
        TaskBizTypeEnum[] values = values();
        for (TaskBizTypeEnum enumObj : values) {
            if (name.equals(enumObj.getName())) {
                return enumObj;
            }
        }
        return null;
    }


    public static Set<TaskBizTypeEnum> bizTypeEnumSet() {
        TaskBizTypeEnum[] values = TaskBizTypeEnum.values();
        return Arrays.stream(values).collect(Collectors.toSet());
    }

    private static final Map<Integer, String> codeNameMap = new HashMap<Integer, String>() {{
        for (TaskBizTypeEnum e : TaskBizTypeEnum.values()) {
            put(e.getCode(), e.getName());
        }
    }};

    public static String getNameByType(Integer taskType) {
        return codeNameMap.get(taskType);
    }
}
