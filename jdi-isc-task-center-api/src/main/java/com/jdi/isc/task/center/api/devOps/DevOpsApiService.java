package com.jdi.isc.task.center.api.devOps;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.task.center.api.dto.task.TaskDTO;

/**
 * <AUTHOR>
 * @date 2024/6/14
 **/
public interface DevOpsApiService {

    DataResponse<String> updateExportTask(TaskDTO taskDTO);


    DataResponse<String> addSupplierTask(TaskDTO taskDTO);

    DataResponse<String> addWimpTask(TaskDTO taskDTO);

    DataResponse<String> getAllCate();
}
