package com.jdi.isc.task.center.api.joysky;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.task.center.api.joysky.req.JoySkyCreateReqApiDTO;
import com.jdi.isc.task.center.api.joysky.res.JoySkyCreateRspApiDTO;

/**
 * <AUTHOR>
 * @Date 2024/10/31
 * @Description joySky审批流业务接口
 */
public interface JoySkyAppFlowApiService {

    /**
     * 创建joySky审批流程
     *
     * @param joySkyCreateReqApiDTO 创建请求数据
     * @return 创建响应数据
     */
    DataResponse<JoySkyCreateRspApiDTO> createSkyApprovalFlow(JoySkyCreateReqApiDTO joySkyCreateReqApiDTO);

}
