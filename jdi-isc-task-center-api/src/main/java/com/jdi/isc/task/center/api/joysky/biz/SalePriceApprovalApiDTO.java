package com.jdi.isc.task.center.api.joysky.biz;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/11/1
 * @Description 销售价审批流参数类
 */

@Data
@NoArgsConstructor
public class SalePriceApprovalApiDTO {

    /**
     * 国际SKU标识。
     */
    private String internationalSkuId;

    /**
     * 商品名称。
     */
    private String productName;

    /**
     * 采购价。
     */
    private BigDecimal purchasePrice;

    /**
     * 采购价币种
     */
    private String purchasePriceCurrency;

    /**
     * 客制化销售价。
     */
    private BigDecimal customizedSalePrice;

    /**
     * 客制化销售价币种
     */
    private String customizedSalePriceCurrency;

    /**
     * 面客未税价。
     */
    private BigDecimal exTaxPrice;

    /**
     * 面客未税价币种
     */
    private String exTaxPriceCurrency;

    /**
     * 利润率。
     */
    private BigDecimal profitMargin;

    /**
     * 较低协议价
     */
    private BigDecimal lowerAgreementPrice;

    /**
     * 较低协议价币种
     */
    private String lowerAgreementPriceCurrency;

    /**
     * 提交人。
     */
    private String applyErp;

    /**
     * 当前原客制化销售价。
     */
    private BigDecimal currentCustomizedSalePrice;

    /**
     * 当前原客制化销售价币种
     */
    private String currentCustomizedSalePriceCurrency;

    /**
     * 利润率预警信息
     */
    private String profitMarginWarning;

    /*---------------流程计算参数----------------*/
    /**
     * 商品国别。
     */
    private String productCountry;

    /**
     * 客户国别。
     */
    private String customerCountry;

    /**
     * 利润率阈值。
     */
    private BigDecimal profitMarginThreshold;

    /**
     * 超低利润率阈值。
     */
    private BigDecimal lowerProfitMarginThreshold;

}
