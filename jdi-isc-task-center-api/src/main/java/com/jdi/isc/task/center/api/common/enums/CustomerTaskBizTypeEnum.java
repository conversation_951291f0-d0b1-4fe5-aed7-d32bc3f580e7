package com.jdi.isc.task.center.api.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/08/13
 **/
@Getter
@AllArgsConstructor
public enum CustomerTaskBizTypeEnum {
    NONE(-1,1,"空任务"),
    IMPORT_MATERIAL(300,10000,"物料导入"),
    ;

    private Integer code;
    /**
     * 导入最大数量,默认1000，导出-1，不设置上限
     */
    private Integer max;
    private String name;


    public static CustomerTaskBizTypeEnum forCode(Integer code) {
        if (code == null) {
            return null;
        }
        CustomerTaskBizTypeEnum[] values = values();
        for (CustomerTaskBizTypeEnum enumObj : values) {
            if (code.equals(enumObj.getCode())) {
                return enumObj;
            }
        }
        return null;
    }

    public static CustomerTaskBizTypeEnum forName(String name) {
        if (null == name || name.length() == 0 ) {
            return null;
        }
        CustomerTaskBizTypeEnum[] values = values();
        for (CustomerTaskBizTypeEnum enumObj : values) {
            if (name.equals(enumObj.getName())) {
                return enumObj;
            }
        }
        return null;
    }
}
