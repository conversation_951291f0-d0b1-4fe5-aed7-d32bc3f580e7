package com.jd.international.soa.domain.promise;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 货期时效信息
 * <AUTHOR>
 * @date 2024/6/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PromiseInfo {

    /** mku&配送时效Map*/
    Map<Long, Integer> deliveryDateMap;

    /** 时效多语言模板*/
    DeliveryTemplate deliveryTemplate;

}
