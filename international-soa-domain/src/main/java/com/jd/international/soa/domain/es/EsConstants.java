package com.jd.international.soa.domain.es;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2024/06/24
 */
@Component
public class EsConstants {

    /**
     * 商品基础信息索引命名
     * */
    public static final String PRODUCT_INDEX = "jdi_isc_mku";


    public static String getMkuEsName(String countryCode) {
        if(StringUtils.isNotBlank(countryCode)){
            return PRODUCT_INDEX + "_" + countryCode.toLowerCase();
        }
        return PRODUCT_INDEX;
    }

}