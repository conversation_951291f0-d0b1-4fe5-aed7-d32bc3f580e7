package com.jd.international.soa.domain.promise;


import lombok.Data;

/**
 * <AUTHOR>
 * @description：JdSkuDetailPromiseReqVO
 * @Date 2024-11-25
 */
@Data
public class JdSkuDetailPromiseReqVO {
    /** 国内商品id*/
    private Long jdSkuId;
    /** 商品数量*/
    private Integer num;
    /** 客户编码*/
    private String clientCode;
    /** 站点类型  1跨境,0本土*/
    private Integer stationType;
    /** 语种*/
    private String lang;
    /** 合同号*/
    private String contractNum;

    public JdSkuDetailPromiseReqVO() {
    }

    public JdSkuDetailPromiseReqVO(Long jdSkuId, Integer num, String clientCode) {
        this.jdSkuId = jdSkuId;
        this.num = num;
        this.clientCode = clientCode;
    }
}
