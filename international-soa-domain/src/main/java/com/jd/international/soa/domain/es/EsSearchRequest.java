package com.jd.international.soa.domain.es;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
@Builder
public class EsSearchRequest {
    private List<String> keywords;
    private List<Long> catIds;
    private String clientCode;
    private String indexName;
    private Integer index;
    private Integer size;
    private Set<Integer> featureTags;
    private Integer stockFlag;
}
