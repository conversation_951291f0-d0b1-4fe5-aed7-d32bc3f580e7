package com.jd.international.soa.domain.es;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;

/**
 * 索引基础类
 * <AUTHOR>
 * @date 20240627
 */
@Data
public class BaseEsPO {

    @Id
    private String id;

    /** 文档创建时间 */
    @JsonProperty("create_time")
    private Long createTime;

    /** 文档创建时间 */
    @JsonProperty("update_time")
    private Long updateTime;

    /** 数据存活状态 */
    private Integer yn;
}
