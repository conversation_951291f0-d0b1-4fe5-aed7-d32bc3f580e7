package com.jd.international.soa.domain.es;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Set;

@Data
@Accessors(chain = true)
public class ProductEsPO extends BaseEsPO {

    /** mku编码*/
    @JsonProperty("mku_id")
    private Long mkuId;

    /** 标题-中文 */
    @JsonProperty("mku_title_zh")
    private String mkuTitleZh;

    /** 标题-英文 */
    @JsonProperty("mku_title_en")
    private String mkuTitleEn;

    /** 标题-当前语种 */
    @JsonProperty("mku_title")
    private String mkuTitle;

    /** 客户简码 */
    @JsonProperty("client_code")
    private String clientCode;

    /** 合同编号 */
    @JsonProperty("contract_no")
    private String contractNo;

    /** 分类编码-全路径 */
    @JsonProperty("cat_id")
    private String catId;

    /** 分类名称-全路径 */
    @JsonProperty("cat_name")
    private String catName;

    /** 品牌id */
    @JsonProperty("brand_id")
    private Long brandId;

    /** 品牌名称-全路径 */
    @JsonProperty("brand_name")
    private String brandName;

    /** 聚合key */
    @JsonProperty("agg_key")
    private String aggKey;

    /** mku创建时间 */
    @JsonProperty("mku_create_time")
    private Long mkuCreateTime;

    @JsonProperty("feature_tags")
    private Set<Integer> featureTags;

    /**
     * 库存标记，用于标识商品是否有库存
     * 0-无库存，1-有在途库存(现货+在途), 2-有现货库存(纯现货)
     */
    @JsonProperty("has_stock")
    private Integer hasStock;

    /** 京东分类编码-全路径 */
    @JsonProperty("jd_cat_id")
    private String jdCatId;

    /** 京东分类名称-全路径 */
    @JsonProperty("jd_cat_name")
    private String jdCatName;

    /** 标题预命中分词 */
    @JsonProperty("pre_word_title")
    private String preWordTitle;

    /** 类目预命中分词 */
    @JsonProperty("pre_word_category")
    private String preWordCategory;

    /** 品牌预命中分词 */
    @JsonProperty("pre_word_brand")
    private String preWordBrand;

    /** 剩余属性-预命中分词 */
    @JsonProperty("pre_word_other")
    private String preWordOther;

    /** 新版上线前仍然需要保留字段，要不然线上查询返回后jackson报错 */
    @JsonProperty("pre_word")
    private String preWord;

    /** 新版上线前仍然需要保留字段，要不然线上查询返回后jackson报错 数值可不要 */
    @JsonProperty("mergeKey")
    private String mergeKey;
}
