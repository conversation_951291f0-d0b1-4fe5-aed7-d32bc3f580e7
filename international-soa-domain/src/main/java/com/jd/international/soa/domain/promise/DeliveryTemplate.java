package com.jd.international.soa.domain.promise;

import com.jdi.isc.product.soa.api.common.LangConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 货期文案模板信息
 * <AUTHOR>
 * @date 2024/6/7
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryTemplate {

    /** 期货货期文案模板*/
    private Delivery futureDelivery;
    /** 非期货货期文案模板*/
    private Delivery noFutureDelivery;
    /** 货期判断阈值*/
    private Integer threshold;

    public static String getByLang(Delivery delivery,String lang){
        switch (lang){
            case  LangConstant.LANG_ZH : return delivery.getZh();
            case  LangConstant.LANG_EN : return delivery.getEn();
            case  LangConstant.LANG_VN : return delivery.getVi();
            case  LangConstant.LANG_TH : return delivery.getTh();
            default: return "";
        }
    }

    @Data
    public static
    class Delivery{
        /** 中文货期模板*/
        private String zh;
        /** 英文货期模板*/
        private String en;
        /** 泰语货期模板*/
        private String th;
        /** 越南语货期模板*/
        private String vi;
    }

}
