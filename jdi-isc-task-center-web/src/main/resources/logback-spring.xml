<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- log路径 -->
<!--    <property name="LOG_PATH" value="/export/Logs/jdi-isc-task-center"/>-->
        <property name="LOG_PATH" value="~/test/jdi.igc.rpc" />

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %X{PFTID} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 默认的业务日志 -->
    <appender name="DEFAULT" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %X{PFTID} - %msg%n</pattern>
        </encoder>
        <append>false</append>
        <file>${LOG_PATH}/default.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/default.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <!-- 日志保存时间 -->
            <maxHistory>35</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- jsf服务调用日志 -->
    <appender name="JSF_INVOKE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %X{PFTID} - %msg%n</pattern>
        </encoder>
        <append>false</append>
        <file>${LOG_PATH}/jsf_invoke.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/jsf_invoke.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <!-- 日志保存时间 -->
            <maxHistory>35</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 第三方服务调用日志 -->
    <appender name="ADAPTER_INVOKE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %X{PFTID} - %msg%n</pattern>
        </encoder>
        <append>false</append>
        <file>${LOG_PATH}/adapter_invoke.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/adapter_invoke.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <!-- 日志保存时间 -->
            <maxHistory>35</maxHistory>
        </rollingPolicy>
    </appender>

    <logger name="com.jd.jsf" level="ERROR" additivity="true">
        <appender-ref ref="DEFAULT"/>
    </logger>

    <!-- Root -->
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="DEFAULT"/>
        <appender-ref ref="JSF_INVOKE"/>
        <appender-ref ref="ADAPTER_INVOKE"/>
    </root>

</configuration>