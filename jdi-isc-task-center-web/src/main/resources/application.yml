service:
  appName: jdos_jdi-isc-task-center

#  京东安全集成
server:
  servlet:
    jsp:
      class-name: com.jd.security.tomcat.JDJspServlet
      init-parameters:
        enableJsp: false
        xpoweredBy: false
        springboot: true
  tomcat:
    max-connections: 10000
    accept-count: 1000
    uri-encoding: UTF-8
    threads:
      max: 1000
      min-spare: 50

spring:
  profiles:
    active: dev
  datasource:
    druid:
      url: ${druid.datasource.url}
      username: ${druid.datasource.username}
      password: ${druid.datasource.password}
      # 配置初始化大小（默认0）、最小、最大（默认8）
      initial-size: 1
      min-idle: 1
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大。 默认为false
      pool-prepared-statements: true
      # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。
      max-open-prepared-statements: 20
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小和最大生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      max-evictable-idle-time-millis: 900000
      # 用来检测连接是否有效的sql，要求是一个查询语句，常用select 'x'。
      # 如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会起作用。
      validation-query: SELECT 1
      # 申请连接时执行validationQuery检测连接是否有效 默认为true
      test-on-borrow: true
      # 归还连接时执行validationQuery检测连接是否有效 默认为false
      test-on-return: false
      # 申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。
      test-while-idle: true
#      driver-class-name: com.mysql.cj.jdbc.Driver
      driver-class-name: com.jd.jdbc.vitess.VitessDriver
      db-type: mysql
  jmq:
    enabled: true
    producers:
      jdiIscTaskCenterProducer:
        password: ${jmq4.producer.taskCenter.password}
        app: ${jmq4.producer.taskCenter.app}
        address: nameserver.jmq.jd.local:80
        enabled: true
    consumers:
      jdiIscTaskCenterConsumer:
        password: ${jmq4.producer.taskCenter.password}
        app: ${jmq4.producer.taskCenter.app}
        address: nameserver.jmq.jd.local:80
        enabled: true

mybatis-plus:
  global-config:
    db-config:
      logic-not-delete-value: 0
      logic-delete-value: 1
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
logging:
  config: classpath:logback-spring.xml
springboot:
  portal:
    cors: true

jdi:
  common:
    frame:
      ump-prefix: test
      log-level: INFO
  isc:
    customerOrderWaybill: xxx

xxl:
  job:
    admin:
      addresses: http://mro-coupon.jd.com:80
    accessToken: default_token
    executor:
      appname: international-isc-taskCenter-pre-cluster
      address:
      ip:
      port: 9999
      logpath: ./data/applogs/xxl-job/jobhandler
      logretentiondays: 30

laf:
  config:
    manager:
      application: ${ducc.application}
      resources[0]:
        name: ${ducc.application}
        uri: ucc://${ducc.application}:${ducc.token}@${ducc.hostPort}/v1/namespace/${ducc.namespace}/config/${ducc.config}/profiles/${ducc.profile}?longPolling=15000
      parameters[0]:
        name: autoListener
        value: true
      resources[1]:
        name: ${oper-ducc.application}
        uri: ucc://${oper-ducc.application}:${oper-ducc.token}@${oper-ducc.hostPort}/v1/namespace/${oper-ducc.namespace}/config/${oper-ducc.config}/profiles/${oper-ducc.profile}?longPolling=15000
      parameters[1]:
        name: autoListener
        value: true



mail:
  mailType: 161106