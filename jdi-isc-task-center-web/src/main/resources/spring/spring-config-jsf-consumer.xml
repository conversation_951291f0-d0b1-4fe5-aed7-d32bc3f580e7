<?xml version="1.0" encoding="utf-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-autowire="byName">

    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="${jsf.registry.index}"/>


    <!--采购单查询服务-->
    <jsf:consumer id="purchaseOrderReadApiService"
                  interface="com.jdi.isc.aggregate.read.wisp.api.purchase.PurchaseOrderReadApiService"
                  alias="${jsf.consumer.purchase.alias}" timeout="${jsf.consumer.purchase.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!--商品中心打标服务-->
    <jsf:consumer id="orderReadApiService"
                  interface="com.jdi.isc.aggregate.read.wisp.api.order.OrderReadApiService"
                  alias="${jsf.consumer.order.alias}" timeout="${jsf.consumer.order.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!--商品中心打标服务-->
    <jsf:consumer id="categoryApiService"
                  interface="com.jdi.isc.aggregate.read.wisp.api.category.CategoryApiService"
                  alias="${jsf.consumer.category.alias}" timeout="${jsf.consumer.category.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!-- 批量导出任务数据查询服务-->
    <jsf:consumer id="taskDataReadApiService"
                  interface="com.jdi.isc.aggregate.read.wisp.api.worker.TaskDataReadApiService"
                  alias="${jsf.consumer.task.alias}" timeout="${jsf.consumer.task.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!--商品中心打标服务-->
    <jsf:consumer id="purchaseOrderParcelReadApiService"
                  interface="com.jdi.isc.vc.soa.api.parcel.PurchaseOrderParcelReadApiService"
                  alias="${jsf.consumer.vc.parcel.alias}" timeout="${jsf.consumer.vc.parcel.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!-- 供应商基本信息查询服务-->
    <jsf:consumer id="supplierAddressReadApiService"
                  interface="com.jdi.isc.vc.soa.api.supplier.SupplierAddressReadApiService"
                  alias="${jsf.consumer.vc.supplierAddress.alias}" timeout="${jsf.consumer.vc.supplierAddress.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!--供应商地址查询服务-->
    <jsf:consumer id="supplierAccountApiService"
                  interface="com.jdi.isc.vc.soa.api.supplier.SupplierAccountApiService"
                  alias="${jsf.consumer.vc.supplierAccount.alias}" timeout="${jsf.consumer.vc.supplierAccount.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>
    <!-- mku物料写服务 -->
    <jsf:consumer id="iscMkuMaterialWriteApiService"
                  interface="com.jdi.isc.product.soa.api.material.IscMkuMaterialWriteApiService"
                  alias="${jsf.consumer.isc.product.soa.alias}" timeout="${jsf.consumer.isc.product.soa.timeout}" retries="2"
                  check="false" serialization="hessian"
                  protocol="jsf" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!-- 物料权限 -->
    <jsf:consumer id="authorityApiService"
                  interface="com.jd.international.soa.sdk.common.authority.AuthorityApiService" alias="${jsf.consumer.international.alias}"
                  timeout="${jsf.consumer.international.timeout}" protocol="jsf" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!-- 库存写接口 -->
    <jsf:consumer id="iscProductSoaStockWriteApiService"
                  interface="com.jdi.isc.product.soa.api.stock.IscProductSoaStockWriteApiService" alias="${jsf.consumer.isc.product.soa.alias}"
                  timeout="${jsf.consumer.isc.product.soa.timeout}"
                  check="false" serialization="hessian" retries="0"
                  protocol="jsf" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!-- 仓库读接口 -->
    <jsf:consumer id="iscWarehouseReadApiService"
                  interface="com.jdi.isc.product.soa.api.warehouse.IscWarehouseReadApiService" alias="${jsf.consumer.isc.product.soa.alias}"
                  timeout="${jsf.consumer.isc.product.soa.timeout}"
                  check="false" serialization="hessian" retries="0"
                  protocol="jsf" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!-- SKU读接口 -->
    <jsf:consumer id="iscProductSoaSkuReadApiService"
                  interface="com.jdi.isc.product.soa.api.sku.IscProductSoaSkuReadApiService" alias="${jsf.consumer.isc.product.soa.alias}"
                  timeout="${jsf.consumer.isc.product.soa.timeout}"
                  check="false" serialization="hessian" retries="0"
                  protocol="jsf" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>
    <!-- 仓库写接口 -->
    <jsf:consumer id="iscWarehouseWriteApiService"
                  interface="com.jdi.isc.product.soa.api.warehouse.IscWarehouseWriteApiService" alias="${jsf.consumer.isc.product.soa.alias}"
                  timeout="${jsf.consumer.isc.product.soa.timeout}"
                  check="false" serialization="hessian" retries="0"
                  protocol="jsf" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!--品牌服务-->
    <jsf:consumer id="brandJobApiService"
                  interface="com.jdi.isc.product.soa.api.wimp.brand.BrandJobApiService"
                  alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!--跨境属性查询服务-->
    <jsf:consumer id="globalAttributeApiService"
                  interface="com.jdi.isc.product.soa.api.wimp.category.GlobalAttributeApiService"
                  alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!-- 国家语言 -->
    <jsf:consumer id="iscProductSoaCountryLangApiService" interface="com.jdi.isc.product.soa.api.wimp.country.IscProductSoaCountryLangApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" warmupWeightStrategy="taskWarmup" />

    <!-- 国家 -->
    <jsf:consumer id="iscProductSoaCountryApiService" interface="com.jdi.isc.product.soa.api.wimp.country.IscProductSoaCountryApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" warmupWeightStrategy="taskWarmup" />

    <!-- 语言 -->
    <jsf:consumer id="iscProductSoaLangApiService" interface="com.jdi.isc.product.soa.api.wimp.lang.IscProductSoaLangApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}"  warmupWeightStrategy="taskWarmup" />
    <!--品牌服务-->
    <jsf:consumer id="brandApiService"
                  interface="com.jdi.isc.product.soa.api.wimp.brand.BrandApiService"
                  alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>
    <!--Spu服务-->
    <jsf:consumer id="iscProductSoaSpuWriteApiService"
                  interface="com.jdi.isc.product.soa.api.spu.IscProductSoaSpuWriteApiService"
                  alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!--Sku服务-->
    <jsf:consumer id="iscProductSoaSkuWriteApiService"
                  interface="com.jdi.isc.product.soa.api.sku.IscProductSoaSkuWriteApiService"
                  alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>
    <!--产品线查询服务-->
    <jsf:consumer id="businessLineReadApiService"
                  interface="com.jdi.isc.product.soa.api.supplier.BusinessLineReadApiService"
                  alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>
    <!--属性查询服务-->
    <jsf:consumer id="iscAttributeReadApiService"
                  interface="com.jdi.isc.product.soa.api.attribute.IscAttributeReadApiService"
                  alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>
    <!--商品查询服务-->
    <jsf:consumer id="iscProductSoaSpuReadApiService"
                  interface="com.jdi.isc.product.soa.api.spu.IscProductSoaSpuReadApiService"
                  alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>
    <!--文本翻译服务-->
    <jsf:consumer id="iscProductSoaTranslateApiService"
                  interface="com.jdi.isc.product.soa.api.translate.IscProductSoaTranslateApiService"
                  alias="${jsf.consumer.product.translate.alias}" timeout="${jsf.consumer.product.translate.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>
    <!--soa类目服务-->
    <jsf:consumer id="jdiIscSoaCategoryApiService"
                  interface="com.jdi.isc.product.soa.api.wimp.category.CategoryApiService"
                  alias="${jsf.consumer.product.soa.alias}" timeout="300000" serialization="hessian"
                  retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>
    <!--soa类目写服务-->
    <jsf:consumer id="jdiIscSoaCategoryWriteApiService"
                  interface="com.jdi.isc.product.soa.api.wimp.category.CategoryWriteApiService"
                  alias="${jsf.consumer.product.soa.alias}"
                  timeout="${jsf.consumer.product.soa.timeout}"
                  serialization="hessian"
                  warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!--soa供应商服务-->
    <jsf:consumer id="supplierReadApiService"
                  interface="com.jdi.isc.product.soa.api.supplier.SupplierReadApiService"
                  alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!-- 商品中台：商品基本信息、特殊属性及区域限制查询接口 -->
    <jsf:consumer id="productRpc" interface="com.jd.gms.crs.rpc.ProductRpc"
                  protocol="jsf" alias="${jsf.consumer.gmsProduct.alias}" timeout="${jsf.common.timeout}" serialization="hessian">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jsf.consumer.gmsProduct.token}" hide="true"/>
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="(J-dos)imp-operate-admin" hide="true"/>
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jsf.consumer.gmsProduct.alias}" hide="true"/>
    </jsf:consumer>
    <!-- mkuId查询skuId关系服务 https://joyspace.jd.com/pages/aSDDKVWQJGbZWvWSEBMg-->
    <jsf:consumer id="relationQueryRpc" interface="com.jd.aggregation.api.service.RelationQueryRpc"
                  protocol="jsf" alias="${jsf.consumer.mapping.alias}" timeout="${jsf.common.timeout}" >
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jsf.consumer.mapping.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdi-isc-product-center" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jsf.consumer.mapping.alias}" hide="true" />
    </jsf:consumer>

    <!--综合可售 https://cf.jd.com/pages/viewpage.action?pageId=1018965961 -->
    <jsf:consumer id="availableSaleService" interface="com.available.sale.api.service.AvailableSaleService"
                  protocol="jsf" alias="${jsf.consumer.available.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="3859cd71-dbb0-4a69-821d-85e7cb31a6cb" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-gptsoa" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jsf.consumer.available.alias}" hide="true" />
    </jsf:consumer>
    <!--跨境属性写服务-->
    <jsf:consumer id="iscProductGlobalAttributeWriteApiService"
                  interface="com.jdi.isc.product.soa.api.spu.IscProductGlobalAttributeWriteApiService"
                  alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>
    <!-- 藏经阁商品大字段查询接口  -->
    <jsf:consumer id="assemblyRpc" interface="com.jd.gms.crs.rpc.AssemblyRpc"
                  protocol="jsf" alias="${jsf.consumer.gmsAssembly.alias}" timeout="${jsf.common.timeout}">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jsf.consumer.gmsAssembly.token}" hide="true"/>
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdi-isc-product-center" hide="true"/>
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jsf.consumer.gmsAssembly.alias}" hide="true"/>
    </jsf:consumer>
    <!-- 装吧：商品装吧装饰数据获取服务 -->
    <jsf:consumer id="zbWareDetailService" interface="com.jd.sdd.mkt.sdk.component.ZbWareDetailService"
                  protocol="jsf" alias="${jsf.consumer.market.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="831b118d-957b-4fa1-bee5-ecd5cccc683e" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-imp-operate-admin" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jsf.consumer.market.alias}" hide="true" />
    </jsf:consumer>

            <!--其它consumer可复用，预热使用-->
    <jsf:warmupWeightStrategy id="taskWarmup" enabled="true" warmupDuration="15000" warmupWeight="40"/>

    <!-- 订单履约信息缺失读服务 -->
    <jsf:consumer id="iscOrderOutVerifyReadApiService" interface="com.jdi.isc.product.soa.api.orderVerify.IscOrderOutVerifyReadApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.alias}"
                  timeout="${jsf.consumer.product.soa.timeout}"  retries="0"  warmupWeightStrategy="taskWarmup"/>
    <!-- MKU读服务 -->
    <jsf:consumer id="iscProductSoaMkuReadApiService" interface="com.jdi.isc.product.soa.api.mku.IscProductSoaMkuReadApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.alias}"
                  timeout="${jsf.consumer.product.soa.timeout}"  retries="0"  warmupWeightStrategy="taskWarmup"/>

    <!-- 客户信息查询服务 -->
    <jsf:consumer id="customerReadService" interface="com.jdi.isc.aggregate.read.api.customer.CustomerReadService"
                  protocol="jsf" alias="${jsf.consumer.agg.customer.alias}"
                  timeout="${jsf.consumer.agg.customer.timeout}"  retries="0"  warmupWeightStrategy="taskWarmup"/>

    <!-- 聚合服务订单查询服务 -->
    <jsf:consumer id="aggReadOrderReadApiService" interface="com.jdi.isc.aggregate.read.api.order.OrderReadApiService"
                  protocol="jsf" alias="${jsf.consumer.agg.read.alias}"
                  timeout="${jsf.consumer.agg.read.timeout}"  retries="0"  warmupWeightStrategy="taskWarmup"/>

    <!-- 邮件发送服务-->
    <jsf:consumer id="customMailService" interface="com.jd.leo.mail.product.produce.rpc.service.CustomMailService"
                  alias="${jsf.consumer.customMailService.alias}"
                  timeout="${jsf.consumer.customMailService.timeout}" retries="0"  warmupWeightStrategy="taskWarmup" >
        <jsf:parameter key="token" value="${jsf.consumer.customMailService.token}" hide="true"/>
    </jsf:consumer>

    <!-- 客户类目关系服务 -->
    <jsf:consumer id="clientCategoryRelationApiService" interface="com.jdi.isc.product.soa.api.wimp.category.ClientCategoryRelationApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.alias}"
                  timeout="${jsf.consumer.product.soa.timeout}"  retries="0"  warmupWeightStrategy="taskWarmup"/>

    <!-- sku定制化价格 -->
    <jsf:consumer id="iscProductCenterCustomerSkuApiService" interface="com.jdi.isc.product.center.api.customerSku.IscProductCenterCustomerSkuApiService"
                  alias="${jsf.consumer.product.soa.alias}"
                  timeout="${jsf.consumer.product.soa.timeout}" retries="0"  warmupWeightStrategy="taskWarmup" />

    <jsf:consumer id="orderPalletRecordApiService" interface="com.jdi.isc.order.center.api.customs.OrderPalletRecordApiService"
                  alias="${jsf.consumer.order.alias}" timeout="${jsf.consumer.order.timeout}" retries="0"
                  warmupWeightStrategy="taskWarmup" check="false">
    </jsf:consumer>

    <jsf:consumer id="customsExportApiService" interface="com.jdi.isc.order.center.api.customs.CustomsExportApiService"
                  alias="${jsf.consumer.order.alias}" timeout="${jsf.consumer.order.timeout}" retries="0"
                  warmupWeightStrategy="taskWarmup" check="false">
    </jsf:consumer>

    <!-- 客户发票读服务 -->
    <jsf:consumer id="customerInvoiceReadApiService" interface="com.jdi.isc.order.center.api.finance.customerInvoice.CustomerInvoiceReadApiService"
                  alias="${jsf.consumer.order.alias}" timeout="${jsf.consumer.order.timeout}" retries="0"
                  warmupWeightStrategy="taskWarmup" check="false"/>

    <!-- sku客制化价格读服务 -->
    <jsf:consumer id="iscProductSoaCustomerSkuPriceReadApiService"
                  interface="com.jdi.isc.product.soa.api.customerSku.IscProductSoaCustomerSkuPriceReadApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.alias}"
                  timeout="${jsf.consumer.product.soa.timeout}" >
    </jsf:consumer>

    <!-- 利润率阈值写服务 -->
    <jsf:consumer id="iscProductSoaProfitRateWriteApiService"
                  interface="com.jdi.isc.product.soa.api.price.IscProductSoaProfitRateWriteApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.alias}"
                  timeout="${jsf.consumer.product.soa.timeout}" >
    </jsf:consumer>

    <!-- 利润率阈值读服务 -->
    <jsf:consumer id="iscProductSoaProfitRateReadApiService"
                  interface="com.jdi.isc.product.soa.api.price.IscProductSoaProfitRateReadApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.alias}"
                  timeout="${jsf.consumer.product.soa.timeout}" >
    </jsf:consumer>

    <!-- 履约费率写服务 -->
    <jsf:consumer id="iscProductSoaFulfillmentRateWriteApiService"
                  interface="com.jdi.isc.product.soa.api.price.IscProductSoaFulfillmentRateWriteApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.fulfillmentRate.alias}"
                  timeout="${jsf.consumer.product.soa.timeout}" >
    </jsf:consumer>

    <!-- 出口税率服务 -->
    <jsf:consumer id="iscProductSoaExportTaxRateApiService"
                  interface="com.jdi.isc.product.soa.api.taxRate.IscProductSoaExportTaxRateApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.alias}"
                  timeout="${jsf.consumer.product.soa.timeout}" >
    </jsf:consumer>
    <!-- sku客制化价格写服务 -->
    <jsf:consumer id="iscProductSoaCustomerSkuPriceWriteApiService"
                  interface="com.jdi.isc.product.soa.api.customerSku.IscProductSoaCustomerSkuPriceWriteApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.alias}"
                  serialization="hessian"
                  timeout="${jsf.common.writeTimeout}" >
    </jsf:consumer>

    <!-- 二段运单接口-->
    <jsf:consumer id="orderWaybillApiService" interface="com.jdi.isc.order.center.api.waybill.OrderWaybillApiService"
                  protocol="jsf" alias="${jsf.consumer.order.alias}" timeout="${jsf.consumer.order.timeout}" retries="0"
                  warmupWeightStrategy="taskWarmup" check="false"/>

    <!-- 价格监控服务 -->
    <jsf:consumer id="iscProductSoaSkuPriceMonitorApiService" interface="com.jdi.isc.product.soa.api.price.SkuPriceMonitorApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" retries="0"
                  warmupWeightStrategy="taskWarmup" check="false" serialization="hessian" />
    <!-- 国家池写服务 -->
    <jsf:consumer id="iscCountryMkuWriteApiService"
                  interface="com.jdi.isc.product.soa.api.countryMku.IscCountryMkuWriteApiService"
                  protocol="jsf"
                  alias="${jsf.consumer.product.soa.alias}"
                  timeout="${jsf.consumer.product.soa.timeout}"
                  serialization="hessian" >
    </jsf:consumer>
    <!-- 商品特殊属性 -->
    <jsf:consumer id="specialAttrApiService" interface="com.jdi.isc.product.soa.api.specialAttr.SpecialAttrApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" />

    <jsf:consumer id="specialAttrRelationApiService" interface="com.jdi.isc.product.soa.api.specialAttr.SpecialAttrRelationApiService"
                  protocol="jsf" alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}" />

    <jsf:consumer id="iscHscodeTaxRateRelationWriteApiServiceImpl" interface="com.jdi.isc.product.soa.api.taxRate.IscHscodeTaxRateRelationWriteApiService"
                  alias="${jsf.consumer.product.soa.alias}" timeout="${jsf.consumer.product.soa.timeout}"/>

    <jsf:consumer id="iscTaxRateWriteApiService"
                  interface="com.jdi.isc.product.soa.api.taxRate.IscTaxRateWriteApiService"
                  alias="${jsf.consumer.product.soa.alias}"
                  timeout="${jsf.consumer.product.soa.timeout}"/>
    <jsf:consumer id="iscTaxRateReadApiService"
                  interface="com.jdi.isc.product.soa.api.taxRate.IscTaxRateReadApiService"
                  alias="${jsf.consumer.product.soa.alias}"
                  timeout="${jsf.consumer.product.soa.timeout}"/>
    <!--  一段运单信息查询  -->
    <jsf:consumer id="purchaseOrderWaybillApiService"
                  interface="com.jdi.isc.vc.soa.api.waybill.PurchaseOrderWaybillApiService"
                  alias="${jsf.consumer.vc.supplierAccount.alias}" timeout="${jsf.consumer.vc.supplierAccount.timeout}" retries="0" warmupWeightStrategy="taskWarmup"/>

    <!-- 京东云打印 -->
    <jsf:consumer id="jdiHiprintPdfRenderService" interface="com.jd.jp.print.templet.center.sdk.service.JdiHiprintPdfRenderService"
                  protocol="jsf" alias="${jsf.consumer.cloudPrint.alias}" timeout="${jsf.consumer.cloudPrint.timeout}" />

    <!--加载类目缓存任务接口 -->
    <jsf:consumer id="workerApiService" interface="com.jdi.isc.aggregate.read.wisp.api.worker.WorkerApiService"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  retries="0" protocol="jsf" />

    <jsf:consumer id="orderBatchWriteApiService" interface="com.jdi.isc.order.center.api.orderWrite.OrderBatchWriteApiService"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.longTimeout}"
                  retries="0" protocol="jsf" />

    <!-- 国家池读服务 -->
    <jsf:consumer id="iscCountryMkuReadApiService"
                  interface="com.jdi.isc.product.soa.api.countryMku.IscCountryMkuReadApiService"
                  protocol="jsf"
                  alias="${jsf.consumer.product.soa.alias}"
                  timeout="${jsf.consumer.product.soa.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 安全库存接口 -->
    <jsf:consumer id="skuStockThresholdApiService"
                  interface="com.jdi.isc.product.soa.stock.threshold.SkuStockThresholdApiService"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"/>

    <!-- 京ME发送消息接口 -->
    <jsf:consumer id="jingMeMessageApiService"
                  interface="com.jdi.isc.biz.component.api.jme.JingMeMessageApiService"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"/>

    <!--区域查询服务 -->
    <jsf:consumer id="areaApiService" interface="com.jdi.isc.aggregate.read.wisp.api.area.AreaApiService"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  retries="0" protocol="jsf" />

    <!--备货采购单查询 -->
    <jsf:consumer id="stockPurchaseOrderApiService" interface="com.jdi.isc.order.center.api.purchaseOrder.StockPurchaseOrderApiService"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  retries="0" protocol="jsf" />

    <!-- 国际SKU税率写服务 -->
    <jsf:consumer id="iscProductSoaTaxWriteApiService"
                  interface="com.jdi.isc.product.soa.price.api.price.IscProductSoaTaxWriteApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.writeTimeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 加价率写服务 -->
    <jsf:consumer id="iscMarkupRateWriteApiService"
                  interface="com.jdi.isc.product.soa.api.markupRate.IscMarkupRateWriteApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 销售价格写服务 -->
    <jsf:consumer id="iscProductSoaSalePriceWriteApiService"
                  interface="com.jdi.isc.product.soa.price.api.salePrice.IscProductSoaSalePriceWriteApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 修改国家协议价服务 -->
    <jsf:consumer id="iscCountryAgreementPriceWriteApiService"
                  interface="com.jdi.isc.product.soa.api.agreementPrice.IscCountryAgreementPriceWriteApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.writeTimeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 订单中心读服务 -->
    <jsf:consumer id="orderCenterOrderReadApiService"
                  interface="com.jdi.isc.order.center.api.orderRead.OrderReadApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.longTimeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!--  查询销售单位 -->
    <jsf:consumer id="saleUnitApiService" interface="com.jdi.isc.aggregate.read.wisp.api.mku.SaleUnitApiService"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  retries="2" check="false">
    </jsf:consumer>

    <!-- 三方归档订单 -->
    <jsf:consumer id="orderThirdArchiveApiService"
                  interface="com.jdi.isc.order.center.api.orderArchive.OrderThirdArchiveApiService"
                  protocol="jsf" alias="${jsf.common.alias}" timeout="${jsf.common.longTimeout}"
                  serialization="hessian"/>
    <!-- 开放消息读写服务 -->
    <jsf:consumer id="wiopMsgApiService"
                  interface="com.jdi.isc.product.soa.api.message.WiopMsgApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 三方归档订单商品 -->
    <jsf:consumer id="orderThirdArchiveWareApiService"
                  interface="com.jdi.isc.order.center.api.orderArchive.OrderThirdArchiveWareApiService"
                  protocol="jsf" alias="${jsf.common.alias}" timeout="${jsf.common.timeout}"
                  serialization="hessian"/>

    <!-- 短标题读服务 -->
    <jsf:consumer id="iscSubtitleReadApiService"
                  interface="com.jdi.isc.product.soa.api.subtitle.IscSubtitleReadApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- Mku商品写服务 -->
    <jsf:consumer id="iscProductSoaMkuWriteApiService"
                  interface="com.jdi.isc.product.soa.api.mku.IscProductSoaMkuWriteApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 协议价读服务 -->
    <jsf:consumer id="iscCountryAgreementPriceReadApiService"
                  interface="com.jdi.isc.product.soa.api.agreementPrice.IscCountryAgreementPriceReadApiService"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  serialization="hessian"/>
    <!-- sku采购价格写服务 -->
    <jsf:consumer id="iscSkuPriceWriteApiService"
                  interface="com.jdi.isc.product.soa.api.price.supplierPrice.IscSkuPriceWriteApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.writeTimeout}"
                  serialization="hessian" >
    </jsf:consumer>


    <!-- 唛头文件服务 -->
    <jsf:consumer id="purchaseOrderParcelFileApiService" interface="com.jdi.isc.vc.soa.api.parcel.PurchaseOrderParcelFileApiService"
                  protocol="jsf" alias="${jsf.consumer.vc.parcel.alias}" timeout="20000" />

    <!-- 商品报关信息服务 -->
    <jsf:consumer id="productCustomsApiService" interface="com.jdi.isc.order.center.api.customs.ProductCustomsApiService"
                  protocol="jsf" alias="${jsf.common.alias}" timeout="${jsf.common.timeout}" />

    <!-- 历史实际退税成功率写服务 -->
    <jsf:consumer id="iscTaxRefundRateWriteApiService"
                  interface="com.jdi.isc.product.soa.api.price.taxRefundRate.IscTaxRefundRateWriteApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <jsf:consumer id="categoryBuyerRelationApiService"
                  interface="com.jdi.isc.product.soa.api.wimp.category.CategoryBuyerRelationApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  serialization="hessian">
    </jsf:consumer>

    <!-- 类目统计 -->
    <jsf:consumer id="iscProductSoaCategoryConfigStatisticsApiService"
                  interface="com.jdi.isc.product.soa.api.category.IscProductSoaCategoryConfigStatisticsApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  serialization="hessian">
    </jsf:consumer>

    <!-- 权限查询 -->
    <jsf:consumer id="iscProductSoaAuthReadApiService"
                  interface="com.jdi.isc.product.soa.api.auth.IscProductSoaAuthReadApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  serialization="hessian">
    </jsf:consumer>


    <!-- 预报备采购单服务 -->
    <jsf:consumer id="forecastOrderApiService" interface="com.jdi.isc.order.center.api.forecast.ForecastOrderApiService"
                  protocol="jsf" alias="${jsf.common.alias}" timeout="${jsf.common.timeout}" />

    <!-- 国内价格查询服务 -->
    <jsf:consumer id="iscSkuDomesticPriceReadApiService"
                  interface="com.jdi.isc.product.soa.price.api.jdPrice.IscSkuDomesticPriceReadApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  serialization="hessian"/>

    <jsf:consumer id="afterSalesReadApiService"
                  interface="com.jdi.isc.order.center.api.aftersales.AfterSalesReadApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  serialization="hessian">
    </jsf:consumer>

    <!-- 昆仑算法能力 -->
    <jsf:consumer id="iClsService" interface="com.jdi.kunlun.algo.cls.api.service.IClsService"
                  protocol="jsf" alias="${jsf.consumer.kunlun.alias}" timeout="${jsf.consumer.kunlun.timeout}" />

    <!-- 类目读写服务 -->
    <jsf:consumer id="iscProductSoaCategoryApiService"
                  interface="com.jdi.isc.product.soa.api.category.IscProductSoaCategoryApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  serialization="hessian">
    </jsf:consumer>

    <!-- 商品服务 审核写能力 -->
    <jsf:consumer id="approveOrderWriteApiService"
                  interface="com.jdi.isc.product.soa.api.approveorder.ApproveOrderWriteApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.longTimeout}"
                  serialization="hessian">
    </jsf:consumer>

    <!-- 类目税率写服务 -->
    <jsf:consumer id="iscCategoryTaxRateWriteApiService"
                  interface="com.jdi.isc.product.soa.api.taxRate.IscCategoryTaxRateWriteApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.longTimeout}"
                  serialization="hessian">
    </jsf:consumer>

    <!-- 库存查询服务 -->
    <jsf:consumer id="iscMkuStockReadApiService"
                  interface="com.jdi.isc.product.soa.stock.mku.IscMkuStockReadApiService"
                  protocol="jsf"
                  alias="${jsf.common.alias}"
                  timeout="${jsf.common.timeout}"
                  serialization="hessian">
    </jsf:consumer>
</beans>