<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd">

	<jsf:server id="jsf-write" protocol="jsf"/>
	<jsf:server id="jsf-read" protocol="jsf"/>

	<!-- 开发工具服务 -->
	<jsf:provider id="devOpsApiService"
				  interface="com.jdi.isc.task.center.api.devOps.DevOpsApiService"
				  alias="${jsf.provider.devOps.alias}"
				  serialization="hessian"
				  timeout="${jsf.provider.devOps.timeout}"
				  ref="devOpsApiServiceImpl" server="jsf-write" >
<!--		<jsf:parameter key="token" value="${provider.product.read.token}" hide="true"/>-->
	</jsf:provider>

	<!-- 销端任务服务 -->
	<jsf:provider id="customerTaskApiService"
				  interface="com.jdi.isc.task.center.api.task.customer.CustomerTaskApiService"
				  alias="${jsf.common.alias}"
				  timeout="${jsf.common.timeout}"
				  serialization="hessian"
				  ref="customerTaskApiServiceImpl" server="jsf-write" >
	</jsf:provider>
	<!-- 动态生成模版服务 -->
	<jsf:provider id="iscTaskTemplateApiService"
				  interface="com.jdi.isc.task.center.api.template.IscTaskTemplateApiService"
				  alias="${jsf.provider.template.alias}"
				  serialization="hessian"
				  timeout="${jsf.provider.template.timeout}"
				  ref="iscTaskTemplateApiServiceImpl" server="jsf-write"
				  compress="snappy">
<!--		<jsf:parameter key="token" value="${provider.product.read.token}" hide="true"/>-->
	</jsf:provider>

	<!-- 二段运单文件服务 -->
	<jsf:provider id="orderWaybillFileApiService"
				  interface="com.jdi.isc.task.center.api.waybill.OrderWaybillFileApiService"
				  alias="${jsf.common.alias}"
				  timeout="${jsf.common.timeout}"
				  serialization="hessian"
				  ref="orderWaybillFileApiServiceImpl" server="jsf-read">
	</jsf:provider>
	<!-- 海外企配仓包裹打印服务 -->
	<jsf:provider id="OutStockPackagePrintApiService"
				  interface="com.jdi.isc.task.center.api.warehouse.OutStockPackagePrintApiService"
				  alias="${jsf.common.alias}"
				  timeout="${jsf.common.timeout}"
				  serialization="hessian"
				  ref="outStockPackagePrintApiServiceImpl" server="jsf-read">
	</jsf:provider>
	<jsf:provider id="iscMailServiceApi"
				  interface="com.jdi.isc.task.center.api.mail.IscMailServiceApi"
				  alias="${jsf.common.alias}"
				  timeout="${jsf.common.timeout}"
				  serialization="hessian"
				  ref="iscMailServiceApiImpl" server="jsf-write">
	</jsf:provider>

	<jsf:provider id="productIdentifyApService"
				  interface="com.jdi.isc.task.center.api.productIdentify.ProductIdentifyApService"
				  alias="${jsf.common.alias}"
				  timeout="${jsf.common.longTimeout}"
				  serialization="hessian"
				  ref="productIdentifyApServiceImpl" server="jsf-write">
	</jsf:provider>

	<jsf:provider id="customsFileApiService" interface="com.jdi.isc.task.center.api.order.CustomsFileApiService"
				  alias="${jsf.common.alias}"
				  timeout="${jsf.common.longTimeout}"
				  ref="customsFileApiServiceImpl" server="jsf-write">
	</jsf:provider>

	<!--云打印模版-->
	<jsf:provider id="waybillPdfService" interface="com.jdi.isc.task.center.api.print.PrintPdfService"
				  alias="${jsf.common.alias}"
				  timeout="${jsf.common.longTimeout}"
				  ref="printPdfServiceImpl" server="jsf-read">
	</jsf:provider>

<!--	任务api服务-->
	<jsf:provider id="wimpTaskApiService" interface="com.jdi.isc.task.center.api.task.WimpTaskApiService"
				  alias="${jsf.common.alias}"
				  timeout="${jsf.common.longTimeout}"
				  ref="wimpTaskApiServiceImpl" server="jsf-write">
	</jsf:provider>

	<!-- 备货采购单打印 -->
	<jsf:provider id="stockPurchaseOrderService" interface="com.jdi.isc.task.center.api.purchaseOrder.StockPurchaseOrderService"
				  alias="${jsf.common.alias}"
				  timeout="${jsf.common.longTimeout}"
				  ref="stockPurchaseOrderServiceImpl" server="jsf-read">
	</jsf:provider>

	<!-- 订单物流清单打印 -->
	<jsf:provider id="orderDeliveryApiService" interface="com.jdi.isc.task.center.api.order.OrderDeliveryApiService"
				  alias="${jsf.common.alias}"
				  timeout="${jsf.common.longTimeout}"
				  ref="orderDeliveryServiceImpl" server="jsf-read">
	</jsf:provider>


	<!-- 订单物流清单打印 -->
	<jsf:provider id="purchaseOrderParcelApiService" interface="com.jdi.isc.task.center.api.waybill.PurchaseOrderParcelApiService"
				  alias="${jsf.common.alias}"
				  timeout="${jsf.common.longTimeout}"
				  ref="purchaseOrderParcelApiServiceImpl" server="jsf-read">
	</jsf:provider>

	<!-- 预报备采购单入库打印 -->
	<jsf:provider id="forecastPurchaseOrderPrintApiService" interface="com.jdi.isc.task.center.api.forecast.ForecastPurchaseOrderPrintApiService"
				  alias="${jsf.common.alias}"
				  timeout="${jsf.common.longTimeout}"
				  ref="forecastPurchaseOrderApiServiceImpl" server="jsf-read">
	</jsf:provider>
</beans>
