infrastructure:
  jsfInvokeLog: true
  jsfInvokeLogLevel: INFO
  jsfValidate: true
  adapterInvokeLog: true
  adapterInvokeLogLevel: DEBUG
  adapterUmpPrefix: depend-
  invokeUmpPrefix: pre-

ducc:
  token: 39ed99a7cd3e42eebfd0c4c92938b28b
  hostPort: ducc.jd.local
  config: config
  profile: online
  namespace: isc-task
  application: jdos_jdi-isc-task-center

jsf:
  registry:
    index: i.jsf.jd.local
  common:
    alias: pro:1.0
    timeout: 5000
    writeTimeout: 20000
    longTimeout: 120000
  consumer:
    timeout: 5000
    bware:
      read: pro:1.0
      timeout: 5000
    purchase:
      alias: pro:1.0
      timeout: 5000
    order:
      alias: pro:1.0
      timeout: 5000
    category:
      alias: pro:1.0
      timeout: 5000
    task:
      alias: pro:1.0
      timeout: 5000
    vc:
      parcel:
        alias: pro:1.0
        timeout: 5000
      supplierAddress:
        alias: pro:1.0
        timeout: 5000
      supplierAccount:
        alias: pro:1.0
        timeout: 5000
    product:
      soa:
        alias: prod:1.0
        timeout: 5000
      translate:
        alias: prod:1.0
        timeout: 20000
    isc:
      product:
        soa:
          alias: prod:1.0
          timeout: 5000
    customMailService:
      token: VBbXzW7xlaD3YiqcVrVehA
      alias: custom_mailService_online
    kunlun:
      alias: ProdV2
      timeout: 5000
  provider:
    devOps:
      alias: prod:1.0
      timeout: 15000
    template:
      alias: prod:1.0
      timeout: 15000
sso:
  clientId: jdi-isc-product-center
  clientSecret: ed6d51e8785a4870ba43eb5991bba20a
  callbackUri: http://wimp.jd.com/


jimdb:
  serviceEndpoint: http://cfs.jim.jd.local
  ioThreadPoolSize: 5
  computationThreadPoolSize: 5
  requestQueueSize: 100000


# https://docs.jdcloud.com/cn/object-storage-service/oss-endpont-list
s3:
  region: cn-north-1
  protocol: https://
  maxConnections: 200
  external:
    endpoint: s3.cn-north-1.jdcloud-oss.com
  internal:
    endpoint: s3-internal.cn-north-1.jdcloud-oss.com
  default:
    bucket: jdi-intl
  connectionTimeout: 30000
  socketTimeout: 30000

jdi:
  isc:
    task:
      env: prod
    zip:
      path: /export/data/zipFolder/

jd:
  product:
    system:
      key: qiye_jdi-igc-product
  gms:
    product:
      system:
        key: gongye_jdi-isc-product-center
image:
  translate:
    url: www.jiatongyitu.com/imageTransl
    getResultUrl: www.jiatongyitu.com/imageTransl/getResult
    apiKey: WV5NeUrCNmpVwHEc
  domain: http://img11.360buyimg.com/n1/



jmq4:
  producer:
    taskCenter:
      password: c1bcb959fd464eafbfe11be5ba447dac
      app: jdiisctaskcenter

topic:
  jmq4:
    consumer:
      purchaseOrder:
        updateStatus: jdi_isc_purchase_order_status_update_msg_pre
        invoice: jdi_isc_purchase_invoice_update_msg_pre
        changePrice: xxxx
      mail:
        common: jdi_isc_mail_send_topic
    producer:
      productIdentify: jdi_isc_ai_material_identify
      mkuEsChange: jdi_isc_mku_es_change_msg

order:
  systemCode: 'task-center'