infrastructure:
  jsfInvokeLog: true
  jsfInvokeLogLevel: INFO
  jsfValidate: true
  adapterInvokeLog: true
  adapterInvokeLogLevel: DEBUG
  adapterUmpPrefix: depend-
  invokeUmpPrefix: dev-

ducc:
  token: 5b558ef142b74623b34c5135a8e6ea14
  hostPort: test.ducc.jd.local
  config: isc-task
  profile: pre
  namespace: international
  application: jdos_eone-jdi-isc-task-center

oper-ducc:
  token: 3a55f84dfbbf4cf999ed8d7342827925
  hostPort: test.ducc.jd.local
  config: isc-oper
  profile: pre
  namespace: international
  application: jdos_jdi-isc-product-center

jsf:
  registry:
    index: test.i.jsf.jd.local
  common:
    alias: test:1.0
    timeout: 5000
    writeTimeout: 20000
    longTimeout: 120000
  consumer:
    timeout: 5000
    cloudPrint:
      alias: yf
      timeout: 5000
    agg:
      customer:
        alias: xx
        timeout: 5000
      read:
        alias: xx
        timeout: 5000
    bware:
      read: test:1.0
      timeout: 5000
    purchase:
      alias: test:1.0
      timeout: 5000
    order:
      alias: test:1.0
      timeout: 5000
    category:
      alias: test:1.0
      timeout: 5000
    task:
      alias: test:1.0
      timeout: 5000
    vc:
      parcel:
        alias: test:1.0
        timeout: 5000
      supplierAddress:
        alias: test:1.0
        timeout: 5000
      supplierAccount:
        alias: test:1.0
        timeout: 5000
      soa:
        cate:
          alias: test:1.0
    isc:
      product:
        soa:
          alias: test:1.0
          timeout: 5000
    product:
      soa:
        alias: test:1.0
        timeout: 5000
        fulfillmentRate:
          alias: uat:1.0-yuy
      translate:
        alias: test:1.0
        timeout: 20000
    international:
      alias: test:1.0
      timeout: 5000
    gmsProduct:
      alias: category_pro_cn_LF
      token: 7212f1b4-1c2a-4133-8ef6-8c3eea0683c9
    mapping:
      alias: ztcs
      token: test
    available:
      alias: ht
    gmsAssembly:
      alias: LF_PUBLIC_DB_0
      token: 4090804c-62fe-4771-aa4d-3f79c4864d5f
    market:
      alias: WARE-DETAIL
      token: 831b118d-957b-4fa1-bee5-ecd5cccc683e
    customMailService:
      token: VBbXzW7xlaD3YiqcVrVehA
      alias: custom_mailService_online
      timeout: 5000
    kunlun:
      alias: ProdV2
      timeout: 5000
  provider:
    devOps:
      alias: test:1.0
      timeout: 5000
    template:
      alias: test:1.0
      timeout: 5000
sso:
  clientId: test
  clientSecret: 347c6161e79f4b6a8873202dd5fe7e8f
  endpoint: http://test.ssa.jd.com
  serviceIndex: test.i.jsf.jd.local
  serviceAlias: bj-test
  callbackUri: http://wimp.jd.com/

jimdb:
  serviceEndpoint: http://test.cfs.jim.jd.local
  ioThreadPoolSize: 5
  computationThreadPoolSize: 5
  requestQueueSize: 100000

# https://docs.jdcloud.com/cn/object-storage-service/oss-endpont-list
s3:
  region: cn-north-1
  protocol: https://
  maxConnections: 500
  external:
    endpoint: s3.cn-north-1.jdcloud-oss.com
  internal:
    endpoint: s3-internal.cn-north-1.jdcloud-oss.com
  default:
    bucket: jdi-intl
  connectionTimeout: 30000
  socketTimeout: 30000
jdi:
  isc:
    task:
      env: dev
    zip:
      path: /Users/<USER>/Downloads/zipFolder/
jd:
  product:
    system:
      key: qiye_jdi-igc-product
  gms:
    product:
      system:
        key: gongye_jdi-isc-product-center
image:
  translate:
    url: www.jiatongyitu.com/imageTransl
    getResultUrl: www.jiatongyitu.com/imageTransl/getResult
    apiKey: WV5NeUrCNmpVwHEc
  domain: http://img11.360buyimg.com/n1/



jmq4:
  producer:
    taskCenter:
      password: 18bd4b250fe34c059a836a045982cf67
      app: eonejdiisctaskcenter

topic:
  jmq4:
    consumer:
      purchaseOrder:
        updateStatus: jdi_isc_purchase_order_status_update_msg_pre
        invoice: jdi_isc_purchase_invoice_update_msg_pre
        changePrice: xxxx
      mail:
        common: jdi_isc_mail_send_topic
      settlementOrder:
        create: jdi_isc_order_settlement_create_msg_test
      updateOrder:
        create: jdi_isc_order_center_status_update_msg_test
    producer:
      productIdentify: jdi_isc_ai_material_identify
      mkuEsChange: jdi_isc_mku_es_change_msg_pre

order:
  systemCode: 'task-center'

xxl:
  job:
    admin:
      addresses: http://mro-coupon.jdtest.net:80
    accessToken: default_token
    executor:
      appname: international-isc-taskCenter-test-cluster
      address:
      ip:
      port: 9999
      logpath: ./data/applogs/xxl-job/jobhandler
      logretentiondays: 30