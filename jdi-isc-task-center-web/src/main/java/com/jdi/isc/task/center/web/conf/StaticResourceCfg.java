package com.jdi.isc.task.center.web.conf;

import com.itextpdf.text.Font;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.BaseFont;
import com.jdi.isc.task.center.common.frame.StaticResourceContainer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * pdf字体文件初始化
 */
@Configuration
@Slf4j
public class StaticResourceCfg {


    //pdf字体文件初始化
    @Bean(name = "resourceContainer")
    public StaticResourceContainer getStaticResourceContainer(){
        StaticResourceContainer fontContainer = new StaticResourceContainer();
        try {
            String path = StaticResourceCfg.class.getResource("/").toURI().getPath();
            //String path ="/Users/<USER>/work/project/jdi-isc-task-center/jdi-isc-task-center-web/src/main/resources/";
            //标题
            fontContainer.setTitleFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),25,Font.BOLD));
            //加大正文
            fontContainer.setVeryBigFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),20,Font.BOLD));
            //略大正文
            fontContainer.setBigFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),14,Font.BOLD));
            //正文
            fontContainer.setTextFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),12,Font.NORMAL));
            //11号字体
            fontContainer.setElevenSizeFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),11,Font.NORMAL));
            //10号字体
            fontContainer.setTenSizeFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),10,Font.NORMAL));
            //正文加粗
            fontContainer.setTextBoldFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),12,Font.BOLD));
            //10号字体 加粗
            fontContainer.setTenSizeBoldFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),10,Font.BOLD));
            //水印字体
            fontContainer.setWaterMarkFont(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED));
            fontContainer.setSmallFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),8,Font.NORMAL));
            //16号加粗字体
            fontContainer.setSixteenBoldFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),16,Font.BOLD));
            //7号加粗字体
            fontContainer.setSevenBoldFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED), 7, Font.BOLD));
            //7号正文字体
            fontContainer.setSevenFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),7,Font.NORMAL));

            //logo图
            Image image01 = Image.getInstance(path + "static/logo.png");
            image01.scaleAbsolute(100, 32);
            fontContainer.setLogo(image01);

        }catch (Exception e){
            log.info("GlobalFontCfg.getFontContainer error" , e );
        }
        return fontContainer;
    }


}
