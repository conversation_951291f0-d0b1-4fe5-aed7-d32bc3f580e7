package com.jdi.isc.task.center.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdi.isc.task.center.api.common.enums.SupplierTaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.api.template.req.TemplateReqDTO;
import com.jdi.isc.task.center.domain.excel.TemplateReqVO;
import com.jdi.isc.task.center.service.support.excel.DynamicTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2024/8/14
 **/
@Slf4j
@RestController
@RequestMapping("excel")
public class ExcelTestController {

    @Resource
    private DynamicTemplateService dynamicTemplateService;

    @RequestMapping("downloadGlobalAttribute")
    public void downloadGlobalAttributeTemplate(TemplateReqVO templateReqDTO, HttpServletResponse response) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("attributeScope", "CN,TH,VN,HU");
            jsonObject.put("sourceCountryCode", "CN");
            templateReqDTO.setOperator(StringUtils.isNotBlank(templateReqDTO.getOperator()) ? templateReqDTO.getOperator() : "sunlei61");
            templateReqDTO.setTaskBizType(TaskBizTypeEnum.GLOBAL_ATTRIBUTE.getCode());
            templateReqDTO.setParamJson(jsonObject.toJSONString());
            dynamicTemplateService.generateTemplateByType(templateReqDTO, response);
        } catch (Exception e) {
            log.error("【系统异常】ExcelTestController.downloadGlobalAttributeTemplate templateReqDTO={}", JSON.toJSONString(templateReqDTO),e);
        }
    }


    @RequestMapping("downloadVcCreateProduct")
    public void downloadVcCreateProduct(TemplateReqVO templateReqDTO, HttpServletResponse response) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("attributeScope", "VN");
            jsonObject.put("sourceCountryCode", "VN");
            jsonObject.put("supplierCode", "YF-20240430001");
            jsonObject.put("lang", "vi");
            jsonObject.put("categoryId", 3870);
            templateReqDTO.setOperator(StringUtils.isNotBlank(templateReqDTO.getOperator()) ? templateReqDTO.getOperator() : "sunlei61");
            templateReqDTO.setTaskBizType(SupplierTaskBizTypeEnum.SUPPLIER_CREATE_PRODUCT_BATCH_IMPORT.getCode());
            templateReqDTO.setParamJson(jsonObject.toJSONString());
            dynamicTemplateService.generateTemplateByType(templateReqDTO, response);
        } catch (Exception e) {
            log.error("【系统异常】ExcelTestController.downloadGlobalAttributeTemplate templateReqDTO={}", JSON.toJSONString(templateReqDTO),e);
        }
    }
}
