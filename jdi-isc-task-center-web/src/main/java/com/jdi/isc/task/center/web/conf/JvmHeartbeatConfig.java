package com.jdi.isc.task.center.web.conf;

import com.jd.ump.annotation.JAnnotation;
import com.jdi.isc.task.center.common.costants.Constant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JvmHeartbeatConfig {

    @Value("${spring.profiles.active}")
    private String active;

    @Value("${service.appName}")
    private String appName;

    @Bean(initMethod="afterPropertiesSet")
    public JAnnotation umpJAnnotation() {
        JAnnotation jAnnotation = new JAnnotation();
        jAnnotation.setJvmKey(appName + Constant.UNDER_LINE + active);
        return jAnnotation;
    }
}
