package com.jdi.isc.task.center.service.excel;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.CacheLocationEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdi.isc.library.common.enmus.file.FileTypeEnum;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.CurrencyLangEnum;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.sku.req.SkuUpdateApiDTO;
import com.jdi.isc.product.soa.api.spu.req.SpuLangApiDTO;
import com.jdi.isc.product.soa.api.spu.req.SpuUpdateApiDTO;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.api.template.req.TemplateReqDTO;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.common.utils.S3Utils;
import com.jdi.isc.task.center.domain.excel.*;
import com.jdi.isc.task.center.domain.task.excel.SkuAmendUpdateCNExcelDTO;
import com.jdi.isc.task.center.domain.task.excel.UnitExcelDTO;
import com.jdi.isc.task.center.rpc.spu.RpcSpuService;
import com.jdi.isc.task.center.service.BaseTest;
import com.jdi.isc.task.center.service.adapter.mapstruct.sku.SkuConvert;
import com.jdi.isc.task.center.service.adapter.mapstruct.spu.SpuConvert;
import com.jdi.isc.task.center.service.adapter.mapstruct.task.TemplateConvert;
import com.jdi.isc.task.center.service.support.AssertValidation;
import com.jdi.isc.task.center.service.support.excel.DynamicTemplateService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class productDataTest  extends BaseTest {


    //@Resource
    //private IscTaskTemplateApiService iscTaskTemplateApiService;

    @Resource
    private DynamicTemplateService dynamicTemplateService;

    @Resource
    private S3Utils s3Utils;

    @Resource
    private RpcSpuService rpcSpuService;


    @Test
    public void templateExport(){
        String pathName = "/Users/<USER>/Downloads/file-zip/中国修改.xlsx";
        try {
            TemplateReqDTO reqDTO = new TemplateReqDTO();
            reqDTO.setOperator("wangpeng965");
            reqDTO.setTaskBizType(46);
            //DataResponse<TemplateResDTO> dataResponse = iscTaskTemplateApiService.generateTemplate(reqDTO);
            TemplateReqVO reqVO = TemplateConvert.INSTANCE.dto2Vo(reqDTO);
            TemplateResVO templateResVO = dynamicTemplateService.generateTemplateByParam(reqVO);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(templateResVO.getBytes());
            try (FileOutputStream fileOutputStream = new FileOutputStream(pathName)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    fileOutputStream.write(buffer, 0, bytesRead);
                }
                System.out.println("文件写入完成！");
            } catch (IOException e) {
                e.printStackTrace();
            }

            //String url = s3Utils.upload(inputStream, FileTypeEnum.BATCH_FILE.getCode(), "中国修改.xlsx");
            //System.out.println("url地址="+url);
        } catch (Exception e) {
            log.error("productDataTest.templateExport 模板生成异常 reqDTO={}", JSON.toJSONString(null),e);
        }
    }

    @Test
    public void excel(){
        String pathName = "/Users/<USER>/Downloads/file-zip/中国修改1.xlsx";
        TemplateResVO templateResVO = new TemplateResVO();
        templateResVO.setFileName(pathName);
        List<TemplateSourceDataVO.SheetDataVO> sheetDataVOList = new ArrayList<>();
        TemplateSourceDataVO.SheetDataVO sheetDataVO1 = new TemplateSourceDataVO.SheetDataVO();
        List<String> saleUnitList = getSaleUnitList();
        List<UnitExcelDTO> list = new ArrayList<>();
        UnitExcelDTO unitExcelDTO1 = new UnitExcelDTO();
        UnitExcelDTO unitExcelDTO2 = new UnitExcelDTO();
        unitExcelDTO1.setSaleUnit("公斤");
        unitExcelDTO2.setSaleUnit("个头");
        list.add(unitExcelDTO1);
        list.add(unitExcelDTO2);
        sheetDataVO1.setDatatList(list);
        sheetDataVO1.setClazz(UnitExcelDTO.class);
        sheetDataVO1.setSheetNo(0);
        sheetDataVOList.add(sheetDataVO1);
        // 模版数据写入
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream(); ExcelWriter excelWriter = EasyExcel.write(outputStream).inMemory(Boolean.TRUE).filedCacheLocation(CacheLocationEnum.NONE).build()) {

           // ExcelWriter excelWriter = EasyExcel.write(outputStream,UnitExcelDTO.class).excludeColumnFieldNames(Sets.newHashSet("result")).build();
            // 设置content-disposition响应头控制浏览器以下载的形式打开文件，中文文件名要使用URLEncoder.encode方法进行编码，否则会出现文件名乱码
            //response.setHeader("content-disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));
            // 这里 需要指定写用哪个class去写，然后写到第一个sheet，名字为模板 然后文件流会自动关闭
           // WriteSheet writeSheet = EasyExcel.writerSheet(0, "result").build();
            //writeSheet.setNeedHead(true);
            int sheetNo = 0;
            // 处理多个表格
            for (TemplateSourceDataVO.SheetDataVO sheetDataVO : sheetDataVOList) {
                 // 数据写入文档
                //excelWriter.write(sheetDataVO.getDatatList(), writeSheet);
                excelWriter.write(sheetDataVO.getDatatList(), this.getWriteSheet(sheetDataVO, sheetNo));
                // 写入数据
                sheetNo++;
            }
            excelWriter.finish();
            templateResVO.setBytes(outputStream.toByteArray());
        } catch (Exception e) {
            log.error("DynamicTemplateService.generateTemplate 生成模版异常, templateSourceDataVO={}", JSON.toJSONString(templateResVO), e);
        }
        ByteArrayInputStream inputStream = new ByteArrayInputStream(templateResVO.getBytes());
        try (FileOutputStream fileOutputStream = new FileOutputStream(pathName)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, bytesRead);
            }
            System.out.println("文件写入完成！");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    protected List<String> getSaleUnitList(){
        List<String> list = new ArrayList<>();
        list.add("个");
        list.add("件");
        list.add("套");
        list.add("卷");
        list.add("包");
        return list;
    }

    private WriteSheet getWriteSheet(TemplateSourceDataVO.SheetDataVO sheetDataVO, int sheetNo) {
        // 写文件
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetNo, sheetDataVO.getSheetName())
            .head(sheetDataVO.getClazz())
            .build();
        return writeSheet;
    }


    @SneakyThrows
    @Test
    public void test3(){
        String pathName = "/Users/<USER>/Downloads/file-zip/中国修改2.xlsx";
        TaskBizTypeEnum taskBizTypeEnum = TaskBizTypeEnum.forCode(43);
        AssertValidation.isEmpty(taskBizTypeEnum,String.format("任务类型不存在 taskType %s",43));
        // 查询客户信息
        // 设置模板数据
        TemplateSourceDataVO templateSourceDataVO = new TemplateSourceDataVO();
        templateSourceDataVO.setExcelName(taskBizTypeEnum.getName());
        TemplateSourceDataVO.SheetDataVO sheetDataVO = new TemplateSourceDataVO.SheetDataVO();
        sheetDataVO.setSheetName("clientCode");
        // 表头
        List<List<String>> headList = Lists.newArrayList();
        headList.add(Lists.newArrayList("* 客户编码"));
        headList.add(Lists.newArrayList("* SKU id"));
        sheetDataVO.setHeadList(headList);
        // 下拉数据
        DropdownDataVO dropdownDataVO = DropdownDataVO.builder().maxRowNum(taskBizTypeEnum.getMax())
            .eachItemDataVOList(Lists.newArrayList(new DropdownDataVO.EachItemDataVO(0, "_clientCode", Lists.newArrayList("aaa","bbb")))).build();
        sheetDataVO.setDropdownDataVO(dropdownDataVO);
        templateSourceDataVO.setSheetDataVOList(Lists.newArrayList(sheetDataVO));

        TemplateResVO templateResVO = dynamicTemplateService.generateTemplate(templateSourceDataVO);
        ByteArrayInputStream inputStream = new ByteArrayInputStream(templateResVO.getBytes());
        try (FileOutputStream fileOutputStream = new FileOutputStream(pathName)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, bytesRead);
            }
            System.out.println("文件写入完成！");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


   /* @Override
    public void export(TaskDTO<CustomerMkuAvailableDTO> task){
        if(task!=null){
            log.info("BatchableCustomerMkuExportHandler.export {}", task.getTaskId());
            String resultName = RESULT_FOLDER + task.getTaskBizTypeEnum().getName() + Constant.UNDER_LINE + System.currentTimeMillis() + ".xlsx";
            ByteArrayOutputStream targetOutputStream = new ByteArrayOutputStream();
            int done = 0;
            long index = 1L;
            try (ExcelWriter excelWriter = EasyExcel.write(targetOutputStream,CustomerMkuAvailableDTO.class).excludeColumnFieldNames(Sets.newHashSet("result")).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet(0, "result").build();
                writeSheet.setNeedHead(true);
                //导出查询入参复原及重置检索分页参数
                CustomerMkuQueryVO req = JSONObject.parseObject(task.getReqJson(), CustomerMkuQueryVO.class);
                req.setIndex(DEFAULT_INDEX);
                req.setSize(DEFAULT_SIZE);
                List<CustomerMkuVO> batchRes = customerMkuManageService.pageAndSaleState(req);
                while (CollectionUtils.isNotEmpty(batchRes) && done < MAX_ITEM){
                    excelWriter.write(po2Excel(batchRes), writeSheet);
                    done = done + batchRes.size();
                    index = index + 1;
                    req.setIndex(index);
                    batchRes = customerMkuManageService.pageAndSaleState(req);
                }
                task.setEndTime(System.currentTimeMillis());
                excelWriter.finish();
                try (ByteArrayInputStream inputStream = new ByteArrayInputStream(targetOutputStream.toByteArray())) {
                    task.setResultUrl(s3Utils.upload(inputStream, FileTypeEnum.BATCH_FILE.getCode(), resultName));
                    taskMangeService.update(new TaskVO(task.getTaskId(), TaskStatusEnum.SUCCESS, task.getResultUrl()));
                }
            } catch (Exception e) {
                log.error("BatchableCustomerMkuExportHandler.export error, target:{} ", task.getTaskId(), e);
                throw new TaskExportException(task.getTaskId() + e.getMessage());
            } finally {
                log.info("BatchableCustomerMkuExportHandler.export 任务「{}」执行完毕:{},文件地址:{}", task.getTaskId(), task.getOssPutMd5(), task.getResultUrl());
            }
        }
    }

    private List<CustomerMkuAvailableDTO> po2Excel(List<CustomerMkuVO> records) {
        List<CustomerMkuAvailableDTO> result = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for(CustomerMkuVO vo : records){
            CustomerMkuAvailableDTO target = new CustomerMkuAvailableDTO();
            BeanUtils.copyProperties(vo,target);
            target.setBindStatus(vo.getBindStatus().getDesc());
            target.setUpdateTime(sdf.format(vo.getUpdateTime()));
            target.setSzgyInPoolFlag(vo.getSzgyInPoolFlag()!=null&&vo.getSzgyInPoolFlag()?"是":"否");
            target.setTggyInPoolFlag(vo.getTggyInPoolFlag()!=null&&vo.getTggyInPoolFlag()?"是":"否");
            target.setJdSkuPurchasePriceFlag(vo.getJdSkuPurchasePriceFlag()!=null&&vo.getJdSkuPurchasePriceFlag()?"是":"否");
            result.add(target);
        }
        return result;
    }*/

    @Test
    public void arrayList(){
        List<String> list = new ArrayList<>();
        /*list.add("aaa");
        list.add("bbb");
        list.add(null);
        list.add("ccc");*/
        String join = String.join(Constant.COMMA, list);
        System.out.println(join);


    }


    @Test
    public void parseExcel(){
        List<SkuAmendUpdateCNExcelDTO> excelDTOList = new ArrayList<>();
        SkuAmendUpdateCNExcelDTO dto = new SkuAmendUpdateCNExcelDTO();
        dto.setSkuId("80000023580");
        //dto.setJdSkuId("100000001");
        dto.setSpuTitleCN("跨境测试商品1修改");
        dto.setSpecification("型号");
        dto.setKeyPhrasesCN("中文关键字");
        dto.setBrandId("20001");
        //dto.setOriginCountry("OriginCountry");
        //dto.setProductionCycle("2.3");
        dto.setSaleUnitStr("公斤//kg//kg//kg");
        dto.setWeight("100");
        dto.setLength("200");
        dto.setWidth("300");
        dto.setHeight("400");
        dto.setUpcCode("30.01");
        dto.setHsCode("HsCode");
        dto.setMainImg("主图");
        dto.setDetailImg1("第一张图");
        dto.setDetailImg2("第二张图");
        dto.setDetailImg3("第三张图");
        dto.setDetailImg4("第四张图");
        dto.setSkuImage("sku的图");
        dto.setSpuTitleTH("泰国名称");
        // dto.setSpecificationTH("泰国规格尺码");
        dto.setKeyPhrasesTH("关键字泰国");
        dto.setPcDescriptionTH("泰国的商品详情");
        dto.setSpuTitleEN("英文名称");
        // dto.setSpecificationEN("英文规格尺码");
        dto.setKeyPhrasesEN("英文关键字");
        dto.setPcDescriptionEN("英文商品详情");
        dto.setSpuTitleVN("越南名称");
        // dto.setSpecificationVN("越南规格尺码");
        dto.setKeyPhrasesVN("越南关键字");
        dto.setPcDescriptionVN("越南的商品详情");
        dto.setPcDescriptionCN("中国的商品详情");
        excelDTOList.add(dto);
        SkuAmendUpdateCNExcelDTO dto1 = new SkuAmendUpdateCNExcelDTO();
        dto1.setSkuId("80000023580");
        //dto1.setJdSkuId("100000001");
        dto1.setSpuTitleCN("跨境测试商品1修改");
        //excelDTOList.add(dto1);
        List<SkuAmendUpdateCNExcelDTO> mkuMaterialExcelDTOList = filterValid(excelDTOList,"wangpeng965");
        if (CollectionUtils.isEmpty(mkuMaterialExcelDTOList)) {
            return;
        }
        List<SkuUpdateApiDTO> skuUpdateApiDTOList = SkuConvert.INSTANCE.excelCNList2ApiList(excelDTOList);
        List<SpuUpdateApiDTO> spuUpdateApiDTOList = SpuConvert.INSTANCE.excelCNList2ApiList(excelDTOList);
        log.info("DisposableSkuAmendUpdateCNImportHandler.run, sku:{} ", JSON.toJSONString(skuUpdateApiDTOList));
        handleExcel2SkuApi(mkuMaterialExcelDTOList,skuUpdateApiDTOList);
        handleExcel2SpuApi(mkuMaterialExcelDTOList,spuUpdateApiDTOList);

        System.out.println("sku=" + JSON.toJSONString(skuUpdateApiDTOList));
        System.out.println("spu=" + JSON.toJSONString(spuUpdateApiDTOList));
    }

    private void handleExcel2SkuApi(List<SkuAmendUpdateCNExcelDTO> excelDTOList,List<SkuUpdateApiDTO> skuExcelApiDTOList){
        Map<String,SkuAmendUpdateCNExcelDTO> excelMap= excelDTOList.stream().collect(Collectors.toMap(SkuAmendUpdateCNExcelDTO::getSkuId, Function.identity()));
        for(SkuUpdateApiDTO skuExcelApiDTO : skuExcelApiDTOList){
            SkuAmendUpdateCNExcelDTO skuAmendUpdateINTExcelDTO = excelMap.get(skuExcelApiDTO.getSkuId().toString());
            List<String> detailList = new ArrayList<>();
            if(Objects.nonNull(skuAmendUpdateINTExcelDTO.getDetailImg1())){
                detailList.add(skuAmendUpdateINTExcelDTO.getDetailImg1());
            }
            if(Objects.nonNull(skuAmendUpdateINTExcelDTO.getDetailImg2())){
                detailList.add(skuAmendUpdateINTExcelDTO.getDetailImg2());
            }
            if(Objects.nonNull(skuAmendUpdateINTExcelDTO.getDetailImg3())){
                detailList.add(skuAmendUpdateINTExcelDTO.getDetailImg3());
            }
            if(Objects.nonNull(skuAmendUpdateINTExcelDTO.getDetailImg4())){
                detailList.add(skuAmendUpdateINTExcelDTO.getDetailImg4());
            }
            if(CollectionUtils.isNotEmpty(detailList)) {
                String detailImg = String.join("#", detailList);
                skuExcelApiDTO.setDetailImg(detailImg);
            }

            if(Objects.nonNull(skuAmendUpdateINTExcelDTO.getSaleUnitStr())){
                Map<String, Integer> saleUnitMap = rpcSpuService.getSaleUnitMap();
                skuExcelApiDTO.setSaleUnit(saleUnitMap.getOrDefault(skuAmendUpdateINTExcelDTO.getSaleUnitStr(),1));
            }
        }
    }

    private void handleExcel2SpuApi(List<SkuAmendUpdateCNExcelDTO> excelDTOList,List<SpuUpdateApiDTO> spuExcelApiDTOList){
        CurrencyLangEnum[] values = CurrencyLangEnum.values();
        Map<String,SkuAmendUpdateCNExcelDTO> excelMap= excelDTOList.stream().collect(Collectors.toMap(SkuAmendUpdateCNExcelDTO::getSkuId, Function.identity()));
        for(SpuUpdateApiDTO spuExcelApiDTO : spuExcelApiDTOList){
            SkuAmendUpdateCNExcelDTO skuAmendUpdateINTExcelDTO = excelMap.get(spuExcelApiDTO.getSkuId().toString());
            Map<String, Object> map = skuAmendUpdateINTExcelDTO.toMap();
            List<SpuLangApiDTO> spuLangList = new ArrayList<>();
            createSpuLangApiDTO(map,spuLangList,values);
            spuExcelApiDTO.setSpuLangList(spuLangList);

            List<String> detailList = new ArrayList<>();
            if(Objects.nonNull(skuAmendUpdateINTExcelDTO.getDetailImg1())){
                detailList.add(skuAmendUpdateINTExcelDTO.getDetailImg1());
            }
            if(Objects.nonNull(skuAmendUpdateINTExcelDTO.getDetailImg2())){
                detailList.add(skuAmendUpdateINTExcelDTO.getDetailImg2());
            }
            if(Objects.nonNull(skuAmendUpdateINTExcelDTO.getDetailImg3())){
                detailList.add(skuAmendUpdateINTExcelDTO.getDetailImg3());
            }
            if(Objects.nonNull(skuAmendUpdateINTExcelDTO.getDetailImg4())){
                detailList.add(skuAmendUpdateINTExcelDTO.getDetailImg4());
            }
            if(CollectionUtils.isNotEmpty(detailList)) {
                String detailImg = String.join("#",detailList);
                spuExcelApiDTO.setDetailImg(detailImg);
            }

            if(Objects.nonNull(skuAmendUpdateINTExcelDTO.getSaleUnitStr())){
                Map<String, Integer> saleUnitMap = rpcSpuService.getSaleUnitMap();
                spuExcelApiDTO.setSaleUnit(saleUnitMap.getOrDefault(skuAmendUpdateINTExcelDTO.getSaleUnitStr(),1));
            }
        }
    }


    private void createSpuLangApiDTO(Map<String, Object> map,List<SpuLangApiDTO> list,CurrencyLangEnum[] values){
        log.info("DisposableSkuAmendUpdateCNImportHandler.createSpuLangApiDTO start req:{}, list:{} ",JSON.toJSONString(map), JSON.toJSONString(list));
        for (CurrencyLangEnum langEnum:values){
            String spuTitle = (String)map.get("spuTitle" + langEnum.name());
            String pcDescription = (String)map.get("pcDescription"+langEnum.name());
            String keyPhrases = (String)map.get("keyPhrases"+langEnum.name());
            String qualifier = (String)map.get("qualifier"+langEnum.name());
            if(Objects.nonNull(spuTitle) || Objects.nonNull(pcDescription)
                || Objects.nonNull(keyPhrases) || Objects.nonNull(qualifier)){
                SpuLangApiDTO spuLangApiDTO = new SpuLangApiDTO();
                spuLangApiDTO.setLang(langEnum.getLang());
                spuLangApiDTO.setSpuTitle(spuTitle);
                spuLangApiDTO.setPcDescription(pcDescription);
                spuLangApiDTO.setKeyPhrases(keyPhrases);
                spuLangApiDTO.setQualifier(qualifier);
                list.add(spuLangApiDTO);
            }
        }
        log.info("DisposableSkuAmendUpdateCNImportHandler.createSpuLangApiDTO end req:{}, list:{} ",JSON.toJSONString(map), JSON.toJSONString(list));
    }

    private List<SkuAmendUpdateCNExcelDTO> filterValid(List<SkuAmendUpdateCNExcelDTO> list,String operator){
        log.info("DisposableSkuAmendUpdateCNImportHandler.filterValid req:{}, operator:{} ",JSON.toJSONString(list), operator);
        List<SkuAmendUpdateCNExcelDTO> validList = Lists.newArrayList();
        for (SkuAmendUpdateCNExcelDTO dto:list){
            if(dto.getValid()){
                dto.setUpdater(operator);
                validList.add(dto);
            }
        }
        return validList;
    }

    @Test
    public void templateProductExport(){
        String pathName = "/Users/<USER>/Desktop/test.xlsx";
        try {
            TemplateReqDTO reqDTO = new TemplateReqDTO();
            reqDTO.setOperator("wangpeng965");
            reqDTO.setTaskBizType(1007);
            reqDTO.setParamJson("{\"pin\":\"zhangjin176\",\"orginId\":\"00135318\"}");
            //DataResponse<TemplateResDTO> dataResponse = iscTaskTemplateApiService.generateTemplate(reqDTO);
            TemplateReqVO reqVO = TemplateConvert.INSTANCE.dto2Vo(reqDTO);
            TemplateResVO templateResVO = dynamicTemplateService.generateTemplateByParam(reqVO);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(templateResVO.getBytes());
            try (FileOutputStream fileOutputStream = new FileOutputStream(pathName)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    fileOutputStream.write(buffer, 0, bytesRead);
                }
                System.out.println("文件写入完成！");
            } catch (IOException e) {
                e.printStackTrace();
            }

            //String url = s3Utils.upload(inputStream, FileTypeEnum.BATCH_FILE.getCode(), "中国修改.xlsx");
            //System.out.println("url地址="+url);
        } catch (Exception e) {
            log.error("productDataTest.templateExport 模板生成异常 reqDTO={}", JSON.toJSONString(null),e);
        }
    }

    @Test
    public void testDownLoadJdMainImgUrl() {
        String url = "http://img11.360buyimg.com/n1/jfs/t1/301283/2/19865/42462/6867472cF0ad58ede/bc6d8095008ced7b.jpg";
        try {
            // 根据文件URL下载
            byte[] bytes = HttpUtil.downloadBytes(url);
            String suffix = FileUtil.getSuffix(url);
            String randomStr = RandomUtil.randomString(5);
            String key = "trans" + System.nanoTime() + randomStr + Constant.DOT + suffix;
            // 将下载的文件流上传到文件服务器
            String imageUrl = s3Utils.upload(new ByteArrayInputStream(bytes), FileTypeEnum.DESCRIPTION_IMAGE.getCode(), key);
            log.info("ImageTranslateServiceImpl.downloadAndUploadS3 翻译后的图片上传到文件服务器 imageUrl={}", imageUrl);
        } catch (Exception e) {
            log.error("翻译后图片下载、上传到s3服务器时发生异常,url={}", url, e);
        }
    }


    @Test
    public void downloadVCCreateTemplate(){
        String pathName = "/Users/<USER>/Desktop/test/vcCreateTemplate.xlsx";
        try {
            TemplateReqDTO reqDTO = new TemplateReqDTO();
            reqDTO.setOperator("sunlei61");
            reqDTO.setTaskBizType(1003);
            BatchParamVO batchParamVO = new BatchParamVO();
            batchParamVO.setSourceCountryCode(CountryConstant.COUNTRY_TH);
            batchParamVO.setAttributeScope(CountryConstant.COUNTRY_TH);
            batchParamVO.setLang(LangConstant.LANG_ZH);
            batchParamVO.setExport(0);
            batchParamVO.setCategoryId(String.valueOf(40426220));
            batchParamVO.setSupplierCode("testoffline30301212");
            reqDTO.setParamJson(JSON.toJSONString(batchParamVO));

            TemplateReqVO reqVO = TemplateConvert.INSTANCE.dto2Vo(reqDTO);
            TemplateResVO templateResVO = dynamicTemplateService.generateTemplateByParam(reqVO);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(templateResVO.getBytes());
            try (FileOutputStream fileOutputStream = new FileOutputStream(pathName)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    fileOutputStream.write(buffer, 0, bytesRead);
                }
                System.out.println("文件写入完成！");
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            log.error("productDataTest.downloadVCCreateTemplate vc批量发品模板生成异常 reqDTO={}", JSON.toJSONString(null),e);
        }
    }
}
