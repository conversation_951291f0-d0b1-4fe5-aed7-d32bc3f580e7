package com.jdi.isc.task.center.service.settlement;
import com.jd.jmq.client.connection.ClusterTransportManager;
import com.jd.jmq.client.connection.TransportConfig;
import com.jd.jmq.client.connection.TransportManager;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.common.message.Message;
import com.alibaba.fastjson.JSON;
import com.jd.jmq.client.producer.Producer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.supplier.SupplierReadApiService;
import com.jdi.isc.product.soa.api.supplier.req.SupplierQueryParam;
import com.jdi.isc.product.soa.api.supplier.res.SupplierAccountRes;
import com.jdi.isc.product.soa.api.supplier.res.SupplierBaseInfoRes;

import com.jdi.isc.task.center.msg.mail.SettlementCreateListener;
import com.jdi.isc.task.center.web.ServiceApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

@Slf4j
@SpringBootTest(classes = ServiceApplication.class)
public class MailToSupplierTest {


    @JmqProducer(name = "jdiIscTaskCenterProducer")
    private Producer producer;


    @Resource
    private SettlementCreateListener settlementCreateListener;
    @Resource
    private SupplierReadApiService supplierReadApiService;
    @Test
    public void testSendToSupplier()
    {

        SupplierBaseInfoRes res =new SupplierBaseInfoRes();
        SupplierQueryParam param = new SupplierQueryParam();
        String supplierCode ="jdivnhtmart";
        param.setSupplierCode(supplierCode);
        DataResponse<List<SupplierAccountRes>> response = supplierReadApiService.queryAccountBySupplierCode(param);

        //response只有一个数据，返回唯一的数据对象,如果返回的供应商CODE不唯一，则报警；增加系统报警提示
        SupplierAccountRes resVo =response.getData().get(0);
        String emailAdress= resVo.getAccountEmail();

        if (null != response && response.getSuccess()) {

            System.out.println(JSON.toJSONString(resVo));

        }


    }

    @Test
    public void TestProduceMassage() throws Exception {
        this.sendMessage("1","2","3");

        System.out.println("结束");

    }

    private void sendMessage(String businessId,Object messageObj,String topic) throws Exception {
        TransportManager manager;
        MessageProducer producer;
        // Group名称
        String group = "jdiiscordercenter";
        // 元数据地址
        String address="test-nameserver.jmq.jd.local:50088";
        // 权限认证令牌
        String token = "ca28064c67f64ecf969b03c7512eadea";
        // 发送超时时长
        int sendTimeout = 300;

        // 实例化连接配置
        TransportConfig config = new TransportConfig();
        // 设置Group
        config.setApp(group);
        // 设置broker地址
        config.setAddress(address);
        // 设置用户名
        config.setUser(group);
        // 设置密码
        config.setPassword(token);
        // 设置超时
        config.setSendTimeout(sendTimeout);

        // 创建集群连接管理器
        manager = new ClusterTransportManager(config);
        manager.start();

        // 创建发送者
        producer = new MessageProducer(manager);
        producer.start();

        String TextContent="{\n" +
                "  \"countryCode\": \"VN\",\n" +
                "  \"requestId\": \"PO202501130001\",\n" +
                "  \"supplierCode\": \"jdivnhtmart\",\n" +
                "  \"status\": \"PENDING\",\n" +
                "  \"settlementOrderTime\": \"2025-01-13 15:23:00\"\n" +
                "}";

        Message message = new Message(/* 主题名 */"jdi_isc_order_settlement_create_msg_test",TextContent, "My business id");

        // 调用消息发送API
        producer.send(message);

        // 当前demo中为了确保消息发送成功所以这里等待了5s，真实生产中千万勿用
        Thread.currentThread().join(5 * 1000);

        // 完成消息发送逻辑后，需回收相关资源
        producer.stop();
        manager.stop();
    }




}


