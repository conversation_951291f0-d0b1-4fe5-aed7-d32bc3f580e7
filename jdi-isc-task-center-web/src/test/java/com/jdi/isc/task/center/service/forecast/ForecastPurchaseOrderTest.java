package com.jdi.isc.task.center.service.forecast;

import com.alibaba.fastjson.JSONObject;
import com.jdi.isc.task.center.domain.forecast.ForecastPurchaseOrderTaskPdfVO;
import com.jdi.isc.task.center.domain.waybill.OrderWaybillPdfInfoVO;
import com.jdi.isc.task.center.service.BaseTest;
import com.jdi.isc.task.center.service.work.task.handler.pdf.ForecastPurchaseOrderPdfService;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

/**
 * 预报备采购单pdf打印
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/30
 */
public class ForecastPurchaseOrderTest extends BaseTest {

    @Resource
    private ForecastPurchaseOrderPdfService forecastPurchaseOrderPdfService;

    @Test
    public void printWaybillPdf(){
        String json = "{\"forecastOrderId\":\"FO202406010001\",\"localeList\":[\"zh\",\"en\",\"vi\"],\"supplierCode\":\"SUP123456\",\"skuNum\":100,\"skuKindNum\":5,\"forecastOrderStatus\":2,\"forecastOrderType\":1,\"countryCode\":\"CN\",\"enterpriseWarehouseId\":\"WH9876\",\"warehouseNo\":\"W2024A\",\"warehouseName\":\"北京备货仓\",\"forecastOrderExtInfo\":\"{\\\"extra\\\":\\\"info\\\"}\",\"version\":1,\"parentForecastOrderId\":\"FO202405310001\",\"splitForecastOrderId\":\"FO202406010001-1\",\"storehouseId\":\"SH001\",\"enterWarehouseNo\":\"EN20240601001\",\"forecastOrderWareList\":[{\"skuId\":100000001,\"skuNameMap\":{\"zh\":\"商品1\",\"en\":\"test1\",\"vi\":\"dsfds1\"},\"skuNum\":50},{\"skuId\":\"100000002\",\"skuNameMap\":{\"zh\":\"商品2\",\"en\":\"test2\",\"vi\":\"dsfds2\"},\"skuNum\":50}],\"forecastOrderConsignee\":{\"consigneeAddressEncrypt\":\"detail address\",\"consigneeName\":\"张三\",\"consigneePhone\":\"13800138000\",\"consigneeAddress\":\"北京市朝阳区XX路\",\"countyNameMap\":{\"zh\":\"地区1\",\"en\":\"count1\",\"vi\":\"jdflsdjfl1\"},\"provinceNameMap\":{\"zh\":\"p1\",\"en\":\"p11\",\"vi\":\"j1\"},\"cityNameMap\":{\"zh\":\"city\",\"en\":\"city\",\"vi\":\"city\"},\"countryNameMap\":{\"zh\":\"guojia1\",\"en\":\"guojia1\",\"vi\":\"guojia1\"}}}";
        ForecastPurchaseOrderTaskPdfVO pdfInfoVO = JSONObject.parseObject(json, ForecastPurchaseOrderTaskPdfVO.class);
        String url = forecastPurchaseOrderPdfService.buildPdf(pdfInfoVO);
        System.out.println(url);
    }
}