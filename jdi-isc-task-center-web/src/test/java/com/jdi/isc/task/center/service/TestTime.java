package com.jdi.isc.task.center.service;

import com.jdi.isc.task.center.common.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

import java.util.Date;

public class TestTime extends BaseTest {

    @Test
    public void strConvertLong() {
        String str = "2025-07-02 15:10:00";
        if (StringUtils.isBlank(str)) {
            System.out.println(str);
        }
        Date date = DateUtil.stringToDate(str);
        if(date == null){
            System.out.println("date is null");
        }
        System.out.println(date.getTime());
    }
}
