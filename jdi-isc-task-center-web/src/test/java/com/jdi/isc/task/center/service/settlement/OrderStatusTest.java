package com.jdi.isc.task.center.service.settlement;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.isc.task.center.api.common.enums.MailModelTypeEnum;
import com.jdi.isc.task.center.api.dto.mail.MailParamDTO;
import com.jdi.isc.task.center.domain.enums.MailMessageTypeEnum;
import com.jdi.isc.task.center.domain.mail.biz.MailMessageVO;
import com.jdi.isc.task.center.domain.settlement.OrderUpdateEmailVO;
import com.jdi.isc.task.center.service.manage.mail.MailManagerService;
import com.jdi.isc.task.center.web.ServiceApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@SpringBootTest(classes = ServiceApplication.class)
public class OrderStatusTest {

    private static final Map<Integer, String> statusToModelTypeMap = new HashMap<>();

    private static final String ORDER_ID = "newOrderId";
    private static final String PIN_NAME = "pin";


    @Resource
    private MailManagerService mailManagerService;

    @LafValue("jdi.isc.task.mail.order.cfg")
    private String OrderEmailCfg;

    private String testMsg ="{\n" +
            "    \"approvalStatus\": 1,\n" +
            "    \"clientCode\": \"OqDcUfH46z7KIMesbtHQ\",\n" +
            "    \"contractNum\": \"ISPG-20240319111956\",\n" +
            "    \"countryCode\": \"BR\",\n" +
            "    \"createTime\": 1717076329000,\n" +
            "    \"creator\": \"Bydauto_th_local\",\n" +
            "    \"currency\": \"THB\",\n" +
            "    \"id\": 35917,\n" +
            "    \"mkuKindNum\": 4,\n" +
            "    \"mkuNum\": 35,\n" +
            "    \"orderCreateTime\": 1717076320000,\n" +
            "    \"orderExtInfo\": \"{\\\"serviceIp\\\":\\\"**************\\\",\\\"systemCode\\\":\\\"open-soa\\\",\\\"userIp\\\":\\\"**************\\\",\\\"localeList\\\":[\\\"zh\\\",\\\"en\\\",\\\"th\\\"]}\",\n" +
            "    \"orderFreightPrice\": 0.0000,\n" +
            "    \"orderId\": 240530213800006,\n" +
            "    \"orderShowStatus\": 31,\n" +
            "    \"orderStatus\": 3,\n" +
            "    \"orderTaxes\": 467.2500,\n" +
            "    \"orderTotalPrice\": 7139.9000,\n" +
            "    \"orderType\": 2,\n" +
            "    \"parentOrderId\": 0,\n" +
            "    \"pin\": \"Bydauto_th_local\",\n" +
            "    \"shippingType\": -1,\n" +
            "    \"sourceCode\": \"WIOP\",\n" +
            "    \"sourcePriceInfo\": \"{\\\"currencySource\\\":\\\"CNY\\\",\\\"exchangeRate\\\":4.99,\\\"waresSaleTotalPrice\\\":1337.2000}\",\n" +
            "    \"splitOrderId\": 0,\n" +
            "    \"thirdExtInfo\": \"{\\\"thrPurchaserAccount\\\":\\\"bydautoTh\\\",\\\"sapOrderNo\\\":\\\"**********\\\"}\",\n" +
            "    \"thirdOrderId\": \"****************\",\n" +
            "    \"updateTime\": *************,\n" +
            "    \"updater\": \"Bydauto_th_local\",\n" +
            "    \"validState\": 1,\n" +
            "    \"version\": 10,\n" +
            "    \"waresSaleTotalPrice\": 6672.6500,\n" +
            "    \"waresSaleTotalPriceRmb\": 6672.6500,\n" +
            "    \"yn\": 1\n" +
            "}\n";
    @Test
    public void testOrderSendToEmail()
    {
        String countryCode;

        //获取消息的值，并转化成实例化对象
        OrderUpdateEmailVO vo = new OrderUpdateEmailVO();
        vo = JSONObject.parseObject(testMsg, OrderUpdateEmailVO.class);
        System.out.println(vo);


        JSONObject jsonObject1 =   getDuccContent(OrderEmailCfg,vo);
        System.out.println(jsonObject1);
        if (jsonObject1 == null) {
            log.error("【订单状态变更邮件发送测试失败】: 未找到国家[{}]的DUCC配置或邮件发送功能未启用", vo.getCountryCode());
            return;
        }


        MailMessageVO mailMessageVO = buildParam(vo,jsonObject1);

        //mailMessageVO.setSupplierCode("Vicotr0821");

        System.out.println(mailMessageVO);

        try {
            mailManagerService.sendMail(mailMessageVO);
        } catch (Exception e) {
            log.error("【结算单邮件发送异常】SettlementCreateListener.onMessage param:{},err:{},e:", testMsg, e.getMessage(), e);
            throw e;
        }

    }

    private MailMessageVO buildParam(OrderUpdateEmailVO vo,JSONObject obj1) {
        MailMessageVO mailMessage = new MailMessageVO();
        mailMessage.setBusinessCode(vo.getOrderId().toString()+MailModelTypeEnum.SEND_ORDER_STATUS_NOTICE.getCode());
        mailMessage.setCountryCode(vo.getCountryCode());
        mailMessage.setModelType(MailModelTypeEnum.SEND_ORDER_STATUS_NOTICE.getCode());
        mailMessage.setMessageType(MailMessageTypeEnum.INSTANT_MESSAGE.getCode());//及时发送
        mailMessage.setOperator("OrderStatusChangeListener_JMQ");


        //构建发送人列表
        MailParamDTO mailParamDTO = new MailParamDTO();
        mailParamDTO.setRecipientEmail(obj1.getString("recipientEmail"));
        mailParamDTO.setCopyToMail(obj1.getString("copyToMail"));
        mailParamDTO.setSendData(createContext(vo.getOrderId(),vo.getPin()));
        mailMessage.setMailParamDTO(mailParamDTO);


        return mailMessage;
    }
    public Map<String, Object> createContext(Long OrderId, String pinName) {
        Map<String, Object> context = new HashMap<>();
        //替换模版中的结算单号
        context.put(ORDER_ID, OrderId);
        //替换模版中的供应商名称
        context.put(PIN_NAME, pinName);
        return context;
    }


    private JSONObject getDuccContent(String  duccContent,OrderUpdateEmailVO vo) {

        //获取配置文件中的国家信息
        JSONArray jsonArray = JSONArray.parseArray(duccContent);
        JSONObject jsonObject =new JSONObject();
        // 遍历JSONArray,确认存在对应的国家信息，且该国家开启邮件发送状态
        for (int i = 0; i < jsonArray.size(); i++) {
            jsonObject = jsonArray.getJSONObject(i);

            String countryCodeCfg = jsonObject.getString("countryCode");
            String enabled = jsonObject.getString("enabled");
            if (org.apache.commons.lang3.StringUtils.equals(vo.getCountryCode(), countryCodeCfg) && enabled.equals("true"))
            {
                return jsonObject;
            }
        }
        return null;
    }
}