package com.jdi.isc.task.center.service.task;

import com.google.common.collect.Sets;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;

import com.jdi.isc.task.center.service.BaseTest;
import com.jdi.isc.task.center.service.manage.task.strategy.TaskStrategyService;
import com.jdi.isc.task.center.service.work.task.handler.wimpImport.tax.ProductCustomsValuationBatchImportHandler;
import com.jdi.isc.task.center.service.work.task.handler.wimpImport.tax.ThHsCodeBatchImportHandler;
import com.xxl.job.core.util.ShardingUtil;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.Objects;

import static com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum.PRODUCT_CUSTOMS_VALUATION;
import static com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum.PRODUCT_HS_CODE_TH_IMPORT;

/**
 * 导入任务测试
 * <AUTHOR>
 * @date 2024/8/28
 **/
public class ProductCustomsValuationTaskTest extends BaseTest {

    @Resource
    private TaskStrategyService wimpTaskServiceImpl;


    @Resource
    private ProductCustomsValuationBatchImportHandler productCustomsValuationImportHandler;

    @Resource
    private ThHsCodeBatchImportHandler thHsCodeBatchImportHandler;


    /**
     * 测试执行跨境商品估价导入任务
     */
    @Test
    public void executeWimpTask() {
        ShardingUtil.ShardingVO shardingVO = new ShardingUtil.ShardingVO(0, 1);
        ShardingUtil.setShardingVo(shardingVO);
        TaskDTO task = wimpTaskServiceImpl.pullTask(Sets.newHashSet(PRODUCT_CUSTOMS_VALUATION.getCode()));
        if(Objects.nonNull(task)){
            try {
                productCustomsValuationImportHandler.in(task).start();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Test
    public void executeImportHsCodeTask() {
        ShardingUtil.ShardingVO shardingVO = new ShardingUtil.ShardingVO(0, 1);
        ShardingUtil.setShardingVo(shardingVO);
        TaskDTO task = wimpTaskServiceImpl.pullTask(Sets.newHashSet(PRODUCT_HS_CODE_TH_IMPORT.getCode()));
        if(Objects.nonNull(task)){
            try {
                thHsCodeBatchImportHandler.in(task).start();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }
}
