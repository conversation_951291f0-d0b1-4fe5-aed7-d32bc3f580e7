package com.jdi.isc.task.center.service.file;


import com.jdi.isc.task.center.service.BaseTest;
import com.jdi.isc.task.center.service.manage.translate.TranslateService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description：ImageUrlDownloadTest
 * @Date 2025-08-22
 */
@Slf4j
public class ImageUrlDownloadTest extends BaseTest {

    @Resource
    private TranslateService translateService;


    @Test
    public void downloadImageUrl() {
        String url = "http://img11.360buyimg.com/n1/jfs/t1/147926/12/22074/123883/61ea12d8Ec4e529cb/929b59f78e0f1f50.jpg";
        String downUrl = translateService.downloadAndUploadS3(url);
        System.out.println(downUrl);
    }
}
