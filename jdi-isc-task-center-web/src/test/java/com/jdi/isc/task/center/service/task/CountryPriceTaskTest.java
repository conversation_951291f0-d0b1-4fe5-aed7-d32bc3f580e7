package com.jdi.isc.task.center.service.task;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.domain.enums.TaskStrategyEnum;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;
import com.jdi.isc.task.center.domain.task.excel.ViCustomsAffairsImportExcelDTO;
import com.jdi.isc.task.center.domain.task.vo.TaskStrategyVO;
import com.jdi.isc.task.center.service.BaseTest;
import com.jdi.isc.task.center.service.manage.task.strategy.TaskStrategyService;
import com.jdi.isc.task.center.service.work.task.frame.BaseJob;
import com.jdi.isc.task.center.service.work.task.handler.wimpImport.DisposableViCustomsAffairsImportHandler;
import com.jdi.isc.task.center.service.work.task.handler.wimpImport.price.DisposableSkuCountryPriceImportHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.ShardingUtil;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.Objects;

public class CountryPriceTaskTest  extends BaseTest {

    @Resource
    private TaskStrategyService wimpTaskServiceImpl;

    @Resource
    private DisposableSkuCountryPriceImportHandler disposableSkuCountryPriceImportHandler;

    @Test
    public void executeWimpTask() {
        ShardingUtil.ShardingVO shardingVO = new ShardingUtil.ShardingVO(0, 1);
        ShardingUtil.setShardingVo(shardingVO);
        TaskDTO task = wimpTaskServiceImpl.pullTask(Sets.newHashSet(TaskBizTypeEnum.UPDATE_SKU_COUNTRY_PRICE_BATCH.getCode()));
        if(Objects.nonNull(task)){
            try {
                disposableSkuCountryPriceImportHandler.in(task).start();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }
}
