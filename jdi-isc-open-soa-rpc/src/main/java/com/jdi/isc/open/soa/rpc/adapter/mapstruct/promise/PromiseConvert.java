package com.jdi.isc.open.soa.rpc.adapter.mapstruct.promise;

import com.jdi.isc.fulfillment.soa.api.promiseTime.req.PromiseTimeMkuReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.promiseTime.res.PromiseTimeInfoResApiDTO;
import com.jdi.isc.fulfillment.soa.api.promiseTime.res.PromiseTimeResApiDTO;
import com.jdi.isc.open.soa.domain.promise.biz.PromiseInfoVO;
import com.jdi.isc.open.soa.domain.promise.req.PromiseMkuReqDTO;
import com.jdi.isc.open.soa.domain.promise.res.PromiseMkuResDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/22
 **/

@Mapper
public interface PromiseConvert {

    PromiseConvert INSTANCE = Mappers.getMapper(PromiseConvert.class);


    PromiseTimeMkuReqApiDTO promiseMkuReqDto2ApiDTO(PromiseMkuReqDTO input);

    List<PromiseTimeMkuReqApiDTO> listPromiseMkuReqDto2ApiDTO(List<PromiseMkuReqDTO> input);

    List<PromiseMkuResDTO> listPromiseTimeResApiDto2Vo(List<PromiseTimeResApiDTO> input);

    @Mappings({
            @Mapping(source = "shipGoodsDays", target = "deliveryTime"),
            @Mapping(source = "promiseResList", target = "promiseMkuResDtos")
    })
    PromiseInfoVO.Response promiseTimeInfoResApiDtoVo(PromiseTimeInfoResApiDTO input);

    Map<Long, PromiseInfoVO.Response> mapPromiseTimeInfoResApiDTO2Vo(Map<Long, PromiseTimeInfoResApiDTO> input);

}
