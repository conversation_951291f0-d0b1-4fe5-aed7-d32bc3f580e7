package com.jdi.isc.open.soa.rpc.customerMku.impl;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.open.soa.rpc.customerMku.IscProductSoaCustomerMkuWriteRpcService;
import com.jdi.isc.product.soa.api.customerMku.IscProductSoaCustomerMkuWriteApiService;
import com.jdi.isc.product.soa.api.customerMku.req.MkuCheckBindReqDTO;
import com.jdi.isc.product.soa.api.customerMku.res.MkuCheckBindResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * CustomerMku写服务
 * <AUTHOR>
 * @date 2024/08/05
 */
@Slf4j
@Service
public class IscProductSoaCustomerMkuWriteRpcServiceImpl implements IscProductSoaCustomerMkuWriteRpcService {

    @Resource
    private IscProductSoaCustomerMkuWriteApiService iscProductSoaCustomerMkuWriteApiService;

    /**
     * 检查并绑定MKU设备。
     * @param dto MKU检查绑定请求对象，包含设备信息等。
     * @return DataResponse对象，包含MKU检查绑定结果。
     */
    @Override
    public DataResponse<MkuCheckBindResDTO> checkAndBind(MkuCheckBindReqDTO dto) {
        DataResponse<MkuCheckBindResDTO> res = null;
        try {
            res = iscProductSoaCustomerMkuWriteApiService.checkAndBind(dto);
        }finally {
            log.info("IscProductSoaCustomerMkuWriteRpcServiceImpl.checkAndBind req:{} , res:{}" , JSON.toJSONString(dto), JSON.toJSONString(res));
        }
        return res;
    }

}
