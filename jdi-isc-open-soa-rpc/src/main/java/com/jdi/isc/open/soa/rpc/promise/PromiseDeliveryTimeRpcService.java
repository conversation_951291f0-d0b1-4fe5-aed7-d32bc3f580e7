package com.jdi.isc.open.soa.rpc.promise;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.fulfillment.soa.api.promiseTime.req.PromiseTimeTextBatchReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.promiseTime.res.PromiseTimeTextResApiDTO;
import com.jdi.isc.open.soa.domain.promise.biz.PromiseInfoVO;

import java.util.Map;

/**
 * 商品履约时效查询服务
 * @Author: zhaojianguo21
 * @Date: 2025/6/21
 */
public interface PromiseDeliveryTimeRpcService {

    /**
     * 批量获取履约时效信息
     * @param input
     * @return
     */
    DataResponse<Map<Long, PromiseInfoVO.Response>> queryPromiseTimeBatch(PromiseInfoVO.Request input);
}
