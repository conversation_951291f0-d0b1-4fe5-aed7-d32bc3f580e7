package com.jdi.isc.open.soa.rpc.customerMku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.customerMku.req.MkuCheckBindReqDTO;
import com.jdi.isc.product.soa.api.customerMku.res.MkuCheckBindResDTO;

/**
 * CustomerMku写服务
 * <AUTHOR>
 * @date 2024/08/05
 */
public interface IscProductSoaCustomerMkuWriteRpcService {

    /**
     * 检查并绑定MKU设备。
     * @param dto MKU检查绑定请求对象，包含设备信息等。
     * @return DataResponse对象，包含MKU检查绑定结果。
     */
    DataResponse<MkuCheckBindResDTO> checkAndBind(MkuCheckBindReqDTO dto);

}
