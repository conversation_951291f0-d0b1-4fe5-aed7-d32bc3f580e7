package com.jdi.isc.open.soa.rpc.promise.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.fulfillment.soa.api.promiseTime.PromiseTimeReadApiService;
import com.jdi.isc.fulfillment.soa.api.promiseTime.req.PromiseTimeInfoBatchReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.promiseTime.req.PromiseTimeMkuReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.promiseTime.req.PromiseTimeTextBatchReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.promiseTime.req.PromiseTimeTextReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.promiseTime.res.PromiseTimeInfoResApiDTO;
import com.jdi.isc.fulfillment.soa.api.promiseTime.res.PromiseTimeTextResApiDTO;
import com.jdi.isc.open.soa.common.constants.Constant;
import com.jdi.isc.open.soa.domain.promise.biz.PromiseInfoVO;
import com.jdi.isc.open.soa.domain.promise.req.PromiseMkuReqDTO;
import com.jdi.isc.open.soa.rpc.adapter.mapstruct.promise.PromiseConvert;
import com.jdi.isc.open.soa.rpc.promise.PromiseDeliveryTimeRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商品履约时效查询服务
 * @Author: zhaojianguo21
 * @Date: 2025/6/21
 */
@Slf4j
@Service
public class PromiseDeliveryTimeRpcServiceImpl implements PromiseDeliveryTimeRpcService {


    @Autowired
    private PromiseTimeReadApiService promiseTimeReadApiService;

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    @ToolKit(umpFlag = false)
    public DataResponse<Map<Long, PromiseInfoVO.Response>> queryPromiseTimeBatch(PromiseInfoVO.Request input) {
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.rpc.promise.impl.PromiseDeliveryTimeRpcServiceImpl.queryPromiseTimeBatch." + active);
        Map<Long, PromiseInfoVO.Response> rpcResData = new HashMap<>();
        try {
            // 分批查询
            List<PromiseMkuReqDTO>  mkuReqDtoList = new ArrayList<>(input.getMkuReqs());
            List<List<PromiseMkuReqDTO>> splitMkuReqDtoList = Lists.partition(mkuReqDtoList, Constant.BATCH_NUM_50);
            for (List<PromiseMkuReqDTO> promiseMkuReqDto : splitMkuReqDtoList){
                List<PromiseTimeMkuReqApiDTO> promiseTimeMkuReqApiDtoList = PromiseConvert.INSTANCE.listPromiseMkuReqDto2ApiDTO(promiseMkuReqDto);

                PromiseTimeInfoBatchReqApiDTO subReqDto = new PromiseTimeInfoBatchReqApiDTO();
                subReqDto.setClientCode(input.getClientCode());
                subReqDto.setMkuReqs(promiseTimeMkuReqApiDtoList);

                DataResponse<Map<Long, PromiseTimeInfoResApiDTO>> subRpcRes = queryPromiseTimeBatchLimitSize(subReqDto);
                if (null==subRpcRes || !Boolean.TRUE.equals(subRpcRes.getSuccess()) || subRpcRes.getData() == null){
                    log.warn("PromiseDeliveryTimeRpcServiceImpl.queryPromiseTimeBatch, queryPromiseTimeBatchLimitSize fail. subReqDto = {}, subRpcRes = {}", JSON.toJSONString(subReqDto), JSON.toJSONString(subRpcRes));
                    continue;
                }
                Map<Long, PromiseInfoVO.Response> subRes = PromiseConvert.INSTANCE.mapPromiseTimeInfoResApiDTO2Vo(subRpcRes.getData());
                rpcResData.putAll(subRes);
            }

            return DataResponse.success(rpcResData);
        } catch (Exception e){
            log.error("PromiseDeliveryTimeRpcServiceImpl.queryPromiseTimeBatch, req = {}, rpcRes = {}", JSON.toJSONString(input), JSON.toJSONString(rpcResData), e);
            Profiler.functionError(callerInfo);
            return DataResponse.error("获取时效信息异常");
        } finally {
            log.info("PromiseDeliveryTimeRpcServiceImpl.queryPromiseTimeBatch, finally, req = {}, rpcRes = {}", JSON.toJSONString(input), JSON.toJSONString(rpcResData));
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    private DataResponse<Map<Long, PromiseTimeInfoResApiDTO>> queryPromiseTimeBatchLimitSize(PromiseTimeInfoBatchReqApiDTO input) {
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.open.soa.rpc.promise.impl.PromiseDeliveryTimeRpcServiceImpl.queryPromiseTimeBatchLimitSize." + active);
        DataResponse<Map<Long, PromiseTimeInfoResApiDTO>> rpcRes = null;
        try {
            if (input.getMkuReqs().size()>Constant.BATCH_NUM_50){
                log.info("PromiseDeliveryTimeRpcServiceImpl.queryPromiseTimeBatchLimitSize, req = {}, rpcRes = {}", JSON.toJSONString(input), JSON.toJSONString(rpcRes));
                return DataResponse.error("单次只允许查询50个品的时效信息。");
            }

            rpcRes = promiseTimeReadApiService.queryPromiseTimeBatch(input);
            if(rpcRes == null){
                log.info("PromiseDeliveryTimeRpcServiceImpl.queryPromiseTimeBatchLimitSize, rpcRes null, req = {}", JSON.toJSONString(input));
                return DataResponse.error("获取时效信息失败");
            }
            return rpcRes;
        } catch (Exception e){
            log.error("PromiseDeliveryTimeRpcServiceImpl.queryPromiseTimeBatchLimitSize, req = {}, rpcRes = {}", JSON.toJSONString(input), JSON.toJSONString(rpcRes), e);
            Profiler.functionError(callerInfo);
            return DataResponse.error("获取时效信息异常");
        } finally {
            log.info("PromiseDeliveryTimeRpcServiceImpl.queryPromiseTimeBatchLimitSize, finally, req = {}, rpcRes = {}", JSON.toJSONString(input), JSON.toJSONString(rpcRes));
            Profiler.registerInfoEnd(callerInfo);
        }
    }
}
