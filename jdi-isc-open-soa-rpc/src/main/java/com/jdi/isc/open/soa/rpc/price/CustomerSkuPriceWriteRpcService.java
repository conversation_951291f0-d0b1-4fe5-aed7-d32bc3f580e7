package com.jdi.isc.open.soa.rpc.price;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.customerSku.res.CustomerSkuPriceDraftApiDTO;
import org.springframework.validation.annotation.Validated;

/**
 * 客制化价格写服务
 * <AUTHOR>
 * @date 2025/06/04
 */
public interface CustomerSkuPriceWriteRpcService {

    /**
     * 保存或更新客户SKU价格(草稿)
     * @description 新增或更新客户SKU价格草稿信息，支持草稿编辑与提交
     * @param input 草稿参数对象
     * @return 操作结果
     */
    DataResponse<String> saveOrUpdate(@Validated CustomerSkuPriceDraftApiDTO input);


}
