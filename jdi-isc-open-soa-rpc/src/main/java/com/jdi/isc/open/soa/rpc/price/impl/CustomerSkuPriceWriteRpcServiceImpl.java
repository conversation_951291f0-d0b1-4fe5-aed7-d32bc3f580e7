package com.jdi.isc.open.soa.rpc.price.impl;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.open.soa.rpc.price.CustomerSkuPriceWriteRpcService;
import com.jdi.isc.product.soa.api.customerSku.IscProductSoaCustomerSkuPriceWriteApiService;
import com.jdi.isc.product.soa.api.customerSku.res.CustomerSkuPriceDraftApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 客制化价格写服务
 * <AUTHOR>
 * @date 2025/06/04
 */
@Slf4j
@Service
public class CustomerSkuPriceWriteRpcServiceImpl implements CustomerSkuPriceWriteRpcService {

    @Resource
    private IscProductSoaCustomerSkuPriceWriteApiService iscProductSoaCustomerSkuPriceWriteApiService;

    @Override
    public DataResponse<String> saveOrUpdate(CustomerSkuPriceDraftApiDTO input) {
        DataResponse<String> res = null;
        try {
            res = iscProductSoaCustomerSkuPriceWriteApiService.saveOrUpdate(input);
        }finally {
            log.info("CustomerSkuPriceWriteRpcServiceImpl.saveOrUpdate req:{} , res:{}" , JSON.toJSONString(input), JSON.toJSONString(res));
        }
        return res;
    }

}
