package com.jdi.isc.product.soa.domain.countryMku.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class CountryPoolSwitchVO {

    /**
     * 是否开启池化功能的标志。
     */
    private Boolean poolSwitch;

    /**
     * 用于存储各个国家的池化开关状态。
     */
    private Map<String,Boolean> countrySwitch = new HashMap<>();

    /**
     * 用于存储需要添加到池化中的国家的开关状态。
     */
    private Map<String,Boolean> addPoolSwitch = new HashMap<>();

    /**
     * 存储客户代码列表的成员变量。
     */
    private List<String> subtitleClientCodeList;

    /**
     * 存储合同代码与其对应的映射关系。
     */
    private Map<String,String> contractCode = new HashMap<>();


    /**
     * 存储国家与ERP系统对应关系的映射。
     */
    private Map<String,String> countryErpMap = new HashMap<>();


    /**
     * 未入国内工鼎池失败信息。
     */
    private List<String> joinPoolFailInfo;

    /**
     * 存储客户名称的成员变量。
     */
    private String customerName;
}
