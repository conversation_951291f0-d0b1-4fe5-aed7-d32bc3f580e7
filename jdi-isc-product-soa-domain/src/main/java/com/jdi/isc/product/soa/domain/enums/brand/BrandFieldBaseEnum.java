package com.jdi.isc.product.soa.domain.enums.brand;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 品牌字段枚举
 * <AUTHOR>
 * @date 2024/7/30
 **/
@Getter
@AllArgsConstructor
public enum BrandFieldBaseEnum {
    // 品牌注册国
    REGISTER_COUNTRY("countryCode", "品牌注册国"),
    // 国内品牌ID
    JD_BRAND_ID("jdBrandId", "国内品牌ID"),
    // 品牌名
    JD_BRAND_NAME("jdBrandName", "品牌名"),
    // 多语言品牌名
    MULTI_BRAND_NAME("multiBrandName", "多语言品牌名"),
    // 跨境品牌出口授权截图
    CROSS_BORDER_BRAND_EXPORT_AUTHORIZATION_SNAPSHOT("authCert", "跨境品牌出口授权截图"),
    // 跨境品牌出口授权方式
    CROSS_BORDER_BRAND_EXPORT_AUTHORIZATION_METHOD("authType", "跨境品牌出口授权方式"),
    // 品牌授权出口国家及地区
    BRAND_AUTHORIZATION_EXPORT_COUNTRIES_AND_REGIONS("authCountryCode", "品牌授权出口国家及地区"),
    // 品牌授权有效期
    BRAND_AUTHORIZATION_VALIDITY("authDate", "品牌授权有效期"),
    // 品牌可以进入的类目
    BRAND_ACCESSIBLE_CATEGORIES("categoryTreeList", "品牌可以进入的类目"),
    // 品牌分层类型
    BRAND_TIER_TYPE("levelType", "品牌分层类型"),
    // 品牌注册证
    BRAND_REGISTRATION_CERTIFICATE("registerCert", "品牌注册证"),
    // 品牌主页或品牌描述
    BRAND_HOMEPAGE_OR_DESCRIPTION("description", "品牌主页或品牌描述"),
    // 品牌获取的资质证书
    BRAND_QUALIFICATION_CERTIFICATE("qualificationCert", "品牌获取的资质证书");


    private String code;
    private String desc;
}
