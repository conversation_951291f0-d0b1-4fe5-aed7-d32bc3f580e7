package com.jdi.isc.product.soa.domain.taxRate.biz;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 跨境进口税率结果实体
 * <AUTHOR>
 * @description：CrossBorderImportTaxResVO
 * @Date 2025-06-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrossBorderImportTaxResVO {
    /**国际skuId**/
    private Long skuId;
    /**京东skuId**/
    private Long jdSkuId;
    /**海关编码**/
    private String hsCode;
}
