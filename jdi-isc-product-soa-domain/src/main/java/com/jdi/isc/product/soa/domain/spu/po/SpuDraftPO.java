package com.jdi.isc.product.soa.domain.spu.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * spu草稿表
 *
 * <AUTHOR>
 * @TableName jdi_isc_spu_draft_sharding
 */
@TableName(value = "jdi_isc_spu_draft_sharding")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SpuDraftPO extends BasePO {

    private String extProperty;   // TODO 2025del
    /**
     * 新扩展属性字段，代替extAttribute，json格式存储
     */
    private String groupExtAttribute;

    /**
     * SPUID
     */
    private Long spuId;

    /**
     * 商品信息json串
     */
    private String spuJsonInfo;

    /**
     * pc端商详
     */
    private String pcDescription;

    /**
     * 移动端商详
     */
    private String appDescription;

    /**
     * spu跨境属性
     */
    private String spuInterProperty;

    /**
     * spu跨境资质
     */
    private String spuCertificate;

    /**
     * 供应商简码
     */
    private String vendorCode;
    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 税务审核状态
     */
    private Integer taxAuditStatus;
    /**
     * 货源国、国家站
     */
    private String sourceCountryCode;
    /**
     * 审核等级
     */
    private Integer level;
    /**
     * 末级类目ID
     */
    private Long catId;

    /**
     * 京东类目id
     */
    private Long jdCatId;
}