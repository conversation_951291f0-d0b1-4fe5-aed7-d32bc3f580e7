package com.jdi.isc.product.soa.domain.customerSku.biz;

import com.jdi.isc.product.soa.domain.common.biz.BasicVO;
import com.jdi.isc.product.soa.domain.enums.PriceTypeEnum;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceResVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRateVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 客户MKU关系实体
 * <AUTHOR>
 * @date 20231128
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CustomerSkuPriceVO extends BasicVO {

    /** skuId*/
    private Long skuId;

    /** 客户编码*/
    private String clientCode;

    /** 客户名称*/
    private String clientName;

    /** 供应商简码*/
    private String vendorCode;

    /** 币种*/
    private String currency;

    /** 客制采购价*/
    private BigDecimal customerPurchasePrice;

    /** 客制销售价*/
    private BigDecimal customerSalePrice;

    /** 商品名称*/
    private String skuName;

    /** 交货方式  */
    private String customerTradeType;

    /** 价格类型*/
    private PriceTypeEnum priceType;

    /**
     * 未税价
     */
    private BigDecimal salePrice;


    /**
     * 含税价
     */
    private BigDecimal includeTaxPrice;
    /**
     * 毛利率
     */
    private BigDecimal grossRate;

    /**
     * 审批人
     */
    private String auditor;

    private Long beginTime;

    private Long endTime;

    /** 0失效1有效2过期3未生效 */
    private Integer enableStatus;

    /**
     * 税率信息列表
     */
    private List<TaxRateVO> taxRateList;

    /**
     * 国家池入池状态，1-已入池，3-未入池
     *
     * {@link CountryMkuPoolStatusEnum}
     */
    private Integer countryMkuPoolStatus;

    /**
     * 是否入客户池 1-绑定，0-未绑定
     *
     * {@link CustomerMkuBindEnum}
     */
    private String customerMkuPoolStatus;

    /**
     * 不可售阈值
     */
    private BigDecimal unsellableThreshold;

    /**
     * 不可售阈值更新时间
     */
    private Long unsellableThresholdTime;

    /**
     * 可售状态
     */
    private Integer availableSaleStatus;

    /**
     * 价格可售状态更新时间
     */
    private Long availableSaleStatusTime;

    public CustomerSkuPriceVO(SkuPriceResVO input){
        this.skuId = input.getSkuId();
        this.currency = input.getCurrency();
        this.customerSalePrice = input.getSalePrice();
        this.customerPurchasePrice = input.getPurchasePrice();
        this.customerTradeType = input.getCustomerTradeType();
    }

    public CustomerSkuPriceVO(Long skuId ,String clientCode){
        this.skuId = skuId;
        this.clientCode = clientCode;
    }

}
