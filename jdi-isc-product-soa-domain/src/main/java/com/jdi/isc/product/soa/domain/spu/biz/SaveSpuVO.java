package com.jdi.isc.product.soa.domain.spu.biz;

import com.jdi.isc.product.soa.domain.common.biz.ClientBaseReqVO;
import com.jdi.isc.product.soa.domain.enums.spu.SpuWriteCountryTypeEnums;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.validation.ValidateSpuGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SaveSpuVO extends ClientBaseReqVO {
    /**
     * spu对象
     */
    @Valid
    @NotNull(message = "商品基本信息不能为空")
    private SpuVO spuVO;

    /**
     * 添加的扩展属性列表，包含属性组信息（与extendPropertyList的区别是，extendPropertyList是全部属性，storeExtendPropertyList是建立商品时添加的属性）
     * 列入类目下全部sku属性有10个，但是建立商品只添加了5个，那么extendPropertyList有10个，storeExtendPropertyList有5个
     * store用于展示，那么extendPropertyList有10个用于编辑
     */
    //@NotNull(message = "商品扩展属性不能为空", groups = {ValidateSpuGroup.createProduct.class, ValidateSpuGroup.updateProduct.class})
    private List<PropertyVO> storeExtendPropertyList;

    /**
     * spu资质列表
     */
    private List<SpuCertificateVO> spuCertificateVOList;
    /**
     * sku列表
     */
    @Valid
    @NotEmpty(message = "sku列表不能为空", groups = {ValidateSpuGroup.createProduct.class, ValidateSpuGroup.updateProduct.class})
    private List<SkuVO> skuVOList;
    /**
     * PC商品详描 key->语言 value->详描信息
     */
    //@NotNull(message = "商品详情不能为空", groups = {ValidateSpuGroup.createProduct.class, ValidateSpuGroup.updateProduct.class})
    private Map<String, String> pcDescriptionMap;
    /**
     * APP商品详描 key->语言 value->详描信息
     */
    private Map<String, String> appDescriptionMap;

    /**
     * 国际扩展属性信息，key->属性ID value->属性值列表
     */
    private List<GroupPropertyVO> spuInterPropertyList;

    /**
     * 审核状态标识，true表示不参与审核，false表示参与审核。
     */
    private boolean notAudit;

    /**
     * 组织ID，用于标识商品所属的组织。
     */
    private String orgId;

    /**
     * 标签关键字ID，用于标识商品的特定属性或分类。
     */
    private Long tagKeyId;
    /**
     * true新发品 false：更新商品
     */
    private boolean createFlag = false;
    /**
     *  1：审核操作 0:修改等操作
     */
    private Integer approveFlag = 0;

    /**
     * 判断是否是跨境品
     */
    public boolean isCrossBorder() {
        if (this.getSpuVO() != null && this.getSpuVO().getSourceCountryCode() != null) {
            String sourceCountryCode = this.getSpuVO().getSourceCountryCode().toUpperCase();
            // 都转化为大写比较
            return SpuWriteCountryTypeEnums.CROSSBORDER.getCountry().equals(sourceCountryCode);
        } else {
            return false;
        }
    }
}
