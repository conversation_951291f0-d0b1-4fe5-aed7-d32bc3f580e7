package com.jdi.isc.product.soa.domain.customerSku.biz;


import com.jdi.isc.product.soa.domain.common.biz.BasePageVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;


/**
 * 客户MKU关系实体
 * <AUTHOR>
 * @date 2024-10-21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CustomerSkuPriceDraftReqVO extends BasePageVO {

    /** skuId*/
    private Long skuId;

    /** 供应商简码*/
    private String vendorCode;

    /** 客户名称*/
    private String clientName;

    /** 客户编码*/
    private String clientCode;

    /**更新人*/
    private String updater;

    /**
     * 来源国家码值
     */
    private String sourceCountryCode;

    /**
     * 目标国家码值集合
     */
    private Set<String> targetCountryCodes;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 审批层级
     */
    private Integer level;

    /**
     * tab标签key
     */
    private String typeKey;

    /**
     * 有效状态
     */
    private Integer enableStatus;

    /**
     * UTC+0时间戳
     */
    private Long nowTime;

    /**
     * skuId、spuId、jdSkuId、mkuId
     */
    private List<Long> productIds;

    /**
     * SKU ID列表，用于标识关联的SKU信息。
     */
    private List<Long> skuIds;

    /**
     * 国家池入池状态，1-已入池，3-未入池
     *
     * {@link CountryMkuPoolStatusEnum}
     */
    private Integer countryMkuPoolStatus;

    /**
     * 是否入客户池 1-绑定，0-未绑定
     *
     * {@link CustomerMkuBindEnum}
     */
    private String customerMkuPoolStatus;

    /**
     * 不可售阈值
     */
    private BigDecimal unsellableThreshold;

    /**
     * 不可售阈值更新时间
     */
    private Long unsellableThresholdTime;

    /**
     * 可售状态
     */
    private Integer availableSaleStatus;

    /**
     * 价格可售状态更新时间
     */
    private Long availableSaleStatusTime;

}
