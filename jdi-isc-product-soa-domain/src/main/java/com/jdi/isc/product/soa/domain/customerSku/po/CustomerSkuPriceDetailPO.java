package com.jdi.isc.product.soa.domain.customerSku.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import com.jdi.isc.product.soa.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description: sku客制化价格草稿明细表 实体类
 * @Author: zhaokun51
 * @Date: 2025/02/27 14:53
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("jdi_isc_customer_sku_price_detail_sharding")
public class CustomerSkuPriceDetailPO extends BasicPO {

    /**
     * 关联客制化价格表id
     */
    private Long bizId;

    /**
     * 草稿明细来源id
     * */
    private Long sourceId;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 客户简码
     */
    private String clientCode;

    /**
     * 对客交货模式 EXW,DDP
     */
    private String customerTradeType;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 客制销售价
     */
    private BigDecimal customerSalePrice;

    /**
     * 开始时间
     */
    private Long beginTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 是否启用
     */
    private Integer enableStatus;

    /**
     * 有效期结束当天的00:00:00时间
     */
    private Long endDay;

    /**
     * 生效状态:0当前记录还未生效;1当前记录生效中
     */
    private Integer effectiveStatus;

    /**
     * 不可售阈值
     */
    private BigDecimal unsellableThreshold;

    /**
     * 不可售阈值更新时间
     */
    private Long unsellableThresholdTime;

    /**
     * 可售状态
     */
    private Integer availableSaleStatus;

    /**
     * 价格可售状态更新时间
     */
    private Long availableSaleStatusTime;

    /**
     * 国家池入池状态，1-已入池，3-未入池
     *
     * {@link CountryMkuPoolStatusEnum}
     */
    private Integer countryMkuPoolStatus;

    /**
     * 是否入客户池 1-绑定，0-未绑定
     *
     * {@link CustomerMkuBindEnum}
     */
    private String customerMkuPoolStatus;
}
