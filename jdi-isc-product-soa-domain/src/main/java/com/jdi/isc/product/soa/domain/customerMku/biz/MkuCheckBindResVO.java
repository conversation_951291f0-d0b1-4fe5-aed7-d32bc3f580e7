package com.jdi.isc.product.soa.domain.customerMku.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class MkuCheckBindResVO {
    /**
     * 成功绑定MKU的消息映射表，键为MKU ID，值为对应的成功消息。
     */
    private Map<Long,String> successMkuMsgMap;

    /**
     * 未成功绑定MKU的消息映射表，键为MKU ID，值为对应的失败消息。
     */
    private Map<Long,String> failMkuMsgMap;
}
