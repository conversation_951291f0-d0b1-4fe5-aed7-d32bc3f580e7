package com.jdi.isc.product.soa.domain.gms;

import lombok.Getter;
import lombok.ToString;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 中台商品字段枚举
 * <AUTHOR>
 * @date 20230803
**/
@Getter
@ToString
public enum JdProductFieldsEnum {

    /**
     * 请使用品牌ID调用品牌接口获取实时中文名称
     */
    CN_BRAND(1, "cn_brand", "中文品牌名称", "brandName", "setBrandName"),
    VENDER_NAME(2, "vender_name", "商家名称", "venderName", "setVenderName"),

    SHOP_ID(3, "shop_id", "店铺编号", "shopId", "setShopId"),
    IMG_DFS_URL(4, "img_dfs_url", "商品主图", "imgUrl", "setImgUrl"),
    CATEGORY_ID1(5, "category_id1", "一级分类", "firstCategoryId", "setFirstCategoryId"),
    CATEGORY_ID2(6, "category_id2", "二级分类", "secondCategoryId", "setSecondCategoryId"),
    CATEGORY_ID(7, "category_id", "三级分类", "thirdCategoryId", "setThirdCategoryId"),
    SHOP_NAME(8, "shop_name", "店铺名", "shopName", "setShopName"),
    BRAND_ID(9, "brand_id", "品牌ID", "brandId", "setBrandId"),
    IS_SHADOW_SKU(10, "IsShadowSku", "是否影分身商品", "isShadowSku", "setIsShadowSku"),
    PRODUCT_NAME(11, "product_name", "主商品名称", "productName", "setProductName"),
    PG(12, "pg", "是否拼购商品", "pg", "setPg"),
    MODEL(13, "model", "型号", "model", "setModel"),
    PRICE_LIMIT(14, "priceLimit", "是否限购", "priceLimit", "setPriceLimit"),
    VENDER_ID(15, "vender_id", "商家id", "venderId", "setVenderId"),
    MN(16, "MN", "购物车最大购买数量", "carMaxLimit", "setCarMaxLimit"),
    PRODUCT_ID(17, "product_id", "商品编号", "productId", "setProductId"),
    /**
     * 上下架状态
     * 1：上架(可搜索，可购买)，
     * 0：下架(可通过skuid搜索，不可购买)，
     * 2：可上架（可通过skuid搜索，不可购买），
     * 10：pop 删除（不可搜索，不可购买））
     */
    SKU_STATUS(19, "sku_status", "上下架状态", "skuStatus", "setSkuStatus"),
    SKU_NAME(20, "sku_name", "sku名称", "skuName", "setSkuName"),
    COLOR(21, "color", "颜色", "color", "setColor"),
    DAY_LIMITED_SALES(22, "day_limited_sales", "24小时限购数量", "dayLimitedSales", "setDayLimitedSales"),
    BUYER_POST(23, "buyer_post", "采购岗", "buyerPost", "setBuyerPost"),
    UPC_CODE(24, "upc_code", "upc码", "upcCode", "setUpcCode"),
    XSGG(25, "xsgg", "销售规格", "xsgg", "setXsgg"),
    PACK_SPECIFICATION(26, "package_type", "包装规格", "packageType", "setPackageType"),
    UNIT(27, "unit", "销售单位", "unit", "setUnit"),
    SIZE(28, "size", "尺码", "size", "setSize"),
    COLOR_SEQUENCE(29, "color_sequence", "颜色顺序", "colorSequence", "setColorSequence"),
    SIZE_SEQUENCE(30, "size_sequence", "尺码顺序", "sizeSequence", "setSizeSequence"),
    IS_7_TO_RETURN(31, "is7ToReturn", "是否支持7天无理由退货", "is7ToReturn", "setIs7ToReturn"),
    IS_SN(32, "is_sn", "是否序列化（ 0 或null： 否，1 ：是 ）", "issn", "setIssn"),
    IS_OVERSEA_PURCHASE(33, "isOverseaPurchase", "海外直采(全球购)", "isOverseaPurchase", "setIsOverseaPurchase"),
    IS_CAN_USE_DQ(34, "isCanUseDQ", "是否可以使用东券", "isCanUseDQ", "setIsCanUseDQ"),
    IS_CAN_USE_JQ(35, "isCanUseJQ", "是否可以使用京券", "isCanUseJQ", "setIsCanUseJQ"),
    YN(36, "yn", "是否有效", "yn", "setYn"),
    NO_SHOW(37, "NoShow", "上柜但前台不显示", "noShow", "setNoShow"),
    NO_SINGLE_BUY(38, "NOSingleBuy", "不能单独购买", "noSingleBuy", "setNoSingleBuy"),
    GIFTS_GOODS(39, "GiftsGoods", "是否赠品", "giftsGoods", "setGiftsGoods"),
    PLACE_OF_PRODUCTION(40, "place_of_production", "产地", "placeOfProduction", "setPlaceOfProduction"),
    SPEC(41, "spec", "自定义属性", "spec", "setSpec"),
    PRODUCT_CODE(42, "product_code", "产品编码", "productCode", "setProductCode"),
    DELIVERY(43, "delivery", "发货地址", "delivery", "setDelivery"),

    ITEM_NUM(44, "item_num", "货号", "itemNum", "setItemNum"),
    BUYER(45, "buyer", "采购员", "buyer", "setBuyer"),
    SALER(46, "saler", "销售员", "saler", "setSaler"),
    WARRANTY(47, "warranty", "质保", "warranty", "setWarranty"),
    SHELF_LIFE(48, "shelf_life", "质保期", "shelflife", "setShelflife"),
    SERVICE_PHONE(49, "service_tel", "服务电话", "servicePhone", "setServicePhone"),
    OFFICIAL_WEBSITE(50, "official_website", "官网地址", "officialWebsite", "setOfficialWebsite"),
    WEIGHT(51, "weight", "重量", "weight", "setWeight"),
    LENGTH(52, "length", "长度", "length", "setLength"),
    WIDTH(53, "width", "宽度", "width", "setWidth"),
    HEIGHT(54, "height", "高度", "height", "setHeight"),
    IS_DANGERGOODS(55, "isdangergoods", "是否危险品", "dangerous", "setDangerous"),
    SYSP(56, "sysp", "试用商品", "trial", "setTrial"),
    CUSTOMIZED(57, "Customize", "定制", "customized", "setCustomized"),
    NEW_APPLY(58, "IsNewGoods", "新品", "newApply", "setNewApply"),
    VENDER_TYPE(59, "vender_type", "商家合作方式", "venderType", "setVenderType"),
    VENDER_COL_TYPE(60, "vender_col_type", "商家类型", "venderColType", "setVenderColType"),
    SUPPLY_UNIT(61, "supply_unit", "供应商简码", "supplyUnit", "setSupplyUnit"),
    SPU_ID(62, "spu_id", "spuId", "spuId", "setSpuId"),
    COL_TYPE(63, "col_type", "商家合作类型", "colType", "setColType"),
    /**
     * 商品税率，其中商品的税率包含：进项税（inputVAT）、销项税（outputVAT）、
     *
     * 消费税（consumptionVAT）、全球购消费税(overseaTAX)、
     *
     * 全球购增值税（overseaVAT）等五项，以及发票大类(invoiceTypeId)，
     *
     * 税控编码（税收分类编码 taxCode）， 成品油容量（单位：T吨/L升 capacity)等。
     *
     * 例如：consumptionVAT:0,outputVAT:13M,inputVAT:13M,capacity:4.0L
     * 表示 消费税0 ，销项税13%免税，进项税13%免税,成品油容量4.0升
     */
    TAX(64, "tax", "税率", "tax", "setTax"),

    OFF_SHELVES_TIME(65, "off_shelves_time", "下架时间", "offShelvesTime", "setOffShelvesTime"),
    ON_SHELVES_TIME(66, "on_shelves_time", "上架时间", "onShelvesTime", "setOnShelvesTime"),
    UN_LIMIT_CID(67, "unLimit_cid", "末级类目id", "unLimitCid", "setUnLimitCid"),

    PACK_TYPE(68, "packType", "商品包装属性", "packType", "setPackType"),
    STORE_PROPERTY(69, "store_property", "冷链标记", "storeProperty", "setStoreProperty"),
    GYPZZCXQSP(70,"gypzzcxqsp","工业品自主采销权标记","gypzzcxqsp","setGypzzcxqsp"),
    GYBZSP(71,"gybzsp","工业标准商品","gybzsp","setGybzsp"),
    PAY_FIRST(72,"pay_first","是否先付款","payFirst","setPayFirst"),
    LOWEST_BUY(73,"LowestBuy","最小起订量","lowestBuy","setLowestBuy"),
    FACTORY_SHIP(74,"factoryShip","厂直标","factoryShip","setFactoryShip"),
    PACKAGE_TYPE_UNIT(75,"packageTypeUnit","包装规格单位","packageTypeUnit","setPackageTypeUnit"),
    JD_MAIN_SKU_ID(76,"main_sku_id","京东主站主skuId","jdMainSkuId","setJdMainSkuId"),
    JD_SPU_ID(77,"spu_id","京东主站spuId","jdSpuId","setJdSpuId"),
    SALE_ATTRIBUTES(78,"sale_attributes","四维以内销售属性","saleAttributes","setSaleAttributes"),
    SALE_ATTS(79,"sale_atts","四维以外销售属性","saleAtts","setSaleAtts")
    ;


    private Integer index;
    private String field;
    private String desc;
    private String resultField;
    private String capField;


    private static final Map<String, JdProductFieldsEnum> fieldMap = new HashMap<String,JdProductFieldsEnum>();

    static {
        for(JdProductFieldsEnum JdProductFieldsEnum : EnumSet.allOf(JdProductFieldsEnum.class)) {
            fieldMap.put(JdProductFieldsEnum.getResultField(), JdProductFieldsEnum);
        }
    }


    JdProductFieldsEnum(Integer index, String field, String desc, String resultField, String capField) {
        this.index = index;
        this.field = field;
        this.desc = desc;
        this.resultField = resultField;
        this.capField = capField;

    }


    public static JdProductFieldsEnum getEnumByResultField(String resultField) {
        return fieldMap.get(resultField);
    }

}
