package com.jdi.isc.product.soa.domain.sku.biz;


import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description：QuerySkuAvailableSaleReqVO
 * @Date 2025-02-20
 */
@Data
public class QuerySkuAvailableSaleReqVO {
    /**
     * 商品ID集合
     */
    private Set<Long> skuIds;
    /**
     * 国家编码
     */
    private String countryCode;
    /**
     * 客户编码
     */
    private String clientCode;

    /**
     * 包含每个 SKU 的销售请求信息的列表。
     */
    private List<SkuSaleReqVO> skuSaleReqList;
    /**
     * 是否跨SKU 0:否 1:是 null:不限制
     */
    private Integer crossSku;

    public QuerySkuAvailableSaleReqVO() {
    }

    public QuerySkuAvailableSaleReqVO(Set<Long> skuIds, String countryCode) {
        this.skuIds = skuIds;
         this.countryCode = countryCode;
    }
}
