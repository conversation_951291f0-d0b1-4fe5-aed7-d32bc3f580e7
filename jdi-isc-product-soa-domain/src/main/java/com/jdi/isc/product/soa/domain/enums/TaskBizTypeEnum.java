package com.jdi.isc.product.soa.domain.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 任务类型枚举
 */
public enum TaskBizTypeEnum {

    CATE_BATCH_PUBLISH(0, 5000,"类目批量新增"),
    EXT_ATTRIBUTE_BATCH_PUBLISH(1, 5000,"扩展属性批量新增"),
    SALE_ATTRIBUTE_BATCH_PUBLISH(2,5000, "销售属性批量新增"),
    CATE_EXT_ATTRIBUTE_BATCH_PUBLISH(3,5000, "类目扩展属性批量新增"),
    CATE_SALE_ATTRIBUTE_BATCH_PUBLISH(4, 5000,"类目销售属性批量新增"),
    CH_PRODUCT_BATCH_PUBLISH(5, 1000,"跨境商品批量新增"),
    VN_PRODUCT_BATCH_PUBLISH(6, 1000,"越南商品批量新增"),
    PRODUCT_BATCH_DOWNLOAD(7, -1,"商品批量下载"),
    SKU_CUSTOM_PRICE_BATCH_MODIFY(8, 5000,"SKU客制化价格批量修改"),
    SKU_CUSTOM_PRICE_BATCH_DOWNLOAD(9, -1,"SKU客制化价格批量下载"),
    LOCAL_SKU_STOCK_BATCH_MODIFY(10, 5000,"本土SKU库存批量修改"),
    LOCAL_SKU_STOCK_BATCH_DOWNLOAD(11, -1,"本土SKU库存批量下载"),
    MKU_DETAIL_BATCH_DOWNLOAD(12, -1,"MKU详情批量下载"),
    MKU_CUSTOMER_REF_BATCH_DOWNLOAD(13, -1,"MKU客户关系批量下载"),
    MKU_CUSTOMER_PRICE_BATCH_DOWNLOAD(14, -1,"MKU客制化价格批量下载"),
    MKU_CUSTOMER_SKU_BATCH_BIND(15, 5000,"客户MKU绑定SKU"),
    MKU_CUSTOMER_BATCH_BIND(16, 5000,"客户绑定MKU"),
    MKU_PROMISE_PREDICT_DAY_BATCH_IMPORT(17, 5000,"MKU设置配送时长"),
    SKU_BASE_BATCH_MODIFY(18, 5000,"批量更新SKU基本信息"),
    //https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/pmethctab1/批量新增泰国HSCODE税率模板.xlsx
    TH_HSCODE_TAX_BATCH_ADD(19, 5000,"批量新增泰国HSCODE税率"),
    //https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/pmethctab1/批量新增泰国商品税率模版.xlsx
    TH_SKU_TAX_BATCH_ADD(20, 5000,"批量新增泰国商品税率"),
    //https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/pmethctab1/批量更新泰国商品关税率模版.xlsx
    TH_SKU_TAX_BATCH_MODIFY(21, 5000,"批量更新泰国商品关税率"),
    //https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/pmethctab1/%E6%89%B9%E9%87%8F%E6%96%B0%E5%A2%9E%E6%9B%B4%E6%96%B0%E8%AE%A2%E5%8D%95%E5%90%8C%E6%AD%A5%E8%B4%A2%E5%8A%A1%E5%8A%A0%E4%BB%B7%E8%A1%A8.xlsx
    ORDER_FINANCE_BATCH_PUBLISH(22, 5000,"批量新增更新核算加价表"),
    //https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/pmethctab1/%E6%B3%B0%E5%9B%BD%E5%95%86%E5%93%81%E9%9D%A2%E4%BB%B7%E8%AE%A1%E7%AE%97%E6%A8%A1%E6%9D%BF.xlsx
    SKU_PRICE_TOOL_BATCH(23, 1000,"批量计算SKU公域面价"),
    VN_CATEGORY_RATE_BATCH(24, 1000,"批量新增越南类目税率"),
    SKU_PRICE_CUSTOMER_TOOL_BATCH(25, 1000,"批量计算SKU客户未税销售价"),
    SKU_EXCLUDE_TAX_PRICE_BATCH(26, 1000,"批量设置客户SKU未税价"),
    TH_PRODUCT_BATCH_PUBLISH(27, 1000,"泰国商品批量新增"),
    TASK_EXPORT_PURCHASE_ORDER(28, 1000, "导出采购单信息"),
    TASK_EXPORT_ORDER(29, 1000, "导出订单信息"),
    TASK_EXPORT_CATEGORY(30, 1000, "导出类目信息"),
    EXPORT_ORDER_WAYBILL_PDF(31, 1000, "导出二段运单交接单"),
    EXPORT_FIRST_WAYBILL_PDF(32, 1000, "导出一段运单交接单"),
    EXPORT_MARK_PDF(33, 1000, "导出唛头"),
    ORDER_DELIVERY_IMPORT_BATCH(34, 1000, "批量新增承运人信息"),
    ORDER_DELIVERY_TRACK_IMPORT_BATCH(35, 1000, "批量新增物流轨迹"),
    VN_PRODUCT_ALL_CATEGORY_BATCH_PUBLISH(36, 1000,"越南全类目商品批量新增"),
    TH_PRODUCT_ALL_CATEGORY_BATCH_PUBLISH(37, 1000,"泰国全类目商品批量新增"),

    // 要和taskCenter保持一致
    VIP_PRICE_LINE_AUDIT_IMPORT(148,100000,"VIP价不可售阈值导入"),
    AGREEMENT_PRICE_LINE_AUDIT_IMPORT(149,100000,"协议价不可售阈值导入"),
    ;

    private Integer code;
    /**
     * 导入最大数量,默认1000，导出-1，不设置上限
     */
    private Integer max;
    private String name;

    TaskBizTypeEnum(Integer code,Integer max,String name) {
        this.code = code;
        this.max = max;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public static TaskBizTypeEnum forCode(Integer code) {
        if (code == null) {
            return null;
        }
        TaskBizTypeEnum[] values = values();
        for (TaskBizTypeEnum enumObj : values) {
            if (code.equals(enumObj.getCode())) {
                return enumObj;
            }
        }
        return null;
    }

    public static TaskBizTypeEnum forName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        TaskBizTypeEnum[] values = values();
        for (TaskBizTypeEnum enumObj : values) {
            if (name.equals(enumObj.getName())) {
                return enumObj;
            }
        }
        return null;
    }


    public static Set<TaskBizTypeEnum> bizTypeEnumSet() {
        TaskBizTypeEnum[] values = TaskBizTypeEnum.values();
        return Arrays.stream(values).collect(Collectors.toSet());
    }

}
