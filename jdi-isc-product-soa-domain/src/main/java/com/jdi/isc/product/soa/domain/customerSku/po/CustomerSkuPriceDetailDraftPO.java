package com.jdi.isc.product.soa.domain.customerSku.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description: sku客制化价格草稿明细表 实体类
 * @Author: zhaokun51
 * @Date: 2025/02/27 14:48
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("jdi_isc_customer_sku_price_detail_draft_sharding")
public class CustomerSkuPriceDetailDraftPO extends BasicPO {

    /**
     * 关联草稿表id
     */
    private Long bizId;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 客户简码
     */
    private String clientCode;

    /**
     * 对客交货模式 EXW,DDP
     */
    private String customerTradeType;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 客制销售价
     */
    private BigDecimal customerSalePrice;

    /**
     * 开始时间
     */
    private Long beginTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 是否启用
     */
    private Integer enableStatus;

    /**
     * 审核状态，1:审核通过，2:驳回 3:待审核
     */
    private Integer auditStatus;

    /**
     * 客制销售价
     */
    private String auditor;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 审批等级
     */
    private Integer level;

    /**
     * 利润率
     */
    private BigDecimal profitRate;

    /**
     * 预警情况
     */
    private String warningMsg;

    /**
     * 调价原因
     */
    private String adjustmentPriceReason;

    /**
     * 附件
     */
    private String attachmentUrls;

    /**
     * 审批流表单
     */
    private String processFromData;


}
