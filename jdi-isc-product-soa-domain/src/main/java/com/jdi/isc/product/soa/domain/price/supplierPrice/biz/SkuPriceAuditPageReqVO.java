package com.jdi.isc.product.soa.domain.price.supplierPrice.biz;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class SkuPriceAuditPageReqVO extends SkuPricePageReqVO implements Serializable {

    private String applyCode;

    /** skuIds */
    private List<Long> skuIds;

    private List<String> ids;

    private String currentAuditor;
}
