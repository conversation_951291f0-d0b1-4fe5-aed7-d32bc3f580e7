package com.jdi.isc.product.soa.domain.price.agreementPrice.po;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description: 国家协议价 实体类
 * @Author: wangpeng965
 * @Date: 2025/03/03 16:45
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("jdi_isc_country_agreement_price_sharding")
public class CountryAgreementPricePO extends CountryAgreementPriceBasePO {

    /**
     * 协议价
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal agreementPrice;

    /**
     * 国家成本价 标记字段必须强制更新（即使允许为 null）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal countryCostPrice;

    /**
     * 建议国家协议价
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal suggestAgreementPrice;
    /**
     * 建议国家协议价计算流程。
     */
    private String suggestAgreementMark;

    /**
     * 协议价更新时间。
     */
    private Long agreementUpdateTime;

    /**
     * 京东类目id
     */
    private Long jdCatId;
    /**
     * 不可售阈值
     */
    private BigDecimal unsellableThreshold;

    /**
     * 不可售阈值更新时间
     */
    private Long unsellableThresholdTime;

    /**
     * 可售状态
     */
    private Integer availableSaleStatus;

    /**
     * 价格可售状态更新时间
     */
    private Long availableSaleStatusTime;

    /**
     * 成本价变更来源
     */
    private Integer costDataStatusSource;

    /**
     * 国家池入池状态，1-已入池，3-未入池
     *
     * {@link CountryMkuPoolStatusEnum}
     */
    private Integer countryMkuPoolStatus;

    /**
     * 是否入客户池 1-绑定，0-未绑定
     *
     * {@link CustomerMkuBindEnum}
     */
    private String customerMkuPoolStatus;

}
