package com.jdi.isc.product.soa.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * The enum Audit form enum.
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum AuditFormEnum {
    /**
     * 审批单据类型
     */
    PURCHASE_PRICE_AUDIT("PURCHASE_PRICE_AUDIT"),
    /**
     * 协议价格审批类型的枚举常量
     */
    AGREEMENT_PRICE_AUDIT("AGREEMENT_PRICE_AUDIT"),
    /**
     * VIP价格审批类型的枚举常量
     */
    VIP_PRICE_AUDIT("VIP_PRICE_AUDIT"),

    /**
     * vip价不可售阈值审批类型的枚举常量
     */
    VIP_PRICE_LINE_AUDIT("VIP_PRICE_LINE_AUDIT"),

    /**
     * 协议价不可售阈值审批类型的枚举常量
     */
    AGREEMENT_PRICE_LINE_AUDIT("AGREEMENT_PRICE_LINE_AUDIT"),
    ;

    private final String code;
}