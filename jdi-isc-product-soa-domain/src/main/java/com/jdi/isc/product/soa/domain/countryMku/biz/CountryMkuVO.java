package com.jdi.isc.product.soa.domain.countryMku.biz;

import com.jdi.isc.product.soa.domain.common.biz.BasicVO;
import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.domain.enums.countryMku.CountryMkuWarnReasonEnum;
import com.jdi.isc.product.soa.domain.enums.countryMku.CountryMkuWarnStatusEnum;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Description: 商品国家池表 VO实体类
 * @Author: wangpeng965
 * @Date: 2024/12/02 21:49
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class CountryMkuVO extends BasicVO {

    /**
     * MKUID
     */
    @NotNull(message = "MKUID不能为空")
    private Long mkuId;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    @NotNull(message = "货源国、国家站、国家码，ISO 3166-1 两字母代码不能为空")
    private String sourceCountryCode;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    @NotNull(message = "货源国、国家站、国家码，ISO 3166-1 两字母代码不能为空")
    private String targetCountryCode;

    /**
     * 末级类目id
     */
    @NotNull(message = "末级类目id不能为空")
    private Long lastCatId;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 采购员
     */
    private String buyer;

    /**
     * 发货时效
     */
    private String productionCycle;

    /**
     * 审核状态，0:无申请，1:待入池，2:待拉黑
     */
    @NotNull(message = "审核状态，0:无申请，1:待入池，2:待拉黑不能为空")
    private Integer applyStatus;

    /**
     * 入池状态，0：待入池，1:已入池，2:已拉黑
     */
    @NotNull(message = "入池状态，0：待入池，1:已入池，2:已拉黑不能为空")
    private Integer poolStatus;

    /**
     * 预警状态，0：预警，1:正常
     */
    @NotNull(message = "预警状态，0：预警，1:正常不能为空")
    private Integer warnStatus;

    /**
     * 预警原因，0:- ,1：进口许可不符合，2：出口许可不符合，3:强制商品认证不符合，4：跨境运输不符合，5:品牌授权不符合，6:跨境商品未入国内商品池
     */
    @NotNull(message = "预警原因，0:- ,1：进口许可不符合，2：出口许可不符合，3:强制商品认证不符合，4：跨境运输不符合，5:品牌授权不符合，6:跨境商品未入国内商品池不能为空")
    private String warnReason;

    /**
     * 拉黑原因，0:固定规则不满足，1:业务规则不满足，2:手动拉黑(0，1，2逗号分隔)
     */
    private String blackReason;
    /**
     * 待确认原因，0:商品类目禁售，1:跨境运费占比超过阈值(0，1)
     */
    private String undeterminedReason;

    /**
     * 标签键ID
     */
    private String tagKeyId;

    /**
     * 标签值ID
     */
    private String tagValueId;


    /**
     * 是否有效的标志位
     */
    private Boolean valid = true;

    /**
     * 入池失败原因
     */
    private String poolFailReason;

    /**
     * 不可售原因
     */
    private String unSaleReason;


    public CountryMkuVO(MkuPO mkuPO){
        this.mkuId = mkuPO.getMkuId();
        this.sourceCountryCode = mkuPO.getSourceCountryCode();
        this.targetCountryCode = mkuPO.getSourceCountryCode();
        //this.lastCatId = mkuPO.getCatId();
        this.lastCatId = mkuPO.getJdCatId();
        this.brandId = mkuPO.getBrandId();
        this.poolStatus = CountryMkuPoolStatusEnum.POOL.getCode();
        this.warnStatus = CountryMkuWarnStatusEnum.NORMAL.getCode();
        this.warnReason = CountryMkuWarnReasonEnum.DRAFT.getCode().toString();
        this.createTime = new Date().getTime();
        this.updateTime = new Date().getTime();
        this.updater = "System";
        this.creator = "System";
        this.buyer = mkuPO.getBuyer();
    }
}
