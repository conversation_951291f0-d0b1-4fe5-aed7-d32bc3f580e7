package com.jdi.isc.product.soa.domain.gms.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 中台商品实体
 * <AUTHOR>
 * @date 20230803
**/
@Data
public class JdProductDTO implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 4987477176029216676L;

    /**
     * 商品编码
     */
    private Long skuId;
    /**
     * 上下架状态
     * 1：上架(可搜索，可购买)，
     *
     * 0：下架(可通过skuid搜索，不可购买)，
     *
     * 2：可上架（可通过skuid搜索，不可购买），
     *
     * 10：pop 删除（不可搜索，不可购买））
     */
    private String skuStatus;
    /**
     * 标准商品名称
     */
    private String skuName;
    /**
     * 一级分类id
     */
    private Integer firstCategoryId;
    /**
     * 二级分类id
     */
    private Integer secondCategoryId;
    /**
     * 三级分类id
     */
    private Integer thirdCategoryId;
    /**
     * 品牌id
     */
    private Integer brandId;
    /**
     * 产地
     */
    private String placeOfProduction;
    /**
     * 订货号
     */
    private String itemNum;
    /**
     * 型号
     */
    private String model;
    /**
     * 销售单位
     */
    private String unit;
    /**
     * 采购员
     */
    private String buyer;
    /**
     * 销售员
     */
    private String saler;
    /**
     * 质保
     */
    private String warranty;
    /**
     * 质保期
     */
    private Integer shelfLife;
    /**
     * 服务电话
     */
    private String servicePhone;
    /**
     * 官网地址
     */
    private String officialWebsite;
    /**
     * 条形码
     */
    private String upcCode;
    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 长度
     */
    private BigDecimal length;
    /**
     * 宽度
     */
    private BigDecimal width;
    /**
     * 高度
     */
    private BigDecimal height;
    /**
     * 包装规格
     */
    private Integer packageType;
    /**
     * 包装清单 待定
     */
    private String packageList;
    /**
     * 危险商品  1:是 0:否
     */
    private Boolean dangerous;
    /**
     * 试用品
     */
    @JSONField(name = "sysp")
    private Boolean trial;
    /**
     * 定制
     */
    @JSONField(name = "Customize")
    private Boolean customized;
    /**
     * 赠品
     */
    @JSONField(name = "GiftsGoods")
    private Boolean gift;
    /**
     * 新申请
     */
    @JSONField(name = "IsNewGoods")
    private Boolean newApply;
    /**
     * 商家合作方式
     */
    @JSONField(name = "vender_type")
    private String venderType;
    /**
     * 商家ID
     */
    @JSONField(name = "vender_id")
    private String venderId;
    /**
     * 商家类型
     */
    @JSONField(name = "vender_col_type")
    private String venderColType;
    /**
     * 供应商简码
     */
    @JSONField(name = "supply_unit")
    private String supplyUnit;
    /**
     * 是否有效
     */
    private String yn;
    /**
     * spuId
     */
    @JSONField(name = "spu_id")
    private String spuId;
    /**
     * productId
     */
    @JSONField(name = "product_id")
    private String productId;
    /**
     * 采销岗
     */
    private String buyerPost;
    /**
     * 店铺ID
     */
    private String shopId;
    /**
     * 商家合作类型
     */
    private String colType;
    /**
     * 商品税率，其中商品的税率包含：进项税（inputVAT）、销项税（outputVAT）、
     *
     * 消费税（consumptionVAT）、全球购消费税(overseaTAX)、
     *
     * 全球购增值税（overseaVAT）等五项，以及发票大类(invoiceTypeId)，
     *
     * 税控编码（税收分类编码 taxCode）， 成品油容量（单位：T吨/L升 capacity)等。
     *
     * 例如：consumptionVAT:0,outputVAT:13M,inputVAT:13M,capacity:4.0L
     * 表示 消费税0 ，销项税13%免税，进项税13%免税,成品油容量4.0升
     */
    private String tax;

    /**
     * 上架时间
     */
    private Long onShelvesTime;
    /**
     * 下架时间
     */
    private Long offShelvesTime;
    /**
     * 末级类目id
     */
    private Integer unLimitCid;

    //=2易碎品
    private String packType;

    //是否冷藏品
    private String storeProperty;

    // 工业品自主采销标记
    private String gypzzcxqsp;

    // 工业标准商品标记
    private String gybzsp;
    /**
     * 是否先付款
     */
    private Integer payFirst;
    /**
     * 最小起订量-中台可能返回为空
     */
    private String lowestBuy;
    /**
     * 商品发货时效
     */
    private Long sendTemplate;

    /**
     * 厂直标
     */
    private String factoryShip;

    /**
     * 7天无理由退换
     */
    private String is7ToReturn;
    /**
     * 包装规格单位
     */
    private String packageTypeUnit;

    /**
     * 主站商品主skuId
     */
    private Long jdMainSkuId;

    /**
     * 主站商品spuId
     */
    private Long jdSpuId;

    /**
     * 四维以内销售属性, json array
     * "[{\"dim\":1,\"saleName\":\"型号\",\"saleValue\":\"8MM\",\"sequenceNo\":1},{\"dim\":2,\"saleName\":\"规格\",\"saleValue\":\"加长内六角扳手\",\"sequenceNo\":1},{\"dim\":4,\"saleValue\":\"现货\",\"sequenceNo\":1}]"
     */
    private String saleAttributes;

    /**
     * 四维以上的销售属性，json array
     * "[{\"dim\":4,\"saleValue\":\"现货\",\"sequenceNo\":1}]"
     */
    private String saleAtts;

    /**
     * 颜色
     */
    private String color;
}
