package com.jdi.isc.product.soa.domain.customer.biz;

import com.jdi.isc.product.soa.domain.address.IopAddressVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 客户基础实体
 *
 * <AUTHOR>
 * @date 20231109
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerVO {

    /**
     * 客户名称
     */
    private String clientName;

    /**
     * 客户编码
     */
    private String clientCode;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 客户pin
     */
    private String pin;

    /**
     * 客户所属站点 CN、US、VN、TH
     */
    private String country;

    /**
     * 拥有的站点
     */
    private List<Integer> stations;

    /**
     * 售卖币种
     */
    private String saleCurrency;

    /**
     * 企业类型：1-EPE(跨境贸易),2-FDI(本土贸易)
     */
    private String companyType;

    /**
     * 是否配置默认的0.07增值税逻辑
     */
    private Boolean excludeDefaultVatFlag;

    /**
     * wisp页面可见的订单列表状态
     */
    private List<Integer> wispOrderListShowStatus;

    /**
     * 订单配置信息
     */
    private Map<String, String> orderMap;

    /**
     * 开放访问所属地区
     */
    private String openArea;
    /**
     * 客户签约机构编码
     */
    private Integer tradeRoute;
    /**
     * EPT仓编码
     */
    private Long storeId;
    /**
     * 内贸段pin
     */
    private String iopPin;
    /**
     * 售卖ERP
     */
    private String sellerErp;
    /**
     * 账期类型
     */
    private Integer periodType;
    /**
     * 预付款比例，只填整数
     */
    private Integer prepaymentRatio;
    /**
     * 最后付款周期
     */
    private Integer finalPaymentPeriod;

    /**
     * iop地址信息
     */
    private IopAddressVO iopAddressVO;

    /** 客户入池检查税率开关*/
    private Boolean taxCheckFlag;

    /**
     * 客户唛头
     */
    private String shippingMark;


    /** 客户简码*/
    private String briefCode;

    /** 订单配置 */
    private String checkThirdExtInfoKey;

    /**
     * 运单直接妥投
     * true是
     * false否
     * 默认false否
     */
    private Boolean orderWaybillDeliver=false;

    /**
     * iopId的唯一标识符
     */
    private String iopId;

    /**
     * 存储多语言信息的映射表   "zh": "中文","vi": "Tiếng Việt"
     */
    private Map<String,String> language;

    /**
     * 国家类型 1:当地（欧盟国家）2:非当地的欧盟国家 3:非欧盟国家
     */
    private Integer countryType;

    /** 客户是否订阅开发消息*/
    private Boolean openSubscribeFlag;

    /**
     * 获取处理后的语言映射。DUCC中pt-BR “-” 配置错误，数据库中是"_"
     * @return 处理后的语言映射，键已被替换。
     */
    public Map<String,String>  getLanguage () {
        if(MapUtils.isEmpty(language)) {
            return language;
        }
        return language.entrySet().stream()
            .collect(Collectors.toMap(
                entry -> replaceKey(entry.getKey()), // 替换键
                Map.Entry::getValue                 // 保留值
            ));
    }

    private String replaceKey(String key) {
        key  =  key.replace("-","_");
        return key;
    }

    /**
     * 销售异常阈值
     */
    private BigDecimal salePriceLimit;

    /**
     * 京东主体编码
     */
    private String jdMainCode;

    /**
     * 客户主体编码
     */
    private String customerMainCode;
}
