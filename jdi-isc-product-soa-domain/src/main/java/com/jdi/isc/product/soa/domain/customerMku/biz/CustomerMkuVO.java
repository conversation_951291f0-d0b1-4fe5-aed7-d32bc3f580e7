package com.jdi.isc.product.soa.domain.customerMku.biz;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import com.jdi.isc.product.soa.api.specialAttr.res.SpecialAttrTagDTO;
import com.jdi.isc.product.soa.domain.common.biz.BaseVO;
import com.jdi.isc.product.soa.domain.common.po.BasePO;
import com.jdi.isc.product.soa.domain.enums.mku.MkuSpuRelationFlagEnum;
import com.jdi.isc.product.soa.domain.specialAttr.biz.SpecialAttrTagVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 客户MKU关系实体
 * <AUTHOR>
 * @date 20231128
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CustomerMkuVO extends BaseVO {

    /** mkuId*/
    private Long mkuId;

    /** mku名称*/
    private String mkuName;

    /** 客户编码*/
    private String clientCode;

    /** 客户名称*/
    private String clientName;

    /** 绑定状态*/
    private CustomerMkuBindEnum bindStatus;

    /** 末级类目ID*/
    private Long catId;

    /** 国际skuId*/
    private Long skuId;
    /** 零售skuId*/
    private Long jdSkuId;
    /** EPT skuId*/
    private Long eptSkuId;
    /** 深圳工业在池状态*/
    private Boolean szgyInPoolFlag;
    /** 泰国工业在池状态*/
    private Boolean tggyInPoolFlag;
    /** 内贸段上架状态*/
    private String jdSkuSaleStatus;
    /** 内贸段1件库存状态*/
    private String jdSkuStockStatus1;
    /** 内贸段10件库存状态*/
    private String jdSkuStockStatus10;
    /** 内贸段50件库存状态*/
    private String jdSkuStockStatus50;
    /** 零售sku采购价是否维护*/
    private Boolean jdSkuPurchasePriceFlag;
    /** EPT商品上架状态*/
    private String eptSkuSaleStatus;
    /** 申报要素*/
    private String declarationElements;
    /** 商品货源国 */
    private String countryCode;

    /** mku下辖固定sku所属spu*/
    private Long spuId;

    /** 主mku标识 {@link MkuSpuRelationFlagEnum}*/
    private Integer spuFlag;

    /** 绑定状态*/
    private String bindValue;

    /** 客户编码*/
    private String briefCode;

    /**
     * 特殊属性标签列表
     */
    private List<SpecialAttrTagVO> specialAttrTagVOList;

    /** 国际池在池状态*/
    private Boolean countryInPoolFlag;

    /**
     * 客户所在国的国别代码
     */
    private String targetCountryCode;

    /**
     * 国家协议价格
     */
    private BigDecimal countryAgreementPrice;

    /**
     * 国家VIP价格
     */
    private BigDecimal countryVipPrice;

    /** 目标币种 */
    private String targetCurrencyCode;
    /** 未税销售价 */
    private BigDecimal salePrice;
    /** 含税销售价 */
    private BigDecimal taxSalePrice;

    public CustomerMkuVO(Long mkuId){
        this.mkuId = mkuId;
    }
}
