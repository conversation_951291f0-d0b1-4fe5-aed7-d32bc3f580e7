package com.jdi.isc.product.soa.domain.taxRate.po.countryTax;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 巴西进口税率表
 * <AUTHOR>
 * @date 20250311
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("jdi_isc_br_jd_sku_tax_sharding")
public class BrSkuTaxPO extends BasePO {

    /**
     * 海关编码
     */
    private String ncmCode;

    /**
     * SKUID
     */
    private Long jdSkuId;

    /**
     * 进口关税率II
     */
    private BigDecimal importTax;

    /**
     * 工业产品税率IPI
     */
    private BigDecimal industryProductTax;

    /**
     * 社会一体化费率PIS
     */
    private BigDecimal socialIntegrationTax;

    /**
     * CONFINS率
     */
    private BigDecimal confinsTax;

    /**
     * 反倾销税率
     */
    private BigDecimal antiDumpingTax;

    /**
     * ICMS流转税率
     */
    private BigDecimal icmsFlowTax;

    /**
     * 是否进口管制1是0否
     */
    private Integer isImportLimit;


}