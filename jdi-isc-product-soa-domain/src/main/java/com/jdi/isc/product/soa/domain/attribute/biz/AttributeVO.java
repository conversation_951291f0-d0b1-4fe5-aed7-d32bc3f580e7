package com.jdi.isc.product.soa.domain.attribute.biz;

import com.jdi.isc.product.soa.domain.common.biz.BaseVO;
import com.jdi.isc.product.soa.api.common.enums.CategoryAttrInputTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.CategoryAttrTypeEnum;
import com.jdi.isc.product.soa.domain.enums.StatusEnum;
import com.jdi.isc.product.soa.domain.enums.validator.EnumValidation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 类目属性
 * <AUTHOR>
 * @date 20231124
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class AttributeVO extends BaseVO {

    /**
     * Integer shield（隐藏属性 shield字段1为隐藏）
     */
    private Integer shield;
    /**
     * String isQuJianZhi（为 1 表明为区间属性）
     */
    private String isQuJianZhi;

    /** 属性组id */
    private Integer comGroupId;

    /** 语言与属性组名称翻译map */
    private Map<String, String> langComGroupNameMap;

    /** 属性级别  0:product  1:sku  2:product和sku都能用，默认0 */
    private Integer level;

    /** 属性类型 {@link CategoryAttrTypeEnum} */
    @NotNull(message = "属性类型不能为空")
    @EnumValidation(clazz = CategoryAttrTypeEnum.class, message = "属性类型非法")
    private Integer attributeType;

    /** 属性值类型 {@link CategoryAttrInputTypeEnum} */
    @NotNull(message = "属性值类型不能为空")
    @EnumValidation(clazz = CategoryAttrInputTypeEnum.class, message = "属性值类型非法")
    private Integer attributeInputType;

    /** 排序*/
    private Integer sort;

    /** 启用状态 {@link StatusEnum} */
    @NotNull(message = "状态不能为空")
    @EnumValidation(clazz = StatusEnum.class, message = "状态非法")
    private Integer status;

    /** 键值对，扩展字段*/
    @Length(max = 100, message = "超长")
    private String features;

    /** 多语言信息*/
    @Valid
    @NotEmpty(message = "属性名称不能为空")
    private List<AttributeLangVO> langList;

    /** 适用范围*/
    @Valid
    private List<AttributeCountryVO> countryList;

    /** 属性值*/
    @Valid
    @NotEmpty(message = "属性值不能为空")
    private List<AttributeValueVO> attributeValueList;
}
