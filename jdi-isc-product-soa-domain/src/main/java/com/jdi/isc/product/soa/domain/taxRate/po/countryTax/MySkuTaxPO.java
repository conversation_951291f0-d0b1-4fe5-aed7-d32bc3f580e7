package com.jdi.isc.product.soa.domain.taxRate.po.countryTax;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 马来进口税率表
 * <AUTHOR>
 * @date 20250311
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("jdi_isc_my_jd_sku_tax_sharding")
public class MySkuTaxPO extends BasePO {

    /**
     * 海关编码
     */
    private String hsCode;

    /**
     * SKUID
     */
    private Long jdSkuId;

    /**
     * 最惠国关税率
     */
    private BigDecimal mfnTax;

    /**
     * 原产地优惠关税率FormE
     */
    private BigDecimal originMfnTax;

    /**
     * 销售税率SST
     */
    private BigDecimal saleTax;

    /**
     * 反倾销税率
     */
    private BigDecimal antiDumpingTax;

    /**
     * 是否办理产地证1是0否
     */
    private Integer isOriginCertificate;

}