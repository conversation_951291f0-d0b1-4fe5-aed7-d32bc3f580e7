package com.jdi.isc.product.soa.domain.sku.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * sku基础表
 *
 * @TableName jdi_isc_sku_sharding
 */
@TableName(value = "jdi_isc_sku_sharding")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SkuPO {

    /**
     * 新扩展属性字段，json格式存储
     */
    private String groupExtAttribute;

    /**
     * SKUID
     */
    @TableId
    private Long skuId;
    /**
     * SPUID
     */
    private Long spuId;

    /**
     * 工业国内SKUID
     */
    private Long jdSkuId;

    /**
     * 京东主站主skuId
     */
    private Long jdMainSkuId;

    /**
     * 京东主站spuId
     */
    private Long jdSpuId;

    /**
     * EPT SKUID
     */
    private Long eptSkuId;

    /**
     * 供应商简码
     */
    private String vendorCode;

    /**
     * 供应商SKUID
     */
    private Long vendorSkuId;

    /**
     * 国内供应商简码
     */
    private String jdVendorCode;

    /**
     * upc编码
     */
    private String upcCode;

    /**
     * 货源国、国家站
     */
    private String sourceCountryCode;

    /**
     * 末级后台类目ID
     */
    private Long catId;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 毛重(g)
     */
    private BigDecimal weight;

    /**
     * 长(mm)
     */
    private BigDecimal length;

    /**
     * 宽(mm)
     */
    private BigDecimal width;

    /**
     * 高(mm)
     */
    private BigDecimal height;

    /**
     * 销售属性及值 aa:bb,cc:dd
     */
    private String saleAttribute;

    /**
     * sku主图
     */
    private String mainImg;

    /**
     * sku细节图
     */
    private String detailImg;
    /**
     * 带磁 默认0=不带磁 1=带磁
     * @date 2024-1-6
     */
    private Integer magnetic;
    /**
     * 带电 默认0=不带电 1=带电
     * @date 2024-1-6
     */
    private Integer electric;
    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updater;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 最后修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 逻辑删除 0=删除,1有效
     */
    private Integer yn;

    /**
     * 海关编码
     */
    private String hsCode;
    /**
     * 商品状态
     */
    private Integer skuStatus;

    /**
     * 最小下订数量
     */
    private Integer moq;

    /**
     * 含液体
     */
    private Integer liquid;
    /**
     * 含粉尘
     */
    private Integer powder;
    /**
     * 发货时效
     */
    private Integer productionCycle;

    /**
     * 跨国属性id及值id aa:bb,cc:dd#ee,ff
     */
    private String skuInterAttribute;
    /**
     * 重量来源
     */
    private Integer weightSource;
    /**
     * 末级后台京东类目ID
     */
    private Long jdCatId;
    /**
     * 外部关联skuId(与国际sku一对一)
     */
    private String skuThirdRefId;
    /**
     * 下架原因
     */
    private String downReason;
    /**
     * 主站状态联动启用状态  0：未启用 1：启用
     */
    private Integer mainSiteSynSwitch;
}