package com.jdi.isc.product.soa.domain.warehouse.biz;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/7/23
 **/
@Data
public class WarehouseSkuResVO {

    /**
     * 仓库ID
     */
    private Long warehouseId;
    /**
     * SKUID
     */
    private Long skuId;
    /**
     * sku图片
     */
    private String skuImg;
    /**
     * sku名称
     */
    private String skuName;
    /**
     * 国内SKU ID
     */
    private Long jdSkuId;
    /**
     * 可用库存
     */
    private Long availableStock;
    /**
     * 国内SKU 库存
     */
    private String jdStock;

    /**
     * 含税采购价
     */
    private BigDecimal purchaseTaxPrice;

    /**
     * 未税价
     */
    private BigDecimal purchasePrice;

    /**在途可售标识 在途可售状态 0:不支持 1:支持 **/
    private Integer onWaySale;

    /**
     * 备货模式
     * 1-备货，2-寄售
     */
    private Integer purchaseModel;

    /**
     * 供应商简码
     */
    private String vendorCode;

    /**
     * 国内供应商简码
     */
    private String jdVendorCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 货主编码
     */
    private String consignorCode;

    /**
     * 商品货源国
     */
    private String sourceCountryCode;
}
