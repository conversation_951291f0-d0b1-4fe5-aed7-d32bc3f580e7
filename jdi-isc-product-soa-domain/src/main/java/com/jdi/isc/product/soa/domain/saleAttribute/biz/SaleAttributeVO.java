package com.jdi.isc.product.soa.domain.saleAttribute.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 销售属性VO对象
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SaleAttributeVO {

    /** 主键ID */
    private Long id;

    /** 主站类目ID */
    private Long jdCatId;

    /** 销售属性类型：图销0、文销1 */
    private Integer saleAttributeType;

    /** 销售属性名，从主站销售属性名用/拼接 */
    private String saleAttributeName;

    /** 排序字段，默认图销在前文销在后 */
    private Integer sort;

    /** 创建时间 */
    private Long createTime;

    /** 更新时间 */
    private Long updateTime;

    /** 创建人 */
    private String creator;

    /** 更新人 */
    private String updater;

    /** 是否删除：0-未删除，1-已删除 */
    private Integer yn;
} 