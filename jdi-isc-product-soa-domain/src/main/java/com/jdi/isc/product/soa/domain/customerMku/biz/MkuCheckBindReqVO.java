package com.jdi.isc.product.soa.domain.customerMku.biz;

import com.jdi.isc.product.soa.domain.common.biz.BaseVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class MkuCheckBindReqVO extends BaseVO {

    @NotEmpty(message = "国际mkuId不能为空")
    private List<Long> mkuIdList;
    /**
     * 客户代码。
     */
    private String clientCode;
}
