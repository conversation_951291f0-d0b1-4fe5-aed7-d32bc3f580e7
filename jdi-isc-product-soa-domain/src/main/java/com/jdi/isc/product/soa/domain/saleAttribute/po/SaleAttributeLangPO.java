package com.jdi.isc.product.soa.domain.saleAttribute.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasicLangPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 销售属性名多语表，机翻与人工校对共用
 * 这里的销售属性多语言翻译表其实是池子的概念，即相同的name共享翻译
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("jdi_isc_sale_attribute_lang_sharding")
public class SaleAttributeLangPO extends BasicLangPO {

    /** 销售属性编码，等于SaleAttributePO中的saleAttributeName，单语言内唯一 */
    private String saleAttributeCode;

}