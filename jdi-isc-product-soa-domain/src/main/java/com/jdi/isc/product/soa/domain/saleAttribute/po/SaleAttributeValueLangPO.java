package com.jdi.isc.product.soa.domain.saleAttribute.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasicLangPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 销售属性值多语表，人工输入或中台数据的机翻
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("jdi_isc_sale_attribute_value_lang_sharding")
public class SaleAttributeValueLangPO extends BasicLangPO {

    /** 来自jdi_isc_sale_attribute_value_sharding表的id字段，销售属性值id */
    private Long saleAttributeValueId;
} 