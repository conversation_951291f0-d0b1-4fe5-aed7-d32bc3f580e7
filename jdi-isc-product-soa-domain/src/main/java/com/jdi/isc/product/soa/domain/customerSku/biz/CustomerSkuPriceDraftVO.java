package com.jdi.isc.product.soa.domain.customerSku.biz;

import com.jdi.isc.product.soa.domain.common.biz.BasicVO;
import com.jdi.isc.product.soa.domain.enums.PriceTypeEnum;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceResVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRateVO;
import com.jdi.isc.product.soa.domain.validation.ValidatorGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: sku客制化价格-草稿表 VO实体类
 * @Author: zhaokun51
 * @Date: 2024/10/21 13:30
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSkuPriceDraftVO extends BasicVO {

    /**
     * SKUID
     */
    @NotNull(message = "SKU id不能为空",groups = {ValidatorGroup.customerSkuUpsert.class,ValidatorGroup.getCustomerSkuPrice.class})
    private Long skuId;

    /**
     * 来源国家
     * */
    private String sourceCountryCode;

    /**
     * 客户简码
     */
    @NotNull(message = "客户编码不能为空",groups = {ValidatorGroup.customerSkuUpsert.class,ValidatorGroup.getCustomerSkuPrice.class})
    private String clientCode;

    /**
     * 客户名称
     */
    private String clientName;

    /**
     * 目标销售国家（客户所在国家）
     */
    private String targetCountryCode;

    /**
     * 供应商简码
     */
    private String vendorCode;

    /**
     * 审批层级 0:已通过层级或跨境品待审,1:本土审批待审,2:joySky层级
     * */
    private Integer level;

    /**
     * 对客交货模式 EXW,DDP
     */
    private String customerTradeType;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 客制销售价
     */
    @NotNull(message = "客制化销售价不能为空",groups = {ValidatorGroup.customerSkuUpsert.class})
    private BigDecimal customerSalePrice;

    /**
     * 客制采购价,默认从base采购价取
     */
    private BigDecimal customerPurchasePrice;

    /**
     * 审核状态，1:审核通过，2:驳回 3:待审核
     */
    private Integer auditStatus;

    /** 商品名称*/
    private String skuName;


    /** 价格类型*/
    private PriceTypeEnum priceType;

    /**
     * 未税价
     */
    private BigDecimal salePrice;

    /**
     * 含税价
     */
    private BigDecimal includeTaxPrice;

    /**
     * 毛利率
     */
    private BigDecimal grossRate;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 流程id
     */
    private String processInstanceId;

    /**
     * 税率信息列表
     */
    private List<TaxRateVO> taxRateList;

    /**
     * 新采购价
     */
    private String newPurchasePrice;


    private Long catId;

    private Long brandId;

    private String buyer;

    private BigDecimal profitRate;

    private String warningMsg;

    private Long beginTime;

    private Long endTime;

    /** 0失效1有效2过期3未生效 */
    private Integer enableStatus;

    /** 计算利润率等 */
    private SalePriceCalculateResVO calculateResVO;

    /**
     * 调价原因
     */
    private String adjustmentPriceReason;

    /**
     * 附件
     */
    private List<String> attachmentUrls;

    /**
     * 国家池入池状态，1-已入池，3-未入池
     *
     * {@link CountryMkuPoolStatusEnum}
     */
    private Integer countryMkuPoolStatus;

    /**
     * 是否入客户池 1-绑定，0-未绑定
     *
     * {@link CustomerMkuBindEnum}
     */
    private String customerMkuPoolStatus;

    /**
     * 不可售阈值
     */
    private BigDecimal unsellableThreshold;

    /**
     * 不可售阈值更新时间
     */
    private Long unsellableThresholdTime;

    /**
     * 可售状态
     */
    private Integer availableSaleStatus;

    /**
     * 价格可售状态更新时间
     */
    private Long availableSaleStatusTime;


    public CustomerSkuPriceDraftVO(SkuPriceResVO input){
        this.skuId = input.getSkuId();
        this.currency = input.getCurrency();
        this.customerSalePrice = input.getSalePrice();
        this.customerPurchasePrice = input.getPurchasePrice();
        this.customerTradeType = input.getCustomerTradeType();
    }

    public CustomerSkuPriceDraftVO(Long skuId , String clientCode){
        this.skuId = skuId;
        this.clientCode = clientCode;
    }
}
