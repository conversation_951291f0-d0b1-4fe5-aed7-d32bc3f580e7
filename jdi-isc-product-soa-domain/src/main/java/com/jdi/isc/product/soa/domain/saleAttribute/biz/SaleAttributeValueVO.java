package com.jdi.isc.product.soa.domain.saleAttribute.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 销售属性值VO对象
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SaleAttributeValueVO {

    /** 主键ID */
    private Long id;

    /**
     * SPUID
     */
    private Long spuId;

    /** 销售属性ID */
    private Long saleAttributeId;

    /** 销售属性值名称，用户填入 */
    private String saleAttributeValueName;

    /** 排序字段 */
    private Integer sort;

    /** 创建时间 */
    private Long createTime;

    /** 更新时间 */
    private Long updateTime;

    /** 创建人 */
    private String creator;

    /** 更新人 */
    private String updater;

    /** 是否删除：0-未删除，1-已删除 */
    private Integer yn;
} 