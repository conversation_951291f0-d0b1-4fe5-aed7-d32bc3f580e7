package com.jdi.isc.product.soa.domain.countryMku.po;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description: 商品国家池表 实体类
 * @Author: wangpeng965
 * @Date: 2024/12/02 21:49
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("jdi_isc_country_mku_sharding")
public class CountryMkuPO extends BasicPO {

    /**
     * MKUID
     */
    private Long mkuId;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 末级类目id
     */
    private Long lastCatId;

    /**
     * 采购员
     */
    private String buyer;

    /**
     * 发货时效
     */
    private BigDecimal productionCycle;

    /**
     * 入池状态，0：待入池，1:已入池，2:已拉黑
     */
    private Integer poolStatus;

    /**
     * 预警状态，0：预警，1:正常
     */
    private Integer warnStatus;

    /**
     * 拉黑前状态，0：待入池，1:已入池
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer poolOriginStatus;

    /**
     * 预警原因，0:- ,1：进口许可不符合，2：出口许可不符合，3:强制商品认证不符合，4：跨境运输不符合，5:品牌授权不符合，6:跨境商品未入国内商品池
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String warnReason;

    /**
     * 拉黑原因，0:固定规则不满足，1:业务规则不满足，2:手动拉黑(0，1，2逗号分隔)
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String blackReason;

    /**
     * 待确认原因，0:商品类目禁售，1:跨境运费占比超过阈值(0，1)
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String undeterminedReason;

    /**
     * 入池失败原因
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String poolFailReason;

    /**
     * 不可售原因
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String unSaleReason;
    /**
     * 京东类目id
     */
    private Long jdCatId;

}
