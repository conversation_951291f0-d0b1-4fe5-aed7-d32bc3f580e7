package com.jdi.isc.product.soa.domain.approveorder.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

import com.jdi.isc.product.soa.domain.common.biz.BasicVO;

/**
 * 商品审核 实体类
 * <AUTHOR>
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ApproveOrderVO extends BasicVO { //BasicVO替换成自己项目实际的VO

    /**
     * 版本号
     */
    @NotNull(message = "版本号不能为空")
    private Integer version;

    /**
     * 业务类型
     */
    @NotNull(message = "业务类型不能为空")
    private Integer bizType;

    /**
     * 流程类型
     */
    @NotNull(message = "流程类型不能为空")
    private Integer flowType;

    /**
     * 业务唯一键
     */
    @NotNull(message = "业务唯一键不能为空")
    private String bizId;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 流程单号
     */
    private String applyCode;

    /**
     * 审批状态，审核状态，0待审核，10审核中，20审核通过，30审核驳回，40审核撤回
     */
    @NotNull(message = "审批状态，审核状态，0待审核，10审核中，20审核通过，30审核驳回，40审核撤回不能为空")
    private Integer auditStatus;

    /**
     * 状态，10进行中， 20 已完成
     */
    @NotNull(message = "状态，10进行中， 20 已完成不能为空")
    private Integer status;

    /**
     * 当前节点审批人
     */
    private String currentNodeAuditor;

    /**
     * 当前节点审批人erp账号
     */
    private String currentNodeErp;

    /**
     * 当前审批节点名称
     */
    private String currentNodeName;

    /**
     * 当前审批节点审核状态
     */
    private Integer currentNodeAuditStatus;

    /**
     * 1：审批通过 2：驳回
     */
    private Integer preApproveFlag;

    /**
     * 审批意见
     */
    private String preApproveComment;

    /**
     * 申请人
     */
    private String applyUserErp;

    /**
     * 申请原因
     */
    private String applyReason;

    /**
     * 审核失败原因
     */
    private String callbackFailMessage;

    /**
     * 失败次数
     */
    private Integer callbackFailCount;

}