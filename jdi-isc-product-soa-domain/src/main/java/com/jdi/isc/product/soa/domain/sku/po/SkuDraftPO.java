package com.jdi.isc.product.soa.domain.sku.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasePO;
import com.jdi.isc.product.soa.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: sku草稿表 实体类
 * @Author: zhaokun51
 * @Date: 2024/12/18 11:27
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("jdi_isc_sku_draft_sharding")
public class SkuDraftPO extends BasicPO {

    /**
     * 新扩展属性字段groupExtAttribute，json格式存储
     */
    private String groupExtAttribute;

    /**
     * SPUID
     */
    private Long spuId;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * SPU内/SKU唯一KEY,UUID
     */
    private String skuKey;

    /**
     * sku基本信息json串
     */
    private String skuJsonInfo;

    /**
     * sku资质json串
     */
    private String skuCertificate;

    /**
     * sku跨境属性json串
     */
    private String skuInterProperty;

    /**
     * sku库存
     */
    private String skuStockRelation;

    /**
     * sku销售属性
     */
    private String saleProperty;
    /**
     * 国内SKU ID
     */
    private Long jdSkuId;

    /**
     * 京东主站主skuId
     */
    private Long jdMainSkuId;

    /**
     * 京东主站spuId
     */
    private Long jdSpuId;

}