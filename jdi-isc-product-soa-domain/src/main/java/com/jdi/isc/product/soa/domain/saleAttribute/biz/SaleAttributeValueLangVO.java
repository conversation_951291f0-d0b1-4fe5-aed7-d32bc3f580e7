package com.jdi.isc.product.soa.domain.saleAttribute.biz;

import com.jdi.isc.product.soa.domain.common.biz.BasicLangVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 销售属性值多语言VO对象
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class SaleAttributeValueLangVO extends BasicLangVO {

    /** 销售属性值ID */
    private Long saleAttributeValueId;
} 