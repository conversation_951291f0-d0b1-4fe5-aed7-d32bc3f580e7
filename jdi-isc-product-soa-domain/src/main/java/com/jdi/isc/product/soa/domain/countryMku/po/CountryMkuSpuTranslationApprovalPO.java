package com.jdi.isc.product.soa.domain.countryMku.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: 国际池商品名称翻译审核表 实体类
 * @Author: zhaoyan316
 * @Date: 2024/12/20 11:00
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("jdi_isc_country_mku_spu_translation_approval_sharding")
public class CountryMkuSpuTranslationApprovalPO extends BasicPO {

    /**
     * spuId
     */
    private Long spuId;

    /**
     * 国家池国家代码
     */
    private String targetCountryCode;

    /**
     * 是否已翻译确认，0翻译未审核、1已审核通过
     */
    private Boolean approvalStatus;

}
