package com.jdi.isc.product.soa.domain.price.supplierPrice.biz;

import com.jdi.isc.product.soa.domain.common.biz.BasePageInfoVO;
import com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class SkuPricePageReqVO extends BasePageInfoVO implements Serializable {

    private String vendorCode;

    /** 货源国 */
    private String sourceCountryCode;

    /**
     * 审核状态，1:审核通过，2:驳回 3:待审核,0:撤销
     */
    private Integer auditStatus;

    /**
     * 类型关键字，用于区分不同类型的请求。
     */
    private String typeKey;

    /**
     * SKU对应的SPU ID列表
     */
    private List<Long> spuIds;

    /**
     * SKU对应的ID列表
     */
    private List<Long> skuIds;

    /**
     * MKU对应的ID列表
     */
    private List<Long> mkuIds;

    /** skuId */
    private Long skuId;

    /** 交易方向 */
    private TradeDirectionEnum tradeDirection;
}
