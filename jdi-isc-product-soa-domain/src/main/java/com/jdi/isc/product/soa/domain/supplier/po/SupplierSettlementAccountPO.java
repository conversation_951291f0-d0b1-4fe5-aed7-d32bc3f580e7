package com.jdi.isc.product.soa.domain.supplier.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasicPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description 供应商结算账号实体类。
 * @Author: zhaojianguo21
 * @Date: 2024/03/18 17:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("jdi_isc_supplier_settlement_account_sharding")
public class SupplierSettlementAccountPO extends BasicPO {

    /**
     * 供应商Code
     */
    private String supplierCode;

    /**
     * 收款单位名称
     */
    private String payeeUnitName;

    /**
     * 开户公司名称
     */
    private String companyNameOpenAccount;

    /**
     * 开户行名称
     */
    private String bankAccountNameOpen;

    /**
     * 开户银行账号
     */
    private String bankAccountNumberOpen;

    /**
     * swift code
     */
    private String swiftCode;

    /**
     * 国家代码，swift code的第五位和第六位
     */
    private String swiftCountryCode;

    /**
     * 中转行名称
     */
    private String transBankNameOpen;

    /**
     * 中转行swift code
     */
    private String transSwiftCode;

    /**
     * 收款账户性质
     */
    private String payeeAccountNature;


    /**
     * 银行账户code
     */
    private String bankFinanceAccountCode;


    /**
     * 银行联行号
     */
    private String bankKeyNo;

    /**
     * 银行所属国家
     */
    private String bankCountryCode;

    /**
     * 供应商币种
     */
    private String currency;

}
