package com.jdi.isc.product.soa.domain.taxRate.biz;


import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * 查询进口税率入参
 * <AUTHOR>
 * @description：IscSkuImportTaxReqVO
 * @Date 2025-06-17
 */
@Data
@NoArgsConstructor
public class IscSkuImportTaxReqVO {

    /**
     * SKU ID集合，用于标识需要导入税收信息的商品。
     */
    /** 跨境SkuId*/
    @NotEmpty(message = "跨境SkuId不能为空")
    @Size(max = 100, message = "单次查询操作不能输入超过100个skuId")
    private Set<Long> skuIds;
    /**国家编码*/
    private String countryCode;

    public IscSkuImportTaxReqVO(Set<Long> skuIds, String countryCode) {
        this.skuIds = skuIds;
        this.countryCode = countryCode;
    }
}
