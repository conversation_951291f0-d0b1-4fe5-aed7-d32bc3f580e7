package com.jdi.isc.product.soa.domain.warehouse.biz;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author：xubing82
 * @date：2025/8/7 11:15
 * @description：WarehouseSkuPriceResVO
 */
@Data
public class WarehouseSkuPriceResVO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 仓所在国家，销售国
     */
    private String countryCode;

    /**
     * 备货仓仓库ID
     */
    private Long warehouseId;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 国际SKU原始采购币种换算国家官方币种汇率
     */
    private BigDecimal exrateSupp2country;

    /**
     * 国家官方币种换算人民币汇率
     */
    private BigDecimal exrateCountry2cny;

    /**
     * 国家官方币种
     */
    private String countryCurrency;

    /**
     * 国际sku未税仓报价,6位小数
     */
    private BigDecimal whPrice;

    /**
     * 国际sku未税仓报价-cny,6位小数
     */
    private BigDecimal whPriceCny;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 最后修改时间
     */
    private Long updateTime;

}
