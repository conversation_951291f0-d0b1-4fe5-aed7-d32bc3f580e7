package com.jdi.isc.product.soa.domain.brand.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasicPO;
import lombok.Data;

import java.io.Serializable;

/**
 * 品牌审核表
 * @TableName jdi_isc_brand_audit_record_sharding
 */
@TableName(value ="jdi_isc_brand_audit_record_sharding")
@Data
public class BrandAuditRecordPO extends BasicPO implements Serializable {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 品牌ID
     */
    private Long brandId;
    /**
     * xbp申请单id
     */
    private Integer xbpTicketId;

    /**
     * 审核人
     */
    private String auditErp;

    /**
     * 审核状态 1通过，2驳回
     */
    private Integer auditStatus;
}