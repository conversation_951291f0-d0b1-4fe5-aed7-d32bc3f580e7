package com.jdi.isc.product.soa.domain.warehouse.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @author：xubing82
 * @date：2025/8/7 11:10
 * @description：WarehouseSkuReqVO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseSkuPriceReqVO {

    /**
     * 仓库ID
     */
    @NotNull(message = "仓库ID必填")
    private Long warehouseId;

    /**
     * SKU ID
     */
    private Long skuId;
}
