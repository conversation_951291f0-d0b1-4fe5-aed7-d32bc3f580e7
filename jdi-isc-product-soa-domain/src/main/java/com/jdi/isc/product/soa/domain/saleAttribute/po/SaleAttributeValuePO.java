package com.jdi.isc.product.soa.domain.saleAttribute.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasicPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 销售属性值实体
 * 记录了sku所有的销售属性值，saleAttributeValueName相同的属性值会属于不同sku，所以id也为多个
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("jdi_isc_sale_attribute_value_sharding")
public class SaleAttributeValuePO extends BasicPO {

    /**
     * SPUID
     */
    private Long spuId;

    /** 销售属性ID，jdi_isc_sale_attribute_sharding中的id */
    private Long saleAttributeId;

    /** 销售属性值名称，用于存储从中台获取的原始跨境属性值，用户手动输入的中文属性值，中文为必填 */
    private String saleAttributeValueName;

    /** 排序字段 */
    private Integer sort;
} 