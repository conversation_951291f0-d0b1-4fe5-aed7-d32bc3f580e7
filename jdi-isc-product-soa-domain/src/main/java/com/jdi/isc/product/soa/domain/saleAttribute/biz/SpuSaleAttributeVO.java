package com.jdi.isc.product.soa.domain.saleAttribute.biz;

import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * SPU销售属性对象
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SpuSaleAttributeVO {

    /** SPU的销售属性，其中属性值对象需要selected=true（新版销售属性不存在没有被选择的属性值） */
    private List<PropertyVO> spuSalePropertyList;

    /** key是skuId，value是sku的销售属性值list */
    private Map<Long, List<PropertyValueVO>> skuSalePropertyListMap;
    
    /** key是skuKey, value是草稿sku的销售属性值list */
    private Map<String, List<PropertyValueVO>> draftSkuSalePropertyListMap;
} 