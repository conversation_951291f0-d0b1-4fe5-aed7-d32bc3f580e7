package com.jdi.isc.product.soa.domain.enums;

/**
 * @description 必填状态
 * <AUTHOR>
 * @date 2023/11/22 20:52
 **/
public enum RequiredEnum {
    /**
     * 非必填
     */
    NOT_REQUIRED(0, "非必填"),
    /**
     * 必填
     */
    REQUIRED(1, "必填");

    private Integer code;
    private String desc;

    private RequiredEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static RequiredEnum forCode(Integer code) {
        if (code == null) {
            return null;
        }

        RequiredEnum[] values = values();
        for (RequiredEnum enumObj : values) {
            if (code.equals(enumObj.getCode())) {
                return enumObj;
            }
        }
        return null;
    }
}
