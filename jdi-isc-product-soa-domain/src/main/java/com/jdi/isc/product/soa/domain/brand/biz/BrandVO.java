
package com.jdi.isc.product.soa.domain.brand.biz;

import com.jdi.isc.product.soa.domain.category.biz.CategoryTreeVo;
import com.jdi.isc.product.soa.domain.common.biz.BaseVO;
import com.jdi.isc.product.soa.domain.enums.StatusEnum;
import com.jdi.isc.product.soa.domain.enums.validator.EnumValidation;
import com.jdi.isc.product.soa.domain.xbp.biz.XbpFlow;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 品牌视图对象
 * <AUTHOR>
 * @date 20231202
 */

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class BrandVO extends BaseVO {

    /** 品牌logo图片路径*/
    private String brandLogoPath;

    /** 排序*/
    private Integer sort;

    /** 状态 {@link StatusEnum} */
    @NotNull(message = "状态不能为空")
    @EnumValidation(clazz = StatusEnum.class, message = "状态非法")
    private Integer status;

    /** 键值对，扩展字段*/
    private String features;

    /** 品牌名称多语言*/
    @Valid
    @NotEmpty(message = "品牌名称不能为空")
    private List<BrandLangVO> langList;

    /** 适用国家*/
    @Valid
    @NotEmpty(message = "适用国家不能为空")
    private List<BrandCountryVO> countryList;

    /**
     * 品牌分层类型
     */
    private String levelType;

    /**
     * 国内品牌id
     */
    private Long jdBrandId;

    /**
     * 类目
     */
    private List<CategoryTreeVo> categoryTreeList;


    /**
     * 品牌国家
     */
    private BrandCountryVO brandCountry;

    /**
     * 全类目生效
     */
    private boolean allCategoryScope;

    /**
     * 选择的类目节点
     */
    private List<CategoryTreeVo> selectCategoryNodes;

    /**
     * 语言id
     */
    private String lang;
    /** xbp审核详情 */
    private List<XbpFlow> xbpFlows;

    /**
     * 品牌中文名
     */
    private String brandZhName;

    /**
     * 跨境品牌授权有效期
     */
    private Long authDate;
}