package com.jdi.isc.product.soa.domain.brand.biz;

import com.jdi.isc.product.soa.domain.common.biz.BasicVO;
import lombok.Data;

import java.io.Serializable;

/**
 * 品牌审核表
 * @TableName jdi_isc_brand_audit_record_sharding
 */
@Data
public class BrandAuditRecordVO extends BasicVO implements Serializable {
    /**
     * 自增ID
     */
    private Long id;

    /**
     * 品牌ID
     */
    private Long brandId;
    /**
     * xbp申请单id
     */
    private Integer xbpTicketId;

    /**
     * 审核人
     */
    private String auditErp;

    /**
     * 审核状态 1通过，2驳回
     */
    private Integer auditStatus;

}