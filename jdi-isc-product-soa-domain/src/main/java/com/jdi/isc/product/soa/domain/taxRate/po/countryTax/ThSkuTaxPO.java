package com.jdi.isc.product.soa.domain.taxRate.po.countryTax;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 泰国进口税率表
 * <AUTHOR>
 * @date 20250311
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("jdi_isc_th_jd_sku_tax_sharding")
public class ThSkuTaxPO extends BasePO {

    /**
     * 海关编码
     */
    private String hsCode;

    /**
     * SKUID
     */
    private Long jdSkuId;

    /**
     * 增值税率
     */
    private BigDecimal valueAddedTax;

    /**
     * 消费税率
     */
    private BigDecimal consumptionTax;

    /**
     * formE关税率
     */
    private BigDecimal formeTax;

    /**
     * 最惠国关税率
     */
    private BigDecimal mfnTax;

    /**
     * 反倾销税率
     */
    private BigDecimal antiDumpingTax;

    /**
     * 本地税率
     */
    private BigDecimal localTax;

    /**
     * 是否需要tisi：0=否,1=是
     */
    private Integer tisi;

}