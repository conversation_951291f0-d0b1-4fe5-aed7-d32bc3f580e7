package com.jdi.isc.product.soa.domain.taxRate.po.countryTax;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 印尼进口税率表
 * <AUTHOR>
 * @date 20250311
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("jdi_isc_id_jd_sku_tax_sharding")
public class IdSkuTaxPO extends BasePO {

    /**
     * 海关编码
     */
    private String hsCode;

    /**
     * SKUID
     */
    private Long jdSkuId;

    /**
     * 普通关税率BM
     */
    private BigDecimal normalImportTax;

    /**
     * 原产地优惠关税率FormE
     */
    private BigDecimal originMfnTax;

    /**
     * 奢侈品税率PPnBM
     */
    private BigDecimal luxuryTax;

    /**
     * 增值税率PPN
     */
    private BigDecimal valueAddedTax;

    /**
     * 预扣税率PPH
     */
    private BigDecimal withholdingTax;

    /**
     * 贸易保护关税BMT率
     */
    private BigDecimal tradeProtectionTax;

    /**
     * 是否办理产地证1是0否
     */
    private Integer isOriginCertificate;

}