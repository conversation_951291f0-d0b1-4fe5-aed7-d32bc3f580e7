package com.jdi.isc.product.soa.domain.countryMku.biz;

import com.jdi.isc.product.soa.domain.common.biz.BasicVO;
import com.jdi.isc.product.soa.domain.specialAttr.biz.SpecialAttrTagVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class CountryMkuExportVO extends BasicVO {
    /**
     * MKUID
     */
    private Long mkuId;

    /**
     * SKU的唯一标识符
     */
    private Long skuId;


    /**
     * 京东SKU ID
     */
    private Long jdSkuId;

    /**
     * mku中文名
     */
    private String mkuTitleZh;

    /**
     * mku中文名
     */
    private String mkuTitleEn;

    /**
     * 目标国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * 货源国
     */
    private String targetCountryName;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 货源国
     */
    private String sourceCountryName;
    /**
     * 类目id
     */
    private Long lastCatId;
    /**
     * 一级类目名称
     */
    private String firstCatNameZh;
    /**
     * 一级类目名称
     */
    private String firstCatNameEn;

    /**
     * 二级类目名称
     */
    private String secondCatNameZh;

    /**
     * 二级类目名称
     */
    private String secondCatNameEn;

    /**
     * 三级类目名称
     */
    private String thirdCatNameZh;

    /**
     * 三级类目名称
     */
    private String thirdCatNameEn;

    /**
     * 末级类目名称
     */
    private String lastCatNameZh;

    /**
     * 末级类目名称
     */
    private String lastCatNameEn;

    /**
     * 品牌Id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandNameZh;

    /**
     * 品牌名称
     */
    private String brandNameEn;

    /**
     * 销售单位ID
     */
    private Integer saleUnitId;

    /**
     * 销售单位名称
     */
    private String saleUnit;

    /**
     *  规格型号
     */
    private String specification;

    /**
     *  发货时效
     */
    private String productionCycle;

    /**
     * 库存
     */
    private String stockNum;
    /**
     * 是否备货
     */
    private String purchase;

    /**
     * 采购员
     */
    private String buyer;

    /**
     * 入池状态，0：待入池，1:已入池，2:已拉黑,3:未入国家池
     */
    private Integer poolStatus;

    /**
     * 入池状态的字符串表示形式。
     */
    private String poolStatusStr;

    /**
     * 拉黑原因，0:固定规则不满足，1:业务规则不满足，2:手动拉黑(0，1，2逗号分隔)
     */
    private String blackReason;

    /**
     * 预警原因，0:- ,1：进口许可不符合，2：出口许可不符合，3:强制商品认证不符合，4：跨境运输不符合，5:品牌授权不符合，6:跨境商品未入国内商品池
     */
    private String warnReason;

    /**
     * 是否有标签的标识
     */
    private String haveTag;

    /**
     * 预警状态，0：预警，1:正常
     */
    private Integer warnStatus;

    /**
     * 特殊属性标签列表
     */
    private List<SpecialAttrTagVO> specialAttrTagVOList;

    /**
     * 用于测试的标签。
     */
    private boolean testTag = false;

    /**
     * 入池失败原因
     */
    private String poolFailReason;

    /**
     * 不可售原因
     */
    private String unSaleReason;
    /**
     * 国家成本价格
     */
    private BigDecimal countryCostPrice;

    /**
     * 国家仓储价格
     */
    private BigDecimal countryWarehousingPrice;

    /**
     * 退税价格
     */
    private BigDecimal refundTaxPrice;

    /**
     * 国家协议价格
     */
    private BigDecimal countryAgreementPrice;

    /**
     * 国家VIP价格
     */
    private BigDecimal countryVipPrice;

    /**
     * 京东商品价格。
     */
    private BigDecimal jdPrice;
    /**
     * 货币类型。
     */
    private String currency;

    /**
     * 成本价格的标记。
     */
    private String costPriceMark;

    /**
     * 协议价格的标记。
     */
    private String agreementPriceMark;

    /**
     * 仓储价格的标记。
     */
    private String warehousingPriceMark;

    /**
     * 退税价格的标记。
     */
    private String refundTaxPriceMark;

    /**
     * 京东价格的标记。
     */
    private String jdPriceMark;

    /**
     * mku巴西葡萄牙语言
     */
    private String mkuTitleBr;
    /**
     * mku巴西葡萄牙语言短标题
     */
    private String mkuBrSubtitle;
    /**
     * ncmCode
     */
    private String ncmCode;
    /**
     * 销售CST
     */
    private String saleCst;
    /**
     * 品牌(葡语)
     */
    private String brandNameBr;
    /**
     * 审核状态
     */
    private Integer auditStatus;
    /**
     * 审核状态
     */
    private Integer taxAuditStatus;

}
