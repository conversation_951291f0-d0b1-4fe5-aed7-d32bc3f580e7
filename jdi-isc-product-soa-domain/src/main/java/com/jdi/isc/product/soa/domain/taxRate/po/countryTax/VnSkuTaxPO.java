package com.jdi.isc.product.soa.domain.taxRate.po.countryTax;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 越南进口税率表
 * <AUTHOR>
 * @date 20250311
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("jdi_isc_vn_jd_sku_tax_sharding")
public class VnSkuTaxPO extends BasePO {

    /**
     * 海关编码
     */
    private String hsCode;

    /**
     * SKUID
     */
    private Long jdSkuId;

    /**
     * 最惠国关税率
     */
    private BigDecimal mfnTax;

    /**
     * 原产地优惠关税率
     */
    private BigDecimal originMfnTax;

    /**
     * 消费税率
     */
    private BigDecimal consumptionTax;

    /**
     * 环保税单价
     */
    private BigDecimal environmentalTaxUnitPrice;

    /**
     * 反倾销税率
     */
    private BigDecimal antiDumpingTax;

    /**
     * 增值税率
     */
    private BigDecimal valueAddedTax;

    /**
     * 检验检疫类别
     */
    private String quarantineCategories;

    /**
     * 海关监管条件
     */
    private String customsSupervisionConditions;

    /**
     * 能否进入越南保税仓
     */
    private Integer isBondedWarehouse;

    /**
     *是否涉及越南进口限制
     */
    private Integer isImportLimit;

}