package com.jdi.isc.product.soa.domain.auth;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RouteVO {
    /**
     * id 对应权限的 菜单编码
     */
    private String id;
    /**
     * 路由地址
     */
    private String path;
    /**
     * 路由的名称
     */
    private String name;
    /**
     * 权限中配置的名称
     */
    private String title;
    /**
     * 图标
     */
    private String icon;
    /**
     * 权限
     */
    private String access;
    /**
     * 组建
     */
    private String component;
    /**
     *  隐藏菜单？ ture 是隐藏，false 不隐藏
     */
    private Boolean hideInMenu;

    /**
     * 跳转地址
     */
    private String redirect;
    /**
     * 功能集合
     */
    private List<FunctionVO> funcList;
    /**
     * 隐藏功能的集合
     */
    private List<String> hideFunctionList;


    /**
     * 子路由（子菜单或菜单功能）
     */
    private List<RouteVO> routes;
}
