package com.jdi.isc.product.soa.domain.warehouse.biz;

import com.jdi.isc.product.soa.api.common.enums.PurchaseModelTypeEnum;
import com.jdi.isc.product.soa.domain.common.biz.BasePageVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 仓库sku绑定关系分页搜索
 * <AUTHOR>
 * @date 2024/7/25
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class WarehouseSkuPageVO extends BasePageVO {

    /**
     * 仓库ID
     */
    @NotNull(message = "仓库ID必填")
    private Long warehouseId;
    /**
     * sku id或名称
     */
    //@NotNull(message = "SKU id或名称必填")
    private String skuIdOrName;
    /**
     * SKU ID
     */
    private String skuId;
    /**
     * 商品货源国
     */
    @NotBlank(message = "商品货源国必填")
    private String sourceCountryCode;

    /**
     * 贸易实体信息
     */
    private String tradeEntity;
    /**
     * 库存查询类型 0或null 供应商库存  1： 备货库存
     */
    private Integer stockType;

    /**
     * 备货模式
     *
     * @see PurchaseModelTypeEnum
     */
    private Integer purchaseModel;
}
