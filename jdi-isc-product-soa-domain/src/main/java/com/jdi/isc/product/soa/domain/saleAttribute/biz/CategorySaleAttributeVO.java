package com.jdi.isc.product.soa.domain.saleAttribute.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类目销售属性对象，用于展示类目的销售属性信息
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CategorySaleAttributeVO {

    /** 销售属性类型（图销0、文销1） */
    private Integer saleAttributeType;

    /** 销售属性ID */
    private Long saleAttributeId;

    /** 销售属性名称，用/将中台属性名按图销文销拼接 */
    private String saleAttributeName;

    /** 排序字段 */
    private Integer sort;
} 