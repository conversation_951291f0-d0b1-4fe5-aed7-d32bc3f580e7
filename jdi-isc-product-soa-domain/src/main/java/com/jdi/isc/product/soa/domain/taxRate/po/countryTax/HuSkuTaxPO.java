package com.jdi.isc.product.soa.domain.taxRate.po.countryTax;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 匈牙利进口税率表
 * <AUTHOR>
 * @date 20250311
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("jdi_isc_hu_jd_sku_tax_sharding")
public class HuSkuTaxPO extends BasePO {

    /**
     * 海关编码
     */
    private String hsCode;

    /**
     * SKUID
     */
    private Long jdSkuId;

    /**
     * 进口关税率
     */
    private BigDecimal importTax;

    /**
     * 增值税率
     */
    private BigDecimal valueAddedTax;

    /**
     * 反补贴税率
     */
    private BigDecimal antiSubsidyTax;

    /**
     * 反倾销税率
     */
    private BigDecimal antiDumpingTax;

    /**
     * 是否办理产地证1是0否
     */
    private Integer isOriginCertificate;

}