package com.jdi.isc.product.soa.domain.price.agreementPrice.po;

import com.jdi.isc.product.soa.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description: 国家协议价 实体类
 * @Author: wangpeng965
 * @Date: 2025/03/03 16:45
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class CountryAgreementPriceBasePO extends BasicPO {

    /**
     * 业务编号
     */
    private String bizNo;

    /**
     * MKUID
     */
    private Long mkuId;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 目标国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * 末级类目id
     */
    public Long lastCatId;

    /**
     * 品牌
     */
    private Long brandId;

    /**
     * 协议价
     */
    private BigDecimal agreementPrice;

    /**
     * 国家成本价 标记字段必须强制更新（即使允许为 null）
     */
    private BigDecimal countryCostPrice;

    /**
     * 货币类型。
     */
    private String currency;

    /**
     * 国家协议价计算流程。
     */
    private String agreementMark;


    /**
     * 国家成本价计算流程。
     */
    private String costMark;

    /**
     * 京东类目id
     */
    public Long jdCatId;

}
