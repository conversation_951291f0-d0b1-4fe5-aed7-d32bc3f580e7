package com.jdi.isc.product.soa.domain.customerSku.biz;

import com.jdi.isc.product.soa.domain.common.biz.BasicVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @Description: sku客制化价格-草稿表 VO实体类
 * @Author: zhaokun51
 * @Date: 2025/01/14 13:30
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSkuPriceAuditResVO extends BasicVO {

    /** 申请单号 */
    private String sourceCountryCode;

    /** 申请单号 */
    private String applyCode;

    /** 客户编码 */
    private String clientCode;

    /** 客户名称 */
    private String clientName;

    /** skuId */
    private Long skuId;

    /** skuName */
    private String skuName;

    /** mkuId */
    private Long mkuId;

    /** 类目id */
    private Long catId;

    /** 类目名称 */
    private String catName;

    /** 品牌id */
    private Long brandId;

    /** 品牌名称 */
    private String brandName;

    /** 供应商编码 */
    private String vendorCode;

    /** 供应商名称 */
    private String vendorName;

    /** 采销 */
    private String buyer;

    /** 利润率 */
    private BigDecimal profitRate;

    /** 预警情况 */
    private String warningMsg;

    /** 发起人ERP */
    private String updater;

    /**  开始时间 */
    private Long beginTime;

    /** 结束时间 */
    private Long endTime;

    /** 0失效1有效2过期3未生效 */
    private Integer enableStatus;

    /**
     * 审批流表单
     */
    private String processFromData;

    /**
     * 自定义列
     */
    private Map<String, String> customColumns;

}
