package com.jdi.isc.product.soa.domain.saleAttribute.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 中台SKU销售属性VO对象
 * 对应JdProductDTO中的saleAttributes和saleAtts字段结构
 * 格式：[{"dim":1,"saleName":"型号","saleValue":"8MM","sequenceNo":1}]
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JdSkuSaleAttributeVO {

    /** 维度（dim） */
    private Integer dim;

    /** 销售属性名称（saleName） */
    private String saleName;

    /** 销售属性值（saleValue） */
    private String saleValue;

    /** 序号（sequenceNo） */
    private Integer sequenceNo;
} 