package com.jdi.isc.product.soa.domain.saleAttribute.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasicPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 销售属性与sku的映射关系，用来获取sku的销售属性值id list
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("jdi_isc_sku_sale_attribute_value_relation_sharding")
public class SkuSaleAttributeValueRelationPO extends BasicPO {

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * SKU草稿唯一键，用于草稿阶段关联
     */
    private String skuKey;

    /**
     * 销售属性值id
     */
    private Long saleAttributeValueId;
} 