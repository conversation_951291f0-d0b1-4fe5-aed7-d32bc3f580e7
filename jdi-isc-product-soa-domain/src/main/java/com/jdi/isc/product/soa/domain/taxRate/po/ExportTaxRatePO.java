package com.jdi.isc.product.soa.domain.taxRate.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description: 中国出口税率信息 实体类
 * @Author: zhaojianguo21
 * @Date: 2024/11/25 20:58
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("jdi_isc_export_tax_rate_sharding")
public class ExportTaxRatePO extends BasicPO {

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 海关编码
     */
    private String hsCode;

    /**
     * 出口征税率
     */
    private BigDecimal exportTaxRate;

    /**
     * 出口退税率
     */
    private BigDecimal exportRebateRate;

    /**
     * 海关控制条件
     */
    private String customsCondition;

    /**
     * 检验检疫类别
     */
    private String quarantineCat;

}
