package com.jdi.isc.product.soa.domain.enums.price;

import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceVO;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Getter
@NoArgsConstructor
public enum AgreementPriceSwitchEnums {
    AUTO_SET_EMPTY_AGREEMENT_PRICE(1, "协议价为空，自动设置国家协议价"){
        @Override
         public void setAgreementPrice(CountryAgreementPriceVO countryAgreementPriceVO) {
            if(Objects.isNull(countryAgreementPriceVO.getAgreementPrice()) || countryAgreementPriceVO.getAgreementPrice().compareTo(BigDecimal.ZERO) == 0 ){
                countryAgreementPriceVO.setAgreementPrice(countryAgreementPriceVO.getSuggestAgreementPrice());
                String agreementMark = countryAgreementPriceVO.getSuggestAgreementMark().replaceAll("建议", "");
                countryAgreementPriceVO.setAgreementMark(agreementMark);
                countryAgreementPriceVO.setAgreementUpdateTime(new Date().getTime());
            }
         }
    },
    NO_AUTO_SET_AGREEMENT_PRICE(2, "不自动设置国家协议价"){
        @Override
        public void setAgreementPrice(CountryAgreementPriceVO countryAgreementPriceVO) {

        }
    },
    AUTO_SET_AGREEMENT_PRICE(3, "自动设置国家协议价"){
        @Override
        public void setAgreementPrice(CountryAgreementPriceVO countryAgreementPriceVO) {
            countryAgreementPriceVO.setAgreementPrice(countryAgreementPriceVO.getSuggestAgreementPrice());
            String agreementMark = countryAgreementPriceVO.getSuggestAgreementMark().replaceAll("建议", "");
            countryAgreementPriceVO.setAgreementMark(agreementMark);
            countryAgreementPriceVO.setAgreementUpdateTime(new Date().getTime());
        }
    },
    VIP_TO_AGREEMENT_PRICE(4, "vip价同步到国家协议价"){
        @Override
        public void setAgreementPrice(CountryAgreementPriceVO countryAgreementPriceVO) {
            if(Objects.isNull(countryAgreementPriceVO.getAgreementPrice()) || countryAgreementPriceVO.getAgreementPrice().compareTo(BigDecimal.ZERO) == 0 ){
                countryAgreementPriceVO.setAgreementPrice(countryAgreementPriceVO.getSuggestAgreementPrice());
                String agreementMark = countryAgreementPriceVO.getSuggestAgreementMark().replaceAll("建议", "");
                countryAgreementPriceVO.setAgreementMark(agreementMark);
                countryAgreementPriceVO.setAgreementUpdateTime(new Date().getTime());
            }
        }
    }
    ;
    private Integer type;
    private String desc;

    AgreementPriceSwitchEnums(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static AgreementPriceSwitchEnums forType(Integer type) {
        if (type == null) {
            return null;
        }
        AgreementPriceSwitchEnums[] values = values();
        for (AgreementPriceSwitchEnums enumObj : values) {
            if (type.equals(enumObj.getType())) {
                return enumObj;
            }
        }
        return AUTO_SET_EMPTY_AGREEMENT_PRICE;
    }

   public abstract void setAgreementPrice(CountryAgreementPriceVO countryAgreementPriceVO);
}
