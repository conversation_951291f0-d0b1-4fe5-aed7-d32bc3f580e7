package com.jdi.isc.product.soa.domain.price.supplierPrice.biz;

import lombok.Data;

import java.io.Serializable;

/**
 * The type Audit column config dto.
 *
 * <AUTHOR>
 */
@Data
public class CustomColumnVO implements Serializable {
    /**
     * 列名称
     */
    private String title;

    private String value;

    /**
     * 拼接符号（%、￥等）
     */
    private String appendSymbol;

    private Integer width;

    private String dataIndex;

    private String key;

    private boolean omitNil;

    private boolean hideInSearch;

    private String align;
}
