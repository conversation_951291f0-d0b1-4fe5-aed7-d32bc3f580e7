package com.jdi.isc.product.soa.domain.customerSku.biz;

import com.jdi.isc.product.soa.api.approveorder.common.AuditStatusEnum;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import com.jdi.isc.product.soa.domain.common.biz.BasePageVO;
import com.jdi.isc.product.soa.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * @Description: sku客制化价格预警表 VO实体类
 * @Author: zhaokun51
 * @Date: 2025/03/17 16:51
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class CustomerSkuPriceWarningPageReqVO extends BasePageVO {

    private Integer warningStatus;

    private String targetCountryCode;

    private List<Long> skuIds;
    /**
     * skuId、spuId、jdSkuId、mkuId
     */
    private List<Long> productIds;


    // add 2025-07-01

    /** 货源国 */
    private String sourceCountryCode;

    /**
     * 国家池入池状态，1-已入池，3-未入池
     *
     * {@link CountryMkuPoolStatusEnum}
     */
    private Integer countryMkuPoolStatus;

    /**
     * 是否入客户池（允许T+1）1-绑定，0-未绑定
     *
     * {@link CustomerMkuBindEnum}
     */
    private String customerMkuPoolStatus;

    /**
     * 价格可售状态
     */
    private Integer availableSaleStatus;

    /**
     * 预警状态变更来源(预警来源) 1：国家协议价 2国家成本价
     */
    private Integer dataStatusSource;

    /** 一级类目 */
    private Long firstCatId;

    /** 二级类目 */
    private Long secondCatId;

    /** 三级类目 */
    private Long thirdCatId;

    /** 四级类目 */
    private Long lastCatId;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 开始预警时间
     */
    private Long beginWarningTime;

    /**
     * 结束预警时间
     */
    private Long endWarningTime;

    /**
     * 预警状态多选
     */
    private List<Integer> warningStatusList;

    /**
     * 审核状态
     * {@link AuditStatusEnum}
     */
    private Integer auditStatus;

    // ----------下面是程序自动添加字段-------//

    /**
     * 末级类目id（前端忽略）
     * 前端传1234级类目id，忽略此字段
     */
    private Set<Long> lastCatIds;

    /**
     * 排除非正式客户（前端忽略）
     */
    private List<String> preseletorClientCodeList;

    public boolean queryCategory() {
        return this.firstCatId != null || this.secondCatId != null || this.thirdCatId != null || this.lastCatId != null;
    }

    /**
     * 是否测试品
     */
    public Integer testProduct;

}
