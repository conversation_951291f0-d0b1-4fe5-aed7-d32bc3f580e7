package com.jdi.isc.product.soa.domain.spu.biz;

import com.jdi.isc.product.soa.domain.common.biz.BaseLangVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 商品属性对象
 *
 * <AUTHOR>
 * @date 2023/11/27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PropertyVO {

    /**
     * Integer shield（隐藏属性 shield字段1为隐藏）
     */
    private Integer shield;
    /**
     * String isQuJianZhi（为 1 表明为区间属性）
     */
    private String isQuJianZhi;

    /**
     *
     */
    /**
     * 仅适用于SPU级别的静态常量, 0才能给spu用
     */
    public static final Integer ONLY_SPU_LEVEL_EXT_ATTR_VALUE=0;

    /**
     * 属性组id
     */
    private Integer comGroupId;

    /**
     * 属性组名称
     */
    private String comGroupName;

    /**
     * SKU属性下沉  属性级别  0:product  1:sku  2:product和sku都能用
     * attrLevel:0   product
     * attrLevel:1   sku
     * attrLevel:2   都可以
     * 默认 = attrLevel:0
     */
    private Integer level;

    /**
     * 属性名
     */
    private String attributeName;
    /**
     * 属性ID
     */
    @NotNull(message = "属性ID不能为空")
    private Long attributeId;
    /**
     * 属性类型
     */
    private Integer attributeType;
    /**
     * 属性值录入类型
     */
    private Integer attributeInputType;
    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否必填
     */
    private Boolean required;
    /**
     * 属性值列表
     */
    @NotEmpty(message = "属性值列表不能为空")
    private List<PropertyValueVO> propertyValueVOList;
    /**
     * 输入为文本时校验填入内容：1：字符串 2:数字
     */
    private Integer inputCheckType;
    /**
     * vc是否展示
     */
    private Integer showSupplier;

    /**
     * 当属性名称多语值
     * */
    private List<BaseLangVO> langList;

}
