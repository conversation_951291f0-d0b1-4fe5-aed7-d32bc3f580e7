package com.jdi.isc.product.soa.domain.customerSku.biz;

import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: sku客制化价格预警表 VO实体类
 * @Author: zhaokun51
 * @Date: 2025/03/17 16:51
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class CustomerSkuPriceWarningVO extends BasicVO {

    /**
     * 关联sku客制化明细表id
     */
    @NotNull(message = "关联sku客制化明细表id不能为空")
    private Long bizId;

    /**
     * SKUID
     */
    @NotNull(message = "SKUID不能为空")
    private Long skuId;

    /**
     * SKU名称
     */
    private String skuName;

    /**
     * 客户简码
     */
    @NotNull(message = "客户简码不能为空")
    private String clientCode;

    /**
     * 对客交货模式 EXW,DDP
     */
    @NotNull(message = "对客交货模式 EXW,DDP不能为空")
    private String customerTradeType;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    @NotNull(message = "币种:VND越南,THB泰国,CNY人民币,USD美元不能为空")
    private String currency;

    /**
     * 客制销售价
     */
    @NotNull(message = "客制销售价不能为空")
    private BigDecimal customerSalePrice;

    /**
     * 开始时间
     */
    private Long beginTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 利润率
     */
    private BigDecimal profitRate;

    /**
     * 利润率阈值低
     */
    private BigDecimal profitRateLimit;

    /**
     * 利润率阈值高
     */
    private BigDecimal profitRateLowLimit;

    /**
     * 预警状态
     */
    private Integer warningStatus;

    /**
     * 绑定状态
     */
    private String bindStatus;

    /**
     * 绑定状态名称
     */
    private String bindStatusName;

    /**
     * 预警状态变更来源
     */
    private Integer dataStatusSource;



    /** 货源国 */
    private String sourceCountryCode;

    /**
     * 价格可售状态
     */
    private Integer availableSaleStatus;

    /**
     * 价格可售状态更新时间
     */
    private Long availableSaleStatusTime;

    /**
     * 类目id(反射使用属性名不能改)
     */
    private Long jdCatId;

    /**
     * 类目名称(反射使用属性名不能改)
     */
    private String catName;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 不可售利润率阈值
     */
    private BigDecimal unsellableThreshold;

    /**
     * 不可售利润率阈值更新时间
     */
    private Long unsellableThresholdTime;

    /**
     * 采销erp
     */
    private String buyerErp;

    /**
     * 国家池入池状态，1-已入池，3-未入池
     *
     * {@link CountryMkuPoolStatusEnum}
     */
    private Integer countryMkuPoolStatus;

    /**
     * 是否入客户池 1-绑定，0-未绑定
     *
     * {@link com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum}
     */
    private String customerMkuPoolStatus;

    /**
     * 审批状态
     * {@link com.jdi.isc.product.soa.api.approveorder.common.AuditStatusEnum}
     */
    private Integer auditStatus;

    /**
     * 是否测试品
     */
    private Integer testProduct;

    /**
     * {@link com.jdi.isc.product.soa.api.approveorder.common.ApproveOrderStatusEnum}
     */
    private Integer approveStatus;

    /**
     * 审核表ID
     */
    private Long approveId;

    /**
     * 审核表ID
     */
    private String applyCode;

    /**
     * 是否展示提交审核按钮
     */
    private boolean showAuditSubmitButton;

    /**
     * 是否展示提交审核按钮
     */
    private boolean showAuditRevokeButton;

    /**
     * 名牌名称
     */
    private String brandName;
}
