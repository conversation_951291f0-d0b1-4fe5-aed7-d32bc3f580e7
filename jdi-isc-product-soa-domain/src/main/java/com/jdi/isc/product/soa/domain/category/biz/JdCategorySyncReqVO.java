package com.jdi.isc.product.soa.domain.category.biz;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description：京东类目同步入参
 * @Date 2025-05-21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JdCategorySyncReqVO {
    /**
     * 京东一级类目
     */
    @NotNull(message = "jdFirstCatId can not be empty")
    private Long jdFirstCatId;
    /**
     * 操作人
     */
    @NotNull(message = "operator can not be empty")
    private String operator;
}
