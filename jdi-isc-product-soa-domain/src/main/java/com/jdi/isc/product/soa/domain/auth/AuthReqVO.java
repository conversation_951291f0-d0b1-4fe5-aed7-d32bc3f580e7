package com.jdi.isc.product.soa.domain.auth;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AuthReqVO {
    /**
     * erp账号
     */
    private String erp;
    /**
     * 菜单的资源码
     */
    private String resCode;
    /**
     * 角色编码
     */
    private String roleCode;
    /**
     * 子应用编码
     */
    private String subAppCode;
    /**
     * 维度资源编码，Auth->数据&维度资源->维度资源->维度资源编码
     */
    private String dimResCode;
    /**
     * 数据资源编码，Auth->数据&维度资源->数据资源->数据资源编码
     */
    private String dataResCode;
    /**
     * 角色编码列表
     */
    private List<String> roleCodeList;
    /**
     * 菜单、菜单功能资源code列表
     */
    public List<String> resCodes;
}
