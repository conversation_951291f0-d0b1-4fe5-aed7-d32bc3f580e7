package com.jdi.isc.product.soa.domain.saleAttribute.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.soa.domain.common.po.BasicPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 销售属性实体
 * 同一个销售属性可以属于多个spu和sku，同一个jdCatId的销售属性生成后id就不变了、只会变名字
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("jdi_isc_sale_attribute_sharding")
public class SaleAttributePO extends BasicPO {

    /** 主站类目ID */
    private Long jdCatId;

    /** 销售属性类型（图销0、文销1），同一个jdCatId下只能有一个图销和一个文销name */
    private Integer saleAttributeType;

    /** 销售属性名，默认中文名称，从主站销售属性名用/拼接而来 */
    private String saleAttributeName;

    /** 排序字段，默认图销在前，文销在后 */
    private Integer sort;
} 