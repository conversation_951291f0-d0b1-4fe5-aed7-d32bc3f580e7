package com.jdi.isc.product.soa.domain.warehouse.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author：xubing82
 * @date：2025/8/7 11:09
 * @description：WarehouseSkuPriceBatchVO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WarehouseSkuPriceBatchVO {

    /**
     * 仓所在国家，销售国
     */
    @NotNull(message = "国家码必填")
    private String countryCode;


    /**
     * 仓及sku信息
     */
    List<WarehouseSkuPriceReqVO> warehouseSkuPriceReqList;

}
