package com.jd.international.soa.provider.product;

import com.alibaba.fastjson.JSON;
import com.jd.international.soa.provider.InternationalSoaApplication;
import com.jd.international.soa.sdk.common.customer.res.CustomerDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientPageReqDTO;
import com.jd.international.soa.service.es.manage.ProductManageService;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.enums.MkuFeatureTagEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Collections;

@Slf4j
@SpringBootTest(classes = InternationalSoaApplication.class)
@AutoConfigureMockMvc
public class MkuTest {

    @Resource
    private ProductManageService ProductManageService;

    @Test
    public void page() throws Exception {
        IscMkuClientPageReqDTO input = new IscMkuClientPageReqDTO();
        input.setClientCode("CLIENT123");
        input.setLang("en");
//        List<Long> catIds = new ArrayList<>();
//        catIds.add(49L);
//        input.setCatIds(catIds);
       // input.setKeyword("更新");
        CustomerDTO customerDTO = new CustomerDTO();
        customerDTO.setClientCode("CLIENT123");
        customerDTO.setCountry("TH");
        input.setCustomerDTO(customerDTO);
        input.setIndex(1L);
        input.setSize(5L);
        input.setFeatureTags(Collections.singleton(MkuFeatureTagEnum.SHIPPING_48_HOUR.getCode()));
        PageInfo<IscMkuClientDTO> result = ProductManageService.search(input);
        System.out.println(" >>>>>>>>>>>>>> " + JSON.toJSONString(result));
    }
}
