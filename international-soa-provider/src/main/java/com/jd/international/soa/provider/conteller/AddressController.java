package com.jd.international.soa.provider.conteller;

import com.jd.international.soa.base.bean.RPCResult;
import com.jd.international.soa.sdk.common.address.AddressManagerProvider;
import com.jd.international.soa.sdk.common.address.req.*;
import com.jd.international.soa.sdk.common.address.res.AddressManagerRes;
import com.jd.international.soa.sdk.common.address.res.PageRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/11 13:45
 */
@RestController
@RequestMapping("/address")
@Slf4j
public class AddressController {

    @Resource
    private AddressManagerProvider addressManagerProvider;


    @RequestMapping("/add")
    public RPCResult addAddress(AddAddressManagerReq addAddressManagerReq){
        return addressManagerProvider.addAddress(addAddressManagerReq);
    }

    /**
     * 修改地址信息
     * @param updateAddressManagerReq 请求参数
     * @return 成功与否
     */
    @RequestMapping("/update")
    public RPCResult updateAddressInfo(UpdateAddressManagerReq updateAddressManagerReq){
        return addressManagerProvider.updateAddressInfo(updateAddressManagerReq);
    }

    /**
     * 批量删除地址
     * @param addressIdList id列表
     * @return 成功与否
     */
    @RequestMapping("/bathdel")
    public RPCResult batchDelAddress(@RequestParam(required = false) List<Long> addressIdList){
        List<Long> address = new ArrayList<>();
        address.add(1L);
        address.add(3L);
        return addressManagerProvider.batchDelAddress(address);
    }

    /**
     * 通过id 查询详细的地址信息
     * @param id 地址id
     * @return 地址详情
     */
    @RequestMapping("/querybyid")
    public RPCResult<AddressManagerRes> queryAddressDetailById(Long id){
        return addressManagerProvider.queryAddressDetailById(id);
    }

    /**
     * 将指定的地址设置为默认地址
     * @param id 地址id
     * @param pin 用户标识
     * @return 是否成功
     */
    @RequestMapping("/setdefault")
    public RPCResult setStatusForDefault(Long id  , String pin){
        return addressManagerProvider.setStatusForDefault(id, pin);
    }

    /**
     * 分页查询地址信息数据
     * @param req 分页参数
     * @return 分页数据
     */
    @RequestMapping("/querypage")
    public RPCResult<PageRes<AddressManagerRes>> queryPageAddress(@RequestBody PageAddressManagerReq req){
        return addressManagerProvider.queryPageAddress(req);
    }

    /**
     * 指定地址给采购员
     * @param req 请求参数
     * @return 是否成功
     */
    @RequestMapping("/pointaddress")
    public RPCResult addAddressAccessories(AddAddressAccessoriesReq req){
        return addressManagerProvider.assignAddressToSb(req);
    }

    @RequestMapping("/getfull")
    public RPCResult getFullAddress(SearchAllAddressReq req){
        return addressManagerProvider.queryFullAddress(req);
    }



}
