package com.jd.international.soa.provider.controller;

import com.jd.international.soa.base.bean.RPCResult;
import com.jd.international.soa.sdk.common.category.CategoryProvider;
import com.jd.international.soa.sdk.common.category.req.CategoryReq;
import com.jd.international.soa.sdk.common.category.res.CategoryRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/13 17:27
 */
@RestController
@RequestMapping("/category")
@Slf4j
public class CategoryController {

    @Resource
    private CategoryProvider categoryProvider;

    @RequestMapping("/query")
    public RPCResult<List<CategoryRes>> queryAllCate(String pin){
        return categoryProvider.queryAllCategory(pin);
    }

    @RequestMapping("/transform")
    public RPCResult transformLanguage(@RequestBody List<CategoryReq> categoryRes){
        return categoryProvider.transformLanguage(categoryRes);
    }

    @RequestMapping("/gettree")
    public RPCResult getCategoryTree(String pin){
        return categoryProvider.getTreeCategoryList(pin);
    }

}
