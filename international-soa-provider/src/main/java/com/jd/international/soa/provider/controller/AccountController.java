package com.jd.international.soa.provider.controller;

import com.jd.international.soa.base.bean.RPCResult;
import com.jd.international.soa.sdk.common.account.AccountProvider;
import com.jd.international.soa.sdk.common.account.res.AccountInfoRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 账户控制层
 * <AUTHOR>
 * @since 2022/10/17 17:52
 */
@RestController
@RequestMapping("/account")
@Slf4j
public class AccountController {

    @Resource
    private AccountProvider accountProvider;

    /**
     * 根据用户标识查询用户详细信息
     *
     * @param pin 用户标识
     * @return 用户账户详细信息
     */
    @RequestMapping(value = "/query" , method = RequestMethod.POST)
    public RPCResult<AccountInfoRes> queryAccountInfoByPin(String pin){
        return accountProvider.queryAccountInfoByPin(pin);
    }

}
