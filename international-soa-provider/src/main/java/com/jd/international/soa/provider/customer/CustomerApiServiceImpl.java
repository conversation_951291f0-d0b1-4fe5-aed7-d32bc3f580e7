package com.jd.international.soa.provider.customer;

import com.jd.international.soa.rpc.customer.RpcCustomerService;
import com.jd.international.soa.sdk.common.customer.CustomerApiService;
import com.jd.international.soa.sdk.common.customer.res.CustomerDTO;
import com.jd.international.soa.service.adapter.customer.CustomerConvert;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.wisp.api.customer.res.CustomerReadResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2024/3/14 20:49
 **/
@Slf4j
@Service
public class CustomerApiServiceImpl implements CustomerApiService {

    @Resource
    private RpcCustomerService rpcCustomerService;

    @Override
    public DataResponse<CustomerDTO> queryCustomerByClientCode(String clientCode) {
        CustomerReadResp customerReadResp = rpcCustomerService.getCustomerByClientCode(clientCode);
        CustomerDTO customerDTO = CustomerConvert.INSTANCE.readReqToDTO(customerReadResp);
        return DataResponse.success(customerDTO);
    }

    @Override
    public DataResponse<CustomerDTO> getCustomerByContractNum(String contractNum) {
        CustomerReadResp customerReadResp = rpcCustomerService.getCustomerByContractNum(contractNum);
        CustomerDTO customerDTO = CustomerConvert.INSTANCE.readReqToDTO(customerReadResp);
        return DataResponse.success(customerDTO);
    }

    @Override
    public DataResponse<CustomerDTO> getGuestByOpenArea(String area) {
        CustomerReadResp customerReadResp = rpcCustomerService.getGuestByOpenArea(area);
        if (null==customerReadResp){
            log.info("getGuestByOpenArea, CustomerReadResp is null. area={}", area);
            return DataResponse.success(null);
        }
        CustomerDTO customerDTO = CustomerConvert.INSTANCE.readReqToDTO(customerReadResp);
        return DataResponse.success(customerDTO);
    }
}
