package com.jd.international.soa.provider.adapter.customer;

import com.jd.international.soa.sdk.common.customer.res.CustomerDTO;
import com.jdi.isc.aggregate.read.wisp.api.customer.res.CustomerReadResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 客户对象转换
 *
 * <AUTHOR>
 * @date 2024/03/14
 **/
@Mapper
public interface CustomerConvert {

    CustomerConvert INSTANCE = Mappers.getMapper(CustomerConvert.class);


    CustomerDTO readReqToDTO(CustomerReadResp input);

}
