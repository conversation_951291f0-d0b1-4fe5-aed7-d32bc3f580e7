package com.jd.international.soa.provider.controller.health;

import com.jd.international.soa.base.bean.RPCResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查接口
 */
@Slf4j
@RestController
@RequestMapping("/jdig/check")
public class HealthController {
    /**
     * 检查服务是否可用。
     * @return 服务状态，"ok" 表示服务正常运行。
     */
    @GetMapping("/health")
    public RPCResult healthCheck() {
        return RPCResult.success();
    }
}


