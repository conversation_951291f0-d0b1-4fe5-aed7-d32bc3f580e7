package com.jd.international.soa.provider.controller;

import com.jd.international.soa.base.bean.RPCResult;
import com.jd.international.soa.sdk.wares.attribute.Res.PageRes;
import com.jd.international.soa.sdk.wares.attribute.Res.SkuMultilingualRes;
import com.jd.international.soa.sdk.wares.attribute.SkuMultilingualProvider;
import com.jd.international.soa.sdk.wares.attribute.req.AddSkuAttributeTranslateReq;
import com.jd.international.soa.sdk.wares.attribute.req.PageSkuMultilingualReq;
import com.jd.international.soa.sdk.wares.attribute.req.UpdateAttributeTranslateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2022/10/26 17:46
 */

@RestController
@RequestMapping("/attribute")
@Slf4j
public class SkuMultilingualController {

    @Resource
    private SkuMultilingualProvider skuMultilingualProvider;

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public RPCResult add(AddSkuAttributeTranslateReq req) {
        return skuMultilingualProvider.addSkuAttributeTranslate(req);
    }

    @RequestMapping(value = "/deleteSkuAttributeTranslate", method = RequestMethod.POST)
    public RPCResult deleteSkuAttributeTranslate(String skuId, String language, String pin) {
        return skuMultilingualProvider.deleteSkuAttributeTranslate(skuId, language, pin);
    }

    @RequestMapping(value = "/deleteAttributeBySkuId", method = RequestMethod.POST)
    public RPCResult deleteAttributeBySkuId(String skuId, String pin) {
        return skuMultilingualProvider.deleteAttributeBySkuId(skuId, pin);
    }

    @RequestMapping(value = "/deleteAttributeById", method = RequestMethod.POST)
    public RPCResult deleteAttributeById(Long id, String pin) {
        return skuMultilingualProvider.deleteAttributeById(id, pin);
    }

    @RequestMapping(value = "/updateAttribute", method = RequestMethod.POST)
    public RPCResult updateAttribute(UpdateAttributeTranslateReq req) {
        return skuMultilingualProvider.updateAttribute(req);
    }

    @RequestMapping(value = "/queryPage", method = RequestMethod.POST)
    public RPCResult<PageRes<SkuMultilingualRes>> queryPage(PageSkuMultilingualReq req) {
        return skuMultilingualProvider.queryPage(req);
    }

    @RequestMapping(value = "/queryDetail", method = RequestMethod.GET)
    public RPCResult<SkuMultilingualRes> queryDetailById(Long id) {
        return skuMultilingualProvider.queryDetailById(id);
    }


}
