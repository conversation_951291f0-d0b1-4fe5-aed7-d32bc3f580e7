package com.jd.international.soa.provider.isc.mku;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.international.soa.domain.wares.product.MkuContextDTO;
import com.jd.international.soa.provider.adapter.MkuConvert;
import com.jd.international.soa.rpc.isc.mku.RpcMkuService;
import com.jd.international.soa.rpc.xbp.XbpTicketRpcService;
import com.jd.international.soa.sdk.common.common.ProductPricesDTO;
import com.jd.international.soa.sdk.common.isc.mku.IscMkuClientApiService;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.*;
import com.jd.international.soa.sdk.common.isc.mku.req.MkuClientStockSoaDTO;
import com.jd.international.soa.sdk.common.isc.mku.req.MkuClientStockSoaReqDTO;
import com.jd.international.soa.sdk.common.isc.mku.req.MkuReplenishmentSoaDTO;
import com.jd.international.soa.sdk.wares.common.ExpectDeliveryEnum;
import com.jd.international.soa.service.common.currency.CurrencySymbolService;
import com.jd.international.soa.service.es.manage.ProductManageService;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.xbp.jsf.api.request.ticket.CreateParam;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.aggregate.read.wisp.api.delivery.resp.DeliveryAgingResp;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.*;
import com.jdi.isc.product.soa.api.common.LangConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2023/12/16 17:48
 **/
@Slf4j
@Service
public class IscMkuClientApiServiceImpl implements IscMkuClientApiService {

    @Resource
    private RpcMkuService rpcMkuService;
    @Resource
    private CurrencySymbolService currencySymbolService;
    @Resource
    private XbpTicketRpcService xbpTicketRpcService;
    @Resource
    private ProductManageService productManageService;

    @Value("${config.mkuContext}")
    private String mkuContext;

    @LafValue("jdi.isc.xbp.default.applyer")
    private String xbpApplyer;
    @LafValue("jdi.isc.xbp.default.apply.id")
    private Integer applyId;


    @Override
    public DataResponse<PageInfo<IscMkuClientDTO>> page(IscMkuClientPageReqDTO input) {
        log.info("page, input={}", JSONObject.toJSONString(input));
//        MkuClientPageReqApiDTO reqApiDTO = MkuConvert.INSTANCE.mkuClientPageReqDTO2ApiDTO(input);
//        PageInfo<MkuClientApiDTO> pageInfo = rpcMkuService.page(reqApiDTO);
//        if (null == pageInfo) {
//            log.warn("pageInfo null. input={}", JSONObject.toJSONString(input));
//            return DataResponse.error("data empty.");
//        }
//
//        PageInfo<IscMkuClientDTO> pageInfoRes = MkuConvert.INSTANCE.pageMkuClientApiDTO2DTO(pageInfo);
//        // 填充币种符号
//        if (null != pageInfoRes && CollectionUtils.isNotEmpty(pageInfoRes.getRecords())) {
//            Map<String, String> symbolMap = currencySymbolService.allSymbols();
//            // 遍历商品
//            for (IscMkuClientDTO mkuClientDTO : pageInfoRes.getRecords()) {
//                fillCurrencySymbol(mkuClientDTO, symbolMap);
//                mkuClientDTO.setMoqText(getMoqText(mkuClientDTO.getMoq(), input.getLang()));
//            }
//        }
        return DataResponse.success();
    }

    public String getMoqText(Integer moq, String lang) {
        if (moq == null) {
            return null;
        }
        JSONObject mkuContextObject = JSONObject.parseObject(mkuContext);
        MkuContextDTO mkuContextDTO = mkuContextObject.getObject(lang, MkuContextDTO.class);
        if (mkuContextDTO == null) {
            mkuContextDTO = mkuContextObject.getObject(LangConstant.LANG_ZH, MkuContextDTO.class);
        }
        return String.format(mkuContextDTO.getMoqText(), moq);
    }

//    @Override
//    public DataResponse<Boolean> existsMku(IscMkuClientDetailReqDTO input) {
//        log.info("existsMku, input={}", JSONObject.toJSONString(input));
//        MkuClientDetailReqApiDTO reqApiDTO = MkuConvert.INSTANCE.mkuClientReqDTO2ApiDTO(input);
//        Boolean existsMku = rpcMkuService.existsMku(reqApiDTO);
//        return DataResponse.success(existsMku);
//    }

    @Override
    public DataResponse<Boolean> existsValidMku(IscMkuClientDetailReqDTO input) {
        log.info("existsValidMku, input={}", JSONObject.toJSONString(input));
        MkuClientDetailReqApiDTO reqApiDTO = MkuConvert.INSTANCE.mkuClientReqDTO2ApiDTO(input);
        Boolean existsMku = rpcMkuService.existsValidMku(reqApiDTO);
        return DataResponse.success(existsMku);
    }

//    @Override
//    public DataResponse<IscMkuClientDTO> baseInfo(IscMkuClientDetailReqDTO input) {
//        log.info("baseInfo, input={}", JSONObject.toJSONString(input));
//        MkuClientDetailReqApiDTO reqApiDTO = MkuConvert.INSTANCE.mkuClientReqDTO2ApiDTO(input);
//        MkuClientApiDTO mkuClientApiDTO = rpcMkuService.baseInfo(reqApiDTO);
//        if (null == mkuClientApiDTO) {
//            log.warn("baseInfo, MkuClientApiVO not exists. input={}", JSONObject.toJSONString(input));
//            return DataResponse.error("not exists.");
//        }
//        IscMkuClientDTO iscMkuClientDTO = MkuConvert.INSTANCE.mkuClientApiDTO2DTO(mkuClientApiDTO);
//
//        Map<String, String> symbolMap = currencySymbolService.allSymbols();
//        fillCurrencySymbol(iscMkuClientDTO, symbolMap);
//        iscMkuClientDTO.setMoqText(getMoqText(iscMkuClientDTO.getMoq(), input.getLang()));
//        return DataResponse.success(iscMkuClientDTO);
//    }
//
//    @Override
//    public DataResponse<IscMkuClientGroupDTO> groupInfo(IscMkuClientDetailReqDTO input) {
//        log.info("groupInfo, input={}", JSONObject.toJSONString(input));
//        MkuClientDetailReqApiDTO reqApiDTO = MkuConvert.INSTANCE.mkuClientReqDTO2ApiDTO(input);
//        MkuClientGroupApiDTO mkuClientGroupApiVO = rpcMkuService.groupInfo(reqApiDTO);
//        if (null == mkuClientGroupApiVO) {
//            log.warn("MkuClientApiVO not exists. input={}", JSONObject.toJSONString(input));
//            return DataResponse.error("not exists.");
//        }
//        IscMkuClientGroupDTO iscMkuClientGroupDTO = MkuConvert.INSTANCE.mkuClientGroupApiDTO2DTO(mkuClientGroupApiVO);
//        return DataResponse.success(iscMkuClientGroupDTO);
//    }
//
//    @Override
//    public DataResponse<IscMkuClientCardInfoResDTO> cardInfo(IscMkuClientCardInfoReqDTO input) {
//        log.info("cardInfo, input={}", JSONObject.toJSONString(input));
//        MkuClientCardInfoReqApiDTO reqApiDTO = MkuConvert.INSTANCE.mkuClientCardInfoReqDTO2DTO(input);
//        MkuClientCardInfoResApiDTO mkuClientCardInfoResApiDTO = rpcMkuService.cardInfo(reqApiDTO);
//        if (null == mkuClientCardInfoResApiDTO) {
//            log.warn("MkuClientCardInfoResApiDTO not exists. input={}", JSONObject.toJSONString(input));
//            return DataResponse.error("not exists.");
//        }
//        IscMkuClientCardInfoResDTO iscMkuClientGroupDTO = MkuConvert.INSTANCE.mkuClientCardInfoResDTO2DTO(mkuClientCardInfoResApiDTO);
//        return DataResponse.success(iscMkuClientGroupDTO);
//    }

    @Override
    public DataResponse<List<MkuClientStockSoaDTO>> getStock(MkuClientStockSoaReqDTO req) {
        List<MkuClientStockSoaDTO> fResult = new ArrayList<>();
        MkuClientStockReqApiDTO input = new MkuClientStockReqApiDTO();
        BeanUtils.copyProperties(req, input);
        List<MkuClientStockApiDTO> list = new ArrayList<>();
        for (MkuClientStockSoaDTO stock : req.getStockItem()) {
            MkuClientStockApiDTO target = new MkuClientStockApiDTO();
            BeanUtils.copyProperties(stock, target);
            list.add(target);
        }
        input.setStockItem(list);
        List<MkuClientStockApiDTO> res = rpcMkuService.getStock(input);
        if (CollectionUtils.isNotEmpty(res)) {
            for (MkuClientStockApiDTO result : res) {
                MkuClientStockSoaDTO r = new MkuClientStockSoaDTO();
                BeanUtils.copyProperties(result, r);
                if (r.getNum() == null) {
                    r.setNum(-1);
                }
                fResult.add(r);
            }
        }
        return DataResponse.success(fResult);
    }

//    /**
//     * 填充币种符号
//     *
//     * @param mkuClientDTO
//     * @param symbolMap
//     */
//    private void fillCurrencySymbol(IscMkuClientDTO mkuClientDTO, Map<String, String> symbolMap) {
//        if (MapUtils.isNotEmpty(mkuClientDTO.getCurrenciesPrices())) {
//            // 遍历币种
//            mkuClientDTO.getCurrenciesPrices().entrySet().forEach(o -> {
//                ProductPricesDTO dto = o.getValue();
//                dto.setSymbol(symbolMap.get(o.getKey()));
//            });
//        }
//
//        ProductPricesDTO showCurrency = mkuClientDTO.getShowCurrency();
//        if (null != showCurrency) {
//            showCurrency.setSymbol(symbolMap.get(showCurrency.getCurrency()));
//        }
//    }

    /**
     * 申请补货
     */
    @Override
    public DataResponse<Boolean> replenishment(MkuReplenishmentSoaDTO req) {
        log.info("IscMkuClientApiServiceImpl.replenishment req:{} ", JSON.toJSONString(req));
        CreateParam input = buildInput(req);
        return DataResponse.success(xbpTicketRpcService.create(input));
    }

    @Override
    public DataResponse<Map<Long, IscMkuClientDeliveryAgingDTO>> getDeliveryAging(List<Long> mkuIds, String clientCode) {
        Map<Long, DeliveryAgingResp> deliveryAging = rpcMkuService.getDeliveryAging(mkuIds, clientCode);
        Map<Long, IscMkuClientDeliveryAgingDTO> res = MkuConvert.INSTANCE.deliveryAgingResp2DTO(deliveryAging);
        return DataResponse.success(res);
    }


    private CreateParam buildInput(MkuReplenishmentSoaDTO req) {

        //商品信息
        IscMkuClientDetailReqDTO mkuQuery = new IscMkuClientDetailReqDTO();
        mkuQuery.setClientCode(req.getClientId());
        mkuQuery.setLang(LangConstant.LANG_ZH);
        mkuQuery.setMkuId(req.getMkuId());
        mkuQuery.setPin(req.getPin());
        mkuQuery.setStationType(req.getStationType());
        IscMkuClientDTO mkuClientApiDTO = productManageService.baseInfo(mkuQuery);

        CreateParam target = new CreateParam();
        target.setProcessId(applyId);
        target.setUsername(xbpApplyer);

        /**
         * 如下为提交给xbp系统的信息，不需要国际化
         */
        //基本信息
        Map<String, Object> applicationInfo = new HashMap<>();
        applicationInfo.put("客户名称", req.getPin());
        applicationInfo.put("登录pin", req.getPin());
        applicationInfo.put("申请数量", req.getApplyCnt());
        ExpectDeliveryEnum expectDeliveryEnum = ExpectDeliveryEnum.getEnumByCode(req.getExpectedDeliveryDate());
        applicationInfo.put("期望发货时间", expectDeliveryEnum != null ? expectDeliveryEnum.getDesc() : null);
        applicationInfo.put("联系邮箱", req.getEmail());
        applicationInfo.put("MKU名称", mkuClientApiDTO != null ? mkuClientApiDTO.getMkuName() : null);
        applicationInfo.put("MKUID", req.getMkuId());

        //主图
        Map<String, String> rtf = new HashMap<>();
        rtf.put("商品主图信息", "<img src=\"" + (mkuClientApiDTO != null ? mkuClientApiDTO.getMkuImage() : "") + "\"" + " style=\"max-width: 200px;\">");

        target.setApplicationInfo(applicationInfo);
        target.setRtf(rtf);

        return target;
    }

}
