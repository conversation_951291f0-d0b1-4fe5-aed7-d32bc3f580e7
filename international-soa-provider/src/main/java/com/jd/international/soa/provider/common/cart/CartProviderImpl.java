package com.jd.international.soa.provider.common.cart;

import com.alibaba.fastjson.JSON;
import com.jd.international.soa.base.bean.RPCResult;
import com.jd.international.soa.common.constants.CommonConstant;
import com.jd.international.soa.sdk.common.cart.CartProvider;
import com.jd.international.soa.sdk.common.cart.req.*;
import com.jd.international.soa.sdk.common.cart.resp.CartResp;
import com.jd.international.soa.service.common.cache.JimCacheService;
import com.jd.international.soa.service.common.cart.BatchCartService;
import com.jd.international.soa.service.common.cart.CartService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Component("cartProvider")
@RequiredArgsConstructor
@Slf4j
public class CartProviderImpl implements CartProvider {
    private final CartService cartService;
    private final JimCacheService jimCacheService;
    private final BatchCartService batchCartService;

    @Override
    public RPCResult<Integer> getCartNum(CartReq cartReq) {
        return RPCResult.success(cartService.getCartNum(cartReq));
    }

    @Override
    public RPCResult<CartResp> getCartList(CartReq cartReq) {
        CartResp res = null;
        try {
            res = cartService.getCartList(cartReq);
        }finally {
            log.info("CartProviderImpl.getCartList req:{} , res:{}" , JSON.toJSONString(cartReq), JSON.toJSONString(res));
        }
        return RPCResult.success(res);
    }

    @Override
    public RPCResult<CartResp> addProducts(AddCartReq addCartReq) {
        CartResp res = null;
        //剔除不在池sku，若过滤后为空则提示
        Set<Long> mkuIds = cartService.getMkuAvailable(addCartReq);
        if (CollectionUtils.isEmpty(mkuIds)) {
            return RPCResult.error("添加商品无效,请联系客户经理!");
        }
        //移除不在池的mku
        addCartReq.setWares(addCartReq.getWares().stream().filter(f -> mkuIds.contains(Long.valueOf(f.getSku()))).collect(Collectors.toList()));
        String requestId = UUID.randomUUID().toString();
        Boolean lock = getCartLockWithTry(addCartReq.getCode(), requestId, CommonConstant.ADD_CART_KEY_RETRY_TIMES, CommonConstant.ADD_CART_KEY_RETRY_DURATION);
        if (null == lock || !lock) {
            return RPCResult.success("请求频繁，请重试！");
        }
        try {
            res = cartService.addProducts(addCartReq);
        }finally {
            jimCacheService.simpleLockRelease(addCartReq.getCode(), requestId);
            log.info("CartProviderImpl.addProducts req:{} , res:{}" , JSON.toJSONString(addCartReq) , JSON.toJSONString(res));
        }
        return RPCResult.success(res);
    }

    //获取购物车锁 重试times次数
    private Boolean getCartLockWithTry(String cartKey,String requestId,long times,int retryTime) {
        int i = 1;
        try {
            Boolean lock = jimCacheService.simpleLock(cartKey, requestId, 3);
            while ((null == lock || !lock) && times > i) {
                Thread.sleep(retryTime);
                lock = jimCacheService.simpleLock(cartKey, requestId, 3);
                if (null != lock && lock) {
                    return true;
                }
                i++;
            }
            return lock;
        } catch (Exception e) {
            log.error("CartProviderImpl getCartLockWithTry,cartKey:{},requestId:{},time:{},retryTime:{},error:",cartKey,requestId,times,retryTime,e);
        }finally {
            log.info("CartProviderImpl getCartLockWithTry result,cartKey:{},requestId:{},time:{},retryTime:{},has time:{}",cartKey,requestId,times,retryTime,i);
        }
        return false;
    }

    @Override
    public RPCResult<CartResp> updateProductNum(UpdateCartReq updateCartReq) {
        CartResp res = null;
        try {
            res = cartService.updateProductNum(updateCartReq);
        }finally {
            log.info("CartProviderImpl.updateProductNum req:{} , res:{}" , JSON.toJSONString(updateCartReq) , JSON.toJSONString(res));
        }
        return RPCResult.success(res);
    }

    @Override
    public RPCResult<CartResp> checkProduct(CartReq cartReq) {
        CartResp res = null;
        try {
            res = cartService.checkProduct(cartReq);
        }finally {
            log.info("CartProviderImpl.checkProduct req:{} , res:{}" , JSON.toJSONString(cartReq) , JSON.toJSONString(res));
        }
        return RPCResult.success(res);
    }

    @Override
    public RPCResult<CartResp> delProducts(DelCartReq delCartReq) {
        CartResp res = null;
        try {
            res = cartService.delProducts(delCartReq);
        }finally {
            log.info("CartProviderImpl.delProducts req:{} , res:{}" , JSON.toJSONString(delCartReq) , res);
        }
        return RPCResult.success(res);
    }

    @Override
    public RPCResult<CartResp> replaceCart(AddCartReq addCartReq) {
        CartResp res = null;
        try {
            res = cartService.replaceCart(addCartReq);
        }finally {
            log.info("CartProviderImpl.replaceCart req:{} , res:{}" , JSON.toJSONString(addCartReq) , JSON.toJSONString(res));
        }
        return RPCResult.success(res);
    }

    @Override
    public RPCResult<CartResp> getBatchCartList(BatchCartReq req) {
        CartResp res = null;
        try {
            res = batchCartService.getBatchCartList(req);
        } finally {
            log.info("CartProviderImpl.getBatchCartList req:{}, res:{}" , JSON.toJSONString(req) , JSON.toJSONString(res));
        }
        return RPCResult.success(res);
    }


}
