package com.jd.international.soa.provider.controller;

import com.jd.fastjson.JSON;
import com.jd.international.soa.base.bean.RPCResult;
import com.jd.international.soa.domain.msg.order.IscOrderApproveMsg;
import com.jd.international.soa.sdk.common.mail.MailProvider;
import com.jd.international.soa.sdk.common.mail.req.SendMailReq;
import com.jd.international.soa.service.approval.OrderApproveMsgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2022/10/18 17:05
 */

@RestController
@RequestMapping("/mail")
@Slf4j
public class MailController {

    @Resource
    private MailProvider mailProvider;

    @Autowired
    private OrderApproveMsgService orderApproveMsgService;

    @RequestMapping(value = "/send" , method = RequestMethod.POST)
    RPCResult sendMail(@RequestBody SendMailReq sendMailReq){
        return mailProvider.sendEmail(sendMailReq);
    }

    @RequestMapping(value = "/saveIscOrderApproveMsg", method = RequestMethod.POST)
    public boolean saveIscOrderApproveMsg(@RequestBody IscOrderApproveMsg iscOrderApproveMsg){
        try{
            return orderApproveMsgService.saveIscOrderApproveMsg(iscOrderApproveMsg);
        }catch (Exception e){
            log.error("saveIscOrderApproveMsg, iscOrderApproveMsg = {}", JSON.toJSONString(iscOrderApproveMsg), e);
        }
        return false;
    }

    @RequestMapping(value = "/sendPassOrRejectEmail", method = RequestMethod.POST)
    public boolean sendPassOrRejectEmail(@RequestBody IscOrderApproveMsg iscOrderApproveMsg){
        return orderApproveMsgService.sendPassOrRejectEmail(iscOrderApproveMsg);
    }

    @RequestMapping(value = "/sendEmailJob", method = RequestMethod.POST)
    public boolean sendEmailJob(){
       try {
           orderApproveMsgService.sendEmailJob();
       }catch (Exception e){
           log.error("sendEmailJob异常", e);
           return false;
       }
       return true;
    }

}
