package com.jd.international.soa.provider.order;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.jd.international.soa.base.bean.RPCResult;
import com.jd.international.soa.base.isc.common.IscBaseReqDTO;
import com.jd.international.soa.common.config.InternationalConfig;
import com.jd.international.soa.common.config.UccConfig;
import com.jd.international.soa.common.constants.CommonConstant;
import com.jd.international.soa.common.constants.I18nConstant;
import com.jd.international.soa.common.enums.FileTypeEnum;
import com.jd.international.soa.common.enums.OrderStateEnum;
import com.jd.international.soa.common.utils.IPUtils;
import com.jd.international.soa.common.utils.MoneyUtils;
import com.jd.international.soa.common.utils.S3Utils;
import com.jd.international.soa.domain.enums.PayTypeEnum;
import com.jd.international.soa.domain.enums.StationTypeEnum;
import com.jd.international.soa.domain.order.config.biz.OrderExportConfigVO;
import com.jd.international.soa.domain.order.config.po.OrderExportConfigPO;
import com.jd.international.soa.provider.adapter.OrderDeliveryRespConvert;
import com.jd.international.soa.provider.order.export.OrderPrintCfgDTO;
import com.jd.international.soa.provider.order.export.OrderPrintService;
import com.jd.international.soa.rpc.config.RpcOrderConfigService;
import com.jd.international.soa.rpc.config.RpcSettleConfigService;
import com.jd.international.soa.rpc.customer.RpcCustomerService;
import com.jd.international.soa.rpc.delivery.DeliveryReadRpcService;
import com.jd.international.soa.rpc.order.IscOrderReadRpcService;
import com.jd.international.soa.rpc.product.RpcIscMkuMaterialService;
import com.jd.international.soa.sdk.order.common.OrderExportItemEnum;
import com.jd.international.soa.sdk.order.download.res.Pagination;
import com.jd.international.soa.sdk.order.orderList.OrderQueryProvider;
import com.jd.international.soa.sdk.order.orderList.req.OrderDeliveryReq;
import com.jd.international.soa.sdk.order.orderList.req.OrderExportProviderReq;
import com.jd.international.soa.sdk.order.orderList.req.OrderReq;
import com.jd.international.soa.sdk.order.orderList.res.*;
import com.jd.international.soa.service.authority.AuthorityService;
import com.jd.international.soa.service.order.export.OrderExportConfigService;
import com.jd.international.soa.service.order.orderList.OrderListService;
import com.jd.ka.mro.workflow.soa.sdk.vo.req.ConfigurationFunctionReq;
import com.jd.ka.mro.workflow.soa.sdk.vo.res.ConfigurationFunctionVO;
import com.jd.ka.mro.workflow.soa.sdk.vo.res.OrderConfigInfoVO;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.wisp.api.common.LogisticScopeEnum;
import com.jdi.isc.aggregate.read.wisp.api.customer.res.CustomerReadResp;
import com.jdi.isc.aggregate.read.wisp.api.delivery.req.QueryOrderDeliveryReadReq;
import com.jdi.isc.aggregate.read.wisp.api.delivery.resp.OrderDeliveryReadResp;
import com.jdi.isc.aggregate.read.wisp.api.order.req.OrderAssemblyApiReq;
import com.jdi.isc.aggregate.read.wisp.api.order.req.QueryConsigneeReadReq;
import com.jdi.isc.aggregate.read.wisp.api.order.res.OrderAssemblyApiResp;
import com.jdi.isc.aggregate.read.wisp.api.order.res.OrderAssemblyWaresApiRes;
import com.jdi.isc.aggregate.read.wisp.api.order.resp.OrderConsigneeInfoReadResp;
import com.jdi.isc.library.common.constants.i18n.I18nKeyConstant;
import com.jdi.isc.library.i18n.datasource.cache.I18nCacheManager;
import com.jdi.isc.order.center.api.common.OrderPrintPDFParam;
import com.jdi.isc.order.center.api.constants.OrderValidStatusConstant;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.material.res.MkuMaterialApiDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.beans.PropertyDescriptor;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("orderQueryProvider")
@Slf4j
public class OrderQueryProviderImpl implements OrderQueryProvider {

    @Resource
    private OrderListService orderListReadService;
    @Resource
    private IscOrderReadRpcService iscOrderReadRpcService;
    @Resource
    private RpcIscMkuMaterialService rpcIscMkuMaterialService;

    public final static String UNDER_LINE = "_";

    @Resource
    public S3Utils s3Utils;
    @Resource
    public RpcOrderConfigService rpcOrderConfigService;
    @Resource
    public RpcSettleConfigService rpcSettleConfigService;

    @Resource
    public OrderPrintService orderExportSupportService;

    @Resource
    private OrderPrintService orderPrintService;

    @Resource
    private DeliveryReadRpcService deliveryReadRpcService;
    @Resource
    private UccConfig uccConfig;

    @Resource
    private RpcCustomerService rpcCustomerService;

    @Resource
    private AuthorityService authorityService;
    @Resource
    private OrderExportConfigService orderExportConfigService;
    @Resource
    private InternationalConfig internationalConfig;

    /**
     * 用于管理国际化缓存，提供多语言支持。
     */
    @Resource
    private I18nCacheManager i18nCacheManager;

   /**
    * 定义了订单导出中需要包含的物料信息字段列表，包括物料编码和物料名称。
    */
   private static final List<String> MATERIAL_LIST = Arrays.asList(OrderExportItemEnum.MATERIAL_CODE.getCode(), OrderExportItemEnum.MATERIAL_NAME.getCode());



    @Value("${orderCenter.systemCode}")
    private String systemCode;

    @Override
    public RPCResult<OrderRes> updateServiceMoney(OrderReq orderReq) {
        log.info("OrderQueryProviderImpl.updateServiceMoney req:{} ", JSON.toJSONString(orderReq));
        OrderRes orderRes = orderListReadService.updateServiceMoney(orderReq);
        if (null != orderRes) {
            return RPCResult.success(orderRes);
        }
        return RPCResult.error("更改服务费失败");
    }

    @Override
    public RPCResult<Boolean> updateOrderStatus(OrderReq orderReq) {
        Boolean aBoolean = orderListReadService.updateOrderStatus(orderReq);
        if (aBoolean) {
            return RPCResult.success(aBoolean);
        }
        return RPCResult.error("更新订单状态失败");
    }

    @Override
    public RPCResult<Boolean> batchUpdateOrderStatus(List<OrderReq> orderReq) {
        Boolean aBoolean = orderListReadService.updateOrderStatusBatch(orderReq);
        if (aBoolean) {
            return RPCResult.success(aBoolean);
        }
        return RPCResult.error("更新订单状态失败");
    }

    @Override
    public RPCResult<Pagination<OrderRes>> queryOrderList(OrderReq orderReq) {
//        long start = System.currentTimeMillis();
        Pagination<OrderRes> pagination = orderListReadService.queryOrderList(orderReq);
//        long mid = System.currentTimeMillis();
//        log.info("OrderQueryProviderImpl.queryOrderList 订单列表查询耗时:{} req:{} " , (mid-start),JSON.toJSONString(orderReq));
//        resolveDeliveryInfo(orderReq,pagination);
//        log.info("OrderQueryProviderImpl.queryOrderList 整合配送时间耗时:{} req:{} , res:{}" , (System.currentTimeMillis()-mid),  JSON.toJSONString(orderReq) , JSON.toJSONString(pagination));
        if (null != pagination.getError()) {
            return RPCResult.error("查询订单失败");
        }
        return RPCResult.success(pagination);
    }

    /**
     * 补充订单物流轨迹信息
     */
    private void resolveDeliveryInfo(OrderReq orderReq, Pagination<OrderRes> pagination) {
        if (CollectionUtils.isNotEmpty(pagination.getList())) {
            QueryOrderDeliveryReadReq readReq = new QueryOrderDeliveryReadReq();
            readReq.setLocaleList(Collections.singletonList(orderReq.getLang()));
            readReq.setLogisticScope(LogisticScopeEnum.ALL.getCode());
            for (OrderRes order : pagination.getList()) {
                //如果有子单按子单处理
                if (CollectionUtils.isNotEmpty(order.getOrderResList())) {
                    for (OrderRes child : order.getOrderResList()) {
                        readReq.setOrderId(Long.valueOf(child.getOrderNo()));
                        readReq.setPin(child.getPin());
                        OrderDeliveryReadResp readResp = deliveryReadRpcService.queryOrderDelivery(readReq);
                        OrderDeliveryResp res = OrderDeliveryRespConvert.INSTANCE.toRes(readResp);
                        if (res != null && CollectionUtils.isNotEmpty(res.getLogisticInfoList())) {
                            child.setOrderDeliveryResp(res);
                            child.setHaveDeliveryFlag(true);
                        }
                    }
                    //否则按父单处理
                } else {
                    readReq.setOrderId(Long.valueOf(order.getOrderNo()));
                    readReq.setPin(order.getPin());
                    OrderDeliveryReadResp readResp = deliveryReadRpcService.queryOrderDelivery(readReq);
                    OrderDeliveryResp res = OrderDeliveryRespConvert.INSTANCE.toRes(readResp);
                    if (res != null && CollectionUtils.isNotEmpty(res.getLogisticInfoList())) {
                        order.setOrderDeliveryResp(res);
                        order.setHaveDeliveryFlag(true);
                    }
                }
            }
        }
    }

    @Override
    public RPCResult<Pagination<OrderRes>> queryOrderListByOp(OrderReq orderReq) {
        log.info("OrderQueryProviderImpl.queryOrderListByOp req:{} ", JSON.toJSONString(orderReq));
        Pagination<OrderRes> pagination = orderListReadService.queryOrderListByOp(orderReq);
        if (null != pagination.getError()) {
            return RPCResult.error("查询订单失败");
        }
        return RPCResult.success(pagination);
    }

    @Override
    public RPCResult<HashMap<String, Long>> getOrderStatusList(String pin, String contractNum) {
        HashMap<String, Long> stringLongHashMap = orderListReadService.queryOrderStatusCount(pin, contractNum);
        return RPCResult.success(stringLongHashMap);
    }

    @Override
    public RPCResult<OrderRes> queryByOrderId(String orderId) {
        log.info("OrderQueryProviderImpl.queryByOrderId req:{}", orderId);
        return RPCResult.error("接口废弃-请调用queryByOrderReq接口");
    }

    @Override
    public RPCResult<ArrayList<OrderRes>> queryBatchByOrderId(List<String> orderIds) {
        ArrayList<OrderRes> orderResList = orderListReadService.queryBatchByOrderId(orderIds);
        if (null != orderResList) {
            return RPCResult.success(orderResList);
        }
        return RPCResult.error("查询订单列表失败");
    }

    @Override
    public RPCResult<ArrayList<OrderRes>> queryByPins(List<String> pins) {
        ArrayList<OrderRes> orderRes = orderListReadService.queryByPins(pins);
        if (null != orderRes) {
            return RPCResult.success(orderRes);
        }
        return RPCResult.error("查询订单列表失败");
    }

    @Override
    public RPCResult<Boolean> cancleOrderById(String orderId) {
        OrderReq orderReq = new OrderReq();
        orderReq.setOrderNo(orderId);
        orderReq.setOrderStatus(OrderStateEnum.CANCELED.getCode());
        return RPCResult.success(orderListReadService.updateOrderStatus(orderReq));
    }

    @Override
    public RPCResult<Boolean> confirmOrderById(String orderId) {
        OrderReq orderReq = new OrderReq();
        orderReq.setOrderNo(orderId);
        orderReq.setOrderStatus(OrderStateEnum.COMPLETED.getCode());
        return RPCResult.success(orderListReadService.updateOrderStatus(orderReq));
    }

    @Override
    public RPCResult<OrderRes> queryByOrderReq(OrderReq orderReq) {
        log.info("OrderQueryProviderImpl.queryByOrderReq orderReq={}", JSONObject.toJSONString(orderReq));
        OrderRes orderRes = orderListReadService.queryByOrderReq(orderReq);
        log.info("OrderQueryProviderImpl.queryByOrderReq orderRes={}", JSON.toJSONString(orderRes));
        if (null == orderRes) {
            return RPCResult.error("该订单不存在");
        }
        return RPCResult.success(orderRes);
    }

    /**
     * 订单导出
     */
    @Override
    public RPCResult<OrderExportProviderRes> orderExport(OrderExportProviderReq req) {
        log.info("OrderQueryProviderImpl.orderExport req:{} ", JSON.toJSONString(req));
        String resultFileName = i18nCacheManager.getValueOrDefault(req.getLang(), I18nConstant.ORDER_DOWNLOAD_FILE_NAME)+ UNDER_LINE + System.currentTimeMillis() + ".xlsx";
        OrderExportProviderRes result = new OrderExportProviderRes(req.getPin(), req.getContractNum(), resultFileName, 1);
        OrderAssemblyApiReq rpcReq = new OrderAssemblyApiReq();
        rpcReq.setOrderIdList(new ArrayList<>(req.getOrderId()));
        rpcReq.setIndex(1);
        rpcReq.setSize(100);
        rpcReq.setLang(req.getLang());
        rpcReq.setPins(orderListReadService.resolvePins(req.getPin(), req.getContractNum()));
        rpcReq.setContractNum(req.getContractNum());
        rpcReq.setValidStatus(OrderValidStatusConstant.VALID);
        DataResponse<List<OrderAssemblyApiResp>> rpcRes = iscOrderReadRpcService.list(rpcReq);
        HeadData head = getExcelHead(req);
        if (head == null) {
            return RPCResult.error("Please select at least one configuration item before exporting.");
        }
        List<List<Object>> data = getExcelData(req, rpcRes.getData(), head.getDynamicHead());
        String resUrl = genFile(resultFileName, head.getTotalHead(), data);
        result.setFileUrl(resUrl);
        return RPCResult.success(result);
    }

    // todo 配置多语言改造
    @Override
    public RPCResult<List<OrderExportConfig>> listOrderExportItem(String contractNum) {
        List<OrderExportConfig> result = new ArrayList<>();
        //将枚举转成对象
        List<OrderConfigItem> orderInfo = new ArrayList<>();
        List<OrderConfigItem> customerInfo = new ArrayList<>();
        OrderExportItemEnum[] fixItemArr = OrderExportItemEnum.values();
        for (OrderExportItemEnum orderExportItemEnum : fixItemArr) {
            OrderConfigItem fixItem = new OrderConfigItem();
            fixItem.setName(orderExportItemEnum.getDesc());
            fixItem.setValue(orderExportItemEnum.getField());
            fixItem.setOrder(orderExportItemEnum.getOrder());
            fixItem.setSelected(orderExportItemEnum.getSelected());
            fixItem.setGroup(orderExportItemEnum.getGroup());
            orderInfo.add(fixItem);
        }
        result.add(new OrderExportConfig(OrderConfigItem.ORDER_INFO, orderInfo, 1));
        // 查当前合同下的配置项
        ConfigurationFunctionReq req = new ConfigurationFunctionReq();
        req.setExt4("1");
        req.setContractNum(contractNum);
        req.setModule("订单");
        List<ConfigurationFunctionVO> res = rpcSettleConfigService.queryFunctionListByCondition(req);
        if (CollectionUtils.isNotEmpty(res)) {
            for (ConfigurationFunctionVO config : res) {
                log.info("OrderQueryProviderImpl.listOrderExportItem config:{} ", JSON.toJSONString(config));
                OrderConfigItem fixItem = new OrderConfigItem();
                fixItem.setName(config.getCategory());
                fixItem.setValue(config.getCategory());
                fixItem.setSelected(true);
                fixItem.setGroup("");
                customerInfo.add(fixItem);
            }
            result.add(new OrderExportConfig(OrderConfigItem.CUSTOMER, customerInfo, 2));
        }
        log.info("OrderQueryProviderImpl.listOrderExportItem req:{} , res:{}", contractNum, JSON.toJSONString(result));
        return RPCResult.success(result);
    }

    //处理excel动态表头
    private HeadData getExcelHead(OrderExportProviderReq req) {
        //最终输出的excel头
        List<List<String>> head = new ArrayList<>();
        List<String> dynamicHead = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(req.getOrderExportConfig())) {
            //固定列的表头
            Optional<OrderExportConfig> order = req.getOrderExportConfig().stream().filter(line -> OrderConfigItem.ORDER_INFO_KEY.equals(line.getOrderInfoKey())).findFirst();
            order.ifPresent(orderExportConfig -> orderExportConfig.getOrderInfo().forEach(orderConfig -> {
                if (!orderConfig.getValue().equals(OrderExportItemEnum.MKU_NUMBER.getField()) && Boolean.TRUE.equals(orderConfig.getSelected())) {
                    List<String> column = new ArrayList<>();
                    column.add(orderConfig.getName());
                    head.add(column);
                }
            }));
            Optional<OrderExportConfig> customs = req.getOrderExportConfig().stream().filter(line -> OrderConfigItem.CUSTOMER_KEY.equals(line.getOrderInfoKey())).findFirst();
            if (customs.isPresent()) {
                customs.get().getOrderInfo().sort(Comparator.comparing(OrderConfigItem::getName));
                customs.get().getOrderInfo().forEach(orderConfig -> {
                    if (Boolean.TRUE.equals(orderConfig.getSelected())) {
                        List<String> column = new ArrayList<>();
                        column.add(orderConfig.getValue());
                        head.add(column);
                        dynamicHead.add(orderConfig.getValue());
                    }
                });
            }
        }
        log.info("OrderQueryProviderImpl.getExcelHead req:{} , res:{}", JSON.toJSONString(req), JSON.toJSONString(head));
        if (CollectionUtils.isEmpty(head) && CollectionUtils.isEmpty(dynamicHead)) {
            return null;
        }
        return new HeadData(head, dynamicHead);
    }

    //处理excel输出数据
    private List<List<Object>> getExcelData(OrderExportProviderReq req, List<OrderAssemblyApiResp> rpcData, List<String> dynamicHead) {
        //最终输出的excel数据
        List<List<Object>> excelData = new ArrayList<>();
        if (CollectionUtils.isEmpty(rpcData)) {
            return excelData;
        }
        // 订单号增加子单号
        Set<Long> orderId = Sets.newHashSet();
        for (OrderAssemblyApiResp resp : rpcData) {
            orderId.add(Long.valueOf(resp.getOrderNo()));
            if (CollectionUtils.isNotEmpty(resp.getOrderAssemblyApiRespList())) {
                orderId.addAll(resp.getOrderAssemblyApiRespList().stream().map(apiResp -> Long.valueOf(apiResp.getOrderNo())).collect(Collectors.toSet()));
            }
        }
        QueryConsigneeReadReq queryConsigneeReadReq = new QueryConsigneeReadReq(orderId, 1);
        Map<Long, OrderConsigneeInfoReadResp> consigneeMap = iscOrderReadRpcService.batchQueryOrderConsigneeInfo(queryConsigneeReadReq);
        Map<Long, List<OrderConfigInfoVO>> orderConfigVoMap = rpcOrderConfigService.queryOrderConfigVoByOrderIds(orderId);
        for (OrderAssemblyApiResp rpcOrder : rpcData) {
            if (rpcOrder.getChildOrderNum() == null) {
                this.handleExcelData(req, dynamicHead, rpcOrder, consigneeMap, excelData, orderConfigVoMap);
            } else if (CollectionUtils.isNotEmpty(rpcOrder.getOrderAssemblyApiRespList())) {
                for (OrderAssemblyApiResp childOrder : rpcOrder.getOrderAssemblyApiRespList()) {
                    this.handleExcelData(req, dynamicHead, childOrder, consigneeMap, excelData, orderConfigVoMap);
                }
            }
        }
        return excelData;
    }

    private void handleExcelData(OrderExportProviderReq req, List<String> dynamicHead, OrderAssemblyApiResp rpcOrder, Map<Long, OrderConsigneeInfoReadResp> consigneeMap, List<List<Object>> excelData, Map<Long, List<OrderConfigInfoVO>> orderConfigVoMap) {
        List<OrderAssemblyWaresApiRes> waresReqs = rpcOrder.getWaresReqs();
        // 物料信息从订单快照获取不再实时从商品获取数据  machao  https://joyspace.jd.com/pages/ep0mvqkwWiIrUwjTEar3
        // String clientCode = req.getClientCode();
        // List<Long> mkuIds = waresReqs.stream().map(OrderAssemblyWaresApiRes::getMkuId).collect(Collectors.toList());
        // Map<Long, MkuMaterialApiDTO> mkuMaterialApiDTOs = rpcIscMkuMaterialService.queryMkuMaterial(mkuIds, clientCode);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Long orderLong = Long.valueOf(rpcOrder.getOrderNo());
        //收集收货人信息
        OrderConsigneeInfoReadResp consignee =
                consigneeMap != null ? consigneeMap.getOrDefault(orderLong, new OrderConsigneeInfoReadResp()) : new OrderConsigneeInfoReadResp();
        OrderConsigneeDTO orderConsigneeDTO = this.getOrderConsigneeDTO(req, consignee);
        int mkuSum = waresReqs.stream().mapToInt(ware -> ware.getSkuNum()).sum();
        int mkuKindNum = waresReqs.size();
        for (OrderAssemblyWaresApiRes ware : waresReqs) {
            // 物料信息从订单快照获取不再实时从商品获取数据  machao  https://joyspace.jd.com/pages/ep0mvqkwWiIrUwjTEar3
            // MkuMaterialApiDTO mkuMaterialApiDTO = mkuMaterialApiDTOs.get(ware.getMkuId());
            // String materialCode = mkuMaterialApiDTO != null ? mkuMaterialApiDTO.getMaterialId() : "";

            // step1 .拼装固定列信息
            Object target;
            if (Integer.valueOf(0).equals(req.getStationType())) {
                target = this.getOrderExportLocalExcelDTO(rpcOrder, ware, sdf, orderConsigneeDTO, mkuSum, mkuKindNum, req.getLang(), null);
            } else {
                target = this.getOrderExportExcelDTO(rpcOrder, ware, sdf, orderConsigneeDTO, req.getLang(),null);
            }
            log.info("OrderQueryProviderImpl.getExcelData target:{} orderConfigSize:{}", JSON.toJSONString(target), JSON.toJSONString(req.getOrderExportConfig()));
            List<Object> dataList = Lists.newArrayList();
            dataList.addAll(this.getFixColumn(req, target));
            dataList.addAll(this.getDynamicColumn(dynamicHead, orderConfigVoMap, rpcOrder));
            excelData.add(dataList);
        }
    }

    private OrderConsigneeDTO getOrderConsigneeDTO(OrderExportProviderReq req, OrderConsigneeInfoReadResp consignee) {
        OrderConsigneeDTO orderConsigneeDTO = new OrderConsigneeDTO();
        String address1 = consignee.getCountryMap() != null ? consignee.getCountryMap().get(req.getLang()) : null;
        String address2 = consignee.getProvinceNameMap() != null ? consignee.getProvinceNameMap().get(req.getLang()) : null;
        String address3 = consignee.getCityNameMap() != null ? consignee.getCityNameMap().get(req.getLang()) : null;
        String address4 = consignee.getCountyNameMap() != null ? consignee.getCountyNameMap().get(req.getLang()) : null;
        orderConsigneeDTO.setConsignee(consignee.getConsigneeName());
        orderConsigneeDTO.setPhone(consignee.getConsigneeMobile());
        orderConsigneeDTO.setAddress1(address1);
        orderConsigneeDTO.setAddress2(address2);
        orderConsigneeDTO.setAddress3(address3);
        orderConsigneeDTO.setAddress4(address4);
        orderConsigneeDTO.setDetailAddress(consignee.getConsigneeAddress());
        return orderConsigneeDTO;
    }

    private OrderExportExcelDTO getOrderExportExcelDTO(OrderAssemblyApiResp rpcOrder, OrderAssemblyWaresApiRes ware, SimpleDateFormat sdf, OrderConsigneeDTO orderConsigneeDTO,String lang, String materialCode) {
        OrderExportExcelDTO target = new OrderExportExcelDTO();
        //先拼装所有固定列信息
        target.setParentOrderId(rpcOrder.getParentOrderId());
        target.setOrderId(rpcOrder.getOrderNo());
        target.setCreateTime(sdf.format(rpcOrder.getOrderTime()));//todo 应考虑客户所属站点的时区
        target.setPin(rpcOrder.getPin());
        target.setMkuId(ware.getSku());
        target.setMkuName(ware.getSkuName());
        target.setMkuCount(ware.getSkuNum());
        target.setMkuPrice(ware.getSkuPrice());
        target.setMkuTotalPrice(ware.getSkuPrice().multiply(new BigDecimal(ware.getSkuNum())));
        target.setTotalPrice(rpcOrder.getTotalPrice());
        target.setConsignee(orderConsigneeDTO.getConsignee());
        target.setPhone(orderConsigneeDTO.getPhone());
        target.setAddress1(orderConsigneeDTO.getAddress1());
        target.setAddress2(orderConsigneeDTO.getAddress2());
        target.setAddress3(orderConsigneeDTO.getAddress3());
        target.setAddress4(orderConsigneeDTO.getAddress4());
        target.setDetailAddress(orderConsigneeDTO.getDetailAddress());
        target.setOrderStatus(i18nCacheManager.getValueOrDefault(lang,rpcOrder.getOrderDesc()));
        target.setOrderRemark(rpcOrder.getRemark());
        target.setMaterialCode(ware.getMaterialCode());
        target.setMaterialName(ware.getMaterialName());
        return target;
    }

    private OrderExportLocalExcelDTO getOrderExportLocalExcelDTO(OrderAssemblyApiResp rpcOrder, OrderAssemblyWaresApiRes ware, SimpleDateFormat sdf, OrderConsigneeDTO orderConsigneeDTO, int mkuSum, int mkuKindNum, String lang, String materialCode) {
        OrderExportLocalExcelDTO target = new OrderExportLocalExcelDTO();
//        JSONObject configJson = JSON.parseObject(uccConfig.getOrderConfigLang());
//        JSONObject itemJson = configJson.getJSONObject(StationTypeEnum.LOCALE.getCode().toString());
        //先拼装所有固定列信息
        target.setParentOrderId(rpcOrder.getParentOrderId());
        target.setOrderId(rpcOrder.getOrderNo());
        target.setCreateTime(sdf.format(rpcOrder.getOrderTime()));//todo 应考虑客户所属站点的时区
        target.setPin(rpcOrder.getPin());
        target.setMkuId(ware.getSku());
        target.setMkuName(ware.getSkuName());
        target.setMkuCount(ware.getSkuNum());
        target.setTotalPrice(MoneyUtils.formatMoneyNoScale(rpcOrder.getTotalPrice()));
        target.setConsignee(orderConsigneeDTO.getConsignee());
        target.setPhone(orderConsigneeDTO.getPhone());
        target.setAddress1(orderConsigneeDTO.getAddress1());
        target.setAddress2(orderConsigneeDTO.getAddress2());
        target.setAddress3(orderConsigneeDTO.getAddress3());
        target.setAddress4(orderConsigneeDTO.getAddress4());
        target.setDetailAddress(orderConsigneeDTO.getDetailAddress());
        target.setOrderStatus(i18nCacheManager.getValueOrDefault(lang, rpcOrder.getOrderDesc()));
        target.setOrderRemark(rpcOrder.getRemark());
        target.setPayCurrency(rpcOrder.getCurrency());
        target.setPayType("");
        if (StringUtils.isNotBlank(rpcOrder.getOrderExtInfo())) {
            JSONObject jsonObject = JSONObject.parseObject(rpcOrder.getOrderExtInfo());
            PayTypeEnum payType = PayTypeEnum.getEnumByCode(jsonObject.getString("payType"));
            // 目前payType只有ttr可以这样写，以后有新的增加需要特殊处理，增加对应的中文和翻译才可以
            if (payType != null) {
                String ttr = internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.OrderConfiguration.TTR);
                if (Objects.nonNull(ttr)) target.setPayType(ttr);
            }
        }
        target.setOrderMkuTotalPriceTax(rpcOrder.getTotalPrice());
        target.setContractNum(rpcOrder.getContractNum());
        target.setTradeType(rpcOrder.getTradeTerms());
        target.setAccountPeriod("");
        target.setOrderMkuTotalPrice(MoneyUtils.formatMoneyNoScale(rpcOrder.getWaresTotalPrice()));
        BigDecimal taxes = Objects.nonNull(rpcOrder.getOrderTaxes()) ? rpcOrder.getOrderTaxes() : BigDecimal.ZERO;
        target.setTaxTotalPrice(MoneyUtils.formatMoneyNoScale(taxes));
        target.setMkuExcludingTaxSalePrice(MoneyUtils.formatMoneyNoScale(ware.getSkuPrice()));
        target.setMkuExcludingTaxTotalPrice(MoneyUtils.formatMoneyNoScale(ware.getSkuPrice().multiply(new BigDecimal(ware.getSkuNum()))));
        target.setMkuTaxRate(MoneyUtils.formatMoneyNoScale(Objects.nonNull(ware.getValueAddedTaxRate()) ? ware.getValueAddedTaxRate() : BigDecimal.ZERO));
        BigDecimal includeTaxPrice = Objects.nonNull(ware.getValueAddedTaxes()) ? ware.getSkuPrice().add(ware.getValueAddedTaxes()) : ware.getSkuPrice();
        target.setMkuPrice(MoneyUtils.formatMoneyNoScale(includeTaxPrice));
        target.setMkuTotalPrice(MoneyUtils.formatMoneyNoScale(includeTaxPrice.multiply(new BigDecimal(ware.getSkuNum()))));
        target.setMkuCurrency(ware.getCurrency());
//        JSONObject productNumTextJson = itemJson.getJSONObject(OrderConfigItem.PRODUCT_NUM_TEXT);
        String typeAndQuantity = internationalConfig.queryLanValOrDefault(lang, I18nKeyConstant.OrderConfiguration.PRODUCT_COUNT_AND_TOTAL_QUANTITY);
        target.setMkuTotalNum(String.format(typeAndQuantity, mkuKindNum, mkuSum));
        target.setPurchaseOrderId(rpcOrder.getThirdOrderId());
        target.setSaleUnit(ware.getSaleUnit());
        if (Objects.nonNull(rpcOrder.getCustomsClearance())) {
//            JSONObject customsClearanceJson = itemJson.getJSONObject(rpcOrder.getCustomsClearance() == 1 ? OrderConfigItem.ORDER_CUSTOMS_CLEARANCE : OrderConfigItem.ORDER_NO_CUSTOMS_CLEARANCE);
//            String customsClearanceStr = customsClearanceJson.getString(lang);
            String customsClearanceStr = internationalConfig.queryLanValOrDefault(lang, rpcOrder.getCustomsClearance() == 1 ? I18nKeyConstant.OrderConfiguration.CUSTOMS : I18nKeyConstant.OrderConfiguration.NOT_CUSTOMS);
            target.setCustomsClearance(customsClearanceStr);
        }
        target.setMaterialCode(ware.getMaterialCode());
        target.setMaterialName(ware.getMaterialName());
        return target;
    }


    private List<Object> getFixColumn(OrderExportProviderReq req, Object target) {
        List<Object> excelLine = new ArrayList<>();
        Optional<OrderExportConfig> order = req.getOrderExportConfig().stream().filter(line -> OrderConfigItem.ORDER_INFO_KEY.equals(line.getOrderInfoKey())).findFirst();
        order.ifPresent(orderExportConfig -> orderExportConfig.getOrderInfo().forEach(orderConfig -> {
            //value存放的是字段名,用反射获得成员变量值
            try {
                if (!orderConfig.getValue().equals(OrderExportItemEnum.MKU_NUMBER.getField())) {
                    PropertyDescriptor namePd = new PropertyDescriptor(orderConfig.getValue(), target.getClass());
                    Method nameRead = namePd.getReadMethod();
                    //每执行一次追加一行中的一列数据
                    Object cell = nameRead.invoke(target);
                    excelLine.add(cell);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }));
        return excelLine;
    }

    private List<Object> getDynamicColumn(List<String> dynamicHead, Map<Long, List<OrderConfigInfoVO>> orderConfigVoMap, OrderAssemblyApiResp rpcOrder) {
        List<Object> excelLine = new ArrayList<>();
        Long orderLong = StringUtils.isNotBlank(rpcOrder.getParentOrderId()) && !"0".equals(rpcOrder.getParentOrderId()) ? Long.valueOf(rpcOrder.getParentOrderId()) : Long.valueOf(rpcOrder.getOrderNo());
        if (orderConfigVoMap != null && orderConfigVoMap.get(orderLong) != null) {
            //订单下所有配置项
            List<OrderConfigInfoVO> config = orderConfigVoMap.get(orderLong);
            Map<String, OrderConfigInfoVO> configMap = config.stream().collect(Collectors.toMap(OrderConfigInfoVO::getFieldName, Function.identity()));
            log.info("OrderQueryProviderImpl.getExcelData child 订单 configMap:{} , dynamicHead:{}", JSON.toJSONString(configMap), JSON.toJSONString(dynamicHead));
            dynamicHead.forEach(head -> excelLine.add(configMap.getOrDefault(head, new OrderConfigInfoVO()).getFieldValue()));
        }
        return excelLine;
    }

    private String genFile(String fileName, List<List<String>> head, List<List<Object>> data) {
        String resUrl = "";
        ByteArrayOutputStream targetOutputStream = new ByteArrayOutputStream();
        try (ExcelWriter excelWriter = EasyExcel.write(targetOutputStream).build()) {
            excelWriter.write(data, EasyExcel.writerSheet(0).head(head).build());
            excelWriter.finish();
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(targetOutputStream.toByteArray())) {
                resUrl = s3Utils.upload(inputStream, FileTypeEnum.WISP_EXPORT.getCode(), fileName);
            }
        } catch (Exception e) {
            log.error("OrderQueryProvider.orderExport error,fileName:{} target:{} ", fileName, JSON.toJSONString(data), e);
            throw new RuntimeException(e);
        } finally {
            log.info("OrderQueryProvider.orderExport {}订单导出结果:{}", fileName, resUrl);
        }
        return resUrl;
    }

    /**
     * excel表头数据
     */
    @Data
    @AllArgsConstructor
    public class HeadData {
        /**
         * 全部表头
         */
        private List<List<String>> totalHead;
        /**
         * 动态字段部分表头
         */
        private List<String> dynamicHead;
    }

    @Override
    public RPCResult<HashMap<String, Long>> getOrderStatusListByCountry(OrderReq orderReq) {
        HashMap<String, Long> countMap = orderListReadService.queryOrderStatusCount(orderReq);
        return RPCResult.success(countMap);
    }

    /**
     * 订单打印
     */
    @Override
    public RPCResult<OrderExportProviderRes> orderPrint(OrderExportProviderReq req) {
        log.info("OrderQueryProviderImpl.orderPrint req:{}", JSON.toJSONString(req));
        if (CollectionUtils.isEmpty(req.getOrderId()) || req.getOrderId().size() > 1) {
            return RPCResult.error("This feature only supports printing a single order.");
        }
        if (StationTypeEnum.LOCALE.getCode().equals(req.getStationType())) {
            CustomerReadResp customerReadResp = rpcCustomerService.getCustomerByClientCode(req.getClientCode());
            if (Objects.isNull(customerReadResp) || StringUtils.isBlank(customerReadResp.getCountry())) {
                return RPCResult.error("查询客户信息失败");
            }
            if (CountryConstant.COUNTRY_VN.equals(customerReadResp.getCountry())) {
                String url = getVNOrderPrint(req);
                OrderExportProviderRes result = new OrderExportProviderRes(req.getPin(), req.getContractNum(), "", 2);
                result.setFileUrl(url);
                return RPCResult.success(result);
            }
        }
        OrderPrintCfgDTO printInput = orderExportSupportService.buildOrderPrint(req);
        String url = orderPrintService.getOrderPdf(printInput);
        OrderExportProviderRes result = new OrderExportProviderRes(req.getPin(), req.getContractNum(), printInput.getOssKey(), 2);
        result.setFileUrl(url);
        log.info("OrderQueryProviderImpl.orderPrint req:{} , res:{} ", JSON.toJSONString(req), JSON.toJSONString(result));
        return RPCResult.success(result);
    }


    @Override
    public RPCResult<List<OrderStatusRes>> getOrderStatusResListByCountry(OrderReq orderReq) {
        RPCResult<List<OrderStatusRes>> rpcResult = null;
        try {
            List<OrderStatusRes> list = orderListReadService.queryOrderStatusResList(orderReq);
            rpcResult = RPCResult.success(list);
        } finally {
            log.error("OrderQueryProviderImpl.getOrderStatusResListByCountry, req = {}, result = {}", JSON.toJSONString(orderReq), JSON.toJSONString(rpcResult));
        }
        return rpcResult;
    }


    @Override
    public RPCResult<OrderDeliveryResp> getDeliveryNodes(OrderDeliveryReq deliveryReq) {

        LogisticScopeEnum scopeEnum = LogisticScopeEnum.getByCode(deliveryReq.getLogisticScope());
        Assert.notNull(scopeEnum, "logisticScope参数必填");

        QueryOrderDeliveryReadReq readReq = new QueryOrderDeliveryReadReq();
        readReq.setOrderId(deliveryReq.getOrderId());
        readReq.setPin(deliveryReq.getOrderPin());
        readReq.setThirdOrderId(deliveryReq.getThirdOrderId());
        readReq.setLocaleList(Collections.singletonList(deliveryReq.getLangId()));
        readReq.setLogisticScope(scopeEnum.getCode());
        readReq.setTimeRuleVersion(deliveryReq.getTimeRuleVersion());
        OrderDeliveryReadResp readResp = deliveryReadRpcService.queryOrderDelivery(readReq);
        OrderDeliveryResp resp = OrderDeliveryRespConvert.INSTANCE.toRes(readResp);
        return RPCResult.success(resp);
    }

    private boolean queryClientMaterialRole(String clientCode) {
        try {
            IscBaseReqDTO baseReqDTO = new IscBaseReqDTO();
            baseReqDTO.setClientCode(clientCode);
            return authorityService.haseMaterialAuthority(baseReqDTO);
        } catch (Exception e) {
            log.error("queryClientMaterialRole error,param:{},e:", clientCode, e);
        }
        return false;
    }

    @Override
    public RPCResult saveOrderExportConfig(IscBaseReqDTO iscBaseReqDTO, List<OrderExportConfig> orderExportConfigList) {
        log.info("saveOrderExportConfig, param iscBaseReqDTO={}, orderExportConfigList={}", JSONObject.toJSONString(iscBaseReqDTO), JSONObject.toJSONString(orderExportConfigList));
        OrderExportConfigVO exportConfigVO = new OrderExportConfigVO();
        exportConfigVO.setClientCode(iscBaseReqDTO.getClientCode());
        exportConfigVO.setPin(iscBaseReqDTO.getPin());
        exportConfigVO.setStationType(iscBaseReqDTO.getStationType());
        exportConfigVO.setConfigJson(JSONObject.toJSONString(orderExportConfigList));

        RPCResult rpcResult = orderExportConfigService.saveUpdateConfig(exportConfigVO);
        return rpcResult;
    }

    @Override
    public RPCResult<List<OrderExportConfig>> listOrderExportItemLang(OrderExportProviderReq req) {
        // 站点 -1:通用 0:跨境 1:本土
        Set<Integer> stationSet = Sets.newHashSet(-1, req.getStationType());
        List<OrderExportConfig> result = new ArrayList<>();
        //将枚举转成对象
        List<OrderConfigItem> orderInfo = new ArrayList<>();
        List<OrderConfigItem> customerInfo = new ArrayList<>();
        //查询物料编码权限
        boolean materialRole = queryClientMaterialRole(req.getClientCode());
        OrderExportItemEnum[] fixItemArr = OrderExportItemEnum.values();
        for (OrderExportItemEnum orderExportItemEnum : fixItemArr) {
            if (!stationSet.contains(orderExportItemEnum.getStationType())) {
                continue;
            }
            if (!materialRole && MATERIAL_LIST.contains(orderExportItemEnum.getCode())) {
                continue;
            }
            OrderConfigItem fixItem = new OrderConfigItem();
            String itemName = i18nCacheManager.getValueOrDefault(req.getLang(), orderExportItemEnum.getI18nKey());
            fixItem.setName(StringUtils.isBlank(itemName) ? orderExportItemEnum.getDesc() : itemName);
            fixItem.setValue(orderExportItemEnum.getField());
            fixItem.setOrder(orderExportItemEnum.getOrder());
            fixItem.setSelected(orderExportItemEnum.getSelected());
            fixItem.setGroup(orderExportItemEnum.getGroup());
            orderInfo.add(fixItem);
        }
        String itemName = i18nCacheManager.getValueOrDefault(req.getLang(), I18nKeyConstant.OrderConfiguration.ORDER_INFO);
        result.add(new OrderExportConfig(OrderConfigItem.ORDER_INFO_KEY, itemName, orderInfo, 1));
        // 查当前合同下的配置项
        ConfigurationFunctionReq configReq = new ConfigurationFunctionReq();
        configReq.setExt4("1");
        configReq.setContractNum(req.getContractNum());
        configReq.setModule(CommonConstant.MODULE);
        List<ConfigurationFunctionVO> res = rpcSettleConfigService.queryFunctionListByCondition(configReq);
        if (CollectionUtils.isNotEmpty(res)) {
            for (ConfigurationFunctionVO config : res) {
                log.info("OrderQueryProviderImpl.listOrderExportItem config:{} ", JSON.toJSONString(config));
                OrderConfigItem fixItem = new OrderConfigItem();
                fixItem.setName(config.getCategory());
                fixItem.setValue(config.getCategory());
                fixItem.setSelected(true);
                fixItem.setGroup("");
                customerInfo.add(fixItem);
            }
//            JSONObject customerItemLang = stationConfig.getJSONObject(OrderConfigItem.CUSTOMER_KEY);
//            String customerName = customerItemLang.getString(req.getLang());
            String customerName = internationalConfig.queryLanValOrDefault(req.getLang(), I18nKeyConstant.OrderConfiguration.CUSTOM_FIELD);
            result.add(new OrderExportConfig(OrderConfigItem.CUSTOMER_KEY, customerName, customerInfo, 2));
        }
        log.info("OrderQueryProviderImpl.listOrderExportItemLang req:{} , res:{}", JSON.toJSONString(req), JSON.toJSONString(result));

        try {
            reBuildExportConfigResult(req, result);
        } catch (Exception e) {
            log.error("listOrderExportItemLang, reBuildExportConfigResult Exception.", e);
        }

        return RPCResult.success(result);
    }


    @Override
    public RPCResult<Boolean> confirmReceipt(OrderReq orderReq) {
        Boolean b = orderListReadService.confirmReceipt(orderReq);
        if(b){
            return RPCResult.success(b);
        }
        return RPCResult.error("确认收货失败");
    }


    /**
     * 重新设置选中状态
     *
     * @param req
     * @param exportConfigs
     */
    private void reBuildExportConfigResult(OrderExportProviderReq req, List<OrderExportConfig> exportConfigs) {
        if (CollectionUtils.isEmpty(exportConfigs)) {
            log.info("reBuildExportConfigResult, exportConfigs empty.");
            return;
        }

        OrderExportConfigVO exportConfigVO = new OrderExportConfigVO();
        exportConfigVO.setClientCode(req.getClientCode());
        exportConfigVO.setPin(req.getPin());
        exportConfigVO.setStationType(req.getStationType());
        OrderExportConfigPO configPO = orderExportConfigService.querySingleConfig(exportConfigVO);
        if (null == configPO || StringUtils.isBlank(configPO.getConfigJson())) {
            log.info("reBuildExportConfigResult, OrderExportConfigPO empty.");
            return;
        }
        log.info("reBuildExportConfigResult, configPO = {}", JSONObject.toJSONString(configPO));


        List<OrderExportConfig> dbOrderExportConfigList = JSONArray.parseArray(configPO.getConfigJson(), OrderExportConfig.class);
        if (CollectionUtils.isEmpty(dbOrderExportConfigList)) {
            log.info("reBuildExportConfigResult, orderExportConfigList null.");
            return;
        }
        Map<String, OrderExportConfig> dbOrderExportConfigMap = dbOrderExportConfigList.stream().collect(Collectors.toMap(OrderExportConfig::getOrderInfoKey, o -> o));

        for (OrderExportConfig exportConfig : exportConfigs) {
            List<OrderConfigItem> configItems = exportConfig.getOrderInfo();
            if (CollectionUtils.isEmpty(configItems)) {
                continue;
            }

            Map<String, Boolean> dbItemSeletMap = null;
            OrderExportConfig dbOrderExportConfig = dbOrderExportConfigMap.get(exportConfig.getOrderInfoKey());
            if (null != dbOrderExportConfig && CollectionUtils.isNotEmpty(dbOrderExportConfig.getOrderInfo())) {
                dbItemSeletMap = dbOrderExportConfig.getOrderInfo().stream().collect(Collectors.toMap(OrderConfigItem::getValue, OrderConfigItem::getSelected));
            }

            for (OrderConfigItem configItem : configItems) {
                configItem.setSelected(Boolean.FALSE);

                // 设置为选中状态
                if (null != dbItemSeletMap && Boolean.TRUE.equals(dbItemSeletMap.get(configItem.getValue()))) {
                    configItem.setSelected(Boolean.TRUE);
                }
            }
        }
    }

    private String getVNOrderPrint(OrderExportProviderReq req) {
        String orderId = req.getOrderId().stream().findFirst().orElse(null);
        OrderPrintPDFParam pdfParam = new OrderPrintPDFParam();
        pdfParam.setLocaleList(Collections.singletonList(LangConstant.LANG_VN));
        pdfParam.setSystemCode(systemCode);
        pdfParam.setOrderId(Long.valueOf(orderId));
        pdfParam.setPin(req.getPin());
        pdfParam.setServiceIp(IPUtils.getLocalIp());
        return iscOrderReadRpcService.orderPrint(pdfParam);
    }
}
