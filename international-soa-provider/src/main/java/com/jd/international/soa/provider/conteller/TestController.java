package com.jd.international.soa.provider.conteller;

import com.jd.international.soa.base.bean.RPCResult;
import com.jd.international.soa.sdk.order.currency.CurrencyManagerProvider;
import com.jd.international.soa.sdk.order.currency.enums.CurrencyEnums;
import com.jd.international.soa.sdk.order.currency.req.AddCurrencyManagerReq;
import com.jd.international.soa.sdk.order.currency.req.PageCurrencyReq;
import com.jd.international.soa.sdk.order.currency.res.NewestCurrencyRes;
import com.jd.international.soa.sdk.order.currency.res.PageCurrencyRes;
import com.jd.international.soa.sdk.order.currency.res.PageRes;
import com.jd.international.soa.service.common.cache.JimCacheService;
import com.jd.international.soa.service.common.cart.CartService;
import com.jd.international.soa.service.common.category.CategoryService;
import com.jd.international.soa.service.common.search.SearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/10/9 16:39
 */

@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {

    @Resource
    private CurrencyManagerProvider currencyManagerProvider;
    @Resource
    private JimCacheService jimCacheService;

    @Resource
    private SearchService searchService;

    @Resource
    private CategoryService categoryService;
    @Resource
    private CartService cartService;

    @RequestMapping("/hello")
    public String hello() {
        return "Hello World";
    }

    @RequestMapping("/test2")
    public RPCResult test1(AddCurrencyManagerReq addCurrencyManagerReq) {
        addCurrencyManagerReq.setCurrencyCode(CurrencyEnums.USD);
        return currencyManagerProvider.addCurrency(addCurrencyManagerReq);
    }

    @RequestMapping("/test3")
    public RPCResult<PageRes<PageCurrencyRes>> test2(Integer pageNo , Integer pageSize) {
        PageCurrencyReq pageCurrencyReq = new PageCurrencyReq();
        pageCurrencyReq.setPageNo(pageNo);
        pageCurrencyReq.setPageSize(pageSize);
        pageCurrencyReq.setCurrency(CurrencyEnums.USD);
        return currencyManagerProvider.pageCurrencyByParam(pageCurrencyReq);
    }

    @RequestMapping("/test4")
    public void test3() {
        cartService.deleteAll();
    }

}
