package com.jd.international.soa.provider;

import com.jd.security.tomcat.JDJspServlet;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.*;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.scheduling.annotation.EnableScheduling;

@PropertySources(
		value = {
				@PropertySource(value = {"classpath:important.properties"}, encoding = "utf-8")
		}
)
//, factory = JDSecurityPropertySourceFactory.class
@MapperScan("com.jd.international.soa.dao.**")
//@Import(JDSecurityPropertyCleanService.class)
@ImportResource(locations= {"classpath:spring-config.xml"})
@EnableScheduling
@Slf4j
@SpringBootApplication(scanBasePackages = {"com.jdi.isc.library.i18n","com.jd.international.soa.**"})
public class InternationalSoaApplication {

	public static void main(String[] args) {
		SpringApplication.run(InternationalSoaApplication.class, args);
		log.info("测试环境启动完成，祝测试顺利");
	}
	public static class InnerTomcatCondition implements Condition {
		@Override
		public boolean matches(ConditionContext conditionContext, AnnotatedTypeMetadata annotatedTypeMetadata) {
			return getClass().getClassLoader().equals(org.apache.catalina.startup.Tomcat.class.getClassLoader());
		}
	}



	public static class OuterTomcatCondition implements Condition {
		@Override
		public boolean matches(ConditionContext conditionContext, AnnotatedTypeMetadata annotatedTypeMetadata) {
			return !getClass().getClassLoader().equals(org.apache.catalina.startup.Tomcat.class.getClassLoader());
		}
	}



	/**

	 * 使用外部tomcat启动的情况

	 * <AUTHOR>

	 * @return

	 */

	@Bean
	@Conditional(OuterTomcatCondition.class)
	public ServletRegistrationBean jdJspServletRegistration() {

		JDJspServlet servlet = new com.jd.security.tomcat.JDJspServlet();

		servlet.setSpringBoot(true);

		ServletRegistrationBean registration = new ServletRegistrationBean();

		registration.setServlet(servlet);

		// jsp功能开关 开启jsp功能 请将false修改为true（配置为true安全防护将失效，不建议修改）

		registration.addInitParameter("enableJsp", "false");

		registration.addInitParameter("fork", "false");

		registration.addInitParameter("xpoweredBy", "false");

		registration.addInitParameter("springboot", "true");

		registration.setLoadOnStartup(3);

		registration.addUrlMappings("*.jsp");

		registration.addUrlMappings("*.jspx");

		registration.setName("jsp");

		return registration;

	}
}
