package com.jd.international.soa.provider.controller;

import com.google.common.collect.Sets;
import com.jd.international.soa.base.bean.RPCResult;
import com.jd.international.soa.rpc.product.RpcIJdiGdGmsProductService;
import com.jd.international.soa.sdk.wares.product.ProductQueryProvider;
import com.jd.international.soa.sdk.wares.product.req.SkuBigFieldCriteriaReq;
import com.jd.international.soa.sdk.wares.product.res.ProductBaseInfoRes;
import com.jd.k2.gd.boost.dto.gms.AttributeSettingResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/10/28 15:30
 */
@RestController
@RequestMapping("/product")
@Slf4j
public class ProductQueryController {

    @Resource
    private ProductQueryProvider productQueryProvider;

    @Resource
    private RpcIJdiGdGmsProductService rpcIJdiGdGmsProductService;

    @RequestMapping("/getBigField")
    public RPCResult<Map<String , String>> getBigFieldForSomething(SkuBigFieldCriteriaReq req){
        RPCResult<Map<String, String>> mapRPCResult = productQueryProvider.queryBigFieldById(req);
        return mapRPCResult;
    }

    @RequestMapping("/getProductProperty")
    public RPCResult<Map<Long , Map<String , String>>> getPropertyBySkuId(Long skuId){
        Map<Long, Map<String, String>> longMapMap = rpcIJdiGdGmsProductService.queryProductProperty(Sets.newHashSet(skuId));
        return RPCResult.success(longMapMap);
    }

    @RequestMapping("/getAttribute")
    public RPCResult<Map<Long, AttributeSettingResultVo>> querySetting(Long skuId){
        return RPCResult.success(rpcIJdiGdGmsProductService.queryNewAttributeSettingForSkuId(Sets.newHashSet(skuId)));
    }

    @RequestMapping("/getImagesBySkuId")
    public RPCResult<Map<Long , AttributeSettingResultVo>> queryImagesBySkuIds(Long skuId){
        return RPCResult.success(rpcIJdiGdGmsProductService.queryProductImages(Sets.newHashSet(skuId)));
    }

    @RequestMapping("/getProductBaseInfo")
    public RPCResult<ProductBaseInfoRes> queryProductBaseInfo(Long skuId , String pin){
        return productQueryProvider.queryProductBaseInfo(skuId.toString() ,pin ,1, true);
    }

}
