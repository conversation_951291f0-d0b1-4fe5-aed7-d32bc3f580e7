server.port=1601
spring.profiles.active= @profile.env@
spring.servlet.multipart.maxFileSize=-1
spring.servlet.multipart.maxRequestSize=-1

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
#mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
#mybatis.configLocation=classpath:config/mybatis-config.xml
mybatis.mapper-locations=classpath*:mapper/*Mapper.xml

spring.datasource.base.driver-class-name=com.jd.jdbc.vitess.VitessDriver
spring.datasource.base.url=${vt_driver_jdbc1}
spring.datasource.base.username=${mysql.wr.jdbc.username1}
spring.datasource.base.password=${mysql.wr.jdbc.password1}
spring.datasource.cart.driver-class-name=com.jd.jdbc.vitess.VitessDriver
spring.datasource.cart.url=${vt_driver_jdbc2}
spring.datasource.cart.username=${mysql.wr.jdbc.username2}
spring.datasource.cart.password=${mysql.wr.jdbc.password2}
spring.datasource.product.driver-class-name=com.jd.jdbc.vitess.VitessDriver
spring.datasource.product.url=${vt_driver_jdbc3}
spring.datasource.product.username=${mysql.wr.jdbc.username3}
spring.datasource.product.password=${mysql.wr.jdbc.password3}

spring.data.elasticsearch.restNodes=${jse.restNodes}
spring.data.elasticsearch.username=${jse.username}
spring.data.elasticsearch.password=${jse.password}
spring.data.elasticsearch.connectionTimeOut=5000
spring.data.elasticsearch.socketTimeOut=5000
spring.data.elasticsearch.connectionRequestTimeOut=5000
spring.data.elasticsearch.maxConnectNum=100
spring.data.elasticsearch.maxConnectNumPerRoute=100
es.maxResultWindow=10000

chatgpt.url=http://gpt-proxy.jd.com/v1/chat/completions


laf.config.manager.application=international
laf.config.manager.resources[0].name=international
laf.config.manager.parameters[0].name=autoListener
laf.config.manager.parameters[0].value=true
laf.config.manager.resources[0].uri=ucc://jdos_international-api:<EMAIL>/v1/namespace/international/config/international/profiles/${profiles.ipcprofiles}?longPolling=60000&amp;necessary=true



#jimdb
jim.client.jimUrl=jim://2797026668334517421/22430
jim.client.serviceEndpoint=http://cfs.jim.jd.local/

server.servlet.jsp.class-name=com.jd.security.tomcat.JDJspServlet
server.servlet.jsp.init-parameters.enableJsp=false
server.servlet.jsp.init-parameters.xpoweredBy=false
server.servlet.jsp.init-parameters.springboot=false

spring.jmq.enabled=true
spring.jmq.producers.iscIntlSoaJmq4Producer.password=${isc.intlSoaJmq4Producer.pwd}
spring.jmq.producers.iscIntlSoaJmq4Producer.app=${isc.intlSoaJmq4Producer.app}
spring.jmq.producers.iscIntlSoaJmq4Producer.address=nameserver.jmq.jd.local:80
spring.jmq.producers.iscIntlSoaJmq4Producer.enabled=true

spring.jmq.consumers.iscIntlSoaJmq4Consumer.password=${isc.intlSoaJmq4Consumer.pwd}
spring.jmq.consumers.iscIntlSoaJmq4Consumer.app=${isc.intlSoaJmq4Consumer.app}
spring.jmq.consumers.iscIntlSoaJmq4Consumer.address=nameserver.jmq.jd.local:80
spring.jmq.consumers.iscIntlSoaJmq4Consumer.enabled=true


rpc.tde.token=${tde.token}
rpc.tde.isProd=${tde.isProd}
rpc.tde.enableGM=${tde.enableGM}
rpc.tde.indexSalt=${tde.indexSalt}