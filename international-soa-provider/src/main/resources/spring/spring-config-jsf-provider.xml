<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd">

    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="i.jsf.jd.com"/>
    <jsf:server id="jsfServer" protocol="jsf"/>


    <jsf:provider id="jsf_currencyManagerProvider"
                  interface="com.jd.international.soa.sdk.order.currency.CurrencyManagerProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="currencyManagerProvider" server="jsfServer" delay="-1" serialization="hessian" />

    <jsf:provider id="jsf_addressManagerProvider"
                  interface="com.jd.international.soa.sdk.common.address.AddressManagerProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="addressManagerProvider" server="jsfServer" delay="-1" serialization="hessian" />

    <jsf:provider id="jsf_addressAuthorityManagerProvider"
                  interface="com.jd.international.soa.sdk.common.address.AddressManagerAuthorityProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="addressManagerAuthorityProvider" server="jsfServer" delay="-1" serialization="hessian" />

    <jsf:provider id="jsf_categoryProvider"
                  interface="com.jd.international.soa.sdk.common.category.CategoryProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="categoryProvider" server="jsfServer" delay="-1" serialization="hessian" />

    <jsf:provider id="jsf_accountProvider"
                  interface="com.jd.international.soa.sdk.common.account.AccountProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="accountProvider" server="jsfServer"  delay="-1" serialization="hessian" />

    <jsf:provider id="jsf_mailProvider"
                  interface="com.jd.international.soa.sdk.common.mail.MailProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="mailProvider" server="jsfServer"  delay="-1" serialization="hessian" />

    <jsf:provider id="jsf_cartProvider"
                  interface="com.jd.international.soa.sdk.common.cart.CartProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="cartProvider" server="jsfServer"  delay="-1" serialization="hessian" />

    <jsf:provider id="jsf_productQueryProvider"
                  interface="com.jd.international.soa.sdk.wares.product.ProductQueryProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="productQueryProvider" server="jsfServer" delay="-1" serialization="hessian" />

    <!-- 属性多语言设置 -->
    <jsf:provider id="jsf_skuMultilingualProvider"
                  interface="com.jd.international.soa.sdk.wares.attribute.SkuMultilingualProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="skuMultilingualProvider" server="jsfServer" delay="-1" serialization="hessian" />

    <jsf:provider id="jsf_tradeProvider"
                  interface="com.jd.international.soa.sdk.order.order.TradeProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="tradeProvider" server="jsfServer" delay="-1" serialization="hessian" />

    <!-- 审批文件操作接口 -->
    <jsf:provider id="jsf_approvalFileProvider" interface="com.jd.international.soa.approval.sdk.ApprovalFileProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="approvalFileProvider" server="jsfServer" delay="-1" serialization="hessian" />

    <jsf:provider id="jsf_searchProvider"
                  interface="com.jd.international.soa.sdk.common.search.SearchProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="searchProvider" server="jsfServer" delay="-1" serialization="hessian" />

    <jsf:provider id="jsf_userProvider"
                  interface="com.jd.international.soa.sdk.common.user.UserProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="userProvider" server="jsfServer" delay="-1" serialization="hessian" />
    <jsf:provider id="jsf_orderQueryProvider"
                  interface="com.jd.international.soa.sdk.order.orderList.OrderQueryProvider"
                  alias="${international-soa.common.provider.jsf.alias}" timeout="${jsf.provider.common.slow.timeout}"
                  ref="orderQueryProvider" server="jsfServer" delay="-1"
                  serialization="hessian" />

    <jsf:provider id="jsf_intlDownloadProvider"
                  interface="com.jd.international.soa.sdk.order.download.IntlDownloadProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="intlDownloadProvider" server="jsfServer" delay="-1" serialization="hessian" />
    <jsf:provider id="jsf_brandProvider"
                  interface="com.jd.international.soa.sdk.wares.product.BrandProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="brandProvider" server="jsfServer" delay="-1" serialization="hessian" />
    <jsf:provider id="jsf_priceProvider"
                  interface="com.jd.international.soa.sdk.common.price.PriceProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="priceProvider" server="jsfServer" delay="-1" serialization="hessian" />
    <jsf:provider id="jsf_marketProvider"
                  interface="com.jd.international.soa.sdk.common.market.MarketProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="marketProvider" server="jsfServer" delay="-1" serialization="hessian" />

    <jsf:provider id="productAssemblyOpenApiService"
                  interface="com.jd.international.soa.sdk.wares.product.ProductAssemblyOpenApiService"
                  alias="${jsf.provider.open.product.alias}"
                  ref="productOpenApiServiceImpl" server="jsfServer" delay="-1" serialization="hessian" />

    <jsf:provider id="iscCategoryApiService"
                  interface="com.jd.international.soa.sdk.common.isc.category.IscCategoryApiService"
                  alias="${jsf.provider.isc.category.alias}"
                  ref="iscCategoryApiServiceImpl" server="jsfServer" delay="-1" serialization="hessian" />

    <jsf:provider id="iscMkuClientApiService"
                  interface="com.jd.international.soa.sdk.common.isc.mku.IscMkuClientApiService"
                  alias="${jsf.provider.isc.mkuClient.alias}"
                  ref="iscMkuClientApiServiceImpl" server="jsfServer" delay="-1" serialization="hessian" />

    <jsf:provider id="iscCalculateFreightApiService"
                  interface="com.jd.international.soa.sdk.common.isc.freight.IscCalculateFreightApiService"
                  alias="${jsf.provider.isc.calculateFreight.alias}"
                  ref="iscCalculateFreightApiServiceImpl" server="jsfServer" delay="-1" serialization="hessian" />

    <jsf:provider id="iscAreaApiService"
                  interface="com.jd.international.soa.sdk.common.isc.area.IscAreaApiService"
                  alias="${jsf.provider.isc.area.alias}"
                  ref="iscAreaApiServiceImpl" server="jsfServer" delay="-1" serialization="hessian" />


    <jsf:provider id="configFunctionProvider"
                  interface="com.jd.international.soa.sdk.approval.config.ConfigFunctionProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="configFunctionProviderImpl" server="jsfServer" delay="-1" serialization="hessian" />

    <jsf:provider id="orderConfigInfoProvider"
                  interface="com.jd.international.soa.sdk.approval.config.OrderConfigInfoProvider"
                  alias="${international-soa.common.provider.jsf.alias}"
                  ref="orderConfigInfoProviderImpl" server="jsfServer" delay="-1" serialization="hessian" />

    <jsf:provider id="customerApiService"
                  interface="com.jd.international.soa.sdk.common.customer.CustomerApiService"
                  alias="${jsf.provider.customer.alias}"
                  ref="customerApiServiceImpl" server="jsfServer" delay="-1" serialization="hessian" />

    <!--履约时效服务-->
    <jsf:provider id="promiseSoaApiService"
                  interface="com.jd.international.soa.sdk.wares.promise.PromiseSoaApiService"
                  alias="${jsf.provider.customer.alias}"
                  ref="promiseSoaApiServiceImpl" server="jsfServer" timeout="10000" delay="5000" serialization="hessian" />

    <!-- 功能权限 -->
    <jsf:provider id="authorityApiService"
                  interface="com.jd.international.soa.sdk.common.authority.AuthorityApiService"
                  alias="${jsf.provider.common.alias}"
                  timeout="${jsf.provider.common.timeout}"
                  ref="authorityApiServiceImpl" server="jsfServer" delay="-1" serialization="hessian" />

    <!-- 售后 -->
    <jsf:provider id="afterSalesProvider"
                  interface="com.jd.international.soa.sdk.order.afterSales.AfterSalesProvider"
                  alias="${jsf.provider.common.alias}"
                  timeout="${jsf.provider.common.timeout}"
                  ref="afterSalesProviderImpl" server="jsfServer" delay="-1" serialization="hessian" />

    <!-- punch out 用户登陆记录 -->
    <jsf:provider id="punchOutAccountProvider"
                  interface="com.jd.international.soa.sdk.common.punchout.PunchOutAccountProvider"
                  alias="${jsf.provider.common.alias}"
                  timeout="${jsf.provider.common.timeout}"
                  ref="punchOutAccountProviderImpl" server="jsfServer" delay="-1" serialization="hessian" />

    <!-- 以后WISP查询商品全都从这走 -->
    <jsf:provider id="productReadApiService"
                  interface="com.jd.international.soa.sdk.common.isc.mku.ProductReadApiService"
                  alias="${jsf.provider.common.alias}"
                  timeout="${jsf.provider.common.slow.timeout}"
                  ref="productReadApiServiceImpl" server="jsfServer" delay="-1" serialization="hessian" />
</beans>