<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jd.international.soa</groupId>
        <artifactId>international-soa</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>war</packaging>
    <name>international-soa-provider</name>
    <artifactId>international-soa-provider</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-soa-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-soa-rpc</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-order-sdk</artifactId>
            <version>${international.order.sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-common-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-wares-sdk</artifactId>
            <version>${international.wares.sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-approval-sdk</artifactId>
            <version>${international.approval.sdk.version}</version>
        </dependency>

        <!--主数据-->
        <dependency>
            <groupId>com.jd.biz</groupId>
            <artifactId>jd-biz-user-soa-sdk</artifactId>
            <version>1.6.9-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.jetbrains</groupId>
                    <artifactId>annotations-java5</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--配置文件解密-->
        <dependency>
            <groupId>com.jd.security.configsec</groupId>
            <artifactId>spring-configsec-sdk</artifactId>
            <version>${com.jd.configsec.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.security</groupId>
            <artifactId>jd-security-tomcat</artifactId>
            <version>1.13.WEBAPP</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-jasper</artifactId>
            <version>${tomcat.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.jd.jmq</groupId>
            <artifactId>jmq2-client-springboot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.aggregate.read</groupId>
            <artifactId>jdi-isc-aggregate-read-wisp-api</artifactId>
        </dependency>

        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.itextpdf</groupId>-->
<!--            <artifactId>itext7-core</artifactId>-->
<!--            <type>pom</type>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.jss</groupId>
            <artifactId>jss-sdk-java</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.jd.ump</groupId>
                    <artifactId>profiler</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>joda-time</groupId>
                    <artifactId>joda-time</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>


    </dependencies>

</project>