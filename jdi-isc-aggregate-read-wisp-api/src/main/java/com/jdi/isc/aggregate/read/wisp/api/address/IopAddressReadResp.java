package com.jdi.isc.aggregate.read.wisp.api.address;

import lombok.Data;

/**
 * 内贸段地址信息
 * <AUTHOR>
 * @date 2024/4/26
 **/
@Data
public class IopAddressReadResp {
    private Long id;
    /**
     * 一级地址
     */
    private Long provinceId;
    /**
     * 二级地址
     */
    private Long cityId;
    /**
     * 三级地址
     */
    private Long countyId;
    /**
     * 四级地址
     */
    private Long townId;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 联系方式
     */
    private String contacts;
    /**
     * 邮政编码
     */
    private String zipCode;
    /**
     * 座机
     */
    private String phone;
    /**
     * 移动电话
     */
    private String mobile;
    private String iopPin;
}
