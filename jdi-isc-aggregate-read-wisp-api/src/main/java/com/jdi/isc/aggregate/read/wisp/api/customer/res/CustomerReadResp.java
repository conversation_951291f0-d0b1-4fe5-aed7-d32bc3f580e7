package com.jdi.isc.aggregate.read.wisp.api.customer.res;

import com.jdi.isc.aggregate.read.wisp.api.address.IopAddressReadResp;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 客户信息
 *
 * <AUTHOR>
 * @date 2024/3/14
 **/
@Data
public class CustomerReadResp implements Serializable {

    private static final long serialVersionUID = 8932504954524044736L;
    /**
     * 客户名称
     */
    private String clientName;

    /**
     * 客户编码
     */
    private String clientCode;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 客户pin
     */
    private String pin;

    /**
     * 客户所属站点 CN、US、VN、TH
     */
    private String country;

    /** 拥有的站点 */
    private List<Integer> stations;

    /**
     * 售卖币种
     */
    private String saleCurrency;

    /**
     * wisp页面可见的订单列表状态
     */
    private List<Integer> wispOrderListShowStatus;

    /**
     * 订单配置信息
     */
    private Map<String, String> orderMap;

    /**
     * 开放访问所属地区
     */
    private String openArea;
    /**
     * 客户签约机构编码
     */
    private Integer tradeRoute;
    /**
     * EPT仓编码
     */
    private Long storeId;
    /**
     * 内贸段pin
     */
    private String iopPin;
    /**
     * 售卖ERP
     */
    private String sellerErp;
    /**
     * 账期类型
     */
    private Integer periodType;
    /**
     * 预付款比例，只填整数
     */
    private Integer prepaymentRatio;
    /**
     * 最后付款周期
     */
    private Integer finalPaymentPeriod;
    /**
     * 企业类型：1-EPE(跨境贸易),2-FDI(本土贸易)
     */
    private String companyType;

    /**
     * 客户语种信息
     */
    private Map<String,String> language;

    /**
     * 多语言国家站信息
     */
    private Map<String,String> countryNameMap;

    /**
     * iop地址信息
     */
    private IopAddressReadResp iopAddressReadResp;
}
