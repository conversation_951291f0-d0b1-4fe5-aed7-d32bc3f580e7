#remove .svn folder

target/
.svn
# IntelliJ project files
.idea
*.iml
out
gen

### Java template
*.class

# Mobile Tools for Java (J2ME)
/vsp.iml
.mtj.tmp/

# Package Files #
*.jar
*.war
*.ear

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*


### Eclipse template
*.pydevproject
.metadata
.gradle
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath

# Eclipse Core
.project

# External tool builders
.externalToolBuilders/

# Locally stored "Eclipse launch configurations"
*.launch

# CDT-specific
.cproject

# JDT-specific (Eclipse Java Development Tools)
.classpath

# Java annotation processor (APT)
.factorypath

# PDT-specific
.buildpath

# sbteclipse plugin
.target

# TeXlipse plugin
.texlipse

*.ktr

*.DS_Store

*.log
/.vscode/launch.json
/.vscode/settings.json
/~/


rebel.xml
/.cursor/rules/basic-rule.mdc
/.joycode/prompt.json
