package com.jdi.isc.product.soa.repository.mapper.price;

import com.jdi.isc.product.soa.domain.price.supplierPrice.biz.SkuPriceAuditPageReqVO;
import com.jdi.isc.product.soa.domain.price.supplierPrice.biz.SkuPriceAuditPageVO;
import com.jdi.isc.product.soa.domain.price.supplierPrice.po.SkuPriceDraftPO;
import com.jdi.isc.product.soa.domain.price.supplierPrice.biz.SkuPricePageReqVO;
import com.jdi.isc.product.soa.domain.price.supplierPrice.biz.SkuPricePageVO;
import com.jdi.isc.product.soa.repository.common.BasicMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * sku价格dao
 * <AUTHOR>
 * @date 20231130
 */
@Mapper
public interface SkuPriceDraftBaseMapper extends BasicMapper<SkuPriceDraftPO> {
    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    @Select({"<script> " +
        " select " +
        " price.id, " +
        " price.spu_id, " +
        " price.sku_id, " +
        " price.vendor_code, " +
        " price.source_country_code, " +
        " price.trade_direction, " +
        " price.trade_type, " +
        " price.currency, " +
        " price.price, " +
        " price.tax_price, " +
        " price.auditor, " +
        " price.audit_status, " +
        " price.reject_reason, " +
        " price.country_cost_price, " +
        " price.cost_mark, " +
        " price.agreement_price, " +
        " price.agreement_mark, " +
        " price.cost_currency, " +
        " price.warning_msg, " +
        " price.profit_rate, " +
        " price.value1, " +
        " price.buyer, " +
        " price.remark, " +
        " price.creator, " +
        " price.updater, " +
        " price.create_time, " +
        " price.update_time, " +
        " price.yn " +
        " from jdi_isc_sku_price_draft_sharding price" +
        " <where>" +
        " and price.yn = 1 " +
        " and price.trade_direction = 'SUPPLIER' " +
        " <if test=\"vendorCode != null and vendorCode != ''\">" +
        "   and price.vendor_code = #{vendorCode}" +
        " </if>" +
        " <if test=\"sourceCountryCode != null and sourceCountryCode != ''\">" +
        "   and price.source_country_code = #{sourceCountryCode}" +
        " </if>" +
        " <if test=\"auditStatus != null and auditStatus != ''\">" +
        "   and price.audit_status = #{auditStatus}" +
        " </if>" +
        " <if test=\"spuIds != null and spuIds.size() > 0\">" +
        "   and price.spu_id in " +
        "       <foreach collection=\"spuIds\" open=\"(\" close=\")\" separator=\",\" "+
        "           index=\"index\" item=\"item\">" +
        "           #{item}" +
        "       </foreach>" +
        " </if>" +
        " <if test=\"skuIds != null and skuIds.size() > 0\">" +
        "   and price.sku_id in " +
        "       <foreach collection=\"skuIds\" open=\"(\" close=\")\" separator=\",\" "+
        "           index=\"index\" item=\"item\">" +
        "           #{item}" +
        "       </foreach>" +
        " </if>" +
        " </where>" +
        " order by price.update_time desc, price.id desc" +
        " limit #{offset},#{size}" +
        "</script>"})
    List<SkuPricePageVO> pageSearch(SkuPricePageReqVO input);

    /**
     * 分页查询的列表总数
     * @param input 查询条件
     * @return 总条数
     */
    @Select({"<script> " +
        " select " +
        " count(0) " +
        " from jdi_isc_sku_price_draft_sharding price" +
        " <where>" +
        " and price.yn = 1 " +
        " and price.trade_direction = 'SUPPLIER' " +
        " <if test=\"vendorCode != null and vendorCode != ''\">" +
        "   and price.vendor_code = #{vendorCode}" +
        " </if>" +
        " <if test=\"sourceCountryCode != null and sourceCountryCode != ''\">" +
        "   and price.source_country_code = #{sourceCountryCode}" +
        " </if>" +
        " <if test=\"spuIds != null and spuIds.size() > 0\">" +
        "   and price.spu_id in " +
        "       <foreach collection=\"spuIds\" open=\"(\" close=\")\" separator=\",\" "+
        "           index=\"index\" item=\"item\">" +
        "           #{item}" +
        "       </foreach>" +
        " </if>" +
        " <if test=\"skuIds != null and skuIds.size() > 0\">" +
        "   and price.sku_id in " +
        "       <foreach collection=\"skuIds\" open=\"(\" close=\")\" separator=\",\" "+
        "           index=\"index\" item=\"item\">" +
        "           #{item}" +
        "       </foreach>" +
        " </if>" +
        " </where>" +
        "</script>"})
    long pageSearchTotal(SkuPricePageReqVO input);

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    @Select({"<script> " +
            " select " +
            " price.id, " +
            " price.spu_id, " +
            " price.sku_id, " +
            " price.source_country_code, " +
            " price.trade_direction, " +
            " price.trade_type, " +
            " price.currency, " +
            " price.price, " +
            " price.tax_price, " +
            " price.country_cost_price, " +
            " price.agreement_price, " +
            " price.profit_limit_rate, " +
            " price.low_profit_limit_rate, " +
            " price.auditor, " +
            " price.audit_status, " +
            " price.reject_reason, " +
            " price.warning_msg, " +
            " price.profit_rate, " +
            " price.buyer, " +
            " price.remark, " +
            " price.creator, " +
            " price.updater, " +
            " price.create_time, " +
            " price.update_time, " +
            " price.process_from_data, " +
            " price.attachment_urls, " +
            " price.adjustment_price_reason, " +
            " price.yn " +
            " from jdi_isc_sku_price_draft_sharding price" +
            " <where>" +
            " and price.yn = 1 " +
            " and price.trade_direction = 'SUPPLIER' " +
            " <if test=\"vendorCode != null and vendorCode != ''\">" +
            "   and price.vendor_code = #{vendorCode}" +
            " </if>" +
            " <if test=\"sourceCountryCode != null and sourceCountryCode != ''\">" +
            "   and price.source_country_code = #{sourceCountryCode}" +
            " </if>" +
            " <if test=\"spuIds != null and spuIds.size() > 0\">" +
            "   and price.spu_id in " +
            "       <foreach collection=\"spuIds\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " <if test=\"skuIds != null and skuIds.size() > 0\">" +
            "   and price.sku_id in " +
            "       <foreach collection=\"skuIds\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " <if test=\"ids != null and ids.size() > 0\">" +
            "   and price.id in " +
            "       <foreach collection=\"ids\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " </where>" +
            " order by price.update_time desc,price.id desc " +
            " limit #{offset},#{size}" +
            "</script>"})
    List<SkuPriceAuditPageVO> approvePageSearch(SkuPriceAuditPageReqVO input);

    /**
     * 分页查询的列表总数
     * @param input 查询条件
     * @return 总条数
     */
    @Select({"<script> " +
            " select " +
            " count(0) " +
            " from jdi_isc_sku_price_draft_sharding price" +
            " <where>" +
            " and price.yn = 1 " +
            " and price.trade_direction = 'SUPPLIER' " +
            " <if test=\"vendorCode != null and vendorCode != ''\">" +
            "   and price.vendor_code = #{vendorCode}" +
            " </if>" +
            " <if test=\"sourceCountryCode != null and sourceCountryCode != ''\">" +
            "   and price.source_country_code = #{sourceCountryCode}" +
            " </if>" +
            " <if test=\"spuIds != null and spuIds.size() > 0\">" +
            "   and price.spu_id in " +
            "       <foreach collection=\"spuIds\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " <if test=\"skuIds != null and skuIds.size() > 0\">" +
            "   and price.sku_id in " +
            "       <foreach collection=\"skuIds\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " <if test=\"ids != null and ids.size() > 0\">" +
            "   and price.id in " +
            "       <foreach collection=\"ids\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " </where>" +
            "</script>"})
    long approvePageSearchTotal(SkuPriceAuditPageReqVO input);
}




