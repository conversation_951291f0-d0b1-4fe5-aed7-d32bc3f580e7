package com.jdi.isc.product.soa.repository.mapper.category;


import com.jdi.isc.product.soa.domain.category.biz.CategoryComboBoxReqVO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryComboBoxVO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryPathVO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryQueryPageVO;
import com.jdi.isc.product.soa.domain.category.po.CategoryPO;
import com.jdi.isc.product.soa.repository.common.BasicMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * 类目基础mapper
 * <AUTHOR>
 * @date 20231109
 **/
@Mapper
public interface CategoryBaseMapper extends BasicMapper<CategoryPO> {

    /**
     * 列表查询
     * @param reqVO 搜索条件
     * @return
     */
//    @Select({"<script> " +
//            " select c.id AS catId, c.jd_cat_id" +
//            " from jdi_isc_category c" +
//            " <where>" +
//            " <if test=\"reqVO.jdCatId != null\"> " +
//            "   and c.jd_cat_id = #{reqVO.jdCatId} " +
//            " </if> " +
//            " <if test=\"reqVO.buyer != null\"> " +
//            "   and exists (select 1" +
//            "               from jdi_isc_category_buyer_relation categoryBuyer" +
//            "               where c.id = categoryBuyer.cat_id  and c.yn = 1 and categoryBuyer.yn = 1 " +
//            "                   and categoryBuyer.buyer = #{reqVO.buyer} and categoryBuyer.country_code = #{reqVO.countryCode})" +
//            " </if> " +
//            " <if test=\"true\"> and c.status = 1 </if>" +
//            " <if test=\"true\"> and c.leaf_node_flag = 1 </if>" +
//            " <if test=\"true\"> and c.jd_parent_cat_id is not null </if>" +
//            " <if test=\"true\"> and c.yn = 1 </if>" +
//            " </where>" +
//            " order by c.update_time desc" +
//            " limit #{offset},#{pageSize}" +
//            "</script>"})
    @Select({"<script> " +
            "select c.id AS catId, c.jd_cat_id " +
            "from jdi_isc_category c " +
            "left join jdi_isc_category_buyer_relation categoryBuyer " +
            "  on c.jd_cat_id = categoryBuyer.jd_cat_id " +
            "  and categoryBuyer.yn = 1 " +
            "  <if test=\"reqVO.countryCode != null\"> and categoryBuyer.country_code = #{reqVO.countryCode} </if>" +
            "<where> " +
            "  <if test=\"reqVO.jdCatId != null\"> and c.jd_cat_id = #{reqVO.jdCatId} </if>" +
            "  <if test=\"reqVO.buyer != null and reqVO.buyer != ''\"> and categoryBuyer.buyer = #{reqVO.buyer} </if>" +
            "  and c.status = 1 " +
            "  and c.leaf_node_flag = 1 " +
            "  and c.jd_parent_cat_id is not null " +
            "  and c.yn = 1 " +
            "</where> " +
            "ORDER BY " +
            "CASE WHEN categoryBuyer.update_time IS NOT NULL THEN 1 ELSE 0 END DESC," +
            "COALESCE(categoryBuyer.update_time, c.update_time) DESC " +
            "limit #{offset},#{pageSize} " +
            "</script>"})
    List<CategoryQueryPageVO.Response> listSearch(long offset, long pageSize, CategoryQueryPageVO.Request reqVO);

    /**
     * 列表总数
     * @param reqVO 搜索条件
     * @return
     */
//    @Select({"<script> " +
//            " select count(0) total" +
//            " from jdi_isc_category c" +
//            " <where>" +
//            " <if test=\"jdCatId != null\"> " +
//            "   and c.jd_cat_id = #{jdCatId} " +
//            " </if> " +
//            " <if test=\"buyer != null\"> " +
//            "   and exists (select 1" +
//            "               from jdi_isc_category_buyer_relation categoryBuyer" +
//            "               where c.id = categoryBuyer.cat_id  and c.yn = 1 and categoryBuyer.yn = 1 " +
//            "                   and categoryBuyer.buyer = #{buyer} and categoryBuyer.country_code = #{countryCode})" +
//            " </if> " +
//            " <if test=\"true\"> and c.status = 1 </if>" +
//            " <if test=\"true\"> and c.leaf_node_flag = 1 </if>" +
//            " <if test=\"true\"> and c.jd_parent_cat_id is not null </if>" +
//            " <if test=\"true\"> and c.yn = 1 </if>" +
//            " </where>" +
//            "</script>"})
    @Select({"<script> " +
            " select count(0) total" +
            " from jdi_isc_category c " +
            " left join jdi_isc_category_buyer_relation categoryBuyer " +
            "  on c.jd_cat_id = categoryBuyer.jd_cat_id " +
            "  and categoryBuyer.yn = 1 " +
            "  <if test=\"countryCode != null\"> and categoryBuyer.country_code = #{countryCode} </if>" +
            "<where> " +
            "  <if test=\"jdCatId != null\"> and c.jd_cat_id = #{jdCatId} </if>" +
            "  <if test=\"buyer != null and buyer != ''\"> and categoryBuyer.buyer = #{buyer} </if>" +
            "  and c.status = 1 " +
            "  and c.leaf_node_flag = 1 " +
            "  and c.jd_parent_cat_id is not null " +
            "  and c.yn = 1 " +
            "</where> " +
            "</script>"})
    long listSearchTotal(CategoryQueryPageVO.Request reqVO);

    /**
     * 根据子类目id查询类目路径
     * @param catIds 类目ID集合
     * @return 类目路径
     */
    @Select({"<script>" +
            "  (" +
            "    SELECT " +
            "      c1.jd_cat_id AS id1, c1.cat_level AS level1, c1.status AS status1, c1.leaf_node_flag AS leaf_node_flag1," +
            "      c2.jd_cat_id AS id2, c2.cat_level AS level2, c2.status AS status2, c2.leaf_node_flag AS leaf_node_flag2," +
            "      c3.jd_cat_id AS id3, c3.cat_level AS level3, c3.status AS status3, c3.leaf_node_flag AS leaf_node_flag3," +
            "      c4.jd_cat_id AS id4, c4.cat_level AS level4, c4.status AS status4, c4.leaf_node_flag AS leaf_node_flag4" +
            "    FROM jdi_isc_category c1" +
            "    LEFT JOIN jdi_isc_category c2 ON c1.jd_cat_id = c2.jd_parent_cat_id AND c1.cat_level = 1 " +
            "    LEFT JOIN jdi_isc_category c3 ON c2.jd_cat_id = c3.jd_parent_cat_id AND c2.cat_level = 2 " +
            "    LEFT JOIN jdi_isc_category c4 ON c3.jd_cat_id = c4.jd_parent_cat_id AND c3.cat_level = 3 AND c4.cat_level = 4 " +
            "    WHERE " +
            "       c4.yn = 1 AND c3.yn = 1 AND c2.yn = 1 AND c1.yn = 1 AND c4.leaf_node_flag = 1" +
            "    <if test='catIds != null and catIds.size() > 0'>" +
            "      and c4.jd_cat_id IN " +
            "           <foreach collection=\"catIds\" item=\"id\" open=\"(\" separator=\",\" close=\")\"> " +
            "               #{id} " +
            "           </foreach> " +
            "     </if>" +
            "      <if test=\"status != null\"> " +
            "        AND c4.status = #{status} " +
            "      </if> " +
            "  ) " +
            "  UNION ALL " +
            "  ( " +
            "    SELECT  " +
            "      c1.jd_cat_id AS id1, c1.cat_level AS level1, c1.status AS status1, c1.leaf_node_flag AS leaf_node_flag1," +
            "      c2.jd_cat_id AS id2, c2.cat_level AS level2, c2.status AS status2, c2.leaf_node_flag AS leaf_node_flag2," +
            "      c3.jd_cat_id AS id3, c3.cat_level AS level3, c3.status AS status3, c3.leaf_node_flag AS leaf_node_flag3," +
            "      NULL AS id4, NULL AS level4, NULL AS status4, NULL AS leaf_node_flag4 " +
            "    FROM jdi_isc_category c1 " +
            "    LEFT JOIN jdi_isc_category c2 ON c1.jd_cat_id = c2.jd_parent_cat_id AND c1.cat_level = 1 " +
            "    LEFT JOIN jdi_isc_category c3 ON c2.jd_cat_id = c3.jd_parent_cat_id AND c2.cat_level = 2" +
            "    WHERE " +
            "       c3.yn = 1 AND c2.yn = 1 AND c1.yn = 1 AND c3.leaf_node_flag = 1" +
            "    <if test='catIds != null and catIds.size() > 0'>" +
            "      and c3.jd_cat_id IN " +
            "      <foreach collection=\"catIds\" item=\"id\" open=\"(\" separator=\",\" close=\")\"> " +
            "        #{id} " +
            "      </foreach> " +
            "     </if> " +
            "      <if test=\"status != null\"> " +
            "        AND c3.status = #{status} " +
            "      </if> " +
            "  )" +
            "</script>"})
    List<CategoryPathVO> path(Set<Long> catIds,Integer status);

    /**
     * 查询子类目
     * @param input 参数
     * @return
     */
    @Select({"<script> " +
            " select c.jd_cat_id as id, cl.lang_name as name,c.cat_level as level" +
            " from jdi_isc_category c left join jdi_isc_category_lang cl on c.jd_cat_id = cl.jd_cat_id " +
            " <where>" +
            " c.jd_parent_cat_id = #{id} " +
            " and cl.lang = #{lang} " +
            " and c.yn = 1 and cl.yn = 1 " +
            " <if test=\"status != null\"> " +
            "   and c.status = #{status} " +
            " </if> " +
            " <if test=\"categoryName != null\"> " +
            "   and cl.lang_name like concat('%',#{categoryName},'%') " +
            " </if> " +
            " order by c.sort asc, c.create_time desc " +
            " </where>" +
            "</script>"})
    List<CategoryComboBoxVO> children(CategoryComboBoxReqVO input);

    /**
     * 查某个类目下的4级类目id集合
     * @param catId 类目ID
     * @return 4级类目id
     */
    @Select({"<script> " +
            "  select jd_cat_id " +
            "  from jdi_isc_category " +
            "  where jd_cat_id = #{catId} and (cat_level = 4 or cat_level = 3) and leaf_node_flag =1 and status = 1 " +
            " union all" +
            "  select jd_cat_id " +
            "  from jdi_isc_category " +
            "  where jd_parent_cat_id = #{catId}" +
            "    and (cat_level = 4 or cat_level = 3) and leaf_node_flag =1" +
            " union all" +
            "  select c1.jd_cat_id" +
            "    FROM jdi_isc_category c1" +
            "    JOIN jdi_isc_category c2 ON c2.jd_cat_id = c1.jd_parent_cat_id" +
            "    WHERE c2.jd_parent_cat_id = #{catId} AND (c1.cat_level = 4 or c1.cat_level = 3) and c1.leaf_node_flag =1 and c1.status = 1 " +
            " union all" +
            "  select c1.jd_cat_id" +
            "    FROM jdi_isc_category c1" +
            "    JOIN jdi_isc_category c2 ON c2.jd_cat_id = c1.jd_parent_cat_id" +
            "    JOIN jdi_isc_category c3 ON c3.jd_cat_id = c2.jd_parent_cat_id" +
            "    WHERE c3.jd_parent_cat_id = #{catId} AND (c1.cat_level = 4 or c1.cat_level = 3) and c1.leaf_node_flag =1 and c1.status = 1 " +
            "</script>"})
    List<Long> categoryLevel4From1234(Long catId);

    @Select({
            "<script>",
            "  (",
            "    SELECT ",
            "      c1.jd_cat_id AS id1, c1.cat_level AS level1, c1.status AS status1, c1.leaf_node_flag AS leaf_node_flag1, ",
            "      c2.jd_cat_id AS id2, c2.cat_level AS level2, c2.status AS status2, c2.leaf_node_flag AS leaf_node_flag2, ",
            "      c3.jd_cat_id AS id3, c3.cat_level AS level3, c3.status AS status3, c3.leaf_node_flag AS leaf_node_flag3, ",
            "      c4.jd_cat_id AS id4, c4.cat_level AS level4, c4.status AS status4, c4.leaf_node_flag AS leaf_node_flag4, ",
            "      c4.jd_cat_id AS jdCatId ",
            "    FROM jdi_isc_category c1 ",
            "    LEFT JOIN jdi_isc_category c2 ON c1.jd_cat_id = c2.jd_parent_cat_id AND c1.cat_level = 1 ",
            "    LEFT JOIN jdi_isc_category c3 ON c2.jd_cat_id = c3.jd_parent_cat_id AND c2.cat_level = 2 ",
            "    LEFT JOIN jdi_isc_category c4 ON c3.jd_cat_id = c4.jd_parent_cat_id AND c3.cat_level = 3 ",
            "    WHERE c4.cat_level = 4 AND c4.leaf_node_flag = 1 ",
            "    <if test='catIds != null and catIds.size() > 0'>",
            "      AND c4.jd_cat_id IN ",
            "      <foreach collection='catIds' item='id' open='(' separator=',' close=')'>#{id}</foreach>",
            "    </if>",
            "    <if test='status != null'>",
            "      AND c4.status = #{status} AND c3.status = #{status}",
            "      AND c2.status = #{status} AND c1.status = #{status} ",
            "    </if>",
            "      AND c1.yn = 1 AND c2.yn = 1 AND c3.yn = 1 AND c4.yn = 1",
            "  )",
            "  UNION ALL",
            "  (",
            "    SELECT ",
            "      c1.jd_cat_id AS id1, c1.cat_level AS level1, c1.status AS status1, c1.leaf_node_flag AS leaf_node_flag1, ",
            "      c2.jd_cat_id AS id2, c2.cat_level AS level2, c2.status AS status2, c2.leaf_node_flag AS leaf_node_flag2, ",
            "      c3.jd_cat_id AS id3, c3.cat_level AS level3, c3.status AS status3, c3.leaf_node_flag AS leaf_node_flag3, ",
            "      NULL AS id4, NULL AS level4, NULL AS status4, NULL AS leaf_node_flag4, ",
            "      c3.jd_cat_id AS jdCatId ",
            "    FROM jdi_isc_category c1 ",
            "    LEFT JOIN jdi_isc_category c2 ON c1.jd_cat_id = c2.jd_parent_cat_id AND c1.cat_level = 1 ",
            "    LEFT JOIN jdi_isc_category c3 ON c2.jd_cat_id = c3.jd_parent_cat_id AND c2.cat_level = 2 ",
            "    WHERE c3.cat_level = 3 AND c3.leaf_node_flag = 1 ",
            "    <if test='catIds != null and catIds.size() > 0'>",
            "      AND c3.jd_cat_id IN ",
            "      <foreach collection='catIds' item='id' open='(' separator=',' close=')'>#{id}</foreach>",
            "    </if>",
            "    <if test='status != null'>",
            "      AND c3.status = #{status}",
            "      AND c2.status = #{status} AND c1.status = #{status} ",
            "    </if>",
            "      AND c1.yn = 1 AND c2.yn = 1 AND c3.yn = 1",
            "  )",
            "</script>"
    })
    List<CategoryPathVO> pathByStatus(Set<Long> catIds,Integer status);

    @Select({
            "<script>",
            "SELECT id1,level1,status1,leaf_node_flag1" ,
                    ",id2,level2,status2,leaf_node_flag2" ,
                    ",id3,level3,status3,leaf_node_flag3" ,
                    ",id4,level4,status4,leaf_node_flag4,jdCatId,createTime FROM (",
            "  (",
            "    SELECT ",
            "      c1.jd_cat_id AS id1, c1.cat_level AS level1, c1.status AS status1, c1.leaf_node_flag AS leaf_node_flag1, ",
            "      c2.jd_cat_id AS id2, c2.cat_level AS level2, c2.status AS status2, c2.leaf_node_flag AS leaf_node_flag2, ",
            "      c3.jd_cat_id AS id3, c3.cat_level AS level3, c3.status AS status3, c3.leaf_node_flag AS leaf_node_flag3, ",
            "      c4.jd_cat_id AS id4, c4.cat_level AS level4, c4.status AS status4, c4.leaf_node_flag AS leaf_node_flag4, ",
            "      c4.jd_cat_id AS jdCatId,c4.create_time AS createTime",
            "    FROM jdi_isc_category c1 ",
            "    LEFT JOIN jdi_isc_category c2 ON c1.jd_cat_id = c2.jd_parent_cat_id AND c1.cat_level = 1 ",
            "    LEFT JOIN jdi_isc_category c3 ON c2.jd_cat_id = c3.jd_parent_cat_id AND c2.cat_level = 2 ",
            "    LEFT JOIN jdi_isc_category c4 ON c3.jd_cat_id = c4.jd_parent_cat_id AND c3.cat_level = 3 ",
            "    WHERE c4.cat_level = 4 AND c4.leaf_node_flag = 1",
            "    <if test='status != null'>",
            "      AND c4.status = #{status} AND c3.status = #{status}",
            "      AND c2.status = #{status} AND c1.status = #{status} ",
            "    </if>",
            "      AND c1.yn = 1 AND c2.yn = 1 AND c3.yn = 1 AND c4.yn = 1",
            "  )",
            "  UNION ALL",
            "  (",
            "    SELECT ",
            "      c1.jd_cat_id AS id1, c1.cat_level AS level1, c1.status AS status1, c1.leaf_node_flag AS leaf_node_flag1, ",
            "      c2.jd_cat_id AS id2, c2.cat_level AS level2, c2.status AS status2, c2.leaf_node_flag AS leaf_node_flag2, ",
            "      c3.jd_cat_id AS id3, c3.cat_level AS level3, c3.status AS status3, c3.leaf_node_flag AS leaf_node_flag3, ",
            "      NULL AS id4, NULL AS level4, NULL AS status4, NULL AS leaf_node_flag4, ",
            "      c3.jd_cat_id AS jdCatId ,c3.create_time AS createTime",
            "    FROM jdi_isc_category c1 ",
            "    LEFT JOIN jdi_isc_category c2 ON c1.jd_cat_id = c2.jd_parent_cat_id AND c1.cat_level = 1 ",
            "    LEFT JOIN jdi_isc_category c3 ON c2.jd_cat_id = c3.jd_parent_cat_id AND c2.cat_level = 2 ",
            "    WHERE c3.cat_level = 3 AND c3.leaf_node_flag = 1",
            "    <if test='status != null'>",
            "      AND c3.status = #{status}",
            "      AND c2.status = #{status} AND c1.status = #{status} ",
            "    </if>",
            "      AND c1.yn = 1 AND c2.yn = 1 AND c3.yn = 1",
            "  )",
            ") AS result",
            "ORDER BY createTime DESC",
            "<if test=\"offset != null and size != null\">",
            "  LIMIT #{offset}, #{size}",
            "</if>",
            "</script>"
    })
    List<CategoryPathVO> pathBySizeStatus(Long offset,Long size,Integer status);


    @Select({"<script>" +
        "  (" +
        "    SELECT " +
        "      c1.jd_cat_id AS id1, c1.cat_level AS level1, c1.status AS status1, c1.leaf_node_flag AS leaf_node_flag1," +
        "      c2.jd_cat_id AS id2, c2.cat_level AS level2, c2.status AS status2, c2.leaf_node_flag AS leaf_node_flag2," +
        "      c3.jd_cat_id AS id3, c3.cat_level AS level3, c3.status AS status3, c3.leaf_node_flag AS leaf_node_flag3," +
        "      c4.jd_cat_id AS id4, c4.cat_level AS level4, c4.status AS status4, c4.leaf_node_flag AS leaf_node_flag4" +
        "    FROM jdi_isc_category c1" +
        "    LEFT JOIN jdi_isc_category c2 ON c1.jd_cat_id = c2.jd_parent_cat_id AND c1.cat_level = 1 " +
        "    LEFT JOIN jdi_isc_category c3 ON c2.jd_cat_id = c3.jd_parent_cat_id AND c2.cat_level = 2 " +
        "    LEFT JOIN jdi_isc_category c4 ON c3.jd_cat_id = c4.jd_parent_cat_id AND c3.cat_level = 3 AND c4.cat_level = 4 " +
        "    WHERE " +
        "      c1.yn = 1 AND c2.yn = 1 AND c3.yn = 1 AND c4.yn = 1" +
        "      AND c4.cat_level = 4 AND c4.leaf_node_flag = 1" +
        "    <if test='catIds != null and catIds.size() > 0'>" +
        "      and c4.jd_cat_id IN " +
        "      <foreach collection=\"catIds\" item=\"id\" open=\"(\" separator=\",\" close=\")\"> " +
        "        #{id} " +
        "      </foreach> " +
        "    </if>" +
        "      <if test=\"status != null\"> " +
        "        AND c4.status = #{status} " +
        "      </if> " +
        "      <if test=\"filterErp != null and filterErp.size()>0\">" +
        "        and c1.updater not in " +
        "        <foreach collection=\"filterErp\" item=\"erp\" open=\"(\" close=\")\" separator=\",\"> " +
        "           #{erp} " +
        "        </foreach> " +
        "      </if> " +
        "  ) " +
        "  UNION ALL " +
        "  ( " +
        "    SELECT  " +
        "      c1.jd_cat_id AS id1, c1.cat_level AS level1, c1.status AS status1, c1.leaf_node_flag AS leaf_node_flag1," +
        "      c2.jd_cat_id AS id2, c2.cat_level AS level2, c2.status AS status2, c2.leaf_node_flag AS leaf_node_flag2," +
        "      c3.jd_cat_id AS id3, c3.cat_level AS level3, c3.status AS status3, c3.leaf_node_flag AS leaf_node_flag3," +
        "      NULL AS id4, NULL AS level4, NULL AS status4, NULL AS leaf_node_flag4 " +
        "    FROM jdi_isc_category c1 " +
        "    LEFT JOIN jdi_isc_category c2 ON c1.jd_cat_id = c2.jd_parent_cat_id AND c1.cat_level = 1 " +
        "    LEFT JOIN jdi_isc_category c3 ON c2.jd_cat_id = c3.jd_parent_cat_id AND c2.cat_level = 2" +
        "    WHERE " +
        "      c1.yn = 1 AND c2.yn = 1 AND c3.yn = 1" +
        "      AND c3.cat_level = 3 AND c3.leaf_node_flag = 1" +
        "       <if test='catIds != null and catIds.size() > 0'>" +
        "       and c3.jd_cat_id IN " +
        "       <foreach collection=\"catIds\" item=\"id\" open=\"(\" separator=\",\" close=\")\"> " +
        "           #{id} " +
        "       </foreach> " +
        "       </if>" +
        "      <if test='status != null'> " +
        "        AND c3.status = #{status} " +
        "      </if> " +
        "      <if test='filterErp != null and filterErp.size()>0'>" +
        "        and c1.updater not in " +
        "        <foreach collection=\"filterErp\" item=\"erp\" open=\"(\" close=\")\" separator=\",\"> " +
        "           #{erp} " +
        "        </foreach> " +
        "      </if> " +
        "  )" +
        "</script>"})
    List<CategoryPathVO> pathByStatusFormalCat(Set<Long> catIds,Integer status,List<String> filterErp);


    /**
     * 查某些类目下的4级类目id集合
     * @param catIds 类目ID集合
     * @return 4级类目id
     */
    @Select({"<script> " +
            "  select jd_cat_id " +
            "  from jdi_isc_category " +
            "  where jd_cat_id in " +
            " <foreach collection=\"catIds\" item=\"id\" open=\"(\" close=\")\" separator=\",\"> " +
            "   #{id} " +
            " </foreach> " +
            "  and (cat_level = 4 or cat_level = 3) and leaf_node_flag =1 and status = 1 " +
            " union all" +
            "  select jd_cat_id " +
            "  from jdi_isc_category " +
            "  where jd_parent_cat_id in " +
            " <foreach collection=\"catIds\" item=\"id\" open=\"(\" close=\")\" separator=\",\"> " +
            "   #{id} " +
            " </foreach> " +
            "    and (cat_level = 4 or cat_level = 3) and leaf_node_flag =1 and status = 1 " +
            " union all" +
            "  select c1.jd_cat_id" +
            "    FROM jdi_isc_category c1" +
            "    JOIN jdi_isc_category c2 ON c2.jd_cat_id = c1.jd_parent_cat_id" +
            "    WHERE c2.jd_parent_cat_id in " +
            "        <foreach collection=\"catIds\" item=\"id\" open=\"(\" close=\")\" separator=\",\"> " +
            "             #{id} " +
            "        </foreach> " +
            "      AND (c1.cat_level = 4 or c1.cat_level = 3) and c1.leaf_node_flag =1 and c1.status = 1" +
            " union all" +
            "  select c1.jd_cat_id" +
            "    FROM jdi_isc_category c1" +
            "    JOIN jdi_isc_category c2 ON c2.jd_cat_id = c1.jd_parent_cat_id" +
            "    JOIN jdi_isc_category c3 ON c3.jd_cat_id = c2.jd_parent_cat_id" +
            "    WHERE c3.jd_parent_cat_id in " +
            "          <foreach collection=\"catIds\" item=\"id\" open=\"(\" close=\")\" separator=\",\"> " +
            "             #{id} " +
            "          </foreach> " +
            "        AND (c1.cat_level = 4 or c1.cat_level = 3) and c1.leaf_node_flag =1 and c1.status = 1 " +
            "</script>"})
    List<Long> categoryLevel4From1234Set(Set<Long> catIds);
}
