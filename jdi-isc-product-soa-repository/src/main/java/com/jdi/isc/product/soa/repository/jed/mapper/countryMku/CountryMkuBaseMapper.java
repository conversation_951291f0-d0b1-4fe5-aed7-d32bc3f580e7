package com.jdi.isc.product.soa.repository.jed.mapper.countryMku;

import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuPageVO;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuVO;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import com.jdi.isc.product.soa.repository.common.BasicMapper;

import java.util.List;

/**
 * @Description: 商品国家池表
 * @Author: wangpeng965
 * @Date: 2024/12/02 21:49
 **/
@Mapper
public interface CountryMkuBaseMapper extends BasicMapper<CountryMkuPO> {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    @Select({"<script> " +
            " select" +
            "   countryMku.id, " +
            "   countryMku.remark, " +
            "   countryMku.creator, " +
            "   countryMku.updater, " +
            "   countryMku.create_time, " +
            "   countryMku.update_time, " +
            "   countryMku.yn, " +
            "   countryMku.mku_id, " +
            "   countryMku.source_country_code, " +
            "   countryMku.target_country_code, " +
            "   countryMku.brand_id, " +
            "   countryMku.jd_cat_id as last_cat_id, " +
            "   countryMku.production_cycle, " +
            "   countryMku.apply_type, " +
            "   countryMku.pool_status, " +
            "   countryMku.warn_status, " +
            "   countryMku.warn_reason, " +
            "   countryMku.pool_fail_reason, " +
            "   countryMku.un_sale_reason, " +
            "   countryMku.undetermined_reason, " +
            "   countryMku.black_reason " +
            " from  " +
            " jdi_isc_country_mku_sharding countryMku" +
            " <where>" +
            " <if test=\"mkuId != null\">" +
            "   and countryMku.mku_id = #{mkuId}" +
            " </if>" +
            " <if test=\"brandId != null and brandId !=''\">" +
            "   and countryMku.brand_id = #{brandId}" +
            " </if>" +
            " <if test=\"sourceCountryCode != null and sourceCountryCode != ''\">" +
            "   and countryMku.source_country_code = #{sourceCountryCode}" +
            " </if>" +
            " <if test=\"targetCountryCode != null and targetCountryCode != ''\">" +
            "   and countryMku.target_country_code = #{targetCountryCode}" +
            " </if>" +
            " <if test=\"productionCycle != null\">" +
            "   and countryMku.production_cycle = #{productionCycle}" +
            " </if>" +
            " <if test=\"poolStatus != null\">" +
            "   and countryMku.pool_status = #{poolStatus}" +
            " </if>" +
            " <if test=\"poolStatusSystem != null\">" +
            "   and countryMku.pool_status = #{poolStatusSystem}" +
            " </if>" +
            " <if test=\"warnStatus != null\">" +
            "   and countryMku.warn_status = #{warnStatus}" +
            " </if>" +
            " <if test=\"warnStatusSystem != null\">" +
            "   and countryMku.warn_status = #{warnStatusSystem}" +
            " </if>" +
            " <if test=\"typeKey != null and typeKey == 'customerNotCountry'\">" +
            "   and exists (select 1 " +
            "            from jdi_isc_customer_mku_sharding customerMku" +
            "            where customerMku.target_country_code = #{targetCountryCode} " +
            "               and customerMku.mku_id = countryMku.mku_id " +
            "               and customerMku.bind_status = 'BIND')" +
            " </if>" +
            " <if test=\"catIds != null and catIds.size()>0\">" +
            "   and countryMku.jd_cat_id in " +
            "       <foreach collection=\"catIds\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " <if test=\"mkuIds != null and mkuIds.size()>0\">" +
            "   and countryMku.mku_id in " +
            "       <foreach collection=\"mkuIds\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " <if test=\"blackReasons != null and blackReasons.size()>0\">" +
            "   and  (" +
            "       <foreach collection=\"blackReasons\" separator=\"or\" item=\"item\">" +
            "           countryMku.black_reason like concat('%', #{item}, '%')  " +
            "       </foreach>" +
            " )</if>" +
            " <if test=\"warnReasons != null and warnReasons.size()>0\">" +
            "   and  (" +
            "       <foreach collection=\"warnReasons\" separator=\"or\" item=\"item\">" +
            "           countryMku.warn_reason REGEXP concat ('(^|[^0-9])', #{item}, '([^0-9]|$)') " +
            "       </foreach>" +
            " )</if>" +
            " <if test=\"undeterminedReasons != null and undeterminedReasons.size()>0\">" +
            "   and  (" +
            "       <foreach collection=\"undeterminedReasons\" separator=\"or\" item=\"item\">" +
            "           countryMku.undetermined_reason like concat('%', #{item}, '%')  " +
            "       </foreach>" +
            " )</if>" +
            " and countryMku.yn = 1 " +
            " </where>" +
            " order by countryMku.update_time desc,countryMku.id desc " +
            " limit #{offset},#{size}" +
            "</script>"})
    List<CountryMkuPageVO.Response> pageSearch(CountryMkuPageVO.Request input);

    /**
     * 分页查询的列表总数
     * @param input 查询条件
     * @return 总条数
     */
    @Select({"<script> " +
            " select count(0) " +
            " from  " +
            " jdi_isc_country_mku_sharding countryMku" +
            " <where>" +
            " <if test=\"mkuId != null\">" +
            "   and countryMku.mku_id = #{mkuId}" +
            " </if>" +
            " <if test=\"brandId != null and brandId !=''\">" +
            "   and countryMku.brand_id = #{brandId}" +
            " </if>" +
            " <if test=\"sourceCountryCode != null and sourceCountryCode != ''\">" +
            "   and countryMku.source_country_code = #{sourceCountryCode}" +
            " </if>" +
            " <if test=\"targetCountryCode != null and targetCountryCode != ''\">" +
            "   and countryMku.target_country_code = #{targetCountryCode}" +
            " </if>" +
            " <if test=\"productionCycle != null\">" +
            "   and countryMku.production_cycle = #{productionCycle}" +
            " </if>" +
            " <if test=\"poolStatus != null\">" +
            "   and countryMku.pool_status = #{poolStatus}" +
            " </if>" +
            " <if test=\"poolStatusSystem != null\">" +
            "   and countryMku.pool_status = #{poolStatusSystem}" +
            " </if>" +
            " <if test=\"warnStatus != null\">" +
            "   and countryMku.warn_status = #{warnStatus}" +
            " </if>" +
            " <if test=\"warnStatusSystem != null\">" +
            "   and countryMku.warn_status = #{warnStatusSystem}" +
            " </if>" +
            " <if test=\"typeKey != null and typeKey == 'customerNotCountry'\">" +
            "   and exists (select 1 " +
            "            from jdi_isc_customer_mku_sharding customerMku" +
            "            where customerMku.target_country_code = #{targetCountryCode} " +
            "               and customerMku.mku_id = countryMku.mku_id " +
            "               and customerMku.bind_status = 'BIND')" +
            " </if>" +
            " <if test=\"catIds != null and catIds.size()>0\">" +
            "   and countryMku.jd_cat_id in " +
            "       <foreach collection=\"catIds\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " <if test=\"mkuIds != null and mkuIds.size()>0\">" +
            "   and countryMku.mku_id in " +
            "       <foreach collection=\"mkuIds\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " <if test=\"blackReasons != null and blackReasons.size()>0\">" +
            "   and  (" +
            "       <foreach collection=\"blackReasons\" separator=\"or\" item=\"item\">" +
            "           countryMku.black_reason like concat('%', #{item}, '%')  " +
            "       </foreach>" +
            " )</if>" +
            " <if test=\"warnReasons != null and warnReasons.size()>0\">" +
            "   and  (" +
            "       <foreach collection=\"warnReasons\" separator=\"or\" item=\"item\">" +
            "           countryMku.warn_reason REGEXP concat ('(^|[^0-9])', #{item}, '([^0-9]|$)') " +
            "       </foreach>" +
            " )</if>" +
            " <if test=\"undeterminedReasons != null and undeterminedReasons.size()>0\">" +
            "   and  (" +
            "       <foreach collection=\"undeterminedReasons\" separator=\"or\" item=\"item\">" +
            "           countryMku.undetermined_reason like concat('%', #{item}, '%')  " +
            "       </foreach>" +
            " )</if>" +
            " and countryMku.yn = 1 " +
            " </where>" +
            "</script>"})
    long pageSearchTotal(CountryMkuPageVO.Request input);

    @Select({"<script> " +
        " select" +
        "   countryMku.id, " +
        "   countryMku.remark, " +
        "   countryMku.creator, " +
        "   countryMku.updater, " +
        "   countryMku.create_time, " +
        "   countryMku.update_time, " +
        "   countryMku.yn, " +
        "   countryMku.mku_id, " +
        "   countryMku.source_country_code, " +
        "   countryMku.target_country_code, " +
        "   countryMku.brand_id, " +
        "   countryMku.last_cat_id, " +
        "   countryMku.jd_cat_id, " +
        "   countryMku.production_cycle, " +
        "   countryMku.apply_type, " +
        "   countryMku.pool_status, " +
        "   countryMku.warn_status, " +
        "   countryMku.warn_reason, " +
        "   countryMku.pool_fail_reason, " +
        "   countryMku.un_sale_reason, " +
        "   countryMku.undetermined_reason, " +
        "   countryMku.black_reason " +
        " from  " +
        " jdi_isc_country_mku_sharding countryMku" +
        " <where>" +
        " (target_country_code,mku_id) in " +
        " <foreach collection=\"voList\" item=\"po\" open=\"(\" close=\")\" separator=\",\"> " +
        " (#{po.targetCountryCode} " +
        " ," +
        " #{po.mkuId} ) " +
        " </foreach> " +
        " and countryMku.yn = 1 " +
        " and countryMku.pool_status = 2 " +
        " </where>" +
        "</script>"})
    List<CountryMkuPO> listSearch(List<CountryMkuVO> voList);
}
