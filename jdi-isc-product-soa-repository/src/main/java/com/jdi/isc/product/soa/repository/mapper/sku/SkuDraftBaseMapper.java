package com.jdi.isc.product.soa.repository.mapper.sku;

import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.repository.common.BasicMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: sku草稿表
 * @Author: zhaokun51
 * @Date: 2024/12/18 11:27
 **/
@Mapper
public interface SkuDraftBaseMapper extends BasicMapper<SkuDraftPO> {

    /**
     * 分页查询有group_ext_attribute的SKU草稿ID列表
     *
     * @param offset 偏移量
     * @param pageSize 分页大小
     * @return ID列表
     */
    @Select({"<script>" +
            " select t.id from jdi_isc_sku_draft_sharding t" +
            " where t.group_ext_attribute is not null and t.yn = 1" +
            " order by t.id limit #{offset}, #{pageSize}" +
            "</script>"})
    List<Long> listSkuDraftIdsByGroupExtAttributePage(Long offset, Integer pageSize);

    /**
     * 根据ID列表查询SKU草稿详情
     *
     * @param ids ID列表
     * @return SkuDraftPO列表
     */
    @Select({"<script>" +
            " select spu_id, sku_id, sku_key, jd_sku_id, group_ext_attribute" +
            " from jdi_isc_sku_draft_sharding" +
            " where id in " +
            " <foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "   #{id}" +
            " </foreach>" +
            "</script>"})
    List<SkuDraftPO> listSkuDraftByIds(@Param("ids") List<Long> ids);
}