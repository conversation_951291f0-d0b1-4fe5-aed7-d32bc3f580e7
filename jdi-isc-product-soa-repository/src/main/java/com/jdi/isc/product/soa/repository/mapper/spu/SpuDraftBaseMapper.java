package com.jdi.isc.product.soa.repository.mapper.spu;


import com.jdi.isc.product.soa.domain.spu.biz.SpuQueryReqVO;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.repository.common.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【jdi_isc_spu_draft_sharding(spu草稿表)】的数据库操作Mapper
 * @createDate 2023-11-25 15:39:30
 * @Entity com.jdi.isc.product.centor.domain.SpuDraftPO
 */
public interface SpuDraftBaseMapper extends BasicMapper<SpuDraftPO> {


    /**
     * 根据查询条件搜索SPU列表
     * @param reqVO 包含查询条件的请求对象
     * @return 符合条件的SPU列表
     */
    @Select({"<script>" +
        "SELECT draft.id,draft.spu_id,draft.spu_json_info,draft.pc_description,draft.app_description,draft.vendor_code,draft.audit_status,draft.tax_audit_status,draft.source_country_code,draft.level,draft.cat_id,draft.jd_cat_id,draft.remark,draft.creator,draft.updater,draft.create_time,draft.update_time,draft.yn " +
        " from jdi_isc_spu_draft_sharding draft " +
        " <where>" +
        " <if test=\"spuId != null\">" +
        "   and draft.spu_id = #{spuId}" +
        " </if>" +
        " <if test=\"catFourId != null\">" +
        "   and draft.jd_cat_id = #{catFourId}" +
        " </if>" +
        " <if test=\"vendorCode != null and vendorCode !=''\">" +
        "   and draft.vendor_code = #{vendorCode}" +
        " </if>" +
        " <if test=\"sourceCountryCode != null and sourceCountryCode != ''\">" +
        "   and draft.source_country_code = #{sourceCountryCode}" +
        " </if>" +
        " <if test=\"auditStatus != null\">" +
        "   and draft.audit_status = #{auditStatus}" +
        " </if>" +
        " <if test=\"level != null\">" +
        "   and draft.level = #{level}" +
        " </if>" +
        " <if test=\"brandId != null\">" +
        "   and draft.spu_json_info like concat('%','\"brandId\":',#{brandId},'%')" +
        " </if>" +
        " <if test=\"vendorSpuId != null\">" +
        "   and draft.spu_json_info like concat('%','\"vendorSpuId\":\"',#{vendorSpuId},'%')" +
        " </if>" +
        " <if test=\"spuName != null and spuName != '' \">" +
        "   and (draft.spu_json_info like concat('%','zh\",\"','%','\"spuTitle','%',#{spuName},'%')  " +
        "   or draft.spu_json_info like concat('%','\"spuTitle','%',#{spuName},'%','zh\"','%')" +
        "   or draft.spu_json_info like concat('%','\"spuZhName\":\"','%',#{spuName},'%'))" +
        " </if>" +
        " <if test=\"buyer != null and buyer != '' \">" +
        "   and draft.spu_json_info like concat('%','\"buyer\":\"',#{buyer},'%')" +
        " </if>" +
        " <if test=\"skuId != null\">" +
        "   and exists (select 1" +
        "               from jdi_isc_sku_sharding sku" +
        "               where sku.spu_id = draft.spu_id  and sku.yn = 1 and sku.sku_id = #{skuId})" +
        " </if>" +
        " <if test=\"skuIds != null and skuIds.size()>0\">" +
        "   and exists (select 1" +
        "               from jdi_isc_sku_sharding sku" +
        "               where sku.spu_id = draft.spu_id  and sku.yn = 1 and " +
        "   sku.sku_id in " +
        "   <foreach collection=\"skuIds\" open=\"(\" close=\")\" separator=\",\" "+
        "      index=\"index\" item=\"item\">" +
        "           #{item}" +
        "   </foreach>" +
        ")" +
        "</if>" +
        " <if test=\"taxAuditStatus != null\">" +
        "   and draft.tax_audit_status = #{taxAuditStatus}" +
        " </if>" +
        " <if test=\"spuIds != null and spuIds !=''\">" +
        "   and draft.spu_id in        " +
        "   <foreach collection=\"spuIds\" open=\"(\" close=\")\" separator=\",\" "+
        "       index=\"index\" item=\"item\">" +
        "           #{item}" +
        "   </foreach>" +
        " </if>" +
        " and draft.yn = 1 " +
        " </where>" +
        " order by draft.update_time desc,draft.spu_id desc " +
        " limit #{offset},#{size}" +
        "</script>"})
    List<SpuDraftPO> listSearch(SpuQueryReqVO reqVO);


    /**
     * 获取总数
     * @param reqVO 查询请求参数
     * @return 总数
     */
    @Select({"<script>" +
        "SELECT count(1) " +
        " from jdi_isc_spu_draft_sharding draft " +
        " <where>" +
        " <if test=\"spuId != null\">" +
        "   and draft.spu_id = #{spuId}" +
        " </if>" +
        " <if test=\"catFourId != null\">" +
        "   and draft.jd_cat_id = #{catFourId}" +
        " </if>" +
        " <if test=\"vendorCode != null and vendorCode !=''\">" +
        "   and draft.vendor_code = #{vendorCode}" +
        " </if>" +
        " <if test=\"sourceCountryCode != null and sourceCountryCode != ''\">" +
        "   and draft.source_country_code = #{sourceCountryCode}" +
        " </if>" +
        " <if test=\"auditStatus != null\">" +
        "   and draft.audit_status = #{auditStatus}" +
        " </if>" +
        " <if test=\"level != null\">" +
        "   and draft.level = #{level}" +
        " </if>" +
        " <if test=\"brandId != null\">" +
        "   and draft.spu_json_info like concat('%','\"brandId\":',#{brandId},'%')" +
        " </if>" +
        " <if test=\"vendorSpuId != null\">" +
        "   and draft.spu_json_info like concat('%','\"vendorSpuId\":\"',#{vendorSpuId},'%')" +
        " </if>" +
        " <if test=\"spuName != null and spuName != '' \">" +
        "   and (draft.spu_json_info like concat('%','zh\",\"','%','\"spuTitle','%',#{spuName},'%') " +
            "   or draft.spu_json_info like concat('%','\"spuTitle','%',#{spuName},'%','zh\"','%')" +
            "   or draft.spu_json_info like concat('%','\"spuZhName\":\"','%',#{spuName},'%'))" +
        " </if>" +
        " <if test=\"buyer != null and buyer != '' \">" +
        "   and draft.spu_json_info like concat('%','\"buyer\":\"',#{buyer},'%')" +
        " </if>" +
        " <if test=\"skuId != null\">" +
        "   and exists (select 1" +
        "               from jdi_isc_sku_sharding sku" +
        "               where sku.spu_id = draft.spu_id  and sku.yn = 1 and sku.sku_id = #{skuId})" +
        " </if>" +
        " <if test=\"skuIds != null and skuIds.size()>0\">" +
        "   and exists (select 1" +
        "               from jdi_isc_sku_sharding sku" +
        "               where sku.spu_id = draft.spu_id  and sku.yn = 1 and " +
        "   sku.sku_id in " +
        "   <foreach collection=\"skuIds\" open=\"(\" close=\")\" separator=\",\" "+
        "      index=\"index\" item=\"item\">" +
        "           #{item}" +
        "   </foreach>" +
        ")" +
        "</if>" +
        " <if test=\"taxAuditStatus != null\">" +
        "   and draft.tax_audit_status = #{taxAuditStatus}" +
        " </if>" +
        " <if test=\"spuIds != null and spuIds !=''\">" +
        "   and draft.spu_id in        " +
        "   <foreach collection=\"spuIds\" open=\"(\" close=\")\" separator=\",\" "+
        "       index=\"index\" item=\"item\">" +
        "           #{item}" +
        "   </foreach>" +
        " </if>" +
        " and draft.yn = 1 " +
        " </where>" +
        "</script>"})
    long getTotal(SpuQueryReqVO reqVO);

    /**
     * 分页获取全部SpuDraftPO数据（仅获取有效数据）
     * 
     * @param offset 偏移量
     * @param size 每页大小
     * @return SpuDraftPO列表
     */
    @Select({"<script>" +
            " select draft.id,draft.spu_id,draft.spu_json_info,draft.pc_description,draft.app_description," +
            "        draft.spu_inter_property,draft.spu_certificate,draft.vendor_code,draft.audit_status," +
            "        draft.tax_audit_status,draft.source_country_code,draft.level,draft.cat_id,draft.jd_cat_id," +
            "        draft.ext_property,draft.group_ext_attribute,draft.creator,draft.updater,draft.create_time,draft.update_time,draft.yn" +
            " from jdi_isc_spu_draft_sharding draft" +
            " where draft.yn = 1 and draft.ext_property!='' and draft.ext_property is not null and draft.update_time>=#{updateTime}" +
            " order by draft.spu_id asc" +
            " limit #{offset},#{size}" +
            "</script>"})
    List<SpuDraftPO> listAllSpuDraftWithPage(String updateTime, Long offset, Long size);

    /**
     * 获取全部有效SpuDraftPO数据的总数
     * 
     * @return 总记录数
     */
    @Select({"<script>" +
            " select count(1)" +
            " from jdi_isc_spu_draft_sharding draft" +
            " where draft.yn = 1 and draft.ext_property!='' and draft.ext_property is not null and draft.update_time>=#{updateTime}" +
            "</script>"})
    long listAllSpuDraftTotal(String updateTime);

    /**
     * 分页查询有group_ext_attribute的SPU草稿ID列表
     *
     * @param offset 偏移量
     * @param pageSize 分页大小
     * @return ID列表
     */
    @Select({"<script>" +
            " select t.id from jdi_isc_spu_draft_sharding t" +
            " where t.group_ext_attribute is not null and t.yn = 1" +
            " order by t.id limit #{offset}, #{pageSize}" +
            "</script>"})
    List<Long> listSpuDraftIdsByGroupExtAttributePage(Long offset, Integer pageSize);

    /**
     * 根据ID列表查询SPU草稿详情
     *
     * @param ids ID列表
     * @return SpuDraftPO列表
     */
    @Select({"<script>" +
            " select d.spu_id, d.group_ext_attribute" +
            " from jdi_isc_spu_draft_sharding d" +
            " where d.id in " +
            " <foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "   #{id}" +
            " </foreach>" +
            "</script>"})
    List<SpuDraftPO> listSpuDraftByIds(@Param("ids") List<Long> ids);

}




