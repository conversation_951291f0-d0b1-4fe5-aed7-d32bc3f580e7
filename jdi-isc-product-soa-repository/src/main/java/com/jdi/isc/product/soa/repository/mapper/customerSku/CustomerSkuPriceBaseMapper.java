package com.jdi.isc.product.soa.repository.mapper.customerSku;

import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceDraftReqVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceVO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPricePO;
import com.jdi.isc.product.soa.repository.common.BasicMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * 客户SKU价格mapper
 * <AUTHOR>
 * @date 20231204
 **/
@Mapper
public interface CustomerSkuPriceBaseMapper extends BasicMapper<CustomerSkuPricePO> {

    /**
     * 获取总数
     * @param reqVO 查询请求参数
     * @return 总数
     */
    @Select({"<script>" +
            "SELECT count(1) " +
            " FROM jdi_isc_customer_sku_price_detail_sharding detailPrice " +
            "   LEFT JOIN jdi_isc_customer_sku_price_sharding price " +
            "   ON detailPrice.sku_id = price.sku_id AND detailPrice.biz_id = price.id" +
            " <where>" +
            " <if test=\"skuId != null\">" +
            "   and detailPrice.sku_id = #{skuId}" +
            " </if>" +
            " <if test=\"countryMkuPoolStatus != null\">" +
            "   and detailPrice.country_mku_pool_status = #{countryMkuPoolStatus}" +
            " </if>" +
            " <if test=\"customerMkuPoolStatus != null and customerMkuPoolStatus != ''\">" +
            "   and detailPrice.customer_mku_pool_status = #{customerMkuPoolStatus}" +
            " </if>" +
            " <if test=\"vendorCode != null and vendorCode !=''\">" +
            "   and price.vendor_code = #{vendorCode}" +
            " </if>" +
            " <if test=\"clientCode != null and clientCode != ''\">" +
            "   and detailPrice.client_code = #{clientCode}" +
            " </if>" +
            " <if test=\"updater != null\">" +
            "   and detailPrice.updater like concat('%',#{updater},'%')" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 3 \">" +
            "   and detailPrice.enable_status = 1 " +
            "   and detailPrice.begin_time &gt; #{nowTime}" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 2 \">" +
            "   and detailPrice.enable_status = 1 " +
            "   and detailPrice.end_time &lt; #{nowTime}" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 1 \">" +
            "   and detailPrice.enable_status = 1 " +
            "   and detailPrice.end_time &gt;= #{nowTime}" +
            "   and detailPrice.begin_time &lt;= #{nowTime}" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 0 \">" +
            "   and detailPrice.enable_status = 0" +
            " </if>" +
            " <if test=\"skuIds != null and skuIds.size() > 0\">" +
            "   and detailPrice.sku_id in " +
            "       <foreach collection=\"skuIds\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " and price.yn = 1 " +
            " and detailPrice.yn = 1 " +
            " </where>" +
            "</script>"})
    long getTotal(CustomerSkuPriceDraftReqVO reqVO);


    /**
     * 根据查询条件搜索客户定制价格
     * @param reqVO 包含查询条件的请求对象
     * @return 符合条件的SPU列表
     */
    @Select({"<script>" +
            "SELECT " +
            "   detailPrice.id, " +
            "   detailPrice.sku_id, " +
            "   detailPrice.client_code, " +
            "   price.client_name, " +
            "   price.vendor_code, " +
            "   detailPrice.customer_trade_type, " +
            "   detailPrice.currency, " +
            "   detailPrice.customer_sale_price, " +
            "   detailPrice.remark, " +
            "   detailPrice.creator, " +
            "   detailPrice.updater, " +
            "   detailPrice.create_time, " +
            "   detailPrice.update_time, " +
            "   detailPrice.enable_status, " +
            "   detailPrice.end_time, " +
            "   detailPrice.begin_time, "+
            "   detailPrice.country_mku_pool_status, "+
            "   detailPrice.customer_mku_pool_status, "+
            "   detailPrice.unsellable_threshold, "+
            "   detailPrice.available_sale_status, "+
            "   detailPrice.unsellable_threshold_time, "+
            "   detailPrice.available_sale_status_time, "+
            "    CASE" +
            "        WHEN #{nowTime} BETWEEN detailPrice.begin_time AND detailPrice.end_time THEN 1 " +
            "        WHEN #{nowTime} &lt; detailPrice.begin_time THEN 2" +
            "        WHEN #{nowTime} &gt; detailPrice.end_time THEN 3 " +
            "        ELSE 4" +
            "    END sort_enable_status," +
            "   detailPrice.yn " +
            " FROM jdi_isc_customer_sku_price_detail_sharding detailPrice " +
            "   LEFT JOIN jdi_isc_customer_sku_price_sharding price " +
            "   ON detailPrice.sku_id = price.sku_id AND detailPrice.biz_id = price.id" +
            " <where>" +
            " <if test=\"skuId != null\">" +
            "   and detailPrice.sku_id = #{skuId}" +
            " </if>" +
            " <if test=\"countryMkuPoolStatus != null\">" +
            "   and detailPrice.country_mku_pool_status = #{countryMkuPoolStatus}" +
            " </if>" +
            " <if test=\"customerMkuPoolStatus != null and customerMkuPoolStatus != ''\">" +
            "   and detailPrice.customer_mku_pool_status = #{customerMkuPoolStatus}" +
            " </if>" +
            " <if test=\"vendorCode != null and vendorCode !=''\">" +
            "   and price.vendor_code = #{vendorCode}" +
            " </if>" +
            " <if test=\"clientCode != null and clientCode != ''\">" +
            "   and detailPrice.client_code = #{clientCode}" +
            " </if>" +
            " <if test=\"updater != null\">" +
            "   and detailPrice.updater like concat('%',#{updater},'%')" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 3 \">" +
            "   and detailPrice.enable_status = 1 " +
            "   and detailPrice.begin_time &gt; #{nowTime}" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 2 \">" +
            "   and detailPrice.enable_status = 1 " +
            "   and detailPrice.end_time &lt; #{nowTime}" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 1 \">" +
            "   and detailPrice.enable_status = 1 " +
            "   and detailPrice.end_time &gt;= #{nowTime}" +
            "   and detailPrice.begin_time &lt;= #{nowTime}" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 0 \">" +
            "   and detailPrice.enable_status = 0" +
            " </if>" +
            " <if test=\"skuIds != null and skuIds.size() > 0\">" +
            "   and detailPrice.sku_id in " +
            "       <foreach collection=\"skuIds\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " and price.yn = 1 " +
            " and detailPrice.yn = 1 " +
            " </where>" +
            " ORDER BY" +
            "    sort_enable_status ASC," +
            "    detailPrice.create_time DESC" +
            " limit #{offset},#{size}" +
            "</script>"})
    List<CustomerSkuPriceVO> listSearch(CustomerSkuPriceDraftReqVO reqVO);
}
