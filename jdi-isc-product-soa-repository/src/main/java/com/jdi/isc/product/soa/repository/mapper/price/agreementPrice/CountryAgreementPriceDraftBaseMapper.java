package com.jdi.isc.product.soa.repository.mapper.price.agreementPrice;

import com.jdi.isc.product.soa.domain.price.agreementPrice.draft.CountryAgreementPriceAuditReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.draft.CountryAgreementPriceAuditResVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.draft.CountryAgreementPriceDraftPageReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.draft.CountryAgreementPriceDraftPageVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPriceDraftPO;
import com.jdi.isc.product.soa.repository.common.BasicMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 国家协议价
 * @Author: wangpeng965
 * @Date: 2025/03/03 16:45
 **/
@Mapper
public interface CountryAgreementPriceDraftBaseMapper extends BasicMapper<CountryAgreementPriceDraftPO> {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    @Select({"<script> " +
        " select " +
        " price.id, " +
        " price.biz_no, " +
        " price.mku_id, " +
        " price.sku_id, " +
        " price.source_country_code, " +
        " price.target_country_code, " +
        " price.jd_cat_id  as last_cat_id, " +
        " price.brand_id, " +
        " price.currency, " +
        " price.agreement_price, " +
        " price.country_cost_price, " +
        " price.agreement_mark, " +
        " price.cost_mark, " +
        " price.audit_status, " +
        " price.auditor, " +
        " price.reject_reason, " +
        " price.profit_rate, " +
        " price.warning_msg, " +
        " price.level, " +
        " price.remark, " +
        " price.creator, " +
        " price.updater, " +
        " price.create_time, " +
        " price.update_time, " +
        " price.yn " +
        " from jdi_isc_country_agreement_price_draft_sharding price" +
        " <where>" +
        " and price.yn = 1 " +
        " <if test=\"sourceCountryCode != null and sourceCountryCode != ''\">" +
        "   and price.source_country_code = #{sourceCountryCode}" +
        " </if>" +
        " <if test=\"targetCountryCode != null and targetCountryCode != ''\">" +
        "   and price.target_country_code = #{targetCountryCode}" +
        " </if>" +
        " <if test=\"brandId != null and brandId != '' \">" +
        "   and price.brand_id = #{brandId}" +
        " </if>" +
        " <if test=\"mkuIds != null and mkuIds.size() > 0\">" +
        "   and price.mku_id in " +
        "       <foreach collection=\"mkuIds\" open=\"(\" close=\")\" separator=\",\" "+
        "           index=\"index\" item=\"item\">" +
        "           #{item}" +
        "       </foreach>" +
        " </if>" +
        " <if test=\"skuIds != null and skuIds.size() > 0\">" +
        "   and price.sku_id in " +
        "       <foreach collection=\"skuIds\" open=\"(\" close=\")\" separator=\",\" "+
        "           index=\"index\" item=\"item\">" +
        "           #{item}" +
        "       </foreach>" +
        " </if>" +
        " <if test=\"catIds != null and catIds.size() > 0\">" +
        "   and price.jd_cat_id in " +
        "       <foreach collection=\"catIds\" open=\"(\" close=\")\" separator=\",\" "+
        "           index=\"index\" item=\"item\">" +
        "           #{item}" +
        "       </foreach>" +
        " </if>" +
        " </where>" +
        " order by price.update_time desc,price.id desc " +
        " limit #{offset},#{size}" +
        "</script>"})
    List<CountryAgreementPriceDraftPageVO> pageSearch(CountryAgreementPriceDraftPageReqVO input);

    /**
     * 分页查询的列表总数
     * @param input 查询条件
     * @return 总条数
     */
    @Select({"<script> " +
        " select " +
        " count(0) " +
        " from  jdi_isc_country_agreement_price_draft_sharding price" +
        " <where>" +
        " and price.yn = 1 " +
        " <if test=\"sourceCountryCode != null and sourceCountryCode != ''\">" +
        "   and price.source_country_code = #{sourceCountryCode}" +
        " </if>" +
        " <if test=\"targetCountryCode != null and targetCountryCode != ''\">" +
        "   and price.target_country_code = #{targetCountryCode}" +
        " </if>" +
        " <if test=\"brandId != null and brandId != '' \">" +
        "   and price.brand_id = #{brandId}" +
        " </if>" +
        " <if test=\"mkuIds != null and mkuIds.size() > 0\">" +
        "   and price.mku_id in " +
        "       <foreach collection=\"mkuIds\" open=\"(\" close=\")\" separator=\",\" "+
        "           index=\"index\" item=\"item\">" +
        "           #{item}" +
        "       </foreach>" +
        " </if>" +
        " <if test=\"skuIds != null and skuIds.size() > 0\">" +
        "   and price.sku_id in " +
        "       <foreach collection=\"skuIds\" open=\"(\" close=\")\" separator=\",\" "+
        "           index=\"index\" item=\"item\">" +
        "           #{item}" +
        "       </foreach>" +
        " </if>" +
        " <if test=\"catIds != null and catIds.size() > 0\">" +
        "   and price.jd_cat_id in " +
        "       <foreach collection=\"catIds\" open=\"(\" close=\")\" separator=\",\" "+
        "           index=\"index\" item=\"item\">" +
        "           #{item}" +
        "       </foreach>" +
        " </if>" +
        " </where>" +
        "</script>"})
    long pageSearchTotal(CountryAgreementPriceDraftPageReqVO input);

    /**
     * 获取待审批总数
     * @param reqVO 查询请求参数
     * @return 总数
     */
    @Select({"<script>" +
            "SELECT count(1) FROM jdi_isc_country_agreement_price_draft_sharding draft" +
            "<where>" +
            "AND draft.yn = 1 " +
            "<if test=\"ids != null and ids.size() > 0\">" +
            "  AND draft.id IN " +
            "  <foreach collection=\"ids\" item=\"id\" open=\"(\" separator=\",\" close=\")\">" +
            "    #{id}" +
            "  </foreach>" +
            "</if>" +
            "<if test=\"skuIds != null and skuIds.size() > 0\">" +
            "  AND draft.sku_id IN " +
            "  <foreach collection=\"skuIds\" item=\"skuId\" open=\"(\" separator=\",\" close=\")\">" +
            "    #{skuId}" +
            "  </foreach>" +
            "</if>" +
            "<if test=\"brandId != null\">" +
            "  AND draft.brand_id = #{brandId} " +
            "</if>" +
            "<if test=\"catIds != null and catIds.size() > 0\">" +
            "  AND draft.jd_cat_id IN " +
            "  <foreach collection=\"catIds\" item=\"catId\" open=\"(\" separator=\",\" close=\")\">" +
            "    #{catId}" +
            "  </foreach>" +
            "</if>" +
            "<if test=\"profitRateBegin != null\">" +
            "  AND draft.profit_rate &gt;= #{profitRateBegin} " +
            "</if>" +
            "<if test=\"profitRateEnd != null\">" +
            "  AND draft.profit_rate &lt;= #{profitRateEnd} " +
            "</if>" +
            "<if test=\"warningMsg != null and warningMsg != ''\">" +
            "  AND draft.warning_msg LIKE CONCAT('%', #{warningMsg}, '%') " +
            "</if>" +
            "<if test=\"updater != null and updater != ''\">" +
            "  AND draft.updater = #{updater} " +
            "</if>" +
            "</where>" +
            "</script>"})
    long getApproveTotal(CountryAgreementPriceAuditReqVO reqVO);

    /**
     * 获取待审批数据
     * @param reqVO 查询请求参数
     * @return 总数
     */
    @Select({"<script>" +
            "SELECT draft.id,draft.source_country_code,draft.target_country_code,draft.mku_id,draft.jd_cat_id as last_cat_id,draft.sku_id" +
            ",draft.brand_id,draft.profit_rate,draft.warning_msg,draft.updater,draft.update_time " +
            "FROM jdi_isc_country_agreement_price_draft_sharding draft" +
            "<where>" +
            "AND draft.yn = 1 " +
            "<if test=\"ids != null and ids.size() > 0\">" +
            "  AND draft.id IN " +
            "  <foreach collection=\"ids\" item=\"id\" open=\"(\" separator=\",\" close=\")\">" +
            "    #{id}" +
            "  </foreach>" +
            "</if>" +
            "<if test=\"skuIds != null and skuIds.size() > 0\">" +
            "  AND draft.sku_id IN " +
            "  <foreach collection=\"skuIds\" item=\"skuId\" open=\"(\" separator=\",\" close=\")\">" +
            "    #{skuId}" +
            "  </foreach>" +
            "</if>" +
            "<if test=\"brandId != null\">" +
            "  AND draft.brand_id = #{brandId} " +
            "</if>" +
            "<if test=\"catIds != null and catIds.size() > 0\">" +
            "  AND draft.jd_cat_id IN " +
            "  <foreach collection=\"catIds\" item=\"catId\" open=\"(\" separator=\",\" close=\")\">" +
            "    #{catId}" +
            "  </foreach>" +
            "</if>" +
            "<if test=\"profitRateBegin != null\">" +
            "  AND draft.profit_rate &gt;= #{profitRateBegin} " +
            "</if>" +
            "<if test=\"profitRateEnd != null\">" +
            "  AND draft.profit_rate &lt;= #{profitRateEnd} " +
            "</if>" +
            "<if test=\"warningMsg != null and warningMsg != ''\">" +
            "  AND draft.warning_msg LIKE CONCAT('%', #{warningMsg}, '%') " +
            "</if>" +
            "<if test=\"updater != null and updater != ''\">" +
            "  AND draft.updater = #{updater} " +
            "</if>" +
            "</where> " +
            "ORDER BY draft.update_time DESC " +
            "</script>"})
    List<CountryAgreementPriceAuditResVO> listApproveSearch(CountryAgreementPriceAuditReqVO reqVO);

}
