package com.jdi.isc.product.soa.repository.mapper.sku;

import com.jdi.isc.product.soa.domain.sku.biz.SkuPageReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuResVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.repository.common.BasicMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【jdi_isc_sku_sharding(sku基础表)】的数据库操作Mapper
 * @createDate 2023-11-25 15:39:30
 * @Entity com.jdi.isc.product.centor.domain.SkuPO
 */
@Mapper
public interface SkuBaseMapper extends BasicMapper<SkuPO> {

    @Select({"<script>" +
            "select sku.sku_id skuId,sku.spu_id spuId,sku.jd_cat_id catId,sku.brand_id brandId,sku.jd_sku_id jdSkuId,sku.main_img mainImg,sku.update_time updateTime" +
            " from " +
            "  jdi_isc_sku_sharding sku" +
            " <where>" +
            "<if test=\"sourceCountryCode != null\">" +
            "  and sku.source_country_code = #{sourceCountryCode}" +
            "</if>" +
            "  and sku.yn = 1 " +
            "<if test=\"jdVendorCode != null\">" +
            "  and sku.jd_vendor_code = #{jdVendorCode}" +
            "</if>" +
            "<if test=\"jdSkuId != null\">" +
            "  and sku.jd_sku_id = #{jdSkuId}" +
            "</if>" +
            "<if test=\"brandId != null\">" +
            "  and sku.brand_id = #{brandId}" +
            "</if>" +
            "<if test=\"catIds != null and catIds.size() > 0\">" +
            "  and sku.jd_cat_id in " +
            "         <foreach collection=\"catIds\" item=\"item\" open=\"(\" close=\")\" separator=\",\"> " +
            "                            #{item} " +
            "         </foreach> " +
            "</if>" +
            "<if test=\"skuIds != null and skuIds.size() > 0\">" +
            "  and sku.sku_id in " +
            "         <foreach collection=\"skuIds\" item=\"item\" open=\"(\" close=\")\" separator=\",\"> " +
            "                            #{item} " +
            "         </foreach> " +
            "</if>" +
            "<if test=\"spuStatus != null\">" +
            "  and exists (" +
            "    select 1" +
            "    from" +
            "      jdi_isc_spu_sharding spu" +
            "    where" +
            "      spu.spu_id = sku.spu_id" +
            "      and spu.yn = 1" +
            "      and spu.spu_status = #{spuStatus}" +
            "  )" +
            "</if>" +
            "<if test=\"auditStatusSet != null and auditStatusSet.size() > 0\">" +
            "  and exists (" +
            "    select 1" +
            "    from" +
            "      jdi_isc_spu_sharding spu" +
            "    where" +
            "      spu.spu_id = sku.spu_id" +
            "      and spu.yn = 1" +
            "      and spu.audit_status in " +
            "         <foreach collection=\"auditStatusSet\" item=\"item\" open=\"(\" close=\")\" separator=\",\"> " +
            "                            #{item} " +
            "         </foreach> " +
            "  )" +
            "</if>" +
            "<if test=\"spuName != null\">" +
            "  and exists (" +
            "    select 1 " +
            "    from" +
            "      jdi_isc_spu_lang_sharding sl" +
            "    where" +
            "      sl.spu_id = sku.spu_id" +
            "      and sl.yn = 1" +
            "      and sl.lang = 'zh'" +
            "      and sl.spu_title like concat('%',#{spuName},'%')" +
            "  )" +
            "</if>" +
            " </where>" +
            "order by" +
            "  sku.update_time desc , sku.sku_id desc" +
            " limit #{offset},#{size} "+
            "</script>"}
    )
    List<SkuResVO>  listSearch(SkuPageReqVO reqVO);


    @Select({"<script>" +
            "select count(*) " +
            " from " +
            "  jdi_isc_sku_sharding sku" +
            " <where>" +
            "  sku.yn = 1 " +
            "<if test=\"sourceCountryCode != null\">" +
            "  and sku.source_country_code = #{sourceCountryCode}" +
            "</if>" +
            "<if test=\"jdVendorCode != null\">" +
            "  and sku.jd_vendor_code = #{jdVendorCode}" +
            "</if>" +
            "<if test=\"jdSkuId != null\">" +
            "  and sku.jd_sku_id = #{jdSkuId}" +
            "</if>" +
            "<if test=\"brandId != null\">" +
            "  and sku.brand_id = #{brandId}" +
            "</if>" +
            "<if test=\"catIds != null and catIds.size() > 0\">" +
            "  and sku.jd_cat_id in " +
            "         <foreach collection=\"catIds\" item=\"item\" open=\"(\" close=\")\" separator=\",\"> " +
            "                            #{item} " +
            "         </foreach> " +
            "</if>" +
            "<if test=\"skuIds != null  and skuIds.size() > 0\">" +
            "  and sku.sku_id in " +
            "         <foreach collection=\"skuIds\" item=\"item\" open=\"(\" close=\")\" separator=\",\"> " +
            "                            #{item} " +
            "         </foreach> " +
            "</if>" +
            "<if test=\"spuStatus != null\">" +
            "  and exists (" +
            "    select 1" +
            "    from" +
            "      jdi_isc_spu_sharding spu" +
            "    where" +
            "      spu.spu_id = sku.spu_id" +
            "      and spu.yn = 1" +
            "      and spu.spu_status = #{spuStatus}" +
            "  )" +
            "</if>" +
            "<if test=\"auditStatusSet != null and auditStatusSet.size() > 0\">" +
            "  and exists (" +
            "    select 1" +
            "    from" +
            "      jdi_isc_spu_sharding spu" +
            "    where" +
            "      spu.spu_id = sku.spu_id" +
            "      and spu.yn = 1" +
            "      and spu.audit_status in " +
            "         <foreach collection=\"auditStatusSet\" item=\"item\" open=\"(\" close=\")\" separator=\",\"> " +
            "                            #{item} " +
            "         </foreach> " +
            "  )" +
            "</if>" +
            "<if test=\"spuName != null\">" +
            "  and exists (" +
            "    select 1 " +
            "    from" +
            "      jdi_isc_spu_lang_sharding sl" +
            "    where" +
            "      sl.spu_id = sku.spu_id" +
            "      and sl.yn = 1" +
            "      and sl.lang = 'zh'" +
            "      and sl.spu_title like concat('%',#{spuName},'%')" +
            "  )" +
            " </if>" +
            " </where>" +
            "</script>"}
    )
    long listSearchTotal(SkuPageReqVO reqVO);

    @Select({"<script> " +
            " select " +
            "  distinct jd_vendor_code,brand_id " +
            " from " +
            "  jdi_isc_sku_sharding " +
            " where " +
            "  brand_id in  " +
            "      <foreach collection=\"brandIds\" item=\"id\" open=\"(\" close=\")\" separator=\",\"> " +
            "           #{id} " +
            "      </foreach> " +
            "  and yn = 1  "+
            "  and jd_vendor_code is not null  " +
            "</script>"})
    List<SkuPO> querySkuListByBrandId(Collection<Long> brandIds);

    /**
     * 查询所有满足条件的京东SKU ID列表
     * @param updateTime 更新时间阈值
     * @return 京东SKU ID列表
     */
    @Select("select distinct jd_sku_id from jdi_isc_sku_sharding where jd_sku_id is not null and yn = 1 and update_time >= #{updateTime} order by jd_sku_id")
    List<Long> queryAllJdSkuIdsByUpdateTime(String updateTime);
}




