package com.jdi.isc.product.soa.repository.mapper.customerSku;

import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceAuditReqVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceAuditResVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceDraftReqVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceDraftVO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDraftPO;
import com.jdi.isc.product.soa.repository.common.BasicMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: sku客制化价格-草稿表
 * @Author: zhaokun51
 * @Date: 2024/10/21 13:30
 **/
@Mapper
public interface CustomerSkuPriceDraftBaseMapper extends BasicMapper<CustomerSkuPriceDraftPO> {

    /**
     * 根据查询条件搜索客户定制价格
     * @param reqVO 包含查询条件的请求对象
     * @return 符合条件的SPU列表
     */
    @Select({"<script>" +
            "SELECT " +
            "   detailDraft.id, " +
            "   detailDraft.sku_id, " +
            "   draft.source_country_code, " +
            "   detailDraft.client_code, " +
            "   draft.client_name, " +
            "   draft.target_country_code, " +
            "   draft.vendor_code, " +
            "   detailDraft.customer_trade_type, " +
            "   detailDraft.currency, " +
            "   detailDraft.customer_sale_price, " +
            "   detailDraft.audit_status, " +
            "   detailDraft.begin_time, " +
            "   detailDraft.end_time, " +
            "   detailDraft.level, " +
            "   detailDraft.auditor, " +
            "   detailDraft.reject_reason, " +
            "   detailDraft.remark, " +
            "   detailDraft.creator, " +
            "   detailDraft.updater, " +
            "   detailDraft.create_time, " +
            "   detailDraft.update_time, " +
            "   detailDraft.enable_status, " +
            "   detailDraft.end_time, " +
            "   detailDraft.begin_time, "+
            "    CASE" +
            "        WHEN #{nowTime} BETWEEN detailDraft.begin_time AND detailDraft.end_time THEN 1 " +
            "        WHEN #{nowTime} &lt; detailDraft.begin_time THEN 2" +
            "        WHEN #{nowTime} &gt; detailDraft.end_time THEN 3 " +
            "        ELSE 4" +
            "    END AS sort_enable_status," +
            "   detailDraft.yn " +
            " FROM jdi_isc_customer_sku_price_detail_draft_sharding detailDraft " +
            " LEFT JOIN jdi_isc_customer_sku_price_draft_sharding draft" +
            "        ON draft.sku_id = detailDraft.sku_id AND draft.id = detailDraft.biz_id" +
            " <where>" +
            " <if test=\"skuId != null\">" +
            "   and detailDraft.sku_id = #{skuId}" +
            " </if>" +
            " <if test=\"vendorCode != null and vendorCode !=''\">" +
            "   and draft.vendor_code = #{vendorCode}" +
            " </if>" +
            " <if test=\"clientCode != null and clientCode != ''\">" +
            "   and detailDraft.client_code = #{clientCode}" +
            " </if>" +
            " <if test=\"sourceCountryCode != null and sourceCountryCode != ''\">" +
            "   and draft.source_country_code = #{sourceCountryCode}" +
            " </if>" +
            " <if test=\"targetCountryCodes != null and targetCountryCodes.size()>0\">" +
            "   and draft.target_country_code in " +
            "       <foreach collection=\"targetCountryCodes\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " <if test=\"auditStatus != null\">" +
            "   and detailDraft.audit_status = #{auditStatus}" +
            " </if>" +
            " <if test=\"level != null\">" +
            "   and detailDraft.level = #{level}" +
            " </if>" +
            " <if test=\"updater != null\">" +
            "   and detailDraft.updater like concat('%',#{updater},'%')" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 3 \">" +
            "   and detailDraft.enable_status = 1 " +
            "   and detailDraft.begin_time &gt; #{nowTime}" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 2 \">" +
            "   and detailDraft.enable_status = 1 " +
            "   and detailDraft.end_time &lt; #{nowTime}" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 1 \">" +
            "   and detailDraft.enable_status = 1 " +
            "   and detailDraft.end_time &gt;= #{nowTime}" +
            "   and detailDraft.begin_time &lt;= #{nowTime}" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 0 \">" +
            "   and detailDraft.enable_status = 0" +
            " </if>" +
            " <if test=\"skuIds != null and skuIds.size() > 0\">" +
            "   and detailDraft.sku_id in " +
            "       <foreach collection=\"skuIds\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " and detailDraft.yn = 1 " +
            " and draft.yn = 1 " +
            " </where>" +
            " ORDER BY" +
            "    sort_enable_status ASC," +
            "    detailDraft.create_time DESC" +
            " limit #{offset},#{size}" +
            "</script>"})
    List<CustomerSkuPriceDraftVO> listSearch(CustomerSkuPriceDraftReqVO reqVO);

    /**
     * 获取总数
     * @param reqVO 查询请求参数
     * @return 总数
     */
    @Select({"<script>" +
            "SELECT count(1) " +
            " FROM jdi_isc_customer_sku_price_detail_draft_sharding detailDraft " +
            " LEFT JOIN jdi_isc_customer_sku_price_draft_sharding draft" +
            "        ON draft.sku_id = detailDraft.sku_id AND draft.id = detailDraft.biz_id" +
            " <where>" +
            " <if test=\"skuId != null\">" +
            "   and detailDraft.sku_id = #{skuId}" +
            " </if>" +
            " <if test=\"vendorCode != null and vendorCode !=''\">" +
            "   and draft.vendor_code = #{vendorCode}" +
            " </if>" +
            " <if test=\"clientCode != null and clientCode != ''\">" +
            "   and detailDraft.client_code = #{clientCode}" +
            " </if>" +
            " <if test=\"sourceCountryCode != null and sourceCountryCode != ''\">" +
            "   and draft.source_country_code = #{sourceCountryCode}" +
            " </if>" +
            " <if test=\"targetCountryCodes != null and targetCountryCodes.size()>0\">" +
            "   and draft.target_country_code in " +
            "       <foreach collection=\"targetCountryCodes\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " <if test=\"auditStatus != null\">" +
            "   and detailDraft.audit_status = #{auditStatus}" +
            " </if>" +
            " <if test=\"level != null\">" +
            "   and detailDraft.level = #{level}" +
            " </if>" +
            " <if test=\"updater != null\">" +
            "   and detailDraft.updater like concat('%',#{updater},'%')" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 3 \">" +
            "   and detailDraft.enable_status = 1 " +
            "   and detailDraft.begin_time &gt; #{nowTime}" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 2 \">" +
            "   and detailDraft.enable_status = 1 " +
            "   and detailDraft.end_time &lt; #{nowTime}" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 1 \">" +
            "   and detailDraft.enable_status = 1 " +
            "   and detailDraft.end_time &gt;= #{nowTime}" +
            "   and detailDraft.begin_time &lt;= #{nowTime}" +
            " </if>" +
            " <if test=\"enableStatus != null and enableStatus == 0 \">" +
            "   and detailDraft.enable_status = 0" +
            " </if>" +
            " <if test=\"skuIds != null and skuIds.size() > 0\">" +
            "   and detailDraft.sku_id in " +
            "       <foreach collection=\"skuIds\" open=\"(\" close=\")\" separator=\",\" "+
            "           index=\"index\" item=\"item\">" +
            "           #{item}" +
            "       </foreach>" +
            " </if>" +
            " and detailDraft.yn = 1 " +
            " and draft.yn = 1" +
            " </where>" +
            "</script>"})
    long getTotal(CustomerSkuPriceDraftReqVO reqVO);

    /**
     * 获取待审批总数
     * @param reqVO 查询请求参数
     * @return 总数
     */
    @Select({"<script>" +
            "SELECT count(1) FROM jdi_isc_customer_sku_price_detail_draft_sharding detailDraft " +
            " LEFT JOIN jdi_isc_customer_sku_price_draft_sharding draft" +
            "        ON draft.sku_id = detailDraft.sku_id AND draft.id = detailDraft.biz_id" +
            "<where>" +
            "AND draft.yn = 1 " +
            "AND detailDraft.yn = 1 " +
            "<if test=\"ids != null and ids.size() > 0\">" +
            "  AND detailDraft.id IN " +
            "  <foreach collection=\"ids\" item=\"id\" open=\"(\" separator=\",\" close=\")\">" +
            "    #{id}" +
            "  </foreach>" +
            "</if>" +
            "<if test=\"vendorCode != null and vendorCode != ''\">" +
            "  AND draft.vendor_code = #{vendorCode} " +
            "</if>" +
            "<if test=\"clientCode != null and clientCode != ''\">" +
            "  AND detailDraft.client_code = #{clientCode} " +
            "</if>" +
            "<if test=\"skuIds != null and skuIds.size() > 0\">" +
            "  AND detailDraft.sku_id IN " +
            "  <foreach collection=\"skuIds\" item=\"skuId\" open=\"(\" separator=\",\" close=\")\">" +
            "    #{skuId}" +
            "  </foreach>" +
            "</if>" +
            "<if test=\"brandId != null\">" +
            "  AND draft.brand_id = #{brandId} " +
            "</if>" +
            "<if test=\"catIds != null and catIds.size() > 0\">" +
            "  AND draft.jd_cat_id IN " +
            "  <foreach collection=\"catIds\" item=\"catId\" open=\"(\" separator=\",\" close=\")\">" +
            "    #{catId}" +
            "  </foreach>" +
            "</if>" +
            "<if test=\"profitRateBegin != null\">" +
            "  AND detailDraft.profit_rate &gt;= #{profitRateBegin} " +
            "</if>" +
            "<if test=\"profitRateEnd != null\">" +
            "  AND detailDraft.profit_rate &lt;= #{profitRateEnd} " +
            "</if>" +
            "<if test=\"warningMsg != null and warningMsg != ''\">" +
            "  AND detailDraft.warning_msg LIKE CONCAT('%', #{warningMsg}, '%') " +
            "</if>" +
            "<if test=\"updater != null and updater != ''\">" +
            "  AND detailDraft.updater = #{updater} " +
            "</if>" +
            "<if test=\"buyer != null and buyer != ''\">" +
            "  AND draft.buyer = #{buyer} " +
            "</if>" +
            "</where>" +
            "</script>"})
    long getApproveTotal(CustomerSkuPriceAuditReqVO reqVO);

    /**
     * 获取待审批数据
     * @param reqVO 查询请求参数
     * @return 总数
     */
    @Select({"<script>" +
            "SELECT detailDraft.id,draft.source_country_code,detailDraft.client_code,detailDraft.sku_id" +
            ",draft.jd_cat_id as cat_id,draft.brand_id,draft.vendor_code,detailDraft.begin_time,detailDraft.end_time" +
            ", detailDraft.process_from_data" +
            ", detailDraft.adjustment_price_reason" +
            ", detailDraft.attachment_urls" +
            ",draft.buyer,detailDraft.profit_rate,detailDraft.warning_msg,detailDraft.updater,detailDraft.update_time " +
            "FROM jdi_isc_customer_sku_price_detail_draft_sharding detailDraft " +
            " LEFT JOIN jdi_isc_customer_sku_price_draft_sharding draft" +
            "        ON draft.sku_id = detailDraft.sku_id AND draft.id = detailDraft.biz_id" +
            "<where>" +
            "AND draft.yn = 1 " +
            "AND detailDraft.yn = 1 " +
            "<if test=\"ids != null and ids.size() > 0\">" +
            "  AND detailDraft.id IN " +
            "  <foreach collection=\"ids\" item=\"id\" open=\"(\" separator=\",\" close=\")\">" +
            "    #{id}" +
            "  </foreach>" +
            "</if>" +
            "<if test=\"vendorCode != null and vendorCode != ''\">" +
            "  AND draft.vendor_code = #{vendorCode} " +
            "</if>" +
            "<if test=\"clientCode != null and clientCode != ''\">" +
            "  AND detailDraft.client_code = #{clientCode} " +
            "</if>" +
            "<if test=\"skuIds != null and skuIds.size() > 0\">" +
            "  AND detailDraft.sku_id IN " +
            "  <foreach collection=\"skuIds\" item=\"skuId\" open=\"(\" separator=\",\" close=\")\">" +
            "    #{skuId}" +
            "  </foreach>" +
            "</if>" +
            "<if test=\"brandId != null\">" +
            "  AND draft.brand_id = #{brandId} " +
            "</if>" +
            "<if test=\"catIds != null and catIds.size() > 0\">" +
            "  AND draft.jd_cat_id IN " +
            "  <foreach collection=\"catIds\" item=\"catId\" open=\"(\" separator=\",\" close=\")\">" +
            "    #{catId}" +
            "  </foreach>" +
            "</if>" +
            "<if test=\"profitRateBegin != null\">" +
            "  AND detailDraft.profit_rate &gt;= #{profitRateBegin} " +
            "</if>" +
            "<if test=\"profitRateEnd != null\">" +
            "  AND detailDraft.profit_rate &lt;= #{profitRateEnd} " +
            "</if>" +
            "<if test=\"warningMsg != null and warningMsg != ''\">" +
            "  AND detailDraft.warning_msg LIKE CONCAT('%', #{warningMsg}, '%') " +
            "</if>" +
            "<if test=\"updater != null and updater != ''\">" +
            "  AND detailDraft.updater = #{updater} " +
            "</if>" +
            "<if test=\"buyer != null and buyer != ''\">" +
            "  AND draft.buyer = #{buyer} " +
            "</if>" +
            "</where> " +
            "ORDER BY detailDraft.update_time DESC " +
            "</script>"})
    List<CustomerSkuPriceAuditResVO> listApproveSearch(CustomerSkuPriceAuditReqVO reqVO);

}
