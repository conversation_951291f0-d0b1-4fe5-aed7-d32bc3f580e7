package com.jdi.isc.product.soa.repository.mapper.customerSku;

import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceWarningPageReqVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceWarningVO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceWarningPO;
import com.jdi.isc.product.soa.repository.common.BasicMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: sku客制化价格预警表
 * @Author: zhaokun51
 * @Date: 2025/03/17 16:51
 **/
@Mapper
public interface CustomerSkuPriceWarningBaseMapper extends BasicMapper<CustomerSkuPriceWarningPO> {


    @Select({
            "<script> " +
                    "SELECT\n" +
                    "    count(*)\n" +
                    "    FROM\n" +
                    "    jdi_isc_customer_sku_price_warning_sharding a\n" +
                    "    WHERE\n" +
                    "    a.yn = 1\n" +
                    "    <if test=\"warningStatus != null\">\n" +
                    "        AND a.warning_status = #{warningStatus}\n" +
                    "    </if>\n" +
                    "    <if test=\"clientCode != null\">\n" +
                    "        AND a.client_code = #{clientCode}\n" +
                    "    </if>\n" +
                    "    <if test=\"sourceCountryCode != null and sourceCountryCode != ''\">\n" +
                    "        AND a.source_country_code = #{sourceCountryCode}\n" +
                    "    </if>\n" +
                    "    <if test=\"targetCountryCode != null and targetCountryCode != ''\">\n" +
                    "        AND a.target_country_code = #{targetCountryCode}\n" +
                    "    </if>\n" +
                    "    <if test=\"skuIds != null and skuIds.size() > 0\">\n" +
                    "        AND a.sku_id IN\n" +
                    "        <foreach item=\"item\" index=\"index\" collection=\"skuIds\" open=\"(\" separator=\",\" close=\")\">\n" +
                    "            #{item}\n" +
                    "        </foreach>\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"lastCatIds != null and lastCatIds.size() > 0\">\n" +
                    "        AND a.jd_cat_id IN\n" +
                    "        <foreach item=\"item\" index=\"index\" collection=\"lastCatIds\" open=\"(\" separator=\",\" close=\")\">\n" +
                    "            #{item}\n" +
                    "        </foreach>\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"dataStatusSource != null\">\n" +
                    "        AND a.data_status_source = #{dataStatusSource}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"brandId != null\">\n" +
                    "        AND a.brand_id = #{brandId}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"countryMkuPoolStatus != null\">\n" +
                    "        AND a.country_mku_pool_status = #{countryMkuPoolStatus}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"customerMkuPoolStatus != null\">\n" +
                    "        AND a.customer_mku_pool_status = #{customerMkuPoolStatus}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"beginWarningTime != null\">\n" +
                    "        AND a.update_time &gt;= #{beginWarningTime}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"endWarningTime != null\">\n" +
                    "        AND a.update_time &lt;= #{endWarningTime}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"warningStatusList != null and warningStatusList.size() > 0\">\n" +
                    "        AND a.warning_status IN\n" +
                    "        <foreach item=\"item\" index=\"index\" collection=\"warningStatusList\" open=\"(\" separator=\",\" close=\")\">\n" +
                    "            #{item}\n" +
                    "        </foreach>\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"availableSaleStatus != null\">\n" +
                    "        AND a.available_sale_status = #{availableSaleStatus}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"testProduct != null\">\n" +
                    "        AND a.test_product = #{testProduct}\n" +
                    "    </if>\n" +
                    "</script>"
    })
    long getTotal(CustomerSkuPriceWarningPageReqVO input);

    @Select({
            "<script> " +
                    "SELECT\n" +
                    "a.id,\n" +
                    "a.biz_id,\n" +
                    "a.sku_id,\n" +
                    "a.client_code,\n" +
                    "a.source_country_code,\n" +
                    "a.target_country_code,\n" +
                    "a.customer_trade_type,\n" +
                    "a.currency,\n" +
                    "a.customer_sale_price,\n" +
                    "a.begin_time,\n" +
                    "a.end_time,\n" +
                    "a.profit_rate,\n" +
                    "a.profit_rate_limit,\n" +
                    "a.profit_rate_low_limit,\n" +
                    "a.warning_status,\n" +
                    "a.data_status_source,\n" +
                    "a.jd_cat_id,\n" +
                    "a.brand_id,\n" +
                    "a.country_mku_pool_status,\n" +
                    "a.customer_mku_pool_status,\n" +
                    "a.test_product,\n" +
                    "a.available_sale_status,\n" +
                    "a.remark,\n" +
                    "a.creator,\n" +
                    "a.updater,\n" +
                    "a.create_time,\n" +
                    "a.update_time,\n" +
                    "a.yn" +
                    "    FROM\n" +
                    "    jdi_isc_customer_sku_price_warning_sharding a\n" +
                    "    WHERE\n" +
                    "    a.yn = 1\n" +
                    "    <if test=\"warningStatus != null\">\n" +
                    "        AND a.warning_status = #{warningStatus}\n" +
                    "    </if>\n" +
                    "    <if test=\"clientCode != null\">\n" +
                    "        AND a.client_code = #{clientCode}\n" +
                    "    </if>\n" +
                    "    <if test=\"sourceCountryCode != null and sourceCountryCode != ''\">\n" +
                    "        AND a.source_country_code = #{sourceCountryCode}\n" +
                    "    </if>\n" +
                    "    <if test=\"targetCountryCode != null and targetCountryCode != ''\">\n" +
                    "        AND a.target_country_code = #{targetCountryCode}\n" +
                    "    </if>\n" +
                    "    <if test=\"skuIds != null and skuIds.size() > 0\">\n" +
                    "        AND a.sku_id IN\n" +
                    "        <foreach item=\"item\" index=\"index\" collection=\"skuIds\" open=\"(\" separator=\",\" close=\")\">\n" +
                    "            #{item}\n" +
                    "        </foreach>\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"lastCatIds != null and lastCatIds.size() > 0\">\n" +
                    "        AND a.jd_cat_id IN\n" +
                    "        <foreach item=\"item\" index=\"index\" collection=\"lastCatIds\" open=\"(\" separator=\",\" close=\")\">\n" +
                    "            #{item}\n" +
                    "        </foreach>\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"dataStatusSource != null\">\n" +
                    "        AND a.data_status_source = #{dataStatusSource}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"brandId != null\">\n" +
                    "        AND a.brand_id = #{brandId}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"countryMkuPoolStatus != null\">\n" +
                    "        AND a.country_mku_pool_status = #{countryMkuPoolStatus}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"customerMkuPoolStatus != null\">\n" +
                    "        AND a.customer_mku_pool_status = #{customerMkuPoolStatus}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"beginWarningTime != null\">\n" +
                    "        AND a.update_time &gt;= #{beginWarningTime}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"endWarningTime != null\">\n" +
                    "        AND a.update_time &lt;= #{endWarningTime}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"warningStatusList != null and warningStatusList.size() > 0\">\n" +
                    "        AND a.warning_status IN\n" +
                    "        <foreach item=\"item\" index=\"index\" collection=\"warningStatusList\" open=\"(\" separator=\",\" close=\")\">\n" +
                    "            #{item}\n" +
                    "        </foreach>\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"availableSaleStatus != null\">\n" +
                    "        AND a.available_sale_status = #{availableSaleStatus}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    <if test=\"testProduct != null\">\n" +
                    "        AND a.test_product = #{testProduct}\n" +
                    "    </if>\n" +
                    "\n" +
                    "    ORDER BY a.update_time DESC\n" +
                    "    limit #{offset}, #{size}" +
                    "</script>"
    })
    List<CustomerSkuPriceWarningVO> listSearch(CustomerSkuPriceWarningPageReqVO input);
}
