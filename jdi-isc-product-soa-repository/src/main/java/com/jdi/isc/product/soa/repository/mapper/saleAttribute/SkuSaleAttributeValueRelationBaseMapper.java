package com.jdi.isc.product.soa.repository.mapper.saleAttribute;

import com.jdi.isc.product.soa.domain.saleAttribute.po.SkuSaleAttributeValueRelationPO;
import com.jdi.isc.product.soa.repository.common.BasicMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * SKU销售属性值关系表基础mapper
 * <AUTHOR>
 * @date 2025-06-26
 */
@Mapper
public interface SkuSaleAttributeValueRelationBaseMapper extends BasicMapper<SkuSaleAttributeValueRelationPO> {
} 