package com.jdi.isc.product.soa.repository.mapper.saleAttribute;

import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeLangPO;
import com.jdi.isc.product.soa.repository.common.BasicMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 销售属性名多语表基础mapper
 * <AUTHOR>
 * @date 2025-06-26
 */
@Mapper
public interface SaleAttributeLangBaseMapper extends BasicMapper<SaleAttributeLangPO> {
} 