package com.jdi.isc.aggregate.read.domain.spu.biz;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 扩展属性分组VO
 */
@Data
public class ExtendPropertyGroupVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分组ID
     */
    private Integer comGroupId;

    /**
     * 分组名称
     */
    private String comGroupName;

    /**
     * 商品扩展属性列表
     */
    private List<ShotPropertyVO> extendPropertyList;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 将JSON字符串转换为ExtendPropertyGroupVO对象列表
     *
     * @param groupExtAttribute JSON格式的字符串，结构与ExtendPropertyGroupVO一致
     * @return List<ExtendPropertyGroupVO> 扩展属性分组列表
     */
    public static List<ExtendPropertyGroupVO> obtainExtendPropertyGroupVOList(String groupExtAttribute) {
        try {
            if (StringUtils.isBlank(groupExtAttribute)) {
                return new ArrayList<>();
            }
            return OBJECT_MAPPER.readValue(groupExtAttribute, new TypeReference<List<ExtendPropertyGroupVO>>() {});
        } catch (Exception e) {
            throw new RuntimeException("解析扩展属性分组JSON失败", e);
        }
    }

    /**
     * 将List<ExtendPropertyGroupVO>序列化为字符串
     *
     * @param extendPropertyGroups 扩展属性分组列表
     * @return 序列化后的JSON字符串
     */
    public static String serializeExtendPropertyGroups(List<ExtendPropertyGroupVO> extendPropertyGroups) {
        try {
            if (extendPropertyGroups == null || extendPropertyGroups.isEmpty()) {
                return "";
            }
            return OBJECT_MAPPER.writeValueAsString(extendPropertyGroups);
        } catch (Exception e) {
            throw new RuntimeException("序列化扩展属性分组列表失败", e);
        }
    }

    /**
     * 合并SPU和SKU的扩展属性组并序列化为字符串
     * @param spuGroupExtAttribute SPU扩展属性组JSON字符串
     * @param skuGroupExtAttribute SKU扩展属性组JSON字符串
     * @return 合并并序列化后的扩展属性字符串
     */
    public static String obtainStringMergeExtProperty(String spuGroupExtAttribute, String skuGroupExtAttribute){
        return serializeExtendPropertyGroups(mergeAndDeserializeExtendPropertyGroups(spuGroupExtAttribute, skuGroupExtAttribute));
    }

    /**
     * 将两个JSON字符串反序列化并合并为List<ExtendPropertyGroupVO>
     *
     * @param spuGroupExtAttribute SPU扩展属性组JSON字符串
     * @param skuGroupExtAttribute SKU扩展属性组JSON字符串
     * @return 合并后的ExtendPropertyGroupVO列表
     */
    public static List<ExtendPropertyGroupVO> mergeAndDeserializeExtendPropertyGroups(String spuGroupExtAttribute, String skuGroupExtAttribute) {
        try {
            List<ExtendPropertyGroupVO> spuGroups = obtainExtendPropertyGroupVOList(spuGroupExtAttribute);
            List<ExtendPropertyGroupVO> skuGroups = obtainExtendPropertyGroupVOList(skuGroupExtAttribute);

            return Stream.concat(spuGroups.stream(), skuGroups.stream())
                    .collect(Collectors.toMap(
                            ExtendPropertyGroupVO::getComGroupId,
                            group -> group,
                            (group1, group2) -> {
                                group1.getExtendPropertyList().addAll(group2.getExtendPropertyList());
                                return group1;
                            }
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("合并和反序列化扩展属性分组失败", e);
        }
    }

}
