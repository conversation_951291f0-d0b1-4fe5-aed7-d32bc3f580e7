package com.jdi.isc.aggregate.read.domain.spu.biz;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 简化版属性VO
 */
@Data
public class ShotPropertyVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 属性ID
     */
    private Long attributeId;

    /**
     * 扩展属性级别  0:product  1:sku  2:product和sku都能用
     */
    private Integer level;

    /**
     * 属性值列表
     */
    private List<ShotPropertyValueVO> propertyValueVOList;
}
