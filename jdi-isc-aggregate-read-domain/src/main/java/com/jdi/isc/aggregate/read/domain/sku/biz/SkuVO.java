package com.jdi.isc.aggregate.read.domain.sku.biz;

import com.jdi.isc.aggregate.read.domain.spu.biz.PropertyValueVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * SKU业务对象
 *
 * <AUTHOR>
 * @date 2023/11/25
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SkuVO {
    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 主skuId
     */
    private Long mainSkuId;

    /**
     * SPUID
     */
    private Long spuId;

    /**
     * 工业国内SKUID
     */
    private Long jdSkuId;

    /**
     * 京东主站主skuId
     */
    private Long jdMainSkuId;

    /**
     * 京东主站spuId
     */
    private Long jdSpuId;

    /**
     * EPT SKUID
     */
    private Long eptSkuId;

    /**
     * 供应商简码
     */
    private String vendorCode;

    /**
     * 供应商SKUID
     */
    private Long vendorSkuId;

    /**
     * upc编码
     */
    private String upcCode;

    /**
     * 货源国、国家站
     */
    private String sourceCountryCode;

    /**
     * 末级后台类目ID
     */
    private Long catId;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 毛重(g)
     */
    @NotNull(message = "重量不能为空")
    private String weight;

    /**
     * 长(mm)
     */
    @NotNull(message = "长不能为空")
    private String length;

    /**
     * 宽(mm)
     */
    @NotNull(message = "宽不能为空")
    private String width;

    /**
     * 高(mm)
     */
    @NotNull(message = "高不能为空")
    private String height;

    /**
     * 销售属性及值 aa:bb,cc:dd
     */
    private String saleAttribute;

    /**
     * sku主图
     */
    @NotNull(message = "sku主图不能为空")
    private String mainImg;

    /**
     * sku细节图
     */
    private String detailImg;
    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后修改时间
     */
    private Date updateTime;

    /**
     * 逻辑删除 0=删除,1有效
     */
    private Integer yn;
    /**
     * 对商模式
     */
    private String vendorTradeType;
    /**
     * 对客模式
     */
    private String customerTradeType;
    /**
     * 币种
     */
    private String currency;
    /**
     * 采购价
     */
    @NotNull(message = "采购价不能为空")
    private String purchasePrice;
    /**
     * 销售价
     */
    @NotNull(message = "销售价不能为空")
    private String salePrice;
    /**
     * 库存数量
     */
    private String stockNum;
    /**
     * sku上架时间
     */
    private Date skuOnlineTime;
    /**
     * 销售属性列表
     */
    @NotEmpty(message = "SKU的销售属性值不能为空")
    private List<PropertyValueVO> storeSalePropertyList;
    /**
     * 带磁 默认0=不带磁 1=带磁
     * @date 2024-1-6
     */
    private Integer magnetic;
    /**
     * 带电 默认0=不带电 1=带电
     * @date 2024-1-6
     */
    private Integer electric;
}
