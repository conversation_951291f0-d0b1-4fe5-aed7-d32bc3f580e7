package com.jdi.isc.aggregate.read.domain.spu.biz;

import lombok.Data;

import java.io.Serializable;

/**
 * 简化版属性值VO
 */
@Data
public class ShotPropertyValueVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 属性值ID（当id不为空时，lang和attributeValueName必须为空）
     */
    private Long attributeValueId;

    /**
     * 属性值语种
     */
    private String lang;

    /**
     * 属性值对应语言的翻译
     */
    private String attributeValueName;
}
