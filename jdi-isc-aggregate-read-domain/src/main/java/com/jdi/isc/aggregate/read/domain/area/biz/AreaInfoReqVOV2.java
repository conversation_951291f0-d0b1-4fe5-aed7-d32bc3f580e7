package com.jdi.isc.aggregate.read.domain.area.biz;

import com.jdi.isc.aggregate.read.domain.common.biz.ClientBaseReqVOV2;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * 区域列表请求
 * @date 2024/2/19 11:19
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AreaInfoReqVOV2 extends ClientBaseReqVOV2 implements Serializable {

    /** 区域ID */
    private Set<Long> areaIds;
}
