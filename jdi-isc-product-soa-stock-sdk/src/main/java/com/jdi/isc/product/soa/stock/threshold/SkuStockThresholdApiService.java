package com.jdi.isc.product.soa.stock.threshold;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.stock.threshold.biz.req.BatchQueryStockThresholdDTO;
import com.jdi.isc.product.soa.stock.threshold.biz.req.SkuStockThresholdPageApiDTO;
import com.jdi.isc.product.soa.stock.threshold.biz.req.SkuStockThresholdReqDTO;
import com.jdi.isc.product.soa.stock.threshold.biz.req.SkuStockThresholdUpdateDTO;
import com.jdi.isc.product.soa.stock.threshold.biz.res.SkuStockThresholdResDTO;
import com.jdi.isc.product.soa.stock.validation.SkuStockThresholdValidation;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * @Description: SKU安全库存阈值api服务
 * @Author: sunlei61
 * @Date: 2025/01/12 20:00
 **/

public interface SkuStockThresholdApiService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<SkuStockThresholdPageApiDTO.Response>> pageSearch(SkuStockThresholdPageApiDTO.Request input);

    /**
     * 保存或更新 SKU 库存阈值信息。
     * @param input SkuStockThresholdReqDTO 对象，包含要保存或更新的 SKU 库存阈值信息。
     * @return DataResponse<Boolean> 对象，表示操作是否成功。
     */
    @Validated(SkuStockThresholdValidation.saveOrUpdate.class)
    DataResponse<Boolean> saveOrUpdate(SkuStockThresholdReqDTO input);

    /**
     * 批量查询 SKU 库存阈值信息
     * @param batchQueryStockThresholdDTO 包含查询条件的数据传输对象
     * @return 包含查询结果的数据响应对象
     */
    @Validated(SkuStockThresholdValidation.batchQueryStockThreshold.class)
    DataResponse<List<SkuStockThresholdResDTO>> batchQuerySkuStockThreshold(BatchQueryStockThresholdDTO batchQueryStockThresholdDTO);

    /**
     * 更新SKU库存阈值属性信息
     * @param input SKU库存阈值更新数据传输对象，包含需要更新的阈值信息
     * @return 包含操作结果的响应对象，其中字符串类型数据表示操作结果信息
     */
    @Validated(SkuStockThresholdValidation.saveOrUpdate.class)
    DataResponse<String> updateSkuStockThresholdInfo(SkuStockThresholdUpdateDTO input);
}
