package com.jdi.isc.product.soa.stock.composite;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.stock.composite.req.StockCompReadReqDTO;
import com.jdi.isc.product.soa.stock.mku.res.IscMkuStockResDTO;

import java.util.Map;

/**
 * @author：xubing82
 * @date：2025/7/18 10:29
 * @description：库存复合读接口，包括mku、sku库存
 */
public interface IscStockCompositeReadApiService {


    /**
     * 查询在途单据当前可用库存，有货先发场景专用
     * 备注：可用现货数量=MAX（订单SKU现货预占，纯现货数）
     *
     * @param req mku以及sku信息
     * @return
     */
    DataResponse<Map<Long, IscMkuStockResDTO>> getStockForOnWayBill(StockCompReadReqDTO req);


}
