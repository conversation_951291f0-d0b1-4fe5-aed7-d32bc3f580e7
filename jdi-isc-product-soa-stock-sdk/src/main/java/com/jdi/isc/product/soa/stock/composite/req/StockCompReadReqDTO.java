package com.jdi.isc.product.soa.stock.composite.req;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author：xubing82
 * @date：2025/7/18 10:31
 * @description：库存复合查询接口请求实体
 */
@Data
public class StockCompReadReqDTO {
    /**
     * sku信息
     */
    @NotNull(message = "stockItem库存请求信息不能为空")
    @Valid
    private List<StockCompItemReadReqDTO> stockItem;

    /**
     * 父订单号
     */
    private String orderId;

    /**
     * 子订单号
     */
    @NotNull(message = "单据号不能为空")
    private String bizNo;

    /**
     * 客户编码
     */
    @NotNull(message = "clientCode不能为空")
    private String clientCode;

    /**
     * 客户国家编码 为空或当前国家无绑定备货仓查厂直库存；有备货仓查备货库存
     */
    private String countryCode;

}
