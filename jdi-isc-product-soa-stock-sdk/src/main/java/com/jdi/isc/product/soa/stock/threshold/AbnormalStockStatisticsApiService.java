package com.jdi.isc.product.soa.stock.threshold;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.stock.threshold.biz.req.AbnormalStockStatisticsPageApiDTO;

/**
 * @Description: SKU异常库存分类统计api服务
 * @Author: sunlei61
 * @Date: 2025/01/12 20:00
 **/

public interface AbnormalStockStatisticsApiService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<AbnormalStockStatisticsPageApiDTO.Response>> pageSearch(AbnormalStockStatisticsPageApiDTO.Request input);
}
