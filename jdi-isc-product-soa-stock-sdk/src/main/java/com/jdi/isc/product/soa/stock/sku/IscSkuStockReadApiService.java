package com.jdi.isc.product.soa.stock.sku;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.stock.sku.req.StockReadReqDTO;
import com.jdi.isc.product.soa.stock.sku.res.StockReadResDTO;

import java.util.Map;

/**
 * <AUTHOR>
 * @description：SKU维度查询库存
 * @Date 2025-02-26
 */
public interface IscSkuStockReadApiService {

    /**
     * 查询国内库存
     * @param reqDTO 库存查询入参。
     * @return 包含股票信息的响应对象。
     */
    DataResponse<Map<Long, StockReadResDTO>> queryCnStock(StockReadReqDTO reqDTO);
}
