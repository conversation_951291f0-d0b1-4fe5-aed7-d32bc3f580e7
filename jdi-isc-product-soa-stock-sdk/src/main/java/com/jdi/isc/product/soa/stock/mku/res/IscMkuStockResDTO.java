package com.jdi.isc.product.soa.stock.mku.res;


import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description：库存返回信息
 * @Date 2025-02-27
 */
@Data
public class IscMkuStockResDTO {
    /**
     * MKU ID
     */
    private Long mkuId;
    /** 现货库存数量*/
    private Long num;
    /** 库存状态 33 有货 34 无货*/
    private Integer stockStateType;

    /** 可用库存*/
    private Long availableStock;
    /** 预占库存*/
    private Long occupy;
    /**在途库存**/
    private Long transitStock;
    /**
     * 是否在途可售标志 0 不可 1 可以
     */
    private Integer onWaySale;
}
