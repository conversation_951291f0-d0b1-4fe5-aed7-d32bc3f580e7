package com.jdi.isc.product.soa.stock.mku;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.stock.mku.req.IscMkuStockReadReqDTO;
import com.jdi.isc.product.soa.stock.mku.res.IscMkuStockResDTO;
import com.jdi.isc.product.soa.stock.validation.MkuStockValidGroup;
import com.jdi.isc.product.soa.stock.validation.SkuStockValidGroup;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description：查询MKU库存数量
 * @Date 2025-02-26
 */
public interface IscMkuStockReadApiService {

    /**
     * 下单专用
     * 查询mku库存数量
     */
    @Validated(MkuStockValidGroup.queryMkuStockForOrder.class)
    DataResponse<Map<Long, IscMkuStockResDTO>> getMkuRealStock(IscMkuStockReadReqDTO req);

    /**
     * 商详专用
     * 查询mku库存数量
     */
    @Validated(MkuStockValidGroup.queryMkuStockForOrder.class)
    DataResponse<Map<Long, IscMkuStockResDTO>> getMkuStock(IscMkuStockReadReqDTO input);

    /**
     * 国际工鼎专用
     * 查询mku库存数量
     */
    @Validated(MkuStockValidGroup.queryMkuStockForGongDing.class)
    DataResponse<Map<Long, IscMkuStockResDTO>> getMkuStockByCountry(IscMkuStockReadReqDTO input);

}
