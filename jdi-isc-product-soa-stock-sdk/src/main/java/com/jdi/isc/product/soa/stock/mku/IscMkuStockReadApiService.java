package com.jdi.isc.product.soa.stock.mku;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.stock.mku.req.IscMkuStockReadReqDTO;
import com.jdi.isc.product.soa.stock.mku.res.IscMkuStockResDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description：查询MKU库存数量
 * @Date 2025-02-26
 */
public interface IscMkuStockReadApiService {

    /**
     * 下单专用
     * 查询mku库存数量
     */
    DataResponse<Map<Long, IscMkuStockResDTO>> getMkuRealStock(IscMkuStockReadReqDTO req);

    /**
     * 商详专用
     * 查询mku库存数量
     */
    DataResponse<Map<Long, IscMkuStockResDTO>> getMkuStock(IscMkuStockReadReqDTO input);
}
