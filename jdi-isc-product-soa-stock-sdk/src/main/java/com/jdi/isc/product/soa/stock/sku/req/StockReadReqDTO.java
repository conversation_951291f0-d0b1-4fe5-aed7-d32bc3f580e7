package com.jdi.isc.product.soa.stock.sku.req;


import com.jdi.isc.product.soa.stock.validation.SkuStockValidGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 库存查询请求参数
 * @Date 2025-02-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockReadReqDTO {
    /**
     * 商品列表
     */
    @NotNull(message = "stockItems库存请求信息不能为空",groups = {SkuStockValidGroup.queryCnStock.class})
    @Valid
    private List<StockItemReqDTO> stockItems;
    /**
     * 客户编码
     */
    //@NotNull(message = "clientCode",groups = {SkuStockValidGroup.queryCnStock.class})
    private String clientCode;
    /**
     * 国家编码
     */
    private String countryCode;
}
