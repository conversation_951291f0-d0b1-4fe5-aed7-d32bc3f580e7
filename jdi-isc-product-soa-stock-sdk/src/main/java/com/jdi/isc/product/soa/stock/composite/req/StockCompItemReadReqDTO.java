package com.jdi.isc.product.soa.stock.composite.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 库存管理操作实体
 *
 * <AUTHOR>
 * @date 2025/7/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockCompItemReadReqDTO {

    /**
     * mkuId
     */
    @NotNull(message = "mkuId不能为空")
    private Long mkuId;

    /**
     * 国际skuId
     */
    @NotNull(message = "skuId不能为空")
    private Long skuId;

    /**
     * 请求数量
     */
    @NotNull(message = "num不能为空")
    private Long num;


    /**
     * 仓库ID
     */
    @NotNull(message = "备货仓ID不能为空")
    private String warehouseId;
}
