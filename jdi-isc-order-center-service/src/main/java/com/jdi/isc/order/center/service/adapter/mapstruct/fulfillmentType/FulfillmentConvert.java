package com.jdi.isc.order.center.service.adapter.mapstruct.fulfillmentType;

import com.jdi.isc.fulfillment.soa.api.time.biz.OrderFulfillmentTimeApiDTO;
import com.jdi.isc.fulfillment.soa.api.time.enums.FulfillmentTypeEnum;
import com.jdi.isc.order.center.api.constants.OrderTypeConstant;
import com.jdi.isc.order.center.domain.enums.order.OrderModelTypeEnum;
import com.jdi.isc.order.center.domain.enums.purchase.PurchaseModelEnum;
import com.jdi.isc.order.center.domain.fulfillment.po.SubmitOrderFulfillmentPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface FulfillmentConvert {

    public static FulfillmentConvert INSTANCE = Mappers.getMapper(FulfillmentConvert.class);

    default FulfillmentTypeEnum orderType2FulfillmentType(int orderTypeConstant, int purchaseModel){
        if(PurchaseModelEnum.STOCK_UP.getCode().equals(purchaseModel)){
            return FulfillmentTypeEnum.STOCK_WAREHOUSE;
        }
        if(OrderTypeConstant.LOCAL == orderTypeConstant){
            return FulfillmentTypeEnum.LOCAL_DIRECT;
        }
        if(OrderTypeConstant.CROSS_BORDER == orderTypeConstant){
            return FulfillmentTypeEnum.CROSS_BORDER_DIRECT;
        }
        return null;
    }

    default FulfillmentTypeEnum orderType2FulfilmentType(OrderModelTypeEnum orderModelTypeEnum){
        switch (orderModelTypeEnum){
            case LOCAL_DELIVERY:
                return FulfillmentTypeEnum.LOCAL_DIRECT;
            case CROSS_BORDER_DELIVERY:
                return FulfillmentTypeEnum.CROSS_BORDER_DIRECT;
            case STOCK_UP_DELIVERY:
                return FulfillmentTypeEnum.STOCK_WAREHOUSE;
        }
        return null;
    }

    @Mappings({
            @Mapping(target = "timeRuleVersion", source = "ffRelationId"),
            @Mapping(target = "minDeliveryDays", source = "timeLimitMin"),
            @Mapping(target = "maxDeliveryDays", source = "timeLimitMax"),
    })
    SubmitOrderFulfillmentPO response2SubmitOrderFulfillmentPO(OrderFulfillmentTimeApiDTO.Response response);

    default SubmitOrderFulfillmentPO responseMap2SubmitOrderFulfillmentPO(Map<String, OrderFulfillmentTimeApiDTO.Response> responseMap){
        if(responseMap == null || responseMap.isEmpty()){
            return null;
        }
        SubmitOrderFulfillmentPO submitOrderFulfillmentPO = new SubmitOrderFulfillmentPO();
        submitOrderFulfillmentPO.setFulfillmentMap(new HashMap<>());
        responseMap.forEach((k, v) -> {
            submitOrderFulfillmentPO.getFulfillmentMap().put(k, response2SubmitOrderFulfillmentPO(v));
        });
        return submitOrderFulfillmentPO;
    }
}
