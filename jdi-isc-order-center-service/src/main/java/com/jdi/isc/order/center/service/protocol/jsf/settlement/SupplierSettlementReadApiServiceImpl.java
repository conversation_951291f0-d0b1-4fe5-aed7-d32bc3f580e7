package com.jdi.isc.order.center.service.protocol.jsf.settlement;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.isc.order.center.api.common.OperateApiDTO;
import com.jdi.isc.order.center.api.settlement.SupplierSettlementReadApiService;
import com.jdi.isc.order.center.api.settlement.biz.PurchaseSettlementDTO;
import com.jdi.isc.order.center.api.settlement.biz.SupplierSettlementDTO;
import com.jdi.isc.order.center.api.settlement.biz.SupplierSettlementDetailDTO;
import com.jdi.isc.order.center.api.settlement.biz.SupplierSettlementStatusCountDTO;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.common.enums.FileTypeEnum;
import com.jdi.isc.order.center.common.utils.S3Utils;
import com.jdi.isc.order.center.domain.config.OrderDuccConfig;
import com.jdi.isc.order.center.domain.enums.settlement.OpStatusEnum;
import com.jdi.isc.order.center.domain.enums.settlement.PayStatusEnum;
import com.jdi.isc.order.center.domain.enums.settlement.SettlementButtonEnum;
import com.jdi.isc.order.center.domain.enums.settlement.SupplierConfirmStatusEnum;
import com.jdi.isc.order.center.domain.invoice.purchaseOrder.po.PurchaseInvoicePO;
import com.jdi.isc.order.center.domain.settlement.biz.SupplierSettlementDetailExportVO;
import com.jdi.isc.order.center.domain.settlement.biz.SupplierSettlementPageVO;
import com.jdi.isc.order.center.domain.settlement.biz.SupplierSettlementVO;
import com.jdi.isc.order.center.domain.settlement.po.SupplierSettlementsDetailPo;
import com.jdi.isc.order.center.rpc.customs.CustomsConfigRpcService;
import com.jdi.isc.order.center.service.adapter.mapstruct.settlement.SupplierSettlementConvert;
import com.jdi.isc.order.center.service.atomic.invoice.PurchaseInvoiceAtomicService;
import com.jdi.isc.order.center.service.atomic.settlement.SupplierSettlementsDetailAtomicService;
import com.jdi.isc.order.center.service.manage.settlement.SupplierSettlementsBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
@Slf4j
public class SupplierSettlementReadApiServiceImpl implements SupplierSettlementReadApiService {


    @Autowired
    private SupplierSettlementsBaseService supplierSettlementsBaseService;

    @Autowired
    private SupplierSettlementsDetailAtomicService supplierSettlementsDetailAtomicService;

    @Autowired
    private OrderDuccConfig orderDuccConfig;

    @Autowired
    private CustomsConfigRpcService customsConfigRpcService;

    @Autowired
    private PurchaseInvoiceAtomicService purchaseInvoiceAtomicService;

    @Resource
    private S3Utils s3Utils;

    public final static String RESULT_FOLDER = "jdi-intl/el1/我的结算单";

    private final static Integer INVOICE_UPLOADED = 1;
    private final static Integer INVOICE_REJECT = -1;

    private final static Integer SETTLEMENT_PENDING =0;

    @Override
    public DataResponse<PageInfo<PurchaseSettlementDTO>> purchaseSettlementPage(SupplierSettlementDTO settlementDTO) {
        try {
            SupplierSettlementPageVO req = SupplierSettlementConvert.INSTANCE.supplierSettlementDto2Req(settlementDTO);
            if (StringUtils.isNotBlank(settlementDTO.getSettlementId())) {
                List<SupplierSettlementsDetailPo> settlementsDetailPos = supplierSettlementsDetailAtomicService.querySettlementsDetailPOListByIds(Collections.singletonList(settlementDTO.getSettlementId()));
                //无满足条件直接返回
                if (CollectionUtils.isEmpty(settlementsDetailPos)) {
                    PageInfo<PurchaseSettlementDTO> pageInfo = new PageInfo<>();
                    return DataResponse.success(pageInfo);
                }
                //根据采购单id集合查询
                Set<String> pIds = settlementsDetailPos.stream().map(SupplierSettlementsDetailPo::getPurchaseOrderId).collect(Collectors.toSet());
                //得到交集
                if(req.getPurchaseOrderIds()!=null){
                    req.getPurchaseOrderIds().retainAll(pIds);
                    if(req.getPurchaseOrderIds().isEmpty()){
                        PageInfo<PurchaseSettlementDTO> pageInfo = new PageInfo<>();
                        return DataResponse.success(pageInfo);
                    }
                }else {
                    req.setPurchaseOrderIds(pIds);
                }
            }

            if (Objects.nonNull(settlementDTO.getSupplierConfirmStatus())) {
                if(SupplierConfirmStatusEnum.PAY_FINISH.getStatue() == settlementDTO.getSupplierConfirmStatus()){
                    req.setSupplierConfirmStatus(SupplierConfirmStatusEnum.CONFIRMED.getStatue());
                    req.setPayStatus(PayStatusEnum.PAID.getStatue());
                }
                if(SupplierConfirmStatusEnum.CONFIRMED.getStatue() == settlementDTO.getSupplierConfirmStatus()){
                    req.setSupplierConfirmStatus(SupplierConfirmStatusEnum.CONFIRMED.getStatue());
                    req.setPayStatus(null);
                    req.setNoPayStatus(true);
                }
            }

            //发票上传状态处理
            if (Constant.DEFAULT_INVOICE_APPROVE_STATUS.equals(settlementDTO.getInvoiceApprovalStatus())){
                req.setQueryCanUploadInvoice(true);
                req.setInvoiceApprovalStatus(null);
            }

            req.setPurchaseOrderStatusSet(orderDuccConfig.getPurchaseInvoiceCountryShowStatus(req.getCountryCode()));

            log.info("SupplierSettlementReadApiServiceImpl#purchaseSettlementPage req:{}", JSON.toJSONString(req));
            PageInfo<SupplierSettlementVO> page = supplierSettlementsBaseService.purchaseSettlementPage(req);
            PageInfo<PurchaseSettlementDTO> pageInfo = SupplierSettlementConvert.INSTANCE.supplierSettlementPageVo2Dto(page);


            //操作状态集合
            if (CollectionUtils.isNotEmpty(pageInfo.getRecords())) {

                Set<String> purchaseOrderIds = pageInfo.getRecords().stream().map(PurchaseSettlementDTO::getPurchaseOrderId).collect(Collectors.toSet());
                List<PurchaseInvoicePO> purchaseInvoicePOList = purchaseInvoiceAtomicService.getPurchaseInvoicePOListByPoId(purchaseOrderIds);
                Map<String, List<PurchaseInvoicePO>> purchaseInvoicePOMap = purchaseInvoicePOList.stream().collect(Collectors.groupingBy(PurchaseInvoicePO::getPurchaseOrderId));

                pageInfo.getRecords().forEach(r ->{
                    List<PurchaseInvoicePO> purchaseInvoicePOs = purchaseInvoicePOMap.get(r.getPurchaseOrderId());
                    if(CollectionUtils.isNotEmpty(purchaseInvoicePOs)){
                        Long invoiceUploadTime = purchaseInvoicePOs.stream().map(PurchaseInvoicePO::getInvoiceUploadTime).filter(Objects::nonNull).max(Long::compareTo).orElse(null);
                        r.setInvoiceUploadTime(invoiceUploadTime);
                        r.setInvoiceNum(purchaseInvoicePOs.size());
                    }

                    setOperateList(r,true);

                    if (Objects.isNull(r.getInvoiceApprovalStatus())) {
                        r.setInvoiceApprovalStatus(Constant.DEFAULT_INVOICE_STATUS);
                    }
                });
            }


            return DataResponse.success(pageInfo);
        } catch (Exception e) {
            log.error("SupplierSettlementReadApiServiceImpl error,param:{},e:", JSON.toJSONString(settlementDTO),e);
            return DataResponse.error(e.getMessage());
        }


    }

    @Override
    public DataResponse<SupplierSettlementDetailDTO> settlementDetail(SupplierSettlementDTO settlementReq) {
        if (Objects.isNull(settlementReq) || StringUtils.isBlank(settlementReq.getSettlementId())) {
            return DataResponse.error("settlementId is not null");
        }
        SupplierSettlementDetailDTO detailDTOS = supplierSettlementsBaseService.settlementDetail(settlementReq);
        return DataResponse.success(detailDTOS);
    }


    @Override
    public DataResponse<PageInfo<SupplierSettlementDetailDTO>> supplierSettlementPage(SupplierSettlementDTO settlementReq) {
        PageInfo<SupplierSettlementDetailDTO> pageInfo = supplierSettlementsBaseService.supplierSettlementPage(settlementReq);
        log.info("supplierSettlementPage#pageInfo:{}", JSON.toJSONString(pageInfo));

        //操作状态集合
        if (CollectionUtils.isNotEmpty(pageInfo.getRecords())) {
            Set<String> purchaseOrderIds = pageInfo.getRecords().stream()
                    .flatMap(record -> Optional.ofNullable(record.getPurchaseSettlementList())
                            .map(List::stream)
                            .orElseGet(Stream::empty))
                    .map(PurchaseSettlementDTO::getPurchaseOrderId)
                    .collect(Collectors.toSet());
            
            if(!purchaseOrderIds.isEmpty()){
                List<PurchaseInvoicePO> purchaseInvoicePOList = purchaseInvoiceAtomicService.getPurchaseInvoicePOListByPoId(purchaseOrderIds);
                Map<String, List<PurchaseInvoicePO>> purchaseInvoicePOMap = purchaseInvoicePOList.stream().collect(Collectors.groupingBy(PurchaseInvoicePO::getPurchaseOrderId));
                log.info("supplierSettlementPage#purchaseInvoicePOMap{}",purchaseInvoicePOMap);
                pageInfo.getRecords().forEach(r ->{
                    if (CollectionUtils.isNotEmpty(r.getPurchaseSettlementList())) {
                        r.getPurchaseSettlementList().forEach(p -> {
                            List<PurchaseInvoicePO> purchaseInvoicePOs = purchaseInvoicePOMap.get(p.getPurchaseOrderId());
                            if(CollectionUtils.isNotEmpty(purchaseInvoicePOs)){
                                //发票上传时间不准确
                                Long invoiceUploadTime = purchaseInvoicePOs.stream().map(PurchaseInvoicePO::getInvoiceUploadTime).filter(Objects::nonNull).max(Long::compareTo).orElse(null);
                                p.setInvoiceUploadTime(invoiceUploadTime);
                                p.setInvoiceNum(purchaseInvoicePOs.size());
                            }

                            setOperateList(p,false);
                        } );
                    }

                });
            }

        }

        return DataResponse.success(pageInfo);
    }

    @Override
    public DataResponse<SupplierSettlementStatusCountDTO> queryPurchaseSettlementStatusCountMap(SupplierSettlementDTO settlementReq) {
        if(StringUtils.isBlank(settlementReq.getSupplierCode()) && StringUtils.isBlank(settlementReq.getCountryCode())){
            return DataResponse.error(DataResponseCode.PARAM_ERROR.getCode(),"供应商编码和结算国家不能同时为空");
        }

        return supplierSettlementsBaseService.queryPurchaseSettlementStatusCountMap(settlementReq);
    }

    @Override
    public DataResponse<String> querySettlementDetailExport(SupplierSettlementDTO supplierSettlementDTO) {
        DataResponse<SupplierSettlementDetailDTO> dataResponse = this.settlementDetail(supplierSettlementDTO);
        if(null == dataResponse.getData()){
            return DataResponse.error(DataResponseCode.PARAM_ERROR.getCode(), DataResponseCode.PARAM_ERROR.getMessage());
        }

        // 导出数据组装
        SupplierSettlementDetailDTO settlementDetailDTO = dataResponse.getData();
        List<SupplierSettlementDetailExportVO> supplierSettlementDetailExportVOList = new ArrayList<>();

        settlementDetailDTO.getPurchaseSettlementList().forEach(purchaseOrder -> {
            purchaseOrder.getPurchaseOrderWareVOS().forEach(purchaseOrderWare -> {
                SupplierSettlementDetailExportVO supplierSettlementDetailExportVO = new SupplierSettlementDetailExportVO();
                supplierSettlementDetailExportVO.setRequestId(settlementDetailDTO.getRequestId());
                supplierSettlementDetailExportVO.setAmount(settlementDetailDTO.getAmount());
                supplierSettlementDetailExportVO.setCurrency(settlementDetailDTO.getCurrency());

                SupplierConfirmStatusEnum supplierConfirmStatusEnum = SupplierConfirmStatusEnum.getEnumByStatus(settlementDetailDTO.getSupplierConfirmStatus());
                if(supplierConfirmStatusEnum != null){
                    supplierSettlementDetailExportVO.setSupplierConfirmStatusStr(supplierConfirmStatusEnum.getShowDesc());
                }

                OpStatusEnum opStatusEnum = OpStatusEnum.getEnumByStatus(settlementDetailDTO.getOpStatus());
                if(opStatusEnum != null){
                    supplierSettlementDetailExportVO.setPayStatusStr(opStatusEnum.getShowDesc());
                }

                supplierSettlementDetailExportVO.setPayTime(settlementDetailDTO.getPayTime());
                supplierSettlementDetailExportVO.setOuName(settlementDetailDTO.getOuName());
                supplierSettlementDetailExportVO.setPurchaseOrderId(purchaseOrder.getPurchaseOrderId());
                supplierSettlementDetailExportVO.setParentPurchaseOrderId(purchaseOrder.getParentPurchaseOrderId());
                supplierSettlementDetailExportVO.setPurchaseTotalPrice(purchaseOrder.getPurchaseTotalPrice());
                supplierSettlementDetailExportVO.setWaresPurchaseTotalPrice(purchaseOrder.getWaresPurchaseTotalPrice());
                supplierSettlementDetailExportVO.setSkuId(purchaseOrderWare.getSkuId());
                supplierSettlementDetailExportVO.setSkuNum(purchaseOrderWare.getSkuNum());
                supplierSettlementDetailExportVO.setSaleUnit(purchaseOrderWare.getSaleUnit());
                supplierSettlementDetailExportVO.setIncludeTaxPrice(purchaseOrderWare.getIncludeTaxPrice());
                supplierSettlementDetailExportVO.setValueAddedTax(purchaseOrderWare.getValueAddedTax());
                supplierSettlementDetailExportVO.setValueAddedTaxRate(purchaseOrderWare.getValueAddedTaxRate());
                supplierSettlementDetailExportVO.setPurchasePrice(purchaseOrderWare.getPurchasePrice());
                supplierSettlementDetailExportVO.setSkuAllPrice(purchaseOrderWare.getSkuAllPrice());
                supplierSettlementDetailExportVO.setSkuSettlementAmount(purchaseOrderWare.getAmount());
                supplierSettlementDetailExportVOList.add(supplierSettlementDetailExportVO);
            });
        });

        String uploadUrl = exportDTO(supplierSettlementDetailExportVOList,supplierSettlementDTO.getSettlementId());
        return DataResponse.success(uploadUrl);
    }


    private String exportDTO(List<SupplierSettlementDetailExportVO> supplierSettlementDetailExportVOList, String settlementId) {
        ByteArrayOutputStream targetOutputStream = new ByteArrayOutputStream();
        try (ExcelWriter excelWriter = EasyExcel.write(targetOutputStream, SupplierSettlementDetailExportVO.class).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.write(supplierSettlementDetailExportVOList, writeSheet);
        }

        String resultName = RESULT_FOLDER + Constant.UNDER_LINE +settlementId +Constant.UNDER_LINE+ System.currentTimeMillis() + ".xlsx";
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(targetOutputStream.toByteArray())) {
            return s3Utils.upload(inputStream, FileTypeEnum.BATCH_FILE.getPath(), resultName);
        } catch (Exception e) {
            log.error("com.jdi.isc.order.center.service.protocol.jsf.settlement.SupplierSettlementReadApiServiceImpl.exportDTO", e);
        }
        return null;
    }



    private void setOperateList(PurchaseSettlementDTO settlementRes,boolean hasSettle) {
        List<OperateApiDTO> result = new ArrayList<>();

        //已驳回
        if (INVOICE_REJECT.equals(settlementRes.getInvoiceApprovalStatus())) {
            OperateApiDTO operateVO = new OperateApiDTO();
            if(settlementRes.getInvoiceNum()==null||settlementRes.getInvoiceNum()==1){
                operateVO.setOperate(SettlementButtonEnum.UPLOAD_INVOICE.getValue());
                operateVO.setOperateName(SettlementButtonEnum.UPLOAD_INVOICE.getDesc());
            }else {
                operateVO.setOperate(SettlementButtonEnum.EDIT_INVOICE.getValue());
                operateVO.setOperateName(SettlementButtonEnum.EDIT_INVOICE.getDesc());
            }

            result.add(operateVO);
            OperateApiDTO operateVO1 = new OperateApiDTO();
            operateVO1.setOperate(SettlementButtonEnum.VIEW_INVOICE.getValue());
            operateVO1.setOperateName(SettlementButtonEnum.VIEW_INVOICE.getDesc());
            result.add(operateVO1);
        }else{
            //未上传
            OperateApiDTO operateVO = new OperateApiDTO();
            if (!INVOICE_UPLOADED.equals(settlementRes.getInvoiceUploadStatus())) {
                operateVO.setOperate(SettlementButtonEnum.UPLOAD_INVOICE.getValue());
                operateVO.setOperateName(SettlementButtonEnum.UPLOAD_INVOICE.getDesc());
            }else{
                //待审核 已审核
                operateVO.setOperate(SettlementButtonEnum.VIEW_INVOICE.getValue());
                operateVO.setOperateName(SettlementButtonEnum.VIEW_INVOICE.getDesc());
            }
            result.add(operateVO);

        }

        //采购单结算数据存在，则支持查看详情
        if (Objects.nonNull(settlementRes.getSupplierConfirmStatus()) && hasSettle) {
            OperateApiDTO operateVO = new OperateApiDTO();
            operateVO.setOperate(SettlementButtonEnum.VIEW_SETTLEMENT.getValue());
            operateVO.setOperateName(SettlementButtonEnum.VIEW_SETTLEMENT.getDesc());
            result.add(operateVO);
            OperateApiDTO operateVO1 = new OperateApiDTO();
            operateVO1.setOperate(SettlementButtonEnum.DOWNLOAD_SETTLEMENT.getValue());
            operateVO1.setOperateName(SettlementButtonEnum.DOWNLOAD_SETTLEMENT.getDesc());
            result.add(operateVO1);
        }

        settlementRes.setOperateVOList(result);
    }


}
