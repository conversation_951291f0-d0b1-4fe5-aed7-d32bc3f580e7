package com.jdi.isc.order.center.service.protocol.jsf;

import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.isc.order.center.api.OrderWriteApiService;
import com.jdi.isc.order.center.api.biz.req.*;
import com.jdi.isc.order.center.api.biz.req.SubmitOrderApiReq;
import com.jdi.isc.order.center.api.biz.resp.OrderInfoOrderApiResp;
import com.jdi.isc.order.center.domain.mapstruct.order.OrderUpdateConvert;
import com.jdi.isc.order.center.service.manage.common.impl.BaseServiceImpl;
import com.jdi.isc.order.center.service.manage.order.OrderWriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/26 17:40
 */
@Service
@Slf4j
public class OrderWriteApiServiceImpl extends BaseServiceImpl<OrderInfoOrderApiResp> implements OrderWriteApiService {

    @Autowired
    private OrderWriteService orderWriteService;

    @Value("${error.msg}")
    private String errorMessage;

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public DataResponse<OrderInfoOrderApiResp> submitOrder(SubmitOrderApiReq submitOrderReqDTO) {
        DataResponse<OrderInfoOrderApiResp> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.OrderWriteApiServiceImpl.submitOrder-" + active);
        try {
            dataResponse = orderWriteService.submitOrder(submitOrderReqDTO);
            if(dataResponse.getSuccess() == null || !dataResponse.getSuccess()){
                Profiler.businessAlarm("com.jdi.isc.order.center.service.protocol.jsf.OrderWriteApiServiceImpl.submitOrder." + active, String.format("[提单]pin = %s, errorCode = %s, errorMessage = %s", submitOrderReqDTO.getPin(), dataResponse.getCode(), dataResponse.getMessage()));
            }
            return dataResponse;
        } catch (Exception e){
            log.error("[提单异常]com.jdi.isc.order.center.service.protocol.jsf.OrderWriteApiServiceImpl.submitOrder, submitOrderReqDTO = {}", JSON.toJSONString(submitOrderReqDTO));
            Profiler.functionError(callerInfo);
        } finally {
            log.info("OrderWriteApiServiceImpl.submitOrder, req = {}, result = {}", JSON.toJSONString(submitOrderReqDTO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;

    }

    @Override
    public DataResponse<Boolean> updateOrderStatus(UpdateOrderStatusApiReq updateOrderStatusApiReq){
        return orderWriteService.updateOrder(OrderUpdateConvert.INSTANCE.apiReq2voReq(updateOrderStatusApiReq));
    }

    @Deprecated
    @Override
    public DataResponse<List<OrderInfoOrderApiResp>> splitOrder(UpdateOrderStatusApiReq updateOrderStatusApiReq){
        // 无用的方法
        return DataResponse.success();
//        return orderWriteService.splitOrder(OrderUpdateConvert.INSTANCE.apiReq2voReq(updateOrderStatusApiReq));
    }

    @Override
    public DataResponse<Boolean> confirmReceipt(UpdateOrderStatusApiReq updateOrderStatusApiReq) {
        return orderWriteService.confirmReceipt(OrderUpdateConvert.INSTANCE.apiReq2voReq(updateOrderStatusApiReq));
    }


    @Override
    public DataResponse<Map<String, Map<Long,String>>> checkBatchSubmitOrderParam(BatchSubmitOrderParamDTO paramDTO) {
        return orderWriteService.checkBatchSubmitOrderParam(OrderUpdateConvert.INSTANCE.batchSubmitOrder2Vo(paramDTO));
    }

    @Override
    public DataResponse<OrderInfoOrderApiResp> submitOrderMock(SubmitOrderApiReq submitOrderReqDTO) {
        try{
            DataResponse<OrderInfoOrderApiResp> respDataResponse = orderWriteService.submitOrderMock(submitOrderReqDTO);
            if(respDataResponse.getSuccess() == null || !respDataResponse.getSuccess()){
                Profiler.businessAlarm("com.jdi.isc.order.center.service.protocol.jsf.OrderWriteApiServiceImpl.submitOrderMock." + active, String.format("[提单]pin = %s, errorCode = %s, errorMessage = %s", submitOrderReqDTO.getPin(), respDataResponse.getCode(), respDataResponse.getMessage()));
            }
            return respDataResponse;
        }catch (Exception e){
            log.error("[提单异常]com.jdi.isc.order.center.service.protocol.jsf.OrderWriteApiServiceImpl.submitOrderMock, submitOrderReqDTO = {}", JSON.toJSONString(submitOrderReqDTO));
            return DataResponse.error(DataResponseCode.SYSTEM_ERROR.getCode(), DataResponseCode.SYSTEM_ERROR.getMessage());
        }

    }
}
