package com.jdi.isc.order.center.service.protocol.jsf;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.ApplyOrderApiService;
import com.jdi.isc.order.center.api.common.ApplyOrderApiReq;
import com.jdi.isc.order.center.api.common.ApplyOrderMkuApiReq;
import com.jdi.isc.order.center.api.common.AuditApplyApiReq;
import com.jdi.isc.order.center.domain.applyOrder.biz.ApplyOrderMkuVO;
import com.jdi.isc.order.center.domain.applyOrder.biz.ApplyOrderVO;
import com.jdi.isc.order.center.domain.applyOrder.biz.AuditApplyVO;
import com.jdi.isc.order.center.service.manage.applyOrder.ApplyOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 申请单rpc写服务
 */
@Slf4j
@Service
public class ApplyOrderApiServiceImpl implements ApplyOrderApiService {

    @Resource
    private ApplyOrderService applyOrderService;

    @Override
    public DataResponse<Boolean> submitApplyOrder(ApplyOrderApiReq applyOrderApiReq) {
        ApplyOrderVO target = new ApplyOrderVO();
        BeanUtils.copyProperties(applyOrderApiReq,target);
        List<ApplyOrderMkuVO> list = new ArrayList<>();
        for(ApplyOrderMkuApiReq rpcMku : applyOrderApiReq.getMkuInfo()){
            ApplyOrderMkuVO mku = new ApplyOrderMkuVO();
            BeanUtils.copyProperties(rpcMku,mku);
            list.add(mku);
        }
        target.setMkuInfo(list);
        return applyOrderService.submitApplyOrder(target);
    }

    @Override
    public DataResponse<Boolean> auditApplyOrder(AuditApplyApiReq auditApplyApiReq) {
        AuditApplyVO target = new AuditApplyVO();
        BeanUtils.copyProperties(auditApplyApiReq,target);
        return applyOrderService.auditApplyOrder(target);
    }

    @Override
    public DataResponse<Boolean> submitOrderByApplyOrder(AuditApplyApiReq auditApplyApiReq) {
        AuditApplyVO target = new AuditApplyVO();
        BeanUtils.copyProperties(auditApplyApiReq,target);
        return applyOrderService.submitOrderByApplyOrder(target);
    }

    @Override
    public DataResponse<Boolean> confirmOrderByApplyOrder(AuditApplyApiReq auditApplyApiReq) {
        AuditApplyVO target = new AuditApplyVO();
        BeanUtils.copyProperties(auditApplyApiReq,target);
        return applyOrderService.confirmOrderByApplyOrder(target);
    }

    @Override
    public DataResponse<Boolean> resetApplyOrder(AuditApplyApiReq auditApplyApiReq) {
        return applyOrderService.resetApplyOrder(auditApplyApiReq.getApplyId());
    }
}
