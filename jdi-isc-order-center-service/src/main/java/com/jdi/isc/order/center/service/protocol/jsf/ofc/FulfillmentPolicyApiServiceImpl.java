package com.jdi.isc.order.center.service.protocol.jsf.ofc;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.jdi.yz.core.utils.CollectionUtils;
import com.jd.jdi.yz.core.utils.StringUtils;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.aggregate.read.api.customer.res.CustomerReadResp;
import com.jdi.isc.order.center.api.ofc.FulfillmentPolicyApiService;
import com.jdi.isc.order.center.api.ofc.biz.StoreHouseConsigneeInfoDTO;
import com.jdi.isc.order.center.api.ofc.biz.req.SkuInfoReqDTO;
import com.jdi.isc.order.center.api.ofc.biz.rsp.CustomerSkuDecisionResult;
import com.jdi.isc.order.center.api.ofc.biz.rsp.SkuInfoResDTO;
import com.jdi.isc.order.center.domain.config.OrderDuccConfig;
import com.jdi.isc.order.center.domain.mapstruct.ofc.FulfillmentPolicyConvert;
import com.jdi.isc.order.center.domain.ofc.fulfillmentOrder.biz.MultiWarehouseSkuVO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.DuccConfigContactInfoVO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.DuccConfigIopInvoiceInfoVo;
import com.jdi.isc.order.center.rpc.customer.CustomerRpcService;
import com.jdi.isc.order.center.rpc.lang.LangRpcService;
import com.jdi.isc.order.center.rpc.sku.SkuReadRpcService;
import com.jdi.isc.order.center.service.manage.ofc.CrossStockWarehouseDecisionService;
import com.jdi.isc.product.soa.api.sku.res.SkuFeatureApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/12
 * @Description 履约决策信息查询
 */
@Service
@Slf4j
public class FulfillmentPolicyApiServiceImpl implements FulfillmentPolicyApiService {

    @Resource
    private SkuReadRpcService skuReadRpcService;

    @Resource
    private LangRpcService langRpcService;

    @Resource
    private CrossStockWarehouseDecisionService crossStockWarehouseDecisionService;

    @Autowired
    private OrderDuccConfig orderDuccConfig;

    @Resource
    private CustomerRpcService customerRpcService;

    @Override
    public DataResponse<CustomerSkuDecisionResult> querySkuMultiWarehouseDecisionByCountry(String countryCode, Set<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return DataResponse.buildError("入参skuId不能为空!");
        }

        CustomerSkuDecisionResult customerSkuDecisionResult = new CustomerSkuDecisionResult();
        try {
            //商品履约决策
            SkuInfoReqDTO skuInfoReqDTO = new SkuInfoReqDTO();
            skuInfoReqDTO.setCountryCode(countryCode);
            skuInfoReqDTO.setSkuIds(skuIds);
            DataResponse<List<SkuInfoResDTO>> skuFulfillmentDecisionResult = queryMultiWarehouseFulfillmentDecisionBySku(skuInfoReqDTO);
            if (!skuFulfillmentDecisionResult.getSuccess() || skuFulfillmentDecisionResult.getData() == null) {
                log.error("querySkuMultiWarehouseDecisionByCustomer failed, 商品履约决策失败, skuIds:{}, error:{}", skuIds, skuFulfillmentDecisionResult.getMessage());
                return DataResponse.error("商品履约决策失败！");
            }

            //iop信息获取
            if (StringUtils.isNotBlank(countryCode)) {
                /*
                 *   国家为非必填字段，为空时可做履约决策，但不去查询iop信息
                 */
                DuccConfigIopInvoiceInfoVo duccConfigIopInvoiceInfoVo = orderDuccConfig.iopInvoiceInfoVo(countryCode, null);
                customerSkuDecisionResult.setIopClientId(duccConfigIopInvoiceInfoVo.getClientId());
                customerSkuDecisionResult.setCountryCode(countryCode);
                customerSkuDecisionResult.setIopPin(duccConfigIopInvoiceInfoVo.getIopPin());
            }

            //商品按照集运仓归堆
            Map<String, List<SkuInfoResDTO>> skuFulfillmentDecisionMap = preProcessCrossSkuSplit(skuFulfillmentDecisionResult);
            customerSkuDecisionResult.setSkuFulfillmentDecisionResult(skuFulfillmentDecisionMap);
            return DataResponse.success(customerSkuDecisionResult);
        } finally {
            log.info("querySkuMultiWarehouseDecisionByCountry countryCode:{}, skuIds:{}, result:{}", countryCode, skuIds, JSON.toJSONString(customerSkuDecisionResult));
        }
    }

    @Override
    public DataResponse<CustomerSkuDecisionResult> querySkuMultiWarehouseDecisionByCustomer(String clientCode, Set<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds) || StringUtils.isBlank(clientCode)) {
            return DataResponse.buildError("入参不能为空!");
        }

        CustomerSkuDecisionResult customerSkuDecisionResult = new CustomerSkuDecisionResult();
        try {
            //客户信息获取
            Map<String, CustomerReadResp> clientMap = customerRpcService.queryCustomerByClientCodes(Sets.newHashSet(clientCode));
            if (org.springframework.util.CollectionUtils.isEmpty(clientMap)) {
                log.error("querySkuMultiWarehouseDecisionByCustomer failed, clientCode:{} query customer is null", clientCode);
                return DataResponse.error("客户信息查询为空！");
            }
            CustomerReadResp customerReadResp = clientMap.get(clientCode);

            //客户对应的iop信息获取
            DuccConfigIopInvoiceInfoVo duccConfigIopInvoiceInfoVo = orderDuccConfig.iopInvoiceInfoVo(customerReadResp.getCountry(), customerReadResp.getContractCode());
            if (duccConfigIopInvoiceInfoVo == null) {
                log.error("querySkuMultiWarehouseDecisionByCustomer failed, clientCode:{} query configIopInfo is null", clientCode);
                return DataResponse.error("客户对应的iop信息查询为空！");
            }

            //商品履约决策
            SkuInfoReqDTO skuInfoReqDTO = new SkuInfoReqDTO();
            skuInfoReqDTO.setCountryCode(customerReadResp.getCountry());
            skuInfoReqDTO.setContractCode(customerReadResp.getContractCode());
            skuInfoReqDTO.setSkuIds(skuIds);
            DataResponse<List<SkuInfoResDTO>> skuFulfillmentDecisionResult = queryMultiWarehouseFulfillmentDecisionBySku(skuInfoReqDTO);
            if (!skuFulfillmentDecisionResult.getSuccess() || skuFulfillmentDecisionResult.getData() == null) {
                log.error("querySkuMultiWarehouseDecisionByCustomer failed, 商品履约决策失败, skuIds:{}, error:{}", skuIds, skuFulfillmentDecisionResult.getMessage());
                return DataResponse.error("商品履约决策失败！");
            }

            customerSkuDecisionResult.setIopClientId(duccConfigIopInvoiceInfoVo.getClientId());
            customerSkuDecisionResult.setCountryCode(customerReadResp.getCountry());
            customerSkuDecisionResult.setIopPin(duccConfigIopInvoiceInfoVo.getIopPin());

            //商品按照集运仓归堆
            Map<String, List<SkuInfoResDTO>> skuFulfillmentDecisionMap = preProcessCrossSkuSplit(skuFulfillmentDecisionResult);
            customerSkuDecisionResult.setSkuFulfillmentDecisionResult(skuFulfillmentDecisionMap);

            return DataResponse.success(customerSkuDecisionResult);
        } finally {
            log.info("querySkuMultiWarehouseDecisionByCustomer clientCode:{}, skuIds:{}, result:{}", clientCode, skuIds, JSON.toJSONString(customerSkuDecisionResult));
        }
    }


    /**
     * 跨境商品预拆分
     *
     * @param skuFulfillmentDecisionResult
     * @return
     */
    private Map<String, List<SkuInfoResDTO>> preProcessCrossSkuSplit(DataResponse<List<SkuInfoResDTO>> skuFulfillmentDecisionResult) {
        List<SkuInfoResDTO> skuInfoResDTOList = skuFulfillmentDecisionResult.getData();
        Map<String, List<SkuInfoResDTO>> skuFulfillmentDecisionMap = skuInfoResDTOList.stream()
                .collect(Collectors.groupingBy(SkuInfoResDTO::getStoreHouseId));

        return skuFulfillmentDecisionMap;
    }


    /**
     * 获取客户 SKU 决策结果。
     *
     * @param iopClientId                  iop客户id。
     * @param customerReadResp             客户读取响应。
     * @param skuFulfillmentDecisionResult SKU 满足决策结果。
     * @return 包含客户 SKU 决策结果的 DataResponse 对象。
     */
    private CustomerSkuDecisionResult getCustomerSkuDecisionResult(String iopClientId, CustomerReadResp customerReadResp, DataResponse<List<SkuInfoResDTO>> skuFulfillmentDecisionResult) {
        CustomerSkuDecisionResult customerSkuDecisionResult = new CustomerSkuDecisionResult();
        customerSkuDecisionResult.setIopClientId(iopClientId);
        customerSkuDecisionResult.setCountryCode(customerReadResp.getCountry());
        customerSkuDecisionResult.setIopPin(customerReadResp.getIopPin());

        //商品按照集运仓归堆
        Map<String, List<SkuInfoResDTO>> skuFulfillmentDecisionMap = preProcessCrossSkuSplit(skuFulfillmentDecisionResult);
        customerSkuDecisionResult.setSkuFulfillmentDecisionResult(skuFulfillmentDecisionMap);

        return customerSkuDecisionResult;
    }

    private DataResponse<List<SkuInfoResDTO>> queryMultiWarehouseFulfillmentDecisionBySku(SkuInfoReqDTO skuInfoReqDTO) {

        List<SkuInfoResDTO> skuInfoResDTOList = Collections.emptyList();
        try {
            //商品信息查询
            List<MultiWarehouseSkuVO> skuInfoReadDTOList = queryGjSkuInfoList(skuInfoReqDTO.getSkuIds());
            if (CollectionUtils.isEmpty(skuInfoReadDTOList)) {
                log.error("queryMultiWarehouseFulfillmentDecisionBySku failed, skuIds:{} query result is null", JSON.toJSONString(skuInfoReqDTO.getSkuIds()));
                return DataResponse.error("商品信息查询为空！");
            }

            //多集运仓履约决策
            DataResponse<Boolean> multiDecisionResult = crossStockWarehouseDecisionService.multiWhFulfillmentDecisionForCrossSku(skuInfoReqDTO.getCountryCode(), skuInfoReqDTO.getContractCode(), skuInfoReadDTOList);
            if (!multiDecisionResult.getSuccess()) {
                log.error("queryMultiWarehouseFulfillmentDecisionBySku multiWhFulfillmentDecision failed, sku:{}, result:{}", JSON.toJSONString(skuInfoReadDTOList), multiDecisionResult);
                return DataResponse.buildError(multiDecisionResult.getCode(), multiDecisionResult.getMessage());
            }

            //通过多仓结果补全原有sku信息
            skuInfoResDTOList = Lists.newArrayListWithExpectedSize(skuInfoReadDTOList.size());
            for (MultiWarehouseSkuVO skuInfoReadDTO : skuInfoReadDTOList) {
                SkuInfoResDTO skuInfoResDTO = FulfillmentPolicyConvert.INSTANCE.sku2SkuInfoResDto(skuInfoReadDTO);

                //设置收货人地址
                String storehouseCode = skuInfoReadDTO.getStoreHouseId();
                if (StringUtils.isNotBlank(storehouseCode)) {
                    DuccConfigContactInfoVO duccConfigContactInfoVO = orderDuccConfig.getStorehouseConfigByCode(storehouseCode);

                    if (duccConfigContactInfoVO != null) {
                        StoreHouseConsigneeInfoDTO storeHouseConsigneeInfoDTO = new StoreHouseConsigneeInfoDTO();
                        storeHouseConsigneeInfoDTO.setConsigneeProvinceId(Long.valueOf(duccConfigContactInfoVO.getProvince()));
                        storeHouseConsigneeInfoDTO.setConsigneeCityId(Long.valueOf(duccConfigContactInfoVO.getCity()));
                        storeHouseConsigneeInfoDTO.setConsigneeCountyId(Long.valueOf(duccConfigContactInfoVO.getCounty()));
                        storeHouseConsigneeInfoDTO.setConsigneeTownId(Long.valueOf(duccConfigContactInfoVO.getTown()));
                        storeHouseConsigneeInfoDTO.setConsigneeAddress(duccConfigContactInfoVO.getAddress());
                        storeHouseConsigneeInfoDTO.setConsigneeName(duccConfigContactInfoVO.getContacts());
                        storeHouseConsigneeInfoDTO.setConsigneeZip(duccConfigContactInfoVO.getZipCode());
                        storeHouseConsigneeInfoDTO.setConsigneePhone(duccConfigContactInfoVO.getPhone());
                        storeHouseConsigneeInfoDTO.setConsigneeMobile(duccConfigContactInfoVO.getMobile());

                        skuInfoResDTO.setStoreHouseConsigneeInfoDTO(storeHouseConsigneeInfoDTO);
                    }
                }

                skuInfoResDTOList.add(skuInfoResDTO);
            }
        } finally {
            log.info("queryMultiWarehouseFulfillmentDecisionBySku request:{}, response:{}", JSON.toJSONString(skuInfoReqDTO), JSON.toJSONString(skuInfoResDTOList));
        }
        return DataResponse.success(skuInfoResDTOList);
    }

    /**
     * 通过一组SKU ID查询对应的SKU信息列表
     *
     * @param skuIds SKU ID集合
     * @return 返回一个映射，其中键是SKU ID，值是对应的SKU信息读取响应对象
     */
    @ToolKit(exceptionWrap = true)
    private Map<String, SkuFeatureApiDTO> querySkuInfoList(Set<Long> skuIds) {
        Map<String, SkuFeatureApiDTO> skuInfoReadRespMap = skuReadRpcService.querySkuFeatureInfoList(skuIds);
        if (org.springframework.util.CollectionUtils.isEmpty(skuInfoReadRespMap)) {
            List<String> skuIdList = skuIds.stream().map(String::valueOf).collect(Collectors.toList());
            String errSkuId = String.join(",", skuIdList);
            throw new RuntimeException("商品[" + errSkuId + "]未查询到商品信息");
        }

        if (skuIds.size() != skuInfoReadRespMap.size()) {
            List<String> errSkuIdList = new ArrayList<>();
            for (Long skuId : skuIds) {
                if (!skuInfoReadRespMap.containsKey(String.valueOf(skuId))) {
                    errSkuIdList.add(String.valueOf(skuId));
                }
            }
            String errSkuId = String.join(",", errSkuIdList);
            throw new RuntimeException("商品[" + errSkuId + "]未查询到商品信息");
        }
        return skuInfoReadRespMap;
    }

    /**
     * 根据给定的商品SKU ID集合，查询并转换为多仓库SKU信息列表。
     *
     * @param gjSkuId 商品SKU ID集合
     * @return 多仓库SKU信息列表
     */
    private List<MultiWarehouseSkuVO> queryGjSkuInfoList(Set<Long> gjSkuId) {
        Map<String, SkuFeatureApiDTO> gjSkuInfoReadRespMap = querySkuInfoList(gjSkuId);
        List<SkuFeatureApiDTO> gjSkuInfoReadRespList = new ArrayList<>(gjSkuInfoReadRespMap.values());

        List<MultiWarehouseSkuVO> skuInfoReadDTOList = gjSkuInfoReadRespList.stream()
                .map(FulfillmentPolicyConvert.INSTANCE::skuFeature2MultiWarehouseSkuVO)
                .collect(Collectors.toList());
        return skuInfoReadDTOList;
    }
}
