package com.jdi.isc.order.center.service.protocol.jsf.ofc;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.ofc.OfcWriteManageApiService;
import com.jdi.isc.order.center.api.ofc.biz.req.ManualSplitOrderApiReq;
import com.jdi.isc.order.center.domain.mapstruct.ofc.FulfillmentOrderConvert;
import com.jdi.isc.order.center.domain.ofc.fulfillmentOrder.vo.FulfillmentSplitOrderVO;
import com.jdi.isc.order.center.service.manage.ofc.filfullmentOrder.FulfillmentOrderWriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author：xubing82
 * @date：2025/7/21 10:48
 * @description：ofc管理端服务实现类
 */
@Service
@Slf4j
public class OfcWriteManageApiServiceImpl implements OfcWriteManageApiService {

    @Autowired
    private FulfillmentOrderWriteService fulfillmentOrderWriteService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> splitOfcOrderForOnWayOrder(ManualSplitOrderApiReq manualSplitOrderApiReq) {
        FulfillmentSplitOrderVO fulfillmentSplitOrderVO = FulfillmentOrderConvert.INSTANCE.manualSplitApiReq2voReq(manualSplitOrderApiReq);
        return fulfillmentOrderWriteService.splitFulfillmentOrder(fulfillmentSplitOrderVO);
    }

}
