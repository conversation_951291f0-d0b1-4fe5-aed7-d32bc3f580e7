package com.jdi.isc.order.center.service.protocol.jsf.invoice;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.finance.customerInvoice.CustomerInvoiceApiService;
import com.jdi.isc.order.center.api.finance.customerInvoice.req.CustomerInvoiceSaveReqDTO;
import com.jdi.isc.order.center.domain.invoice.customer.po.CustomerInvoiceApplyPO;
import com.jdi.isc.order.center.domain.invoice.customer.vo.CustomerInvoiceApplySubmitReqVO;
import com.jdi.isc.order.center.service.adapter.mapstruct.invoice.CustomerInvoiceConvert;
import com.jdi.isc.order.center.service.manage.invoice.CustomerInvoiceService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Data
@Service
public class CustomerInvoiceApiServiceImpl implements CustomerInvoiceApiService {

    @Autowired
    private CustomerInvoiceService customerInvoiceService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> saveInvoice(CustomerInvoiceSaveReqDTO customerInvoiceSaveReqVO) {
        CustomerInvoiceApplySubmitReqVO reqVO = CustomerInvoiceConvert.INSTANCE.submitReqDTO2VO(customerInvoiceSaveReqVO);
        DataResponse<CustomerInvoiceApplyPO> dataResponse = customerInvoiceService.submitInvoice(reqVO);
        return dataResponse.getSuccess() ? DataResponse.success() : DataResponse.error(dataResponse.getCode(), dataResponse.getMessage());
    }
}
