package com.jdi.isc.order.center.service.protocol.jsf.orderArchive.impl;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.orderArchive.OrderThirdArchiveApiService;
import com.jdi.isc.order.center.api.orderArchive.biz.OrderThirdArchiveMakeInvoiceApiDTO;
import com.jdi.isc.order.center.api.orderArchive.biz.OrderThirdArchivePageApiDTO;
import com.jdi.isc.order.center.api.orderArchive.req.CatlPreInvoiceOrderQuery;
import com.jdi.isc.order.center.api.orderArchive.req.OrderThirdArchiveSaveUpdateReqApiDTO;
import com.jdi.isc.order.center.api.orderArchive.res.CatlPreInvoiceOrderDTO;
import com.jdi.isc.order.center.api.orderArchive.res.OrderThirdArchiveResApiDTO;
import com.jdi.isc.order.center.domain.order.biz.OrderThirdArchiveMakeInvoiceVO;
import com.jdi.isc.order.center.domain.order.biz.OrderThirdArchivePageVO;
import com.jdi.isc.order.center.domain.orderArchive.req.OrderThirdArchiveReqVO;
import com.jdi.isc.order.center.domain.orderArchive.res.OrderThirdArchiveResVO;
import com.jdi.isc.order.center.service.adapter.mapstruct.orderArchive.OrderThirdArchiveConvert;
import com.jdi.isc.order.center.service.manage.orderArchive.OrderThirdArchiveInvoiceService;
import com.jdi.isc.order.center.service.manage.orderArchive.OrderThirdArchiveManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 订单三方归档api服务实现
 * @Author: zhaojianguo21
 * @Date: 2024/12/27 22:18
 **/

@Slf4j
@Service
public class OrderThirdArchiveApiServiceImpl implements OrderThirdArchiveApiService {

    @Resource
    private OrderThirdArchiveManageService orderThirdArchiveManageService;

    @Resource
    private OrderThirdArchiveInvoiceService orderThirdArchiveInvoiceService;

    @ToolKit
    @Override
    public DataResponse<OrderThirdArchiveResApiDTO> orderArchive(OrderThirdArchiveSaveUpdateReqApiDTO input) {
        DataResponse<OrderThirdArchiveResVO> invokeResult = null;
        try {
            OrderThirdArchiveReqVO reqVO = OrderThirdArchiveConvert.INSTANCE.saveUpdateReqApi2Req(input);
            invokeResult = orderThirdArchiveManageService.orderArchive(reqVO);
            if (!invokeResult.getSuccess()){
               log.warn("orderArchive, invokeResult fail. input={}", JSONObject.toJSONString(input));
               return DataResponse.error(invokeResult.getMessage());
            }

            OrderThirdArchiveResApiDTO responseVo = OrderThirdArchiveConvert.INSTANCE.orderThirdArchiveResVo2ResReqApiDto(invokeResult.getData());
            return DataResponse.success(responseVo);
        } catch (Exception e) {
            log.error("orderArchive exception. input={}", JSONObject.toJSONString(input), e);
            return DataResponse.error("发生异常。");
        }finally {
            log.info("orderArchive end. input={}, invokeResult={}", JSONObject.toJSONString(input), JSONObject.toJSONString(invokeResult));
        }

    }

    @ToolKit
    @Override
    public DataResponse<PageInfo<OrderThirdArchivePageApiDTO.Response>> pageSearch(OrderThirdArchivePageApiDTO.Request input){
        log.info("pageSearch, input={}", JSONObject.toJSONString(input));
        PageInfo<OrderThirdArchivePageVO.Response> invokeResult = null;
        try {
            OrderThirdArchivePageVO.Request reqVO = OrderThirdArchiveConvert.INSTANCE.pageReqApi2PageReqVo(input);
            invokeResult = orderThirdArchiveManageService.pageSearch(reqVO);
            if (null==invokeResult){
                log.warn("pageSearch, invokeResult fail. input={}", JSONObject.toJSONString(input));
                return DataResponse.error("查询失败");
            }

            PageInfo<OrderThirdArchivePageApiDTO.Response> pageInfoRes = OrderThirdArchiveConvert.INSTANCE.pageResVo2PageResApi(invokeResult);
            return DataResponse.success(pageInfoRes);
        } catch (Exception e) {
            log.error("pageSearch exception. input={}", JSONObject.toJSONString(input), e);
            return DataResponse.error("发生异常。");
        }finally {
            log.info("pageSearch end. input={}, invokeResult={}", JSONObject.toJSONString(input), JSONObject.toJSONString(invokeResult));
        }
    }

    /**
     * 根据订单ID集合生成发票
     * @param input 包含订单ID集合的请求对象
     * @return 生成发票的响应对象
     * 此方法由AI生成
     */
    @ToolKit
    @Override
    public DataResponse<OrderThirdArchiveMakeInvoiceApiDTO.Response> makeInvoice(OrderThirdArchiveMakeInvoiceApiDTO.Request input) {
        DataResponse<OrderThirdArchiveMakeInvoiceVO.Response> invokeResult = null;
        try {
            OrderThirdArchiveMakeInvoiceVO.Request reqVO = OrderThirdArchiveConvert.INSTANCE.makeInvoiceReqApi2ReqVo(input);
            invokeResult = orderThirdArchiveInvoiceService.makeInvoice(reqVO);
            if (null==invokeResult || !Boolean.TRUE.equals(invokeResult.getSuccess())){
                log.warn("makeInvoice, invokeResult fail. input={}", JSONObject.toJSONString(input));
                return DataResponse.error("开票失败。"+ (null!=invokeResult?invokeResult.getMessage():"") );
            }

            OrderThirdArchiveMakeInvoiceApiDTO.Response apiRes = OrderThirdArchiveConvert.INSTANCE.makeInvoiceResVo2ResApi(invokeResult.getData());
            return DataResponse.success(apiRes);
        } catch (Exception e) {
            log.error("makeInvoice exception. input={}", JSONObject.toJSONString(input), e);
            return DataResponse.error("开票发生异常。");
        }finally {
            log.info("makeInvoice end. input={}, invokeResult={}", JSONObject.toJSONString(input), JSONObject.toJSONString(invokeResult));
        }
    }

    @Override
    public DataResponse<List<CatlPreInvoiceOrderDTO>> queryCatlInvoiceOrder(CatlPreInvoiceOrderQuery req) {
        return DataResponse.success(orderThirdArchiveManageService.queryListByBatchNum(req));
    }

}
