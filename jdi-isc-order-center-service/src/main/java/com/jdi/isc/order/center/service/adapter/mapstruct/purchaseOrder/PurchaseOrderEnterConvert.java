package com.jdi.isc.order.center.service.adapter.mapstruct.purchaseOrder;


import com.jdi.isc.order.center.domain.purchaseOrder.biz.PurchaseOrderEnterWarehouseVO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.PurchaseOrderEnterWarehouseWareVO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderEnterWarehousePO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderEnterWarehouseWarePO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PurchaseOrderEnterConvert {

    PurchaseOrderEnterConvert INSTANCE = Mappers.getMapper(PurchaseOrderEnterConvert.class);

    PurchaseOrderEnterWarehousePO vo2po(PurchaseOrderEnterWarehouseVO purchaseOrderEnterWarehouseVO);

    PurchaseOrderEnterWarehouseWarePO wareVo2Po(PurchaseOrderEnterWarehouseWareVO purchaseOrderEnterWarehouseWareVO);


    PurchaseOrderEnterWarehouseVO po2vo(PurchaseOrderEnterWarehousePO purchaseOrderEnterWarehousePO);

    PurchaseOrderEnterWarehouseWareVO warePo2Vo(PurchaseOrderEnterWarehouseWarePO purchaseOrderEnterWarehouseWarePO);

    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    PurchaseOrderEnterWarehouseWarePO copyPo2WarePO(PurchaseOrderEnterWarehousePO purchaseOrderEnterWarehousePO);
}
