package com.jdi.isc.order.center.service.adapter.mapstruct.warehouse;

import com.jdi.isc.order.center.api.purchaseOrder.biz.resp.WarehouseApiDTO;
import com.jdi.isc.order.center.domain.warehouse.biz.WarehouseVO;
import com.jdi.isc.order.center.domain.warehouse.po.WarehousePO;
import com.jdi.isc.product.soa.api.warehouse.base.WarehouseDTO;
import com.jdi.isc.product.soa.api.warehouse.res.WarehouseResDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 仓信息表对象转换
 * @Author: wangpeng965
 * @Date: 2024/07/18 11:23
 **/
@Mapper
public interface WarehouseConvert {

    WarehouseConvert INSTANCE = Mappers.getMapper(WarehouseConvert.class);

    WarehousePO vo2Po(WarehouseVO vo);

    WarehouseVO po2Vo(WarehousePO po);

    List<WarehouseVO> listPo2Vo(List<WarehousePO> poList);

    List<WarehousePO> listVo2Po(List<WarehouseVO> voList);

    WarehouseApiDTO po2DTO(WarehousePO po);


    WarehousePO dto2Po(WarehouseDTO dto);

    WarehousePO dto2Po(WarehouseResDTO warehouseResDTO);
}
