package com.jdi.isc.order.center.service.protocol.jsf;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.isc.order.center.api.OrderWriteManageApiService;
import com.jdi.isc.order.center.api.biz.req.BrazilTaxFeeDetailReq;
import com.jdi.isc.order.center.api.biz.req.UpdateOrderStatusManageApiReq;
import com.jdi.isc.order.center.api.biz.req.UpdateOrderThirdIdReq;
import com.jdi.isc.order.center.common.constants.OrderOperateServiceConstant;
import com.jdi.isc.order.center.domain.mapstruct.order.OrderUpdateConvert;
import com.jdi.isc.order.center.domain.order.biz.UpdateOrderStatusReqVO;
import com.jdi.isc.order.center.service.manage.order.OrderWriteService;
import com.jdi.isc.order.center.service.manage.tax.BrazilTaxFeeDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class OrderWriteManageApiServiceImpl implements OrderWriteManageApiService {

    @Autowired
    private OrderWriteService orderWriteService;

    @Autowired
    BrazilTaxFeeDetailService  brazilTaxFeeDetailService;

    @Override
    public DataResponse<Boolean> updateOrderStatus(UpdateOrderStatusManageApiReq updateOrderStatusManageApiReq) {
        return orderWriteService.updateOrder(OrderUpdateConvert.INSTANCE.manageApiReq2voReq(updateOrderStatusManageApiReq));
    }

    @Override
    public DataResponse<Boolean> updateOrderStatusByApproveXbp(Integer ticketId,Integer auditStatus){
        return orderWriteService.updateOrderStatusByApproveXbp(ticketId,auditStatus);
    }

    /**
     * 更新订单的第三方ID。
     * @param req 包含要更新的订单ID和新的第三方ID的请求对象。
     * @return 更新操作的结果，包括更新的订单ID列表。
     */
    @Override
    public DataResponse<List<Long>> updateOrderThirdId(UpdateOrderThirdIdReq req) {
        return orderWriteService.updateOrderThirdId(OrderUpdateConvert.INSTANCE.thirdIdReqToVoReq(req));
    }


    /**
     * 批量保存或更新巴西税费明细。
     * @param list 巴西税费明细请求列表。
     * @return 包含所有保存或更新结果的响应对象。
     */
    @Override
    public DataResponse<String> batchSaveOrUpdateBrazilTaxFeeDetail(List<BrazilTaxFeeDetailReq> list) {
        return brazilTaxFeeDetailService.batchSaveOrUpdateBrazilTaxFeeDetail(list);
    }


}
