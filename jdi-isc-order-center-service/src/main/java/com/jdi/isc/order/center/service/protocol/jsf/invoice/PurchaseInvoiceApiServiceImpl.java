package com.jdi.isc.order.center.service.protocol.jsf.invoice;

import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.common.SystemInfoOrderApiReq;
import com.jdi.isc.order.center.api.finance.purchaseInvoice.PurchaseInvoiceApiService;
import com.jdi.isc.order.center.api.finance.purchaseInvoice.req.PurchaseBatchInvoiceSaveReqDTO;
import com.jdi.isc.order.center.api.finance.purchaseInvoice.req.PurchaseInvoiceSaveReqDTO;
import com.jdi.isc.order.center.api.finance.purchaseInvoice.req.PurchaseInvoiceUpdatePoApproveReqDTO;
import com.jdi.isc.order.center.api.finance.purchaseInvoice.req.TaxReviewReq;
import com.jdi.isc.order.center.common.utils.JsonUtil;
import com.jdi.isc.order.center.domain.invoice.purchaseOrder.vo.PurchaseInvoiceBatchSaveReqVO;
import com.jdi.isc.order.center.domain.invoice.purchaseOrder.vo.PurchaseInvoiceSaveReqVO;
import com.jdi.isc.order.center.domain.invoice.purchaseOrder.vo.PurchaseOrderInvoiceTaxUpdateApproveReqVO;
import com.jdi.isc.order.center.service.adapter.mapstruct.invoice.PurchaseInvoiceConvert;
import com.jdi.isc.order.center.service.manage.invoice.PurchaseInvoiceService;
import com.jdi.isc.order.center.service.manage.invoice.PurchaseInvoiceUpdatePoApplyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PurchaseInvoiceApiServiceImpl implements PurchaseInvoiceApiService {

    @Autowired
    private PurchaseInvoiceService purchaseInvoiceService;

    @Autowired
    private PurchaseInvoiceUpdatePoApplyService purchaseInvoiceUpdatePoApplyService;

    /**
     * 保存或更新采购发票。
     * @param purchaseInvoiceSaveReqDTO 采购发票保存请求DTO，包含发票信息和系统信息。
     * @return DataResponse<Boolean>，表示操作结果的响应对象，包含操作是否成功的布尔值。
     */
    @Override
    public DataResponse<Boolean> saveOrUpdateInvoice(PurchaseInvoiceSaveReqDTO purchaseInvoiceSaveReqDTO) {
        DataResponse<Boolean> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.invoice.PurchaseInvoiceApiServiceImpl.saveOrUpdateInvoice");
        PurchaseInvoiceSaveReqVO reqVO = null;
        try {
            reqVO = PurchaseInvoiceConvert.INSTANCE.saveReqDTO2VO(purchaseInvoiceSaveReqDTO);
            SystemInfoOrderApiReq systemInfoOrderApiReq = new SystemInfoOrderApiReq();
            systemInfoOrderApiReq.setLocaleList(purchaseInvoiceSaveReqDTO.getLocaleList());
            systemInfoOrderApiReq.setSystemCode(purchaseInvoiceSaveReqDTO.getSystemCode());
            systemInfoOrderApiReq.setServiceIp(purchaseInvoiceSaveReqDTO.getServiceIp());
            systemInfoOrderApiReq.setUserIp(purchaseInvoiceSaveReqDTO.getUserIp());
            reqVO.setClientReqExt(systemInfoOrderApiReq);
            dataResponse = purchaseInvoiceService.saveOrUpdateInvoice(reqVO);
        } catch (Exception e) {
            log.error("PurchaseInvoiceApiServiceImpl.saveOrUpdateInvoice, req = {}, result = {}", JSON.toJSONString(reqVO), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.error("PurchaseInvoiceApiServiceImpl.saveOrUpdateInvoice, req = {}, result = {}", JSON.toJSONString(reqVO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }


    @Override
    public DataResponse<Boolean> batchSaveOrUpdateInvoice(PurchaseBatchInvoiceSaveReqDTO purchaseInvoiceSaveReqDTO) {
        DataResponse<Boolean> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.invoice.PurchaseInvoiceApiServiceImpl.batchSaveOrUpdateInvoice");
        PurchaseInvoiceBatchSaveReqVO reqVO = null;
        try {
            reqVO=JsonUtil.getJavaObject(purchaseInvoiceSaveReqDTO,PurchaseInvoiceBatchSaveReqVO.class);
            SystemInfoOrderApiReq systemInfoOrderApiReq = new SystemInfoOrderApiReq();
            systemInfoOrderApiReq.setLocaleList(purchaseInvoiceSaveReqDTO.getLocaleList());
            systemInfoOrderApiReq.setSystemCode(purchaseInvoiceSaveReqDTO.getSystemCode());
            systemInfoOrderApiReq.setServiceIp(purchaseInvoiceSaveReqDTO.getServiceIp());
            systemInfoOrderApiReq.setUserIp(purchaseInvoiceSaveReqDTO.getUserIp());
            reqVO.setClientReqExt(systemInfoOrderApiReq);
            dataResponse = purchaseInvoiceService.batchSaveOrUpdateInvoice(reqVO);
        } catch (Exception e) {
            log.error("PurchaseInvoiceApiServiceImpl.batchSaveOrUpdateInvoice, req = {}, result = {}", JSON.toJSONString(reqVO), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.error("PurchaseInvoiceApiServiceImpl.batchSaveOrUpdateInvoice, req = {}, result = {}", JSON.toJSONString(reqVO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }



    /**
     * 执行税务审查操作。
     * @param taxReviewReq
     * 税务审查请求对象，包含审查所需的信息。
     * @return 审查结果，true 表示审查通过，false 表示审查未通过。
     */
    @Override
    public DataResponse<Boolean> taxReview(TaxReviewReq taxReviewReq) {

        DataResponse<Boolean> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.invoice.PurchaseInvoiceApiServiceImpl.taxReview");


        try {

            dataResponse = purchaseInvoiceService.taxReview(taxReviewReq);

        } catch (Exception e) {
            log.error("PurchaseInvoiceApiServiceImpl.taxReview, req = {}, result = {}", JSON.toJSONString(taxReviewReq), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.error("PurchaseInvoiceApiServiceImpl.taxReview, req = {}, result = {}", JSON.toJSONString(taxReviewReq), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }





    /**
     * 验证发票是否符合税务审核规则。
     * @param purchaseInvoiceSaveReqDTO 发票请求DTO，包含发票相关信息。
     * @return DataResponse对象，包含一个布尔值表示发票是否通过税务审核。
     */
    @Override
    public DataResponse<Boolean> taxReviewRuleCheck(PurchaseInvoiceSaveReqDTO purchaseInvoiceSaveReqDTO) {
        DataResponse<Boolean> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.invoice.PurchaseInvoiceApiServiceImpl.taxReview");
        PurchaseInvoiceSaveReqVO reqVO = null;

        try {
            reqVO = PurchaseInvoiceConvert.INSTANCE.saveReqDTO2VO(purchaseInvoiceSaveReqDTO);
            SystemInfoOrderApiReq systemInfoOrderApiReq = new SystemInfoOrderApiReq();
            systemInfoOrderApiReq.setLocaleList(purchaseInvoiceSaveReqDTO.getLocaleList());
            systemInfoOrderApiReq.setSystemCode(purchaseInvoiceSaveReqDTO.getSystemCode());
            systemInfoOrderApiReq.setServiceIp(purchaseInvoiceSaveReqDTO.getServiceIp());
            systemInfoOrderApiReq.setUserIp(purchaseInvoiceSaveReqDTO.getUserIp());
            reqVO.setClientReqExt(systemInfoOrderApiReq);

            dataResponse = purchaseInvoiceService.taxReviewRuleCheck(reqVO);

        } catch (Exception e) {
            log.error("PurchaseInvoiceApiServiceImpl.taxReview, req = {}, result = {}", JSON.toJSONString(reqVO), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.error("PurchaseInvoiceApiServiceImpl.taxReview, req = {}, result = {}", JSON.toJSONString(reqVO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }

    @Override
    public DataResponse<Boolean> approvePurchaseInvoiceUpdatePoApply(PurchaseInvoiceUpdatePoApproveReqDTO req) {
        DataResponse<Boolean> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.invoice.PurchaseInvoiceReadApiServiceImpl.approvePurchaseInvoiceUpdatePoApply");
        try {
            PurchaseOrderInvoiceTaxUpdateApproveReqVO reqVO = PurchaseInvoiceConvert.INSTANCE.approvePurchaseInvoiceUpdatePoApplyDto2Vo(req);
            dataResponse = purchaseInvoiceUpdatePoApplyService.approvePurchaseInvoiceUpdatePoApply(reqVO);
        } catch (Exception e) {
            log.error("PurchaseInvoiceReadApiServiceImpl.approvePurchaseInvoiceUpdatePoApply, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.error("PurchaseInvoiceReadApiServiceImpl.approvePurchaseInvoiceUpdatePoApply, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }

}
