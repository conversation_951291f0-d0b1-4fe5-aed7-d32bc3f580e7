package com.jdi.isc.order.center.service.adapter.mapstruct.settlement;


import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.settlement.biz.PurchaseSettlementDTO;
import com.jdi.isc.order.center.api.settlement.biz.PurchaseSettlementSkuDTO;
import com.jdi.isc.order.center.api.settlement.biz.SupplierSettlementDTO;
import com.jdi.isc.order.center.api.settlement.biz.SupplierSettlementDetailDTO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.settlement.biz.SupplierSettlementPageVO;
import com.jdi.isc.order.center.domain.settlement.biz.SupplierSettlementVO;
import com.jdi.isc.order.center.domain.settlement.po.SupplierSettlementsDetailPo;
import com.jdi.isc.order.center.domain.settlement.po.SupplierSettlementsPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SupplierSettlementConvert {

    SupplierSettlementConvert INSTANCE = Mappers.getMapper(SupplierSettlementConvert.class);

    SupplierSettlementPageVO supplierSettlementDto2Req(SupplierSettlementDTO settlementDTO);

    PageInfo<PurchaseSettlementDTO> supplierSettlementPageVo2Dto(PageInfo<SupplierSettlementVO> page);

    List<SupplierSettlementVO> purchaseOrderPo2setlVo(List<PurchaseOrderPO> records);


    List<SupplierSettlementDetailDTO> supplierSettlementsPo2Dto(List<SupplierSettlementsPo> settlementsPos);


    List<PurchaseSettlementDTO> purchaseOrderPo2setlDto(List<PurchaseOrderPO> orderPOList);

    List<PurchaseSettlementSkuDTO> supplierSettlementsDetailPo2Dto(List<SupplierSettlementsDetailPo> settlementsDetailPos);
}
