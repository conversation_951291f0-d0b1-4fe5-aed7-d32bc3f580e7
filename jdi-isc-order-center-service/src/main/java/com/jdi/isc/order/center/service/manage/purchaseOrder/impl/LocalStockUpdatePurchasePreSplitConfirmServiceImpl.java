package com.jdi.isc.order.center.service.manage.purchaseOrder.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.constants.OrderValidStatusConstant;
import com.jdi.isc.order.center.api.constants.PurchaseOrderStatusConstant;
import com.jdi.isc.order.center.api.constants.PurchaseOrderValidStatusConstant;
import com.jdi.isc.order.center.domain.enums.OrderPreSplitStatusEnum;
import com.jdi.isc.order.center.domain.mapstruct.purchaseOrder.PurchaseOrderConvert;
import com.jdi.isc.order.center.domain.mapstruct.purchaseOrder.PurchaseOrderWareConvert;
import com.jdi.isc.order.center.domain.promiseTime.res.UpdateBizOrderExtPromiseTimeRes;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.*;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderExtInfoPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderWarePO;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderAtomicService;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderWareAtomicService;
import com.jdi.isc.order.center.service.manage.common.PurchaseOrderCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 预拆单生效
 */
@Service
@Slf4j
public class LocalStockUpdatePurchasePreSplitConfirmServiceImpl extends AbstractUpdatePurchaseOrderWriteServiceImpl<PurchaseOrderPreSplitConfirmVO> {

    @Autowired
    private PurchaseOrderAtomicService purchaseOrderAtomicService;

    @Autowired
    private PurchaseOrderWareAtomicService purchaseOrderWareAtomicService;
    @Autowired
    private PurchaseOrderCommonService purchaseOrderCommonService;

    @Value("${topic.producer.purchaseOrder.updateStatus}")
    private String updatePurchaseOrderStatusTopic;

    @Value("${topic.producer.purchaseOrder.split}")
    private String purchaseOrderSplitTopic;


    @Override
    protected void sendMessage(PurchaseOrderWrapperMqMsgVO purchaseOrderPO, PurchaseOrderPreSplitConfirmVO specialData) {
        List<PurchaseOrderPO> orderPOList = specialData.getPurchaseOrderPOList();
        if (CollectionUtils.isNotEmpty(orderPOList)) {
            Map<String, Object> childPurchaseOrder = orderPOList.stream().peek(o -> {
                o.setPurchaseOrderStatus(PurchaseOrderStatusConstant.CONFIRM.getStatus());
                o.setValidState(OrderValidStatusConstant.VALID);
            }).collect(Collectors.toMap(PurchaseOrderPO::getPurchaseOrderId, Function.identity()));
            orderMqService.sendMessageBatchRetry(childPurchaseOrder, updatePurchaseOrderStatusTopic);
        }else{
            //未拆单的场景下，发送父单变更消息
            purchaseOrderPO.setPurchaseOrderStatus(PurchaseOrderStatusConstant.CONFIRM.getStatus());
            Map<String, Object> parentMap = new HashMap<>();
            parentMap.put(purchaseOrderPO.getPurchaseOrderId(), purchaseOrderPO);
            orderMqService.sendMessageBatchRetry(parentMap, updatePurchaseOrderStatusTopic);
        }

        // 发送拆单消息
        PurchaseOrderVO purchaseOrderVO = wrapSplitMQInfo(purchaseOrderPO.getPurchaseOrderId());
        log.error("本土备货采购单拆单消息:{}", JSON.toJSONString(purchaseOrderVO));
        orderMqService.sendMessageRetry(purchaseOrderPO.getOrderId().toString(),purchaseOrderVO,purchaseOrderSplitTopic);
    }

    private PurchaseOrderVO wrapSplitMQInfo(String parentPurchaseOrderId) {
        PurchaseOrderPO originPurchaseOrder = purchaseOrderAtomicService.queryByPoId(parentPurchaseOrderId);
        PurchaseOrderPO queryPurchase = new PurchaseOrderPO();
        queryPurchase.setParentPurchaseOrderId(parentPurchaseOrderId);
        Assert.notNull(queryPurchase.getParentPurchaseOrderId(),"采购单号不能为空");
        List<PurchaseOrderPO> purchaseOrderPOS = purchaseOrderAtomicService.listByCondition(queryPurchase);
        List<PurchaseOrderPO> existOrder = purchaseOrderPOS.stream().filter(p -> PurchaseOrderValidStatusConstant.VALID.equals(p.getValidState())).collect(Collectors.toList());
        Set<String> existPurchaseOrderIds = existOrder.stream().map(PurchaseOrderPO::getPurchaseOrderId).collect(Collectors.toSet());
        Map<String, List<PurchaseOrderWarePO>> existPurchaseOrderWareMap = purchaseOrderWareAtomicService.queryPurchaseOrderWarePOListByIds(existPurchaseOrderIds);
        return buildPurchaseOrderVO(originPurchaseOrder, existOrder, existPurchaseOrderWareMap);
    }

    public PurchaseOrderVO buildPurchaseOrderVO(PurchaseOrderPO parentPurchaseOrderPO, List<PurchaseOrderPO> existPurchaseOrderPOList, Map<String, List<PurchaseOrderWarePO>> existPurchaseOrderWarePOMap){
        PurchaseOrderVO purchaseOrderVO = PurchaseOrderConvert.INSTANCE.purchaseOrderPo2Vo(parentPurchaseOrderPO);

        purchaseOrderVO.setPurchaseOrderWareVOList(PurchaseOrderWareConvert.INSTANCE.listPurchaseOrderWarePo2Vo(existPurchaseOrderWarePOMap.get(purchaseOrderVO.getPurchaseOrderId())));

        if(CollectionUtils.isEmpty(existPurchaseOrderPOList)){
            return purchaseOrderVO;
        }
        List<PurchaseOrderVO> childPurchaseOrderVOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(existPurchaseOrderPOList)){
            childPurchaseOrderVOList.addAll(existPurchaseOrderPOList.stream().map(po -> {
                PurchaseOrderVO vo = PurchaseOrderConvert.INSTANCE.purchaseOrderPo2Vo(po);
                vo.setPurchaseOrderWareVOList(PurchaseOrderWareConvert.INSTANCE.listPurchaseOrderWarePo2Vo(existPurchaseOrderWarePOMap.get(vo.getPurchaseOrderId())));
                return vo;
            }).collect(Collectors.toList()));
        }

        purchaseOrderVO.setChildPurchaseOrderList(childPurchaseOrderVOList);
        return purchaseOrderVO;
    }


    @Override
    public DataResponse<PurchaseOrderPreSplitConfirmVO> specialBiz(PurchaseOrderPO purchaseOrderPO, UpdatePurchaseOrderStatusReqVO updatePurchaseOrderStatusReqVO) {

        //判断采购单是否预拆完成
        if (Objects.isNull(purchaseOrderPO.getPreSplitStatus()) || OrderPreSplitStatusEnum.PRE_SPLIT_FINISH.getStatue() != purchaseOrderPO.getPreSplitStatus()) {
            return DataResponse.error("采购单未完成预拆单");
        }
        //查询子单
        PurchaseOrderPO query = new PurchaseOrderPO();
        query.setSplitPurchaseOrderId(purchaseOrderPO.getPurchaseOrderId());
        Assert.notNull(query.getSplitPurchaseOrderId(),"采购单号不能为空");
        List<PurchaseOrderPO> purchaseOrderPOList = purchaseOrderAtomicService.listByCondition(query);
        PurchaseOrderPreSplitConfirmVO orderPreSplitConfirmVO = new PurchaseOrderPreSplitConfirmVO();
        orderPreSplitConfirmVO.setPurchaseOrderPOList(purchaseOrderPOList);
        orderPreSplitConfirmVO.setUpdateParentOrderPO(purchaseOrderPO);

        // 设置父子单的确认时间、履约时间
        long time = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(purchaseOrderPOList)) {
            purchaseOrderPOList.forEach(p -> {
                p.setConfirmTime(time);
                // 必须先设置确认时间，再计算履约时间
                calculatePromiseTime(p);
            });
        }

        purchaseOrderPO.setConfirmTime(time);
        // 必须先设置确认时间，再计算履约时间
        calculatePromiseTime(purchaseOrderPO);

        return DataResponse.success(orderPreSplitConfirmVO);
    }

    @Override
    protected boolean updatePurchaseOrderInfo(PurchaseOrderPO purchaseOrderPO, int newStatus, UpdatePurchaseOrderStatusReqVO reqVO, PurchaseOrderPreSplitConfirmVO specialData){
        List<PurchaseOrderPO> purchaseOrderList = specialData.getPurchaseOrderPOList();
        boolean result = purchaseOrderAtomicService.confirmPreSplitPurchaseOrdersStatus(purchaseOrderPO,purchaseOrderList,newStatus);
        Assert.isTrue(result, "采购单预拆单确认失败");
        return true;
    }


    private void calculatePromiseTime(PurchaseOrderPO purchaseOrderPO){
        // 计算履约时间
        if (StringUtils.isBlank(purchaseOrderPO.getPurchaseOrderExtInfo())){
            log.info("calculatePromiseTime, PurchaseOrderExtInfo is null, purchaseOrderOrderId= {}", purchaseOrderPO.getPurchaseOrderId());
            return;
        }

        PurchaseOrderExtInfoPO purchaseOrderExtInfoPO = JSON.parseObject(purchaseOrderPO.getPurchaseOrderExtInfo(), PurchaseOrderExtInfoPO.class);
        UpdateBizOrderExtPromiseTimeRes bizOrderExtPromiseTimeRes = purchaseOrderCommonService.buildPurchaseOrderExtInfoPoPromiseTime(purchaseOrderPO, true, true);

        if (null!=bizOrderExtPromiseTimeRes && null!=bizOrderExtPromiseTimeRes.getPromisedShipmentTime()){
            purchaseOrderExtInfoPO.setPromisedShipmentTime(bizOrderExtPromiseTimeRes.getPromisedShipmentTime());
        }
        if (null!=bizOrderExtPromiseTimeRes && null!=bizOrderExtPromiseTimeRes.getPromisedDeliveryTime()){
            purchaseOrderExtInfoPO.setPromisedDeliveryTime(bizOrderExtPromiseTimeRes.getPromisedDeliveryTime());
        }
        purchaseOrderPO.setPurchaseOrderExtInfo(JSONObject.toJSONString(purchaseOrderExtInfoPO));
    }
}
