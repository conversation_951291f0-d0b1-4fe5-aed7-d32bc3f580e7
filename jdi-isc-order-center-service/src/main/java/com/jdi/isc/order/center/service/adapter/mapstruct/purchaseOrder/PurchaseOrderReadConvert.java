package com.jdi.isc.order.center.service.adapter.mapstruct.purchaseOrder;

import com.jdi.isc.order.center.api.purchaseOrder.biz.resp.PurchaseOrderWareApiDTO;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.req.PurchaseOrderPageDTO;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.res.PurchaseOrderReadApiDTO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.PurchaseOrderPageReqVO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderWarePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PurchaseOrderReadConvert {

    PurchaseOrderReadConvert INSTANCE = Mappers.getMapper(PurchaseOrderReadConvert.class);


    PurchaseOrderPageReqVO pageDto2Vo(PurchaseOrderPageDTO pageDTO);

    List<PurchaseOrderReadApiDTO> pagePo2Api(List<PurchaseOrderPO> records);

    PurchaseOrderReadApiDTO po2ReadApi(PurchaseOrderPO purchaseOrderPO);

    List<PurchaseOrderWareApiDTO> listWarePo2Api(List<PurchaseOrderWarePO> warePoList);
}
