package com.jdi.isc.order.center.service.adapter.mapstruct.invoice;

import com.jdi.isc.order.center.api.finance.purchaseInvoice.req.*;
import com.jdi.isc.order.center.api.finance.purchaseInvoice.res.*;
import com.jdi.isc.order.center.api.purchaseOrder.biz.resp.PurchaseOrderWareTaxInfoDTO;
import com.jdi.isc.order.center.domain.invoice.purchaseOrder.po.PurchaseInvoiceMsgPO;
import com.jdi.isc.order.center.domain.invoice.purchaseOrder.po.PurchaseInvoicePO;
import com.jdi.isc.order.center.domain.invoice.purchaseOrder.po.PurchaseInvoiceWarePO;
import com.jdi.isc.order.center.domain.invoice.purchaseOrder.vo.*;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderUpdateApplyPO;
import com.jdi.isc.order.center.domain.tax.PurchaseOrderWareTaxExtendsVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PurchaseInvoiceConvert {

    PurchaseInvoiceConvert INSTANCE = Mappers.getMapper(PurchaseInvoiceConvert.class);

    PurchaseInvoicePO req2Po(PurchaseInvoiceReqVO purchaseInvoiceReqVO);

    List<PurchaseInvoiceWarePO> listWareReq2Po(List<PurchaseInvoiceWareReqVO> purchaseInvoiceWareSaveReqVOList);

    PurchaseInvoiceWarePO wareReq2Po(PurchaseInvoiceWareReqVO purchaseInvoiceWareSaveReqVO);

    List<PurchaseInvoiceMainDTO> listPo2MainDTO(List<PurchaseOrderPO> purchaseOrderPOS);

    @Mapping(target = "invoiceTotalPrice", expression = "java(purchaseInvoicePO.getCustomerInvoiceTotalPrice() != null ? purchaseInvoicePO.getCustomerInvoiceTotalPrice() : purchaseInvoicePO.getInvoiceTotalPrice())")
    PurchaseInvoiceDetailDTO invoicePo2DetailDto(PurchaseInvoicePO purchaseInvoicePO);

    List<PurchaseInvoiceDetailDTO> listInvoicePo2DetailDto(List<PurchaseInvoicePO> list);

    List<PurchaseInvoiceWareDTO> listPo2Dto(List<PurchaseInvoiceWarePO> wareList);

    @Mapping(source = "completeTime", target = "finishTime")
    @Mapping(source = "waresPurchaseTotalPrice", target = "skuPrice")
    @Mapping(source = "warePurchaseTotalIncludeTaxPrice", target = "includeTaxPrice")
    @Mapping(source = "purchaseOrderTaxes", target = "valueAddedTax")
    PurchaseInvoiceMainDTO po2MainDTO(PurchaseOrderPO purchaseOrderPOS);

    PurchaseInvoicePO vo2Po(PurchaseInvoiceVO invoiceVO);

    List<PurchaseInvoiceWarePO> listWareVo2Po(List<PurchaseInvoiceWareVO> purchaseInvoiceWareVOList);

    PurchaseInvoiceSaveReqVO saveReqDTO2VO(PurchaseInvoiceSaveReqDTO purchaseInvoiceSaveReqDTO);

    PurchaseInvoiceReqVO invoiceReqDto2vo(PurchaseInvoiceReqDTO purchaseInvoiceReqDTO);

    PurchaseOrderInvoiceVO purchaseOrder2Invoice(PurchaseOrderPO existPo);

    PurchaseOrderInvoiceDTO purchaseOrderInvoice2Dto(PurchaseOrderInvoiceVO result);

    PurchaseInvoiceMsgPO  invoicePO2Msg(PurchaseInvoicePO purchaseInvoicePO);


    PurchaseInvoicePO  copyPO(PurchaseInvoicePO purchaseInvoicePO);

    PurchaseInvoiceXmlParseDTO xmlParse2Dto(PurchaseInvoiceXmlParseVO xmlParseVO);

    PurchaseOrderWareTaxInfoDTO xmlParseTax2Dto(PurchaseOrderWareTaxExtendsVO xmlParseVO);

    PurchaseInvoiceOrderUpdatePageReqVO purchaseInvoiceOrderUpdatePageReqDto2ReqVo(PurchaseInvoiceUpdatePoApplyPageReqDTO reqDTO);

    PurchaseOrderInvoiceTaxUpdateApplyDetailReqVO purchaseInvoiceOrderUpdateDetailReqDto2ReqVo(PurchaseInvoiceUpdatePoApplyReqDTO reqDTO);

    @Mappings({
        @Mapping(target = "invoiceId", source = "applyId")
    })
    PurchaseInvoiceUpdatePoApplyDTO purchaseInvoiceOrderUpdateDto2Vo(PurchaseOrderUpdateApplyPO purchaseOrderUpdateApplyPO);

    PurchaseOrderInvoiceTaxUpdateApproveReqVO approvePurchaseInvoiceUpdatePoApplyDto2Vo(PurchaseInvoiceUpdatePoApproveReqDTO approveReqDTO);
}
