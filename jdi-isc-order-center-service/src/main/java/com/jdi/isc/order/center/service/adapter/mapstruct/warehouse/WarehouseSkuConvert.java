package com.jdi.isc.order.center.service.adapter.mapstruct.warehouse;

import com.jdi.isc.order.center.domain.warehouse.biz.WarehouseSkuVO;
import com.jdi.isc.order.center.domain.warehouse.po.WarehouseSkuPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 仓信息表对象转换
 * @Author: wangpeng965
 * @Date: 2024/07/18 11:23
 **/
@Mapper
public interface WarehouseSkuConvert {

    WarehouseSkuConvert INSTANCE = Mappers.getMapper(WarehouseSkuConvert.class);

    WarehouseSkuPO vo2Po(WarehouseSkuVO vo);

    WarehouseSkuVO po2Vo(WarehouseSkuPO po);

    List<WarehouseSkuVO> listPo2Vo(List<WarehouseSkuPO> poList);

    List<WarehouseSkuPO> listVo2Po(List<WarehouseSkuVO> voList);
}
