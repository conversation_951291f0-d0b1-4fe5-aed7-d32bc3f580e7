package com.jdi.isc.order.center.service.protocol.jsf.aftersales;

import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.isc.order.center.api.aftersales.AfterSalesReadApiService;
import com.jdi.isc.order.center.api.aftersales.req.*;
import com.jdi.isc.order.center.api.aftersales.res.AfterSalesApiResp;
import com.jdi.isc.order.center.api.aftersales.res.AfterSalesAuditInitRespApiDTO;
import com.jdi.isc.order.center.api.aftersales.res.AfterSalesDetailApiResp;
import com.jdi.isc.order.center.api.aftersales.res.AfterSalesSubmitInitRespApiDTO;
import com.jdi.isc.order.center.domain.config.OrderDuccConfig;
import com.jdi.isc.order.center.service.manage.aftersales.AfterSalesReadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/24 17:20
 */
@Service
@Slf4j
public class AfterSalesReadApiServiceImpl implements AfterSalesReadApiService {

    @Resource
    private AfterSalesReadService afterSalesReadService;

    @Autowired
    private OrderDuccConfig orderDuccConfig;

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public DataResponse<PageInfo<AfterSalesApiResp>> managePageAfterSalesOrder(AfterSalesManagePageApiDTO req) {
        DataResponse<PageInfo<AfterSalesApiResp>> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.aftersales.AfterSalesReadApiServiceImpl.managePageAfterSalesOrder." + active);
        try {
            PageInfo<AfterSalesApiResp> afterSalesApiRespPageInfo = afterSalesReadService.managePageAfterSalesOrder(req);
            dataResponse = DataResponse.success(afterSalesApiRespPageInfo);
        } catch (Exception e){
            dataResponse = DataResponse.error(DataResponseCode.SYSTEM_ERROR.getCode());
            log.error("AfterSalesReadApiServiceImpl.managePageAfterSalesOrder, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("AfterSalesReadApiServiceImpl.managePageAfterSalesOrder, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;

    }

    @Override
    public DataResponse<AfterSalesApiResp> manageDetail(AfterSalesManageDetailApiDTO req) {
        DataResponse<AfterSalesApiResp> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.aftersales.AfterSalesReadApiServiceImpl.manageDetail." + active);
        try {
            AfterSalesApiResp afterSalesApiResp = afterSalesReadService.manageDetail(req);
            dataResponse = DataResponse.success(afterSalesApiResp);
        } catch (Exception e){
            dataResponse = DataResponse.error(DataResponseCode.SYSTEM_ERROR.getCode());
            log.error("AfterSalesReadApiServiceImpl.manageDetail, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("AfterSalesReadApiServiceImpl.manageDetail, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }

    @Override
    public DataResponse<AfterSalesSubmitInitRespApiDTO> querySubmitInitInfo(AfterSalesSubmitInitReqApiDTO req) {
        DataResponse<AfterSalesSubmitInitRespApiDTO> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.aftersales.AfterSalesReadApiServiceImpl.submitInitInfo." + active);
        try {
            dataResponse = afterSalesReadService.submitInitInfo(req);
        } catch (Exception e){
            dataResponse = DataResponse.error(DataResponseCode.SYSTEM_ERROR.getCode());
            log.error("AfterSalesReadApiServiceImpl.submitInitInfo, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("AfterSalesReadApiServiceImpl.submitInitInfo, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }

    @Override
    public DataResponse<AfterSalesAuditInitRespApiDTO> queryAuditInitInfo(AfterSalesAuditInitInfoReqApiDTO req) {
        DataResponse<AfterSalesAuditInitRespApiDTO> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.aftersales.AfterSalesReadApiServiceImpl.auditInitInfo." + active);
        try {
            AfterSalesAuditInitRespApiDTO afterSalesSubmitInitInfo = orderDuccConfig.getAfterSalesSubmitInitInfo();
            afterSalesSubmitInitInfo.setAfterSalesOrderId(req.getAfterSalesOrderId());
            dataResponse = DataResponse.success(afterSalesSubmitInitInfo);
        } catch (Exception e){
            dataResponse = DataResponse.error(DataResponseCode.SYSTEM_ERROR.getCode());
            log.error("AfterSalesReadApiServiceImpl.auditInitInfo, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("AfterSalesReadApiServiceImpl.auditInitInfo, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }

    @Override
    public DataResponse<AfterSalesDetailApiResp> detail(AfterSalesDetailReqApiDTO req) {
        DataResponse<AfterSalesDetailApiResp> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.aftersales.AfterSalesReadApiServiceImpl.detail." + active);
        try {
            AfterSalesDetailApiResp afterSalesApiResp = afterSalesReadService.detail(req);
            dataResponse = DataResponse.success(afterSalesApiResp);
        } catch (Exception e){
            log.error("AfterSalesReadApiServiceImpl.detail, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("AfterSalesReadApiServiceImpl.detail, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }

    @Override
    public DataResponse<PageInfo<AfterSalesApiResp>> jobPageAfterSalesOrder(AfterSalesManagePageApiDTO req) {
        DataResponse<PageInfo<AfterSalesApiResp>> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.aftersales.AfterSalesReadApiServiceImpl.jobPageAfterSalesOrder." + active);
        try {
            PageInfo<AfterSalesApiResp> afterSalesApiRespPageInfo = afterSalesReadService.managePageAfterSalesOrder(req);
            dataResponse = DataResponse.success(afterSalesApiRespPageInfo);
        } catch (Exception e) {
            dataResponse = DataResponse.error(DataResponseCode.SYSTEM_ERROR.getCode());
            log.error("AfterSalesReadApiServiceImpl.jobPageAfterSalesOrder, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("AfterSalesReadApiServiceImpl.jobPageAfterSalesOrder, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }
}
