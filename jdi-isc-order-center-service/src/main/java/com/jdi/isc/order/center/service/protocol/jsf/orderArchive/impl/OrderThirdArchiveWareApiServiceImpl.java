package com.jdi.isc.order.center.service.protocol.jsf.orderArchive.impl;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.orderArchive.OrderThirdArchiveWareApiService;
import com.jdi.isc.order.center.api.orderArchive.biz.OrderThirdArchiveWarePageApiDTO;
import com.jdi.isc.order.center.api.orderArchive.req.OrderThirdArchiveWareDetailReqApiDTO;
import com.jdi.isc.order.center.api.orderArchive.res.OrderThirdArchiveWareResApiDTO;
import com.jdi.isc.order.center.domain.orderArchive.res.OrderThirdArchiveWareResVO;
import com.jdi.isc.order.center.domain.orderArchive.biz.OrderThirdArchiveWarePageVO;
import com.jdi.isc.order.center.domain.orderArchive.req.OrderThirdArchiveWareDetailReqVO;
import com.jdi.isc.order.center.service.adapter.mapstruct.orderArchive.OrderThirdArchiveWareConvert;
import com.jdi.isc.order.center.service.manage.orderArchive.OrderThirdArchiveWareManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: 订单归档商品api服务实现
 * @Author: zhaojianguo21
 * @Date: 2025/02/27 13:51
 **/

@Slf4j
@Service
public class OrderThirdArchiveWareApiServiceImpl implements OrderThirdArchiveWareApiService {

    @Resource
    private OrderThirdArchiveWareManageService orderThirdArchiveWareManageService;


    @ToolKit
    @Override
    public DataResponse<OrderThirdArchiveWareResApiDTO> detail(OrderThirdArchiveWareDetailReqApiDTO input) {
        OrderThirdArchiveWareResVO invokeResult = null;
        try {
            OrderThirdArchiveWareDetailReqVO reqVO = OrderThirdArchiveWareConvert.INSTANCE.detailReqApi2ReqVo(input);
            invokeResult = orderThirdArchiveWareManageService.detail(reqVO.getId());
            if (null==invokeResult){
               log.warn("detail, invokeResult fail. input={}", JSONObject.toJSONString(input));
               return DataResponse.error("未查询到数据");
            }

            OrderThirdArchiveWareResApiDTO responseVo = OrderThirdArchiveWareConvert.INSTANCE.orderThirdArchiveWareResVo2ResReqApiDto(invokeResult);
            return DataResponse.success(responseVo);
        } catch (Exception e) {
            log.error("detail exception. input={}", JSONObject.toJSONString(input), e);
            return DataResponse.error("发生异常。");
        }finally {
            log.info("detail end. input={}, invokeResult={}", JSONObject.toJSONString(input), JSONObject.toJSONString(invokeResult));
        }
    }

    @ToolKit
    @Override
    public DataResponse<PageInfo<OrderThirdArchiveWarePageApiDTO.Response>> pageSearch(OrderThirdArchiveWarePageApiDTO.Request input){
        PageInfo<OrderThirdArchiveWarePageVO.Response> invokeResult = null;
        try {
            OrderThirdArchiveWarePageVO.Request reqVO = OrderThirdArchiveWareConvert.INSTANCE.pageReqApi2PageReqVo(input);
            invokeResult = orderThirdArchiveWareManageService.pageSearch(reqVO);
            if (null==invokeResult){
                log.warn("pageSearch, invokeResult fail. input={}", JSONObject.toJSONString(input));
                return DataResponse.error("查询失败");
            }

            PageInfo<OrderThirdArchiveWarePageApiDTO.Response> pageInfoRes = OrderThirdArchiveWareConvert.INSTANCE.pageResVo2PageResApi(invokeResult);
            return DataResponse.success(pageInfoRes);
        } catch (Exception e) {
            log.error("pageSearch exception. input={}", JSONObject.toJSONString(input), e);
            return DataResponse.error("发生异常。");
        }finally {
            log.info("pageSearch end. input={}, invokeResult={}", JSONObject.toJSONString(input), JSONObject.toJSONString(invokeResult));
        }
    }
}