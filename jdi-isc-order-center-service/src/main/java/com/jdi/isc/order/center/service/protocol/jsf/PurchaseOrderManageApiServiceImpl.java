package com.jdi.isc.order.center.service.protocol.jsf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.purchaseOrder.PurchaseOrderManageApiService;
import com.jdi.isc.order.center.api.purchaseOrder.biz.req.*;
import com.jdi.isc.order.center.domain.mapstruct.purchaseOrder.PurchaseOrderConvert;
import com.jdi.isc.order.center.domain.mapstruct.purchaseOrder.PurchaseOrderUpdateConvert;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.*;
import com.jdi.isc.order.center.service.manage.purchaseOrder.PurchaseOrderManageService;
import com.jdi.isc.order.center.service.manage.purchaseOrder.PurchaseOrderWriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class PurchaseOrderManageApiServiceImpl implements PurchaseOrderManageApiService {

    @Autowired
    private PurchaseOrderWriteService purchaseOrderWriteService;
    @Autowired
    private PurchaseOrderManageService purchaseOrderManageService;


    @ToolKit
    @Override
    public DataResponse<Boolean> updatePurchaseOrderStatus(UpdatePurchaseOrderStatusManageApiReq req) {
        DataResponse<Boolean> dataResponse = null;
        try{
            UpdatePurchaseOrderStatusReqVO updatePurchaseOrderStatusReqVO = PurchaseOrderUpdateConvert.INSTANCE.manageApiReq2voReq(req);
            updatePurchaseOrderStatusReqVO.setClientReqExt(PurchaseOrderUpdateConvert.INSTANCE.manageReq2ApiReq(req));
            return purchaseOrderWriteService.updatePurchaseOrderStatus(updatePurchaseOrderStatusReqVO);
        }catch (Exception e){
            log.error("PurchaseOrderManageApiServiceImpl.updatePurchaseOrderStatus, req = {}, dataResponse = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse), e);
            return DataResponse.error(DataResponseCode.SYSTEM_ERROR.getMessage());
        }
    }

    @Override
    public DataResponse<PageInfo<PurchaseOrderApiDTO>> pagePurchaseOrder(PurchaseOrderApiReq req) {
        PurchaseOrderPageVO.Request pageReq = PurchaseOrderConvert.INSTANCE.req2PageReq(req);
        PageInfo<PurchaseOrderPageVO.Response> responsePageInfo = purchaseOrderManageService.pagePurchaseOrder(pageReq);
        PageInfo<PurchaseOrderApiDTO> pageInfo = PurchaseOrderConvert.INSTANCE.page2ApiDTO(responsePageInfo);
        return DataResponse.success(pageInfo);
    }

    @Override
    public DataResponse<PurchaseOrderApiDTO> detailPurchaseOrder(PurchaseOrderApiReq req) {
        PurchaseOrderPageVO.Request param = PurchaseOrderConvert.INSTANCE.req2PageReq(req);
        PurchaseOrderVO purchaseOrderVO = purchaseOrderManageService.detailPurchaseOrder(param);
        PurchaseOrderApiDTO apiDTO = PurchaseOrderConvert.INSTANCE.vo2ApiDTO(purchaseOrderVO);
        return DataResponse.success(apiDTO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PurchaseOrderApiDTO> crossBorderStockUp(CrossBorderStockUpApiReq req) {
        return DataResponse.success();
       /* CrossBorderStockUpReqVO reqVO = PurchaseOrderConvert.INSTANCE.stockUpApiReq2ReqVO(req);
        DataResponse<PurchaseOrderVO> res = crossBorderStockUpPurchaseWriteService.saveCrossBorderStockUp(reqVO);
        if (!res.getSuccess()){
            return DataResponse.error(res.getMessage());
        }
        PurchaseOrderApiDTO apiDTO = PurchaseOrderConvert.INSTANCE.vo2ApiDTO(res.getData());
        return DataResponse.success(apiDTO);*/
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse crossBorderStockUpAudit(CrossBorderStockUpAuditApiReq input){
        /*CrossBorderStockUpAuditReqVO reqVO = PurchaseOrderConvert.INSTANCE.stockUpAuditApiReq2ReqVO(input);
        DataResponse res = crossBorderStockUpPurchaseWriteService.crossBorderStockUpAudit(reqVO);
        if (!res.getSuccess()){
            return DataResponse.error(res.getMessage());
        }
        */
        return DataResponse.success();
    }

    @Override
    public DataResponse crossBorderStockUpAuditBatch(CrossBorderStockUpAuditApiBatchReq input) {
        /*if(CollectionUtils.isEmpty(input.getPoNos())){
            log.warn("crossBorderStockUpAuditBatch, param error.");
            return DataResponse.error("param error.");
        }
        log.warn("crossBorderStockUpAuditBatch, param input={}", JSONObject.toJSONString(input));

        Set<String> successPoNos = new HashSet<>();
        Set<String> failPoNos = new HashSet<>();
        Set<String> exceptionPoNos = new HashSet<>();

        for (String poNo: input.getPoNos()){
            try {
                CrossBorderStockUpAuditReqVO reqVO = new CrossBorderStockUpAuditReqVO();
                reqVO.setPoNo(poNo);
                DataResponse res = crossBorderStockUpPurchaseWriteService.crossBorderStockUpAudit(reqVO);
                if (res.getSuccess()){
                    log.info("crossBorderStockUpAuditBatch, loop success. poNo={}, res={}", poNo, JSONObject.toJSONString(res));
                    successPoNos.add(poNo);
                }else {
                    log.warn("crossBorderStockUpAuditBatch, loop fail. poNo={}, res={}", poNo, JSONObject.toJSONString(res));
                    failPoNos.add(poNo);
                }
                Thread.sleep(50);
            } catch (Exception e) {
                log.error("crossBorderStockUpAuditBatch, loop exception. poNo={}", poNo, e);
                exceptionPoNos.add(poNo);
            }
        }

        Map<String, Object> resMap = new HashMap<>();
        resMap.put("successPoNos", successPoNos);
        resMap.put("successPoNosSize", successPoNos.size());
        resMap.put("failPoNos", failPoNos);
        resMap.put("failPoNosSize", failPoNos.size());
        resMap.put("exceptionPoNos", exceptionPoNos);
        resMap.put("exceptionPoNosSize", exceptionPoNos.size());
        log.info("crossBorderStockUpAuditBatch, resMap={}", JSONObject.toJSONString(resMap));
        return DataResponse.success(resMap);*/
        return DataResponse.success();
    }
}
