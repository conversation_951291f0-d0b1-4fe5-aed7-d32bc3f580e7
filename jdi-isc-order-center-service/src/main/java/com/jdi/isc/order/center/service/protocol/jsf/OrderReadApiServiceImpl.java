package com.jdi.isc.order.center.service.protocol.jsf;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.ka.gpt.soa.client.order.resp.QueryOrderOpenResp;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.biz.req.OrderReadApiReq;
import com.jdi.isc.order.center.api.biz.req.OrderWareInfoReadApiReq;
import com.jdi.isc.order.center.api.biz.resp.*;
import com.jdi.isc.order.center.api.biz.resp.dto.OrderApiDTO;
import com.jdi.isc.order.center.api.biz.resp.dto.OrderWareApiDTO;
import com.jdi.isc.order.center.api.common.BaseBizApiDTO;
import com.jdi.isc.order.center.api.constants.OrderStatusConstant;
import com.jdi.isc.order.center.api.constants.PurchaseOrderStatusConstant;
import com.jdi.isc.order.center.api.enums.OrderQueryEnum;
import com.jdi.isc.order.center.api.orderRead.OrderReadApiService;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.common.utils.ConfigUtils;
import com.jdi.isc.order.center.common.utils.JsonUtil;
import com.jdi.isc.order.center.domain.enums.SignStatusEnum;
import com.jdi.isc.order.center.domain.enums.ofc.StockInOutOrderBizTypeEnum;
import com.jdi.isc.order.center.domain.enums.purchase.PurchaseModelEnum;
import com.jdi.isc.order.center.domain.mapstruct.mku.OrderMkuConvert;
import com.jdi.isc.order.center.domain.mapstruct.order.OrderConsigneeConvert;
import com.jdi.isc.order.center.domain.mapstruct.order.OrderConvert;
import com.jdi.isc.order.center.domain.mapstruct.order.OrderLogConvert;
import com.jdi.isc.order.center.domain.mapstruct.purchaseOrder.PurchaseOrderLogConvert;
import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.ofc.stockIoOrder.po.StockInOutOrderPO;
import com.jdi.isc.order.center.domain.ofc.stockIoOrder.po.StockInOutOrderWarePO;
import com.jdi.isc.order.center.domain.order.po.*;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderLogPO;
import com.jdi.isc.order.center.domain.waybill.po.OrderWaybillDeliveryCustomerPO;
import com.jdi.isc.order.center.domain.waybill.po.OrderWaybillRelationPO;
import com.jdi.isc.order.center.rpc.iop.IopOrderRpcService;
import com.jdi.isc.order.center.rpc.tde.TdeClientRpcService;
import com.jdi.isc.order.center.service.atomic.mku.OrderWareAtomicService;
import com.jdi.isc.order.center.service.atomic.ofc.stockIoOrder.StockInOutOrderAtomicService;
import com.jdi.isc.order.center.service.atomic.ofc.stockIoOrder.StockInOutOrderWareAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderBizInfoAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderConsigneeAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderLogAtomicService;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderLogAtomicService;
import com.jdi.isc.order.center.service.atomic.waybill.OrderWaybillDeliveryCustomerAtomicService;
import com.jdi.isc.order.center.service.atomic.waybill.OrderWaybillRelationAtomicService;
import com.jdi.isc.order.center.service.manage.order.OrderBaseReadService;
import com.jdi.isc.order.center.service.manage.warehouse.WarehouseManageService;
import com.jdi.isc.product.soa.api.warehouse.base.WarehouseSkuDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.ehcache.impl.internal.concurrent.ConcurrentHashMap;
import org.jetbrains.annotations.NotNull;
import org.mockito.internal.util.collections.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/8/22 8:54 下午
 */

@Slf4j
@Service
public class OrderReadApiServiceImpl implements OrderReadApiService {

    @Resource
    private OrderBizInfoAtomicService orderBizInfoAtomicService;

    @Resource
    private OrderWareAtomicService orderWareAtomicService;
    @Resource
    private OrderBaseReadService orderBaseReadService;

    @Resource
    private IopOrderRpcService iopOrderRpcService;

    @Resource
    private AsyncTaskExecutor commonTaskExecutor;

    @Resource
    private OrderWaybillRelationAtomicService orderWaybillRelationAtomicService;

    @Resource
    private OrderWaybillDeliveryCustomerAtomicService orderWaybillDeliveryCustomerAtomicService;

    @Resource
    private PurchaseOrderLogAtomicService purchaseOrderLogAtomicService;

    @Resource
    private OrderAtomicService orderAtomicService;

    @Resource
    private OrderLogAtomicService orderLogAtomicService;

    @Resource
    private OrderConsigneeAtomicService orderConsigneeAtomicService;

    @Autowired
    private TdeClientRpcService tdeClientRpcService;


    /**
     * 用于处理订单多仓履约决策中仓库相关操作的原子服务
     */
    @Autowired
    StockInOutOrderWareAtomicService stockInOutOrderWareAtomicService;


    /**
     * 用于处理订单多仓履约决策的原子服务
     */
    @Autowired
    StockInOutOrderAtomicService stockInOutOrderAtomicService;

    @Autowired
    private WarehouseManageService warehouseManageService;

//    @Override
//    @ToolKit(exceptionWrap = true)
//    public DataResponse<List<OrderAssemblyApiResp>> listOrder(OrderReadApiReq req) {
//        DataResponse<List<OrderAssemblyApiResp>> res = DataResponse.success();
//        if(CollectionUtils.isEmpty(req.getOrderId()) && StringUtils.isBlank(req.getPin()) && StringUtils.isBlank(req.getClientCode())){
//            return res;
//        }
//        List<OrderPO> dbRes = orderBaseReadService.listOrder(req);
//        if(CollectionUtils.isNotEmpty(dbRes)){
//            res.setData(OrderConvert.INSTANCE.po2AssemblyDTO(dbRes));
//        }
//        return res;
//    }

    @Override
    public DataResponse<Map<Long, OrderRelationResp>> queryOrderIdByIopOrderIds(Set<Long> iopOrderIds) {

        Map<Long, OrderRelationResp> result = new HashMap<>();
        try {
            if (iopOrderIds.size() > Constant.BATCH_NUM_50) {
                return DataResponse.buildError("查询数量不能大于" + Constant.BATCH_NUM_50);
            }
            List<OrderBizInfoPO> orderBizInfoPOS = orderBizInfoAtomicService.queryOrderIdByIopOrderIds(iopOrderIds);
            if (CollectionUtils.isNotEmpty(orderBizInfoPOS)) {
                Set<Long> orderIds = orderBizInfoPOS.stream().map(OrderBizInfoPO::getOrderId).collect(Collectors.toSet());
                List<OrderPO> orderPOList = orderAtomicService.getOrderPOsByOrderIds(orderIds);
                Map<Long, OrderPO> orderPOMap = orderPOList.stream().collect(Collectors.toMap(OrderPO::getOrderId, o -> o));

                result = orderBizInfoPOS.stream().map(o -> {
                    OrderRelationResp orderRelation = new OrderRelationResp();
                    orderRelation.setOrderId(o.getOrderId());
                    orderRelation.setPurchaseOrderId(o.getPurchaseOrderId());
                    orderRelation.setIopOrderId(o.getIopOrderId());
                    orderRelation.setEnterpriseWarehouseId(
                            Optional.ofNullable(orderPOMap.get(o.getOrderId()))
                                    .map(OrderPO::getEnterpriseWarehouseId)
                                    .orElse(""));

                    return orderRelation;
                }).collect(Collectors.toMap(o -> Long.valueOf(o.getIopOrderId()), o -> o));
            }

        } catch (Exception e) {
            log.error("queryOrderIdByIopOrderIds param:{},e:", JSON.toJSONString(iopOrderIds), e);
        }
        return DataResponse.success(result);
    }


    @Override
    public DataResponse<SkuRelationResp> querySkuMappingByOrderAndJdSku(Long iopOrderId, Set<Long> jdSkuIds) {

        SkuRelationResp skuRelationResp = new SkuRelationResp();
        try {
            if (jdSkuIds.size() > Constant.BATCH_NUM_50) {
                return DataResponse.buildError("查询数量不能大于" + Constant.BATCH_NUM_50);
            }
            if (CollectionUtils.isEmpty(jdSkuIds)) {
                return DataResponse.buildError("输入sku列表不能为空！" + iopOrderId);
            }

            log.info("querySkuMappingByOrderAndJdSku request:{},{}", iopOrderId, jdSkuIds);
            //更换国际订单号
            List<OrderBizInfoPO> orderBizInfoPOS = orderBizInfoAtomicService.queryOrderIdByIopOrderIds(Sets.newSet(iopOrderId));
            if (CollectionUtils.isEmpty(orderBizInfoPOS)) {
                return DataResponse.buildError("内贸单号对应的国际订单号不存在！" + iopOrderId);
            }
            Long orderId = orderBizInfoPOS.get(0).getOrderId();

            //根据国际单号和jdSku查询国际sku
            List<OrderWarePO> orderWarePOList = orderWareAtomicService.getOrderWarePOListByOrderId(orderId);
            if (CollectionUtils.isEmpty(orderWarePOList)) {
                return DataResponse.buildError("订单商品记录不存在！" + orderId);
            }

            Map<Long, Long> skuRelation = Maps.newHashMap();
            Map<Long, SkuInfoApiResp> skuInfoRelation = Maps.newHashMap();
            for (OrderWarePO orderWarePO : orderWarePOList) {
                OrderSkuDetailPO skuInfo = JSON.parseObject(orderWarePO.getSkuJsonInfo(), OrderSkuDetailPO.class);
                if (jdSkuIds.contains(skuInfo.getJdSkuId())) {
                    skuRelation.put(skuInfo.getJdSkuId(), orderWarePO.getSkuId());
                    skuInfoRelation.put(skuInfo.getJdSkuId(), OrderConvert.INSTANCE.orderWarePo2Res(orderWarePO));
                }
            }

            skuRelationResp.setOrderId(orderId);
            skuRelationResp.setSkuMappingResult(skuRelation);
            skuRelationResp.setSkuInfoMappingResult(skuInfoRelation);
        } catch (Exception e) {
            log.error("querySkuMappingByOrderAndJdSku param:{},e:", iopOrderId, e);
        }
        return DataResponse.success(skuRelationResp);
    }


    @Override
    public DataResponse<Map<Long, BigDecimal>> sumIopOrderAmount(Map<String, Set<Long>> queryParam) {
        try {
            if (MapUtils.isEmpty(queryParam)) {
                return DataResponse.buildError("查询参数为空");
            }

            Map<Long, BigDecimal> result = new ConcurrentHashMap<>();
            List<CompletableFuture<Void>> futureList = new ArrayList<>();

            for (Map.Entry<String, Set<Long>> entry : queryParam.entrySet()) {
                String key = entry.getKey();
                Set<Long> value = entry.getValue();

                for (Long iopOrderId : value) {
                    CompletableFuture<Void> productNameFuture = CompletableFuture.runAsync(() -> {
                        try {
                            List<QueryOrderOpenResp> orderList = iopOrderRpcService.queryOrderDetail(key, iopOrderId);
                            orderList.forEach(o -> result.put(o.getJdOrderId(), o.getOrderPrice().getOrderTotalPrice()));
                        } catch (Exception e) {
                            log.error("Failed to query order detail for key: {}, iopOrderId: {}", key, iopOrderId, e);
                        }
                    }, commonTaskExecutor);
                    futureList.add(productNameFuture);
                }
            }

            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
            return DataResponse.buildSuccess(result);
        } catch (Exception e) {
            log.error("sumIopOrderAmount param: {}, e:", JSON.toJSONString(queryParam), e);
        }
        return DataResponse.buildError("查询失败");
    }

    @Override
    public DataResponse<List<OrderWaybillResp>> queryOrderDelivery(OrderReadApiReq orderReadApiReq) {
        Set<Long> orderIds = orderReadApiReq.getOrderId();
        if (orderIds.size() > Constant.BATCH_NUM_20) {
            return DataResponse.buildError("orderIds超过最大限制20");
        }
        List<OrderWaybillRelationPO> orderWaybillRelationList = orderWaybillRelationAtomicService.batchByOrderId(orderIds);
        if (CollectionUtils.isEmpty(orderWaybillRelationList)) {
            return DataResponse.success();
        }
        List<OrderWaybillResp> resultData = new ArrayList<>();
        Map<String, List<OrderWaybillRelationPO>> waybillMap = orderWaybillRelationList.stream().collect(Collectors.groupingBy(OrderWaybillRelationPO::getWaybillNum));
        List<OrderWaybillDeliveryCustomerPO> waybillDeliveryCustomerList = orderWaybillDeliveryCustomerAtomicService.batchBywayBillNum(waybillMap.keySet(), SignStatusEnum.PASS.getCode());
        waybillDeliveryCustomerList.forEach(w -> {
            List<OrderWaybillRelationPO> list = waybillMap.get(w.getWaybillNum());
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(l -> {
                    OrderWaybillResp waybillResp = new OrderWaybillResp();
                    waybillResp.setOrderId(l.getOrderId());
                    waybillResp.setWaybillNum(l.getWaybillNum());
                    waybillResp.setUpdater(w.getUpdater());
                    waybillResp.setDeliveryCustomerDate(w.getDeliveryCustomerDate());
                    waybillResp.setUpdateTime(w.getUpdateTime());
                    waybillResp.setCreator(l.getCreator());
                    waybillResp.setCreateTime(l.getCreateTime());
                    resultData.add(waybillResp);
                });

            }
        });
        return DataResponse.buildSuccess(resultData);
    }

    @Override
    public DataResponse<List<PurchaseOrderLogResp>> queryLocalOrderShipInfo(OrderReadApiReq orderReadApiReq) {
        Set<Long> orderIds = orderReadApiReq.getOrderId();
        if (orderIds.size() > Constant.BATCH_NUM_20) {
            return DataResponse.buildError("orderIds超过最大限制20");
        }
        List<PurchaseOrderLogPO> purchaseOrderLogList = purchaseOrderLogAtomicService.getPurchaseOrderLogPOByOIds(orderIds, PurchaseOrderStatusConstant.SHIPPED.getStatus());
        List<PurchaseOrderLogResp> respList = PurchaseOrderLogConvert.INSTANCE.listPo2Resp(purchaseOrderLogList);
        return DataResponse.success(respList);
    }


    @Override
    public DataResponse<List<OrderInfoOrderApiResp>> queryOrderInfoByOrderIds(OrderReadApiReq orderReadApiReq) {
        Set<Long> orderIds = orderReadApiReq.getOrderId();
        if (orderIds.size() > Constant.BATCH_NUM_20) {
            return DataResponse.buildError("orderIds超过最大限制20");
        }
        List<OrderPO> orderPOList = orderAtomicService.getOrderPOsByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(orderPOList)) {
            return DataResponse.buildError(DataResponseCode.DATA_NULL);
        }
        Map<Long, OrderPO> orderPOMap = orderPOList.stream().collect(Collectors.toMap(OrderPO::getOrderId, Function.identity()));
        List<OrderWarePO> orderWarePOList = orderWareAtomicService.getOrderWarePOList(orderIds);
        Map<Long, List<OrderWarePO>> orderWarePoMap = orderWarePOList.stream().collect(Collectors.groupingBy(OrderWarePO::getOrderId));
        List<OrderInfoOrderApiResp> orderInfoList = OrderConvert.INSTANCE.listPo2Res(orderPOList);
        orderInfoList.forEach(orderInfo -> {
            List<OrderWarePO> orderWarePos = orderWarePoMap.get(orderInfo.getOrderId());
            List<MkuInfoOrderApiResp> mkuInfoOrderApiRespList = OrderMkuConvert.INSTANCE.listPo2mkuInfoOrderApiResp(orderWarePos);
            orderInfo.setMkuInfoList(mkuInfoOrderApiRespList);
            PriceOrderApiResp priceOrderApiResp = new PriceOrderApiResp();
            OrderPO orderPO = orderPOMap.get(orderInfo.getOrderId());
            priceOrderApiResp.setCurrency(orderPO.getCurrency());
            priceOrderApiResp.setOrderTotalPrice(orderPO.getOrderTotalPrice().toPlainString());
            priceOrderApiResp.setWaresSaleTotalPrice(orderPO.getWaresSaleTotalPrice());
            if (Objects.nonNull(orderPO.getOrderTaxes())) {
                priceOrderApiResp.setOrderTaxPrice(orderPO.getOrderTaxes().toPlainString());
            }
            orderInfo.setOrderPrice(priceOrderApiResp);
        });
        return DataResponse.success(orderInfoList);
    }

    @Override
    public DataResponse<List<OrderLogResp>> queryOrderStatusInfo(OrderReadApiReq orderReadApiReq) {
        Set<Long> orderIds = orderReadApiReq.getOrderId();
        if (orderIds.size() > Constant.BATCH_NUM_20) {
            return DataResponse.buildError("orderIds超过最大限制20");
        }

        if (null == orderReadApiReq.getOrderStatus()) {
            return DataResponse.buildError("orderStatus不能为空");
        }

        List<OrderLogPO> orderLogList = orderLogAtomicService.getOrderLogByOrderIds(orderIds, orderReadApiReq.getOrderStatus());
        List<OrderLogResp> orderLogResp = OrderLogConvert.INSTANCE.listPo2Resp(orderLogList);
        return DataResponse.success(orderLogResp);
    }

    @Override
    public DataResponse<BrOrderTaxApiResp> queryBrOrdersTaxInfo(OrderReadApiReq orderReadApiReq) {
        Set<Long> orderIds = orderReadApiReq.getOrderId();
        if (orderIds.size() > Constant.BATCH_NUM_50) {
            return DataResponse.buildError("orderIds超过最大限制50");
        }

        List<String> languageList = CollectionUtils.isNotEmpty(orderReadApiReq.getLocaleList()) ?
                orderReadApiReq.getLocaleList() : Lists.newArrayList(Constant.DEFAULT_LANGUAGE);
        BrOrderTaxApiResp taxResult = orderBaseReadService.batchQueryBrOrderTax(orderIds, languageList);
        return DataResponse.success(taxResult);
    }


    @Override
    public DataResponse<List<OrderCommonInfoApiResp>> queryCommonOrderInfoByIds(OrderReadApiReq orderReadApiReq) {
        //参数校验
        if (orderReadApiReq.getOrderId().size() > Constant.BATCH_NUM_20) {
            return DataResponse.buildError("orderIds超过最大限制" + Constant.BATCH_NUM_20);
        }
        List<OrderCommonInfoApiResp> orderCommonInfoApiRespList = null;

        try {
            //订单基础数据查询
            List<OrderPO> orderPOList = orderAtomicService.getOrderPOsByOrderIds(orderReadApiReq.getOrderId());
            if (CollectionUtils.isEmpty(orderPOList)) {
                return DataResponse.buildError("订单信息查询为空!");
            }
            List<OrderApiDTO> orderBasicInfoList = OrderConvert.INSTANCE.listOrderPo2OrderApiDto(orderPOList);
            Set<Long> validOrderIds = orderBasicInfoList.stream().map(OrderApiDTO::getOrderId).collect(Collectors.toSet());

            //订单额外信息补充查询
            Map<Long, List<OrderWarePO>> orderWareInfoMap = Collections.emptyMap();
            Map<Long, List<OrderConsigneeApiDTO>> consigneeMap = Collections.emptyMap();
            Set<OrderQueryEnum> queryEnums = orderReadApiReq.getOrderQueryEnums();
            if (CollectionUtils.isNotEmpty(queryEnums)) {
                // 仅当枚举集合中包含WARES时，调用服务获取信息
                if (queryEnums.contains(OrderQueryEnum.WARES)) {
                    orderWareInfoMap = orderWareAtomicService.getOrderWarePOListByOrderId(validOrderIds);
                }

                // 其他枚举值的处理可以在这里继续添加
                if (queryEnums.contains(OrderQueryEnum.CONSIGNEE)) {
                    List<OrderConsigneePO> consigneePOList = orderConsigneeAtomicService.getOrderConsigneePOsByOrderIds(validOrderIds);
                    if (CollectionUtils.isNotEmpty(consigneePOList)) {
                        consigneeMap = consigneePOList.stream().map(c -> {
                            OrderConsigneeApiDTO apiDTO = OrderConsigneeConvert.INSTANCE.po2dto(c);
                            apiDTO.setConsigneeEncrypt(tdeClientRpcService.decrypt(c.getConsigneeEncrypt()));
                            apiDTO.setConsigneeMobileEncrypt(tdeClientRpcService.decrypt(c.getConsigneeMobileEncrypt()));
                            apiDTO.setConsigneeAddressEncrypt(tdeClientRpcService.decrypt(c.getConsigneeAddressEncrypt()));
                            return apiDTO;
                        }).collect(Collectors.groupingBy(OrderConsigneeApiDTO::getOrderId));
                    }
                }
            }

            //结果处理
            orderCommonInfoApiRespList = Lists.newArrayListWithCapacity(validOrderIds.size());
            for (OrderApiDTO orderApiDTO : orderBasicInfoList) {
                OrderCommonInfoApiResp orderCommonInfoApiResp = new OrderCommonInfoApiResp();
                orderCommonInfoApiResp.setOrderApiDTO(orderApiDTO);
                //商品详情补充
                if (!MapUtils.isEmpty(orderWareInfoMap)) {
                    List<OrderWareApiDTO> orderWareApiDTOList = getOrderWareApiDTOS(orderApiDTO, orderWareInfoMap);
                    orderCommonInfoApiResp.setOrderWareApiDTOList(orderWareApiDTOList);
                }
                //地址信息
                if (!MapUtils.isEmpty(consigneeMap)) {
                    orderCommonInfoApiResp.setConsigneeApiDTOList(consigneeMap.get(orderApiDTO.getOrderId()));
                }
                //可扩展-其他信息补充
                orderCommonInfoApiRespList.add(orderCommonInfoApiResp);
            }

        } catch (Exception e) {
            log.error("failed to queryCommonOrderInfoByIds param:{},e:", JSON.toJSONString(orderReadApiReq), e);
            return DataResponse.buildError(DataResponseCode.BUSINESS_ERROR.getCode(), e.getMessage());
        }

        return DataResponse.success(orderCommonInfoApiRespList);
    }

    /**
     * 根据订单API DTO和订单仓库信息映射，获取对应的订单仓库API DTO列表。
     *
     * @param orderApiDTO      订单API DTO对象，包含订单ID等信息。
     * @param orderWareInfpMap 订单仓库信息映射，key为订单ID，value为对应的订单仓库PO列表。
     * @return 对应的订单仓库API DTO列表。
     */
    private List<OrderWareApiDTO> getOrderWareApiDTOS(OrderApiDTO orderApiDTO, Map<Long, List<OrderWarePO>> orderWareInfpMap) {
        List<OrderWarePO> orderWarePOList = orderWareInfpMap.get(orderApiDTO.getOrderId());
        return OrderConvert.INSTANCE.listOrderWarePo2OrderWareApiDto(orderWarePOList);
    }

    /**
     * 根据订单ID列表查询订单及其关联的仓库信息
     *
     * @param orderReadApiReq 包含订单ID列表的请求对象
     * @return 包含订单仓库信息的响应对象
     */
    @Override
    public DataResponse<List<OrderWareApiResp>> queryOrderWareInfoByOrderIds(OrderWareInfoReadApiReq orderReadApiReq, List<String> langList) {
        log.info("queryOrderWareInfoByOrderIds orderReadApiReq:{}", JSON.toJSONString(orderReadApiReq));
        Long orderId = orderReadApiReq.getOrderId();

        OrderPO orderPO = orderAtomicService.getOrderPOById(orderId);
        //订单校验条件：
        //订单号校验不存在，则提示“订单号不存在”；
        //订单号下无任何备货商品信息，则提示“无备货商品”；
        //订单所属备货仓是否等于界面选择的备货仓
        if (orderPO == null) {
            return DataResponse.buildError("订单号不存在！");
        }

        List<OrderWarePO> orderWarePOList = orderWareAtomicService.getOrderWarePOListByOrderId(orderId);
        if(CollectionUtils.isEmpty(orderWarePOList)){
            log.warn("queryOrderWareInfoByOrderIds getOrderWarePOListByOrderId empty, orderId:{}", orderId);
            return DataResponse.buildError("订单商品明细不存在！");
        }

        Set<String> skuIds = orderWarePOList.stream()
                .map(orderWarePO -> String.valueOf(orderWarePO.getSkuId()))
                .collect(Collectors.toSet());
        //商品绑定仓校验
        Map<String, Map<String, WarehouseSkuDTO>> warehouseSkuMap = warehouseManageService.querySkuWarehouseRelationMap(skuIds);
        if(org.springframework.util.CollectionUtils.isEmpty(warehouseSkuMap)){
            log.warn("queryOrderWareInfoByOrderIds querySkuWarehouseRelationMap empty, skuIds:{}", skuIds);
            return DataResponse.buildError("订单无备货商品(商品未绑定备货仓)！");
        }

        //备货品校验
        String inputWarehouseId = orderReadApiReq.getStockUpWarehouseId();
        List<OrderWarePO> stockUpOrderWarePOList = orderWarePOList.stream()
                .filter(checkOrderWareStockModelPredicate(warehouseSkuMap, inputWarehouseId))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockUpOrderWarePOList)) {
            log.warn("queryOrderWareInfoByOrderIds checkOrderWareStockModelPredicate failed, orderReadApiReq:{}", JSON.toJSONString(orderReadApiReq));
            return DataResponse.buildError("订单无备货商品(未匹配到所选备货仓的备货品)！");
        }

        List<StockInOutOrderPO> stockInOutOrderPOList = stockInOutOrderAtomicService.queryByReferOrderNo(orderId.toString(), StockInOutOrderBizTypeEnum.REVERSE_IN.getCode());
        log.info("queryOrderWareInfoByOrderIds#queryByReferOrderNo stockInOutOrderPO:{}", stockInOutOrderPOList);

        Map<Long, Integer> stockInOutOrderWarePOMap = null;
        if (CollectionUtils.isNotEmpty(stockInOutOrderPOList)) {

            Set<String> stockInOutOrderIds = stockInOutOrderPOList.stream().map(StockInOutOrderPO::getStockInOutOrderId).collect(Collectors.toSet());

            List<StockInOutOrderWarePO> stockInOutOrderWarePOS = stockInOutOrderWareAtomicService.queryByStockInOutOrderId(stockInOutOrderIds);

            stockInOutOrderWarePOMap = stockInOutOrderWarePOS.stream().collect(Collectors.toMap(
                    StockInOutOrderWarePO::getSkuId,
                    StockInOutOrderWarePO::getPlanSkuNum,
                    Integer::sum));
        }

        List<OrderWareApiResp> orderWareApiRes = JsonUtil.getJavaObject(stockUpOrderWarePOList, OrderWareApiResp.class);
        for (OrderWareApiResp orderWare : orderWareApiRes) {

            //mku商品名称设置
            if (StringUtils.isNotBlank(orderWare.getMkuJsonInfo())) {
                JSONObject mkuNameJson = ConfigUtils.getJsonByKey(orderWare.getMkuJsonInfo(), "mkuNameMap");
                if (mkuNameJson != null) {
                    orderWare.setMkuName(mkuNameJson.getString(langList.get(0)));

                    Map<String, String> mkuNameMap = Maps.newHashMap();
                    for (String lang : langList) {
                        String mkuName = mkuNameJson.getString(lang);
                        if (mkuName != null) {
                            mkuNameMap.put(lang, mkuName);
                        }
                    }
                    // 设置语言映射
                    orderWare.setMkuNameMap(mkuNameMap);
                }
            }
            orderWare.setMkuNum(orderWare.getMkuNum());
            orderWare.setAvailableStock(orderWare.getMkuNum());

            if (stockInOutOrderWarePOMap != null) {
                Integer planSkuNum = stockInOutOrderWarePOMap.get(orderWare.getSkuId());
                if (planSkuNum != null) {
                    orderWare.setAvailableStock(Math.max(orderWare.getMkuNum() - planSkuNum, 0));
                }
            }
        }

        return DataResponse.success(orderWareApiRes);
    }

    /**
     * 检查订单商品是否符合备货模型条件的谓词，注意：订单商品行上的备货属性不校验，只校验当前商品的实时备货属性
     * @param warehouseSkuMap 仓库SKU映射表，包含SKU与仓库的绑定关系
     * @param inputWarehouseId 界面输入的备货仓库ID
     * @return 返回一个谓词，用于判断订单商品行是否符合备货模型条件
     */
    @NotNull
    private Predicate<OrderWarePO> checkOrderWareStockModelPredicate(Map<String, Map<String, WarehouseSkuDTO>> warehouseSkuMap, String inputWarehouseId) {
        return orderWarePO ->
        {
            String skuIdStr = String.valueOf(orderWarePO.getSkuId());
            //兼容历史订单
            if (StringUtils.isBlank(inputWarehouseId)) {
                return true;
            }

            //订单商品备货仓和绑定的备货仓id校验
            log.info("queryOrderWareInfoByOrderIds checkOrderWareStockModelPredicate商品的绑仓关系, skuId:{},warehouseSkuMap:{}", skuIdStr, JSON.toJSONString(warehouseSkuMap.get(skuIdStr)));
            return warehouseSkuMap.containsKey(skuIdStr) && warehouseSkuMap.get(skuIdStr).containsKey(inputWarehouseId);
        };
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<String, Boolean>> checkOrderExist(BaseBizApiDTO req) {
        Map<String, Boolean> result = new HashMap<>();
        Set<Long> toQueryId = new HashSet<>();
        req.getBizNumList().forEach(orderId->{
            if(NumberUtil.isNumber(orderId)){
                toQueryId.add(Long.valueOf(orderId));
            }else {
                //非数字类型订单id直接判断为非国际单据id
                result.put(orderId,false);
            }
        });
        if(CollectionUtils.isNotEmpty(toQueryId)){
            Map<String, OrderPO> dbItem = orderAtomicService.queryByOrder(toQueryId);
            toQueryId.forEach(orderId-> result.put(String.valueOf(orderId), dbItem.containsKey(String.valueOf(orderId))));
        }
        return DataResponse.success(result);
    }


    public static void main(String[] args) {
        System.out.println(NumberUtil.isNumber("kTAPV"));
    }

}

