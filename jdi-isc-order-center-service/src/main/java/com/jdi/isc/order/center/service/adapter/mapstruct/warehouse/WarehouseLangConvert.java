package com.jdi.isc.order.center.service.adapter.mapstruct.warehouse;

import com.jdi.isc.order.center.domain.warehouse.biz.WarehouseLangVO;
import com.jdi.isc.order.center.domain.warehouse.po.WarehouseLangPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 仓信息多语言表对象转换
 * @Author: wangpeng965
 * @Date: 2024/07/18 11:30
 **/
@Mapper
public interface WarehouseLangConvert {

    WarehouseLangConvert INSTANCE = Mappers.getMapper(WarehouseLangConvert.class);

    WarehouseLangPO vo2Po(WarehouseLangVO vo);

    WarehouseLangVO po2Vo(WarehouseLangPO po);

    List<WarehouseLangVO> listPo2Vo(List<WarehouseLangPO> poList);

    List<WarehouseLangPO> listVo2Po(List<WarehouseLangVO> voList);
}
