package com.jdi.isc.order.center.service.adapter.mapstruct.waybill;

import com.jdi.isc.order.center.api.waybill.biz.*;
import com.jdi.isc.order.center.domain.waybill.biz.*;
import com.jdi.isc.order.center.domain.waybill.po.OrderWaybillDeliveryCustomerPO;
import com.jdi.isc.order.center.domain.waybill.po.OrderWaybillPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 二段运单对象转换
 * @Author: zhaojianguo21
 * @Date: 2024/06/13 18:12
 **/
@Mapper
public interface OrderWaybillConvert {

    OrderWaybillConvert INSTANCE = Mappers.getMapper(OrderWaybillConvert.class);

    OrderWaybillVO sendApiReq2Vo(OrderWaybillSendApiDTO.Request input);

    OrderWaybillPO vo2Po(OrderWaybillVO vo);

    OrderWaybillVO po2Vo(OrderWaybillPO po);

    OrderWaybillSendApiDTO.Response po2SendApiDtoResponseV(OrderWaybillPO po);

    OrderWaybillMqMsgVO po2MsgVo(OrderWaybillPO po);

    List<OrderWaybillVO> listPo2Vo(List<OrderWaybillPO> poList);

    List<OrderWaybillPO> listVo2Po(List<OrderWaybillVO> voList);

    OrderWaybillDeliveryCustomerVO deliverApiReq2Vo(OrderWaybillDeliverApiDTO.Request input);

    BillDeliverySubmitVO deliverBillApiReq2Vo(BillDeliverSubmitApiDTO.Request input);


    /**
     * @function 将订单运单配送客户信息视图对象转换为持久化对象
     * @param vo 订单运单配送客户信息视图对象
     * @returns 返回持久化对象OrderWaybillDeliveryCustomerPO
     */
    OrderWaybillDeliveryCustomerPO deliverCustomerVo2Po(OrderWaybillDeliveryCustomerVO vo);


    OrderWaybillStatusUpdateVO.Request statusUpdateApiDtoReq2Vo(OrderWaybillStatusUpdateApiDTO.Request input);

    OrderWaybillStatusUpdateApiDTO.Response statusUpdateVoRes2Api(OrderWaybillStatusUpdateVO.Response input);

    QueryOrderSignatureFormConditionReq signatureFormConditionReqApi2Vo(QueryOrderSignatureFormConditionReqApiDTO input);

    List<QueryOrderSignatureFormConditionReq> listSignatureFormConditionReqApi2Vo(List<QueryOrderSignatureFormConditionReqApiDTO> input);

    QueryOrderSignatureFormReqRes.Request signatureFormReqApi2Vo(QueryOrderSignatureFormReqResApiDTO.Request input);

    QueryOrderSignatureFormReqResApiDTO.Response signatureFormResVo2Api(QueryOrderSignatureFormReqRes.Response input);

    List<QueryOrderSignatureFormReqResApiDTO.Response> listSignatureFormResVo2Api(List<QueryOrderSignatureFormReqRes.Response> input);
}
