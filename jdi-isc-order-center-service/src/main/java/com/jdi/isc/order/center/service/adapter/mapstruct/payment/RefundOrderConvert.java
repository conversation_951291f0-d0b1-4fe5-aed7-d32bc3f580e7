package com.jdi.isc.order.center.service.adapter.mapstruct.payment;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.payment.biz.RefundOrderApiDTO;
import com.jdi.isc.order.center.api.payment.req.RefundOrderPageReqApiDTO;
import com.jdi.isc.order.center.domain.payment.po.RefundOrderPO;
import com.jdi.isc.order.center.domain.payment.biz.RefundOrderVO;
import com.jdi.isc.order.center.domain.payment.req.RefundOrderPageReqVO;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 退款订单明细对象转换
 * @Author: zhaojianguo21
 * @Date: 2025/08/07 20:15
 **/
@Mapper
public interface RefundOrderConvert {

    RefundOrderConvert INSTANCE = Mappers.getMapper(RefundOrderConvert.class);

    RefundOrderPO vo2Po(RefundOrderVO input);

    RefundOrderVO po2Vo(RefundOrderPO input);

    List<RefundOrderVO> listPo2Vo(List<RefundOrderPO> input);

    List<RefundOrderPO> listVo2Po(List<RefundOrderVO> voinputList);

    /** api jsf实现层使用开始 */
    RefundOrderPageReqVO pageReqApiDto2PageReqVo(RefundOrderPageReqApiDTO input);

    RefundOrderApiDTO refundOrderVo2ApiDto(RefundOrderVO input);

    PageInfo<RefundOrderApiDTO> pageVo2PageApiDto(PageInfo<RefundOrderVO> input);

    /** api jsf实现层使用结束 */

}