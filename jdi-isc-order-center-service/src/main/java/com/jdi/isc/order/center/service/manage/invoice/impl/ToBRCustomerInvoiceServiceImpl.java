package com.jdi.isc.order.center.service.manage.invoice.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.ump.profiler.CallerInfo;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.isc.aggregate.read.api.customer.res.CustomerReadResp;
import com.jdi.isc.aggregate.read.wisp.api.mku.SaleUnitApiService;
import com.jdi.isc.order.center.api.constants.DataResponseCodeConstant;
import com.jdi.isc.order.center.api.constants.OrderSourceCodeConstant;
import com.jdi.isc.order.center.api.constants.order.OrderExtKeyConstant;
import com.jdi.isc.order.center.api.constants.order.OrderWareMkuExtKeyConstant;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.common.exception.BizException;
import com.jdi.isc.order.center.common.utils.DateUtil;
import com.jdi.isc.order.center.domain.config.DuccConfigCountryInfoVO;
import com.jdi.isc.order.center.domain.config.DuccConfigCustomerInvoiceCountryVO;
import com.jdi.isc.order.center.domain.config.DuccConfigCustomerInvoiceRuleVO;
import com.jdi.isc.order.center.domain.config.OrderDuccConfig;
import com.jdi.isc.order.center.domain.enums.YnEnum;
import com.jdi.isc.order.center.domain.enums.customer.CustomerOrderVO;
import com.jdi.isc.order.center.domain.enums.customer.CustomerVO;
import com.jdi.isc.order.center.domain.enums.invoice.CustomerInvoiceApprovalStatus;
import com.jdi.isc.order.center.domain.enums.invoice.CustomerInvoiceSourceCodeEnum;
import com.jdi.isc.order.center.domain.invoice.customer.po.CustomerInvoiceApplyPO;
import com.jdi.isc.order.center.domain.invoice.customer.po.CustomerInvoiceApplySaveBizPO;
import com.jdi.isc.order.center.domain.invoice.customer.po.CustomerInvoiceApplyWarePO;
import com.jdi.isc.order.center.domain.invoice.customer.vo.ApplyInvoiceApiReqVO;
import com.jdi.isc.order.center.domain.invoice.customer.vo.ApplyInvoiceDetailApiReqVO;
import com.jdi.isc.order.center.domain.invoice.customer.vo.CustomerInvoiceApplySubmitReqVO;
import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.domain.order.po.OrderSkuDetailPO;
import com.jdi.isc.order.center.domain.order.po.OrderWareMkuJsonInfoPO;
import com.jdi.isc.order.center.domain.tax.MkuTaxResVO;
import com.jdi.isc.order.center.domain.tax.OrderWareTaxInfoVO;
import com.jdi.isc.order.center.rpc.material.MkuMaterialRpcService;
import com.jdi.isc.order.center.rpc.sku.SkuReadRpcService;
import com.jdi.isc.order.center.rpc.tax.MkuTaxRpcService;
import com.jdi.isc.order.center.service.adapter.mapstruct.invoice.CustomerInvoiceConvert;
import com.jdi.isc.order.center.service.atomic.invoice.CustomerInvoiceApplyWareAtomicService;
import com.jdi.isc.order.center.service.atomic.mku.OrderWareAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderAtomicService;
import com.jdi.isc.order.center.service.manage.common.OrderCommonService;
import com.jdi.isc.order.center.service.manage.common.OrderIdGeneratorService;
import com.jdi.isc.order.center.service.manage.customer.CustomerBaseService;
import com.jdi.isc.order.center.service.manage.invoice.CustomerInvoiceAbstractService;
import com.jdi.isc.order.center.service.manage.invoice.CustomerInvoiceRouteService;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.material.res.MkuMaterialApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("toBRCustomerInvoiceServiceImpl")
@Slf4j
public class ToBRCustomerInvoiceServiceImpl extends CustomerInvoiceAbstractService {


    @Autowired
    private CustomerBaseService customerBaseService;


    @Autowired
    private OrderIdGeneratorService orderIdGeneratorService;

    @Autowired
    private OrderWareAtomicService orderWareAtomicService;

    @Autowired
    private OrderAtomicService orderAtomicService;

    @Autowired
    private MkuMaterialRpcService mkuMaterialRpcService;

    @Resource
    private SaleUnitApiService saleUnitApiService;


    @Autowired
    private OrderDuccConfig orderDuccConfig;


    @Autowired
    private CustomerInvoiceApplyWareAtomicService customerInvoiceApplyWareAtomicService;

    @Autowired
    private MkuTaxRpcService mkuTaxRpcService;

    @Autowired
    private OrderCommonService orderCommonService;

    @Autowired
    private SkuReadRpcService skuReadRpcService;

    /**
     * 根据客户发票申请提交请求、订单列表及订单商品列表构建客户发票申请保存业务对象
     * @param customerInvoiceApplySubmitReqVO 客户发票申请提交请求值对象
     * @param orderPOList 订单持久化对象列表
     * @param orderWarePOList 订单商品持久化对象列表
     * @return 构建完成的客户发票申请保存业务对象
     * https://joyspace.jd.com/pages/zWEaFe1UJYvvbCfByjgn
     */
    @Override
    public CustomerInvoiceApplySaveBizPO submitInvoiceBuildData(CustomerInvoiceApplySubmitReqVO customerInvoiceApplySubmitReqVO, List<OrderPO> orderPOList, List<OrderWarePO> orderWarePOList) {

        //准备基础数据信息
        CustomerInvoiceApplySaveBizPO customerInvoiceApplySaveBizPO = new CustomerInvoiceApplySaveBizPO();
        Map<Long, List<OrderWarePO>> orderWaresMap = orderWarePOList.stream().collect(Collectors.groupingBy(OrderWarePO::getOrderId));
        Map<String, OrderWarePO> orderWaresByUuidMap = orderWarePOList.stream().collect(Collectors.toMap(OrderWarePO::getUuid, Function.identity()));
        Long now = DateUtil.getCurrentTime();


        //开始组装开票信息
        CustomerInvoiceApplyPO customerInvoiceApplyPO = buildCustomerInvoiceApplyPO(customerInvoiceApplySubmitReqVO, orderPOList, orderWarePOList, now);
        // 补充客户配置
        buildCustomerExtInfo(customerInvoiceApplyPO, customerInvoiceApplySubmitReqVO);
        customerInvoiceApplySaveBizPO.setCustomerInvoiceApplyPO(customerInvoiceApplyPO);

        //开始组装开票商品信息
        List<CustomerInvoiceApplyWarePO> customerInvoiceApplyWarePOList = buildCustomerInvoiceApplyWarePOList(customerInvoiceApplyPO, orderPOList, orderWarePOList, orderWaresMap);
        customerInvoiceApplySaveBizPO.setCustomerInvoiceApplyWarePOList(customerInvoiceApplyWarePOList);

        //任何一行商品未税金额为0校验
        Optional<CustomerInvoiceApplyWarePO> any = customerInvoiceApplyWarePOList.stream().filter(warePO -> warePO.getWareSalePrice() == null || warePO.getWareSalePrice().compareTo(BigDecimal.ZERO) <= 0).findAny();
        if(any.isPresent()){
            // 任何一行商品未税金额为0，则提示异常。
            log.info("submitInvoiceBuildData#customerInvoiceApplySubmitReqVO = {}, customerInvoiceApplyWarePOList = {}", JSON.toJSONString(customerInvoiceApplySubmitReqVO), JSON.toJSONString(customerInvoiceApplyWarePOList));
            customerInvoiceApplySaveBizPO.getCustomerInvoiceApplyPO().setApprovalStatus(CustomerInvoiceApprovalStatus.WAIT_APPROVAL_ERROR.getStatus());
        }
        //税率和其他商品金额校验
        //checkTaxes(orderWarePOList,customerInvoiceApplySaveBizPO,orderWaresByUuidMap);

        return customerInvoiceApplySaveBizPO;
    }


    private void buildApplyInvoiceApiReqVO(ApplyInvoiceApiReqVO applyInvoiceApiReqVO, CustomerInvoiceApplyPO customerInvoiceApplyPO, CustomerReadResp customerReadResp){
        applyInvoiceApiReqVO.setOriginalInvFlag(0);
        applyInvoiceApiReqVO.setIoFlag(1);
        applyInvoiceApiReqVO.setReceiverCompanyProperty(null);
        applyInvoiceApiReqVO.setReceiverCompanyNITKU(null);
        applyInvoiceApiReqVO.setFinalConsumerFlag(1);
        applyInvoiceApiReqVO.setCustomerCompanyProperty(null);
        applyInvoiceApiReqVO.setCustomerCompanyNITKU(null);
        applyInvoiceApiReqVO.setFreightPayerFlag(1);
        Long createTime = customerInvoiceApplyPO.getCreateTime();
        String dueAppendDay = customerReadResp.getOrderMap().getOrDefault("dueAppendDay", null);
        if(StringUtils.isBlank(dueAppendDay)){
            log.info("ToBRCustomerInvoiceService, 巴西发票客户dueAppendDay未配置, customerInvoiceApplyPO = {}", JSON.toJSONString(customerInvoiceApplyPO));
            throw new BizException("ToBRCustomerInvoiceService, 巴西发票客户dueAppendDay未配置");
        }
        Date date = DateUtil.addDay(new Date(createTime), Integer.parseInt(dueAppendDay));
        applyInvoiceApiReqVO.setDueDate(DateUtil.formatDay(date));
        applyInvoiceApiReqVO.setApplyUser(customerInvoiceApplyPO.getCreator());
    }

    public void buildCustomerInfo4Apply(ApplyInvoiceApiReqVO applyInvoiceApiReqVO, CustomerInvoiceApplyPO customerInvoiceApplyPO, CustomerReadResp customerReadResp){
        super.buildCustomerInfo4Apply(applyInvoiceApiReqVO, customerInvoiceApplyPO, customerReadResp);
        // by pin 配置
        applyInvoiceApiReqVO.setEmail(customerReadResp.getOrderMap().get("email"));
        if(customerReadResp.getOrderMap().containsKey("cfop")){
            applyInvoiceApiReqVO.setDeliveryAddressFlag(Integer.valueOf(customerReadResp.getOrderMap().get("cfop")));
        }

        if(customerReadResp.getOrderMap().containsKey(customerInvoiceApplyPO.getPin())){
            String pinConfig = customerReadResp.getOrderMap().get(customerInvoiceApplyPO.getPin());
            JSONObject pinJson = JSON.parseObject(pinConfig);
            applyInvoiceApiReqVO.setEmail(pinJson.getString("email"));
            if(customerReadResp.getOrderMap().containsKey("cfop")){
                applyInvoiceApiReqVO.setDeliveryAddressFlag(Integer.valueOf(customerReadResp.getOrderMap().get("cfop")));
            }
        }
    }


    /**
     * 根据国家配置信息、客户发票申请数据和客户信息构建发票申请API请求对象
     * @param countryInfoVO 国家配置信息，包含开票国家/地区的相关配置
     * @param customerInvoiceApplyPO 客户发票申请数据，包含申请发票的具体信息
     * @param customerReadResp 客户信息响应，包含客户的基本信息和开票资料
     * @return 构建好的发票申请API请求对象
     */
    @Override
    public ApplyInvoiceApiReqVO buildApplyInvoiceApiReqVO(DuccConfigCountryInfoVO countryInfoVO, CustomerInvoiceApplyPO customerInvoiceApplyPO, CustomerReadResp customerReadResp) {
        DuccConfigCustomerInvoiceCountryVO customerInvoiceCountryVO = countryInfoVO.getCustomerInvoiceCountryVO();
        ApplyInvoiceApiReqVO applyInvoiceApiReqVO = CustomerInvoiceConvert.INSTANCE.applyConfig2Req(customerInvoiceCountryVO);


        String applyNo = customerInvoiceApplyPO.getApplyId() + Constant.UNDER_LINE + customerInvoiceApplyPO.getVersion();
        applyInvoiceApiReqVO.setApplyNo(applyNo);

        // by pin 配置
        buildCustomerInfo4Apply(applyInvoiceApiReqVO, customerInvoiceApplyPO, customerReadResp);

        applyInvoiceApiReqVO.setCurrencyCode(customerInvoiceApplyPO.getCurrency());
        applyInvoiceApiReqVO.setReqNumber(customerInvoiceApplyPO.getBizId());
        applyInvoiceApiReqVO.setEbsContractNo(customerReadResp.getEbsContractCode());
        applyInvoiceApiReqVO.setSupplyDateBegin(customerReadResp.getOrderMap().get("supplyDateBegin"));
        applyInvoiceApiReqVO.setSupplyDateEnd(customerReadResp.getOrderMap().get("supplyDateEnd"));


        applyInvoiceApiReqVO.setDateOfInvoice(this.getInvoiceDate4ApplyV2(customerInvoiceApplyPO, countryInfoVO));
        if(StringUtils.isBlank(applyInvoiceApiReqVO.getSupplyDateBegin())){
            applyInvoiceApiReqVO.setSupplyDateBegin(this.getInvoiceDay4ApplyV2(customerInvoiceApplyPO, countryInfoVO));
        }
        if(StringUtils.isBlank(applyInvoiceApiReqVO.getSupplyDateEnd())){
            applyInvoiceApiReqVO.setSupplyDateEnd(this.getInvoiceDay4ApplyV2(customerInvoiceApplyPO, countryInfoVO));
        }
        List<ApplyInvoiceDetailApiReqVO> applyInvoiceDetailApiReqVOList = buildDetails(customerInvoiceApplyPO, applyInvoiceApiReqVO, customerInvoiceCountryVO);
        if(CollectionUtils.isEmpty(applyInvoiceDetailApiReqVOList)){
            log.error("巴西发票, applyInvoiceDetailApiReqVOList is null, customerInvoiceApplyPO = {}", JSON.toJSONString(customerInvoiceApplyPO));
            return null;
        }


        buildApplyInvoiceApiReqVO(applyInvoiceApiReqVO, customerInvoiceApplyPO, customerReadResp);


        applyInvoiceApiReqVO.setDetails(applyInvoiceDetailApiReqVOList);
        return applyInvoiceApiReqVO;

    }

    public String getIncomeType(OrderWarePO orderWarePO, Map<Long, String> noNameSkuNameMap){
        OrderWareMkuJsonInfoPO mkuJson = JSON.parseObject(orderWarePO.getMkuJsonInfo(),OrderWareMkuJsonInfoPO.class);
        String orderMkuName = mkuJson.getMkuNameMap().get(LangConstant.LANG_BR);

        if(StringUtils.isBlank(orderMkuName)){
            orderMkuName = noNameSkuNameMap.get(orderWarePO.getSkuId());
        }

        if(StringUtils.isBlank(orderMkuName)){
            log.error("客户开票商品葡语名为空");
            return null;
        }

        String str = orderMkuName;

        return str.length() > Constant.CUSTOMER_INVOICE_NAME_MAX_SIZE ? str.substring(0, Constant.CUSTOMER_INVOICE_NAME_MAX_SIZE) : str;
    }

    private String getBrLineNumber(OrderPO orderPO, OrderWarePO orderWarePO, Map<Long, MkuMaterialApiDTO> mkuMaterialMap){
        String lineNum = orderCommonService.getMkuExtInfo(orderWarePO, OrderWareMkuExtKeyConstant.LINE_NUM);
        if(StringUtils.isNotBlank(lineNum)){
            return lineNum;
        }
        return getMaterialId(orderWarePO, mkuMaterialMap);

    }

    private String getCst(OrderWarePO orderWarePO, Map<Long, MkuTaxResVO> skuCstMap){
        OrderWareMkuJsonInfoPO orderWareMkuJsonInfoPO = JSON.parseObject(orderWarePO.getMkuJsonInfo(),OrderWareMkuJsonInfoPO.class);
        if(StringUtils.isNotBlank(orderWareMkuJsonInfoPO.getCst())){
            return orderWareMkuJsonInfoPO.getCst();
        }

        MkuTaxResVO mkuTaxResVO = skuCstMap.get(orderWarePO.getMkuId());
        if(mkuTaxResVO != null && StringUtils.isNotBlank(mkuTaxResVO.getCst())){
            return mkuTaxResVO.getCst();
        }

        return null;
    }

    /**
     * 构建发票申请明细信息列表
     * @param customerInvoiceApplyPO 客户发票申请主表信息
     * @param applyInvoiceApiReqVO 发票申请API请求信息
     * @param customerInvoiceCountryVO 客户发票国家配置信息
     * @return 包含发票明细信息的列表，若处理失败返回null
     */
    private List<ApplyInvoiceDetailApiReqVO> buildDetails(CustomerInvoiceApplyPO customerInvoiceApplyPO, ApplyInvoiceApiReqVO applyInvoiceApiReqVO, DuccConfigCustomerInvoiceCountryVO customerInvoiceCountryVO){
        List<CustomerInvoiceApplyWarePO> customerInvoiceApplyWarePOList = customerInvoiceApplyWareAtomicService.queryListByApplyId(customerInvoiceApplyPO.getApplyId());

        List<ApplyInvoiceDetailApiReqVO> applyInvoiceDetailApiReqVOList = new ArrayList<>();
        Set<Long> orderIds = customerInvoiceApplyWarePOList.stream().map(CustomerInvoiceApplyWarePO::getOrderId).collect(Collectors.toSet());
        Map<Long, Map<Long, OrderWarePO>> orderWarePOListMap = orderWareAtomicService.getOrderWarePOListMapByOrderId(orderIds);
        Map<Long, OrderPO> orderPOMap = orderAtomicService.getOrderPOMapByOrderIds(orderIds);

        Map<Long, String> noNameSkuNameMap = queryNoNameSkuIdMap(orderWarePOListMap, customerInvoiceApplyPO.getCountryCode());
        LinkedHashMap<Integer, String> saleUnitMap = skuReadRpcService.querySaleUnitByLang(LangConstant.LANG_EN);

        List<Long> mkuIds = orderWarePOListMap.values().stream().flatMap(map -> map.values().stream()).map(OrderWarePO::getMkuId).collect(Collectors.toList());

        Map<Long, MkuTaxResVO> skuCstMap = mkuTaxRpcService.batchQueryCustomerTax(customerInvoiceApplyPO.getClientCode(), customerInvoiceApplyPO.getCountryCode(), Sets.newHashSet(mkuIds));


        Map<Long, MkuMaterialApiDTO> mkuMaterialMap = mkuMaterialRpcService.queryMkuMaterialMap(mkuIds, customerInvoiceApplyPO.getClientCode());
        for(CustomerInvoiceApplyWarePO po : customerInvoiceApplyWarePOList){
            ApplyInvoiceDetailApiReqVO vo = new ApplyInvoiceDetailApiReqVO();

            OrderPO orderPO = orderPOMap.get(po.getOrderId());
            OrderWarePO orderWarePO = orderWarePOListMap.get(po.getOrderId()).get(po.getMkuId());

            vo.setBrlineNumber(getBrLineNumber(orderPO, orderWarePO, mkuMaterialMap));

            String materialId = getMaterialId(orderWarePO, mkuMaterialMap);
            vo.setCustomerMaterialCode(materialId);

            String substring = getIncomeType(orderWarePO, noNameSkuNameMap);
            if(StringUtils.isBlank(substring)){
                log.error("客户开票商品葡语名为空");
                return null;
            }
            vo.setIncomeType(Lists.newArrayList(substring));


            OrderSkuDetailPO skuJson = JSON.parseObject(orderWarePO.getSkuJsonInfo(), OrderSkuDetailPO.class);
            vo.setNcm(skuJson.getNcmCode());

            String cst = getCst(orderWarePO, skuCstMap);
            if(StringUtils.isBlank(cst)){
                log.error("客户开票CST为空");
                return null;
            }
            vo.setCst(cst);

            Integer saleUnit = skuJson.getSaleUnit();
            if (saleUnit != null){
                vo.setUnit(saleUnitMap.getOrDefault(saleUnit,DEFAULT_UNIT));
            }

            if(StringUtils.isNotBlank(orderPO.getThirdExtInfo()) && orderPO.getThirdExtInfo().contains(sapOrderNo)){
                JSONObject ext = JSON.parseObject(orderPO.getThirdExtInfo());
                vo.setOrderNumber(ext.getString(sapOrderNo));
            }
            if(StringUtils.isBlank(vo.getOrderNumber()) && !orderDuccConfig.getOrderSwitchVO().getBlackInvoiceThirdOrderIdSet().contains(orderPO.getThirdOrderId())){
                vo.setOrderNumber(orderPO.getThirdOrderId());
            }

            vo.setJdParentOrderNumber(String.valueOf(orderPO.getParentOrderId()));
            vo.setJdSubOrderNumber(String.valueOf(orderPO.getOrderId()));
            vo.setSku(String.valueOf(orderWarePO.getSkuId()));
            vo.setMku(String.valueOf(orderWarePO.getMkuId()));
            vo.setQuantity(new BigDecimal(orderWarePO.getMkuNum()));
            vo.setUnitPrice(po.getWareSalePrice());
            vo.setDiscount(BigDecimal.ZERO);
            // 税率
            if(po.getWareTaxRate() != null){
                vo.setTaxRate(po.getWareTaxRate().multiply(new BigDecimal(Constant.ONE_HUNDRED)).setScale(0, RoundingMode.HALF_DOWN));
            }else{
                vo.setTaxRate(BigDecimal.ZERO);
            }
            // 未税订单行金额
            vo.setTaxExclusiveAmount(po.getWareSalePriceTotal());
            // 税额
            vo.setTaxAmount(po.getWareTotalTaxes());
            vo.setTaxInclusiveAmount(po.getWareIncludeTaxSaleTotalPrice());
            vo.setAccruedMonth(applyInvoiceApiReqVO.getDateOfInvoice().substring(0, 7));
            vo.setUnitPriceWithTax(po.getWareIncludeTaxSalePrice());


            // 巴西税
            String valueAddedTaxesInfo = orderWarePO.getValueAddedTaxesInfo();
            if(StringUtils.isBlank(valueAddedTaxesInfo)){
                log.error("客户开票商品税率为空orderId = {}, mkuId = {}", orderWarePO.getOrderId(), orderWarePO.getMkuId());
                return null;
            }

            OrderWareTaxInfoVO orderWareTaxInfoVO = JSON.parseObject(valueAddedTaxesInfo, OrderWareTaxInfoVO.class);
            vo.setIpiTaxRate(orderWareTaxInfoVO.getIpiRate());
            vo.setPisTaxRate(orderWareTaxInfoVO.getPisRate());
            vo.setIcmsTaxRate(orderWareTaxInfoVO.getIcmsRate());
            vo.setConfinsTaxRate(orderWareTaxInfoVO.getCofinsRate());

            vo.setProduceFrom(customerInvoiceCountryVO.getProduceFrom());
            applyInvoiceDetailApiReqVOList.add(vo);

        }

        return applyInvoiceDetailApiReqVOList;
    }









    /**
     * 构建客户发票申请PO对象。
     * @param customerInvoiceApplySubmitReqVO
     * 客户发票申请提交请求VO
     * @param orderPOList 订单PO列表
     * @param orderWarePOList 订单商品PO列表
     * @param now 当前时间戳
     * @return 客户发票申请PO对象
     */
    private CustomerInvoiceApplyPO buildCustomerInvoiceApplyPO(CustomerInvoiceApplySubmitReqVO customerInvoiceApplySubmitReqVO, List<OrderPO> orderPOList, List<OrderWarePO> orderWarePOList, Long now){
        log.info("构建客户发票申请PO对象#buildCustomerInvoiceApplyPO");
        CustomerInvoiceApplyPO customerInvoiceApplyPO = new CustomerInvoiceApplyPO();

        OrderPO firstOrderPO = orderPOList.get(0);
        OrderWarePO orderWarePO = orderWarePOList.get(0);
        LinkedList<String> customerInvoiceList = orderIdGeneratorService.getCustomerInvoiceId(1L);
        customerInvoiceApplyPO.setApplyId(customerInvoiceList.get(0));
        if(StringUtils.isNotBlank(customerInvoiceApplySubmitReqVO.getWaybillNo())){
            customerInvoiceApplyPO.setSourceCode(CustomerInvoiceSourceCodeEnum.WAYBILL.getSourceCode());
            customerInvoiceApplyPO.setBizId(customerInvoiceApplySubmitReqVO.getWaybillNo());
        }else{
            customerInvoiceApplyPO.setSourceCode(CustomerInvoiceSourceCodeEnum.ORDER.getSourceCode());
            customerInvoiceApplyPO.setBizId(customerInvoiceApplyPO.getApplyId());
        }
        customerInvoiceApplyPO.setOrderNum(orderPOList.size());
        customerInvoiceApplyPO.setOrderMkuNum(orderWarePOList.size());

        customerInvoiceApplyPO.setContractNum(firstOrderPO.getContractNum());
        customerInvoiceApplyPO.setClientCode(firstOrderPO.getClientCode());
        customerInvoiceApplyPO.setPin(firstOrderPO.getPin());
        customerInvoiceApplyPO.setCurrency(firstOrderPO.getCurrency());
        customerInvoiceApplyPO.setCountryCode(firstOrderPO.getCountryCode());


        String valueAddedTaxesInfo = orderWarePO.getValueAddedTaxesInfo();
        customerInvoiceApplyPO.setTaxRate(BigDecimal.ZERO);
        if(StringUtils.isNotBlank(valueAddedTaxesInfo)){
            OrderWareTaxInfoVO orderWareTaxInfoVO = JSON.parseObject(valueAddedTaxesInfo, OrderWareTaxInfoVO.class);
            BigDecimal taxRate = orderWareTaxInfoVO.getTaxRate();
            customerInvoiceApplyPO.setTaxRate(taxRate);
        }


        customerInvoiceApplyPO.setApprovalStatus(CustomerInvoiceApprovalStatus.WAIT_APPROVAL.getStatus());
        customerInvoiceApplyPO.setCustomerInvoiceDay(customerInvoiceApplySubmitReqVO.getCustomerInvoiceDay());
        customerInvoiceApplyPO.setInvoiceNo("");
        customerInvoiceApplyPO.setVersion(1);
        customerInvoiceApplyPO.setCreator(customerInvoiceApplySubmitReqVO.getOperator());
        customerInvoiceApplyPO.setUpdater(customerInvoiceApplySubmitReqVO.getOperator());
        customerInvoiceApplyPO.setCreateTime(now);
        customerInvoiceApplyPO.setUpdateTime(now);
        customerInvoiceApplyPO.setYn(YnEnum.YES.getCode());
        return customerInvoiceApplyPO;
    }



    /**
     * 构建客户发票申请商品列表。
     * 4、unitPrice不含税单价取系统中记录未税价
     * 5、taxExclusiveAmount不含税金额为订单行的未税总金额
     * 6、订单行含税金额的计算方案已确认，按订单行含税金额（除了最后一行） =
     * 订单行未税金额 ➗ 发票未税总价 * 发票含税总价 （四舍五入），最后一行的含税总金额 = 发票含税总价 - 其他所有行的含税总金额之和
     * 7、taxAmount订单行税额 =  订单行含税总金额 - 订单行的未税总金额
     * 订单行税额 < 0 的情况下报异常
     * @param customerInvoiceApplyPO 客户发票申请信息对象
     * @param orderPOList 订单信息列表
     * @param orderWarePOList 订单商品信息列表
     * @param orderWaresMap 订单商品信息映射表
     * @return 客户发票申请商品列表
     * 240914193800003
     */
    private List<CustomerInvoiceApplyWarePO> buildCustomerInvoiceApplyWarePOList(CustomerInvoiceApplyPO customerInvoiceApplyPO, List<OrderPO> orderPOList, List<OrderWarePO> orderWarePOList, Map<Long, List<OrderWarePO>> orderWaresMap){
        log.info("构建客户发票申请商品列表#buildCustomerInvoiceApplyWarePOList");
        List<CustomerInvoiceApplyWarePO> customerInvoiceApplyWarePOList = new ArrayList<>(orderWarePOList.size());

        BigDecimal salePriceTotal = BigDecimal.ZERO;
        BigDecimal totalTaxes = BigDecimal.ZERO;

        //计算每个商品 含税信息
        for(OrderPO orderPO : orderPOList){
            List<OrderWarePO> currWarePOList = orderWaresMap.get(orderPO.getOrderId());
            for(OrderWarePO currWarePO : currWarePOList) {

                CustomerInvoiceApplyWarePO customerInvoiceApplyWarePO = copyOrderWarePO(currWarePO, customerInvoiceApplyPO);

                BigDecimal num = new BigDecimal(currWarePO.getMkuNum());
                // 未税销售单价,8位小数
                customerInvoiceApplyWarePO.setWareSalePrice(currWarePO.getSalePrice());
                // 总税金,8位小数
                customerInvoiceApplyWarePO.setWareTotalTaxes(currWarePO.getTaxes());
                // 未税总价,8位小数
                customerInvoiceApplyWarePO.setWareSalePriceTotal(currWarePO.getSalePrice().multiply(num));
                // 总价,8位小数
                customerInvoiceApplyWarePO.setWareIncludeTaxSaleTotalPrice(currWarePO.getMkuAllPrice());
                // 含税销售单价,8位小数
                customerInvoiceApplyWarePO.setWareIncludeTaxSalePrice(currWarePO.getIncludeWareTaxes());
                // 税金单价,8位小数
                customerInvoiceApplyWarePO.setWareTaxes(currWarePO.getValueAddedTaxes());

                customerInvoiceApplyWarePOList.add(customerInvoiceApplyWarePO);

                salePriceTotal = salePriceTotal.add(customerInvoiceApplyWarePO.getWareSalePriceTotal());
                totalTaxes = totalTaxes.add(customerInvoiceApplyWarePO.getWareTotalTaxes());
            }
        }

        // 1、未税金额合计
        customerInvoiceApplyPO.setSalePriceTotal(salePriceTotal);
        // 2、税金合计
        customerInvoiceApplyPO.setTotalTaxes(totalTaxes);
        // 3、价税合计金额
        customerInvoiceApplyPO.setIncludeTaxSaleTotalPrice(salePriceTotal.add(totalTaxes));

        return customerInvoiceApplyWarePOList;
    }

    /**
     * 不同主体的订单不可进行合并——确认：不可以
     * 不同SAP的订单（不同PO）是否可以合并——确认：暂时不可以
     * 同一个SAP单号可以合并，发货单与运单保持一致；——订单维度输入手动控制??? 运单逻辑TODO
     * 是否不同的PIN开票主体会有所不同？——确认：不合并，不同PIN开票主体会不同
     * 卡控规则：接口必填字段无内容卡住无法开票（包括物料编码必填、CST、葡语名称），提示：“开票信息xxx未填写，请补充后提交开票”； ncm
     */
    @Override
    public DataResponse<Boolean> checkParam(CustomerInvoiceApplySubmitReqVO customerInvoiceApplySubmitReqVO,List<OrderPO> orderPOList) {
        // 特殊校验
        Set<String> contractNumSet = new HashSet<>();
        Set<String> sapOrderNoSet = new HashSet<>();
        Set<String> pinSet = new HashSet<>();

        orderPOList.forEach(orderPO -> {
            contractNumSet.add(orderPO.getContractNum());
            pinSet.add(orderPO.getPin());
            String thirdExtInfo = orderPO.getThirdExtInfo();
            if (StringUtils.isNotBlank(thirdExtInfo) && thirdExtInfo.contains(OrderExtKeyConstant.SAP_ORDER_NO)){
                JSONObject thirdExtInfoJson = JSONObject.parseObject(thirdExtInfo);
                sapOrderNoSet.add(thirdExtInfoJson.getString(OrderExtKeyConstant.SAP_ORDER_NO));
            }
        });

        if(contractNumSet.size() != 1){
            log.info("客户开票失败, req = {}, contractNumSet = {}", JSON.toJSON(customerInvoiceApplySubmitReqVO), JSON.toJSON(contractNumSet));
            return DataResponse.error(DataResponseCodeConstant.CUSTOMER_INVOICE_SUBMIT_CONTRACT_FAIL, null);
        }

        if(pinSet.size() != 1){
            log.info("客户开票失败, req = {}, pinSet = {}", JSON.toJSON(customerInvoiceApplySubmitReqVO), JSON.toJSON(contractNumSet));
            return DataResponse.error(DataResponseCodeConstant.CUSTOMER_INVOICE_SUBMIT_ORDER_PIN_FAIL, null);
        }

        if(sapOrderNoSet.size() > 1){
            log.info("客户开票失败, req = {}, sapOrderNoSet = {}", JSON.toJSON(customerInvoiceApplySubmitReqVO), JSON.toJSON(sapOrderNoSet));
            return DataResponse.error(DataResponseCodeConstant.CUSTOMER_INVOICE_SUBMIT_SAP_ORDER_NO_FAIL, null);
        }

        return checkWareParam(customerInvoiceApplySubmitReqVO,orderPOList);
    }

    private DataResponse<Boolean> checkWareParam(CustomerInvoiceApplySubmitReqVO customerInvoiceApplySubmitReqVO,List<OrderPO> orderPOList) {
        Set<Long> noCstMkuIds = new HashSet<>();
        Set<Long> noMaterialCodeMkuIds = new HashSet<>();
        Set<Long> noWareNameMkuIds  = new HashSet<>();

        Set<Long> orderIds = orderPOList.stream().map(OrderPO::getOrderId).collect(Collectors.toSet());
        Map<Long, Map<Long, OrderWarePO>> orderWarePOListMap = orderWareAtomicService.getOrderWarePOListMapByOrderId(orderIds);

        List<OrderWarePO> orderWarePOList = new ArrayList<>();
        orderWarePOListMap.values().forEach(map -> {
            orderWarePOList.addAll(map.values());
        });

        List<Long> mkuIds = orderWarePOList.stream().map(OrderWarePO::getMkuId).collect(Collectors.toList());
        OrderPO firstOrderPO = orderPOList.get(0);
        Map<Long, MkuMaterialApiDTO> mkuMaterialMap = mkuMaterialRpcService.queryMkuMaterialMap(mkuIds, firstOrderPO.getClientCode());
        Map<Long, MkuTaxResVO> skuCstMap = mkuTaxRpcService.batchQueryCustomerTax(firstOrderPO.getClientCode(), firstOrderPO.getCountryCode(), Sets.newHashSet(mkuIds));

        Map<Long, String> noNameSkuNameMap = queryNoNameSkuIdMap(orderWarePOListMap, firstOrderPO.getCountryCode());

        orderWarePOList.forEach(orderWarePO -> {
            String materialId = getMaterialId(orderWarePO, mkuMaterialMap);
            if(StringUtils.isBlank(materialId)){
                log.info("客户发票-无物料编码，订单号：{}，mkuId：{}, mkuJson = {}", orderWarePO.getOrderId(), orderWarePO.getMkuId(), JSON.toJSONString(orderWarePO));
                noMaterialCodeMkuIds.add(orderWarePO.getMkuId());
            }

            MkuTaxResVO mkuTaxResVO = skuCstMap.get(orderWarePO.getMkuId());
            if(mkuTaxResVO == null || StringUtils.isBlank(mkuTaxResVO.getCst())){
                log.info("客户发票-无cst，订单号：{}，mkuId：{}, mkuJson = {}", orderWarePO.getOrderId(), orderWarePO.getMkuId(), JSON.toJSONString(orderWarePO));
                noCstMkuIds.add(orderWarePO.getMkuId());
            }

            String wareName = getIncomeType(orderWarePO, noNameSkuNameMap);
            if(StringUtils.isBlank(wareName)){
                log.info("客户发票-无wareName，订单号：{}，mkuId：{}, mkuJson = {}", orderWarePO.getOrderId(), orderWarePO.getMkuId(), JSON.toJSONString(orderWarePO));
                noWareNameMkuIds.add(orderWarePO.getMkuId());
            }
        });

        if(!noCstMkuIds.isEmpty()){
            return DataResponse.error(DataResponseCodeConstant.CUSTOMER_INVOICE_SUBMIT_WARE_CST_FAIL, Joiner.on(",").join(noCstMkuIds));
        }
        if(!noMaterialCodeMkuIds.isEmpty()){
            return DataResponse.error(DataResponseCodeConstant.CUSTOMER_INVOICE_SUBMIT_WARE_MATERIAL_CODE_FAIL, Joiner.on(",").join(noMaterialCodeMkuIds));
        }
        if(!noWareNameMkuIds.isEmpty()){
            return DataResponse.error(DataResponseCodeConstant.CUSTOMER_INVOICE_SUBMIT_WARE_NAME_BR_FAIL, Joiner.on(",").join(noWareNameMkuIds));
        }

        return DataResponse.success(true);
    }

}
