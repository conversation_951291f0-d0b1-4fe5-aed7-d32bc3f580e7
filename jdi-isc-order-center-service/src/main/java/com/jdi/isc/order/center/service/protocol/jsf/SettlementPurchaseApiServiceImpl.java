package com.jdi.isc.order.center.service.protocol.jsf;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.common.PageResultApiResp;
import com.jdi.isc.order.center.api.purchaseOrder.SettlementPurchaseApiService;
import com.jdi.isc.order.center.api.purchaseOrder.biz.req.ConfirmInfoReq;
import com.jdi.isc.order.center.api.purchaseOrder.biz.req.ExpenseQueryReq;
import com.jdi.isc.order.center.api.purchaseOrder.biz.req.PurchaseSettlementApiReq;
import com.jdi.isc.order.center.api.purchaseOrder.biz.resp.ExpenseApiResp;
import com.jdi.isc.order.center.api.purchaseOrder.biz.resp.SettlementOrderApiResp;
import com.jdi.isc.order.center.service.manage.purchaseOrder.SettlementPurchaseService;
import com.jdi.isc.order.center.service.manage.settlement.SupplierSettlementsBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@Service
@Slf4j
public class SettlementPurchaseApiServiceImpl implements SettlementPurchaseApiService {


    @Resource
    SettlementPurchaseService settlementPurchaseService;

    @Resource
    SupplierSettlementsBaseService supplierSettlementsBaseService;


    /**
     * 生成购买流水记录。
     * @param purchaseOrderId 购买订单ID。
     * @return Boolean 类型的 DataResponse 对象，表示生成流水记录的结果。
     */
    @Override
    public DataResponse<Boolean> generatePurchaseStream(String purchaseOrderId) {
        return settlementPurchaseService.generatePurchaseStream(purchaseOrderId);
    }

    /**
     * 生成购买流水记录。
     * @param purchaseOrderId 购买订单ID。
     * @return Boolean 类型的 DataResponse 对象，表示生成流水记录的结果。
     */
    @Override
    public DataResponse<Boolean> repeatGeneratePurchaseStream(String purchaseOrderId) {
        return settlementPurchaseService.repeatGeneratePurchaseStream(purchaseOrderId);
    }

    /**
     * 推送采购流水到结算系统
     * @param bizCode 业务编码
     * @return 推送结果
     */
    @Override
    public DataResponse<Boolean> pushPurchaseStream(String bizCode) {
        return settlementPurchaseService.pushPurchaseStream(bizCode);
    }

    /**
     * 删除指定的采购流水信息。
     * @param purchaseOrderId 采购订单ID，用于标识要删除的流水信息。
     * @return 删除操作的结果，true表示删除成功，false表示删除失败。
     */
    @Override
    public DataResponse<Boolean> deletePurchaseStream(String purchaseOrderId) {
        return settlementPurchaseService.deletePurchaseStream(purchaseOrderId);
    }

    /**
     * 查询结算单页数据
     * @param purchaseSettlementApiReq 结算单查询请求参数
     * @return 结算单页数据响应结果
     */
    @Override
    public DataResponse<PageResultApiResp<SettlementOrderApiResp>> queryPageReadApi(PurchaseSettlementApiReq purchaseSettlementApiReq) {
        return settlementPurchaseService.queryPageReadApi(purchaseSettlementApiReq);
    }

    /**
     * 通过Elasticsearch读取外部API的费用信息。
     * @param expenseQueryReq 费用查询请求参数，包含查询条件等信息。
     * @return 包含查询结果的DataResponse对象。
     */
    @Override
    public DataResponse<List<ExpenseApiResp>> expenseEsReadOuterApi(ExpenseQueryReq expenseQueryReq) {
        return settlementPurchaseService.expenseEsReadOuterApi(expenseQueryReq);
    }

    /**
     * 新增供应商确认状态
     * @param confirmInfoReq 确认信息请求体
     * @return Boolean 类型的 DataResponse 对象，表示操作是否成功
     */
    @Override
    public DataResponse<Boolean> supplierConfirmStatusNew(ConfirmInfoReq confirmInfoReq) {
        return settlementPurchaseService.supplierConfirmStatusNew(confirmInfoReq);
    }



    /**
     * 临时保存授权结算账本。
     * @param json 请求参数
     * @return 结算结果
     */
    @Override
    public DataResponse<String> tmpSaveAuthSettleLedger(JSONObject json) {
        return settlementPurchaseService.tmpSaveAuthSettleLedger(json);
    }

    @Override
    public DataResponse<Boolean> supplierConfirmSettlementInfo(ConfirmInfoReq confirmInfoReq) {
        return supplierSettlementsBaseService.supplierConfirmSettlementInfo(confirmInfoReq);
    }



    /**
     * 标记采购流方法
     * @param purchaseOrderIds 需要标记的采购订单ID集合
     * @param updater 更新操作人标识
     * @return 包含操作结果的响应对象，指示标记是否成功
     */
    @Override
    public DataResponse<Boolean> markPurchaseStream(Set<String> purchaseOrderIds, String updater) {
        return settlementPurchaseService.markPurchaseStream(purchaseOrderIds,updater);
    }



    /**
     * 标记采购流方法
     * @param purchaseOrderIds 需要标记的采购订单ID集合
     * @return 包含操作结果的响应对象，指示标记是否成功
     */
    @Override
    public DataResponse<Boolean> deleteMarkPurchaseStream(Set<String> purchaseOrderIds) {
        return settlementPurchaseService.deleteMarkPurchaseStream(purchaseOrderIds);
    }


}
