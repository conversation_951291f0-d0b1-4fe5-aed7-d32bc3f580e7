package com.jdi.isc.order.center.service.adapter.mapstruct.orderArchive;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.orderArchive.biz.OrderThirdArchiveWarePageApiDTO;
import com.jdi.isc.order.center.api.orderArchive.req.OrderThirdArchiveWareDetailReqApiDTO;
import com.jdi.isc.order.center.api.orderArchive.req.OrderThirdArchiveWareReqApiDTO;
import com.jdi.isc.order.center.api.orderArchive.req.OrderThirdArchiveWareSaveUpdateReqApiDTO;
import com.jdi.isc.order.center.api.orderArchive.res.OrderThirdArchiveWareResApiDTO;
import com.jdi.isc.order.center.domain.orderArchive.biz.OrderThirdArchiveWarePageVO;
import com.jdi.isc.order.center.domain.orderArchive.req.OrderThirdArchiveWareDetailReqVO;
import com.jdi.isc.order.center.domain.orderArchive.req.OrderThirdArchiveWareSaveUpdateReqVO;
import com.jdi.isc.order.center.domain.order.biz.OrderThirdArchiveWareVO;
import com.jdi.isc.order.center.domain.orderArchive.po.OrderThirdArchiveWarePO;
import com.jdi.isc.order.center.domain.orderArchive.req.OrderThirdArchiveWareReqVO;
import com.jdi.isc.order.center.domain.orderArchive.res.OrderThirdArchiveWareResVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 订单三方归档商品对象转换
 * @Author: zhaojianguo21
 * @Date: 2024/12/27 22:12
 **/
@Mapper
public interface OrderThirdArchiveWareConvert {

    OrderThirdArchiveWareConvert INSTANCE = Mappers.getMapper(OrderThirdArchiveWareConvert.class);

    OrderThirdArchiveWarePO orderThirdArchiveWareReqVo2Po(OrderThirdArchiveWareReqVO input);

    OrderThirdArchiveWareResVO orderThirdArchiveWarePo2ResVo(OrderThirdArchiveWarePO po);

    List<OrderThirdArchiveWareResVO> listPo2ResVo(List<OrderThirdArchiveWarePO> poList);

    OrderThirdArchiveWarePO vo2Po(OrderThirdArchiveWareVO vo);

    OrderThirdArchiveWareVO po2Vo(OrderThirdArchiveWarePO po);

    List<OrderThirdArchiveWareVO> listPo2Vo(List<OrderThirdArchiveWarePO> poList);

    List<OrderThirdArchiveWarePO> listVo2Po(List<OrderThirdArchiveWareVO> voList);

    List<OrderThirdArchiveWarePO> listReqVo2Po(List<OrderThirdArchiveWareReqVO> voList);

    List<OrderThirdArchiveWarePageVO.Response> pageListPo2Vo(List<OrderThirdArchiveWarePO> pos);

    /** api jsf实现层使用开始 */
    OrderThirdArchiveWareReqVO orderThirdArchiveWareReqApiDTO2ReqVo(OrderThirdArchiveWareReqApiDTO input);

    OrderThirdArchiveWareResApiDTO orderThirdArchiveWareResVo2ResReqApiDto(OrderThirdArchiveWareResVO input);

    OrderThirdArchiveWarePageVO.Request pageReqApi2PageReqVo(OrderThirdArchiveWarePageApiDTO.Request input);

    PageInfo<OrderThirdArchiveWarePageApiDTO.Response> pageResVo2PageResApi(PageInfo<OrderThirdArchiveWarePageVO.Response> input);

    OrderThirdArchiveWareSaveUpdateReqVO saveUpdateReqApi2ReqVo(OrderThirdArchiveWareSaveUpdateReqApiDTO input);

    OrderThirdArchiveWareDetailReqVO detailReqApi2ReqVo(OrderThirdArchiveWareDetailReqApiDTO input);
    /** api jsf实现层使用结束 */
}