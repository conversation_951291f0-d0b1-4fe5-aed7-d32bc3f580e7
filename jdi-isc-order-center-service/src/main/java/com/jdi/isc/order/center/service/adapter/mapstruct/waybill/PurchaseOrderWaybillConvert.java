package com.jdi.isc.order.center.service.adapter.mapstruct.waybill;

import com.jdi.isc.order.center.api.waybill.biz.PurchaseOrderWaybillEnterApiDTO;
import com.jdi.isc.order.center.api.waybill.biz.PurchaseOrderWaybillOperateApiReq;
import com.jdi.isc.order.center.api.waybill.biz.PurchaseOrderWaybillOperateApiResp;
import com.jdi.isc.order.center.domain.waybill.biz.*;
import com.jdi.isc.order.center.domain.waybill.po.PurchaseOrderWaybillEnterPO;
import com.jdi.isc.order.center.domain.waybill.po.PurchaseOrderWaybillPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 一段运单对象转换
 * @Author: zhaojianguo21
 * @Date: 2024/06/13 15:17
 **/
@Mapper
public interface PurchaseOrderWaybillConvert {

    PurchaseOrderWaybillConvert INSTANCE = Mappers.getMapper(PurchaseOrderWaybillConvert.class);

    PurchaseOrderWaybillPO vo2Po(PurchaseOrderWaybillVO vo);

    PurchaseOrderWaybillVO po2Vo(PurchaseOrderWaybillPO po);

    List<PurchaseOrderWaybillVO> listPo2Vo(List<PurchaseOrderWaybillPO> poList);

    List<PurchaseOrderWaybillPO> listVo2Po(List<PurchaseOrderWaybillVO> voList);

    PurchaseOrderWaybillEnterVO enterApiReq2VO(PurchaseOrderWaybillEnterApiDTO.Request input);

    PurchaseOrderWaybillEnterPO enterVO2PO(PurchaseOrderWaybillEnterVO input);

    PurchaseOrderWaybillDTO waybillPO2DTO(PurchaseOrderWaybillPO waybillPO);

    PurchaseOrderWaybillOperateReq operateApiReq2VO(PurchaseOrderWaybillOperateApiReq input);

    List<PurchaseOrderWaybillOperateApiResp> operateApiResp2VO(List<PurchaseOrderWaybillOperateResp> operateResps);

    com.jdi.isc.order.center.api.purchaseOrder.biz.req.PurchaseOrderWaybillDTO waybillpo2Dto(PurchaseOrderWaybillPO purchaseOrderWaybillPO);
}
