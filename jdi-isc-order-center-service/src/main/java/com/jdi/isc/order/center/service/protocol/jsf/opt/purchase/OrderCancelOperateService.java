package com.jdi.isc.order.center.service.protocol.jsf.opt.purchase;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jd.front.store.core.vo.base.JdiPurchaseOrderReq;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.common.OrderStatusDTO;
import com.jdi.isc.order.center.api.common.SystemInfoOrderApiReq;
import com.jdi.isc.order.center.api.constants.DataResponseCodeConstant;
import com.jdi.isc.order.center.api.constants.OrderOperateTypeConstant;
import com.jdi.isc.order.center.api.constants.OrderStatusConstant;
import com.jdi.isc.order.center.api.constants.OrderTypeConstant;
import com.jdi.isc.order.center.api.opt.biz.OrderOptSaveDTO;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.common.utils.DateUtil;
import com.jdi.isc.order.center.domain.enums.purchase.PurchaseModelEnum;
import com.jdi.isc.order.center.domain.frontOrder.enums.OrderTypeEnum;
import com.jdi.isc.order.center.domain.order.biz.UpdateOrderStatusReqVO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.rpc.iop.IopOrderRpcService;
import com.jdi.isc.order.center.rpc.warehouse.FrontStoreOrderRpcService;
import com.jdi.isc.order.center.service.atomic.order.OrderAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderBizInfoAtomicService;
import com.jdi.isc.order.center.service.manage.order.OrderWriteService;
import com.jdi.isc.product.soa.api.common.LangConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 采购单运维基类
 */
@Slf4j
@Service
public class OrderCancelOperateService extends OrderOperateService {

    @Autowired
    private OrderAtomicService orderAtomicService;

    @Autowired
    private OrderBizInfoAtomicService orderBizInfoAtomicService;

    @Autowired
    private IopOrderRpcService iopOrderRpcService;

    @Autowired
    private FrontStoreOrderRpcService frontStoreOrderRpcService;

    @Autowired
    private OrderWriteService orderWriteService;

    private final static Integer IOP_ORDER_CANCEL_STATUS = 3;


    public DataResponse<Boolean> operate(OrderOptSaveDTO saveDTO){
        Long orderId = Long.valueOf(saveDTO.getBizId());
        OrderPO orderPO = orderAtomicService.getValidOrderPOById(orderId);
        log.info("OrderCancelOperateService operate data:{}", JSONObject.toJSONString(orderPO));
        if (Objects.isNull(orderPO)) {
            log.info("OrderCancelOperateService operate data is null,param:{}", JSONObject.toJSONString(saveDTO));
            return DataResponse.buildError(DataResponseCodeConstant.ORDER_NOT_EXISTS,"订单不存在");
        }

        //前置校验
        Integer orderStatus = orderPO.getOrderStatus();
        DataResponse<Boolean> checkRes = check(orderStatus);
        if (!checkRes.getSuccess()) {
            return checkRes;
        }

        //3.=========== 未妥投=已发货
        if (OrderStatusConstant.SHIPPED.getStatus().equals(orderStatus)){
            JdiPurchaseOrderReq orderReq = new JdiPurchaseOrderReq();
            orderReq.setIntlOrderNo(saveDTO.getBizId());
            //本本直发入库订单类型
            orderReq.setOrderType(getOrderType(orderPO));
            //跨境-直发订单，需要判断内贸段订单是否取消
            if (OrderTypeConstant.CROSS_BORDER == orderPO.getOrderType() && isDireactOrder(orderPO)) {
                //查询内贸单是否取消
                boolean isCancel = checkIopOrderStatusIsCancel(orderId);
                if (!isCancel) {
                    return DataResponse.buildError(DataResponseCodeConstant.CANCEL_INNER_ORDER_STATUS_FAIL,"内贸段订单未取消，请先处理。");
                }
            }
            //取消企配中心是否收货单
            DataResponse<Boolean> response = frontStoreOrderRpcService.cancelFrontOrder(orderReq);
            if (!Boolean.TRUE.equals(response.getSuccess())) {
                return DataResponse.buildError(DataResponseCodeConstant.FRONT_ORDER_CANCEL_FAIL,"企配/集运中心取消失败！");
            }
            //取消订单
            DataResponse<Boolean> cancelRes = cancelOrder(orderPO, saveDTO);
            //保存日志
            super.saveOptLog(saveDTO,cancelRes);
            return cancelRes;
        }

        //4.============ 已确认未发货
        if (OrderTypeConstant.CROSS_BORDER == orderPO.getOrderType()) {
            //跨境-直发订单 取消内贸段订单
            if (isDireactOrder(orderPO)) {
                //查询内贸单是否取消
                boolean isCancel = checkIopOrderStatusIsCancel(orderId);
                if (!isCancel) {
                    return DataResponse.buildError(DataResponseCodeConstant.CANCEL_INNER_ORDER_STATUS_FAIL,"内贸段订单未取消，请先处理。");
                }
            }
        }
        //取消订单
        DataResponse<Boolean> response = cancelOrder(orderPO, saveDTO);
        log.error("OrderCancelOperateService operate param:{},cancelOrder:{}",JSONObject.toJSONString(orderPO),JSONObject.toJSONString(response));
        //保存日志
        super.saveOptLog(saveDTO,response);
        return response;
    }

    /**
     * 判断仓发订单
     * @param orderPO
     * @return
     */
    private boolean isDireactOrder(OrderPO orderPO) {
        return  Objects.isNull(orderPO.getPurchaseModel()) || PurchaseModelEnum.DIRECT_SUPPLY.getCode().equals(orderPO.getPurchaseModel());
    }

    /**
     * 根据备货模式，订单类型 返回对应的集运仓订单类型
     */
    private Integer getOrderType(OrderPO orderPO) {

        if (isDireactOrder(orderPO)) {
            return OrderTypeConstant.LOCAL == orderPO.getOrderType() ?OrderTypeEnum.LOCAL.getCode() :OrderTypeEnum.CODE_5.getCode();
        }
        //寄售、备货 都属于仓发订单
        return  OrderTypeEnum.PURCHASE.getCode();
    }


    /**
     * 未确认状态
     */
    private Set<Integer> unConfirmStatus(){
        Set<Integer> status = new HashSet<>();
        status.add(OrderStatusConstant.SUBMIT_ORDER.getStatus());
        status.add(OrderStatusConstant.PRICE_CONFIRM.getStatus());
        return status;
    }




    private DataResponse<Boolean> cancelOrder(OrderPO orderPO, OrderOptSaveDTO saveDTO){
        SystemInfoOrderApiReq systemInfoOrderApiReq = new SystemInfoOrderApiReq();
        systemInfoOrderApiReq.setLocaleList(Lists.newArrayList(LangConstant.LANG_ZH));
        systemInfoOrderApiReq.setSystemCode(Constant.SYSTEM_CODE);
        systemInfoOrderApiReq.setServiceIp(saveDTO.getServiceIp());
        systemInfoOrderApiReq.setUserIp(saveDTO.getUserIp());

        UpdateOrderStatusReqVO reqVO = getUpdateOrderStatusReqVO(orderPO);
        reqVO.setClientReqExt(systemInfoOrderApiReq);
        reqVO.setOperateErp(saveDTO.getApplicant());
        return orderWriteService.updateOrder(reqVO);
    }

    private UpdateOrderStatusReqVO getUpdateOrderStatusReqVO(OrderPO orderPO) {
        UpdateOrderStatusReqVO reqVO = new UpdateOrderStatusReqVO();
        reqVO.setOrderId(orderPO.getOrderId());
        reqVO.setThirdOrderId(orderPO.getThirdOrderId());
        reqVO.setPin(orderPO.getPin());
        reqVO.setOperateType(OrderOperateTypeConstant.CANCEL_MANAGE);
        reqVO.setSourceCode(orderPO.getSourceCode());
        reqVO.setClientCode(orderPO.getClientCode());
        reqVO.setContractNum(orderPO.getContractNum());
        reqVO.setCurrentTime(DateUtil.getCurrentTime());
        return reqVO;
    }


    private DataResponse<Boolean> check(Integer orderStatus){
        //1.=========== 不重复取消
        if (OrderStatusConstant.CANCEL_ORDER.getStatus().equals(orderStatus)) {
            return DataResponse.buildError(DataResponseCodeConstant.ORDER_INFO_UPDATE_ORDER_STATUS_ERROR,"订单已经是取消状态");
        }
//        if (unConfirmStatus().contains(orderStatus)) {
//            return DataResponse.buildError(DataResponseCodeConstant.ORDER_INFO_UPDATE_ORDER_STATUS_ERROR,"客户可以自行操作取消");
//        }

        //2.=========== 已妥投、已完成 不支持取消
        if (OrderStatusConstant.FINISH_ORDER.getStatus().equals(orderStatus) ||
                OrderStatusConstant.DELIVERY_SUBMITTED.getStatus().equals(orderStatus) ) {
            OrderStatusDTO orderStatusDTO = OrderStatusConstant.valueOf(orderStatus);
            return DataResponse.buildError(DataResponseCodeConstant.ORDER_INFO_UPDATE_ORDER_STATUS_ERROR,String.format("订单状态是:%s,不能取消",orderStatusDTO.getDesc()));
        }
        return DataResponse.success();
    }
}
