package com.jdi.isc.order.center.service.protocol.jsf.customs;

import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.customs.CustomsExportApiService;
import com.jdi.isc.order.center.api.customs.biz.req.CustomsExportReqDTO;
import com.jdi.isc.order.center.api.customs.biz.resp.CustomsExportDTO;
import com.jdi.isc.order.center.service.manage.customs.CustomsExportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CustomsExportApiServiceImpl implements CustomsExportApiService {

    @Autowired
    private CustomsExportService customsExportService;

    @Override
    public DataResponse<CustomsExportDTO> queryCustomsExportDTO(CustomsExportReqDTO customsExportReqDTO) {
        DataResponse<CustomsExportDTO> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.customs.CustomsExportApiServiceImpl.queryCustomsExportDTO");
        try {
            dataResponse = customsExportService.queryCustomsExportDTO(customsExportReqDTO);
        } catch (Exception e) {
            log.error("CustomsExportApiServiceImpl.queryCustomsExportDTO, req = {}, result = {}", JSON.toJSONString(customsExportReqDTO), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.error("CustomsExportApiServiceImpl.queryCustomsExportDTO, req = {}, result = {}", JSON.toJSONString(customsExportReqDTO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }
}
