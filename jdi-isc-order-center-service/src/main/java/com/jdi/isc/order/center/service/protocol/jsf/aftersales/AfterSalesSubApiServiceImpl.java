package com.jdi.isc.order.center.service.protocol.jsf.aftersales;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.aftersales.AfterSalesSubApiService;
import com.jdi.isc.order.center.api.aftersales.req.AfterSalesSubApiDTO;
import com.jdi.isc.order.center.api.aftersales.req.AfterSalesSubPageReqApiDTO;
import com.jdi.isc.order.center.api.aftersales.req.AfterSalesSubProcessDTO;
import com.jdi.isc.order.center.api.aftersales.res.AfterSalesSubApiResp;
import com.jdi.isc.order.center.api.aftersales.res.AfterSalesSubPageResApiDTO;
import com.jdi.isc.order.center.domain.aftersales.biz.AfterSalesSubOperateStatusVo;
import com.jdi.isc.order.center.domain.aftersales.req.AfterSalesSubPageReqVO;
import com.jdi.isc.order.center.domain.aftersales.res.AfterSalesSubPageResVO;
import com.jdi.isc.order.center.service.adapter.mapstruct.aftersale.AfterSalesSubConvert;
import com.jdi.isc.order.center.service.manage.aftersales.AfterSalesSubManageService;
import com.jdi.isc.order.center.service.manage.aftersales.AfterSalesSubOperateStatusService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 售后开放服务
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
@Service
@Slf4j
public class AfterSalesSubApiServiceImpl implements AfterSalesSubApiService {

    @Resource
    private AfterSalesSubManageService afterSalesSubManageService;


    @Autowired
    AfterSalesSubOperateStatusService afterSalesSubOperateStatusService;


    /**
     * 分页查询售后子订单列表
     * @param req 售后子订单分页查询请求参数DTO
     * @return 包含分页信息的售后子订单响应数据
     */
    @ToolKit
    @Override
    public DataResponse<PageInfo<AfterSalesSubPageResApiDTO>> pageAfterSalesSubOrder(AfterSalesSubPageReqApiDTO req) {
        DataResponse<PageInfo<AfterSalesSubPageResVO>> invokeResult = null;
        try {
            AfterSalesSubPageReqVO reqVO = AfterSalesSubConvert.INSTANCE.pageReqApi2PageReqVo(req);
            invokeResult = afterSalesSubManageService.pageAfterSalesSubOrder(reqVO);
            if (!Boolean.TRUE.equals(invokeResult.getSuccess())){
                log.warn("pageAfterSalesSubOrder, invokeResult fail. req={}", JSONObject.toJSONString(req));
                return DataResponse.error("查询失败。"+invokeResult.getMessage());
            }

            PageInfo<AfterSalesSubPageResApiDTO> pageInfoRes = AfterSalesSubConvert.INSTANCE.pageResVo2PageResApi(invokeResult.getData());
            return DataResponse.success(pageInfoRes);
        } catch (Exception e) {
            log.error("pageAfterSalesSubOrder, exception. req={}", JSONObject.toJSONString(req), e);
            return DataResponse.error("发生异常。");
        }finally {
            log.info("pageAfterSalesSubOrder, finally. req={}, invokeResult={}", JSONObject.toJSONString(req), JSONObject.toJSONString(invokeResult));
        }
    }




    /**
     * 获取售后子单详细信息列表
     * @param req 售后子单查询请求参数DTO对象
     * @return 包含售后子单详细信息列表的数据响应对象
     */
    @Override
    public DataResponse<List<AfterSalesSubApiResp>> detailList(AfterSalesSubApiDTO req) {
        return afterSalesSubManageService.detailList(req);
    }






    /**
     * 处理售后子流程请求
     * @param req 售后子流程处理请求参数
     * @return 包含处理结果的响应对象
     */
    @Override
    public DataResponse<Boolean> process(AfterSalesSubProcessDTO req) {
        String operateCode = req.getOperateCode();

        if(StringUtils.isNotBlank(operateCode)){
            AfterSalesSubOperateStatusVo statusByOperateCode = afterSalesSubOperateStatusService.getStatusByOperateCode(operateCode);
            log.info("AfterSalesSubApiServiceImpl#process, operateCode={}, status={}", operateCode, statusByOperateCode);
            if(statusByOperateCode!=null){
                req.setStatus(statusByOperateCode.getStatus());
            }

        }

        return afterSalesSubManageService.process(req);
    }




}
