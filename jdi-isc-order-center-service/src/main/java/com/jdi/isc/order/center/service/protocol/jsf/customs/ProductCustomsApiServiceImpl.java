package com.jdi.isc.order.center.service.protocol.jsf.customs;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.customs.ProductCustomsApiService;
import com.jdi.isc.order.center.api.customs.biz.req.ProductCustomsPageReqDTO;
import com.jdi.isc.order.center.api.customs.biz.req.ProductCustomsUpdateDTO;
import com.jdi.isc.order.center.api.customs.biz.resp.ProductCustomsDTO;
import com.jdi.isc.order.center.domain.config.OrderDuccConfig;
import com.jdi.isc.order.center.domain.customs.biz.ProductCustomsPageReqVO;
import com.jdi.isc.order.center.domain.customs.biz.ProductCustomsUpdateReqVO;
import com.jdi.isc.order.center.domain.customs.biz.ProductCustomsUpdateVO;
import com.jdi.isc.order.center.domain.customs.po.ProductCustomsPO;
import com.jdi.isc.order.center.domain.enums.declaration.ProductCustomsOperateTypeEnum;
import com.jdi.isc.order.center.rpc.sku.SkuReadRpcService;
import com.jdi.isc.order.center.service.adapter.mapstruct.customs.ProductCustomsConvert;
import com.jdi.isc.order.center.service.atomic.customs.ProductCustomsAtomicService;
import com.jdi.isc.order.center.service.manage.customs.ProductCustomsService;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.IscAttributeDimensionEnum;
import com.jdi.isc.product.soa.api.sku.req.GetProductGlobalAttributeReqDTO;
import com.jdi.isc.product.soa.api.sku.req.QuerySkuReqDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuBaseInfoApiDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ProductCustomsApiServiceImpl implements ProductCustomsApiService {

    @Resource
    private ProductCustomsAtomicService productCustomsAtomicService;

    @Resource
    private SkuReadRpcService skuReadRpcService;

    @Autowired
    private ProductCustomsService productCustomsService;

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private OrderDuccConfig orderDuccConfig;

    @Override
    public DataResponse<PageInfo<ProductCustomsDTO>> page(ProductCustomsPageReqDTO pageReqDTO) {
        DataResponse<PageInfo<ProductCustomsDTO>> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.customs.ProductCustomsApiServiceImpl.page." + active);
        try {
            PageInfo<ProductCustomsDTO> pageInfo = new PageInfo<>();
            pageInfo.setIndex(pageReqDTO.getIndex());
            pageInfo.setSize(pageReqDTO.getSize());
            ProductCustomsPageReqVO reqVO = ProductCustomsConvert.INSTANCE.pageReqDto2Vo(pageReqDTO);
            reqVO.setDataIsolationQueryVO(orderDuccConfig.getDataIsolationQueryVO(pageReqDTO.getOperator()));
            Page<ProductCustomsPO> page = productCustomsAtomicService.page(reqVO);
            if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getRecords())) {
                pageInfo.setTotal(0);
                return DataResponse.success(pageInfo);
            }
            pageInfo.setTotal(page.getTotal());
            List<ProductCustomsDTO> records = ProductCustomsConvert.INSTANCE.listProductCustomsVo2Dto(page.getRecords());
            //补充sku其他信息
            addAdditionalSkuInfo(records);
            pageInfo.setRecords(records);
            dataResponse = DataResponse.success(pageInfo);
        } catch (Exception e){
            log.error("ProductCustomsApiServiceImpl.page, req = {}, result = {}", JSON.toJSONString(pageReqDTO), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("ProductCustomsApiServiceImpl.page, req = {}, result = {}", JSON.toJSONString(pageReqDTO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;

    }

    //查询sku基础信息
    private void addAdditionalSkuInfo(List<ProductCustomsDTO> records) {
        Set<String> skuIds = new HashSet<>();
        Set<Long> spuIds = new HashSet<>();
        records.stream().forEach(r -> {
            skuIds.add(String.valueOf(r.getSkuId()));
            spuIds.add(r.getSpuId());
        });
        Map<String, SkuBaseInfoApiDTO> skuMap = querySkuInfo(skuIds);

        //查询sku信息
        records.forEach(r -> {
            SkuBaseInfoApiDTO baseSku = skuMap.get(String.valueOf(r.getSkuId()));
            if (Objects.nonNull(baseSku)) {
                r.setWeight(baseSku.getWeight());
                r.setLength(baseSku.getLength());
                r.setWide(baseSku.getWidth());
                r.setHigh(baseSku.getHeight());
                r.setWeightDataSource(baseSku.getWeightSource());
                Map<String, String> skuNameMap = baseSku.getSkuNameMap();
                if (MapUtils.isNotEmpty(skuNameMap)) {
                    String name = skuNameMap.get(LangConstant.LANG_ZH);
                    if (StringUtils.isBlank(name)) {
                        name = skuNameMap.get(LangConstant.LANG_EN);
                    }
                    r.setSkuName(name);
                }
                r.setSkuImage(baseSku.getMainImg());
                //上下架状态
                r.setProductStatus(baseSku.getSkuStatus());
            }
        });
    }

    private Map<String, Map<String, PropertyApiDTO>>  querySpuInfo(Set<Long> spuIds) {
        GetProductGlobalAttributeReqDTO reqDTO = new GetProductGlobalAttributeReqDTO();
        reqDTO.setSkuIds(spuIds);
        reqDTO.setDimension(IscAttributeDimensionEnum.SPU.getCode());
        return skuReadRpcService.queryProductGlobalAttributeMap(reqDTO);
    }

    private Map<String, SkuBaseInfoApiDTO> querySkuInfo(Set<String> skuIds) {
        QuerySkuReqDTO querySkuReqDTO = new QuerySkuReqDTO();
        querySkuReqDTO.setSkuIds(skuIds);
        return skuReadRpcService.querySkuInfo(querySkuReqDTO);
    }

    @Override
    public DataResponse<Boolean> update(ProductCustomsUpdateDTO updateDTO) {
        DataResponse<Boolean> dataResponse = null;
        DataResponse<List<Long>> resp = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.customs.ProductCustomsApiServiceImpl.update." + active);
        try {
            ProductCustomsUpdateReqVO reqVO = new ProductCustomsUpdateReqVO();
            reqVO.setOperator(updateDTO.getUpdater());
            reqVO.setOperatorTime(updateDTO.getUpdateTime());
            reqVO.setOperateType(ProductCustomsOperateTypeEnum.BATCH_UPDATE.getValue());

            if(CollectionUtils.isNotEmpty(updateDTO.getProductCustomsDTOList())){
                List<ProductCustomsUpdateVO> productCustomsUpdateVOList = updateDTO.getProductCustomsDTOList().stream().map(ProductCustomsConvert.INSTANCE::reqDto2UpdateVo).collect(Collectors.toList());
                reqVO.setProductCustomsVOList(productCustomsUpdateVOList);
            }

            resp = productCustomsService.batchUpdateCustoms(reqVO);
            dataResponse = resp.getSuccess() ? DataResponse.success(true) : DataResponse.error(resp.getCode(), resp.getMessage());
        } catch (Exception e){
            log.error("ProductCustomsApiServiceImpl.update, req = {}, result = {}", JSON.toJSONString(updateDTO), JSON.toJSONString(resp), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("ProductCustomsApiServiceImpl.update, req = {}, result = {}", JSON.toJSONString(updateDTO), JSON.toJSONString(resp));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;

    }

    @Override
    public DataResponse<List<Long>> batchUpdate(ProductCustomsUpdateDTO updateDTO) {
        DataResponse<List<Long>> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.customs.ProductCustomsApiServiceImpl.batchUpdate." + active);
        try {
            ProductCustomsUpdateReqVO reqVO = new ProductCustomsUpdateReqVO();
            reqVO.setOperator(updateDTO.getUpdater());
            reqVO.setOperatorTime(updateDTO.getUpdateTime());
            reqVO.setOperateType(ProductCustomsOperateTypeEnum.BATCH_UPDATE.getValue());

            if(CollectionUtils.isNotEmpty(updateDTO.getProductCustomsDTOList())){
                List<ProductCustomsUpdateVO> productCustomsUpdateVOList = updateDTO.getProductCustomsDTOList().stream().map(ProductCustomsConvert.INSTANCE::reqDto2UpdateVo).collect(Collectors.toList());
                reqVO.setProductCustomsVOList(productCustomsUpdateVOList);
            }
            dataResponse = productCustomsService.batchUpdateCustoms(reqVO);
            return dataResponse;
        } catch (Exception e){
            log.error("ProductCustomsApiServiceImpl.batchUpdate, req = {}, result = {}", JSON.toJSONString(updateDTO), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("ProductCustomsApiServiceImpl.batchUpdate, req = {}, result = {}", JSON.toJSONString(updateDTO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;

    }
}
