package com.jdi.isc.order.center.service.protocol.jsf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jd.ka.gpt.soa.client.order.resp.QueryOrderOpenResp;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.constants.OrderTypeConstant;
import com.jdi.isc.order.center.api.purchaseOrder.PurchaseOrderApiService;
import com.jdi.isc.order.center.api.purchaseOrder.biz.req.*;
import com.jdi.isc.order.center.api.purchaseOrder.biz.resp.PurchaseOrderOperateApiResp;
import com.jdi.isc.order.center.common.utils.ConfigUtils;
import com.jdi.isc.order.center.domain.common.biz.StatusVO;
import com.jdi.isc.order.center.domain.mapstruct.purchaseOrder.PurchaseOrderConvert;
import com.jdi.isc.order.center.domain.mapstruct.purchaseOrder.PurchaseOrderUpdateConvert;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.LocalSplitPurchaseOrderReqVO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.PurchaseOrderVO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.UpdatePurchaseOrderStatusReqVO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.UpdatePurchaseOrdersStatusReqVO;
import com.jdi.isc.order.center.rpc.iop.IopOrderRpcService;
import com.jdi.isc.order.center.service.manage.purchaseOrder.PurchaseOrderWriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PurchaseOrderApiServiceImpl implements PurchaseOrderApiService {

    @Autowired
    private PurchaseOrderWriteService purchaseOrderWriteService;
    @Autowired
    private IopOrderRpcService iopOrderRpcService;


    @Value("${purchaseOrder.orderOperateStatus}")
    private String orderOperateStatusJson;

    @ToolKit
    @Override
    public DataResponse<Boolean> updatePurchaseOrderStatus(UpdatePurchaseOrderStatusApiReq req) {
        DataResponse<Boolean> dataResponse = null;
        try{
            UpdatePurchaseOrderStatusReqVO updatePurchaseOrderStatusReqVO = PurchaseOrderUpdateConvert.INSTANCE.apiReq2voReq(req);
            updatePurchaseOrderStatusReqVO.setClientReqExt(PurchaseOrderUpdateConvert.INSTANCE.req2ApiReq(req));
            return purchaseOrderWriteService.updatePurchaseOrderStatus(updatePurchaseOrderStatusReqVO);
        }catch (Exception e){
            log.error("PurchaseOrderApiServiceImpl.updatePurchaseOrderStatus, req = {}, dataResponse = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse), e);
            return DataResponse.error(DataResponseCode.SYSTEM_ERROR.getMessage());
        }
    }

    @ToolKit
    @Override
    public DataResponse<Boolean> updatePurchaseOrdersStatus(UpdatePurchaseOrdersStatusApiReq req) {
        DataResponse<Boolean> dataResponse = null;
        try{
            UpdatePurchaseOrdersStatusReqVO updatePurchaseOrdersStatusReqVO = PurchaseOrderUpdateConvert.INSTANCE.apiReq2voReq(req);
            updatePurchaseOrdersStatusReqVO.setClientReqExt(PurchaseOrderUpdateConvert.INSTANCE.req2ApiReq(req));
            return purchaseOrderWriteService.updatePurchaseOrdersStatus(updatePurchaseOrdersStatusReqVO);
        }catch (Exception e){
            log.error("PurchaseOrderApiServiceImpl.updatePurchaseOrdersStatus, req = {}, dataResponse = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse), e);
            return DataResponse.error(DataResponseCode.SYSTEM_ERROR.getMessage());
        }
    }

    @ToolKit
    @Override
    public DataResponse<List<PurchaseOrderOperateApiResp>> queryPurchaseOrderOperateList(PurchaseOrderOperateApiReq req) {
        DataResponse<List<PurchaseOrderOperateApiResp>> result = null;
        try {
            JSONObject statusJson = ConfigUtils.getValueByKey(orderOperateStatusJson, req.getSystemCode(), JSONObject.class);
            if(statusJson == null){
                log.error("PurchaseOrderApiServiceImpl.queryPurchaseOrderOperateList, statusJson is null, req = {}", JSON.toJSONString(req));
                return DataResponse.error(DataResponseCode.PARAM_ERROR.getCode(), DataResponseCode.PARAM_ERROR.getMessage());
            }

            List<PurchaseOrderOperateApiResp> respList = statusJson.keySet().stream().map(operate -> {
                PurchaseOrderOperateApiResp resp = new PurchaseOrderOperateApiResp();
                resp.setOperate(operate);
                StatusVO statusVO = statusJson.getObject(operate, StatusVO.class);
                resp.setStatus(statusVO.getStatus());
                resp.setBeforeStatus(statusVO.getBeforeStatus());
                Map<String, String> operateDescMap = statusVO.getOperateDescMap();
                Map<String, String> localOperateDescMap = operateDescMap.entrySet().stream().filter(entry -> req.getLocaleList().contains(entry.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                resp.setOperateDescMap(localOperateDescMap);
                return resp;
            }).collect(Collectors.toList());

            result = DataResponse.success(respList);
        }finally {
            log.error("PurchaseOrderApiServiceImpl.queryPurchaseOrderOperateList, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(result));
        }
        return result;
    }


    @Override
    public DataResponse<Boolean> splitPurchaseOrderLocal(SplitPurchaseOrderLocalReq req) {
        LocalSplitPurchaseOrderReqVO reqVO = PurchaseOrderConvert.INSTANCE.splitPurchaseOrderLocalReq2Vo(req);
        reqVO.setOrderType(OrderTypeConstant.LOCAL);
        return purchaseOrderWriteService.splitPurchaseOrder(reqVO);
    }

    @Override
    @ToolKit
    public DataResponse<Map<Long, List<Long>>> queryIopChildOrderIdMap(Set<Long> iopOrderIds) {
        if (CollectionUtils.isEmpty(iopOrderIds)){
            return DataResponse.error("参数为空");
        }

        Map<Long, List<Long>> resultMap = new HashMap<>();
        for(Long iopOrderId: iopOrderIds){
            List<Long> iopChildOrderIds = new ArrayList<>();
            resultMap.put(iopOrderId, iopChildOrderIds);

            try {
                List<QueryOrderOpenResp> queryOrderOpenRespList = iopOrderRpcService.queryOrderDetail(null, iopOrderId);
                if(CollectionUtils.isEmpty(queryOrderOpenRespList)){
                    continue;
                }
                List<Long> rpcResChildIds = queryOrderOpenRespList.stream().filter(o->iopOrderId.equals(o.getJdOrderId())).findFirst().get().getChildJdOrderIdList();
                iopChildOrderIds.addAll(rpcResChildIds);
            } catch (Exception e) {
                log.error("getIopChildOrderId, exception. iopOrderId={}", iopOrderId, e);
            }
        }

        return DataResponse.success(resultMap);
    }

    @Override
    @ToolKit
    public DataResponse<List<Long>> queryIopChildOrderIdList(Set<Long> iopOrderIds){
        DataResponse<Map<Long, List<Long>>> resMap = queryIopChildOrderIdMap(iopOrderIds);
        if (!resMap.getSuccess()){
            return DataResponse.error(resMap.getMessage());
        }

        List<Long> allChildOrderIds = new ArrayList<>();
        Map<Long, List<Long>> resMapData = resMap.getData();
        resMapData.values().forEach(o->{
            allChildOrderIds.addAll(o);
        });

        return DataResponse.success(allChildOrderIds);
    }

    @Override
    public DataResponse<Boolean> preSplitPurOrder(PurchaseOrderApiDTO purchaseOrderVO) {
        try {
            PurchaseOrderVO purchaseOrderParam = new PurchaseOrderVO();
            purchaseOrderParam.setPurchaseOrderId(purchaseOrderVO.getPurchaseOrderId());
            purchaseOrderParam.setPurchaseOrderType(purchaseOrderVO.getPurchaseOrderType());
            purchaseOrderParam.setUpdater(purchaseOrderVO.getUpdater());
            return purchaseOrderWriteService.preSplitPurOrder(purchaseOrderParam);
        } catch (Exception e) {
            log.error("preSplitPurOrder param:{},e:", JSON.toJSONString(purchaseOrderVO),e);
        }
        return DataResponse.error("服务异常");
    }
}
