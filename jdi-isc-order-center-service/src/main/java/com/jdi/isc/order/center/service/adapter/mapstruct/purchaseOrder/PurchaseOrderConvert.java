package com.jdi.isc.order.center.service.adapter.mapstruct.purchaseOrder;

import com.jdi.isc.order.center.api.finance.purchaseInvoice.res.PurchaseInvoiceOrderApiDTO;
import com.jdi.isc.order.center.api.orderRead.biz.CustomsQueryOrderInfoDTO;
import com.jdi.isc.order.center.api.orderRead.biz.CustomsQueryWareInfoDTO;
import com.jdi.isc.order.center.api.purchaseOrder.biz.req.PurchaseOrderWareReq;
import com.jdi.isc.order.center.api.purchaseOrder.biz.resp.PurchaseOrderWareApiDTO;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.WareLastPurchaseTimeApiDTO;
import com.jdi.isc.order.center.domain.parcel.biz.PurchaseOrderParcelVO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.*;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderWarePO;
import com.jdi.isc.order.center.domain.supplier.biz.SupplierBaseInfoVO;
import com.jdi.isc.vc.soa.api.parcel.biz.PurchaseOrderParcelReadApiDTO;
import com.jdi.isc.vc.soa.api.purchaseOrder.res.PurchaseOrderBasicResApiDTO;
import com.jdi.isc.vc.soa.api.purchaseOrder.res.PurchaseOrderConsigneeInfoRes;
import com.jdi.isc.vc.soa.api.purchaseOrder.res.PurchaseOrderWareRes;
import com.jdi.isc.vc.soa.api.supplier.res.SupplierBaseInfoRes;
import com.jdi.isc.vc.soa.api.waybill.biz.PurchaseOrderWaybillRes;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PurchaseOrderConvert {

    PurchaseOrderConvert INSTANCE = Mappers.getMapper(PurchaseOrderConvert.class);


    PurchaseOrderWrapperMqMsgVO basicResApiDto2MqMsg(PurchaseOrderBasicResApiDTO input);

    List<PurchaseOrderWareVO> wareApiList2VoList(List<PurchaseOrderWareRes> warePoList);

    List<PurchaseOrderParcelVO> parcelApiList2VoList(List<PurchaseOrderParcelReadApiDTO> warePoList);

    PurchaseOrderWareHouseVO consigneeApi2Vo(PurchaseOrderConsigneeInfoRes input);

    PurchaseOrderWaybillVO waybillApi2Vo(PurchaseOrderWaybillRes input);

    SupplierBaseInfoVO supplierBaseInfoApi2Vo(SupplierBaseInfoRes input);

    /**
     * 将 PurchaseOrderPO 列表转换为 PurchaseOrderVO 列表
     */
    List<PurchaseOrderVO> listPo2Vo(List<PurchaseOrderPO> records);


    @Mappings({
            @Mapping(target = "newPurchaseOrderPO", ignore = true),
            @Mapping(target = "newPurchaseOrderWarePOList", ignore = true)
    })
    ChangePurchaseOrderRespVO changePurchaseOrderRespVO2Old(ChangePurchaseOrderRespVO changePurchaseOrderRespVO);

    @Mappings({
            @Mapping(target = "oldPurchaseOrderPO", ignore = true),
            @Mapping(target = "oldPurchaseOrderWarePOList", ignore = true)
    })
    ChangePurchaseOrderRespVO changePurchaseOrderRespVO2New(ChangePurchaseOrderRespVO changePurchaseOrderRespVO);

    PurchaseInvoiceOrderApiDTO purchaseOrderPO2DTO(PurchaseOrderPO purchaseOrderPO);

    PurchaseOrderWareApiDTO purchaseOrderWarePO2DTO(PurchaseOrderWarePO purchaseOrderWarePOList);

    List<PurchaseOrderWareApiDTO> purchaseOrderWarePOList2DTOList(List<PurchaseOrderWarePO> purchaseOrderWarePOList);

    WareLastPurchaseTimeVO.Request wareLastPurchaseTimeApiDTO2VO(WareLastPurchaseTimeApiDTO.Request input);

    WareLastPurchaseTimeApiDTO.Response wareLastPurchaseTimeVO2DTO(WareLastPurchaseTimeVO.Response input);

    List<WareLastPurchaseTimeApiDTO.Response> wareLastPurchaseTimeVoList2DtoList(List<WareLastPurchaseTimeVO.Response> input);

    PurchaseOrderWrapperMqMsgVO purchaseOrderPo2PurchaseOrderWrapperMqMsgVO(PurchaseOrderPO purchaseOrderPO);


    @Mappings({
            @Mapping(target = "orderFinishTime", source = "completeTime"),
            @Mapping(target = "orderTotalPrice", source = "purchaseTotalPrice"),
            @Mapping(target = "mkuNum", source = "skuNum")
    })
    List<CustomsQueryOrderInfoDTO> purchaseOrderPo2CusmtosList(List<PurchaseOrderPO> purchaseOrderList);


    CustomsQueryWareInfoDTO warePo2CusDto(PurchaseOrderWarePO p);

    List<PurchaseOrderWareApiDTO> listWarePo2Api(List<PurchaseOrderWarePO> purchaseOrderWarePOList);
}
