package com.jdi.isc.order.center.service.protocol.jsf.opt.purchase;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.ka.gpt.soa.client.order.resp.QueryOrderOpenResp;
import com.jd.ka.gpt.soa.client.order.resp.StateOrderOpenResp;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.opt.biz.OrderOptDTO;
import com.jdi.isc.order.center.api.opt.biz.OrderOptPageDTO;
import com.jdi.isc.order.center.api.opt.biz.OrderOptSaveDTO;
import com.jdi.isc.order.center.domain.order.po.OrderBizInfoPO;
import com.jdi.isc.order.center.domain.order.po.OrderOptLogPO;
import com.jdi.isc.order.center.rpc.iop.IopOrderRpcService;
import com.jdi.isc.order.center.service.adapter.mapstruct.opt.OrderOptLogConvert;
import com.jdi.isc.order.center.service.atomic.order.OrderBizInfoAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderOptLogAtomicService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 采购单运维基类
 */
@Slf4j
@Service
public class OrderOperateService {


    @Resource
    private OrderOptLogAtomicService orderOptLogAtomicService;


    @Autowired
    private OrderBizInfoAtomicService orderBizInfoAtomicService;

    @Autowired
    private IopOrderRpcService iopOrderRpcService;

    private final static Integer IOP_ORDER_CANCEL_STATUS = 3;


    public DataResponse<Boolean> operate(OrderOptSaveDTO saveDTO){
        return DataResponse.success();
    }

    protected boolean saveOptLog(OrderOptSaveDTO saveDTO, DataResponse<? extends Object> response) {
        OrderOptLogPO logPO = OrderOptLogConvert.INSTANCE.dto2Po(saveDTO);
        logPO.setOptRes(1);
        if (!Boolean.TRUE.equals(response.getSuccess())) {
            logPO.setFailError(response.getMessage());
            logPO.setOptRes(-1);
        }
        logPO.setCreator(logPO.getUpdater());
        logPO.setUpdateTime(System.currentTimeMillis());
        logPO.setCreateTime(logPO.getUpdateTime());
        boolean save = orderOptLogAtomicService.save(logPO);
        if (!save) {
            log.error("saveOptLog fail param:{}", JSON.toJSONString(logPO));
            //ump
        }
        return save;
    }

    public OrderOptDTO detail(OrderOptDTO dto) {
        OrderOptLogPO orderOptLogPO = orderOptLogAtomicService.getById(dto.getId());
       return OrderOptLogConvert.INSTANCE.po2Dto(orderOptLogPO);
    }

    public PageInfo<OrderOptDTO> page(OrderOptPageDTO pageDTO) {
        Page<OrderOptLogPO> page = orderOptLogAtomicService.pageInfo(pageDTO);
        List<OrderOptLogPO> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
          return pageTransform(page, Collections.EMPTY_LIST);
        }

        List<OrderOptDTO> list = OrderOptLogConvert.INSTANCE.listPo2ListDto(records);
        return pageTransform(page,list);
    }

    private PageInfo<OrderOptDTO> pageTransform(Page<OrderOptLogPO> target, List<OrderOptDTO> item){
        PageInfo<OrderOptDTO> output = new PageInfo<>();
        output.setSize(target.getSize());
        output.setIndex(target.getCurrent());
        if(CollectionUtils.isNotEmpty(target.getRecords()) && CollectionUtils.isNotEmpty(item)){
            output.setTotal(target.getTotal());
            output.setRecords(item);
        }
        return output;
    }

    /**
     * iop订单是否取消
     * @param orderId
     * @return
     */
    public boolean checkIopOrderStatusIsCancel(Long orderId){
        OrderBizInfoPO orderBizInfoPO = orderBizInfoAtomicService.getOrderBizInfoPOByOrderId(orderId);
        if (Objects.isNull(orderBizInfoPO) || StringUtils.isBlank(orderBizInfoPO.getIopOrderId())) {
            log.error("orderId:{},无iop 订单",orderId);
            return true;
        }
        return iopOrderIsCancel(orderBizInfoPO);
    }

    public boolean checkIopOrderStatusIsCancel(String purchaseOrderId){
        OrderBizInfoPO orderBizInfoPO = orderBizInfoAtomicService.getOrderBizInfoPOByPurchaseOrderId(purchaseOrderId);
        if (StringUtils.isBlank(orderBizInfoPO.getIopOrderId())) {
            log.error("orderId:{},无iop 订单",purchaseOrderId);
            return true;
        }
        return iopOrderIsCancel(orderBizInfoPO);
    }


    private boolean iopOrderIsCancel(OrderBizInfoPO orderBizInfoPO){
        if (StringUtils.isBlank(orderBizInfoPO.getIopOrderId())) {
            return true;
        }

        Long iopOrderId = Long.valueOf(orderBizInfoPO.getIopOrderId());
        List<QueryOrderOpenResp> iopOrderList = iopOrderRpcService.queryOrderDetail(orderBizInfoPO.getIopPin(), iopOrderId);
        if (CollectionUtils.isNotEmpty(iopOrderList)){
            QueryOrderOpenResp queryOrderOpenResp = iopOrderList.stream().filter(i -> iopOrderId.equals(i.getJdOrderId())).findFirst().orElse(null);
            if (Objects.nonNull(queryOrderOpenResp)) {
                StateOrderOpenResp orderState = queryOrderOpenResp.getOrderState();
                Integer jdOrderState = orderState.getOrderState();
                return IOP_ORDER_CANCEL_STATUS.equals(jdOrderState);
            }
        }
        return false;
    }
}
