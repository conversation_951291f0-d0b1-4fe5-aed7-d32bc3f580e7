package com.jdi.isc.order.center.service.protocol.jsf.config;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.biz.req.WarehouseSkuUnBindReq;
import com.jdi.isc.order.center.api.biz.resp.WarehouseSkuUnBindResp;
import com.jdi.isc.order.center.api.config.OrderCompositeReadApiService;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.domain.enums.purchase.PurchaseModelEnum;
import com.jdi.isc.order.center.domain.forecast.po.ForecastOrderPO;
import com.jdi.isc.order.center.domain.forecast.po.ForecastOrderWarePO;
import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.ofc.stockIoOrder.po.StockInOutOrderPO;
import com.jdi.isc.order.center.domain.ofc.stockIoOrder.po.StockInOutOrderWarePO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.domain.order.po.OrderSkuDetailPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderWarePO;
import com.jdi.isc.order.center.service.atomic.forecast.ForecastOrderAtomicService;
import com.jdi.isc.order.center.service.atomic.forecast.ForecastOrderWareAtomicService;
import com.jdi.isc.order.center.service.atomic.mku.OrderWareAtomicService;
import com.jdi.isc.order.center.service.atomic.ofc.stockIoOrder.StockInOutOrderAtomicService;
import com.jdi.isc.order.center.service.atomic.ofc.stockIoOrder.StockInOutOrderWareAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderAtomicService;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderAtomicService;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderWareAtomicService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @author：xubing82
 * @date：2025/7/9 18:02
 * @description：OrderCompositeReadApiServiceImpl
 */
@Service
@Slf4j
public class OrderCompositeReadApiServiceImpl implements OrderCompositeReadApiService {

    @Resource
    private OrderWareAtomicService orderWareAtomicService;

    @Resource
    private OrderAtomicService orderAtomicService;

    @Resource
    private PurchaseOrderWareAtomicService purchaseOrderWareAtomicService;

    @Resource
    private PurchaseOrderAtomicService purchaseOrderAtomicService;

    @Resource
    private ForecastOrderWareAtomicService forecastOrderWareAtomicService;

    @Resource
    private ForecastOrderAtomicService forecastOrderAtomicService;

    @Resource
    private StockInOutOrderWareAtomicService stockInOutOrderWareAtomicService;

    @Resource
    private StockInOutOrderAtomicService stockInOutOrderAtomicService;


    /**
     * 解绑类型：0-解绑
     */
    private static final int UNBIND = 0;

    /**
     * 解绑类型：1-归档
     */
    private static final int ARCHIVE = 1;


    /**
     * 场景：
     * 1、解绑：只可能有备货仓库存sku；
     * 2、归档：厂直、备货仓库存sku都有
     *
     * @param warehouseSkuUnBindReqs 仓库SKU解绑请求集合，包含需要查询解绑关系的SKU信息
     * @return
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<WarehouseSkuUnBindResp>> queryBillRelationForWarehouseSkuUnbind(Set<WarehouseSkuUnBindReq> warehouseSkuUnBindReqs) {
        //查询批次数量大小校验
        if (warehouseSkuUnBindReqs.size() > Constant.BATCH_NUM_20) {
            return DataResponse.buildError("请求数超过最大限制" + Constant.BATCH_NUM_20);
        }

        //备货仓解绑参数合法性校验
        boolean hasInvalidWarehouseSkuRequest = warehouseSkuUnBindReqs.stream()
                .anyMatch(req -> Objects.equals(UNBIND, req.getUnBindType())
                        && Objects.isNull(req.getWarehouseId()));
        if (hasInvalidWarehouseSkuRequest) {
            return DataResponse.error("备货仓sku解绑查询验证，备货仓ID不能传值为空!");
        }


        // 根据 warehouseId 是否为空将请求参数分为两组
        Map<Boolean, List<WarehouseSkuUnBindReq>> partitionedRequests = warehouseSkuUnBindReqs.stream()
                .collect(Collectors.partitioningBy(req -> Objects.isNull(req.getWarehouseId())
                        && Objects.equals(ARCHIVE, req.getUnBindType())));


        // 处理厂直库存sku单据关联关系校验
        List<WarehouseSkuUnBindReq> factoryStockSkuUnBindRequest = partitionedRequests.get(true);
        List<WarehouseSkuUnBindResp> factoryStockSkuBillRelationValidateResult = null;
        if (CollectionUtils.isNotEmpty(factoryStockSkuUnBindRequest)) {
            //备货仓解绑不会有厂直品，不走此分支
            factoryStockSkuBillRelationValidateResult = processRequestsWithFactoryStockSku(factoryStockSkuUnBindRequest);
        }

        // 处理备货仓库存sku单据关联关系校验
        List<WarehouseSkuUnBindReq> warehouseSkuUnBindRequest = partitionedRequests.get(false);
        List<WarehouseSkuUnBindResp> warehouseSkuBillRelationValidateResult = null;
        if (CollectionUtils.isNotEmpty(warehouseSkuUnBindRequest)) {
            warehouseSkuBillRelationValidateResult = processRequestsWithWarehouseStockSku(warehouseSkuUnBindRequest);
        }


        // 合并处理结果
        List<WarehouseSkuUnBindResp> allResponses = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(factoryStockSkuBillRelationValidateResult)) {
            allResponses.addAll(factoryStockSkuBillRelationValidateResult);
        }

        if (CollectionUtils.isNotEmpty(warehouseSkuBillRelationValidateResult)) {
            allResponses.addAll(warehouseSkuBillRelationValidateResult);
        }

        return DataResponse.success(allResponses);
    }

    /**
     * 处理厂直库存SKU的归档校验请求
     *
     * @param requests 待处理的仓库SKU解绑请求列表
     * @return 处理后的仓库SKU解绑响应列表
     */
    private List<WarehouseSkuUnBindResp> processRequestsWithFactoryStockSku(List<WarehouseSkuUnBindReq> requests) {
        List<WarehouseSkuUnBindResp> factoryStockSkuBillRelationRespList = Lists.newArrayListWithExpectedSize(requests.size());

        Map<Long, List<WarehouseSkuUnBindReq>> factorySkuUnBindReqMap = requests.stream().collect(Collectors.groupingBy(WarehouseSkuUnBindReq::getSkuId));

        Set<Long> inputSkuIds = factorySkuUnBindReqMap.keySet();


        /*
         * 1、本土备货采购单
         * 本土备货采购单在下单时会进行供应商库存预占及扣减，取消时会回退现货；
         */
        List<PurchaseOrderWarePO> purchaseOrderWarePOList = purchaseOrderWareAtomicService.queryPurchaseOrderWareBySkuIds(inputSkuIds);
        Map<Long, List<String>> skuPurchaseOrderIdMap = Collections.emptyMap();
        Map<String, PurchaseOrderPO> unFinishedPurchaseOrderMap = Collections.emptyMap();

        if (CollectionUtils.isNotEmpty(purchaseOrderWarePOList)) {
            //过滤有效备货采购单明细
            skuPurchaseOrderIdMap = purchaseOrderWarePOList.stream()
                    .collect(Collectors.groupingBy(
                            PurchaseOrderWarePO::getSkuId,
                            Collectors.mapping(PurchaseOrderWarePO::getPurchaseOrderId, Collectors.toList())
                    ));

            //查询未到终态的采购单信息
            Set<String> purchaseOrderIds = purchaseOrderWarePOList.stream().map(PurchaseOrderWarePO::getPurchaseOrderId).collect(Collectors.toSet());
            List<PurchaseOrderPO> unFinishedPurchaseOrderList = purchaseOrderAtomicService.queryUnFinishedPurchaseOrdersByIds(purchaseOrderIds);
            if (CollectionUtils.isNotEmpty(unFinishedPurchaseOrderList)) {
                unFinishedPurchaseOrderMap = unFinishedPurchaseOrderList.stream()
                        .collect(Collectors.toMap(PurchaseOrderPO::getPurchaseOrderId, Function.identity()));
            }
        }


        /*
         * 2、厂直订单数据获取
         * 厂直订单在下单时会进行库存预占或扣减，厂直订单取消会回退供应商现货，厂直采购单不会处理库存(因为目前是根据订单创建厂直采购单的，不用重复处理库存)；
         */
        List<OrderWarePO> orderWarePOS = orderWareAtomicService.queryOrderWareBySkuIds(inputSkuIds);
        Map<Long, List<Long>> skuOrderIdMap = Collections.emptyMap();
        Map<Long, OrderPO> unFinishedOrderMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(orderWarePOS)) {
            //过滤有效直发订单明细
            List<OrderWarePO> stockOrderWarePOList = orderWarePOS.stream().filter(getWarePOPredicateForFactorySku(
                    OrderWarePO::getSkuJsonInfo
            )).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(stockOrderWarePOList)) {
                skuOrderIdMap = stockOrderWarePOList.stream()
                        .collect(Collectors.groupingBy(
                                OrderWarePO::getSkuId,
                                Collectors.mapping(OrderWarePO::getOrderId, Collectors.toList())
                        ));


                //查询未到终态的信息
                Set<Long> orderIds = stockOrderWarePOList.stream().map(OrderWarePO::getOrderId).collect(Collectors.toSet());

                List<OrderPO> unFinishedOrderList = orderAtomicService.queryUnFinishedFactoryOrdersByIds(orderIds);
                if (CollectionUtils.isNotEmpty(unFinishedOrderList)) {
                    unFinishedOrderMap = unFinishedOrderList.stream()
                            .collect(Collectors.toMap(OrderPO::getOrderId, Function.identity()));
                }
            }
        }


        /*
         * 3、单据绑定结果信息构建
         */
        for (WarehouseSkuUnBindReq skuUnBindReq : requests) {
            Long skuId = skuUnBindReq.getSkuId();
            Long warehouseId = skuUnBindReq.getWarehouseId();
            WarehouseSkuUnBindResp result = new WarehouseSkuUnBindResp(warehouseId, skuId);
            StringBuilder failedMsg = new StringBuilder();

            // 本土备货采购单检查
            List<String> purchaseOrderIds = skuPurchaseOrderIdMap.get(skuId);
            if (CollectionUtils.isNotEmpty(purchaseOrderIds)) {
                List<String> unFinishedOrderIds = purchaseOrderIds.stream()
                        .filter(unFinishedPurchaseOrderMap::containsKey)
                        .collect(Collectors.toList());

                if (!unFinishedOrderIds.isEmpty()) {
                    String unFinishedOrders = String.join(",", unFinishedOrderIds);
                    failedMsg.append(String.format("存在以下本土备货采购单未完结: %s", unFinishedOrders)).append(",");
                }
            }

            // 直发订单检查
            List<Long> orderIds = skuOrderIdMap.get(skuId);
            if (CollectionUtils.isNotEmpty(orderIds)) {
                List<Long> unFinishedOrderIds = orderIds.stream()
                        .filter(unFinishedOrderMap::containsKey)
                        .collect(Collectors.toList());

                if (!unFinishedOrderIds.isEmpty()) {
                    String unFinishedOrders = unFinishedOrderIds.stream()
                            .map(String::valueOf) // 将 Long 转为 String
                            .collect(Collectors.joining(","));
                    failedMsg.append(String.format("存在以下直发订单未完结: %s", unFinishedOrders)).append(",");
                }
            }


            if (failedMsg.length() > 0) {
                failedMsg.append("不能归档！");
                result.failed(failedMsg.toString());
            }

            factoryStockSkuBillRelationRespList.add(result);
        }


        return factoryStockSkuBillRelationRespList;
    }


    /**
     * 处理备货仓库存SKU解绑或归档请求校验，返回解绑结果列表
     *
     * @param requests 仓库SKU解绑请求列表，包含需要解绑的SKU信息
     * @return 仓库SKU解绑响应列表，包含每个请求的处理结果
     */
    private List<WarehouseSkuUnBindResp> processRequestsWithWarehouseStockSku(List<WarehouseSkuUnBindReq> requests) {

        List<WarehouseSkuUnBindResp> warehouseSkuUnBindRespList = Lists.newArrayListWithExpectedSize(requests.size());

        Map<Long, List<WarehouseSkuUnBindReq>> warehouseSkuUnBindReqMap = requests.stream().collect(Collectors.groupingBy(WarehouseSkuUnBindReq::getSkuId));

        Set<Long> inputSkuIds = warehouseSkuUnBindReqMap.keySet();


        /*
         * 1、备货采购单数据获取
         */
        List<PurchaseOrderWarePO> purchaseOrderWarePOList = purchaseOrderWareAtomicService.queryPurchaseOrderWareBySkuIds(inputSkuIds);
        Map<Long, List<String>> skuPurchaseOrderIdMap = Collections.emptyMap();
        Map<String, PurchaseOrderPO> unFinishedPurchaseOrderMap;
        if (CollectionUtils.isNotEmpty(purchaseOrderWarePOList)) {
            //过滤有效采购单明细
            skuPurchaseOrderIdMap = purchaseOrderWarePOList.stream()
                    .collect(Collectors.groupingBy(
                            PurchaseOrderWarePO::getSkuId,
                            Collectors.mapping(PurchaseOrderWarePO::getPurchaseOrderId, Collectors.toList())
                    ));

            //查询未到终态的采购单信息
            Set<String> purchaseOrderIds = purchaseOrderWarePOList.stream().map(PurchaseOrderWarePO::getPurchaseOrderId).collect(Collectors.toSet());
            List<PurchaseOrderPO> unFinishedPurchaseOrderList = purchaseOrderAtomicService.queryUnFinishedPurchaseOrdersByIds(purchaseOrderIds);
            if (CollectionUtils.isNotEmpty(unFinishedPurchaseOrderList)) {
                unFinishedPurchaseOrderMap = unFinishedPurchaseOrderList.stream()
                        .collect(Collectors.toMap(PurchaseOrderPO::getPurchaseOrderId, Function.identity()));
            } else {
                unFinishedPurchaseOrderMap = Collections.emptyMap();
            }
        } else {
            unFinishedPurchaseOrderMap = Collections.emptyMap();
        }



        /*
         * 2、仓发订单数据获取
         */
        List<OrderWarePO> orderWarePOS = orderWareAtomicService.queryOrderWareBySkuIds(inputSkuIds);
        Map<Long, List<Long>> skuOrderIdMap = Collections.emptyMap();
        Map<Long, OrderPO> unFinishedOrderMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(orderWarePOS)) {
            //过滤有效采购单明细
            List<OrderWarePO> stockOrderWarePOList = orderWarePOS.stream().filter(
                    getWarePOPredicate(
                            warehouseSkuUnBindReqMap,
                            OrderWarePO::getSkuId,
                            OrderWarePO::getSkuJsonInfo)
            ).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(stockOrderWarePOList)) {
                skuOrderIdMap = stockOrderWarePOList.stream()
                        .collect(Collectors.groupingBy(
                                OrderWarePO::getSkuId,
                                Collectors.mapping(OrderWarePO::getOrderId, Collectors.toList())
                        ));


                //查询未到终态的订单信息
                Set<Long> orderIds = stockOrderWarePOList.stream().map(OrderWarePO::getOrderId).collect(Collectors.toSet());

                List<OrderPO> unFinishedOrderList = orderAtomicService.queryUnFinishedStockUpOrdersByIds(orderIds);
                if (CollectionUtils.isNotEmpty(unFinishedOrderList)) {
                    unFinishedOrderMap = unFinishedOrderList.stream()
                            .collect(Collectors.toMap(OrderPO::getOrderId, Function.identity()));
                }
            }
        }


        /*
         * 3、出入库单数据获取
         */
        List<StockInOutOrderWarePO> stockInOutOrderWarePOList = stockInOutOrderWareAtomicService.queryStockInOutOrderWareBySkuIds(inputSkuIds);
        Map<Long, List<String>> skuStockInOutOrderIdMap = Collections.emptyMap();
        Map<String, StockInOutOrderPO> unFinishedStockInOutOrderMap;
        if (CollectionUtils.isNotEmpty(stockInOutOrderWarePOList)) {
            skuStockInOutOrderIdMap = stockInOutOrderWarePOList.stream()
                    .collect(Collectors.groupingBy(
                            StockInOutOrderWarePO::getSkuId,
                            Collectors.mapping(StockInOutOrderWarePO::getStockInOutOrderId, Collectors.toList())
                    ));

            //查询未到终态的出入库订单信息
            Set<String> stockInOutOrderIds = stockInOutOrderWarePOList.stream().map(StockInOutOrderWarePO::getStockInOutOrderId).collect(Collectors.toSet());
            List<StockInOutOrderPO> unFinishedStockInOutOrderList = stockInOutOrderAtomicService.queryUnFinishedStockInOutOrdersByIds(stockInOutOrderIds);
            if (CollectionUtils.isNotEmpty(unFinishedStockInOutOrderList)) {
                unFinishedStockInOutOrderMap = unFinishedStockInOutOrderList.stream()
                        .collect(Collectors.toMap(StockInOutOrderPO::getStockInOutOrderId, Function.identity()));
            } else {
                unFinishedStockInOutOrderMap = Collections.emptyMap();
            }

        } else {
            unFinishedStockInOutOrderMap = Collections.emptyMap();
        }

        /*
         * 4、预报备货单数据获取
         */
        List<ForecastOrderWarePO> forecastOrderWarePOList = forecastOrderWareAtomicService.queryForecastOrderWareBySkuIds(inputSkuIds);
        Map<Long, List<String>> skuForecastOrderIdMap = Collections.emptyMap();
        Map<String, ForecastOrderPO> unFinishedForecastOrderMap;
        if (CollectionUtils.isNotEmpty(forecastOrderWarePOList)) {
            skuForecastOrderIdMap = forecastOrderWarePOList.stream()
                    .collect(Collectors.groupingBy(
                            ForecastOrderWarePO::getSkuId,
                            Collectors.mapping(ForecastOrderWarePO::getForecastOrderId, Collectors.toList())
                    ));

            //查询未到终态的预报备货单信息
            Set<String> forecastOrderIds = forecastOrderWarePOList.stream().map(ForecastOrderWarePO::getForecastOrderId).collect(Collectors.toSet());
            List<ForecastOrderPO> unFinishedForecastOrderList = forecastOrderAtomicService.queryUnFinishedForecastOrdersByIds(forecastOrderIds);
            if (CollectionUtils.isNotEmpty(unFinishedForecastOrderList)) {
                unFinishedForecastOrderMap = unFinishedForecastOrderList.stream()
                        .collect(Collectors.toMap(ForecastOrderPO::getForecastOrderId, Function.identity()));
            } else {
                unFinishedForecastOrderMap = Collections.emptyMap();
            }

        } else {
            unFinishedForecastOrderMap = Collections.emptyMap();
        }


        /*
         * 5、单据绑定结果信息构建
         */
        for (WarehouseSkuUnBindReq skuUnBindReq : requests) {
            Long skuId = skuUnBindReq.getSkuId();
            Long warehouseId = skuUnBindReq.getWarehouseId();
            WarehouseSkuUnBindResp result = new WarehouseSkuUnBindResp(warehouseId, skuId);
            StringBuilder failedMsg = new StringBuilder();

            // 采购单检查
            List<String> purchaseOrderIds = skuPurchaseOrderIdMap.get(skuId);
            if (CollectionUtils.isNotEmpty(purchaseOrderIds)) {

                List<String> unFinishedOrderIds = purchaseOrderIds.stream()
                        .filter(purchaseOrderId ->
                                StringUtils.isNotBlank(purchaseOrderId) &&
                                        unFinishedPurchaseOrderMap.containsKey(purchaseOrderId) &&
                                        Objects.equals(unFinishedPurchaseOrderMap.get(purchaseOrderId).getEnterpriseWarehouseCode(), String.valueOf(warehouseId))
                        )
                        .collect(Collectors.toList());

                if (!unFinishedOrderIds.isEmpty()) {
                    String unFinishedOrders = String.join(",", unFinishedOrderIds);
                    failedMsg.append(String.format("存在以下采购单未完结: %s", unFinishedOrders)).append(",");
                }
            }

            // 仓发订单检查
            List<Long> orderIds = skuOrderIdMap.get(skuId);
            if (CollectionUtils.isNotEmpty(orderIds)) {
                List<Long> unFinishedOrderIds = orderIds.stream()
                        .filter(unFinishedOrderMap::containsKey)
                        .collect(Collectors.toList());

                if (!unFinishedOrderIds.isEmpty()) {
                    String unFinishedOrders = unFinishedOrderIds.stream()
                            .map(String::valueOf) // 将 Long 转为 String
                            .collect(Collectors.joining(","));
                    failedMsg.append(String.format("存在以下仓发订单未完结: %s", unFinishedOrders)).append(",");
                }
            }

            //出入库单检查
            List<String> stockInOutOrderIds = skuStockInOutOrderIdMap.get(skuId);
            if (stockInOutOrderIds != null) {
                List<String> unFinishedStockInOutOrderIds = stockInOutOrderIds.stream()
                        .filter(stockInOutOrderId ->
                                StringUtils.isNotBlank(stockInOutOrderId) &&
                                        unFinishedStockInOutOrderMap.containsKey(stockInOutOrderId) &&
                                        Objects.equals(unFinishedStockInOutOrderMap.get(stockInOutOrderId).getStockUpWarehouseId(), String.valueOf(warehouseId))
                        )
                        .collect(Collectors.toList());

                if (!unFinishedStockInOutOrderIds.isEmpty()) {
                    String unFinishedOrders = String.join(",", unFinishedStockInOutOrderIds);
                    failedMsg.append(String.format("存在以下出入库单未完结: %s", unFinishedOrders)).append(",");
                }
            }


            // 预报备货单检查
            List<String> forecastOrderIds = skuForecastOrderIdMap.get(skuId);
            if (forecastOrderIds != null) {
                List<String> unFinishedForecastOrderIds = forecastOrderIds.stream()
                        .filter(forecastOrderId ->
                                StringUtils.isNotBlank(forecastOrderId) &&
                                        unFinishedForecastOrderMap.containsKey(forecastOrderId) &&
                                        Objects.equals(unFinishedForecastOrderMap.get(forecastOrderId).getEnterpriseWarehouseId(), String.valueOf(warehouseId))
                        )
                        .collect(Collectors.toList());

                if (!unFinishedForecastOrderIds.isEmpty()) {
                    String unFinishedOrders = String.join(",", unFinishedForecastOrderIds);
                    failedMsg.append(String.format("存在以下预报备货单未完结: %s", unFinishedOrders)).append(",");
                }
            }


            if (failedMsg.length() > 0) {
                failedMsg.append("不能解绑或归档！");
                result.failed(failedMsg.toString());
            }

            warehouseSkuUnBindRespList.add(result);
        }

        return warehouseSkuUnBindRespList;
    }


    /**
     * 根据仓库SKU解绑请求映射和两个函数式接口创建断言条件
     *
     * @param warehouseSkuUnBindReqMap 仓库SKU解绑请求映射表，键为SKU ID，值为解绑请求对象
     * @param getSkuIdFunc             从输入对象中提取SKU ID的函数
     * @param getSkuJsonInfoFunc       从输入对象中提取SKU JSON信息的函数
     * @return 返回一个断言条件，用于判断仓库PO对象是否符合解绑条件
     */
    @NotNull
    private static <T> Predicate<T> getWarePOPredicate(
            Map<Long, List<WarehouseSkuUnBindReq>> warehouseSkuUnBindReqMap,
            Function<T, Long> getSkuIdFunc,
            Function<T, String> getSkuJsonInfoFunc) {

        return warePO -> {
            Long skuId = getSkuIdFunc.apply(warePO);
            String skuJsonInfo = getSkuJsonInfoFunc.apply(warePO);
            OrderSkuDetailPO orderSkuDetailPO = JSON.parseObject(skuJsonInfo, OrderSkuDetailPO.class);

            if (Objects.equals(orderSkuDetailPO.getPurchaseModel(), PurchaseModelEnum.DIRECT_SUPPLY.getCode()) || orderSkuDetailPO.getPurchaseModel() == null) {
                return false;
            }

            List<WarehouseSkuUnBindReq> warehouseSkuUnBindReqList = warehouseSkuUnBindReqMap.get(skuId);

            if (CollectionUtils.isNotEmpty(warehouseSkuUnBindReqList)) {
                return warehouseSkuUnBindReqList.stream()
                        .anyMatch(req -> Objects.equals(orderSkuDetailPO.getWarehouseId(), req.getWarehouseId()));
            }

            return false;
        };
    }


    /**
     * 根据商品SKU信息判断是否为本土备货采购单明细
     *
     * @param getSkuJsonInfoFunc 从泛型对象中提取SKU JSON信息的函数
     * @return 返回判断是否为本土备货采购单的断言函数，备货品返回true，直供品返回false
     */
    private static <T> Predicate<T> getWarePOPredicateForLocalStock(
            Function<T, String> getSkuJsonInfoFunc) {

        return warePO -> {
            String skuJsonInfo = getSkuJsonInfoFunc.apply(warePO);
            OrderSkuDetailPO orderSkuDetailPO = JSON.parseObject(skuJsonInfo, OrderSkuDetailPO.class);

            //识别本土备货采购单明细，只要采购单商品是备货品则返回true
            if (Objects.equals(orderSkuDetailPO.getPurchaseModel(), PurchaseModelEnum.DIRECT_SUPPLY.getCode()) || orderSkuDetailPO.getPurchaseModel() == null) {
                return false;
            }

            return true;
        };
    }

    /**
     * 根据商品SKU信息判断是否为直发订单商品的断言生成方法
     *
     * @param getSkuJsonInfoFunc 从商品对象中提取SKU JSON信息的函数式接口
     * @return 返回一个Predicate断言，用于判断商品是否符合直发订单条件
     */
    private static <T> Predicate<T> getWarePOPredicateForFactorySku(
            Function<T, String> getSkuJsonInfoFunc) {

        return warePO -> {
            String skuJsonInfo = getSkuJsonInfoFunc.apply(warePO);
            OrderSkuDetailPO orderSkuDetailPO = JSON.parseObject(skuJsonInfo, OrderSkuDetailPO.class);

            //识别是否为直发订单的商品
            if (Objects.equals(orderSkuDetailPO.getPurchaseModel(), PurchaseModelEnum.DIRECT_SUPPLY.getCode()) || orderSkuDetailPO.getPurchaseModel() == null) {
                return true;
            }

            return false;
        };
    }

}
