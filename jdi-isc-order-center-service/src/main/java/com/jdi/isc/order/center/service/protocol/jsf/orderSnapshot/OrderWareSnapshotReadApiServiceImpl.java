package com.jdi.isc.order.center.service.protocol.jsf.orderSnapshot;

import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.orderSnapshot.OrderWareSnapshotReadApiService;
import com.jdi.isc.order.center.api.orderSnapshot.req.OrderWareSnapshotReqApiDTO;
import com.jdi.isc.order.center.api.orderSnapshot.res.OrderWareSnapshotResApiDTO;
import com.jdi.isc.order.center.domain.orderSnapshot.req.OrderWareSnapshotReqVO;
import com.jdi.isc.order.center.domain.orderSnapshot.res.OrderWareSnapshotResVO;
import com.jdi.isc.order.center.service.adapter.mapstruct.orderSnapshot.OrderWareSnapshotConvert;
import com.jdi.isc.order.center.service.manage.orderSnapshot.OrderWareSnapshotReadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2025/5/21 16:13
 **/
@Slf4j
@Service
public class OrderWareSnapshotReadApiServiceImpl implements OrderWareSnapshotReadApiService {

    @Resource
    private OrderWareSnapshotReadService promiseTimeTextReadService;

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    @ToolKit(umpFlag = false)
    public DataResponse<OrderWareSnapshotResApiDTO> queryWareSnapshot(OrderWareSnapshotReqApiDTO input) {
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.orderSnapshot.OrderWareSnapshotReadApiServiceImpl.queryWareSnapshot." + active);

        DataResponse<OrderWareSnapshotResVO> serviceRes = null;
        try {
            OrderWareSnapshotReqVO reqVO = OrderWareSnapshotConvert.INSTANCE.orderWareSnapshotReqApiDto2Vo(input);
            serviceRes = promiseTimeTextReadService.queryWareSnapshot(reqVO);

            if (!Boolean.TRUE.equals(serviceRes.getSuccess())){
                log.warn("queryWareSnapshot, serviceRes not true, input: {}, serviceRes: {}", input, serviceRes);
                return DataResponse.error(serviceRes.getCode(), serviceRes.getMessage());
            }

            OrderWareSnapshotResVO serviceResData = serviceRes.getData();
            OrderWareSnapshotResApiDTO orderWareSnapshotResApiDTO = OrderWareSnapshotConvert.INSTANCE.orderWareSnapshotResVo2ApiDto(serviceResData);
            return DataResponse.success(orderWareSnapshotResApiDTO);
        } catch (Exception e){
            log.error("queryWareSnapshot, exception, req = {}, rpcRes = {}", JSON.toJSONString(input), JSON.toJSONString(serviceRes), e);
            Profiler.functionError(callerInfo);
            return DataResponse.error("获取时效文案异常");
        } finally {
            log.info("queryWareSnapshot, req = {}, rpcRes = {}", JSON.toJSONString(input), JSON.toJSONString(serviceRes));
            Profiler.registerInfoEnd(callerInfo);
        }

    }
}
