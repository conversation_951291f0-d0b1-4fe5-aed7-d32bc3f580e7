package com.jdi.isc.order.center.service.protocol.jsf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.api.customer.res.CustomerReadResp;
import com.jdi.isc.aggregate.read.wisp.api.mku.SaleUnitApiService;
import com.jdi.isc.order.center.api.common.OrderStatusDTO;
import com.jdi.isc.order.center.api.constants.OrderStatusConstant;
import com.jdi.isc.order.center.api.constants.OrderTypeConstant;
import com.jdi.isc.order.center.api.constants.PurchaseOrderStatusConstant;
import com.jdi.isc.order.center.api.orderRead.OrderCustomsReadApiService;
import com.jdi.isc.order.center.api.orderRead.biz.CustomsQueryOderDTO;
import com.jdi.isc.order.center.api.orderRead.biz.CustomsQueryOrderInfoDTO;
import com.jdi.isc.order.center.api.orderRead.biz.CustomsQueryRecipientInfoDTO;
import com.jdi.isc.order.center.api.orderRead.biz.CustomsQueryWareInfoDTO;
import com.jdi.isc.order.center.api.purchaseOrder.biz.req.PurchaseOrderStatusDTO;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.domain.config.OrderDuccConfig;
import com.jdi.isc.order.center.domain.declaration.po.CustomsDeclarationMainPo;
import com.jdi.isc.order.center.domain.declaration.po.CustomsOrderPo;
import com.jdi.isc.order.center.domain.enums.YnEnum;
import com.jdi.isc.order.center.domain.enums.declaration.BusinessTypeEnum;
import com.jdi.isc.order.center.domain.enums.purchase.PurchaseModelEnum;
import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.order.biz.OrderQueryReqVO;
import com.jdi.isc.order.center.domain.order.po.*;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.DuccConfigContactInfoVO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderConsigneePO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderWarePO;
import com.jdi.isc.order.center.rpc.customer.CustomerRpcService;
import com.jdi.isc.order.center.rpc.logistics.LogisticsWaybillRpc;
import com.jdi.isc.order.center.rpc.sku.SkuReadRpcService;
import com.jdi.isc.order.center.rpc.tde.TdeClientRpcService;
import com.jdi.isc.order.center.rpc.warehouse.WareHouseReadRpcService;
import com.jdi.isc.order.center.service.adapter.mapstruct.order.OrderConvert;
import com.jdi.isc.order.center.service.adapter.mapstruct.purchaseOrder.PurchaseOrderConvert;
import com.jdi.isc.order.center.service.atomic.declaration.CustomsDeclarationMainAtomicService;
import com.jdi.isc.order.center.service.atomic.declaration.CustomsOrderAtomicService;
import com.jdi.isc.order.center.service.atomic.mku.OrderWareAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderBizInfoAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderConsigneeAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderLogAtomicService;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderAtomicService;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderConsigneeAtomicService;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderWareAtomicService;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.sku.res.VendorDTO;
import com.smart.terminal.isc.sdk.vo.WmsWareHouseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
import com.jdi.isc.order.center.common.utils.JsonUtil;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/8/22 8:54 下午
 */

@Slf4j
@Service
public class OrderCustomsReadApiServiceImpl implements OrderCustomsReadApiService {

    @Resource
    private OrderBizInfoAtomicService orderBizInfoAtomicService;

    @Resource
    private OrderWareAtomicService orderWareAtomicService;

    @Resource
    private PurchaseOrderAtomicService purchaseOrderAtomicService;

    @Resource
    private OrderAtomicService orderAtomicService;


    @Resource
    private OrderLogAtomicService orderLogAtomicService;


    @Resource
    private LogisticsWaybillRpc logisticsWaybillRpc;

    @Resource
    private OrderConsigneeAtomicService orderConsigneeAtomicService;

    @Resource
    private PurchaseOrderConsigneeAtomicService purchaseOrderConsigneeAtomicService;

    @Resource
    private PurchaseOrderWareAtomicService purchaseOrderWareAtomicService;


    @Resource
    private SaleUnitApiService saleUnitApiService;

    @Autowired
    private TdeClientRpcService tdeClientRpcService;

    @Autowired
    private CustomerRpcService customerRpcService;

    @Autowired
    private CustomsDeclarationMainAtomicService customsDeclarationMainAtomicService;

    @Autowired
    private CustomsOrderAtomicService customsOrderAtomicService;


    @Autowired
    private WareHouseReadRpcService wareHouseReadRpcService;


    @Autowired
    private OrderDuccConfig orderDuccConfig;


    @Autowired
    private ThreadPoolExecutor customsOrderThreadPool;

    @Autowired
    private SkuReadRpcService skuReadRpcService;


    private final static String SAP_ORDER_NO = "sapOrderNo";

    private final static Integer CROSS_ORDER = 1;
    private final static Integer CROSS_PURCHASE_ORDER = 2;


    /**
     * 发货天数的常量，用于在订单扩展信息中获取和设置发货天数。
     */
    public static final String DELIVER_GOODS_DAYS = "deliverGoodsDays";

    /**
     * 最小配送天数的常量，用于在订单扩展信息中获取和设置最小配送天数。
     */
    public static final String MIN_DELIVERY_DAYS = "minDeliveryDays";

    /**
     * 最大配送天数的常量，用于在订单扩展信息中获取和设置最大配送天数。
     */
    public static final String MAX_DELIVERY_DAYS = "maxDeliveryDays";
    public static final String PURCHASE_ORDER_STAR = "S";


    @Override
    public DataResponse<List<CustomsQueryOrderInfoDTO>> queryOrderInfoByCustoms(CustomsQueryOderDTO queryOderDTO) {
        try {
            if (StringUtils.isBlank(queryOderDTO.getOrderId()) && StringUtils.isBlank(queryOderDTO.getCustomsNo())) {
                return DataResponse.error("param is null");
            }
            Set<Long> orderIds = new HashSet<>();
            Set<String> pIds = new HashSet<>();
            //订单号为空时，则查询报关单号关联的订单号
            if (StringUtils.isBlank(queryOderDTO.getOrderId()) && StringUtils.isNotBlank(queryOderDTO.getCustomsNo())) {
                makeBizIdsByCustomsId(queryOderDTO.getCustomsNo(), pIds, orderIds);
                log.info("queryOrderInfoByCustoms 根据报关单号查询,结果，orderIds:{},pIds:{}", JSON.toJSONString(orderIds), JSON.toJSONString(pIds));
            } else {
                //判断是采购单号，查询采购单信息
                checkOrderIdOrPurchaseIdOrIopId(queryOderDTO,pIds,orderIds);
                log.info("queryOrderInfoByCustoms 根据订单号查询,结果，orderIds:{},pIds:{}", JSON.toJSONString(orderIds), JSON.toJSONString(pIds));
            }

            //校验订单类型是否 跨境订单和跨境备货采购单
            filterOrderTypeIsCross(pIds,orderIds);

            if (pIds.isEmpty() && orderIds.isEmpty()) {
                return DataResponse.success(Collections.emptyList());
            }

            //查询销售单位
            LinkedHashMap<Integer, String> allLangUnit = getAllLangUnit(queryOderDTO.getLang());
            //查询集运中心信息
            Map<String, WmsWareHouseResult> wareInfoMap = getStoreMap();
            //最终的返回结果
            List<CustomsQueryOrderInfoDTO> result = new ArrayList<>();
            List<String> langList = new ArrayList<>(queryOderDTO.getLang());
            //都不为空则走多线程
            if (!pIds.isEmpty() && !orderIds.isEmpty()) {
                CompletableFuture<List<CustomsQueryOrderInfoDTO>> purchaseOrderInfo = syncQueryPurchaseOrderInfo(pIds, langList, allLangUnit, wareInfoMap);
                CompletableFuture<List<CustomsQueryOrderInfoDTO>> orderInfo = syncQueryOrderInfo(orderIds, langList, allLangUnit, wareInfoMap);
                result.addAll(purchaseOrderInfo.get());
                result.addAll(orderInfo.get());
            } else {
                //查询采购单
                if (!pIds.isEmpty()) {
                    result.addAll(buildPurchaseOrder(pIds, langList, allLangUnit, wareInfoMap));
                }
                //查询订单信息
                if (!orderIds.isEmpty()) {
                    result.addAll(buildOrderInfo(orderIds, langList, allLangUnit, wareInfoMap));
                }
            }
            return DataResponse.success(result);
        } catch (Exception e) {
            log.error("queryOrderInfoByCustoms error,param:{},e:",JSON.toJSONString(queryOderDTO),e);
        }
        return DataResponse.error("查询失败");

    }

    /**
     * 根据订单类型和采购模型过滤订单和采购单ID集合。
     */
    private void filterOrderTypeIsCross(Set<String> pIds, Set<Long> orderIds) {
        try {
            if (CollectionUtils.isNotEmpty(orderIds)) {
                OrderQueryReqVO reqVO = new OrderQueryReqVO();
                reqVO.setOrderType(OrderTypeConstant.CROSS_BORDER);
                List<OrderPO> orderPos = orderAtomicService.getOrderPos(orderIds, reqVO);
                orderIds.clear();
                orderIds.addAll(orderPos.stream().map(OrderPO::getOrderId).collect(Collectors.toSet()));
            }
            if (CollectionUtils.isNotEmpty(pIds)) {
                List<PurchaseOrderPO> purchaseOrderList = purchaseOrderAtomicService.queryByPoIds(new ArrayList<>(pIds));
                pIds.clear();
                pIds.addAll(purchaseOrderList.stream().filter(p ->
                                Objects.nonNull(p)
                                        && OrderTypeConstant.CROSS_BORDER == p.getPurchaseOrderType()
                                        && PurchaseModelEnum.STOCK_UP.getCode().equals(p.getPurchaseModel())).map(PurchaseOrderPO::getPurchaseOrderId)
                        .collect(Collectors.toSet()));
            }
        } catch (Exception e) {
            log.error("filterOrderTypeIsCross is error,pIds:{},orderIds:{},e:",JSON.toJSONString(pIds),JSON.toJSONString(orderIds),e);
            pIds.clear();
            orderIds.clear();
        }
    }

    /**
     * 检查并处理订单ID、备货采购单号或IOP号。
     * @param queryOderDTO 包含订单ID、备货采购单号或IOP号的DTO对象。
     * @param pIds 用于存储备货采购单号的集合。
     * @param orderIds 用于存储订单号的集合。
     */
    private void checkOrderIdOrPurchaseIdOrIopId(CustomsQueryOderDTO queryOderDTO, Set<String> pIds, Set<Long> orderIds) {
        if (StringUtils.isNotBlank(queryOderDTO.getOrderId()) && queryOderDTO.getOrderId().startsWith(PURCHASE_ORDER_STAR)) {
            OrderBizInfoPO orderBizInfo = orderBizInfoAtomicService.getOrderBizInfoPOByPurchaseOrderId(queryOderDTO.getOrderId());
            if (Objects.isNull(orderBizInfo)) {
                log.error("queryOrderInfoByCustoms 根据备货采购单号查询 orderBiz 信息不存在：{}", JSON.toJSONString(queryOderDTO));
                return ;
            }
            pIds.add(orderBizInfo.getPurchaseOrderId());
        } else {
            //判断为数字，则按照订单号或iop号 查询订单号(校验订单是否存在)，
            if (!StringUtils.isNumeric(queryOderDTO.getOrderId())) {
                log.error("queryOrderInfoByCustoms 传入的订单不合法 param：{}", JSON.toJSONString(queryOderDTO));
                return ;
            }
            OrderBizInfoPO orderBizInfo = queryOrderIdByOrderIdAndIopId(queryOderDTO.getOrderId());
            if (Objects.isNull(orderBizInfo)) {
                log.error("queryOrderInfoByCustoms 根据iop订单号查询采购单号和订单号查询为空 param：{}", JSON.toJSONString(queryOderDTO));
                return ;
            }
            //通过订单号集合查询
            if (Constant.DEFAULT_ORDER_ID.equals(orderBizInfo.getOrderId()) && StringUtils.isNotBlank(orderBizInfo.getPurchaseOrderId())) {
                pIds.add(orderBizInfo.getPurchaseOrderId());
            }
            if (Objects.nonNull(orderBizInfo.getOrderId()) && !Constant.DEFAULT_ORDER_ID.equals(orderBizInfo.getOrderId())) {
                orderIds.add(orderBizInfo.getOrderId());
            }
        }
    }

    /**
     * 异步同步查询采购订单信息。
     * @param pIds 订单ID集合。
     * @param langList 语言列表。
     * @param allLangUnit 所有语言单元映射。
     * @param wareInfoMap 仓库信息映射。
     * @return 采购订单信息DTO列表。
     */
    private CompletableFuture<List<CustomsQueryOrderInfoDTO>> syncQueryPurchaseOrderInfo(Set<String> pIds, List<String> langList, Map<Integer, String> allLangUnit, Map<String, WmsWareHouseResult> wareInfoMap) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return buildPurchaseOrder(pIds,langList,allLangUnit,wareInfoMap);
            } catch (Exception e) {
                log.error("syncQueryPurchaseOrderInfo error,param: {}, e:", JSON.toJSONString(pIds), e);
                return Collections.emptyList();
            }
        }, customsOrderThreadPool);
    }

    /**
     * 异步同步查询订单信息。
     * @param orderIds 订单ID集合。
     * @param langList 语言列表。
     * @param allLangUnit 所有语言单元映射。
     * @param wareInfoMap 仓库信息映射。
     * @return 订单信息DTO列表。
     */
    private CompletableFuture<List<CustomsQueryOrderInfoDTO>> syncQueryOrderInfo(Set<Long> orderIds, List<String> langList, LinkedHashMap<Integer, String> allLangUnit, Map<String, WmsWareHouseResult> wareInfoMap) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return buildOrderInfo(orderIds,langList,allLangUnit,wareInfoMap);
            } catch (Exception e) {
                log.error("syncQueryOrderInfo error,param: {}, e:", JSON.toJSONString(orderIds), e);
                return Collections.emptyList();
            }
        }, customsOrderThreadPool);
    }

    /**
     * 获取集运中心仓信息。
     * @return 国际仓库的映射关系
     */
    private Map<String, WmsWareHouseResult> getStoreMap() {
        List<WmsWareHouseResult> wareHouseResults = wareHouseReadRpcService.queryInternationalWareHouse(CountryConstant.COUNTRY_ZH, null, null);
        return wareHouseResults.stream().collect(HashMap::new, (m, v) -> m.put(v.getEnterpriseWarehouseCode(), v), HashMap::putAll);
    }


    /**
     * 构建海关查询订单信息DTO列表。
     * @param pIds 采购单ID集合
     * @param langList 语言列表
     * @param allLangUnit 所有语言单位映射表
     * @param wareInfoMap 仓库信息映射表
     * @return 海关查询订单信息DTO列表
     */
    private List<CustomsQueryOrderInfoDTO> buildPurchaseOrder(Set<String> pIds, List<String> langList, Map<Integer, String> allLangUnit, Map<String, WmsWareHouseResult> wareInfoMap) throws ExecutionException, InterruptedException {
        //查询iop单信息
        List<OrderBizInfoPO> orderBizInfoList = orderBizInfoAtomicService.queryIopOrderIdByPurchaseOrderId(new ArrayList<>(pIds));
        Map<String, Long> purchaseAndIopMap = orderBizInfoList.stream().filter(o -> StringUtils.isNotBlank(o.getIopOrderId()) && YnEnum.YES.getCode().equals(o.getValidState()))
                .collect(HashMap::new, (m, v) -> m.put(v.getPurchaseOrderId(), Long.valueOf(v.getIopOrderId())), HashMap::putAll);        // 国内运单号  orderIds
        //多线程查询运单信息
        CompletableFuture<Map<Long, String>> waybillFuture = syncQueryWaybill(new HashSet<>(purchaseAndIopMap.values()));

        //查询采购单信息
        List<PurchaseOrderPO> purchaseOrderList = purchaseOrderAtomicService.queryByPoIds(new ArrayList<>(pIds));
        //查询客户编号
        Set<String> clientSet = new HashSet<>();
        Map<Long, PurchaseOrderPO> purchaseOrderMap = new HashMap<>();
        purchaseOrderList.forEach(o -> {
            purchaseOrderMap.put(o.getOrderId(), o);
            clientSet.add(o.getSupplierCode());
        });

        //多线程查询 客户名称 code
        CompletableFuture<Map<String, CustomerReadResp>> clientFuture = syncQueryClient(clientSet);
        List<CustomsQueryOrderInfoDTO> customsQueryOrder = PurchaseOrderConvert.INSTANCE.purchaseOrderPo2CusmtosList(purchaseOrderList);

        // 收货信息
        Map<String, CustomsQueryRecipientInfoDTO> recipientMap = queryPurchaseConsignee(pIds);
        //查询商品信息
        Map<String, List<CustomsQueryWareInfoDTO>> wareResMap = queryPurchaseOrderWareList(pIds, langList, allLangUnit);
        //获取异步结果
        Map<Long, String> waybillMap = waybillFuture.get();
        Map<String, CustomerReadResp> customerMap = clientFuture.get();

        //封装最终返回结果
        customsQueryOrder.forEach(c -> {
            PurchaseOrderPO pOrder = purchaseOrderMap.get(c.getOrderId());
            if (Objects.isNull(pOrder)) {
                return;
            }

            c.setOrderBizType(CROSS_PURCHASE_ORDER);
            Long iopOrderId = purchaseAndIopMap.get(pOrder.getPurchaseOrderId());
            c.setWaybillNumber(waybillMap.get(iopOrderId));
            CustomerReadResp customerReadResp = customerMap.get(c.getClientCode());
            if (Objects.nonNull(customerReadResp)) {
                c.setClientName(customerReadResp.getClientName());
            }
            Integer status = setPurchaseOrderShipment(c, pOrder.getPurchaseOrderStatus());
            c.setOrderBizStatus(status);
            setPurchaseShipment(c,pOrder.getPurchaseOrderExtInfo());
            setStoreHouseNameAndAddress(c,wareInfoMap,pOrder.getStoreHouseId());
            //收货信息
            c.setRecipientInfo(recipientMap.get(c.getPurchaseOrderId()));
            //商品信息
            c.setWareList(wareResMap.get(c.getPurchaseOrderId()));
            //订单创建时间
            c.setOrderCreateTime(pOrder.getPurchaseCreateTime());
            //采购单完成时间
            c.setOrderFinishTime(pOrder.getCompleteTime());
            //采购单总金额
            c.setOrderTotalPrice(pOrder.getPurchaseTotalPrice());
            //mkuNum
            c.setMkuNum(pOrder.getSkuNum());
        });

        return customsQueryOrder;


    }

    /**
     * 根据采购订单ID查询关联的收货人信息
     * @return 以采购订单ID为键，收货人信息为值的Map对象
     */
    private Map<String, CustomsQueryRecipientInfoDTO> queryPurchaseConsignee(Set<String> pIds) {
        List<PurchaseOrderConsigneePO> consigneePoList = purchaseOrderConsigneeAtomicService.queryByPurchaseOrderIds(pIds);
        Map<String, CustomsQueryRecipientInfoDTO> recipientMap = new HashMap<>();
        consigneePoList.forEach(c -> {
            String pId = c.getPurchaseOrderId();
            if (!recipientMap.containsKey(pId)) {
                CustomsQueryRecipientInfoDTO recipient = new CustomsQueryRecipientInfoDTO();
                recipient.setRecipientName(tdeClientRpcService.decrypt(c.getConsigneeEncrypt()));
                recipient.setRecipientPhone(tdeClientRpcService.decrypt(c.getConsigneeMobileEncrypt()));
                recipientMap.put(pId, recipient);
            }
        });
        return recipientMap;
    }

    /**
     * 封装报关单商品信息。
     * @param pIds 采购单ID集合
     * @param langList 指定的语言列表
     * @param allLangUnit 所有语言单位映射
     * @return 每个采购单对应的商品信息列表的映射
     */
    private Map<String, List<CustomsQueryWareInfoDTO>> queryPurchaseOrderWareList(Set<String> pIds, List<String> langList, Map<Integer, String> allLangUnit) {
        List<PurchaseOrderWarePO> purchaseOrderWareList = purchaseOrderWareAtomicService.queryPurchaseOrderWarePOByIds(pIds);
        Map<String, List<CustomsQueryWareInfoDTO>> wareResMap = new HashMap<>();
        Set<Long> jdSkuIds = new HashSet<>();
        purchaseOrderWareList.forEach(p -> {
            List<CustomsQueryWareInfoDTO> customsWareList = wareResMap.get(p.getPurchaseOrderId());
            if (CollectionUtils.isEmpty(customsWareList)) {
                customsWareList = new ArrayList<>();
            }
            CustomsQueryWareInfoDTO ware = PurchaseOrderConvert.INSTANCE.warePo2CusDto(p);
            if (StringUtils.isNotBlank(p.getSkuJsonInfo())) {
                OrderSkuDetailPO skuDetailPO = JSON.parseObject(p.getSkuJsonInfo(), OrderSkuDetailPO.class);
                Map<String, String> skuNameMap = skuDetailPO.getSkuNameMap();
                if (MapUtils.isNotEmpty(skuNameMap)) {
                    skuNameMap.keySet().removeIf(key -> !langList.contains(key));
                    ware.setSkuNameMap(skuNameMap);
                }
                if (Objects.nonNull(skuDetailPO.getSaleUnit())) {
                    ware.setSaleUnit(allLangUnit.get(skuDetailPO.getSaleUnit()));
                }
                ware.setSupplierName(skuDetailPO.getJdVendorName());
                ware.setJdSkuId(skuDetailPO.getJdSkuId());
                ware.setMkuNum(p.getSkuNum());
                if (StringUtils.isBlank(ware.getSupplierName()) && Objects.nonNull(skuDetailPO.getJdSkuId())) {
                    jdSkuIds.add(skuDetailPO.getJdSkuId());
                }
            }
            ware.setMkuAllPrice(p.getSkuAllPrice());
            customsWareList.add(ware);
            wareResMap.put(p.getPurchaseOrderId(), customsWareList);
        });

        Map<Long, VendorDTO> vendorMap = queryJdVendor(jdSkuIds);
        wareResMap.values().forEach(w -> {
            w.forEach(wf -> {
                VendorDTO vendorDTO = vendorMap.get(wf.getJdSkuId());
                if (Objects.nonNull(vendorDTO)) {
                    wf.setSupplierName(vendorDTO.getVendorName());
                }
            });
        });
        return wareResMap;

    }

    private Map<Long, VendorDTO> queryJdVendor(Set<Long> jdSkuIds) {
        Map<Long, VendorDTO> result = new HashMap<>();
        List<List<Long>> partition = Lists.partition(new ArrayList<>(jdSkuIds), Constant.BATCH_NUM_50);
        for (List<Long> part : partition) {
            Map<Long, VendorDTO> vendorMap = skuReadRpcService.queryVendorByJdSku(new HashSet<>(part));
            if (MapUtils.isNotEmpty(vendorMap)) {
                result.putAll(vendorMap);
            }
        }
        return  result;
    }

    /**
     * 设置采购单的发货时效。
     * @param c CustomsQueryOrderInfoDTO 对象，用于存储购买发货信息。
     * @param extInfo 扩展信息，可能包含交货天数。
     */
    private void setPurchaseShipment(CustomsQueryOrderInfoDTO c, String extInfo) {
        if (StringUtils.isNotBlank(extInfo)) {
            BigDecimal deliverGoodsDays = JsonUtil.getIntegerFromJson(extInfo, DELIVER_GOODS_DAYS);
            if (deliverGoodsDays != null) {
                c.setShippingLeadTime(formatBigDecimal(deliverGoodsDays));
            }
        }
    }

    /**
     * 根据仓库ID设置订单的仓库名称和地址。
     * @param c 订单信息DTO对象。
     * @param wareInfoMap 仓库信息映射表。
     * @param storeHouseId 仓库ID。
     */
    private void setStoreHouseNameAndAddress(CustomsQueryOrderInfoDTO c, Map<String, WmsWareHouseResult> wareInfoMap, String storeHouseId) {
        if (StringUtils.isNotBlank(storeHouseId)) {
            DuccConfigContactInfoVO duccConfig = orderDuccConfig.getStorehouseConfigByCode(storeHouseId);
            if (Objects.isNull(duccConfig)) {
                return;
            }
            WmsWareHouseResult wmsWareHouseResult = wareInfoMap.get(duccConfig.getStoreHouseCode());
            if (Objects.nonNull(wmsWareHouseResult)) {
                c.setStoreHouseAddress(wmsWareHouseResult.getFullWarehouseAddress());
                c.setStoreHouseName(wmsWareHouseResult.getEnterpriseWarehouseName());
            }
        }
    }

    /**
     * 根据传入的CustomsQueryOrderInfoDTO和purchaseOrderExtInfo，判断并返回对应的采购订单状态。
     * @param c CustomsQueryOrderInfoDTO对象，用于查询订单信息。
     * @param purchaseOrderExtInfo Integer类型，表示采购订单的扩展信息。
     * @return Integer类型，返回对应的采购订单状态码。
     */
    private Integer setPurchaseOrderShipment(CustomsQueryOrderInfoDTO c, Integer purchaseOrderExtInfo) {
        PurchaseOrderStatusDTO orderStatusDTO = PurchaseOrderStatusConstant.getDTOByStatus(purchaseOrderExtInfo);
        //已创建
        if (getPurchaseCreateStatus().contains(orderStatusDTO.getStatus())) {
            return PurchaseOrderStatusConstant.CREATE_FINISH.getStatus();
        }

        //已取消
        if (getPurchaseCancelStatus().contains(orderStatusDTO.getStatus())) {
            return PurchaseOrderStatusConstant.CANCEL.getStatus();
        }

        //已发货 已妥投
        if (getPurchaseShippedStatus().contains(orderStatusDTO.getStatus())) {
            return PurchaseOrderStatusConstant.SHIPPED.getStatus();
        }

        //已完成
        if (getPurchaseFinishStatus().contains(orderStatusDTO.getStatus())) {
            return PurchaseOrderStatusConstant.FINISH.getStatus();
        }
        //兜底
        return null;
    }

    /**
     * 获取创建采购单的状态集合
     */
    private Set<Integer> getPurchaseCreateStatus() {
        Set<Integer> set = new HashSet<>();
        set.add(PurchaseOrderStatusConstant.CANCEL_INNER.getStatus());
        set.add(PurchaseOrderStatusConstant.CREATE_FINISH.getStatus());
        set.add(PurchaseOrderStatusConstant.CONFIRM.getStatus());
        set.add(PurchaseOrderStatusConstant.WAIT_DISTRIBUTED.getStatus());
        set.add(PurchaseOrderStatusConstant.RECEIVE.getStatus());
        return set;
    }

    /**
     * 获取取消的采购订单状态集合
     * @return 已取消的采购订单状态集合
     */
    private Set<Integer> getPurchaseCancelStatus() {
        Set<Integer> set = new HashSet<>();
        set.add(PurchaseOrderStatusConstant.CANCEL.getStatus());
        return set;
    }

    /**
     * 获取发货采购单状态集合。
     * @return 包含已发货和部分入库状态的采购单状态集合。
     */
    private Set<Integer> getPurchaseShippedStatus() {
        Set<Integer> set = new HashSet<>();
        set.add(PurchaseOrderStatusConstant.SHIPPED.getStatus());
        set.add(PurchaseOrderStatusConstant.INPUT_CONSOLIDATION_WH.getStatus());
        set.add(PurchaseOrderStatusConstant.INPUT_WAREHOUSE_PART.getStatus());
        set.add(PurchaseOrderStatusConstant.INPUT_WAREHOUSE.getStatus());
        return set;
    }

    /**
     * 获取已完成采购订单状态集合。
     * @return 包含已完成状态的集合。
     */
    private Set<Integer> getPurchaseFinishStatus() {
        Set<Integer> set = new HashSet<>();
        set.add(PurchaseOrderStatusConstant.FINISH.getStatus());
        return set;
    }

    private List<CustomsQueryOrderInfoDTO> buildOrderInfo(Set<Long> orderIds, List<String> langList, LinkedHashMap<Integer, String> allLangUnit, Map<String, WmsWareHouseResult> wareInfoMap) throws ExecutionException, InterruptedException {
        //查询订单
        List<OrderBizInfoPO> orderBizInfoList = orderBizInfoAtomicService.getOrderBizInfoPOByOrderIds(orderIds);
        Map<Long, Long> orderAndIopMap = orderBizInfoList.stream().filter(o -> StringUtils.isNotBlank(o.getIopOrderId()) && YnEnum.YES.getCode().equals(o.getValidState()))
                .collect(HashMap::new, (m, v) -> m.put(v.getOrderId(), Long.valueOf(v.getIopOrderId())), HashMap::putAll);
        //多线程 国内运单号  orderIds
        CompletableFuture<Map<Long, String>> waybillFuture = syncQueryWaybill(new HashSet<>(orderAndIopMap.values()));

        //查询订单信息
        List<OrderPO> orderList = orderAtomicService.getOrderPOsByOrderIds(orderIds);
        List<CustomsQueryOrderInfoDTO> customsQueryOrder = OrderConvert.INSTANCE.orderPoList2CustomerList(orderList);

        //封装查询查询客户编码、订单map
        Set<String> clientSet = new HashSet<>();
        Map<Long, OrderPO> orderMap = new HashMap<>();
        orderList.forEach(o -> {
            orderMap.put(o.getOrderId(), o);
            clientSet.add(o.getClientCode());
        });

        //多线程查询
        CompletableFuture<Map<String, CustomerReadResp>> clientFuture = syncQueryClient(clientSet);

        // 订单完成时间 orderIds
        Map<Long, Date> finishOrderMap = queryOrderFinishTime(orderIds);

        // 收货信息 orderIds
        Map<Long, CustomsQueryRecipientInfoDTO> recipientMap = queryOrderConsignee(orderIds);

        //设置订单商品信息
        Map<Long, List<CustomsQueryWareInfoDTO>> wareResMap = queryOrderWareInfoList(orderIds, langList, allLangUnit);
        //获取异步结果
        Map<Long, String> waybillMap = waybillFuture.get();
        Map<String, CustomerReadResp> respMap = clientFuture.get();
        //组装最终返回结果
        customsQueryOrder.forEach(c -> {
            OrderPO orderPO = orderMap.get(c.getOrderId());
            if (Objects.isNull(orderPO)) {
                return;
            }

            c.setOrderBizType(CROSS_ORDER);
            c.setOrderBizStatus(transformOrderStatus(orderPO.getOrderStatus()));
            if (StringUtils.isNotBlank(orderPO.getThirdExtInfo())) {
                JSONObject ext = JSON.parseObject(orderPO.getThirdExtInfo());
                c.setSapOrderId(ext.getString(SAP_ORDER_NO));
            }
            Long iopId = orderAndIopMap.get(c.getOrderId());
            c.setWaybillNumber(waybillMap.get(iopId));
            CustomerReadResp customerReadResp = respMap.get(c.getClientCode());
            if (Objects.nonNull(customerReadResp)) {
                c.setClientName(customerReadResp.getClientName());
            }
            setShipment(c, orderPO.getOrderExtInfo());
            setStoreHouseNameAndAddress(c, wareInfoMap, orderPO.getStoreHouseId());
            Date date = finishOrderMap.get(c.getOrderId());
            if (Objects.nonNull(date)) {
                c.setOrderFinishTime(date.getTime());
            }
            //收货信息
            c.setRecipientInfo(recipientMap.get(c.getOrderId()));
            //商品信息
            c.setWareList(wareResMap.get(c.getOrderId()));

        });

        return customsQueryOrder;
    }

    private CompletableFuture<Map<String, CustomerReadResp>> syncQueryClient(Set<String> clientSet) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return queryClientMap(clientSet);
            } catch (Exception e) {
                log.error("queryClient error,param: {}, e:", JSON.toJSONString(clientSet), e);
                return new HashMap<>();
            }
        }, customsOrderThreadPool);
    }

    private CompletableFuture<Map<Long, String>> syncQueryWaybill(Set<Long> iopOrderIds) {
        return  CompletableFuture.supplyAsync(() -> {
            try {
                return queryIopWaybill(iopOrderIds);

            } catch (Exception e) {
                log.error("queryIopWaybill error,param: {}, e:", JSON.toJSONString(iopOrderIds), e);
                return new HashMap<>();
            }
        }, customsOrderThreadPool);
    }

    /**
     * 组装报关订单的商品信息。
     * @param orderIds 订单ID集合
     * @param langList 语言列表
     * @param allLangUnit 所有语言单位的映射
     * @return 订单ID到CustomsQueryWareInfoDTO列表的映射
     */
    private Map<Long, List<CustomsQueryWareInfoDTO>> queryOrderWareInfoList(Set<Long> orderIds, List<String> langList, LinkedHashMap<Integer, String> allLangUnit) {
        Map<Long, List<CustomsQueryWareInfoDTO>> wareResMap = new HashMap<>();

        //查询商品信息
        Map<Long, List<OrderWarePO>> orderWareList = orderWareAtomicService.getOrderWarePOListByOrderId(orderIds);
        Map<String, PurchaseOrderPO> purchaseOrderPOMap = purchaseOrderAtomicService.queryPIdByOrderIds(orderIds);
        List<PurchaseOrderWarePO> purchaseWarePOList = purchaseOrderWareAtomicService.queryPurchaseOrderWarePOListByPoIds(purchaseOrderPOMap.keySet());
        Map<String, BigDecimal> purchasePriceMap = purchaseWarePOList.stream().collect(Collectors.toMap(
                o -> getPurchaseWareKey(o.getOrderId(), o.getSkuId()),
                PurchaseOrderWarePO::getSkuAllPrice));

        Set<Long> jdSkuIds = new HashSet<>();
        orderWareList.forEach((k, v) -> {
            List<CustomsQueryWareInfoDTO> cWares = new ArrayList<>();
            v.forEach(w -> {
                CustomsQueryWareInfoDTO ware = OrderConvert.INSTANCE.warePo2CusDto(w);
                OrderWareMkuJsonInfoPO mkuJson = JSON.parseObject(w.getMkuJsonInfo(), OrderWareMkuJsonInfoPO.class);
                ware.setMaterialId(mkuJson.getMaterialCode());
                OrderSkuDetailPO skuDetailPO = JSON.parseObject(w.getSkuJsonInfo(), OrderSkuDetailPO.class);
                Map<String, String> skuNameMap = skuDetailPO.getSkuNameMap();
                if (MapUtils.isNotEmpty(skuNameMap)) {
                    skuNameMap.keySet().removeIf(key -> !langList.contains(key));
                    ware.setSkuNameMap(skuNameMap);
                }
                ware.setJdSkuId(skuDetailPO.getJdSkuId());
                ware.setSaleUnit(allLangUnit.get(skuDetailPO.getSaleUnit()));
                ware.setSupplierName(skuDetailPO.getJdVendorName());
                BigDecimal purchaseTotalPrice = purchasePriceMap.get(getPurchaseWareKey(w.getOrderId(), w.getSkuId()));
                ware.setPurchaseAllPrice(purchaseTotalPrice);
                if (StringUtils.isBlank(ware.getSupplierName()) && Objects.nonNull(ware.getJdSkuId())) {
                    jdSkuIds.add(ware.getJdSkuId());
                }
                cWares.add(ware);
            });

            wareResMap.put(k, cWares);
        });

        Map<Long, VendorDTO> vendorMap = queryJdVendor(jdSkuIds);
        wareResMap.values().forEach(w -> {
            w.forEach(wf -> {
                Long jdSkuId = wf.getJdSkuId();
                VendorDTO vendorDTO = vendorMap.get(jdSkuId);
                if (Objects.nonNull(vendorDTO)) {
                    wf.setSupplierName(vendorDTO.getVendorName());
                }
            });
        });
        return wareResMap;
    }

    /**
     * 根据订单ID查询关联的收件人信息。
     * @return 每个订单对应的收件人信息，键为订单ID，值为CustomsQueryRecipientInfoDTO对象
     */
    private Map<Long, CustomsQueryRecipientInfoDTO> queryOrderConsignee(Set<Long> orderIds) {
        List<OrderConsigneePO> consigneePoList = orderConsigneeAtomicService.getOrderConsigneePOsByOrderIds(orderIds);
        Map<Long, CustomsQueryRecipientInfoDTO> recipientMap = new HashMap<>();
        consigneePoList.forEach(c -> {
            Long orderId = c.getOrderId();
            if (!recipientMap.containsKey(orderId)) {
                CustomsQueryRecipientInfoDTO recipient = new CustomsQueryRecipientInfoDTO();
                recipient.setRecipientName(tdeClientRpcService.decrypt(c.getConsigneeEncrypt()));
                recipient.setRecipientPhone(tdeClientRpcService.decrypt(c.getConsigneeMobileEncrypt()));
                recipientMap.put(orderId, recipient);
            }
        });
        return recipientMap;
    }

    /**
     * 根据订单ID集合查询每个订单的完成时间。
     * @param orderIds 订单ID集合
     * @return 订单ID到完成时间的映射关系
     */
    private Map<Long, Date> queryOrderFinishTime(Set<Long> orderIds) {
        List<OrderLogPO> finishLog = orderLogAtomicService.getOrderLogByOrderIds(orderIds, OrderStatusConstant.FINISH_ORDER.getStatus());
        Map<Long, OrderLogPO> orderLogMap = finishLog.stream().collect(Collectors.toMap(
                OrderLogPO::getOrderId, // 键：orderId
                log -> log, // 值：OrderLogPO 对象
                (log1, log2) -> log1.getVersion() < log2.getVersion() ? log1 : log2 // 合并函数：选择 version 较小的
        ));

        // 将结果转换为 Map<Long, Date>
        Map<Long, Date> finishOrderMap = orderLogMap.values().stream().collect(Collectors.toMap(
                OrderLogPO::getOrderId, // 键：orderId
                OrderLogPO::getCreateTime // 值：createTime
        ));

        return finishOrderMap;
    }

    /**
     * 根据订单ID和商品SKU ID生成购买商品的Key。
     * @param orderId 订单ID
     * @param skuId 商品SKU ID
     * @return 格式为"orderId#skuId"的购买商品Key
     */
    private String getPurchaseWareKey(Long orderId, Long skuId) {
        return String.format("%s#%s",orderId,skuId);
    }

    /**
     * 适配订单状态，统一封装对外可见状态
     */
    private Integer transformOrderStatus(Integer orderStatus) {
        OrderStatusDTO orderStatusDTO = OrderStatusConstant.valueOf(orderStatus);
        if (OrderStatusConstant.SUBMIT_ORDER.getShowStatus().equals(orderStatusDTO.getShowStatus())
            || OrderStatusConstant.CONFIRM_ORDER.getShowStatus().equals(orderStatusDTO.getShowStatus())
        ) {
            //已创建
            return OrderStatusConstant.SUBMIT_ORDER.getStatus();
        }

        //已取消
        if (OrderStatusConstant.CANCEL_ORDER.getShowStatus().equals(orderStatusDTO.getShowStatus())) {
            //已取消
            return OrderStatusConstant.CANCEL_ORDER.getStatus();
        }
        //已发货 已妥投
        if (OrderStatusConstant.SHIPPED.getShowStatus().equals(orderStatusDTO.getShowStatus())
            || OrderStatusConstant.DELIVERY_SUBMITTED.getShowStatus().equals(orderStatusDTO.getShowStatus())
        ) {
            //已发货
            return OrderStatusConstant.SHIPPED.getStatus();
        }
        if (OrderStatusConstant.FINISH_ORDER.getShowStatus().equals(orderStatusDTO.getShowStatus())) {
            //已完成
            return OrderStatusConstant.FINISH_ORDER.getStatus();
        }
        //兜底
        return orderStatus;
    }

    private LinkedHashMap<Integer, String> getAllLangUnit(Set<String> langList) {
        String lang = langList.iterator().next();
        if (langList.contains(LangConstant.LANG_ZH)) {
            lang = LangConstant.LANG_ZH;
        }
        DataResponse<LinkedHashMap<Integer, String>> saleUnitResponse = saleUnitApiService.querySaleUnitByLang(lang);
        if (Objects.nonNull(saleUnitResponse) && Boolean.TRUE.equals(saleUnitResponse.getSuccess()) && Objects.nonNull(saleUnitResponse.getData())) {
            return saleUnitResponse.getData();
        }
        return new LinkedHashMap<>();
    }


    private OrderBizInfoPO queryOrderIdByOrderIdAndIopId(String orderId) {
        Long orderIdOrIopId = Long.valueOf(orderId);
        List<OrderBizInfoPO> orderBizInfoPOS = orderBizInfoAtomicService.queryByOrderIdByIopId(orderIdOrIopId);
        if (CollectionUtils.isEmpty(orderBizInfoPOS)) {
            return null;
        }
        return orderBizInfoPOS.get(0);
    }

    private Map<Long,String> queryIopWaybill(Set<Long> orderIds){
        return logisticsWaybillRpc.queryWaybillCode(orderIds);
    }


    private void  setShipment(CustomsQueryOrderInfoDTO customs, String extInfo){
        BigDecimal deliverGoodsDays = JsonUtil.getIntegerFromJson(extInfo, DELIVER_GOODS_DAYS);
        BigDecimal minDeliveryDays = JsonUtil.getIntegerFromJson(extInfo, MIN_DELIVERY_DAYS);
        BigDecimal maxDeliveryDays = JsonUtil.getIntegerFromJson(extInfo, MAX_DELIVERY_DAYS);

        if (deliverGoodsDays != null) {
            customs.setShippingLeadTime(formatBigDecimal(deliverGoodsDays));
        }
        if (minDeliveryDays != null && maxDeliveryDays != null) {
            String deliveryTimeInfo;
            if (minDeliveryDays.equals(maxDeliveryDays)) {
                deliveryTimeInfo = formatBigDecimal(minDeliveryDays);
            } else {
                deliveryTimeInfo = formatBigDecimal(minDeliveryDays) + Constant.HYPHEN + formatBigDecimal(maxDeliveryDays);
            }
            customs.setDeliveryLeadTime(deliveryTimeInfo);
        }
    }


    /**
     * 格式化BigDecimal值为指定的小数位数或整数。
     *
     * @param value 要格式化的BigDecimal值。
     * @return 格式化后的字符串表示。
     */
    private String formatBigDecimal(BigDecimal value) {
        if (value.scale() > 0 && value.stripTrailingZeros().scale() > 0) {
            DecimalFormat df = new DecimalFormat("#.##");
            return df.format(value);
        } else {
            return String.valueOf(value.intValue());
        }
    }


    /**
     * 查询客户信息
     */
    private Map<String,CustomerReadResp> queryClientMap(Set<String> clientSet){
        return customerRpcService.queryCustomerByClientCodes(clientSet);
    }


    /**
     * 根据报关单号 组装订单号，采购单号
     */
    private void makeBizIdsByCustomsId(String customsId, Set<String> pIds, Set<Long> orderIds){
        CustomsDeclarationMainPo mainPo = customsDeclarationMainAtomicService.selectByCustomsId(customsId);
        if (Objects.isNull(mainPo) || Objects.isNull(mainPo.getId())) {
            return ;
        }

        List<CustomsOrderPo> customsOrderPos = customsOrderAtomicService.selectByMainId(mainPo.getId());
        if (CollectionUtils.isNotEmpty(customsOrderPos)) {
            customsOrderPos.forEach(c -> {
                if (BusinessTypeEnum.CROSS_BORDER_ORDER.getCode().equals(c.getType())) {
                    orderIds.add(Long.valueOf(c.getBizId()));
                }
                if (BusinessTypeEnum.PROCUREMENT_ORDER.getCode().equals(c.getType())) {
                    pIds.add(c.getBizId());
                }
            });

        }
    }
}
