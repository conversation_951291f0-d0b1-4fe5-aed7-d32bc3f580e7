package com.jdi.isc.order.center.service.adapter.mapstruct.aftersale;

import com.jdi.isc.order.center.api.aftersales.req.AfterSalesApiDTO;
import com.jdi.isc.order.center.api.aftersales.req.AfterSalesDetailReqApiDTO;
import com.jdi.isc.order.center.api.aftersales.req.AfterSalesManageDetailApiDTO;
import com.jdi.isc.order.center.api.aftersales.req.AfterSalesManagePageApiDTO;
import com.jdi.isc.order.center.api.aftersales.res.AfterSalesApiResp;
import com.jdi.isc.order.center.api.aftersales.res.AfterSalesDetailApiResp;
import com.jdi.isc.order.center.domain.aftersales.biz.AfterSalesPageReqVO;
import com.jdi.isc.order.center.domain.aftersales.po.AfterSalesMsgPO;
import com.jdi.isc.order.center.domain.aftersales.po.AfterSalesOperatePO;
import com.jdi.isc.order.center.domain.aftersales.po.AfterSalesPO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;


/**
 * 售后单属性对象转换
 * <AUTHOR>
 * @date 20240822
 */
@Mapper
public interface AfterSalesConvert {

    AfterSalesConvert INSTANCE = Mappers.getMapper(AfterSalesConvert.class);

    @InheritConfiguration
    List<AfterSalesApiResp> listPo2dto(List<AfterSalesPO> pos);

    @InheritConfiguration
    @Mappings({
            @Mapping(target = "customerContactName", source = "customerContactNameEncrypt")
    })
    AfterSalesApiResp po2dto(AfterSalesPO po);

    AfterSalesMsgPO po2MsgPO(AfterSalesPO afterSalesPO);

    @Mappings({
            @Mapping(target = "submitTimeStart", ignore = true),
            @Mapping(target = "submitTimeEnd", ignore = true),
    })
    AfterSalesPageReqVO mangePageDto2AfterSalesPageReqVO(AfterSalesManagePageApiDTO afterSalesManagePageApiDTO);

    @Mappings({
            @Mapping(target = "createTime", expression = "java(po.getCreateTime().getTime())"),
            @Mapping(target = "updateTime", expression = "java(po.getUpdateTime().getTime())")
    })
    AfterSalesOperatePO po2OperatePO(AfterSalesPO po);

    AfterSalesManageDetailApiDTO detailReq2ManageDetailReq(AfterSalesDetailReqApiDTO afterSalesDetailReqApiDTO);

    AfterSalesDetailApiResp manageDetailResp2DetailResp(AfterSalesApiResp po);

}
