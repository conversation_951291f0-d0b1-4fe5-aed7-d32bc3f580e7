package com.jdi.isc.order.center.service.protocol.jsf.order;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.isc.order.center.api.constants.OrderStatusConstant;
import com.jdi.isc.order.center.api.constants.OrderTypeConstant;
import com.jdi.isc.order.center.api.orderRead.OrderReadManageApiService;
import com.jdi.isc.order.center.api.orderRead.biz.manage.OrderReadManageApiDTO;
import com.jdi.isc.order.center.api.orderRead.biz.manage.OrderReadDetailReqApiDTO;
import com.jdi.isc.order.center.api.orderRead.biz.manage.OrderReadManagePageReqApiDTO;
import com.jdi.isc.order.center.api.orderRead.biz.manage.OrderReadSkuStockDetailApiDTO;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.common.utils.validation.PropertyError;
import com.jdi.isc.order.center.common.utils.validation.ValidateResult;
import com.jdi.isc.order.center.common.utils.validation.ValidationUtil;
import com.jdi.isc.order.center.domain.enums.order.OrderHoldStatusEnum;
import com.jdi.isc.order.center.domain.enums.order.OrderShowTypeEnum;
import com.jdi.isc.order.center.domain.enums.purchase.PurchaseModelEnum;
import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.domain.order.po.OrderSkuDetailPO;
import com.jdi.isc.order.center.domain.order.po.OrderWareMkuJsonInfoPO;
import com.jdi.isc.order.center.rpc.stock.StockReadRpcService;
import com.jdi.isc.order.center.service.atomic.mku.OrderWareAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderAtomicService;
import com.jdi.isc.order.center.service.manage.order.OrderReadManageService;
import com.jdi.isc.product.soa.stock.composite.req.StockCompItemReadReqDTO;
import com.jdi.isc.product.soa.stock.composite.req.StockCompReadReqDTO;
import com.jdi.isc.product.soa.stock.mku.res.IscMkuStockResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderReadManageApiServiceImpl implements OrderReadManageApiService {

    @Autowired
    private Map<String, OrderReadManageService> orderReadManageServiceMap;

    @Autowired
    private OrderAtomicService orderAtomicService;

    @Autowired
    private OrderWareAtomicService orderWareAtomicService;

    @Autowired
    private StockReadRpcService stockReadRpcService;

    private static final Map<Integer, String> orderReadManageServiceKeyMap = new HashMap<>();

    static {
        orderReadManageServiceKeyMap.put(OrderShowTypeEnum.STOCK_UP.getType(), "orderReadManageCrossBorderStockUpServiceImpl");
        orderReadManageServiceKeyMap.put(OrderShowTypeEnum.LOCAL_DIRECT.getType(), "orderReadManageLocalServiceImpl");
        orderReadManageServiceKeyMap.put(OrderShowTypeEnum.CROSS_BORDER.getType(), "orderReadManageCrossBorderServiceImpl");
    }

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public DataResponse<PageInfo<OrderReadManageApiDTO>> queryOrderPage(OrderReadManagePageReqApiDTO orderReadManagePageReqApiDTO) {
        DataResponse<PageInfo<OrderReadManageApiDTO>> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.order.OrderReadManageApiServiceImpl.queryOrderPage." + active);
        try {
            ValidateResult<OrderReadManagePageReqApiDTO> validateResult = ValidationUtil.checkParam(orderReadManagePageReqApiDTO);
            if(!validateResult.getSuccess()){
                Set<String> messages = validateResult.getPropertyErrors().stream().map(PropertyError::getMessage).collect(Collectors.toSet());
                return DataResponse.error(DataResponseCode.PARAM_ERROR.getCode(), String.join(Constant.COMMA, messages));
            }

            String serviceKey = "";
            if(PurchaseModelEnum.STOCK_UP.getCode().equals(orderReadManagePageReqApiDTO.getManagePurchaseModel())){
                serviceKey = orderReadManageServiceKeyMap.get(OrderShowTypeEnum.STOCK_UP.getType());
            }else if(orderReadManagePageReqApiDTO.getOrderType() != null && OrderTypeConstant.LOCAL == orderReadManagePageReqApiDTO.getOrderType()){
                serviceKey = orderReadManageServiceKeyMap.get(OrderShowTypeEnum.LOCAL_DIRECT.getType());
            }else{
                serviceKey = orderReadManageServiceKeyMap.get(OrderShowTypeEnum.CROSS_BORDER.getType());
            }

            PageInfo<OrderReadManageApiDTO> list = orderReadManageServiceMap.get(serviceKey).list(orderReadManagePageReqApiDTO);
            return DataResponse.success(list);
        } catch (Exception e){
            dataResponse = DataResponse.error(DataResponseCode.SYSTEM_ERROR.getCode(), "系统异常");
            log.error("OrderReadManageApiServiceImpl.queryOrderPage, req = {}, result = {}", JSON.toJSONString(orderReadManagePageReqApiDTO), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("OrderReadManageApiServiceImpl.queryOrderPage, req = {}, result = {}", JSON.toJSONString(orderReadManagePageReqApiDTO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }

    @Override
    public DataResponse<OrderReadManageApiDTO> queryOrderDetail(OrderReadDetailReqApiDTO orderReadDetailReqApiDTO) {
        DataResponse<OrderReadManageApiDTO> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.order.OrderReadManageApiServiceImpl.queryOrderDetail." + active);
        try {
            ValidateResult<OrderReadDetailReqApiDTO> validateResult = ValidationUtil.checkParam(orderReadDetailReqApiDTO);
            if(!validateResult.getSuccess()){
                Set<String> messages = validateResult.getPropertyErrors().stream().map(PropertyError::getMessage).collect(Collectors.toSet());
                return DataResponse.error(DataResponseCode.PARAM_ERROR.getCode(), String.join(Constant.COMMA, messages));
            }

            OrderPO orderPO = orderAtomicService.getOrderPOById(orderReadDetailReqApiDTO.getOrderId());
            if(orderPO == null){
                log.error("com.jdi.isc.order.center.service.protocol.jsf.order.OrderReadManageApiServiceImpl.queryOrderDetail orderPO is null, orderId = {}", orderReadDetailReqApiDTO.getOrderId());
                return DataResponse.error(DataResponseCode.DATA_NULL.getCode(), "订单不存在");
            }

            String serviceKey = "";
            if(PurchaseModelEnum.WIMP_STOCK_MODEL.contains(orderPO.getPurchaseModel())){
                serviceKey = orderReadManageServiceKeyMap.get(OrderShowTypeEnum.STOCK_UP.getType());
            }else if(OrderTypeConstant.LOCAL == orderPO.getOrderType()){
                serviceKey = orderReadManageServiceKeyMap.get(OrderShowTypeEnum.LOCAL_DIRECT.getType());
            }else{
                serviceKey = orderReadManageServiceKeyMap.get(OrderShowTypeEnum.CROSS_BORDER.getType());
            }

            OrderReadManageApiDTO detail = orderReadManageServiceMap.get(serviceKey).detail(orderPO, orderReadDetailReqApiDTO);
            return DataResponse.success(detail);
        } catch (Exception e){
            dataResponse = DataResponse.error(DataResponseCode.SYSTEM_ERROR.getCode(), "系统异常");
            log.error("OrderReadManageApiServiceImpl.queryOrderDetail, req = {}, result = {}", JSON.toJSONString(orderReadDetailReqApiDTO), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("OrderReadManageApiServiceImpl.queryOrderDetail, req = {}, result = {}", JSON.toJSONString(orderReadDetailReqApiDTO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }

    @Override
    public DataResponse<List<OrderReadSkuStockDetailApiDTO>> queryOrderDetailAndAvailableStockInfo(OrderReadDetailReqApiDTO orderReadDetailReqApiDTO) {
        DataResponse<List<OrderReadSkuStockDetailApiDTO>> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.order.OrderReadManageApiServiceImpl.queryOrderDetailAndAvailableStockInfo." + active);

        Long orderId = orderReadDetailReqApiDTO.getOrderId();
        try {
            ValidateResult<OrderReadDetailReqApiDTO> validateResult = ValidationUtil.checkParam(orderReadDetailReqApiDTO);
            if (!validateResult.getSuccess()) {
                Set<String> messages = validateResult.getPropertyErrors().stream().map(PropertyError::getMessage).collect(Collectors.toSet());
                return DataResponse.error(DataResponseCode.PARAM_ERROR.getCode(), String.join(Constant.COMMA, messages));
            }

            OrderPO orderPO = orderAtomicService.getOrderPOById(orderReadDetailReqApiDTO.getOrderId());
            if (orderPO == null) {
                log.warn("OrderReadManageApiServiceImpl.queryOrderDetailAndAvailableStockInfo orderPO is null, orderId = {}", orderReadDetailReqApiDTO.getOrderId());
                return DataResponse.error(DataResponseCode.DATA_NULL.getCode(), "订单不存在!");
            }

            //订单状态校验
            if (!Objects.equals(orderPO.getOrderStatus(), OrderStatusConstant.CONFIRM_INNER_ORDER.getStatus()) ||
                    Objects.equals(orderPO.getHoldStatus(), OrderHoldStatusEnum.UN_HOLD.getCode()) ||
                    Objects.isNull(orderPO.getStockUpWarehouseId())) {
                log.warn("OrderReadManageApiServiceImpl.queryOrderDetailAndAvailableStockInfo orderPO status illegal! orderId = {}", orderReadDetailReqApiDTO.getOrderId());
                return DataResponse.error(DataResponseCode.NO_PERMISSION.getCode(), "数据已刷新，请重新查询!");
            }

            //获取订单明细
            List<OrderWarePO> orderWarePOList = orderWareAtomicService.getOrderWarePOListByOrderId(orderId);
            if (CollectionUtils.isEmpty(orderWarePOList)) {
                log.warn("OrderReadManageApiServiceImpl.queryOrderDetailAndAvailableStockInfo orderWarePO is null, orderId = {}", orderReadDetailReqApiDTO.getOrderId());
                return DataResponse.error(DataResponseCode.DATA_NULL.getCode(), "订单商品明细不存在!");
            }


            //获取可用库存现货信息
            StockCompReadReqDTO stockRequest = buildStockCompReadReqDTO(orderPO, orderWarePOList);
            Map<Long, IscMkuStockResDTO> stockResDTOMap = stockReadRpcService.getOnWayOrderStock(stockRequest);


            //获取多语言
            String language = orderReadDetailReqApiDTO.getSystemInfo().getLocaleList().get(0);

            //组装结果
            List<OrderReadSkuStockDetailApiDTO> orderReadSkuStockDetailApiDTOList = buildOrderReadSkuStockDetailApiDTOS(orderWarePOList, language, stockResDTOMap);
            dataResponse = DataResponse.success(orderReadSkuStockDetailApiDTOList);

        } catch (Exception e) {
            dataResponse = DataResponse.error(DataResponseCode.SYSTEM_ERROR.getCode(), "查询异常:" + e.getMessage());
            log.error("OrderReadManageApiServiceImpl.queryOrderDetailAndAvailableStockInfo failed, req = {}, result = {}", JSON.toJSONString(orderReadDetailReqApiDTO), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("OrderReadManageApiServiceImpl.queryOrderDetailAndAvailableStockInfo finished, req = {}, result = {}", JSON.toJSONString(orderReadDetailReqApiDTO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }

        return dataResponse;
    }

    /**
     * 构建订单商品库存详情DTO列表
     * @param orderWarePOList 订单商品PO列表，包含商品基本信息
     * @param language 语言标识，用于获取对应语言的商品名称
     * @param stockResDTOMap 商品库存信息映射，key为skuId，value为库存详情
     * @return 订单商品库存详情DTO列表，包含商品基础信息、名称及可用库存
     */
    @NotNull
    private  List<OrderReadSkuStockDetailApiDTO> buildOrderReadSkuStockDetailApiDTOS(List<OrderWarePO> orderWarePOList, String language, Map<Long, IscMkuStockResDTO> stockResDTOMap) {
        List<OrderReadSkuStockDetailApiDTO> orderReadSkuStockDetailApiDTOList = Lists.newArrayList();
        for(OrderWarePO orderWarePO: orderWarePOList) {
            OrderReadSkuStockDetailApiDTO orderReadSkuStockDetail = new OrderReadSkuStockDetailApiDTO();
            orderReadSkuStockDetail.setMkuId(orderWarePO.getMkuId());
            orderReadSkuStockDetail.setSkuId(orderWarePO.getSkuId());
            orderReadSkuStockDetail.setMkuNum(orderWarePO.getMkuNum());

            //设置商品名称
            String mkuJsonInfo = orderWarePO.getMkuJsonInfo();
            OrderWareMkuJsonInfoPO orderWareMkuJsonInfoPO = JSON.parseObject(mkuJsonInfo, OrderWareMkuJsonInfoPO.class);
            Map<String, String> mkuNameMap = orderWareMkuJsonInfoPO.getMkuNameMap();
            orderReadSkuStockDetail.setMkuName(mkuNameMap.get(language));

            //设置可用现货库存
            IscMkuStockResDTO stockResDTO = stockResDTOMap.get(orderWarePO.getSkuId());
            orderReadSkuStockDetail.setAvailableStock(stockResDTO != null ? stockResDTO.getAvailableStock().intValue() : Constant.ZERO_INTEGER);

            orderReadSkuStockDetailApiDTOList.add(orderReadSkuStockDetail);
        }
        return orderReadSkuStockDetailApiDTOList;
    }

    /**
     * 根据订单信息及订单商品列表构建库存查询请求DTO
     * @param orderPO 订单信息对象，包含父订单ID、订单ID、客户编码和国家编码等
     * @param orderWarePOList 订单商品列表，包含商品SKU、MKU信息及数量等
     * @return 构建完成的库存查询请求DTO对象
     */
    @NotNull
    private  StockCompReadReqDTO buildStockCompReadReqDTO(OrderPO orderPO, List<OrderWarePO> orderWarePOList) {
        StockCompReadReqDTO stockRequest = new StockCompReadReqDTO();
        stockRequest.setOrderId(orderPO.getParentOrderId() != null ? String.valueOf(orderPO.getParentOrderId()) : null);
        stockRequest.setBizNo(String.valueOf(orderPO.getOrderId()));
        stockRequest.setClientCode(orderPO.getClientCode());
        stockRequest.setCountryCode(orderPO.getCountryCode());

        List<StockCompItemReadReqDTO> stockItems = Lists.newArrayList();
        for (OrderWarePO orderWarePO : orderWarePOList) {
            StockCompItemReadReqDTO stockCompItemReadReq = new StockCompItemReadReqDTO();
            stockCompItemReadReq.setMkuId(orderWarePO.getMkuId());
            stockCompItemReadReq.setSkuId(orderWarePO.getSkuId());
            stockCompItemReadReq.setNum(1L);

            //设置备货仓编码
            String skuJsonInfo = orderWarePO.getSkuJsonInfo();
            OrderSkuDetailPO orderSkuDetailPO = JSON.parseObject(skuJsonInfo, OrderSkuDetailPO.class);

            stockCompItemReadReq.setWarehouseId(orderSkuDetailPO.getWarehouseId() != null ? String.valueOf(orderSkuDetailPO.getWarehouseId()) : null);
            stockItems.add(stockCompItemReadReq);
        }
        stockRequest.setStockItem(stockItems);
        return stockRequest;
    }
}
