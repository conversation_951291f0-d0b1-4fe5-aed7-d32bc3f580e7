package com.jdi.isc.order.center.service.adapter.mapstruct.orderSnapshot;

import com.jdi.isc.order.center.api.orderSnapshot.req.OrderWareSnapshotReqApiDTO;
import com.jdi.isc.order.center.api.orderSnapshot.res.OrderWareSnapshotResApiDTO;
import com.jdi.isc.order.center.domain.orderSnapshot.biz.OrderWareSnapshotVO;
import com.jdi.isc.order.center.domain.orderSnapshot.po.OrderWareSnapshotPO;
import com.jdi.isc.order.center.domain.orderSnapshot.req.OrderWareSnapshotReqVO;
import com.jdi.isc.order.center.domain.orderSnapshot.res.OrderWareSnapshotResVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 订单商品快照对象转换
 * @Author: zhaojianguo21
 * @Date: 2025/5/26
 **/
@Mapper
public interface OrderWareSnapshotConvert {

    OrderWareSnapshotConvert INSTANCE = Mappers.getMapper(OrderWareSnapshotConvert.class);

    OrderWareSnapshotReqVO orderWareSnapshotReqApiDto2Vo(OrderWareSnapshotReqApiDTO input);

    OrderWareSnapshotResApiDTO orderWareSnapshotResVo2ApiDto(OrderWareSnapshotResVO input);

    OrderWareSnapshotResVO orderWareSnapshotPo2ResVo(OrderWareSnapshotPO input);

    OrderWareSnapshotVO orderWareSnapshotPo2Vo(OrderWareSnapshotPO input);

    OrderWareSnapshotPO orderWareSnapshotVo2Po(OrderWareSnapshotVO input);

    List<OrderWareSnapshotPO> listOrderWareSnapshotVo2Po(List<OrderWareSnapshotVO> input);
}