package com.jdi.isc.order.center.service.protocol.jsf.waybill;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.waybill.OrderWaybillReadApiService;
import com.jdi.isc.order.center.api.waybill.biz.QueryOrderSignatureFormReqResApiDTO;
import com.jdi.isc.order.center.api.common.BaseBizApiDTO;
import com.jdi.isc.order.center.domain.waybill.biz.QueryOrderSignatureFormReqRes;
import com.jdi.isc.order.center.service.adapter.mapstruct.waybill.OrderWaybillConvert;
import com.jdi.isc.order.center.service.manage.waybill.order.OrderWaybillReadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2024/12/27 14:25
 **/
@Slf4j
@Service
public class OrderWaybillReadApiServiceImpl implements OrderWaybillReadApiService {

    @Resource
    private OrderWaybillReadService orderWaybillReadService;

    @ToolKit
    @Override
    public DataResponse<List<QueryOrderSignatureFormReqResApiDTO.Response>> queryOrderSignatureFormBatch(QueryOrderSignatureFormReqResApiDTO.Request input) {
        QueryOrderSignatureFormReqRes.Request req = OrderWaybillConvert.INSTANCE.signatureFormReqApi2Vo(input);
        DataResponse<List<QueryOrderSignatureFormReqRes.Response>> dataResponse = orderWaybillReadService.queryOrderSignatureFormBatch(req);
        if (!Boolean.TRUE.equals(dataResponse.getSuccess())){
            return DataResponse.error(dataResponse.getMessage());
        }
        List<QueryOrderSignatureFormReqResApiDTO.Response> dataResponseData = OrderWaybillConvert.INSTANCE.listSignatureFormResVo2Api(dataResponse.getData());
        return DataResponse.success(dataResponseData);
    }

    /** 查询给定的二段运单号是否存在*/
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<String, Boolean>> checkWaybillExist(BaseBizApiDTO req) {
        return DataResponse.success(orderWaybillReadService.checkWaybillExist(req));
    }
}
