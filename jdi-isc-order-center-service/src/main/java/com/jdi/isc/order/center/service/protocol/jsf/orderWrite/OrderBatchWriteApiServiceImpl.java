package com.jdi.isc.order.center.service.protocol.jsf.orderWrite;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.orderWrite.OrderBatchWriteApiService;
import com.jdi.isc.order.center.api.orderWrite.biz.OrderBatchUpdateConfirmImportApiDTO;
import com.jdi.isc.order.center.domain.order.biz.OrderBatchUpdateConfirmImportVO;
import com.jdi.isc.order.center.service.adapter.mapstruct.order.OrderConvert;
import com.jdi.isc.order.center.service.manage.order.OrderBatchWriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2025/1/8 21:47
 **/
@Slf4j
@Service
public class OrderBatchWriteApiServiceImpl implements OrderBatchWriteApiService {

    @Resource
    private OrderBatchWriteService orderBatchWriteService;

    @ToolKit(exceptionWrap = true)
    @Override
    public DataResponse<OrderBatchUpdateConfirmImportApiDTO.Response> batchUpdateConfirm(OrderBatchUpdateConfirmImportApiDTO.Request input) {
        OrderBatchUpdateConfirmImportVO.Request requestVo = OrderConvert.INSTANCE.batchUpdateConfirmReqApiDto2Vo(input);
        DataResponse<OrderBatchUpdateConfirmImportVO.Response> serviceResponse = orderBatchWriteService.batchUpdateConfirm(requestVo);
        if (!Boolean.TRUE.equals(serviceResponse.getSuccess())){
            log.warn("batchUpdateConfirm fail, serviceResponse={}", JSONObject.toJSONString(serviceResponse));
            return DataResponse.error(serviceResponse.getMessage());
        }
        OrderBatchUpdateConfirmImportApiDTO.Response responseData = OrderConvert.INSTANCE.batchUpdateConfirmResVo2ApiDto(serviceResponse.getData());
        return DataResponse.success(responseData);
    }
}
