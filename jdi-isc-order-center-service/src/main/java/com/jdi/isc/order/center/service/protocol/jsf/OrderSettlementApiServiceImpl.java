package com.jdi.isc.order.center.service.protocol.jsf;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.settlement.OrderSettlementApiService;
import com.jdi.isc.order.center.api.settlement.biz.SettlementOrderApiReq;
import com.jdi.isc.order.center.service.manage.settlement.OrderSettlementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OrderSettlementApiServiceImpl implements OrderSettlementApiService {

    @Autowired
    private OrderSettlementService orderSettlementService;

    @ToolKit
    @Override
    public DataResponse<Boolean> saveSettlement(SettlementOrderApiReq req) {
        DataResponse<Boolean> dataResponse = null;
        try {
            dataResponse = orderSettlementService.saveSettlement(req);
        } finally {
            log.error("OrderSettlementApiServiceImpl.saveSettlement, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse));
        }
        return dataResponse;
    }
}
