package com.jdi.isc.order.center.service.protocol.jsf;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.settlement.PurchaseOrderSettlementApiService;
import com.jdi.isc.order.center.api.settlement.biz.SettlementPurchaseOrderApplyApiReq;
import com.jdi.isc.order.center.api.settlement.biz.SettlementPurchaseOrderApproveApiReq;
import com.jdi.isc.order.center.service.manage.settlement.PurchaseOrderSettlementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PurchaseOrderSettlementApiServiceImpl implements PurchaseOrderSettlementApiService {

    @Autowired
    private PurchaseOrderSettlementService purchaseOrderSettlementService;

    @ToolKit
    @Override
    public DataResponse<Boolean> applySettlement(SettlementPurchaseOrderApplyApiReq req) {
        DataResponse<Boolean> dataResponse = null;
        try {
            dataResponse = purchaseOrderSettlementService.applySettlement(req);
        }finally {
            log.error("PurchaseOrderSettlementServiceImpl.applySettlement, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse));
        }
        return dataResponse;
    }

    @Override
    public DataResponse<Boolean> approveSettlement(SettlementPurchaseOrderApproveApiReq req) {
        DataResponse<Boolean> dataResponse = null;
        try {
            dataResponse = purchaseOrderSettlementService.approveSettlement(req);
        } finally {
            log.error("PurchaseOrderSettlementServiceImpl.approveSettlement, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse));
        }
        return dataResponse;
    }
}
