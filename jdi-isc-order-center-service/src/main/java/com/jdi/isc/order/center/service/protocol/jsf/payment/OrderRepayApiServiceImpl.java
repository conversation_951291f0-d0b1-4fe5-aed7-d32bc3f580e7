package com.jdi.isc.order.center.service.protocol.jsf.payment;

import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.payment.OrderRepayApiService;
import com.jdi.isc.order.center.api.payment.req.OrderRepaymentInfoReqDTO;
import com.jdi.isc.order.center.api.payment.res.OrderRepaymentInfoRespDTO;
import com.jdi.isc.order.center.service.manage.payment.OrderRepayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 订单还款相关接口
 * @date 2025/7/10 15:59
 */
@Service
@Slf4j
public class OrderRepayApiServiceImpl implements OrderRepayApiService {

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private OrderRepayService orderRepayService;


    @Override
    public DataResponse<OrderRepaymentInfoRespDTO> queryOrderInfo4Repayment(OrderRepaymentInfoReqDTO orderRepaymentInfoReqDTO) {
        DataResponse<OrderRepaymentInfoRespDTO> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.payment.OrderRepayApiServiceImpl.queryOrderInfo4Repayment." + active);
        try {
            return orderRepayService.queryOrderRepayInfo(orderRepaymentInfoReqDTO);
        } catch (Exception e){
            log.error("OrderRepayApiServiceImpl.queryOrderInfo4Repayment, req = {}, result = {}", JSON.toJSONString(orderRepaymentInfoReqDTO), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("OrderRepayApiServiceImpl.queryOrderInfo4Repayment, req = {}, result = {}", JSON.toJSONString(orderRepaymentInfoReqDTO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }
}
