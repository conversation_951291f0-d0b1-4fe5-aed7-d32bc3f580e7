package com.jdi.isc.order.center.service.protocol.jsf.invoice;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.finance.invoice.InvoiceApiService;
import com.jdi.isc.order.center.api.finance.invoice.req.InvoiceReqDTO;
import com.jdi.isc.order.center.api.finance.invoice.res.InvoiceResDTO;
import com.jdi.isc.order.center.service.manage.finance.invoice.InvoiceManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 发票申请服务
 * <AUTHOR>
 * @date 2024/7/25
 */
@Service
@Slf4j
public class InvoiceApiServiceImpl implements InvoiceApiService {
    @Resource
    private InvoiceManageService invoiceManageService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Void> applyInvoice(InvoiceReqDTO req) {
        return invoiceManageService.applyInvoice(req);
    }
}
