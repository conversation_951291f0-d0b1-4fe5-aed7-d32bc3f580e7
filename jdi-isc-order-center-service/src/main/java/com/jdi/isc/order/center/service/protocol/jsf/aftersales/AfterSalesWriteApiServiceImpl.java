package com.jdi.isc.order.center.service.protocol.jsf.aftersales;

import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.isc.order.center.api.aftersales.AfterSalesWriteApiService;
import com.jdi.isc.order.center.api.aftersales.req.AfterSalesInvoiceSubmitReqDTO;
import com.jdi.isc.order.center.api.aftersales.req.AfterSalesSubmitReqDTO;
import com.jdi.isc.order.center.api.aftersales.req.UpdateAfterSalesInfoApiDTO;
import com.jdi.isc.order.center.api.aftersales.req.UpdateAfterSalesStatusApiDTO;
import com.jdi.isc.order.center.api.aftersales.res.AfterSalesInvoiceRespApiDTO;
import com.jdi.isc.order.center.api.constants.DataResponseCodeConstant;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.common.utils.validation.PropertyError;
import com.jdi.isc.order.center.common.utils.validation.ValidateResult;
import com.jdi.isc.order.center.common.utils.validation.ValidationUtil;
import com.jdi.isc.order.center.domain.aftersales.po.AfterSalesPO;
import com.jdi.isc.order.center.domain.invoice.customer.po.CustomerInvoiceApplyPO;
import com.jdi.isc.order.center.domain.invoice.customer.vo.CustomerReInvoiceApplySubmitReqVO;
import com.jdi.isc.order.center.service.atomic.aftersales.AfterSalesAtomicService;
import com.jdi.isc.order.center.service.manage.aftersales.AfterSalesWriteService;
import com.jdi.isc.order.center.service.manage.invoice.CustomerInvoiceService;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 售后服务写接口
 * @date 2025/6/25 16:22
 */
@Service
@Slf4j
public class AfterSalesWriteApiServiceImpl implements AfterSalesWriteApiService {

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private AfterSalesWriteService afterSalesWriteService;

    @Autowired
    private AfterSalesAtomicService afterSalesAtomicService;

    @Autowired
    private CustomerInvoiceService customerInvoiceService;

    @Override
    public DataResponse<String> submitAfterSales(AfterSalesSubmitReqDTO afterSalesSubmitReqDTO) {
        DataResponse<String> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.aftersales.AfterSalesWriteApiServiceImpl.submitAfterSales." + active);
        try {
            dataResponse = afterSalesWriteService.submitAfterSalesOrder(afterSalesSubmitReqDTO);
        } catch (Exception e){
            dataResponse = DataResponse.error(DataResponseCode.SYSTEM_ERROR.getCode());
            log.error("AfterSalesWriteApiServiceImpl.submitAfterSales, req = {}, result = {}", JSON.toJSONString(afterSalesSubmitReqDTO), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("AfterSalesWriteApiServiceImpl.submitAfterSales, req = {}, result = {}", JSON.toJSONString(afterSalesSubmitReqDTO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }

    @Override
    public DataResponse<Boolean> updateAfterSalesStatus(UpdateAfterSalesStatusApiDTO req) {
        DataResponse<Boolean> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.aftersales.AfterSalesWriteApiServiceImpl.updateAfterSalesStatus." + active);
        try {
            dataResponse = afterSalesWriteService.updateAfterSalesStatus(req);
        } catch (Exception e){
            dataResponse = DataResponse.error(DataResponseCode.SYSTEM_ERROR.getCode());
            log.error("AfterSalesWriteApiServiceImpl.updateAfterSalesStatus, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("AfterSalesWriteApiServiceImpl.updateAfterSalesStatus, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }

    @Override
    public DataResponse<Boolean> reassign(UpdateAfterSalesInfoApiDTO req) {
        return afterSalesWriteService.reassign(req);
    }


    @Override
    public DataResponse<Boolean> audit(UpdateAfterSalesInfoApiDTO req) {
        return afterSalesWriteService.audit(req);
    }

    @Override
    public DataResponse<AfterSalesInvoiceRespApiDTO> submitAfterSalesInvoice(AfterSalesInvoiceSubmitReqDTO reqDTO) {
        DataResponse<AfterSalesInvoiceRespApiDTO> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.aftersales.AfterSalesWriteApiServiceImpl.submitAfterSalesInvoice." + active);
        try {

            // 1、参数校验，是否都填了
            ValidateResult<AfterSalesInvoiceSubmitReqDTO> validateResult = ValidationUtil.checkParam(reqDTO);
            if (!validateResult.getSuccess()) {
                Set<String> messages = validateResult.getPropertyErrors().stream().map(PropertyError::getMessage).collect(Collectors.toSet());
                return DataResponse.error(DataResponseCode.PARAM_ERROR.getCode(), String.join(Constant.COMMA, messages));
            }

            // 2、查询售后单是否存在 客户+售后id
            AfterSalesPO queryAfterSalesPO = new AfterSalesPO();
            queryAfterSalesPO.setAfterSalesOrderId(reqDTO.getAfsServiceId());
            queryAfterSalesPO.setSourceCode(reqDTO.getSourceCode());
            queryAfterSalesPO.setClientCode(reqDTO.getClientCode());
            AfterSalesPO afterSalesPO = afterSalesAtomicService.queryByAfterSalesOrderId(queryAfterSalesPO);
            if(afterSalesPO == null){
                dataResponse = DataResponse.error(DataResponseCodeConstant.AFTER_SALES_DATA_NOT_EXIST, "售后单不存在");
                return dataResponse;
            }

            if(!CountryConstant.COUNTRY_BR.equals(afterSalesPO.getCountryCode())){
                log.info("com.jdi.isc.order.center.service.protocol.jsf.aftersales.AfterSalesWriteApiServiceImpl.submitAfterSalesInvoice, 非巴西售后单不支持开票, req = {}", JSON.toJSONString(reqDTO));
                dataResponse = DataResponse.error(DataResponseCode.PARAM_ERROR.getCode(), "非巴西售后单不支持开票");
                return dataResponse;
            }

            // 3、保存发票到数据库
            CustomerReInvoiceApplySubmitReqVO customerReInvoiceApplySubmitReqVO = new CustomerReInvoiceApplySubmitReqVO();
            customerReInvoiceApplySubmitReqVO.setOperator(reqDTO.getPin());
            customerReInvoiceApplySubmitReqVO.setRequestId(reqDTO.getRequestId());
            customerReInvoiceApplySubmitReqVO.setAfsServiceId(reqDTO.getAfsServiceId());
            customerReInvoiceApplySubmitReqVO.setReInvoiceNo(reqDTO.getReInvoiceNo());
            customerReInvoiceApplySubmitReqVO.setInvoiceFileList(reqDTO.getInvoiceFileList());

            dataResponse = customerInvoiceService.submitReInvoice(customerReInvoiceApplySubmitReqVO);

        } catch (Exception e){
            dataResponse = DataResponse.error(DataResponseCode.SYSTEM_ERROR.getCode());
            log.error("AfterSalesWriteApiServiceImpl.submitAfterSalesInvoice, req = {}, result = {}", JSON.toJSONString(reqDTO), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("AfterSalesWriteApiServiceImpl.submitAfterSalesInvoice, req = {}, result = {}", JSON.toJSONString(reqDTO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }
}
