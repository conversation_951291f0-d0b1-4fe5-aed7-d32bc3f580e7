package com.jdi.isc.order.center.service.protocol.jsf.config;

import com.jd.jdi.yz.core.utils.CollectionUtils;
import com.jd.jdi.yz.core.utils.StringUtils;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.config.OrderConfigApiService;
import com.jdi.isc.order.center.api.config.biz.ClientIopConfigInfoDTO;
import com.jdi.isc.order.center.api.config.biz.CrossBorderSkuIopInfoDTO;
import com.jdi.isc.order.center.api.config.biz.StoreHouseConfigInfoDTO;
import com.jdi.isc.order.center.domain.config.CrossBorderSkuIopInfoVO;
import com.jdi.isc.order.center.domain.config.OrderDuccConfig;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.DuccConfigContactInfoVO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.DuccConfigIopInvoiceInfoVo;
import com.jdi.isc.order.center.service.adapter.mapstruct.config.CommonConfigConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/10
 * @Description 订单、履约域通用ducc配置接口实现类
 */
@Service
@Slf4j
public class OrderConfigApiServiceImpl implements OrderConfigApiService {

    @Autowired
    private OrderDuccConfig orderDuccConfig;


    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<ClientIopConfigInfoDTO> queryClientIopConfigInfo(String countryCode, String contractNum) {
        if (StringUtils.isBlank(countryCode)) {
            return DataResponse.error("必填参数countryCode为空.");
        }

        DuccConfigIopInvoiceInfoVo duccConfigIopInvoiceInfoVo = orderDuccConfig.iopInvoiceInfoVo(countryCode, contractNum);
        ClientIopConfigInfoDTO clientIopConfigInfoDTO = new ClientIopConfigInfoDTO();
        BeanUtils.copyProperties(duccConfigIopInvoiceInfoVo, clientIopConfigInfoDTO);

        return DataResponse.success(clientIopConfigInfoDTO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<StoreHouseConfigInfoDTO> queryStoreHouseConfigInfo(String storehouseCode) {
        if (StringUtils.isBlank(storehouseCode)) {
            return DataResponse.error("必填参数storehouseCode为空.");
        }

        DuccConfigContactInfoVO duccConfigContactInfoVO = orderDuccConfig.getStorehouseConfigByCode(storehouseCode);
        StoreHouseConfigInfoDTO storeHouseConfigInfoDTO = new StoreHouseConfigInfoDTO();
        BeanUtils.copyProperties(duccConfigContactInfoVO, storeHouseConfigInfoDTO);

        return DataResponse.success(storeHouseConfigInfoDTO);
    }

    @Override
    public DataResponse<List<CrossBorderSkuIopInfoDTO>> queryAllIopPinAndAddressConfigInfo() {
        List<CrossBorderSkuIopInfoVO> crossBorderSkuIopInfoVOList = orderDuccConfig.crossBorderSkuIopConfig();
        if (CollectionUtils.isEmpty(crossBorderSkuIopInfoVOList)) {
            return DataResponse.error("配置信息为空！");
        }

        List<CrossBorderSkuIopInfoDTO> crossBorderSkuIopInfoDTOS = CommonConfigConvert.INSTANCE.crossBorderIopInfopOToDTOList(crossBorderSkuIopInfoVOList);
        return DataResponse.success(crossBorderSkuIopInfoDTOS);
    }
}
