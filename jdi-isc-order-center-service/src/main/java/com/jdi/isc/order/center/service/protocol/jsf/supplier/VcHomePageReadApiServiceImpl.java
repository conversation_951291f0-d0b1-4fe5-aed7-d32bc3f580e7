package com.jdi.isc.order.center.service.protocol.jsf.supplier;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.supplier.VcHomePageReadApiService;
import com.jdi.isc.order.center.api.supplier.req.VcHomePageDashboardReq;
import com.jdi.isc.order.center.api.supplier.req.VcHomePageReminderReq;
import com.jdi.isc.order.center.api.supplier.resp.VcDashboardDTO;
import com.jdi.isc.order.center.api.supplier.resp.VcReminderDTO;
import com.jdi.isc.order.center.domain.config.OrderDuccConfig;
import com.jdi.isc.order.center.domain.mapstruct.supplier.VcHomePageDashboardVO;
import com.jdi.isc.order.center.domain.mapstruct.supplier.VcHomePageReminderVO;
import com.jdi.isc.order.center.service.adapter.mapstruct.vc.HomePageConvert;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderAtomicService;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.VcHomePageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 供应商数据报表相关业务
 */
@Service
@Slf4j
public class VcHomePageReadApiServiceImpl implements VcHomePageReadApiService {
    @Autowired
    private VcHomePageService vcHomePageService;
    @Autowired
    private PurchaseOrderAtomicService purchaseOrderAtomicService;
    @Autowired
    private OrderDuccConfig orderDuccConfig;

    @Override
    public DataResponse<Map<String, List<VcReminderDTO>>> getReminderList(VcHomePageReminderReq req) {
        VcHomePageReminderVO vo = HomePageConvert.INSTANCE.transformReq2VO(req);
        List<VcReminderDTO> invoiceList = vcHomePageService.invoiceCount(vo);
        return DataResponse.success(new HashMap<String, List<VcReminderDTO>>() {{
            put("invoice", invoiceList);
        }});
    }

    @Override
    public DataResponse<List<VcDashboardDTO>> getDashboardList(VcHomePageDashboardReq req) {
        VcHomePageDashboardVO vo = HomePageConvert.INSTANCE.transformDashboardReq2VO(req);
        return DataResponse.success(vcHomePageService.assembleDashboardData(vo));
    }

}
