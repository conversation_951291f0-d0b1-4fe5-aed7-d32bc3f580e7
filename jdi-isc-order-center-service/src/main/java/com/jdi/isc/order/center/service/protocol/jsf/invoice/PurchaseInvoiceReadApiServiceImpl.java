package com.jdi.isc.order.center.service.protocol.jsf.invoice;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.common.OperateApiDTO;
import com.jdi.isc.order.center.api.finance.purchaseInvoice.PurchaseInvoiceReadApiService;
import com.jdi.isc.order.center.api.finance.purchaseInvoice.req.*;
import com.jdi.isc.order.center.api.finance.purchaseInvoice.res.*;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.common.utils.JsonUtil;
import com.jdi.isc.order.center.domain.config.OrderDuccConfig;
import com.jdi.isc.order.center.domain.enums.invoice.TaxReviewStatusEnum;
import com.jdi.isc.order.center.domain.enums.settlement.SettlementButtonEnum;
import com.jdi.isc.order.center.domain.invoice.purchaseOrder.po.PurchaseInvoicePO;
import com.jdi.isc.order.center.domain.invoice.purchaseOrder.po.PurchaseInvoiceWarePO;
import com.jdi.isc.order.center.domain.invoice.purchaseOrder.vo.*;
import com.jdi.isc.order.center.domain.order.po.OrderSkuDetailPO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.InvoicePageVO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderWarePO;
import com.jdi.isc.order.center.service.adapter.mapstruct.invoice.InvoicePageVOConvert;
import com.jdi.isc.order.center.service.adapter.mapstruct.invoice.PurchaseInvoiceConvert;
import com.jdi.isc.order.center.service.atomic.invoice.PurchaseInvoiceAtomicService;
import com.jdi.isc.order.center.service.atomic.invoice.PurchaseInvoiceWareAtomicService;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderAtomicService;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderWareAtomicService;
import com.jdi.isc.order.center.service.manage.invoice.PurchaseInvoiceUpdatePoApplyService;
import com.jdi.isc.order.center.service.manage.xml.BrSupplierInvoiceXmlParserUtil;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/2 8:56 下午
 */

@Slf4j
@Service
public class PurchaseInvoiceReadApiServiceImpl implements PurchaseInvoiceReadApiService {

    @Autowired
    private PurchaseOrderAtomicService purchaseOrderAtomicService;

    @Autowired
    private PurchaseInvoiceAtomicService purchaseInvoiceAtomicService;

    @Autowired
    private PurchaseInvoiceWareAtomicService purchaseInvoiceWareAtomicService;

    @Autowired
    private OrderDuccConfig orderDuccConfig;

    @Autowired
    private BrSupplierInvoiceXmlParserUtil brSupplierInvoiceXmlParserUtil;

    @Autowired
    private PurchaseInvoiceUpdatePoApplyService purchaseInvoiceUpdatePoApplyService;

    @Autowired
    private PurchaseOrderWareAtomicService purchaseOrderWareAtomicService;

    @Override
    public DataResponse<PageInfo<PurchaseInvoiceMainDTO>> invoicePage(InvoicePageReq req) {
        PageInfo<PurchaseInvoiceMainDTO> pageInfo = new PageInfo<>();
        pageInfo.setSize(req.getSize());
        pageInfo.setIndex(req.getIndex());
        InvoicePageVO vo = InvoicePageVOConvert.INSTANCE.req2Vo(req);
        vo.setPurchaseOrderStatusSet(orderDuccConfig.getPurchaseInvoiceCountryShowStatus(req.getCountryCode()));
        Page<PurchaseOrderPO> purchaseOrderPage = purchaseOrderAtomicService.purchaseInvoicePageSearch(vo);
        pageInfo.setTotal(purchaseOrderPage.getTotal());
        pageInfo.setRecords(Collections.emptyList());
        if (!CollectionUtils.isEmpty(purchaseOrderPage.getRecords())) {
            List<PurchaseInvoiceMainDTO> recorders = PurchaseInvoiceConvert.INSTANCE.listPo2MainDTO(purchaseOrderPage.getRecords());
            pageInfo.setRecords(recorders);
        }
        return DataResponse.success(pageInfo);
    }

    @Override
    public DataResponse<PurchaseOrderInvoiceDTO> invoiceDetail(PurchaseInvoiceMainDTO mainDTO) {
        PurchaseOrderInvoiceVO result = checkPurchaseOrderAuth(mainDTO);
        if (Objects.isNull(result)) {
            return DataResponse.buildSuccess(null);
        }
        PurchaseInvoiceVO param = new PurchaseInvoiceVO();
        param.setPurchaseOrderId(mainDTO.getPurchaseOrderId());
        List<PurchaseInvoicePO> list = purchaseInvoiceAtomicService.queryByCondition(param);
        if (CollectionUtils.isEmpty(list)) {
            return DataResponse.buildSuccess(null);
        }
        List<PurchaseInvoiceDetailDTO> detailList = PurchaseInvoiceConvert.INSTANCE.listInvoicePo2DetailDto(list);
        PurchaseInvoiceWareVO invoiceWareVO = new PurchaseInvoiceWareVO();
        invoiceWareVO.setPurchaseOrderId(mainDTO.getPurchaseOrderId());
        List<PurchaseInvoiceWarePO> wareList = purchaseInvoiceWareAtomicService.queryByCondition(invoiceWareVO);
        List<PurchaseInvoiceWareDTO> wareVoList = PurchaseInvoiceConvert.INSTANCE.listPo2Dto(wareList);
        //查询采购单商品信息
        Map<Long, PurchaseOrderWarePO> purchaseWareMap = getPurchaseWareMap(mainDTO.getPurchaseOrderId());

        Map<String, List<PurchaseInvoiceWareDTO>> wareMap = getPurchaseInvoiceWareMap(wareVoList,purchaseWareMap);
        setPurchaseInvoiceData(detailList,wareMap);
        result.setInvoiceDetailDTOS(detailList);
        PurchaseOrderInvoiceDTO response = PurchaseInvoiceConvert.INSTANCE.purchaseOrderInvoice2Dto(result);

        //处理客户输入未税价的情况
        response.getInvoiceDetailDTOS().forEach(r -> {
            if (Objects.nonNull(r.getCustomerPurchaseTotalPrice()) && BigDecimal.ZERO.compareTo(r.getCustomerPurchaseTotalPrice()) < 0) {
                r.setWaresInvoiceTotalPrice(r.getCustomerPurchaseTotalPrice());
            }
        });

        return DataResponse.buildSuccess(response);
    }


    /**
     * 组装发票商品信息
     */
    private Map<String, List<PurchaseInvoiceWareDTO>> getPurchaseInvoiceWareMap(List<PurchaseInvoiceWareDTO> wareVoList, Map<Long, PurchaseOrderWarePO> purchaseWareMap) {
        return wareVoList.stream().peek(wareVo -> {
                    if(wareVo.getCustomerSkuInvoiceTotalPrice() == null) {
                        wareVo.setCustomerSkuInvoiceTotalPrice(wareVo.getIncludeTaxPriceTotal());
                    }
                    final String valueAddedTaxesInfo = wareVo.getValueAddedTaxesInfo();
                    log.info("invoiceDetail#valueAddedTaxesInfo:{}",valueAddedTaxesInfo);
                    if(StringUtils.isNotBlank(valueAddedTaxesInfo)){
                        PurchaseInvoiceWareTaxInfoDTO purchaseOrderWareTaxInfoDTO = JsonUtil.getJavaObject(valueAddedTaxesInfo, PurchaseInvoiceWareTaxInfoDTO.class);
                        wareVo.setPurchaseOrderWareTaxInfoDTO(purchaseOrderWareTaxInfoDTO);
                    }
                    PurchaseOrderWarePO warePO = purchaseWareMap.get(wareVo.getSkuId());
                    if (Objects.nonNull(warePO) && StringUtils.isNotBlank(warePO.getSkuJsonInfo())) {
                        OrderSkuDetailPO skuDetailPO = JSON.parseObject(warePO.getSkuJsonInfo(), OrderSkuDetailPO.class);
                        wareVo.setSpuId(skuDetailPO.getSpuId());
                        wareVo.setSkuImage(skuDetailPO.getMainImg());
                    }
                })
                .collect(Collectors.groupingBy(PurchaseInvoiceWareDTO::getInvoiceId));
    }


    /**
     * 组装发票商品信息
     */
    private Map<String, List<PurchaseInvoiceWareDTO>> getPurchaseInvoiceWareMap2(List<PurchaseInvoiceWareDTO> wareVoList, Map<String, PurchaseOrderWarePO> purchaseWareMap) {

         wareVoList.stream().forEach(wareVo -> {
                    if(wareVo.getCustomerSkuInvoiceTotalPrice() == null) {
                        wareVo.setCustomerSkuInvoiceTotalPrice(wareVo.getIncludeTaxPriceTotal());
                    }
                    final String valueAddedTaxesInfo = wareVo.getValueAddedTaxesInfo();
                    log.info("invoiceDetail#valueAddedTaxesInfo:{}",valueAddedTaxesInfo);
                    if(StringUtils.isNotBlank(valueAddedTaxesInfo)){
                        PurchaseInvoiceWareTaxInfoDTO purchaseOrderWareTaxInfoDTO = JsonUtil.getJavaObject(valueAddedTaxesInfo, PurchaseInvoiceWareTaxInfoDTO.class);
                        wareVo.setPurchaseOrderWareTaxInfoDTO(purchaseOrderWareTaxInfoDTO);
                    }
                    PurchaseOrderWarePO warePO = purchaseWareMap.get(wareVo.getPurchaseOrderId()+"#"+wareVo.getSkuId());
                    if (Objects.nonNull(warePO) && StringUtils.isNotBlank(warePO.getSkuJsonInfo())) {
                        OrderSkuDetailPO skuDetailPO = JSON.parseObject(warePO.getSkuJsonInfo(), OrderSkuDetailPO.class);
                        wareVo.setSpuId(skuDetailPO.getSpuId());
                        wareVo.setSkuImage(skuDetailPO.getMainImg());
                    }
                });

         log.info("getPurchaseInvoiceWareMap2#wareVoList:{}",wareVoList);
        return wareVoList.stream().collect(Collectors.groupingBy(PurchaseInvoiceWareDTO::getInvoiceId));
    }



    /**
     * 设置发票维度的信息
     */
    private void setPurchaseInvoiceData(List<PurchaseInvoiceDetailDTO> detailList, Map<String, List<PurchaseInvoiceWareDTO>> wareMap) {
        detailList.forEach(d -> {
            d.setPurchaseInvoiceWareRespVOList(wareMap.get(d.getInvoiceId()));
            if(StringUtils.isNotBlank(d.getInvoiceExtInfo())){
                PurchaseInvoiceExtInfoVO purchaseInvoiceExtInfoVO = JSON.parseObject(d.getInvoiceExtInfo(), PurchaseInvoiceExtInfoVO.class);
                d.setFileType(purchaseInvoiceExtInfoVO.getFileType());
                d.setInvoiceFlag(purchaseInvoiceExtInfoVO.getInvoiceFlag());
                d.setOtherFileType(purchaseInvoiceExtInfoVO.getOtherFileType());
                d.setInvoiceOtherUrl(purchaseInvoiceExtInfoVO.getInvoiceOtherUrl());
            }
        });
    }

    /**
     * 设置发票维度的信息
     */
    private void setPurchaseInvoiceData2(List<PurchaseInvoiceDetailDTO> detailList, Map<String, List<PurchaseInvoiceWareDTO>> wareMap) {
        detailList.forEach(d -> {
            d.setPurchaseInvoiceWareRespVOList(wareMap.get(d.getInvoiceId()));
            if(StringUtils.isNotBlank(d.getInvoiceExtInfo())){
                PurchaseInvoiceExtInfoVO purchaseInvoiceExtInfoVO = JSON.parseObject(d.getInvoiceExtInfo(), PurchaseInvoiceExtInfoVO.class);
                d.setFileType(purchaseInvoiceExtInfoVO.getFileType());
                d.setInvoiceFlag(purchaseInvoiceExtInfoVO.getInvoiceFlag());
                d.setOtherFileType(purchaseInvoiceExtInfoVO.getOtherFileType());
                d.setInvoiceOtherUrl(purchaseInvoiceExtInfoVO.getInvoiceOtherUrl());
            }
        });
        log.info("PurchaseInvoiceReadApiServiceImpl#setPurchaseInvoiceData2处理完成");
    }


    private Map<Long, PurchaseOrderWarePO> getPurchaseWareMap(String purchaseOrderId) {
        List<PurchaseOrderWarePO> purchaseWareList = purchaseOrderWareAtomicService.queryPurchaseOrderWarePOListById(purchaseOrderId);
        return purchaseWareList.stream().collect(HashMap::new, (m, v) -> m.put(v.getSkuId(), v), HashMap::putAll);
    }

    /**
     * 解析采购发票 XML 文件并返回解析结果。
     * @param mainDTO 包含 XML URL 的数据传输对象。
     * @return 解析结果的数据响应对象。
     */
    @Override
    public DataResponse<PurchaseInvoiceXmlParseDTO> invoiceXmlParse(PurchaseInvoiceMainDTO mainDTO) {
        try {
            // 检查 mainDTO 和 XML URL 是否为空
            if (mainDTO == null || StringUtils.isBlank(mainDTO.getXmlUrl())) {
                return DataResponse.error("Invalid input: mainDTO or XML URL is null");
            }
            // 解析 XML
            PurchaseInvoiceXmlParseVO xmlParseVO = brSupplierInvoiceXmlParserUtil.parseXml(mainDTO.getXmlUrl());
            PurchaseInvoiceXmlParseDTO xmlParseDTO = PurchaseInvoiceConvert.INSTANCE.xmlParse2Dto(xmlParseVO);
            return DataResponse.buildSuccess(xmlParseDTO);
        } catch (Exception e) {
            // 处理异常并返回失败响应
            log.error("InvoiceParserService.invoiceXmlParse error: ", e);
            return DataResponse.error(e.getMessage());
        }
    }


    @Override
    @ToolKit
    public DataResponse<List<PurchaseInvoiceDetailDTO>> invoiceMainList(PurchaseInvoiceMainDTO mainDTO) {
        if (CollectionUtils.isEmpty(mainDTO.getPurchaseOrderIds()) && StringUtils.isBlank(mainDTO.getCountryCode())) {
            return DataResponse.error("入参不能为空");
        }
        PurchaseInvoiceVO param = new PurchaseInvoiceVO();
        param.setPurchaseOrderIds(mainDTO.getPurchaseOrderIds());
        param.setCountryCode(mainDTO.getCountryCode());
        List<PurchaseInvoicePO> list = purchaseInvoiceAtomicService.queryByCondition(param);
        if (CollectionUtils.isEmpty(list)) {
            return DataResponse.buildSuccess(Collections.emptyList());
        }

        List<PurchaseInvoiceDetailDTO> detailList = PurchaseInvoiceConvert.INSTANCE.listInvoicePo2DetailDto(list);
        return DataResponse.success(detailList);
    }

    /**
     * 分页查询税务审核的采购发票信息。
     * @param req 查询条件，包括页码、每页记录数等。
     * @return 分页结果，包含当前页数据和分页信息。
     */
    @Override
    public DataResponse<PageInfo<PurchaseInvoiceDetailDTO>> taxReviewPage(TaxReviewPageReq req) {
        log.info("PurchaseInvoiceReadApiServiceImpl#taxReviewPage:{}",req);

        PageInfo<PurchaseInvoiceDetailDTO> pageInfo = new PageInfo<>();
        pageInfo.setSize(req.getSize());
        pageInfo.setIndex(req.getIndex());

        InvoicePageVO vo = new InvoicePageVO();

        if(req.getTaxReviewStatus()==null){
            vo.setTaxReviewStatus(Sets.newHashSet(TaxReviewStatusEnum.APPROVED.getCode(), TaxReviewStatusEnum.PENDING.getCode()));
        }else {
            vo.setTaxReviewStatus(Sets.newHashSet(req.getTaxReviewStatus()));
        }

        vo.setCountryCode(CountryConstant.COUNTRY_BR);
        vo.setPurchaseOrderId(req.getPurchaseOrderId());
        vo.setIndex(req.getIndex());
        vo.setSize(req.getSize());


        log.info("PurchaseInvoiceReadApiServiceImpl#InvoicePageVO:{}",vo);

        Page<PurchaseInvoicePO> purchaseInvoicePOPage = purchaseInvoiceAtomicService.purchaseInvoicePageSearch(vo);

        pageInfo.setTotal(purchaseInvoicePOPage.getTotal());
        pageInfo.setRecords(Collections.emptyList());
        if (!CollectionUtils.isEmpty(purchaseInvoicePOPage.getRecords())) {

            List<PurchaseInvoiceDetailDTO> recorders =JsonUtil.getJavaObject(purchaseInvoicePOPage.getRecords(),PurchaseInvoiceDetailDTO.class);
            pageInfo.setRecords(recorders);

        }
        return DataResponse.success(pageInfo);
    }

    /**
     * 检查采购订单授权并转换为发票信息。
     * @param param 包含采购订单ID和供应商代码的DTO对象。
     * @return 对应的采购订单发票VO对象。
     */
    private PurchaseOrderInvoiceVO checkPurchaseOrderAuth(PurchaseInvoiceMainDTO param){
        PurchaseOrderPO purchaseOrderPO = new PurchaseOrderPO();
        purchaseOrderPO.setPurchaseOrderId(param.getPurchaseOrderId());
        purchaseOrderPO.setSupplierCode(param.getSupplierCode());
        PurchaseOrderPO existPo = purchaseOrderAtomicService.queryByPoId(purchaseOrderPO);
        return PurchaseInvoiceConvert.INSTANCE.purchaseOrder2Invoice(existPo);
    }

    @Override
    public DataResponse<PageInfo<PurchaseInvoiceUpdatePoApplyDTO>> queryPurchaseInvoiceUpdatePoApplyPage(PurchaseInvoiceUpdatePoApplyPageReqDTO req) {
        DataResponse<PageInfo<PurchaseInvoiceUpdatePoApplyDTO>> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.invoice.PurchaseInvoiceReadApiServiceImpl.queryPurchaseInvoiceUpdatePoApplyPage");
        try {
            PurchaseInvoiceOrderUpdatePageReqVO reqVO = PurchaseInvoiceConvert.INSTANCE.purchaseInvoiceOrderUpdatePageReqDto2ReqVo(req);
            dataResponse = purchaseInvoiceUpdatePoApplyService.queryPurchaseOrderInvoiceTaxUpdateApplyPage(reqVO);
        } catch (Exception e) {
            log.error("PurchaseInvoiceReadApiServiceImpl.queryPurchaseInvoiceUpdatePoApplyPage, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.error("PurchaseInvoiceReadApiServiceImpl.queryPurchaseInvoiceUpdatePoApplyPage, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }

    @Override
    public DataResponse<PurchaseInvoiceUpdatePoApplyDTO> queryPurchaseInvoiceUpdatePoApply(PurchaseInvoiceUpdatePoApplyReqDTO req) {
        DataResponse<PurchaseInvoiceUpdatePoApplyDTO> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.invoice.PurchaseInvoiceReadApiServiceImpl.queryPurchaseInvoiceUpdatePoApply");
        try {
            PurchaseOrderInvoiceTaxUpdateApplyDetailReqVO reqVO = PurchaseInvoiceConvert.INSTANCE.purchaseInvoiceOrderUpdateDetailReqDto2ReqVo(req);
            dataResponse = purchaseInvoiceUpdatePoApplyService.queryPurchaseOrderInvoiceTaxUpdateApplyDetail(reqVO);
        } catch (Exception e) {
            log.error("PurchaseInvoiceReadApiServiceImpl.queryPurchaseInvoiceUpdatePoApply, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.error("PurchaseInvoiceReadApiServiceImpl.queryPurchaseInvoiceUpdatePoApply, req = {}, result = {}", JSON.toJSONString(req), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }




    @Override
    public DataResponse<PurchaseOrderInvoiceApiDTO> invoiceDetailPro(PurchaseInvoiceMainReqDTO mainDTO) {

        PurchaseOrderInvoiceApiDTO purchaseOrderInvoiceApiDTO=new PurchaseOrderInvoiceApiDTO();

        PurchaseInvoiceVO param = new PurchaseInvoiceVO();
        param.setPurchaseOrderId(mainDTO.getPurchaseOrderId());
        List<PurchaseInvoicePO> purchaseInvoicePOList = purchaseInvoiceAtomicService.queryByCondition(param);
        if (CollectionUtils.isEmpty(purchaseInvoicePOList)) {
            return DataResponse.error("发票信息不存在");
        }

        log.info("PurchaseInvoiceReadApiServiceImpl#invoiceDetailPro:{}",purchaseInvoicePOList);
        //发票id集合
        Set<String> invoiceIds = purchaseInvoicePOList.stream().map(PurchaseInvoicePO::getInvoiceId).collect(Collectors.toSet());
        //发票id集合查询发票信息
        List<PurchaseInvoicePO> purchaseInvoiceByInvoiceIds = purchaseInvoiceAtomicService.getPurchaseInvoiceByInvoiceIds(invoiceIds);
        //采购单id集合
        Set<String> purchaseOrderIds = purchaseInvoiceByInvoiceIds.stream().map(PurchaseInvoicePO::getPurchaseOrderId).collect(Collectors.toSet());
        log.info("PurchaseInvoiceReadApiServiceImpl#invoiceDetailPro,purchaseOrderIds:{}",purchaseOrderIds);

        //采购单信息集合
        List<PurchaseOrderPO> purchaseOrderPOList = purchaseOrderAtomicService.queryByPurchaseOrderIds(purchaseOrderIds);

        if (purchaseOrderPOList.isEmpty()) {
            return DataResponse.success();
        }

        if(StringUtils.isNotBlank(mainDTO.getSupplierCode())){
            log.info("验证供应商是否合法supplierCode{}",mainDTO.getSupplierCode());
            Set<String> supplierCodeSet = purchaseOrderPOList.stream().map(PurchaseOrderPO::getSupplierCode).collect(Collectors.toSet());
            if(supplierCodeSet.size()>1 || !supplierCodeSet.contains(mainDTO.getSupplierCode())){
                return DataResponse.success();
            }
        }



        List<PurchaseOrderInvoiceResDTO> purchaseOrderInvoiceResDTO = JsonUtil.getJavaObject(purchaseOrderPOList, PurchaseOrderInvoiceResDTO.class);
        purchaseOrderInvoiceResDTO= new ArrayList<>(purchaseOrderInvoiceResDTO.stream()
                .peek(m->{
                        if (Objects.isNull(m.getInvoiceApprovalStatus())) {
                            m.setInvoiceApprovalStatus(Constant.DEFAULT_INVOICE_STATUS);
                        }
                })
                .collect(Collectors.toMap(PurchaseOrderInvoiceResDTO::getPurchaseOrderId, dto -> dto, (existing, replacement) -> existing))
                .values());
        log.info("PurchaseInvoiceReadApiServiceImpl#invoiceDetailPro,purchaseOrderInvoiceResDTO:{}",purchaseOrderInvoiceResDTO);


        List<PurchaseInvoiceDetailDTO> detailList = PurchaseInvoiceConvert.INSTANCE.listInvoicePo2DetailDto(purchaseInvoiceByInvoiceIds);
        detailList= new ArrayList<>(detailList.stream()
                .collect(Collectors.toMap(PurchaseInvoiceDetailDTO::getInvoiceId, dto -> dto, (existing, replacement) -> existing))
                .values());


        PurchaseInvoiceWareVO invoiceWareVO = new PurchaseInvoiceWareVO();
        invoiceWareVO.setPurchaseOrderId(mainDTO.getPurchaseOrderId());
        List<PurchaseInvoiceWarePO> wareList = purchaseInvoiceWareAtomicService.getPurchaseInvoiceWare(purchaseOrderIds);
        List<PurchaseInvoiceWareDTO> wareVoList = PurchaseInvoiceConvert.INSTANCE.listPo2Dto(wareList);


        List<PurchaseOrderWarePO> purchaseWareList = purchaseOrderWareAtomicService.queryPurchaseOrderWarePOListById(purchaseOrderIds);

        //查询采购单商品信息
        Map<String, PurchaseOrderWarePO> purchaseWareMap = purchaseWareList.stream()
                .collect(Collectors.toMap(m->m.getPurchaseOrderId()+"#"+m.getSkuId(), Function.identity()));
        log.info("PurchaseInvoiceReadApiServiceImpl#invoiceDetailPro,purchaseWareMap:{}",purchaseWareMap);


        Map<String, List<PurchaseInvoiceWareDTO>> wareMap = getPurchaseInvoiceWareMap2(wareVoList,purchaseWareMap);
        log.info("PurchaseInvoiceReadApiServiceImpl#invoiceDetailPro,wareMap:{}",wareMap);

        setPurchaseInvoiceData2(detailList,wareMap);

        //处理客户输入未税价的情况
        detailList.forEach(r -> {
            if (Objects.nonNull(r.getCustomerPurchaseTotalPrice()) && BigDecimal.ZERO.compareTo(r.getCustomerPurchaseTotalPrice()) < 0) {
                r.setWaresInvoiceTotalPrice(r.getCustomerPurchaseTotalPrice());
            }
        });


        log.info("PurchaseInvoiceReadApiServiceImpl#invoiceDetailPro,detailList:{}",detailList);

        //设置操作按钮
        purchaseOrderInvoiceApiDTO.setOperateList(setOperateList(purchaseOrderInvoiceResDTO));
        purchaseOrderInvoiceApiDTO.setInvoiceDetailDTOS(detailList);
        purchaseOrderInvoiceApiDTO.setPurchaseOrderInvoiceResDTO(purchaseOrderInvoiceResDTO);

        return DataResponse.success(purchaseOrderInvoiceApiDTO);

    }


    /**
     * 根据采购订单发票审批状态设置操作列表
     * @param purchaseOrderInvoiceResDTO 采购订单发票响应DTO列表，包含发票审批状态等信息
     * @return 操作列表，包含可执行的操作类型及名称
     */
    private List<OperateApiDTO> setOperateList(List<PurchaseOrderInvoiceResDTO> purchaseOrderInvoiceResDTO ) {
        Integer INVOICE_REJECT = -1;
        List<OperateApiDTO> result = new ArrayList<>();
        PurchaseOrderInvoiceResDTO settlementRes = purchaseOrderInvoiceResDTO.get(0);

        //已驳回
        if (INVOICE_REJECT.equals(settlementRes.getInvoiceApprovalStatus())) {
            OperateApiDTO operateVO = new OperateApiDTO();
            if(purchaseOrderInvoiceResDTO.size() > 1){
                operateVO.setOperate(SettlementButtonEnum.EDIT_INVOICE.getValue());
                operateVO.setOperateName(SettlementButtonEnum.EDIT_INVOICE.getDesc());
            }else {
                operateVO.setOperate(SettlementButtonEnum.UPLOAD_INVOICE.getValue());
                operateVO.setOperateName(SettlementButtonEnum.UPLOAD_INVOICE.getDesc());
            }

            result.add(operateVO);
        }

        //采购单结算数据存在，则支持查看详情
//        if (Objects.nonNull(settlementRes.getSupplierConfirmStatus()) && hasSettle) {
//            OperateApiDTO operateVO = new OperateApiDTO();
//            operateVO.setOperate(SettlementButtonEnum.VIEW_SETTLEMENT.getValue());
//            operateVO.setOperateName(SettlementButtonEnum.VIEW_SETTLEMENT.getDesc());
//            result.add(operateVO);
//            OperateApiDTO operateVO1 = new OperateApiDTO();
//            operateVO1.setOperate(SettlementButtonEnum.DOWNLOAD_SETTLEMENT.getValue());
//            operateVO1.setOperateName(SettlementButtonEnum.DOWNLOAD_SETTLEMENT.getDesc());
//            result.add(operateVO1);
//        }

        return result;
    }




}
