package com.jdi.isc.order.center.service.protocol.jsf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hierynomus.utils.Strings;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.jd.jsf.sp.util.Assert;
import com.jd.jsf.sp.util.CollectionUtils;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.isc.aggregate.read.api.customer.res.CustomerReadResp;
import com.jdi.isc.order.center.api.common.OrderPrintPDFParam;
import com.jdi.isc.order.center.api.common.PayTypeDTO;
import com.jdi.isc.order.center.api.constants.PayTypeConstant;
import com.jdi.isc.order.center.common.biz.StaticResourceContainer;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.common.constants.UmpKeyConstant;
import com.jdi.isc.order.center.common.enums.FileTypeEnum;
import com.jdi.isc.order.center.common.utils.DateUtil;
import com.jdi.isc.order.center.common.utils.MoneyUtils;
import com.jdi.isc.order.center.common.utils.S3Utils;
import com.jdi.isc.order.center.common.utils.StringLangDetectorUtil;
import com.jdi.isc.order.center.domain.mku.biz.OrderWareDTO;
import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.order.biz.OrderPrintPDFVO;
import com.jdi.isc.order.center.domain.order.biz.OrderWarePrintVO;
import com.jdi.isc.order.center.domain.order.po.OrderConsigneePO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.domain.order.po.OrderSkuDetailPO;
import com.jdi.isc.order.center.rpc.customer.CustomerRpcService;
import com.jdi.isc.order.center.rpc.material.MkuMaterialRpcService;
import com.jdi.isc.order.center.rpc.sku.SkuReadRpcService;
import com.jdi.isc.order.center.rpc.tde.TdeClientRpcService;
import com.jdi.isc.order.center.service.adapter.mapstruct.order.OrderConvert;
import com.jdi.isc.order.center.service.atomic.mku.OrderWareAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderAtomicService;
import com.jdi.isc.order.center.service.atomic.order.OrderConsigneeAtomicService;
import com.jdi.isc.order.center.service.common.pdf.BaseExportSupportService;
import com.jdi.isc.order.center.service.common.pdf.PdfHeaderEvent;
import com.jdi.isc.product.soa.api.material.res.MkuMaterialApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

import static java.nio.file.StandardOpenOption.DELETE_ON_CLOSE;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/2 3:03 下午
 */
@Slf4j
@Service
public class OrderPrintPrintApiService extends BaseExportSupportService {

    @Autowired
    private S3Utils s3Utils;

    @Resource
    private StaticResourceContainer resourceContainer;

    @Resource
    private OrderAtomicService orderAtomicService;
    @Resource
    private CustomerRpcService customerRpcService;
    @Resource
    private OrderConsigneeAtomicService orderConsigneeAtomicService;
    @Resource
    private OrderWareAtomicService orderWareAtomicService;
    @Resource
    private TdeClientRpcService tdeClientRpcService;
    @Resource
    private SkuReadRpcService skuReadRpcService;

    @Resource
    private MkuMaterialRpcService mkuMaterialRpcService;

    private final static String TITLE = "SALES ORDER";

    public String printOrder(OrderPrintPDFParam printPDFParam){
        try {
            //获取数据
            OrderPrintPDFVO param = buildParam(printPDFParam);
            //生成pdf
           return createPdf(param,printPDFParam);
        } catch (Exception e) {
            log.error("OrderPrintPrintApiService#printOrder param:{},e:",JSON.toJSONString(printPDFParam),e);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_ORDER_PRINT_ERROR, ("订单PDF打印失败:orderId=" +printPDFParam.getOrderId()));
        }
        return null;
    }

    private OrderPrintPDFVO buildParam(OrderPrintPDFParam printPDFParam) {
        OrderPO orderPO = orderAtomicService.getOrderPOById(printPDFParam.getOrderId());
        Map<String, CustomerReadResp> customerReadRespMap = customerRpcService.queryCustomerByClientCodes(Collections.singleton(orderPO.getClientCode()));
        CustomerReadResp customerReadResp = customerReadRespMap.get(orderPO.getClientCode());
        Assert.notNull(customerReadResp, "客户信息不存在");

        OrderConsigneePO orderConsigneePO = orderConsigneeAtomicService.getOrderConsigneePOByOrderId(printPDFParam.getOrderId());
        List<OrderWarePO> orderWarePOListByOrderId = orderWareAtomicService.getOrderWarePOListByOrderId(printPDFParam.getOrderId());

        OrderPrintPDFVO orderPrintPDFVO = new OrderPrintPDFVO();
        orderPrintPDFVO.setOrderId(printPDFParam.getOrderId().toString());
        orderPrintPDFVO.setOrderCreator(orderPO.getPin());
        orderPrintPDFVO.setOrderCreateDate(DateUtil.formatDate(orderPO.getOrderCreateTime(),DateUtil.DEFAULT_DAY_PATTERN_VN));
        orderPrintPDFVO.setTotalAmountAfterTax(MoneyUtils.formatMoneyNoScale(orderPO.getOrderTotalPrice()));
        orderPrintPDFVO.setVatAmount(MoneyUtils.formatMoneyNoScale(orderPO.getOrderTaxes()));
        orderPrintPDFVO.setTotalAmountBeforeTax(MoneyUtils.formatMoneyNoScale(orderPO.getWaresSaleTotalPrice()));
        orderPrintPDFVO.setShippingFee(MoneyUtils.formatMoneyNoScale(orderPO.getOrderFreightPrice()));
        orderPrintPDFVO.setJdSubject(customerReadResp.getJdContractName());
        orderPrintPDFVO.setJdSubjectAddress(customerReadResp.getJdContractAddress());
        buildTradeTypeAndPayment(orderPrintPDFVO,orderPO);
        orderPrintPDFVO.setCurrency(orderPO.getCurrency());
        orderPrintPDFVO.setContractNo(customerReadResp.getEbsContractCode());
        orderPrintPDFVO.setClientAddress(customerReadResp.getCustomerAddress());
        orderPrintPDFVO.setClientName(customerReadResp.getCustomerNameVn());
        orderPrintPDFVO.setRecipientName(tdeClientRpcService.decrypt(orderConsigneePO.getConsigneeEncrypt()));
        orderPrintPDFVO.setRecipientTel(tdeClientRpcService.decrypt(orderConsigneePO.getConsigneeMobileEncrypt()));
        orderPrintPDFVO.setDeliveryAddress(tdeClientRpcService.decrypt(orderConsigneePO.getConsigneeAddressEncrypt()));
        List<OrderWarePrintVO> orderWarePrintVOList = buildOrderWareList(orderWarePOListByOrderId, printPDFParam.getLocaleList().get(0), orderPO);
        //商品
        orderPrintPDFVO.setOrderWareList(orderWarePrintVOList);
        return orderPrintPDFVO;
    }



    private void buildTradeTypeAndPayment(OrderPrintPDFVO orderPrintPDFVO, OrderPO orderPO) {
        String orderExtInfo = orderPO.getOrderExtInfo();
        if (Strings.isNotBlank(orderExtInfo)) {
            JSONObject extInfo = JSON.parseObject(orderExtInfo);
            if (extInfo.containsKey("payType")) {
                Integer payType = extInfo.getInteger("payType");
                PayTypeDTO payTypeDTO = PayTypeConstant.codeMap.get(payType);
                if (null != payTypeDTO) {
                    orderPrintPDFVO.setPaymentMethod(payTypeDTO.getNameEn());
                }
            }
        }
        orderPrintPDFVO.setShippingTerm(orderPO.getTradeType());
    }


    public String createPdf(OrderPrintPDFVO param, OrderPrintPDFParam printPDFParam) throws IOException {
        Document document = null;
        Path tempDir = Files.createTempDirectory(null);
        String tmpPath = Paths.get(tempDir + File.separator + UUID.randomUUID().toString()+".pdf").toString();
        String outPath = Paths.get(tempDir + File.separator +printPDFParam.getPin()+"_"+param.getOrderId()+"_"+System.currentTimeMillis()+".pdf").toString();
        try {
            document = new Document(PageSize.A4, 0, 0, 80, 60);
            loadHeaderAndFooter(document, tmpPath);
            document.open();
            //标题
            getParagraph(document, 10, null, null, null, Element.ALIGN_CENTER, new Chunk(TITLE, resourceContainer.getTitleFont()));
            //订单信息
            document.add(getOrderTable(param));
            //商品信息
            document.add(getSkuTable(printPDFParam, param));
            //添加总金额
            document.add(getOrderTotal(param));
            //底部
            getBottomText(document,param);
            document.newPage();
            document.close();
            addWaterMarkToPdf(Files.newInputStream(Paths.get(tmpPath),DELETE_ON_CLOSE), Files.newOutputStream(Paths.get(outPath)),printPDFParam.getPin());
            log.info("OrderPrintPrintApiService.createPdf path:{} ", outPath);
            return s3Utils.upload(outPath, FileTypeEnum.WISP_EXPORT.getCode());
        } catch (Exception e) {
            log.error("订单打印失败,入参:{},error:", JSON.toJSONString(param), e);
            throw new RuntimeException(e);
        } finally {
            FileUtils.deleteDirectory(tempDir.toFile());
            if (document != null && document.isOpen()) {
                document.close();
            }
        }
    }

    private Element getOrderTotal(OrderPrintPDFVO param) {
        PdfPTable orderTable = new PdfPTable(new float[] { 0.37f, 0.32f, 0.15f, 0.15f });
        orderTable.setWidthPercentage(92);
        orderTable.addCell(cellBorderZero("",resourceContainer.getBigFont(),2));
        orderTable.addCell(cellBorderZero("Total amount before tax:",resourceContainer.getBigFont()));
        orderTable.addCell(rightCellBorderZero(param.getTotalAmountBeforeTax(),resourceContainer.getBigFont()));
        orderTable.addCell(cellBorderZero("",resourceContainer.getBigFont(),2));
        orderTable.addCell(cellBorderZero("VAT amount:",resourceContainer.getBigFont()));
        orderTable.addCell(rightCellBorderZero(param.getVatAmount(),resourceContainer.getBigFont()));
        orderTable.addCell(cellBorderZero("",resourceContainer.getBigFont(),2));
        orderTable.addCell(cellBorderZero("Shipping fee:",resourceContainer.getBigFont()));
        orderTable.addCell(rightCellBorderZero(param.getShippingFee(),resourceContainer.getBigFont()));
        orderTable.addCell(cellBorderZero("",resourceContainer.getBigFont(),2));
        orderTable.addCell(cellBorderZero("Total amount after tax:",resourceContainer.getBigFont()));
        orderTable.addCell(rightCellBorderZero(param.getTotalAmountAfterTax(),resourceContainer.getBigFont()));
        orderTable.setSpacingBefore(8);
        return orderTable;
    }

    private void getBottomText(Document document, OrderPrintPDFVO param) throws DocumentException {
        String stringBuilder = "THIS ORDER IS PLACED BY [" + param.getOrderCreator() +
                "] ON [" + param.getOrderCreateDate() + "] ON THE WEBSITE HTTPS://BUY.JDINDUSTRY.COM/ AND APPROVED BY [JINGDONG INDUSTRIALS SUPPLY CHAIN (VIETNAM)] ON [" +
                param.getOrderCreateDate() +
                "] THIS IS A COMPUTER-GENERATED DOCUMENT AND IT DOES NOT REQUIRE SIGNATURE";
        getParagraph(document, 50, null, 20, 20, Element.ALIGN_LEFT, new Chunk(stringBuilder, resourceContainer.getBigFont()));

    }


    //载入页眉
    protected void loadHeaderAndFooter(Document document, String path) throws IOException, DocumentException, URISyntaxException, ClassNotFoundException {
        PdfWriter writer = PdfWriter.getInstance(document, Files.newOutputStream(Paths.get(path)));
        writer.setPageEmpty(false);
        PdfHeaderEvent headerTable = new PdfHeaderEvent(new PdfPTable(1),resourceContainer);
        headerTable.setTableHeader(writer,document);
        writer.close();
    }


    private PdfPTable getOrderTable(OrderPrintPDFVO order) {
        PdfPTable orderTable = new PdfPTable(new float[] { 0.14f, 0.36f, 0.11f, 0.39f });
        orderTable.setWidthPercentage(92);
        orderTable.addCell(cellBorderZero("",resourceContainer.getBigFont(),2));
        orderTable.addCell(cellBorderZero("Sales Order No.:",resourceContainer.getTextFont(),1));
        orderTable.addCell(cellBorderZero(order.getOrderId(),resourceContainer.getTextFont(),1));
        orderTable.addCell(cellBorderZero("",resourceContainer.getBigFont(),2));
        orderTable.addCell(cellBorderZero("Sales Order Date:",resourceContainer.getTextFont(),1));
        orderTable.addCell(cellBorderZero(order.getOrderCreateDate(),resourceContainer.getTextFont(),1));
        orderTable.addCell(cellBorderZero(" ",resourceContainer.getTextFont(),4));

        //第一行
        orderTable.addCell(cellBorderZero("SELLER:",resourceContainer.getBigFont(),2));
        orderTable.addCell(cellBorderZero("BUYER:",resourceContainer.getBigFont(),2));
        //第二行
        orderTable.addCell(cellBorderZero(order.getJdSubject(),resourceContainer.getBigFont(),2));
        orderTable.addCell(cellBorderZero(order.getClientName(),resourceContainer.getBigFont(),2));
        //第三行
        orderTable.addCell(cellBorderZero(order.getJdSubjectAddress(),resourceContainer.getTextFont(),2));
        orderTable.addCell(cellBorderZero(order.getClientAddress(),resourceContainer.getTextFont(),2));
        //第四行
        orderTable.addCell(cellBorderZero("PAYMENT METHOD:",resourceContainer.getBigFont()));
        orderTable.addCell(cellBorderZero(order.getPaymentMethod(),resourceContainer.getTextFont()));
        orderTable.addCell(cellBorderZero("SHIP TO:",resourceContainer.getBigFont(),2));
        //第五行
        orderTable.addCell(cellBorderZero("SHIPPING TERM:",resourceContainer.getBigFont()));
        orderTable.addCell(cellBorderZero(order.getShippingTerm(),resourceContainer.getTextFont()));
        orderTable.addCell(cellBorderZero("Recipient Name:",resourceContainer.getBigFont()));
        orderTable.addCell(cellBorderZero(order.getRecipientName(),resourceContainer.getTextFont()));
        //第六行
        orderTable.addCell(cellBorderZero("CURRENCY:",resourceContainer.getBigFont()));
        orderTable.addCell(cellBorderZero(order.getCurrency(),resourceContainer.getTextFont()));
        orderTable.addCell(cellBorderZero("Recipient Tel:",resourceContainer.getBigFont()));
        orderTable.addCell(cellBorderZero(order.getRecipientTel(),resourceContainer.getTextFont()));
        //第七行
        orderTable.addCell(cellBorderZero("CONTRACT NO.:",resourceContainer.getBigFont()));
        orderTable.addCell(cellBorderZero(order.getContractNo(),resourceContainer.getTextFont()));
        orderTable.addCell(cellBorderZero("Delivery Address:",resourceContainer.getBigFont()));
        orderTable.addCell(cellBorderZero(order.getDeliveryAddress(),resourceContainer.getTextFont()));
        orderTable.setSpacingBefore(20);
        return orderTable;
    }

    private PdfPTable getSkuTable(OrderPrintPDFParam printPDFParam, OrderPrintPDFVO order) {
        // TODO 将物料编号打印到PDF中
        PdfPTable orderTable = new PdfPTable(new float[] { 0.035f,0.14f,0.14f,0.14f,0.3175f,0.08f,0.035f,0.1f,0.08f,0.1f,0.1f,0.1f });
        orderTable.setWidthPercentage(92);
        orderTable.addCell(midCell("No.",resourceContainer.getBigFont()));
        orderTable.addCell(midCell("MKU/Part No.",resourceContainer.getBigFont()));
        orderTable.addCell(midCell("Material Code",resourceContainer.getBigFont()));
        orderTable.addCell(midCell("Material Name",resourceContainer.getBigFont()));
        orderTable.addCell(midCell("Description",resourceContainer.getBigFont()));
        orderTable.addCell(midCell("Quantity",resourceContainer.getBigFont()));
        orderTable.addCell(midCell("Unit",resourceContainer.getBigFont()));
        orderTable.addCell(midCell("Unit Price\nbefore tax",resourceContainer.getBigFont()));
        orderTable.addCell(midCell("Tax Rate",resourceContainer.getBigFont()));
        orderTable.addCell(midCell("Unit Price\nafter tax",resourceContainer.getBigFont()));
        orderTable.addCell(midCell("Amount\nbefore tax",resourceContainer.getBigFont()));
        orderTable.addCell(midCell("Amount\nafter tax",resourceContainer.getBigFont()));
        List<OrderWarePrintVO> mku = order.getOrderWareList();
        for (int i = 0; i < mku.size(); i++) {
            orderTable.addCell(midCell(String.valueOf(i+1)));
            orderTable.addCell(midCell(mku.get(i).getMkuId()));
            orderTable.addCell(midCell(mku.get(i).getMaterialId()));
            orderTable.addCell(midCell(mku.get(i).getMaterialName()));
            orderTable.addCell(cell( appendMkuLangValue(printPDFParam, mku.get(i)) ));
            orderTable.addCell(midCell(mku.get(i).getQuantity()));
            orderTable.addCell(midCell(mku.get(i).getUnit()));
            orderTable.addCell(midCell(mku.get(i).getUnitPriceBeforeTax()));
            orderTable.addCell(midCell(mku.get(i).getTaxRate()));
            orderTable.addCell(midCell(mku.get(i).getUnitPriceAfterTax()));
            orderTable.addCell(midCell(mku.get(i).getAmountBeforeTax()));
            orderTable.addCell(midCell(mku.get(i).getAmountAfterTax()));
        }
        orderTable.setSpacingBefore(8);
        return orderTable;
    }



    private List<OrderWarePrintVO> buildOrderWareList(List<OrderWarePO> orderWarePOList,String lang,OrderPO orderPO) {
        List<OrderWarePrintVO> result = new ArrayList<>();
        List<OrderWareDTO> orderWareDTOList = OrderConvert.INSTANCE.pOToDTOList(orderWarePOList);
        //从Material通过product -soa 查询数据 ->  改为查询订单快照信息 machao
        //setMaterial(orderWareDTOList,orderPO.getClientCode());
        for (OrderWareDTO orderWareDTO : orderWareDTOList) {
            OrderSkuDetailPO skuInfo = JSON.parseObject(orderWareDTO.getSkuJsonInfo(), OrderSkuDetailPO.class);
            OrderWarePrintVO warePrintVO = new OrderWarePrintVO();
            warePrintVO.setMkuId(String.valueOf(orderWareDTO.getMkuId()));
            warePrintVO.setMkuName(skuInfo.getSkuNameMap().get(lang));
            warePrintVO.setMkuNameMap(skuInfo.getSkuNameMap());
            warePrintVO.setQuantity(String.valueOf(orderWareDTO.getMkuNum()));
            if (Objects.nonNull(skuInfo.getSaleUnit())) {
                String saleUnit = skuReadRpcService.querySaleUnit(lang, skuInfo.getSaleUnit());
                warePrintVO.setUnit(saleUnit);
            }
            warePrintVO.setTaxRate(dealTaxRate(orderWareDTO.getValueAddedTaxesInfo()));
            warePrintVO.setUnitPriceBeforeTax(MoneyUtils.formatMoneyNoScale(orderWareDTO.getSalePrice()));
            warePrintVO.setUnitPriceAfterTax(MoneyUtils.formatMoneyNoScale(orderWareDTO.getIncludeTaxPrice()));
            warePrintVO.setAmountBeforeTax(MoneyUtils.formatMoneyNoScale(orderWareDTO.getMkuAllSalePrice()));
            warePrintVO.setAmountAfterTax(MoneyUtils.formatMoneyNoScale(orderWareDTO.getMkuAllPrice()));
            //  从 通过product -soa 查询数据 ->  改为查询订单快照信息 machao
            warePrintVO.setMaterialId(null != orderWareDTO.getMkuJsonInfo() ? JSONObject.parseObject(orderWareDTO.getMkuJsonInfo()).getString("materialCode") : null);
            warePrintVO.setMaterialName(null != orderWareDTO.getMkuJsonInfo() ? JSONObject.parseObject(orderWareDTO.getMkuJsonInfo()).getString("materialName") : null);
            result.add(warePrintVO);
        }
        return result;
    }

    private void setMaterial(List<OrderWareDTO> orderWareDTOList,String ClientCode){
        log.info("OrderPrintPrintApiService.setMaterial req:{}" , JSON.toJSONString(orderWareDTOList));
        try {
            if(CollectionUtils.isEmpty(orderWareDTOList)){
                return;
            }
            List<Long> mkuIdList = orderWareDTOList.stream().map(OrderWareDTO::getMkuId).collect(Collectors.toList());
            Map<Long, MkuMaterialApiDTO> mkuMaterialMap = mkuMaterialRpcService.queryMkuMaterialMap(mkuIdList,ClientCode);
            for (OrderWareDTO orderWareDTO : orderWareDTOList){
                if(null != orderWareDTO.getMkuId() && mkuMaterialMap.containsKey(orderWareDTO.getMkuId())){
                    MkuMaterialApiDTO mkuMaterialApiDTO = mkuMaterialMap.get(orderWareDTO.getMkuId());
                    orderWareDTO.setMaterialName(mkuMaterialApiDTO.getMaterialName());
                    orderWareDTO.setMaterialId(mkuMaterialApiDTO.getMaterialId());
                }
            }
            log.info("OrderPrintPrintApiService.setMaterial req:{},res:{}" , JSON.toJSONString(orderWareDTOList), JSON.toJSONString(mkuMaterialMap));
        }catch (Exception e){
            log.error("获取mku物料绑定关系失败,入参:{}, err:{} ,e:{}", JSON.toJSONString(orderWareDTOList),e.getMessage(), e);
        }
    }


    private String dealTaxRate(String valueAddedTaxesInfo) {
        if (Strings.isNotBlank(valueAddedTaxesInfo)) {
            JSONObject jsonObject = JSONObject.parseObject(valueAddedTaxesInfo);
            if (jsonObject.containsKey("taxRate")) {
                BigDecimal taxRate = jsonObject.getBigDecimal("taxRate");
                if (null != taxRate) {
                    String rateStr = BigDecimal.valueOf(100f).multiply(taxRate).stripTrailingZeros().toPlainString();
                    return rateStr+"%";
                }
            }
        }
        return null;
    }

    /**
     * 商品多语言
     * @param printPDFParam
     * @param warePrintVO
     * @return
     */
    public String appendMkuLangValue(OrderPrintPDFParam printPDFParam, OrderWarePrintVO warePrintVO){
        Map<String/*语言*/, String> mkuNameMap = warePrintVO.getMkuNameMap();
        if (MapUtils.isEmpty(mkuNameMap)){
            return warePrintVO.getMkuName();
        }

        String reqLang = printPDFParam.getLocaleList().get(0);
        List<String> langList = new ArrayList<>();
        langList.add(reqLang);
        if (!langList.contains(StringLangDetectorUtil.LANG_ZH)){
            langList.add(StringLangDetectorUtil.LANG_ZH);
        }

        StringBuilder result = new StringBuilder();
        for (int i=0;i<langList.size();i++){
            result.append(mkuNameMap.get(langList.get(i)));

            if (i!=langList.size()-1){
                result.append("\n");
            }

        }
        String res = result.toString();
        if (StringUtils.isNotBlank(res)){
            return res;
        }
        return warePrintVO.getMkuName();
    }
}
