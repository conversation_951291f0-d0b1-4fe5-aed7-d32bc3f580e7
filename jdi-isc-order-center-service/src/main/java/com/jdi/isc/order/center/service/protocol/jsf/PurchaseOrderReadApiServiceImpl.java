package com.jdi.isc.order.center.service.protocol.jsf;

import com.alibaba.fastjson.JSONObject;
import com.hierynomus.utils.Strings;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.constants.PurchaseOrderStatusConstant;
import com.jdi.isc.order.center.api.purchaseOrder.biz.req.PurchaseOrderStatusDTO;
import com.jdi.isc.order.center.api.purchaseOrderRead.PurchaseOrderReadApiService;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.WareLastPurchaseTimeApiDTO;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.req.PurchaseOrderCountReqDTO;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.req.PurchaseOrderPageDTO;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.req.PurchaseOrderReqDTO;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.res.*;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.domain.config.OrderDuccConfig;
import com.jdi.isc.order.center.domain.enums.purchase.PurchaseDimensionsEnum;
import com.jdi.isc.order.center.domain.enums.purchase.PurchaseModelEnum;
import com.jdi.isc.order.center.domain.order.biz.SettlementPurchaseDuccVO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.WareLastPurchaseTimeVO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.rpc.supplier.SupplierBaseInfoReadRpcService;
import com.jdi.isc.order.center.service.adapter.mapstruct.purchaseOrder.PurchaseOrderConvert;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderAtomicService;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderWareAtomicService;
import com.jdi.isc.order.center.service.manage.purchaseOrder.PurchaseOrderInvoiceReadService;
import com.jdi.isc.order.center.service.manage.purchaseOrder.PurchaseOrderReadManager;
import com.jdi.isc.order.center.service.manage.purchaseOrder.impl.StockUpPurchaseOrderReadService;
import com.jdi.isc.order.center.service.manage.purchaseOrder.impl.SupplierPurchaseOrderReadService;
import com.jdi.isc.product.soa.api.supplier.res.SupplierBaseInfoRes;
import com.jdi.isc.product.soa.api.supplier.res.SupplierContractRes;
import com.jdi.isc.product.soa.api.supplier.res.SupplierInfoRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PurchaseOrderReadApiServiceImpl implements PurchaseOrderReadApiService {

    @Autowired
    private PurchaseOrderAtomicService purchaseOrderAtomicService;

    @Autowired
    private PurchaseOrderWareAtomicService purchaseOrderWareAtomicService;

    @Autowired
    private SupplierBaseInfoReadRpcService supplierBaseInfoReadRpcService;

    @Autowired
    private OrderDuccConfig orderDuccConfig;

    @Autowired
    private Map<String,PurchaseOrderReadManager>  purchaseOrderReadMap;

    @Autowired
    private SupplierPurchaseOrderReadService supplierPurchaseOrderReadService;

    @Autowired
    private StockUpPurchaseOrderReadService stockUpPurchaseOrderReadService;


    @Autowired
    PurchaseOrderInvoiceReadService purchaseOrderInvoiceReadService;

    private static final Map<Integer, String> PURCHASE_SERVICE_MAP = new HashMap<>();

    private static final Integer ALL_PURCHASE_MODEL = -1;
    static {
        PURCHASE_SERVICE_MAP.put(PurchaseModelEnum.DIRECT_SUPPLY.getCode(), "directSupplyPurchaseOrderReadService");
        PURCHASE_SERVICE_MAP.put(PurchaseModelEnum.STOCK_UP.getCode(), "stockUpPurchaseOrderReadService");
        PURCHASE_SERVICE_MAP.put(PurchaseModelEnum.CONSIGNMENT.getCode(), "consignmentPurchaseOrderReadServiceService");
        PURCHASE_SERVICE_MAP.put(ALL_PURCHASE_MODEL, "supplierPurchaseOrderReadService");
    }

    /**
     * 查询采购订单的财务信息。
     */
    @Override
    @ToolKit
    public DataResponse<Map<String, PurchaseOrderFinanceRes>> queryPurchaseOrderFinanceInfo(PurchaseOrderReq orderReq) {
        if (orderReq == null || CollectionUtils.isEmpty(orderReq.getPurchaseOrderIds())) {
            return DataResponse.buildError("入参不能为空");
        }

        if (orderReq.getPurchaseOrderIds().size() > Constant.BATCH_NUM_20) {
            return DataResponse.buildError("查询参数超过最大限制");
        }

        List<PurchaseOrderPO> purchaseOrders = purchaseOrderAtomicService.listBySplitOrderIds(orderReq.getPurchaseOrderIds());
        if (CollectionUtils.isEmpty(purchaseOrders)) {
            return DataResponse.buildSuccess(new HashMap<>());
        }

        Set<String> supplierCodes = purchaseOrders.stream()
                .map(PurchaseOrderPO::getSupplierCode)
                .collect(Collectors.toSet());

        SupplierInfoRes supplierInfoRes = supplierBaseInfoReadRpcService.batchSupplierInfo(supplierCodes);
        if (supplierInfoRes == null) {
            return DataResponse.success();
        }

        Map<String, SupplierBaseInfoRes> baseInfoResMap = supplierInfoRes.getBaseInfoResMap();
        Map<String, SupplierContractRes> contractResMap = supplierInfoRes.getContractResMap();

        Set<String> validCodes = getValidSupplierCodes(baseInfoResMap, orderReq);

        Map<String, SettlementPurchaseDuccVO> settlementMap = getSettlementInfo(purchaseOrders);

        Map<String, PurchaseOrderFinanceRes> result = purchaseOrders.stream()
                .filter(order -> isValidOrder(order, orderReq, validCodes))
                .map(order -> buildFinanceResponse(order, baseInfoResMap, contractResMap, settlementMap))
                .collect(Collectors.toMap(PurchaseOrderFinanceRes::getPurchaseOrderId, res -> res));

        return DataResponse.success(result);
    }

    @ToolKit(exceptionWrap = true)
    @Override
    public DataResponse<Map<Long,WareLastPurchaseTimeApiDTO.Response>> queryWareLastPurchaseTime(WareLastPurchaseTimeApiDTO.Request request) {
        WareLastPurchaseTimeVO.Request reqVo = PurchaseOrderConvert.INSTANCE.wareLastPurchaseTimeApiDTO2VO(request);

        List<WareLastPurchaseTimeVO.Response> dbRes = purchaseOrderWareAtomicService.queryWareLastPurchaseTime(reqVo);

        if(CollectionUtils.isEmpty(dbRes)){
            return DataResponse.success(Collections.EMPTY_MAP);
        }

        List<WareLastPurchaseTimeApiDTO.Response> apiList = PurchaseOrderConvert.INSTANCE.wareLastPurchaseTimeVoList2DtoList(dbRes);

        // 将apiList转为map映射
        Map<Long,WareLastPurchaseTimeApiDTO.Response> apiMap = apiList.stream()
                .collect(Collectors.toMap(WareLastPurchaseTimeApiDTO.Response::getSkuId, Function.identity()));

        return DataResponse.success(apiMap);
    }


    @Override
    public DataResponse<PageInfo<PurchaseOrderReadApiDTO>> purchaseOrderPage(PurchaseOrderPageDTO page) {
        log.info("PurchaseOrderReadApiServiceImpl.purchaseOrderPage param:{}",JSONObject.toJSONString(page));
        PurchaseOrderReadManager purchaseOrderReadService = getPurchaseOrderReadService(page.getQueryDimensions(), page.getPurchaseModel());
        return purchaseOrderReadService.pageQuery(page);
    }


    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PurchaseOrderReadApiDTO> purchaseOrderDetailManage(PurchaseOrderReqDTO reqDTO) {
        PurchaseOrderPO purchaseOrderPO = purchaseOrderAtomicService.queryByPoId(reqDTO.getPurchaseOrderId());
        Integer purchaseModel = Objects.isNull(purchaseOrderPO.getPurchaseModel()) ? PurchaseModelEnum.DIRECT_SUPPLY.getCode():purchaseOrderPO.getPurchaseModel();
        return getServiceByPurchaseModel(purchaseModel).detail(reqDTO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PurchaseOrderReadApiDTO> supplierPurchaseOrderDetail(PurchaseOrderReqDTO reqDTO) {
        return getPurchaseOrderReadService(PurchaseDimensionsEnum.SUPPLIER.getCode(),null).detail(reqDTO);
    }


    @Override
    public DataResponse<PurchaseOrderCountResDTO> countStatus(PurchaseOrderCountReqDTO countDTO) {
        return getPurchaseOrderReadService(PurchaseDimensionsEnum.SUPPLIER.getCode(),null).countStatus(countDTO);
    }



    @Override
    public DataResponse<PurchaseOrderReadApi> purchaseOrderDetailInvoice(PurchaseOrderReq req) {
        return purchaseOrderInvoiceReadService.purchaseOrderDetailInvoice(req);
    }


    @Override
    public DataResponse<Boolean> checkPurchaseOrderDetailInvoice(PurchaseOrderReq req) {
        Set<String> purchaseOrderIds = req.getPurchaseOrderIds();
        List<PurchaseOrderPO> purchaseOrderPOS = purchaseOrderAtomicService.queryByPurchaseOrderIds(purchaseOrderIds);
        return purchaseOrderInvoiceReadService.checkPurchaseOrderDetailInvoice(req,purchaseOrderPOS);
    }



    private PurchaseOrderReadManager getPurchaseOrderReadService(String queryDimensions, Integer purchaseModel){
        if (PurchaseDimensionsEnum.SUPPLIER.getCode().equals(queryDimensions)) {
            return getServiceByPurchaseModel(ALL_PURCHASE_MODEL);
        }
        if (PurchaseDimensionsEnum.PURCHASE_MODEL.getCode().equals(queryDimensions)) {
           return getServiceByPurchaseModel(purchaseModel);
        }
        throw new RuntimeException("请求来源非法");
    }

    private PurchaseOrderReadManager getServiceByPurchaseModel(Integer purchaseModel){
        PurchaseOrderReadManager orderReadManager = purchaseOrderReadMap.get(PURCHASE_SERVICE_MAP.get(purchaseModel));
        if (Objects.isNull(orderReadManager)) {
            log.error("无此模式的实现类，purchaseModel:{}",purchaseModel);
            throw new RuntimeException("无此模式的实现类 purchaseModel:"+purchaseModel);
        }
        return orderReadManager;
    }

    /**
     * 验证采购订单是否有效。
     */
    private boolean isValidOrder(PurchaseOrderPO order, PurchaseOrderReq orderReq, Set<String> validCodes) {
        boolean isValid = true;
        if (Strings.isNotBlank(orderReq.getCurrency())) {
            isValid = order.getCurrency().equals(orderReq.getCurrency());
        }
        return isValid && validCodes.contains(order.getSupplierCode());
    }

    /**
     * 构建采购订单财务响应对象。
     */
    private PurchaseOrderFinanceRes buildFinanceResponse(PurchaseOrderPO order, Map<String, SupplierBaseInfoRes> baseInfoResMap,
                                                         Map<String, SupplierContractRes> contractResMap, Map<String, SettlementPurchaseDuccVO> settlementMap) {
        PurchaseOrderFinanceRes financeRes = new PurchaseOrderFinanceRes();
        financeRes.setPurchaseOrderId(order.getPurchaseOrderId());
        financeRes.setCurrency(order.getCurrency());
        financeRes.setPurchaseOrderStatus(order.getPurchaseOrderStatus());

        PurchaseOrderStatusDTO orderStatusDTO = PurchaseOrderStatusConstant.statusMap.get(order.getPurchaseOrderStatus());
        if (orderStatusDTO != null) {
            financeRes.setPurchaseOrderStatusStr(orderStatusDTO.getDesc());
        }

        financeRes.setPurchaseCreateTime(order.getPurchaseCreateTime());
        financeRes.setPurchaseTotalPrice(order.getPurchaseTotalPrice());
        financeRes.setSupplierCode(order.getSupplierCode());

        SupplierBaseInfoRes baseInfoRes = baseInfoResMap.get(order.getSupplierCode());
        if (baseInfoRes != null) {
            financeRes.setSupplierName(baseInfoRes.getBusinessLicenseName());
            financeRes.setSalesPerson(baseInfoRes.getCreator());
        }

        SupplierContractRes contractRes = contractResMap.get(order.getSupplierCode());
        if (contractRes != null) {
            financeRes.setEBSContractNum(contractRes.getContractNumber());
            financeRes.setEBSContractSubject(contractRes.getOrgName());
        }

        SettlementPurchaseDuccVO settleConfig = settlementMap.get(order.getCountryCode());
        if (settleConfig != null) {
            financeRes.setJdContractSubject(settleConfig.getOu());
            financeRes.setJdContractSubjectName(settleConfig.getOuName());
        }

        return financeRes;
    }

    /**
     * 根据采购订单列表获取结算信息。
     */
    private Map<String, SettlementPurchaseDuccVO> getSettlementInfo(List<PurchaseOrderPO> purchaseOrders) {
        return purchaseOrders.stream()
                .map(PurchaseOrderPO::getCountryCode)
                .distinct()
                .collect(Collectors.toMap(
                        countryCode -> countryCode,
                        countryCode -> orderDuccConfig.getSettlementConfig(countryCode)
                ));
    }

    /**
     * 根据给定的条件从供应商基本信息映射中获取有效的供应商代码集合。
     */
    private Set<String> getValidSupplierCodes(Map<String, SupplierBaseInfoRes> baseInfoResMap, PurchaseOrderReq orderReq) {
        if (Strings.isNotBlank(orderReq.getMerchantCode()) ||
                Strings.isNotBlank(orderReq.getErp()) ||
                Strings.isNotBlank(orderReq.getSupplierCode())) {
            return baseInfoResMap.values().stream()
                    .filter(supplier -> isValidSupplier(supplier, orderReq))
                    .map(SupplierBaseInfoRes::getSupplierCode)
                    .collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }

    /**
     * 验证供应商信息是否与采购订单信息匹配。
     */
    private boolean isValidSupplier(SupplierBaseInfoRes supplier, PurchaseOrderReq orderReq) {
        boolean isValid = true;
        if (isValid && Strings.isNotBlank(orderReq.getMerchantCode())) {
            isValid = orderReq.getMerchantCode().equals(supplier.getMerchantCode());
        }
        if (isValid && Strings.isNotBlank(orderReq.getSupplierCode())) {
            isValid = orderReq.getSupplierCode().equals(supplier.getSupplierCode());
        }
        if (isValid &&  Strings.isNotBlank(orderReq.getErp())) {
            isValid = orderDuccConfig.getSettlementConfig(Constant.STAR).getSettlementPoQueryAdminErpList().contains(orderReq.getErp()) || orderReq.getErp().equals(supplier.getCreator());
        }
        return isValid;
    }
}