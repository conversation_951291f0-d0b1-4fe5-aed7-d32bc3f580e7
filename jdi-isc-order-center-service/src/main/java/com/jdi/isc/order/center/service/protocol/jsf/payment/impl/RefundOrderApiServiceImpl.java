package com.jdi.isc.order.center.service.protocol.jsf.payment.impl;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.domain.payment.req.RefundOrderPageReqVO;
import com.jdi.isc.order.center.domain.payment.biz.RefundOrderVO;
import com.jdi.isc.order.center.api.payment.req.RefundOrderPageReqApiDTO;
import com.jdi.isc.order.center.api.payment.biz.RefundOrderApiDTO;
import com.jdi.isc.order.center.service.adapter.mapstruct.payment.RefundOrderConvert;
import com.jdi.isc.order.center.service.manage.payment.RefundOrderReadService;
import com.jdi.isc.order.center.api.payment.RefundOrderApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: 退款订单明细api服务实现
 * @Author: zhaojianguo21
 * @Date: 2025/08/07 20:15
 **/

@Slf4j
@Service
public class RefundOrderApiServiceImpl implements RefundOrderApiService {

    @Resource
    private RefundOrderReadService refundOrderReadService;

    @ToolKit
    @Override
    public DataResponse<PageInfo<RefundOrderApiDTO>> pageSearch(RefundOrderPageReqApiDTO input){
        log.info("pageSearch, input={}", JSONObject.toJSONString(input));
        PageInfo<RefundOrderVO> invokeResult = null;
        try {
            RefundOrderPageReqVO reqVO = RefundOrderConvert.INSTANCE.pageReqApiDto2PageReqVo(input);
            invokeResult = refundOrderReadService.pageSearch(reqVO);
            if (null==invokeResult){
                log.warn("pageSearch, invokeResult fail. input={}", JSONObject.toJSONString(input));
                return DataResponse.error("查询失败");
            }

            PageInfo<RefundOrderApiDTO> pageInfoRes = RefundOrderConvert.INSTANCE.pageVo2PageApiDto(invokeResult);
            return DataResponse.success(pageInfoRes);
        } catch (Exception e) {
            log.error("pageSearch exception. input={}", JSONObject.toJSONString(input), e);
            return DataResponse.error("发生异常。");
        }finally {
            log.info("pageSearch end. input={}, invokeResult={}", JSONObject.toJSONString(input), JSONObject.toJSONString(invokeResult));
        }
    }
}