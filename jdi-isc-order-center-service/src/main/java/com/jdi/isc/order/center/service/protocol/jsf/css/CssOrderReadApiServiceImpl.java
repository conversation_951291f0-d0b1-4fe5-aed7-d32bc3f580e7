package com.jdi.isc.order.center.service.protocol.jsf.css;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.isc.order.center.api.css.CssOrderReadApiService;
import com.jdi.isc.order.center.api.css.biz.req.CssOrderReadDetailReqApiDTO;
import com.jdi.isc.order.center.api.css.biz.req.CssOrderReadPageReqApiDTO;
import com.jdi.isc.order.center.api.css.biz.resp.CssOrderReadApiDTO;
import com.jdi.isc.order.center.service.manage.css.CssOrderReadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @description
 * @date 2025/5/23 14:17
 */
@Service
@Slf4j
public class CssOrderReadApiServiceImpl implements CssOrderReadApiService {

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private CssOrderReadService cssOrderReadService;

    @Override
    public DataResponse<PageInfo<CssOrderReadApiDTO>> queryCssOrderPage(CssOrderReadPageReqApiDTO cssOrderReadPageReqApiDTO) {
        DataResponse<PageInfo<CssOrderReadApiDTO>> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.css.CssOrderReadApiServiceImpl.queryCssOrderPage." + active);
        try {
            dataResponse = cssOrderReadService.queryCssOrderPage(cssOrderReadPageReqApiDTO);
        } catch (Exception e){
            log.error("CssOrderReadApiServiceImpl.queryCssOrderPage, req = {}, result = {}", JSON.toJSONString(cssOrderReadPageReqApiDTO), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
            dataResponse = DataResponse.success();
        } finally {
            log.info("CssOrderReadApiServiceImpl.queryCssOrderPage, req = {}, result = {}", JSON.toJSONString(cssOrderReadPageReqApiDTO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }

    @Override
    public DataResponse<CssOrderReadApiDTO> queryCssOrderDetail(CssOrderReadDetailReqApiDTO cssOrderReadDetailReqApiDTO) {
        DataResponse<CssOrderReadApiDTO> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.css.CssOrderReadApiServiceImpl.queryCssOrderDetail." + active);
        try {
            dataResponse = cssOrderReadService.queryCssOrderDetail(cssOrderReadDetailReqApiDTO);
        } catch (Exception e){
            dataResponse = DataResponse.error(DataResponseCode.SYSTEM_ERROR.getCode());
            log.error("CssOrderReadApiServiceImpl.queryCssOrderDetail, req = {}, result = {}", JSON.toJSONString(cssOrderReadDetailReqApiDTO), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("CssOrderReadApiServiceImpl.queryCssOrderDetail, req = {}, result = {}", JSON.toJSONString(cssOrderReadDetailReqApiDTO), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }
}
