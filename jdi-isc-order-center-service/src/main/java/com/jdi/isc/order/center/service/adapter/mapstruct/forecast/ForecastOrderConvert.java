package com.jdi.isc.order.center.service.adapter.mapstruct.forecast;

import com.jdi.isc.order.center.api.forecast.biz.req.ForecastOrderApiReq;
import com.jdi.isc.order.center.api.forecast.biz.req.ForecastOrderWareLastPurchaseTimeReqApiDTO;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderWareLastPurchaseTimeResApiDTO;
import com.jdi.isc.order.center.domain.forecast.biz.ForecastOrderApiVo;
import com.jdi.isc.order.center.domain.forecast.req.ForecastOrderWareLastPurchaseTimeReqVO;
import com.jdi.isc.order.center.domain.forecast.res.ForecastOrderWareLastPurchaseTimeResVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ForecastOrderConvert {

    ForecastOrderConvert INSTANCE = Mappers.getMapper(ForecastOrderConvert.class);

    ForecastOrderApiVo apiDto2Vo(ForecastOrderApiReq input);

    ForecastOrderWareLastPurchaseTimeReqVO wareLastPurchaseTimeApiDTO2VO(ForecastOrderWareLastPurchaseTimeReqApiDTO input);

    ForecastOrderWareLastPurchaseTimeResApiDTO wareLastPurchaseTimeVO2DTO(ForecastOrderWareLastPurchaseTimeResVO input);

    List<ForecastOrderWareLastPurchaseTimeResApiDTO> wareLastPurchaseTimeVoList2DtoList(List<ForecastOrderWareLastPurchaseTimeResVO> input);

}
