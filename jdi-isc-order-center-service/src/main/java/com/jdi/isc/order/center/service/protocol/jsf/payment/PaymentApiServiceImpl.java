package com.jdi.isc.order.center.service.protocol.jsf.payment;

import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.payment.PaymentApiService;
import com.jdi.isc.order.center.api.payment.req.OrderRepaymentInfoReqDTO;
import com.jdi.isc.order.center.api.payment.res.OrderRepaymentInfoRespDTO;
import com.jdi.isc.order.center.common.constants.CacheKeyConstant;
import com.jdi.isc.order.center.common.utils.JimUtils;
import com.jdi.isc.order.center.domain.enums.payment.LedgerStatusEnum;
import com.jdi.isc.order.center.domain.mapstruct.order.OrderConvert;
import com.jdi.isc.order.center.domain.order.po.OrderMsgPO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.service.atomic.order.OrderAtomicService;
import com.jdi.isc.order.center.service.manage.payment.OrderRepayService;
import com.jdi.isc.order.center.service.manage.payment.impl.OrderConfirmMessagePaymentServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PaymentApiServiceImpl implements PaymentApiService {

    @Autowired
    private OrderAtomicService orderAtomicService;

    @Autowired
    private JimUtils jimUtils;

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private OrderConfirmMessagePaymentServiceImpl orderConfirmMessagePaymentServiceImpl;

    @Autowired
    private OrderRepayService orderRepayService;


    @Override
    public DataResponse<String> retryPayment(Long orderId){
        DataResponse<String> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.service.protocol.jsf.payment.PaymentApiServiceImpl.retryPayment." + active);
        try {
            String key = CacheKeyConstant.getKey(CacheKeyConstant.ORDER_JC_PAY_RPC_RETRY_KEY, String.valueOf(orderId));
            jimUtils.del(key);
            OrderPO orderPO = orderAtomicService.getOrderPOById(orderId);

            if(!LedgerStatusEnum.UN_RECONCILED.getStatus().equals(orderPO.getLedgerStatus())){
                log.error("PaymentApiServiceImpl.retryPayment, 已支付, req = {}, result = {}", orderId, orderPO.getLedgerStatus());
                return DataResponse.success("金采支付重试-已支付");
            }

            OrderMsgPO orderMsgPO = OrderConvert.INSTANCE.po2msgPo(orderPO);
            try {
                orderConfirmMessagePaymentServiceImpl.consumerMessage(orderMsgPO);
            } catch (Exception e) {
                return DataResponse.error("金采支付重试-失败");
            }
            return DataResponse.success("金采支付重试-成功");
        } catch (Exception e){
            log.error("PaymentApiServiceImpl.retryPayment, req = {}, result = {}", JSON.toJSONString(orderId), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("PaymentApiServiceImpl.retryPayment, req = {}, result = {}", JSON.toJSONString(orderId), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;

    }
}
