package com.jdi.isc.order.center.service.adapter.mapstruct.aftersale;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.aftersales.req.AfterSalesSubPageReqApiDTO;
import com.jdi.isc.order.center.api.aftersales.res.AfterSalesSubPageResApiDTO;
import com.jdi.isc.order.center.domain.aftersales.po.AfterSalesSubPO;
import com.jdi.isc.order.center.domain.aftersales.req.AfterSalesSubPageReqVO;
import com.jdi.isc.order.center.domain.aftersales.res.AfterSalesSubPageResVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 售后子单对象转换
 * @Author: zhaojianguo21
 * @Date: 2025/6/17
 **/
@Mapper
public interface AfterSalesSubConvert {

    AfterSalesSubConvert INSTANCE = Mappers.getMapper(AfterSalesSubConvert.class);

    AfterSalesSubPageResVO afterSalesSubPO(AfterSalesSubPO input);

    List<AfterSalesSubPageResVO> listAfterSalesSubPO(List<AfterSalesSubPO> input);

    /** api jsf实现层使用开始 */
    AfterSalesSubPageReqVO pageReqApi2PageReqVo(AfterSalesSubPageReqApiDTO input);

    PageInfo<AfterSalesSubPageResApiDTO> pageResVo2PageResApi(PageInfo<AfterSalesSubPageResVO> input);

    /** api jsf实现层使用结束 */

}