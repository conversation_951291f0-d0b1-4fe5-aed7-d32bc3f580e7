package com.jdi.isc.order.center.service.protocol.jsf.finance;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.finance.ots.OtsOrderPayApiService;
import com.jdi.isc.order.center.api.finance.ots.biz.SyncOrderPayToOtsCollectApiDTO;
import com.jdi.isc.order.center.domain.finace.biz.OrderPayMsgVO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.service.adapter.mapstruct.order.OrderConvert;
import com.jdi.isc.order.center.service.atomic.order.OrderAtomicService;
import com.jdi.isc.order.center.service.manage.finace.ots.OtsOrderPayMsgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2024/9/21 10:44
 **/
@Slf4j
@Service
public class OtsOrderPayApiServiceImpl implements OtsOrderPayApiService {

    @Resource
    private OrderAtomicService orderAtomicService;

    @Resource
    private OtsOrderPayMsgService otsOrderResourceService;

    @ToolKit
    @Override
    public DataResponse<SyncOrderPayToOtsCollectApiDTO.Response> syncOrderPayToOtsCollect(SyncOrderPayToOtsCollectApiDTO.Request input) {
        Map<String, DataResponse> syncResult = new HashMap<>();

        Set<Long> failSkuIds = new HashSet<>();
        for(Long orderId: input.getOrderIds()){
            try {
                OrderPO orderPO = orderAtomicService.getOrderPOById(orderId);
                if (null==orderPO){
                    log.warn("syncOrderPayToOtsCollect orderPO null, orderId={}", orderId);
                    failSkuIds.add(orderId);
                    syncResult.put(orderId.toString(), DataResponse.error("订单不存在"));
                    continue;
                }

                OrderPayMsgVO orderPayMsgVO = OrderConvert.INSTANCE.po2OrderPayMsgVO(orderPO);
                DataResponse dataResponse = otsOrderResourceService.syncOtsCollectManual(orderPayMsgVO);
                syncResult.put(orderId.toString(), dataResponse);
                if (null==dataResponse || !dataResponse.getSuccess()){
                    log.warn("syncOrderPayToOtsCollect consumePayMsg fail, orderId={}", orderId);
                    failSkuIds.add(orderId);
                }
                Thread.sleep(100);
            } catch (Exception e) {
                failSkuIds.add(orderId);
                syncResult.put(orderId.toString(), DataResponse.error(e.getMessage()));
                log.error("syncOrderPayToOtsCollect exception, orderId={}", orderId, e);
            }

        }
        log.info("syncOrderPayToOtsCollect, failSkuIds={}", JSONObject.toJSONString(failSkuIds));

        SyncOrderPayToOtsCollectApiDTO.Response response = new SyncOrderPayToOtsCollectApiDTO.Response();
        response.setSyncResult(syncResult);
        return DataResponse.success(response);
    }
}
