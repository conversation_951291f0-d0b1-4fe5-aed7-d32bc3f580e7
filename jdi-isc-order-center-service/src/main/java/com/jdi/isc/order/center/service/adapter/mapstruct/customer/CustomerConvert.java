package com.jdi.isc.order.center.service.adapter.mapstruct.customer;

import com.jdi.isc.aggregate.read.api.customer.res.CustomerReadResp;
import com.jdi.isc.order.center.domain.enums.customer.CustomerVO;
import com.jdi.isc.order.center.domain.order.biz.OrderAutoFinishCustomerVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2024/7/9 15:17
 **/
@Mapper
public interface CustomerConvert {
    CustomerConvert INSTANCE = Mappers.getMapper(CustomerConvert.class);

    List<OrderAutoFinishCustomerVO> readRespList2AutoFinishCustomers(List<CustomerReadResp> input);

    CustomerVO customerReadResp2Vo(CustomerReadResp customerReadResp);
}
