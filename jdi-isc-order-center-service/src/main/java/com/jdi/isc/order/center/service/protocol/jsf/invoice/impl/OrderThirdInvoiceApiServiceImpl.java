package com.jdi.isc.order.center.service.protocol.jsf.invoice.impl;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.invoice.OrderThirdInvoiceApiService;
import com.jdi.isc.order.center.api.invoice.biz.OrderThirdInvoicePageApiDTO;
import com.jdi.isc.order.center.api.invoice.req.OrderThirdInvoiceDetailReqApiDTO;
import com.jdi.isc.order.center.api.invoice.req.OrderThirdInvoiceReqDTO;
import com.jdi.isc.order.center.api.invoice.req.QueryInvoiceOutlineOpenReq;
import com.jdi.isc.order.center.api.invoice.res.InvoiceOutlineOpenResp;
import com.jdi.isc.order.center.api.invoice.res.OrderThirdInvoiceDTO;
import com.jdi.isc.order.center.api.invoice.res.OrderThirdInvoiceResApiDTO;
import com.jdi.isc.order.center.domain.invoice.biz.OrderThirdInvoicePageVO;
import com.jdi.isc.order.center.domain.invoice.po.OrderThirdInvoicePO;
import com.jdi.isc.order.center.domain.invoice.req.OrderThirdInvoiceDetailReqVO;
import com.jdi.isc.order.center.domain.invoice.res.OrderThirdInvoiceResVO;
import com.jdi.isc.order.center.service.adapter.mapstruct.invoice.OrderThirdInvoiceConvert;
import com.jdi.isc.order.center.service.manage.invoice.OrderThirdInvoiceManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 第三方订单归档发票记录api服务实现
 * @Author: zhangjin176
 * @Date: 2025/02/26 15:46
 **/

@Slf4j
@Service
public class OrderThirdInvoiceApiServiceImpl implements OrderThirdInvoiceApiService {

    @Resource
    private OrderThirdInvoiceManageService orderThirdInvoiceManageService;


    @ToolKit
    @Override
    public DataResponse<OrderThirdInvoiceResApiDTO> detail(OrderThirdInvoiceDetailReqApiDTO input) {
        log.info("detail, input={}", JSONObject.toJSONString(input)); // todo 有@ToolKit注解就不需要此行日志了，删除掉。
        OrderThirdInvoiceResVO invokeResult = null;
        try {
            OrderThirdInvoiceDetailReqVO reqVO = OrderThirdInvoiceConvert.INSTANCE.detailReqApi2ReqVo(input);
            invokeResult = orderThirdInvoiceManageService.detail(reqVO.getId());
            if (null == invokeResult) {
                log.warn("detail, invokeResult fail. input={}", JSONObject.toJSONString(input));
                return DataResponse.error("未查询到数据");
            }

            OrderThirdInvoiceResApiDTO responseVo = OrderThirdInvoiceConvert.INSTANCE.orderThirdInvoiceResVo2ResReqApiDto(invokeResult);
            return DataResponse.success(responseVo);
        } catch (Exception e) {
            log.error("detail exception. input={}", JSONObject.toJSONString(input), e);
            return DataResponse.error("发生异常。");
        } finally {
            log.info("detail end. input={}, invokeResult={}", JSONObject.toJSONString(input), JSONObject.toJSONString(invokeResult));
        }
    }

    @ToolKit
    @Override
    public DataResponse<PageInfo<OrderThirdInvoicePageApiDTO.Response>> pageSearch(OrderThirdInvoicePageApiDTO.Request input) {
        log.info("pageSearch, input={}", JSONObject.toJSONString(input));
        PageInfo<OrderThirdInvoicePageVO.Response> invokeResult = null;
        try {
            OrderThirdInvoicePageVO.Request reqVO = OrderThirdInvoiceConvert.INSTANCE.pageReqApi2PageReqVo(input);
            invokeResult = orderThirdInvoiceManageService.pageSearch(reqVO);
            if (null == invokeResult) {
                log.warn("pageSearch, invokeResult fail. input={}", JSONObject.toJSONString(input));
                return DataResponse.error("查询失败");
            }

            PageInfo<OrderThirdInvoicePageApiDTO.Response> pageInfoRes = OrderThirdInvoiceConvert.INSTANCE.pageResVo2PageResApi(invokeResult);
            return DataResponse.success(pageInfoRes);
        } catch (Exception e) {
            log.error("pageSearch exception. input={}", JSONObject.toJSONString(input), e);
            return DataResponse.error("发生异常。");
        } finally {
            log.info("pageSearch end. input={}, invokeResult={}", JSONObject.toJSONString(input), JSONObject.toJSONString(invokeResult));
        }
    }

    @Override
    public DataResponse updateInvoiceById(OrderThirdInvoiceReqDTO input) {
        orderThirdInvoiceManageService.updateInvoiceById(input);
        return DataResponse.success();
    }

    @Override
    public DataResponse<List<OrderThirdInvoiceDTO>> queryByBatchNum(OrderThirdInvoiceReqDTO input) {
        List<OrderThirdInvoicePO> orderThirdInvoicePOS = orderThirdInvoiceManageService.queryByBatchNum(input.getBatchNums());
        List<OrderThirdInvoiceDTO> orderThirdInvoiceDTOS = OrderThirdInvoiceConvert.INSTANCE.listPo2Dto(orderThirdInvoicePOS);
        return DataResponse.success(orderThirdInvoiceDTOS);
    }

    @Override
    public DataResponse<List<InvoiceOutlineOpenResp>> queryInvoiceOutline(QueryInvoiceOutlineOpenReq openReq) {
        return DataResponse.success(orderThirdInvoiceManageService.queryInvoiceOutline(openReq));
    }

    @Override
    public DataResponse<Boolean> updateCatlInvoice(QueryInvoiceOutlineOpenReq openReq) {
        return DataResponse.success(orderThirdInvoiceManageService.updateCatlInvoice(openReq));
    }
}