package com.jdi.isc.order.center.service.protocol.jsf.waybill;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.waybill.PurchaseOrderWaybillApiService;
import com.jdi.isc.order.center.api.waybill.biz.PurchaseOrderWaybillEnterApiDTO;
import com.jdi.isc.order.center.api.waybill.biz.PurchaseOrderWaybillOperateApiReq;
import com.jdi.isc.order.center.api.waybill.biz.PurchaseOrderWaybillOperateApiResp;
import com.jdi.isc.order.center.api.common.BaseBizApiDTO;
import com.jdi.isc.order.center.domain.waybill.biz.PurchaseOrderWaybillEnterVO;
import com.jdi.isc.order.center.domain.waybill.biz.PurchaseOrderWaybillOperateReq;
import com.jdi.isc.order.center.domain.waybill.biz.PurchaseOrderWaybillOperateResp;
import com.jdi.isc.order.center.service.adapter.mapstruct.waybill.PurchaseOrderWaybillConvert;
import com.jdi.isc.order.center.service.manage.waybill.PurchaseOrderWaybillManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description: 一段运单api服务实现
 * @Author: zhaojianguo21
 * @Date: 2024/06/13 15:17
 **/

@Slf4j
@Service
public class PurchaseOrderWaybillApiServiceImpl implements PurchaseOrderWaybillApiService {

    @Resource
    private PurchaseOrderWaybillManageService purchaseOrderWaybillManageService;

    @ToolKit
    @Override
    public DataResponse<PurchaseOrderWaybillEnterApiDTO.Response> purchaseOrderWaybillEnter(PurchaseOrderWaybillEnterApiDTO.Request input) {
        log.info("PurchaseOrderWaybillApiServiceImpl.purchaseOrderWaybillEnter, input={}", JSONObject.toJSONString(input));
        PurchaseOrderWaybillEnterVO reqVo = PurchaseOrderWaybillConvert.INSTANCE.enterApiReq2VO(input);
        DataResponse<PurchaseOrderWaybillEnterApiDTO.Response> response = purchaseOrderWaybillManageService.purchaseOrderWaybillEnter(reqVo);
        return response;
    }

    @Override
    public DataResponse<List<PurchaseOrderWaybillOperateApiResp>> queryPurchaseOrderWaybillOperateList(PurchaseOrderWaybillOperateApiReq req) {
        log.info("queryPurchaseOrderWaybillOperateList, input={}", JSONObject.toJSONString(req));
        PurchaseOrderWaybillOperateReq reqVo = PurchaseOrderWaybillConvert.INSTANCE.operateApiReq2VO(req);

        List<PurchaseOrderWaybillOperateResp> operateResps = purchaseOrderWaybillManageService.queryPurchaseOrderWaybillOperateList(reqVo);
        List<PurchaseOrderWaybillOperateApiResp> operateApiResps = PurchaseOrderWaybillConvert.INSTANCE.operateApiResp2VO(operateResps);

        return DataResponse.success(operateApiResps);
    }

    /** 查询给定的一段运单号是否存在*/
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<String, Boolean>> checkWaybillExist(BaseBizApiDTO req) {
        return DataResponse.success(purchaseOrderWaybillManageService.checkWaybillExist(req));
    }
}
