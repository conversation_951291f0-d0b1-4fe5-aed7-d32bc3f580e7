package com.jdi.isc.order.center.service.protocol.jsf.ofc;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.ofc.StockInOutOrderApiService;
import com.jdi.isc.order.center.api.ofc.biz.req.*;
import com.jdi.isc.order.center.api.ofc.biz.rsp.StockInOutOrderDetailResDTO;
import com.jdi.isc.order.center.api.ofc.biz.rsp.StockInOutOrderResDTO;
import com.jdi.isc.order.center.domain.mapstruct.ofc.StockInOutOrderConvert;
import com.jdi.isc.order.center.domain.ofc.stockIoOrder.biz.UpdateStockInOutOrderStatusReqVO;
import com.jdi.isc.order.center.service.manage.ofc.stockIoOrder.StockInOutOrderReadService;
import com.jdi.isc.order.center.service.manage.ofc.stockIoOrder.StockInOutOrderWriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author：xubing82
 * @Date：2025/3/25 16:01
 * @Description：出入库订单服务实现类
 */
@Service
@Slf4j
public class StockInOutOrderApiServiceImpl implements StockInOutOrderApiService {


    @Autowired
    private StockInOutOrderReadService stockInOutOrderReadService;

    @Autowired
    private StockInOutOrderWriteService stockInOutOrderWriteService;

    @Override
    public DataResponse<Boolean> submitStockInOutOrder(StockInOutOrderApiDTO stockInOutOrderApiDTO) {
        return stockInOutOrderWriteService.createStockInOutOrder(stockInOutOrderApiDTO);
    }

    @Override
    public DataResponse<Boolean> updateStockInOutOrderStatus(UpdateStockInOutOrderStatusManageApiReq req) {
        UpdateStockInOutOrderStatusReqVO updatePurchaseOrderStatusReqVO = StockInOutOrderConvert.INSTANCE.manageApiReq2voReq(req);
        updatePurchaseOrderStatusReqVO.setClientReqExt(StockInOutOrderConvert.INSTANCE.req2ApiReq(req));

        return stockInOutOrderWriteService.updateStockInOutOrderStatus(updatePurchaseOrderStatusReqVO);
    }


    @Override
    public DataResponse<StockInOutOrderDetailResDTO> detail(StockInOutOrderWareApiDTO input) {
        return stockInOutOrderReadService.detail(input);
    }


    @Override
    public DataResponse<PageInfo<StockInOutOrderResDTO>> pageSearch(StockInOutOrderWarePageApiDTO input) {
        return stockInOutOrderReadService.pageSearch(input);

    }

    @Override
    public DataResponse<StockInOutOrderDetailResDTO> queryStockInOutOrder(StockInOutOrderDetailReq input) {
        return stockInOutOrderReadService.detailStockInOutOrder(input);
    }

}
