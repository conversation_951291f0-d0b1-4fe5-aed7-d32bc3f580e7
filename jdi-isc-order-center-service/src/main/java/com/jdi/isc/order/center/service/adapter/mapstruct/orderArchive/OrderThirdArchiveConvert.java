package com.jdi.isc.order.center.service.adapter.mapstruct.orderArchive;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.orderArchive.biz.OrderThirdArchiveMakeInvoiceApiDTO;
import com.jdi.isc.order.center.api.orderArchive.biz.OrderThirdArchivePageApiDTO;
import com.jdi.isc.order.center.api.orderArchive.req.OrderThirdArchiveDetailReqApiDTO;
import com.jdi.isc.order.center.api.orderArchive.req.OrderThirdArchiveReqApiDTO;
import com.jdi.isc.order.center.api.orderArchive.req.OrderThirdArchiveSaveUpdateReqApiDTO;
import com.jdi.isc.order.center.api.orderArchive.res.OrderThirdArchiveResApiDTO;
import com.jdi.isc.order.center.domain.order.biz.OrderThirdArchiveMakeInvoiceVO;
import com.jdi.isc.order.center.domain.order.biz.OrderThirdArchivePageVO;
import com.jdi.isc.order.center.domain.order.biz.OrderThirdArchiveVO;
import com.jdi.isc.order.center.domain.orderArchive.po.OrderThirdArchivePO;
import com.jdi.isc.order.center.domain.orderArchive.req.OrderThirdArchiveDetailReqVO;
import com.jdi.isc.order.center.domain.orderArchive.req.OrderThirdArchiveReqVO;
import com.jdi.isc.order.center.domain.orderArchive.req.OrderThirdArchiveSaveUpdateReqVO;
import com.jdi.isc.order.center.domain.orderArchive.res.OrderThirdArchiveResVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 订单三方归档对象转换
 * @Author: zhaojianguo21
 * @Date: 2024/12/27 22:18
 **/
@Mapper
public interface OrderThirdArchiveConvert {

    OrderThirdArchiveConvert INSTANCE = Mappers.getMapper(OrderThirdArchiveConvert.class);

    OrderThirdArchivePO orderThirdArchiveReqVo2Po(OrderThirdArchiveReqVO input);

    OrderThirdArchiveResVO orderThirdArchivePo2ResVo(OrderThirdArchivePO po);

    List<OrderThirdArchiveResVO> listPo2ResVo(List<OrderThirdArchivePO> poList);

    OrderThirdArchivePO vo2Po(OrderThirdArchiveVO vo);

    OrderThirdArchiveVO po2Vo(OrderThirdArchivePO po);

    List<OrderThirdArchiveVO> listPo2Vo(List<OrderThirdArchivePO> poList);

    List<OrderThirdArchivePO> listVo2Po(List<OrderThirdArchiveVO> voList);

    List<OrderThirdArchivePageVO.Response> pageListPo2Vo(List<OrderThirdArchivePO> pos);

    /** api jsf实现层使用开始 */
    OrderThirdArchiveReqVO orderThirdArchiveReqApiDTO2ReqVo(OrderThirdArchiveReqApiDTO input);

    OrderThirdArchiveResApiDTO orderThirdArchiveResVo2ResReqApiDto(OrderThirdArchiveResVO input);

    OrderThirdArchivePageVO.Request pageReqApi2PageReqVo(OrderThirdArchivePageApiDTO.Request input);

    PageInfo<OrderThirdArchivePageApiDTO.Response> pageResVo2PageResApi(PageInfo<OrderThirdArchivePageVO.Response> input);

    OrderThirdArchiveSaveUpdateReqVO saveUpdateReqApi2ReqVo(OrderThirdArchiveSaveUpdateReqApiDTO input);

    OrderThirdArchiveReqVO saveUpdateReqApi2Req(OrderThirdArchiveSaveUpdateReqApiDTO input);

    OrderThirdArchiveDetailReqVO detailReqApi2ReqVo(OrderThirdArchiveDetailReqApiDTO input);

    OrderThirdArchiveMakeInvoiceVO.Request makeInvoiceReqApi2ReqVo(OrderThirdArchiveMakeInvoiceApiDTO.Request input);

    OrderThirdArchiveMakeInvoiceApiDTO.Response makeInvoiceResVo2ResApi(OrderThirdArchiveMakeInvoiceVO.Response input);
    /** api jsf实现层使用结束 */

}