package com.jdi.isc.order.center.service.protocol.jsf.order;

import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.isc.order.center.admin.api.order.OrderAdminApiService;
import com.jdi.isc.order.center.admin.api.order.req.UpdateOrderStatusReqDTO;
import com.jdi.isc.order.center.admin.api.order.resp.OrderDetailDTO;
import com.jdi.isc.order.center.api.common.SystemInfoOrderApiReq;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.common.utils.DateUtil;
import com.jdi.isc.order.center.common.utils.HostUtil;
import com.jdi.isc.order.center.domain.config.OrderDuccConfig;
import com.jdi.isc.order.center.domain.order.biz.OrderChangeReqVO;
import com.jdi.isc.order.center.domain.order.biz.UpdateOrderStatusReqVO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.service.atomic.order.OrderAtomicService;
import com.jdi.isc.order.center.service.manage.order.OrderWriteService;
import com.jdi.isc.product.soa.api.common.LangConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.Set;

@Service
@Slf4j
public class OrderAdminApiServiceImpl implements OrderAdminApiService {

    @Autowired
    private OrderWriteService orderWriteService;

    @Autowired
    private OrderAtomicService orderAtomicService;

    @Autowired
    private OrderDuccConfig orderDuccConfig;

    private DataResponse<Boolean> checkAuth(UpdateOrderStatusReqDTO reqDTO){
        Set<String> adminErpSet = orderDuccConfig.getAdminErpSet();
        if(!adminErpSet.contains(reqDTO.getUpdater())){
            return DataResponse.error("无操作权限");
        }
        return DataResponse.success();
    }

    @Override
    public DataResponse<OrderDetailDTO> queryOrderInfo(UpdateOrderStatusReqDTO reqDTO) {

        DataResponse<Boolean> checkAuth = checkAuth(reqDTO);
        if(!checkAuth.getSuccess()){
            return DataResponse.error(checkAuth.getMessage());
        }

        OrderPO orderPO = orderAtomicService.getOrderPOById(reqDTO.getOrderId());

        if(orderPO == null){
            return DataResponse.error("数据不存在");
        }

        OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
        orderDetailDTO.setOrderId(orderPO.getOrderId());
        orderDetailDTO.setOrderStatus(orderPO.getOrderStatus());
        return DataResponse.success(orderDetailDTO);
    }

    @Override
    public DataResponse<Boolean> updateOrderStatus(UpdateOrderStatusReqDTO reqDTO) {
        // 校验
        DataResponse<Boolean> checkAuth = checkAuth(reqDTO);
        if(!checkAuth.getSuccess()){
            return checkAuth;
        }

        OrderPO orderPO = orderAtomicService.getOrderPOById(reqDTO.getOrderId());
        if(orderPO == null){
            return DataResponse.error("数据不存在");
        }

        UpdateOrderStatusReqVO reqVO = new UpdateOrderStatusReqVO();
        reqVO.setOrderId(reqDTO.getOrderId());
        reqVO.setThirdOrderId(orderPO.getThirdOrderId());
        reqVO.setPin(orderPO.getPin());
        reqVO.setOperateType(reqDTO.getOperateType());
        reqVO.setSourceCode(orderPO.getSourceCode());
        SystemInfoOrderApiReq systemInfoOrderApiReq = new SystemInfoOrderApiReq();
        systemInfoOrderApiReq.setLocaleList(Lists.newArrayList(LangConstant.LANG_ZH));
        systemInfoOrderApiReq.setSystemCode(Constant.SYSTEM_CODE);
        systemInfoOrderApiReq.setServiceIp(HostUtil.getLocalIP());
        systemInfoOrderApiReq.setUserIp(HostUtil.getLocalIP());
        reqVO.setClientReqExt(systemInfoOrderApiReq);
        reqVO.setOperateErp(orderPO.getPin());
        reqVO.setClientCode(orderPO.getClientCode());
        reqVO.setContractNum(orderPO.getContractNum());
        reqVO.setCurrentTime(DateUtil.getCurrentTime());
        return orderWriteService.updateOrder(reqVO);
    }

    @Override
    public DataResponse<Boolean> changeOrder(UpdateOrderStatusReqDTO reqDTO) {

        DataResponse<Boolean> checkAuth = checkAuth(reqDTO);
        if(!checkAuth.getSuccess()){
            return checkAuth;
        }

        OrderChangeReqVO orderChangeReqVO = new OrderChangeReqVO();
        orderChangeReqVO.setOrderId(reqDTO.getOrderId());
        orderChangeReqVO.setOperateErp(reqDTO.getOperateAccount());
        SystemInfoOrderApiReq systemInfoOrderApiReq = new SystemInfoOrderApiReq();
        systemInfoOrderApiReq.setLocaleList(Lists.newArrayList(LangConstant.LANG_ZH));
        systemInfoOrderApiReq.setSystemCode(Constant.SYSTEM_CODE);
        systemInfoOrderApiReq.setServiceIp(HostUtil.getLocalIP());
        systemInfoOrderApiReq.setUserIp(HostUtil.getLocalIP());
        orderChangeReqVO.setClientReqExt(systemInfoOrderApiReq);
        orderChangeReqVO.setCheckOrderStatus(true);
        return orderWriteService.changeOrder(orderChangeReqVO);
    }
}
