package com.jdi.isc.order.center.service.protocol.jsf.config;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.config.OrderStaticResourceApiService;
import com.jdi.isc.order.center.domain.config.OrderDuccConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2025/4/28
 * @Description 订单静态资源实现类 主要服务前端资源
 */

@Service
@Slf4j
public class OrderStaticResourceApiServiceImpl implements OrderStaticResourceApiService {

    @Autowired
    private OrderDuccConfig orderDuccConfig;

    @Override
    public DataResponse<Map<String, String>> getTemplate(String code) {
        Map<String, String> result = new HashMap<>();
        try {
            Map<String, String> staticTemplateMap = orderDuccConfig.getStaticTemplateConfig();
            String value = staticTemplateMap.get(code);
            result.put(code, value);
        } catch (Exception e) {
            log.error("OrderStaticResourceApiServiceImpl.getTemplate error,code:{},e",code,e);

        }

        return DataResponse.success(result);
    }
}
