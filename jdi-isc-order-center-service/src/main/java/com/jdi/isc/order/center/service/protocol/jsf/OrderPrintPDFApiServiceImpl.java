package com.jdi.isc.order.center.service.protocol.jsf;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.common.OrderPrintPDFParam;
import com.jdi.isc.order.center.api.orderRead.OrderPrintPDFApiService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/3 2:23 下午
 */

@Service
public class OrderPrintPDFApiServiceImpl implements OrderPrintPDFApiService {

    @Resource
    private OrderPrintPrintApiService orderPrintPrintApiService;

    @Override
    public DataResponse<String> orderPrint(OrderPrintPDFParam printPDFParam) {
        String url = orderPrintPrintApiService.printOrder(printPDFParam);
        return DataResponse.success(url);
    }
}
