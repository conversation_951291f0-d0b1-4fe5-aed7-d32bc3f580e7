package com.jdi.isc.order.center.service.adapter.mapstruct.invoice;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.invoice.req.OrderThirdInvoiceDetailReqApiDTO;
import com.jdi.isc.order.center.api.invoice.req.OrderThirdInvoiceReqApiDTO;
import com.jdi.isc.order.center.api.invoice.req.OrderThirdInvoiceSaveUpdateReqApiDTO;
import com.jdi.isc.order.center.api.invoice.res.OrderThirdInvoiceDTO;
import com.jdi.isc.order.center.api.invoice.res.OrderThirdInvoiceResApiDTO;
import com.jdi.isc.order.center.domain.invoice.biz.OrderThirdInvoicePageVO;
import com.jdi.isc.order.center.domain.invoice.biz.OrderThirdInvoiceVO;
import com.jdi.isc.order.center.domain.invoice.po.OrderThirdInvoicePO;
import com.jdi.isc.order.center.api.invoice.biz.OrderThirdInvoicePageApiDTO;
import com.jdi.isc.order.center.domain.invoice.req.OrderThirdInvoiceDetailReqVO;
import com.jdi.isc.order.center.domain.invoice.req.OrderThirdInvoiceReqVO;
import com.jdi.isc.order.center.domain.invoice.req.OrderThirdInvoiceSaveUpdateReqVO;
import com.jdi.isc.order.center.domain.invoice.res.OrderThirdInvoiceResVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 第三方订单归档发票记录对象转换
 * @Author: zhangjin176
 * @Date: 2025/02/26 15:46
 **/
@Mapper
public interface OrderThirdInvoiceConvert {

    OrderThirdInvoiceConvert INSTANCE = Mappers.getMapper(OrderThirdInvoiceConvert.class);

    OrderThirdInvoicePO orderThirdInvoiceReqVo2Po(OrderThirdInvoiceReqVO input);

    OrderThirdInvoiceResVO orderThirdInvoicePo2ResVo(OrderThirdInvoicePO po);

    List<OrderThirdInvoiceResVO> listPo2ResVo(List<OrderThirdInvoicePO> poList);
    List<OrderThirdInvoicePageVO.Response> listPo2ResPage(List<OrderThirdInvoicePO> poList);

    OrderThirdInvoicePO vo2Po(OrderThirdInvoiceVO vo);

    OrderThirdInvoiceVO po2Vo(OrderThirdInvoicePO po);

    List<OrderThirdInvoiceVO> listPo2Vo(List<OrderThirdInvoicePO> poList);

    List<OrderThirdInvoicePO> listVo2Po(List<OrderThirdInvoiceVO> voList);

    List<OrderThirdInvoicePageVO.Response> pageListPo2Vo(List<OrderThirdInvoicePO> pos);

    /** api jsf实现层使用开始 */
    OrderThirdInvoiceReqVO orderThirdInvoiceReqApiDTO2ReqVo(OrderThirdInvoiceReqApiDTO input);

    OrderThirdInvoiceResApiDTO orderThirdInvoiceResVo2ResReqApiDto(OrderThirdInvoiceResVO input);

    OrderThirdInvoicePageVO.Request pageReqApi2PageReqVo(OrderThirdInvoicePageApiDTO.Request input);

    PageInfo<OrderThirdInvoicePageApiDTO.Response> pageResVo2PageResApi(PageInfo<OrderThirdInvoicePageVO.Response> input);

    OrderThirdInvoiceSaveUpdateReqVO saveUpdateReqApi2ReqVo(OrderThirdInvoiceSaveUpdateReqApiDTO input);

    OrderThirdInvoiceDetailReqVO detailReqApi2ReqVo(OrderThirdInvoiceDetailReqApiDTO input);

    List<OrderThirdInvoiceDTO> listPo2Dto(List<OrderThirdInvoicePO> orderThirdInvoicePOS);
    /** api jsf实现层使用结束 */

}