package com.jdi.isc.order.center.service.protocol.jsf.forecast;

import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.forecast.ForecastOrderReadApiService;
import com.jdi.isc.order.center.api.forecast.biz.req.ForecastOrderWareLastPurchaseTimeReqApiDTO;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderWareLastPurchaseTimeResApiDTO;
import com.jdi.isc.order.center.domain.forecast.req.ForecastOrderWareLastPurchaseTimeReqVO;
import com.jdi.isc.order.center.domain.forecast.res.ForecastOrderWareLastPurchaseTimeResVO;
import com.jdi.isc.order.center.service.adapter.mapstruct.forecast.ForecastOrderConvert;
import com.jdi.isc.order.center.service.atomic.forecast.ForecastOrderWareAtomicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2025/6/18 17:23
 **/
@Slf4j
@Service
public class ForecastOrderReadApiServiceImpl implements ForecastOrderReadApiService {

    @Autowired
    private ForecastOrderWareAtomicService forecastOrderWareAtomicService;

    @Value("${spring.profiles.active}")
    private String active;

    @ToolKit(umpFlag = false)
    @Override
    public DataResponse<Map<Long, ForecastOrderWareLastPurchaseTimeResApiDTO>> queryWareLastPurchaseTime(ForecastOrderWareLastPurchaseTimeReqApiDTO request) {
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.order.center.api.forecast.ForecastOrderReadApiService.queryWareLastPurchaseTime."+active);
        List<ForecastOrderWareLastPurchaseTimeResVO> dbRes = null;
        try {
            ForecastOrderWareLastPurchaseTimeReqVO reqVo = ForecastOrderConvert.INSTANCE.wareLastPurchaseTimeApiDTO2VO(request);

            dbRes = forecastOrderWareAtomicService.queryWareLastPurchaseTime(reqVo);
            if(CollectionUtils.isEmpty(dbRes)){
                return DataResponse.success(Collections.EMPTY_MAP);
            }

            List<ForecastOrderWareLastPurchaseTimeResApiDTO> apiList = ForecastOrderConvert.INSTANCE.wareLastPurchaseTimeVoList2DtoList(dbRes);

            // 将apiList转为map映射
            Map<Long,ForecastOrderWareLastPurchaseTimeResApiDTO> apiMap = apiList.stream()
                    .collect(Collectors.toMap(ForecastOrderWareLastPurchaseTimeResApiDTO::getSkuId, Function.identity()));

            return DataResponse.success(apiMap);
        } catch (Exception e) {
            log.error("ForecastOrderReadApiService.queryWareLastPurchaseTime, req = {}, dbRes = {}", JSON.toJSONString(request), JSON.toJSONString(dbRes), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("ForecastOrderReadApiService.queryWareLastPurchaseTime, finally, req = {}, dbRes = {}", JSON.toJSONString(request), JSON.toJSONString(dbRes));
            Profiler.registerInfoEnd(callerInfo);
        }

        return DataResponse.error("查询最新采购时间异常");
    }
}
