package com.jdi.isc.order.center.service.protocol.jsf.customs;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.customs.OrderPalletRecordApiService;
import com.jdi.isc.order.center.api.customs.biz.req.OrderPalletRecordPageReqDTO;
import com.jdi.isc.order.center.api.customs.biz.req.OrderPalletRecordRemoveReqDTO;
import com.jdi.isc.order.center.api.customs.biz.req.OrderPalletRecordSaveReqDTO;
import com.jdi.isc.order.center.api.customs.biz.req.QueryOrderPalletRecordReqDTO;
import com.jdi.isc.order.center.api.customs.biz.resp.OrderPalletRecordDTO;
import com.jdi.isc.order.center.api.customs.biz.resp.OrderPalletRecordSaveRespDTO;
import com.jdi.isc.order.center.common.frame.BaseManageSupportService;
import com.jdi.isc.order.center.domain.customs.biz.OrderPalletRecordRemoveVO;
import com.jdi.isc.order.center.domain.customs.biz.OrderPalletRecordVO;
import com.jdi.isc.order.center.service.adapter.mapstruct.customs.OrderPalletRecordConvert;
import com.jdi.isc.order.center.service.atomic.customs.OrderPalletRecordAtomicService;
import com.jdi.isc.order.center.service.manage.customs.OrderPalletRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class OrderPalletRecordApiServiceImpl extends BaseManageSupportService<OrderPalletRecordDTO, OrderPalletRecordVO> implements OrderPalletRecordApiService {

    @Autowired
    private OrderPalletRecordService orderPalletRecordService;

    @Autowired
    private OrderPalletRecordAtomicService orderPalletRecordAtomicService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<OrderPalletRecordSaveRespDTO>> batchSaveOrderPalletRecord(OrderPalletRecordSaveReqDTO orderPalletRecordSaveReqDTO) {
        return orderPalletRecordService.batchSaveOrderPalletRecord(orderPalletRecordSaveReqDTO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> batchRemoveOrderPalletRecord(OrderPalletRecordRemoveReqDTO orderPalletRecordRemoveReqDTO) {
        OrderPalletRecordRemoveVO orderPalletRecordRemoveVO = OrderPalletRecordConvert.INSTANCE.removeReqDTO2VO(orderPalletRecordRemoveReqDTO);
        return orderPalletRecordService.batchRemoveOrderPalletRecord(orderPalletRecordRemoveVO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<OrderPalletRecordDTO>> queryOrderPalletRecordPageList(OrderPalletRecordPageReqDTO orderPalletRecordPageReqDTO) {
        PageInfo<OrderPalletRecordDTO> output = new PageInfo<>();

        DataResponse<PageInfo<OrderPalletRecordVO>> pageInfoDataResponse = orderPalletRecordService.queryOrderPalletRecordPageList(OrderPalletRecordConvert.INSTANCE.pageReqDTO2VO(orderPalletRecordPageReqDTO));
        if(pageInfoDataResponse.getData() != null){
            PageInfo<OrderPalletRecordVO> data = pageInfoDataResponse.getData();
            output.setSize(data.getSize());
            output.setIndex(data.getIndex());
            if(CollectionUtils.isNotEmpty(data.getRecords())){
                output.setTotal(data.getTotal());
                output.setRecords(OrderPalletRecordConvert.INSTANCE.listVO2DTO(data.getRecords()));
            }
        }
        return DataResponse.success(output);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<OrderPalletRecordDTO>> queryOrderPalletRecordList(QueryOrderPalletRecordReqDTO queryOrderPalletRecordReqDTO) {

        DataResponse<List<OrderPalletRecordVO>> pageInfoDataResponse = orderPalletRecordService.queryOrderPalletRecordList(OrderPalletRecordConvert.INSTANCE.reqDTO2VO(queryOrderPalletRecordReqDTO));
        if(pageInfoDataResponse.getData() != null){
            List<OrderPalletRecordVO> list = pageInfoDataResponse.getData();
            if(CollectionUtils.isNotEmpty(list)){
                return DataResponse.success(OrderPalletRecordConvert.INSTANCE.listVO2DTO(list));
            }
        }

        return DataResponse.success();
    }
}
