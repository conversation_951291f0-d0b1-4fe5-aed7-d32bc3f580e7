package com.jdi.isc.order.center.service.protocol.jsf.declaration;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.biz.resp.CustomsDeclarationBaseResp;
import com.jdi.isc.order.center.api.declaration.DeclarationReadApiService;
import com.jdi.isc.order.center.api.declaration.biz.CustomsDeclarationBaseApiReq;
import com.jdi.isc.order.center.service.manage.declaration.DeclarationReadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class DeclarationReadApiServiceImpl implements DeclarationReadApiService {

    @Autowired
    DeclarationReadService declarationReadService;

    /**
     * 根据销售订单ID集合查询订单信息。
     * @param saleOrdIds 销售订单ID集合。
     * @return 包含订单信息的DataResponse对象。
     */
    @Override
    public DataResponse<Map<String, List<CustomsDeclarationBaseResp>>> queryOrderInfoBySaleOrdIds(Set<String> saleOrdIds) {

        return declarationReadService.queryOrderInfoBySaleOrdIds(saleOrdIds);
    }




    @Override
    public DataResponse<Boolean> updateOrderInfoBySaleOrdId(CustomsDeclarationBaseApiReq customsDeclarationBaseApiReq) {
        return declarationReadService.updateOrderInfoBySaleOrdId(null);
    }



}
