package com.jdi.isc.order.center.service.adapter.mapstruct.aftersale;

import com.jdi.isc.order.center.api.aftersales.res.AfterSalesWareApiResp;
import com.jdi.isc.order.center.domain.aftersales.po.AfterSalesWarePO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 售后单商品属性对象转换
 * <AUTHOR>
 * @date 20240822
 */
@Mapper
public interface AfterSalesWareConvert {

    AfterSalesWareConvert INSTANCE = Mappers.getMapper(AfterSalesWareConvert.class);

    @InheritConfiguration
    List<AfterSalesWareApiResp> listPo2dto(List<AfterSalesWarePO> pos);

}
