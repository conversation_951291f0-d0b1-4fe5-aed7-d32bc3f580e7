package com.jdi.isc.order.center.service.protocol.jsf.invoice;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.finance.customerInvoice.brInvoice.BrazilCustomerInvoiceApiService;
import com.jdi.isc.order.center.api.finance.customerInvoice.brInvoice.req.BRCustomerInvoiceDetailReq;
import com.jdi.isc.order.center.api.finance.customerInvoice.brInvoice.req.BRCustomerInvoicePageReq;
import com.jdi.isc.order.center.api.finance.customerInvoice.brInvoice.req.BRCustomerInvoiceSaveReq;
import com.jdi.isc.order.center.api.finance.customerInvoice.brInvoice.resp.BRCustomerInvoiceDetailDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.brInvoice.resp.BRCustomerInvoiceInfoDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.brInvoice.resp.BRCustomerInvoicePageInfoDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.brInvoice.resp.BRCustomerInvoiceSaveResp;
import com.jdi.isc.order.center.service.manage.invoice.BRCustomerInvoiceService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/12/26
 * @Description
 */
@Data
@Service
public class BrazilCustomerInvoiceApiServiceImpl implements BrazilCustomerInvoiceApiService {

    @Autowired
    private BRCustomerInvoiceService brCustomerInvoiceService;

    @ToolKit(exceptionWrap = true)
    @Override
    public DataResponse<BRCustomerInvoiceSaveResp> saveBrInvoiceData(BRCustomerInvoiceSaveReq brCustomerInvoiceSaveReq) {
        return brCustomerInvoiceService.saveBrInvoiceData(brCustomerInvoiceSaveReq);
    }

    @Override
    public DataResponse<BRCustomerInvoiceInfoDTO> queryBrInvoiceDetail(BRCustomerInvoiceDetailReq brCustomerInvoiceDetailReq) {
        return brCustomerInvoiceService.queryBrInvoiceDetail(brCustomerInvoiceDetailReq);
    }

    @Override
    public DataResponse<BRCustomerInvoiceDetailDTO> detailQueryBrInvoice(BRCustomerInvoiceDetailReq brCustomerInvoiceDetailReq) {
        return brCustomerInvoiceService.detailQueryBrInvoice(brCustomerInvoiceDetailReq);
    }

    @Override
    public DataResponse<PageInfo<BRCustomerInvoicePageInfoDTO>> pageQueryBrInvoice(BRCustomerInvoicePageReq brCustomerInvoicePageReq) {
        return brCustomerInvoiceService.pageQueryBrInvoice(brCustomerInvoicePageReq);
    }

}
