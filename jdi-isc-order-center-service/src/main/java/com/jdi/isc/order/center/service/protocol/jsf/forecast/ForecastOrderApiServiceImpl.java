package com.jdi.isc.order.center.service.protocol.jsf.forecast;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.common.SystemInfoOrderApiReq;
import com.jdi.isc.order.center.api.constants.OrderTypeConstant;
import com.jdi.isc.order.center.api.forecast.ForecastOrderApiService;
import com.jdi.isc.order.center.api.forecast.biz.ForecastOrderApiDTO;
import com.jdi.isc.order.center.api.forecast.biz.req.*;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderApiResp;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderCountVO;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderDetailResp;
import com.jdi.isc.order.center.common.utils.JsonUtil;
import com.jdi.isc.order.center.domain.forecast.biz.ForecastOrderApiVo;
import com.jdi.isc.order.center.domain.forecast.biz.LocalSplitForecastOrderReqVO;
import com.jdi.isc.order.center.domain.forecast.biz.UpdateForecastOrderStatusReqVo;
import com.jdi.isc.order.center.domain.forecast.biz.UpdateForecastOrdersStatusReqVo;
import com.jdi.isc.order.center.service.adapter.mapstruct.forecast.ForecastOrderConvert;
import com.jdi.isc.order.center.service.manage.forecast.ForecastOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2024/9/21 10:44
 **/
@Slf4j
@Service
public class ForecastOrderApiServiceImpl implements ForecastOrderApiService {



    @Resource
    ForecastOrderService forecastOrderService;

    /**
     * 提交预测订单
     * @param req 预测订单API传输对象，包含订单相关信息
     * @return 数据响应对象，包含操作是否成功的布尔值
     */
    @Override
    public DataResponse<Boolean> submitForecastOrder(ForecastOrderApiDTO req) {
        return forecastOrderService.submitForecastOrder(req);
    }

    /**
     * 拆分预测订单本地处理
     * @param req 拆分预测订单本地请求参数
     * @return 包含处理结果的响应对象
     */
    @Override
    public DataResponse<Boolean> splitForecastOrderLocal(SplitForecastOrderLocalReq req) {
        LocalSplitForecastOrderReqVO reqVO = JsonUtil.getJavaObject(req, LocalSplitForecastOrderReqVO.class);
        reqVO.setOrderType(OrderTypeConstant.LOCAL);
        return forecastOrderService.splitForecastOrder(reqVO);
    }

    /**
     * 更新预测订单状态
     * @param req 更新预测订单状态请求参数对象
     * @return 包含操作结果的响应对象
     */
    @Override
    @ToolKit
    public DataResponse<Boolean> updateForecastOrderStatus(UpdateForecastOrderStatusApiReq req) {
        DataResponse<Boolean> dataResponse = null;
        try{

            if(StringUtils.isBlank(req.getOperateAccount())){
                req.setOperateAccount(req.getUpdater());
            }

            UpdateForecastOrderStatusReqVo updateForecastOrderStatusReqVo = JsonUtil.getJavaObject(req, UpdateForecastOrderStatusReqVo.class);
            SystemInfoOrderApiReq clientReqExt = JsonUtil.getJavaObject(req,SystemInfoOrderApiReq.class);
            updateForecastOrderStatusReqVo.setClientReqExt(clientReqExt);
            return forecastOrderService.updateForecastOrderStatus(updateForecastOrderStatusReqVo);
        }catch (Exception e){
            log.error("ForecastOrderApiServiceImpl.updateForecastOrderStatus, req = {}", JSON.toJSONString(req), e);
            return DataResponse.error(DataResponseCode.SYSTEM_ERROR.getMessage());
        }
    }

    @Override
    @ToolKit
    public DataResponse<Boolean> updateForecastOrdersStatus(UpdateForecastOrdersStatusApiReq req) {
        DataResponse<Boolean> dataResponse = null;
        try{
            UpdateForecastOrdersStatusReqVo updateForecastOrdersStatusReqVo = JsonUtil.getJavaObject(req, UpdateForecastOrdersStatusReqVo.class);
            SystemInfoOrderApiReq clientReqExt = JsonUtil.getJavaObject(req,SystemInfoOrderApiReq.class);
            updateForecastOrdersStatusReqVo.setClientReqExt(clientReqExt);
            return forecastOrderService.updateForecastOrdersStatus(updateForecastOrdersStatusReqVo);
        }catch (Exception e){
            log.error("ForecastOrderApiServiceImpl.updateForecastOrderStatus, req = {}", JSON.toJSONString(req), e);
            return DataResponse.error(DataResponseCode.SYSTEM_ERROR.getMessage());
        }
    }





    /**
     * 分页查询预测订单信息
     * @param req 预测订单查询请求参数
     * @return 包含分页预测订单信息的响应对象
     */
    @Override
    public DataResponse<PageInfo<ForecastOrderApiResp>> pageForecastOrder(ForecastOrderApiReq req) {
        ForecastOrderApiVo forecastOrderApiVo = ForecastOrderConvert.INSTANCE.apiDto2Vo(req);
        return forecastOrderService.pageForecastOrder(forecastOrderApiVo);
    }

    /**
     * 分页查询预测订单信息返回给VC系统
     * @param req 预测订单查询请求参数
     * @return 包含分页预测订单信息的响应对象
     */
    @Override
    public DataResponse<PageInfo<ForecastOrderApiResp>> pageForecastOrderToVc(ForecastOrderApiReq req) {
        ForecastOrderApiVo forecastOrderApiVo = ForecastOrderConvert.INSTANCE.apiDto2Vo(req);
        return forecastOrderService.pageForecastOrderToVc(forecastOrderApiVo);
    }



    /**
     * 根据预测订单请求生成预测订单
     * @param req 预测订单请求参数对象，包含生成预测订单所需的信息
     * @return 包含操作是否成功的响应对象，其中布尔值表示操作结果
     */
    @Override
    public DataResponse<Boolean> issuedForecastOrder(IssuedForecastOrderApiReq req) {
        return forecastOrderService.issuedForecastOrder(req);
    }

    /**
     * 根据预测订单请求获取预测订单详情
     * @param req 预测订单请求参数对象，包含查询所需的请求信息
     * @return 包含预测订单详情信息的数据响应对象
     */
    @Override
    public DataResponse<ForecastOrderDetailResp> detailForecastOrder(ForecastDetailOrderApiReq req) {
        return forecastOrderService.detailForecastOrder(req);
    }



    /**
     * 根据预测订单请求获取预测订单详情
     * @param req 预测订单请求参数对象，包含查询所需的请求信息
     * @return 包含预测订单详情信息的数据响应对象
     */
    @Override
    public DataResponse<ForecastOrderDetailResp> detailForecastOrderToVc(ForecastDetailOrderApiReq req) {
        return forecastOrderService.detailForecastOrderToVc(req);
    }




    @Override
    public DataResponse<ForecastOrderCountVO> count(ForecastOrderCountReq req) {
        return forecastOrderService.count(req);
    }


}
