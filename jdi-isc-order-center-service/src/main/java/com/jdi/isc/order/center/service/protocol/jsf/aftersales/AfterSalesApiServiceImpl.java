package com.jdi.isc.order.center.service.protocol.jsf.aftersales;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.aftersales.AfterSalesApiService;
import com.jdi.isc.order.center.api.aftersales.req.*;
import com.jdi.isc.order.center.api.aftersales.res.AfterSalesApiResp;
import com.jdi.isc.order.center.api.common.validation.group.ValidateAfterSalesGroup;
import com.jdi.isc.order.center.common.utils.validation.ValidateResult;
import com.jdi.isc.order.center.common.utils.validation.ValidationUtil;
import com.jdi.isc.order.center.service.manage.aftersales.AfterSalesManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 售后开放服务
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
@Service
@Slf4j
public class AfterSalesApiServiceImpl implements AfterSalesApiService {

    @Resource
    private AfterSalesManageService afterSalesManageService;
    private final static String TOKEN = "abc123xyz";

    /**
     * 提交售后单
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> submitAfterSalesOrder(AfterSalesReq afterSalesOpenReq) {
        return afterSalesManageService.submitAfterSalesOrder(afterSalesOpenReq);
    }

    /**
     * 移除售后单
     */
    @Override
    public DataResponse<Void> removeAfterSalesOrder(Long orderId, String token) {
        if (!TOKEN.equals(token)) {
            return DataResponse.error("ILLEGAL ACCESS");
        }
        afterSalesManageService.removeAfterSalesOrder(orderId);
        afterSalesManageService.removeAfterSalesWareOrder(orderId);
        return DataResponse.success();
    }

    /**
     * 售后单分页查询
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<AfterSalesApiResp>> pageAfterSalesOrder(AfterSalesApiDTO req) {
        return DataResponse.success(afterSalesManageService.pageAfterSalesOrder(req));
    }

    /**
     * 售后单信息查询
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<AfterSalesApiResp>> detailList(AfterSalesApiDTO req) {
        return DataResponse.success(afterSalesManageService.detailList(req));
    }

    /**
     * 售后单信息审核
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Void> audit(AfterSalesProcessDTO req) {
        ValidateResult<AfterSalesProcessDTO> validateResult = ValidationUtil.checkParam(req, ValidateAfterSalesGroup.audit.class);
        if(!validateResult.getSuccess()){
            return DataResponse.error(DataResponseCode.PARAM_ERROR.getCode(), validateResult.getPropertyErrors().get(0).getMessage());
        }
        return afterSalesManageService.audit(req);
    }

    /**
     * 售后单处理
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Void> process(AfterSalesProcessDTO req) {
        return afterSalesManageService.process(req);
    }

    /**
     * 售后单取消
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Void> cancel(AfterSalesProcessDTO req) {
        return afterSalesManageService.cancel(req);
    }

    @Override
    public DataResponse<Map<Long, Map<String, Integer>>> queryStatusByMkuIds(Set<Long> mkuIdSet) {
        DataResponse<Map<Long, Map<String, Integer>>> statusMapResponse = afterSalesManageService.queryStatusByMkuIds(mkuIdSet);
        if (!statusMapResponse.getSuccess()) {
            return DataResponse.error(statusMapResponse.getMessage());
        }
        return statusMapResponse;
    }

    @Override
    public DataResponse<List<String>> duringAfterSalesProcess(List<AfterSalesStatusDTO> afterSalesStatusDTOList) {
        return afterSalesManageService.duringAfterSalesProcess(afterSalesStatusDTOList);
    }


    @Override
    public DataResponse<Void> reassign(AfterSalesProcessDTO req) {
        return null;
    }

    @Override
    public DataResponse<Boolean> updateAfterSalesStatus(UpdateAfterSalesStatusApiDTO req) {
        return null;
    }
}
