package com.jdi.isc.order.center.service.protocol.jsf.waybill;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.aggregate.read.api.order.constant.OrderApiConstants;
import com.jdi.isc.aggregate.read.api.order.req.QueryOrderDetailReadReq;
import com.jdi.isc.order.center.api.constants.orderWaybill.OrderWaybillTypeConstant;
import com.jdi.isc.order.center.api.waybill.OrderWaybillApiService;
import com.jdi.isc.order.center.api.waybill.biz.BillDeliverSubmitApiDTO;
import com.jdi.isc.order.center.api.waybill.biz.OrderWaybillDeliverApiDTO;
import com.jdi.isc.order.center.api.waybill.biz.OrderWaybillSendApiDTO;
import com.jdi.isc.order.center.api.waybill.biz.OrderWaybillStatusUpdateApiDTO;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.domain.order.response.OrderConsigneeInfoVO;
import com.jdi.isc.order.center.domain.order.response.OrderInfoVO;
import com.jdi.isc.order.center.domain.waybill.biz.*;
import com.jdi.isc.order.center.domain.waybill.po.OrderWaybillPO;
import com.jdi.isc.order.center.rpc.lang.LangRpcService;
import com.jdi.isc.order.center.rpc.mq.OrderMqService;
import com.jdi.isc.order.center.rpc.order.OrderReadApiRpcService;
import com.jdi.isc.order.center.service.adapter.mapstruct.waybill.OrderWaybillConvert;
import com.jdi.isc.order.center.service.atomic.waybill.OrderWaybillAtomicService;
import com.jdi.isc.order.center.service.manage.waybill.OrderWaybillManageService;
import com.jdi.isc.order.center.service.manage.waybill.order.OrderWaybillBeanFactoryService;
import com.jdi.isc.order.center.service.manage.waybill.order.OrderWaybillWriteService;
import com.jdi.isc.vc.soa.api.purchaseOrder.req.PurchaseOrderDetailReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 二段运单api服务实现
 * @Author: zhaojianguo21
 * @Date: 2024/06/13 18:12
 **/

@Slf4j
@Service
public class OrderWaybillApiServiceImpl implements OrderWaybillApiService {

    @Resource
    private OrderWaybillAtomicService orderWaybillAtomicService;

    @Resource
    private OrderWaybillManageService orderWaybillManageService;


    @Resource
    private OrderMqService orderMqService;

    @Resource
    private OrderReadApiRpcService orderReadApiRpcService;
    @Resource
    private LangRpcService langRpcService;
    @Resource
    private OrderWaybillBeanFactoryService orderWaybillBeanFactoryService;


    @Value("${topic.producer.orderWaybill.created}")
    private String orderWaybillCreatedTopic;

    @Value("${topic.producer.order.waybill}")
    private String waybillUpdateStatusTopic;

    @Override
    @ToolKit
    public DataResponse<OrderWaybillSendApiDTO.ResponseOfCheck> orderSendCheck(OrderWaybillSendApiDTO.Request input) {
        OrderWaybillVO reqVo = OrderWaybillConvert.INSTANCE.sendApiReq2Vo(input);

        OrderWaybillWriteService orderWaybillWriteService = orderWaybillBeanFactoryService.getOrderWaybillWriteServiceBean(input.getWaybillType());
        DataResponse<OrderWaybillSendApiDTO.ResponseOfCheck> response = orderWaybillWriteService.orderSendCheck(reqVo);
        return response;
    }

    @Override
    @ToolKit
    public DataResponse<OrderWaybillSendApiDTO.Response> orderWaybillSend(OrderWaybillSendApiDTO.Request input) {
        OrderWaybillVO reqVo = OrderWaybillConvert.INSTANCE.sendApiReq2Vo(input);

        OrderWaybillWriteService orderWaybillWriteService = orderWaybillBeanFactoryService.getOrderWaybillWriteServiceBean(input.getWaybillType());
        DataResponse<OrderWaybillSendApiDTO.Response> response = orderWaybillWriteService.saveSendInfo(reqVo);
        if (null==response || !response.getSuccess()){
            log.info("orderWaybillSend, saveSendInfo fail. response={}, input={}",
                    JSONObject.toJSONString(response), JSONObject.toJSONString(input));
            return response;
        }

        // 发送创建运单mq消息
        sendMqMsgAfterOrderWaybillCreate(input, response);

        return response;
    }

    @Override
    @ToolKit
    public DataResponse<OrderWaybillStatusUpdateApiDTO.Response> updateToSendStatus(OrderWaybillStatusUpdateApiDTO.Request input) {
        OrderWaybillStatusUpdateVO.Request reqVo = OrderWaybillConvert.INSTANCE.statusUpdateApiDtoReq2Vo(input);

        OrderWaybillWriteService orderWaybillWriteService = orderWaybillBeanFactoryService.getOrderWaybillWriteServiceBean(input.getWaybillType());
        DataResponse<OrderWaybillStatusUpdateVO.Response> response = orderWaybillWriteService.updateToSendStatus(reqVo);

        if (!response.getSuccess()){
            return DataResponse.error(response.getMessage());
        }

        OrderWaybillStatusUpdateApiDTO.Response apiResponseData = OrderWaybillConvert.INSTANCE.statusUpdateVoRes2Api(response.getData());
        Set<String> waybillNums = input.getWaybillNums();
        List<OrderWaybillPO> orderWaybillPOList = orderWaybillAtomicService.queryByWaybillNum(waybillNums);
        Map<String, Object> orderWaybillPOMap = orderWaybillPOList.stream().collect(Collectors.toMap(OrderWaybillPO::getWaybillNum, Function.identity(), (o1, o2) -> o1));
        orderMqService.sendMessageBatchRetry(orderWaybillPOMap, waybillUpdateStatusTopic);
        return DataResponse.success(apiResponseData);
    }

    @Override
    @ToolKit
    public DataResponse<OrderWaybillDeliverApiDTO.Response> orderWaybillDeliver(OrderWaybillDeliverApiDTO.Request input) {
        OrderWaybillDeliveryCustomerVO reqVo = OrderWaybillConvert.INSTANCE.deliverApiReq2Vo(input);
        DataResponse<OrderWaybillDeliverApiDTO.Response> response = orderWaybillManageService.waybillDeliver(reqVo);
        return response;
    }

    @Override
    @ToolKit
    public DataResponse<BillDeliverSubmitApiDTO.Response> submitBillDeliver(BillDeliverSubmitApiDTO.Request input) {
        BillDeliverySubmitVO reqVo = OrderWaybillConvert.INSTANCE.deliverBillApiReq2Vo(input);
        DataResponse<BillDeliverSubmitApiDTO.Response> response = orderWaybillManageService.billDeliverSubmit(reqVo);
        return response;
    }


    @Override
    public DataResponse<OrderWaybillDeliverApiDTO.Response> queryWayBill(OrderWaybillDeliverApiDTO.Request input) {
        OrderWaybillDeliveryCustomerVO reqVo = OrderWaybillConvert.INSTANCE.deliverApiReq2Vo(input);
        DataResponse<OrderWaybillDeliverApiDTO.Response> response = orderWaybillManageService.queryWayBill(reqVo);
        return response;
    }

    /**
     * 发送创建运单mq消息
     * @param input
     * @param response
     */
    private void sendMqMsgAfterOrderWaybillCreate(OrderWaybillSendApiDTO.Request input, DataResponse<OrderWaybillSendApiDTO.Response> response){
        log.info("sendMqMsgAfterOrderWaybillCreate, param input={}, response={}", JSONObject.toJSONString(input), JSONObject.toJSONString(response));
        if (null==response || !response.getSuccess()){
            log.info("sendMqMsgAfterOrderWaybillCreate, send msg fail, do not send mq msg. input={}", JSONObject.toJSONString(input));
            return;
        }
        Set<Long> orderIds = Arrays.stream(input.getOrderIds().split(Constant.COMMA))
                .filter(o-> StringUtils.isNotBlank(o)).map(o->Long.valueOf(o.trim())).collect(Collectors.toSet());
        OrderWaybillSendApiDTO.Response orderWaybill = response.getData();

        OrderWaybillPO orderWaybillPO = orderWaybillAtomicService.queryByWaybillNum(orderWaybill.getWaybillNum());
        OrderWaybillMqMsgVO mqMsgVO = OrderWaybillConvert.INSTANCE.po2MsgVo(orderWaybillPO);
        mqMsgVO.setOrderIds(orderIds);

        log.info("sendMqMsgAfterOrderWaybillCreate, mqMsgVO={}, topic={}", JSONObject.toJSONString(mqMsgVO), orderWaybillCreatedTopic);
        // 发送创建运单消息
        orderMqService.sendMessageRetry(mqMsgVO.getWaybillNum(), mqMsgVO, orderWaybillCreatedTopic);
    }

}
