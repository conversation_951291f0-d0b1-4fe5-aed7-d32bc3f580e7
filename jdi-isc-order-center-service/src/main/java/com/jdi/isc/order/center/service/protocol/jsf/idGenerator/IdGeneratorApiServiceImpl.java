package com.jdi.isc.order.center.service.protocol.jsf.idGenerator;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.idGenerator.IdGeneratorApiService;
import com.jdi.isc.order.center.service.manage.common.OrderIdGeneratorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2024/8/7 21:11
 **/
@Slf4j
@Service
public class IdGeneratorApiServiceImpl implements IdGeneratorApiService {

    @Resource
    private OrderIdGeneratorService orderIdGeneratorService;

    @ToolKit
    @Override
    public DataResponse<List<String>> generateDeliverId(Long size) {
        List<String> res = orderIdGeneratorService.getDeliverId(size);
        return DataResponse.success(res);
    }

    @ToolKit
    @Override
    public DataResponse<List<String>> generateParcelId(Long size) {
        List<String> res = orderIdGeneratorService.getParcelId(size);
        return DataResponse.success(res);
    }
}
