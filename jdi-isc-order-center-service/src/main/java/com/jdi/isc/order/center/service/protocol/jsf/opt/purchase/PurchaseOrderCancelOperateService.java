package com.jdi.isc.order.center.service.protocol.jsf.opt.purchase;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.common.SystemInfoOrderApiReq;
import com.jdi.isc.order.center.api.constants.OrderTypeConstant;
import com.jdi.isc.order.center.api.constants.PurchaseOrderOperateTypeConstant;
import com.jdi.isc.order.center.api.constants.PurchaseOrderStatusConstant;
import com.jdi.isc.order.center.api.opt.biz.OrderOptSaveDTO;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.domain.enums.purchase.PurchaseModelEnum;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.UpdatePurchaseOrderStatusReqVO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderAtomicService;
import com.jdi.isc.order.center.service.manage.purchaseOrder.PurchaseOrderWriteService;
import com.jdi.isc.order.center.service.manage.purchaseOrder.impl.LocalPurchaseOrderWriteServiceImpl;
import com.jdi.isc.product.soa.api.common.LangConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * 采购单运维基类
 */
@Slf4j
@Service
public class PurchaseOrderCancelOperateService extends OrderOperateService {

    @Autowired
    private PurchaseOrderAtomicService purchaseOrderAtomicService;

    @Autowired
    private LocalPurchaseOrderWriteServiceImpl localPurchaseOrderWriteServiceImpl;

    @Autowired
    private PurchaseOrderWriteService purchaseOrderWriteService;

    public DataResponse<Boolean> operate(OrderOptSaveDTO saveDTO){

        String bizId = saveDTO.getBizId();
        PurchaseOrderPO purchaseOrder = purchaseOrderAtomicService.queryValidPurchaseOrderByPoId(bizId);
        log.info("PurchaseOrderCancelOperateService.operate purchaseOrder:{}", JSONObject.toJSONString(purchaseOrder));
        if (Objects.isNull(purchaseOrder)) {
            log.error("PurchaseOrderCancelOperateService.operate purchaseOrder is null ,param:{}", JSONObject.toJSONString(saveDTO));
            return DataResponse.buildError("采购单不存在");
        }

        Integer purchaseModel = purchaseOrder.getPurchaseModel();
        if (Objects.isNull(purchaseModel) || PurchaseModelEnum.DIRECT_SUPPLY.getCode().equals(purchaseModel) || PurchaseModelEnum.CONSIGNMENT.getCode().equals(purchaseModel)) {
            return DataResponse.buildError("直供模式的采购单请走订单取消方式，");
        }


        Integer purchaseOrderStatus = purchaseOrder.getPurchaseOrderStatus();
        //已入仓及以后 校验财务邮件截图不能为空
        if (PurchaseOrderStatusConstant.CANCEL.getStatus().equals(purchaseOrderStatus)) {
            return DataResponse.buildError("采购单已经是取消状态");
        }

        //不允许取消的场景
        if (!allowCancelStatus().contains(purchaseOrderStatus)) {
            return DataResponse.buildError(String.format("%s 状态不允许取消",PurchaseOrderStatusConstant.getDTOByStatus(purchaseOrderStatus).getDesc()));
        }

        //跨境采购单
        if (OrderTypeConstant.CROSS_BORDER == purchaseOrder.getPurchaseOrderType()) {
            //判断iop订单是否取消
            if (!super.checkIopOrderStatusIsCancel(purchaseOrder.getPurchaseOrderId())) {
                return DataResponse.buildError("内贸单没有取消");
            }
        }
        //取消采购单
        log.info("PurchaseOrderCancelOperateService.operate param:{}", JSON.toJSONString(saveDTO));
        DataResponse<Boolean> dataResponse = cancelPurchaseOrder(saveDTO,purchaseOrder);
        log.info("PurchaseOrderCancelOperateService.operate result:{}", JSON.toJSONString(dataResponse));
        if (!Boolean.TRUE.equals(dataResponse.getSuccess())) {
            return DataResponse.buildError(dataResponse.getMessage());
        }

        //更新发票价格 TODO
        //updateInvoicePrice();
        //保存日志
        super.saveOptLog(saveDTO, DataResponse.success());
        return DataResponse.success();
    }

    public Set<Integer> allowCancelStatus(){
        Set<Integer> status = new HashSet<>();
        status.add(PurchaseOrderStatusConstant.WAIT_CREATE.getStatus());
        status.add(PurchaseOrderStatusConstant.CREATE_FINISH.getStatus());
        status.add(PurchaseOrderStatusConstant.CANCEL_INNER.getStatus());
        status.add(PurchaseOrderStatusConstant.CONFIRM.getStatus());
        status.add(PurchaseOrderStatusConstant.WAIT_DISTRIBUTED.getStatus());
        status.add(PurchaseOrderStatusConstant.RECEIVE.getStatus());
        return status;
    }

    private DataResponse<Boolean> cancelPurchaseOrder(OrderOptSaveDTO saveDTO, PurchaseOrderPO purchaseOrder) {

        SystemInfoOrderApiReq systemInfoOrderApiReq = new SystemInfoOrderApiReq();
        systemInfoOrderApiReq.setLocaleList(Lists.newArrayList(LangConstant.LANG_ZH));
        systemInfoOrderApiReq.setSystemCode(Constant.SYSTEM_CODE);
        systemInfoOrderApiReq.setServiceIp(saveDTO.getServiceIp());
        systemInfoOrderApiReq.setUserIp(saveDTO.getUserIp());

        UpdatePurchaseOrderStatusReqVO req = new UpdatePurchaseOrderStatusReqVO();
        req.setPurchaseOrderId(purchaseOrder.getPurchaseOrderId());
        req.setOperateAccount(saveDTO.getApplicant());
        req.setOperate(PurchaseOrderOperateTypeConstant.CANCEL_MANAGE);
        req.setClientReqExt(systemInfoOrderApiReq);
        return purchaseOrderWriteService.updatePurchaseOrderStatus(req);
    }
}
