package com.jdi.isc.order.center.service.protocol.jsf.parcel;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.constants.orderWaybill.OrderWaybillDataSourceConstant;
import com.jdi.isc.order.center.api.finance.ots.biz.SyncOrderPayToOtsCollectApiDTO;
import com.jdi.isc.order.center.api.parcel.PurchaseOrderParcelWriteApiService;
import com.jdi.isc.order.center.api.parcel.biz.ParcelMigrateApiDTO;
import com.jdi.isc.order.center.api.parcel.biz.PurchaseOrderParcelSplitApiDTO;
import com.jdi.isc.order.center.api.waybill.biz.OrderWaybillSendApiDTO;
import com.jdi.isc.order.center.domain.enums.YnEnum;
import com.jdi.isc.order.center.domain.finace.biz.OrderPayMsgVO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.domain.parcel.biz.PurchaseOrderParcelSplitVO;
import com.jdi.isc.order.center.domain.parcel.po.ParcelDetailPO;
import com.jdi.isc.order.center.domain.parcel.po.ParcelPO;
import com.jdi.isc.order.center.domain.parcel.po.PurchaseOrderParcelDetailPO;
import com.jdi.isc.order.center.domain.parcel.po.PurchaseOrderParcelPO;
import com.jdi.isc.order.center.domain.waybill.biz.OrderWaybillVO;
import com.jdi.isc.order.center.domain.waybill.po.OrderWaybillPO;
import com.jdi.isc.order.center.service.adapter.mapstruct.order.OrderConvert;
import com.jdi.isc.order.center.service.adapter.mapstruct.parcel.PurchaseOrderParcelConvert;
import com.jdi.isc.order.center.service.adapter.mapstruct.waybill.OrderWaybillConvert;
import com.jdi.isc.order.center.service.atomic.parcel.*;
import com.jdi.isc.order.center.service.manage.parcel.ParcelManageService;
import com.jdi.isc.order.center.service.manage.parcel.PurchaseOrderParcelManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description: 包裹api服务实现
 * @Author: zhaojianguo21
 * @Date: 2024/06/17 11:27
 **/

@Slf4j
@Service
public class PurchaseOrderParcelWriteApiServiceImpl implements PurchaseOrderParcelWriteApiService {

    @Resource
    private PurchaseOrderParcelManageService purchaseOrderParcelManageService;

    @Resource
    private PurchaseOrderParcelAtomicService purchaseOrderParcelAtomicService;
    @Resource
    private PurchaseOrderParcelDetailAtomicService purchaseOrderParcelDetailAtomicService;

    @Resource
    private ParcelAtomicService parcelAtomicService;
    @Resource
    private ParcelDetailAtomicService parcelDetailAtomicService;
    @Resource
    private ParcelManageService parcelManageService;

    @Override
    public DataResponse<PurchaseOrderParcelSplitApiDTO.Response> split(PurchaseOrderParcelSplitApiDTO.Request input) {
        log.info("split, input={}", JSONObject.toJSONString(input));
        PurchaseOrderParcelSplitVO.Request reqVo = PurchaseOrderParcelConvert.INSTANCE.splitApiDtoReq2Vo(input);
        DataResponse<PurchaseOrderParcelSplitApiDTO.Response> response = purchaseOrderParcelManageService.split(reqVo);
        return response;
    }

    @ToolKit
    @Override
    public DataResponse<ParcelMigrateApiDTO.Response> migrateData(ParcelMigrateApiDTO.Request input) {
        Map<String, DataResponse> syncResult = new HashMap<>();

        Set<Long> failIds = new HashSet<>();
        Set<Long> existsParcelIds = new HashSet<>();
        Set<Long> noParcelDetailIds = new HashSet<>();
        for(Long id: input.getIds()){
            try {
                PurchaseOrderParcelPO purchaseOrderParcelPO = purchaseOrderParcelAtomicService.getValidById(id);
                if (null==purchaseOrderParcelPO){
                    log.warn("migrateData PurchaseOrderParcelPO null, id={}", id);
                    failIds.add(id);
                    syncResult.put(id.toString(), DataResponse.error("id不存在"));
                    continue;
                }

                LambdaQueryWrapper<ParcelPO> ParcelPoWrapper = Wrappers.<ParcelPO>lambdaQuery()
                        .eq(ParcelPO::getParcelCode, purchaseOrderParcelPO.getParcelId());
                ParcelPO parcelPO = parcelAtomicService.getBaseMapper().selectOne(ParcelPoWrapper);
                if (parcelPO!=null){
                    log.warn("migrateData ParcelPO exist, id={}", id);
                    existsParcelIds.add(id);
                    syncResult.put(id.toString(), DataResponse.error("已存在，id="+id+", parcel="+purchaseOrderParcelPO.getParcelId()));
                    continue;
                }

                List<PurchaseOrderParcelDetailPO> purchaseOrderParcelDetailPOList = purchaseOrderParcelDetailAtomicService.queryByParcelId(purchaseOrderParcelPO.getParcelId());
                if (CollectionUtils.isEmpty(purchaseOrderParcelDetailPOList)){
                    log.warn("migrateData purchaseOrderParcelDetailPOList empty, id={}", id);
                    noParcelDetailIds.add(id);
                    syncResult.put(id.toString(), DataResponse.error("不存在包裹明细，id="+id+", parcel="+purchaseOrderParcelPO.getParcelId()));
                    continue;
                }

                // 保存数据
                parcelPO = PurchaseOrderParcelConvert.INSTANCE.oldPo2ParcelPo(purchaseOrderParcelPO);
                parcelPO.setId(null);
                parcelPO.setDataSource(OrderWaybillDataSourceConstant.INNER);

                List<ParcelDetailPO> parcelDetailPOList = PurchaseOrderParcelConvert.INSTANCE.listOldDetailPo2NewPo(purchaseOrderParcelDetailPOList);
                parcelDetailPOList.forEach(parcelDetailPO -> parcelDetailPO.setId(null));

                DataResponse dataResponse = parcelManageService.saveParcel(parcelPO, parcelDetailPOList);
                syncResult.put(id.toString(), dataResponse);
                if (null==dataResponse || !dataResponse.getSuccess()){
                    log.warn("migrateData saveParcel fail, id={}", id);
                    failIds.add(id);
                }
            } catch (Exception e) {
                failIds.add(id);
                syncResult.put(id.toString(), DataResponse.error(e.getMessage()));
                log.error("migrateData exception, id={}", id, e);
            }

        }
        log.info("migrateData, failIds={}, existsParcelIds={}, noParcelDetailIds={}"
                , JSONObject.toJSONString(failIds), JSONObject.toJSONString(existsParcelIds), JSONObject.toJSONString(noParcelDetailIds));

        ParcelMigrateApiDTO.Response response = new ParcelMigrateApiDTO.Response();
        response.setSyncResult(syncResult);
        return DataResponse.success(response);
    }
}
