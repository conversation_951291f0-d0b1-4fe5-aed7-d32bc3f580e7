package com.jdi.isc.order.center.service.protocol.jsf.opt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.opt.OrderOperateApiService;
import com.jdi.isc.order.center.api.opt.biz.OrderOptDTO;
import com.jdi.isc.order.center.api.opt.biz.OrderOptPageDTO;
import com.jdi.isc.order.center.api.opt.biz.OrderOptSaveDTO;
import com.jdi.isc.order.center.service.protocol.jsf.opt.purchase.OrderOperateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Date 2025/4/8
 * @Description 履约单运维接口
 */
@Service
@Slf4j
public class OrderOperateApiServiceImpl implements OrderOperateApiService {

    @Autowired
    private Map<String, OrderOperateService> orderOperateServiceMap;

    @Autowired
    private OrderOperateService orderOperateService;

    private static final Map<String, String> serviceMapping = new HashMap<>();
    static {
        serviceMapping.put("2-201", "purchaseOrderPriceOperateService");
        serviceMapping.put("1-101", "orderCancelOperateService");
        serviceMapping.put("2-101", "purchaseOrderCancelOperateService");
    }

    @Override
    public DataResponse<Boolean> operate(OrderOptSaveDTO saveDTO) {
        DataResponse<Boolean> result = null;
        try {
            OrderOperateService service = getService(saveDTO);
            if (Objects.isNull(service)) {
                return DataResponse.buildError("不允许此操作！");
            }
            result = service.operate(saveDTO);
        } catch (Exception e) {
            log.error("OrderOperateApiServiceImpl.operate param:{},e:", JSON.toJSONString(saveDTO),e);
            result = DataResponse.buildError("操作失败");
        }finally {
            log.info("OrderOperateApiServiceImpl.operate param:{},result:{}", JSONObject.toJSONString(saveDTO),JSONObject.toJSONString(result));
        }
        return result;

    }

    @Override
    public DataResponse<OrderOptDTO> detail(OrderOptDTO dto) {
        OrderOptDTO detail = orderOperateService.detail(dto);
        return DataResponse.success(detail);
    }

    @Override
    public DataResponse<PageInfo<OrderOptDTO>> page(OrderOptPageDTO pageDTO) {
        PageInfo<OrderOptDTO> pageInfo= orderOperateService.page(pageDTO);
        return DataResponse.success(pageInfo);
    }

    private OrderOperateService getService(OrderOptSaveDTO saveDTO) {
        String busType = String.format("%s-%s", saveDTO.getBizType(), saveDTO.getOptType());
        String serviceName = serviceMapping.get(busType);
        return orderOperateServiceMap.get(serviceName);
    }

}
