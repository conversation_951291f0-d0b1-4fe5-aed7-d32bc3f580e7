package com.jdi.isc.order.center.service.protocol.jsf.xbp;

import com.jd.xbp.jmq4.data.Mq4FieldBO;
import com.jd.xbp.jmq4.data.Mq4TicketBO;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.xbp.OrderXbpManageApiService;
import com.jdi.isc.order.center.api.xbp.biz.ApproveXbpOrderCompletedApiReq;
import com.jdi.isc.order.center.common.utils.JsonUtil;
import com.jdi.isc.order.center.domain.enums.ToolTaskStatusEnum;
import com.jdi.isc.order.center.rpc.xbp.XbpTicketRpcService;
import com.jdi.isc.order.center.service.manage.purchaseOrder.SettlementPurchaseService;
import com.jdi.isc.order.center.service.manage.xbp.OrderInnerConfirmXbpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderXbpManageApiServiceImpl implements OrderXbpManageApiService {

    @Autowired
    private OrderInnerConfirmXbpService orderInnerConfirmXbpService;


    @Resource
    SettlementPurchaseService settlementPurchaseService;

    @Autowired
    private XbpTicketRpcService xbpTicketRpcService;

    @ToolKit(exceptionWrap = true)
    @Override
    public DataResponse<Map<Long, Boolean>> queryOrderInnerConfirmXbpStatus(Set<Long> orderIds) {
        Map<Long, Boolean> map = orderIds.stream().collect(Collectors.toMap(orderId -> orderId, orderId -> Boolean.TRUE));
        return DataResponse.success(map);
    }

    @ToolKit(exceptionWrap = true)
    @Override
    public DataResponse<Integer> approveXbpOrderCompleted(ApproveXbpOrderCompletedApiReq approveXbpOrderCompletedApiReq) {
        DataResponse<Integer> b=orderInnerConfirmXbpService.approveXbpOrderCompleted(approveXbpOrderCompletedApiReq);
        return b;
    }




    /**
     * 将MQ4消息转换为工具任务处理
     * @param mq4FieldReq MQ4字段请求字符串，需包含业务类型和采购单号等信息
     * @param auditStatus 审核状态，用于判断是否执行特定业务逻辑
     * @return 返回处理结果，包含成功标志或错误信息
     */
    @Override
    public DataResponse<Boolean> xbpMessageToToolTask(String mq4FieldReq, Integer auditStatus) {

        final String bizType="业务类型";
        final String purchaseOrderId="采购单号";

        log.info("xbpMessageToToolTask#mq4FieldReq:{},auditStatus{}",mq4FieldReq,auditStatus);

        if(!Objects.equals(auditStatus, ToolTaskStatusEnum.PASS.getCode())){
            log.warn("审批未通过xbpMessageToToolTask#auditStatus{}",auditStatus);
            return DataResponse.error("审批未通过");
        }

//        DataResponse<Ticket> ticketDataResponse = xbpTicketRpcService.get(ticketId);
//        log.info("xbpMessageToToolTask#ticketDataResponse:{}",ticketDataResponse);
//
//        if(!ticketDataResponse.getSuccess()){
//            log.warn("获取工单信息失败xbpMessageToToolTask#ticketId{}",ticketId);
//            return DataResponse.error("获取工单信息失败");
//        }

        Mq4TicketBO mq4TicketBO = JsonUtil.getJavaObject(mq4FieldReq, Mq4TicketBO.class);

        List<Mq4FieldBO> applicationInfo = mq4TicketBO.getMainFields();

        if(!CollectionUtils.isNotEmpty(applicationInfo)){
            log.warn("获取工单信息失败,applicationInfo is null xbpMessageToToolTask#ticketId{}",mq4TicketBO);
            return DataResponse.error("获取工单信息失败");
        }

        Map<String, Mq4FieldBO> applicationInfoMap = applicationInfo.stream().collect(Collectors.toMap(Mq4FieldBO::getTitle, Function.identity()));

        Mq4FieldBO bizTypeInfo = applicationInfoMap.get(bizType);
        String itemValue = bizTypeInfo.getValue();
        log.info("xbpMessageToToolTask#itemValue:{}",itemValue);

        switch (itemValue){
            case "结算流水重新推":
               Mq4FieldBO purchaseOrderIdInfo = applicationInfoMap.get(purchaseOrderId);
               return settlementPurchaseService.repeatGeneratePurchaseStream(purchaseOrderIdInfo.getValue());
            case "采购单不推结算流水打标":
                Mq4FieldBO purchaseOrderIdInfos = applicationInfoMap.get(purchaseOrderId);
                String purchaseOrderIds = purchaseOrderIdInfos.getValue();
                Set<String> purchaseOrderIdSet = Arrays.stream(purchaseOrderIds.split(","))
                        .map(String::trim) // 去除每个元素的空格
                        .collect(Collectors.toSet()); // 转换为Set集合
                //审批流任务id
                String updater=mq4TicketBO.getId().toString();
                return settlementPurchaseService.markPurchaseStream(purchaseOrderIdSet,updater);
            case "其他":
                return DataResponse.error("其他业务类型不支持");
            default:
                return DataResponse.error("未找到对应的业务类型");
        }

    }


}
