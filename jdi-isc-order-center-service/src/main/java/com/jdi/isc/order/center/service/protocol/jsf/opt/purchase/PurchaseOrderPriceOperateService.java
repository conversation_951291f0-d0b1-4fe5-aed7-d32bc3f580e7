package com.jdi.isc.order.center.service.protocol.jsf.opt.purchase;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.constants.PurchaseOrderStatusConstant;
import com.jdi.isc.order.center.api.opt.biz.OrderOptSaveDTO;
import com.jdi.isc.order.center.common.utils.DateUtil;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.ChangePurchaseOrderReqVO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.ChangePurchaseOrderRespVO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.service.atomic.purchaseOrder.PurchaseOrderAtomicService;
import com.jdi.isc.order.center.service.manage.purchaseOrder.impl.LocalPurchaseOrderWriteServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * 采购单运维基类
 */
@Slf4j
@Service
public class PurchaseOrderPriceOperateService extends OrderOperateService {

    @Autowired
    private PurchaseOrderAtomicService purchaseOrderAtomicService;

    @Autowired
    private LocalPurchaseOrderWriteServiceImpl localPurchaseOrderWriteServiceImpl;


    public DataResponse<Boolean> operate(OrderOptSaveDTO saveDTO){

        String bizId = saveDTO.getBizId();
        PurchaseOrderPO purchaseOrder = purchaseOrderAtomicService.queryValidPurchaseOrderByPoId(bizId);
        log.info("PurchaseOrderPriceOperateService.operate purchaseOrder:{}", JSONObject.toJSONString(purchaseOrder));
        if (Objects.isNull(purchaseOrder)) {
            log.error("PurchaseOrderPriceOperateService.operate purchaseOrder is null param:{}", JSONObject.toJSONString(saveDTO));
            return DataResponse.buildError("采购单不存在");
        }

        Integer purchaseOrderStatus = purchaseOrder.getPurchaseOrderStatus();
        //已入仓及以后 校验财务邮件截图不能为空
        if (enterWarehouse().contains(purchaseOrderStatus) && StringUtils.isBlank(saveDTO.getAttach1Url())) {
            return DataResponse.buildError("财务邮件截图不能为空");
        }

        //已完成 结算邮件截图不能为空
        if (PurchaseOrderStatusConstant.FINISH.getStatus().equals(purchaseOrderStatus) && StringUtils.isBlank(saveDTO.getAttach2Url())) {
            return DataResponse.buildError("结算邮件截图不能为空");
        }

        //更新订单采购价
        log.info("PurchaseOrderPriceOperateService.operate param:{}", JSON.toJSONString(saveDTO));
        DataResponse<ChangePurchaseOrderRespVO> dataResponse = updatePurchasePrice(saveDTO);
        log.info("PurchaseOrderPriceOperateService.operate result:{}", JSON.toJSONString(dataResponse));
        if (!Boolean.TRUE.equals(dataResponse.getSuccess())) {
            return DataResponse.buildError(dataResponse.getMessage());
        }

        //更新发票价格 TODO
        //updateInvoicePrice();
        //保存日志
        super.saveOptLog(saveDTO, DataResponse.success());
        return DataResponse.success();
    }


    private DataResponse<ChangePurchaseOrderRespVO> updatePurchasePrice(OrderOptSaveDTO saveDTO){

        ChangePurchaseOrderReqVO createPurchaseOrderReqVO = new ChangePurchaseOrderReqVO();
        createPurchaseOrderReqVO.setPurchaseOrderId(saveDTO.getBizId());
        createPurchaseOrderReqVO.setUserIp(saveDTO.getUserIp());
        createPurchaseOrderReqVO.setUpdate(true);
        createPurchaseOrderReqVO.setOperator(saveDTO.getApplicant());
        return localPurchaseOrderWriteServiceImpl.updatePurchaseOrder4changePrice(createPurchaseOrderReqVO);
    }



    private Set<Integer> enterWarehouse() {
        Set<Integer> status = new HashSet<>();
        status.add(PurchaseOrderStatusConstant.INPUT_CONSOLIDATION_WH.getStatus());
        status.add(PurchaseOrderStatusConstant.INPUT_WAREHOUSE_PART.getStatus());
        status.add(PurchaseOrderStatusConstant.INPUT_WAREHOUSE.getStatus());
        status.add(PurchaseOrderStatusConstant.FINISH.getStatus());
        return status;
    }
}
