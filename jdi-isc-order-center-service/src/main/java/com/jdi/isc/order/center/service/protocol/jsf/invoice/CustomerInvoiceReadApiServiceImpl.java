package com.jdi.isc.order.center.service.protocol.jsf.invoice;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.finance.customerInvoice.CustomerInvoiceReadApiService;
import com.jdi.isc.order.center.api.finance.customerInvoice.req.CustomerInvoiceDetailReqDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.req.CustomerInvoiceListReqDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.req.CustomerInvoicePageReqDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceApplyDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.OrderInvoiceXmlParseDTO;
import com.jdi.isc.order.center.domain.invoice.customer.vo.CustomerInvoiceApplyVO;
import com.jdi.isc.order.center.service.manage.invoice.CustomerInvoiceReadService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class CustomerInvoiceReadApiServiceImpl implements CustomerInvoiceReadApiService {

    @Autowired
    private CustomerInvoiceReadService customerInvoiceReadService;



    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<CustomerInvoiceApplyDTO>> invoicePage(CustomerInvoicePageReqDTO req) {
        return customerInvoiceReadService.invoicePage(req);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<CustomerInvoiceApplyDTO> invoiceDetail(CustomerInvoiceDetailReqDTO req) {
        return customerInvoiceReadService.invoiceDetail(req);
    }

    @Override
    public DataResponse<OrderInvoiceXmlParseDTO> parseXml(CustomerInvoiceDetailReqDTO req) {
        if (Objects.isNull(req) || StringUtils.isBlank(req.getUrl())) {
            return DataResponse.error("url is null");
        }
        return customerInvoiceReadService.parseXml(req);
    }

    @Override
    public DataResponse<List<CustomerInvoiceApplyDTO>> queryInvoiceList(CustomerInvoiceListReqDTO req) {
        return customerInvoiceReadService.queryInvoiceList(req);
    }

    @Override
    public DataResponse<Map<Long, CustomerInvoiceApplyDTO>> queryInvoiceCount4shipped(CustomerInvoiceListReqDTO req){
        return customerInvoiceReadService.queryInvoiceCount4shipped(req);
    }
}
