<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>spring-boot-starter-parent</artifactId>
		<groupId>org.springframework.boot</groupId>
		<version>2.7.18</version>
	</parent>
	<groupId>com.jd.international.soa</groupId>
	<artifactId>international-soa</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>pom</packaging>
	<name>international-soa</name>
	<description>国际站soa</description>
	<modules>
		<module>international-soa-common</module>
		<module>international-soa-service</module>
		<module>international-soa-dao</module>
		<module>international-soa-domain</module>
		<module>international-soa-rpc</module>
		<module>international-soa-provider</module>
		<module>international-order-sdk</module>
		<module>international-wares-sdk</module>
		<module>international-common-sdk</module>
		<module>international-approval-sdk</module>
		<module>international-soa-base</module>
	</modules>
	<properties>
		<java.version>1.8</java.version>
		<ajdv.version>0.1</ajdv.version>
		<ducc.version>1.4.1</ducc.version>
		<mybatis.version>2.2.0</mybatis.version>
		<com.jd.configsec.version>1.0.3-SNAPSHOT</com.jd.configsec.version>
		<org.springframework.cloud.zuul.version>2.2.10.RELEASE</org.springframework.cloud.zuul.version>
		<lombok.version>1.18.30</lombok.version>
		<log4j.version>2.18.0-jdsec.rc2</log4j.version>
		<com.jd.ump.version>6.2.13-HOTFIX-T1</com.jd.ump.version>
		<com.jd.jim.verison>2.1.11</com.jd.jim.verison>
		<fastjson.version>1.2.83-jdsec.rc1</fastjson.version>
		<fastjson2.verison>2.0.17</fastjson2.verison>
		<jsf.version>1.7.6-HOTFIX-T2</jsf.version>
		<com.jd.configsec.version>1.0.3-SNAPSHOT</com.jd.configsec.version>
		<user-query-sdk-version>0.0.1-SNAPSHOT</user-query-sdk-version>
		<commons-lang3.version>3.1</commons-lang3.version>
		<com.jd.mail.version>0.0.1-SNAPSHOT</com.jd.mail.version>
		<commons-collections4.version>4.1</commons-collections4.version>
		<component-crs-proxy-rpc.version>2.0.27</component-crs-proxy-rpc.version>
		<jdi-product-center-core-boost-sdk.version>1.0.1-SNAPSHOT</jdi-product-center-core-boost-sdk.version>
		<b2b-promotion-engine-sdk.version>1.0.1-SNAPSHOT</b2b-promotion-engine-sdk.version>
		<b2b-price-common.version>2.0.5</b2b-price-common.version>
		<b2b-price-biz-sdk.version>1.1.21</b2b-price-biz-sdk.version>
		<user.sdk.version>1.0.6-SNAPSHOT</user.sdk.version>
		<ka.user.soa.service.version>2.1.2-SNAPSHOT</ka.user.soa.service.version>
		<vtDrive.version>1.2.19</vtDrive.version>
		<wanfang.version>0.1.6.1-SNAPSHOT</wanfang.version>
		<gms.component.version>2.0.24</gms.component.version>
		<category.soa.dubbo.client.version>1.2.3-SNAPSHOT</category.soa.dubbo.client.version>
		<gms.common.version>1.1.8-SNAPSHOT</gms.common.version>
		<hutool.version>5.3.5</hutool.version>
		<ognl.version>3.1.15</ognl.version>
		<html2pdf.version>4.0.3</html2pdf.version>
		<itextpdf.verison>5.5.12</itextpdf.verison>
		<itext-asian.version>5.2.0</itext-asian.version>
		<sdd_market-sdk.verison>3.0-SNAPSHOT</sdd_market-sdk.verison>
		<flying-saucer-pdf.verison>9.1.6</flying-saucer-pdf.verison>
		<b2b-user-svr-sdk.version>0.2.2</b2b-user-svr-sdk.version>
		<promotion.sdk.version>1.9.9</promotion.sdk.version>
		<ept-warecenter-client.version>2.0.0-SNAPSHOT</ept-warecenter-client.version>
		<pfinder.version>1.2.1-FINAL</pfinder.version>
		<ept.order.version>1.0.32-SNAPSHOT</ept.order.version>
		<iop.client.version>0.0.17-SNAPSHOT</iop.client.version>
		<biz.user.sdk.version>1.6.9-SNAPSHOT</biz.user.sdk.version>
		<jmq.client.all>4.2.7.RC1</jmq.client.all>
		<jmq.client.spring.version>2.3.3-RC2</jmq.client.spring.version>
		<jmq.client.springboot.version>2.3.2.2</jmq.client.springboot.version>
		<binlake.wave.client.version>1.1.1</binlake.wave.client.version>
		<org.mapstruct.version>1.5.3.Final</org.mapstruct.version>

		<!-- 没继承父pom（international-soa）的module 开始。其它子module都仅限于本服务使用，都继承了父pom，使用统一的版本号project.version -->
		<international.base.version>1.1.0${lib.env}</international.base.version>
		<international.approval.sdk.version>0.0.4${lib.env}</international.approval.sdk.version>
		<international.common.sdk.version>1.1.14${lib.env}</international.common.sdk.version>
		<international.order.sdk.version>0.0.25${lib.env}</international.order.sdk.version>
		<international.wares.sdk.version>0.0.4${lib.env}</international.wares.sdk.version>
		<!-- 没继承父pom（international-soa）的module 结束 -->

        <isc.order.api.version>0.0.1-SNAPSHOT</isc.order.api.version>
        <order.center.version>0.0.45-SNAPSHOT</order.center.version>

		<isc.aggregate.read.wisp.api>1.1.4${lib.env}</isc.aggregate.read.wisp.api>
		<tdeclient.version>3.0.4-SNAPSHOT</tdeclient.version>
		<aces.springclient.version>3.0.2</aces.springclient.version>
		<aws.s3.version>1.11.490</aws.s3.version>
		<com.alibaba.easyexcel.version>3.3.2</com.alibaba.easyexcel.version>
		<project.international.workflow.version>0.0.10-SNAPSHOT</project.international.workflow.version>
		<jackson.version>2.15.2</jackson.version>
		<lingala.zip4j.version>1.3.2</lingala.zip4j.version>
		<itextpdf.version>5.5.13</itextpdf.version>
		<jss-sdk-java.version>1.4.7-SNAPSHOT</jss-sdk-java.version>
		<itext.version>7.1.6</itext.version>
		<jdi.isc.aggregate.read.version>1.1.4${lib.env}</jdi.isc.aggregate.read.version>
		<com.jd.xbp.jsf.api.version>1.2.5</com.jd.xbp.jsf.api.version>
		<com.jd.xbp.jmq4.data.version>1.0.12</com.jd.xbp.jmq4.data.version>
		<isc.product.soa.version>1.1.39${lib.env}</isc.product.soa.version>
		<jdi.isc.library.common.version>2.2.4-SNAPSHOT</jdi.isc.library.common.version>
		<jdi.isc.i18n.datasource.version>2.2.7-SNAPSHOT</jdi.isc.i18n.datasource.version>
        <data.elasticsearch.version>4.4.0</data.elasticsearch.version>
        <jakarta.json-api.version>2.0.1</jakarta.json-api.version>
        <isc.product.soa.price.version>1.1.2${lib.env}</isc.product.soa.price.version>
        <isc.product.soa.stock.version>1.1.3${lib.env}</isc.product.soa.stock.version>
        <jp.strategy.version>2.1.85-SNAPSHOT</jp.strategy.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency> <!-- 引入log4j2依赖 -->
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-jul</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.ump</groupId>
            <artifactId>profiler</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>${log4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jul</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>${fastjson2.verison}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.30</version>
        </dependency>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf</artifactId>
            <version>${jsf.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>${commons-collections4.version}</version>
        </dependency>

        <!-- jimdb -->
        <dependency>
            <groupId>com.jd.jim.cli</groupId>
            <artifactId>jim-cli-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.security.codesec</groupId>
            <artifactId>ajdv</artifactId>
            <version>${ajdv.version}</version>
        </dependency>
        <!--巴别塔-->
        <dependency>
            <groupId>com.jd.ka</groupId>
            <artifactId>buser-sdk</artifactId>
        </dependency>
        <!-- 自定义邮件发送接口 -->
        <dependency>
            <groupId>com.jd.leo.mail</groupId>
            <artifactId>product_produce-rpc-service</artifactId>
        </dependency>
        <!-- 用户组接口密码修改start -->
        <dependency>
            <groupId>com.jd.user.sdk</groupId>
            <artifactId>user-sdk-export</artifactId>
        </dependency>
        <!-- 用户组接口密码修改end -->
        <dependency>
            <groupId>io.vitess.driver</groupId>
            <artifactId>vtdriver</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <dependency>
            <groupId>ognl</groupId>
            <artifactId>ognl</artifactId>
            <version>${ognl.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jd.laf.config</groupId>
            <artifactId>laf-config-client-jd-springboot-starter</artifactId>
            <version>${ducc.version}</version>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>flying-saucer-pdf</artifactId>
            <version>${flying-saucer-pdf.verison}</version>
        </dependency>
        <!-- 装吧css-->
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>sdd_market-sdk</artifactId>
            <version>${sdd_market-sdk.verison}</version>
        </dependency>
        <!--  EPT商品发布服务  -->
        <dependency>
            <groupId>com.jd.ept</groupId>
            <artifactId>ept-warecenter-client</artifactId>
            <version>${ept-warecenter-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.pfinder</groupId>
            <artifactId>pfinder-profiler-sdk</artifactId>
            <version>${pfinder.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.biz</groupId>
            <artifactId>mro-ka-workflow-sdk</artifactId>
            <version>${project.international.workflow.version}</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- 解决依赖冲突：统一版本管理 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>19.0</version>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.20.0-GA</version>
            </dependency>
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>traceholder</artifactId>
                <version>1.0.1</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-price-sdk</artifactId>
                <version>${isc.product.soa.price.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-stock-sdk</artifactId>
                <version>${isc.product.soa.stock.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.international.soa</groupId>
                <artifactId>international-common-sdk</artifactId>
                <version>${international.common.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>io.vitess.driver</groupId>
                <artifactId>vtdriver</artifactId>
                <version>${vtDrive.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>bcprov-jdk15on</artifactId>
                        <groupId>org.bouncycastle</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 用户组接口密码修改start -->
            <dependency>
                <groupId>com.jd.user.sdk</groupId>
                <artifactId>user-sdk-export</artifactId>
                <version>${user.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd</groupId>
                        <artifactId>jsf</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 用户组接口密码修改end -->
            <dependency>
                <groupId>com.jd.leo.mail</groupId>
                <artifactId>product_produce-rpc-service</artifactId>
                <version>${com.jd.mail.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd</groupId>
                        <artifactId>jsf</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--巴别塔-->
            <dependency>
                <groupId>com.jd.ka</groupId>
                <artifactId>buser-sdk</artifactId>
                <version>0.5.3-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context-support</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.common</groupId>
                        <artifactId>jd-common-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.b2b</groupId>
                        <artifactId>b2b-user-common-sdk</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.aspectj</groupId>
                        <artifactId>aspectjweaver</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- jimdb -->
            <dependency>
                <groupId>com.jd.jim.cli</groupId>
                <artifactId>jim-cli-spring</artifactId>
                <version>${com.jd.jim.verison}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>unitrouter</artifactId>
                        <groupId>com.jd</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j-api</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j-core</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j-api</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd.ump</groupId>
                <artifactId>profiler</artifactId>
                <version>${com.jd.ump.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-jul</artifactId>
                <version>${log4j.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j-api</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-to-slf4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>jsf</artifactId>
                <version>${jsf.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--主站商祥接口-->
            <dependency>
                <groupId>gms.jd.component.crs.proxy</groupId>
                <artifactId>component-crs-proxy-rpc</artifactId>
                <version>${component-crs-proxy-rpc.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 促销接口 -->
            <dependency>
                <groupId>com.jd.b2b.promotion</groupId>
                <artifactId>b2b-promotion-engine-sdk</artifactId>
                <version>${b2b-promotion-engine-sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.jim.cli</groupId>
                        <artifactId>jim-cli-jedis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-beans</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-slf4j-impl</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-pool2</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.jim.cli</groupId>
                        <artifactId>jim-cli-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-collections4</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-1.2-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-jcl</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-slf4j-impl</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-web</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd.b2b.price</groupId>
                <artifactId>b2b-price-common</artifactId>
                <version>${b2b-price-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.jim.cli</groupId>
                        <artifactId>jim-cli-spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.jim.cli</groupId>
                        <artifactId>jim-cli-compat</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.jim.cli</groupId>
                        <artifactId>jim-cli-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.javassist</groupId>
                        <artifactId>javassist</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-test</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-codec</groupId>
                        <artifactId>commons-codec</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-beans</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 价格平台 -->
            <dependency>
                <groupId>com.jd.b2b.price</groupId>
                <artifactId>b2b-price-biz-sdk</artifactId>
                <version>${b2b-price-biz-sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-collections4</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 商品组公共包 -->
            <dependency>
                <groupId>jd.gms</groupId>
                <artifactId>gms-common</artifactId>
                <version>${gms.common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-codec</groupId>
                        <artifactId>commons-codec</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-lang</groupId>
                        <artifactId>commons-lang</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.validation</groupId>
                        <artifactId>validation-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 促销服务 -->
            <dependency>
                <groupId>com.jd.promotion</groupId>
                <artifactId>promotion-sdk</artifactId>
                <version>${promotion.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.common</groupId>
                        <artifactId>jd-common-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-collections</artifactId>
                        <groupId>commons-collections</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--			iop订单服务-->
            <dependency>
                <groupId>com.jd.ka.gpt.soa</groupId>
                <artifactId>gpt-soa-client</artifactId>
                <version>${iop.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.wanfang</groupId>
                        <artifactId>wanfang-support-soa-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--			ept订单服务-->
            <dependency>
                <groupId>com.jd.ept</groupId>
                <artifactId>ept-order-sdk</artifactId>
                <version>${ept.order.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.biz</groupId>
                <artifactId>jd-biz-user-soa-sdk</artifactId>
                <version>${biz.user.sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.jmq</groupId>
                <artifactId>jmq2-client-springboot-starter</artifactId>
                <version>${jmq.client.springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd.binlake</groupId>
                <artifactId>binlake-wave.client</artifactId>
                <version>${binlake.wave.client.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <!--工业国际订单中心-->
            <dependency>
                <groupId>com.jdi.isc.order.center</groupId>
                <artifactId>jdi-isc-order-center-api</artifactId>
                <version>${order.center.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdi.isc.aggregate.read</groupId>
                <artifactId>jdi-isc-aggregate-read-wisp-api</artifactId>
                <version>${isc.aggregate.read.wisp.api}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>tdeclient</artifactId>
                <version>${tdeclient.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>aces-springclient</artifactId>
                <version>${aces.springclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.security</groupId>
                        <artifactId>tdeclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.security</groupId>
                        <artifactId>tdecommon</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws.s3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${com.alibaba.easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>net.lingala.zip4j</groupId>
                <artifactId>zip4j</artifactId>
                <version>${lingala.zip4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.jss</groupId>
                <artifactId>jss-sdk-java</artifactId>
                <version>${jss-sdk-java.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.ump</groupId>
                        <artifactId>profiler</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-codec</groupId>
                        <artifactId>commons-codec</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-lang</groupId>
                        <artifactId>commons-lang</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>joda-time</groupId>
                        <artifactId>joda-time</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itextpdf</artifactId>
                <version>${itextpdf.verison}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.aggregate.read</groupId>
                <artifactId>jdi-isc-aggregate-read-api</artifactId>
                <version>${jdi.isc.aggregate.read.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.xbp</groupId>
                <artifactId>xbp-jmq4-data</artifactId>
                <version>${com.jd.xbp.jmq4.data.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.xbp</groupId>
                <artifactId>jsf-api</artifactId>
                <version>${com.jd.xbp.jsf.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-api</artifactId>
                <version>${isc.product.soa.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.library</groupId>
                <artifactId>jdi-isc-library-common</artifactId>
                <version>${jdi.isc.library.common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.library</groupId>
                <artifactId>jdi-isc-library-i18n-datasource</artifactId>
                <version>${jdi.isc.i18n.datasource.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-elasticsearch</artifactId>
                <version>${data.elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.json</groupId>
                <artifactId>jakarta.json-api</artifactId>
                <version>${jakarta.json-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.jp</groupId>
                <artifactId>strategy-center-sdk</artifactId>
                <version>${jp.strategy.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <lib.env>-test-SNAPSHOT</lib.env>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <lib.env>-test-SNAPSHOT</lib.env>
            </properties>
        </profile>
        <profile>
            <id>pre</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profile.env>pre</profile.env>
                <lib.env>-SNAPSHOT</lib.env>
            </properties>
        </profile>
        <profile>
            <id>pro</id>
            <properties>
                <profile.env>pro</profile.env>
                <lib.env></lib.env>
            </properties>
        </profile>
    </profiles>
    <build>


		<plugins>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.11.0</version>
				<configuration>
					<source>21</source>
					<target>21</target>
					<encoding>utf-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.3.1</version>
				<configuration>
					<encoding>utf-8</encoding>
				</configuration>
			</plugin>
			<!-- Source attach plugin -->
			<plugin>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.3.0</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.2.2</version>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>3.1.0</version>
			</plugin>
		</plugins>
	</build>

    <distributionManagement>
        <repository>
            <id>libs-releases-local</id>
            <name>Release Repository</name>
            <url>http://artifactory.jd.com/libs-releases-local</url>
        </repository>
        <snapshotRepository>
            <id>libs-snapshots-local</id>
            <name>Snapshot Repository</name>
            <url>http://artifactory.jd.com/libs-snapshots-local</url>
        </snapshotRepository>
    </distributionManagement>

</project>
