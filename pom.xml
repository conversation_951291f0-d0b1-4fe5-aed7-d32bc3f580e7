<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.jdi.dependencies</groupId>
        <artifactId>jdi-dependencies-parent</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <groupId>com.jdi.isc.aggregate.read</groupId>
    <artifactId>jdi-isc-aggregate-read</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>jdi-isc-aggregate-read</name>
    <packaging>pom</packaging>

    <modules>
        <module>jdi-isc-aggregate-read-common</module>
        <module>jdi-isc-aggregate-read-domain</module>
        <module>jdi-isc-aggregate-read-repository</module>
        <module>jdi-isc-aggregate-read-service</module>
        <module>jdi-isc-aggregate-read-web</module>
        <module>jdi-isc-aggregate-read-rpc</module>
        <module>jdi-isc-aggregate-read-msg</module>
        <module>jdi-isc-aggregate-read-wiop-api</module>
        <module>jdi-isc-aggregate-read-wisp-api</module>
        <module>jdi-isc-aggregate-read-api</module>
    </modules>

    <properties>
        <spring.boot.version>2.7.8</spring.boot.version>
        <druid.version>1.2.11</druid.version>
        <tomcat.version>9.0.82</tomcat.version>
        <servlet.api.version>3.1.0</servlet.api.version>
        <laf.client.version>1.4.1</laf.client.version>
        <xxl.job.core.version>2.2.0</xxl.job.core.version>
        <hutool.version>5.8.5</hutool.version>
        <binlake.wave.client.version>1.1.1</binlake.wave.client.version>
        <jss.sdk.version>1.3.0-SNAPSHOT</jss.sdk.version>
        <unirest.java.version>1.4.9</unirest.java.version>
        <jd.image.common.version>1.0.7-SNAPSHOT</jd.image.common.version>
        <lingala.zip4j.version>1.3.2</lingala.zip4j.version>
        <igc.product.version>0.0.15-SNAPSHOT</igc.product.version>
        <b2b.product.version>2.26.0-SNAPSHOT</b2b.product.version>
        <com.alibaba.easyexcel.version>3.3.2</com.alibaba.easyexcel.version>
        <org.apache.httpclient.version>4.5.5</org.apache.httpclient.version>

<!--        <isc.aggregate.read.wisp.api.version>0.7.14-SNAPSHOT</isc.aggregate.read.wisp.api.version>-->
        <isc.aggregate.read.wisp.api.version>1.1.4${lib.version}</isc.aggregate.read.wisp.api.version>
<!--        <isc.aggregate.read.wiop.api.version>0.0.12-SNAPSHOT</isc.aggregate.read.wiop.api.version>-->
        <isc.aggregate.read.wiop.api.version>1.1.4${lib.version}</isc.aggregate.read.wiop.api.version>
<!--        <isc.aggregate.read.api.version>0.0.25-SNAPSHOT</isc.aggregate.read.api.version>-->
        <isc.aggregate.read.api.version>1.1.4${lib.version}</isc.aggregate.read.api.version>
        <isc.aggregate.read.common.version>0.0.1-SNAPSHOT</isc.aggregate.read.common.version>
        <isc.aggregate.read.domain.version>0.0.1-SNAPSHOT</isc.aggregate.read.domain.version>
        <isc.aggregate.read.repository.version>0.0.1-SNAPSHOT</isc.aggregate.read.repository.version>
        <isc.aggregate.read.rpc.version>0.0.1-SNAPSHOT</isc.aggregate.read.rpc.version>
        <isc.aggregate.read.service.version>0.0.1-SNAPSHOT</isc.aggregate.read.service.version>
        <isc.aggregate.read.web.version>0.0.1-SNAPSHOT</isc.aggregate.read.web.version>
        <spsc.price.version>0.0.1-SNAPSHOT</spsc.price.version>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <mysql.version>8.0.16</mysql.version>
        <component.crs.proxy.verison>2.0.25</component.crs.proxy.verison>
        <xxl.job.core.version>2.2.0</xxl.job.core.version>
        <jd.aggregation.version>1.0.5-SNAPSHOT</jd.aggregation.version>
        <com.jd.gms.relation>0.0.1-SNAPSHOT</com.jd.gms.relation>
        <industry.ops.version>1.6.1-20230815.092715-1</industry.ops.version>
        <dynamic.datasource.relation>3.1.0</dynamic.datasource.relation>
        <vtDrive.version>1.2.20</vtDrive.version>
        <xstream.version>1.4.18</xstream.version>
        <validator.version>6.0.18.Final</validator.version>
        <commons.collections.version>3.2.2</commons.collections.version>
        <jmq.client.all>4.2.7.RC1</jmq.client.all>
        <hibernate-validator.version>6.0.18.Final</hibernate-validator.version>
        <jmq.client.spring.version>2.3.3-RC2</jmq.client.spring.version>
        <imp.operate.version>1.0.4.3-SNAPSHOT</imp.operate.version>
        <imp.price.core.version>1.0.0-SNAPSHOT</imp.price.core.version>
        <jdi.dependencies.version>1.0-SNAPSHOT</jdi.dependencies.version>
        <jmq.client.core.version>2.3.2</jmq.client.core.version>
        <jsf.version>1.7.6-HOTFIX-T2</jsf.version>
        <jdi.common.dependency.version>1.0.2-SNAPSHOT</jdi.common.dependency.version>
        <jd.security.tomcat>1.13.WEBAPP</jd.security.tomcat>
        <jdi.common.frame.starter.version>1.0-SNAPSHOT</jdi.common.frame.starter.version>
        <jdi.common.model.domain.version>2.0.0</jdi.common.model.domain.version>
        <mybatis.plus.version>3.5.2</mybatis.plus.version>
        <aws.java.version>1.11.490</aws.java.version>
        <commons.fileupload.version>1.5</commons.fileupload.version>
        <wanfang.version>0.1.6.1-SNAPSHOT</wanfang.version>
        <laf.client.version>1.4.1</laf.client.version>
        <jmq.version>2.3.2.2</jmq.version>
        <gms.cate.version>1.3.4-releases</gms.cate.version>
        <iop.client.version>0.0.17-SNAPSHOT</iop.client.version>
        <iop.soa.sdk>1.3.0-SNAPSHOT</iop.soa.sdk>
        <iop.gpt.soa.version>0.0.20-SNAPSHOT</iop.gpt.soa.version>
        <aces.springclient.version>3.0.2</aces.springclient.version>
        <tdeclient.version>3.0.4-SNAPSHOT</tdeclient.version>
        <pps.price.version>1.0.46-RELEASES</pps.price.version>
        <fusionds.lib.version>1.5.10</fusionds.lib.version>
        <fms.plough.version>4.1.0-RELEASE</fms.plough.version>
        <jp.strategy.version>2.1.85-SNAPSHOT</jp.strategy.version>
        <ept-warecenter-client.version>1.0.37-SNAPSHOT</ept-warecenter-client.version>
        <state.stock.version>1.7.8-jdk8-SNAPSHOT</state.stock.version>
<!--        <isc.fulfillment.soa.version>0.0.3-SNAPSHOT</isc.fulfillment.soa.version>-->
        <isc.fulfillment.soa.version>1.1.0${lib.version}</isc.fulfillment.soa.version>
<!--        <isc.product.soa.version>${lib.version.pre}.0.37-SNAPSHOT</isc.product.soa.version>-->
        <isc.product.soa.version>1.1.39${lib.version}</isc.product.soa.version>
<!--        <isc.product.soa.price.version>0.0.1-SNAPSHOT</isc.product.soa.price.version>-->
        <isc.product.soa.price.version>1.1.4${lib.version}</isc.product.soa.price.version>
        <isc.library.i18n.version>2.2.7-SNAPSHOT</isc.library.i18n.version>
<!--        <isc.order.center.version>0.0.41-SNAPSHOT</isc.order.center.version>-->
        <isc.order.center.version>1.1.3${lib.version}</isc.order.center.version>
<!--        <isc.product.mku.stock.version>0.0.2-SNAPSHOT</isc.product.mku.stock.version>-->
        <isc.product.mku.stock.version>1.1.2${lib.version}</isc.product.mku.stock.version>

        <org.projectlombok.version>1.18.30</org.projectlombok.version>
        <jakarta.annotation.version>1.3.5</jakarta.annotation.version>
        <jaxb.runtime.version>2.3.9</jaxb.runtime.version>
        <google.guava.version>30.1-jre</google.guava.version>

    </properties>

    <dependencyManagement>

        <dependencies>

            <dependency>
                <artifactId>common-domain</artifactId>
                <groupId>com.jdi.common</groupId>
                <version>${jdi.common.model.domain.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.common</groupId>
                <artifactId>jdi-common-frame-spring-boot-starter</artifactId>
                <version>${jdi.common.frame.starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>jd-security-tomcat</artifactId>
                <version>${jd.security.tomcat}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${servlet.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl.job.core.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.binlake</groupId>
                <artifactId>binlake-wave.client</artifactId>
                <version>${binlake.wave.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mashape.unirest</groupId>
                <artifactId>unirest-java</artifactId>
                <version>${unirest.java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ipd</groupId>
                <artifactId>jss-sdk-java</artifactId>
                <version>${jss.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.image</groupId>
                <artifactId>image-common</artifactId>
                <version>${jd.image.common.version}</version>
            </dependency>

            <dependency>
                <groupId>net.lingala.zip4j</groupId>
                <artifactId>zip4j</artifactId>
                <version>${lingala.zip4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${com.alibaba.easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.aggregate.read</groupId>
                <artifactId>jdi-isc-aggregate-read-wisp-api</artifactId>
                <version>${isc.aggregate.read.wisp.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.aggregate.read</groupId>
                <artifactId>jdi-isc-aggregate-read-wiop-api</artifactId>
                <version>${isc.aggregate.read.wiop.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.aggregate.read</groupId>
                <artifactId>jdi-isc-aggregate-read-api</artifactId>
                <version>${isc.aggregate.read.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.aggregate.read</groupId>
                <artifactId>jdi-isc-aggregate-read-common</artifactId>
                <version>${isc.aggregate.read.common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.aggregate.read</groupId>
                <artifactId>jdi-isc-aggregate-read-domain</artifactId>
                <version>${isc.aggregate.read.domain.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.aggregate.read</groupId>
                <artifactId>jdi-isc-aggregate-read-repository</artifactId>
                <version>${isc.aggregate.read.repository.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.aggregate.read</groupId>
                <artifactId>jdi-isc-aggregate-read-rpc</artifactId>
                <version>${isc.aggregate.read.rpc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.aggregate.read</groupId>
                <artifactId>jdi-isc-aggregate-read-service</artifactId>
                <version>${isc.aggregate.read.service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.aggregate.read</groupId>
                <artifactId>jdi-isc-aggregate-read-web</artifactId>
                <version>${isc.aggregate.read.web.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <dependency>
                <groupId>gms.jd.component.crs.proxy</groupId>
                <artifactId>component-crs-proxy-rpc</artifactId>
                <version>${component.crs.proxy.verison}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic.datasource.relation}</version>
            </dependency>

            <dependency>
                <groupId>io.vitess.driver</groupId>
                <artifactId>vtdriver</artifactId>
                <version>${vtDrive.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>bcprov-jdk15on</artifactId>
                        <groupId>org.bouncycastle</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>${xstream.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${validator.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons.collections.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.joyqueue</groupId>
                <artifactId>jmq-client-all</artifactId>
                <version>${jmq.client.all}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.jmq</groupId>
                <artifactId>jmq-client-spring</artifactId>
                <version>${jmq.client.spring.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.jmq</groupId>
                <artifactId>jmq-client-core</artifactId>
                <version>${jmq.client.core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>jsf</artifactId>
                <version>${jsf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-core</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-catalina</artifactId>
                <version>${tomcat.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-jasper</artifactId>
                <version>${tomcat.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws.java.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.wanfang</groupId>
                <artifactId>wanfang-support-soa-client</artifactId>
                <version>${wanfang.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.laf.config</groupId>
                <artifactId>laf-config-client-jd-springboot-starter</artifactId>
                <version>${laf.client.version}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <artifactId>greatdane.client</artifactId>
                <groupId>component.gms</groupId>
                <version>${gms.cate.version}</version>
            </dependency>
            <!--iop标品服务-->
            <dependency>
                <groupId>com.jd.ka.gpt.soa</groupId>
                <artifactId>gpt-soa-client</artifactId>
                <version>${iop.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.wanfang</groupId>
                        <artifactId>wanfang-support-soa-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>aces-springclient</artifactId>
                <version>${aces.springclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.security</groupId>
                        <artifactId>tdeclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.security</groupId>
                        <artifactId>tdecommon</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>tdeclient</artifactId>
                <version>${tdeclient.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.pps</groupId>
                <artifactId>pps-public-api</artifactId>
                <version>${pps.price.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.fds</groupId>
                <artifactId>fusionds-lib</artifactId>
                <version>${fusionds.lib.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.fms</groupId>
                <artifactId>plough-JsfService</artifactId>
                <version>${fms.plough.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.jp</groupId>
                <artifactId>strategy-center-sdk</artifactId>
                <version>${jp.strategy.version}</version>
            </dependency>

            <!--ept商品服务-->
            <dependency>
                <groupId>com.jd.ept</groupId>
                <artifactId>ept-warecenter-client</artifactId>
                <version>${ept-warecenter-client.version}</version>
            </dependency>

            <dependency>
                <groupId>state-stock</groupId>
                <artifactId>state-stock-export</artifactId>
                <version>${state.stock.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-api</artifactId>
                <version>${isc.product.soa.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.fulfillment.soa</groupId>
                <artifactId>jdi-isc-fulfillment-soa-api</artifactId>
                <version>${isc.fulfillment.soa.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdi.isc.library</groupId>
                <artifactId>jdi-isc-library-i18n-datasource</artifactId>
                <version>${isc.library.i18n.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdi.isc.order.center</groupId>
                <artifactId>jdi-isc-order-center-api</artifactId>
                <version>${isc.order.center.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-price-sdk</artifactId>
                <version>${isc.product.soa.price.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-stock-sdk</artifactId>
                <version>${isc.product.mku.stock.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${org.projectlombok.version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.annotation</groupId>
                <artifactId>jakarta.annotation-api</artifactId>
                <version>${jakarta.annotation.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>${jaxb.runtime.version}</version>
            </dependency>

            <dependency>
                <artifactId>guava</artifactId>
                <groupId>com.google.guava</groupId>
                <version>${google.guava.version}</version>
            </dependency>

        </dependencies>



    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>libs-releases-local</id>
            <name>Release Repository</name>
            <url>http://artifactory.jd.com/libs-releases-local</url>
        </repository>
        <snapshotRepository>
            <id>libs-snapshots-local</id>
            <name>Snapshot Repository</name>
            <url>http://artifactory.jd.com/libs-snapshots-local</url>
        </snapshotRepository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>test</id>
            <properties>
                <lib.version>-test-SNAPSHOT</lib.version>
                <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
                <old.order.version>0</old.order.version>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <lib.version>-test-SNAPSHOT</lib.version>
                <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
                <old.order.version>0</old.order.version>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <lib.version>-SNAPSHOT</lib.version>
                <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
                <old.order.version>0</old.order.version>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <!-- 生产环境版本 -->
                <lib.version></lib.version>
                <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
                <old.order.version>1</old.order.version>
            </properties>
        </profile>
    </profiles>

</project>
