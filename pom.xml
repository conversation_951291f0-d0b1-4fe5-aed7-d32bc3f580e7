<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.jdi.dependencies</groupId>
        <artifactId>jdi-dependencies-parent</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <groupId>com.jdi.isc.task.center</groupId>
    <artifactId>jdi-isc-task-center</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>jdi-isc-task-center</name>
    <packaging>pom</packaging>

    <modules>
        <module>jdi-isc-task-center-api</module>
        <module>jdi-isc-task-center-common</module>
        <module>jdi-isc-task-center-domain</module>
        <module>jdi-isc-task-center-repository</module>
        <module>jdi-isc-task-center-service</module>
        <module>jdi-isc-task-center-web</module>
        <module>jdi-isc-task-center-rpc</module>
        <module>jdi-isc-task-center-msg</module>
    </modules>


    <properties>
        <spring.boot.version>2.7.8</spring.boot.version>
        <druid.version>1.2.11</druid.version>
        <tomcat.version>9.0.82</tomcat.version>
        <servlet.api.version>3.1.0</servlet.api.version>
        <laf.client.version>1.4.1</laf.client.version>
        <xxl.job.core.version>2.2.0</xxl.job.core.version>
        <hutool.version>5.8.5</hutool.version>
        <binlake.wave.client.version>1.1.1</binlake.wave.client.version>
        <jss.sdk.version>1.3.0-SNAPSHOT</jss.sdk.version>
        <unirest.java.version>1.4.9</unirest.java.version>
        <jd.image.common.version>1.0.7-SNAPSHOT</jd.image.common.version>
        <lingala.zip4j.version>1.3.2</lingala.zip4j.version>
        <igc.product.version>0.0.15-SNAPSHOT</igc.product.version>
        <b2b.product.version>2.26.0-SNAPSHOT</b2b.product.version>
        <com.alibaba.easyexcel.version>3.3.2</com.alibaba.easyexcel.version>
        <org.apache.httpclient.version>4.5.5</org.apache.httpclient.version>
        <isc.task.center.api.version>1.1.8${lib.version}</isc.task.center.api.version>
        <isc.task.center.common.version>0.0.1-SNAPSHOT</isc.task.center.common.version>
        <isc.task.center.domain.version>0.0.1-SNAPSHOT</isc.task.center.domain.version>
        <isc.task.center.repository.version>0.0.1-SNAPSHOT</isc.task.center.repository.version>
        <isc.task.center.rpc.version>0.0.1-SNAPSHOT</isc.task.center.rpc.version>
        <isc.task.center.service.version>0.0.1-SNAPSHOT</isc.task.center.service.version>
        <isc.task.center.msg.version>0.0.1-SNAPSHOT</isc.task.center.msg.version>
        <isc.task.center.web.version>0.0.1-SNAPSHOT</isc.task.center.web.version>
        <spsc.price.version>0.0.1-SNAPSHOT</spsc.price.version>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <mysql.version>8.0.16</mysql.version>
        <component.crs.proxy.verison>2.0.25</component.crs.proxy.verison>
        <xxl.job.core.version>2.2.0</xxl.job.core.version>
        <jd.aggregation.version>1.0.5-SNAPSHOT</jd.aggregation.version>
        <com.jd.gms.relation>0.0.1-SNAPSHOT</com.jd.gms.relation>
        <industry.ops.version>1.6.1-20230815.092715-1</industry.ops.version>
        <dynamic.datasource.relation>3.1.0</dynamic.datasource.relation>
        <vtDrive.version>1.2.20-Hotfix3</vtDrive.version>
        <xstream.version>1.4.18</xstream.version>
        <validator.version>6.0.18.Final</validator.version>
        <commons.collections.version>3.2.2</commons.collections.version>
        <jmq.client.all>4.2.7.RC1</jmq.client.all>
        <hibernate-validator.version>6.0.18.Final</hibernate-validator.version>
        <jmq.client.spring.version>2.3.3-RC2</jmq.client.spring.version>
        <imp.operate.version>1.0.4.3-SNAPSHOT</imp.operate.version>
        <imp.price.core.version>1.0.0-SNAPSHOT</imp.price.core.version>
        <jdi.dependencies.version>1.0-SNAPSHOT</jdi.dependencies.version>
        <jmq.client.core.version>2.3.2</jmq.client.core.version>
        <jsf.version>1.7.6-HOTFIX-T2</jsf.version>
        <jdi.common.dependency.version>1.0.2-SNAPSHOT</jdi.common.dependency.version>
        <jd.security.tomcat>1.13.WEBAPP</jd.security.tomcat>
        <jdi.common.frame.starter.version>1.0-SNAPSHOT</jdi.common.frame.starter.version>
        <jdi.common.model.domain.version>2.0.0</jdi.common.model.domain.version>
        <sso.version>1.0.5-SNAPSHOT</sso.version>
        <mybatis.plus.version>3.5.2</mybatis.plus.version>
        <aws.java.version>1.11.490</aws.java.version>
<!--        <isc.aggregate.read.wisp.api.version>0.7.12-SNAPSHOT</isc.aggregate.read.wisp.api.version>-->
        <isc.aggregate.read.wisp.api.version>1.1.4${lib.version}</isc.aggregate.read.wisp.api.version>
<!--        <isc.order.center.api.version>${lib.version}.0.62-SNAPSHOT</isc.order.center.api.version>-->
<!--        <isc.order.center.api.version>${old.order.version}.0.62-SNAPSHOT</isc.order.center.api.version>-->
        <isc.order.center.api.version>1.1.4${lib.version}</isc.order.center.api.version>
        <itextpdf.verison>5.5.13</itextpdf.verison>
        <jdi.isc.vc.soa.api.version>0.0.23-SNAPSHOT</jdi.isc.vc.soa.api.version>
<!--        <isc.product.soa.version>1.0.37-SNAPSHOT</isc.product.soa.version>-->
        <isc.product.soa.version>1.1.41${lib.version}</isc.product.soa.version>
        <jdi.isc.library.common.version>2.2.3-SNAPSHOT</jdi.isc.library.common.version>
        <jd.international.soa.version>1.1.14${lib.version}</jd.international.soa.version>
        <relation.aggregation.version>1.0.8</relation.aggregation.version>
        <com.jdi.isc.aggregate.api.version>1.1.4${lib.version}</com.jdi.isc.aggregate.api.version>
        <wanfang.version>0.1.6.1-SNAPSHOT</wanfang.version>
        <sdd.market.version>5.3-RELEASE</sdd.market.version>
        <com.jd.mail.version>0.0.1-SNAPSHOT</com.jd.mail.version>
        <jmq.client.springboot.version>2.3.2.2</jmq.client.springboot.version>
        <isc.product.center.api.version>0.0.6-SNAPSHOT</isc.product.center.api.version>
        <fastjson.version>1.2.83-jdsec.rc1</fastjson.version>
        <jd.print.version>1.1.48-SNAPSHOT</jd.print.version>
<!--        <isc.product.soa.stock.sdk.version>0.0.1-SNAPSHOT</isc.product.soa.stock.sdk.version>-->
        <isc.product.soa.stock.sdk.version>1.1.5${lib.version}</isc.product.soa.stock.sdk.version>
        <isc.bizcomponent.soa.version>1.0.8-SNAPSHOT</isc.bizcomponent.soa.version>
<!--        <isc.product.soa.price.version>0.0.1-SNAPSHOT</isc.product.soa.price.version>-->
        <isc.product.soa.price.version>1.1.6${lib.version}</isc.product.soa.price.version>
        <org.projectlombok.version>1.18.30</org.projectlombok.version>
        <jakarta.annotation.version>1.3.5</jakarta.annotation.version>
        <jaxb.runtime.version>2.3.9</jaxb.runtime.version>
        <google.guava.version>30.1-jre</google.guava.version>
        <hibernate.validator.annotation.version>8.0.2.Final</hibernate.validator.annotation.version>
        <kunlun.api.version>2.0.0-SNAPSHOT</kunlun.api.version>
        <byte.buddy.version>1.14.12</byte.buddy.version>
    </properties>

    <dependencyManagement>

        <dependencies>

            <dependency>
                <artifactId>common-domain</artifactId>
                <groupId>com.jdi.common</groupId>
                <version>${jdi.common.model.domain.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.common</groupId>
                <artifactId>jdi-common-frame-spring-boot-starter</artifactId>
                <version>${jdi.common.frame.starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>jd-security-tomcat</artifactId>
                <version>${jd.security.tomcat}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${servlet.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl.job.core.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.binlake</groupId>
                <artifactId>binlake-wave.client</artifactId>
                <version>${binlake.wave.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mashape.unirest</groupId>
                <artifactId>unirest-java</artifactId>
                <version>${unirest.java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ipd</groupId>
                <artifactId>jss-sdk-java</artifactId>
                <version>${jss.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.image</groupId>
                <artifactId>image-common</artifactId>
                <version>${jd.image.common.version}</version>
            </dependency>

            <dependency>
                <groupId>net.lingala.zip4j</groupId>
                <artifactId>zip4j</artifactId>
                <version>${lingala.zip4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${com.alibaba.easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.task.center</groupId>
                <artifactId>jdi-isc-task-center-api</artifactId>
                <version>${isc.task.center.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.task.center</groupId>
                <artifactId>jdi-isc-task-center-common</artifactId>
                <version>${isc.task.center.common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.task.center</groupId>
                <artifactId>jdi-isc-task-center-domain</artifactId>
                <version>${isc.task.center.domain.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.task.center</groupId>
                <artifactId>jdi-isc-task-center-repository</artifactId>
                <version>${isc.task.center.repository.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.task.center</groupId>
                <artifactId>jdi-isc-task-center-rpc</artifactId>
                <version>${isc.task.center.rpc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.task.center</groupId>
                <artifactId>jdi-isc-task-center-service</artifactId>
                <version>${isc.task.center.service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdi.isc.task.center</groupId>
                <artifactId>jdi-isc-task-center-msg</artifactId>
                <version>${isc.task.center.msg.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.task.center</groupId>
                <artifactId>jdi-isc-task-center-web</artifactId>
                <version>${isc.task.center.web.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic.datasource.relation}</version>
            </dependency>

            <dependency>
                <groupId>io.vitess.driver</groupId>
                <artifactId>vtdriver</artifactId>
                <version>${vtDrive.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>bcprov-jdk15on</artifactId>
                        <groupId>org.bouncycastle</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>${xstream.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${validator.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons.collections.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.joyqueue</groupId>
                <artifactId>jmq-client-all</artifactId>
                <version>${jmq.client.all}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.jmq</groupId>
                <artifactId>jmq-client-spring</artifactId>
                <version>${jmq.client.spring.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>jsf</artifactId>
                <version>${jsf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.ssa</groupId>
                <artifactId>oidc-client</artifactId>
                <version>${sso.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-catalina</artifactId>
                <version>${tomcat.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-jasper</artifactId>
                <version>${tomcat.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws.java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.aggregate.read</groupId>
                <artifactId>jdi-isc-aggregate-read-wisp-api</artifactId>
                <version>${isc.aggregate.read.wisp.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.order.center</groupId>
                <artifactId>jdi-isc-order-center-api</artifactId>
                <version>${isc.order.center.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.laf.config</groupId>
                <artifactId>laf-config-client-jd-springboot-starter</artifactId>
                <version>${laf.client.version}</version>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itextpdf</artifactId>
                <version>${itextpdf.verison}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.vc.soa</groupId>
                <artifactId>jdi-isc-vc-soa-api</artifactId>
                <version>${jdi.isc.vc.soa.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-api</artifactId>
                <version>${isc.product.soa.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.library</groupId>
                <artifactId>jdi-isc-library-common</artifactId>
                <version>${jdi.isc.library.common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.international.soa</groupId>
                <artifactId>international-common-sdk</artifactId>
                <version>${jd.international.soa.version}</version>
            </dependency>

            <dependency>
                <groupId>gms.jd.component.crs.proxy</groupId>
                <artifactId>component-crs-proxy-rpc</artifactId>
                <version>${component.crs.proxy.verison}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.aggregation</groupId>
                <artifactId>relation.aggregation.client</artifactId>
                <version>${relation.aggregation.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.aggregate.read</groupId>
                <artifactId>jdi-isc-aggregate-read-api</artifactId>
                <version>${com.jdi.isc.aggregate.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.wanfang</groupId>
                <artifactId>wanfang-support-soa-client</artifactId>
                <version>${wanfang.version}</version>
            </dependency>
            <!--iop标品服务-->
            <dependency>
                <groupId>com.jd.ka.gpt.soa</groupId>
                <artifactId>gpt-soa-client</artifactId>
                <version>0.0.17-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.wanfang</groupId>
                        <artifactId>wanfang-support-soa-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 装吧 -->
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>sdd_market-sdk</artifactId>
                <version>${sdd.market.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.4.1</version>
            </dependency>

            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>3.4.1</version>
            </dependency>

            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>2.3.31</version>
            </dependency>

            <!-- 用户组接口密码修改end -->
            <dependency>
                <groupId>com.jd.leo.mail</groupId>
                <artifactId>product_produce-rpc-service</artifactId>
                <version>${com.jd.mail.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd</groupId>
                        <artifactId>jsf</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.jmq</groupId>
                <artifactId>jmq2-client-springboot-starter</artifactId>
                <version>${jmq.client.springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.library</groupId>
                <artifactId>jdi-isc-library-i18n-datasource</artifactId>
                <version>2.2.2-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.10.0</version>
            </dependency>
            <dependency>
                <groupId>com.jd.jp</groupId>
                <artifactId>print-templet-center-sdk</artifactId>
                <version>${jd.print.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-stock-sdk</artifactId>
                <version>${isc.product.soa.stock.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdi.isc.bizcomponent.center</groupId>
                <artifactId>jdi-isc-bizcomponent-api</artifactId>
                <version>${isc.bizcomponent.soa.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-price-sdk</artifactId>
                <version>${isc.product.soa.price.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${org.projectlombok.version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.annotation</groupId>
                <artifactId>jakarta.annotation-api</artifactId>
                <version>${jakarta.annotation.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>${jaxb.runtime.version}</version>
            </dependency>

            <dependency>
                <artifactId>guava</artifactId>
                <groupId>com.google.guava</groupId>
                <version>${google.guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator-annotation-processor</artifactId>
                <version>${hibernate.validator.annotation.version}</version>
                <optional>true</optional>
            </dependency>

            <dependency>
                <groupId>com.jdi.kunlun</groupId>
                <artifactId>kunlun-algo-cls-api</artifactId>
                <version>${kunlun.api.version}</version>
            </dependency>

            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${byte.buddy.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>libs-releases-local</id>
            <name>Release Repository</name>
            <url>http://artifactory.jd.com/libs-releases-local</url>
        </repository>
        <snapshotRepository>
            <id>libs-snapshots-local</id>
            <name>Snapshot Repository</name>
            <url>http://artifactory.jd.com/libs-snapshots-local</url>
        </snapshotRepository>
    </distributionManagement>


    <profiles>
        <profile>
            <id>test</id>
            <properties>
                <lib.version>-test-SNAPSHOT</lib.version>
                <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
                <old.order.version>0</old.order.version>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <lib.version>-test-SNAPSHOT</lib.version>
                <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
                <old.order.version>0</old.order.version>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <lib.version>-SNAPSHOT</lib.version>
                <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
                <old.order.version>0</old.order.version>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <!-- 生产环境版本 -->
                <lib.version></lib.version>
                <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
                <old.order.version>1</old.order.version>
            </properties>
        </profile>
    </profiles>

</project>
