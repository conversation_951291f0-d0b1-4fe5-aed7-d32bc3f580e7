<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>spring-boot-starter-parent</artifactId>
		<groupId>org.springframework.boot</groupId>
		<version>2.7.18</version>
	</parent>
	<groupId>com.jd.international</groupId>
	<artifactId>international</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>pom</packaging>
	<name>international</name>
	<description>工采国际站</description>
	<modules>
		<module>international-web</module>
		<module>international-common</module>
		<module>international-service</module>
		<module>international-domain</module>
		<module>international-rpc</module>
    </modules>
	<properties>
		<java.version>21</java.version>
		<spring-framework.version>5.3.31</spring-framework.version>
		<com.jd.configsec.version>1.0.3-SNAPSHOT</com.jd.configsec.version>
		<org.springframework.cloud.zuul.version>2.2.10.RELEASE</org.springframework.cloud.zuul.version>
		<lombok.version>1.18.30</lombok.version>
		<log4j.verison>2.18.0-jdsec.rc2</log4j.verison>
		<!--<com.jd.ump.version>20240630</com.jd.ump.version>-->
		<com.jd.ump.version>6.2.13-HOTFIX-T1</com.jd.ump.version>
		<com.jd.jim.verison>2.2.7</com.jd.jim.verison>
		<fastjson2.verison>2.0.17</fastjson2.verison>
		<fastjson.version>1.2.83-jdsec.rc1</fastjson.version>
		<ducc.version>1.4.1</ducc.version>
		<jsf.version>1.7.6-HOTFIX-T3</jsf.version>
		<jd.common.util.version>1.2-SNAPSHOT</jd.common.util.version>
		<hutool.version>5.8.5</hutool.version>
		<ognl.version>3.1.15</ognl.version>
		<project.international.workflow.version>0.0.10-SNAPSHOT</project.international.workflow.version>
		<international.approval.sdk.version>0.0.4-SNAPSHOT</international.approval.sdk.version>
		<international.common.sdk.version>1.1.14${lib.version}</international.common.sdk.version>
		<org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
		<com.alibaba.easyexcel.version>3.3.2</com.alibaba.easyexcel.version>
		<login.agent.version>2.1.1</login.agent.version>
		<jmq.client.springboot.version>2.3.2.2</jmq.client.springboot.version>
		<isc.product.soa.api.version>1.1.39${lib.version}</isc.product.soa.api.version>
		<jdi.isc.library.common.version>2.2.5-SNAPSHOT</jdi.isc.library.common.version>
		<jdi.isc.task.center.api.version>0.0.18-SNAPSHOT</jdi.isc.task.center.api.version>
		<aws.java.version>1.11.490</aws.java.version>
		<international.order.sdk.version>0.0.25-SNAPSHOT</international.order.sdk.version>
		<wanfang.support.soa.client.version>0.1.8.0-SNAPSHOT</wanfang.support.soa.client.version>
		<jdi.isc.i18n.datasource.version>2.2.6-SNAPSHOT</jdi.isc.i18n.datasource.version>
		<com.jdi.isc.aggregate.api.version>1.1.4${lib.version}</com.jdi.isc.aggregate.api.version>
		<jdi.common.model.domain.version>1.0-SNAPSHOT</jdi.common.model.domain.version>
		<sec.api.version>0.0.11-RELEASES</sec.api.version>
		<pfinder.version>1.2.1-FINAL</pfinder.version>
		<isc.order.center.version>1.1.4${lib.version}</isc.order.center.version>
		<isc.product.mku.stock.version>0.0.2-SNAPSHOT</isc.product.mku.stock.version>
		<isc.aggregate.read.wisp.api>1.1.4${lib.version}</isc.aggregate.read.wisp.api>
		<isc.fulfillment.soa.api.version>1.1.3${lib.version}</isc.fulfillment.soa.api.version>
		<dd.open.gw.version>4.2.0-SNAPSHOT</dd.open.gw.version>
		<user.sdk.version>4.3.1-SNAPSHOT</user.sdk.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency> <!-- 引入log4j2依赖 -->
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-log4j2</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>${log4j.verison}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-jul</artifactId>
		</dependency>
		<!--配置文件解密-->
		<dependency>
			<groupId>com.jd.security.configsec</groupId>
			<artifactId>spring-configsec-sdk</artifactId>
			<version>${com.jd.configsec.version}</version>
		</dependency>
		<dependency>
			<groupId>com.jd.ump</groupId>
			<artifactId>profiler</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba.fastjson2</groupId>
			<artifactId>fastjson2</artifactId>
			<version>${fastjson2.verison}</version>
		</dependency>
		<!-- ducc -->
		<dependency>
			<groupId>com.jd.laf.config</groupId>
			<artifactId>laf-config-client-jd-springboot-starter</artifactId>
			<version>${ducc.version}</version>
			<type>pom</type>
			<exclusions>
				<exclusion>
					<artifactId>fastjson</artifactId>
					<groupId>com.alibaba</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.jd.common</groupId>
			<artifactId>jd-common-util</artifactId>
			<version>${jd.common.util.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>${hutool.version}</version>
		</dependency>
		<!--登陆拦截器-->
		<dependency>
			<groupId>com.jd.passport</groupId>
			<artifactId>login-filter-agent</artifactId>
		</dependency>
	</dependencies>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-thymeleaf</artifactId>
				<version>2.7.4</version>
				<exclusions>
					<exclusion>
						<groupId>org.springframework.boot</groupId>
						<artifactId>spring-boot-starter-logging</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency> <!-- 引入log4j2依赖 -->
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-log4j2</artifactId>
				<version>2.7.4</version>
				<exclusions>
					<exclusion>
						<groupId>org.apache.logging.log4j</groupId>
						<artifactId>log4j-slf4j-impl</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.logging.log4j</groupId>
						<artifactId>log4j-core</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.logging.log4j</groupId>
						<artifactId>log4j-jul</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.logging.log4j</groupId>
						<artifactId>log4j-api</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-slf4j-impl</artifactId>
				<version>${log4j.verison}</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j-api</artifactId>
						<groupId>org.apache.logging.log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>log4j-core</artifactId>
						<groupId>org.apache.logging.log4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-core</artifactId>
				<version>${log4j.verison}</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j-api</artifactId>
						<groupId>org.apache.logging.log4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-jul</artifactId>
				<version>${log4j.verison}</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j-api</artifactId>
						<groupId>org.apache.logging.log4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.jd.biz</groupId>
				<artifactId>mro-ka-workflow-sdk</artifactId>
				<version>${project.international.workflow.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>4.4</version>
			</dependency>
			<dependency>
				<groupId>com.jd.biz</groupId>
				<artifactId>jd-biz-user-soa-sdk</artifactId>
				<version>1.6.8-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.jd.jdi</groupId>
				<artifactId>suan-ni</artifactId>
				<version>0.0.1-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.jd.jdi</groupId>
				<artifactId>k2-product-sku-boost-cache-sdk</artifactId>
				<version>0.0.7-SNAPSHOT</version>
			</dependency>
			<dependency>
				<artifactId>international-approval-sdk</artifactId>
				<groupId>com.jd.international.soa</groupId>
				<version>${international.approval.sdk.version}</version>
			</dependency>
			<dependency>
				<artifactId>international-common-sdk</artifactId>
				<groupId>com.jd.international.soa</groupId>
				<version>${international.common.sdk.version}</version>
			</dependency>
			<dependency>
				<groupId>com.jd.ump</groupId>
				<artifactId>profiler</artifactId>
				<version>${com.jd.ump.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>fastjson</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-api</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.jd.security</groupId>
				<artifactId>jd-security-tomcat</artifactId>
				<version>1.13.WEBAPP</version>
			</dependency>

			<dependency>
				<groupId>org.mapstruct</groupId>
				<artifactId>mapstruct</artifactId>
				<version>${org.mapstruct.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mapstruct</groupId>
				<artifactId>mapstruct-processor</artifactId>
				<version>${org.mapstruct.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>easyexcel</artifactId>
				<version>${com.alibaba.easyexcel.version}</version>
			</dependency>
			<!--登陆拦截器-->
			<dependency>
				<groupId>com.jd.passport</groupId>
				<artifactId>login-filter-agent</artifactId>
				<version>${login.agent.version}</version>
			</dependency>
			<!--<dependency>
				<groupId>com.jd.passport</groupId>
				<artifactId>login-filter-agent-jakarta</artifactId>
				<version>${login.agent.version}</version>
			</dependency>-->
			<dependency>
				<groupId>com.jd.jmq</groupId>
				<artifactId>jmq2-client-springboot-starter</artifactId>
				<version>${jmq.client.springboot.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.alibaba</groupId>
						<artifactId>fastjson</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework.boot</groupId>
						<artifactId>spring-boot-starter-logging</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.jdi.isc.product.soa</groupId>
				<artifactId>jdi-isc-product-soa-api</artifactId>
				<version>${isc.product.soa.api.version}</version>
			</dependency>
			<dependency>
				<groupId>com.jdi.isc.library</groupId>
				<artifactId>jdi-isc-library-common</artifactId>
				<version>${jdi.isc.library.common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.jdi.isc.task.center</groupId>
				<artifactId>jdi-isc-task-center-api</artifactId>
				<version>${jdi.isc.task.center.api.version}</version>
			</dependency>

			<dependency>
				<groupId>com.amazonaws</groupId>
				<artifactId>aws-java-sdk-s3</artifactId>
				<version>${aws.java.version}</version>
			</dependency>
			<dependency>
				<artifactId>international-order-sdk</artifactId>
				<groupId>com.jd.international.soa</groupId>
				<version>${international.order.sdk.version}</version>
			</dependency>
			<dependency>
				<groupId>com.jd.wanfang</groupId>
				<artifactId>wanfang-support-soa-client</artifactId>
				<version>${wanfang.support.soa.client.version}</version>
			</dependency>
			<dependency>
				<groupId>com.jdi.isc.library</groupId>
				<artifactId>jdi-isc-library-i18n-datasource</artifactId>
				<version>${jdi.isc.i18n.datasource.version}</version>
			</dependency>
			<dependency>
				<groupId>com.jdi.isc.aggregate.read</groupId>
				<artifactId>jdi-isc-aggregate-read-api</artifactId>
				<version>${com.jdi.isc.aggregate.api.version}</version>
			</dependency>
			<dependency>
				<groupId>com.jdi.common</groupId>
				<artifactId>jdi-common-frame-spring-boot-starter</artifactId>
				<version>${jdi.common.model.domain.version}</version>
			</dependency>
			<dependency>
				<groupId>com.jd</groupId>
				<artifactId>sec_api</artifactId>
				<version>${sec.api.version}</version>
			</dependency>
			<dependency>
				<groupId>com.jd.pfinder</groupId>
				<artifactId>pfinder-profiler-sdk</artifactId>
				<version>${pfinder.version}</version>
			</dependency>

			<!--工业国际订单中心-->
			<dependency>
				<groupId>com.jdi.isc.order.center</groupId>
				<artifactId>jdi-isc-order-center-api</artifactId>
				<version>${isc.order.center.version}</version>
			</dependency>

			<dependency>
				<groupId>com.jdi.isc.product.soa</groupId>
				<artifactId>jdi-isc-product-soa-stock-sdk</artifactId>
				<version>${isc.product.mku.stock.version}</version>
			</dependency>

			<dependency>
				<groupId>com.jdi.isc.aggregate.read</groupId>
				<artifactId>jdi-isc-aggregate-read-wisp-api</artifactId>
				<version>${isc.aggregate.read.wisp.api}</version>
			</dependency>

<!--			<dependency>-->
<!--				<groupId>com.xingyuv</groupId>-->
<!--				<artifactId>spring-boot-starter-captcha-plus</artifactId>-->
<!--				<version>1.0.8</version>-->
<!--			</dependency>-->

			<dependency>
				<groupId>com.jdi.isc.fulfillment.soa</groupId>
				<artifactId>jdi-isc-fulfillment-soa-api</artifactId>
				<version>${isc.fulfillment.soa.api.version}</version>
			</dependency>

			<dependency>
				<groupId>com.jd.dd</groupId>
				<artifactId>dd-open-gw-api</artifactId>
				<version>${dd.open.gw.version}</version>
			</dependency>

			<dependency>
				<groupId>com.jd.user.sdk</groupId>
				<artifactId>user-sdk-export</artifactId>
				<version>${user.sdk.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.10.0</version>
				<configuration>
					<source>21</source>
					<target>21</target>
					<encoding>utf-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.3.1</version>
				<configuration>
					<encoding>utf-8</encoding>
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>
			<!-- Source attach plugin -->
			<plugin>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.3.0</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.2.2</version>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<profiles>

		<profile>
			<id>dev</id>
			<properties>
				<profile.env>dev</profile.env>
				<lib.version>-test-SNAPSHOT</lib.version>
			</properties>
		</profile>

		<profile>
			<id>test</id>
			<properties>
				<profile.env>test</profile.env>
				<lib.version>-test-SNAPSHOT</lib.version>
			</properties>
		</profile>

		<profile>
			<id>pre</id>
			<properties>
				<profile.env>pre</profile.env>
				<lib.version>-SNAPSHOT</lib.version>
			</properties>
		</profile>

		<profile>
			<id>pro</id>
			<properties>
				<profile.env>pro</profile.env>
				<lib.version></lib.version>
			</properties>
		</profile>
	</profiles>


</project>
