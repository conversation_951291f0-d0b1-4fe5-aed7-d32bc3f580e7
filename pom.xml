<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.jdi.dependencies</groupId>
        <artifactId>jdi-dependencies-parent</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <groupId>com.jdi.isc.product.soa</groupId>
    <artifactId>jdi-isc-product-soa</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>jdi-isc-product-soa</name>
    <packaging>pom</packaging>

    <modules>
        <module>jdi-isc-product-soa-api</module>
        <module>jdi-isc-product-soa-common</module>
        <module>jdi-isc-product-soa-domain</module>
        <module>jdi-isc-product-soa-repository</module>
        <module>jdi-isc-product-soa-service</module>
        <module>jdi-isc-product-soa-rpc</module>
        <module>jdi-isc-product-soa-msg</module>
        <module>jdi-isc-product-soa-web</module>
        <module>jdi-isc-product-soa-stock-sdk</module>
        <module>jdi-isc-product-soa-price-sdk</module>
    </modules>

    <properties>
        <spring.boot.version>2.7.8</spring.boot.version>
        <druid.version>1.2.11</druid.version>
        <tomcat.version>9.0.82</tomcat.version>
        <servlet.api.version>3.1.0</servlet.api.version>
        <laf.client.version>1.4.1</laf.client.version>
        <xxl.job.core.version>2.2.0</xxl.job.core.version>
        <hutool.version>5.8.5</hutool.version>
        <binlake.wave.client.version>1.1.1</binlake.wave.client.version>
        <jss.sdk.version>1.3.0-SNAPSHOT</jss.sdk.version>
        <unirest.java.version>1.4.9</unirest.java.version>
        <jd.image.common.version>1.0.7-SNAPSHOT</jd.image.common.version>
        <lingala.zip4j.version>1.3.2</lingala.zip4j.version>
        <igc.product.version>0.0.15-SNAPSHOT</igc.product.version>
        <b2b.product.version>2.26.0-SNAPSHOT</b2b.product.version>
        <com.alibaba.easyexcel.version>3.3.2</com.alibaba.easyexcel.version>
        <org.apache.httpclient.version>4.5.5</org.apache.httpclient.version>
<!--        <isc.product.soa.api.version>${lib.version}.0.37-SNAPSHOT</isc.product.soa.api.version>-->
        <isc.product.soa.api.version>1.1.39${lib.version}</isc.product.soa.api.version>
        <isc.product.soa.common.version>0.0.1-SNAPSHOT</isc.product.soa.common.version>
        <isc.product.soa.domain.version>0.0.1-SNAPSHOT</isc.product.soa.domain.version>
        <isc.product.soa.repository.version>0.0.1-SNAPSHOT</isc.product.soa.repository.version>
        <isc.product.soa.rpc.version>0.0.1-SNAPSHOT</isc.product.soa.rpc.version>
        <isc.product.soa.service.version>0.0.1-SNAPSHOT</isc.product.soa.service.version>
        <isc.product.soa.msg.version>0.0.1-SNAPSHOT</isc.product.soa.msg.version>
<!--        <isc.product.soa.price.version>0.0.1-SNAPSHOT</isc.product.soa.price.version>-->
        <isc.product.soa.price.version>1.1.6${lib.version}</isc.product.soa.price.version>

        <spsc.price.version>0.0.1-SNAPSHOT</spsc.price.version>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <mysql.version>8.0.16</mysql.version>
        <component.crs.proxy.verison>1.4.44</component.crs.proxy.verison>
        <xxl.job.core.version>2.2.0</xxl.job.core.version>
        <jd.aggregation.version>1.0.5-SNAPSHOT</jd.aggregation.version>
        <com.jd.gms.relation>0.0.1-SNAPSHOT</com.jd.gms.relation>
        <industry.ops.version>1.6.1-20230815.092715-1</industry.ops.version>
        <dynamic.datasource.relation>3.1.0</dynamic.datasource.relation>
        <vtDrive.version>1.2.20-Hotfix3</vtDrive.version>
        <xstream.version>1.4.18</xstream.version>
        <validator.version>6.0.18.Final</validator.version>
        <commons.collections.version>3.2.2</commons.collections.version>
        <hibernate-validator.version>6.0.18.Final</hibernate-validator.version>
        <imp.operate.version>1.0.4.3-SNAPSHOT</imp.operate.version>
        <imp.price.core.version>1.0.0-SNAPSHOT</imp.price.core.version>
        <jdi.dependencies.version>1.0-SNAPSHOT</jdi.dependencies.version>
        <jsf.version>1.7.8-HOTFIX-T3</jsf.version>
        <jdi.common.dependency.version>1.0.2-SNAPSHOT</jdi.common.dependency.version>
        <jd.security.tomcat>1.13.WEBAPP</jd.security.tomcat>
        <jdi.common.frame.starter.version>1.0-SNAPSHOT</jdi.common.frame.starter.version>
        <jdi.common.model.domain.version>2.0.0</jdi.common.model.domain.version>
        <sso.version>1.0.5-SNAPSHOT</sso.version>
        <mybatis.plus.version>3.5.2</mybatis.plus.version>
        <aws.java.version>1.11.490</aws.java.version>
        <commons.fileupload.version>1.5</commons.fileupload.version>
        <wanfang.version>0.1.6.1-SNAPSHOT</wanfang.version>
        <laf.client.version>1.4.1</laf.client.version>
        <gms.cate.version>1.4.10-RELEASE</gms.cate.version>
        <iop.client.version>0.0.17-SNAPSHOT</iop.client.version>
        <pps.price.version>1.0.46-RELEASES</pps.price.version>
        <sdd.market.version>5.3-RELEASE</sdd.market.version>
<!--        <jdi.isc.order.center.api.verison>${old.order.version}.0.49-SNAPSHOT</jdi.isc.order.center.api.verison>-->
        <jdi.isc.order.center.api.verison>1.1.6${lib.version}</jdi.isc.order.center.api.verison>
        <aces.springclient.version>3.0.2</aces.springclient.version>
        <tdeclient.version>3.0.4-SNAPSHOT</tdeclient.version>
        <jmq.client.springboot.version>2.3.2.2</jmq.client.springboot.version>
        <vms.external.version>1.3.2-SNAPSHOT</vms.external.version>
        <relation.aggregation.version>1.0.8</relation.aggregation.version>
        <international.common.sdk.version>0.0.4-SNAPSHOT</international.common.sdk.version>
        <data.elasticsearch.version>4.4.0</data.elasticsearch.version>
        <com.jd.xbp.jmq4.data.version>1.0.12</com.jd.xbp.jmq4.data.version>
        <okhttp3.version>4.9.3</okhttp3.version>
        <jakarta.json-api.version>2.0.1</jakarta.json-api.version>
        <fms.plough.version>4.1.0-RELEASE</fms.plough.version>
        <jp.strategy.version>2.1.85-SNAPSHOT</jp.strategy.version>
        <elasticsearch.version>7.17.3</elasticsearch.version>
        <vc.auth.api.versoin>0.2.4.1</vc.auth.api.versoin>
        <pop.anycall.versoin>1.1.8-SNAPSHOT</pop.anycall.versoin>
        <com.jd.xbp.jsf.api.version>1.2.5</com.jd.xbp.jsf.api.version>
        <esi.core.verison>1.0.3-Mcode-SNAPSHOT</esi.core.verison>
<!--        <com.jdi.isc.aggregate.api.version>0.0.20-SNAPSHOT</com.jdi.isc.aggregate.api.version>-->
        <com.jdi.isc.aggregate.api.version>1.1.4${lib.version}</com.jdi.isc.aggregate.api.version>
        <com.jd.mail.version>0.0.1-SNAPSHOT</com.jd.mail.version>
        <mdm.version>0.0.5-RELEASE</mdm.version>
        <flattener.version>0.12.1</flattener.version>
        <common.text.verson>1.10.0</common.text.verson>
        <ept-warecenter-client.version>1.0.37-SNAPSHOT</ept-warecenter-client.version>
        <jdi.isc.aggregate.read.wiop.api.verison>0.0.7-SNAPSHOT</jdi.isc.aggregate.read.wiop.api.verison>
        <caffeine.version>2.9.3</caffeine.version>
        <b2b.price.core.version>1.0.7</b2b.price.core.version>
        <jdi.isc.bizcomponent.api.version>${old.order.version}.0.8-SNAPSHOT</jdi.isc.bizcomponent.api.version>
        <fastjson.version>1.2.83-jdsec.rc1</fastjson.version>
        <camunda-platform-7-rest-client-spring-boot.version>7.19.0</camunda-platform-7-rest-client-spring-boot.version>
        <camunda.platform.version>7.19.0</camunda.platform.version>
<!--        <product.soa.stock.sdk.version>0.0.2-SNAPSHOT</product.soa.stock.sdk.version>-->
        <product.soa.stock.sdk.version>1.1.6${lib.version}</product.soa.stock.sdk.version>
        <k2.customer.pool.sdk.version>0.3.1.6-SNAPSHOT</k2.customer.pool.sdk.version>
        <state.stock.version>1.7.8-jdk8-SNAPSHOT</state.stock.version>
        <com.jd.sec.version>0.0.11-RELEASES</com.jd.sec.version>
        <org.projectlombok.version>1.18.30</org.projectlombok.version>
        <jakarta.annotation.version>1.3.5</jakarta.annotation.version>
        <jaxb.runtime.version>2.3.9</jaxb.runtime.version>
        <google.guava.version>30.1-jre</google.guava.version>
        <jd.auth.facade.version>1.5.0</jd.auth.facade.version>
        <user.soa.manage.version>1.0.7-SNAPSHOT</user.soa.manage.version>
        <buser.sdk.version>0.6.6-SNAPSHOT</buser.sdk.version>
        <isc.vc.soa.version>1.1.1-SNAPSHOT</isc.vc.soa.version>
        <isc.fulfillment.soa.version>1.1.3${lib.version}</isc.fulfillment.soa.version>
        <spring.retry.version>1.3.4</spring.retry.version>
        <order-client.limitbuy.version>0.1.76</order-client.limitbuy.version>
    </properties>

    <dependencyManagement>

        <dependencies>

            <dependency>
                <groupId>jd.gms</groupId>
                <artifactId>category-soa-dubbo-client</artifactId>
                <version>2.9.12</version>
            </dependency>

            <dependency>
                <artifactId>common-domain</artifactId>
                <groupId>com.jdi.common</groupId>
                <version>${jdi.common.model.domain.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.common</groupId>
                <artifactId>jdi-common-frame-spring-boot-starter</artifactId>
                <version>${jdi.common.frame.starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>jd-security-tomcat</artifactId>
                <version>${jd.security.tomcat}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${servlet.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl.job.core.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.binlake</groupId>
                <artifactId>binlake-wave.client</artifactId>
                <version>${binlake.wave.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mashape.unirest</groupId>
                <artifactId>unirest-java</artifactId>
                <version>${unirest.java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ipd</groupId>
                <artifactId>jss-sdk-java</artifactId>
                <version>${jss.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.image</groupId>
                <artifactId>image-common</artifactId>
                <version>${jd.image.common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${com.alibaba.easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-api</artifactId>
                <version>${isc.product.soa.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-common</artifactId>
                <version>${isc.product.soa.common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-domain</artifactId>
                <version>${isc.product.soa.domain.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-repository</artifactId>
                <version>${isc.product.soa.repository.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-rpc</artifactId>
                <version>${isc.product.soa.rpc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-service</artifactId>
                <version>${isc.product.soa.service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-msg</artifactId>
                <version>${isc.product.soa.msg.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic.datasource.relation}</version>
            </dependency>

            <dependency>
                <groupId>io.vitess.driver</groupId>
                <artifactId>vtdriver</artifactId>
                <version>${vtDrive.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>bcprov-jdk15on</artifactId>
                        <groupId>org.bouncycastle</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>${xstream.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${validator.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons.collections.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>jsf</artifactId>
                <version>${jsf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-catalina</artifactId>
                <version>${tomcat.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-jasper</artifactId>
                <version>${tomcat.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws.java.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.wanfang</groupId>
                <artifactId>wanfang-support-soa-client</artifactId>
                <version>${wanfang.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.laf.config</groupId>
                <artifactId>laf-config-client-jd-springboot-starter</artifactId>
                <version>${laf.client.version}</version>
                <type>pom</type>
            </dependency>

            <dependency>
                <artifactId>greatdane.client</artifactId>
                <groupId>component.gms</groupId>
                <version>${gms.cate.version}</version>
            </dependency>


            <dependency>
                <groupId>com.jdi.isc.order.center</groupId>
                <artifactId>jdi-isc-order-center-api</artifactId>
                <version>${jdi.isc.order.center.api.verison}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.pps</groupId>
                <artifactId>pps-public-api</artifactId>
                <version>${pps.price.version}</version>
            </dependency>

            <!-- 装吧 -->
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>sdd_market-sdk</artifactId>
                <version>${sdd.market.version}</version>
            </dependency>

            <dependency>
                <groupId>net.lingala.zip4j</groupId>
                <artifactId>zip4j</artifactId>
                <version>${lingala.zip4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.gms.relation</groupId>
                <artifactId>gms-relation-platform-api</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.jd.jmq</groupId>
                <artifactId>jmq2-client-springboot-starter</artifactId>
                <version>${jmq.client.springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>aces-springclient</artifactId>
                <version>${aces.springclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.security</groupId>
                        <artifactId>tdeclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.security</groupId>
                        <artifactId>tdecommon</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>tdeclient</artifactId>
                <version>${tdeclient.version}</version>
            </dependency>

            <dependency>
                <groupId>gms.jd.component.crs.proxy</groupId>
                <artifactId>component-crs-proxy-rpc</artifactId>
                <version>${component.crs.proxy.verison}</version>
            </dependency>


            <dependency>
                <groupId>com.jd.vms</groupId>
                <artifactId>vms-i18n-external-api</artifactId>
                <version>${vms.external.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.aggregation</groupId>
                <artifactId>relation.aggregation.client</artifactId>
                <version>${relation.aggregation.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-elasticsearch</artifactId>
                <version>${data.elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.json</groupId>
                <artifactId>jakarta.json-api</artifactId>
                <version>${jakarta.json-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.xbp</groupId>
                <artifactId>xbp-jmq4-data</artifactId>
                <version>${com.jd.xbp.jmq4.data.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.fms</groupId>
                <artifactId>plough-JsfService</artifactId>
                <version>${fms.plough.version}</version>
            </dependency>

            <!--iop标品服务-->
            <dependency>
                <groupId>com.jd.ka.gpt.soa</groupId>
                <artifactId>gpt-soa-client</artifactId>
                <version>${iop.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.wanfang</groupId>
                        <artifactId>wanfang-support-soa-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.jp</groupId>
                <artifactId>strategy-center-sdk</artifactId>
                <version>${jp.strategy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.ssa</groupId>
                <artifactId>oidc-client</artifactId>
                <version>${sso.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.xbp</groupId>
                <artifactId>jsf-api</artifactId>
                <version>${com.jd.xbp.jsf.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.ebs.esi</groupId>
                <artifactId>esi-core</artifactId>
                <version>${esi.core.verison}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.aggregate.read</groupId>
                <artifactId>jdi-isc-aggregate-read-api</artifactId>
                <version>${com.jdi.isc.aggregate.api.version}</version>
            </dependency>

            <dependency>
                <artifactId>international-common-sdk</artifactId>
                <groupId>com.jd.international.soa</groupId>
                <version>${international.common.sdk.version}</version>
            </dependency>

            <!-- 用户组接口密码修改end -->
            <dependency>
                <groupId>com.jd.leo.mail</groupId>
                <artifactId>product_produce-rpc-service</artifactId>
                <version>${com.jd.mail.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd</groupId>
                        <artifactId>jsf</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>ea-mdm-export</artifactId>
                <version>${mdm.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.wnameless.json</groupId>
                <artifactId>json-flattener</artifactId>
                <version>${flattener.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.vc.auth</groupId>
                <artifactId>vc_auth_api</artifactId>
                <version>${vc.auth.api.versoin}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.pop</groupId>
                <artifactId>pop-anycall-client</artifactId>
                <version>${pop.anycall.versoin}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${common.text.verson}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-freemarker</artifactId>
                <version>3.2.4</version>
            </dependency>

            <!--ept商品服务-->
            <dependency>
                <groupId>com.jd.ept</groupId>
                <artifactId>ept-warecenter-client</artifactId>
                <version>${ept-warecenter-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>

            <!--价格平台-->
            <dependency>
                <groupId>com.jd.b2b.price</groupId>
                <artifactId>b2b-price-core-sdk</artifactId>
                <version>${b2b.price.core.version}</version>
            </dependency>

            <!-- 审批-->
            <dependency>
                <groupId>com.jdi.isc.bizcomponent.center</groupId>
                <artifactId>jdi-isc-bizcomponent-api</artifactId>
                <version>${jdi.isc.bizcomponent.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.biz</groupId>
                <artifactId>jd-biz-user-soa-manage-sdk</artifactId>
                <version>${user.soa.manage.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.ka</groupId>
                <artifactId>buser-sdk</artifactId>
                <version>${buser.sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>org.camunda.bpm</groupId>
                <artifactId>camunda-bom</artifactId>
                <version>${camunda.platform.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>org.camunda.community</groupId>
                <artifactId>camunda-engine-rest-client-complete-springboot-starter</artifactId>
                <version>${camunda-platform-7-rest-client-spring-boot.version}</version>

            </dependency>
            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-stock-sdk</artifactId>
                <version>${product.soa.stock.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.jdi</groupId>
                <artifactId>k2-customer-pool-core-client</artifactId>
                <version>${k2.customer.pool.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>state-stock</groupId>
                <artifactId>state-stock-export</artifactId>
                <version>${state.stock.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.product.soa</groupId>
                <artifactId>jdi-isc-product-soa-price-sdk</artifactId>
                <version>${isc.product.soa.price.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.vc.soa</groupId>
                <artifactId>jdi-isc-vc-soa-api</artifactId>
                <version>${isc.vc.soa.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdi.isc.fulfillment.soa</groupId>
                <artifactId>jdi-isc-fulfillment-soa-api</artifactId>
                <version>${isc.fulfillment.soa.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${org.projectlombok.version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.annotation</groupId>
                <artifactId>jakarta.annotation-api</artifactId>
                <version>${jakarta.annotation.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>${jaxb.runtime.version}</version>
            </dependency>

            <dependency>
                <artifactId>guava</artifactId>
                <groupId>com.google.guava</groupId>
                <version>${google.guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>sec_api</artifactId>
                <version>${com.jd.sec.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.auth</groupId>
                <artifactId>auth-facade</artifactId>
                <version>${jd.auth.facade.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.springframework.retry/spring-retry -->
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>${spring.retry.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.limitbuy</groupId>
                <artifactId>limitbuy.soa.order-client</artifactId>
                <version>${order-client.limitbuy.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>libs-releases-local</id>
            <name>Release Repository</name>
            <url>http://artifactory.jd.com/libs-releases-local</url>
        </repository>
        <snapshotRepository>
            <id>libs-snapshots-local</id>
            <name>Snapshot Repository</name>
            <url>http://artifactory.jd.com/libs-snapshots-local</url>
        </snapshotRepository>
    </distributionManagement>


    <profiles>
        <profile>
            <id>test</id>
            <properties>
                <lib.version>-test-SNAPSHOT</lib.version>
                <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
                <old.order.version>0</old.order.version>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <lib.version>-test-SNAPSHOT</lib.version>
                <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
                <old.order.version>0</old.order.version>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <lib.version>-SNAPSHOT</lib.version>
                <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
                <old.order.version>0</old.order.version>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <!-- 生产环境版本 -->
                <lib.version></lib.version>
                <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
                <old.order.version>1</old.order.version>
            </properties>
        </profile>
    </profiles>


</project>
