package com.jdi.isc.task.center.msg.mail;

import com.alibaba.fastjson.JSONObject;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;

import com.jdi.isc.task.center.api.common.enums.MailModelTypeEnum;
import com.jdi.isc.task.center.domain.enums.MailMessageTypeEnum;
import com.jdi.isc.task.center.domain.mail.biz.MailMessageVO;
import com.jdi.isc.task.center.domain.settlement.SettlementOrderVO;
import com.jdi.isc.task.center.service.manage.mail.MailManagerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class SettlementCreateListener {

    @Resource
    private MailManagerService mailManagerService;

    private static final Map<String, String> statusToModelTypeMap = new HashMap<>();

    //模板类型
    static {
        // 结算单创建得到创建模版
        statusToModelTypeMap.put("CREATE", MailModelTypeEnum.SETTLEMENT_CREATE.getCode());
    }

    public enum SettlementStatus { CREATE, APPROVED, REJECTED }

    @JmqListener(id = "jdiIscTaskCenterConsumer", topics = {"${topic.jmq4.consumer.settlementOrder.create}"})
    public void onMessage(List<Message> messages) throws Exception {

        for (Message msg : messages) {

            if (Objects.isNull(msg) || Strings.isBlank(msg.getText())) {
                log.error("【结算单邮件发送异常】:消息数据为空，已忽略");
                continue;
            }
            //消息反序列化
            SettlementOrderVO settlementOrderVO = null;

            try {
                settlementOrderVO = JSONObject.parseObject(msg.getText(), SettlementOrderVO.class);
            } catch (Exception e) {
                log.error("【结算单邮件发送异常】:结算单消息反序列化失败，疑似上游消息格式发生变更。原始消息内容: {},异常信息: {},e:", msg.getText(), e.getMessage(), e);
                continue;
            }


            //如果接受结算单消息的时候，结算单状态为已创建，则发送结算单创建的邮件
            if (!Objects.equals(SettlementStatus.CREATE.name(), settlementOrderVO.getSaveOrUpdate())) {
                continue;
            }
            //必要性参数校验如果校验不通过，则不发送邮件

            if (StringUtils.isBlank(settlementOrderVO.getSupplierId())
                    || StringUtils.isBlank(settlementOrderVO.getRequestId())) {
                log.error("【结算单邮件发送异常】:结算单上游消息校验不通过，需人工介入结算单信息: {}", settlementOrderVO);
                continue;
            }


            //构建参数
            MailMessageVO mailMessageVO = buildParam(settlementOrderVO);
            //发送邮件
            try {
                mailManagerService.sendMail(mailMessageVO);
            } catch (Exception e) {
                log.error("【结算单邮件发送异常】SettlementCreateListener.onMessage param:{},err:{},e:", msg.getText(), e.getMessage(), e);
                throw e;
            }
        }
    }

    private MailMessageVO buildParam(SettlementOrderVO settlementOrderVO) {
        MailMessageVO mailMessage = new MailMessageVO();
        mailMessage.setBusinessCode(settlementOrderVO.getRequestId());
        mailMessage.setCountryCode(settlementOrderVO.getCountryCode());
        mailMessage.setMessageType(MailMessageTypeEnum.INSTANT_MESSAGE.getCode());//及时发送
        mailMessage.setOperator("SettlementCreateListener_JMQ");
        mailMessage.setModelType(getModelType(settlementOrderVO.getSaveOrUpdate()));
        mailMessage.setSupplierCode(settlementOrderVO.getSupplierId());
        return mailMessage;
    }

    private String getModelType(String status) {
        return statusToModelTypeMap.getOrDefault(status, null);
    }
}
