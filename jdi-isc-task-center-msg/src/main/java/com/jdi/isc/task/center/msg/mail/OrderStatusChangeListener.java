package com.jdi.isc.task.center.msg.mail;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.isc.task.center.api.common.enums.MailModelTypeEnum;
import com.jdi.isc.task.center.api.dto.mail.MailParamDTO;
import com.jdi.isc.task.center.domain.enums.MailMessageTypeEnum;
import com.jdi.isc.task.center.domain.mail.biz.MailMessageVO;
import com.jdi.isc.task.center.domain.settlement.OrderUpdateEmailVO;
import com.jdi.isc.task.center.service.manage.mail.MailManagerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 类描述：
 * 客户确定订单后发送到指定的人，通过DUCC配置收件人
 *
 * <AUTHOR>
 * @date 2025/8/21
 */

@Slf4j
@Component
public class OrderStatusChangeListener {

    @Resource
    private MailManagerService mailManagerService;

    @LafValue("jdi.isc.task.mail.order.cfg")
    private String orderEmailCfg;


    private static final String ORDER_ID = "newOrderId";
    private static final String PIN_NAME = "pin";


    @JmqListener(id = "jdiIscTaskCenterConsumer", topics = {"${topic.jmq4.consumer.updateOrder.create}"})
    public void onMessage(List<Message> messages) throws Exception {
            for (Message msg : messages) {
        if (Objects.isNull(msg) || Strings.isBlank(msg.getText())) {
            log.error("【客户订单状态变更邮件发送异常】:消息数据为空，已忽略");
            return;
        }

        // 消息反序列化
        OrderUpdateEmailVO orderUpdateEmailVO = null;
        try {
            orderUpdateEmailVO = JSONObject.parseObject(msg.getText(), OrderUpdateEmailVO.class);
        } catch (Exception e) {
            log.error("【客户订单状态变更邮件发送异常】:客户订单消息反序列化失败，疑似上游消息格式发生变更。原始消息内容: {},异常信息: {},e:", msg.getText(), e.getMessage(), e);
            return;
        }
        //如果消息的订单状态不为3，直接返回
        if (orderUpdateEmailVO.getOrderStatus() != 3) {
            return;
        }

        // 获取是否存在可以发送邮件的对象
        JSONObject tempObj=getDuccContent(orderEmailCfg,orderUpdateEmailVO);
        if(tempObj==null){
            log.error("【客户订单状态变更邮件发送异常】:没有可用的基于国家的邮件配置信息，查看DUCC配置项: {}", msg.getText());
            return;
        }

        //发送邮件
        MailMessageVO mailMessageVO = buildParam(orderUpdateEmailVO,tempObj);
        try {
            mailManagerService.sendMail(mailMessageVO);
        } catch (Exception e) {
            log.error("【客户订单状态变更邮件发送异常】OrderStatusChangeListener.onMessage param:{},err:{},e:", msg.getText(), e.getMessage(), e);
            throw e;
        }
    }

}

    /*
    构建发送邮件列表
     */
    private MailMessageVO buildParam(OrderUpdateEmailVO vo,JSONObject obj1) {
        MailMessageVO mailMessage = new MailMessageVO();
        mailMessage.setBusinessCode(vo.getOrderId().toString()+"-"+MailModelTypeEnum.SEND_ORDER_STATUS_NOTICE.getCode());
        mailMessage.setCountryCode(vo.getCountryCode());
        mailMessage.setModelType(MailModelTypeEnum.SEND_ORDER_STATUS_NOTICE.getCode());
        mailMessage.setMessageType(MailMessageTypeEnum.INSTANT_MESSAGE.getCode());//及时发送
        mailMessage.setOperator("OrderStatusChangeListener_JMQ");
        //构建发送人列表
        MailParamDTO mailParamDTO = new MailParamDTO();
        mailParamDTO.setRecipientEmail(obj1.getString("recipientEmail"));
        mailParamDTO.setCopyToMail(obj1.getString("copyToMail"));
        mailParamDTO.setSendData(createContext(vo.getOrderId().toString(),vo.getPin()));
        mailMessage.setMailParamDTO(mailParamDTO);
        return mailMessage;
    }


    /*
    根据配置文件中的信息与消息进行比对，返回存在的对象
     */
    private JSONObject getDuccContent(String  duccContent,OrderUpdateEmailVO vo) {

        //获取配置文件中的国家信息
        JSONArray jsonArray = JSONArray.parseArray(duccContent);
        JSONObject jsonObject =new JSONObject();
        // 遍历JSONArray,确认存在对应的国家信息，且该国家开启邮件发送状态
        for (int i = 0; i < jsonArray.size(); i++) {
            jsonObject = jsonArray.getJSONObject(i);

            String countryCodeCfg = jsonObject.getString("countryCode");
            String enabled = jsonObject.getString("enabled");
            if (StringUtils.equals(vo.getCountryCode(), countryCodeCfg) && enabled.equals("true"))
            {
                return jsonObject;
            }
        }
        return null;
    }

    public Map<String, Object> createContext(String OrderId, String pinName) {
        Map<String, Object> context = new HashMap<>();
        //替换模版中的结算单号
        context.put(ORDER_ID, OrderId);
        //替换模版中的供应商名称
        context.put(PIN_NAME, pinName);
        return context;
    }

}