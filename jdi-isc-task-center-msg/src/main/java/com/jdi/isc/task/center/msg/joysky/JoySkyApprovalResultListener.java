package com.jdi.isc.task.center.msg.joysky;

import com.alibaba.fastjson.JSONObject;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jdi.isc.task.center.api.joysky.biz.enums.JoySkyBizFlowTypeEnum;
import com.jdi.isc.task.center.domain.joysky.biz.JoySkyApplyResultDTO;
import com.jdi.isc.task.center.domain.joysky.biz.JoySkyMessageVO;
import com.jdi.isc.task.center.domain.joysky.po.JoySkyApprovalTaskPO;
import com.jdi.isc.task.center.service.atomic.joysky.JoySkyTaskAtomicService;
import com.jdi.isc.task.center.service.manage.joysky.JoySkyApprovalResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/11/1
 * @Description 审批流程结果订阅消费处理类
 */
@Slf4j
@Component
public class JoySkyApprovalResultListener {

    @Resource
    private JoySkyTaskAtomicService joySkyTaskAtomicService;

    @Resource
    private JoySkyApprovalResultService joySkyApprovalResultService;


    @JmqListener(id = "jdiIscTaskCenterConsumer", topics = {"${topic.jmq4.consumer.joySky.common}"})
    public void onMessage(List<Message> messages) {
        for (Message msg : messages) {
            try {
                if (Objects.isNull(msg) || Strings.isBlank(msg.getText())) {
                    log.error("消息为空，自动过滤");
                    return;
                }

                JoySkyMessageVO joySkyMessage = JSONObject.parseObject(msg.getText(), JoySkyMessageVO.class);
                String processKey = joySkyMessage.getProcessDefinitionKey();
                String processInstanceId = joySkyMessage.getProcessInstanceId();
                String processStatus = joySkyMessage.getProcessStatus();
                String actionType = joySkyMessage.getOperateType();

                //订阅过滤
                Set<String> processDefinitionKeyConfigs = JoySkyBizFlowTypeEnum.getAllProcessKeys();
                if (!processDefinitionKeyConfigs.contains(processKey)) {
                    log.info("JoySkyListener接收审批流MQ流程KEY不存在，非工业国际审批流直接过滤；流程processDefinitionKey:{}", processKey);
                    return;
                }
                log.info("JoySkyApprovalResultListener.onMessage  skip request:{}", JSONObject.toJSON(joySkyMessage));
                return;

//                //数据校验
//                JoySkyApprovalTaskPO skyApprovalTaskPO = joySkyTaskAtomicService.queryApprovalTaskByProcessId(processInstanceId);
//                if (Objects.isNull(skyApprovalTaskPO)) {
//                    // 流程不存在，直接返回
//                    log.info("流程不存在直接返回, 流程processInstanceId:{}, operateType:{}", processInstanceId, actionType);
//                    return;
//                }
//
//                // 构建结果请求参数
//                JoySkyApplyResultDTO joySkyApplyResultDTO = buildJoySkyApplyResultParam(skyApprovalTaskPO, processStatus, actionType, joySkyMessage);
//
//                switch (processStatus) {
//                    case "APPROVING":
//                        break;
//                    case "REJECT":
//                        break;
//                    case "ROLLBACK":
//                        break;
//                    case "CANCEL":
//                        break;
//                    case "REVOKE":
//                        break;
//                    case "END":
//                        joySkyApprovalResultService.approvalTaskReject(joySkyApplyResultDTO);
//                        break;
//                    case "FINISH":
//                        joySkyApprovalResultService.approvalTaskFinish(joySkyApplyResultDTO);
//                        break;
//                    default:
//                }

            } catch (Exception e) {
                log.error("JoySkyApprovalResultListener.onMessage Error param:{},e:", msg.getText(), e);
                throw e;
            }
        }
    }

    /**
     * 构建 JoySkyApplyResultDTO 对象，用于记录审批结果。
     *
     * @param skyApprovalTaskPO 审批任务PO对象，包含审批流程的基本信息。
     * @param processStatus     审批流程的当前状态。
     * @param actionType        审批操作类型。
     * @param joySkyMessage     审批消息VO对象，包含审批人的ERP信息和审批备注。
     * @return JoySkyApplyResultDTO 对象，记录了审批结果的详细信息。
     */
    private @NotNull JoySkyApplyResultDTO buildJoySkyApplyResultParam(JoySkyApprovalTaskPO skyApprovalTaskPO,
                                                                      String processStatus, String actionType, JoySkyMessageVO joySkyMessage) {
        JoySkyApplyResultDTO joySkyApplyResultDTO = new JoySkyApplyResultDTO();
        skyApprovalTaskPO.setProcessStatus(processStatus);
        joySkyApplyResultDTO.setJoySkyApprovalTaskPO(skyApprovalTaskPO);
        joySkyApplyResultDTO.setActionType(actionType);
        joySkyApplyResultDTO.setApproverErp(joySkyMessage.getAssignee() != null ? joySkyMessage.getAssignee().getErp() : "");
        joySkyApplyResultDTO.setApproverRemark(joySkyMessage.getTaskComment());
        return joySkyApplyResultDTO;
    }
}
