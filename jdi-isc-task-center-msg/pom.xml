<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jdi.isc.task.center</groupId>
        <artifactId>jdi-isc-task-center</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>jdi-isc-task-center-msg</artifactId>
    <packaging>jar</packaging>

    <name>jdi-isc-task-center-msg</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.jd.jmq</groupId>
            <artifactId>jmq2-client-springboot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.task.center</groupId>
            <artifactId>jdi-isc-task-center-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.task.center</groupId>
            <artifactId>jdi-isc-task-center-service</artifactId>
        </dependency>
    </dependencies>
</project>
