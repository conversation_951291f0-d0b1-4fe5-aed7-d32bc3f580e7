<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.jdi.isc.task.center</groupId>
        <artifactId>jdi-isc-task-center</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>jdi-isc-task-center-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>jdi-isc-task-center-service</name>
    <packaging>jar</packaging>

    <properties>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.ump</groupId>
            <artifactId>profiler</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.pfinder</groupId>
            <artifactId>pfinder-profiler-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.task.center</groupId>
            <artifactId>jdi-isc-task-center-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.task.center</groupId>
            <artifactId>jdi-isc-task-center-repository</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.task.center</groupId>
            <artifactId>jdi-isc-task-center-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.task.center</groupId>
            <artifactId>jdi-isc-task-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jmq</groupId>
            <artifactId>jmq-client-springboot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mashape.unirest</groupId>
            <artifactId>unirest-java</artifactId>
        </dependency>
        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.jim.cli</groupId>
            <artifactId>jim-cli-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.common</groupId>
            <artifactId>jdi-common-frame-spring-boot-starter</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>easyexcel</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.jdi.isc.task.center</groupId>
            <artifactId>jdi-isc-task-center-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.order.center</groupId>
            <artifactId>jdi-isc-order-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.laf.config</groupId>
            <artifactId>laf-config-client-jd-springboot-starter</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.leo.mail</groupId>
            <artifactId>product_produce-rpc-service</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.product.soa</groupId>
            <artifactId>jdi-isc-product-soa-price-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
        </dependency>

    </dependencies>
</project>
