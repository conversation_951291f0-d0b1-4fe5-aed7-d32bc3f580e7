package com.jdi.isc.task.center.service.work.task.handler.wimpExport;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.finance.customerInvoice.CustomerInvoiceReadApiService;
import com.jdi.isc.order.center.api.finance.customerInvoice.req.CustomerInvoicePageReqDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceApplyDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceApprovalExtDTO;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskStatusEnum;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.library.common.enmus.file.FileTypeEnum;
import com.jdi.isc.task.center.common.exception.TaskExportException;
import com.jdi.isc.task.center.common.utils.ZipUtils;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;
import com.jdi.isc.task.center.domain.invoice.ExportOrderInvoiceDTO;
import com.jdi.isc.task.center.domain.task.vo.TaskStrategyVO;
import com.jdi.isc.task.center.service.work.task.frame.BaseExecutableAbsJob;
import com.jdi.isc.task.center.service.work.task.frame.JobExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
@JobExecutor(taskBizType = TaskBizTypeEnum.ORDER_INVOICE_URL_EXPORT)
public class BatchAbleOrderInvoiceExportHandler extends BaseExecutableAbsJob<ExportOrderInvoiceDTO> {

    @Autowired
    private CustomerInvoiceReadApiService customerInvoiceReadApiService;

    @Override
    public void export(TaskDTO<ExportOrderInvoiceDTO> task) {
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.task.center.service.work.task.handler.wimpExport.BatchAbleOrderInvoiceExportHandler.export");
        try {
            if(task == null){
                return;
            }
            log.info("BatchAbleOrderInvoiceExportHandler.export task={}", JSONObject.toJSONString(task));
            TaskStrategyVO taskStrategyVO = task.getTaskStrategyVO();
            TaskStrategyVO taskPo = this.getStrategyService(taskStrategyVO.getTaskStrategyEnum()).getTask(task.getTaskId());
            if (Objects.nonNull(taskPo) && TaskStatusEnum.FAILURE.equals(taskPo.getStatus())){
                log.info("BatchAbleOrderInvoiceExportHandler.export 任务已经失败，跳过执行。 taskId={}" , task.getTaskId());
                return;
            }

            try {

                CustomerInvoicePageReqDTO reqDTO = JSON.parseObject(task.getReqJson(), CustomerInvoicePageReqDTO.class);
                reqDTO.setSize(200L);
                reqDTO.setIndex(1L);
                reqDTO.setSystemCode("task-center");
                reqDTO.setServiceIp("127.0.0.1");
                reqDTO.setLocaleList(Lists.newArrayList(LangConstant.LANG_ZH));
                DataResponse<PageInfo<CustomerInvoiceApplyDTO>> pageInfoDataResponse = customerInvoiceReadApiService.invoicePage(reqDTO);
                PageInfo<CustomerInvoiceApplyDTO> data = pageInfoDataResponse.getData();
                if(data.getSize() == 0){
                    log.info("BatchAbleOrderInvoiceExportHandler.export 数据为空,跳过执行。 taskId={}" , task.getTaskId());
                    return ;
                }

                List<CustomerInvoiceApplyDTO> invoiceApplyDTOList = data.getRecords().stream().filter(invoice -> {
                            CustomerInvoiceApprovalExtDTO approvalExtVO = invoice.getApprovalExtVO();
                            if (approvalExtVO == null) {
                                return false;
                            }
                            return CollectionUtils.isNotEmpty(approvalExtVO.getOtherUrl()) || StringUtils.isNotBlank(approvalExtVO.getInvUrl());
                        }
                ).collect(Collectors.toList());

                if(CollectionUtils.isEmpty(invoiceApplyDTOList)){
                    log.info("BatchAbleOrderInvoiceExportHandler.export 发票数据为空,跳过执行。 taskId={}" , task.getTaskId());
                    return ;
                }

                String s3Url = downloadInvoiceUrl2Stream(invoiceApplyDTOList);
                task.setEndTime(System.currentTimeMillis());

                if(StringUtils.isNotBlank(s3Url)){
                    task.setResultUrl(s3Url);
                    this.getStrategyService(taskStrategyVO.getTaskStrategyEnum()).update(new TaskStrategyVO(task, TaskStatusEnum.SUCCESS));
                }
            } catch (Exception e) {
                log.error("BatchAbleOrderInvoiceExportHandler.export error, taskId={} ", task.getTaskId(), e);
                throw new TaskExportException(task.getTaskId() + e.getMessage());
            } finally {
                log.info("BatchAbleOrderInvoiceExportHandler.export 任务「{}」执行完毕={}, 文件地址={}", task.getTaskId(), task.getOssPutMd5(), task.getResultUrl());
            }

        } catch (Exception e) {
            log.error("BatchAbleOrderInvoiceExportHandler.export, req = {}", JSON.toJSONString(task), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.error("BatchAbleOrderInvoiceExportHandler.export, req = {}", JSON.toJSONString(task));
            Profiler.registerInfoEnd(callerInfo);
        }
    }


    public String downloadInvoiceUrl2Stream(List<CustomerInvoiceApplyDTO> invoiceApplyDTOList) throws IOException {
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.task.center.service.work.task.handler.wimpExport.BatchAbleOrderInvoiceExportHandler.downloadInvoiceUrl2Stream");
        // 压缩文件夹的名称
        String folderName = "";
        // 压缩后的ZIP文件名
        String zipName = "";
        // 压缩包路径
        String tempDir = null;
        Path tempDirPath = null;
        try {
            // 压缩包路径
            tempDirPath = Files.createTempDirectory(TaskBizTypeEnum.ORDER_INVOICE_URL_EXPORT.getName());
            tempDir = tempDirPath.toString();


            for(CustomerInvoiceApplyDTO customerInvoiceApplyDTO : invoiceApplyDTOList) {
                CustomerInvoiceApprovalExtDTO approvalExtVO = customerInvoiceApplyDTO.getApprovalExtVO();
                String invUrl = approvalExtVO.getInvUrl();
                if(StringUtils.isNotBlank(invUrl)){
                    downloadFile(invUrl, getFileNameByUrl(invUrl), tempDir);
                }

                List<String> otherUrl = approvalExtVO.getOtherUrl();
                AtomicInteger index = new AtomicInteger(0);
                if(CollectionUtils.isNotEmpty(otherUrl)){
                    for(String url : otherUrl) {
                        if(StringUtils.isBlank(url)){
                            continue;
                        }
                        if(index.intValue() == 0) {
                            downloadFile(url, getFileNameByUrl(url), tempDir);
                        }else {
                            downloadFile(url, getFileNameByUrl(url) + Constant.UNDER_LINE + index.intValue(), tempDir);
                        }
                        index.incrementAndGet();
                    }
                }
            }


            zipName = tempDirPath + Constant.DOT + Constant.ZIP;
            ZipUtils.compressFolderToZip(tempDir, folderName, zipName);
            // 上传压缩包
            return s3Utils.upload(zipName, FileTypeEnum.BATCH_FILE.getCode());
        } catch (Exception e) {
            log.error("BatchAbleOrderInvoiceExportHandler.downloadInvoiceUrl2Stream, req = {}", JSON.toJSONString(invoiceApplyDTOList), e);
            Profiler.functionError(callerInfo);
            throw new TaskExportException(e.getMessage());
        } finally {
            if(tempDirPath != null) {
                FileUtils.deleteDirectory(tempDirPath.toFile());
            }
            log.error("BatchAbleOrderInvoiceExportHandler.downloadInvoiceUrl2Stream, req = {}", JSON.toJSONString(invoiceApplyDTOList));
            Profiler.registerInfoEnd(callerInfo);
        }

    }

    private static void downloadFile(String fileUrl, String fileName, String downloadDir) throws IOException {
        URL url = new URL(fileUrl);
        File destination = new File(downloadDir, fileName);
        FileUtils.copyURLToFile(url, destination);
    }

    private static String getFileNameByUrl(String url){
        url = url.substring(0, url.indexOf(".pdf"));
        url = url.substring(url.lastIndexOf("/") + 1);
        url = url.substring(url.indexOf("_") + 1);
        return url + ".pdf";
    }

    public static void main(String[] args) {
        System.out.println(getFileNameByUrl("https://storage.jd.com/fm-international/2_inv202409001268-TH660520240000000569-original.pdf?Expires=2727416682&AccessKey=ZGiaIQAvJwpB4ZSG&Signature=bA1J%2F1c5uFyaY31dDLX0z%2BXxldY%3D"));
        System.out.println(getFileNameByUrl("https://storage.jd.com/fm-international/2_inv202409001268-TH660520240000000569-copy.pdf?Expires=2727416682&AccessKey=ZGiaIQAvJwpB4ZSG&Signature=gEHZu%2BraqayHex%2BofN%2BU1jd7%2Fxk%3D"));
    }

}
