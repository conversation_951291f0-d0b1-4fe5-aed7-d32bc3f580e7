package com.jdi.isc.task.center.service.work.task.handler.pdf;

import com.alibaba.fastjson.JSONObject;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPTable;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.library.common.enmus.file.FileTypeEnum;
import com.jdi.isc.order.center.api.forecast.biz.req.ForecastDetailOrderApiReq;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderDetailResp;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.common.frame.AbsExecutor;
import com.jdi.isc.task.center.common.utils.HostUtil;
import com.jdi.isc.task.center.domain.forecast.*;
import com.jdi.isc.task.center.rpc.country.CountryRpcService;
import com.jdi.isc.task.center.rpc.forecast.ForecastPurchaseOrderRpcService;
import com.jdi.isc.task.center.service.adapter.mapstruct.forecast.ForecastPurchaseOrderConvert;
import com.jdi.isc.task.center.service.pdf.BaseExecutableService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static java.nio.file.StandardOpenOption.DELETE_ON_CLOSE;

/**
 * 预报备采购单pdf打印
 *
 * <AUTHOR>
 * @description 预报备采购单pdf打印
 * @date 2025/5/28
 */
@Slf4j
@Service
public class ForecastPurchaseOrderPdfService extends BaseExecutableService<ForecastPurchaseOrderTaskPdfVO, ForecastPurchaseOrderTaskVO> {

    @Resource
    private ForecastPurchaseOrderRpcService forecastPurchaseOrderRpcService;

    @Resource
    private CountryRpcService countryRpcService;

    @Value("${order.systemCode}")
    private String systemCode;

    private static  final String TITLE = "预报备货入库单/Pre-order stock entry";

    @Override
    public AbsExecutor<String> of(ForecastPurchaseOrderTaskVO input) {
       return  () -> {
           ForecastPurchaseOrderTaskPdfVO pdfVO = buildInput(input);
           return buildPdf(pdfVO);
        };
    }

    @Override
    @ToolKit
    public ForecastPurchaseOrderTaskPdfVO buildInput(ForecastPurchaseOrderTaskVO input) {
        List<String> langList = new ArrayList<>();
        langList.add(LangConstant.LANG_EN);
        langList.add(LangConstant.LANG_ZH);
        ForecastDetailOrderApiReq apiReq = new ForecastDetailOrderApiReq();
        apiReq.setForecastOrderId(input.getForecastOrderId());
        apiReq.setSupplierCode(input.getSupplierCode());
        apiReq.setLocaleList(langList);
        apiReq.setSystemCode(systemCode);
        apiReq.setServiceIp(HostUtil.getLocalIP());
        ForecastOrderDetailResp forecastOrderDetailResp = forecastPurchaseOrderRpcService.queryForecastOrderDetail(apiReq);
        ForecastPurchaseOrderTaskPdfVO pdfVO = ForecastPurchaseOrderConvert.INSTANCE.res2Vo(forecastOrderDetailResp);
        //查询收货国家 所属的语言
        List<ForecastOrderConsigneeVO> consigneeVOList = pdfVO.getForecastOrderConsignee().getConsigneeVOList();
        if (CollectionUtils.isNotEmpty(consigneeVOList)) {
            String countryLang = countryRpcService.getLangByCountryCode(consigneeVOList.get(0).getConsigneeCountry());
            pdfVO.setCountryLang(countryLang);
            langList.add(0, countryLang);
        }
        pdfVO.setLocaleList(langList);
        return pdfVO;
    }

    @Override
    public String buildPdf(ForecastPurchaseOrderTaskPdfVO input) {

        if (Objects.isNull(input)) {
            throw new IllegalArgumentException("预报备采购单数据不存在");
        }
        Document document = null;
        try{
            Path tempDir = Files.createTempDirectory(null);
            String tmpPath = Paths.get(tempDir + File.separator + input.getForecastOrderId()+".pdf").toString();
            String outPath = Paths.get(tempDir + File.separator + input.getForecastOrderId() + Constant.UNDER_LINE + System.currentTimeMillis() + Constant.PDF).toString();
            document = new Document(PageSize.A4, 0, 0, 80, 60);
            loadHeaderAndFooter(document, tmpPath,super.getBarcode(input.getForecastOrderId()));
            document.open();
            //标题
            getParagraph(document, 10, null, null, null, Element.ALIGN_CENTER, new Chunk(TITLE, resourceContainer.getTitleFont()));
            //预报备采购单信息
            document.add(getForecastPurchaseOrderTable(input));
            //商品信息
            document.add(getSkuTable(input.getForecastOrderWareList(),input.getLocaleList()));
            //签字内容
            document.add(addSignText(input.getUpdater()));
            //注意事项
            document.add(addPrecautionText());
            document.newPage();
            document.close();
            if (jdPathCheck(tmpPath) && jdPathCheck(outPath)) {
                addWaterMarkToPdf(Files.newInputStream(Paths.get(tmpPath), DELETE_ON_CLOSE), Files.newOutputStream(Paths.get(outPath)), input.getUpdater());
            } else {
                throw new RuntimeException("您发送请求中的参数中含有非法字符");
            }

            log.info("ForecastPurchaseOrderPdfService.buildPdf path:{} ", outPath);
           return s3Utils.upload(outPath, FileTypeEnum.VC_PURCHASE_ORDER_PDF.getCode());
        }catch (Exception e){
            log.error("ForecastPurchaseOrderPdfService buildPdf error,param:{},e:", JSONObject.toJSONString(input),e);
            throw new RuntimeException(e);
        }
    }

    private PdfPTable getForecastPurchaseOrderTable(ForecastPurchaseOrderTaskPdfVO input) {
        ForecastOrderConsigneeInfoVO consigneeApiDTO = input.getForecastOrderConsignee();
        ForecastOrderConsigneeVO consigneeVO = consigneeApiDTO.getConsigneeVOList().get(0);
        PdfPTable forecastPurchaseOrder = new PdfPTable(new float[]{1, 1, 1, 1});
        forecastPurchaseOrder.setWidthPercentage(92);
        //第一行
        forecastPurchaseOrder.addCell(cell("备货仓编码 \nWarehouse Code", resourceContainer.getTenSizeFont()));
        forecastPurchaseOrder.addCell(cell(input.getWarehouseNo(), resourceContainer.getBigFont()));
        forecastPurchaseOrder.addCell(cell("备货仓名称 \nWarehouse Name", resourceContainer.getTenSizeFont()));
        forecastPurchaseOrder.addCell(cell(input.getWarehouseName(), resourceContainer.getBigFont()));
        //第二行
        forecastPurchaseOrder.addCell(cell("预报备货单 \nPre-order NO", resourceContainer.getTenSizeFont()));
        forecastPurchaseOrder.addCell(cell(input.getForecastOrderId(), resourceContainer.getBigFont()));
        forecastPurchaseOrder.addCell(cell("入库单号 \nInventory NO", resourceContainer.getTenSizeFont()));
        forecastPurchaseOrder.addCell(cell(input.getEnterWarehouseNo(), resourceContainer.getBigFont()));
        //第三行
        forecastPurchaseOrder.addCell(cell("收件人 \nConsignee", resourceContainer.getBigFont()));
        forecastPurchaseOrder.addCell(cell(consigneeVO.getConsigneeEncrypt(), resourceContainer.getBigFont()));
        forecastPurchaseOrder.addCell(cell("手机 \nTel", resourceContainer.getBigFont()));
        forecastPurchaseOrder.addCell(cell(consigneeVO.getConsigneeMobileEncrypt(), resourceContainer.getBigFont()));
        //第四行
        forecastPurchaseOrder.addCell(cell("详细地址 \nDetailed Address", resourceContainer.getBigFont()));
        forecastPurchaseOrder.addCell(cell(buildFullConsigneeAddress(consigneeApiDTO,input.getCountryLang()),resourceContainer.getBigFont(),3));
        forecastPurchaseOrder.setSpacingBefore(8);
        return forecastPurchaseOrder;
    }

    /**
     * 构建完整的详细地址
     *
     * @param consigneeApiDTO 地址信息
     * @param lang
     * @return 完整的详细地址
     */
    private String buildFullConsigneeAddress(ForecastOrderConsigneeInfoVO consigneeApiDTO, String lang) {
        StringBuilder address = new StringBuilder(consigneeApiDTO.getConsigneeAddressEncrypt());
        List<ForecastOrderConsigneeVO> consigneeVOList = consigneeApiDTO.getConsigneeVOList();
        ForecastOrderConsigneeVO consigneeVO = consigneeVOList.get(0);
        //区
        if (MapUtils.isNotEmpty(consigneeVO.getCountyNameMap())) {
            String countName = consigneeVO.getCountyNameMap().get(lang);
            if (StringUtils.isBlank(countName)) {
                countName = consigneeVO.getCountyNameMap().get(LangConstant.LANG_EN);
            }
            address.append(",").append(countName);
        }
        //市
        if (MapUtils.isNotEmpty(consigneeVO.getCityNameMap())) {
            String cityName = consigneeVO.getCityNameMap().get(lang);
            if (StringUtils.isBlank(cityName)) {
                cityName = consigneeVO.getCityNameMap().get(LangConstant.LANG_EN);
            }
            address.append(",").append(cityName);
        }
        //省
        if (MapUtils.isNotEmpty(consigneeVO.getProvinceNameMap())) {
            String proviceName = consigneeVO.getProvinceNameMap().get(lang);
            if (StringUtils.isBlank(proviceName)) {
                proviceName = consigneeVO.getProvinceNameMap().get(LangConstant.LANG_EN);
            }
            address.append(",").append(proviceName);
        }

        //国家
        if (MapUtils.isNotEmpty(consigneeVO.getCountryNameMap())) {
            String countryName = consigneeVO.getCountryNameMap().get(lang);
            if (StringUtils.isBlank(countryName)) {
                countryName = consigneeVO.getCityNameMap().get(LangConstant.LANG_EN);
            }
            address.append(",").append(countryName);
        }
        return address.toString();
    }

    /**
     * 商品信息
     */
    private PdfPTable getSkuTable(List<ForecastOrderWareVO> wareList, List<String> localeList) {
        PdfPTable pdfPTable = new PdfPTable(new float[]{1f, 3f, 8f, 1f});
        pdfPTable.setWidthPercentage(92);
        pdfPTable.addCell(cell("序号 \nNo.", resourceContainer.getBigFont()));
        pdfPTable.addCell(cell("国际商品编号 \nINT Item ID", resourceContainer.getBigFont()));
        pdfPTable.addCell(cell("品名 \nItem Name", resourceContainer.getBigFont()));
        pdfPTable.addCell(cell("数量 \nQty", resourceContainer.getBigFont()));
        for (int i = 0; i < wareList.size(); i++) {
            pdfPTable.addCell(midCell(String.valueOf(i + 1)));
            pdfPTable.addCell(cell(String.valueOf(wareList.get(i).getSkuId())));
            //三语
            ForecastOrderWareVO wareVO = wareList.get(i);
            StringBuilder sb = new StringBuilder();
            localeList.forEach(l -> {
                String skuName = wareVO.getSkuNameMap().get(l);
                if (StringUtils.isNotBlank(skuName)) {
                    sb.append(skuName).append("\n");
                }
            });
            pdfPTable.addCell(chunkCell(sb.toString()));
            pdfPTable.addCell(midCell(String.valueOf(wareList.get(i).getSkuNum())));
        }
        pdfPTable.setSpacingBefore(8);
        return pdfPTable;
    }

    private Element addSignText(String erp) {
        PdfPTable orderTable = new PdfPTable(new float[]{0.18f, 0.32f, 0.3f, 0.2f});
        orderTable.setWidthPercentage(92);
        orderTable.addCell(noBorderCell("制单人 /Prepared by:", resourceContainer.getTenSizeFont()));
        orderTable.addCell(noBorderCell(erp, resourceContainer.getTenSizeFont()));
        orderTable.addCell(noBorderCell("签收人 /Recipient:", resourceContainer.getTenSizeFont()));
        orderTable.addCell(noBorderCell("", resourceContainer.getTenSizeFont()));
        orderTable.setSpacingBefore(8);
        orderTable.setSpacingAfter(60);
        return orderTable;
    }

    private Element addPrecautionText() {
        PdfPTable table = new PdfPTable(new float[]{220, 60, 220, 60, 220, 60});
        table.setWidthPercentage(92);
        List<String> notes = createNoteStrList();
        for (String note : notes) {
            table.addCell(createPdfPCell(note, resourceContainer.getSmallFont(), 1, 6, true, false));
        }
        table.setSpacingBefore(8);
        return table;
    }

    private List<String> createNoteStrList() {
        List<String> strList = new ArrayList<>();
        strList.add("注意事项 / Notes:");
        strList.add("1. 请仔细核对入库物品的数量和质量，确保与订单一致。");
        strList.add("Please carefully check the quantity and quality of the received items to ensure they match the order.");
        strList.add("2. 验收人员需在验收后签字确认。");
        strList.add("The inspector must sign to confirm after inspection.");
        strList.add("3. 审核人员需在审核后签字确认。");
        strList.add("The reviewer must sign to confirm after the review.");
        return strList;
    }
}