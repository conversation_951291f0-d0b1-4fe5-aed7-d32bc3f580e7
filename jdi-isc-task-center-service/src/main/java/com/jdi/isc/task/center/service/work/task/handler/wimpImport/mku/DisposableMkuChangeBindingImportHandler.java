package com.jdi.isc.task.center.service.work.task.handler.wimpImport.mku;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuReqDTO;
import com.jdi.isc.product.soa.api.mkuBinding.req.MkuChangeBindingReqDTO;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.common.utils.StrUtils;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;
import com.jdi.isc.task.center.domain.task.excel.MkuChangeBindingImportDTO;
import com.jdi.isc.task.center.rpc.mku.MkuRpcService;
import com.jdi.isc.task.center.service.work.task.DisposableAbsJob;
import com.jdi.isc.task.center.service.work.task.frame.JobExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 批量导入MKU变更绑定执行器
 * <AUTHOR>
 * @description：DisposableMkuChangeBindingImportHandler
 * @Date 2025-08-27
 */
@Slf4j
@Service
@JobExecutor(taskBizType = TaskBizTypeEnum.WIMP_MKU_CHANGE_BINDING_IMPORT)
public class DisposableMkuChangeBindingImportHandler extends DisposableAbsJob<MkuChangeBindingImportDTO> {

    @Resource
    private MkuRpcService mkuRpcService;

    @Override
    public void run(TaskDTO<MkuChangeBindingImportDTO> task) {
        List<MkuChangeBindingImportDTO> target = task.getTarget();
        if (CollectionUtils.isEmpty(target)) {
            log.error("DisposableMkuChangeBindingImportHandler.run 任务解析后数据为空，不能执行 taskId={}",task.getTaskId());
            return;
        }

        String operator = task.getOperator();

        Map<Long,Long> originSkuIdMkuMap = querySkuMkuMap(target);

        for (MkuChangeBindingImportDTO importDTO : target) {
            if (!importDTO.getValid()) {
                continue;
            }
            importDTO.setOriginSkuId(StrUtils.trimAll(importDTO.getOriginSkuId()));
            importDTO.setAfterSkuId(StrUtils.trimAll(importDTO.getAfterSkuId()));
            importDTO.setApplicant(importDTO.getApplicant());
            importDTO.setApplyReason(importDTO.getApplyReason());

            MkuChangeBindingReqDTO reqDTO = this.convertImportDTOToReqDTO(importDTO,operator,originSkuIdMkuMap);

            DataResponse<Boolean> response = mkuRpcService.mkuChangeBinding(reqDTO);
            if (null != response && !response.getSuccess()) {
                importDTO.failed(response.getMessage());
            }
        }
    }

    private Map<Long, Long> querySkuMkuMap(List<MkuChangeBindingImportDTO> target) {
        Map<Long, Long> originSkuIdMkuMap = Maps.newHashMap();
        List<Long> originSkuIds = target.stream()
                .filter(Objects::nonNull)
                .filter(MkuChangeBindingImportDTO::getValid)
                .filter(dto -> StringUtils.isNotBlank(dto.getOriginSkuId()))
                .map(dto -> StrUtils.trimAll(dto.getOriginSkuId()))
                .filter(Objects::nonNull).map(Long::valueOf).collect(Collectors.toList());
        BatchQueryMkuReqDTO mkuReqDTO = new BatchQueryMkuReqDTO();
        List<List<Long>> partition = Lists.partition(originSkuIds, 20);
        for (List<Long> skuIds : partition) {
            mkuReqDTO.setSkuIds(Sets.newHashSet(skuIds));
            originSkuIdMkuMap.putAll(mkuRpcService.getIscMkuIdBySkuId(mkuReqDTO));
        }
        return originSkuIdMkuMap;
    }


    private MkuChangeBindingReqDTO convertImportDTOToReqDTO(MkuChangeBindingImportDTO importDTO,String operator,Map<Long,Long> originSkuIdMkuMap) {
        MkuChangeBindingReqDTO reqDTO = new MkuChangeBindingReqDTO();
        reqDTO.setMkuId(originSkuIdMkuMap.get(Long.valueOf(importDTO.getOriginSkuId())));
        reqDTO.setOriginSkuId(Long.valueOf(importDTO.getOriginSkuId()));
        reqDTO.setAfterSkuId(Long.valueOf(importDTO.getAfterSkuId()));
        reqDTO.setApplicant(importDTO.getApplicant());
        reqDTO.setApplyReason(importDTO.getApplyReason());
        reqDTO.setOperator(operator);
        reqDTO.setTraceId(UUID.randomUUID().toString());
        return reqDTO;
    }
}
