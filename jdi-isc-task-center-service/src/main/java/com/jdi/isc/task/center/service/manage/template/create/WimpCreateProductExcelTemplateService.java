package com.jdi.isc.task.center.service.manage.template.create;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jdi.isc.product.soa.api.common.enums.AttributeTypeEnum;
import com.jdi.isc.product.soa.api.supplier.res.BrandResDTO;
import com.jdi.isc.product.soa.api.supplier.res.CategoryTreeResDTO;
import com.jdi.isc.task.center.common.costants.ProductConstant;
import com.jdi.isc.task.center.domain.excel.*;
import com.jdi.isc.task.center.rpc.brand.BrandRpcService;
import com.jdi.isc.task.center.rpc.category.RpcCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 供应商批量发品模板生成-对应的是跨境
 * <AUTHOR>
 * @date 2024/8/31
 **/
@Slf4j
@Service
public class WimpCreateProductExcelTemplateService extends ParentCreateProductExcelTemplateService {

    @Resource
    private BrandRpcService brandRpcService;
    @Resource
    private RpcCategoryService rpcCategoryService;
    /** 系统编码 */
    private String systemCode;
    /** 来源国家 */
    private String sourceCountryCode;

    @PostConstruct
    public void initExcelName(){
        super.setExcelName("CNBatchCreateProduct");
    }

    @Override
    protected List<TemplateSourceDataVO.SheetDataVO> getSheetDataVoList(TemplateReqVO reqVO) {
        log.info("WimpCreateProductExcelTemplateService.getSheetDataVoList reqVO={}",JSON.toJSONString(reqVO));
        super.initVal(reqVO);
        return super.getSheetDataVoList(reqVO);
    }

    @Override
    DropdownDataVO fillDropdownDataVO(TemplateHeadSupportVO headSupportVo) {
        List<List<String>> headList = headSupportVo.getHeadList();
        // 表头取第二行表头数据
        List<String> headStrList = Lists.newArrayList();
        for (List<String> subHead : headList) {
            headStrList.add(subHead.get(1));
        }

        Map<String,Integer> headTitleIndexMap = Maps.newHashMap();
        for (int i = 0,len = headStrList.size(); i < len; i++) {
            headTitleIndexMap.put(headStrList.get(i), i);
        }

        // 下拉数据
        log.info("ParentCreateProductExcelTemplateService.getSheetDataVoList 固定下拉数据表头 headStrList={},headTitleIndexMap={}",JSON.toJSONString(headStrList),JSON.toJSONString(headTitleIndexMap));
        List<DropdownDataVO.EachItemDataVO> fixEachDataList = Lists.newArrayList();
//        // 四级类目
//        fixEachDataList.add(new DropdownDataVO.EachItemDataVO(5, ProductConstant.FIX_CATEGORY_ID,headSupportVo.getFourthCategoryNameList()));
//        log.info("ParentCreateProductExcelTemplateService.getSheetDataVoList 固定下拉数据表头 四级类目={}",JSON.toJSONString(headSupportVo.getFourthCategoryNameList()));
//        // 品牌ID
//        fixEachDataList.add(new DropdownDataVO.EachItemDataVO(7,ProductConstant.FIX_BRAND_ID,headSupportVo.getBrandNameList()));
//        log.info("ParentCreateProductExcelTemplateService.getSheetDataVoList 固定下拉数据表头 品牌={}",JSON.toJSONString(headSupportVo.getBrandNameList()));
        // 销售单位
        fixEachDataList.add(new DropdownDataVO.EachItemDataVO(8 - errorIndex,ProductConstant.FIX_SALES_UNIT,headSupportVo.getSaleUnitList()));
        log.info("ParentCreateProductExcelTemplateService.getSheetDataVoList 固定下拉数据表头 销售单位={}",JSON.toJSONString(headSupportVo.getSaleUnitList()));


        List<DropdownDataVO.DynamicDataVO> dynamicDataVOList = Lists.newArrayList();
        Map<AttributeTypeEnum, TemplateAttrVO> attributeTypeMap = headSupportVo.getAttributeTypeMap();

//        if(Objects.nonNull(attributeTypeMap)) {
//            // 销售属性
//            log.info("ParentCreateProductExcelTemplateService.getSheetDataVoList attributeTypeMap attributeTypeMap={}",JSON.toJSONString(attributeTypeMap));
//            TemplateAttrVO templateAttrVO = attributeTypeMap.get(AttributeTypeEnum.SELL);
//            if (Objects.nonNull(templateAttrVO) && Objects.nonNull(templateAttrVO.getAttrNameMap())  &&  MapUtils.isNotEmpty(templateAttrVO.getAttrNameMap())) {
//                LinkedHashMap<String, List<String>> attrNameMap = templateAttrVO.getAttrNameMap();
//                int saleIndex = 25 + attrNameMap.size();
//                log.info("ParentCreateProductExcelTemplateService.getSheetDataVoList 动态下拉数据表头 销售属性索引={},销售属性key集合={}",
//                    saleIndex, JSON.toJSONString(attrNameMap.keySet()));
//                dynamicDataVOList.add(new DropdownDataVO.DynamicDataVO(saleIndex- errorIndex, attrNameMap));
//            }
//        }

        // 跨境属性
        LinkedHashMap<String, List<String>> allAttributeMap = Maps.newLinkedHashMap();
        TemplateAttrVO spuTemplateAttrVO = attributeTypeMap.get(AttributeTypeEnum.SPU_CROSS_BORDER);
        if (spuTemplateAttrVO != null && MapUtils.isNotEmpty(spuTemplateAttrVO.getAttrNameMap())){
            allAttributeMap.putAll(spuTemplateAttrVO.getAttrNameMap());
        }
        TemplateAttrVO skuTemplateAttrVO = attributeTypeMap.get(AttributeTypeEnum.SKU_CROSS_BORDER);
        if (skuTemplateAttrVO != null && MapUtils.isNotEmpty(skuTemplateAttrVO.getAttrNameMap())) {
            allAttributeMap.putAll(skuTemplateAttrVO.getAttrNameMap());
        }
        if (MapUtils.isNotEmpty(allAttributeMap)) {
            TemplateAttrVO extendTemplateAttrVO = attributeTypeMap.get(AttributeTypeEnum.EXTEND);
            int extendSize = 0;
            if (Objects.nonNull(extendTemplateAttrVO) && MapUtils.isNotEmpty(extendTemplateAttrVO.getAttrNameMap())){
                extendSize = extendTemplateAttrVO.getAttrNameMap().size();
            }
            int globalAttrIndex = headStrList.size() - extendSize;
            dynamicDataVOList.add(new DropdownDataVO.DynamicDataVO(globalAttrIndex- errorIndex, allAttributeMap));
        }

        // 扩展属性
        TemplateAttrVO extendTemplateAttrVO = attributeTypeMap.get(AttributeTypeEnum.EXTEND);
        if (extendTemplateAttrVO != null && MapUtils.isNotEmpty(extendTemplateAttrVO.getAttrNameMap())) {
            LinkedHashMap<String, List<String>> attrNameMap = extendTemplateAttrVO.getAttrNameMap();
            int extendIndex = headStrList.size();
            log.info("ParentCreateProductExcelTemplateService.getSheetDataVoList 动态下拉数据表头 扩展属性索引={},销售属性key集合={}",extendIndex,JSON.toJSONString(attrNameMap.keySet()));
            dynamicDataVOList.add(new DropdownDataVO.DynamicDataVO(extendIndex,attrNameMap));
        }

        return DropdownDataVO.builder()
                .fixedEndIndex(20)
                .maxRowNum(1000)
                .headList(headStrList)
                .eachItemDataVOList(fixEachDataList)
                .dynamicDataVOList(dynamicDataVOList).build();
    }

    @Override
    List<CategoryTreeResDTO> getCategoryTreeResDTO(String supplierCode,Long catId){
        return super.getCategoryTreeRes(supplierCode,catId);
    }

    @Override
    List<BrandResDTO> getBrandResDTO(String supplierCode, Long catId){
        return super.getBrandRes(supplierCode,catId);
    }


}
