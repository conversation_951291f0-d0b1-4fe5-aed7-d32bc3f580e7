package com.jdi.isc.task.center.service.protocol.jsf.joysky;

import com.alibaba.fastjson.JSONObject;
import com.jd.jdi.yz.bean.convert.util.AgileConvert;
import com.jd.process.api.ProcessSimpleOperateJsfService;
import com.jd.process.api.dto.StartProcess;
import com.jd.process.api.dto.simple.FormModel;
import com.jd.process.api.dto.simple.StartProcessParameters;
import com.jd.process.api.dto.simple.StartProcessResult;
import com.jd.process.api.enums.SourceCodeEnum;
import com.jd.process.api.query.StartProcessParamsQuery;
import com.jd.process.api.util.ApiSignUtil;
import com.jd.process.api.util.PackFormDataModelUtil;
import com.jd.process.api.vo.ApiResult;
import com.jd.process.api.vo.ApiSignInfo;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.task.center.api.joysky.JoySkyAppFlowApiService;
import com.jdi.isc.task.center.api.joysky.biz.enums.JoySkyBizFlowTypeEnum;
import com.jdi.isc.task.center.api.joysky.req.JoySkyCreateReqApiDTO;
import com.jdi.isc.task.center.api.joysky.res.JoySkyCreateRspApiDTO;
import com.jdi.isc.task.center.common.utils.DateUtil;
import com.jdi.isc.task.center.common.utils.Md5Utils;
import com.jdi.isc.task.center.service.manage.joysky.JoySkyTaskManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/10/31
 * @Description joySky审批流业务接口实现类
 */
@Slf4j
@Service
public class JoySkyAppFlowApiServiceImpl implements JoySkyAppFlowApiService {

    /**
     * JoySky应用的编码，用于API签名。
     */
    @Value("${joySky.appCode}")
    private String appCode;

    /**
     * JoySky应用的密钥，用于API签名。
     */
    @Value("${joySky.appSecret}")
    private String appSecret;


    /**
     * 环境变量，用于指定任务的运行环境。
     */
    @Value("${jdi.isc.task.env}")
    private String env;

    /**
     * ProcessSimpleOperateJsfService对象，用于调用joySky流程相关服务。
     */
    @Resource
    private ProcessSimpleOperateJsfService processSimpleOperateJsfService;

    /**
     * JoySky审批流任务管理服务，用于存储和管理审批流任务。
     */
    @Resource
    private JoySkyTaskManageService joySkyTaskManageService;


    @Override
    public DataResponse<JoySkyCreateRspApiDTO> createSkyApprovalFlow(JoySkyCreateReqApiDTO joySkyCreateReqApiDTO) {
        ApiResult<StartProcessResult> result = null;
        try {
            //审批业务类型获取
            JoySkyBizFlowTypeEnum skyBizFlowTypeEnum = JoySkyBizFlowTypeEnum.getByFlowTypeCode(joySkyCreateReqApiDTO.getJoySkyFlowType());

            // 先获取入参参数
            ApiResult<StartProcessParameters> startProcessParamsResult = getStartProcessParams(JoySkyBizFlowTypeEnum.getProcessKeyByEnv(joySkyCreateReqApiDTO.getJoySkyFlowType(), env),
                    null, joySkyCreateReqApiDTO.getErp());
            if (Objects.isNull(startProcessParamsResult)) {
                log.error("createSkyApprovalFlow, getStartProcessParams fail. input={}", JSONObject.toJSONString(joySkyCreateReqApiDTO));
                return DataResponse.error("审批流创建发生异常!");
            }
            StartProcessParameters startProcessParameters = startProcessParamsResult.getData();

            ApiSignInfo apiSignInfo = buildBaseApiSignInfo();
            StartProcess startProcess = AgileConvert.convert(apiSignInfo, StartProcess.class);
            startProcess.setProcessDefinitionKey(startProcessParameters.getProcessDefinitionKey());
            startProcess.setProcessDefinitionId(startProcessParameters.getProcessDefinitionId());
            startProcess.setBusinessKey(Md5Utils.randomCreator(10));
            startProcess.setSourceCode(SourceCodeEnum.JDI.getKey());
            startProcess.setAppId(startProcessParameters.getAppId());
            startProcess.setTenantId(startProcessParameters.getTenantId());
            startProcess.setFormBaseKey(startProcessParameters.getFormBaseKey());
            //表单字段填值
            startProcess.setFormDataModel(buildFormDataModel(startProcessParameters, skyBizFlowTypeEnum, joySkyCreateReqApiDTO.getProcessFromData()));
            startProcess.setIsMobile(false);
            startProcess.setCurrentUserErp(joySkyCreateReqApiDTO.getErp());
            startProcess.setProcessNodeConfig(new ArrayList<>());
            //流程分支变量
            startProcess.setVariables(joySkyCreateReqApiDTO.getProcessControlData());


            log.info("startOrReStartProcess env:{}, request:{}", env, JSONObject.toJSONString(startProcess));
            result = processSimpleOperateJsfService.startOrReStartProcess(startProcess);
            if (!result.getSuccess() || result.getData() == null) {
                log.error("createSkyApprovalFlow, invokeResult fail. input={}", JSONObject.toJSONString(joySkyCreateReqApiDTO));
                return DataResponse.error(result.getMessage());
            }

            // 执行流程后获取流程ID等信息
            JoySkyCreateRspApiDTO joySkyCreateRspApiDTO = AgileConvert.convert(result.getData(), JoySkyCreateRspApiDTO.class);
            //存储审批流任务
            joySkyTaskManageService.saveApprovalTask(joySkyCreateReqApiDTO, joySkyCreateRspApiDTO);
            return DataResponse.success(joySkyCreateRspApiDTO);
        } catch (Exception e) {
            log.error("createSkyApprovalFlow invoke exception! request:{}", JSONObject.toJSONString(joySkyCreateReqApiDTO), e);
            return DataResponse.error("审批流创建发生异常:" + e.getMessage());
        } finally {
            log.info("createSkyApprovalFlow finished. request={}, invokeResult={}", JSONObject.toJSONString(joySkyCreateReqApiDTO), JSONObject.toJSONString(result));
        }
    }

    /**
     * 获取流程入参参数
     *
     * @param processDefinitionKey 流程key
     * @param processInstanceId    可以为空
     * @param erp                  提交人erp
     * @return ApiResult<StartProcessParams>
     */
    public ApiResult<StartProcessParameters> getStartProcessParams(String processDefinitionKey, String processInstanceId, String erp) {
        String msg = "获取joySky流程参数";
        try {
            ApiSignInfo apiSignInfo = buildBaseApiSignInfo();
            StartProcessParamsQuery query = new StartProcessParamsQuery();
            BeanUtils.copyProperties(apiSignInfo, query);
            query.setProcessDefinitionKey(processDefinitionKey);
            query.setProcessInstanceId(processInstanceId);
            query.setCurrentUserErp(erp);

            ApiResult<StartProcessParameters> result = processSimpleOperateJsfService.getStartProcessParameters(query);
            log.info("getStartProcessParams request:{}, response:{}", JSONObject.toJSONString(query), result);
            if (result.getSuccess()) {
                return result;
            }
        } catch (Exception e) {
            log.error("{}，失败。", msg, e);
        }
        return null;
    }

    /**
     * 构建基础API签名信息。
     *
     * @return ApiSignInfo 对象，包含了应用编码、请求密钥、时间戳和签名
     */
    //这里是为了构建每次调用的签名sign，这里也可以使用平台提供的工具：com.jd.process.api.util.ApiSignUtil
    private ApiSignInfo buildBaseApiSignInfo() {
        //配置参数校验
        Assert.isTrue(StringUtils.isNotBlank(appCode), "joySky appCode配置缺失！");
        Assert.isTrue(StringUtils.isNotBlank(appSecret), "joySky appSecret配置缺失！");

        String requestKey = UUID.randomUUID().toString();
        String timestamp = DateUtil.getFormattedTimestamp();
        String sign = ApiSignUtil.getSign(appSecret, requestKey, timestamp);

        ApiSignInfo apiSignInfo = new ApiSignInfo();
        apiSignInfo.setAppCode(appCode);
        apiSignInfo.setRequestKey(requestKey);
        apiSignInfo.setTimestamp(timestamp);
        apiSignInfo.setSign(sign);

        return apiSignInfo;
    }

    /**
     * 构建表单数据模型。
     *
     * @param startProcessParameters 启动流程的参数。
     * @param skyBizFlowTypeEnum     JoySky业务流程类型枚举。
     * @param processFormData        流程表单数据
     * @return 表单数据模型字符串。
     */
    private String buildFormDataModel(StartProcessParameters startProcessParameters, JoySkyBizFlowTypeEnum skyBizFlowTypeEnum, Map<String, String> processFormData) {
        //这里使用平台提供的方法
        return PackFormDataModelUtil.packFormDataModelStr(
                startProcessParameters.getFormBaseKey(),
                skyBizFlowTypeEnum.getProcessTitle(),
                buildFormModelValues(startProcessParameters.getFormModelList(), skyBizFlowTypeEnum, processFormData),
                null);
    }

    /**
     * 构建流程中的表单数据。
     *
     * @param formModelList      表单模型列表
     * @param skyBizFlowTypeEnum 业务流程类型枚举
     * @param processFormData    流程表单数据
     * @return 表单模型值映射
     */
    private Map<String, Object> buildFormModelValues(List<FormModel> formModelList, JoySkyBizFlowTypeEnum skyBizFlowTypeEnum, Map<String, String> processFormData) {
        if (CollectionUtils.isEmpty(processFormData)) {
            log.error("buildFormModelValues empty! processCode:{}", skyBizFlowTypeEnum.getFlowTypeCode());
            throw new RuntimeException("表单流程参数为空！processCode:" + skyBizFlowTypeEnum.getFlowTypeCode());
        }

        //字段赋值
        Map<String, Object> formModelValues = new HashMap<>(processFormData.size());
        for (FormModel model : formModelList) {
            if (processFormData.containsKey(model.getFieldComment())) {
                formModelValues.put(model.getFieldName(), processFormData.get(model.getFieldComment()));
            }
        }

        return formModelValues;
    }

}
