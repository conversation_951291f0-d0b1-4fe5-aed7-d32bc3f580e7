package com.jdi.isc.task.center.service.support.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.CacheLocationEnum;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.common.exception.TaskBizException;
import com.jdi.isc.task.center.common.utils.validation.PropertyError;
import com.jdi.isc.task.center.common.utils.validation.ValidateResult;
import com.jdi.isc.task.center.common.utils.validation.ValidationUtil;
import com.jdi.isc.task.center.domain.enums.ExcelTemplateEnum;
import com.jdi.isc.task.center.domain.excel.TemplateReqVO;
import com.jdi.isc.task.center.domain.excel.TemplateResVO;
import com.jdi.isc.task.center.domain.excel.TemplateSourceDataVO;
import com.jdi.isc.task.center.service.manage.template.ExcelTemplateManageService;
import com.jdi.isc.task.center.service.support.AssertValidation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 动态模版生成
 *
 * <AUTHOR>
 * @date 2024/4/12
 **/
@Slf4j
@Service
public class DynamicTemplateService {

    @Resource
    private Map<String, ExcelTemplateManageService> excelTemplateManageServiceMap;

    private static final WriteHandler COLUMN_WIDTH_HANDLER = new SimpleColumnWidthStyleStrategy(50);
    private static final WriteHandler HEAD_ROW_HANDLER = new HeadRowWriteHandler();

    /**
     * 根据类型生成模板
     * @param reqVO 模版入参
     */
    public void generateTemplateByType(TemplateReqVO reqVO, HttpServletResponse response) {
        Integer taskBizType = reqVO.getTaskBizType();
        ExcelTemplateManageService excelTemplateManageService = this.getExcelTemplateManageService(taskBizType);

        reqVO.setTaskBizType(taskBizType);
        TemplateSourceDataVO sourceDataVO = excelTemplateManageService.loadTemplateSourceDataVo(reqVO);

        this.generateTemplate(sourceDataVO, response);
    }


    /**
     * 生成模板
     *
     * @param templateSourceDataVO 模板源数据
     * @param response             请求返回
     */
    public void generateTemplate(TemplateSourceDataVO templateSourceDataVO, HttpServletResponse response) {
        ValidateResult<TemplateSourceDataVO> result = ValidationUtil.checkParam(templateSourceDataVO);
        if (!result.getSuccess() && CollectionUtils.isNotEmpty(result.getPropertyErrors())) {
            throw new TaskBizException(String.join(Constant.COMMA, result.getPropertyErrors().stream().map(PropertyError::getMessage).collect(Collectors.toSet())));
        }
        // 文件名
        String fileName = templateSourceDataVO.getExcelName() + Constant.DOT + Constant.XLSX;
        // 模版数据写入
        try (ServletOutputStream outputStream = response.getOutputStream(); ExcelWriter excelWriter = EasyExcel.write(outputStream).inMemory(Boolean.TRUE).filedCacheLocation(CacheLocationEnum.NONE).build()) {
            // 设置content-disposition响应头控制浏览器以下载的形式打开文件，中文文件名要使用URLEncoder.encode方法进行编码，否则会出现文件名乱码
            response.setHeader("content-disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));
            // 这里 需要指定写用哪个class去写，然后写到第一个sheet，名字为模板 然后文件流会自动关闭
            int sheetNo = 0;
            // 处理多个表格
            List<TemplateSourceDataVO.SheetDataVO> sheetDataVOList = templateSourceDataVO.getSheetDataVOList();
            for (TemplateSourceDataVO.SheetDataVO sheetDataVO : sheetDataVOList) {
                AssertValidation.isTrue(Objects.isNull(sheetDataVO.getClazz()) && CollectionUtils.isEmpty(sheetDataVO.getHeadList()), "EasyExcel模版类和表头列表不能同时为空");
                AssertValidation.isTrue(Objects.nonNull(sheetDataVO.getClazz()) && CollectionUtils.isNotEmpty(sheetDataVO.getHeadList()), "EasyExcel模版类和表头列表只能选一种");
                // 数据写入文档
                excelWriter.write(null == sheetDataVO.getDatatList() ? Lists.newArrayList() : sheetDataVO.getDatatList(), this.getWriteSheet(sheetDataVO, sheetNo));
                // 写入数据
                sheetNo++;
            }
            excelWriter.finish();
        } catch (Exception e) {
            log.error("DynamicTemplateService.generateTemplate 生成模版异常, templateSourceDataVO={}", JSON.toJSONString(templateSourceDataVO), e);
        }
    }




    /**
     * 根据给定的参数生成模板
     * @param reqVO 模板请求参数对象，包含任务业务类型等信息
     * @return 生成的模板字节数组
     */
    public TemplateResVO generateTemplateByParam(TemplateReqVO reqVO){
        Integer taskBizType = reqVO.getTaskBizType();
        Stopwatch stopwatch = Stopwatch.createStarted();
        ExcelTemplateManageService excelTemplateManageService = this.getExcelTemplateManageService(taskBizType);
        TemplateSourceDataVO sourceDataVO = excelTemplateManageService.loadTemplateSourceDataVo(reqVO);
        log.info("generateTemplateByParam 生成模版，fileName={} 准备数据耗时: {}ms",sourceDataVO.getExcelName(), stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return this.generateTemplate(sourceDataVO);
    }


    public TemplateResVO generateTemplate(TemplateSourceDataVO templateSourceDataVO) {

        ValidateResult<TemplateSourceDataVO> result = ValidationUtil.checkParam(templateSourceDataVO);
        if (!result.getSuccess() && CollectionUtils.isNotEmpty(result.getPropertyErrors())) {
            throw new TaskBizException(String.join(Constant.COMMA, result.getPropertyErrors().stream().map(PropertyError::getMessage).collect(Collectors.toSet())));
        }

        TemplateResVO templateResVO = new TemplateResVO();
        // 文件名
        String fileName = templateSourceDataVO.getExcelName() + Constant.DOT + Constant.XLSX;
        templateResVO.setFileName(fileName);
        Stopwatch stopwatch = Stopwatch.createStarted();
        // 模版数据写入
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream(); ExcelWriter excelWriter = EasyExcel.write(outputStream)
                .excelType(ExcelTypeEnum.XLSX).inMemory(Boolean.TRUE).filedCacheLocation(CacheLocationEnum.MEMORY).build()) {
            // 设置content-disposition响应头控制浏览器以下载的形式打开文件，中文文件名要使用URLEncoder.encode方法进行编码，否则会出现文件名乱码
            //response.setHeader("content-disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));
            // 这里 需要指定写用哪个class去写，然后写到第一个sheet，名字为模板 然后文件流会自动关闭
            int sheetNo = 0;
            // 处理多个表格
            List<TemplateSourceDataVO.SheetDataVO> sheetDataVOList = templateSourceDataVO.getSheetDataVOList();
            for (TemplateSourceDataVO.SheetDataVO sheetDataVO : sheetDataVOList) {
                if (Objects.nonNull(sheetDataVO)){
                    AssertValidation.isTrue(Objects.isNull(sheetDataVO.getClazz()) && CollectionUtils.isEmpty(sheetDataVO.getHeadList()), sheetDataVO.getSheetName()+"EasyExcel模版类和表头列表不能同时为空");
                    AssertValidation.isTrue(Objects.nonNull(sheetDataVO.getClazz()) && CollectionUtils.isNotEmpty(sheetDataVO.getHeadList()), sheetDataVO.getSheetName()+"EasyExcel模版类和表头列表只能选一种");
                    // 数据写入文档
                    excelWriter.write(null == sheetDataVO.getDatatList() ? Lists.newArrayList() : sheetDataVO.getDatatList(), this.getWriteSheet(sheetDataVO, sheetNo));
                }
                // 写入数据
                sheetNo++;
            }
            excelWriter.finish();
            templateResVO.setBytes(outputStream.toByteArray());
        } catch (Exception e) {
            log.error("DynamicTemplateService.generateTemplate 生成模版异常, templateSourceDataVO={}", JSON.toJSONString(templateSourceDataVO), e);
        }
        log.info("DynamicTemplateService.generateTemplate fileName={} 生成模版耗时:{}ms",fileName, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return templateResVO;
    }

    private WriteSheet getWriteSheet(TemplateSourceDataVO.SheetDataVO sheetDataVO, int sheetNo) {
        // 写文件
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetNo, sheetDataVO.getSheetName())
                .head(sheetDataVO.getClazz())
                .head(sheetDataVO.getHeadList()) // 设置固定的表头
                .filedCacheLocation(CacheLocationEnum.NONE)
                .build();
        addWriteHandlerList(writeSheet, sheetDataVO);
        return writeSheet;
    }

    private void addWriteHandlerList(WriteSheet writeSheet, TemplateSourceDataVO.SheetDataVO sheetDataVO) {
        List<WriteHandler> writeHandlerList = Lists.newArrayList(COLUMN_WIDTH_HANDLER
                ,HEAD_ROW_HANDLER);
        // 存在下拉数据时，添加下拉数据处理
        if (Objects.nonNull(sheetDataVO.getDropdownDataVO())
                && (CollectionUtils.isNotEmpty(sheetDataVO.getDropdownDataVO().getEachItemDataVOList()) || CollectionUtils.isNotEmpty(sheetDataVO.getDropdownDataVO().getDynamicDataVOList()))) {
            writeHandlerList.add(new DropdownListWriteHandler(sheetDataVO.getDropdownDataVO()));
        }

        if (CollectionUtils.isNotEmpty(sheetDataVO.getWriteHandlerList())) {
            writeHandlerList.addAll(sheetDataVO.getWriteHandlerList());
        }
        writeSheet.getCustomWriteHandlerList().addAll(writeHandlerList);
    }

    private ExcelTemplateManageService getExcelTemplateManageService(Integer taskBizType){
        return excelTemplateManageServiceMap.get(ExcelTemplateEnum.getServiceName(taskBizType));
    }
}
