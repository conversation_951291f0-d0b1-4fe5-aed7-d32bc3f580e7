package com.jdi.isc.task.center.service.work.task.frame;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.fastjson.JSON;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskCreateTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskStatusEnum;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.library.common.enmus.file.FileTypeEnum;
import com.jdi.isc.task.center.common.exception.TaskExportException;
import com.jdi.isc.task.center.common.utils.S3Utils;
import com.jdi.isc.task.center.common.utils.validation.PropertyError;
import com.jdi.isc.task.center.common.utils.validation.ValidateResult;
import com.jdi.isc.task.center.common.utils.validation.ValidationUtil;
import com.jdi.isc.task.center.domain.dynamic.DynamicClassFieldVO;
import com.jdi.isc.task.center.domain.enums.TaskStrategyEnum;
import com.jdi.isc.task.center.domain.task.dto.BaseWorkerDTO;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;
import com.jdi.isc.task.center.domain.task.vo.TaskStrategyVO;
import com.jdi.isc.task.center.service.manage.task.strategy.TaskContextService;
import com.jdi.isc.task.center.service.manage.task.strategy.TaskStrategyService;
import com.jdi.isc.task.center.service.work.task.dynamic.DynamicGenerateImportSubClass;
import com.jdi.isc.task.center.service.work.task.listener.HeadPageReadListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用抽象任务执行器
 * <AUTHOR>
 * @date 20231125
 */
@Component
@Slf4j
public abstract class BaseExecutableAbsJob<T extends BaseWorkerDTO> implements BaseJob<T> {

    @Resource
    public S3Utils s3Utils;
    @Resource
    public TaskContextService taskContextService;

    public final static String RESULT_FOLDER = "jdi-intl/el1/";

    @Resource
    protected DynamicGenerateImportSubClass dynamicGenerateImportSubClass;

    /**
     * 校验表头的枚举集合
     */
    private static final Set<TaskBizTypeEnum> checkHeadTaskTypeSet = Sets.newHashSet(TaskBizTypeEnum.SKU_AMEND_BATCH_UPDATE_CN);

    @Override
    public void accept(TaskDTO<T> task){
        //导入类任务
        TaskStrategyVO taskStrategyVO = task.getTaskStrategyVO();
        if(TaskCreateTypeEnum.IMPORT.equals(taskStrategyVO.getCreateType())){
            // SUPPLIER_TASK 获取的 - getReqFileUrl      WIMP_TASK  从 getReqParam 改成 getReqFileUrl  CUSTOMER_TASK 继续用 taskStrategyVO.getReqParam()
            if(TaskStrategyEnum.CUSTOMER_TASK.equals(taskStrategyVO.getTaskStrategyEnum())){
                task.setTargetInputFile(s3Utils.download(taskStrategyVO.getReqParam()));
            }else {
                task.setTargetInputFile(s3Utils.download(taskStrategyVO.getReqFileUrl()));
            }
            task.setFileName(taskStrategyVO.getName());
        //导出类任务
        }else if(TaskCreateTypeEnum.EXPORT.equals(taskStrategyVO.getCreateType())){
            //入参查询参数json实体
            task.setReqJson(taskStrategyVO.getReqParam());
        }
        task.setTaskBizTypeEnum(TaskBizTypeEnum.forCode(taskStrategyVO.getBizType()));
        task.setTaskId(taskStrategyVO.getId());
        task.setStartTime(System.currentTimeMillis());
        //更新任务状态
        taskStrategyVO.setStatus(TaskStatusEnum.PROCESSING);
        this.getStrategyService(taskStrategyVO.getTaskStrategyEnum()).update(taskStrategyVO);
        log.info("BaseExecutableAbsJob.accept,taskId:{}",task.getTaskId());
    }

    protected TaskStrategyService getStrategyService(TaskStrategyEnum strategyEnum) {
        return taskContextService.getTaskStrategyService(strategyEnum);
    }

    /**
     * 通用解析逻辑
     * @param task
     */
    @Override
    public void parse(TaskDTO<T> task){
        if(TaskCreateTypeEnum.IMPORT.equals(task.getTaskCreateType())){
            log.info("BaseExecutableAbsJob.import parse {},file:{} ",task.getTaskId(),task.getTargetInputFile().getKey());
            if(task.getTargetInputFile()!=null && task.getTargetInputFile().getObjectContent()!=null){
                Class<T> targetClass = getTargetClass();
                PageReadListener<T> tPageReadListener = checkHeadTaskTypeSet.contains(task.getTaskBizTypeEnum()) ? new HeadPageReadListener<T>(dataList -> {}, this.getStandardHeaders(targetClass)) : new PageReadListener<T>(dataList -> {});
                List<T> target = EasyExcel.read(task.getTargetInputFile().getObjectContent(), targetClass, tPageReadListener)
                        .sheet(0).headRowNumber(1).autoTrim(Boolean.TRUE).doReadSync();
                this.validate(target);
                task.setTarget(target);
            }
        }
    }

    /**
     * 导入通用落库逻辑/导出构建list<Bean>逻辑
     * @param task
     */
    @Override
    public void run(TaskDTO<T> task){ }

    @Override
    public void export(TaskDTO<T> task){
        if(task!=null){
            log.info("BaseExecutableAbsJob.export task={}", JSONObject.toJSONString(task));
            TaskStrategyVO taskStrategyVO = task.getTaskStrategyVO();
            TaskStrategyVO taskPo = this.getStrategyService(taskStrategyVO.getTaskStrategyEnum()).getTask(task.getTaskId());
            if (Objects.nonNull(taskPo) && TaskStatusEnum.FAILURE.equals(taskPo.getStatus())){
                log.info("BaseExecutableAbsJob.export 任务已经失败，跳过执行。 taskId={}" , task.getTaskId());
                return;
            }
            if(CollectionUtils.isEmpty(task.getTarget())){
                this.getStrategyService(taskStrategyVO.getTaskStrategyEnum()).update(new TaskStrategyVO(task, TaskStatusEnum.SUCCESS));
                log.info("BaseExecutableAbsJob.export 空任务,跳过执行。 taskId={}" , task.getTaskId());
                return;
            }

            String taskName = "";
            if (null!=task.getTaskStrategyVO() && StringUtils.isNotBlank(task.getTaskStrategyVO().getName())){
                taskName = task.getTaskStrategyVO().getName();
            }else if (null!=task.getTaskBizTypeEnum()){
                taskName = task.getTaskBizTypeEnum().getName();
            }
            String resultName = RESULT_FOLDER + taskName + Constant.UNDER_LINE + System.currentTimeMillis() + ".xlsx";
            ByteArrayOutputStream targetOutputStream = new ByteArrayOutputStream();
            try (ExcelWriter excelWriter = EasyExcel.write(targetOutputStream).build()) {
                if (CollectionUtils.isNotEmpty(task.getTarget())) {
                    excelWriter.write(task.getTarget(), EasyExcel.writerSheet(0, "result").head(getTargetClass()).build());
                }
                task.setEndTime(System.currentTimeMillis());
                excelWriter.finish();
                try (ByteArrayInputStream inputStream = new ByteArrayInputStream(targetOutputStream.toByteArray())) {
                    task.setResultUrl(s3Utils.upload(inputStream, FileTypeEnum.BATCH_FILE.getCode(),resultName));
                    this.getStrategyService(taskStrategyVO.getTaskStrategyEnum()).update(new TaskStrategyVO(task, TaskStatusEnum.SUCCESS));
                }
            } catch (Exception e) {
                log.error("BaseExecutableAbsJob.export error, taskId={} ", task.getTaskId(), e);
                throw new TaskExportException(task.getTaskId() + e.getMessage());
            } finally {
                log.info("BaseExecutableAbsJob.export 任务「{}」执行完毕={}, 文件地址={}", task.getTaskId(), task.getOssPutMd5(), task.getResultUrl());
            }
        }
    }

    @Override
    public void errHandle(TaskDTO<T> taskDTO,Exception e){
        log.error("BaseExecutableAbsJob.errHandle taskDTO={} ", JSON.toJSONString(taskDTO) ,e);
        TaskStrategyVO taskStrategyVO = taskDTO.getTaskStrategyVO();
        this.getStrategyService(taskStrategyVO.getTaskStrategyEnum()).errHandle(taskStrategyVO,e.getMessage());
    }

    public Class<T> getTargetClass(){
            Type res = getClass().getGenericSuperclass();
            if(res instanceof ParameterizedType){
                ParameterizedType pRes = (ParameterizedType) res;
                Type[] type = pRes.getActualTypeArguments();
                if(type.length>0){
                    if(type[0] instanceof Class){
                        Type typeE = type[0];
                        return (Class<T>)typeE;
                    }
                }
            }
        return null;
    }

    /**
     * 基础参数校验
     * @param classes
     */
    public void validate(List<? extends BaseWorkerDTO> classes) {
        Optional.ofNullable(classes).orElseGet(ArrayList::new)
                .forEach(dto-> {
                    ValidateResult<BaseWorkerDTO> listValidateResult = ValidationUtil.checkParam(dto);
                    if (Boolean.FALSE.equals(listValidateResult.getSuccess())) {
                        dto.setValid(Boolean.FALSE);
                        List<String> messageList = listValidateResult.getPropertyErrors().stream().filter(Objects::nonNull).map(PropertyError::getMessage).collect(Collectors.toList());
                        dto.setResult(String.join(Constant.COMMA, messageList));
                    }
                });
    }

    public void newValidate(List<? extends BaseWorkerDTO> classes) {
        Optional.ofNullable(classes).orElseGet(ArrayList::new)
            .forEach(dto-> {
                ValidateResult<BaseWorkerDTO> listValidateResult = ValidationUtil.checkParam(dto);
                if (Boolean.FALSE.equals(listValidateResult.getSuccess())) {
                    dto.setValid(Boolean.FALSE);
                    List<String> messageList = listValidateResult.getPropertyErrors().stream().filter(Objects::nonNull).map(PropertyError::getMessage).collect(Collectors.toList());
                    dto.setResult(String.join(Constant.COMMA, messageList));
                }
            });
    }

    /**
     * 根据给定的字段列表生成一个动态的子类。
     * @param fields 字段列表，不能为空。
     */
    protected Class<?> generateClass(List<String> fields){
        if (CollectionUtils.isEmpty(fields)) {
            return null;
        }

        List<DynamicClassFieldVO> dynamicClassFieldVOList = Lists.newArrayList();
        for (int i = 0, len = fields.size(); i < len; i++) {
            DynamicClassFieldVO classFieldVO = new DynamicClassFieldVO();
            classFieldVO.setIndex(i);
            String fieldName = fields.get(i);
            classFieldVO.setFieldName(fieldName);
            classFieldVO.setFieldDesc(fieldName);
            classFieldVO.setRequired(false);
            dynamicClassFieldVOList.add(classFieldVO);
        }

        return dynamicGenerateImportSubClass.generateClass(BaseWorkerDTO.class, dynamicClassFieldVOList);
    }


    /**
     * 获取实体类标准表头
     */
    private List<String> getStandardHeaders(Class<?> clazz) {
        List<Field> fields = this.getAllFields(clazz);

        boolean settingIndex = Optional.of(fields).orElseGet(ArrayList::new).stream().anyMatch(field -> {
            boolean annotationPresent = field.isAnnotationPresent(ExcelProperty.class);
            ExcelProperty prop = field.getAnnotation(ExcelProperty.class);
            return annotationPresent && prop.index() >= 0;
        });

        if (settingIndex){
            return Optional.of(fields)
                    .orElseGet(ArrayList::new)
                    .stream()
                    .filter(field -> field.isAnnotationPresent(ExcelProperty.class))
                    .sorted(Comparator.comparingInt(field -> {
                        ExcelProperty prop = field.getAnnotation(ExcelProperty.class);
                        // 兼容BaseWorkerDTO 中result字段下载到模板的情况
                        int index = prop.index();
                        if (index == -1){
                            index = 99999;
                        }
                        return index;
                    }))
                    .map(field -> field.getAnnotation(ExcelProperty.class).value()[0])
                    .collect(Collectors.toList());
        }
        return Optional.of(fields)
                .orElseGet(ArrayList::new)
                .stream()
                .filter(field -> field.isAnnotationPresent(ExcelProperty.class))
                .map(field -> field.getAnnotation(ExcelProperty.class).value()[0])
                .collect(Collectors.toList());
    }

    /**
     * 获取类及其父类的所有字段
     */
    public List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            Field[] declaredFields = clazz.getDeclaredFields();
            fields.addAll(Arrays.asList(declaredFields));
            clazz = clazz.getSuperclass();
        }
        return fields;
    }

}
