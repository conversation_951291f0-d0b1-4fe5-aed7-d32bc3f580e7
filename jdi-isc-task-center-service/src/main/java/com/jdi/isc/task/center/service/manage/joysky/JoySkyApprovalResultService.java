package com.jdi.isc.task.center.service.manage.joysky;

import com.jdi.isc.task.center.domain.joysky.biz.JoySkyApplyResultDTO;

/**
 * <AUTHOR>
 * @Date 2024/11/4
 * @Description 审批结果处理类
 */
public interface JoySkyApprovalResultService {

    /**
     * 完成审批任务
     * @param resultParam 审批结果DTO对象
     */
    void approvalTaskFinish(JoySkyApplyResultDTO resultParam);


    /**
     * 拒绝审批任务
     * @param resultParam 审批结果参数
     */
    void approvalTaskReject(JoySkyApplyResultDTO resultParam);
}
