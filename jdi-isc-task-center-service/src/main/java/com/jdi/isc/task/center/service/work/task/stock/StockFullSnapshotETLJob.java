package com.jdi.isc.task.center.service.work.task.stock;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.aggregate.read.api.customer.res.CustomerReadResp;
import com.jdi.isc.product.soa.api.common.enums.EsAttributeChangeTypeEnum;
import com.jdi.isc.product.soa.stock.mku.IscMkuStockReadApiService;
import com.jdi.isc.product.soa.stock.mku.req.IscMkuStockItemReadReqDTO;
import com.jdi.isc.product.soa.stock.mku.req.IscMkuStockReadReqDTO;
import com.jdi.isc.product.soa.stock.mku.res.IscMkuStockResDTO;
import com.jdi.isc.task.center.common.costants.CacheKeyConstant;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.common.costants.StockConstants;
import com.jdi.isc.task.center.common.costants.UmpKeyConstant;
import com.jdi.isc.task.center.common.ducc.TaskDuccConfigService;
import com.jdi.isc.task.center.common.enums.OnWaySaleTypeEnum;
import com.jdi.isc.task.center.common.utils.DateUtil;
import com.jdi.isc.task.center.common.utils.JimUtils;
import com.jdi.isc.task.center.common.utils.StockNumUtils;
import com.jdi.isc.task.center.domain.enums.StockTypeEnum;
import com.jdi.isc.task.center.domain.enums.YnEnum;
import com.jdi.isc.task.center.domain.promise.biz.PageDataPullConfig;
import com.jdi.isc.task.center.domain.stock.biz.StockEtlConfigVO;
import com.jdi.isc.task.center.domain.stock.biz.StockQueryVO;
import com.jdi.isc.task.center.domain.stock.msg.SkuStockChangeMsg;
import com.jdi.isc.task.center.domain.stock.po.StockFullSnapshotPO;
import com.jdi.isc.task.center.domain.stock.po.StockPO;
import com.jdi.isc.task.center.domain.stock.po.WarehouseSkuPO;
import com.jdi.isc.task.center.rpc.customer.CustomerReadRpcService;
import com.jdi.isc.task.center.rpc.stock.StockRpcService;
import com.jdi.isc.task.center.service.atomic.stock.StockAtomicService;
import com.jdi.isc.task.center.service.atomic.stock.StockFullSnapshotAtomicService;
import com.jdi.isc.task.center.service.atomic.stock.WarehouseSkuAtomicService;
import com.jdi.isc.task.center.service.manage.msg.TaskCenterMsgSender;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.jdi.isc.task.center.common.costants.Constant.PAGE_BATCH_SIZE;


/**
 * @author：xubing82
 * @date：2025/6/9 14:14
 * @description：“库存有无”打标数据加工处理流程, 参考文档：https://joyspace.jd.com/pages/1NymR9joyllq5DGFwxUB
 */
@Slf4j
@Component
public class StockFullSnapshotETLJob {

    @Resource
    private StockAtomicService stockAtomicService;

    @Resource
    private StockFullSnapshotAtomicService stockFullSnapshotAtomicService;

    @Value("${topic.jmq4.producer.mkuEsChange}")
    private String mkuEsChangeTopic;

    @Resource
    private TaskCenterMsgSender taskCenterMsgSender;

    @Resource
    private CustomerReadRpcService customerReadRpcService;

    @Resource
    private TaskDuccConfigService taskDuccConfigService;

    @Resource
    private JimUtils jimUtils;

    @Resource
    private WarehouseSkuAtomicService warehouseSkuAtomicService;


    @LafValue("jdi.isc.task.stock.etl.config")
    private String stockEtlConfigs;


    @Resource
    private StockRpcService stockRpcService;

    /**
     * 用于标识计算所有类型库存的标记值
     */
    private static final int ALL_STOCK_TYE = 3;

    /**
     * 用于标识客户代码前缀的常量字符串，主要用于构建库存快照数据同步完成标记
     */
    private static final String CLIENT_CODE_PREFIX = "CLIENTCODE_";

    /**
     * 用于标识库存打标任务配置类型的常量字符串，主要用于从任务配置中心获取相关配置
     */
    private static final String JOB_CONFIG_TYPE = "stockLabel";


    /**
     * 有现货库存
     */
    public final static Set<Integer> HAVE_IN_STOCK = Sets.newHashSet(33);


    /**
     * 有在途库存
     */
    public final static Set<Integer> HAVE_IN_TRANSIT_STOCK = Sets.newHashSet(39, 40);


    /**
     * 用于控制批量查询国内库存信息时每批次请求的最大项目数
     */
    private static final int BATCH_QUERY_CN_STOCK_SIZE = 30; // 每批次请求的最大项目数




    @SneakyThrows
    @PFTracing
    //@XxlJob("stockFullSnapshotEtlJob")
    @Deprecated
    public ReturnT<String> stockFullSnapshotEtlJob(String param) {

        //客户名单获取
        Set<String> clientCodes = getNeedETLValidClientCodes();
        if (CollectionUtils.isEmpty(clientCodes)) {
            log.warn("StockFullSnapshotETLJob getNeedETLValidClientCodes 需要加工处理的客户名单为空，加工任务不执行！");
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_FRAME_WARNING, (LevelCode.P0.getMessage() + "无有效的客户名单，无法进行库存打标任务，请联系管理员进行查看:"));
            return ReturnT.FAIL;
        }
        log.info("StockFullSnapshotETLJob getNeedETLValidClientCodes:{}", clientCodes);

        //大数据同步情况校验
        String syncFinishedFlag = CLIENT_CODE_PREFIX + DateUtil.getDateStrByPattern(DateUtil.YY_MM_DD_DAY_PATTERN);
        StockFullSnapshotPO dataSyncStockSnapshotPO = stockFullSnapshotAtomicService.getStockFullSnapshotByClientCode(syncFinishedFlag);
        if (dataSyncStockSnapshotPO == null) {
            log.warn("查询库存快照数据同步完成标记为空, 请检查大数据同步流程是否正常！syncFinishedFlag:{}", syncFinishedFlag);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_FRAME_WARNING, (LevelCode.P0.getMessage() + "库存快照底表数据为空，无法进行库存打标任务，请联系管理员进行查看"));
            return ReturnT.FAIL;
        }

        Integer etlStockType = getEtlStockType();
        for (String clientCode : clientCodes) {
            log.info("StockFullSnapshotETLJob processStockChangeMessages start, clientCode:{}", clientCode);

            // 全量库存总数
            LambdaQueryWrapper<StockFullSnapshotPO> totalWrapper = Wrappers.lambdaQuery(StockFullSnapshotPO.class)
                    .eq(StockFullSnapshotPO::getClientCode, clientCode)
                    .eq(etlStockType != null, StockFullSnapshotPO::getStockType, etlStockType)
                    .eq(StockFullSnapshotPO::getYn, YnEnum.YES.getCode());
            long total = stockFullSnapshotAtomicService.count(totalWrapper);
            if (total == 0) {
                log.warn("StockFullSnapshotETLJob queryBaseData 查询客户:{}库存快照数据为空, 不做打标处理！", clientCode);
                continue;
            }

            long current = 0;
            IPage<StockFullSnapshotPO> page = new Page<>();
            page.setSize(Constant.PAGE_BATCH_SIZE);
            LambdaQueryWrapper<StockFullSnapshotPO> queryWrapper = Wrappers.lambdaQuery(StockFullSnapshotPO.class)
                    .eq(StockFullSnapshotPO::getClientCode, clientCode)
                    .eq(etlStockType != null, StockFullSnapshotPO::getStockType, etlStockType)
                    .eq(StockFullSnapshotPO::getYn, YnEnum.YES.getCode())
                    .orderByAsc(StockFullSnapshotPO::getCreateTime);

            do {
                // 设置当前页
                page.setCurrent(current);
                IPage<StockFullSnapshotPO> resPage = stockFullSnapshotAtomicService.page(page, queryWrapper);
                if (null == resPage || CollectionUtils.isEmpty(resPage.getRecords())) {
                    log.warn("StockFullSnapshotETLJob queryBaseData 查询客户:{}库存快照数据为空!", clientCode);
                    break;
                }

                //无效数据过滤
                List<StockFullSnapshotPO> stockFullSnapshotPOList = resPage.getRecords().stream()
                        .filter(stockFullSnapshotPO -> StringUtils.isNotBlank(stockFullSnapshotPO.getClientCode())
                                && stockFullSnapshotPO.getMkuId() != null
                                && StringUtils.isNotBlank(stockFullSnapshotPO.getCountryCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(stockFullSnapshotPOList)) {
                    log.warn("StockFullSnapshotETLJob queryBaseData 查询客户:{}有效库存快照数据为空!", clientCode);
                    break;
                }

                //1、对商品进行归堆，归成三类：跨境直发品、供应商库存、备货仓库存
                List<StockFullSnapshotPO> domesticStockList = classifyStocks(stockFullSnapshotPOList, StockFullSnapshotETLJob::isCnStock);
                List<StockFullSnapshotPO> supplierStockList = classifyStocks(stockFullSnapshotPOList, StockFullSnapshotETLJob::isSupplyStock);
                List<StockFullSnapshotPO> warehouseStockList = classifyStocks(stockFullSnapshotPOList, StockFullSnapshotETLJob::isWarehouseStock);
                if (CollectionUtils.isEmpty(domesticStockList) && CollectionUtils.isEmpty(supplierStockList) && CollectionUtils.isEmpty(warehouseStockList)) {
                    log.warn("StockFullSnapshotETLJob queryBaseData 客户:{}存在非法快照数据，不处理! stockFullSnapshotPOList:{}", clientCode, JSON.toJSONString(stockFullSnapshotPOList));
                    break;
                }

                //2、处理库存变更消息
                log.info("StockFullSnapshotETLJob processStockChangeMessages start, clientCode:{}, current:{}", clientCode, current);
                List<SkuStockChangeMsg> stockChangeMsgList = processStockChangeMessages(clientCode, supplierStockList, warehouseStockList, domesticStockList);


                //3、发送MQ
                if (CollectionUtils.isNotEmpty(stockChangeMsgList)) {
                    log.info("StockFullSnapshotETLJob sendMessagesInBatches start,clientCode:{}, stockChangeMsgList:{}", clientCode, JSON.toJSONString(stockChangeMsgList));
                    sendMessagesInBatches(stockChangeMsgList);
                }

                current++;
            } while (current * Constant.PAGE_BATCH_SIZE < total);

        }

        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @PFTracing
    @XxlJob("stockFullSnapshotEtlJob")
    public ReturnT<String> stockFullSnapshotEtlJobV2(String param) {
        String todayStr = DateUtil.getDateStrByPattern(DateUtil.YY_MM_DD_DAY_PATTERN);
        //客户名单获取
        Set<String> clientCodes = getNeedETLValidClientCodes();
        if (CollectionUtils.isEmpty(clientCodes)) {
            log.warn("StockFullSnapshotETLJob getNeedETLValidClientCodes 需要加工处理的客户名单为空，加工任务不执行！");
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_FRAME_WARNING, (LevelCode.P0.getMessage() + "无有效的客户名单，无法进行库存打标任务，请联系管理员进行查看:"));
            return ReturnT.FAIL;
        }
        Stopwatch stopwatch = Stopwatch.createStarted(); // 开始计时
        log.info("StockFullSnapshotETLJob start, getNeedETLValidClientCodes:{}", clientCodes);

        //大数据同步情况校验
        String syncFinishedFlag = CLIENT_CODE_PREFIX + DateUtil.getDateStrByPattern(DateUtil.YY_MM_DD_DAY_PATTERN);
        StockFullSnapshotPO dataSyncStockSnapshotPO = stockFullSnapshotAtomicService.getStockFullSnapshotByClientCode(syncFinishedFlag);
        if (dataSyncStockSnapshotPO == null) {
            log.warn("查询库存快照数据同步完成标记为空, 请检查大数据同步流程是否正常！syncFinishedFlag:{}", syncFinishedFlag);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_FRAME_WARNING, (LevelCode.P0.getMessage() + "库存快照底表数据为空，无法进行库存打标任务，请联系管理员进行查看"));
            return ReturnT.FAIL;
        }

        Integer etlStockType = getEtlStockType();
        for (String clientCode : clientCodes) {
            StockQueryVO stockQueryVO = new StockQueryVO();
            stockQueryVO.setClientCode(clientCode);
            stockQueryVO.setStockType(etlStockType);
            long count = stockFullSnapshotAtomicService.queryStockFullSnapshotPOCount(stockQueryVO);
            long size = PAGE_BATCH_SIZE;
            long page = count / size + (count % size == 0 ? 0 : 1);

            log.info("StockFullSnapshotETLJob processStockChangeMessages start, clientCode:{}, totalCnt:{}, page:{}", clientCode, count, page);

            for (long index = 1; index <= page; index++) {
                // 1、分页获取数据标签表数据
                List<StockFullSnapshotPO> originStockFullSnapshotPOList = getStockFullSnapshotPOListByPage(index, size, clientCode, etlStockType);
                if (CollectionUtils.isEmpty(originStockFullSnapshotPOList)) {
                    log.warn("StockFullSnapshotETLJob queryBaseData 查询客户:{}库存快照数据为空!", clientCode);
                    continue;
                }

                //无效数据过滤
                List<String> simpleSkuLabelData = originStockFullSnapshotPOList.stream()
                        .map(getSimpleStockFullSnapshotPOStringFunction())
                        .collect(Collectors.toList());
                log.info("StockFullSnapshotETLJob getStockFullSnapshotPOListByPage:{}", JSON.toJSONString(simpleSkuLabelData));
                List<StockFullSnapshotPO> stockFullSnapshotPOList = originStockFullSnapshotPOList.stream()
                        .filter(stockFullSnapshotPO -> StringUtils.isNotBlank(stockFullSnapshotPO.getClientCode())
                                && stockFullSnapshotPO.getMkuId() != null
                                && StringUtils.isNotBlank(stockFullSnapshotPO.getCountryCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(stockFullSnapshotPOList)) {
                    log.warn("StockFullSnapshotETLJob queryBaseData 查询客户:{}有效库存快照数据为空!", clientCode);
                    continue;
                }

                //1、对商品进行归堆，归成三类：跨境直发品、供应商库存、备货仓库存
                List<StockFullSnapshotPO> domesticStockList = classifyStocks(stockFullSnapshotPOList, StockFullSnapshotETLJob::isCnStock);
                List<StockFullSnapshotPO> supplierStockList = classifyStocks(stockFullSnapshotPOList, StockFullSnapshotETLJob::isSupplyStock);
                List<StockFullSnapshotPO> warehouseStockList = classifyStocks(stockFullSnapshotPOList, StockFullSnapshotETLJob::isWarehouseStock);
                if (CollectionUtils.isEmpty(domesticStockList) && CollectionUtils.isEmpty(supplierStockList) && CollectionUtils.isEmpty(warehouseStockList)) {
                    log.warn("StockFullSnapshotETLJob queryBaseData 客户:{}存在非法快照数据，不处理! stockFullSnapshotPOList:{}", clientCode, JSON.toJSONString(stockFullSnapshotPOList));
                    continue;
                }


                //2、处理库存变更消息
                log.info("StockFullSnapshotETLJob processStockChangeMessages calculateStockTag, clientCode:{}, currentIndex:{}", clientCode, index);
                List<SkuStockChangeMsg> stockChangeMsgList = processStockChangeMessages(clientCode, supplierStockList, warehouseStockList, domesticStockList);
                updateCacheLabelData(stockChangeMsgList, todayStr);

                //3、发送MQ
                if (CollectionUtils.isNotEmpty(stockChangeMsgList)) {
                    List<SkuStockChangeMsg> changedSkuStockMsgList = filterChangedSkuLabelRecord(stockChangeMsgList);
                    if (CollectionUtils.isNotEmpty(changedSkuStockMsgList)) {
                        log.info("StockFullSnapshotETLJob sendMessagesInBatches start sendMQ, clientCode:{}, stockChangeMsgList:{}", clientCode, JSON.toJSONString(stockChangeMsgList));
                        sendMessagesInBatches(stockChangeMsgList);
                    }
                }

                //流程控制处理
                PageDataPullConfig dataPullConfig = taskDuccConfigService.getPageDataPullConfig(JOB_CONFIG_TYPE);
                if (dataPullConfig != null) {
                    Boolean stopSwitch = dataPullConfig.getStopSwitch();
                    if (stopSwitch != null && stopSwitch) {
                        log.info("StockFullSnapshotETLJob 管理员已终止此次数据处理流程，stopSwitch:{}", stopSwitch);
                        break;
                    }

                    Long intervalTime = dataPullConfig.getPageIntervalTime();
                    if (intervalTime != null && intervalTime > 0) {
                        Thread.sleep(intervalTime);
                    }
                }
            }
        }

        stopwatch.stop();
        log.info("StockFullSnapshotETLJob end, cost:{}s", stopwatch.elapsed(TimeUnit.SECONDS));
        return ReturnT.SUCCESS;
    }

    /**
     * 根据库存变更消息列表更新Redis中的库存标签数据
     * @param skuStockChangeMsgList 库存变更消息列表，包含需要更新的SKU库存标签信息
     * @param todayStr 当天日期字符串，用于构造Redis键
     */
    private void updateCacheLabelData(List<SkuStockChangeMsg> skuStockChangeMsgList, String todayStr) {
        Map<String, Map<String, String>> redisDataMap = new HashMap<>();
        for (SkuStockChangeMsg stockChangeMsg : skuStockChangeMsgList) {

            String redisKey = CacheKeyConstant.getStockLabelRedisKey(stockChangeMsg.getClientCode(), stockChangeMsg.getMkuId(), todayStr);

            // 获取承诺值
            Integer stockTag = stockChangeMsg.getHasStockTag();

            // 构建第二层 Map，放置sku的各类需打标的标签数据，例如履约标签、优先标签等等
            Map<String, String> stockMap = new HashMap<>();
            stockMap.put("stock_tag", String.valueOf(stockTag));


            // 将第二层 Map 放入第一层 Map
            redisDataMap.put(redisKey, stockMap);
        }

        //如果redis设置失败，es更新会再次校验是否更新， 不阻塞整体流程
        Boolean result = jimUtils.pipeHmSet(redisDataMap, CacheKeyConstant.TIME_OUT_OF_24_HOURS);
        log.warn("StockFullSnapshotETLJob updateCacheLabelData result:{}, date:{}, request:{}", result, todayStr, JSON.toJSONString(skuStockChangeMsgList));
    }


    /**
     * 过滤出需要更新库存标签的SKU记录
     *
     * @param stockFullSnapshotPOList SKU库存变更消息列表，包含全量库存快照数据
     * @return 返回需要更新库存标签的SKU记录列表
     */
    private List<SkuStockChangeMsg> filterChangedSkuLabelRecord(List<SkuStockChangeMsg> stockFullSnapshotPOList) {
        List<SkuStockChangeMsg> changedStockLabelsList = org.apache.commons.compress.utils.Lists.newArrayList();
        for (SkuStockChangeMsg stockChangeMsg : stockFullSnapshotPOList) {

            String yesterdayDayStr = DateUtil.getYesterdayDateStr();
            String redisKey = CacheKeyConstant.getStockLabelRedisKey(stockChangeMsg.getClientCode(), stockChangeMsg.getMkuId(), yesterdayDayStr);
            String yesterdayValue = jimUtils.hGet(redisKey, "stock_tag");
            Integer todayHasStockTag = stockChangeMsg.getHasStockTag();

            //redis没有数据，或者和当日的值不一致则判定为需更新数据
            if (com.jd.jdi.yz.core.utils.StringUtils.isBlank(yesterdayValue) || !Objects.equals(todayHasStockTag, Integer.parseInt(yesterdayValue))) {
                changedStockLabelsList.add(stockChangeMsg);
            }
        }

        return changedStockLabelsList;
    }

    @NotNull
    private static Function<StockFullSnapshotPO, String> getSimpleStockFullSnapshotPOStringFunction() {
        return item -> item.getId() + "-" + item.getClientCode() + "-" + item.getMkuId() + "-" + item.getSkuId();
    }

    /**
     * 分页查询stock全量快照信息列表
     *
     * @param index      分页索引
     * @param size       每页记录数
     * @param clientCode 客户代码
     * @param stockType  股票类型
     * @return 股票全量快照信息分页列表
     */
    private List<StockFullSnapshotPO> getStockFullSnapshotPOListByPage(long index, long size, String clientCode, Integer stockType) {
        StockQueryVO stockQueryVO = new StockQueryVO();
        stockQueryVO.setIndex(index);
        stockQueryVO.setSize(size);
        stockQueryVO.setClientCode(clientCode);
        stockQueryVO.setStockType(stockType);
        Page<StockFullSnapshotPO> stockFullSnapshotPOPage = stockFullSnapshotAtomicService.queryStockFullSnapshotPOPage(stockQueryVO);
        return stockFullSnapshotPOPage.getRecords();
    }


    /**
     * 获取需要进行ETL处理的客户端代码集合（排除黑名单后）
     *
     * @return 需要进行ETL处理的客户端代码集合，可能为空集合但不会为null
     */
    private Set<String> getNeedETLValidClientCodes() {
        //灰度名单是否有值
        Set<String> clientCodes = getEtlGrayClientCodes();
        if (CollectionUtils.isEmpty(clientCodes)) {
            //获取全量
            List<CustomerReadResp> customerReadRespList = customerReadRpcService.queryAllCustumers();
            log.info("queryAllCustumers result size:{}", CollectionUtils.isNotEmpty(customerReadRespList) ? customerReadRespList.size() : 0);

            if (CollectionUtils.isNotEmpty(customerReadRespList)) {
                clientCodes = customerReadRespList.stream().map(CustomerReadResp::getClientCode).collect(Collectors.toSet());
            }
        }

        //过滤黑名单
        Set<String> blackClientCodes = getEtlBlackListClientCodes();
        if (CollectionUtils.isNotEmpty(blackClientCodes)) {
            clientCodes.removeAll(blackClientCodes);
        }

        return clientCodes;
    }


    /**
     * 处理库存变更消息，生成库存变更消息列表
     *
     * @param supplierStockList  供应商库存快照列表
     * @param warehouseStockList 仓库库存快照列表
     * @param domesticStockList  国内库存快照列表
     * @return 库存变更消息列表
     */
    private List<SkuStockChangeMsg> processStockChangeMessages(String clientCode, List<StockFullSnapshotPO> supplierStockList, List<StockFullSnapshotPO> warehouseStockList, List<StockFullSnapshotPO> domesticStockList) {
        List<SkuStockChangeMsg> stockChangeMsgList = new ArrayList<>();

        //国际库存处理(本土直发、本土备货、跨境备货品)
        List<StockFullSnapshotPO> combinedStockList = Lists.newArrayList(Iterables.concat(supplierStockList, warehouseStockList));
        if (CollectionUtils.isNotEmpty(combinedStockList)) {
            //批量获取商品库存
            Set<Long> skuIdSet = combinedStockList.stream()
                    .map(StockFullSnapshotPO::getSkuId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            Map<Long, List<StockPO>> internationalStockMap = stockAtomicService.getStockMap(skuIdSet);


            //批量获取商品-仓绑定关系
            Map<Long, List<WarehouseSkuPO>> warehouseSkuMap = getWarehouseSkuMap(combinedStockList);

            for (StockFullSnapshotPO stock : combinedStockList) {
                processInternationalStock(stock, internationalStockMap, stockChangeMsgList, warehouseSkuMap);
            }
        }

        //国内库存处理(跨境直发品)
        processDomesticStocks(clientCode, domesticStockList, stockChangeMsgList);

        return stockChangeMsgList;
    }

    /**
     * 根据库存快照列表获取仓库SKU映射关系
     * @param combinedStockList 库存快照列表，包含仓库ID和SKU ID信息
     * @return 返回以SKU ID为key，对应仓库SKU对象列表为value的映射关系
     */
    private Map<Long, List<WarehouseSkuPO>> getWarehouseSkuMap(List<StockFullSnapshotPO> combinedStockList) {
        Set<Long> warehouseSkuIds = combinedStockList.stream()
                .filter(item -> item.getWarehouseId() != null)
                .map(StockFullSnapshotPO::getSkuId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(warehouseSkuIds)) {
            Map<Long, List<WarehouseSkuPO>> warehouseSkuMap = warehouseSkuAtomicService.queryBindSkuMap(warehouseSkuIds);
            return warehouseSkuMap;
        }

        return Collections.emptyMap();
    }

    /**
     * 处理国际库存信息并生成库存变更消息
     *
     * @param stockFullSnapshotPO   包含SKU完整快照信息的对象
     * @param internationalStockMap 按SKU ID分组的国际库存数据映射表
     * @param stockChangeMsgList    用于存储生成的库存变更消息列表
     */
    private void processInternationalStock(StockFullSnapshotPO stockFullSnapshotPO, Map<Long, List<StockPO>> internationalStockMap,
                                           List<SkuStockChangeMsg> stockChangeMsgList, Map<Long, List<WarehouseSkuPO>> warehouseSkuMap) {
        Long skuId = stockFullSnapshotPO.getSkuId();
        String warehouseId = stockFullSnapshotPO.getWarehouseId();

        SkuStockChangeMsg skuStockChangeMsg = new SkuStockChangeMsg();
        skuStockChangeMsg.setClientCode(stockFullSnapshotPO.getClientCode());
        skuStockChangeMsg.setCountryCode(stockFullSnapshotPO.getCountryCode());
        skuStockChangeMsg.setMkuId(stockFullSnapshotPO.getMkuId());
        skuStockChangeMsg.setJdSkuId(stockFullSnapshotPO.getJdSkuId());
        skuStockChangeMsg.setSkuId(stockFullSnapshotPO.getSkuId());
        skuStockChangeMsg.setChangeType(EsAttributeChangeTypeEnum.STOCK_TAG_CHANGE.getCode());

        List<StockPO> stockPOList = internationalStockMap.get(skuId);
        if (CollectionUtils.isEmpty(stockPOList)) {
            log.error("StockFullSnapshotETLJob.processInternationalStock 没查到库存信息，不处理此sku:{}", skuId);
            return;
        }

        if (isWarehouseStock(stockFullSnapshotPO)) {
            skuStockChangeMsg.setStockType(StockTypeEnum.WAREHOUSE_STOCK.getCode());

            //获取在途标
            List<WarehouseSkuPO> warehouseSkuPOList = warehouseSkuMap.get(skuId);
            Optional<WarehouseSkuPO> warehouseSkuOptional = Optional.empty();
            if (CollectionUtils.isNotEmpty(warehouseSkuPOList)) {
                warehouseSkuOptional = warehouseSkuPOList.stream()
                        .filter(warehouseSkuPO -> Objects.equals(String.valueOf(warehouseSkuPO.getWarehouseId()), warehouseId))
                        .findFirst();
            }

            if (warehouseSkuOptional.isPresent()) {
                handleWarehouseStock(stockPOList, warehouseId, skuStockChangeMsg, warehouseSkuOptional.get());
            } else {
                log.error("StockFullSnapshotETLJob.handleWarehouseStock 备货品的备货仓库存不存在！sku:{}", skuStockChangeMsg.getSkuId());
                skuStockChangeMsg.setHasStockTag(StockConstants.NO_STOCK);
                skuStockChangeMsg.setAvailableStock(0L);
            }
        } else {
            skuStockChangeMsg.setStockType(StockTypeEnum.SUPPLY_STOCK.getCode());
            handleSupplyStock(stockPOList, skuStockChangeMsg);
        }

        log.info("StockFullSnapshotETLJob processInternationalStock end, changeMsg:{}", JSON.toJSONString(skuStockChangeMsg));
        stockChangeMsgList.add(skuStockChangeMsg);
    }

    /**
     * 处理指定仓库的库存信息并设置SKU库存状态和可用库存量
     *
     * @param stockPOList       库存对象列表，包含多个仓库的库存信息
     * @param warehouseId       目标仓库ID，用于筛选特定仓库的库存
     * @param skuStockChangeMsg SKU库存变更消息对象，用于存储处理后的库存状态和可用库存量
     */
    private void handleWarehouseStock(List<StockPO> stockPOList, String warehouseId, SkuStockChangeMsg skuStockChangeMsg, WarehouseSkuPO warehouseSkuPO) {
        Optional<StockPO> stockPOOptional = stockPOList.stream()
                .filter(stockPO -> Objects.equals(stockPO.getWarehouseId(), warehouseId))
                .findFirst();

        if (stockPOOptional.isPresent()) {
            StockPO stockPO = stockPOOptional.get();
            boolean onWaySale = Objects.nonNull(warehouseSkuPO.getOnWaySale()) ?
                    OnWaySaleTypeEnum.SUPPORTED.getCode() == warehouseSkuPO.getOnWaySale() :
                    Boolean.FALSE;

            /*
             * 1、纯现货库存有，则标签为纯现货；
             * 2、纯现货没有，在看在途库存，有则为在途库存；
             * 注意：如果在途标签开启，则算在途，否则就是无货；
             * 3、否则无货；
             */
            Long inStockAvailableStock = StockNumUtils.calculateAvailableStock(stockPO, Boolean.FALSE, ALL_STOCK_TYE);
            if (inStockAvailableStock > 0) {
                skuStockChangeMsg.setHasStockTag(StockConstants.IN_STOCK);
                skuStockChangeMsg.setAvailableStock(inStockAvailableStock);
            } else {
                Long onWayAvailableStock = StockNumUtils.calculateAvailableStock(stockPO, Boolean.TRUE, ALL_STOCK_TYE);
                if (onWayAvailableStock > 0 && onWaySale) {
                    skuStockChangeMsg.setHasStockTag(StockConstants.IN_TRANSIT_STOCK);
                    skuStockChangeMsg.setAvailableStock(onWayAvailableStock);
                } else {
                    skuStockChangeMsg.setHasStockTag(StockConstants.NO_STOCK);
                    skuStockChangeMsg.setAvailableStock(0L);
                }
            }
        } else {
            log.warn("StockFullSnapshotETLJob.handleWarehouseStock 备货品的备货仓库存不存在！sku:{}", skuStockChangeMsg.getSkuId());
            skuStockChangeMsg.setHasStockTag(StockConstants.NO_STOCK);
            skuStockChangeMsg.setAvailableStock(0L);
        }
    }

    /**
     * 处理供应商库存信息，若商品已绑定备货仓则不处理，否则根据库存可用量设置库存状态标签和可用库存数量
     *
     * @param stockPOList       库存信息列表，用于检查是否存在备货仓库存和获取库存数据
     * @param skuStockChangeMsg 库存变更消息对象，用于设置库存状态标签和可用库存数量
     */
    private void handleSupplyStock(List<StockPO> stockPOList, SkuStockChangeMsg skuStockChangeMsg) {
        Boolean hasWarehouseStock = stockPOList.stream()
                .anyMatch(stockPO -> StringUtils.isNotBlank(stockPO.getWarehouseId()));
        if (hasWarehouseStock) {
            log.warn("StockFullSnapshotETLJob.handleSupplyStock 已存在备货仓库存，供应商库存无效不处理！sku:{}", skuStockChangeMsg.getSkuId());
            return; // 商品已经绑定了备货仓，不处理厂直库存
        }

        StockPO stockPO = stockPOList.get(0);
        Long inStockAvailableStock = StockNumUtils.calculateAvailableStock(stockPO, Boolean.FALSE, ALL_STOCK_TYE);
        if (inStockAvailableStock > 0) {
            skuStockChangeMsg.setHasStockTag(StockConstants.IN_STOCK);
            skuStockChangeMsg.setAvailableStock(inStockAvailableStock);
        } else {
            skuStockChangeMsg.setHasStockTag(StockConstants.NO_STOCK);
            skuStockChangeMsg.setAvailableStock(0L);
        }
    }

    /**
     * 处理国内库存数据并生成库存变更消息列表
     *
     * @param domesticStockList  国内库存快照数据列表
     * @param stockChangeMsgList 用于存储生成的库存变更消息列表
     */
    private void processDomesticStocks(String clientCode, List<StockFullSnapshotPO> domesticStockList, List<SkuStockChangeMsg> stockChangeMsgList) {
        if (CollectionUtils.isEmpty(domesticStockList)) {
            log.warn("StockFullSnapshotETLJob.processDomesticStocks 跨境直发库存数据为空，不处理！");
            return;
        }

        //分批&限流查询国内库存信息
        Map<Long, IscMkuStockResDTO> mkuStockResDTOMap = queryDomesticStock(clientCode, domesticStockList);

        //循环标记每条库存的库存有无标签
        for (StockFullSnapshotPO stockCnSnapshotPO : domesticStockList) {
            SkuStockChangeMsg skuStockChangeMsg = new SkuStockChangeMsg();
            skuStockChangeMsg.setClientCode(stockCnSnapshotPO.getClientCode());
            skuStockChangeMsg.setCountryCode(stockCnSnapshotPO.getCountryCode());
            skuStockChangeMsg.setMkuId(stockCnSnapshotPO.getMkuId());
            skuStockChangeMsg.setJdSkuId(stockCnSnapshotPO.getJdSkuId());
            skuStockChangeMsg.setSkuId(stockCnSnapshotPO.getSkuId());
            skuStockChangeMsg.setStockType(StockTypeEnum.CN_STOCK.getCode());
            skuStockChangeMsg.setChangeType(EsAttributeChangeTypeEnum.STOCK_TAG_CHANGE.getCode());

            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(mkuStockResDTOMap)) {
                //获取库存信息
                Long mkuId = stockCnSnapshotPO.getMkuId();
                IscMkuStockResDTO mkuStockResDTO = mkuStockResDTOMap.get(mkuId);
                if (mkuStockResDTO != null) {
                    Integer stockStateType = mkuStockResDTO.getStockStateType();
                    skuStockChangeMsg.setAvailableStock(mkuStockResDTO.getAvailableStock());

                    //补充有货/无货标识
                    if (HAVE_IN_STOCK.contains(stockStateType)) {
                        skuStockChangeMsg.setHasStockTag(StockConstants.IN_STOCK);
                    } else if (HAVE_IN_TRANSIT_STOCK.contains(stockStateType)) {
                        skuStockChangeMsg.setHasStockTag(StockConstants.IN_TRANSIT_STOCK);
                    } else {
                        skuStockChangeMsg.setHasStockTag(StockConstants.NO_STOCK);
                        skuStockChangeMsg.setAvailableStock(0L);
                    }

                    log.info("StockFullSnapshotETLJob processDomesticStocks Use Interface stock, changeMsg:{}", JSON.toJSONString(skuStockChangeMsg));
                } else {
                    log.info("StockFullSnapshotETLJob processDomesticStocks queryCnStock Empty Use default stock, changeMsg:{}", JSON.toJSONString(skuStockChangeMsg));

                    //没有查询到库存信息，则默认用大数据的库存信息兜底处理
                    fillDefaultCnStock(stockCnSnapshotPO, skuStockChangeMsg);
                }
            } else {
                log.info("StockFullSnapshotETLJob processDomesticStocks Use default stock, changeMsg:{}", JSON.toJSONString(skuStockChangeMsg));

                //没有查询到库存信息，则默认用大数据的库存信息兜底处理
                fillDefaultCnStock(stockCnSnapshotPO, skuStockChangeMsg);
            }

            log.info("StockFullSnapshotETLJob processDomesticStocks end, changeMsg:{}", JSON.toJSONString(skuStockChangeMsg));
            stockChangeMsgList.add(skuStockChangeMsg);
        }
    }


    /**
     * 批量查询国内库存信息
     * @param clientCode 客户编码
     * @param domesticStockList 国内库存快照列表
     * @return 商品ID与库存响应DTO的映射关系
     */
    private Map<Long, IscMkuStockResDTO> queryDomesticStock(String clientCode, List<StockFullSnapshotPO> domesticStockList) {
        Map<Long, IscMkuStockResDTO> resultMap = new HashMap<>();

        Integer BATCH_SIZE = BATCH_QUERY_CN_STOCK_SIZE;
        Long requestIntervalMs = getQueryCnStockIntervalTimes();

        List<List<StockFullSnapshotPO>> batches = IntStream.range(0, (domesticStockList.size() + BATCH_SIZE - 1) / BATCH_SIZE)
                .mapToObj(i -> domesticStockList.subList(i * BATCH_SIZE, Math.min(domesticStockList.size(), (i + 1) * BATCH_SIZE)))
                .collect(Collectors.toList());

        for (List<StockFullSnapshotPO> batch : batches) {
            IscMkuStockReadReqDTO mkuStockReadReqDTO = new IscMkuStockReadReqDTO();
            mkuStockReadReqDTO.setClientCode(clientCode);

            List<IscMkuStockItemReadReqDTO> stockItems = batch.stream().map(item -> {
                IscMkuStockItemReadReqDTO mkuStockItemReadReqDTO = new IscMkuStockItemReadReqDTO();
                mkuStockItemReadReqDTO.setMkuId(item.getMkuId());
                mkuStockItemReadReqDTO.setSkuId(item.getSkuId());
                mkuStockItemReadReqDTO.setNum(1);
                return mkuStockItemReadReqDTO;
            }).collect(Collectors.toList());

            mkuStockReadReqDTO.setStockItem(stockItems);

            // 调用接口获取库存信息
            Map<Long, IscMkuStockResDTO> batchResult = stockRpcService.batchQueryMkuStockInfo(mkuStockReadReqDTO);
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(batchResult)) {
                resultMap.putAll(batchResult);
            }

            // 限流控制，等待一段时间再发送下一个批次请求
            try {
                if (requestIntervalMs != null && requestIntervalMs > 0) {
                    TimeUnit.MILLISECONDS.sleep(requestIntervalMs);
                }
            } catch (InterruptedException e) {
                log.error("StockFullSnapshotETLJob queryDomesticStock sleep failed, clientCode:{}, domesticStockList:{}, e", clientCode, JSON.toJSONString(batch), e);
                Thread.currentThread().interrupt();
            }
        }

        return resultMap;
    }

    /**
     * 使用默认的华南区库存信息填充库存变更消息对象
     * @param stockCnSnapshotPO 包含中国区库存信息的快照对象
     * @param skuStockChangeMsg 需要被填充的库存变更消息对象
     */
    private  void fillDefaultCnStock(StockFullSnapshotPO stockCnSnapshotPO, SkuStockChangeMsg skuStockChangeMsg) {
        //没有查询到库存信息，则默认用大数据的库存信息兜底处理
        Long availableStock = stockCnSnapshotPO.getCnStock();
        if (availableStock > 0) {
            skuStockChangeMsg.setHasStockTag(StockConstants.IN_STOCK);
            skuStockChangeMsg.setAvailableStock(availableStock);
        } else {
            skuStockChangeMsg.setHasStockTag(StockConstants.NO_STOCK);
            skuStockChangeMsg.setAvailableStock(0L);
        }
    }


    /**
     * 将库存变更消息列表按批次发送到消息队列
     *
     * @param stockChangeMsgList 需要发送的库存变更消息列表，包含多个SKU库存变更信息
     */
    public void sendMessagesInBatches(List<SkuStockChangeMsg> stockChangeMsgList) {
        int totalSize = stockChangeMsgList.size();
        for (int i = 0; i < totalSize; i += StockConstants.MQ_BATCH_SIZE) {
            // 获取当前批次的子列表
            List<SkuStockChangeMsg> batchList = stockChangeMsgList.subList(i, Math.min(i + StockConstants.MQ_BATCH_SIZE, totalSize));

            // 将子列表转换为Map，键为String类型的mkuId
            Map<String, Object> stockChangeMsgMap = batchList.stream()
                    .collect(Collectors.toMap(
                            msg -> String.valueOf(msg.getMkuId()), // 将Long类型的mkuId转换为String
                            Function.identity()
                    ));

            // 发送当前批次消息
            taskCenterMsgSender.sendMessageBatchRetry(stockChangeMsgMap, mkuEsChangeTopic);
        }
    }


    /**
     * 根据指定条件对股票列表进行分类筛选
     *
     * @param stockList 待分类的股票全量快照列表
     * @param condition 用于筛选股票的条件断言
     * @return 符合条件筛选后的股票列表
     */
    private List<StockFullSnapshotPO> classifyStocks(List<StockFullSnapshotPO> stockList, Predicate<StockFullSnapshotPO> condition) {
        return stockList.stream()
                .filter(condition)
                .collect(Collectors.toList());
    }

    /**
     * 判断库存是否为仓库库存
     *
     * @param stock 库存全量快照对象
     * @return 若库存对象的warehouseId不为空则返回true，否则返回false
     */
    private static boolean isWarehouseStock(StockFullSnapshotPO stock) {
        return StringUtils.isNotBlank(stock.getWarehouseId()) &&
                Objects.equals(stock.getStockType(), StockTypeEnum.WAREHOUSE_STOCK.getCode());
    }

    /**
     * 判断库存是否为供应库存
     *
     * @param stock 库存全量快照对象
     * @return 当仓库ID为空且国内库存为null且供应库存不为null时返回true，否则返回false
     */
    private static boolean isSupplyStock(StockFullSnapshotPO stock) {
        return StringUtils.isBlank(stock.getWarehouseId()) && stock.getSupplyStock() != null &&
                Objects.equals(stock.getStockType(), StockTypeEnum.SUPPLY_STOCK.getCode());
    }

    /**
     * 判断是否为国内库存
     *
     * @param stock 库存全量快照对象
     * @return 如果是国内库存返回true，否则返回false
     */
    private static boolean isCnStock(StockFullSnapshotPO stock) {
        return StringUtils.isBlank(stock.getWarehouseId()) && stock.getJdSkuId() != null &&
                Objects.equals(stock.getStockType(), StockTypeEnum.CN_STOCK.getCode());
    }


    /**
     * 从JSON配置中获取ETL灰度处理的客户端代码集合
     *
     * @return 包含ETL灰度客户端代码的Set集合，若配置为空则返回空集合
     */
    private Set<String> getEtlGrayClientCodes() {
        StockEtlConfigVO stockEtlConfigVO = JSONObject.parseObject(stockEtlConfigs, StockEtlConfigVO.class);
        if (stockEtlConfigVO != null) {
            return stockEtlConfigVO.getEtlGrayClientCodes();
        }
        return Collections.emptySet();
    }


    /**
     * 从JSON配置中获取ETL黑名单客户代码集合
     *
     * @return 包含ETL黑名单客户代码的不可变集合，若配置为空则返回空集合
     */
    private Set<String> getEtlBlackListClientCodes() {
        StockEtlConfigVO stockEtlConfigVO = JSONObject.parseObject(stockEtlConfigs, StockEtlConfigVO.class);
        if (stockEtlConfigVO != null) {
            return stockEtlConfigVO.getEtlBlackClientCodes();
        }
        return Collections.emptySet();
    }

    /**
     * 从JSON配置中解析并获取库存类型枚举对应的编码值
     *
     * @return 库存类型枚举对应的整型编码值，若配置为空或类型不匹配则返回null
     */
    private Integer getEtlStockType() {
        StockEtlConfigVO stockEtlConfigVO = JSONObject.parseObject(stockEtlConfigs, StockEtlConfigVO.class);
        if (stockEtlConfigVO != null) {
            StockTypeEnum typeEnum = StockTypeEnum.getByCode(stockEtlConfigVO.getStockType());
            return typeEnum != null ? typeEnum.getCode() : null;
        }
        return null;
    }

    /**
     * 从JSON配置中获取查询国内stock查询数据的间隔时间
     * @return 查询间隔时间(毫秒)，若配置解析失败则返回null
     */
    private Long getQueryCnStockIntervalTimes() {
        StockEtlConfigVO stockEtlConfigVO = JSONObject.parseObject(stockEtlConfigs, StockEtlConfigVO.class);
        if (stockEtlConfigVO != null) {
            return stockEtlConfigVO.getQueryIntervalTime();
        }

        return null;
    }

}
