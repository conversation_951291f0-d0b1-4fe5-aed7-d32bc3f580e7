package com.jdi.isc.task.center.service.work.task.handler.wimpImport.hsCode;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.hsCode.hu.HsCodeHuApiService;
import com.jdi.isc.product.soa.api.hsCode.hu.req.HsCodeHuSaveUpdateBatchReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.hu.req.HsCodeHuSaveUpdateReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.hu.res.HsCodeHuSaveUpdateBatchResApiDTO;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.common.exception.TaskBizException;
import com.jdi.isc.task.center.common.utils.ExcelUtils;
import com.jdi.isc.task.center.domain.enums.customs.CustomsControlsEnum;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;
import com.jdi.isc.task.center.domain.task.excel.hsCode.HsCodeHuExcelDTO;
import com.jdi.isc.task.center.service.work.task.DisposableAbsJob;
import com.jdi.isc.task.center.service.work.task.frame.JobExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@JobExecutor(taskBizType = TaskBizTypeEnum.PRODUCT_HS_CODE_HU_IMPORT)
public class HsCodeHuImportHandler extends DisposableAbsJob<HsCodeHuExcelDTO> {

    @Resource
    private HsCodeHuApiService hsCodeHuApiService;

    @Override
    public void run(TaskDTO<HsCodeHuExcelDTO> task) {
        log.info("run, taskId={}, data={} " ,task.getTaskId(), JSON.toJSONString(task.getTarget()));
        List<HsCodeHuExcelDTO> excelDTOList = task.getTarget();

        checkData(excelDTOList);

        // 将 list 分割成n个子列表
        List<List<HsCodeHuExcelDTO>> splitedList = ListUtils.partition(excelDTOList, 100);
        for(List<HsCodeHuExcelDTO> splitList: splitedList ){
            try {
                doRunRpc(task, splitList);
            } catch (Exception e) {
                log.error("run 任务 {} 执行失败", task.getTaskId(), e);
            }
        }
    }

    private void doRunRpc(TaskDTO<HsCodeHuExcelDTO> task, List<HsCodeHuExcelDTO> excelDTOList){
        // 转换组装合法的请求参数
        List<HsCodeHuSaveUpdateReqApiDTO> validReqList = excel2ApiDtoBatch(task, excelDTOList);
        if (CollectionUtils.isEmpty(validReqList)) {
            log.info("doRunRpc, validReqList empty.");
            return;
        }

        // 只处理校验通过的数据
        HsCodeHuSaveUpdateBatchReqApiDTO reqParam = new HsCodeHuSaveUpdateBatchReqApiDTO();
        reqParam.setReqList(validReqList);

        DataResponse<HsCodeHuSaveUpdateBatchResApiDTO> rpcRes = hsCodeHuApiService.saveOrUpdateBatch(reqParam);
        // 响应失败
        if (null==rpcRes || !Boolean.TRUE.equals(rpcRes.getSuccess()) || null==rpcRes.getData()) {
            log.info("doRunRpc, rpc saveOrUpdateBatch fail. rpcRes={}", JSONObject.toJSONString(rpcRes));
            String failMsg = (null!=rpcRes && StringUtils.isNotBlank(rpcRes.getMessage()))?rpcRes.getMessage():"未知结果";

            HsCodeHuSaveUpdateBatchResApiDTO rpcData = null!=rpcRes? rpcRes.getData():null;
            Map<String, String> failedRecordMap = null!=rpcData?rpcData.getFailedRecordMap():null;

            // 全部标记为失败
            for(HsCodeHuExcelDTO excelDTO:excelDTOList){
                // 已经标记为失败的数据跳过
                if (Boolean.FALSE.equals(excelDTO.getValid())){
                    continue;
                }

                String failedRecordMsg = null!=failedRecordMap?failedRecordMap.get(uniqueFlag(excelDTO)):null;
                excelDTO.failed(failMsg + (StringUtils.isNotBlank(failedRecordMsg)?("。"+failedRecordMsg):""));
            }
            return;
        }

        // 解析返回结果
        HsCodeHuSaveUpdateBatchResApiDTO rpcData = rpcRes.getData();
        Map<String/*唯一标识*/, String/*原因*/> failedRecordMap = rpcData.getFailedRecordMap();
        for(HsCodeHuExcelDTO excelDTO:excelDTOList){
            // 已经标记为失败的数据跳过
            if (Boolean.FALSE.equals(excelDTO.getValid())){
                continue;
            }

            String uniqueFlag = uniqueFlag(excelDTO);
            // 失败的数据设置失败原因
            if (null!=failedRecordMap && failedRecordMap.containsKey(uniqueFlag)){
                excelDTO.failed(failedRecordMap.get(uniqueFlag));
            }else {
                excelDTO.success();
            }
        }
    }

    private List<HsCodeHuSaveUpdateReqApiDTO> excel2ApiDtoBatch(TaskDTO<HsCodeHuExcelDTO> task, List<HsCodeHuExcelDTO> excelDTOList){
        if (CollectionUtils.isEmpty(excelDTOList)) {
            return Collections.emptyList();
        }

        List<HsCodeHuSaveUpdateReqApiDTO> resList = new ArrayList<>();
        for(HsCodeHuExcelDTO excelDTO:excelDTOList){
            // 未校验通过的数据不处理
            if (Boolean.FALSE.equals(excelDTO.getValid())){
                continue;
            }

            HsCodeHuSaveUpdateReqApiDTO apiDTO = null;
            try {
                apiDTO = excel2ApiDto(task, excelDTO);
            }catch (TaskBizException e){
                excelDTO.failed(e.getMessage());
                continue;
            }catch (Exception e) {
                log.error("excel2ApiDtoBatch, Exception excelDTO={}", JSONObject.toJSONString(excelDTO), e);
                excelDTO.failed("出错了，请检查数据是否合法");
                continue;
            }

            resList.add(apiDTO);
        }

        return resList;
    }

    private HsCodeHuSaveUpdateReqApiDTO excel2ApiDto(TaskDTO<HsCodeHuExcelDTO> task, HsCodeHuExcelDTO excelDto){
        HsCodeHuSaveUpdateReqApiDTO target = new HsCodeHuSaveUpdateReqApiDTO();
        try {
            target.setHsCode(excelDto.getHsCode());
            target.setImportTax(ExcelUtils.string2BigDecimal(excelDto.getImportTax()));
            target.setValueAddedTax(ExcelUtils.string2BigDecimal(excelDto.getValueAddedTax()));
            target.setAntiSubsidyTax(ExcelUtils.string2BigDecimal(excelDto.getAntiSubsidyTax()));
            target.setAntiDumpingTax(ExcelUtils.string2BigDecimal(excelDto.getAntiDumpingTax()));

            if("是".equals(excelDto.getControls())){
                target.setControls(CustomsControlsEnum.REQUIRED.getCode());
            }else if("否".equals(excelDto.getControls())){
                target.setControls(CustomsControlsEnum.NOT_REQUIRED.getCode());
                if (StringUtils.isNotBlank(target.getControlsInfo())){
                    throw new TaskBizException("是否管制为“否”时，不允许填写管制信息");
                }
            }else {
                throw new TaskBizException(String.format("* 是否管制输入不合法%s",excelDto.getControls()));
            }
            target.setControlsInfo(excelDto.getControlsInfo());
            target.setRemark(excelDto.getRemark());
            target.setCreator(task.getOperator());
            target.setUpdater(task.getOperator());
        }catch (Exception e){
            throw new RuntimeException("当前行数据输入不合法,请检查");
        }
        return target;
    }

    @Override
    public String uniqueFlag(HsCodeHuExcelDTO input){
        return StringUtils.isNotBlank(input.getHsCode())?input.getHsCode():"";
    }

    @Override
    public DataResponse checkByBiz(HsCodeHuExcelDTO excelDto) {
        if(StringUtils.isNotBlank(excelDto.getControls()) && "是".equals(excelDto.getControls().trim())
                && StringUtils.isBlank(excelDto.getControlsInfo())){
            excelDto.failed("是否管制为“是”时，管制信息不能为空！");
        }

        return DataResponse.success();
    }
}