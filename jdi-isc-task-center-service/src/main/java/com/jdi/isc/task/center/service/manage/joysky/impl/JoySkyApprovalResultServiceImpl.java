package com.jdi.isc.task.center.service.manage.joysky.impl;

import com.jdi.isc.task.center.domain.enums.ApprovalStatusEnum;
import com.jdi.isc.task.center.domain.joysky.biz.JoySkyApplyResultDTO;
import com.jdi.isc.task.center.domain.joysky.msg.ApprovalResultMsg;
import com.jdi.isc.task.center.domain.joysky.po.JoySkyApprovalTaskPO;
import com.jdi.isc.task.center.rpc.mq.TaskCenterMqService;
import com.jdi.isc.task.center.service.manage.joysky.JoySkyApprovalResultService;
import com.jdi.isc.task.center.service.manage.joysky.JoySkyTaskManageService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/11/4
 * @Description 审批结果处理类
 */
@Slf4j
@Service
public class JoySkyApprovalResultServiceImpl implements JoySkyApprovalResultService {

    @Resource
    private JoySkyTaskManageService joySkyTaskManageService;

    @Value("${topic.jmq4.product.joySky.result}")
    private String topic;

    @Resource
    private TaskCenterMqService taskCenterMqService;

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public void approvalTaskFinish(JoySkyApplyResultDTO resultParam) {
        doApprovalResult(resultParam, ApprovalStatusEnum.FINISH, "");
    }


    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public void approvalTaskReject(JoySkyApplyResultDTO resultParam) {
        doApprovalResult(resultParam, ApprovalStatusEnum.REJECT, resultParam.getApproverRemark());
    }

    /**
     * 处理审批结果并发送消息。
     *
     * @param resultParam        审批结果参数对象，包含审批任务PO和其他相关信息。
     * @param approvalStatusEnum 审批状态枚举值，表示当前审批的结果。
     * @param approvalComment    审批评论信息。
     */
    private void doApprovalResult(JoySkyApplyResultDTO resultParam, ApprovalStatusEnum approvalStatusEnum, String approvalComment) {
        JoySkyApprovalTaskPO skyApprovalTaskPO = resultParam.getJoySkyApprovalTaskPO();
        //记录审批流水
        joySkyTaskManageService.updateApprovalTask(skyApprovalTaskPO);
        joySkyTaskManageService.saveApprovalTaskLog(skyApprovalTaskPO, resultParam.getActionType());

        //发送审批结果消息
        ApprovalResultMsg approvalResultMsg = buildResultMsg(resultParam, approvalStatusEnum, skyApprovalTaskPO);
        log.info("发送审批流结果消息，result:{}", approvalResultMsg);
        taskCenterMqService.sendMessageRetry(approvalResultMsg.getProcessKey(), approvalResultMsg, topic);
    }

    /**
     * 构建审批结果消息对象。
     *
     * @param resultParam        审批结果参数对象，包含审批人ERP号和审批备注信息。
     * @param approvalStatusEnum 审批状态枚举值，表示当前审批的状态。
     * @param skyApprovalTaskPO  审批任务对象，包含流程类型、流程关键字等信息。
     * @return 审批结果消息对象，包含流程类型、流程关键字、流程状态、拒绝原因和操作人等信息。
     */
    private static @NotNull ApprovalResultMsg buildResultMsg(JoySkyApplyResultDTO resultParam, ApprovalStatusEnum approvalStatusEnum,
                                                             JoySkyApprovalTaskPO skyApprovalTaskPO) {
        ApprovalResultMsg approvalResultMsg = new ApprovalResultMsg();
        approvalResultMsg.setProcessType(skyApprovalTaskPO.getProcessType());
        approvalResultMsg.setProcessKey(skyApprovalTaskPO.getProcessKey());
        approvalResultMsg.setProcessInstanceId(skyApprovalTaskPO.getProcessInstanceId());
        approvalResultMsg.setProcessStatus(approvalStatusEnum.getCode());
        approvalResultMsg.setRejectReason(resultParam.getApproverRemark());
        approvalResultMsg.setOperator(resultParam.getApproverErp());
        return approvalResultMsg;
    }

}
