package com.jdi.isc.task.center.service.work.task.handler.wimpImport;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.sku.res.SkuBaseInfoApiDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuFeatureApiDTO;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.warehouse.base.WarehouseSkuDTO;
import com.jdi.isc.product.soa.api.warehouse.req.WarehouseBatchGetReqDTO;
import com.jdi.isc.product.soa.api.warehouse.res.WarehouseResDTO;
import com.jdi.isc.product.soa.api.warehouse.res.WarehouseSkuRelationResDTO;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;
import com.jdi.isc.task.center.domain.task.excel.WimpStockImportDTO;
import com.jdi.isc.task.center.rpc.sku.SkuRpcService;
import com.jdi.isc.task.center.rpc.stock.StockRpcService;
import com.jdi.isc.task.center.rpc.warehouse.WarehouseRpcService;
import com.jdi.isc.task.center.service.work.task.DisposableAbsJob;
import com.jdi.isc.task.center.service.work.task.frame.JobExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量更新库存
 * <AUTHOR>
 * @date 2024/8/22
 **/
@Slf4j
@Service
@JobExecutor(taskBizType = TaskBizTypeEnum.SKU_STOCK_BATCH_IMPORT)
public class DisposableWimpStockImportHandler extends DisposableAbsJob<WimpStockImportDTO> {

    @Resource
    private StockRpcService stockRpcService;
    @Resource
    private WarehouseRpcService warehouseRpcService;
    @Resource
    private SkuRpcService skuRpcService;

    @Override
    public void run(TaskDTO<WimpStockImportDTO> task) {

        List<WimpStockImportDTO> stockImportList = task.getTarget();
        log.info("DisposableWImpStockImportHandler.run  taskId={},stockImportList:{}",task.getTaskId(), stockImportList);
        if (CollectionUtils.isEmpty(stockImportList)) {
            return;
        }

        // 校验SKU ID、仓库编码的合规性
        this.validateParam(stockImportList);

        // 调用设置库存接口

        List<WimpStockImportDTO> validStockImportDTOList = stockImportList.stream().filter(WimpStockImportDTO::getValid).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(validStockImportDTOList)) {
            Map<String,Boolean> skuResultMap = Maps.newHashMap();

            // 分批更新库存
            List<List<WimpStockImportDTO>> dtoPartationList = Lists.partition(validStockImportDTOList, 100);
            String updater = StringUtils.isNotBlank(task.getOperator()) ? task.getOperator() : Constant.SYSTEM;
            for (List<WimpStockImportDTO> wimpStockImportDTOS : dtoPartationList) {
                StockManageReqDTO reqDTO = new StockManageReqDTO();
                try {
                    // sku绑定仓库
                    List<WarehouseSkuDTO> warehouseSkuDTOList = wimpStockImportDTOS.stream().filter(dto -> Objects.nonNull(dto.getWarehouseId()) && !dto.getBindStatus()).map(dto -> this.importDto2WarehouseSku(dto,updater)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(warehouseSkuDTOList)) {
                        WarehouseSkuRelationResDTO skuRelationResDTO = warehouseRpcService.bindWarehouseSku(warehouseSkuDTOList);
                        if (Objects.isNull(skuRelationResDTO) || CollectionUtils.isNotEmpty(skuRelationResDTO.getFailedSet())) {
                            for (WimpStockImportDTO dto : wimpStockImportDTOS) {
                                if (skuRelationResDTO.getFailedSet().contains(""+dto.getWarehouseId()+dto.getSkuId())){
                                    dto.failed(String.format("商品 %s 和仓库 %s 绑定失败",dto.getSkuId(),dto.getWarehouseNo()));
                                }
                            }
                        }
                    }

                    // 循环更新库存
                    wimpStockImportDTOS.stream().filter(WimpStockImportDTO::getValid).forEach(dto -> {
                        StockItemManageReqDTO stockItemManageReqDTO = new StockItemManageReqDTO(Long.parseLong(dto.getSkuId()), Long.parseLong(dto.getStockNum()), updater, Objects.isNull(dto.getWarehouseId()) ? null : String.valueOf(dto.getWarehouseId()));
                        List<StockItemManageReqDTO> stockItemManageReqDTOList = Lists.newArrayList(stockItemManageReqDTO);
                        reqDTO.setStockItem(stockItemManageReqDTOList);
                        reqDTO.setBizNo(UUID.randomUUID().toString());
                        reqDTO.setStockWriteType(1); // 1标识增加库存数量
                        Boolean updateStock = stockRpcService.saveOrUpdateStock(reqDTO);
                        skuResultMap.put(dto.getSkuId(), updateStock);
                    });
                } catch (Exception e) {
                    log.error("DisposableWImpStockImportHandler.run update stock rpc exception 入参:[{}]", JSON.toJSONString(reqDTO),e);
                    skuResultMap.putAll(wimpStockImportDTOS.stream().collect(Collectors.toMap(WimpStockImportDTO::getSkuId, r -> false)));
                }
            }

            for (WimpStockImportDTO stockImportDTO : stockImportList) {
                if (!stockImportDTO.getValid()) {
                    continue;
                }
                String skuId = stockImportDTO.getSkuId();
                if (skuResultMap.get(skuId)) {
                    stockImportDTO.success();
                }else {
                    stockImportDTO.failed(String.format("商品 %s 更新库存失败",skuId));
                }
            }
        }
    }


    /**
     * 校验并处理库存导入参数
     *
     * @param stockImportList 库存导入列表
     */
    private void validateParam(List<WimpStockImportDTO> stockImportList) {

        List<Long> skuIdList = Lists.newArrayList();
        List<String> warhouseNoList = Lists.newArrayList();

        for (WimpStockImportDTO stockImportDTO : stockImportList) {
            if (!stockImportDTO.getValid()) {
                continue;
            }
            skuIdList.add(Long.parseLong(stockImportDTO.getSkuId()));
            if (StringUtils.isNotBlank(stockImportDTO.getWarehouseNo())){
                warhouseNoList.add(stockImportDTO.getWarehouseNo());
            }
        }

        // 校验仓库是否存在 存在设置仓库ID
        Map<String, WarehouseResDTO> warehouseResDTOMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(warhouseNoList)){
            List<List<String>> partition = Lists.partition(warhouseNoList, 100);
            for (List<String> list : partition) {
                WarehouseBatchGetReqDTO reqDTO = new WarehouseBatchGetReqDTO();
                reqDTO.setWarehouseNos(Sets.newHashSet(list));
                warehouseResDTOMap.putAll(warehouseRpcService.queryWarehouseMap(reqDTO));
            }
        }

        // 只有跨境品才能绑定仓库
        Map<String, SkuBaseInfoApiDTO> skuMap = Maps.newHashMap();
        // sku属性
        Map<String, SkuFeatureApiDTO> skuFeatureApiDTOMap = Maps.newHashMap();
        // sku和仓库绑定关系
        Map<String, Map<String, WarehouseSkuDTO>> warehouseSkuMap = Maps.newHashMap();
        List<List<Long>> skuPartition = Lists.partition(skuIdList, 100);
        for (List<Long> longs : skuPartition) {
            HashSet<Long> skuIds = Sets.newHashSet(longs);
            skuMap.putAll(skuRpcService.querySkuInfo(skuIds));
            skuFeatureApiDTOMap.putAll(skuRpcService.querySkuFeature(skuIds));
            warehouseSkuMap.putAll(warehouseRpcService.querySkuRelationMap(longs.stream().map(String::valueOf).collect(Collectors.toSet())));
        }
        for (WimpStockImportDTO stockImportDTO : stockImportList) {
            if (!stockImportDTO.getValid()) {
                continue;
            }
            // 校验商品是否存在
            if (!skuMap.containsKey(stockImportDTO.getSkuId())) {
                stockImportDTO.failed(String.format("商品 %s 不存在",stockImportDTO.getSkuId()));
                continue;
            }

            // 跨境品
            SkuBaseInfoApiDTO skuBaseInfoApiDTO = skuMap.get(stockImportDTO.getSkuId());

            if (StringUtils.isNotBlank(stockImportDTO.getWarehouseNo())){
                if (!CountryConstant.COUNTRY_ZH.equals(skuBaseInfoApiDTO.getSourceCountryCode())) {
                    stockImportDTO.failed(String.format("商品 %s 非跨境商品，不能设置备货库存",stockImportDTO.getSkuId()));
                    continue;
                }

                // 备货模式
/*                SkuFeatureApiDTO skuFeatureApiDTO = skuFeatureApiDTOMap.get(stockImportDTO.getSkuId());
                if (Objects.isNull(skuFeatureApiDTO) || Objects.isNull(skuFeatureApiDTO.getPurchaseModel()) || skuFeatureApiDTO.getPurchaseModel() != 1) {
                    stockImportDTO.setPurchaseModel(Boolean.FALSE);
                    stockImportDTO.failed(String.format("商品 %s 非备货商品，不能设置备货库存",stockImportDTO.getSkuId()));
                    continue;
                }else {
                    stockImportDTO.setPurchaseModel(Boolean.TRUE);
                }*/

                // 仓库编码是否存在
                if (StringUtils.isNotBlank(stockImportDTO.getWarehouseNo()) && !warehouseResDTOMap.containsKey(stockImportDTO.getWarehouseNo())) {
                    stockImportDTO.failed("仓库编码不存在,请检查");
                    continue;
                }else {
                    WarehouseResDTO warehouseResDTO = warehouseResDTOMap.get(stockImportDTO.getWarehouseNo());
                    // 设置仓库ID
                    stockImportDTO.setWarehouseId(warehouseResDTO.getId());
                    // 仓库和sku关系存在 sku-> warehouseId-> sku+仓库ID信息
                    if (warehouseSkuMap.containsKey(stockImportDTO.getSkuId()) && warehouseSkuMap.get(stockImportDTO.getSkuId()).containsKey(String.valueOf(warehouseResDTO.getId()))) {
                        stockImportDTO.setBindStatus(Boolean.TRUE);
                    }
                }
            }else {
                if (CountryConstant.COUNTRY_ZH.equals(skuBaseInfoApiDTO.getSourceCountryCode())) {
                    stockImportDTO.failed(String.format("商品 %s 跨境商品，备货仓编码不能为空",stockImportDTO.getSkuId()));
                    continue;
                }

                // 备货模式
        /*        SkuFeatureApiDTO skuFeatureApiDTO = skuFeatureApiDTOMap.get(stockImportDTO.getSkuId());
                if (Objects.nonNull(skuFeatureApiDTO) && Objects.nonNull(skuFeatureApiDTO.getPurchaseModel()) && skuFeatureApiDTO.getPurchaseModel() == 1) {
                    stockImportDTO.setPurchaseModel(Boolean.TRUE);
                    stockImportDTO.failed(String.format("商品 %s 备货商品，备货仓编码不能为空",stockImportDTO.getSkuId()));
                    continue;
                }else {
                    stockImportDTO.setPurchaseModel(Boolean.FALSE);
                }*/
            }
        }
    }

    private WarehouseSkuDTO importDto2WarehouseSku(WimpStockImportDTO dto,String operator){
        if ( dto == null ) {
            return null;
        }

        WarehouseSkuDTO warehouseSkuDTO = new WarehouseSkuDTO();

        warehouseSkuDTO.setWarehouseId( dto.getWarehouseId() );
        if ( dto.getSkuId() != null ) {
            warehouseSkuDTO.setSkuId( Long.parseLong( dto.getSkuId() ) );
        }
         if (!dto.getBindStatus()) {
             warehouseSkuDTO.setBindStatus(1);
         }
         warehouseSkuDTO.setCreator(operator);
         warehouseSkuDTO.setUpdater(operator);
         long now = System.currentTimeMillis();
         warehouseSkuDTO.setCreateTime(now);
         warehouseSkuDTO.setUpdateTime(now);
        return warehouseSkuDTO;
     }
}
