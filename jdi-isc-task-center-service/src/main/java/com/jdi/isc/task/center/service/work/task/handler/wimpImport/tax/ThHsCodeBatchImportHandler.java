package com.jdi.isc.task.center.service.work.task.handler.wimpImport.tax;

//import com.jdi.isc.product.soa.api.taxRate.res.HsCodeCustomsValuationDTO;
//import com.jdi.isc.product.soa.api.taxRate.res.ThHsCodeDTO;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.domain.task.excel.countrTax.ThSkuHsCodeExcelDTO;
import com.jdi.isc.task.center.service.work.task.DisposableAbsJob;
import com.jdi.isc.task.center.service.work.task.frame.JobExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 泰国商品关税配置批量导入(一次性解析全部excel)
 * <AUTHOR>
 * @date 20250705
 */
@Service
@Slf4j
@JobExecutor(taskBizType = TaskBizTypeEnum.PRODUCT_HS_CODE_TH_IMPORT)
public class ThHsCodeBatchImportHandler extends DisposableAbsJob<ThSkuHsCodeExcelDTO> {

//    @Override
//    public void run(TaskDTO<ThSkuHsCodeExcelDTO> task){
//        log.info("ThSkuTaxBatchImportHandler.run {}, data:{} ",task.getTaskId(), task.getTarget().size());
//        if(CollectionUtils.isNotEmpty(task.getTarget())){
//            //推入数据
//
//           int total = 0;
//           System.out.println("测试导入泰国税率表开始");
//            /**逻辑处理
//             *读取所有EXCEL数据，循环处理
//             * 对于HS_CODE的不存在主各国数据表的数据，进行插入，对于HS _CODE已经存在的数据，返回结果EXCEL通知上传人人工校对后，页面上修改（因为税率变化对商品价格有很大影响，暂时先人工修改）
//             * 判断jd_SKU_ID和国家，HSCODE是否存在循环写入国内SKUID与HSCODE的数据表jdi_isc_product_customs_relation表，如果存在，返回导出结果，该商品已经存在，不需要导入
//             * 不存在：更新循环写入国内SKUID与HSCODE的数据表jdi_isc_product_customs_relation表，返回结果：导入成功
//             */
//            /*for(ThSkuHsCodeExcelDTO skuHsCode : task.getTarget()){
//                try {
//                    ThHsCodeDTO target = excel2dto(task.getOperator(),skuHsCode);
//                    DataResponse<Boolean> res = iscProductSoaTaxWriteApiService.saveOrUpdateThTax(target);
//                    if(res!=null && res.getSuccess() && res.getData()){
//                        skuTax.setResult(res.getMessage());
//                    }else {
//                        skuTax.failed(res!=null?res.getMessage():"");
//                    }
//                }catch (Exception e){
//                    log.error("ThSkuTaxBatchImportHandler.run invoke error, target:{}", JSON.toJSONString(skuTax),e);
//                    if(e.getMessage().contains(Constant.EXHAUSTED_KEYWORD)){
//                        skuTax.failed("系统当前负载过大,请联系管理员或稍后重试该记录导入任务.");
//                    }else if(e.getMessage().contains(Constant.TIME_OUT_KEYWORD)){
//                        skuTax.setResult("success");
//                    }else{
//                        skuTax.failed(e.getMessage());
//                    }
//                }
//            }*/
//
//        }
//    }
//    private ThHsCodeDTO excel2dto(String pin, ThSkuHsCodeExcelDTO dto){
//        Date now = new Date();
//        ThHsCodeDTO target = new ThHsCodeDTO();
//        try {
//            target.setHsCode(dto.getHsCode());
//            target.setValueAddedTax(new BigDecimal(dto.getValueAddedTax()));
//            target.setConsumptionTax(new BigDecimal(dto.getConsumptionTax()));
//            target.setFormeTax(new BigDecimal(dto.getFormeTax()));
//            target.setMfnTax(new BigDecimal(dto.getMfnTax()));
//            target.setAntiDumpingTax(new BigDecimal(dto.getAntiDumpingTax()));
//            target.setLocalTax(new BigDecimal(dto.getLocalTax()));
//            if("是".equals(dto.getIsControlled())){
//                target.setIsControlled(1);
//            }else if("否".equals(dto.getIsControlled())){
//                target.setIsControlled(0);
//
//            }else {
//                throw new RuntimeException(String.format("是否管控字段不合法，只能填写是或否且不能为空%s",target.getIsControlled()));
//            }
//            target.setControlReason(dto.getControlReason());
//            target.setBrandAuthorization(dto.getBrandAuthorization());
//            target.setRemark(dto.getRemark());
//            target.setCreator(pin);
//            target.setUpdater(pin);
//            target.setCreateTime(now);
//            target.setUpdateTime(now);
//
//        }catch (Exception e){
//            throw new RuntimeException("当前行数据输入不合法,请检查");
//        }
//        return target;
//    }

    /** 实体转换*/
   /* private ThSkuTaxVO excel2vo(String pin, ThSkuTaxExcelDTO skuTax){
        Date now = new Date();
        ThSkuTaxVO target = new ThSkuTaxVO();
        try {
            target.setHsCode(skuTax.getHsCode());
            target.setJdSkuId(Long.valueOf(skuTax.getJdSkuId()));
            target.setValueAddedTax(new BigDecimal(skuTax.getValueAddedTax()));
            target.setConsumptionTax(new BigDecimal(skuTax.getConsumptionTax()));
            target.setFormeTax(new BigDecimal(skuTax.getFormeTax()));
            target.setMfnTax(new BigDecimal(skuTax.getMfnTax()));
            target.setAntiDumpingTax(new BigDecimal(skuTax.getAntiDumpingTax()));
            target.setLocalTax(new BigDecimal(skuTax.getLocalTax()));
            if("是".equals(skuTax.getTisi())){
                target.setTisi(1);
            }else if("否".equals(skuTax.getTisi())){
                target.setTisi(0);
            }else {
                throw new RuntimeException(String.format("是否需要tisi输入不合法%s",skuTax.getTisi()));
            }
            target.setRemark(skuTax.getRemark());
            target.setCreator(pin);
            target.setUpdater(pin);
            target.setCreateTime(now);
            target.setUpdateTime(now);
        }catch (Exception e){
            throw new RuntimeException("当前行数据输入不合法,请检查");
        }
        return target;
    }*/


}
