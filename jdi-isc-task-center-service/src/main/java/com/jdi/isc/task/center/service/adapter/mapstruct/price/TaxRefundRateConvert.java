package com.jdi.isc.task.center.service.adapter.mapstruct.price;


import com.jdi.isc.product.soa.api.markupRate.res.MarkupRateDTO;
import com.jdi.isc.product.soa.api.price.taxRefundRate.res.TaxRefundRateDTO;
import com.jdi.isc.task.center.domain.task.excel.CountryMarkupRateImportDTO;
import com.jdi.isc.task.center.domain.task.excel.TaxRefundRateImportDTO;
import com.jdi.isc.task.center.rpc.markupRate.impl.StatusMarkupRateDTO;
import com.jdi.isc.task.center.rpc.price.impl.StatusTaxRefundRateDTO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 物料和MKU绑定对象转换
 * @Author: wangpeng965
 * @Date: 2024/08/05 16:45
 **/
@Mapper
public interface TaxRefundRateConvert {

    TaxRefundRateConvert INSTANCE = Mappers.getMapper(TaxRefundRateConvert.class);

    @InheritConfiguration
    List<TaxRefundRateDTO> excelDtoList2ReqDtoList(List<TaxRefundRateImportDTO> dtoList);
    @InheritConfiguration
    List<TaxRefundRateImportDTO> apiList2ExcelList(List<TaxRefundRateDTO> dtoList);


    @InheritConfiguration
    List<StatusTaxRefundRateDTO> excelDtoList2DtoList(List<TaxRefundRateImportDTO> dtoList);
    @InheritConfiguration
    List<TaxRefundRateImportDTO> dtoList2ExcelList(List<StatusTaxRefundRateDTO> dtoList);
}
