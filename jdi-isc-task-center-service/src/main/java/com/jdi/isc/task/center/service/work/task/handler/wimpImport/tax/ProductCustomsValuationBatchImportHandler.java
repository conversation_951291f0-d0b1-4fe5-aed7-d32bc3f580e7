package com.jdi.isc.task.center.service.work.task.handler.wimpImport.tax;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.customs.req.ProductCustomsDraftApiDTO;
import com.jdi.isc.product.soa.api.sku.req.QuerySkuReqDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuBaseInfoApiDTO;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.common.utils.DateUtil;
import com.jdi.isc.task.center.domain.gms.req.JdProductQueryDTO;
import com.jdi.isc.task.center.domain.gms.res.JdProductDTO;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;
import com.jdi.isc.task.center.domain.task.excel.countrTax.ProductCustomsValuationExcelDTO;
import com.jdi.isc.task.center.rpc.customs.IscProductCustomsDraftWriteRpcService;
import com.jdi.isc.task.center.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.task.center.rpc.sku.SkuRpcService;
import com.jdi.isc.task.center.service.work.task.DisposableAbsJob;
import com.jdi.isc.task.center.service.work.task.frame.JobExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@JobExecutor(taskBizType = TaskBizTypeEnum.PRODUCT_CUSTOMS_VALUATION)
public class ProductCustomsValuationBatchImportHandler extends DisposableAbsJob<ProductCustomsValuationExcelDTO> {

    @Resource
    private IscProductCustomsDraftWriteRpcService iscProductCustomsDraftWriteRpcService;

    @Resource
    private SkuInfoRpcService skuInfoRpcService;

    @Resource
    private SkuRpcService skuRpcService;


    private final static String EXCEL_TASK = "excel";

    private final static String CREATE_ACTION= "create" ;


    @Override
    public void run(TaskDTO<ProductCustomsValuationExcelDTO> task){
        log.info("ProductCustomsValuationBatchImportHandler.run {}, data:{} ",task.getTaskId(), JSON.toJSONString(task.getTarget()));
        if(CollectionUtils.isNotEmpty(task.getTarget())){
            //推入数据库
            //如果是批次需要调用最新的批次ID
            Set<Long> jdSkuIds = new HashSet<>();
            Map<String, String> errorMsg = new HashMap<>();
            task.getTarget().stream()
                    .filter(f -> !Boolean.FALSE.equals(f.getValid()))
                    .forEach(f -> {
                        if (!StringUtils.isNumeric(f.getJdSkuId())) {
                            errorMsg.put(f.getJdSkuId(), "jdSkuId非数字格式");
                            return;
                        }
                        jdSkuIds.add(Long.valueOf(f.getJdSkuId()));
                    });


            List<Long> validJdSkuIds = getValidJdSkuIds(jdSkuIds);
            Map<Long, List<SkuBaseInfoApiDTO>> relateMap = queryIntSkuId(validJdSkuIds);
            long time = System.currentTimeMillis();
            String batchId = "excel-"+task.getTaskId();
            for(ProductCustomsValuationExcelDTO skuValuationDto : task.getTarget()){
                try {
                    // 已经标记为失败的数据跳过
                    if (Boolean.FALSE.equals(skuValuationDto.getValid())){
                        continue;
                    }

                    if (errorMsg.containsKey(skuValuationDto.getJdSkuId())) {
                        skuValuationDto.failed(errorMsg.get(skuValuationDto.getJdSkuId()));
                        continue;
                    }
                    Long jdSkuId = Long.valueOf(skuValuationDto.getJdSkuId());
                    if (!validJdSkuIds.contains(jdSkuId)){
                        skuValuationDto.failed("jdSkuId不存在");
                        continue;
                    }

                    List<ProductCustomsDraftApiDTO> createList = excel2dto(task.getOperator(),skuValuationDto,relateMap.get(jdSkuId),time);
                    saveSkuDate(batchId,createList,skuValuationDto);
                }catch (Exception e){
                    log.error("参数skuValuationDto的参数错误:{}", JSON.toJSONString(skuValuationDto),e);
                    if(e.getMessage().contains(Constant.EXHAUSTED_KEYWORD)){
                        skuValuationDto.failed("系统当前负载过大,请联系管理员或稍后重试该记录导入任务.");
                    }else if(e.getMessage().contains(Constant.TIME_OUT_KEYWORD)){
                        skuValuationDto.setResult("success");
                    }else{
                        skuValuationDto.failed(e.getMessage());
                    }
                }
            }
        }


    }

    private void saveSkuDate(String batchId, List<ProductCustomsDraftApiDTO> createList, ProductCustomsValuationExcelDTO skuValuationDto) {

        StringBuilder error = new StringBuilder();
        StringBuilder success = new StringBuilder();
        for (ProductCustomsDraftApiDTO create : createList) {
            create.setBatchId(batchId);
            DataResponse<Boolean> res = iscProductCustomsDraftWriteRpcService.saveOrUpdate(create);
            if (!Boolean.TRUE.equals(res.getSuccess())) {
                if (Objects.nonNull(create.getSkuId())) {
                    error.append(create.getSkuId()).append(":");
                }
                error.append(res.getMessage()).append(",");
            }else{
                success.append(create.getSkuId()).append(",");
            }
        }
        if (success.length() < 1) {
            skuValuationDto.failed("导入失败："+ error);
            return;
        }
        String msg = "导入成功待评估：" + success;
        if (error.length() > 0) {
            msg = msg + "；导入失败：" + error;
        }
        skuValuationDto.success();
        skuValuationDto.setResult(msg);
    }


    private Map<Long,List<SkuBaseInfoApiDTO>> queryIntSkuId(List<Long> validJdSkuIds) {
        Map<Long, List<SkuBaseInfoApiDTO>> result = new HashMap<>();
        List<List<Long>> partition = Lists.partition(validJdSkuIds, Constant.PAGE_BATCH_SIZE_INT);

        for (List<Long> part : partition) {
            //查询绑定的国际skuId
            QuerySkuReqDTO reqDTO = new QuerySkuReqDTO();
            reqDTO.setJdSkuIds(new HashSet<>(part));
            List<SkuBaseInfoApiDTO> list = skuRpcService.querySkuInfoByJdSkuId(reqDTO);
            if (list.isEmpty()) {
                continue;
            }
            Map<Long, List<SkuBaseInfoApiDTO>> map = list.stream()
                    .filter(Objects::nonNull)
                    .filter(sku -> sku.getJdSkuId() != null && sku.getSkuId() != null)
                    .collect(Collectors.groupingBy(
                            SkuBaseInfoApiDTO::getJdSkuId,
                            Collectors.mapping(
                                    s ->s,
                                    Collectors.toList()
                            )
                    ));
            result.putAll(map);
        }
        return result;
    }



    private List<Long> getValidJdSkuIds(Set<Long> jdSkuIds) {
        ArrayList<Long> jdSkuIdList = new ArrayList<>(jdSkuIds);
        List<List<Long>> partition = Lists.partition(jdSkuIdList, Constant.PAGE_BATCH_SIZE_INT);
        List<Long> validJdSkuId = new ArrayList<>();
        for (List<Long> part : partition) {
            JdProductQueryDTO param = new JdProductQueryDTO();
            param.setSkuIds(part);
            Map<Long, JdProductDTO> querySkuMap = skuInfoRpcService.querySkuMap(param);
            if (MapUtils.isNotEmpty(querySkuMap)) {
                validJdSkuId.addAll(querySkuMap.keySet());
            }
        }
        return validJdSkuId;
    }


    /**
     * 实体转换
     */
    private List<ProductCustomsDraftApiDTO> excel2dto(String pin, ProductCustomsValuationExcelDTO dto, List<SkuBaseInfoApiDTO> skuInfos, long time) {
        List<ProductCustomsDraftApiDTO> reuslt = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuInfos)) {
            reuslt.add(createSkuInfo(dto,time,pin,null));
            return reuslt;
        }

        for (SkuBaseInfoApiDTO skuInfo : skuInfos){
            reuslt.add(createSkuInfo(dto,time,pin,skuInfo.getSkuId()));
        }
        return reuslt;
    }


    private ProductCustomsDraftApiDTO createSkuInfo(ProductCustomsValuationExcelDTO dto, long time, String pin,Long skuId){
        ProductCustomsDraftApiDTO targetDto = new ProductCustomsDraftApiDTO();
        targetDto.setJdSkuId(Long.valueOf(dto.getJdSkuId().trim()));
        targetDto.setCountryCode(dto.getCountryCode());
        targetDto.setDataSource(EXCEL_TASK);
        targetDto.setPurchaseSalesErp(dto.getPurchaseSalesErp());
        targetDto.setOperator(pin);
        targetDto.setOperateTime(time);
        targetDto.setRemark(dto.getRemarks());
        targetDto.setOperateCode(CREATE_ACTION);
        if (Objects.nonNull(dto.getExpectedCompletionTime())) {
            targetDto.setExpectCompletionTime(DateUtil.parseTime(dto.getExpectedCompletionTime()));
        }
        targetDto.setSkuId(skuId);
        return targetDto;
    }

}

