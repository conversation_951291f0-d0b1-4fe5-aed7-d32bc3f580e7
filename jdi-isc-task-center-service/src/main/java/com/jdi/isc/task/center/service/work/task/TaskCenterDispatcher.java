package com.jdi.isc.task.center.service.work.task;

import cn.hutool.core.annotation.AnnotationUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.CurrencyLangEnum;
import com.jdi.isc.product.soa.api.message.req.WiopMsgReqDTO;
import com.jdi.isc.product.soa.price.api.salePrice.IscProductSoaSalePriceWriteApiService;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskStatusEnum;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.domain.common.biz.BaseXxlParamDTO;
import com.jdi.isc.task.center.domain.enums.TaskStrategyEnum;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;
import com.jdi.isc.task.center.domain.task.vo.TaskStrategyVO;
import com.jdi.isc.task.center.rpc.openMsg.OpenMsgRpcService;
import com.jdi.isc.task.center.rpc.utils.ExceptionUtil;
import com.jdi.isc.task.center.service.language.LanguageService;
import com.jdi.isc.task.center.service.manage.devOps.DevOpsManageService;
import com.jdi.isc.task.center.service.manage.task.strategy.TaskContextService;
import com.jdi.isc.task.center.service.manage.task.strategy.TaskStrategyService;
import com.jdi.isc.task.center.service.price.SkuPriceMonitorService;
import com.jdi.isc.task.center.service.work.task.frame.BaseJob;
import com.jdi.isc.task.center.service.work.task.frame.JobExecutor;
import com.jdi.isc.task.center.service.work.task.frame.PdfService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.ShardingUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.xxl.job.core.biz.model.ReturnT.FAIL_CODE;

/**
 * 任务中心任务调度派发，wimp相关任务
 * <AUTHOR>
 * @date 20231127
 */
@Component
@Slf4j
public class TaskCenterDispatcher {

    @Resource
    private TaskContextService taskContextService;
    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private LanguageService languageService;

    @Resource
    private SkuPriceMonitorService skuPriceMonitorService;
    @Resource
    private IscProductSoaSalePriceWriteApiService iscProductSoaSalePriceWriteApiService;
    @Resource
    private OpenMsgRpcService openMsgRpcService;
    @Resource
    private DevOpsManageService devOpsManageService;

    @SneakyThrows
    @XxlJob("exportPurchaseOrderWorker")
    public ReturnT<String> exportPurchaseOrderWorker(String param) {
        log.info("TaskCenterDispatcher.exportPurchaseOrderWorker WIMP导出采购单拉取任务,入参type={}", TaskBizTypeEnum.TASK_EXPORT_PURCHASE_ORDER);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.TASK_EXPORT_PURCHASE_ORDER.getCode()));
        log.info("TaskCenterDispatcher.exportPurchaseOrderWorker WIMP导出采购单拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.out(task).start();
                log.info("exportPurchaseOrderWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("exportOrderWorker")
    public ReturnT<String> exportOrderWorker(String param) {
        log.info("TaskCenterDispatcher.exportOrderWorker WIMP导出订单拉取任务,入参type={}", TaskBizTypeEnum.TASK_EXPORT_ORDER);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.TASK_EXPORT_ORDER.getCode()));
        log.info("TaskCenterDispatcher.exportOrderWorker WIMP导出订单拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.out(task).start();
                log.info("exportPurchaseOrderWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("exportStockUpOrderWorker")
    public ReturnT<String> exportStockUpOrderWorker(String param) {
        log.info("TaskCenterDispatcher.exportStockUpOrderWorker WIMP导出备货订单拉取任务,入参type={}", TaskBizTypeEnum.TASK_EXPORT_STOCK_UP_ORDER);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.TASK_EXPORT_STOCK_UP_ORDER.getCode()));
        log.info("TaskCenterDispatcher.exportStockUpOrderWorker WIMP导出备货订单拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.out(task).start();
                log.info("exportStockUpOrderWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("dynamicExportCategoryWorker")
    public ReturnT<String> dynamicExportCategoryWorker(String param) {
        log.info("TaskCenterDispatcher.exportCategoryWorker WIMP 动态导出类目信息拉取任务,入参type={}", TaskBizTypeEnum.TASK_EXPORT_CATEGORY);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.TASK_EXPORT_CATEGORY.getCode()));
        log.info("TaskCenterDispatcher.exportCategoryWorker WIMP 动态导出类目信息拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.out(task).start();
                log.info("exportCategoryWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("importBatchWimpCrossProduct")
    public ReturnT<String> importBatchWimpCrossProduct(String param) {
        log.info("TaskCenterDispatcher.importBatchWimpCrossProduct WIMP批量创建商品任务,入参type={}", TaskBizTypeEnum.CN_CREATE_PRODUCT_BATCH_IMPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.CN_CREATE_PRODUCT_BATCH_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.importBatchWimpCrossProduct WIMP批量创建商品任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importBatchWimpCrossProduct 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("importBatchWimpLocalProduct")
    public ReturnT<String> importBatchWimpLocalProduct(String param) {
        log.info("TaskCenterDispatcher.importBatchWimpLocalProduct WIMP批量创建商品任务,入参type={}", TaskBizTypeEnum.INT_CREATE_PRODUCT_BATCH_IMPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.INT_CREATE_PRODUCT_BATCH_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.importBatchWimpLocalProduct WIMP批量创建商品任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importBatchWimpLocalProduct 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("orderWaybillPdf")
    public ReturnT<String> orderWaybillPdf(String param) {
        log.info("TaskCenterSupplierDispatcher.orderWaybillPdf 生成二段交接单pdf，入参type={}", TaskBizTypeEnum.EXPORT_ORDER_WAYBILL_PDF);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.EXPORT_ORDER_WAYBILL_PDF.getCode()));
        log.info("TaskCenterSupplierDispatcher.orderWaybillPdf 生成二段交接单pdf,出参={}",JSON.toJSONString(task));
        if(task!=null){
            PdfService pdfExecutor = this.getPDFExecutor(TaskBizTypeEnum.EXPORT_ORDER_WAYBILL_PDF.getCode());
            if(null != pdfExecutor) {
                pdfExecutor.of(task.getTaskStrategyVO()).start();
                log.info("orderWaybillPdf 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }


    @SneakyThrows
    @XxlJob("exportWimpFirstOrderWaybillPdf")
    public ReturnT<String> exportWimpFirstOrderWaybillPdf(String param) {
        log.info("TaskCenterSupplierDispatcher.exportWimpFirstOrderWaybillPdf 生成一段交接单pdf，入参type={}", TaskBizTypeEnum.EXPORT_FIRST_WAYBILL_PDF);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.EXPORT_FIRST_WAYBILL_PDF.getCode()));
        log.info("TaskCenterSupplierDispatcher.exportWimpFirstOrderWaybillPdf 生成一段交接单pdf,出参={}",JSON.toJSONString(task));
        if(task!=null){
            PdfService pdfExecutor = this.getPDFExecutor(TaskBizTypeEnum.EXPORT_FIRST_WAYBILL_PDF.getCode());
            if(null != pdfExecutor) {
                pdfExecutor.of(task.getTaskStrategyVO()).start();
                log.info("exportWimpFirstOrderWaybillPdf 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("exportWimpMarkPdf")
    public ReturnT<String> exportWimpMarkPdf(String param) {
        log.info("TaskCenterSupplierDispatcher.exportWimpMarkPdf 生成唛头pdf，入参type={}", TaskBizTypeEnum.EXPORT_MARK_PDF);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.EXPORT_MARK_PDF.getCode()));
        log.info("TaskCenterSupplierDispatcher.exportWimpMarkPdf 生成唛头pdf,出参={}",JSON.toJSONString(task));
        if(task!=null){
            PdfService pdfExecutor = this.getPDFExecutor(TaskBizTypeEnum.EXPORT_MARK_PDF.getCode());
            if(null != pdfExecutor) {
                pdfExecutor.of(task.getTaskStrategyVO()).start();
                log.info("exportWimpMarkPdf 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }


    @SneakyThrows
    @XxlJob("updateGlobalAttribute")
    public ReturnT<String> updateGlobalAttribute(String param) {
        log.info("TaskCenterSupplierDispatcher.updateGlobalAttribute 修改跨境属性，入参type={}", TaskBizTypeEnum.GLOBAL_ATTRIBUTE);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.GLOBAL_ATTRIBUTE.getCode()));
        log.info("TaskCenterSupplierDispatcher.updateGlobalAttribute 修改跨境属性,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("updateGlobalAttribute 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("importUpdateProductWorker")
    public ReturnT<String> importUpdateProductWorker(String param) {
        log.info("TaskCenterDispatcher.importUpdateProductWorker 批量更新商品信息任务,入参type={}",TaskBizTypeEnum.SKU_AMEND_BATCH_UPDATE_INT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.SKU_AMEND_BATCH_UPDATE_INT.getCode(),TaskBizTypeEnum.SKU_AMEND_BATCH_UPDATE_CN.getCode()));
        log.info("TaskCenterDispatcher.importUpdateProductWorker 批量更新商品信息任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importUpdateProductWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("importWimpSkuStock")
    public ReturnT<String> importWimpSkuStock(String param) {
        log.info("TaskCenterDispatcher.importWimpSkuStock WIMP导入批量更新库存任务,入参type={}", TaskBizTypeEnum.SKU_STOCK_BATCH_IMPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.SKU_STOCK_BATCH_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.importWimpSkuStock WIMP导入批量更新库存任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importWimpSkuStock 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 批量更新商品信息任务
     *
     * @param param 任务参数
     * @return 返回任务执行结果
     */
    @SneakyThrows
    @XxlJob("exportOrderOutVerifyWorker")
    public ReturnT<String> exportOrderOutVerifyWorker(String param) {
        log.info("TaskCenterDispatcher.exportOrderOutVerifyWorker 批量导出履约信息缺失信息,入参type={}",TaskBizTypeEnum.ORDER_OUT_VERIFY_BATCH_EXPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.ORDER_OUT_VERIFY_BATCH_EXPORT.getCode()));
        log.info("TaskCenterDispatcher.exportOrderOutVerifyWorker 批量导出履约信息缺失信息,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("updateTaskStatusWorker")
    public ReturnT<String> updateTaskStatusWorker(String param) {
        log.info("TaskCenterDispatcher.updateTaskStatusWorker  更新任务状态,入参taskId={}",param);
        if(StringUtils.isBlank(param)){
            return ReturnT.FAIL;
        }
        Long taskId = Long.valueOf(param);
        TaskStrategyVO task = this.getStrategyService().getTask(taskId);
        task.setStatus(TaskStatusEnum.PENDING);
        boolean update = this.getStrategyService().update(task);
        log.info("TaskCenterDispatcher.updateTaskStatusWorker 更新任务状态,入参taskId={}，结果:{}",JSON.toJSONString(taskId),JSON.toJSONString(update));
        return ReturnT.SUCCESS;
    }


    @SneakyThrows
    @XxlJob("updateTaskStatusWorkerEnd")
    public ReturnT<String> updateTaskStatusWorkerEnd(String param) {
        log.info("TaskCenterDispatcher.updateTaskStatusWorkerEnd  更新任务状态,入参taskId={}",param);
        if(StringUtils.isBlank(param)){
            return ReturnT.FAIL;
        }
        Long taskId = Long.valueOf(param);
        TaskStrategyVO task = this.getStrategyService().getTask(taskId);
        task.setStatus(TaskStatusEnum.SUCCESS);
        boolean update = this.getStrategyService().update(task);
        log.info("TaskCenterDispatcher.updateTaskStatusWorkerEnd 更新任务状态,入参taskId={}，结果:{}",JSON.toJSONString(taskId),JSON.toJSONString(update));
        return ReturnT.SUCCESS;
    }

//    /** 批量商品识别类目品牌*/
//    @SneakyThrows
//    @XxlJob("identifyProductCateAndBrand")
//    public ReturnT<String> identifyProductCateAndBrand(String param) {
//        log.info("TaskCenterDispatcher.identifyProductCateAndBrand 批量商品识别类目品牌,入参type={}",TaskBizTypeEnum.PRODUCT_IDENTIFY);
//        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.PRODUCT_IDENTIFY.getCode()));
//        log.info("TaskCenterDispatcher.identifyProductCateAndBrand 批量商品识别类目品牌,出参={}",JSON.toJSONString(task));
//        if(task!=null){
//            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
//            if(null!=executor){
//                executor.in(task).start();
//                log.info("TaskCenterDispatcher identifyProductCateAndBrand 执行完毕:{} " , JSON.toJSONString(task));
//            }
//        }
//        return ReturnT.SUCCESS;
//    }


    /** 批量导入客户关联类目*/
    @SneakyThrows
    @XxlJob("clientCategoryRelationImportHandler")
    public ReturnT<String> clientCategoryRelationImportHandler(String param) {
        log.info("TaskCenterDispatcher.clientCategoryRelationImportHandler 批量导入客户类目关系,入参type={}",TaskBizTypeEnum.CLIENT_CATEGORY_RELATION);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.CLIENT_CATEGORY_RELATION.getCode()));
        log.info("TaskCenterDispatcher.clientCategoryRelationImportHandler 批量导入客户类目关系,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher clientCategoryRelationImportHandler 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 批量SPU类目迁移*/
    @SneakyThrows
    @XxlJob("transferCategoryImportHandler")
    public ReturnT<String> transferCategoryImportHandler(String param) {
        log.info("TaskCenterDispatcher.transferCategoryImportHandler 批量SPU类目迁移,入参type={}",TaskBizTypeEnum.PRODUCT_TRANSFER_CATEGORY);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.PRODUCT_TRANSFER_CATEGORY.getCode()));
        log.info("TaskCenterDispatcher.transferCategoryImportHandler 批量SPU类目迁移,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher transferCategoryImportHandler 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 订单装托导入*/
    @SneakyThrows
    @XxlJob("orderPalletImportHandler")
    public ReturnT<String> orderPalletImportHandler(String param) {
        log.info("TaskCenterDispatcher.orderPalletImportHandler 订单装托导入,入参type={}",TaskBizTypeEnum.ORDER_PALLET_RECORD_IMPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.ORDER_PALLET_RECORD_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.orderPalletImportHandler 订单装托导入,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher orderPalletImportHandler 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("exportOrderInvoiceWorker")
    public ReturnT<String> exportOrderInvoiceWorker(String param) {
        log.info("TaskCenterDispatcher.exportOrderInvoiceWorker WIMP导出订单发票任务,入参type={}", TaskBizTypeEnum.ORDER_INVOICE_URL_EXPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.ORDER_INVOICE_URL_EXPORT.getCode()));
        log.info("TaskCenterDispatcher.exportOrderInvoiceWorker WIMP导出订单发票任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.out(task).start();
                log.info("exportOrderInvoiceWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 批量新增利润率阈值 */
    @SneakyThrows
    @XxlJob("importWimpProfitRate")
    public ReturnT<String> importWimpProfitRate(String param) {
        log.info("TaskCenterDispatcher.importWimpProfitRate 批量新增利润率阈值,入参type={}",TaskBizTypeEnum.WIMP_PROFIT_RATE);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.WIMP_PROFIT_RATE.getCode()));
        log.info("TaskCenterDispatcher.importWimpProfitRate 批量新增利润率阈值,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher importWimpProfitRate 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 批量新增履约费率 */
    @SneakyThrows
    @XxlJob("importWimpFulfillmentRate")
    public ReturnT<String> importWimpFulfillmentRate(String param) {
        log.info("TaskCenterDispatcher.importWimpFulfillmentRate 批量新增履约费率,入参type={}",TaskBizTypeEnum.WIMP_FULFILLMENT_RATE);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.WIMP_FULFILLMENT_RATE.getCode()));
        log.info("TaskCenterDispatcher.importWimpFulfillmentRate 批量新增履约费率,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher importWimpFulfillmentRate 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 批量新增履约费率 */
    @SneakyThrows
    @XxlJob("importWimpCustomerSkuCalculate")
    public ReturnT<String> importWimpCustomerSkuCalculate(String param) {
        log.info("TaskCenterDispatcher.importWimpCustomerSkuCalculate 批量试算sku客制化价格,入参type={}",TaskBizTypeEnum.WIMP_CUSTOMER_SKU_CALCULATE);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.WIMP_CUSTOMER_SKU_CALCULATE.getCode()));
        log.info("TaskCenterDispatcher.importWimpCustomerSkuCalculate 批量试算sku客制化价格,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher importWimpCustomerSkuCalculate 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 商品备货仓绑定导入*/
    @SneakyThrows
    @XxlJob("skuWarehouseBindImportHandler")
    public ReturnT<String> skuWarehouseBindImportHandler(String param) {
        log.info("TaskCenterDispatcher.skuWarehouseBindImportHandler 商品备货仓绑定导入,入参type={}",TaskBizTypeEnum.SKU_WAREHOUSE_BIND);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.SKU_WAREHOUSE_BIND.getCode()));
        log.info("TaskCenterDispatcher.skuWarehouseBindImportHandler 商品备货仓绑定导入,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher skuWarehouseBindImportHandler 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 类目名称多语言导入
     * @param param
     * @return
     */
    @SneakyThrows
    @XxlJob("categoryLangImportHandler")
    public ReturnT<String> categoryLangImportHandler(String param) {
        log.info("TaskCenterDispatcher.categoryLangImportHandler 类目名称多语言导入，入参type={}",TaskBizTypeEnum.CATEGORY_LANG_BATCH_IMPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.CATEGORY_LANG_BATCH_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.categoryLangImportHandler 类目名称多语言导入，出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher categoryLangImportHandler 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 更新多语言信息
     * @param lang 多语言信息，使用逗号分隔
     * @return 更新结果
     */
    @SneakyThrows
    @XxlJob("updateLangInfoHandler")
    public ReturnT<String> updateLangInfoHandler(String lang) {
        log.info("TaskCenterDispatcher.updateLangInfo 更新多语言信息,入参lang={}",lang);
        if(StringUtils.isNotBlank(lang)) {
            String[] langArr = lang.split(Constant.COMMA);
            for (int i = 0 ;i < langArr.length ; i++){
                languageService.cacheLanguageConfig(langArr[i]);
            }
        }else {
            CurrencyLangEnum[] langEnums = CurrencyLangEnum.values();
            Set<String> langSet = new HashSet<>();
            for (CurrencyLangEnum langEnum : langEnums) {
                String langStr = langEnum.getLang();
                if(!langSet.contains(langStr)){
                    langSet.add(langStr);
                    languageService.cacheLanguageConfig(langStr);
                }
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 批量导入出口税率
     * @param param 参数
     * @return 更新结果
     */
    @SneakyThrows
    @XxlJob("importExportTaxRateHandler")
    public ReturnT<String> importExportTaxRateHandler(String param) {
        log.info("TaskCenterDispatcher.importExportTaxRateHandler 批量导入出口税率,param={}",param);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.WIMP_EXPORT_TAX_RATE_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.importExportTaxRateHandler 入参:{},出参={}",TaskBizTypeEnum.WIMP_EXPORT_TAX_RATE_IMPORT.getName(), JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher importExportTaxRateHandler 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 批量导入巴西客制化价格任务
     * @param param 参数
     * @return 更新结果
     */
    @SneakyThrows
    @XxlJob("importBrCustomerSkuPriceHandler")
    public ReturnT<String> importBrCustomerSkuPriceHandler(String param) {
        log.info("TaskCenterDispatcher.importBrCustomerSkuPriceHandler 批量导入巴西客制化价格任务,param={}",param);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.WIMP_BR_CUSTOMER_PRICE_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.importBrCustomerSkuPriceHandler 入参:{},出参={}",TaskBizTypeEnum.WIMP_BR_CUSTOMER_PRICE_IMPORT.getName(), JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher importBrCustomerSkuPriceHandler 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("exportOrderWaybillWorker")
    public ReturnT<String> exportOrderWaybillWorker(String param) {
        Set<Integer> codeSet = Sets.newHashSet(TaskBizTypeEnum.LOCAL_ORDER_WAY_BILL_EXPORT.getCode(), TaskBizTypeEnum.WAREHOUSE_ORDER_WAY_BILL_EXPORT.getCode(), TaskBizTypeEnum.CROSS_ORDER_WAY_BILL_EXPORT.getCode());
        log.info("TaskCenterDispatcher.exportOrderWaybillWorker WIMP导出运单任务,入参type={}",JSON.toJSONString(codeSet));
        TaskDTO task = this.getStrategyService().pullTask(codeSet);
        log.info("TaskCenterDispatcher.exportOrderWaybillWorker WIMP导出运单任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.out(task).start();
                log.info("exportOrderWaybillWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 采购价格变更监控
     * @param param xxlJob入参
     * @return 响应
     */
    @XxlJob("skuPurchasePriceMonitor")
    public ReturnT<String> skuPurchasePriceChangeMonitor(String param){
        ShardingUtil.ShardingVO shardingVo = ShardingUtil.getShardingVo();
        skuPriceMonitorService.monitorScan();
        return ReturnT.SUCCESS;
    }

    /** 商品特殊导入*/
    @SneakyThrows
    @XxlJob("wimpProductSpecialAttrHandler")
    public ReturnT<String> wimpProductSpecialAttrWorker(String param) {
        log.info("TaskCenterDispatcher.wimpProductSpecialAttrRpc 商品批量打标异常,入参type={}",TaskBizTypeEnum.WIMP_PRODUCT_SPECIAL_ATTR);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.WIMP_PRODUCT_SPECIAL_ATTR.getCode()));
        log.info("TaskCenterDispatcher.identifyProductCateRpc 批量商品识别类目,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher identifyProductCateRpc 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 商品特殊导出*/
    @SneakyThrows
    @XxlJob("wimpProductSpecialAttrExportHandler")
    public ReturnT<String> WimpProductSpecialAttrExportWork(String param) {
        log.info("TaskCenterDispatcher.wimpProductSpecialAttrRpc 商品批量打标异常,入参type={}",TaskBizTypeEnum.WIMP_PRODUCT_SPECIAL_EXPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.WIMP_PRODUCT_SPECIAL_EXPORT.getCode()));
        log.info("TaskCenterDispatcher.identifyProductCateRpc 批量商品识别类目,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher identifyProductCateRpc 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 越南税率导入任务
     * @param param
     * @return
     */
    @SneakyThrows
    @XxlJob("vnHscodeImportHandler")
    public ReturnT<String> vnHscodeImportHandler(String param) {
        Set<Integer> codeSet = Sets.newHashSet(TaskBizTypeEnum.VI_HS_CODE_TAX_RATE_IMPORT.getCode(), TaskBizTypeEnum.VI_HS_CODE_IMPORT_TAX_RATE_IMPORT.getCode(), TaskBizTypeEnum.VI_HS_CODE_RESTRICT_IMPORT.getCode());
        log.info("TaskCenterDispatcher.vnHscodeImportHandler 越南税率导入任务,入参type={}", JSON.toJSONString(codeSet));
        TaskDTO task = this.getStrategyService().pullTask(codeSet);
        log.info("TaskCenterDispatcher.vnHscodeImportHandler 越南税率导入任务,出参={}", JSON.toJSONString(task));
        if (task != null) {
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if (null != executor) {
                executor.in(task).start();
                log.info("vnHscodeImportHandler 执行完毕:{} ", JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }


    /** 批量商品识别类目*/
    @SneakyThrows
    @XxlJob("identifyProductCateRpc")
    public ReturnT<String> identifyProductCateRpc(String param) {
        log.info("TaskCenterDispatcher.identifyProductCateRpc 批量商品识别类目,入参type={}",TaskBizTypeEnum.PRODUCT_IDENTIFY_RPC);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.PRODUCT_IDENTIFY_RPC.getCode()));
        log.info("TaskCenterDispatcher.identifyProductCateRpc 批量商品识别类目,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher identifyProductCateRpc 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 批量更新三方单号及批量确认订单
     * @param param
     * @return
     */
    @SneakyThrows
    @XxlJob("orderUpdateConfirm")
    public ReturnT<String> orderUpdateConfirm(String param) {
        log.info("TaskCenterDispatcher.orderUpdateConfirm 批量更新三方单号及确认订单导入，入参type={}",TaskBizTypeEnum.ORDER_UPDATE_AND_CONFIRM_IMPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.ORDER_UPDATE_AND_CONFIRM_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.orderUpdateConfirm 批量更新三方单号及确认订单导入，出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher orderUpdateConfirm 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 批量商品识别类目*/
    @SneakyThrows
    @XxlJob("importSkuStockThreshold")
    public ReturnT<String> importSkuStockThreshold(String param) {
        log.info("TaskCenterDispatcher.importSkuStockThreshold 批量导入SKU库存阈值,入参type={}",TaskBizTypeEnum.SKU_STOCK_THRESHOLD_IMPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.SKU_STOCK_THRESHOLD_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.importSkuStockThreshold 批量导入SKU库存阈值,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher importSkuStockThreshold 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    //获取执行器
    public BaseJob getExecutor(Integer taskBizType){
        Map<String, Object> beanMap = applicationContext.getBeansWithAnnotation(JobExecutor.class);
        if(beanMap.isEmpty()){
            return null;
        }

        TaskBizTypeEnum taskBizTypeEnum = TaskBizTypeEnum.forCode(taskBizType);
        log.info("TaskDispatcher.getExecutor class list:{}" , beanMap.keySet());
        for (Map.Entry<String,Object> entry : beanMap.entrySet()) {
            try {
                JobExecutor ano = AnnotationUtil.getAnnotation(entry.getValue().getClass(), JobExecutor.class);
                if(taskBizTypeEnum.equals(ano.taskBizType()) && entry.getValue() instanceof BaseJob){
                    log.info("TaskDispatcher.getExecutor 当前任务:{}命中执行策略job:{}" , taskBizTypeEnum, entry.getValue());
                    return (BaseJob) entry.getValue();
                }
            }catch (Exception e){
                log.error("【系统异常】vnHscodeImportHandler.getExecutor ,err:{}", e.getMessage(), e);
            }
        }
        return null;
    }



    //获取执行器
    public PdfService getPDFExecutor(Integer taskBizType){
        Map<String, Object> beanMap = applicationContext.getBeansWithAnnotation(JobExecutor.class);
        if(beanMap.isEmpty()){
            return null;
        }

        TaskBizTypeEnum taskBizTypeEnum = TaskBizTypeEnum.forCode(taskBizType);
        log.info("TaskDispatcher.getExecutor class list:{}" , beanMap.keySet());
        for (Map.Entry<String,Object> entry : beanMap.entrySet()) {
            try {
                JobExecutor ano = AnnotationUtil.getAnnotation(entry.getValue().getClass(), JobExecutor.class);
                if(taskBizTypeEnum.equals(ano.taskBizType()) && entry.getValue() instanceof PdfService){
                    log.info("TaskDispatcher.getExecutor 当前任务:{}命中执行策略job:{}" , taskBizTypeEnum, entry.getValue());
                    return (PdfService) entry.getValue();
                }
            }catch (Exception e){
                log.error("【系统异常】TaskCenterDispatcher.getPDFExecutor,err:{}", e.getMessage(), e);
            }
        }
        return null;
    }


    @SneakyThrows
    @XxlJob("exportCountryMkuHandler")
    public ReturnT<String> exportCountryMkuHandler(String param) {
        log.info("TaskCenterDispatcher.exportCountryMkuHandler 导出国家池数据，入参type={}",TaskBizTypeEnum.COUNTRY_MKU_BATCH_EXPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.COUNTRY_MKU_BATCH_EXPORT.getCode()));
        log.info("TaskCenterDispatcher.exportCountryMkuHandler 导出国家池数据，出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher exportCountryMkuHandler 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    private TaskStrategyService getStrategyService(){
        return taskContextService.getTaskStrategyService(TaskStrategyEnum.WIMP_TASK);
    }


    /** 泰国商品关税批量导入任务*/
    @SneakyThrows
    @XxlJob("thSkuImportTaxBatchTask")
    public ReturnT<String> thSkuImportTaxBatchTask(String param) {
        log.info("TaskCenterDispatcher.thSkuImportTaxBatchTask 泰国商品关税批量导入任务,入参type={}",TaskBizTypeEnum.TH_SKU_IMPORT_TAX_BATCH_ADD);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.TH_SKU_IMPORT_TAX_BATCH_ADD.getCode()));
        log.info("TaskCenterDispatcher.thSkuImportTaxBatchTask 泰国商品关税批量导入任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher thSkuImportTaxBatchTask 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 越南商品关税批量导入任务*/
    @SneakyThrows
    @XxlJob("vnSkuImportTaxBatchTask")
    public ReturnT<String> vnSkuImportTaxBatchTask(String param) {
        log.info("TaskCenterDispatcher.vnSkuImportTaxBatchTask 越南商品关税批量导入任务,入参type={}",TaskBizTypeEnum.VN_SKU_IMPORT_TAX_BATCH_ADD);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.VN_SKU_IMPORT_TAX_BATCH_ADD.getCode()));
        log.info("TaskCenterDispatcher.vnSkuImportTaxBatchTask 越南商品关税批量导入任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher vnSkuImportTaxBatchTask 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 巴西商品关税批量导入任务*/
    @SneakyThrows
    @XxlJob("brSkuImportTaxBatchTask")
    public ReturnT<String> brSkuImportTaxBatchTask(String param) {
        log.info("TaskCenterDispatcher.brSkuImportTaxBatchTask 巴西商品关税批量导入任务,入参type={}",TaskBizTypeEnum.BR_SKU_IMPORT_TAX_BATCH_ADD);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.BR_SKU_IMPORT_TAX_BATCH_ADD.getCode()));
        log.info("TaskCenterDispatcher.brSkuImportTaxBatchTask 巴西商品关税批量导入任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher brSkuImportTaxBatchTask 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 马来商品关税批量导入任务*/
    @SneakyThrows
    @XxlJob("mySkuImportTaxBatchTask")
    public ReturnT<String> mySkuImportTaxBatchTask(String param) {
        log.info("TaskCenterDispatcher.mySkuImportTaxBatchTask 马来商品关税批量导入任务,入参type={}",TaskBizTypeEnum.MY_SKU_IMPORT_TAX_BATCH_ADD);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.MY_SKU_IMPORT_TAX_BATCH_ADD.getCode()));
        log.info("TaskCenterDispatcher.mySkuImportTaxBatchTask 马来商品关税批量导入任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher mySkuImportTaxBatchTask 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 匈牙利商品关税批量导入任务*/
    @SneakyThrows
    @XxlJob("huSkuImportTaxBatchTask")
    public ReturnT<String> huSkuImportTaxBatchTask(String param) {
        log.info("TaskCenterDispatcher.huSkuImportTaxBatchTask 匈牙利商品关税批量导入任务,入参type={}",TaskBizTypeEnum.HU_SKU_IMPORT_TAX_BATCH_ADD);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.HU_SKU_IMPORT_TAX_BATCH_ADD.getCode()));
        log.info("TaskCenterDispatcher.huSkuImportTaxBatchTask 匈牙利商品关税批量导入任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher huSkuImportTaxBatchTask 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 印尼商品关税批量导入任务*/
    @SneakyThrows
    @XxlJob("idSkuImportTaxBatchTask")
    public ReturnT<String> idSkuImportTaxBatchTask(String param) {
        log.info("TaskCenterDispatcher.idSkuImportTaxBatchTask 印尼商品关税批量导入任务,入参type={}",TaskBizTypeEnum.ID_SKU_IMPORT_TAX_BATCH_ADD);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.ID_SKU_IMPORT_TAX_BATCH_ADD.getCode()));
        log.info("TaskCenterDispatcher.idSkuImportTaxBatchTask 印尼商品关税批量导入任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher idSkuImportTaxBatchTask 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("importMarkupRateWorker")
    public ReturnT<String> importMarkupRateWorker(String param) {
        log.info("TaskCenterDispatcher.importMarkupRateWorker 批量国家协议加价率导入任务,入参type={}",TaskBizTypeEnum.COUNTRY_MARKUP_RATE_IMPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.COUNTRY_MARKUP_RATE_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.importMarkupRateWorker 批量国家协议加价率导入任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importMarkupRateWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("importSkuCountryPriceWorker")
    public ReturnT<String> importSkuCountryPriceWorker(String param) {
        log.info("TaskCenterDispatcher.importSkuCountryPriceWorker 批量更新SKU国家协议、京东价格导入任务,入参type={}",TaskBizTypeEnum.UPDATE_SKU_COUNTRY_PRICE_BATCH);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.UPDATE_SKU_COUNTRY_PRICE_BATCH.getCode()));
        log.info("TaskCenterDispatcher.importSkuCountryPriceWorker 批量更新SKU国家协议、京东价格导入任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importSkuCountryPriceWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("importApplyForSkuCountryPriceWorker")
    public ReturnT<String> importApplyForSkuCountryPriceWorker(String param) {
        log.info("TaskCenterDispatcher.importApplyForSkuCountryPriceWorker 批量申请更新SKU国家协议、京东价格导入任务,入参type={}",TaskBizTypeEnum.UPDATE_APPLY_FOR_SKU_COUNTRY_PRICE_BATCH);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.UPDATE_APPLY_FOR_SKU_COUNTRY_PRICE_BATCH.getCode()));
        log.info("TaskCenterDispatcher.importApplyForSkuCountryPriceWorker 批量申请更新SKU国家协议、京东价格导入任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importApplyForSkuCountryPriceWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("importWimpSkuPriceWorker")
    public ReturnT<String> importWimpSkuPriceWorker(String param) {
        log.info("TaskCenterDispatcher.importWimpSkuPriceWorker 申请修改采购价导入任务,入参type={}",TaskBizTypeEnum.UPDATE_SKU_PRICE_IMPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.UPDATE_SKU_PRICE_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.importWimpSkuPriceWorker 申请修改采购价导入任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importMkuSubtitleWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }


    /** 扫描客制化价格生效区间轮动，每2分钟执行一次*/
    @SneakyThrows
    @XxlJob("scanCustomerPriceChange")
    public ReturnT<String> scanCustomerPriceChange(String param) {
        DataResponse<Integer> res = null;
        try {
            res = iscProductSoaSalePriceWriteApiService.triggerExpiredPriceRefresh();
        }finally {
            log.info("TaskCenterDispatcher.scanCustomerPriceChange:{}" ,  JSON.toJSONString(res));
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("exportCustomerSkuBr")
    public ReturnT<String> exportCustomerSkuBr(String param) {
        log.info("TaskCenterDispatcher.exportCustomerSkuBr 导出巴西客制化价格数据，入参type={}",TaskBizTypeEnum.WIMP_CUSTOMER_SKU_BR_EXPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.WIMP_CUSTOMER_SKU_BR_EXPORT.getCode()));
        log.info("TaskCenterDispatcher.exportCustomerSkuBr 导出巴西客制化价格数据，出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher exportCustomerSkuBr 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 扫描WIOP消息延迟未消费并报警*/
    @SneakyThrows
    @XxlJob("scanDelayConsumeOpenMsg")
    public ReturnT<String> scanDelayConsumeOpenMsg(String param) {
        try {
            WiopMsgReqDTO req = new WiopMsgReqDTO();
            req.setSystemCode("TASK-CENTER");
            openMsgRpcService.scanDelayConsumeOpenMsg(req);
        }finally {
            log.info("TaskCenterDispatcher.scanDelayConsumeOpenMsg..");
        }
        return ReturnT.SUCCESS;
    }


    @SneakyThrows
    @XxlJob("thirdArchiveExportWorker")
    public ReturnT<String> thirdArchiveExportWorker(String param) {
        log.info("TaskCenterDispatcher.thirdArchiveExportWorker 导出订单归档数据，入参type={}",TaskBizTypeEnum.THIRD_ARCHIVE_EXPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.THIRD_ARCHIVE_EXPORT.getCode()));
        log.info("TaskCenterDispatcher.exportCustomerSkuBr 导出订单归档数据，出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher exportCustomerSkuBr 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }


    @SuppressWarnings({"rawtypes", "unchecked"})
    @SneakyThrows
    @XxlJob("mkuSubtitleExportWorker")
    public ReturnT<String> mkuSubtitleExportWorker(String param) {
        log.info("TaskCenterDispatcher.mkuSubtitleExportWorker 导出巴西比亚迪短标题，入参type={}",TaskBizTypeEnum.MKU_SUBTITLE_EXPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.MKU_SUBTITLE_EXPORT.getCode()));
        log.info("TaskCenterDispatcher.mkuSubtitleExportWorker 导出巴西比亚迪短标题，出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.out(task).start();
                log.info("TaskCenterDispatcher mkuSubtitleExportWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @SneakyThrows
    @XxlJob("mkuSubtitleNewImportWorker")
    public ReturnT<String> mkuSubtitleNewImportWorker(String param) {
        log.info("TaskCenterDispatcher.mkuSubtitleNewImportWorker 导入巴西比亚迪短标题，入参type={}", TaskBizTypeEnum.MKU_SUBTITLE_IMPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.MKU_SUBTITLE_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.mkuSubtitleNewImportWorker 导入巴西比亚迪短标题，出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher mkuSubtitleNewImportWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("exportCustomerSkuWarning")
    public ReturnT<String> exportCustomerSkuWarning(String param) {
        log.info("TaskCenterDispatcher.exportCustomerSkuWarning 导出sku客制化价格预警数据，入参type={}",TaskBizTypeEnum.CUSTOMER_SKU_WARNING_EXPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.CUSTOMER_SKU_WARNING_EXPORT.getCode()));
        log.info("TaskCenterDispatcher.exportCustomerSkuWarning 导出sku客制化价格预警数据，出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher exportCustomerSkuWarning 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("exportCountryAgreementWarning")
    public ReturnT<String> exportCountryAgreementWarning(String param) {
        log.info("TaskCenterDispatcher.exportCountryAgreementWarning 导出国家协议价价预警数据，入参type={}",TaskBizTypeEnum.COUNTRY_AGREEMENT_WARNING_EXPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.COUNTRY_AGREEMENT_WARNING_EXPORT.getCode()));
        log.info("TaskCenterDispatcher.exportCountryAgreementWarning 导出国家协议价价预警数据，出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher exportCountryAgreementWarning 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }


    @SneakyThrows
    @XxlJob("importProductCustomsWorker")
    public ReturnT<String> importProductCustomsWorker(String param) {
        log.info("TaskCenterDispatcher.importProductCustomsWorker 导入商品报关信息，入参type={}",TaskBizTypeEnum.PRODUCT_CUSTOMS_IMPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.PRODUCT_CUSTOMS_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.importProductCustomsWorker 导入商品报关信息，出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher importProductCustomsWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("exportProductCustomsWorker")
    public ReturnT<String> exportProductCustomsWorker(String param) {
        log.info("TaskCenterDispatcher.exportProductCustomsWorker WIMP导出商品报关任务,入参type={}", TaskBizTypeEnum.PRODUCT_CUSTOMS_PAGE_EXPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.PRODUCT_CUSTOMS_PAGE_EXPORT.getCode()));
        log.info("TaskCenterDispatcher.exportProductCustomsWorker WIMP导出商品报关任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.out(task).start();
                log.info("exportProductCustomsWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("importTaxRefundRateWorker")
    public ReturnT<String> importTaxRefundRateWorker(String param) {
        log.info("TaskCenterDispatcher.importTaxRefundRateWorker 批量历史实际退税成功率导入任务,入参type={}",TaskBizTypeEnum.TAX_REFUND_RATE_IMPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.TAX_REFUND_RATE_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.importTaxRefundRateWorker 批量历史实际退税成功率导入任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importTaxRefundRateWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("dynamicExportCategoryBuyerWorker")
    public ReturnT<String> dynamicExportCategoryBuyerWorker(String param) {
        log.info("TaskCenterDispatcher.dynamicExportCategoryBuyerWorker WIMP 动态导出类目采销信息拉取任务,入参type={}", TaskBizTypeEnum.TASK_EXPORT_CATEGORY_BUYER);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.TASK_EXPORT_CATEGORY_BUYER.getCode()));
        log.info("TaskCenterDispatcher.dynamicExportCategoryBuyerWorker WIMP 动态导出类目采销信息拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.out(task).start();
                log.info("dynamicExportCategoryBuyerWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 类目采销关系导入
     * @param param
     * @return
     */
    @SneakyThrows
    @XxlJob("categoryBuyerImportHandler")
    public ReturnT<String> categoryBuyerImportHandler(String param) {
        log.info("TaskCenterDispatcher.categoryBuyerImportHandler 类目国家采销关系导入，入参type={}",TaskBizTypeEnum.CATEGORY_BUYER_BATCH_IMPORT);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.CATEGORY_BUYER_BATCH_IMPORT.getCode()));
        log.info("TaskCenterDispatcher.categoryBuyerImportHandler 类目国家采销关系导入，出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher categoryBuyerImportHandler 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /** 定期清理日志表*/
    @SneakyThrows
    @XxlJob("clearLog")
    public ReturnT<String> clearLog(String param) {
        try {
            devOpsManageService.clearLog();
        }finally {
            log.info("TaskCenterDispatcher.clearLog..");
        }
        return ReturnT.SUCCESS;
    }

    /** 批量商品识别类目V2*/
    @SneakyThrows
    @XxlJob("identifyProductCateV2")
    public ReturnT<String> identifyProductCateV2(String param) {
        log.info("TaskCenterDispatcher.identifyProductCateV2 批量商品识别类目V2,入参type={}",TaskBizTypeEnum.PRODUCT_IDENTIFY_V2);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.PRODUCT_IDENTIFY_V2.getCode()));
        log.info("TaskCenterDispatcher.identifyProductCateV2 批量商品识别类目V2,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("TaskCenterDispatcher identifyProductCateV2 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("importCategoryTaxID")
    public ReturnT<String> importCategoryTaxID(String param) {
        log.info("TaskCenterDispatcher.importCategoryTaxID WIMP批量创建印尼税率任务,入参type={}", TaskBizTypeEnum.ID_CATEGORY_RATE_BATCH);
        TaskDTO task = this.getStrategyService().pullTask(Sets.newHashSet(TaskBizTypeEnum.ID_CATEGORY_RATE_BATCH.getCode()));
        log.info("TaskCenterDispatcher.importCategoryTaxID WIMP批量创建印尼税率任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTaskStrategyVO().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importBatchWimpCrossProduct 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 执行WIMP类型的任务
     * @param taskBizType 任务业务类型枚举
     */
    public ReturnT<String> executeWIMPTask(TaskBizTypeEnum taskBizType, boolean export, String param) {
        return this.executeTask(TaskStrategyEnum.WIMP_TASK, taskBizType, export, param);
    }

    /**
     * 执行供应商任务
     * @param taskBizType 任务业务类型枚举
     */
    public ReturnT<String> executeSupplierTask(TaskBizTypeEnum taskBizType, boolean export, String param) {
       return this.executeTask(TaskStrategyEnum.SUPPLIER_TASK, taskBizType, export, param);
    }

    /**
     * 执行客户任务
     * @param taskBizType 任务业务类型枚举，指定要执行的客户任务类型
     */
    public ReturnT<String> executeCustomerTask(TaskBizTypeEnum taskBizType, boolean export, String param) {
        return this.executeTask(TaskStrategyEnum.CUSTOMER_TASK, taskBizType, export, param);
    }

    /**
     * 根据任务策略和业务类型执行对应的任务
     * @param strategyEnum 任务策略枚举，用于确定使用哪种任务策略服务
     * @param taskBizType 任务业务类型枚举，用于获取对应业务类型的任务
     */
    @SneakyThrows
    @SuppressWarnings({"rawtypes", "unchecked"})
    public ReturnT<String> executeTask(TaskStrategyEnum strategyEnum, TaskBizTypeEnum taskBizType, boolean export, String param) {
        Instant start = Instant.now();
        String jobName = null;
        TaskDTO task = null;
        int count = 0;
        // 一次最多任务数量
        int maxExecuteCount = this.getExecuteCount(param);
        try {
            BaseJob executor = this.getExecutor(taskBizType.getCode());
            if (executor == null) {
                log.error("找不到任务执行器. 任务结束. taskBizType={}", taskBizType);
                return new ReturnT<>(FAIL_CODE, String.format("找不到任务执行器 %s", taskBizType));
            }
            jobName = executor.getJobName();
            while (count < maxExecuteCount) {
                task = taskContextService.getTaskStrategyService(strategyEnum).getFirstTask(taskBizType.getCode());

                if(task == null) {
                    log.error("未获取到任务，任务结束，[{}], strategy={}, execute cost={}" , jobName, strategyEnum.getServiceName(), Duration.between(start, Instant.now()).getNano());
                    break;
                }

                if (export) {
                    executor.out(task).start();
                } else {
                    executor.in(task).start();
                }
                count ++;
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            String message = ExceptionUtil.getMessage(e, String.format("[%s], 任务执行失败", jobName));
            log.error("任务执行失败，[{}], strategy={}, execute cost={}, count={}, message={}" , jobName, strategyEnum.getServiceName(), Duration.between(start, Instant.now()).getNano(), count, message, e);
            return new ReturnT<>(FAIL_CODE, message);
        } finally {
            log.info("任务执行完毕，[{}], strategy={}, execute cost={} task={}, count={}" , jobName, strategyEnum.getServiceName(), Duration.between(start, Instant.now()).getNano(), JSON.toJSONString(task), count);
        }
    }

    private int getExecuteCount(String param) {
        if (StringUtils.isNotEmpty(param)) {
            try {
                BaseXxlParamDTO parseObject = JSONObject.parseObject(param, BaseXxlParamDTO.class);

                if (parseObject != null && parseObject.getMaxExecuteCount() != null) {
                    return parseObject.getMaxExecuteCount();
                }

            } catch (Exception e) {
                log.error("parseObject error, param:{}, message={}", param, ExceptionUtil.getMessage(e, "parse task param fail"), e);
            }
        }
        return 1;
    }

}
