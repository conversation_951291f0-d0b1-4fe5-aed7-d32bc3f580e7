package com.jdi.isc.task.center.service.protocol.jsf.order;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.task.center.api.dto.order.OrderReq;
import com.jdi.isc.task.center.api.order.OrderTaskApiService;
import com.jdi.isc.task.center.api.order.biz.OrderFieldConfigDTO;
import com.jdi.isc.task.center.domain.task.enums.ExportOrderAggDTOFieldUtils;
import com.jdi.isc.task.center.domain.task.excel.DynamicExportFieldConfig;
import com.jdi.isc.task.center.service.adapter.mapstruct.order.OrderConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 订单服务实现类
 *
 * <AUTHOR>
 * @description
 * @date 2025/8/20
 */
@Service
@Slf4j
public class OrderTaskApiServiceImpl implements OrderTaskApiService {

    @Override
    public DataResponse<List<OrderFieldConfigDTO>> queryOrderShowItem(OrderReq orderReq) {
        Map<String, DynamicExportFieldConfig> allFieldConfigs = ExportOrderAggDTOFieldUtils.getAllFieldConfigs();
        Collection<DynamicExportFieldConfig> values = allFieldConfigs.values();
        List<OrderFieldConfigDTO> result = OrderConvert.INSTANCE.listDynamicExportField(values);
        return DataResponse.success(result);
    }
}