package com.jdi.isc.task.center.service.manage.template.create;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jdi.isc.product.soa.api.common.BaseLangDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.supplier.res.BrandResDTO;
import com.jdi.isc.product.soa.api.supplier.res.CategoryTreeResDTO;
import com.jdi.isc.product.soa.api.wimp.brand.req.BrandReqApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.CategoryReqApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.GlobalAttributeQueryReqDTO;
import com.jdi.isc.product.soa.api.wimp.category.res.GlobalAttributeApiResDTO;
import com.jdi.isc.product.soa.api.wimp.category.res.GlobalAttributeValueApiResDTO;
import com.jdi.isc.task.center.common.costants.AttributeConstant;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.common.costants.ProductConstant;
import com.jdi.isc.task.center.common.ducc.InternationalDuccConfig;
import com.jdi.isc.task.center.domain.excel.*;
import com.jdi.isc.task.center.rpc.brand.BrandRpcService;
import com.jdi.isc.task.center.rpc.category.RpcCategoryService;
import com.jdi.isc.task.center.service.manage.globalattribute.GlobalAttributeManageService;
import com.jdi.isc.task.center.service.manage.template.ExcelTemplateManageService;
import com.jdi.isc.task.center.service.manage.template.support.TemplateSupportService;
import com.jdi.isc.task.center.service.support.excel.*;
import com.jdi.isc.task.center.service.work.task.dynamic.DynamicGenerateImportSubClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/31
 **/
@Slf4j
@Service
public abstract class ParentCreateProductExcelTemplateService extends ExcelTemplateManageService {


    @Resource
    private DynamicGenerateImportSubClass dynamicGenerateImportSubClass;
    @Resource
    private InternationalDuccConfig internationalDuccConfig;
    @Resource
    private TemplateSupportService templateSupportService;
    @Resource
    private GlobalAttributeManageService  globalAttributeManageService;
    @Resource
    private BrandRpcService brandRpcService;
    @Resource
    private RpcCategoryService rpcCategoryService;

    private final ExecutorService pool = new ThreadPoolExecutor(5, 5, 30L, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(1000), new ThreadFactoryBuilder().setNamePrefix("ParentCreateProduct").build(), new ThreadPoolExecutor.CallerRunsPolicy());

    /**隐藏sheet表头字段列表*/
    private List<List<String>> hiddenHeadList = Lists.newArrayList();
    /** 系统编码 */
    protected String systemCode;
    /** 来源国家 */
    protected String sourceCountryCode;

    /**
     * 记录错误的索引位置。
     */
    protected int errorIndex;

    @Override
    protected List<TemplateSourceDataVO.SheetDataVO> getSheetDataVoList(TemplateReqVO reqVO) {
        log.info("ParentCreateProductExcelTemplateService.getSheetDataVoList reqVO={}",JSON.toJSONString(reqVO));
        // -- reqVO 没有操作人 operator
        this.initVal(reqVO);
        ArrayList<TemplateSourceDataVO.SheetDataVO> sheetDataVOS = Lists.newArrayList();
        String operator = reqVO.getOperator();
        String paramJson = reqVO.getParamJson();
        BatchParamVO batchParamVO = JSON.parseObject(paramJson, BatchParamVO.class);
        batchParamVO.setOperator(operator);
        // 数据填写sheet填充内容
        sheetDataVOS.add(this.importSheetData(batchParamVO,0));
        List<Future<TemplateSourceDataVO.SheetDataVO>> futureList = Lists.newArrayList();
        // 供应商类目数据Sheet填充内容
        futureList.add(pool.submit(() -> this.categorySheetData(batchParamVO,1)));
        // 供应商品牌数据sheet填充内容
        futureList.add(pool.submit(() -> this.brandSheetData(batchParamVO, 2)));
        // SPU跨境属性
        futureList.add(pool.submit(() -> this.spuGlobalAttributeSheetData(batchParamVO,3)));
        // SKU跨境属性
        futureList.add(pool.submit(() -> this.skuGlobalAttributeSheetData(batchParamVO, 4)));
        // 取回异步数据
        sheetDataVOS.addAll(this.extendFuture(futureList));
        // 隐藏列表
        this.hiddenSheetData(sheetDataVOS);

        return sheetDataVOS;
    }

    protected TemplateSourceDataVO.SheetDataVO importSheetData(BatchParamVO batchParamVO,int sheetNo) {
        TemplateSourceDataVO.SheetDataVO sheetDataVO = new TemplateSourceDataVO.SheetDataVO();
        sheetDataVO.setSheetNo(sheetNo);
        sheetDataVO.setSheetName(this.getI18nMessageByKey(ProductConstant.VC_SHEET_NAME_FIRST, StringUtils.isNotBlank(batchParamVO.getLang()) ? batchParamVO.getLang() : LangConstant.LANG_EN));
        // 表头
        TemplateHeadSupportVO headSupportVo = templateSupportService.templateHeadSupportVo(batchParamVO);
        this.hiddenHeadList = headSupportVo.getHiddenHeadList();
        if(batchParamVO.getSourceCountryCode().equals(CountryConstant.COUNTRY_ZH)){
            errorIndex += 1;
        }

        List<List<String>> headList = headSupportVo.getHeadList();
        log.info("ParentCreateProductExcelTemplateService.getSheetDataVoList headList={}", JSON.toJSONString(headList));
        sheetDataVO.setHeadList(headList);
        sheetDataVO.setDropdownDataVO(this.fillDropdownDataVO(headSupportVo));
        log.info("ParentCreateProductExcelTemplateService.getSheetDataVoList 表格数据 sheetDataVO={}",JSON.toJSONString(sheetDataVO));
        Map<Integer,Short> headRowHeightMap = Maps.newHashMap();
        headRowHeightMap.put(1,new Short("40"));
        headRowHeightMap.put(2,new Short("120"));
        sheetDataVO.setWriteHandlerList(Lists.newArrayList(new SpecialRowHeightStyleStrategy(new Short("40"), new Short("40"),headRowHeightMap)
                ,new HeadNoMergeWriteHandler(Sets.newHashSet(2),Sets.newHashSet(0))
                 // 跨境模板冻结2列，本土冻结1列
                ,new FreezePaneWriteHandler(CountryConstant.COUNTRY_ZH.equals(batchParamVO.getSourceCountryCode()) ? 2 : 1,3)
                ,new AutoSizeColumnWriterHandler(Sets.newHashSet(0,1))));
        return sheetDataVO;
    }

    /**
     * 添加一个隐藏的工作表到模板数据中。
     * @param sheetDataVOS 包含所有工作表数据的列表。
     */
    protected void hiddenSheetData(ArrayList<TemplateSourceDataVO.SheetDataVO> sheetDataVOS) {
        TemplateSourceDataVO.SheetDataVO hiddenSheetData = new TemplateSourceDataVO.SheetDataVO();
        hiddenSheetData.setSheetNo(sheetDataVOS.size());
        hiddenSheetData.setSheetName(ProductConstant.HIDDEN_SHEET_NAME);
        // 隐藏表格表头
        hiddenSheetData.setHeadList(hiddenHeadList);
        hiddenSheetData.setWriteHandlerList(Lists.newArrayList(new HiddenSheetWriteHandler(ProductConstant.HIDDEN_SHEET_NAME)));
        sheetDataVOS.add(hiddenSheetData);
    }

    /**
     * 填充下拉数据VO对象。
     *
     * @param headSupportVo 模板头支持VO对象，包含表头信息、四级类目、品牌ID、销售单位、销售属性和扩展属性。
     * @return DropdownDataVO对象，包含固定下拉数据、动态下拉数据以及其他必要的信息。
     */
    abstract DropdownDataVO fillDropdownDataVO(TemplateHeadSupportVO headSupportVo);

    /**
     * 根据供应商代码生成类目列表的Excel数据。
     * @param batchParamVO 批量参数对象，包含供应商代码。
     */
    protected TemplateSourceDataVO.SheetDataVO categorySheetData(BatchParamVO batchParamVO,int sheetNo) {
        TemplateSourceDataVO.SheetDataVO sheetDataVO = new TemplateSourceDataVO.SheetDataVO();
        sheetDataVO.setSheetNo(sheetNo);
        sheetDataVO.setSheetName(this.getI18nMessageByKey(ProductConstant.VC_SHEET_NAME_SECOND,StringUtils.isNotBlank(batchParamVO.getLang()) ? batchParamVO.getLang() : LangConstant.LANG_EN));

        sheetDataVO.setClazz(CategoryExcelVO.class);

        List<CategoryExcelVO> categoryExcelVOList = Lists.newArrayList();

        List<CategoryTreeResDTO> categoryTreeResDTOList = this.getCategoryTreeResDTO(batchParamVO.getSupplierCode(),StringUtils.isNotBlank(batchParamVO.getCategoryId())?Long.valueOf(batchParamVO.getCategoryId()):null);

        for (CategoryTreeResDTO categoryTreeResDTO : categoryTreeResDTOList) {
            if (CollectionUtils.isEmpty(categoryTreeResDTO.getChild())) {
                continue;
            }

            Map<String, String> firstLangMap = this.getLangMap(categoryTreeResDTO);
            List<CategoryTreeResDTO> secondCatList = categoryTreeResDTO.getChild();
            for (CategoryTreeResDTO second : secondCatList) {
                if (CollectionUtils.isEmpty(second.getChild())) {
                    continue;
                }

                Map<String, String> secondLangMap = this.getLangMap(second);
                List<CategoryTreeResDTO> thirdCatList = second.getChild();
                for (CategoryTreeResDTO third : thirdCatList) {
                    if (CollectionUtils.isEmpty(third.getChild())) {
                        // 三级为末级类目
                        if (third.getIsLeaf()) {
                            Map<String, String> thirdLangMap = this.getLangMap(third);
                            categoryExcelVOList.add(this.getCategoryExcelVO(third, firstLangMap, secondLangMap, thirdLangMap, Maps.newHashMap()));
                        }
                        continue;
                    }

                    Map<String, String> thirdLangMap = this.getLangMap(third);
                    List<CategoryTreeResDTO> fourthCatList = third.getChild();
                    for (CategoryTreeResDTO treeResDTO : fourthCatList) {
                        Map<String, String> fourthLangMap = this.getLangMap(treeResDTO);
                        categoryExcelVOList.add(this.getCategoryExcelVO(treeResDTO, firstLangMap, secondLangMap, thirdLangMap, fourthLangMap));
                    }
                }
            }
        }

        sheetDataVO.setDatatList(categoryExcelVOList);
        sheetDataVO.setWriteHandlerList(Lists.newArrayList(new SimpleRowHeightStyleStrategy(new Short("30"), new Short("30"))));
        return sheetDataVO;
    }

    /**
     * 获取供应商的类目树形结构数据。
     * @param supplierCode 供应商代码。
     */
    abstract List<CategoryTreeResDTO> getCategoryTreeResDTO(String supplierCode,Long catId);

    protected List<CategoryTreeResDTO> getCategoryTreeRes(String supplierCode,Long catId){
        List<String> langList = Arrays.asList(LangConstant.LANG_ZH,LangConstant.LANG_EN,LangConstant.LANG_TH,LangConstant.LANG_VN);
        CategoryReqApiDTO categoryReqApiDTO = new CategoryReqApiDTO();
        categoryReqApiDTO.setLangSet(new HashSet<>(langList));
        categoryReqApiDTO.setCatId(catId);
        List<CategoryTreeResDTO> categoryTreeResDTOList = rpcCategoryService.queryCategoryTree(categoryReqApiDTO);
        return categoryTreeResDTOList;
    }

    protected List<BrandResDTO> getBrandRes(String supplierCode,Long catId){
        List<String> langList = Arrays.asList(LangConstant.LANG_ZH,LangConstant.LANG_EN,LangConstant.LANG_TH,LangConstant.LANG_VN);
        BrandReqApiDTO brandReqApiDTO = new BrandReqApiDTO();
        brandReqApiDTO.setLangSet(new HashSet<>(langList));
        List<BrandResDTO> brandResDTOList = brandRpcService.queryBrandList(brandReqApiDTO);
        return brandResDTOList;
    }

    /**
     * 将CategoryTreeResDTO对象转换为CategoryExcelVO对象，并根据传入的语言映射填充各级类别的名称。
     * @param treeResDTO 类别树的响应数据转换对象
     * @param firstLangMap 第一级类别的语言映射
     * @param secondLangMap 第二级类别的语言映射
     * @param thirdLangMap 第三级类别的语言映射
     * @param fourthLangMap 第四级类别的语言映射
     * @return 转换后的CategoryExcelVO对象
     */
    private CategoryExcelVO getCategoryExcelVO(CategoryTreeResDTO treeResDTO, Map<String, String> firstLangMap, Map<String, String> secondLangMap, Map<String, String> thirdLangMap, Map<String, String> fourthLangMap) {
        CategoryExcelVO excelVO = new CategoryExcelVO();
        excelVO.setFirstCategoryZhName(firstLangMap.get(LangConstant.LANG_ZH));
        excelVO.setFirstCategoryEnName(firstLangMap.get(LangConstant.LANG_EN));
        excelVO.setFirstCategoryTHName(firstLangMap.get(LangConstant.LANG_TH));
        excelVO.setSecondCategoryZhName(secondLangMap.get(LangConstant.LANG_ZH));
        excelVO.setSecondCategoryEnName(secondLangMap.get(LangConstant.LANG_EN));
        excelVO.setSecondCategoryTHName(secondLangMap.get(LangConstant.LANG_TH));
        excelVO.setThirdCategoryZhName(thirdLangMap.get(LangConstant.LANG_ZH));
        excelVO.setThirdCategoryEnName(thirdLangMap.get(LangConstant.LANG_EN));
        excelVO.setThirdCategoryTHName(thirdLangMap.get(LangConstant.LANG_TH));
        excelVO.setThirdCategoryId(MapUtils.isEmpty(fourthLangMap) ? treeResDTO.getCatId().toString() : "");
        excelVO.setFourthCategoryZhName(fourthLangMap.get(LangConstant.LANG_ZH));
        excelVO.setFourthCategoryEnName(fourthLangMap.get(LangConstant.LANG_EN));
        excelVO.setFourthCategoryTHName(fourthLangMap.get(LangConstant.LANG_TH));
        excelVO.setFourthCategoryId(MapUtils.isNotEmpty(fourthLangMap) ?  treeResDTO.getCatId().toString() :"");
        return excelVO;
    }

    /**
     * 获取指定分类树的语言映射
     * @param categoryTreeResDTO 分类树资源DTO
     * @return 语言代码到语言名称的映射
     */
    protected Map<String, String> getLangMap(CategoryTreeResDTO categoryTreeResDTO) {
        return categoryTreeResDTO.getBaseLangDTOList().stream().collect(Collectors.toMap(BaseLangDTO::getLang, BaseLangDTO::getLangName));
    }

    /**
     * 将供应商的品牌信息添加到模板的数据表中。
     * @param batchParamVO 批量处理参数对象，包含供应商代码和可选的产品线分类ID。
     */
    protected TemplateSourceDataVO.SheetDataVO brandSheetData(BatchParamVO batchParamVO ,int sheetNo) {
        TemplateSourceDataVO.SheetDataVO sheetDataVO = new TemplateSourceDataVO.SheetDataVO();
        sheetDataVO.setSheetNo(sheetNo);
        sheetDataVO.setSheetName(this.getI18nMessageByKey(ProductConstant.VC_SHEET_NAME_THIRD,StringUtils.isNotBlank(batchParamVO.getLang()) ? batchParamVO.getLang() : LangConstant.LANG_EN));
        // 查询产品线品牌
        List<BrandResDTO> brandResDTOList = this.getBrandResDTO(batchParamVO.getSupplierCode(),StringUtils.isNotBlank(batchParamVO.getCategoryId()) ? Long.parseLong(batchParamVO.getCategoryId()):null);
        // 转换品牌
        List<BrandExcelVO> brandExcelVOList = Optional.ofNullable(brandResDTOList).orElseGet(java.util.ArrayList::new).stream().filter(Objects::nonNull).map(this::getBrandExcelVO).collect(Collectors.toList());
        sheetDataVO.setClazz(BrandExcelVO.class);
        sheetDataVO.setDatatList(brandExcelVOList);
        sheetDataVO.setWriteHandlerList(Lists.newArrayList(new SimpleRowHeightStyleStrategy(new Short("30"), new Short("30"))));
        return sheetDataVO;
    }

    /**
     * 根据供应商代码和类别ID获取品牌资源DTO列表
     * @param supplierCode 供应商代码
     * @param catId 类别ID
     * @return 品牌资源DTO列表
     */
    abstract List<BrandResDTO> getBrandResDTO(String supplierCode,Long catId);

    /**
     * 将 BrandResDTO 转换为 BrandExcelVO，用于生成品牌信息 Excel。
     * @param brandResDTO 品牌资源 DTO
     * @return 转换后的 BrandExcelVO 对象
     */
    private BrandExcelVO getBrandExcelVO(BrandResDTO brandResDTO) {
        BrandExcelVO excelVO = new BrandExcelVO();
        excelVO.setBrandId(brandResDTO.getBrandId().toString());
        if (CollectionUtils.isNotEmpty(brandResDTO.getBrandLangDTOList())) {
            Map<String, String> brandLangMap = brandResDTO.getBrandLangDTOList().stream().collect(Collectors.toMap(BaseLangDTO::getLang, BaseLangDTO::getLangName));
            excelVO.setBrandZhName(brandLangMap.getOrDefault(LangConstant.LANG_ZH,""));
            excelVO.setBrandEnName(brandLangMap.getOrDefault(LangConstant.LANG_EN,""));
            excelVO.setBrandThName(brandLangMap.getOrDefault(LangConstant.LANG_TH,""));
        }
        return excelVO;
    }


    protected TemplateSourceDataVO.SheetDataVO spuGlobalAttributeSheetData(BatchParamVO batchParamVO ,int sheetNo) {
        TemplateSourceDataVO.SheetDataVO sheetDataVO = new TemplateSourceDataVO.SheetDataVO();
        sheetDataVO.setSheetNo(sheetNo);
        sheetDataVO.setSheetName(this.getI18nMessageByKey(ProductConstant.SPU_GLOBAL_I18N,StringUtils.isNotBlank(batchParamVO.getLang()) ? batchParamVO.getLang() : LangConstant.LANG_EN));
        // 查询跨境属性

        Map<Long, GlobalAttributeApiResDTO> spuAttributeMap = globalAttributeManageService.queryGlobalAttributeMapByCatId(this.getGlobalAttributeQueryReqDTO(batchParamVO, AttributeConstant.SPU_GLOBAL_DIMENSION));

        List<List<String>> headList = Lists.newArrayList();
        List<List<String>> dataList = Lists.newArrayList();
        this.fillHeadAndDataList(spuAttributeMap, headList, dataList);
        if (CollectionUtils.isEmpty(headList)) {
            return null;
        }
        // 转换跨境属性
        sheetDataVO.setHeadList(headList);
        sheetDataVO.setDatatList(dataList);
        sheetDataVO.setWriteHandlerList(Lists.newArrayList(new SimpleRowHeightStyleStrategy(new Short("30"), new Short("30"))));
        return sheetDataVO;
    }

    private void fillHeadAndDataList(Map<Long, GlobalAttributeApiResDTO> spuAttributeMap, List<List<String>> headList, List<List<String>> dataList) {
        if (MapUtils.isNotEmpty(spuAttributeMap)) {
            int maxColumnNum = 0; // 最大列数，取所有列最大值
            Map<String,List<String>> attributeAndValueMap = Maps.newHashMap();
            for (Map.Entry<Long, GlobalAttributeApiResDTO> entry : spuAttributeMap.entrySet()) {
                GlobalAttributeApiResDTO attributeApiResDTO = entry.getValue();
                if (attributeApiResDTO.getAttributeInputType() != AttributeConstant.MULTIPLE_SELECTION) {
                    continue;
                }

                headList.add(Lists.newArrayList(attributeApiResDTO.getAttributeName()));
                List<String> valueList = CollectionUtils.isEmpty(attributeApiResDTO.getAttributeValueList()) ? Lists.newArrayList() : attributeApiResDTO.getAttributeValueList().stream().map(GlobalAttributeValueApiResDTO::getAttributeValueName).collect(Collectors.toList());
                attributeAndValueMap.put(attributeApiResDTO.getAttributeName(), valueList);
                maxColumnNum = Math.max(maxColumnNum, valueList.size());
            }

            //  跨境属性sheet数据值
            for (int i = 0; i < maxColumnNum; i++) {
                List<String> rowDataList = Lists.newArrayList();
                for (List<String> headColumnList : headList) {
                    String headColumn = headColumnList.get(0);
                    List<String> attributeValueList = attributeAndValueMap.get(headColumn);
                    rowDataList.add(i < attributeValueList.size() ? attributeValueList.get(i) : "");
                }
                dataList.add(rowDataList);
            }
        }
    }

    protected TemplateSourceDataVO.SheetDataVO skuGlobalAttributeSheetData(BatchParamVO batchParamVO ,int sheetNo) {
        TemplateSourceDataVO.SheetDataVO sheetDataVO = new TemplateSourceDataVO.SheetDataVO();
        sheetDataVO.setSheetNo(sheetNo);
        sheetDataVO.setSheetName(this.getI18nMessageByKey(ProductConstant.SKU_GLOBAL_I18N,StringUtils.isNotBlank(batchParamVO.getLang()) ? batchParamVO.getLang() : LangConstant.LANG_EN));
        // 查询跨境属性
        Map<Long, GlobalAttributeApiResDTO> skuGlobalAttributeMap = globalAttributeManageService.queryGlobalAttributeMapByCatId(this.getGlobalAttributeQueryReqDTO(batchParamVO, AttributeConstant.SKU_GLOBAL_DIMENSION));

        List<List<String>> headList = Lists.newArrayList();
        List<List<String>> dataList = Lists.newArrayList();
        this.fillHeadAndDataList(skuGlobalAttributeMap, headList, dataList);
        // 转换跨境属性
        if (CollectionUtils.isEmpty(headList)) {
            return null;
        }
        sheetDataVO.setHeadList(headList);
        sheetDataVO.setDatatList(dataList);
        sheetDataVO.setWriteHandlerList(Lists.newArrayList(new SimpleRowHeightStyleStrategy(new Short("30"), new Short("30"))));
        return sheetDataVO;
    }

    private GlobalAttributeQueryReqDTO getGlobalAttributeQueryReqDTO(BatchParamVO batchParamVO,Integer dimension) {
        Long catId = null;
        if(StringUtils.isNotBlank(batchParamVO.getCategoryId())){
            catId = Long.parseLong(batchParamVO.getCategoryId());
        } else {
            catId = AttributeConstant.ALL_CATEGORY_ATTRIBUTE;
        }

        GlobalAttributeQueryReqDTO queryReqDTO = new GlobalAttributeQueryReqDTO();
        queryReqDTO.setCategoryId(catId);
        queryReqDTO.setSourceCountryCode(batchParamVO.getSourceCountryCode());
        queryReqDTO.setTargetCountryCodes(Arrays.stream(batchParamVO.getAttributeScope().split(Constant.COMMA)).collect(Collectors.toSet()));
        queryReqDTO.setAttributeDimension(dimension);
        queryReqDTO.setExport(null);
        queryReqDTO.setLang(batchParamVO.getLang());
        return queryReqDTO;
    }


    /**
     * 根据给定的关键字和语言获取国际化消息。
     * @param key 消息的关键字。
     * @param lang 消息的语言代码。
     * @return 对应语言的国际化消息值。
     */
    protected String getI18nMessageByKey(String key, String lang) {
        if(CountryConstant.COUNTRY_ZH.equals(sourceCountryCode)){
            Map<String/*词条*/, Map<String/*语种*/, String/*多语言值*/>> internationalItemMap  = internationalDuccConfig.queryModuleKeysForCnCreateProduct(ProductConstant.BATCH_I18N_MODEL);
            String i18Key = ProductConstant.BATCH_I18N_MODEL + Constant.DOT + key;
            return internationalDuccConfig.queryLanValOrDefaultFromMap(internationalItemMap, lang, i18Key);
        }

        Map<String/*词条*/, Map<String/*语种*/, String/*多语言值*/>> internationalItemMap  = internationalDuccConfig.queryModuleKeysForCreateProduct(ProductConstant.BATCH_I18N_MODEL);
        String i18Key = ProductConstant.BATCH_I18N_MODEL + Constant.DOT + key;
        return internationalDuccConfig.queryLanValOrDefaultFromMap(internationalItemMap, lang, i18Key);
    }

    protected void initVal(TemplateReqVO reqVO){
        String paramJson = reqVO.getParamJson();
        BatchParamVO batchParamVO = JSON.parseObject(paramJson, BatchParamVO.class);
        systemCode = batchParamVO.getSystemCode();
        sourceCountryCode = batchParamVO.getSourceCountryCode();
        errorIndex = 0;
    }

    /**
     * 扩展未来的方法，获取所有 Future 对象的结果并返回。
     * @param futureList Future 对象列表
     * @return 包含所有 Future 对象结果的 SheetDataVO 列表
     */
    protected List<TemplateSourceDataVO.SheetDataVO> extendFuture(List<Future<TemplateSourceDataVO.SheetDataVO>> futureList){
        List<TemplateSourceDataVO.SheetDataVO> sheetDataVOList = Lists.newArrayList();
        for (Future<TemplateSourceDataVO.SheetDataVO> future : futureList) {
            try {
                TemplateSourceDataVO.SheetDataVO sheetDataVO = future.get(30, TimeUnit.SECONDS);
                if (Objects.nonNull(sheetDataVO)){
                    sheetDataVOList.add(sheetDataVO);
                }
            } catch (Exception e) {
                log.error("批量发品模版数据 异步取回数据异常",e);
                if (null != future) {
                    future.cancel(true);
                }
            }
        }
        resetSheetNo(sheetDataVOList);
        return sheetDataVOList;
    }

    private void resetSheetNo(List<TemplateSourceDataVO.SheetDataVO> sheetDataVOList) {
        int len = sheetDataVOList.size();
        for (int i = 0; i < len; i++) {
            sheetDataVOList.get(i).setSheetNo(i);
        }
    }
}
