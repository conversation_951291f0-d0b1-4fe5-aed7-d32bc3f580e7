package com.jdi.isc.task.center.service.manage.template;


import com.google.common.collect.Lists;
import com.jdi.isc.task.center.api.common.enums.CustomerTaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.SupplierTaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.domain.excel.TemplateReqVO;
import com.jdi.isc.task.center.domain.excel.TemplateSourceDataVO;
import com.jdi.isc.task.center.domain.task.dto.BaseWorkerDTO;
import com.jdi.isc.task.center.service.work.task.DisposableAbsJob;
import com.jdi.isc.task.center.service.work.task.frame.BaseExecutableAbsJob;
import com.jdi.isc.task.center.service.work.task.frame.JobExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description：DefaultTemplateService
 * @Date 2025-08-27
 */
@Slf4j
@Service
public class DefaultTemplateService extends ExcelTemplateManageService{

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 任务类型与处理程序类的映射
     */
    private final Map<Integer, Class<? extends DisposableAbsJob>> taskTypeHandlerMap = new HashMap<>();
    private final Map<Integer, Class<? extends DisposableAbsJob>> supplierTaskTypeHandlerMap = new HashMap<>();
    private final Map<Integer, Class<? extends DisposableAbsJob>> customerTaskTypeHandlerMap = new HashMap<>();

    @PostConstruct
    public void init() {
        log.info("开始初始化任务类型与处理程序类的映射");
        // 获取所有DisposableAbsJob的子类
        Map<String, DisposableAbsJob> handlerMap = applicationContext.getBeansOfType(DisposableAbsJob.class);
        log.info("找到 {} 个DisposableAbsJob的子类", handlerMap.size());
        
        // 遍历所有处理程序类
        for (Map.Entry<String, DisposableAbsJob> entry : handlerMap.entrySet()) {
            DisposableAbsJob<?> handler = entry.getValue();
            Class<? extends DisposableAbsJob> handlerClass = handler.getClass();
            
            // 检查是否有JobExecutor注解
            if (!handlerClass.isAnnotationPresent(JobExecutor.class)) {
                continue;
            }

            JobExecutor annotation = handlerClass.getAnnotation(JobExecutor.class);
            TaskBizTypeEnum taskBizType = annotation.taskBizType();

            if (taskBizType != TaskBizTypeEnum.NONE) {
                log.info("TaskBizTypeEnum 找到处理程序类: {}, 任务类型: {}", handlerClass.getName(), taskBizType.getName());
                taskTypeHandlerMap.put(taskBizType.getCode(), handlerClass);
            }

            SupplierTaskBizTypeEnum supplierTaskBizType = annotation.supplierTaskBizType();
            if (supplierTaskBizType != SupplierTaskBizTypeEnum.NONE) {
                log.info("SupplierTaskBizTypeEnum 找到处理程序类: {}, 任务类型: {}", handlerClass.getName(), taskBizType.getName());
                supplierTaskTypeHandlerMap.put(supplierTaskBizType.getCode(), handlerClass);
            }

            CustomerTaskBizTypeEnum customerTaskBizType = annotation.customerTaskBizType();
            if (customerTaskBizType != CustomerTaskBizTypeEnum.NONE) {
                log.info("CustomerTaskBizTypeEnum 找到处理程序类: {}, 任务类型: {}", handlerClass.getName(), taskBizType.getName());
                customerTaskTypeHandlerMap.put(customerTaskBizType.getCode(), handlerClass);
            }
        }
        log.info("初始化任务类型与处理程序类的映射完成，TaskBizTypeEnum共 {} 个映射", taskTypeHandlerMap.size());
        log.info("初始化任务类型与处理程序类的映射完成，SupplierTaskBizTypeEnum {} 个映射", supplierTaskTypeHandlerMap.size());
        log.info("初始化任务类型与处理程序类的映射完成，CustomerTaskBizTypeEnum {} 个映射", customerTaskTypeHandlerMap.size());
    }

    @Override
    protected List<TemplateSourceDataVO.SheetDataVO> getSheetDataVoList(TemplateReqVO reqVO) {
        // 设置Excel文件的名称
        this.setExcelName(TaskBizTypeEnum.getNameByType(reqVO.getTaskBizType()));

        // 创建一个包含SheetDataVO的列表
        ArrayList<TemplateSourceDataVO.SheetDataVO> sheetList = Lists.newArrayList();
        TemplateSourceDataVO.SheetDataVO sheetDataVO = new TemplateSourceDataVO.SheetDataVO();
        sheetDataVO.setSheetNo(0);
        sheetDataVO.setSheetName("sheet1");
        
        // 根据任务类型获取对应的实体类
        Class<?> entityClass = getEntityClassByTaskType(reqVO.getTaskBizType());
        log.info("任务类型: {}, 对应的实体类: {}", reqVO.getTaskBizType(), entityClass != null ? entityClass.getName() : "未找到");
        
        // 设置实体类
        sheetDataVO.setClazz(entityClass != null ? entityClass : BaseWorkerDTO.class);
        sheetList.add(sheetDataVO);
        return sheetList;
    }
    
    /**
     * 根据任务类型获取对应的实体类
     * @param taskType 任务类型
     * @return 实体类
     */
    private Class<?> getEntityClassByTaskType(Integer taskType) {

        Class<? extends DisposableAbsJob> handlerClass = null;

        if (TaskBizTypeEnum.forCode(taskType) != null){
            handlerClass = taskTypeHandlerMap.get(taskType);
        }else if (SupplierTaskBizTypeEnum.forCode(taskType) != null){
            handlerClass = supplierTaskTypeHandlerMap.get(taskType);
        }else if (CustomerTaskBizTypeEnum.forCode(taskType) != null){
            handlerClass = customerTaskTypeHandlerMap.get(taskType);
        }

        if (handlerClass == null) {
            log.warn("未找到任务类型 {} 对应的处理程序类", taskType);
            return null;
        }
        
        try {
            // 创建处理程序类的实例
            DisposableAbsJob<?> handler = applicationContext.getBean(handlerClass);
            
            // 获取泛型参数的实际类型
            return handler.getTargetClass();
        } catch (Exception e) {
            log.error("获取任务类型 {} 对应的实体类失败", taskType, e);
        }
        
        return null;
    }
}
