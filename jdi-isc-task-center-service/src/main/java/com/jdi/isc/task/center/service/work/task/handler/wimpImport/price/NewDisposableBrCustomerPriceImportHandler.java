package com.jdi.isc.task.center.service.work.task.handler.wimpImport.price;

import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.domain.task.excel.BrCustomerSkuPriceExcelDTO;
import com.jdi.isc.task.center.service.work.task.frame.JobExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@JobExecutor(taskBizType = TaskBizTypeEnum.WIMP_BR_CUSTOMER_PRICE_IMPORT)
public class NewDisposableBrCustomerPriceImportHandler extends DisposableBrCustomerPriceImportHandler<BrCustomerSkuPriceExcelDTO>{

    @Override
    public Class<BrCustomerSkuPriceExcelDTO> getTargetClass() {
        return BrCustomerSkuPriceExcelDTO.class;
    }

    @Override
    public String getJobName() {
        return TaskBizTypeEnum.WIMP_BR_CUSTOMER_PRICE_IMPORT.getName();
    }
}
