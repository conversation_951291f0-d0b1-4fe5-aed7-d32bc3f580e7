package com.jdi.isc.task.center.service.work.task.sku;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.jdi.yz.core.utils.StringUtils;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.product.soa.api.common.enums.EsAttributeChangeTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.PromiseTimeTagEnum;
import com.jdi.isc.task.center.common.costants.CacheKeyConstant;
import com.jdi.isc.task.center.common.costants.StockConstants;
import com.jdi.isc.task.center.common.costants.UmpKeyConstant;
import com.jdi.isc.task.center.common.ducc.TaskDuccConfigService;
import com.jdi.isc.task.center.common.utils.DateUtil;
import com.jdi.isc.task.center.common.utils.JimUtils;
import com.jdi.isc.task.center.domain.mku.CustomerMkuPO;
import com.jdi.isc.task.center.domain.promise.SkuSnapshotLabelPO;
import com.jdi.isc.task.center.domain.promise.biz.PageDataPullConfig;
import com.jdi.isc.task.center.domain.promise.biz.SkuLabelQueryVO;
import com.jdi.isc.task.center.domain.sku.biz.SkuAttributeLabelChangeMsg;
import com.jdi.isc.task.center.rpc.specialAttr.SpecialAttrApiRpcService;
import com.jdi.isc.task.center.service.atomic.mku.CustomerMkuAtomicService;
import com.jdi.isc.task.center.service.atomic.promise.SkuSnapshotLabelAtomicService;
import com.jdi.isc.task.center.service.manage.msg.TaskCenterMsgSender;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.jdi.isc.task.center.common.costants.Constant.*;

/**
 * @author：xubing82
 * @date：2025/6/27 10:23
 * @description：sku标签打标处理流程 适用场景：sku的各类标签通过大数据快照数据获取标签打标数据到ES上，数据量大小小于100w以内，如果更大则需考虑更换其他方案。
 */
@Slf4j
@Component
public class SkuSnapShotLabelCalculateJob {

    @Autowired
    private SkuSnapshotLabelAtomicService skuSnapshotLabelAtomicService;

    @Value("${topic.jmq4.producer.mkuEsChange}")
    private String mkuEsChangeTopic;

    @Resource
    private TaskCenterMsgSender taskCenterMsgSender;

    @Resource
    private JimUtils jimUtils;

    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;

    @Resource
    private TaskDuccConfigService taskDuccConfigService;

    @Resource
    private SpecialAttrApiRpcService specialAttrApiRpcService;


    /**
     * 用于标识大数据同步完成的SKU前缀常量
     */
    private static final String FINISH_SKU_PREFIX = "9999";

    private static final String JOB_CONFIG_TYPE = "skuLabel";

    @SneakyThrows
    @PFTracing
    @XxlJob("skuSnapShotLabelCalculateJob")
    public ReturnT<String> skuSnapShotLabelCalculateJob(String param) {
        String todayStr = DateUtil.getDateStrByPattern(DateUtil.YY_MM_DD_DAY_PATTERN);

        //大数据同步结束标志校验
        String syncFinishedFlag = FINISH_SKU_PREFIX + DateUtil.getDateStrByPattern(DateUtil.YY_MM_DD_DAY_PATTERN);
        SkuSnapshotLabelPO dataSyncPO = skuSnapshotLabelAtomicService.getSkuLabelDataBySkuId(Long.valueOf(syncFinishedFlag));
        if (dataSyncPO == null) {
            log.warn("skuSnapShotLabelCalculateJob查询快照数据同步完成标记为空, 请检查大数据同步流程是否正常！syncFinishedFlag:{}", syncFinishedFlag);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_FRAME_WARNING, (LevelCode.P0.getMessage() + "sku标签快照底表数据为空，无法进行打标任务，请联系管理员进行查看！"));
            return ReturnT.FAIL;
        }

        try {
            SkuLabelQueryVO skuLabelQueryVO = new SkuLabelQueryVO();
            long count = skuSnapshotLabelAtomicService.queryAllSkuSnapshotLabelPOCount(skuLabelQueryVO);
            long size = PAGE_BATCH_SIZE;
            long page = count / size + (count % size == 0 ? 0 : 1);

            for (long index = 1; index <= page; index++) {
                // 1、分页获取数据标签表数据
                List<SkuSnapshotLabelPO> skuPromiseSnapshotPOList = getSkuSnapshotLabelPOS(index, size);
                if (CollectionUtils.isEmpty(skuPromiseSnapshotPOList)) {
                    log.warn("skuSnapShotLabelCalculateJob getSkuSnapshotLabelPOS empty!");
                    continue;
                }

                //2、更新缓存标签数据
                List<String> simpleSkuLabelData = skuPromiseSnapshotPOList.stream()
                        .map(item -> item.getSkuId() + "-" + item.getCountryCode()).collect(Collectors.toList());
                log.info("skuSnapShotLabelCalculateJob getSkuSnapshotLabelPOS:{}", JSON.toJSONString(simpleSkuLabelData));
                updateCacheLabelData(skuPromiseSnapshotPOList, todayStr);

                //3、获取变更数据
                List<SkuSnapshotLabelPO> changedSkuLabelsList = filterChangedSkuLabelRecord(skuPromiseSnapshotPOList, syncFinishedFlag);
                log.info("skuSnapShotLabelCalculateJob filterChangedSkuLabelRecord:{}", JSON.toJSONString(changedSkuLabelsList));
                if (CollectionUtils.isEmpty(changedSkuLabelsList)) {
                    log.info("skuSnapShotLabelCalculateJob 数据无变更或者非目标数据，不更新。index:{}", index);
                    continue;
                }

                //4、写入商品属性标数据
                addOrRemoveSkuAtrrFlagValue(changedSkuLabelsList);

                //5、转换成by客维度数据
                List<CustomerMkuPO> customerMkuPOListForChange = getCustomerMkuList(changedSkuLabelsList);
                if (CollectionUtils.isEmpty(customerMkuPOListForChange)) {
                    log.info("skuSnapShotLabelCalculateJob getCustomerMkuList empty。changedSkuLabelsList:{}", JSON.toJSONString(changedSkuLabelsList));
                    continue;
                }

                //6、组装MQ发送消息
                List<SkuAttributeLabelChangeMsg> skuAttributeLabelChangeMsgList = buildMkuLabelChangeMessages(customerMkuPOListForChange, changedSkuLabelsList);
                if (CollectionUtils.isNotEmpty(skuAttributeLabelChangeMsgList)) {
                    log.info("skuSnapShotLabelCalculateJob sendMessagesInBatches start sendMQ, stockChangeMsgList:{}", JSON.toJSONString(skuAttributeLabelChangeMsgList));
                    sendMessagesInBatches(skuAttributeLabelChangeMsgList);
                }

                //7、流程控制处理
                PageDataPullConfig dataPullConfig = taskDuccConfigService.getPageDataPullConfig(JOB_CONFIG_TYPE);
                if (dataPullConfig != null) {
                    Boolean stopSwitch = dataPullConfig.getStopSwitch();
                    if (stopSwitch != null && stopSwitch) {
                        log.info("skuSnapShotLabelCalculateJob 管理员已终止此次数据处理流程，stopSwitch:{}", stopSwitch);
                        break;
                    }

                    Long intervalTime = dataPullConfig.getPageIntervalTime();
                    if (intervalTime != null && intervalTime > 0) {
                        Thread.sleep(intervalTime);
                    }
                }
            }

        } catch (Exception e) {
            log.error("skuSnapShotLabelCalculateJob failed, date:{}", todayStr, e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 批量添加或移除SKU属性标志值
     *
     * @param changedSkuLabelsList 需要变更的SKU标签列表，包含SKU ID和承诺值信息
     * @throws InterruptedException 当线程休眠被中断时抛出
     */
    private void addOrRemoveSkuAtrrFlagValue(List<SkuSnapshotLabelPO> changedSkuLabelsList) throws InterruptedException {
        PageDataPullConfig dataPullConfig = taskDuccConfigService.getPageDataPullConfig(JOB_CONFIG_TYPE);
        for (SkuSnapshotLabelPO snapshotLabelPO : changedSkuLabelsList) {
            specialAttrApiRpcService.addOrRemoveSkuPromiseAttr(snapshotLabelPO.getSkuId(), snapshotLabelPO.getPromiseValue());
            if (dataPullConfig != null) {
                Long changeSkuAttrIntervalTime = dataPullConfig.getChangeSkuAttrIntervalTime();
                if (changeSkuAttrIntervalTime != null && changeSkuAttrIntervalTime > 0) {
                    Thread.sleep(changeSkuAttrIntervalTime);
                }
            }
        }
    }

    /**
     * 更新缓存中的商品标签数据
     * 目标：只有标签变更的数据才会发往ES进行更新， 可以将sku相关标签数据，昨日和今日的数据进行比对
     *
     * @param skuPromiseSnapshotPOList 商品标签快照列表，包含需要更新的商品标签信息
     * @param todayStr                 当前日期字符串，用于生成缓存键
     */
    private void updateCacheLabelData(List<SkuSnapshotLabelPO> skuPromiseSnapshotPOList, String todayStr) {
        Map<String, Map<String, String>> redisDataMap = new HashMap<>();
        for (SkuSnapshotLabelPO skuSnapshotLabel : skuPromiseSnapshotPOList) {

            String redisKey = CacheKeyConstant.getSkuLabelRedisKey(skuSnapshotLabel.getSkuId(), skuSnapshotLabel.getCountryCode(), todayStr);

            // 获取承诺值
            Integer promiseValue = skuSnapshotLabel.getPromiseValue();
            if (promiseValue == null) {
                skuSnapshotLabel.setPromiseValue(EMPTY_PROMISE_VALUE);
            }

            // 构建第二层 Map，放置sku的各类需打标的标签数据，例如履约标签、优先标签等等
            Map<String, String> promiseMap = new HashMap<>();
            promiseMap.put("promise_value", String.valueOf(promiseValue));


            // 将第二层 Map 放入第一层 Map
            redisDataMap.put(redisKey, promiseMap);
        }

        //如果redis设置失败，es更新会再次校验是否更新， 不阻塞整体流程
        Boolean result = jimUtils.pipeHmSet(redisDataMap, CacheKeyConstant.TIME_OUT_OF_24_HOURS);
        log.warn("skuSnapShotLabelCalculateJob updateCacheLabelData result:{}, date:{}, request:{}", result, todayStr, JSON.toJSONString(skuPromiseSnapshotPOList));
    }


    /**
     * 批量发送SKU属性标签变更消息
     *
     * @param skuAttributeLabelChangeMsgList 需要发送的SKU属性标签变更消息列表
     */
    public void sendMessagesInBatches(List<SkuAttributeLabelChangeMsg> skuAttributeLabelChangeMsgList) {
        int totalSize = skuAttributeLabelChangeMsgList.size();
        for (int i = 0; i < totalSize; i += StockConstants.MQ_BATCH_SIZE) {
            // 获取当前批次的子列表
            List<SkuAttributeLabelChangeMsg> batchList = skuAttributeLabelChangeMsgList.subList(i, Math.min(i + StockConstants.MQ_BATCH_SIZE, totalSize));

            // 将子列表转换为Map，键为String类型的mkuId
            Map<String, Object> stockChangeMsgMap = batchList.stream()
                    .collect(Collectors.toMap(
                            msg -> String.valueOf(msg.getMkuId()), // 将Long类型的mkuId转换为String
                            Function.identity()
                    ));

            // 发送当前批次消息
            taskCenterMsgSender.sendMessageBatchRetry(stockChangeMsgMap, mkuEsChangeTopic);
        }
    }

    /**
     * 构建SKU属性标签变更消息列表
     *
     * @param customerMkuPOListForChange 客户MKU变更列表，用于构建变更消息
     * @param changedSkuLabelsList       已变更的SKU标签列表，包含需要处理的标签变更数据
     * @return 返回构建完成的SKU属性标签变更消息列表
     */
    private List<SkuAttributeLabelChangeMsg> buildMkuLabelChangeMessages(List<CustomerMkuPO> customerMkuPOListForChange, List<SkuSnapshotLabelPO> changedSkuLabelsList) {
        Map<String, List<CustomerMkuPO>> customerMkuPOChangeMap = customerMkuPOListForChange.stream()
                .collect(Collectors.groupingBy(item -> SkuSnapShotLabelCalculateJob.buildMkuKey(item.getMkuId(), item.getTargetCountryCode())));

        List<SkuAttributeLabelChangeMsg> skuAttributeLabelChangeMsgList = new ArrayList<>();
        for (SkuSnapshotLabelPO skuSnapshotLabelPO : changedSkuLabelsList) {
            String mkuCountryKey = buildMkuKey(skuSnapshotLabelPO.getMkuId(), skuSnapshotLabelPO.getCountryCode());
            List<CustomerMkuPO> customerMkuPOList = customerMkuPOChangeMap.get(mkuCountryKey);
            if (CollectionUtils.isEmpty(customerMkuPOList)) {
                log.warn("buildMkuLabelChangeMessages mku未入池，data:{}", JSON.toJSONString(skuSnapshotLabelPO));
                continue;
            }

            for (CustomerMkuPO customerMkuPO : customerMkuPOList) {
                SkuAttributeLabelChangeMsg skuAttributeLabelChange = new SkuAttributeLabelChangeMsg();
                skuAttributeLabelChange.setClientCode(customerMkuPO.getClientCode());
                skuAttributeLabelChange.setMkuId(customerMkuPO.getMkuId());
                skuAttributeLabelChange.setCountryCode(customerMkuPO.getTargetCountryCode());
                skuAttributeLabelChange.setChangeType(EsAttributeChangeTypeEnum.STOCK_PROMISE_TAG_CHANGE.getCode());
                skuAttributeLabelChange.setSkuId(skuSnapshotLabelPO.getSkuId());
                skuAttributeLabelChange.setPromiseValue(skuSnapshotLabelPO.getPromiseValue());

                skuAttributeLabelChangeMsgList.add(skuAttributeLabelChange);
            }
        }

        return skuAttributeLabelChangeMsgList;
    }

    /**
     * 根据变更的SKU标签列表按国家分组查询对应的MKU客户信息列表
     *
     * @param changedSkuLabelsList 发生变更的SKU标签列表
     * @return 查询到的MKU客户信息列表
     */
    private List<CustomerMkuPO> getCustomerMkuList(List<SkuSnapshotLabelPO> changedSkuLabelsList) {

        Map<String, List<SkuSnapshotLabelPO>> changedSkuLabelsMap = changedSkuLabelsList.stream()
                .collect(Collectors.groupingBy(SkuSnapshotLabelPO::getCountryCode));

        //by国家分批次获取mku客户信息
        List<CustomerMkuPO> customerMkuPOListForChangeList = Lists.newArrayList();
        changedSkuLabelsMap.forEach((countryCode, skuSnapshotLabels) -> {
            List<Long> mkuIds = skuSnapshotLabels.stream()
                    .map(SkuSnapshotLabelPO::getMkuId)
                    .collect(Collectors.toList());

            // 根据 mkuIds 和 countryCode 查询 CustomerMkuPO 列表
            List<CustomerMkuPO> customerMkuPOList = customerMkuAtomicService.listCustomerMkuByMkuIdsCountry(mkuIds, countryCode);
            customerMkuPOListForChangeList.addAll(customerMkuPOList);
        });

        return customerMkuPOListForChangeList;
    }

    /**
     * 过滤出发生变化的SKU标签记录
     *
     * @param skuPromiseSnapshotPOList 待过滤的SKU标签快照列表
     * @return 发生变化需要更新的SKU标签列表
     */
    private List<SkuSnapshotLabelPO> filterChangedSkuLabelRecord(List<SkuSnapshotLabelPO> skuPromiseSnapshotPOList, String syncFinishedFlag) {
        List<SkuSnapshotLabelPO> changedSkuLabelsList = Lists.newArrayList();
        for (SkuSnapshotLabelPO skuSnapshotLabel : skuPromiseSnapshotPOList) {
            Integer todayPromiseValue = skuSnapshotLabel.getPromiseValue();

            //过滤掉标识数据
            if (Objects.equals(skuSnapshotLabel.getSkuId(), Long.valueOf(syncFinishedFlag))) {
                continue;
            }

            //时效标本次只处理7日达时效
            if (todayPromiseValue != null &&
                    !Objects.equals(PromiseTimeTagEnum.forCode(todayPromiseValue), PromiseTimeTagEnum.DEFAULT_EMPTY_TAG) &&
                    !Objects.equals(PromiseTimeTagEnum.forCode(todayPromiseValue), PromiseTimeTagEnum.DELIVERY_7_DAYS_TAG)) {
                //非4的数据不处理、如果是null则放过
                log.warn("filterChangedSkuLabelRecord 非七日达数据，跳过处理！ todayPromiseValue:{}", todayPromiseValue);
                continue;
            }

            String yesterdayDayStr = DateUtil.getYesterdayDateStr();
            String redisKey = CacheKeyConstant.getSkuLabelRedisKey(skuSnapshotLabel.getSkuId(), skuSnapshotLabel.getCountryCode(), yesterdayDayStr);
            String yesterdayPromiseValue = jimUtils.hGet(redisKey, "promise_value");

            //设置标签默认值
            if (todayPromiseValue == null) {
                skuSnapshotLabel.setPromiseValue(EMPTY_PROMISE_VALUE);
            }

            //redis没有数据，或者和当日的值不一致则判定为需更新数据
            if (StringUtils.isBlank(yesterdayPromiseValue) || !Objects.equals(todayPromiseValue, Integer.parseInt(yesterdayPromiseValue))) {
                changedSkuLabelsList.add(skuSnapshotLabel);
            }
        }

        return changedSkuLabelsList;
    }

    /**
     * 根据商家ID和国家代码构建商家唯一标识键
     *
     * @param mkuId       商家ID，不可为空
     * @param countryCode 国家代码，用于区分不同国家/地区的商家
     * @return 由商家ID和国家代码组成的唯一标识字符串，格式为"mkuId_countryCode"
     */
    @NotNull
    private static String buildMkuKey(Long mkuId, String countryCode) {
        return mkuId.toString() + "_" + countryCode;
    }

    /**
     * 根据分页参数查询SKU快照标签列表
     *
     * @param index 分页起始索引
     * @param size  每页记录数
     * @return 包含SKU快照标签信息的列表
     */
    private List<SkuSnapshotLabelPO> getSkuSnapshotLabelPOS(long index, long size) {
        SkuLabelQueryVO skuPromiseQueryPage = new SkuLabelQueryVO();
        skuPromiseQueryPage.setIndex(index);
        skuPromiseQueryPage.setSize(size);
        Page<SkuSnapshotLabelPO> skuPromiseSnapshotPOPage = skuSnapshotLabelAtomicService.queryAllSkuSnapshotLabelPOPage(skuPromiseQueryPage);
        List<SkuSnapshotLabelPO> skuPromiseSnapshotPOList = skuPromiseSnapshotPOPage.getRecords();
        return skuPromiseSnapshotPOList;
    }


    private List<SkuSnapshotLabelPO> getMockSkuSnapshotLabelPOS(int type) {
        SkuSnapshotLabelPO skuSnapshotLabelPO = new SkuSnapshotLabelPO();

        if (type == 1) {
            //caochuanv2的正常品，有7日达标记的，可正常走流程；
            skuSnapshotLabelPO.setSkuId(80000164391L);
            skuSnapshotLabelPO.setMkuId(50000247976L);
            skuSnapshotLabelPO.setCountryCode("TH");
            skuSnapshotLabelPO.setSourceCountryCode("TH");
            skuSnapshotLabelPO.setWarehouseId("0");
            skuSnapshotLabelPO.setSkuType(2);
            skuSnapshotLabelPO.setPromiseValue(PromiseTimeTagEnum.DELIVERY_7_DAYS_TAG.getCode());
            skuSnapshotLabelPO.setYn(1);

        } else if (type == 2) {
            //caochuanv2的正常品，标签清空的，可正常走流程；
            skuSnapshotLabelPO.setSkuId(80000164391L);
            skuSnapshotLabelPO.setMkuId(50000247976L);
            skuSnapshotLabelPO.setCountryCode("TH");
            skuSnapshotLabelPO.setSourceCountryCode("TH");
            skuSnapshotLabelPO.setWarehouseId("0");
            skuSnapshotLabelPO.setSkuType(2);
            skuSnapshotLabelPO.setPromiseValue(null);
            skuSnapshotLabelPO.setYn(1);
        } else {
            //非7日达标记数据
            skuSnapshotLabelPO.setSkuId(80000120692L);
            skuSnapshotLabelPO.setMkuId(50000204500L);
            skuSnapshotLabelPO.setCountryCode("BR");
            skuSnapshotLabelPO.setSourceCountryCode("BR");
            skuSnapshotLabelPO.setWarehouseId("0");
            skuSnapshotLabelPO.setSkuType(2);
            skuSnapshotLabelPO.setPromiseValue(3);
            skuSnapshotLabelPO.setYn(1);
        }

        return Stream.of(skuSnapshotLabelPO).collect(Collectors.toList());
    }

}
