package com.jdi.isc.task.center.service.atomic.sku;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.task.center.domain.sku.po.ChangeRecordPO;
import com.jdi.isc.task.center.repository.jed.mapper.sku.ChangeRecordBaseMapper;
import org.springframework.stereotype.Service;


/**
 * 变更记录日志原子服务
 * <AUTHOR>
 * @date 2025/5/19
 */
@Service
public class ChangeRecordAtomicService extends ServiceImpl<ChangeRecordBaseMapper, ChangeRecordPO> {

}




