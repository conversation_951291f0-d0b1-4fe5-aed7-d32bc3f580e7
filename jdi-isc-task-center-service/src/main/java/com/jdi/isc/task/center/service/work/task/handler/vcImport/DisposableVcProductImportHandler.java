package com.jdi.isc.task.center.service.work.task.handler.vcImport;

import com.alibaba.fastjson.JSONObject;
import com.jdi.isc.product.soa.api.sku.req.SaveSkuApiDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import com.jdi.isc.product.soa.api.spu.res.SpuApiDTO;
import com.jdi.isc.task.center.api.common.enums.SupplierTaskBizTypeEnum;
import com.jdi.isc.task.center.common.utils.ExcelUtils;
import com.jdi.isc.task.center.domain.enums.TaskStrategyEnum;
import com.jdi.isc.task.center.domain.excel.BatchParamVO;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;
import com.jdi.isc.task.center.domain.task.excel.ImportProductDTO;
import com.jdi.isc.task.center.service.manage.task.strategy.TaskContextService;
import com.jdi.isc.task.center.service.manage.task.strategy.TaskStrategyService;
import com.jdi.isc.task.center.service.work.task.frame.JobExecutor;
import com.jdi.isc.task.center.service.work.task.handler.wimpImport.DisposableProductImportHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;
import java.util.Map;

import static com.jdi.isc.task.center.common.costants.ProductConstant.FIX_BRAND_ID;
import static com.jdi.isc.task.center.common.costants.ProductConstant.FIX_CATEGORY_ID;

/**
 * <AUTHOR>
 * @date 2024/9/3
 **/
@Slf4j
@Service
@JobExecutor(supplierTaskBizType = SupplierTaskBizTypeEnum.SUPPLIER_CREATE_PRODUCT_BATCH_IMPORT)
public class DisposableVcProductImportHandler extends DisposableProductImportHandler {
    @Resource
    public TaskContextService taskContextService;

    @Override
    public void parse(TaskDTO<ImportProductDTO> task) {
        super.parse(task);
    }


    @Override
    public void run(TaskDTO<ImportProductDTO> task) {
        super.run(task);
    }


    @Override
    public void export(TaskDTO<ImportProductDTO> task) {
        super.export(task);
    }

    @Override
    protected TaskStrategyService getTaskService() {
        return taskContextService.getTaskStrategyService(TaskStrategyEnum.SUPPLIER_TASK);
    }

    public SpuApiDTO handleSpuApiDTO(Map<String, File> imageFileMap, List<String> langList, JSONObject data, BatchParamVO batchParamVO) {
        SpuApiDTO spuApiDTO = super.handleSpuApiDTO(imageFileMap, langList, data, batchParamVO);

        Long brandId = getBrandId(data);
        spuApiDTO.setBrandId(brandId);

        Long categoryId = getCategoryId(data);
        spuApiDTO.setCatId(categoryId);
        return spuApiDTO;
    }


    public SaveSkuApiDTO getSaveSkuApiDTO(BatchParamVO batchParamVO, JSONObject data, Map<String, File> imageFileMap, ImportProductDTO importProductDTO ,Map<Long,Map<Integer, PropertyApiDTO>> propertyApiDTOMap) {
        SaveSkuApiDTO saveSkuApiDTO = super.getSaveSkuApiDTO(batchParamVO, data, imageFileMap, importProductDTO,propertyApiDTOMap);
        Long brandId = getBrandId(data);
        saveSkuApiDTO.setBrandId(brandId);

        Long categoryId = getCategoryId(data);
        saveSkuApiDTO.setCatId(categoryId);
        return saveSkuApiDTO;
    }


    /**
     * 从给定的数据中提取品牌ID。
     * @param data 包含品牌ID的数据对象。
     * @return 解析出的品牌ID，若无法解析则返回null。
     */
    private Long getBrandId(JSONObject data) {
        Long brandId = null;

        String brandIdStr = this.getValueStr(data,FIX_BRAND_ID);
        if (StringUtils.isNotBlank(brandIdStr)){
            brandId = Long.parseLong(brandIdStr);
        }
        return brandId;
    }

    /**
     * 从给定的 JSONObject 中获取固定类别的 ID。
     * @param data JSONObject 对象，包含需要解析的数据。
     * @return 解析出的固定类别的 ID，若不存在则返回 null。
     */
    private Long getCategoryId(JSONObject data) {
        Long categoryId = null;

        String categoryIdStr = this.getValueStr(data,FIX_CATEGORY_ID);
        if (StringUtils.isNotBlank(categoryIdStr)){
            categoryId = Long.parseLong(categoryIdStr);
        }
        return categoryId;
    }

    public String getValueStr(JSONObject data,String key) {
        String idStr = this.getJSONValue(data, key);
        log.info("DisposableVcProductImportHandler.handleSpuApiDTO 新模版解析前key={}, value={}",key,idStr);
        if (StringUtils.isNotBlank(idStr) && !StringUtils.isNumeric(idStr)){
            idStr = ExcelUtils.getCellId(idStr);
            log.info("DisposableVcProductImportHandler.handleSpuApiDTO 新模版解析后 key={} value={}",key,idStr);
        }
        return idStr;
    }
}
