package com.jdi.isc.task.center.service.work.task.handler.wimpImport.hsCode;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.hsCode.br.HsCodeBrApiService;
import com.jdi.isc.product.soa.api.hsCode.br.req.HsCodeBrSaveUpdateBatchReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.br.req.HsCodeBrSaveUpdateReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.br.res.HsCodeBrSaveUpdateBatchResApiDTO;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.common.exception.TaskBizException;
import com.jdi.isc.task.center.common.utils.ExcelUtils;
import com.jdi.isc.task.center.domain.enums.customs.CustomsControlsEnum;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;
import com.jdi.isc.task.center.domain.task.excel.hsCode.HsCodeBrExcelDTO;
import com.jdi.isc.task.center.service.work.task.DisposableAbsJob;
import com.jdi.isc.task.center.service.work.task.frame.JobExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
@JobExecutor(taskBizType = TaskBizTypeEnum.PRODUCT_HS_CODE_BR_IMPORT)
public class HsCodeBrImportHandler extends DisposableAbsJob<HsCodeBrExcelDTO> {

    @Resource
    private HsCodeBrApiService hsCodeBrApiService;

    @Override
    public void run(TaskDTO<HsCodeBrExcelDTO> task) {
        log.info("run, taskId={}, data={} " ,task.getTaskId(), JSON.toJSONString(task.getTarget()));
        List<HsCodeBrExcelDTO> excelDTOList = task.getTarget();

        checkData(excelDTOList);

        // 将 list 分割成n个子列表
        List<List<HsCodeBrExcelDTO>> splitedList = ListUtils.partition(excelDTOList, 100);
        for(List<HsCodeBrExcelDTO> splitList: splitedList ){
            try {
                doRunRpc(task, splitList);
            } catch (Exception e) {
                log.error("run 任务 {} 执行失败", task.getTaskId(), e);
            }
        }
    }

    private void doRunRpc(TaskDTO<HsCodeBrExcelDTO> task, List<HsCodeBrExcelDTO> excelDTOList){
        // 转换组装合法的请求参数
        List<HsCodeBrSaveUpdateReqApiDTO> validReqList = excel2ApiDtoBatch(task, excelDTOList);
        if (CollectionUtils.isEmpty(validReqList)) {
            log.info("doRunRpc, validReqList empty.");
            return;
        }

        // 只处理校验通过的数据
        HsCodeBrSaveUpdateBatchReqApiDTO reqParam = new HsCodeBrSaveUpdateBatchReqApiDTO();
        reqParam.setReqList(validReqList);

        DataResponse<HsCodeBrSaveUpdateBatchResApiDTO> rpcRes = hsCodeBrApiService.saveOrUpdateBatch(reqParam);
        // 响应失败
        if (null==rpcRes || !Boolean.TRUE.equals(rpcRes.getSuccess()) || null==rpcRes.getData()) {
            log.info("doRunRpc, rpc saveOrUpdateBatch fail. rpcRes={}", JSONObject.toJSONString(rpcRes));
            String failMsg = (null!=rpcRes && StringUtils.isNotBlank(rpcRes.getMessage()))?rpcRes.getMessage():"未知结果";

            HsCodeBrSaveUpdateBatchResApiDTO rpcData = null!=rpcRes? rpcRes.getData():null;
            Map<String, String> failedRecordMap = null!=rpcData?rpcData.getFailedRecordMap():null;

            // 全部标记为失败
            for(HsCodeBrExcelDTO excelDTO:excelDTOList){
                // 已经标记为失败的数据跳过
                if (Boolean.FALSE.equals(excelDTO.getValid())){
                    continue;
                }

                String failedRecordMsg = null!=failedRecordMap?failedRecordMap.get(uniqueFlag(excelDTO)):null;
                excelDTO.failed(failMsg + (StringUtils.isNotBlank(failedRecordMsg)?("。"+failedRecordMsg):""));
            }
            return;
        }

        // 解析返回结果
        HsCodeBrSaveUpdateBatchResApiDTO rpcData = rpcRes.getData();
        Map<String/*唯一标识*/, String/*原因*/> failedRecordMap = rpcData.getFailedRecordMap();
        for(HsCodeBrExcelDTO excelDTO:excelDTOList){
            // 已经标记为失败的数据跳过
            if (Boolean.FALSE.equals(excelDTO.getValid())){
                continue;
            }

            String uniqueFlag = uniqueFlag(excelDTO);
            // 失败的数据设置失败原因
            if (null!=failedRecordMap && failedRecordMap.containsKey(uniqueFlag)){
                excelDTO.failed(failedRecordMap.get(uniqueFlag));
            }else {
                excelDTO.success();
            }
        }
    }

    private List<HsCodeBrSaveUpdateReqApiDTO> excel2ApiDtoBatch(TaskDTO<HsCodeBrExcelDTO> task, List<HsCodeBrExcelDTO> excelDTOList){
        if (CollectionUtils.isEmpty(excelDTOList)) {
            return Collections.emptyList();
        }

        List<HsCodeBrSaveUpdateReqApiDTO> resList = new ArrayList<>();
        for(HsCodeBrExcelDTO excelDTO:excelDTOList){
            // 未校验通过的数据不处理
            if (Boolean.FALSE.equals(excelDTO.getValid())){
                continue;
            }

            HsCodeBrSaveUpdateReqApiDTO apiDTO = null;
            try {
                apiDTO = excel2ApiDto(task, excelDTO);
            }catch (TaskBizException e){
                excelDTO.failed(e.getMessage());
                continue;
            }catch (Exception e) {
                log.error("excel2ApiDtoBatch, Exception excelDTO={}", JSONObject.toJSONString(excelDTO), e);
                excelDTO.failed("出错了，请检查数据是否合法");
                continue;
            }

            resList.add(apiDTO);
        }

        return resList;
    }

    private HsCodeBrSaveUpdateReqApiDTO excel2ApiDto(TaskDTO<HsCodeBrExcelDTO> task, HsCodeBrExcelDTO excelDto){
        HsCodeBrSaveUpdateReqApiDTO target = new HsCodeBrSaveUpdateReqApiDTO();
        try {
            target.setHsCode(excelDto.getHsCode());
            target.setImportTax( ExcelUtils.string2BigDecimal(excelDto.getImportTax()));
            target.setIndustryProductTax(ExcelUtils.string2BigDecimal(excelDto.getIndustryProductTax()));
            target.setSocialIntegrationTax(ExcelUtils.string2BigDecimal(excelDto.getSocialIntegrationTax()));
            target.setConfinsTax(ExcelUtils.string2BigDecimal(excelDto.getConfinsTax()));
            target.setAntiDumpingTax(ExcelUtils.string2BigDecimal(excelDto.getAntiDumpingTax()));
            target.setIcmsFlowTax(ExcelUtils.string2BigDecimal(excelDto.getIcmsFlowTax()));

            if("是".equals(excelDto.getControls())){
                target.setControls(CustomsControlsEnum.REQUIRED.getCode());
            }else if("否".equals(excelDto.getControls())){
                target.setControls(CustomsControlsEnum.NOT_REQUIRED.getCode());
                if (StringUtils.isNotBlank(target.getControlsInfo())){
                    throw new TaskBizException("是否管制为“否”时，不允许填写管制信息");
                }
            }else {
                throw new TaskBizException(String.format("* 是否管制输入不合法%s",excelDto.getControls()));
            }
            target.setControlsInfo(excelDto.getControlsInfo());
            target.setRemark(excelDto.getRemark());
            target.setCreator(task.getOperator());
            target.setUpdater(task.getOperator());
        }catch (Exception e){
            throw new RuntimeException("当前行数据输入不合法,请检查");
        }
        return target;
    }

    @Override
    public String uniqueFlag(HsCodeBrExcelDTO input){
        return StringUtils.isNotBlank(input.getHsCode())?input.getHsCode():"";
    }

    @Override
    public DataResponse checkByBiz(HsCodeBrExcelDTO excelDto) {
        if(StringUtils.isNotBlank(excelDto.getControls()) && "是".equals(excelDto.getControls().trim())
                && StringUtils.isBlank(excelDto.getControlsInfo())){
            excelDto.failed("是否管制为“是”时，管制信息不能为空！");
        }

        return DataResponse.success();
    }
}