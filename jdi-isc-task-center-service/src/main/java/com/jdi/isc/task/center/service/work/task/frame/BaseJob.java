package com.jdi.isc.task.center.service.work.task.frame;


import com.jdi.isc.task.center.common.exception.TaskBizException;
import com.jdi.isc.task.center.common.frame.AbsExecutor;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;

public interface BaseJob<T> {

    void accept(TaskDTO<T> task);

    void parse(TaskDTO<T> task);

    void run(TaskDTO<T> task);

    void export(TaskDTO<T> task);

    void errHandle(TaskDTO<T> task,Exception e);

    //导入类任务编排
    default AbsExecutor<Void> in(TaskDTO<T> task){
        return () -> {
            try {
                accept(task);
                try {
                    parse(task);
                }finally {
                    if(task.getTargetInputFile()!=null){
                        task.getTargetInputFile().close();
                    }
                }
                run(task);
                export(task);
            }catch (Exception e){
                errHandle(task,e);
            }
            return null;
        };
    }

    //导出类任务编排
    default AbsExecutor<Void> out(TaskDTO<T> task){
        return () -> {
            try {
                accept(task);
                export(task);
            }catch (Exception e){
                errHandle(task,e);
            }
            return null;
        };
    }

    default String getJobName(){
        throw new TaskBizException("please override getJobName method!");
    }

}
