package com.jdi.isc.task.center.service.atomic.joysky;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.task.center.domain.enums.YnEnum;
import com.jdi.isc.task.center.domain.joysky.po.JoySkyApprovalTaskPO;
import com.jdi.isc.task.center.repository.jed.mapper.joysky.JoySkyTaskBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/1
 * @Description JoySkyApprovalTaskPO 数据库操作实现类
 */
@Service
@Slf4j
public class JoySkyTaskAtomicService extends ServiceImpl<JoySkyTaskBaseMapper, JoySkyApprovalTaskPO> {

    /**
     * 根据流程key查询审批任务信息。
     *
     * @param processKey 审批流程key。
     * @return 对应的审批任务信息，若不存在则返回null。
     */
    public List<JoySkyApprovalTaskPO> queryApprovalTaskByProcessKey(String processKey) {
        if (StringUtils.isBlank(processKey)) {
            return null;
        }

        LambdaQueryWrapper<JoySkyApprovalTaskPO> wrapper = Wrappers.<JoySkyApprovalTaskPO>lambdaQuery()
                .eq(JoySkyApprovalTaskPO::getProcessKey, processKey)
                .eq(JoySkyApprovalTaskPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(wrapper);
    }


    /**
     * 根据流程ID查询审批任务信息。
     *
     * @param processInstanceId 审批流程实例id。
     * @return 对应的审批任务信息，若不存在则返回null。
     */
    public JoySkyApprovalTaskPO queryApprovalTaskByProcessId(String processInstanceId) {
        if (StringUtils.isBlank(processInstanceId)) {
            return null;
        }

        LambdaQueryWrapper<JoySkyApprovalTaskPO> wrapper = Wrappers.<JoySkyApprovalTaskPO>lambdaQuery()
                .eq(JoySkyApprovalTaskPO::getProcessInstanceId, processInstanceId)
                .eq(JoySkyApprovalTaskPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectOne(wrapper);
    }
}
