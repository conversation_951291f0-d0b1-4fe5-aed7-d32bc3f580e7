package com.jdi.isc.task.center.service.manage.log.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.isc.task.center.domain.price.po.PriceLogPO;
import com.jdi.isc.task.center.domain.sku.po.ChangeRecordPO;
import com.jdi.isc.task.center.domain.sku.po.SkuLogPO;
import com.jdi.isc.task.center.service.atomic.price.PriceLogAtomicService;
import com.jdi.isc.task.center.service.atomic.sku.ChangeRecordAtomicService;
import com.jdi.isc.task.center.service.atomic.sku.SkuLogAtomicService;
import com.jdi.isc.task.center.service.manage.log.LogManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;

/**
 * 日志清理服务
 * <AUTHOR>
 * @date 2025/5/19
 */
@Service
@Slf4j
public class LogManageServiceImpl implements LogManageService {

    @LafValue("isc.task.log.delete.keep.days")
    private Integer keepDays;
    @LafValue("isc.task.log.delete.flag")
    private String deleteFlagStr;
    @Resource
    private SkuLogAtomicService skuLogAtomicService;
    @Resource
    private ChangeRecordAtomicService changeRecordAtomicService;
    @Resource
    private PriceLogAtomicService priceLogAtomicService;
    private final static String skuLogPO = "SkuLogPO";
    private final static String priceLogPO = "PriceLogPO";
    private final static String changeRecordPO = "ChangeRecordPO";

    @Override
    public void clearLog() {
        long time = Instant.now().minusSeconds(keepDays * 24 * 60 * 60).toEpochMilli();
        JSONObject deleteFlag = JSONObject.parseObject(deleteFlagStr);

        //清理价格修改日志
        LambdaQueryWrapper<PriceLogPO> priceWrapper = Wrappers.<PriceLogPO>lambdaQuery()
                .le(PriceLogPO::getCreateTime, time);
        log.info("LogManageServiceImpl.clearLog 价格日志表{}天前存量待删除记录:{}条" ,  keepDays, priceLogAtomicService.count(priceWrapper));
        if(deleteFlag.getBoolean(priceLogPO)){
            priceLogAtomicService.remove(priceWrapper);
        }
        //清理商品修改日志
        LambdaQueryWrapper<SkuLogPO> skuWrapper = Wrappers.<SkuLogPO>lambdaQuery()
                .le(SkuLogPO::getCreateTime, time);
        log.info("LogManageServiceImpl.clearLog 商品日志表{}天前存量待删除记录:{}条" ,  keepDays, skuLogAtomicService.count(skuWrapper));
        if(deleteFlag.getBoolean(skuLogPO)){
            skuLogAtomicService.remove(skuWrapper);
        }
        //清理SPU修改日志
        LambdaQueryWrapper<ChangeRecordPO> spuWrapper = Wrappers.<ChangeRecordPO>lambdaQuery()
                .le(ChangeRecordPO::getCreateTime, time);
        log.info("LogManageServiceImpl.clearLog SPU日志表{}天前存量待删除记录:{}条" ,  keepDays, changeRecordAtomicService.count(spuWrapper));
        if(deleteFlag.getBoolean(changeRecordPO)){
            changeRecordAtomicService.remove(spuWrapper);
        }
    }

    public static void main(String[] args) {
        long time = Instant.now().minusSeconds(90 * 24 * 60 * 60).toEpochMilli();
        System.out.println(time);
    }

}
