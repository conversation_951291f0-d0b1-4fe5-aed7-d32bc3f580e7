package com.jdi.isc.task.center.service.atomic.stock;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.task.center.domain.enums.YnEnum;
import com.jdi.isc.task.center.domain.stock.po.WarehouseSkuPricePO;
import com.jdi.isc.task.center.repository.jed.mapper.stock.WarehouseSkuPriceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @author：xubing82
 * @date：2025/8/7 10:07
 * @description：WarehouseSkuPriceAtomicService
 */
@Slf4j
@Service
public class WarehouseSkuPriceAtomicService extends ServiceImpl<WarehouseSkuPriceMapper, WarehouseSkuPricePO> {

    /**
     * 删除历史数据，n days before前的数据。
     *
     * @return 删除的记录数。
     */
    public int deleteHistoryWarehousePriceData(Long createTimeEnd) {
        LambdaQueryWrapper<WarehouseSkuPricePO> wrapper = new LambdaQueryWrapper<WarehouseSkuPricePO>()
                //删除createTimeEnd之前的历史仓报价数据
                .lt(WarehouseSkuPricePO::getCreateTime, createTimeEnd)
                .eq(WarehouseSkuPricePO::getYn, YnEnum.YES.getCode());

        return this.baseMapper.delete(wrapper);
    }

}
