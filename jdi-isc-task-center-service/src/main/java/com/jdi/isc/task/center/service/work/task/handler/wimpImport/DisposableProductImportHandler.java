package com.jdi.isc.task.center.service.work.task.handler.wimpImport;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.model.S3Object;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.library.common.enmus.file.FileTypeEnum;
import com.jdi.isc.product.soa.api.common.BaseLangDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.TradeTypeConstant;
import com.jdi.isc.product.soa.api.common.enums.AtrributeValueInputCheckTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.AttributeInputTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuStatusEnum;
import com.jdi.isc.product.soa.api.sku.req.SaveSkuApiDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuBaseInfoApiDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyValueApiDTO;
import com.jdi.isc.product.soa.api.spu.req.SaveSpuApiDTO;
import com.jdi.isc.product.soa.api.spu.req.SpuLangApiDTO;
import com.jdi.isc.product.soa.api.spu.res.GroupPropertyDTO;
import com.jdi.isc.product.soa.api.spu.res.SaleUnitApiDTO;
import com.jdi.isc.product.soa.api.spu.res.SpuApiDTO;
import com.jdi.isc.product.soa.api.supplier.req.BusinessLineQueryReqDTO;
import com.jdi.isc.product.soa.api.supplier.res.BrandResDTO;
import com.jdi.isc.product.soa.api.supplier.res.CategoryTreeResDTO;
import com.jdi.isc.product.soa.api.translate.req.MultiTranslateReqDTO;
import com.jdi.isc.product.soa.api.wimp.brand.req.BrandReqApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.GlobalAttributeQueryReqDTO;
import com.jdi.isc.product.soa.api.wimp.category.res.GlobalAttributeApiResDTO;
import com.jdi.isc.product.soa.api.wimp.category.res.GlobalAttributeValueApiResDTO;
import com.jdi.isc.product.soa.api.wimp.lang.res.LangResDTO;
import com.jdi.isc.product.soa.price.api.jdPrice.res.IscEachSkuPriceResDTO;
import com.jdi.isc.product.soa.price.api.jdPrice.res.IscSkuDomesticPriceResDTO;
import com.jdi.isc.task.center.api.common.enums.SupplierTaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskStatusEnum;
import com.jdi.isc.task.center.common.costants.AttributeConstant;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.common.costants.ProductConstant;
import com.jdi.isc.task.center.common.costants.UmpKeyConstant;
import com.jdi.isc.task.center.common.ducc.InternationalDuccConfig;
import com.jdi.isc.task.center.common.exception.TaskBizException;
import com.jdi.isc.task.center.common.exception.TaskExportException;
import com.jdi.isc.task.center.common.utils.CommonUtils;
import com.jdi.isc.task.center.common.utils.ExcelUtils;
import com.jdi.isc.task.center.common.utils.ZipUtils;
import com.jdi.isc.task.center.domain.enums.YnEnum;
import com.jdi.isc.task.center.domain.excel.BatchParamVO;
import com.jdi.isc.task.center.domain.gms.req.JdProductQueryDTO;
import com.jdi.isc.task.center.domain.gms.res.JdProductDTO;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;
import com.jdi.isc.task.center.domain.task.excel.ImportProductDTO;
import com.jdi.isc.task.center.domain.task.vo.TaskStrategyVO;
import com.jdi.isc.task.center.domain.tax.CalculateTaxVO;
import com.jdi.isc.task.center.rpc.brand.BrandRpcService;
import com.jdi.isc.task.center.rpc.category.RpcAttributeService;
import com.jdi.isc.task.center.rpc.country.CountryLangRpcService;
import com.jdi.isc.task.center.rpc.country.CountryRpcService;
import com.jdi.isc.task.center.rpc.gms.MkuMappingRpcService;
import com.jdi.isc.task.center.rpc.gms.RpcGdProductService;
import com.jdi.isc.task.center.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.task.center.rpc.mku.MkuRpcService;
import com.jdi.isc.task.center.rpc.sku.SkuRpcService;
import com.jdi.isc.task.center.rpc.spu.RpcSpuService;
import com.jdi.isc.task.center.rpc.supplier.RpcBusinessLineService;
import com.jdi.isc.task.center.rpc.supplier.RpcSupplierAccountService;
import com.jdi.isc.task.center.rpc.translate.TranslateRpcService;
import com.jdi.isc.task.center.rpc.vendor.VendorPriceRpcService;
import com.jdi.isc.task.center.service.manage.globalattribute.GlobalAttributeManageService;
import com.jdi.isc.task.center.service.manage.task.strategy.TaskContextService;
import com.jdi.isc.task.center.service.manage.task.strategy.TaskStrategyService;
import com.jdi.isc.task.center.service.manage.tax.CalculateTaxManageService;
import com.jdi.isc.task.center.service.manage.template.support.TemplateSupportService;
import com.jdi.isc.task.center.service.manage.translate.TranslateService;
import com.jdi.isc.task.center.service.support.AssertValidation;
import com.jdi.isc.task.center.service.work.task.frame.BaseExecutableAbsJob;
import com.jdi.isc.vc.soa.api.supplier.req.SupplierBaseInfoDTO;
import com.jdi.isc.vc.soa.api.supplier.res.SupplierBaseInfoRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipException;
import java.util.zip.ZipInputStream;

import static com.jdi.isc.task.center.common.costants.AttributeConstant.EXPORT_NAME;
import static com.jdi.isc.task.center.common.costants.AttributeConstant.EXPORT_ONE;
import static com.jdi.isc.task.center.common.costants.Constant.*;
import static com.jdi.isc.task.center.common.costants.ProductConstant.*;

/**
 * <AUTHOR>
 * @date 2024/9/3
 **/
@Slf4j
@Service
public abstract class DisposableProductImportHandler extends BaseExecutableAbsJob<ImportProductDTO> {

    @Value("${jdi.isc.zip.path}")
    private String outputFolderPath;
    @Resource
    public RpcSpuService rpcSpuService;
    @Resource
    private CountryLangRpcService countryLangRpcService;
    @Resource
    public TaskContextService taskContextService;
    @Resource
    private TranslateRpcService translateRpcService;
    @Resource
    private RpcBusinessLineService rpcBusinessLineService;
    @Resource
    private TemplateSupportService templateSupportService;
    @Resource
    private InternationalDuccConfig internationalDuccConfig;
    @Resource
    private RpcAttributeService rpcAttributeService;
    @Resource
    private GlobalAttributeManageService globalAttributeManageService;
    @Resource
    private BrandRpcService brandRpcService;
    @Resource
    private SkuInfoRpcService skuInfoRpcService;
    @Resource
    private MkuMappingRpcService mkuMappingRpcService;
    @Resource
    private VendorPriceRpcService vendorPriceRpcService;
    @Resource
    private RpcGdProductService rpcGdProductService;
    @Resource
    private TranslateService translateService;
    @Resource
    private CountryRpcService countryRpcService;
    @Resource
    public SkuRpcService skuRpcService;
    @Resource
    public MkuRpcService mkuRpcService;
    @Resource
    private CalculateTaxManageService calculateTaxManageService;
    @Resource
    private RpcSupplierAccountService rpcSupplierAccountService;

    private static final String CN_VENDOR_CODE = "bjjdbyxxjs";

    private static final String STANDARD_SKU_FLAG = "1";

    private static final int SKU_NAME_LEN = 200;

    private Map<Long, IscEachSkuPriceResDTO> skuPriceMap = new HashMap<>();
    /**查询国内采购价失败的商品关系*/
    private Map<Long,IscEachSkuPriceResDTO> failedSkuPriceMap = new HashMap<>();
    // jdSku 可售
    private List<Long> skuCanPurchaseList = new ArrayList<>();
    // jdSku 区域限售
    private List<Long> skuAreaLimit = new ArrayList<>();
    // jdSku 基础信息
    private Map<Long, JdProductDTO> skuMap = new HashMap<>();
    // jdSpu 商详
    private Map<Long, Map<String, String>> descriptionMap = new HashMap<>();
    // 国家对应语言
    private List<String> langList = new ArrayList<>();
    // 品牌 多语
    private Map<Long, Map<String, String>> brandNameMap = new HashMap<>();
    // 来源国家
    private String sourceCountryCode;
    // 系统编码
    private String systemCode;
    // 主图
    private Map<Long,String> mainImageMap = new HashMap<>();
    /**
     * 价格接口支持mku逻辑
     */
    @LafValue("jdi.isc.spu.import.supportMku")
    private Boolean supportMku;
    /**
     * 商品详情翻译语种集合
     */
    @LafValue("jdi.isc.spu.import.lang")
    private String lang;

    @Value("${image.domain}")
    private String imageDomain;

    @LafValue("${isc.image.domain}")
    private String iscImageDomain;

    /**
     * 模板下载采购价
     */
    @LafValue("jdi.isc.task.product.purchase.countryCodes")
    private Set<String> countryCodeSets;

    @Value("${jdi.isc.task.env}")
    public String env;
    /** 固定sku信息，用于特定sku固定其采购价和供应商信息*/
    @LafValue("jdi.isc.oper.fixed.sku.info")
    protected String fixedSkuInfo;
    @LafValue("jdi.isc.price.contract.config")
    private String contactConfig;
    @LafValue("jdi.isc.sale.attr.default")
    private String saleAttrDefault;


    @Override
    public void parse(TaskDTO<ImportProductDTO> task) {
        log.info("DisposableProductImportHandler.parse {},file:{} ", task.getTaskId(), task.getTargetInputFile().getKey());
        String reqFileName = FileUtil.mainName(task.getTaskStrategyVO().getReqFileName());
        if (task.getTargetInputFile() != null && task.getTargetInputFile().getObjectContent() != null) {
            String outputFolderPathTemp = outputFolderPath + File.separator + System.nanoTime();
            try {
                // 从task#targetInputFile 获取用户上传文件并解压缩zip包
                boolean unzip = this.unzip(task.getTaskStrategyVO().getReqFileUrl(), outputFolderPathTemp);
                if (!unzip) {
                    log.info("DisposableProductImportHandler.parse 解压文件失败 taskId:{},file:{} ", task.getTaskId(), task.getTargetInputFile().getKey());
                    // 记录解压失败信息
                    super.errHandle(task, new ZipException("解压文件异常"));
                    return;
                }

                // 读取解压后的数据
                Pair<File, Map<String, File>> fileMapPair = this.readFiles(outputFolderPathTemp, reqFileName);
                Map<String, File> imageFileMap = fileMapPair.getRight();
                if (null == fileMapPair.getLeft()){
                    super.errHandle(task, new ZipException("批量发品没有EXCEL文件"));
                    return;
                }
                // 拼装List<Bean>业务实体 target
                // 通过EasyExcel读取数据
                List<ImportProductDTO> importSpuExcelList = getImportSpuExcelList(task,fileMapPair.getLeft());

                // 初步校验数据合法性,将非法结果写到ImportSpuDto#writeResult中,通过校验的记录结果置ImportSpuDto#valid=true
                if (CollectionUtils.isEmpty(importSpuExcelList)) {
                    log.info("DisposableProductImportHandler.parse 批量发品模版内容不能为空 taskId:{},file:{} ", task.getTaskId(), task.getTargetInputFile().getKey());
                    // 记录解压失败信息
                    super.errHandle(task, new ZipException("批量发品模版内容不能为空"));
                    return;
                }

                // validation校验
                this.validateImportExcel(importSpuExcelList,task);
                // 获取jdSkuId
                this.handleInitData(importSpuExcelList,task);
                // 设置初始值
                this.setInitData(importSpuExcelList,task);
                // 处理所有数据
                this.handleExcelData(importSpuExcelList, imageFileMap, task);
            } catch (Exception e) {
                log.error("【系统异常】DisposableProductImportHandler.parse 解析上传文件异常,taskId={},fileName={}", task.getTaskId(), task.getFileName(), e);
                super.errHandle(task, new TaskBizException("解析文件发生异常"));
            } finally {
                ZipUtils.deleteFile(new File(outputFolderPathTemp));
            }
        }
    }


    /**
     * 解析excel文件
     *
     * @param excelFile 读取excel内容到实体类
     * @return 返回上传的数据
     * [
  {
    "fields": ["spuName", "brandId", "categoryId", "mainImage", "salePrice", "color", "storage"],
    "data": {
      "spuName": "iPhone 15 Pro",
      "brandId": "1001",
      "categoryId": "2001",
      "mainImage": "iphone15.jpg",
      "salePrice": "999.99",
      "color": "深空黑",
      "storage": "128GB"
    },
    "fieldColumnInfoMap": {
      "spuName": {
        "field": "spuName",
        "columnNum": 0,
        "required": true,
        "title": "商品名称*"
      },
      "brandId": {
        "field": "brandId",
        "columnNum": 1,
        "required": true,
        "title": "品牌ID*"
      },
      "categoryId": {
        "field": "categoryId",
        "columnNum": 2,
        "required": true,
        "title": "类目ID*"
      },
      "mainImage": {
        "field": "mainImage",
        "columnNum": 3,
        "required": true,
        "title": "主图*"
      },
      "salePrice": {
        "field": "salePrice",
        "columnNum": 4,
        "required": true,
        "title": "销售价格*"
      },
      "color": {
        "field": "color",
        "columnNum": 5,
        "required": false,
        "title": "颜色"
      },
      "storage": {
        "field": "storage",
        "columnNum": 6,
        "required": false,
        "title": "尺码"
      }
    },
    "batchParamVO": {
      "sourceCountryCode": "CN",
      "attributeScope": "CN,VN,TH",
      "export": null,
      "lang": "zh",
      "operator": "admin",
      "supplierCode": "SUPPLIER001",
      "identityFlag": 1,
      "categoryId": 2001,
      "systemCode": "ISC"
    },
    "importExcelDataClass": "com.jdi.isc.task.center.dynamic.GeneratedClass_12345",
    "valid": true,
    "result": null
  },
  {
    "fields": ["spuName", "brandId", "categoryId", "mainImage", "salePrice", "color", "storage"],
    "data": {
      "spuName": "Samsung S24",
      "brandId": "1002",
      "categoryId": "2001",
      "mainImage": "samsung24.jpg",
      "salePrice": "899.99",
      "color": "幻影白",
      "storage": "256GB"
    },
    "fieldColumnInfoMap": {
      // 同上，字段映射信息相同
    },
    "batchParamVO": {
      // 同上，批量参数相同
    },
    "importExcelDataClass": "com.jdi.isc.task.center.dynamic.GeneratedClass_12345",
    "valid": true,
    "result": null
  },
  {
    "fields": ["spuName", "brandId", "categoryId", "mainImage", "salePrice", "color", "storage"],
    "data": {
      "spuName": "小米14 Ultra",
      "brandId": "1003",
      "categoryId": "2001",
      "mainImage": "xiaomi14.jpg",
      "salePrice": "799.99",
      "color": "钛金灰",
      "storage": "512GB"
    },
    "fieldColumnInfoMap": {
      // 同上，字段映射信息相同
    },
    "batchParamVO": {
      // 同上，批量参数相同
    },
    "importExcelDataClass": "com.jdi.isc.task.center.dynamic.GeneratedClass_12345",
    "valid": true,
    "result": null
  }
]
     */

    private List<ImportProductDTO> getImportSpuExcelList(TaskDTO<ImportProductDTO> task, File excelFile) {
        if (excelFile == null) {
            log.info("getImportSpuExcelList, excelFile is null.");
            return Collections.emptyList();
        }

        // 1、解析文件 workbook
        try (FileInputStream fileInputStream = new FileInputStream(excelFile)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fileInputStream);
            XSSFSheet sheetAt = workbook.getSheetAt(0);
            if (sheetAt == null) {
                 super.errHandle(task, new TaskBizException(String.format("任务 %s 文件为空", task.getTaskId())));
                 return Collections.emptyList();
            }

            BatchParamVO batchParamVO = new BatchParamVO();
            Integer bizType = task.getTaskStrategyVO().getBizType();
            if (TaskBizTypeEnum.CN_CREATE_PRODUCT_BATCH_IMPORT.getCode() == bizType || TaskBizTypeEnum.INT_CREATE_PRODUCT_BATCH_IMPORT.getCode() == bizType){
                XSSFRow row = sheetAt.getRow(0);
                XSSFCell cell = row.getCell(0);
                String cellValue = cell.getStringCellValue();

                String[] split = cellValue.split("\n");
                if (ArrayUtils.isEmpty(split)) {
                    super.errHandle(task,new TaskBizException("表头第一行数据错误"));
                }
                Map<String,String> countryNameCodeMap = countryRpcService.getCountryMap();
                String sourceCountryName = null;
                if(split[0].contains(AttributeConstant.BELONG_COUNTRY)){
                    sourceCountryName = split[0].replace(AttributeConstant.BELONG_COUNTRY, "");
                } else {
                    sourceCountryName = split[0].replace(AttributeConstant.BELONG_COUNTRY_EN, "");
                }

                Map<String, String> swapMap = CommonUtils.swapKeyMap(countryNameCodeMap);
                swapMap.put(EXPORT_NAME, EXPORT_ONE + "");
                List<String> countryCodes = countryRpcService.getCountryCodes();
                if(CollectionUtils.isNotEmpty(countryCodes)){
                    countryCodes.forEach(item -> swapMap.put(item, item));
                }
                batchParamVO.setSourceCountryCode(swapMap.get(sourceCountryName));
                String scope = null;
                if(split[1].contains(AttributeConstant.SALE_COUNTRY)){
                    scope = split[1].replace(AttributeConstant.SALE_COUNTRY, "");
                } else {
                    scope = split[1].replace(AttributeConstant.SALE_COUNTRY_EN, "");
                }

                String attributeScope = String.join(Constant.COMMA,Arrays.stream(scope.split(Constant.COMMA)).map(swapMap::get).collect(Collectors.toList()));
                batchParamVO.setAttributeScope(attributeScope);
                batchParamVO.setExport(attributeScope.contains(EXPORT_ONE+"") ? EXPORT_ONE : null);

                String reqParam = task.getTaskStrategyVO().getReqParam();
                BatchParamVO batchParamVO1 = JSON.parseObject(reqParam, BatchParamVO.class);

                batchParamVO.setLang(batchParamVO1.getLang());
                batchParamVO.setOperator(batchParamVO1.getOperator());
                batchParamVO.setSupplierCode(batchParamVO1.getSupplierCode());
                batchParamVO.setIdentityFlag(batchParamVO1.getIdentityFlag());
                batchParamVO.setCategoryId(batchParamVO1.getCategoryId());
                batchParamVO.setSystemCode(batchParamVO1.getSystemCode());
                sourceCountryCode = batchParamVO.getSourceCountryCode();
                systemCode = batchParamVO.getSystemCode();
            }

            // 隐藏sheet数据读取
            XSSFSheet hiddenSheet = workbook.getSheet(ProductConstant.HIDDEN_SHEET_NAME);
            if (hiddenSheet == null){
                super.errHandle(task, new TaskBizException(String.format("任务%s 的模板没有隐藏页，请使用原版模板", task.getTaskId())));
                return Collections.emptyList();
            }

            // 读取每一列的内容
            XSSFRow row = hiddenSheet.getRow(0);
            int numberOfCells = row.getPhysicalNumberOfCells();
            // 第一个数据sheet的表头行
            XSSFRow dataSheetRow = sheetAt.getRow(1);
            List<String> fields = new ArrayList<>(numberOfCells);
            Map<String, ImportProductDTO.FieldColumnInfo> fieldColumnInfoMap = Maps.newHashMap();
            for (int i = 0; i < numberOfCells; i++) {
                String filed = ExcelUtils.getCellValueStr(row, i);
                fields.add(filed);
                ImportProductDTO.FieldColumnInfo fieldColumnInfo = new ImportProductDTO.FieldColumnInfo();
                String titleValue = ExcelUtils.getCellValueStr(dataSheetRow, i);
                fieldColumnInfo.setColumnNum(i);
                fieldColumnInfo.setField(filed);
                fieldColumnInfo.setTitle(titleValue);
                fieldColumnInfo.setRequired(StringUtils.isNotBlank(titleValue) && titleValue.startsWith(Constant.STAR));
                fieldColumnInfoMap.put(filed, fieldColumnInfo);
            }
            // 生成动态的解析类
            Class<?> generateClass = generateClass(fields);

            List<Object> objectList = Lists.newArrayList();
            try (FileInputStream excelFileInputStream = new FileInputStream(excelFile)) {
                objectList = EasyExcelFactory.read(excelFileInputStream, generateClass, new PageReadListener<Object>(dataList -> {
                })).head(generateClass).sheet(0).headRowNumber(3).autoTrim(Boolean.TRUE).doReadSync();
            }

            generateClass = null;

            // Convert to JSONObject list
            List<JSONObject> dataList = Lists.newArrayList();
            for (Object data : objectList) {
                dataList.add((JSONObject) JSON.toJSON(data));
            }

            List<ImportProductDTO> importProductDTOList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(dataList)) {
                for (JSONObject o : dataList) {
                    ImportProductDTO importProductDTO = new ImportProductDTO();
                    importProductDTO.setImportExcelDataClass(generateClass);
                    importProductDTO.setFields(fields);
                    importProductDTO.setData(o);
                    importProductDTO.setFieldColumnInfoMap(fieldColumnInfoMap);
                    importProductDTO.setBatchParamVO(batchParamVO);
                    importProductDTOList.add(importProductDTO);
                }
            }
            return importProductDTOList;
        } catch (Exception e) {
            log.error("【系统异常】EasyExcel.read error excelFileName={} e:", excelFile.getName(), e);
        }
        log.info("getImportSpuExcelList, resul is null.");
        return Collections.emptyList();
    }


    private void validateImportExcel(List<ImportProductDTO> importProductDTOList,TaskDTO<ImportProductDTO> taskDTO) {
        if (CollectionUtils.isEmpty(importProductDTOList)) {
            return;
        }

        String reqParam = taskDTO.getTaskStrategyVO().getReqParam();
        BatchParamVO batchParamVO = JSON.parseObject(reqParam, BatchParamVO.class);
        Integer bizType = taskDTO.getTaskStrategyVO().getBizType();
        if (TaskBizTypeEnum.CN_CREATE_PRODUCT_BATCH_IMPORT.getCode() == bizType || TaskBizTypeEnum.INT_CREATE_PRODUCT_BATCH_IMPORT.getCode() == bizType){
            batchParamVO = importProductDTOList.get(0).getBatchParamVO();
        }

        log.info("DisposableProductImportHandler.validateImportExcel 校验开始，taskId={}", taskDTO.getTaskId());
        String supplierCode = batchParamVO.getSupplierCode();
        String lang = StringUtils.isNotBlank(batchParamVO.getLang()) ? batchParamVO.getLang() : LangConstant.LANG_EN;

        // 查询供应商所有品牌
        Set<String> brandIdSet = Sets.newHashSet();
        // 跨境品支持所有品牌
        List<BrandResDTO> brandResDTOList = Lists.newArrayList();
        if(CountryConstant.COUNTRY_ZH.equals(batchParamVO.getSourceCountryCode())){
            List<String> langList = Arrays.asList(LangConstant.LANG_ZH,LangConstant.LANG_EN,LangConstant.LANG_TH,LangConstant.LANG_VN);
            BrandReqApiDTO brandReqApiDTO = new BrandReqApiDTO();
            brandReqApiDTO.setLangSet(new HashSet<>(langList));
            brandResDTOList = brandRpcService.queryBrandList(brandReqApiDTO);
            brandIdSet = Optional.ofNullable(brandResDTOList).orElseGet(ArrayList::new).stream().map(BrandResDTO::getBrandId).map(String::valueOf).collect(Collectors.toSet());
        }

        // 查询供应商所有类目
        Set<String> fourCatIdSet = this.queryFourCategorySet(importProductDTOList, taskDTO, batchParamVO);
        List<CategoryTreeResDTO> fourCategoryResDTOList;
        int rowNum = 1;
        for (ImportProductDTO importProductDTO : importProductDTOList) {
            if(Objects.nonNull(taskDTO.getTaskStrategyVO())
                    && TaskBizTypeEnum.INT_CREATE_PRODUCT_BATCH_IMPORT.getCode().equals(taskDTO.getTaskStrategyVO().getBizType())){
                JSONObject data = importProductDTO.getData();
                if(StringUtils.isBlank(batchParamVO.getSupplierCode())){
                    String supplierCodeStr = data.getString(FIX_SUPPLIER_CODE);
                    // 供应商是否存在
                    SupplierBaseInfoDTO baseInfoDTO = new SupplierBaseInfoDTO();
                    baseInfoDTO.setSupplierCode(supplierCodeStr);
                    SupplierBaseInfoRes supplierBase = rpcSupplierAccountService.getSupplierBaseInfoBySupplierCode(baseInfoDTO);
                    if (Objects.isNull(supplierBase)) {
                        importProductDTO.failed(String.format("供应商简码 %s 不存在",supplierCodeStr));
                        continue;
                    }
                    batchParamVO.setSupplierCode(supplierCodeStr);
                }
                fourCategoryResDTOList = templateSupportService.queryFourCategoryResDTOList(batchParamVO);
                fourCatIdSet = Optional.ofNullable(fourCategoryResDTOList).orElseGet(ArrayList::new).stream().map(CategoryTreeResDTO::getCatId).map(String::valueOf).collect(Collectors.toSet());
            }
            // 但前行数据
            JSONObject data = importProductDTO.getData();
            log.info("DisposableProductImportHandler.validateImportExcel 校验开始，taskId={} rowNum={} ,data={}", taskDTO.getTaskId(),rowNum,JSON.toJSONString(data));
            if (Objects.isNull(data)){
                importProductDTO.failed(this.getI18nMessageByKey(TEMPLATE_ERROR_DATA_NULL_I18N,lang));
                continue;
            }

            // 取第一个类目，只能是选择类目任务  全类目模板要特殊处理
            String categoryId = this.getValueStr(data, FIX_CATEGORY_ID);
            if (StringUtils.isNotBlank(categoryId)){
                // 非跨境品只查询产品线的品牌
                if(!CountryConstant.COUNTRY_ZH.equals(batchParamVO.getSourceCountryCode())){
                    BusinessLineQueryReqDTO queryReqDTO = new BusinessLineQueryReqDTO();
                    queryReqDTO.setSupplierCode(supplierCode);
                    queryReqDTO.setCategoryId(Long.parseLong(categoryId));
                    queryReqDTO.setLangSet(Sets.newHashSet(LangConstant.LANG_ZH));
                    brandResDTOList = rpcBusinessLineService.queryBrandListBySupplierCode(new BusinessLineQueryReqDTO(batchParamVO.getSupplierCode()
                            , Long.parseLong(categoryId), Sets.newHashSet(LangConstant.LANG_ZH)));
                    brandIdSet = Optional.ofNullable(brandResDTOList).orElseGet(ArrayList::new).stream().map(BrandResDTO::getBrandId).map(String::valueOf).collect(Collectors.toSet());
                }

//                // 查询当前类目销售属性和属性值映射  用于销售属性值回填  后面替换成批量接口
//                List<AttributeDTO> attributeDTOList = rpcAttributeService.queryAttributeList(new AttributeQueryReqDTO(Long.parseLong(categoryId), AttributeTypeEnum.SELL, Sets.newHashSet(LangConstant.LANG_ZH)));
//                if (CollectionUtils.isNotEmpty(attributeDTOList)) {
//                    Map<Long, Set<Long>> saleAttributeAndValueMap = Maps.newHashMap();
//                    attributeDTOList.forEach(attributeDTO -> {
//                        if (CollectionUtils.isNotEmpty(attributeDTO.getAttributeValueList())) {
//                            saleAttributeAndValueMap.put(attributeDTO.getId(), attributeDTO.getAttributeValueList().stream().map(AttributeValueDTO::getId).collect(Collectors.toSet()));
//                        }
//                    });
//                    log.info("DisposableProductImportHandler.validateImportExcel 校验开始，taskId={} rowNum={} ,saleAttributeAndValueMap={}", taskDTO.getTaskId(),rowNum,JSON.toJSONString(saleAttributeAndValueMap));
//                    importProductDTO.setSaleAttributeAndValueMap(saleAttributeAndValueMap);
//                }

                log.info("DisposableProductImportHandler.validateImportExcel 校验开始，taskId={} rowNum={} ,categoryId={}", taskDTO.getTaskId(),rowNum, categoryId);

                // SPU跨境属性校验
                Map<Long,GlobalAttributeApiResDTO>  spuGlobalResMap =  globalAttributeManageService.queryGlobalAttributeMapByCatId(this.getGlobalAttributeQueryReqDTO(batchParamVO,Long.parseLong(categoryId), AttributeConstant.SPU_GLOBAL_DIMENSION));
                this.setSpuGlobalAttributeCheckboxMap(importProductDTO, spuGlobalResMap);
                // SKU跨境属性校验
                Map<Long,GlobalAttributeApiResDTO>  skuGlobalResMap =  globalAttributeManageService.queryGlobalAttributeMapByCatId(this.getGlobalAttributeQueryReqDTO(batchParamVO,Long.parseLong(categoryId), AttributeConstant.SKU_GLOBAL_DIMENSION));
                this.setSkuGlobalAttributeCheckboxMap(importProductDTO, skuGlobalResMap);
            }


            // 字段信息映射
            Map<String, ImportProductDTO.FieldColumnInfo> fieldColumnInfoMap = importProductDTO.getFieldColumnInfoMap();
            log.info("DisposableProductImportHandler.validateImportExcel 校验开始，taskId={} rowNum={} ,fieldColumnInfoMap={}", taskDTO.getTaskId(),rowNum,JSON.toJSONString(fieldColumnInfoMap));
            for (Map.Entry<String, ImportProductDTO.FieldColumnInfo> entry : fieldColumnInfoMap.entrySet()) {
                if (!importProductDTO.getValid()) {
                    break;
                }
                String field = entry.getKey();
                ImportProductDTO.FieldColumnInfo fieldColumnInfo = entry.getValue();
                if (Objects.isNull(fieldColumnInfo)) {
                    continue;
                }
                // 必填校验
                if (fieldColumnInfo.getRequired() && (!data.containsKey(field) || StringUtils.isBlank(data.getString(field))) ){
                    log.info("DisposableProductImportHandler.validateImportExcel 校验 数据必填 taskId={} rowNum={} ,field={}", taskDTO.getTaskId(),rowNum,field);
                    importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_NOT_NULL_I18N,lang),fieldColumnInfo.getTitle()));
                    break;
                }
                log.info("DisposableProductImportHandler.validateImportExcel 校验 taskId={} rowNum={} ,field={} fieldColumnInfo={}", taskDTO.getTaskId(),rowNum,field,JSON.toJSONString(fieldColumnInfo));
                if (StringUtils.isNotBlank(fieldColumnInfo.getTitle()) && fieldColumnInfo.getTitle().startsWith(Constant.STAR) && (!data.containsKey(field) || StringUtils.isBlank(data.getString(field)))) {
                    log.info("DisposableProductImportHandler.validateImportExcel 校验 数据不能为空 taskId={} rowNum={} ,field={}", taskDTO.getTaskId(),rowNum,field);
                    importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_NOT_NULL_I18N,lang),fieldColumnInfo.getTitle()));
                    break;
                }

                // 数字类型校验
                if (fixColumnNumberSet.contains(fieldColumnInfo.getField()) && data.containsKey(field)  && StringUtils.isNotBlank(data.getString(field)) && !NumberUtils.isDigits(data.getString(field))) {
                    log.info("DisposableProductImportHandler.validateImportExcel 校验 不是数字 taskId={} rowNum={} ,field={} value={}", taskDTO.getTaskId(),rowNum,field,data.getString(field));
                    importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_NOT_NUMBER_I18N,lang),fieldColumnInfo.getTitle()));
                    break;
                }
                // 数字类型非整数校验
//                if (fixColumnNumberNotZeroSet.contains(fieldColumnInfo.getField()) && data.containsKey(field) && StringUtils.isNotBlank(data.getString(field)) && !CommonUtils.thanZeroInteger(data.getString(field))) {
//                    log.info("DisposableProductImportHandler.validateImportExcel 校验 不是非零正整数字 taskId={} rowNum={} ,field={} value={}", taskDTO.getTaskId(),rowNum,field,data.getString(field));
//                    importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_NOT_ZERO_I18N,lang),fieldColumnInfo.getTitle()));
//                    break;
//                }
                // 校验价格格式，并处理千分位
                if (FIX_UNTAXED_PURCHASE_PRICE.equals(fieldColumnInfo.getField()) && data.containsKey(field) && StringUtils.isNotBlank(data.getString(field)) && !CommonUtils.validatePrice(data.getString(field))){
                    log.info("DisposableProductImportHandler.validateImportExcel 校验采购价 不是正确的数字 taskId={} rowNum={} ,field={} value={}", taskDTO.getTaskId(),rowNum,field,data.getString(field));
                    importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_NOT_ZERO_I18N,lang),fieldColumnInfo.getTitle()));
                    break;
                }

                // 校验价格格式，并处理千分位
                if (FIX_TAXED_PURCHASE_PRICE.equals(fieldColumnInfo.getField()) && data.containsKey(field) && StringUtils.isNotBlank(data.getString(field)) && !CommonUtils.validatePrice(data.getString(field))){
                    log.info("DisposableProductImportHandler.validateImportExcel 校验含税采购价 不是正确的数字 taskId={} rowNum={} ,field={} value={}", taskDTO.getTaskId(),rowNum,field,data.getString(field));
                    importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_NOT_ZERO_I18N,lang),fieldColumnInfo.getTitle()));
                    break;
                }


                // SPU跨境属性多选值校验
                if (field.startsWith(SPU_GLOBAL_I18N) && field.endsWith(AttributeConstant.MULTIPLE_SELECTION+"") && data.containsKey(field) && StringUtils.isNotBlank(data.getString(field))){
                    Set<String> checkBoxValues = Arrays.stream(data.getString(field).split(Constant.COMMA)).collect(Collectors.toSet());
                    String spuGlobalAttributeId = field.split(Constant.UNDER_LINE)[1];
                    Map<Long, Map<String, Long>> checkBoxValueMap = importProductDTO.getSpuAttributeAndCheckBoxValueMap();
                    if (MapUtils.isEmpty(checkBoxValueMap) && CollectionUtils.isEmpty(checkBoxValues)) {
                        log.info("DisposableProductImportHandler.validateImportExcel 校验 SPU跨境属性多选值 taskId={} rowNum={} ,field={} value={}", taskDTO.getTaskId(),rowNum,field,data.getString(field));
                        importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_SPU_GLOBAL_CHECKBOX_I18N,lang),fieldColumnInfo.getTitle()));
                        break;
                    }
                    long spuAttributeId = Long.parseLong(spuGlobalAttributeId);
                    if ( !checkBoxValueMap.containsKey(spuAttributeId) || MapUtils.isEmpty(checkBoxValueMap.get(spuAttributeId))) {
                        log.info("DisposableProductImportHandler.validateImportExcel 校验 SPU跨境属性多选值 taskId={} rowNum={} ,field={} value={}", taskDTO.getTaskId(),rowNum,field,data.getString(field));
                        importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_SPU_GLOBAL_CHECKBOX_I18N,lang),fieldColumnInfo.getTitle()));
                        break;
                    }else {
                        Map<String, Long> valueMap = checkBoxValueMap.get(spuAttributeId);
                        for (String value : checkBoxValues) {
                            if (!valueMap.containsKey(value)) {
                                log.info("DisposableProductImportHandler.validateImportExcel 校验 SPU跨境属性多选值,填写的属性值不存在 taskId={} rowNum={} ,field={} value={},globalValue={}", taskDTO.getTaskId(),rowNum,field,data.getString(field),value);
                                importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_SPU_GLOBAL_CHECKBOX_I18N,lang),fieldColumnInfo.getTitle()));
                                break;
                            }
                        }
                    }
                }
                // SKU跨境属性多选值校验
                if (field.startsWith(SKU_GLOBAL_I18N) && field.endsWith(AttributeConstant.MULTIPLE_SELECTION+"") && data.containsKey(field) && StringUtils.isNotBlank(data.getString(field))){
                    Set<String> checkBoxValues = Arrays.stream(data.getString(field).split(Constant.COMMA)).collect(Collectors.toSet());
                    String skuGlobalAttributeIdStr = field.split(Constant.UNDER_LINE)[1];
                    Map<Long, Map<String, Long>> checkBoxValueMap = importProductDTO.getSkuAttributeAndCheckBoxValueMap();
                    if (MapUtils.isEmpty(checkBoxValueMap) && CollectionUtils.isEmpty(checkBoxValues)) {
                        log.info("DisposableProductImportHandler.validateImportExcel 校验 SPU跨境属性多选值 taskId={} rowNum={} ,field={} value={}", taskDTO.getTaskId(),rowNum,field,data.getString(field));
                        importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_SPU_GLOBAL_CHECKBOX_I18N,lang),fieldColumnInfo.getTitle()));
                        break;
                    }
                    //
                    long skuAttributeId = Long.parseLong(skuGlobalAttributeIdStr);
                    if (!checkBoxValueMap.containsKey(skuAttributeId) || MapUtils.isEmpty(checkBoxValueMap.get(skuAttributeId))) {
                        log.info("DisposableProductImportHandler.validateImportExcel 校验 SKU跨境属性多选值 taskId={} rowNum={} ,field={} value={}", taskDTO.getTaskId(),rowNum,field,data.getString(field));
                        importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_SPU_GLOBAL_CHECKBOX_I18N,lang),fieldColumnInfo.getTitle()));
                        break;
                    }else {
                        Map<String, Long> valueMap = checkBoxValueMap.get(skuAttributeId);
                        for (String value : checkBoxValues) {
                            if (!valueMap.containsKey(value)) {
                                log.info("DisposableProductImportHandler.validateImportExcel 校验 SKU跨境属性多选值,填写的属性值不存在 taskId={} rowNum={} ,field={} value={},globalValue={}", taskDTO.getTaskId(),rowNum,field,data.getString(field),value);
                                importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_SPU_GLOBAL_CHECKBOX_I18N,lang),fieldColumnInfo.getTitle()));
                                break;
                            }
                        }
                    }
                }
            }

            if (!importProductDTO.getValid()) {
                continue;
            }

            // 品牌校验
            String jsonValue = this.getValueStr(data,FIX_BRAND_ID);
            if (!brandIdSet.contains(jsonValue)) {
                log.info("DisposableProductImportHandler.validateImportExcel 校验 品牌不属于供应商 taskId={} rowNum={} ,field={} value={}", taskDTO.getTaskId(),rowNum,FIX_BRAND_ID,jsonValue);
                if (CountryConstant.COUNTRY_ZH.equals(sourceCountryCode)) {
                    importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_CN_NO_BRAND_I18N,lang),jsonValue));
                }else {
                    importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_SUPPLIER_NO_BRAND_I18N,lang),supplierCode, jsonValue));
                }
                continue;
            }

            // 类目校验
            String categoryValue = this.getValueStr(data, FIX_CATEGORY_ID);
            if (!fourCatIdSet.contains(categoryValue)) {
                log.info("DisposableProductImportHandler.validateImportExcel 校验 类目不属于供应商 taskId={} rowNum={} ,field={} value={}", taskDTO.getTaskId(),rowNum,FIX_CATEGORY_ID,categoryValue);
                if (CountryConstant.COUNTRY_ZH.equals(sourceCountryCode)) {
                    importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_CN_NO_CATEGORY_I18N,lang), categoryValue));
                }else {
                    importProductDTO.failed(String.format(this.getI18nMessageByKey(TEMPLATE_ERROR_SUPPLIER_NO_CATEGORY_I18N,lang),supplierCode, categoryValue));
                }
            }
        }
    }

    /**
     * 查询所有类目集合
     * @param importProductDTOList
     * @param taskDTO
     * @param batchParamVO
     * @return
     */
    private Set<String> queryFourCategorySet(List<ImportProductDTO> importProductDTOList, TaskDTO<ImportProductDTO> taskDTO, BatchParamVO batchParamVO) {
        List<CategoryTreeResDTO> fourCategoryResDTOList;
        Set<String> fourCatIdSet = new HashSet<>();
        if(Objects.nonNull(taskDTO.getTaskStrategyVO()) &&  taskDTO.getTaskStrategyVO().getBizType()  != TaskBizTypeEnum.INT_CREATE_PRODUCT_BATCH_IMPORT.getCode()) {
            if (StringUtils.isNotBlank(batchParamVO.getCategoryId())) {
                fourCategoryResDTOList = templateSupportService.queryFourCategoryResDTOList(batchParamVO);
                fourCatIdSet = Optional.ofNullable(fourCategoryResDTOList).orElseGet(ArrayList::new).stream().map(CategoryTreeResDTO::getCatId).map(String::valueOf).collect(Collectors.toSet());
            }else {
                for (ImportProductDTO importProductDTO : importProductDTOList) {
                    JSONObject data = importProductDTO.getData();
                    String categoryValue = this.getValueStr(data, FIX_CATEGORY_ID);
                    if (StringUtils.isBlank(categoryValue)) {
                        continue;
                    }
                    batchParamVO.setCategoryId(categoryValue);
                    fourCategoryResDTOList = templateSupportService.queryFourCategoryResDTOList(batchParamVO);
                    Set<String> queryCategoryIdSet = Optional.ofNullable(fourCategoryResDTOList).orElseGet(ArrayList::new).stream().map(CategoryTreeResDTO::getCatId).map(String::valueOf).collect(Collectors.toSet());
                    fourCatIdSet.addAll(queryCategoryIdSet);
                }
            }
        }
        return fourCatIdSet;
    }


    private void setSkuGlobalAttributeCheckboxMap(ImportProductDTO importProductDTO, Map<Long, GlobalAttributeApiResDTO> skuGlobalResMap) {
        if (MapUtils.isNotEmpty(skuGlobalResMap)) {
            Map<Long,Map<String,Long>> skuAttributeAndCheckBoxValueMap = Maps.newHashMap();
            Map<Long,Integer> skuAttributeAndInputTypeCheckTypeMap = Maps.newHashMap();
            for (Map.Entry<Long, GlobalAttributeApiResDTO> entry : skuGlobalResMap.entrySet()) {
                GlobalAttributeApiResDTO value = entry.getValue();
                if (Objects.isNull(value) || value.getAttributeInputType() != AttributeInputTypeEnum.CHECKBOX.getCode() || CollectionUtils.isEmpty(value.getAttributeValueList())) {
                    continue;
                }
                if (AttributeInputTypeEnum.CHECKBOX.getCode().equals(value.getAttributeInputType()) && CollectionUtils.isNotEmpty(value.getAttributeValueList())) {
                    List<GlobalAttributeValueApiResDTO> attributeValueList = value.getAttributeValueList();
                    Map<String,Long> valueMap = Maps.newHashMap();
                    for (GlobalAttributeValueApiResDTO valueApiResDTO : attributeValueList) {
                        valueMap.put(valueApiResDTO.getAttributeValueName(), valueApiResDTO.getAttributeValueId());
                    }
                    skuAttributeAndCheckBoxValueMap.put(entry.getKey(), valueMap);
                } else if (AttributeInputTypeEnum.TEXT.getCode().equals(value.getAttributeInputType())) {
                    skuAttributeAndInputTypeCheckTypeMap.put(entry.getKey(), value.getInputCheckType());
                }

            }

            importProductDTO.setSkuAttributeAndCheckBoxValueMap(skuAttributeAndCheckBoxValueMap);
            // 文本输入类型，录入数据类型校验
            log.info("DisposableProductImportHandler.validateImportExcel 校验 SKU跨境属性多选值 skuAttributeAndInputTypeCheckTypeMap={}",JSON.toJSONString(skuAttributeAndCheckBoxValueMap));
            if (MapUtils.isNotEmpty(importProductDTO.getTextCheckTypeMap())){
                importProductDTO.getTextCheckTypeMap().putAll(skuAttributeAndInputTypeCheckTypeMap);
            }else {
                importProductDTO.setTextCheckTypeMap(skuAttributeAndInputTypeCheckTypeMap);
            }
        }
    }


    private void setSpuGlobalAttributeCheckboxMap(ImportProductDTO importProductDTO, Map<Long, GlobalAttributeApiResDTO> spuGlobalResMap) {
        if (MapUtils.isNotEmpty(spuGlobalResMap)) {
            Map<Long,Map<String,Long>> spuAttributeAndCheckBoxValueMap = Maps.newHashMap();
            Map<Long,Integer> spuAttributeAndInputTypeCheckTypeMap = Maps.newHashMap();
            for (Map.Entry<Long, GlobalAttributeApiResDTO> entry : spuGlobalResMap.entrySet()) {
                GlobalAttributeApiResDTO value = entry.getValue();
                if (Objects.isNull(value) || AttributeInputTypeEnum.RADIO.getCode().equals(value.getAttributeInputType())) {
                    continue;
                }

                if (AttributeInputTypeEnum.CHECKBOX.getCode().equals(value.getAttributeInputType()) && CollectionUtils.isNotEmpty(value.getAttributeValueList())) {
                    List<GlobalAttributeValueApiResDTO> attributeValueList = value.getAttributeValueList();
                    Map<String, Long> valueMap = Maps.newHashMap();
                    for (GlobalAttributeValueApiResDTO valueApiResDTO : attributeValueList) {
                        valueMap.put(valueApiResDTO.getAttributeValueName(), valueApiResDTO.getAttributeValueId());
                    }
                    spuAttributeAndCheckBoxValueMap.put(entry.getKey(), valueMap);
                }else if(AttributeInputTypeEnum.TEXT.getCode().equals(value.getAttributeInputType())){
                    spuAttributeAndInputTypeCheckTypeMap.put(entry.getKey(), value.getInputCheckType());
                }
            }

            importProductDTO.setSpuAttributeAndCheckBoxValueMap(spuAttributeAndCheckBoxValueMap);
            // 文本输入类型，录入数据类型校验
            log.info("DisposableProductImportHandler.validateImportExcel 校验 SPU跨境属性多选值 spuAttributeAndInputTypeCheckTypeMap={}", JSON.toJSONString(spuAttributeAndInputTypeCheckTypeMap));
            importProductDTO.setTextCheckTypeMap(spuAttributeAndInputTypeCheckTypeMap);
        }
    }


    /**
     * 处理表格数据
     *
     * @param importProductDTOList
     * @param imageFileMap
     * @param taskDTO
     */
    private void handleExcelData(List<ImportProductDTO> importProductDTOList,Map<String, File> imageFileMap, TaskDTO<ImportProductDTO> taskDTO){

        if (CollectionUtils.isEmpty(importProductDTOList)) {
            return;
        }

        String reqParam = taskDTO.getTaskStrategyVO().getReqParam();
        if (StringUtils.isBlank(reqParam)){
            log.error("参数为空 reqParam={}",reqParam);
            return;
        }
        BatchParamVO batchParamVO = JSON.parseObject(reqParam, BatchParamVO.class);
        Integer bizType = taskDTO.getTaskStrategyVO().getBizType();
        if (TaskBizTypeEnum.CN_CREATE_PRODUCT_BATCH_IMPORT.getCode() == bizType || TaskBizTypeEnum.INT_CREATE_PRODUCT_BATCH_IMPORT.getCode() == bizType){
            batchParamVO = importProductDTOList.get(0).getBatchParamVO();
        }

        String pin = batchParamVO.getOperator();
        this.sourceCountryCode = batchParamVO.getSourceCountryCode();

        log.info("DisposableProductImportHandler.handleExcelData 解析数据开始,langList={}",JSON.toJSONString(langList));


        // 获取类目ID
        Set<Long> catIds = importProductDTOList.stream()
                .filter(ImportProductDTO::getValid)
                .map(ImportProductDTO::getData)
                .map(data -> this.getValueStr(data, FIX_CATEGORY_ID))
                .filter(StringUtils::isNotBlank)
                .map(Long::valueOf)
                .collect(Collectors.toSet());
        Map<Long,Map<Integer,PropertyApiDTO>> propertyApiDTOMap = new HashMap<>();
        if(bizType.equals(TaskBizTypeEnum.INT_CREATE_PRODUCT_BATCH_IMPORT.getCode())
                || bizType.equals(SupplierTaskBizTypeEnum.SUPPLIER_CREATE_PRODUCT_BATCH_IMPORT.getCode())){
            propertyApiDTOMap = rpcAttributeService.querySellAttrMap(catIds);
        }

        for (ImportProductDTO importProductDTO : importProductDTOList) {
            if (!importProductDTO.getValid()) {
                continue;
            }

            // 解析每个字段
            JSONObject data = importProductDTO.getData();

            SaveSpuApiDTO saveSpuApiDTO = new SaveSpuApiDTO();
            saveSpuApiDTO.setPin(pin);
            // SPU信息
            saveSpuApiDTO.setSpuVO(this.handleSpuApiDTO(imageFileMap, langList, data, batchParamVO));
            // SKU信息
            saveSpuApiDTO.setSkuVOList(this.getSaveSkuApiDTOS(batchParamVO, data,imageFileMap, propertyApiDTOMap,importProductDTO));
            // 商详
            this.handleDescription(langList, data, saveSpuApiDTO);
            // 扩展属性
            this.handExtendAttribute(importProductDTO, data, saveSpuApiDTO);
            // SPU跨境属性
            this.handSpuGlobalAttribute(importProductDTO, data, saveSpuApiDTO);
            // SKU跨境属性
            this.handSkuGlobalAttribute(importProductDTO, data, saveSpuApiDTO);
            // 设置创建商品入参
            importProductDTO.setSaveSpuApiDTO(saveSpuApiDTO);
        }
        taskDTO.setTarget(importProductDTOList);
    }




    /**
     * 从字段名中提取数字
     *
     * @param field 字段名，格式如：spuExtend_5_123_1_en   spuExtend_{comGroupId}_{attributeId}_{level}_{lang} 
     * 有属性组，size=3，无属性组，size=2
     * @return 提取的数字列表
     */
    private List<Long> extractNumbersFromField(String field) {
        List<Long> numbers = Lists.newArrayList();
        if (StringUtils.isBlank(field)) {
            return numbers;
        }

        String[] parts = field.split(Constant.UNDER_LINE);
        for (String part : parts) {
            if (NumberUtils.isDigits(part)) {
                numbers.add(Long.parseLong(part));
            }
        }
        return numbers;
    }

    /**
     * 处理导入商品的扩展属性。
     *
     * @param importProductDTO 导入商品DTO，包含扩展属性字段信息。
     * @param data 扩展属性的数据对象。
     * @param saveSpuApiDTO 保存SPU API DTO，用于存储处理后的扩展属性。
     */
    private void handExtendAttribute(ImportProductDTO importProductDTO, JSONObject data, SaveSpuApiDTO saveSpuApiDTO) {
        // 解析扩展属性开头  这里拿到了所有的扩展属性隐藏list，形如[spuExtend_5_123_en, spuExtend_5_123_zh, spuExtend_5_456_]，这里要处理是否包含属性组的情况，如果连续两个数字，证明包含属性组，否则属性组默认id为0，即不包含属性组
        List<String> extendFields = importProductDTO.getFields().stream().filter(k-> k.startsWith(SPU_EXTEND_I18N)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(extendFields)) {
            return;
        }
        // 单选扩展属性，用最后的字符串是否是下划线来区分是单选属性还是文本属性
        List<String> radioExtendAttributeList = extendFields.stream().filter(k -> k.endsWith(Constant.UNDER_LINE)).collect(Collectors.toList());
        // 文本扩展属性
        List<String> textExtendAttributeList = extendFields.stream().filter(k -> !k.endsWith(Constant.UNDER_LINE)).collect(Collectors.toList());
        log.info("DisposableProductImportHandler.handExtendAttribute 解析扩展属性，radioExtendAttributeList={},textExtendAttributeList={}",JSON.toJSONString(radioExtendAttributeList),JSON.toJSONString(textExtendAttributeList));
        List<PropertyApiDTO> propertyApiDTOList = getPropertyApiDTOList(data, radioExtendAttributeList, textExtendAttributeList);
        log.info("DisposableProductImportHandler.handExtendAttribute 解析扩展属性，propertyApiDTOList={}",JSON.toJSONString(propertyApiDTOList));
        // 根据level将propertyApiDTOList分别存储到spu或sku上
        List<PropertyApiDTO> spuPropertyList = Lists.newArrayList();
        List<PropertyApiDTO> skuPropertyList = Lists.newArrayList();
        
        for (PropertyApiDTO propertyApiDTO : propertyApiDTOList) {
            // 获取属性的level，默认为0
            Integer level = propertyApiDTO.getLevel();
            // 这里需要根据attributeId获取属性的level信息
            // 假设有方法可以获取属性信息，如果level=0则存储到spu，否则存储到sku
            if (level == Constant.DEFAULT_SPU_LEVEL) {
                spuPropertyList.add(propertyApiDTO);
            } else {
                skuPropertyList.add(propertyApiDTO);
            }
        }
        
        // 设置spu扩展属性
        saveSpuApiDTO.setStoreExtendPropertyList(spuPropertyList);
        
        // 设置sku扩展属性（只对第一个sku赋值）
        if (CollectionUtils.isNotEmpty(skuPropertyList) && 
            CollectionUtils.isNotEmpty(saveSpuApiDTO.getSkuVOList())) {
            saveSpuApiDTO.getSkuVOList().get(0).setStoreExtendPropertyList(skuPropertyList);
        }
    }

    /**
     * 从JSON数据中提取单选和文本扩展属性并转换为PropertyApiDTO列表
     * @param data 包含属性值的JSON对象
     * @param radioExtendAttributeList 单选扩展属性字段名列表
     * @param textExtendAttributeList 文本扩展属性字段名列表
     * @return 包含处理后的属性信息的PropertyApiDTO列表
     */
    public List<PropertyApiDTO> getPropertyApiDTOList(JSONObject data, List<String> radioExtendAttributeList, List<String> textExtendAttributeList) {
        // 单选扩展属性处理
        List<PropertyApiDTO> propertyApiDTOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(radioExtendAttributeList)) {
            for (String field : radioExtendAttributeList){
                PropertyApiDTO extendPropertyApiDTO = new PropertyApiDTO();
                // 单选扩展属性
                List<Long> longs = extractNumbersFromField(field);
                Long attributeId = null;
                Integer attributeGroupId = null;
                Integer level = null;
                if (longs.size()>2){
                    // 包含属性组
                    attributeGroupId = longs.get(0).intValue();
                    attributeId = longs.get(1);
                    level = longs.get(2).intValue();
                } else{
                    // 默认属性组id为0，即不包含属性组
                    attributeGroupId = Constant.DEFAULT_NO_COMGROUP_ID;
                    attributeId = longs.get(0);
                    level = longs.get(1).intValue();
                }
                // 设置属性组id
                extendPropertyApiDTO.setComGroupId(attributeGroupId);
                // 设置level, level=0表示spu扩展属性，否则表示sku扩展属性
                extendPropertyApiDTO.setLevel(level); 
                // 设置属性id
                extendPropertyApiDTO.setAttributeId(attributeId);
                // 获取属性值
                String value = this.getJSONValue(data, field);
                if (StringUtils.isBlank(value)) {
                    continue;
                }
                String extendValueId = ExcelUtils.getCellId(value);
                List<PropertyValueApiDTO> propertyValueVOList = Lists.newArrayList();
                if (StringUtils.isNotBlank(extendValueId)) {
                    PropertyValueApiDTO valueApiDTO = new PropertyValueApiDTO();
                    valueApiDTO.setAttributeId(attributeId);
                    valueApiDTO.setAttributeValueId(Long.parseLong(extendValueId));
                    valueApiDTO.setSelected(Boolean.TRUE);
                    propertyValueVOList.add(valueApiDTO);
                }
                extendPropertyApiDTO.setPropertyValueVOList(propertyValueVOList);
                propertyApiDTOList.add(extendPropertyApiDTO);
            }
        }
        // 文本扩展属性处理
        if (CollectionUtils.isNotEmpty(textExtendAttributeList)) {
            Map<Long,Map<String,String>>  textExtendAttibuteMap = Maps.newHashMap();
            Map<Long,PropertyApiDTO> attIdPropertyMap = new HashMap<>();
            for (String field : textExtendAttributeList){

                // 表格填写数据
                String jsonValue = this.getJSONValue(data, field);
                if (StringUtils.isBlank(jsonValue)) {
                    continue;
                }
                // 包含属性组spuExtend_5_123_1_ZH，不包含属性组spuExtend_123_1_ZH
                String lang = "";
                String[] split = field.split(Constant.UNDER_LINE);
                List<Long> longs = extractNumbersFromField(field);
                Long attributeId = null;
                Integer attributeGroupId = null;
                Integer level = null;
                if(extractNumbersFromField(field).size() > 2) {
                    // 包含属性组
                    attributeGroupId = longs.get(0).intValue();
                    attributeId = longs.get(1);
                    level = longs.get(2).intValue();
                    lang = split[4];
                } else{
                    // 不包含属性组
                    attributeGroupId = Constant.DEFAULT_NO_COMGROUP_ID;
                    attributeId = longs.get(0);
                    level = longs.get(1).intValue();
                    lang = split[3];
                }
                PropertyApiDTO propertyApiDTO = new PropertyApiDTO();
                propertyApiDTO.setComGroupId(attributeGroupId);
                propertyApiDTO.setLevel(level);
                propertyApiDTO.setAttributeId(attributeId);
                attIdPropertyMap.put(attributeId, propertyApiDTO);
                // 属性id、属性组id、lang值
                if (!textExtendAttibuteMap.containsKey(attributeId)) {
                    Map<String,String> valueLangMap = Maps.newHashMap();
                    valueLangMap.put(lang, jsonValue);
                    textExtendAttibuteMap.put(attributeId, valueLangMap);
                }else if (!textExtendAttibuteMap.get(attributeId).containsKey(lang)) {
                    textExtendAttibuteMap.get(attributeId).put(lang, jsonValue);
                }
            }

            if (MapUtils.isNotEmpty(textExtendAttibuteMap)) {
                textExtendAttibuteMap.forEach((k,valueMap)-> {
                    PropertyApiDTO propertyApiDTO = new PropertyApiDTO();

                    long attributeId = k;
                    propertyApiDTO.setAttributeId(attributeId);
                    // 设置属性组id和level
                    if(attIdPropertyMap.get(k)!=null) {
                        propertyApiDTO.setComGroupId(attIdPropertyMap.get(k).getComGroupId());
                        propertyApiDTO.setLevel(attIdPropertyMap.get(k).getLevel());
                    }

                    List<PropertyValueApiDTO> propertyValueVOList = Lists.newArrayList();
                    valueMap.forEach((lang,value)-> {
                        PropertyValueApiDTO valueApiDTO = new PropertyValueApiDTO();
                        valueApiDTO.setLang(lang);
                        valueApiDTO.setAttributeId(attributeId);
                        valueApiDTO.setAttributeValueName(value);
                        propertyValueVOList.add(valueApiDTO);
                    });
                    propertyApiDTO.setPropertyValueVOList(propertyValueVOList);
                    propertyApiDTOList.add(propertyApiDTO);
                });
            }
        }
        return propertyApiDTOList;
    }

    private void handleDescription(List<String> langList, JSONObject data, SaveSpuApiDTO saveSpuApiDTO) {
        Map<String,String> pcDescriptionMap = Maps.newHashMap();
        for (String lang : langList) {
            // 详情
            String description = String.format(ProductConstant.SPU_DESCRIPTION_PREFIX, lang);
            pcDescriptionMap.put(lang, this.getJSONValue(data, description));
        }
        saveSpuApiDTO.setPcDescriptionMap(pcDescriptionMap);
    }

    protected SpuApiDTO handleSpuApiDTO(Map<String, File> imageFileMap, List<String> langList, JSONObject data, BatchParamVO batchParamVO) {
        log.info("DisposableProductImportHandler.handleSpuApiDTO 解析SPU数据开始");
        SpuApiDTO spuVO = new SpuApiDTO();

        String categoryId = this.getValueStr(data, FIX_CATEGORY_ID);
        if (StringUtils.isNotBlank(categoryId)){
            spuVO.setCatId(Long.parseLong(categoryId));
        }
        String brandId = this.getValueStr(data, FIX_BRAND_ID);
        if (StringUtils.isNotBlank(brandId) && StringUtils.isNumeric(brandId)){
            spuVO.setBrandId(Long.parseLong(brandId));
        }
        // 多语言名称、规格等内容处理
        this.handleSpuLangList(langList, data, spuVO);
        spuVO.setSpecification(this.getJSONValue(data, SPU_SPECIFICATION_PREFIX));
        spuVO.setSourceCountryCode(batchParamVO.getSourceCountryCode());
        spuVO.setAttributeScope(batchParamVO.getAttributeScope());
        spuVO.setVendorCode(StringUtils.isNotBlank(batchParamVO.getSupplierCode()) ? batchParamVO.getSupplierCode() : this.getJSONValue(data,FIX_SUPPLIER_CODE));
        spuVO.setSpuStatus(SpuStatusEnum.ON_SALE.getStatus());
        String vendorSpuId = this.getJSONValue(data, FIX_SUPPLIER_SPUID);
        spuVO.setVendorSpuId(vendorSpuId);
        log.info("DisposableProductImportHandler.handleSpuApiDTO 解析SPU数据结束 spuVO={}",JSON.toJSONString(spuVO));
        // 处理图片
        this.handleSpuImage(imageFileMap, data, spuVO);
        spuVO.setBuyer(batchParamVO.getOperator());
        spuVO.setCreator(batchParamVO.getOperator());
        spuVO.setUpdater(batchParamVO.getOperator());
        spuVO.setCreateTime(new Date());
        spuVO.setUpdateTime(new Date());
        spuVO.setYn(YnEnum.YES.getCode());
        spuVO.setSystemCode(batchParamVO.getSystemCode());
        spuVO.setAuditStatus(SpuAuditStatusEnum.WAITING_APPROVED.getCode());
        spuVO.setIdentityFlag(batchParamVO.getIdentityFlag()); // 1: 供应商 2:采销
        String saleUnit = this.getJSONValue(data, FIX_SALES_UNIT);
        String saleUnitId = ExcelUtils.getCellId(saleUnit);
        spuVO.setSaleUnit(StringUtils.isBlank(saleUnitId) ? null : Integer.valueOf(saleUnitId));
        spuVO.setSpuInterAttribute("");
        log.info("DisposableProductImportHandler.handleSpuApiDTO 解析SPU数据结束 spuVO={}",JSON.toJSONString(spuVO));
        return spuVO;
    }

    private void handleSpuLangList(List<String> langList, JSONObject data, SpuApiDTO spuVO) {
         log.info("DisposableProductImportHandler.handleSpuLangList 解析翻译多语言开始 spuVO={},langList={}",JSON.toJSONString(spuVO),JSON.toJSONString(langList));
        List<SpuLangApiDTO> spuLangExtendVOList = Lists.newArrayList();
        // 先将固定列的中文名称和中文规格自动识别语种翻译成目标语种列表
        String spuZhName = String.format(ProductConstant.SPU_NAME_PREFIX, LangConstant.LANG_ZH);
        String spuZHQualifier = String.format(ProductConstant.SPU_QUALIFIER_PREFIX, LangConstant.LANG_ZH);
        String spuNameValueStr = this.getJSONValue(data, spuZhName);
        Map<String, String> spuNameMap = this.getSpuNameMap(spuNameValueStr,spuVO.getBrandId(),langList);
        String spuZhQualiferValue = this.getJSONValue(data, spuZHQualifier);
        log.info("DisposableProductImportHandler.handleSpuLangList 解析翻译多语言 翻译文案 spuZhName={},spuZHQualifier={}",spuNameValueStr,spuZhQualiferValue);
        Map<String, String> spuQualifierMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(spuZhQualiferValue)) {
            spuQualifierMap=  translateRpcService.translateMultiLang(new MultiTranslateReqDTO(spuZhQualiferValue, null, langList,null));
        }

        // 型号
        String specification = this.getJSONValue(data, SPU_SPECIFICATION_PREFIX);

        for (String lang : langList) {
            String spuName = String.format(ProductConstant.SPU_NAME_PREFIX, lang);
            String spuQualifier = String.format(ProductConstant.SPU_QUALIFIER_PREFIX, lang);
            String spuKeyWord = String.format(ProductConstant.SPU_KEY_WORD_PREFIX, lang);

            SpuLangApiDTO spuLangApiDTO = new SpuLangApiDTO();
            spuLangApiDTO.setLang(lang);
            if(LangConstant.LANG_ZH.equals(lang)){
                spuLangApiDTO.setSpuTitle(spuNameMap.getOrDefault(lang,""));
            } else {
                String spuNameValue = this.getJSONValue(data, spuName);
                spuLangApiDTO.setSpuTitle(StringUtils.isNotBlank(spuNameValue) ? this.convertSpuName(spuNameValue,spuVO.getBrandId(),lang) :spuNameMap.getOrDefault(lang,""));
            }
            String spuQualifierValue = this.getJSONValue(data, spuQualifier);
            spuLangApiDTO.setQualifier(StringUtils.isNotBlank(spuQualifierValue) ? spuQualifierValue : spuQualifierMap.getOrDefault(lang,""));
            spuLangApiDTO.setKeyPhrases(this.getJSONValue(data, spuKeyWord));

            // 拼接商品名称、型号、规格，中间用空格分隔
            this.appendSpuTitle(spuLangApiDTO, specification);

            spuLangExtendVOList.add(spuLangApiDTO);
        }
        spuVO.setSpuLangExtendVOList(spuLangExtendVOList);
        log.info("DisposableProductImportHandler.handleSpuLangList 解析翻译多语言结束 spuLangExtendVOList={},langList={}",JSON.toJSONString(spuLangExtendVOList),JSON.toJSONString(langList));
    }

    private void appendSpuTitle(SpuLangApiDTO spuLangApiDTO, String specification) {
        String spuAppendName = "";
        if (StringUtils.isNotBlank(spuLangApiDTO.getSpuTitle())) {
            spuAppendName = spuAppendName + spuLangApiDTO.getSpuTitle();
        }
        if (StringUtils.isNotBlank(spuAppendName) && StringUtils.isNotBlank(specification)) {
            spuAppendName = spuAppendName + " " + specification;
        }
        if (StringUtils.isNotBlank(spuAppendName) && StringUtils.isNotBlank(spuLangApiDTO.getQualifier())) {
            spuAppendName = spuAppendName + " " + spuLangApiDTO.getQualifier();
        }
        if (StringUtils.isNotBlank(spuAppendName)) {
            spuLangApiDTO.setSpuTitle(spuAppendName);
        }
    }

    /**
     * 处理SPU图片信息并上传到服务器。
     * @param imageFileMap 图片文件映射，key为图片URL，value为对应的File对象。
     * @param data JSONObject类型的数据，包含SPU的主图和详情图信息。
     * @param spuVO SpuApiDTO类型的对象，用于存储处理后的SPU图片信息。
     */
    private void handleSpuImage(Map<String, File> imageFileMap, JSONObject data, SpuApiDTO spuVO) {
        String mainImage = this.getJSONValue(data, FIX_MAIN_IMAGE_URL);
        if (StringUtils.isNotBlank(mainImage)
                && imageFileMap.containsKey(mainImage.toUpperCase())) {
            spuVO.setMainImg(this.uploadImage(imageFileMap.get(mainImage.toUpperCase()), FileTypeEnum.SPU_IMAGE.getCode()));
        }

        // 跨境已经设置的主图
        if (StringUtils.isNotBlank(mainImage)
                && mainImage.startsWith(Constant.HTTP)) {
            spuVO.setMainImg(mainImage);
        }

        List<String> detailImageList = Lists.newArrayList();
        for (int i = 1; i <= 4; i++) {
            String key  = FIX_SPU_IMAGE_URL_PREFIX + i;
            String detailImage = this.getJSONValue(data, key);
            if (StringUtils.isNotBlank(detailImage) && imageFileMap.containsKey(detailImage.toUpperCase()) && null != imageFileMap.get(detailImage.toUpperCase())) {
                String url = this.uploadImage(imageFileMap.get(detailImage.toUpperCase()), FileTypeEnum.SPU_IMAGE.getCode());
                if (StringUtils.isNotBlank(url)) {
                    detailImageList.add(url);
                }
            }
        }
        spuVO.setDetailImgList(detailImageList);
    }

    private List<SaveSkuApiDTO> getSaveSkuApiDTOS(BatchParamVO batchParamVO, JSONObject data,Map<String, File> imageFileMap
            ,Map<Long,Map<Integer,PropertyApiDTO>> propertyApiDTOMap,ImportProductDTO importProductDTO) {
        List<SaveSkuApiDTO> skuApiDTOList = Lists.newArrayList();
        SaveSkuApiDTO saveSkuApiDTO = getSaveSkuApiDTO(batchParamVO, data, imageFileMap, importProductDTO,propertyApiDTOMap);
        skuApiDTOList.add(saveSkuApiDTO);
        return skuApiDTOList;
    }

    protected SaveSkuApiDTO getSaveSkuApiDTO(BatchParamVO batchParamVO, JSONObject data, Map<String, File> imageFileMap, ImportProductDTO importProductDTO ,Map<Long,Map<Integer,PropertyApiDTO>> propertyApiDTOMap) {
        SaveSkuApiDTO saveSkuApiDTO = new SaveSkuApiDTO();
        String jdSkuID = this.getJSONValue(data,JD_SKU_ID_PREFIX);
        if(StringUtils.isNotBlank(jdSkuID)){
            saveSkuApiDTO.setJdSkuId(Long.valueOf(this.getJSONValue(data,JD_SKU_ID_PREFIX)));
        }
        saveSkuApiDTO.setVendorCode(batchParamVO.getSupplierCode());
        saveSkuApiDTO.setUpcCode(this.getJSONValue(data,FIX_BARCODE));
        saveSkuApiDTO.setSourceCountryCode(batchParamVO.getSourceCountryCode());
        String categoryId = this.getValueStr(data, FIX_CATEGORY_ID);
        if (StringUtils.isNotBlank(categoryId)) {
             saveSkuApiDTO.setCatId(Long.parseLong(categoryId));
        }
        String brandId = this.getValueStr(data, FIX_BRAND_ID);
        if (StringUtils.isNotBlank(brandId) && StringUtils.isNumeric(brandId)){
            saveSkuApiDTO.setBrandId(Long.parseLong(brandId));
        }
        saveSkuApiDTO.setWeight(this.getJSONValue(data,FIX_GROSS_WEIGHT));
        saveSkuApiDTO.setLength(this.getJSONValue(data,FIX_LENGTH));
        saveSkuApiDTO.setWidth(this.getJSONValue(data,FIX_WIDTH));
        saveSkuApiDTO.setHeight(this.getJSONValue(data,FIX_HEIGHT));
        saveSkuApiDTO.setHsCode(this.getJSONValue(data,FIX_HSCODE));
        saveSkuApiDTO.setJdVendorCode(this.getJSONValue(data,FIX_JD_SUPPLIER_CODE));
        saveSkuApiDTO.setCreator(batchParamVO.getOperator());
        saveSkuApiDTO.setUpdater(batchParamVO.getOperator());
        saveSkuApiDTO.setCreateTime(new Date());
        saveSkuApiDTO.setUpdateTime(new Date());
        saveSkuApiDTO.setYn(YnEnum.YES.getCode());
        if(CountryConstant.COUNTRY_ZH.equals(batchParamVO.getSourceCountryCode())){
            saveSkuApiDTO.setVendorTradeType(TradeTypeConstant.EXW);
            saveSkuApiDTO.setCustomerTradeType(TradeTypeConstant.EXW);
        } else {
            saveSkuApiDTO.setVendorTradeType(TradeTypeConstant.BD);
            saveSkuApiDTO.setCustomerTradeType(TradeTypeConstant.BD);
        }

        if (CountryConstant.COUNTRY_BR.equals(batchParamVO.getSourceCountryCode())){
            String purchasePrice = this.getJSONValue(data, FIX_UNTAXED_PURCHASE_PRICE);
            saveSkuApiDTO.setPurchasePrice(CommonUtils.removeThousandSeparator(purchasePrice));
        }

        // 越南品录入含税采购价，未税采购价通过计算得到;其他国家直接解析未税采购价
        if (CollectionUtils.isNotEmpty(countryCodeSets) && countryCodeSets.contains(batchParamVO.getSourceCountryCode())) {
            String taxPurchasePrice = this.getJSONValue(data, FIX_TAXED_PURCHASE_PRICE);
            saveSkuApiDTO.setTaxPurchasePrice(CommonUtils.removeThousandSeparator(taxPurchasePrice));
        }else {
            String purchasePrice = this.getJSONValue(data, FIX_UNTAXED_PURCHASE_PRICE);
            saveSkuApiDTO.setPurchasePrice(CommonUtils.removeThousandSeparator(purchasePrice));
        }
        String salePrice = this.getJSONValue(data, FIX_UNTAXED_SALES_PRICE);
        if (StringUtils.isNotBlank(salePrice)) {
            saveSkuApiDTO.setSalePrice(CommonUtils.removeThousandSeparator(salePrice));
        }
        saveSkuApiDTO.setStockNum(this.getJSONValue(data,FIX_STOCK));
        saveSkuApiDTO.setSkuOnlineTime(new Date());
        saveSkuApiDTO.setSkuStatus(SpuStatusEnum.ON_SALE.getStatus());
        String moq = this.getJSONValue(data, FIX_MOQ);
        saveSkuApiDTO.setMoq(StringUtils.isBlank(moq) ? null : Integer.valueOf(moq));
        String productCycle = this.getJSONValue(data, FIX_PRODUCTION_CYCLE);
        saveSkuApiDTO.setProductionCycle(StringUtils.isNotBlank(productCycle) ? new BigDecimal(productCycle) : null);
        saveSkuApiDTO.setOriginCountry(this.getJSONValue(data,FIX_ORIGIN));
        saveSkuApiDTO.setSkuInterPropertyList(Lists.newArrayList());
        saveSkuApiDTO.setSkuCertificateVOList(Lists.newArrayList());
        saveSkuApiDTO.setSpecification(this.getJSONValue(data,ProductConstant.SPU_SPECIFICATION_PREFIX));
        saveSkuApiDTO.setCreateTime(new Date());
        saveSkuApiDTO.setUpdateTime(new Date());
        // SKU主图
        this.handSkuImage(data, imageFileMap, saveSkuApiDTO);
        // 销售属性
//        saveSkuApiDTO.setStoreSalePropertyList(this.handleSaleAttributeList(data, importProductDTO));
        saveSkuApiDTO.setStoreSalePropertyList(this.handleSaleAttributes(saveSkuApiDTO,propertyApiDTOMap));
        String vendorSkuId = this.getJSONValue(data, FIX_SUPPLIER_SKUID);
        log.info("DisposableProductImportHandler.getSaveSkuApiDTO 解析固定列， 供应商SKUID 供应商简码={} vendorSkuId={}",batchParamVO.getSupplierCode(), vendorSkuId);
        if(StringUtils.isNotBlank(vendorSkuId)){
            saveSkuApiDTO.setVendorSkuIdNew(vendorSkuId);
        }
        return saveSkuApiDTO;
    }

    /**
     * 处理销售属性。
     * @param saveSkuApiDTO 保存 SKU 的 API DTO。
     * @param propertyApiDTOMap 属性 API DTO 映射。
     * @return 处理后的销售属性列表。
     */
    private List<PropertyValueApiDTO> handleSaleAttributes(SaveSkuApiDTO saveSkuApiDTO, Map<Long,Map<Integer,PropertyApiDTO>> propertyApiDTOMap) {
        log.info("DisposableProductImportHandler.handleSaleAttributes 解析销售属性开始，data={}",JSON.toJSONString(saveSkuApiDTO));
        if(StringUtils.equals(CountryConstant.COUNTRY_ZH,saveSkuApiDTO.getSourceCountryCode())){
            log.error("DisposableProductImportHandler.handleSaleAttributes CN");
            return null;
        }

        if(saveSkuApiDTO.getCatId() == null){
            log.error("DisposableProductImportHandler.handleSaleAttributes catId is null");
            return null;
        }
        if(MapUtils.isEmpty(propertyApiDTOMap)){
            log.error("DisposableProductImportHandler.handleSaleAttributes propertyApiDTOMap is null");
            return null;
        }
        Map<Integer,PropertyApiDTO> catPropertyApiDTOMap = propertyApiDTOMap.get(saveSkuApiDTO.getCatId());
        if(MapUtils.isEmpty(catPropertyApiDTOMap)){
            log.error("DisposableProductImportHandler.handleSaleAttributes catPropertyApiDTOMap is null");
            return null;
        }

        List<BaseLangDTO> langDTOS = JSONArray.parseArray(saleAttrDefault,BaseLangDTO.class);

        List<PropertyValueApiDTO> results = catPropertyApiDTOMap.values().stream()
                .map(propertyApiDTO -> {
                    PropertyValueApiDTO value = new PropertyValueApiDTO();
                    value.setAttributeId(propertyApiDTO.getAttributeId());
                    value.setSort(YnEnum.YES.getCode());
                    value.setLangList(langDTOS);
                    return value;
                })
                .collect(Collectors.toList());

        log.info("DisposableProductImportHandler.handleSaleAttributes 解析销售属性结束，storeSalePropertyList={}",JSON.toJSONString(results));
        return results;
    }

//    private List<PropertyValueApiDTO> handleSaleAttributeList(JSONObject data, ImportProductDTO importProductDTO) {
//        log.info("DisposableProductImportHandler.handleSaleAttributeList 解析销售属性开始，data={}",JSON.toJSONString(data));
//        List<PropertyValueApiDTO> storeSalePropertyList = Lists.newArrayList();
//        List<String> saleAttrList = Lists.newArrayList();
//        List<String> fields = importProductDTO.getFields();
//        for (String field : fields) {
//            if (field.startsWith(SALE_ATTRIBUTE_I18N)){
//                saleAttrList.add(field);
//            }
//        }
//
//        //  如果销售属性为空，则获取类目上的其他，没有则报错
//        if (CollectionUtils.isEmpty(saleAttrList) && MapUtils.isEmpty(importProductDTO.getSaleAttributeAndValueMap())) {
//            // 处理 其他
//            log.info("DisposableProductImportHandler.handleSaleAttributeList 销售属性表头是空,fields={}",JSON.toJSONString(fields));
//            importProductDTO.failed("类目没有销售属性");
//        }else {
//            for (String saleAtt : saleAttrList) {
//                PropertyValueApiDTO valueApiDTO = new PropertyValueApiDTO();
//                // 截取销售属性ID
//                String saleAttributeId = saleAtt.substring(saleAtt.lastIndexOf(Constant.UNDER_LINE) + 1);
//                valueApiDTO.setAttributeId(Long.parseLong(saleAttributeId));
//                // 截取销售属性值
//                String saleValue = this.getJSONValue(data, saleAtt);
//                if (StringUtils.isBlank(saleValue)) {
//                    continue;
//                }
//                String attributeValueId = ExcelUtils.getCellId(saleValue);
//                if (StringUtils.isNotBlank(attributeValueId)){
//                    valueApiDTO.setAttributeValueId(Long.parseLong(attributeValueId));
//                }
//                storeSalePropertyList.add(valueApiDTO);
//            }
//            // 没填销售属性且类目没有其他属性时报错
//            if (CollectionUtils.isEmpty(storeSalePropertyList)) {
//                Map<Long, Set<Long>> saleAttributeAndValueMap = importProductDTO.getSaleAttributeAndValueMap();
//                if (MapUtils.isEmpty(importProductDTO.getSaleAttributeAndValueMap()) || !saleAttributeAndValueMap.containsKey(OTHER_SALE_ATTRIBUTE_ID)){
//                    log.info("DisposableProductImportHandler.handleSaleAttributeList 销售属性值为空");
//                    importProductDTO.failed("类目没有销售属性");
//                }else if (saleAttributeAndValueMap.containsKey(OTHER_SALE_ATTRIBUTE_ID)){
//                    PropertyValueApiDTO valueApiDTO = new PropertyValueApiDTO();
//                    // 销售属性ID
//                    valueApiDTO.setAttributeId(OTHER_SALE_ATTRIBUTE_ID);
//                    valueApiDTO.setAttributeValueId(OTHER_SALE_ATTRIBUTE_VALUE_ID);
//                    storeSalePropertyList.add(valueApiDTO);
//                }
//            }
//        }
//
//        log.info("DisposableProductImportHandler.handleSaleAttributeList 解析销售属性结束，storeSalePropertyList={}",JSON.toJSONString(storeSalePropertyList));
//        return storeSalePropertyList;
//    }

    /**
     * 处理 SKU 图片并上传到服务器。
     * @param data JSON 对象，包含 SKU 主图信息。
     * @param imageFileMap 图片文件映射表，用于查找 SKU 主图文件。
     * @param saveSkuApiDTO 保存 SKU API DTO，用于设置上传后的 SKU 主图 URL。
     */
    private void handSkuImage(JSONObject data, Map<String, File> imageFileMap, SaveSkuApiDTO saveSkuApiDTO) {
        String skuMainImg = this.getJSONValue(data, FIX_SKU_IMAGE);
        if (StringUtils.isNotBlank(skuMainImg) && imageFileMap.containsKey(skuMainImg.toUpperCase())) {
            String url = this.uploadImage(imageFileMap.get(skuMainImg.toUpperCase()), FileTypeEnum.SKU_IMAGE.getCode());
            if (StringUtils.isNotBlank(url)) {
                saveSkuApiDTO.setMainImg(url);
            }
        }

        // 跨境已经设置的主图
        if (StringUtils.isNotBlank(skuMainImg)
                && skuMainImg.startsWith(Constant.HTTP)) {
            saveSkuApiDTO.setMainImg(skuMainImg);
        }
    }

    protected String getJSONValue(JSONObject data, String spuQualifier) {
        return data.containsKey(spuQualifier) ? data.getString(spuQualifier) : "";
    }

    @Override
    public void run(TaskDTO<ImportProductDTO> task) {
        List<ImportProductDTO> productDTOList = task.getTarget();
        log.info("DisposableProductImportHandler.run 执行任务，任务ID：{}", task.getTaskId());
        if (CollectionUtils.isEmpty(productDTOList)) {
            return;
        }

        for (ImportProductDTO importProductDTO : productDTOList) {
            log.info("DisposableProductImportHandler.run 执行任务，任务ID：{} valid={} result={} data={}", task.getTaskId(),importProductDTO.getValid(), importProductDTO.getResult(),JSON.toJSONString(importProductDTO.getData()));
            if (!importProductDTO.getValid()) {
                continue;
            }

            // 保存商品信息
            DataResponse<Long> dataResponse = rpcSpuService.submit(importProductDTO.getSaveSpuApiDTO());

            if (!dataResponse.getSuccess()) {
                importProductDTO.failed(dataResponse.getMessage());
                log.error("DisposableProductImportHandler.run taskId={},message={} saveApiDto={}",task.getTaskId(),dataResponse.getMessage(),JSON.toJSONString(importProductDTO.getSaveSpuApiDTO()));
                continue;
            }

            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            Long spuId = dataResponse.getData();
            Map<String, SkuBaseInfoApiDTO> skuInfoMap = skuRpcService.querySkuInfoBySpuId(spuId);
            if(MapUtils.isNotEmpty(skuInfoMap)){
                String skuID = skuInfoMap.keySet().iterator().next();
                Long mkuID = mkuRpcService.queryMkuId(skuID);
                importProductDTO.setSkuId(skuID);
                importProductDTO.setMkuId(String.valueOf(mkuID));
            }
            importProductDTO.setSpuId(spuId);
            // 设置spuId
            importProductDTO.setResult(String.valueOf(spuId));
        }

        // 主动报警
        if (productDTOList.stream().filter(importProductDTO -> !importProductDTO.getValid()).anyMatch(dto -> "提交商品失败".equals(dto.getResult()))){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_PRODUCT_SUBMIT,String.format("【%s】【%s】商品发品失败 任务ID:[%s] ", LevelCode.P3.getMessage(),env,task.getTaskId()));
        }
    }


    @Override
    public void export(TaskDTO<ImportProductDTO> task) {
        String reqFileName = FileUtil.mainName(task.getTaskStrategyVO().getReqFileName());
        // 将结果写入到文件中
        String outputFolderPathTemp = outputFolderPath + File.separator + System.nanoTime();
        try {
            TaskStrategyVO taskStrategyVO = task.getTaskStrategyVO();
            TaskStrategyVO taskPo = this.getStrategyService(taskStrategyVO.getTaskStrategyEnum()).getTask(task.getTaskId());
            if (Objects.nonNull(taskPo) && TaskStatusEnum.FAILURE.equals(taskPo.getStatus())){
                log.info("DisposableProductImportHandler.export 任务已经失败，跳过执行。 taskId={}" , task.getTaskId());
                return;
            }
            log.info("DisposableProductImportHandler.export 生成结果文件，任务ID：{},文件地址:{}", task.getTaskId(), task.getTaskStrategyVO().getReqFileUrl());
            if (CollectionUtils.isEmpty(task.getTarget())) {
                log.info("DisposableProductImportHandler.export 空任务{},跳过执行", task.getTaskId());
                this.getTaskService().update(new TaskStrategyVO(task, TaskStatusEnum.SUCCESS));
                return;
            }
            // 下载上传数据
            //task.setTargetInputFile(s3Utils.download(task.getTaskStrategyVO().getReqFileUrl()));
            this.unzip(task.getTaskStrategyVO().getReqFileUrl(), outputFolderPathTemp);

            // 读取解压后的数据
            Pair<File, Map<String, File>> fileMapPair = this.readFiles(outputFolderPathTemp, reqFileName);
            if (null == fileMapPair.getLeft()){
                super.errHandle(task, new ZipException("批量发品没有EXCEL文件"));
                return;
            }
            // 文件流转为文档
            XSSFWorkbook workbook = new XSSFWorkbook(fileMapPair.getLeft());
            // 结果写入文档最后一列
            this.writeResultToExcel(workbook, task.getTarget());
            // 生成结果文件，并上传到云存储，更新任务状态为成功
            this.exportResultExcel(task, workbook);
        } catch (Exception e) {
            log.error("【系统异常】DisposableProductImportHandler.export 结果内容写入表格中发生异常,taskId={}", task.getTaskId(), e);
            log.error("DisposableProductImportHandler.export 结果内容写入表格中发生异常,taskId={}", task.getTaskId(), e);
            this.getTaskService().update(new TaskStrategyVO(task, TaskStatusEnum.FAILURE));
        }finally {
            ZipUtils.deleteFile(new File(outputFolderPathTemp));
        }
    }

    private void exportResultExcel(TaskDTO<ImportProductDTO> task, XSSFWorkbook workbook) {
        String resultName = RESULT_FOLDER + task.getTaskStrategyVO().getBizType() + Constant.UNDER_LINE + System.currentTimeMillis() + ".xlsx";
        try (ByteArrayOutputStream targetOutputStream = new ByteArrayOutputStream()) {
            workbook.write(targetOutputStream);
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(targetOutputStream.toByteArray())) {
                task.setResultUrl(s3Utils.upload(inputStream, FileTypeEnum.BATCH_FILE.getCode(), resultName));
                this.getTaskService().update(new TaskStrategyVO(task, TaskStatusEnum.SUCCESS));
            }
        } catch (Exception e) {
            log.error("DisposableProductImportHandler.exportResultExcel error, target:{} ", task.getTaskId(), e);
            throw new TaskExportException(task.getTaskId() + e.getMessage());
        } finally {
            log.info("DisposableProductImportHandler.exportResultExcel 任务「{}」执行完毕:{},文件地址:{}", task.getTaskId(), task.getOssPutMd5(), task.getResultUrl());
        }
    }

    private void writeResultToExcel(XSSFWorkbook workbook, List<ImportProductDTO> importVnSpuExcelDTOList) {
        int size = importVnSpuExcelDTOList.size();
        log.info("DisposableProductImportHandler.writeResultToExcel 将结果写入文件,数据条数:{}", size);
        XSSFSheet sheetAt = workbook.getSheetAt(0);
        log.info("DisposableProductImportHandler.writeResultToExcel 将结果写入文件,sheet名:{}", sheetAt.getSheetName());
        int lastRowNum = sheetAt.getLastRowNum();
        log.info("DisposableProductImportHandler.writeResultToExcel 将结果写入文件1,lastRowNum:{}", lastRowNum);
        lastRowNum = Math.min(lastRowNum,size + 2);
        log.info("DisposableProductImportHandler.writeResultToExcel 将结果写入文件2,lastRowNum:{}", lastRowNum);
        // 取行数最小值
        ListIterator<ImportProductDTO> importProductDTOListIterator = importVnSpuExcelDTOList.listIterator();
        short lastRow = 0;
        for (int i = 0; i <= lastRowNum; i++) {
            XSSFRow row = sheetAt.getRow(i);
            if (Objects.isNull(row)){
                log.info("DisposableProductImportHandler.writeResultToExcel 第{}行数据为空,跳过", i);
                continue;
            }
            short lastCellNum =  i <=1 ?  row.getLastCellNum() : lastRow;
            XSSFCell resultColumn = row.createCell(lastCellNum);
            XSSFCell skuIdColumn = row.createCell(lastCellNum+1);
            XSSFCell mkuIdColumn = row.createCell(lastCellNum+2);
            if (i == 1) {
                lastRow = lastCellNum;
                XSSFFont font = workbook.createFont();
                font.setFontName(ExcelUtils.FONT_NAME);
                font.setBold(Boolean.TRUE);
                font.setColor(IndexedColors.BLACK.getIndex());
                XSSFCellStyle resultStyle = resultColumn.getCellStyle();
                resultStyle.setFont(font);
                resultColumn.setCellStyle(resultStyle);
                resultColumn.setCellValue("Result");

                XSSFCellStyle skuStyle = skuIdColumn.getCellStyle();
                skuStyle.setFont(font);
                skuIdColumn.setCellStyle(skuStyle);
                skuIdColumn.setCellValue("SKU ID");

                XSSFCellStyle mkuStyle = mkuIdColumn.getCellStyle();
                mkuStyle.setFont(font);
                mkuIdColumn.setCellStyle(mkuStyle);
                mkuIdColumn.setCellValue("MKU ID");

            } else if (i > 2 ) { // 跳过表头和舍弃空行
                // 结果写入单元格
                if(importProductDTOListIterator.hasNext()){
                    ImportProductDTO importProductDTO = importProductDTOListIterator.next();
                    //ImportProductDTO importProductDTO = importVnSpuExcelDTOList.get(i-3);
                    log.info("DisposableProductImportHandler.writeResultToExcel 将结果写入文件,i={},result={}", i, importProductDTO.getResult());
                    resultColumn.setCellValue(importProductDTO.getResult());
                    skuIdColumn.setCellValue(importProductDTO.getSkuId());
                    mkuIdColumn.setCellValue(importProductDTO.getMkuId());
                }
            }
        }
    }


    /**
     * 从 S3 对象流中解压缩文件到指定的输出文件夹。
     * @param reqFileUrl S3 对象流输入
     * @param outputFolder 解压缩后的文件输出文件夹路径
     * @return 解压缩操作是否成功
     */
    public boolean unzip(String reqFileUrl, String outputFolder) {

        try{
            try {
                S3Object s3Object = s3Utils.download(reqFileUrl);
                ZipInputStream utfInputStream = new ZipInputStream(s3Object.getObjectContent(), Charset.forName(UTF_8));
                ZipUtils.unzipFiles(utfInputStream, outputFolder);
            } catch (Exception e) {
                log.error("unzip 解压缩文件异常 charset={},outputFolder={}", UTF_8, outputFolder);
            }
            try {
                S3Object s3Object = s3Utils.download(reqFileUrl);
                ZipInputStream gbkInputStream = new ZipInputStream(s3Object.getObjectContent(), Charset.forName(GBK));
                ZipUtils.unzipFiles(gbkInputStream, outputFolder);
            } catch (Exception e) {
                log.error("unzip 解压缩文件异常 charset={},outputFolder={}", GBK, outputFolder);
            }

        }catch(Exception e){
            log.error("unzip 解压缩文件异常  error outputFolder={}", outputFolder, e);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 处理解压后的文件
     * @param outputFolderPathTemp  解压文件路径
     * @param reqParam 文件名
     * @return 返回excel文件、图片映射key->图片名  value-> 图片文件
     */
    private Pair<File, Map<String, File>> readFiles(String outputFolderPathTemp, String reqParam) {
        int dotLastIndex = reqParam.lastIndexOf(DOT);
        log.info("DisposableProductImportHandler.readFiles  入参:[reqParam={},dotLastIndex={}]", reqParam, dotLastIndex);

        File file = new File(outputFolderPathTemp);
        if (file.isFile() || !file.exists()) {
            file = new File(outputFolderPathTemp);
        }
        String absolutePath = file.getAbsolutePath();
        boolean isDirectory = !file.isDirectory();
        log.info("DisposableProductImportHandler.readFiles  入参:[reqParam={},dotLastIndex={},file={},isDirectory={},absolutePath={}]", reqParam, dotLastIndex,outputFolderPathTemp + File.separator + reqParam,isDirectory,absolutePath);
        AssertValidation.isTrue(isDirectory, "文件结构不正确");

        File[] files = file.listFiles();
        AssertValidation.isTrue(files == null || ArrayUtils.isEmpty(files), "不存在可以处理的文件");

        File excelFile = null;
        Map<String, File> imageMap = Maps.newHashMap();
        for (File temp : files) {
            String name = FileUtil.getName(temp);
            // 排除临时excel文件
            if (name.startsWith(Constant.EXCEL_TEMP_PREFIX) || name.startsWith("__MACOSX")) {
                log.info("DisposableProductImportHandler.readFiles. file is temp excel isFile={}, name={}, path={}", temp.isFile(), name, temp.getAbsolutePath());
                continue;
            }
            // 读取excel文件
            if (Objects.isNull(excelFile)) {
                excelFile = this.getFile(temp, excelFile);
            }
            this.readImages(temp, imageMap);
        }
        log.info("DisposableProductImportHandler.readFiles. excelFile is null={}, path={}", null == excelFile, null != excelFile ? excelFile.getAbsolutePath() : "null");
        return Pair.of(excelFile, imageMap);
    }


    private void readImages(File file, Map<String, File> imageMap) {
        String suffix = FileUtil.getSuffix(file);
        if (file.isFile() && Constant.IMAGE_TYPE_SET.contains(suffix)) {
            this.readImage(file, imageMap);
            return;
        }
        if (file.isDirectory()) {
            File[] imageFiles = file.listFiles();
            if (imageFiles == null || ArrayUtils.isEmpty(imageFiles)) {
                return;
            }
            for (File image : imageFiles) {
                this.readImages(image, imageMap);
            }
        }
    }

    private void readImage(File file, Map<String, File> imageMap) {
//        String imageName = file.getName().split("\\.")[0];
        String imageName = FileUtil.mainName(file);
        log.info("DisposableProductImportHandler.readImage  读取图片文件名  fileName={}  imageName={}",file.getName(), imageName);
        String imageNameUpperCase = imageName.trim().toUpperCase();
        if (!imageMap.containsKey(imageNameUpperCase)){
            imageMap.put(imageName.trim().toUpperCase(), file);
            // throw new TaskBizException(this.getI18nMessageByKey(TEMPLATE_ERROR_IMAGE_SAME_I18N,LangConstant.LANG_EN));
        }
    }

    /**
     * 上传图片到S3存储服务。
     *
     * @param image 要上传的图片文件。
     * @param type 图片类型，用于区分不同种类的图片。
     * @return 上传成功后返回的图片URL。
     */
    private String uploadImage(File image,int type) {
        String imageName = image.getName();
        try (FileInputStream fileInputStream = new FileInputStream(image)) {
            //String fileName = FileUtil.getPrefix(image);
            String suffix = FileUtil.getSuffix(image);
            // 上传图片名
            String finalFileName  = TASK + System.nanoTime() + DOT + suffix;
            log.info("DisposableProductImportHandler.uploadImage finalFileName={}", finalFileName);
            return s3Utils.upload(fileInputStream, type , finalFileName);
        } catch (Exception e) {
            log.info("DisposableProductImportHandler.uploadImage error finalFileName={}", imageName, e);
            throw new TaskBizException(this.getI18nMessageByKey(TEMPLATE_ERROR_IMAGE_EXCEPTION_I18N,LangConstant.LANG_EN));
        }
    }


    abstract protected TaskStrategyService getTaskService();

    private String getI18nMessageByKey(String key, String lang) {
        if(CountryConstant.COUNTRY_ZH.equals(sourceCountryCode)){
            Map<String/*词条*/, Map<String/*语种*/, String/*多语言值*/>> internationalItemMap  = internationalDuccConfig.queryModuleKeysForCnCreateProduct(ProductConstant.BATCH_I18N_MODEL);
            String i18Key = ProductConstant.BATCH_I18N_MODEL + Constant.DOT + key;
            return internationalDuccConfig.queryLanValOrDefaultFromMap(internationalItemMap, lang, i18Key);
        }
        Map<String/*词条*/, Map<String/*语种*/, String/*多语言值*/>> internationalItemMap  = internationalDuccConfig.queryModuleKeysForCreateProduct(ProductConstant.BATCH_I18N_MODEL);
        String i18Key = ProductConstant.BATCH_I18N_MODEL + Constant.DOT + key;
        return internationalDuccConfig.queryLanValOrDefaultFromMap(internationalItemMap, lang, i18Key);
    }

    private GlobalAttributeQueryReqDTO getGlobalAttributeQueryReqDTO(BatchParamVO batchParamVO, long categoryId,Integer dimension) {
        GlobalAttributeQueryReqDTO queryReqDTO = new GlobalAttributeQueryReqDTO();
        queryReqDTO.setCategoryId(categoryId);
        queryReqDTO.setSourceCountryCode(batchParamVO.getSourceCountryCode());
        queryReqDTO.setTargetCountryCodes(Arrays.stream(batchParamVO.getAttributeScope().split(Constant.COMMA)).collect(Collectors.toSet()));
        queryReqDTO.setAttributeDimension(dimension);
        queryReqDTO.setExport(batchParamVO.getExport());
        queryReqDTO.setLang(batchParamVO.getLang());
        return queryReqDTO;
    }

    /**
     * 处理SPU的全局属性。
     * @param importProductDTO 导入产品的DTO对象。
     * @param data SPU的全局属性数据。
     * @param saveSpuApiDTO 保存SPU的API DTO对象。
     */
    private void handSpuGlobalAttribute(ImportProductDTO importProductDTO, JSONObject data, SaveSpuApiDTO saveSpuApiDTO){
        // 解析扩展属性开头
        List<String> spuGlobalAttrFields = importProductDTO.getFields().stream().filter(k-> k.startsWith(SPU_GLOBAL_I18N)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(spuGlobalAttrFields)) {
            return;
        }
        // 单选
        List<String> radioSpuGloablAttributeList = spuGlobalAttrFields.stream().filter(k -> k.endsWith(AttributeConstant.SINGLE_SELECTION + "")).collect(Collectors.toList());
        // 多选
        List<String> checkboxSpuGloablAttributeList = spuGlobalAttrFields.stream().filter(k -> k.endsWith(AttributeConstant.MULTIPLE_SELECTION + "")).collect(Collectors.toList());
        // 文本
        List<String> textSpuGloablAttributeList = spuGlobalAttrFields.stream().filter(k -> k.endsWith(AttributeConstant.TEXT + "")).collect(Collectors.toList());

        // 单选跨境属性处理
        List<GroupPropertyDTO> groupPropertyDTOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(radioSpuGloablAttributeList)) {
            this.handleGlobalRadioAndTextAttributeList(data, radioSpuGloablAttributeList, groupPropertyDTOList,AttributeConstant.SINGLE_SELECTION,importProductDTO);
        }
        if (CollectionUtils.isNotEmpty(checkboxSpuGloablAttributeList)) {
            this.handleGlobalCheckBoxAttributeList(importProductDTO, data, radioSpuGloablAttributeList, groupPropertyDTOList);
        }
        if (CollectionUtils.isNotEmpty(textSpuGloablAttributeList)) {
            this.handleGlobalRadioAndTextAttributeList(data, textSpuGloablAttributeList, groupPropertyDTOList,AttributeConstant.TEXT,importProductDTO);
        }
        saveSpuApiDTO.setSpuInterPropertyList(groupPropertyDTOList);

    }

    private void handleGlobalCheckBoxAttributeList(ImportProductDTO importProductDTO, JSONObject data, List<String> radioSpuGloablAttributeList, List<GroupPropertyDTO> groupPropertyDTOList) {
        GroupPropertyDTO groupPropertyDTO = new GroupPropertyDTO();
        List<PropertyApiDTO> propertyApiDTOList = Lists.newArrayList();
        // spu多选数据
        Map<Long, Map<String, Long>> spuCheckBoxValueMap = importProductDTO.getSpuAttributeAndCheckBoxValueMap();
        for (String field : radioSpuGloablAttributeList) {
            PropertyApiDTO propertyApiDTO = new PropertyApiDTO();
            // 单选跨境属性
            String globalAttributeId = field.split(Constant.UNDER_LINE)[1];
            long attributeId = Long.parseLong(globalAttributeId);
            propertyApiDTO.setAttributeId(attributeId);
            String value = this.getJSONValue(data, field);
            log.info("handleGlobalCheckBoxAttributeList 复选跨境属性解析 field={},attributeId={},value={}",field,attributeId,value);
            if (StringUtils.isBlank(value) || MapUtils.isEmpty(spuCheckBoxValueMap) || MapUtils.isEmpty(spuCheckBoxValueMap.get(attributeId))) {
                continue;
            }
            Map<String, Long> valueMap = spuCheckBoxValueMap.get(attributeId);
            log.info("handleGlobalCheckBoxAttributeList 复选跨境属性解析 field={},attributeId={},value={},valueMap={}",field,attributeId,value,JSON.toJSONString(valueMap));
            List<PropertyValueApiDTO> propertyValueVOList = Lists.newArrayList();

            Set<String> checkboxValueSet = Arrays.stream(value.split(Constant.COMMA)).collect(Collectors.toSet());
            for (String checkboxValue : checkboxValueSet) {
                PropertyValueApiDTO propertyValueApiDTO = new PropertyValueApiDTO();
                propertyValueApiDTO.setAttributeId(attributeId);
                propertyValueApiDTO.setAttributeValueId(valueMap.get(checkboxValue));
                propertyValueApiDTO.setAttributeValueName(checkboxValue);
                propertyValueVOList.add(propertyValueApiDTO);
            }
            propertyApiDTO.setPropertyValueVOList(propertyValueVOList);
            propertyApiDTOList.add(propertyApiDTO);
        }
        groupPropertyDTO.setPropertyVOS(propertyApiDTOList);
        groupPropertyDTOList.add(groupPropertyDTO);
    }

    /**
     * 处理全局单选和文本属性列表。
     *
     * @param data JSONObject类型的数据对象。
     * @param radioSpuGloablAttributeList List<String>类型的单选跨境属性列表。
     * @param groupPropertyDTOList List<GroupPropertyDTO>类型的组属性DTO列表。
     * @param inputType Integer类型的输入类型，表示单选或文本。
     */
    private void handleGlobalRadioAndTextAttributeList(JSONObject data, List<String> radioSpuGloablAttributeList, List<GroupPropertyDTO> groupPropertyDTOList, Integer inputType,ImportProductDTO importProductDTO) {
        if (!importProductDTO.getValid()) {
            return;
        }
        GroupPropertyDTO groupPropertyDTO = new GroupPropertyDTO();
        List<PropertyApiDTO> propertyApiDTOList = Lists.newArrayList();
        for (String field : radioSpuGloablAttributeList) {
            PropertyApiDTO propertyApiDTO = new PropertyApiDTO();
            // 单选跨境属性
            String globalAttributeId = field.split(Constant.UNDER_LINE)[1];
            long attributeId = Long.parseLong(globalAttributeId);
            propertyApiDTO.setAttributeId(attributeId);
            String value = this.getJSONValue(data, field);
            if (StringUtils.isBlank(value) || !importProductDTO.getValid()) {
                continue;
            }
            List<PropertyValueApiDTO> propertyValueVOList = Lists.newArrayList();
            if (AttributeConstant.SINGLE_SELECTION == inputType){
                String globalValueId = ExcelUtils.getCellId(value);
                if (StringUtils.isNotBlank(globalValueId)) {
                    PropertyValueApiDTO propertyValueApiDTO = new PropertyValueApiDTO();
                    propertyValueApiDTO.setAttributeId(attributeId);
                    propertyValueApiDTO.setAttributeValueId(Long.parseLong(globalValueId));
                    propertyValueApiDTO.setSelected(Boolean.TRUE);
                    propertyValueVOList.add(propertyValueApiDTO);
                }else {
                    // 跨境属性填写不正确直接报错返回
                    importProductDTO.failed(String.format("%s 单选跨境属性选择不正确,请在下拉列表中选择，不要覆盖",value));
                    continue;
                }
            } else if (AttributeConstant.TEXT.equals(inputType)) {
                PropertyValueApiDTO propertyValueApiDTO = new PropertyValueApiDTO();
                propertyValueApiDTO.setAttributeId(attributeId);
                if (MapUtils.isNotEmpty(importProductDTO.getTextCheckTypeMap())) {
                    Map<Long, Integer> textCheckTypeMap = importProductDTO.getTextCheckTypeMap();
                    if (textCheckTypeMap.containsKey(attributeId) && Objects.nonNull(textCheckTypeMap.get(attributeId))
                            && AtrributeValueInputCheckTypeEnum.NUMBER.getCode().equals(textCheckTypeMap.get(attributeId))
                    && !com.jdi.isc.task.center.common.utils.NumberUtils.isPositiveNumeric(value)) {
                        importProductDTO.failed(String.format("%s 当前文本跨境属性值必须是正整数",value));
                        continue;
                    }else if (textCheckTypeMap.containsKey(attributeId) && Objects.nonNull(textCheckTypeMap.get(attributeId))
                            && AtrributeValueInputCheckTypeEnum.DECIMAL.getCode().equals(textCheckTypeMap.get(attributeId))
                            && !com.jdi.isc.task.center.common.utils.NumberUtils.isPositiveDecimal(value)){
                        importProductDTO.failed(String.format("%s 当前文本跨境属性值必须是正小数",value));
                        continue;
                    }
                }


                propertyValueApiDTO.setLang(LangConstant.LANG_ZH);
                propertyValueApiDTO.setAttributeValueName(value);
                propertyValueVOList.add(propertyValueApiDTO);
            }

            if (importProductDTO.getValid()) {
                propertyApiDTO.setPropertyValueVOList(propertyValueVOList);
                propertyApiDTOList.add(propertyApiDTO);
            }
        }

        if (CollectionUtils.isNotEmpty(propertyApiDTOList)) {
            groupPropertyDTO.setPropertyVOS(propertyApiDTOList);
            groupPropertyDTOList.add(groupPropertyDTO);
        }
    }


    /**
     * 处理 SKU 全局属性。
     * @param importProductDTO 导入产品的 DTO 对象。
     * @param data SKU 的全局属性数据。
     * @param saveSpuApiDTO 保存 SPu 的 API DTO 对象。
     */
    private void handSkuGlobalAttribute(ImportProductDTO importProductDTO, JSONObject data, SaveSpuApiDTO saveSpuApiDTO){
        // 解析扩展属性开头
        List<String> skuGlobalAttrFields = importProductDTO.getFields().stream().filter(k-> k.startsWith(SKU_GLOBAL_I18N)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuGlobalAttrFields)) {
            return;
        }

        List<SaveSkuApiDTO> skuVOList = saveSpuApiDTO.getSkuVOList();
        if (CollectionUtils.isEmpty(skuVOList)) {
            return;
        }
        // 单选
        List<String> radioSkuGloablAttributeList = skuGlobalAttrFields.stream().filter(k -> k.endsWith(AttributeConstant.SINGLE_SELECTION + "")).collect(Collectors.toList());
        // 多选
        List<String> checkboxSkuGloablAttributeList = skuGlobalAttrFields.stream().filter(k -> k.endsWith(AttributeConstant.MULTIPLE_SELECTION + "")).collect(Collectors.toList());
        // 文本
        List<String> textSuGloablAttributeList = skuGlobalAttrFields.stream().filter(k -> k.endsWith(AttributeConstant.TEXT + "")).collect(Collectors.toList());

        // 单选跨境属性处理
        List<GroupPropertyDTO> groupPropertyDTOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(radioSkuGloablAttributeList)) {
            this.handleGlobalRadioAndTextAttributeList(data, radioSkuGloablAttributeList, groupPropertyDTOList,AttributeConstant.SINGLE_SELECTION,importProductDTO);
        }
        if (CollectionUtils.isNotEmpty(checkboxSkuGloablAttributeList)) {
            this.handleGlobalCheckBoxAttributeList(importProductDTO, data, radioSkuGloablAttributeList, groupPropertyDTOList);
        }
        if (CollectionUtils.isNotEmpty(textSuGloablAttributeList)) {
            this.handleGlobalRadioAndTextAttributeList(data, textSuGloablAttributeList, groupPropertyDTOList,AttributeConstant.TEXT,importProductDTO);
        }

        if (!importProductDTO.getValid()) {
            log.warn("importProductDTO Valid. message={}", importProductDTO.getResult());
            return;
        }

        // TODO  越南未税采购价计算和含税采购价计算
        SaveSkuApiDTO saveSkuApiDTO = skuVOList.get(0);
        log.info("DisposableProductImportHandler.handSkuGlobalAttribute 计算税金前跨境属性：[{}]",JSON.toJSONString(groupPropertyDTOList));
        CalculateTaxVO input = new CalculateTaxVO();
        input.setGroupPropertyDTOList(groupPropertyDTOList);
        input.setSaveSkuApiDTO(saveSkuApiDTO);
        input.setSaveSpuApiDTO(saveSpuApiDTO);
        input.setSourceCountryCode(sourceCountryCode);
        input.setWorkerDTO(importProductDTO);
        calculateTaxManageService.calculateTax(input);
        log.info("DisposableProductImportHandler.handSkuGlobalAttribute 计算税金后跨境属性：[{}]",JSON.toJSONString(input.getGroupPropertyDTOList()));
        saveSkuApiDTO.setSkuInterPropertyList(input.getGroupPropertyDTOList());
    }

    private void handleInitData(List<ImportProductDTO> importProductDTOList,TaskDTO<ImportProductDTO> taskDTO){
        if(CollectionUtils.isEmpty(importProductDTOList)){
            log.info("DisposableProductImportHandler.handlePrice params is null");
            return;
        }

        String reqParam = taskDTO.getTaskStrategyVO().getReqParam();
        BatchParamVO batchParamVO = JSON.parseObject(reqParam, BatchParamVO.class);
        Integer bizType = taskDTO.getTaskStrategyVO().getBizType();
        if (TaskBizTypeEnum.CN_CREATE_PRODUCT_BATCH_IMPORT.getCode() == bizType || TaskBizTypeEnum.INT_CREATE_PRODUCT_BATCH_IMPORT.getCode() == bizType){
            batchParamVO = importProductDTOList.get(0).getBatchParamVO();
        }

        String sourceCountryCode = batchParamVO.getSourceCountryCode();
        String attributeScope = batchParamVO.getAttributeScope();
        attributeScope = StringUtils.isNotBlank(attributeScope) ? attributeScope : sourceCountryCode;
        // 国家语言集合
        List<LangResDTO> langResDTOList = countryLangRpcService.queryLangListByCountryCodes(Arrays.stream(attributeScope.split(Constant.COMMA)).collect(Collectors.toList()));
        langList = new ArrayList<>();
        for (LangResDTO resDTO : langResDTOList) {
            langList.add(resDTO.getLangCode());
        }

        // 初始化品牌名称
        this.handleBrandNameMap(importProductDTOList);

        List<Long> longJdSkuIds = new ArrayList<>();
        importProductDTOList.forEach(item->{
            String jdSkuID = this.getJSONValue(item.getData(),JD_SKU_ID_PREFIX);
            if(StringUtils.isNotBlank(jdSkuID) && item.getValid()){
                // 替换掉所有的空格和空行数据
                jdSkuID = ReUtil.replaceAll(jdSkuID, "\\p{Z}+", "");
                longJdSkuIds.add(Long.valueOf(jdSkuID));
            }
        });

        if(CollectionUtils.isEmpty(longJdSkuIds)){
            log.info("DisposableProductImportHandler.handlePrice longJdSkuIds is null");
            return;
        }

        JdProductQueryDTO param = new JdProductQueryDTO();
        param.setSkuIds(longJdSkuIds);
        skuMap = skuInfoRpcService.querySkuMap(param);

        if (MapUtils.isEmpty(skuMap)) {
            return;
        }

        // 查询价格
        this.getSkuPriceMap(skuMap, longJdSkuIds);
        // jdSku 可售
        skuCanPurchaseList = rpcGdProductService.queryStandardSkuAvailableSaleAllArea(longJdSkuIds);
        // jdSku 区域限售
        skuAreaLimit = rpcGdProductService.queryStandardSkuAreaLimitAllArea(longJdSkuIds);
        // jdSku 商详+翻译
        descriptionMap = translateService.queryTranslateDescByJdSkuIds(longJdSkuIds,this.getDescriptionTranslateLangList());
        // jdSku 主图
        mainImageMap = this.downJdSkuMainImage(longJdSkuIds,skuMap);
    }

    private Map<Long,String> downJdSkuMainImage(List<Long> jdSkuIds, Map<Long, JdProductDTO> skuMap) {
        Map<Long,String> result = new HashMap<>();
        if(CollectionUtils.isEmpty(jdSkuIds)){
            log.info("DisposableProductImportHandler.downJdSkuMainImage jdSkuIds is null.");
            return result;
        }
        for(Long jdSkuId : jdSkuIds){
            JdProductDTO jdProductDTO = skuMap.get(jdSkuId);
            if (Objects.isNull(jdProductDTO) || StringUtils.isBlank(jdProductDTO.getProductImage())){
                continue;
            }
            String jdMainImageUrl = imageDomain + jdProductDTO.getProductImage();
            log.info("DisposableProductImportHandler.downJdSkuMainImage 拉取主站主图，设置到工业国际商品主图,jdSkuId={},jdMainImageUrl={}", jdSkuId, jdMainImageUrl);
            String s3ImageUrl = translateService.downloadAndUploadS3(jdMainImageUrl);
            if (StringUtils.isNotBlank(s3ImageUrl)) {
                result.put(jdSkuId,s3ImageUrl);
            }
        }
        return result;
    }


    private List<String> getDescriptionTranslateLangList() {
        List<String> translateLangList = Lists.newArrayList();
        if (StringUtils.isNotBlank(lang) && ArrayUtils.isNotEmpty(lang.split(Constant.COMMA))) {
            String[] langArray = lang.split(Constant.COMMA);
            translateLangList = Arrays.asList(langArray);
        } else {
            translateLangList = langList;
        }
        return translateLangList;
    }

    /**
     * 1、查询商品价格，先判断是否有mku
     * 2、mku先查询关联sku，取sku中价格最高的
     * 3、非mku直接查询生效价格返回
     *
     * @param skuMap   商品map
     * @param jdSkuIds 导入的sku数组
     * @return 返回商品和价格关系
     */
    private void getSkuPriceMap(Map<Long, JdProductDTO> skuMap, List<Long> jdSkuIds) {

        List<String> mkuIdList = Lists.newArrayList();
        List<Long> skuIdList = Lists.newArrayList();

        if (Boolean.TRUE.equals(supportMku)) {
            skuMap.forEach((k, v) -> {
                // 满足条件的为mku，工业标准商品标记=1  供应商简码:bjjdbyxxjs
                if (STANDARD_SKU_FLAG.equals(v.getGybzsp()) && CN_VENDOR_CODE.equals(v.getSupplyUnit())) {
                    mkuIdList.add(String.valueOf(k));
                } else {
                    skuIdList.add(k);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(mkuIdList)) {
            Map<String, Set<String>> mkuRelationSkuMap = mkuMappingRpcService.queryMkuRelationSku(Sets.newHashSet(mkuIdList));
            Map<Long, IscEachSkuPriceResDTO> mkuMaxPriceMap = Maps.newHashMap();
            mkuRelationSkuMap.forEach((mkuId, skuSet) -> mkuMaxPriceMap.put(Long.valueOf(mkuId), vendorPriceRpcService.getJdiMaxPrice(skuSet.stream().map(Long::valueOf).collect(Collectors.toList()))));
            skuPriceMap.putAll(mkuMaxPriceMap);
            this.fillSkuPurchasePriceMap(skuIdList);
        } else {
            // 查询采购价格
            this.fillSkuPurchasePriceMap(skuIdList);
        }
    }

    private void fillSkuPurchasePriceMap(List<Long> skuIdList) {
        // 分批查询
        List<List<Long>> skuIdPartitionList = Lists.partition(skuIdList, 100);
        for (List<Long> skuIds : skuIdPartitionList) {
            // 查询采购价格
            IscSkuDomesticPriceResDTO domesticPriceResDTO = vendorPriceRpcService.queryJdiSkuPriceContainFailed(skuIds);
            if (MapUtils.isNotEmpty(domesticPriceResDTO.getSuccessSkuPriceMap())) {
                skuPriceMap.putAll(domesticPriceResDTO.getSuccessSkuPriceMap());
            }
            // 未查到采购价的的sku
            if (MapUtils.isNotEmpty(domesticPriceResDTO.getFailSkuPriceMap())) {
                failedSkuPriceMap.putAll(domesticPriceResDTO.getFailSkuPriceMap());
            }
        }
    }

    private void handleJdSkuCanPurchase(ImportProductDTO importProductDTO) {
        String jdSkuID = this.getJSONValue(importProductDTO.getData(),JD_SKU_ID_PREFIX);
        if(StringUtils.isBlank(jdSkuID)){
            return;
        }
        final Long jdSkuId = Long.valueOf(jdSkuID);
        if (!skuCanPurchaseList.contains(jdSkuId)) {
            importProductDTO.failed("该主站商品在主站处于不可售状态，请检查");
        }
        if(skuAreaLimit.contains(jdSkuId)){
            importProductDTO.failed("该主站商品在主站处于区域限售状态，请检查");
        }
    }

    private void handlePrice(ImportProductDTO importProductDTO) {
        String jdSkuID = this.getJSONValue(importProductDTO.getData(),JD_SKU_ID_PREFIX);
        if(StringUtils.isBlank(jdSkuID)){
            return;
        }

        JSONObject jsonObject = importProductDTO.getData();

        final Long jdSkuId = Long.valueOf(jdSkuID);
        if (skuPriceMap.containsKey(jdSkuId)) {
            IscEachSkuPriceResDTO priceVo = skuPriceMap.get(jdSkuId);
            jsonObject.put(FIX_UNTAXED_PURCHASE_PRICE,priceVo.getJdSkuPurchasePrice());

            String salePrice = this.getJSONValue(importProductDTO.getData(),FIX_UNTAXED_SALES_PRICE);
            if(StringUtils.isBlank(salePrice)){
                jsonObject.put(FIX_UNTAXED_SALES_PRICE,priceVo.getJdSkuPurchasePrice().divide(new BigDecimal(Constant.PURCHASE_RATE), 4, RoundingMode.UP));
            }
        } else if (failedSkuPriceMap.containsKey(jdSkuId) && Objects.nonNull(failedSkuPriceMap.get(jdSkuId))) {
            importProductDTO.failed(failedSkuPriceMap.get(jdSkuId).getMessage());
        }else {
            String contactErp = StringUtils.isNotBlank(contactConfig) ? contactConfig :  Constant.CONTACT_ERP;
            importProductDTO.failed( String.format("没查到国内采购价，请联系ERP:[%s]。",contactErp));
        }
    }

    private void handleJdSkuInfo(ImportProductDTO importProductDTO) {
        String jdSkuID = this.getJSONValue(importProductDTO.getData(),JD_SKU_ID_PREFIX);
        if(StringUtils.isBlank(jdSkuID)){
            return;
        }

        JSONObject jsonObject = importProductDTO.getData();

        final Long jdSkuId = Long.valueOf(jdSkuID);
        if (skuMap.containsKey(jdSkuId)) {
            JdProductDTO jdProductDTO = skuMap.get(jdSkuId);
            log.info("DisposableChProductImportHandler.handleJdSkuInfo 处理sku信息,skuId={},jdProductDTO={}", jdSkuId, JSON.toJSONString(jdProductDTO));
            String length = this.getJSONValue(importProductDTO.getData(),FIX_LENGTH);
            if (StringUtils.isBlank(length) && Objects.nonNull(jdProductDTO.getLength())) {
                jsonObject.put(FIX_LENGTH,jdProductDTO.getLength());
            }
            String width = this.getJSONValue(importProductDTO.getData(),FIX_WIDTH);
            if (StringUtils.isBlank(width) && Objects.nonNull(jdProductDTO.getWidth())) {
                jsonObject.put(FIX_WIDTH,jdProductDTO.getWidth());
            }
            String height = this.getJSONValue(importProductDTO.getData(),FIX_HEIGHT);
            if (StringUtils.isBlank(height) && Objects.nonNull(jdProductDTO.getHeight())) {
                jsonObject.put(FIX_HEIGHT,jdProductDTO.getHeight());
            }
            String weight = this.getJSONValue(importProductDTO.getData(),FIX_GROSS_WEIGHT);
            if (StringUtils.isBlank(weight) && Objects.nonNull(jdProductDTO.getWeight())) {
                jsonObject.put(FIX_GROSS_WEIGHT,jdProductDTO.getWeight().multiply(new BigDecimal(Constant.THOUSAND)).setScale(2, RoundingMode.HALF_UP).toString());
                log.info("DisposableChProductImportHandler.handleJdSkuInfo 处理sku信息,skuId={},jdWeight={},weight={}", jdSkuId, jdProductDTO.getWeight(), weight);
            }
            String upcCode = this.getJSONValue(importProductDTO.getData(),FIX_BARCODE);
            if (StringUtils.isBlank(upcCode)) {
                jsonObject.put(FIX_BARCODE,jdProductDTO.getUpcCode());
            }
            //如果是ducc上的白名单jdSkuId，从白名单上获取固定的供应商信息
            JSONObject defaultSku = getDefaultSkuInfo(jdSkuId);
            if(defaultSku!=null && StringUtils.isNotBlank(defaultSku.getString("venderCode"))){
                jsonObject.put(FIX_JD_SUPPLIER_CODE,defaultSku.getString("venderCode"));
            }else {
                if (StringUtils.isNotBlank(jdProductDTO.getSupplyUnit())) {
                    jsonObject.put(FIX_JD_SUPPLIER_CODE,jdProductDTO.getSupplyUnit());
                }
            }
            String saleUnit = this.getJSONValue(importProductDTO.getData(),FIX_SALES_UNIT);
            if(StringUtils.isBlank(saleUnit)){
                String unit = "";
                List<SaleUnitApiDTO> saleUnitApiDTOList = rpcSpuService.saleUnitList(LangConstant.LANG_ZH);
                if(CollectionUtils.isNotEmpty(saleUnitApiDTOList) && StringUtils.isNotBlank(jdProductDTO.getUnit())){
                    for(SaleUnitApiDTO saleUnitApiDTO : saleUnitApiDTOList){
                        if(jdProductDTO.getUnit().equals(saleUnitApiDTO.getUnitName())){
                            unit = String.valueOf(saleUnitApiDTO.getUnitCode());
                            break;
                        }
                    }
                }
                if(StringUtils.isBlank(unit)){
                    importProductDTO.failed(String.format("销售单位获取失败,请手动填写补充"));
                }
                jsonObject.put(FIX_SALES_UNIT,Constant.LEFT_BRACKET + unit + RIGHT_BRACKET);
            }

            // 商品中文名称
            String spuZhName = String.format(ProductConstant.SPU_NAME_PREFIX, LangConstant.LANG_ZH);
            String spuNameValueStr = this.getJSONValue(importProductDTO.getData(), spuZhName);
            if (StringUtils.isBlank(spuNameValueStr) && StringUtils.isNotBlank(jdProductDTO.getSkuName())) {
                String skuName = jdProductDTO.getSkuName();
                jsonObject.put(spuZhName,skuName.length() > SKU_NAME_LEN ? skuName.substring(0, SKU_NAME_LEN) : skuName);
            }

            // 商品主图
            String spuMainImage = this.getJSONValue(importProductDTO.getData(),FIX_MAIN_IMAGE_URL);
            if (StringUtils.isBlank(spuMainImage)) {
                jsonObject.put(FIX_MAIN_IMAGE_URL,mainImageMap.get(jdSkuId));
            }

            // 商品sku主图
            String skuMainImage = this.getJSONValue(importProductDTO.getData(),FIX_SKU_IMAGE);
            if (StringUtils.isBlank(skuMainImage)) {
                jsonObject.put(FIX_SKU_IMAGE,mainImageMap.get(jdSkuId));
            }

            String vendorSkuId = this.getJSONValue(importProductDTO.getData(),FIX_SUPPLIER_SKUID);
            if (StringUtils.isBlank(vendorSkuId)){
                jsonObject.put(FIX_SUPPLIER_SKUID,jdProductDTO.getModel());
            }
        }
    }

    /** 根据京东SKUId获取ducc固化信息*/
    private JSONObject getDefaultSkuInfo(Long jdSkuId) {
        try {
            JSONObject customerPrice = JSON.parseObject(fixedSkuInfo);
            return customerPrice.getJSONObject(String.valueOf(jdSkuId));
        }catch (Exception e){
            log.error("DisposableProductImportHandler.getDefaultSkuInfo invoke error, target:{} ", jdSkuId,e);
        }
        return null;
    }

    private void handleDescription(ImportProductDTO importProductDTO) {
        String jdSkuID = this.getJSONValue(importProductDTO.getData(),JD_SKU_ID_PREFIX);
        if(StringUtils.isBlank(jdSkuID)){
            return;
        }
        final Long jdSkuId = Long.valueOf(jdSkuID);
        JSONObject jsonObject = importProductDTO.getData();
        for(String lang : langList){
            String descriptionKey = String.format(ProductConstant.SPU_DESCRIPTION_PREFIX, lang);
            String description = this.getJSONValue(importProductDTO.getData(),descriptionKey);
            if(StringUtils.isBlank(description)){
                Map<String, String> langDesc= descriptionMap.get(jdSkuId);
                if(MapUtils.isNotEmpty(langDesc)){
                    jsonObject.put(descriptionKey,langDesc.get(lang));
                }
            }
        }
    }

    private void handleInitData(ImportProductDTO importProductDTO) {
        String jdSkuID = this.getJSONValue(importProductDTO.getData(),JD_SKU_ID_PREFIX);
        if(StringUtils.isBlank(jdSkuID)){
            return;
        }
        JSONObject jsonObject = importProductDTO.getData();
        // 原产地
        String origin = this.getJSONValue(jsonObject,FIX_ORIGIN);
        if (StringUtils.isBlank(origin)) {
            jsonObject.put(FIX_ORIGIN,countryRpcService.getCountryNameByCountryCodeAndLang(CountryConstant.COUNTRY_ZH,LangConstant.LANG_ZH));
        }
    }

    private void setInitData(List<ImportProductDTO> importProductDTOList,TaskDTO<ImportProductDTO> taskDTO){
        for (ImportProductDTO importProductDTO : importProductDTOList) {
            if (!importProductDTO.getValid()) {
                continue;
            }


            if(!TaskBizTypeEnum.CN_CREATE_PRODUCT_BATCH_IMPORT.getCode().equals(taskDTO.getTaskStrategyVO().getBizType())){
                continue;
            }

            // 获取京东SKU信息
            this.handleJdSkuInfo(importProductDTO);

            // 限售
            this.handleJdSkuCanPurchase(importProductDTO);

            // 设置价格
            this.handlePrice(importProductDTO);

            // 设置商品详情
            this.handleDescription(importProductDTO);

            // 设置空字符串
            this.handleInitData(importProductDTO);
        }
    }
    private void handleBrandNameMap(List<ImportProductDTO> importProductDTOList){
        brandNameMap = new HashMap<>();
        List<Long> brandIds = new ArrayList<>();
        importProductDTOList.forEach(item->{
            String brandID = this.getValueStr(item.getData(),FIX_BRAND_ID);
            if(StringUtils.isNotBlank(brandID) && item.getValid()){
                brandIds.add(Long.parseLong(brandID));
            }
        });
        BrandReqApiDTO brandReqApiDTO = new BrandReqApiDTO();
        brandReqApiDTO.setBrandIds(new HashSet<>(brandIds));
        brandReqApiDTO.setLangSet(new HashSet<>(langList));
        List<BrandResDTO> brandResDTOS = brandRpcService.queryBrandList(brandReqApiDTO);
        if(CollectionUtils.isEmpty(brandResDTOS)){
            log.error("DisposableProductImportHandler.handleBrandNameMap brandResDTOS is null");
            return;
        }
        for(BrandResDTO brandResDTO : brandResDTOS){
            Long brandId = brandResDTO.getBrandId();
            List<BaseLangDTO> brandLangDTOList  = brandResDTO.getBrandLangDTOList();
            if(CollectionUtils.isEmpty(brandLangDTOList)){
                continue;
            }
            Map<String,String> nameMap = brandLangDTOList.stream()
                    .collect(Collectors.toMap(BaseLangDTO::getLang,BaseLangDTO::getLangName
                            , (existingValue, newValue) -> newValue));
            brandNameMap.put(brandId,nameMap);
        }
    }

    private Map<String,String> getSpuNameMap(String spuNameValueStr,Long brandId,List<String> langList){
        Map<String, String> translateSpuTitleMap = translateRpcService.translateMultiLang(new MultiTranslateReqDTO(spuNameValueStr, null, langList,null));

        Map<String,String> brandLangNameMap = brandNameMap.get(brandId);
        if(MapUtils.isEmpty(brandLangNameMap)){
            log.error("DisposableProductImportHandler.getSpuNameMap brandLangNameMap is null");
            return translateSpuTitleMap;
        }
        Map<String,String> result = new HashMap<>();
        translateSpuTitleMap.forEach((k,v)->{
            String brandName = brandLangNameMap.get(k);
            String spuTitle = v;
            if (StringUtils.isBlank(brandName)){
                brandName = brandLangNameMap.get(LangConstant.LANG_EN);
            }
            if(StringUtils.isNotBlank(brandName)){
                spuTitle = brandName + Constant.WHITE_SPACE + v;
            }
            result.put(k,spuTitle);
        });
        return result;
    }

    private String convertSpuName(String spuNameValueStr,Long brandId,String langCode){
        Map<String,String> brandLangNameMap = brandNameMap.get(brandId);
        if(MapUtils.isEmpty(brandLangNameMap)){
            log.error("DisposableProductImportHandler.convertSpuName brandLangNameMap is null.brandId:{}",brandId);
            return spuNameValueStr;
        }

        // 先取对应小语种品牌名、若为空再取英文小语种品牌名
        String brandName = brandLangNameMap.get(langCode);
        if(StringUtils.isNotBlank(brandName)){
            spuNameValueStr = brandName + Constant.WHITE_SPACE + spuNameValueStr;
        } else {
            brandName = brandLangNameMap.get(LangConstant.LANG_EN);
            if(StringUtils.isNotBlank(brandName)){
                spuNameValueStr = brandName + Constant.WHITE_SPACE + spuNameValueStr;
            }
        }
        return spuNameValueStr;
    }

    /**
     * 读取解压后文件夹中的excel文件，支持多层级读取
     * @param temp 当前文件
     * @param excelFile excel文件对象
     * @return
     */
    private File getFile(File temp,File excelFile) {

        if (excelFile != null){
            return excelFile;
        }

        if (temp.isDirectory()) {
            File[] files = temp.listFiles();
            for (File file : files){
                if (excelFile == null){
                    excelFile = this.getFile(file, excelFile);
                }
            }
        }else {
            String suffix = FileUtil.getSuffix(temp);
            log.info("DisposableProductImportHandler.readFiles. isFile={}, suffix={}, path={}", temp.isFile(), suffix, temp.getAbsolutePath());
            if (temp.isFile() && (Constant.XLSX.equals(suffix) || Constant.XLS.equals(suffix))){
                excelFile = temp;
            }
        }
        return excelFile;
    }

    protected String getValueStr(JSONObject data,String key) {
       return this.getJSONValue(data, key);
    }
}
