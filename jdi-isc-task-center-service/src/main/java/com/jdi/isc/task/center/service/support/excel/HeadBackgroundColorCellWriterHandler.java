package com.jdi.isc.task.center.service.support.excel;


import com.alibaba.excel.constant.OrderConstant;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import com.jdi.isc.task.center.common.costants.Constant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.DefaultIndexedColorMap;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.Set;

/**
 * 表头背景色处理器
 * <AUTHOR>
 * @description：HeadBackgroudColorCellWriterHandler
 * @Date 2025-08-05
 */
public class HeadBackgroundColorCellWriterHandler implements RowWriteHandler {

    @Override
    public int order() {
        return OrderConstant.FILL_STYLE+5;
    }


    private Set<Integer> headRowIndexSet;

    public HeadBackgroundColorCellWriterHandler() {
    }

    public HeadBackgroundColorCellWriterHandler(Set<Integer> headRowIndexSet) {
        this.headRowIndexSet = headRowIndexSet;
    }

    @Override
    public void afterRowDispose(RowWriteHandlerContext context) {
        if (context.getHead() && CollectionUtils.isNotEmpty(headRowIndexSet) && headRowIndexSet.contains(context.getRelativeRowIndex())) {
            Workbook workbook = context.getWriteSheetHolder().getSheet().getWorkbook();

            Row row = context.getRow();
            int numberOfCells = row.getPhysicalNumberOfCells();

            for (int i = 0; i < numberOfCells; i++) {
                Cell cell = row.getCell(i);

                if (workbook instanceof XSSFWorkbook) {
                    // 创建一个新的样式（不要直接修改原来的）
                    XSSFCellStyle newCellStyle = ((XSSFWorkbook) workbook).createCellStyle();
                    newCellStyle.cloneStyleFrom(cell.getCellStyle()); // 先复制原来的样式

                    if (context.getHead() && cell.getStringCellValue().contains(Constant.STAR)) {
                        // 表头包含 * → 浅黄色
                        newCellStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
                    } else {
                        // 其他单元格 → 浅灰色
                        XSSFColor grayColor = new XSSFColor(new java.awt.Color(242, 242, 242), new DefaultIndexedColorMap());
                        newCellStyle.setFillForegroundColor(grayColor);
                    }
                    newCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                    cell.setCellStyle(newCellStyle); // 应用新的样式
                }else if (workbook instanceof HSSFWorkbook){
                    // 创建一个新的样式（不要直接修改原来的）
                    CellStyle newCellStyle = workbook.createCellStyle();
                    newCellStyle.cloneStyleFrom(cell.getCellStyle()); // 先复制原来的样式

                    if (context.getHead() && cell.getStringCellValue().contains(Constant.STAR)) {
                        // 表头包含 * → 浅黄色
                        newCellStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
                    } else {
                        // 其他单元格 → 浅灰色
                        HSSFPalette palette = ((HSSFWorkbook) workbook).getCustomPalette();
                        palette.setColorAtIndex(HSSFColor.HSSFColorPredefined.GREY_25_PERCENT.getIndex(),
                                (byte) 242, (byte) 242, (byte) 242);
                        newCellStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.GREY_25_PERCENT.getIndex());
                    }
                    newCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                    cell.setCellStyle(newCellStyle); // 应用新的样式
                }
            }
        }
    }
}
