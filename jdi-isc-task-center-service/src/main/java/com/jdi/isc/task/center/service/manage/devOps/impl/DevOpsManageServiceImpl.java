package com.jdi.isc.task.center.service.manage.devOps.impl;

import com.jdi.isc.task.center.service.manage.devOps.DevOpsManageService;
import com.jdi.isc.task.center.service.manage.log.LogManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 系统运维管理服务
 * <AUTHOR>
 * @date 2025/5/19
 */
@Service
@Slf4j
public class DevOpsManageServiceImpl implements DevOpsManageService {

    @Resource
    private LogManageService logManageService;

    @Override
    public void clearLog() {
        logManageService.clearLog();
    }
}
