package com.jdi.isc.task.center.service.manage.joysky.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jd.process.api.enums.ProcessStatusEnum;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.task.center.api.joysky.biz.enums.JoySkyBizFlowTypeEnum;
import com.jdi.isc.task.center.api.joysky.req.JoySkyCreateReqApiDTO;
import com.jdi.isc.task.center.api.joysky.res.JoySkyCreateRspApiDTO;
import com.jdi.isc.task.center.domain.enums.YnEnum;
import com.jdi.isc.task.center.domain.joysky.po.JoySkyApprovalTaskLogPO;
import com.jdi.isc.task.center.domain.joysky.po.JoySkyApprovalTaskPO;
import com.jdi.isc.task.center.service.atomic.joysky.JoySkyTaskAtomicService;
import com.jdi.isc.task.center.service.atomic.joysky.JoySkyTaskLogAtomicService;
import com.jdi.isc.task.center.service.manage.joysky.JoySkyTaskManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/11/1
 * @Description joySky业务操作类
 */
@Slf4j
@Service
public class JoySkyTaskManageServiceImpl implements JoySkyTaskManageService {

    @Resource
    private JoySkyTaskAtomicService joySkyTaskAtomicService;

    @Resource
    private JoySkyTaskLogAtomicService joySkyTaskLogAtomicService;

    /**
     * 从配置文件中获取任务环境变量。
     */
    @Value("${jdi.isc.task.env}")
    private String env;


    @Override
    public DataResponse<Boolean> saveApprovalTask(JoySkyCreateReqApiDTO reqInput, JoySkyCreateRspApiDTO rspInput) {
        log.info("saveApprovalTask, input={}", JSONObject.toJSONString(reqInput));

        // 参数业务校验
        if (!preCheckParamValid(reqInput, rspInput)) {
            throw new RuntimeException("存在非法参数！");
        }

        long timeMillis = System.currentTimeMillis();

        // 保存对象
        JoySkyApprovalTaskPO approvalTaskPO = new JoySkyApprovalTaskPO();
        approvalTaskPO.setProcessType(reqInput.getJoySkyFlowType());
        approvalTaskPO.setProcessKey(JoySkyBizFlowTypeEnum.getProcessKeyByEnv(reqInput.getJoySkyFlowType(), env));
        approvalTaskPO.setProcessInstanceId(rspInput.getProcessInstanceId());
        approvalTaskPO.setProcessStatus(ProcessStatusEnum.APPROVING.getValue());
        approvalTaskPO.setRequestData(JSONObject.toJSONString(reqInput.getProcessFromData()));

        //扩展字段
        approvalTaskPO.setYn(YnEnum.YES.getCode());
        approvalTaskPO.setCreator(reqInput.getErp());
        approvalTaskPO.setUpdater(reqInput.getErp());
        approvalTaskPO.setCreateTime(timeMillis);
        approvalTaskPO.setUpdateTime(timeMillis);

        boolean saveRes = joySkyTaskAtomicService.save(approvalTaskPO);
        if (!saveRes) {
            log.warn("saveApprovalTask fail. JoySkyApprovalTaskPO={}", JSONObject.toJSONString(approvalTaskPO));
            throw new RuntimeException("保存失败");
        }

        return DataResponse.success();
    }

    @Override
    public DataResponse<Boolean> updateApprovalTask(JoySkyApprovalTaskPO approvalTaskPO) {
        log.info("updateApprovalTask, input={}", JSONObject.toJSONString(approvalTaskPO));

        long timeMillis = System.currentTimeMillis();

        //扩展字段
        approvalTaskPO.setUpdater("system");
        approvalTaskPO.setUpdateTime(timeMillis);

        LambdaUpdateWrapper<JoySkyApprovalTaskPO> wrapper = Wrappers.<JoySkyApprovalTaskPO>lambdaUpdate()
                .eq(JoySkyApprovalTaskPO::getId, approvalTaskPO.getId())
                .set(JoySkyApprovalTaskPO::getProcessStatus, approvalTaskPO.getProcessStatus())
                .set(JoySkyApprovalTaskPO::getUpdateTime, timeMillis);
        boolean updateRes = joySkyTaskAtomicService.update(wrapper);
        if (!updateRes) {
            log.warn("updateApprovalTask fail. JoySkyApprovalTaskPO={}", JSONObject.toJSONString(approvalTaskPO));
            throw new RuntimeException("更新失败");
        }

        return DataResponse.success();
    }

    @Override
    public DataResponse<Boolean> saveApprovalTaskLog(JoySkyApprovalTaskPO reqInput, String actionType) {
        log.info("saveApprovalTaskLog, input={}", JSONObject.toJSONString(reqInput));

        // 参数业务校验
        if (StringUtils.isBlank(actionType)) {
            throw new RuntimeException("操作类型为空！");
        }
        long timeMillis = System.currentTimeMillis();

        // 保存对象
        JoySkyApprovalTaskLogPO approvalTaskLogPO = new JoySkyApprovalTaskLogPO();
        approvalTaskLogPO.setProcessType(reqInput.getProcessType());
        approvalTaskLogPO.setProcessKey(reqInput.getProcessKey());
        approvalTaskLogPO.setProcessInstanceId(reqInput.getProcessInstanceId());
        approvalTaskLogPO.setProcessStatus(reqInput.getProcessStatus());
        approvalTaskLogPO.setOperateType(actionType);

        //扩展字段
        approvalTaskLogPO.setYn(YnEnum.YES.getCode());
        approvalTaskLogPO.setCreator(reqInput.getCreator());
        approvalTaskLogPO.setUpdater(reqInput.getUpdater());
        approvalTaskLogPO.setCreateTime(timeMillis);
        approvalTaskLogPO.setUpdateTime(timeMillis);

        boolean saveRes = joySkyTaskLogAtomicService.save(approvalTaskLogPO);
        if (!saveRes) {
            log.warn("saveApprovalTaskLog fail. JoySkyApprovalTaskLogPO={}", JSONObject.toJSONString(approvalTaskLogPO));
            throw new RuntimeException("保存失败");
        }

        return DataResponse.success();
    }

    /**
     * 检查创建审批任务的请求和响应参数是否有效。
     *
     * @param reqInput JoySkyCreateReqApiDTO类型的请求参数，包含流程定义key、类型和数据。
     * @param rspInput JoySkyCreateRspApiDTO类型的响应参数，包含流程实例ID。
     * @return 如果所有参数都有效，则返回true；否则返回false。
     */
    private boolean preCheckParamValid(JoySkyCreateReqApiDTO reqInput, JoySkyCreateRspApiDTO rspInput) {
        if (Objects.isNull(reqInput.getJoySkyFlowType()) ||
                CollectionUtils.isEmpty(reqInput.getProcessFromData())) {
            log.info("saveApprovalTask, check input param fail, input={}", reqInput);
            return false;
        }

        if (StringUtils.isBlank(rspInput.getProcessInstanceId())) {
            log.info("saveApprovalTask, check output param fail, input={}", rspInput);
            return false;
        }

        return true;
    }

}
