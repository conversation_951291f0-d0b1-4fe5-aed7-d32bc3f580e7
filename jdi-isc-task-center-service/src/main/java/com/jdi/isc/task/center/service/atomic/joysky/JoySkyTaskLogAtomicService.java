package com.jdi.isc.task.center.service.atomic.joysky;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.task.center.domain.joysky.po.JoySkyApprovalTaskLogPO;
import com.jdi.isc.task.center.repository.jed.mapper.joysky.JoySkyTaskLogBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/11/1
 * @Description
 */
@Service
@Slf4j
public class JoySkyTaskLogAtomicService extends ServiceImpl<JoySkyTaskLogBaseMapper, JoySkyApprovalTaskLogPO> {
}
