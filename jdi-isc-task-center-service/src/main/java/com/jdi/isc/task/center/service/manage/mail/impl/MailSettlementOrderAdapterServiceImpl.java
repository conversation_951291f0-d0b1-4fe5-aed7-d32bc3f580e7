package com.jdi.isc.task.center.service.manage.mail.impl;

import com.jdi.isc.product.soa.api.supplier.res.SupplierAccountRes;
import com.jdi.isc.task.center.api.dto.mail.MailParamDTO;
import com.jdi.isc.task.center.domain.mail.biz.MailMessageVO;
import com.jdi.isc.task.center.domain.mail.biz.MailParam;
import com.jdi.isc.task.center.rpc.supplier.RpcSupplierBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类描述：
 * 默认处理类 入参中携带邮件人信息
 *
 * <AUTHOR>
 * @date 2025/8/16
 */

@Slf4j
@Service
public class MailSettlementOrderAdapterServiceImpl extends AbstractMailAdapterServiceImpl {

    private static final String SETTLEMENT_ID_KEY = "settlemenOrderId";
    private static final String ACCOUNT_NAME = "accountName";
    @Resource
    private RpcSupplierBaseService rpcSupplierBaseService;

    @Override
    public List<MailParam> parseMailContext(MailMessageVO mailMessageVO) {
        List<SupplierAccountRes> accountList = rpcSupplierBaseService.queryAccountList(mailMessageVO.getSupplierCode());
        if (CollectionUtils.isEmpty(accountList)) {
            return Collections.emptyList();
        }
        return accountList.stream()
                .map(account -> createMailParam(mailMessageVO, account))
                .collect(Collectors.toList());
    }

    /**
     * 创建邮件消息上下文。
     */
    public Map<String, Object> createContext(String businessCode, String accountName) {
        Map<String, Object> context = new HashMap<>();
        //替换模版中的结算单号
        context.put(SETTLEMENT_ID_KEY, businessCode);
        //替换模版中的供应商名称
        context.put(ACCOUNT_NAME, accountName);
        return context;
    }

    /**
     * 创建邮件参数对象。
     * @param mailMessageVO 邮件消息视图对象，包含业务代码、消息类型、模型类型和操作员信息。
     * @return MailParam 对象，包含邮件的所有必要参数。
     */
    private MailParam createMailParam(MailMessageVO mailMessageVO, SupplierAccountRes account) {
        MailParam mailParam = new MailParam();
        mailParam.setBusinessCode(mailMessageVO.getBusinessCode() + "-" + account.getId()+ "-" +mailMessageVO.getModelType());

        mailParam.setRecipientEmail(account.getAccountEmail());
        mailParam.setRecipient(account.getAccountName());
        mailParam.setPin(account.getAccountName());
        mailParam.setSendData(createContext(mailMessageVO.getBusinessCode(),account.getAccountName()));
        mailParam.setMessageType(mailMessageVO.getMessageType());
        mailParam.setModelType(mailMessageVO.getModelType());
        mailParam.setUpdater(mailMessageVO.getOperator());
        mailParam.setCreator(mailMessageVO.getOperator());
        mailParam.setCreateTime(System.currentTimeMillis());
        mailParam.setUpdateTime(mailParam.getCreateTime());
        mailParam.setCountryCode(mailMessageVO.getCountryCode());
        return mailParam;
    }
}
      