package com.jdi.isc.task.center.service.protocol.jsf.forecast;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.isc.task.center.api.forecast.ForecastPurchaseOrderPrintApiService;
import com.jdi.isc.task.center.api.forecast.impl.ForecastPurchaseOrderPrintApiDTO;
import com.jdi.isc.task.center.domain.forecast.ForecastPurchaseOrderTaskVO;
import com.jdi.isc.task.center.service.work.task.handler.pdf.ForecastPurchaseOrderPdfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 预报采购单打印服务实现
 *
 * <AUTHOR>
 * @description 预报采购单打印服务实现类
 * @date 2025/5/28
 */
@Slf4j
@Service
public class ForecastPurchaseOrderApiServiceImpl implements ForecastPurchaseOrderPrintApiService {

    @Resource
    private ForecastPurchaseOrderPdfService forecastPurchaseOrderPdfService;

    @Override
    public DataResponse<String> printEntryStock(ForecastPurchaseOrderPrintApiDTO apiDTO){
        DataResponse<String> response = DataResponse.success();
        try {
            ForecastPurchaseOrderTaskVO param = new ForecastPurchaseOrderTaskVO();
            param.setForecastOrderId(apiDTO.getForecastOrderId());
            param.setSupplierCode(apiDTO.getSupplierCode());
            param.setLangList(apiDTO.getLangList());
            String pdfUrl = forecastPurchaseOrderPdfService.of(param).start();
            response.setData(pdfUrl);
        } catch (IllegalArgumentException e) {
            log.error("ForecastPurchaseOrderApiServiceImpl.printEntryStock param error,param:{},e:", JSONObject.toJSONString(apiDTO), e);
            response = DataResponse.buildError(DataResponseCode.DATA_NULL.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error("ForecastPurchaseOrderApiServiceImpl.printEntryStock error,param:{},e:", JSONObject.toJSONString(apiDTO), e);
            response = DataResponse.buildError(DataResponseCode.SERVICE_ERROR.getCode(),e.getMessage());
        }finally {
            log.info("ForecastPurchaseOrderApiServiceImpl.printEntryStock param:{},result:{}",JSONObject.toJSONString(apiDTO),JSONObject.toJSONString(response));
        }
        return response;

    }
}