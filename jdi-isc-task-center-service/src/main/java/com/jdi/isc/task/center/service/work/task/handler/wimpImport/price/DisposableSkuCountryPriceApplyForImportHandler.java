package com.jdi.isc.task.center.service.work.task.handler.wimpImport.price;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.common.utils.validation.PropertyError;
import com.jdi.isc.task.center.common.utils.validation.ValidateResult;
import com.jdi.isc.task.center.common.utils.validation.ValidationUtil;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;
import com.jdi.isc.task.center.domain.task.excel.SkuCountryPriceExcelDTO;
import com.jdi.isc.task.center.rpc.price.AgreementPriceRpcService;
import com.jdi.isc.task.center.rpc.price.impl.CountryCodeVO;
import com.jdi.isc.task.center.rpc.price.impl.StatusCountryAgreementPriceReqDTO;
import com.jdi.isc.task.center.service.adapter.mapstruct.price.CountryPriceConvert;
import com.jdi.isc.task.center.service.work.task.frame.BaseExecutableAbsJob;
import com.jdi.isc.task.center.service.work.task.frame.JobExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@JobExecutor(taskBizType = TaskBizTypeEnum.UPDATE_APPLY_FOR_SKU_COUNTRY_PRICE_BATCH)
public class DisposableSkuCountryPriceApplyForImportHandler extends BaseExecutableAbsJob<SkuCountryPriceExcelDTO> {
    @Resource
    public AgreementPriceRpcService agreementPriceRpcService;

    public final ExecutorService pool = new ThreadPoolExecutor(1, 1, 30L, TimeUnit.SECONDS,
        new ArrayBlockingQueue<>(100000), new ThreadFactoryBuilder().setNamePrefix("agreement-price").build(), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 国家代码变量，用于存储当前处理的国家代码。
     */
    public String countryCode;

    @Override
    public void parse(TaskDTO<SkuCountryPriceExcelDTO> task) {
        super.parse(task);
        List<SkuCountryPriceExcelDTO> skuCountryPriceExcelDTOList = task.getTarget();
        if (CollectionUtils.isEmpty(skuCountryPriceExcelDTOList)) {
            return;
        }
        // 校验参数
        Set<String> skuIds = new HashSet<>();
        for (SkuCountryPriceExcelDTO excelDTO : skuCountryPriceExcelDTOList) {
            ValidateResult<SkuCountryPriceExcelDTO> validateResult = ValidationUtil.checkParam(excelDTO);
            if (Boolean.FALSE.equals(validateResult.getSuccess())) {
                excelDTO.setValid(Boolean.FALSE);
                List<String> messageList = validateResult.getPropertyErrors().stream()
                    .filter(Objects::nonNull)
                    .map(PropertyError::getMessage)
                    .collect(Collectors.toList());
                excelDTO.setResult(String.join(Constant.COMMA, messageList));
            }
            if(Objects.isNull(excelDTO.getSkuId())){
                continue;
            }
            if(skuIds.contains(excelDTO.getSkuId() + excelDTO.getTargetCountryCode())){
                excelDTO.setValid(false);
                excelDTO.setResult("skuId + 目标国重复,请检查数据!");
            }else {
                skuIds.add(excelDTO.getSkuId() + excelDTO.getTargetCountryCode());
            }

        }
    }

    @Override
    public void run(TaskDTO<SkuCountryPriceExcelDTO> task) {
        log.info("DisposableSkuCountryPriceImportHandler.run {}, data:{} ",task.getTaskId(), JSON.toJSONString(task.getTarget()));
        List<SkuCountryPriceExcelDTO> excelDTOList = task.getTarget();
        List<SkuCountryPriceExcelDTO> skuCountryPriceExcelDTOList = filterValid(excelDTOList,task.getOperator());
        if (CollectionUtils.isEmpty(skuCountryPriceExcelDTOList)) {
            return;
        }
        List<StatusCountryAgreementPriceReqDTO> reqDTOList = CountryPriceConvert.INSTANCE.excelDtoList2DtoList(skuCountryPriceExcelDTOList);
        try {
            if(StringUtils.isNotBlank(task.getTaskStrategyVO().getReqParam())) {
                CountryCodeVO countryCodeVO = JSONObject.parseObject(task.getTaskStrategyVO().getReqParam(), CountryCodeVO.class);
                countryCode = countryCodeVO.getTargetCountryCode();
            }
            List<Future<StatusCountryAgreementPriceReqDTO>> priceDtoList  = new ArrayList<>();
            for (StatusCountryAgreementPriceReqDTO priceDTO : reqDTOList){
                priceDTO.setPin(task.getOperator());
                priceDtoList.add(pool.submit(() -> submit(priceDTO)));
            }
            List<StatusCountryAgreementPriceReqDTO> apiDTOList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(priceDtoList)) {
                for (Future<StatusCountryAgreementPriceReqDTO> future : priceDtoList) {
                    try {
                        apiDTOList.add(future.get(10, TimeUnit.SECONDS));
                    } catch (Exception e) {
                        log.error("保存国家协议价和京东价,异步取回数据异常",e);
                        if (null != future) {
                            future.cancel(true);
                        }
                    }
                }
            }
            Map<String, StatusCountryAgreementPriceReqDTO> skuIdCountryMap = apiDTOList
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(dto -> {
                        return dto.getSkuId() + dto.getTargetCountryCode();
                    }, Function.identity())
                );
            log.info("DisposableSkuCountryPriceImportHandler.req {},customerMap:{}",JSON.toJSONString(excelDTOList),  JSON.toJSONString(skuIdCountryMap));
            for(SkuCountryPriceExcelDTO dto : excelDTOList){
                if(dto.getValid()){
                    if(skuIdCountryMap.containsKey(dto.getSkuId() + dto.getTargetCountryCode())){
                        StatusCountryAgreementPriceReqDTO priceReqDTO = skuIdCountryMap.get(dto.getSkuId() + dto.getTargetCountryCode());
                        dto.setValid(priceReqDTO.getValid());
                        dto.setResult(priceReqDTO.getResult());
                    }else {
                        dto.failed("更新国家协议价和京东价失败");
                    }
                }
            }
            task.setTarget(excelDTOList);
            return;
        }catch (Exception e){
            log.error("DisposableSkuCountryPriceImportHandler.run error req:{},err:{}",JSON.toJSONString(excelDTOList),e.getMessage(),e);
            excelDTOList.forEach(dto -> {
                dto.setValid(false);
                dto.setResult("系统异常，请联系管理员");
            });
        }
        task.setTarget(excelDTOList);
    }

    private List<SkuCountryPriceExcelDTO> filterValid(List<SkuCountryPriceExcelDTO> list,String operator){
        log.info("DisposableSkuCountryPriceImportHandler.filterValid req:{}, operator:{}",JSON.toJSONString(list), operator);
        List<SkuCountryPriceExcelDTO> validList = Lists.newArrayList();
        for (SkuCountryPriceExcelDTO dto:list){
            if(dto.getValid()){
                dto.setUpdater(operator);
                validList.add(dto);
            }
        }
        return validList;
    }

    @Deprecated
    public StatusCountryAgreementPriceReqDTO submit(StatusCountryAgreementPriceReqDTO priceDTO){
        if(Objects.nonNull(priceDTO.getAgreementPrice()) && Objects.nonNull(priceDTO.getJdPrice())) {
            if (priceDTO.getAgreementPrice().compareTo(priceDTO.getJdPrice()) >= 0) {
                priceDTO.failed("国家协议价不能大于京东价");
                return priceDTO;
            }
        }
        if(Objects.isNull(priceDTO.getAgreementPrice()) && Objects.isNull(priceDTO.getJdPrice())) {
            priceDTO.failed("国家协议价和京东价不能都为空");
            return priceDTO;
        }

        if(Objects.nonNull(priceDTO.getAgreementPrice()) && priceDTO.getAgreementPrice().compareTo(BigDecimal.ZERO) <= 0) {
            priceDTO.failed("国家协议价不能小于等于0");
            return priceDTO;
        }
        if(Objects.nonNull(priceDTO.getJdPrice()) && priceDTO.getJdPrice().compareTo(BigDecimal.ZERO) <= 0) {
            priceDTO.failed("京东价不能小于等于0");
            return priceDTO;
        }
        if(!priceDTO.getTargetCountryCode().equals(countryCode)) {
            priceDTO.failed("目标国家代码填写错误，请重新填写，当前目标国家代码为：" + countryCode);
            return priceDTO;
        }
        if(!priceDTO.getSourceCountryCode().equals(countryCode) && !priceDTO.getSourceCountryCode().equals( CountryConstant.COUNTRY_ZH)) {
            priceDTO.failed("货源国家代码填写错误，请重新填写，当前货源国家代码为CN或者为：" + countryCode);
            return priceDTO;
        }
        return agreementPriceRpcService.applyForUpdateAgreementAndJdPrice(priceDTO);
    }
}
