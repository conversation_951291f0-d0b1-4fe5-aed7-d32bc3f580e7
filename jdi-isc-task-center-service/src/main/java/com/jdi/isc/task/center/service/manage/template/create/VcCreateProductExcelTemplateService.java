package com.jdi.isc.task.center.service.manage.template.create;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.task.center.common.costants.ProductConstant;
import com.jdi.isc.task.center.domain.excel.BatchParamVO;
import com.jdi.isc.task.center.domain.excel.TemplateHeadSupportVO;
import com.jdi.isc.task.center.domain.excel.TemplateReqVO;
import com.jdi.isc.task.center.domain.excel.TemplateSourceDataVO;
import com.jdi.isc.task.center.service.manage.template.support.TemplateSupportService;
import com.jdi.isc.task.center.service.support.excel.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.jdi.isc.task.center.common.costants.ProductConstant.TEMPLATE_FIRST_ROW_GROUP_TIP_I18N;

/**
 * 供应商批量发品模板生成-主要是对应本土
 * <AUTHOR>
 * @date 2024/8/31
 **/
@Slf4j
@Service
public class VcCreateProductExcelTemplateService extends SupplierParentCreateProductExcelTemplateService {

    @Resource
    private TemplateSupportService templateSupportService;

    @PostConstruct
    public void initExcelName(){
        super.setExcelName("VCBatchCreateProduct");
    }

    /**隐藏sheet表头字段列表*/
    private List<List<String>> hiddenHeadList = Lists.newArrayList();

    @Override
    protected List<TemplateSourceDataVO.SheetDataVO> getSheetDataVoList(TemplateReqVO reqVO) {
        log.info("VcCreateProductExcelTemplateService.getSheetDataVoList reqVO={}",JSON.toJSONString(reqVO));
        super.initVal(reqVO);
        ArrayList<TemplateSourceDataVO.SheetDataVO> sheetDataVOS = Lists.newArrayList();
        String operator = reqVO.getOperator();
        String paramJson = reqVO.getParamJson();
        BatchParamVO batchParamVO = JSON.parseObject(paramJson, BatchParamVO.class);
        batchParamVO.setOperator(operator);
        // 数据填写sheet填充内容
        this.importSheetData(batchParamVO, sheetDataVOS);
        // 供应商类目数据Sheet填充内容
        super.supplierSheetData(batchParamVO,sheetDataVOS);
        // 供应商类目数据Sheet填充内容
        super.categorySheetData(batchParamVO,sheetDataVOS);
        // 供应商品牌数据sheet填充内容
        super.brandSheetData(batchParamVO,sheetDataVOS);
        // SPU跨境属性
        super.spuGlobalAttributeSheetData(batchParamVO,sheetDataVOS);
        // SKU跨境属性
        super.skuGlobalAttributeSheetData(batchParamVO,sheetDataVOS);
        // 隐藏列表
        this.hiddenSheetData(sheetDataVOS);

        return sheetDataVOS;
    }


    public void importSheetData(BatchParamVO batchParamVO, ArrayList<TemplateSourceDataVO.SheetDataVO> sheetDataVOS) {
        TemplateSourceDataVO.SheetDataVO sheetDataVO = new TemplateSourceDataVO.SheetDataVO();
        sheetDataVO.setSheetNo(sheetDataVOS.size());
        sheetDataVO.setSheetName(this.getI18nMessageByKey(ProductConstant.VC_SHEET_NAME_FIRST, StringUtils.isNotBlank(batchParamVO.getLang()) ? batchParamVO.getLang() : LangConstant.LANG_EN));
        // 表头
        TemplateHeadSupportVO headSupportVo = templateSupportService.templateHeadSupportVo(batchParamVO);
        this.hiddenHeadList = headSupportVo.getHiddenHeadList();
        if(batchParamVO.getSourceCountryCode().equals(CountryConstant.COUNTRY_ZH)){
            errorIndex += 1;
        }

        String lang = StringUtils.isBlank(batchParamVO.getLang()) ? LangConstant.LANG_EN : batchParamVO.getLang();
        List<List<String>> headList = headSupportVo.getHeadList();
        fillFirstRowCell(headList,lang);
        log.info("ParentCreateProductExcelTemplateService.getSheetDataVoList headList={}", JSON.toJSONString(headList));
        sheetDataVO.setHeadList(headList);
        sheetDataVO.setDropdownDataVO(super.fillDropdownDataVO(headSupportVo));
        log.info("ParentCreateProductExcelTemplateService.getSheetDataVoList 表格数据 sheetDataVO={}",JSON.toJSONString(sheetDataVO));
        Map<Integer,Short> headRowHeightMap = Maps.newHashMap();
        headRowHeightMap.put(1,new Short("40"));
        headRowHeightMap.put(2,new Short("120"));
        sheetDataVO.setWriteHandlerList(Lists.newArrayList(
                new SpecialRowHeightStyleStrategy(new Short("40"), new Short("40"),headRowHeightMap)
                ,new HeadNoMergeWriteHandler(Sets.newHashSet(2),Sets.newHashSet(0))
                // 跨境模板冻结2列，本土冻结1列
                ,new FreezePaneWriteHandler( 1,3)
                ,new AutoSizeColumnWriterHandler(Sets.newHashSet(0,1))
                ,new CreateProductHeadGroupColumnSheetWriterHandler(1)
                ,new HeadBackgroundColorCellWriterHandler(Sets.newHashSet(1,2))
        ));
        sheetDataVOS.add(sheetDataVO);
    }

    /**
     * 添加一个隐藏的工作表到模板数据中。
     * @param sheetDataVOS 包含所有工作表数据的列表。
     */
    public void hiddenSheetData(ArrayList<TemplateSourceDataVO.SheetDataVO> sheetDataVOS) {
        TemplateSourceDataVO.SheetDataVO hiddenSheetData = new TemplateSourceDataVO.SheetDataVO();
        hiddenSheetData.setSheetNo(sheetDataVOS.size());
        hiddenSheetData.setSheetName(ProductConstant.HIDDEN_SHEET_NAME);
        // 隐藏表格表头
        hiddenSheetData.setHeadList(hiddenHeadList);
        hiddenSheetData.setWriteHandlerList(Lists.newArrayList(new HiddenSheetWriteHandler(ProductConstant.HIDDEN_SHEET_NAME)));
        sheetDataVOS.add(hiddenSheetData);
    }

    private void fillFirstRowCell(List<List<String>> headList,String lang){
        if (CollectionUtils.isEmpty(headList)) {
            return;
        }

        for (List<String> columns : headList){
            String cellValue = columns.get(0);
            if (StringUtils.isBlank(cellValue)){
                columns.set(0,templateSupportService.getI18nMessage(TEMPLATE_FIRST_ROW_GROUP_TIP_I18N,lang));
            }
        }
    }
}
