package com.jdi.isc.task.center.service.support.excel;


import com.alibaba.excel.constant.OrderConstant;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.jdi.isc.task.center.common.costants.Constant;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.List;

/**
 * 批量发品表头行实现列分组隐藏处理器
 * <AUTHOR>
 * @description：CreateProductHeadGroupColumnRowWriterHandler
 * @Date 2025-08-05
 */
public class CreateProductHeadGroupColumnSheetWriterHandler implements SheetWriteHandler {

    @Override
    public int order() {
        return OrderConstant.FILL_STYLE + 4;
    }
    /**
     * 表头行集合
     */
    private Integer groupRowIndex;

    public CreateProductHeadGroupColumnSheetWriterHandler() {
    }


    public CreateProductHeadGroupColumnSheetWriterHandler(Integer groupRowIndex) {
        this.groupRowIndex = groupRowIndex;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        // 单元格按列分组
        groupColumn(writeSheetHolder);

        Sheet sheet = writeSheetHolder.getSheet();

        Workbook workbook = sheet.getWorkbook();

    }

    private void groupColumn(WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();

        // 添加这两句非常关键
        sheet.setRowSumsBelow(false);
        sheet.setRowSumsRight(true);


        List<List<String>> headList = writeSheetHolder.getHead();
        // 获取行中的单元格数量
        int cellTotalNum = headList.size();
        // 遍历单元格，计算隐藏列索引
        int startGroupIndex = -1;
        for (int i = 0; i < cellTotalNum; i++) {
            String valueStr = headList.get(i).get(groupRowIndex);
            if (StringUtils.isNotBlank(valueStr) && valueStr.startsWith(Constant.STAR)){
                if (startGroupIndex != -1) {
                    int endGroupIndex = i - 1;
                    System.out.println("正在为 Excel 应用分组: " + startGroupIndex + " -> " + endGroupIndex);
                    sheet.groupColumn(startGroupIndex, endGroupIndex);
                    sheet.setColumnGroupCollapsed(startGroupIndex , true); // 初始折叠状态
                    startGroupIndex = -1;
                }
            }else {
                if (startGroupIndex == -1) {
                    startGroupIndex = i;
                }
            }
        }

        // 获取最后一列的值
        String valueStr = headList.get(cellTotalNum -1).get(1);
        // 最后一列是结果时，“结果”列不隐藏不分组
        if (startGroupIndex != -1 && "结果".equals(valueStr)){
            // 去除结果中
            int endGroupIndex = cellTotalNum - 2;
            System.out.println("正在为 Excel 应用最后一个分组: " + startGroupIndex + " -> " + endGroupIndex);
            sheet.groupColumn(startGroupIndex, endGroupIndex);
            sheet.setColumnGroupCollapsed(startGroupIndex, true); // 初始折叠状态
        }
        // 处理以非星号列结尾的情况,最后一列不是结果或“Result”时，设置隐藏
        if (startGroupIndex != -1 && !"Result".equals(valueStr) && !"结果".equals(valueStr)) {
            // 去除结果中的
            int endGroupIndex = cellTotalNum - 1;
            System.out.println("正在为 Excel 应用最后一个分组: " + startGroupIndex + " -> " + endGroupIndex);
            sheet.groupColumn(startGroupIndex, endGroupIndex);
            sheet.setColumnGroupCollapsed(startGroupIndex , true); // 初始折叠状态
        }
    }
}
