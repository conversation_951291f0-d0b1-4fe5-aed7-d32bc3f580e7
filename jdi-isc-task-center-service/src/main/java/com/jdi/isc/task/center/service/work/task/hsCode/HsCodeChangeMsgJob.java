package com.jdi.isc.task.center.service.work.task.hsCode;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.binlog.client.WaveEntry;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.customs.IscProductCustomsReadApiService;
import com.jdi.isc.product.soa.api.customs.req.ProductCustomsApiDTO;
import com.jdi.isc.product.soa.api.customs.req.ProductCustomsPageApiDTO;
import com.jdi.isc.product.soa.api.hsCode.common.HsCodeChangeMsgApiService;
import com.jdi.isc.product.soa.api.hsCode.common.HsCodeChangeMsgSkuApiService;
import com.jdi.isc.product.soa.api.hsCode.common.HsCodeChangeMsgWriteApiService;
import com.jdi.isc.product.soa.api.hsCode.common.biz.HsCodeChangeMsgApiDTO;
import com.jdi.isc.product.soa.api.hsCode.common.biz.HsCodeChangeMsgSkuApiDTO;
import com.jdi.isc.product.soa.api.hsCode.common.req.*;
import com.jdi.isc.product.soa.api.hsCode.common.res.HsCodeChangeMsgSkuSaveUpdateBatchResApiDTO;
import com.jdi.isc.product.soa.api.hsCode.common.res.HsCodeChangeMsgUpdateResApiDTO;
import com.jdi.isc.product.soa.api.taxRate.req.BrImportTaxReqDTO;
import com.jdi.isc.product.soa.price.api.price.req.PriceRefreshVO;
import com.jdi.isc.task.center.common.costants.CacheKeyConstant;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.common.costants.LogKeyWordAlertConstant;
import com.jdi.isc.task.center.common.ducc.TaskDuccConfigService;
import com.jdi.isc.task.center.common.utils.JimUtils;
import com.jdi.isc.task.center.domain.binlake.ChangeTableEventMsg;
import com.jdi.isc.task.center.domain.enums.hsCode.HsCodeChangeMsgDealStatusEnum;
import com.jdi.isc.task.center.domain.hsCode.biz.HsCodeChangeMsgConfig;
import com.jdi.isc.task.center.domain.hsCode.biz.HsCodeChangeMsgJobContext;
import com.jdi.isc.task.center.domain.hsCode.biz.HsCodeChangeSkuConfig;
import com.jdi.isc.task.center.domain.hsCode.biz.HsCodeSkuInfoChangeMsgVO;
import com.jdi.isc.task.center.domain.sku.po.SkuPO;
import com.jdi.isc.task.center.rpc.price.IscProductSoaPriceWriteRpcService;
import com.jdi.isc.task.center.rpc.suport.AlertHelper;
import com.jdi.isc.task.center.rpc.taxRate.IscTaxRateWriteApiRpcService;
import com.jdi.isc.task.center.rpc.utils.ExceptionUtil;
import com.jdi.isc.task.center.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.task.center.service.manage.msg.TaskCenterMsgSender;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 处理HsCode变更任务，触发商品价格重新计算
 * @Author: zhaojianguo21
 * @Date: 2025/07/22 22:56
 **/

@Slf4j
@Component
public class HsCodeChangeMsgJob {

    @Resource
    private HsCodeChangeMsgApiService hsCodeChangeMsgApiService;
    @Resource
    private HsCodeChangeMsgWriteApiService hsCodeChangeMsgWriteApiService;
    @Resource
    private IscProductCustomsReadApiService iscProductCustomsReadApiService;
    @Resource
    private IscProductSoaPriceWriteRpcService iscProductSoaPriceWriteRpcService;
    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private TaskDuccConfigService taskDuccConfigService;

    @Resource
    private IscTaxRateWriteApiRpcService iscTaxRateRpcService;
    @Resource
    private HsCodeChangeMsgSkuApiService hsCodeChangeMsgSkuApiService;
    @Resource
    private JimUtils jimUtils;
    @Resource
    private TaskCenterMsgSender taskCenterMsgSender;

    @Value("${topic.jmq4.producer.hsCodeSkuInfoChangeTopic}")
    private String hsCodeSkuInfoChangeTopic;

    @Resource
    private AsyncTaskExecutor hsCodeExecutor;

    private static final Set<String> dbFields = Sets.newHashSet(/*"country_code", "client_code",*/ "industry_product_tax");

    @SneakyThrows
    @PFTracing
    @XxlJob("hsCodeChangeMsgDealJob")
    public ReturnT<String> hsCodeChangeMsgDealJob(String param) {
        log.info("hsCodeChangeMsgDealJob, start... xxlJobParam={}", param);
        hsCodeExecutor.execute(() -> {
            try {
                log.info("hsCodeChangeMsgDealJob, hsCodeExecutor xxlJobParam={}", param);
                doHsCodeChangeMsgDealJob(param);
            } catch (Exception e) {
                log.error("hsCodeChangeMsgDealJob hsCodeExecutor exception.", e);
            }
        } );

        return ReturnT.SUCCESS;
    }

    public void doHsCodeChangeMsgDealJob(String param){
        String uuid = UUID.randomUUID().toString();
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.HS_CODE_CHANGE_MSG_JOB_KEY, "All");
        try {
            // 长时间任务，锁时长目前先设置为180s
            Boolean lockRes = jimUtils.simpleLock(lockKey, uuid, 180);
            log.info("doHsCodeChangeMsgDealJob, lockRes={}, lockKey={}", lockRes, lockKey);
            //获取分布式锁
            if(!lockRes){
                log.error("doHsCodeChangeMsgDealJob, acquire HS_CODE_CHANGE_MSG_JOB_KEY lock fail ，"+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+" lockKey={}, input={}", lockKey, param);
                // 目前先返回成功
                return ;
            }

            hsCodeChangeMsgPageScan();
        }catch (Exception e) {
            log.error("doHsCodeChangeMsgDealJob, error,"+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+" requestId={}, param={}", uuid, param, e);
        }finally {
            log.info("doHsCodeChangeMsgDealJob, simpleLockRelease, lockKey={}", lockKey);
            //释放分布式锁
            jimUtils.simpleLockRelease(lockKey, uuid);
        }
    }

    /**
     * 分页处理所有的hsCode变更消息
     * @return
     */
    private void hsCodeChangeMsgPageScan(){
        log.info("hsCodeChangeMsgPageScan, start...");
        HsCodeChangeMsgJobContext context = new HsCodeChangeMsgJobContext();
        context.setTimeEnd(System.currentTimeMillis());
        context.setDealBatch(UUID.randomUUID().toString());

        Stopwatch stopwatch = Stopwatch.createStarted();
        HsCodeChangeMsgConfig monitorConfig = taskDuccConfigService.hsCodeChangeMsgConfig();

        // 更新标识本次要处理的数据
        HsCodeChangeMsgUpdateReqApiDTO updateDealBatchReq = new HsCodeChangeMsgUpdateReqApiDTO();
        updateDealBatchReq.setCreateTimeEnd(context.getTimeEnd());
        updateDealBatchReq.setDealBatch(context.getDealBatch());
        updateDealBatchReq.setOperator(Constant.SYSTEM);
        updateDealBatchReq.setOperateTime(System.currentTimeMillis());
        DataResponse<HsCodeChangeMsgUpdateResApiDTO> updateDealBatchRes = hsCodeChangeMsgWriteApiService.updateDealBatch(updateDealBatchReq);
        log.info("dealHsCodeChangeMsgBatch, updateDealBatchRes={}, updateDealBatchReq={}", JSONObject.toJSONString(updateDealBatchRes), JSONObject.toJSONString(updateDealBatchReq));
        if (null==updateDealBatchRes || !Boolean.TRUE.equals(updateDealBatchRes.getSuccess())){
            log.error("hsCodeChangeMsgPageScan, invoke updateDealBatch fail. "+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+" updateDealBatchRes={}, updateDealBatchReq={}"
                    , JSONObject.toJSONString(updateDealBatchRes), updateDealBatchReq);
            return ;
        }

        // 通过分页接口获取总页数
        HsCodeChangeMsgPageReqApiDTO pageReq = new HsCodeChangeMsgPageReqApiDTO();
        pageReq.setIndex(1L);
        // 第一次请求，size为1即可
        pageReq.setSize(1L);
        pageReq.setCreateTimeEnd(context.getTimeEnd());
        pageReq.setDealBatch(context.getDealBatch());
        DataResponse<PageInfo<HsCodeChangeMsgApiDTO>> queryTotalRes = hsCodeChangeMsgApiService.pageForJob(pageReq);
        log.info("hsCodeChangeMsgPageScan, queryTotalRes={}", JSONObject.toJSONString(queryTotalRes));

        if (null==queryTotalRes || !Boolean.TRUE.equals(queryTotalRes.getSuccess()) || null==queryTotalRes.getData()){
            log.error("hsCodeChangeMsgPageScan, invoke pageSearch fail. "+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+" queryTotalRes={}", JSONObject.toJSONString(queryTotalRes));
            return ;
        }

        // 总条数
        long total = queryTotalRes.getData().getTotal();
        if (total<=0){
            log.warn("hsCodeChangeMsgPageScan, no data, return. queryTotalRes={}", JSONObject.toJSONString(queryTotalRes));
            return ;
        }

        // 总页数，确定总页数。
        long pageSize = null!=monitorConfig.getScanPageSize() && monitorConfig.getScanPageSize()<=1000?
                monitorConfig.getScanPageSize(): Constant.BATCH_SIZE_100;
        long pageTotalNum = total%pageSize==0? total/pageSize : (total/pageSize+1);
        long startPageNum  = null!=monitorConfig.getScanStartPageNum() ?
                monitorConfig.getScanStartPageNum():1L;
        log.info("hsCodeChangeMsgPageScan, total={}, pageSize={}, pageTotalNum={}, startPageNum={}, monitorConfig={}"
                , total, pageSize, pageTotalNum, startPageNum, JSONObject.toJSONString(monitorConfig));

        // 开始分页处理
        pageReq.setIndex(1L);
        pageReq.setSize(pageSize);

        Stopwatch stopwatchCurrentPage = Stopwatch.createUnstarted();
        for (long i=startPageNum;i<=pageTotalNum;i++){
            try {
                stopwatchCurrentPage.reset().start();
                // 每次都获取最新配置，立马感知到配置变化
                monitorConfig = taskDuccConfigService.hsCodeChangeMsgConfig();
                log.info("hsCodeChangeMsgPageScan, loop start, pageNum={}" ,i);
                if (null!=monitorConfig.getScanEndPageNum() && i>monitorConfig.getScanEndPageNum()){
                    log.error("hsCodeChangeMsgPageScan, loop break,"+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+" pageNum={}" ,i);
                    break;
                }

                // 第几页
                pageReq.setIndex(i);
                // 查当前页数据
                DataResponse<PageInfo<HsCodeChangeMsgApiDTO>> pageRecord = hsCodeChangeMsgApiService.pageForJob(pageReq);

                if (null==pageRecord || !Boolean.TRUE.equals(pageRecord.getSuccess()) || null==pageRecord.getData()
                        || CollectionUtils.isEmpty(pageRecord.getData().getRecords())){
                    log.error("hsCodeChangeMsgPageScan, loop, pageNum={}， pageRecord null."+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+" pageRecord={}, pageReq={}"
                            ,i, JSONObject.toJSONString(pageRecord), JSONObject.toJSONString(pageReq));
                    continue;
                }
                log.info("hsCodeChangeMsgPageScan, loop, pageNum={}， pageRecord size={}" ,i, null!=pageRecord.getData()?pageRecord.getData().getSize():0);

                dealHsCodeChangeMsgBatch(pageRecord.getData().getRecords(), context);

                // 当前页处理结束
                stopwatchCurrentPage.stop();

                // 不要在计时器中间休眠，否则会计算上休眠时间
                Thread.sleep( (null!=monitorConfig && null!=monitorConfig.getScanPageSleepTime() && monitorConfig.getScanPageSleepTime()>0)?monitorConfig.getScanPageSleepTime():50);
            } catch (Exception e) {
                log.error("hsCodeChangeMsgPageScan, loop exception. "+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+"  pageNum={}", i, e);
            }
            log.info("hsCodeChangeMsgPageScan, loop end, pageNum={}, current page elapsedTime={}",i, stopwatchCurrentPage.elapsed(TimeUnit.MILLISECONDS));
        }

        log.info("hsCodeChangeMsgPageScan, end..., total={}, pageTotalNum={}, elapsedTime={}", total, pageTotalNum, stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }

    private void dealHsCodeChangeMsgBatch(List<HsCodeChangeMsgApiDTO> input, HsCodeChangeMsgJobContext context){
        // 处理当前页数据，循环处理hsCode
        for (HsCodeChangeMsgApiDTO codeChangeMsgApiDTO : input){
            DataResponse res = null;
            try {
                res = hsCodeChangeSkuPageScan(codeChangeMsgApiDTO, context);
            } catch (Exception e) {
                log.error("dealHsCodeChangeMsgBatch, invoke dealHsCodeChangeMsg exception."+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+" codeChangeMsgApiDTO={}", JSONObject.toJSONString(codeChangeMsgApiDTO), e);
            }

            // 更新状态
            HsCodeChangeMsgUpdateReqApiDTO updateReq = new HsCodeChangeMsgUpdateReqApiDTO();
            updateReq.setIds(Sets.newHashSet(codeChangeMsgApiDTO.getId()));
            updateReq.setStatus(null!=res && Boolean.TRUE.equals(res.getSuccess())?HsCodeChangeMsgDealStatusEnum.SUCCESS.getCode():HsCodeChangeMsgDealStatusEnum.FAIL.getCode());
            updateReq.setOperator(Constant.SYSTEM);
            updateReq.setOperateTime(System.currentTimeMillis());
            DataResponse<HsCodeChangeMsgUpdateResApiDTO> updateRes = hsCodeChangeMsgWriteApiService.updateStatus(updateReq);
            log.info("dealHsCodeChangeMsgBatch, updateRes={}, updateReq={}", JSONObject.toJSONString(updateRes), JSONObject.toJSONString(updateReq));
        }
    }

    /**
     * 处理某个hsCodeChangeMsg消息。分页处理关联的商品。一个hsCode关联多个 jdSku。循环处理每一个jdSku
     * @param input
     * @return
     */
    private DataResponse hsCodeChangeSkuPageScan(HsCodeChangeMsgApiDTO input, HsCodeChangeMsgJobContext context){
        log.info("hsCodeChangeSkuPageScan, start... hsCode={}", input.getHsCode());
        Stopwatch stopwatch = Stopwatch.createStarted();

        HsCodeChangeSkuConfig monitorConfig = taskDuccConfigService.hsCodeChangeSkuConfig();
        // 通过分页接口获取总页数
        ProductCustomsPageApiDTO pageReq = new ProductCustomsPageApiDTO();
        pageReq.setIndex(1L);
        // 第一次请求，size为1即可
        pageReq.setSize(1L);
        pageReq.setHsCode(input.getHsCode());
        pageReq.setCountryCode(input.getCountryCode());
        pageReq.setCreateTimeEnd(context.getTimeEnd());
        pageReq.setOperator(Constant.SYSTEM);

        DataResponse<PageInfo<ProductCustomsApiDTO>> queryTotalRes = iscProductCustomsReadApiService.pageForJobHsCodeMsg(pageReq);

        if (null==queryTotalRes || !Boolean.TRUE.equals(queryTotalRes.getSuccess()) || null==queryTotalRes.getData()){
            log.error("hsCodeChangeSkuPageScan, invoke page fail."+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+" queryTotalRes={}, hsCode={}"
                    , JSONObject.toJSONString(queryTotalRes), input.getHsCode());
            return  DataResponse.error("查询hsCode关联的sku失败");
        }

        // 总条数
        long total = queryTotalRes.getData().getTotal();
        if (total<=0){
            log.warn("hsCodeChangeSkuPageScan, no data, return. queryTotalRes={}, hsCode={}", JSONObject.toJSONString(queryTotalRes), input.getHsCode());
            return  DataResponse.success();
        }
        // 总页数，确定总页数。
        long pageSize = null!=monitorConfig.getScanPageSize() && monitorConfig.getScanPageSize()<=1000?
                monitorConfig.getScanPageSize(): Constant.BATCH_SIZE_100;
        long pageTotalNum = total%pageSize==0? total/pageSize : (total/pageSize+1);
        long startPageNum  = null!=monitorConfig.getScanStartPageNum() ?
                monitorConfig.getScanStartPageNum():1L;
        log.info("hsCodeChangeSkuPageScan, total={}, pageSize={}, pageTotalNum={}, startPageNum={}, monitorConfig={}, hsCode={}"
                , total, pageSize, pageTotalNum, startPageNum, JSONObject.toJSONString(monitorConfig), input.getHsCode());

        // 开始分页处理。一个hsCode关联多个 jdSku。循环处理关联的jdSku
        pageReq.setIndex(1L);
        pageReq.setSize(pageSize);

        Stopwatch stopwatchCurrentPage = Stopwatch.createUnstarted();
        for (long i=startPageNum;i<=pageTotalNum;i++){
            try {
                stopwatchCurrentPage.reset().start();
                // 每次都获取最新配置，立马感知到配置变化
                monitorConfig = taskDuccConfigService.hsCodeChangeSkuConfig();
                log.info("hsCodeChangeSkuPageScan, loop start, pageNum={}, hsCode={}" ,i, input.getHsCode());
                if (null!=monitorConfig.getScanEndPageNum() && i>monitorConfig.getScanEndPageNum()){
                    log.error("hsCodeChangeSkuPageScan, loop break,"+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+" pageNum={}, hsCode={}" ,i, input.getHsCode());
                    break;
                }

                // 第几页
                pageReq.setIndex(i);
                // 查当前页数据
                DataResponse<PageInfo<ProductCustomsApiDTO>> pageRecord = iscProductCustomsReadApiService.page(pageReq);

                if (null==pageRecord || !Boolean.TRUE.equals(pageRecord.getSuccess()) || null==pageRecord.getData() || CollectionUtils.isEmpty(pageRecord.getData().getRecords())){
                    log.error("hsCodeChangeSkuPageScan, loop, pageNum={}， pageRecord null."+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+" pageRecord={}" ,i, JSONObject.toJSONString(pageRecord));
                    continue;
                }
                List<ProductCustomsApiDTO> pageRecordData = pageRecord.getData().getRecords();
                log.info("hsCodeChangeSkuPageScan, loop, pageNum={}， pageRecord size={}, hsCode={}" ,i, null!=pageRecordData?pageRecordData.size():0, input.getHsCode());

                List<BrImportTaxReqDTO> syncTaxReqList = Lists.newArrayList();

                // 处理当前页数据
                doHsCodeChangeSku(input, pageRecordData, syncTaxReqList);

                // 当前页处理结束
                stopwatchCurrentPage.stop();

                // 不要在计时器中间休眠，否则会计算上休眠时间
                Thread.sleep( (null!=monitorConfig && null!=monitorConfig.getScanPageSleepTime() && monitorConfig.getScanPageSleepTime()>0)?monitorConfig.getScanPageSleepTime():50);
            } catch (Exception e) {
                log.error("hsCodeChangeSkuPageScan, loop exception."+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+" pageNum={}, hsCode={}", i, input.getHsCode(), e);
            }
            log.info("hsCodeChangeSkuPageScan, loop end, pageNum={}, current page elapsedTime={}, hsCode={}",i, stopwatchCurrentPage.elapsed(TimeUnit.MILLISECONDS), input.getHsCode());
        }

        log.info("hsCodeChangeSkuPageScan, end..., total={}, pageTotalNum={}, elapsedTime={}, hsCode={}", total, pageTotalNum, stopwatch.elapsed(TimeUnit.MILLISECONDS), input.getHsCode());
        return DataResponse.success();
    }

    private void doHsCodeChangeSku(HsCodeChangeMsgApiDTO msgApiDto, List<ProductCustomsApiDTO> msgList, List<BrImportTaxReqDTO> syncTaxReqList ) {
        String hsCode = msgApiDto.getHsCode();
        if (CollectionUtils.isEmpty(msgList)){
            log.warn("doHsCodeChangeSku, msgList is empty. hsCode={}", hsCode);
            return ;
        }

        Set<Long> jdSkuIds = msgList.stream().filter(Objects::nonNull).map(ProductCustomsApiDTO::getJdSkuId).collect(Collectors.toSet());
        List<SkuPO> skuPoList = skuAtomicService.getSkuPoByJdSkuIds(jdSkuIds);
        if (CollectionUtils.isEmpty(skuPoList)){
            log.warn("doHsCodeChangeSku, skuPoList is empty. jdSkuIds={}, hsCode={}", JSONObject.toJSONString(jdSkuIds), hsCode);
            return ;
        }
        Set<Long> skuIds = skuPoList.stream().map(SkuPO::getSkuId).collect(Collectors.toSet());
        Map<Long,List<SkuPO>> skuMap = skuPoList.stream().collect(Collectors.groupingBy(SkuPO::getJdSkuId));

        // 查关联的已处理过的sku
        HsCodeChangeMsgSkuQueryReqApiDTO msgSkuQueryReqApiDTO = new HsCodeChangeMsgSkuQueryReqApiDTO();
        msgSkuQueryReqApiDTO.setHsCode(msgApiDto.getHsCode());
        msgSkuQueryReqApiDTO.setSkuIds(skuIds);
        msgSkuQueryReqApiDTO.setHsCodeChangeMsgId(msgApiDto.getId());
        msgSkuQueryReqApiDTO.setCountryCode(msgApiDto.getCountryCode());
        DataResponse<List<HsCodeChangeMsgSkuApiDTO>> msgSkuRes = hsCodeChangeMsgSkuApiService.queryList(msgSkuQueryReqApiDTO);
        if (null==msgSkuRes || !Boolean.TRUE.equals(msgSkuRes.getSuccess())){
            log.error("doHsCodeChangeSku, msgSkuRes res error."+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+" msgSkuRes={}, hsCode={}"
                    , JSONObject.toJSONString(msgSkuRes), hsCode);
            return ;
        }
        List<HsCodeChangeMsgSkuApiDTO> msgSkuResData = msgSkuRes.getData();
        Map<Long,HsCodeChangeMsgSkuApiDTO> msgSkuMap = null;
        if (CollectionUtils.isNotEmpty(msgSkuResData)){
            msgSkuMap = msgSkuResData.stream().collect(Collectors.toMap(HsCodeChangeMsgSkuApiDTO::getSkuId, Function.identity()));
        }else {
            log.warn("doHsCodeChangeSku, msgSkuResData is empty. hsCode={}", hsCode);
            msgSkuMap = Maps.newHashMap();
        }

        // 要发送的topic消息
        Map<String, Object> hsCodeSkuInfoChangeMsgMap = new HashMap<>();

        List<HsCodeChangeMsgSkuSaveUpdateReqApiDTO> saveReqList = new ArrayList<>();
        int count = 0;
        // 处理每个jdSkuId
        for (ProductCustomsApiDTO msg : msgList) {
            // 一个jdSkuId关联多个skuId
            List<SkuPO> skuRef = skuMap.get(msg.getJdSkuId());
            if(CollectionUtils.isEmpty(skuRef)){
                log.info("doHsCodeChangeSku not exists sku, ignore, 零售skuId: {}, hsCode={} " , msg.getJdSkuId(), hsCode);
                continue;
            }

            // 处理每个sku
            for(SkuPO po: skuRef){
                HsCodeChangeMsgSkuApiDTO msgSku = msgSkuMap.get(po.getSkuId());
                if (null!=msgSku && HsCodeChangeMsgDealStatusEnum.SUCCESS.getCode().equals(msgSku.getStatus())){
                    log.info("doHsCodeChangeSku done, ignore。零售skuId: {} 关联的skuId: {}, hsCode={}" , msg.getJdSkuId(), po.getSkuId(), hsCode);
                    continue;
                }

                // 构建topic消息
                HsCodeSkuInfoChangeMsgVO.HsCodeSkuInfoChangeMsgVOBuilder builder = HsCodeSkuInfoChangeMsgVO.builder();
                builder.hsCodeChangeMsgId(msgApiDto.getId()).bizId(msgApiDto.getBizId())
                        .countryCode(msgApiDto.getCountryCode()).hsCode(msgApiDto.getHsCode()).skuId(po.getSkuId()).jdSkuId(msg.getJdSkuId())
                        .hsCodeMsgBody(msgApiDto.getMsgBody()).hsCodeChangeMsgCreateTime(msgApiDto.getCreateTime());
                HsCodeSkuInfoChangeMsgVO topicMsg = builder.build();
                hsCodeSkuInfoChangeMsgMap.put(msgApiDto.getCountryCode()+"_"+msgApiDto.getHsCode()+"-"+po.getSkuId()+"-"+msgApiDto.getId(), topicMsg);

                // sku明细记录
                HsCodeChangeMsgSkuSaveUpdateReqApiDTO req = new HsCodeChangeMsgSkuSaveUpdateReqApiDTO();
                req.setId(null!=msgSku?msgSku.getId():null);
                req.setUniqueFlag(Integer.valueOf(++count).toString());
                req.setHsCodeChangeMsgId(msgApiDto.getId());
                req.setHsCode(msgApiDto.getHsCode());
                req.setCountryCode(msgApiDto.getCountryCode());
                req.setJdSkuId(msg.getJdSkuId());
                req.setSkuId(po.getSkuId());
                req.setStatus(HsCodeChangeMsgDealStatusEnum.SUCCESS.getCode());
                req.setUpdater(Constant.SYSTEM);
                req.setUpdateTime(System.currentTimeMillis());
                saveReqList.add(req);
            }
        }

        // 批量发送mq
        taskCenterMsgSender.sendMessageBatchRetry(hsCodeSkuInfoChangeMsgMap, hsCodeSkuInfoChangeTopic);

        // 保存关联sku处理结果
        HsCodeChangeMsgSkuSaveUpdateBatchReqApiDTO msgSkuSaveUpdateBatchReqApiDTO = new HsCodeChangeMsgSkuSaveUpdateBatchReqApiDTO();
        msgSkuSaveUpdateBatchReqApiDTO.setReqList(saveReqList);
        DataResponse<HsCodeChangeMsgSkuSaveUpdateBatchResApiDTO> saveMsgSkuRes = null;
        try {
            saveMsgSkuRes = hsCodeChangeMsgSkuApiService.saveOrUpdateBatch(msgSkuSaveUpdateBatchReqApiDTO);
            if (null==saveMsgSkuRes || !Boolean.TRUE.equals(saveMsgSkuRes.getSuccess())){
                log.error("doHsCodeChangeSku, saveMsgSkuRes res fail."+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+" saveMsgSkuRes={}, hsCode={}"
                        , JSONObject.toJSONString(saveMsgSkuRes), hsCode);
            }
        } catch (Exception e) {
            log.error("doHsCodeChangeSku, saveMsgSkuRes res exception."+ LogKeyWordAlertConstant.HS_CODE_CHANGE_MSG_ERROR+" saveMsgSkuRes={}, msgSkuSaveUpdateBatchReqApiDTO={}, hsCode={}"
                    , JSONObject.toJSONString(saveMsgSkuRes), JSONObject.toJSONString(msgSkuSaveUpdateBatchReqApiDTO), hsCode, e);
        }

        // 构建同步巴西ipi税率请求数据
        // this.buildBrImportTaxReqData(msgApiDto, msgList, syncTaxReqList, skuMap);

    }

    private void buildBrImportTaxReqData(HsCodeChangeMsgApiDTO msgApiDto, List<ProductCustomsApiDTO> msgList, List<BrImportTaxReqDTO> syncTaxReqList, Map<Long, List<SkuPO>> skuMap) {
        for (ProductCustomsApiDTO msg : msgList) {
            if (CountryConstant.COUNTRY_BR.equals(msg.getCountryCode())) {
                continue;
            }

            String msgBody = msgApiDto.getMsgBody();

            if (StringUtils.isEmpty(msgBody)) {
                log.error("msgBody is null. msg={}", JSONObject.toJSONString(msg));
               continue;
            }

            ChangeTableEventMsg event;
            try {
                event = JSONObject.parseObject(msgBody, ChangeTableEventMsg.class);
            } catch (Exception e) {
                log.warn("解析 msgBody 失败. msg={}, message={}", msgBody, ExceptionUtil.getMessage(e, "处理巴西ipi税率，解析 msgBody 失败"),  e);
                continue;
            }

            List<BrImportTaxReqDTO> list;
            try {
                list = this.filterAndTransform(event, skuMap);
            } catch (Exception e) {
                log.warn("filterAndTransform 失败. event={}， message={}", event, ExceptionUtil.getMessage(e, "处理巴西ipi税率，解析 filterAndTransform 失败"), e);
                continue;
            }

            if (CollectionUtils.isNotEmpty(list)) {
                syncTaxReqList.addAll(list);
            }
        }
    }

    private void syncBrCustomerPriceIpiTax(List<BrImportTaxReqDTO> targets) {
        log.info("BrJdSkuTaxBinlakeListener.filterAndTransform, data={}", JSON.toJSONString(targets));
        if (CollectionUtils.isNotEmpty(targets)) {
            log.info("syncBrCustomerPriceIpiTax, targets is null");
            return;
        }

        for(BrImportTaxReqDTO brImportTaxReq : targets){
            DataResponse<Boolean> response = iscTaxRateRpcService.updateBrIpiTax(brImportTaxReq);
            if (response == null || !response.getSuccess()) {
                log.warn("syncBrCustomerPriceIpiTax 失败. targets={}", JSONObject.toJSONString(targets));
                AlertHelper.p0("更新巴西税率失败，请尽快处理", String.valueOf(brImportTaxReq.getSkuId()));
            }
        }
    }


    private List<BrImportTaxReqDTO> filterAndTransform(ChangeTableEventMsg eventMsg, Map<Long,List<SkuPO>> skuMap) {
        List<BrImportTaxReqDTO> result = Lists.newArrayList();
//        List<Long> testSkuIds = operDuccConfig.getTestSkuIds();
//        if(CollectionUtils.isEmpty(testSkuIds)){
//            return result;
//        }
        if(WaveEntry.EventType.DELETE.equals(eventMsg.getChangeType())){
            return Lists.newArrayList();
        }

        long count = dbFields.stream().filter(eventMsg.getChangeFields()::containsKey).count();

        if (count < 1) {
            log.info("BrJdSkuTaxBinlakeListener.filterAndTransform 价格变更字段为空, 跳过推送价格数据");
            return Lists.newArrayList();
        }

        log.info("BrJdSkuTaxBinlakeListener.filterAndTransform eventMsg={}", JSONObject.toJSONString(eventMsg));;

        String jdSkuIdAfterStr = eventMsg.getRowAfter().get("jd_sku_id");
        if(StringUtils.isBlank(jdSkuIdAfterStr)){
            log.error("BrJdSkuTaxBinlakeListener.filterAndTransform jdSkuIdAfterStr is null");
            return Lists.newArrayList();
        }
        String ipiRate = eventMsg.getAfterValueStr("industry_product_tax");
        String updater = eventMsg.getAfterValueStr("updater");

        Long jdSkuId = Long.valueOf(jdSkuIdAfterStr);

        List<SkuPO> skuPOList = skuMap.get(jdSkuId);
        if(CollectionUtils.isEmpty(skuPOList)){
            log.error("BrJdSkuTaxBinlakeListener.filterAndTransform skuPOList is null， jdSkuId={}", jdSkuId);
            return Lists.newArrayList();
        }

        for(SkuPO skuPO : skuPOList){
//                if(!testSkuIds.contains(skuPO.getSkuId())){
//                    continue;
//                }
            BrImportTaxReqDTO brImportTaxReqDTO = new BrImportTaxReqDTO();
            brImportTaxReqDTO.setSkuId(skuPO.getSkuId());
            brImportTaxReqDTO.setPin(updater);
            brImportTaxReqDTO.setIpiRate(new BigDecimal(ipiRate));
            result.add(brImportTaxReqDTO);
        }
        return result;
    }
}
