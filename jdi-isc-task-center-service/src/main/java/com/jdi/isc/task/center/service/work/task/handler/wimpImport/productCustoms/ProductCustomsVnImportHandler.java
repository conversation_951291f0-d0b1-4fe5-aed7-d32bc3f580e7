package com.jdi.isc.task.center.service.work.task.handler.wimpImport.productCustoms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.customs.req.ProductCustomsDraftApiDTO;
import com.jdi.isc.product.soa.api.hsCode.vn.req.HsCodeVnSaveUpdateReqApiDTO;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.common.exception.TaskBizException;
import com.jdi.isc.task.center.common.utils.ExcelUtils;
import com.jdi.isc.task.center.domain.enums.customs.CustomsBrandLicensingEnum;
import com.jdi.isc.task.center.domain.enums.customs.CustomsControlsEnum;
import com.jdi.isc.task.center.domain.enums.customs.CustomsDataSourceEnum;
import com.jdi.isc.task.center.domain.enums.customs.ProductCustomsOperateEnum;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;
import com.jdi.isc.task.center.domain.task.excel.productCustoms.ProductCustomsVnExcelDTO;
import com.jdi.isc.task.center.rpc.customs.IscProductCustomsDraftWriteRpcService;
import com.jdi.isc.task.center.service.work.task.DisposableAbsJob;
import com.jdi.isc.task.center.service.work.task.frame.JobExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@JobExecutor(taskBizType = TaskBizTypeEnum.PRODUCT_CUSTOMS_VN_IMPORT)
public class ProductCustomsVnImportHandler extends DisposableAbsJob<ProductCustomsVnExcelDTO> {

    @Resource
    private IscProductCustomsDraftWriteRpcService iscProductCustomsDraftWriteRpcService;

    @Override
    public void run(TaskDTO<ProductCustomsVnExcelDTO> task) {
        log.info("run, taskId={}, data={} " ,task.getTaskId(), JSON.toJSONString(task.getTarget()));
        List<ProductCustomsVnExcelDTO> excelDTOList = task.getTarget();

        checkData(excelDTOList);

        String batchId = UUID.randomUUID().toString();
        for(ProductCustomsVnExcelDTO excelDto: excelDTOList ){
            try {
                doRunRpc(task, excelDto, batchId);
            } catch (Exception e) {
                log.error("run 任务 {} 执行失败", task.getTaskId(), e);
            }
        }
    }

    private void doRunRpc(TaskDTO<ProductCustomsVnExcelDTO> task, ProductCustomsVnExcelDTO excelDTO, String batchId){
        // 未校验通过的数据不处理
        if (Boolean.FALSE.equals(excelDTO.getValid())){
            return;
        }

        ProductCustomsDraftApiDTO apiDTO = null;
        try {
            // 组装请求参数
            apiDTO = excel2ApiDto(task, excelDTO, batchId);
            DataResponse rpcRes = iscProductCustomsDraftWriteRpcService.saveOrUpdate(apiDTO);
            // 响应失败
            if (!Boolean.TRUE.equals(rpcRes.getSuccess()) ) {
                log.info("doRunRpc, rpc fail. rpcRes={}", JSONObject.toJSONString(rpcRes));
                excelDTO.failed(rpcRes.getMessage());
                return;
            }

            excelDTO.success();
        }catch (TaskBizException e){
            excelDTO.failed(e.getMessage());
        }catch (Exception e) {
            log.error("doRunRpc, Exception excelDTO={}", JSONObject.toJSONString(excelDTO), e);
            excelDTO.failed("出错了");
        }
    }

    private ProductCustomsDraftApiDTO excel2ApiDto(TaskDTO<ProductCustomsVnExcelDTO> task, ProductCustomsVnExcelDTO excelDto, String batchId){
        ProductCustomsDraftApiDTO target = new ProductCustomsDraftApiDTO();
        try {
            target.setJdSkuId(excelDto.getJdSkuId());
            target.setSkuId(excelDto.getSkuId());
            target.setDeclarationName(excelDto.getDeclarationName());
            target.setDeclarationLocalName(excelDto.getDeclarationLocalName());
            if("是".equals(excelDto.getControls())){
                target.setControls(CustomsControlsEnum.REQUIRED.getCode());
            }else if("否".equals(excelDto.getControls())){
                target.setControls(CustomsControlsEnum.NOT_REQUIRED.getCode());
                if (StringUtils.isNotBlank(target.getControlsInfo())){
                    throw new TaskBizException("是否管制为“否”时，不允许填写管制信息");
                }
            }else {
                throw new TaskBizException(String.format("* 是否管制输入不合法%s",excelDto.getControls()));
            }
            target.setControlsInfo(excelDto.getControlsInfo());
            if("有限制".equals(excelDto.getBrandLicensing())){
                target.setBrandLicensing(CustomsBrandLicensingEnum.REQUIRED.getCode());
            }else if("无限制".equals(excelDto.getBrandLicensing())){
                target.setBrandLicensing(CustomsBrandLicensingEnum.NOT_REQUIRED.getCode());
            }else if(StringUtils.isNotBlank(excelDto.getBrandLicensing())){
                throw new TaskBizException(String.format("品牌授权是否限制不合法%s",excelDto.getBrandLicensing()));
            }
            target.setRemark(excelDto.getRemark());

            target.setOperator(task.getOperator());
            target.setOperateTime(System.currentTimeMillis());
            target.setOperateCode(ProductCustomsOperateEnum.UPDATE_DECLARATION_INFO.getCode());
            target.setDataSource(CustomsDataSourceEnum.EXCEL_IMPORT.getCode());
            target.setBatchId(batchId);
            target.setCountryCode(CountryConstant.COUNTRY_VN);

            target.setHsCode(excelDto.getHsCode());
            HsCodeVnSaveUpdateReqApiDTO hsCodeDto = getHsCodeDto(excelDto);
            target.setTaxJsonInfo(JSONObject.toJSONString(hsCodeDto));
        }catch (TaskBizException e){
            throw e;
        }catch (Exception e){
            log.error("excel2ApiDto, Exception excelDTO={}", JSONObject.toJSONString(excelDto), e);
            throw new RuntimeException("当前行数据输入不合法,请检查");
        }

        return target;
    }

    private static HsCodeVnSaveUpdateReqApiDTO getHsCodeDto(ProductCustomsVnExcelDTO excelDto) {
        HsCodeVnSaveUpdateReqApiDTO target = new HsCodeVnSaveUpdateReqApiDTO();
        target.setHsCode(excelDto.getHsCode());
        target.setMfnTax(ExcelUtils.string2BigDecimal(excelDto.getMfnTax()));
        target.setOriginMfnTax(ExcelUtils.string2BigDecimal(excelDto.getOriginMfnTax()));
        target.setConsumptionTax(ExcelUtils.string2BigDecimal(excelDto.getConsumptionTax()));
        target.setEnvironmentalTaxUnitPrice(ExcelUtils.string2BigDecimal(excelDto.getEnvironmentalTaxUnitPrice()));
        target.setAntiDumpingTax(ExcelUtils.string2BigDecimal(excelDto.getAntiDumpingTax()));
        target.setValueAddedTax(ExcelUtils.string2BigDecimal(excelDto.getValueAddedTax()));
        return target;
    }

    @Override
    public String uniqueFlag(ProductCustomsVnExcelDTO input){
        return null!=input.getJdSkuId()?input.getJdSkuId().toString():"";
    }

    @Override
    public DataResponse checkByBiz(ProductCustomsVnExcelDTO excelDto) {
        if(StringUtils.isNotBlank(excelDto.getControls()) && "是".equals(excelDto.getControls().trim())
                && StringUtils.isBlank(excelDto.getControlsInfo())){
            excelDto.failed("是否管制为“是”时，管制信息不能为空！");
        }

        return DataResponse.success();
    }

}