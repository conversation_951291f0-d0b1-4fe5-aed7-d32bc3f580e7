package com.jdi.isc.task.center.service.work.task.handler.pdf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.library.common.enmus.file.FileTypeEnum;
import com.jdi.isc.library.i18n.datasource.cache.I18nCacheManager;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.common.costants.UmpKeyConstant;
import com.jdi.isc.task.center.domain.order.OrderWareVO;
import com.jdi.isc.task.center.domain.parcel.ParcelDetailVO;
import com.jdi.isc.task.center.domain.parcel.ParcelItemVO;
import com.jdi.isc.task.center.domain.task.vo.TaskStrategyVO;
import com.jdi.isc.task.center.domain.waybill.OrderWaybillPdfInfoVO;
import com.jdi.isc.task.center.domain.waybill.WaybillWarehouseVO;
import com.jdi.isc.task.center.rpc.authority.AuthorityRpcService;
import com.jdi.isc.task.center.rpc.parcel.RpcParcelService;
import com.jdi.isc.task.center.service.adapter.mapstruct.parcel.ParcelConvert;
import com.jdi.isc.task.center.service.pdf.BaseExecutableService;
import com.jdi.isc.task.center.service.pdf.PdfHeaderEvent;
import com.jdi.isc.task.center.service.work.task.frame.PdfService;
import com.jdi.isc.vc.soa.api.enums.OrderWaybillScopeTypeEnum;
import com.jdi.isc.vc.soa.api.parcel.biz.PurchaseOrderParcelReadApiDTO;
import com.jdi.isc.vc.soa.api.parcel.biz.PurchaseWarehouseApiDTO;
import com.jdi.isc.vc.soa.api.waybill.biz.QueryOrderWaybillInfoReadApiDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.*;

/**
 * 二段交接单pdf服务
 * <AUTHOR>
 * @date 2024/6/20
 */
@Service("poOrderWaybillPdfService")
@Slf4j
public class PoOrderWaybillPdfService extends BaseExecutableService<OrderWaybillPdfInfoVO,TaskStrategyVO> implements PdfService<OrderWaybillPdfInfoVO, TaskStrategyVO> {

    @Resource
    private RpcParcelService rpcParcelService;

    @Resource
    private AuthorityRpcService authorityRpcService;

    @Resource
    private I18nCacheManager i18nCacheManager;


    private static final String  LINE_NUM = "lineNum";



    @Override
    public OrderWaybillPdfInfoVO buildInput(TaskStrategyVO input) {
        JSONObject paramJson = JSON.parseObject(input.getReqParam());
        String waybillNum = paramJson.getString("waybillNum");
        JSONArray langArray = paramJson.getJSONArray("langList");
        List<String> langList = langArray == null ? new ArrayList<>() : langArray.toJavaList(String.class);
        Set<String> scopeType = new HashSet<>();
        scopeType.add(OrderWaybillScopeTypeEnum.ORDER_WARE.getCode());
        QueryOrderWaybillInfoReadApiDTO.Response rpcData = rpcParcelService.queryOrderWaybillInfo(waybillNum,langList,scopeType);
        if (null==rpcData){
            return null;
        }
        PurchaseWarehouseApiDTO purchaseWarehouseVO = rpcData.getParcels().entrySet().stream().findFirst().get().getValue().get(0).getPurchaseWarehouseVO();
        WaybillWarehouseVO waybillWarehouseVO = ParcelConvert.INSTANCE.apiWarehouseVo2WaybillWarehouseVo(purchaseWarehouseVO);

        OrderWaybillPdfInfoVO pdfInfoVO = ParcelConvert.INSTANCE.orderWaybillPdfInfoRpc2Vo(rpcData);
        pdfInfoVO.setWaybillNum(waybillNum);
        pdfInfoVO.setWaybillWarehouseVO(waybillWarehouseVO);
        boolean show = isShow(pdfInfoVO.getParcels());
        pdfInfoVO.setShowMaterialId(show);
        log.info("OrderWaybillPdfService.buildInput, waybillNum={}, rpcData={}, pdfInfoVO={}"
                , waybillNum, JSONObject.toJSONString(rpcData), JSON.toJSONString(pdfInfoVO));
        return pdfInfoVO;
    }

    @SneakyThrows
    @Override
    public String buildPdf(OrderWaybillPdfInfoVO input){
        List<String> langList = input.getLangList();
        String waybillNum = input.getWaybillNum();
        Map<Long, List<PurchaseOrderParcelReadApiDTO>> parcels = null;
        Document document = null;
        Path tempDir = Files.createTempDirectory(null);
        String outPath = Paths.get(tempDir + File.separator + waybillNum + Constant.UNDER_LINE + System.currentTimeMillis() + Constant.PDF).toString();
        try{
            document = new Document(PageSize.A4.rotate(), 0, 0, 80, 60);
            loadHeaderAndFooter(document,outPath);

            document.open();
            document.add(buildTableTitle(waybillNum,langList));
            document.add(buildTableAddress(input,langList));
            document.add(buildTableDeliveryInstructions(input,langList));
            if (MapUtils.isNotEmpty(input.getCustomRemark())) {
                document.add(buildCustomRemark(input));
            }
            document.add(buildTableDesc(langList));
            document.add(buildTableProductListGroupByOrder(input,langList));
            document.add(buildTableSign(input.getParcels(),langList));
            document.close();

            log.info("OrderWaybillPdfService.buildPdf path:{} " , outPath);
            return s3Utils.upload(outPath, FileTypeEnum.ORDER_WAYBILL_PDF.getCode());
        }catch(Exception e){
            log.error("二段交接单打印失败,入参:{}", JSON.toJSONString(input),e);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_FRAME_WARNING,(LevelCode.P0.getMessage() + "二段交接单打印失败: "+waybillNum+". exception:"+e));
            throw new RuntimeException(e);
        }finally {
            FileUtils.deleteDirectory(tempDir.toFile());
            if(document!=null && document.isOpen()){
                document.close();
            }
        }
    }


    //载入页眉
    protected void loadHeaderAndFooter(Document document, String path) throws IOException, DocumentException, URISyntaxException, ClassNotFoundException {
        PdfWriter writer = PdfWriter.getInstance(document, Files.newOutputStream(Paths.get(path)));
        writer.setPageEmpty(false);
        PdfHeaderEvent headerTable = new PdfHeaderEvent(new PdfPTable(1),resourceContainer);
        headerTable.setTableHeader(writer,document);
        addPageNum(writer);
        writer.close();
    }

    private PdfPTable buildTableTitle(String waybillNum,List<String> langList){
        PdfPTable table = new PdfPTable(new float[] { 0.5f,0.5f });
        table.setWidthPercentage(92);
        Font titleFont = resourceContainer.getTextBoldFont();
        PdfPCell cell1 = cell(appendThreeLangValue("运输清单",langList,Constant.SLASH),titleFont, 2);
        cell1.setBorder(0);
        cell1.setHorizontalAlignment(Element.ALIGN_CENTER);
        table.addCell(cell1);
        Font tenFont = resourceContainer.getTenSizeFont();
        PdfPCell cell2 = cell(appendThreeLangValue("运输单号",langList,Constant.SLASH)+":",tenFont);
        cell2.setBorder(0);
        cell2.setHorizontalAlignment(Element.ALIGN_RIGHT);
        cell2.setPaddingRight(2);
        table.addCell(cell2);

        PdfPCell cell3 = cell(waybillNum,tenFont);
        cell3.setBorder(0);
        cell3.setPaddingLeft(2);
        table.addCell(cell3);

        table.setSpacingBefore(8);
        return table;
    }

    private PdfPTable buildTableOutDate(){
        // 参数为每列的占比
        PdfPTable table = new PdfPTable(new float[] { 0.5f,0.5f });
        table.setWidthPercentage(92);

        table.addCell(cell("Ngày xuất hàng (Shipping Out Date):", resourceContainer.getBigFont()));
        table.addCell(cell(""));

        table.setSpacingBefore(8);
        return table;
    }

    private PdfPTable buildTableAddress(OrderWaybillPdfInfoVO input, List<String> langList){
        // 参数为每列的占比
        PdfPTable table = new PdfPTable(new float[] { 0.2f,0.3f,0.2f,0.3f });
        table.setWidthPercentage(92);
        WaybillWarehouseVO waybillWarehouseVo = input.getWaybillWarehouseVO();
        Font tenSizeFont = resourceContainer.getTenSizeFont();
        // 第一行
        table.addCell(cell(this.appendThreeLangValue("发货人",langList,Constant.SLASH)+":", resourceContainer.getTenSizeBoldFont(), 2));
        table.addCell(cell(this.appendThreeLangValue("收件人",langList,Constant.SLASH)+":", resourceContainer.getTenSizeBoldFont(), 2));

        // 第二行
        table.addCell(cell(this.appendThreeLangValue("供应商名称",langList,Constant.SLASH)+":", resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(null!=waybillWarehouseVo && null!=waybillWarehouseVo.getCompanyName()?waybillWarehouseVo.getCompanyName():"",tenSizeFont));
        table.addCell(cell(this.appendThreeLangValue("收件公司名称",langList,Constant.SLASH)+":", resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(input.getClientName(),tenSizeFont));

        // 第三行
        table.addCell(cell(this.appendThreeLangValue("地址",langList,Constant.SLASH)+":", resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(null!=waybillWarehouseVo && null!=waybillWarehouseVo.getCompanyAddress()?waybillWarehouseVo.getCompanyAddress():"",tenSizeFont));
        table.addCell(cell(this.appendThreeLangValue("收件地址",langList,Constant.SLASH)+":", resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(input.getDetailAddress(),tenSizeFont));

        // 第四行
        table.addCell(cell(this.appendThreeLangValue("发货地址",langList,Constant.SLASH)+":", resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(null!=waybillWarehouseVo && null!=waybillWarehouseVo.getWarehouseAddress()?waybillWarehouseVo.getWarehouseAddress():"",tenSizeFont));
        table.addCell(cell(this.appendThreeLangValue("客户编码",langList,Constant.SLASH)+":", resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(input.getBriefCode(),tenSizeFont));

        // 第5行
        table.addCell(cell(this.appendThreeLangValue("联系人",langList,Constant.SLASH)+":", resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(null!=waybillWarehouseVo && null!=waybillWarehouseVo.getShipperName()?waybillWarehouseVo.getShipperName():"",tenSizeFont));
        table.addCell(cell(this.appendThreeLangValue("联系人",langList,Constant.SLASH)+":", resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(input.getConsignee(),tenSizeFont));

        // 第六行
        table.addCell(cell(this.appendThreeLangValue("电话",langList,Constant.SLASH)+":", resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(null!=waybillWarehouseVo && null!=waybillWarehouseVo.getShipperPhone()?waybillWarehouseVo.getShipperPhone():"",tenSizeFont));
        table.addCell(cell(this.appendThreeLangValue("电话",langList,Constant.SLASH)+":", resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(input.getMobile(),tenSizeFont));

        table.setSpacingBefore(8);
        return table;
    }

    private PdfPTable buildTableDeliveryInstructions(OrderWaybillPdfInfoVO input,List<String> langList){
        Font tenSizeFont = resourceContainer.getTenSizeFont();
        // 参数为每列的占比
        PdfPTable table = new PdfPTable(new float[] { 0.5f,0.5f });
        table.setWidthPercentage(92);
        PdfPCell cell1 = cell(this.appendThreeLangValue("配送备注",langList,Constant.SLASH)+":",tenSizeFont,2);
        cell1.setBorder(0);
        table.addCell(cell1);

        PdfPCell cell2 = cell(MapUtils.isEmpty(input.getCustomRemark())?input.getOrderRemark():Constant.WHITE_SPACE,tenSizeFont,2);
        cell2.setBorder(0);
        table.addCell(cell2);

        table.setSpacingBefore(8);
        return table;
    }

    private PdfPTable buildCustomRemark(OrderWaybillPdfInfoVO input) {
        Font tenSizeFont = resourceContainer.getTenSizeFont();
        PdfPTable table = new PdfPTable(new float[] { 0.2f,0.8f });
        table.setWidthPercentage(92);
        Map<String, String> customRemark = input.getCustomRemark();
        customRemark.forEach((k,v) -> {
            PdfPCell key = cell(k,tenSizeFont);
            key.setBorder(0);
            table.addCell(key);
            PdfPCell value = cell(v,tenSizeFont);
            value.setBorder(0);
            table.addCell(value);
        });
        table.setSpacingBefore(8);
        return table;
    }

    private PdfPTable buildTableDesc(List<String> langList){
        Font tenSizeFont = resourceContainer.getTenSizeFont();
        // 参数为每列的占比
        PdfPTable table = new PdfPTable(new float[] { 1f });
        table.setWidthPercentage(92);
        table.addCell(cellBorderZero(this.appendThreeLangValue("双方确认所有物品已全部完好交付，具体如下",langList,Constant.SLASH)+":",tenSizeFont));
        table.setSpacingBefore(8);
        return table;
    }

    private PdfPTable buildTableProductListGroupParcels(OrderWaybillPdfInfoVO input){
        Map<Long, List<ParcelItemVO>> parcels = input.getParcels();
        // 参数为每列的占比
        PdfPTable table = new PdfPTable(new float[] { 0.05f,0.1f,0.1f,0.05f,0.08f,0.08f,0.34f,0.1f,0.05f,0.05f });
        table.setWidthPercentage(92);

        // Adding header
        addTableHeaderOfProductListGroupByParcel(table);

        int num = 0;
        boolean newOrder = false;
        // Adding rows
        for (Map.Entry<Long, List<ParcelItemVO>> entry : parcels.entrySet()) {
            newOrder = true;
            Long orderId = entry.getKey();
            List<ParcelItemVO> parcelList = entry.getValue();

            int orderRowSpan = parcelList.stream().mapToInt(p -> p.getParcelDetailList().size()).sum();
            int parcelCount = parcelList.size();

            boolean newParcel = false;
            for (ParcelItemVO parcel : parcelList) {
                newParcel = true;
                int parcelRowSpan = parcel.getParcelDetailList().size();
                String thirdOrderId = parcel.getThirdOrderId();

                for (ParcelDetailVO skuDetail : parcel.getParcelDetailList()) {
                    num++;
                    StringBuilder size = new StringBuilder();
                    size.append(parcel.getLength().toString()).append("x").append(parcel.getWidth().toString()).append("x").append(parcel.getHeight().toString());

                    table.addCell(cell(num+""));
                    if (newOrder){
                        newOrder = false;
                        table.addCell(cell(orderId.toString(), 1,orderRowSpan));
                        table.addCell(cell(thirdOrderId, 1,orderRowSpan));
                    }
                    if (newParcel){
                        newParcel = false;
                        table.addCell(cell(parcel.getParcelId() + "("+parcel.getParcelSort().toString()+"/"+parcelCount+")",1,parcelRowSpan));
                        table.addCell(cell(parcel.getGrossWeight().toString(),1,parcelRowSpan));
                        table.addCell(cell(size.toString(),1,parcelRowSpan));
                    }

                    table.addCell(cell(skuDetail.getSkuName()));
                    table.addCell(cell(null!=skuDetail.getMkuId()?skuDetail.getMkuId().toString():"-"));
                    table.addCell(cell(skuDetail.getSaleUnit()));
                    table.addCell(cell(skuDetail.getNum().toString()));
                }
            }
        }

        table.setSpacingBefore(8);
        return table;
    }

    private PdfPTable buildTableProductListGroupByOrder(OrderWaybillPdfInfoVO input, List<String> langList){
        Font tenSizeFont = resourceContainer.getTenSizeFont();
        Map<Long, List<ParcelItemVO>> parcels = input.getParcels();
        boolean isShow = input.getShowMaterialId();
        // 参数为每列的占比
        PdfPTable table;
        if(isShow) {
            table = new PdfPTable(new float[] {0.03f, 0.07f, 0.1f,0.1f, 0.2f, 0.1f, 0.1f, 0.1f, 0.05f, 0.075f, 0.075f, 0.08f, 0.05f});
        }else {
            table = new PdfPTable(new float[] {0.03f, 0.07f, 0.1f,0.1f, 0.3f,  0.15f, 0.05f, 0.05f, 0.05f, 0.08f,0.05f});
        }
        table.setWidthPercentage(92);

        // Adding header
        addTableHeaderOfProductListGroupByOrder(table,isShow,langList);

        Map<String,String> mkuPoMap = getMkuPoMap(input.getOrderWareVOS());
        log.error("buildTableProductListGroupByOrder.mkuPoMap:{} ",JSONObject.toJSONString(mkuPoMap));
        int num = 0;
        boolean newOrder = false;
        // 循环订单
        for (Map.Entry<Long, List<ParcelItemVO>> entry : parcels.entrySet()) {
            newOrder = true;
            Long orderId = entry.getKey();
            List<ParcelItemVO> parcelList = entry.getValue();

            // 当前订单的包裹总重量
            BigDecimal grossWeightSum = BigDecimal.ZERO;
            // 当前订单的所有商品
            Map<Long,ParcelDetailVO> allProductMap = new HashMap<>();
            //下单人map
            Map<String,String> buyerMap = new HashMap<>();
            // 循环包裹
            for (ParcelItemVO parcel : parcelList) {
                // 循环包裹商品
                for (ParcelDetailVO skuDetail : parcel.getParcelDetailList()) {
                    ParcelDetailVO parcelDetailVO = allProductMap.get(skuDetail.getMkuId());
                    if (null==parcelDetailVO){
                        parcelDetailVO = skuDetail;
                        allProductMap.put(parcelDetailVO.getMkuId(), parcelDetailVO);
                    }else {
                        // 累加商品数量
                        parcelDetailVO.setNum( parcelDetailVO.getNum() + skuDetail.getNum() );
                    }
                }
                if(parcel.getGrossWeight() != null){
                    grossWeightSum = grossWeightSum.add(parcel.getGrossWeight());
                }
                buyerMap.put(parcel.getOrderId(), parcel.getBuyer());
            }

            String thirdOrderId = parcelList.get(0).getThirdOrderId();
            List<ParcelDetailVO> allProductList = new ArrayList<>(allProductMap.values());
            allProductList.sort(Comparator.comparing(ParcelDetailVO::getMkuId));

            int orderRowSpan = allProductList.size();
            int parcelCount = parcelList.size();
            // 循环当前订单的所有商品
            for (ParcelDetailVO skuDetail : allProductList) {
                num++;
                table.addCell(cell(num+"",tenSizeFont));
                if (newOrder){
                    table.addCell(cell(orderId.toString(),tenSizeFont, 1,orderRowSpan));
                    table.addCell(cell(thirdOrderId,tenSizeFont, 1,orderRowSpan));
                    String key = String.format("%s-%s",orderId,skuDetail.getMkuId());
                    String linePo = mkuPoMap.get(key);
                    log.error("buildTableProductListGroupByOrder.linePo:{},key:{} ",linePo,key);
                    table.addCell(cell(linePo,tenSizeFont, 1,orderRowSpan));
                }
                table.addCell(cell(skuDetail.getSkuName(),tenSizeFont));
                table.addCell(cell(null!=skuDetail.getMkuId()?skuDetail.getMkuId().toString():"-",tenSizeFont));
                if (isShow) {
                    table.addCell(cell(null != skuDetail.getMaterialName() ? skuDetail.getMaterialName() : "-",tenSizeFont));
                    table.addCell(cell(null != skuDetail.getMaterialId() ? skuDetail.getMaterialId() : "-",tenSizeFont));
                }
                table.addCell(cell(skuDetail.getNum().toString(),tenSizeFont));
                table.addCell(cell(skuDetail.getSaleUnit(),tenSizeFont));

                if (newOrder){
                    table.addCell(cell(Integer.toString(parcelCount),tenSizeFont,1,orderRowSpan));
                    table.addCell(cell(BigDecimal.ZERO.equals(grossWeightSum) ? "" : grossWeightSum.toPlainString(),tenSizeFont,1,orderRowSpan));
                    newOrder = false;
                }
                table.addCell(cell(buyerMap.get(orderId.toString()),tenSizeFont));
            }
        }

        table.setSpacingBefore(8);
        return table;
    }

    /**
     * 获取mku 对应 行号
     * @param orderWareVOS
     * @return
     */
    private Map<String, String> getMkuPoMap(List<OrderWareVO> orderWareVOS) {
        Map<String,String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(orderWareVOS)) {
            return result;
        }
        orderWareVOS.forEach(o -> {
            String mkuExtInfo = o.getMkuExtInfo();
            if (StringUtils.isBlank(mkuExtInfo)) {
                return;
            }
            JSONObject jsonObject = JSONObject.parseObject(mkuExtInfo);
            if (!jsonObject.containsKey(LINE_NUM)) {
                return;
            }
            String lineNum = jsonObject.getString(LINE_NUM);
            if (Objects.isNull(lineNum)) {
                lineNum = "";
            }
            result.put(String.format("%s-%s",o.getOrderId(),o.getMkuId()),lineNum);
        });
        return result;
    }

    private boolean isShow(Map<Long, List<ParcelItemVO>> map){
        try {
            for(List<ParcelItemVO> list: map.values()){
                for (ParcelItemVO vo : list){
                    if(StringUtils.isNotBlank(vo.getClientCode())){
                        Boolean result = authorityRpcService.haseMaterialAuthority(vo.getClientCode());
                        if(result){
                            return true;
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("二段交接单是否显示物料编码列失败,入参:{}，err:{}", JSON.toJSONString(map),e.getMessage(),e);
        }
        return false;
    }

    private void addTableHeaderOfProductListGroupByParcel(PdfPTable table) {
        table.addCell(cell("STT\n\nNo.", resourceContainer.getBigFont()));
        table.addCell(cell("Mã đơn hàng\n\nOrder No.", resourceContainer.getBigFont()));
        table.addCell(cell("Order Remarks\n\nCustomer Ref No.", resourceContainer.getBigFont()));

        table.addCell(cell("Số kiện\n\nPackage No.", resourceContainer.getBigFont()));
        table.addCell(cell("Trọng lượng\n\nPackage G.W (kg)", resourceContainer.getBigFont()));
        table.addCell(cell("Trạng thái hàng hóa\nPackage Condition", resourceContainer.getBigFont()));

        table.addCell(cell("Mô tả hàng hóa\n\nDescription of goods/Part No.", resourceContainer.getBigFont()));
        table.addCell(cell("Số MKU\n\nMKU No.", resourceContainer.getBigFont()));
        table.addCell(cell("Đơn vị\n\nUnit", resourceContainer.getBigFont()));
        table.addCell(cell("Số lượng\n\nQuantity", resourceContainer.getBigFont()));
    }

    private void addTableHeaderOfProductListGroupByOrder(PdfPTable table, boolean isShow, List<String> langList) {
        table.addCell(cell(this.appendThreeLangValue("序号",langList,Constant.SLASH), resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(this.appendThreeLangValue("订单号",langList,Constant.SLASH),resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(this.appendThreeLangValue("SAP号",langList,Constant.SLASH), resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(this.appendThreeLangValue("PO行号",langList,Constant.SLASH), resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(this.appendThreeLangValue("商品描述",langList,Constant.SLASH), resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(this.appendThreeLangValue("MKU编号",langList,Constant.SLASH,LangConstant.LANG_ZH), resourceContainer.getTenSizeBoldFont()));
        if(isShow) {
            table.addCell(cell(this.appendThreeLangValue("客户物料名称",langList,Constant.SLASH), resourceContainer.getTenSizeBoldFont()));
            table.addCell(cell(this.appendThreeLangValue("客户物料编码",langList,Constant.SLASH), resourceContainer.getTenSizeBoldFont()));
        }
        table.addCell(cell(this.appendThreeLangValue("数量",langList,Constant.SLASH), resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(this.appendThreeLangValue("单位",langList,Constant.SLASH),resourceContainer.getTenSizeBoldFont()));

        table.addCell(cell(this.appendThreeLangValue("包裹数量",langList,Constant.SLASH), resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(this.appendThreeLangValue("包裹重量",langList,Constant.SLASH), resourceContainer.getTenSizeBoldFont()));
        table.addCell(cell(this.appendThreeLangValue("下单人",langList,Constant.SLASH), resourceContainer.getTenSizeBoldFont()));
    }

    private PdfPTable buildTableSign(Map<Long, List<ParcelItemVO>> parcels,List<String> langList){
        Font tenSizeFont = resourceContainer.getTenSizeFont();
        // 参数为每列的占比
        PdfPTable table = new PdfPTable(new float[] { 3f,1f,2f,1f,3f,1f,2f,1f});
        table.setWidthPercentage(92);
        table.addCell(cellBorderZero(this.appendThreeLangValue("包裹数量",langList,Constant.SLASH)+":", tenSizeFont,3));
        int parcelCount = parcels.entrySet().stream().filter(o->null!=o.getValue()).mapToInt(o->o.getValue().size()).sum();
        PdfPCell parcelCountCell = cellBorderZero(Integer.toString(parcelCount),tenSizeFont);
        parcelCountCell.setHorizontalAlignment(Element.ALIGN_LEFT);
        table.addCell(parcelCountCell);
        table.addCell(cellBorderZero("",4));
        //第二行
        table.addCell(cellBorderZero(this.appendThreeLangValue("发货人签名",langList,Constant.SLASH)+":",tenSizeFont));
        table.addCell(cellBorderZero(""));
        table.addCell(cellBorderZero(this.appendThreeLangValue("日期",langList,Constant.SLASH)+":",tenSizeFont));
        table.addCell(cellBorderZero(""));
        table.addCell(cellBorderZero(this.appendThreeLangValue("收货人签名",langList,":"+Constant.SLASH)+":",tenSizeFont));
        table.addCell(cellBorderZero(""));
        table.addCell(cellBorderZero(this.appendThreeLangValue("日期",langList,Constant.SLASH)+":",tenSizeFont));
        table.addCell(cellBorderZero(""));
        table.setSpacingBefore(8);
        return table;
    }

    /**
     * 在指定的key后面添加多语言值。
     */
    private String appendThreeLangValue(String key,List<String> langList,String span){
        return appendThreeLangValue(key,langList,span,null);
    }
    private String appendThreeLangValue(String key,List<String> langList,String span,String filter){
        Map<String, String> langMap = i18nCacheManager.getValuesOrDefaultForLangList(key, langList);
        if (MapUtils.isNotEmpty(langMap)) {
            StringBuilder result = new StringBuilder();
            for (String lang : langList) {
                if (StringUtils.isNotBlank(filter) && filter.equals(lang)) {
                    continue;
                }
                result.append(langMap.get(lang)).append(span);
            }
            result.deleteCharAt(result.length() - 1);
            return result.toString();
        }
        throw new RuntimeException("查询多语言失败："+key);
    }

}
