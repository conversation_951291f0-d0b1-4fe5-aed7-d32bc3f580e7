package com.jdi.isc.task.center.service.manage.template.create;


import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.AttributeTypeEnum;
import com.jdi.isc.product.soa.api.supplier.req.BusinessLineQueryReqDTO;
import com.jdi.isc.product.soa.api.supplier.res.BrandResDTO;
import com.jdi.isc.product.soa.api.supplier.res.CategoryTreeResDTO;
import com.jdi.isc.product.soa.api.supplier.res.SupplierBaseInfoRes;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.common.costants.ProductConstant;
import com.jdi.isc.task.center.domain.excel.*;
import com.jdi.isc.task.center.rpc.supplier.RpcBusinessLineService;
import com.jdi.isc.task.center.rpc.supplier.RpcSupplierBaseService;
import com.jdi.isc.task.center.service.manage.template.support.TemplateSupportService;
import com.jdi.isc.task.center.service.support.excel.HiddenSheetWriteHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description：SupplierParentCreateProductExcelTemplateService
 * @Date 2025-08-08
 */
@Slf4j
@Service
public class SupplierParentCreateProductExcelTemplateService extends ParentCreateProductExcelTemplateService{

    @Resource
    private RpcBusinessLineService rpcBusinessLineService;
    @Resource
    private RpcSupplierBaseService rpcSupplierBaseService;
    @Resource
    private TemplateSupportService templateSupportService;

    @Override
    protected List<TemplateSourceDataVO.SheetDataVO> getSheetDataVoList(TemplateReqVO reqVO) {
        log.info("VcCreateProductExcelTemplateService.getSheetDataVoList reqVO={}",JSON.toJSONString(reqVO));
        super.initVal(reqVO);
        ArrayList<TemplateSourceDataVO.SheetDataVO> sheetDataVOS = Lists.newArrayList();
        String operator = reqVO.getOperator();
        String paramJson = reqVO.getParamJson();
        BatchParamVO batchParamVO = JSON.parseObject(paramJson, BatchParamVO.class);
        batchParamVO.setOperator(operator);
        // 数据填写sheet填充内容
        this.importSheetData(batchParamVO, sheetDataVOS);

        // 供应商类目数据Sheet填充内容
        this.supplierSheetData(batchParamVO,sheetDataVOS);
        // 供应商类目数据Sheet填充内容
        super.categorySheetData(batchParamVO,sheetDataVOS);
        // 供应商品牌数据sheet填充内容
        super.brandSheetData(batchParamVO,sheetDataVOS);
        // SPU跨境属性
        super.spuGlobalAttributeSheetData(batchParamVO,sheetDataVOS);
        // SKU跨境属性
        super.skuGlobalAttributeSheetData(batchParamVO,sheetDataVOS);
        // 隐藏列表
        this.hiddenSheetData(sheetDataVOS);

        return sheetDataVOS;
    }

    protected void supplierSheetData(BatchParamVO batchParamVO, List<TemplateSourceDataVO.SheetDataVO> sheetDataVOS){
        if(!Constant.SYSTEM_CODE.equals(systemCode)){
            log.info("VcCreateProductExcelTemplateService.supplierSheetData VC model");
            return;
        }
        TemplateSourceDataVO.SheetDataVO sheetDataVO = new TemplateSourceDataVO.SheetDataVO();
        sheetDataVO.setSheetNo(sheetDataVOS.size());
        sheetDataVO.setSheetName(this.getI18nMessageByKey(ProductConstant.VC_SHEET_NAME_FORTH, StringUtils.isNotBlank(batchParamVO.getLang()) ? batchParamVO.getLang() : LangConstant.LANG_EN));
        // 供应商
        List<SupplierBaseInfoRes> supplierBaseInfoRes = rpcSupplierBaseService.queryBaseList(batchParamVO.getSourceCountryCode());
        // 转换品牌
        List<SupplierExcelVO> supplierExcelVOS = Optional.ofNullable(supplierBaseInfoRes).orElseGet(java.util.ArrayList::new).stream().filter(Objects::nonNull).map(this::getSupplierExcelVO).collect(Collectors.toList());
        sheetDataVO.setClazz(SupplierExcelVO.class);
        sheetDataVO.setDatatList(supplierExcelVOS);
        sheetDataVO.setWriteHandlerList(Lists.newArrayList(new SimpleRowHeightStyleStrategy(new Short("30"), new Short("30"))));
        sheetDataVOS.add(sheetDataVO);
    }

    /**
     * 将 BrandResDTO 转换为 BrandExcelVO，用于生成供应商信息 Excel。
     * @param supplierBaseInfoRes 供应商资源 DTO
     * @return 转换后的 BrandExcelVO 对象
     */
    private SupplierExcelVO getSupplierExcelVO(SupplierBaseInfoRes supplierBaseInfoRes) {
        SupplierExcelVO excelVO = new SupplierExcelVO();
        excelVO.setSupplierCode(supplierBaseInfoRes.getSupplierCode());
        excelVO.setSupplierName(supplierBaseInfoRes.getBusinessLicenseName());
        return excelVO;
    }

    @Override
    protected DropdownDataVO fillDropdownDataVO(TemplateHeadSupportVO headSupportVo) {
        List<List<String>> headList = headSupportVo.getHeadList();
        // 表头取第二行表头数据
        List<String> headStrList = Lists.newArrayList();
        for (List<String> subHead : headList) {
            headStrList.add(subHead.get(1));
        }

        Map<String,Integer> headTitleIndexMap = Maps.newHashMap();
        for (int i = 0,len = headStrList.size(); i < len; i++) {
            headTitleIndexMap.put(headStrList.get(i), i);
        }

        Integer catIndex = 4;
        Integer brandIndex = 6;
        Integer saleUnit = 8;
        Integer saleAttributeIndex = 24;
        Integer fixedEndIndex = 23;
        if(Constant.SYSTEM_CODE.equals(systemCode)){
            brandIndex = 7;
            saleUnit = 9;
            saleAttributeIndex = 26;
            fixedEndIndex = 25;
        }

        if (CountryConstant.COUNTRY_BR.equals(sourceCountryCode)) {
            saleAttributeIndex = 25;
            fixedEndIndex = 24;
        }

        // 下拉数据
        log.info("VcCreateProductExcelTemplateService.getSheetDataVoList 固定下拉数据表头 headStrList={},headTitleIndexMap={}", JSON.toJSONString(headStrList),JSON.toJSONString(headTitleIndexMap));
        List<DropdownDataVO.EachItemDataVO> fixEachDataList = Lists.newArrayList();
//        // 四级类目
        fixEachDataList.add(new DropdownDataVO.EachItemDataVO(catIndex, ProductConstant.FIX_CATEGORY_ID,headSupportVo.getFourthCategoryNameList()));
        log.info("VcCreateProductExcelTemplateService.getSheetDataVoList 固定下拉数据表头 四级类目={}",JSON.toJSONString(headSupportVo.getFourthCategoryNameList()));
//        // 品牌ID
        fixEachDataList.add(new DropdownDataVO.EachItemDataVO(brandIndex, ProductConstant.FIX_BRAND_ID,headSupportVo.getBrandNameList()));
        log.info("VcCreateProductExcelTemplateService.getSheetDataVoList 固定下拉数据表头 品牌={}",JSON.toJSONString(headSupportVo.getBrandNameList()));
        // 销售单位
        fixEachDataList.add(new DropdownDataVO.EachItemDataVO(saleUnit,ProductConstant.FIX_SALES_UNIT,headSupportVo.getSaleUnitList()));
        log.info("VcCreateProductExcelTemplateService.getSheetDataVoList 固定下拉数据表头 销售单位={}",JSON.toJSONString(headSupportVo.getSaleUnitList()));

        List<DropdownDataVO.DynamicDataVO> dynamicDataVOList = Lists.newArrayList();
        Map<AttributeTypeEnum, TemplateAttrVO> attributeTypeMap = headSupportVo.getAttributeTypeMap();
//        // 销售属性
//        TemplateAttrVO templateAttrVO = attributeTypeMap.get(AttributeTypeEnum.SELL);
//        if (templateAttrVO != null && MapUtils.isNotEmpty(templateAttrVO.getAttrNameMap())){
//            LinkedHashMap<String, List<String>> attrNameMap = templateAttrVO.getAttrNameMap();
//            int saleIndex = saleAttributeIndex + attrNameMap.size();
//            log.info("VcCreateProductExcelTemplateService.getSheetDataVoList 动态下拉数据表头 销售属性索引={},销售属性key集合={}",saleIndex,JSON.toJSONString(attrNameMap.keySet()));
//            dynamicDataVOList.add(new DropdownDataVO.DynamicDataVO(saleIndex,attrNameMap));
//        }


        // 跨境属性
        LinkedHashMap<String, List<String>> allAttributeMap = Maps.newLinkedHashMap();
        TemplateAttrVO spuTemplateAttrVO = attributeTypeMap.get(AttributeTypeEnum.SPU_CROSS_BORDER);
        if (spuTemplateAttrVO != null && MapUtils.isNotEmpty(spuTemplateAttrVO.getAttrNameMap())){
            allAttributeMap.putAll(spuTemplateAttrVO.getAttrNameMap());
        }
        TemplateAttrVO skuTemplateAttrVO = attributeTypeMap.get(AttributeTypeEnum.SKU_CROSS_BORDER);
        if (skuTemplateAttrVO != null && MapUtils.isNotEmpty(skuTemplateAttrVO.getAttrNameMap())) {
            allAttributeMap.putAll(skuTemplateAttrVO.getAttrNameMap());
        }
        if (MapUtils.isNotEmpty(allAttributeMap)) {
            TemplateAttrVO extendTemplateAttrVO = attributeTypeMap.get(AttributeTypeEnum.EXTEND);
            int extendSize = 0;
            if (Objects.nonNull(extendTemplateAttrVO) && MapUtils.isNotEmpty(extendTemplateAttrVO.getAttrNameMap())){
                extendSize = extendTemplateAttrVO.getAttrNameMap().size();
            }
            int globalAttrIndex = headStrList.size() - extendSize;
            dynamicDataVOList.add(new DropdownDataVO.DynamicDataVO(globalAttrIndex, allAttributeMap));
        }

//        // 扩展属性-行上的索引
//        TemplateAttrVO extendIndexTemplateAttrVO = attributeTypeMap.get(AttributeTypeEnum.EXTEND);
//        if (MapUtils.isNotEmpty(allAttributeMap) && extendIndexTemplateAttrVO != null) {
//            int extendSize = 0;
//            if (MapUtils.isNotEmpty(extendIndexTemplateAttrVO.getAttrNameMap())){
//                extendSize = extendIndexTemplateAttrVO.getAttrNameMap().size();
//            }
//            int globalAttrIndex = headStrList.size() - extendSize;
//            dynamicDataVOList.add(new DropdownDataVO.DynamicDataVO(globalAttrIndex, allAttributeMap));
//        }

        // 扩展属性-下拉
        TemplateAttrVO extendTemplateAttrVO = attributeTypeMap.get(AttributeTypeEnum.EXTEND);
        if (extendTemplateAttrVO != null && MapUtils.isNotEmpty(extendTemplateAttrVO.getAttrNameMap())) {
            LinkedHashMap<String, List<String>> attrNameMap = extendTemplateAttrVO.getAttrNameMap();
            int extendIndex = headStrList.size();
            log.info("VcCreateProductExcelTemplateService.getSheetDataVoList 动态下拉数据表头 扩展属性索引={},销售属性key集合={}",extendIndex,JSON.toJSONString(attrNameMap.keySet()));
            dynamicDataVOList.add(new DropdownDataVO.DynamicDataVO(extendIndex,attrNameMap));
        }

        return DropdownDataVO.builder()
                .fixedEndIndex(fixedEndIndex)
                .maxRowNum(1000)
                .headList(headStrList)
                .eachItemDataVOList(fixEachDataList)
                .dynamicDataVOList(dynamicDataVOList).build();
    }

    @Override
    protected List<CategoryTreeResDTO> getCategoryTreeResDTO(String supplierCode, Long catId) {
        if(StringUtils.isBlank(supplierCode)){
            log.info("VcCreateProductExcelTemplateService.getCategoryTreeResDTO supplierCode is null");
            return super.getCategoryTreeRes(supplierCode,catId);
        }
        return rpcBusinessLineService.queryCategoryTreeBySupplierCode(new BusinessLineQueryReqDTO(supplierCode, catId, null));
    }

    @Override
    protected List<BrandResDTO> getBrandResDTO(String supplierCode, Long catId) {
        if(StringUtils.isBlank(supplierCode)){
            log.info("VcCreateProductExcelTemplateService.getBrandResDTO supplierCode is null");
            return super.getBrandRes(supplierCode,catId);
        }

        // 查询产品线品牌
        return rpcBusinessLineService.queryBrandListBySupplierCode(new BusinessLineQueryReqDTO(supplierCode
                , catId, null));
    }
}
