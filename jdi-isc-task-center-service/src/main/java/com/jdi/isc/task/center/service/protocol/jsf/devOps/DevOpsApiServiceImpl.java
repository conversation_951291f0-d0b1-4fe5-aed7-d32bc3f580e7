package com.jdi.isc.task.center.service.protocol.jsf.devOps;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.task.center.api.common.enums.TaskStatusEnum;
import com.jdi.isc.task.center.api.devOps.DevOpsApiService;
import com.jdi.isc.task.center.api.dto.task.TaskDTO;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskCreateTypeEnum;
import com.jdi.isc.task.center.domain.mail.po.JdiIscMailMsgPO;
import com.jdi.isc.task.center.domain.productidentity.ProductIdentifyDependenceVO;
import com.jdi.isc.task.center.domain.task.po.TaskPO;
import com.jdi.isc.task.center.domain.task.vo.TaskStrategyVO;
import com.jdi.isc.task.center.domain.task.vo.TaskVO;
import com.jdi.isc.task.center.service.adapter.mapstruct.task.TaskConvert;
import com.jdi.isc.task.center.service.atomic.category.CategoryAtomicService;
import com.jdi.isc.task.center.service.atomic.mail.MailMsgAtomicService;
import com.jdi.isc.task.center.service.manage.task.TaskMangeService;
import com.jdi.isc.task.center.service.manage.task.strategy.TaskStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/6/14
 **/
@Slf4j
@Service
public class DevOpsApiServiceImpl implements DevOpsApiService {

    @Value("${jdi.isc.task.env}")
    private String env;

    @Resource
    private TaskMangeService taskMangeService;

    @Resource
    private TaskStrategyService supplierTaskServiceImpl;

    @Resource
    private MailMsgAtomicService mailMsgAtomicService;

    @Resource
    private CategoryAtomicService categoryAtomicService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> updateExportTask(TaskDTO taskDTO) {
        TaskBizTypeEnum taskBizTypeEnum = TaskBizTypeEnum.forCode(Integer.valueOf(taskDTO.getBizType()));
        TaskPO res = taskMangeService.save("", "system", taskDTO.getReqParam(), taskBizTypeEnum, TaskCreateTypeEnum.EXPORT);
        return DataResponse.success(String.valueOf(res.getId()));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> addSupplierTask(TaskDTO taskDTO) {
        TaskStrategyVO strategyVO = TaskConvert.INSTANCE.taskDto2StrategyVo(taskDTO);
        if (Objects.isNull(taskDTO.getStatus())) {
            strategyVO.setStatus(TaskStatusEnum.PENDING);
        }
        if (Objects.isNull(taskDTO.getCreateType())) {
            strategyVO.setCreateType(TaskCreateTypeEnum.EXPORT);
        }
        Long aLong = supplierTaskServiceImpl.save(strategyVO);
        return DataResponse.success(String.valueOf(aLong));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> addWimpTask(TaskDTO taskDTO) {

        //输入样例
//        [
//            {
//                "name": "批量识别商品类目品牌信息",
//                    "bizType": "批量识别商品类目品牌信息",
//                    "status": "待处理",
//                    "createType": "导入",
//                    "reqFileName": "pi101201.xlsx",
//                    "reqParam": "pi101201.xlsx",
//                    "reqFileUrl": "hctab1/pi101201.xlsx",
//            }
//        ]


        TaskVO taskVO = new TaskVO();

        taskVO.setName(taskDTO.getName());
        taskVO.setBizType(TaskBizTypeEnum.forName(taskDTO.getBizType()));
        taskVO.setStatus(TaskStatusEnum.forName(taskDTO.getStatus()));
        taskVO.setCreateType(TaskCreateTypeEnum.forName(taskDTO.getCreateType()));
        taskVO.setReqFileName(taskDTO.getReqFileName());
        taskVO.setReqParam(taskDTO.getReqParam());
        taskVO.setReqFileUrl(taskDTO.getReqFileUrl());

        taskVO.setEnv(env);
        taskVO.setCreator(StringUtils.isNotBlank(taskDTO.getCreator())?taskDTO.getCreator():"admin");
        taskVO.setUpdater(taskVO.getCreator());
        taskVO.setCreateTime(new Date());
        taskVO.setUpdateTime(taskVO.getCreateTime());

        TaskPO res = taskMangeService.saveOrUpdate(taskVO);
        return DataResponse.success(String.valueOf(res.getId()));
    }

    @Override
    public DataResponse<String> getAllCate() {
        ProductIdentifyDependenceVO res = categoryAtomicService.getAssemblyCateMap();
        return DataResponse.success();
    }

}
