package com.jdi.isc.task.center.service.adapter.mapstruct.forecast;

import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderDetailResp;
import com.jdi.isc.task.center.domain.forecast.ForecastPurchaseOrderTaskPdfVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * ForecastPurchaseOrder 转换类
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/30
 */

@Mapper
public interface ForecastPurchaseOrderConvert {

    ForecastPurchaseOrderConvert INSTANCE = Mappers.getMapper(ForecastPurchaseOrderConvert.class);

    ForecastPurchaseOrderTaskPdfVO res2Vo(ForecastOrderDetailResp forecastOrderDetailResp);

}