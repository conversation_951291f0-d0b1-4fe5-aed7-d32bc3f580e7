package com.jdi.isc.task.center.service.manage.joysky;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.task.center.api.joysky.req.JoySkyCreateReqApiDTO;
import com.jdi.isc.task.center.api.joysky.res.JoySkyCreateRspApiDTO;
import com.jdi.isc.task.center.domain.joysky.po.JoySkyApprovalTaskPO;

/**
 * <AUTHOR>
 * @Date 2024/11/1
 * @Description joySky业务操作类
 */
public interface JoySkyTaskManageService {

    /**
     * 保存审批任务。
     * @param reqInput JoySkyCreateReqApiDTO类型的请求输入参数，包含创建审批任务所需的信息。
     * @param rspInput JoySkyCreateRspApiDTO类型的响应输入参数，用于接收创建审批任务的结果。
     * @return DataResponse&lt;Boolean&gt;类型的响应对象，表示是否成功保存审批任务。
     */
    DataResponse<Boolean> saveApprovalTask(JoySkyCreateReqApiDTO reqInput, JoySkyCreateRspApiDTO rspInput);

    /**
     * 更新审批任务状态
     * @param approvalTaskPO 审批任务PO对象，包含更新的信息
     * @return 更新结果，true表示更新成功，false表示更新失败
     */
    DataResponse<Boolean> updateApprovalTask(JoySkyApprovalTaskPO approvalTaskPO);

    /**
     * 保存审批任务日志。
     * @param reqInput 审批任务PO对象，包含审批任务的详细信息。
     * @param actionType 操作类型，表示执行的操作。
     * @return 保存结果，true表示保存成功，false表示保存失败。
     */
    DataResponse<Boolean> saveApprovalTaskLog(JoySkyApprovalTaskPO reqInput, String actionType);
}
