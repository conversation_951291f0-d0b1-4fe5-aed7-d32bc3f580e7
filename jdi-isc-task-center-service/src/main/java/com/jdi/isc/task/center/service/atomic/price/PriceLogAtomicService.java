package com.jdi.isc.task.center.service.atomic.price;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.task.center.domain.price.po.PriceLogPO;
import com.jdi.isc.task.center.repository.jed.mapper.price.PriceLogBaseMapper;
import org.springframework.stereotype.Service;


/**
 * 价格日志原子服务
 * <AUTHOR>
 * @date 2025/5/19
 */
@Service
public class PriceLogAtomicService extends ServiceImpl<PriceLogBaseMapper, PriceLogPO> {

}




