package com.jdi.isc.task.center.service.work.task.stock;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.biz.component.api.enums.RobotTypeEnum;
import com.jdi.isc.biz.component.api.jme.req.SendMessageReqDTO;
import com.jdi.isc.library.common.enmus.file.FileTypeEnum;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.SkuQueryEnum;
import com.jdi.isc.product.soa.api.sku.req.QuerySkuReqDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuBaseInfoApiDTO;
import com.jdi.isc.product.soa.api.supplier.res.JdSupplierBaseInfoRes;
import com.jdi.isc.product.soa.api.supplier.res.SupplierBaseInfoRes;
import com.jdi.isc.product.soa.api.warehouse.req.WarehouseBatchGetReqDTO;
import com.jdi.isc.product.soa.api.warehouse.res.WarehouseResDTO;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.common.costants.UmpKeyConstant;
import com.jdi.isc.task.center.common.enums.StatusEnum;
import com.jdi.isc.task.center.common.utils.ConfigUtils;
import com.jdi.isc.task.center.common.utils.DateUtil;
import com.jdi.isc.task.center.common.utils.S3Utils;
import com.jdi.isc.task.center.common.utils.StockNumUtils;
import com.jdi.isc.task.center.domain.category.vo.CategoryComboLangBoxVO;
import com.jdi.isc.task.center.domain.category.vo.CategoryPathVO;
import com.jdi.isc.task.center.domain.enums.YnEnum;
import com.jdi.isc.task.center.domain.sku.biz.SkuInfoVO;
import com.jdi.isc.task.center.domain.sku.po.SkuPO;
import com.jdi.isc.task.center.domain.spu.SpuPO;
import com.jdi.isc.task.center.domain.stock.po.StockPO;
import com.jdi.isc.task.center.domain.stockThreshold.biz.AbnormalStockStatisticsExcelDTO;
import com.jdi.isc.task.center.domain.stockThreshold.biz.AbnormalStockStatisticsExcelForBuyerDTO;
import com.jdi.isc.task.center.domain.stockThreshold.biz.AbnormalStockStatisticsExcelForCountryDTO;
import com.jdi.isc.task.center.domain.stockThreshold.po.AbnormalStockStatisticsPO;
import com.jdi.isc.task.center.domain.stockThreshold.po.SkuStockThresholdPO;
import com.jdi.isc.task.center.rpc.jme.DongDongMessageRcpService;
import com.jdi.isc.task.center.rpc.sku.SkuRpcService;
import com.jdi.isc.task.center.rpc.supplier.RpcSupplierBaseService;
import com.jdi.isc.task.center.rpc.warehouse.WarehouseRpcService;
import com.jdi.isc.task.center.service.atomic.category.CategoryAtomicService;
import com.jdi.isc.task.center.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.task.center.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.task.center.service.atomic.stock.StockAtomicService;
import com.jdi.isc.task.center.service.atomic.stockThreshold.AbnormalStockStatisticsAtomicService;
import com.jdi.isc.task.center.service.atomic.stockThreshold.SkuStockThresholdAtomicService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

import static com.jdi.isc.task.center.common.costants.Constant.COUNTRY_CODE_CN;
import static com.jdi.isc.task.center.common.costants.Constant.PAGE_SIZE;

/**
 * <AUTHOR>
 * @description：StockThresholdStatisticJob
 * @Date 2025-01-18
 */
@Slf4j
@Component
public class StockThresholdStatisticJob {
    @Resource
    private S3Utils s3Utils;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    private StockAtomicService stockAtomicService;
    @Resource
    private WarehouseRpcService warehouseRpcService;
    @Resource
    private RpcSupplierBaseService rpcSupplierBaseService;
    @Resource
    private DongDongMessageRcpService dongDongMessageRcpService;
    @Resource
    private SkuStockThresholdAtomicService skuStockThresholdAtomicService;
    @Resource
    private AbnormalStockStatisticsAtomicService abnormalStockStatisticsAtomicService;
    @Resource
    private CategoryAtomicService categoryAtomicService;
    @Resource
    private SkuRpcService skuRpcService;

    @LafValue("isc.task.sku.stock.dongdong.config")
    private String stockDongMessageConfig;
    @LafValue("isc.task.sku.stock.dongdong.country.receivers.config")
    private String stockReceiverConfig;
    @LafValue("isc.task.sku.stock.test.firstCategory.config")
    private Set<Long> testFirstCategoryIdSets;
    @Value("${jdi.isc.task.env}")
    private String env;
    @LafValue("isc.task.sku.stock.dongdong.test.receivers.config")
    private String testReceiveConfig;

    private static final String LOW_STOCK_LIST_EXCEL_NAME = "below_safety_stock_list";
    private static final String OUT_STOCK_LIST_EXCEL_NAME = "Out_of_Stock_List";
    private static final int LOW_STOCK_TYE = 1;
    private static final int OUT_STOCK_TYE = 2;
    private static final int COUNTRY = 1;
    private static final int BUYER = 2;
    private Set<Long> testLastCategoryIds = Sets.newHashSet();
    @SneakyThrows
    @XxlJob("statisticsStockJob")
    public ReturnT<String> statisticsStockJob(String param) {
        // 初始化测试末级类目ID集合
        this.initLastCategory();
        // 分页处理安全库存阈值数据，并将异常库存数据分类
        this.handleThresholdByPage();

        // 分页处理无库存数据，并将异常库存数据落库
        this.handleOutOfStockByPage();

        // 按照统计好的数据发送邮件和消息
        this.handleAbnormalStockAndMessage();

        return ReturnT.SUCCESS;
    }

    private void handleAbnormalStockAndMessage() {
        long abnormalTotal = abnormalStockStatisticsAtomicService.count();
        if (abnormalTotal <= 0) {
            log.warn("StockThresholdStatisticJob.statisticsStockJob 日期：{} 安全库存阈值数据为空 ", DateUtil.getCurrentDatetime());
            return;
        }

        log.info("StockThresholdStatisticJob.statisticsStockJob 开始发送低库存预警消息给采销、国家经理");
        // 1、低库存发送给采销
        this.handleAbnormalStockAndMessageByBuyer(LOW_STOCK_TYE);
        // 2、低库存发送给国家商品经理
        this.handleAbnormalStockAndMessageByCountry(LOW_STOCK_TYE);

        log.info("StockThresholdStatisticJob.statisticsStockJob 开始发送无库存预警消息给采销、国家经理");
        // 3、无库存发送给采销
        this.handleAbnormalStockAndMessageByBuyer(OUT_STOCK_TYE);
        // 4、无库存发送给国家商品经理
        this.handleAbnormalStockAndMessageByCountry(OUT_STOCK_TYE);
    }

    /**
     * 按采销发送消息
     */
    private void handleAbnormalStockAndMessageByBuyer(Integer type) {
        // 异常库存数据总数量
        long stockTotal = abnormalStockStatisticsAtomicService.count(Wrappers.lambdaQuery(AbnormalStockStatisticsPO.class)
                .eq(AbnormalStockStatisticsPO::getType, type)
                .eq(AbnormalStockStatisticsPO::getYn, YnEnum.YES.getCode()));
        if (stockTotal <= 0) {
            return;
        }
        // 查询所有采销
        List<Object> buyers = abnormalStockStatisticsAtomicService.listObjs(Wrappers.lambdaQuery(AbnormalStockStatisticsPO.class)
                .select(AbnormalStockStatisticsPO::getBuyer).eq(AbnormalStockStatisticsPO::getType, type)
                .eq(AbnormalStockStatisticsPO::getYn, YnEnum.YES.getCode())
                .groupBy(AbnormalStockStatisticsPO::getBuyer));
        if (CollectionUtils.isEmpty(buyers)) {
            return;
        }

        int len = buyers.size();
        for (int i = 0; i < len; i++) {
            if (Objects.isNull(buyers.get(i))){
                continue;
            }
            String buyer = (String) buyers.get(i);
            log.info("handleAbnormalStockAndMessageByBuyer 查询采销库存预警信息 buyer={}",buyer);
            LambdaQueryWrapper<AbnormalStockStatisticsPO> queryWrapper = Wrappers.lambdaQuery(AbnormalStockStatisticsPO.class)
                    .eq(AbnormalStockStatisticsPO::getType, type)
                    .eq(AbnormalStockStatisticsPO::getBuyer, buyer)
                    .eq(AbnormalStockStatisticsPO::getYn, YnEnum.YES.getCode());
            // 1、低库存发送给采销
            List<AbnormalStockStatisticsPO> buyerStatisticsPoList = abnormalStockStatisticsAtomicService.list(queryWrapper);
            // 过滤掉不在池商品
            List<AbnormalStockStatisticsPO> filterStatisticList = filterPoolSku(buyerStatisticsPoList);
            if (CollectionUtils.isEmpty(filterStatisticList)) {
                log.error("StockThresholdStatisticJob.handleAbnormalStockAndMessageByBuyer buyer={}，无入池商品",buyer);
                continue;
            }
            // 上传文件返回文件链接
            String url = uploadStockStatisticsResult(filterStatisticList, buyer, type == 1 ? LOW_STOCK_LIST_EXCEL_NAME : OUT_STOCK_LIST_EXCEL_NAME) ;
            if (StringUtils.isBlank(url)) {
                log.error("StockThresholdStatisticJob.handleAbnormalStockAndMessageByBuyer 上传文件结果为空 buyer={},type={}", buyer, type);
                continue;
            }
            // 京ME消息体
            sendDongDongMessage(type, filterStatisticList, url, Lists.newArrayList(buyer));
        }
    }


    /**
     * 根据仓库国家和货源国过滤异常库存统计数据，筛选出不在客户池的备货品和本土品。
     * @param statisticsPoList 需要过滤的异常库存统计数据列表
     * @return 过滤后的结果列表
     */
    private List<AbnormalStockStatisticsPO> filterPoolSku(List<AbnormalStockStatisticsPO> statisticsPoList) {
        if (CollectionUtils.isEmpty(statisticsPoList)) {
            return Collections.emptyList();
        }

        List<AbnormalStockStatisticsPO> resultList = Lists.newArrayList();
        // 备货品根据仓库国家过滤
        Map<String, List<AbnormalStockStatisticsPO>> warehouseStatisticPoMap = statisticsPoList.stream().filter(Objects::nonNull).filter(po -> StringUtils.isNotBlank(po.getWarehouseNo())).collect(Collectors.groupingBy(AbnormalStockStatisticsPO::getWarehouseCountryCode));
        if (MapUtils.isNotEmpty(warehouseStatisticPoMap)) {
            // 按照国家维度查询统计数据
            warehouseStatisticPoMap.forEach((countryCode,poList)->{
                // 批量查询sku是否在客户池
                filterNoCustomerPool(countryCode, poList, resultList);
            });
        }
        // 本土品根据货源国过滤
        Map<String, List<AbnormalStockStatisticsPO>> sourceCountryStatisticPoMap = statisticsPoList.stream().filter(Objects::nonNull).filter(po -> StringUtils.isBlank(po.getWarehouseNo())).collect(Collectors.groupingBy(AbnormalStockStatisticsPO::getSourceCountryCode));
        if (MapUtils.isNotEmpty(sourceCountryStatisticPoMap)) {
            sourceCountryStatisticPoMap.forEach((countryCode,poList)->{
                filterNoCustomerPool(countryCode, poList, resultList);
            });
        }

        return resultList;
    }

    private void filterNoCustomerPool(String countryCode, List<AbnormalStockStatisticsPO> poList, List<AbnormalStockStatisticsPO> resultList) {
        // 批量查询sku是否在客户池
        List<List<AbnormalStockStatisticsPO>> partition = Lists.partition(poList, 100);
        for (List<AbnormalStockStatisticsPO> sub : partition) {
            Set<String> skuIdStrs = sub.stream().map(po -> String.valueOf(po.getSkuId())).collect(Collectors.toSet());
            Map<String, SkuBaseInfoApiDTO> apiDTOMap = getSkuInfoMap(countryCode, skuIdStrs);
            log.info("StockThresholdStatisticJob.filterNoCustomerPool 按照国家统计商品是否入客户池 国家={},skuIds={} 返回商品信息个数:{}", countryCode,JSON.toJSONString(skuIdStrs),apiDTOMap.size());
            if (MapUtils.isEmpty(apiDTOMap)) {
                return;
            }
            sub.forEach(po-> {
                String skuStr = String.valueOf(po.getSkuId());
                if (apiDTOMap.containsKey(skuStr) && Objects.nonNull(apiDTOMap.get(skuStr))) {
                    SkuBaseInfoApiDTO skuBaseInfoApiDTO = apiDTOMap.get(skuStr);
                    log.info("StockThresholdStatisticJob.filterNoCustomerPool 按照国家统计商品是否入客户池 国家={},skuStr={} 商品信息：{}", countryCode,skuStr, JSON.toJSONString(skuBaseInfoApiDTO));
                    if (null != skuBaseInfoApiDTO.getInCustomerPool() && skuBaseInfoApiDTO.getInCustomerPool()) {
                        resultList.add(po);
                    }
                }
            });
        }
    }

    private Map<String, SkuBaseInfoApiDTO> getSkuInfoMap(String countryCode, Set<String> skuIdStrs) {
        QuerySkuReqDTO skuReqDTO = new QuerySkuReqDTO(skuIdStrs, countryCode);
        skuReqDTO.setSkuQueryEnums(Sets.newHashSet(SkuQueryEnum.CUSTOMER_POOL));
        return skuRpcService.querySkuInfoMap(skuReqDTO);
    }

    private String buildDongDongMessage(Integer type, List<AbnormalStockStatisticsPO> buyerStatisticsPoList, String url) {
        // 拼接消息体
        String message = "";
        // 中文消息体
        String zhMessage = ConfigUtils.getStringFromJsonString(stockDongMessageConfig, String.valueOf(type), LangConstant.LANG_ZH);
        if (StringUtils.isNotBlank(zhMessage)){
            String zhDateFormatStr = DateUtil.formatDate(new Date(), DateUtil.DEFAULT_CHINESE_DATE_PATTERN);
            message += String.format(zhMessage, zhDateFormatStr, buyerStatisticsPoList.size(), url) + "\n\n";
        }
        // 英语消息体
        String enMessage = ConfigUtils.getStringFromJsonString(stockDongMessageConfig, String.valueOf(type), LangConstant.LANG_EN);
        if (StringUtils.isNotBlank(enMessage)) {
            String enDateFormatStr = DateUtil.formatDate(new Date(), DateUtil.DEFAULT_DAY_PATTERN_THAI);
            message += String.format(enMessage, enDateFormatStr, buyerStatisticsPoList.size(), url);
        }
        return message;
    }

    /**
     * 按采销发送消息
     */
    private void handleAbnormalStockAndMessageByCountry(Integer type) {
        // 异常库存数据总数量
        long stockTotal = abnormalStockStatisticsAtomicService.count(Wrappers.lambdaQuery(AbnormalStockStatisticsPO.class)
                .eq(AbnormalStockStatisticsPO::getType, type)
                .eq(AbnormalStockStatisticsPO::getYn, YnEnum.YES.getCode()));
        if (stockTotal <= 0) {
            return;
        }
        Set<Object> filterCountryCodeSet = getCountryCodeList(type);
        if (CollectionUtils.isEmpty(filterCountryCodeSet)) {
            return;
        }

        for (Object o : filterCountryCodeSet) {
            String countryCode = (String)o;
            // 获取当前国家接收人
            String receiverStr = ConfigUtils.getStringByKey(stockReceiverConfig, countryCode);
            if (StringUtils.isBlank(receiverStr)) {
                continue;
            }
            // 跳过国家为中国
            if (CountryConstant.COUNTRY_ZH.equals(countryCode)) {
                continue;
            }
            // 按国家维度查询所有统计信息
            List<AbnormalStockStatisticsPO> filterStatisticPoList = getAbnormalStockStatisticsList(type, countryCode);
            if (CollectionUtils.isEmpty(filterStatisticPoList )) {
                continue;
            }
            // 过滤掉不在池商品
            List<AbnormalStockStatisticsPO> filterInPoolStatisticList = filterPoolSku(filterStatisticPoList);
            if (CollectionUtils.isEmpty(filterInPoolStatisticList)) {
                continue;
            }
            // 上传文件返回文件链接
            String url = this.uploadStockStatisticsResult(filterInPoolStatisticList, countryCode, type == 1 ? LOW_STOCK_LIST_EXCEL_NAME : OUT_STOCK_LIST_EXCEL_NAME) ;
            // 发送消息
            this.sendDongDongMessage(type, filterInPoolStatisticList, url, Lists.newArrayList(receiverStr.split(Constant.COMMA)));
        }
    }

    /**
     * 获取指定类型和国家代码的异常库存统计列表。
     * @param type 异常库存类型
     * @param countryCode 国家代码
     * @return 异常库存统计列表
     */
    private List<AbnormalStockStatisticsPO> getAbnormalStockStatisticsList(Integer type, String countryCode) {
        // 处理非中国货源国的商品库存
        LambdaQueryWrapper<AbnormalStockStatisticsPO> queryWrapper = Wrappers.lambdaQuery(AbnormalStockStatisticsPO.class)
                .eq(AbnormalStockStatisticsPO::getType, type)
                .eq(AbnormalStockStatisticsPO::getSourceCountryCode, countryCode)
                .eq(AbnormalStockStatisticsPO::getYn, YnEnum.YES.getCode());
        List<AbnormalStockStatisticsPO> localStatisticsPoList = abnormalStockStatisticsAtomicService.list(queryWrapper);

        // 处理跨境备货的商品库存
        LambdaQueryWrapper<AbnormalStockStatisticsPO> warehouseQueryWrapper = Wrappers.lambdaQuery(AbnormalStockStatisticsPO.class)
                .eq(AbnormalStockStatisticsPO::getType, type)
                .eq(AbnormalStockStatisticsPO::getWarehouseCountryCode, countryCode)
                .eq(AbnormalStockStatisticsPO::getSourceCountryCode, CountryConstant.COUNTRY_ZH)
                .eq(AbnormalStockStatisticsPO::getYn, YnEnum.YES.getCode());

        List<AbnormalStockStatisticsPO> purchaseModelStatisticsPoList = abnormalStockStatisticsAtomicService.list(warehouseQueryWrapper);
        if (CollectionUtils.isEmpty(localStatisticsPoList)) {
            localStatisticsPoList = Lists.newArrayList();
        }
        if (CollectionUtils.isNotEmpty(purchaseModelStatisticsPoList)) {
            localStatisticsPoList.addAll(purchaseModelStatisticsPoList);
        }
        if (CollectionUtils.isEmpty(localStatisticsPoList)) {
            return Collections.emptyList();
        }

        // 过滤掉重复的商品和备货仓关系数据
        List<AbnormalStockStatisticsPO> filterStatisticPoList = localStatisticsPoList.stream()
                .collect(Collectors.toMap(item -> item.getSkuId() + item.getWarehouseNo()
                        , item -> item, (existing, replacement) -> existing))
                .values().stream().collect(Collectors.toList());
        return filterStatisticPoList;
    }

    /**
     * 获取指定类型的国家编码集合
     * @param type 类型
     * @return 国家编码集合
     */
    private Set<Object> getCountryCodeList(Integer type) {
        // 货源国国家列表
        List<Object> countryCodeList = abnormalStockStatisticsAtomicService.listObjs(Wrappers.lambdaQuery(AbnormalStockStatisticsPO.class)
                .select(AbnormalStockStatisticsPO::getSourceCountryCode).eq(AbnormalStockStatisticsPO::getType, type)
                .eq(AbnormalStockStatisticsPO::getYn, YnEnum.YES.getCode())
                .groupBy(AbnormalStockStatisticsPO::getSourceCountryCode));

        // 备货国家编码列表
        List<Object> warehouseCountryCodeList = abnormalStockStatisticsAtomicService.listObjs(Wrappers.lambdaQuery(AbnormalStockStatisticsPO.class)
                .select(AbnormalStockStatisticsPO::getWarehouseCountryCode).eq(AbnormalStockStatisticsPO::getType, type)
                .isNotNull(AbnormalStockStatisticsPO::getWarehouseNo)
                .isNotNull(AbnormalStockStatisticsPO::getWarehouseCountryCode)
                .eq(AbnormalStockStatisticsPO::getSourceCountryCode,CountryConstant.COUNTRY_ZH)
                .eq(AbnormalStockStatisticsPO::getYn, YnEnum.YES.getCode())
                .groupBy(AbnormalStockStatisticsPO::getWarehouseCountryCode));

        // 所有国家编码
        if (CollectionUtils.isEmpty(countryCodeList)) {
            countryCodeList = Lists.newArrayList();
        }
        if (CollectionUtils.isNotEmpty(warehouseCountryCodeList)) {
            countryCodeList.addAll(warehouseCountryCodeList);
        }

        // 国家编码去重
        Set<Object> filterCountryCodeSet = Sets.newHashSet(countryCodeList);
        return filterCountryCodeSet;
    }

    private void sendDongDongMessage(Integer type, List<AbnormalStockStatisticsPO> buyerStatisticsPoList, String url, List<String> receiverStr) {
        // 京ME消息体
        String message = buildDongDongMessage(type, buyerStatisticsPoList, url);
        // 发送消息
        SendMessageReqDTO messageReqDTO = new SendMessageReqDTO();
        messageReqDTO.setMessage(message);
        messageReqDTO.setRobotType(RobotTypeEnum.SKU_STOCK_ROBOT.getType());
        if (StringUtils.isNotBlank(testReceiveConfig) && !Constant.STAR.equals(testReceiveConfig)){
            receiverStr = Arrays.asList(testReceiveConfig.split(Constant.COMMA));
            log.info("sendDongDongMessage 当前接收人:receiverStr={},文件链接:{}",receiverStr,url);

            messageReqDTO.setReceiveErps(receiverStr);
            messageReqDTO.setTraceId(UUID.randomUUID().toString());
            dongDongMessageRcpService.sendMessage(messageReqDTO);
        }else {
            messageReqDTO.setReceiveErps(receiverStr);
            messageReqDTO.setTraceId(UUID.randomUUID().toString());
            dongDongMessageRcpService.sendMessage(messageReqDTO);
        }
    }


    private String uploadStockStatisticsResult(List<AbnormalStockStatisticsPO> poList, String buyer,String filePrefix) {
        List<AbnormalStockStatisticsExcelDTO> excelDTOList = Lists.newArrayListWithExpectedSize(poList.size());
        Set<Long> skuIds = poList.stream().map(AbnormalStockStatisticsPO::getSkuId).collect(Collectors.toSet());
        // 查询商品名称
        Map<Long, SkuInfoVO> skuNameMap = skuAtomicService.querySkuNameMap(Lists.newArrayList(skuIds), LangConstant.LANG_ZH);

        // 根据来源国家代码分组获取供应商编码
        Map<Boolean, Set<String>> vendorCodesMap = poList.stream()
                .collect(Collectors.partitioningBy(
                        po -> COUNTRY_CODE_CN.equals(po.getSourceCountryCode()),
                        Collectors.mapping(AbnormalStockStatisticsPO::getVendorCode, Collectors.toSet())));

        // 获取本本供应商编码
        Set<String> vendorCodes = vendorCodesMap.get(false);
        Map<String, SupplierBaseInfoRes> supplierBaseMap = getPagedSupplierBaseInfo(vendorCodes);
        log.info("uploadStockStatisticsResult querySupplierBaseInfoMap vendorCodes:{}, supplierBaseMap:{}", vendorCodes, JSON.toJSONString(supplierBaseMap));

        // 获取跨境供应商编码
        Set<String> jdVendorCodes = vendorCodesMap.get(true);
        Map<String, JdSupplierBaseInfoRes> jdSupplierBaseMap = rpcSupplierBaseService.queryJdSupplierBaseInfoMap(jdVendorCodes);
        log.info("uploadStockStatisticsResult querySupplierBaseInfoMap jdVendorCodes:{}, jdSupplierBaseMap:{}", jdVendorCodes, JSON.toJSONString(jdSupplierBaseMap));

        // 查询仓库名称
        Set<String> warehouseNoSet = poList.stream().filter(Objects::nonNull).map(AbnormalStockStatisticsPO::getWarehouseNo).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        // 查询仓库信息
        Map<String, WarehouseResDTO> warehouseMap = queryWarehouseResMapByNos(warehouseNoSet);

        //获取商品各级类目名称
        Map<Long,Map<Integer,String>>  skuCategoryNameMap = getCategoryNameBySkuIds(skuIds);
        log.info("uploadStockStatisticsResult getCategoryNameBySkuIds skuIds:{}, supplierBaseMap:{}", skuIds, JSON.toJSONString(skuCategoryNameMap));


        Set<String> repeatSkuSet = Sets.newHashSet();
        poList.forEach(po -> {
            // 不存在重复的skuId+仓库编码
            if (!repeatSkuSet.contains(po.getSkuId() + po.getWarehouseNo())) {
                String supplierName="";
                if (COUNTRY_CODE_CN.equals(po.getSourceCountryCode())) {
                    //跨境
                    supplierName = jdSupplierBaseMap.getOrDefault(po.getVendorCode(), new JdSupplierBaseInfoRes()).getVendorName();
                } else {
                    //本本
                    SupplierBaseInfoRes result = supplierBaseMap.get(po.getVendorCode());
                    log.info("uploadStockStatisticsResult supplierName result:{}", JSON.toJSONString(result));
                    if (result != null) {
                        supplierName = result.getBusinessLicenseName();
                    }
                    supplierName = supplierBaseMap.getOrDefault(po.getVendorCode(), new SupplierBaseInfoRes()).getBusinessLicenseName();
                }

                if (StringUtils.isBlank(supplierName)) {
                    log.info("uploadStockStatisticsResult supplierName is empty, skuId:{}, vendorCode:{}, localVendorResult:{}, crossVendorResult:{}",
                            po.getSkuId(),
                            po.getVendorCode(),
                            JSON.toJSONString(supplierBaseMap.getOrDefault(po.getVendorCode(), new SupplierBaseInfoRes())),
                            JSON.toJSONString(jdSupplierBaseMap.getOrDefault(po.getVendorCode(), new JdSupplierBaseInfoRes())));
                }

                excelDTOList.add(new AbnormalStockStatisticsExcelForBuyerDTO(po, skuNameMap,
                        this.getWarehouseName(warehouseMap, po.getWarehouseNo()), supplierName,
                        skuCategoryNameMap.getOrDefault(po.getSkuId(), new HashMap<>())));
            }

            repeatSkuSet.add(po.getSkuId() + po.getWarehouseNo());
        });

        // 生成结果文件，然后上传到云服务，返回结果链接
        String fileUrl = null;
        String resultName = filePrefix + Constant.UNDER_LINE + buyer + Constant.UNDER_LINE + DateUtil.formatDay(new Date()) + ".xlsx";
        ByteArrayOutputStream targetOutputStream = new ByteArrayOutputStream();
        try (ExcelWriter excelWriter = EasyExcel.write(targetOutputStream).build()) {
            excelWriter.write(excelDTOList, EasyExcel.writerSheet(0, "result")
                    .head(AbnormalStockStatisticsExcelForBuyerDTO.class)
                    .build());
            excelWriter.finish();

            // 将结果上传到云存储
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(targetOutputStream.toByteArray())) {
                String upload = s3Utils.upload(inputStream, FileTypeEnum.BATCH_FILE.getCode(), resultName);
                if (StringUtils.isBlank(upload)) {
                    Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STATISTICS_STOCK_UPLOAD_FILE,String.format("【%s】%s 采销或国家 %s 库存统计数据上传文件异常,文件类型 %s",env, LevelCode.P2.getMessage() , buyer,filePrefix));
                }else {
                    fileUrl = upload;
                }
            }
        } catch (Exception e) {
            log.error("StockThresholdStatisticJob.uploadStockStatisticsResult error, buyer={} ",buyer, e);
        } finally {
            log.info("StockThresholdStatisticJob.uploadStockStatisticsResult 采销「{}」文件地址={}", buyer,fileUrl);
        }
        return fileUrl;
    }

    public Map<String, SupplierBaseInfoRes> getPagedSupplierBaseInfo(Set<String> vendorCodes) {
        Map<String, SupplierBaseInfoRes> supplierBaseMap = new HashMap<>();

        // 将 vendorCodes 转换为 List
        List<String> vendorCodeList = new ArrayList<>(vendorCodes);

        // 使用 Guava 的 Lists.partition 方法进行分页
        List<List<String>> partitions = Lists.partition(vendorCodeList, PAGE_SIZE);

        for (List<String> batch : partitions) {
            // 执行分页查询
            Map<String, SupplierBaseInfoRes> batchResult = rpcSupplierBaseService.querySupplierBaseInfoMap(new HashSet<>(batch));
            supplierBaseMap.putAll(batchResult);

            log.info("uploadStockStatisticsResult querySupplierBaseInfoMap batch vendorCodes:{}, supplierBaseMap:{}", batch, JSON.toJSONString(batchResult));
        }

        return supplierBaseMap;
    }

    /**
     * 上传库存统计结果到云服务并返回结果链接。
     * @param poList 异常库存统计PO列表
     * @param buyer 采销商名称
     * @param filePrefix 库存类型，1表示低库存，2表示无库存
     * @return 结果文件的链接地址
     */
//    private String uploadStockStatisticsResult(List<AbnormalStockStatisticsPO> poList, String buyer,String filePrefix, Integer receiverType) {
//        List<AbnormalStockStatisticsExcelDTO> excelDTOList = Lists.newArrayListWithExpectedSize(poList.size());
//        Set<Long> skuIds = poList.stream().map(AbnormalStockStatisticsPO::getSkuId).collect(Collectors.toSet());
//        // 查询商品名称
//        Map<Long, SkuInfoVO> skuNameMap = skuAtomicService.querySkuNameMap(Lists.newArrayList(skuIds), LangConstant.LANG_ZH);
//
//        // 查询供应商
//        Set<String> vendorCodes = poList.stream().map(AbnormalStockStatisticsPO::getVendorCode).collect(Collectors.toSet());
//        Map<String, SupplierBaseInfoRes> supplierBaseMap = rpcSupplierBaseService.querySupplierBaseInfoMap(vendorCodes);
//        log.info("uploadStockStatisticsResult querySupplierBaseInfoMap verdorCodes:{}, supplierBaseMap:{}", vendorCodes, JSON.toJSONString(supplierBaseMap));
//
//        // 查询仓库名称
//        Set<String> warehouseNoSet = poList.stream().filter(Objects::nonNull).map(AbnormalStockStatisticsPO::getWarehouseNo).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
//        // 查询仓库信息
//        Map<String, WarehouseResDTO> warehouseMap = queryWarehouseResMapByNos(warehouseNoSet);
//
//        // 查询一级类目名称
//        //Map<Long, String> firstCategoryNameMap = getFirstCategoryNameBySkuIds(skuIds);
//        //获取商品各级类目名称
//        Map<Long,Map<Integer,String>>  skuCategoryNameMap = getCategoryNameBySkuIds(skuIds);
//        log.info("uploadStockStatisticsResult getCategoryNameBySkuIds skuIds:{}, supplierBaseMap:{}", skuIds, JSON.toJSONString(skuCategoryNameMap));
//
//
//        Set<String> repeatSkuSet = Sets.newHashSet();
//        poList.forEach(po ->{
//            // 不存在重复的skuId+仓库编码
//            if (!repeatSkuSet.contains(po.getSkuId() + po.getWarehouseNo())) {
//
//                String supplierName = supplierBaseMap.getOrDefault(po.getVendorCode(), new SupplierBaseInfoRes()).getBusinessLicenseName();
//                if (StringUtils.isNotBlank(supplierName)) {
//                    log.info("uploadStockStatisticsResult supplierName is empty, vendorCode:{}, result:{}",
//                            po.getVendorCode(), JSON.toJSONString(supplierBaseMap.getOrDefault(po.getVendorCode(), new SupplierBaseInfoRes())));
//                }
//
//                if (receiverType == BUYER) {
//                    //发给采销excel模板
//                    excelDTOList.add(
//                            new AbnormalStockStatisticsExcelForBuyerDTO(po, skuNameMap,
//                                    this.getWarehouseName(warehouseMap,
//                                            po.getWarehouseNo()), supplierBaseMap,
//                                    skuCategoryNameMap.getOrDefault(po.getSkuId(), new HashMap<>())
//                            ));
//                } else {
//                    ////发送给国家商品经理excel模板
//                    excelDTOList.add(
//                            new AbnormalStockStatisticsExcelForCountryDTO(po, skuNameMap,
//                                    this.getWarehouseName(warehouseMap, po.getWarehouseNo()), supplierBaseMap,
//                                    skuCategoryNameMap.getOrDefault(po.getSkuId(), new HashMap<>())
//                            ));
//                }
//            }
//
//            repeatSkuSet.add(po.getSkuId() + po.getWarehouseNo());
//        });
//
//        // 生成结果文件，然后上传到云服务，返回结果链接
//        String fileUrl = null;
//        String resultName = filePrefix + Constant.UNDER_LINE + buyer + Constant.UNDER_LINE + DateUtil.formatDay(new Date()) + ".xlsx";
//        ByteArrayOutputStream targetOutputStream = new ByteArrayOutputStream();
//        try (ExcelWriter excelWriter = EasyExcel.write(targetOutputStream).build()) {
//            excelWriter.write(excelDTOList, EasyExcel.writerSheet(0, "result")
//                    .head(receiverType == BUYER ? AbnormalStockStatisticsExcelForBuyerDTO.class : AbnormalStockStatisticsExcelForCountryDTO.class)
//                    .build());
//            excelWriter.finish();
//
//            // 将结果上传到云存储
//            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(targetOutputStream.toByteArray())) {
//                String upload = s3Utils.upload(inputStream, FileTypeEnum.BATCH_FILE.getCode(), resultName);
//                if (StringUtils.isBlank(upload)) {
//                    Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STATISTICS_STOCK_UPLOAD_FILE,String.format("【%s】%s 采销或国家 %s 库存统计数据上传文件异常,文件类型 %s",env, LevelCode.P2.getMessage() , buyer,filePrefix));
//                }else {
//                    fileUrl = upload;
//                }
//            }
//        } catch (Exception e) {
//            log.error("StockThresholdStatisticJob.uploadStockStatisticsResult error, buyer={} ",buyer, e);
//        } finally {
//            log.info("StockThresholdStatisticJob.uploadStockStatisticsResult 采销「{}」文件地址={}", buyer,fileUrl);
//        }
//        return fileUrl;
//    }

    /**
     * 根据仓库编号集合查询仓库资源映射表。
     * @param warehouseNoSet 仓库编号集合
     * @return 仓库资源映射表，key为仓库编号，value为对应的WarehouseResDTO对象
     */
    private Map<String, WarehouseResDTO> queryWarehouseResMapByNos(Set<String> warehouseNoSet) {
        Map<String, WarehouseResDTO> warehouseMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(warehouseNoSet)) {
            WarehouseBatchGetReqDTO reqDTO = new WarehouseBatchGetReqDTO();
            reqDTO.setWarehouseNos(warehouseNoSet);
            warehouseMap = warehouseRpcService.queryWarehouseMap(reqDTO);
        }
        return warehouseMap;
    }


    /**
     * 按页处理安全库存阈值。
     */
    private void handleThresholdByPage() {
        // 安全库存阈值总数
        long total = skuStockThresholdAtomicService.count();
        long current = 0;
        IPage<SkuStockThresholdPO> page = new Page<>();
        page.setSize(Constant.PURCHASE_PRICE_BATCH_SIZE);

        LambdaQueryWrapper<SkuStockThresholdPO> queryWrapper = Wrappers.lambdaQuery(SkuStockThresholdPO.class)
                .eq(SkuStockThresholdPO::getYn, YnEnum.YES.getCode())
                .orderByAsc(SkuStockThresholdPO::getCreateTime);

        do {
            // 设置当前页
            page.setCurrent(current);
            IPage<SkuStockThresholdPO> resPage = skuStockThresholdAtomicService.page(page, queryWrapper);
            if (null == resPage) {
                log.warn("StockThresholdStatisticJob.statisticsStockJob 查询安全库存阈值数据为空");
                break;
            }
            // 统计库存异常数据数据
            this.handleStockThreshold(resPage.getRecords());

            current++;
        }while (current * Constant.PURCHASE_PRICE_BATCH_SIZE < total);
    }

    /**
     * 按页处理安全库存阈值。
     */
    private void handleOutOfStockByPage() {
        // 安全库存阈值总数
        LambdaQueryWrapper<StockPO> totalWrapper = Wrappers.lambdaQuery(StockPO.class).eq(StockPO::getYn, YnEnum.YES.getCode());
        long total = stockAtomicService.count(totalWrapper);
        long current = 0;
        IPage<StockPO> page = new Page<>();
        page.setSize(Constant.PURCHASE_PRICE_BATCH_SIZE);

        LambdaQueryWrapper<StockPO> queryWrapper = Wrappers.lambdaQuery(StockPO.class)
                .eq(StockPO::getYn, YnEnum.YES.getCode())
                .orderByAsc(StockPO::getCreateTime);

        do {
            // 设置当前页
            page.setCurrent(current);
            IPage<StockPO> resPage = stockAtomicService.page(page, queryWrapper);
            if (null == resPage) {
                log.warn("StockThresholdStatisticJob.handleOutOfStockByPage  查询所有库存数据为空");
                break;
            }
            // 统计库存异常数据数据
            this.handleOutOfStock(resPage.getRecords());
            current++;
        }while (current * Constant.PURCHASE_PRICE_BATCH_SIZE < total);
    }

    /**
     * 处理库存不足的商品列表。
     * @param stockPOList 需要处理的库存商品列表。
     */
    private void handleOutOfStock(List<StockPO> stockPOList) {
        if (CollectionUtils.isEmpty(stockPOList)) {
            return;
        }

        // 备货仓ID
        Set<Long> warehouseIds = stockPOList.stream().filter(Objects::nonNull).filter(vo-> StringUtils.isNotBlank(vo.getWarehouseId())).map(StockPO::getWarehouseId).map(Long::valueOf).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> skuIds = stockPOList.stream().filter(Objects::nonNull).map(StockPO::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
        log.info("StockThresholdStatisticJob.handleOutOfStock start ,skuIds:{}", skuIds);

        // 查询SKU信息
        Map<Long, SkuPO> skuPoMap = skuAtomicService.batchQuerySkuPO(skuIds);
        // skuPoMap 为空，则直接返回
        if (MapUtils.isEmpty(skuPoMap)) {
            log.warn("StockThresholdStatisticJob.handleOutOfStock skuPoMap 为空,skuIds={}", JSON.toJSONString(skuIds));
            return;
        }
        // 查询spu信息
        Set<Long> spuIds = skuPoMap.values().stream().map(SkuPO::getSpuId).collect(Collectors.toSet());

        Map<Long, SpuPO> spuMap = spuAtomicService.getSpuMap(spuIds);
        // spuMap 为空，则直接返回
        if (MapUtils.isEmpty(spuMap)) {
            log.warn("StockThresholdStatisticJob.handleOutOfStock spuMap 为空,spuIds={}", JSON.toJSONString(spuIds));
            return;
        }

        // 查询仓库信息
        Map<Long, WarehouseResDTO> warehouseMap = Maps.newHashMap();
//        Map<String, Map<String, WarehouseSkuDTO>> skuWarehouseRelationMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(warehouseIds)) {
            WarehouseBatchGetReqDTO reqDTO = new WarehouseBatchGetReqDTO();
            reqDTO.setWarehouseIds(warehouseIds);
            warehouseMap = warehouseRpcService.queryWarehouseWarehouseIdMap(reqDTO);

            // 查询有备货仓的商品绑定关系信息映射
/*            Set<String> warehouseSkuIds = stockPOList.stream().filter(Objects::nonNull).filter(po -> StringUtils.isNotBlank(po.getWarehouseId())).map(po -> String.valueOf(po.getSkuId())).filter(Objects::nonNull).collect(Collectors.toSet());
            skuWarehouseRelationMap = warehouseRpcService.querySkuRelationMap(warehouseSkuIds);*/
        }

        List<AbnormalStockStatisticsPO> statisticsPOList = Lists.newArrayList();
        for (StockPO stockPO : stockPOList) {
            log.info("StockThresholdStatisticJob.handleOutOfStock process, skuId:{}, warehouseId:{}", stockPO.getSkuId(), stockPO.getWarehouseId());
            if (!skuPoMap.containsKey(stockPO.getSkuId())) {
                log.error("StockThresholdStatisticJob.handleOutOfStock skuId not exist:{}", stockPO.getSkuId());
                continue;
            }
            SkuPO skuPO = skuPoMap.get(stockPO.getSkuId());
            // 测试类目商品跳过
            if (testLastCategoryIds.contains(skuPO.getJdCatId())) {
                log.error("StockThresholdStatisticJob.handleOutOfStock testLastCategoryIds skip:{}", skuPO.getJdCatId());
                continue;
            }
            //  计算可售库存数量
/*            boolean onWaySale = Boolean.FALSE;
            if (MapUtils.isNotEmpty(skuWarehouseRelationMap)) {
                Map<String, WarehouseSkuDTO> warehouseSkuRelationMap = skuWarehouseRelationMap.getOrDefault(String.valueOf(stockPO.getSkuId()), Maps.newHashMap());
                // 在途可售
                if (MapUtils.isNotEmpty(warehouseSkuRelationMap) && StringUtils.isNotBlank(stockPO.getWarehouseId()) && Objects.equals(Constant.ONE,warehouseSkuRelationMap.getOrDefault(stockPO.getWarehouseId(),new WarehouseSkuDTO()).getOnWaySale())) {
                    onWaySale = Boolean.TRUE;
                }
            }*/
            Long availableStock = StockNumUtils.calculateAvailableStock(stockPO, Boolean.TRUE, OUT_STOCK_TYE);
            // 库存数量大于0，则跳过
            if (availableStock.compareTo(new Long(0)) > 0) {
                continue;
            }

            log.info("StockThresholdStatisticJob.handleOutOfStock zero stock skuId:{}, availableStock:{}", skuPO.getSkuId(), availableStock);
            WarehouseResDTO warehouseResDTO = StringUtils.isNotBlank(stockPO.getWarehouseId()) ? warehouseMap.getOrDefault(Long.valueOf(stockPO.getWarehouseId()), null) : null;
            SpuPO spuPO = spuMap.get(skuPO.getSpuId());
            statisticsPOList.add(new AbnormalStockStatisticsPO(skuPO, warehouseResDTO, OUT_STOCK_TYE, spuPO.getBuyer(),availableStock));
        }

        // 保存异常库存数据
        if (CollectionUtils.isNotEmpty(statisticsPOList)) {
            log.info("StockThresholdStatisticJob.handleOutOfStock save data:{}", JSON.toJSONString(statisticsPOList));
            abnormalStockStatisticsAtomicService.saveBatch(statisticsPOList);
        }
    }

    /**
     * 处理库存阈值记录，统计并保存异常库存数据。
     * @param records 库存阈值记录列表
     */
    private void handleStockThreshold(List<SkuStockThresholdPO> records) {
        try {
            if (CollectionUtils.isEmpty(records)) {
                return;
            }

            Set<Long> warehouseIds = records.stream().filter(Objects::nonNull).map(SkuStockThresholdPO::getWarehouseId).filter(Objects::nonNull).collect(Collectors.toSet());
            Set<Long> skuIds = records.stream().filter(Objects::nonNull).map(SkuStockThresholdPO::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
            log.info("StockThresholdStatisticJob.handleStockThreshold start ,skuIds:{}", skuIds);

            // 查询SKU信息
            Map<Long, SkuPO> skuPoMap = skuAtomicService.batchQuerySkuPO(skuIds);
            // skuPoMap 为空，则直接返回
            if (MapUtils.isEmpty(skuPoMap)) {
                log.warn("StockThresholdStatisticJob.handleStockThreshold skuPoMap 为空,skuIds={}", JSON.toJSONString(skuIds));
                return;
            }

            // 查询spu信息
            Set<Long> spuIds = skuPoMap.values().stream().map(SkuPO::getSpuId).collect(Collectors.toSet());

            Map<Long, SpuPO> spuMap = spuAtomicService.getSpuMap(spuIds);
            // spuMap 为空，则直接返回
            if (MapUtils.isEmpty(spuMap)) {
                log.warn("StockThresholdStatisticJob.handleStockThreshold spuMap 为空,spuIds={}", JSON.toJSONString(spuIds));
                return;
            }

            // 查询仓库信息
            Map<Long, WarehouseResDTO> warehouseMap = Maps.newHashMap();
            //Map<String, Map<String, WarehouseSkuDTO>> skuWarehouseRelationMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(warehouseIds)) {
                WarehouseBatchGetReqDTO reqDTO = new WarehouseBatchGetReqDTO();
                reqDTO.setWarehouseIds(warehouseIds);
                warehouseMap = warehouseRpcService.queryWarehouseWarehouseIdMap(reqDTO);

                // 查询有备货仓的商品绑定关系信息映射
/*                Set<String> warehouseSkuIds = records.stream().filter(Objects::nonNull).filter(po -> Objects.nonNull(po.getWarehouseId())).map(po -> String.valueOf(po.getSkuId())).filter(Objects::nonNull).collect(Collectors.toSet());
                skuWarehouseRelationMap = warehouseRpcService.querySkuRelationMap(warehouseSkuIds);*/
            }

            List<AbnormalStockStatisticsPO> statisticsPOList = Lists.newArrayList();
            for (SkuStockThresholdPO thresholdPO : records) {
                if (!skuPoMap.containsKey(thresholdPO.getSkuId())) {
                    log.error("StockThresholdStatisticJob.handleStockThreshold sku not exist ,skuId:{}", thresholdPO.getSkuId());
                    continue;
                }

                SkuPO skuPO = skuPoMap.get(thresholdPO.getSkuId());
                // 测试类目商品跳过
                if (testLastCategoryIds.contains(skuPO.getJdCatId())) {
                    log.error("StockThresholdStatisticJob.handleStockThreshold  testLastCategoryIds skip, skuId:{}", skuPO.getJdCatId());
                    continue;
                }

                StockPO stockPo = stockAtomicService.getStockBySkuIdAndWarehouseId(thresholdPO.getSkuId(), thresholdPO.getWarehouseId());
                if (Objects.isNull(stockPo)) {
                    log.error("StockThresholdStatisticJob.handleStockThreshold stockPo 为空,skuId={},warehouseId={}", thresholdPO.getSkuId(), thresholdPO.getWarehouseId());
                    continue;
                }
                //  计算可售库存数量
/*                boolean onWaySale = Boolean.FALSE;
                if (MapUtils.isNotEmpty(skuWarehouseRelationMap)) {
                    Map<String, WarehouseSkuDTO> warehouseSkuRelationMap = skuWarehouseRelationMap.getOrDefault(String.valueOf(thresholdPO.getSkuId()), Maps.newHashMap());
                    // 在途可售
                    if (MapUtils.isNotEmpty(warehouseSkuRelationMap) && Objects.nonNull(thresholdPO.getWarehouseId()) && Objects.equals(Constant.ONE,warehouseSkuRelationMap.getOrDefault(String.valueOf(thresholdPO.getWarehouseId()),new WarehouseSkuDTO()).getOnWaySale())) {
                        onWaySale = Boolean.TRUE;
                    }
                }*/
                Long availableStock = StockNumUtils.calculateAvailableStock(stockPo,  Boolean.TRUE, LOW_STOCK_TYE);
                // 库存数量大于等于安全库存阈值，则跳过
                if (availableStock.compareTo(thresholdPO.getStockThreshold()) >= 0 || availableStock.compareTo(new Long(0)) <= 0) {
                    continue;
                }

                log.info("StockThresholdStatisticJob.handleStockThreshold  warnStock skuId:{}, availableStock:{}, stockThreshold:{}",
                        skuPO.getSkuId(), availableStock, thresholdPO.getStockThreshold());
                // 异常库存数据类型
                WarehouseResDTO warehouseResDTO = Objects.nonNull(thresholdPO.getWarehouseId()) ? warehouseMap.getOrDefault(thresholdPO.getWarehouseId(), null) : null;
                SpuPO spuPO = spuMap.get(skuPO.getSpuId());
                statisticsPOList.add(new AbnormalStockStatisticsPO(skuPO, warehouseResDTO, LOW_STOCK_TYE, spuPO.getBuyer(),stockPo.getStock()));
            }
            // 保存异常库存数据
            if (CollectionUtils.isNotEmpty(statisticsPOList)) {
                log.info("StockThresholdStatisticJob.handleStockThreshold save data ,statisticsPOList:{}", JSON.toJSONString(statisticsPOList));
                abnormalStockStatisticsAtomicService.saveBatch(statisticsPOList);
            }
        } catch (Exception e) {
            log.error("StockThresholdStatisticJob.handleStockThreshold 异常", e);
        }

    }

    /**
     * 清空所有异常库存统计记录。
     */
    @XxlJob("truncateStockStatisticsJob")
    public ReturnT<String> truncateStockStatistics(String param) {
        // 删除所有异常库存统计记录
        abnormalStockStatisticsAtomicService.truncate();
        return ReturnT.SUCCESS;
    }

    /**
     * 根据仓库编号从仓库信息映射中获取对应的中文名称。
     * @param warehouseResDTOMap 仓库信息映射
     * @param warehouseNo 仓库编号
     * @return 对应的中文仓库名称，若无则返回空字符串
     */
    private String getWarehouseName(Map<String, WarehouseResDTO> warehouseResDTOMap,String warehouseNo) {
        if (StringUtils.isBlank(warehouseNo)) {
            return "";
        }
        // 根据仓库编号获取库存信息
        Map<String, String> warehouseNameMap = warehouseResDTOMap.getOrDefault(warehouseNo, new WarehouseResDTO()).getWarehouseNameMap();
        if (MapUtils.isEmpty(warehouseNameMap)) {
            return "";
        }
        return warehouseNameMap.getOrDefault(LangConstant.LANG_ZH, "");
    }

    /**
     * 根据 SKU ID 获取一级类目名称
     * @param skuIds SKU ID 集合
     * @return Map<Long, String> SKU ID 到一级类目名称的映射
     */
    public Map<Long, String> getFirstCategoryNameBySkuIds(Set<Long> skuIds) {
        try {
            if (CollectionUtils.isEmpty(skuIds)) {
                log.info("StockThresholdStatisticJob.getFirstCategoryNameBySkuIds skuIds is empty");
                return Collections.emptyMap();
            }

            // 1. 获取 SKU ID 到类目 ID 的映射
            Map<Long, Long> skuToCatIdMap = skuAtomicService.queryCatIdBySkuIds(skuIds);

            if (MapUtils.isEmpty(skuToCatIdMap)) {
                log.warn(String.format("StockThresholdStatisticJob.getFirstCategoryNameBySkuIds skuToCatIdMap is empty, skuIds=%s", JSON.toJSONString(skuIds)));
                return Collections.emptyMap();
            }

            // 2. 获取类目路径信息
            Map<Long, List<CategoryComboLangBoxVO>> categoryPathMap = categoryAtomicService.queryPath(new HashSet<>(skuToCatIdMap.values()));

            // 3. 构建结果 Map
            return skuToCatIdMap.entrySet().stream()
                    .filter(entry -> categoryPathMap.containsKey(entry.getValue()))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> {
                                List<CategoryComboLangBoxVO> path = categoryPathMap.get(entry.getValue());
                                if (CollectionUtils.isNotEmpty(path)) {
                                    for (CategoryComboLangBoxVO category : path) {
                                        if (category.getLevel() != null && category.getLevel() == 1) {
                                            return category.getZhName();
                                        }
                                    }
                                }
                                return null;
                            },
                            (v1, v2) -> v1,
                            HashMap::new
                    ));
        } catch (Exception e) {
            log.error("StockThresholdStatisticJob.getFirstCategoryNameBySkuIds error", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 根据SKU ID集合获取每个SKU对应的类目层级名称映射
     * @param skuIds SKU ID集合，不可为null但可为空集合
     * @return 返回嵌套Map结构，外层Key为SKU ID，内层Map的Key为类目层级(整数)、Value为对应层级的中文类目名称。当输入为空或查询无结果时返回空Map
     */
    public Map<Long, Map<Integer,String>> getCategoryNameBySkuIds(Set<Long> skuIds) {
        try {
            if (CollectionUtils.isEmpty(skuIds)) {
                log.info("StockThresholdStatisticJob.getFirstCategoryNameBySkuIds skuIds is empty");
                return Collections.emptyMap();
            }

            // 1. 获取 SKU ID 到类目 ID 的映射
            Map<Long, Long> skuToCatIdMap = skuAtomicService.queryCatIdBySkuIds(skuIds);

            if (MapUtils.isEmpty(skuToCatIdMap)) {
                log.warn(String.format("StockThresholdStatisticJob.getFirstCategoryNameBySkuIds skuToCatIdMap is empty, skuIds=%s", JSON.toJSONString(skuIds)));
                return Collections.emptyMap();
            }

            // 2. 获取类目路径信息
            Map<Long, List<CategoryComboLangBoxVO>> categoryPathMap = categoryAtomicService.queryPath(new HashSet<>(skuToCatIdMap.values()));

            // 3. 构建结果 Map
            return skuToCatIdMap.entrySet().stream()
                    .filter(entry -> categoryPathMap.containsKey(entry.getValue()))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> {
                                List<CategoryComboLangBoxVO> path = categoryPathMap.get(entry.getValue());
                                if (CollectionUtils.isNotEmpty(path)) {
                                    Map<Integer, String> categoryBoxMap = new HashMap<>();
                                    for (CategoryComboLangBoxVO category : path) {
                                        if (category.getLevel() != null) {
                                            categoryBoxMap.put(category.getLevel(), category.getZhName());
                                        }
                                    }
                                    return categoryBoxMap;
                                }
                                return Collections.emptyMap();
                            },
                            (v1, v2) -> v1,
                            HashMap::new
                    ));
        } catch (Exception e) {
            log.error("StockThresholdStatisticJob.getFirstCategoryNameBySkuIds error", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 初始化四级类目ID集合
     */
    private void initLastCategory() {

        if (CollectionUtils.isEmpty(testFirstCategoryIdSets)) {
            log.warn("StockThresholdStatisticJob.initLastCategory 测试一级类目配置为空");
            return;
        }
        List<CategoryPathVO> categoryPathVOList = categoryAtomicService.queryChildrenPathByFirst(testFirstCategoryIdSets, StatusEnum.ENABLE.getCode());
        if (CollectionUtils.isNotEmpty(categoryPathVOList)) {
           testLastCategoryIds = categoryPathVOList.stream().filter(Objects::nonNull).filter(o-> Objects.nonNull(o.getId4())).map(CategoryPathVO::getId4).collect(Collectors.toSet());
            log.info("StockThresholdStatisticJob.initLastCategory ,testLastCategoryIds:{}", testLastCategoryIds);
        }
    }
}
