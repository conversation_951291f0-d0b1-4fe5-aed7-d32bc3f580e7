package com.jdi.isc.task.center.service.work.task.handler.wimpExport;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdi.isc.order.center.api.orderRead.biz.manage.OrderReadManageApiDTO;
import com.jdi.isc.order.center.api.orderRead.biz.manage.OrderReadManagePageReqApiDTO;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskStatusEnum;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.library.common.enmus.file.FileTypeEnum;
import com.jdi.isc.task.center.common.exception.TaskExportException;
import com.jdi.isc.task.center.domain.common.biz.ConfigTaskVO;
import com.jdi.isc.task.center.domain.enums.TaskStrategyEnum;
import com.jdi.isc.task.center.domain.task.dto.TaskDTO;
import com.jdi.isc.task.center.domain.task.enums.ExportOrderAggDTOFieldUtils;
import com.jdi.isc.task.center.domain.task.excel.DynamicExportFieldConfig;
import com.jdi.isc.task.center.domain.task.excel.ExportOrderAggDTO;
import com.jdi.isc.task.center.domain.task.enums.ExportFieldTypeEnum;
import com.jdi.isc.task.center.domain.task.vo.TaskStrategyVO;
import com.jdi.isc.task.center.rpc.order.OrderRpcService;
import com.jdi.isc.task.center.service.work.task.frame.BaseExecutableAbsJob;
import com.jdi.isc.task.center.service.work.task.frame.JobExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态字段订单导出处理器
 * <AUTHOR>
 * @date 2025/8/19
 */
@Slf4j
@Service
@JobExecutor(taskBizType = TaskBizTypeEnum.ORDER_AGG_EXPORT)
public class DynamicOrderExportHandler extends BaseExecutableAbsJob<ExportOrderAggDTO> {

    private final static Long DEFAULT_SIZE = 100L;
    private final static Integer MAX_ITEM = 100000;

    @Resource
    private OrderRpcService orderRpcService;

    @Override
    public void export(TaskDTO<ExportOrderAggDTO> task) {
        if (task == null) {
            return;
        }
        log.info("DynamicOrderExportHandler.export taskId:{}", task.getTaskId());
        
        String resultName = RESULT_FOLDER + task.getTaskBizTypeEnum().getName() + Constant.UNDER_LINE + System.currentTimeMillis() + ".xlsx";
        ConfigTaskVO configTaskVO = JSONObject.parseObject(task.getReqJson(), ConfigTaskVO.class);

        // 获取字段配置
        List<DynamicExportFieldConfig> fieldConfigs = getFieldConfigs(configTaskVO.getExtInfo());
        if (CollectionUtils.isEmpty(fieldConfigs)) {
            throw new TaskExportException("导出字段配置为空");
        }

        ByteArrayOutputStream targetOutputStream = new ByteArrayOutputStream();
        try (ExcelWriter excelWriter = EasyExcel.write(targetOutputStream).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "result").build();
            writeSheet.setNeedHead(true);

            // 设置表头 - 所有表头在一行中展示
            List<String> headers = new ArrayList<>();
            for (DynamicExportFieldConfig config : fieldConfigs) {
                headers.add(config.getDisplayName());
            }
            List<List<Object>> fieldList = new ArrayList<>();
            fieldList.add(new ArrayList<>(headers));
            excelWriter.write(fieldList, writeSheet);

            // 分页查询数据并导出
            int done = 0;
            long index = 1L;
            OrderReadManagePageReqApiDTO queryParam = JSON.parseObject(configTaskVO.getParam(),OrderReadManagePageReqApiDTO.class);
            queryParam.setOperate(task.getOperator());
            List<OrderReadManageApiDTO> batchRes = queryData(queryParam, index);
            while (CollectionUtils.isNotEmpty(batchRes) && done < MAX_ITEM) {
                List<List<Object>> dataList = convertToExcelData(batchRes, fieldConfigs);
                excelWriter.write(dataList, writeSheet);
                
                done += batchRes.size();
                index += 1;
                batchRes = queryData(queryParam, index);
            }

            task.setEndTime(System.currentTimeMillis());
            excelWriter.finish();

            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(targetOutputStream.toByteArray())) {
                task.setResultUrl(s3Utils.upload(inputStream, FileTypeEnum.BATCH_FILE.getCode(), resultName));
                task.setStrategyEnum(TaskStrategyEnum.WIMP_TASK);
                TaskStrategyVO taskStrategyVO = task.getTaskStrategyVO();
                taskStrategyVO.setStatus(TaskStatusEnum.SUCCESS);
                taskStrategyVO.setResFileUrl(task.getResultUrl());
                super.getStrategyService(taskStrategyVO.getTaskStrategyEnum()).update(new TaskStrategyVO(task, TaskStatusEnum.SUCCESS));
            }
        } catch (Exception e) {
            log.error("DynamicOrderExportHandler.export error, taskId:{}", task.getTaskId(), e);
            throw new TaskExportException(task.getTaskId() + e.getMessage());
        } finally {
            log.info("DynamicOrderExportHandler.export 任务「{}」执行完毕:{},文件地址:{}", 
                task.getTaskId(), task.getOssPutMd5(), task.getResultUrl());
        }
    }

    /**
     * 获取导出字段配置
     */
    private List<DynamicExportFieldConfig> getFieldConfigs(String extInfo) {
        try {
            List<String> lists = JSON.parseArray(extInfo, String.class);
            List<DynamicExportFieldConfig> configItems = lists.stream().map(ExportOrderAggDTOFieldUtils::getFieldConfig).collect(Collectors.toList());
            configItems.sort(Comparator.comparing(
                    DynamicExportFieldConfig::getSort,
                    Comparator.nullsLast(Comparator.naturalOrder())
            ));
            return configItems;
        } catch (Exception e) {
            log.error("解析字段配置失败,param:{},e:", extInfo,e);
            throw new TaskExportException("解析导出字段配置失败");
        }
    }


    /**
     * 分页查询数据
     */
    private List<OrderReadManageApiDTO> queryData(OrderReadManagePageReqApiDTO param, long pageIndex) {
        try {
            param.setIndex(pageIndex);
            param.setSize(DEFAULT_SIZE);
            return orderRpcService.queryAggregatePage(param).getRecords();
        } catch (Exception e) {
            log.error("查询订单数据失败,param:", e);
            throw new TaskExportException("查询订单数据失败");
        }
    }

    /**
     * 转换为Excel数据
     */
    private List<List<Object>> convertToExcelData(List<OrderReadManageApiDTO> dataList,
            List<DynamicExportFieldConfig> fieldConfigs) {
        List<List<Object>> result = new ArrayList<>();
        
        for (OrderReadManageApiDTO data : dataList) {
            if (Constant.ZERO.equals(data.getParentOrderId())){
                data.setParentOrderId(null);
            }
            List<Object> rowData = new ArrayList<>();
            for (DynamicExportFieldConfig config : fieldConfigs) {
                Object value = getFieldValue(data, config);
                rowData.add(formatFieldValue(value, config));
            }
            result.add(rowData);
        }
        
        return result;
    }

    /**
     * 获取字段值
     */
    private Object getFieldValue(OrderReadManageApiDTO data, DynamicExportFieldConfig config) {
        try {
            Field field = OrderReadManageApiDTO.class.getDeclaredField(config.getFieldName());
            field.setAccessible(true);
            return field.get(data);
        } catch (Exception e) {
            log.error("获取字段值失败, fieldName:{}", config.getFieldName(), e);
            return null;
        }
    }

    /**
     * 格式化字段值
     */
    private Object formatFieldValue(Object value, DynamicExportFieldConfig config) {
        if (value == null) {
            return "";
        }

        ExportFieldTypeEnum fieldType = ExportFieldTypeEnum.getByCode(config.getFieldType());
        switch (fieldType) {
            case DATE:
            case DATETIME:
                if (value instanceof Long) {
                    String format = StringUtils.defaultIfBlank(config.getDateFormat(), "yyyy-MM-dd HH:mm:ss");
                    return new SimpleDateFormat(format).format(new Date((Long) value));
                }
                return value.toString();
            case DECIMAL:
                if (value instanceof BigDecimal) {
                    return ((BigDecimal) value).stripTrailingZeros().toString();
                }
                return value.toString();
            case ENUM_TYPE:
                // 处理枚举类型，直接调用枚举类的getNameByCode方法
                if (StringUtils.isNotBlank(config.getEnumClassName())) {
                    try {
                        // 加载枚举类
                        Class<?> enumClass = Class.forName(config.getEnumClassName());
                        if (enumClass.isEnum()) {
                            // 尝试获取getNameByCode静态方法
                            try {
                                // 尝试Integer参数
                                java.lang.reflect.Method getNameByCodeMethod = enumClass.getDeclaredMethod("getNameByCode", Integer.class);
                                Object result = getNameByCodeMethod.invoke(null, Integer.valueOf(value.toString()));
                                if (result != null) {
                                    return result.toString();
                                }
                            } catch (NoSuchMethodException e1) {
                                // 尝试String参数
                                try {
                                    java.lang.reflect.Method getNameByCodeMethod = enumClass.getDeclaredMethod("getNameByCode", String.class);
                                    Object result = getNameByCodeMethod.invoke(null, value.toString());
                                    if (result != null) {
                                        return result.toString();
                                    }
                                } catch (NoSuchMethodException e2) {
                                    log.debug("枚举类{}没有getNameByCode方法", config.getEnumClassName());
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("获取枚举名称失败, enumClassName:{}, value:{}", config.getEnumClassName(), value, e);
                    }
                }
                return value.toString();
            default:
                return value.toString();
        }
    }
}