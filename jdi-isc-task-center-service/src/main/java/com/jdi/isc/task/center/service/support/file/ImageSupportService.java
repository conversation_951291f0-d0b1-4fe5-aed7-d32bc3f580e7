package com.jdi.isc.task.center.service.support.file;


import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Sets;
import com.jdi.isc.product.soa.api.outUrl.res.OutUrlTransformRes;
import com.jdi.isc.task.center.common.utils.ImageUtils;
import com.jdi.isc.task.center.domain.common.biz.ResultBaseVO;
import com.jdi.isc.task.center.rpc.file.OutUrlRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description：ImageSupportService
 * @Date 2025-08-15
 */
@Slf4j
@Service
public class ImageSupportService {

    @Resource
    private OutUrlRpcService outUrlRpcService;

    /**
     * 将文本中的外链图片转换为京东图片链接
     * @param text 需要处理的文本内容
     * @return 处理后的结果
     */
    public ResultBaseVO convertTextImageOutUrlToJdUrl(String text){
        if (StringUtils.isBlank(text)){
            return new ResultBaseVO(Boolean.FALSE,"商品详情文本内容为空");
        }

        ResultBaseVO resultBaseVO = new ResultBaseVO();
        resultBaseVO.setData(text);
        try{
            Stopwatch stopwatch = Stopwatch.createStarted();
            List<String> outImgUrlList = ImageUtils.convertDesc2ImageList(text);

            if (CollectionUtils.isEmpty(outImgUrlList)){
                log.warn("convertTextImageOutUrlToJdUrl 文本中未解析出图片链接,text={}",text);
                return resultBaseVO;
            }

            // 外部图片链接转内部链接
            Map<String, OutUrlTransformRes> outUrlTransformResMap = outUrlRpcService.batchTransformOutUrlMap(Sets.newHashSet(outImgUrlList));
            if (MapUtils.isEmpty(outUrlTransformResMap)) {
                resultBaseVO.failed(String.format("图片链接转换失败，请检查图片链接是否正确，图片链接：%s", JSON.toJSONString(outImgUrlList)));
            }else if (outUrlTransformResMap.values().stream().anyMatch(out-> !out.getSuccess())){
                Optional<OutUrlTransformRes> first = outUrlTransformResMap.values().stream().filter(out -> !out.getSuccess()).findFirst();
                first.ifPresent(outUrlTransformRes -> resultBaseVO.failed(String.format("图片链接转换失败，请检查图片链接是否正确，图片链接：%s", outUrlTransformRes.getOutUrl())));
            }else {
                outUrlTransformResMap.forEach((outUrl,res)-> {
                    String string = text.replaceAll(outUrl, res.getJdUrl());
                    log.info("handleDescription 图片链接转换成功，原链接：{}，转换后链接：{} 替换结果：{}",outUrl,res.getJdUrl(), string);
                });
                resultBaseVO.setData(text);
            }
            log.info("handleDescription convertDescriptionOutUrlToJdUrl 图片链接转换耗时：{}ms",stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        }catch (Exception e){
            log.error("convertImageOutUrlToJdUrl 图片链接转换失败,text={}",text,e);
            resultBaseVO.failed("图片链接转换失败");
        }
        return resultBaseVO;
    }
}
