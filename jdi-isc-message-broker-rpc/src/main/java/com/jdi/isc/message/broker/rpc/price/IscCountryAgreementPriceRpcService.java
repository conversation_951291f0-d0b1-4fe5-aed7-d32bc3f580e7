package com.jdi.isc.message.broker.rpc.price;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.agreementPrice.req.CountryAgreementPriceReqDTO;
import com.jdi.isc.product.soa.api.price.req.PriceAvailableSaleStatusReqDTO;
import com.jdi.isc.product.soa.api.price.res.PriceAvailableSaleStatusResDTO;
import com.jdi.isc.product.soa.api.pricewarn.dto.PriceWarnMqDTO;

import java.util.List;
import java.util.Set;

public interface IscCountryAgreementPriceRpcService {
    /**
     * 更新国家协议价格和京东价格。
     * @param dto 国家协议价格和京东价格请求体对象。
     * @return 更新操作的结果。
     */
    DataResponse<Boolean> updateAgreementAndJdPrice(CountryAgreementPriceReqDTO dto);

    /**
     * 更新商品的可售状态
     * @param input 商品可售状态请求DTO列表，包含需要更新的商品状态信息
     * @return 包含更新后的商品可售状态响应DTO的数据响应对象
     */
    PriceAvailableSaleStatusResDTO updateAvailableSaleStatus(List<PriceAvailableSaleStatusReqDTO> input);

    /**
     * 根据协议ID集合初始化国家协议预警价格
     * @param agreementIds 协议ID集合，用于查询对应协议的预警价格信息
     * @return 返回初始化后的预警价格字符串
     */
    String initCountryAgreementWarningPrice(Set<Long> agreementIds);

    /**
     * 初始化价格池状态
     * @param ids 需要初始化状态的价格池ID集合
     * @return 初始化操作结果字符串
     */
    String initPricePoolStatus(Set<Long> ids);


    /**
     * 根据价格预警信息生成告警内容
     * @param input 价格预警消息传输对象，包含触发预警的价格相关信息
     * @return 格式化后的价格预警提示字符串
     */
    String writePriceWarn(PriceWarnMqDTO input);
}
