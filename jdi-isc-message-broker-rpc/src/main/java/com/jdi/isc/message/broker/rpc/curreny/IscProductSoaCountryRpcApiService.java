package com.jdi.isc.message.broker.rpc.curreny;

import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.message.broker.rpc.common.AbstractRpcService;
import com.jdi.isc.product.soa.api.wimp.country.IscProductSoaCountryApiService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class IscProductSoaCountryRpcApiService extends AbstractRpcService {

    @Resource
    private IscProductSoaCountryApiService iscProductSoaCountryApiService;

    @ToolKit(exceptionWrap = true)
    public String getCurrencyNameByCurrencyCode(String countryCode) {
        return unWriteWrapper(() -> iscProductSoaCountryApiService.getCurrencyByCountryCode(countryCode), countryCode, "查询国家币种");
    }
}