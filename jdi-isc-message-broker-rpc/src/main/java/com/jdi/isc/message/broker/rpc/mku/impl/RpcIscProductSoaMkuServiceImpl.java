package com.jdi.isc.message.broker.rpc.mku.impl;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.message.broker.rpc.mku.RpcIscProductSoaMkuService;
import com.jdi.isc.product.soa.api.agreementPrice.IscCountryAgreementPriceWriteApiService;
import com.jdi.isc.product.soa.api.countryMku.IscCountryMkuWriteApiService;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuReqDTO;
import com.jdi.isc.product.soa.api.customerMku.IscProductSoaCustomerMkuWriteApiService;
import com.jdi.isc.product.soa.api.customerMku.req.CustomerMkuBatchReqDTO;
import com.jdi.isc.product.soa.api.orderVerify.IscOrderOutVerifyWriteApiService;
import com.jdi.isc.product.soa.api.orderVerify.req.MkuVerifyReqDTO;
import com.jdi.isc.product.soa.api.spu.IscProductSoaSpuWriteApiService;
import com.jdi.isc.product.soa.api.spu.req.SpuUpdateApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.MkuClientApiService;
import com.jdi.isc.product.soa.api.wisp.mku.biz.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * IscProductSoa
 */
@Service
@Slf4j
public class RpcIscProductSoaMkuServiceImpl implements RpcIscProductSoaMkuService {

    @Resource
    private MkuClientApiService mkuClientApiService;
    @Resource
    private IscOrderOutVerifyWriteApiService iscOrderOutVerifyWriteApiService;
    @Resource
    private IscProductSoaSpuWriteApiService iscProductSoaSpuWriteApiService;
    @Resource
    private IscCountryMkuWriteApiService iscCountryMkuWriteApiService;

    @Resource
    private IscProductSoaCustomerMkuWriteApiService iscProductSoaCustomerMkuWriteApiService;

    @Resource
    private IscCountryAgreementPriceWriteApiService iscCountryAgreementPriceWriteApiService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> upsertToEs(MkuClientDetailReqApiDTO input){
        log.info("RpcIscProductSoaMkuServiceImpl.upsertToEs req:{}" , JSON.toJSONString(input));
        return mkuClientApiService.upsertToEs(input);
    }

    /**
     * 更新订单的验证信息
     * @return 更新操作的结果，true 表示成功，false 表示失败
     */
    @Override
    public DataResponse<Boolean> createOrderOutVerifyInfo(List<MkuVerifyReqDTO> list) {
        DataResponse<Boolean> response = iscOrderOutVerifyWriteApiService.saveOrderOutVerifyInfo(list);
        log.info("RpcIscProductSoaMkuServiceImpl.createOrderOutVerifyInfo req:{}" , JSON.toJSONString(response));
        return response;
    }

    @Override
    public DataResponse<Boolean> updateSpuSkuMku(SpuUpdateApiDTO dto) {
        DataResponse<Boolean> response = iscProductSoaSpuWriteApiService.updateSpuSkuMku(dto);
        log.info("RpcIscProductSoaMkuServiceImpl.updateSpuSkuMku req:{},res:{}" , JSON.toJSONString(dto),response.getSuccess());
        return response;
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> mkuJoinCountryPool(CountryMkuReqDTO dto) {
        DataResponse<Boolean> response = iscCountryMkuWriteApiService.mkuMsgJoinCountryPool(dto);
        log.info("RpcIscProductSoaMkuServiceImpl.mkuJoinCountryPool req:{},res:{}" , JSON.toJSONString(dto),response.getSuccess());
        return response;
    }

    @Override
    public DataResponse<Boolean> jdSkuIdJoinCountryPool(CountryMkuReqDTO dto) {
        DataResponse<Boolean> response = iscCountryMkuWriteApiService.jdSkuIdMsgJoinCountryPool(dto);
        log.info("RpcIscProductSoaMkuServiceImpl.jdSkuIdJoinCountryPool req:{},res:{}" , JSON.toJSONString(dto),response.getSuccess());
        return response;
    }

    @Override
    public DataResponse<Boolean> bindCustomerToMku(CustomerMkuBatchReqDTO input) {
        DataResponse<Boolean> response = iscProductSoaCustomerMkuWriteApiService.bindCustomerToMku(input);
        log.info("RpcIscProductSoaMkuServiceImpl.bindCustomerToMku req:{},res:{}" , JSON.toJSONString(input),JSON.toJSONString(response));
        return response;
    }

    @Override
    public DataResponse<Boolean> updateMkuFeatureTag(MkuFeatureTagDTO input) {
        return mkuClientApiService.updateMkuCustomerFeature(input);
    }

    @Override
    public DataResponse<Boolean> updateMkuPoolFlag(MkuPoolFlagDTO input) {
        log.info("RpcIscProductSoaMkuServiceImpl.updateMkuPoolFlag req:{}" , JSON.toJSONString(input));
        return mkuClientApiService.updateMkuPoolFlag(input);
    }

    @Override
    public DataResponse<MkuEsClientDTO> queryEsMkuByCondition(MkuEsDetailReqApiDTO input) {
        return mkuClientApiService.queryEsMkuByCondition(input);
    }

    @Override
    public DataResponse<Boolean> updateMkuEsStockTag(MkuEsStockTagReqDTO mkuEsStockTagReqDTO) {
        return mkuClientApiService.updateMkuEsStockTag(mkuEsStockTagReqDTO);
    }

    @Override
    public DataResponse<Boolean> updateMkuEsAggKey(MkuClientDetailReqApiDTO input) {
        log.info("RpcIscProductSoaMkuServiceImpl.updateMkuEsAggKey req:{}" , JSON.toJSONString(input));
        return mkuClientApiService.updateAggKey(input);
    }
    @Override
    public DataResponse<Boolean> updateMkuEsPromiseTimeTag(MkuPromiseTagDTO mkuPromiseTagDTO) {
        return mkuClientApiService.updateMkuPromiseFeature(mkuPromiseTagDTO);
    }

    @Override
    public void initCountryAgreementWarningPrice(List<MkuPoolFlagDTO> targets) {
        iscCountryAgreementPriceWriteApiService.refreshCountryAgreementWarningPrice(targets);
    }


}
