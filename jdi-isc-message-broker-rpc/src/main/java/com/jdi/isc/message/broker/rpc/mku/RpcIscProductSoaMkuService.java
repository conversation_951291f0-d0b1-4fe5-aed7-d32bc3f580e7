package com.jdi.isc.message.broker.rpc.mku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuReqDTO;
import com.jdi.isc.product.soa.api.customerMku.req.CustomerMkuBatchReqDTO;
import com.jdi.isc.product.soa.api.orderVerify.req.MkuVerifyReqDTO;
import com.jdi.isc.product.soa.api.spu.req.SpuUpdateApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientDetailReqApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuFeatureTagDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuPoolFlagDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.*;

import java.util.List;

/**
 * IscProductSoa
 */
public interface RpcIscProductSoaMkuService {


    /**
     * 上下品
     * @param input 列表参数f
     * @return 商品列表、包含全部信息es中全部信息
     */
    DataResponse<Boolean> upsertToEs(MkuClientDetailReqApiDTO input);

    DataResponse<Boolean> createOrderOutVerifyInfo(List<MkuVerifyReqDTO> list);

    DataResponse<Boolean> updateSpuSkuMku(SpuUpdateApiDTO dto);

    DataResponse<Boolean> mkuJoinCountryPool(CountryMkuReqDTO dto);

    DataResponse<Boolean> jdSkuIdJoinCountryPool(CountryMkuReqDTO dto);


    DataResponse<Boolean> bindCustomerToMku(CustomerMkuBatchReqDTO input);

    DataResponse<Boolean> updateMkuFeatureTag(MkuFeatureTagDTO input);

    DataResponse<Boolean> updateMkuPoolFlag(MkuPoolFlagDTO input);

    DataResponse<MkuEsClientDTO> queryEsMkuByCondition(MkuEsDetailReqApiDTO input);

    DataResponse<Boolean> updateMkuEsStockTag(MkuEsStockTagReqDTO mkuEsStockTagReqDTO);

    DataResponse<Boolean> updateMkuEsAggKey(MkuClientDetailReqApiDTO input);

    DataResponse<Boolean> updateMkuEsPromiseTimeTag(MkuPromiseTagDTO mkuPromiseTagDTO);

    void initCountryAgreementWarningPrice(List<MkuPoolFlagDTO> targets);
}
