package com.jdi.isc.message.broker.rpc.mku;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.countryMku.IscCountryMkuReadApiService;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuCheckReqDTO;
import com.jdi.isc.product.soa.api.mku.IscProductSoaMkuReadApiService;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuReqDTO;
import com.jdi.isc.product.soa.api.mku.req.SpecialAttrMkuReqDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuResDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientInPoolApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * isc MKU商品读服务
 * <AUTHOR>
 * @date 2024/12/9
 */
@Slf4j
@Service
public class IscProductSoaMkuReadRpcService {

    @Resource
    private IscProductSoaMkuReadApiService iscProductSoaMkuReadApiService;
    @Resource
    private IscCountryMkuReadApiService iscCountryMkuReadApiService;

    /**
     * 国际MKU批量查询
     */
    public Map<Long, IscMkuResDTO> getIscMku(BatchQueryMkuReqDTO req){
        DataResponse<Map<Long, IscMkuResDTO>> res = null;
        try {
            res = iscProductSoaMkuReadApiService.getIscMku(req);
            if(res!=null && res.getSuccess()){
                return res.getData();
            }
        }finally {
            log.info("IscProductSoaMkuReadRpcService.getIscMku req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(res));
        }
        return null;
    }


    /**
     * 根据jdSku查询MkuId
     */
    public Map<Long, Set<Long>> getIscMkuIdByJdSkuId(BatchQueryMkuReqDTO req){
        DataResponse<Map<Long, Set<Long>>> res = null;
        try {
            res = iscProductSoaMkuReadApiService.getIscMkuIdByJdSkuId(req);
            if(res!=null && res.getSuccess()){
                return res.getData();
            }
        }finally {
            log.info("IscProductSoaMkuReadRpcService.getIscMkuIdByJdSkuId req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(res));
        }
        return null;
    }

   /**
    * 根据MkuIds查询特殊属性映射。
    * @param reqDTO SpecialAttrMkuReqDTO对象，包含MkuIds列表。
    * @return Map<Long, Map<String, String>>特殊属性映射，key为MkuId，value为特殊属性名和值的映射。
    */
   public Map<Long, Map<String, String>> querySpecialAttrMapByMkuIds(SpecialAttrMkuReqDTO reqDTO){
        DataResponse<Map<Long, Map<String, String>>> res = null;
        try {
            res = iscProductSoaMkuReadApiService.querySpecialAttrMapByMkuIds(reqDTO);
            if(res!=null && res.getSuccess()){
                return res.getData();
            }
        }finally {
            log.info("IscProductSoaMkuReadRpcService.querySpecialAttrMapByMkuIds req:{} , res:{}" , JSON.toJSONString(reqDTO), JSON.toJSONString(res));
        }
        return null;
    }

    /**
     * 根据国家信息检查 MKU 是否在池中。
     * @param reqDTO 国家信息请求对象
     * @return 如果 MKU 在池中，则返回包含 MKU 客户端信息的列表；否则返回 null。
     */
    public List<MkuClientInPoolApiDTO> getIscMkuCountryPool(CountryMkuCheckReqDTO reqDTO){
        DataResponse<List<MkuClientInPoolApiDTO>> res = null;
        try {
            res = iscCountryMkuReadApiService.checkInPool(reqDTO);
            if (res != null && res.getSuccess()) {
                return res.getData();
            }
        } finally {
            log.info("IscProductSoaMkuReadRpcService.getIscMkuCountryPool req:{} , res:{}", JSON.toJSONString(reqDTO), JSON.toJSONString(res));
        }
        return null;
    }

    /**
     * 根据商品SKUID批量获取对应的ISC MKUID。
     * @param reqDTO 包含需要查询的商品SKUID的请求对象。
     * @return 一个Map对象，其中key为商品SKUID，value为对应的ISC MKUID。
     */
    public Map<Long, Long> getIscMkuIdBySkuId(BatchQueryMkuReqDTO reqDTO) {
        DataResponse<Map<Long,Long>> res = null;
        try {
            res = iscProductSoaMkuReadApiService.getIscMkuIdBySkuId(reqDTO);
            if (res != null && res.getSuccess()) {
                return res.getData();
            }
        } finally {
            log.info("IscProductSoaMkuReadRpcService.getIscMkuIdBySkuId req:{} , res:{}", JSON.toJSONString(reqDTO), JSON.toJSONString(res));
        }
        return Collections.emptyMap();
    }

    /**
     * 根据mkuId查询SkuId
     */
    public Map<Long, Long> getIscSkuIdByMkuId(BatchQueryMkuReqDTO req){
        DataResponse<Map<Long, Long>> res = null;
        try {
            res = iscProductSoaMkuReadApiService.getIscSkuIdByMkuId(req);
            if(res!=null && res.getSuccess()){
                return res.getData();
            }
        }finally {
            log.info("IscProductSoaMkuReadRpcService.getIscSkuIdByMkuId req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(res));
        }
        return Collections.emptyMap();
    }

}
