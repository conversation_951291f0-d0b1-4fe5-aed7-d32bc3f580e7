package com.jdi.isc.message.broker.rpc.price.impl;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.message.broker.rpc.common.AbstractRpcService;
import com.jdi.isc.message.broker.rpc.price.IscCountryAgreementPriceRpcService;
import com.jdi.isc.product.soa.api.agreementPrice.IscCountryAgreementPriceWriteApiService;
import com.jdi.isc.product.soa.api.agreementPrice.req.CountryAgreementPriceReqDTO;
import com.jdi.isc.product.soa.api.price.req.PriceAvailableSaleStatusReqDTO;
import com.jdi.isc.product.soa.api.price.res.PriceAvailableSaleStatusResDTO;
import com.jdi.isc.product.soa.api.pricewarn.dto.PriceWarnMqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@Service
@Slf4j
public class IscCountryAgreementPriceRpcServiceImpl extends AbstractRpcService implements IscCountryAgreementPriceRpcService {

    @Resource
    private IscCountryAgreementPriceWriteApiService iscCountryAgreementPriceWriteApiService;

    @Override
    public DataResponse<Boolean> updateAgreementAndJdPrice(CountryAgreementPriceReqDTO dto) {
        log.info("IscCountryAgreementPriceRpcServiceImpl.updateAgreementAndJdPrice req:{}", JSON.toJSONString(dto));
        DataResponse<Boolean> response = null;
        try {
            return response = iscCountryAgreementPriceWriteApiService.updateAgreementAndJdPrice(dto);
        }catch (Exception e){
            log.error("IscCountryAgreementPriceRpcServiceImpl.updateAgreementAndJdPrice req:{},res:{}", JSON.toJSONString(dto), JSON.toJSONString(e));
        }finally {
            log.info("IscCountryAgreementPriceRpcServiceImpl.updateAgreementAndJdPrice req:{},res:{}", JSON.toJSONString(dto), JSON.toJSONString(response));
        }
        return null;
    }

    @Override
    public PriceAvailableSaleStatusResDTO updateAvailableSaleStatus(List<PriceAvailableSaleStatusReqDTO> input) {
        return unWriteWrapper(() -> iscCountryAgreementPriceWriteApiService.updateAvailableSaleStatus(input), input, "更新协议价可售状态");
    }

    @Override
    public String initCountryAgreementWarningPrice(Set<Long> agreementIds) {
        return unWriteWrapper(() -> iscCountryAgreementPriceWriteApiService.initCountryAgreementWarningPrice(agreementIds), agreementIds, "初始化国家协议预警价格");
    }

    @Override
    public String initPricePoolStatus(Set<Long> ids) {
        return unWriteWrapper(() -> iscCountryAgreementPriceWriteApiService.initPricePoolStatus(ids), ids, "更新价格客户池和国家池状态");
    }

    @Override
    public String writePriceWarn(PriceWarnMqDTO input) {
        return unWriteWrapper(() -> iscCountryAgreementPriceWriteApiService.writePriceWarn(input), input, "更新价格预警");
    }
}
