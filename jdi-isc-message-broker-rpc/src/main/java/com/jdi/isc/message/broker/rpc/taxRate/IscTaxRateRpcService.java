package com.jdi.isc.message.broker.rpc.taxRate;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.taxRate.IscTaxRateWriteApiService;
import com.jdi.isc.product.soa.api.taxRate.req.BrImportTaxReqDTO;
import com.jdi.isc.product.soa.api.taxRate.req.TaxRateReqDTO;
import com.jdi.isc.product.soa.api.taxRate.res.TaxRateApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class IscTaxRateRpcService {
    @Resource
    private IscTaxRateWriteApiService iscTaxRateWriteApiService;

    public DataResponse<Boolean> updateBrIpiTax(BrImportTaxReqDTO input) {
        log.info("IscTaxRateRpcService.updateBrIpiTax input:{}", JSONObject.toJSONString(input));
        DataResponse<Boolean> response = iscTaxRateWriteApiService.updateBrIpiTax(input);
        if (response.getSuccess()) {
            return DataResponse.success();
        } else {
            return DataResponse.error(response.getMessage());
        }
    }
}
