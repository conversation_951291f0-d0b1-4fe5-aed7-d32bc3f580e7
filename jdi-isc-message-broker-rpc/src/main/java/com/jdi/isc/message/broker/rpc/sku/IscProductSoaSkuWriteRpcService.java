package com.jdi.isc.message.broker.rpc.sku;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.message.broker.common.exception.ProductBizException;
import com.jdi.isc.product.soa.api.sku.IscProductSoaSkuWriteApiService;
import com.jdi.isc.product.soa.api.sku.req.SkuSyncNcmDTO;
import com.jdi.isc.product.soa.api.sku.req.UpdateSkuStatusBySupplierCodeReqDTO;
import com.jdi.isc.product.soa.api.sku.req.UpdateSkuStatusReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class IscProductSoaSkuWriteRpcService {

    @Resource
    private IscProductSoaSkuWriteApiService iscProductSoaSkuWriteApiService;

    public void saveProductGlobalNcmAttribute(List<SkuSyncNcmDTO> input) {
        log.info("同步修改sku的ncm属性. target= {}", input);
        if (CollectionUtils.isEmpty(input)) {
            return;
        }

        try {
            for (SkuSyncNcmDTO item : input) {

//                Long jdSkuId = item.getJdSkuId();

//                // TODO 测试使用, 上线注释掉
//                if (!Lists.newArrayList(100142998590L, 100039261291L, 100166221881L, 100039261289L, 100103154895L,
//                        862302L,
//                        10089022986674L,
//                        100052395943L
//                ).contains(jdSkuId)) {
//                    continue;
//                }

                DataResponse<Void> response = iscProductSoaSkuWriteApiService.saveProductGlobalNcmAttribute(item);
                log.info("同步修改sku的ncm属性. item={}, response={}", JSON.toJSONString(input), JSON.toJSONString(response));

                if (!response.getSuccess()) {
                    log.info("同步修改sku的ncm属性. 失败. item={}", JSON.toJSONString(item));
                    throw new RuntimeException("同步修改sku的ncm属性异常");
                }
            }
        } catch (Exception e) {
            log.error("同步修改sku的ncm属性, 出现异常. target= {}", input, e);
            throw new RuntimeException("同步修改sku的ncm属性异常");
        }

    }

    /**
     * Batch fix br ncm code.
     *
     * @param jdSkuId the jd sku id
     * @param updater the updater
     */
    public void batchFixBrNcmCode(Long jdSkuId, String updater){

        if (jdSkuId == null) {
            log.warn("无jdSkuId, 无需更新Sku的跨境属性ncmCode, jdSkuIds is null");
            return;
        }
        DataResponse<Integer> response = null;
        try {
            response = iscProductSoaSkuWriteApiService.batchFixBrNcmCode(Lists.newArrayList(jdSkuId), true, updater);

            if (response == null) {
                log.error("更新Sku的跨境属性ncmCode失败, response is null, jdSkuId={}", jdSkuId);
                return;
            }

            if (!response.getSuccess()) {
                throw new ProductBizException(response.getMessage());
            }

        } catch (Exception e) {
            log.error("更新Sku的跨境属性ncmCode失败, jdSkuId={}", jdSkuId, e);

            if (e instanceof ProductBizException) {
                throw e;
            }

            throw new ProductBizException("更新Sku的跨境属性ncmCode失败");
        } finally {
            log.info("更新Sku的跨境属性ncmCode, jdSkuId={}, response={}", jdSkuId, response);
        }

    }

    /**
     * 更新Sku的上下架状态。
     * @param statusReqDTO 更新Sku状态的请求对象。
     * @return 更新操作是否成功。
     */
    public boolean skuUpOrDown(UpdateSkuStatusReqDTO statusReqDTO){
        boolean result = false;
        DataResponse<Boolean> response = null;
        try {
            response = iscProductSoaSkuWriteApiService.skuUpOrDown(statusReqDTO);
            if (Objects.nonNull(response) && response.getSuccess()) {
                result =  response.getSuccess();
            }
        } catch (Exception e) {
            log.error("更新Sku的上下架状态异常, statusReqDTO={}", statusReqDTO, e);
        }finally {
            log.info("更新Sku的上下架状态 req:{},res:{}",JSON.toJSONString(statusReqDTO),JSON.toJSONString(response));
        }
        return result;
    }



    /**
     * 根据供应商简码更新Sku的上下架状态
     * @param supplierCodeReqDTO 更新Sku状态的请求参数
     * @return 更新操作是否成功
     */
    public boolean skuUpOrDownBySupplierCode(UpdateSkuStatusBySupplierCodeReqDTO supplierCodeReqDTO){
        boolean result = false;
        DataResponse<Boolean> response = null;
        try {
            response = iscProductSoaSkuWriteApiService.skuUpOrDownBySupplierCode(supplierCodeReqDTO);
            if (Objects.nonNull(response) && response.getSuccess()) {
                result =  response.getSuccess();
            }
        } catch (Exception e) {
            log.error("根据供应商简码更新Sku的上下架状态异常, statusReqDTO={}", supplierCodeReqDTO, e);
        }finally {
            log.info("根据供应商简码更新Sku的上下架状态 req:{},res:{}",JSON.toJSONString(supplierCodeReqDTO),JSON.toJSONString(response));
        }
        return result;
    }
}
