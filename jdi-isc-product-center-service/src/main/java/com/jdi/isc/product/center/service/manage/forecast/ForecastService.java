package com.jdi.isc.product.center.service.manage.forecast;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.forecast.biz.ForecastOrderApiDTO;
import com.jdi.isc.order.center.api.forecast.biz.req.ForecastDetailOrderApiReq;
import com.jdi.isc.order.center.api.forecast.biz.req.ForecastOrderApiReq;
import com.jdi.isc.order.center.api.forecast.biz.req.IssuedForecastOrderApiReq;
import com.jdi.isc.order.center.api.forecast.biz.req.UpdateForecastOrderStatusApiReq;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderApiResp;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderDetailResp;
import com.jdi.isc.task.center.api.forecast.impl.ForecastPurchaseOrderPrintApiDTO;

public interface ForecastService {

    PageInfo<ForecastOrderApiResp> pageForecastOrder(ForecastOrderApiReq forecastOrderApiDTO);


    Boolean submitForecastOrder(ForecastOrderApiDTO api);

    ForecastOrderDetailResp detailForecastOrder(ForecastDetailOrderApiReq apiDTO);


    Boolean updateForecastOrderStatus(UpdateForecastOrderStatusApiReq apiDTO);

    Boolean issuedForecastOrder(IssuedForecastOrderApiReq issuedForecastOrderApiReq);

    String printEntryStock(ForecastPurchaseOrderPrintApiDTO apiDTO);
}
