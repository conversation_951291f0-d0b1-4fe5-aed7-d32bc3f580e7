package com.jdi.isc.product.center.service.manage.customs.impl;

import com.alibaba.fastjson.JSONObject;
import com.jd.common.web.LoginContext;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.customs.dto.*;
import com.jdi.isc.product.center.domain.customs.req.CustomsPageReqDTO;
import com.jdi.isc.product.center.domain.customs.resp.CustomsInfoRespDTO;
import com.jdi.isc.product.center.service.adapter.mapstruct.customs.CustomsConvert;
import com.jdi.isc.product.center.service.manage.customs.CustomsManageService;
import com.jdi.isc.product.soa.api.customs.IscProductCustomsDraftWriteApiService;
import com.jdi.isc.product.soa.api.customs.IscProductCustomsReadApiService;
import com.jdi.isc.product.soa.api.customs.req.*;
import com.jdi.isc.product.soa.api.customs.req.ProductCustomsPageApiDTO;
import com.jdi.isc.product.soa.api.customs.supplier.CustomsSupplierApiService;
import com.jdi.isc.product.soa.api.customs.supplier.biz.CustomsSupplierApiDTO;
import com.jdi.isc.product.soa.api.customs.supplier.req.CustomsSupplierPageReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 关务管理Service实现类
 * <AUTHOR>
 * @date 2024/07/18 10:25
 */
@Slf4j
@Service
public class CustomsManageServiceImpl implements CustomsManageService {

    @Resource
    private IscProductCustomsReadApiService iscProductCustomsReadApiService;

    @Resource
    private IscProductCustomsDraftWriteApiService iscProductCustomsDraftWriteApiService;

    @Resource
    private CustomsSupplierApiService customsSupplierApiService;

    @Override
    public DataResponse<PageInfo<CustomsInfoRespDTO>> page(CustomsPageReqDTO reqDTO) {
        try {
            log.info("查询关务列表，请求参数：{}", JSONObject.toJSONString(reqDTO));
            
            // 转换请求参数
            ProductCustomsPageApiDTO jsfReq = CustomsConvert.INSTANCE.reqDto2JsfReq(reqDTO);
            
            // 调用JSF接口
            DataResponse<PageInfo<ProductCustomsApiDTO>> jsfResp = iscProductCustomsReadApiService.page(jsfReq);
            log.info("查询关务列表，JSF响应：{}", JSONObject.toJSONString(jsfResp));
            
            // 检查JSF响应是否成功
            if (jsfResp == null || !jsfResp.getSuccess()) {
                log.error("查询关务列表失败，JSF响应错误：{}", jsfResp != null ? jsfResp.getMessage() : "响应为空");
                return DataResponse.error(jsfResp != null ? jsfResp.getMessage() : "查询关务列表失败");
            }

            // 转换响应数据
            PageInfo<ProductCustomsApiDTO> jsfPageInfo = jsfResp.getData();
            if (jsfPageInfo == null || CollectionUtils.isEmpty(jsfPageInfo.getRecords())) {
                return DataResponse.success(new PageInfo<>());
            }
            // 使用 MapStruct 转换数据
            PageInfo<CustomsInfoRespDTO> pageInfo = new PageInfo<>();
            pageInfo.setTotal(jsfPageInfo.getTotal());
            pageInfo.setRecords(CustomsConvert.INSTANCE.jsfRespList2RespDtoList(jsfPageInfo.getRecords()));
            return DataResponse.success(pageInfo);
        } catch (Exception e) {
            log.error("查询关务列表异常", e);
            return DataResponse.error("查询关务列表异常：" + e.getMessage());
        }
    }

    @Override
    public DataResponse<CustomsInfoRespDTO> productCustomsDetail(Long id) {
        try {
            log.info("查询关务详情，请求参数：{}", id);
            
            // 构建JSF请求参数
            ProductCustomsReqApiDTO reqDTO = new ProductCustomsReqApiDTO();
            reqDTO.setId(id);
            reqDTO.setLang(LoginContext.getLoginContext().getLang());
            reqDTO.setScope("all");
            // 调用JSF接口
            DataResponse<ProductCustomsApiDTO> jsfResp = iscProductCustomsReadApiService.productCustomsDetail(reqDTO);
            log.info("查询关务详情，JSF响应：{}", JSONObject.toJSONString(jsfResp));
            
            // 检查JSF响应是否成功
            if (jsfResp == null || !jsfResp.getSuccess()) {
                log.error("查询关务详情失败，JSF响应错误：{}", jsfResp != null ? jsfResp.getMessage() : "响应为空");
                return DataResponse.error(jsfResp != null ? jsfResp.getMessage() : "查询关务详情失败");
            }

            // 转换响应数据
            ProductCustomsApiDTO jsfData = jsfResp.getData();
            if (jsfData == null) {
                return DataResponse.error("关务详情数据为空");
            }

            CustomsInfoRespDTO respDTO = CustomsConvert.INSTANCE.jsfResp2RespDetailDto(jsfData);
            return DataResponse.success(respDTO);
        } catch (Exception e) {
            log.error("查询关务详情异常", e);
            return DataResponse.error("查询关务详情异常：" + e.getMessage());
        }
    }

    @Override
    public DataResponse<Boolean> assignedSupplier(ProductCustomsAssignedSupplierDTO dto) {
        try {
            AssignSupplierReqApiDTO apiDTO = CustomsConvert.INSTANCE.dto2apiDto(dto);
            return iscProductCustomsDraftWriteApiService.assignedSupplier(apiDTO);
        } catch (Exception e) {
            log.error("更新关务草稿 assignedSupplier 异常", e);
            return DataResponse.error("更新关务草稿 assignedSupplier 异常：" + e.getMessage());
        }
    }

    @Override
    public DataResponse<Boolean> assessment(ProductCustomsAssessmentDTO dto) {
        try {
            ProductCustomsDraftApiDTO apiDTO = CustomsConvert.INSTANCE.dto2assessmentApiDto(dto);
            return iscProductCustomsDraftWriteApiService.saveOrUpdate(apiDTO);
        } catch (Exception e) {
            log.error("关务评估接口", e);
            return DataResponse.error("关务评估接口：" + e.getMessage());
        }
    }

    @Override
    public DataResponse<Boolean> compulsoryCertificate(ProductCustomsCompulsoryCertificateDTO dto) {
        try {
            ProductCustomsDraftApiDTO apiDTO = CustomsConvert.INSTANCE.dto2compulsoryCertificateApiDto(dto);
            return iscProductCustomsDraftWriteApiService.saveOrUpdate(apiDTO);
        } catch (Exception e) {
            log.error("强制性认证接口", e);
            return DataResponse.error("强制性认证接口：" + e.getMessage());
        }
    }

    @Override
    public DataResponse<Boolean> confirm(ProductCustomsConfirmDTO dto) {
        try {
            UpdateStatusProductCustomsApiDTO apiDTO = CustomsConvert.INSTANCE.dto2ConfirmapiDto(dto);
            DataResponse<Boolean> response = iscProductCustomsDraftWriteApiService.updateStatus(apiDTO);
            if(response.getCode().equals("6006")){
                response.setMessage("商品ID:[%s]关务评估或强制认证状态未提交。");
            }
            return response;
        } catch (Exception e) {
            log.error("批量确认评估信息接口", e);
            return DataResponse.error("批量确认评估信息接口：" + e.getMessage());
        }
    }

    @Override
    public DataResponse<Boolean> delete(ProductCustomsConfirmDTO dto) {
        try {
            UpdateStatusProductCustomsApiDTO apiDTO = CustomsConvert.INSTANCE.dto2ConfirmapiDto(dto);
            return iscProductCustomsDraftWriteApiService.updateStatus(apiDTO);
        } catch (Exception e) {
            log.error("批量删除评估信息接口", e);
            return DataResponse.error("批量删除评估信息接口：" + e.getMessage());
        }
    }

    @Override
    public DataResponse<PageInfo<CustomsSupplierApiDTO>> customsSupplierList(CustomsSupplierPageReqApiDTO dto) {
        return customsSupplierApiService.pageSearch(dto);
    }
} 