package com.jdi.isc.product.center.service.order.comment;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentReadApiDTO;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentDetailReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentPageReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentSaveReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentCountReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentDeleteReq;

/**
 * 订单留言板管理服务接口
 *
 * @author: zhangjin176
 * @date: 2025/5/23 11:35
 * @version:
 **/
public interface OrderCommentManageService {

    /**
     * 保存更新订单留言
     *
     * @param req 订单留言保存更新请求对象
     * @return 订单留言响应对象
     */
    DataResponse<Boolean> save(OrderCommentSaveReq req);


    /**
     * 根据条件分页查询订单留言信息
     *
     * @param req 分页查询请求对象，包含分页信息和查询条件
     * @return 包含订单留言信息的分页结果对象
     */
    PageInfo<OrderCommentReadApiDTO> page(OrderCommentPageReq req);

    /**
     * 查询订单留言数量
     *
     * @param req 订单留言数量查询请求对象
     * @return 订单留言数量
     */
    Long count(OrderCommentCountReq req);

}
