package com.jdi.isc.product.center.service.manage.hscode;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeDetailVO;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;

/**
 * 6个国家的HsCode管理服务接口
 * 支持国家：BR(巴西)、HU(匈牙利)、ID(印度尼西亚)、MY(马来西亚)、TH(泰国)、VN(越南)
 * <AUTHOR>
 * @date 2024/02/21
 */
public interface CountryHsCodeManageService {

    /**
     * 保存或更新6个国家的HsCode信息
     * @param input 表单参数
     * @return 响应结果
     */
    DataResponse<CountryHsCodeVO> saveOrUpdate(CountryHsCodeVO input);

    /**
     * 详情查询
     * @param input 查询条件
     * @return HsCode详情
     */
    DataResponse<CountryHsCodeVO> detail(CountryHsCodeDetailVO input);

    /**
     * 分页查询6个国家的HsCode信息
     * @param input 查询参数
     * @return 分页结果
     */
    DataResponse<PageInfo<CountryHsCodePageResponse>> pageSearch(CountryHsCodePageRequest input);
} 