package com.jdi.isc.product.center.service.manage.hscode.strategy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.ArrayList;

/**
 * 国家HsCode策略工厂类
 * 负责管理和获取不同国家的HsCode策略
 * <AUTHOR>
 * @date 2024/02/21
 */
@Slf4j
@Component
public class CountryHsCodeStrategyFactory {

    @Autowired
    private List<CountryHsCodeStrategy> strategies;

    private final Map<String, CountryHsCodeStrategy> strategyMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        for (CountryHsCodeStrategy strategy : strategies) {
            String countryCode = strategy.getCountryCode();
            strategyMap.put(countryCode, strategy);
            log.info("注册国家HsCode策略: {}", countryCode);
        }
        log.info("国家HsCode策略工厂初始化完成，共注册{}个策略", strategyMap.size());
    }

    /**
     * 根据国家代码获取对应的策略
     * @param countryCode 国家代码
     * @return 对应的策略实现
     */
    public CountryHsCodeStrategy getStrategy(String countryCode) {
        if (countryCode == null || countryCode.trim().isEmpty()) {
            throw new IllegalArgumentException("国家代码不能为空");
        }
        
        CountryHsCodeStrategy strategy = strategyMap.get(countryCode.toUpperCase());
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的国家代码: " + countryCode);
        }
        
        return strategy;
    }

    /**
     * 检查是否支持指定的国家代码
     * @param countryCode 国家代码
     * @return 是否支持
     */
    public boolean isSupported(String countryCode) {
        if (countryCode == null || countryCode.trim().isEmpty()) {
            return false;
        }
        return strategyMap.containsKey(countryCode.toUpperCase());
    }

    /**
     * 获取所有支持的国家代码
     * @return 支持的国家代码列表
     */
    public List<String> getSupportedCountryCodes() {
        return new ArrayList<>(strategyMap.keySet());
    }
} 