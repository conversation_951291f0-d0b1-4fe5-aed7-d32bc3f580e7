package com.jdi.isc.product.center.service.manage.order.impl;

import com.jd.common.web.LoginContext;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.library.i18n.exception.JsfException;
import com.jdi.isc.order.center.api.customs.ProductCustomsApiService;
import com.jdi.isc.order.center.api.customs.biz.req.ProductCustomsPageReqDTO;
import com.jdi.isc.order.center.api.customs.biz.req.ProductCustomsReqDTO;
import com.jdi.isc.order.center.api.customs.biz.req.ProductCustomsUpdateDTO;
import com.jdi.isc.order.center.api.customs.biz.resp.ProductCustomsDTO;
import com.jdi.isc.product.center.domain.customs.ProductCustomsVO;
import com.jdi.isc.product.center.service.adapter.mapstruct.customs.CustomsConvert;
import com.jdi.isc.product.center.service.manage.order.CustomsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

@Slf4j
@Service
public class CustomsServiceImpl implements CustomsService {

    /**
     * 依赖注入的产品报关API服务，用于与下游服务进行交互。
     */
    @Resource
    private ProductCustomsApiService productCustomsApiService;

    /**
     * 分页获取报关列表
     * @param productCustomsPageReqDTO 报关分页请求参数
     * @return 报关分页信息
     */
    @Override
    public PageInfo<ProductCustomsDTO> page(ProductCustomsPageReqDTO productCustomsPageReqDTO) {
        productCustomsPageReqDTO.setOperator(LoginContext.getLoginContext().getPin());
        DataResponse<PageInfo<ProductCustomsDTO>> pageJsf = productCustomsApiService.page(productCustomsPageReqDTO);
        if (pageJsf.getSuccess()) {
            return pageJsf.getData();
        } else {
            log.error("JSF失败 - 获取报关列表异常: " + pageJsf.getMessage());
            throw new JsfException(pageJsf.getCode(), pageJsf.getMessage());
        }
    }
    /**
     * 修改报关信息。
     * @param vo 包含要更新的报关信息的 ProductCustomsVO 对象。
     * @return 更新操作是否成功。
     */
    @Override
    public Boolean edit(ProductCustomsVO vo) {
        ProductCustomsUpdateDTO updateDTO = new ProductCustomsUpdateDTO();
        ProductCustomsReqDTO req=CustomsConvert.INSTANCE.vo2req(vo);
        updateDTO.setUpdater(vo.getUpdater());
        updateDTO.setUpdateTime(System.currentTimeMillis());
        updateDTO.setProductCustomsDTOList(Arrays.asList(req));
        DataResponse<Boolean> updateJsf = productCustomsApiService.update(updateDTO);
        if (updateJsf.getSuccess()) {
            return updateJsf.getData();
        } else {
            log.error("JSF失败 - 修改报关信息异常: " + updateJsf.getMessage());
            throw new JsfException(updateJsf.getCode(), updateJsf.getMessage());
        }
    }
}
