package com.jdi.isc.product.center.service.worker.task.handler;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jdi.isc.product.center.common.utils.BigDecimalUtil;
import com.jdi.isc.product.center.domain.task.dto.TaskDTO;
import com.jdi.isc.product.center.domain.task.excel.CustomerSkuPriceProjectExtExcelDTO;
import com.jdi.isc.product.center.rpc.customerSku.CustomerSkuPriceRpcService;
import com.jdi.isc.product.center.rpc.price.productprice.IscProductSoaProjectPriceWriteRpcService;
import com.jdi.isc.product.center.rpc.utils.ExceptionUtil;
import com.jdi.isc.product.center.service.worker.task.frame.JobExecutor;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceApplyImportApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceUpdateImportStatusApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.res.ProjectPriceDownloadApiDTO;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 巴西价格申请单下载.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@JobExecutor(taskBizType = TaskBizTypeEnum.PROJECT_CUSTOMER_PRICE_DOWNLOAD)
public class DisposableCustomerPriceProjectExtDownloadHandler extends DisposableSkuCustomerPriceImportHandler<CustomerSkuPriceProjectExtExcelDTO> {

    @Resource
    private CustomerSkuPriceRpcService customerSkuPriceRpcService;

    @Resource
    private IscProductSoaProjectPriceWriteRpcService iscProductSoaProjectPriceWriteRpcService;

    @Override
    public void run(TaskDTO<CustomerSkuPriceProjectExtExcelDTO> task) {
        // 补充数据
        List<CustomerSkuPriceProjectExtExcelDTO> target = task.getTarget();

        if (CollectionUtils.isEmpty(target)) {
            return;
        }

        // 校验客户
        String reqJson = task.getTask().getReqParam();

        // 请求参数
        ProjectPriceApplyImportApiDTO param = JSONObject.parseObject(reqJson, ProjectPriceApplyImportApiDTO.class);
        String clientCode = param.getClientCode();
        String projectCode = param.getProjectCode();


        for (CustomerSkuPriceProjectExtExcelDTO item : target) {
            if (!item.getValid()) {
                continue;
            }
            if (StringUtils.isEmpty(clientCode) || StringUtils.isEmpty(projectCode)) {
                log.warn("参数错误. param={}, 客户编码和项目编码不能为空", reqJson);
                item.setValid(false);
                item.setResult("参数错误, 客户编码和项目编码不能为空");
                continue;
            }

            if (!StringUtils.equals(item.getClientCode(), clientCode)) {
                log.warn("客户编号与项目单不一致. param={}, item={}", reqJson, JSONObject.toJSONString(item));
                item.setValid(false);
                item.setResult("客户编号与项目单不一致");
            }
        }

        List<CustomerSkuPriceProjectExtExcelDTO> list = filterValid(target, task.getOperator());

        // 校验无问题的数据需要填充数据，成本价，成本价公式，利润率，利润率预警状态

        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 客户和sku的关系
        List<Long/*skuId*/> skuIds = list.stream().filter(item -> StringUtils.isNumeric(item.getSkuId())).map(item -> Long.parseLong(item.getSkuId())).distinct().collect(Collectors.toList());

        // 查询客户和国家的关系

        for (List<Long> skuIdList : Lists.partition(skuIds, 100)) {
            List<ProjectPriceDownloadApiDTO> projectDownloadData = customerSkuPriceRpcService.listProjectPriceDownloadDataByClientCodeAndSkuIds(clientCode, skuIdList);

            this.fillData(target, projectDownloadData);
        }


    }

    private void fillData(List<CustomerSkuPriceProjectExtExcelDTO> target, List<ProjectPriceDownloadApiDTO> projectDownloadData) {

        if (CollectionUtils.isEmpty(projectDownloadData)) {
            for (CustomerSkuPriceProjectExtExcelDTO item : target) {
                item.setValid(false);
                item.setResult("成本价不存在，请补充后重试");
            }
            log.warn("成本价不存在，请补充后重试. projectDownloadData={}", JSONObject.toJSONString(projectDownloadData));
            return;
        }

        Map<Long, ProjectPriceDownloadApiDTO> downloadApiMap = projectDownloadData.stream().collect(Collectors.toMap(ProjectPriceDownloadApiDTO::getSkuId, Function.identity(), (a, b) -> a));

        // 设置成本价
        List<ProjectPriceDownloadApiDTO> list = Lists.newArrayList();
        for (CustomerSkuPriceProjectExtExcelDTO item : target) {
            if (!item.getValid()) {
                continue;
            }

            if (!StringUtils.isNumeric(item.getSkuId())) {
                item.setResult("skuId格式错误");
                log.warn("skuId格式错误. skuId={}", item.getSkuId());
                continue;
            }

            ProjectPriceDownloadApiDTO projectPriceDownload = downloadApiMap.get(Long.parseLong(item.getSkuId()));

            if (projectPriceDownload == null || projectPriceDownload.getCountryCostPrice() == null) {
                item.setValid(false);
                item.setResult("成本价不存在，请补充后重试");
                log.warn("成本价不存在，请补充后重试. skuId={}", item.getSkuId());
                continue;
            }

            item.setCostMark(projectPriceDownload.getCostMark());
            if (projectPriceDownload.getCountryCostPrice() != null) {
                item.setCountryCostPrice(projectPriceDownload.getCountryCostPrice().toPlainString());
            }

            if (BigDecimalUtil.isBigDecimal(item.getCountryCostPrice()) && BigDecimalUtil.neNullAndZero(new BigDecimal(item.getCountryCostPrice()))
                    && BigDecimalUtil.isBigDecimal(item.getCustomerSalePrice()) && BigDecimalUtil.neNullAndZero(new BigDecimal(item.getCustomerSalePrice()))) {
                ProjectPriceDownloadApiDTO bean = BeanUtil.toBean(projectPriceDownload, ProjectPriceDownloadApiDTO.class);
                bean.setCustomerSalePrice(new BigDecimal(item.getCustomerSalePrice()));
                list.add(bean);
            }
        }

        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 设置利润率
        List<ProjectPriceDownloadApiDTO> profitRateResult = customerSkuPriceRpcService.setProfitRate(list);

        if (CollectionUtils.isEmpty(profitRateResult)) {
            return;
        }

        Map<String, BigDecimal> profitRateMap = profitRateResult.stream()
                .filter(item -> item.getProfitRate() != null)
                .collect(Collectors.toMap(item -> String.format("%s-%s", item.getCountryCostPrice(), item.getCustomerSalePrice()), ProjectPriceDownloadApiDTO::getProfitRate, (k1, k2) -> k1));
        Map<Long, ProjectPriceDownloadApiDTO> skuPriceMap = profitRateResult.stream()
                .collect(Collectors.toMap(ProjectPriceDownloadApiDTO::getSkuId, Function.identity(), (k1, k2) -> k1));

        // 回写利润率
        for (CustomerSkuPriceProjectExtExcelDTO item : target) {
            if (!item.getValid()) {
                continue;
            }
            if (BigDecimalUtil.isBigDecimal(item.getCountryCostPrice()) && BigDecimalUtil.neNullAndZero(new BigDecimal(item.getCountryCostPrice()))
                    && BigDecimalUtil.isBigDecimal(item.getCustomerSalePrice()) && BigDecimalUtil.neNullAndZero(new BigDecimal(item.getCustomerSalePrice()))) {

                BigDecimal profitRate = profitRateMap.get(String.format("%s-%s", item.getCountryCostPrice(), item.getCustomerSalePrice()));
                item.setProfitRateFormat(BigDecimalUtil.getProfitRateFormat(profitRate));
            }

            ProjectPriceDownloadApiDTO download = skuPriceMap.get(Long.valueOf(item.getSkuId()));

            if (download == null) {
                log.info("成本价不存在，请补充后重试！skuId={}, list={}, skuPriceMap={}", item.getSkuId(), JSONObject.toJSONString(list), JSONObject.toJSONString(skuPriceMap));;
                item.setValid(false);
                item.setResult("成本价不存在，请补充后重试");
                continue;
            }

            item.setSkuName(download.getSkuName());
            item.setWarningStatusName(download.getWarningStatusName());
            item.setWarningStatus(download.getWarningStatus());

            item.setProfitLimitRateFormat(BigDecimalUtil.getProfitRateFormat(download.getProfitRate()));
            item.setProfitLimitRateFormat(BigDecimalUtil.getProfitRateFormat(download.getProfitLimitRate()));
            item.setLowProfitLimitRateFormat(BigDecimalUtil.getProfitRateFormat(download.getLowProfitLimitRate()));
            item.setGrossProfitFormat(BigDecimalUtil.getProfitRateFormat(download.getGrossProfit()));

            if (download.getProfitLimitRate() == null || download.getLowProfitLimitRate() == null) {
                item.setValid(false);
                item.setResult("利润率阈值缺失，请补充后重试");
                continue;
            }

            if (StringUtils.isEmpty(item.getProfitRateFormat())) {
                item.setValid(false);
                item.setResult("利润率缺失，请补充后重试");
                continue;
            }
            item.success();

        }
    }

    @Override
    public void export(TaskDTO<CustomerSkuPriceProjectExtExcelDTO> task) {
        // 执行导出
        super.export(task);
        this.updateExcelStatus(task);

    }

    protected void updateExcelStatus(TaskDTO<CustomerSkuPriceProjectExtExcelDTO> task) {
        try {
            ProjectPriceUpdateImportStatusApiDTO input = buildUpdateExcelStatusParam(task);
            Integer result = iscProductSoaProjectPriceWriteRpcService.updateExcelStatus(input);
            log.info("updateExcelStatus, input={}, result={}", JSONObject.toJSONString(input), result);
        } catch (Exception e) {
            log.error("更新下载或者上传状态失败，message={}", ExceptionUtil.getMessage(e, "更新下载或者上传状态失败"), e);
        }
    }

    protected ProjectPriceUpdateImportStatusApiDTO buildUpdateExcelStatusParam(TaskDTO<CustomerSkuPriceProjectExtExcelDTO> task) {
        ProjectPriceUpdateImportStatusApiDTO input = new ProjectPriceUpdateImportStatusApiDTO();

        // 校验客户
        String reqJson = task.getTask().getReqParam();

        // 请求参数
        ProjectPriceApplyImportApiDTO param = JSONObject.parseObject(reqJson, ProjectPriceApplyImportApiDTO.class);

        input.setProjectCode(param.getProjectCode());
        input.setTaskId(task.getTaskId());
        input.setPin(task.getOperator());
        return input;
    }

    @Override
    public Class<CustomerSkuPriceProjectExtExcelDTO> getTargetClass() {
        return CustomerSkuPriceProjectExtExcelDTO.class;
    }

    @Override
    public String getJobName() {
        return TaskBizTypeEnum.PROJECT_BR_CUSTOMER_PRICE_DOWNLOAD.getName();
    }
}
