package com.jdi.isc.product.center.service.adapter.mapstruct.businessLog;


import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.biz.component.api.businessLog.biz.BusinessLogDTO;
import com.jdi.isc.product.center.domain.brand.biz.*;
import com.jdi.isc.product.center.domain.brand.biz.business.BrandBusinessLineVO;
import com.jdi.isc.product.center.domain.brand.po.BrandCountryPO;
import com.jdi.isc.product.center.domain.brand.po.BrandLangPO;
import com.jdi.isc.product.center.domain.brand.po.BrandPO;
import com.jdi.isc.product.center.domain.businessLog.BusinessLogResp;
import com.jdi.isc.product.soa.api.wimp.brand.req.BrandApiDTO;
import com.jdi.isc.product.soa.api.wimp.brand.req.BrandLangApiDTO;
import com.jdi.isc.product.soa.api.wimp.brand.req.BrandPageApi;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface BusinessLogConvert {


    BusinessLogConvert INSTANCE = Mappers.getMapper(BusinessLogConvert.class);

    @InheritConfiguration
    PageInfo<BusinessLogResp> apiPage2Page(PageInfo<BusinessLogDTO> input);
}
