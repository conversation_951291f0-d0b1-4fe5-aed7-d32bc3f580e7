package com.jdi.isc.product.center.service.manage.supplier.v2.businessLine;

import com.jdi.isc.product.center.domain.supplier.biz.BusinessLineVO;
import com.jdi.isc.product.center.domain.supplier.po.BusinessLineRelationPO;
import com.jdi.isc.product.center.domain.supplier.req.BusinessLineEditDTO;
import com.jdi.isc.product.center.service.adapter.mapstruct.supplier.v2.BusinessLineRelationV2Convert;
/**
    * @description: 产品线编辑抽象类
    * @author: zhangjin176
    * @date: 2025/5/10 00:40
    * @version:
    **/
public abstract class AbstractBusinessLineEditStrategy implements BusinessLineEditStrategy {

  /**
   * 无效状态标识符。
   */
  protected final static  String invalid = "invalid";
  /**
   * 代表编辑状态的常量。
   */
  protected final static  String edit = "edit";
  /**
   * 代表删除状态的常量。
   */
  protected final static  String delete = "delete";
  /**
   * 代表添加状态的常量。
   */
  protected final static  String add = "add";
    /**
     * 将BusinessLineEditDTO对象转换为BusinessLineAuditVO对象，并设置必要的审计信息。
     *
     * @param dto          业务线编辑DTO对象
     * @param relationPO   业务线关系PO对象
     * @param operator     操作人名称
     * @param supplierCode 供应商代码
     * @return 填充了审计信息的BusinessLineAuditVO对象
     */
    protected BusinessLineVO fillAuditVO(BusinessLineEditDTO dto, BusinessLineRelationPO relationPO, String operator, String supplierCode) {
        BusinessLineVO businessLineVO = BusinessLineRelationV2Convert.INSTANCE.dto2Audit(dto);
        if (relationPO != null) {
            businessLineVO.setId(relationPO.getId());
        }
        businessLineVO.setSupplierCode(supplierCode);
        businessLineVO.setLangId("zh");
        businessLineVO.setCreator(operator);
        businessLineVO.setUpdater(operator);
        businessLineVO.setCreateTime(null);
        businessLineVO.setUpdateTime(null);
        businessLineVO.setYn(null);
        return businessLineVO;
    }
}