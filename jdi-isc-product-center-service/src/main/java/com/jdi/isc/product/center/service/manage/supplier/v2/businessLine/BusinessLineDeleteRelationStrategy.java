package com.jdi.isc.product.center.service.manage.supplier.v2.businessLine;

import com.jdi.isc.product.center.domain.supplier.po.BusinessLineRelationPO;
import com.jdi.isc.product.center.domain.supplier.req.BusinessLineDataDTO;
import com.jdi.isc.product.center.domain.supplier.req.BusinessLineEditDTO;
import com.jdi.isc.product.center.domain.supplier.resp.BLDiffResult;
import org.springframework.stereotype.Component;

import java.util.Map;
/**
    * @description: 产品线策略-删除
    * @author: zhangjin176
    * @date: 2025/5/10 00:23
    * @version:
    **/
@Component
class BusinessLineDeleteRelationStrategy extends AbstractBusinessLineEditStrategy {
    /**
     * 判断是否支持指定的操作。
     * @param action 要检查的操作名称。
     * @return 如果支持指定的操作，则返回 true，否则返回 false。
     */
    @Override
    public boolean supports(String action) {
        return delete.equals(action);
    }


    /**
     * 处理业务线编辑请求。
     * @param dto 业务线编辑数据传输对象。
     * @param dbMap 数据库中存储的业务线关系映射。
     * @param result 业务线差异结果对象。
     * @param operator 操作员标识。
     * @param supplierCode 供应商代码。
     */
    @Override
    public void process(BusinessLineEditDTO dto, Map<String, BusinessLineRelationPO> dbMap, BLDiffResult result, String operator, String supplierCode) {
        BusinessLineDataDTO data = dto.getData();
        String key = data.getBrandId() + "-" + data.getCategoryId();
        if (dbMap.containsKey(key)) {
            result.getToDelete().add(fillAuditVO(dto,dbMap.get(key),operator,supplierCode));
        } else {
            dto.setAction(invalid);
            result.getToInvalid().add(fillAuditVO(dto,null,operator,supplierCode));
        }
    }
}