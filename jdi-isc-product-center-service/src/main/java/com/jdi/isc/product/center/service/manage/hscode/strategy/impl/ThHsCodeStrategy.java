package com.jdi.isc.product.center.service.manage.hscode.strategy.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeDetailVO;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.domain.hscode.th.ThHsCodeDetailRequest;
import com.jdi.isc.product.center.service.adapter.mapstruct.hscode.ThHsCodeConvert;
import com.jdi.isc.product.center.service.manage.hscode.strategy.CountryHsCodeStrategy;
import com.jdi.isc.product.soa.api.hsCode.th.HsCodeThApiService;
import com.jdi.isc.product.soa.api.hsCode.th.biz.HsCodeThApiDTO;
import com.jdi.isc.product.soa.api.hsCode.th.req.HsCodeThDetailReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.th.req.HsCodeThPageReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.th.req.HsCodeThSaveUpdateReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class ThHsCodeStrategy implements CountryHsCodeStrategy {

    @Resource
    private HsCodeThApiService hsCodeThApiService;

    @Override
    public String getCountryCode() {
        return "TH";
    }

    @Override
    public DataResponse<?> saveOrUpdate(CountryHsCodeVO input) {
        try {
            HsCodeThSaveUpdateReqApiDTO req = ThHsCodeConvert.INSTANCE.voToSaveRequest(input);
            return hsCodeThApiService.saveOrUpdate(req);
        } catch (Exception e) {
            log.error("调用泰国HsCode保存或更新接口失败，input={}", input, e);
            return DataResponse.error("泰国HsCode保存或更新失败");
        }
    }

    @Override
    public DataResponse<CountryHsCodeVO> detail(CountryHsCodeDetailVO input) {
        try {
            HsCodeThDetailReqApiDTO req = new HsCodeThDetailReqApiDTO();
            req.setId(input.getId());
            req.setHsCode(input.getHsCode());
            DataResponse<HsCodeThApiDTO> response = hsCodeThApiService.detail(req);
            if (response.getSuccess() && response.getData() != null) {
                CountryHsCodeVO vo = ThHsCodeConvert.INSTANCE.detailResponseToVo(response.getData());
                return DataResponse.success(vo);
            } else {
                return DataResponse.error(response.getMessage());
            }
        } catch (Exception e) {
            log.error("调用泰国HsCode详情接口失败，input={}", input, e);
            return DataResponse.error("泰国HsCode详情查询失败");
        }
    }

    @Override
    public PageInfo<CountryHsCodePageResponse> pageSearch(CountryHsCodePageRequest input) {
        try {
            HsCodeThPageReqApiDTO apiReq = ThHsCodeConvert.INSTANCE.voToPageRequest(input);
            DataResponse<PageInfo<HsCodeThApiDTO>> response = hsCodeThApiService.pageSearch(apiReq);
            if (response.getSuccess() && response.getData() != null) {
                PageInfo<HsCodeThApiDTO> page = response.getData();
                PageInfo<CountryHsCodePageResponse> result = new PageInfo<>();
                result.setIndex(page.getIndex());
                result.setSize(page.getSize());
                result.setTotal(page.getTotal());
                List<CountryHsCodePageResponse> records = ThHsCodeConvert.INSTANCE.pageResponseListToVoList(page.getRecords());
                result.setRecords(records);
                return result;
            } else {
                return new PageInfo<>();
            }
        } catch (Exception e) {
            log.error("调用泰国HsCode分页查询接口失败，input={}", input, e);
            return new PageInfo<>();
        }
    }
} 