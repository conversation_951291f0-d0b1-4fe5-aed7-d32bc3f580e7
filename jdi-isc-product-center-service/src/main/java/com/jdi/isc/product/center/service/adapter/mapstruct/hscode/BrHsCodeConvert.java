package com.jdi.isc.product.center.service.adapter.mapstruct.hscode;

import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.domain.hscode.br.*;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.soa.api.hsCode.br.biz.HsCodeBrApiDTO;
import com.jdi.isc.product.soa.api.hsCode.br.req.HsCodeBrSaveUpdateReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.br.req.HsCodeBrDetailReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.br.req.HsCodeBrPageReqApiDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface BrHsCodeConvert {
    BrHsCodeConvert INSTANCE = Mappers.getMapper(BrHsCodeConvert.class);
    HsCodeBrSaveUpdateReqApiDTO voToSaveRequest(CountryHsCodeVO vo);
    BrHsCodePageRequest voToPageRequest(CountryHsCodePageRequest vo);
    HsCodeBrPageReqApiDTO toApiPageReq(BrHsCodePageRequest req);
    CountryHsCodeVO apiDtoToVo(HsCodeBrApiDTO apiDto);
    List<CountryHsCodePageResponse> apiDtoListToVoList(List<HsCodeBrApiDTO> apiDtoList);
} 