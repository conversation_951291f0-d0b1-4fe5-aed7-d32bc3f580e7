package com.jdi.isc.product.center.service.order.comment.convert;

import com.jdi.isc.order.center.api.orderComment.biz.OrderCommentApiDTO;
import com.jdi.isc.order.center.api.orderComment.req.*;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentDetailReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentPageReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentSaveReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentCountReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentDeleteReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentReadApiDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 订单留言板DTO转换器
 * 注意：当前API DTO类不存在，此转换器供将来使用
 *
 * @author: zhangjin176
 * @date: 2025/5/23 11:35
 * @version:
 **/
@Mapper
public interface OrderCommentConvert {

    OrderCommentConvert INSTANCE = Mappers.getMapper(OrderCommentConvert.class);



    /**
     * 保存更新请求转换
     */
     OrderCommentSaveUpdateReqApiDTO saveReq2ApiDto(OrderCommentSaveReq req);

    /**
     * 详情查询请求转换
     */
     OrderCommentDetailReqApiDTO detailReq2ApiDto(OrderCommentDetailReq req);

    /**
     * 分页查询请求转换
     */
     OrderCommentPageReqApiDTO pageReq2ApiDto(OrderCommentPageReq req);

    /**
     * 数量查询请求转换
     */
     OrderCommentCountReqApiDTO countReq2ApiDto(OrderCommentCountReq req);

    /**
     * 删除请求转换
     */
     OrderCommentDelReqApiDTO deleteReq2ApiDto(OrderCommentDeleteReq req);

    /**
     * API响应DTO转换
     */
     OrderCommentReadApiDTO apiDto2ReadApiDto(OrderCommentApiDTO apiDto);
}
