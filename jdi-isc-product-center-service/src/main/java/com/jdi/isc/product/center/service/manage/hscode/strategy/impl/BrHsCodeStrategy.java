package com.jdi.isc.product.center.service.manage.hscode.strategy.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeDetailVO;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.service.manage.hscode.strategy.CountryHsCodeStrategy;
import com.jdi.isc.product.soa.api.hsCode.br.HsCodeBrApiService;
import com.jdi.isc.product.center.domain.hscode.br.BrHsCodeSaveOrUpdateRequest;
import com.jdi.isc.product.center.domain.hscode.br.BrHsCodeDetailRequest;
import com.jdi.isc.product.center.domain.hscode.br.BrHsCodePageRequest;
import com.jdi.isc.product.soa.api.hsCode.br.biz.HsCodeBrApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import com.jdi.isc.product.center.service.adapter.mapstruct.hscode.BrHsCodeConvert;
import com.jdi.isc.product.soa.api.hsCode.br.req.HsCodeBrSaveUpdateReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.br.req.HsCodeBrDetailReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.br.req.HsCodeBrPageReqApiDTO;

/**
 * 巴西HsCode策略实现类
 * <AUTHOR>
 * @date 2024/02/21
 */
@Slf4j
@Component
public class BrHsCodeStrategy implements CountryHsCodeStrategy {

    @Resource
    private HsCodeBrApiService hsCodeBrApiService;

    @Override
    public String getCountryCode() {
        return "BR";
    }

    @Override
    public DataResponse<?> saveOrUpdate(CountryHsCodeVO input) {
        try {
            HsCodeBrSaveUpdateReqApiDTO apiReq = BrHsCodeConvert.INSTANCE.voToSaveRequest(input);
            return hsCodeBrApiService.saveOrUpdate(apiReq);
        } catch (Exception e) {
            log.error("调用巴西HsCode保存或更新接口失败，input={}", input, e);
            return DataResponse.error("巴西HsCode保存或更新失败");
        }
    }

    @Override
    public DataResponse<CountryHsCodeVO> detail(CountryHsCodeDetailVO input) {
        try {
            HsCodeBrDetailReqApiDTO req = new HsCodeBrDetailReqApiDTO();
            req.setId(input.getId());
            req.setHsCode(input.getHsCode());
            DataResponse<HsCodeBrApiDTO> response = hsCodeBrApiService.detail(req);
            if (response.getSuccess() && response.getData() != null) {
                CountryHsCodeVO vo = BrHsCodeConvert.INSTANCE.apiDtoToVo(response.getData());
                return DataResponse.success(vo);
            } else {
                return DataResponse.error(response.getMessage());
            }
        } catch (Exception e) {
            log.error("调用巴西HsCode详情接口失败，input={}", input, e);
            return DataResponse.error("巴西HsCode详情查询失败");
        }
    }

    @Override
    public PageInfo<CountryHsCodePageResponse> pageSearch(CountryHsCodePageRequest input) {
        try {
            BrHsCodePageRequest req = BrHsCodeConvert.INSTANCE.voToPageRequest(input);
            HsCodeBrPageReqApiDTO apiReq = BrHsCodeConvert.INSTANCE.toApiPageReq(req);
            DataResponse<PageInfo<HsCodeBrApiDTO>> response = hsCodeBrApiService.pageSearch(apiReq);
            if (response.getSuccess() && response.getData() != null) {
                PageInfo<HsCodeBrApiDTO> page = response.getData();
                PageInfo<CountryHsCodePageResponse> result = new PageInfo<>();
                result.setIndex(page.getIndex());
                result.setSize(page.getSize());
                result.setTotal(page.getTotal());
                List<CountryHsCodePageResponse> records = BrHsCodeConvert.INSTANCE.apiDtoListToVoList(page.getRecords());
                result.setRecords(records);
                return result;
            } else {
                return new PageInfo<>();
            }
        } catch (Exception e) {
            log.error("调用巴西HsCode分页查询接口失败，input={}", input, e);
            return new PageInfo<>();
        }
    }
} 