package com.jdi.isc.product.center.service.worker.task.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.common.costants.Constant;
import com.jdi.isc.product.center.common.exception.TaskExportException;
import com.jdi.isc.product.center.domain.countryMku.CountryMkuTranslationExportExcelDTO;
import com.jdi.isc.product.center.domain.countryMku.CountryMkuTranslationVO;
import com.jdi.isc.product.center.domain.task.dto.TaskDTO;
import com.jdi.isc.product.center.domain.task.vo.TaskVO;
import com.jdi.isc.product.center.service.manage.countryMku.CountryMkuManageService;
import com.jdi.isc.product.center.service.worker.task.frame.BaseExecutableAbsJob;
import com.jdi.isc.product.center.service.worker.task.frame.JobExecutor;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskStatusEnum;
import com.jdi.isc.library.common.enmus.file.FileTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 国家池翻译确认导出任务执行器
 * @Author: system
 * @Date: 2024/12/20
 **/
@Slf4j
@Service
@JobExecutor(taskBizType = TaskBizTypeEnum.COUNTRY_POOL_TRANSLATION_EXPORT)
public class CountryMkuTranslationExportHandler extends BaseExecutableAbsJob<CountryMkuTranslationExportExcelDTO> {
    
    private static final Long DEFAULT_INDEX = 1L;
    private static final Long DEFAULT_SIZE = 50L;
    private static final Integer MAX_ITEM = 100000;
    
    @Resource
    private CountryMkuManageService countryMkuManageService;
    
    @Override
    public void export(TaskDTO<CountryMkuTranslationExportExcelDTO> task) {
        if (task != null) {
            log.info("CountryMkuTranslationExportHandler.export taskId: {}", task.getTaskId());
            String resultName = RESULT_FOLDER + task.getTaskBizTypeEnum().getName() + Constant.UNDER_LINE + System.currentTimeMillis() + ".xlsx";
            ByteArrayOutputStream targetOutputStream = new ByteArrayOutputStream();
            int done = 0;
            long index = 1L;
            
            try (ExcelWriter excelWriter = EasyExcel.write(targetOutputStream, CountryMkuTranslationExportExcelDTO.class)
                    .excludeColumnFieldNames(Sets.newHashSet("result")).build()) {
                
                WriteSheet writeSheet = EasyExcel.writerSheet(0, "国家池翻译确认").build();
                writeSheet.setNeedHead(true);
                
                // 解析查询参数
                CountryMkuTranslationVO.ExportRequest exportRequest = JSONObject.parseObject(task.getReqJson(), CountryMkuTranslationVO.ExportRequest.class);
                CountryMkuTranslationVO.Request req = exportRequest.getQueryCondition();
                if (req == null) {
                    req = new CountryMkuTranslationVO.Request();
                }
                req.setIndex(DEFAULT_INDEX);
                req.setSize(DEFAULT_SIZE);
                
                // 分页查询数据
                PageInfo<CountryMkuTranslationVO.Response> pageResult = countryMkuManageService.pageSearchTranslation(req);
                List<CountryMkuTranslationVO.Response> batchRes = pageResult.getRecords();
                
                while (CollectionUtils.isNotEmpty(batchRes) && done < MAX_ITEM) {
                    excelWriter.write(po2Excel(batchRes), writeSheet);
                    done += batchRes.size();
                    index++;
                    req.setIndex(index);
                    pageResult = countryMkuManageService.pageSearchTranslation(req);
                    batchRes = pageResult.getRecords();
                }
                
                task.setEndTime(System.currentTimeMillis());
                excelWriter.finish();
                
                try (ByteArrayInputStream inputStream = new ByteArrayInputStream(targetOutputStream.toByteArray())) {
                    task.setResultUrl(s3Utils.upload(inputStream, FileTypeEnum.BATCH_FILE.getCode(), resultName));
                    taskMangeService.update(new TaskVO(task, TaskStatusEnum.SUCCESS));
                }
                
                log.info("CountryMkuTranslationExportHandler.export 任务完成: taskId={}, fileUrl={}", task.getTaskId(), task.getResultUrl());
                
            } catch (Exception e) {
                log.error("CountryMkuTranslationExportHandler.export 导出异常: taskId={}", task.getTaskId(), e);
                throw new TaskExportException(task.getTaskId() + ": " + e.getMessage());
            }
        }
    }
    
    /**
     * 数据转换
     */
    private List<CountryMkuTranslationExportExcelDTO> po2Excel(List<CountryMkuTranslationVO.Response> records) {
        List<CountryMkuTranslationExportExcelDTO> result = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        for (CountryMkuTranslationVO.Response vo : records) {
            CountryMkuTranslationExportExcelDTO target = new CountryMkuTranslationExportExcelDTO();
            BeanUtils.copyProperties(vo, target);
            target.setMkuId(vo.getMkuId() != null ? vo.getMkuId().toString() : "");
            target.setSpuId(vo.getSpuId() != null ? vo.getSpuId().toString() : "");
            target.setCreateTime(vo.getCreateTime() != null ? sdf.format(vo.getCreateTime()) : "");
            target.setUpdateTime(vo.getUpdateTime() != null ? sdf.format(vo.getUpdateTime()) : "");
            result.add(target);
        }
        
        return result;
    }
}
