package com.jdi.isc.product.center.service.manage.forecast.impl;

import com.jd.common.web.LoginContext;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.library.i18n.exception.BusinessException;
import com.jdi.isc.order.center.api.forecast.ForecastOrderApiService;
import com.jdi.isc.order.center.api.forecast.biz.ForecastOrderApiDTO;
import com.jdi.isc.order.center.api.forecast.biz.req.ForecastDetailOrderApiReq;
import com.jdi.isc.order.center.api.forecast.biz.req.ForecastOrderApiReq;
import com.jdi.isc.order.center.api.forecast.biz.req.IssuedForecastOrderApiReq;
import com.jdi.isc.order.center.api.forecast.biz.req.UpdateForecastOrderStatusApiReq;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderApiResp;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderDetailResp;
import com.jdi.isc.product.center.service.manage.forecast.ForecastService;
import com.jdi.isc.task.center.api.forecast.ForecastPurchaseOrderPrintApiService;
import com.jdi.isc.task.center.api.forecast.impl.ForecastPurchaseOrderPrintApiDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class ForecastServiceImpl implements ForecastService {
    @Resource
    private ForecastOrderApiService forecastOrderApiService;

    @Resource
    private ForecastPurchaseOrderPrintApiService forecastPurchaseOrderPrintApiService;

    @Override
    public PageInfo<ForecastOrderApiResp> pageForecastOrder(ForecastOrderApiReq forecastOrderApiDTO) {
        forecastOrderApiDTO.setOperator(LoginContext.getLoginContext().getPin());
        DataResponse<PageInfo<ForecastOrderApiResp>> response = forecastOrderApiService.pageForecastOrder(forecastOrderApiDTO);
        if (!Boolean.TRUE.equals(response.getSuccess())) {
            throw new BusinessException(response.getCode(),response.getMessage());
        }
        return response.getData();
    }

    @Override
    public Boolean submitForecastOrder(ForecastOrderApiDTO api) {
        DataResponse<Boolean> response = forecastOrderApiService.submitForecastOrder(api);
        if (!Boolean.TRUE.equals(response.getSuccess())) {
            throw new BusinessException(response.getCode(),response.getMessage());
        }
        return response.getData();
    }

    @Override
    public ForecastOrderDetailResp detailForecastOrder(ForecastDetailOrderApiReq apiDTO) {
        DataResponse<ForecastOrderDetailResp> response = forecastOrderApiService.detailForecastOrder(apiDTO);
        if (!Boolean.TRUE.equals(response.getSuccess())) {
            throw new BusinessException(response.getCode(),response.getMessage());
        }
        return response.getData();
    }

    @Override
    public Boolean updateForecastOrderStatus(UpdateForecastOrderStatusApiReq apiDTO) {
        DataResponse<Boolean> response = forecastOrderApiService.updateForecastOrderStatus(apiDTO);
        if (!Boolean.TRUE.equals(response.getSuccess())) {
            throw new BusinessException(response.getCode(),response.getMessage());
        }
        return response.getData();
    }

    @Override
    public Boolean issuedForecastOrder(IssuedForecastOrderApiReq issuedForecastOrderApiReq) {
        DataResponse<Boolean> response = forecastOrderApiService.issuedForecastOrder(issuedForecastOrderApiReq);
        if (!Boolean.TRUE.equals(response.getSuccess())) {
            throw new BusinessException(response.getCode(),response.getMessage());
        }
        return response.getData();
    }

    @Override
    public String printEntryStock(ForecastPurchaseOrderPrintApiDTO apiDTO) {
        DataResponse<String> response = forecastPurchaseOrderPrintApiService.printEntryStock(apiDTO);
        if (!Boolean.TRUE.equals(response.getSuccess())) {
            throw new BusinessException(response.getCode(),response.getMessage());
        }
        return response.getData();
    }
}
