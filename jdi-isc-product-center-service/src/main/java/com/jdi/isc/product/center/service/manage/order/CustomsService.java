package com.jdi.isc.product.center.service.manage.order;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.customs.biz.req.ProductCustomsPageReqDTO;
import com.jdi.isc.order.center.api.customs.biz.resp.ProductCustomsDTO;
import com.jdi.isc.product.center.domain.customs.ProductCustomsVO;


public interface CustomsService {



    /**
     * 根据售后订单信息分页查询产品海关信息。
     * @param pageReqDTO 售后订单信息，用于查询相关的产品海关信息。
     * @return 包含查询结果的分页信息对象。
     */
    PageInfo<ProductCustomsDTO> page(ProductCustomsPageReqDTO pageReqDTO);

    /**
     * 编辑产品的海关信息。
     * @param reqDTO 包含要编辑的产品海关信息的DTO对象。
     * @return 编辑操作是否成功。
     */
    Boolean edit(ProductCustomsVO reqDTO);

}
