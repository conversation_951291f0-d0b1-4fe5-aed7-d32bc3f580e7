package com.jdi.isc.product.center.service.manage.hscode.strategy;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeDetailVO;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;

/**
 * 国家HsCode策略接口
 * 定义不同国家HsCode操作的统一接口
 * <AUTHOR>
 * @date 2024/02/21
 */
public interface CountryHsCodeStrategy {

    /**
     * 获取策略支持的国家代码
     * @return 国家代码
     */
    String getCountryCode();

    /**
     * 保存或更新HsCode信息
     *
     * @param input HsCode信息
     * @return 操作结果
     */
    DataResponse<?> saveOrUpdate(CountryHsCodeVO input);

    /**
     * 分页查询HsCode信息
     * @param input 查询条件
     * @return 分页结果
     */
    PageInfo<CountryHsCodePageResponse> pageSearch(CountryHsCodePageRequest input);

    /**
     * 查询HsCode详情
     * @param input 查询条件
     * @return 详情结果
     */
    DataResponse<CountryHsCodeVO> detail(CountryHsCodeDetailVO input);
} 