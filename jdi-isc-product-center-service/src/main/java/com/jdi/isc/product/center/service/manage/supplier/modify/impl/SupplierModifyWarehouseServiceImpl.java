package com.jdi.isc.product.center.service.manage.supplier.modify.impl;

import com.alibaba.fastjson.JSON;
import com.jd.common.web.LoginContext;
import com.jdi.isc.product.center.common.exception.BizException;
import com.jdi.isc.product.center.domain.common.biz.BasicVO;
import com.jdi.isc.product.center.domain.common.po.BasicPO;
import com.jdi.isc.product.center.domain.enums.supplier.SupplierModifyActionEnum;
import com.jdi.isc.product.center.domain.enums.supplier.SupplierModuleEnum;
import com.jdi.isc.product.center.domain.supplier.biz.SupplierAccountVO;
import com.jdi.isc.product.center.domain.supplier.biz.SupplierModifyRecordVO;
import com.jdi.isc.product.center.domain.supplier.biz.SupplierWarehouseVO;
import com.jdi.isc.product.center.domain.supplier.po.SupplierAccountPO;
import com.jdi.isc.product.center.domain.supplier.po.SupplierModifyRecordPO;
import com.jdi.isc.product.center.domain.supplier.po.SupplierWarehousePO;
import com.jdi.isc.product.center.service.adapter.mapstruct.supplier.SupplierAccountConvert;
import com.jdi.isc.product.center.service.adapter.mapstruct.supplier.SupplierWareHouseConvert;
import com.jdi.isc.product.center.service.atomic.supplier.SupplierWarehouseAtomicService;
import com.jdi.isc.product.center.service.manage.supplier.SupplierAccountManageService;
import com.jdi.isc.product.center.service.manage.supplier.SupplierWarehouseManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 供应商信息修改记录数据维护服务
 * @Author: zhaojianguo21
 * @Date: 2024/03/20 21:29
 **/
@Slf4j
@Service
public class SupplierModifyWarehouseServiceImpl extends AbstractSupplierModifyService {
    @Resource
    private SupplierWarehouseManageService supplierWarehouseManageService;

    @Override
    public List<? extends BasicPO> queryPoListByCode(String supplierCode) {
        return null;
    }

    @Override
    public boolean checkAllowAddNewPo() {
        return false;
    }

    @Override
    public boolean checkAllowDelPo() {
        return false;
    }

    @Override
    public Set<String> allowModifyFields() {
        return null;
    }

    @Override
    public List<? extends BasicPO> quedoryPoList(Set<Long> ids) {
        return null;
    }

    @Override
    public void savePos(List<BasicPO> basePOV2s) {
        List<SupplierWarehouseVO> batchSave = basePOV2s.stream().map(b -> {
            SupplierWarehousePO po = (SupplierWarehousePO)b;
            return SupplierWareHouseConvert.INSTANCE.poToVO(po);
        }).collect(Collectors.toList());
        boolean res = supplierWarehouseManageService.batchAdd(batchSave);
        Assert.isTrue(res,"新增仓异常");
    }

    @Override
    public void updatePos(List<BasicPO> basePOV2s) {

    }

    @Override
    public void delPos(Set<Long> delIds, String optPin, long optTimestamp) {
        boolean res = supplierWarehouseManageService.batchDel(delIds,optPin,optTimestamp);
    }

    @Override
    public BasicPO vo2Po(BasicVO vo) {
        return null;
    }

    @Override
    public BasicVO po2Vo(BasicPO po) {
        return null;
    }
}
