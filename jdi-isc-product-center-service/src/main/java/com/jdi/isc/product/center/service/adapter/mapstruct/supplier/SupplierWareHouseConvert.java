package com.jdi.isc.product.center.service.adapter.mapstruct.supplier;


import com.jdi.isc.product.center.domain.supplier.biz.SupplierWarehouseVO;
import com.jdi.isc.product.center.domain.supplier.po.SupplierWarehousePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SupplierWareHouseConvert {

    SupplierWareHouseConvert INSTANCE = Mappers.getMapper(SupplierWareHouseConvert.class);


    SupplierWarehousePO voToPO(SupplierWarehouseVO warehouse);
    List<SupplierWarehousePO> listVoToPO(List<SupplierWarehouseVO> warehouses);

    /**
     * 将供应商仓库PO列表转换为VO列表
     * @param list 供应商仓库PO对象列表
     * @return 转换后的供应商仓库VO对象列表
     */
    List<SupplierWarehouseVO> poToVOList(List<SupplierWarehousePO> list);

    /**
     * 将供应商仓库PO对象转换为VO对象
     * @param po 供应商仓库持久化对象，包含原始数据
     * @return 转换后的供应商仓库视图对象
     */
    SupplierWarehouseVO poToVO(SupplierWarehousePO po);
}
