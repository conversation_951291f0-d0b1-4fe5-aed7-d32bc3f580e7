package com.jdi.isc.product.center.service.manage.bdp;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.api.devOps.req.ForeverLogDTO;
import com.jdi.isc.product.center.domain.bdp.ForeverLogVO;

/**
 * 离线日志查询服务
 * <AUTHOR>
 * @date 2025/4/16
 */
public interface OfflineLogManageService {

    DataResponse<PageInfo<ForeverLogVO>> page(ForeverLogDTO req);

}
