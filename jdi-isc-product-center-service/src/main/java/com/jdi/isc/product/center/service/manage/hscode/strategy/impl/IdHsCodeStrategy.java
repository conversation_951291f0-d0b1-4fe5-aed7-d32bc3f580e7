package com.jdi.isc.product.center.service.manage.hscode.strategy.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeDetailVO;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.service.adapter.mapstruct.hscode.IdHsCodeConvert;
import com.jdi.isc.product.center.service.manage.hscode.strategy.CountryHsCodeStrategy;
import com.jdi.isc.product.soa.api.hsCode.id.HsCodeIdApiService;
import com.jdi.isc.product.soa.api.hsCode.id.biz.HsCodeIdApiDTO;
import com.jdi.isc.product.soa.api.hsCode.id.req.HsCodeIdSaveUpdateReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.id.req.HsCodeIdDetailReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.id.req.HsCodeIdPageReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class IdHsCodeStrategy implements CountryHsCodeStrategy {

    @Resource
    private HsCodeIdApiService hsCodeIdApiService;

    @Override
    public String getCountryCode() {
        return "ID";
    }

    @Override
    public DataResponse<?> saveOrUpdate(CountryHsCodeVO input) {
        try {
            // 直接转换，避免两次转换
            HsCodeIdSaveUpdateReqApiDTO apiReq = IdHsCodeConvert.INSTANCE.voToApiSaveReq(input);
            // 调用API服务并进行类型转换
            DataResponse<?> response = hsCodeIdApiService.saveOrUpdate(apiReq);
            if (response.getSuccess()) {
                return DataResponse.success(response.getData());
            } else {
                return DataResponse.error(response.getMessage());
            }
        } catch (Exception e) {
            log.error("调用印尼HsCode保存或更新接口失败，input={}", input, e);
            return DataResponse.error("印尼HsCode保存或更新失败");
        }
    }

    @Override
    public DataResponse<CountryHsCodeVO> detail(CountryHsCodeDetailVO input) {
        try {
            HsCodeIdDetailReqApiDTO req = new HsCodeIdDetailReqApiDTO();
            req.setId(input.getId());
            req.setHsCode(input.getHsCode());
            DataResponse<HsCodeIdApiDTO> response = hsCodeIdApiService.detail(req);
            if (response.getSuccess() && response.getData() != null) {
                CountryHsCodeVO vo = IdHsCodeConvert.INSTANCE.apiDtoToVo(response.getData());
                return DataResponse.success(vo);
            } else {
                return DataResponse.error(response.getMessage());
            }
        } catch (Exception e) {
            log.error("调用印尼HsCode详情接口失败，input={}", input, e);
            return DataResponse.error("印尼HsCode详情查询失败");
        }
    }

    @Override
    public PageInfo<CountryHsCodePageResponse> pageSearch(CountryHsCodePageRequest input) {
        try {
            HsCodeIdPageReqApiDTO apiReq = IdHsCodeConvert.INSTANCE.voToApiPageReq(input);
            DataResponse<PageInfo<HsCodeIdApiDTO>> response = hsCodeIdApiService.pageSearch(apiReq);
            if (response.getSuccess() && response.getData() != null) {
                PageInfo<HsCodeIdApiDTO> page = response.getData();
                PageInfo<CountryHsCodePageResponse> result = new PageInfo<>();
                result.setIndex(page.getIndex());
                result.setSize(page.getSize());
                result.setTotal(page.getTotal());
                List<CountryHsCodePageResponse> records = IdHsCodeConvert.INSTANCE.pageResponseListToVoList(page.getRecords());
                result.setRecords(records);
                return result;
            } else {
                return new PageInfo<>();
            }
        } catch (Exception e) {
            log.error("调用印尼HsCode分页查询接口失败，input={}", input, e);
            return new PageInfo<>();
        }
    }
} 