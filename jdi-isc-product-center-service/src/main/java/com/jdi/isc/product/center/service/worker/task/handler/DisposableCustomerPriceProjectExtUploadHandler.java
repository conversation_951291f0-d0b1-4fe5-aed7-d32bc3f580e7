package com.jdi.isc.product.center.service.worker.task.handler;

import com.jdi.isc.product.center.domain.enums.YnEnum;
import com.jdi.isc.product.center.domain.task.dto.TaskDTO;
import com.jdi.isc.product.center.domain.task.excel.CustomerSkuPriceProjectExtExcelDTO;
import com.jdi.isc.product.center.service.worker.task.frame.JobExecutor;
import com.jdi.isc.product.soa.api.common.enums.CustomerSkuPriceWarningEnums;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceUpdateImportStatusApiDTO;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 巴西价格申请单上传.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@JobExecutor(taskBizType = TaskBizTypeEnum.PROJECT_CUSTOMER_PRICE_UPLOAD)
public class DisposableCustomerPriceProjectExtUploadHandler extends DisposableCustomerPriceProjectExtDownloadHandler {

    @Override
    protected ProjectPriceUpdateImportStatusApiDTO buildUpdateExcelStatusParam(TaskDTO<CustomerSkuPriceProjectExtExcelDTO> task) {
        ProjectPriceUpdateImportStatusApiDTO result = super.buildUpdateExcelStatusParam(task);

        // 默认均为需要审批
        int firstAudit = YnEnum.NO.getCode();
        int secondAudit = YnEnum.NO.getCode();
        int thirdAudit = YnEnum.NO.getCode();
        int fourthAudit = YnEnum.NO.getCode();
        int uploadCheckResult = YnEnum.YES.getCode();
        String updateCheckReason = StringUtils.EMPTY;
        String rejectReason = StringUtils.EMPTY;

        boolean hasInvalid = false;
        boolean hasProfitLoss = false;
        boolean hasYellow = false;
        boolean hasRed = false;
        boolean hasDeepRed = false;

        // 一次遍历统计所有情况
        for (CustomerSkuPriceProjectExtExcelDTO item : task.getTarget()) {
            if (!item.getValid()) {
                hasInvalid = true;
                break;
            }
            Integer warningStatus = item.getWarningStatus();
            if (warningStatus == null || warningStatus.equals(CustomerSkuPriceWarningEnums.PROFIT_RATE_LOSS.getCode())) {
                hasProfitLoss = true;
                break;
            }
            if (warningStatus.equals(CustomerSkuPriceWarningEnums.DEEP_RED.getCode())) {
                hasDeepRed = true;
            } else if (warningStatus.equals(CustomerSkuPriceWarningEnums.RED.getCode())) {
                hasRed = true;
            } else if (warningStatus.equals(CustomerSkuPriceWarningEnums.YELLOW.getCode())) {
                hasYellow = true;
            }
        }

        // 校验结果处理
        if (hasInvalid) {
            uploadCheckResult = YnEnum.NO.getCode();
            updateCheckReason = "excel存在验证不通过数据，请下载excel查看详情";
        } else if (hasProfitLoss) {
            uploadCheckResult = YnEnum.NO.getCode();
            updateCheckReason = "excel存在利润率缺或利润率阈值缺失数据，请下载excel查看详情";
        }

        // 审批级别处理
        if (uploadCheckResult == YnEnum.YES.getCode()) {
            if (hasDeepRed) {
                // 边际负毛，最高级审批
                firstAudit = secondAudit = thirdAudit = fourthAudit = YnEnum.YES.getCode();
                rejectReason = "商品中存在边际负毛商品，需要经过店长->国家经理->大区总->经分审批。请下载商品明细查看";
            } else if (hasRed) {
                // 超低毛，三级审批
                firstAudit = secondAudit = thirdAudit = YnEnum.YES.getCode();
                rejectReason = "商品中存在超低毛商品，需要经过店长->国家经理->大区总审批。请下载商品明细查看。";
            } else if (hasYellow) {
                // 低毛，一级审批
                firstAudit = YnEnum.YES.getCode();
                rejectReason = "商品中存在低毛商品，需要经过店长审批。请下载商品明细查看";
            } else {
                // 正常品，无需审批
                rejectReason = "商品中不存在低毛商品，无需审批";
            }
            result.setFirstAudit(firstAudit);
            result.setSecondAudit(secondAudit);
            result.setThirdAudit(thirdAudit);
            result.setFourthAudit(fourthAudit);


            result.setRejectReason(rejectReason);
        }

        result.setUploadCheckResult(uploadCheckResult);
        result.setUploadCheckReason(updateCheckReason);

        return result;
    }


    @Override
    public String getJobName() {
        return TaskBizTypeEnum.PROJECT_CUSTOMER_PRICE_UPLOAD.getName();
    }
}