package com.jdi.isc.product.center.service.manage.hscode.strategy.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeDetailVO;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.service.adapter.mapstruct.hscode.HuHsCodeConvert;
import com.jdi.isc.product.center.service.manage.hscode.strategy.CountryHsCodeStrategy;
import com.jdi.isc.product.soa.api.hsCode.hu.HsCodeHuApiService;
import com.jdi.isc.product.soa.api.hsCode.hu.biz.HsCodeHuApiDTO;
import com.jdi.isc.product.soa.api.hsCode.hu.req.HsCodeHuDetailReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.hu.req.HsCodeHuPageReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.hu.req.HsCodeHuSaveUpdateReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class HuHsCodeStrategy implements CountryHsCodeStrategy {

    @Resource
    private HsCodeHuApiService hsCodeHuApiService;

    @Override
    public String getCountryCode() {
        return "HU";
    }

    @Override
    public DataResponse<?> saveOrUpdate(CountryHsCodeVO input) {
        try {
            HsCodeHuSaveUpdateReqApiDTO apiReq = HuHsCodeConvert.INSTANCE.voToSaveRequest(input);
            return hsCodeHuApiService.saveOrUpdate(apiReq);
        } catch (Exception e) {
            log.error("调用匈牙利HsCode保存或更新接口失败，input={}", input, e);
            return DataResponse.error("匈牙利HsCode保存或更新失败");
        }
    }

    @Override
    public DataResponse<CountryHsCodeVO> detail(CountryHsCodeDetailVO input) {
        try {
            HsCodeHuDetailReqApiDTO req = new HsCodeHuDetailReqApiDTO();
            req.setId(input.getId());
            req.setHsCode(input.getHsCode());
            DataResponse<HsCodeHuApiDTO> response = hsCodeHuApiService.detail(req);
            if (response.getSuccess() && response.getData() != null) {
                CountryHsCodeVO vo = HuHsCodeConvert.INSTANCE.detailResponseToVo(response.getData());
                return DataResponse.success(vo);
            } else {
                return DataResponse.error(response.getMessage());
            }
        } catch (Exception e) {
            log.error("调用匈牙利HsCode详情接口失败，input={}", input, e);
            return DataResponse.error("匈牙利HsCode详情查询失败");
        }
    }

    @Override
    public PageInfo<CountryHsCodePageResponse> pageSearch(CountryHsCodePageRequest input) {
        try {
            HsCodeHuPageReqApiDTO apiReq = HuHsCodeConvert.INSTANCE.voToPageRequest(input);
            DataResponse<PageInfo<HsCodeHuApiDTO>> response = hsCodeHuApiService.pageSearch(apiReq);
            if (response.getSuccess() && response.getData() != null) {
                PageInfo<HsCodeHuApiDTO> page = response.getData();
                PageInfo<CountryHsCodePageResponse> result = new PageInfo<>();
                result.setIndex(page.getIndex());
                result.setSize(page.getSize());
                result.setTotal(page.getTotal());
                List<CountryHsCodePageResponse> records = HuHsCodeConvert.INSTANCE.pageResponseListToVoList(page.getRecords());
                result.setRecords(records);
                return result;
            } else {
                return new PageInfo<>();
            }
        } catch (Exception e) {
            log.error("调用匈牙利HsCode分页查询接口失败，input={}", input, e);
            return new PageInfo<>();
        }
    }
} 