package com.jdi.isc.product.center.service.manage.mku;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.mku.biz.MkuChangeBindingRecordQueryVO;
import com.jdi.isc.product.center.domain.mku.biz.MkuChangeBindingRecordVO;

/**
 * MKU换绑服务
 * <AUTHOR>
 * @description：MkuChangeBindingManageService
 * @Date 2025-08-27
 */
public interface MkuChangeBindingManageService {

    /**
     * 分页查询MKU变更绑定记录
     * @param reqVO 查询条件
     * @return 分页结果，包含MKU变更绑定记录列表和分页信息
     */
    DataResponse<PageInfo<MkuChangeBindingRecordVO>> page(MkuChangeBindingRecordQueryVO reqVO);
}
