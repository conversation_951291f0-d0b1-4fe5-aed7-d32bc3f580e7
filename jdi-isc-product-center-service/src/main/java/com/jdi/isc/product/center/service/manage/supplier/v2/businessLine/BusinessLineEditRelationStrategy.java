package com.jdi.isc.product.center.service.manage.supplier.v2.businessLine;

import com.jdi.isc.product.center.domain.common.biz.FieldDiffVO;
import com.jdi.isc.product.center.domain.enums.supplier.SupplierAgentLevelEnum;
import com.jdi.isc.product.center.domain.enums.supplier.SupplierFieldAllEnum;
import com.jdi.isc.product.center.domain.enums.supplier.SupplierFieldBussiseLineEnum;
import com.jdi.isc.product.center.domain.enums.supplier.SupplierModuleEnum;
import com.jdi.isc.product.center.domain.supplier.biz.BusinessLineVO;
import com.jdi.isc.product.center.domain.supplier.po.BusinessLineRelationPO;
import com.jdi.isc.product.center.domain.supplier.req.BusinessLineDataDTO;
import com.jdi.isc.product.center.domain.supplier.req.BusinessLineEditDTO;
import com.jdi.isc.product.center.domain.supplier.resp.BLDiffResult;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @description: 产品线策略-新增/修改
 * @author: zhangjin176
 * @date: 2025/5/10 00:41
 * @version:
 **/
@Component
class BusinessLineEditRelationStrategy extends AbstractBusinessLineEditStrategy {
    /**
     * 判断是否支持指定的操作。
     *
     * @param action 操作名称。
     * @return 如果支持指定的操作，则返回 true，否则返回 false。
     */
    @Override
    public boolean supports(String action) {
        return edit.equals(action);
    }

    /**
     * 处理业务线编辑请求，比较数据库中存储的数据与传入的DTO，更新或添加相应的记录。
     *
     * @param dto          业务线编辑DTO对象，包含要更新或添加的数据。
     * @param dbMap        数据库中存储的业务线关系PO对象的Map，用于查找对应的PO对象。
     * @param result       BLDiffResult对象，用于存储处理结果。
     * @param operator     操作员ID，用于记录操作日志。
     * @param supplierCode 供应商代码，用于记录操作日志。
     */
    @Override
    public void process(BusinessLineEditDTO dto, Map<String, BusinessLineRelationPO> dbMap, BLDiffResult result, String operator, String supplierCode) {
        BusinessLineDataDTO data = dto.getData();
        String key = data.getBrandId() + "-" + data.getCategoryId();
        BusinessLineRelationPO po = dbMap.get(key);
        if (po == null) {
            dto.setAction(add);
            result.getToAdd().add(fillAuditVO(dto, null, operator, supplierCode));
        } else {

            List<FieldDiffVO> fieldDiffVOS = collectBusinessLineDiff(po, dto.getData());
            if (fieldDiffVOS.size()>0) {
                BusinessLineVO businessLineVO = fillAuditVO(dto, dbMap.get(key), operator, supplierCode);
                businessLineVO.setDiffFields(fieldDiffVOS);
                result.getToUpdate().add(businessLineVO);
            } else {
                dto.setAction(invalid);
                result.getNoChange().add(fillAuditVO(dto, null, operator, supplierCode));
            }
        }
    }

    public List<FieldDiffVO> collectBusinessLineDiff(BusinessLineRelationPO before, BusinessLineDataDTO after) {
        List<FieldDiffVO> diffList = new ArrayList<>();

        // 授权书
        if (!Objects.equals(before.getAuthorizeUrl(), after.getAuthorizeUrl())) {
            diffList.add(buildDelChange(
                    SupplierFieldBussiseLineEnum.AUTHORIZE_URL.getCode(),
                    after.getAuthorizeUrl(),
                    SupplierFieldAllEnum.codeToDesc(SupplierModuleEnum.BUSINESS_LINE.getCode(), SupplierFieldBussiseLineEnum.AUTHORIZE_URL.getCode()),
                    before.getAuthorizeUrl()));
        }

        // 商标证书
        if (!Objects.equals(before.getTrademarkCertificateUrl(), after.getTrademarkCertificateUrl())) {
            diffList.add(buildDelChange(
                    SupplierFieldBussiseLineEnum.TRADE_MARK_CERTIFICATE_URL.getCode(),
                    after.getTrademarkCertificateUrl(),
                    SupplierFieldAllEnum.codeToDesc(SupplierModuleEnum.BUSINESS_LINE.getCode(), SupplierFieldBussiseLineEnum.TRADE_MARK_CERTIFICATE_URL.getCode()),
                    before.getTrademarkCertificateUrl()));
        }

        // 代理等级
        if (!Objects.equals(before.getAgentLevel(), after.getAgentLevel())) {
            String beforeDesc = Optional.ofNullable(SupplierAgentLevelEnum.forCode(before.getAgentLevel()))
                    .map(SupplierAgentLevelEnum::getDesc).orElse(SupplierAgentLevelEnum.UNAUTHORIZED.getDesc());
            String afterDesc = Optional.ofNullable(SupplierAgentLevelEnum.forCode(after.getAgentLevel()))
                    .map(SupplierAgentLevelEnum::getDesc).orElse(SupplierAgentLevelEnum.UNAUTHORIZED.getDesc());
            diffList.add(buildDelChange(
                    SupplierFieldBussiseLineEnum.AGENT_LEVEL.getCode(),
                    afterDesc,
                    SupplierFieldAllEnum.codeToDesc(SupplierModuleEnum.BUSINESS_LINE.getCode(), SupplierFieldBussiseLineEnum.AGENT_LEVEL.getCode()),
                    beforeDesc));
        }

        return diffList;
    }

    private FieldDiffVO buildDelChange(String fieldName, Object targetValue,String labelName,Object originValue) {
        FieldDiffVO compareResult = new FieldDiffVO();
        compareResult.setFieldName(fieldName);
        compareResult.setTargetContent(targetValue);
        compareResult.setFieldLabelName(labelName);
        compareResult.setSourceContent(originValue);
        return compareResult;
    }
}