package com.jdi.isc.product.center.service.manage.supplier.v2.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdi.isc.library.i18n.exception.BusinessFeatureException;
import com.jdi.isc.product.center.common.costants.I18nConstant;
import com.jdi.isc.product.center.common.enums.response.BusinessErrorEnums;
import com.jdi.isc.product.center.domain.enums.YnEnum;
import com.jdi.isc.product.center.domain.supplier.po.BusinessLineRelationPO;
import com.jdi.isc.product.center.service.atomic.supplier.BusinessLineRelationAtomicService;
import com.jdi.isc.product.center.service.manage.supplier.v2.BusinessLineRelationManageV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 产品线管理实现类
 * @author: zhangjin176
 * @date: 2025/5/6 13:23
 * @version: 2.0
 **/
@Slf4j
@Service
public class BusinessLineRelationManageV2ServiceImpl implements BusinessLineRelationManageV2Service {

    @Resource
    private BusinessLineRelationAtomicService businessLineRelationAtomicService;


    @Override
    public Page<BusinessLineRelationPO> page(String supplierCode, Long brandId, List<Long> catIds, Integer agentLevel, Integer index, Integer size) {
        Page<BusinessLineRelationPO> page = new Page<>( index==null? 1 : index, size==null ? 20 : size);
        if (StringUtils.isBlank(supplierCode)) {
            throw new BusinessFeatureException(BusinessErrorEnums.PARAM_NOT_ALLOW_EMPTY, I18nConstant.SUPPLIER_CODE);
        }
        LambdaQueryWrapper<BusinessLineRelationPO> queryWrapper = Wrappers.<BusinessLineRelationPO>lambdaQuery()
                .eq(BusinessLineRelationPO::getSupplierCode, supplierCode)
                .eq(agentLevel!=null,BusinessLineRelationPO::getAgentLevel, agentLevel)
                .in(CollectionUtils.isNotEmpty(catIds), BusinessLineRelationPO::getJdCatId, catIds)
                .eq(brandId != null, BusinessLineRelationPO::getBrandId, brandId)
                .eq(BusinessLineRelationPO::getYn, YnEnum.YES.getCode())
                .orderByDesc(BusinessLineRelationPO::getBrandId,BusinessLineRelationPO::getUpdateTime);
        return businessLineRelationAtomicService.page(page, queryWrapper);
    }

    @Override
    public List<BusinessLineRelationPO> getSelectList(String supplierCode) {
        if (StringUtils.isBlank(supplierCode)) {
            throw new BusinessFeatureException(BusinessErrorEnums.PARAM_NOT_ALLOW_EMPTY, I18nConstant.SUPPLIER_CODE);
        }
        LambdaQueryWrapper<BusinessLineRelationPO> queryWrapper = Wrappers.<BusinessLineRelationPO>lambdaQuery()
                .select(BusinessLineRelationPO::getId,
                        BusinessLineRelationPO::getSupplierCode,
                        BusinessLineRelationPO::getBrandId,
                        BusinessLineRelationPO::getCategoryId,
                        BusinessLineRelationPO::getAgentLevel,
                        BusinessLineRelationPO::getAuthorizeUrl,
                        BusinessLineRelationPO::getTrademarkCertificateUrl,
                        BusinessLineRelationPO::getJdCatId)
                .eq(BusinessLineRelationPO::getSupplierCode, supplierCode)
                .eq(BusinessLineRelationPO::getYn, YnEnum.YES.getCode());
        return businessLineRelationAtomicService.list(queryWrapper);
    }

    @Override
    public Long count(String supplierCode, String brandId, String catId) {
        LambdaQueryWrapper<BusinessLineRelationPO> queryWrapper = Wrappers.<BusinessLineRelationPO>lambdaQuery()
                .eq(BusinessLineRelationPO::getSupplierCode, supplierCode)
                .eq(BusinessLineRelationPO::getBrandId, brandId)
                .eq(BusinessLineRelationPO::getJdCatId, catId)
                .eq(BusinessLineRelationPO::getYn, YnEnum.YES.getCode());
        return businessLineRelationAtomicService.count(queryWrapper);
    }
}
