package com.jdi.isc.product.center.service.manage.supplier.v2.businessLine;

import com.jdi.isc.product.center.domain.supplier.po.BusinessLineRelationPO;
import com.jdi.isc.product.center.domain.supplier.req.BusinessLineEditDTO;
import com.jdi.isc.product.center.domain.supplier.resp.BLDiffResult;
import org.apache.commons.lang3.builder.DiffResult;

import java.util.Map;
/**
    * @description: 产品线编辑接口
    * @author: zhangjin176
    * @date: 2025/5/10 00:40
    * @version:
    **/
public interface BusinessLineEditStrategy {
    /**
     * 判断是否支持指定的操作。
     * @param action 要检查的操作名称。
     * @return 如果支持指定的操作，则返回 true；否则返回 false。
     */
    boolean supports(String action);

    /**
     * 处理业务线编辑请求。
     * @param dto 业务线编辑DTO对象，包含编辑的业务线信息。
     * @param dbMap 数据库中存储的业务线关系映射表。
     * @param result 业务线差异结果对象，用于记录编辑前后的差异。
     * @param operator 操作员标识，记录谁进行了编辑操作。
     * @param supplierCode 供应商代码，用于关联业务线和供应商。
     */
    void process(BusinessLineEditDTO dto, Map<String, BusinessLineRelationPO> dbMap, BLDiffResult result, String operator, String supplierCode);
}