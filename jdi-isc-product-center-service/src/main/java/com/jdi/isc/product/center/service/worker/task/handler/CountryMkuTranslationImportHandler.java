package com.jdi.isc.product.center.service.worker.task.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSON;
import com.jdi.isc.product.center.common.utils.validation.PropertyError;
import com.jdi.isc.product.center.common.utils.validation.ValidateResult;
import com.jdi.isc.product.center.common.utils.validation.ValidationUtil;
import com.jdi.isc.product.center.domain.countryMku.CountryMkuTranslationImportExcelDTO;
import com.jdi.isc.product.center.domain.countryMku.CountryMkuTranslationVO;
import com.jdi.isc.product.center.domain.task.dto.TaskDTO;
import com.jdi.isc.product.center.service.manage.countryMku.CountryMkuManageService;
import com.jdi.isc.product.center.service.worker.task.frame.BaseExecutableAbsJob;
import com.jdi.isc.product.center.service.worker.task.frame.JobExecutor;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskCreateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 国家池翻译确认导入任务执行器
 * @Author: system
 * @Date: 2024/12/20
 **/
@Slf4j
@Service
@JobExecutor(taskBizType = TaskBizTypeEnum.COUNTRY_POOL_TRANSLATION_IMPORT)
public class CountryMkuTranslationImportHandler extends BaseExecutableAbsJob<CountryMkuTranslationImportExcelDTO> {
    
    @Resource
    private CountryMkuManageService countryMkuManageService;
    
    @Override
    public void parse(TaskDTO<CountryMkuTranslationImportExcelDTO> task) {
        if (TaskCreateTypeEnum.IMPORT.equals(task.getTaskCreateType())) {
            log.info("CountryMkuTranslationImportHandler.parse taskId: {}, file: {}", task.getTaskId(), task.getTargetInputFile().getKey());
            
            if (task.getTargetInputFile() != null && task.getTargetInputFile().getObjectContent() != null) {
                List<CountryMkuTranslationImportExcelDTO> target = EasyExcel.read(
                    task.getTargetInputFile().getObjectContent(), 
                    getTargetClass(),
                    new PageReadListener<CountryMkuTranslationImportExcelDTO>(dataList -> {})
                ).sheet(0).headRowNumber(1).autoTrim(Boolean.TRUE).doReadSync();
                
                this.validate(target);
                task.setTarget(target);
            }
        }
    }
    
    @Override
    public void validate(List<CountryMkuTranslationImportExcelDTO> target) {
        if (CollectionUtils.isEmpty(target)) {
            return;
        }
        
        for (CountryMkuTranslationImportExcelDTO excelDTO : target) {
            // 参数校验
            ValidateResult<CountryMkuTranslationImportExcelDTO> validateResult = ValidationUtil.checkParam(excelDTO);
            if (Boolean.FALSE.equals(validateResult.getSuccess())) {
                excelDTO.setValid(Boolean.FALSE);
                List<String> messageList = validateResult.getPropertyErrors().stream()
                    .filter(Objects::nonNull)
                    .map(PropertyError::getMessage)
                    .collect(Collectors.toList());
                excelDTO.setResult(String.join(", ", messageList));
                continue;
            }
            
            // 业务校验
            try {
                // 校验必填字段
                if (excelDTO.getMkuId() == null) {
                    excelDTO.setValid(Boolean.FALSE);
                    excelDTO.setResult("MKU ID不能为空");
                    continue;
                }
                
                if (StringUtils.isBlank(excelDTO.getTargetCountryCode())) {
                    excelDTO.setValid(Boolean.FALSE);
                    excelDTO.setResult("国家代码不能为空");
                    continue;
                }
                
                if (StringUtils.isBlank(excelDTO.getTargetLang())) {
                    excelDTO.setValid(Boolean.FALSE);
                    excelDTO.setResult("目标语言不能为空");
                    continue;
                }
                
                // 校验至少有一个标题不为空
                if (StringUtils.isBlank(excelDTO.getConfirmedMkuTitle()) && StringUtils.isBlank(excelDTO.getConfirmedSpuTitle())) {
                    excelDTO.setValid(Boolean.FALSE);
                    excelDTO.setResult("确认后的MKU标题和SPU标题至少填写一个");
                    continue;
                }
                
                excelDTO.setValid(Boolean.TRUE);
                
            } catch (Exception e) {
                log.error("CountryMkuTranslationImportHandler.validate 业务校验异常: mkuId={}", excelDTO.getMkuId(), e);
                excelDTO.setValid(Boolean.FALSE);
                excelDTO.setResult("校验异常: " + e.getMessage());
            }
        }
    }
    
    @Override
    public void run(TaskDTO<CountryMkuTranslationImportExcelDTO> task) {
        List<CountryMkuTranslationImportExcelDTO> target = task.getTarget();
        if (CollectionUtils.isEmpty(target)) {
            return;
        }
        
        // 过滤出有效数据
        List<CountryMkuTranslationImportExcelDTO> validData = target.stream()
            .filter(dto -> Boolean.TRUE.equals(dto.getValid()))
            .collect(Collectors.toList());
        
        if (CollectionUtils.isNotEmpty(validData)) {
            try {
                // 转换为ImportRequest对象
                List<CountryMkuTranslationVO.ImportRequest> requestList = new ArrayList<>();
                for (CountryMkuTranslationImportExcelDTO dto : validData) {
                    CountryMkuTranslationVO.ImportRequest request = new CountryMkuTranslationVO.ImportRequest();
                    request.setMkuId(dto.getMkuId());
                    request.setSpuId(dto.getSpuId());
                    request.setTargetCountryCode(dto.getTargetCountryCode());
                    request.setTargetLang(dto.getTargetLang());
                    request.setConfirmedMkuTitle(dto.getConfirmedMkuTitle());
                    request.setConfirmedSpuTitle(dto.getConfirmedSpuTitle());
                    request.setRemark(dto.getRemark());
                    requestList.add(request);
                }
                
                // 批量更新翻译确认数据
                countryMkuManageService.batchUpdateTranslationConfirm(requestList);
                log.info("CountryMkuTranslationImportHandler.run 批量更新完成: taskId={}, count={}", task.getTaskId(), validData.size());
            } catch (Exception e) {
                log.error("CountryMkuTranslationImportHandler.run 批量更新异常: taskId={}", task.getTaskId(), e);
                throw e;
            }
        }
    }
}
