package com.jdi.isc.product.center.service.manage.supplier;

import com.jdi.isc.product.center.domain.enums.supplier.SupplierModuleEnum;
import com.jdi.isc.product.center.domain.supplier.biz.SupplierModifyRecordVO;
import com.jdi.isc.product.center.domain.supplier.biz.SupplierWarehouseVO;
import com.jdi.isc.product.center.domain.supplier.po.SupplierAccountPO;
import com.jdi.isc.product.center.domain.supplier.po.SupplierModifyRecordPO;
import com.jdi.isc.product.center.domain.supplier.po.SupplierWarehousePO;

import java.util.List;
import java.util.Set;

/**
 * @Description: 供应商信息修改记录数据维护服务
 * @Author: zhaojianguo21
 * @Date: 2024/03/20 21:29
 **/

public interface SupplierWarehouseManageService {


    List<SupplierModifyRecordVO> makeupDiffList(List<SupplierWarehouseVO> warehouses, String batchNum, String supplierCode);

    boolean batchSaveOrUpdate(List<SupplierWarehouseVO> warehouses);

    List<SupplierWarehouseVO> makeupDiffView(List<SupplierModifyRecordPO> wareHouseList);

    /**
     * 根据供应商编码查询对应的仓库信息列表
     * @param supplierCode 供应商编码
     * @return 供应商对应的仓库信息列表，可能为空列表但不会为null
     */
    List<SupplierWarehouseVO> queryBySupplierCode(String supplierCode);

    boolean batchAdd(List<SupplierWarehouseVO> batchSave);

    boolean batchDel(Set<Long> delIds, String optPin, long optTimestamp);
}
