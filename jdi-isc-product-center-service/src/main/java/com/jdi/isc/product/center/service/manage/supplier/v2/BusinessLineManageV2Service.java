package com.jdi.isc.product.center.service.manage.supplier.v2;


import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.supplier.req.BusinessLineEditReq;
import com.jdi.isc.product.center.domain.supplier.req.BusinessLineReq;
import com.jdi.isc.product.center.domain.supplier.resp.BusinessLineRelationResp;

/**
    * @description: 产品线管理类
    * @author: zhangjin176
    * @date: 2025/5/6 13:22
    * @version: 2.0
    **/
public interface BusinessLineManageV2Service {



    /**
     * 根据业务线请求对象获取业务线关系列表
     *
     * @param lineReq 业务线请求对象
     * @return 业务线关系列表
     */
    PageInfo<BusinessLineRelationResp> list(BusinessLineReq lineReq);


    /**
     * 处理业务线编辑请求并返回操作结果。
     *
     * @param req      业务线编辑请求对象，包含编辑的具体信息。
     * @param operator 操作人姓名，用于记录操作日志。
     * @return 操作结果的唯一标识符。
     */
    String handleBusinessLineChange(BusinessLineEditReq req, String operator);

    /**
     * 判断指定的供应商、品牌和四级分类是否存在。
     * @param supplierCode 供应商代码
     * @param brandId 品牌ID
     * @param catId 四级分类ID
     * @return true 如果存在，false 否则
     */
    boolean isExist(String supplierCode, String brandId, String catId);
}
