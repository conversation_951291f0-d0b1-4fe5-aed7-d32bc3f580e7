package com.jdi.isc.product.center.service.manage.supplier.v2;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdi.isc.product.center.domain.supplier.po.BusinessLineRelationPO;

import java.util.List;

/**
 * @description: 产品线品牌和类目管理类
 * @author: zhangjin176
 * @date: 2025/5/6 13:22
 * @version: 2.0
 **/
public interface BusinessLineRelationManageV2Service {


    /**
     * 分页查询供应商的业务线关系信息。
     *
     * @param supplierCode 供应商编码
     * @param brandId      品牌ID
     * @param catIds       类目ID列表
     * @param agentLevel   代理等级
     * @param index        分页索引
     * @param size         分页大小
     * @return 业务线关系列表
     */
    Page<BusinessLineRelationPO> page(String supplierCode, Long brandId, List<Long> catIds, Integer agentLevel, Integer index, Integer size);

    /**
     * 根据供应商代码获取有效业务线关系列表
     * getId,getSupplierCode,
     * BusinessLineRelationPO::getId,getSupplierCode,getBrandId,getCategoryId,getAgentLevel,getAuthorizeUrl,getTrademarkCertificateUrl
     *
     * @param supplierCode 供应商代码
     * @return 业务线关系列表
     */
    List<BusinessLineRelationPO> getSelectList(String supplierCode);

    /**
     * 根据供应商代码、品牌ID和类别4ID统计商品数量。
     *
     * @param supplierCode 供应商代码。
     * @param brandId      品牌ID。
     * @param cate4Id      类别4ID。
     * @return 统计的商品数量。
     */
    Long count(String supplierCode, String brandId, String cate4Id);
}
