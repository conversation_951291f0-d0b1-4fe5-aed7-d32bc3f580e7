package com.jdi.isc.product.center.service.worker.task;

import cn.hutool.core.annotation.AnnotationUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.jdi.isc.product.center.domain.delivery.biz.OrderDeliveryPdfVO;
import com.jdi.isc.product.center.domain.delivery.biz.OrderDeliveryTaskVO;
import com.jdi.isc.product.center.domain.delivery.po.OrderDeliveryTaskPO;
import com.jdi.isc.product.center.domain.task.dto.TaskDTO;
import com.jdi.isc.product.center.service.manage.delivery.OrderDeliveryPdfService;
import com.jdi.isc.product.center.service.manage.delivery.OrderDeliveryTaskService;
import com.jdi.isc.product.center.service.manage.task.TaskMangeService;
import com.jdi.isc.product.center.service.worker.task.frame.BaseJob;
import com.jdi.isc.product.center.service.worker.task.frame.JobExecutor;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

import static com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum.*;

/**
 * 任务中心任务调度派发
 * <AUTHOR>
 * @date 20231127
 */
@Component
@Slf4j
public class TaskCenterDispatcher {

    @Resource
    private TaskMangeService taskMangeService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private OrderDeliveryPdfService<OrderDeliveryPdfVO,OrderDeliveryTaskPO> orderDeliveryPdfService;
    @Resource
    private OrderDeliveryTaskService orderDeliveryTaskService;

    @SneakyThrows
    @XxlJob("iscWorker")
    public ReturnT<String> iscWorker(String param) {
        TaskDTO task = taskMangeService.pullTask();
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("iscWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("importFinanceOrderWorker")
    public ReturnT<String> importFinanceOrderWorker(String param) {
        log.info("TaskCenterDispatcher.importFinanceOrderWorker 核算加价拉取任务,入参type={}",ORDER_FINANCE_BATCH_PUBLISH);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(ORDER_FINANCE_BATCH_PUBLISH));
        log.info("TaskCenterDispatcher.importFinanceOrderWorker 核算加价拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importFinanceOrder 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("exportCustomerMkuWorker")
    public ReturnT<String> exportCustomerMkuWorker(String param) {
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(MKU_CUSTOMER_REF_BATCH_DOWNLOAD));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.out(task).start();
                log.info("exportCustomerMkuWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("importVnProductWorker")
    public ReturnT<String> importVnProductWorker(String param) {
        log.info("TaskCenterDispatcher.importVnProductWorker 越南批量发品拉取任务,入参type={}",VN_PRODUCT_BATCH_PUBLISH);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(VN_PRODUCT_BATCH_PUBLISH,VN_PRODUCT_ALL_CATEGORY_BATCH_PUBLISH));
        log.info("TaskCenterDispatcher.importVnProductWorker 越南批量发品拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importVnProductWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("importThProductWorker")
    public ReturnT<String> importThProductWorker(String param) {
        log.info("TaskCenterDispatcher.importThProductWorker 泰国批量发品拉取任务,入参type={}",TH_PRODUCT_BATCH_PUBLISH);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(TH_PRODUCT_BATCH_PUBLISH,TH_PRODUCT_ALL_CATEGORY_BATCH_PUBLISH));
        log.info("TaskCenterDispatcher.importThProductWorker 泰国批量发品拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importThProductWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("deliveryPrintWorker")
    public ReturnT<String> deliveryPrintWorker(String param) {
        OrderDeliveryTaskPO task = orderDeliveryTaskService.pullTask(null);
        if(task!=null){
            //置任务为处理中
            OrderDeliveryTaskVO target = new OrderDeliveryTaskVO(task,1);
            try{
                Boolean res = orderDeliveryTaskService.update(target);
                if(res){
                    String url = orderDeliveryPdfService.of(task).start();
                    if(StringUtils.isBlank(url)){
                        throw new RuntimeException("TaskCenterDispatcher.deliveryPrintWorker error" + JSON.toJSONString(task));
                    }
                    target.success(url);
                    orderDeliveryTaskService.update(target);
                }
            }catch(Exception e){
                orderDeliveryTaskService.err(target,e);
            }
        }
        return ReturnT.SUCCESS;
    }


    @SneakyThrows
    @XxlJob("importMkuMaterialWorker")
    public ReturnT<String> importMkuMaterialWorker(String param) {
        log.info("TaskCenterDispatcher.importMkuMaterialWorker mku物料管理批量新增、修改/删除任务,入参type={}",MKU_MATERIAL_BATCH_PUBLISH);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(MKU_MATERIAL_BATCH_PUBLISH,MKU_MATERIAL_BATCH_DELETE));
        log.info("TaskCenterDispatcher.importMkuMaterialWorker mku物料管理批量新增、修改/删除任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importMkuMaterialWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    @XxlJob("importMkuMaterialExportWorker")
    public ReturnT<String> importMkuMaterialExportWorker(String param) {
        log.info("TaskCenterDispatcher.importMkuMaterialExportWorker mku物料管理批量导出任务,入参type={}",MKU_MATERIAL_BATCH_EXPORT);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(MKU_MATERIAL_BATCH_EXPORT));
        log.info("TaskCenterDispatcher.importMkuMaterialExportWorker mku物料管理批量导出任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.out(task).start();
                log.info("importMkuMaterialExportWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    //获取执行器
    public BaseJob getExecutor(String bizType){
        TaskBizTypeEnum taskBizType = TaskBizTypeEnum.valueOf(bizType);
        Map<String, Object> beanMap = applicationContext.getBeansWithAnnotation(JobExecutor.class);
        if(beanMap.isEmpty()){
            return null;
        }
        log.info("TaskDispatcher.getExecutor class list:{}" , beanMap.keySet());
        for (Map.Entry<String,Object> entry : beanMap.entrySet()) {
            try {
                JobExecutor ano = AnnotationUtil.getAnnotation(entry.getValue().getClass(), JobExecutor.class);
                if(taskBizType.equals(ano.taskBizType()) && entry.getValue() instanceof BaseJob){
                    log.info("TaskDispatcher.getExecutor 当前任务:{}命中执行策略job:{}" , taskBizType, entry.getValue());
                    return (BaseJob) entry.getValue();
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        return null;
    }

    @SneakyThrows
    @XxlJob("importBusinessLineWorker")
    public ReturnT<String> importBusinessLineWorker(String param) {
        log.info("TaskCenterDispatcher.importBusinessLineWorker 批量维护产品线拉取任务,入参type={}",BUSINESS_LINE_BATCH_APPEND);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(BUSINESS_LINE_BATCH_APPEND));
        log.info("TaskCenterDispatcher.importBusinessLineWorker 批量维护产品线拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importBusinessLineWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    // ========
    /**
     * 批量修改 SKU 客制化价格。
     * @param param 任务参数。
     * @return 执行结果。
     */
    @SneakyThrows
    @XxlJob("importUpdateSkuCustomPriceWorker")
    public ReturnT<String> importUpdateSkuCustomPriceWorker(String param) {
        log.info("TaskCenterDispatcher.importUpdateSkuCustomPriceWorker SKU客制化价格批量修改拉取任务,入参type={}",SKU_CUSTOM_PRICE_BATCH_MODIFY);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(SKU_CUSTOM_PRICE_BATCH_MODIFY));
        log.info("TaskCenterDispatcher.importUpdateSkuCustomPriceWorker SKU客制化价格批量修改拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importUpdateSkuCustomPriceWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 批量导入客户并绑定MKU的工作流任务。
     * @param param 工作流任务参数。
     * @return 如果任务执行成功，返回ReturnT对象，状态为SUCCESS。
     */
    @SneakyThrows
    @XxlJob("importMkuCustomerBatchBindWorker")
    public ReturnT<String> importMkuCustomerBatchBindWorker(String param) {
        log.info("TaskCenterDispatcher.importMkuCustomerBatchBindWorker 客户绑定MKU拉取任务,入参type={}",MKU_CUSTOMER_BATCH_BIND);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(MKU_CUSTOMER_BATCH_BIND));
        log.info("TaskCenterDispatcher.importMkuCustomerBatchBindWorker 客户绑定MKU拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importMkuCustomerBatchBindWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 批量更新泰国商品关税率拉取任务。
     * @param param 任务参数。
     */
    @SneakyThrows
    @XxlJob("importThSkuTaxBatchModifyWorker")
    public ReturnT<String> importThSkuTaxBatchModifyWorker(String param) {
        log.info("TaskCenterDispatcher.importThSkuTaxBatchModifyWorker 批量更新泰国商品关税率拉取任务,入参type={}",TH_SKU_TAX_BATCH_MODIFY);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(TH_SKU_TAX_BATCH_MODIFY));
        log.info("TaskCenterDispatcher.importThSkuTaxBatchModifyWorker 批量更新泰国商品关税率拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importThSkuTaxBatchModifyWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 批量更新SKU基本信息拉取任务
     * @param param 任务参数
     * @return 执行结果
     */
    @SneakyThrows
    @XxlJob("importSkuTaxBatchModifyWorker")
    public ReturnT<String> importSkuTaxBatchModifyWorker(String param) {
        log.info("TaskCenterDispatcher.importSkuTaxBatchModifyWorker 批量更新SKU基本信息拉取任务,入参type={}",SKU_BASE_BATCH_MODIFY);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(SKU_BASE_BATCH_MODIFY));
        log.info("TaskCenterDispatcher.importSkuTaxBatchModifyWorker 批量更新SKU基本信息拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importSkuTaxBatchModifyWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }




    /**
     * 批量计算SKU客户未税销售价并更新。
     * @param param 任务参数。
     * @return 执行结果。
     */
    @SneakyThrows
    @XxlJob("importSkuPriceCustomerBatchWorker")
    public ReturnT<String> importSkuPriceCustomerBatchWorker(String param) {
        log.info("TaskCenterDispatcher.importSkuPriceCustomerBatchWorker 批量计算SKU客户未税销售价拉取任务,入参type={}",SKU_PRICE_CUSTOMER_TOOL_BATCH);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(SKU_PRICE_CUSTOMER_TOOL_BATCH));
        log.info("TaskCenterDispatcher.importSkuPriceCustomerBatchWorker 批量更新SKU基本信息拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importSkuPriceCustomerBatchWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 批量计算SKU公域面价拉取任务。
     * @param param 任务参数。
     */
    @SneakyThrows
    @XxlJob("importSkuPriceToolBatchWorker")
    public ReturnT<String> importSkuPriceToolBatchWorker(String param) {
        log.info("TaskCenterDispatcher.importSkuPriceToolBatchWorker 批量计算SKU公域面价拉取任务,入参type={}",SKU_PRICE_TOOL_BATCH);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(SKU_PRICE_TOOL_BATCH));
        log.info("TaskCenterDispatcher.importSkuPriceToolBatchWorker 批量计算SKU公域面价拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importSkuPriceToolBatchWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 批量设置客户SKU未税价拉取任务
     * @param param 任务参数
     * @return 执行结果
     */
    @SneakyThrows
    @XxlJob("importSkuExcludeTaxPriceBatchWorker")
    public ReturnT<String> importSkuExcludeTaxPriceBatchWorker(String param) {
        log.info("TaskCenterDispatcher.importSkuExcludeTaxPriceBatchWorker 批量设置客户SKU未税价拉取任务,入参type={}",SKU_EXCLUDE_TAX_PRICE_BATCH);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(SKU_EXCLUDE_TAX_PRICE_BATCH));
        log.info("TaskCenterDispatcher.importSkuExcludeTaxPriceBatchWorker 批量设置客户SKU未税价拉取任务,出参={}",JSON.toJSONString(task));
        if(task!=null){
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if(null!=executor){
                executor.in(task).start();
                log.info("importSkuExcludeTaxPriceBatchWorker 执行完毕:{} " , JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 国家池翻译确认导出任务
     * @param param 任务参数
     * @return 执行结果
     */
    @SneakyThrows
    @XxlJob("countryPoolTranslationExportWorker")
    public ReturnT<String> countryPoolTranslationExportWorker(String param) {
        log.info("TaskCenterDispatcher.countryPoolTranslationExportWorker 国家池翻译确认导出任务,入参type={}", COUNTRY_POOL_TRANSLATION_EXPORT);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(COUNTRY_POOL_TRANSLATION_EXPORT));
        log.info("TaskCenterDispatcher.countryPoolTranslationExportWorker 国家池翻译确认导出任务,出参={}", JSON.toJSONString(task));
        if (task != null) {
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if (null != executor) {
                executor.out(task).start();
                log.info("countryPoolTranslationExportWorker 执行完毕: {}", JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 国家池翻译确认导入任务
     * @param param 任务参数
     * @return 执行结果
     */
    @SneakyThrows
    @XxlJob("countryPoolTranslationImportWorker")
    public ReturnT<String> countryPoolTranslationImportWorker(String param) {
        log.info("TaskCenterDispatcher.countryPoolTranslationImportWorker 国家池翻译确认导入任务,入参type={}", COUNTRY_POOL_TRANSLATION_IMPORT);
        TaskDTO task = taskMangeService.pullTask(Sets.newHashSet(COUNTRY_POOL_TRANSLATION_IMPORT));
        log.info("TaskCenterDispatcher.countryPoolTranslationImportWorker 国家池翻译确认导入任务,出参={}", JSON.toJSONString(task));
        if (task != null) {
            BaseJob executor = getExecutor(task.getTask().getBizType());
            if (null != executor) {
                executor.in(task).start();
                log.info("countryPoolTranslationImportWorker 执行完毕: {}", JSON.toJSONString(task));
            }
        }
        return ReturnT.SUCCESS;
    }

}
