package com.jdi.isc.product.center.service.manage.countryMku.impl;

import com.jd.common.web.LoginContext;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.common.costants.Constant;
import com.jdi.isc.product.center.common.frame.LangContextHolder;
import com.jdi.isc.product.center.domain.countryMku.*;
import com.jdi.isc.product.center.domain.sku.biz.SkuCountryVO;
import com.jdi.isc.product.center.rpc.countryMku.CountryMkuRpcService;
import com.jdi.isc.product.center.service.manage.countryMku.CountryMkuManageService;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.spu.req.SpuLangApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description: 商品国家池表数据维护服务实现
 * @Author: wangpeng965
 * @Date: 2024/12/02 21:49
 **/

@Slf4j
@Service
public class CountryMkuManageServiceImpl implements CountryMkuManageService {

    @Resource
    private CountryMkuRpcService countryMkuRpcService;


    @Override
    public PageInfo<CountryMkuPageVO.Response> pageSearch(CountryMkuPageVO.Request input) {
        return countryMkuRpcService.pageSearch(input);
    }

    @Override
    public List<CountryMkuPageVO.Request> auditStatusNum(CountryMkuPageVO.Request input) {
        return countryMkuRpcService.auditStatusNum(input);
    }

    @Override
    public DataResponse<String> batchBlack(CountryMkuApproveVO input) {
        return countryMkuRpcService.batchBlack(input);
    }

    @Override
    public DataResponse<String> batchOutBlack(CountryMkuApproveVO input) {
        return countryMkuRpcService.batchOutBlack(input);
    }

    @Override
    public DataResponse<String> batchPool(CountryMkuApproveVO input) {
        return countryMkuRpcService.batchPool(input);
    }

    @Override
    public DataResponse<CountryMkuCheckVO> checkBlackData(CountryMkuCheckReqVO input) {
        return countryMkuRpcService.checkBlackData(input);
    }

    @Override
    public DataResponse<CountryMkuCheckVO> checkOutBlackData(CountryMkuCheckReqVO input) {
        return countryMkuRpcService.checkOutBlackData(input);
    }

    @Override
    public DataResponse<Boolean> refreshCountryMku(CountryMkuRefreshVO vo) {
        vo.setPin(LoginContext.getLoginContext().getPin());
        vo.setLang(LangContextHolder.get());
        return countryMkuRpcService.refreshCountryMku(vo);
    }

    @Override
    public DataResponse<String> updateMkuStatus(CountryMkuUpdateStatusVO vo){
        if(StringUtils.isBlank(vo.getDownReason())){
            vo.setDownReason("-");
        }
        return countryMkuRpcService.updateMkuStatus(vo);
    }

    @Override
    public DataResponse<List<SpuLangApiDTO>> getMkuSpuTitleLang(Long mkuId, String targetCountryCode) {
        return countryMkuRpcService.getMkuSpuTitleLang(mkuId, targetCountryCode);
    }

    @Override
    public DataResponse<Boolean> updateMkuSpuTitleLangAndApprovalTranslation(CountryMkuTranslationVO.ImportRequest request) {
        return countryMkuRpcService.updateMkuSpuTitleLangAndApprovalTranslation(request);
    }

}
