package com.jdi.isc.product.center.service.adapter.mapstruct.invoice;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.finance.customerInvoice.req.*;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceApplyDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceOrderResDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceQualificationTemplateRes;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerPreInvoiceDetailResDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceQualificationRes;
import com.jdi.isc.product.center.domain.finance.invoice.*;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 发票对象转换
 * <AUTHOR>
 * @date 2025/01/03
 */
@Mapper
public interface InvoiceConvert {

    InvoiceConvert INSTANCE = Mappers.getMapper(InvoiceConvert.class);

    /**
     * 将前端请求DTO转换为外部API请求DTO
     * @param reqDTO 前端请求DTO
     * @return 外部API请求DTO
     */
    @InheritConfiguration
    CustomerInvoiceOrderPageReqDTO convertToApiReq(InvoiceOrderPageReqDTO reqDTO);

    /**
     * 将外部API响应DTO转换为前端响应DTO
     * @param resDTO 外部API响应DTO
     * @return 前端响应DTO
     */
    @InheritConfiguration
    InvoiceOrderPageRspDTO convertFromApiRes(CustomerInvoiceOrderResDTO resDTO);

    /**
     * 将外部API响应DTO列表转换为前端响应DTO列表
     * @param resDTOList 外部API响应DTO列表
     * @return 前端响应DTO列表
     */
    @InheritConfiguration
    List<InvoiceOrderPageRspDTO> convertFromApiResList(List<CustomerInvoiceOrderResDTO> resDTOList);

    /**
     * 将外部API分页响应整体转换为前端分页响应
     * @param apiPageInfo 外部API分页响应
     * @return 前端分页响应
     */
    @InheritConfiguration
    PageInfo<InvoiceOrderPageRspDTO> convertFromApiPageInfo(PageInfo<CustomerInvoiceOrderResDTO> apiPageInfo);

    /**
     * 将外部API发票分页响应整体转换为前端分页响应
     * @param apiPageInfo 外部API发票分页响应
     * @return 前端发票分页响应
     */
    @InheritConfiguration
    PageInfo<InvoicePageRspDTO> convertFromInvoicePageApiPageInfo(PageInfo<CustomerInvoiceApplyDTO> apiPageInfo);

    /**
     * 将前端预开票明细请求DTO转换为外部API请求DTO
     * @param reqDTO 前端请求DTO
     * @return 外部API请求DTO
     */
    @InheritConfiguration
    CustomerPreInvoiceDetailReq convertToPreInvoiceDetailApiReq(PreInvoiceDetailReqDTO reqDTO);

    /**
     * 将外部API预开票明细响应DTO转换为前端响应DTO
     * @param resDTO 外部API响应DTO
     * @return 前端响应DTO
     */
    @InheritConfiguration
    PreInvoiceDetailRspDTO convertFromPreInvoiceDetailApiRes(CustomerPreInvoiceDetailResDTO resDTO);

    /**
     * 将前端保存发票请求DTO转换为外部API请求DTO
     * @param reqDTO 前端请求DTO
     * @return 外部API请求DTO
     */
    @InheritConfiguration
    CustomerInvoiceSaveReqDTO convertToSaveInvoiceApiReq(SaveInvoiceProReqDTO reqDTO);

    /**
     * 将前端发票分页请求DTO转换为外部API请求DTO
     * @param reqDTO 前端请求DTO
     * @return 外部API请求DTO
     */
    @InheritConfiguration
    CustomerInvoicePageReqDTO convertToInvoicePageApiReq(InvoicePageReqDTO reqDTO);

    /**
     * 将外部API发票分页响应DTO转换为前端响应DTO
     * @param resDTO 外部API响应DTO
     * @return 前端响应DTO
     */
    @InheritConfiguration
    InvoicePageRspDTO convertFromInvoicePageApiRes(CustomerInvoiceApplyDTO resDTO);

    /**
     * 将外部API发票分页响应DTO列表转换为前端响应DTO列表
     * @param resDTOList 外部API响应DTO列表
     * @return 前端响应DTO列表
     */
    @InheritConfiguration
    List<InvoicePageRspDTO> convertFromInvoicePageApiResList(List<CustomerInvoiceApplyDTO> resDTOList);

    /**
     * 将前端发票详情请求DTO转换为外部API请求DTO
     * @param reqDTO 前端请求DTO
     * @return 外部API请求DTO
     */
    @InheritConfiguration
    CustomerInvoiceDetailReqDTO convertToInvoiceDetailApiReq(InvoiceDetailReqDTO reqDTO);

    /**
     * 将外部API发票详情响应DTO转换为前端响应DTO
     * @param resDTO 外部API响应DTO
     * @return 前端响应DTO
     */
    @InheritConfiguration
    InvoiceDetailRspDTO convertFromInvoiceDetailApiRes(CustomerInvoiceApplyDTO resDTO);

    /**
     * 将前端发票冲红请求DTO转换为外部API请求DTO
     * @param reqDTO 前端请求DTO
     * @return 外部API请求DTO
     */
    @InheritConfiguration
    CustomerReverseInvoiceReqDTO convertToSaveReverseInvoiceApiReq(SaveReverseInvoiceReqDTO reqDTO);


    /**
     * 资质按照国家获取模版 res 2  dto
     * @param res 前端请求DTO
     * @return 外部API请求DTO
     */
    @InheritConfiguration
    List<GetQualificationTemplateListItemDTO> convertToQualificationTemplate(List<CustomerInvoiceQualificationTemplateRes> res);

    /**
     * 资质分页：前端请求DTO -> 外部分页请求DTO
     */
    @InheritConfiguration
    CustomerInvoiceQualificationPageReqDTO convertToQualificationPageApiReq(QualificationPageReqDTO reqDTO);

    /**
     * 资质分页：外部单条记录 -> 前端单条记录
     */
    @InheritConfiguration
    QualificationPageRspDTO convertToQualificationPageItem(CustomerInvoiceQualificationRes resDTO);

    /**
     * 资质分页：外部分页 -> 前端分页
     */
    @InheritConfiguration
    PageInfo<QualificationPageRspDTO> convertFromQualificationApiPageInfo(PageInfo<CustomerInvoiceQualificationRes> apiPageInfo);

    /**
     * 资质详情：外部响应 -> 前端响应
     */
    @InheritConfiguration
    QualificationDetailRspDTO convertFromQualificationDetailApiRes(CustomerInvoiceQualificationRes resDTO);

    /**
     * 保存资质：前端请求 -> 外部API请求
     */
    CustomerInvoiceQualificationSaveReqDTO convertToSaveQualificationApiReq(SaveInvoiceQualificationReqDTO reqDTO);

    /**
     * 编辑资质：前端请求 -> 外部API请求
     */
    CustomerInvoiceQualificationEditReqDTO convertToEditQualificationApiReq(EditInvoiceQualificationReqDTO reqDTO);
}
