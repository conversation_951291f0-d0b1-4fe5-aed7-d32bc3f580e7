package com.jdi.isc.product.center.service.adapter.mapstruct.hscode;

import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.soa.api.hsCode.id.biz.HsCodeIdApiDTO;
import com.jdi.isc.product.soa.api.hsCode.id.req.HsCodeIdSaveUpdateReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.id.req.HsCodeIdDetailReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.id.req.HsCodeIdPageReqApiDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface IdHsCodeConvert {
    IdHsCodeConvert INSTANCE = Mappers.getMapper(IdHsCodeConvert.class);
    
    /**
     * 将CountryHsCodeVO对象转换为HsCodeIdSaveUpdateReqApiDTO对象，用于API保存请求。
     * @param vo CountryHsCodeVO对象，包含HS Code的相关信息。
     * @return 转换后的HsCodeIdSaveUpdateReqApiDTO对象。
     */
    HsCodeIdSaveUpdateReqApiDTO voToApiSaveReq(CountryHsCodeVO vo);
    /**
     * 将 CountryHsCodePageRequest 对象转换为 HsCodeIdPageReqApiDTO 对象，用于 API 分页请求。
     * @param vo CountryHsCodePageRequest 对象，包含分页请求的必要信息。
     * @return 转换后的 HsCodeIdPageReqApiDTO 对象。
     */
    HsCodeIdPageReqApiDTO voToApiPageReq(CountryHsCodePageRequest vo);
    
    /**
     * 将 HsCodeIdApiDTO 列表转换为 CountryHsCodePageResponse Vo 列表。
     * @param respList HsCodeIdApiDTO 列表
     * @return 转换后的 CountryHsCodePageResponse Vo 列表
     */
    List<CountryHsCodePageResponse> pageResponseListToVoList(List<HsCodeIdApiDTO> respList);

    /**
     * 将 API DTO 转换为 CountryHsCodeVO 对象
     * @param data API DTO 数据对象
     * @return 转换后的 CountryHsCodeVO 对象
     */
    CountryHsCodeVO apiDtoToVo(HsCodeIdApiDTO data);
}