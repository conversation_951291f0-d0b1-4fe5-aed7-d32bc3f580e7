package com.jdi.isc.product.center.service.manage.price.impl;

import com.jd.common.web.LoginContext;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.biz.component.api.businessLog.req.BusinessLogPageReq;
import com.jdi.isc.product.center.common.costants.Constant;
import com.jdi.isc.product.center.common.frame.LangContextHolder;
import com.jdi.isc.product.center.domain.businessLog.BusinessLogResp;
import com.jdi.isc.product.center.domain.price.productprice.ProjectPriceUploadExcelVO;
import com.jdi.isc.product.center.rpc.apporder.ApproveOrderWriteRpcService;
import com.jdi.isc.product.center.rpc.businessLog.BusinessLogRpcService;
import com.jdi.isc.product.center.rpc.price.productprice.IscProductSoaProjectPriceReadRpcService;
import com.jdi.isc.product.center.rpc.price.productprice.IscProductSoaProjectPriceWriteRpcService;
import com.jdi.isc.product.center.service.adapter.mapstruct.businessLog.BusinessLogConvert;
import com.jdi.isc.product.center.service.manage.price.ProjectPriceApplyManageService;
import com.jdi.isc.product.center.service.support.BaseReqDTOHelper;
import com.jdi.isc.product.soa.api.common.BaseDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiReqDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiResDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceApplyAuditApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceApplyForBuyerUpsertApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceApplyForProductUpsertApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceApplyImportApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.res.ProjectPriceApplyApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.res.ProjectPriceApplyPageApiDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * The type Product price apply manage service.
 *
 * <AUTHOR>
 */
@Service
public class ProjectPriceApplyManageServiceImpl implements ProjectPriceApplyManageService {

    @Resource
    private IscProductSoaProjectPriceWriteRpcService iscProductSoaProjectPriceWriteRpcService;
    @Resource
    private IscProductSoaProjectPriceReadRpcService iscProductSoaProjectPriceReadRpcService;
    @Resource
    private BusinessLogRpcService businessLogRpcService;

    @Resource
    private ApproveOrderWriteRpcService approveOrderWriteRpcService;


    @Override
    public Long uploadExcel(ProjectPriceUploadExcelVO param) {
        ProjectPriceApplyImportApiDTO input = new ProjectPriceApplyImportApiDTO();
        input.setBizType(param.getBizType());
        input.setFileUrl(param.getFileUrl());
        input.setProjectCode(param.getProjectCode());

//        input.setClientCode();
//        input.setStationType();

        input.setPin(LoginContext.getLoginContext().getPin());
        input.setSystemCode(Constant.SYSTEM_CODE);
        input.setLang(LangContextHolder.get());

        return iscProductSoaProjectPriceWriteRpcService.uploadExcel(input);
    }

    @Override
    public PageInfo<BusinessLogResp> queryLog(BusinessLogPageReq input) {
        return BusinessLogConvert.INSTANCE.apiPage2Page(businessLogRpcService.queryPage(input));
    }

    @Override
    public PageInfo<ProjectPriceApplyPageApiDTO.Response> pageSearch(ProjectPriceApplyPageApiDTO.Request input) {
        input.setPin(LoginContext.getLoginContext().getPin());
        input.setSystemCode(Constant.SYSTEM_CODE);
        input.setLang(LangContextHolder.get());
        return iscProductSoaProjectPriceReadRpcService.pageSearch(input);
    }

    @Override
    public ProjectPriceApplyApiDTO detail(BaseDTO input) {
        input.setPin(LoginContext.getLoginContext().getPin());
        input.setSystemCode(Constant.SYSTEM_CODE);
        input.setLang(LangContextHolder.get());
        return iscProductSoaProjectPriceReadRpcService.detail(input);
    }

    @Override
    public Boolean saveOrUpdate(ProjectPriceApplyForProductUpsertApiDTO input) {
        input.setPin(LoginContext.getLoginContext().getPin());
        input.setSystemCode(Constant.SYSTEM_CODE);
        input.setLang(LangContextHolder.get());
        return iscProductSoaProjectPriceWriteRpcService.saveOrUpdate(input);
    }

    @Override
    public Boolean productRevoke(ProjectPriceApplyAuditApiDTO input) {
        input.setPin(LoginContext.getLoginContext().getPin());
        input.setSystemCode(Constant.SYSTEM_CODE);
        input.setLang(LangContextHolder.get());
        return iscProductSoaProjectPriceWriteRpcService.productRevoke(input);
    }

    @Override
    public Boolean saveOrUpdateAndSubmit(ProjectPriceApplyForBuyerUpsertApiDTO input) {
        input.setPin(LoginContext.getLoginContext().getPin());
        input.setSystemCode(Constant.SYSTEM_CODE);
        input.setLang(LangContextHolder.get());
        return iscProductSoaProjectPriceWriteRpcService.saveOrUpdateAndSubmit(input);
    }

    @Override
    public Boolean buyerReject(ProjectPriceApplyAuditApiDTO input) {
        input.setPin(LoginContext.getLoginContext().getPin());
        input.setSystemCode(Constant.SYSTEM_CODE);
        input.setLang(LangContextHolder.get());
        return iscProductSoaProjectPriceWriteRpcService.buyerReject(input);
    }

    @Override
    public DataResponse<AuditApiResDTO> auditOrder(AuditApiReqDTO input) {
        // 设置操作人信息
        BaseReqDTOHelper.setOperatorInfo(input);
        AuditApiResDTO res = approveOrderWriteRpcService.audit(input);
        return DataResponse.success(res);
    }
}
