package com.jdi.isc.product.center.service.adapter.mapstruct.hscode;

import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.domain.hscode.vn.VnHsCodeDetailRequest;
import com.jdi.isc.product.center.domain.hscode.vn.VnHsCodeSaveOrUpdateRequest;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.soa.api.hsCode.vn.biz.HsCodeVnApiDTO;
import com.jdi.isc.product.soa.api.hsCode.vn.req.HsCodeVnSaveUpdateReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.vn.req.HsCodeVnDetailReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.vn.req.HsCodeVnPageReqApiDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface VnHsCodeConvert {
    VnHsCodeConvert INSTANCE = Mappers.getMapper(VnHsCodeConvert.class);
    HsCodeVnSaveUpdateReqApiDTO voToSaveRequest(CountryHsCodeVO vo);
    CountryHsCodeVO detailResponseToVo(HsCodeVnApiDTO resp);
    List<CountryHsCodePageResponse> pageResponseListToVoList(List<HsCodeVnApiDTO> respList);
    HsCodeVnPageReqApiDTO voToPageRequest(CountryHsCodePageRequest vo);
}