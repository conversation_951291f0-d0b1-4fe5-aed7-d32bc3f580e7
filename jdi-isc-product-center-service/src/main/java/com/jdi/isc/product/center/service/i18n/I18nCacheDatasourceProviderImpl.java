package com.jdi.isc.product.center.service.i18n;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.library.i18n.datasource.cache.I18nCacheDatasourceProvider;
import com.jdi.isc.product.soa.api.wimp.lang.IscProductSoaLangApiService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class I18nCacheDatasourceProviderImpl implements I18nCacheDatasourceProvider {

    @Resource
    private IscProductSoaLangApiService iscProductSoaLangApiService;

    @Override
    public Map<String, Map<String, String>> getI18nByJsf() {
        DataResponse<JSONObject> langLibraryByLang = iscProductSoaLangApiService.getLangLibraryByLang(null);
        if (langLibraryByLang != null && langLibraryByLang.getData() != null) {
            JSONObject jsonData = langLibraryByLang.getData();
            return jsonData.toJavaObject(new TypeReference<Map<String, Map<String, String>>>() {
            });
        }
        return Collections.emptyMap();
    }

    @Override
    public Map<String, String> getI18nByLang(String lang) {
        DataResponse<Map<String, String>> langMapByLang = iscProductSoaLangApiService.getLangMapByLang(lang);
        if (langMapByLang != null && langMapByLang.getData() != null) {
            return langMapByLang.getData();
        }
        return new HashMap<>();
    }

    @Override
    public List<String> supportI18nLangList() {
        DataResponse<List<String>> langCodeList = iscProductSoaLangApiService.getLangCodeList();
        if (langCodeList != null && langCodeList.getData() != null) {
            return langCodeList.getData();
        }
        return Collections.emptyList();
    }
}