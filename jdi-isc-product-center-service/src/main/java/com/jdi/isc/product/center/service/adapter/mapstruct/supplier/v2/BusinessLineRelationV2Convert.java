package com.jdi.isc.product.center.service.adapter.mapstruct.supplier.v2;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.enums.supplier.SupplierAgentLevelEnum;
import com.jdi.isc.product.center.domain.supplier.biz.BusinessLineVO;
import com.jdi.isc.product.center.domain.supplier.po.BusinessLineRelationPO;
import com.jdi.isc.product.center.domain.supplier.req.BusinessLineEditDTO;
import com.jdi.isc.product.center.domain.supplier.resp.BusinessLineRelationResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import static com.jdi.isc.product.center.domain.enums.supplier.SupplierModifyActionEnum.*;


/**
    * @description: 产品线实体类转换器
    * @author: zhangjin176
    * @date: 2025/5/10 00:02
    * @version:
    **/
@Mapper
public interface BusinessLineRelationV2Convert {
    BusinessLineRelationV2Convert INSTANCE = Mappers.getMapper(BusinessLineRelationV2Convert.class);


    /**
     * 根据分页信息获取业务线关系列表
     * @param page 分页信息
     * @return 包含业务线关系的分页信息
     */
    PageInfo<BusinessLineRelationResp> poPage2Resp(Page<BusinessLineRelationPO> page);
    /**
     * 将 BusinessLineRelationPO 转换为 BusinessLineRelationResp
     * @param po 业务线关系 PO
     * @return 业务线关系响应
     */
    @Mapping(source = "jdCatId", target = "categoryId")  // 映射 jdCatId 到 categoryId
    BusinessLineRelationResp poToResp(BusinessLineRelationPO po);
    /**
     * 将业务线编辑DTO转换为审计VO
     * @param dto 业务线编辑DTO
     * @return 业务线审计VO
     */
    @Mapping(source = "action", target = "action", qualifiedByName = "mapAction")
    @Mapping(source = "data.authorizeUrl", target = "authorizeUrl")
    @Mapping(source = "data.trademarkCertificateUrl", target = "trademarkCertificateUrl")
    @Mapping(source = "data.agentLevel", target = "agentLevel")
    @Mapping(source = "data.agentLevel", target = "agentLevelStr", qualifiedByName = "mapAgentLevel")
    @Mapping(source = "data.brandName", target = "brandName")
    @Mapping(source = "data.brandId", target = "brandId")
    @Mapping(source = "data.categoryId", target = "categoryId")
    @Mapping(source = "data.categoryId", target = "jdCatId")
    @Mapping(source = "data.categoryStr", target = "categoryAllPathStr")
    BusinessLineVO dto2Audit(BusinessLineEditDTO dto);

    @Named("mapAction")
    default Integer mapAction(String action) {
        switch (action.toLowerCase()) {
            case "add":
                return ADD.getCode();
            case "edit":
                return UPDATE.getCode();
            case "delete":
                return DEL.getCode();
            case "invalid":
            default:
                return INVALID.getCode();
        }
    }

    @Named("mapAgentLevel")
    default String mapAgentLevel(Integer agentLevel) {
        return SupplierAgentLevelEnum.forCode(agentLevel).getDesc();
    }
}
