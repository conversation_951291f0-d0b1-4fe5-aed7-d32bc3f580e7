package com.jdi.isc.product.center.service.adapter.mapstruct.hscode;

import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.domain.hscode.th.*;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.soa.api.hsCode.th.biz.HsCodeThApiDTO;
import com.jdi.isc.product.soa.api.hsCode.th.req.HsCodeThSaveUpdateReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.th.req.HsCodeThDetailReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.th.req.HsCodeThPageReqApiDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface ThHsCodeConvert {
    ThHsCodeConvert INSTANCE = Mappers.getMapper(ThHsCodeConvert.class);
    HsCodeThSaveUpdateReqApiDTO voToSaveRequest(CountryHsCodeVO vo);
    CountryHsCodeVO detailResponseToVo(HsCodeThApiDTO resp);
    List<CountryHsCodePageResponse> pageResponseListToVoList(List<HsCodeThApiDTO> respList);
    HsCodeThPageReqApiDTO voToPageRequest(CountryHsCodePageRequest vo);
}