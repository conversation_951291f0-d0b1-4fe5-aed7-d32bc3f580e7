package com.jdi.isc.product.center.service.worker.task.handler;

import com.jdi.isc.product.center.domain.task.excel.CustomerSkuPriceExcelDTO;
import com.jdi.isc.product.center.service.worker.task.frame.JobExecutor;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * The type New disposable sku customer price import handler.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@JobExecutor(taskBizType = TaskBizTypeEnum.SKU_CUSTOM_PRICE_BATCH_MODIFY)
public class NewDisposableSkuCustomerPriceImportHandler extends DisposableSkuCustomerPriceImportHandler<CustomerSkuPriceExcelDTO> {
    @Override
    public Class<CustomerSkuPriceExcelDTO> getTargetClass() {
        return CustomerSkuPriceExcelDTO.class;
    }

    @Override
    public String getJobName() {
        return TaskBizTypeEnum.SKU_CUSTOM_PRICE_BATCH_MODIFY.getName();
    }
}
