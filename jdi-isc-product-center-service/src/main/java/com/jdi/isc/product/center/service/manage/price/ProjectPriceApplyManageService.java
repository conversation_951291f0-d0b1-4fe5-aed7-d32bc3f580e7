package com.jdi.isc.product.center.service.manage.price;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.biz.component.api.businessLog.req.BusinessLogPageReq;
import com.jdi.isc.product.center.domain.businessLog.BusinessLogResp;
import com.jdi.isc.product.center.domain.price.productprice.ProjectPriceUploadExcelVO;
import com.jdi.isc.product.soa.api.common.BaseDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiReqDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiResDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceApplyAuditApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceApplyForBuyerUpsertApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceApplyForProductUpsertApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.res.ProjectPriceApplyApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.res.ProjectPriceApplyPageApiDTO;

import javax.validation.Valid;

/**
 * The type Product price apply manage service.
 *
 * <AUTHOR>
 */
public interface ProjectPriceApplyManageService {
    /**
     * 上传Excel文件并解析数据到项目价格表中
     * @param param 包含Excel文件信息的VO对象
     * @return 上传成功后返回的操作结果，类型为Long
     */
    Long uploadExcel(ProjectPriceUploadExcelVO param);

    /**
     * 查询业务日志分页信息。
     * @param input 业务日志分页请求对象，包含查询条件和分页信息。
     * @return 分页后的业务日志DTO列表。
     */
    PageInfo<BusinessLogResp> queryLog(BusinessLogPageReq input);

    /**
     * 分页查询项目价格申请信息。
     * @param input 查询请求参数。
     * @return 分页查询结果，包含项目价格申请信息列表和分页信息。
     */
    PageInfo<ProjectPriceApplyPageApiDTO.Response> pageSearch(ProjectPriceApplyPageApiDTO.Request input);

    /**
     * 获取项目价格申请详细信息
     * @param input 基础输入参数
     * @return 包含项目价格申请详细信息的 DataResponse 对象
     */
    ProjectPriceApplyApiDTO detail(BaseDTO input);

    /**
     * 保存或更新项目价格申请信息。
     * @param input 项目价格申请信息DTO对象。
     * @return 操作结果，true表示成功，false表示失败。
     */
    Boolean saveOrUpdate(ProjectPriceApplyForProductUpsertApiDTO input);

    /**
     * 撤销商品经理的价格申请审批
     * @param input 项目价格申请审批的DTO对象，包含了撤销操作所需的信息
     * @return 撤销操作的结果，true表示撤销成功，false表示撤销失败
     */
    Boolean productRevoke(ProjectPriceApplyAuditApiDTO input);

    /**
     * 保存或更新并提交项目价格申请-采销。
     * @param input 项目价格申请的更新或插入数据。
     * @return 保存或更新操作的结果。
     */
    Boolean saveOrUpdateAndSubmit(ProjectPriceApplyForBuyerUpsertApiDTO input);

    /**
     * 采销拒绝项目价格申请审核。
     * @param input 项目价格申请审核DTO对象，包含审核所需的信息。
     * @return Boolean类型的DataResponse对象，表示操作结果。
     */
    Boolean buyerReject(ProjectPriceApplyAuditApiDTO input);

    /**
     * 审核订单
     * @param input 审核请求DTO，包含订单信息和审核操作等
     * @return 审核结果DTO，包含审核状态、错误信息等
     */
    DataResponse<AuditApiResDTO> auditOrder(@Valid AuditApiReqDTO input);
}
