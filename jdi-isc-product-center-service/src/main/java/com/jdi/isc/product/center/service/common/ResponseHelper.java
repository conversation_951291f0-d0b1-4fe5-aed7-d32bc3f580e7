package com.jdi.isc.product.center.service.common;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.library.i18n.exception.BusinessException;
import com.jdi.isc.library.i18n.exception.BusinessFeatureException;
import com.jdi.isc.product.center.common.costants.JsfErrorConstant;
import com.jdi.isc.product.center.common.enums.response.SystemErrorEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.function.Function;
import java.util.function.Supplier;

import static com.jdi.isc.product.center.common.costants.ErrorAlarmConstant.ALARM_JSF;

/**
 * 响应处理工具类
 * 用于抽离重复的响应检查和错误处理逻辑
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@Slf4j
@Component
public class ResponseHelper {


    /**
     * 处理响应并转换数据（带错误码映射）
     *
     * @param <S>             源数据类型
     * @param <T>             目标数据类型
     * @param response        原始响应
     * @param converter       数据转换器
     * @param errorCodeMapper 错误码映射器
     * @param serviceName
     * @return 转换后的响应
     */
    public static <S, T> DataResponse<T> processResponse(DataResponse<S> response,
                                                         Function<S, T> converter,
                                                         ErrorCodeMapper errorCodeMapper, String serviceName) {
        // 类型安全的错误检查带映射
        if (!Boolean.TRUE.equals(response.getSuccess())) {
            String originalCode = response.getCode();
            String originalMessage = response.getMessage();
            String[] originalErrorParams = response.getErrorParams();
            ErrorInfo mappedError = errorCodeMapper.mapError(originalCode, originalMessage);
            log.error(ALARM_JSF + "响应检查失败: 原始code={}, 原始message={}, 原始拓展字段={}, 映射后code={}, 映射后message={}",
                    originalCode, originalMessage, originalErrorParams, mappedError.getCode(), mappedError.getMessage());
            return DataResponse.error(mappedError.getCode(), mappedError.getMessage(), originalErrorParams == null ? new String[]{serviceName} : originalErrorParams);
        }
        T convertedData = converter.apply(response.getData());
        return DataResponse.success(convertedData);
    }


    /**
     * 错误信息封装类
     */
    public static class ErrorInfo {
        private final String code;
        private final String message;

        public ErrorInfo(String code, String message) {
            this.code = code;
            this.message = message;
        }

        public String getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }
    }


    /**
     * 错误码映射器接口
     */
    public interface ErrorCodeMapper {
        /**
         * 映射错误码和消息
         *
         * @param originalCode    原始错误码
         * @param originalMessage 原始错误消息
         * @return 映射后的错误信息
         */
        ErrorInfo mapError(String originalCode, String originalMessage);
    }

    /**
     * 简化的错误码映射器接口
     * 只需要提供基础的错误码映射配置，判断逻辑由Helper处理
     */
    public interface SimpleErrorCodeMapper {
        /**
         * 获取错误码映射表
         * key: JSF返回的错误码
         * value: 自定义的错误信息
         *
         * @return 错误码映射表
         */
        Map<String, ErrorInfo> getErrorMappings();
    }


    /**
     * 创建错误码映射器 - 超级简化版本
     * 只需要传入映射信息和服务名，其他都自动处理
     *
     * @param errorMappings 错误码映射表 (JSF错误码 -> ErrorInfo)
     * @param serviceName   服务名称
     * @return 完整的错误码映射器
     */
    public static ErrorCodeMapper createErrorCodeMapper(Map<String, ErrorInfo> errorMappings, String serviceName) {
        return createErrorCodeMapper(() -> errorMappings, serviceName);
    }

    /**
     * 创建简化的错误码映射器
     * 包含通用的判断逻辑：系统级异常、堆栈异常检测等
     *
     * @param simpleMapper 简单映射器
     * @param serviceName  服务名称（用于日志和错误消息）
     * @return 完整的错误码映射器
     */
    public static ErrorCodeMapper createErrorCodeMapper(SimpleErrorCodeMapper simpleMapper, String serviceName) {
        return (originalCode, originalMessage) -> {

            // 1. 检查是否为英文堆栈异常（第二优先级）
            if (isStackTraceException(originalMessage)) {
                log.error(ALARM_JSF + " - 堆栈异常: service={}, code={}, stackTrace={}",
                        serviceName, originalCode, originalMessage);
                return new ErrorInfo(SystemErrorEnum.SYSTEM_ERROR.getCode(), SystemErrorEnum.SYSTEM_ERROR.getMessage());
            }

            // 2. 查找自定义映射表
            Map<String, ErrorInfo> mappings = simpleMapper.getErrorMappings();
            if (mappings != null && mappings.containsKey(originalCode)) {
                ErrorInfo mappedError = mappings.get(originalCode);
                log.error(ALARM_JSF + " - 业务异常: service={}, code={}, message={}",
                        serviceName, originalCode, originalMessage);
                return mappedError;
            }

            // 3. 未知错误的默认处理（最低优先级）
            log.error(ALARM_JSF + " - 未知错误: service={}, code={}, message={}",
                    serviceName, originalCode, originalMessage);
            return new ErrorInfo(
                    originalCode != null ? originalCode : SystemErrorEnum.UNKNOWN_ERROR.getCode(),
                    SystemErrorEnum.UNKNOWN_ERROR.getMessage()
            );
        };
    }


    /**
     * 判断是否为堆栈异常信息
     */
    private static boolean isStackTraceException(String message) {
        if (message == null || message.trim().isEmpty()) {
            return false;
        }

        String lowerMessage = message.toLowerCase();

        // 1. 包含Java包名路径
        if (lowerMessage.contains("java.") || lowerMessage.contains("javax.") ||
                lowerMessage.contains("com.") || lowerMessage.contains("org.")) {
            return true;
        }

        // 2. 包含常见异常类名
        if (lowerMessage.contains("exception") || lowerMessage.contains("error") ||
                lowerMessage.contains("throwable") || lowerMessage.contains("stacktrace")) {
            return true;
        }

        // 3. 包含堆栈跟踪的典型格式
        if (lowerMessage.contains("at ") || lowerMessage.contains("caused by") ||
                lowerMessage.contains("nested exception") || lowerMessage.contains("root cause")) {
            return true;
        }

        // 4. 消息过长且包含多行（通常堆栈信息会很长）
        if (message.length() > 200 && (message.contains("\n") || message.contains("\\n"))) {
            return true;
        }

        return false;
    }

    /**
     * 安全调用JSF服务并处理异常（带数据转换）
     *
     * @param jsfCall         JSF服务调用函数
     * @param converter       数据转换器
     * @param errorCodeMapper 错误码映射器
     * @param serviceName     服务名称
     * @param <S>             源数据类型
     * @param <T>             目标数据类型
     * @return 处理后的响应
     */
    public static <S, T> DataResponse<T> safeJsfCall(Supplier<DataResponse<S>> jsfCall,
                                                     Function<S, T> converter,
                                                     ErrorCodeMapper errorCodeMapper,
                                                     String serviceName) {
        try {
            DataResponse<S> response = jsfCall.get();
            return processResponse(response, converter, errorCodeMapper,serviceName);
        } catch (Exception e) {
            return handleJsfException(e, serviceName);
        }
    }

    /**
     * 处理JSF调用异常
     * 识别常见的JSF异常类型并返回友好的错误信息
     *
     * @param e           异常对象
     * @param serviceName 服务名称
     * @param <T>         返回数据类型
     * @return 错误响应
     */
    private static <T> DataResponse<T> handleJsfException(Exception e,
                                                          String serviceName) {
        String code = SystemErrorEnum.CALL_ERROR.getCode();
        String msg = SystemErrorEnum.CALL_ERROR.getMessage();
        String[] errorParams = null;
        // 记录异常日志
        log.error(ALARM_JSF+": service={}, exception={}", serviceName, e.getMessage(), e);

        // 根据异常类型返回不同的错误信息
        if (e instanceof java.net.ConnectException || e instanceof java.net.NoRouteToHostException) {
            code = SystemErrorEnum.CONNECTION_ERROR.getCode();
            msg = SystemErrorEnum.CONNECTION_ERROR.getMessage();
        } else if (e instanceof java.net.SocketTimeoutException) {
            code = SystemErrorEnum.TIMEOUT_ERROR.getCode();
            msg = SystemErrorEnum.TIMEOUT_ERROR.getMessage();
        } else if (e instanceof java.rmi.ConnectException || e instanceof java.rmi.ConnectIOException) {
            code = SystemErrorEnum.SERVICE_UNAVAILABLE.getCode();
            msg = SystemErrorEnum.SERVICE_UNAVAILABLE.getMessage();
        } else if (e instanceof java.lang.RuntimeException && e.getMessage() != null && e.getMessage().contains(JsfErrorConstant.KEY_NO_PROVIDER)) {
            code = SystemErrorEnum.NO_PROVIDER.getCode();
            msg = SystemErrorEnum.NO_PROVIDER.getMessage();
        } else if (e instanceof java.lang.RuntimeException && e.getMessage() != null && e.getMessage().toLowerCase().contains(JsfErrorConstant.KEY_TIMEOUT)) {
            code = SystemErrorEnum.TIMEOUT_ERROR.getCode();
            msg = SystemErrorEnum.TIMEOUT_ERROR.getMessage();
        } else if (e instanceof BusinessFeatureException) {
            BusinessFeatureException be = (BusinessFeatureException) e;
            code = be.getCode();
            msg = be.getMessage();
            errorParams = be.getErrorParams();
        } else if (e instanceof BusinessException) {
            BusinessException be = (BusinessException) e;
            code = be.getCode();
            msg = be.getMessage();
            errorParams = be.getErrorParams();
        }
        return DataResponse.error(code, msg, errorParams != null ? errorParams : new String[]{serviceName});
    }
}
