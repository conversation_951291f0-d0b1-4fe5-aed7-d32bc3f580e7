package com.jdi.isc.product.center.service.adapter.mapstruct.hscode;

import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.soa.api.hsCode.cn.biz.HsCodeCnApiDTO;
import com.jdi.isc.product.soa.api.hsCode.cn.req.HsCodeCnPageReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.cn.req.HsCodeCnSaveUpdateReqApiDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CnHsCodeConvert {
    CnHsCodeConvert INSTANCE = Mappers.getMapper(CnHsCodeConvert.class);
    HsCodeCnSaveUpdateReqApiDTO voToSaveRequest(CountryHsCodeVO vo);
    CountryHsCodeVO detailResponseToVo(HsCodeCnApiDTO resp);
    List<CountryHsCodePageResponse> pageResponseListToVoList(List<HsCodeCnApiDTO> respList);
    HsCodeCnPageReqApiDTO voToPageRequest(CountryHsCodePageRequest vo);
}