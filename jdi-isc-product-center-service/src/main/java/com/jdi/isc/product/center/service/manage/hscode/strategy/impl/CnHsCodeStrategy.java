package com.jdi.isc.product.center.service.manage.hscode.strategy.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeDetailVO;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.service.adapter.mapstruct.hscode.CnHsCodeConvert;
import com.jdi.isc.product.center.service.manage.hscode.strategy.CountryHsCodeStrategy;
import com.jdi.isc.product.soa.api.hsCode.cn.HsCodeCnApiService;
import com.jdi.isc.product.soa.api.hsCode.cn.biz.HsCodeCnApiDTO;
import com.jdi.isc.product.soa.api.hsCode.cn.req.HsCodeCnDetailReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.cn.req.HsCodeCnPageReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.cn.req.HsCodeCnSaveUpdateReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class CnHsCodeStrategy implements CountryHsCodeStrategy {

    @Resource
    private HsCodeCnApiService hsCodeCnApiService;

    @Override
    public String getCountryCode() {
        return "CN";
    }

    @Override
    public DataResponse<?> saveOrUpdate(CountryHsCodeVO input) {
        try {
            HsCodeCnSaveUpdateReqApiDTO apiReq = CnHsCodeConvert.INSTANCE.voToSaveRequest(input);
            return hsCodeCnApiService.saveOrUpdate(apiReq);
        } catch (Exception e) {
            log.error("调用中国HsCode保存或更新接口失败，input={}", input, e);
            return DataResponse.error("中国HsCode保存或更新失败");
        }
    }

    @Override
    public DataResponse<CountryHsCodeVO> detail(CountryHsCodeDetailVO input) {
        try {
            HsCodeCnDetailReqApiDTO req = new HsCodeCnDetailReqApiDTO();
            req.setId(input.getId());
            req.setHsCode(input.getHsCode());
            DataResponse<HsCodeCnApiDTO> response = hsCodeCnApiService.detail(req);
            if (response.getSuccess() && response.getData() != null) {
                CountryHsCodeVO vo = CnHsCodeConvert.INSTANCE.detailResponseToVo(response.getData());
                return DataResponse.success(vo);
            } else {
                return DataResponse.error(response.getMessage());
            }
        } catch (Exception e) {
            log.error("调用中国HsCode详情接口失败，input={}", input, e);
            return DataResponse.error("中国HsCode详情查询失败");
        }
    }

    @Override
    public PageInfo<CountryHsCodePageResponse> pageSearch(CountryHsCodePageRequest input) {
        try {
            HsCodeCnPageReqApiDTO apiReq = CnHsCodeConvert.INSTANCE.voToPageRequest(input);
            DataResponse<PageInfo<HsCodeCnApiDTO>> response = hsCodeCnApiService.pageSearch(apiReq);
            if (response.getSuccess() && response.getData() != null) {
                PageInfo<HsCodeCnApiDTO> page = response.getData();
                PageInfo<CountryHsCodePageResponse> result = new PageInfo<>();
                result.setIndex(page.getIndex());
                result.setSize(page.getSize());
                result.setTotal(page.getTotal());
                List<CountryHsCodePageResponse> records = CnHsCodeConvert.INSTANCE.pageResponseListToVoList(page.getRecords());
                result.setRecords(records);
                return result;
            } else {
                return new PageInfo<>();
            }
        } catch (Exception e) {
            log.error("调用中国HsCode分页查询接口失败，input={}", input, e);
            return new PageInfo<>();
        }
    }
} 