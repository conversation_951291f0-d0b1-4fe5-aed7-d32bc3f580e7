package com.jdi.isc.product.center.service.manage.customs;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.customs.dto.*;
import com.jdi.isc.product.center.domain.customs.req.CustomsPageReqDTO;
import com.jdi.isc.product.center.domain.customs.resp.CustomsInfoRespDTO;
import com.jdi.isc.product.soa.api.customs.supplier.biz.CustomsSupplierApiDTO;
import com.jdi.isc.product.soa.api.customs.supplier.req.CustomsSupplierPageReqApiDTO;

/**
 * 关务管理Service接口
 * <AUTHOR>
 * @date 2024/07/18 10:25
 */
public interface CustomsManageService {

    /**
     * 分页查询关务列表
     * @param reqDTO 查询条件
     * @return 分页结果
     */
    DataResponse<PageInfo<CustomsInfoRespDTO>> page(CustomsPageReqDTO reqDTO);


    /**
     * 获取指定产品的海关信息
     * @param id 产品ID
     * @return 海关信息响应对象
     */
    DataResponse<CustomsInfoRespDTO> productCustomsDetail(Long id);


    /**
     * 分配供应商给产品的海关信息
     * @param dto 包含产品海关信息和供应商信息的数据传输对象
     * @return 分配是否成功的布尔值
     */
    DataResponse<Boolean> assignedSupplier(ProductCustomsAssignedSupplierDTO dto);

    /**
     * 关务评估接口
     * @param dto 产品海关申报草稿DTO
     * @return 评估结果，true表示评估通过，false表示评估失败
     */
    DataResponse<Boolean> assessment(ProductCustomsAssessmentDTO dto);

    /**
     * 检查产品是否需要强制性证书。
     * @param dto 产品海关申报草稿信息。
     * @return true 如果产品需要强制性证书，false 否则。
     */
    DataResponse<Boolean> compulsoryCertificate(ProductCustomsCompulsoryCertificateDTO dto);

    /**
     * 批量确认评估信息接口。
     * @param dto 包含待确认的产品海关信息的DTO对象。
     * @return 操作是否成功的布尔值。
     */
    DataResponse<Boolean> confirm(ProductCustomsConfirmDTO dto);

    /**
     * 删除指定的产品海关确认信息。
     * @param dto 包含要删除的产品海关确认信息的DTO对象。
     * @return 操作结果，true表示删除成功，false表示删除失败。
     */
    DataResponse<Boolean> delete(ProductCustomsConfirmDTO dto);

    /**
     * 获取海关供应商列表
     *
     * @param dto 海关供应商分页请求体
     * @return DataResponse对象，包含一个布尔类型的结果
     */
    DataResponse<PageInfo<CustomsSupplierApiDTO>> customsSupplierList(CustomsSupplierPageReqApiDTO dto);
}