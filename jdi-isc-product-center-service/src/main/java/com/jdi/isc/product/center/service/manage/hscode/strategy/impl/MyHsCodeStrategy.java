package com.jdi.isc.product.center.service.manage.hscode.strategy.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeDetailVO;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.service.adapter.mapstruct.hscode.MyHsCodeConvert;
import com.jdi.isc.product.center.service.manage.hscode.strategy.CountryHsCodeStrategy;
import com.jdi.isc.product.soa.api.hsCode.my.HsCodeMyApiService;
import com.jdi.isc.product.soa.api.hsCode.my.biz.HsCodeMyApiDTO;
import com.jdi.isc.product.soa.api.hsCode.my.req.HsCodeMySaveUpdateReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import com.jdi.isc.product.soa.api.hsCode.my.req.HsCodeMyDetailReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.my.req.HsCodeMyPageReqApiDTO;

@Slf4j
@Component
public class MyHsCodeStrategy implements CountryHsCodeStrategy {

    @Resource
    private HsCodeMyApiService hsCodeMyApiService;

    @Override
    public String getCountryCode() {
        return "MY";
    }

    @Override
    public DataResponse<?> saveOrUpdate(CountryHsCodeVO input) {
        try {
            HsCodeMySaveUpdateReqApiDTO apiReq = MyHsCodeConvert.INSTANCE.voToSaveRequest(input);
            return hsCodeMyApiService.saveOrUpdate(apiReq);
        } catch (Exception e) {
            log.error("调用马来西亚HsCode保存或更新接口失败，input={}", input, e);
            return DataResponse.error("马来西亚HsCode保存或更新失败");
        }
    }

    @Override
    public DataResponse<CountryHsCodeVO> detail(CountryHsCodeDetailVO input) {
        try {
            HsCodeMyDetailReqApiDTO req = new HsCodeMyDetailReqApiDTO();
            req.setId(input.getId());
            req.setHsCode(input.getHsCode());
            DataResponse<HsCodeMyApiDTO> response = hsCodeMyApiService.detail(req);
            if (response.getSuccess() && response.getData() != null) {
                CountryHsCodeVO vo = MyHsCodeConvert.INSTANCE.detailResponseToVo(response.getData());
                return DataResponse.success(vo);
            } else {
                return DataResponse.error(response.getMessage());
            }
        } catch (Exception e) {
            log.error("调用马来西亚HsCode详情接口失败，input={}", input, e);
            return DataResponse.error("马来西亚HsCode详情查询失败");
        }
    }

    @Override
    public PageInfo<CountryHsCodePageResponse> pageSearch(CountryHsCodePageRequest input) {
        try {
            HsCodeMyPageReqApiDTO apiReq = MyHsCodeConvert.INSTANCE.voToPageRequest(input);
            DataResponse<PageInfo<HsCodeMyApiDTO>> response = hsCodeMyApiService.pageSearch(apiReq);
            if (response.getSuccess() && response.getData() != null) {
                PageInfo<HsCodeMyApiDTO> page = response.getData();
                PageInfo<CountryHsCodePageResponse> result = new PageInfo<>();
                result.setIndex(page.getIndex());
                result.setSize(page.getSize());
                result.setTotal(page.getTotal());
                List<CountryHsCodePageResponse> records = MyHsCodeConvert.INSTANCE.pageResponseListToVoList(page.getRecords());
                result.setRecords(records);
                return result;
            } else {
                return new PageInfo<>();
            }
        } catch (Exception e) {
            log.error("调用马来西亚HsCode分页查询接口失败，input={}", input, e);
            return new PageInfo<>();
        }
    }
} 