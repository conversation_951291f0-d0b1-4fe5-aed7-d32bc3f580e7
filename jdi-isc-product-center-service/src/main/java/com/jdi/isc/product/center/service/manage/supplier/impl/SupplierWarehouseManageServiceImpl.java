package com.jdi.isc.product.center.service.manage.supplier.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jd.common.web.LoginContext;
import com.jdi.isc.library.i18n.exception.BusinessException;
import com.jdi.isc.product.center.domain.enums.YnEnum;
import com.jdi.isc.product.center.domain.enums.supplier.SupplierModifyActionEnum;
import com.jdi.isc.product.center.domain.enums.supplier.SupplierModuleEnum;
import com.jdi.isc.product.center.domain.supplier.biz.SupplierModifyRecordVO;
import com.jdi.isc.product.center.domain.supplier.biz.SupplierWarehouseVO;
import com.jdi.isc.product.center.domain.supplier.po.SupplierAccountPO;
import com.jdi.isc.product.center.domain.supplier.po.SupplierModifyRecordPO;
import com.jdi.isc.product.center.domain.supplier.po.SupplierWarehousePO;
import com.jdi.isc.product.center.service.adapter.mapstruct.supplier.SupplierWareHouseConvert;
import com.jdi.isc.product.center.service.atomic.supplier.SupplierWarehouseAtomicService;
import com.jdi.isc.product.center.service.manage.supplier.SupplierWarehouseManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 供应商信息修改记录数据维护服务
 * @Author: zhaojianguo21
 * @Date: 2024/03/20 21:29
 **/
@Slf4j
@Service
public class SupplierWarehouseManageServiceImpl implements SupplierWarehouseManageService {

    @Resource
    private SupplierWarehouseAtomicService supplierWarehouseAtomicService;


    @Override
    public List<SupplierModifyRecordVO> makeupDiffList(List<SupplierWarehouseVO> warehouses, String batchNum, String supplierCode) {
        if (null == warehouses) {
            return null;
        }
        // 查询数据库中该供应商已有的有效仓信息
        LambdaQueryWrapper<SupplierWarehousePO> queryWrapper = Wrappers.<SupplierWarehousePO>lambdaQuery()
                .eq(SupplierWarehousePO::getSupplierCode, supplierCode)
                .eq(SupplierWarehousePO::getYn, YnEnum.YES.getCode());
        List<SupplierWarehousePO> existingWarehouses = supplierWarehouseAtomicService.list(queryWrapper);
        LoginContext loginContext = LoginContext.getLoginContext();
        long timestamp = System.currentTimeMillis();
        // 前端传回来的id集合（老数据）
        Set<Long> inputIds = warehouses.stream()
                .map(SupplierWarehouseVO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 新增：前端list中id为null的
        List<SupplierWarehouseVO> toAdd = warehouses.stream()
                .filter(vo -> vo.getId() == null)
                .collect(Collectors.toList());

        // 删除：数据库有但前端list没有的
        List<SupplierWarehousePO> toDelete = existingWarehouses.stream()
                .filter(po -> po.getId() != null && !inputIds.contains(po.getId()))
                .collect(Collectors.toList());

        List<SupplierModifyRecordVO> result = new ArrayList<>();
        SupplierWareHouseConvert instance = SupplierWareHouseConvert.INSTANCE;
        if (CollectionUtils.isNotEmpty(toAdd)) {
            List<SupplierWarehousePO> addPOs = toAdd.stream()
                    .map(instance::voToPO)
                    .collect(Collectors.toList());
            addPOs.forEach(add -> {
                add.setYn(YnEnum.YES.getCode());
                add.setCreator(loginContext.getPin());
                add.setCreateTime(timestamp);
                add.setUpdater(loginContext.getPin());
                add.setUpdateTime(timestamp);
            });
            result.addAll(buildSupplierDiffInfoVOList(addPOs, SupplierModifyActionEnum.ADD.getCode(), supplierCode, batchNum));
        }
        if (CollectionUtils.isNotEmpty(toDelete)) {
            result.addAll(buildSupplierDiffInfoVOList(toDelete, SupplierModifyActionEnum.DEL.getCode(), supplierCode, batchNum));
        }
        return result;
    }

    @Override
    public boolean batchSaveOrUpdate(List<SupplierWarehouseVO> warehouses) {
        if (CollectionUtils.isNotEmpty(warehouses)) {
            List<SupplierWarehouseVO> validData = warehouses.stream()
                    .filter(supplierWarehouse -> supplierWarehouse.getId() == null)
                    .collect(Collectors.toList());
            LoginContext userInfo = LoginContext.getLoginContext();
            Long timestamp = System.currentTimeMillis();
            if (!validData.isEmpty()) {
                List<SupplierWarehousePO> warehousePOS = SupplierWareHouseConvert.INSTANCE.listVoToPO(validData);
                warehousePOS.forEach(po -> {
                    po.setCreator(userInfo.getPin());
                    po.setCreateTime(timestamp);
                    po.setUpdater(userInfo.getPin());
                    po.setUpdateTime(timestamp);
                    po.setYn(YnEnum.YES.getCode());
                });

                boolean batch = supplierWarehouseAtomicService.saveBatch(warehousePOS);
                Assert.isTrue(batch, "保存供应商仓信息异常！");
            }
        }
        return true;
    }

    @Override
    public List<SupplierWarehouseVO> makeupDiffView(List<SupplierModifyRecordPO> wareHouseList) {
        List<SupplierWarehouseVO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(wareHouseList)) {
            //新增
            List<SupplierModifyRecordPO> add = wareHouseList.stream().filter(a -> !SupplierModifyActionEnum.UPDATE.getCode().equals(a.getAction())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(add)) {
                result.addAll(add.stream().map(a -> {
                    SupplierWarehouseVO supplierWarehouseVO = JSON.parseObject(a.getJsonInfo(), SupplierWarehouseVO.class);
                    supplierWarehouseVO.setAction(a.getAction());
                    return supplierWarehouseVO;
                }).collect(Collectors.toList()));
            }
        }
        return result;
    }

    @Override
    public List<SupplierWarehouseVO> queryBySupplierCode(String supplierCode) {
        LambdaQueryWrapper<SupplierWarehousePO> queryWrapper = Wrappers.<SupplierWarehousePO>lambdaQuery()
                .eq(SupplierWarehousePO::getSupplierCode, supplierCode)
                .eq(SupplierWarehousePO::getYn, YnEnum.YES.getCode());
        List<SupplierWarehousePO> list = supplierWarehouseAtomicService.list(queryWrapper);
        return SupplierWareHouseConvert.INSTANCE.poToVOList(list);
    }

    @Override
    public boolean batchAdd(List<SupplierWarehouseVO> batchSave) {
        if (CollectionUtils.isEmpty(batchSave)) {
            return true;
        }

        // 获取所有需要添加的仓库信息
        List<SupplierWarehousePO> warehousePOS = SupplierWareHouseConvert.INSTANCE.listVoToPO(batchSave);

        // 获取第一个仓库的供应商编码（假设所有仓库都属于同一个供应商）
        String supplierCode = warehousePOS.get(0).getSupplierCode();

        // 查询该供应商已存在的仓库信息
        LambdaQueryWrapper<SupplierWarehousePO> queryWrapper = Wrappers.<SupplierWarehousePO>lambdaQuery()
                .eq(SupplierWarehousePO::getSupplierCode, supplierCode)
                .eq(SupplierWarehousePO::getYn, YnEnum.YES.getCode());
        List<SupplierWarehousePO> existingWarehouses = supplierWarehouseAtomicService.list(queryWrapper);

        // 使用迭代器来安全地移除元素
        Iterator<SupplierWarehousePO> iterator = existingWarehouses.iterator();
        while (iterator.hasNext()) {
            SupplierWarehousePO newWarehouse = iterator.next();

            // 检查是否存在重复的货主编码+仓库 或 货主ID+仓库组合
            boolean isDuplicate = warehousePOS.stream()
                    .filter(w -> !w.equals(newWarehouse))
                    .anyMatch(w -> w.getWarehouseId().equals(newWarehouse.getWarehouseId())
                            && (w.getCustomerDeptCode().equals(newWarehouse.getCustomerDeptCode())
                            || w.getCargoOwnerId().equals(newWarehouse.getCargoOwnerId())));

            if (isDuplicate) {
                log.error("supplier_code :{} 存在重复的仓库组合：货主编码[{}]或货主ID[{}]与仓库[{}]重复",
                        supplierCode,
                        newWarehouse.getCustomerDeptCode(),
                        newWarehouse.getCargoOwnerId(),
                        newWarehouse.getWarehouseId());
                iterator.remove();
            }
        }

        if (CollectionUtils.isEmpty(warehousePOS)) {
            return true;
        }

        // 批量保存新的仓库信息
        boolean batch = supplierWarehouseAtomicService.saveBatch(warehousePOS);
        Assert.isTrue(batch, "保存供应商仓信息异常！");

        return true;
    }

    @Override
    public boolean batchDel(Set<Long> delIds, String optPin, long optTimestamp) {
        LambdaUpdateWrapper<SupplierWarehousePO> updateWrapper = Wrappers.<SupplierWarehousePO>lambdaUpdate()
                .set(SupplierWarehousePO::getUpdater, optPin)
                .set(SupplierWarehousePO::getUpdateTime, optTimestamp)
                .set(SupplierWarehousePO::getYn, YnEnum.NO.getCode())
                .in(SupplierWarehousePO::getId, delIds);
        supplierWarehouseAtomicService.update(updateWrapper);
        return true;
    }

    private List<SupplierModifyRecordVO> buildSupplierDiffInfoVOList(List<SupplierWarehousePO> originData, Integer action, String supplierCode, String batchNum) {
        List<SupplierModifyRecordVO> result = new ArrayList<>();
        LoginContext loginContext = LoginContext.getLoginContext();
        long timestamp = System.currentTimeMillis();
        for (SupplierWarehousePO data : originData) {
            SupplierModifyRecordVO businessUpdate = new SupplierModifyRecordVO();
            businessUpdate.setAction(action);
            businessUpdate.setJsonInfo(JSON.toJSONString(data));
            businessUpdate.setModule(SupplierModuleEnum.WARE_HOUSE.getCode());
            businessUpdate.setSupplierCode(supplierCode);
            businessUpdate.setBatchNum(batchNum);
            businessUpdate.setUpdater(loginContext.getPin());
            businessUpdate.setCreator(loginContext.getPin());
            businessUpdate.setUpdateTime(timestamp);
            businessUpdate.setCreateTime(timestamp);
            result.add(businessUpdate);
        }
        return result;
    }
}
