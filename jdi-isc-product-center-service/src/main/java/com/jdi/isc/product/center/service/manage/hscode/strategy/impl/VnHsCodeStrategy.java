package com.jdi.isc.product.center.service.manage.hscode.strategy.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeDetailVO;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.domain.hscode.vn.VnHsCodeSaveOrUpdateRequest;
import com.jdi.isc.product.center.domain.hscode.vn.VnHsCodeDetailRequest;
import com.jdi.isc.product.center.service.adapter.mapstruct.hscode.VnHsCodeConvert;
import com.jdi.isc.product.center.service.manage.hscode.strategy.CountryHsCodeStrategy;
import com.jdi.isc.product.soa.api.hsCode.vn.HsCodeVnApiService;
import com.jdi.isc.product.soa.api.hsCode.vn.biz.HsCodeVnApiDTO;
import com.jdi.isc.product.soa.api.hsCode.vn.req.HsCodeVnDetailReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.vn.req.HsCodeVnPageReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.vn.req.HsCodeVnSaveUpdateReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class VnHsCodeStrategy implements CountryHsCodeStrategy {

    @Resource
    private HsCodeVnApiService hsCodeVnApiService;

    @Override
    public String getCountryCode() {
        return "VN";
    }

    @Override
    public DataResponse<?> saveOrUpdate(CountryHsCodeVO input) {
        try {
            HsCodeVnSaveUpdateReqApiDTO apiReq = VnHsCodeConvert.INSTANCE.voToSaveRequest(input);
            return hsCodeVnApiService.saveOrUpdate(apiReq);
        } catch (Exception e) {
            log.error("调用越南HsCode保存或更新接口失败，input={}", input, e);
            return DataResponse.error("越南HsCode保存或更新失败");
        }
    }

    @Override
    public DataResponse<CountryHsCodeVO> detail(CountryHsCodeDetailVO input) {
        try {
            HsCodeVnDetailReqApiDTO req = new HsCodeVnDetailReqApiDTO();
            req.setId(input.getId());
            req.setHsCode(input.getHsCode());
            DataResponse<HsCodeVnApiDTO> response = hsCodeVnApiService.detail(req);
            if (response.getSuccess() && response.getData() != null) {
                CountryHsCodeVO vo = VnHsCodeConvert.INSTANCE.detailResponseToVo(response.getData());
                return DataResponse.success(vo);
            } else {
                return DataResponse.error(response.getMessage());
            }
        } catch (Exception e) {
            log.error("调用越南HsCode详情接口失败，input={}", input, e);
            return DataResponse.error("越南HsCode详情查询失败");
        }
    }

    @Override
    public PageInfo<CountryHsCodePageResponse> pageSearch(CountryHsCodePageRequest input) {
        try {
            HsCodeVnPageReqApiDTO req = VnHsCodeConvert.INSTANCE.voToPageRequest(input);
            DataResponse<PageInfo<HsCodeVnApiDTO>> response = hsCodeVnApiService.pageSearch(req);
            if (response.getSuccess() && response.getData() != null) {
                PageInfo<HsCodeVnApiDTO> page = response.getData();
                PageInfo<CountryHsCodePageResponse> result = new PageInfo<>();
                result.setIndex(page.getIndex());
                result.setSize(page.getSize());
                result.setTotal(page.getTotal());
                List<CountryHsCodePageResponse> records = VnHsCodeConvert.INSTANCE.pageResponseListToVoList(page.getRecords());
                result.setRecords(records);
                return result;
            } else {
                return new PageInfo<>();
            }
        } catch (Exception e) {
            log.error("调用越南HsCode分页查询接口失败，input={}", input, e);
            return new PageInfo<>();
        }
    }
} 