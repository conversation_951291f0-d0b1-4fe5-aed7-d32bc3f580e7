package com.jdi.isc.product.center.service.manage.mku.impl;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.mku.biz.MkuChangeBindingRecordQueryVO;
import com.jdi.isc.product.center.domain.mku.biz.MkuChangeBindingRecordVO;
import com.jdi.isc.product.center.rpc.mku.MkuRpcService;
import com.jdi.isc.product.center.service.adapter.mapstruct.mku.MkuRelationConvert;
import com.jdi.isc.product.center.service.manage.mku.MkuChangeBindingManageService;
import com.jdi.isc.product.soa.api.mkuBinding.req.MkuChangeBindingRecordQueryDTO;
import com.jdi.isc.product.soa.api.mkuBinding.res.MkuChangeBindingRecordResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description：MkuChangeBindingManageServiceImpl
 * @Date 2025-08-27
 */
@Slf4j
@Service
public class MkuChangeBindingManageServiceImpl implements MkuChangeBindingManageService {

    @Resource
    private MkuRpcService mkuRpcService;

    @Override
    public DataResponse<PageInfo<MkuChangeBindingRecordVO>> page(MkuChangeBindingRecordQueryVO reqVO) {
        MkuChangeBindingRecordQueryDTO queryDTO = MkuRelationConvert.INSTANCE.recordVo2DTO(reqVO);
        DataResponse<PageInfo<MkuChangeBindingRecordResDTO>> response = mkuRpcService.page(queryDTO);
        PageInfo<MkuChangeBindingRecordVO> pageInfo = MkuRelationConvert.INSTANCE.pageDTO2VO(response.getData());
        return DataResponse.success(pageInfo);
    }
}
