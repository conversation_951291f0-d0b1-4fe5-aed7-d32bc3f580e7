package com.jdi.isc.product.center.service.order.comment.impl;

import com.jd.common.web.LoginContext;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.library.i18n.exception.BusinessException;
import com.jdi.isc.order.center.api.orderComment.OrderCommentApiService;
import com.jdi.isc.order.center.api.orderComment.biz.OrderCommentApiDTO;
import com.jdi.isc.order.center.api.orderComment.req.OrderCommentCountReqApiDTO;
import com.jdi.isc.order.center.api.orderComment.req.OrderCommentSaveUpdateReqApiDTO;
import com.jdi.isc.product.center.common.utils.SystemUtil;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentDetailReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentPageReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentSaveReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentCountReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentDeleteReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentReadApiDTO;
import com.jdi.isc.product.center.service.order.comment.OrderCommentManageService;
import com.jdi.isc.product.center.service.order.comment.convert.OrderCommentConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.jdi.isc.order.center.api.orderComment.req.OrderCommentDetailReqApiDTO;
import com.jdi.isc.order.center.api.orderComment.req.OrderCommentPageReqApiDTO;
import com.jdi.isc.order.center.api.orderComment.req.OrderCommentDelReqApiDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单留言板管理服务实现类
 *
 * @author: zhangjin176
 * @date: 2025/5/23 11:35
 * @version:
 **/
@Slf4j
@Service
public class OrderCommentManageServiceImpl implements OrderCommentManageService {
    @Resource
    private OrderCommentApiService orderCommentApiService;

    @Override
    public DataResponse<Boolean> save(OrderCommentSaveReq req) {
        log.info("保存更新订单留言，请求参数: {}", req);
        try {
            // 使用转换器转换请求DTO
            OrderCommentSaveUpdateReqApiDTO saveUpdateReqApiDTO = OrderCommentConvert.INSTANCE.saveReq2ApiDto(req);
            saveUpdateReqApiDTO.setCommenterErp(LoginContext.getLoginContext().getPin());
            saveUpdateReqApiDTO.setCommentTime(System.currentTimeMillis());
            saveUpdateReqApiDTO.setUpdater(LoginContext.getLoginContext().getPin());
            // 调用API服务
            DataResponse<OrderCommentApiDTO> response = orderCommentApiService.saveOrUpdate(saveUpdateReqApiDTO);
            
            // 检查响应是否为空
            if (response == null) {
                log.error("【返回为空】saveOrUpdate, 参数: {}", saveUpdateReqApiDTO);
                throw new BusinessException("DOWNSTREAM_SERVICE_CALLBACK_NULL", "下游服务返回为空");
            }
            
            // 检查响应是否成功
            if (!Boolean.TRUE.equals(response.getSuccess())) {
                log.error("【返回失败】saveOrUpdate, 参数: {}, 响应: {}", saveUpdateReqApiDTO, response);
                String code = response.getCode() != null ? response.getCode() : "-1";
                throw new BusinessException(code, response.getMessage() != null ? response.getMessage() : "保存更新失败");
            }
            return DataResponse.success(true);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("保存更新订单留言发生异常，请求参数: {}", req, e);
            throw new BusinessException("SYSTEM_ERROR", "系统异常：" + e.getMessage());
        }
    }



    @Override
    public PageInfo<OrderCommentReadApiDTO> page(OrderCommentPageReq req) {
        log.info("分页查询订单留言，请求参数: {}", req);
        try {
            // 使用转换器转换请求DTO
            OrderCommentPageReqApiDTO pageReqApiDTO = OrderCommentConvert.INSTANCE.pageReq2ApiDto(req);

            // 调用API服务
            DataResponse<PageInfo<OrderCommentApiDTO>> response = orderCommentApiService.pageSearch(pageReqApiDTO);
            
            // 检查响应是否为空
            if (response == null) {
                log.error("【返回为空】pageSearch, 参数: {}", pageReqApiDTO);
                throw new BusinessException("DOWNSTREAM_SERVICE_CALLBACK_NULL", "下游服务返回为空");
            }
            
            // 检查响应是否成功
            if (!Boolean.TRUE.equals(response.getSuccess())) {
                log.error("【返回失败】pageSearch, 参数: {}, 响应: {}", pageReqApiDTO, response);
                String code = response.getCode() != null ? response.getCode() : "-1";
                throw new BusinessException(code, response.getMessage() != null ? response.getMessage() : "分页查询失败");
            }
            
            // 获取响应数据
            PageInfo<OrderCommentApiDTO> data = response.getData();
            if (data == null) {
                log.error("【数据为null】pageSearch 成功返回但 data 为空, 参数: {}", pageReqApiDTO);
                throw new BusinessException("DOWNSTREAM_SERVICE_CALLBACK_NULL", "下游服务返回数据为空");
            }
            
            // 转换响应数据
            PageInfo<OrderCommentReadApiDTO> result = convertToPageInfo(data);
            log.info("分页查询订单留言成功，请求参数: {}, 返回结果: {}", req, result);
            return result;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("分页查询订单留言发生异常，请求参数: {}", req, e);
            throw new BusinessException("SYSTEM_ERROR", "系统异常：" + e.getMessage());
        }
    }

    @Override
    public Long count(OrderCommentCountReq req) {
        log.info("查询订单留言数量，请求参数: {}", req);
        try {
            // 使用转换器转换请求DTO
            OrderCommentCountReqApiDTO countReqApiDTO = OrderCommentConvert.INSTANCE.countReq2ApiDto(req);

            // 调用API服务
            DataResponse<Long> response = orderCommentApiService.count(countReqApiDTO);
            
            // 检查响应是否为空
            if (response == null) {
                log.error("【返回为空】count, 参数: {}", countReqApiDTO);
                throw new BusinessException("DOWNSTREAM_SERVICE_CALLBACK_NULL", "下游服务返回为空");
            }
            
            // 检查响应是否成功
            if (!Boolean.TRUE.equals(response.getSuccess())) {
                log.error("【返回失败】count, 参数: {}, 响应: {}", countReqApiDTO, response);
                String code = response.getCode() != null ? response.getCode() : "-1";
                throw new BusinessException(code, response.getMessage() != null ? response.getMessage() : "查询数量失败");
            }
            
            // 获取响应数据
            Long data = response.getData();
            if (data == null) {
                log.warn("【数据为null】count 成功返回但 data 为空, 参数: {}, 返回默认值0", countReqApiDTO);
                return 0L;
            }
            
            log.info("查询订单留言数量成功，请求参数: {}, 返回结果: {}", req, data);
            return data;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询订单留言数量发生异常，请求参数: {}", req, e);
            throw new BusinessException("SYSTEM_ERROR", "系统异常：" + e.getMessage());
        }
    }



    private PageInfo<OrderCommentReadApiDTO> convertToPageInfo(PageInfo<OrderCommentApiDTO> apiPageInfo) {
        PageInfo<OrderCommentReadApiDTO> result = new PageInfo<>();
        result.setTotal(apiPageInfo.getTotal());
        result.setIndex(apiPageInfo.getIndex());
        result.setSize(apiPageInfo.getSize());
        
        if (apiPageInfo.getRecords() != null && !apiPageInfo.getRecords().isEmpty()) {
            List<OrderCommentReadApiDTO> records = apiPageInfo.getRecords().stream()
                    .map(OrderCommentConvert.INSTANCE::apiDto2ReadApiDto)
                    .filter(dto -> dto != null)
                    .collect(Collectors.toList());
            result.setRecords(records);
        }
        
        return result;
    }
}
