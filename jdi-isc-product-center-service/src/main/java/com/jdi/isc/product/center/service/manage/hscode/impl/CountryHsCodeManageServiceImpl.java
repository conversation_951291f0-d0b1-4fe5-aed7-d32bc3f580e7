package com.jdi.isc.product.center.service.manage.hscode.impl;

import com.jd.common.web.LoginContext;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeDetailVO;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.service.manage.hscode.CountryHsCodeManageService;
import com.jdi.isc.product.center.service.manage.hscode.strategy.CountryHsCodeStrategy;
import com.jdi.isc.product.center.service.manage.hscode.strategy.CountryHsCodeStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 6个国家的HsCode管理服务实现类
 * 支持国家：BR(巴西)、HU(匈牙利)、ID(印度尼西亚)、MY(马来西亚)、TH(泰国)、VN(越南)
 * <AUTHOR>
 * @date 2024/02/21
 */
@Slf4j
@Service
public class CountryHsCodeManageServiceImpl implements CountryHsCodeManageService {

    @Resource
    private CountryHsCodeStrategyFactory strategyFactory;

    @Override
    @ToolKit
    public DataResponse<CountryHsCodeVO> saveOrUpdate(CountryHsCodeVO input) {
        log.info("CountryHsCodeManageServiceImpl.saveOrUpdate input={}", input);
        try {
            input.setCreator(LoginContext.getLoginContext().getPin());
            input.setUpdater(LoginContext.getLoginContext().getPin());
            input.setCreateTime(System.currentTimeMillis());
            input.setUpdateTime(System.currentTimeMillis());
            
            // 使用策略模式根据国家代码获取对应的策略
            String countryCode = input.getCountryCode().toUpperCase();
            CountryHsCodeStrategy strategy = strategyFactory.getStrategy(countryCode);
            DataResponse<?> response = strategy.saveOrUpdate(input);
            
            if (response.getSuccess()) {
                log.info("保存或更新HsCode信息成功，国家代码：{}，HsCode：{}", input.getCountryCode(), input.getHsCode());
                return DataResponse.success(input);
            } else {
                return DataResponse.error(response.getMessage());
            }
        } catch (Exception e) {
            log.error("CountryHsCodeManageServiceImpl.saveOrUpdate exception, input={}", input, e);
            return DataResponse.error("保存或更新失败");
        }
    }

    @Override
    public DataResponse<CountryHsCodeVO> detail(CountryHsCodeDetailVO detailVO) {
        log.info("CountryHsCodeManageServiceImpl.detail input={}", detailVO);
        try {
            CountryHsCodeStrategy strategy = strategyFactory.getStrategy(detailVO.getCountryCode());
            if (strategy == null) {
                return DataResponse.error("暂不支持该国家的HsCode详情查询");
            }

            return strategy.detail(detailVO);
        } catch (Exception e) {
            log.error("CountryHsCodeManageServiceImpl.detail exception, input={}", detailVO, e);
            return DataResponse.error("查询详情失败");
        }
    }

    @Override
    public DataResponse<PageInfo<CountryHsCodePageResponse>> pageSearch(CountryHsCodePageRequest input) {
        log.info("CountryHsCodeManageServiceImpl.pageSearch input={}", input);
        try {
            if (input == null) {
                return DataResponse.error("请求参数不能为空");
            }
            String countryCode = input.getCountryCode();
            if (countryCode == null || countryCode.trim().isEmpty()) {
                return DataResponse.error("国家代码不能为空");
            }
            
            CountryHsCodeStrategy strategy = strategyFactory.getStrategy(countryCode);
            PageInfo<CountryHsCodePageResponse> pageInfo = strategy.pageSearch(input);
            
            log.info("分页查询HsCode信息，国家代码：{}，HsCode：{}", input.getCountryCode(), input.getHsCodes());
            
            return DataResponse.success(pageInfo);
        } catch (Exception e) {
            log.error("CountryHsCodeManageServiceImpl.pageSearch exception, input={}", input, e);
            return DataResponse.error("分页查询失败");
        }
    }
} 