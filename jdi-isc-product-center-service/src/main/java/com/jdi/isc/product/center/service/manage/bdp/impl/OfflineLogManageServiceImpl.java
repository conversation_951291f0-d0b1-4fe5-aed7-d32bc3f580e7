package com.jdi.isc.product.center.service.manage.bdp.impl;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.api.devOps.req.ForeverLogDTO;
import com.jdi.isc.product.center.domain.bdp.ForeverLogVO;
import com.jdi.isc.product.center.rpc.bdp.BdpQueryRpcService;
import com.jdi.isc.product.center.service.manage.bdp.OfflineLogManageService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.*;

/**
 * 离线日志查询服务
 * <AUTHOR>
 * @date 2025/4/16
 */
@Slf4j
@Service
public class OfflineLogManageServiceImpl implements OfflineLogManageService {

    final ExecutorService pool = new ThreadPoolExecutor(32, 48, 30L, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(200), new ThreadFactoryBuilder().setNamePrefix("off-log").build());

    private final static Integer TIME_OUT = 58;

    @Resource
    private BdpQueryRpcService bdpQueryRpcService;

    @SneakyThrows
    @Override
    public DataResponse<PageInfo<ForeverLogVO>> page(ForeverLogDTO req) {
        DataResponse<PageInfo<ForeverLogVO>> response = new DataResponse<>();
        PageInfo<ForeverLogVO> pageInfo = new PageInfo<>();
        try {
            CompletableFuture<Long> cntFuture = CompletableFuture.supplyAsync(() -> bdpQueryRpcService.queryOfflineLogCnt(req),pool).exceptionally(e -> {throw new RuntimeException(e);});
            CompletableFuture<List<ForeverLogVO>> logFuture = CompletableFuture.supplyAsync(() -> bdpQueryRpcService.queryOfflineLog(req),pool).exceptionally(e -> {throw new RuntimeException(e);});
            Long cnt = cntFuture.get(TIME_OUT, TimeUnit.SECONDS);
            List<ForeverLogVO> logList = logFuture.get(TIME_OUT, TimeUnit.SECONDS);
            pageInfo.setSize(req.getPageSize());
            pageInfo.setTotal(cnt);
            pageInfo.setIndex(req.getPageNum());
            pageInfo.setRecords(logList);
        }catch (com.jd.jsf.gd.error.RpcException e){
            log.error("OfflineLogManageServiceImpl.page invoke error, target:{} ", JSON.toJSONString(req), e);
            return DataResponse.error("下游大数据查询请求超时,请稍后再试.");
        }
        response.setData(pageInfo);
        return response;
    }
}
