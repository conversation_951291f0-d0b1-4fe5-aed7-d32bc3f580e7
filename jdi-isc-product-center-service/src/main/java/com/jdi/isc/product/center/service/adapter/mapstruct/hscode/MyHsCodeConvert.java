package com.jdi.isc.product.center.service.adapter.mapstruct.hscode;

import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.soa.api.hsCode.my.biz.HsCodeMyApiDTO;
import com.jdi.isc.product.soa.api.hsCode.my.req.HsCodeMySaveUpdateReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.my.req.HsCodeMyDetailReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.my.req.HsCodeMyPageReqApiDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface MyHsCodeConvert {
    MyHsCodeConvert INSTANCE = Mappers.getMapper(MyHsCodeConvert.class);
    HsCodeMySaveUpdateReqApiDTO voToSaveRequest(CountryHsCodeVO vo);
    CountryHsCodeVO detailResponseToVo(HsCodeMyApiDTO resp);
    List<CountryHsCodePageResponse> pageResponseListToVoList(List<HsCodeMyApiDTO> respList);
    HsCodeMyPageReqApiDTO voToPageRequest(CountryHsCodePageRequest vo);
}