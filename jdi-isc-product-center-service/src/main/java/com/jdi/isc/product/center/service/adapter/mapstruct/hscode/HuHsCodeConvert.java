package com.jdi.isc.product.center.service.adapter.mapstruct.hscode;

import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.soa.api.hsCode.hu.biz.HsCodeHuApiDTO;
import com.jdi.isc.product.soa.api.hsCode.hu.req.HsCodeHuSaveUpdateReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.hu.req.HsCodeHuDetailReqApiDTO;
import com.jdi.isc.product.soa.api.hsCode.hu.req.HsCodeHuPageReqApiDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface HuHsCodeConvert {
    HuHsCodeConvert INSTANCE = Mappers.getMapper(HuHsCodeConvert.class);
    HsCodeHuSaveUpdateReqApiDTO voToSaveRequest(CountryHsCodeVO vo);
    CountryHsCodeVO detailResponseToVo(HsCodeHuApiDTO resp);
    List<CountryHsCodePageResponse> pageResponseListToVoList(List<HsCodeHuApiDTO> respList);
    HsCodeHuPageReqApiDTO voToPageRequest(CountryHsCodePageRequest vo);
}