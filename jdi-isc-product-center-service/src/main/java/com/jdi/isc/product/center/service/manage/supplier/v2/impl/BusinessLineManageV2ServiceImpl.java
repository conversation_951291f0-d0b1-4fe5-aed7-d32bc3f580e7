package com.jdi.isc.product.center.service.manage.supplier.v2.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.common.web.LoginContext;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.library.i18n.datasource.cache.I18nCacheManager;
import com.jdi.isc.library.i18n.exception.BusinessException;
import com.jdi.isc.product.center.common.enums.response.BusinessErrorEnums;
import com.jdi.isc.product.center.common.exception.BizException;
import com.jdi.isc.product.center.domain.brand.biz.BrandLangVO;
import com.jdi.isc.product.center.domain.brand.biz.BrandVO;
import com.jdi.isc.product.center.domain.brand.po.BrandPO;
import com.jdi.isc.product.center.domain.category.biz.CategoryAggDetailReqVO;
import com.jdi.isc.product.center.domain.category.po.CategoryPO;
import com.jdi.isc.product.center.domain.common.biz.CompareResult;
import com.jdi.isc.product.center.domain.common.biz.FieldDiffVO;
import com.jdi.isc.product.center.domain.enums.StatusEnum;
import com.jdi.isc.product.center.domain.enums.supplier.*;
import com.jdi.isc.product.center.domain.supplier.SupplierModifyRecordDTO;
import com.jdi.isc.product.center.domain.supplier.biz.SupplierBaseInfoVO;
import com.jdi.isc.product.center.domain.supplier.biz.SupplierDiffInfoVO;
import com.jdi.isc.product.center.domain.supplier.biz.SupplierModifyRecordGroupDetailVO;
import com.jdi.isc.product.center.domain.supplier.biz.SupplierModifyRecordVO;
import com.jdi.isc.product.center.domain.supplier.po.BusinessLineRelationPO;
import com.jdi.isc.product.center.domain.supplier.po.SupplierBaseInfoPO;
import com.jdi.isc.product.center.domain.supplier.req.BusinessLineEditDTO;
import com.jdi.isc.product.center.domain.supplier.req.BusinessLineEditReq;
import com.jdi.isc.product.center.domain.supplier.req.BusinessLineReq;
import com.jdi.isc.product.center.domain.supplier.resp.BLDiffResult;
import com.jdi.isc.product.center.domain.supplier.resp.BusinessLineRelationResp;
import com.jdi.isc.product.center.domain.supplier.resp.CalculateResult;
import com.jdi.isc.product.center.domain.category.biz.CategoryPathVO;
import com.jdi.isc.product.center.service.adapter.mapstruct.supplier.v2.BusinessLineRelationV2Convert;
import com.jdi.isc.product.center.service.atomic.brand.BrandAtomicService;
import com.jdi.isc.product.center.service.atomic.category.CategoryAtomicService;
import com.jdi.isc.product.center.service.manage.brand.BrandManageService;
import com.jdi.isc.product.center.service.manage.category.CategoryOutService;
import com.jdi.isc.product.center.service.manage.supplier.BusinessLineManageService;
import com.jdi.isc.product.center.service.manage.supplier.SupplierAuditManageService;
import com.jdi.isc.product.center.service.manage.supplier.modify.impl.SupplierModifyBaseInfoServiceImpl;
import com.jdi.isc.product.center.service.manage.supplier.v2.BusinessLineManageV2Service;
import com.jdi.isc.product.center.service.manage.supplier.v2.BusinessLineRelationManageV2Service;
import com.jdi.isc.product.center.service.manage.supplier.SupplierBaseInfoManageService;
import com.jdi.isc.product.center.service.manage.supplier.SupplierModifyRecordManageService;
import com.jdi.isc.product.center.service.manage.supplier.v2.businessLine.BusinessLineEditStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.center.common.costants.I18nConstant.FAILURE_KEY;

/**
 * @description: 产品线管理实现类
 * @author: zhangjin176
 * @date: 2025/5/6 13:23
 * @version: 2.0
 **/
@Slf4j
@Service
public class BusinessLineManageV2ServiceImpl implements BusinessLineManageV2Service {
    /**
     * 提供业务线关系管理服务，用于查询和更新供应商的业务线信息。
     */
    @Resource
    private BusinessLineRelationManageV2Service businessLineRelationManageV2Service;
    /**
     * 提供供应商基本信息管理服务，用于验证供应商的合法性。
     */
    @Resource
    private SupplierBaseInfoManageService supplierBaseInfoManageService;
    /**
     * 提供类目基本信息管理服务，用于查询类目路径等信息。
     */
    @Resource
    private CategoryOutService categoryOutService;
    /**
     * 提供品牌基本信息管理服务，用于查询和获取品牌相关信息。
     */
    @Resource
    private BrandManageService brandManageService;
    /**
     * 国际化缓存管理器，用于获取多语言环境下的翻译文本。
     */
    @Resource
    private I18nCacheManager i18nCacheManager;
    /**
     * 业务线编辑策略列表，用于处理业务线变更操作。
     */
    @Resource
    private List<BusinessLineEditStrategy> strategyList;
    /**
     * 供应商修改记录管理服务，用于检查是否有待审批的修改记录。
     */
    @Resource
    private SupplierModifyRecordManageService supplierModifyRecordManageService;

    /**
     * 供应商审批管理服务，用于保存和处理供应商修改的审批流程。
     */
    @Resource
    private SupplierAuditManageService supplierAuditManageService;

    @Resource
    private SupplierModifyBaseInfoServiceImpl supplierModifyBaseInfoService;

    @Resource
    private BusinessLineManageService businessLineManageService;

    @Resource
    private CategoryAtomicService categoryAtomicService;


    /**
     * 列表查询业务线关系信息
     *
     * @param req 查询请求对象，包含供应商编码、品牌ID、类目ID、代理级别、分页索引和分页大小等信息
     * @return 分页结果对象，包含总记录数、当前页数据等信息
     */
    @Override
    public PageInfo<BusinessLineRelationResp> list(BusinessLineReq req) {
        //首先校验供应商的合法性
        SupplierBaseInfoVO baseInfoDb = supplierBaseInfoManageService.getByCode(req.getSupplierCode());
        if (baseInfoDb == null) {
            throw new BusinessException(BusinessErrorEnums.DATA_NOT_EXIST);
        }
        List<Long> cateIds = null;
        if (req.getLastCatId() != null) {
            CategoryPO categoryVO = categoryAtomicService.getValidPOByJdCatId(req.getLastCatId());
            if (categoryVO == null) {
                return new PageInfo<>();
            }
            cateIds = categoryOutService.LevelFinalIdsByCateId(CategoryAggDetailReqVO.builder().cateId(categoryVO.getJdCatId()).level(categoryVO.getCatLevel()).build(), categoryVO.getCatLevel());
        }
        Page<BusinessLineRelationPO> page = businessLineRelationManageV2Service.page(req.getSupplierCode(), req.getBrandId(), cateIds, req.getAgentLevel(), req.getIndex(), req.getSize());
        PageInfo<BusinessLineRelationResp> result = BusinessLineRelationV2Convert.INSTANCE.poPage2Resp(page);
        result.getRecords().forEach(re -> fillBusinessLineExtraInfo(re, baseInfoDb.getBusinessLicenseName(), req.getLang()));
        return result;
    }


    /**
     * 填充业务线额外信息。
     *
     * @param re                  BusinessLineRelationResp 对象，用于存储填充后的结果。
     * @param businessLicenseName 企业营业执照名称。
     * @param lang                语言代码，用于获取品牌名称和类别路径。
     */
    private void fillBusinessLineExtraInfo(BusinessLineRelationResp re, String businessLicenseName, String lang) {
        AtomicBoolean isValid = new AtomicBoolean(true);
        re.setBusinessLicenseName(businessLicenseName);
        re.setBrandName(getBrandName(re.getBrandId(), lang, isValid));
        re.setCategoryAllPathStr(getCategoryPathStr(re.getCategoryId(), lang, isValid));
        SupplierAgentLevelEnum levelEnum = SupplierAgentLevelEnum.forCode(re.getAgentLevel() == null ? null : Integer.valueOf(re.getAgentLevel()));
        re.setAgentLevel(levelEnum != null ? levelEnum.getCode() : null);
        re.setValid(isValid.get());
    }


    /**
     * 根据品牌ID和语言获取品牌名称。
     *
     * @param brandId 品牌ID
     * @param lang    语言代码
     * @param isValid 是否成功获取品牌名称的标志
     * @return 品牌名称或错误信息
     */
    String getBrandName(Long brandId, String lang, AtomicBoolean isValid) {
        BrandVO detail = brandManageService.detail(brandId, lang);
        if (detail == null || CollectionUtils.isEmpty(detail.getLangList())||detail.getStatus()==0) {
            isValid.set(false);
            return i18nCacheManager.getValueOrDefault(lang, FAILURE_KEY) + brandId.toString();
        }
        List<BrandLangVO> langList = detail.getLangList();
        for (BrandLangVO langVO : langList) {
            if (lang != null && lang.equals(langVO.getLang())) {
                return langVO.getLangName();
            }
        }
        return langList.get(0).getLangName();
    }

    /**
     * 根据给定的类别ID和语言，获取类别路径字符串。
     *
     * @param catId   类别ID
     * @param lang    语言代码
     * @param isValid 是否有效的标志
     * @return 类别路径字符串，如果类别名称为空则返回指定语言的失败信息
     */
    String getCategoryPathStr(Long catId, String lang, AtomicBoolean isValid) {
        // 获取类目路径名
        String name = categoryOutService.queryPathStr(catId, lang);

        // 如果为空，标记为无效并使用默认错误信息
        if (StringUtils.isBlank(name)) {
            isValid.set(false);
            String fallback = i18nCacheManager.getValueOrDefault(lang, FAILURE_KEY);
            name = fallback + catId;
        }

        // 获取类目信息并判断是否为禁用状态
        CategoryPO categoryPO = categoryAtomicService.getValidPOByJdCatId(catId);
        if (categoryPO != null && StatusEnum.FORBIDDEN.getCode().equals(categoryPO.getStatus())) {
            String fallback = i18nCacheManager.getValueOrDefault(lang, FAILURE_KEY);
            name = fallback + catId;
        }

        return name;
    }


    /**
     * 处理供应商业务线变更请求。
     *
     * @param req      业务线编辑请求对象，包含要修改的供应商代码和业务线信息。
     * @param operator 操作人标识。
     * @return null
     */
    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public String handleBusinessLineChange(BusinessLineEditReq req, String operator) {
        SupplierBaseInfoPO baseInfoDb = supplierBaseInfoManageService.isValid(req.getSupplierCode());
        Map<String, BusinessLineEditDTO> data = req.getData();
        if (MapUtils.isEmpty(data)) {
            throw new BusinessException(BusinessErrorEnums.NO_DATA_CHANGED);
        }
        List<BusinessLineRelationPO> relationDb = businessLineRelationManageV2Service.getSelectList(baseInfoDb.getSupplierCode());
        List<BusinessLineEditDTO> values = new ArrayList<>(data.values());
        Map<String, BusinessLineRelationPO> dbMap = relationDb.stream()
                .collect(Collectors.toMap(po -> po.getBrandId() + "-" + po.getJdCatId(), Function.identity()));
        BLDiffResult blDiffResult = compareBusinessLineChanges(values, dbMap, operator, req.getSupplierCode());
        log.info("BusinessLineManageV2Service#blDiffResult:{}", blDiffResult);
        //开始计算 假如原有供应商等级为 K-跨境集货商  不参与重新计算 供应商等级。
        Integer enterpriseType;
        if (baseInfoDb.getEnterpriseType() != null && !SupplierEnterpriseTypeEnum.CROSS_BORDER_TRADER.getCode().equals(baseInfoDb.getEnterpriseType())) {
            CalculateResult calculateResult = filterAgentLevelAndSecondCategoryCount(blDiffResult, dbMap);
            enterpriseType = calculateEnterpriseType(calculateResult);
        } else {
            enterpriseType = SupplierEnterpriseTypeEnum.CROSS_BORDER_TRADER.getCode();
        }
        String batchNum = UUID.randomUUID().toString();

        //找差异准备提交xbp
        List<SupplierModifyRecordVO> records = new ArrayList<>();

        //供应商代理等级差异
        SupplierModifyRecordVO recordVO = makeupDiffBaseInfo(baseInfoDb, enterpriseType, batchNum);
        records.add(recordVO);

        //产品线差异
        List<SupplierModifyRecordVO> businessRes = businessLineManageService.makeupDiffListV2(blDiffResult, baseInfoDb.getSupplierCode(), batchNum);
        if (CollectionUtils.isNotEmpty(businessRes)) {
            records.addAll(businessRes);
        }
        boolean isWaitApproval = supplierModifyRecordManageService.hasWaitApprovalRecord(req.getSupplierCode());
        if (isWaitApproval) {
            throw new BusinessException("目前存在正在执行的审批流，请催促相关人员审批后，提交产品线信息");
        }
        saveModifyAndAudit(records, baseInfoDb, enterpriseType, batchNum);
        return batchNum;
    }

    /**
     * 判断是否存在指定供应商、品牌和类别的业务线关系。
     *
     * @param supplierCode 供应商代码。
     * @param brandId      品牌ID。
     * @param catId        类别ID。
     * @return true 如果存在指定的业务线关系，false 否则。
     */
    @Override
    public boolean isExist(String supplierCode, String brandId, String catId) {
        return businessLineRelationManageV2Service.count(supplierCode, brandId, catId) > 0;
    }

    private SupplierModifyRecordVO makeupDiffBaseInfo(SupplierBaseInfoPO baseInfoDb, Integer enterpriseType, String batchNum) {
        LoginContext loginContext = LoginContext.getLoginContext();
        List<FieldDiffVO> diffFields = new ArrayList<>();
        diffFields.add(FieldDiffVO.builder()
                .fieldName(SupplierFieldBaseEnum.ENTERPRISE_TYPE.getCode())
                .fieldLabelName(SupplierFieldBaseEnum.ENTERPRISE_TYPE.getDesc())
                .sourceContent(baseInfoDb.getEnterpriseType())
                .targetContent(enterpriseType)
                .build());
        SupplierModifyRecordDTO recordDTO = SupplierModifyRecordDTO.builder().id(baseInfoDb.getId())
                .diffField(diffFields)
                .updater(loginContext.getPin())
                .updateTime(System.currentTimeMillis()).build();
        SupplierModifyRecordVO baseInfoUpdate = new SupplierModifyRecordVO();
        baseInfoUpdate.setJsonInfo(JSON.toJSONString(recordDTO));
        baseInfoUpdate.setModule(SupplierModuleEnum.BASE_INFO.getCode());
        baseInfoUpdate.setSupplierCode(baseInfoDb.getSupplierCode());
        baseInfoUpdate.setBatchNum(batchNum);
        baseInfoUpdate.setUpdater(loginContext.getPin());
        baseInfoUpdate.setAction(SupplierModifyActionEnum.UPDATE.getCode());
        baseInfoUpdate.setUpdateTime(System.currentTimeMillis());
        return baseInfoUpdate;
    }

    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public void saveModifyAndAudit(List<SupplierModifyRecordVO> records, SupplierBaseInfoPO baseInfoDb, Integer enterpriseType, String batchNum) {
        DataResponse<Boolean> savedModifyRecord = supplierModifyRecordManageService.saveOrUpdateBatch(records);
        if (!savedModifyRecord.getSuccess()) {
            log.info("updateAfterFirstAudit, modifyRecordManageService.saveOrUpdateBatch fail. finalRes={}", JSONObject.toJSONString(records));
            throw new BizException("提交待审核记录失败");
        }
        // 保存初始化审核结果信息
        SupplierModifyRecordGroupDetailVO.Request queryReq = new SupplierModifyRecordGroupDetailVO.Request();
        queryReq.setSupplierCode(baseInfoDb.getSupplierCode());
        queryReq.setBatchNum(batchNum);
        SupplierModifyRecordGroupDetailVO.Response groupDetailVo = supplierModifyRecordManageService.groupDetail(queryReq);
        //保存审核信息 + 创建xbp表单 画表单
        DataResponse<Boolean> modifyAuditRes = supplierAuditManageService.saveModifyAudit(baseInfoDb.getSupplierCode(), batchNum, groupDetailVo, baseInfoDb, enterpriseType);
        if (null == modifyAuditRes || !modifyAuditRes.getSuccess()) {
            throw new BizException("保存审批流异常");
        }
    }


    /**
     * 保存修改记录
     *
     * @param blDiffResult   BLDiffResult对象，包含了修改的差异信息
     * @param enterpriseType 供应商等级，用于区分不同类型的供应商等级
     *                       SupplierModuleEnum
     * @param batchNum       批次号，用于标识本次修改操作
     */
    private List<SupplierModifyRecordVO> handleModifyRecord(BLDiffResult blDiffResult, Integer enterpriseType, String batchNum, String operator, SupplierBaseInfoPO baseInfoDb) {
        List<SupplierModifyRecordVO> records = new ArrayList<>();
        long updateTime = System.currentTimeMillis();

        // 记录企业类型变更
        SupplierDiffInfoVO diffInfoVO = new SupplierDiffInfoVO();
        diffInfoVO.setId(baseInfoDb.getId());
        diffInfoVO.setUpdater(operator);
        diffInfoVO.setUpdateTime(updateTime);
        diffInfoVO.setDiffField(Collections.singletonList(
                CompareResult.builder()
                        .fieldName("enterpriseType")
                        .targetContent(enterpriseType)
                        .sourceContent(baseInfoDb.getEnterpriseType())
                        .build()
        ));
        records.add(buildModifyRecordVO(SupplierModuleEnum.BASE_INFO.getCode(), JSON.toJSONString(diffInfoVO), batchNum, operator, updateTime, baseInfoDb.getSupplierCode()));

        // 处理业务线新增、更新、删除
        Map<String, Object> blChanges = new LinkedHashMap<>();
        blChanges.put("UPDATE", blDiffResult.getToUpdate());
        blChanges.put("ADD", blDiffResult.getToAdd());
        blChanges.put("DELETE", blDiffResult.getToDelete());

        blChanges.forEach((action, dataList) -> {
            if (dataList != null && !((Collection<?>) dataList).isEmpty()) {
                records.add(buildModifyRecordVO(
                        SupplierModuleEnum.BUSINESS_LINE.getCode(),
                        JSON.toJSONString(dataList),
                        batchNum,
                        operator,
                        updateTime,
                        baseInfoDb.getSupplierCode()
                ));
            }
        });
        return records;
    }


    private SupplierModifyRecordVO buildModifyRecordVO(String module, String jsonInfo, String batchNum, String operator, long updateTime, String supplierCode) {
        SupplierModifyRecordVO vo = new SupplierModifyRecordVO();
        vo.setJsonInfo(jsonInfo);
        vo.setModule(module);
        vo.setBatchNum(batchNum);
        vo.setUpdater(operator);
        vo.setUpdateTime(updateTime);
        vo.setSupplierCode(supplierCode);
        vo.setAction(SupplierModifyActionEnum.UPDATE.getCode());
        return vo;
    }

    private CalculateResult filterAgentLevelAndSecondCategoryCount(BLDiffResult blDiffResult, Map<String, BusinessLineRelationPO> dbMap) {

        // 删除：从 dbMap 中移除对应的 brandId-categoryId
        blDiffResult.getToDelete().forEach(vo -> {
            String key = vo.getBrandId() + "-" + vo.getCategoryId();
            dbMap.remove(key);
        });

        // 添加：构建 PO 加入 dbMap
        blDiffResult.getToAdd().forEach(vo -> {
            String key = vo.getBrandId() + "-" + vo.getCategoryId();
            BusinessLineRelationPO po = new BusinessLineRelationPO();
            po.setBrandId(Long.valueOf(vo.getBrandId()));
            po.setCategoryId(vo.getCategoryId());
            po.setJdCatId(vo.getCategoryId());
            po.setAgentLevel(vo.getAgentLevel());
            dbMap.put(key, po);
        });

        // 修改：在 dbMap 中找到并替换值
        blDiffResult.getToUpdate().forEach(vo -> {
            String key = vo.getBrandId() + "-" + vo.getCategoryId();
            BusinessLineRelationPO po = dbMap.get(key);
            if (po != null) {
                po.setAgentLevel(vo.getAgentLevel());
                dbMap.put(key, po);
            }
        });
        cleanDbMap(dbMap);
        // 最终返回值只保留 brandId、categoryId、agentLevel
        List<Integer> agentLevels = dbMap.values().stream()
                .map(po -> {
                    // 如果 agentLevel 为 null，默认为 UNAUTHORIZED
                    return po.getAgentLevel() == null ? SupplierAgentLevelEnum.UNAUTHORIZED.getCode() : po.getAgentLevel();
                })
                .collect(Collectors.toList());

        // 收集所有有效的jdCatId
        Set<Long> catIds = dbMap.values().stream()
                .map(BusinessLineRelationPO::getJdCatId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        int secondaryCategoryCount = 0;
        if (CollectionUtils.isNotEmpty(catIds)) {
            // 调用接口 获取全路径
            List<CategoryPathVO> paths = categoryAtomicService.path(catIds);
            // 在匹配出来一共有多少二级类目的个数
            Set<Long> secondaryCategoryIds = paths.stream()
                    .map(CategoryPathVO::getId2)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            secondaryCategoryCount = secondaryCategoryIds.size();
        }

        return CalculateResult.builder().agentLevel(agentLevels).secondaryCategoryCount(secondaryCategoryCount).build();
    }
    @Resource
    private BrandAtomicService brandAtomicService;
    private void cleanDbMap(Map<String, BusinessLineRelationPO> dbMap) {
            if (dbMap == null || dbMap.isEmpty()) {
                return;
            }
            // 1. 收集所有jdCatId和brandId
            Set<Long> jdCatIds = new HashSet<>();
            Set<Long> brandIds = new HashSet<>();
            for (BusinessLineRelationPO po : dbMap.values()) {
                jdCatIds.add(po.getJdCatId());
                brandIds.add(po.getBrandId());
            }

            // 2. 批量查询
            List<CategoryPO> categoryList = categoryAtomicService.getValidPOByJdCatIds(jdCatIds);
            List<BrandPO> brandList = brandAtomicService.getByIds(brandIds);

            // 3. 转为Map，且只保留有效的
            Set<Long> validCatIdSet = categoryList.stream()
                    .filter(c -> c.getStatus() != null && c.getStatus() != 0)
                    .map(CategoryPO::getJdCatId)
                    .collect(Collectors.toSet());
            Set<Long> validBrandIdSet = brandList.stream()
                    .filter(b -> b.getStatus() != null && b.getStatus() != 0)
                    .map(BrandPO::getId)
                    .collect(Collectors.toSet());

            // 4. 过滤dbMap
            Iterator<Map.Entry<String, BusinessLineRelationPO>> iterator = dbMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, BusinessLineRelationPO> entry = iterator.next();
                BusinessLineRelationPO po = entry.getValue();
                if (!validCatIdSet.contains(po.getJdCatId()) || !validBrandIdSet.contains(po.getBrandId())) {
                    iterator.remove();
                }
            }
        }


    public Integer calculateEnterpriseType(CalculateResult calculateResult) {
        List<Integer> agentLevels = calculateResult.getAgentLevel();
        // 特殊情况处理：当所有业务线都是"无授权"时，agentLevels为空列表
        if (agentLevels == null || agentLevels.isEmpty()) {
            log.warn("供应商所有业务线均为无授权状态，默认设置为未开通产品线");
            // 没有有效授权的供应商默认为最低等级：S-集货商
            return SupplierEnterpriseTypeEnum.INIT.getCode();
        }
        
        // 统计各个等级的数量
        long factoryCount = agentLevels.stream().filter(level -> level.equals(SupplierAgentLevelEnum.FACTORY.getCode())).count();
        long primaryAgentCount = agentLevels.stream().filter(level -> level.equals(SupplierAgentLevelEnum.PRIMARY_AGENT.getCode())).count();
        // 计算原厂的数量百分比
        double factoryPercentage = (double) factoryCount / agentLevels.size() * 100;
        // 判断类型 A（原厂）、B（代理商）、C（品类商）、S（集货商）
        if (factoryPercentage == 100) {
            return SupplierEnterpriseTypeEnum.MANUFACTURER_OR_BRAND_OWNER.getCode();
        } else if (primaryAgentCount >= 0.7 * agentLevels.size()) {
            return SupplierEnterpriseTypeEnum.FIRST_LEVEL_AGENT.getCode();
        } else if (primaryAgentCount <= 0.7 * agentLevels.size() && calculateResult.getSecondaryCategoryCount() <= 3) {
            return SupplierEnterpriseTypeEnum.CATEGORY_MERCHANT.getCode();
        } else {
            return SupplierEnterpriseTypeEnum.LOCAL_TRADER.getCode();
        }
    }


    /**
     * 比较业务线变更并生成差异结果。
     *
     * @param datas        业务线编辑数据列表。
     * @param relationDb   业务线关系数据库列表。
     * @param operator     操作员。
     * @param supplierCode 供应商代码。
     * @return 业务线差异结果。
     */
    BLDiffResult compareBusinessLineChanges(List<BusinessLineEditDTO> datas, Map<String, BusinessLineRelationPO> relationDb, String operator, String supplierCode) {
        BLDiffResult result = new BLDiffResult();
        for (BusinessLineEditDTO dto : datas) {
            strategyList.stream()
                    .filter(strategy -> strategy.supports(dto.getAction()))
                    .findFirst()
                    .ifPresent(strategy -> strategy.process(dto, relationDb, result, operator, supplierCode));
        }
        return result;
    }

}
