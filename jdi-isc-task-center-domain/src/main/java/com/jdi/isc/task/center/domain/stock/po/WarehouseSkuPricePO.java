package com.jdi.isc.task.center.domain.stock.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.task.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author：xubing82
 * @date：2025/8/7 09:48
 * @description：仓报价大数据快照同步表，大数据每天凌晨3点同步一次增量数据
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("jdi_isc_warehouse_price_sharding")
public class WarehouseSkuPricePO extends BasicPO {

    private static final long serialVersionUID = 1L;

    /**
     * 仓所在国家，销售国
     */
    private String countryCode;

    /**
     * 备货仓仓库ID
     */
    private Long warehouseId;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 国际SKU原始采购币种换算国家官方币种汇率
     */
    private BigDecimal exrateSupp2country;

    /**
     * 国家官方币种换算人民币汇率
     */
    private BigDecimal exrateCountry2cny;

    /**
     * 国家官方币种
     */
    private String countryCurrency;

    /**
     * 国际sku未税仓报价,6位小数
     */
    private BigDecimal whPrice;

    /**
     * 国际sku未税仓报价-cny,6位小数
     */
    private BigDecimal whPriceCny;

    /**
     * 版本号，用于乐观锁控制并发操作
     */
    private Integer version;

}
