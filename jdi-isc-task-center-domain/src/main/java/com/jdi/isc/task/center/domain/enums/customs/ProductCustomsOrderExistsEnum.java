package com.jdi.isc.task.center.domain.enums.customs;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品关务订单存在状态枚举
 * <p>用于标识商品关务是否有关联的关务订单</p>
 */
@Getter
public enum ProductCustomsOrderExistsEnum {

    YES(1, "是"),
    NO(0, "否");

    private final Integer value;
    private final String desc;

    // 预构建value到枚举的映射，提高查找效率
    private static final Map<Integer, ProductCustomsOrderExistsEnum> VALUE_MAP =
            Arrays.stream(values())
                    .collect(Collectors.toMap(
                            ProductCustomsOrderExistsEnum::getValue,
                            Function.identity()
                    ));

    ProductCustomsOrderExistsEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据value值获取对应的枚举实例
     * @param value 枚举value值
     * @return 对应的枚举实例，未找到返回null
     */
    public static ProductCustomsOrderExistsEnum fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        return VALUE_MAP.get(value);
    }

    /**
     * 检查value值是否有效
     * @param value 待检查的value值
     * @return 有效返回true，否则false
     */
    public static boolean isValid(Integer value) {
        return VALUE_MAP.containsKey(value);
    }

    /**
     * 获取所有有效的value值
     * @return 有效value值数组
     */
    public static Integer[] validValues() {
        return VALUE_MAP.keySet().toArray(new Integer[0]);
    }

    /**
     * 获取描述信息（null安全）
     * @param value 枚举value值
     * @return 对应的描述信息，未找到返回null
     */
    public static String getDescByValue(Integer value) {
        ProductCustomsOrderExistsEnum e = fromValue(value);
        return e != null ? e.getDesc() : null;
    }

    @Override
    public String toString() {
        return value + ":" + desc;
    }
}