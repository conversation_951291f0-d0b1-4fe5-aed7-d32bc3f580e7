package com.jdi.isc.task.center.domain.task.excel.hsCode;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.task.center.domain.task.dto.BaseWorkerDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;


/**
 * 匈牙利商品关税导入实体
 * 注意：不要随意修改 ExcelProperty 注解的value值，校验excel模板是否正确时，value值会和excel表头比较
 *
 * <AUTHOR>
 * @date 20250720
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class HsCodeHuExcelDTO extends BaseWorkerDTO {

    // 注意：不要随意修改 ExcelProperty 注解的value值，校验excel模板是否正确时，value值会和excel表头比较

    /**
     * hsCode
     */
    @NotNull(message = "hsCode(TRAIC)码不能为空")
    @Pattern(regexp="(^[0-9]{1,15}$)", message = "hsCode(TRAIC)只能是长度介于1-15位的纯数字字符串")
    @ExcelProperty(index = 0, value = "* HScode(TARIC)")
    private String hsCode;

    /**
     * 进口关税率
     */
    @NotNull(message = "进口关税率不能为空")
    @DecimalMin(value = "0", message = "进口关税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "进口关税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "进口关税率最多两位小数")
    @ExcelProperty(index = 1, value = "* 进口关税率import duty%")
    private String importTax;

    /**
     * 反倾销税率
     */
    @NotNull(message = "反倾销税率不能为空")
    @DecimalMin(value = "0", message = "反倾销税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "反倾销税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "反倾销税率最多两位小数")
    @ExcelProperty(index = 2, value = "* 反倾销税率anti-dumping duty%")
    private String antiDumpingTax;

    /**
     * 反补贴税率
     */
    @NotNull(message = "反补贴税率不能为空")
    @DecimalMin(value = "0", message = "反补贴税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "反补贴税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "反补贴税率最多两位小数")
    @ExcelProperty(index = 3, value = "* 反补贴税率countervailing duty%")
    private String antiSubsidyTax;

    /**
     * 增值税率
     */
    @NotNull(message = "增值税率不能为空")
    @DecimalMin(value = "0", message = "增值税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "增值税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "增值税率最多两位小数")
    @ExcelProperty(index = 4, value = "* 增值税率VAT%")
    private String valueAddedTax;

    /**
     * 是否管制
     */
    @NotNull(message = "是否管制不能为空")
    @ExcelProperty(index = 5, value = "* 是否管制")
    private String controls;

    /**
     * 管制信息
     */
    @Length(max = 500, message = "管制信息长度不能超过500")
    @ExcelProperty(index = 6, value = "管制信息（是否管制为是时，必填）")
    private String controlsInfo;

    /**
     * 备注
     */
    @Length(max = 100, message = "备注长度不能超过100")
    @ExcelProperty(index = 7, value = "备注")
    private String remark;

}


    
