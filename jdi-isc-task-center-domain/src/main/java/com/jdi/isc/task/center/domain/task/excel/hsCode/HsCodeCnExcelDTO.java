package com.jdi.isc.task.center.domain.task.excel.hsCode;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.task.center.domain.task.dto.BaseWorkerDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;


/**
 * 中国商品关税导入实体
 * 注意：不要随意修改 ExcelProperty 注解的value值，校验excel模板是否正确时，value值会和excel表头比较
 *
 * <AUTHOR>
 * @date 20250720
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class HsCodeCnExcelDTO extends BaseWorkerDTO {

    // 注意：不要随意修改 ExcelProperty 注解的value值，校验excel模板是否正确时，value值会和excel表头比较

    /**
     * 海关编码
     */
    @NotNull(message = "海关编码（HSCODE）不能为空")
    @Pattern(regexp="(^[0-9]{1,15}$)", message = "海关编码（HSCODE）只能是长度介于1-15位的纯数字字符串")
    @ExcelProperty(index = 0, value = "* 海关编码（HSCODE）")
    private String hsCode;

    /**
     * 出口退税率
     */
    @NotNull(message = "出口退税率不能为空")
    @DecimalMin(value = "0", message = "出口退税率应介于0%-100%之间")
    @DecimalMax(value = "100", message = "出口退税率应介于0%-100%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "出口退税率最多两位小数")
    @ExcelProperty(index = 1, value = "* 出口退税率%")
    private String exportRebateRate;

    /**
     * 出口关税率
     */
    @DecimalMin(value = "0", message = "出口关税率应介于0%-100%之间")
    @DecimalMax(value = "100", message = "出口关税率应介于0%-100%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "出口关税率最多两位小数")
    @ExcelProperty(index = 2, value = "出口关税率%")
    private String exportTaxRate;

    /**
     * 海关控制条件
     */
//    @NotNull(message = "海关控制条件不能为空")
//    @Length(max = 100, message = "海关控制条件长度不能超过100")
    @ExcelIgnore
    private String customsCondition;

    /**
     * 检验检疫类别
     */
//    @NotNull(message = "检验检疫类别不能为空")
//    @Length(max = 100, message = "检验检疫类别长度不能超过100")
    @ExcelIgnore
    private String quarantineCat;

    /**
     * 是否管制
     */
    @NotNull(message = "是否管制不能为空")
    @ExcelProperty(index = 3, value = "* 是否管制")
    private String controls;

    /**
     * 管制信息
     */
    @Length(max = 500, message = "管制信息长度不能超过500")
    @ExcelProperty(index = 4, value = "管制信息（是否管制为是时，必填）")
    private String controlsInfo;

    /**
     * 备注
     */
    @Length(max = 100, message = "备注长度不能超过100")
    @ExcelProperty(index = 5, value = "备注")
    private String remark;

}


    
