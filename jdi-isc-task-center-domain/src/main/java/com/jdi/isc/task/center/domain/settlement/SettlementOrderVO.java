package com.jdi.isc.task.center.domain.settlement;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class SettlementOrderVO {
    /**
     * 结算单号
     */
    private String requestId;

    /**
     * 所属国家
     */
    private  String countryCode;


    /**
     * 供应商编码
     */
    private String supplierId;

    /**
     * 状态
     * CREATE 新生成的结算单
     */
    private String saveOrUpdate;

    /**
     * 结算订单时间
     */

    private String createTime;
}
