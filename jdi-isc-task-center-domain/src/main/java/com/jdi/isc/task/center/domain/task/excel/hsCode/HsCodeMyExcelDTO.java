package com.jdi.isc.task.center.domain.task.excel.hsCode;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.task.center.domain.task.dto.BaseWorkerDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;


/**
 * 马来商品关税导入实体
 * 注意：不要随意修改 ExcelProperty 注解的value值，校验excel模板是否正确时，value值会和excel表头比较
 *
 * <AUTHOR>
 * @date 20250720
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class HsCodeMyExcelDTO extends BaseWorkerDTO {

    // 注意：不要随意修改 ExcelProperty 注解的value值，校验excel模板是否正确时，value值会和excel表头比较

    /**
     * HScode
     */
    @NotNull(message = "海关编码hsCode不能为空")
    @Pattern(regexp="(^[0-9]{1,15}$)", message = "海关编码（HSCODE）只能是长度介于1-15位的纯数字字符串")
    @ExcelProperty(index = 0, value = "* HScode")
    private String hsCode;

    /**
     * 最惠国关税率
     */
    @NotNull(message = "最惠国关税率MFN不能为空")
    @DecimalMin(value = "0", message = "最惠国关税率MFN应介于0%-300%之间")
    @DecimalMax(value = "300", message = "最惠国关税率MFN应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "最惠国关税率MFN最多两位小数")
    @ExcelProperty(index = 1, value = "* 最惠国关税率MFN%")
    private String mfnTax;

    /**
     * 原产地优惠关税率FormE
     */
    @NotNull(message = "原产地优惠关税率FormE不能为空")
    @DecimalMin(value = "0", message = "原产地优惠关税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "原产地优惠关税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "原产地优惠关税率最多两位小数")
    @ExcelProperty(index = 2, value = "* 原产地优惠关税率FormE%")
    private String originMfnTax;

    /**
     * 销售税率SST
     */
    @NotNull(message = "销售税率SST不能为空")
    @DecimalMin(value = "0", message = "销售税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "销售税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "销售税率最多两位小数")
    @ExcelProperty(index = 3, value = "* 销售税率SST%")
    private String saleTax;

    /**
     * 反倾销税率
     */
    @DecimalMin(value = "0", message = "反倾销税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "反倾销税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "反倾销税率最多两位小数")
    @ExcelProperty(index = 4, value = "反倾销税率Anti_dumping Tax%")
    private String antiDumpingTax;

    /**
     * 是否管制
     */
    @NotNull(message = "是否管制不能为空")
    @ExcelProperty(index = 5, value = "* 是否管制")
    private String controls;

    /**
     * 管制信息
     */
    @Length(max = 500, message = "管制信息长度不能超过500")
    @ExcelProperty(index = 6, value = "管制信息（是否管制为是时，必填）")
    private String controlsInfo;

    /**
     * 备注
     */
    @Length(max = 100, message = "备注长度不能超过100")
    @ExcelProperty(index = 7, value = "备注")
    private String remark;

}


    
