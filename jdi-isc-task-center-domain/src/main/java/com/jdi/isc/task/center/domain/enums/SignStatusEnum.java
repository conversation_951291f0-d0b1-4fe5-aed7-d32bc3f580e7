package com.jdi.isc.task.center.domain.enums;

import java.util.Arrays;

/**
 * 签收单审批状态 0待审批1已通过2未通过
 * <AUTHOR>
 * @date 20230803
 **/
public enum SignStatusEnum {

    PASS(1, "已通过"),
    REFUSE(2, "未通过"),
    PENDING(0, "待审批");

    private Integer code;
    private String name;
    private SignStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }


    public String getName() {
        return this.name;
    }


    public static SignStatusEnum getEnumByCode(Integer code) {
        if (code != null) {
            SignStatusEnum[] var1 = values();
            for (SignStatusEnum system : var1) {
                if (code.equals(system.getCode())) {
                    return system;
                }
            }
            return null;
        } else {
            return null;
        }
    }

    /**
     * 根据状态码获取状态名称
     */
    public static String getNameByCode(Integer code) {
        SignStatusEnum status = getByCode(code);
        return status != null ? status.getName() : null;
    }
    /**
     * 根据状态码获取枚举
     */
    public static SignStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }

        return Arrays.stream(SignStatusEnum.values())
                .filter(status -> status.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
