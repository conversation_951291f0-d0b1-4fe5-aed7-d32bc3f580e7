package com.jdi.isc.task.center.domain.task.enums;

import com.jdi.isc.task.center.domain.task.excel.DynamicExportFieldConfig;
import com.jdi.isc.task.center.domain.task.excel.ExportOrderAggDTO;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * ExportOrderAggDTO字段配置工具类
 * <AUTHOR>
 * @date 2025/8/19
 */
@Slf4j
public class ExportOrderAggDTOFieldUtils {

    private static final Map<String, DynamicExportFieldConfig> FIELD_CONFIG_MAP = new LinkedHashMap<>();

    private static final String  DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    static {
        // 按照ExportOrderAggDTO中字段定义的顺序初始化配置
        FIELD_CONFIG_MAP.put("orderId", DynamicExportFieldConfig.builder()
                .fieldName("orderId")
                .displayName("WIMP订单号")
                .fieldType(ExportFieldTypeEnum.INTEGER.getCode())
                .required(true)
                .sort(1)
                .build());
        FIELD_CONFIG_MAP.put("sourceCode", DynamicExportFieldConfig.builder()
                .fieldName("sourceCode")
                .displayName("订单来源")
                .fieldType(ExportFieldTypeEnum.STRING.getCode())
                .required(true)
                .sort(2)
                .build());

        FIELD_CONFIG_MAP.put("parentOrderId", DynamicExportFieldConfig.builder()
                .fieldName("parentOrderId")
                .displayName("父订单号")
                .fieldType(ExportFieldTypeEnum.INTEGER.getCode())
                .required(true)
                .sort(3)
                .build());


        FIELD_CONFIG_MAP.put("thirdOrderId", DynamicExportFieldConfig.builder()
                .fieldName("thirdOrderId")
                .displayName("三方订单号")
                .fieldType(ExportFieldTypeEnum.STRING.getCode())
                .required(true)
                .sort(4)
                .build());

        FIELD_CONFIG_MAP.put("iopOrderId", DynamicExportFieldConfig.builder()
                .fieldName("iopOrderId")
                .displayName("内贸订单号")
                .fieldType(ExportFieldTypeEnum.STRING.getCode())
                .required(false)
                .sort(5)
                .build());

        FIELD_CONFIG_MAP.put("purchaseOrderId", DynamicExportFieldConfig.builder()
                .fieldName("purchaseOrderId")
                .displayName("采购单号")
                .fieldType(ExportFieldTypeEnum.STRING.getCode())
                .required(true)
                .sort(6)
                .build());

        FIELD_CONFIG_MAP.put("orderShowType", DynamicExportFieldConfig.builder()
                .fieldName("orderShowType")
                .displayName("业务模式")
                .fieldType(ExportFieldTypeEnum.ENUM_TYPE.getCode())
                .enumClassName("com.jdi.isc.task.center.domain.enums.OrderShowTypeEnum")
                .required(true)
                .sort(7)
                .build());

        FIELD_CONFIG_MAP.put("orderStatus", DynamicExportFieldConfig.builder()
                .fieldName("orderStatus")
                .displayName("订单状态")
                .fieldType(ExportFieldTypeEnum.ENUM_TYPE.getCode())
                .enumClassName("com.jdi.isc.task.center.domain.enums.OrderStatusEnum")
                .required(true)
                .sort(8)
                .build());

        FIELD_CONFIG_MAP.put("pin", DynamicExportFieldConfig.builder()
                .fieldName("pin")
                .displayName("下单pin（下单账号）")
                .fieldType(ExportFieldTypeEnum.STRING.getCode())
                .required(true)
                .sort(9)
                .build());

        FIELD_CONFIG_MAP.put("contractNum", DynamicExportFieldConfig.builder()
                .fieldName("contractNum")
                .displayName("合同号")
                .fieldType(ExportFieldTypeEnum.STRING.getCode())
                .required(true)
                .sort(10)
                .build());

        FIELD_CONFIG_MAP.put("clientName", DynamicExportFieldConfig.builder()
                .fieldName("clientName")
                .displayName("客户名称")
                .fieldType(ExportFieldTypeEnum.STRING.getCode())
                .required(true)
                .sort(11)
                .build());

        FIELD_CONFIG_MAP.put("currency", DynamicExportFieldConfig.builder()
                .fieldName("currency")
                .displayName("币种")
                .fieldType(ExportFieldTypeEnum.STRING.getCode())
                .required(true)
                .sort(12)
                .build());

        FIELD_CONFIG_MAP.put("orderTotalPrice", DynamicExportFieldConfig.builder()
                .fieldName("orderTotalPrice")
                .displayName("订单金额")
                .fieldType(ExportFieldTypeEnum.DECIMAL.getCode())
                .required(true)
                .sort(13)
                .build());

        FIELD_CONFIG_MAP.put("orderCreateTime", DynamicExportFieldConfig.builder()
                .fieldName("orderCreateTime")
                .displayName("下单时间")
                .fieldType(ExportFieldTypeEnum.DATETIME.getCode())
                .dateFormat(DATE_FORMAT)
                .required(true)
                .sort(14)
                .build());

        FIELD_CONFIG_MAP.put("orderConfirmTimeStr", DynamicExportFieldConfig.builder()
                .fieldName("orderConfirmTimeStr")
                .displayName("客户审核完成时间")
                .fieldType(ExportFieldTypeEnum.STRING.getCode())
                .required(true)
                .sort(15)
                .build());

        FIELD_CONFIG_MAP.put("orderConfirmTime", DynamicExportFieldConfig.builder()
                .fieldName("orderConfirmTime")
                .displayName("订单确认时间")
                .fieldType(ExportFieldTypeEnum.DATETIME.getCode())
                .dateFormat(DATE_FORMAT)
                .required(true)
                .sort(16)
                .build());

        FIELD_CONFIG_MAP.put("enterWarehouseName", DynamicExportFieldConfig.builder()
                .fieldName("enterWarehouseName")
                .displayName("备货仓名称")
                .fieldType(ExportFieldTypeEnum.STRING.getCode())
                .required(true)
                .sort(17)
                .build());

        FIELD_CONFIG_MAP.put("purchaseWaybillNum", DynamicExportFieldConfig.builder()
                .fieldName("purchaseWaybillNum")
                .displayName("一段运单号")
                .fieldType(ExportFieldTypeEnum.STRING.getCode())
                .required(false)
                .sort(18)
                .build());

        FIELD_CONFIG_MAP.put("promisedShipmentTime", DynamicExportFieldConfig.builder()
                .fieldName("promisedShipmentTime")
                .displayName("计划发货时间")
                .fieldType(ExportFieldTypeEnum.DATETIME.getCode())
                .dateFormat(DATE_FORMAT)
                .required(false)
                .sort(19)
                .build());

        FIELD_CONFIG_MAP.put("shippedTime", DynamicExportFieldConfig.builder()
                .fieldName("shippedTime")
                .displayName("发货时间")
                .fieldType(ExportFieldTypeEnum.DATETIME.getCode())
                .dateFormat(DATE_FORMAT)
                .required(true)
                .sort(20)
                .build());

        FIELD_CONFIG_MAP.put("enterStorehouseTime", DynamicExportFieldConfig.builder()
                .fieldName("enterStorehouseTime")
                .displayName("集运中心入仓时间")
                .fieldType(ExportFieldTypeEnum.DATETIME.getCode())
                .dateFormat(DATE_FORMAT)
                .required(false)
                .sort(21)
                .build());

        FIELD_CONFIG_MAP.put("outStorehouseTime", DynamicExportFieldConfig.builder()
                .fieldName("outStorehouseTime")
                .displayName("集运中心发货时间")
                .fieldType(ExportFieldTypeEnum.DATETIME.getCode())
                .dateFormat(DATE_FORMAT)
                .required(false)
                .sort(22)
                .build());

        FIELD_CONFIG_MAP.put("enterpriseWarehouseId", DynamicExportFieldConfig.builder()
                .fieldName("enterpriseWarehouseId")
                .displayName("企配名称")
                .fieldType(ExportFieldTypeEnum.STRING.getCode())
                .required(false)
                .sort(23)
                .build());

        FIELD_CONFIG_MAP.put("orderWaybillNum", DynamicExportFieldConfig.builder()
                .fieldName("orderWaybillNum")
                .displayName("二段运单号")
                .fieldType(ExportFieldTypeEnum.STRING.getCode())
                .required(true)
                .sort(24)
                .build());

        FIELD_CONFIG_MAP.put("enterWarehouseTime", DynamicExportFieldConfig.builder()
                .fieldName("enterWarehouseTime")
                .displayName("企配收货时间")
                .fieldType(ExportFieldTypeEnum.DATETIME.getCode())
                .dateFormat(DATE_FORMAT)
                .required(false)
                .sort(25)
                .build());

        FIELD_CONFIG_MAP.put("outWarehouseTime", DynamicExportFieldConfig.builder()
                .fieldName("outWarehouseTime")
                .displayName("企配发货时间")
                .fieldType(ExportFieldTypeEnum.DATETIME.getCode())
                .dateFormat(DATE_FORMAT)
                .required(false)
                .sort(26)
                .build());

        FIELD_CONFIG_MAP.put("deliveryCustomerTime", DynamicExportFieldConfig.builder()
                .fieldName("deliveryCustomerTime")
                .displayName("实物妥投时间")
                .fieldType(ExportFieldTypeEnum.DATETIME.getCode())
                .dateFormat(DATE_FORMAT)
                .required(true)
                .sort(27)
                .build());

        FIELD_CONFIG_MAP.put("promisedDeliveryTime", DynamicExportFieldConfig.builder()
                .fieldName("promisedDeliveryTime")
                .displayName("承诺送达时间")
                .fieldType(ExportFieldTypeEnum.DATETIME.getCode())
                .dateFormat(DATE_FORMAT)
                .required(true)
                .sort(28)
                .build());

        FIELD_CONFIG_MAP.put("signStatus", DynamicExportFieldConfig.builder()
                .fieldName("signStatus")
                .displayName("签单状态")
                .fieldType(ExportFieldTypeEnum.ENUM_TYPE.getCode())
                .enumClassName("com.jdi.isc.task.center.domain.enums.SignStatusEnum")
                .required(true)
                .sort(29)
                .build());

        FIELD_CONFIG_MAP.put("deliverySubmittedTime", DynamicExportFieldConfig.builder()
                .fieldName("deliverySubmittedTime")
                .displayName("系统妥投时间")
                .fieldType(ExportFieldTypeEnum.DATETIME.getCode())
                .dateFormat(DATE_FORMAT)
                .required(true)
                .sort(30)
                .build());

        FIELD_CONFIG_MAP.put("orderFinishTime", DynamicExportFieldConfig.builder()
                .fieldName("orderFinishTime")
                .displayName("完成时间")
                .fieldType(ExportFieldTypeEnum.DATETIME.getCode())
                .dateFormat(DATE_FORMAT)
                .required(true)
                .sort(31)
                .build());

        FIELD_CONFIG_MAP.put("holdStatus", DynamicExportFieldConfig.builder()
                .fieldName("holdStatus")
                .displayName("hold单状态")
                .fieldType(ExportFieldTypeEnum.ENUM_TYPE.getCode())
                .enumClassName("com.jdi.isc.task.center.domain.enums.OrderHoldStatusEnum")
                .required(false)
                .sort(32)
                .build());
    }

    /**
     * 根据字段名获取字段配置
     * @param fieldName 字段名
     * @return 字段配置对象
     */
    public static DynamicExportFieldConfig getFieldConfig(String fieldName) {
        return FIELD_CONFIG_MAP.get(fieldName);

    }

    /**
     * 获取所有字段配置
     * @return 字段配置Map
     */
    public static Map<String, DynamicExportFieldConfig> getAllFieldConfigs() {
        return new LinkedHashMap<>(FIELD_CONFIG_MAP);
    }

    /**
     * 根据required参数获取字段集合
     * @param required 是否必需，true表示获取所有必需字段，false表示获取所有非必需字段
     * @return 符合条件的字段配置Map
     */
    public static Map<String, DynamicExportFieldConfig> getFieldConfigsByRequired(boolean required) {
        Map<String, DynamicExportFieldConfig> result = new LinkedHashMap<>();
        
        for (Map.Entry<String, DynamicExportFieldConfig> entry : FIELD_CONFIG_MAP.entrySet()) {
            DynamicExportFieldConfig config = entry.getValue();
            // 根据required参数筛选字段
            if (config.getRequired().equals(required)) {
                result.put(entry.getKey(), config);
            }
        }
        
        return result;
    }
}