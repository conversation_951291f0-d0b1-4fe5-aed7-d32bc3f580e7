package com.jdi.isc.task.center.domain.enums.customs;


import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 强制认证枚举
 */

@Getter
public enum CompulsoryCertificationEnum {

    Not_Required(0,"否"),
    Required(1,"是");

    private Integer code;

    private String desc;

    public static Map<Integer, CompulsoryCertificationEnum> enumMap = new HashMap();
    public static Map<Integer, String> codeDescMap = new HashMap();

    static {
        for (CompulsoryCertificationEnum val : values()) {
            enumMap.put(val.getCode(), val);
            codeDescMap.put(val.getCode(), val.getDesc());
        }
    }

    CompulsoryCertificationEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CompulsoryCertificationEnum forCode(Integer code) {
        if (null==code) {
            return null;
        }
        return enumMap.get(code);
    }

    public static String code2Desc(Integer code) {
        if (null==code) {
            return null;
        }
        return codeDescMap.get(code);
    }
}
