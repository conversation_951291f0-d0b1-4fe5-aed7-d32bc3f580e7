package com.jdi.isc.task.center.domain.joysky.msg;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/11/4
 * @Description 审批流业务结果消息
 */
@Data
public class ApprovalResultMsg {
    /**
     * 审批流任务类型码
     */
    private Integer processType;

    /**
     * 审批流程key编码
     */
    private String processKey;

    /**
     * 流程实例id，一次流程的唯一标识
     */
    private String processInstanceId;

    /**
     * 审批流状态
     *
     * @see com.jdi.isc.task.center.domain.enums.ApprovalStatusEnum
     */
    private Integer processStatus;

    /**
     * 驳回原因(驳回&填写了原因才有值)
     */
    private String rejectReason;

    /**
     * 审批操作人
     */
    private String operator;

}
