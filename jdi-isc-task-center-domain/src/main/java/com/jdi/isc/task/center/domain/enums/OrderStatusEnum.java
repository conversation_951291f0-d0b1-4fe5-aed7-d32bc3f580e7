package com.jdi.isc.task.center.domain.enums;

import java.util.Arrays;

/**
 * 订单状态枚举（完整版本）
 */
public enum OrderStatusEnum {

    SUBMIT_ORDER(1, "提单", 1, "客户已提单", 1, "已提单"),
    PRICE_CONFIRM(11, "运营已确认", 1, "客户已提单", 2, "待确认"),
    USER_CONFIRM(12, "客户已确认", 1, "客户已提单", 3, "待审批"),
    CANCEL_ORDER(2, "已取消", 10, "客户已取消", 99, "已取消"),
    CONFIRM_ORDER(3, "客户审批完成", 3, "待发货", 4, "发货中"),
    SPLIT_ORDER(301, "商品数据问题，订单下传失败", 3, "待发货", 4, "发货中"),
    CANCEL_INNER_ORDER_FINISH(302, "已取消跨境采购单", 3, "待发货", 4, "发货中"),
    PRE_SPLIT_ORDER_FINISH(303, "已提交跨境采购单", 3, "待发货", 4, "发货中"),
    PRE_SPLIT_ORDER_FAIL(304, "预拆单对应采购单创建失败", 3, "待发货", 4, "发货中"),
    CONFIRM_ORDER_SUBMIT_EPT(31, "已提交ept订单", 3, "待发货", 4, "发货中"),
    CANCEL_INNER_ORDER(32, "已取消内贸段订单", 3, "待发货", 4, "发货中"),
    CONFIRM_INNER_ORDER(33, "已确认跨境采购单", 3, "待发货", 4, "发货中"),
    SHIPPED(80, "已发货", 12, "订单已发货", 80, "已发货"),
    DELIVERY_SUBMITTED(90, "已收货", 5, "订单已妥投", 90, "已妥投"),
    FINISH_ORDER(99, "已完成", 31, "订单已完成", 5, "已完成");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 扩展字段1
     */
    private final Integer extField1;

    /**
     * 扩展字段2
     */
    private final String extField2;

    /**
     * 扩展字段3
     */
    private final Integer extField3;

    /**
     * 扩展字段4
     */
    private final String extField4;

    OrderStatusEnum(Integer code, String name, Integer extField1, String extField2,
                    Integer extField3, String extField4) {
        this.code = code;
        this.name = name;
        this.extField1 = extField1;
        this.extField2 = extField2;
        this.extField3 = extField3;
        this.extField4 = extField4;
    }

    // Getter 方法
    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public Integer getExtField1() {
        return extField1;
    }

    public String getExtField2() {
        return extField2;
    }

    public Integer getExtField3() {
        return extField3;
    }

    public String getExtField4() {
        return extField4;
    }

    /**
     * 根据状态码获取枚举
     */
    public static OrderStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }

        return Arrays.stream(OrderStatusEnum.values())
                .filter(status -> status.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据状态码获取状态名称
     */
    public static String getNameByCode(Integer code) {
        OrderStatusEnum status = getByCode(code);
        return status != null ? status.getName() : null;
    }
}