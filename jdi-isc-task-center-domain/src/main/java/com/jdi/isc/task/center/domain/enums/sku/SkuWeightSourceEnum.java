package com.jdi.isc.task.center.domain.enums.sku;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description：SKU重量来源
 * @Date 2025-04-27
 */
@Getter
@AllArgsConstructor
public enum SkuWeightSourceEnum {
    SUPPLIER_INPUT(0,"供应商填写"),
    WAREHOUSE_SYNC(1,"集运中心仓回传")
    ;

    private Integer code;

    private String desc;

    public static Map<Integer, SkuWeightSourceEnum> enumMap = new HashMap();
    public static Map<Integer, String> codeDescMap = new HashMap();

    static {
        for (SkuWeightSourceEnum val : values()) {
            enumMap.put(val.getCode(), val);
            codeDescMap.put(val.getCode(), val.getDesc());
        }
    }

    public static SkuWeightSourceEnum forCode(Integer code) {
        if (null==code) {
            return null;
        }
        return enumMap.get(code);
    }

    public static String code2Desc(Integer code) {
        if (null==code) {
            return null;
        }
        return codeDescMap.get(code);
    }
}
