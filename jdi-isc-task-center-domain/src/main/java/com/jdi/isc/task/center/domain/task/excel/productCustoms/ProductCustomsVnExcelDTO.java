package com.jdi.isc.task.center.domain.task.excel.productCustoms;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.task.center.domain.task.dto.BaseWorkerDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;


/**
 * @Description: 越南商品关务导入实体
 * 注意：不要随意修改 ExcelProperty 注解的value值，校验excel模板是否正确时，value值会和excel表头比较
 *
 * <AUTHOR>
 * @date 20250720
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProductCustomsVnExcelDTO extends BaseWorkerDTO {
    // 注意：不要随意修改 ExcelProperty 注解的value值，校验excel模板是否正确时，value值会和excel表头比较

    /**
     * 国内skuId
     */
    @NotNull(message = "国内jdSkuId不能为空")
    @ExcelProperty(index = 0, value = "*skuId-国内\nSKU ID (China)")
    private Long jdSkuId;

    /**
     * 国际skuId
     */
    @ExcelProperty(index = 1, value = "skuId-国际\nSKU ID (International)")
    private Long skuId;

    /**
     * 申报品名
     */
    @ExcelProperty(index = 2, value = "申报英文品名\nProduct Name(English)")
    private String declarationName;

    /**
     * 申报多语言名
     */
    @ExcelProperty(index = 3, value = "申报越南品名\nProduct Name (Vietnam)")
    private String declarationLocalName;

    /**
     * HScode
     */
    @NotNull(message = "海关编码hsCode不能为空")
    @Pattern(regexp="(^[0-9]{1,15}$)", message = "海关编码（HSCODE）只能是长度介于1-15位的纯数字字符串")
    @ExcelProperty(index = 4, value = "*HSCODE")
    private String hsCode;

    /**
     * 最惠国关税率
     */
    @NotNull(message = "最惠国关税率不能为空")
    @DecimalMin(value = "0", message = "最惠国关税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "最惠国关税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "最惠国关税率最多两位小数")
    @ExcelProperty(index = 5, value = "*最惠国关税MFN %")
    private String mfnTax;

    /**
     * 原产地优惠关税率FormE
     */
    @NotNull(message = "原产地优惠关税率不能为空")
    @DecimalMin(value = "0", message = "原产地优惠关税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "原产地优惠关税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "原产地优惠关税率最多两位小数")
    @ExcelProperty(index = 6, value = "*原产地优惠关税\nForm E %")
    private String originMfnTax;

    /**
     * 消费税率
     */
    @DecimalMin(value = "0", message = "消费税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "消费税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "消费税率最多两位小数")
    @ExcelProperty(index = 7, value = "消费税率\nSpecial Consumption Tax %")
    private String consumptionTax;

    /**
     * 环保税单价
     */
    @DecimalMin(value = "0", message = "环保税单价应介于0-999999999999之间")
    @DecimalMax(value = "999999999999", message = "环保税单价应介于0-999999999999之间")
    @ExcelProperty(index = 8, value = "环保税单价(¥)\nEnvironmental Tax")
    private String environmentalTaxUnitPrice;

    /**
     * 反倾销税率
     */
    @NotNull(message = "反倾销税率不能为空")
    @DecimalMin(value = "0", message = "反倾销税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "反倾销税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "反倾销税率最多两位小数")
    @ExcelProperty(index = 9, value = "*反倾销税Anti_dumpingTax %")
    private String antiDumpingTax;

    /**
     * 增值税率
     */
    @NotNull(message = "增值税率不能为空")
    @DecimalMin(value = "0", message = "增值税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "增值税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "增值税率最多两位小数")
    @ExcelProperty(index = 10, value = "*增值税\nVAT %")
    private String valueAddedTax;

    /**
     * 是否管制
     */
    @NotNull(message = "是否管制不能为空")
    @ExcelProperty(index = 11, value = "*是否管制\nImport Restricted or Not")
    private String controls;

    /**
     * 管制信息
     */
    @Length(max = 500, message = "管制信息长度不能超过500")
    @ExcelProperty(index = 12, value = "*管制信息（是否管制为是时，必填）\n* Restriction Information (Mandatory if Restricted)")
    private String controlsInfo;

    /**
     * 品牌授权 有限制/无限制
     */
    @ExcelProperty(index = 13, value = "品牌授权（越南）有限制/无限制\nBrand Authorization （Vietnam） required")
    private String brandLicensing;

    /**
     * 备注
     */
    @Length(max = 100, message = "备注长度不能超过100")
    @ExcelProperty(index = 14, value = "备注Remarks")
    private String remark;


}


    
