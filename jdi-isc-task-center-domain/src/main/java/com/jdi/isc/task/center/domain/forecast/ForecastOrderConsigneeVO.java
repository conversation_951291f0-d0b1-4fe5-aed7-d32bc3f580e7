package com.jdi.isc.task.center.domain.forecast;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/11
 */

@Data
public class ForecastOrderConsigneeVO {


    /**
     * 订单id
     */
    private String forecastOrderId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 企配仓编码
     */
    private String enterpriseWarehouseCode;

    /**
     * 收货人id
     */
    private Long consigneeId;

    /**
     * 收货人名称
     */
    private String consigneeEncrypt;

    /**
     * 收货人名称
     */
    private String consigneeIndex;

    /**
     * 收货详细地址
     */
    private String consigneeAddressEncrypt;

    /**
     * 收货详细地址
     */
    private String consigneeAddressIndex;

    /**
     * 收货人电话
     */
    private String consigneePhoneEncrypt;

    /**
     * 收货人电话
     */
    private String consigneePhoneIndex;

    /**
     * 收货人手机号
     */
    private String consigneeMobileEncrypt;

    /**
     * 收货人手机号
     */
    private String consigneeMobileIndex;

    /**
     * 收货人邮箱
     */
    private String consigneeEmailEncrypt;

    /**
     * 收货人邮箱
     */
    private String consigneeEmailIndex;

    /**
     * 收货国家
     */
    private String consigneeCountry;

    /**
     * 收货地址省ID
     */
    private Long consigneeProvinceId;


    private Map<String,String> countyNameMap;

    private Map<String,String> provinceNameMap;

    private Map<String,String> cityNameMap;

    private Map<String,String> townNameMap;

    private Map<String,String> countryNameMap;

    /**
     * 收货地址城市ID
     */
    private Long consigneeCityId;

    /**
     * 收货地址区县ID
     */
    private Long consigneeCountyId;

    /**
     * 收货地址乡镇ID
     */
    private Long consigneeTownId;

    /**
     * 邮政编码
     */
    private String consigneeZip;

    /**
     * 岗位
     */
    private String position;
}