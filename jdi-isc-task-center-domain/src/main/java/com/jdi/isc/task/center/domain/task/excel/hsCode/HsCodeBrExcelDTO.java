package com.jdi.isc.task.center.domain.task.excel.hsCode;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.task.center.domain.task.dto.BaseWorkerDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;


/**
 * 巴西商品关税导入实体
 * 注意：不要随意修改 ExcelProperty 注解的value值，校验excel模板是否正确时，value值会和excel表头比较
 *
 * <AUTHOR>
 * @date 20250720
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class HsCodeBrExcelDTO extends BaseWorkerDTO {

    // 注意：不要随意修改 ExcelProperty 注解的value值，校验excel模板是否正确时，value值会和excel表头比较

    /**
     * ncmCode
     */
    @NotNull(message = "ncmCode码不能为空")
    @Pattern(regexp="(^[0-9]{1,15}$)", message = "ncmCode码只能是长度介于1-15位的纯数字字符串")
    @ExcelProperty(index = 0, value = "* NCM code")
    private String hsCode;

    /**
     * 进口关税率II
     */
    @NotNull(message = "进口关税率II不能为空")
    @DecimalMin(value = "0", message = "进口关税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "进口关税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "进口关税率最多两位小数")
    @ExcelProperty(index = 1, value = "* 进口关税率II%")
    private String importTax;

    /**
     * 工业产品税率IPI
     */
    @NotNull(message = "工业产品税率IPI不能为空")
    @DecimalMin(value = "0", message = "工业产品税税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "工业产品税税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "工业产品税税率最多两位小数")
    @ExcelProperty(index = 2, value = "* 工业产品税率IPI%")
    private String industryProductTax;

    /**
     * 社会一体化费率PIS
     */
    @NotNull(message = "社会一体化费率PIS不能为空")
    @DecimalMin(value = "0", message = "社会一体化费率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "社会一体化费率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "社会一体化费率最多两位小数")
    @ExcelProperty(index = 3, value = "* 社会一体化费率PIS%")
    private String socialIntegrationTax;

    /**
     * CONFINS率
     */
    @NotNull(message = "社会保险融资税率CONFINS不能为空")
    @DecimalMin(value = "0", message = "社会保险融资税率CONFINS应介于0%-300%之间")
    @DecimalMax(value = "300", message = "社会保险融资税率CONFINS应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "社会保险融资税率CONFINS最多两位小数")
    @ExcelProperty(index = 4, value = "* 社会保险融资税率CONFINS%")
    private String confinsTax;

    /**
     * ICMS流转税率
     */
    @NotNull(message = "ICMS流转税率不能为空")
    @DecimalMin(value = "0", message = "ICMS流转税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "ICMS流转税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "ICMS流转税率最多两位小数")
    @ExcelProperty(index = 5, value = "* ICMS流转税率%")
    private String icmsFlowTax;

    /**
     * 反倾销税率
     */
    @NotNull(message = "反倾销税率不能为空")
    @DecimalMin(value = "0", message = "反倾销税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "反倾销税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "反倾销税率最多两位小数")
    @ExcelProperty(index = 6, value = "* 反倾销税率Anti_dumping Tax%")
    private String antiDumpingTax;

    /**
     * 是否管制
     */
    @NotNull(message = "是否管制不能为空")
    @ExcelProperty(index = 7, value = "* 是否管制")
    private String controls;

    /**
     * 管制信息
     */
    @Length(max = 500, message = "管制信息长度不能超过500")
    @ExcelProperty(index = 8, value = "管制信息（是否管制为是时，必填）")
    private String controlsInfo;

    /**
     * 备注
     */
    @Length(max = 100, message = "备注长度不能超过100")
    @ExcelProperty(index = 9, value = "备注")
    private String remark;

}


    
