package com.jdi.isc.task.center.domain.common.biz;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 基础结果类
 * <AUTHOR>
 * @description：ResultBaseVO
 * @Date 2025-08-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultBaseVO {
    // 状态
    private boolean success = true;
    // 消息
    private String message;
    // 返回结果
    private String data;

    public ResultBaseVO(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    public void failed(String message) {
        this.success = false;
        this.message = message;
    }
}
