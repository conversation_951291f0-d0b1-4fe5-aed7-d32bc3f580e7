package com.jdi.isc.task.center.domain.task.excel.productCustoms;

import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * @Description: 商品关务导出实体
 * @Author: zhaojianguo21
 * @Date: 2025/7/18
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProductCustomsExportExcelDTO extends BasicDTO {

    /**
     * 国际skuId
     */
    private Long skuId;
    /**
     * 国际skuId
     */
    private String skuIdStr;
    /**
     * 草稿id
     */
    private Long draftId;

    /**
     * 国内skuId
     */
    private Long jdSkuId;
    /**
     * 国内skuId
     */
    private String jdSkuIdStr;

    /**
     * 国家代码: TH,VN,MY,ID,HU,BR
     */
    private String countryCode;

    /**
     * 国家hs code码
     */
    private String hsCode;


    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku多语言名称
     */
    private String skuLocalName;


    /**
     * sku图片
     */
    private String skuImage;

    /**
     * 毛重
     */
    private String weight;

    /**
     * 长
     */
    private String length;

    /**
     * 宽
     */
    private String wide;

    /**
     * 高
     */
    private String high;

    /**
     * 采销erp
     */
    private String purchaseSalesErp;

    /**
     * 是否存在订单
     */
    private Integer orderExists;

    /**
     * 重量数据来源
     */
    private Integer weightDataSource;

    /**
     * 期望完成时间
     */
    private Long expectCompletionTime;


    /**
     * 商品关务状态
     */
    private Integer status;


    /**
     * 类目名称
     */
    private String categoryName;


    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商品上下架状态
     */
    private Integer productStatus;


    /**
     * 关务评估状态
     */
    private Integer customsComplianceStatus;

    /**
     * 强制性认证评估状态
     */
    private Integer compulsoryCertificationStatus;


    /**
     * 申报品名
     */
    private String declarationName;

    /**
     * 申报属地国商品名
     */
    private String declarationLocalName;

    /**
     * 申报要素
     */
    private String declarationElement;

    /**
     * 是否管制
     */
    private Integer controls;

    /**
     * 管制信息
     */
    private String controlsInfo;

    /**
     * 是否需强制认证
     */
    private Integer compulsoryCertification;

    /**
     * 强制认证信息
     */
    private String compulsoryCertificationInfo;

    /**
     * 品牌授权限制
     */
    private Integer brandLicensing;

    /**
     * 所属服务商
     */
    private String customsSupplierId;

    /**
     * 所属服务商
     */
    private String customsSupplierName;

    /**
     * 数据来源
     */
    private String dataSource;


    //税率 TODO
    /**
     * 最惠国关税率
     */
    private BigDecimal mfnTax;

    /**
     * 原产地优惠关税率
     */
    private BigDecimal originMfnTax;

    /**
     * 消费税率
     */
    private BigDecimal consumptionTax;

    /**
     * 环保税单价
     */
    private BigDecimal environmentalTaxUnitPrice;

    /**
     * 反倾销税率
     */
    private BigDecimal antiDumpingTax;

    /**
     * 增值税率
     */
    private BigDecimal valueAddedTax;

    /**
     * formE关税率
     */
    private BigDecimal formeTax;

    /**
     * 本地税率
     */
    private BigDecimal localTax;

    /**
     * 销售税率SST
     */
    private BigDecimal saleTax;

    /**
     * 进口关税率II
     */
    private BigDecimal importTax;

    /**
     * 工业产品税率IPI
     */
    private BigDecimal industryProductTax;

    /**
     * 社会一体化费率PIS
     */
    private BigDecimal socialIntegrationTax;

    /**
     * CONFINS率
     */
    private BigDecimal confinsTax;

    /**
     * ICMS流转税率
     */
    private BigDecimal icmsFlowTax;

    /**
     * 反补贴税率
     */
    private BigDecimal antiSubsidyTax;

    /**
     * 普通关税率BM
     */
    private BigDecimal normalImportTax;

    /**
     * 奢侈品税率PPnBM
     */
    private BigDecimal luxuryTax;

    /**
     * 预扣税率PPH
     */
    private BigDecimal withholdingTax;

    /**
     * 贸易保护关税BMT率
     */
    private BigDecimal tradeProtectionTax;


    /**
     * sku修改时间
     */
    private Long skuUpdateTime;

    /**
     * spu修改时间
     */
    private Long spuUpdateTime;

    /**
     * 订单修改时间
     */
    private Long orderUpdateTime;


    /**
     * 出口征税率
     */
    private BigDecimal exportTaxRate;

    /**
     * 出口退税率
     */
    private BigDecimal exportRebateRate;
}
