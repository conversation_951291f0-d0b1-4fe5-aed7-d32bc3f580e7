package com.jdi.isc.task.center.domain.enums;

import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Excel模板枚举
 * <AUTHOR>
 * @date 2024/8/12
 **/
@Getter
@AllArgsConstructor
public enum ExcelTemplateEnum {
    GLOBAL_ATTRIBUTE(38,"globalAttributeExcelTemplateService","跨境属性更新模板"),
    CN_PRODUCT_AMEND(45,"skuAmendCNExcelTemplateService","中国商品修改模版"),
    INT_PRODUCT_AMEND(46,"skuAmendINTExcelTemplateService","国际商品修改模版"),
    VC_CREATE_PRODUCT(1003,"vcCreateProductExcelTemplateService","VC批量发品模板-本本"),
    WIMP_CROSS_CREATE_PRODUCT(47,"wimpCreateProductExcelTemplateService","WIMP批量发品模板-跨境"),
    WIMP_LOCAL_CREATE_PRODUCT(48,"wimpLocalCreateProductExcelTemplateService","WIMP批量发品模板-本本"),
    WISP_MATERIAL_BATCH_ADD(52, "wispBatchAddMaterialTemplateService", "WISP批量新增物料模板"),
    WISP_BATCH_CONFIG(53, "wispEnumerationTemplateService", "WISP批量配置枚举值"),
    WIMP_PROFIT_RATE(TaskBizTypeEnum.WIMP_PROFIT_RATE.getCode(), "wimpProfitRateTemplateService", "WIMP批量利润率阈值模版"),
    WIMP_FULFILLMENT_RATE(TaskBizTypeEnum.WIMP_FULFILLMENT_RATE.getCode(), "wimpFulfillmentRateTemplateService", "WIMP批量履约费率模版"),
    WIMP_EXPORT_TAX_RATE(TaskBizTypeEnum.WIMP_EXPORT_TAX_RATE_IMPORT.getCode(), "wimpExportTaxRateTemplateService", "WIMP批量导入出口税率"),

    SUPPLIER_SKU_STOCK_BATCH_IMPORT(1002, "vcSkuStockTemplateService", "VC批量追加库存"),
    UPDATE_PRICE_BATCH_IMPORT(1005, "vcSkuStockTemplateService", "VC批量更新价格"),
    UPDATE_SUPPLIER_SKUID_BATCH_IMPORT(1006, "vcSkuStockTemplateService", "VC批量更新供应商SKUID"),
    WIMP_PRODUCT_SPECIAL_ATTR_TEMPLATE(1007, "wimpProductSpecialAttrTemplateService", "特殊属性打标模板"),
    UPDATE_SKU_PRICE_BATCH_IMPORT(1008, "vcSkuStockTemplateService", "VC申请修改采购价"),
    PRODUCT_IDENTIFY_TEMPLATE(TaskBizTypeEnum.PRODUCT_IDENTIFY_V2.getCode(), "productIdentifyTemplateService", "商品识别类目导入模板"),
    ;

    /**
     * 任务类型
     */
    private Integer type;
    /**
     * 动态模板实现类
     */
    private String templateService;
    /**
     * 类描述
     */
    private String desc;


    public static String getServiceName(int type) {
        ExcelTemplateEnum[] values = ExcelTemplateEnum.values();
        for (ExcelTemplateEnum value : values) {
            if (value.getType().equals(type)) {
                return value.getTemplateService();
            }
        }
        return null;
    }
}
