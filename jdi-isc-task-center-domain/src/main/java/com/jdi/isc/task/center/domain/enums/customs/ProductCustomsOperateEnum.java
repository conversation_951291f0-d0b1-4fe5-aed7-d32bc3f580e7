package com.jdi.isc.task.center.domain.enums.customs;


import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品海关操作类型枚举
 *
 * <p>用于标识产品在海关系统中的操作类型</p>
 */
public enum ProductCustomsOperateEnum {

    CREATE("create", "新增"),
    UPDATE_DECLARATION_INFO("update_declaration_info", "修改申报信息"),
    UPDATE_COMPULSORY_CERTIFICATION_INFO("update_compulsory_certification_info", "修改强制认证信息"),
    UPDATE_PRODUCT("update_product", "修改商品信息"),
    UPDATE_ORDER("update_order", "修改订单信息"),


    ;

    private final String code;
    private final String desc;

    ProductCustomsOperateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举实例（支持Jackson反序列化）
     */
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ProductCustomsOperateEnum fromCode(String code) {
        if (code == null || code.isEmpty()) {
            return null;
        }
        for (ProductCustomsOperateEnum operator : values()) {
            if (operator.code.equalsIgnoreCase(code)) {
                return operator;
            }
        }
        throw new IllegalArgumentException("未知的操作类型编码: " + code);
    }

    /**
     * 获取所有有效的操作类型编码
     */
    public static List<String> getAllCodes() {
        return Arrays.stream(values())
                .map(ProductCustomsOperateEnum::getCode)
                .collect(Collectors.toList());
    }

    /**
     * 验证操作类型编码是否有效
     */
    public static boolean isValid(String code) {
        try {
            fromCode(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    @Override
    public String toString() {
        return code + "(" + desc + ")";
    }
}