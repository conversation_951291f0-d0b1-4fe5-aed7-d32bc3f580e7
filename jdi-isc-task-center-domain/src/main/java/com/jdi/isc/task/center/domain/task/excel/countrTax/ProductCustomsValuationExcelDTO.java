package com.jdi.isc.task.center.domain.task.excel.countrTax;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.task.center.domain.task.dto.BaseWorkerDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProductCustomsValuationExcelDTO extends BaseWorkerDTO {
    /**
     * 国内SKUID（必填）
     */
    @ExcelProperty(index = 0, value = "*国内SKUID（必填） Domestic SKUID（CN）")
    private String jdSkuId;

    /**
     * 国家（Country）
     */
    @ExcelProperty(index = 1, value = "*评估国家 Country")
    private String countryCode;

    /**
     * Cut-off time
     */
    @ExcelProperty(index = 2, value = "期望完成时间 Cut-off time\n格式：2025/3/31")
    private String expectedCompletionTime;

    /**
     * 采销ERP
     */
    @ExcelProperty(index = 3, value = "采销ERP")
    private String purchaseSalesErp;

    /**
     * 备注（Remarks）
     */
    @ExcelProperty(index = 4, value = "备注 Remarks")
    private String remarks;
}
