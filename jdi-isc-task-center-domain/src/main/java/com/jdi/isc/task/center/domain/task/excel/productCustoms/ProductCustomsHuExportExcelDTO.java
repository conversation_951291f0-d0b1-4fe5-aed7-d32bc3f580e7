package com.jdi.isc.task.center.domain.task.excel.productCustoms;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.task.center.domain.task.dto.BaseWorkerDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description: 匈牙利商品关务导出实体
 * @Author: zhaojianguo21
 * @Date: 2025/7/18
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProductCustomsHuExportExcelDTO extends BaseWorkerDTO {
    /**
     * 国家代码
     */
    @ExcelIgnore
    private String countryCode;

    /**
     * 国际skuId
     */
    @ExcelProperty(index = 0, value = "国际 SKU ID(International)")
    private String skuIdStr;

    /**
     * 国内skuId
     */
    @ExcelProperty(index = 1, value = "国内 SKU ID (China)")
    private String jdSkuIdStr;

    /**
     * sku名称
     */
    @ExcelProperty(index = 2, value = "商品中文名称 Product Name(Chinese)")
    private String skuName;

    /**
     * sku多语言名称
     */
    @ExcelProperty(index = 3, value = "商品匈牙利语名称 Product Name(Hungary)")
    private String skuLocalName;

    /**
     * sku图片
     */
    @ExcelIgnore
    private String skuImage;

    /**
     * 品牌名称
     */
    @ExcelProperty(index = 4, value = "品牌名 Brand Name")
    private String brandName;

    /**
     * 类目名称
     */
    @ExcelProperty(index = 5, value = "商品类目 Product Category")
    private String categoryName;

    /**
     * 商品上下架状态
     */
    @ExcelIgnore
    private Integer productStatus;

    /**
     * 商品上下架状态。Str后缀不要变更，导出时需要。
     */
    @ExcelProperty(index = 6, value = "商品上下架状态 Product Listing Status (On/Off Shelf)")
    private String productStatusStr;

    /**
     * 毛重
     */
    @ExcelProperty(index = 7, value = "毛重(g) Gross Weight (g)")
    private String weight;

    /**
     * 长
     */
    @ExcelProperty(index = 8, value = "长(mm) Length (mm)")
    private String length;

    /**
     * 宽
     */
    @ExcelProperty(index = 9, value = "宽(mm) Width (mm)")
    private String wide;

    /**
     * 高
     */
    @ExcelProperty(index = 10, value = "高(mm) Height (mm)")
    private String high;

    /**
     * 重量数据来源
     */
    @ExcelIgnore
    private Integer weightDataSource;

    /**
     * 重量数据来源。Str后缀不要变更，导出时需要。
     */
    @ExcelProperty(index = 11, value = "重量数据来源 Weight Data Source")
    private String weightDataSourceStr;

    /**
     * 采销erp
     */
    @ExcelProperty(index = 12, value = "采销ERP Procurement (ERP）")
    private String purchaseSalesErp;

    /**
     * 是否存在订单
     */
    @ExcelIgnore
    private Integer orderExists;

    /**
     * 是否存在订单。Str后缀不要变更，导出时需要。
     */
    @ExcelProperty(index = 13, value = "是否生成过订单 Order Generated (Yes/No)")
    private String orderExistsStr;

    /**
     * 数据来源
     */
    @ExcelIgnore
    private String dataSource;

    /**
     * 数据来源。Str后缀不要变更，导出时需要。
     */
    @ExcelProperty(index = 14, value = "数据来源 Data Source")
    private String dataSourceStr;

    /**
     * 期望完成时间
     */
    @ExcelIgnore
    private Long expectCompletionTime;

    /**
     * 期望完成时间。Str后缀不要变更，导出时需要。
     */
    @ExcelProperty(index = 15, value = "期望完成时间 Expected Completion Time")
    private String expectCompletionTimeStr;

    /**
     * 商品关务状态
     */
    @ExcelIgnore
    private Integer status;

    /**
     * 商品关务状态。Str后缀不要变更，导出时需要。
     */
    @ExcelProperty(index = 16, value = "状态 Status")
    private String statusStr;

    /**
     * 关务评估状态
     */
    @ExcelIgnore
    private Integer customsComplianceStatus;

    /**
     * 关务评估状态。Str后缀不要变更，导出时需要。
     */
    @ExcelProperty(index = 17, value = "关务评估状态 Assessment Status (Customs)")
    private String customsComplianceStatusStr;

    /**
     * 申报品名
     */
    @ExcelProperty(index = 18, value = "申报英文品名 Declared product Name (English)")
    private String declarationName;

    /**
     * 申报属地国商品名
     */
    @ExcelProperty(index = 19, value = "申报匈牙利品名 Declared product Name (Hungary)")
    private String declarationLocalName;

    /**
     * 申报要素
     */
    @ExcelIgnore
    private String declarationElement;

    /**
     * 国家hs code码
     */
    @ExcelProperty(index = 20, value = "TARIC Code")
    private String hsCode;

    /**
     * 进口关税率
     */
    @ExcelProperty(index = 21, value = "进口关税Import duty%")
    private BigDecimal importTax;

    /**
     * 反倾销税率
     */
    @ExcelProperty(index = 22, value = "反倾销税率anti-dumping duty%")
    private BigDecimal antiDumpingTax;

    /**
     * 反补贴税率
     */
    @ExcelProperty(index = 23, value = "反补贴税率countervailing duty%")
    private BigDecimal antiSubsidyTax;

    /**
     * 增值税率
     */
    @ExcelProperty(index = 24, value = "增值税VAT%")
    private BigDecimal valueAddedTax;

    /**
     * 是否管制
     */
    @ExcelIgnore
    private Integer controls;

    /**
     * 是否管制。Str后缀不要变更，导出时需要。
     */
    @ExcelProperty(index = 25, value = "是否管制 Import Restricted (Yes/No)")
    private String controlsStr;

    /**
     * 管制信息
     */
    @ExcelProperty(index = 26, value = "管制信息 Restriction Information")
    private String controlsInfo;

    /**
     * 强制认证状态
     */
    @ExcelIgnore
    private Integer compulsoryStatus;

    /**
     * 强制认证状态。Str后缀不要变更，导出时需要。
     */
    @ExcelProperty(index = 27, value = "强制性认证状态 Assessment Status (Quality Certification)")
    private String compulsoryStatusStr;

    /**
     * 是否需强制认证
     */
    @ExcelIgnore
    private Integer compulsoryCertification;

    /**
     * 是否需强制认证。Str后缀不要变更，导出时需要。
     */
    @ExcelProperty(index = 28, value = "是否需强制认证 Mandatory Certification Required (Yes/No)")
    private String compulsoryCertificationStr;

    /**
     * 强制认证信息
     */
    @ExcelProperty(index = 29, value = "强制认证信息 Mandatory Certification Details")
    private String compulsoryCertificationInfo;

    /**
     * 品牌授权限制。Str后缀不要变更，导出时需要。
     */
    @ExcelIgnore
    private Integer brandLicensing;

    /**
     * 品牌授权限制
     */
    @ExcelProperty(index = 30, value = "品牌授权限制 Brand Authorization required (Yes/No)")
    private String brandLicensingStr;

    /**
     * 所属服务商
     */
    @ExcelIgnore
    private String customsSupplierId;

    /**
     * 所属服务商
     */
    @ExcelProperty(index = 31, value = "评估服务商 Classification Service Agent")
    private String customsSupplierName;

}


    
