package com.jdi.isc.task.center.domain.joysky.biz;

import com.jdi.isc.task.center.domain.joysky.po.JoySkyApprovalTaskPO;

/**
 * <AUTHOR>
 * @Date 2024/11/4
 * @Description 审批任务结果对象
 */
public class JoySkyApplyResultDTO {

    /**
     * 审批动作
     */
    private String actionType;

    /**
     * 审批任务
     */
    private JoySkyApprovalTaskPO joySkyApprovalTaskPO;

    /**
     * 审批人ERP
     */
    private String approverErp;

    /**
     * 审批意见
     */
    private String approverRemark;

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public JoySkyApprovalTaskPO getJoySkyApprovalTaskPO() {
        return joySkyApprovalTaskPO;
    }

    public void setJoySkyApprovalTaskPO(JoySkyApprovalTaskPO joySkyApprovalTaskPO) {
        this.joySkyApprovalTaskPO = joySkyApprovalTaskPO;
    }

    public String getApproverErp() {
        return approverErp;
    }

    public void setApproverErp(String approverErp) {
        this.approverErp = approverErp;
    }


    public String getApproverRemark() {
        return approverRemark;
    }

    public void setApproverRemark(String approverRemark) {
        this.approverRemark = approverRemark;
    }

    @Override
    public String toString() {
        return "JoySkyApplyResultDTO{" +
                "actionType='" + actionType + '\'' +
                "approverErp='" + approverErp + '\'' +
                "approverRemark='" + approverRemark + '\'' +
                ", joySkyApprovalTaskPO=" + joySkyApprovalTaskPO +
                '}';
    }
}
