package com.jdi.isc.task.center.domain.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @description 订单类型和模式汇总，orderType和purchaseModel的组合结果
 * @date 2025/5/29 21:51
 */

public enum OrderShowTypeEnum {


    LOCAL_DIRECT(1, "本土直供订单"),
    CROSS_BORDER(2, "跨境直供订单"),
    MIXED(3, "混合订单"),
    STOCK_UP(10, "仓发订单"),
    SELL_DIRECT(20, "寄售订单");

    private final Integer type;
    private final String name;

    OrderShowTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    // Getter方法
    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据type获取name
     * @param type 类型值
     * @return 对应的名称，如果未找到返回null
     */
    public static String getNameByCode(Integer type) {
        if (type == null) {
            return null;
        }
        for (OrderShowTypeEnum enumItem : values()) {
            if (enumItem.type.equals(type)) {
                return enumItem.name;
            }
        }
        return null;
    }

    /**
     * 根据type获取枚举对象
     * @param type 类型值
     * @return 对应的枚举对象，如果未找到返回null
     */
    public static OrderShowTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (OrderShowTypeEnum enumItem : values()) {
            if (enumItem.type.equals(type)) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 根据name获取枚举对象
     * @param name 名称
     * @return 对应的枚举对象，如果未找到返回null
     */
    public static OrderShowTypeEnum getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        for (OrderShowTypeEnum enumItem : values()) {
            if (enumItem.name.equals(name)) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 判断给定的type是否存在
     * @param type 类型值
     * @return 存在返回true，否则返回false
     */
    public static boolean containsType(Integer type) {
        return getByType(type) != null;
    }

    /**
     * 获取所有type值
     * @return type值列表
     */
    public static List<Integer> getAllTypes() {
        return Arrays.stream(values())
                .map(OrderShowTypeEnum::getType)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有name值
     * @return name值列表
     */
    public static List<String> getAllNames() {
        return Arrays.stream(values())
                .map(OrderShowTypeEnum::getName)
                .collect(Collectors.toList());
    }
}
