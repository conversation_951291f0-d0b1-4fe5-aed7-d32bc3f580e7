package com.jdi.isc.task.center.domain.task.enums;

import lombok.Getter;

/**
 * 导出字段类型枚举
 * <AUTHOR>
 * @date 2025/8/19
 */
@Getter
public enum ExportFieldTypeEnum {

    STRING("STRING", "字符串"),
    INTEGER("INTEGER", "整数"),
    DECIMAL("DECIMAL", "小数"),
    DATE("DATE", "日期"),
    DATETIME("DATETIME", "日期时间"),
    BOOLEAN("BOOLEAN", "布尔值"),
    ENUM_TYPE("ENUM", "枚举类");

    private final String code;
    private final String desc;

    ExportFieldTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ExportFieldTypeEnum getByCode(String code) {
        for (ExportFieldTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return STRING;
    }
}