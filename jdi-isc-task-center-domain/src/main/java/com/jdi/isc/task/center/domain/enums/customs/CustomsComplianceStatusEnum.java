package com.jdi.isc.task.center.domain.enums.customs;


import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 关务评估状态
 */
@Getter
public enum CustomsComplianceStatusEnum {
    TO_BE_SUBMITTED(1, "待提交"),
    SUBMITTED(2, "已提交");

    private final Integer code;
    private final String desc;

    CustomsComplianceStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    // 根据code获取枚举实例
    public static CustomsComplianceStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CustomsComplianceStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    // 根据code获取描述文本
    public static String getDescByCode(Integer code) {
        CustomsComplianceStatusEnum status = getByCode(code);
        return status != null ? status.desc : null;
    }

    // 检查code是否有效
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    // 获取所有状态码
    public static List<Integer> getAllCodes() {
        return Arrays.stream(values())
                .map(CustomsComplianceStatusEnum::getCode)
                .collect(Collectors.toList());
    }
}
