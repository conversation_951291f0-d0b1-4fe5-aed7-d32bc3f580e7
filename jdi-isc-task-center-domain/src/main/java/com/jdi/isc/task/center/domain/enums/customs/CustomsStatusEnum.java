package com.jdi.isc.task.center.domain.enums.customs;

import lombok.Getter;

/**
 * 关务状态
 *
 * <AUTHOR>
 * @description
 * @date 2025/7/14
 */
@Getter
public enum CustomsStatusEnum {

    TO_BE_EVALUATED(1, "待评估"),
    TO_BE_CONFIRMED(20, "待确认"),
    CONFIRMED(30, "已确认");

    private final Integer code;
    private final String desc;

    CustomsStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    // 根据code获取枚举实例
    public static CustomsStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CustomsStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    // 根据code获取描述文本
    public static String getDescByCode(Integer code) {
        CustomsStatusEnum status = getByCode(code);
        return status != null ? status.desc : null;
    }

    // 判断code是否有效
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}