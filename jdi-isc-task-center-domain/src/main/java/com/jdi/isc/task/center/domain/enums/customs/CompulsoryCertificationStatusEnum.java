package com.jdi.isc.task.center.domain.enums.customs;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 强制性评估状态
 */
@Getter
public enum CompulsoryCertificationStatusEnum {

    TO_BE_SUBMITTED(1, "待提交"),
    SUBMITTED(2, "已提交");

    private final Integer code;
    private final String desc;

    CompulsoryCertificationStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    // 根据code获取枚举实例
    public static CompulsoryCertificationStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CompulsoryCertificationStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    // 根据code获取描述文本
    public static String getDescByCode(Integer code) {
        CompulsoryCertificationStatusEnum status = getByCode(code);
        return status != null ? status.desc : null;
    }

    // 检查code是否有效
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    // 获取所有状态码
    public static List<Integer> getAllCodes() {
        return Arrays.stream(values())
                .map(CompulsoryCertificationStatusEnum::getCode)
                .collect(Collectors.toList());
    }
}
