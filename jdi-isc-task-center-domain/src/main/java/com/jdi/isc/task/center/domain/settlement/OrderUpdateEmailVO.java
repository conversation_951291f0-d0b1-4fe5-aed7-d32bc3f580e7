package com.jdi.isc.task.center.domain.settlement;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class OrderUpdateEmailVO {

    /*订单号
     */
    private Long orderId;

    /**
     *客户编号
     */
    private String clientCode;

    /*客户Pin/

     */
    private String pin;
    /**
     *所在国家
     */
    private  String countryCode;

    /**
     *商品种类
     */
    private Integer mkuKindNum;

    /**
     *商品总数量
     */
    private Integer mkuNum;

    /**
     *订单总金额
     */
    private BigDecimal waresSaleTotalPrice;


    /*订单状态对应内容
     * 3:确认订单
     * 90:订单已妥投
     * 99:订单已完成
     * */
    private Integer orderStatus;

    /*订单来源
    WIOP、WISP、SRMLINK、WIEP_CATL、WIEP_CXML、WIEP_JD、WIEP_OCI
     */
    private String sourceCode;

    /*客户第三方订单号
     */
    private String thirdOrderId;
}