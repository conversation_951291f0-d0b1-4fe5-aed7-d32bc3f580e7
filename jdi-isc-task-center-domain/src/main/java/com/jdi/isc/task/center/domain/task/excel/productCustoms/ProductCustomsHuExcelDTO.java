package com.jdi.isc.task.center.domain.task.excel.productCustoms;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.task.center.domain.task.dto.BaseWorkerDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @Description: 匈牙利商品关务导入实体
 * 注意：不要随意修改 ExcelProperty 注解的value值，校验excel模板是否正确时，value值会和excel表头比较
 *
 * <AUTHOR>
 * @date 20250720
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProductCustomsHuExcelDTO extends BaseWorkerDTO {
    // 注意：不要随意修改 ExcelProperty 注解的value值，校验excel模板是否正确时，value值会和excel表头比较

    /**
     * 国内skuId
     */
    @NotNull(message = "国内jdSkuId不能为空")
    @ExcelProperty(index = 0, value = "*skuId-国内\nSKU ID (China)")
    private Long jdSkuId;

    /**
     * 国际skuId
     */
    @ExcelProperty(index = 1, value = "skuId-国际\nSKU ID (International)")
    private Long skuId;

    /**
     * 申报品名
     */
    @ExcelProperty(index = 2, value = "申报英文品名\nProduct Name(English)")
    private String declarationName;

    /**
     * 申报多语言名
     */
    @ExcelProperty(index = 3, value = "申报匈牙利品名Product Name (Hungarian)  ")
    private String declarationLocalName;

    /**
     * hsCode
     */
    @NotNull(message = "HsCode(TRAIC)码不能为空")
    @Pattern(regexp="(^[0-9]{1,15}$)", message = "HsCode(TRAIC)只能是长度介于1-15位的纯数字字符串")
    @ExcelProperty(index = 4, value = "*TARIC")
    private String hsCode;

    /**
     * 进口关税率
     */
    @NotNull(message = "进口关税率不能为空")
    @DecimalMin(value = "0", message = "进口关税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "进口关税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "进口关税率最多两位小数")
    @ExcelProperty(index = 5, value = "*进口关税Import duty %")
    private String importTax;

    /**
     * 反倾销税率
     */
    @NotNull(message = "反倾销税率不能为空")
    @DecimalMin(value = "0", message = "反倾销税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "反倾销税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "反倾销税率最多两位小数")
    @ExcelProperty(index = 6, value = "*反倾销税率anti-dumping duty %")
    private String antiDumpingTax;

    /**
     * 反补贴税率
     */
    @NotNull(message = "反补贴税率不能为空")
    @DecimalMin(value = "0", message = "反补贴税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "反补贴税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "反补贴税率最多两位小数")
    @ExcelProperty(index = 7, value = "*反补贴税率countervailing duty %")
    private String antiSubsidyTax;

    /**
     * 增值税率
     */
    @NotNull(message = "增值税率不能为空")
    @DecimalMin(value = "0", message = "增值税率应介于0%-300%之间")
    @DecimalMax(value = "300", message = "增值税率应介于0%-300%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "增值税率最多两位小数")
    @ExcelProperty(index = 8, value = "*增值税VAT %")
    private String valueAddedTax;

    /**
     * 是否管制
     */
    @NotNull(message = "是否管制不能为空")
    @ExcelProperty(index = 9, value = "*是否管制\nImport Restricted or Not")
    private String controls;

    /**
     * 管制信息
     */
    @Length(max = 500, message = "管制信息长度不能超过500")
    @ExcelProperty(index = 10, value = "*管制信息（是否管制为是时，必填）\n* Restriction Information (Mandatory if Restricted)")
    private String controlsInfo;

    /**
     * 品牌授权 有限制/无限制
     */
    @ExcelProperty(index = 11, value = "品牌授权（匈牙利）有限制/无限制Brand Authorization (Hungary) required")
    private String brandLicensing;

    /**
     * 备注
     */
    @Length(max = 100, message = "备注长度不能超过100")
    @ExcelProperty(index = 12, value = "备注Remarks")
    private String remark;

}


    
