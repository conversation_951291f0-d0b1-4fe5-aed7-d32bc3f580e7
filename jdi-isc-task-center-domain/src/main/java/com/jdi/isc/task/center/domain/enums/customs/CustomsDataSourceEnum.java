package com.jdi.isc.task.center.domain.enums.customs;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 关务数据来源枚举
 * <p>用于标识关务数据的来源渠道</p>
 */
@Getter
public enum CustomsDataSourceEnum {
    WIMP("wimp", "WIMP"),
    EXCEL_IMPORT("excel", "EXCEL"),
    LING_XI("ling_xi", "灵犀");

    private final String code;
    private final String desc;

    // 缓存映射表用于快速查找
    private static final Map<String, CustomsDataSourceEnum> CODE_MAP =
            Arrays.stream(values())
                    .collect(Collectors.toMap(CustomsDataSourceEnum::getCode, e -> e));

    CustomsDataSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据编码获取枚举实例
     * @param code 枚举编码
     * @return 对应枚举实例，未找到返回null
     */
    public static CustomsDataSourceEnum fromCode(String code) {
        if (null==code){
            return null;
        }
        return CODE_MAP.get(code);
    }

    /**
     * 验证编码是否有效
     * @param code 待验证编码
     * @return 有效返回true，否则false
     */
    public static boolean isValid(String code) {
        return code != null && CODE_MAP.containsKey(code);
    }

    /**
     * 获取所有有效编码（适合参数校验场景）
     * @return 有效编码集合
     */
    public static String[] validCodes() {
        return CODE_MAP.keySet().toArray(new String[0]);
    }

    /**
     * 获取枚举描述（适用于需要null安全的场景）
     * @param code 枚举编码
     * @return 对应描述，未找到返回null
     */
    public static String getDescByCode(String code) {
        CustomsDataSourceEnum e = fromCode(code);
        return e != null ? e.getDesc() : null;
    }

    @Override
    public String toString() {
        return code + ":" + desc;
    }
}
