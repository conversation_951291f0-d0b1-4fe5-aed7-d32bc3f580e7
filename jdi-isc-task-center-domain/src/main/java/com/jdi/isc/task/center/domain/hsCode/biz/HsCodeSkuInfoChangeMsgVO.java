package com.jdi.isc.task.center.domain.hsCode.biz;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: HsCode sku变更消息记 实体类
 * @Author: zhaojianguo21
 * @Date: 2025/07/22 23:05
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HsCodeSkuInfoChangeMsgVO {

    /**
     * hs_code_change_msg表ID
     */
    private Long hsCodeChangeMsgId;

    /**
     * 业务ID。各国家hsCode表的主键id
     */
    private String bizId;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 海关编码
     */
    private String hsCode;

    /**
     * hsCode信息变更消息体内容
     */
    private String hsCodeMsgBody;

    /**
     * 国内skuId
     */
    private Long jdSkuId;

    /**
     * 国际skuId
     */
    private Long skuId;

    /** hs_code_change_msg表创建时间 */
    private Long hsCodeChangeMsgCreateTime;
}