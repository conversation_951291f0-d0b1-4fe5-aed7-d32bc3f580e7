package com.jdi.isc.task.center.domain.stockThreshold.biz;


import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.task.center.domain.sku.biz.SkuInfoVO;
import com.jdi.isc.task.center.domain.stockThreshold.po.AbnormalStockStatisticsPO;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @description：异常库存数据统计DTO
 * @Date 2025-01-18
 */
@Data
public class AbnormalStockStatisticsExcelForBuyerDTO extends AbnormalStockStatisticsExcelDTO {

    @ExcelProperty(value = "二级类目名称", index = 7)
    private String secondCatName;

    @ExcelProperty(value = "三级类目名称", index = 8)
    private String thirdCatName;

    @ExcelProperty(value = "四级类目名称", index = 9)
    private String fourCatName;

    @ExcelProperty(value = "商品类型", index = 10)
    private String skuType;

    @ExcelProperty(value = "采销名称", index = 11)
    private String buyerName;


    public AbnormalStockStatisticsExcelForBuyerDTO(AbnormalStockStatisticsPO po, Map<Long, SkuInfoVO> skuNameMap,
                                                   String warehouseName, String supplierName,
                                                   Map<Integer, String> catNameMap) {
        this.skuId = String.valueOf(po.getSkuId());
        this.warehouseNo = po.getWarehouseNo();
        this.stock = String.valueOf(po.getStock());
        this.skuName = skuNameMap.getOrDefault(po.getSkuId(), new SkuInfoVO()).getSkuName();
        this.warehouseName = warehouseName;
        this.supplierName = supplierName;
        this.firstCatName = catNameMap.get(1);
        this.secondCatName = catNameMap.get(2);
        this.thirdCatName = catNameMap.get(3);
        this.fourCatName = catNameMap.get(4);
        this.buyerName = po.getBuyer();
        this.skuType = "CN".equals(po.getSourceCountryCode()) ? "跨境" : "本本";
    }
}
