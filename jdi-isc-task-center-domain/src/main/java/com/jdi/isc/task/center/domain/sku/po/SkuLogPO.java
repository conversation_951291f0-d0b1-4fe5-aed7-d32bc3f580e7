package com.jdi.isc.task.center.domain.sku.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.task.center.domain.common.po.BasicPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * sku/spu/spuDrafe/mku日志操作表
 *
 * @TableName jdi_isc_sku_log_sharding
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("jdi_isc_sku_log_sharding")
public class SkuLogPO extends BasicPO {
    /**
     * key类型。1:sku;2:spu;3:spDraft;4:mku;5:specialAttr;6:countryPool;7:skuPrice
     */
    private Integer keyType;

    /**
     * 可能是skuId或spuId或mkuId
     */
    private String keyId;

    /**
     * 源数据JSON
     */
    private String sourceJson;

    /**
     * 现在数据JSON
     */
    private String targetJson;

}
