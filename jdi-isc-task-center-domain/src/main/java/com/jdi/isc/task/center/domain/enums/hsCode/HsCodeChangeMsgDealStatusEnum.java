package com.jdi.isc.task.center.domain.enums.hsCode;

import java.util.HashMap;
import java.util.Map;

/**
 * @description HsCode变更消息处理状态枚举
 * <AUTHOR>
 * @date 2025/07/22
 **/
public enum HsCodeChangeMsgDealStatusEnum {
    WAIT(0,"待处理"),
    SUCCESS(1,"成功"),
    FAIL(2,"失败"),
    ;

    private Integer code;
    private String desc;

    public static Map<Integer, HsCodeChangeMsgDealStatusEnum> enumMap = new HashMap();
    public static Map<Integer, String> codeDescMap = new HashMap();

    static {
        for (HsCodeChangeMsgDealStatusEnum val : values()) {
            enumMap.put(val.getCode(), val);
            codeDescMap.put(val.getCode(), val.getDesc());
        }
    }

    HsCodeChangeMsgDealStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }

    public static HsCodeChangeMsgDealStatusEnum forCode(Integer code) {
        return enumMap.get(code);
    }
}