package com.jdi.isc.task.center.domain.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 订单hold状态
 * <AUTHOR> @version 1.0
 * @date 2025/3/7 20:08
 */
@Getter
public enum OrderHoldStatusEnum {

    HOLD(1, "暂停"),

    UN_HOLD(0, "非暂停");


    OrderHoldStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;

    private String name;

    /**
     * 根据状态码获取枚举
     */
    public static OrderHoldStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }

        return Arrays.stream(OrderHoldStatusEnum.values())
                .filter(status -> status.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据状态码获取状态名称
     */
    public static String getNameByCode(Integer code) {
        OrderHoldStatusEnum status = getByCode(code);
        return status != null ? status.getName() : null;
    }
}
