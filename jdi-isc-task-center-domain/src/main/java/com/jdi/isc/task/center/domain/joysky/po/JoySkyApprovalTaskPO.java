package com.jdi.isc.task.center.domain.joysky.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.task.center.domain.common.po.BasePOV2;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/11/1
 * @Description joysky审批流任务表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value ="joysky_approval_task")
public class JoySkyApprovalTaskPO extends BasePOV2 {

    /**
     * 审批流任务类型
     */
    private Integer processType;

    /**
     * 审批流程key
     */
    private String processKey;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 审批流状态
     */
    private String processStatus;

    /**
     * 业务方请求数据
     */
    private String requestData;

}
