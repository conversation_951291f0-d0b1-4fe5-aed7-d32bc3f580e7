package com.jdi.isc.task.center.domain.enums.sku;


import com.jdi.isc.task.center.domain.enums.customs.CompulsoryCertificationEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * SKU状态枚举
 * <AUTHOR>
 * @description：SkuStatusEnum
 * @Date 2025-07-07
 */
@Getter
@AllArgsConstructor
public enum SkuStatusEnum {
    DOWN(0,"下架"),
    ON_SALE(1, "上架"),;
    /**
     * SKU状态
     */
    private Integer status;
    /**
     * 状态描述
     */
    private String desc;


    public static Map<Integer, SkuStatusEnum> enumMap = new HashMap();
    public static Map<Integer, String> codeDescMap = new HashMap();

    static {
        for (SkuStatusEnum val : values()) {
            enumMap.put(val.getStatus(), val);
            codeDescMap.put(val.getStatus(), val.getDesc());
        }
    }

    public static SkuStatusEnum forCode(Integer code) {
        if (null==code) {
            return null;
        }
        return enumMap.get(code);
    }

    public static String code2Desc(Integer code) {
        if (null==code) {
            return null;
        }
        return codeDescMap.get(code);
    }
}
