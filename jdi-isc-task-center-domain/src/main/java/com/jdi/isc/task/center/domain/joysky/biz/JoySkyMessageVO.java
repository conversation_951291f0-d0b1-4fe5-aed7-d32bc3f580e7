package com.jdi.isc.task.center.domain.joysky.biz;

/**
 * <AUTHOR>
 * @Date 2024/11/1
 * @Description joySky审批流结果消息定义
 * refer: https://joyspace.jd.com/pages/2SHD48tZffWHdULV8X0z
 */
public class JoySkyMessageVO {
    /**
     * 操作类型，表示当前审批流的操作类型。
     */
    private String operateType;

    /**
     * 流程定义的唯一标识符。
     */
    private String processDefinitionId;

    /**
     * 流程定义的唯一标识符，用于区分不同的流程定义。
     */
    private String processDefinitionKey;

    /**
     * 流程实例的唯一标识符。
     */
    private String processInstanceId;

    /**
     * 流程状态，表示当前审批流的状态。
     */
    private String processStatus;

    /**
     * 任务名称。
     */
    private String taskName;

    /**
     * 任务备注
     */
    private String taskComment;


    /**
     * 审批人信息
     */
    private JoySkyAssigneeVO assignee;


    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public void setProcessDefinitionKey(String processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public JoySkyAssigneeVO getAssignee() {
        return assignee;
    }

    public void setAssignee(JoySkyAssigneeVO assignee) {
        this.assignee = assignee;
    }

    public String getTaskComment() {
        return taskComment;
    }

    public void setTaskComment(String taskComment) {
        this.taskComment = taskComment;
    }

    @Override
    public String toString() {
        return "JoySkyMessageVO{" +
                "operateType='" + operateType + '\'' +
                ", processDefinitionId='" + processDefinitionId + '\'' +
                ", processDefinitionKey='" + processDefinitionKey + '\'' +
                ", processInstanceId='" + processInstanceId + '\'' +
                ", processStatus='" + processStatus + '\'' +
                ", taskName='" + taskName + '\'' +
                ", taskComment='" + taskComment + '\'' +
                ", assignee=" + assignee +
                '}';
    }
}
