package com.jdi.isc.task.center.domain.enums.customs;


import lombok.Getter;


/**
 * 关务管制枚举类
 */
@Getter
public enum CustomsControlsEnum {

    NOT_REQUIRED(0, "否"),
    REQUIRED(1, "是");

    private final Integer code;
    private final String desc;

    CustomsControlsEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举实例
     * @param code 枚举编码
     * @return 对应的枚举实例，未找到返回null
     */
    public static CustomsControlsEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CustomsControlsEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述文本
     * @param code
     * @return
     */
    public static String getDescByCode(Integer code) {
        CustomsControlsEnum customsControlsEnum = getByCode(code);
        return customsControlsEnum != null ? customsControlsEnum.desc : null;
    }

    /**
     * 检查code是否存在
     * @param code 枚举编码
     * @return 存在返回true，否则false
     */
    public static boolean contains(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 获取所有枚举值的描述（用于下拉框等场景）
     * @return 描述列表
     */
    public static String[] getAllDescs() {
        CustomsControlsEnum[] values = values();
        String[] descs = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            descs[i] = values[i].getDesc();
        }
        return descs;
    }

    @Override
    public String toString() {
        return this.desc;
    }
}