package com.jdi.isc.task.center.domain.countryMku;

import com.jdi.isc.task.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CountryMkuTranslationExcelVO extends BasicVO {

    /**
     * 国际MKU的唯一标识符。
     */
    private String mkuId;

    /**
     * 国际SKU的唯一标识符。
     */
    private String skuId;

    /**
     * mku中文名
     */
    private String mkuTitleZh;

    /**
     * mku英文名
     */
    private String mkuTitleEn;

    /**
     * mku目标国语言名称
     */
    private String targetCountryMkuTitle;

    /**
     * 目标国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * 目标国语言
     */
    private String targetCountryLang;
}
