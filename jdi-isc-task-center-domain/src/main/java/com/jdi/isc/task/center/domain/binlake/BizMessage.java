package com.jdi.isc.task.center.domain.binlake;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息基础类
 * <AUTHOR>
 */
@Data
public class BizMessage implements Serializable {

    /** SID*/
    private static final long serialVersionUID = -1;

    /** 主题*/
    protected String topic;

    /** 业务主键*/
    protected String businessId;

    /** 接受记录时间*/
    protected Date recordTime;

    /**
     * 业务渠道
     */
    protected String bizCode;


}
