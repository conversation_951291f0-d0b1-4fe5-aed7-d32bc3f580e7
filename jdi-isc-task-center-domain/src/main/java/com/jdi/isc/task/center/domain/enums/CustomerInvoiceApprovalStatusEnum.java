package com.jdi.isc.task.center.domain.enums;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

@AllArgsConstructor
@Getter
public enum CustomerInvoiceApprovalStatusEnum {

    /**
     * 3:审批通过（待开票）;
     * 4:驳回;
     * 5:已开票
     * 6:开票驳回
     * 7:开票异常
     */
    WAIT_APPROVAL_ERROR(-99, null, "开票数据异常"),

    WAIT_APPROVAL(0, null, "待审批"),

    APPROVAL_PASS(1,3, "审批通过"),

    APPROVAL_REJECT(-1, 4, "已驳回"),

    CREATE_INVOICE_FINISH(2,5, "已开票"),

    INVOICE_REJECT(-2, 6, "开票驳回"),

    INVOICE_ERROR(-3, 7, "开票异常")

    ;

    private Integer status;

    private Integer msgStatus;

    private String desc;

    public static CustomerInvoiceApprovalStatusEnum getStatusByMsgStatus(Integer msgStatus){
        for(CustomerInvoiceApprovalStatusEnum approvalStatus : CustomerInvoiceApprovalStatusEnum.values()){
            if(msgStatus.equals(approvalStatus.getMsgStatus())){
                return approvalStatus;
            }
        }
        return null;
    }

    public static Set<Integer> getCannotSubmitMsgStatuses(){
        return Sets.newHashSet(APPROVAL_PASS.getStatus(), WAIT_APPROVAL.getStatus(), CREATE_INVOICE_FINISH.getStatus());
    }

    public static Set<Integer> getFailStatuses(){
        return Sets.newHashSet(APPROVAL_REJECT.getStatus(), INVOICE_REJECT.getStatus(), INVOICE_ERROR.getStatus());
    }

    public static Set<Integer> getSuccessStatuses(){
        return Sets.newHashSet(CREATE_INVOICE_FINISH.getStatus());
    }

}
