package com.jdi.isc.task.center.domain.price.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.task.center.api.common.BasicDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 国家价格流水实体
 * <AUTHOR>
 * @date 2025/3/4
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("jdi_isc_price_log_sharding")
public class PriceLogPO extends BasicDTO {

    /** 业务id,如SkuId*/
    private String bizId;
    /** 价格类型  */
    private Integer bizType;
    /** '价格'*/
    private BigDecimal bizValue;
    /** '币种'*/
    private String currency;
    /** 预留字段一 */
    private String value1;
    /** 预留字段二 */
    private String value2;


}
