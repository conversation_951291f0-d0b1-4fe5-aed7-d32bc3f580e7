package com.jdi.isc.task.center.domain.task.excel.countrTax;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.task.center.domain.task.dto.BaseWorkerDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 服务商导入评估进入到泰国商品的HSCODE，管制信息等相关数据
 * <AUTHOR>
 * @date 20250705
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ThSkuHsCodeExcelDTO extends BaseWorkerDTO {

    /** jdSkuId id*/
    @ExcelProperty(index = 0,value = "* 零售SKU id")
    private String jdSkuId;

    /** SkuId id*/
    @ExcelProperty(index = 1,value = "国际SKU id")
    private String SkuId;

    @ExcelProperty(index = 2,value = "* 申报英文品名")
    private String declarationName;

    @ExcelProperty(index = 3,value = "* 申报泰国品名")
    private String declarationLocalName;

    @ExcelProperty(index = 5,value = "* 申报要素")
    private String declarationElement;


    /** HScode*/
    @ExcelProperty(index = 6,value = "* HScode")
    private String hsCode;

    /** 最惠国关税率*/
    @ExcelProperty(index = 7,value = "* 最惠国关税率%")
    private String mfnTax;

    /** formE关税率*/
    @ExcelProperty(index = 8,value = "* 原产地优惠关税(Form E)")
    private String formeTax;

    /** 消费税率*/
    @ExcelProperty(index = 9,value = "消费税率(Excise tax)")
    private String consumptionTax;

    /** 本地税率*/
    @ExcelProperty(index = 10,value = "本地税率(Local tax)")
    private String localTax;

    /** 反倾销税率*/
    @ExcelProperty(index = 11,value = "* 反倾销税率(Anti_dumpingTax)")
    private String antiDumpingTax;


    /** 增值税率*/
    @ExcelProperty(index = 12,value = "* 增值税率(VAT)")
    private String valueAddedTax;


    /** 是否管制*/
    @ExcelProperty(index = 13 ,value = "* 是否管制")
    private String isControlled;

    /** 是否需要tisi*/
    @ExcelProperty(index = 14 ,value = "管制信息")
    private String controlReason;

    /** 品牌授权*/
    @ExcelProperty(index = 15 ,value = "品牌授权(泰国)")
    private String brandAuthorization;


    /** 备注*/
    @ExcelProperty(index = 16 ,value = "备注")
    private String remark;

}


    
