package com.jdi.isc.task.center.domain.forecast;

import com.jdi.isc.task.center.domain.task.vo.TaskStrategyVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * 预报备采购单pdf 实体对象
 *
 * <AUTHOR>
 * @description 预报备采购单pdf 实体对象
 * @date 2025/5/28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ForecastPurchaseOrderTaskPdfVO extends TaskStrategyVO {
    /**
     * 预报备货单的唯一标识号
     */
    @NotNull(message = "forecastOrderId is not null")
    private String forecastOrderId;

    protected List<String> localeList;

    /**
     * 供应商简码，标识订单关联的供应商信息
     */
    private String supplierCode; // 供应商简码

    /**
     * SKU总数量(所有SKU数量的加合)
     */
    private Integer skuNum; // sku总数量(所有skuNum的加合)

    /**
     * 预报备货单商品种类
     */
    private Integer skuKindNum; // 预报备货单商品种类

    /**
     * 预报备货单后台状态
     */
    private Integer forecastOrderStatus; // 预报备货单后台状态

    /**
     * 采购单类型【1.本土；2跨境；3混合】
     */
    private Byte forecastOrderType; // 采购单类型【1.本土；2跨境；3混合】

    /**
     * 目的国国家码
     */
    private String countryCode; // 目的国国家码

    /**
     * 备货仓编码
     */
    private String enterpriseWarehouseId; // 备货仓编码


    /**
     * 仓库编号
     */
    private String warehouseNo;



    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 扩展标，下游透传
     */
    private String forecastOrderExtInfo; // 扩展标，下游透传

    /**
     * 版本号
     */
    private Integer version; // 版本号

    /**
     * 原始父预报备货单号
     */
    private String parentForecastOrderId; // 原始父预报备货单号

    /**
     * 父预报备货单号
     */
    private String splitForecastOrderId; // 父预报备货单号

    /**
     * 预报备货单下单时间，子单下单时间为父单下单时间，早于createTime
     */
    private Long forecastCreateTime; // 预报备货单下单时间，子单下单时间为父单下单时间，早于createTime

    /**
     * 确认时间
     */
    private Long confirmTime; // 确认时间

    /**
     * 预报备货单的有效状态
     */
    private Integer validState; // 预报备货单的有效状态

    /**
     * 接单时间
     */
    private Long receiveTime; // 接单时间

    /**
     * 发货时间
     */
    private Long shippedTime; // 发货时间

    /**
     * 入仓时间
     */
    private Long enterWarehouseTime; // 入仓时间

    /**
     * 全部入库时间
     */
    private Long allStoredTime; // 全部入库时间

    /**
     * 取消时间
     */
    private Long cancelTime; // 取消时间

    /**
     * 是否报关状态 1报关，0不报关
     */
    private Byte customsClearance; // 是否报关状态 1报关，0不报关

    /**
     * 三方订单号/唯一ID
     */
    private String thirdOrderId; // 三方订单号/唯一ID

    /**
     * 入集货仓时间
     */
    private Long enterStorehouseTime; // 入集货仓时间

    /**
     * 出集货仓时间
     */
    private Long outStorehouseTime; // 出集货仓时间

    /**
     * 集运中心编号
     */
    private String storehouseId; // 集运中心编号

    /**
     * 入库单号
     */
    private String enterWarehouseNo; // 入库单号

    /**
     * 商品入库数量(商品表的加合)
     */
    private Integer inboundWarehouseWareNum; // 商品入库数量(商品表的加合)

    /**
     * 预拆单状态 0待预拆单，1已预拆单
     */
    private Byte preSplitStatus; // 预拆单状态 0待预拆单，1已预拆单

    /**
     * 预报备货单中包含的商品明细列表
     */
    List<ForecastOrderWareVO> forecastOrderWareList;

    /**
     * 预报备货单关联的收货人信息
     */
    private ForecastOrderConsigneeInfoVO forecastOrderConsignee;
    /**
     * 运输单号
     */
    private String waybillNum;

    /**
     * 是否录入过唛头
     */
    private boolean hasEnterMark;



    /** 包裹Ids */
    private Set<String> parcelIds;


    private String countryLang;
    /**
     * 国家id
     */
    private String countryId;
}