package com.jdi.isc.task.center.domain.task.excel.productCustoms;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.task.center.domain.task.dto.BaseWorkerDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @Description: 中国商品关务导入实体
 * 注意：不要随意修改 ExcelProperty 注解的value值，校验excel模板是否正确时，value值会和excel表头比较
 *
 * <AUTHOR>
 * @date 20250720
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProductCustomsCnExcelDTO extends BaseWorkerDTO {
    // 注意：不要随意修改 ExcelProperty 注解的value值，校验excel模板是否正确时，value值会和excel表头比较

    /**
     * 国内skuId
     */
    @NotNull(message = "国内jdSkuId不能为空")
    @ExcelProperty(index = 0, value = "*skuId-国内")
    private Long jdSkuId;

    /**
     * 国际skuId
     */
    @ExcelProperty(index = 1, value = "skuId-国际")
    private Long skuId;

    /**
     * 申报要素
     */
    @NotNull(message = "申报要素不能为空")
    @ExcelProperty(index = 2, value = "*申报要素")
    private String declarationElement;

    /**
     * 申报品名
     */
    @NotNull(message = "申报英文品名不能为空")
    @ExcelProperty(index = 3, value = "*申报英文品名")
    private String declarationName;

    /**
     * 申报多语言名
     */
    @NotNull(message = "申报中文品名不能为空")
    @ExcelProperty(index = 4, value = "*申报中文品名")
    private String declarationLocalName;

    /**
     * 海关编码
     */
    @NotNull(message = "海关编码hsCode不能为空")
    @Pattern(regexp="(^[0-9]{1,15}$)", message = "海关编码（HSCODE）只能是长度介于1-15位的纯数字字符串")
    @ExcelProperty(index = 5, value = "*HS CODE")
    private String hsCode;

    /**
     * 出口退税率
     */
    @NotNull(message = "出口退税率不能为空")
    @DecimalMin(value = "0", message = "出口退税率应介于0%-100%之间")
    @DecimalMax(value = "100", message = "出口退税率应介于0%-100%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "出口退税率最多两位小数")
    @ExcelProperty(index = 6, value = "*出口退税税率%")
    private String exportRebateRate;

    /**
     * 出口关税率
     */
    @DecimalMin(value = "0", message = "出口关税率应介于0%-100%之间")
    @DecimalMax(value = "100", message = "出口关税率应介于0%-100%之间")
    @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "出口关税率最多两位小数")
    @ExcelProperty(index = 7, value = "出口关税税率%")
    private String exportTaxRate;

    /**
     * 是否管制
     */
    @NotNull(message = "是否管制不能为空")
    @ExcelProperty(index = 8, value = "*是否管制")
    private String controls;

    /**
     * 管制信息
     */
    @Length(max = 500, message = "管制信息长度不能超过500")
    @ExcelProperty(index = 9, value = "管制信息（是否管制为是时，必填）")
    private String controlsInfo;

    /**
     * 品牌授权 有限制/无限制
     */
    @ExcelProperty(index = 10, value = "品牌授权（中国）有限制/无限制")
    private String brandLicensing;

    /**
     * 备注
     */
    @Length(max = 100, message = "备注长度不能超过100")
    @ExcelProperty(index = 11, value = "备注Remarks")
    private String remark;

}


    
