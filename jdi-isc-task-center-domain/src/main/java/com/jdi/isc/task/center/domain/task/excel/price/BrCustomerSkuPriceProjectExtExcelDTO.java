package com.jdi.isc.task.center.domain.task.excel.price;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.task.center.domain.task.excel.BrCustomerSkuPriceExcelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * The type Br customer sku price project ext excel dto.
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BrCustomerSkuPriceProjectExtExcelDTO extends BrCustomerSkuPriceExcelDTO implements Serializable {

    /**
     * sku名称
     */
    @ExcelProperty(value = "sku名称", index = 11)
    private String skuName;

    /**
     * 预警状态。
     */
    @ExcelProperty(value = "预警状态", index = 12)
    private String warningStatusName;

    /**
     * 预警状态。
     */
    @ExcelIgnore
    private Integer warningStatus;

    /**
     * 利润率。
     */
    @ExcelProperty(value = "利润率", index = 13)
    private String profitRateFormat;

    /**
     * 低利润率阈值
     */
    @ExcelProperty(value = "低利润率阈值", index = 14)
    private String profitLimitRateFormat;

    /**
     * 超低利润率阈值
     */
    @ExcelProperty(value = "超低利润率阈值", index = 15)
    private String lowProfitLimitRateFormat;

    /**
     * 边际负毛阈值
     */
    @ExcelProperty(value = "边际负毛阈值", index = 16)
    private String grossProfitFormat;

    /**
     * 国家成本价
     */
    @ExcelProperty(value = "国家成本价", index = 17)
    private String countryCostPrice;

    /**
     * 国家成本价计算流程。
     */
    @ExcelProperty(value = "国家成本价计算公式", index = 18)
    private String costMark;
}
