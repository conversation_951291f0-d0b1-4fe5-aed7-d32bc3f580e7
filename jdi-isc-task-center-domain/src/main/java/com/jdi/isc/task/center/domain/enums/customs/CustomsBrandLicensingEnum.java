package com.jdi.isc.task.center.domain.enums.customs;


import lombok.Getter;

import java.util.Arrays;

/**
 * 品牌授权限制
 * <p>用于标识品牌是否需要授权</p>
 */
@Getter
public enum CustomsBrandLicensingEnum {
    NOT_REQUIRED(0, "否"),
    REQUIRED(1, "是");

    private final Integer code;
    private final String desc;

    CustomsBrandLicensingEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据编码获取枚举实例
     * @param code 枚举编码
     * @return 对应枚举实例，未找到返回null
     */
    public static CustomsBrandLicensingEnum of(Integer code) {
        if (code == null) return null;
        for (CustomsBrandLicensingEnum e : values()) {
            if (e.code.equals(code)) return e;
        }
        return null;
    }

    /**
     * 根据code获取描述文本
     * @param code
     * @return
     */
    public static String getDescByCode(Integer code) {
        CustomsBrandLicensingEnum brandLicensingEnum = of(code);
        return brandLicensingEnum != null ? brandLicensingEnum.desc : null;
    }

    /**
     * 验证编码是否有效
     * @param code 待验证编码
     * @return 有效返回true，否则false
     */
    public static boolean isValid(Integer code) {
        return of(code) != null;
    }

    /**
     * 获取所有编码值（适合参数校验场景）
     * @return 有效编码集合
     */
    public static Integer[] validCodes() {
        return Arrays.stream(values())
                .map(CustomsBrandLicensingEnum::getCode)
                .toArray(Integer[]::new);
    }

    @Override
    public String toString() {
        return code + ":" + desc;
    }
}
