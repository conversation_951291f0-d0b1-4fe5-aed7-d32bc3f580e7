package com.jdi.isc.task.center.domain.forecast;

import com.jdi.isc.task.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class ForecastOrderWareVO extends BasicVO {

    /**
     * 预报备货单号
     */
    private String forecastOrderId;

    /**
     * 履约SKU编码
     */
    private Long skuId;

    /**
     * SKU数量
     */
    private Integer skuNum;



    /**
     * SKU名称
     */
    private String skuName;

    /**
     * 商品多语言名称
     */
    private Map<String, String> skuNameMap;


    /**
     * SPU ID，表示商品的标准产品单元标识
     */
    private Long spuId;



    /**
     * 销售单位
     */
    private String saleUnit;



    /**
     * SKU类型
     * 0：商品
     * 1：附件
     * 2：赠品
     */
    private Integer skuType ;

    /**
     * 父类SKU（主要赠品附件用）
     */
    private Long parentSkuId ;

    /**
     * 目的国家码
     */
    private String skuCountryCode;

    /**
     * SKU信息快照（不含商详）
     */
    private String skuJsonInfo;

    /**
     * 商品入库数量（商品表的加合）
     */
    private Integer inboundWarehouseWareNum;

}
