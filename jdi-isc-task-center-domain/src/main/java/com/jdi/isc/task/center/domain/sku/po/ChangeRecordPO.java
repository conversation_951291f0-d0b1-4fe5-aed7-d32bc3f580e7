package com.jdi.isc.task.center.domain.sku.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.task.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: spu商品信息变更记录表 实体类
 * @Author: mengqingyu19
 * @Date: 2025/02/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("jdi_isc_spu_change_record_sharding")
public class ChangeRecordPO extends BasicPO {
    /** spuID */
    private Long spuId;

    /** skuID */
    private Long skuId;

    /** 变更的表名 */
    private String changedTable;

    /** 修改的字段 */
    private String modifiedField;

    /** 修改前的值 */
    private String oldValue;

    /** 修改后的值 */
    private String newValue;
}