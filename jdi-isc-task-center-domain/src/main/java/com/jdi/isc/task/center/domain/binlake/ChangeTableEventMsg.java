package com.jdi.isc.task.center.domain.binlake;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.jd.binlog.client.WaveEntry;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * binLake解析实体
 */
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class ChangeTableEventMsg extends BizMessage {

    /** * 数据库名 */
    private String dataBase;
    /** 变更类型(1.insert 2.update 3.delete) */
    private WaveEntry.EventType changeType;
    /** * 表名 */
    private String tableName;
    /** * 变更前数据 */
    private Map<String, String> rowBefore;
    /** * 变更后数据 */
    private Map<String, String> rowAfter;
    /** * 变更数据 */
    private Map<String, String> changeFields;

    public String getBeforeValueStr(String key) {
        if (rowBefore == null) {
            rowBefore = Maps.newHashMap();
        }

        return rowBefore.get(key);
    }

    public String getAfterValueStr(String key) {
        if (rowAfter == null) {
            rowAfter = Maps.newHashMap();
        }

        return rowAfter.get(key);
    }

    public String getBeforeValue(String key) {
        return getBeforeValueStr(key);
    }

    public String getAfterValue(String key) {
        return getAfterValueStr(key);
    }

    public <T> T getBeforeValue(String key, Class<T> clazz) {
        if (clazz == String.class) {
            //noinspection unchecked
            return (T) getBeforeValueStr(key);
        }
        try {
            return JSONObject.parseObject(this.getBeforeValueStr(key), clazz);
        } catch (Exception e) {
            log.error("getBeforeValue, parse binlak key is error,[{}] [{}]", tableName, key, e);
            throw new IllegalArgumentException(String.format("parse binlake key is error, [%s][%s]", tableName, key));
        }
    }

    public <T> T getAfterValue(String key, Class<T> clazz) {

        if (clazz == String.class) {
            //noinspection unchecked
            return (T) getAfterValueStr(key);
        }

        try {
            return JSONObject.parseObject(this.getAfterValueStr(key), clazz);
        } catch (Exception e) {
            log.error("getAfterValue, parse binlak key is error,[{}] [{}]", tableName, key, e);
            throw new IllegalArgumentException(String.format("parse binlake key is error, [%s][%s]", tableName, key));
        }
    }

    public Map<String, String> getChangeFields() {
        return this.changeFields == null ? Maps.newHashMap() : this.changeFields;
    }
}
