package com.jdi.isc.task.center.domain.forecast;

import com.jdi.isc.task.center.domain.task.vo.TaskStrategyVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 预报备采购单异步任务VO
 *
 * <AUTHOR>
 * @description 预报备采购单异步任务VO
 * @date 2025/5/28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ForecastPurchaseOrderTaskVO extends TaskStrategyVO {

    /**
     * 预报备采购单号
     */
    private String forecastOrderId;

    /**
     * 采购单号
     */
    private String supplierCode;


    /**
     * 多语言列表
     */
    private List<String> langList;

}