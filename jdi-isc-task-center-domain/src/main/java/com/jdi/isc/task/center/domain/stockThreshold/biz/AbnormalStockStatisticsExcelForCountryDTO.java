package com.jdi.isc.task.center.domain.stockThreshold.biz;


import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.product.soa.api.supplier.res.SupplierBaseInfoRes;
import com.jdi.isc.task.center.domain.sku.biz.SkuInfoVO;
import com.jdi.isc.task.center.domain.stockThreshold.po.AbnormalStockStatisticsPO;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @description：异常库存数据统计DTO
 * @Date 2025-01-18
 */
@Data
public class AbnormalStockStatisticsExcelForCountryDTO extends AbnormalStockStatisticsExcelDTO{

    public AbnormalStockStatisticsExcelForCountryDTO(AbnormalStockStatisticsPO po, Map<Long, SkuInfoVO> skuNameMap, String warehouseName, Map<String, SupplierBaseInfoRes> supplierBaseMap, Map<Integer,String> catNameMap) {
        this.skuId = String.valueOf(po.getSkuId());
        this.warehouseNo = po.getWarehouseNo();
        this.stock = String.valueOf(po.getStock());
        this.skuName = skuNameMap.getOrDefault(po.getSkuId(), new SkuInfoVO()).getSkuName();
        this.warehouseName = warehouseName;
        this.supplierName = supplierBaseMap.getOrDefault(po.getVendorCode(), new SupplierBaseInfoRes()).getBusinessLicenseName();
        this.firstCatName = catNameMap.get(1);
    }
}
