package com.jdi.isc.product.soa.service.atomic.spu;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.spu.po.ProductGlobalAttributePO;
import com.jdi.isc.product.soa.repository.mapper.spu.ProductGlobalAttributeBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: spu/sku跨境属性原子服务
 * @Author: zhaokun51
 * @Date: 2024/12/16 09:49
 **/
@Slf4j
@Service
public class ProductGlobalAttributeAtomicService extends ServiceImpl<ProductGlobalAttributeBaseMapper, ProductGlobalAttributePO> {

    /**
     * 未删除的对象
     * @param id id
     * @return 对象
     */
    public ProductGlobalAttributePO getValidById(Long id){
        LambdaQueryWrapper<ProductGlobalAttributePO> wrapper = Wrappers.<ProductGlobalAttributePO>lambdaQuery()
                .eq(ProductGlobalAttributePO::getId, id)
                .eq(ProductGlobalAttributePO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    /**
     * 根据商品ID获取其全局属性列表。
     * @param spuId 商品ID
     * @return 全局属性列表
     */
    public List<ProductGlobalAttributePO> getBySpuId(Long spuId){
        if(spuId == null){
            return null;
        }
        LambdaQueryWrapper<ProductGlobalAttributePO> wrapper = Wrappers.<ProductGlobalAttributePO>lambdaQuery()
                .eq(ProductGlobalAttributePO::getKeyType, KeyTypeEnum.SPU.getCode())
                .eq(ProductGlobalAttributePO::getKeyId, spuId)
                .eq(ProductGlobalAttributePO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }

    /**
     * 根据商品ID获取其全局属性列表。
     * @param spuIds 商品ID
     * @return 全局属性列表
     */
    @PFTracing
    public List<ProductGlobalAttributePO> getBySpuId(Collection<Long> spuIds){
        if(CollectionUtils.isEmpty(spuIds)){
            return null;
        }
        LambdaQueryWrapper<ProductGlobalAttributePO> wrapper = Wrappers.<ProductGlobalAttributePO>lambdaQuery()
                .eq(ProductGlobalAttributePO::getKeyType, KeyTypeEnum.SPU.getCode())
                .in(ProductGlobalAttributePO::getKeyId, spuIds)
                .eq(ProductGlobalAttributePO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }

    /**
     * 根据skuID获取其全局属性列表。
     * @param skuId skuId
     * @return 全局属性列表
     */
    public List<ProductGlobalAttributePO> getBySkuId(Long skuId){
        if(skuId == null){
            return null;
        }
        LambdaQueryWrapper<ProductGlobalAttributePO> wrapper = Wrappers.<ProductGlobalAttributePO>lambdaQuery()
                .eq(ProductGlobalAttributePO::getKeyType, KeyTypeEnum.SKU.getCode())
                .eq(ProductGlobalAttributePO::getKeyId, skuId)
                .eq(ProductGlobalAttributePO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }

    /**
     * 根据skuID获取其全局属性列表。
     * @param skuIds skuIds
     * @return 全局属性列表
     */
    @PFTracing
    public List<ProductGlobalAttributePO> getBySkuIds(Collection<Long> skuIds){
        if(CollectionUtils.isEmpty(skuIds)){
            return null;
        }
        LambdaQueryWrapper<ProductGlobalAttributePO> wrapper = Wrappers.<ProductGlobalAttributePO>lambdaQuery()
                .eq(ProductGlobalAttributePO::getKeyType, KeyTypeEnum.SKU.getCode())
                .in(ProductGlobalAttributePO::getKeyId, skuIds)
                .eq(ProductGlobalAttributePO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }

    /**
     * 根据skuID获取其全局属性列表。
     * @param skuIds skuIds
     * @return 全局属性列表
     */
    public List<ProductGlobalAttributePO> getBySkuIdsAndAttributeIds(Collection<Long> skuIds,Collection<Long> attributeIds){
        if(CollectionUtils.isEmpty(skuIds) || CollectionUtils.isEmpty(attributeIds)){
            return null;
        }
        LambdaQueryWrapper<ProductGlobalAttributePO> wrapper = Wrappers.<ProductGlobalAttributePO>lambdaQuery()
                .eq(ProductGlobalAttributePO::getKeyType, KeyTypeEnum.SKU.getCode())
                .in(ProductGlobalAttributePO::getKeyId, skuIds)
                .in(ProductGlobalAttributePO::getAttributeId, attributeIds)
                .eq(ProductGlobalAttributePO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }

    /**
     * 根据商品skuId集合批量删除商品全局属性。
     * @param skuIds 商品skuId集合
     * @return 删除操作结果
     */
    public Boolean deleteBySkuIdsAndAttributeIds(Collection<Long> skuIds,Collection<Long> attributeIds){
        if(CollectionUtils.isEmpty(skuIds) || CollectionUtils.isEmpty(attributeIds)){
            log.error("ProductGlobalAttributeAtomicService.deleteBySkuIds param is null");
            return Boolean.FALSE;
        }

        List<ProductGlobalAttributePO> dbPOS = this.getBySkuIdsAndAttributeIds(skuIds,attributeIds);
        if(CollectionUtils.isEmpty(dbPOS)){
            log.error("ProductGlobalAttributeAtomicService.deleteBySkuIds dbPOS is null");
            return Boolean.FALSE;
        }
        dbPOS.forEach(item->{
            item.setKeyId(null);
            item.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
            item.setUpdateTime(new Date().getTime());
            item.setYn(YnEnum.NO.getCode());
        });
        return this.saveOrUpdateBatch(dbPOS);
    }

    /**
     * 根据skuID获取其全局属性列表。
     * @param skuIds skuIds
     * @return 全局属性列表
     */
    public List<ProductGlobalAttributePO> queryGlobalAttributeListByIds(Collection<Long> skuIds, Set<Long> attributeIds, Integer dimension){
        if(CollectionUtils.isEmpty(skuIds)){
            return null;
        }
        LambdaQueryWrapper<ProductGlobalAttributePO> wrapper = Wrappers.<ProductGlobalAttributePO>lambdaQuery()
                .eq(ProductGlobalAttributePO::getKeyType, dimension)
                .in(CollectionUtils.isNotEmpty(attributeIds) ,ProductGlobalAttributePO::getAttributeId, attributeIds)
                .in(ProductGlobalAttributePO::getKeyId, skuIds)
                .eq(ProductGlobalAttributePO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }

    /**
     * 根据 SKU 和属性 ID 获取全局属性映射。
     * @param skuIds SKU ID 集合
     * @param attributeIds 属性 ID 集合
     * @return 全局属性映射，键为属性 ID，值为对应的 ProductGlobalAttributePO 列表
     */
    public Map<Long,List<ProductGlobalAttributePO>> queryGlobalAttributeMapBySkuAndIds(Set<Long> skuIds, Set<Long> attributeIds){
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }

        List<ProductGlobalAttributePO> globalAttributePoList = this.queryGlobalAttributeListByIds(skuIds, attributeIds,KeyTypeEnum.SKU.getCode());
        return Optional.ofNullable(globalAttributePoList).orElseGet(ArrayList::new)
                .stream().collect(Collectors.groupingBy(ProductGlobalAttributePO::getKeyId));
    }

    /**
     * 根据提供的 SKU IDs 和属性 IDs，查询并返回指定维度的产品全局属性映射。
     * @param skuIds SKU IDs 集合
     * @param attributeIds 属性 IDs 集合
     * @param dimension 属性维度
     * @return 一个 Map，key 为 SKU ID，value 为该 SKU ID 对应的属性 ID 和属性值的 Map
     */
    public Map<Long,Map<Long,List<ProductGlobalAttributePO>>> queryProductGlobalAttributeMap(Set<Long> skuIds, Set<Long> attributeIds, Integer dimension){
        if (CollectionUtils.isEmpty(skuIds) || Objects.isNull(dimension)) {
            return Collections.emptyMap();
        }

        List<ProductGlobalAttributePO> globalAttributePOList = this.queryGlobalAttributeListByIds(skuIds, attributeIds, dimension);

        return Optional.ofNullable(globalAttributePOList)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.groupingBy(ProductGlobalAttributePO::getKeyId, Collectors.groupingBy(ProductGlobalAttributePO::getAttributeId)));
    }


    /**
     * 根据 SKU ID 获取对应的全局属性列表，按 ID 降序排列。
     * @param skuId SKU 的 ID
     * @return 全局属性列表，若 SKU ID 为空则返回 null
     * 无YN的
     */
    public List<ProductGlobalAttributePO> getBySkuIdNoYn(Long skuId){
        if(skuId == null){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProductGlobalAttributePO> wrapper = Wrappers.<ProductGlobalAttributePO>lambdaQuery()
            .eq(ProductGlobalAttributePO::getKeyType, KeyTypeEnum.SKU.getCode())
            .eq(ProductGlobalAttributePO::getKeyId, skuId)
            .orderByDesc(ProductGlobalAttributePO::getId)
            ;
        List<ProductGlobalAttributePO> list = this.list(wrapper);
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 根据SKU ID和属性ID获取产品全局属性信息
     * @param skuId SKU ID
     * @param attributeId 属性ID
     * @return 匹配的产品全局属性信息，未找到返回null
     */
    public ProductGlobalAttributePO getBySkuIdAndAttributeId(Long skuId, Long attributeId){
        if(skuId == null || attributeId == null){
            return null;
        }
        LambdaQueryWrapper<ProductGlobalAttributePO> wrapper = Wrappers.<ProductGlobalAttributePO>lambdaQuery()
                .eq(ProductGlobalAttributePO::getKeyType, KeyTypeEnum.SKU.getCode())
                .eq(ProductGlobalAttributePO::getKeyId, skuId)
                .eq(ProductGlobalAttributePO::getAttributeId, attributeId)
                .eq(ProductGlobalAttributePO::getYn, YnEnum.YES.getCode());
        List<ProductGlobalAttributePO> list = this.list(wrapper);

        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.get(0);
    }
}
