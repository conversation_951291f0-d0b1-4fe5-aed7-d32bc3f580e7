package com.jdi.isc.product.soa.service.manage.mku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuLangsReqDTO;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuReqDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuLangsResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuResDTO;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerMku.biz.CustomerLatestMkuReqVO;
import com.jdi.isc.product.soa.domain.mku.biz.*;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.SaveSpuVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 客户端服务接口
 * @Author: zhaokun51
 * @Date: 2024/06/24 15:29
 **/
public interface MkuManageService {


    /**
     * 同步商品信息通过SPU和SKU
     * @param saveSpuVO 保存SPU信息的对象
     * @return 同步是否成功
     */
    Boolean syncMkuInfoBySpuAndSku(SaveSpuVO saveSpuVO);


    /**
     * 判断指定的MkuClientPageReqVO对象是否存在
     * @param input MkuClientPageReqVO对象，用于检查是否存在
     * @return 如果存在则返回对应的Long值，否则返回null
     */
    Long existsMku(MkuClientPageReqVO input);


    /**
     * 判断指定 SKU 是否存在
     * @param skuId SKU ID
     * @return 如果 SKU 存在则返回对应的 MkuClientVO 对象，否则返回 null
     */
    Long queryMkuBySkuId(Long skuId);

    /**
     * 根据 MKU ID 列表查询对应的 SKU PO 映射
     * @param mkuIds MKU ID 列表
     * @return MKU ID 到 SKU PO 的映射关系
     */
    Map<Long, SkuPO> queryMkuIdSkuPoMapByMkuIds(List<Long> mkuIds);


    /**
     * 从客户端池中检查并返回非黑名单的客户端列表。
     * @param mkuReqList 客户端详细信息请求列表。
     * @return 非黑名单的客户端列表。
     */
    List<MkuClientInPoolVO> checkInPoolNoBlack(List<MkuClientDetailReqVO> mkuReqList);

    /**
     * 对外查询查询mku信息
     */
    Map<Long, IscMkuResDTO> getIscMku(BatchQueryMkuReqDTO req);

    /**
     * 根据jdSkuId查询查询国际mkuID
     */
    Map<Long, Set<Long>> getIscMkuIdByJdSkuId(BatchQueryMkuReqDTO req);

    /**
     * 根据 Mku IDs 查询特殊属性映射表。
     * @param input SpecialAttrMkuReqVO 对象，包含要查询的 Mku IDs。
     * @return DataResponse 对象，包含一个 Map，键为 Mku ID，值为特殊属性的名称和值的映射表。
     */
    DataResponse<Map<Long,Map<String,String>>> querySpecialAttrMapByMkuIds(SpecialAttrMkuReqVO input);

    /**
     * 最近入池的商品
     * @param input
     * @return
     */
    DataResponse<List<MkuClientVO>> latestWares(CustomerLatestMkuReqVO input);

    /**
     * 查询商品信息
     * @param input
     * @return
     */
    DataResponse<List<MkuClientVO>> queryWaresInfo(MkuListInfoReqReqVO input);

    /**
     * 根据 Mku IDs 和客户编码查询特殊属性映射表。
     * @param input SpecialAttrMkuReqVO 对象，包含要查询的 Mku IDs和客户编码。
     * @return DataResponse 对象，包含一个 Map，键为 Mku ID，值为特殊属性的名称和值的映射表。
     */
    DataResponse<Map<Long,Map<String,String>>> querySpecialAttrMapByClientCode(SpecialAttrMkuClientReqVO input);

    /**
     * 根据sku集合查询mkuId
     * @param req skuId集合
     * @return sku和mku的关系
     */
    Map<Long, Long> getIscMkuIdBySkuId(BatchQueryMkuReqDTO req);

    /**
     * 批量查询mku的多语言信息
     * @Description: 涉及多语言的信息，都以多语言的形式返回
     * @param req
     * @return
     */
    Map<Long, IscMkuLangsResDTO> getIscMkuLangs(BatchQueryMkuLangsReqDTO req);

    /**
     * 根据mku集合查询skuId
     * @param req mkuId集合
     * @return mku和sku的关系
     */
    Map<Long, Long> getIscSkuIdByMkuId(BatchQueryMkuReqDTO req);

    /**
     * 根据 MKU ID 批量查询对应的 ISC JD SKU ID。
     * @param req 包含需要查询的 MKU ID 的请求对象。
     * @return 一个 Map 对象，其中键为 MKU ID，值为对应的 ISC JD SKU ID。
     */
    Map<Long,Long> getIscJdSkuIdByMkuId(BatchQueryMkuReqDTO req);

    /**
     * 根据 MKU ID 批量查询 ISC SPU SKU 关系。
     * @param req 包含 MKU ID 的请求对象。
     * @return ISC SPU SKU 关系的 Map，key 为 MKU ID，value 为是否存在 ISC SPU SKU 关系的布尔值。
     */
    Map<Long, Boolean> getIscSpuSkuRelationByMkuId(BatchQueryMkuReqDTO req);

    /**
     * 补充展示的商品信息
     * @param clientVOList
     * @param customerVO
     * @param lang
     * @param stationType
     */
    void buildMkuShowInfo(List<MkuClientVO> clientVOList, CustomerVO customerVO, String lang, Integer stationType);

}
