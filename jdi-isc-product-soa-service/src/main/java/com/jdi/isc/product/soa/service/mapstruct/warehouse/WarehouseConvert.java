package com.jdi.isc.product.soa.service.mapstruct.warehouse;


import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.biz.req.WarehouseSkuUnBindReq;
import com.jdi.isc.order.center.api.biz.resp.WarehouseSkuUnBindResp;
import com.jdi.isc.product.soa.api.warehouse.base.WarehouseDTO;
import com.jdi.isc.product.soa.api.warehouse.base.WarehouseLangDTO;
import com.jdi.isc.product.soa.api.warehouse.base.WarehouseSkuDTO;
import com.jdi.isc.product.soa.api.warehouse.req.*;
import com.jdi.isc.product.soa.api.warehouse.res.*;
import com.jdi.isc.product.soa.domain.warehouse.biz.*;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehouseLangPO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehousePO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehouseSkuPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Set;

/**
 * @Description: 仓信息表对象转换
 * @Author: wangpeng965
 * @Date: 2024/07/18 11:23
 **/
@Mapper
public interface WarehouseConvert {

    WarehouseConvert INSTANCE = Mappers.getMapper(WarehouseConvert.class);

    WarehousePO vo2Po(WarehouseVO vo);

    WarehouseVO po2Vo(WarehousePO po);

    List<WarehouseVO> listPo2Vo(List<WarehousePO> poList);

    List<WarehousePO> listVo2Po(List<WarehouseVO> voList);

    List<WarehouseLangPO> listLangVo2Po(List<WarehouseLangVO> voList);

    List<WarehouseLangVO> listLangPo2Vo(List<WarehouseLangPO> poList);

    List<WarehouseLangVO> listLangDTO2Vo(List<WarehouseLangDTO> dtoList);


    @Mapping(source = "warehouseDTO",target = "warehouseVO")
    @Mapping(source = "warehouseLangDTOList",target = "warehouseLangVOList")
    WarehouseUpdateVO updateDto2Vo(WarehouseUpdateReqDTO reqDTO);

    WarehouseVO warehouseDto2Vo(WarehouseDTO warehouseDTO);

    @Mapping(source = "warehouseVO", target = "warehouseDTO")
    @Mapping(source = "warehouseLangVOList", target = "warehouseLangDTOList")
    WarehouseDetailResDTO detailVo2Dto(WarehouseDetailVO detailVO);


    WarehousePageReqVO searchDto2Vo(WarehousePageReqDTO reqDTO);

    List<WarehouseResDTO> listResVo2Dto(List<WarehouseResVO> list);

    PageInfo<WarehouseResDTO> pageResVo2Dto(PageInfo<WarehouseResVO> pageInfo);

    List<WarehouseSkuVO> listWarehouseSkuDTO2Vo(List<WarehouseSkuDTO> warehouseSkuDTOList);

    List<WarehouseSkuPO> listWarehouseSkuVo2Po(List<WarehouseSkuVO> warehouseSkuVOList);


    WarehouseSkuRelationResDTO relationResVO2DTO(WarehouseSkuRelationResVO resVO);

    WarehouseSkuPageVO getReqDTO2Vo(WarehouseSkuPageReqDTO reqDTO);

    List<WarehouseSkuResDTO> listSkuResVO2DTO(List<WarehouseSkuResVO> skuResVOList);

    PageInfo<WarehouseSkuResDTO> pageSkuResVo2DTO(PageInfo<WarehouseSkuResVO> pageInfo);

    WarehouseBatchGetReqVO batchGetDTO2VO(WarehouseBatchGetReqDTO batchGetReqDTO);

    WarehouseResVO po2ResVo(WarehousePO warehousePO);

    WarehouseSkuResVO warehouseSkuPO2ResVo(WarehouseSkuPO warehouseSkuPO);

    WarehouseSkuVO warehouseSkuPo2VO(WarehouseSkuPO skuPO);

    WarehouseSkuDTO warehouseSkuVo2Dto(WarehouseSkuVO warehouseSkuVO);

    WarehouseBatchSkuVo batchSkuReq2Vo(WarehouseBatchSkuReqDTO batchSkuReq);

    WarehouseSkuVO warehouseOnWaySaleReqDTO2Vo(WarehouseUpdateOnWaySaleReqDTO reqDTO);

    WarehouseResVO warehouseResDTOToWarehouseVO(WarehouseResDTO warehouseResDTO);

    PageInfo<WarehouseConsignSkuResDTO> pageSkuResVo2ConsignDTO(PageInfo<WarehouseSkuResVO> pageInfo);

    Set<WarehouseSkuUnBindVO> warehouseSkuUnBindReqDTOToVO(Set<WarehouseSkuUnBindItemReqDTO> warehouseSkuUnBindReqs);

    List<WarehouseSkuUnBindResDTO> warehouseSkuUnBindResVOToDTO(List<WarehouseSkuUnBindResVO> warehouseSkuUnBindResVO);

    Set<WarehouseSkuUnBindReq> setWarehouseSkuUnbindVO2DTO(Set<WarehouseSkuUnBindVO> warehouseSkuUnBindVOS);

    List<WarehouseSkuUnBindResVO> listWarehouseSkuUnbindDTO2VO(List<WarehouseSkuUnBindResp> warehouseSkuUnBindResps);

}
