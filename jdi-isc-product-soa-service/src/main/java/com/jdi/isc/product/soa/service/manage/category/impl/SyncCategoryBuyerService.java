package com.jdi.isc.product.soa.service.manage.category.impl;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.wimp.category.req.CategoryBuyerRelationSyncReqApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.res.CategoryBuyerRelationSyncResApiDTO;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.util.validation.ValidationUtil;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.SaveSpuVO;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.service.atomic.mku.MkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDraftAtomicService;
import com.jdi.isc.product.soa.service.manage.spu.ProductMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 同步类目的采销信息.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SyncCategoryBuyerService {

    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private MkuAtomicService mkuAtomicService;

    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;

    @Resource
    private ProductMessageService productMessageService;

    @Resource
    private OperDuccConfig operDuccConfig;

    /**
     * Execute data response.
     *
     * @param input the input
     * @return the data response
     */
    @ToolKit(exceptionWrap = true, validFlag = false)
    public DataResponse<CategoryBuyerRelationSyncResApiDTO> execute(CategoryBuyerRelationSyncReqApiDTO input) {
        String errorMessage = ValidationUtil.validateFindFirstError(input);

        if (StringUtils.isNotEmpty(errorMessage)) {
            return DataResponse.error(errorMessage);
        }

        if (StringUtils.isEmpty(input.getPin())) {
            return DataResponse.error("操作人不能为空");
        }

        // 不是灰度直接返回
        if (!operDuccConfig.isGrayJdCatIds(input.getJdCatId())) {
            log.info("非灰度jd类目id:{}", input.getJdCatId());
            return DataResponse.success();
        }

        // 采销
        String buyer = input.getBuyer();

        // 查询spu草稿
        List<SpuDraftPO> spuDrafts = this.querySpuDrafts(input);

        // 查询spu（这里要用类目查不能用spuId查，因为草稿数据的采销可能和真实表数据不一致）
        List<SpuPO> spus = this.querySpus(input);

        // 更新的时候过滤一下无需更新的数据, 由于没有事务，每次发送类目下所有的spu信息
        List<SpuPO> filterSpus = spus.stream().filter(item -> !StringUtils.equals(item.getBuyer(), buyer)).collect(Collectors.toList());

        // 查询mku
        List<MkuPO> mkus = this.queryMkus(input);

        // 天然幂等不需要事务
        // 更新spu草稿buyer
        spuDraftAtomicService.updateBuyer(spuDrafts, buyer, input.getPin());
        // 更新spu buyer
        spuAtomicService.updateBuyer(filterSpus, buyer, input.getPin());
        // 更新mku buyer
        mkuAtomicService.updateBuyer(mkus, buyer, input.getPin());

        // 发送sku变更消息
        List<Long> spuIds = spus.stream().map(SpuPO::getSpuId).collect(Collectors.toList());
        productMessageService.sendSkuChangeMsg(spuIds);

        return DataResponse.success();
    }

    private List<SpuPO> querySpus(CategoryBuyerRelationSyncReqApiDTO input) {
        Long jdCatId = input.getJdCatId();
        String countryCode = input.getCountryCode();

        return spuAtomicService.listBySourceCountryCodeAndJdCatId(countryCode, jdCatId);
    }

    private List<MkuPO> queryMkus(CategoryBuyerRelationSyncReqApiDTO input) {
        Long jdCatId = input.getJdCatId();
        String buyer = input.getBuyer();
        String countryCode = input.getCountryCode();

        List<MkuPO> mkus = mkuAtomicService.listBySourceCountryCodeAndJdCatId(countryCode, jdCatId);

        return mkus.stream().filter(item -> !StringUtils.equals(item.getBuyer(), buyer)).collect(Collectors.toList());
    }

    /**
     * 返回buyer有变化的
     */
    private List<SpuDraftPO> querySpuDrafts(CategoryBuyerRelationSyncReqApiDTO input) {

        Long jdCatId = input.getJdCatId();
        String buyer = input.getBuyer();
        String countryCode = input.getCountryCode();

        List<SpuDraftPO> spuDrafts = spuDraftAtomicService.listBySourceCountryCodeAndJdCatId(countryCode, jdCatId);

        // 查询符合条件的sku
        return spuDrafts.stream().filter(item -> {
            SaveSpuVO saveSpuVO = JSON.parseObject(item.getSpuJsonInfo(), SaveSpuVO.class);
            if (saveSpuVO == null || saveSpuVO.getSpuVO() == null) {
                return false;
            }
            String spuBuyer = saveSpuVO.getSpuVO().getBuyer();
            return !StringUtils.equals(buyer, spuBuyer);
        }).collect(Collectors.toList());
    }
}
