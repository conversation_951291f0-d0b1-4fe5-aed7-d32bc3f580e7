package com.jdi.isc.product.soa.service.manage.sku.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.ka.gpt.soa.client.goods.request.AreaBaseInfoGoodsReq;
import com.jd.ka.gpt.soa.client.goods.request.GetStockByIdGoodsReq;
import com.jd.ka.gpt.soa.client.goods.request.SkuNumBaseGoodsReq;
import com.jd.ka.gpt.soa.client.goods.resp.GetStockByIdGoodsResp;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.vms.i18n.external.pojo.VendorVo;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.CalculatePriceEnums;
import com.jdi.isc.product.soa.api.common.enums.GlobalAttributeVnEnum;
import com.jdi.isc.product.soa.api.common.enums.SkuQueryEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuStatusEnum;
import com.jdi.isc.product.soa.api.sku.base.CustomsApiDTO;
import com.jdi.isc.product.soa.api.sku.req.QuerySkuReqDTO;
import com.jdi.isc.product.soa.api.sku.req.SkuPredictPriceReqDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuBaseInfoApiDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuFeatureApiDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuPredictPriceResDTO;
import com.jdi.isc.product.soa.api.sku.res.VendorInfoApiDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.AttributeDimensionEnum;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.domain.brand.biz.BrandLangVO;
import com.jdi.isc.product.soa.domain.brand.biz.BrandVO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryPathVO;
import com.jdi.isc.product.soa.domain.category.biz.GlobalQualificationVO;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.product.soa.domain.enums.CompanyTypeEnum;
import com.jdi.isc.product.soa.domain.enums.SkuScopeEnum;
import com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum;
import com.jdi.isc.product.soa.domain.enums.attribute.GlobalAttributeEnum;
import com.jdi.isc.product.soa.domain.enums.attribute.GlobalAttributeValueRealtionEnum;
import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.domain.enums.product.ReturnTypeEnum;
import com.jdi.isc.product.soa.domain.enums.spu.SkuTaxCalculateCountryEnums;
import com.jdi.isc.product.soa.domain.enums.spu.SpuTaxCountryEnums;
import com.jdi.isc.product.soa.domain.enums.warehouse.OnWaySaleTypeEnum;
import com.jdi.isc.product.soa.domain.gms.req.JdProductQueryDTO;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceReqVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceResVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceVO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.sku.biz.*;
import com.jdi.isc.product.soa.domain.sku.po.SkuCertificatePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuFeaturePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.ProductGlobalAttributePO;
import com.jdi.isc.product.soa.domain.spu.po.SpuLangPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.stock.vo.SkuStockReqVO;
import com.jdi.isc.product.soa.domain.stock.vo.SkuStockVO;
import com.jdi.isc.product.soa.domain.stockThreshold.biz.SkuStockThresholdVO;
import com.jdi.isc.product.soa.domain.stockThreshold.po.SkuStockThresholdPO;
import com.jdi.isc.product.soa.domain.supplier.po.SupplierBaseInfoPO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CategoryTaxVO;
import com.jdi.isc.product.soa.domain.vendor.po.VendorPO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehouseSkuPO;
import com.jdi.isc.product.soa.price.api.jdPrice.res.IscEachSkuPriceResDTO;
import com.jdi.isc.product.soa.repository.mapper.sku.SkuBaseMapper;
import com.jdi.isc.product.soa.repository.mapper.spu.SpuBaseMapper;
import com.jdi.isc.product.soa.repository.mapper.spu.SpuLangBasicMapper;
import com.jdi.isc.product.soa.rpc.gms.MkuMappingRpcService;
import com.jdi.isc.product.soa.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.product.soa.rpc.iop.SkuStockRpcService;
import com.jdi.isc.product.soa.rpc.vcs.VendorRpcService;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuCertificateAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuFeatureAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.ProductGlobalAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.stockThreshold.SkuStockThresholdAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.SupplierAllInfoAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.SupplierSettlementAccountAtomicService;
import com.jdi.isc.product.soa.service.atomic.vendor.VendorAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseSkuAtomicService;
import com.jdi.isc.product.soa.service.manage.brand.BrandOutService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.category.GlobalAttributeManageService;
import com.jdi.isc.product.soa.service.manage.category.GlobalQualificationManageService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuTaxPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.domestic.DomesticSkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuExternalManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuFeatureManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.ExtendInfoService;
import com.jdi.isc.product.soa.service.manage.spu.SpuTaxManageService;
import com.jdi.isc.product.soa.service.manage.spu.validate.SkuValidateService;
import com.jdi.isc.product.soa.service.manage.stock.SkuStockManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.CategoryTaxOutService;
import com.jdi.isc.product.soa.service.manage.vendor.VendorManageService;
import com.jdi.isc.product.soa.service.mapstruct.ProductAttributeConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SkuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.sku.SkuConvert;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import com.jdi.isc.product.soa.service.support.ProductIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.Constant.*;


/**
 * SKU读服务
 *
 * <AUTHOR>
 * @date 2023/11/25
 **/
@Slf4j
@Service
public class SkuReadManageServiceImpl implements SkuReadManageService {

    @Resource
    private SkuBaseMapper skuBaseMapper;

    @Resource
    private SpuBaseMapper spuBaseMapper;

    @Resource
    private SpuLangBasicMapper spuLangBasicMapper;

    @Resource
    private SkuPriceAtomicService skuPriceAtomicService;

    @Resource
    private VendorAtomicService vendorAtomicService;

    @Resource
    private SkuInfoRpcService skuInfoRpcService;

    @Resource
    private SkuExternalManageService skuExternalManageService;

    @Resource
    private BrandOutService brandOutService;

    @Resource
    private CategoryOutService categoryOutService;

    @Resource
    private VendorManageService vendorManageService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    @Lazy
    private SkuConvertService skuConvertService;

    @Resource
    private ProductIdGenerator productIdGenerator;

    @Resource
    @Lazy
    private SkuValidateService skuValidateService;

    @Resource
    private VendorRpcService vendorRpcService;

    @Resource
    private MkuMappingRpcService mkuMappingRpcService;

    @Resource
    private DomesticSkuPriceManageService domesticSkuPriceManageService;

    @Resource
    private SkuPriceManageService skuPriceManageService;

    @Resource
    private SkuStockManageService skuStockManageService;

    @Resource
    private SkuStockRpcService skuStockRpcService;

    @Resource
    private SkuFeatureAtomicService skuFeatureAtomicService;

    @Resource
    private CategoryTaxOutService categoryTaxOutService;

    @Resource
    private SkuCertificateAtomicService skuCertificateAtomicService;

    @Resource
    private GlobalQualificationManageService globalQualificationManageService;

    @Resource
    @Lazy
    private GlobalAttributeManageService globalAttributeManageService;

    @Resource
    private CountryManageService countryManageService;

    @Resource
    @Lazy
    private Map<String, SkuTaxPriceManageService> skuTaxPriceManageServiceMap;

    @Resource
    @Lazy
    private Map<String, SpuTaxManageService> spuTaxManageServiceMap;

    @Resource
    private ProductAttributeConvertService productAttributeConvertService;

    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;

    @Resource
    private SkuStockThresholdAtomicService skuStockThresholdAtomicService;

    @Resource
    private WarehouseSkuAtomicService warehouseSkuAtomicService;

    @Resource
    private WarehouseAtomicService warehouseAtomicService;

    @Resource
    private SkuFeatureManageService skuFeatureManageService;

    @Resource
    private SpuLangAtomicService spuLangAtomicService;

    @Resource
    private ExtendInfoService extendInfoService;

    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @Resource
    private CountryMkuAtomicService countryMkuAtomicService;

    @Value("${sku.detail.purchaseTypeName}")
    private String purchaseTypeStr;

    @Value("${sku.detail.customerTradeTypeName}")
    private String customerTradeTypeName;

    @LafValue("jdi.isc.sku.global.attribute")
    private String globalAttribute;

    private static final String IN_STOCK = "有货";

    private static final String SOLD_OUT = "无货";

    private static final String CN_VENDOR_CODE = "bjjdbyxxjs";

    @LafValue("jdi.isc.stock.default.addr")
    private String defaultAddr;
    @LafValue("jdi.isc.stock.default.clientId")
    private String defaultClientId;
    @LafValue("jdi.isc.stock.default.pin")
    private String defaultPin;
    @LafValue("jdi.isc.spu.import.supportMku")
    private Boolean supportMku;
    @LafValue("jdi.isc.default.vat")
    private String defaultVatConfig;
    @LafValue("jdi.isc.stock.iop.addr")
    private String iopParamInfo;

    private static final String STANDARD_SKU_FLAG = "1";

    @Resource
    private SupplierSettlementAccountAtomicService supplierSettlementAccountAtomicService;
    @Resource
    private SupplierAllInfoAtomicService supplierAllInfoAtomicService;

    @Resource
    private AsyncTaskExecutor descriptionExecutor;

    @Override
    public Map<Long, SkuDetailOrderReadVO> getDetailBySkuId(Set<Long> skuIds, List<String> localList, boolean checkOnlineStatus) {
        LambdaQueryWrapper<SkuPO> wrapper = new LambdaQueryWrapper<SkuPO>().in(SkuPO::getSkuId, skuIds)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> skuPOList = skuBaseMapper.selectList(wrapper);
        skuPOList = checkOnlineStatus ? skuPOList.stream().filter(skuPO -> !(skuPO.getSkuStatus() != null && SpuStatusEnum.DOWN.getStatus() == skuPO.getSkuStatus())).collect(Collectors.toList()) : skuPOList;
        if (CollectionUtils.isEmpty(skuPOList)) {
            log.error("SkuReadManageServiceImpl.getDetailBySkuId, skuPOList is null, skuIds = {}", JSON.toJSONString(skuIds));
            return null;
        }
        List<Long> spuIds = skuPOList.stream().map(SkuPO::getSpuId).collect(Collectors.toList());

        LambdaQueryWrapper<SpuPO> spuWrapper =
                new LambdaQueryWrapper<SpuPO>().in(SpuPO::getSpuId, spuIds).eq(SpuPO::getYn, YnEnum.YES.getCode());
        List<SpuPO> spuPOList = spuBaseMapper.selectList(spuWrapper);
        Map<Long, SpuPO> spuPOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(spuPOList)) {
            spuPOMap = spuPOList.stream().collect(Collectors.toMap(SpuPO::getSpuId, Function.identity()));
        }
        LambdaQueryWrapper<SpuLangPO> langWrapper =
                new LambdaQueryWrapper<SpuLangPO>().in(SpuLangPO::getSpuId, spuIds).in(SpuLangPO::getLang, localList).eq(SpuLangPO::getYn, YnEnum.YES.getCode());
        List<SpuLangPO> spuLangPOList = spuLangBasicMapper.selectList(langWrapper);
        Map<Long, Map<String, String>> spuLangPOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(spuLangPOList)) {
            for (SpuLangPO spuLangPO : spuLangPOList) {
                Long spuId = spuLangPO.getSpuId();
                Map<String, String> spuMap = spuLangPOMap.getOrDefault(spuId, Maps.newHashMap());
                spuMap.put(spuLangPO.getLang(), spuLangPO.getSpuTitle());
                spuLangPOMap.put(spuId, spuMap);
            }
        }

        // 价格
        Map<Long, List<SkuPricePO>> skuPricePOMap = skuPriceAtomicService.batchQuerySkuPrice(skuIds);
        if (MapUtils.isEmpty(skuPricePOMap) || skuPricePOMap.size() != skuIds.size()) {
            log.error("SkuReadManageServiceImpl.getDetailBySkuId, skuPricePOMap size not equals skuIds size, skuPricePOMap = {}, skuIds = {}", JSON.toJSONString(skuPricePOMap), JSON.toJSONString(skuIds));
            return null;
        }

        // 是否厂直
        List<Long> jdSkuIds = skuPOList.stream().map(SkuPO::getJdSkuId).collect(Collectors.toList());
        Map<Long, JdProductDTO> jdProductDTOMap = skuInfoRpcService.querySkuMap(new JdProductQueryDTO(jdSkuIds));
        log.error("skuIds = {}, jdProductDTOMap = {}", JSON.toJSONString(skuIds), JSON.toJSONString(jdProductDTOMap));


        Map<Long, SkuDetailOrderReadVO> orderSkuDetailReadRespMap = Maps.newHashMap();
        for (SkuPO skuPO : skuPOList) {
            SkuDetailOrderReadVO skuDetailReadResp = SkuConvert.INSTANCE.skuPO2orderSkuDetail(skuPO);
            SpuPO spuPO = spuPOMap.get(skuPO.getSpuId());
            // 为sku添加聚合的spu和sku扩展属性
            String spuGroupExtAttribute = "";
            if(spuPO != null){
                skuDetailReadResp.setHsCode(spuPO.getHsCode());
                spuGroupExtAttribute = skuPO.getGroupExtAttribute();
            }
            Map<String, String> spuNameMap = spuLangPOMap.get(skuPO.getSpuId());
            if (spuNameMap != null) {
                skuDetailReadResp.setSkuNameMap(spuNameMap);
            }

            List<SkuPricePO> skuPricePOList = skuPricePOMap.get(skuPO.getSkuId());
            SkuDetailPriceOrderReadVO skuDetailPriceOrderReadVO = new SkuDetailPriceOrderReadVO();
            skuPricePOList.forEach(skuPricePO -> {
                if (TradeDirectionEnum.CUSTOMER.getStatus().equals(skuPricePO.getTradeDirection().getStatus())) {
                    skuDetailPriceOrderReadVO.setSalePrice(skuPricePO.getPrice());
                    skuDetailPriceOrderReadVO.setSalePriceCurrency(skuPricePO.getCurrency());
                } else if (TradeDirectionEnum.SUPPLIER.getStatus().equals(skuPricePO.getTradeDirection().getStatus())) {
                    skuDetailPriceOrderReadVO.setPurchaseOrderPrice(skuPricePO.getPrice());
                    skuDetailPriceOrderReadVO.setPurchaseOrderPriceCurrency(skuPricePO.getCurrency());
                }
            });
            skuDetailReadResp.setSkuDetailPriceOrderReadVO(skuDetailPriceOrderReadVO);
            if (StringUtils.isNotBlank(skuPO.getJdVendorCode())) {
                skuDetailReadResp.setJdVendorCode(skuPO.getJdVendorCode());
                VendorPO vendorPo = vendorAtomicService.getVendorPo(skuPO.getJdVendorCode(), skuPO.getSourceCountryCode());
                if (vendorPo != null) {
                    skuDetailReadResp.setJdVendorName(vendorPo.getVendorName());
                }
            }

            if (skuDetailReadResp.getJdSkuId() != null && jdProductDTOMap != null && jdProductDTOMap.get(skuDetailReadResp.getJdSkuId()) != null) {
                JdProductDTO jdProductDTO = jdProductDTOMap.get(skuDetailReadResp.getJdSkuId());
                skuDetailReadResp.setJdiProductFlag("1".equals(jdProductDTO.getGypzzcxqsp()) ? 1 : 0);
                skuDetailReadResp.setCommonProductFlag(skuDetailReadResp.getJdiProductFlag() == 1 ? 0 : 1);
                skuDetailReadResp.setFactoryProductFlag("1".equals(jdProductDTO.getFactoryShip()) ? 1 : 0);
                skuDetailReadResp.setJdProductFlag(skuDetailReadResp.getFactoryProductFlag() == 1 ? 0 : 1);
            }

            orderSkuDetailReadRespMap.put(skuPO.getSkuId(), skuDetailReadResp);
        }

        return orderSkuDetailReadRespMap;
    }

    @Override
    public Map<Long, SkuPO> getSkuBaseInfoBySku(Set<Long> skuId) {
        LambdaQueryWrapper<SkuPO> wrapper =
                new LambdaQueryWrapper<SkuPO>().in(SkuPO::getSkuId, skuId).eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> res = skuBaseMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(res)) {
            return res.stream().collect(Collectors.toMap(SkuPO::getSkuId, Function.identity()));
        }
        return null;
    }

    @Override
    public Map<Long, SkuPO> getSkuBaseInfoByVendorSkuIds(Set<Long> vendorSkuIds) {
        LambdaQueryWrapper<SkuPO> wrapper = new LambdaQueryWrapper<SkuPO>()
                .in(SkuPO::getVendorSkuId, vendorSkuIds)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> res = skuBaseMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(res)) {
            return res.stream().collect(Collectors.toMap(SkuPO::getVendorSkuId, Function.identity()));
        }
        return null;
    }

    @Override
    public List<SkuGlobalAttributeDetailVO> selectGlobalAttribute(SkuGlobalAttributeVO param) {

        // 合法性校验
        this.checkSelectGlobalAttribute(param);

        List<SkuGlobalAttributeDetailVO> params = param.getDetailData();
        List<Long> skuIds = params.stream().map(SkuGlobalAttributeDetailVO::getSkuId).collect(Collectors.toList());
        List<SkuPO> skuPOS = skuAtomicService.queryBySkuIds(new HashSet<>(skuIds));
        if (CollectionUtils.isEmpty(skuPOS)) {
            return new ArrayList<>();
        }

        List<ProductGlobalAttributePO> productGlobalAttributePOS = productGlobalAttributeAtomicService.getBySkuIds(skuIds);
        Map<Long, List<ProductGlobalAttributePO>> skuGlobalAttributeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productGlobalAttributePOS)) {
            skuGlobalAttributeMap.putAll(productGlobalAttributePOS.stream().collect(Collectors.groupingBy(ProductGlobalAttributePO::getKeyId)));
        }

        Map<String,List<SkuPO>> groupSkus = skuPOS.stream().collect(Collectors.groupingBy(item->StringUtils.joinWith("_",item.getJdCatId(),item.getSourceCountryCode())));
        Map<Long,String> attributeRelationMap = getSelectAttribute(param.getTargetCountryCode());
        Set<Long> attributeIds = new HashSet<>(attributeRelationMap.keySet());
        Set<String> targetCountryCodes = this.getAllSelectCountryCode();

        List<SkuGlobalAttributeDetailVO> results = new ArrayList<>();
        groupSkus.forEach((k, v) -> {
            SkuPO skuPOSample = v.get(0);
            List<GroupPropertyVO> skuInterGroupPropertyVOS = globalAttributeManageService.queryGroupPropertyVos(skuPOSample.getJdCatId(),skuPOSample.getSourceCountryCode(),targetCountryCodes, AttributeDimensionEnum.SKU.getCode(), LangConstant.LANG_ZH, null);
            for(SkuPO skuPO : v){
                List<ProductGlobalAttributePO> skuGlobalAttributePOS = skuGlobalAttributeMap.get(skuPO.getSkuId());
                if (CollectionUtils.isEmpty(skuGlobalAttributePOS)) {
                    continue;
                }
                List<GroupPropertyVO> groupPropertyVOS = productAttributeConvertService.convertAttributeFromDB(skuGlobalAttributePOS, skuInterGroupPropertyVOS);
                if (CollectionUtils.isEmpty(groupPropertyVOS)) {
                    continue;
                }

                SkuGlobalAttributeDetailVO detailVO = new SkuGlobalAttributeDetailVO();
                detailVO.setSkuId(skuPO.getSkuId());

                Map<String, Object> resultMap = new HashMap<>();
                for (GroupPropertyVO groupPropertyVO : groupPropertyVOS) {
                    if (CollectionUtils.isEmpty(groupPropertyVO.getPropertyVOS())) {
                        continue;
                    }
                    this.processPropertiesVn(param.getTargetCountryCode(), groupPropertyVO.getPropertyVOS(), resultMap, attributeIds, attributeRelationMap);
                }
                detailVO.setUpdateData(resultMap);
                results.add(detailVO);
            }
        });

        return results;
    }

    @Override
    public List<SkuCountryVO> getSourceCountryBySkuId(Set<Long> skuId) {
        LambdaQueryWrapper<SkuPO> wrapper =
                new LambdaQueryWrapper<SkuPO>()
                        .select(SkuPO::getSkuId,SkuPO::getSourceCountryCode)
                        .in(SkuPO::getSkuId, skuId).eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> res = skuBaseMapper.selectList(wrapper);
        return SkuConvert.INSTANCE.listPo2SkuCountry(res);
    }


    @Override
    public List<SkuVO> selectSkusBySpuId(SpuVO spuVO, String lang) {
        Long spuId = null;
        try {
            spuId = spuVO.getSpuId();
            SpuPO spuPo = spuAtomicService.getSpuPoBySpuId(spuId);
            // 发货国
            final String sourceCountryCode = spuPo.getSourceCountryCode();
            // 查询skuPo列表
            List<SkuPO> skuPoList = skuAtomicService.selectSkuPosBySpuId(spuId);
            // sku po列表 -> vo列表
            List<SkuVO> skuVoList = SkuConvert.INSTANCE.listPo2Vo(skuPoList);
            List<Long> skuIdList = skuVoList.stream().filter(Objects::nonNull).map(SkuVO::getSkuId).collect(Collectors.toList());
            // 拆分sku列表
            List<List<Long>> partitionList = Lists.partition(skuIdList, PARTITION_SIZE);
            // 拆分京东sku列表
            List<Long> jdSkuIdList = skuVoList.stream().filter(Objects::nonNull).map(SkuVO::getJdSkuId).collect(Collectors.toList());
            List<List<Long>> partitionJdSkuIdList = Lists.partition(jdSkuIdList, PARTITION_SIZE);
            // 分批查询价格
            Map<Long, SkuPriceResVO> skuPriceMap = Maps.newHashMap();
            CompletableFuture<Void> priceFuture = CompletableFuture.runAsync(() -> this.skuPriceMap(sourceCountryCode, partitionList, skuPriceMap, spuPo),descriptionExecutor);
            // 分批查询库存
            // 批量查询sku-》仓库-〉库存
            Map<Long, Map<String, SkuStockVO>>  skuWarehouseStockMap = Maps.newHashMap();
            // 国际skuId和国内skuId关系
            Map<Long, Long> skuAndJdSkuMap = CountryConstant.COUNTRY_ZH.equals(spuPo.getSourceCountryCode()) ?  skuVoList.stream().collect(Collectors.toMap(SkuVO::getSkuId, SkuVO::getJdSkuId)) : Maps.newHashMap();
            CompletableFuture<Void> stockFuture = CompletableFuture.runAsync(() -> this.skuStockMap(sourceCountryCode, partitionList, partitionJdSkuIdList, skuWarehouseStockMap,skuAndJdSkuMap),descriptionExecutor);
            //按照skuId 获取仓信息 - 在途可售 状态
            Map<Long, Map<Long,WarehouseSkuPO>> skuOnWaySaleMap = Maps.newHashMap();
            CompletableFuture<Void> skuOnWaySaleFuture = CompletableFuture.runAsync(() -> this.skuOnWaySaleMap(skuIdList, skuOnWaySaleMap),descriptionExecutor);
            // 三级类目查询销售属性
//            List<PropertyVO> salePropertyList = Lists.newArrayList();
//            CompletableFuture<Void> salePropertyFuture = CompletableFuture.runAsync(() -> skuConvertService.salePropertyVoList(spuPo.getJdCatId(), lang, salePropertyList),descriptionExecutor);
            // SKU属性查询
            Map<Long, SkuFeaturePO> skuFeaturePoMap = Maps.newHashMap();
            CompletableFuture<Void> skuFeatureFuture = CompletableFuture.runAsync(() -> this.skuFeatureMap(skuIdList, skuFeaturePoMap),descriptionExecutor);
            // 补充跨境属性
            List<GroupPropertyVO> groupPropertyVOS = new ArrayList<>();
            CompletableFuture<Void> groupPropertyVO = CompletableFuture.runAsync(() -> this.getGroupPropertyVOS(spuPo.getJdCatId(),spuVO.getAttributeScope(),lang, sourceCountryCode, groupPropertyVOS, spuPo.getIsExport()),descriptionExecutor);
            Map<Long,List<ProductGlobalAttributePO>> skuGlobalAttributeMap = new HashMap<>();
            CompletableFuture<Void> skuGlobalAttributes = CompletableFuture.runAsync(() -> this.getSkuGlobalAttribute(skuIdList,skuGlobalAttributeMap),descriptionExecutor);
            // 补充资质
            Map<Long, List<SkuCertificatePO>> groupSkuCertificateVOHashMap = Maps.newHashMap();
            List<GlobalQualificationVO> globalQualificationVOS = new ArrayList<>();
            CompletableFuture<Void> groupSkuCertificateVO = CompletableFuture.runAsync(() -> this.groupSkuCertMap(skuIdList, groupSkuCertificateVOHashMap),descriptionExecutor);
            CompletableFuture<Void> skuGlobalCertificateVOS = CompletableFuture.runAsync(() -> this.getGlobalQualificateVO(spuPo.getJdCatId(),spuPo.getSourceCountryCode(),spuVO.getAttributeScope(), globalQualificationVOS, lang, spuPo.getIsExport()),descriptionExecutor);
            // 等待所有任务执行完成
            CompletableFuture.allOf(priceFuture, stockFuture, skuOnWaySaleFuture, skuFeatureFuture,groupSkuCertificateVO,skuGlobalCertificateVOS,groupPropertyVO,skuGlobalAttributes).join();
            // 补充sku信息
            skuVoList.forEach(skuVo -> fillSkuVo(sourceCountryCode, skuVo, skuPriceMap, skuWarehouseStockMap,skuOnWaySaleMap, skuFeaturePoMap, groupPropertyVOS, groupSkuCertificateVOHashMap, globalQualificationVOS,skuGlobalAttributeMap));
            // 补充越南含税采购价等信息
            this.handleLocalSkuPrice(skuVoList,spuPo.getSourceCountryCode(),spuPo.getVendorCode());
            log.info("SkuReadManageServiceImpl.selectSkusBySpuId  param [spuId={},lang={}] result=[skuVoList={}]", spuId, lang, JSON.toJSONString(skuVoList));
            return skuVoList;
        } catch (Exception e) {
            log.error("【系统异常】SkuReadManageServiceImpl.selectSkusBySpuId error spuId={},lang={}", spuId, lang, e);
        }

        return Collections.emptyList();
    }

    private void skuOnWaySaleMap(List<Long> skuIdList,Map<Long, Map<Long,WarehouseSkuPO>> skuOnWaySaleMap) {
        try {
            Map<Long, Map<Long, WarehouseSkuPO>> saleMap = warehouseSkuAtomicService.queryBindSkuWarehouseMap(Sets.newHashSet(skuIdList));
            skuOnWaySaleMap.putAll(saleMap);
        }catch (Exception e){
            log.error("查询 warehouseSkuAtomicService.skuOnWaySaleMap database 异常 入参 skuIdList:{}",JSONObject.toJSONString(skuIdList) );

        }
    }

    @Override
    public void selectSkusInter(SpuVO spuVO, List<SkuVO> skuVOS, String lang) {
        if(CollectionUtils.isEmpty(skuVOS)){
            log.error("SkuReadManageServiceImpl.selectSkusInter skuVOS is null");
            return;
        }
        try {
            // 发货国
            final String sourceCountryCode = spuVO.getSourceCountryCode();
            List<Long> skuIdList = skuVOS.stream().filter(Objects::nonNull).map(SkuVO::getSkuId).collect(Collectors.toList());
            // 补充跨国属性
            List<GroupPropertyVO> groupPropertyVOS = new ArrayList<>();
            CompletableFuture<Void> groupPropertyVO = CompletableFuture.runAsync(() -> this.getGroupPropertyVOS(spuVO.getCatId(),spuVO.getAttributeScope(),lang, sourceCountryCode, groupPropertyVOS, spuVO.getIsExport()));
            // 补充资质
            Map<Long, List<SkuCertificatePO>> groupSkuCertificateVOHashMap = Maps.newHashMap();
            List<GlobalQualificationVO> globalQualificationVOS = new ArrayList<>();
            CompletableFuture<Void> groupSkuCertificateVO = CompletableFuture.runAsync(() -> this.groupSkuCertMap(skuIdList, groupSkuCertificateVOHashMap));
            CompletableFuture<Void> skuGlobalCertificateVOS = CompletableFuture.runAsync(() -> this.getGlobalQualificateVO(spuVO.getCatId(),sourceCountryCode,spuVO.getAttributeScope(), globalQualificationVOS, lang, spuVO.getIsExport()));
            // 等待所有任务执行完成
            CompletableFuture.allOf(groupPropertyVO, groupSkuCertificateVO,skuGlobalCertificateVOS).join();
            // 补充sku信息
            skuVOS.forEach(skuVo -> fillSkuInterVo(skuVo, groupPropertyVOS, groupSkuCertificateVOHashMap, globalQualificationVOS));

            log.info("SkuReadManageServiceImpl.selectSkusInter  param [spuId={},lang={}] result=[skuVoList={}]", spuVO.getSpuId(), lang, JSON.toJSONString(skuVOS));
        } catch (Exception e) {
            log.error("【系统异常】SkuReadManageServiceImpl.selectSkusInter error spuId={},lang={}", spuVO.getSpuId(), lang, e);
        }
    }

    @Override
    public void selectSkusCurrency(SpuVO spuVO, List<SkuVO> skuVOS, String lang) {
        // 跨境默认为CNY；本土为越南币
        for (SkuVO skuVO : skuVOS) {
            String currency = supplierSettlementAccountAtomicService.getCurrencyBySupplierCode(spuVO.getVendorCode());
            // countryManageService.getCurrencyByCountryCode(
            skuVO.setCurrency(currency);
        }
    }

    private void skuPriceMap(String sourceCountryCode, List<List<Long>> partitionList, Map<Long, SkuPriceResVO> skuPriceResVoMap,SpuPO spuPo) {
        SkuPriceReqVO skuPriceReqVO = new SkuPriceReqVO();
        // 跨境默认为CNY；本土为越南币
        // TODO 这个要把 sku传进来，然后从sku上获取供应商，然后获取币种  by wang ********
        String currency = supplierSettlementAccountAtomicService.getCurrencyBySupplierCode(spuPo.getVendorCode());
        // countryManageService.getCurrencyByCountryCode(
        for (List<Long> subList : partitionList) {
            List<SkuPriceVO> skuPriceVOList = Lists.newArrayList();
            for (Long skuId : subList) {
                SkuPriceVO priceVO = new SkuPriceVO();
                priceVO.setSkuId(skuId);
                priceVO.setSourceCountryCode(sourceCountryCode);
                priceVO.setCurrency(currency);
                skuPriceVOList.add(priceVO);
            }
            skuPriceReqVO.setSkuPriceVO(skuPriceVOList);
            // 查询价格
            skuPriceResVoMap.putAll(skuPriceManageService.list(skuPriceReqVO));
        }
    }

    /**
     * 补全价格、库存、销售属性信息
     *
     * @param sourceCountryCode 发货国 中国：跨境  越南：本地
     * @param skuVO             skuVO
     * @param skuPriceMap       sku价格映射
     * @param skuStockMap       sku库存映射
     *
     */
    private void fillSkuVo(String sourceCountryCode, SkuVO skuVO, Map<Long, SkuPriceResVO> skuPriceMap
            , Map<Long, Map<String, SkuStockVO>> skuStockMap, Map<Long, Map<Long,WarehouseSkuPO>> skuOnWaySaleMap, Map<Long, SkuFeaturePO> skuFeaturePOMap
            , List<GroupPropertyVO> groupPropertyVOS, Map<Long, List<SkuCertificatePO>> skuCertificateVOMap
            , List<GlobalQualificationVO> globalQualificationVOS,Map<Long,List<ProductGlobalAttributePO>> skuGlobalAttributeMap) {
        final Long skuId = skuVO.getSkuId();
        SkuPriceResVO skuPriceResVO = skuPriceMap.get(skuId);
        if (Objects.nonNull(skuPriceResVO)) {
            skuVO.setPurchasePrice(Objects.nonNull(skuPriceResVO.getPurchasePrice()) ? skuPriceResVO.getPurchasePrice().toPlainString() : "");
            skuVO.setSalePrice(Objects.nonNull(skuPriceResVO.getSalePrice()) ? skuPriceResVO.getSalePrice().toPlainString() : "");
            skuVO.setTaxPurchasePrice(Objects.nonNull(skuPriceResVO.getTaxPurchasePrice()) ? skuPriceResVO.getTaxPurchasePrice().toPlainString() : null);
            skuVO.setTaxSalePrice(Objects.nonNull(skuPriceResVO.getTaxSalePrice()) ? skuPriceResVO.getTaxSalePrice().toPlainString() : null);
            skuVO.setCurrency(skuPriceResVO.getCurrency());
            skuVO.setVendorTradeType(skuPriceResVO.getSupplierTradeType());
            skuVO.setCustomerTradeType(skuPriceResVO.getCustomerTradeType());
        }

        log.info("fillSkuVo skuStockMap={}",JSON.toJSONString(skuStockMap));
        // 库存 跨境展示文字“有货”和“无货”  本土展示库存数量
        Map<String, SkuStockVO> warehouseStockMap = skuStockMap.get(skuId);

        // 查询sku的安全库存阈值
        Map<String, SkuStockThresholdPO> thresholdPOMap = this.getStockThresholdMap(warehouseStockMap, skuId);
        if (MapUtils.isNotEmpty(warehouseStockMap)){
            Map<Long, WarehouseSkuPO> warehouseSkuPOMap = skuOnWaySaleMap.get(skuVO.getSkuId());
            log.info("fillSkuVo 补充库存信息  skuId={},warehouseSkuPOMap ={}",skuId, JSON.toJSONString(warehouseSkuPOMap));
            if (skuConvertService.crossborder(sourceCountryCode)) {
                skuVO.setStockNum(MapUtils.isNotEmpty(warehouseStockMap) ? warehouseStockMap.get(FACTORY_DEFAULT_ID_STR).getStockStr() : SOLD_OUT);
                List<SkuStockRelationVO> skuStockRelationVOList = Lists.newArrayList();
                for (Map.Entry<String, SkuStockVO> entry : warehouseStockMap.entrySet()) {
                    String warehouseId = entry.getKey();
                    SkuStockVO stock = entry.getValue();
                    log.info("fillSkuVo 跨境品 仓库id：{} SkuStockVO={}", entry.getKey(),JSON.toJSONString(entry));
                    skuStockRelationVOList.add(new SkuStockRelationVO( skuVO.getSpuId(), this.getStockThreshold(thresholdPOMap, skuId, warehouseId),stock, MapUtils.isNotEmpty(warehouseSkuPOMap) ? warehouseSkuPOMap.get(Long.valueOf(warehouseId)) : null)) ;
                }
                skuVO.setSkuStockRelationList(skuStockRelationVOList);
            } else {
                skuVO.setStockNum(MapUtils.isNotEmpty(warehouseStockMap) ? warehouseStockMap.get(FACTORY_DEFAULT_ID_STR).getStockStr() : "");
                List<SkuStockRelationVO> skuStockRelationVOList = Lists.newArrayList();
                for (Map.Entry<String, SkuStockVO> entry : warehouseStockMap.entrySet()) {
                    String warehouseId = entry.getKey();
                    SkuStockVO stock = entry.getValue();
                    log.info("fillSkuVo 本土品 仓库id：{} SkuStockVO={}", warehouseId,JSON.toJSONString(stock));
                    skuStockRelationVOList.add(new SkuStockRelationVO( skuVO.getSpuId(), this.getStockThreshold(thresholdPOMap, skuId, warehouseId),stock, MapUtils.isNotEmpty(warehouseSkuPOMap) ? warehouseSkuPOMap.get(Long.valueOf(warehouseId)) : null)) ;
                }
                skuVO.setSkuStockRelationList(skuStockRelationVOList);
            }
        }
        // 已经存储的销售属性和属性值映射关系
//        skuConvertService.convertSaleAttributeValueForView(skuVO, salePropertyList);
        // SKU属性
        if (MapUtils.isNotEmpty(skuFeaturePOMap) && Objects.nonNull(skuFeaturePOMap.get(skuId))) {
            SkuFeaturePO skuFeature = skuFeaturePOMap.get(skuId);
            SkuConvert.INSTANCE.fillSkuFeature(skuVO, skuFeature);
        }
        // 跨境属性
        if (CollectionUtils.isNotEmpty(groupPropertyVOS)) {
            List<GroupPropertyVO> skuGlobalAttribute = productAttributeConvertService.convertAttributeFromDB(skuGlobalAttributeMap.get(skuId),groupPropertyVOS);
            skuVO.setSkuInterPropertyList(skuGlobalAttribute);
        }
        // 资质
        if (CollectionUtils.isNotEmpty(globalQualificationVOS)) {
            if(MapUtils.isEmpty(skuCertificateVOMap)){
                skuCertificateVOMap = new HashMap<>();
            }
            skuConvertService.convertGroupSkuInterCertificateVO(skuVO,skuCertificateVOMap.get(skuId),globalQualificationVOS);
        }
    }

    /**
     * 根据仓库库存信息和 SKU ID 获取 SKU 的库存阈值映射。
     * @param warehouseStockMap 仓库库存信息，key 为仓库 ID，value 为库存数量。
     * @param skuId SKU ID。
     * @return SKU 的库存阈值映射，key 为仓库 ID，value 为 SkuStockThresholdPO 对象。
     */
    private Map<String, SkuStockThresholdPO> getStockThresholdMap(Map<String, SkuStockVO> warehouseStockMap, Long skuId) {
        if (MapUtils.isEmpty(warehouseStockMap)) {
            return Collections.emptyMap();
        }
        List<SkuStockThresholdVO> skuStockThresholdVOList = Lists.newArrayList();
        warehouseStockMap.forEach((warehouseId, stock) -> {
            if (FACTORY_DEFAULT_ID_STR.equals(warehouseId)) {
                skuStockThresholdVOList.add(new SkuStockThresholdVO(null, skuId));
            }else {
                skuStockThresholdVOList.add(new SkuStockThresholdVO(Long.valueOf(warehouseId), skuId));
            }
        });
        return skuStockThresholdAtomicService.batchQuerySkuStockThresholdMap(skuStockThresholdVOList);
    }

    /**
     * 补全跨境
     *
     * @param skuVO skuVO
     */
    private void fillSkuInterVo(SkuVO skuVO, List<GroupPropertyVO> groupPropertyVOS, Map<Long, List<SkuCertificatePO>> skuCertificateVOMap
            , List<GlobalQualificationVO> globalQualificationVOS) {
        final Long skuId = skuVO.getSkuId();
        // 跨国属性
        if (CollectionUtils.isNotEmpty(groupPropertyVOS) ) {
            List<GroupPropertyVO> skuAttributeFromDraft = productAttributeConvertService.convertAttributeFromDraft(skuVO.getSkuInterPropertyList(),groupPropertyVOS);
            skuVO.setSkuInterPropertyList(skuAttributeFromDraft);
        }
        // 资质
        if (CollectionUtils.isNotEmpty(globalQualificationVOS)) {
            if(MapUtils.isEmpty(skuCertificateVOMap)){
                skuCertificateVOMap = new HashMap<>();
            }
            skuConvertService.convertGroupSkuInterCertificateVO(skuVO,skuCertificateVOMap.get(skuId),globalQualificationVOS);
        }
    }

    private void skuStockMap(String sourceCountryCode, List<List<Long>> partitionList, List<List<Long>> partitionJdSkuIdList, Map<Long, Map<String, SkuStockVO>>  skuStockMap, Map<Long, Long> skuAndJdSkuMap) {
        // 查询跨境备货品库存
        Map<Long, Map<String,SkuStockVO>> skuWarehouseStockMap = Maps.newHashMap();
        SkuStockReqVO skuStockReqVO = new SkuStockReqVO();
        for (List<Long> skuIds : partitionList) {
            skuStockReqVO.setSkuIds(Sets.newHashSet(skuIds));
            skuWarehouseStockMap.putAll(skuStockManageService.listSkuWarehouseStock(skuStockReqVO));
        }

        // 跨境库存查询
        if (StringUtils.equals(CountryConstant.COUNTRY_ZH, sourceCountryCode)) {
            Map<Long, String> jdSkuStockMap = Maps.newHashMap();
            for (List<Long> jdSkuIds : partitionJdSkuIdList) {
                jdSkuStockMap.putAll(this.getJdSkuStockBySkuIds(jdSkuIds));
            }

            skuAndJdSkuMap.forEach((skuId, jdSkuId) -> {
                //Map<String, String> warehouseStockMap = Maps.newHashMap();
                Map<String, SkuStockVO> warehouseStockMap = Maps.newHashMap();
                SkuStockVO skuStockVO = new SkuStockVO();
                skuStockVO.setSkuId(skuId);
                skuStockVO.setStockStr(jdSkuStockMap.get(jdSkuId));
                skuStockVO.setOnWaySale(OnWaySaleTypeEnum.NOT_SUPPORTED.getCode());
                skuStockVO.setOccupy(Constant.ZERO);
                skuStockVO.setWarehouseId(FACTORY_DEFAULT_ID_STR);
                //warehouseStockMap.put(String.valueOf(FACTORY_DEFAULT_ID), jdSkuStockMap.get(jdSkuId));
                warehouseStockMap.put(FACTORY_DEFAULT_ID_STR, skuStockVO);
                Map<String, SkuStockVO> skuWarehouseStockRelationMap = skuWarehouseStockMap.get(skuId);
                if (MapUtils.isNotEmpty(skuWarehouseStockRelationMap)) {
/*                    skuWarehouseStockRelationMap.forEach((warehouseId, skuStockVo) -> {
                        warehouseStockMap.put(warehouseId, String.valueOf(skuStockVo.getStock()));
                    });*/
                    warehouseStockMap.putAll(skuWarehouseStockRelationMap);
                }
                skuStockMap.put(skuId, warehouseStockMap);
            });
        } else {
            // 本土sku查库存
            if (MapUtils.isNotEmpty(skuWarehouseStockMap)) {
                skuWarehouseStockMap.forEach((skuId, map) -> {
                    Map<String, SkuStockVO> warehouseStockMap = Maps.newHashMap();
                    //Map<String, String> stockMap = Maps.newHashMap();
/*                    if (MapUtils.isNotEmpty(map)) {
                        map.forEach((warehouseId, skuStockVo) -> {
                            stockMap.put(warehouseId, Objects.nonNull(skuStockVo) ? String.valueOf(skuStockVo.getStock()) : "");
                        });
                    } else {
                        stockMap.put(FACTORY_DEFAULT_ID_STR, "");
                    }*/
                    if (MapUtils.isEmpty(map)) {
                        SkuStockVO skuStockVO = new SkuStockVO();
                        skuStockVO.setSkuId(skuId);
                        skuStockVO.setStockStr("");
                        skuStockVO.setWarehouseId(FACTORY_DEFAULT_ID_STR);
                        skuStockVO.setStock(ZERO);
                        skuStockVO.setOccupy(ZERO);
                        skuStockVO.setOnWaySale(OnWaySaleTypeEnum.NOT_SUPPORTED.getCode());
                        map.put(FACTORY_DEFAULT_ID_STR,skuStockVO);
                    }
                    skuStockMap.put(skuId, map);
                });
            }
        }
    }


    @Override
    public Map<Long, String> getJdSkuStockBySkuIds(List<Long> jdSkuIds) {
        if (CollectionUtils.isEmpty(jdSkuIds)) {
            return Collections.emptyMap();
        }
        Map<Long,Integer> jdSkuIdNumMap = Maps.newHashMap();
        jdSkuIds.forEach(id -> jdSkuIdNumMap.put(id,1));
        return this.getJdSkuStockBySkuIdsAndNum(jdSkuIdNumMap);
    }

    @Override
    public Map<Long, String> getJdSkuStockBySkuIdsAndNum(Map<Long, Integer> jdSkuIdNumMap) {

        List<GetStockByIdGoodsResp> goodsResps = this.getGetStockByIdGoodsResps(jdSkuIdNumMap);
        if (CollectionUtils.isEmpty(goodsResps)) {
            return Collections.emptyMap();
        }

        /**
         * 剩余数量。当此值为-1时，为未查询到。
         * StockStateDesc为33：入参的skuNums字段，skuld对应的num小于50，此字段为实际库存。入参的skuNums字段，skuld对应的num大于等50于并小于100，此字段为-1。入参的skuNums字段，skuld对应的num大于100，此字段等于num。（此种情况并未返回真实京东库存）
         *
         *  库存状态类型，参考枚举值：
         *  33,39,40,36,34,99
         *  stockStateDesc
         *  库存状态描述。以下为stockStateld不同时，此字段不同的返回值：33 有货 现货-下单立即发货   39有货 在途-正在内部配货，预计21111111111\×706天到达本仓库   40有货可配货-下单后从有货仓库配货 36预订34无货99无货开预定
         *
         *  {"remainNum":-1,"skuId":100112519555,"stockStateDesc":"无货","stockStateType":34}
         *  {"remainNum":10,"skuId":100111702595,"stockStateDesc":"有货","stockStateType":33}
         *  {"remainNum":-1,"skuId":100107100241,"stockStateDesc":"有货","stockStateType":33}
         */
        Map<Long, String> stockMap = Maps.newHashMap();
        goodsResps.forEach(goodsResp -> stockMap.put(goodsResp.getSkuId(), goodsResp.getStockStateDesc()));
        return stockMap;
    }

    private List<GetStockByIdGoodsResp> getGetStockByIdGoodsResps(Map<Long, Integer> jdSkuIdNumMap) {
        if (MapUtils.isEmpty(jdSkuIdNumMap)) {
            return Collections.emptyList();
        }

        AreaBaseInfoGoodsReq areaInfo = new AreaBaseInfoGoodsReq();
        String[] addrArr = defaultAddr.split(Constant.COMMA);
        areaInfo.setProvinceId(Long.valueOf(addrArr[0]));
        areaInfo.setCityId(Long.valueOf(addrArr[1]));
        areaInfo.setCountyId(Long.valueOf(addrArr[2]));
        areaInfo.setTownId(Long.valueOf(addrArr[3]));
        return getGetStockByArea(jdSkuIdNumMap, areaInfo);
    }

    private List<GetStockByIdGoodsResp> getGetStockByArea(Map<Long, Integer> jdSkuIdNumMap, AreaBaseInfoGoodsReq areaInfo) {
        GetStockByIdGoodsReq req = new GetStockByIdGoodsReq();
        req.setAreaInfo(areaInfo);
        req.setPin(defaultPin);
        req.setClientId(defaultClientId);

        List<SkuNumBaseGoodsReq> skuNumInfoList = new ArrayList<>();
        jdSkuIdNumMap.forEach((id, num) -> {
            SkuNumBaseGoodsReq goodsReq = new SkuNumBaseGoodsReq();
            goodsReq.setSkuId(id);
            goodsReq.setSkuNumber(num);
            skuNumInfoList.add(goodsReq);
        });
        req.setSkuNumInfoList(skuNumInfoList);

        List<GetStockByIdGoodsResp> goodsResps = skuStockRpcService.getCnStockBySkuId(req);
        log.info("SkuReadManageServiceImpl.getJdSkuStockBySkuIds req={},jdSkuStockMap={}", JSON.toJSONString(req), JSON.toJSONString(goodsResps));
        return goodsResps;
    }

    private String getStockDesc(GetStockByIdGoodsResp goodsResp) {
        if (Objects.isNull(goodsResp)) {
            return Constant.SOLD_OUT;
        }

        if (goodsResp.getStockStateType() == 33) {
            return String.valueOf(goodsResp.getRemainNum());
        }else {
            return String.valueOf(ZERO);
        }
    }


    private void skuFeatureMap(List<Long> skuIds, Map<Long, SkuFeaturePO> skuFeaturePOMap) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        LambdaQueryWrapper<SkuFeaturePO> queryWrapper = Wrappers.lambdaQuery(SkuFeaturePO.class).in(SkuFeaturePO::getSkuId, skuIds).eq(SkuFeaturePO::getYn, YnEnum.YES.getCode());
        List<SkuFeaturePO> skuFeaturePOList = skuFeatureAtomicService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(skuFeaturePOList)) {
            skuFeaturePOList.stream().filter(Objects::nonNull).forEach(skuFeaturePO -> skuFeaturePOMap.put(skuFeaturePO.getSkuId(), skuFeaturePO));
        }
    }

    private void getGroupPropertyVOS(Long catId,String attributeScope,String lang,String sourceCountryCode, List<GroupPropertyVO> groupPropertyVOS,Integer isExport) {
        if (catId== null || StringUtils.isBlank(attributeScope)) {
            return;
        }

        List<String> targetCountryCodes = StringUtils.isNotBlank(attributeScope) ? Arrays.asList(attributeScope.split(COMMA)) : Lists.newArrayList();

        List<GroupPropertyVO> groupPropertyVOS1 = globalAttributeManageService.queryGroupPropertyVos(catId,sourceCountryCode,targetCountryCodes,AttributeDimensionEnum.SKU.getCode(), lang, isExport);
        if(CollectionUtils.isNotEmpty(groupPropertyVOS1)){
            groupPropertyVOS.addAll(groupPropertyVOS1);
        }
    }

    private void getSkuGlobalAttribute(List<Long> skuIds,Map<Long,List<ProductGlobalAttributePO>> skuGlobalAttributeMap){
        if(CollectionUtils.isEmpty(skuIds)){
            return;
        }
        List<ProductGlobalAttributePO> skuGlobalAttributePOS1 = productGlobalAttributeAtomicService.getBySkuIds(skuIds);
        if(CollectionUtils.isNotEmpty(skuGlobalAttributePOS1)){
            skuGlobalAttributeMap.putAll(skuGlobalAttributePOS1.stream().collect(Collectors.groupingBy(ProductGlobalAttributePO::getKeyId)));
        }

    }

    private void groupSkuCertMap(List<Long> skuIds, Map<Long, List<SkuCertificatePO>> groupSkuCerMap) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }
        Map<Long, List<SkuCertificatePO>> certMap = skuCertificateAtomicService.querySkuCertMap(skuIds);
        if(MapUtils.isNotEmpty(certMap)){
            groupSkuCerMap.putAll(certMap);
        }

    }

    private void getGlobalQualificateVO(Long catId,String sourceCountryCode,String attributeScope,List<GlobalQualificationVO> params,String lang,Integer isExport){
        List<String> targetCountryCodes = StringUtils.isNotBlank(attributeScope) ? Arrays.asList(attributeScope.split(COMMA)) : Lists.newArrayList();
        // 查询所有资质
        List<GlobalQualificationVO> globalQualificationVOS = globalQualificationManageService.queryByCountry(catId,sourceCountryCode
                ,targetCountryCodes, AttributeDimensionEnum.SKU.getCode(),lang,isExport);
        if (CollectionUtils.isNotEmpty(globalQualificationVOS)) {
            params.addAll(globalQualificationVOS);
        }
    }

    @Override
    @ToolKit
    public Map<Long, SkuDetailVO> batchQueryDetailBySkuIds(Set<Long> skuIds, Set<SkuScopeEnum> skuScopeEnumSet, String lang) {
        try {
            SkuExternalReqVO skuExternalReqVO = new SkuExternalReqVO(Lists.newArrayList(skuIds), skuScopeEnumSet);
            Map<Long, ExternalVO> externalVoMap = skuExternalManageService.querySkuInfo(skuExternalReqVO);
            if (externalVoMap == null) {
                return null;
            }
            List<ExternalVO> externalVOList = new ArrayList<>();
            List<SkuPriceVO> skuPriceVOList = new ArrayList<>();
            Set<Long> catIds = new HashSet<>();
            Set<Long> brandIds = Sets.newHashSet();
            Set<String> vendorSet = Sets.newHashSet();

            externalVoMap.forEach((key, value) -> {
                externalVOList.add(value);
                SkuVO skuVO = value.getSkuVO();
                catIds.add(skuVO.getCatId());
                brandIds.add(skuVO.getBrandId());
                vendorSet.add(skuVO.getVendorCode());
                skuPriceVOList.add(new SkuPriceVO(skuVO.getSkuId(), skuVO.getSourceCountryCode(),
                    supplierSettlementAccountAtomicService.getCurrencyBySupplierCode(skuVO.getVendorCode())));
                // countryManageService.getCurrencyByCountryCode(
            });

            // 类目
            Map<Long, String> catePathMap = categoryOutService.queryPathStr(catIds, lang);
            // 品牌
            Map<Long, String> brandMap = brandOutService.queryNameByIds(brandIds, lang);
            // 查询供应商
            Map<String, String> vendorMap = vendorManageService.getVendorMapByCodes(vendorSet);

            // 价格
            Map<Long, SkuPriceResVO> skuPriceResVoMap = skuScopeEnumSet.contains(SkuScopeEnum.PRICE) ? queryPriceBySkuList(skuPriceVOList) : null;
            // 库存
            Map<Long, SkuStockVO> skuStockVoMap = skuScopeEnumSet.contains(SkuScopeEnum.STOCK) ? queryStockBySkuList(skuIds) : null;
            return externalVOList.stream().map(external -> {
                SkuVO skuVO = external.getSkuVO();
                SkuDetailVO skuDetailVO = SkuConvert.INSTANCE.skuVo2detailVo(skuVO);
                // 为sku添加聚合的spu和sku扩展属性
                String spuGroupExtAttribute = "";
                if(external.getSpuVO() != null){
                    spuGroupExtAttribute = external.getSpuVO().getGroupExtAttribute();
                }
                // 为sku添加聚合的spu和sku扩展属性
                String skuGroupExtAttribute = "";
                if(external.getSkuVO() != null){
                    skuGroupExtAttribute = external.getSkuVO().getGroupExtAttribute();
                }
                String groupExtAttribute = ExtendPropertyGroupVO.obtainStringMergeExtProperty(spuGroupExtAttribute, skuGroupExtAttribute);
                skuDetailVO.setGroupExtAttribute(groupExtAttribute);
                skuDetailVO.setBuyer(external.getSpuVO().getBuyer());
                skuDetailVO.setPurchaseTypeStr(ConfigUtils.getStringFromJsonString(purchaseTypeStr, skuDetailVO.getSourceCountryCode(), lang));
                skuDetailVO.setCustomerTradeType(countryManageService.getTradeTypeByCountry(skuDetailVO.getSourceCountryCode()));
                skuDetailVO.setCustomerTradeTypeName(ConfigUtils.getStringFromJsonString(customerTradeTypeName, skuDetailVO.getCustomerTradeType(), lang));
                SpuLangVO spuLangVO = external.getLangVOList().stream().filter(vo -> vo.getLang().equals(lang)).findFirst().orElse(null);
                skuDetailVO.setSkuName(spuLangVO != null ? spuLangVO.getSpuTitle() : null);
                skuDetailVO.setCatePathStr(catePathMap != null ? catePathMap.get(skuVO.getCatId()) : null);
                skuDetailVO.setBrandName(brandMap != null ? brandMap.get(skuVO.getBrandId()) : null);
                if ((skuPriceResVoMap != null && skuPriceResVoMap.containsKey(skuDetailVO.getSkuId()))) {
                    skuDetailVO.setPurchasePrice(skuPriceResVoMap.get(skuDetailVO.getSkuId()).getPurchasePrice());
                    skuDetailVO.setSalePrice(skuPriceResVoMap.get(skuDetailVO.getSkuId()).getSalePrice());
                    skuDetailVO.setCurrency(skuPriceResVoMap.get(skuDetailVO.getSkuId()).getCurrency());
                }
                skuDetailVO.setStock((skuStockVoMap != null && skuStockVoMap.containsKey(skuDetailVO.getSkuId())) ? skuStockVoMap.get(skuDetailVO.getSkuId()).getStock() : null);
                skuDetailVO.setVendorName(vendorMap != null && vendorMap.containsKey(skuDetailVO.getVendorCode()) ? vendorMap.get(skuDetailVO.getVendorCode()) : null);
                skuDetailVO.setSkuStatus(external.getSpuVO().getSpuStatus());
                return skuDetailVO;
            }).collect(Collectors.toMap(SkuDetailVO::getSkuId, vo -> vo));
        } catch (Exception e) {
            log.error("【系统异常】SkuReadManageServiceImpl.batchQueryDetailBySkuIds, skuIds = {}, lang = {}", skuIds, lang, e);
        }
        return null;
    }

    @Override
    public SkuVO getSkuBySkuId(Long skuId) {
        SkuPO po = skuAtomicService.getSkuPoBySkuId(skuId);
        return SkuConvert.INSTANCE.po2vo(po);
    }

    @Override
    public DataResponse<SkuVO> getJdSkuById(Long jdSkuId, Long skuId) {
        //  校验jdSkuId存在绑定关系
        skuValidateService.validateJdSkuExist(jdSkuId, skuId);

        SkuVO skuVO = new SkuVO();
        skuVO.setJdSkuId(jdSkuId);
        // 查询jdSku信息
        JdProductQueryDTO param = new JdProductQueryDTO();
        param.setSkuIds(Lists.newArrayList(jdSkuId));
        Map<Long, JdProductDTO> jdSkuMap = skuInfoRpcService.querySkuMap(param);
        AssertValidation.isEmpty(jdSkuMap.get(jdSkuId), "国内skuId不存在，请检查！");

        JdProductDTO jdProductDTO = jdSkuMap.get(jdSkuId);
        if (Objects.nonNull(jdProductDTO.getWeight())) {
            // 主站重量单位是kg，国际工业单位是g，需要乘以1000
            skuVO.setWeight(jdProductDTO.getWeight().multiply(new BigDecimal(THOUSAND)).setScale(2, RoundingMode.UP).toString());
        }

        skuVO.setLength(Objects.nonNull(jdProductDTO.getLength()) ? String.valueOf(jdProductDTO.getLength().setScale(2, RoundingMode.UP)) : "");
        skuVO.setWidth(Objects.nonNull(jdProductDTO.getWidth()) ? String.valueOf(jdProductDTO.getWidth().setScale(2, RoundingMode.UP)) : "");
        skuVO.setHeight(Objects.nonNull(jdProductDTO.getHeight()) ? String.valueOf(jdProductDTO.getHeight().setScale(2, RoundingMode.UP)) : "");
        if (StringUtils.isNotBlank(jdProductDTO.getSupplyUnit())) {
            skuVO.setJdVendorCode(jdProductDTO.getSupplyUnit());
            VendorVo vendorVo = vendorRpcService.getVendorVoByVendorCode(jdProductDTO.getSupplyUnit());
            skuVO.setJdVendorName(Objects.nonNull(vendorVo) ? vendorVo.getVendorName() : "");
        }
        // 查询jdSku库存
        Map<Long, String> jdSkuStockMap = this.getJdSkuStockBySkuIds(Lists.newArrayList(jdSkuId));
        skuVO.setStockNum(MapUtils.isNotEmpty(jdSkuStockMap) && StringUtils.isNotBlank(jdSkuStockMap.get(jdSkuId)) ? jdSkuStockMap.get(jdSkuId) : SOLD_OUT);
        // 查询jdSku价格 满足条件的为mku，工业标准商品标记=1  供应商简码:bjjdbyxxjs
        if (Boolean.TRUE.equals(supportMku) && STANDARD_SKU_FLAG.equals(jdProductDTO.getGybzsp()) && CN_VENDOR_CODE.equals(jdProductDTO.getSupplyUnit())) {
            String mkuId = String.valueOf(jdSkuId);
            HashSet<String> mkuIdSet = Sets.newHashSet(mkuId);
            Map<String, Set<String>> mkuRelationSkuMap = mkuMappingRpcService.queryMkuRelationSku(mkuIdSet);
            log.info("SkuReadManageServiceImpl.getJdSkuPriceMap 查询mku关联sku mkuIdList={},mkuRelationSkuMap={}", JSON.toJSONString(mkuIdSet), JSON.toJSONString(mkuRelationSkuMap));
            if (MapUtils.isNotEmpty(mkuRelationSkuMap) && CollectionUtils.isNotEmpty(mkuRelationSkuMap.get(mkuId))) {
                Set<String> skuIdSet = mkuRelationSkuMap.get(mkuId);
                IscEachSkuPriceResDTO jdiMaxPrice = domesticSkuPriceManageService.getJdiMaxPrice(skuIdSet.stream().map(Long::valueOf).collect(Collectors.toList()));
                if (Objects.nonNull(jdiMaxPrice) && Objects.nonNull(jdiMaxPrice.getJdSkuPurchasePrice())) {
                    skuVO.setPurchasePrice(jdiMaxPrice.getJdSkuPurchasePrice().toPlainString());
                    skuVO.setSalePrice(jdiMaxPrice.getJdSkuPurchasePrice().divide(new BigDecimal(PURCHASE_RATE), 4, RoundingMode.UP).toPlainString());
                }
            }
        } else {
            Map<Long, IscEachSkuPriceResDTO> skuPriceResMap = domesticSkuPriceManageService.queryJdiSkuPrice(Lists.newArrayList(jdSkuId));
            log.info("SkuReadManageServiceImpl.getJdSkuById 查询sku价格 jdSkuId={},skuPriceResMap={}", jdSkuId, JSON.toJSONString(skuPriceResMap));
            if (MapUtils.isNotEmpty(skuPriceResMap) && Objects.nonNull(skuPriceResMap.get(jdSkuId))) {
                IscEachSkuPriceResDTO jdiSkuPriceResDTO = skuPriceResMap.get(jdSkuId);
                if (Objects.nonNull(jdiSkuPriceResDTO.getJdSkuPurchasePrice())) {
                    skuVO.setPurchasePrice(jdiSkuPriceResDTO.getJdSkuPurchasePrice().toPlainString());
                    skuVO.setSalePrice(jdiSkuPriceResDTO.getJdSkuPurchasePrice().divide(new BigDecimal(PURCHASE_RATE), 4, RoundingMode.UP).toPlainString());
                }
            }
        }
        return DataResponse.buildSuccess(skuVO);
    }

    private void fillSkuFeature(SkuVO skuVO, SkuFeaturePO skuFeature) {
        skuVO.setMagnetic(skuFeature.getMagnetic());
        skuVO.setElectric(skuFeature.getElectric());
        skuVO.setLiquid(skuFeature.getLiquid());
        skuVO.setPowder(skuFeature.getPowder());
        skuVO.setProductionCycle(skuFeature.getProductionCycle());
        skuVO.setOriginCountry(skuFeature.getOriginCountry());
        skuVO.setReturnType(skuFeature.getReturnType());
        skuVO.setReturnTypeValue(ReturnTypeEnum.descFromCode(skuFeature.getReturnType()));
    }

    private Map<Long, SkuPriceResVO> queryPriceBySkuList(List<SkuPriceVO> skuPriceVOList) {
        if (CollectionUtils.isEmpty(skuPriceVOList)) {
            return Collections.emptyMap();
        }
        SkuPriceReqVO skuPriceReqVO = new SkuPriceReqVO();
        skuPriceReqVO.setSkuPriceVO(skuPriceVOList);
        return skuPriceManageService.list(skuPriceReqVO);
    }

    private Map<Long, SkuStockVO> queryStockBySkuList(Set<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }
        SkuStockReqVO skuStockReqVO = new SkuStockReqVO();
        skuStockReqVO.setSkuIds(skuIds);
        return skuStockManageService.list(skuStockReqVO);
    }

    @Override
    public SkuCalculateTaxPriceVO getSkuCalculateSalePrice(SkuCalculatePriceReqVO reqVO) {
        String sourceCountryCode = reqVO.getSourceCountryCode();
        return skuTaxPriceManageService(sourceCountryCode).skuTaxPrice(reqVO);
    }

    private void calculateVnSkuPrice(CategoryTaxVO categoryTaxVO, BigDecimal purchasePrice, BigDecimal salePrice, SkuCalculateTaxPriceVO skuCalculateTaxPriceVO) {
        // 含税销售价 含税采购价=未税采购价*税率（进项增值税：系统自动根据税率计算）；
        if (Objects.nonNull(categoryTaxVO.getVatRate())) {
            BigDecimal taxPrice = purchasePrice.multiply(categoryTaxVO.getVatRate()).setScale(4, RoundingMode.UP);
            BigDecimal taxPurchasePrice = purchasePrice.add(taxPrice);
            skuCalculateTaxPriceVO.setTaxPurchasePrice(taxPurchasePrice);
            // 不退税情况下毛利=（不含税销售价-不含税采购价-不含税采购价*税率）/不含税销售价*100%
            if(salePrice != null){
                BigDecimal noTaxGross = salePrice.subtract(purchasePrice).subtract(taxPrice).divide(salePrice, 4, RoundingMode.UP).multiply(new BigDecimal(100));
                skuCalculateTaxPriceVO.setNoTaxGrossRate(noTaxGross);
            }
        }


        // 含税销售价 含税销售价=未税销售价*税率（销项增值税：EPE为0，后期FDI根据系统维护FDI税率）
        if (Objects.nonNull(categoryTaxVO.getDestinationVatRate()) && salePrice != null) {
            BigDecimal taxSalePrice = salePrice.add(salePrice.multiply(categoryTaxVO.getDestinationVatRate()).setScale(4, RoundingMode.UP));
            skuCalculateTaxPriceVO.setTaxSalePrice(taxSalePrice);
        }

        if(salePrice != null){
            // 正常退税情况下毛利=（不含税销售价-不含税采购价）/不含税销售价*100
            BigDecimal taxGross = salePrice.subtract(purchasePrice).divide(salePrice, 4, RoundingMode.UP).multiply(new BigDecimal(100));
            skuCalculateTaxPriceVO.setTaxGrossRate(taxGross.setScale(4, RoundingMode.UP));
        }
    }

    private void calculateThSkuPrice(BigDecimal valueAddTax, BigDecimal purchasePrice, BigDecimal salePrice, SkuCalculateTaxPriceVO skuCalculateTaxPriceVO) {
        // 含税销售价 含税采购价=未税采购价*税率（进项增值税：系统自动根据税率计算）；
        if (Objects.nonNull(valueAddTax)) {
            BigDecimal taxPrice = purchasePrice.multiply(valueAddTax).setScale(4, RoundingMode.UP);
            BigDecimal taxPurchasePrice = purchasePrice.add(taxPrice);
            skuCalculateTaxPriceVO.setTaxPurchasePrice(taxPurchasePrice);
            // 不退税情况下毛利=（不含税销售价-不含税采购价-不含税采购价*税率）/不含税销售价*100%
            if(salePrice != null){
                BigDecimal noTaxGross = salePrice.subtract(purchasePrice).subtract(taxPrice).divide(salePrice, 4, RoundingMode.UP).multiply(new BigDecimal(100));
                skuCalculateTaxPriceVO.setNoTaxGrossRate(noTaxGross);
            }
        }


        // 含税销售价 含税销售价=未税销售价*税率（销项增值税：EPE为0，后期FDI根据系统维护FDI税率）
        if (Objects.nonNull(valueAddTax) && salePrice != null) {
            BigDecimal taxSalePrice = salePrice.add(salePrice.multiply(valueAddTax).setScale(4, RoundingMode.UP));
            skuCalculateTaxPriceVO.setTaxSalePrice(taxSalePrice);
        }

        if(salePrice != null){
            // 正常退税情况下毛利=（不含税销售价-不含税采购价）/不含税销售价*100
            BigDecimal taxGross = salePrice.subtract(purchasePrice).divide(salePrice, 4, RoundingMode.UP).multiply(new BigDecimal(100));
            skuCalculateTaxPriceVO.setTaxGrossRate(taxGross.setScale(4, RoundingMode.UP));
        }
    }

    @Override
    public void handleLocalSkuPrice(List<SkuVO> skuVOList,String sourceCountryCode) {
        if (CollectionUtils.isEmpty(skuVOList) || StringUtils.isBlank(sourceCountryCode)) {
            log.error("SkuReadManageServiceImpl.handleLocalSkuPrice params is null");
            return;
        }

        SkuVO skuVO = skuVOList.get(0);
        // 不是中国就处理
        if (CountryConstant.COUNTRY_ZH.equals(sourceCountryCode)) {
            log.info("handleLocalSkuPrice 中国");
            return;
        }
        final Long catId = skuVO.getCatId();
        // 计算含税采购价
        List<CategoryTaxVO> categoryTaxVOS = categoryTaxOutService.getCategoryTaxVO(new CategoryTaxVO(catId, CompanyTypeEnum.FDI.getCode(), sourceCountryCode));
        if (CountryConstant.COUNTRY_VN.equals(sourceCountryCode) && CollectionUtils.isEmpty(categoryTaxVOS)) {
            log.info("handleLocalSkuPrice 未查询到当前类目税率 入参:[catId={},sourceCountryCode={},companyType={}]", catId, sourceCountryCode, CompanyTypeEnum.FDI);
            return;
        }

        Map<String,CategoryTaxVO> taxVOMap = categoryTaxVOS.stream().collect(Collectors.toMap(item->StringUtils.joinWith(
            Constant.UNDER_LINE,item.getCategoryId(),item.getSkuId()),Function.identity(),(v1,v2)->v2));
        log.info("handleLocalSkuPrice 未查询到当前类目税率 入参:[catId={},taxVOMap={},companyType={}]", catId, taxVOMap.keySet(), CompanyTypeEnum.FDI);

        // 补充含税采购价和含税销售价等信息
        for (SkuVO vo : skuVOList) {
            if (StringUtils.isBlank(vo.getPurchasePrice()) || new BigDecimal(vo.getPurchasePrice()).compareTo(BigDecimal.ZERO) <= 0 ){
                continue;
            }
            SkuPricePO skuPricePO = skuPriceManageService.getOne(new SkuPriceVO(vo.getSkuId(), TradeDirectionEnum.SUPPLIER, vo.getSourceCountryCode(), vo.getCurrency()));

            SkuCalculatePriceReqVO input = new SkuCalculatePriceReqVO();
            input.setCatId(vo.getCatId());
            input.setSkuId(vo.getSkuId());
            input.setSourceCountryCode(sourceCountryCode);
            SpuTaxManageService spuTaxManageService = this.spuTaxManageService(sourceCountryCode);
            BigDecimal taxRate = spuTaxManageService.getParamTax(vo);
            input.setTaxRate(Objects.nonNull(taxRate)?taxRate.multiply(DECIMAL_HUNDRED):null);
            input.setSkuPriceFlag(Boolean.FALSE);
            // 采购价从价格主数据查询，不用草稿价格数据 sunlei61 2025-04-16
            input.setPurchasePrice(StringUtils.isNotBlank(vo.getPurchasePrice())?new BigDecimal(vo.getPurchasePrice()):null);
            if (Objects.nonNull(skuPricePO) && Objects.nonNull(skuPricePO.getPrice())){
                input.setPurchasePrice(skuPricePO.getPrice());
            }
            input.setSalePrice(StringUtils.isNotBlank(vo.getSalePrice())?new BigDecimal(vo.getSalePrice()):null);
            input.setTaxPurchasePrice(StringUtils.isNotBlank(vo.getTaxPurchasePrice())?new BigDecimal(vo.getTaxPurchasePrice()):null);
            if (Objects.nonNull(skuPricePO) && Objects.nonNull(skuPricePO.getTaxPrice())){
                input.setTaxPurchasePrice(skuPricePO.getTaxPrice());
            }
            input.setTaxSalePrice(StringUtils.isNotBlank(vo.getTaxSalePrice())?new BigDecimal(vo.getTaxSalePrice()):null);
            input.setSupplierCode(vo.getVendorCode());
            SkuCalculateTaxPriceVO taxPriceVO = new SkuCalculateTaxPriceVO();
            if(CountryConstant.COUNTRY_VN.equals(sourceCountryCode)){
                CategoryTaxVO categoryTaxVO = taxVOMap.get(StringUtils.joinWith(Constant.UNDER_LINE,vo.getCatId(),vo.getSkuId()));
                if(categoryTaxVO == null){
                    log.info("handleLocalSkuPrice 未查询到当前类目税率 入参:[catId={},taxVOMap={},companyType={}]", catId, StringUtils.joinWith(Constant.UNDER_LINE,vo.getCatId(),""), CompanyTypeEnum.FDI);
                    categoryTaxVO = taxVOMap.get(StringUtils.joinWith(Constant.UNDER_LINE,vo.getCatId(),""));
                }
                input.setCategoryTaxVO(categoryTaxVO);
            } else if (CountryConstant.COUNTRY_BR.equals(sourceCountryCode)){
                Map<Long,String>  taxRateMap = Maps.newHashMap();
                // 计算税金和销售价
                skuConvertService.fillBrTaxRateMap(vo, spuTaxManageService, taxRateMap);

                input.setTaxRateMap(taxRateMap);
            }
            input.setCalculatePriceEnums(CalculatePriceEnums.INQUIRE);
            taxPriceVO = this.skuTaxPriceManageService(sourceCountryCode).skuTaxPrice(input);

            vo.setPurchasePrice(Objects.isNull(taxPriceVO.getPurchasePrice())?null:taxPriceVO.getPurchasePrice().toString());
            vo.setSalePrice(Objects.isNull(taxPriceVO.getSalePrice())?null:taxPriceVO.getSalePrice().toString());
            vo.setTaxPurchasePrice(Objects.isNull(taxPriceVO.getTaxPurchasePrice())?null:taxPriceVO.getTaxPurchasePrice().toString());
            vo.setTaxSalePrice(Objects.isNull(taxPriceVO.getTaxSalePrice())?null:taxPriceVO.getTaxSalePrice().toString());
            vo.setNoTaxGrossRate(taxPriceVO.getNoTaxGrossRate());
            vo.setTaxGrossRate(taxPriceVO.getTaxGrossRate());
            /** 采购价体系优化 2025-04-14 sunlei61 增加国家协议价、国家成本价、协议价利润率**/
            vo.setAgreementPrice(taxPriceVO.getAgreementPrice());
            vo.setCountryCostPrice(taxPriceVO.getCountryCostPrice());
            vo.setAgreementProfitRate(taxPriceVO.getAgreementProfitRate());
            vo.setCostCurrency(taxPriceVO.getCostCurrency());
        }
    }

    @Override
    public List<SkuVO> querySkuListByBrandId(Set<Long> brandIds) {
        List<Long> list = new ArrayList<>(brandIds);
        List<List<Long>> partition = Lists.partition(list, PAGE_SIZE);
        List<SkuVO> result = new ArrayList<>();
        for (List<Long> part : partition) {
            List<SkuPO> tmp = skuAtomicService.querySkuListByBrandId(part);
            if (CollectionUtils.isNotEmpty(tmp)) {
                result.addAll(SkuConvert.INSTANCE.listPo2Vo(tmp));
            }
        }
        return result;
    }

    @Override
    public Map<String, SkuFeatureApiDTO> getSkuFeatureBySku(QuerySkuReqDTO skuReqDTO) {
        Set<SkuQueryEnum> skuQueryEnums = Sets.newHashSet();
        // 兼容老逻辑，没传查询参数时，默认查询销售信息
        if (CollectionUtils.isEmpty(skuReqDTO.getSkuQueryEnums())) {
            skuQueryEnums.add(SkuQueryEnum.FEATURE);
        }else {
            skuQueryEnums = skuReqDTO.getSkuQueryEnums();
        }
        Map<String, SkuFeatureApiDTO> resultMap = Maps.newHashMap();
        // 补充sku的销售信息
        if (skuQueryEnums.contains(SkuQueryEnum.FEATURE)){
           this.fillSkuFeature(skuReqDTO, resultMap);
        }
        if (skuQueryEnums.contains(SkuQueryEnum.CUSTOMS)) {
            this.fillSkuCustoms(skuReqDTO, resultMap);
        }
        return resultMap;
    }

    /**
     * 根据给定的 SKU IDs，填充 SKU 的海关信息。
     * @param skuReqDTO 包含 SKU IDs 的请求对象。
     * @param resultMap 存储填充后的 SKU 特性信息的 Map。
     */
    private void fillSkuCustoms(QuerySkuReqDTO skuReqDTO, Map<String, SkuFeatureApiDTO> resultMap) {
        Set<Long> skuIds = skuReqDTO.getSkuIds().stream().map(Long::valueOf).collect(Collectors.toSet());
        Map<Long, SkuPO> skuPoMap = skuAtomicService.batchQuerySkuPO(skuIds);
        if (MapUtils.isEmpty(skuPoMap)) {
            return;
        }
        Set<Long> spuIds = skuPoMap.values().stream().filter(Objects::nonNull).map(SkuPO::getSpuId).collect(Collectors.toSet());
        // 查询spu跨境属性
        Map<Long, Map<Long, List<ProductGlobalAttributePO>>> spuGlobalAttributeMap = productGlobalAttributeAtomicService.queryProductGlobalAttributeMap(spuIds, GlobalAttributeEnum.spuAttributeIdSet(), KeyTypeEnum.SPU.getCode());
        for (Map.Entry<Long, SkuPO> entry : skuPoMap.entrySet()) {
            Long skuId = entry.getKey();
            SkuPO skuPO = entry.getValue();
            SkuFeatureApiDTO featureApiDTO = resultMap.get(String.valueOf(skuId));
            if (Objects.isNull(featureApiDTO)) {
                featureApiDTO = new SkuFeatureApiDTO();
            }
            CustomsApiDTO customsApiDTO = new CustomsApiDTO();
            customsApiDTO.setHsCode(skuPO.getHsCode());
            Map<Long, List<ProductGlobalAttributePO>> spuGlobalAttributePoMap = spuGlobalAttributeMap.getOrDefault(skuPO.getSpuId(), Maps.newHashMap());
            customsApiDTO.setDeclarationElements(this.getTextResultValue(spuGlobalAttributePoMap,GlobalAttributeEnum.SPU_DECLARATION_ELEMENTS));
            customsApiDTO.setDeclarationEnName(this.getTextResultValue(spuGlobalAttributePoMap,GlobalAttributeEnum.SPU_DECLARATION_NAME));
            customsApiDTO.setDeclarationZhName(this.getTextResultValue(spuGlobalAttributePoMap,GlobalAttributeEnum.SPU_DECLARATION_EN_NAME));
            featureApiDTO.setCustomsApiDTO(customsApiDTO);
            resultMap.put(String.valueOf(skuId), featureApiDTO);
        }
    }

    /**
     * 填充 SKU 特性信息。
     * @param skuReqDTO 查询 SKU 请求对象，包含国家代码等信息。
     * @param resultMap SKU 特性信息结果集，key 为 SKU ID，value 为 SKU 特性信息。
     */
    private void fillSkuFeature(QuerySkuReqDTO skuReqDTO, Map<String, SkuFeatureApiDTO> resultMap) {
        Set<Long> skuIds = skuReqDTO.getSkuIds().stream().map(Long::valueOf).collect(Collectors.toSet());
        Map<Long, SkuFeaturePO> skuFeaturePOMap = skuFeatureAtomicService.querySkuFeatureMap(skuIds);
        if (MapUtils.isEmpty(skuFeaturePOMap)) {
            return;
        }
        // 查询sku跨境属性
        Map<Long, Map<Long, List<ProductGlobalAttributePO>>> skuGlobalAttributeMap = productGlobalAttributeAtomicService.queryProductGlobalAttributeMap(skuIds, GlobalAttributeEnum.skuAttributeIdSet(), KeyTypeEnum.SKU.getCode());
        // 根据国家查询SKU的备货模式
        Map<Long, Integer> skuPurchaseModelMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(skuReqDTO.getCountryCode())) {
            skuPurchaseModelMap = skuFeatureManageService.querySkuPurchaseModel(skuIds, skuReqDTO.getCountryCode());
        }

        // 取出sku的跨境属性值
        for (Map.Entry<Long,SkuFeaturePO> entry : skuFeaturePOMap.entrySet()) {
            Long skuId = entry.getKey();
            SkuFeaturePO skuFeaturePO = entry.getValue();
            // 获取sku的跨境属性
            Map<Long, List<ProductGlobalAttributePO>> skuGlobalAttributePoMap = skuGlobalAttributeMap.get(skuId);
            skuFeaturePO.setElectric(this.getRadioResultValue(skuGlobalAttributePoMap,GlobalAttributeEnum.SKU_ELECTRIFIED));
            skuFeaturePO.setMagnetic(this.getRadioResultValue(skuGlobalAttributePoMap,GlobalAttributeEnum.SKU_MAGNETIC));
            skuFeaturePO.setLiquid(this.getRadioResultValue(skuGlobalAttributePoMap,GlobalAttributeEnum.SKU_LIQUID));
            skuFeaturePO.setPowder(this.getRadioResultValue(skuGlobalAttributePoMap,GlobalAttributeEnum.SKU_DUST));
            skuFeaturePO.setOriginCountry(this.getTextResultValue(skuGlobalAttributePoMap,GlobalAttributeEnum.SKU_COUNTRY_ORIGIN));
            skuFeaturePO.setEnterprise(this.getTextResultValue(skuGlobalAttributePoMap,GlobalAttributeEnum.SKU_MANUFACTURING_ENTERPRISE));
            skuFeaturePO.setOrgCode(this.getTextResultValue(skuGlobalAttributePoMap,GlobalAttributeEnum.SKU_ORGANIZATION_CODE));
            skuFeaturePO.setContactsName(this.getTextResultValue(skuGlobalAttributePoMap,GlobalAttributeEnum.SKU_CONTACT_PERSON_NAME));
            skuFeaturePO.setContactsPhone(this.getTextResultValue(skuGlobalAttributePoMap,GlobalAttributeEnum.SKU_CONTACT_PERSON_PHONE));
            skuFeaturePO.setPurchaseModel(skuPurchaseModelMap.get(skuId));
            resultMap.put(String.valueOf(skuId),SkuConvert.INSTANCE.po2SkuFeatureApiDTO(skuFeaturePO));
        }
    }

    private Integer getRadioResultValue(Map<Long, List<ProductGlobalAttributePO>> skuGlobalAttributePoMap, GlobalAttributeEnum attributeEnum) {

        if (MapUtils.isEmpty(skuGlobalAttributePoMap)) {
            return null;
        }

        List<ProductGlobalAttributePO> globalAttributePOList = skuGlobalAttributePoMap.get(attributeEnum.getId());
        Integer radioValue = null;
        if (CollectionUtils.isNotEmpty(globalAttributePOList)) {
            ProductGlobalAttributePO attributePO = globalAttributePOList.get(0);
            radioValue = GlobalAttributeValueRealtionEnum.getAttributeValue(attributePO.getAttributeId(), attributePO.getAttributeValueId());
        }
        return radioValue;
    }

    private String getTextResultValue(Map<Long, List<ProductGlobalAttributePO>> skuGlobalAttributePoMap, GlobalAttributeEnum attributeEnum) {
        if (MapUtils.isEmpty(skuGlobalAttributePoMap)) {
            return null;
        }
        List<ProductGlobalAttributePO> globalAttributePOList = skuGlobalAttributePoMap.get(attributeEnum.getId());

        if (CollectionUtils.isEmpty(globalAttributePOList)) {
            return null;
        }
        ProductGlobalAttributePO attributePO = globalAttributePOList.get(0);
        return attributePO.getAttributeValue();
    }

    @Override
    public Map<Long, SkuPO> getSkuBaseInfoBySku(Long spuId) {
        LambdaQueryWrapper<SkuPO> wrapper =
                new LambdaQueryWrapper<SkuPO>().in(SkuPO::getSpuId, spuId).eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> res = skuBaseMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(res)){
            return res.stream().collect(Collectors.toMap(SkuPO::getSkuId, Function.identity()));
        }
        return null;
    }

    private SkuTaxPriceManageService skuTaxPriceManageService(String sourceCountryCode) {
        return skuTaxPriceManageServiceMap.get(SkuTaxCalculateCountryEnums.getEnumByCountryCode(sourceCountryCode).getServiceName());
    }

    private SpuTaxManageService spuTaxManageService(String sourceCountryCode) {
        return spuTaxManageServiceMap.get(SpuTaxCountryEnums.getEnumByCountryCode(sourceCountryCode).getServiceName());
    }

    /**
     * 合法性校验
     * @param param 全球属性的更新参数列表。
     *               如果参数为空、skuId为空、变更的跨境属性为空、跨境属性的key或value为空，会抛出异常。
     */
    private void checkSelectGlobalAttribute(SkuGlobalAttributeVO param){
        if(param == null){
            throw new BizException("参数为空");
        }
        if(StringUtils.isBlank(param.getTargetCountryCode())){
            throw new BizException("目标国家为空");
        }
        if(CollectionUtils.isEmpty(param.getDetailData())){
            throw new BizException("数据为空");
        }

        for(SkuGlobalAttributeDetailVO skuGlobalAttributeDetailVO : param.getDetailData()){
            if(skuGlobalAttributeDetailVO.getSkuId() == null){
                throw new BizException("skuId为空");
            }
        }
    }

    /**
     * 根据国家代码获取更新属性。
     * @param countryCode 国家代码
     * @return 更新属性的键值对，键为属性名，值为属性值的长整型表示；如果国家代码为空或找不到对应的更新属性，则返回null。
     */
    private Map<Long,String> getSelectAttribute(String countryCode){
        if (StringUtils.isBlank(countryCode)) {
            return null;
        }
        com.alibaba.fastjson.JSONObject globalAttributeObj = ConfigUtils.getJsonByKey(globalAttribute, countryCode);
        return globalAttributeObj.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> Long.parseLong(entry.getValue().toString()), // 将value转换为Long作为新的key
                        Map.Entry::getKey, // 原来的key作为新的value
                        (v1, v2) -> v1, // 如果有重复的key，保留第一个值
                        HashMap::new // 使用HashMap作为结果Map的实现
                ));
    }

    /**
     * 获取所有更新国家代码
     * @return 更新国家代码集合
     */
    private Set<String> getAllSelectCountryCode(){
        com.alibaba.fastjson.JSONObject configJson = com.alibaba.fastjson.JSONObject.parseObject(globalAttribute);
        return configJson.keySet();
    }

    /**
     * 处理属性的方法，针对特定国家（非越南）的属性进行处理。
     * @param targetCountryCode 目标国家的国别码
     * @param propertyVOS 属性列表
     * @param resultMap 结果映射
     * @param attributeIds 属性ID集合
     * @param attributeRelationMap 属性关系映射
     */
    private void processPropertiesVn(String targetCountryCode,List<PropertyVO> propertyVOS, Map<String, Object> resultMap,Set<Long> attributeIds,Map<Long,String> attributeRelationMap) {
        if(!StringUtils.equals(CountryConstant.COUNTRY_VN,targetCountryCode)){
            return;
        }

        for (PropertyVO propertyVO : propertyVOS) {
            if (!attributeIds.contains(propertyVO.getAttributeId())) {
                continue;
            }

            String key = attributeRelationMap.get(propertyVO.getAttributeId());
            GlobalAttributeVnEnum vnEnum = GlobalAttributeVnEnum.getEnumByCode(key);

            processPropertyByEnum(vnEnum, propertyVO, key, resultMap);
        }
    }

    private void processPropertyByEnum(GlobalAttributeVnEnum vnEnum, PropertyVO propertyVO, String key, Map<String, Object> resultMap) {
        switch (vnEnum) {
            case VN_IMPORT_LIMIT:
                processImportLimit(propertyVO, key, resultMap);
                break;
            case VN_IN_BONDED_WAREHOUSE:
                processInBondedWarehouse(propertyVO, key, resultMap);
                break;
            case VN_HSCODE:
            case VN_IMPORT_REMARK:
                processHscodeOrImportRemark(propertyVO, key, resultMap);
                break;
        }
    }

    private void processImportLimit(PropertyVO propertyVO, String key, Map<String, Object> resultMap) {
        Map<Long, Integer> limitMap = invertMap(GlobalAttributeVnEnum.importLimiValueMap);
        processSelectedValue(propertyVO, key, resultMap, limitMap);
    }

    private void processInBondedWarehouse(PropertyVO propertyVO, String key, Map<String, Object> resultMap) {
        Map<Long, Integer> bondMap = invertMap(GlobalAttributeVnEnum.inBondedValueMap);
        processSelectedValue(propertyVO, key, resultMap, bondMap);
    }

    private void processHscodeOrImportRemark(PropertyVO propertyVO, String key, Map<String, Object> resultMap) {
        for (PropertyValueVO propertyValueVO : propertyVO.getPropertyValueVOList()) {
            if (Boolean.TRUE.equals(propertyValueVO.getSelected())) {
                resultMap.put(key, propertyValueVO.getAttributeValueName());
            }
        }
    }

    private <K, V> Map<V, K> invertMap(Map<K, V> map) {
        return map.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getValue,
                        Map.Entry::getKey,
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }

    private void processSelectedValue(PropertyVO propertyVO, String key, Map<String, Object> resultMap, Map<Long, Integer> valueMap) {
        for (PropertyValueVO propertyValueVO : propertyVO.getPropertyValueVOList()) {
            if (Boolean.TRUE.equals(propertyValueVO.getSelected())) {
                resultMap.put(key, valueMap.get(propertyValueVO.getAttributeValueId()));
            }
        }
    }

    @Override
    public void handleLocalSkuPrice(List<SkuVO> skuVOList, String sourceCountryCode, String supplierCode) {
        if (CollectionUtils.isNotEmpty(skuVOList)) {
             skuVOList.forEach(vo -> {vo.setVendorCode(supplierCode);});
        }
        this.handleLocalSkuPrice(skuVOList,sourceCountryCode);
    }

    /**
     * 根据SPU ID集合获取对应的SKU列表
     * @param spuIds SPU ID集合
     * @return SKU列表
     */
    @Override
    public List<SkuPO> getSkuListBySpuIds(Collection<Long> spuIds) {
        List<SkuPO> skuListBySpuIds = skuAtomicService.getSkuListBySpuIds(spuIds);
        return skuListBySpuIds;
    }

    /**
     * 根据 SKU ID 和仓库 ID 从 Map 中获取库存阈值。
     * @param skuStockThresholdMap SKU 的库存阈值 Map。
     * @param skuId SKU 的 ID。
     * @param warehouseId 仓库的 ID。
     * @return 库存阈值，若未找到则返回 null。
     */
    private Long getStockThreshold(Map<String,SkuStockThresholdPO> skuStockThresholdMap,Long skuId,String warehouseId) {
        warehouseId = StringUtils.isBlank(warehouseId) || FACTORY_DEFAULT_ID_STR.equals(warehouseId) ? null : warehouseId;

        if (StringUtils.isBlank(warehouseId) && skuStockThresholdMap.containsKey(skuId.toString())) {
            return skuStockThresholdMap.get(skuId.toString()).getStockThreshold();
        }else if (StringUtils.isNotBlank(warehouseId) && skuStockThresholdMap.containsKey(skuId + warehouseId)) {
            return skuStockThresholdMap.get(skuId + warehouseId).getStockThreshold();
        }
        return null;
    }

    /**
     * 获取在途销售状态。
     * @param skuOnWaySaleMap SKU在途销售状态映射表。
     * @param skuId SKU ID。
     * @param warehouseId 仓库ID。
     * @return 在途销售状态。
     */
    private Integer getOnWaySaleStatus(Map<Long, Map<Long, Integer>> skuOnWaySaleMap, Long skuId, String warehouseId) {
            if (skuOnWaySaleMap == null || skuOnWaySaleMap.isEmpty() || skuId == null || StringUtils.isBlank(warehouseId)) {
                return null;
            }
            return Optional.ofNullable(skuOnWaySaleMap.get(skuId))
                    .map(warehouseOnSale -> warehouseOnSale.get(Long.valueOf(warehouseId)))
                    .orElse(null);
    }

    @Override
    public Map<Long, String> queryCnStockByTargetCountryCode(List<Long> skuIds, String targetCountryCode) {
        return Collections.emptyMap();
    }

    @Override
    public Map<Long, String> queryCnStockByClientCode(List<Long> skuIds, String clientCode) {
        return Collections.emptyMap();
    }


    @Override
    public Map<Long, VendorPO> queryVendorBySkuIds(Set<Long> skuIds) {
        Map<Long, VendorPO> result = new HashMap<>();

        LambdaQueryWrapper<SkuPO> wrapper = new LambdaQueryWrapper<SkuPO>().in(SkuPO::getJdSkuId, skuIds)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> skuPOList = skuBaseMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(skuPOList)){
            return result;
        }

        Map<String, Long> vendorSkuMap = skuPOList.stream().filter(s -> StringUtils.isNotBlank(s.getJdVendorCode())).collect(Collectors.toMap(SkuPO::getJdVendorCode, SkuPO::getJdSkuId));
        List<VendorPO> vendorList = vendorAtomicService.getVendorByJdVendorCode(vendorSkuMap.keySet());
        if (CollectionUtils.isEmpty(vendorList)){
            return result;
        }

        vendorList.forEach(v -> {
            Long skuId = vendorSkuMap.get(v.getVendorCode());
            if (Objects.nonNull(skuId)) {
                result.put(skuId,v);
            }
        });
        return result;
    }


    @Override
    public DataResponse<Map<String, SkuBaseInfoApiDTO>> querySkuBaseInfo(QuerySkuReqDTO reqDTO) {
        Set<Long> skuIds = reqDTO.getSkuIds().stream().map(Long::parseLong).collect(Collectors.toSet());
        Map<Long, SkuPO> skuBaseMap = this.getSkuBaseInfoBySku(skuIds);
        if (MapUtils.isEmpty(skuBaseMap)) {
            return DataResponse.success(Collections.emptyMap());
        }
        // 查询SPU信息
        Set<Long> spuIds = skuBaseMap.values().stream().map(SkuPO::getSpuId).collect(Collectors.toSet());
        Map<Long, SpuPO> spuMap = spuAtomicService.getSpuMap(spuIds);
        // 商品多语言映射 key:spuId value: 多语言映射
        Map<Long, Map<String, SpuLangPO>> spuLangMap = spuLangAtomicService.querySpuLangMap(spuIds, reqDTO.getLangSet());
        // 品牌ID和品牌信息的关系
        Map<Long, BrandVO> brandMap = getBrandVOMap(reqDTO, skuBaseMap);

        Map<String, SkuBaseInfoApiDTO> resultMap = Maps.newHashMap();
        skuBaseMap.forEach((skuId, skuPo) -> {
            SkuBaseInfoApiDTO apiDTO = getSkuBaseInfoApiDTO(skuPo, spuMap, spuLangMap, brandMap);
            resultMap.put(String.valueOf(skuId), apiDTO);
        });
        // 查询特殊信息
        buildSkuFeature(reqDTO, resultMap);
        // 供应商信息
        buildSupplyInfo(reqDTO, resultMap);
        // 扩展信息
        buildExtend(reqDTO, spuMap, resultMap);
        // 客户池、国家池数据
        buildPool(reqDTO, skuIds, resultMap);
        return DataResponse.success(resultMap);
    }

    /**
     * 构建 SKU 池，包括客户池和国家池。
     * @param reqDTO 查询 SKU 的请求参数
     * @param skuIds SKU 的 ID 集合
     * @param resultMap SKU 基础信息的映射结果
     */
    private void buildPool(QuerySkuReqDTO reqDTO, Set<Long> skuIds, Map<String, SkuBaseInfoApiDTO> resultMap) {
        if (CollectionUtils.isNotEmpty(reqDTO.getSkuQueryEnums()) && StringUtils.isNotBlank(reqDTO.getCountryCode())
                && (reqDTO.getSkuQueryEnums().contains(SkuQueryEnum.CUSTOMER_POOL) || reqDTO.getSkuQueryEnums().contains(SkuQueryEnum.COUNTRY_POOL))) {
            Map<Long, Long> skuMkuMap = mkuRelationAtomicService.queryMkuIdBySkuIds(skuIds);
            // 客户池
            customerPool(reqDTO, skuMkuMap, resultMap);
            // 国家池
            countryPool(reqDTO, skuMkuMap, resultMap);
        }
    }

    /**
     * 根据请求参数和 SKU-MKU 映射表，更新 SKU 的在国池状态。
     * @param reqDTO 查询 SKU 的请求参数对象。
     * @param skuMkuMap SKU 和 MKU 的映射表。
     * @param resultMap SKU 的基本信息结果集。
     */
    private void countryPool(QuerySkuReqDTO reqDTO, Map<Long, Long> skuMkuMap, Map<String, SkuBaseInfoApiDTO> resultMap) {
        if (!reqDTO.getSkuQueryEnums().contains(SkuQueryEnum.COUNTRY_POOL)) {
            return;
        }
        // 查询商品国家池状态
        List<CountryMkuPO> countryPoolList = countryMkuAtomicService.getPoByMkuIdCountryPoolStatus(Lists.newArrayList(skuMkuMap.values()), reqDTO.getCountryCode(), CountryMkuPoolStatusEnum.POOL.getCode());
        if (CollectionUtils.isNotEmpty(countryPoolList)) {
            Map<Long, Long> countryMkuCountMap = countryPoolList.stream().collect(Collectors.groupingBy(CountryMkuPO::getMkuId, Collectors.counting()));
            resultMap.forEach((skuId, apiDto)-> {
                if (!skuMkuMap.containsKey(Long.valueOf(skuId))) {
                    apiDto.setInCountryPool(Boolean.FALSE);
                }else {
                    Long mkuId = skuMkuMap.get(Long.valueOf(skuId));
                    apiDto.setInCustomerPool(countryMkuCountMap.containsKey(mkuId) ? Boolean.TRUE:Boolean.FALSE);
                }
            });
        }
    }

    /**
     * 根据请求参数和商品信息，更新商品的客户池状态。
     * @param reqDTO 查询请求参数对象，包含查询类型等信息。
     * @param skuMkuMap 商品ID到MKU ID的映射关系。
     * @param resultMap 已经查询到的商品基本信息，需要更新客户池状态。
     */
    private void customerPool(QuerySkuReqDTO reqDTO, Map<Long, Long> skuMkuMap, Map<String, SkuBaseInfoApiDTO> resultMap) {
        if (!reqDTO.getSkuQueryEnums().contains(SkuQueryEnum.CUSTOMER_POOL)) {
           return;
        }
        // 查询商品客户池状态
        List<CustomerMkuPO> customerMkuPOList = customerMkuAtomicService.listCustomerMkuByMkuIdsCountry(Sets.newHashSet(skuMkuMap.values()), reqDTO.getCountryCode());
        if (CollectionUtils.isNotEmpty(customerMkuPOList)) {
            Map<Long, Long> mkuCountMap = customerMkuPOList.stream().collect(Collectors.groupingBy(CustomerMkuPO::getMkuId, Collectors.counting()));
            resultMap.forEach((skuId, apiDto) -> {
                if (!skuMkuMap.containsKey(Long.valueOf(skuId))) {
                    apiDto.setInCustomerPool(Boolean.FALSE);
                }else {
                    Long mkuId = skuMkuMap.get(Long.valueOf(skuId));
                    apiDto.setInCustomerPool(mkuCountMap.containsKey(mkuId) ? Boolean.TRUE:Boolean.FALSE);
                }
            });
        }
    }

    private void buildExtend(QuerySkuReqDTO reqDTO, Map<Long, SpuPO> spuMap, Map<String, SkuBaseInfoApiDTO> resultMap) {
        if (CollectionUtils.isNotEmpty(reqDTO.getSkuQueryEnums()) && (reqDTO.getSkuQueryEnums().contains(SkuQueryEnum.ALL) || reqDTO.getSkuQueryEnums().contains(SkuQueryEnum.EXTEND))){
            // 语言信息获取
            String lang =  CollectionUtils.isNotEmpty(reqDTO.getLangSet()) ? Lists.newArrayList(reqDTO.getLangSet()).get(0) : LangConstant.LANG_EN;

            // 一级、二级、三级类目查询
            Set<Long> catIds = spuMap.values().stream().filter(Objects::nonNull).map(SpuPO::getJdCatId).collect(Collectors.toSet());
            Map<Long, CategoryPathVO> categoryPathVOMap = categoryOutService.queryPath(catIds);

            resultMap.forEach((skuId, apiDto)->{
                if (MapUtils.isNotEmpty(categoryPathVOMap)) {
                    Long catId = apiDto.getCatId();
                    CategoryPathVO categoryPathVO = categoryPathVOMap.getOrDefault(catId, new CategoryPathVO());
                    apiDto.setFirstCatId(categoryPathVO.getId1());
                    apiDto.setSecondCatId(categoryPathVO.getId2());
                    apiDto.setThirdCatId(categoryPathVO.getId3());
                }
                Long spuId = apiDto.getSpuId();
                SpuPO spuPO = spuMap.get(spuId);
                if (Objects.nonNull(spuPO)) {
                    // 型号
                    apiDto.setSpecification(spuPO.getSpecification());
                    // 销售单位
                    if (Objects.nonNull(spuPO.getSaleUnit())){
                        apiDto.setSaleUnit(extendInfoService.saleUnit(spuPO.getSaleUnit(), lang));
                        apiDto.setSaleUnitMap(extendInfoService.saleUnit(spuPO.getSaleUnit(), CollectionUtils.isNotEmpty(reqDTO.getLangSet())?reqDTO.getLangSet():Sets.newHashSet(LangConstant.LANG_ZH)));
                    }
                }
            });
        }
    }

    private void buildSkuFeature(QuerySkuReqDTO reqDTO, Map<String, SkuBaseInfoApiDTO> resultMap) {
        if (CollectionUtils.isNotEmpty(reqDTO.getSkuQueryEnums())
                && (reqDTO.getSkuQueryEnums().contains(SkuQueryEnum.ALL) || (reqDTO.getSkuQueryEnums().contains(SkuQueryEnum.FEATURE) || reqDTO.getSkuQueryEnums().contains(SkuQueryEnum.CUSTOMS)))) {
            Map<String, SkuFeatureApiDTO> featureApiDTOMap = this.getSkuFeatureBySku(reqDTO);
            if (MapUtils.isNotEmpty(featureApiDTOMap)) {
                resultMap.forEach((skuId, apiDto) -> {
                    apiDto.setSkuFeatureApiDTO(featureApiDTOMap.getOrDefault(skuId, null));
                });
            }
        }
    }

    /** 构建供应商信息*/
    private void buildSupplyInfo(QuerySkuReqDTO reqDTO, Map<String, SkuBaseInfoApiDTO> resultMap) {
        if (CollectionUtils.isNotEmpty(reqDTO.getSkuQueryEnums()) && (reqDTO.getSkuQueryEnums().contains(SkuQueryEnum.ALL) || reqDTO.getSkuQueryEnums().contains(SkuQueryEnum.SUPPLY_INFO))) {
            Map<String, VendorPO> crossVendorMap = new HashMap<>();
            Map<String, SupplierBaseInfoPO> localVendorMap = new HashMap<>();
            Set<String> crossVendorCode = new HashSet<>();
            Set<String> localVendorCode = new HashSet<>();
            List<SkuBaseInfoApiDTO> crossSku = resultMap.values().stream().filter(line -> CountryConstant.COUNTRY_ZH.equals(line.getSourceCountryCode())).collect(Collectors.toList());
            List<SkuBaseInfoApiDTO> localSku = resultMap.values().stream().filter(line -> !CountryConstant.COUNTRY_ZH.equals(line.getSourceCountryCode())).collect(Collectors.toList());
            //处理跨境供应商信息
            if(CollectionUtils.isNotEmpty(crossSku)){
                for(SkuBaseInfoApiDTO sku : crossSku){
                    if(StringUtils.isNotBlank(sku.getJdVendorCode())){
                        crossVendorCode.add(sku.getJdVendorCode());
                    }
                    if(StringUtils.isNotBlank(sku.getVendorCode())){
                        crossVendorCode.add(sku.getVendorCode());
                    }
                }
                if(CollectionUtils.isNotEmpty(crossVendorCode)){
                    crossVendorMap = vendorAtomicService.getCrossVendorMap(crossVendorCode);
                }
            }
            //处理本土供应商信息
            if(CollectionUtils.isNotEmpty(localSku)){
                for(SkuBaseInfoApiDTO sku : localSku){
                    if(StringUtils.isNotBlank(sku.getVendorCode())){
                        localVendorCode.add(sku.getVendorCode());
                    }
                }
                if(CollectionUtils.isNotEmpty(localVendorCode)){
                    localVendorMap = supplierAllInfoAtomicService.getLocalSupplierMap(localVendorCode);
                }
            }
            for (Map.Entry<String, SkuBaseInfoApiDTO> entry : resultMap.entrySet()) {
                SkuBaseInfoApiDTO sku = entry.getValue();
                //跨境品
                if(CountryConstant.COUNTRY_ZH.equals(sku.getSourceCountryCode())){
                    sku.setVendorInfo(new VendorInfoApiDTO(sku.getVendorCode(),crossVendorMap.getOrDefault(sku.getVendorCode(),new VendorPO()).getVendorName()));
                    sku.setJdVendorInfo(new VendorInfoApiDTO(sku.getJdVendorCode(),crossVendorMap.getOrDefault(sku.getJdVendorCode(),new VendorPO()).getVendorName()));
                }else {
                    //本土品
                    sku.setVendorInfo(new VendorInfoApiDTO(sku.getVendorCode(),localVendorMap.getOrDefault(sku.getVendorCode(),new SupplierBaseInfoPO()).getBusinessLicenseName()));
                }
            }
        }
    }

    private Map<Long, BrandVO> getBrandVOMap(QuerySkuReqDTO reqDTO, Map<Long, SkuPO> skuBaseMap) {
        if (MapUtils.isEmpty(skuBaseMap)) {
            return Collections.emptyMap();
        }
        Set<Long> brandIds = skuBaseMap.values().stream().map(SkuPO::getBrandId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(reqDTO.getLangSet())) {
            reqDTO.setLangSet(Sets.newHashSet(LangConstant.LANG_EN));
        }
        return brandOutService.batchQueryBrandNameByIdAndLangs(brandIds, reqDTO.getLangSet());
    }

    private SkuBaseInfoApiDTO getSkuBaseInfoApiDTO(SkuPO skuPo, Map<Long, SpuPO> spuMap, Map<Long, Map<String, SpuLangPO>> spuLangMap, Map<Long, BrandVO> brandMap) {
        SkuBaseInfoApiDTO apiDTO = SkuConvert.INSTANCE.po2BaseInfoApiDTO(skuPo);
        apiDTO.setCatId(skuPo.getJdCatId());
        apiDTO.setSupplierCode(skuPo.getVendorCode());
        SpuPO spuPO = spuMap.get(skuPo.getSpuId());
        if (Objects.nonNull(spuPO)) {
            // 商品状态和审核状态
            apiDTO.setSkuStatus(spuPO.getSpuStatus());
            apiDTO.setAuditStatus(spuPO.getAuditStatus());
            apiDTO.setBuyer(spuPO.getBuyer());
            apiDTO.setSystemCode(spuPO.getSystemCode());
        }
        if (StringUtils.isBlank(apiDTO.getMainImg())) {
            apiDTO.setMainImg(spuPO.getMainImg());
        }
        // 商品名称
        if (MapUtils.isNotEmpty(spuLangMap) && spuLangMap.containsKey(spuPO.getSpuId())) {
            Map<String, SpuLangPO> langPOMap = spuLangMap.getOrDefault(spuPO.getSpuId(), Maps.newHashMap());
            Map<String, String> skuNameMap = Maps.newHashMap();
            langPOMap.forEach((lang, spulangPo) -> skuNameMap.put(lang, spulangPo.getSpuTitle()));
            apiDTO.setSkuNameMap(skuNameMap);
        }
        // 品牌名称，默认是英文
        if (MapUtils.isNotEmpty(brandMap)) {
            BrandVO brandVO = brandMap.getOrDefault(spuPO.getBrandId(), new BrandVO());
            apiDTO.setBrandNameMap(Optional.ofNullable(brandVO.getLangList()).orElseGet(ArrayList::new)
                    .stream().collect(Collectors.toMap(BrandLangVO::getLang, BrandLangVO::getLangName)));
            ;
        }
        return apiDTO;
    }

    @Override
    public List<SkuPO> getSkuBaseInfoByJdSkuId(Set<Long> jdSkuId){
        return skuBaseMapper.selectList(new LambdaQueryWrapper<SkuPO>().in(SkuPO::getJdSkuId, jdSkuId).eq(SkuPO::getYn, YnEnum.YES.getCode()));
    }


    @Override
    public DataResponse<SkuPredictPriceResDTO> calculateBrPredictCountryCostPrice(SkuPredictPriceReqDTO query) {

        try {
            String sourceCountryCode = query.getSourceCountryCode();
            SkuPredictPriceResDTO result = skuTaxPriceManageService(sourceCountryCode).predictCountryCostPrice(query);
            log.info("calculateBrPredictCountryCostPrice ok, query={}, result={}", JSONObject.toJSONString(query), JSONObject.toJSONString(result));
            return DataResponse.success(result);
        } catch (Exception e) {
            log.error("calculateBrPredictCountryCostPrice error, query={}", JSONObject.toJSONString(query), e);
            if (e instanceof BizException) {
                return DataResponse.error(e.getMessage());
            }
            return DataResponse.error("calculateBrPredictCountryCostPrice error");
        }


    }

    /**
     * 根据商品ID列表获取商品信息
     * @param productIds 商品ID列表
     * @return 商品信息VO对象
     */
    @Override
    public ProductIdVO getProductInfoByIds(List<Long> productIds) {
        // 1. 创建返回对象
        ProductIdVO productIdVO = new ProductIdVO();
        // 2. 判空处理
        if (CollectionUtils.isEmpty(productIds)) {
            return productIdVO;
        }
        // 3. 创建四个集合
        List<Long> skuIds = new ArrayList<>();
        List<Long> spuIds = new ArrayList<>();
        List<Long> jdSkuIds = new ArrayList<>();
        List<Long> mkuIds = new ArrayList<>();
        // 4. 遍历处理
        for (Long productId : productIds) {
            if (productId == null) {
                continue;
            }
            // 转成字符串便于判断前缀
            String idStr = String.valueOf(productId);
            // 根据前缀分类
            if (idStr.startsWith("8")) {
                skuIds.add(productId);
            } else if (idStr.startsWith("2")) {
                spuIds.add(productId);
            } else if (idStr.startsWith("5")) {
                mkuIds.add(productId);
            } else if (idStr.startsWith("1")) {
                jdSkuIds.add(productId);
            }
        }
        if(CollectionUtils.isNotEmpty(skuIds)){
            ProductIdVO skuProduct = handleSkuIds(skuIds);
            handleProduct(productIdVO, skuProduct);
        }
        if(CollectionUtils.isNotEmpty(spuIds)){
            ProductIdVO spuProduct = handleSpuIds(spuIds);
            handleProduct(productIdVO, spuProduct);
        }
        if(CollectionUtils.isNotEmpty(jdSkuIds)){
            ProductIdVO jdSkuProduct = handleJdSkuIds(jdSkuIds);
            handleProduct(productIdVO, jdSkuProduct);
        }
        if(CollectionUtils.isNotEmpty(mkuIds)){
            ProductIdVO mkuProduct = handleMkuIds(mkuIds);
            handleProduct(productIdVO, mkuProduct);
        }
        return productIdVO;
    }

    private void handleProduct(ProductIdVO totalProduct,ProductIdVO productIdVO) {
        // 2. 安全地添加集合元素
        if (CollectionUtils.isNotEmpty(productIdVO.getSkuIds())) {
            totalProduct.getSkuIds().addAll(productIdVO.getSkuIds());
        }
        if (CollectionUtils.isNotEmpty(productIdVO.getSpuIds())) {
            totalProduct.getSpuIds().addAll(productIdVO.getSpuIds());
        }
        if (CollectionUtils.isNotEmpty(productIdVO.getJdSkuIds())) {
            totalProduct.getJdSkuIds().addAll(productIdVO.getJdSkuIds());
        }
        if (CollectionUtils.isNotEmpty(productIdVO.getMkuIds())) {
            totalProduct.getMkuIds().addAll(productIdVO.getMkuIds());
        }
    }

    private ProductIdVO handleSkuIds(List<Long> skuIds) {
        ProductIdVO productIdVO = new ProductIdVO();
        productIdVO.setSkuIds(skuIds);
        List<SkuPO> skuList = skuAtomicService.queryBySkuIds(new HashSet<>(skuIds));
        List<Long> spuIdList = skuList.stream().map(SkuPO::getSpuId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> jdSkuIdList = skuList.stream().map(SkuPO::getJdSkuId).filter(Objects::nonNull).collect(Collectors.toList());
        productIdVO.setSpuIds(spuIdList);
        productIdVO.setJdSkuIds(jdSkuIdList);
        List<MkuRelationPO> mkuRelationPOS = mkuRelationAtomicService.queryBindListBySkuIds(skuIds);
        if(CollectionUtils.isEmpty(mkuRelationPOS)){
            return productIdVO;
        }
        List<Long> mkuIdList = mkuRelationPOS.stream().map(MkuRelationPO::getMkuId).filter(Objects::nonNull).collect(Collectors.toList());
        productIdVO.setMkuIds(mkuIdList);
        return productIdVO;
    }

    private ProductIdVO handleJdSkuIds(List<Long> jdSkuIds) {
        ProductIdVO productIdVO = new ProductIdVO();
        productIdVO.setJdSkuIds(jdSkuIds);
        List<SkuPO> skuList = getSkuBaseInfoByJdSkuId(new HashSet<>(jdSkuIds));
        if(CollectionUtils.isEmpty(skuList)) {
            return productIdVO;
        }
        List<Long> skuIdList = skuList.stream().map(SkuPO::getSkuId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> spuIdList = skuList.stream().map(SkuPO::getSpuId).filter(Objects::nonNull).collect(Collectors.toList());
        productIdVO.setSkuIds(skuIdList);
        productIdVO.setSpuIds(spuIdList);
        List<MkuRelationPO> mkuRelationPOS = mkuRelationAtomicService.queryBindListBySkuIds(skuIdList);
        if(CollectionUtils.isEmpty(mkuRelationPOS)){
            return productIdVO;
        }
        List<Long> mkuIdList = mkuRelationPOS.stream().map(MkuRelationPO::getMkuId).filter(Objects::nonNull).collect(Collectors.toList());
        productIdVO.setMkuIds(mkuIdList);
        return productIdVO;
    }


    private ProductIdVO handleSpuIds(List<Long> spuIds) {
        ProductIdVO productIdVO = new ProductIdVO();
        productIdVO.setSpuIds(spuIds);
        List<SkuPO> skuList = getSkuListBySpuIds(new HashSet<>(spuIds));
        if(CollectionUtils.isEmpty(skuList)) {
            return productIdVO;
        }
        List<Long> skuIdList = skuList.stream().map(SkuPO::getSkuId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> jdSkuIdList = skuList.stream().map(SkuPO::getJdSkuId).filter(Objects::nonNull).collect(Collectors.toList());
        productIdVO.setSkuIds(skuIdList);
        productIdVO.setJdSkuIds(jdSkuIdList);
        List<MkuRelationPO> mkuRelationPOS = mkuRelationAtomicService.queryBindListBySkuIds(skuIdList);
        if(CollectionUtils.isEmpty(mkuRelationPOS)){
            return productIdVO;
        }
        List<Long> mkuIdList = mkuRelationPOS.stream().map(MkuRelationPO::getMkuId).filter(Objects::nonNull).collect(Collectors.toList());
        productIdVO.setMkuIds(mkuIdList);
        return productIdVO;
    }

    private ProductIdVO handleMkuIds(List<Long> mkuIds) {
        ProductIdVO productIdVO = new ProductIdVO();
        productIdVO.setMkuIds(mkuIds);
        List<MkuRelationPO> mkuRelationPOS = mkuRelationAtomicService.queryBindListByMkuIds(mkuIds);
        if(CollectionUtils.isEmpty(mkuRelationPOS)){
            return productIdVO;
        }
        List<Long> skuIdList = mkuRelationPOS.stream().map(MkuRelationPO::getSkuId).filter(Objects::nonNull).collect(Collectors.toList());
        productIdVO.setSkuIds(skuIdList);
        List<SkuPO> skuList = skuAtomicService.queryBySkuIds(new HashSet<>(skuIdList));
        if(CollectionUtils.isEmpty(skuList)) {
            return productIdVO;
        }
        List<Long> spuIdList = skuList.stream().map(SkuPO::getSpuId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> jdSkuIdList = skuList.stream().map(SkuPO::getJdSkuId).filter(Objects::nonNull).collect(Collectors.toList());
        productIdVO.setSpuIds(spuIdList);
        productIdVO.setJdSkuIds(jdSkuIdList);
        return productIdVO;
    }
}
