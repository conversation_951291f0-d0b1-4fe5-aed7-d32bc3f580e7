package com.jdi.isc.product.soa.service.manage.customerSku.approve;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.biz.component.api.enums.ApprovalOperateEnum;
import com.jdi.isc.biz.component.api.enums.JoySkyBizFlowTypeEnum;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyCancelApiDTO;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyCreateReqApiDTO;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyOperateApiDTO;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyQueryApiDTO;
import com.jdi.isc.biz.component.api.joysky.res.JoySkyCreateRspApiDTO;
import com.jdi.isc.biz.component.api.joysky.res.JoySkyDetailInfoDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.TaxConstant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.util.BigDecimalUtil;
import com.jdi.isc.product.soa.common.util.DateTimeUtils;
import com.jdi.isc.product.soa.domain.apply.po.ApplyInfoPO;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.*;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailDraftPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailPO;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.SkuScopeEnum;
import com.jdi.isc.product.soa.domain.enums.customerSku.CustomerSkuPriceCountryEnums;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.sku.biz.ExternalVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuExternalReqVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangVO;
import com.jdi.isc.product.soa.rpc.joySky.JoySkyApproveRpcService;
import com.jdi.isc.product.soa.service.atomic.apply.ApplyInfoAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceApproveService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuSalePriceManageService;
import com.jdi.isc.product.soa.service.manage.price.ProfitCalculateManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuExternalManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;


/**
 * 销价审批
 *
 * <AUTHOR>
 * @date 2024/11/25
 **/
@Slf4j
@Service
public abstract class AbstractCustomerSkuPriceApproveService implements CustomerSkuPriceApproveService {
    @Resource
    protected CustomerSkuPriceManageService customerSkuPriceManageService;
    @Resource
    protected CountryManageService countryManageService;
    @Resource
    protected JoySkyApproveRpcService joySkyApproveRpcService;
    @Resource
    protected SkuExternalManageService skuExternalManageService;
    @Resource
    protected CustomerManageService customerManageService;
    @Resource
    @Lazy
    private Map<String, CustomerSkuSalePriceManageService> customerSkuSalePriceManageServiceMap;
    @Resource
    protected ApplyInfoAtomicService applyInfoAtomicService;
    @Resource
    @Lazy
    protected CustomerSkuPriceDetailDraftAtomicService customerSkuPriceDetailDraftAtomicService;
    @Resource
    private OperDuccConfig operDuccConfig;

    /** 对应的joySky审批流 */
    protected static final JoySkyBizFlowTypeEnum flowTypeEnum = JoySkyBizFlowTypeEnum.PRODUCT_SALE_PRICE_FLOW;
    @Value("${spring.profiles.active}")
    protected String systemProfile;


    @Override
    public SalePriceCalculateResVO getCalculateResVO(CustomerSkuPriceDraftVO draftVO) {
        SalePriceCalculateVO calculateVO = new SalePriceCalculateVO();
        calculateVO.setClientCode(draftVO.getClientCode());
        calculateVO.setSkuId(draftVO.getSkuId());
        calculateVO.setSalePrice(draftVO.getCustomerSalePrice());

        SalePriceCalculateResVO calculateResVO = this.getCustomerSkuPriceManageService(draftVO.getTargetCountryCode()).calculateCustomerSalePrice(calculateVO);
        if(calculateResVO.getSalePrice() == null){
            throw new BizException("客制化未税销售价为空");
        }
        if(calculateResVO.getProfitLimitRate() == null){
            throw new BizException("利润率阈值为空");
        }
        if(calculateResVO.getLowProfitLimitRate() == null){
            throw new BizException("超低利润率阈值为空");
        }
        if(calculateResVO.getCountryCostPrice() == null){
            throw new BizException("国家成本价为空");
        }
        if(calculateResVO.getProfitRate() == null){
            throw new BizException("利润率为空");
        }
        if(calculateResVO.getJdPrice() == null){
            throw new BizException("京东价为空");
        }
        if(draftVO.getCustomerSalePrice().compareTo(calculateResVO.getJdPrice())>0){
            throw new BizException("客制化销售价不能超过京东价");
        }

        return calculateResVO;
    }
    
    @Override
    public void setSubmitAuditStatus(CustomerSkuPriceDraftVO input) {
        input.setAuditStatus(AuditStatusEnum.WAITING_APPROVED.getCode());
        input.setLevel(2);
    }
    
    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public String batchSubmit(CustomerSkuPriceDraftVO input) {
        log.info("AbstractCustomerSkuPriceApproveService.batchSubmit input:{}", JSONObject.toJSONString(input));
        JoySkyCreateReqApiDTO joySkyCreateReqApiDTO = new JoySkyCreateReqApiDTO();
        joySkyCreateReqApiDTO.setErp(StringUtils.isNotBlank(input.getUpdater())?input.getUpdater(): LoginContextHolder.getLoginContextHolder().getPin());
        joySkyCreateReqApiDTO.setJoySkyFlowType(flowTypeEnum.getFlowTypeCode());
        Map<String, String> processFormDataModelMap = this.constructProcessFormDataModel(input);
        joySkyCreateReqApiDTO.setProcessFromData(processFormDataModelMap);
        joySkyCreateReqApiDTO.setProcessControlData(this.constructProcessControlData(input.getSourceCountryCode(),input.getTargetCountryCode()));

        // 审批流流程表单数据 增加附件，附件比较特殊，需要单独处理
        Map<String/*文件名*/, String/*url*/> attachmentMap = com.jdi.isc.product.soa.common.util.StringUtils.getAttachmentMap(input.getAttachmentUrls());
        joySkyCreateReqApiDTO.addAttachment("附件", attachmentMap);
        // 多附件
        joySkyCreateReqApiDTO.setMultiFile(true);

        // 更新表单数据
        customerSkuPriceDetailDraftAtomicService.updateProcessFromData(input.getId(), joySkyCreateReqApiDTO.getProcessFromData());

        JoySkyCreateRspApiDTO joySkyCreateRspApiDTO = joySkyApproveRpcService.createSkyApprovalFlow(joySkyCreateReqApiDTO);
        JoySkyDetailInfoDTO joySkyDetailInfo = this.getJoySkyDetailInfo(joySkyCreateRspApiDTO.getProcessInstanceId(),Boolean.FALSE,Boolean.TRUE);

        ApplyInfoPO dbApplyInfoPO = applyInfoAtomicService.getByBizTypeAndBizId(flowTypeEnum,input.getId());
        ApplyInfoPO applyInfoPO = null;
        if(dbApplyInfoPO != null){
            applyInfoPO = this.getUpdateApplyInfo(dbApplyInfoPO.getId()
                    ,AuditStatusEnum.WAITING_APPROVED.getCode(), LoginContextHolder.getLoginContextHolder().getPin(),dbApplyInfoPO.getVersion()
                    ,LoginContextHolder.getLoginContextHolder().getPin());
            applyInfoPO.setProcessInstanceId(joySkyDetailInfo.getProcessInstanceId());
            applyInfoPO.setApplyCode(joySkyDetailInfo.getApplyCode());
            applyInfoAtomicService.update(applyInfoPO,dbApplyInfoPO.getVersion());
        }else{
            applyInfoPO = this.initApplyInfo(input.getId(),joySkyDetailInfo);
            applyInfoAtomicService.save(applyInfoPO);
        }
        return joySkyDetailInfo.getProcessInstanceId();
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean messageApprove(CustomerSkuAuditVO input) {
        log.info("AbstractCustomerSkuPriceApproveService.messageApprove input:{}", JSONObject.toJSONString(input));
        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(flowTypeEnum,input.getProcessInstanceId());
        if(applyInfoPO == null || applyInfoPO.getAuditStatus() == null || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.error("messageApprove.当前记录非待审核状态，不允许重复审批，processInstanceId:{}", input.getProcessInstanceId());
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化审批消息通过异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "applyInfoPO status is error"));
            return Boolean.FALSE;
        }

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId(),AuditStatusEnum.APPROVED.getCode(), null,applyInfoPO.getVersion(),input.getAuditErp());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
        CustomerSkuPriceDetailDraftPO detailDraftPO = customerSkuPriceDetailDraftAtomicService.getValidById(Long.valueOf(applyInfoPO.getBizId()));

        if(detailDraftPO == null){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化审批消息通过异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "draftPO is null"));
            return Boolean.FALSE;
        }

        CustomerSkuPriceDetailDraftPO updateDraftPO = this.getUpdateDetailDraftPO(detailDraftPO,AuditStatusEnum.APPROVED,detailDraftPO.getUpdater(),input.getAuditErp(),input.getRejectReason());
        customerSkuPriceDetailDraftAtomicService.saveOrUpdate(updateDraftPO);
        customerSkuPriceManageService.saveOrUpdate(this.getCustomerSkuUpdate(detailDraftPO));

        log.info("AbstractCustomerSkuPriceApproveService.messageApprove " +
                        "pin:{},clientCode:{},skuId:{},salePrice:{}",input.getAuditErp()
                ,detailDraftPO.getClientCode(),detailDraftPO.getSkuId(),detailDraftPO.getCustomerSalePrice());
        return Boolean.TRUE;

    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean messageReject(CustomerSkuAuditVO input) {
        log.info("AbstractCustomerSkuPriceApproveService.messageReject input:{}", JSONObject.toJSONString(input));
        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(flowTypeEnum,input.getProcessInstanceId());
        if(applyInfoPO == null || applyInfoPO.getAuditStatus() == null || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.error("messageReject.当前记录非待审核状态，不允许重复审批，processInstanceId:{}", input.getProcessInstanceId());
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化审批消息驳回异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "applyInfoPO status is error"));
            return Boolean.FALSE;
        }

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId(),AuditStatusEnum.REJECTED.getCode(), null,applyInfoPO.getVersion(),input.getAuditErp());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
        CustomerSkuPriceDetailDraftPO detailDraftPO = customerSkuPriceDetailDraftAtomicService.getValidById(Long.valueOf(applyInfoPO.getBizId()));

        if(detailDraftPO == null){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化审批消息驳回异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "draftPO is null"));
            return Boolean.FALSE;
        }

        CustomerSkuPriceDetailDraftPO updateDraftPO = this.getUpdateDetailDraftPO(detailDraftPO,AuditStatusEnum.REJECTED,detailDraftPO.getUpdater(),input.getAuditErp(),input.getRejectReason());
        customerSkuPriceDetailDraftAtomicService.saveOrUpdate(updateDraftPO);

        log.info("AbstractCustomerSkuPriceApproveService.messageReject " +
                        "pin:{},clientCode:{},skuId:{},salePrice:{}",input.getAuditErp()
                ,updateDraftPO.getClientCode(),updateDraftPO.getSkuId(),updateDraftPO.getCustomerSalePrice());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean messageRevoke(CustomerSkuAuditVO input) {
        log.info("AbstractCustomerSkuPriceApproveService.messageRevoke input:{}", JSONObject.toJSONString(input));
        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(flowTypeEnum,input.getProcessInstanceId());
        if(applyInfoPO == null || applyInfoPO.getAuditStatus() == null || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.error("messageRevoke.当前记录非待审核状态，不允许重复审批，processInstanceId:{}", input.getProcessInstanceId());
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化审批消息撤回异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "applyInfoPO status is error"));
            return Boolean.FALSE;
        }

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId(),AuditStatusEnum.DRAFT.getCode(), null,applyInfoPO.getVersion(),input.getAuditErp());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
        CustomerSkuPriceDetailDraftPO detailDraftPO = customerSkuPriceDetailDraftAtomicService.getValidById(Long.valueOf(applyInfoPO.getBizId()));

        if(detailDraftPO == null){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化审批消息撤回异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "draftPO is null"));
            return Boolean.FALSE;
        }

        CustomerSkuPriceDetailDraftPO updateDraftPO = this.getUpdateDetailDraftPO(detailDraftPO,AuditStatusEnum.DRAFT,detailDraftPO.getUpdater(),input.getAuditErp(),input.getRejectReason());
        customerSkuPriceDetailDraftAtomicService.saveOrUpdate(updateDraftPO);

        log.info("AbstractCustomerSkuPriceApproveService.messageRevoke " +
                        "pin:{},clientCode:{},skuId:{},salePrice:{}",input.getAuditErp()
                ,detailDraftPO.getClientCode(),detailDraftPO.getSkuId(),detailDraftPO.getCustomerSalePrice());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean messageApproving(CustomerSkuAuditVO input) {
        log.info("AbstractCustomerSkuPriceApproveService.messageApproving input:{}", JSONObject.toJSONString(input));
        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(flowTypeEnum,input.getProcessInstanceId());
        if(applyInfoPO == null){
            log.error("messageApproving.当前记录不存在，processInstanceId:{}", input.getProcessInstanceId());
            return Boolean.FALSE;
        }

        if(applyInfoPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.error("messageApproving.当前记录非待审核状态，不允许重复审批，processInstanceId:{}", input.getProcessInstanceId());
            return Boolean.FALSE;
        }

        JoySkyDetailInfoDTO joySkyDetailInfo = this.getJoySkyDetailInfo(applyInfoPO.getProcessInstanceId(),Boolean.FALSE,Boolean.TRUE);
        String currentAuditor = null;
        if(CollectionUtils.isNotEmpty(joySkyDetailInfo.getTodoList())){
            currentAuditor = String.join(",", joySkyDetailInfo.getTodoList());
        }
        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId(),AuditStatusEnum.WAITING_APPROVED.getCode(), currentAuditor,applyInfoPO.getVersion(),input.getAuditErp());

        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
        log.info("AbstractCustomerSkuPriceApproveService.messageApproving " +
                "processInstanceId:{},auditErp:{}",input.getProcessInstanceId(),input.getAuditErp());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean singleApprove(CustomerSkuPriceDetailDraftPO detailDraftPO,CustomerSkuAuditVO input,ApplyInfoPO applyInfoPO) {
        log.info("AbstractCustomerSkuPriceApproveService.singleApprove start detailDraftPO:{},input:{},applyInfoPO:{}",JSONObject.toJSONString(detailDraftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));

        if(applyInfoPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        if(detailDraftPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != detailDraftPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        Boolean flag = this.operateJoySky(ApprovalOperateEnum.AGREE,applyInfoPO.getProcessInstanceId()
                ,input.getRejectReason(),LoginContextHolder.getLoginContextHolder().getPin());

        if(!flag){
            throw new BizException("审批通过失败");
        }
        JoySkyDetailInfoDTO joySkyDetailInfo = this.getJoySkyDetailInfo(applyInfoPO.getProcessInstanceId(),Boolean.FALSE,Boolean.TRUE);
        if(joySkyDetailInfo != null && joySkyDetailInfo.getAuditStatus().equals(AuditStatusEnum.APPROVING.getCode())){
            String currentAuditor = null;
            if(CollectionUtils.isNotEmpty(joySkyDetailInfo.getTodoList())){
                currentAuditor = String.join(",", joySkyDetailInfo.getTodoList());
            }
            ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId()
                    ,AuditStatusEnum.WAITING_APPROVED.getCode(), currentAuditor ,applyInfoPO.getVersion()
                    ,LoginContextHolder.getLoginContextHolder().getPin());
            applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
            log.info("AbstractCustomerSkuPriceApproveService.singleApprove 流程未结束 detailDraftPO:{},input:{},applyInfo:{}",JSONObject.toJSONString(detailDraftPO)
                    ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));
            return Boolean.TRUE;
        }

        CustomerSkuPriceDetailDraftPO updatePO = this.getUpdateDetailDraftPO(detailDraftPO,AuditStatusEnum.APPROVED
                ,detailDraftPO.getUpdater(),LoginContextHolder.getLoginContextHolder().getPin(), input.getRejectReason());
        customerSkuPriceDetailDraftAtomicService.saveOrUpdate(updatePO);
        customerSkuPriceManageService.saveOrUpdate(this.getCustomerSkuUpdate(detailDraftPO));

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId()
                ,AuditStatusEnum.APPROVED.getCode(), LoginContextHolder.getLoginContextHolder().getPin(),applyInfoPO.getVersion()
                ,LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());

        log.info("AbstractCustomerSkuPriceApproveService.singleApprove end detailDraftPO:{},input:{},applyInfoMap:{}",JSONObject.toJSONString(detailDraftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean singleReject(CustomerSkuPriceDetailDraftPO detailDraftPO,CustomerSkuAuditVO input,ApplyInfoPO applyInfoPO) {
        log.info("AbstractCustomerSkuPriceApproveService.singleReject start detailDraftPO:{},input:{},applyInfoPO:{}",JSONObject.toJSONString(detailDraftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));

        if(applyInfoPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        if(detailDraftPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != detailDraftPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        CustomerSkuPriceDetailDraftPO updatePO = this.getUpdateDetailDraftPO(detailDraftPO,AuditStatusEnum.REJECTED
                ,detailDraftPO.getUpdater(),LoginContextHolder.getLoginContextHolder().getPin(), input.getRejectReason());
        customerSkuPriceDetailDraftAtomicService.saveOrUpdate(updatePO);

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId()
                ,AuditStatusEnum.REJECTED.getCode(), LoginContextHolder.getLoginContextHolder().getPin(),applyInfoPO.getVersion()
                ,LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());

        this.operateJoySky(ApprovalOperateEnum.REJECT_TO_SUBMIT_NODE,applyInfoPO.getProcessInstanceId()
                ,input.getRejectReason(),LoginContextHolder.getLoginContextHolder().getPin());

        log.info("AbstractCustomerSkuPriceApproveService.singleReject end detailDraftPO:{},input:{},applyInfoMap:{}",JSONObject.toJSONString(detailDraftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean singleRevoke(CustomerSkuPriceDetailDraftPO detailDraftPO,CustomerSkuAuditVO input,ApplyInfoPO applyInfoPO) {
        log.info("AbstractCustomerSkuPriceApproveService.singleRevoke start detailDraftPO:{},input:{},applyInfoPO:{}",JSONObject.toJSONString(detailDraftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));

        if(applyInfoPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        if(detailDraftPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != detailDraftPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        CustomerSkuPriceDetailDraftPO updatePO = this.getUpdateDetailDraftPO(detailDraftPO,AuditStatusEnum.DRAFT
                ,detailDraftPO.getUpdater(),null, input.getRejectReason());
        customerSkuPriceDetailDraftAtomicService.saveOrUpdate(updatePO);

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId()
                ,AuditStatusEnum.DRAFT.getCode(), LoginContextHolder.getLoginContextHolder().getPin(),applyInfoPO.getVersion()
                ,LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
        String processInstanceId = applyInfoPO.getProcessInstanceId();

        this.cancelJoySky(processInstanceId,detailDraftPO.getUpdater());

        log.info("AbstractCustomerSkuPriceApproveService.singleRevoke end detailDraftPO:{},input:{},processInstanceId:{}",JSONObject.toJSONString(detailDraftPO)
                ,JSONObject.toJSONString(input),processInstanceId);
        return Boolean.TRUE;
    }

    /**
     * 根据 CustomerSkuPriceDraftPO 对象创建并返回一个新的 CustomerSkuPriceVO 对象。
     * @param detailDraftPO CustomerSkuPriceDraftPO 对象，包含要设置到 CustomerSkuPriceVO 对象的属性信息。
     * @return 一个新的 CustomerSkuPriceVO 对象，包含从 draftPO 对象中获取的属性值。
     */
    public CustomerSkuPriceVO getCustomerSkuUpdate(CustomerSkuPriceDetailDraftPO detailDraftPO){
        CustomerSkuPriceVO customerSkuPriceVO = new CustomerSkuPriceVO();
        customerSkuPriceVO.setId(detailDraftPO.getId());
        customerSkuPriceVO.setSkuId(detailDraftPO.getSkuId());
        customerSkuPriceVO.setClientCode(detailDraftPO.getClientCode());
        customerSkuPriceVO.setCurrency(detailDraftPO.getCurrency());
        customerSkuPriceVO.setCustomerTradeType(detailDraftPO.getCustomerTradeType());
        customerSkuPriceVO.setCustomerSalePrice(detailDraftPO.getCustomerSalePrice());
        customerSkuPriceVO.setAuditor(detailDraftPO.getAuditor());
        return customerSkuPriceVO;
    }

    /**
     * 根据国家代码获取对应的客户 SKU 价格管理服务。
     * @param countryCode 国家代码
     * @return 对应的客户 SKU 价格管理服务，若国家代码为空则返回 null
     */
    protected CustomerSkuSalePriceManageService getCustomerSkuPriceManageService(String countryCode){
        if(StringUtils.isBlank(countryCode)){
            return null;
        }
        return customerSkuSalePriceManageServiceMap.get(CustomerSkuPriceCountryEnums.getEnumByCountryCode(countryCode).getServiceName());
    }

    /**
     * 获取审批流程详细信息。
     * @param processInstanceId 流程实例ID。
     * @param subData 是否需要子表数据。
     * @param todoList 是否需要待办列表。
     * @return 审批流程详细信息DTO。
     */
    protected JoySkyDetailInfoDTO getJoySkyDetailInfo(String processInstanceId
            ,Boolean subData,Boolean todoList){
        // 审批通过获取回填数据
        JoySkyQueryApiDTO joySkyQueryApiDTO = new JoySkyQueryApiDTO();
        joySkyQueryApiDTO.setJoySkyFlowType(flowTypeEnum.getFlowTypeCode());
        joySkyQueryApiDTO.setProcessInstanceId(processInstanceId);
        joySkyQueryApiDTO.setNeedSubTableData(subData);
        joySkyQueryApiDTO.setNeedTodoList(todoList);
        JoySkyDetailInfoDTO joySkyDetailInfoDTO = joySkyApproveRpcService.queryFlowModel(joySkyQueryApiDTO);
        return joySkyDetailInfoDTO;
    }

    protected ApplyInfoPO initApplyInfo(Long id,JoySkyDetailInfoDTO joySkyDetailInfoDTO){
        ApplyInfoPO applyInfoPO = new ApplyInfoPO();
        applyInfoPO.setProcessType(flowTypeEnum.getFlowTypeCode());
        applyInfoPO.setBizId(id.toString());
        applyInfoPO.setProcessInstanceId(joySkyDetailInfoDTO.getProcessInstanceId());
        applyInfoPO.setApplyCode(joySkyDetailInfoDTO.getApplyCode());
        applyInfoPO.setAuditStatus(AuditStatusEnum.WAITING_APPROVED.getCode());
        if(CollectionUtils.isNotEmpty(joySkyDetailInfoDTO.getTodoList())){
            String currentAuditor = String.join(",", joySkyDetailInfoDTO.getTodoList());
            applyInfoPO.setCurrentAuditor(currentAuditor);
        }
        applyInfoPO.setVersion(0);
        applyInfoPO.setCreator(LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoPO.setCreateTime(new Date().getTime());
        applyInfoPO.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoPO.setUpdateTime(new Date().getTime());
        return applyInfoPO;
    }

    /**
     * 更新申请信息
     * @param id 申请信息ID
     * @param auditStatus 审核状态
     * @param currentAuditor 当前审核人
     * @param version 版本号
     * @param updater 更新人
     * @return 更新后的申请信息对象
     */
    protected ApplyInfoPO getUpdateApplyInfo(Long id,Integer auditStatus,String currentAuditor,Integer version,String updater){
        ApplyInfoPO updateApplyInfoPO = new ApplyInfoPO();
        updateApplyInfoPO.setId(id);
        updateApplyInfoPO.setAuditStatus(auditStatus);
        updateApplyInfoPO.setCurrentAuditor(currentAuditor);
        updateApplyInfoPO.setUpdater(updater);
        updateApplyInfoPO.setVersion(version+1);
        return updateApplyInfoPO;
    }

    /**
     * 执行JoySky审批操作
     * @param operateEnum 审批操作枚举
     * @param processInstanceId 流程实例ID
     * @param comment 审批备注
     * @param erp ERP系统信息
     * @return 操作结果
     */
    protected Boolean operateJoySky(ApprovalOperateEnum operateEnum,String processInstanceId,String comment,String erp){
        JoySkyOperateApiDTO joySkyOperateApiDTO = new JoySkyOperateApiDTO();
        joySkyOperateApiDTO.setJoySkyFlowType(flowTypeEnum.getFlowTypeCode());
        joySkyOperateApiDTO.setOperateEnum(operateEnum);
        joySkyOperateApiDTO.setProcessInstanceId(processInstanceId);
        joySkyOperateApiDTO.setComment(comment);
        joySkyOperateApiDTO.setErp(erp);
        return joySkyApproveRpcService.operateSky(joySkyOperateApiDTO);
    }

    /**
     * 取消产品销售价格审批流程
     * @param processInstanceId 流程实例ID
     * @param erp ERP系统标识
     * @return 取消结果
     */
    protected Boolean cancelJoySky(String processInstanceId,String erp){
        JoySkyCancelApiDTO joySkyCancelApiDTO = new JoySkyCancelApiDTO();
        joySkyCancelApiDTO.setErp(erp);
        joySkyCancelApiDTO.setJoySkyFlowType(JoySkyBizFlowTypeEnum.PRODUCT_SALE_PRICE_FLOW.getFlowTypeCode());
        joySkyCancelApiDTO.setCancelComment("用户主动取消");
        joySkyCancelApiDTO.setProcessInstanceId(processInstanceId);
        return joySkyApproveRpcService.cancelSkyApprovalFlow(joySkyCancelApiDTO);
    }

    /**
     * 根据给定的参数更新 CustomerSkuPriceDetailDraftPO 对象并返回。
     * @param detailDraftPO 要更新的 CustomerSkuPriceDetailDraftPO 对象。
     * @param auditStatusEnum 审核状态枚举值。
     * @param updater 更新人。
     * @param auditErp 审核ERP。
     * @param rejectReason 拒绝原因。
     * @return 更新后的 CustomerSkuPriceDetailDraftPO 对象。
     */
    protected CustomerSkuPriceDetailDraftPO getUpdateDetailDraftPO(CustomerSkuPriceDetailDraftPO detailDraftPO
            , AuditStatusEnum auditStatusEnum, String updater, String auditErp, String rejectReason){
        CustomerSkuPriceDetailDraftPO updatePO = new CustomerSkuPriceDetailDraftPO();
        updatePO.setId(detailDraftPO.getId());
        updatePO.setAuditStatus(auditStatusEnum.getCode());
        updatePO.setAuditor(auditErp);
        updatePO.setUpdater(updater);
        updatePO.setUpdateTime(new Date().getTime());
        updatePO.setRejectReason(rejectReason);
        return updatePO;
    }

    /**
     * 获取有效时间范围。
     * @param beginTime 开始时间（毫秒）
     * @param endTime 结束时间（毫秒）
     * @return 格式化后的有效时间范围，若开始时间或结束时间为空则返回"永久有效"
     */
//    protected String getEffectiveTime(Long beginTime, Long endTime, String countryCode){
//        if(StringUtils.isBlank(countryCode)){
//            return "";
//        }
//        ZoneOffset zoneOffset = DateTimeUtils.countryZoneOffsetMap.get(countryCode);
//
//        if (Constant.ZERO.equals(beginTime) && Constant.MAX.equals(endTime) ) {
//            return TaxConstant.END_TIME_NONE;
//        }
//        if (Constant.ZERO.equals(beginTime)) {
//            return TaxConstant.START_TIME_NONE +  Constant.MINUS + DateTimeUtils.milliToFmtSeconds(endTime,zoneOffset);
//        }
//        if(Constant.MAX.equals(endTime)){
//            return DateTimeUtils.milliToFmtSeconds(beginTime,zoneOffset) + Constant.MINUS + TaxConstant.END_TIME_NONE;
//        }
//        return DateTimeUtils.milliToFmtSeconds(beginTime,zoneOffset) +  Constant.MINUS + DateTimeUtils.milliToFmtSeconds(endTime,zoneOffset);
//    }

    @Resource
    private CustomerSkuPriceDetailAtomicService customerSkuPriceDetailAtomicService;

    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;
    
    /**
     * 构建客户化产品价格表单数据模型。
     * @param draftVO 客户化产品价格表单数据模型草稿对象，包含客户代码、SKU ID、客户交易类型、货币代码等信息。
     * @return 构建好的客户化产品价格表单数据模型，包含国际SKUID、商品名称、客户名称、采购价、客制化销售价、面客未税价、利润率、较低协议价、当前原客制化销售价、提交人、利润率预警信息、利润率阈值、超低利润率阈值、备注、客户国家、生效时间-北京时间、生效时间-当地时间等字段。
     */
    private Map<String, String> constructProcessFormDataModel(CustomerSkuPriceDraftVO draftVO){
        CustomerVO customer = customerManageService.detail(draftVO.getClientCode());
        SalePriceCalculateResVO calculateResVO = draftVO.getCalculateResVO();
        if(calculateResVO.getSalePrice() == null){
            throw new BizException("客制化未税销售价为空");
        }
        if(calculateResVO.getProfitLimitRate() == null){
            throw new BizException("利润率阈值为空");
        }
        if(calculateResVO.getLowProfitLimitRate() == null){
            throw new BizException("超低利润率阈值为空");
        }
        if(calculateResVO.getCountryCostPrice() == null){
            throw new BizException("国家成本价为空");
        }
        if(calculateResVO.getProfitRate() == null){
            throw new BizException("利润率为空");
        }
        CustomerSkuPriceDetailDraftPO customerSkuPriceDetailDraftPO = customerSkuPriceDetailDraftAtomicService.getValidById(draftVO.getId());


        Map<String, String> fieldCommentToFieldNameMap = new HashMap<>(9);
        fieldCommentToFieldNameMap.put("国际SKUID", customerSkuPriceDetailDraftPO.getSkuId().toString());
        fieldCommentToFieldNameMap.put("商品名称", this.querySkuName(customerSkuPriceDetailDraftPO.getSkuId()));
        fieldCommentToFieldNameMap.put("客户名称", customer.getClientName());
        fieldCommentToFieldNameMap.put("客制化销售价", customerSkuPriceDetailDraftPO.getCustomerSalePrice().stripTrailingZeros().toPlainString() + customerSkuPriceDetailDraftPO.getCurrency());
        fieldCommentToFieldNameMap.put("利润率", calculateResVO.getProfitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        fieldCommentToFieldNameMap.put("国家成本价", calculateResVO.getCountryCostPrice().stripTrailingZeros().toPlainString() + customerSkuPriceDetailDraftPO.getCurrency());
        fieldCommentToFieldNameMap.put("提交人", LoginContextHolder.getLoginContextHolder().getPin());
        fieldCommentToFieldNameMap.put("利润率预警信息",calculateResVO.getWarningMsg());
        fieldCommentToFieldNameMap.put("利润率阈值", calculateResVO.getProfitLimitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        fieldCommentToFieldNameMap.put("超低利润率阈值", calculateResVO.getLowProfitLimitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        fieldCommentToFieldNameMap.put("备注", draftVO.getRemark());

        fieldCommentToFieldNameMap.put("客户国家", countryManageService.getCountryNameByCountryCode(customer.getCountry(), LangConstant.LANG_ZH));
        fieldCommentToFieldNameMap.put("生效时间-北京时间", CustomerSkuPriceAtomicService.getEffectiveTime(customerSkuPriceDetailDraftPO.getBeginTime(),customerSkuPriceDetailDraftPO.getEndTime(), CountryConstant.COUNTRY_ZH));
        fieldCommentToFieldNameMap.put("生效时间-当地时间", CustomerSkuPriceAtomicService.getEffectiveTime(customerSkuPriceDetailDraftPO.getBeginTime(),customerSkuPriceDetailDraftPO.getEndTime(),customer.getCountry()));


        // 查询变化前(vip价)
        CustomerSkuPriceDetailPO customerSkuPrice = customerSkuPriceDetailAtomicService.getValidBySourceId(draftVO.getId());
        // 查询成本价
        CountryAgreementPricePO countryAgreementPrice = countryAgreementPriceAtomicService.getBySkuIdAndTargetCountryCode(draftVO.getSkuId(), draftVO.getTargetCountryCode());

        // 客制化销售价（调价后）
        fieldCommentToFieldNameMap.put("客制化销售价（调价后）", customerSkuPriceDetailDraftPO.getCustomerSalePrice().stripTrailingZeros().toPlainString() + customerSkuPriceDetailDraftPO.getCurrency());
        // 利润率-前毛（调价后）
        fieldCommentToFieldNameMap.put("利润率-前毛（调价后）", calculateResVO.getProfitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        // 客制化销售价调整幅度（调价前后对比）= 客制化销售价（调价后）/ 客制化销售价（调价前） - 1，两位四舍五入，15.65%百分比
        fieldCommentToFieldNameMap.put("客制化销售价调整幅度（调价前后对比）", this.getCustomerPriceAdjustment(customerSkuPrice == null ? null : customerSkuPrice.getCustomerSalePrice(), customerSkuPriceDetailDraftPO.getCustomerSalePrice()));

        // 客制化销售价（调价前）
        fieldCommentToFieldNameMap.put("客制化销售价（调价前）", customerSkuPrice == null ? StringUtils.EMPTY : customerSkuPrice.getCustomerSalePrice().stripTrailingZeros().toPlainString()  + customerSkuPriceDetailDraftPO.getCurrency());
        // 利润率-前毛（调价前）
        fieldCommentToFieldNameMap.put("利润率-前毛（调价前）", this.calculateSalePriceProfitRate(customerSkuPrice, countryAgreementPrice));
        // 调价原因
        fieldCommentToFieldNameMap.put("调价原因", draftVO.getAdjustmentPriceReason());
        // 调价附件
        fieldCommentToFieldNameMap.put("调价附件", com.jdi.isc.product.soa.common.util.StringUtils.listToString(draftVO.getAttachmentUrls()));
        // 边际负毛阈值
        fieldCommentToFieldNameMap.put("边际负毛阈值", operDuccConfig.getGrossProfit(draftVO.getTargetCountryCode()).multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());

        // 去掉前毛 http://jagile.jd.com/demands/view/LPRZMTB2?demandId=3423569
        fieldCommentToFieldNameMap.put("利润率（调价后）", calculateResVO.getProfitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        fieldCommentToFieldNameMap.put("利润率（调价前）", this.calculateSalePriceProfitRate(customerSkuPrice, countryAgreementPrice));

        return fieldCommentToFieldNameMap;
    }

    private String calculateSalePriceProfitRate(CustomerSkuPriceDetailPO customerSkuPrice, CountryAgreementPricePO countryAgreementPrice) {

        if (customerSkuPrice == null || customerSkuPrice.getCustomerSalePrice() == null || BigDecimalUtil.eq0(customerSkuPrice.getCustomerSalePrice())) {
            return StringUtils.EMPTY;
        }

        if (countryAgreementPrice == null || countryAgreementPrice.getCountryCostPrice() == null || BigDecimalUtil.eq0(countryAgreementPrice.getCountryCostPrice())) {
            return StringUtils.EMPTY;
        }

        return SpringUtil.getBean(ProfitCalculateManageService.class).calculateSalePriceProfitRate(customerSkuPrice.getCustomerSalePrice(), countryAgreementPrice.getCountryCostPrice())
                .multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString();
    }



    private String getCustomerPriceAdjustment(BigDecimal originCustomerSalePrice, BigDecimal customerSalePrice) {

        if (originCustomerSalePrice == null || customerSalePrice == null) {
            return StringUtils.EMPTY;
        }

        if (originCustomerSalePrice.compareTo(BigDecimal.ZERO) == 0) {
            return StringUtils.EMPTY;
        }

        return customerSalePrice
                .divide(originCustomerSalePrice, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.ONE)
                .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toPlainString();
    }

    /**
     * 构建流程控制数据。
     * @param sourceCountryCode 商品来源国代码
     * @param targetCountryCode 客户所在国代码
     * @return 流程控制参数映射
     */
    private Map<String, Object> constructProcessControlData(String sourceCountryCode,String targetCountryCode) {
        //流程控制参数校验
        if (StringUtils.isBlank(sourceCountryCode) || StringUtils.isBlank(targetCountryCode)) {
            throw new BizException("流程控制参数不能为空!,商品来源国:"+sourceCountryCode+",客户所在国:" + targetCountryCode);
        }

        //流程参数赋值
        Map<String, Object> flowCalculeVarMap = new HashMap<>(3);
        flowCalculeVarMap.put("productCountry", sourceCountryCode);
        flowCalculeVarMap.put("customerCountry", targetCountryCode);
        return flowCalculeVarMap;
    }

    /**
     * 根据 SKU ID 查询商品标题信息。
     * @param skuId SKU 的唯一标识符。
     * @return 中文商品标题，若查询不到则返回 null。
     */
    private String querySkuName(Long skuId) {
        SkuExternalReqVO skuQuery = new SkuExternalReqVO();
        skuQuery.setSkuIds(Lists.newArrayList(skuId));
        skuQuery.setScopeEnums(Sets.newHashSet(SkuScopeEnum.BASE, SkuScopeEnum.LANG));

        Map<Long,ExternalVO> skuExternalVOMap = skuExternalManageService.querySkuInfo(skuQuery);
        if(MapUtils.isEmpty(skuExternalVOMap)){
            return null;
        }
        ExternalVO externalVO = skuExternalVOMap.get(skuId);
        if(externalVO == null){
            return null;
        }
        SpuLangVO spuLangVO = externalVO.getLangVOList().stream()
                .filter(line -> LangConstant.LANG_ZH.equals(line.getLang()))
                .findFirst().orElse(null);
        return spuLangVO != null ? spuLangVO.getSpuTitle() : null;
    }

    /** 提交默认申请单信息(落库但不会触发调用joySky审批流)*/
    @Override
    public String submitDefaultApplyInfo(Long id){
        ApplyInfoPO defaultApplyInfo = getDefaultApplyInfoPo(id);
        boolean res = applyInfoAtomicService.save(defaultApplyInfo);
        return res?defaultApplyInfo.getProcessInstanceId():null;
    }

    /** 获取不走joySku审批流时的默认占位审批单信息*/
    private ApplyInfoPO getDefaultApplyInfoPo(Long id){
        ApplyInfoPO applyInfoPO = new ApplyInfoPO();
        applyInfoPO.setProcessType(flowTypeEnum.getFlowTypeCode());
        applyInfoPO.setBizId(id.toString());
        applyInfoPO.setProcessInstanceId(UUID.randomUUID().toString());
        applyInfoPO.setApplyCode("DEFAULT-"+UUID.randomUUID());
        applyInfoPO.setAuditStatus(AuditStatusEnum.APPROVED.getCode());
        applyInfoPO.setVersion(0);
        applyInfoPO.setCreator(LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoPO.setCreateTime(new Date().getTime());
        applyInfoPO.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoPO.setUpdateTime(new Date().getTime());
        return applyInfoPO;
    }


}
