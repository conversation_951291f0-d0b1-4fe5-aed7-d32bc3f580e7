package com.jdi.isc.product.soa.service.manage.customerMku.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuStatusEnum;
import com.jdi.isc.product.soa.common.constants.AmendConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.AttributeCheckTypeEnum;
import com.jdi.isc.product.soa.common.enums.PoolVerificationEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.exception.CustomerMkuBindException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryPoolSwitchVO;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerMku.biz.*;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.product.soa.domain.ducc.ChatGptProperties;
import com.jdi.isc.product.soa.domain.ducc.PreselectionVO;
import com.jdi.isc.product.soa.domain.enums.countryMku.CountryMkuBlackReasonEnum;
import com.jdi.isc.product.soa.domain.enums.countryMku.CountryMkuWarnReasonEnum;
import com.jdi.isc.product.soa.domain.enums.mku.MkuSpuRelationFlagEnum;
import com.jdi.isc.product.soa.domain.enums.price.SkuSalePriceTypeEnums;
import com.jdi.isc.product.soa.domain.mku.biz.MkuLangTitleVO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuOperateVO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuPoolVerificationReqVO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuPoolVerificationResVO;
import com.jdi.isc.product.soa.domain.mku.po.*;
import com.jdi.isc.product.soa.domain.mkuTag.biz.MkuTagPageVO;
import com.jdi.isc.product.soa.domain.mkuTag.po.MkuTagPO;
import com.jdi.isc.product.soa.domain.sku.biz.ProductIdVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.specialAttr.biz.SpecialAttrTagVO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuLangPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.taxRate.po.CustomerSkuTaxRatePO;
import com.jdi.isc.product.soa.price.api.salePrice.req.SalePriceReqDTO;
import com.jdi.isc.product.soa.price.api.salePrice.res.SalePriceResDTO;
import com.jdi.isc.product.soa.rpc.ept.EptWareRpcService;
import com.jdi.isc.product.soa.service.atomic.brand.BrandAtomicService;
import com.jdi.isc.product.soa.service.atomic.brand.BrandCategoryAtomicService;
import com.jdi.isc.product.soa.service.atomic.brand.BrandCountryAtomicService;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.*;
import com.jdi.isc.product.soa.service.atomic.mkuTag.MkuTagAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.CustomerSkuTaxRateAtomicService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.customerMku.CustomerMkuBindManageService;
import com.jdi.isc.product.soa.service.manage.customerMku.CustomerMkuManageService;
import com.jdi.isc.product.soa.service.manage.customerMku.CustomerMkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuManageService;
import com.jdi.isc.product.soa.service.manage.mkuTag.MkuTagManageService;
import com.jdi.isc.product.soa.service.manage.price.salePrice.SkuSalePriceManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuCrossBorderSaleStatusReadManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuDraftManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.validate.SkuValidateService;
import com.jdi.isc.product.soa.service.mapstruct.customer.CustomerConvert;
import com.jdi.isc.product.soa.service.mapstruct.customerMku.CustomerMkuConvert;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuConvert;
import com.jdi.isc.product.soa.service.support.TextTranslateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
@Service
@Slf4j
public class CustomerMkuManageServiceImpl extends BaseManageSupportService<CustomerMkuVO, CustomerMkuPO> implements CustomerMkuManageService {

    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;
    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;
    @Resource
    private CustomerMkuPriceManageService customerMkuPriceManageService;
    @Resource
    private MkuAtomicService mkuAtomicService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private EptWareRpcService eptWareRpcService;
    @Resource
    private CustomerSkuTaxRateAtomicService customerSkuTaxRateAtomicService;
    @Resource
    private CustomerMkuPriceAtomicService customerMkuPriceAtomicService;
    @Resource
    private SpuReadManageService spuReadManageService;
    @Resource
    private SpuManageService spuManageService;
    @Resource
    private BrandAtomicService brandAtomicService;
    @Resource
    private BrandCategoryAtomicService brandCategoryAtomicService;
    @Resource
    private BrandCountryAtomicService brandCountryAtomicService;

    @LafValue("jdi.isc.bind.check")
    private Boolean bindCheckFlag;

    // 入池校验商品无英文名，比亚迪
    @LafValue("jdi.isc.bind.check.mkuName.client")
    private String clientCodes;

    @LafValue("jdi.isc.test.erp")
    private String testErp;

    @Resource
    private MkuLangAtomicService mkuLangAtomicService;

    @Resource
    private CustomerMkuBindManageService customerMkuBindManageService;

    @Resource
    private SpuAtomicService spuAtomicService;
    /**
     * spu草稿服务的注入，用于在校验商品可售性时更新spu草稿的属性范围。
     */
    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;

    /**
     * MKU管理服务，提供对MKU的操作和查询功能。
     */
    @Resource
    private MkuManageService mkuManageService;

    /**
     * SKU校验服务，用于在校验商品可售性时更新spu草稿的属性范围。
     */
    @Resource
    private SkuValidateService skuValidateService;
    @Resource
    private LangManageService langManageService;
    @Resource
    private TextTranslateService xiangJiTranslateService;
    @Resource
    private SpuLangAtomicService spuLangAtomicService;
    @Resource
    private SpuDraftManageService spuDraftManageService;
    @Resource
    private MkuDescLangAtomicService mkuDescLangAtomicService;
    @Resource
    private MkuDraftAtomicService mkuDraftAtomicService;

    @Resource
    private CountryManageService countryManageService;
    @Resource
    private MkuTagAtomicService mkuTagAtomicService;

    @LafValue("jdi.isc.test.customer")
    private Set<String> testClientSet;

    @LafValue("jdi.isc.test.mkuIds")
    private Set<Long> testMkuIds;

    @Resource
    private OperDuccConfig operDuccConfig;
    @Resource
    private CountryMkuAtomicService countryMkuAtomicService;

    @Resource
    private MkuTagManageService mkuTagManageService;
    private static Long PRE_SELECTED = 9L;

    @Resource
    private SkuCrossBorderSaleStatusReadManageService skuCrossBorderSaleStatusReadManageService;

    @LafValue("jdi.isc.erp.check.switch")
    private boolean erpCheckSwitch;

    @Resource
    private SkuReadManageService skuReadManageService;

    @Resource
    private Map<String, SkuSalePriceManageService> skuSalePriceManageServiceMap;

    @Override
    public List<CustomerMkuVO> listCustomerMkuPOByMkuIds(List<Long> mkuIds, String clientCode) {
        List<CustomerMkuPO> customerMkuPOList = customerMkuAtomicService.listCustomerMkuPOByMkuIds(mkuIds, clientCode);
        List<CustomerMkuVO> customerMkuVOList = CustomerMkuConvert.INSTANCE.listPo2vo(customerMkuPOList);
        return customerMkuVOList;
    }
    @Override
    public CustomerMkuVO customerMkuPOByMkuId(Long mkuId, String clientCode) {
        CustomerMkuPO customerMkuPO = customerMkuAtomicService.customerMkuPOByMkuId(mkuId,clientCode);
        CustomerMkuVO customerMkuVO = CustomerMkuConvert.INSTANCE.vo2Po(customerMkuPO);
        return customerMkuVO;
    }

    @Override
    public PageInfo<CustomerMkuVO> page(CustomerMkuQueryVO input) {
        List<Long> mkuIdSet = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(input.getMkuIds())){
            mkuIdSet.addAll(input.getMkuIds());
        }

        if(CollectionUtils.isNotEmpty(input.getProductIds())){
            ProductIdVO productIdVO = skuReadManageService.getProductInfoByIds(input.getProductIds());
            if(CollectionUtils.isNotEmpty(productIdVO.getMkuIds())){
                mkuIdSet.addAll(productIdVO.getMkuIds());
                input.setMkuIds(mkuIdSet);
            }
        }
        PageInfo<CustomerMkuVO> page = customerMkuAtomicService.page(input);
        List<CustomerMkuVO> list = page.getRecords();
        setMkuTag(list);
        setSalePrice(list);
        page.setRecords(list);
        return page;
    }

    private void setSalePrice(List<CustomerMkuVO> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        List<Long> mkuList = voList.stream().map(CustomerMkuVO::getMkuId).collect(Collectors.toList());
        List<MkuRelationPO> mkuRelationPOList = mkuRelationAtomicService.queryBindListByMkuIds(mkuList);
        if(CollectionUtils.isEmpty(mkuRelationPOList)){
            return;
        }
        Map<Long, Long> mkuSkuMap = mkuRelationPOList.stream().collect(Collectors.toMap(MkuRelationPO::getMkuId,
            MkuRelationPO::getSkuId, (v1, v2) -> v1));
        Map<String,CustomerVO> clientMap = new HashMap<>();
        for (CustomerMkuVO customerMkuVO : voList ){
            if(!mkuSkuMap.containsKey(customerMkuVO.getMkuId())){
                continue;
            }
            CustomerVO customerVO = null;
            if(clientMap.containsKey(customerMkuVO.getClientCode())){
                customerVO = clientMap.get(customerMkuVO.getClientCode());
            }else {
                customerVO = customerManageService.detail(customerMkuVO.getClientCode());
                clientMap.put(customerMkuVO.getClientCode(),customerVO);
            }
            SalePriceReqDTO salePriceReqDTO = new SalePriceReqDTO();
            salePriceReqDTO.setSkuId(mkuSkuMap.get(customerMkuVO.getMkuId()));
            salePriceReqDTO.setClientCode(customerMkuVO.getClientCode());
            salePriceReqDTO.setCustomerDTO(CustomerConvert.INSTANCE.customerVo2Dto(customerVO));
            salePriceReqDTO.setCurrencyCode(customerVO.getSaleCurrency());
            SalePriceResDTO salePriceResDTO = skuSalePriceManageServiceMap.get(SkuSalePriceTypeEnums.getEnumByCountryCode(customerVO.getCountry()).getServiceName()).getSalePrice(salePriceReqDTO);
            if(Objects.nonNull(salePriceResDTO)){
                customerMkuVO.setSalePrice(salePriceResDTO.getSalePrice());
                customerMkuVO.setTaxSalePrice(salePriceResDTO.getTaxSalePrice());
                customerMkuVO.setTargetCurrencyCode(salePriceResDTO.getTargetCurrencyCode());
            }
        }
    }

    public void setMkuTag(List<CustomerMkuVO> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        for (CustomerMkuVO customerMkuVO : voList) {
            if (Objects.isNull(customerMkuVO.getMkuId()) || StringUtils.isBlank(customerMkuVO.getTargetCountryCode())) {
                continue;
            }
            MkuTagPageVO mkuTagVO = new MkuTagPageVO();
            List<Long> mkuList = new ArrayList<>();
            mkuList.add(customerMkuVO.getMkuId());
            mkuTagVO.setMkuList(mkuList);
            mkuTagVO.setTargetCountryCode(customerMkuVO.getTargetCountryCode());
            Map<Long, List<SpecialAttrTagVO>> mkuSpecialMap = mkuTagManageService.getMkuTagVOMap(mkuTagVO);

            if (mkuSpecialMap.containsKey(customerMkuVO.getMkuId())) {
                List<SpecialAttrTagVO> specialAttrTagVOList = mkuSpecialMap.get(customerMkuVO.getMkuId());
                customerMkuVO.setSpecialAttrTagVOList(specialAttrTagVOList);
            }
        }
    }

    /**
     * 批量修改
     * @param spuAmendReqVOList
     * @return
     */
    @Override
    public DataResponse<String> batchAmend(List<SpuAmendReqVO> spuAmendReqVOList) {
        return spuManageService.batchAmend(spuAmendReqVOList);
    }

    @Override
    @PFTracing
    public List<SpuAmendVO> getDetailListByMkuIds(List<Long> mkuIds, String countryCode) {
        log.info("CustomerMkuManageServiceImpl.getDetailListByMkuIds 请求信息 input={}", JSON.toJSONString(mkuIds));
        Map<Long, List<Long>> spuIdMkuIdMap = getSpuMkuRelationNewMap(mkuIds);
        if (CollectionUtils.isEmpty(spuIdMkuIdMap.keySet())) {
            return new ArrayList<>();
        }
        List<Integer> checkTypeList = new ArrayList<>();
        checkTypeList.add(AttributeCheckTypeEnum.ADD_CUSTOMER_POOL.getCode());
        checkTypeList.add(AttributeCheckTypeEnum.PUBLISH_PRODUCT.getCode());
        List<SpuAmendVO> spuAmendVOs = spuReadManageService.getSpuAmendVOList(Lists.newArrayList(spuIdMkuIdMap.keySet()), Lists.newArrayList(countryCode), checkTypeList);
        // 查询商品英文名称
        Map<Long, MkuLangPO> mkuLangPOMap = mkuLangAtomicService.listLang(new MkuOperateVO(Sets.newHashSet(mkuIds), LangConstant.LANG_ZH));
        log.info("CustomerMkuManageServiceImpl.getDetailListByMkuIds spuAmendVOs={}", JSON.toJSONString(spuAmendVOs));
        //海关编码hscode
        Map<Long, CustomerSkuTaxRatePO> skuTaxRateMap = customerSkuTaxRateAtomicService.getSkuTaxRateBySpuIds(Lists.newArrayList(spuIdMkuIdMap.keySet()));
        for (SpuAmendVO spuAmendVO : spuAmendVOs) {
            List<Long> spuMkuIds = spuIdMkuIdMap.get(spuAmendVO.getSpuId());
            for(Long mkuId : spuMkuIds){
                spuAmendVO.setMkuId(mkuId);
                spuAmendVO.setMkuName(mkuLangPOMap.get(mkuId).getMkuTitle());
                Map<String, Boolean> propertyMap = spuAmendVO.getPropertyMap();
                if (propertyMap.isEmpty()) {
                    continue;
                }
                Map<String, Boolean> newMap = new HashMap<>();
                for (Map.Entry<String, Boolean> entry : propertyMap.entrySet()) {
                    if (entry.getValue()) {
                        newMap.put(entry.getKey(), entry.getValue());
                    }
                }
                // hscode
                CustomerSkuTaxRatePO taxRate = skuTaxRateMap.get(spuAmendVO.getSpuId());
                if (Objects.nonNull(taxRate)) {
                    spuAmendVO.setSkuHSCode(taxRate.getHsCode());
                }
                spuAmendVO.setPropertyMap(newMap);
            }
        }
        // check校验数据属性值有为null的返回给前台。
        List<SpuAmendVO> spuAmendVOList = spuAmendVOs.stream().filter(spuAmendVO -> {
            Map<String, Boolean> propertyMap = spuAmendVO.getPropertyMap();
            if (propertyMap.isEmpty()) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        log.info("CustomerMkuManageServiceImpl.getDetailListByMkuIds 请求信息 output={}", JSON.toJSONString(spuAmendVOs));
        return spuAmendVOList;
    }

    /**
     * 根据给定的mkuIds获取spu和mku的对应关系
     *
     * @param mkuIds mku的id列表
     * @return spuId到mkuId的映射关系
     */
//    @PFTracing
//    private Map<Long, Long> getSpuMkuRelationMap(List<Long> mkuIds) {
//        Map<Long, Long> mkuSkuMap = getMkuIdSkuIdMapByMkuIds(mkuIds);
//        Map<Long, Long> spuIdMkuIdMap = Maps.newHashMap();
//        if (CollectionUtils.isEmpty(mkuSkuMap.values())) {
//            return spuIdMkuIdMap;
//        }
//        // sku和spu关系
//        Map<Long, Long> skuSpuMap = skuAtomicService.getSkuSpuMapBySkuIds(Sets.newHashSet(mkuSkuMap.values()));
//        mkuSkuMap.forEach((mkuId, skuId) -> {
//            if (Objects.nonNull(skuSpuMap.get(skuId))) {
//                spuIdMkuIdMap.put(skuSpuMap.get(skuId), mkuId);
//            }
//        });
//        return spuIdMkuIdMap;
//    }

    /**
     * 根据给定的mkuIds获取spu和mku的对应关系
     *
     * @param mkuIds mku的id列表
     * @return spuId到mkuId的映射关系
     */
    @PFTracing
    private Map<Long, List<Long>> getSpuMkuRelationNewMap(List<Long> mkuIds) {
        // 获取MKU到SKU的映射关系
        Map<Long, Long> mkuSkuMap = getMkuIdSkuIdMapByMkuIds(mkuIds);
        if (MapUtils.isEmpty(mkuSkuMap)) {
            return Maps.newHashMap();
        }

        // 获取SKU到SPU的映射关系
        Set<Long> skuIds = Sets.newHashSet(mkuSkuMap.values());
        Map<Long, Long> skuSpuMap = skuAtomicService.getSkuSpuMapBySkuIds(skuIds);

        // 构建SPU到MKU列表的映射关系
        Map<Long, List<Long>> spuIdMkuIdMap = Maps.newHashMap();
        mkuSkuMap.forEach((mkuId, skuId) -> {
            Long spuId = skuSpuMap.get(skuId);
            if (Objects.nonNull(spuId)) {
                spuIdMkuIdMap.computeIfAbsent(spuId, k -> Lists.newArrayList()).add(mkuId);
            }
        });
        return spuIdMkuIdMap;
    }

    /**
     * 根据 MKU ID 列表获取 MKU ID 和对应 SPu ID 的映射关系。
     * @param mkuIds MKU ID 列表
     * @return MKU ID 和对应 SPu ID 的映射关系，若无匹配则返回空 Map
     */
    private Map<Long, Long> getMkuIdSpuIdMap(List<Long> mkuIds) {
        Map<Long, Long> mkuSkuMap = getMkuIdSkuIdMapByMkuIds(mkuIds);
        Map<Long, Long> mkuIdSpuIdMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(mkuSkuMap.values())) {
            return mkuIdSpuIdMap;
        }
        // sku和spu关系
        Map<Long, Long> skuSpuMap = skuAtomicService.getSkuSpuMapBySkuIds(Sets.newHashSet(mkuSkuMap.values()));
        mkuSkuMap.forEach((mkuId, skuId) -> {
            if (Objects.nonNull(skuSpuMap.get(skuId))) {
                mkuIdSpuIdMap.put(mkuId, skuSpuMap.get(skuId));
            }
        });
        return mkuIdSpuIdMap;
    }

    /**
     * 根据给定的mkuIds获取对应的mkuId和固定sku的映射关系。
     *
     * @param mkuIds 需要查询的mkuId列表。
     * @return 每个mkuId对应的固定sku的id的映射关系。
     */
    @PFTracing
    private Map<Long, Long> getMkuIdSkuIdMapByMkuIds(List<Long> mkuIds) {
        // 查询mku关联sku列表
        Map<Long, List<MkuRelationPO>> mkuBindSkuListMap = mkuRelationAtomicService.queryMkuBindSkuListMap(mkuIds);
        Map<Long, Long> mkuSkuMap = Maps.newHashMap();
        mkuBindSkuListMap.forEach((mkuId, skuList) -> {
            // mku和sku关系简历最早为固定sku
            MkuRelationPO fixSkuPo = skuList.stream().min(Comparator.comparing(MkuRelationPO::getCreateTime)).orElse(null);
            if (null == fixSkuPo) {
                throw new CustomerMkuBindException(
                    String.format("客户mku绑定失败:mku %s 未能找到同货源国的固定sku", mkuId));
            }
            mkuSkuMap.put(mkuId, fixSkuPo.getSkuId());
        });
        return mkuSkuMap;
    }

    @Override
    @PFTracing
    public DataResponse<Set<Long>> batchBind(CustomerMkuBatchReqVO input) {

        CustomerVO customer = customerManageService.detail(input.getClientCode());
        if (customer == null) {
            throw new CustomerMkuBindException(String.format("客户mku绑定失败:客户 %s 不存在", input.getClientCode()));
        }
        if (CollectionUtils.isEmpty(customer.getStations())) {
            throw new CustomerMkuBindException(String.format("客户mku绑定失败:客户 %s 未配置可采购的货源站点", input.getClientCode()));
        }

        // 是否为测试客户 如果是再校验商品是否为测试商品
        boolean testClient = testClientSet.contains(input.getClientCode());

        Set<Long> failedMkuSet = Sets.newHashSet();
        StringBuilder msgBuilder = new StringBuilder();
        input.getMkuIds().forEach(mkuId -> {
            try {
                // 入池校验非测试品和测试客户
                if (testClient && !testMkuIds.contains(mkuId)) {
                    throw new CustomerMkuBindException(String.format("测试客户编码 %s 不能绑定非测试品。", input.getClientCode()));
                }
                CustomerMkuEachReqVO customerMkuEachReqVO = new CustomerMkuEachReqVO();
                customerMkuEachReqVO.setClientCode(input.getClientCode());
                customerMkuEachReqVO.setMkuId(mkuId);
                customerMkuEachReqVO.setBindStatus(input.getBindStatus());
                customerMkuEachReqVO.setGreenPass(input.getGreenPass());
                customerMkuBindManageService.bind(customerMkuEachReqVO);
            } catch (CustomerMkuBindException mkuBindException) {
                log.error("batchBind 单个入池校验不通过,mkuId={}", mkuId, mkuBindException);
                msgBuilder.append(mkuId).append(Constant.COLON).append(mkuBindException.getMessage()).append(Constant.FRONT_LINE);
                failedMkuSet.add(mkuId);
            } catch (Exception e) {
                log.error("batchBind 单个入池异常,mkuId={}", mkuId, e);
                failedMkuSet.add(mkuId);
            }
        });

        // 返回失败MKU集合
        if (StringUtils.isNotBlank(msgBuilder.toString())) {
            String msgPrefix = failedMkuSet.size() == input.getMkuIds().size() ? "全部失败 " : "部分失败 ";
            DataResponse<Set<Long>> dataResponse = DataResponse.error(msgPrefix + msgBuilder);
            dataResponse.setData(failedMkuSet);
            return dataResponse;
        }
        return DataResponse.success();
    }

    @Override
    @PFTracing
    public MkuPoolVerificationResVO poolVerification(MkuPoolVerificationReqVO mkuPoolVerificationReqVO) {
        MkuPoolVerificationResVO response = handlePoolVerification(mkuPoolVerificationReqVO);
        if (CollectionUtils.isEmpty(response.getFailMkuIdList()) && CollectionUtils.isEmpty(response.getNotExistMkuIdList())
            && !PoolVerificationEnum.CHECK_SUBTITLE.getCode().equals(response.getCheckType())) {
            mkuPoolVerificationReqVO.setCheckType(PoolVerificationEnum.getNextCode(mkuPoolVerificationReqVO.getCheckType()));
            return poolVerification(mkuPoolVerificationReqVO);
        }
        return response;
    }

    @PFTracing
    private MkuPoolVerificationResVO handlePoolVerification(MkuPoolVerificationReqVO request) {
        Integer checkType = request.getCheckType();
        if (PoolVerificationEnum.CHECK_TARGET_COUNTRY.getCode().equals(checkType)) {
            MkuPoolVerificationResVO response = checkTargetCountry(request);
            return response;
        } else if (PoolVerificationEnum.CHECK_MARKETABILITY.getCode().equals(checkType)) {
            MkuPoolVerificationResVO response = checkMarketability(request);
            return response;
        } else if (PoolVerificationEnum.CHECK_MULTILINGUAL.getCode().equals(checkType)) {
            MkuPoolVerificationResVO response = checkMultilingual(request);
            return response;
        } else if (PoolVerificationEnum.UPDATE_MULTILINGUAL.getCode().equals(checkType)) {
            MkuPoolVerificationResVO response = updateMultilingual(request);
            return response;
        } else if (PoolVerificationEnum.CHECK_COUNTRY_POOL.getCode().equals(checkType)) {
            MkuPoolVerificationResVO response = checkCountryPool(request);
            return response;
        } else if (PoolVerificationEnum.CHECK_SUBTITLE.getCode().equals(checkType)) {
            MkuPoolVerificationResVO response = checkSubtitle(request);
            return response;
        }
        return new MkuPoolVerificationResVO();
    }
    @PFTracing
    private MkuPoolVerificationResVO checkSubtitle(MkuPoolVerificationReqVO request) {
        MkuPoolVerificationResVO response = new MkuPoolVerificationResVO();
        response.setCheckType(request.getCheckType());

        CountryPoolSwitchVO countryPool = operDuccConfig.getCountryPool();
        if (!countryPool.getSubtitleClientCodeList().contains(request.getClientCode())) {
            response.setSuccessMkuIdList(request.getSuccessMkuIdList());
            return response;
        }
        Map<Long, MkuLangPO> mkuIdLangPoMap =
            mkuLangAtomicService.getMkuLangNameByMkuIds(request.getSuccessMkuIdList(), LangConstant.LANG_BR);
        List<Long> successMkuIdList = new ArrayList<>();
        List<Long> failMkuIdList = new ArrayList<>();
        ChatGptProperties chatGptProperties = operDuccConfig.preseChatGpt();
        Map<String, Integer> contentLengthMap = chatGptProperties.getContentLength();
        Integer contentLength = null;
        for (Long mkuId : request.getSuccessMkuIdList()) {
            if (!mkuIdLangPoMap.containsKey(mkuId)) {
                failMkuIdList.add(mkuId);
                continue;
            }
            MkuLangPO mkuLangPO = mkuIdLangPoMap.get(mkuId);
            contentLength = contentLengthMap.get(mkuLangPO.getLang());
            if (StringUtils.isBlank(mkuLangPO.getMkuSubtitle()) || mkuLangPO.getMkuSubtitle().length() > contentLength) {
                failMkuIdList.add(mkuId);
                continue;
            }
            if (chatGptProperties.getFilterList().contains(mkuLangPO.getMkuSubtitle())) {
                failMkuIdList.add(mkuId);
                continue;
            }
            successMkuIdList.add(mkuId);
        }
        response.setSuccessMkuIdList(successMkuIdList);
        response.setFailMkuIdList(failMkuIdList);
        if (CollectionUtils.isNotEmpty(failMkuIdList)) {
            String successStr = successMkuIdList.stream().map(String::valueOf).collect(Collectors.joining(Constant.COMMA));
            String failStr = failMkuIdList.stream().map(String::valueOf).collect(Collectors.joining(Constant.COMMA));
            StringBuffer sb = new StringBuffer();
            sb.append("通过MKUID：")
                .append(successStr)
                .append("</br>")
                .append("未通过MKUID：")
                .append(failStr)
                .append("</br>")
                .append("未通过原因：巴葡短标题超过")
                .append(contentLength)
                .append("字符或缺失，无法入池。")
                .append("</br>")
                .append("请通知采销维护，并重新绑定。");
            response.setWarnStr(sb.toString());
        }
        return response;
    }

    @PFTracing
    private MkuPoolVerificationResVO checkCountryPool(MkuPoolVerificationReqVO request) {
        MkuPoolVerificationResVO response = new MkuPoolVerificationResVO();
        response.setCheckType(request.getCheckType());
        CustomerVO customerVO = customerManageService.detail(request.getClientCode());
        if (Objects.nonNull(customerVO.getCountry())) {
            request.setCountryCode(customerVO.getCountry());
        }
        CountryPoolSwitchVO countryPool = operDuccConfig.getCountryPool();
        if (!countryPool.getCountrySwitch().getOrDefault(customerVO.getCountry(), false)) {
            response.setSuccessMkuIdList(request.getSuccessMkuIdList());
            return response;
        }
        List<CountryMkuPO> countryMkuPOList = countryMkuAtomicService.getPoByMkuIdAndCountryCode(request.getSuccessMkuIdList(), request.getCountryCode());
        List<Long> existList = new ArrayList<>();
        for (CountryMkuPO countryMkuPO : countryMkuPOList) {
            if (Objects.nonNull(countryMkuPO) && Objects.nonNull(countryMkuPO.getMkuId())) {
                existList.add(countryMkuPO.getMkuId());
            }
        }
        List<PreselectionVO> preselectionVOList = operDuccConfig.preselectionList();
        for (PreselectionVO preselectionVO : preselectionVOList) {
            if (preselectionVO.getClientCodeList().contains(request.getClientCode())) {
                List<Long> preselectionMkuList = filterPreselection(request.getSuccessMkuIdList(), request.getCountryCode());
                existList.addAll(preselectionMkuList);
            }
        }
        existList = existList.stream().distinct().collect(Collectors.toList());

        List<Long> notExistMkuIdList = checkNotExistMkuIdList(request.getSuccessMkuIdList(), new HashSet<>(existList));
        response.setSuccessMkuIdList(existList);
        response.setNotExistMkuIdList(notExistMkuIdList);
        if (CollectionUtils.isNotEmpty(notExistMkuIdList)) {
            List<CountryMkuPO> noExistMkuPoList = countryMkuAtomicService.getPoByMkuIdCountryPoolStatus(notExistMkuIdList, request.getCountryCode(), null);
            String countryName = countryManageService.getCountryNameByCountryCode(request.getCountryCode(), LangConstant.LANG_ZH);
            for (CountryMkuPO countryMkuPO : noExistMkuPoList) {
                List<String> reasonList = new ArrayList<>();
                String blackReason = countryMkuPO.getBlackReason();
                if (StringUtils.isNotBlank(blackReason)) {
                    String desc = CountryMkuBlackReasonEnum.countryEnumByCode(Integer.valueOf(blackReason)).getDesc();
                    reasonList.add(desc);
                }
                String warnReason = countryMkuPO.getWarnReason();
                if (StringUtils.isNotBlank(warnReason)) {
                    String[] split = warnReason.split(Constant.COMMA);
                    for (String s : split) {
                        String desc = CountryMkuWarnReasonEnum.countryEnumByCode(Integer.valueOf(s)).getDesc();
                        reasonList.add(desc);
                    }
                }
                String reasonStr = String.join(Constant.COMMA, reasonList);
                String notExistWarn = String.format("MKUID %s，未入【%s】国家池，入客户池失败，失败原因: %s。</br>", countryMkuPO.getMkuId(), countryName, reasonStr);
                notExistWarn = notExistWarn + "业务疑问可进群：10208414205。";
                response.setWarnStr(notExistWarn);
            }
            //response.setWarnStr(buildWarningString(null,null, notExistMkuIdList,notExistWarn));
        }
        return response;
    }

    private List<Long> filterPreselection(List<Long> successMkuIdList, String countryCode) {
        MkuTagPageVO mkuTagPageVO = new MkuTagPageVO();
        mkuTagPageVO.setMkuList(successMkuIdList);
        mkuTagPageVO.setTargetCountryCode(countryCode);
        List<MkuTagPO> poList = mkuTagAtomicService.getPoList(mkuTagPageVO);
        List<Long> mkuIdList = poList.stream().filter(po -> po.getAttributeKeyId().equals(PRE_SELECTED)).map(MkuTagPO::getMkuId).collect(Collectors.toList());
        return mkuIdList;
    }
    @PFTracing
    private MkuPoolVerificationResVO checkTargetCountry(MkuPoolVerificationReqVO request) {
        MkuPoolVerificationResVO response = new MkuPoolVerificationResVO();
        response.setCheckType(request.getCheckType());
        List<Long> successMkuIdList = new ArrayList<>();
        List<Long> failMkuIdList = new ArrayList<>();

        CustomerVO customerVO = customerManageService.detail(request.getClientCode());
        if (Objects.nonNull(customerVO.getCountry())) {
            request.setCountryCode(customerVO.getCountry());
        }
        Map<Long, List<Long>> spuIdMkuIdMap = getSpuMkuRelationNewMap(request.getSuccessMkuIdList());
        List<Long> notExistMkuIdList = checkNotExistMkuIdList(request.getSuccessMkuIdList()
                , spuIdMkuIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet()));
        Map<Long, SpuPO> spuMap = spuAtomicService.getSpuMap(spuIdMkuIdMap.keySet());
        spuMap.forEach((spuId, spuPO) -> {
            String attributeScope = spuPO.getAttributeScope();
            List<String> countryArray = Arrays.asList(attributeScope.split(Constant.COMMA));
            List<Long> mkuIds = spuIdMkuIdMap.get(spuId);
            if (countryArray.contains(request.getCountryCode())) {
                successMkuIdList.addAll(mkuIds);
            } else {
                failMkuIdList.addAll(mkuIds);
            }
        });
        response.setSuccessMkuIdList(successMkuIdList);
        response.setFailMkuIdList(failMkuIdList);
        response.setNotExistMkuIdList(notExistMkuIdList);

        String countryName = countryManageService.getCountryNameByCountryCode(request.getCountryCode(), LangConstant.LANG_ZH);
        String failWarn = String.format("销售国家中不包含【%s】，请确认这些商品是否需要售往【%s】。", countryName, countryName);
        failWarn = "MKUID %s，" + failWarn;
        String notExistWarn = "MKUID %s，不存在，无法绑定。";
        response.setWarnStr(buildWarningString(failMkuIdList, failWarn, notExistMkuIdList, notExistWarn));
        return response;
    }

    private List<Long> checkNotExistMkuIdList(List<Long> mkuIdList, Set<Long> targetMkuList) {
        Set<Long> mkuIds = new HashSet<>(mkuIdList);
        mkuIds.removeAll(targetMkuList);
        return new ArrayList<>(mkuIds);
    }

    /**
     * 验证商品池中商品的可售性和是否符合加池条件。
     * @param request 包含待验证商品的信息，包括检查类型、成功的MKU ID列表和客户代码。
     * @return MkuPoolVerificationResVO 对象，包含验证结果，包括成功的MKU ID列表、失败的MKU ID列表和警告信息。
     */
    private MkuPoolVerificationResVO checkMarketability(MkuPoolVerificationReqVO request) {
        MkuPoolVerificationResVO response = new MkuPoolVerificationResVO();
        response.setCheckType(request.getCheckType());

        handleSpuAttributeScope(request);

        Map<Long, List<Long>> spuIdMkuIdMap = getSpuMkuRelationNewMap(request.getSuccessMkuIdList());
        Map<Long, SpuPO> spuIdSpuPoMap = spuAtomicService.getSpuMap(spuIdMkuIdMap.keySet());

        Map<Long, SpuPO> mkuIdSpuPOMap = new HashMap<>();
        List<Long> failMkuIdList = new ArrayList<>();
        List<Long> successMkuIdList = new ArrayList<>();
        // 判断是否是上架状态，可售状态
        spuIdSpuPoMap.forEach((spuId, spuPO) -> {
            List<Long> mkuIds = spuIdMkuIdMap.get(spuId);
            if (!spuPO.getSpuStatus().equals(SpuStatusEnum.ON_SALE.getStatus()) || spuPO.getAuditStatus().equals(SpuAuditStatusEnum.WAITING_APPROVED.getCode())
                || spuPO.getAuditStatus().equals(SpuAuditStatusEnum.WAITING_APPROVED_VENDOR_SUBMIT.getCode())) {
                failMkuIdList.addAll(mkuIds);
            } else {
                for(Long mkuId : mkuIds){
                    mkuIdSpuPOMap.put(mkuId, spuPO);
                }
            }
        });

        List<Long> failCanPurchaseMkuIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mkuIdSpuPOMap.keySet())) {
            Map<Long, SkuPO> mkuIdSkuPOMap = mkuManageService.queryMkuIdSkuPoMapByMkuIds(new ArrayList<>(mkuIdSpuPOMap.keySet()));
            Map<Long, Long> cnMkuIdSkuIdMap = new HashMap<>();
            // 校验跨境的是否主站可售，MRO是否加池
            mkuIdSpuPOMap.forEach((mkuId, spuPO) -> {
                if (CountryConstant.COUNTRY_ZH.equals(spuPO.getSourceCountryCode())) {
                    SkuPO skuPO = mkuIdSkuPOMap.get(mkuId);
                    if (skuPO != null) {
                        cnMkuIdSkuIdMap.put(mkuId, skuPO.getSkuId());
                    }
                } else {
                    successMkuIdList.add(mkuId);
                }
            });
            CustomerVO customerVO = customerManageService.detail(request.getClientCode());
            // 主站可售
            Set<Long> skuIds = cnMkuIdSkuIdMap.values().stream().filter(Objects::nonNull).collect(Collectors.toSet());
            List<Long> canPurchaseSkuIdS = skuValidateService.validateSkuIdCanPurchase(Lists.newArrayList(skuIds), customerVO.getCountry(), new HashMap<>(),true);
            cnMkuIdSkuIdMap.forEach((mkuId,skuId) -> {
                if(!canPurchaseSkuIdS.contains(skuId)) {
                    failCanPurchaseMkuIdList.add(mkuId);
                }
            });
            // MRO是否加池
            List<Long> areaSkuIdS = skuValidateService.validateSkuIdAreaLimit(canPurchaseSkuIdS, customerVO.getCountry(),true);
            cnMkuIdSkuIdMap.forEach((mkuId, skuId) -> {
                if (areaSkuIdS.contains(skuId)) {
                    failCanPurchaseMkuIdList.add(mkuId);
                } else {
                    successMkuIdList.add(mkuId);
                }
            });
        }
        response.setSuccessMkuIdList(successMkuIdList);
        response.setFailMkuIdList(mergeLists(failMkuIdList, failCanPurchaseMkuIdList));
        String failWarn = "MKUID %s下无SKU或没有SKU在上架且过审的状态（在审核中的商品不能加池，须完成审核），加池失败";
        String notCanPurchaseWarn = "MKUID %s，在主站不可售，加池失败，请检查其商品主站MRO池状态、上架状态，请复制本段文字给采销检查。其他商品正常进入下一步。";
        response.setWarnStr(buildWarningString(failMkuIdList, failWarn, failCanPurchaseMkuIdList, notCanPurchaseWarn));
        return response;
    }

    private List<Long> mergeLists(List<Long> mkuIdList, List<Long> addMkuIdList) {
        List<Long> mergedList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mkuIdList)) {
            mergedList.addAll(mkuIdList);
        }
        if (CollectionUtils.isNotEmpty(addMkuIdList)) {
            mergedList.addAll(addMkuIdList);
        }
        return mergedList;
    }

    private String buildWarningString(List<Long> mkuIdList1, String str1, List<Long> mkuIdList2, String str2) {
        StringBuilder warnStrBuilder = new StringBuilder();

        if (CollectionUtils.isNotEmpty(mkuIdList1)) {
            String mkuStr = StringUtils.join(mkuIdList1, Constant.SYMBOL_COMMA);
            String warnStr = String.format(str1, mkuStr);
            warnStrBuilder.append(warnStr);
        }

        if (CollectionUtils.isNotEmpty(mkuIdList2)) {
            String mkuStr = StringUtils.join(mkuIdList2, Constant.SYMBOL_COMMA);
            String warnStr = String.format(str2, mkuStr);
            warnStrBuilder.append(warnStr);
        }

        return warnStrBuilder.toString();
    }
    @PFTracing
    private void handleSpuAttributeScope(MkuPoolVerificationReqVO request) {
        if (CollectionUtils.isEmpty(request.getFailMkuIdList())) {
            return;
        }
        String countryCode = request.getCountryCode();
        Map<Long, List<Long>> failSpuIdMkuIdMap = getSpuMkuRelationNewMap(request.getFailMkuIdList());
        Map<Long, SpuPO> failSpuMap = spuAtomicService.getSpuMap(failSpuIdMkuIdMap.keySet());
        failSpuMap.forEach((spuId, spuPO) -> {
            String attributeScope = spuPO.getAttributeScope();
            if (StringUtils.isBlank(attributeScope)) {
                spuPO.setAttributeScope(countryCode);
            } else {
                List<String> countryArray = new ArrayList<>(Arrays.asList(attributeScope.split(Constant.COMMA)));
                if (!countryArray.contains(countryCode)) {
                    countryArray.add(countryCode);
                    String countryCodeStr = StringUtils.join(countryArray, Constant.COMMA);
                    spuPO.setAttributeScope(countryCodeStr);
                }
            }
            spuPO.setUpdater(request.getUpdater());
            spuPO.setUpdateTime(new Date());
        });
        if (CollectionUtils.isNotEmpty(failSpuMap.values())) {
            boolean updateSpuPOStatus = spuAtomicService.saveOrUpdateBatch(failSpuMap.values());
        }
        // 修改草稿表
        List<SpuDraftPO> spuDraftPOList = spuDraftAtomicService.batchQuerySpuDraftPoList(new HashSet<>(failSpuIdMkuIdMap.keySet()));
        Map<Long, SpuDraftPO> spuIdSpuDraftPOMap = spuDraftPOList.stream().collect(Collectors.toMap(SpuDraftPO::getSpuId, Function.identity(), (spu1, spu2) -> spu1));
        spuIdSpuDraftPOMap.forEach((spuId, spuDraftPO) -> {
            if (StringUtils.isNotBlank(spuDraftPO.getSpuJsonInfo())) {
                SaveSpuVO draftSaveSpuVo = JSON.parseObject(spuDraftPO.getSpuJsonInfo(), SaveSpuVO.class);
                SpuVO spuVO = draftSaveSpuVo.getSpuVO();
                if (Objects.isNull(spuVO)) {
                    SpuPO spuPO = failSpuMap.get(spuId);
                    spuVO = SpuConvert.INSTANCE.po2vo(spuPO);
                }
                String attributeScope = spuVO.getAttributeScope();
                if (StringUtils.isBlank(attributeScope)) {
                    spuVO.setAttributeScope(countryCode);
                } else {
                    List<String> countryArray = new ArrayList<>(Arrays.asList(attributeScope.split(Constant.COMMA)));
                    if (!countryArray.contains(countryCode)) {
                        countryArray.add(countryCode);
                        String countryCodeStr = StringUtils.join(countryArray, Constant.COMMA);
                        spuVO.setAttributeScope(countryCodeStr);
                    }
                }
                spuVO.setUpdater(request.getUpdater());
                draftSaveSpuVo.setSpuVO(spuVO);
                spuDraftPO.setSpuJsonInfo(JSON.toJSONString(draftSaveSpuVo));
                spuDraftPO.setUpdateTime(new Date());
                spuDraftPO.setSpuId(null);
            }
        });
        boolean updateSpuDraftStatus = spuDraftAtomicService.saveOrUpdateBatch(spuIdSpuDraftPOMap.values());
        request.setSuccessMkuIdList(mergeLists(request.getSuccessMkuIdList(), request.getFailMkuIdList()));
    }
    @PFTracing
    private MkuPoolVerificationResVO checkMultilingual(MkuPoolVerificationReqVO request) {
        MkuPoolVerificationResVO response = new MkuPoolVerificationResVO();
        response.setCheckType(request.getCheckType());
        List<Long> successMkuIdList = new ArrayList<>();
        List<Long> failMkuIdList = new ArrayList<>();
        CustomerVO customerVO = customerManageService.detail(request.getClientCode());
        Map<String, String> langMap = customerVO.getLanguage();
        if (MapUtils.isEmpty(langMap)) {
            response.setSuccessMkuIdList(request.getSuccessMkuIdList());
            return response;
        }

        // 确保 getSuccessMkuIdList 返回的是 Set<Long>
        Set<Long> successMkuIdSet = new HashSet<>(request.getSuccessMkuIdList());
        // 确保 langMap.keySet 返回的是 List<String>
        List<String> langKeyList = new ArrayList<>(langMap.keySet());
        Map<Long, Map<String, String>> mkuIdLangTitleMap = mkuLangAtomicService.getLangMapByMkuIds(successMkuIdSet, langKeyList);
        Map<String, String> finalLangTitleKeyMap = new HashMap<>(langMap);
        langMap.forEach((langCode, lang) -> {
            String langNameByLangCode = langManageService.getLangNameByLangCode(langCode, LangConstant.LANG_ZH);
            finalLangTitleKeyMap.put(langCode, "MKU" + langNameByLangCode + "名");
        });

        List<MkuLangTitleVO> langTitleList = new ArrayList<>();
        mkuIdLangTitleMap.forEach((mkuId, langTitleMap) -> {
            Set<String> dbLangSet = new HashSet<>();
            langTitleMap.forEach((lang, value) -> {
                if (StringUtils.isNotBlank(value)) {
                    dbLangSet.add(lang);
                }
            });
            Set<String> difference = new HashSet<>(finalLangTitleKeyMap.keySet());
            difference.removeAll(dbLangSet);
            if (difference.isEmpty()) {
                successMkuIdList.add(mkuId);
            } else {
                failMkuIdList.add(mkuId);
                MkuLangTitleVO mkuLangTitleVO = new MkuLangTitleVO();
                mkuLangTitleVO.setMkuId(mkuId);
                Map<String, String> map = new HashMap<>();
                finalLangTitleKeyMap.forEach((lang, titleKey) -> {
                    if (difference.contains(lang)) {
                        map.put(titleKey, "");
                    } else {
                        String title = langTitleMap.get(lang);
                        map.put(titleKey, title);
                    }
                });
                mkuLangTitleVO.setLangTitleMap(map);
                langTitleList.add(mkuLangTitleVO);
            }
        });
        response.setSuccessMkuIdList(successMkuIdList);
        response.setFailMkuIdList(failMkuIdList);
        response.setLangTitleList(langTitleList);
        return response;
    }

    @PFTracing
    private MkuPoolVerificationResVO updateMultilingual(MkuPoolVerificationReqVO request) {
        MkuPoolVerificationResVO response = new MkuPoolVerificationResVO();
        response.setCheckType(request.getCheckType());
        if (CollectionUtils.isEmpty(request.getFailMkuIdList())) {
            response.setSuccessMkuIdList(request.getSuccessMkuIdList());
            response.setFailMkuIdList(new ArrayList<>());
            return response;
        }

        CustomerVO customerVO = customerManageService.detail(request.getClientCode());
        Map<String, String> langMap = customerVO.getLanguage();
        if (MapUtils.isEmpty(langMap)) {
            response.setSuccessMkuIdList(request.getSuccessMkuIdList());
            response.setFailMkuIdList(new ArrayList<>());
            return response;
        }
        // 确保 getSuccessMkuIdList 返回的是 Set<Long>
        Set<Long> failMkuIdSet = new HashSet<>(request.getFailMkuIdList());
        // 确保 langMap.keySet 返回的是 List<String>
        List<String> langKeyList = new ArrayList<>(langMap.keySet());
        Map<Long, Map<String, String>> mkuIdLangTitleMap = mkuLangAtomicService.getLangMapByMkuIds(failMkuIdSet, langKeyList);

        List<MkuLangTitleVO> langTitleList = new ArrayList<>();
        mkuIdLangTitleMap.forEach((mkuId, langTitleMap) -> {
            MkuLangTitleVO mkuLangTitleVO = new MkuLangTitleVO();
            mkuLangTitleVO.setMkuId(mkuId);
            Map<String, String> titleMap = new HashMap<>();
            Set<String> dbLangSet = new HashSet<>();
            langTitleMap.forEach((lang, value) -> {
                if (StringUtils.isNotBlank(value)) {
                    dbLangSet.add(lang);
                }
            });
            langMap.forEach((lang, langStr) -> {
                if (!dbLangSet.contains(lang)) {
                    String translate = xiangJiTranslateService.translate(langTitleMap.get(LangConstant.LANG_ZH), LangConstant.LANG_ZH, lang);
                    titleMap.put(lang, translate);
                }
            });
            mkuLangTitleVO.setLangTitleMap(titleMap);
            langTitleList.add(mkuLangTitleVO);
        });

        Map<Long, List<Long>> spuIdMkuIdMap = getSpuMkuRelationNewMap(request.getFailMkuIdList());

        // 更新spu多语言表
        saveSpuLang(spuIdMkuIdMap, langTitleList);
        // 更新草稿表
        saveSpuDraft(spuIdMkuIdMap, langTitleList);
        // 更新mku多语言
        saveMkuLang(spuIdMkuIdMap, langTitleList);
        // 更新mku详情多语言
        saveMkuDescLang(spuIdMkuIdMap, langTitleList);
        // 更新mku草稿表
        saveMkuDraft(spuIdMkuIdMap, langTitleList);
        response.setSuccessMkuIdList(mergeLists(request.getSuccessMkuIdList(), request.getFailMkuIdList()));
        response.setFailMkuIdList(new ArrayList<>());
        return response;
    }

    /**
     * 保存SPU语言信息
     * @param spuIdMkuIdMap SPU ID与MKU ID的映射关系
     * @param langTitleList MKU语言标题列表
     */
    @PFTracing
    private void saveSpuLang(Map<Long, List<Long>> spuIdMkuIdMap, List<MkuLangTitleVO> langTitleList) {
        List<Long> spuIds = new ArrayList<>(spuIdMkuIdMap.keySet());
        String pin = LoginContextHolder.getLoginContextHolder().getPin();
        Map<Long, SpuLangPO> spuIdSpuLangPoMap = spuLangAtomicService.getSpuLangNameBySpuIds(spuIds, LangConstant.LANG_ZH);
        Map<String, SpuLangPO> spuIdLangPOMap = spuLangAtomicService.getMapBySpuId(spuIdMkuIdMap.keySet());
        List<SpuLangPO> spuLangPOList = new ArrayList<>();

        Set<Long> saveSpuIds = new HashSet<>();
        langTitleList.forEach(mkuLangTitleVO -> {
            Long spuId = null;
            Map<String, String> langTitleMap = mkuLangTitleVO.getLangTitleMap();
            for(Map.Entry<Long, List<Long>> spuIdMkuIdMapEntry : spuIdMkuIdMap.entrySet()){
                List<Long> mkuIds = spuIdMkuIdMapEntry.getValue();
                if(mkuIds.contains(mkuLangTitleVO.getMkuId())){
                    spuId = spuIdMkuIdMapEntry.getKey();
                }
            }
            if(saveSpuIds.contains(spuId)){
                return;
            }
            SpuLangPO sourceSpuLangPO = spuIdSpuLangPoMap.get(spuId);
            for(Map.Entry<String, String> langTitleMapEntry : langTitleMap.entrySet()){
                String lang = langTitleMapEntry.getKey();
                String title = langTitleMapEntry.getValue();
                String key = spuId + lang;
                if (!spuIdLangPOMap.containsKey(key)) {
                    String qualifier = xiangJiTranslateService.translate(sourceSpuLangPO.getQualifier(), LangConstant.LANG_ZH, lang);
                    SpuLangPO spuLangPO = new SpuLangPO();
                    spuLangPO.setSpuId(spuId);
                    spuLangPO.setLang(lang);
                    spuLangPO.setSpuTitle(title);
                    spuLangPO.setMainImg(sourceSpuLangPO.getMainImg());
                    spuLangPO.setDetailImg(sourceSpuLangPO.getDetailImg());
                    spuLangPO.setTextExtAttribute(sourceSpuLangPO.getTextExtAttribute());
                    spuLangPO.setQualifier(qualifier);
                    spuLangPO.setSpecification(sourceSpuLangPO.getSpecification());
                    spuLangPO.setKeyPhrases(sourceSpuLangPO.getKeyPhrases());
                    spuLangPO.setCreator(pin);
                    spuLangPO.setUpdater(pin);
                    spuLangPO.setCreateTime(new Date());
                    spuLangPO.setUpdateTime(new Date());
                    spuLangPOList.add(spuLangPO);
                } else {
                    SpuLangPO spuLangPO = spuIdLangPOMap.get(key);
                    spuLangPO.setSpuId(null);
                    spuLangPO.setUpdateTime(new Date());
                    spuLangPO.setUpdater(pin);
                    if (StringUtils.isBlank(spuLangPO.getQualifier())) {
                        String qualifier = xiangJiTranslateService.translate(sourceSpuLangPO.getQualifier(), LangConstant.LANG_ZH, lang);
                        spuLangPO.setQualifier(qualifier);
                    }
                    if (StringUtils.isBlank(spuLangPO.getSpuTitle())) {
                        spuLangPO.setSpuTitle(title);
                    }
                    spuLangPOList.add(spuLangPO);
                }
            }
            saveSpuIds.add(spuId);
        });
        boolean result = spuLangAtomicService.saveOrUpdateBatch(spuLangPOList);
        log.info("CustomerMkuManageServiceImpl saveSpuLang result:{},langTitleList:{},spuLangPOList:{}", result, JSON.toJSONString(langTitleList),
            JSON.toJSONString(spuLangPOList));
    }
    @PFTracing
    private <K, V> K getKeyByValue(Map<K, V> map, V value) {
        for (Map.Entry<K, V> entry : map.entrySet()) {
            if (Objects.equals(entry.getValue(), value)) {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * 保存SPU草稿的方法。
     * @param spuIdMkuIdMap 一个映射SPU ID到MKU ID的Map对象。
     * @param langTitleList 一个包含多语言标题的List对象。
     */
    @PFTracing
    private void saveSpuDraft(Map<Long, List<Long>> spuIdMkuIdMap, List<MkuLangTitleVO> langTitleList) {
        String pin = LoginContextHolder.getLoginContextHolder().getPin();
        Map<Long, MkuLangTitleVO> mkuLangTitleVOMap = langTitleList.stream().collect(Collectors.toMap(MkuLangTitleVO::getMkuId, Function.identity(), (mku1, mku2) -> mku1));

        List<SaveSpuVO> saveSpuVOList = spuDraftManageService.getSaveSpuVoFromDraftListBySpuIds(new HashSet<>(spuIdMkuIdMap.keySet()));
        saveSpuVOList.forEach(saveSpuVO -> {
            SpuVO spuVO = saveSpuVO.getSpuVO();
            List<SpuLangExtendVO> spuLangExtendVOList = spuVO.getSpuLangExtendVOList();
            if (CollectionUtils.isEmpty(spuLangExtendVOList)) {
                spuLangExtendVOList = new ArrayList<>();
            }

            Map<String, SpuLangExtendVO> langExtendVOMap = spuLangExtendVOList.stream().collect(Collectors.toMap(SpuLangExtendVO::getLang, Function.identity(), (spu1, spu2) -> spu1));
            SpuLangExtendVO zhSpuLangExtendVO = langExtendVOMap.get(LangConstant.LANG_ZH);
            Long mkuId = spuIdMkuIdMap.get(spuVO.getSpuId()).get(0);
            MkuLangTitleVO mkuLangTitleVO = mkuLangTitleVOMap.get(mkuId);
            Map<String, String> langTitleMap = mkuLangTitleVO.getLangTitleMap();

            List<SpuLangExtendVO> finalSpuLangExtendVOList = spuLangExtendVOList;
            langTitleMap.forEach((lang, title) -> {
                if (!langExtendVOMap.containsKey(lang)) {
                    SpuLangExtendVO spuLangExtendVO = new SpuLangExtendVO();
                    spuLangExtendVO.setLang(lang);
                    spuLangExtendVO.setSpuTitle(title);
                    String qualifier = xiangJiTranslateService.translate(zhSpuLangExtendVO.getQualifier(), LangConstant.LANG_ZH, lang);
                    spuLangExtendVO.setQualifier(qualifier);
                    finalSpuLangExtendVOList.add(spuLangExtendVO);
                } else {
                    for (SpuLangExtendVO spuLangExtendVO : finalSpuLangExtendVOList) {
                        if (spuLangExtendVO.getLang().equals(lang)) {
                            if (StringUtils.isBlank(spuLangExtendVO.getSpuTitle())) {
                                spuLangExtendVO.setSpuTitle(title);
                            }
                            if (StringUtils.isBlank(spuLangExtendVO.getQualifier())) {
                                String qualifier = xiangJiTranslateService.translate(zhSpuLangExtendVO.getQualifier(), LangConstant.LANG_ZH, lang);
                                spuLangExtendVO.setQualifier(qualifier);
                            }
                            if (StringUtils.isBlank(spuLangExtendVO.getKeyPhrases())) {
                                String keyPhrases = xiangJiTranslateService.translate(zhSpuLangExtendVO.getKeyPhrases(), LangConstant.LANG_ZH, lang);
                                spuLangExtendVO.setKeyPhrases(keyPhrases);
                            }
                        }
                    }
                }
            });
            spuVO.setSpuLangExtendVOList(finalSpuLangExtendVOList);
            spuVO.setUpdater(pin);
        });
        if (CollectionUtils.isNotEmpty(saveSpuVOList)) {
            spuDraftManageService.saveOrUpdateDraftList(saveSpuVOList);
        }
        if (langTitleList.size() == saveSpuVOList.size()) {
            return;
        }
        List<SpuDraftPO> spuDraftPOList = spuDraftAtomicService.batchQuerySpuDraftPoList(spuIdMkuIdMap.keySet());
        Map<Long, SpuDraftPO> spuIdSpuDraftPOMap = spuDraftPOList.stream().collect(Collectors.toMap(SpuDraftPO::getSpuId, Function.identity(), (spu1, spu2) -> spu1));
        List<Long> newSpuIdList = new ArrayList<>();
        spuIdMkuIdMap.forEach((spuId, mkuId) -> {
            if (!spuIdSpuDraftPOMap.containsKey(spuId)) {
                newSpuIdList.add(spuId);
            }
        });
        for (Long spuId : newSpuIdList) {
            SpuDetailVO spuDetailVO = spuReadManageService.getDetailBySpuId(spuId);
            spuDraftManageService.saveOrUpdateDraft(spuDetailVO);
        }
    }

    /**
     * 保存MKU语言信息。
     * @param spuIdMkuIdMap SPu ID与MKU ID的映射关系
     * @param langTitleList MKU语言标题信息列表
     */
    @PFTracing
    private void saveMkuLang(Map<Long, List<Long>> spuIdMkuIdMap, List<MkuLangTitleVO> langTitleList) {
        String pin = LoginContextHolder.getLoginContextHolder().getPin();
        List<MkuLangPO> mkuLangPOList = new ArrayList<>();
        Set<Long> mkuIds = spuIdMkuIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        Map<String, MkuLangPO> mkuIdLangPOMap = mkuLangAtomicService.getMapByMkuId(mkuIds);
        langTitleList.forEach(mkuLangTitleVO -> {
            Map<String, String> langTitleMap = mkuLangTitleVO.getLangTitleMap();
            langTitleMap.forEach((lang, title) -> {
                String key = mkuLangTitleVO.getMkuId() + lang;
                if (!mkuIdLangPOMap.containsKey(key)) {
                    MkuLangPO mkuLangPO = new MkuLangPO();
                    mkuLangPO.setMkuId(mkuLangTitleVO.getMkuId());
                    mkuLangPO.setLang(lang);
                    mkuLangPO.setMkuTitle(title);
                    mkuLangPO.setCreator(pin);
                    mkuLangPO.setUpdater(pin);
                    mkuLangPO.setCreateTime(new Date());
                    mkuLangPO.setUpdateTime(new Date());
                    mkuLangPOList.add(mkuLangPO);
                } else {
                    MkuLangPO mkuLangPO = mkuIdLangPOMap.get(key);
                    mkuLangPO.setUpdateTime(new Date());
                    mkuLangPO.setUpdater(pin);
                    mkuLangPO.setMkuId(null);
                    if (StringUtils.isBlank(mkuLangPO.getMkuTitle())) {
                        mkuLangPO.setMkuTitle(title);
                    }
                    mkuLangPOList.add(mkuLangPO);
                }
            });
        });
        boolean result = mkuLangAtomicService.saveOrUpdateBatch(mkuLangPOList);
        log.info("CustomerMkuManageServiceImpl saveMkuLang result:{},langTitleList:{},mkuLangPOList:{}", result, JSON.toJSONString(langTitleList), JSON.toJSONString(mkuLangPOList));
    }

    /**
     * 保存MKU描述语言信息
     * @param spuIdMkuIdMap SPu ID与MKU ID的映射关系
     * @param langTitleList MKU语言标题列表
     */
    @PFTracing
    private void saveMkuDescLang(Map<Long, List<Long>> spuIdMkuIdMap, List<MkuLangTitleVO> langTitleList) {
        String pin = LoginContextHolder.getLoginContextHolder().getPin();
        List<MkuDescLangPO> mkuLangPOList = new ArrayList<>();
        Set<Long> mkuIds = spuIdMkuIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        Map<String, MkuDescLangPO> mkuIdLangPOMap = mkuDescLangAtomicService.getMapByMkuId(mkuIds);
        langTitleList.forEach(mkuLangTitleVO -> {
            Map<String, String> langTitleMap = mkuLangTitleVO.getLangTitleMap();
            langTitleMap.forEach((lang, title) -> {
                String key = mkuLangTitleVO.getMkuId() + lang;
                String zhKey = mkuLangTitleVO.getMkuId() + LangConstant.LANG_ZH;
                if (mkuIdLangPOMap.containsKey(zhKey)) {
                    if (!mkuIdLangPOMap.containsKey(key)) {
                        MkuDescLangPO mkuDescLangPO = new MkuDescLangPO();
                        mkuDescLangPO.setMkuId(mkuLangTitleVO.getMkuId());
                        mkuDescLangPO.setLang(lang);
                        mkuDescLangPO.setCreator(pin);
                        mkuDescLangPO.setUpdater(pin);
                        mkuDescLangPO.setCreateTime(new Date());
                        mkuDescLangPO.setUpdateTime(new Date());
                        MkuDescLangPO zhMkuDescLangPO = mkuIdLangPOMap.get(zhKey);
                        String description = xiangJiTranslateService.translate(zhMkuDescLangPO.getPcDescription(), LangConstant.LANG_ZH, lang);
                        if (!description.startsWith("<p>")) {
                            description = "<p>" + description;
                        }
                        mkuDescLangPO.setPcDescription(description);
                        mkuLangPOList.add(mkuDescLangPO);
                    } else {
                        MkuDescLangPO zhMkuDescLangPO = mkuIdLangPOMap.get(zhKey);
                        MkuDescLangPO mkuDescLangPO = mkuIdLangPOMap.get(key);
                        mkuDescLangPO.setMkuId(null);
                        mkuDescLangPO.setUpdateTime(new Date());
                        mkuDescLangPO.setUpdater(pin);
                        if (mkuDescLangPO.getPcDescription().equals("<p></p>")) {
                            String description = xiangJiTranslateService.translate(zhMkuDescLangPO.getPcDescription(), LangConstant.LANG_ZH, lang);
                            if (!description.startsWith("<p>")) {
                                description = "<p>" + description;
                            }
                            mkuDescLangPO.setPcDescription(description);
                        }
                        mkuLangPOList.add(mkuDescLangPO);
                    }
                }
            });
        });
        boolean result = mkuDescLangAtomicService.saveOrUpdateBatch(mkuLangPOList);
        log.info("CustomerMkuManageServiceImpl saveMkuDescLang result:{},langTitleList:{},mkuLangPOList:{}", result, JSON.toJSONString(langTitleList), JSON.toJSONString(mkuLangPOList));
    }

    /**
     * 保存MKU草稿信息。
     *
     * @param spuIdMkuIdMap SPu ID与MKU ID的映射关系
     * @param langTitleList MKU语言标题列表
     */
    @PFTracing
    private void saveMkuDraft(Map<Long, List<Long>> spuIdMkuIdMap, List<MkuLangTitleVO> langTitleList) {
        String pin = LoginContextHolder.getLoginContextHolder().getPin();
        Set<Long> mkuIds = spuIdMkuIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        Map<Long, MkuDraftPO> mkuIdMkuDraftPOMap = mkuDraftAtomicService.getMapByMkuId(mkuIds);
        Map<Long, Map<String, String>> mkuIdLangDescMap = mkuDescLangAtomicService.mkuIdDescPoListByMkuIds(mkuIds);
        List<MkuDraftPO> mkuDraftPOList = new ArrayList<>();
        for (Long mkuId : mkuIds) {
            if (!mkuIdMkuDraftPOMap.containsKey(mkuId)) {
                continue;
            }
            MkuDraftPO mkuLangPO = mkuIdMkuDraftPOMap.get(mkuId);
            Map<String, String> langDescMap = mkuIdLangDescMap.get(mkuId);
            mkuLangPO.setPcDescription(JSON.toJSONString(langDescMap));
            mkuLangPO.setMkuId(null);
            mkuLangPO.setUpdater(pin);
            mkuLangPO.setUpdateTime(new Date());
            mkuDraftPOList.add(mkuLangPO);
        }
        if (CollectionUtils.isEmpty(mkuDraftPOList)) {
            return;
        }
        boolean result = mkuDraftAtomicService.saveOrUpdateBatch(mkuDraftPOList);
        log.info("CustomerMkuManageServiceImpl saveMkuDescLang result:{},langTitleList:{},mkuDraftPOList:{}", result,
            JSON.toJSONString(langTitleList), JSON.toJSONString(mkuDraftPOList));
    }

    @Override
    public List<CustomerMkuVO> listCustomerMkuByMkuIdCountry(List<Long> mkuIds, String targetCountryCode) {
        if (CollectionUtils.isEmpty(mkuIds)) {
            return new ArrayList<>();
        }
        List<CustomerMkuPO> customerMkuPOList = customerMkuAtomicService.listCustomerMkuByMkuIdsCountry(mkuIds, targetCountryCode);
        List<CustomerMkuVO> customerMkuVOList = CustomerMkuConvert.INSTANCE.listPo2vo(customerMkuPOList);
        return customerMkuVOList;
    }

    @Override
    public DataResponse<Boolean> bindCustomerToMku(CustomerMkuEachReqVO vo) {
        validateParams(vo);
        try {
            if (vo.getBindStatus().equals(CustomerMkuBindEnum.BIND)) {
                filterMku(vo.getMkuId());
                CustomerMkuBatchReqVO customerMkuBatchReqVO = new CustomerMkuBatchReqVO();
                customerMkuBatchReqVO.setBindStatus(vo.getBindStatus());
                customerMkuBatchReqVO.setClientCode(vo.getClientCode());
                customerMkuBatchReqVO.setGreenPass(vo.getGreenPass());
                Set<Long> mkuSet = new HashSet<>();
                mkuSet.add(vo.getMkuId());
                customerMkuBatchReqVO.setMkuIds(mkuSet);
                DataResponse<Set<Long>> response = batchBind(customerMkuBatchReqVO);
                if (response.getSuccess()) {
                    return DataResponse.success();
                } else {
                    return DataResponse.error(response.getMessage());
                }
            } else {
                Boolean result = remove(vo);
                if(result){
                    return DataResponse.success();
                }else {
                    return DataResponse.error("");
                }
            }
        } catch (CustomerMkuBindException e) {
            log.error("CustomerMkuManageServiceImpl.bindCustomerToMku error vo:{}", JSON.toJSONString(vo), e);
            return DataResponse.error(e.getMsg());
        } catch (Exception e) {
            log.error("CustomerMkuManageServiceImpl.bindCustomerToMku Exception vo:{}", JSON.toJSONString(vo), e);
            return DataResponse.error("系统异常，请联系研发工程师");
        }
    }

    public Boolean remove(CustomerMkuEachReqVO input) {
        CustomerMkuPO existRef = customerMkuAtomicService.getOne(input.getMkuId(), input.getClientCode(), CustomerMkuBindEnum.BIND);
        if (Objects.isNull(existRef)) {
            return false;
        }
        //逻辑解绑客户mku关系
        customerMkuAtomicService.update(new CustomerMkuPO(CustomerMkuBindEnum.INVALID, MkuSpuRelationFlagEnum.NO),
            Wrappers.<CustomerMkuPO>lambdaUpdate().eq(CustomerMkuPO::getMkuId, input.getMkuId())
                .eq(CustomerMkuPO::getClientCode, input.getClientCode()).set(CustomerMkuPO::getSpuId, null));
        log.info("CustomerMkuManageServiceImpl.remove 客户{}移除mku:{}", input.getClientCode(), input.getMkuId());
        //如果当前关系的spuFlag=1,则需要选举新的mku作为主展示mku
        if (MkuSpuRelationFlagEnum.YES.getCode().equals(existRef.getSpuFlag())) {
            customerMkuAtomicService.setNewLeaderMku(input.getClientCode(), existRef.getSpuId(), input.getMkuId(), existRef.getCountryCode());
        }
        //移除客户mku固定sku
        customerMkuPriceManageService.delete(new CustomerMkuPO(input.getClientCode(), input.getMkuId(), ""));
        return true;
    }

    private void validateParams(CustomerMkuEachReqVO vo) {
        if (Objects.isNull(vo)) {
            throw new CustomerMkuBindException(String.format("Customer MKU binding failed: request does not exist"));
        }
        if (Objects.isNull(vo.getMkuId())) {
            throw new CustomerMkuBindException(String.format("Customer MKU binding failed: mkuId %s does not exist", vo));
        }
        if (StringUtils.isBlank(vo.getTargetCountryCode())) {
            throw new CustomerMkuBindException(
                String.format("Customer MKU binding failed: targetCountryCode %s does not exist", vo));
        }
        if (Objects.isNull(vo.getBindStatus())) {
            throw new CustomerMkuBindException(
                String.format("Customer MKU binding failed: bindStatus %s does not exist", vo));
        }
        Map<String, String> clientCodeByCountry = operDuccConfig.getClientCodeByCountry();
        if (clientCodeByCountry.containsKey(vo.getTargetCountryCode())) {
            String clientCode = clientCodeByCountry.get(vo.getTargetCountryCode());
            vo.setClientCode(clientCode);
        } else {
            throw new CustomerMkuBindException(
                String.format("Customer MKU binding failed: clientCodeByCountry %s does not exist", clientCodeByCountry));
        }

        MkuPO mkuPO = mkuAtomicService.getById(vo.getMkuId());
        if( Objects.isNull(mkuPO) || StringUtils.isBlank(mkuPO.getBuyer()) || testErp.contains(mkuPO.getBuyer())){
            throw new CustomerMkuBindException(String.format("Customer MKU binding failed: mku buyer %s is test erp", JSON.toJSONString(mkuPO)));
        }
    }

    private void filterMku(Long mkuId) {
        List<MkuRelationPO> mkuRelationPOList = mkuRelationAtomicService.queryBindListByMkuId(mkuId);
        if(CollectionUtils.isEmpty(mkuRelationPOList)){
            throw new CustomerMkuBindException(String.format("Customer MKU binding failed: mku bind sku %s is null", JSON.toJSONString(mkuId)));
        }
        Map<Long, MkuRelationPO> skuIdMkuMap = mkuRelationPOList.stream().collect(Collectors.toMap(MkuRelationPO::getSkuId, Function.identity()));
        List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(skuIdMkuMap.keySet());
        if(CollectionUtils.isEmpty(skuPOList)){
            throw new CustomerMkuBindException(String.format("Customer MKU binding failed: skuList %s is null", JSON.toJSONString(skuPOList)));
        }
        List<String> supplierList = operDuccConfig.getSupplierList();
        skuPOList.forEach(skuPO -> {
            if(supplierList.contains(skuPO.getVendorCode())){
                throw new CustomerMkuBindException(String.format("Customer MKU binding failed: this sku supplier %s is test supplier", JSON.toJSONString(skuPO)));
            }
        });
    }

    @Override
    @PFTracing
    public MkuCheckBindResVO checkAndBind(MkuCheckBindReqVO reqVO) {
        // 1. 获取客户信息
        Map<String, CustomerVO> customerVOMap = customerManageService.map();
        CustomerVO customerVO = customerVOMap.get(reqVO.getClientCode());
        if (Objects.isNull(customerVO)) {
            throw new BizException("客户信息不存在");
        }
        // 2. 初始化响应对象
        MkuCheckBindResVO response = initializeResponse();

        // 校验权限
        if(erpCheckSwitch) {
            ClientIdVO clientIdVO = customerManageService.erpBindCustomer(reqVO.getUpdater());
            if (!clientIdVO.getClientIdList().contains(reqVO.getClientCode())) {
                String msg = String.format("此erp: %s 账号无操作此客户: %s 绑定商品权限", reqVO.getUpdater(), customerVO.getClientName());
                throw new BizException(msg);
            }
        }

        // 3. 验证和处理MKU列表
        List<Long> mkuIdList = reqVO.getMkuIdList();
        // 4. 执行池验证
        poolVerification(mkuIdList,customerVO,response);
        // 5. 检查SPU修改
        checkSpuAmendVO(mkuIdList,customerVO.getCountry(),response.getFailMkuMsgMap());
        // 6. 执行批量绑定
        processBatchBinding(mkuIdList,reqVO.getClientCode(), response);

        for (Long mkuId : mkuIdList) {
            response.getSuccessMkuMsgMap().put(mkuId,"success");
        }
        return response;
    }

    @PFTracing
    private void processBatchBinding(List<Long> mkuIdList, String clientCode, MkuCheckBindResVO response) {
        if(CollectionUtils.isEmpty(mkuIdList)){
            return;
        }
        CustomerMkuBatchReqVO batchReqVO = new CustomerMkuBatchReqVO();
        batchReqVO.setMkuIds(new HashSet<>(mkuIdList));
        batchReqVO.setClientCode(clientCode);
        batchReqVO.setBindStatus(CustomerMkuBindEnum.BIND);

        DataResponse<Set<Long>> bindResponse = this.batchBind(batchReqVO);
        if (!bindResponse.getSuccess()) {
            Set<Long> data = bindResponse.getData();
            for (Long mkuId : data) {
                response.getFailMkuMsgMap().put(mkuId,bindResponse.getMessage());
                mkuIdList.remove(mkuId);
            }
        }
    }

    private MkuCheckBindResVO initializeResponse() {
        MkuCheckBindResVO response = new MkuCheckBindResVO();
        response.setFailMkuMsgMap(new HashMap<>());
        response.setSuccessMkuMsgMap(new HashMap<>());
        return response;
    }

    @PFTracing
    private void checkSpuAmendVO(List<Long> mkuIdList,String country,Map<Long,String> failMkuMsgMap){
        if(CollectionUtils.isEmpty(mkuIdList)){
            return;
        }
        Map<Long, List<SpuAmendReqVO>> mkuSpuAmendMap = buildMkuToSpuAmendMap(mkuIdList, country);
        processInvalidSpuAmendments(mkuIdList,mkuSpuAmendMap,failMkuMsgMap);
    }


    @PFTracing
    private void processInvalidSpuAmendments(Collection<Long> mkuIds,
        Map<Long, List<SpuAmendReqVO>> mkuSpuAmendMap,
        Map<Long, String> failureMessages) {
        mkuIds.removeIf(mkuId -> {
            List<SpuAmendReqVO> spuAmendments = mkuSpuAmendMap.get(mkuId);
            if (CollectionUtils.isNotEmpty(spuAmendments)) {
                String errorMessage = buildErrorMessage(spuAmendments);
                failureMessages.put(mkuId, errorMessage);
                return true;
            }
            return false;
        });
    }

    /**
     * 构建错误信息
     *
     * @param spuAmendments SPU修改信息列表
     * @return 错误信息
     */
    @PFTracing
    private String buildErrorMessage(List<SpuAmendReqVO> spuAmendments) {
        return spuAmendments.stream()
            .filter(Objects::nonNull)
            .map(this::formatSpuAmendError)
            .collect(Collectors.joining(Constant.COMMA));
    }

    /**
     * 格式化单个SPU修改的错误信息
     *
     * @param spuAmend SPU修改信息
     * @return 格式化后的错误信息
     */
    private String formatSpuAmendError(SpuAmendReqVO spuAmend) {
        String missingFields = String.join(",", spuAmend.getPropertyMap().keySet());
        return String.format("spuId=%s,%s信息为空，不能加入客户池，请检查维护！",
            spuAmend.getSpuId(),
            missingFields);
    }
    @PFTracing
    private Map<Long, List<SpuAmendReqVO>> buildMkuToSpuAmendMap(List<Long> mkuIdList, String country) {
        List<SpuAmendVO> spuAmendList = this.getDetailListByMkuIds(mkuIdList, country);
        return Optional.ofNullable(spuAmendList)
            .map(list -> list.stream()
                .filter(spuAmend -> Objects.nonNull(spuAmend) && !spuAmend.getPropertyMap().isEmpty())
                .map(spuAmend -> convertToSpuAmendRequest(spuAmend, country))
                .collect(Collectors.groupingBy(SpuAmendReqVO::getMkuId)))
            .orElse(Collections.emptyMap());
    }


    /**
     * 将SpuAmendVO对象转换为SpuAmendReqVO对象，并设置国家代码。
     * @param spuAmend SpuAmendVO对象，包含SPU的修改信息。
     * @param country 国家代码。
     * @return 转换后的SpuAmendReqVO对象。
     */
    private SpuAmendReqVO convertToSpuAmendRequest(SpuAmendVO spuAmend, String country) {
        SpuAmendReqVO request = new SpuAmendReqVO();
        request.setSpuId(spuAmend.getSpuId());
        request.setMkuId(spuAmend.getMkuId());
        request.setCountryCode(country);
        request.setPropertyMap(spuAmend.getPropertyMap());
        return request;
    }

    @PFTracing
    private void poolVerification(List<Long> mkuIdList,CustomerVO customerVO,MkuCheckBindResVO mkuCheckBindResVO){
        if(CollectionUtils.isEmpty(mkuIdList)){
            return;
        }
        Map<Long,String> failMap = new HashMap<>();
        Map<Long,String> translateMap = new HashMap<>();
        MkuPoolVerificationReqVO mkuPoolVerificationReqVO  = new MkuPoolVerificationReqVO();
        mkuPoolVerificationReqVO.setSuccessMkuIdList(mkuIdList);
        mkuPoolVerificationReqVO.setClientCode(customerVO.getClientCode());
        mkuPoolVerificationReqVO.setCountryCode(customerVO.getCountry());
        mkuPoolVerificationReqVO.setCheckType(PoolVerificationEnum.CHECK_TARGET_COUNTRY.getCode());
        poolVerification(failMap,translateMap,mkuPoolVerificationReqVO);

        mkuIdList.removeAll(failMap.keySet());
        mkuCheckBindResVO.getFailMkuMsgMap().putAll(failMap);
        mkuCheckBindResVO.getSuccessMkuMsgMap().putAll(translateMap);
    }


    @PFTracing
    private void poolVerification(Map<Long,String> failMap,Map<Long,String> translateMap,MkuPoolVerificationReqVO mkuPoolVerificationReqVO){
        MkuPoolVerificationResVO  mkuPoolVerificationResVO = this.poolVerification(mkuPoolVerificationReqVO);
        log.info("DisposableMkuBindImportHandler.poolVerification mkuPoolVerificationReqVO:{},mkuPoolVerificationResVO:{}", JSON.toJSONString(mkuPoolVerificationReqVO),JSON.toJSONString(mkuPoolVerificationResVO));
        if(PoolVerificationEnum.CHECK_TARGET_COUNTRY.getCode().equals(mkuPoolVerificationResVO.getCheckType())) {
            if(CollectionUtils.isNotEmpty(mkuPoolVerificationResVO.getNotExistMkuIdList())){
                saveMapMsg(failMap,mkuPoolVerificationResVO.getNotExistMkuIdList(),"mku不存在");
                return;
            }
        }else if(PoolVerificationEnum.CHECK_MARKETABILITY.getCode().equals(mkuPoolVerificationResVO.getCheckType())){
            if(CollectionUtils.isNotEmpty(mkuPoolVerificationResVO.getFailMkuIdList())){
                saveMapMsg(failMap,mkuPoolVerificationResVO.getFailMkuIdList(),"MKU下无SKU或没有SKU在上架且过审的状态（在审核中的商品不能加池，须完成审核）；或其商品主站MRO未加池、未上架，或在主站有区域限售导致不可售。");
                return;
            }
            if(CollectionUtils.isEmpty(mkuPoolVerificationResVO.getSuccessMkuIdList())){
                return;
            }
        }else if(PoolVerificationEnum.CHECK_MULTILINGUAL.getCode().equals(mkuPoolVerificationResVO.getCheckType())){
            if(CollectionUtils.isNotEmpty(mkuPoolVerificationResVO.getFailMkuIdList())){
                saveMapMsg(translateMap,mkuPoolVerificationResVO.getFailMkuIdList(),"已机翻");
            }
        }else if(PoolVerificationEnum.UPDATE_MULTILINGUAL.getCode().equals(mkuPoolVerificationResVO.getCheckType())){

        }else if(PoolVerificationEnum.CHECK_COUNTRY_POOL.getCode().equals(mkuPoolVerificationResVO.getCheckType())) {
            String countryName = countryManageService.getCountryNameByCountryCode(mkuPoolVerificationReqVO.getCountryCode(), LangContextHolder.get());
            if(CollectionUtils.isNotEmpty(mkuPoolVerificationResVO.getNotExistMkuIdList())){
                saveMapMsg(failMap,mkuPoolVerificationResVO.getNotExistMkuIdList(),"加池失败，MKU未入国家池，请联系国家店长入【" + countryName + "】国家池");
                return;
            }
        }else if(PoolVerificationEnum.CHECK_SUBTITLE.getCode().equals(mkuPoolVerificationResVO.getCheckType())){
            if(CollectionUtils.isNotEmpty(mkuPoolVerificationResVO.getFailMkuIdList())){
                saveMapMsg(failMap,mkuPoolVerificationResVO.getFailMkuIdList(),"巴葡短标题超过40字符或缺失，无法入池");
                return;
            }
            return;
        }
        mkuPoolVerificationReqVO.setSuccessMkuIdList(mkuPoolVerificationResVO.getSuccessMkuIdList());
        mkuPoolVerificationReqVO.setFailMkuIdList(mkuPoolVerificationResVO.getFailMkuIdList());
        mkuPoolVerificationReqVO.setCheckType(PoolVerificationEnum.getNextCode(mkuPoolVerificationResVO.getCheckType()));
        poolVerification(failMap,translateMap,mkuPoolVerificationReqVO);
    }

    private void saveMapMsg(Map<Long,String> map,List<Long> failMkuIdList,String msg){
        for (Long mkuId : failMkuIdList){
            map.put(mkuId,msg);
        }
    }

    @Override
    public List<CustomerMkuVO> listCustomerMkuPOByMkuIds(CustomerMkuReqVO queryVO) {
        List<Long> mkuIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(queryVO.getMkuIdList())){
            mkuIds.addAll(queryVO.getMkuIdList());
        }
        if(CollectionUtils.isNotEmpty(queryVO.getProductIds())){
            ProductIdVO productIdVO = skuReadManageService.getProductInfoByIds(queryVO.getProductIds());
            if(CollectionUtils.isNotEmpty(productIdVO.getSkuIds())){
                mkuIds.addAll(productIdVO.getSkuIds());
            }
        }
        List<CustomerMkuVO> customerMkuVOS = listCustomerMkuPOByMkuIds(mkuIds, queryVO.getClientCode());
        return customerMkuVOS;
    }

}
