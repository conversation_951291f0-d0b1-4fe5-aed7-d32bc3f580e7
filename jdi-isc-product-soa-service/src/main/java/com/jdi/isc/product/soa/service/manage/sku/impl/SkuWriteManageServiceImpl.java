package com.jdi.isc.product.soa.service.manage.sku.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.BaseResDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.GlobalAttributeVnEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuStatusEnum;
import com.jdi.isc.product.soa.api.sku.req.BatchUpdateCustomsApiReqDTO;
import com.jdi.isc.product.soa.api.sku.req.SkuSyncNcmDTO;
import com.jdi.isc.product.soa.api.sku.req.SkuUpdateApiDTO;
import com.jdi.isc.product.soa.api.sku.req.UpdateCustomsApiDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.*;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.domain.enums.attribute.GlobalAttributeEnum;
import com.jdi.isc.product.soa.domain.enums.taxRate.TaxTypeRelationEnum;
import com.jdi.isc.product.soa.domain.gms.req.JdProductQueryDTO;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.sku.biz.*;
import com.jdi.isc.product.soa.domain.sku.po.*;
import com.jdi.isc.product.soa.domain.spu.biz.GroupPropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;
import com.jdi.isc.product.soa.domain.spu.biz.SaveSpuVO;
import com.jdi.isc.product.soa.domain.spu.po.ProductGlobalAttributePO;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.*;
import com.jdi.isc.product.soa.service.atomic.spu.ProductGlobalAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDraftAtomicService;
import com.jdi.isc.product.soa.service.manage.category.GlobalAttributeManageService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuWriteManageService;
import com.jdi.isc.product.soa.service.manage.spu.SkuCompareService;
import com.jdi.isc.product.soa.service.manage.spu.SpuDraftManageService;
import com.jdi.isc.product.soa.service.manage.spu.validate.SkuValidateService;
import com.jdi.isc.product.soa.service.manage.stock.SkuStockManageService;
import com.jdi.isc.product.soa.service.manage.vendor.VendorManageService;
import com.jdi.isc.product.soa.service.mapstruct.ProductAttributeConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SkuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.category.GroupPropertyConvert;
import com.jdi.isc.product.soa.service.mapstruct.mku.MkuConvert;
import com.jdi.isc.product.soa.service.mapstruct.sku.SkuConvert;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import com.jdi.isc.product.soa.service.support.DataResponseMessageService;
import com.jdi.isc.product.soa.service.support.ProductIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant.SKU_CREATE_ERROR;


/**
 * SKU写服务
 *
 * <AUTHOR>
 * @date 2023/12/5
 **/
@Slf4j
@Service
public class SkuWriteManageServiceImpl implements SkuWriteManageService {

    @Resource
    private SkuConvertService skuConvertService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private ProductIdGenerator productIdGenerator;

    @Resource
    private SkuValidateService skuValidateService;

    @Resource
    private SkuCompareService skuCompareService;

    @Resource
    private SkuBizAtomicService skuBizAtomicService;

    @Resource
    private DataResponseMessageService dataResponseMessageService;

    @Resource
    private MkuAtomicService mkuAtomicService;

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @Resource
    private SkuInfoRpcService skuInfoRpcService;

    @Resource
    private VendorManageService vendorManageService;

    @Resource
    private SkuUniqueAtomicService skuUniqueAtomicService;

    @Resource
    private SkuFeatureAtomicService skuFeatureAtomicService;

    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private SkuCertificateAtomicService skuCertificateAtomicService;

    @Resource
    private SkuStockManageService skuStockManageService;

    @Resource
    private SkuReadManageService skuReadManageService;

    @Resource
    private ProductAttributeConvertService productAttributeConvertService;

    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;

    @Resource
    private GlobalAttributeManageService globalAttributeManageService;

    @Resource
    private SpuDraftManageService spuDraftManageService;

    @Resource
    @Lazy
    private SkuDraftAtomicService skuDraftAtomicService;

    @Resource
    @Lazy
    private SkuWriteManageService skuWriteManageService;

    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;

    @LafValue("jdi.isc.sku.global.attribute")
    private String globalAttribute;

    @Resource
    private SaleAttributeManageService saleAttributeManageService;

    @Override
    @ToolKit
    public List<Long> addSkus(List<SkuVO> skuVOList) {
        // skuId编号生成
        List<Long> skuIds = productIdGenerator.batchGenSkuId((long) skuVOList.size());
        // 处理sku信息
        List<SkuPO> skuPoList = skuConvertService.convertSkuVoListToSkuPo(skuVOList, skuIds);
        // 保存sku列表
        boolean saveBatch = skuAtomicService.saveBatch(skuPoList);

        AssertValidation.isTrue(!saveBatch, SKU_CREATE_ERROR, dataResponseMessageService.getErrorMessage(SKU_CREATE_ERROR));
        log.info("批量保存sku完成,spuId={},skuIds={}", skuVOList.get(0).getSpuId(), skuIds);
        // 插入sku特殊属性
        this.addSkuFeatures(skuVOList);
        // 插入sku唯一编码
        this.batchSaveUniqueCodes(skuVOList);
        // 记录sku上架时间
        this.initSkuBizList(skuPoList);
        // 添加sku级别的资质
        this.addSkuCert(skuVOList);
        // 添加sku级别的跨境属性
        this.addSkuGlobalAttribute(skuVOList);

        return skuIds;
    }

    @Override
    @ToolKit
    public List<Long> updateSkus(List<SkuVO> skuVOList, List<SkuVO> newSkuList, Long spuId) {
        String erp = LoginContextHolder.getLoginContextHolder().getPin();
        // sku校验
        List<SkuPO> dbSkuPoList = skuAtomicService.selectSkuPosBySpuId(spuId);
        skuValidateService.validateUpdateSkuVOList(skuVOList, spuId, dbSkuPoList);
        // 需要更新的sku
        List<SkuVO> updateSkuList = Lists.newArrayList();
        // 筛选出新增的sku列表
        skuVOList.forEach(skuVO -> {
            if (Objects.nonNull(skuVO.getSkuId())) {
                updateSkuList.add(skuVO);
            } else {
                newSkuList.add(skuVO);
            }
        });
        // 生成新sku号 更新的sku列表
        if (CollectionUtils.isNotEmpty(newSkuList)) {
            int size = newSkuList.size();
            List<Long> newSkuIds = productIdGenerator.batchGenSkuId((long) size);
            Map<String, Long> skuKeyToSkuIdMap = new HashMap<>();
            for (int i = 0; i < size; i++) {
                SkuVO skuVO = newSkuList.get(i);
                skuVO.setSkuStatus(SpuStatusEnum.ON_SALE.getStatus());
                skuVO.setSkuId(newSkuIds.get(i));
                skuKeyToSkuIdMap.put(skuVO.getSkuKey(), skuVO.getSkuId());
            }

            // 绑定新增的sku的销售属性关系
            Boolean result = saleAttributeManageService.updateDraftSaleAttributeRelationsForLocalSkuApprovel(skuKeyToSkuIdMap); // ZHAOYAN_SALE_ATTR

            if (Boolean.TRUE.equals(result)) {
                log.info("SkuWriteManageServiceImpl.updateSkus 本土品销售属性关系转换成功, spuId={}, 转换数量={}",
                        spuId, skuKeyToSkuIdMap.size());
            } else {
                log.warn("SkuWriteManageServiceImpl.updateSkus 本土品销售属性关系转换失败, spuId={}", spuId);
            }

            skuAtomicService.saveBatch(SkuConvert.INSTANCE.listVO2PO(newSkuList));

            // 插入sku特殊属性
            this.addSkuFeatures(newSkuList);
            // 添加sku级别的资质
            this.addSkuCert(newSkuList);
            // 添加sku级别的跨境属性
            this.addSkuGlobalAttribute(newSkuList);
        }
        List<SkuPO> skuPoList = SkuConvert.INSTANCE.listVO2PO(updateSkuList);
        // 对比更新sku的值，剔除不需要更新的字段
        compareSkuListAndRemoveEqualSku(spuId, skuPoList);

        skuPoList.forEach(po -> {
            // 更新时不能更新spuId，因为有索引，需要设置为null
            po.setSpuId(null);
            po.setUpdater(erp);
            po.setUpdateTime(new Date());
        });
        skuAtomicService.updateBatchById(skuPoList);
        // 更新sku特殊属性
        this.updateSkuFeatures(updateSkuList);
        // 更新sku资质
        this.upsertSkuCert(updateSkuList);
        // 更新skuGlobalAttribute
        this.addSkuGlobalAttribute(updateSkuList);
        // todo 更新和添加销售属性
        //this.addSkuSaleAttribute(updateSkuList);

        // 更新数据库
        return skuPoList.stream().filter(Objects::nonNull).map(SkuPO::getSkuId).collect(Collectors.toList());
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Map<Long, SkuUpdateApiDTO> updateSkuInfo(List<SkuVO> skuVOList, Map<Long, SkuUpdateApiDTO> resultMap) {
        List<SkuVO> updateSkuList = new ArrayList<>();
        // 检查SKU是否存在
        checkSku(skuVOList,updateSkuList,resultMap);
        updateSkuList = checkJdSku(updateSkuList,resultMap);
        List<SkuPO> skuPoList = SkuConvert.INSTANCE.listVO2PO(updateSkuList);
        log.info("SkuWriteManageServiceImpl.updateSkuInfo skuVOList:{},skuPoList:{}", JSON.toJSONString(skuVOList), JSON.toJSONString(skuPoList));
        this.updateSku(skuPoList,resultMap);
        this.updateSkuFeature(updateSkuList,resultMap);
        return resultMap;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public String updateGlobalAttribute(SkuGlobalAttributeVO param) {

        // 合法性校验
        this.checkUpdateGlobalAttribute(param);

        // 合理性校验
        List<SkuGlobalAttributeDetailVO> params = param.getDetailData();
        List<Long> skuIds = params.stream().map(SkuGlobalAttributeDetailVO::getSkuId).collect(Collectors.toList());
        List<SkuPO> skuPOS = skuAtomicService.queryBySkuIds(new HashSet<>(skuIds));
        this.checkUpdateGlobalAttribute(param,skuIds,skuPOS);

        List<ProductGlobalAttributePO> productGlobalAttributePOS = productGlobalAttributeAtomicService.getBySkuIds(skuIds);
        Map<Long,List<ProductGlobalAttributePO>> skuGlobalAttributeMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(productGlobalAttributePOS)){
            skuGlobalAttributeMap.putAll(productGlobalAttributePOS.stream().collect(Collectors.groupingBy(ProductGlobalAttributePO::getKeyId)));
        }

        // 更新主表
        Map<String,Long> attributeRelationMap = getUpdateAttribute(param.getTargetCountryCode());
        Set<Long> attributeIds = new HashSet<>(attributeRelationMap.values());
        Set<String> targetCountryCodes = this.getAllUpdateCountryCode();
        // 将主表解析出来，变更+新增后、然后统一更新
        Map<String,List<SkuPO>> groupSkus = skuPOS.stream().collect(Collectors.groupingBy(item->StringUtils.joinWith("_",item.getJdCatId(),item.getSourceCountryCode())));

        Map<Long,SkuGlobalAttributeDetailVO> skuGlobalAttributeDetailVOMap = param.getDetailData().stream().collect(Collectors.toMap(SkuGlobalAttributeDetailVO::getSkuId,Function.identity()));

        List<ProductGlobalAttributePO> allGlobalAttributePOS = new ArrayList<>();
        List<ProductGlobalAttributePO> updateGlobalAttributePOS = new ArrayList<>();
        groupSkus.forEach((k,v)->{
            SkuPO skuPO = v.get(0);
            List<GroupPropertyVO> skuInterGroupPropertyVOS = globalAttributeManageService.queryGroupPropertyVos(skuPO.getJdCatId(),skuPO.getSourceCountryCode(),targetCountryCodes, AttributeDimensionEnum.SKU.getCode(), LangConstant.LANG_ZH, null);
            List<PropertyVO> propertyVOS = new ArrayList<>();
            for(GroupPropertyVO groupPropertyVO : skuInterGroupPropertyVOS){
                propertyVOS.addAll(groupPropertyVO.getPropertyVOS());
            }

            Map<Long,PropertyVO> propertyVOMap = propertyVOS.stream().collect(Collectors.toMap(PropertyVO::getAttributeId,Function.identity()));

            v.forEach(item->{
                List<ProductGlobalAttributePO> skuGlobalAttributePOS = skuGlobalAttributeMap.get(item.getSkuId());
                List<GroupPropertyVO> skuInterPropertyList = productAttributeConvertService.convertAttributeFromDB(skuGlobalAttributePOS,skuInterGroupPropertyVOS);
                Map<String,Object> globalAttributeDetailMap = skuGlobalAttributeDetailVOMap.get(item.getSkuId()).getUpdateData();

                for(GroupPropertyVO groupPropertyVO : skuInterPropertyList){
                    if(CollectionUtils.isEmpty(groupPropertyVO.getPropertyVOS())){
                        continue;
                    }
                    for(PropertyVO propertyVO : groupPropertyVO.getPropertyVOS()){
                        if(!attributeIds.contains(propertyVO.getAttributeId())){
                            continue;
                        }
                        List<PropertyValueVO> valueVOS = new ArrayList<>();
                        globalAttributeDetailMap.forEach((m,n)->{
                            Long attributeId = attributeRelationMap.get(m);
                            PropertyVO partProperty = propertyVOMap.get(attributeId);
                            String value = n != null ? n.toString() : "";
                            if(attributeId.equals(propertyVO.getAttributeId())){
                                this.processAttributeValue(param.getTargetCountryCode(),m, value, partProperty, valueVOS);
                            }
                        });
                        propertyVO.setPropertyValueVOList(valueVOS);
                        valueVOS.forEach(valueVO->{
                            if(Boolean.TRUE.equals(valueVO.getSelected())){
                                ProductGlobalAttributePO attributePO = new ProductGlobalAttributePO();
                                attributePO.setAttributeId(valueVO.getAttributeId());
                                attributePO.setAttributeValueId(valueVO.getAttributeValueId());
                                attributePO.setKeyId(skuPO.getSkuId());
                                attributePO.setKeyType(KeyTypeEnum.SKU.getCode());
                                if(StringUtils.isNotBlank(valueVO.getAttributeValueName())
                                        && AttributeInputTypeEnum.TEXT.getCode().equals(propertyVO.getAttributeInputType())){
                                    attributePO.setAttributeValue(valueVO.getAttributeValueName());
                                }
                                updateGlobalAttributePOS.add(attributePO);
                            }
                        });
                    }
                }
                List<ProductGlobalAttributePO> productGlobalAttributePOS1 = productAttributeConvertService.convertSkuAttribute(skuPO.getSkuId(), skuInterPropertyList);
                allGlobalAttributePOS.addAll(productGlobalAttributePOS1);

            });
        });

        Map<Long,List<ProductGlobalAttributePO>> allGlobalAttributeMap = allGlobalAttributePOS.stream().collect(Collectors.groupingBy(ProductGlobalAttributePO::getKeyId));

        // 更新草稿表
        List<SaveSpuVO> updateDraftVOS = new ArrayList<>();
        groupSkus.forEach((k,v)->{
            SkuPO skuPOSample = v.get(0);
            List<GroupPropertyVO> skuInterGroupPropertyVOS = globalAttributeManageService.queryGroupPropertyVos(skuPOSample.getJdCatId(),skuPOSample.getSourceCountryCode(),targetCountryCodes, AttributeDimensionEnum.SKU.getCode(), LangConstant.LANG_ZH, null);
            for(SkuPO skuPO : v){
                SaveSpuVO saveSpuVO = spuDraftManageService.getSaveSpuVoFromDraftBySpuId(skuPO.getSpuId());
                List<SkuVO> skuVOList = saveSpuVO.getSkuVOList();
                for(SkuVO skuVO : skuVOList){
                    List<ProductGlobalAttributePO> allGlobalAttributePOS1 = allGlobalAttributeMap.get(skuVO.getSkuId());
                    if(CollectionUtils.isNotEmpty(allGlobalAttributePOS1)){
                        List<GroupPropertyVO> groupPropertyVOS = productAttributeConvertService.convertAttributeFromDB(allGlobalAttributePOS1,skuInterGroupPropertyVOS);
                        skuVO.setSkuInterPropertyList(groupPropertyVOS);
                    }
                }
                updateDraftVOS.add(saveSpuVO);
            }
        });
        for(SaveSpuVO saveSpuVO : updateDraftVOS){
            spuDraftManageService.saveOrUpdateDraft(saveSpuVO);
        }

        if(CollectionUtils.isNotEmpty(updateGlobalAttributePOS)){
            productGlobalAttributeAtomicService.deleteBySkuIdsAndAttributeIds(skuIds,attributeIds);
            productGlobalAttributeAtomicService.saveOrUpdateBatch(updateGlobalAttributePOS);
        }
        return "";
    }

    private void updateSkuFeature(List<SkuVO> updateSkuList,Map<Long, SkuUpdateApiDTO> resultMap){
        try {
            this.updateSkuFeatures(updateSkuList);
        }catch (Exception e){
            log.error("【系统异常】SkuWriteManageServiceImpl.updateSkuFeature updateSkuList:{},err:{}",JSON.toJSONString(updateSkuList),e.getMessage(),e);
            updateSkuList.forEach(skuVO -> {
                SkuUpdateApiDTO skuUpdateApiDTO = resultMap.get(skuVO.getSkuId());
                skuUpdateApiDTO.setValid(false);
                skuUpdateApiDTO.setResult("更新商品属性信息失败");
                resultMap.put(skuUpdateApiDTO.getSkuId(),skuUpdateApiDTO);
            } );
            throw new RuntimeException("更新商品属性信息失败");
        }
    }



    private List<SkuVO> checkJdSku(List<SkuVO> skuVOList,Map<Long, SkuUpdateApiDTO> resultMap){
        List<SkuVO> updateSkuList = new ArrayList<>();
        List<SkuVO> jdSkuList = new ArrayList<>();
        for (SkuVO skuVO : skuVOList){
            if(Objects.nonNull(skuVO.getJdSkuId())){
                Boolean result = skuValidateService.validateJdSkuIdExist(skuVO.getJdSkuId(), skuVO.getSkuId());
                if(result){
                    SkuUpdateApiDTO skuUpdateApiDTO = resultMap.get(skuVO.getSkuId());
                    skuUpdateApiDTO.setValid(false);
                    skuUpdateApiDTO.setResult("此JDSKUID已经绑定其他SKUID");
                    resultMap.put(skuUpdateApiDTO.getSkuId(),skuUpdateApiDTO);
                }else {
                    jdSkuList.add(skuVO);
                }
            }else {
                updateSkuList.add(skuVO);
            }
        }
        if(CollectionUtils.isEmpty(jdSkuList)){
            return updateSkuList;
        }

        List<Long> jdSkuIdList = jdSkuList.stream().map(SkuVO::getJdSkuId).collect(Collectors.toList());
        // 查询jdSku信息
        JdProductQueryDTO param = new JdProductQueryDTO();
        param.setSkuIds(jdSkuIdList);
        Map<Long, JdProductDTO> jdSkuMap = skuInfoRpcService.querySkuMap(param);
        for (SkuVO skuVO : jdSkuList){
            JdProductDTO jdProductDTO = jdSkuMap.get(skuVO.getJdSkuId());
            if(Objects.nonNull(jdProductDTO) && Objects.nonNull(jdProductDTO.getProductId())){
                updateSkuList.add(skuVO);
            }else {
                SkuUpdateApiDTO skuUpdateApiDTO = resultMap.get(skuVO.getSkuId());
                skuUpdateApiDTO.setValid(false);
                skuUpdateApiDTO.setResult("此JDSKUID商品不存在");
                resultMap.put(skuUpdateApiDTO.getSkuId(),skuUpdateApiDTO);
            }
        }
        log.info("SkuWriteManageServiceImpl.checkJdSku updateSkuList:{},jdSkuMap:{}", JSON.toJSONString(updateSkuList),JSON.toJSONString(jdSkuMap));
        return updateSkuList;
    }




    /**
     * 检查 SKU 列表中的每个 SKU 是否存在于数据库中，并更新结果集。
     * @param skuVOList 要检查的 SKU 列表。
     * @param updateSkuList 用于存储存在于数据库中的 SKU 的列表。
     * @param resultMap 结果集，包含每个 SKU 的更新状态和结果信息。
     */
    private void checkSku(List<SkuVO> skuVOList,List<SkuVO> updateSkuList,Map<Long, SkuUpdateApiDTO> resultMap){
        Set<Long> skuIdSet = skuVOList.stream().map(SkuVO::getSkuId).collect(Collectors.toSet());
        Map<Long, SkuPO> skuMap = skuAtomicService.batchQuerySkuPO(skuIdSet);
        for (SkuVO skuVO : skuVOList){
            if(skuMap.containsKey(skuVO.getSkuId())){
                updateSkuList.add(skuVO);
            }else {
                SkuUpdateApiDTO skuUpdateApiDTO = resultMap.get(skuVO.getSkuId());
                skuUpdateApiDTO.setValid(false);
                skuUpdateApiDTO.setResult("此国际SKUID商品不存在");
                resultMap.put(skuUpdateApiDTO.getSkuId(),skuUpdateApiDTO);
            }
        }
    }

    private void updateSku(List<SkuPO> skuPoList,Map<Long, SkuUpdateApiDTO> resultMap){
        try {
            skuPoList.forEach(po -> {
                // 更新时不能更新spuId，因为有索引，需要设置为null
                po.setSpuId(null);
                po.setUpdateTime(new Date());
            });
            log.info("SkuWriteManageServiceImpl.updateSku skuPoList:{}", JSON.toJSONString(skuPoList));
            skuAtomicService.updateBatchById(skuPoList);
        }catch (Exception e){
            log.error("【系统异常】SkuWriteManageServiceImpl.updateSku skuPoList:{},err:{}",JSON.toJSONString(skuPoList),e.getMessage(),e);
            skuPoList.forEach(skuPO -> {
                SkuUpdateApiDTO skuUpdateApiDTO = resultMap.get(skuPO.getSkuId());
                skuUpdateApiDTO.setValid(false);
                skuUpdateApiDTO.setResult("更新商品信息失败");
                resultMap.put(skuUpdateApiDTO.getSkuId(),skuUpdateApiDTO);
            } );
            throw new RuntimeException("更新商品信息失败");
        }
    }

    public static List<List<SkuVO>> partitionList(List<SkuVO> list, int size) {
        List<List<SkuVO>> partitionedLists = new ArrayList<>();
        int totalSize = list.size();
        for (int i = 0; i < totalSize; i += size) {
            partitionedLists.add(new ArrayList<>(list.subList(i, Math.min(totalSize, i + size))));
        }
        return partitionedLists;
    }

    private void compareSkuListAndRemoveEqualSku(Long spuId, List<SkuPO> skuPoList) {
        // 查询已经存储的sku列表
        List<SkuPO> dbSkuPoList = getSkuPoList(spuId);
        Map<Long, SkuPO> skuPoMap = dbSkuPoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(SkuPO::getSkuId, Function.identity()));
        List<SkuPO> removeList = Lists.newArrayList();
        for (SkuPO po : skuPoList) {
            if (skuPoMap.containsKey(po.getSkuId())) {
                SkuPO dbSkuPo = skuPoMap.get(po.getSkuId());
                if (skuCompareService.compareSkuPo(dbSkuPo, po)) {
                    removeList.add(po);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(removeList)) {
            skuPoList.removeAll(removeList);
        }
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public List<Long> saveOrUpdateSkuBiz(Long spuId, Date onlineTime) {
        List<SkuPO> skuPoList = getSkuPoList(spuId);

        Pair<List<Long>, List<SkuBizPO>> pair = pairSkuIdAndDbBiz(skuPoList);
        List<SkuBizPO> skuBizPoList = new ArrayList<>(skuPoList.size());
        // 无sku业务记录时，插入新sku业务记录
        if (CollectionUtils.isEmpty(pair.getRight())) {
            for (SkuPO po : skuPoList) {
                SkuBizPO skuBizPo = new SkuBizPO();
                skuBizPo.setSkuId(po.getSkuId());
                skuBizPo.setSkuOnlineTime(onlineTime);
                skuBizPo.setYn(YnEnum.YES.getCode());
                skuBizPoList.add(skuBizPo);
            }
            skuBizAtomicService.saveBatch(skuBizPoList);
        } else {
            // 存在sku业务记录时，更新业务记录时间,取更新时间最大的值更新
            Map<Long, SkuBizPO> skuIdBizMap = pair.getRight().stream().filter(Objects::nonNull).collect(Collectors.toMap(SkuBizPO::getSkuId, Function.identity(),
                    BinaryOperator.maxBy(Comparator.comparing(SkuBizPO::getUpdateTime))));
            for (SkuPO po : skuPoList) {
                SkuBizPO skuBizPo = new SkuBizPO();
                if (skuIdBizMap.containsKey(po.getSkuId())) {
                    skuBizPo = skuIdBizMap.get(po.getSkuId());
                    skuBizPo.setSkuId(null);
                } else {
                    skuBizPo.setSkuId(po.getSkuId());
                }
                skuBizPo.setSkuOnlineTime(onlineTime);
                skuBizPo.setYn(YnEnum.YES.getCode());
                skuBizPoList.add(skuBizPo);
            }
            skuBizAtomicService.saveOrUpdateBatch(skuBizPoList);
        }
        return pair.getLeft();
    }

    public void initSkuBizList(List<SkuPO> skuPoList) {
        final Long spuId = skuPoList.get(0).getSpuId();
        // spu为上架状态时，记录sku上架时间
        if (spuAtomicService.onSaleCount(spuId) > 0) {
            final Date onlineTime = new Date();
            List<SkuBizPO> skuBizPoList = new ArrayList<>(skuPoList.size());
            for (SkuPO po : skuPoList) {
                SkuBizPO skuBizPo = new SkuBizPO();
                skuBizPo.setSkuId(po.getSkuId());
                skuBizPo.setSkuOnlineTime(onlineTime);
                skuBizPo.setYn(YnEnum.YES.getCode());
                skuBizPoList.add(skuBizPo);
            }
            skuBizAtomicService.saveBatch(skuBizPoList);
        }
    }

    public void addSkuCert(List<SkuVO> skuVOList) {
        if (CollectionUtils.isEmpty(skuVOList)) {
            log.info("SkuWriteManageServiceImpl.addSkuCert skuVOList is null");
            return;
        }

        // sku属性
        List<SkuCertificatePO> certificatePOS = new ArrayList<>();
        for(SkuVO skuVO : skuVOList){
            List<GroupSkuCertificateVO> groupSkuCertificateVOS = skuVO.getSkuCertificateVOList();
            if(CollectionUtils.isEmpty(groupSkuCertificateVOS)){
                continue;
            }
            for(GroupSkuCertificateVO groupSkuCertificateVO : groupSkuCertificateVOS){
                if(CollectionUtils.isEmpty(groupSkuCertificateVO.getSkuCertificateVOS())){
                    continue;
                }
                for(SkuCertificateVO skuCertificateVO : groupSkuCertificateVO.getSkuCertificateVOS()){
                    if(StringUtils.isBlank(skuCertificateVO.getCertificatePath())){
                        continue;
                    }
                    SkuCertificatePO skuCertificatePO = SkuConvert.INSTANCE.skuCertVo2Po(skuCertificateVO);
                    skuCertificatePO.setSkuId(skuVO.getSkuId());
                    skuCertificatePO.setRequirement(groupSkuCertificateVO.getRequirement());
                    skuCertificatePO.setCreateTime(new Date());
                    skuCertificatePO.setUpdateTime(new Date());
                    certificatePOS.add(skuCertificatePO);
                }
            }
        }

        if(CollectionUtils.isNotEmpty(certificatePOS)){
            log.info("SkuWriteManageServiceImpl.addSkuCert 新增属性资质,入参:[certificatePOS={}]", JSON.toJSONString(certificatePOS));
            skuCertificateAtomicService.saveBatch(certificatePOS);
        }
    }

    private void addSkuGlobalAttribute(List<SkuVO> skuVOList){
        if (CollectionUtils.isEmpty(skuVOList)) {
            log.info("SkuWriteManageServiceImpl.addSkuGlobalAttribute skuVOList is null");
            return;
        }



        // sku属性
        for(SkuVO skuVO : skuVOList){
            if (CollectionUtils.isEmpty(skuVO.getSkuInterPropertyList())) {
                continue;
            }

            Long skuId = skuVO.getSkuId();
            List<ProductGlobalAttributePO> skuGlobalAttributePOS = productAttributeConvertService.convertSkuAttribute(skuId, skuVO.getSkuInterPropertyList());

            // 更新时、如果存在就删除
            List<ProductGlobalAttributePO> globalAttributePOS = productGlobalAttributeAtomicService.getBySkuId(skuId);
            if(CollectionUtils.isNotEmpty(globalAttributePOS)){
                globalAttributePOS.forEach(item->{
                    item.setKeyId(null);
                    item.setUpdateTime(new Date().getTime());
                    item.setYn(YnEnum.NO.getCode());
                });
                productGlobalAttributeAtomicService.saveOrUpdateBatch(globalAttributePOS);
            }
            productGlobalAttributeAtomicService.saveBatch(skuGlobalAttributePOS);
        }
    }

    public void upsertSkuCert(List<SkuVO> skuVOList) {
        if (CollectionUtils.isEmpty(skuVOList)) {
            log.error("SkuWriteManageServiceImpl.upsertSkuCert skuVOList is null");
            return;
        }

        Set<Long> skuIds = skuVOList.stream().filter(Objects::nonNull).map(SkuVO::getSkuId).collect(Collectors.toSet());
        skuCertificateAtomicService.removeBySkuIds(skuIds);

        this.addSkuCert(skuVOList);
    }

    private void batchSaveUniqueCodes(List<SkuVO> skuVOList) {
        boolean uniqueCodeNull = skuVOList.stream().anyMatch(skuVO -> StringUtils.isBlank(skuVO.getUniqueCode()));
        if (!uniqueCodeNull) {
            List<SkuUniqueShardingPO> uniqueShardingPoList = SkuConvert.INSTANCE.listSkuVo2UniquePo(skuVOList);
            boolean saveBatch = skuUniqueAtomicService.saveBatch(uniqueShardingPoList);
            if (!saveBatch) {
                throw new BizException("保存供应商唯一编码失败");
            }
        }
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public List<Long> removeSkuBiz(Long spuId) {
        List<SkuPO> skuPoList = getSkuPoList(spuId);

        Pair<List<Long>, List<SkuBizPO>> pair = pairSkuIdAndDbBiz(skuPoList);
        if (CollectionUtils.isEmpty(pair.getRight())) {
            return pair.getLeft();
        }
        pair.getRight().forEach(biz -> biz.setYn(YnEnum.NO.getCode()));
        skuBizAtomicService.updateBatchById(pair.getRight());
        return pair.getLeft();
    }

    private List<SkuPO> getSkuPoList(Long spuId) {
        return skuAtomicService.selectSkuPosBySpuId(spuId);
    }

    private Pair<List<Long>, List<SkuBizPO>> pairSkuIdAndDbBiz(List<SkuPO> skuPoList) {
        List<Long> skuIds = skuPoList.stream().map(SkuPO::getSkuId).collect(Collectors.toList());
        // 查询sku业务记录
        List<SkuBizPO> dbBizList = skuBizAtomicService.list(new LambdaQueryWrapper<SkuBizPO>().in(SkuBizPO::getSkuId, skuIds));
        return Pair.of(skuIds, dbBizList);
    }


    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> updateSkuInfoRelationMku(SkuVO skuVO) {

        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>().eq(SkuPO::getSkuId, skuVO.getSkuId()).eq(SkuPO::getYn, YnEnum.YES.getCode());
        SkuPO skuPo = skuAtomicService.getOne(queryWrapper);
        AssertValidation.isEmpty(skuPo, String.format("SKU id %s 不存在", skuVO.getSkuId()));
        // 更新sku
        skuAtomicService.updateById(SkuConvert.INSTANCE.vo2po(skuVO));
        // 更新sku属性
        SkuFeaturePO featurePO = skuFeatureAtomicService.getOne(Wrappers.lambdaQuery(SkuFeaturePO.class).eq(SkuFeaturePO::getSkuId, skuPo.getSkuId()).eq(SkuFeaturePO::getYn,YnEnum.YES.getCode()));
        if (Objects.isNull(featurePO)) {
            skuVO.setSpuId(skuPo.getSpuId());
            skuVO.setCreator(skuVO.getUpdater());
            this.addSkuFeatures(Lists.newArrayList(skuVO));
        }else if (this.containSkuFeature(skuVO)){
            this.updateSkuFeatures(Lists.newArrayList(skuVO));
        }

        // 查询mku和sku关系
        List<MkuRelationPO> mkuRelationPoList = mkuRelationAtomicService.listBySkuId(skuVO.getSkuId());
        if (CollectionUtils.isEmpty(mkuRelationPoList)) {
            return DataResponse.success();
        }

        List<MkuPO> mkuPoList = Lists.newArrayListWithCapacity(mkuRelationPoList.size());
        for (MkuRelationPO mkuRelationPo : mkuRelationPoList) {
            MkuPO mkuPo = MkuConvert.INSTANCE.skuVo2mkuPO(skuVO);
            mkuPo.setMkuId(mkuRelationPo.getMkuId());
            mkuPoList.add(mkuPo);
        }
        // 更新mku
        mkuAtomicService.saveOrUpdateBatch(mkuPoList);
        return DataResponse.success();
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public void saveProductGlobalNcmAttribute(SkuSyncNcmDTO input) {

        Long jdSkuId = input.getJdSkuId();
        String ncmCode = input.getNcmCode();
        String updater = input.getPin();

        // JDSKUID 和进口sku 是1对1关系, 历史数据可能存在1对多场景, 所以这里查到多条sku,NCM码都需要修改一下
        List<SkuPO> skus = skuAtomicService.listSkuPoByJdSkuId(jdSkuId);

        if (CollectionUtils.isEmpty(skus)) {
            log.warn("此jdskuid:{} 无对应的国际sku，请先督促跨境采销完成跨境商品二发。", jdSkuId);
            return;
        }

        if (StringUtils.isBlank(ncmCode)) {
            ncmCode = StringUtils.EMPTY;
        }

        log.info("批量更新商品的ncm码, skus={}, ncmCode={}, updater={}", com.alibaba.fastjson.JSONObject.toJSONString(skus), ncmCode, updater);

        // 查找国际品且包含巴西的skuId
        Set<Long> skuIds = this.getBrSkuId(skus);

        if (CollectionUtils.isEmpty(skuIds)) {
            log.info("无国际包含巴西的sku, 无需更新");
            return;
        }

        // 查询sku的属性
        List<ProductGlobalAttributePO> productGlobalAttributes = productGlobalAttributeAtomicService.queryGlobalAttributeListByIds(skuIds, Sets.newHashSet(TaxTypeRelationEnum.NCM.getGlobalAttributeId()), KeyTypeEnum.SKU.getCode());

        log.info("NCM 属性列表, productGlobalAttributes={}", com.alibaba.fastjson.JSONObject.toJSONString(productGlobalAttributes));

        if (CollectionUtils.isNotEmpty(productGlobalAttributes)) {
            // 更新属性值
            for (ProductGlobalAttributePO item : productGlobalAttributes) {
                if (StringUtils.equals(item.getAttributeValue(), ncmCode)) {
                    log.info("ncmCode 相同，无需更新, ncmCode={}", ncmCode);
                    continue;
                }

                log.info("ncmCode 不相同，before={}, after={}", item.getAttributeValue(), ncmCode);

                ProductGlobalAttributePO update = new ProductGlobalAttributePO();
                update.setId(item.getId());
                // 主要就是更新这个字段
                update.setAttributeValue(ncmCode);
                if (StringUtils.isNotEmpty(updater)) {
                    update.setUpdater(updater);
                }
                update.setUpdateTime(new Date().getTime());

                boolean success = productGlobalAttributeAtomicService.updateById(update);
                log.info("同步ncm码到sku属性完成, result={}", success);
            }
        } else {
            log.info("无ncm码属性，初始化一条数据, skuIds={}, ncmCode={}", skuIds, ncmCode);
            for (Long skuId : skuIds) {
                ProductGlobalAttributePO insert = getProductGlobalAttributePO(skuId, ncmCode, updater);
                productGlobalAttributeAtomicService.save(insert);
            }
        }


        // 更新草稿中的ncm码
        Map<Long, SkuDraftPO> draftMap = skuDraftAtomicService.querySkuInterPropertyMapBySkuIds(skuIds);
        if (MapUtils.isEmpty(draftMap)) {
            log.info("无草稿，无需更新, skuIds={}", skuIds);
            return;
        }

        for (SkuPO item : skus) {

            SkuDraftPO skuDraftPO = draftMap.get(item.getSkuId());

            if (skuDraftPO == null) {
                log.info("无草稿信息. skuId={}", item.getSkuId());
                continue;
            }

            // 更新草稿中的ncm码
            skuWriteManageService.syncUpdateSkuDraftNcmCode(ncmCode, skuDraftPO, updater);
        }
    }

    private Set<Long> getBrSkuId(List<SkuPO> skus) {
        // skuId和spuId 的映射关系
        Map<Long, Long> skuIdspuIdMap = skus.stream()
                .filter(item -> StringUtils.equals(item.getSourceCountryCode(), CountryConstant.COUNTRY_ZH))
                .filter(item -> item.getSkuId() != null && item.getSpuId() != null)
                .collect(Collectors.toMap(SkuPO::getSkuId, SkuPO::getSpuId, (k1, k2) -> k1));

        if (MapUtils.isEmpty(skuIdspuIdMap)) {
            return Collections.emptySet();
        }

        Map<Long, SpuPO> spuMap = spuAtomicService.getSpuMap(Sets.newHashSet(skuIdspuIdMap.values()));

        Set<Long> result = Sets.newHashSet();

        skuIdspuIdMap.forEach((skuId, spuId) -> {
            SpuPO spu = spuMap.get(spuId);
            if (spu == null) {
                log.warn("找不到spu信息，无需更新, skuId={}, spuId={}", skuId, spuId);
                return;
            }

            // 属性范围, 判断是否有巴西
            String attributeScope = spu.getAttributeScope();

            if (StringUtils.isBlank(attributeScope)) {
                log.warn("属性范围为空, skuId={}, spuId={}", skuId, spuId);
                return;
            }

            List<String> countryCodes = Splitter.on(",").splitToList(attributeScope);

            // 属性范围包含巴西的，才需要更新
            if (countryCodes.contains(CountryConstant.COUNTRY_BR)) {
                result.add(skuId);
            }
        });

        return result;
    }

    @NotNull
    private static ProductGlobalAttributePO getProductGlobalAttributePO(Long skuId, String ncmCode, String updater) {
        ProductGlobalAttributePO insert = new ProductGlobalAttributePO();
        insert.setKeyType(KeyTypeEnum.SKU.getCode());
        insert.setKeyId(skuId);
        insert.setAttributeId(TaxTypeRelationEnum.NCM.getGlobalAttributeId());
        insert.setAttributeValueId(100078L);
        insert.setAttributeValue(ncmCode);
//                insert.setId();
//                insert.setRemark("同步ncm码 自动创建");
        insert.setCreator(updater);
        insert.setUpdater(updater);
        insert.setCreateTime(new Date().getTime());
        insert.setUpdateTime(new Date().getTime());
        insert.setYn(YnEnum.YES.getCode());
        return insert;
    }

    @Override
    public void syncUpdateSkuDraftNcmCode(String ncmCode, SkuDraftPO skuDraftPO, String updater) {
        log.info("更新草稿中的ncm码, skuDraftPO={}, ncmCode={}, updater={}", com.alibaba.fastjson.JSONObject.toJSONString(skuDraftPO), ncmCode, updater);
        Long skuId = skuDraftPO.getSkuId();

        String skuInterProperty = skuDraftPO.getSkuInterProperty();

//        if (StringUtils.isBlank(skuInterProperty)) {
//            log.info("无跨境属性无需更新草稿, skuId={}", skuId);
//            return;
//        }

        boolean required = false;

        try {
            List<GroupPropertyVO> properties = com.alibaba.fastjson.JSON.parseObject(skuInterProperty,new TypeReference<List<GroupPropertyVO>>() {});

            if (CollectionUtils.isEmpty(properties)) {
//                properties = initGroupProperty(ncmCode);
                log.info("无跨境属性无需更新草稿, skuId={}", skuId);
                return;
            }

            for (GroupPropertyVO property : properties) {
                List<PropertyVO> propertyVOS = property.getPropertyVOS();

//                Integer requirement = property.getRequirement();
//
//                if (requirement == null || requirement != 2) {
//                    continue;
//                }

                if (CollectionUtils.isEmpty(propertyVOS)) {
//                    propertyVOS = initPropertyVOS(ncmCode);
//                    property.setPropertyVOS(propertyVOS);
                    continue;
                }

                for (PropertyVO propertyVO : propertyVOS) {
                    Long attributeId = propertyVO.getAttributeId();

                    if (!TaxTypeRelationEnum.NCM.getGlobalAttributeId().equals(attributeId)) {
                        continue;
                    }

                    List<PropertyValueVO> list = propertyVO.getPropertyValueVOList();

                    if (CollectionUtils.isEmpty(list)) {
                        list = initPropertyValueVOS(ncmCode);
                        propertyVO.setPropertyValueVOList(list);
                        required = true;
                        continue;
                    }

                    for (PropertyValueVO value : list) {
//                        if (StringUtils.equals(value.getAttributeValueName(), ncmCode)) {
//                            log.info("sku草稿ncmCode 相同，无需更新, ncmCode={}", ncmCode);
//                            continue;
//                        }
                        value.setAttributeValueName(ncmCode);
                        required = true;
                    }
                }
            }

            if (required) {
//                System.out.println(com.alibaba.fastjson.JSONObject.toJSONString(properties));
                boolean success = skuDraftAtomicService.updateSkuDraftInterPropertyBySkuId(properties, skuId, updater);
                log.info("同步ncm码到sku草稿完成, result={}, skuId={}", success, skuId);
            } else {
                log.info("同步ncm码到sku草稿完成, 无需更新, skuId={}", skuId);
            }

        } catch (Exception e) {
            log.error("同步ncm码更新草稿失败, skuId={}", skuId, e);
            throw new BizException("同步ncm码更新草稿失败");
        }
    }

    @Override
    public void updateDraftYn(Long spuId, String updater) {

        log.info("更新sku草稿yn=0, spuId={}, updater={}", spuId, updater);

        if (spuId == null) {
            log.info("更新sku草稿yn=0, skuIds is empty");
            return;
        }

        List<SkuDraftPO> list = skuDraftAtomicService.getBySpuId(spuId);

        List<Long> ids = list.stream().map(SkuDraftPO::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            log.info("更新sku草稿yn=0, ids is empty");
            return;
        }

        LambdaUpdateWrapper<SkuDraftPO> wrapper = Wrappers.<SkuDraftPO>lambdaUpdate()
                .in(SkuDraftPO::getId, ids);
        SkuDraftPO update = new SkuDraftPO();
        update.setYn(YnEnum.NO.getCode());
        update.setUpdateInfo(updater);

        boolean result = skuDraftAtomicService.update(update, wrapper);
        log.info("更新sku草稿yn=0, 更新结果={}, ids={}, updater={}", result, ids, updater);
        if (!result) {
            log.error("更新sku草稿yn=0, 更新失败. id={}, updater={}", ids, updater);
            throw new BizException("更新sku草稿失败");
        }

    }

    public static void main(String[] args) {
        String ncmCode = "自定义ncmcode";
        SkuDraftPO skuDraftPO = new SkuDraftPO();

        String aaa = "[{\n" +
                "\t\"propertyVOS\": [{\n" +
                "\t\t\t\"attributeId\": 69,\n" +
                "\t\t\t\"attributeInputType\": 3,\n" +
                "\t\t\t\"attributeName\": \"巴西供应商 CNPJ\",\n" +
                "\t\t\t\"inputCheckType\": 1,\n" +
                "\t\t\t\"propertyValueVOList\": [{\n" +
                "\t\t\t\t\"attributeId\": 69,\n" +
                "\t\t\t\t\"attributeValueId\": 100076,\n" +
                "\t\t\t\t\"attributeValueName\": \"11\",\n" +
                "\t\t\t\t\"lang\": \"zh\",\n" +
                "\t\t\t\t\"langName\": \"中文\",\n" +
                "\t\t\t\t\"placeholderValue\": \"请按正确格式录入\",\n" +
                "\t\t\t\t\"required\": true,\n" +
                "\t\t\t\t\"selected\": true,\n" +
                "\t\t\t\t\"sort\": 0\n" +
                "\t\t\t}],\n" +
                "\t\t\t\"required\": true,\n" +
                "\t\t\t\"showSupplier\": 1,\n" +
                "\t\t\t\"sort\": 20\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"attributeId\": 71,\n" +
                "\t\t\t\"attributeInputType\": 3,\n" +
                "\t\t\t\"attributeName\": \"NCM 码\",\n" +
                "\t\t\t\"inputCheckType\": 1,\n" +
                "\t\t\t\"required\": true,\n" +
                "\t\t\t\"showSupplier\": 1,\n" +
                "\t\t\t\"sort\": 22\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"requirement\": 1\n" +
                "}]";

        skuDraftPO.setSkuInterProperty(aaa);

        String updater = "liuzhaoming";
        new SkuWriteManageServiceImpl().syncUpdateSkuDraftNcmCode(ncmCode, skuDraftPO, updater);
    }

   public List<GroupPropertyVO> initGroupProperty(String ncmCode) {
        List<GroupPropertyVO> properties;
        List<GroupPropertyVO> temp = Lists.newArrayList();

        GroupPropertyVO group = new GroupPropertyVO();


        List<PropertyVO> propertyVOs = this.initPropertyVOS(ncmCode);

        group.setPropertyVOS(propertyVOs);
        group.setRequirement(2);
        temp.add(group);
        properties = temp;
        return properties;
    }

    public List<PropertyVO> initPropertyVOS(String ncmCode) {
        List<PropertyVO> propertyVOs = Lists.newArrayList();

        PropertyVO vo = this.initPropertyVO(ncmCode);
        propertyVOs.add(vo);
        return propertyVOs;
    }

    @Override
    public PropertyVO initPropertyVO(String ncmCode) {
        PropertyVO vo = new PropertyVO();
        vo.setAttributeInputType(3);
        vo.setAttributeName("NCM 码");
        vo.setInputCheckType(1);
//        vo.setRequired(true);
//        vo.setShowSupplier(1);
        vo.setSort(0);
        vo.setAttributeId(TaxTypeRelationEnum.NCM.getGlobalAttributeId());

        List<PropertyValueVO> propertyValueVO = this.initPropertyValueVOS(ncmCode);

        vo.setPropertyValueVOList(propertyValueVO);
        return vo;
    }

    @Override
    public List<PropertyValueVO> initPropertyValueVOS(String ncmCode) {
        List<PropertyValueVO> result = Lists.newArrayList();

        PropertyValueVO propertyValue = new PropertyValueVO();
        propertyValue.setAttributeId(TaxTypeRelationEnum.NCM.getGlobalAttributeId());
        propertyValue.setAttributeValueId(100078L);
        propertyValue.setAttributeValueName(ncmCode);
        propertyValue.setSort(0);
//        propertyValue.setLang("zh");
//        propertyValue.setLangName("中文");
        propertyValue.setSelected(true);
//        propertyValue.setRequired(true);
//        propertyValue.setPlaceholderValue("NCM code");

        result.add(propertyValue);
        return result;
    }

    /**
     * 新增sku特殊属性
     *
     * @param skuVOList
     */
    private void addSkuFeatures(List<SkuVO> skuVOList) {
        if (CollectionUtils.isEmpty(skuVOList)) {
            return;
        }

        skuVOList.forEach(skuVO -> {
            if (CollectionUtils.isNotEmpty(skuVO.getSkuStockRelationList()) && skuVO.getSkuStockRelationList().stream()
                .filter(Objects::nonNull)
                .anyMatch(relation -> !Constant.FACTORY_DEFAULT_ID_STR.equals(relation.getWarehouseId()))) {
                skuVO.setPurchaseModel(1);
            }
        });
        // sku属性
        List<SkuFeaturePO> skuFeaturePOList = SkuConvert.INSTANCE.listSkuVo2FeaturePo(skuVOList);
        skuFeaturePOList.forEach(skuFeaturePO -> {
            skuFeaturePO.setCreateTime((new Date()).getTime());
            skuFeaturePO.setUpdateTime((new Date()).getTime());}
        );
        log.info("SkuWriteManageServiceImpl.addSkuFeatures 新增sku特殊属性,入参:[skuFeaturePOList={}]", JSON.toJSONString(skuFeaturePOList));
        skuFeatureAtomicService.saveBatch(skuFeaturePOList);
    }

    /**
     * 更新sku特殊属性
     *
     * @param skuVOList 处理的sku信息
     */
    private void updateSkuFeatures(List<SkuVO> skuVOList) {
        if (CollectionUtils.isEmpty(skuVOList)) {
            return;
        }
        Map<Long, SkuVO> skuVoMap = skuVOList.stream().filter(Objects::nonNull).collect(Collectors.toMap(SkuVO::getSkuId, Function.identity()));
        List<SkuFeaturePO> skuFeaturePoList = skuFeatureAtomicService.queryListByskuIds(skuVoMap.keySet());
        if (CollectionUtils.isNotEmpty(skuFeaturePoList)) {
            for (SkuFeaturePO skuFeaturePo : skuFeaturePoList) {
                final Long skuId = skuFeaturePo.getSkuId();
                if (skuVoMap.containsKey(skuId)) {
                    this.convertSkuVo2SkuFeature(skuVoMap.get(skuId), skuFeaturePo);
                }
            }
            // 更新sku特殊属性
            log.info("SkuWriteManageServiceImpl.addSkuFeatures 更新sku特殊属性 入参:[skuFeaturePOList={}]", JSON.toJSONString(skuFeaturePoList));
            skuFeatureAtomicService.saveOrUpdateBatch(skuFeaturePoList);
        }else {
            this.addSkuFeatures(skuVOList);
        }
    }

    private void convertSkuVo2SkuFeature(SkuVO skuVO, SkuFeaturePO skuFeaturePO) {
        BeanUtils.copyProperties(skuVO, skuFeaturePO);
        skuFeaturePO.setSkuId(null);
        skuFeaturePO.setUpdateTime(new Date().getTime());
    }

    private boolean containSkuFeature(SkuVO skuVO) {
        return Objects.nonNull(skuVO.getMagnetic()) || Objects.nonNull(skuVO.getElectric())
                || Objects.nonNull(skuVO.getLiquid()) || Objects.nonNull(skuVO.getPowder())
                || Objects.nonNull(skuVO.getReturnType()) || StringUtils.isNotBlank(skuVO.getOriginCountry())
                || StringUtils.isNotBlank(skuVO.getAdapterPlus()) ||  StringUtils.isNotBlank(skuVO.getVoltage())
                || Objects.nonNull(skuVO.getProductionCycle());
    }

    /**
     * 合法性校验
     * @param param 全球属性的更新参数列表。
     *               如果参数为空、skuId为空、变更的跨境属性为空、跨境属性的key或value为空，会抛出异常。
     */
    private void checkUpdateGlobalAttribute(SkuGlobalAttributeVO param){
        if(param == null){
            throw new BizException("参数为空");
        }
        if(StringUtils.isBlank(param.getTargetCountryCode())){
            throw new BizException("目标国家为空");
        }
        if(CollectionUtils.isEmpty(param.getDetailData())){
            throw new BizException("数据为空");
        }

        for(SkuGlobalAttributeDetailVO skuGlobalAttributeDetailVO : param.getDetailData()){
            if(skuGlobalAttributeDetailVO.getSkuId() == null){
                throw new BizException("skuId为空");
            }
            if(MapUtils.isEmpty(skuGlobalAttributeDetailVO.getUpdateData())){
                throw new BizException("变更的跨境属性为空");
            }
            skuGlobalAttributeDetailVO.getUpdateData().forEach((k,v)->{
                if(k == null){
                    throw new BizException("跨境属性key为空");
                }
            });
        }
    }

    /**
     * 合理性校验。
     * @param param SkuGlobalAttributeVO 对象，包含目标国家代码和详细数据。
     * @param skuIds SKU 的 ID 列表。
     * @param skuPOS SKU 的 PO 对象列表。
     * @throws BizException 如果 SKU 的 PO 对象列表为空、SKUID 不唯一、SKUID 在数据库中不存在或目标国家代码不支持更新。
     */
    private void checkUpdateGlobalAttribute(SkuGlobalAttributeVO param,List<Long> skuIds,List<SkuPO> skuPOS){

        // 检查跨境属性的公共方法
        this.checkUpdateGlobalAttributeCommon(skuIds,skuPOS);

        // 检查越南跨境属性
        this.checkUpdateGlobalAttributeVn(param);

    }

    /**
     * 检查并越南跨境属性。
     * @param param SkuGlobalAttributeVO 对象，包含目标国家代码和详细数据。
     */
    private void checkUpdateGlobalAttributeVn(SkuGlobalAttributeVO param){
        // 判断允许更新的跨境属性
        String targetCountryCode = param.getTargetCountryCode();
        if(!StringUtils.equals(CountryConstant.COUNTRY_VN,targetCountryCode)){
            log.error("SkuWriteManageServiceImpl.checkUpdateGlobalAttributeVn return");
            return;
        }
        List<SkuGlobalAttributeDetailVO> detailVOS = param.getDetailData();
        for(SkuGlobalAttributeDetailVO skuGlobalAttributeDetailVO : detailVOS){
            Map<String,Object> updateDataMap = skuGlobalAttributeDetailVO.getUpdateData();
            updateDataMap.forEach((k,v)->{
                GlobalAttributeVnEnum globalAttributeVnEnum = GlobalAttributeVnEnum.getEnumByCode(k);
                if(globalAttributeVnEnum == null){
                    throw new BizException("非合法跨境属性更新");
                }
            });
        }
    }

    /**
     * 检查更新全局属性的公共方法。
     * @param skuIds List<Long>，表示要更新全局属性的 SKU ID 列表。
     * @param skuPOS List<SkuPO>，表示 SKU 对象列表，用于验证 SKU ID 是否存在于数据库中。
     */
    private void checkUpdateGlobalAttributeCommon(List<Long> skuIds,List<SkuPO> skuPOS){
        if(CollectionUtils.isEmpty(skuIds) || CollectionUtils.isEmpty(skuPOS)){
            throw new BizException("国际SKU为空");
        }
        // 判断唯一
        Set<Long> uniqueSkuIds = new HashSet<>(skuIds);
        if(uniqueSkuIds.size() != skuIds.size()){
            List<Long> duplicateSkuIds = skuIds.stream()
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                    .entrySet().stream()
                    .filter(entry -> entry.getValue() > 1)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            throw new BizException("国际SKUID请保持唯一，SKUID:"+JSONObject.toJSONString(duplicateSkuIds));
        }

        // 判断数据库中存在
        if(skuIds.size() != skuPOS.size()){
            Set<Long> skuPOIds = skuPOS.stream()
                    .map(SkuPO::getSkuId)
                    .collect(Collectors.toSet());
            List<Long> missingSkuIds = skuIds.stream()
                    .filter(id -> !skuPOIds.contains(id))
                    .collect(Collectors.toList());
            throw new BizException("以下国际SKUID在数据库中不存在:" + JSONObject.toJSONString(missingSkuIds));
        }
    }


    /**
     * 根据国家代码获取更新属性。
     * @param countryCode 国家代码
     * @return 更新属性的键值对，键为属性名，值为属性值的长整型表示；如果国家代码为空或找不到对应的更新属性，则返回null。
     */
    private Map<String,Long> getUpdateAttribute(String countryCode){
        if (StringUtils.isBlank(countryCode)) {
            return null;
        }
        com.alibaba.fastjson.JSONObject globalAttributeObj = ConfigUtils.getJsonByKey(globalAttribute, countryCode);
        return globalAttributeObj.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            Object value = entry.getValue();
                            return Long.parseLong(value.toString());
                        }, (v1, v2) -> v1, // 如果有重复的key，保留第一个值
                        HashMap::new // 使用HashMap作为结果Map的实现
                ));
    }

    /**
     * 获取所有更新国家代码
     * @return 更新国家代码集合
     */
    private Set<String> getAllUpdateCountryCode(){
        com.alibaba.fastjson.JSONObject configJson = com.alibaba.fastjson.JSONObject.parseObject(globalAttribute);
        return configJson.keySet();
    }


    /**
     * 处理属性值。
     * @param attributeCode 属性代码
     * @param value 属性值
     * @param partProperty 属性对象
     * @param valueVOS 属性值列表
     */
    private void processAttributeValue(String targetCountryCode,String attributeCode, String value, PropertyVO partProperty, List<PropertyValueVO> valueVOS) {
        if(!StringUtils.equals(CountryConstant.COUNTRY_VN,targetCountryCode)){
            return;
        }

        GlobalAttributeVnEnum vnEnum = GlobalAttributeVnEnum.getEnumByCode(attributeCode);
        if (vnEnum == GlobalAttributeVnEnum.VN_IMPORT_LIMIT || vnEnum == GlobalAttributeVnEnum.VN_IN_BONDED_WAREHOUSE) {
            Map<Long, PropertyValueVO> valueVOMap = partProperty.getPropertyValueVOList().stream()
                    .collect(Collectors.toMap(PropertyValueVO::getAttributeValueId, Function.identity()));
            if(StringUtils.isBlank(value)){
                return;
            }
            PropertyValueVO propertyValueVO = valueVOMap.get(Long.parseLong(value));
            propertyValueVO.setSelected(Boolean.TRUE);
            valueVOS.add(propertyValueVO);
        } else if (vnEnum == GlobalAttributeVnEnum.VN_HSCODE) {
            if(StringUtils.isBlank(value)){
                return;
            }
            PropertyValueVO propertyValueVO = new PropertyValueVO();
            propertyValueVO.setAttributeId(partProperty.getAttributeId());
            propertyValueVO.setAttributeValueId(partProperty.getPropertyValueVOList().get(0).getAttributeValueId());
            propertyValueVO.setLang(LangConstant.LANG_ZH);
            propertyValueVO.setSelected(Boolean.TRUE);
            propertyValueVO.setAttributeValueName(value);
            valueVOS.add(propertyValueVO);
        } else if (vnEnum == GlobalAttributeVnEnum.VN_IMPORT_REMARK){
            if(StringUtils.isNotBlank(value) && value.length() > 50){
                throw new BizException("备注超长，备注更新失败，请在商品列表手工编辑缩减备注。");
            }
            List<PropertyValueVO> dbValueList = partProperty.getPropertyValueVOList();
            if(CollectionUtils.isEmpty(dbValueList)){
                if(StringUtils.isBlank(value)){
                    return;
                }
                PropertyValueVO propertyValueVO = new PropertyValueVO();
                propertyValueVO.setAttributeId(partProperty.getAttributeId());
                propertyValueVO.setAttributeValueId(partProperty.getPropertyValueVOList().get(0).getAttributeValueId());
                propertyValueVO.setLang(LangConstant.LANG_ZH);
                propertyValueVO.setSelected(Boolean.TRUE);
                propertyValueVO.setAttributeValueName(value);
                valueVOS.add(propertyValueVO);
            } else {
                for(PropertyValueVO valueVO : dbValueList){
                    if(StringUtils.equals(LangConstant.LANG_ZH,valueVO.getLang())){
                        String origin = valueVO.getAttributeValueName();
                        String str = "";

                        if(StringUtils.isBlank(origin)){
                            str = value;
                        } else if(StringUtils.isNotBlank(origin) && StringUtils.isNotBlank(value)){
                            str = origin+"；"+value;
                        } else {
                            str = origin;
                        }
                        valueVO.setAttributeValueName(str);
                        valueVO.setSelected(Boolean.TRUE);
                        valueVOS.add(valueVO);
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Map<String, BaseResDTO>> batchUpdateCustoms(BatchUpdateCustomsApiReqDTO reqDTO) {
        Set<UpdateCustomsApiDTO> updateCustomsApiDTOSet = reqDTO.getUpdateCustomsApiDTOSet();
        String updater = reqDTO.getUpdater();
        Map<String,BaseResDTO> resultMap = Maps.newHashMap();

        // 批量查询sku信息
        Set<Long> skuIds = updateCustomsApiDTOSet.stream().filter(Objects::nonNull).map(dto -> Long.valueOf(dto.getSkuId())).collect(Collectors.toSet());
        Map<Long, SkuPO> skuMap = skuAtomicService.getSkuMap(skuIds);
        if (MapUtils.isEmpty(skuMap)) {
            for (UpdateCustomsApiDTO apiDTO : updateCustomsApiDTOSet) {
                resultMap.put(apiDTO.getSkuId(), new BaseResDTO(String.format("商品%s不存在",apiDTO.getSkuId())));
            }
            return DataResponse.success(resultMap);
        }

        // 批量查询SKU的草稿
        Map<Long, SkuDraftPO> skuDraftMap = skuDraftAtomicService.querySkuInterPropertyMapBySkuIds(skuIds);
        // 查询SPU信息
        Set<Long> spuIds = skuMap.values().stream().filter(Objects::nonNull).map(skuPO -> skuPO.getSpuId()).collect(Collectors.toSet());
        // 查询SPU的草稿信息
        Map<Long, SpuDraftPO> spuDraftPOMap = spuDraftAtomicService.querySpuDraftMap(spuIds);
        // 查询SPU的跨境属性
        Map<Long, Map<Long, List<ProductGlobalAttributePO>>> spuGlobalAttributeMap = productGlobalAttributeAtomicService.queryProductGlobalAttributeMap(spuIds, GlobalAttributeEnum.spuAttributeIdSet(), KeyTypeEnum.SPU.getCode());
        // 查询申报要素等跨境属性列表
        List<GroupPropertyVO> groupPropertyVOList = globalAttributeManageService.queryGroupPropertyVos(GlobalAttributeEnum.spuAttributeIdSet(),AttributeDimensionEnum.SPU.getCode(), LangConstant.LANG_ZH, CountryConstant.COUNTRY_ZH, Lists.newArrayList(CountryConstant.COUNTRY_TH));

        // 批量查询SPU的跨境视讯跟
        for (UpdateCustomsApiDTO apiDTO : updateCustomsApiDTOSet) {
            BaseResDTO baseResDTO = new BaseResDTO();
            try {
                Long skuId = Long.valueOf(apiDTO.getSkuId());
                if (MapUtils.isEmpty(skuMap) || !skuMap.containsKey(skuId)) {
                    resultMap.put(apiDTO.getSkuId(), new BaseResDTO(String.format("%s商品不存在", apiDTO.getSkuId())));
                    continue;
                }
                SkuPO skuPO = skuMap.get(skuId);
                // 更新海关编码 sku主表海关编号和入参不同时更新
                this.updateSkuHsCode(apiDTO, skuPO, updater, skuId, skuDraftMap);
                Long spuId = skuPO.getSpuId();
                // 更新SPU跨境属性
                this.updateSpuGlobalAttribute(apiDTO, spuGlobalAttributeMap, spuId, updater, skuId);
                SpuDraftPO spuDraftPO = spuDraftPOMap.get(spuId);
                if (Objects.isNull(spuDraftPO)) {
                    continue;
                }
                // 更新SPU草稿中的申报要素信息
                this.updateSpuDraftForCustoms(apiDTO, groupPropertyVOList, spuDraftPO, updater, spuId);
            } catch (Exception e) {
                log.error("SkuWriteManageServiceImpl.batchUpdateCustoms 更新SKU报关信息异常 入参:[{}]",JSON.toJSONString(apiDTO),e);
                baseResDTO = new BaseResDTO(String.format("商品%s更新报关信息失败",apiDTO.getSkuId()));
            }
            resultMap.put(apiDTO.getSkuId(), baseResDTO);
        }
        return DataResponse.success(resultMap);
    }

    private void updateSpuDraftForCustoms(UpdateCustomsApiDTO apiDTO, List<GroupPropertyVO> groupPropertyVOList, SpuDraftPO spuDraftPO, String updater, Long spuId) {
        // 深拷贝跨境属性
        GroupPropertyVO groupPropertyVO = GroupPropertyConvert.INSTANCE.copyGroup(groupPropertyVOList.get(0));
        List<PropertyVO> propertyVOS = groupPropertyVO.getPropertyVOS();
        Map<Long, PropertyVO> propertyVOMap = Maps.newHashMapWithExpectedSize(propertyVOS.size());
        for (PropertyVO vo : propertyVOS) {
            List<PropertyValueVO> propertyValueVOList = vo.getPropertyValueVOList();
            // 剔除非中文语言的属性值
            List<PropertyValueVO> zhPropertyVOList = propertyValueVOList.stream().filter(propertyValueVO -> LangConstant.LANG_ZH.equals(propertyValueVO.getLang())).collect(Collectors.toList());
            vo.setPropertyValueVOList(zhPropertyVOList);
            propertyVOMap.put(vo.getAttributeId(), vo);
        }

        // 组装新跨境属性列表
        String spuInterPropertyStr = getSpuInterPropertyStr(apiDTO, spuDraftPO, propertyVOMap, groupPropertyVO);
        LambdaUpdateWrapper<SpuDraftPO> updateWrapper = Wrappers.lambdaUpdate(SpuDraftPO.class).set(SpuDraftPO::getSpuInterProperty, spuInterPropertyStr).set(SpuDraftPO::getUpdater, updater).set(SpuDraftPO::getUpdateTime, new Date())
                .eq(SpuDraftPO::getSpuId, spuId).eq(SpuDraftPO::getYn, YnEnum.YES.getCode());
        spuDraftAtomicService.update(updateWrapper);
        log.info("batchUpdateCustoms 更新spu的草稿中跨境属性信息完成 skuId={},spuId={},spuInterPropertyStr ={}",apiDTO.getSkuId(), spuId,spuInterPropertyStr);
    }

    private String getSpuInterPropertyStr(UpdateCustomsApiDTO apiDTO, SpuDraftPO spuDraftPO, Map<Long, PropertyVO> propertyVOMap, GroupPropertyVO groupPropertyVO) {
        List<GroupPropertyVO> newGroupPropertyList = Lists.newArrayList();

        if (StringUtils.isBlank(spuDraftPO.getSpuInterProperty())) {
            List<PropertyVO> newPropertyVOList = this.getNewPropertyVOList(apiDTO, propertyVOMap);
            groupPropertyVO.setPropertyVOS(newPropertyVOList);
            newGroupPropertyList.add(groupPropertyVO);
            return JSON.toJSONString(newGroupPropertyList);
        }

        String spuInterProperty = spuDraftPO.getSpuInterProperty();
        List<GroupPropertyVO> spuInterProperties = JSON.parseObject(spuInterProperty,
                new TypeReference<List<GroupPropertyVO>>() {});

        // 跨境属性列表为空时
        if (CollectionUtils.isEmpty(spuInterProperties)) {
            List<PropertyVO> newPropertyVOList = this.getNewPropertyVOList(apiDTO, propertyVOMap);
            groupPropertyVO.setPropertyVOS(newPropertyVOList);
            newGroupPropertyList.add(groupPropertyVO);
            return JSON.toJSONString(newGroupPropertyList);
        }

        // 不包含发货前的跨境属性时，直接赋值申报要素信息等属性增加到属性列表中
        if (spuInterProperties.stream().noneMatch(p -> AttributeCheckTypeEnum.BEFORE_SHIPMENT.getCode().equals(p.getRequirement()))) {
            List<PropertyVO> newPropertyVOList = this.getNewPropertyVOList(apiDTO, propertyVOMap);
            groupPropertyVO.setPropertyVOS(newPropertyVOList);
            spuInterProperties.add(groupPropertyVO);
            return JSON.toJSONString(spuInterProperties);
        }

        // 有发货前必填跨境属性时，更新其中的申报要素信息等属性
        for (GroupPropertyVO group: spuInterProperties){
            if (!AttributeCheckTypeEnum.BEFORE_SHIPMENT.getCode().equals(group.getRequirement())) {
                continue;
            }
            List<PropertyVO> propertyVOList = group.getPropertyVOS();
            if (CollectionUtils.isEmpty(propertyVOList)) {
                propertyVOList = this.getNewPropertyVOList(apiDTO, propertyVOMap);
                group.setPropertyVOS(propertyVOList);
            }else {
                // 过滤掉非申报要素信息的跨境属性
                List<PropertyVO> newPropertyList = propertyVOList.stream().filter(vo-> !GlobalAttributeEnum.spuAttributeIdSet().contains(vo.getAttributeId())).collect(Collectors.toList());;
                newPropertyList.addAll(this.getNewPropertyVOList(apiDTO, propertyVOMap));
                group.setPropertyVOS(newPropertyList);
            }
        }
        return JSON.toJSONString(spuInterProperties);
    }

    private List<PropertyVO> getNewPropertyVOList(UpdateCustomsApiDTO apiDTO, Map<Long, PropertyVO> propertyVOMap) {
        List<PropertyVO> propertyVOList = Lists.newArrayList();
        for (Map.Entry<Long, PropertyVO> entry : propertyVOMap.entrySet()) {
            Long id = entry.getKey();
            PropertyVO propertyVo = entry.getValue();
            PropertyValueVO propertyValueVO = propertyVo.getPropertyValueVOList().get(0);
            if (GlobalAttributeEnum.SPU_DECLARATION_ELEMENTS.getId().equals(id) && StringUtils.isNotBlank(apiDTO.getDeclarationElements())) {
                propertyValueVO.setSelected(Boolean.TRUE);
                propertyValueVO.setAttributeValueName(apiDTO.getDeclarationElements());
            } else if (GlobalAttributeEnum.SPU_DECLARATION_NAME.getId().equals(id) && StringUtils.isNotBlank(apiDTO.getDeclarationZhName())) {
                propertyValueVO.setSelected(Boolean.TRUE);
                propertyValueVO.setAttributeValueName(apiDTO.getDeclarationZhName());
            } else if (GlobalAttributeEnum.SPU_DECLARATION_EN_NAME.getId().equals(id) && StringUtils.isNotBlank(apiDTO.getDeclarationEnName())) {
                propertyValueVO.setSelected(Boolean.TRUE);
                propertyValueVO.setAttributeValueName(apiDTO.getDeclarationEnName());
            }else {
                continue;
            }
            propertyVOList.add(propertyVo);
        }
        return propertyVOList;
    }

    /**
     * 更新SPU的申报要素、申报品名信息。
     * @param apiDTO 更新的自定义API DTO对象。
     * @param spuGlobalAttributeMap SPU的全局属性映射表。
     * @param spuId SPU的ID。
     * @param updater 更新人。
     * @param skuId SKU的ID。
     */
    private void updateSpuGlobalAttribute(UpdateCustomsApiDTO apiDTO, Map<Long, Map<Long, List<ProductGlobalAttributePO>>> spuGlobalAttributeMap, Long spuId, String updater, Long skuId) {
        // 更新SPU的申报要素、申报品名信息
        Map<Long, List<ProductGlobalAttributePO>> globalAttributeMap = spuGlobalAttributeMap.getOrDefault(spuId,Maps.newHashMap());
        // 跨境属性为空时
        List<ProductGlobalAttributePO> list = Lists.newArrayList();
        GlobalAttributeEnum.spuAttributeIdSet().forEach(id-> {
            ProductGlobalAttributePO attributePO = new ProductGlobalAttributePO();
            if (MapUtils.isNotEmpty(globalAttributeMap) && globalAttributeMap.containsKey(id) && CollectionUtils.isNotEmpty(globalAttributeMap.get(id))) {
                attributePO = globalAttributeMap.get(id).get(0);
                attributePO.setKeyId(null);
            }else {
                attributePO.setKeyId(spuId);
            }
            attributePO.setAttributeId(id);
            if (GlobalAttributeEnum.SPU_DECLARATION_ELEMENTS.getId().equals(id)){
                attributePO.setAttributeValueId(100049L);
                attributePO.setAttributeValue(apiDTO.getDeclarationElements());
            } else if (GlobalAttributeEnum.SPU_DECLARATION_NAME.getId().equals(id)) {
                attributePO.setAttributeValueId(100050L);
                attributePO.setAttributeValue(apiDTO.getDeclarationZhName());
            } else if (GlobalAttributeEnum.SPU_DECLARATION_EN_NAME.getId().equals(id)) {
                attributePO.setAttributeValueId(100051L);
                attributePO.setAttributeValue(apiDTO.getDeclarationEnName());
            }
            attributePO.setKeyType(KeyTypeEnum.SPU.getCode());
            attributePO.setCreator(updater);
            attributePO.setUpdater(updater);
            long now = System.currentTimeMillis();
            attributePO.setCreateTime(now);
            attributePO.setUpdateTime(now);
            attributePO.setYn(YnEnum.YES.getCode());
            list.add(attributePO);
        });
        productGlobalAttributeAtomicService.saveOrUpdateBatch(list);
        log.info("batchUpdateCustoms 更新spu的跨境属性信息完成 skuId={},spuId={},globalAttributeList ={}", skuId, spuId,JSON.toJSONString(list));
    }

    /**
     * 更新商品的海关编码信息，包括主表和草稿表。
     * @param apiDTO 更新海关编码的DTO对象
     * @param skuPO 商品主表PO对象
     * @param updater 更新人
     * @param skuId 商品ID
     * @param skuDraftMap 商品草稿表PO对象的Map集合
     */
    private void updateSkuHsCode(UpdateCustomsApiDTO apiDTO, SkuPO skuPO, String updater, Long skuId, Map<Long, SkuDraftPO> skuDraftMap) {
        if (StringUtils.isNotBlank(apiDTO.getHsCode()) && !Objects.equals(apiDTO.getHsCode(), skuPO.getHsCode())){
            LambdaUpdateWrapper<SkuPO> updateWrapper = Wrappers.lambdaUpdate(SkuPO.class).set(SkuPO::getHsCode, apiDTO.getHsCode())
                    .set(SkuPO::getUpdater, updater).set(SkuPO::getUpdateTime, new Date())
                    .eq(SkuPO::getSkuId, skuId).eq(SkuPO::getYn, YnEnum.YES.getCode());
            skuAtomicService.update(updateWrapper);
            log.info("batchUpdateCustoms 更新sku主表海关编码信息完成 skuId={},hsCode={}", skuId, apiDTO.getHsCode());
        }
        // 更新sku草稿的海关编码
        SkuDraftPO skuDraftPo = skuDraftMap.get(skuId);
        if (Objects.nonNull(skuDraftPo) && StringUtils.isNotBlank(skuDraftPo.getSkuJsonInfo())) {
            SkuVO skuVO = JSON.parseObject(skuDraftPo.getSkuJsonInfo(), SkuVO.class);
            if (StringUtils.isNotBlank(apiDTO.getHsCode()) && !Objects.equals(skuVO.getHsCode(), apiDTO.getHsCode())) {
                skuVO.setHsCode(apiDTO.getHsCode());
                LambdaUpdateWrapper<SkuDraftPO> updateWrapper = Wrappers.lambdaUpdate(SkuDraftPO.class).set(SkuDraftPO::getSkuJsonInfo, JSON.toJSONString(skuVO))
                        .set(SkuDraftPO::getUpdater, updater).set(SkuDraftPO::getUpdateTime, System.currentTimeMillis())
                        .eq(SkuDraftPO::getSkuId, skuId).eq(SkuDraftPO::getYn, YnEnum.YES.getCode());
                skuDraftAtomicService.update(updateWrapper);
                log.info("batchUpdateCustoms 更新sku的草稿海关编码信息完成 skuId={},hsCode={}", skuId, apiDTO.getHsCode());
            }
        }
    }


    @Override
    public Boolean removeNeedUnbindSkuRelation(Set<Long> skuIds, String warehouseId) {
        Map<Long, SkuDraftPO> skuDraftPOMap = skuDraftAtomicService.querySkuInterPropertyMapBySkuIds(skuIds);
        if (skuDraftPOMap != null && !skuDraftPOMap.isEmpty()) {
            for (Map.Entry<Long, SkuDraftPO> entry : skuDraftPOMap.entrySet()) {
                Long skuId = entry.getKey();
                SkuDraftPO skuDraftPO = entry.getValue();

                if (StringUtils.isNotBlank(skuDraftPO.getSkuStockRelation())) {
                    List<SkuStockRelationVO> skyStockRelations = JSON.parseObject(skuDraftPO.getSkuStockRelation(),
                            new TypeReference<List<SkuStockRelationVO>>() {
                            });

                    List<SkuStockRelationVO> skuStockRelationVOList = skyStockRelations.stream()
                            .filter(skuStockRelation -> !Objects.equals(skuStockRelation.getWarehouseId(), warehouseId))
                            .collect(Collectors.toList());

                    // 只有在过滤后的列表不为空时才更新 skuStockRelation
                    if (!skuStockRelationVOList.isEmpty()) {
                        boolean result = skuDraftAtomicService.updateSkuStockRelationPropertyBySkuId(skuStockRelationVOList, skuId, "systemUnBind");
                        log.info("removeNeedUnbindSkuRelation skuId:{}, warehouseId:{}, skuStockRelationVOList:{},result:{}", skuId, warehouseId, JSON.toJSONString(skuStockRelationVOList), result);
                    }
                }
            }
        }

        return Boolean.TRUE;
    }

}
