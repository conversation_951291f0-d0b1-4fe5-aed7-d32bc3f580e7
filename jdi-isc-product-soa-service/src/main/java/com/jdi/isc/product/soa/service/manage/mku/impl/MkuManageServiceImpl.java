package com.jdi.isc.product.soa.service.manage.mku.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.jp.strategy.sdk.dto.SkuDetailedPageInfoResp;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.aggregate.read.api.stock.req.StockReadReqVO;
import com.jdi.isc.fulfillment.soa.api.promiseTime.res.PromiseTimeTagResApiDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeLangDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeValueDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeValueLangDTO;
import com.jdi.isc.product.soa.api.common.*;
import com.jdi.isc.product.soa.api.common.enums.*;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuLangsReqDTO;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuReqDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuDescLangDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuLangsResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuPriceResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.CommonDUCCConfig;
import com.jdi.isc.product.soa.common.util.CommonUtils;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.common.util.CommonUtils;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.common.util.StockNumUtils;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeVO;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeValueVO;
import com.jdi.isc.product.soa.domain.brand.biz.BrandClientVO;
import com.jdi.isc.product.soa.domain.brand.po.BrandPO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryComboBoxVO;
import com.jdi.isc.product.soa.domain.common.biz.DeliveryTemplate;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuVO;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerMku.biz.CustomerLatestMkuReqVO;
import com.jdi.isc.product.soa.domain.customerMku.biz.CustomerMkuVO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPricePO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPricePO;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.domain.enums.mku.MkuRelationBindStatusEnum;
import com.jdi.isc.product.soa.domain.enums.mku.MkuStatusEnum;
import com.jdi.isc.product.soa.domain.gms.req.JdProductQueryDTO;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.material.po.MkuMaterialPO;
import com.jdi.isc.product.soa.domain.mku.biz.*;
import com.jdi.isc.product.soa.domain.mku.po.MkuDescLangPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuLangPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.biz.MkuPriceReqVO;
import com.jdi.isc.product.soa.domain.price.biz.MkuPriceResVO;
import com.jdi.isc.product.soa.domain.promise.JdSkuDetailPromiseReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.QuerySkuAvailableSaleReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuLimitBuyVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuFeaturePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.specialAttr.biz.SpecialAttrSkuReqVO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.supplier.po.SupplierBaseInfoPO;
import com.jdi.isc.product.soa.domain.warehouse.biz.QuerySkuRelationReqVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseResVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseSkuVO;
import com.jdi.isc.product.soa.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.product.soa.rpc.promise.PromiseTimeInfoRpcService;
import com.jdi.isc.product.soa.service.adapter.mapstruct.mku.MkuClientStockConvert;
import com.jdi.isc.product.soa.service.atomic.brand.BrandLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.material.MkuMaterialAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuDescLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuFeatureAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.manage.attribute.AttributeOutService;
import com.jdi.isc.product.soa.service.manage.common.CommonService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuLangManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuManageService;
import com.jdi.isc.product.soa.service.manage.price.ExchangeRateManageService;
import com.jdi.isc.product.soa.service.manage.price.clientPrice.MkuClientPriceManageService;
import com.jdi.isc.product.soa.service.manage.promise.PromiseManageService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuFeatureManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.specialAttr.SpecialAttrManageService;
import com.jdi.isc.product.soa.service.manage.spu.ExtendInfoService;
import com.jdi.isc.product.soa.service.manage.stock.MkuStockService;
import com.jdi.isc.product.soa.service.manage.supplier.SupplierBaseInfoManageService;
import com.jdi.isc.product.soa.service.manage.warehouse.WarehouseManageService;
import com.jdi.isc.product.soa.service.manage.warehouse.WarehouseSkuManageService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.attribute.AttributeConvert;
import com.jdi.isc.product.soa.service.mapstruct.mku.MkuConvert;
import com.jdi.isc.product.soa.service.mapstruct.mku.MkuLangConvert;
import com.jdi.isc.product.soa.stock.mku.req.IscMkuStockItemReadReqDTO;
import com.jdi.isc.product.soa.stock.mku.req.IscMkuStockReadReqDTO;
import com.jdi.isc.product.soa.stock.mku.res.IscMkuStockResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 客户端服务类
 * @Author: zhaokun51
 * @Date: 2024/06/24 16:30
 **/
@Slf4j
@Service
public class MkuManageServiceImpl implements MkuManageService {

    @Resource
    private MkuAtomicService mkuAtomicService;
    @Resource
    private MkuLangAtomicService mkuLangAtomicService;
    @Resource
    private CommonService commonService;
    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;
    @Resource
    private CustomerMkuPriceAtomicService customerMkuPriceAtomicService;
    @Resource
    private CommonDUCCConfig commonDUCCConfig;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    private SkuInfoRpcService skuInfoRpcService;
    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;
    @Resource
    private MkuDescLangAtomicService mkuDescLangAtomicService;
    @Resource
    private MkuMaterialAtomicService mkuMaterialAtomicService;
    @Resource
    private CountryManageService countryManageService;
    @Resource
    private BrandLangAtomicService brandLangAtomicService;
    @Resource
    private ExtendInfoService extendInfoService;
    @Resource
    private SkuFeatureAtomicService skuFeatureAtomicService;
    @Resource
    private CustomerSkuPriceManageService customerSkuPriceManageService;
    @Resource
    private SpecialAttrManageService specialAttrManageService;
    @Resource
    private WarehouseSkuManageService warehouseSkuManageService;
    @Resource
    private CountryMkuAtomicService countryMkuAtomicService;
    @Resource
    private MkuStockService mkuStockService;
    @Resource
    private SpuConvertService spuConvertService;

    @Resource
    private ExchangeRateManageService exchangeRateManageService;
    @Resource
    private PromiseManageService promiseManageService;
    @Resource
    private AsyncTaskExecutor promiseQueryExecutor;

    @Resource
    private WarehouseManageService warehouseManageService;

    @Resource
    private MkuClientPriceManageService mkuClientPriceManageService;

    @Resource
    private SkuFeatureManageService skuFeatureManageService;

    @Resource
    private SupplierBaseInfoManageService supplierBaseInfoManageService;
    @Resource
    private AttributeOutService attributeOutService;
    @Resource
    private MkuLangManageService mkuLangManageService;
    @Resource
    private LangManageService langManageService;
    @Resource
    private PromiseTimeInfoRpcService promiseTimeInfoRpcService;
    @Resource
    private SaleAttributeManageService saleAttributeManageService;

    @Resource
    private SkuReadManageService skuReadManageService;

    @LafValue("jdi.isc.agg.delivery.text")
    private String deliveryText;
    @Value("${spring.profiles.active}")
    private String systemProfile;
    @LafValue("jdi.isc.mku.fulfilPromise.switch")
    private Boolean fulfilPromise72Switch;
    @LafValue("isc.message.broker.warehouseSku.country")
    private Set<String> warehouseSkuCountry;
    @LafValue("isc.product.fulfilPromise.country")
    private Set<String> fulfilPromiseCountry;
    @LafValue("jdi.isc.mku.fulfilPromise.switch.country")
    private String fulfilPromise72SwitchByCountry;


    @Override
    public Boolean syncMkuInfoBySpuAndSku(SaveSpuVO saveSpuVO) {
        if(Objects.nonNull(saveSpuVO)&&saveSpuVO.getSkuVOList()!=null&&!saveSpuVO.getSkuVOList().isEmpty()){
            List<Long> skuIds = saveSpuVO.getSkuVOList().stream().map(SkuVO::getSkuId).collect(Collectors.toList());
            List<MkuRelationPO> mkuRelationPO = mkuRelationAtomicService.queryBindListBySkuIds(skuIds);
            log.info("待同步的mku信息为：{}",mkuRelationPO);
            if(mkuRelationPO.isEmpty())return false;
            List<Long> mkuIds = mkuRelationPO.stream().map(MkuRelationPO::getMkuId).collect(Collectors.toList());
            SpuVO spuVO = saveSpuVO.getSpuVO();
            Long brandId = spuVO.getBrandId();
//            String extAttribute = spuConvertService.getExtAttribute(saveSpuVO.getStoreExtendPropertyList());  2025del_service
            List<@Valid SkuVO> skuVOList = saveSpuVO.getSkuVOList();
            Map<Long, SkuVO> map = mkuRelationPO.stream()
                    .flatMap(mkuRelation -> skuVOList.stream().filter(skuVO -> Objects.equals(mkuRelation.getSkuId(), skuVO.getSkuId()))
                            .map(skuVO -> new AbstractMap.SimpleEntry<>(mkuRelation.getMkuId(), skuVO)))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            List<MkuPO> mkuPOS = mkuAtomicService.listByMkuIds(mkuIds);
            List<MkuPO> updateBatchList = new ArrayList<>();
            for (MkuPO mkuPO : mkuPOS) {
                SkuVO skuVO = map.get(mkuPO.getMkuId());
                MkuPO mku=new MkuPO();
                mku.setMkuId(mkuPO.getMkuId());
                // 修改为待审核状态
                mku.setMkuStatus(MkuStatusEnum.WAIT_APPROVE.getStatus());
                List<PropertyVO> storeExtendPropertyList = new ArrayList<>();
                if(Objects.nonNull(saveSpuVO) && CollectionUtils.isNotEmpty(saveSpuVO.getStoreExtendPropertyList())){
                    storeExtendPropertyList.addAll(saveSpuVO.getStoreExtendPropertyList());
                }
                if(Objects.nonNull(skuVO) && CollectionUtils.isNotEmpty(skuVO.getStoreExtendPropertyList())){
                    storeExtendPropertyList.addAll(skuVO.getStoreExtendPropertyList());
                }
                String groupExtAttribute = spuConvertService.getGroupExtAttribute(storeExtendPropertyList);
                if(StringUtils.isNotBlank(groupExtAttribute)){
                    mku.setGroupExtAttribute(groupExtAttribute);    // mku的扩展属性为sku扩展属性+spu扩展属性
                }

                String skuMainImg = skuVO.getMainImg();
                String skuDetailImg = skuVO.getDetailImg();
                if(!Objects.equals(mkuPO.getBrandId(),brandId))mku.setBrandId(brandId);
                if(StringUtils.isNotBlank(skuMainImg)) {
                    if (!Objects.equals(mkuPO.getMainImg(),skuMainImg)){
                        mku.setMainImg(skuMainImg);
                    }
                }else if (!Objects.equals(mkuPO.getMainImg(),spuVO.getMainImg())){
                    mku.setMainImg(spuVO.getMainImg());
                }

                if (StringUtils.isNotBlank(skuDetailImg)){
                    if(!Objects.equals(mkuPO.getDetailImg(),skuDetailImg)) {
                        mku.setDetailImg(skuDetailImg);
                    }
                }else if (StringUtils.isNotBlank(spuVO.getDetailImg()) && !Objects.equals(mkuPO.getDetailImg(),spuVO.getDetailImg())){
                    mku.setDetailImg(spuVO.getDetailImg());
                }

                if(!Objects.equals(mkuPO.getDetailImg(),skuDetailImg))mku.setDetailImg(skuDetailImg);
                if(skuVO!=null){
                    if(StringUtils.isNotBlank(skuVO.getHeight())&&!Objects.equals(mkuPO.getHeight(),new BigDecimal(skuVO.getHeight())))mku.setHeight(new BigDecimal(skuVO.getHeight()));
                    if(StringUtils.isNotBlank(skuVO.getWeight())&&!Objects.equals(mkuPO.getWeight(),new BigDecimal(skuVO.getWeight())))mku.setWeight(new BigDecimal(skuVO.getWeight()));
                    if(StringUtils.isNotBlank(skuVO.getWidth())&&!Objects.equals(mkuPO.getWidth(),new BigDecimal(skuVO.getWidth())))mku.setWidth(new BigDecimal(skuVO.getWidth()));
                    if(StringUtils.isNotBlank(skuVO.getLength())&&!Objects.equals(mkuPO.getLength(),new BigDecimal(skuVO.getLength())))mku.setLength(new BigDecimal(skuVO.getLength()));
                    if(StringUtils.isNotBlank(skuVO.getUpcCode())&&!Objects.equals(mkuPO.getUpcCode(),skuVO.getUpcCode()))mku.setUpcCode(skuVO.getUpcCode());
                }
                updateBatchList.add(mku);
            }
            mkuAtomicService.updateBatchById(updateBatchList);
            log.info("同步mkuAtomicService==>jdi_isc_mku_sharding中的信息完成：{}",updateBatchList);
            List<MkuDescLangPO> mkuDescLangPOList = mkuDescLangAtomicService.listDescLang(mkuIds);
            List<MkuDescLangPO> updateBatchMkuDescLangList = new ArrayList<>();
            //PC商品详描 key->语言 value->详描信息
            Map<String, String> pcDescriptionMap = saveSpuVO.getPcDescriptionMap();
            //APP商品详描 key->语言 value->详描信息
            Map<String, String> appDescriptionMap = saveSpuVO.getAppDescriptionMap();
            if (CollectionUtils.isEmpty(mkuDescLangPOList)) {
                for (Long mkuId : mkuIds) {
                    if (MapUtils.isNotEmpty(pcDescriptionMap)) {
                        pcDescriptionMap.forEach((lang,description)->{
                            MkuDescLangPO mkuDescLang = new MkuDescLangPO();
                            mkuDescLang.setMkuId(mkuId);
                            mkuDescLang.setLang(lang);
                            mkuDescLang.setYn(YnEnum.YES.getCode());
                            mkuDescLang.setPcDescription(description);
                            updateBatchMkuDescLangList.add(mkuDescLang);
                        });
                    }
                }
                mkuDescLangAtomicService.saveBatch(updateBatchMkuDescLangList);
            }else {
                for (MkuDescLangPO mkuDescLangPO : mkuDescLangPOList) {
                    String lang = mkuDescLangPO.getLang();
                    MkuDescLangPO mkuDescLang = new MkuDescLangPO();
                    mkuDescLang.setId(mkuDescLangPO.getId());
                    if(pcDescriptionMap!=null&&!StringUtils.equals(mkuDescLangPO.getPcDescription(),pcDescriptionMap.get(lang)))mkuDescLang.setPcDescription(pcDescriptionMap.get(lang));
                    if(appDescriptionMap!=null&&!StringUtils.equals(mkuDescLangPO.getAppDescription(),appDescriptionMap.get(lang)))mkuDescLang.setAppDescription(appDescriptionMap.get(lang));
                    updateBatchMkuDescLangList.add(mkuDescLang);
                }
                mkuDescLangAtomicService.updateBatchById(updateBatchMkuDescLangList);
            }

            log.info("同步mkuDescLangAtomicService==>jdi_isc_mku_desc_lang_sharding中的信息完成：{}",updateBatchMkuDescLangList);
            Map<String, String>  mkuTitleMap=Maps.newHashMap();
            List<SpuLangExtendVO> spuLangExtendVOList = spuVO.getSpuLangExtendVOList();
            if(CollectionUtils.isNotEmpty(spuLangExtendVOList)){
                Map<String, String> spuTitleMap = spuLangExtendVOList.stream()
                        .collect(Collectors.toMap(
                                SpuLangExtendVO::getLang,
                                vo -> StringUtils.isNotBlank(vo.getSpuTitle()) ? vo.getSpuTitle() : "",
                                (existingValue, newValue) -> newValue
                        ));
                mkuTitleMap.putAll(spuTitleMap);
            } else {
                String spuZhName = spuVO.getSpuZhName();
                String spuVnName = StringUtils.isNotBlank(spuVO.getSpuVnName())?spuVO.getSpuVnName():"";
                String spuEnName = StringUtils.isNotBlank(spuVO.getSpuEnName())?spuVO.getSpuEnName():"";
                String spuThName = StringUtils.isNotBlank(spuVO.getSpuThName())?spuVO.getSpuThName():"";
                mkuTitleMap.put("zh",spuZhName);
                mkuTitleMap.put("th",spuThName);
                mkuTitleMap.put("en",spuEnName);
                mkuTitleMap.put("vi",spuVnName);
            }

            List<MkuLangPO> mkuLangPOS = mkuLangAtomicService.listLang2(mkuIds);
            Map<Long, Map<String, MkuLangPO>> mkuGroupMap = mkuLangPOS.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(MkuLangPO::getMkuId,Collectors.toMap(MkuLangPO::getLang, Function.identity(), (existValue, newValue) -> newValue)));
            List<MkuLangPO> updateBatchMkuLangPOList = new ArrayList<>();

            mkuGroupMap.forEach((mkuId,mkuLangPoMap) -> {
                mkuTitleMap.forEach((lang,mkuTitle) -> {
                    MkuLangPO mkuLang = new MkuLangPO();
                    if (mkuLangPoMap.containsKey(lang)) {
                        MkuLangPO mkuLangPO = mkuLangPoMap.get(lang);
                        mkuLang.setId(mkuLangPO.getId());
                        //mkuLang.setLang(lang);
                        if(!StringUtils.equals(mkuLangPO.getMkuTitle(),mkuTitle)){
                            mkuLang.setMkuTitle(mkuTitle);
                            updateBatchMkuLangPOList.add(mkuLang);
                        }else if(LangConstant.LANG_BR.equals(mkuLangPO.getLang()) && StringUtils.isBlank(mkuLangPO.getMkuSubtitle()) && StringUtils.isNotBlank(mkuLangPO.getMkuTitle())){
                            mkuLang.setMkuTitle(mkuTitle);
                            updateBatchMkuLangPOList.add(mkuLang);
                        }
                    }else {
                        // 新语种名称增加
                        mkuLang.setMkuId(mkuId);
                        mkuLang.setLang(lang);
                        mkuLang.setMkuTitle(mkuTitle);
                        mkuLang.setCreateTime(new Date());
                        mkuLang.setUpdateTime(new Date());
                        String operator = StringUtils.isNotBlank(spuVO.getUpdater()) ? spuVO.getUpdater() : Constant.SYSTEM;
                        mkuLang.setCreator(operator);
                        mkuLang.setUpdater(operator);
                        updateBatchMkuLangPOList.add(mkuLang);
                    }
                });
            });

            mkuLangAtomicService.saveOrUpdateBatch(updateBatchMkuLangPOList);
            log.info("同步mkuLangAtomicService==>jdi_isc_mku_lang_sharding中的信息完成：{}",updateBatchMkuLangPOList);
            for (MkuLangPO mkuLangPO : updateBatchMkuLangPOList){
                if(LangConstant.LANG_BR.equals(mkuLangPO.getLang()) && StringUtils.isNotBlank(mkuLangPO.getMkuTitle())) {
                    CompletableFuture.runAsync(() -> this.updateMkuLang(mkuLangPO));
                }
            }

            List<MkuPO> onSaleMkuList = Lists.newArrayList();
            for (MkuPO mkuPO : mkuPOS) {
                if (MkuStatusEnum.WAIT_APPROVE.getStatus() == mkuPO.getMkuStatus()) {
                    continue;
                }
                MkuPO newMkuPo = new MkuPO();
                newMkuPo.setMkuId(mkuPO.getMkuId());
                newMkuPo.setMkuStatus(MkuStatusEnum.ON_SALE.getStatus());
                newMkuPo.setUpdater(mkuPO.getUpdater());
                newMkuPo.setUpdateTime(new Date());
                onSaleMkuList.add(newMkuPo);
            }

            // 修改mku状态触发发送消息给科技
            if (CollectionUtils.isNotEmpty(onSaleMkuList)) {
                mkuAtomicService.updateBatchById(onSaleMkuList);
            }
            log.info("同步mkuAtomicService==>修改MKU状态信息完成：{}",onSaleMkuList);

        }
        return true;
    }

    /**
     * 更新多语言字幕信息
     * @param mkuLangPo 多语言字幕信息对象
     */
    public void updateMkuLang(MkuLangPO mkuLangPo){
        if(Objects.isNull(mkuLangPo) || StringUtils.isBlank(mkuLangPo.getMkuTitle())){
            log.info("MkuManageServiceImpl.updateSubtitle 标题为空 mkuLangPo:{}",JSON.toJSONString(mkuLangPo));
            return;
        }
        MkuLangVO mkuLangVO = MkuLangConvert.INSTANCE.po2VO(mkuLangPo);
        mkuLangManageService.saveMkuLang(mkuLangVO);
    }

    @Override
    public Long existsMku(MkuClientPageReqVO input) {
        String keyword = input.getKeyword();
        if(StringUtils.isBlank(keyword)){
            log.info("MkuManageServiceImpl.existsMku param is null");
        }

        String clientCode = input.getClientCode();
        CustomerVO customerVO = customerManageService.detail(clientCode);
        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
            log.info("MkuManageServiceImpl.existsMku, not exists. customerVO error. clientCode={}, customerVO={}", clientCode, JSONObject.toJSONString(customerVO));
            return null;
        }

        if (commonService.stationForbidden(customerVO.getCountry(), input.getStationType())) {
            log.info("MkuManageServiceImpl.existsMku, not exists. stationForbidden. input={}", JSONObject.toJSONString(input));
            return null;
        }

        if(this.isLong(keyword)){
            CustomerMkuPO customerMkuPO = customerMkuAtomicService.getOneValid(Long.valueOf(keyword), input.getClientCode());
            if (null != customerMkuPO) {
                return customerMkuPO.getMkuId();
            }
        }

        List<MkuMaterialPO> mkuMaterialPOS = mkuMaterialAtomicService.queryMaterialPOByMaterialId(keyword,clientCode);
        if(CollectionUtils.isEmpty(mkuMaterialPOS)){
            log.info("MkuManageServiceImpl.existsMku, not exists. mku is null. input={}", JSONObject.toJSONString(input));
            return null;
        }

        return mkuMaterialPOS.get(0).getMkuId();
    }

    @Override
    public Long queryMkuBySkuId(Long skuId) {
        MkuRelationPO mkuRelationPO = mkuRelationAtomicService.getMkuBySkuId(skuId);
        if(mkuRelationPO == null){
            log.error("MkuManageServiceImpl.queryMkuBySkuId, mkuRelationPO is null");
            return null;
        }
        return mkuRelationPO.getMkuId();
    }

    /**
     * 校验参数
     * */
    private void checkParam(MkuClientDetailReqVO input){
        if (null == input.getMkuId()) {
            log.error("MkuManageServiceImpl.checkParam, mkuId error. input={}", JSONObject.toJSONString(input));
            throw new BizException("MkuManageServiceImpl.checkParam, mkuId is null");
        }

        if (StringUtils.isBlank(input.getClientCode())) {
            log.warn("MkuManageServiceImpl.checkParam, clientCode error. input={}", JSONObject.toJSONString(input));
            throw new BizException("MkuManageServiceImpl.checkParam, clientCode is null");
        }
    }

    private void checkCommonParam(MkuClientDetailReqVO input) {
        //mku及clientCode创建
        checkParam(input);

        //国家信息创建
        if (StringUtils.isBlank(input.getCountry())) {
            log.warn("MkuManageServiceImpl.checkParam, CountryCode error. input={}", JSONObject.toJSONString(input));
            throw new BizException("MkuManageServiceImpl.checkParam, CountryCode is null");
        }
    }

    /**
     * 补充展示的商品信息
     * @param clientVOList
     * @param customerVO
     * @param lang
     * @param stationType
     */
    public void buildMkuShowInfo(List<MkuClientVO> clientVOList, CustomerVO customerVO, String lang, Integer stationType){
        String clientCode = customerVO.getClientCode();
        Set<Long> mkuIds = clientVOList.stream().map(MkuClientVO::getMkuId).collect(Collectors.toSet());

        String productSourceCountry = commonService.productSourceCountry(customerVO.getCountry(), stationType);

        // 查名称多语言
        MkuOperateVO mkuLang = new MkuOperateVO(mkuIds, lang);
        Map<Long, MkuLangPO> mkuLangMap = mkuLangAtomicService.listLang(mkuLang);

        // 查询价格
        MkuPriceReqVO priceReqVO = new MkuPriceReqVO();
        priceReqVO.setClientCode(clientCode);
        priceReqVO.setMkuIdList(mkuIds);
        priceReqVO.setSourceCountryCode(productSourceCountry);

        Map<Long, MkuPriceResVO> priceMap = new HashMap<>();
        // sku、mku映射关系
        Map<Long/*skuId*/, Long/*mkuId*/> fixSkuIdMap = new HashMap<>();

        DataResponse<List<MkuPriceResVO>> priceResponse = mkuClientPriceManageService.listMkuPrice(priceReqVO);
        if (priceResponse.getSuccess() && CollectionUtils.isNotEmpty(priceResponse.getData())) {
            priceResponse.getData().stream()
                    .filter(Objects::nonNull).forEach(mkuPriceReadResp -> {
                        priceMap.put(mkuPriceReadResp.getMkuId(), mkuPriceReadResp);
                        fixSkuIdMap.put(mkuPriceReadResp.getSkuId(), mkuPriceReadResp.getMkuId());
                    });
        }
        // 固定sku信息
        Map<Long/*skuId*/, SkuPO> skuPOMap = skuAtomicService.batchQuerySkuPO(fixSkuIdMap.keySet());
        Map<Long/*mkuId*/, SkuPO> skuRef = buildMkuRef(priceMap, skuPOMap);
        Set<Long> jdSkuIds = skuPOMap.values().stream().filter(line -> CountryConstant.COUNTRY_ZH.equals(line.getSourceCountryCode())).map(SkuPO::getJdSkuId).collect(Collectors.toSet());
        Map<Long, Integer> mkuMoqMap = buildMoq(skuRef,customerVO.getCountry());
        Map<Long, IscMkuStockResDTO> mkuStockMap = buildStock(fixSkuIdMap,mkuMoqMap,clientCode,stationType,customerVO.getContractCode());
        //货期 1、列表页面的货期页面上没展示。2、buildCnProductDeliveryDate 返回的值不准。 先屏蔽掉
//        Map<Long, Integer> deliveryDateMap = buildCnProductDeliveryDate(clientCode, jdSkuIds, lang);
        DeliveryTemplate deliveryTemplate = JSONObject.parseObject(deliveryText, DeliveryTemplate.class);
        Map<Long, CountryMkuPO> countryPoolMap =  getCountryPoolMap(mkuIds, customerVO.getCountry());
        for (MkuClientVO clientVO : clientVOList) {
            // 设置sku信息
            buildSkuId(clientVO, skuRef);

            // 设置价格
            if (MapUtils.isNotEmpty(priceMap) && null != priceMap.get(clientVO.getMkuId())) {
                MkuPriceResVO mkuPriceReadResp = priceMap.get(clientVO.getMkuId());
                clientVO.setSalePrice(mkuPriceReadResp.getSalePrice());
                clientVO.setCurrency(mkuPriceReadResp.getCurrency());

                // 显示币种和价格
                ProductPricesDTO productPricesDTO = new ProductPricesDTO();
                productPricesDTO.setCurrency(mkuPriceReadResp.getCurrency());
                productPricesDTO.setCurrencySource(mkuPriceReadResp.getOriginCurrency());
                productPricesDTO.setExchangeRate(mkuPriceReadResp.getExchangeRate());
                productPricesDTO.setSalePrice(mkuPriceReadResp.getSalePrice());
                productPricesDTO.setIncludeTaxPrice(mkuPriceReadResp.getIncludeTaxPrice());
                productPricesDTO.setValueAddedTax(mkuPriceReadResp.getTaxPrice());
                productPricesDTO.setValueAddedTaxRate(mkuPriceReadResp.getValueAddedTax());
                clientVO.setShowCurrency(productPricesDTO);

                // 多币种价格
                Map<String, ProductPricesDTO> pricesDTOMap = Maps.newHashMap();
                pricesDTOMap.put(mkuPriceReadResp.getCurrency(), productPricesDTO);
                clientVO.setCurrenciesPrices(pricesDTOMap);
                //moq
                clientVO.setMoq(mkuMoqMap.get(clientVO.getMkuId()));
                if (MapUtils.isNotEmpty(mkuStockMap)){
                    //现货库存
                    buildStock(clientVO, mkuStockMap.get(clientVO.getMkuId()));
                }
                //货期
                /*if(MapUtils.isNotEmpty(deliveryDateMap)){
                    Integer deliveryDays = deliveryDateMap.get(skuRef.get(clientVO.getMkuId()).getJdSkuId());
                    if(deliveryDays!=null){
                        String langTemp;
                        //期货
                        if(deliveryDays>=deliveryTemplate.getThreshold()){
                            langTemp = DeliveryTemplate.getByLang(deliveryTemplate.getFutureDelivery(), lang);
                            clientVO.setDeliveryDate(String.format(langTemp,deliveryDays));
                        }
                    }
                }*/
            }

            // 设置商品名称
            MkuLangPO langPO = mkuLangMap.get(clientVO.getMkuId());
            clientVO.setMkuName(null != langPO ? langPO.getMkuTitle() : "");

            // 设置品牌信息
            BrandClientVO brandClientVO = new BrandClientVO();
            brandClientVO.setId(clientVO.getBrandId());
            clientVO.setBrand(brandClientVO);

            if(countryPoolMap.containsKey(clientVO.getMkuId())){
                clientVO.setCountryInPoolFlag(Boolean.FALSE);
            }else {
                clientVO.setCountryInPoolFlag(Boolean.TRUE);
            }
        }

        if (null != commonDUCCConfig && Objects.equals(Boolean.TRUE, commonDUCCConfig.getLogDebugSwitch())) {
            log.info("MkuManageServiceImpl.buildMkuShowInfo. result={}", JSONObject.toJSONString(clientVOList));
        }
    }

    /**
     * 构建库存对象
     * @param mkuVO
     * @param stockResVO
     */
    private void buildStock(MkuClientVO mkuVO, IscMkuStockResDTO stockResVO){
        // 库存对象
        MkuClientStockVO mkuClientStockVO = MkuClientStockConvert.INSTANCE.iscMkuStockResDTO2VO(stockResVO);
        if (null==mkuClientStockVO){
            mkuClientStockVO = new MkuClientStockVO();
        }

        mkuVO.setStock(mkuClientStockVO);
    }

    /**
     * 设置skuId
     * @param mkuVO
     * @param mkuMap
     */
    private void buildSkuId(MkuClientVO mkuVO, Map<Long/*mkuId*/, SkuPO> mkuMap){
        if (MapUtils.isEmpty(mkuMap)) {
            log.info("buildSkuId, mkuMap is empty, mkuVO={}", JSONObject.toJSONString(mkuVO));
            return;
        }

        SkuPO skuPO = mkuMap.get(mkuVO.getMkuId());
        if (null==skuPO){
            log.info("buildSkuId, skuPO null, mkuVO={}", JSONObject.toJSONString(mkuVO));
            return;
        }
        // 设置skuId
        mkuVO.setSkuId(skuPO.getSkuId());
    }

    private Map<Long, CountryMkuPO> getCountryPoolMap(Set<Long> mkuIds, String targetCountryCode){
        List<CountryMkuPO> poList = countryMkuAtomicService.getPoByMkuIdCountryPoolStatus(new ArrayList<>(mkuIds),targetCountryCode,
            CountryMkuPoolStatusEnum.BLACK.getCode());
        Map<Long, CountryMkuPO> mkuCountryMap = poList.stream().collect(Collectors.toMap(CountryMkuPO::getMkuId, Function.identity()));
        return mkuCountryMap;
    }

    private Map<Long, SkuPO> buildMkuRef(Map<Long, MkuPriceResVO> priceMap, Map<Long, SkuPO> skuPOMap) {
        Map<Long, SkuPO> result = new HashMap<>();
        for (Map.Entry<Long, MkuPriceResVO> entry : priceMap.entrySet()) {
            if(skuPOMap.containsKey(entry.getValue().getSkuId())){
                result.put(entry.getKey(),skuPOMap.get(entry.getValue().getSkuId()));
            }else {
                log.error("MkuClientManageServiceImpl.buildMkuRef error, sku:{} 未能查询到 po信息{} ", entry.getValue().getSkuId(),entry.getKey());
            }
        }
        return result;
    }

    /** 构建mku和jdsSku关系*/
    private Map<Long, Integer> buildMoq(Map<Long, SkuPO> req,String country) {
        Map<Long, Integer> result = new HashMap<>();
        try {
            //跨境sku  <jdSku,mku关系>
  /*          Map<Long,Long> crossMap = new HashMap<>();
            for (Map.Entry<Long, SkuPO> entry : req.entrySet()) {
                if(entry.getValue()!=null && StringUtils.isNotEmpty(entry.getValue().getSourceCountryCode())){
                    if(CountryConstant.COUNTRY_ZH.equals(entry.getValue().getSourceCountryCode())){
                        crossMap.put(entry.getValue().getJdSkuId(),entry.getKey());
                    }else {
                        //处理本土moq
                        result.put(entry.getKey(),entry.getValue().getMoq());
                    }
                }else {
                    log.error("MkuClientManageServiceImpl.buildStock error, moq构建脏数据:{}", JSON.toJSONString(entry.getValue()));
                    Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_COMMON_WARNING, String.format("【%s】%s moq构建脏数据 %s ",systemProfile, LevelCode.P2.getMessage(),JSON.toJSONString(entry.getValue())));
                }
            }
            //处理跨境moq
            if(MapUtils.isNotEmpty(crossMap)){
                Map<Long, JdProductDTO> gmsSkuMap = skuInfoRpcService.querySkuMap(new JdProductQueryDTO(new ArrayList<>(crossMap.keySet())));
                for (Map.Entry<Long, JdProductDTO> entry : gmsSkuMap.entrySet()) {
                    Long mkuId = crossMap.get(entry.getKey());
                    String lb = entry.getValue().getLowestBuy();
                    if(StringUtils.isBlank(lb)){
                        result.put(mkuId,1);
                    }else {
                        result.put(mkuId,Integer.valueOf(lb));
                    }
                }
            }*/
            // 查询SKU 最小起订量
            Set<Long> skuIds = req.values().stream().filter(Objects::nonNull).map(SkuPO::getSkuId).collect(Collectors.toSet());
            Map<Long, SkuLimitBuyVO> skuLimitBuyVOMap = skuReadManageService.querySkuLimitBuyMap(new QuerySkuAvailableSaleReqVO(skuIds, country));

            for (Map.Entry<Long, SkuPO> entry : req.entrySet()) {
                SkuPO skuPO = entry.getValue();
                result.put(entry.getKey(), skuLimitBuyVOMap.getOrDefault(skuPO.getSkuId(), new SkuLimitBuyVO(skuPO.getSkuId(),Constant.ONE)).getMoq());
            }
        }catch (Exception e){
            log.error("MkuClientManageServiceImpl.buildMoq error, target:{} ,errMsg:{}", JSON.toJSONString(req),e);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_COMMON_WARNING, String.format("【%s】%s moq构建异常 %s ",systemProfile,LevelCode.P2.getMessage(), e));
        }
        log.info("MkuClientManageServiceImpl.buildMoq req:{} , res:{}" , JSON.toJSONString(req) , JSON.toJSONString(result));
        return result;
    }

    //构建现货库存信息
    private Map<Long, IscMkuStockResDTO> buildStock(Map<Long, Long> fixSkuIdMap,Map<Long, Integer> mkuMoq,String clientCode,Integer stationType,String contractNum) {
        Map<Long, IscMkuStockResDTO> resultStockMap = null;
        try {

            StockReadReqVO stockReadReqVO = new StockReadReqVO();
            stockReadReqVO.setClientCode(clientCode);
            stockReadReqVO.setStationType(stationType);
            stockReadReqVO.setContractNum(contractNum);

            IscMkuStockReadReqDTO readReqDTO = new IscMkuStockReadReqDTO();
            readReqDTO.setClientCode(clientCode);
            List<IscMkuStockItemReadReqDTO> stockItemReadReqVOList = Lists.newArrayList();
            for (Map.Entry<Long, Long> entry : fixSkuIdMap.entrySet()) {
                IscMkuStockItemReadReqDTO stockItemReadReqVO = new IscMkuStockItemReadReqDTO();
                stockItemReadReqVO.setMkuId(entry.getValue());
                stockItemReadReqVO.setNum(mkuMoq.getOrDefault(entry.getKey(),1));
                stockItemReadReqVOList.add(stockItemReadReqVO);
                readReqDTO.setStockItem(stockItemReadReqVOList);
            }

            resultStockMap = mkuStockService.getStock(readReqDTO);
            return resultStockMap;
        }catch (Exception e){
            log.error("MkuClientManageServiceImpl.buildStock error, target:{} ,errMsg:", JSON.toJSONString(fixSkuIdMap),e);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING, String.format("【%s】%s 库存构建异常 %s ",systemProfile,LevelCode.P1.getMessage(),e));
        }
        log.info("MkuClientManageServiceImpl.buildStock clientCode={} , resultStockMap={}" ,  clientCode, JSON.toJSONString(resultStockMap));
        return new HashMap<>();
    }

    private boolean isLong(String input) {
        try {
            Long.parseLong(input);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 根据mkuIds查询对应的SkuPO集合，返回一个Map，key为mkuId，value为对应的SkuPO。
     * @param mkuIds 客户mku的id列表
     * @return 一个Map，key为mkuId，value为对应的SkuPO
     */
    @Override
    @PFTracing
    public Map<Long, SkuPO> queryMkuIdSkuPoMapByMkuIds(List<Long> mkuIds) {
        Map<Long, List<MkuRelationPO>> mkuBindSkuListMap = mkuRelationAtomicService.queryMkuBindSkuListMap(mkuIds);
        Map<Long,Long> mkuSkuMap = Maps.newHashMap();
        mkuBindSkuListMap.forEach((mkuId,skuList)-> {
            // mku和sku关系简历最早为固定sku
            MkuRelationPO fixSkuPo = skuList.stream().min(Comparator.comparing(MkuRelationPO::getCreateTime)).orElse(null);
            if (null == fixSkuPo) {
                throw new RuntimeException(String.format("mku %s 未能找到固定sku",mkuId));
            }
            mkuSkuMap.put(mkuId, fixSkuPo.getSkuId());
        });

        Map<Long,SkuPO> mkuIdSkuPOMap = Maps.newHashMap();
        if(CollectionUtils.isEmpty(mkuSkuMap.values())){
            return mkuIdSkuPOMap;
        }
        // skuId 和 SkuPo
        Map<Long,SkuPO> skuIdSkuPOMap = skuAtomicService.getSkuMap(Sets.newHashSet(mkuSkuMap.values()));
        mkuSkuMap.forEach((mkuId,skuId)->{
            if(skuIdSkuPOMap.containsKey(skuId)){
                SkuPO skuPO = skuIdSkuPOMap.get(skuId);
                mkuIdSkuPOMap.put(mkuId,skuPO);
            }
        });
        return mkuIdSkuPOMap;
    }

    @Override
    public List<MkuClientInPoolVO> checkInPoolNoBlack(List<MkuClientDetailReqVO> mkuReqList) {
        Map<String, CustomerVO> map = customerManageService.map();
        List<CustomerMkuVO> customerMkuVOList = new ArrayList<>();
        for (MkuClientDetailReqVO req : mkuReqList){
            if(map.containsKey(req.getClientCode())){
                CustomerVO customerVO = map.get(req.getClientCode());
                CustomerMkuVO customerMkuVO = new CustomerMkuVO();
                customerMkuVO.setMkuId(req.getMkuId());
                customerMkuVO.setTargetCountryCode(customerVO.getCountry());
                customerMkuVOList.add(customerMkuVO);
            }
        }
        Map<String, CountryMkuPO> countryPoolMap = getCountryPoolMap(customerMkuVOList);
        List<MkuClientInPoolVO> list = new ArrayList<>();
        for (MkuClientDetailReqVO req : mkuReqList){
            MkuClientInPoolVO vo = new MkuClientInPoolVO();
            vo.setClientCode(req.getClientCode());
            vo.setMkuId(req.getMkuId());
            if(countryPoolMap.containsKey(req.getMkuId()+ req.getCountry())){
                vo.setCountryPoolNotBlackFlag(false);
            }else {
                vo.setCountryPoolNotBlackFlag(true);
            }
            list.add(vo);
        }
        return list;
    }


    /**
     * 根据客户MKU信息获取国家MKU映射表
     * @param list 客户MKU信息列表
     * @return 国家MKU映射表，key为MKU ID和目标国家代码的组合，value为对应的CountryMkuPO对象
     */
    private Map<String,CountryMkuPO> getCountryPoolMap(List<CustomerMkuVO> list){
        List<CountryMkuVO> voList = new ArrayList<>();
        for (CustomerMkuVO customerMkuVO : list){
            CountryMkuVO countryMkuVO = new CountryMkuVO();
            countryMkuVO.setMkuId(customerMkuVO.getMkuId());
            countryMkuVO.setTargetCountryCode(customerMkuVO.getTargetCountryCode());
            voList.add(countryMkuVO);
        }
        List<CountryMkuPO> poList = countryMkuAtomicService.listSearch(voList);
        Map<String, CountryMkuPO> mkuCountryMap = poList.stream().collect(Collectors.toMap(po -> {
            return po.getMkuId() + po.getTargetCountryCode();
        }, Function.identity()));
        return mkuCountryMap;
    }
    /** 对外查询查询mku信息 */
    @Override
    public Map<Long, IscMkuResDTO> getIscMku(BatchQueryMkuReqDTO req) {
        CustomerVO customer = customerManageService.detail(req.getClientCode());
        if(customer==null){
            throw new RuntimeException(String.format("非法的客户信息,请检查 %s",req.getClientCode()));
        }
        Map<Long, IscMkuResDTO> result;
        //mku基本信息
        Map<Long, MkuPO> mkuBaseMap = mkuAtomicService.getByMkuIds(req.getMkuId());
        if(MapUtils.isEmpty(mkuBaseMap)){
            throw new RuntimeException(String.format("MKU数据归档或失效,请检查 %s",req.getMkuId()));
        }
        Set<String> allLang = new HashSet<>(langManageService.getLangCodeList());

        //构建mku基础信息
        result = buildMkuBaseInfo(req,mkuBaseMap,customer,allLang);
        Map<Long, List<MkuRelationPO>> dbMkuSkuMap = mkuRelationAtomicService.queryMkuBindSkuListMap(new ArrayList<>(req.getMkuId()));
        if(MapUtils.isEmpty(dbMkuSkuMap)){
            throw new RuntimeException(String.format("MKU数据异常,未能获取有效的sku信息 %s",req.getMkuId()));
        }
        //mku&sku一对多取第一个
        Map<Long, MkuRelationPO> mkuSkuMap = dbMkuSkuMap.entrySet().stream().filter(entry -> entry.getValue() != null && !entry.getValue().isEmpty()).collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().get(0)));
        //构建mku扩展信息
        buildExtendInfo(result, mkuSkuMap,req,customer,allLang);
        //构建mku价格信息
        buildPriceInfo(result,mkuSkuMap,req,customer);
        return result;
    }

    @Override
    public Map<Long, Set<Long>> getIscMkuIdByJdSkuId(BatchQueryMkuReqDTO req) {
        Map<Long, Set<Long>> result = new HashMap<>();
        List<SkuPO> skuBaseList = skuAtomicService.querySkuPoMapByJdSkuIds(req.getJdSkuId());//会存在多条同jdSkuId对应多个国际skuId的数据
        if(CollectionUtils.isEmpty(skuBaseList)){
            return null;
        }
        //国际sku&jdSku关系
        Map<Long, SkuPO> skuMap = skuBaseList.stream().collect(Collectors.toMap(SkuPO::getSkuId, Function.identity()));
        //国际sku&mku关系
        Map<Long, Long> skuMku = mkuRelationAtomicService.queryMkuIdBySkuIds(skuBaseList.stream().map(SkuPO::getSkuId).collect(Collectors.toSet()));
        if(MapUtils.isEmpty(skuMku)){
            return null;
        }
        for (Map.Entry<Long, Long> entry : skuMku.entrySet()) {
            Long skuId = entry.getKey();
            Long mkuId = entry.getValue();
            SkuPO skuPo = skuMap.get(skuId);
            if(result.get(skuPo.getJdSkuId())!=null){
                result.get(skuPo.getJdSkuId()).add(mkuId);
            }else {
                result.put(skuPo.getJdSkuId(),Sets.newHashSet(mkuId));
            }
        }
        return result;
    }

    //构建mku价格信息
    private void buildPriceInfo(Map<Long, IscMkuResDTO> result, Map<Long, MkuRelationPO> mkuSkuMap, BatchQueryMkuReqDTO req,CustomerVO customer) {
        if((!req.getQueryEnum().contains(MkuQueryEnum.ALL) && !req.getQueryEnum().contains(MkuQueryEnum.PRICE)) || CollectionUtils.isEmpty(mkuSkuMap.values())){
            return;
        }
        MkuPriceReqVO priceReq = new MkuPriceReqVO();
        priceReq.setClientCode(req.getClientCode());
        priceReq.setSkuIdList(mkuSkuMap.values().stream().map(MkuRelationPO::getSkuId).collect(Collectors.toSet()));
        //sku及价格关系
        Map<Long, CustomerSkuPricePO> priceRes = customerSkuPriceManageService.getBaseSaleAndSupplierPrice(priceReq);
        for (Map.Entry<Long, IscMkuResDTO> entry : result.entrySet()) {
            IscMkuResDTO target = entry.getValue();
            MkuRelationPO mkuSku = mkuSkuMap.get(entry.getKey());
            if(mkuSku!=null){
                CustomerSkuPricePO priceDTO = priceRes.get(mkuSku.getSkuId());
                if(priceDTO!=null){
                    BigDecimal salePrice = priceDTO.getCustomerSalePrice();
                    BigDecimal purchasePrice = priceDTO.getCustomerPurchasePrice();
                    if(salePrice==null){
                        throw new RuntimeException(String.format("%s销价为空,请联系业务维护.",mkuSku.getSkuId()));
                    }
                    if(purchasePrice==null){
                        throw new RuntimeException(String.format("%s采购价为空,请联系管理员.",mkuSku.getSkuId()));
                    }
                    //跨境品需将人民币转为客户目标币种
                    if(CurrencyConstant.CURRENCY_ZH.equals(priceDTO.getCurrency())){
                        DataResponse<BigDecimal> exchangeRate = exchangeRateManageService.getExchangeRateByCurrency(CurrencyConstant.CURRENCY_ZH, customer.getSaleCurrency());
                        if(exchangeRate.getSuccess()){
                            salePrice = salePrice.multiply(exchangeRate.getData());
                            purchasePrice = purchasePrice.multiply(exchangeRate.getData());
                            log.info("MkuManageServiceImpl.buildPriceInfo {}当前为跨境品,需将RMB转换为{},当前汇率:{}" ,  priceDTO.getSkuId(),customer.getSaleCurrency(),exchangeRate.getData());
                        }
                    }
                    target.setPrice(new IscMkuPriceResDTO(
                            salePrice,
                            salePrice.subtract(purchasePrice).divide(salePrice,4, RoundingMode.HALF_UP),
                            customer.getSaleCurrency()
                    ));
                }
            }
        }
        log.info("MkuManageServiceImpl.buildPriceInfo req:{} , res:{}" ,  JSON.toJSONString(result), JSON.toJSONString(result));
    }

    //构建mku扩展信息
    private void buildExtendInfo(Map<Long, IscMkuResDTO> result, Map<Long, MkuRelationPO> mkuSkuMap,BatchQueryMkuReqDTO req ,CustomerVO customer,Set<String> allLang) {
        if((!req.getQueryEnum().contains(MkuQueryEnum.ALL) && !req.getQueryEnum().contains(MkuQueryEnum.EXTEND)) || CollectionUtils.isEmpty(mkuSkuMap.values())){
            return;
        }
        Set<Long> skuSet = mkuSkuMap.values().stream().map(MkuRelationPO::getSkuId).collect(Collectors.toSet());
        //sku和型号关系
        Map<Long, SkuPO> skuBaseMap = skuAtomicService.batchQuerySkuPO(skuSet);
        //mku销售单位关系
        Map<Long, Map<String, String>> mkuAllLangSaleUnitMap = getMkuSaleUnit(mkuSkuMap, skuBaseMap, allLang);
        Map<Long, Map<String, String>> mkuQueryLangSaleUnitMap = new HashMap<>();
        // sku的spu关系、型号
        Map<Long, SpuPO> spuPOMap = getSpuMap(skuBaseMap);
        if(MapUtils.isEmpty(spuPOMap)){
            throw new RuntimeException(String.format("系统异常:skuSet%s下未查到spu信息",skuSet));
        }
        //MKU销售单位:过滤不需要语种的信息,只保留查询语种信息
        for (Map.Entry<Long, Map<String, String>> mkuEntry : mkuAllLangSaleUnitMap.entrySet()) {
            Map<String, String> mkuLang = new HashMap<>();
            for(Map.Entry<String,String> langEntry : mkuEntry.getValue().entrySet()){
                if(req.getLangSet().contains(langEntry.getKey())){
                    mkuLang.put(langEntry.getKey(),langEntry.getValue());
                }
            }
            if(MapUtils.isNotEmpty(mkuLang)){
                mkuQueryLangSaleUnitMap.put(mkuEntry.getKey(),mkuLang);
            }
        }
        // 供应商简码和供应商信息的关系
        Map<String, SupplierBaseInfoPO> supplierBaseInfoPOMap = getSupplierMap(spuPOMap);
        // 查询MKU国家池关系
        Map<Long, MkuClientInPoolVO> mkuClientInPoolVOMap = getMkuInPoolMap(mkuSkuMap, customer);
        // 查询SKU的最小起订量
        Map<Long, SkuLimitBuyVO> skuLimitBuymap = skuReadManageService.querySkuLimitBuyMap(new QuerySkuAvailableSaleReqVO(skuSet, customer.getCountry()));
        for (Map.Entry<Long, IscMkuResDTO> entry : result.entrySet()) {
            IscMkuResDTO target = entry.getValue();
            MkuRelationPO mkuSku = mkuSkuMap.get(entry.getKey());
            if(mkuSku!=null){
                //中文销售单位
                target.setUnit(mkuAllLangSaleUnitMap.getOrDefault(entry.getKey(),new HashMap<>()).get(req.getLangSet().size()>1?LangConstant.LANG_ZH : req.getLangSet().stream().iterator().next()));
                target.setSaleUnitMap(mkuQueryLangSaleUnitMap.get(entry.getKey()));
                //型号
                SpuPO spuPO = spuPOMap.get(mkuSku.getSkuId());
                target.setModel(spuPO.getSpecification());
                //moq
                target.setMoq(skuLimitBuymap.getOrDefault(mkuSku.getSkuId(),new SkuLimitBuyVO(mkuSku.getSkuId(),Constant.ONE)).getMoq());
                // 供应商简码
                target.setSupplierCode(spuPO.getVendorCode());
                // 供应商名称
                if (supplierBaseInfoPOMap.containsKey(spuPO.getVendorCode())) {
                    target.setSupplierName(supplierBaseInfoPOMap.getOrDefault(spuPO.getVendorCode(),new SupplierBaseInfoPO()).getBusinessLicenseName());
                }
                // 是否在国家池
                if (mkuClientInPoolVOMap.containsKey(mkuSku.getMkuId()) && Objects.nonNull(mkuClientInPoolVOMap.get(mkuSku.getMkuId()))) {
                    MkuClientInPoolVO inPoolVO = mkuClientInPoolVOMap.get(mkuSku.getMkuId());
                    target.setInCountryPool(BooleanUtils.toInteger(inPoolVO.getCountryPoolFlag()));
                }
            }
        }
        log.info("MkuManageServiceImpl.buildExtendInfo req:{} , res:{}" ,  JSON.toJSONString(result), JSON.toJSONString(result));
    }

    private  Map<Long, MkuClientInPoolVO> getMkuInPoolMap(Map<Long, MkuRelationPO> mkuSkuMap, CustomerVO customer) {
        Map<Long,MkuClientInPoolVO> mkuClientInPoolVOMap = Maps.newHashMap();
        ArrayList<Long> mkuIdList = Lists.newArrayList(mkuSkuMap.keySet());
        List<CountryMkuPO> countryPoolStatus = countryMkuAtomicService.getPoByMkuIdCountryPoolStatus(mkuIdList, customer.getCountry(), CountryMkuPoolStatusEnum.POOL.getCode());
        Set<Long> poolMkuSet = Optional.ofNullable(countryPoolStatus).orElseGet(ArrayList::new).stream().map(CountryMkuPO::getMkuId).collect(Collectors.toSet());
        mkuIdList.forEach(mkuId->{
            MkuClientInPoolVO inPoolVO = new MkuClientInPoolVO(mkuId,Boolean.FALSE);
            if (poolMkuSet.contains(mkuId)){
                inPoolVO.setCountryPoolFlag(Boolean.TRUE);
                mkuClientInPoolVOMap.put(mkuId, inPoolVO);
            }
        });
        return mkuClientInPoolVOMap;
    }

    /**
     * 根据SPU信息获取供应商基础信息的映射关系
     * @param spuPOMap SPU信息的映射关系，key为SPU的ID，value为SPU的详细信息
     * @return 供应商基础信息的映射关系，key为供应商的代码，value为供应商的详细信息
     */
    private Map<String, SupplierBaseInfoPO> getSupplierMap(Map<Long, SpuPO> spuPOMap) {
        Map<String, SupplierBaseInfoPO> supplierBaseInfoPOMap = Maps.newHashMap();
        if (MapUtils.isNotEmpty(spuPOMap)) {
            Set<String> supplierCodeSet = spuPOMap.values().stream().map(SpuPO::getVendorCode).collect(Collectors.toSet());
            supplierBaseInfoPOMap = supplierBaseInfoManageService.batchQueryByCode(supplierCodeSet);
        }
        return supplierBaseInfoPOMap;
    }

    /** 构建MKU base信息*/
    private Map<Long, IscMkuResDTO> buildMkuBaseInfo(BatchQueryMkuReqDTO req, Map<Long, MkuPO> mkuBaseMap ,CustomerVO customer,Set<String> allLang) {
        //品牌多语言信息
        Map<Long, Map<String, String>> allLangBrandMap = brandLangAtomicService.queryBrandMap(mkuBaseMap.values().stream().map(MkuPO::getBrandId).collect(Collectors.toSet()), allLang);
        Map<Long, Map<String, String>> queryLangBrandMap = new HashMap<>();
        //mku多语言信息
        String clientLangCode = countryManageService.getLangByCountryCode(customer.getCountry());
        Map<Long, Map<String, String>> allLangMkuNameMap = mkuLangAtomicService.queryMkuLangMap(req.getMkuId(), new HashSet<>(allLang));
        Map<Long, Map<String, String>> queryLangMkuNameMap = new HashMap<>();
        //MKU名称:过滤不需要语种的信息,只保留查询语种信息
        for (Map.Entry<Long, Map<String, String>> mkuEntry : allLangMkuNameMap.entrySet()) {
            Map<String, String> mkuLang = new HashMap<>();
            for(Map.Entry<String,String> langEntry : mkuEntry.getValue().entrySet()){
                if(req.getLangSet().contains(langEntry.getKey())){
                    mkuLang.put(langEntry.getKey(),langEntry.getValue());
                }
            }
            if(MapUtils.isNotEmpty(mkuLang)){
                queryLangMkuNameMap.put(mkuEntry.getKey(),mkuLang);
            }
        }
        //品牌名称:过滤不需要语种的信息,只保留查询语种信息
        for (Map.Entry<Long, Map<String, String>> brandEntry : allLangBrandMap.entrySet()) {
            Map<String, String> brandLang = new HashMap<>();
            for(Map.Entry<String,String> langEntry : brandEntry.getValue().entrySet()){
                if(req.getLangSet().contains(langEntry.getKey())){
                    brandLang.put(langEntry.getKey(),langEntry.getValue());
                }
            }
            if(MapUtils.isNotEmpty(brandLang)){
                queryLangBrandMap.put(brandEntry.getKey(),brandLang);
            }
        }
        Map<Long, IscMkuResDTO> result = new HashMap<>();
        for(Long mkuId : req.getMkuId()){
            MkuPO mkuPo = mkuBaseMap.get(mkuId);
            if(mkuPo == null){
                log.error("mku不存在，mkuId:{}",mkuId);
                continue;
            }
            //mku名称 多语言取值逻辑:传了LangSet该字段固定为中文名称;未传LangSet取uniform中语种信息
            String mkuName = allLangMkuNameMap.getOrDefault(mkuId, new HashMap<>()).get(req.getLangSet().size()>1?LangConstant.LANG_ZH : req.getLangSet().stream().iterator().next());
            //客户主语言mku名称
            String clientLangMkuName = allLangMkuNameMap.getOrDefault(mkuId, new HashMap<>()).get(clientLangCode);
            //中文品牌名称
            String cnBrandName = allLangBrandMap.getOrDefault(mkuPo.getBrandId(), new HashMap<>()).get(req.getLangSet().size()>1?LangConstant.LANG_ZH : req.getLangSet().stream().iterator().next());
            //构建返回实体
            IscMkuResDTO mkuResDTO = new IscMkuResDTO(mkuId, mkuName,mkuPo.getJdCatId(), mkuPo.getBrandId(), cnBrandName, mkuPo.getMainImg(), clientLangMkuName, mkuPo.getSourceCountryCode());
            //多语言商品名称
            mkuResDTO.setMkuNameMap(queryLangMkuNameMap.get(mkuId));
            //多语言品牌名称
            mkuResDTO.setBrandNameMap(queryLangBrandMap.get(mkuPo.getBrandId()));
            //细节图
            mkuResDTO.setDetailImgList(StringUtils.isNotBlank(mkuPo.getDetailImg()) ? Arrays.stream(mkuPo.getDetailImg().split(Constant.HASHTAG)).collect(Collectors.toList()) : null);
            result.put(mkuId,mkuResDTO);
        }
        log.info("MkuManageServiceImpl.buildMkuBaseInfo req:{} , res:{}" , JSON.toJSONString(req) , JSON.toJSONString(result));
        return result;
    }

    /** 查询mku销售单位*/
    private Map<Long, Map<String,String>> getMkuSaleUnit(Map<Long, MkuRelationPO> mkuSkuMap, Map<Long, SkuPO> skuBaseMap, Set<String> langSet) {
        Map<Long, Map<String,String>> result = Maps.newHashMapWithExpectedSize(mkuSkuMap.size());
        Set<Long> spuIdSet = skuBaseMap.values().stream().map(SkuPO::getSpuId).collect(Collectors.toSet());
        Map<Long, SpuPO> spuBaseMap = spuAtomicService.getSpuMap(spuIdSet);
        for (Map.Entry<Long, MkuRelationPO> entry : mkuSkuMap.entrySet()) {
            Long skuId = entry.getValue().getSkuId();
            Long spuId = skuBaseMap.get(skuId).getSpuId();
            SpuPO spuBase = spuBaseMap.get(spuId);
            if(spuBase!=null && spuBase.getSaleUnit()!=null){
                Map<String, String> saleUnitMap = extendInfoService.saleUnit(spuBase.getSaleUnit(), langSet);
                if(MapUtils.isNotEmpty(saleUnitMap)){
                    result.put(entry.getKey(),saleUnitMap);
                }
            }
        }
        return result;
    }

    /**
     * 根据 SKU 基础信息 Map 获取对应的 SPu 信息 Map。
     * @return SPu 信息 Map，key 为 SKUID，value 为 SpuPO 对象。
     */
    private Map<Long,SpuPO> getSpuMap(Map<Long, SkuPO> skuBaseMap){
        if(MapUtils.isEmpty(skuBaseMap)){
            return new HashMap<>();
        }

        Collection<SkuPO> skuPOS = skuBaseMap.values();
        if(CollectionUtils.isEmpty(skuPOS)){
            return new HashMap<>();
        }

        Set<Long> spuIds = skuPOS.stream().map(SkuPO::getSpuId).collect(Collectors.toSet());
        List<SpuPO> spuPOS = spuAtomicService.querySpuList(spuIds);
        Map<Long,SpuPO> result = new HashMap<>();
        if(CollectionUtils.isNotEmpty(spuPOS)){
            Map<Long,SpuPO> spuPOMap = spuPOS.stream().collect(Collectors.toMap(SpuPO::getSpuId,Function.identity(),(v1,v2)->v2));
            for(SkuPO skuPO : skuPOS){
                result.put(skuPO.getSkuId(),spuPOMap.get(skuPO.getSpuId()));
            }
        }
        return result;
    }

    @Override
    public DataResponse<Map<Long, Map<String, String>>> querySpecialAttrMapByMkuIds(SpecialAttrMkuReqVO input) {
        Set<Long> mkuIds = input.getMkuIds();
        // 结果
        Map<Long, Map<String, String>> resultMap = Maps.newHashMap();
        // 查询MKU的信息
        Map<Long, MkuPO> mkuMap = mkuAtomicService.getByMkuIds(mkuIds);
        if (MapUtils.isEmpty(mkuMap)) {
            log.error("MkuManageServiceImpl.querySpecialAttrMapByMkuIds mkuMap is empty, input:{}", JSON.toJSONString(input));
            return DataResponse.buildSuccess(resultMap);
        }
        // MKU特殊属性集合
        Set<MkuSpecialAttrEnum> mkuSpecialAttrEnums = CollectionUtils.isNotEmpty(input.getSpecialAttrEnums()) ? input.getSpecialAttrEnums() : MkuSpecialAttrEnum.getEnumSetByCountryCode(input.getCountryCode());

        // 查询MKU关联的sku列表
        Map<Long, List<MkuRelationPO>> mkuBindSkuListMap = mkuRelationAtomicService.queryMkuBindSkuListMap(Lists.newArrayList(mkuIds));
        Set<Long> skuIds = Sets.newHashSet();
        mkuBindSkuListMap.values().forEach(skuList-> skuIds.addAll(skuList.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toSet())));


        // 查询MKU关联的SKU是否为备货
        Map<Long, Integer> skuPurchaseModelMap = mkuSpecialAttrEnums.contains(MkuSpecialAttrEnum.PURCHASE_MODEL) ?   skuFeatureManageService.querySkuPurchaseModel(skuIds, input.getCountryCode()) : Maps.newHashMap();

        // 查询mku是否在当前国家池
        Map<Long, CountryMkuPO> countryMkuPOMap = Maps.newHashMap();
        if (mkuSpecialAttrEnums.contains(MkuSpecialAttrEnum.COUNTRY_POOL)) {
            List<CountryMkuPO> countryMkuPoList = countryMkuAtomicService.getPoByMkuIdCountryPoolStatus(Lists.newArrayList(input.getMkuIds()), input.getCountryCode(), CountryMkuPoolStatusEnum.POOL.getCode());
            countryMkuPOMap = Optional.ofNullable(countryMkuPoList).orElseGet(ArrayList::new)
                    .stream().collect(Collectors.toMap(CountryMkuPO::getMkuId, Function.identity()));
        }


        // 查询MKU关联的SKU的特殊属性
        Set<SkuSpecialAttrEnum> skuSpecialAttrEnumSet = SkuSpecialAttrEnum.getEnumSetByKeys(mkuSpecialAttrEnums.stream().map(MkuSpecialAttrEnum::getKey).collect(Collectors.toSet()));

        // 查询sku特殊属性
        Map<Long, Map<String, String>> skuSpecialAttrMap = CollectionUtils.isNotEmpty(skuSpecialAttrEnumSet) ?  this.querySkuSpecialAttrMap(skuIds, skuSpecialAttrEnumSet) : Maps.newHashMap();

        for (Map.Entry<Long,MkuPO> mkuPOEntry : mkuMap.entrySet() ) {
            Long mkuId = mkuPOEntry.getKey();
            MkuPO mkuPo = mkuPOEntry.getValue();
            Map<String,String> specialAttrMap = Maps.newHashMap();
            // 在国家池
            if (mkuSpecialAttrEnums.contains(MkuSpecialAttrEnum.COUNTRY_POOL)){
                specialAttrMap.put(MkuSpecialAttrEnum.COUNTRY_POOL.getKey(), countryMkuPOMap.containsKey(mkuId) && null != countryMkuPOMap.get(mkuId)? String.valueOf(YesOrNoEnum.YES.getCode()) : String.valueOf(YesOrNoEnum.NO.getCode()));
            }
            // 跨境标 货源国时CN是跨境，其他国家是本土
            if (mkuSpecialAttrEnums.contains(MkuSpecialAttrEnum.CROSS_BORDER)) {
                specialAttrMap.put(MkuSpecialAttrEnum.CROSS_BORDER.getKey(),  String.valueOf( CountryConstant.COUNTRY_ZH.equals(mkuPo.getSourceCountryCode()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode()));
            }
            // 处理SKU纬度的标
            List<MkuRelationPO> mkuRelationPOList = mkuBindSkuListMap.get(mkuId);
            // 备货模式
            this.handlePurchaseModel(mkuRelationPOList, mkuSpecialAttrEnums, skuPurchaseModelMap, specialAttrMap);
            // SKU的特殊属性
            specialAttrMap.putAll(this.handleMkuRelationSkuSpecialAttr(mkuRelationPOList, skuSpecialAttrMap));

            resultMap.put(mkuId, specialAttrMap);
        }

        return DataResponse.success(resultMap);
    }

    /**
     * 处理购买模型的特殊属性。
     * @param mkuRelationPOList MKU 关系 PO 列表
     * @param mkuSpecialAttrEnums MKU 特殊属性枚举集合
     * @param skuPurchaseModelMap SKU 购买模型映射
     * @param specialAttrMap 特殊属性映射
     */
    private void handlePurchaseModel(List<MkuRelationPO> mkuRelationPOList, Set<MkuSpecialAttrEnum> mkuSpecialAttrEnums, Map<Long, Integer> skuPurchaseModelMap, Map<String, String> specialAttrMap) {
        if (CollectionUtils.isNotEmpty(mkuRelationPOList) && mkuSpecialAttrEnums.contains(MkuSpecialAttrEnum.PURCHASE_MODEL)) {
            // 备货模式
            int purchaseModel = 0;
            for (MkuRelationPO mkuRelationPO : mkuRelationPOList) {
                if (MapUtils.isNotEmpty(skuPurchaseModelMap) && skuPurchaseModelMap.containsKey(mkuRelationPO.getSkuId()) && Objects.nonNull(mkuRelationPO.getSkuId())){
                    // 按位或备货模式标，有一个是1就位1
                    purchaseModel = skuPurchaseModelMap.get(mkuRelationPO.getSkuId()) | purchaseModel;
                }
            }
            specialAttrMap.put(MkuSpecialAttrEnum.PURCHASE_MODEL.getKey(), String.valueOf(purchaseModel));
        }
    }

    /**
     * 根据给定的 SKU ID 集合和特殊属性枚举集合，查询并返回每个 SKU ID 对应的特殊属性键值对映射。
     * @param skuIds SKU ID 集合
     * @param skuSpecialAttrEnumSet 特殊属性枚举集合
     * @return 每个 SKU ID 对应的特殊属性键值对映射
     */
    private Map<Long, Map<String, String>> querySkuSpecialAttrMap(Set<Long> skuIds, Set<SkuSpecialAttrEnum> skuSpecialAttrEnumSet) {
        SpecialAttrSkuReqVO reqVO = new SpecialAttrSkuReqVO();
        reqVO.setSkuIds(Lists.newArrayList(skuIds));
        reqVO.setSkuSpecialAttrEnumSet(skuSpecialAttrEnumSet);
        DataResponse<Map<Long, Map<String, String>>> skuSpecialResponse = specialAttrManageService.querySpecialAttrMapBySkuIds(reqVO);

        return skuSpecialResponse.getData();
    }

    /**
     * 处理MkuRelationSku的特殊属性关系
     * @param mkuRelationPOList MkuRelationPO对象列表
     * @param skuSpecialAttrMap SKU的特殊属性映射表
     * @return 处理后的特殊属性映射表
     */
    private Map<String,String> handleMkuRelationSkuSpecialAttr(List<MkuRelationPO> mkuRelationPOList, Map<Long, Map<String, String>> skuSpecialAttrMap) {
        Map<String,String> specialAttrMap = Maps.newHashMap();
        for (MkuRelationPO mkuRelationPO : mkuRelationPOList) {
            // SKU的特殊属性
            if (MapUtils.isNotEmpty(skuSpecialAttrMap)) {
                // 默认将所有sku的标合并到一起
                specialAttrMap.putAll(skuSpecialAttrMap.get(mkuRelationPO.getSkuId()));
            }
        }
        return specialAttrMap;
    }

    @Override
    public DataResponse<List<MkuClientVO>> latestWares(CustomerLatestMkuReqVO input) {
        List<MkuClientVO> emptyResults = new ArrayList<>();
        Integer stationType = input.getStationType();
        String clientCode = input.getClientCode();

        CustomerVO customerVO = customerManageService.detail(clientCode);
        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
            log.warn("MkuManageServiceImpl.latestWares, customerVO error. clientCode={}, customerVO={}", clientCode, JSONObject.toJSONString(customerVO));
            return DataResponse.success(emptyResults);
        }

        if (commonService.stationForbidden(customerVO.getCountry(), stationType)) {
            log.warn("MkuManageServiceImpl.latestWares, stationForbidden. country={}, stationType={}", customerVO.getCountry(), JSONObject.toJSONString(stationType));
            return DataResponse.success(emptyResults);
        }

        input.setMaxQueryNum(15L);
        List<CustomerMkuPO> latestWaresList = customerMkuAtomicService.latestWares(input);
        if(CollectionUtils.isEmpty(latestWaresList)){
            log.warn("MkuManageServiceImpl.latestWares, latestWaresList empty. input={}", JSONObject.toJSONString(input));
            return DataResponse.success(emptyResults);
        }

        Set<Long> mkuIds = latestWaresList.stream().filter(o -> null != o && Objects.nonNull(o.getMkuId()))
                .map(CustomerMkuPO::getMkuId).collect(Collectors.toSet());
        List<MkuPO> mkuPOList = mkuAtomicService.listByMkuIds(Lists.newArrayList(mkuIds));
        if(CollectionUtils.isEmpty(mkuPOList)){
            log.warn("MkuManageServiceImpl.latestWares, mkuPOList empty. country={}, input={} ", customerVO.getCountry(), JSONObject.toJSONString(stationType));
            return DataResponse.success(emptyResults);
        }

        String country = customerVO.getCountry();
        Set<Long> stockUpMkuIds =  queryLocalStockUpSku(mkuIds,country);

        List<MkuClientVO> clientVOList = MkuConvert.INSTANCE.listMkuPo2mkuClientVo(mkuPOList);
        Map<Long,MkuClientVO> clientVoMap = clientVOList.stream().collect(Collectors.toMap(MkuClientVO::getMkuId, Function.identity()));

        List<MkuClientVO> clientVoListSort = new ArrayList<>(clientVOList.size());
        // 排序
        for(CustomerMkuPO customerMkuPO:latestWaresList){
            MkuClientVO mkuClientVO = clientVoMap.get(customerMkuPO.getMkuId());
            if (stockUpMkuIds.contains(customerMkuPO.getMkuId())) {
                mkuClientVO.setFeatureTagMap(Collections.singletonMap(MkuFeatureTagEnum.SHIPPING_48_HOUR.getCode(),MkuFeatureTagEnum.SHIPPING_48_HOUR.getDesc()));
            }
            clientVoListSort.add(mkuClientVO);
        }

        buildMkuShowInfo(clientVoListSort, customerVO, input.getLang(), stationType);
        return DataResponse.success(clientVoListSort);
    }

    /**
     * 查询关联备货sku的mku
     */
    public Set<Long> queryLocalStockUpSku(Set<Long> mkuIds, String country) {
        Set<Long> stockUpMku = new HashSet<>();
        try {
            if (!warehouseSkuCountry.contains(country)) {
                return stockUpMku;
            }
            //查询绑定的sku
            List<MkuRelationPO> mkuRelationList = mkuRelationAtomicService.queryBindListByMkuIds(new ArrayList<>(mkuIds));
            if (CollectionUtils.isNotEmpty(mkuRelationList)) {
                Map<String, Long> skuMkuMap = mkuRelationList.stream().collect(Collectors.toMap(c -> String.valueOf(c.getSkuId()), MkuRelationPO::getMkuId));
                QuerySkuRelationReqVO reqVO = new QuerySkuRelationReqVO();
                reqVO.setSkuIds(skuMkuMap.keySet());
                Map<String, Map<String, WarehouseSkuVO>> warehouseMap = warehouseSkuManageService.querySkuWarehouseRelationMap(reqVO);
                if (MapUtils.isEmpty(warehouseMap)) {
                    return Collections.emptySet();
                }
                //判断sku是否绑仓
                Map<Long, Set<Long>> warehouseRelMap = new HashMap<>();
                warehouseMap.values().forEach(w -> w.values().forEach(wp -> {
                    Set<Long> longs = warehouseRelMap.get(wp.getWarehouseId());
                    if (CollectionUtils.isEmpty(longs)) {
                        longs = new HashSet<>();
                    }
                    longs.add(wp.getSkuId());
                    warehouseRelMap.put(wp.getWarehouseId(), longs);
                }));
                //去查备货仓信息
                List<WarehouseResVO> warehouseDetails = warehouseManageService.queryByWarehouseIds(warehouseRelMap.keySet());
                //判断仓是否属于泰国
                warehouseDetails.forEach(w -> {
                    if (country.equals(w.getCountryCode())) {
                        Set<Long> skuIds = warehouseRelMap.get(w.getId());
                        if (CollectionUtils.isNotEmpty(skuIds)) {
                            skuIds.forEach(s -> {
                                Long mkuId = skuMkuMap.get(String.valueOf(s));
                                if (Objects.nonNull(mkuId)) {
                                    stockUpMku.add(mkuId);
                                }
                            });
                        }

                    }
                });
            }
        } catch (Exception e) {
            log.error("queryLocalStockUpSku mkuIds:{},country:{}",JSON.toJSONString(mkuIds),country,e);
        }
       return stockUpMku;

    }

    @Override
    public DataResponse<List<MkuClientVO>> queryWaresInfo(MkuListInfoReqReqVO input) {
        List<MkuClientVO> emptyResults = new ArrayList<>();
        Integer stationType = input.getStationType();
        String clientCode = input.getClientCode();
        Set<Long> mkuIdsReq = input.getMkuIds();

        CustomerVO customerVO = customerManageService.detail(clientCode);
        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
            log.warn("MkuManageServiceImpl.queryWaresInfo, customerVO error. clientCode={}, customerVO={}", clientCode, JSONObject.toJSONString(customerVO));
            return DataResponse.success(emptyResults);
        }

        if (commonService.stationForbidden(customerVO.getCountry(), stationType)) {
            log.warn("MkuManageServiceImpl.queryWaresInfo, stationForbidden. country={}, stationType={}", customerVO.getCountry(), JSONObject.toJSONString(stationType));
            return DataResponse.success(emptyResults);
        }

        // 查在池的商品
        List<CustomerMkuPO> customerMkuListInPool = customerMkuAtomicService.listCustomerMkuPOByMkuIds(mkuIdsReq, clientCode);
        if(CollectionUtils.isEmpty(customerMkuListInPool)){
            log.warn("MkuManageServiceImpl.queryWaresInfo, customerMkuListInPool empty. input={}", JSONObject.toJSONString(input));
            return DataResponse.success(emptyResults);
        }

        Set<Long> mkuIdsInPool = customerMkuListInPool.stream().filter(o -> null != o && Objects.nonNull(o.getMkuId()))
                .map(CustomerMkuPO::getMkuId).collect(Collectors.toSet());
        List<MkuPO> mkuPOList = mkuAtomicService.listByMkuIds(Lists.newArrayList(mkuIdsInPool));
        if(CollectionUtils.isEmpty(mkuPOList)){
            log.warn("MkuManageServiceImpl.queryWaresInfo, mkuPOList empty. country={}, input={} ", customerVO.getCountry(), JSONObject.toJSONString(stationType));
            return DataResponse.success(emptyResults);
        }

        List<MkuClientVO> clientVOList = MkuConvert.INSTANCE.listMkuPo2mkuClientVo(mkuPOList);
        Map<Long,MkuClientVO> clientVoMap = clientVOList.stream().collect(Collectors.toMap(MkuClientVO::getMkuId, Function.identity()));

        List<MkuClientVO> clientVoListSort = new ArrayList<>(clientVOList.size());
        Set<Long> mkuIds = queryLocalStockUpSku(clientVoMap.keySet(), customerVO.getCountry());
        // 排序
        for(Long mkuIdReq:mkuIdsReq){
            MkuClientVO vo = clientVoMap.get(mkuIdReq);
            if (null==vo){
                log.warn("MkuManageServiceImpl.queryWaresInfo, req mkuId not in pool. mkuIdReq={}, country={}, clientCode={}"
                        , mkuIdReq, customerVO.getCountry(), clientCode);
                continue;
            }
            if (mkuIds.contains(vo.getMkuId())) {
                vo.setFeatureTagMap(Collections.singletonMap(MkuFeatureTagEnum.SHIPPING_48_HOUR.getCode(),MkuFeatureTagEnum.SHIPPING_48_HOUR.getDesc()));
            }
            clientVoListSort.add(vo);
        }

        buildMkuShowInfo(clientVoListSort, customerVO, input.getLang(), stationType);
        return DataResponse.success(clientVoListSort);
    }

    @Override
    public DataResponse<Map<Long, Map<String, String>>> querySpecialAttrMapByClientCode(SpecialAttrMkuClientReqVO input) {
        CustomerVO customerVO = customerManageService.detail(input.getClientCode());
        if (Objects.isNull(customerVO)) {
            log.warn("MkuManageServiceImpl.querySpecialAttrMapByClientCode clientCode not exist input={}",JSON.toJSONString(input));
            return DataResponse.error(String.format("客户 %s 不存在", input.getClientCode()));
        }

        Set<Long> mkuIds = input.getMkuIds();
        // 结果
        Map<Long, Map<String, String>> resultMap = Maps.newHashMap();
        // 客制化价格
        Map<Long, CustomerMkuPricePO> customerMkuPricePOMap = customerMkuPriceAtomicService.queryByMkuAndClient(mkuIds, input.getClientCode());
        if (MapUtils.isEmpty(customerMkuPricePOMap)) {
            log.warn("kuManageServiceImpl.querySpecialAttrMapByClientCode customerMkuPricePOMap is empty input={}",JSON.toJSONString(input));
            return DataResponse.success(resultMap);
        }
        // 固定SKU集合
        Set<Long> skuIds = Sets.newHashSet();
        Map<Long,Long> mkuSkuRelationMap = Maps.newHashMap();
        customerMkuPricePOMap.forEach((mkuId, customerMkuPricePO) -> {
            skuIds.add(customerMkuPricePO.getFixedSkuId());
            mkuSkuRelationMap.put(mkuId, customerMkuPricePO.getFixedSkuId());
        });

        Set<MkuClientSpecialAttrEnum> specialAttrEnums = CollectionUtils.isNotEmpty(input.getSpecialAttrEnums()) ?  input.getSpecialAttrEnums() : Sets.newHashSet(MkuClientSpecialAttrEnum.values());
        input.setSpecialAttrEnums(specialAttrEnums);
        Map<Long, SkuPO> skuPOMap = skuAtomicService.batchQuerySkuPO(skuIds);
        //Map<Long, SkuFeaturePO> skuFeaturePOMap =  specialAttrEnums.contains(MkuClientSpecialAttrEnum.FULFIL_PROMISE) ?  skuFeatureAtomicService.querySkuFeatureMap(skuIds) : Maps.newHashMap();
        // 查询MKU关联的SKU是否为备货
        //Map<Long, Integer> skuPurchaseModelMap = specialAttrEnums.contains(MkuClientSpecialAttrEnum.FULFIL_PROMISE) ?   skuFeatureManageService.querySkuPurchaseModel(skuIds, customerVO.getCountry()) : Maps.newHashMap();

        //Map<Long, SkuDetailedPageInfoResp> jdProductDTOMap = getJdSkuPromiseMap(specialAttrEnums, skuPOMap,customerVO.getClientCode());
        // 查询履约时效特殊属性
        Map<Long, PromiseTimeTagResApiDTO> timeTagMap = getMkuPromiseTagMap(input);

        for (Map.Entry<Long, Long> entry : mkuSkuRelationMap.entrySet()) {
            Long mkuId = entry.getKey();
            Long skuId = entry.getValue();
            Map<String, String> specialMap = Maps.newHashMap();
            if (specialAttrEnums.contains(MkuClientSpecialAttrEnum.CROSS_BORDER)) {
                SkuPO skuPO = skuPOMap.get(skuId);
                specialMap.put(MkuClientSpecialAttrEnum.CROSS_BORDER.getKey(), CountryConstant.COUNTRY_ZH.equals(skuPO.getSourceCountryCode()) ? String.valueOf(Constant.ONE) : String.valueOf(Constant.ZERO));
            }
            if(timeTagMap.containsKey(mkuId) && Objects.nonNull(timeTagMap.get(mkuId)) && Objects.nonNull(timeTagMap.get(mkuId).getTagVal())){
                specialMap.put(MkuClientSpecialAttrEnum.FULFIL_PROMISE.getKey(),String.valueOf(timeTagMap.get(mkuId).getTagVal()));
            }
            //this.handleFulPromise(specialAttrEnums, customerVO, skuPurchaseModelMap, skuId, skuPOMap, skuFeaturePOMap, specialMap, jdProductDTOMap);
            resultMap.put(mkuId, specialMap);
        }
        return DataResponse.success(resultMap);
    }

    /**
     * 根据输入参数获取MKU承诺标签映射
     * @param input 特殊属性MKU客户端请求VO，包含MKU ID列表、客户端代码等特殊属性信息
     * @return 返回MKU ID与承诺标签响应DTO的映射关系，若输入不包含履约承诺属性则返回空Map
     */
    private Map<Long, PromiseTimeTagResApiDTO> getMkuPromiseTagMap(SpecialAttrMkuClientReqVO input) {
        if (!input.getSpecialAttrEnums().contains(MkuClientSpecialAttrEnum.FULFIL_PROMISE)) {
            return Collections.emptyMap();
        }
        Map<Long, PromiseTimeTagResApiDTO> timeTagMap = promiseTimeInfoRpcService.batchQueryPromiseTags(input.getClientCode(), Lists.newArrayList(input.getMkuIds()));
        return timeTagMap;
    }

    /**
     * 48小时达：入泰国备货仓仓商品，优先级最高；（库存系统中，商品和仓绑定的时候）
     * 72小时达：本本SKU，系统维护发货时效为1天，优先级第二；（跨境SKU没有这个标）
     * 京准达：（本本+跨境）有发货时效的商品，优先级第三；
     * 满足更高优先级的标时，低优先级标规则不生效。
     * @param specialAttrEnums 特殊属性枚举集合。
     * @param customerVO 客户信息对象。
     * @param skuPurchaseModelMap SKU购买模式映射。
     * @param skuId SKU ID。
     * @param skuPOMap SKU PO对象映射。
     * @param skuFeaturePOMap SKU特性PO对象映射。
     * @param specialMap 特殊属性映射。
     * @param jdProductDTOMap 京东产品DTO对象映射。
     */
    private void handleFulPromise(Set<MkuClientSpecialAttrEnum> specialAttrEnums, CustomerVO customerVO, Map<Long, Integer> skuPurchaseModelMap, Long skuId, Map<Long, SkuPO> skuPOMap, Map<Long, SkuFeaturePO> skuFeaturePOMap, Map<String, String> specialMap, Map<Long, SkuDetailedPageInfoResp> jdProductDTOMap) {
        if (specialAttrEnums.contains(MkuClientSpecialAttrEnum.FULFIL_PROMISE) && fulfilPromiseCountry.contains(customerVO.getCountry())) {
            Integer purchaseModel = skuPurchaseModelMap.get(skuId);
            SkuPO skuPO = skuPOMap.get(skuId);
            SkuFeaturePO skuFeaturePO = skuFeaturePOMap.get(skuId);
            if (Objects.nonNull(purchaseModel) && Objects.equals(PurchaseModelTypeEnum.STOCK_UP.getCode(), purchaseModel)) {
                // 备货品 48小时达
                specialMap.put(MkuClientSpecialAttrEnum.FULFIL_PROMISE.getKey(), String.valueOf(Constant.ONE));
                return;
            }
            if (Objects.isNull(skuPO)) {
                return;
            }

            // 本土非备货品+发货时效=1  72小时达
            if (fulfilPromiseCountry.contains(skuPO.getSourceCountryCode()) ){
                if (Objects.nonNull(skuFeaturePO) && Objects.nonNull(skuFeaturePO.getProductionCycle())
                        && skuFeaturePO.getProductionCycle().compareTo(BigDecimal.ONE) == 0){
                    //  跳过72小时达标的逻辑
                    String fulfillPromise27CountrySwitch = ConfigUtils.getStringByKey(fulfilPromise72SwitchByCountry, skuPO.getSourceCountryCode());
                    // 国家履约时效72小时开关为空或者false时，不支持72小时标
                    if (StringUtils.isBlank(fulfillPromise27CountrySwitch) || !Boolean.valueOf(fulfillPromise27CountrySwitch)) {
                        return;
                    }

                    // 非备货品 72小时达
                    specialMap.put(MkuClientSpecialAttrEnum.FULFIL_PROMISE.getKey(), String.valueOf(2));
                }else if (Objects.nonNull(skuFeaturePO) && Objects.nonNull(skuFeaturePO.getProductionCycle())){
                    // 京准达
                    specialMap.put(MkuClientSpecialAttrEnum.FULFIL_PROMISE.getKey(), String.valueOf(3));
                }
            } else if (CountryConstant.COUNTRY_ZH.equals(skuPO.getSourceCountryCode())) {
                SkuDetailedPageInfoResp jdProductDTO = jdProductDTOMap.get(skuPO.getJdSkuId());
                if (Objects.nonNull(jdProductDTO) && Objects.nonNull(jdProductDTO.getDeliveryDays())) {
                    // 京准达
                    specialMap.put(MkuClientSpecialAttrEnum.FULFIL_PROMISE.getKey(), String.valueOf(3));
                }
            }
        }
    }

    /**
     * 根据特殊属性集合、SKU PO映射和客户代码，获取JD SKU的履约时效信息。
     * @param specialAttrEnums 特殊属性枚举集合，用于判断是否需要查询履约时效信息。
     * @param skuPOMap SKU PO映射，用于获取JD SKU的ID。
     * @param clientCode 客户代码，用于查询履约时效信息。
     * @return 包含JD SKU ID和对应的履约时效信息的映射。
     */
    private Map<Long, SkuDetailedPageInfoResp> getJdSkuPromiseMap(Set<MkuClientSpecialAttrEnum> specialAttrEnums, Map<Long, SkuPO> skuPOMap, String  clientCode) {
        Map<Long, SkuDetailedPageInfoResp> jdSkuPromiseMap = Maps.newHashMap();
        if (specialAttrEnums.contains(MkuClientSpecialAttrEnum.FULFIL_PROMISE)) {
            // 查询JD商品信息
            Set<Long> jdSkuIds = Sets.newHashSet();
            skuPOMap.forEach((skuId, skuPo) -> {
                if (Objects.nonNull(skuPo.getJdSkuId())) {
                    jdSkuIds.add(skuPo.getJdSkuId());
                }
            });
            if (CollectionUtils.isEmpty(jdSkuIds)){
                return jdSkuPromiseMap;
            }

            // 多线程查询国内商品履约时效
            List<Future<Map<Long,SkuDetailedPageInfoResp>>> futureList = Lists.newArrayList();
            for (Long jdSkuId : jdSkuIds) {
                futureList.add(CompletableFuture.supplyAsync(() -> this.queryJdSkuPromise(jdSkuId, clientCode), promiseQueryExecutor));
            }

            for (Future<Map<Long, SkuDetailedPageInfoResp>> future : futureList) {
                try {
                    jdSkuPromiseMap.putAll(future.get(5, TimeUnit.SECONDS));
                } catch (Exception e) {
                    log.error("MkuManageServiceImpl.getJdSkuPromiseMap 查询mkuPromise取回结果失败", e);
                    if (null != future) {
                        future.cancel(Boolean.TRUE);
                    }
                }
            }
        }
        return jdSkuPromiseMap;
    }


    private Map<Long,SkuDetailedPageInfoResp> queryJdSkuPromise(Long jdSkuId,String clientCode){
        Map<Long, SkuDetailedPageInfoResp> jdSkuPromiseMap = Maps.newHashMapWithExpectedSize( Constant.ONE);
        SkuDetailedPageInfoResp infoResp = promiseManageService.getPromiseInfoByJdSkuId(new JdSkuDetailPromiseReqVO(jdSkuId, Constant.ONE, clientCode));
        jdSkuPromiseMap.put(jdSkuId, infoResp);
        return jdSkuPromiseMap;
    }

    @Override
    public Map<Long, Long> getIscMkuIdBySkuId(BatchQueryMkuReqDTO req) {
        // 查询SKU关联的MKU
        Map<Long, Long> skuMkuMap = mkuRelationAtomicService.queryMkuIdBySkuIds(req.getSkuIds());
        Set<Long> skuIds = req.getSkuIds();
        skuIds.forEach(skuId-> {
            if (!skuMkuMap.containsKey(skuId)) {
                skuMkuMap.put(skuId, null);
            }
        });
        return skuMkuMap;
    }

    @Override
    public Map<Long, IscMkuLangsResDTO> getIscMkuLangs(BatchQueryMkuLangsReqDTO req) {
        CustomerVO customer = customerManageService.detail(req.getClientCode());
        if(customer==null){
            log.warn("getIscMkuLangs, 客户信息为空, clientCode:{}", req.getClientCode());
            return null;
        }

        //mku基本信息
        Map<Long, MkuPO> mkuBaseMap = mkuAtomicService.getByMkuIds(req.getMkuId());
        if(MapUtils.isEmpty(mkuBaseMap)){
            log.warn("getIscMkuLangs, mku信息为空, mkuId:{}", req.getMkuId());
            return null;
        }

        Set<String> langs = req.getLangs();
        if (CollectionUtils.isEmpty(langs)){
            langs = new HashSet<>();
            langs.add(CommonUtils.getLangFromUniformBizInfo(req.getUniformBizInfo()));
        }

        // 响应结果
        Map<Long, IscMkuLangsResDTO> result = new HashMap<>();

        // 构建基本信息
        buildMkuLangsBaseInfo(req, result,  mkuBaseMap, langs, customer);
        // 构建详描
        buildMkuDescLangs(req, result, langs);
        // 构建销售属性
        buildMkuSellAttributeLangs(req, result, mkuBaseMap, langs);
        // 构建扩展属性
        try {
            buildMkuExtAttributeLangs(req, result, mkuBaseMap, langs);
        } catch (Exception e) {
            //
            log.error("getIscMkuLangs, buildMkuExtAttributeLangs error, req={}", JSONObject.toJSONString(req), e);
        }

        log.info("getIscMkuLangs, finish, req={}", JSONObject.toJSONString(req));
        return result;
    }

    /** 根据是客户id&mkuId查询skuId*/
    @Override
    public Map<Long, Long> getIscSkuIdByMkuId(BatchQueryMkuReqDTO req) {
        //按客户MKU维度定位SKUId
        if(StringUtils.isNotBlank(req.getClientCode())){
            List<CustomerMkuPricePO> mkuPOList = customerMkuPriceAtomicService.getListByMkuIds(req.getMkuId(),req.getClientCode());
            return mkuPOList.stream().collect(Collectors.toMap(CustomerMkuPricePO::getMkuId, CustomerMkuPricePO::getFixedSkuId));
        //按MKU查询SKUID
        }else {
            List<MkuRelationPO> mkuRelationList = mkuRelationAtomicService.queryBindListByMkuIds(new ArrayList<>(req.getMkuId()));
            if(CollectionUtils.isNotEmpty(mkuRelationList)){
                return mkuRelationList.stream().collect(Collectors.toMap(
                        MkuRelationPO::getMkuId,
                        MkuRelationPO::getSkuId,
                        (existing, replacement) -> {
                            throw new RuntimeException(String.format("mku&sku暂不支持一对多关系:sku%s与sku%s挂载在同一mku下",existing,replacement));
                        }
                ));
            }
            return new HashMap<>();
        }
    }

    @Override
    public Map<Long, Long> getIscJdSkuIdByMkuId(BatchQueryMkuReqDTO req) {
        Map<Long, Long> result = new HashMap<>();
        Set<Long> mkuIds = req.getMkuId();
        List<CustomerMkuPricePO> fixedSkuList = customerMkuPriceAtomicService.getListByMkuIds(mkuIds, req.getClientCode());
        if(CollectionUtils.isEmpty(fixedSkuList)){
            log.warn("getIscJdSkuIdByMkuId, finish, fixedSkuList is null,req:{}", JSONObject.toJSONString(req));
            return result;
        }
        //mku&sku
        Map<Long, Long> mkuMap = fixedSkuList.stream().collect(Collectors.toMap(CustomerMkuPricePO::getMkuId, CustomerMkuPricePO::getFixedSkuId));
        //sku&skuPo
        Set<Long> skuIds = fixedSkuList.stream().map(CustomerMkuPricePO::getFixedSkuId).collect(Collectors.toSet());
        Map<Long, SkuPO> skuPOMap = skuAtomicService.batchQuerySkuPO(skuIds);
        if(MapUtils.isEmpty(skuPOMap)){
            log.warn("getIscJdSkuIdByMkuId, finish, skuPOMap is null,req:{}", JSONObject.toJSONString(req));
            return result;
        }

        mkuIds.forEach(mkuId->{
            Long skuId = mkuMap.get(mkuId);
            SkuPO skuPO = skuPOMap.get(skuId);
            if(skuPO != null){
                result.put(mkuId,skuPO.getJdSkuId());
            }
        });

        return result;
    }

    @Override
    public Map<Long, Boolean> getIscSpuSkuRelationByMkuId(BatchQueryMkuReqDTO req) {
        Map<Long, Boolean> result = new HashMap<>();
        Set<Long> mkuIds = req.getMkuId();
        List<CustomerMkuPricePO> fixedSkuList = customerMkuPriceAtomicService.getListByMkuIds(mkuIds, req.getClientCode());
        if(CollectionUtils.isEmpty(fixedSkuList)){
            log.warn("getIscSpuSkuRelationByMkuId, finish, fixedSkuList is null,req:{}", JSONObject.toJSONString(req));
            return result;
        }
        //mku&sku
        Map<Long, Long> mkuMap = fixedSkuList.stream().collect(Collectors.toMap(CustomerMkuPricePO::getMkuId, CustomerMkuPricePO::getFixedSkuId));
        //sku&skuPo
        Set<Long> skuIds = fixedSkuList.stream().map(CustomerMkuPricePO::getFixedSkuId).collect(Collectors.toSet());
        List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(skuIds);
        if(CollectionUtils.isEmpty(skuPOList)){
            log.warn("getIscSpuSkuRelationByMkuId, finish, skuPOList is null,req:{}", JSONObject.toJSONString(req));
            return result;
        }
        Map<Long,SkuPO> skuPOMap = skuPOList.stream().collect(Collectors.toMap(SkuPO::getSkuId, Function.identity()));

        Set<Long> spuIds = skuPOList.stream().map(SkuPO::getSpuId).collect(Collectors.toSet());
        Map<Long, List<SkuPO>> spuSkuMap = skuAtomicService.querySkuListBySpuIds(spuIds);
        if(MapUtils.isEmpty(spuSkuMap)){
            log.warn("getIscSpuSkuRelationByMkuId, finish, spuSkuMap is null,req:{}", JSONObject.toJSONString(req));
            return result;
        }

        mkuIds.forEach(mkuId->{
            Long skuId = mkuMap.get(mkuId);
            SkuPO skuPO = skuPOMap.get(skuId);
            if(skuPO==null){
                return;
            }
            List<SkuPO> skuPOS = spuSkuMap.get(skuPO.getSpuId());
            if(CollectionUtils.isEmpty(skuPOS)){
                return;
            }
            if(skuPOS.size() > 1){
                result.put(mkuId,Boolean.TRUE);
            } else {
                result.put(mkuId,Boolean.FALSE);
            }
        });

        return result;
    }

    /** 构建MKU base信息*/
    private void buildMkuLangsBaseInfo(BatchQueryMkuLangsReqDTO req
            ,Map<Long, IscMkuLangsResDTO> result, Map<Long, MkuPO> mkuBaseMap,Set<String> langs,CustomerVO customer) {

        if(!req.getQueryEnum().contains(MkuQueryLangsEnum.BASE)){
            log.info("buildMkuLangsBaseInfo, not contains MkuQueryLangsEnum.BASE, mkuId:{}", req.getMkuId());
            return;
        }

        //mku名称多语言信息
        Map<Long, Map<String, MkuLangPO>> mkuLangMap = mkuLangAtomicService.getMkuLangPoByMkuIds(new ArrayList<>(req.getMkuId()), langs);

        for(Long mkuId : req.getMkuId()){
            MkuPO mkuPo = mkuBaseMap.get(mkuId);
            if (null==mkuPo){
                log.warn("buildMkuLangsBaseInfo, mkuId:{} not exist", mkuId);
                continue;
            }

            //mku基本信息
            IscMkuLangsResDTO mkuLangsResDTO = new IscMkuLangsResDTO();
            mkuLangsResDTO.setMkuId(mkuId);
            mkuLangsResDTO.setMainImgUrl(mkuPo.getMainImg());
            mkuLangsResDTO.setDetailImgList(StringUtils.isNotBlank(mkuPo.getDetailImg()) ? Arrays.stream(mkuPo.getDetailImg().split(Constant.HASHTAG)).collect(Collectors.toList()) : null);

            // mku名称多语言
            Map<String, MkuLangPO> mkuLangPOMap = mkuLangMap.get(mkuId);
            if (MapUtils.isNotEmpty(mkuLangPOMap)){
                Map<String/*语种*/, String/*多语言名称*/> mkuNames = new HashMap<>();
                mkuLangPOMap.forEach((lang, mkuLangPO) -> {
                    mkuNames.put(lang, mkuLangPO.getMkuTitle());
                });
                mkuLangsResDTO.setMkuNames(mkuNames);
            }else {
                log.warn("buildMkuLangsBaseInfo, mkuLangPOMap empty. mkuId={}", mkuId);
            }

            result.put(mkuId,mkuLangsResDTO);
        }

        log.info("MkuManageServiceImpl.buildMkuLangsBaseInfo req:{} , res:{}" , JSON.toJSONString(req) , JSON.toJSONString(result));
    }

    /** 构建MKU 详描信息*/
    private void buildMkuDescLangs(BatchQueryMkuLangsReqDTO req,Map<Long, IscMkuLangsResDTO> result,Set<String> langs) {
        if(!req.getQueryEnum().contains(MkuQueryLangsEnum.DESCRIPTION)){
            log.info("buildMkuDescLangs, not contains MkuQueryLangsEnum.DESCRIPTION, mkuId:{}", req.getMkuId());
            return;
        }

        //mku名称多语言信息
        Map<Long, List<MkuDescLangPO>> mkuLangDescMap = mkuDescLangAtomicService.getMkuDescLangGroupPoByMkuIds(new ArrayList<>(req.getMkuId()), langs);

        for(Long mkuId : req.getMkuId()){
            IscMkuLangsResDTO mkuLangsResDTO = result.getOrDefault(mkuId, new IscMkuLangsResDTO());

            // mku详描多语言
            List<MkuDescLangPO> mkuDescLangPoList = mkuLangDescMap.get(mkuId);
            if (CollectionUtils.isNotEmpty(mkuDescLangPoList)){
                List<IscMkuDescLangDTO> descLangDTOList = MkuConvert.INSTANCE.listMkuDescLangPo2Dto(mkuDescLangPoList);
                mkuLangsResDTO.setDesc(descLangDTOList);
            }else {
                log.warn("buildMkuDescLangs, mkuDescLangPoList empty. mkuId={}", mkuId);
            }

            result.put(mkuId,mkuLangsResDTO);
        }

        log.info("MkuManageServiceImpl.buildMkuDescLangs req:{} , res:{}" , JSON.toJSONString(req) , JSON.toJSONString(result));
    }

    private void buildMkuSellAttributeLangs(BatchQueryMkuLangsReqDTO req, Map<Long, IscMkuLangsResDTO> result,
                                            Map<Long, MkuPO> mkuBaseMap, Set<String> langs) {
        if (!this.checkParam(req)) {
            return;
        }

        Set<Long> mkuIds = Optional.ofNullable(req.getMkuId()).orElse(Collections.emptySet());
        List<CustomerMkuPricePO> mkuPOList = customerMkuPriceAtomicService.getListByMkuIds(mkuIds, req.getClientCode());
        if (CollectionUtils.isEmpty(mkuPOList)) {
            log.warn("buildMkuSellAttributeLangs, mkuPOList is empty. mkuIds:{}", mkuIds);
            return;
        }

        Map<Long, Long> skuIdToMkuIdMap = this.buildSkuIdToMkuIdMap(mkuPOList);
        Set<Long> skuIds = this.buildSkuIds(mkuPOList);
        Map<Long, List<AttributeDTO>> mkuSellAttributeVOMap = this.buildMkuSellAttributeVOMap(skuIds, skuIdToMkuIdMap, langs);

        for (Long mkuId : mkuIds) {
            IscMkuLangsResDTO mkuLangsResDTO = result.getOrDefault(mkuId, new IscMkuLangsResDTO());
            MkuPO mkuPO = mkuBaseMap.get(mkuId);
            if (mkuPO == null) {
                log.warn("buildMkuSellAttributeLangs, mkuPO is null, mkuId={}", mkuId);
                continue;
            }
            List<AttributeDTO> attributeDTOS = mkuSellAttributeVOMap.get(mkuPO.getMkuId());
            if (CollectionUtils.isEmpty(attributeDTOS)) {
                log.warn("buildMkuSellAttributeLangs, attributeVOS is empty, mkuId={}", mkuId);
                continue;
            }
            mkuLangsResDTO.setSellAttributeList(attributeDTOS);
            result.put(mkuId, mkuLangsResDTO);
        }

        log.info("MkuManageServiceImpl.buildMkuSellAttributeLangs req:{}, res.size:{}",
                JSON.toJSONString(req), result.size());
    }

    private boolean checkParam(BatchQueryMkuLangsReqDTO req) {
        if (req == null || !req.getQueryEnum().contains(MkuQueryLangsEnum.SELL_ATTRIBUTE)) {
            log.info("buildMkuSellAttributeLangs, not contains SELL_ATTRIBUTE, mkuId:{}",
                    req != null ? req.getMkuId() : null);
            return false;
        }
        Set<Long> mkuIds = Optional.ofNullable(req.getMkuId()).orElse(Collections.emptySet());
        if (mkuIds.isEmpty()) {
            log.warn("buildMkuSellAttributeLangs, mkuIds is empty. req:{}", req);
            return false;
        }
        return true;
    }

    private Map<Long, Long> buildSkuIdToMkuIdMap(List<CustomerMkuPricePO> mkuPOList) {
        return mkuPOList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        CustomerMkuPricePO::getFixedSkuId,
                        CustomerMkuPricePO::getMkuId,
                        (v1, v2) -> v1));
    }

    private Set<Long> buildSkuIds(List<CustomerMkuPricePO> mkuPOList) {
        return mkuPOList.stream()
                .map(CustomerMkuPricePO::getFixedSkuId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    private Map<Long, List<AttributeDTO>> buildMkuSellAttributeVOMap(Set<Long> skuIds, Map<Long, Long> skuIdToMkuIdMap, Set<String> langs) {
        Map<Long, List<AttributeDTO>> mkuSellAttributeVOMap = new HashMap<>();
        for (Long skuId : skuIds) {
            List<PropertyVO> propertyVOS = saleAttributeManageService.getSkuSaleAttributeDetailWithLangList(skuId, new ArrayList<>(langs));
            if (CollectionUtils.isEmpty(propertyVOS)) {
                continue;
            }
            List<AttributeVO> attributeVOS = saleAttributeManageService.convertPropertyVo2AttributeVos(propertyVOS);

            List<AttributeDTO> attributeDTOS = saleAttributeManageService.convertAttributeVOsToDTOs(attributeVOS);

            Long mkuId = skuIdToMkuIdMap.get(skuId);
            if (mkuId != null && !CollectionUtils.isEmpty(attributeVOS)) {
                mkuSellAttributeVOMap.put(mkuId, attributeDTOS);
            }
        }
        return mkuSellAttributeVOMap;
    }



    /** 构建MKU 扩展属性信息*/
    private void buildMkuExtAttributeLangs(BatchQueryMkuLangsReqDTO req,Map<Long, IscMkuLangsResDTO> result
            , Map<Long, MkuPO> mkuBaseMap, Set<String> langs) {
        if(!req.getQueryEnum().contains(MkuQueryLangsEnum.EXTEND_ATTRIBUTE)){
            log.info("buildMkuExtAttributeLangs, not contains MkuQueryLangsEnum.EXTEND_ATTRIBUTE, mkuId:{}", req.getMkuId());
            return;
        }

        Map<Long/*类目id*/, Map<String/*属性id+comGroupId*/, AttributeVO>> allCategoryAttrMap = new HashMap<>();
        // 所有商品的类目属性信息
        Set<Long> catIds = mkuBaseMap.values().stream().map(MkuPO::getJdCatId).collect(Collectors.toSet());
        for(Long catId  : catIds){
            List<AttributeVO> categotyAttrList = attributeOutService.queryExtAttrDetail(catId, langs);
            if (CollectionUtils.isEmpty(categotyAttrList)){
                log.info("buildMkuExtAttributeLangs, categotyAttrList empty, catId={}", catId);
                continue;
            }
            // 重复的属性id会被覆盖
            // 改为使用AttributeVO id+comGroupId作为key
            Map<String, AttributeVO> attrVoOfCategoryMap = categotyAttrList.stream().collect(Collectors.toMap(
                attr -> getKey(attr.getId(), attr.getComGroupId()),
                Function.identity(),
                (existing, replacement) -> existing
            ));

            allCategoryAttrMap.put(catId, attrVoOfCategoryMap);
        }

        for(Long mkuId : req.getMkuId()){
            IscMkuLangsResDTO mkuLangsResDTO = result.getOrDefault(mkuId, new IscMkuLangsResDTO());
            MkuPO mkuPO = mkuBaseMap.get(mkuId);
            Map<String, AttributeVO> attrVoOfCategoryMap = allCategoryAttrMap.get(mkuPO.getJdCatId());

            List<AttributeDTO> attributeDTOS = getGroupExtendPropertyVOList(mkuPO.getMkuId(), mkuPO.getGroupExtAttribute(), attrVoOfCategoryMap, langs);
            if (CollectionUtils.isEmpty(attributeDTOS)){
                log.info("buildMkuExtAttributeLangs, attributeDTOS empty, mkuId={}", mkuId);
                continue;
            }

            mkuLangsResDTO.setExtentAttributeList(attributeDTOS);

            result.put(mkuId,mkuLangsResDTO);
        }

        log.info("MkuManageServiceImpl.buildMkuExtAttributeLangs req:{} , res:{}" , JSON.toJSONString(req) , JSON.toJSONString(result));
    }

    @NotNull
    private static String getKey(Long id, Integer comGroupId) {
        return id + Constant.UNDER_LINE + comGroupId;
    }


    /**
     * 取扩展属性组 与 商品所属类目挂载的属性 的交集
     * @param productAttribute
     * @param attrVoOfCategoryMap
     * @param langs
     * @return
     */
    private List<AttributeDTO> getGroupExtendPropertyVOList(Long mkuId, String productAttribute, Map<String, AttributeVO> attrVoOfCategoryMap, Set<String> langs){
        if (null!=commonDUCCConfig && Objects.equals(Boolean.TRUE,commonDUCCConfig.getLogDebugSwitch()) ){
            log.info("getPropertyVOList, parma productAttribute={}, attrVoOfCategoryMap={}, langs={}, mkuId={}"
                    , productAttribute, JSONObject.toJSONString(attrVoOfCategoryMap), JSONObject.toJSONString(langs), mkuId);
        }

        List<AttributeDTO> propertyVOListRes = Lists.newArrayList();
        if(StringUtils.isBlank(productAttribute) || MapUtils.isEmpty(attrVoOfCategoryMap)){
            log.info("getPropertyVOList, param empty. mkuId={}", mkuId);
            return propertyVOListRes;
        }

        // 将扩展属性组转换为PropertyVO列表
        List<PropertyVO> extendPropertyGroupVOS = ExtendPropertyGroupVO.convertToPropertyVOList(productAttribute);

        // 使用已解析的PropertyVO列表代替字符串解析
        for (PropertyVO propertyVO : extendPropertyGroupVOS) {
            Long attrId = propertyVO.getAttributeId();
            Integer comGroupId = propertyVO.getComGroupId();
            log.info("getPropertyVOList, propertyVO={}, mkuId={}", JSONObject.toJSONString(propertyVO), mkuId);

            // 属性解析结果对象
            AttributeDTO attrVoRes = new AttributeDTO();
            // 类目下挂载的属性
            AttributeVO attributeVoBindCategory = attrVoOfCategoryMap.get(getKey(attrId, comGroupId));
            log.info("getPropertyVOList, attributeVoBindCategory={}, attrId={}, mkuId={}"
                    , JSONObject.toJSONString(attributeVoBindCategory), attrId, mkuId);
            if(attributeVoBindCategory == null){
                continue;
            }

            attrVoRes.setId(attrId);
            attrVoRes.setAttributeType(attributeVoBindCategory.getAttributeType());
            attrVoRes.setAttributeInputType(attributeVoBindCategory.getAttributeInputType());
            attrVoRes.setSort(attributeVoBindCategory.getSort());
            attrVoRes.setComGroupId(attributeVoBindCategory.getComGroupId());
            attrVoRes.setLevel(attributeVoBindCategory.getLevel());
            attrVoRes.setLangComGroupNameMap(attributeVoBindCategory.getLangComGroupNameMap());
            List<AttributeLangDTO> attributeLangVOList = AttributeConvert.INSTANCE.listAttributeLangVo2DTO(attributeVoBindCategory.getLangList());
            attrVoRes.setLangList(attributeLangVOList);

            List<AttributeValueDTO> propertyValueVOList = new ArrayList<>();

            // 当前属性是否为扩展文本属性
            boolean extendTextAttr = CategoryAttrTypeEnum.EXTEND.getCode().equals(attributeVoBindCategory.getAttributeType())
                    && attrVoRes.getAttributeInputType().intValue() == CategoryAttrInputTypeEnum.TEXT.getCode().intValue();
            log.info("getPropertyVOList, extendTextAttr={}, attrId={}, mkuId={}"
                    , extendTextAttr, attrId, mkuId);
            
            // 扩展文本属性使用
            List<AttributeValueLangDTO> extendTextAttrValueLangList = null;
            if(extendTextAttr){
                extendTextAttrValueLangList = new ArrayList<>();
            }

            Map<Long, AttributeValueVO> attributeValueVoBindCategoryMap = attributeVoBindCategory.getAttributeValueList().stream().collect(Collectors.toMap(AttributeValueVO::getId, vo -> vo, (o1, o2) -> o1));
            
            // 处理属性值列表
            if (propertyVO.getPropertyValueVOList() != null && !propertyVO.getPropertyValueVOList().isEmpty()) {
                for (PropertyValueVO propertyValueVO : propertyVO.getPropertyValueVOList()) {
                    if (extendTextAttr) {
                        // 扩展属性，且输入类型为文本，则直接取文本值
                        String lang = propertyValueVO.getLang();
                        String attributeValueName = propertyValueVO.getAttributeValueName();
                        
                        if (langs.contains(lang)) {
                            AttributeValueLangDTO attributeValueLangDTO = new AttributeValueLangDTO();
                            attributeValueLangDTO.setLang(lang);
                            attributeValueLangDTO.setLangName(attributeValueName);
                            extendTextAttrValueLangList.add(attributeValueLangDTO);
                        }
                    } else {
                        // 非扩展文本属性，根据attributeValueId查找对应的AttributeValueVO
                        Long valueId = propertyValueVO.getAttributeValueId();
                        if (valueId != null) {
                            AttributeValueVO attributeValueVoBindCategory = attributeValueVoBindCategoryMap.get(valueId);
                            if (attributeValueVoBindCategory != null) {
                                AttributeValueDTO attributeValueDTO = AttributeConvert.INSTANCE.attributeValueVo2DTO(attributeValueVoBindCategory);
                                propertyValueVOList.add(attributeValueDTO);
                            }
                        }
                    }
                }
            }

            if(extendTextAttr && extendTextAttrValueLangList != null && !extendTextAttrValueLangList.isEmpty()){
                AttributeValueDTO attributeValueDTO = new AttributeValueDTO();
                attributeValueDTO.setLangList(extendTextAttrValueLangList);
                propertyValueVOList.add(attributeValueDTO);
            }

            attrVoRes.setAttributeValueList(propertyValueVOList);
            propertyVOListRes.add(attrVoRes);
        }

        return propertyVOListRes;
    }
}
