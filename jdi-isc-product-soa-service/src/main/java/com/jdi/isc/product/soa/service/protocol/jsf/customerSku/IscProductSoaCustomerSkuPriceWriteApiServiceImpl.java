package com.jdi.isc.product.soa.service.protocol.jsf.customerSku;

import com.google.common.collect.Lists;
import com.jd.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.enums.CustomerSkuDataSourceTypeEnums;
import com.jdi.isc.product.soa.api.customerSku.IscProductSoaCustomerSkuPriceWriteApiService;
import com.jdi.isc.product.soa.api.customerSku.req.CustomerSkuAuditApiDTO;
import com.jdi.isc.product.soa.api.customerSku.req.CustomerSkuPriceDraftIdApiDTO;
import com.jdi.isc.product.soa.api.customerSku.res.CustomerSkuPriceDraftApiDTO;
import com.jdi.isc.product.soa.api.price.req.PriceAvailableSaleStatusReqDTO;
import com.jdi.isc.product.soa.api.price.res.PriceAvailableSaleStatusResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuAuditVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceDraftVO;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceDraftManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceWarningManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuSalePriceDetailManageService;
import com.jdi.isc.product.soa.service.mapstruct.customerSku.CustomerSkuPriceConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * sku定制化价格
 * <AUTHOR>
 * @date 2024/11/01
 */
@Service
@Slf4j
public class IscProductSoaCustomerSkuPriceWriteApiServiceImpl implements IscProductSoaCustomerSkuPriceWriteApiService, InitializingBean {

    @Resource
    private CustomerSkuPriceDraftManageService customerSkuPriceDraftManageService;
    @Resource
    private CustomerSkuPriceWarningManageService customerSkuPriceWarningManageService;

    @Resource
    private CustomerSkuSalePriceDetailManageService customerSkuSalePriceDetailManageService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> saveOrUpdate(@Validated CustomerSkuPriceDraftApiDTO input){
        log.info("IscProductSoaCustomerSkuPriceWriteApiServiceImpl.saveOrUpdate param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CustomerSkuPriceDraftVO reqVO = CustomerSkuPriceConvert.INSTANCE.dto2Vo(input);
        return DataResponse.success(customerSkuPriceDraftManageService.saveOrUpdate(reqVO));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchApprove(CustomerSkuAuditApiDTO input){
        log.info("IscProductSoaCustomerSkuPriceWriteApiServiceImpl.batchApprove param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CustomerSkuAuditVO reqVO = CustomerSkuPriceConvert.INSTANCE.auditDto2Vo(input);
        String result = customerSkuPriceDraftManageService.batchApprove(reqVO);
        return StringUtils.equals(Constant.SUCCESS,result) ? DataResponse.success() : DataResponse.error(result);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchReject(CustomerSkuAuditApiDTO input){
        log.info("IscProductSoaCustomerSkuPriceWriteApiServiceImpl.batchReject param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CustomerSkuAuditVO reqVO = CustomerSkuPriceConvert.INSTANCE.auditDto2Vo(input);
        String result = customerSkuPriceDraftManageService.batchReject(reqVO);
        return StringUtils.equals(Constant.SUCCESS,result) ? DataResponse.success() : DataResponse.error(result);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> updateEnableStatusById(CustomerSkuPriceDraftIdApiDTO input){
        log.info("IscProductSoaCustomerSkuPriceWriteApiServiceImpl.updateEnableStatusById param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        return DataResponse.success(customerSkuPriceDraftManageService.updateEnableStatusById(input.getDetailDraftId()));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> deleteById(Long id) {
        log.info("IscProductSoaCustomerSkuPriceWriteApiServiceImpl.deleteById param:{}", id);
        return DataResponse.success(customerSkuPriceDraftManageService.deleteById(id));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchRevoke(CustomerSkuAuditApiDTO input) {
        log.info("IscProductSoaCustomerSkuPriceWriteApiServiceImpl.batchRevoke param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CustomerSkuAuditVO reqVO = CustomerSkuPriceConvert.INSTANCE.auditDto2Vo(input);
        return DataResponse.success(customerSkuPriceDraftManageService.batchRevoke(reqVO));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchEnableApprove(CustomerSkuAuditApiDTO input){
        log.info("IscProductSoaCustomerSkuPriceWriteApiServiceImpl.batchEnableApprove param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CustomerSkuAuditVO reqVO = CustomerSkuPriceConvert.INSTANCE.auditDto2Vo(input);
        String result = customerSkuPriceDraftManageService.batchEnableApprove(reqVO);
        return StringUtils.equals(Constant.SUCCESS,result) ? DataResponse.success() : DataResponse.error(result);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchEnableReject(CustomerSkuAuditApiDTO input){
        log.info("IscProductSoaCustomerSkuPriceWriteApiServiceImpl.batchEnableReject param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CustomerSkuAuditVO reqVO = CustomerSkuPriceConvert.INSTANCE.auditDto2Vo(input);
        String result = customerSkuPriceDraftManageService.batchEnableReject(reqVO);
        return StringUtils.equals(Constant.SUCCESS,result) ? DataResponse.success() : DataResponse.error(result);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchEnableRevoke(CustomerSkuAuditApiDTO input){
        log.info("IscProductSoaCustomerSkuPriceWriteApiServiceImpl.batchEnableRevoke param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CustomerSkuAuditVO reqVO = CustomerSkuPriceConvert.INSTANCE.auditDto2Vo(input);
        return DataResponse.success(customerSkuPriceDraftManageService.batchEnableRevoke(reqVO));
    }

    @Override
    public DataResponse<String> initCustomerSkuWarningPrice(Set<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return DataResponse.error("参数错误");
        }
        for(Long id : ids){
            try{
                customerSkuPriceWarningManageService.saveOrUpdateFromCustomerSku(id, CustomerSkuDataSourceTypeEnums.CUSTOMER);
            }catch(Exception e){
                log.error("DevOpsApiTwoServiceImpl.initCustomerSkuWarningPrice error,id={}",id,e);
            }
        }
        return DataResponse.success();
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PriceAvailableSaleStatusResDTO> updateAvailableSaleStatus(List<PriceAvailableSaleStatusReqDTO> input) {
        return customerSkuSalePriceDetailManageService.updateAvailableSaleStatus(input);
    }

    @Override
    public void afterPropertiesSet() {
//        String json = "{\"clientCode\":\"uDFnyS3iUOBc3D1EP1XP\",\"customerSalePrice\":120,\"id\":19200,\"lang\":\"zh\",\"pin\":\"liuzhaoming.10\",\"remark\":\"q\",\"skuId\":80000000139,\"systemCode\":\"WIMP\",\"taxRateList\":[{\"clientCode\":\"uDFnyS3iUOBc3D1EP1XP\",\"clientType\":1,\"countryCode\":\"BR\",\"keyId\":\"80000000139\",\"keyType\":1,\"taxCode\":\"ICMS\",\"taxRate\":1},{\"clientCode\":\"uDFnyS3iUOBc3D1EP1XP\",\"clientType\":1,\"countryCode\":\"BR\",\"keyId\":\"80000000139\",\"keyType\":1,\"taxCode\":\"IPI\",\"taxRate\":1},{\"clientCode\":\"uDFnyS3iUOBc3D1EP1XP\",\"clientType\":1,\"countryCode\":\"BR\",\"keyId\":\"80000000139\",\"keyType\":1,\"taxCode\":\"PIS\",\"taxRate\":1},{\"clientCode\":\"uDFnyS3iUOBc3D1EP1XP\",\"clientType\":1,\"countryCode\":\"BR\",\"keyId\":\"80000000139\",\"keyType\":1,\"taxCode\":\"COFINS\",\"taxRate\":1},{\"clientCode\":\"uDFnyS3iUOBc3D1EP1XP\",\"clientType\":1,\"countryCode\":\"BR\",\"keyId\":\"80000000139\",\"keyType\":1,\"taxCode\":\"UTILIZATION\",\"taxRate\":2}]}";
//        CustomerSkuPriceDraftApiDTO update = JSONObject.parseObject(json, CustomerSkuPriceDraftApiDTO.class);
//                String url1 = "https://wiop-api.jdindustry.com/img/ks1/image%20%287%2934909803174299380.png?x-oss-process=img/sb/800/800/fmt/png";
//        String url3 = "https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/ks1/image%20%287%2934909803174299380.png";
//
//        update.setAdjustmentPriceReason("测试调整原因");
//        update.setAttachmentUrls(Lists.newArrayList(url1, url3));
//        DataResponse<String> response = this.saveOrUpdate(update);
//
//        log.info("saveOrUpdate response={}", response);
    }
}
