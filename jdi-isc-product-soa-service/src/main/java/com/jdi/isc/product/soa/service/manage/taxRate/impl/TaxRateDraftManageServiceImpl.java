package com.jdi.isc.product.soa.service.manage.taxRate.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.enums.CustomerSkuBrTaxEnum;
import com.jdi.isc.product.soa.api.taxRate.req.BrImportTaxReqDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRatePageVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRateVO;
import com.jdi.isc.product.soa.domain.taxRate.po.TaxRateDraftPO;
import com.jdi.isc.product.soa.domain.taxRate.po.TaxRateLogPO;
import com.jdi.isc.product.soa.service.atomic.taxRate.TaxRateDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.TaxRateLogAtomicService;
import com.jdi.isc.product.soa.service.manage.taxRate.TaxRateManageService;
import com.jdi.isc.product.soa.service.mapstruct.taxRate.TaxRateDraftConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TaxRateDraftManageServiceImpl extends BaseManageSupportService<TaxRateVO, TaxRateDraftPO> implements TaxRateManageService {


    @Resource
    private TaxRateDraftAtomicService taxRateDraftAtomicService;

    @Resource
    private TaxRateLogAtomicService taxRateLogAtomicService;

    @Override
    public DataResponse<TaxRateVO> saveOrUpdate(TaxRateVO vo) {
        TaxRateDraftPO taxRateDraftPO = TaxRateDraftConvert.INSTANCE.vo2Po(vo);
        boolean result = taxRateDraftAtomicService.saveOrUpdate(taxRateDraftPO);
        return DataResponse.success(vo);
    }

    @Override
    public DataResponse<Boolean> saveOrUpdateBatch(List<TaxRateVO> voList) {
        List<TaxRateDraftPO> taxRatePOList = TaxRateDraftConvert.INSTANCE.listVo2Po(voList);
        List<TaxRateDraftPO> dbTaxRatePOList = taxRateDraftAtomicService.queryListPoByList(taxRatePOList);
        Map<String, TaxRateDraftPO> taxRateMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(dbTaxRatePOList)){
            taxRateMap = dbTaxRatePOList.stream().collect(Collectors.toMap(taxRatePO -> {
                return taxRatePO.getKeyId() + taxRatePO.getTaxCode();
            }, Function.identity()));
        }
        for (TaxRateDraftPO taxRatePO : taxRatePOList){
            if(taxRateMap.containsKey(taxRatePO.getKeyId() + taxRatePO.getTaxCode())){
                TaxRateDraftPO tempTaxRatePO = taxRateMap.get(taxRatePO.getKeyId() + taxRatePO.getTaxCode());
                taxRatePO.setCreator(tempTaxRatePO.getUpdater());
                taxRatePO.setUpdateTime(new Date().getTime());
                taxRatePO.setCountryCode(null);
                taxRatePO.setClientCode(null);
                taxRatePO.setKeyType(null);
                taxRatePO.setKeyId(null);
                taxRatePO.setClientType(null);
                taxRatePO.setId(tempTaxRatePO.getId());
            }else {
                taxRatePO.setId(null);
                taxRatePO.setCreateTime(new Date().getTime());
                taxRatePO.setUpdateTime(new Date().getTime());
            }
        }
        boolean result = taxRateDraftAtomicService.saveOrUpdateBatch(taxRatePOList);
        return DataResponse.success(result);
    }

    @Override
    public TaxRateVO getDetailById(TaxRateVO vo) {
        if(Objects.isNull(vo.getId())){
            return null;
        }
        TaxRateDraftPO taxRatePO = taxRateDraftAtomicService.getById(vo.getId());
        TaxRateVO taxRateVO = TaxRateDraftConvert.INSTANCE.po2Vo(taxRatePO);
        return taxRateVO;
    }

    @Override
    public PageInfo<TaxRateVO> pageTaxRate(TaxRatePageVO vo) {
        log.info("TaxRateManageServiceImpl.pageTaxRate param:{}", JSON.toJSONString(vo));
        Page<TaxRateDraftPO> taxRatePOPage = taxRateDraftAtomicService.pageTaxRatePO(vo);
        List<TaxRateVO> taxRateVOList = TaxRateDraftConvert.INSTANCE.listPo2Vo(taxRatePOPage.getRecords());
        log.info("TaxRateManageServiceImpl.pageTaxRate param:{},res:{}", JSON.toJSONString(vo), JSON.toJSONString(taxRateVOList));
        return super.pageTransform(taxRatePOPage,taxRateVOList);
    }

    /**
     * 根据给定的TaxRateVO对象查询税率信息。
     * @param vo TaxRateVO对象，包含查询条件。
     * @return List<TaxRateVO> 符合条件的税率信息列表。
     */
    @Override
    public List<TaxRateVO> listTaxRate(TaxRateVO vo){
        log.info("TaxRateManageServiceImpl.listTaxRate param:{}", JSON.toJSONString(vo));
        List<TaxRateDraftPO> taxRatePOList = new ArrayList<>();
        TaxRateDraftPO taxRateDraftPO = TaxRateDraftConvert.INSTANCE.vo2Po(vo);
        taxRatePOList.add(taxRateDraftPO);
        List<TaxRateDraftPO> resultPOList = taxRateDraftAtomicService.queryListPoByList(taxRatePOList);
        List<TaxRateVO> taxRateVOList = TaxRateDraftConvert.INSTANCE.listPo2Vo(resultPOList);
        log.info("TaxRateManageServiceImpl.listTaxRate param:{},res:{}", JSON.toJSONString(vo), JSON.toJSONString(taxRateVOList));
        return taxRateVOList;
    }

    @Override
    public Boolean delete(TaxRateVO vo) {
        TaxRateDraftPO taxRateDraftPO = taxRateDraftAtomicService.getById(vo.getId());
        TaxRateLogPO taxRateLogPO = TaxRateDraftConvert.INSTANCE.po2po(taxRateDraftPO);
        taxRateLogPO.setCreator(vo.getCreator());
        taxRateLogPO.setUpdater(vo.getCreator());
        taxRateLogPO.setCreateTime(new Date().getTime());
        taxRateLogPO.setUpdateTime(new Date().getTime());
        taxRateLogAtomicService.save(taxRateLogPO);
        boolean result = taxRateDraftAtomicService.removeById(vo.getId());
        return result;
    }

    @Override
    public Boolean exists(TaxRateVO vo) {
        return null;
    }

    @Override
    public List<TaxRateVO> listByCountryCodeAndSkuId(TaxRateVO vo) {
        return null;
    }

    @Override
    public Boolean updateBrIpiTax(BrImportTaxReqDTO input) {
        if(input.getIpiRate() == null || input.getSkuId() == null || input.getPin() == null){
            log.info("TaxRateDraftManageServiceImpl.updateBrIpiTax ipiRate/skuId/pin is null");
            return Boolean.FALSE;
        }

        LambdaQueryWrapper<TaxRateDraftPO> wrapper = Wrappers.<TaxRateDraftPO>lambdaQuery()
                .eq(TaxRateDraftPO::getCountryCode, CountryConstant.COUNTRY_BR)
                .eq(TaxRateDraftPO::getKeyType, KeyTypeEnum.SKU.getCode())
                .eq(TaxRateDraftPO::getKeyId, input.getSkuId())
                .eq(TaxRateDraftPO::getTaxCode, CustomerSkuBrTaxEnum.IPI.name())
                .eq(TaxRateDraftPO::getYn, YnEnum.YES.getCode());
        List<TaxRateDraftPO> taxRatePOS = taxRateDraftAtomicService.getBaseMapper().selectList(wrapper);
        if(CollectionUtils.isEmpty(taxRatePOS)){
            log.info("TaxRateDraftManageServiceImpl.updateBrIpiTax taxRatePOS is null");
            return Boolean.FALSE;
        }
        Long current = new Date().getTime();
        List<TaxRateDraftPO> updatePOList = new ArrayList<>();
        for (TaxRateDraftPO taxRatePO : taxRatePOS) {
            TaxRateDraftPO updatePO = new TaxRateDraftPO();
            updatePO.setId(taxRatePO.getId());
            updatePO.setTaxRate(input.getIpiRate().multiply(Constant.DECIMAL_HUNDRED).doubleValue());
            updatePO.setUpdater(input.getPin());
            updatePO.setUpdateTime(current);
            updatePOList.add(updatePO);
        }

        return taxRateDraftAtomicService.updateBatchById(updatePOList);
    }
}
