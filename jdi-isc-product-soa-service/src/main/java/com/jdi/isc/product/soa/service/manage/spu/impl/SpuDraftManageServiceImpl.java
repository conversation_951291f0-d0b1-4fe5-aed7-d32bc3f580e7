package com.jdi.isc.product.soa.service.manage.spu.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SkuSaleAttributeValueRelationPO;
import com.jdi.isc.product.soa.domain.sku.biz.GroupSkuCertificateVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuStockRelationVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.domain.supplier.po.SupplierBaseInfoPO;
import com.jdi.isc.product.soa.domain.taxRate.po.CategoryTaxPO;
import com.jdi.isc.product.soa.service.atomic.attribute.AttributeLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.attribute.AttributeValueLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SkuSaleAttributeValueRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.SupplierBaseInfoAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.CategoryTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.brand.BrandOutService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.ExtendInfoService;
import com.jdi.isc.product.soa.service.manage.spu.SpuDraftManageService;
import com.jdi.isc.product.soa.service.manage.stock.StockManageService;
import com.jdi.isc.product.soa.service.mapstruct.ProductAttributeConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SkuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuConvert;
import com.jdi.isc.product.soa.service.support.ProductIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * spu草稿服务
 *
 * <AUTHOR>
 * @date 2023/11/26
 **/
@Slf4j
@Service
public class SpuDraftManageServiceImpl extends BaseManageSupportService<SpuDraftVO, SpuDraftPO> implements SpuDraftManageService {

    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;

    @Resource
    private SkuDraftAtomicService skuDraftAtomicService;

    @Resource
    private SpuConvertService spuConvertService;

    @Resource
    private SupplierBaseInfoAtomicService supplierBaseInfoAtomicService;

    @Resource
    private CategoryOutService categoryOutService;

    @Resource
    private ExtendInfoService extendInfoService;

    @Resource
    private SkuReadManageService skuReadManageService;
    @Resource
    private SkuConvertService skuConvertService;
    @Resource
    private BrandOutService brandOutService;

    /**
     * 提供SPU与MKU、SKU的关联服务，用于查询和管理SPU下对应的MKU和SKU信息。
     */
    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    /**
     * 异步任务执行器，用于并发处理商品搜索草稿的相关操作。
     */
    @Resource
    private AsyncTaskExecutor searchSpuExecutor;

    /**
     * 提供SKU原子操作服务，包括查询和管理SKU信息等功能。
     */
    @Resource
    private SkuAtomicService skuAtomicService;

    /**
     * 提供类目税信息的原子服务，用于批量查询类目税信息。
     */
    @Resource
    private CategoryTaxAtomicService categoryTaxAtomicService;

    /**
     * 提供属性语言服务，用于根据属性ID和语言获取对应的属性名称。
     */
    @Resource
    private AttributeLangAtomicService attributeLangAtomicService;

    /**
     * 提供属性语言服务，用于根据属性ID和语言获取对应的属性名称。
     */
    @Resource
    private AttributeValueLangAtomicService attributeValueLangAtomicService;

    @Resource
    private SkuPriceAtomicService skuPriceAtomicService;

    @Resource
    private ProductAttributeConvertService productAttributeConvertService;

    @Resource
    private ProductIdGenerator productIdGenerator;

    @Resource
    private SaleAttributeManageService saleAttributeManageService;

    @Resource
    private SkuSaleAttributeValueRelationAtomicService skuSaleAttributeValueRelationAtomicService;

    @Override
    public SaveSpuVO getSaveSpuVoFromDraftBySpuId(Long spuId) {
        LambdaQueryWrapper<SpuDraftPO> spuDraftQuery = new LambdaQueryWrapper<SpuDraftPO>()
                .eq(SpuDraftPO::getSpuId, spuId)
                .eq(SpuDraftPO::getYn, YnEnum.YES.getCode());
        SpuDraftPO draftPo = spuDraftAtomicService.getOne(spuDraftQuery);

        if (Objects.isNull(draftPo)) {
            return null;
        }
        String spuJsonInfo = draftPo.getSpuJsonInfo();
        SaveSpuVO saveVo = JSON.parseObject(spuJsonInfo, SaveSpuVO.class);
        saveVo.getSpuVO().setAuditStatus(draftPo.getAuditStatus());
        saveVo.getSpuVO().setCatId(draftPo.getJdCatId());
//        saveVo.getSpuVO().setTaxAuditStatus(draftPo.getTaxAuditStatus());
        this.setListRef(saveVo,draftPo);

        List<SkuDraftPO> skuDrafts = skuDraftAtomicService.getBySpuId(spuId);
        List<SkuVO> skuVOList = this.buildSkuVOS(skuDrafts,null, draftPo);
        if(CollectionUtils.isNotEmpty(skuVOList)){
            saveVo.setSkuVOList(skuVOList);
        }

        return saveVo;
    }

    /**
     * 根据SPU ID集合从草稿列表中获取保存的SPU VO对象列表
     *
     * @param spuIds SPU ID集合
     * @return 保存的SPU VO对象列表
     */
    @Override
    @PFTracing
    public List<SaveSpuVO> getSaveSpuVoFromDraftListBySpuIds(Set<Long> spuIds) {
        LambdaQueryWrapper<SpuDraftPO> spuDraftQuery = new LambdaQueryWrapper<SpuDraftPO>()
            .in(SpuDraftPO::getSpuId, spuIds).eq(SpuDraftPO::getYn, YnEnum.YES.getCode());
        List<SpuDraftPO> draftPoList = spuDraftAtomicService.list(spuDraftQuery);

        if (CollectionUtils.isEmpty(draftPoList)) {
            return new ArrayList<SaveSpuVO>();
        }
        List<SaveSpuVO> saveSpuVOList = new ArrayList<>();
        for (SpuDraftPO draftPo:draftPoList){
            String spuJsonInfo = draftPo.getSpuJsonInfo();
            SaveSpuVO saveVo = JSON.parseObject(spuJsonInfo, SaveSpuVO.class);
            saveVo.getSpuVO().setAuditStatus(draftPo.getAuditStatus());
//            saveVo.getSpuVO().setTaxAuditStatus(draftPo.getTaxAuditStatus());
            saveVo.getSpuVO().setCatId(draftPo.getJdCatId());
            this.setListRef(saveVo,draftPo);

            saveVo.getSpuVO().setSpuLangExtendVOList(spuConvertService.convertSpuLangExtendList(saveVo.getSpuVO()));
            List<SkuDraftPO> skuDrafts = skuDraftAtomicService.getBySpuId(saveVo.getSpuVO().getSpuId());
            List<SkuVO> skuVOList = this.buildSkuVOS(skuDrafts,null,draftPo);
            if(CollectionUtils.isNotEmpty(skuVOList)){
                saveVo.setSkuVOList(skuVOList);
            }
            saveSpuVOList.add(saveVo);
        }
        return saveSpuVOList;
    }

    /**
     * 保存或更新草稿列表
     * @param saveSpuVOList 保存SPU VO列表
     */
    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class)
    @PFTracing
    public void saveOrUpdateDraftList(List<SaveSpuVO> saveSpuVOList) {
        Set<Long> spuIdSet = saveSpuVOList.stream().map(saveSpuVO -> saveSpuVO.getSpuVO().getSpuId()).collect(Collectors.toSet());
        List<SpuDraftPO> spuDraftPOList = spuDraftAtomicService.batchQuerySpuDraftPoList(spuIdSet);
        Map<Long,List<SkuDraftPO>>  skuDraftPOMap = skuDraftAtomicService.queryMapBySpuIds(spuIdSet);
        Map<Long, SpuDraftPO> spuIdMap = spuDraftPOList.stream().collect(Collectors.toMap(SpuDraftPO::getSpuId, Function.identity(), (spu1, spu2) -> spu1));
        List<SpuDraftPO> updateSpuDraftList = new ArrayList<>();
        List<SkuDraftPO> insertSkuDraftList = new ArrayList<>();
        List<SkuDraftPO> deleteSkuDraftList = new ArrayList<>();
        for(SaveSpuVO saveSpuVO : saveSpuVOList){
            if(spuIdMap.containsKey(saveSpuVO.getSpuVO().getSpuId())){
                // spuDraft
                SpuDraftPO spuDraftPO = spuIdMap.get(saveSpuVO.getSpuVO().getSpuId());
                spuDraftPO.setSpuId(null);
                SpuVO spuVO = saveSpuVO.getSpuVO();
                spuDraftPO.setUpdater(spuVO.getUpdater());
                // 为sku生成skuKey，此处非常关键   ZHAOYAN_SALE_ATTR
                this.generatorSkuKeyForSkuList(saveSpuVO.isCrossBorder(), saveSpuVO.getSkuVOList());
                // skuDrafts
                List<SkuDraftPO> insertSkuDraftPOS = this.buildSkuDraftPos(saveSpuVO,spuDraftPO);
                saveSpuVO.setSkuVOList(null);
                this.buildDraftPo(saveSpuVO, spuDraftPO);

                updateSpuDraftList.add(spuDraftPO);
                insertSkuDraftList.addAll(insertSkuDraftPOS);
                if(CollectionUtils.isNotEmpty(skuDraftPOMap.get(saveSpuVO.getSpuVO().getSpuId()))){
                    deleteSkuDraftList.addAll(skuDraftPOMap.get(saveSpuVO.getSpuVO().getSpuId()));
                }
            }
        }
        if(CollectionUtils.isNotEmpty(insertSkuDraftList)){
            skuDraftAtomicService.saveBatch(insertSkuDraftList);
        }
        if(CollectionUtils.isNotEmpty(deleteSkuDraftList)){
            deleteSkuDraftList.forEach(item->{
                item.setSpuId(null);
//                item.setUpdateTime(new Date().getTime());
                item.setYn(YnEnum.NO.getCode());
            });
            skuDraftAtomicService.saveOrUpdateBatch(deleteSkuDraftList);
        }
        spuDraftAtomicService.saveOrUpdateBatch(updateSpuDraftList);
    }

    @Override
    public void updateDraftList(List<SaveSpuVO> saveSpuVOList) {

    }

    @Override
    @PFTracing
    public SpuDetailVO getSpuDetailVoFromDraftBySpuId(Long spuId) {
        log.info("SpuDraftManageServiceImpl.getSpuDetailVoFromDraftBySpuId spuId:{}",spuId);
        // 查询已保存草稿
        SaveSpuVO saveVo = getSaveSpuVoFromDraftBySpuId(spuId);
        if (Objects.isNull(saveVo)) {
            log.error("SpuDraftManageServiceImpl.getSpuDetailVoFromDraftBySpuId saveVo is null");
            return null;
        }
        // 对象转换
        SpuDetailVO spuDetailVO = SpuConvert.INSTANCE.saveVo2DetailVo(saveVo);

        SpuVO spuVO = spuDetailVO.getSpuVO();
        spuVO.setSaleUnitStr(extendInfoService.saleUnit(spuVO.getSaleUnit(), LangConstant.LANG_ZH));
        // 补全国家信息
        spuConvertService.fillSpuCountry(spuDetailVO);
        // 补全语种信息
        spuConvertService.fillSpuLang(spuDetailVO);
        // 补全类目信息
        List<PropertyVO> skuExtendPropertyList = spuConvertService.fillCategoryInfo(spuDetailVO);
        // 补全品牌信息
        spuConvertService.fillBrandInfo(spuDetailVO);
        spuConvertService.fillVendorName(spuDetailVO);
        spuConvertService.fillJdVendorName(spuDetailVO.getSkuVOList());
        spuDetailVO.getSpuVO().setGroupExtAttribute((spuConvertService.getGroupExtAttribute(spuDetailVO.getStoreExtendPropertyList())));
        spuConvertService.convertExtendAttributeAndFillSelectedForView(spuDetailVO);
        spuConvertService.fillSpuCertificate(spuDetailVO);
        spuConvertService.fillApprovedSkuStockRelation(spuDetailVO.getSkuVOList(),spuVO.getAttributeScope());
        spuConvertService.fillSpuAmendReason(spuDetailVO);
        spuDetailVO.setSpuInterPropertyList(productAttributeConvertService.convertAttributeFromDraft(spuDetailVO.getSpuInterPropertyList(),spuDetailVO.getInterPropertyList()));
        spuConvertService.fillSkuInterVoList(spuDetailVO);
        // 处理sku扩展属性，sku扩展属性分组信息
        spuConvertService.handleSkuVOGroupExtAttribute(spuDetailVO.getSkuVOList(), skuExtendPropertyList);
        spuConvertService.fillSkuCurrencyVoList(spuDetailVO);
        spuDetailVO.getSpuVO().setSpuLangExtendVOList(spuConvertService.convertSpuLangExtendList(spuDetailVO.getSpuVO()));
        skuReadManageService.handleLocalSkuPrice(spuDetailVO.getSkuVOList(),spuVO.getSourceCountryCode(),spuVO.getVendorCode());
        // 获取新版销售属性信息  ZHAOYAN_SALE_ATTR
        spuConvertService.fillSaleAttributes(spuDetailVO);
        return spuDetailVO;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class)
    public Long saveOrUpdateDraft(SaveSpuVO saveSpuVOParam) {

        // 为sku生成skuKey，此处非常关键，后面深copy后这个key也会在新对象skuVOList中    ZHAOYAN_SALE_ATTR
        this.generatorSkuKeyForSkuList(saveSpuVOParam.isCrossBorder(), saveSpuVOParam.getSkuVOList());

        // 深copy一下
        SaveSpuVO saveSpuVO = JSONObject.parseObject(JSONObject.toJSONString(saveSpuVOParam),SaveSpuVO.class);
        final Long spuId = saveSpuVO.getSpuVO().getSpuId();
        SpuDraftPO spuDraftPo = spuDraftAtomicService.getBySpuId(spuId);
        if (Objects.nonNull(spuDraftPo)) {
            spuDraftPo.setSpuId(null);
        } else {
            spuDraftPo = new SpuDraftPO();
            spuDraftPo.setSpuId(spuId);
            if(LoginContextHolder.getLoginContextHolder()!= null && StringUtils.isNotBlank(LoginContextHolder.getLoginContextHolder().getPin())){
                spuDraftPo.setCreator(LoginContextHolder.getLoginContextHolder().getPin());
                spuDraftPo.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
            } else {
                spuDraftPo.setCreator(Constant.SYSTEM);
                spuDraftPo.setUpdater(Constant.SYSTEM);
            }
            spuDraftPo.setCreateTime(new Date());
        }

        List<SkuDraftPO> inertSkuDrafts = this.buildSkuDraftPos(saveSpuVO,spuDraftPo);

        if(!saveSpuVO.isCrossBorder()){
            // 本土品：草稿保存后，保存销售属性
            this.saveSaleAttributeForDraft(saveSpuVO);
        }
        saveSpuVO.setSkuVOList(null);
        this.buildDraftPo(saveSpuVO, spuDraftPo);

        skuDraftAtomicService.deleteBySpuId(spuId);
        if(CollectionUtils.isNotEmpty(inertSkuDrafts)){
            skuDraftAtomicService.saveBatch(inertSkuDrafts);
        }
        // 保存更新草稿
        spuDraftAtomicService.saveOrUpdate(spuDraftPo);

        log.error("保存草稿完成，spuId={}", spuId);
        return spuId;
    }

     /**
     * 保存草稿阶段的销售属性
     *
     * @param saveSpuVO 保存SPU请求对象
     */
    private void saveSaleAttributeForDraft(SaveSpuVO saveSpuVO) {
        try {
            log.info("LocalSpuWriteManageService.saveSaleAttributeForDraft 开始保存草稿阶段销售属性, spuId={}", saveSpuVO.getSpuVO().getSpuId());

            Long spuId = saveSpuVO.getSpuVO().getSpuId();
            List<SkuVO> skuVOList = saveSpuVO.getSkuVOList();

            if (spuId == null || CollectionUtils.isEmpty(skuVOList)) {
                log.warn("LocalSpuWriteManageService.saveSaleAttributeForDraft spuId或SKU列表为空, spuId={}", spuId);
                return;
            }

            Boolean result = saleAttributeManageService.createSkuSaleAttributeForLocalSkuDraft(spuId, skuVOList);  // ZHAOYAN_SALE_ATTR

            if (Boolean.TRUE.equals(result)) {
                log.info("LocalSpuWriteManageService.saveSaleAttributeForDraft 草稿阶段销售属性保存成功, spuId={}, SKU数量={}",
                        spuId, skuVOList.size());
            } else {
                log.warn("LocalSpuWriteManageService.saveSaleAttributeForDraft 草稿阶段销售属性保存失败, spuId={}", spuId);
            }
        } catch (Exception e) {
            log.error("LocalSpuWriteManageService.saveSaleAttributeForDraft 草稿阶段销售属性保存异常, spuId={}", saveSpuVO.getSpuVO().getSpuId(), e);
            throw e; // 销售属性失败应该整体失败
        }
    }

    @Override
    public SpuDraftVO  getDraftBySpuId(Long spuId) {
        LambdaQueryWrapper<SpuDraftPO> queryWrapper = new LambdaQueryWrapper<SpuDraftPO>().eq(SpuDraftPO::getSpuId, spuId).eq(SpuDraftPO::getYn, YnEnum.YES.getCode());
        SpuDraftPO spuDraftPo = spuDraftAtomicService.getOne(queryWrapper);
        return SpuConvert.INSTANCE.draftPo2Vo(spuDraftPo);
    }

    @Override
    public boolean deleteDraftBySpuIds(List<Long> spuIds) {
        LambdaQueryWrapper<SpuDraftPO> lqw = new LambdaQueryWrapper<SpuDraftPO>()
                .select(SpuDraftPO::getSpuId, SpuDraftPO::getId).in(SpuDraftPO::getSpuId, spuIds).eq(SpuDraftPO::getYn, YnEnum.YES.getCode());
        List<SpuDraftPO> spuDraftPoList = spuDraftAtomicService.list(lqw);
        spuDraftPoList.forEach(draft -> draft.setYn(YnEnum.NO.getCode()));
        // 批量逻辑删除
        return spuDraftAtomicService.saveOrUpdateBatch(spuDraftPoList);
    }

    @Override
    public boolean deleteDraftBySpuId(Long spuId) {
        // 逻辑删除
        UpdateWrapper<SpuDraftPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("yn", YnEnum.NO.getCode()).eq("spu_id", spuId).eq("yn", YnEnum.YES.getCode());
        return spuDraftAtomicService.update(new SpuDraftPO(), updateWrapper);
    }

    /**
     * 草稿
     *
     * @param saveSpuVO 保存spu对象
     */
    @PFTracing
    public void buildDraftPo(SaveSpuVO saveSpuVO, SpuDraftPO draftPo) {
        draftPo.setYn(YnEnum.YES.getCode());
        SpuVO spuVO = saveSpuVO.getSpuVO();
        draftPo.setVendorCode(spuVO.getVendorCode());
        draftPo.setAuditStatus(spuVO.getAuditStatus());
        draftPo.setTaxAuditStatus(spuVO.getTaxAuditStatus());
        draftPo.setCatId(spuVO.getCatId());
        draftPo.setJdCatId(spuVO.getCatId());
        draftPo.setSourceCountryCode(spuVO.getSourceCountryCode());
        log.info("SpuDraftManageServiceImpl.buildDraftPo draftPo.getGroupExtAttribute:{}, saveSpuVO.getStoreExtendPropertyList():{}",draftPo.getGroupExtAttribute(),saveSpuVO.getStoreExtendPropertyList());
        draftPo.setLevel(spuVO.getLevel());
        if (MapUtils.isNotEmpty(saveSpuVO.getPcDescriptionMap())) {
            draftPo.setPcDescription(JSON.toJSONString(saveSpuVO.getPcDescriptionMap()));
            saveSpuVO.setPcDescriptionMap(null);
        }
        if (MapUtils.isNotEmpty(saveSpuVO.getAppDescriptionMap())) {
            draftPo.setAppDescription(JSON.toJSONString(saveSpuVO.getAppDescriptionMap()));
            saveSpuVO.setAppDescriptionMap(null);
        }
        if (StringUtils.isNotBlank(draftPo.getSpuJsonInfo())) {
            SaveSpuVO draftSaveSpuVo = JSON.parseObject(draftPo.getSpuJsonInfo(), SaveSpuVO.class);
            saveSpuVO.getSpuVO().setBuyer(draftSaveSpuVo.getSpuVO().getBuyer());
        }
        if(CollectionUtils.isNotEmpty(saveSpuVO.getSpuCertificateVOList())){
            draftPo.setSpuCertificate(JSON.toJSONString(saveSpuVO.getSpuCertificateVOList()));
            saveSpuVO.setSpuCertificateVOList(null);
        }
        if(CollectionUtils.isNotEmpty(saveSpuVO.getSpuInterPropertyList())){
            draftPo.setSpuInterProperty(JSON.toJSONString(saveSpuVO.getSpuInterPropertyList()));
            saveSpuVO.setSpuInterPropertyList(null);
        }
        if(CollectionUtils.isNotEmpty(saveSpuVO.getStoreExtendPropertyList())){
            // 对原草稿扩展属性的修改，原草稿存储的json，新版本仍然为json，但是包含了扩展属性组信息
            draftPo.setGroupExtAttribute(spuConvertService.getGroupExtAttribute(saveSpuVO.getStoreExtendPropertyList()));
            saveSpuVO.setStoreExtendPropertyList(null);
        }
        draftPo.setSpuJsonInfo(JSON.toJSONString(saveSpuVO));
        draftPo.setRemark("");
        draftPo.setUpdateTime(new Date());
    }

    /**
     * 为sku生成skuKey，关键方法
     * @param skuVOList
     */
    private void generatorSkuKeyForSkuList(Boolean isCrossBorder, List<SkuVO> skuVOList){
        if(CollectionUtils.isEmpty(skuVOList)){
            return;
        }
        // 通过销售属性恢复skuKey，以确保skuKey只生成一次，全局唯一，如果销售属性无法恢复，尝试通过skuId恢复（需要判断是否需要，如果跨境品有问题先排查一下跨境品为什么有问题）
        this.recoverSkuKeyForSkuListBySaleAttribute(isCrossBorder, skuVOList);
        for(SkuVO skuVO : skuVOList){
            if(StringUtils.isBlank(skuVO.getSkuKey())){
                skuVO.setSkuKey(String.valueOf(productIdGenerator.genSkuId()));
            }
        }
    }

    /**
     * 通过销售属性恢复已发布sku的（草稿或正式）skuKey，无奈之举
     */
    public void  recoverSkuKeyForSkuListBySaleAttribute(Boolean isCrossBorder, List<SkuVO> skuVOList){
        if(CollectionUtils.isEmpty(skuVOList)){
            return;
        }
        Map<String, SkuVO> saleAttributeValueIdToSkuVOMap = new HashMap<>();
        Map<String, Set<Long>> stringToSaleAttributeValueIdSetMap = new HashMap<>();
        // 通过销售属性恢复skuKey
        for (SkuVO skuVO : skuVOList) {
            if(isCrossBorder!= null && StringUtils.isNotBlank(skuVO.getSkuKey()) && isCrossBorder){
                continue;
            }
            List<PropertyValueVO> storeSalePropertyList = skuVO.getStoreSalePropertyList();
            if (CollectionUtils.isNotEmpty(storeSalePropertyList)) {
                int valudIdCount = 0;
                if (CollectionUtils.isNotEmpty(skuVO.getStoreSalePropertyList())) {
                    if (skuVO.getStoreSalePropertyList().size()>Constant.MAX_SALE_ATTRIBUTE_NUM_PER_SKU) {
                        throw new BizException("恢复sku key时，销售属性值个数超过最大值:"+Constant.MAX_SALE_ATTRIBUTE_NUM_PER_SKU+"，请检查");
                    }
                    Set<Long> saleAttributeValueIdSet = new HashSet<>();
                    for (PropertyValueVO propertyValueVO : skuVO.getStoreSalePropertyList()) {
                        if (propertyValueVO.getAttributeValueId() != null) {
                            valudIdCount++;
                            saleAttributeValueIdSet.add(propertyValueVO.getAttributeValueId());
                        }
                    }
                    if (valudIdCount == skuVO.getStoreSalePropertyList().size() && saleAttributeValueIdSet.size() == skuVO.getStoreSalePropertyList().size()) {
                        // 如果sku的所有销售属性值都有id（值id的个数与销售属性值的个数相等）, 则是一个已有的sku，根据销售属性值id查询关系表，获取skuKey
                        // saleAttributeValueIdSet连接成字符串，查询关系表，获取skuKey
                        String saleAttributeValueIdSetStr = saleAttributeValueIdSet.stream()
                                .sorted()  // 确保顺序一致
                                .map(String::valueOf)
                                .collect(Collectors.joining(Constant.COMMA));
                        saleAttributeValueIdToSkuVOMap.put(saleAttributeValueIdSetStr, skuVO);
                        stringToSaleAttributeValueIdSetMap.put(saleAttributeValueIdSetStr, saleAttributeValueIdSet);
                    }
                } else {
                    throw new BizException("恢复sku key时，销售属性值为空，请检查");
                }
            }
        }
        if(MapUtils.isEmpty(stringToSaleAttributeValueIdSetMap)){
            return;
        }
        // 从stringToSaleAttributeValueIdSetMap中获取所有saleAttributeValueIdSet    
        Set<Long> allSaleAttributeValueIdSet = new HashSet<>();
        for (Set<Long> saleAttributeValueIdSet : stringToSaleAttributeValueIdSetMap.values()) {
            allSaleAttributeValueIdSet.addAll(saleAttributeValueIdSet);
        }
        // 根据allSaleAttributeValueIdSet查询关系表，获取skuKey
        List<SkuSaleAttributeValueRelationPO> skuSaleAttributeValueRelationPOList = skuSaleAttributeValueRelationAtomicService.getValidBySaleAttributeValueIds(allSaleAttributeValueIdSet);
        if(CollectionUtils.isEmpty(skuSaleAttributeValueRelationPOList)){
            throw new BizException("恢复sku key时，销售属性值id关系表为空，请检查");
        }
        // skuSaleAttributeValueRelationPOList通过skukey分组，获取attributeValueIdSet
        Map<String, Set<Long>> skuKeyToAttributeValueIdSetMap = new HashMap<>();
        for (SkuSaleAttributeValueRelationPO skuSaleAttributeValueRelationPO : skuSaleAttributeValueRelationPOList) {
            if(skuSaleAttributeValueRelationPO!=null && skuSaleAttributeValueRelationPO.getSkuKey()!=null){
                skuKeyToAttributeValueIdSetMap.computeIfAbsent(skuSaleAttributeValueRelationPO.getSkuKey(), k -> new HashSet<>()).add(skuSaleAttributeValueRelationPO.getSaleAttributeValueId());
            }
        }
        // 遍历saleAttributeValueIdToSkuVOMap，将skuKey设置到skuVO中
        for (Map.Entry<String, SkuVO> entry : saleAttributeValueIdToSkuVOMap.entrySet()) {
            String saleAttributeValueIdSetStr = entry.getKey();
            SkuVO skuVO = entry.getValue();
            Set<Long> saleAttributeValueIdSet = stringToSaleAttributeValueIdSetMap.get(saleAttributeValueIdSetStr);
            if (CollectionUtils.isNotEmpty(saleAttributeValueIdSet)) {
                // 计算skuKey
                for (Map.Entry<String, Set<Long>> stringSetEntry : skuKeyToAttributeValueIdSetMap.entrySet()) {
                    String skuKey = stringSetEntry.getKey();
                    Set<Long> attributeValueIdSet = stringSetEntry.getValue();
                    if(CollectionUtils.isNotEmpty(attributeValueIdSet) && attributeValueIdSet.containsAll(saleAttributeValueIdSet)){
                        skuVO.setSkuKey(skuKey);
                        log.info("恢复sku key时，成功恢复skukey，saleAttributeValueIdSetStr:{},skuKey:{}", saleAttributeValueIdSetStr, skuKey);
                        break;
                    }
                }
                // 如果最终没有找到skukey，抛出异常
                if(StringUtils.isBlank(skuVO.getSkuKey())){
                    log.error("恢复sku key时，没有找到skukey，请检查saleAttributeValueIdSetStr:{}", saleAttributeValueIdSetStr);
                    throw new BizException("恢复sku key时，没有找到skukey，请检查");
                }
            }
        }
    }

    /**
     * 草稿
     *
     * @param saveSpuVO 保存spu对象
     */
    @PFTracing
    public List<SkuDraftPO> buildSkuDraftPos(SaveSpuVO saveSpuVO,SpuDraftPO spuDraftPO) {
        List<SkuVO> skuVOList = saveSpuVO.getSkuVOList();
        if(CollectionUtils.isEmpty(skuVOList)){
            log.error("SpuDraftManageServiceImpl.buildSkuDraftPos skuVOList is null");
            return Collections.emptyList();
        }
        Long spuId = saveSpuVO.getSpuVO().getSpuId();
        List<SkuDraftPO> results = new ArrayList<>();
        for(SkuVO skuVO : skuVOList){
            SkuDraftPO skuDraftPO = new SkuDraftPO();
            skuDraftPO.setSpuId(spuId);
            skuDraftPO.setSkuId(skuVO.getSkuId());
            skuDraftPO.setJdSkuId(skuVO.getJdSkuId());

            // SpuDraftManageServiceImpl.buildSkuDraftPos 设置jdMainSkuId和jdSpuId到SkuDraftPO独立字段
            if (Objects.nonNull(skuVO.getJdMainSkuId())) {
                skuDraftPO.setJdMainSkuId(skuVO.getJdMainSkuId());
                log.info("SpuDraftManageServiceImpl.buildSkuDraftPos 设置jdMainSkuId到草稿表，skuId: {}, jdMainSkuId: {}", skuVO.getSkuId(), skuVO.getJdMainSkuId());
            }

            if (Objects.nonNull(skuVO.getJdSpuId())) {
                skuDraftPO.setJdSpuId(skuVO.getJdSpuId());
                log.info("SpuDraftManageServiceImpl.buildSkuDraftPos 设置jdSpuId到草稿表，skuId: {}, jdSpuId: {}", skuVO.getSkuId(), skuVO.getJdSpuId());
            }

            // sku添加按群组划分的扩展属性, json字符串
            if(CollectionUtils.isNotEmpty(skuVO.getStoreExtendPropertyList())){
                skuDraftPO.setGroupExtAttribute(spuConvertService.getGroupExtAttribute(skuVO.getStoreExtendPropertyList()));
                skuVO.setStoreExtendPropertyList(null);
                skuVO.setExtendPropertyList(null);
            }

            log.info("SpuDraftManageServiceImpl.buildSkuDraftPos skuDraftPO.getGroupExtAttribute:{}, skuVO.getStoreExtendPropertyList():{}",skuDraftPO.getGroupExtAttribute(),skuVO.getStoreExtendPropertyList());
            // 在这里skuKey应该已经生成了，如果没有，请在方法外部调用generatorSkuKeyForSkuList   ZHAOYAN_SALE_ATTR
            skuDraftPO.setSkuKey(skuVO.getSkuKey());
            if(CollectionUtils.isNotEmpty(skuVO.getSkuInterPropertyList())){
                skuDraftPO.setSkuInterProperty(JSON.toJSONString(skuVO.getSkuInterPropertyList()));
                skuVO.setSkuInterPropertyList(null);
            }
            if(CollectionUtils.isNotEmpty(skuVO.getSkuCertificateVOList())){
                skuDraftPO.setSkuCertificate(JSON.toJSONString(skuVO.getSkuCertificateVOList()));
                skuVO.setSkuCertificateVOList(null);
            }
            if(CollectionUtils.isNotEmpty(skuVO.getSkuStockRelationList())){
                skuDraftPO.setSkuStockRelation(JSON.toJSONString(skuVO.getSkuStockRelationList()));
                skuVO.setSkuStockRelationList(null);
            }
            List<PropertyValueVO> storeSalePropertyList = skuVO.getStoreSalePropertyList();
            if(CollectionUtils.isNotEmpty(skuVO.getStoreSalePropertyList())){
//                skuDraftPO.setSaleProperty(JSON.toJSONString(skuVO.getStoreSalePropertyList()));
                skuVO.setStoreSalePropertyList(null);
            }

            List<PropertyVO> extendPropertyList = skuVO.getExtendPropertyList();
            skuVO.setExtendPropertyList(null);
            skuDraftPO.setSkuJsonInfo(JSON.toJSONString(skuVO));
            skuVO.setStoreSalePropertyList(storeSalePropertyList);  // 恢复，实属无奈之举
            skuVO.setExtendPropertyList(extendPropertyList); // 恢复
            skuDraftPO.setCreator(spuDraftPO.getCreator());
            Long current = new Date().getTime();
            skuDraftPO.setCreateTime(current);
            skuDraftPO.setUpdater(spuDraftPO.getUpdater());
            skuDraftPO.setUpdateTime(current);
            results.add(skuDraftPO);
        }
//        saveSpuVO.setSkuVOList(null);      实属无奈之举
        return results;
    }

    @Override
    public PageInfo<SpuVO> page(SpuQueryReqVO spuQueryReqVO) {
        String lang = LangContextHolder.get();
        Page<SpuVO> spuVoPage = new Page<>(spuQueryReqVO.getIndex(), spuQueryReqVO.getSize());
        try {

            // 补充spu关联mku的sku
            if (null != spuQueryReqVO.getMkuId()) {
                List<MkuRelationPO> mkuRelationPOS = mkuRelationAtomicService.getMkuByMkuId(spuQueryReqVO.getMkuId());
                if (CollectionUtils.isEmpty(mkuRelationPOS)) {
                    return pageTransformSpu(spuVoPage);
                }
                List<Long> skuIds = mkuRelationPOS.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toList());
                spuQueryReqVO.setSkuIds(skuIds);
            }

            if (CollectionUtils.isNotEmpty(spuQueryReqVO.getMkuIds())) {
                List<MkuRelationPO> mkuRelationPOS = mkuRelationAtomicService.queryBindListByMkuIds(spuQueryReqVO.getMkuIds());
                if (CollectionUtils.isEmpty(mkuRelationPOS)) {
                    return pageTransformSpu(spuVoPage);
                }
                List<Long> skuIds = mkuRelationPOS.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toList());
                // 如果传过来的skuIds不为空，则和mku绑定的skuid去交集
                if (CollectionUtils.isNotEmpty(spuQueryReqVO.getSkuIds())) {
                    skuIds = Lists.newArrayList(CollectionUtil.intersection(spuQueryReqVO.getSkuIds(), skuIds));
                    if (CollectionUtils.isEmpty(skuIds)) {
                        return pageTransformSpu(spuVoPage);
                    }
                }
                spuQueryReqVO.setSkuIds(skuIds);
            }

            // 查询草稿总数
            long total = spuDraftAtomicService.getTotal(spuQueryReqVO);
            spuVoPage.setTotal(total);

            if (total == 0) {
                return pageTransformSpu(spuVoPage);
            }

            // 分页查询草稿
            List<SpuDraftPO> spuDraftPOList = spuDraftAtomicService.listSearch(spuQueryReqVO);

            if (CollectionUtils.isNotEmpty(spuDraftPOList)) {
                // 批量查询类目面包屑
                Set<Long> catIds = Sets.newHashSet();
                Set<Long> spuIds = Sets.newHashSet();
                Set<Long> brandIds = Sets.newHashSet();
                Set<String> supplierCodes = Sets.newHashSet();
                spuDraftPOList.forEach(po -> {
                    catIds.add(po.getJdCatId());
                    spuIds.add(po.getSpuId());
                    Long brandId = this.getBrandId(po);
                    if(brandId != null){
                        brandIds.add(brandId);
                    }
                    supplierCodes.add(po.getVendorCode());
                });

                // sku草稿
                CompletableFuture<Map<Long,List<SkuDraftPO>>>
                        skuDraftPOMapFuture = CompletableFuture.supplyAsync(() -> skuDraftAtomicService.queryMapBySpuIds(spuIds), searchSpuExecutor);
                // 品牌
                CompletableFuture<Map<Long, String>>
                        brandMapFeature = CompletableFuture.supplyAsync(() -> brandOutService.queryNameByIds(brandIds,lang), searchSpuExecutor);
                // 查询类目面包屑
                CompletableFuture<Map<Long, String>>
                    catPathFuture = CompletableFuture.supplyAsync(() -> categoryOutService.queryPathStr(catIds, lang), searchSpuExecutor);
                // 批量查询供应商信息
                // 查询spu下第一个sku的国内供应商信息
                CompletableFuture<Map<String, SupplierBaseInfoPO>> jdVendorFuture = CompletableFuture.supplyAsync(() -> supplierBaseInfoAtomicService.batchQueryByCode(supplierCodes), searchSpuExecutor);
                // 查询spu下sku列表
                CompletableFuture<Map<Long, List<SkuPO>>> skuMapFuture = CompletableFuture.supplyAsync(() -> skuAtomicService.querySkuListBySpuIds(Sets.newHashSet(spuIds)), searchSpuExecutor);

                Map<Long, List<SkuDraftPO>> skuDraftPOMap = skuDraftPOMapFuture.get(30, TimeUnit.SECONDS);
                Map<Long, String> brandMap = brandMapFeature.get(30, TimeUnit.SECONDS);
                Map<Long, String> catPathMap = catPathFuture.get(30, TimeUnit.SECONDS);
                Map<String, SupplierBaseInfoPO> supplierMap = jdVendorFuture.get(30, TimeUnit.SECONDS);
                // spu关联的mku和sku查询
                Map<Long, List<SkuPO>> spuAndSkuMap = skuMapFuture.get(30, TimeUnit.SECONDS);
                CompletableFuture<Map<Long, SpuVO>> mkuRelationMapFuture = CompletableFuture.supplyAsync(() -> spuConvertService.queryMkuAndSkuRelation(spuAndSkuMap), searchSpuExecutor);
                Map<Long, SpuVO> mkuRelationMap = mkuRelationMapFuture.get(30, TimeUnit.SECONDS);

                CompletableFuture.allOf(skuDraftPOMapFuture,catPathFuture,brandMapFeature,jdVendorFuture,skuMapFuture,mkuRelationMapFuture).join();
                List<SpuVO> spuVoList = new ArrayList<>(spuDraftPOList.size());

                CustomerVO customerVO = new CustomerVO();
                customerVO.setCountry(spuQueryReqVO.getSourceCountryCode());
                customerVO.setCompanyType("1");

                List<Future<SpuVO>> draftFutureList = Lists.newArrayList();
                for (SpuDraftPO spuDraftPo : spuDraftPOList) {
//                    SpuVO vo = this.spuVo(spuDraftPo, catPathMap, supplierMap, lang, mkuRelationMap, customerVO, spuAndSkuMap,skuDraftPOMap,brandMap);
                    //spuVoList.add(this.spuVo(spuDraftPo, catPathMap, supplierMap, lang, mkuRelationMap, customerVO,spuAndSkuMap));
                    draftFutureList.add(CompletableFuture.supplyAsync(() -> this.spuVo(spuDraftPo, catPathMap, supplierMap, lang, mkuRelationMap, customerVO, spuAndSkuMap,skuDraftPOMap,brandMap),
                            searchSpuExecutor));
                }

                if (CollectionUtils.isNotEmpty(draftFutureList)) {
                    for (Future<SpuVO> future : draftFutureList) {
                        try {
                            spuVoList.add(future.get(2, TimeUnit.SECONDS));
                        } catch (Exception e) {
                            log.error("【系统异常】draft page 异步取回数据异常",e);
                            if (null != future) {
                                future.cancel(true);
                            }
                        }
                    }
                }

                spuVoPage.setRecords(spuVoList);
            }
        } catch (Exception e) {
            log.error("【系统异常】SpuDraftManageServiceImpl.page 商品搜索草稿商品异常,入参:[spuQueryReqVO={}]", JSON.toJSONString(spuQueryReqVO), e);
        }
        return pageTransformSpu(spuVoPage);
    }


    private SpuVO spuVo(SpuDraftPO spuDraftPo, Map<Long, String> catPathMap, Map<String, SupplierBaseInfoPO> supplierMap, String lang, Map<Long, SpuVO> mkuRelationMap
            , CustomerVO customerVO,Map<Long, List<SkuPO>> spuAndSkuMap,Map<Long,List<SkuDraftPO>> skuDraftMap,Map<Long, String> brandMap) {
        SaveSpuVO saveSpuVO = JSON.parseObject(spuDraftPo.getSpuJsonInfo(), SaveSpuVO.class);
        SpuVO spuVO = saveSpuVO.getSpuVO();
        spuVO.setCatId(spuDraftPo.getJdCatId());
        spuVO.setSpuId(spuDraftPo.getSpuId());

        List<SkuVO> skuVOList = this.buildSkuVOS(skuDraftMap.get(spuDraftPo.getSpuId()),lang, spuDraftPo);
        if(CollectionUtils.isNotEmpty(skuVOList)){
            saveSpuVO.setSkuVOList(skuVOList);
        }

        spuVO.setSkuCount(CollectionUtils.isNotEmpty(saveSpuVO.getSkuVOList()) ? saveSpuVO.getSkuVOList().size() : 0);
        // 拼接skuId
        spuVO.setSkuIdStr(CollectionUtils.isNotEmpty(saveSpuVO.getSkuVOList()) ? saveSpuVO.getSkuVOList().stream().filter(Objects::nonNull).filter(vo -> Objects.nonNull(vo.getSkuId())).map(String::valueOf).collect(Collectors.joining(Constant.COMMA)) : "");
        spuVO.setAuditStatus(spuDraftPo.getAuditStatus());
        spuVO.setTaxAuditStatus(spuDraftPo.getTaxAuditStatus());
        spuVO.setCategoryBreadCrumbs(catPathMap.getOrDefault(spuVO.getCatId(), ""));
        spuVO.setVendorName(supplierMap.containsKey(spuVO.getVendorCode()) ? supplierMap.get(spuVO.getVendorCode()).getBusinessLicenseName() : "");
        if(spuDraftPo.getAuditStatus() != null && SpuAuditStatusEnum.WAITING_APPROVED.getCode().equals(spuDraftPo.getAuditStatus())){
            spuVO.setLevel(spuDraftPo.getLevel());
        } else {
            spuVO.setLevel(null);
        }
        spuVO.setBrandName(brandMap.get(spuVO.getBrandId()));
        spuVO.setUpdateTime(spuDraftPo.getUpdateTime());
        spuVO.setCreateTime(spuDraftPo.getCreateTime());
        this.spuNameByLang(spuVO, lang);
        SpuVO tempSpuVo = mkuRelationMap.get(spuDraftPo.getSpuId());
        if (Objects.nonNull(tempSpuVo)) {
            spuVO.setMkuIds(tempSpuVo.getMkuIds());
            spuVO.setSkuIds(tempSpuVo.getSkuIds());
        }
        if(CollectionUtils.isNotEmpty(spuVO.getSkuIds()) && spuVO.getSkuIds().size() == 1) {
            List<SkuPO> skuPOList = spuAndSkuMap.get(spuDraftPo.getSpuId());
            Map<Long, CategoryTaxPO> categoryTaxMap = categoryTaxAtomicService.batchSkuCatTax(customerVO, skuPOList);
            Long skuId = spuVO.getSkuIds().get(0);
            if(categoryTaxMap != null) {
                if(categoryTaxMap.containsKey(skuId)){
                    CategoryTaxPO categoryTaxPO = categoryTaxMap.get(skuId);
                    spuVO.setVatRate(categoryTaxPO.getVatRate());
                }
            }
        }
        List<SpuSkuStockVO> skuStockVOList = convertSkuListSaleInfo(saveSpuVO.getSkuVOList(),spuVO, lang );
        spuVO.setSkuStockList(skuStockVOList);
        return spuVO;
    }


     @Resource
     private StockManageService stockManageService;
    /**
     * 将 SkuVO 列表中的销售属性信息转换为指定语言的名称映射。
     * @param skuVOList SkuVO 对象列表
     * @return 转换后的 SkuVO 对象列表
     */
    private List<SpuSkuStockVO> convertSkuListSaleInfo(List<SkuVO> skuVOList,SpuVO spuVO,String lang){
//        List<Long> AttributeIdList = new ArrayList<>();
//        List<Long> AttributeValueIdList = new ArrayList<>();
        List<Long> skuIdList = new ArrayList<>();
        String sourceCountryCode = spuVO.getSourceCountryCode();
        skuReadManageService.handleLocalSkuPrice(skuVOList,sourceCountryCode,spuVO.getVendorCode());
        for (SkuVO skuVO : skuVOList){
            List<PropertyValueVO> storeSalePropertyList = skuVO.getStoreSalePropertyList();
            if(Objects.nonNull(skuVO.getSkuId())) {
                skuIdList.add(skuVO.getSkuId());
            }
//            for (PropertyValueVO propertyValueVO : storeSalePropertyList){
//                AttributeIdList.add(propertyValueVO.getAttributeId());
//                AttributeValueIdList.add(propertyValueVO.getAttributeValueId());
//            }
        }

//        Map<Long, String> attrIdNameMap= attributeLangAtomicService.attrNameByAttrId(AttributeIdList,lang);
//        Map<Long, String> attrvalueIdNameMap = attributeValueLangAtomicService.attrValueNameByAttrValueId(AttributeValueIdList,lang);
        Map<Long, Long> skuIdMkuIdMap = CollectionUtils.isEmpty(skuIdList) ? Maps.newHashMap() : mkuRelationAtomicService.skuIdMkuIdMapBySkuIds(skuIdList);
        StockManageReqDTO stockManageReqDTO = getStockManageReqDTO(skuIdList);
        //stockManageReqDTO.setCountryCode(sourceCountryCode);
        Map<Long, StockResDTO> stockMap = CollectionUtils.isEmpty(skuIdList) ? Maps.newHashMap() : stockManageService.getStock(stockManageReqDTO);
        List<SpuSkuStockVO> skuStockVOList  = new ArrayList<>();
        for (SkuVO skuVO : skuVOList){
            List<PropertyValueVO> storeSalePropertyList = skuVO.getStoreSalePropertyList();
            Map<String,String> saleAttributeMap = Maps.newHashMap();
            for (PropertyValueVO propertyValueVO : storeSalePropertyList){
//                String key = attrIdNameMap.getOrDefault(propertyValueVO.getAttributeId(),propertyValueVO.getAttributeId().toString());
//                String value = attrvalueIdNameMap.getOrDefault(propertyValueVO.getAttributeValueId(),propertyValueVO.getAttributeValueId().toString());
                saleAttributeMap.put(propertyValueVO.getAttributeName(),propertyValueVO.getAttributeValueName());
            }
            Long mkuId = skuIdMkuIdMap.get(skuVO.getSkuId());
            StockResDTO stockResDTO = stockMap.get(skuVO.getSkuId());

            SpuSkuStockVO spuSkuStockVO = new SpuSkuStockVO();
            spuSkuStockVO.setSaleAttributeMap(saleAttributeMap);
            spuSkuStockVO.setMkuId(mkuId);
            spuSkuStockVO.setStock(Objects.nonNull(stockResDTO) ? String.valueOf(stockResDTO.getStock()) : (StringUtils.isNotBlank(skuVO.getStockNum()) ? skuVO.getStockNum() : Constant.MINUS));
            spuSkuStockVO.setSkuId(skuVO.getSkuId());
            spuSkuStockVO.setSpuId(spuVO.getSpuId());
            spuSkuStockVO.setVendorSkuId(skuVO.getVendorSkuId());
            spuSkuStockVO.setSalePrice(StringUtils.isNotBlank(skuVO.getSalePrice())? new BigDecimal(skuVO.getSalePrice()): null);
            spuSkuStockVO.setPurchasePrice(StringUtils.isNotBlank(skuVO.getPurchasePrice()) ? new BigDecimal(skuVO.getPurchasePrice()) : null);
            if (StringUtils.isNotBlank(skuVO.getTaxSalePrice())){
                spuSkuStockVO.setTaxSalePrice(new BigDecimal(skuVO.getTaxSalePrice()));
            }
            if (StringUtils.isNotBlank(skuVO.getTaxPurchasePrice())){
                spuSkuStockVO.setTaxPurchasePrice(new BigDecimal(skuVO.getTaxPurchasePrice()));
            }
            spuSkuStockVO.setSkuKey(skuVO.getSkuKey());
            spuSkuStockVO.setCountryCostPrice(skuVO.getCountryCostPrice());
            spuSkuStockVO.setAgreementPrice(skuVO.getAgreementPrice());
            spuSkuStockVO.setAgreementProfitRate(skuVO.getAgreementProfitRate());
            spuSkuStockVO.setCostCurrency(skuVO.getCostCurrency());
            spuSkuStockVO.setUpcCode(skuVO.getUpcCode());
            spuSkuStockVO.setWeight(skuVO.getWeight());
            spuSkuStockVO.setLength(skuVO.getLength());
            spuSkuStockVO.setWidth(skuVO.getWidth());
            spuSkuStockVO.setHeight(skuVO.getHeight());
            spuSkuStockVO.setProductionCycle(Optional.ofNullable(skuVO.getProductionCycle())
                .map(Object::toString)
                .orElse(""));
            spuSkuStockVO.setMoq(skuVO.getMoq());
            spuSkuStockVO.setHsCode(skuVO.getHsCode());
            skuStockVOList.add(spuSkuStockVO);
        }
        return skuStockVOList;
    }

    private StockManageReqDTO getStockManageReqDTO( List<Long> skuIdList){
        StockManageReqDTO stockManageReqDTO = new StockManageReqDTO();
        List<StockItemManageReqDTO> stockItem = new ArrayList<>();
        for (Long id : skuIdList){
            StockItemManageReqDTO stockItemManageReqDTO = new StockItemManageReqDTO();
            stockItemManageReqDTO.setSkuId(id);
            stockItemManageReqDTO.setNum(1L);
            stockItem.add(stockItemManageReqDTO);
        }
        stockManageReqDTO.setStockItem(stockItem);
        return stockManageReqDTO;
    }

    private void spuNameByLang(SpuVO spuVO,String lang){
        String spuName = spuVO.getSpuZhName();
        switch (lang){
            case LangConstant.LANG_VN:
                spuName = spuVO.getSpuVnName();
                break;
            case LangConstant.LANG_TH:
                spuName = spuVO.getSpuThName();
                break;
            case LangConstant.LANG_EN:
                spuName = spuVO.getSpuEnName();
                break;
            default:
                break;
        }
        spuVO.setSpuName(spuName);
    }

    public PageInfo<SpuVO> pageTransformSpu(Page<SpuVO> target) {
        PageInfo<SpuVO> output = new PageInfo<>();
        output.setSize(target.getSize());
        output.setIndex(target.getCurrent());
        if (CollectionUtils.isNotEmpty(target.getRecords())) {
            output.setTotal(target.getTotal());
            output.setRecords(target.getRecords());
        }
        return output;
    }

    @Override
    public SpuDetailVO getSpuDetailVoFromDraftInfo(SpuDetailReqVO reqVO) {
        log.info("SpuDraftManageServiceImpl.getSpuDetailVoFromDraftInfo reqVO:{}",JSON.toJSONString(reqVO));
        // 查询已保存草稿
        SaveSpuVO saveVo = getSaveSpuVoFromDraftBySpuId(reqVO.getSpuId());
        if (Objects.isNull(saveVo)) {
            log.error("SpuDraftManageServiceImpl.getSpuDetailVoFromDraftInfo saveVo is null");
            return null;
        }
        // 对象转换
        SpuDetailVO spuDetailVO = SpuConvert.INSTANCE.saveVo2DetailVo(saveVo);

        SpuVO spuVO = spuDetailVO.getSpuVO();
        // 兼容草稿中细节图片不为空的情况
        if (StringUtils.isNotBlank(spuVO.getDetailImg()) && CollectionUtils.isEmpty(spuVO.getDetailImgList())) {
            spuVO.setDetailImgList(Lists.newArrayList(spuVO.getDetailImg().split(Constant.HASHTAG)));
        }
        spuVO.setSaleUnitStr(extendInfoService.saleUnit(spuVO.getSaleUnit(), LangConstant.LANG_ZH));
        if(StringUtils.isNotBlank(reqVO.getAttributeScope())){
            spuVO.setAttributeScope(reqVO.getAttributeScope());
        }
        if(Objects.nonNull(reqVO.getIsExport())){
            spuVO.setIsExport(reqVO.getIsExport());
        }
        // 补全国家信息
        spuConvertService.fillSpuCountry(spuDetailVO);
        // 补全语种信息
        spuConvertService.fillSpuLang(spuDetailVO);
        // 补全类目信息
        List<PropertyVO> skuExtendPropertyList = spuConvertService.fillCategoryInfo(spuDetailVO);
        // 补全品牌信息
        spuConvertService.fillBrandInfo(spuDetailVO);
        spuConvertService.fillVendorName(spuDetailVO);
        spuConvertService.fillJdVendorName(spuDetailVO.getSkuVOList());
        // TODO 泄露出去的，要找到位置，处理，注释原方式的ext字符串
        spuDetailVO.getSpuVO().setGroupExtAttribute(spuConvertService.getGroupExtAttribute(spuDetailVO.getStoreExtendPropertyList()));
        spuConvertService.convertExtendAttributeAndFillSelectedForView(spuDetailVO);
        spuConvertService.fillSpuCertificate(spuDetailVO);
        spuDetailVO.setSpuInterPropertyList(productAttributeConvertService.convertAttributeFromDraft(spuDetailVO.getSpuInterPropertyList(),spuDetailVO.getInterPropertyList()));
        spuConvertService.fillCrossBorderSkuStock(spuDetailVO.getSkuVOList(),spuVO.getAttributeScope());
        spuConvertService.fillSpuAmendReason(spuDetailVO);
        spuConvertService.fillSkuInterVoList(spuDetailVO);
        // 处理sku扩展属性，sku扩展属性分组信息
        spuConvertService.handleSkuVOGroupExtAttribute(spuDetailVO.getSkuVOList(), skuExtendPropertyList);
        spuConvertService.fillSkuCurrencyVoList(spuDetailVO);
        spuDetailVO.getSpuVO().setSpuLangExtendVOList(spuConvertService.convertSpuLangExtendList(spuDetailVO.getSpuVO()));
        skuReadManageService.handleLocalSkuPrice(spuDetailVO.getSkuVOList(),spuVO.getSourceCountryCode(),spuVO.getVendorCode());
        // 获取新版销售属性信息  ZHAOYAN_SALE_ATTR
        spuConvertService.fillSaleAttributes(spuDetailVO);

        // 对sku根据销售属性进行排序
        spuConvertService.sortSkuList(spuDetailVO);

        return spuDetailVO;
    }

    /**
     * 获取SPU数量
     * @param spuQueryReqVO 查询条件对象
     * @return SPU数量
     */
    @Override
    public long getTotal(SpuQueryReqVO spuQueryReqVO) {
        return spuDraftAtomicService.getTotal(spuQueryReqVO);
    }

    /**
     * 设置列表引用
     * @param saveVo 保存SPU的VO对象
     * @param draftPo 草稿PO对象
     */
    private void setListRef(SaveSpuVO saveVo,SpuDraftPO draftPo){
        saveVo.setPcDescriptionMap(JSON.parseObject(draftPo.getPcDescription(), Map.class));
        saveVo.setAppDescriptionMap(JSON.parseObject(draftPo.getAppDescription(), Map.class));
        if(StringUtils.isNotBlank(draftPo.getSpuCertificate())){
            List<SpuCertificateVO> spuCertificateVOList = JSON.parseObject(draftPo.getSpuCertificate(),
                    new TypeReference<List<SpuCertificateVO>>() {});
            saveVo.setSpuCertificateVOList(spuCertificateVOList);
        }
        if(StringUtils.isNotBlank(draftPo.getSpuInterProperty())){
            List<GroupPropertyVO> spuInterProperties = JSON.parseObject(draftPo.getSpuInterProperty(),
                    new TypeReference<List<GroupPropertyVO>>() {});
            saveVo.setSpuInterPropertyList(spuInterProperties);
        }
        if(StringUtils.isNotBlank(draftPo.getGroupExtAttribute())){
            List<PropertyVO> storeExtends = ExtendPropertyGroupVO.convertToPropertyVOList(draftPo.getGroupExtAttribute());
            saveVo.setStoreExtendPropertyList(storeExtends);
            // 设置原始扩展属性json字符串
            if(saveVo.getSpuVO()!=null){
                saveVo.getSpuVO().setGroupExtAttribute(draftPo.getGroupExtAttribute());
            }
        }
    }

    /**
     * 根据 SkuDraftPO 列表构建 SkuVO 列表。
     * @param skuDraftPOS SkuDraftPO 列表，包含每个 SKU 的基本信息和扩展信息。
     * @return 构建后的 SkuVO 列表。
     */
    @PFTracing
    private List<SkuVO> buildSkuVOS(List<SkuDraftPO> skuDraftPOS,String lang, SpuDraftPO spuDraftPo){
        if(CollectionUtils.isEmpty(skuDraftPOS)){
            return Collections.emptyList();
        }
        if(StringUtils.isBlank(lang)){
            lang = LangContextHolder.get();
            if(StringUtils.isBlank(lang)) {
                lang = LangConstant.LANG_ZH;
            }
        }
        // 获取skuKey set，用来获取销售属性 ZHAOYAN_SALE_ATTR
        Set<String> skuKeySet = skuDraftPOS.stream().filter(Objects::nonNull).map(SkuDraftPO::getSkuKey).collect(Collectors.toSet());
        Map<String, List<PropertyValueVO>> saleAttributeDetailResult = saleAttributeManageService.getSkuDraftSaleAttributeDetailBySkuKeySet(skuKeySet, lang);
        List<SkuVO> skuVOS = new ArrayList<>();
        for(SkuDraftPO skuDraftPO : skuDraftPOS){
            SkuVO skuVO = JSON.parseObject(skuDraftPO.getSkuJsonInfo(), SkuVO.class);
            // 设置原始扩展属性json字符串
            skuVO.setGroupExtAttribute(skuDraftPO.getGroupExtAttribute());

            // 设置jdSkuId、jdSpuId、jdMainSkuId
            skuVO.setJdSkuId(skuDraftPO.getJdSkuId());
            skuVO.setJdSpuId(skuDraftPO.getJdSpuId());
            skuVO.setJdMainSkuId(skuDraftPO.getJdMainSkuId());

            // SpuDraftManageServiceImpl.buildSkuVOS 添加SKU扩展属性转换逻辑，解决MKU只有SPU扩展属性没有融合SKU扩展属性的问题
            if(StringUtils.isNotBlank(skuDraftPO.getGroupExtAttribute())){
                List<PropertyVO> skuStoreExtends = ExtendPropertyGroupVO.convertToPropertyVOList(skuDraftPO.getGroupExtAttribute());
                skuVO.setStoreExtendPropertyList(skuStoreExtends);
                log.info("SpuDraftManageServiceImpl.buildSkuVOS 设置SKU扩展属性，skuId:{}，groupExtAttribute:{}，storeExtendPropertyList size:{}", 
                    skuVO.getSkuId(), skuDraftPO.getGroupExtAttribute(), skuStoreExtends.size());
            }
            
            if(StringUtils.isNotBlank(skuDraftPO.getSkuCertificate())){
                List<GroupSkuCertificateVO> skuGroupCertificateVOList = JSON.parseObject(skuDraftPO.getSkuCertificate(),
                        new TypeReference<List<GroupSkuCertificateVO>>() {});
                skuVO.setSkuCertificateVOList(skuGroupCertificateVOList);
            }
            if(StringUtils.isNotBlank(skuDraftPO.getSkuInterProperty())){
                List<GroupPropertyVO> skuInterProperties = JSON.parseObject(skuDraftPO.getSkuInterProperty(),
                        new TypeReference<List<GroupPropertyVO>>() {});
                skuVO.setSkuInterPropertyList(skuInterProperties);
            }
            // 设置销售属性
            List<PropertyValueVO> storeExtends = saleAttributeDetailResult.get(skuDraftPO.getSkuKey());
            skuVO.setStoreSalePropertyList(storeExtends);
            if (StringUtils.isNotBlank(skuDraftPO.getSkuStockRelation())){
                List<SkuStockRelationVO> skyStockRelations = JSON.parseObject(skuDraftPO.getSkuStockRelation(),
                        new TypeReference<List<SkuStockRelationVO>>() {});
                this.fillWarehouseBindStatus(skyStockRelations);
                skuVO.setSkuStockRelationList(skyStockRelations);
            }
            skuVO.setSkuKey(skuDraftPO.getSkuKey());
            skuVOS.add(skuVO);
        }

        // 审核通过的商品 补充库存关联信息
        if (SpuAuditStatusEnum.APPROVED.getCode().equals(spuDraftPo.getAuditStatus())){
            SaveSpuVO saveVo = JSON.parseObject(spuDraftPo.getSpuJsonInfo(), SaveSpuVO.class);
            String attributeScope = saveVo.getSpuVO().getAttributeScope();
            log.info("SpuDraftManageServiceImpl.buildSkuVOS 补充审核通过的商品，SKU草稿库存信息 spuId={},attribute={}",saveVo.getSpuVO().getSpuId(),attributeScope);
            spuConvertService.fillApprovedSkuStockRelation(skuVOS,attributeScope);
        }
        return skuVOS;
    }

    private Long getBrandId(SpuDraftPO spuDraftPO){
        if(spuDraftPO == null){
            return null;
        }

        String spuJsonInfo = spuDraftPO.getSpuJsonInfo();
        SaveSpuVO saveVo = JSON.parseObject(spuJsonInfo, SaveSpuVO.class);
        if(saveVo == null || saveVo.getSpuVO() == null){
            return null;
        }
        return saveVo.getSpuVO().getBrandId();
    }

    @Override
    public boolean existSupplierSpuId(String supplierSpuId, String supplierCode,Long spuId) {
        return spuDraftAtomicService.existSupplierSpuId(supplierSpuId, supplierCode,spuId);
    }

    /**
     * 设置库存关系的绑定状态
     * @param skyStockRelations 需要设置绑定状态的库存关系列表
     */
    private void fillWarehouseBindStatus(List<SkuStockRelationVO> skyStockRelations) {
        if (CollectionUtils.isNotEmpty(skyStockRelations)) {
            skyStockRelations.forEach(skyStockRelationVO -> {
                 if (skyStockRelationVO.getWarehouseId() != null && !Constant.FACTORY_DEFAULT_ID_STR.equals(skyStockRelationVO.getWarehouseId()) && Objects.isNull(skyStockRelationVO.getBindStatus())) {
                     log.info("fillWarehouseBindStatus 草稿数据兼容备货仓绑定状态为空时，默认绑定 skuId={},warehouseId={} ",skyStockRelationVO.getSkuId(),skyStockRelationVO.getWarehouseId());
                     skyStockRelationVO.setBindStatus(YnEnum.YES.getCode());
                 }
            });
        }
    }
}
