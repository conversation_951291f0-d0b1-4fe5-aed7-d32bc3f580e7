package com.jdi.isc.product.soa.service.protocol.jsf.price;

import com.alibaba.fastjson.JSON;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.domain.enums.product.FulfillmentRateTypeEnum;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.InitAgreementPriceVO;
import com.jdi.isc.product.soa.domain.price.extendPrice.biz.CurrencyPriceVO;
import com.jdi.isc.product.soa.price.api.price.IscProductSoaPriceWriteApiService;
import com.jdi.isc.product.soa.price.api.price.req.FulfillmentRateChangeReqVO;
import com.jdi.isc.product.soa.price.api.price.req.PriceRefreshVO;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.extendPrice.CountryExtendPriceManageService;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant.SPU_AUDIT_LOCK_FAILED;

/**
 * 国际价格写服务
 * <AUTHOR>
 * @date 2025/3/4
 */
@Slf4j
@Service
public class IscProductSoaPriceWriteApiServiceImpl implements IscProductSoaPriceWriteApiService {

    @Resource
    private CountryAgreementPriceManageService countryAgreementPriceManageService;

    @Resource
    private CountryManageService countryManageService;

    @Resource
    private CountryExtendPriceManageService countryExtendPriceManageService;
    @Resource
    private SkuPriceManageService skuPriceManageService;
    @Resource
    private JimUtils jimUtils;
    @Resource
    private OperDuccConfig operDuccConfig;

    /** 触发跨境国家成本价重算*/
    @Override
    @ToolKit(exceptionWrap = true)
    @PFTracing
    public DataResponse<Boolean> refreshCnCostPrice(List<PriceRefreshVO> input) {
        for(PriceRefreshVO vo : input){
            InitAgreementPriceVO trigger = new InitAgreementPriceVO();
            Boolean res = false;
            try {
                Set<String> countryCodeSet = getTargetCountryCodes(vo);
                if(CollectionUtils.isEmpty(countryCodeSet)){
                    log.error("IscProductSoaPriceWriteApiServiceImpl.updateCostPrice fail vo:{}",JSON.toJSONString(vo));
                    continue;
                }
                trigger.setSourceCountryCode(vo.getSourceCountryCode());
                trigger.setTargetCountryCode(vo.getTargetCountryCode());
                trigger.setSkuId(vo.getSkuId());
                trigger.setIsWareHouseProduct(FulfillmentRateTypeEnum.DIRECT_DELIVERY.getCode());
                trigger.setDataStatusSource(vo.getDataStatusSource());
                res = countryAgreementPriceManageService.initAgreementPrice(trigger, null);
            }catch (Exception e){
                log.error("IscProductSoaPriceWriteApiServiceImpl.refreshCnCostPrice error trigger:{}",JSON.toJSONString(trigger),e);
            } finally {
                log.info("IscProductSoaPriceWriteApiServiceImpl.refreshCnCostPrice 触发跨境国家成本价重算 req:{} , res:{}" , JSON.toJSONString(trigger),res);
            }
        }
        return DataResponse.success(true);
    }

    /** 触发跨境入仓成本价重算*/
    @Override
    @ToolKit(exceptionWrap = true)
    @PFTracing
    public DataResponse<Boolean> refreshCnWarehouseCostPrice(List<PriceRefreshVO> input) {
        for(PriceRefreshVO vo : input){
            InitAgreementPriceVO trigger = new InitAgreementPriceVO();
            Boolean res = false;
            try {
                Set<String> countryCodeSet = getTargetCountryCodes(vo);
                if(CollectionUtils.isEmpty(countryCodeSet)){
                    log.error("IscProductSoaPriceWriteApiServiceImpl.updateCostPrice fail vo:{}",JSON.toJSONString(vo));
                    continue;
                }
                trigger.setSourceCountryCode(vo.getSourceCountryCode());
                trigger.setTargetCountryCode(vo.getTargetCountryCode());
                trigger.setSkuId(vo.getSkuId());
                trigger.setIsWareHouseProduct(1);
                trigger.setDataStatusSource(vo.getDataStatusSource());
                res = countryExtendPriceManageService.addWarehousingPrice(trigger);
            }finally {
                log.info("IscProductSoaPriceWriteApiServiceImpl.refreshCnCostPrice 触发跨境入仓成本价重算 req:{} , res:{}" , JSON.toJSONString(trigger),res);
            }
        }
        return DataResponse.success(true);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> updateCostPrice(PriceRefreshVO vo) {
        Set<String> countryCodeSet = getTargetCountryCodes(vo);
        if(CollectionUtils.isEmpty(countryCodeSet)){
            log.error("IscProductSoaPriceWriteApiServiceImpl.updateCostPrice fail vo:{}",JSON.toJSONString(vo));
            return DataResponse.error("");
        }
        // 更新国家成本价
        List<Boolean> booleanList = new ArrayList<>();
        for (String targetCountryCode : countryCodeSet){
            InitAgreementPriceVO initAgreementPriceVO = new InitAgreementPriceVO();
            initAgreementPriceVO.setSkuId(vo.getSkuId());
            initAgreementPriceVO.setSourceCountryCode(vo.getSourceCountryCode());
            initAgreementPriceVO.setTargetCountryCode(targetCountryCode);
            initAgreementPriceVO.setDataStatusSource(vo.getDataStatusSource());
            Boolean result = handlerUpdateCostPrice(initAgreementPriceVO);
            booleanList.add(result);
        }
        boolean costPriceResult = booleanList.stream().anyMatch(Boolean::new);
        return DataResponse.success(costPriceResult);
    }

    private Set<String> getTargetCountryCodes(PriceRefreshVO vo) {
        Set<String> countryCodeSet = new HashSet<>();
        Map<String, Integer> countryCodeMap = operDuccConfig.agreementPriceSwitch();
        String sourceCountry = vo.getSourceCountryCode();
        String targetCountry = vo.getTargetCountryCode();
        // 如果不是中国，直接返回源国家（需在配置map中存在）
        if (!CountryConstant.COUNTRY_ZH.equals(sourceCountry)) {
            if(countryCodeMap.containsKey(sourceCountry)){
                countryCodeSet.add(sourceCountry);
            }
            return countryCodeSet;
        }
        // 如果是中国且指定了目标国家
        if (targetCountry != null) {
            if(countryCodeMap.containsKey(sourceCountry)){
                countryCodeSet.add(targetCountry);
            }
            return countryCodeSet;
        }
        countryCodeSet.addAll(countryCodeMap.keySet());
        // 如果是中国且未指定目标国家，返回所有配置的国家
        return countryCodeSet;
    }

    private Boolean handlerUpdateCostPrice(InitAgreementPriceVO initAgreementPriceVO) {
        String requestId = UUID.randomUUID().toString();
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.AGREEMENT_PRICE_SKU_LOCK, initAgreementPriceVO.getTargetCountryCode(),String.valueOf(initAgreementPriceVO.getSkuId()));
        try {
            AssertValidation.isTrue(!jimUtils.simpleLock(lockKey, requestId, 60), SPU_AUDIT_LOCK_FAILED, "加锁失败");
            initAgreementPriceVO.setIsWareHouseProduct(FulfillmentRateTypeEnum.DIRECT_DELIVERY.getCode());
            CurrencyPriceVO currencyPriceVO = countryAgreementPriceManageService.calculateCountryCostPrice(initAgreementPriceVO);
            Boolean agreementResult = countryAgreementPriceManageService.initAgreementPrice(initAgreementPriceVO,currencyPriceVO);
            initAgreementPriceVO.setIsWareHouseProduct(FulfillmentRateTypeEnum.WAREHOUSE_PRODUCT.getCode());
            Boolean warehousingPriceResult = countryExtendPriceManageService.addWarehousingPrice(initAgreementPriceVO);
            return agreementResult;
        }catch (Exception e) {
            log.error("IscProductSoaPriceWriteApiServiceImpl.handlerUpdateCostPrice error initAgreementPriceVO={},err={}", JSON.toJSONString(initAgreementPriceVO), e.getMessage(), e);
            return false;
        } finally {
            jimUtils.simpleLockRelease(lockKey, requestId);
        }
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> triggerCostPriceRefresh(List<FulfillmentRateChangeReqVO> input) {
        return DataResponse.success(skuPriceManageService.triggerCostPriceRefresh(input));
    }

    /**
     * 更新京东价格
     * @param vo PriceRefreshVO 对象，包含需要更新的价格信息
     * @return DataResponse<Boolean> 对象，表示更新操作的结果
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> initJdPrice(PriceRefreshVO vo) {
        InitAgreementPriceVO initAgreementPriceVO = new InitAgreementPriceVO();
        initAgreementPriceVO.setSkuId(vo.getSkuId());
        initAgreementPriceVO.setSourceCountryCode(vo.getSourceCountryCode());
        initAgreementPriceVO.setTargetCountryCode(vo.getTargetCountryCode());
        Boolean jdPriceResult = countryExtendPriceManageService.addJdPrice(initAgreementPriceVO);
        return DataResponse.success(jdPriceResult);
    }

    @Override
    public DataResponse<Map<Long,Boolean>> updateCostPriceList(PriceRefreshVO input) {
        List<Long> skuIdList = input.getSkuIdList();
        Map<Long,Boolean> resultMap = new HashMap<>();
        if(Objects.nonNull(skuIdList) && CollectionUtils.isNotEmpty(skuIdList)){
            for (Long skuId : skuIdList){
                PriceRefreshVO vo = new PriceRefreshVO();
                vo.setSourceCountryCode(input.getSourceCountryCode());
                vo.setTargetCountryCode(input.getTargetCountryCode());
                vo.setIsWareHouseProduct(input.getIsWareHouseProduct());
                vo.setSkuId(skuId);
                vo.setDataStatusSource(input.getDataStatusSource());
                DataResponse<Boolean> response = updateCostPrice(vo);
                resultMap.put(skuId,response.getSuccess());
            }
        }
        return DataResponse.success(resultMap);
    }

}
