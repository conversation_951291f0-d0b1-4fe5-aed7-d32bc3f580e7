package com.jdi.isc.product.soa.service.manage.customerSku;

import com.jdi.isc.product.soa.domain.price.biz.MkuPriceAvailableVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Description: sku客制化价格草稿明细表数据维护服务
 * @Author: zhaokun51
 * @Date: 2025/02/27 14:53
 **/

public interface CustomerSkuPriceDetailManageService {

    /**
     * 根据客户代码、商品 SKU ID 列表和 MKU 固定 SKU 映射，获取可用价格的 MKU 列表。
     * @param clientCode 客户代码
     * @param skuIds 商品 SKU ID 列表
     * @param mkuFixedSkuMap MKU 固定 SKU 映射
     * @return 可用价格的 MKU 列表
     */
    List<MkuPriceAvailableVO> listMkuPriceAvailableStatus(String clientCode, Collection<Long> skuIds, Map<Long, Long> mkuFixedSkuMap);

}
