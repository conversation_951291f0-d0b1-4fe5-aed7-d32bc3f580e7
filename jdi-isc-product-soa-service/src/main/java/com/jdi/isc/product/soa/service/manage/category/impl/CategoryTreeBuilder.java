package com.jdi.isc.product.soa.service.manage.category.impl;

import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.domain.category.biz.*;
import com.jdi.isc.product.soa.domain.enums.CategoryLevelEnum;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 构建类目树形结构
 * @Date 2024/7/18 11:04 上午
 */
@Service
public class CategoryTreeBuilder {

    @Resource
    private CategoryOutService categoryOutService;

    public List<CategoryTreeVo>  getSelectCategory(List<CategoryTreeVo> categoryMap){
        List<CategoryTreeVo> result = new ArrayList<>();
        categoryMap.forEach(c -> {
            doSelectCategory(result,c);
        });
        return result;
    }
    private void  doSelectCategory(List<CategoryTreeVo> result,CategoryTreeVo category){
        if (category == null) {
            return;
        }
        if (category.isSelected()) {
            category.setChild(null);
            category.setChildIds(null);
            result.add(category);
        }else{
            if (CollectionUtils.isNotEmpty(category.getChild())) {
                category.getChild().forEach(c -> {
                    doSelectCategory(result,c);
                });
            }
        }
    }




    /**
     * 将平铺的类目结构转换为树形结构
     */
    public List<CategoryTreeVo> buildCategoryTree(Map<Long, List<CategoryComboBoxVO>> categoryMap) {
        Map<Long, CategoryTreeVo> firstCate = new HashMap<>();
        Map<Long, CategoryTreeVo> twoCate = new HashMap<>();
        Map<Long, CategoryTreeVo> threeCate = new HashMap<>();
        Map<Long, CategoryTreeVo> fourCate = new HashMap<>();
        categoryMap.forEach((k, cate) -> {
            cate = cate.stream().sorted(Comparator.comparingInt(CategoryComboBoxVO::getLevel)).collect(Collectors.toList());
            // 添加到对应的层级map
            CategoryTreeVo parent = null;
            for (CategoryComboBoxVO comboVo : cate) {
                CategoryTreeVo categoryTreeVo = addCategory(comboVo, firstCate, twoCate, threeCate, fourCate);
                if (parent != null) {
                    if (!parent.getChildIds().contains(categoryTreeVo.getCatId())) {
                        parent.getChild().add(categoryTreeVo);
                        parent.getChildIds().add(categoryTreeVo.getCatId());
                    }
                }
                parent = categoryTreeVo;
            }
        });
        //比较节点数判断是否全选。

        return new ArrayList<>(firstCate.values());
    }

    /**
     * 将boxVO添加到类目树中
     * 1：如果boxVO对应的级别类目不存在，则创建类目节点并返回该对象。
     * 2：如果对应的节点存在，在返回已存在类目节点，保证同一类目只存在一份。
     */
    private CategoryTreeVo addCategory(CategoryComboBoxVO boxVO, Map<Long, CategoryTreeVo> firstCate, Map<Long, CategoryTreeVo> twoCate, Map<Long, CategoryTreeVo> threeCate, Map<Long, CategoryTreeVo> fourCate) {
        CategoryTreeVo categoryNew = buildCategoryVo(boxVO);
        if (CategoryLevelEnum.ONE.getCode().equals(boxVO.getLevel())) {
            if (!firstCate.containsKey(boxVO.getId())) {
                firstCate.put(boxVO.getId(), categoryNew);
            } else {
                return firstCate.get(boxVO.getId());
            }
        }

        if (CategoryLevelEnum.TWO.getCode().equals(boxVO.getLevel())) {
            if (!twoCate.containsKey(boxVO.getId())) {
                twoCate.put(boxVO.getId(), categoryNew);
            } else {
                return twoCate.get(boxVO.getId());
            }
        }

        if (CategoryLevelEnum.THREE.getCode().equals(boxVO.getLevel())) {
            if (!threeCate.containsKey(boxVO.getId())) {
                threeCate.put(boxVO.getId(), categoryNew);
            } else {
                return threeCate.get(boxVO.getId());
            }
        }
        if (CategoryLevelEnum.FOUR.getCode().equals(boxVO.getLevel())) {
            if (!fourCate.containsKey(boxVO.getId())) {
                fourCate.put(boxVO.getId(), categoryNew);
            } else {
                return fourCate.get(boxVO.getId());
            }
        }
        return categoryNew;

    }



    /**
     * 构建类目VO
     */
    private CategoryTreeVo buildCategoryVo(CategoryComboBoxVO boxVO) {
        CategoryTreeVo categoryTreeVo = new CategoryTreeVo();
        categoryTreeVo.setCatId(boxVO.getId());
        categoryTreeVo.setCatName(boxVO.getName());
        categoryTreeVo.setLevel(boxVO.getLevel());
        categoryTreeVo.setChild(new ArrayList<>());
        categoryTreeVo.setChildIds(new HashSet<>());
        return categoryTreeVo;
    }



    /**
     * 补充同一级类目缺少的类目，并标注选中的类目
     */
    public List<CategoryTreeVo> getCategoryChildrenFullNode(List<CategoryTreeVo> categoryMap) {
        categoryMap.forEach(this::processCategoryNode);
        return categoryMap;
    }

    private void processCategoryNode(CategoryTreeVo category) {
        if (CategoryLevelEnum.ONE.getCode().equals(category.getLevel())) {
            processLevelOneCategory(category);
        }
    }

    /**
     * 处理一级类目
     * 1：判断一级类目是否需要选中
     * 2：在非选中场景下补充二级类目的兄弟节点
     */
    private void processLevelOneCategory(CategoryTreeVo category) {
        if (CollectionUtils.isNotEmpty(category.getChild())) {
            Long catId = category.getCatId();
            List<CategoryComboBoxVO> allSecCategory = queryChildByParentId(catId);

            category.getChild().forEach(sec -> processLevelTwoCategory(sec, allSecCategory));

            long selectedCount = category.getChild().stream().filter(CategoryTreeVo::isSelected).count();
            if (allSecCategory.size() == selectedCount) {
                category.setSelected(true);
                category.getChild().forEach(s -> s.setSelected(false));
            } else {
                fillMissingCategories(category, allSecCategory, category.getChildIds());
            }
        }
    }

    /**
     * 处理二级类目
     * 1：判断三级类目是否需要选中
     * 2：在非选中场景下补充三级类目的兄弟节点
     */
    private void processLevelTwoCategory(CategoryTreeVo sec, List<CategoryComboBoxVO> allSecCategory) {
        if (CollectionUtils.isNotEmpty(sec.getChild())) {
            Set<Long> childIds = sec.getChildIds();
            List<CategoryComboBoxVO> allThirdCategory = queryChildByParentId(sec.getCatId());
            Map<Long, List<CategoryComboBoxVO>> fourCategory = new HashMap<>();
            childIds.forEach(c -> {
                List<CategoryComboBoxVO> categoryComboBoxVos = queryChildByParentId(c);
                fourCategory.put(c, categoryComboBoxVos);
            });

            sec.getChild().forEach(third -> processLevelThreeCategory(third, fourCategory));

            long selectedCount = sec.getChild().stream().filter(CategoryTreeVo::isSelected).count();
            if (allThirdCategory.size() == selectedCount) {
                sec.setSelected(true);
                sec.getChild().forEach(s -> s.setSelected(false));
            } else {
                fillMissingCategories(sec, allThirdCategory, childIds);
            }
        }
    }

    /**
     * 处理三级类目
     * 1：判断四级类目是否需要选中
     * 2：在非选中场景下补充四级类目的兄弟节点
     */
    private void processLevelThreeCategory(CategoryTreeVo third, Map<Long, List<CategoryComboBoxVO>> listMap) {
        List<CategoryComboBoxVO> categoryPOS = listMap.get(third.getCatId());
        if (third.getChildIds().size() == categoryPOS.size()) {
            third.setSelected(true);
            return;
        }
        third.getChild().forEach(t -> t.setSelected(true));
        fillMissingCategories(third,categoryPOS,third.getChildIds());
    }

    /**
     * 填充缺失的类目
     */
    private void fillMissingCategories(CategoryTreeVo category, List<CategoryComboBoxVO> allCategories, Set<Long> existingChildIds) {
        List<CategoryTreeVo> missingCategories = allCategories.stream()
                .filter(t -> !existingChildIds.contains(t.getId()))
                .map(t -> {
                    CategoryTreeVo treeDTO = new CategoryTreeVo();
                    treeDTO.setCatId(t.getId());
                    treeDTO.setLevel(t.getLevel());
                    treeDTO.setCatName(t.getName());
                    return treeDTO;
                }).collect(Collectors.toList());
        category.getChild().addAll(missingCategories);
    }

    private List<CategoryComboBoxVO> queryChildByParentId(Long cateId) {
        CategoryComboBoxReqVO boxReqVO = new CategoryComboBoxReqVO();
        boxReqVO.setId(cateId);
        boxReqVO.setLang(LangConstant.LANG_ZH);
        return categoryOutService.queryChildren(boxReqVO);

    }


    public Set<Long> buildFourCategoryIds(List<CategoryTreeVo> categoryNodes) {
        Set<Long> saveCateIds = new HashSet<>();
        //待查询四级节点的类目
        Map<Integer, List<CategoryTreeVo>> cateLevelMap = categoryNodes.stream().collect(Collectors.groupingBy(CategoryTreeVo::getLevel));
        //查询末级id
        cateLevelMap.forEach((k,v) -> {
            if (CategoryLevelEnum.FOUR.getCode().equals(k)) {
                saveCateIds.addAll(v.stream().map(CategoryTreeVo::getCatId).collect(Collectors.toSet()));
                return;
            }
            // 查询末级节点
            Set<Long> catIds = v.stream().filter(Objects::nonNull).map(CategoryTreeVo::getCatId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(catIds)) {
                return;
            }
            saveCateIds.addAll(categoryOutService.categoryLevel4From1234Set(catIds));
        });
        return saveCateIds;
    }
}
