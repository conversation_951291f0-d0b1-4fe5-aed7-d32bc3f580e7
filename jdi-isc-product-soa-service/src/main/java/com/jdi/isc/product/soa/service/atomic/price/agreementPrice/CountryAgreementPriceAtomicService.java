package com.jdi.isc.product.soa.service.atomic.price.agreementPrice;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPricePageReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPricePageVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.repository.mapper.price.agreementPrice.CountryAgreementPriceBaseMapper;
import com.jdi.isc.product.soa.service.support.helper.BasicPOHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 国家协议价原子服务
 * @Author: wangpeng965
 * @Date: 2025/03/03 16:45
 **/
@Slf4j
@Service
public class CountryAgreementPriceAtomicService extends ServiceImpl<CountryAgreementPriceBaseMapper, CountryAgreementPricePO> {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    public List<CountryAgreementPricePageVO> pageSearch(CountryAgreementPricePageReqVO input){
        input.setOffset((input.getIndex()-1)*input.getSize());
        return super.getBaseMapper().pageSearch(input);
    }

    public List<CountryAgreementPricePageVO> pageSearchByIdList(List<Long> idList){
        return super.getBaseMapper().pageSearchByIdList( idList);
    }

    /**
     * 分页查询的列表总数
     * @param input 查询条件
     * @return 总条数
     */
    public long pageSearchTotal(CountryAgreementPricePageReqVO input){
        return super.getBaseMapper().pageSearchTotal(input);
    }

    /**
     * 未删除的对象
     * @param id id
     * @return 对象
     */
    public CountryAgreementPricePO getValidById(Long id){
        LambdaQueryWrapper<CountryAgreementPricePO> wrapper = Wrappers.<CountryAgreementPricePO>lambdaQuery()
                .eq(CountryAgreementPricePO::getId, id)
                .eq(CountryAgreementPricePO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    public List<CountryAgreementPricePO> listValidByIds(Collection<Long> ids){
        LambdaQueryWrapper<CountryAgreementPricePO> wrapper = Wrappers.<CountryAgreementPricePO>lambdaQuery()
                .in(CountryAgreementPricePO::getId, ids)
                .eq(CountryAgreementPricePO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    @PFTracing
    public CountryAgreementPricePO getCountryAgreementPrice(CountryAgreementPriceReqVO reqVO){
        LambdaQueryWrapper<CountryAgreementPricePO> wrapper = new LambdaQueryWrapper<CountryAgreementPricePO>()
            .eq(Objects.nonNull(reqVO.getSkuId()),CountryAgreementPricePO::getSkuId,reqVO.getSkuId())
            .eq(Objects.nonNull(reqVO.getBrandId()),CountryAgreementPricePO::getBrandId,reqVO.getBrandId())
            .in(CollectionUtils.isNotEmpty(reqVO.getCatIds()),CountryAgreementPricePO::getJdCatId,reqVO.getCatIds())
            .eq(StringUtils.isNotBlank(reqVO.getTargetCountryCode()),CountryAgreementPricePO::getTargetCountryCode,reqVO.getTargetCountryCode())
            .eq(StringUtils.isNotBlank(reqVO.getSourceCountryCode()), CountryAgreementPricePO::getSourceCountryCode,reqVO.getSourceCountryCode())
            .eq(CountryAgreementPricePO::getYn,YnEnum.YES.getCode())
            ;
        List<CountryAgreementPricePO> pricePOList = super.getBaseMapper().selectList(wrapper);
        if(CollectionUtils.isNotEmpty(pricePOList)){
            return pricePOList.get(0);
        }
        return null;
    }

    /**
     * 根据条件查询国家协议价格列表。
     * @param reqVO 查询条件对象，包含skuId、brandId、skuIds、catIds、targetCountryCode和sourceCountryCode等字段。
     * @return 符合条件的国家协议价格PO对象列表。
     */
    public List<CountryAgreementPricePO> getCountryAgreementPriceList(CountryAgreementPriceReqVO reqVO){
        LambdaQueryWrapper<CountryAgreementPricePO> wrapper = new LambdaQueryWrapper<CountryAgreementPricePO>()
            .eq(Objects.nonNull(reqVO.getSkuId()),CountryAgreementPricePO::getSkuId,reqVO.getSkuId())
            .eq(Objects.nonNull(reqVO.getBrandId()),CountryAgreementPricePO::getBrandId,reqVO.getBrandId())
            .in(CollectionUtils.isNotEmpty(reqVO.getSkuIds()),CountryAgreementPricePO::getSkuId,reqVO.getSkuIds())
            .in(CollectionUtils.isNotEmpty(reqVO.getCatIds()),CountryAgreementPricePO::getJdCatId,reqVO.getCatIds())
            .eq(StringUtils.isNotBlank(reqVO.getTargetCountryCode()),CountryAgreementPricePO::getTargetCountryCode,reqVO.getTargetCountryCode())
            .eq(StringUtils.isNotBlank(reqVO.getSourceCountryCode()), CountryAgreementPricePO::getSourceCountryCode,reqVO.getSourceCountryCode())
            .eq(CountryAgreementPricePO::getYn,YnEnum.YES.getCode())
            ;
        List<CountryAgreementPricePO> pricePOList = super.getBaseMapper().selectList(wrapper);
        if(CollectionUtils.isEmpty(pricePOList)){
            return new ArrayList<>();
        }
        return pricePOList;
    }

    public List<CountryAgreementPricePO> getCountryAgreementPriceList(List<Long> skuIdList,String targetCountryCode){
        LambdaQueryWrapper<CountryAgreementPricePO> wrapper = new LambdaQueryWrapper<CountryAgreementPricePO>()
            .in(CollectionUtils.isNotEmpty(skuIdList),CountryAgreementPricePO::getSkuId,skuIdList)
            .eq(StringUtils.isNotBlank(targetCountryCode),CountryAgreementPricePO::getTargetCountryCode,targetCountryCode)
            .eq(CountryAgreementPricePO::getYn,YnEnum.YES.getCode())
            ;
        List<CountryAgreementPricePO> pricePOList = super.getBaseMapper().selectList(wrapper);
        if(CollectionUtils.isEmpty(pricePOList)){
            return new ArrayList<>();
        }
        return pricePOList;
    }

    public CountryAgreementPricePO getValidBySkuIdAndTargetCountryCode(Long skuId,String targetCountryCode){
        CountryAgreementPricePO countryAgreementPrice = this.getBySkuIdAndTargetCountryCode(skuId, targetCountryCode);
        if (countryAgreementPrice == null) {
            log.warn("不存在的协议价. sku={}, targetCountryCode={}", skuId, targetCountryCode);
            throw new ProductBizException("不存在的协议价. %s-%s", skuId, targetCountryCode);
        }

        return countryAgreementPrice;
    }

    public CountryAgreementPricePO getBySkuIdAndTargetCountryCode(Long skuId,String targetCountryCode){
        if (skuId == null || targetCountryCode == null) {
            return null;
        }

        List<CountryAgreementPricePO> list = this.getCountryAgreementPriceList(Lists.newArrayList(skuId), targetCountryCode);

        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public void forceUpdateCountryCostPriceIsNull(Long id, String updater) {
        if (id != null) {
            log.info("forceUpdateCountryCostPriceIsNull, id: {}, updater: {}", id, updater);
            int count = super.getBaseMapper().forceUpdateCountryCostPriceIsNull(id, updater, System.currentTimeMillis());
            if (count < 1) {
                log.error("forceUpdateCountryCostPriceIsNull, id: {}, updater: {}, count: {}", id, updater, count);
                throw new ProductBizException("更新成本价失败. id=%s", id);
            }
        }
    }

    /**
     * 不可售阈值只能越来越小
     */
    public void updateUnsellableThreshold(CountryAgreementPricePO update, boolean optimisticLock) {
        Preconditions.checkArgument(update != null && update.getId() != null, "id不能为空");
        Preconditions.checkArgument(update.getUnsellableThreshold() != null, "不可售阈值不能为空");

        // 设置更新人和更新时间，MyMetaObjectHandler 此时失效，手动设置更新人和更新时间
        BasicPOHelper.setUpdateInfo(update);

        LambdaUpdateWrapper<CountryAgreementPricePO> updateWrapper = Wrappers.lambdaUpdate(CountryAgreementPricePO.class)
                .set(CountryAgreementPricePO::getUnsellableThreshold, update.getUnsellableThreshold())
                .set(CountryAgreementPricePO::getUnsellableThresholdTime, new Date().getTime())
                .set(CountryAgreementPricePO::getUpdater, update.getUpdater())
                .set(CountryAgreementPricePO::getUpdateTime, update.getUpdateTime())

                .eq(CountryAgreementPricePO::getId, update.getId())
                .eq(CountryAgreementPricePO::getYn, YnEnum.YES.getCode());

        if (optimisticLock) {
            updateWrapper.ge(CountryAgreementPricePO::getUnsellableThreshold, update.getUnsellableThreshold());
        }

        boolean execute = super.update(updateWrapper);

        if (!execute) {
            log.warn("不可售阈值发生变化，更新不可售阈值失败. update={}", JSONObject.toJSONString(update));
            throw new ProductBizException("不可售阈值发生变化，更新不可售阈值失败. id=%s", update.getId());
        }
    }

    /**
     * 更新不可售阈值
     * @param id 客制化价格明细ID
     * @param unsellableThreshold 新的不可售阈值
     */
    public void updateUnsellableThreshold(Long id, BigDecimal unsellableThreshold) {
        CountryAgreementPricePO countryAgreementPricePO = this.getValidById(id);

        if (countryAgreementPricePO == null) {
            log.error("更新不可售阈值，找不到协议价. id={}", id);
            return;
        }

        CountryAgreementPricePO update = new CountryAgreementPricePO();

        update.setUnsellableThreshold(unsellableThreshold);
        update.setId(id);

        this.updateUnsellableThreshold(update, false);
    }

    /**
     * 更新可售状态列表
     * @param updateList 需要更新的国家协议价格列表，列表为空时方法直接返回
     */
    public void updateAvailableSaleStatusList(List<CountryAgreementPricePO> updateList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(updateList)) {
            return;
        }

        // 排序
        CollectionUtil.sort(updateList, Comparator.comparing(CountryAgreementPricePO::getId, Comparator.nullsLast(Long::compareTo)));

        for (CountryAgreementPricePO item : updateList) {
            this.updateAvailableSaleStatus(item);
        }
    }

    /**
     * 更新国家协议价格的可用销售状态
     * @param update 包含更新信息的国家协议价格对象，其中id不能为空
     */
    public void updateAvailableSaleStatus(CountryAgreementPricePO update) {
        Preconditions.checkArgument(update.getId() != null, "id不能为空");

        LambdaUpdateWrapper<CountryAgreementPricePO> updateWrapper = Wrappers.lambdaUpdate(CountryAgreementPricePO.class)
                .set(CountryAgreementPricePO::getAvailableSaleStatus, update.getAvailableSaleStatus())
                .set(CountryAgreementPricePO::getAvailableSaleStatusTime, new Date().getTime())
                .set(CountryAgreementPricePO::getUpdateTime, new Date().getTime())
                .set(CountryAgreementPricePO::getUpdater, update.getUpdater())

                .eq(CountryAgreementPricePO::getId, update.getId())
                .eq(CountryAgreementPricePO::getYn, YnEnum.YES.getCode())
                .eq(CountryAgreementPricePO::getUpdateTime, update.getUpdateTime());

        boolean flag = super.update(updateWrapper);

        log.info("更新协议价可售状态. result=[{}], update={}", flag, JSONObject.toJSONString(update));

        if (!flag) {
            log.info("更新协议价可售状态失败, update={}", JSONObject.toJSONString(update));
            throw new ProductBizException("客制化价格正在进行其他业务操作，请稍后重试 %s", update.getSkuId());
        }
    }


    public CountryAgreementPricePO getMapByBizNo(String bizNo) {
        if (StringUtils.isNotEmpty(bizNo)) {

            Map<String, CountryAgreementPricePO> tempMap = this.getMapByBizNos(Lists.newArrayList(bizNo));

            return tempMap.get(bizNo);
        }
        log.warn("找不到协议价. bizNo={}", bizNo);
        return null;
    }

    public Map<String, CountryAgreementPricePO> getMapByBizNos(List<String> bizNos) {
        if (CollectionUtils.isEmpty(bizNos)) {
            return Maps.newHashMap();
        }

        LambdaQueryWrapper<CountryAgreementPricePO> wrapper = new LambdaQueryWrapper<CountryAgreementPricePO>()
                .in(CountryAgreementPricePO::getBizNo, bizNos)
                .eq(CountryAgreementPricePO::getYn,YnEnum.YES.getCode());

        List<CountryAgreementPricePO> list = super.getBaseMapper().selectList(wrapper);

        return list.stream().filter(item -> StringUtils.isNotEmpty(item.getBizNo())).collect(Collectors.toMap(CountryAgreementPricePO::getBizNo, Function.identity(), (a, b) -> a));
    }


    public Set<Long> getAllIds() {
        // 查询次数，最大查询10000次，即最大返回50万数据
        int count = 0;
        // 一页最大查询sku数量
        int size = 500;
        // 起始查询的skuId，开区间
        Long minId = 0L;

        Set<Long> result = Sets.newHashSet();

        while (count < 1000) {
            List<Long> ids = getAllIds(minId, size);

            if (CollectionUtils.isNotEmpty(ids)) {
                result.addAll(ids);
            }

            if (ids.size() < size) {
                break;
            }

            minId = ids.get(ids.size() - 1);
            count++;
        }

        return result;
    }

    public List<Long> getAllIds(Long minId, Integer size) {
        if (minId == null) {
            minId = 0L;
        }

        if (size == null) {
            size = 500;
        }

        LambdaQueryWrapper<CountryAgreementPricePO> queryWrapper = new LambdaQueryWrapper<CountryAgreementPricePO>()
                .select(CountryAgreementPricePO::getId)
                .gt(CountryAgreementPricePO::getId, minId)
                .eq(CountryAgreementPricePO::getYn, YnEnum.YES.getCode())
                .orderByAsc(CountryAgreementPricePO::getId)
                .last("LIMIT " + size)
                ;


        List<CountryAgreementPricePO> skus = super.getBaseMapper().selectList(queryWrapper);

        return skus.stream().map(CountryAgreementPricePO::getId).collect(Collectors.toList());
    }

    public void updatePoolStatus(List<CountryAgreementPricePO> updateList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(updateList)) {
            return;
        }

        for (CountryAgreementPricePO update : updateList) {
            LambdaUpdateWrapper<CountryAgreementPricePO> updateWrapper = Wrappers.lambdaUpdate(CountryAgreementPricePO.class)
                    .set(CountryAgreementPricePO::getCustomerMkuPoolStatus, update.getCustomerMkuPoolStatus())
                    .set(CountryAgreementPricePO::getCountryMkuPoolStatus, update.getCountryMkuPoolStatus())

                    .eq(CountryAgreementPricePO::getId, update.getId())
                    .eq(CountryAgreementPricePO::getYn, YnEnum.YES.getCode());
            boolean result = super.update(updateWrapper);
            log.info("updatePoolStatus, result={}", result);
        }
    }
}
