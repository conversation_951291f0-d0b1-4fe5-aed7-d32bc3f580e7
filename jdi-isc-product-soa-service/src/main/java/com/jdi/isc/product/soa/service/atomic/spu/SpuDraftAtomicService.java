package com.jdi.isc.product.soa.service.atomic.spu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.domain.spu.biz.SpuQueryReqVO;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.repository.mapper.spu.SpuDraftBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【jdi_isc_spu_draft_sharding(spu草稿表)】的数据库操作Service实现
 * @createDate 2023-11-25 15:39:30
 */
@Slf4j
@Service
public class SpuDraftAtomicService extends ServiceImpl<SpuDraftBaseMapper, SpuDraftPO> {

    private static final String SPU_VENDOR_SPU_ID = "\"vendorSpuId\":\"";

    public SpuDraftPO getOneBySpuId(Long spuId) {
        return getDraftPO(spuId, YnEnum.YES);
    }


    /**
     * 更新spu草稿等级
     *
     * @param spuDraftPo 草稿信息
     * @return 更新结果
     */
    public boolean updateDraft(SpuDraftPO spuDraftPo) {
        int update = super.getBaseMapper().updateById(spuDraftPo);
        return BooleanUtils.toBoolean(update);
    }

    /**
     * 查询无状态的草稿
     *
     * @param spuId 商品ID
     * @return 草稿数据
     */
    public SpuDraftPO getBySpuId(Long spuId) {
        LambdaQueryWrapper<SpuDraftPO> queryWrapper = Wrappers.lambdaQuery(SpuDraftPO.class).eq(SpuDraftPO::getSpuId, spuId).orderByDesc(SpuDraftPO::getUpdateTime);
        List<SpuDraftPO> spuDraftPOList = super.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(spuDraftPOList)) {
            return spuDraftPOList.get(0);
        }
        return null;
    }

    private SpuDraftPO getDraftPO(Long spuId, YnEnum ynEnum) {
        return getOne(new LambdaQueryWrapper<SpuDraftPO>()
                .eq(SpuDraftPO::getSpuId, spuId).eq(Objects.nonNull(ynEnum), SpuDraftPO::getYn, ynEnum.getCode()));
    }

    /**
     * 批量查询SpuDraftPO列表
     * @param spuIds 包含SpuId的集合
     * @return 返回SpuDraftPO列表
     */
    @PFTracing
    public List<SpuDraftPO> batchQuerySpuDraftPoList(Set<Long> spuIds) {
        LambdaQueryWrapper<SpuDraftPO> queryWrapper = Wrappers.lambdaQuery(SpuDraftPO.class).in(SpuDraftPO::getSpuId, spuIds).orderByDesc(SpuDraftPO::getUpdateTime);

        List<SpuDraftPO> spuDraftPOList = super.list(queryWrapper);
        return Optional.ofNullable(spuDraftPOList).orElseGet(Collections::emptyList);
    }

    @PFTracing
    public List<SpuDraftPO> batchQueryValidSpuDraftPoList(Set<Long> spuIds) {
        LambdaQueryWrapper<SpuDraftPO> queryWrapper = Wrappers.lambdaQuery(SpuDraftPO.class).in(SpuDraftPO::getSpuId, spuIds).eq(SpuDraftPO::getYn, YnEnum.YES.getCode()).orderByDesc(SpuDraftPO::getUpdateTime);

        List<SpuDraftPO> spuDraftPOList = super.list(queryWrapper);
        return Optional.ofNullable(spuDraftPOList).orElseGet(Collections::emptyList);
    }

    public Map<Long, SpuDraftPO> queryValidSpuDraftMap(Set<Long> spuIds) {
        List<SpuDraftPO> spuDraftPOList = batchQueryValidSpuDraftPoList(spuIds);
        return Optional.ofNullable(spuDraftPOList)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(SpuDraftPO::getSpuId, Function.identity(),(o1,o2) -> o1));
    }


    /**
     * 获取符合条件的总数
     * @param reqVO 查询条件对象
     * @return 符合条件的记录总数
     */
    public long getTotal(SpuQueryReqVO reqVO){
        if (StringUtils.isNotBlank(reqVO.getSpuName())){
            reqVO.setSpuName(reqVO.getSpuName().trim());
        }
        return super.getBaseMapper().getTotal(reqVO);
    }

    /**
     * 列表查询
     *
     * @param reqVO
     * @return
     */
    public List<SpuDraftPO> listSearch(SpuQueryReqVO reqVO) {
        // 计算偏移量
        reqVO.setOffset((reqVO.getIndex() - 1) * reqVO.getSize());
        if (StringUtils.isNotBlank(reqVO.getSpuName())){
            reqVO.setSpuName(reqVO.getSpuName().trim());
        }
        return super.getBaseMapper().listSearch(reqVO);
    }

    /**
     * 根据给定的 spuIds 集合查询对应的 SpuDraftPO 对象，并将结果以 Map 形式返回，key 为 spuId，value 为 SpuDraftPO 对象。
     * @param spuIds 要查询的 spuId 集合
     * @return 包含查询结果的 Map 对象，key 为 spuId，value 为 SpuDraftPO 对象；如果没有找到匹配的 SpuDraftPO 对象，则返回一个空的 Map 对象。
     */
    public Map<Long, SpuDraftPO> querySpuDraftMap(Set<Long> spuIds) {
        List<SpuDraftPO> spuDraftPOList = batchQuerySpuDraftPoList(spuIds);
        return Optional.ofNullable(spuDraftPOList)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(SpuDraftPO::getSpuId, Function.identity(),(o1,o2) -> o1));
    }

    public List<SpuDraftPO> querySpuList(Collection<Long> spuIds){
        return super.getBaseMapper().selectList(new LambdaQueryWrapper<SpuDraftPO>().in(SpuDraftPO::getSpuId, spuIds).eq(SpuDraftPO::getYn, YnEnum.YES.getCode()));
    }

    /**
     * 分页获取全部SpuDraftPO数据（仅获取有效数据）
     *
     * @param index 页码，从1开始
     * @param size 每页大小
     * @return SpuDraftPO列表
     */
    public List<SpuDraftPO> listAllSpuDraftWithPage(String updateTime,Long index, Long size) {
        // 参数校验
        if (index == null || index < 1) {
            throw new IllegalArgumentException("页码必须大于等于1");
        }
        if (size == null || size < 1 || size > 1000) {
            throw new IllegalArgumentException("页大小必须在1-1000之间");
        }

        // 计算偏移量
        Long offset = (index - 1) * size;

        return super.getBaseMapper().listAllSpuDraftWithPage(updateTime, offset, size);
    }

    /**
     * 获取全部有效SpuDraftPO数据的总数
     *
     * @return 总记录数
     */
    public long listAllSpuDraftTotal(String updateTime) {
        return super.getBaseMapper().listAllSpuDraftTotal(updateTime);
    }

    public List<SpuDraftPO> listBySourceCountryCodeAndJdCatId(String sourceCountryCode, Long jdCatId) {
        if (StringUtils.isEmpty(sourceCountryCode) || jdCatId == null) {
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<SpuDraftPO> wrapper = Wrappers.<SpuDraftPO>lambdaQuery()
                .select(SpuDraftPO::getId, SpuDraftPO::getSpuId, SpuDraftPO::getSpuJsonInfo, SpuDraftPO::getUpdateTime)
                .eq(SpuDraftPO::getSourceCountryCode, sourceCountryCode)
                .eq(SpuDraftPO::getJdCatId, jdCatId)
                .eq(SpuDraftPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    public void updateBuyer(List<SpuDraftPO> spuDrafts, String buyer, String updater) {
        if (CollectionUtils.isEmpty(spuDrafts)) {
            return;
        }
        // 更新
        for (SpuDraftPO spuDraft : spuDrafts) {
            log.info("更新草稿中的采销信息. spuId={}, buyer={}, updater={}", spuDraft.getSpuId(), buyer, updater);
            String spuJsonInfo = spuDraft.getSpuJsonInfo();

            JSONObject spuJsonInfoObject = JSONObject.parseObject(spuJsonInfo);
            if (spuJsonInfoObject == null) {
                log.info("更新草稿中的采销信息. spuJsonInfo is null, spuId={}, buyer={}, updater={}", spuDraft.getSpuId(), buyer, updater);
                continue;
            }
            JSONObject spuVO = spuJsonInfoObject.getJSONObject("spuVO");

            if (spuVO == null) {
                log.info("更新草稿中的采销信息. spuVO is null, spuId={}, buyer={}, updater={}", spuDraft.getSpuId(), buyer, updater);
                continue;
            }

            String dbBuyer = spuVO.getString("buyer");

            if (StringUtils.equals(dbBuyer, buyer)) {
                log.info("更新草稿中的采销信息. buyer 相同无需更新, spuId={}, buyer={}, updater={}", spuDraft.getSpuId(), buyer, updater);
                continue;
            }

            spuVO.put("buyer", buyer);

            LambdaUpdateWrapper<SpuDraftPO> updateWrapper = Wrappers.lambdaUpdate(SpuDraftPO.class)
                    .set(SpuDraftPO::getSpuJsonInfo, JSON.toJSONString(spuJsonInfoObject))
                    .set(SpuDraftPO::getUpdateTime, new Date())
                    .set(SpuDraftPO::getUpdater, StringUtils.isNotBlank(updater) ? updater : Constant.SYSTEM)
                    .eq(SpuDraftPO::getId, spuDraft.getId())
                    .eq(SpuDraftPO::getYn, YnEnum.YES.getCode())
                    .eq(SpuDraftPO::getUpdateTime, spuDraft.getUpdateTime());
            boolean update = super.update(updateWrapper);

            log.info("更新草稿中的采销信息. result=[{}] spuId={}, buyer={}, updater={}", update, spuDraft.getSpuId(), buyer, updater);

            if (!update) {
                log.info("更新草稿中的采销信息失败. result=[{}] spuId={}, buyer={}, updater={}", false, spuDraft.getSpuId(), buyer, updater);
                throw new ProductBizException("spu草稿正在进行其他业务操作，请稍后重试 %s", spuDraft.getSpuId());
            }
        }
    }


    /**
     * 检查指定的供应商SPU ID是否存在。
     * @param supplierSpuId 供应商SPU ID
     * @param supplierCode 供应商简码
     * @return 如果供应商SPU ID存在，返回true；否则返回false。
     */
    public boolean existSupplierSpuId(String supplierSpuId,String supplierCode,Long spuId) {
        LambdaQueryWrapper<SpuDraftPO> queryWrapper = Wrappers.lambdaQuery(SpuDraftPO.class)
                .like(SpuDraftPO::getSpuJsonInfo, SPU_VENDOR_SPU_ID + supplierSpuId + "\"")
                .eq(SpuDraftPO::getVendorCode, supplierCode)
                .ne(Objects.nonNull(spuId),SpuDraftPO::getSpuId, spuId)
                .eq(SpuDraftPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().exists(queryWrapper);
    }

    public List<SpuDraftPO> listSpuDraftByGroupExtAttributePage(Long offset, Integer pageSize){
        // 先查询ID列表
        List<Long> ids = super.getBaseMapper().listSpuDraftIdsByGroupExtAttributePage(offset, pageSize);
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        // 再根据ID列表查询详情
        return super.getBaseMapper().listSpuDraftByIds(ids);
    }
}




