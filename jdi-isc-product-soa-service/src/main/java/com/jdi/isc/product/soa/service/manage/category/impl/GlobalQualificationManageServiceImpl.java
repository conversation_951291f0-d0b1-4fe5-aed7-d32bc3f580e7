package com.jdi.isc.product.soa.service.manage.category.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.AttributeCheckTypeEnum;
import com.jdi.isc.product.soa.common.enums.UseSceneEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.domain.category.biz.*;
import com.jdi.isc.product.soa.domain.category.po.*;
import com.jdi.isc.product.soa.domain.common.biz.KvVO;
import com.jdi.isc.product.soa.domain.common.po.BasicPO;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.service.atomic.category.*;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuCertificateAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuCertificateAtomicService;
import com.jdi.isc.product.soa.service.manage.category.GlobalQualificationManageService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.mapstruct.category.GlobalQualificationConvert;
import com.jdi.isc.product.soa.service.mapstruct.category.GlobalQualificationLangConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.api.common.LangConstant.LANG_ZH;

/**
 * @Description: 国际资质主数据数据维护服务实现
 * @Author: taxuezheng1
 * @Date: 2024/07/12 11:33
 **/

@Slf4j
@Service
public class GlobalQualificationManageServiceImpl extends BaseManageSupportService<GlobalQualificationVO, GlobalQualificationPO> implements GlobalQualificationManageService {

    @Resource
    private GlobalQualificationAtomicService globalQualificationAtomicService;
    @Resource
    private GlobalCountryQualificationCategoryRelationAtomicService globalCountryQualificationCategoryRelationAtomicService;
    @Resource
    private GlobalQualificationLangAtomicService globalQualificationLangAtomicService;
    @Resource
    private GlobalAttributeLangAtomicService globalAttributeLangAtomicService;
    @Resource
    private GlobalAttributeValueLangAtomicService globalAttributeValueLangAtomicService;
    @Resource
    private LangManageService langManageService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    private SkuCertificateAtomicService skuCertificateAtomicService;
    @Resource
    private SpuCertificateAtomicService spuCertificateAtomicService;

    @Override
    public DataResponse<Boolean> saveOrUpdate(GlobalQualificationVO input) {
        log.info("saveOrUpdate, input={}", JSONObject.toJSONString(input));

        // 参数业务校验
        DataResponse<Boolean> checkResult = checkAndInitInput(input);
        if (!checkResult.getSuccess()){
            log.info("saveOrUpdate, check input fail, input={}, checkResult={}", JSONObject.toJSONString(input), JSONObject.toJSONString(checkResult));
            return checkResult;
        }
        long timestamp = System.currentTimeMillis();
        // 保存对象
        GlobalQualificationPO po = GlobalQualificationConvert.INSTANCE.vo2Po(input);
        List<GlobalQualificationLangPO> qualificationLangPOS = GlobalQualificationLangConvert.INSTANCE.listVo2Po(input.getGlobalQualificationLangVOList());
        po.setUpdateTime(timestamp);
        po.setCreateTime(timestamp);
        po.setYn(YnEnum.YES.getCode());
        if (null!=po.getId()){
            // 编辑时不允许修改如下字段。重置为空，就不会更新
            po.setCreator(null);
            po.setCreateTime(null);
        }
        if (CollectionUtils.isNotEmpty(input.getClassification1())) {
            po.setClassification1Json(JSON.toJSONString(input.getClassification1()));
        }
        if (CollectionUtils.isNotEmpty(input.getClassification2())) {
            po.setClassification2Json(JSON.toJSONString(input.getClassification2()));
        }
        //必填属性保存
        if (input.getAttributeParam() !=null){
            input.getAttributeParam().setAttributeName(null);
            input.getAttributeParam().setAttributeValueName(null);
            po.setFeatures(JSON.toJSONString(input.getAttributeParam()));
        }
        boolean saveRes = globalQualificationAtomicService.saveOrUpdate(po);
        Assert.isTrue(saveRes,"保存资质信息失败");
        qualificationLangPOS.forEach(p -> {
            p.setQualificationId(po.getId());
            p.setUpdateTime(timestamp);
            p.setUpdater(input.getUpdater());
            p.setCreateTime(timestamp);
            p.setCreator(input.getCreator());
            p.setYn(YnEnum.YES.getCode());
            if (p.getId() != null) {
                p.setCreateTime(null);
                p.setCreator(null);
            }
        });
        boolean batch = globalQualificationLangAtomicService.saveOrUpdateBatch(qualificationLangPOS);
        Assert.isTrue(batch,"保存资质多语言信息失败");
        return DataResponse.success(true);
    }

    @Override
    public GlobalQualificationVO detail(Long id) {
        GlobalQualificationPO po = globalQualificationAtomicService.getValidById(id);
        if (null==po){
            log.info("detail, GlobalQualificationPO null. id={}", id);
            return null;
        }
        Map<String, String> langMap = langManageService.getLangMap(null);
        List<GlobalQualificationLangPO> langList = globalQualificationLangAtomicService.queryByQualificationIds(Collections.singletonList(id),null);
        GlobalQualificationVO vo = GlobalQualificationConvert.INSTANCE.po2Vo(po);
        vo.setClassification1(buildClassification(po.getClassification1Json()));
        vo.setClassification2(buildClassification(po.getClassification2Json()));
        List<GlobalQualificationLangVO> qualificationLangPOS = GlobalQualificationLangConvert.INSTANCE.listPo2Vo(langList);
        if (CollectionUtils.isNotEmpty(qualificationLangPOS)) {
            qualificationLangPOS.forEach(q -> {
                q.setLangName(langMap.get(q.getLang()));
            });
        }
        vo.setGlobalQualificationLangVOList(qualificationLangPOS);

        //如果必填属性不为空，则查询对应的中文名
        if (po.getFeatures() != null) {
            GlobalAttributeParam attributeParam = JSON.parseObject(po.getFeatures(), GlobalAttributeParam.class);
            //查询属性中文名
            GlobalAttributeLangPO attributeLangPO = globalAttributeLangAtomicService.queryByAttrIdAndLang(attributeParam.getAttributeId(), LangConstant.LANG_ZH);
            if (attributeLangPO != null) {
                attributeParam.setAttributeName(attributeLangPO.getLangAttributeName());
            }
            //查询属性值中文名
            GlobalAttributeValueLangPO attrValueLang = globalAttributeValueLangAtomicService.queryByAttrIdAndLang(attributeParam.getAttributeId(), LangConstant.LANG_ZH);
            if (attrValueLang != null) {
                attributeParam.setAttributeValueName(attrValueLang.getLangAttributeValueName());
            }
            vo.setAttributeParam(attributeParam);
        }
        return vo;
    }

    private List<String> buildClassification(String classificationJson) {
        if (Objects.isNull(classificationJson)) {
            return Collections.emptyList();
        }
        return JSON.parseObject(classificationJson, List.class);
    }

    @Override
    public PageInfo<GlobalQualificationPageVO.Response> pageSearch(GlobalQualificationPageVO.Request input) {
        PageInfo<GlobalQualificationPageVO.Response> pageInfo = new PageInfo<>();
        pageInfo.setSize(input.getSize());
        pageInfo.setIndex(input.getIndex());
        Set<Long> ids = new HashSet<>();
        if (CollectionUtils.isNotEmpty(input.getGlobalQualificationLangVOList())){
            List<GlobalQualificationLangVO> qualificationLang = input.getGlobalQualificationLangVOList();
            GlobalQualificationLangVO langVO = qualificationLang.get(0);
            List<GlobalQualificationLangPO> langPOList = globalQualificationLangAtomicService.queryByNameAndLang(langVO.getLang(), langVO.getQualificationName());
            if (CollectionUtils.isNotEmpty(langPOList)) {
                ids.addAll(langPOList.stream().map(GlobalQualificationLangPO::getQualificationId).collect(Collectors.toSet()));
            }
        }
        if (!ids.isEmpty() && null != input.getId()) {
            if (!ids.contains(input.getId())) {
                return pageInfo;
            }
            ids.clear();
            ids.add(input.getId());
            input.setId(null);
        }

        // 查询列表
        Page<GlobalQualificationPO> pageDB = new Page<>(input.getIndex(), input.getSize());
        LambdaQueryWrapper<GlobalQualificationPO> wrapper = Wrappers.<GlobalQualificationPO>lambdaQuery()
                .eq(GlobalQualificationPO::getYn, YnEnum.YES.getCode())
                .in(CollectionUtils.isNotEmpty(ids),GlobalQualificationPO::getId,ids)
                .eq(Objects.nonNull(input.getCheckType()),GlobalQualificationPO::getCheckType,input.getCheckType())
                .eq(Objects.nonNull(input.getId()),GlobalQualificationPO::getId,input.getId())
                .eq(Objects.nonNull(input.getValidStatus()),GlobalQualificationPO::getValidStatus,input.getValidStatus())
                .eq(Objects.nonNull(input.getShowCustomer()),GlobalQualificationPO::getShowCustomer,input.getShowCustomer())
                .eq(Objects.nonNull(input.getQualificationDimension()),GlobalQualificationPO::getQualificationDimension,input.getQualificationDimension())
                .orderByDesc(GlobalQualificationPO::getUpdateTime);
        Page<GlobalQualificationPO> dbRecord = globalQualificationAtomicService.page(pageDB, wrapper);
        pageInfo.setTotal(dbRecord.getTotal());
        if (CollectionUtils.isEmpty(dbRecord.getRecords())){
            return pageInfo;
        }

        Set<Long> idSet = dbRecord.getRecords().stream().map(BasicPO::getId).collect(Collectors.toSet());
        List<GlobalQualificationLangPO> langList = globalQualificationLangAtomicService.queryByIdsAndLang(idSet, LangConstant.LANG_ZH);
        List<GlobalQualificationLangVO> langVOList = GlobalQualificationLangConvert.INSTANCE.listPo2Vo(langList);
        Map<Long, List<GlobalQualificationLangVO>> langMap = langVOList.stream().collect(Collectors.groupingBy(GlobalQualificationLangVO::getQualificationId));
        List<GlobalQualificationPageVO.Response> pageList = GlobalQualificationConvert.INSTANCE.pageListPo2Vo(dbRecord.getRecords());
        if (CollectionUtils.isNotEmpty(pageList)){
            pageList.forEach(p -> {
                p.setGlobalQualificationLangVOList(langMap.get(p.getId()));
            });
            pageInfo.setRecords(pageList);
        }

        return pageInfo;
    }

    /**
     * 根据国家代码和类别ID查询全球资格信息
     *
     * @param catId 类别ID
     * @param sourceCountryCode 源国家代码
     * @param targetCountryCodes 目标国家代码集合
     * @param dimension 维度
     * @param lang 语言代码
     * @return 包含全球资格信息的列表
     */
    @Override
    @PFTracing
    public List<GlobalQualificationVO> queryByCountry(Long catId, String sourceCountryCode,
        Collection<String> targetCountryCodes,Integer dimension,String lang) {
        return queryByCountry(catId,sourceCountryCode
                ,targetCountryCodes, dimension,lang,0);
     }

    @Override
    public List<GroupCertificateVO> queryGroupByCountry(Long catId, String sourceCountryCode, Collection<String> targetCountryCodes, Integer dimension, String lang, Integer isExport) {
        List<GlobalQualificationVO> globalQualificationVOS = queryByCountry(catId,sourceCountryCode
                ,targetCountryCodes, dimension,lang,isExport);
        if(CollectionUtils.isEmpty(globalQualificationVOS)){
            return new ArrayList<>();
        }

        Map<Integer, List<GlobalQualificationVO>> groupMap = globalQualificationVOS.stream()
                .collect(Collectors.groupingBy(GlobalQualificationVO::getCheckType));

        List<GroupCertificateVO> results = new ArrayList<>();
        AttributeCheckTypeEnum[] checkTypeEnums = AttributeCheckTypeEnum.values();
        for(AttributeCheckTypeEnum checkTypeEnum : checkTypeEnums){
            List<GlobalQualificationVO> n = groupMap.get(checkTypeEnum.getCode());
            if(CollectionUtils.isEmpty(n)){
                continue;
            }
            GroupCertificateVO groupCertificateVO = new GroupCertificateVO();
            List<GlobalCertificateVO> globalCertificateVOS = new ArrayList<>();
            for (GlobalQualificationVO qualificationVO : n) {
                GlobalCertificateVO globalCertificateVO = this.createGlobalCertificateVO(qualificationVO);
                if(globalCertificateVO != null){
                    globalCertificateVOS.add(globalCertificateVO);
                }

            }
            if(CollectionUtils.isNotEmpty(globalCertificateVOS)){
                groupCertificateVO.setRequirement(checkTypeEnum.getCode());
                groupCertificateVO.setCertificateVOS(globalCertificateVOS);
                results.add(groupCertificateVO);
            }
        }

        return results;
    }

    @Override
    @PFTracing
    public List<GlobalQualificationVO> queryByCountry(Long catId, String sourceCountryCode,
        Collection<String> targetCountryCodes,Integer dimension,String lang,Integer isExport) {
        if(catId == null || StringUtils.isBlank(sourceCountryCode)){
            log.error("GlobalQualificationManageServiceImpl.queryByCountry param is null");
            return new ArrayList<>();
        }

        Set<Long> catIds = new HashSet<>();
        catIds.add(Constant.DEFAULT_ALL_CATEGORY_SELECT);
        catIds.add(catId);

        if(CollectionUtils.isEmpty(targetCountryCodes)){
            targetCountryCodes = new ArrayList<>();
        }

        List<GlobalCountryQualificationCategoryRelationPO> allRelationPOS = new ArrayList<>();
        // 本土
        if(targetCountryCodes.contains(sourceCountryCode)){
            List<GlobalCountryQualificationCategoryRelationPO> relationPOS = globalCountryQualificationCategoryRelationAtomicService.queryByCountryCode(sourceCountryCode, catIds, UseSceneEnum.LOCAL.getCode());
            if(CollectionUtils.isNotEmpty(relationPOS)){
                allRelationPOS.addAll(relationPOS);
            }
        }

        // 出口
        if(targetCountryCodes.size() > 1 || !targetCountryCodes.contains(sourceCountryCode)
                || Integer.valueOf(YesOrNoEnum.YES.getCode()).equals(isExport)){
            List<GlobalCountryQualificationCategoryRelationPO> relationPOS = globalCountryQualificationCategoryRelationAtomicService.queryByCountryCode(sourceCountryCode, catIds, UseSceneEnum.EXPORT.getCode());
            if(CollectionUtils.isNotEmpty(relationPOS)){
                allRelationPOS.addAll(relationPOS);
            }
        }

        // 进口
        if(targetCountryCodes.size() > 1){
            List<String> targetCountryCodesParam = new ArrayList<>(targetCountryCodes);
            targetCountryCodesParam.remove(sourceCountryCode);
            List<GlobalCountryQualificationCategoryRelationPO> relationPOS = globalCountryQualificationCategoryRelationAtomicService.queryByCountryCodes(targetCountryCodesParam, catIds, UseSceneEnum.IMPORT.getCode());
            if(CollectionUtils.isNotEmpty(relationPOS)){
                allRelationPOS.addAll(relationPOS);
            }
        }

        if(CollectionUtils.isEmpty(allRelationPOS)){
            log.error("GlobalQualificationManageServiceImpl.queryByCountry allRelationPOS is null");
            return new ArrayList<>();
        }
        Set<Long> qualificationIds = allRelationPOS.stream()
            .map(GlobalCountryQualificationCategoryRelationPO::getQualificationId)
            .collect(Collectors.toSet());
        List<GlobalQualificationPO> qualificationPOS = globalQualificationAtomicService.getByIds(qualificationIds,dimension,null);

        List<GlobalQualificationLangPO> langList = globalQualificationLangAtomicService.queryByQualificationIds(qualificationIds,lang);

        Map<Long,List<GlobalQualificationLangPO>> langMap = langList.stream().collect(Collectors.groupingBy(GlobalQualificationLangPO::getQualificationId));
        List<GlobalQualificationVO> qualificationVOS = new ArrayList<>();
        for(GlobalQualificationPO po : qualificationPOS){
            GlobalQualificationVO vo = GlobalQualificationConvert.INSTANCE.po2Vo(po);
            vo.setClassification1(buildClassification(po.getClassification1Json()));
            vo.setClassification2(buildClassification(po.getClassification2Json()));
            List<GlobalQualificationLangVO> qualificationLangVOS = GlobalQualificationLangConvert.INSTANCE.listPo2Vo(langMap.get(po.getId()));
            vo.setGlobalQualificationLangVOList(qualificationLangVOS);
            qualificationVOS.add(vo);
        }
        return qualificationVOS;
    }


        @Override
    public boolean checkLangName(GlobalQualificationVO reqVo) {
        if (CollectionUtils.isEmpty(reqVo.getGlobalQualificationLangVOList())) {
            return false;
        }

        GlobalQualificationLangVO langVO = reqVo.getGlobalQualificationLangVOList().get(0);
        List<GlobalQualificationLangPO> attributeLangPOS = globalQualificationLangAtomicService.queryLangAndAttrName(langVO.getLang(), langVO.getQualificationName().trim());
        if (CollectionUtils.isEmpty(attributeLangPOS)) {
            return true;
        }

        if (attributeLangPOS.size() == 1) {
            return attributeLangPOS.get(0).getQualificationId().equals(reqVo.getId());
        }
        return false;
    }

    @Override
    public List<KvVO> queryQualificationName(List<Long> ids, String lang) {
        if(StringUtils.isBlank(lang) || CollectionUtils.isEmpty(ids)){
            log.error("GlobalQualificationManageServiceImpl.queryAttributeName param is null");
            return null;
        }
        Map<Long, List<GlobalQualificationLangPO>> qualificationLangMap = globalQualificationLangAtomicService.getQualificationLangMap(ids);
        if(MapUtils.isEmpty(qualificationLangMap)){
            log.error("GlobalQualificationManageServiceImpl.queryAttributeName attributeLangMap is null");
            return null;
        }

        List<KvVO> results = new ArrayList<>();
        for(Long id : ids){
            KvVO result = new KvVO();
            result.setId(id);
            List<GlobalQualificationLangPO> qualificationLangPOS = qualificationLangMap.get(id);
            if(CollectionUtils.isEmpty(qualificationLangPOS)){
                continue;
            }
            Map<String,GlobalQualificationLangPO> langPOMap = qualificationLangPOS.stream().collect(Collectors.toMap(GlobalQualificationLangPO::getLang, Function.identity()));
            result.setValue(langPOMap.get(lang) == null?langPOMap.get(LANG_ZH).getQualificationName():langPOMap.get(lang).getQualificationName());
            results.add(result);
        }
        return results;
    }

    /**
     * 参数校验
     * @param input 提交参数
     * @return 检查结果
     */
    private DataResponse<Boolean> checkAndInitInput(GlobalQualificationVO input){
        List<GlobalQualificationLangVO> attributeLangList = input.getGlobalQualificationLangVOList();
        GlobalQualificationVO checkParam = new GlobalQualificationVO();
        checkParam.setId(input.getId());
        StringBuilder errorMsg = new StringBuilder();
        for (GlobalQualificationLangVO langVO : attributeLangList) {
            checkParam.setGlobalQualificationLangVOList(Collections.singletonList(langVO));
            boolean checkRes = checkLangName(checkParam);
            if (!checkRes) {
                errorMsg.append(langVO.getQualificationName()).append("名称重复,");

            }
        }
        if (errorMsg.length() > 0) {
            String error = errorMsg.substring(0, errorMsg.length());
            return DataResponse.buildError(error);
        }

        return DataResponse.success();
    }

    /**
     * 根据GlobalQualificationVO对象创建并返回一个GlobalCertificateVO对象。
     * @param qualificationVO 一个包含全球认证信息的GlobalQualificationVO对象。
     * @return 一个包含全球认证证书信息的GlobalCertificateVO对象。
     */
    private GlobalCertificateVO createGlobalCertificateVO(GlobalQualificationVO qualificationVO) {
        if(qualificationVO.getValidStatus()== null || qualificationVO.getValidStatus().equals(YesOrNoEnum.NO.getCode())){
            return null;
        }
        GlobalCertificateVO certificateVO = new GlobalCertificateVO();
        certificateVO.setCertificateId(qualificationVO.getId());
        if (CollectionUtils.isNotEmpty(qualificationVO.getGlobalQualificationLangVOList())) {
            certificateVO.setCertificateName(qualificationVO.getGlobalQualificationLangVOList().get(0).getQualificationName());
        }
        certificateVO.setRequirement(qualificationVO.getCheckType());
        certificateVO.setCertificateNote(qualificationVO.getQualificationRemark());
        certificateVO.setQualificationSample(qualificationVO.getQualificationSample());
        if(CollectionUtils.isNotEmpty(qualificationVO.getClassification1())){
            certificateVO.setClassification1(qualificationVO.getClassification1());
        }
        if(CollectionUtils.isNotEmpty(qualificationVO.getClassification2())){
            certificateVO.setClassification2(qualificationVO.getClassification2());
        }
        return certificateVO;
    }

}
