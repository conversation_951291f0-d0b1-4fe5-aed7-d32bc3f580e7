package com.jdi.isc.product.soa.service.mapstruct.customerSku;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.customerSku.req.*;
import com.jdi.isc.product.soa.api.customerSku.res.CustomerSkuPriceAuditApiDTO;
import com.jdi.isc.product.soa.api.customerSku.res.CustomerSkuPriceAuditNumApiDTO;
import com.jdi.isc.product.soa.api.customerSku.res.CustomerSkuPriceDraftApiDTO;
import com.jdi.isc.product.soa.api.customerSku.res.SalePriceCalculateResDTO;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.domain.customerSku.biz.*;
import com.jdi.isc.product.soa.domain.customerSku.po.*;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * 客制化sku价格对象转换
 * <AUTHOR>
 * @date 20231128
 */
@Mapper
public interface CustomerSkuPriceConvert {

    CustomerSkuPriceConvert INSTANCE = Mappers.getMapper(CustomerSkuPriceConvert.class);

    @InheritConfiguration
    CustomerSkuPricePO vo2po(CustomerSkuPriceVO categoryDTO);

    @InheritConfiguration
    CustomerSkuPriceVO po2vo(CustomerSkuPricePO categoryPO);

    @InheritConfiguration
    List<CustomerSkuPriceVO> listPo2vo(List<CustomerSkuPricePO> customerMkuPO);

    @InheritConfiguration
    CustomerSkuExcludeTaxPricePO excludeTax2Po(CustomerSkuExcludeTaxPriceVO vo);
    @InheritConfiguration
    CustomerSkuExcludeTaxPriceVO excludeTaxPo2Vo(CustomerSkuExcludeTaxPricePO po);

    List<CustomerSkuExcludeTaxPriceVO> listExcludePo2Vo(List<CustomerSkuExcludeTaxPricePO> poList);

    @InheritConfiguration
    @Mappings({
            @Mapping(target = "createTime", expression = "java(long2Date(input.getCreateTime()))"),
            @Mapping(target = "updateTime", expression = "java(long2Date(input.getUpdateTime()))")
    })
    CustomerSkuPriceDraftPO draftVo2po(CustomerSkuPriceDraftVO input);

    @InheritConfiguration
    @Mappings({
            @Mapping(target = "createTime", expression = "java(date2Long(input.getCreateTime()))"),
            @Mapping(target = "updateTime", expression = "java(date2Long(input.getUpdateTime()))")
    })
    CustomerSkuPriceDraftVO draftPo2vo(CustomerSkuPriceDraftPO input);

    @InheritConfiguration
    List<CustomerSkuPriceDraftVO> draftListPo2vo(List<CustomerSkuPriceDraftPO> customerMkuPO);

    @InheritConfiguration
    CustomerSkuPriceReqVO draftReqVo2ReqVo(CustomerSkuPriceDraftReqVO input);

    @InheritConfiguration
    PageInfo<CustomerSkuPriceDraftVO> vo2DraftVo(PageInfo<CustomerSkuPriceVO> inputs);

    CustomerSkuPriceDraftReqVO reqDto2Vo(CustomerSkuPriceDraftReqApiDTO input);

    PageInfo<CustomerSkuPriceDraftApiDTO> pageVo2Dto(PageInfo<CustomerSkuPriceDraftVO> input);

    List<CustomerSkuPriceAuditNumApiDTO> auditNumVo2Dto(List<CustomerSkuPriceAuditNumVO> inputs);

    CustomerSkuPriceDraftVO dto2Vo(CustomerSkuPriceDraftApiDTO input);

    CustomerSkuAuditVO auditDto2Vo(CustomerSkuAuditApiDTO input);

    SalePriceCalculateResDTO calculateVo2Dto(SalePriceCalculateResVO input);

    SalePriceCalculateVO calculateDto2Vo(SalePriceCalculateReqDTO input);

    CustomerSkuPriceVO draftVo2vo(CustomerSkuPriceDraftVO draftVO);

    CustomerSkuPriceAuditReqVO auditReqApiDto2Vo(CustomerSkuPriceAuditReqApiDTO input);

    PageInfo<CustomerSkuPriceAuditApiDTO> auditResApiVo2Dto(PageInfo<CustomerSkuPriceAuditResVO> input);


    // 刷数据用
    @Mappings({
            @Mapping(target = "createTime", expression = "java(date2Long(input.getCreateTime()))"),
            @Mapping(target = "updateTime", expression = "java(date2Long(input.getUpdateTime()))")
    })
    CustomerSkuPriceDetailDraftPO skuPriceDraftPo2DetailDraftPo(CustomerSkuPriceDraftPO input);

    // 刷数据用
    @Mappings({
            @Mapping(target = "createTime", expression = "java(date2Long(input.getCreateTime()))"),
            @Mapping(target = "updateTime", expression = "java(date2Long(input.getUpdateTime()))")
    })
    CustomerSkuPriceDetailPO skuPricePo2DetailPo(CustomerSkuPricePO input);

    CustomerSkuPriceWarningPageReqVO warningReqDto2Vo(CustomerSkuPriceWarningPageReqDTO input);

    List<CustomerSkuPriceWarningVO> warningListPo2Vo(List<CustomerSkuPriceWarningPO> input);

    PageInfo<CustomerSkuPriceWarningDTO> warningPageVo2Dto(PageInfo<CustomerSkuPriceWarningVO> input);

    CustomerSkuPriceDraftApiDTO convert(CustomerSkuPriceDraftVO input);

    List<CustomerSkuPriceWarningDTO> convert(List<CustomerSkuPriceWarningPO> list);

    default Long date2Long(Date dateTime){
        if(Objects.isNull(dateTime)){
            return null;
        }
        return dateTime.getTime();
    }

    default Date long2Date(Long dateTime){
        if(Objects.isNull(dateTime)){
            return null;
        }
        return DateUtil.long2date(dateTime);
    }

    @Named("stringToList2")
    default List<String> stringToList(String stringProperty) {
        if (stringProperty == null) {
            return Lists.newArrayList();
        }
        return Arrays.asList(stringProperty.split(",")); // 假设字符串由逗号分隔
    }

    @Named("listToString")
    default String listToString(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return Joiner.on(",").join(list);
    }
}
