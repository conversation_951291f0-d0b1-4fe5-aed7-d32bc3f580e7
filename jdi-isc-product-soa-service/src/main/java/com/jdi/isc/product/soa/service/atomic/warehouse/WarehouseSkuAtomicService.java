package com.jdi.isc.product.soa.service.atomic.warehouse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.domain.warehouse.biz.*;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehouseSkuPO;
import com.jdi.isc.product.soa.repository.mapper.warehouse.WarehouseSkuBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 仓信息多语言表原子服务
 * @Author: wangpeng965
 * @Date: 2024/07/18 11:30
 **/
@Slf4j
@Service
public class WarehouseSkuAtomicService extends ServiceImpl<WarehouseSkuBaseMapper, WarehouseSkuPO> {


    /**
     * @function 根据条件查询仓库中的SKU信息
     * @param reqVO - 查询条件封装的请求对象
     * @returns 返回符合条件的仓库SKU信息列表
     */
    public List<WarehouseSkuResVO> querySkusByCondition(WarehouseSkuPageVO reqVO){
        reqVO.setOffset((reqVO.getIndex()-1)*reqVO.getSize());
        return this.getBaseMapper().querySkusByCondition(reqVO);
    }

    /**
     * 根据条件查询仓库SKU列表(V1版本)
     * @param reqVO 仓库SKU分页查询请求对象，包含页码、每页数量等信息
     * @return 满足条件的仓库SKU响应对象列表
     */
    public List<WarehouseSkuResVO> querySkusByConditionV1(WarehouseSkuPageVO reqVO){
        reqVO.setOffset((reqVO.getIndex()-1)*reqVO.getSize());
        return this.getBaseMapper().querySkusByConditionV1(reqVO);
    }

    /**
     * @function 统计特定条件下仓库SKU的总数
     * @param reqVO - 查询条件封装的WarehouseSkuPageVO对象
     * @returns 返回满足条件的仓库SKU总数
     */
    public long getTotal(WarehouseSkuPageVO reqVO){
        // TODO 分片表分页，统计总条数方式修改
        List<Long> skuList = this.getBaseMapper().total(reqVO);
        return skuList.size();
    }


    /**
     * @function 查询绑定到特定仓库的SKU ID集合
     * @param warehouseId 要查询的仓库ID
     * @param skuIds SKU ID集合，用于过滤查询结果
     * @returns 绑定到指定仓库的SKU ID集合
     */
    public Set<Long> queryBindSku(Long warehouseId, Set<Long> skuIds){
        LambdaQueryWrapper<WarehouseSkuPO> queryWrapper = Wrappers.lambdaQuery(WarehouseSkuPO.class)
                .select(WarehouseSkuPO::getSkuId,WarehouseSkuPO::getBindStatus)
                .eq(WarehouseSkuPO::getWarehouseId, warehouseId)
                .eq(WarehouseSkuPO::getBindStatus, YesOrNoEnum.YES.getCode())
                .eq(WarehouseSkuPO::getYn,YnEnum.YES.getCode())
                .in(WarehouseSkuPO::getSkuId, skuIds);

        List<WarehouseSkuPO> warehouseSkuPOList = this.getBaseMapper().selectList(queryWrapper);
        return Optional.ofNullable(warehouseSkuPOList)
                .orElseGet(ArrayList::new)
                .stream().map(WarehouseSkuPO::getSkuId).collect(Collectors.toSet());
    }

    /**
     * 根据 SKU ID 集合查询绑定的 SKU 映射
     *
     * @param skuIds SKU ID 集合
     * @return SKU ID 到仓库 SKU 列表的映射
     */
    public Map<Long, List<WarehouseSkuPO>> queryBindSkuMap(Set<Long> skuIds) {
        LambdaQueryWrapper<WarehouseSkuPO> queryWrapper = Wrappers.lambdaQuery(WarehouseSkuPO.class)
                .select(WarehouseSkuPO::getWarehouseId,WarehouseSkuPO::getSkuId,WarehouseSkuPO::getBindStatus,WarehouseSkuPO::getOnWaySale,WarehouseSkuPO::getPurchaseModel)
                .eq(WarehouseSkuPO::getBindStatus, YesOrNoEnum.YES.getCode())
                .eq(WarehouseSkuPO::getYn,YnEnum.YES.getCode())
                .in(WarehouseSkuPO::getSkuId, skuIds);

        List<WarehouseSkuPO> warehouseSkuPOList = this.getBaseMapper().selectList(queryWrapper);
        return Optional.ofNullable(warehouseSkuPOList)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.groupingBy(WarehouseSkuPO::getSkuId));
    }

    /**
     * 根据 StockManageReqDTO 对象查询 SKU 和仓库的关系信息。
     * @param reqDTO 包含查询条件的 StockManageReqDTO 对象。
     * @return 一个 Map 对象，其中键为 SKU 的 ID，值为对应的仓库 ID。
     */
    public Map<Long, Long> querySkuWarehouseInfo(StockManageReqDTO reqDTO) {
        if (StringUtils.isBlank(reqDTO.getCountryCode())) {
            return Collections.emptyMap();
        }
        Set<String> skuIds = reqDTO.getStockItem().stream().map(item -> item.getSkuId().toString()).collect(Collectors.toSet());
        List<WarehouseSkuResVO> warehouseSkuResVOList = this.getBaseMapper().querySkuWarehouseInfo(new QuerySkuRelationReqVO(skuIds,reqDTO.getCountryCode()));

        // sku和仓库关系
        return Optional.ofNullable(warehouseSkuResVOList).orElseGet(ArrayList::new).stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(WarehouseSkuResVO::getSkuId, WarehouseSkuResVO::getWarehouseId));
    }





    /**
     * 根据国家代码和商品ID列表查询商品在各仓库的存量信息。
     * @param reqDTO 请求对象，包含国家代码和商品ID列表。
     * @return 每个商品ID对应的仓库存量信息列表的映射。
     */
    public Map<Long, List<WarehouseSkuResVO>> querySkuWarehouseList(StockManageReqDTO reqDTO) {
        if (StringUtils.isBlank(reqDTO.getCountryCode())) {
            return Collections.emptyMap();
        }
        Set<String> skuIds = reqDTO.getStockItem().stream().map(item -> item.getSkuId().toString()).collect(Collectors.toSet());
        List<WarehouseSkuResVO> warehouseSkuResVOList = this.getBaseMapper().querySkuWarehouseInfo(new QuerySkuRelationReqVO(skuIds,reqDTO.getCountryCode()));
        return Optional.ofNullable(warehouseSkuResVOList).orElseGet(ArrayList::new).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(WarehouseSkuResVO::getSkuId));
    }

    /**
     * 根据 SKU ID 集合查询绑定的 SKU 映射
     *
     * @param skuIds SKU ID 集合
     * @return SKU ID 到仓库 SKU 列表的映射
     */
    public Map<Long, Set<Long>> queryBindSkuMapBySkuIds(Set<Long> skuIds) {
        LambdaQueryWrapper<WarehouseSkuPO> queryWrapper = Wrappers.lambdaQuery(WarehouseSkuPO.class)
                .select(WarehouseSkuPO::getWarehouseId,WarehouseSkuPO::getSkuId,WarehouseSkuPO::getBindStatus)
                .eq(WarehouseSkuPO::getBindStatus, YesOrNoEnum.YES.getCode())
                .eq(WarehouseSkuPO::getYn,YnEnum.YES.getCode())
                .in(WarehouseSkuPO::getSkuId, skuIds);

        List<WarehouseSkuPO> warehouseSkuPOList = this.getBaseMapper().selectList(queryWrapper);
        return Optional.ofNullable(warehouseSkuPOList)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.groupingBy(WarehouseSkuPO::getSkuId,Collectors.mapping(WarehouseSkuPO::getWarehouseId, Collectors.toSet())));
    }


    public List<WarehouseSkuResVO> batchSkusByCondition(WarehouseBatchSkuVo reqVO){
        return this.getBaseMapper().batchSkusByCondition(reqVO);
    }


    /**
     * 根据 SKU ID 集合查询仓的在途库存是否支持
     *
     * @param skuIds SKU ID 集合
     * @return SKU ID 到仓库 SKU 列表的映射
     */
    public Map<Long, Map<Long, Integer>> skuOnWaySaleMap(List<Long> skuIds) {
        Map<Long, Map<Long, Integer>> resultMap = new HashMap<>();
        if (skuIds == null || skuIds.isEmpty()) {
            log.info("warehouseSkuAtomicService.skuOnWaySaleMap-传入参数异常 skuIds is null ");
            return resultMap;
        }
        LambdaQueryWrapper<WarehouseSkuPO> queryWrapper = Wrappers.lambdaQuery(WarehouseSkuPO.class)
                .select(WarehouseSkuPO::getWarehouseId, WarehouseSkuPO::getSkuId, WarehouseSkuPO::getOnWaySale)
                .eq(WarehouseSkuPO::getBindStatus, YesOrNoEnum.YES.getCode())
                .eq(WarehouseSkuPO::getYn, YnEnum.YES.getCode())
                .in(WarehouseSkuPO::getSkuId, skuIds);

        List<WarehouseSkuPO> warehouseSkuPOList = Optional.ofNullable(this.getBaseMapper().selectList(queryWrapper))
                .orElse(Collections.emptyList());
        warehouseSkuPOList.forEach(warehouseSkuPO ->
                resultMap
                        .computeIfAbsent(warehouseSkuPO.getSkuId(), k -> new HashMap<>())
                        .put(warehouseSkuPO.getWarehouseId(),
                                warehouseSkuPO.getOnWaySale() != null ? warehouseSkuPO.getOnWaySale() : 0)
        );
        return resultMap;
    }



    /**
     * 根据 SKU ID 集查询仓库 SKU 绑定信息
     * @param skuIds SKU ID 集合
     * @return 仓信息列表
     */
    public List<WarehouseSkuPO>  queryListBySkuIds(Set<Long> skuIds) {
        LambdaQueryWrapper<WarehouseSkuPO> queryWrapper = Wrappers.lambdaQuery(WarehouseSkuPO.class)
                .eq(WarehouseSkuPO::getBindStatus, YesOrNoEnum.YES.getCode())
                .eq(WarehouseSkuPO::getYn,YnEnum.YES.getCode())
                .in(WarehouseSkuPO::getSkuId, skuIds);

        return this.getBaseMapper().selectList(queryWrapper);
    }

    /**
     * 根据 SKU ID 和仓库 ID 查询绑定状态为已绑定且有效的仓库 SKU。
     * @param skuId SKU ID
     * @param warehouseId 仓库 ID
     * @return 符合条件的仓库 SKU 对象
     */
    public WarehouseSkuPO queryWarehouseSku(Long skuId, Long warehouseId) {
        LambdaQueryWrapper<WarehouseSkuPO> queryWrapper = Wrappers.lambdaQuery(WarehouseSkuPO.class)
                .eq(WarehouseSkuPO::getBindStatus, YesOrNoEnum.YES.getCode())
                .eq(WarehouseSkuPO::getYn,YnEnum.YES.getCode())
                .eq(WarehouseSkuPO::getSkuId, skuId)
                .eq(WarehouseSkuPO::getWarehouseId, warehouseId);

        return this.getBaseMapper().selectOne(queryWrapper);
    }

    /**
     * 根据 SKU ID 集合查询绑定的 SKU 映射
     *
     * @param skuIds SKU ID 集合
     * @return SKU ID 到仓库 SKU 列表的映射
     */
    public Map<Long, Map<Long,WarehouseSkuPO>> queryBindSkuWarehouseMap(Set<Long> skuIds) {
        LambdaQueryWrapper<WarehouseSkuPO> queryWrapper = Wrappers.lambdaQuery(WarehouseSkuPO.class)
                .select(WarehouseSkuPO::getWarehouseId,WarehouseSkuPO::getSkuId,WarehouseSkuPO::getBindStatus,WarehouseSkuPO::getOnWaySale,WarehouseSkuPO::getPurchaseModel)
                .eq(WarehouseSkuPO::getBindStatus, YesOrNoEnum.YES.getCode())
                .eq(WarehouseSkuPO::getYn,YnEnum.YES.getCode())
                .in(WarehouseSkuPO::getSkuId, skuIds);

        List<WarehouseSkuPO> warehouseSkuPOList = this.getBaseMapper().selectList(queryWrapper);
        return Optional.ofNullable(warehouseSkuPOList)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.groupingBy(WarehouseSkuPO::getSkuId,Collectors.toMap(WarehouseSkuPO::getWarehouseId, Function.identity())));
    }
}
