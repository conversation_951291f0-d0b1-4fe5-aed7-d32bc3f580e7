package com.jdi.isc.product.soa.service.manage.customerMku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.customerMku.req.CustomerMkuReqDTO;
import com.jdi.isc.product.soa.domain.customerMku.biz.*;
import com.jdi.isc.product.soa.domain.mku.biz.MkuPoolVerificationReqVO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuPoolVerificationResVO;
import com.jdi.isc.product.soa.domain.spu.biz.GreenPassAmendReqVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuAmendReqVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuAmendVO;
import com.jdi.isc.product.soa.domain.validation.ValidateSpuGroup;
import com.jdi.isc.product.soa.domain.validation.ValidatorGroup;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Set;


/**
 * 客户mku读写服务
 * <AUTHOR>
 * @date 20231120
 */
public interface CustomerMkuManageService {
    /** 分页查询 */
    PageInfo<CustomerMkuVO> page(CustomerMkuQueryVO input);

    /** 批量修改 */
    DataResponse<String>  batchAmend(List<SpuAmendReqVO> spuAmendReqVOList);

    @Validated(ValidateSpuGroup.querySpu.class)
    List<SpuAmendVO> getDetailListByMkuIds(List<Long> mkuIds,String countryCode);


    @Validated(ValidatorGroup.customerMkuBind.class)
    DataResponse<Set<Long>> batchBind(CustomerMkuBatchReqVO input);


    List<CustomerMkuVO> listCustomerMkuPOByMkuIds(List<Long> mkuIds,String clientCode);

    MkuPoolVerificationResVO poolVerification(MkuPoolVerificationReqVO mkuPoolVerificationReqVO);


    List<CustomerMkuVO> listCustomerMkuByMkuIdCountry(List<Long> mkuIds,String targetCountryCode);

    CustomerMkuVO customerMkuPOByMkuId(Long mkuId, String clientCode);


    DataResponse<Boolean> bindCustomerToMku(CustomerMkuEachReqVO vo);

    /**
     * 检查并绑定用户信息
     * @param reqVO 请求参数对象，包含需要绑定的用户信息
     * @return 绑定结果对象，包含绑定状态和可能的错误信息
     */
    MkuCheckBindResVO checkAndBind(MkuCheckBindReqVO reqVO);

    List<CustomerMkuVO> listCustomerMkuPOByMkuIds(CustomerMkuReqVO reqVO);
}
