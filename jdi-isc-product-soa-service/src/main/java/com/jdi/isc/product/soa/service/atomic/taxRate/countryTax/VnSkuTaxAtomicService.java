package com.jdi.isc.product.soa.service.atomic.taxRate.countryTax;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jd.common.web.LoginContext;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.VnSkuTaxPO;
import com.jdi.isc.product.soa.price.api.price.req.VnSkuTaxVO;
import com.jdi.isc.product.soa.repository.mapper.taxRate.countryTax.VnSkuTaxBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 越南SKU税率原子服务
 * <AUTHOR>
 * @date 20250311
 */
@Service
@Slf4j
public class VnSkuTaxAtomicService extends ServiceImpl<VnSkuTaxBaseMapper, VnSkuTaxPO> {

    /**
     * 单个查询
     */
    public VnSkuTaxPO getOne(VnSkuTaxVO vo) {
        LambdaQueryWrapper<VnSkuTaxPO> query = Wrappers.<VnSkuTaxPO>lambdaQuery()
                .eq(VnSkuTaxPO::getJdSkuId, vo.getJdSkuId())
                .eq(VnSkuTaxPO::getYn, YnEnum.YES.getCode());
        return super.getOne(query);
    }

    public Map<Long, VnSkuTaxPO> listSkuTax(Set<Long> skuIds){
        LambdaQueryWrapper<VnSkuTaxPO> queryWrapper = Wrappers.<VnSkuTaxPO>lambdaQuery()
                .in(VnSkuTaxPO::getJdSkuId, skuIds)
                .eq(VnSkuTaxPO::getYn, YnEnum.YES.getCode());
        List<VnSkuTaxPO> res = super.list(queryWrapper);
        if(CollectionUtils.isNotEmpty(res)){
            return res.stream().collect(Collectors.toMap(VnSkuTaxPO::getJdSkuId, a -> a));
        }
        return new HashMap<>();
    }


    /**
     * 税率更新
     */
    public boolean updateTax(VnSkuTaxVO vo) {
        LambdaUpdateWrapper<VnSkuTaxPO> wrapper = Wrappers.<VnSkuTaxPO>lambdaUpdate()
            .set(StringUtils.isNotBlank(vo.getHsCode()), VnSkuTaxPO::getHsCode, vo.getHsCode())
            .set(vo.getMfnTax() != null, VnSkuTaxPO::getMfnTax, vo.getMfnTax())
            .set(vo.getOriginMfnTax() != null, VnSkuTaxPO::getOriginMfnTax, vo.getOriginMfnTax())
            .set(vo.getConsumptionTax() != null, VnSkuTaxPO::getConsumptionTax, vo.getConsumptionTax())
            .set(vo.getEnvironmentalTaxUnitPrice() != null, VnSkuTaxPO::getEnvironmentalTaxUnitPrice, vo.getEnvironmentalTaxUnitPrice())
            .set(vo.getAntiDumpingTax() != null, VnSkuTaxPO::getAntiDumpingTax, vo.getAntiDumpingTax())
            .set(vo.getValueAddedTax() != null, VnSkuTaxPO::getValueAddedTax, vo.getValueAddedTax())
            .set(StringUtils.isNotBlank(vo.getQuarantineCategories()), VnSkuTaxPO::getQuarantineCategories, vo.getQuarantineCategories())
            .set(StringUtils.isNotBlank(vo.getCustomsSupervisionConditions()), VnSkuTaxPO::getCustomsSupervisionConditions, vo.getCustomsSupervisionConditions())
            .set(vo.getIsBondedWarehouse()!=null, VnSkuTaxPO::getIsBondedWarehouse, vo.getIsBondedWarehouse())
            .set(vo.getIsImportLimit()!=null, VnSkuTaxPO::getIsImportLimit, vo.getIsImportLimit())
            .set(StringUtils.isNotBlank(vo.getRemark()), VnSkuTaxPO::getRemark, vo.getRemark())
            .set(VnSkuTaxPO::getUpdater, vo.getUpdater())
            .set(VnSkuTaxPO::getUpdateTime, new Date())
            .eq(VnSkuTaxPO::getId, vo.getId());
        return super.update(wrapper);
    }

}
