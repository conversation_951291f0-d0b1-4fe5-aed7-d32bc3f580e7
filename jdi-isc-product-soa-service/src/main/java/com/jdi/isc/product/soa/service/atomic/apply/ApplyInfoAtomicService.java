package com.jdi.isc.product.soa.service.atomic.apply;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.biz.component.api.enums.JoySkyBizFlowTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.apply.po.ApplyInfoPO;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.repository.mapper.apply.ApplyInfoBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @Description: 申请审批表原子服务
 * @Author: zhaokun51
 * @Date: 2025/01/13 17:59
 **/
@Slf4j
@Service
public class ApplyInfoAtomicService extends ServiceImpl<ApplyInfoBaseMapper, ApplyInfoPO> {


    /**
     * 查询所有待审的业务id
     * @return 对象
     */
    public List<String> selectWaitAuditBizId(JoySkyBizFlowTypeEnum flowTypeEnum,String applyCode,String currentAuditor){
        if(flowTypeEnum == null){
            return null;
        }
        return baseMapper.selectWaitAuditBizId(flowTypeEnum.getFlowTypeCode(),applyCode,currentAuditor);
    }

    /**
     * 查询所有待审的业务id
     * @return 对象
     */
    public List<String> selectWaitAuditBizId(List<JoySkyBizFlowTypeEnum> flowTypeEnums,String applyCode,String currentAuditor){
        if(CollectionUtils.isEmpty(flowTypeEnums)){
            return null;
        }
        List<Integer> flowTypeCodes = flowTypeEnums.stream().map(JoySkyBizFlowTypeEnum::getFlowTypeCode).collect(Collectors.toList());
        return baseMapper.selectWaitAuditBizIds(flowTypeCodes,applyCode,currentAuditor);
    }

    /**
     * 未删除的对象
     * @param id id
     * @return 对象
     */
    public ApplyInfoPO getValidById(Long id){
        if(id == null){
            return null;
        }
        LambdaQueryWrapper<ApplyInfoPO> wrapper = Wrappers.<ApplyInfoPO>lambdaQuery()
                .eq(ApplyInfoPO::getId, id)
                .eq(ApplyInfoPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    /**
     * 根据业务类型和业务ID获取申请信息。
     * @param flowTypeEnum 业务流程类型枚举。
     * @param bizId 业务ID。
     * @return 申请信息对象。
     */
    public ApplyInfoPO getByBizTypeAndBizId(JoySkyBizFlowTypeEnum flowTypeEnum, Long bizId){
        if(flowTypeEnum == null || bizId == null){
            return null;
        }
        LambdaQueryWrapper<ApplyInfoPO> wrapper = Wrappers.<ApplyInfoPO>lambdaQuery()
                .eq(ApplyInfoPO::getProcessType, flowTypeEnum.getFlowTypeCode())
                .eq(ApplyInfoPO::getBizId, bizId)
                .eq(ApplyInfoPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    /**
     * 根据业务类型和业务ID获取申请信息。
     * @param flowTypeEnum 业务流程类型枚举。
     * @param bizIds 业务IDS。
     * @return 申请信息对象。
     */
    public List<ApplyInfoPO> getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum flowTypeEnum, List<Long> bizIds){
        if(flowTypeEnum == null || CollectionUtils.isEmpty(bizIds)){
            return null;
        }
        LambdaQueryWrapper<ApplyInfoPO> wrapper = Wrappers.<ApplyInfoPO>lambdaQuery()
                .eq(ApplyInfoPO::getProcessType, flowTypeEnum.getFlowTypeCode())
                .in(ApplyInfoPO::getBizId, bizIds)
                .eq(ApplyInfoPO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }

    /**
     * 根据业务类型和业务ID获取申请信息。
     * @param flowTypeEnum 业务流程类型枚举。
     * @param processInstanceId 流程id
     * @return 申请信息对象。
     */
    public ApplyInfoPO getByBizTypeAndProcessInstanceId(JoySkyBizFlowTypeEnum flowTypeEnum, String processInstanceId){
        if(flowTypeEnum == null || StringUtils.isBlank(processInstanceId)){
            return null;
        }
        LambdaQueryWrapper<ApplyInfoPO> wrapper = Wrappers.<ApplyInfoPO>lambdaQuery()
                .eq(ApplyInfoPO::getProcessType, flowTypeEnum.getFlowTypeCode())
                .eq(ApplyInfoPO::getProcessInstanceId, processInstanceId)
                .eq(ApplyInfoPO::getYn, YnEnum.YES.getCode());
        return this.getOne(wrapper);
    }

    /**
     * 更新申请信息。
     * @param applyInfoPO 申请信息实体类
     * @param version 申请信息版本号
     * @return 更新成功与否
     */
    public Boolean update(ApplyInfoPO applyInfoPO,Integer version){
        if(applyInfoPO.getId() == null){
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<ApplyInfoPO> updateWrapper = Wrappers.<ApplyInfoPO>lambdaUpdate();
        updateWrapper.eq(ApplyInfoPO::getId,applyInfoPO.getId());
        updateWrapper.eq(ApplyInfoPO::getVersion,version);

        updateWrapper.set(StringUtils.isNotBlank(applyInfoPO.getProcessInstanceId()),ApplyInfoPO::getProcessInstanceId,applyInfoPO.getProcessInstanceId());
        updateWrapper.set(StringUtils.isNotBlank(applyInfoPO.getApplyCode()),ApplyInfoPO::getApplyCode,applyInfoPO.getApplyCode());
        updateWrapper.set(ApplyInfoPO::getAuditStatus,applyInfoPO.getAuditStatus());
        updateWrapper.set(ApplyInfoPO::getCurrentAuditor,applyInfoPO.getCurrentAuditor());
        updateWrapper.set(ApplyInfoPO::getUpdater,applyInfoPO.getUpdater());
        updateWrapper.set(ApplyInfoPO::getUpdateTime,new Date().getTime());
        return this.update(updateWrapper);

    }

    /** 提交默认申请单信息(落库但不会触发调用joySky审批流)*/
    public ApplyInfoPO submitDefaultApplyInfo(Long id, JoySkyBizFlowTypeEnum flowTypeEnum){
        ApplyInfoPO defaultApplyInfo = getDefaultApplyInfoPo(id, flowTypeEnum);
        boolean res = this.save(defaultApplyInfo);
        if (!res) {
            log.error("记录审核信息失败, applyInfoPO={}", JSONObject.toJSONString(defaultApplyInfo));
            throw new ProductBizException("记录审核信息失败，请您稍后重试！！！");
        }

        return defaultApplyInfo;
    }

    /** 获取不走joySku审批流时的默认占位审批单信息*/
    private ApplyInfoPO getDefaultApplyInfoPo(Long id, JoySkyBizFlowTypeEnum flowTypeEnum){
        ApplyInfoPO applyInfoPO = new ApplyInfoPO();
        applyInfoPO.setProcessType(flowTypeEnum.getFlowTypeCode());
        applyInfoPO.setBizId(id.toString());
        applyInfoPO.setProcessInstanceId(UUID.randomUUID().toString());
        applyInfoPO.setApplyCode("DEFAULT-"+UUID.randomUUID());
        applyInfoPO.setAuditStatus(AuditStatusEnum.APPROVED.getCode());
        applyInfoPO.setVersion(0);
        applyInfoPO.setCreator(LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoPO.setCreateTime(new Date().getTime());
        applyInfoPO.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoPO.setUpdateTime(new Date().getTime());
        return applyInfoPO;
    }

    public ApplyInfoPO updateAuditPassStatus(ApplyInfoPO applyInfoPO) {
        if(applyInfoPO == null || applyInfoPO.getId() == null){
            log.warn("updateAuditPassStatus fail, id is null, applyInfoPO={}", JSONObject.toJSONString(applyInfoPO));
            throw new ProductBizException("不存在审批记录");
        }
        LambdaUpdateWrapper<ApplyInfoPO> updateWrapper = Wrappers.<ApplyInfoPO>lambdaUpdate();
        updateWrapper.eq(ApplyInfoPO::getId, applyInfoPO.getId());
        updateWrapper.eq(ApplyInfoPO::getVersion, applyInfoPO.getVersion());

        updateWrapper.set(ApplyInfoPO::getAuditStatus, AuditStatusEnum.APPROVED.getCode());
        updateWrapper.set(ApplyInfoPO::getUpdater, LoginContextHolder.getLoginContextHolder().getPin());
        updateWrapper.set(ApplyInfoPO::getUpdateTime, new Date().getTime());
        boolean update = this.update(updateWrapper);
        if (!update) {
            throw new ProductBizException("当前单据正在被其他用户修改，请刷新页面后重试");
        }

        ApplyInfoPO applyInfo = this.getValidById(applyInfoPO.getId());

        if (applyInfo == null) {
            log.error("error, applyInfo is null, applyInfoPO={}", JSONObject.toJSONString(applyInfoPO));
            throw new ProductBizException("不存在审批单据，请刷新页面重试！！！");
        }

        return applyInfo;
    }
}
