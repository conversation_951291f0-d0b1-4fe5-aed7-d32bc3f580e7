package com.jdi.isc.product.soa.service.protocol.jsf.agreementPrice;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.agreementPrice.IscCountryAgreementPriceReadApiService;
import com.jdi.isc.product.soa.api.agreementPrice.req.*;
import com.jdi.isc.product.soa.api.agreementPrice.res.*;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveOrderPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.*;
import com.jdi.isc.product.soa.domain.price.agreementPrice.draft.CountryAgreementPriceAuditResVO;
import com.jdi.isc.product.soa.service.adapter.mapstruct.agreementPrice.CountryAgreementPriceConvert;
import com.jdi.isc.product.soa.service.atomic.approveorder.ApproveOrderAtomicService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceCalculateService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceDraftManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceWarningManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 国家协议价api服务实现
 * @Author: wangpeng965
 * @Date: 2025/03/03 16:45
 **/

@Slf4j
@Service
public class IscCountryAgreementPriceReadApiServiceImpl implements IscCountryAgreementPriceReadApiService, InitializingBean {

    @Resource
    private CountryAgreementPriceManageService countryAgreementPriceManageService;

    @Resource
    private CountryAgreementPriceDraftManageService countryAgreementPriceDraftManageService;

    @Resource
    private CountryAgreementPriceWarningManageService countryAgreementPriceWarningManageService;


    @Resource
    private CountryAgreementPriceCalculateService countryAgreementPriceCalculateService;

    @Resource
    private ApproveOrderAtomicService approveOrderAtomicService;


    private static final String draft = "draft";
    private static final String formal = "formal";

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<CountryAgreementPricePageApiDTO>> pageSearch(CountryAgreementPricePageReqDTO dto){
        log.info("IscCountryAgreementPriceReadApiServiceImpl.pageSearch input:{}", JSONObject.toJSONString(dto));
        ApiInitUtils.init(dto);
        CountryAgreementPricePageReqVO reqVo = CountryAgreementPriceConvert.INSTANCE.pageReqDto2PageReqVo(dto);
        PageInfo<CountryAgreementPricePageVO> pageInfoRes = null;
        if(StringUtils.equals(reqVo.getTypeKey(),formal)){
            pageInfoRes = countryAgreementPriceManageService.pageSearch(reqVo);
        }else  if(StringUtils.equals(reqVo.getTypeKey(),draft)){
            pageInfoRes = countryAgreementPriceDraftManageService.pageSearch(reqVo);
        }
        PageInfo<CountryAgreementPricePageApiDTO> pageInfoApiRes = CountryAgreementPriceConvert.INSTANCE.pageVo2PageDto(pageInfoRes);
        return DataResponse.success(pageInfoApiRes);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<CountryAgreementPriceDTO> detail(CountryAgreementPriceReqDTO dto) {
        log.info("IscCountryAgreementPriceReadApiServiceImpl.detail input:{}", JSONObject.toJSONString(dto));
        ApiInitUtils.init(dto);
        CountryAgreementPriceReqVO reqVo = CountryAgreementPriceConvert.INSTANCE.reqDto2ReqVo(dto);
        CountryAgreementPriceVO detail = countryAgreementPriceManageService.detailById(reqVo);
        CountryAgreementPriceDTO countryAgreementPriceDTO = CountryAgreementPriceConvert.INSTANCE.vo2Dto(detail);
        return DataResponse.success(countryAgreementPriceDTO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<CountryAgreementPriceAuditApiDTO>> approvePageSearch(CountryAgreementPriceAuditReqApiDTO input) {
        log.info("IscCountryAgreementPriceReadApiServiceImpl.approvePage param:{}", com.alibaba.fastjson.JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        PageInfo<CountryAgreementPriceAuditResVO> resVO = countryAgreementPriceDraftManageService.approvePageSearch(CountryAgreementPriceConvert.INSTANCE.auditReqApiDto2Vo(input));
        return DataResponse.success(CountryAgreementPriceConvert.INSTANCE.auditResApiVo2Dto(resVO));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<CountryAgreementPriceWarningDTO>> warningPageSearch(CountryAgreementPriceWarningPageReqDTO input) {
        log.info("IscCountryAgreementPriceReadApiServiceImpl.warningPageSearch param:{}", com.alibaba.fastjson.JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        PageInfo<CountryAgreementPriceWarningVO> resVO = countryAgreementPriceWarningManageService.pageSearch(CountryAgreementPriceConvert.INSTANCE.warningReqApiDto2Vo(input));
        return DataResponse.success(CountryAgreementPriceConvert.INSTANCE.warningResApiVo2Dto(resVO));
    }

    @Override
    public DataResponse<List<CountryAgreementPriceWarningDTO>> listWarningsByApproveIds(CountryAgreementPriceWarningPageReqDTO input) {
        if (input == null || input.getApproveIds() == null || input.getApproveIds().isEmpty()) {
            return DataResponse.success(Lists.newArrayList());
        }

        List<ApproveOrderPO> list = approveOrderAtomicService.listByIds(input.getApproveIds());
        List<Long> warnIds = list.stream().map(item -> {
            if (StringUtils.isNumeric(item.getBizId())) {
                return Long.valueOf(item.getBizId());
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        List<CountryAgreementPriceWarningDTO> data = countryAgreementPriceWarningManageService.listWarningsByIds(warnIds);
        return DataResponse.success(data);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
//        String json = "{\"index\":1,\"lang\":\"zh\",\"offset\":0,\"pin\":\"liuzhaoming.10\",\"size\":20,\"systemCode\":\"WIMP\"}";
//        CountryAgreementPriceAuditReqApiDTO query = JSONObject.parseObject(json, CountryAgreementPriceAuditReqApiDTO.class);
//        DataResponse<PageInfo<CountryAgreementPriceAuditApiDTO>> response = this.approvePageSearch(query);
//
//        log.info("approvePageSearch response={}", JSONObject.toJSONString(response));
    }

    /*@ToolKit(exceptionWrap = true)
    @Override
    public DataResponse<AgreementAndCostPriceDTO> calculateAgreementAndCostPrice(CalculateAgreementPriceReqDTO dto) {
        log.info("IscCountryAgreementPriceReadApiServiceImpl.calculateAgreementAndCostPrice param:{}", com.alibaba.fastjson.JSONObject.toJSONString(dto));
        InitAgreementPriceVO initAgreementPriceVO = CountryAgreementPriceConvert.INSTANCE.calculateReqDto2Vo(dto);
        AgreementAndCostPriceVO agreementAndCostPriceVO = countryAgreementPriceCalculateService.calculateAgreementAndCostPrice(initAgreementPriceVO);
        AgreementAndCostPriceDTO agreementAndCostPriceDTO = CountryAgreementPriceConvert.INSTANCE.vo2Dto(agreementAndCostPriceVO);
        return DataResponse.success(agreementAndCostPriceDTO);
    }*/

}
