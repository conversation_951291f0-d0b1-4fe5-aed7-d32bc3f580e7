package com.jdi.isc.product.soa.service.manage.taxRate;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.taxRate.req.BrImportTaxReqDTO;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRatePageVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRateVO;

import java.util.List;

/**
 * @Description: 税率信息对外服务
 * @Author: wangpeng965
 * @Date: 2024/11/08 17:51
 **/
public interface TaxRateManageService {

    /**
     * 保存、更新
     * @param input 表单参数
     * @return 响应结果
     */
    DataResponse<TaxRateVO> saveOrUpdate(TaxRateVO input);

    /**
     * 批量保存或更新税率信息。
     * @param voList 待保存或更新的税率信息列表。
     * @return 保存或更新操作的结果。
     */
    DataResponse<Boolean> saveOrUpdateBatch(List<TaxRateVO> voList);

    /**
     * 根据税率请求对象获取详细的税率信息。
     * @param vo 税率请求对象，包含查询条件。
     * @return TaxRateVO 对象，包含税率的详细信息。
     */
    TaxRateVO getDetailById(TaxRateVO vo);
    /**
     * 分页获取税率列表
     * @return 分页结果，包含税率信息
     */
    PageInfo<TaxRateVO> pageTaxRate(TaxRatePageVO vo);

    /**
     * 获取税率列表
     * @param vo TaxRateVO 对象，用于查询条件
     * @return TaxRateVO 列表
     */
    List<TaxRateVO> listTaxRate(TaxRateVO vo);

    /**
     * 删除税率信息。
     * @param dto TaxRateReqDTO 对象，包含要删除的税率信息。
     * @return Boolean 类型，表示操作是否成功。
     */
    Boolean delete(TaxRateVO vo);

    /**
     * 根据唯一key判断是否已存在
     * @param vo
     * @return
     */
    Boolean exists(TaxRateVO vo);

    List<TaxRateVO> listByCountryCodeAndSkuId(TaxRateVO vo);

    /**
     * 更新进口商品税率信息。
     * @param input 进口商品税率更新请求对象。
     * @return 更新是否成功。
     */
    Boolean updateBrIpiTax(BrImportTaxReqDTO input);
}
