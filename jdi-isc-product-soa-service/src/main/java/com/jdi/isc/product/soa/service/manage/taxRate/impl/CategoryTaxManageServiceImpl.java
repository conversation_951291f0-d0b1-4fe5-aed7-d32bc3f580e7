package com.jdi.isc.product.soa.service.manage.taxRate.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.domain.category.biz.CategoryComboBoxVO;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.supplier.biz.CategoryFullNodeVO;
import com.jdi.isc.product.soa.domain.task.excel.ImportVNCategoryTaxExcelDTO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CategoryTaxPageVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CategoryTaxVO;
import com.jdi.isc.product.soa.domain.taxRate.po.CategoryTaxPO;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.*;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.CategoryTaxManageService;
import com.jdi.isc.product.soa.service.mapstruct.category.CategoryFullNodeConvert;
import com.jdi.isc.product.soa.service.mapstruct.taxRate.CategoryTaxConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 类目税率信息数据维护服务实现
 * @Author: taxuezheng1
 * @Date: 2024/03/26 17:51
 **/

@Slf4j
@Service
public class CategoryTaxManageServiceImpl extends BaseManageSupportService<CategoryTaxVO, CategoryTaxPO> implements CategoryTaxManageService {

    @Resource
    private CategoryTaxAtomicService categoryTaxAtomicService;
    @Resource
    private CategoryOutService categoryOutService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private CustomerManageService customerManageService;

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> saveOrUpdate(CategoryTaxVO input) {
        log.info("saveOrUpdate, input={}", JSONObject.toJSONString(input));

        // 参数业务校验
        DataResponse<Boolean> checkResult = checkAndInitInput(input);
        if (!checkResult.getSuccess()){
            log.info("saveOrUpdate, check input fail, input={}, checkResult={}", JSONObject.toJSONString(input), JSONObject.toJSONString(checkResult));
            return checkResult;
        }

        // 保存对象
        CategoryTaxPO po = CategoryTaxConvert.INSTANCE.vo2Po(input);
        //增值税，货源国增值税
        if (Objects.nonNull(po.getVatRate())) {
            po.setVatRate(po.getVatRate().divide(new BigDecimal(100)));
        }

        //目的国增值税
        if (Objects.nonNull(po.getDestinationVatRate())) {
            po.setDestinationVatRate(po.getDestinationVatRate().divide(new BigDecimal(100)));
        }
        boolean saveRes = categoryTaxAtomicService.saveOrUpdate(po);
        if (!saveRes){
            log.warn("saveOrUpdate, CategoryTaxPO fail. CategoryTaxPO={}", JSONObject.toJSONString(po));
            throw new BizException("保存失败");
        }

        return DataResponse.success(true);
    }

    @Override
    public CategoryTaxVO detail(Long id) {
        CategoryTaxPO po = categoryTaxAtomicService.getValidById(id);
        if (null==po){
            log.info("detail, CategoryTaxPO null. id={}", id);
            return null;
        }
        CategoryTaxVO vo = CategoryTaxConvert.INSTANCE.po2Vo(po);
        Set<Long> param = new HashSet<>();
        param.add(vo.getCategoryId());
        Map<Long, List<CategoryComboBoxVO>> langNameMap = categoryOutService.queryPath(param, LangConstant.LANG_ZH);
        List<CategoryFullNodeVO> categoryFullNodeVOS = CategoryFullNodeConvert.buildBusinessLineCategoryVO(langNameMap);
        if (CollectionUtils.isNotEmpty(categoryFullNodeVOS)) {
            CategoryFullNodeVO categoryFullNodeVO = categoryFullNodeVOS.get(0);
            vo.setFullNodeVO(categoryFullNodeVO);
            vo.setFullCategoryName(buildCateFullName(categoryFullNodeVO));
        }
        //增值税，货源国增值税
        if (Objects.nonNull(po.getVatRate())) {
            vo.setVatRate(vo.getVatRate().multiply(new BigDecimal(100)));
        }
        //目的国增值税
        if (Objects.nonNull(po.getDestinationVatRate())) {
            vo.setDestinationVatRate(po.getDestinationVatRate().multiply(new BigDecimal(100)));
        }
        //设置客户名称
        if (StringUtils.isNotBlank(po.getClientCode())) {
            vo.setClientName(getClientName(po.getClientCode()));
        }
        return vo;
    }

    @Override
    public PageInfo<CategoryTaxPageVO.Response> page(CategoryTaxPageVO.Request input) {
        PageInfo<CategoryTaxPageVO.Response> output = new PageInfo<>();
        output.setSize(input.getSize());
        output.setIndex(input.getIndex());

        // 查询列表
        Page<CategoryTaxPO> pageDB = new Page<>(input.getIndex(), input.getSize());
        LambdaQueryWrapper<CategoryTaxPO> wrapper = Wrappers.<CategoryTaxPO>lambdaQuery()
                .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode())
                .eq(Objects.nonNull(input.getCategoryId()),CategoryTaxPO::getJdCatId,input.getCategoryId())
                .eq(Objects.nonNull(input.getCompanyType()),CategoryTaxPO::getCompanyType,input.getCompanyType())
                .eq(Objects.nonNull(input.getClientCode()),CategoryTaxPO::getClientCode,input.getClientCode())
                .eq(Objects.nonNull(input.getCountryCode()),CategoryTaxPO::getCountryCode,input.getCountryCode())
                .eq(Objects.nonNull(input.getHsCode()),CategoryTaxPO::getHsCode,input.getHsCode())
                .eq(Objects.nonNull(input.getSkuId()),CategoryTaxPO::getSkuId,input.getSkuId())
                .orderByDesc(CategoryTaxPO::getUpdateTime);
        Page<CategoryTaxPO> dbRecord = categoryTaxAtomicService.page(pageDB, wrapper);
        output.setTotal(dbRecord.getTotal());
        if (CollectionUtils.isEmpty(dbRecord.getRecords())){
            return output;
        }

        List<CategoryTaxPageVO.Response> pageList = CategoryTaxConvert.INSTANCE.pageListPo2Vo(dbRecord.getRecords());
        if (CollectionUtils.isNotEmpty(pageList)){
            //填充类目信息
            Set<Long> cateSet = pageList.stream().collect(HashSet::new,(m,v)->m.add(v.getCategoryId()),HashSet::addAll);
            Map<Long, List<CategoryComboBoxVO>> langNameMap = categoryOutService.queryPath(cateSet, LangConstant.LANG_ZH);
            List<CategoryFullNodeVO> categoryFullNodeVOS = CategoryFullNodeConvert.buildBusinessLineCategoryVO(langNameMap);
            Map<Long, CategoryFullNodeVO> fullMap = categoryFullNodeVOS.stream().collect(HashMap::new, (m, v) -> m.put(v.getFourCategoryId(), v), HashMap::putAll);
            pageList.forEach(p -> {
                CategoryFullNodeVO categoryFullNode = fullMap.get(p.getCategoryId());
                p.setFullNodeVO(categoryFullNode);
                p.setFullCategoryName(buildCateFullName(categoryFullNode));
                if (Objects.nonNull(p.getVatRate())) {
                    p.setVatRate(p.getVatRate().multiply(new BigDecimal(100)));
                }
                //目的国增值税
                if (Objects.nonNull(p.getDestinationVatRate())) {
                    p.setDestinationVatRate(p.getDestinationVatRate().multiply(new BigDecimal(100)));
                }
                //设置客户名称
                if (StringUtils.isNotBlank(p.getClientCode())) {
                    p.setClientName(getClientName(p.getClientCode()));
                }
            });
            output.setRecords(pageList);
        }

        return output;
    }


    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean remove(Long id) {
        return categoryTaxAtomicService.removeById(id);
    }


    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean validData(Long id) {
        LambdaUpdateWrapper<CategoryTaxPO> wrapper = Wrappers.<CategoryTaxPO>lambdaUpdate()
                .eq(CategoryTaxPO::getId, id)
                .set(CategoryTaxPO::getYn, YnEnum.NO.getCode())
             ;
        return categoryTaxAtomicService.update(wrapper);
    }

    @Override
    public void updateYn(List<Long> skuIds, String updater) {
        log.info("更新品类税率yn=0, skuIds={}, updater={}", skuIds, updater);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(skuIds)) {
            log.info("更新品类税率yn=0, skuIds is empty");
            return;
        }

        List<CategoryTaxPO> list = categoryTaxAtomicService.listBySkuIds(skuIds);

        List<Long> ids = list.stream().map(CategoryTaxPO::getId).collect(Collectors.toList());

        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(ids)) {
            log.info("更新品类税率yn=0, ids is empty");
            return;
        }

        LambdaUpdateWrapper<CategoryTaxPO> wrapper = Wrappers.<CategoryTaxPO>lambdaUpdate()
                .in(CategoryTaxPO::getId, ids);
        CategoryTaxPO update = new CategoryTaxPO();
        update.setYn(YnEnum.NO.getCode());
        update.setUpdateInfo(updater);

        boolean result = categoryTaxAtomicService.update(update, wrapper);
        log.info("更新品类税率yn=0, 更新结果={}, ids={}, updater={}", result, ids, updater);
        if (!result) {
            log.error("更新品类税率yn=0, 更新失败. id={}, updater={}", ids, updater);
            throw new BizException("更新品类税率失败");
        }
    }

    /**
     * 参数校验
     * @param input 提交参数
     * @return 检查结果
     */
    private DataResponse<Boolean> checkAndInitInput(CategoryTaxVO input) {
        if (CountryConstant.COUNTRY_VN.equals(input.getCountryCode())) {
            //泰国 校验目的国增值税率
            if (Objects.isNull(input.getCompanyType())) {
                return DataResponse.error("企业性质不能为空");
            }
        }
        //越南
        //国家+企业性质+类目+skuId+目的国 货源国增值税率 分别只能有一条。
        //国家+企业性质+类目+目的国 货源国增值税率 在skuid为空时分别只能有一条。
        //泰国
        //国家+类目+客户编码+目的国/货源国增值税率 分别只能有一条。
        //抽象模型:统一查询维度
        //国家+企业性质+客户编码+类目+skuId
        List<CategoryTaxPO> taxPOList = queryCategoryTax(input);
        if (CollectionUtils.isNotEmpty(taxPOList)) {
            if (taxPOList.size() > 1) {
                return DataResponse.error("当前类目存在重复的税率");
            }
            //是否为匹配更新
            Boolean matchAndUpdate = input.getMatchAndUpdate() != null && input.getMatchAndUpdate();
            CategoryTaxPO categoryTaxPO = taxPOList.get(0);
            if (!matchAndUpdate && !categoryTaxPO.getId().equals(input.getId())) {
                return DataResponse.error("当前类目已存在税率，请返回修改");
            }else{
                input.setId(categoryTaxPO.getId());
            }
        }

        //校验skuId配置的类目是否正确
        if (Objects.nonNull(input.getSkuId())) {
            SkuPO skuPO = skuAtomicService.getSkuPoBySkuId(input.getSkuId());
            if (null == skuPO) {
                return DataResponse.error("skuId不存在！");
            }
            if (!input.getCategoryId().equals(skuPO.getJdCatId())) {
                return DataResponse.error("skuId关联的类目关系不正确！");
            }
        }

        return DataResponse.success();
    }


    private List<CategoryTaxPO> queryCategoryTax(CategoryTaxVO taxVO) {
        LambdaQueryWrapper<CategoryTaxPO> wrapper = Wrappers.<CategoryTaxPO>lambdaQuery()
                .eq(CategoryTaxPO::getCountryCode,taxVO.getCountryCode())
                .eq(Objects.nonNull(taxVO.getCompanyType()),CategoryTaxPO::getCompanyType,taxVO.getCompanyType())
                .isNull(Objects.isNull(taxVO.getCompanyType()),CategoryTaxPO::getCompanyType)
                .eq(Objects.nonNull(taxVO.getClientCode()),CategoryTaxPO::getClientCode,taxVO.getClientCode())
                .isNull(Objects.isNull(taxVO.getClientCode()),CategoryTaxPO::getClientCode)
                .eq(CategoryTaxPO::getJdCatId,taxVO.getCategoryId())
                .eq(Objects.nonNull(taxVO.getSkuId()),CategoryTaxPO::getSkuId,taxVO.getSkuId())
                .isNull(Objects.isNull(taxVO.getSkuId()),CategoryTaxPO::getSkuId)
                .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode())
                ;
        return categoryTaxAtomicService.list(wrapper);
    }

    /**
     * 构建类别全名
     */
    private String buildCateFullName(CategoryFullNodeVO categoryFullNode){
        if (Objects.isNull(categoryFullNode)) {
            return null;
        }
        StringBuilder sb = new StringBuilder(categoryFullNode.getFirstCategoryName());
        if (StringUtils.isNotBlank(categoryFullNode.getSecondCategoryName())) {
            sb.append(">").append(categoryFullNode.getSecondCategoryName());
        }
        if (StringUtils.isNotBlank(categoryFullNode.getThreeCategoryName())) {
            sb.append(">").append(categoryFullNode.getThreeCategoryName());
        }
        if (StringUtils.isNotBlank(categoryFullNode.getFourCategoryName())) {
            sb.append(">").append(categoryFullNode.getFourCategoryName());
        }
        return sb.toString();
    }


    @Override
    public void template(String type, HttpServletResponse response) {
        try {
            if (CountryConstant.COUNTRY_VN.equals(type)) {
                String fileName = "vn_categoryRateTemplate" + Constant.DOT + Constant.XLSX;
                response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));
                EasyExcel.write(response.getOutputStream(),ImportVNCategoryTaxExcelDTO.class).sheet("类目税率").doWrite(new ArrayList<>());
            }
        } catch (Exception e) {
            log.error("【系统异常】下载模板异常，e:",e);
        }
    }


    /**
     * 根据ClientCode 获取客户名称
     */
    private String getClientName(String clientCode){
        CustomerVO customerVO = customerManageService.detail(clientCode);
        if (null != customerVO) {
            return customerVO.getClientName();
        }
        return null;
    }
}
