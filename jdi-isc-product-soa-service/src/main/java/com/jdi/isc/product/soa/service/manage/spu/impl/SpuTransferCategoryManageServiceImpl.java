package com.jdi.isc.product.soa.service.manage.spu.impl;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.CategoryAttrInputTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuTaxAuditStatusEnum;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.ProductConstant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.domain.category.biz.CategoryVO;
import com.jdi.isc.product.soa.domain.category.biz.TransferCategoryLogVO;
import com.jdi.isc.product.soa.domain.enums.CategoryLevelEnum;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.sku.biz.GroupSkuCertificateVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCertificateVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuCertificatePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.ProductGlobalAttributePO;
import com.jdi.isc.product.soa.domain.spu.po.SpuCertificatePO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.taxRate.po.CategoryTaxPO;
import com.jdi.isc.product.soa.service.atomic.category.CategoryBuyerRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuCertificateAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.ProductGlobalAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuCertificateAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.CategoryTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.category.TransferCategoryLogManageService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuDraftManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuTransferCategoryManageService;
import com.jdi.isc.product.soa.service.manage.supplier.BusinessLineManageService;
import com.jdi.isc.product.soa.service.mapstruct.ProductAttributeConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SkuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.Constant.FACTORIAL;

/**
 * <AUTHOR>
 * @description：SPU类目迁移服务实现类
 * @Date 2024-10-27
 */
@Slf4j
@Service
public class SpuTransferCategoryManageServiceImpl implements SpuTransferCategoryManageService {

    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    private MkuAtomicService mkuAtomicService;
    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;
    @Resource
    private SpuCertificateAtomicService spuCertificateAtomicService;
    @Resource
    private CategoryTaxAtomicService categoryTaxAtomicService;
    @Resource
    private SkuCertificateAtomicService skuCertificateAtomicService;
    @Resource
    private TransferCategoryLogManageService transferCategoryLogManageService;
    @Resource
    private SpuConvertService spuConvertService;
    @Resource
    private SkuConvertService skuConvertService;
    @Resource
    private CategoryOutService categoryOutService;
    @Resource
    private BusinessLineManageService businessLineManageService;
    @Resource
    private SpuReadManageService spuReadManageService;
    @Resource
    private SpuDraftManageService spuDraftManageService;
    @Resource
    private ProductAttributeConvertService productAttributeConvertService;
    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;
    @LafValue("jdi.isc.product.soa.transferCategory.auditStatus.enable")
    private Boolean validateAuditStatus;
    @Resource
    private CategoryBuyerRelationAtomicService categoryBuyerRelationAtomicService;

    @Resource
    private SaleAttributeManageService saleAttributeManageService;

    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 60)
    public DataResponse<Boolean> transferCategory(TransferCategoryReqVO reqVO) {
        DataResponse<Boolean> validate = this.validate(reqVO);
        if (!validate.getSuccess()) {
            return validate;
        }

        Long spuId = reqVO.getSpuId();
        // 查询SPU
        SpuPO spuPo = spuAtomicService.getSpuPoBySpuId(spuId);
        if (spuPo == null) {
            return DataResponse.error("SPU信息获取失败");
        }
        // 查询目标类目的所有属性
        SpuPrepareInfoVO prepareVo = spuReadManageService.getPrepareVo(reqVO.getTargetCatId(), spuPo.getAttributeScope(), spuPo.getSourceCountryCode(), spuPo.getIsExport(),spuPo.getVendorCode());
        if (Objects.isNull(prepareVo)) {
            return DataResponse.error(String.format("类目%s不存在有效的属性",reqVO.getTargetCatId()));
        }
        if (CollectionUtils.isEmpty(prepareVo.getSalePropertyList())) {
            return DataResponse.error(String.format("类目%s不存在有效的销售属性",reqVO.getTargetCatId()));
        }
        // 执行迁移数据
        this.processTransfer(reqVO, prepareVo, spuPo);

        return DataResponse.success();
    }

    /**
     * 处理SPU迁移及相关信息。
     * @param reqVO 转移请求对象
     * @param prepareVo 准备信息对象
     * @param spuPo SPU信息对象
     */
    private void processTransfer(TransferCategoryReqVO reqVO, SpuPrepareInfoVO prepareVo, SpuPO spuPo) {
        // 处理SPU迁移，主要是扩展属性
        this.transferSpu(reqVO, prepareVo, spuPo);
        // 处理跨境属性列表
        this.transferGlobalAttributeList(reqVO, prepareVo);
        // 处理资质列表
        this.transferSpuCertificationList(reqVO, prepareVo);
        // 处理SKU销售属性、跨境属性、资质列表，以及新版扩展属性
        this.transferSkuList(reqVO, prepareVo);
        // 处理SPU草稿, 已经在上一步处理了spu、sku的扩展属性，所以在这里直接读取的数据库信息赋值到了草稿，不需要单独处理草稿扩展属性了
        this.transferSpuDraft(reqVO, spuPo);
        // 更新MKU、MKU草稿没有存储类目相关数据不用处理
        this.transferMku(reqVO);
        // 更新越南商品类目税率和SKU的关系
        this.transferSkuTax(spuPo, reqVO);
        // 记录迁移信息
        this.recordTransferLog(reqVO, spuPo);
    }

    /**
     * 记录转移类目日志
     * @param reqVO 转移类目请求对象
     * @param spuPo SPuPO 对象
     */
    private void recordTransferLog(TransferCategoryReqVO reqVO, SpuPO spuPo) {
        transferCategoryLogManageService.saveOrUpdate(new TransferCategoryLogVO(reqVO,spuPo.getJdCatId()));
    }

    /**
     * 执行MKU转移操作
     * @param reqVO 转移请求对象，包含转移的相关信息
     */
    private void transferMku(TransferCategoryReqVO reqVO) {

        List<SkuPO> skuPOList = skuAtomicService.selectSkuPosBySpuId(reqVO.getSpuId());
        if (CollectionUtils.isEmpty(skuPOList)) {
            return;
        }

        Set<Long> skuIds = skuPOList.stream().filter(Objects::nonNull).map(SkuPO::getSkuId).collect(Collectors.toSet());
        Map<Long, Long> skuMkuRelationMap = mkuRelationAtomicService.queryMkuIdBySkuIds(skuIds);
        if (MapUtils.isEmpty(skuMkuRelationMap)) {
            return;
        }
        log.info("transferMku spuId={},targetCategoryId={}, 迁移SKU的MKU,SKU和MKU绑定关系{}",reqVO.getSpuId(),reqVO.getTargetCatId(), JSON.toJSONString(skuMkuRelationMap));
        // 查询SPU
        SpuPO spuPo = spuAtomicService.getSpuPoBySpuId(reqVO.getSpuId());
        Map<Long, SkuPO> skuMap = Optional.of(skuPOList).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(SkuPO::getSkuId, Function.identity()));
        // 迁移SKU的MKU
        for (Map.Entry<Long, Long> entry : skuMkuRelationMap.entrySet()) {
            Long skuId = entry.getKey();
            Long mkuId = entry.getValue();
            // 查询MKU绑定的SKU列表
            List<MkuRelationPO> mkuRealtionPoList = mkuRelationAtomicService.getMkuByMkuId(mkuId);
            if (CollectionUtils.isEmpty(mkuRealtionPoList)) {
                log.info("transferMku spuId={},targetCategoryId={}, mkuId={} 未查到绑定关系的sku数据",reqVO.getSpuId(),reqVO.getTargetCatId(), mkuId);
                continue;
            }
            // 迁移绑定一个SKU的MKU
            int size = mkuRealtionPoList.size();
            if (size == 1){
                SkuPO skuPO = skuMap.get(skuId);
                MkuPO mkuPO = mkuAtomicService.getPOById(mkuId);
                mkuPO.setCatId(reqVO.getTargetCatId());
                mkuPO.setJdCatId(reqVO.getTargetCatId());
                mkuPO.setBuyer(spuPo.getBuyer());
                // 处理新版扩展属性
                String mkuExtAttribute = ExtendPropertyGroupVO.obtainStringMergeExtProperty(spuPo.getGroupExtAttribute(), skuPO.getGroupExtAttribute());
                mkuPO.setGroupExtAttribute(mkuExtAttribute);
                mkuPO.setSaleAttribute(skuPO.getSaleAttribute());
                mkuPO.setUpdater(reqVO.getUpdater());
                mkuPO.setUpdateTime(new Date());
                mkuAtomicService.saveOrUpdate(mkuPO);
            }else if (size > 1){
                // TODO 多个SKU时暂时不处理
                throw new BizException("该商品的MKU在多个不同的SKU，无法迁移，请联系产研协助解决。");
            }
        }

        // 迁移多个SKU的MKU
    }


    /**
     * 将SPU草稿从PO转移到DB中。
     * @param reqVO 转移请求对象，包含SPU ID等信息。
     * @param spuPo SPU PO对象，包含属性范围和是否导出等信息。
     */
    private void transferSpuDraft(TransferCategoryReqVO reqVO, SpuPO spuPo) {
        SpuDetailReqVO detailReqVO = new SpuDetailReqVO();
        detailReqVO.setSpuId(reqVO.getSpuId());
        detailReqVO.setLang(LangConstant.LANG_ZH);
        detailReqVO.setAttributeScope(spuPo.getAttributeScope());
        detailReqVO.setIsExport(spuPo.getIsExport());
        SpuDetailVO spuDetailVo = spuReadManageService.getSpuDetailVOFromDb(detailReqVO);
        spuDraftManageService.saveOrUpdateDraft(spuDetailVo);
    }

    /**
     * 将商品转移到新的类目，并更新其扩展属性和跨境属性。
     * @param reqVO 转移请求对象，包含商品ID、目标类目ID和更新人信息。
     * @param prepareVo 准备信息对象，包含商品的扩展属性和内部属性列表。
     * @param spuPo 商品PO对象，用于获取原始的扩展属性和内部属性。
     */
    private void transferSpu(TransferCategoryReqVO reqVO, SpuPrepareInfoVO prepareVo, SpuPO spuPo) {
        // 组成新类目扩展属性
        String groupExtendAttribute = this.getGroupExtendAttribute(reqVO, prepareVo.getExtendPropertyList(), spuPo.getGroupExtAttribute());
        log.info("spuId={} targetCatId={} groupExtendAttribute={}", reqVO.getSpuId(), reqVO.getTargetCatId(), groupExtendAttribute);

        SpuPO updateSpuPo = new SpuPO();
        updateSpuPo.setSpuId(reqVO.getSpuId());
        updateSpuPo.setUpdater(reqVO.getUpdater());
        updateSpuPo.setCatId(reqVO.getTargetCatId());
        updateSpuPo.setJdCatId(reqVO.getTargetCatId());
        updateSpuPo.setBuyer(categoryBuyerRelationAtomicService.getBuyerByCountryCodeAndCatId(spuPo.getSourceCountryCode(),reqVO.getTargetCatId()));
        updateSpuPo.setUpdateTime(new Date());
        updateSpuPo.setGroupExtAttribute(groupExtendAttribute);
        spuAtomicService.saveOrUpdate(updateSpuPo);
    }

    /**
     * 处理 SKU 列表，更新销售属性和属性值。
     * @param reqVO 转移类目请求对象，包含 spuId 和 updater 信息。
     * @param prepareVo 准备信息对象，包含 salePropertyList。
     */
    private void transferSkuList(TransferCategoryReqVO reqVO, SpuPrepareInfoVO prepareVo) {

        Long spuId = reqVO.getSpuId();
        String updater = reqVO.getUpdater();
        List<SkuPO> skuPOList = skuAtomicService.selectSkuPosBySpuId(spuId);

        if (CollectionUtils.isEmpty(skuPOList)) {
            log.info("spuId={} 没有SKU ",spuId);
            return;
        }
        // 迁移spu销售属性 ZHAOYAN_SALE_ATTR
        saleAttributeManageService.transferSkuSaleAttribute(reqVO.getTargetCatId(), spuId);

        List<PropertyVO> extendPropertyList = prepareVo.getExtendPropertyList();

        // 一个SKU时，比对销售属性
        int size = skuPOList.size();
        for (SkuPO skuPO : skuPOList) {
            // 处理单个SKU的销售属性
//            this.handleSingleSkuSaleProperty(skuPO,  salePropertyList,size == 1);
            // 处理单个SKU的跨境属性
            this.handSingleSkuInterProperty(reqVO,skuPO, prepareVo.getSkuInterPropertyList());
            // 处理单个SKU的跨境资质
            this.handleSingleSkuCertification(skuPO, prepareVo.getGroupSkuCertificateVOList());
            // 处理sku新扩展属性
            skuPO.setGroupExtAttribute(getGroupExtendAttributeString(reqVO.getTargetCatId(), extendPropertyList, skuPO.getGroupExtAttribute()));
        }

        skuPOList.forEach(skuPO -> {
            skuPO.setSpuId(null);
            skuPO.setJdCatId(reqVO.getTargetCatId());
            skuPO.setUpdater(updater);
            skuPO.setUpdateTime(new Date());
        });
        log.info("spuId={} targetCatId={} skuPoList={}",spuId,reqVO.getTargetCatId(),JSON.toJSONString(skuPOList));
        // 更新sku信息
        skuAtomicService.saveOrUpdateBatch(skuPOList);
    }

    /**
     * 处理单个 SKU 的资质信息。
     * @param skuPo SKU 对象，包含 SKU 的基本信息。
     * @param skuCertificateVOList 资质信息列表，包含该 SKU 的所有认证信息。
     */
    private void handleSingleSkuCertification(SkuPO skuPo, List<GroupSkuCertificateVO> skuCertificateVOList) {
        List<SkuCertificatePO> skuCertificatePOList = skuCertificateAtomicService.queryListBySkuId(skuPo.getSkuId());
        if (CollectionUtils.isEmpty(skuCertificatePOList)) {
            log.info("handleSingleSkuCertification skuId={} 商品没有设置SKU资质",skuPo.getSkuId());
            return;
        }

        if (CollectionUtils.isEmpty(skuCertificateVOList)){
            log.info("handleSingleSkuCertification skuId={} 新类目没有SKU资质",skuPo.getSkuId());
            skuCertificateAtomicService.removeBySkuIds(Sets.newHashSet(skuPo.getSkuId()));
        }else {
            List<SkuCertificateVO> skuCertificateVOS = Lists.newArrayList();
            skuCertificateVOList.forEach(vo -> {
                if (CollectionUtils.isNotEmpty(vo.getSkuCertificateVOS())) {
                    skuCertificateVOS.addAll(vo.getSkuCertificateVOS());
                }
            });
            // SKU 资质映射
            Map<Long, SkuCertificateVO> skuCertificationMap = skuCertificateVOS.stream().filter(Objects::nonNull).collect(Collectors.toMap(SkuCertificateVO::getCertificateId, Function.identity()));
            log.info("handleSingleSkuCertification skuId={} 新类目所有资质 skuCertificationMap={}",skuPo.getSkuId(),JSON.toJSONString(skuCertificationMap));
            List<SkuCertificatePO> removeList = Lists.newArrayList();
            skuCertificatePOList.forEach(po-> {
                if (!skuCertificationMap.containsKey(po.getCertificateId())) {
                    removeList.add(po);
                }
            });
            log.info("handleSingleSkuCertification skuId={} 新类目没有的资质删除 removeList={}",skuPo.getSkuId(),JSON.toJSONString(removeList));
            if (CollectionUtils.isNotEmpty(removeList)) {
                // TODO 删除新类目没有的资质
                //skuCertificateAtomicService.removeBatchByIds(removeList);
            }
        }
    }


    /**
     * 处理单个 SKU 的跨境属性。
     * @param skuPo SKU 对象，包含了 SKU 的基本信息。
     * @param groupPropertyVOS SKU 的关联跨境属性列表。
     */
    private void handSingleSkuInterProperty(TransferCategoryReqVO reqVO,SkuPO skuPo, List<GroupPropertyVO> groupPropertyVOS) {
        String updater = reqVO.getUpdater();

        List<ProductGlobalAttributePO> skuGlobalAttributePOS = productGlobalAttributeAtomicService.getBySkuId(skuPo.getSkuId());
        if (CollectionUtils.isEmpty(skuGlobalAttributePOS)) {
            log.info("handSingleSkuInterProperty skuId={} 商品没有设置SKU跨境属性",skuPo.getSkuId());
            return;
        }
        if (CollectionUtils.isEmpty(groupPropertyVOS)){
            log.info("handSingleSkuInterProperty skuId={} 新类目没有SKU跨境属性",skuPo.getSkuId());
            skuGlobalAttributePOS.forEach(each -> {
                each.setKeyId(null);
                each.setUpdater(updater);
                each.setUpdateTime(new Date().getTime());
                each.setYn(YnEnum.NO.getCode());
            });
            // 删除新类目不存在的SPU资质
            productGlobalAttributeAtomicService.saveOrUpdateBatch(skuGlobalAttributePOS);
        } else {
            List<PropertyVO> spuProperties = groupPropertyVOS.stream()
                    .filter(groupProperty -> CollectionUtils.isNotEmpty(groupProperty.getPropertyVOS()))
                    .flatMap(groupProperty -> groupProperty.getPropertyVOS().stream())
                    .filter(property -> CollectionUtils.isNotEmpty(property.getPropertyValueVOList()))
                    .collect(Collectors.toList());

            Map<Long, ProductGlobalAttributePO> storeAttributeMap = skuGlobalAttributePOS.stream().filter(Objects::nonNull)
                    .collect(Collectors.toMap(ProductGlobalAttributePO::getAttributeId, Function.identity()));
            List<ProductGlobalAttributePO> removeList = Lists.newArrayList();
            for (PropertyVO propertyVO : spuProperties) {
                // SPU为绑定的资质放入删除列表
                if (!storeAttributeMap.containsKey(propertyVO.getAttributeId())) {
                    ProductGlobalAttributePO attributePO = storeAttributeMap.get(propertyVO.getAttributeId());
                    if(attributePO != null ){
                        removeList.add(attributePO);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(removeList)) {
                log.info("handSingleSkuInterProperty,skuId:{},removeList:{}",skuPo.getSkuId(), JSONObject.toJSONString(removeList));
                removeList.forEach(each-> {
                    each.setKeyId(null);
                    each.setUpdater(updater);
                    each.setUpdateTime(new Date().getTime());
                    each.setYn(YnEnum.NO.getCode());
                });
                // 删除新类目不存在的SPU资质
                productGlobalAttributeAtomicService.saveOrUpdateBatch(removeList);
            }
        }
    }

    /**
     * 处理单个 SKU PO 的销售属性，确保其与新类目销售属性一致。
     * @param skuPO SKU PO 对象
     * @param salePropertyList 新类目销售属性列表
     */
    private void handleSingleSkuSaleProperty(SkuPO skuPO, List<PropertyVO> salePropertyList, boolean onlyOneSku)  {
        String saleAttribute = skuPO.getSaleAttribute();
        Long spuId = skuPO.getSpuId();
        Map<Long, Long> storeSaleAttributeMap = skuConvertService.splitSaleAttribute(saleAttribute);
        // 销售属性为空，不能迁移
        AssertValidation.isEmpty(storeSaleAttributeMap, String.format("skuId=%s spuId=%s 销售属性为空", skuPO.getSkuId(), spuId));

        // 新类目销售属性ID和属性值集合映射
        Map<Long,Set<Long>> targetSalePropertyMap = salePropertyList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        PropertyVO::getAttributeId,
                        propertyVO -> Optional.ofNullable(propertyVO.getPropertyValueVOList())
                                .orElse(Collections.emptyList())
                                .stream()
                                .map(PropertyValueVO::getAttributeValueId)
                                .collect(Collectors.toSet())));
        log.info("spuId={} skuId={} 新类目销售属性关系={},旧类目销售属性关系={}",spuId,saleAttribute,JSON.toJSONString(targetSalePropertyMap),JSON.toJSONString(storeSaleAttributeMap));
        // 处理单读sku，且销售属性值为“其它”的情况,新类目没有“其它”销售属性
        if (this.shouldProcessOtherAttribute(onlyOneSku, storeSaleAttributeMap, targetSalePropertyMap)){
            boolean existOther = false;
            for (PropertyVO propertyVO : salePropertyList) {
                if (!existOther) {
                    Optional<PropertyValueVO> first = this.findOtherAttributeValue(propertyVO);
                    log.info("spuId={} skuId={} 新类目查找“其它”销售属性值 结果:{}",spuId,skuPO.getSkuId(),first.isPresent());
                    if (first.isPresent()){
                        existOther = true;
                        PropertyValueVO propertyValueVO = first.get();
                        saleAttribute = propertyValueVO.getAttributeId() + Constant.COLON + propertyValueVO.getAttributeValueId();
                        log.info("spuId={} skuId={} SKU旧类目销售属性值拼接:{} ,SKU新类目销售属性值拼接:{}",spuId,skuPO.getSkuId(),skuPO.getSaleAttribute(),saleAttribute);
//                        skuPO.setSaleAttribute(saleAttribute);
                    }
                }
            }
            AssertValidation.isTrue(!existOther,String.format("skuId=%s,spuId=%s 新类目没有“其它”销售属性值", skuPO.getSkuId(), spuId));
            // 如果校验通过，则跳过后面的校验
            return;
        }

        // 销售属性维度不同
        AssertValidation.isTrue(!targetSalePropertyMap.keySet().containsAll(storeSaleAttributeMap.keySet()),String.format("skuId=%s,spuId=%s SKU销售属性维度和新类目不同", skuPO.getSkuId(), spuId));
        // 校验销售属性值是否相同
        storeSaleAttributeMap.forEach((attributeId,valueId)-> AssertValidation.isTrue(!targetSalePropertyMap.containsKey(attributeId) || (CollectionUtils.isNotEmpty(targetSalePropertyMap.get(attributeId)) && !targetSalePropertyMap.get(attributeId).contains(valueId)) ,String.format("skuId=%s,spuId=%s 销售属性ID %s 中销售属性值ID %s 在新类目中不存在，请联系研发", skuPO.getSkuId(), spuId,attributeId,valueId)));
    }

    /**
     * 查找指定属性的其他销售属性值。
     * @param propertyVO 属性对象
     * @return 其他销售属性值，若不存在则返回空的Optional对象
     */
    @NotNull
    private Optional<PropertyValueVO> findOtherAttributeValue(PropertyVO propertyVO) {
        // 如果新类目上有销售属性
        return Optional.ofNullable(propertyVO.getPropertyValueVOList()).orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .filter(valueVo -> ProductConstant.OTHER.equals(valueVo.getAttributeValueName()))
                .findFirst();
    }

    /**
     * 判断是否应该处理其他属性。
     * @param onlyOneSku 是否只有一个 SKU。
     * @param storeSaleAttributeMap 存储销售属性的映射表。
     * @param targetSalePropertyMap 目标销售属性的映射表。
     * @return 如果满足条件则返回 true，否则返回 false。
     */
    private boolean shouldProcessOtherAttribute(boolean onlyOneSku, Map<Long, Long> storeSaleAttributeMap, Map<Long, Set<Long>> targetSalePropertyMap) {
        return onlyOneSku && storeSaleAttributeMap.size() == 1
                && storeSaleAttributeMap.containsKey(ProductConstant.OTHER_ATTRIBUTE_ID) && storeSaleAttributeMap.containsValue(ProductConstant.OTHER_ATTRIBUTE_VALUE_ID)
                && !targetSalePropertyMap.containsKey(ProductConstant.OTHER_ATTRIBUTE_ID);
    }

    /**
     * 处理商品税收信息的转移。
     * @param spuPo 商品PO对象，包含源国家代码和原始分类ID。
     * @param reqVO 转移请求VO对象，包含目标分类ID和更新人信息。
     */
    private void transferSkuTax(SpuPO spuPo, TransferCategoryReqVO reqVO) {
        if (CountryConstant.COUNTRY_VN.equals(spuPo.getSourceCountryCode())) {
            List<SkuPO> skuPOList = skuAtomicService.selectSkuPosBySpuId(reqVO.getSpuId());
            Set<Long> skuIds = skuPOList.stream().filter(Objects::nonNull).map(SkuPO::getSkuId).collect(Collectors.toSet());
            Long originCategoryId = spuPo.getJdCatId();
            List<CategoryTaxPO> categoryTaxPOList = categoryTaxAtomicService.listBySkuIdsAndCategoryId(skuIds, originCategoryId);
            if (CollectionUtils.isNotEmpty(categoryTaxPOList)) {
                categoryTaxPOList.forEach(each -> {
                    each.setCategoryId(reqVO.getTargetCatId());
                    each.setJdCatId(reqVO.getTargetCatId());
                    each.setUpdater(reqVO.getUpdater());
                    each.setUpdateTime(System.currentTimeMillis());
                });
                categoryTaxAtomicService.saveOrUpdateBatch(categoryTaxPOList);
            }
        }
    }

    /**
     * 将跨境属性列表转移到新的分类下，并更新或删除不再相关的SPU跨境属性。
     * @param reqVO 转移请求对象，包含更新人信息和SPU ID
     * @param prepareVo 准备信息对象，包含SPU的跨境属性列表
     */
    private void transferGlobalAttributeList(TransferCategoryReqVO reqVO, SpuPrepareInfoVO prepareVo) {
        String updater = reqVO.getUpdater();
        List<GroupPropertyVO> spuInterPropertyList = prepareVo.getSpuInterPropertyList();
        List<PropertyVO> spuProperties = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(spuInterPropertyList)){
            spuProperties = spuInterPropertyList.stream()
                    .filter(groupProperty -> CollectionUtils.isNotEmpty(groupProperty.getPropertyVOS()))
                    .flatMap(groupProperty -> groupProperty.getPropertyVOS().stream())
                    .filter(property -> CollectionUtils.isNotEmpty(property.getPropertyValueVOList()))
                    .collect(Collectors.toList());
        }

        List<ProductGlobalAttributePO> storeProductGlobalAttributePOS = productGlobalAttributeAtomicService.getBySpuId(reqVO.getSpuId());
        if(CollectionUtils.isNotEmpty(storeProductGlobalAttributePOS) && CollectionUtils.isEmpty(spuProperties)){
            storeProductGlobalAttributePOS.forEach(each -> {
                each.setKeyId(null);
                each.setUpdater(updater);
                each.setUpdateTime(new Date().getTime());
                each.setYn(YnEnum.NO.getCode());
            });
            // 删除新类目不存在的SPU资质
            productGlobalAttributeAtomicService.saveOrUpdateBatch(storeProductGlobalAttributePOS);
        }else if (CollectionUtils.isNotEmpty(spuProperties) && CollectionUtils.isNotEmpty(storeProductGlobalAttributePOS)) {
            Map<Long, ProductGlobalAttributePO> storeCertificationMap = storeProductGlobalAttributePOS.stream().filter(Objects::nonNull).collect(Collectors.toMap(ProductGlobalAttributePO::getAttributeId, Function.identity()));
            List<ProductGlobalAttributePO> removeList = Lists.newArrayList();
            for (PropertyVO propertyVO : spuProperties) {
                // SPU为绑定的资质放入删除列表
                if (!storeCertificationMap.containsKey(propertyVO.getAttributeId())) {
                    ProductGlobalAttributePO productGlobalAttributePO = storeCertificationMap.get(propertyVO.getAttributeId());
                    if(productGlobalAttributePO != null){
                        removeList.add(productGlobalAttributePO);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(removeList)) {
                removeList.forEach(each-> {
                    each.setKeyId(null);
                    each.setUpdater(updater);
                    each.setUpdateTime(new Date().getTime());
                    each.setYn(YnEnum.NO.getCode());
                });
                // 删除新类目不存在的SPU资质
                productGlobalAttributeAtomicService.saveOrUpdateBatch(removeList);
            }
        }
    }

    /**
     * 处理SPU证书列表的方法。
     * @param reqVO 迁移商品信息
     * @param prepareVo SpuPrepareInfoVO对象，包含SPU证书的相关信息。
     */
    private void transferSpuCertificationList(TransferCategoryReqVO reqVO, SpuPrepareInfoVO prepareVo) {
        String updater = reqVO.getUpdater();
        List<SpuCertificateVO> spuCertificateVOList = prepareVo.getSpuCertificateVOList();
        List<SpuCertificatePO> storeCertificatePoList = spuCertificateAtomicService.listCertificateBySpuId(reqVO.getSpuId());
        if (CollectionUtils.isEmpty(spuCertificateVOList) && CollectionUtils.isNotEmpty(storeCertificatePoList)) {
            storeCertificatePoList.forEach(each -> {
                each.setSpuId(null);
                each.setUpdater(updater);
                each.setUpdateTime(new Date());
                each.setYn(YnEnum.NO.getCode());
            });
            // 删除新类目不存在的SPU资质
             spuCertificateAtomicService.saveOrUpdateBatch(storeCertificatePoList);
        }else if (CollectionUtils.isNotEmpty(spuCertificateVOList) && CollectionUtils.isNotEmpty(storeCertificatePoList)) {
            Map<Long, SpuCertificatePO> storeCertificationMap = storeCertificatePoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(SpuCertificatePO::getCertificateId, Function.identity()));
            List<SpuCertificatePO> removeList = Lists.newArrayList();
            for (SpuCertificateVO spuCertificateVO : spuCertificateVOList) {
                // SPU为绑定的资质放入删除列表
                if (!storeCertificationMap.containsKey(spuCertificateVO.getCertificateId())) {
                    removeList.add(storeCertificationMap.get(spuCertificateVO.getCertificateId()));
                }
            }

            if (CollectionUtils.isNotEmpty(removeList)) {
                removeList.forEach(each-> {
                    each.setSpuId(null);
                    each.setUpdater(updater);
                    each.setUpdateTime(new Date());
                    each.setYn(YnEnum.NO.getCode());
                });
                // 删除新类目不存在的SPU资质
                 spuCertificateAtomicService.saveOrUpdateBatch(removeList);
            }
        }
    }

    /**
     * 根据传入的类目信息和商品绑定的扩展属性，获取商品的扩展属性。
     * @param reqVO 转移类目请求对象，包含目标类目ID等信息。
     * @param extendPropertyList 新类目的扩展属性列表。
     * @param storeGroupExtAttribute 商品绑定的扩展属性字符串。
     * @return 商品的扩展属性字符串。
     */
    private String getGroupExtendAttribute(TransferCategoryReqVO reqVO, List<PropertyVO> extendPropertyList, String storeGroupExtAttribute) {
        String extendAttribute = "";
        // 3、校验类目是否存在相关扩展属性、资质
        if (CollectionUtils.isEmpty(extendPropertyList)) {
            log.info("类目{}不存在扩展属性", reqVO.getTargetCatId());
            return extendAttribute;
        }
        // 根据原存储的扩展属性json字符串，返回其中在新类目扩展属性中存在的属性
        return extendAttribute = getGroupExtendAttributeString(reqVO.getTargetCatId(), extendPropertyList, storeGroupExtAttribute);
    }

    /**
     * 根据商品绑定的扩展属性和新类目扩展属性列表，生成匹配后的扩展属性字符串
     * @param extendPropertyList 新类目的扩展属性列表
     * @param storeGroupExtAttribute 商品绑定的扩展属性JSON字符串
     * @return 匹配处理后的扩展属性字符串
     */
    private String getGroupExtendAttributeString(Long targetCatId, List<PropertyVO> extendPropertyList, String storeGroupExtAttribute) {
        String extendAttribute ="";
        // 商品绑定的扩展属性
        List<ExtendPropertyGroupVO> storeExtendPropertyGroupVOList = ExtendPropertyGroupVO.obtainExtendPropertyGroupVOList(storeGroupExtAttribute);
        if (!storeExtendPropertyGroupVOList.isEmpty()) {
            // 以ExtendPropertyGroupVO中的comGroupId和extendPropertyList中的attributeId字符串相加为key，以ShotPropertyVO为value，生成一个map
            Map<String, ShotPropertyVO> map = new HashMap<>();
            for (ExtendPropertyGroupVO extendPropertyGroupVO : storeExtendPropertyGroupVOList) {
                if (extendPropertyGroupVO.getExtendPropertyList()!=null){
                    for (ShotPropertyVO shotPropertyVO : extendPropertyGroupVO.getExtendPropertyList()) {
                        map.put(getKey(extendPropertyGroupVO.getComGroupId(), shotPropertyVO.getAttributeId()), shotPropertyVO);
                    }
                }
            }
            List<PropertyVO> storeExtPropertyList = Lists.newArrayList();
            // 遍历新类目扩展属性
            for (PropertyVO propertyVO : extendPropertyList) {
                log.info("类目{}扩展属性{}", targetCatId, JSON.toJSONString(propertyVO));
                String groupAttributeId = getKey(propertyVO.getComGroupId(), propertyVO.getAttributeId());
                if (!map.containsKey(groupAttributeId)) {
                    continue;
                }
                // 如果存在属性组id和属性id都匹配，那么进一步处理扩展属性值
                List<PropertyValueVO> propertyValueVOList = Lists.newArrayList();
                // 单选属性
                if (CategoryAttrInputTypeEnum.RADIO.getCode().equals(propertyVO.getAttributeInputType())
                        || CategoryAttrInputTypeEnum.CHECKBOX.getCode().equals(propertyVO.getAttributeInputType())) {
                    if (CollectionUtils.isEmpty(propertyVO.getPropertyValueVOList())) {
                        continue;
                    }
                    // 扩展属性值处理
                    ShotPropertyVO valueList = map.get(groupAttributeId);
                    for (PropertyValueVO valueVO : propertyVO.getPropertyValueVOList()) {
                        log.info("类目{} 扩展属性{} 属性值{}", targetCatId,groupAttributeId, JSON.toJSONString(valueVO));
                        if(valueList.getPropertyValueVOList()!=null){
                            for (ShotPropertyValueVO shotPropertyValueVO : valueList.getPropertyValueVOList()) {
                                if (shotPropertyValueVO.getAttributeValueId()!=null && shotPropertyValueVO.getAttributeValueId() == valueVO.getAttributeValueId()) {
                                    propertyValueVOList.add(valueVO);
                                }
                            }
                        }
                    }
                } else if (CategoryAttrInputTypeEnum.TEXT.getCode().equals(propertyVO.getAttributeInputType())) {
                    if (CollectionUtils.isEmpty(propertyVO.getPropertyValueVOList())) {
                        continue;
                    }

                    ShotPropertyVO valueList = map.get(groupAttributeId);
                    for (PropertyValueVO valueVO : propertyVO.getPropertyValueVOList()) {
                        log.info("类目{} 扩展属性{} 属性值{}", targetCatId,groupAttributeId, JSON.toJSONString(valueVO));
                        if (valueList.getPropertyValueVOList() !=null && StringUtils.isNotBlank(valueVO.getLang()) && valueList.getPropertyValueVOList().stream().filter(Objects::nonNull).anyMatch(v -> v.getLang() == valueVO.getLang())) {
                            PropertyValueVO storeValueVO = new PropertyValueVO();
                            BeanUtil.copyProperties(valueVO,storeValueVO,false);
                            storeValueVO.setAttributeValueName(storeValueVO.getAttributeValueName());
                            propertyValueVOList.add(storeValueVO);
                        }
                    }
                }
                propertyVO.setPropertyValueVOList(propertyValueVOList);
                storeExtPropertyList.add(propertyVO);
            }
            log.info("类目{} 比较后的扩展属性{}", targetCatId, JSON.toJSONString(storeExtPropertyList));
            if (CollectionUtils.isNotEmpty(storeExtPropertyList)) {
                extendAttribute = spuConvertService.getGroupExtAttribute(storeExtPropertyList);
            }
        }
        return extendAttribute;
    }

    @NotNull
    private static String getKey(Integer comGroupId, Long attributeId) {
        return comGroupId + Constant.COLON + attributeId;
    }


    private DataResponse<Boolean> validate(TransferCategoryReqVO reqVO) {
        // 查询新类目信息
        CategoryVO categoryVO = categoryOutService.queryCategoryById(reqVO.getTargetCatId());
        if (Objects.isNull(categoryVO) || !categoryVO.getLeafNodeFlag()) {
            return DataResponse.error(String.format("类目%s不存在或不是末级类目",reqVO.getTargetCatId()));
        }

        SpuPO spuPo = spuAtomicService.getSpuPoBySpuId(reqVO.getSpuId());
        if (Objects.isNull(spuPo)) {
            return DataResponse.error(String.format("商品%s不存在",reqVO.getSpuId()));
        }

        // 商品状态校验
        if (validateAuditStatus) {
            if (!SpuAuditStatusEnum.APPROVED.getCode().equals(spuPo.getAuditStatus())) {
                return DataResponse.error(String.format("商品%s审核未通过，不能迁移",reqVO.getSpuId()));
            }
        }

        // 本土商品校验产品线
        String sourceCountryCode = spuPo.getSourceCountryCode();
        // 税务审核状态不通过的商品不能迁移
        if (Objects.nonNull(spuPo.getTaxAuditStatus())
                && !SpuTaxAuditStatusEnum.APPROVED.getCode().equals(spuPo.getTaxAuditStatus())){
            return DataResponse.error(String.format("商品%s税务审核未通过，不能迁移",reqVO.getSpuId()));
        }

        String supplierCode = spuPo.getVendorCode();
        // 校验供应商产品线
        if (!CountryConstant.COUNTRY_ZH.equals(sourceCountryCode)
                &&  !businessLineManageService.checkBusinessLineRelation(supplierCode, spuPo.getBrandId(), reqVO.getTargetCatId())){
            return DataResponse.error(String.format("供应商%s没有目标类目%s的产品线",supplierCode,reqVO.getTargetCatId()));
        }

        return DataResponse.success();
    }
}
