package com.jdi.isc.product.soa.service.manage.price.agreementPrice;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.agreementPrice.res.CountryAgreementPriceWarningDTO;
import com.jdi.isc.product.soa.api.common.enums.AgreementPriceDataSourceTypeEnums;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceWarningPageReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceWarningVO;

import java.util.List;

/**
 * @Description: 国家协议价预警表数据维护服务
 * @Author: zhaokun51
 * @Date: 2025/03/18 10:59
 **/

public interface CountryAgreementPriceWarningManageService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    PageInfo<CountryAgreementPriceWarningVO> pageSearch(CountryAgreementPriceWarningPageReqVO input);

    /**
     * 保存或更新指定的agreementPriceId对应的对象。
     * @param agreementPriceId 需要保存或更新的对象的ID。
     * @return true表示操作成功，false表示操作失败。
     */
    Boolean saveOrUpdate(Long agreementPriceId, AgreementPriceDataSourceTypeEnums dataSourceTypeEnums);

    /**
     * 更新 国家协议价格预警 yn=0.
     *
     * @param skuIds  the sku ids
     * @param updater the updater
     */
    void updateYn(List<Long> skuIds, String updater);

    /**
     * 根据ID列表查询国家协议价格预警信息
     * @param ids 要查询的国家协议价格预警ID列表
     * @return 包含国家协议价格预警DTO列表的数据响应对象
     */
    List<CountryAgreementPriceWarningDTO> listWarningsByIds(List<Long> ids);
}
