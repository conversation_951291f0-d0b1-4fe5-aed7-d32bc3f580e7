package com.jdi.isc.product.soa.service.mapstruct.customerMku;


import com.jdi.isc.product.soa.api.customerMku.req.*;
import com.jdi.isc.product.soa.api.customerMku.res.CustomerMkuDTO;
import com.jdi.isc.product.soa.api.customerMku.res.MkuCheckBindResDTO;
import com.jdi.isc.product.soa.api.customerMku.res.MkuPoolVerificationResDTO;
import com.jdi.isc.product.soa.domain.customerMku.biz.CustomerMkuVO;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.customerMku.res.CustomerMkuPriceResDTO;
import com.jdi.isc.product.soa.domain.customerMku.biz.*;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuPoolVerificationReqVO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuPoolVerificationResVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;


/**
 * 客户mku对象转换
 * <AUTHOR>
 * @date 20231128
 */
@Mapper
public interface CustomerMkuConvert {

    CustomerMkuConvert INSTANCE = Mappers.getMapper(CustomerMkuConvert.class);

    @InheritConfiguration
    List<CustomerMkuVO> listPo2vo(List<CustomerMkuPO> customerMkuPO);
    @Mapping(source = "jdCatId",target = "catId")
    @InheritConfiguration
    CustomerMkuVO vo2Po(CustomerMkuPO customerMkubVO);

    @InheritConfiguration
    CustomerMkuBatchReqVO dto2vo(CustomerMkuBatchReqDTO input);

    @InheritConfiguration
    CustomerMkuPricePageReqVO priceReqDto2Vo(CustomerMkuPricePageReqDTO input);

    @Mapping(source = "createTime", target = "createTime")
    @Mapping(source = "updateTime", target = "updateTime")
    List<CustomerMkuDTO> listVo2Dto (List<CustomerMkuVO> customerMkuVOList);

    @InheritConfiguration
    PageInfo<CustomerMkuPriceResDTO> priceResVo2Dto(PageInfo<CustomerMkuPriceVO> input);

    default Long map(Date value) {
        return value != null ? value.getTime() : null;
    }


    MkuPoolVerificationReqVO poolVerificationDto2Vo(MkuPoolVerificationReqDTO dto);

    MkuPoolVerificationReqDTO poolVerificationVo2Dto(MkuPoolVerificationReqVO vo);

    MkuPoolVerificationResVO poolVerificationResDto2Vo(MkuPoolVerificationResDTO dto);

    MkuPoolVerificationResDTO poolVerificationResVo2Dto(MkuPoolVerificationResVO vo);


    CustomerMkuQueryVO queryDto2Vo (CustomerMkuQueryDTO dto);


    PageInfo<CustomerMkuDTO> pageVo2Dto(PageInfo<CustomerMkuVO> pageInfo);

    CustomerMkuEachReqVO reqDto2Vo(CustomerMkuBatchReqDTO dto);

    MkuCheckBindReqVO reqDto2ReqVo(MkuCheckBindReqDTO dto);

    MkuCheckBindResDTO checkBindResVo2Dto(MkuCheckBindResVO vo);


    CustomerMkuReqVO reqDto2Vo(CustomerMkuReqDTO dto);
}
