package com.jdi.isc.product.soa.service.mapstruct.stock;

import com.jdi.isc.product.soa.api.stock.res.StockResDTO;
import com.jdi.isc.product.soa.domain.stock.po.StockLogPO;
import com.jdi.isc.product.soa.domain.stock.po.StockPO;
import com.jdi.isc.product.soa.domain.stock.vo.SkuStockVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 库存对象转换
 * <AUTHOR>
 * @date 20240615
 */
@Mapper
public interface StockConvert {

    StockConvert INSTANCE = Mappers.getMapper(StockConvert.class);

    @InheritConfiguration
    List<StockResDTO> listPo2dto(List<StockPO> attributePOS);

    @InheritConfiguration
    StockPO vo2po(SkuStockVO skuStockVO);

    @InheritConfiguration
    List<StockPO> listVo2Po(List<SkuStockVO> skuVoList);
    @InheritConfiguration
    List<SkuStockVO> listPo2Vo(List<StockPO> skuVoList);

    StockLogPO po2TempPo(StockLogPO po);

}
