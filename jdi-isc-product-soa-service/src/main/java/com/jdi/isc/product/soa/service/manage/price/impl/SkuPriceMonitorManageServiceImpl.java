package com.jdi.isc.product.soa.service.manage.price.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.aggregate.read.api.price.res.JdiSkuPriceResDTO;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.PurchasePriceCompareResultEnum;
import com.jdi.isc.product.soa.domain.enums.PurchasePriceCompareStatusEnum;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceMonitorSpuPageVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPurchasePriceCompareResultVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPurchasePriceCompareVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPurchasePriceQueryVO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.SaveSpuVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuDetailVO;
import com.jdi.isc.product.soa.price.api.jdPrice.res.IscEachSkuPriceResDTO;
import com.jdi.isc.product.soa.rpc.vcs.VendorPriceRpcService;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceMonitorAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuDraftAtomicService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceMonitorManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPurchasePriceChangeManageService;
import com.jdi.isc.product.soa.service.manage.price.domestic.DomesticSkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuDraftManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuReadManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 价格监控数据维护服务实现
 * @Author: zhaojianguo21
 * @Date: 2024/12/11 17:13
 **/

@Slf4j
@Service
public class SkuPriceMonitorManageServiceImpl implements SkuPriceMonitorManageService {

    @Resource
    private SkuPriceMonitorAtomicService skuPriceMonitorAtomicService;
    @Resource
    private SkuPurchasePriceChangeManageService skuPurchasePriceChangeManageService;
    @Resource
    private SpuReadManageService spuReadManageService;
    @Resource
    private SpuManageService spuManageService;
    @Resource
    private DomesticSkuPriceManageService domesticSkuPriceManageService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private SkuPriceAtomicService skuPriceAtomicService;
    @Resource
    private OperDuccConfig operDuccConfig;

    @LafValue("jdi.isc.price.monitor.spu.exclude.creators")
    private Set<String> excludeCreators;

    @Resource
    private SkuDraftAtomicService skuDraftAtomicService;

    @Resource
    private SpuDraftManageService spuDraftManageService;

    /**
     * 需要保存记录的比较结果枚举
     */
    private static final Set<Integer> NEED_SAVE_COMPARE_RESULT_LIST = Sets.newHashSet(PurchasePriceCompareResultEnum.GT_THEN_JD.getCode(),
            PurchasePriceCompareResultEnum.LT_THEN_JD.getCode());

    /**
     * 需要发起改价审批的比较结果枚举
     */
    private static final Set<Integer> NEED_APPROVAL_COMPARE_RESULT_LIST = Sets.newHashSet(PurchasePriceCompareResultEnum.GT_THEN_JD.getCode(),
            PurchasePriceCompareResultEnum.LT_THEN_JD.getCode());

    @Override
    public PageInfo<SkuPriceMonitorSpuPageVO.Response> pageSearchSpu(SkuPriceMonitorSpuPageVO.Request input) {
        input.setAuditStatus(SpuAuditStatusEnum.APPROVED.getCode());
        if (CollectionUtils.isNotEmpty(excludeCreators)){
            input.setExcludeCreators(excludeCreators);
        }

        PageInfo<SkuPriceMonitorSpuPageVO.Response> pageInfo = new PageInfo<>();
        pageInfo.setSize(input.getSize());
        pageInfo.setIndex(input.getIndex());

        // 总条数
        long total = skuPriceMonitorAtomicService.pageSearchSpuTotal(input);
        pageInfo.setTotal(total);
        if (0 == total) {
            log.info("pageSearchSpu, total is zero. input={}", JSONObject.toJSONString(input));
            return pageInfo;
        }

        // 分页查询
        List<SkuPriceMonitorSpuPageVO.Response> pageList = skuPriceMonitorAtomicService.pageSearchSpu(input);
        pageInfo.setRecords(pageList);

        return pageInfo;
    }

    @Override
    public SkuPurchasePriceCompareVO.Response comparePurchasePrice(SkuPurchasePriceCompareVO.Request input) {
        SkuPurchasePriceCompareVO.Response result = new SkuPurchasePriceCompareVO.Response();
        if (CollectionUtils.isEmpty(input.getSpuIds())){
            log.info("comparePurchasePrice, req SpuIds empty.");
            return result;
        }

        // 查spu下所有sku的采购价格信息
        SkuPurchasePriceQueryVO.Request queryPriceReq = new SkuPurchasePriceQueryVO.Request();
        queryPriceReq.setSpuIds(input.getSpuIds());
        List<SkuPurchasePriceQueryVO.Response> dbRecordPriceList = skuPriceMonitorAtomicService.querySkuPriceInfo(queryPriceReq);
        if (CollectionUtils.isEmpty(dbRecordPriceList)){
            log.info("comparePurchasePrice, dbRecordPriceList empty.");
            return result;
        }
        Set<Long> skuIds = dbRecordPriceList.stream().map(SkuPurchasePriceQueryVO.Response::getSkuId).collect(Collectors.toSet());
        // 查商品信息。不要和价格信息一起连表查。用spu_id连表后查出来的数据不对。
        List<SkuPO> skuPoList = skuAtomicService.queryInfoForPriceCompare(skuIds);
        Map<Long, SkuPO> skuPoMap = skuPoList.stream().collect(Collectors.toMap(SkuPO::getSkuId, o->o));
        // 补充商品信息
        dbRecordPriceList.forEach(o->{
            SkuPO skuPo = skuPoMap.get(o.getSkuId());
            o.setJdSkuId(skuPo.getJdSkuId());
            o.setVendorCode(skuPo.getVendorCode());
        });

        // 查所有sku的销售价
        List<SkuPurchasePriceQueryVO.Response> sellPrices = skuPriceMonitorAtomicService.sellingPrice(skuIds);
        Map<Long,SkuPurchasePriceQueryVO.Response> sellPriceInfoMap = null;
        if (CollectionUtils.isNotEmpty(sellPrices)){
            sellPriceInfoMap = sellPrices.stream().filter(Objects::nonNull).collect(Collectors.toMap(SkuPurchasePriceQueryVO.Response::getSkuId, o->o));
        }

        // 查中台采购价
        Set<Long> jdSkuIds = dbRecordPriceList.stream().map(SkuPurchasePriceQueryVO.Response::getJdSkuId).collect(Collectors.toSet());
        Map<Long, IscEachSkuPriceResDTO> jdPurchasePriceInfoMap = domesticSkuPriceManageService.queryJdiSkuPrice(new ArrayList<>(jdSkuIds));

        // 循环比较旧采购价格
        List<SkuPurchasePriceCompareResultVO> compareResList = new ArrayList<>();
        for (SkuPurchasePriceQueryVO.Response oldPurchasePriceInfo: dbRecordPriceList){
            try {
                SkuPurchasePriceCompareResultVO compareResultVo = monitorScanSingleSku(oldPurchasePriceInfo, jdPurchasePriceInfoMap, sellPriceInfoMap);
                if (null != compareResultVo.getCompareResult() && PurchasePriceCompareResultEnum.EQUAL_JD.getCode().intValue()!=compareResultVo.getCompareResult()){
                    // 只处理价格不相等的sku
                    compareResList.add(compareResultVo);
                }
            } catch (Exception e) {
                log.error("comparePurchasePrice, exception. SkuPurchasePriceQueryVO.Response={}",
                        JSONObject.toJSONString(oldPurchasePriceInfo), e);
            }
        }

        // 处理比较结果
        dealCompareResult(compareResList);

        return result;
    }

    /**
     * 处理单条sku采购价格变更
     * @param oldPurchasePriceInfo 旧采购价信息
     * @param jdPurchasePriceInfoMap 中台最新采购价信息
     * @param sellPriceInfoMap 销售价信息
     * @return 比较结果
     */
    private SkuPurchasePriceCompareResultVO monitorScanSingleSku(SkuPurchasePriceQueryVO.Response oldPurchasePriceInfo, Map<Long, IscEachSkuPriceResDTO> jdPurchasePriceInfoMap,
                                                                 Map<Long,SkuPurchasePriceQueryVO.Response> sellPriceInfoMap){
        Long skuId = oldPurchasePriceInfo.getSkuId();
        Long jdSkuId = oldPurchasePriceInfo.getJdSkuId();

        // 国际旧采购价
        BigDecimal oldPurchasePrice = oldPurchasePriceInfo.getPurchasePrice();
        String oldPurchaseCurrency = oldPurchasePriceInfo.getCurrency();
        // 国际销售价格
        BigDecimal sellPrice = MapUtils.isNotEmpty(sellPriceInfoMap) && null != sellPriceInfoMap.get(skuId) ?
                sellPriceInfoMap.get(skuId).getSellingPrice() : null;
        String sellCurrency = MapUtils.isNotEmpty(sellPriceInfoMap) && null != sellPriceInfoMap.get(skuId) ?
                sellPriceInfoMap.get(skuId).getCurrency() : null;
        // 中台最新采购价
        BigDecimal newPurchasePrice = MapUtils.isNotEmpty(jdPurchasePriceInfoMap) && null != jdPurchasePriceInfoMap.get(jdSkuId) ?
                jdPurchasePriceInfoMap.get(jdSkuId).getJdSkuPurchasePrice() : null;
//        String newPurchaseCurrency = MapUtils.isNotEmpty(jdPurchasePriceInfoMap) && null!=jdPurchasePriceInfoMap.get(jdSkuId) ?
//                jdPurchasePriceInfoMap.get(jdSkuId).getCurrency():null;

        boolean condition = null != sellPrice && null != newPurchasePrice && null != oldPurchasePrice
                && oldPurchaseCurrency.equals(sellCurrency);

        SkuPurchasePriceCompareResultVO compareResultVo = null;
        int compareRes = PurchasePriceCompareResultEnum.UNKNOWN.getCode();
        if (condition){
            compareRes = newPurchasePrice.compareTo(oldPurchasePrice);

            // 采购价格有变化，保存变化信息
            BigDecimal grossRate = null;
            if (compareRes!=0){
                // 计算毛利率
                if(BigDecimal.ZERO.compareTo(sellPrice) != 0){
                    grossRate = sellPrice.subtract(newPurchasePrice).divide(sellPrice, 2, RoundingMode.HALF_UP);
                }else {
                    log.error("SkuPriceMonitorManageServiceImpl.monitorScanSingleSku error sellPrice 除数不能等于0");
                }
            }
            compareResultVo = new SkuPurchasePriceCompareResultVO(oldPurchasePriceInfo.getSkuPriceId(), oldPurchasePriceInfo.getSpuId(), skuId, oldPurchaseCurrency,
                    oldPurchasePrice, newPurchasePrice, sellPrice, grossRate, PurchasePriceCompareStatusEnum.FINISH.getCode());
        }else {
            // 无法比较采购价
//            log.info("monitorScanSingleSku, missing price info. skuId={}, jdSkuId={}, newPurchasePrice={}, oldPurchasePrice={}, sellPrice={}",
//                    skuId, jdSkuId, newPurchasePrice, oldPurchasePrice, sellPrice);
            compareResultVo = new SkuPurchasePriceCompareResultVO(oldPurchasePriceInfo.getSkuPriceId(), oldPurchasePriceInfo.getSpuId(), skuId, oldPurchaseCurrency,
                    oldPurchasePrice, newPurchasePrice, sellPrice, null, PurchasePriceCompareStatusEnum.UNKNOWN.getCode());
        }
        log.info("monitorScanSingleSku, compareRes={}, skuId={}, jdSkuId={},  newPurchasePrice={}, oldPurchasePrice={}, sellPrice={}",
                compareRes, skuId, jdSkuId, newPurchasePrice, oldPurchasePrice, sellPrice);

        compareResultVo.setCompareResult(compareRes);

        return compareResultVo;
    }

    /**
     * 处理比较结果
     * @param compareResList
     */
    private void dealCompareResult(List<SkuPurchasePriceCompareResultVO> compareResList){
        if (CollectionUtils.isEmpty(compareResList)){
            log.info("SkuPriceMonitorManageServiceImpl.dealCompareResult empty, skip.");
            return;
        }
        log.info("SkuPriceMonitorManageServiceImpl.dealCompareResult compareResList:{}", JSONObject.toJSONString(compareResList));
        // 保存比较结果记录
        List<SkuPurchasePriceCompareResultVO> needSaveList = compareResList.stream()
                .filter(o->NEED_SAVE_COMPARE_RESULT_LIST.contains(o.getCompareResult())).collect(Collectors.toList());
        log.info("SkuPriceMonitorManageServiceImpl.dealCompareResult, needSaveList:{}", JSONObject.toJSONString(needSaveList));
        if (CollectionUtils.isNotEmpty(needSaveList)){
            log.info("SkuPriceMonitorManageServiceImpl.dealCompareResult, needSaveList:{}", needSaveList.size());
            skuPurchasePriceChangeManageService.saveUpdatePriceMonitorRecordInfo(needSaveList);
        }
        //if (Boolean.TRUE.equals(operDuccConfig.updatePriceByApprove())){
        //    updatePurchasePriceByApproval(compareResList);
        //}else if (Boolean.TRUE.equals(operDuccConfig.updatePriceDirect())){
        //    updatePurchasePriceDirect(compareResList);
        //}
        if (!Boolean.TRUE.equals(operDuccConfig.updatePriceDirect())){
            log.info("SkuPriceMonitorManageServiceImpl.dealCompareResult is false req:{}",JSON.toJSONString(compareResList));
            return;
        }

        List<SkuPurchasePriceCompareResultVO> needUpdateList = compareResList.stream()
            .filter(o->NEED_APPROVAL_COMPARE_RESULT_LIST.contains(o.getCompareResult())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needUpdateList)){
            log.info("SkuPriceMonitorManageServiceImpl.dealCompareResult list is empty, req:{}", JSONObject.toJSONString(needUpdateList));
            return;
        }

        try {
            skuPurchasePriceChangeManageService.updatePurchasePrice(needUpdateList);
        }catch (Exception e){
            log.error("SkuPriceMonitorManageServiceImpl.dealCompareResult updatePurchasePrice error req:{} ,err:{}", JSONObject.toJSONString(needUpdateList),e.getMessage(),e);
        }finally {
            log.info("SkuPriceMonitorManageServiceImpl.dealCompareResult filer req:{}", JSONObject.toJSONString(needUpdateList));
        }
    }

    private void updatePurchasePriceByApproval(List<SkuPurchasePriceCompareResultVO> compareResList){
        if (!Boolean.TRUE.equals(operDuccConfig.updatePriceByApprove())){
            log.info("updatePurchasePriceByApproval, updatePriceByApprove is false, skip.");
            return;
        }

        // 发起审批
        List<SkuPurchasePriceCompareResultVO> needApprovalList = compareResList.stream()
                .filter(o->NEED_APPROVAL_COMPARE_RESULT_LIST.contains(o.getCompareResult())).collect(Collectors.toList());
        log.info("updatePurchasePriceByApproval, needApprovalList={}", JSONObject.toJSONString(needApprovalList));
        if (CollectionUtils.isEmpty(needApprovalList)){
             log.info("updatePurchasePriceByApproval, needApprovalList empty, skip.");
             return;
        }

        log.info("updatePurchasePriceByApproval, needApprovalList size={}", needApprovalList.size());
        //  needApprovalList按照spuId分组
        Map<Long,List<SkuPurchasePriceCompareResultVO>> needApprovalGroupList = needApprovalList.stream().collect(Collectors.groupingBy(o->o.getSpuId()));
        Iterator<Map.Entry<Long,List<SkuPurchasePriceCompareResultVO>>> groupListIterator = needApprovalGroupList.entrySet().iterator();
        while (groupListIterator.hasNext()){
            Map.Entry<Long,List<SkuPurchasePriceCompareResultVO>> mapEntry = groupListIterator.next();
            Long spuId = mapEntry.getKey();
            try {
                List<SkuPurchasePriceCompareResultVO> skuPriceChangedList = mapEntry.getValue();
                Map<Long,SkuPurchasePriceCompareResultVO> skuPriceChangedMap = skuPriceChangedList.stream().collect(Collectors.toMap(o->o.getSkuId(), o->o));
                log.info("updatePurchasePriceByApproval, skuPriceChangedList size={}, skuPriceChangedList={}", skuPriceChangedMap.size(), JSONObject.toJSONString(skuPriceChangedList));
                SpuDetailVO spuDetailVO = spuReadManageService.getDetailBySpuId(spuId);

                // 修改价格
                List<SkuVO> skuVOList = spuDetailVO.getSkuVOList();
                for(SkuVO skuVO: skuVOList){
                    if (!skuPriceChangedMap.containsKey(skuVO.getSkuId()) || null==skuPriceChangedMap.get(skuVO.getSkuId())
                            || null==skuPriceChangedMap.get(skuVO.getSkuId()).getPurchasePriceNew()){
                        continue;
                    }

                    // 修改采购价
                    skuVO.setPurchasePrice(skuPriceChangedMap.get(skuVO.getSkuId()).getPurchasePriceNew().toString());
                }
                // 设置为审批状态
                spuDetailVO.getSpuVO().setAuditStatus(AuditStatusEnum.WAITING_APPROVED.getCode());
                // 发起审批
                log.info("updatePurchasePriceByApproval, submit spuId={}", spuId);
                spuManageService.submit(spuDetailVO);
            } catch (Exception e) {
                log.error("updatePurchasePriceByApproval, exception, spuId={}", spuId, e);
            }
        }
    }

    private void updatePurchasePriceToSkuPrice(List<SkuPurchasePriceCompareResultVO> needUpdateList){
        Date date = new Date();
        List<SkuPricePO> updateList = new ArrayList<>(needUpdateList.size());
        for (SkuPurchasePriceCompareResultVO resultVO: needUpdateList){
            // 更新为新的采购价
            SkuPricePO skuPricePO = new SkuPricePO();
            skuPricePO.setId(resultVO.getSkuPriceId());
            skuPricePO.setPrice(resultVO.getPurchasePriceNew());
            skuPricePO.setUpdater(Constant.PIN_SYSTEM);
            skuPricePO.setUpdateTime(date);
            updateList.add(skuPricePO);
        }
        Boolean updateRes = skuPriceAtomicService.updateBatchById(updateList, Long.valueOf(Constant.BATCH_SIZE).intValue());
        log.info("SkuPriceMonitorManageServiceImpl.updatePurchasePriceDirect updateList list:{},result:{}",JSON.toJSONString(updateList) , updateRes);

    }

    private void updatePurchasePriceToSkuDraft(List<SkuPurchasePriceCompareResultVO> needUpdateList){
        // 更新skuDraft
        Set<Long> spuIdSet = needUpdateList.stream().map(SkuPurchasePriceCompareResultVO::getSpuId).collect(Collectors.toSet());
        List<SkuDraftPO> skuDraftPOS = skuDraftAtomicService.queryListBySpuIds(spuIdSet);
        Map<Long, SkuPurchasePriceCompareResultVO> purchasePriceMap = needUpdateList.stream()
            .collect(Collectors.toMap(SkuPurchasePriceCompareResultVO::getSkuId, Function.identity()));
        List<SkuDraftPO> updateSkuDraftList = new ArrayList<>();
        log.info("SkuPriceMonitorManageServiceImpl.updatePurchasePriceDirect skuDraftList list:{}",JSON.toJSONString(skuDraftPOS));
        Date date = new Date();
        for(SkuDraftPO skuDraftPO : skuDraftPOS) {
            SkuVO skuVO = JSON.parseObject(skuDraftPO.getSkuJsonInfo(), SkuVO.class);
            if(Objects.nonNull(skuVO) && Objects.nonNull(skuVO.getSkuId())){
                if(purchasePriceMap.containsKey(skuVO.getSkuId())){
                    SkuPurchasePriceCompareResultVO skuPurchasePriceVo = purchasePriceMap.get(skuVO.getSkuId());
                    skuVO.setPurchasePrice(String.valueOf(skuPurchasePriceVo.getPurchasePriceNew()));
                    skuVO.setUpdater(Constant.PIN_SYSTEM);
                    skuVO.setUpdateTime(date);
                    skuDraftPO.setSkuJsonInfo(JSON.toJSONString(skuVO));
                    updateSkuDraftList.add(skuDraftPO);
                }
            }
        }
        if(CollectionUtils.isNotEmpty(updateSkuDraftList)){
            boolean result = skuDraftAtomicService.updateBatchById(updateSkuDraftList, Long.valueOf(Constant.BATCH_SIZE).intValue());
            log.info("SkuPriceMonitorManageServiceImpl.updateSkuDraftAtomicService updateList list:{} ,result:{}",JSON.toJSONString(updateSkuDraftList) , result);
        }
    }


    private void updatePurchasePriceToSpuDraft(List<SkuPurchasePriceCompareResultVO> needUpdateList){
        Map<Long, SkuPurchasePriceCompareResultVO> skuPurchasePriceMap = needUpdateList.stream().collect(Collectors.toMap(SkuPurchasePriceCompareResultVO::getSkuId, Function.identity()));
        Date date = new Date();
        for (SkuPurchasePriceCompareResultVO skuPurchasePriceVO : needUpdateList){
            SaveSpuVO saveSpuVO = spuDraftManageService.getSaveSpuVoFromDraftBySpuId(skuPurchasePriceVO.getSpuId());
            List<SkuVO> skuVOList = saveSpuVO.getSkuVOList();
            for (SkuVO skuVO : skuVOList){
                if(skuPurchasePriceMap.containsKey(skuVO.getSkuId())){
                    SkuPurchasePriceCompareResultVO skuPurchasePriceCompareVO = skuPurchasePriceMap.get(skuVO.getSkuId());
                    skuVO.setPurchasePrice(String.valueOf(skuPurchasePriceCompareVO.getPurchasePriceNew()));
                    skuVO.setUpdater(Constant.PIN_SYSTEM);
                    skuVO.setUpdateTime(date);
                }
            }
            Long result = spuDraftManageService.saveOrUpdateDraft(saveSpuVO);
            log.info("SkuPriceMonitorManageServiceImpl.updatePurchasePriceBySpuDraft updateSpuDraft res:{} ,result:{}",JSON.toJSONString(saveSpuVO) , result);
        }
    }
}