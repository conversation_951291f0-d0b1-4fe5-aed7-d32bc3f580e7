package com.jdi.isc.product.soa.service.manage.outUrl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.outUrl.req.OutUrlMapDTO;
import com.jdi.isc.product.soa.domain.url.po.OutUrlMapPO;
import com.jdi.isc.product.soa.domain.url.vo.OutUrlMapQueryVO;
import com.jdi.isc.product.soa.domain.url.vo.OutUrlTransformVO;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

/**
 * 外链读写服务
 * <AUTHOR>
 * @date 20250401
 */
public interface OutUrlManageService {

    /**
     * 提交文件外链转换内链任务<功能未上线>
     */
    @Deprecated
    void submitTransformTask(OutUrlMapDTO req);
    /**
     * 文件外链转查询内链<功能未上线>
     */
    @Deprecated
    Map<String,OutUrlMapPO> getJdUrl(OutUrlMapQueryVO req);

    /**
     * 执行外链转内链服务<功能未上线>
     */
    @Deprecated
    DataResponse<Boolean> doUrlTransform(OutUrlMapDTO req);

    /**
     * 批量转换
     */
    List<OutUrlTransformVO> batchTransform(LinkedHashSet<String> outUrl);


}
