package com.jdi.isc.product.soa.service.manage.spu.impl.approve;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.product.soa.api.common.TradeTypeConstant;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuTaxAuditStatusEnum;
import com.jdi.isc.product.soa.common.constants.*;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.frame.SystemContextHolder;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.CompanyTypeEnum;
import com.jdi.isc.product.soa.domain.enums.product.AuditLevelEnum;
import com.jdi.isc.product.soa.domain.enums.spu.SpuWriteCountryTypeEnums;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.spu.biz.SaveSpuVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuAuditRecordUpdateVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuDraftVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuVO;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CategoryTaxVO;
import com.jdi.isc.product.soa.domain.taxRate.po.CategoryTaxPO;
import com.jdi.isc.product.soa.service.atomic.spu.AuditRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDraftAtomicService;
import com.jdi.isc.product.soa.service.manage.spu.SpuWriteManageService;
import com.jdi.isc.product.soa.service.manage.spu.validate.SkuValidateService;
import com.jdi.isc.product.soa.service.mapstruct.taxRate.CategoryTaxConvert;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import com.jdi.isc.product.soa.service.support.DataResponseMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.Constant.COMMA;
import static com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant.SPU_AUDIT_LOCK_FAILED;

/**
 * 本土审核服务父类
 *
 * <AUTHOR>
 * @date 2024/5/8
 **/
@Slf4j
@Service
public class VnSpuApproveManageService extends AbstractSpuApproveManageService {

    @Resource
    private Map<String, SpuWriteManageService> spuWriteManageServiceMap;

    @Resource
    private AuditRelationAtomicService auditRelationAtomicService;

    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;

    @Resource
    private DataResponseMessageService dataResponseMessageService;

    @Resource
    private ExecutorService spuApproveExecutorService;

    @Resource
    private SkuValidateService skuValidateService;

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<String> batchApprove(SpuAuditRecordUpdateVO spuAuditRecordReqVO) {
        // erp
        final String erp = LoginContextHolder.getLoginContextHolder().getPin();
        final String systemCode = SystemContextHolder.get();
        final String lang = LangContextHolder.get();
        List<Long> spuIdList = spuAuditRecordReqVO.getSpuIds();
        String scene = spuAuditRecordReqVO.getTaxStatus() == null?null: TaxConstant.TAX_APPROVE;
        try {
            String message = null;
            for (Long spuId : spuIdList) {
                try{
                    CompletableFuture<Void> task = CompletableFuture.runAsync(() -> {
                        ApiInitUtils.init(erp,lang,systemCode);
                        this.singleApproved(spuId,scene);
                    }, spuApproveExecutorService);
                }catch (Exception e) {
                    message = String.format("%s：%s%s", spuId, e.getMessage(),"\n");
                }
            }
            return StringUtils.isNotBlank(message) ? DataResponse.error(message) : DataResponse.success(DataResponseCode.SUCCESS.getMessage());
        } catch (BizException bizException) {
            log.error("VnSpuApproveManageService.batchApprove warning spuAuditRecordReqVO={},message={}", JSON.toJSONString(spuAuditRecordReqVO), bizException.getMessage());
            throw bizException;
        } catch (Exception e) {
            log.error("VnSpuApproveManageService.batchApprove error erp={},spuAuditRecordReqVO={}", erp, JSON.toJSONString(spuAuditRecordReqVO), e);
            String errorMessage = dataResponseMessageService.getErrorMessage(DataResponseCodeConstant.SPU_APPROVED_ERROR);
            log.error("VnSpuApproveManageService.batchApprove error ,errorMessage={}", errorMessage);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("%s 发品审批通过异常,spuIds: %s, 异常信息:%s"
                ,systemProfile
                    , LevelCode.P1.getMessage()
                , spuIdList.stream().map(String::valueOf).collect(Collectors.joining(","))
                , e.getMessage()));
            throw new BizException(errorMessage, e);
        }
    }

    @Override
    void recordVendor(List<SkuVO> skuVOList) {

    }

    @Override
    void createSpu(SaveSpuVO saveSpuVO) {
        final String sourceCountryCode = saveSpuVO.getSpuVO().getSourceCountryCode();
        spuWriteManageServiceMap.get(SpuWriteCountryTypeEnums.getEnumByCountryCode(sourceCountryCode).getServiceName()).create(saveSpuVO);
    }

    @Override
    void validateSkuList(List<SkuVO> skuVOList, SpuVO spuVO) {
        skuValidateService.validateCreateSkuVoList(skuVOList);
        skuValidateService.validateSkuStockList(skuVOList,spuVO.getSourceCountryCode());
        // 校验价格
        skuValidateService.validateSkuPrice(skuVOList, spuVO.getSourceCountryCode());
        // 校验长宽高重
        skuValidateService.validateFourDimension(skuVOList);
        // 校验跨境属性
        skuValidateService.validateInterProperty(spuVO.getCatId()
                ,spuVO.getSourceCountryCode()
                ,StringUtils.isNotBlank(spuVO.getAttributeScope()) ? Arrays.asList(spuVO.getAttributeScope().split(COMMA)) : Lists.newArrayList()
                ,skuVOList,spuVO.getIsExport());
        // 校验跨境资质
        skuValidateService.validateCertificate(spuVO.getCatId()
                ,spuVO.getSourceCountryCode()
                ,StringUtils.isNotBlank(spuVO.getAttributeScope()) ? Arrays.asList(spuVO.getAttributeScope().split(COMMA)) : Lists.newArrayList()
                ,skuVOList,spuVO.getIsExport());
        // 校验发货时效
        skuValidateService.validateProductionCycle(skuVOList);
        // 校验出口HsCode
        skuValidateService.validateHsCode(skuVOList);
        // 校验条形码
        skuValidateService.validateUpcCode(skuVOList);
    }

    @Override
    void updateAuditLevel(SpuDraftVO draftVo) {
        final String erp = LoginContextHolder.getLoginContextHolder().getPin();
        // 审核分级别审批
        int level = draftVo.getLevel() + 1;
        AssertValidation.isTrue(!auditRelationAtomicService.checkErpLevel(level, erp, draftVo.getSourceCountryCode()), String.format("当前ERP %s不是当前审批层级%s", erp, AuditLevelEnum.descByLevel(level)));
        log.info("VnSpuApproveManageService.updateAuditLevel 更新草稿审核等级 spuId={},erp={},level={}", draftVo.getSpuId(), erp, level);
        if (level < auditRelationAtomicService.getMaxAuditLevel(draftVo.getSourceCountryCode())) {
            SpuDraftPO newDraftPo = new SpuDraftPO();
            newDraftPo.setLevel(level);
            newDraftPo.setId(draftVo.getId());
            newDraftPo.setUpdateTime(new Date());
            spuDraftAtomicService.updateDraft(newDraftPo);
        }
    }

    @Override
    void handleSkuList(List<SkuVO> skuVOList) {
        if (CollectionUtils.isNotEmpty(skuVOList)) {
            skuVOList.forEach(skuVO -> {
                if (StringUtils.isBlank(skuVO.getCustomerTradeType())) {
                    skuVO.setCustomerTradeType(TradeTypeConstant.BD);
                }
                if (StringUtils.isBlank(skuVO.getVendorTradeType())) {
                    skuVO.setVendorTradeType(TradeTypeConstant.BD);
                }
                if (Objects.isNull(skuVO.getSalePrice())) {
                    skuVO.setSalePrice(skuVO.getPurchasePrice());
                }
            });
        }
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 30)
    public void singleApproved(Long spuId, String scene) {
        final String erp = LoginContextHolder.getLoginContextHolder().getPin();
        String requestId = UUID.randomUUID().toString();
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.SPU_AUDIT_OPERATE_LOCK_PRE, String.valueOf(spuId));
        try {
            // 加排他锁，同时只有一个审核操作可以审核当前商品
            AssertValidation.isTrue(!jimUtils.simpleLock(lockKey, requestId, 60), SPU_AUDIT_LOCK_FAILED, "加锁失败");
            // 查询草稿
            SpuDraftVO draftVo = spuDraftManageService.getDraftBySpuId(spuId);
            if (Objects.isNull(draftVo)) {
                updateSpuToSaleStatus(spuId);
                // 修改sku上架时间
                skuWriteManageService.saveOrUpdateSkuBiz(spuId, new Date());
                return;
            }

            int level = draftVo.getLevel() + 1;
            int maxLevel = 0;

            if (!StringUtils.equals(TaxConstant.TAX_APPROVE, scene)) {
                // 审核分级别审批
                level = draftVo.getLevel() + 1;
                // 最大审核层级
                maxLevel = auditRelationAtomicService.getMaxAuditLevel(draftVo.getSourceCountryCode());
                Boolean returnFlag = super.auditLevel(draftVo, erp, level, maxLevel);
                if (returnFlag) {
                    log.info("VnSpuApproveManageService.singleApproved not last approve return");
                    return;
                }
            }


            SaveSpuVO saveSpuVO = spuDraftManageService.getSaveSpuVoFromDraftBySpuId(spuId);
            if(StringUtils.equals(TaxConstant.TAX_APPROVE,scene)){
                saveSpuVO.getSpuVO().setTaxAuditStatus(SpuTaxAuditStatusEnum.APPROVED.getCode());
            }
            // 设置采销
            String buyer = categoryBuyerRelationAtomicService.getBuyerByCountryCodeAndCatId(saveSpuVO.getSpuVO().getSourceCountryCode(),saveSpuVO.getSpuVO().getCatId());
            saveSpuVO.getSpuVO().setBuyer(buyer);

            Boolean createFlag = this.canGenerateFormalSpu(saveSpuVO,scene);
            // 校验参数
            saveSpuVO.setApproveFlag(Constant.ONE);
            saveSpuVO.getSpuVO().setApproveFlag(Constant.ONE);
            spuValidateService.validateSaveParams(saveSpuVO);
            this.validateSkuList(saveSpuVO.getSkuVOList(),saveSpuVO.getSpuVO());
            // 审核通过后，更新商品
            if (Objects.nonNull(spuAtomicService.getSpuPoBySpuId(spuId)) && createFlag) {
                // 更新spu信息、spu状态为上架、审核通过
                saveSpuVO.getSpuVO().setSpuStatus(SpuStatusEnum.ON_SALE.getStatus());
                saveSpuVO.getSpuVO().setAuditStatus(SpuAuditStatusEnum.APPROVED.getCode());
                this.updateSpu(saveSpuVO);
                // 更新详描
                this.saveOrUpdateSpuDescLang(saveSpuVO);
                // 更新sku列表、sku价格、库存、扩展属性更新上架时间
                List<SkuVO> skuVoList = saveSpuVO.getSkuVOList();
                List<SkuVO> newSkuList = Lists.newArrayList();
                super.fillSkuVo(saveSpuVO.getSpuVO(), skuVoList);
                skuWriteManageService.updateSkus(skuVoList, newSkuList, spuId);
                // 修改sku上架时间
                skuWriteManageService.saveOrUpdateSkuBiz(spuId, saveSpuVO.getSpuVO().getUpdateTime());
                this.handleSkuList(skuVoList);
                // 更新sku价格
                super.initSkuPrice(skuVoList, saveSpuVO.getSpuVO().getSourceCountryCode());
                // 保存库存
                skuConvertService.initSkuStock(skuVoList, saveSpuVO.getSpuVO());
                // 更新资质列表
                super.saveOrUpdateSpuCertificate(saveSpuVO);
                // 处理SPU跨境属性
                super.initSpuGlobalAttribute(saveSpuVO);
                // 记录新的国内供应商
                this.recordVendor(skuVoList);
                // 备货仓库绑定更新
                //this.bindWarehouse(saveSpuVO.getSpuVO(), skuVoList);
            } else {
                // 更新spu信息、spu状态为上架、审批通过
                if(createFlag){
                    saveSpuVO.getSpuVO().setSpuStatus(SpuStatusEnum.ON_SALE.getStatus());
                }
                if(!StringUtils.equals(TaxConstant.TAX_APPROVE,scene)){
                    saveSpuVO.getSpuVO().setAuditStatus(SpuAuditStatusEnum.APPROVED.getCode());
                }
                saveSpuVO.getSpuVO().setCreator(draftVo.getCreator());
                saveSpuVO.getSpuVO().setUpdater(draftVo.getCreator());
                Date now = new Date();
                saveSpuVO.getSpuVO().setCreateTime(now);
                saveSpuVO.getSpuVO().setUpdateTime(now);
                // sku状态为上架
                List<@Valid SkuVO> skuVOList = saveSpuVO.getSkuVOList();
                skuVOList.forEach(skuVO -> {
                    skuVO.setSkuStatus(SpuStatusEnum.ON_SALE.getStatus());
                    skuVO.setCreator(draftVo.getCreator());
                    skuVO.setUpdater(draftVo.getCreator());
                    skuVO.setCreateTime(now);
                    skuVO.setUpdateTime(now);
                });
                this.handleSkuList(skuVOList);
                if(createFlag){
                    this.createSpu(saveSpuVO);
                }
                saveSpuVO.setCreateFlag(Boolean.TRUE);
            }

            // 更新草稿审核状态
            log.info("VnSpuApproveManageService.singleApproved 更新草稿前参数,saveSpuVO={}",JSON.toJSONString(saveSpuVO));
            spuDraftManageService.saveOrUpdateDraft(saveSpuVO);

            // 添加审核记录 跨境商品审核等级不变一直是0，本土时设置为3级
            String approveText = AuditLevelEnum.descByLevel(level) + "," + APPROVED_TEXT;
            if(StringUtils.isNotBlank(scene)){
                approveText  = approveText + "," + scene;
            }
            super.addAuditRecord(spuId, erp, AuditStatusEnum.APPROVED, approveText,
                    maxLevel == level ? level : AuditLevelEnum.ZERO.getLevel());

            if (!createFlag) {
                log.info("VnSpuApproveManageService.singleApproved createFlag is false");
                return;
            }
            // 保存税率
            this.saveSkuTaxes(saveSpuVO);

            // 审核通过发品成功
            super.sendSkuMsg(saveSpuVO);

            //审批通过MKU与SPU、SKU的信息同步
            log.info("VnSpuApproveManageService.singleApproved 审批通过MKU与SPU、SKU的信息同步,saveSpuVO={}",JSON.toJSONString(saveSpuVO));
            mkuManageService.syncMkuInfoBySpuAndSku(saveSpuVO);
        } catch (BizException bizException) {
            log.error("【系统异常】VnSpuApproveManageService.singleApproved biz error spuId={}", JSON.toJSONString(spuId), bizException);
            throw bizException;
        } catch (Exception e) {
            log.error("【系统异常】VnSpuApproveManageService.singleApproved error spuId={}", spuId, e);
            String errorMessage = dataResponseMessageService.getErrorMessage(DataResponseCodeConstant.SPU_APPROVED_ERROR);
            log.error("【系统异常】VnSpuApproveManageService.singleApproved error spuId={},message={}", spuId, errorMessage);
            throw new BizException(errorMessage, e);
        } finally {
            jimUtils.simpleLockRelease(lockKey, requestId);
        }
    }

    /**
     * 保存 SKU 税信息。
     * @param saveSpuVO 保存 SPu 的 VO 对象，包含源国家代码等信息。
     */
    private void saveSkuTaxes(SaveSpuVO saveSpuVO){
        this.saveSkuTax(saveSpuVO, CompanyTypeEnum.EPE);
        this.saveSkuTax(saveSpuVO,CompanyTypeEnum.FDI);
    }

    /**
     * 保存SKU税率信息。
     * @param saveSpuVO 保存SPU VO对象，包含SKU列表和SPU信息。
     */
    private void saveSkuTax(SaveSpuVO saveSpuVO,CompanyTypeEnum companyTypeEnum){
        String sourceCountryCode = saveSpuVO.getSpuVO().getSourceCountryCode();
        List<SkuVO> skuVOS = saveSpuVO.getSkuVOList();
        if(CollectionUtils.isEmpty(skuVOS)){
            log.error("VnSpuApproveManageService.saveSkuTax skuVOS is null,CompanyTypeEnum:{}",companyTypeEnum.getDesc());
            return;
        }

        if(CollectionUtils.isEmpty(skuVOS)){
            log.error("VnSpuApproveManageService.saveSkuTax skuVOS is null,CompanyTypeEnum:{}",companyTypeEnum.getDesc());
            return;
        }

        for(SkuVO skuVO : skuVOS){
            Long skuId = skuVO.getSkuId();
            if(skuId == null){
                continue;
            }

            BigDecimal tax = super.spuTaxManageService(sourceCountryCode).getParamTax(skuVO);
            if(tax == null){
                log.error("VnSpuApproveManageService.saveSkuTax skuVO tax is null,skuId:{},CompanyTypeEnum:{}", JSONObject.toJSONString(skuId),companyTypeEnum.getDesc());
                continue;
            }
            CategoryTaxVO categoryFDITaxVO = super.spuTaxManageService(sourceCountryCode).getCatTaxVOBySkuId(saveSpuVO.getSpuVO().getCatId(),skuId,sourceCountryCode,companyTypeEnum);

            CategoryTaxPO po = null;
            if(categoryFDITaxVO != null){
                po = CategoryTaxConvert.INSTANCE.vo2Po(categoryFDITaxVO);
            } else {
                po = this.initCategoryTaxPO(skuVO,companyTypeEnum);
            }

            po.setVatRate(tax);
            if(companyTypeEnum == CompanyTypeEnum.FDI){
                po.setDestinationVatRate(tax);
            } else {
                CategoryTaxVO categoryEPETaxVO = super.spuTaxManageService(sourceCountryCode).getCatTaxVO(saveSpuVO.getSpuVO().getCatId(),null,sourceCountryCode,companyTypeEnum);
                if(categoryEPETaxVO == null){
                    po.setDestinationVatRate(BigDecimal.ZERO);
                } else {
                    po.setDestinationVatRate(categoryEPETaxVO.getDestinationVatRate());
                }

            }
            boolean saveRes = categoryTaxAtomicService.saveOrUpdate(po);
            if (!saveRes){
                log.warn("VnSpuApproveManageService.saveSkuTax.saveOrUpdate, CategoryTaxPO fail. CategoryTaxPO={}", JSONObject.toJSONString(po));
                throw new BizException("保存失败");
            }
        }
    }

    /**
     * 初始化商品分类税收信息对象。
     * @param skuVO SKU信息对象。
     * @param companyTypeEnum 公司类型枚举。
     * @return 初始化后的商品分类税收信息对象。
     */
    private CategoryTaxPO initCategoryTaxPO(SkuVO skuVO,CompanyTypeEnum companyTypeEnum){
        CategoryTaxPO categoryTaxPO = new CategoryTaxPO();
        categoryTaxPO.setCountryCode(skuVO.getSourceCountryCode());
        categoryTaxPO.setCompanyType(companyTypeEnum.getCode());
        categoryTaxPO.setCategoryId(skuVO.getCatId());
        categoryTaxPO.setJdCatId(skuVO.getCatId());
        categoryTaxPO.setSkuId(skuVO.getSkuId());
        categoryTaxPO.setCreator(skuVO.getCreator());
        categoryTaxPO.setUpdater(skuVO.getUpdater());
        Date current = new Date();
        categoryTaxPO.setCreateTime(current.getTime());
        categoryTaxPO.setUpdateTime(current.getTime());
        categoryTaxPO.setYn(YnEnum.YES.getCode());
        return categoryTaxPO;
    }

    private Boolean canGenerateFormalSpu(SaveSpuVO saveSpuVO,String scene){
        if(StringUtils.equals(TaxConstant.TAX_APPROVE,scene)){
            Integer auditStatus = saveSpuVO.getSpuVO().getAuditStatus();
            if(SpuAuditStatusEnum.APPROVED.getCode().equals(auditStatus)){
                return Boolean.TRUE;
            }
        } else {
            Integer taxAuditStatus = saveSpuVO.getSpuVO().getTaxAuditStatus();
            if(taxAuditStatus == null){
                return Boolean.TRUE;
            }
            if(SpuTaxAuditStatusEnum.APPROVED.getCode().equals(taxAuditStatus)){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }
}
