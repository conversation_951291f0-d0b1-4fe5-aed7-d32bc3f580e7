package com.jdi.isc.product.soa.service.manage.price.impl.tax;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jd.fastjson.JSON;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.isc.product.soa.api.sku.req.SkuPredictPriceReqDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuPredictPriceResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.AttributeDimensionEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.api.common.enums.CalculatePriceEnums;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.domain.enums.product.FulfillmentRateTypeEnum;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.AgreementAndCostPriceVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPricePageVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.InitAgreementPriceVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceReqVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceResVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCalculatePriceReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCalculateTaxPriceVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CategoryTaxVO;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.SupplierSettlementAccountAtomicService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuTaxPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuTaxManageService;
import com.jdi.isc.product.soa.service.mapstruct.sku.SkuConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public abstract class AbstractSkuTaxPriceManageService implements SkuTaxPriceManageService {

    @Resource
    protected CountryManageService countryManageService;
    @Resource
    protected SkuAtomicService skuAtomicService;
    @Resource
    protected SkuPriceManageService skuPriceManageService;
    @Resource
    @Lazy
    protected Map<String, SpuTaxManageService> spuTaxManageServiceMap;

    /** 注入类 */
    protected SpuTaxManageService spuTaxManageService;
    @LafValue("jdi.isc.default.vat")
    protected String defaultVatConfig;
    /** 精确位数 */
    protected Integer priceScale;
    /** 加价率*/
    @LafValue("jdi.isc.product.soa.makeUpRateConfig")
    public String makeUpRateConfig;
    @LafValue("jdi.isc.price.purchase.scale")
    protected String purchaseScale;
    @Resource
    public SupplierSettlementAccountAtomicService supplierSettlementAccountAtomicService;

    @Resource
    private CountryAgreementPriceManageService countryAgreementPriceManageService;

    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;


    @Override
    public SkuCalculateTaxPriceVO skuTaxPrice(SkuCalculatePriceReqVO input){
        log.info("AbstractSkuTaxPriceManageService.skuTaxPrice input:{}", JSONObject.toJSONString(input));
        this.validateParam(input);
        String sourceCountryCode = input.getSourceCountryCode();
        String currencyCode = null;
        if(StringUtils.isNotBlank(input.getSupplierCode())){
            currencyCode = supplierSettlementAccountAtomicService.getCurrencyBySupplierCode(input.getSupplierCode());
        }else if(Objects.nonNull(input.getSkuId()) ){
            SkuPO sku = skuAtomicService.getSkuPoBySkuId(input.getSkuId());
            currencyCode = supplierSettlementAccountAtomicService.getCurrencyBySupplierCode(sku.getVendorCode());
        }else {

        }
        // countryManageService.getCurrencyByCountryCode(
        //默认增值税
        CategoryTaxVO categoryTaxVO = input.getCategoryTaxVO();
        BigDecimal categoryDestinationTax = categoryTaxVO != null?categoryTaxVO.getDestinationVatRate():null;

        BigDecimal valueAddTax = this.getFinalTaxRate(input.getTaxRate(),this.skuTaxRate(input.getSkuId()),categoryTaxVO != null?categoryTaxVO.getVatRate():null,sourceCountryCode);
        BigDecimal destinationVatRate = this.getValueAddTax(input.getTaxRate()==null?(categoryTaxVO != null?categoryDestinationTax:null):valueAddTax,sourceCountryCode);
        SkuPriceResVO skuPriceResVO = this.getSkuPrice(input.getSkuId(),input.getSkuPriceFlag(),sourceCountryCode,currencyCode);

        // 各种计算
        SkuCalculateTaxPriceVO skuCalculateTaxPriceVO = new SkuCalculateTaxPriceVO();
        skuCalculateTaxPriceVO.setCatId(input.getCatId());
        skuCalculateTaxPriceVO.setSourceCountryCode(sourceCountryCode);
        skuCalculateTaxPriceVO.setTaxRate(valueAddTax);
        skuCalculateTaxPriceVO.setDestinationVatRate(destinationVatRate);
        skuCalculateTaxPriceVO.setCurrency(currencyCode);
        if(skuPriceResVO != null){
            input.setPurchasePrice(skuPriceResVO.getPurchasePrice());
            input.setTaxPurchasePrice(skuPriceResVO.getTaxPurchasePrice());
            input.setSalePrice(skuPriceResVO.getSalePrice());
            input.setTaxSalePrice(skuPriceResVO.getTaxSalePrice());
        }
        skuCalculateTaxPriceVO.setPurchasePrice(this.calculatePurchase(input.getPurchasePrice(),valueAddTax,input.getTaxPurchasePrice()));
        skuCalculateTaxPriceVO.setTaxPurchasePrice(this.calculateTaxPurchase(input.getTaxPurchasePrice(),valueAddTax,input.getPurchasePrice()));
        skuCalculateTaxPriceVO.setSalePrice(this.calculateSale(input.getSalePrice(),valueAddTax,input.getTaxSalePrice()));
        skuCalculateTaxPriceVO.setTaxSalePrice(this.calculateTaxSale(input.getTaxSalePrice(),destinationVatRate,input.getSalePrice()));
        skuCalculateTaxPriceVO.setNoTaxGrossRate(this.calculateNoTaxGross(skuCalculateTaxPriceVO.getPurchasePrice(),skuCalculateTaxPriceVO.getSalePrice(),valueAddTax));
        skuCalculateTaxPriceVO.setTaxGrossRate(this.calculateTaxGross(skuCalculateTaxPriceVO.getPurchasePrice(),skuCalculateTaxPriceVO.getSalePrice()));
        skuCalculateTaxPriceVO.setMakeUpRate(this.getMakeUpRate(sourceCountryCode));
        // 设置国家成本价、国家协议价、利润率
        this.setCountryAgreementPriceAndRate(input, skuCalculateTaxPriceVO);
        return skuCalculateTaxPriceVO;
    }

    /**
     * 根据 SKU ID 获取对应商品的税率。
     * @param skuId SKU 的唯一标识。
     * @return 对应 SKU 的税率。
     */
    protected BigDecimal skuTaxRate(Long skuId){
        if(skuId == null){
            log.info("AbstractSkuTaxPriceManageService.skuTaxRate skuId is null");
            return null;
        }
        SkuPO skuPO = skuAtomicService.getSkuPoBySkuId(skuId);
        return spuTaxManageService.getParamTax(SkuConvert.INSTANCE.po2vo(skuPO));
    }

    /**
     * 根据 SKU ID 获取对应商品的税率。
     * @param skuId SKU 的唯一标识。
     * @return 对应 SKU 的税率。
     */
    protected SkuPriceResVO getSkuPrice(Long skuId,Boolean skuPriceFlag,String sourceCountryCode,String currencyCode){
        if(skuId == null || !skuPriceFlag){
            log.info("AbstractSkuTaxPriceManageService.getSkuPrice skuId is null or skuPriceFlag is false");
            return null;
        }
        SkuPriceReqVO priceReqVO = new SkuPriceReqVO();
        List<SkuPriceVO> skuPriceVOS = new ArrayList<>();
        SkuPriceVO skuPriceVO = new SkuPriceVO();
        skuPriceVO.setSkuId(skuId);
        skuPriceVO.setCurrency(currencyCode);
        skuPriceVO.setSourceCountryCode(sourceCountryCode);
        skuPriceVOS.add(skuPriceVO);
        priceReqVO.setSkuPriceVO(skuPriceVOS);
        Map<Long, SkuPriceResVO> skuPriceResVOMap = skuPriceManageService.list(priceReqVO);
        if(MapUtils.isEmpty(skuPriceResVOMap)){
            log.info("AbstractSkuTaxPriceManageService.getSkuPrice skuPriceResVOMap is null");
            return null;
        }
        return skuPriceResVOMap.get(skuId);
    }

    /**
     * 获取增值税值。
     * @param valueAddTax 增值税值，若不为空则直接返回该值。
     * @return 增值税值。
     */
    protected BigDecimal getValueAddTax(BigDecimal valueAddTax,String sourceCountryCode){
        if(valueAddTax != null){
            return valueAddTax;
        }
        return ConfigUtils.getValueByKey(defaultVatConfig, sourceCountryCode, BigDecimal.class);
    }

    /**
     * 获取最终的税率。
     * @param inputTaxRate 用户输入的税率。
     * @param skuTaxRate SKU对应的税率。
     * @param categoryTaxRate 类目对应的税率。
     * @return 最终的税率。
     */
    protected BigDecimal getFinalTaxRate(BigDecimal inputTaxRate,BigDecimal skuTaxRate,BigDecimal categoryTaxRate,String sourceCountryCode){
        if(inputTaxRate != null){
            return inputTaxRate.divide(Constant.DECIMAL_HUNDRED).setScale(4,RoundingMode.UP);
        }
        if(skuTaxRate != null){
            return skuTaxRate;
        }
        if(categoryTaxRate != null){
            return categoryTaxRate;
        }

        return this.getValueAddTax(null,sourceCountryCode);
    }

    /**
     * 采购价 含税计算未税。
     * @param purchasePrice 采购价格。
     * @param valueAddTax 增值税税率。
     * @param taxPurchasePrice 含税采购价格。
     * @return 未税采购价格。
     */
    protected BigDecimal calculatePurchase(BigDecimal purchasePrice, BigDecimal valueAddTax,BigDecimal taxPurchasePrice){
        log.info("AbstractSkuTaxPriceManageService.calculatePurchase");
        if(purchasePrice != null){
            log.info("AbstractSkuTaxPriceManageService.calculatePurchase not need calculate");
            return purchasePrice;
        }
        if(taxPurchasePrice == null || valueAddTax == null){
            log.error("AbstractSkuTaxPriceManageService.calculatePurchase param is null,taxPurchasePrice:{},valueAddTax:{}"
                    , JSONObject.toJSONString(taxPurchasePrice),JSONObject.toJSONString(valueAddTax));
            return null;
        }
        purchasePrice = taxPurchasePrice.divide(valueAddTax.add(BigDecimal.ONE),priceScale, RoundingMode.HALF_UP);

        if(!priceScale.equals(YesOrNoEnum.NO.getCode())){
            return purchasePrice;
        }
        return this.validateAndReturnNoTaxPrice(purchasePrice,taxPurchasePrice,valueAddTax);
    }

    /**
     * 采购价 计算含税采购价
     * @param purchasePrice 采购价。
     * @param valueAddTax 增值税率。
     * @return 含税采购价。
     */
    protected BigDecimal calculateTaxPurchase(BigDecimal taxPurchasePrice, BigDecimal valueAddTax,BigDecimal purchasePrice){
        if(taxPurchasePrice != null){
            log.info("AbstractSkuTaxPriceManageService.calculateTaxPurchase not need calculate");
            return taxPurchasePrice;
        }
        if(purchasePrice == null || valueAddTax == null){
            log.error("AbstractSkuTaxPriceManageService.calculateTaxPurchase param is null,purchasePrice:{},valueAddTax:{}"
                    , JSONObject.toJSONString(purchasePrice),JSONObject.toJSONString(valueAddTax));
            return null;
        }
        BigDecimal taxPrice = purchasePrice.multiply(valueAddTax).setScale(priceScale, RoundingMode.UP);
        return purchasePrice.add(taxPrice);
    }

    /**
     * 销售价，含税计算未税。
     * @param salePrice 销售价格
     * @param valueAddTax 增值税率
     * @param taxSalePrice 含税销售价格
     * @return 未税销售价格
     */
    protected BigDecimal calculateSale(BigDecimal salePrice, BigDecimal valueAddTax,BigDecimal taxSalePrice){
        if(salePrice != null){
            log.info("AbstractSkuTaxPriceManageService.calculateSale not need calculate");
            return salePrice;
        }
        if(taxSalePrice == null || valueAddTax == null){
            log.error("AbstractSkuTaxPriceManageService.calculateSale param is null,taxSalePrice:{},valueAddTax:{}"
                    , JSONObject.toJSONString(taxSalePrice),JSONObject.toJSONString(valueAddTax));
            return null;
        }
        salePrice = taxSalePrice.divide(valueAddTax.add(BigDecimal.ONE),priceScale, RoundingMode.HALF_UP);

        if(!priceScale.equals(YesOrNoEnum.NO.getCode())){
            return salePrice;
        }
        return this.validateAndReturnNoTaxPrice(salePrice,taxSalePrice,valueAddTax);
    }

    /**
     * 销售价格 计算含税销售价格
     * @param salePrice 销售价格。
     * @param valueAddTax 增值税税率。
     * @return 含税销售价格。
     */
    protected BigDecimal calculateTaxSale(BigDecimal taxSalePrice, BigDecimal valueAddTax,BigDecimal salePrice){
        if(taxSalePrice != null){
            log.info("AbstractSkuTaxPriceManageService.calculateTaxSale not need calculate");
            return taxSalePrice;
        }
        if(salePrice == null || valueAddTax == null){
            log.error("AbstractSkuTaxPriceManageService.calculateTaxSale param is null,salePrice:{},valueAddTax:{}"
                    , JSONObject.toJSONString(salePrice),JSONObject.toJSONString(valueAddTax));
            return null;
        }
        BigDecimal taxPrice = salePrice.multiply(valueAddTax).setScale(priceScale, RoundingMode.UP);
        return salePrice.add(taxPrice);
    }

    /**
     * 计算不含税毛利润百分比。
     * @param purchasePrice 采购价格。
     * @param salePrice 销售价格。
     * @param valueAddTax 增值税税率。
     * @return 不含税毛利润百分比。
     */
    protected BigDecimal calculateNoTaxGross(BigDecimal purchasePrice,BigDecimal salePrice,BigDecimal valueAddTax){
        if(purchasePrice == null || salePrice == null || valueAddTax == null){
            log.error("AbstractSkuTaxPriceManageService.calculateNoTaxGross param is null,salePrice:{},purchaseTaxPrice:{},valueAddTax:{}"
                    , JSONObject.toJSONString(salePrice),JSONObject.toJSONString(salePrice),JSONObject.toJSONString(valueAddTax));
            return null;
        }
        BigDecimal taxPrice = purchasePrice.multiply(valueAddTax).setScale(priceScale, RoundingMode.UP);
        // 不退税情况下毛利=（不含税销售价-不含税采购价-不含税采购价*税率）/不含税销售价*100%
        return salePrice.subtract(purchasePrice).subtract(taxPrice).divide(salePrice, 4, RoundingMode.UP)
                .multiply(new BigDecimal(100)).setScale(4, RoundingMode.UP);
    }

    /**
     * 计算税后毛利率。
     * @param purchasePrice 采购价格。
     * @param salePrice 销售价格。
     * @return 税后毛利率。
     */
    protected BigDecimal calculateTaxGross(BigDecimal purchasePrice,BigDecimal salePrice){
        if(purchasePrice == null || salePrice == null){
            log.error("AbstractSkuTaxPriceManageService.calculateTaxGross param is null,salePrice:{},purchaseTaxPrice:{}"
                    , JSONObject.toJSONString(salePrice),JSONObject.toJSONString(salePrice));
            return null;
        }
        // 正常退税情况下毛利=（不含税销售价-不含税采购价）/不含税销售价*100
        return salePrice.subtract(purchasePrice).divide(salePrice, 4, RoundingMode.UP)
                .multiply(new BigDecimal(100)).setScale(4, RoundingMode.UP);
    }

    /**
     * 通过未税计算并校验含税价格。
     * 未税价*（1+增值税率），看是否能得出含税价.
     * 如果不行，则未税价-1或+1后重新计算含税价格，进行试验。
     * 最多各重复10次
     * @param price 未税价格
     * @param taxPrice 含税价格
     * @param valueAddTax 增值税税率
     * @return 未税价格
     */
    protected BigDecimal validateAndReturnNoTaxPrice(BigDecimal price,BigDecimal taxPrice,BigDecimal valueAddTax){
        if(price == null || taxPrice == null || valueAddTax == null){
            log.error("AbstractSkuTaxPriceManageService.validateAndReturnNoTaxPrice param is null,price:{},taxPrice:{},valueAddTax:{}"
                    ,JSONObject.toJSONString(price),JSONObject.toJSONString(taxPrice),JSONObject.toJSONString(valueAddTax));
        }

        BigDecimal result = null;
        BigDecimal calculateAddPurchasePrice = new BigDecimal(price.toString());
        for(int m=0;m<10;m++){
            BigDecimal calculateTaxPurchasePrice = this.calculateTaxPurchase(null,valueAddTax,calculateAddPurchasePrice);
            if(calculateTaxPurchasePrice.equals(taxPrice)){
                result = calculateAddPurchasePrice;
                break;
            }
            calculateTaxPurchasePrice.add(BigDecimal.ONE);
        }

        if(result == null){
            BigDecimal calculateSubPurchasePrice = new BigDecimal(price.toString());
            for(int n=0;n<10;n++){
                BigDecimal calculateSubTaxPurchasePrice = this.calculateTaxPurchase(null,valueAddTax,calculateSubPurchasePrice);
                if(calculateSubTaxPurchasePrice.equals(taxPrice)){
                    result = calculateSubTaxPurchasePrice;
                    break;
                }
                calculateSubPurchasePrice.subtract(BigDecimal.ONE);
            }
        }

        if(result != null){
            return result;
        }
        return price;
    }


    /**
     * 验证价格计算请求参数的合法性。
     * @param priceReqVO 包含商品信息和计算价格所需参数的请求对象。
     */
    abstract void validateParam(SkuCalculatePriceReqVO priceReqVO);

    /**
     * 初始化价格计算的基础数据
     * @param priceReqVO SKU价格计算请求对象，包含了商品的基本信息和价格计算所需的参数
     */
    abstract void initVal(SkuCalculatePriceReqVO priceReqVO);

    /**
     * 查询跨境属性ID
     * @param dimensionEnum 维度
     * @param key 属性KEY
     * @return 返回属性ID
     */
    protected String getAttributeId(AttributeDimensionEnum dimensionEnum, String key){
        return spuTaxManageService.getAttributeId(dimensionEnum, key);
    }

    /**
     * 获取加价率
     * @return
     */
    protected BigDecimal getMakeUpRate(String sourceCountryCode) {
        if (StringUtils.isBlank(makeUpRateConfig)) {
            return null;
        }
        // 加价率
        String makeUpRate = ConfigUtils.getStringByKey(makeUpRateConfig, sourceCountryCode);
        if (StringUtils.isBlank(makeUpRate)) {
            return null;
        }

        return new BigDecimal(makeUpRate);
    }


    private void queryCountryAgreementPriceAndRate(SkuCalculatePriceReqVO input, SkuCalculateTaxPriceVO priceResultVo){
        CountryAgreementPriceReqVO reqVO = new CountryAgreementPriceReqVO();
        reqVO.setSkuIds(Lists.newArrayList(input.getSkuId()));
        reqVO.setSourceCountryCode(input.getSourceCountryCode());
        reqVO.setTargetCountryCode(input.getSourceCountryCode());
        Map<Long, CountryAgreementPricePageVO> agreementPricePageVOMap =
            countryAgreementPriceManageService.queryCountryAgreementPriceBySkuIds(reqVO);

        if (MapUtils.isEmpty(agreementPricePageVOMap)) {
            log.warn(
                "AbstractSkuTaxPriceManageService.setCountryAgreementPriceAndRate 商品:[{}] 没有国家协议价信息 入参:[{}]",
                input.getSkuId(), JSON.toJSONString(input));
            return;
        }
        CountryAgreementPricePageVO pricePageVO = agreementPricePageVOMap.get(input.getSkuId());
        if (Objects.nonNull(pricePageVO)) {
            priceResultVo.setCountryCostPrice(pricePageVO.getCountryCostPrice());
            priceResultVo.setAgreementPrice(pricePageVO.getAgreementPrice());
            priceResultVo.setAgreementProfitRate(pricePageVO.getProfitRate());
            priceResultVo.setCostCurrency(pricePageVO.getCurrency());
        }
    }


   /**
    * 根据输入的商品信息和国家代码，获取相应的国家协议价格和利润率，并将结果设置到输出对象中。
    * @param input SkuCalculatePriceReqVO对象，包含商品ID和源国家代码。
    * @param priceResultVo SkuCalculateTaxPriceVO对象，用于存储计算结果。
    */
   protected void setCountryAgreementPriceAndRate(SkuCalculatePriceReqVO input, SkuCalculateTaxPriceVO priceResultVo) {
       if (Objects.isNull(input.getCalculatePriceEnums())) {
           return;
       }
       if(CalculatePriceEnums.INQUIRE.equals(input.getCalculatePriceEnums())){
           queryCountryAgreementPriceAndRate(input,priceResultVo);
       }else if (CalculatePriceEnums.CALCULATED.equals(input.getCalculatePriceEnums())){
           calculateCountryAgreementPriceAndRate(input,priceResultVo);
       }
   }


    private void calculateCountryAgreementPriceAndRate(SkuCalculatePriceReqVO input, SkuCalculateTaxPriceVO priceResultVo) {
        InitAgreementPriceVO initAgreementPriceVO  = new InitAgreementPriceVO();
        initAgreementPriceVO.setSkuId(input.getSkuId());
        initAgreementPriceVO.setSourceCountryCode(input.getSourceCountryCode());
        initAgreementPriceVO.setTargetCountryCode(input.getSourceCountryCode());
        initAgreementPriceVO.setPrice(priceResultVo.getPurchasePrice());
        initAgreementPriceVO.setCurrency(priceResultVo.getCurrency());
        initAgreementPriceVO.setIsWareHouseProduct(FulfillmentRateTypeEnum.DIRECT_DELIVERY.getCode());
        AgreementAndCostPriceVO agreementAndCostPriceVO = countryAgreementPriceManageService.calculateAgreementAndCostPrice(initAgreementPriceVO);
        if (agreementAndCostPriceVO == null){
            priceResultVo.setCostMark("无法计算出国家成本价及国家协议价");
            return;
        }
        List<Long> skuIdList = new ArrayList<>();
        skuIdList.add(input.getSkuId());
        String targetCountryCode = input.getSourceCountryCode();
        List<CountryAgreementPricePO> countryAgreementPriceList = countryAgreementPriceAtomicService.getCountryAgreementPriceList(skuIdList, targetCountryCode);
        if(CollectionUtils.isEmpty(countryAgreementPriceList)){
            priceResultVo.setCostMark("未查询到价格管控中的协议价");
            return;
        }
        CountryAgreementPricePO countryAgreementPricePO = countryAgreementPriceList.get(0);

        if(agreementAndCostPriceVO.getCostPrice() == null || countryAgreementPricePO.getAgreementPrice() == null) {
            priceResultVo.setAgreementMark(countryAgreementPricePO.getAgreementMark());
            priceResultVo.setCostMark(agreementAndCostPriceVO.getCostMsg());
            return;
        }

        if (countryAgreementPricePO.getAgreementPrice().compareTo(BigDecimal.ZERO) == 0) {
            priceResultVo.setAgreementMark("国家协议价为零");
            return;
        }
//        BigDecimal ratio = agreementAndCostPriceVO.getCostPrice().divide(countryAgreementPricePO.getAgreementPrice(), 4, RoundingMode.HALF_UP);
//        BigDecimal profitRate = BigDecimal.ONE.subtract(ratio);

        BigDecimal profitRate = this.computeAgreementProfitRate(agreementAndCostPriceVO.getCostPrice(), countryAgreementPricePO.getAgreementPrice());

        priceResultVo.setAgreementMark(countryAgreementPricePO.getAgreementMark());
        priceResultVo.setCostMark(agreementAndCostPriceVO.getCostMsg());
        priceResultVo.setCountryCostPrice(agreementAndCostPriceVO.getCostPrice());
        priceResultVo.setAgreementPrice(countryAgreementPricePO.getAgreementPrice());
        priceResultVo.setAgreementProfitRate(profitRate);
        priceResultVo.setCostCurrency(agreementAndCostPriceVO.getCurrency());
    }

    public BigDecimal computeAgreementProfitRate(BigDecimal costPrice, BigDecimal agreementPrice) {

       if (costPrice == null || agreementPrice == null) {
           log.error("成本价或协议价为空");
            return null;
        }

        BigDecimal ratio = costPrice.divide(agreementPrice, 4, RoundingMode.HALF_UP);
        return BigDecimal.ONE.subtract(ratio);
    }

    @Override
    public SkuPredictPriceResDTO predictCountryCostPrice(SkuPredictPriceReqDTO query) {
        throw new BizException("本功能暂未开放");
    }
}
