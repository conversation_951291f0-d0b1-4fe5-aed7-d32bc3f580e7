package com.jdi.isc.product.soa.service.atomic.countryMku;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuPageVO;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuVO;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.repository.jed.mapper.countryMku.CountryMkuBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 商品国家池表原子服务
 * @Author: wangpeng965
 * @Date: 2024/12/02 21:49
 **/
@Slf4j
@Service
public class CountryMkuAtomicService extends ServiceImpl<CountryMkuBaseMapper, CountryMkuPO> {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    public List<CountryMkuPageVO.Response> pageSearch(CountryMkuPageVO.Request input){
        input.setOffset((input.getIndex()-1)*input.getSize());
        return super.getBaseMapper().pageSearch(input);
    }

    /**
     * 分页查询的列表总数
     * @param input 查询条件
     * @return 总条数
     */
    public long pageSearchTotal(CountryMkuPageVO.Request input){
        return super.getBaseMapper().pageSearchTotal(input);
    }

    /**
     * 未删除的对象
     * @param id id
     * @return 对象
     */
    public CountryMkuPO getValidById(Long id){
        LambdaQueryWrapper<CountryMkuPO> wrapper = Wrappers.<CountryMkuPO>lambdaQuery()
                .eq(CountryMkuPO::getId, id)
                .eq(CountryMkuPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }


    /**
     * 根据ID范围获取有效的国家信息。
     * @param beginId 起始ID
     * @param endId 结束ID
     * @return 符合条件的国家信息列表
     */
    public List<CountryMkuPO> getValidByIdRange(Long beginId,Long endId){
        LambdaQueryWrapper<CountryMkuPO> wrapper = Wrappers.<CountryMkuPO>lambdaQuery()
            .gt(CountryMkuPO::getId, beginId)  // ID > beginId
            .lt(CountryMkuPO::getId, endId) // ID < endId
            .eq(CountryMkuPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    /**
     * 根据给定的 id 集合获取有效的 CountryMkuPO 对象。
     * @param ids 要查询的 CountryMkuPO 对象的 id 集合。
     * @return 满足条件的 CountryMkuPO 对象，若无则返回 null。
     */
    public List<CountryMkuPO> getValidByIds(Collection<Long> ids){
        if(CollectionUtils.isEmpty(ids)){
            return null;
        }

        LambdaQueryWrapper<CountryMkuPO> wrapper = Wrappers.<CountryMkuPO>lambdaQuery()
                .in(CountryMkuPO::getId, ids)
                .eq(CountryMkuPO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }

    /**
     * 根据 MKU ID 和目标国家代码获取 CountryMkuPO 列表。
     * @param mkuId MKU ID
     * @param targetCountryCode 目标国家代码
     * @return CountryMkuPO 列表
     */
    public List<CountryMkuPO> getByMkuIdAndCountryCode(Long mkuId,String targetCountryCode){
        if(mkuId == null || StringUtils.isBlank(targetCountryCode)){
            return null;
        }
        LambdaQueryWrapper<CountryMkuPO> qw = new LambdaQueryWrapper<>();
        qw.eq(CountryMkuPO::getMkuId,mkuId);
        qw.eq(CountryMkuPO::getTargetCountryCode,targetCountryCode);
        qw.eq(CountryMkuPO::getPoolStatus, CountryMkuPoolStatusEnum.POOL.getCode());
        qw.eq(CountryMkuPO::getYn,YnEnum.YES.getCode());
        return this.list(qw);
    }


    /**
     * 根据 MKU ID 和目标国家代码获取 CountryMkuPO 列表。
     * @param mkuId MKU ID
     * @param targetCountryCode 目标国家代码
     * @return CountryMkuPO 列表
     */
    public CountryMkuPO getByMkuIdCountryCode(Long mkuId,String targetCountryCode){
        if(mkuId == null || StringUtils.isBlank(targetCountryCode)){
            return null;
        }
        LambdaQueryWrapper<CountryMkuPO> qw = new LambdaQueryWrapper<>();
        qw.eq(CountryMkuPO::getMkuId,mkuId);
        qw.eq(CountryMkuPO::getTargetCountryCode,targetCountryCode);
        qw.eq(CountryMkuPO::getYn,YnEnum.YES.getCode());
        List<CountryMkuPO> list = this.list(qw);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    /**
     * 根据 MKU ID 列表和目标国家代码获取 CountryMkuPO 对象列表。
     * @param mkuIds MKU ID 列表
     * @param targetCountryCode 目标国家代码
     * @return 满足条件的 CountryMkuPO 对象列表
     */
    public List<CountryMkuPO> getPoByMkuIdAndCountryCode(List<Long> mkuIds,String targetCountryCode){
        if(CollectionUtils.isEmpty(mkuIds) || StringUtils.isBlank(targetCountryCode)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CountryMkuPO> wrapper = Wrappers.<CountryMkuPO>lambdaQuery()
            .in(CollectionUtils.isNotEmpty(mkuIds),CountryMkuPO::getMkuId,mkuIds)
            .eq(CountryMkuPO::getTargetCountryCode,targetCountryCode)
            .eq(CountryMkuPO::getPoolStatus, CountryMkuPoolStatusEnum.POOL.getCode())
            .eq(CountryMkuPO::getYn,YnEnum.YES.getCode());
        List<CountryMkuPO> list = super.list(wrapper);
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 根据 MKU ID 和目标国家代码获取 CountryMkuPO 列表。
     * @param mkuIds MKU ID
     * @return CountryMkuPO 列表
     */
    public List<CountryMkuPO> getByMkuIds(Collection<Long> mkuIds){
        if(CollectionUtils.isEmpty(mkuIds)){
            return null;
        }
        LambdaQueryWrapper<CountryMkuPO> qw = new LambdaQueryWrapper<>();
        qw.in(CountryMkuPO::getMkuId,mkuIds);
        qw.eq(CountryMkuPO::getYn,YnEnum.YES.getCode());
        return this.list(qw);
    }

    public Boolean updateStatus(String countryCode,Integer sourceStatus,Integer targetStatus){
        LambdaUpdateWrapper<CountryMkuPO> wrapper = Wrappers.<CountryMkuPO>lambdaUpdate()
            .set(CountryMkuPO::getWarnStatus, targetStatus)
            .eq(CountryMkuPO::getTargetCountryCode,countryCode)
            .eq(CountryMkuPO::getWarnStatus,sourceStatus)
            .eq(CountryMkuPO::getPoolStatus,1)
            .eq(CountryMkuPO::getWarnReason,"0")
            .eq(CountryMkuPO::getYn,YnEnum.YES.getCode());
        return super.update(wrapper);
    }

    public List<CountryMkuPO> listSearch(List<CountryMkuVO> voList){
        return super.getBaseMapper().listSearch(voList);
    }

    public List<CountryMkuPO> getPoByMkuIdCountryPoolStatus(List<Long> mkuIds,String targetCountryCode,Integer poolStatus){
        if(CollectionUtils.isEmpty(mkuIds) || StringUtils.isBlank(targetCountryCode)){
            return null;
        }
        LambdaQueryWrapper<CountryMkuPO> wrapper = Wrappers.<CountryMkuPO>lambdaQuery()
            .in(CollectionUtils.isNotEmpty(mkuIds),CountryMkuPO::getMkuId,mkuIds)
            .eq(CountryMkuPO::getTargetCountryCode,targetCountryCode)
            .eq(Objects.nonNull(poolStatus),CountryMkuPO::getPoolStatus,poolStatus)
            .eq(CountryMkuPO::getYn,YnEnum.YES.getCode());
        List<CountryMkuPO> list = super.list(wrapper);
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list;
    }


    /**
     * 根据目标国家代码和池状态获取PO列表。
     * @param targetCountryCode 目标国家代码。
     * @param warnStatus 池状态。
     * @return PO列表。
     */
    public List<CountryMkuPO> getPoByCountryAndPool(String targetCountryCode,Integer warnStatus){
        if(StringUtils.isBlank(targetCountryCode) || Objects.isNull(warnStatus)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CountryMkuPO> wrapper = Wrappers.<CountryMkuPO>lambdaQuery()
            .eq(CountryMkuPO::getTargetCountryCode,targetCountryCode)
            .eq(CountryMkuPO::getWarnStatus,warnStatus)
            .eq(CountryMkuPO::getPoolStatus,CountryMkuPoolStatusEnum.POOL.getCode())
            .eq(CountryMkuPO::getYn,YnEnum.YES.getCode());
        List<CountryMkuPO> list = super.list(wrapper);
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list;
    }
}
