package com.jdi.isc.product.soa.service.manage.spu.impl.write;

import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.TradeTypeConstant;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.SaleAttributeUNhandleContextHolder;
import com.jdi.isc.product.soa.common.frame.SystemContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.util.validation.PropertyError;
import com.jdi.isc.product.soa.common.util.validation.ValidateResult;
import com.jdi.isc.product.soa.common.util.validation.ValidationUtil;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.product.AuditLevelEnum;
import com.jdi.isc.product.soa.domain.enums.spu.SpuTaxCountryEnums;
import com.jdi.isc.product.soa.domain.enums.vendor.IdentityEnum;
import com.jdi.isc.product.soa.domain.price.supplierPrice.draft.SkuPriceDraftVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.GroupPropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.SaveSpuVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuVO;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.validation.ValidateSpuGroup;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDraftAtomicService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceDraftManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuTaxManageService;
import com.jdi.isc.product.soa.service.manage.spu.validate.SkuValidateService;
import com.jdi.isc.product.soa.service.manage.spu.validate.SpuValidateService;
import com.jdi.isc.product.soa.service.mapstruct.SkuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.Constant.COMMA;


/**
 * <AUTHOR>
 * @date 2024/5/9
 **/
@Slf4j
@Service
public class BrSpuWriteManageService extends AbstractSpuWriteManageService {

    @Resource
    private SpuValidateService spuValidateService;

    @Resource
    private SkuValidateService skuValidateService;

    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;

    @Resource
    private SpuConvertService spuConvertService;
    @Resource
    private SkuConvertService skuConvertService;

    @Resource
    @Lazy
    private Map<String, SpuTaxManageService> spuTaxManageServiceMap;

    @Resource
    private SkuPriceDraftManageService skuPriceDraftManageService;


    @Override
    public DataResponse<Long> create(SaveSpuVO saveSpuVO) {
        this.checkSaveSpuVo(saveSpuVO);
        // 补充spu、sku状态信息 发品人身份、系统编码、站点
        SpuVO spuVO = saveSpuVO.getSpuVO();
        spuVO.setIdentityFlag(IdentityEnum.BUYER.getCode());
        return super.create(saveSpuVO);
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Long> saveDraft(SaveSpuVO saveSpuVO) {
        this.checkSaveSpuVo(saveSpuVO);
        this.validateSaveParams(saveSpuVO);
        SpuVO spuVO = saveSpuVO.getSpuVO();

        if(StringUtils.isBlank(spuVO.getAttributeScope())
                && !StringUtils.equals(spuVO.getSourceCountryCode(),CountryConstant.COUNTRY_ZH)){
            spuVO.setAttributeScope(spuVO.getSourceCountryCode());
        }
        if(StringUtils.isBlank(spuVO.getSpuName())){
            spuConvertService.getSpuName(spuVO);
        }

        Long spuId = spuVO.getSpuId();
        // 第一次保存，生成spuId并设置草稿
        if (Objects.isNull(spuId)) {
            spuId = super.generateId();
            spuVO.setSpuId(spuId);
            spuVO.setBuyer(categoryBuyerRelationAtomicService.getBuyerByCountryCodeAndCatId(spuVO.getSourceCountryCode(),spuVO.getCatId()));
            this.validateCreateSkuVoList(saveSpuVO.getSkuVOList(),saveSpuVO.getSpuVO());
            // 补充销售价格，根据不同国家配置的加价率
            skuConvertService.fillSalePriceForCreateProduct(spuVO.getSystemCode(),spuVO.getSourceCountryCode(),saveSpuVO.getSkuVOList());
        }

        SpuDraftPO draftPO = spuDraftAtomicService.getBySpuId(spuId);
        if(draftPO != null){
            this.validateUpdateSkuVoList(saveSpuVO.getSkuVOList(),saveSpuVO.getSpuVO());
        }
        // 审核状态为草稿态;审核层级为0
        spuVO.setLevel(AuditLevelEnum.ZERO.getLevel());
        spuVO.setAuditStatus(SpuAuditStatusEnum.DRAFT.getCode());
        super.checkSkuList(saveSpuVO);
        this.handleSkuList(saveSpuVO);
        SpuPO spuPo = spuAtomicService.getSpuPoBySpuId(spuId);
        if (Objects.nonNull(spuPo)) {
            saveSpuVO.getSpuVO().setBuyer(spuPo.getBuyer());
            // 存在SPU主数据，更新审核状态为草稿
            spuAtomicService.updateSpuAuditStatus(spuId, SpuAuditStatusEnum.DRAFT);
        }
        // 保存或更新草稿
        super.saveOrUpdateDraft(saveSpuVO);
        return DataResponse.success(spuId);
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Long> submit(SaveSpuVO saveSpuVO) {
        this.checkSaveSpuVo(saveSpuVO);
        this.validateSaveParams(saveSpuVO);
        // 校验sku是否存在
        super.checkSkuList(saveSpuVO);
        this.handleSkuList(saveSpuVO);
        String erp = LoginContextHolder.getLoginContextHolder().getPin();
        SpuVO spuVO = saveSpuVO.getSpuVO();
        if(StringUtils.isBlank(spuVO.getAttributeScope())
                && !StringUtils.equals(spuVO.getSourceCountryCode(),CountryConstant.COUNTRY_ZH)){
            spuVO.setAttributeScope(spuVO.getSourceCountryCode());
        }
        if(StringUtils.isBlank(spuVO.getSpuName())){
            spuConvertService.getSpuName(spuVO);
        }
        super.fillSpuInfoToSkuVo(saveSpuVO,saveSpuVO.getSkuVOList());
        Long spuId = spuVO.getSpuId();
        boolean isNew = Objects.isNull(spuId);
        // 第一次提交，生成草稿，状态为待审核
        if (Objects.isNull(spuId)) {
            spuId = super.generateId();
            spuVO.setSpuId(spuId);
            spuVO.setBuyer(categoryBuyerRelationAtomicService.getBuyerByCountryCodeAndCatId(spuVO.getSourceCountryCode(),spuVO.getCatId()));
            this.validateCreateSkuVoList(saveSpuVO.getSkuVOList(),saveSpuVO.getSpuVO());
            // 补充销售价格，根据不同国家配置的加价率
            skuConvertService.fillSalePriceForCreateProduct(spuVO.getSystemCode(),spuVO.getSourceCountryCode(),saveSpuVO.getSkuVOList());
        }

        SpuDraftPO draftPO = spuDraftAtomicService.getBySpuId(spuId);
        if(draftPO != null){
            this.validateUpdateSkuVoList(saveSpuVO.getSkuVOList(),saveSpuVO.getSpuVO());
        }

        // 校验采购价
        this.checkPurchasePrice(saveSpuVO);

        // 审核状态修改为待审核;审核层级为0
        if(saveSpuVO.isNotAudit()) {
            saveSpuVO.setNotAudit(false);
            super.saveOrUpdateDraft(saveSpuVO);
            return DataResponse.success(spuId);
        }
        spuVO.setLevel(AuditLevelEnum.ZERO.getLevel());
        spuVO.setAuditStatus(SpuAuditStatusEnum.WAITING_APPROVED.getCode());
        SpuPO spuPo = spuAtomicService.getSpuPoBySpuId(spuId);
        if (Objects.nonNull(spuPo)) {
            saveSpuVO.getSpuVO().setBuyer(spuPo.getBuyer());
            saveSpuVO.getSpuVO().setSpuStatus(spuPo.getSpuStatus());
            saveSpuVO.getSpuVO().setTaxAuditStatus(spuPo.getTaxAuditStatus());
            // 存在SPU主数据，更新审核状态为待审核状态
            spuAtomicService.updateSpuAuditStatus(spuId, SpuAuditStatusEnum.WAITING_APPROVED);
        }

        if (!isNew){
            // 增加修改记录
            String compareResult = spuCompareService.compareSaveSpu(saveSpuVO);
            log.info("LocalSpuWriteManageService.submit spuId={} erp={} compareResult={}", spuId, erp, compareResult);
            super.addAuditRecord(spuId, erp, AuditStatusEnum.WAITING_APPROVED, compareResult);
        }
        // 保存或更新草稿
        super.saveOrUpdateDraft(saveSpuVO);

        // 提交时需提交税率审核审批
        try {
            // 设置跳过销售属性处理 ZHAOYAN_SALE_ATTR  实属无奈
            SaleAttributeUNhandleContextHolder.set();
            this.spuTaxManageService(spuVO.getSourceCountryCode()).batchSubmit(saveSpuVO);
        } finally {
            // 移除跳过销售属性处理 ZHAOYAN_SALE_ATTR
            SaleAttributeUNhandleContextHolder.remove();
        }
        return DataResponse.success(spuId);
    }

    /**
     * 校验 未税采购价 + 税金 = 含税采购价
     */
    private void checkPurchasePrice(SaveSpuVO saveSpuVO) {

        try {
            List<SkuVO> skus = saveSpuVO.getSkuVOList();

            if (CollectionUtils.isEmpty(skus)) {
                return;
            }

            for (SkuVO sku : skus) {
                // 含税采购价
                String taxPurchasePrice = sku.getTaxPurchasePrice();
                // 未税采购价
                String purchasePrice = sku.getPurchasePrice();

                SkuPriceDraftVO input = new SkuPriceDraftVO();
                input.setTaxPrice(StringUtils.isEmpty(taxPurchasePrice) ? null : new BigDecimal(taxPurchasePrice));
                input.setPrice(StringUtils.isEmpty(purchasePrice) ? null : new BigDecimal(purchasePrice));
                List<PropertyVO> properties = sku.getSkuInterPropertyList().stream().map(GroupPropertyVO::getPropertyVOS).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
                input.setPropertyVos(properties);

                skuPriceDraftManageService.checkSkuPriceDraftForBrazil(input);
            }
        } catch (Exception e) {
            if (e instanceof BizException) {
                throw e;
            }
            // 不要因为校验失败，导致提交失败
            log.error("checkPurchasePrice is error. ", e);
        }

    }


    @Override
    void validateSaveParams(SaveSpuVO saveSpuVO) {
        spuValidateService.validateSaveParams(saveSpuVO);
        spuValidateService.validateVendorCountry(saveSpuVO.getSpuVO().getVendorCode(), saveSpuVO.getSpuVO().getSourceCountryCode());
        SpuVO spuVO = saveSpuVO.getSpuVO();
        spuValidateService.validateBusinessLineRelationAndSetVendorCode(spuVO);
    }


    @Override
    void validateCreateSkuVoList(List<SkuVO> skuVOList,SpuVO spuVO) {
        skuValidateService.validateCreateSkuVoList(skuVOList);
        skuValidateService.validateSkuStockList(skuVOList,spuVO.getSourceCountryCode());
        // 校验价格
        skuValidateService.validateSkuPrice(skuVOList, spuVO.getSourceCountryCode());
        // 校验长宽高重
        skuValidateService.validateFourDimension(skuVOList);
        // 校验跨境属性
        skuValidateService.validateInterProperty(spuVO.getCatId()
                ,spuVO.getSourceCountryCode()
                ,StringUtils.isNotBlank(spuVO.getAttributeScope()) ? Arrays.asList(spuVO.getAttributeScope().split(COMMA)) : Lists.newArrayList()
                ,skuVOList,spuVO.getIsExport());
        // 校验跨境资质
        skuValidateService.validateCertificate(spuVO.getCatId()
                ,spuVO.getSourceCountryCode()
                ,StringUtils.isNotBlank(spuVO.getAttributeScope()) ? Arrays.asList(spuVO.getAttributeScope().split(COMMA)) : Lists.newArrayList()
                ,skuVOList,spuVO.getIsExport());
        // 校验发货时效
        skuValidateService.validateProductionCycle(skuVOList);
        // 校验出口HsCode
        skuValidateService.validateHsCode(skuVOList);
        // 校验条形码
        skuValidateService.validateUpcCode(skuVOList);
        // 本土品新建sku销售属性校验  ZHAOYAN_SALE_ATTR
        skuValidateService.validateCreateSkuSaleAttributeForLocal(spuVO,skuVOList);
    }

    @Override
    void validateUpdateSkuVoList(List<SkuVO> skuVOList, SpuVO spuVO) {
        List<SkuPO> dbSkuPoList = skuAtomicService.selectSkuPosBySpuId(spuVO.getSpuId());
        skuValidateService.validateUpdateSkuVOList(skuVOList, spuVO.getSpuId(), dbSkuPoList);
        skuValidateService.validateSkuStockList(skuVOList,spuVO.getSourceCountryCode());
        // 校验价格
        skuValidateService.validateSkuPrice(skuVOList, spuVO.getSourceCountryCode());
        // 校验长宽高重
        skuValidateService.validateFourDimension(skuVOList);
        // 校验跨境属性
        skuValidateService.validateInterProperty(spuVO.getCatId()
                ,spuVO.getSourceCountryCode()
                ,StringUtils.isNotBlank(spuVO.getAttributeScope()) ? Arrays.asList(spuVO.getAttributeScope().split(COMMA)) : Lists.newArrayList()
                ,skuVOList,spuVO.getIsExport());
        // 校验跨境资质
        skuValidateService.validateCertificate(spuVO.getCatId()
                ,spuVO.getSourceCountryCode()
                ,StringUtils.isNotBlank(spuVO.getAttributeScope()) ? Arrays.asList(spuVO.getAttributeScope().split(COMMA)) : Lists.newArrayList()
                ,skuVOList,spuVO.getIsExport());
        // 校验发货时效
        skuValidateService.validateProductionCycle(skuVOList);
        // 校验出口HsCode
        skuValidateService.validateHsCode(skuVOList);
        // 校验条形码
        skuValidateService.validateUpcCode(skuVOList);
        // 本土品更新sku销售属性校验  ZHAOYAN_SALE_ATTR
        skuValidateService.validateUpdateSkuSaleAttributeForLocal(spuVO,skuVOList);
        // 校验扩展属性
        skuValidateService.validateSkuExtendProperty(skuVOList);
    }

    @Override
    void validateDbSaveSpuVoAndViewSaveSpuVo(SaveSpuVO saveSpuVO) {
        spuValidateService.validateDbSaveSpuVoAndViewSaveSpuVo(saveSpuVO);
    }

    @Override
    void recordVendor(List<SkuVO> skuVOList) {

    }

    private void handleSkuList(SaveSpuVO saveSpuVO){
        List<@Valid SkuVO> skuVOList = saveSpuVO.getSkuVOList();
        if (CollectionUtils.isNotEmpty(skuVOList)) {
            skuVOList.forEach(skuVO -> {
                if (StringUtils.isBlank(skuVO.getVendorTradeType())){
                    skuVO.setVendorTradeType(TradeTypeConstant.BD);
                }
                if (StringUtils.isBlank(skuVO.getCustomerTradeType())) {
                    skuVO.setCustomerTradeType(TradeTypeConstant.BD);
                }
            });
        }
    }

    private void checkSaveSpuVo(SaveSpuVO saveSpuVO) {
        this.validateAndThrowException(saveSpuVO);
        // 本土特殊字段校验
        this.validateAndThrowException(saveSpuVO, ValidateSpuGroup.localSpu.class);
    }

    private void validateAndThrowException(SaveSpuVO saveSpuVO,Class<?> ... groups){
        ValidateResult<SaveSpuVO> validateResult = ValidationUtil.checkParam(saveSpuVO,groups);
        if (!validateResult.getSuccess()){
            Set<String> messageSet = validateResult.getPropertyErrors().stream().map(PropertyError::getMessage).collect(Collectors.toSet());
            throw new BizException(String.join(Constant.COMMA,messageSet));
        }
    }

    @Override
    void updateStatusToWaitApprove(Long spuId) {
        // 更新spu审核状态
        spuAtomicService.updateSpuAuditStatus(spuId, SpuAuditStatusEnum.WAITING_APPROVED);
        log.info("更新商品状态完成,spuId={}", spuId);
    }


    private SpuTaxManageService spuTaxManageService(String sourceCountryCode) {
        return spuTaxManageServiceMap.get(SpuTaxCountryEnums.getEnumByCountryCode(sourceCountryCode).getServiceName());
    }

}
