package com.jdi.isc.product.soa.service.manage.taxRate.countryTax.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.sku.IscProductSoaSkuWriteApiService;
import com.jdi.isc.product.soa.api.sku.req.SkuSyncNcmDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.BrSkuTaxPO;
import com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum;
import com.jdi.isc.product.soa.price.api.price.req.BrSkuTaxVO;
import com.jdi.isc.product.soa.service.adapter.mapstruct.countryTax.BrSkuTaxConvert;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.countryTax.BrSkuTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.sku.SkuWriteManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.BrSkuTaxManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 巴西税率管理服务
 * <AUTHOR>
 * @date 2025/3/10
 */
@Slf4j
@Service
public class BrSkuTaxManageServiceImpl extends BaseTaxManageService implements BrSkuTaxManageService {

    @Resource
    private BrSkuTaxAtomicService brSkuTaxAtomicService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private SkuWriteManageService skuWriteManageService;

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> saveOrUpdate(BrSkuTaxVO input) {
        boolean flag;
        //将关税除以100保存
        input.setImportTax(input.getImportTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setIndustryProductTax(input.getIndustryProductTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setSocialIntegrationTax(input.getSocialIntegrationTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setConfinsTax(input.getConfinsTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setAntiDumpingTax(input.getAntiDumpingTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setIcmsFlowTax(input.getIcmsFlowTax().divide(factor,5, RoundingMode.HALF_UP));
        JdProductDTO skuPO = getSku(input.getJdSkuId());
        if(skuPO==null){
            return DataResponse.error(String.format("零售skuId:%s 不存在,请检查",input.getJdSkuId()));
        }

        // JDSKUID 和进口sku 是1对1关系, 历史数据可能存在1对多场景, 所以这里查到多条sku,NCM码都需要修改一下
        // fix 产品确定去掉该逻辑
//        List<SkuPO> skus = skuAtomicService.listSkuPoByJdSkuId(input.getJdSkuId());
//
//        if (CollectionUtils.isEmpty(skus)) {
//            log.warn("此jdskuid:{} 无对应的国际sku，请先督促跨境采销完成跨境商品二发。", input.getJdSkuId());
//            return DataResponse.error(String.format("此jdskuid:%s 无对应的国际sku，请先督促跨境采销完成跨境商品二发。", input.getJdSkuId()));
//        }

        BrSkuTaxPO res = brSkuTaxAtomicService.getOne(input);
        if(res==null){
            BrSkuTaxPO target = BrSkuTaxConvert.INSTANCE.vo2Po(input);
            target.setCreateTime(new Date());
            target.setUpdateTime(target.getCreateTime());
            flag = brSkuTaxAtomicService.save(target);
            recordLog(target.getJdSkuId(), PriceTypeEnum.BR_TAX, target.getImportTax(),target,flag);
        }else {
            input.setId(res.getId());
            flag = brSkuTaxAtomicService.updateTax(input);
            recordLog(input.getJdSkuId(), PriceTypeEnum.BR_TAX, input.getImportTax(),input,flag);
        }

        // https://joyspace.jd.com/pages/UaxqBrCBhgsMEqqmkkFu
        // 保存巴西进口税率的时候，需要修改进口商品的NCM 码

//        try {
//            this.saveProductGlobalNcmAttribute(skus, input.getNcmCode(), input.getUpdater());
//        } catch (Exception e) {
//            log.error("保存sku的ncm属性失败, input={}", JSONObject.toJSONString(input), e);
//            return DataResponse.error("更新sku的ncm属性失败!");
//        }

        return DataResponse.success(flag);
    }

    @Override
    public DataResponse<Integer> batchFixBrNcmCode(List<Long> jdSkuIds, boolean update, String updater) {
        // 如果为空则修正所有的跨境商品
        if (CollectionUtils.isEmpty(jdSkuIds)) {
            long start = System.currentTimeMillis();
            jdSkuIds = this.getJdSkuIdsFromTaxRate();
            log.info("getJdSkuIdsFromTaxRate, cost={}, jdSkuIds.size={}", jdSkuIds.size(), System.currentTimeMillis() - start);
        }

        if (!update) {
            log.info("需要更新的数据 size={}. jdSkuIds={}", jdSkuIds.size(), jdSkuIds);
            return DataResponse.success(jdSkuIds.size());
        }

        int count = 0;

        // 一次性任务, 数据量不大, 直接循环处理了
        for (Long jdSkuId : jdSkuIds) {
            // JDSKUID 和进口sku 是1对1关系, 历史数据可能存在1对多场景, 所以这里查到多条sku,NCM码都需要修改一下
            List<SkuPO> skus = skuAtomicService.listSkuPoByJdSkuId(jdSkuId);

            if (CollectionUtils.isEmpty(skus)) {
                log.info("sku 数据不存在, jdSkuId={}。[跳过处理]", jdSkuId);
//                log.warn("此jdskuid:{} 无对应的国际sku，请先督促跨境采销完成跨境商品二发。[跳过处理]", jdSkuId);
                continue;
            }

            BrSkuTaxPO skuTax = brSkuTaxAtomicService.getOneByJdSkuId(jdSkuId);

            if (skuTax == null) {
                log.warn("找不到 skuTax 数据, jdSkuId={}。[跳过处理]", jdSkuId);
                continue;
            }

            try {
                skuWriteManageService.saveProductGlobalNcmAttribute(new SkuSyncNcmDTO(jdSkuId, skuTax.getNcmCode(), StringUtils.isEmpty(updater) ? Constant.PIN_SYSTEM : updater));
                count ++ ;
            } catch (Exception e) {
                log.error("保存sku的ncm属性失败, skus={}", JSONObject.toJSONString(skus), e);
                return DataResponse.error("更新sku的ncm属性失败!");
            }
        }

        return DataResponse.success(count);
    }

    @Override
    public DataResponse<Integer> batchFixBrNcmCode(List<Long> jdSkuIds, String updater) {
        return this.batchFixBrNcmCode(jdSkuIds, true, updater);
    }

    /**
     * 从税率表中获取所有
     */
    private List<Long> getJdSkuIdsFromTaxRate() {

        int index = 0;

        Set<Long> result = Sets.newHashSet();

        while (index < 1000) {
            index ++;

            Set<Long> jdSkuIds = brSkuTaxAtomicService.getJdSkuIds(index, 100);

            if (CollectionUtils.isNotEmpty(jdSkuIds)) {
                result.addAll(jdSkuIds);
            }

            if (CollectionUtils.isEmpty(jdSkuIds)) {
                break;
            }
        }

        return result.stream().sorted().collect(Collectors.toList());
    }

}
