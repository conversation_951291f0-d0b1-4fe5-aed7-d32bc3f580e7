package com.jdi.isc.product.soa.service.atomic.spu;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuTaxAuditStatusEnum;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.spu.biz.SpuQueryReqVO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.repository.mapper.spu.SpuBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【jdi_isc_spu_sharding(spu基础表)】的数据库操作Service实现
 * @createDate 2023-11-25 15:39:30
 */
@Slf4j
@Service
public class SpuAtomicService extends ServiceImpl<SpuBaseMapper, SpuPO> {

    /**
     * 查询spu信息
     *
     * @param spuId
     * @return
     */
    public SpuPO getSpuPoBySpuId(Long spuId) {
        LambdaQueryWrapper<SpuPO> lambdaQueryWrapper = new LambdaQueryWrapper<SpuPO>().eq(SpuPO::getSpuId, spuId).eq(SpuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectOne(lambdaQueryWrapper);
    }


    /**
     * 查询当前spuId上架个数
     *
     * @param spuId
     * @return
     */
    public long onSaleCount(Long spuId) {
        return super.getBaseMapper().selectCount(new LambdaQueryWrapper<SpuPO>().eq(SpuPO::getSpuId, spuId).eq(SpuPO::getSpuStatus, SpuStatusEnum.ON_SALE.getStatus()).eq(SpuPO::getYn, YnEnum.YES.getCode()));
    }

    /**
     * 更新spu状态
     *
     * @param spuId     spuId
     * @param spuStatus spuStatus
     * @return
     */
    public boolean updateSpuStatus(Long spuId, SpuStatusEnum spuStatus) {
        SpuPO spuPo = new SpuPO();
        spuPo.setSpuStatus(spuStatus.getStatus());
        spuPo.setSpuId(spuId);
        return BooleanUtils.toBoolean(super.getBaseMapper().updateById(spuPo));
    }


    /**
     * 获取spuId和国际编码
     *
     * @param spuIds spuId数组
     * @return 返回spu信息
     */
    public List<SpuPO> getSpuCountryCodeByIds(List<Long> spuIds) {
        return super.getBaseMapper().selectList(new LambdaQueryWrapper<SpuPO>().select(SpuPO::getSpuId, SpuPO::getSourceCountryCode,SpuPO::getVendorCode).in(SpuPO::getSpuId, spuIds).eq(SpuPO::getYn, YnEnum.YES.getCode()));
    }

    public List<SpuPO> listBySpuIds(List<Long> spuIds) {
        if (CollectionUtils.isEmpty(spuIds)) {
            return Lists.newArrayList();
        }

        List<SpuPO> result = Lists.newArrayList();

        for (List<Long> ids : Lists.partition(spuIds, 100)) {
            LambdaQueryWrapper<SpuPO> query = new LambdaQueryWrapper<SpuPO>()
                    .select()
                    .in(SpuPO::getSpuId, ids)
                    .eq(SpuPO::getYn, YnEnum.YES.getCode());
            List<SpuPO> list = super.getBaseMapper().selectList(query);
            if (CollectionUtils.isNotEmpty(list)) {
                result.addAll(list);
            }
        }

        return result;
    }


    public Map<Long,SpuPO> getSpuStatusBySpuIds(Set<Long> spuIds){
        if (CollectionUtils.isEmpty(spuIds)) {
            return Collections.emptyMap();
        }
        List<SpuPO> spuPOList = super.getBaseMapper().selectList(new LambdaQueryWrapper<SpuPO>()
                .select(SpuPO::getSpuId, SpuPO::getSpuStatus,SpuPO::getAuditStatus, SpuPO::getTaxAuditStatus, SpuPO::getAttributeScope)
                .in(SpuPO::getSpuId, spuIds).eq(SpuPO::getYn, YnEnum.YES.getCode()));

        return Optional.ofNullable(spuPOList).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(SpuPO::getSpuId, Function.identity()));
    }

    /**
     * 列表查询
     *
     * @param reqVO
     * @return
     */
    public List<SpuPO> listSearch(SpuQueryReqVO reqVO) {
        // 计算偏移量
        reqVO.setOffset((reqVO.getIndex() - 1) * reqVO.getSize());
        return super.getBaseMapper().listSearch(reqVO);
    }

    /**
     * 查询搜索页spu总数
     *
     * @param reqVO
     * @return
     */
    public long getTotal(SpuQueryReqVO reqVO) {
        return super.getBaseMapper().getTotal(reqVO);
    }


    /**
     * 更新spu状态
     *
     * @param spuId     spuId
     * @param auditStatusEnum auditStatusEnum
     * @return
     */
    public boolean updateSpuAuditStatus(Long spuId, SpuAuditStatusEnum auditStatusEnum) {
        SpuPO spuPo = new SpuPO();
        spuPo.setAuditStatus(auditStatusEnum.getCode());
        spuPo.setSpuId(spuId);
        if(LoginContextHolder.getLoginContextHolder()!= null && StringUtils.isNotBlank(LoginContextHolder.getLoginContextHolder().getPin())){
            spuPo.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
        }else {
            spuPo.setUpdater(Constant.SYSTEM);
        }
        spuPo.setUpdateTime(new Date());
        return BooleanUtils.toBoolean(super.getBaseMapper().updateById(spuPo));
    }

    /**
     * 更新spuTax状态
     *
     * @param spuId     spuId
     * @param taxAuditStatusEnum taxAuditStatusEnum
     * @return
     */
    public boolean updateSpuTaxAuditStatus(Long spuId, SpuTaxAuditStatusEnum taxAuditStatusEnum) {
        SpuPO spuPo = new SpuPO();
        spuPo.setTaxAuditStatus(taxAuditStatusEnum.getCode());
        spuPo.setSpuId(spuId);
        if(LoginContextHolder.getLoginContextHolder()!= null && StringUtils.isNotBlank(LoginContextHolder.getLoginContextHolder().getPin())){
            spuPo.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
        }else {
            spuPo.setUpdater(Constant.SYSTEM);
        }
        spuPo.setUpdateTime(new Date());
        return BooleanUtils.toBoolean(super.getBaseMapper().updateById(spuPo));
    }

    /**
     * 根据供应商和商品集合查询商品
     *
     * @param spuIds     商品集合
     * @param vendorCode 供应商简码
     * @return 商品集合
     */
    public List<SpuPO> querySpuList(Collection<Long> spuIds, String vendorCode) {
        return super.getBaseMapper().selectList(new LambdaQueryWrapper<SpuPO>().select(SpuPO::getSpuId,SpuPO::getSpuStatus).in(SpuPO::getSpuId, spuIds)
                .eq(StringUtils.isNotBlank(vendorCode),SpuPO::getVendorCode, vendorCode).eq(SpuPO::getYn, YnEnum.YES.getCode()));
    }

    /**
     * 按照spuIds查询到spuDO数据
     * @param spuIds
     * @return
     */
    public Map<Long,SpuPO> querySpuPOMapBySpuIds(List<Long> spuIds){
        List<SpuPO> spuPOs = this.querySpuList(spuIds);
        return Optional.ofNullable(spuPOs).orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(SpuPO::getSpuId,v -> v,(value1, value2) -> value1));
    }
    @PFTracing
    public List<SpuPO> querySpuList(Collection<Long> spuIds){
        return super.getBaseMapper().selectList(new LambdaQueryWrapper<SpuPO>().in(SpuPO::getSpuId, spuIds).eq(SpuPO::getYn, YnEnum.YES.getCode()));
    }

    /**
     * 根据spuIds获取SpuPO的映射关系
     * @param spuIds spuId的集合
     * @return spuId到SpuPO的映射关系，若spuIds为空或无对应数据则返回null
     */
    @PFTracing
    public Map<Long, SpuPO> getSpuMap(Set<Long> spuIds) {
        if (CollectionUtils.isNotEmpty(spuIds)) {
            List<SpuPO> listSpu = super.getBaseMapper().selectList(new LambdaQueryWrapper<SpuPO>()
                .in(SpuPO::getSpuId, spuIds)
                .eq(SpuPO::getYn, YnEnum.YES.getCode()));
            if (CollectionUtils.isNotEmpty(listSpu)) {
                return listSpu.stream().collect(Collectors.toMap(SpuPO::getSpuId, Function.identity()));
            }
        }
        return new HashMap<>();
    }

    /**
     * 分页获取全部SpuPO数据（仅获取有效数据）
     *
     * @param index 页码，从1开始
     * @param size 每页大小
     * @return SpuPO列表
     */
    public List<SpuPO> listAllSpuWithPage(String updateTime,Long index, Long size) {
        // 参数校验
        if (index == null || index < 1) {
            throw new IllegalArgumentException("页码必须大于等于1");
        }
        if (size == null || size < 1 || size > 1000) {
            throw new IllegalArgumentException("页大小必须在1-1000之间");
        }

        // 计算偏移量
        Long offset = (index - 1) * size;

        return super.getBaseMapper().listAllSpuWithPage(updateTime,offset, size);
    }

    /**
     * 获取全部有效SpuPO数据的总数
     *
     * @return 总记录数
     */
    public long listAllSpuTotal(String updateTime) {
        return super.getBaseMapper().listAllSpuTotal(updateTime);
    }

    public List<SpuPO> listBySourceCountryCodeAndJdCatId(String sourceCountryCode, Long jdCatId) {
        if (StringUtils.isEmpty(sourceCountryCode) || jdCatId == null) {
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<SpuPO> wrapper = Wrappers.<SpuPO>lambdaQuery()
                .select(SpuPO::getSpuId, SpuPO::getBuyer, SpuPO::getUpdateTime)
                .eq(SpuPO::getSourceCountryCode, sourceCountryCode)
                .eq(SpuPO::getJdCatId, jdCatId)
                .eq(SpuPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    public void updateBuyer(List<SpuPO> spus, String buyer, String updater) {
        if (CollectionUtils.isEmpty(spus)) {
            return;
        }
        // 更新
        for (SpuPO spu : spus) {
            log.info("更新spu的采销信息. spuId={}, buyer={}, updater={}", spu.getSpuId(), buyer, updater);

            if (StringUtils.equals(spu.getBuyer(), buyer)) {
                log.info("更新spu的采销信息. buyer 相同无需更新, spuId={}, buyer={}, updater={}", spu.getSpuId(), buyer, updater);
                continue;
            }

            LambdaUpdateWrapper<SpuPO> updateWrapper = Wrappers.lambdaUpdate(SpuPO.class)
                    .set(SpuPO::getBuyer, buyer)
                    .set(SpuPO::getUpdateTime, new Date())
                    .set(SpuPO::getUpdater, StringUtils.isNotBlank(updater) ? updater : Constant.SYSTEM)
                    .eq(SpuPO::getSpuId, spu.getSpuId())
                    .eq(SpuPO::getYn, YnEnum.YES.getCode())
                    .eq(SpuPO::getUpdateTime, spu.getUpdateTime());
            boolean update = super.update(updateWrapper);

            log.info("更新spu的采销信息. result=[{}] spuId={}, buyer={}, updater={}", update, spu.getSpuId(), buyer, updater);

            if (!update) {
                log.info("更新spu的采销信息失败. result=[{}] spuId={}, buyer={}, updater={}", false, spu.getSpuId(), buyer, updater);
                throw new ProductBizException("spu正在进行其他业务操作，请稍后重试 %s", spu.getSpuId());
            }
        }
    }

    /**
     * 检查指定的供应商SPU ID是否存在。
     * @param supplierSpuId 供应商SPU ID
     * @param supplierCode 供应商简码
     * @return 如果供应商SPU ID存在，返回true；否则返回false。
     */
    public boolean existSupplierSpuId(String supplierSpuId,String supplierCode,Long spuId) {
        LambdaQueryWrapper<SpuPO> queryWrapper = Wrappers.lambdaQuery(SpuPO.class)
                .eq(SpuPO::getVendorSpuId, supplierSpuId)
                .eq(SpuPO::getVendorCode, supplierCode)
                .ne(Objects.nonNull(spuId),SpuPO::getSpuId,spuId)
                .eq(SpuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().exists(queryWrapper);
    }

}




