package com.jdi.isc.product.soa.service.atomic.supplier;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.enums.supplier.SupplierStatusEnum;
import com.jdi.isc.product.soa.domain.supplier.po.SupplierBaseInfoPO;
import com.jdi.isc.product.soa.repository.mapper.supplier.SupplierBaseInfoBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 供应商基础信息原子服务
 * @Author: zhaojianguo21
 * @Date: 2024/03/20 21:01
 **/
@Slf4j
@Service
public class SupplierBaseInfoAtomicService extends ServiceImpl<SupplierBaseInfoBaseMapper, SupplierBaseInfoPO> {

    /**
     * 根据供应商简码获取基本信息
     * @param supplierCode 简码
     * @return 对象
     */
    public SupplierBaseInfoPO getByCode(String supplierCode){
        LambdaQueryWrapper<SupplierBaseInfoPO> wrapper = Wrappers.<SupplierBaseInfoPO>lambdaQuery()
                .eq(SupplierBaseInfoPO::getSupplierCode, supplierCode)
                .eq(SupplierBaseInfoPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    /**
     * 获取未删除的对象
     * @param id id
     * @return 对象
     */
    public SupplierBaseInfoPO getValidById(Long id){
        LambdaQueryWrapper<SupplierBaseInfoPO> wrapper = Wrappers.<SupplierBaseInfoPO>lambdaQuery()
                .eq(SupplierBaseInfoPO::getId, id)
                .eq(SupplierBaseInfoPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }


    /**
     * 根据供应商名称获取供应商基本信息
     * @param supplyName 供应商名称
     * @return SupplierBaseInfoPO 供应商基本信息
     */
    public SupplierBaseInfoPO getByName(String supplyName){
        LambdaQueryWrapper<SupplierBaseInfoPO> wrapper = Wrappers.<SupplierBaseInfoPO>lambdaQuery()
                .eq(SupplierBaseInfoPO::getBusinessLicenseName, supplyName)
                .eq(SupplierBaseInfoPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }
    /**
     * 删除
     * @param ids
     * @return 对象
     */
    public boolean delByIds(Set<Long> ids){
        LambdaUpdateWrapper<SupplierBaseInfoPO> wrapper = Wrappers.<SupplierBaseInfoPO>lambdaUpdate()
                .set(SupplierBaseInfoPO::getYn, YnEnum.NO.getCode())
                .in(SupplierBaseInfoPO::getId, ids);
        return super.update(wrapper);
    }

    /**
     * 更新商家审核状态
     * @param supplierCode
     * @param statusEnum
     * @return
     */
    public boolean updateStatus(String supplierCode, SupplierStatusEnum statusEnum, String updater, long updateTime){
        LambdaUpdateWrapper<SupplierBaseInfoPO> wrapper = Wrappers.<SupplierBaseInfoPO>lambdaUpdate()
                .set(SupplierBaseInfoPO::getSupplierStatus, statusEnum.getCode())
                .set(SupplierBaseInfoPO::getUpdater, updater)
                .set(SupplierBaseInfoPO::getUpdateTime, updateTime)
                .eq(SupplierBaseInfoPO::getSupplierCode, supplierCode);
        return super.update(wrapper);
    }

    /**
     * 批量获取供应商信息
     * @param supplierCodeSet
     * @return
     */
    public Map<String, SupplierBaseInfoPO> batchQueryByCode(Set<String> supplierCodeSet){
        LambdaQueryWrapper<SupplierBaseInfoPO> wrapper = Wrappers.<SupplierBaseInfoPO>lambdaQuery()
                .in(SupplierBaseInfoPO::getSupplierCode, supplierCodeSet)
                .eq(SupplierBaseInfoPO::getYn, YnEnum.YES.getCode());
        List<SupplierBaseInfoPO> supplierBaseInfoPOList = super.getBaseMapper().selectList(wrapper);
        if(CollectionUtils.isEmpty(supplierBaseInfoPOList)){
            return Maps.newHashMap();
        }
        return supplierBaseInfoPOList.stream().collect(Collectors.toMap(SupplierBaseInfoPO::getSupplierCode, Function.identity()));
    }

    /**
     * 批量获取供应商信息
     * @return
     */
    public List<SupplierBaseInfoPO> batchQueryBySourceCountryCode(String sourceCountryCode){
        LambdaQueryWrapper<SupplierBaseInfoPO> wrapper = Wrappers.<SupplierBaseInfoPO>lambdaQuery()
                .in(SupplierBaseInfoPO::getBusinessLicenseCountry, sourceCountryCode)
                .in(SupplierBaseInfoPO::getSupplierStatus, SupplierStatusEnum.ACTIVATE.getCode())
                .eq(SupplierBaseInfoPO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }



    /**
     * 批量获取供应商信息
     * @param supplierCodeSet
     * @return
     */
    public List<SupplierBaseInfoPO> listQueryByCode(Set<String> supplierCodeSet){
        LambdaQueryWrapper<SupplierBaseInfoPO> wrapper = Wrappers.<SupplierBaseInfoPO>lambdaQuery()
                .in(SupplierBaseInfoPO::getSupplierCode, supplierCodeSet)
                .eq(SupplierBaseInfoPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(wrapper);
    }


    /**
     * 根据供应商代码集合查询供应商国家类型映射关系。
     * @param supplierCodes 供应商代码集合。
     * @return 供应商代码到国家类型的映射关系。
     */
    public Map<String,Integer> queryVendorCountryTypeMap(Set<String> supplierCodes){
        LambdaQueryWrapper<SupplierBaseInfoPO> wrapper = Wrappers.<SupplierBaseInfoPO>lambdaQuery()
                .select(SupplierBaseInfoPO::getSupplierCode,SupplierBaseInfoPO::getCountryType)
                .in(SupplierBaseInfoPO::getSupplierCode, supplierCodes)
                .eq(SupplierBaseInfoPO::getYn, YnEnum.YES.getCode());
        List<SupplierBaseInfoPO> supplierBaseInfoPOList = super.getBaseMapper().selectList(wrapper);

        return Optional.ofNullable(supplierBaseInfoPOList).orElseGet(ArrayList::new).stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(SupplierBaseInfoPO::getSupplierCode, SupplierBaseInfoPO::getCountryType));
    }


    /**
     * 批量获取供应商信息
     * @param supplierCodeSet
     * @return
     */
    public List<SupplierBaseInfoPO> listBySupplierCode(Set<String> supplierCodeSet){
        LambdaQueryWrapper<SupplierBaseInfoPO> wrapper = Wrappers.<SupplierBaseInfoPO>lambdaQuery()
                .in(SupplierBaseInfoPO::getSupplierCode, supplierCodeSet)
                .eq(SupplierBaseInfoPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(wrapper);
    }

    /**
     * 根据供应商编码获取供应商基本信息
     * @param supplierCode 供应商编码
     * @return 供应商基本信息PO对象
     */
    public SupplierBaseInfoPO getSupplierBaseInfoPOBySupplierCode(String supplierCode){
        LambdaQueryWrapper<SupplierBaseInfoPO> wrapper = Wrappers.<SupplierBaseInfoPO>lambdaQuery()
                .eq(SupplierBaseInfoPO::getSupplierCode, supplierCode)
                .eq(SupplierBaseInfoPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }
}
