package com.jdi.isc.product.soa.service.manage.attribute;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.attribute.common.AttributeDTO;
import com.jdi.isc.product.soa.api.attribute.res.AttributeFlatDTO;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeFlatVO;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;

import java.util.List;
import java.util.Set;

/**
 * 属性对外服务
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2023/11/27 16:42
 **/
public interface AttributeOutService {

    /**
     * 根据语言查询商品销售属性。
     * @param lang 语言代码
     * @return 包含商品销售属性的 AttributeFlatVO 列表
     */
    List<AttributeFlatVO> querySellAttr(String lang);

    /**
     * 查询类目销售属性
     * @param categoryId 类目ID
     * @param lang 语种
     * @return 类目属性集合
     */
    List<AttributeFlatVO> querySellAttrDetail(Long categoryId, String lang);

    /**
     * 查询类目销售属性
     * @param categoryId 类目ID
     * @param lang 语种
     * @return 类目属性集合
     */
    List<AttributeFlatVO> querySellAttrWithDefaultValue(Long categoryId, String lang);

    /**
     * 查询类目销售属性（仅包含属性id和属性名称，来自中台，不包含属性值信息）
     * 新版销售属性查询接口，替代 querySellAttrDetail
     * @param categoryId 类目ID
     * @param lang 语种
     * @return 类目属性集合
     */
    List<AttributeFlatVO> querySellAttrWithoutValue(Long categoryId, String lang);

    /**
     * 查询类目销售属性ID集合
     * @param categoryId 类目ID
     * @return 类目属性ID集合
     */
    Set<Long> querySellAttrIds(Long categoryId);

    /**
     * 查询类目扩展属性
     * @param categoryId 类目ID
     * @param lang 语种
     * @return 类目属性集合
     */
    List<AttributeFlatVO> queryExtAttrDetail(Long categoryId, String lang);

    /**
     * 根据属性ID集合和语言列表查询对应的属性VO列表。
     * @param attributeIds 属性ID集合
     * @param langList 语言列表
     * @return 属性VO列表
     */
//    List<AttributeVO> queryAttributeVoByIds(Set<Long> attributeIds, List<String> langList);

    /**
     * 根据类别ID和语言集合查询扩展属性详细信息。
     * @param categoryId 类别ID
     * @param langSet 语言集合
     * @return 扩展属性详细信息列表
     */
    List<AttributeVO> queryExtAttrDetail(Long categoryId, Set<String> langSet);

    /**
     * 根据类目id和京东skuid查询扩展属性，并对中台已经添加的扩展属性对类目的对应扩展属性赋值（selected）
     * @param catId 京东类目ID
     * @param jdSkuId 京东SKU ID
     * @param lang 语言标识
     * @return 包含京东SKU扩展属性的扁平化DTO列表
     */
    List<PropertyVO> obtainJDSkuExtAttributeList(Long catId, Long jdSkuId, String lang);

    /**
     * 查询类目销售属性（由原来读取数据库本地信息，改为从中台获取销售属性名称信息，为适配，同时也会返回默认属性值及默认属性值ID）
     *
     * 新版销售属性查询接口，替代 querySellAttrDetail
     * @param categoryId 类目ID
     * @param lang 语种
     * @return 类目属性集合
     */
    List<PropertyVO> querySellAttrByCatId(Long categoryId, String lang);

    /**
     * 根据京东skuid查询主站sku已存储扩展属性, jdCatId为当前sku的主站末级类目id
     * @param jdCatId 京东类目ID
     * @param jdSkuId 京东SKU ID
     * @return 包含京东SKU扩展属性的扁平化DTO列表
     */
    List<PropertyVO> obtainJDSkuStoreExtAttributeList(Long jdCatId,Long jdSkuId);
}
