package com.jdi.isc.product.soa.service.manage.price.extendPrice.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jd.fastjson.JSON;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.CostPriceTypeEnums;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.CountryExtendPriceEnum;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.domain.category.biz.CategoryIdVO;
import com.jdi.isc.product.soa.domain.ducc.PriceConfig;
import com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum;
import com.jdi.isc.product.soa.domain.enums.product.FulfillmentRateTypeEnum;
import com.jdi.isc.product.soa.domain.mku.po.MkuLangPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.InitAgreementPriceVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.extendPrice.biz.*;
import com.jdi.isc.product.soa.domain.price.extendPrice.po.CountryExtendPricePO;
import com.jdi.isc.product.soa.domain.price.markupRate.biz.MarkupRateReqVO;
import com.jdi.isc.product.soa.domain.price.markupRate.biz.MarkupRateVO;
import com.jdi.isc.product.soa.domain.price.po.PriceLogPO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuLogPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum;
import com.jdi.isc.product.soa.service.adapter.mapstruct.extendPrice.CountryExtendPriceConvert;
import com.jdi.isc.product.soa.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.PriceLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.extendPrice.CountryExtendPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuLogAtomicService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.mku.MkuRelationManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.cost.AbstractCostPriceService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.cost.CostPriceService;
import com.jdi.isc.product.soa.service.manage.price.extendPrice.CountryExtendPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.markupRate.MarkupRateManageService;
import com.jdi.isc.product.soa.service.support.ProductIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 国家扩展（参考）价数据维护服务实现
 * @Author: wangpeng965
 * @Date: 2025/03/04 16:28
 **/

@Slf4j
@Service
public class CountryExtendPriceManageServiceImpl extends BaseManageSupportService<CountryExtendPriceVO, CountryExtendPricePO> implements CountryExtendPriceManageService {

    @Resource
    private CountryExtendPriceAtomicService countryExtendPriceAtomicService;

    @Resource
    private MkuRelationManageService mkuRelationManageService;

    @Resource
    private CategoryOutService categoryOutService;

    @Resource
    private MkuLangAtomicService mkuLangAtomicService;

    @Resource
    private SkuLogAtomicService skuLogAtomicService;

    @Resource
    private ProductIdGenerator productIdGenerator;

    @Resource
    private SkuPriceAtomicService skuPriceAtomicService;

    @Resource
    private OperDuccConfig operDuccConfig;

    @Resource
    private MarkupRateManageService markupRateManageService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private PriceLogAtomicService priceLogAtomicService;

    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;

    @Resource
    protected Map<String, AbstractCostPriceService> costPriceServiceMap;

    @Override
    public PageInfo<CountryExtendPricePageVO> pageSearch(CountryExtendPricePageReqVO vo) {
        PageInfo<CountryExtendPricePageVO> pageInfo = new PageInfo<>();
        pageInfo.setSize(vo.getSize());
        pageInfo.setIndex(vo.getIndex());
        this.buildQuery(vo);
        long total = countryExtendPriceAtomicService.pageSearchTotal(vo);
        if (total == 0) {
            pageInfo.setTotal(0);
            return pageInfo;
        }
        List<CountryExtendPricePageVO> countryExtendPriceList = countryExtendPriceAtomicService.pageSearch(vo);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(countryExtendPriceList)){
            return pageInfo;
        }
        this.setMkuTitle(countryExtendPriceList);
        this.setCatIdAndName(countryExtendPriceList);
        this.setSkuId(countryExtendPriceList);
        pageInfo.setTotal(total);
        pageInfo.setRecords(countryExtendPriceList);
        return pageInfo;
    }



    private void buildQuery(CountryExtendPricePageReqVO vo){
        Set<Long> mkuIdSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(vo.getMkuIds())){
            mkuIdSet.addAll(vo.getMkuIds());
        }
        if(CollectionUtils.isNotEmpty(vo.getSkuIds())){
            Map<Long, Long> skuMkuMap = mkuRelationManageService.queryMkuIdBySkuIds(new HashSet<>(vo.getSkuIds()));
            if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(mkuIdSet)){
                mkuIdSet.retainAll(skuMkuMap.values());
                if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(mkuIdSet)){
                    mkuIdSet.add(-1L);
                }
            }else {
                if(MapUtils.isEmpty(skuMkuMap)){
                    mkuIdSet.retainAll(skuMkuMap.values());
                }else {
                    mkuIdSet.addAll(skuMkuMap.values());
                }
            }
        }
        // 类目id转换为终极类目id
        Set<Long> lastCatIds = this.getLastCatIds(vo);
        vo.setCatIds(lastCatIds);
        vo.setMkuIds(new ArrayList<>(mkuIdSet));
    }

    /**
     * 根据输入的类目信息，查询并返回最后一级类目的ID集合。
     * @param countryExtendPricePageReqVO 包含所有类目信息的对象。
     */
    private Set<Long> getLastCatIds(CountryExtendPricePageReqVO countryExtendPricePageReqVO){
        CategoryIdVO categoryIdVO = new CategoryIdVO();
        categoryIdVO.setFirstCatId(countryExtendPricePageReqVO.getFirstCatId());
        categoryIdVO.setSecondCatId(countryExtendPricePageReqVO.getSecondCatId());
        categoryIdVO.setThirdCatId(countryExtendPricePageReqVO.getThirdCatId());
        categoryIdVO.setLastCatId(countryExtendPricePageReqVO.getLastCatId());
        Set<Long> catIdSet = categoryOutService.queryLastCatId(categoryIdVO);
        return catIdSet;
    }

    /**
     * 设置字幕页的MKU标题（中文）
     * @param countryExtendPriceVOList 字幕页列表
     */
    private void setMkuTitle(List<CountryExtendPricePageVO> countryExtendPriceVOList){
        if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(countryExtendPriceVOList)){
            return;
        }
        List<Long> mkuIds = countryExtendPriceVOList.stream().map(CountryExtendPricePageVO::getMkuId).collect(Collectors.toList());
        Map<Long, MkuLangPO> mkuLangMap = new HashMap<>();
        if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(mkuIds)){
            mkuLangMap = mkuLangAtomicService.getMkuLangNameByMkuIds(mkuIds, LangConstant.LANG_ZH);
        }
        for (CountryExtendPricePageVO vo : countryExtendPriceVOList){
            MkuLangPO mkuLangPOzh = mkuLangMap.get(vo.getMkuId());
            if(Objects.nonNull(mkuLangPOzh)){
                vo.setMkuTitle(mkuLangPOzh.getMkuTitle());
            }
        }
    }

    /**
     * 设置短标题的分类 ID 和名称。
     * @param countryExtendPriceVOList 字幕页列表
     */
    private void setCatIdAndName(List<CountryExtendPricePageVO> countryExtendPriceVOList){
        if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(countryExtendPriceVOList)){
            return;
        }
        Set<Long> lastCatIds = countryExtendPriceVOList.stream().map(CountryExtendPricePageVO::getLastCatId).collect(Collectors.toSet());
        Map<Long, String> lastCatMap = new HashMap<>();
        if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(lastCatIds)){
            lastCatMap.putAll(categoryOutService.queryPathStr(lastCatIds, LangContextHolder.get()));
        }
        countryExtendPriceVOList.forEach(item->{
            item.setCatName(lastCatMap.get(item.getLastCatId()));
        });
    }

    /**
     * 设置子标题页面的 SKU ID 列表。
     * @param countryExtendPriceVOList 子标题页面列表。
     */
    private void setSkuId(List<CountryExtendPricePageVO> countryExtendPriceVOList){
        if(CollectionUtils.isEmpty(countryExtendPriceVOList)){
            return;
        }
        List<Long> mkuList = countryExtendPriceVOList.stream().map(CountryExtendPricePageVO::getMkuId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, List<MkuRelationPO>> mkuIdPoListMap = mkuRelationManageService.queryMkuBindSkuListMap(mkuList);
        for (CountryExtendPricePageVO countryExtendPricePageVO : countryExtendPriceVOList){
            if(mkuIdPoListMap.containsKey(countryExtendPricePageVO.getMkuId())){
                List<MkuRelationPO> mkuRelationPOS = mkuIdPoListMap.get(countryExtendPricePageVO.getMkuId());
                List<Long> skuIdList = mkuRelationPOS.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toList());
                countryExtendPricePageVO.setSkuIds(skuIdList);
            }
        }
    }

    /**
     * 保存或更新国家扩展价格信息，并记录价格日志。
     * @param countryExtendPriceVO 国家扩展价格信息的VO对象
     * @return 更新结果，true表示更新成功，false表示更新失败
     */
    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> saveOrUpdate(CountryExtendPriceVO countryExtendPriceVO)  {
        log.info("CountryExtendPriceManageServiceImpl.saveOrUpdate, input={}", JSONObject.toJSONString(countryExtendPriceVO));
        Long id = countryExtendPriceVO.getId();
        String bizNo = "";
        CountryExtendPricePO saveOrUpdatePo = null;
        if(Objects.nonNull(id)){
            CountryExtendPricePO existingRecord = countryExtendPriceAtomicService.getById(id);
            if (Objects.isNull(existingRecord)) {
                log.warn("CountryExtendPriceManageServiceImpl.saveOrUpdate fail not query id:{}", id);
                return DataResponse.error("更新失败: 记录不存在");
            }
            bizNo = existingRecord.getBizNo();
            countryExtendPriceVO.setBizNo(null);
            saveOrUpdatePo = CountryExtendPriceConvert.INSTANCE.vo2Po(countryExtendPriceVO);
        }else {
            bizNo = countryExtendPriceVO.getTargetCountryCode() + countryExtendPriceVO.getSkuId() + "-" + countryExtendPriceVO.getBusinessType();
            countryExtendPriceVO.setBizNo(bizNo);
            saveOrUpdatePo = CountryExtendPriceConvert.INSTANCE.vo2Po(countryExtendPriceVO);
        }
        boolean updateResult = countryExtendPriceAtomicService.saveOrUpdate(saveOrUpdatePo);
        if (!updateResult) {
            log.warn("CountryExtendPriceManageServiceImpl.saveOrUpdate,fail countryExtendPricePO: {}", JSONObject.toJSONString(saveOrUpdatePo));
            return DataResponse.error("更新记录失败");
        }
        PriceLogPO priceLogPO = getPriceLogPO(saveOrUpdatePo);
        priceLogAtomicService.save(priceLogPO);
        sendLog(bizNo, countryExtendPriceVO, saveOrUpdatePo);
        return DataResponse.success(true);
    }

    private PriceLogPO getPriceLogPO(CountryExtendPricePO param){
        PriceLogPO priceLogPO = new PriceLogPO();
        priceLogPO.setBizId(param.getId().toString());
        priceLogPO.setBizType(PriceTypeEnum.COUNTRY_EXTEND_PRICE_DRAFT.getCode());
        priceLogPO.setBizValue(param.getPrice());
        priceLogPO.setValue1(param.getSourceCountryCode());
        priceLogPO.setValue2(param.getTargetCountryCode());
        priceLogPO.setCreateTime(DateUtil.getCurrentTime());
        priceLogPO.setCreator(param.getCreator());
        priceLogPO.setUpdater(param.getUpdater());
        priceLogPO.setUpdateTime(DateUtil.getCurrentTime());
        return priceLogPO;
    }

    /**
     * 批量保存或更新国家扩展价格信息。
     * @param countryExtendPriceVOList 国家扩展价格信息列表。
     * @return 保存或更新后的国家扩展价格信息列表。
     */
    @Override
    public DataResponse<List<CountryExtendPriceVO>> batchSaveOrUpdate(List<CountryExtendPriceVO> countryExtendPriceVOList) {
        log.info("saveOrUpdate, input={}", JSONObject.toJSONString(countryExtendPriceVOList));
        // 参数业务校验
        for (CountryExtendPriceVO countryExtendPriceVO : countryExtendPriceVOList){
            DataResponse<Boolean> response = saveOrUpdate(countryExtendPriceVO);
            if(response.getSuccess()){
                countryExtendPriceVO.setValid(response.getSuccess());
            }
        }
        return DataResponse.success(countryExtendPriceVOList);
    }
    /**
     * 根据ID获取标记率详细信息
     * @param reqVO 标记率请求对象，包含要查询的ID
     * @return 标记率详细信息对象，如果ID对应的标记率不存在则返回null
     */
    @Override
    public CountryExtendPriceVO detail(CountryExtendPriceReqVO reqVO) {
        CountryExtendPricePO po = countryExtendPriceAtomicService.getById(reqVO.getId());
        if (Objects.isNull(po)){
            log.info("detail, countryExtendPricePO null. id={}", reqVO.getId());
            return null;
        }
        CountryExtendPriceVO vo = CountryExtendPriceConvert.INSTANCE.po2Vo(po);
        return vo;
    }


    @Override
    @PFTracing
    public  CountryExtendPriceVO getCountryExtendPrice(InitAgreementPriceVO vo,Integer businessType) {
        // 如果vo为null，直接返回null
        if (vo == null) {
            return null;
        }
        CountryExtendPriceReqVO reqVO = new CountryExtendPriceReqVO();
        reqVO.setSkuId(vo.getSkuId());
        reqVO.setSourceCountryCode(vo.getSourceCountryCode());
        reqVO.setTargetCountryCode(vo.getTargetCountryCode());
        reqVO.setBusinessType(businessType);
        CountryExtendPricePO countryExtendPrice = countryExtendPriceAtomicService.getPricePo(reqVO);
        if(Objects.nonNull(countryExtendPrice)) {
            CountryExtendPriceVO countryExtendPriceVO = CountryExtendPriceConvert.INSTANCE.po2Vo(countryExtendPrice);
            return countryExtendPriceVO;
        }
        return null;
    }


    /**
     * 发送日志到数据库。
     * @param mkuId MKU的唯一标识。
     * @param sourceData 日志的源数据。
     * @param targetData 日志的目标数据。
     */
    private void sendLog(String mkuId, CountryExtendPriceVO sourceData, CountryExtendPricePO targetData) {
        try {
            SkuLogPO skuLogPO = new SkuLogPO();
            skuLogPO.setSourceJson(JSONObject.toJSONString(sourceData));
            skuLogPO.setTargetJson(JSONObject.toJSONString(targetData));
            skuLogPO.setKeyType(KeyTypeEnum.MARKUP_RATE.getCode());
            skuLogPO.setKeyId(mkuId);
            skuLogPO.setCreator(targetData.getUpdater());
            skuLogPO.setUpdater(targetData.getUpdater());
            skuLogAtomicService.save(skuLogPO);
            log.info("日志保存成功，MKU: {}", mkuId);
        } catch (Exception e) {
            log.error("存储日志异常，MKU: {},sourceData:{},targetData:{} ,Error: {}", mkuId, JSONObject.toJSONString(sourceData), JSONObject.toJSONString(targetData), e.getMessage(), e);
        }
    }



    protected CostPriceService costPriceService(String sourceCountryCode) {
        return costPriceServiceMap.get(Objects.requireNonNull(CostPriceTypeEnums.getEnumByCountryCode(sourceCountryCode)).getServiceName());
    }

    /**
     * 添加或更新国家扩展价格信息。
     * @param vo InitAgreementPriceVO 对象，包含源国家代码、目标国家代码和 SKU ID。
     * @param currencyPriceVO CurrencyPriceVO 对象，包含价格和货币类型。
     * @param businessType 业务类型。
     * @return 保存或更新操作是否成功。
     */
    private Boolean addCountryExtendPrice(InitAgreementPriceVO vo, CurrencyPriceVO currencyPriceVO,Integer businessType,CountryExtendPriceVO countryExtendPrice) {
        SkuPO skuPo = skuAtomicService.getSkuPoBySkuId(vo.getSkuId());
        CountryExtendPriceVO countryExtendPriceVO = new CountryExtendPriceVO();
        countryExtendPriceVO.setSourceCountryCode(vo.getSourceCountryCode());
        countryExtendPriceVO.setTargetCountryCode(vo.getTargetCountryCode());
        countryExtendPriceVO.setSkuId(vo.getSkuId());
        countryExtendPriceVO.setBrandId(skuPo.getBrandId());
        countryExtendPriceVO.setLastCatId(skuPo.getJdCatId());
        countryExtendPriceVO.setPrice(currencyPriceVO.getPrice());
        countryExtendPriceVO.setPriceMark(currencyPriceVO.getMsg());
        countryExtendPriceVO.setCurrency(currencyPriceVO.getCurrency());
        countryExtendPriceVO.setBusinessType(businessType);
        countryExtendPriceVO.setCreator("System");
        countryExtendPriceVO.setUpdater("System");
        countryExtendPriceVO.setCreateTime(new Date().getTime());
        countryExtendPriceVO.setUpdateTime(new Date().getTime());
        if(Objects.nonNull(countryExtendPrice)){
            countryExtendPriceVO.setId(countryExtendPrice.getId());
            countryExtendPriceVO.setCreator(countryExtendPrice.getCreator());
            countryExtendPriceVO.setBizNo(countryExtendPrice.getBizNo());
        }
        DataResponse<Boolean> response = saveOrUpdate(countryExtendPriceVO);
        return response.getData();
    }

    @Override
    @PFTracing
    public Boolean addWarehousingPrice(InitAgreementPriceVO vo) {
        if(!CountryConstant.COUNTRY_ZH.equals(vo.getSourceCountryCode())){
            log.info("CountryExtendPriceManageServiceImpl.addWarehousingPrice 不是跨境品，无入仓成本价 vo:{}", JSON.toJSONString(vo));
            return true;
        }
        Boolean validateResult = validateSkuInfo(vo);
        if(!validateResult){
            return validateResult;
        }
        CountryExtendPriceVO countryExtendPrice = getCountryExtendPrice(vo,CountryExtendPriceEnum.WAREHOUSING_PRICE.getCode());
        vo.setIsWareHouseProduct(FulfillmentRateTypeEnum.WAREHOUSE_PRODUCT.getCode());
        CostPriceService costPriceService = costPriceService(vo.getSourceCountryCode());
        CurrencyPriceVO warehousingPrice = costPriceService.localizedCostPrice(vo);
        if(Objects.isNull(warehousingPrice) || Objects.isNull(warehousingPrice.getPrice())){
            log.info("addWarehousingPrice warehousingPrice is null vo:{},warehousingPrice:{}",JSON.toJSONString(vo),JSON.toJSONString(warehousingPrice));
            return false;
        }
        if(Objects.isNull(countryExtendPrice)) {
            // 设置国家跨境入仓价
            return addCountryExtendPrice(vo,warehousingPrice, CountryExtendPriceEnum.WAREHOUSING_PRICE.getCode(),null);
        }else {
            countryExtendPrice.setPrice(warehousingPrice.getPrice());
            countryExtendPrice.setPriceMark(warehousingPrice.getMsg());
            countryExtendPrice.setUpdateTime(new Date().getTime());
            DataResponse<Boolean> response = saveOrUpdate(countryExtendPrice);
            return response.getSuccess();
        }

    }

    @Override
    public Boolean addRefundTaxPrice(InitAgreementPriceVO vo) {
        if(!CountryConstant.COUNTRY_ZH.equals(vo.getSourceCountryCode())){
            log.info("CountryExtendPriceManageServiceImpl.addRefundTaxPrice 不是跨境品,无预估跨境退税金额 vo:{}", JSON.toJSONString(vo));
            return false;
        }
        Boolean validateResult = validateSkuInfo(vo);
        if(!validateResult){
            return validateResult;
        }
        CountryExtendPriceVO countryExtendPrice = getCountryExtendPrice(vo,CountryExtendPriceEnum.REFUND_TAX_PRICE.getCode());
        CurrencyPriceVO taxRefundPrice = getTaxRefundPrice(vo);
        if(Objects.isNull(countryExtendPrice)) {
            return addCountryExtendPrice(vo,taxRefundPrice, CountryExtendPriceEnum.REFUND_TAX_PRICE.getCode(),null);
        }else {
            countryExtendPrice.setPrice(taxRefundPrice.getPrice());
            countryExtendPrice.setPriceMark(taxRefundPrice.getMsg());
            countryExtendPrice.setUpdateTime(new Date().getTime());
            DataResponse<Boolean> response = saveOrUpdate(countryExtendPrice);
            return response.getSuccess();
        }
    }



    @Override
    public Boolean addJdPrice(InitAgreementPriceVO vo) {
        Boolean validateResult = validateSkuInfo(vo);
        if(!validateResult){
            return validateResult;
        }
        CountryAgreementPriceReqVO reqVO = new CountryAgreementPriceReqVO();
        reqVO.setSkuId(vo.getSkuId());
        reqVO.setSourceCountryCode(vo.getSourceCountryCode());
        reqVO.setTargetCountryCode(vo.getTargetCountryCode());
        CountryAgreementPricePO countryAgreementPrice = countryAgreementPriceAtomicService.getCountryAgreementPrice(reqVO);
        if(Objects.isNull(countryAgreementPrice) || Objects.isNull(countryAgreementPrice.getCountryCostPrice())){
            log.info("CountryExtendPriceManageServiceImpl.addJdPrice 国家成本价为空,vo:{},countryAgreementPrice:{}",JSON.toJSONString(vo),JSON.toJSONString(countryAgreementPrice));
            return false;
        }
        CountryExtendPriceVO countryExtendPrice = getCountryExtendPrice(vo,CountryExtendPriceEnum.JD_PRICE.getCode());
        CurrencyPriceVO countryCostPrice = new CurrencyPriceVO();
        countryCostPrice.setPrice(countryAgreementPrice.getCountryCostPrice());
        countryCostPrice.setMsg(countryAgreementPrice.getCostMark());
        countryCostPrice.setCurrency(countryAgreementPrice.getCurrency());
        countryCostPrice.setSuccess(true);
        CurrencyPriceVO jdPrice = getJdPrice(vo, countryCostPrice);
        if(!jdPrice.getSuccess() || Objects.isNull(jdPrice.getPrice())){
            log.info("CountryExtendPriceManageServiceImpl.addJdPrice jdPrice is null vo:{},jdPrice:{}",JSON.toJSONString(vo),JSON.toJSONString(jdPrice));
            return false;
        }
        return addCountryExtendPrice(vo, jdPrice, CountryExtendPriceEnum.JD_PRICE.getCode(),countryExtendPrice);
    }

    private Boolean validateSkuInfo(InitAgreementPriceVO vo){
        SkuPO skuPo = skuAtomicService.getSkuPoBySkuId(vo.getSkuId());
        if (skuPo == null) {
            log.info("skuPo is null vo:{}",JSON.toJSONString(vo));
            return false;
        }
        if(!Objects.equals(vo.getSourceCountryCode(),skuPo.getSourceCountryCode())){
            log.error("CountryAgreementPriceManageServiceImpl.validateSkuInfo fail sourceCountryCode error sku:{}",JSON.toJSONString(skuPo));
            return false;
        }
        if (!CountryConstant.COUNTRY_ZH.equals(vo.getSourceCountryCode())) {
            if(!Objects.equals(vo.getTargetCountryCode(),vo.getSourceCountryCode())){
                log.error("CountryAgreementPriceManageServiceImpl.validateSkuInfo fail targetCountryCode error sku:{}",JSONObject.toJSONString(vo));
                return false;
            }
        }
        if (CountryConstant.COUNTRY_ZH.equals(vo.getTargetCountryCode())) {
            if(!Objects.equals(vo.getSourceCountryCode(),CountryConstant.COUNTRY_ZH)){
                log.error("CountryAgreementPriceManageServiceImpl.validateSkuInfo fail sourceCountryCode error sku:{}",JSONObject.toJSONString(vo));
                return false;
            }
        }
        return true;
    }

    private CurrencyPriceVO getTaxRefundPrice(InitAgreementPriceVO vo){
        //查询
        SkuPricePO skuPriceInfo = skuPriceAtomicService.querySkuPrice(vo.getSkuId(),
            vo.getSourceCountryCode(),
            TradeDirectionEnum.SUPPLIER);
        //跨境品含税采购价，取采购价
        BigDecimal procurementPrice = skuPriceInfo.getPrice();
        PriceConfig config = operDuccConfig.priceConfig();
        BigDecimal refundDivisor = config.getRefundDivisor();
        BigDecimal exitDivisor =  new BigDecimal("0.06");
        // 针对越南和巴西的退税价格计算
        if (isVietnamOrBrazil(vo.getTargetCountryCode())) {
            return calculatePrecisePriceForVN(procurementPrice, exitDivisor, refundDivisor , skuPriceInfo.getCurrency());
        } else {
            // 针对其他国家的退税价格计算
            return calculateGeneralPrice(procurementPrice, exitDivisor, refundDivisor,skuPriceInfo.getCurrency());
        }
    }

    /**
     * 判断是否越南或巴西（特殊计算国家）
     */
    private boolean isVietnamOrBrazil(String countryCode) {
        return CountryConstant.COUNTRY_VN.equals(countryCode) || CountryConstant.COUNTRY_ID.equals(countryCode);
    }

    /**
     * 越南和巴西的精确价格计算（整数向上进位）
     */
    private CurrencyPriceVO calculatePrecisePriceForVN(BigDecimal procurementPrice, BigDecimal exitDivisor
        , BigDecimal refundDivisor,String currency) {
        BigDecimal taxRefundPrice = procurementPrice.multiply(exitDivisor).divide(refundDivisor, 0, RoundingMode.UP);

        CurrencyPriceVO currencyPriceVO = new CurrencyPriceVO();
        currencyPriceVO.setCurrency(currency);
        currencyPriceVO.setPrice(taxRefundPrice);
        String totalMsg = String.format("预估跨境退税金额%s=跨境品含税采购价%s/因子%s*退税率%s(参考值);\n"
            ,taxRefundPrice.stripTrailingZeros().toPlainString()
            ,procurementPrice.stripTrailingZeros().toPlainString()
            ,refundDivisor.stripTrailingZeros().toPlainString()
            ,exitDivisor.stripTrailingZeros().toPlainString());
        currencyPriceVO.setMsg(totalMsg);
        return currencyPriceVO;
    }

    /**
     * 其他国家的一般价格计算（保留2位小数，四舍五入）
     */
    private CurrencyPriceVO calculateGeneralPrice(BigDecimal procurementPrice, BigDecimal exitDivisor
        , BigDecimal refundDivisor,String currency) {
        BigDecimal taxRefundPrice = procurementPrice.multiply(refundDivisor).divide(exitDivisor, 2, RoundingMode.HALF_UP);
        CurrencyPriceVO currencyPriceVO = new CurrencyPriceVO();
        currencyPriceVO.setCurrency(currency);
        currencyPriceVO.setPrice(taxRefundPrice);
        String totalMsg = String.format("预估跨境退税金额%s=跨境品含税采购价%s/因子%s*退税率%s(参考值);\n",taxRefundPrice.stripTrailingZeros().toPlainString()
            ,procurementPrice.stripTrailingZeros().toPlainString()
            ,refundDivisor.stripTrailingZeros().toPlainString()
            ,exitDivisor.stripTrailingZeros().toPlainString());
        currencyPriceVO.setMsg(totalMsg);
        return currencyPriceVO;
    }

    private CurrencyPriceVO getJdPrice(InitAgreementPriceVO vo,CurrencyPriceVO countryCostPrice){
        CurrencyPriceVO jdCurrencyPrice = new CurrencyPriceVO();
        SkuPO skuPo = skuAtomicService.getSkuPoBySkuId(vo.getSkuId());
        MarkupRateReqVO markupRateReqVO = new MarkupRateReqVO();
        markupRateReqVO.setBrandId(skuPo.getBrandId());
        markupRateReqVO.setSkuId(skuPo.getSkuId());
        markupRateReqVO.setLastCatId(skuPo.getJdCatId());
        markupRateReqVO.setSourceCountryCode(vo.getSourceCountryCode());
        markupRateReqVO.setTargetCountryCode(vo.getTargetCountryCode());
        MarkupRateVO markupRate = markupRateManageService.getMarkupRate(markupRateReqVO);
        if(Objects.isNull(markupRate) || Objects.isNull(markupRate.getJdMarkupRate())){
            jdCurrencyPrice.setMsg("京东价率为空;");
            jdCurrencyPrice.setSuccess(Boolean.FALSE);
            return jdCurrencyPrice;
        }
        // 国家京东价 = 国家成本价 /（1 - 京东价率），后续手动更新
        BigDecimal costPrice = countryCostPrice.getPrice();
        // 京东价率
        BigDecimal jdRate = markupRate.getJdMarkupRate().divide(BigDecimal.valueOf(100));
        // 京东溢价率
        BigDecimal jdPremiumRate = BigDecimal.ONE.subtract(jdRate);
        BigDecimal jdPrice = costPrice.divide(jdPremiumRate, 2, RoundingMode.HALF_UP);
        jdCurrencyPrice.setPrice(jdPrice);
        jdCurrencyPrice.setCurrency(countryCostPrice.getCurrency());
        String totalMsg = String.format("国际京东价%s=国家成本价%s/[(1-京东价率%s)=%s];\n"
            ,jdPrice.stripTrailingZeros().toPlainString()
            ,costPrice.stripTrailingZeros().toPlainString()
            ,jdRate.stripTrailingZeros().toPlainString()
            ,jdPremiumRate.stripTrailingZeros().toPlainString());
        jdCurrencyPrice.setMsg(totalMsg + countryCostPrice.getMsg());
        return jdCurrencyPrice;
    }



    /**
     * 根据给定的 SKU ID 列表和目标国家代码，获取每个 SKU 在该国家的扩展价格信息。
     * @param skuId SKU ID 列表
     * @param targetCountryCode 目标国家代码
     * @return 每个 SKU 在目标国家的扩展价格信息，Map 的键为 SKU ID，值为该 SKU 在目标国家的扩展价格信息
     */
    @Override
    public Map<Long, Map<Integer, CountryExtendPricePO>> getCountryExtendPriceMap(List<Long> skuId,String targetCountryCode) {
        if(CollectionUtils.isEmpty(skuId)){
            Map<Long,Map<Integer, CountryExtendPricePO>> map = new HashMap<>();
            return map;
        }
        CountryExtendPriceReqVO reqVO = new CountryExtendPriceReqVO();
        reqVO.setSkuIds(skuId);
        reqVO.setTargetCountryCode(targetCountryCode);
        List<CountryExtendPricePO> pricePoList = countryExtendPriceAtomicService.getPricePoList(reqVO);
        Map<Long, Map<Integer, CountryExtendPricePO>> skuIdTypePriceMap = pricePoList.stream()
            .collect(Collectors.groupingBy(
                CountryExtendPricePO::getSkuId,
                Collectors.toMap(
                    CountryExtendPricePO::getBusinessType,
                    Function.identity(),
                    (existing, replacement) -> replacement
                )
            ));
        return skuIdTypePriceMap;
    }

    @Override
    public CurrencyPriceVO updateJdPrice(CountryAgreementPriceReqVO input) {
        log.info("CountryAgreementPriceDraftManageServiceImpl.updateJdPrice input:{}", JSON.toJSONString(input));
        InitAgreementPriceVO initAgreementPriceVO = new InitAgreementPriceVO();
        initAgreementPriceVO.setSkuId(input.getSkuId());
        initAgreementPriceVO.setSourceCountryCode(input.getSourceCountryCode());
        initAgreementPriceVO.setTargetCountryCode(input.getTargetCountryCode());
        CountryExtendPriceVO countryExtendPrice = this.getCountryExtendPrice(initAgreementPriceVO,
            CountryExtendPriceEnum.JD_PRICE.getCode());
        if(Objects.isNull(countryExtendPrice)) {
            CurrencyPriceVO currencyPriceVO = new CurrencyPriceVO();
            currencyPriceVO.setSuccess(false);
            currencyPriceVO.setMsg("未查询到京东价，无法更新");
            return currencyPriceVO;
        }else {
            countryExtendPrice.setPrice(input.getJdPrice());
            countryExtendPrice.setPriceMark("手动批量修改");
            countryExtendPrice.setUpdater(input.getPin());
            countryExtendPrice.setUpdateTime(new Date().getTime());
            DataResponse<Boolean> response = this.saveOrUpdate(countryExtendPrice);
            CurrencyPriceVO currencyPriceVO = new CurrencyPriceVO();
            currencyPriceVO.setSuccess(response.getSuccess());
            currencyPriceVO.setMsg(response.getMessage());
            return currencyPriceVO;
        }
    }
}
