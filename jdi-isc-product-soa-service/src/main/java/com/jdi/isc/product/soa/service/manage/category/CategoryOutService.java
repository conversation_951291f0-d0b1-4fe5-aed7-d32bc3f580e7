package com.jdi.isc.product.soa.service.manage.category;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.category.biz.CategoryReadDTO;
import com.jdi.isc.product.soa.api.category.biz.GmsCategoryReqDTO;
import com.jdi.isc.product.soa.api.wisp.category.biz.CategoryDTO;
import com.jdi.isc.product.soa.domain.category.biz.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 类目对外服务
 * <AUTHOR>
 * @date 20231127
 **/

public interface CategoryOutService {

    /**
     * 查询子类目
     * @param input 参数；一级类目的父类目id为0。
     * @return
     */
    List<CategoryComboBoxVO> queryChildren(CategoryComboBoxReqVO input);

    /**
     * 查询类目层级路径，包含自身类目
     * @param categoryId 类目id
     * @param lang 语种
     * @return 类目层级路径字符串。从一级类目依次往后排列。
     */
    String queryPathStr(Long categoryId, String lang);

    /**
     * 查询类目层级路径，包含自身类目
     * @param categoryId 类目id
     * @param lang 语种
     * @return 类目层级根据索引依次排列。索引0对应一级类目。
     */
    List<CategoryComboBoxVO> queryPath(Long categoryId, String lang);

    /**
     * 查询类目层级路径，包含自身类目
     * @param categoryIds 类目id集合
     * @param lang 语种
     * @return 类目层级路径字符串。从一级类目依次往后排列。
     */
    Map<Long/*类目ID*/,String/*类目层级路径*/> queryPathStr(Set<Long> categoryIds, String lang);

    /**
     * 查询类目层级路径，包含自身类目
     * @param categoryIds 类目id集合
     * @param lang 语种
     * @return 类目层级根据索引依次排列。索引0对应一级类目。
     */
    Map<Long/*类目ID*/,List<CategoryComboBoxVO>/*类目层级路径*/> queryPath(Set<Long> categoryIds, String lang);

    /**
     * 查询类目层级路径，包含自身类目
     * @param categoryIds 类目id集合
     * @param lang 语种
     * @return 类目层级根据索引依次排列。索引0对应一级类目。
     */
    Map<Long/*类目ID*/,List<CategoryComboBoxVO>/*类目层级路径*/> queryPathByStatus(Set<Long> categoryIds,Integer status, String lang);


    /**
     * 查2，3，4类目下的4级类目id集合
     * @param catId 类目ID集合
     * @return 4级类目id
     */
    List<Long> categoryLevel4From1234Set(Set<Long> catId);

    /**
     * 查某个类目下的4级类目id集合
     * @param catId 类目ID
     * @return 4级类目id
     */
    List<Long> categoryLevel4From1234(Long catId);

    /**
     * 查询类目层级路径，包含自身类目
     * @param categoryIds 类目id
     * @param langs 语种
     * @return 类目层级根据索引依次排列。索引0对应一级类目。
     */
    List<CategoryComboBoxVO> queryPath(Set<Long> categoryIds, List<String> langs);

    /**
     * 根据分类ID查询分类信息
     *
     * @param categoryId 分类ID
     * @return 分类信息
     * @throws Exception 异常信息
     */
    CategoryVO queryCategoryById(Long categoryId);

    /**
     * 根据末级分类ID查询各级分类ID
     * @param categoryIds 类目id集合
     * @return 类目层级。。
     */
    Map<Long, CategoryPathVO> queryPath(Set<Long> categoryIds);

    /**
     * 查询类别语言名称映射
     * @param catIds 类别ID集合
     * @param langs 语言集合
     * @return 类别ID和语言对应的名称映射
     */
    Map<Long, Map<String, String>> queryCatLangNameMap(Set<Long> catIds, Set<String> langs);

    /**
     *  查询子级类目
     */
    List<CategoryVO> queryChildByIds(Set<Long>  catIds);


    /**
     * 根据指定的类别ID集合、语言集合、状态和是否只查询正式类别，查询对应的类别信息。
     * @param catIds 类别ID集合
     * @param langSet 语言集合
     * @param status 状态
     * @param onlyFormalCat 是否只查询正式类别
     * @return 类别ID映射的类别VO列表
     */
    Map<Long,List<CategoryVO>> queryCategoryByIds(Set<Long> catIds,Set<String> langSet,Integer status,Boolean onlyFormalCat);


    /**
     * 根据给定的分类ID、语言集合和是否仅获取正式分类的标志，查询并返回相应的分类信息。
     * @param catId 分类ID
     * @param langSet 需要查询的语言集合
     * @param onlyFormalCat 是否仅获取正式分类的标志
     * @return 以分类ID为键，值为对应语言下的分类信息列表的Map对象
     */
    Map<Long,List<CategoryVO>> queryCategory(Long catId,Set<String> langSet,Boolean onlyFormalCat);

    Set<Long> queryLastCatId(CategoryIdVO categoryIdVO);

    List<CategoryTreeVo> queryListBySupplierCode(String supplierCode);

    /**
     * 查询类目层级路径，包含自身类目
     * @param categoryId 类目id
     * @param lang 语种
     * @return 类目层级根据索引依次排列。索引0对应一级类目。
     */
    List<CategoryPathVO> queryPathByStatus(Long categoryId, String lang,Integer status);

    /**
     * 根据提供的分类ID集合和状态查询分类路径和名称的映射关系。
     * @param catIds 需要查询的分类ID集合。
     * @param status 分类的状态。
     * @return 每个分类ID对应的分类路径和名称的VO对象的映射关系。
     */
    Map<Long,CategoryPathNameVO> queryCategoryByIds(Set<Long> catIds,Integer status);

    /**
     * 查询所有状态为指定值的分类路径信息。
     * @param status 分类状态，1表示正常，0表示删除。
     * @return List<CategoryPathNameVO> 包含所有符合条件的分类路径信息的列表。
     */
    List<CategoryPathNameVO> queryAllCategory(Integer status);

    /**
     * 查询平铺的所有分类的标题信息
     * @param langCodes 语言代码列表
     * @return 所有分类的标题信息列表
     */
    List<String> queryAllCategoryTile(Long index,Long size, List<String> langCodes);

    /**
     * 根据一级类目ID同步京东类目信息
     * @param reqVO 包含同步请求的必要信息的对象
     * @return 同步操作是否成功
     */
    boolean syncJdCategoryByFirstCatId(JdCategorySyncReqVO reqVO);


    /**
     * 根据请求获取京东商品分类信息
     * @param reqVO 包含同步请求的参数
     * @return 京东商品分类的详细信息
     */
    CategoryVO getJdCategory(JdCategorySyncReqVO reqVO);

    /**
     * 查询平铺的所有分类的标题信息
     * @param langCodes 语言代码列表
     * @return 所有分类的标题信息列表
     */
    List<String> queryCategoryBuyerAllCategoryTile(Long index,Long size,String countryCode, List<String> langCodes);

    /**
     * 将路径列表中的所有目录名连接成一个字符串
     * @param pathList 包含目录名的路径列表
     * @return 连接后的路径字符串
     */
    String joinPath(List<CategoryComboBoxVO> pathList);

    /**
     * 修改商品分类
     * @param reqDTO 请求参数，包含要修改的商品分类信息
     * @return 操作结果，true表示成功，false表示失败
     */
    DataResponse<Boolean> changeCategory(GmsCategoryReqDTO reqDTO);

    /**
     * 根据给定的ID列表查询对应的分类信息。
     * @param req 包含要查询的分类ID的请求对象。
     * @return 查询到的分类信息列表。
     */
    Map<Long,CategoryVO> queryCategoryByIds(CategoryReadDTO req);
}
