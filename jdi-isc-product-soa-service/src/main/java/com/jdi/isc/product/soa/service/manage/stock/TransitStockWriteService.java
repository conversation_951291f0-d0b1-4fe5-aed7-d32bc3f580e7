package com.jdi.isc.product.soa.service.manage.stock;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;

/**
 * <AUTHOR>
 * @description：在途库存专属接口接口
 * @Date 2025-03-10
 */
public interface TransitStockWriteService {

    /**
     * 在途库存退回并返回操作结果。
     * 1、在途库存退回
     * 2、本土厂直品现货库存退回
     * @param req 库存回退请求对象，包含调拨的商品信息，仓库信息。
     * @return 调拨操作的结果，true表示成功，false表示失败。
     */
    DataResponse<Boolean> transitStockReturn(StockManageReqDTO req);

    /**在途库存转现货库存**/
    DataResponse<Boolean> transitStockToStock(StockManageReqDTO req);
}
