package com.jdi.isc.product.soa.service.protocol.jsf.customerSku;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.customerSku.IscProductSoaCustomerSkuPriceReadApiService;
import com.jdi.isc.product.soa.api.customerSku.req.*;
import com.jdi.isc.product.soa.api.customerSku.res.*;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveOrderPO;
import com.jdi.isc.product.soa.domain.common.biz.BaseErpVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.*;
import com.jdi.isc.product.soa.service.atomic.approveorder.ApproveOrderAtomicService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceDraftManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceWarningManageService;
import com.jdi.isc.product.soa.service.mapstruct.customerSku.CustomerSkuPriceConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * sku定制化价格
 * <AUTHOR>
 * @date 2024/11/01
 */
@Service
@Slf4j
public class IscProductSoaCustomerSkuPriceReadApiServiceImpl implements IscProductSoaCustomerSkuPriceReadApiService {

    @Resource
    private CustomerSkuPriceDraftManageService customerSkuPriceDraftManageService;
    @Resource
    private CustomerSkuPriceWarningManageService customerSkuPriceWarningManageService;
    @Resource
    private ApproveOrderAtomicService approveOrderAtomicService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<CustomerSkuPriceDraftApiDTO>> page(CustomerSkuPriceDraftReqApiDTO input){
        log.info("IscProductSoaCustomerSkuReadApiServiceImpl.page param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CustomerSkuPriceDraftReqVO reqVO = CustomerSkuPriceConvert.INSTANCE.reqDto2Vo(input);
        PageInfo<CustomerSkuPriceDraftVO> pageInfo = customerSkuPriceDraftManageService.page(reqVO);
        return DataResponse.success(CustomerSkuPriceConvert.INSTANCE.pageVo2Dto(pageInfo));
    }


    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<CustomerSkuPriceAuditNumApiDTO>> auditStatusNum(CustomerSkuPriceDraftReqApiDTO input){
        log.info("IscProductSoaCustomerSkuReadApiServiceImpl.auditStatusNum param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CustomerSkuPriceDraftReqVO reqVO = CustomerSkuPriceConvert.INSTANCE.reqDto2Vo(input);
        List<CustomerSkuPriceAuditNumVO> results = customerSkuPriceDraftManageService.auditStatusNum(reqVO);
        return DataResponse.success(CustomerSkuPriceConvert.INSTANCE.auditNumVo2Dto(results));
    }

    @Override
    public DataResponse<List<CustomerSkuEmailDTO>> waitingAudit(){
        log.info("IscProductSoaCustomerSkuReadApiServiceImpl.waitingAudit start");
        List<BaseErpVO> erpVOList = customerSkuPriceDraftManageService.waitingAudit();
        if(CollectionUtils.isEmpty(erpVOList)){
            return DataResponse.success();
        }
        List<CustomerSkuEmailDTO> results = new ArrayList<>();
        for(BaseErpVO baseErpVO : erpVOList){
            CustomerSkuEmailDTO customerSkuEmailDTO = new CustomerSkuEmailDTO();
            customerSkuEmailDTO.setErp(baseErpVO.getErp());
            customerSkuEmailDTO.setErpName(baseErpVO.getErpName());
            customerSkuEmailDTO.setEmail(baseErpVO.getEmail());
            results.add(customerSkuEmailDTO);
        }
        return DataResponse.success(results);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<SalePriceCalculateResDTO> getSalePrice(SalePriceCalculateReqDTO input) {
        log.info("IscProductSoaProfitRateReadApiServiceImpl.getSalePrice param:{}", com.alibaba.fastjson.JSONObject.toJSONString(input));
        SalePriceCalculateResVO resVO = customerSkuPriceDraftManageService.calculateCustomerSalePrice(CustomerSkuPriceConvert.INSTANCE.calculateDto2Vo(input));
        return DataResponse.success(CustomerSkuPriceConvert.INSTANCE.calculateVo2Dto(resVO));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<CustomerSkuPriceAuditApiDTO>> approvePage(CustomerSkuPriceAuditReqApiDTO input) {
        log.info("IscProductSoaProfitRateReadApiServiceImpl.approvePage param:{}", com.alibaba.fastjson.JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        PageInfo<CustomerSkuPriceAuditResVO> resVO = customerSkuPriceDraftManageService.approvePage(CustomerSkuPriceConvert.INSTANCE.auditReqApiDto2Vo(input));
        return DataResponse.success(CustomerSkuPriceConvert.INSTANCE.auditResApiVo2Dto(resVO));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<CustomerSkuPriceWarningDTO>> warningPage(CustomerSkuPriceWarningPageReqDTO input) {
        log.info("IscProductSoaCustomerSkuReadApiServiceImpl.warningPage param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CustomerSkuPriceWarningPageReqVO reqVO = CustomerSkuPriceConvert.INSTANCE.warningReqDto2Vo(input);
        PageInfo<CustomerSkuPriceWarningVO> pageInfo = customerSkuPriceWarningManageService.pageSearch(reqVO);
        return DataResponse.success(CustomerSkuPriceConvert.INSTANCE.warningPageVo2Dto(pageInfo));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<CustomerSkuPriceDraftExportApiDTO>> queryCustomerSkuDraft(CustomerSkuPriceDraftReqApiDTO input) {
        log.info("IscProductSoaCustomerSkuReadApiServiceImpl.queryCustomerSkuDraft param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CustomerSkuPriceDraftReqVO reqVO = CustomerSkuPriceConvert.INSTANCE.reqDto2Vo(input);
        List<CustomerSkuPriceDraftExportApiDTO> results = customerSkuPriceDraftManageService.queryCustomerSkuDraft(reqVO);
        return DataResponse.success(results);
    }

    @Override
    public DataResponse<List<CustomerSkuPriceWarningDTO>> listWarningsByApproveIds(CustomerSkuPriceWarningPageReqDTO input) {
        if (input == null || input.getApproveIds() == null || input.getApproveIds().isEmpty()) {
            return DataResponse.success(Lists.newArrayList());
        }

        List<ApproveOrderPO> list = approveOrderAtomicService.listByIds(input.getApproveIds());
        List<Long> warnIds = list.stream().map(item -> {
            if (StringUtils.isNumeric(item.getBizId())) {
                return Long.valueOf(item.getBizId());
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        List<CustomerSkuPriceWarningDTO> data = customerSkuPriceWarningManageService.listWarningsByIds(warnIds);
        return DataResponse.success(data);
    }
}
