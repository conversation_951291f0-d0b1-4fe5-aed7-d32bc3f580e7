package com.jdi.isc.product.soa.service.manage.auth.impl;

import com.alibaba.fastjson.JSON;
import com.jd.auth.facade.request.BaseRequest;
import com.jd.auth.facade.request.dim.DimResCodeRequest;
import com.jd.auth.facade.request.user.ErpResRequest;
import com.jd.auth.facade.request.user.UserRequest;
import com.jd.auth.facade.request.user.UserResRequest;
import com.jd.auth.facade.response.Response;
import com.jd.auth.facade.response.menu.FuncDto;
import com.jd.auth.facade.response.menu.MenuDto;
import com.jd.auth.facade.response.menu.MenuFuncDto;
import com.jd.auth.facade.response.res.DimResourceDto;
import com.jd.auth.facade.response.res.DimRowDto;
import com.jd.auth.facade.response.role.RoleDto;
import com.jdi.isc.product.soa.common.constants.AuthConstant;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.domain.auth.AuthReqVO;
import com.jdi.isc.product.soa.domain.auth.FunctionVO;
import com.jdi.isc.product.soa.domain.auth.RouteResVO;
import com.jdi.isc.product.soa.domain.auth.RouteVO;
import com.jdi.isc.product.soa.rpc.adapter.mapstruct.auth.AuthConvert;
import com.jdi.isc.product.soa.rpc.auth.AuthRpcService;
import com.jdi.isc.product.soa.service.manage.auth.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 权限Rpc服务
 * <AUTHOR>
 * @date 2024-09-27
 */
@Slf4j
@Service
public class AuthServiceImpl implements AuthService {
    /**
     * 访问权限Rpc服务的实例，用于调用Rpc服务的方法。
     */
    @Resource
    private AuthRpcService authRpcService;

    @Resource
    private JimUtils jimUtils;

    @Value("${jdi.isc.task.env}")
    private String env;
    /**
     * 检查资源是否存在。
     * @param vo 授权请求对象，包含资源信息。
     * @return true 如果资源存在，false 否则。
     */
    @Override
    public Response<Boolean> checkResource(AuthReqVO vo) {
        ErpResRequest erpResRequest = AuthConvert.INSTANCE.vo2ErpResRequest(vo);
        Response<Boolean> booleanResponse = authRpcService.checkResource(erpResRequest);
        return booleanResponse;
    }

    /**
     * 根据ERP信息过滤资源。
     * @param vo 认证请求对象，包含ERP相关信息。
     * @return 过滤后的资源列表。
     */
    @Override
    public Response<List<String>> filterResourcesByErp(AuthReqVO vo) {
        ErpResRequest erpResRequest = AuthConvert.INSTANCE.vo2ErpResRequest(vo);
        Response<List<String>> listResponse = authRpcService.filterResourcesByErp(erpResRequest);
        return listResponse;
    }

    /**
     * 查询系统所有菜单
     * @param vo 认证请求体
     * @return 菜单列表响应
     */
    @Override
    public Response<List<MenuDto>> querySystemAllMenu(AuthReqVO vo) {
        BaseRequest baseRequest = new BaseRequest();
        Response<List<MenuDto>> listResponse = authRpcService.querySystemAllMenu(baseRequest);
        return listResponse;
    }

    /**
     * 获取根菜单
     * @param vo 认证请求VO
     * @return 根菜单列表
     */
    @Override
    public Response<List<MenuDto>> getRootMenu(AuthReqVO vo) {
        UserRequest userRequest = AuthConvert.INSTANCE.vo2UserRequest(vo);
        Response<List<MenuDto>> rootMenu = authRpcService.getRootMenu(userRequest);
        return rootMenu;
    }

    /**
     * 获取菜单树并转换为路由对象
     * @param vo 认证请求参数
     * @return 路由响应对象，包含路由列表和菜单ID列表
     */
    @Override
    public RouteResVO getMenuTree(AuthReqVO vo) {
        UserRequest userRequest = AuthConvert.INSTANCE.vo2UserRequest(vo);
        Response<List<MenuDto>> response = authRpcService.getMenuTree(userRequest);
        List<RouteVO> routeVOList = new ArrayList<>();
        List<String> idList = new ArrayList<>();
        convertMenuTree(routeVOList,response.getData(),idList);
        RouteResVO routeResVO = new RouteResVO();
        routeResVO.setRouteVOList(routeVOList);
        routeResVO.setIdList(idList);
        return routeResVO;
    }

    /**
     * 获取菜单功能列表
     * @param vo 认证请求信息
     * @return 菜单功能列表
     */
    @Override
    public Response<List<FuncDto>> getMenuFun(AuthReqVO vo) {
        UserResRequest userResRequest =  AuthConvert.INSTANCE.vo2UserResRequest(vo);
        Response<List<FuncDto>> menuFun = authRpcService.getMenuFun(userResRequest);
        return menuFun;
    }

    @Override
    public RouteResVO getAllMenuFunc(AuthReqVO vo) {
        try {
            String erp = vo.getErp();
            final String cacheKey = CacheKeyConstant.getKey(CacheKeyConstant.ALL_MENU_FUNC_KEY) + env + erp;
            String cachedResult = jimUtils.get(cacheKey);
            if (StringUtils.isNotBlank(cachedResult)) {
                return JSON.parseObject(cachedResult, RouteResVO.class);
            }
            UserRequest userRequest = AuthConvert.INSTANCE.vo2UserRequest(vo);
            Response<List<MenuDto>> response = authRpcService.getAllMenuFunc(userRequest);
            List<RouteVO> routeVOList = new ArrayList<>();
            List<String> idList = new ArrayList<>();
            convertMenuTree(routeVOList, response.getData(), idList);
            RouteResVO routeResVO = new RouteResVO();
            routeResVO.setRouteVOList(routeVOList);
            routeResVO.setIdList(idList);
            if(CollectionUtils.isNotEmpty(routeVOList)){
                jimUtils.set(cacheKey, JSON.toJSONString(routeResVO), 3, TimeUnit.MINUTES);
            }
            return routeResVO;
        }catch (Exception e){
            log.error("AuthServiceImpl.getAllMenuFunc error:{}",e);
        }
        return new RouteResVO();
    }


    /**
     * 根据资源代码获取角色列表。
     * @param vo 认证请求对象，包含资源代码等信息。
     * @return 角色列表的响应对象。
     */
    @Override
    public Response<List<RoleDto>> getRoleByResCode(AuthReqVO vo) {
        ErpResRequest erpResRequest = AuthConvert.INSTANCE.vo2ErpResRequest(vo);
        Response<List<RoleDto>> roleByResCode = authRpcService.getRoleByResCode(erpResRequest);
        return roleByResCode;
    }

    /**
     * 根据授权请求查询维度资源值列表。
     * @param vo 授权请求对象，包含查询条件。
     * @return 维度资源值列表的响应对象。
     */
    @Override
    public Response<List<DimResourceDto>> queryDimResValueList(AuthReqVO vo) {
        DimResCodeRequest request = AuthConvert.INSTANCE.vo2DimResCodeRequest(vo);
        Response<List<DimResourceDto>> listResponse = authRpcService.queryDimResValueList(request);
        log.info("CustomerManageServiceImpl filterCustomer vo:{}.response:{}", JSON.toJSONString(vo),JSON.toJSONString(listResponse));
        return listResponse;
    }

    /**
     * 根据 AuthReqVO 获取角色组代码集合
     * @param vo 认证请求对象
     * @return 角色组代码集合
     */
    @Override
    public Response<Set<String>> getRoleGroupCode(AuthReqVO vo){
        Response<List<DimResourceDto>> response = this.queryDimResValueList(vo);
        log.info("CustomerManageServiceImpl filterCustomer response:{}", com.jd.fastjson.JSON.toJSONString(response));
        List<DimResourceDto> data = response.getData();
        log.info("CustomerManageServiceImpl filterCustomer data:{}", com.jd.fastjson.JSON.toJSONString(data));
        Set<String> roleGroupCodes = new HashSet<>();
        for (DimResourceDto dto : data){
            List<DimRowDto> dimRowDtoList = dto.getDimRowDtoList();
            for (DimRowDto dimRowDto : dimRowDtoList){
                roleGroupCodes.add(dimRowDto.getPrimaryKeyVal());
            }
        }
        return Response.success(roleGroupCodes);
    }


    /**
     * 将菜单树转换为路由树，并根据授权信息过滤功能列表。
     * @param routeVOList 路由树的根节点列表。
     * @param menuDtoList 菜单树的根节点列表。
     * @param idList 用于去重的菜单ID列表。
     */
    private void convertMenuTree(List<RouteVO> routeVOList, List<MenuDto> menuDtoList, List<String> idList) {
        for (MenuDto menuDto : menuDtoList) {
            RouteVO vo = new RouteVO();
            vo.setId(menuDto.getResCode());
            vo.setTitle(menuDto.getName());
            setMenuExtInfo(vo, menuDto.getMenuExtInfoMap());

            if (menuDto instanceof MenuFuncDto) {
                MenuFuncDto dto = (MenuFuncDto) menuDto;
                List<FunctionVO> functionVOList = convertFuncList(dto.getFuncList());
                vo.setFuncList(functionVOList);
            }

            List<RouteVO> subRoutes = new ArrayList<>();
            if (StringUtils.isNotBlank(vo.getRedirect())) {
                RouteVO redirectRoute = createRedirectRoute(vo);
                subRoutes.add(redirectRoute);
            }

            List<MenuDto> subMenuList = menuDto.getSubMenuList();
            idList.add(menuDto.getResCode());
            convertMenuTree(subRoutes, subMenuList, idList);
            vo.setRoutes(subRoutes);
            routeVOList.add(vo);
        }
    }

    /**
     * 设置菜单扩展信息到路由VO对象中。
     * @param vo 路由VO对象，用于存储菜单扩展信息。
     * @param menuExtInfoMap 菜单扩展信息Map，包含path、component、name、redirect和hideInMenu等属性。
     */
    private void setMenuExtInfo(RouteVO vo, Map<String, String> menuExtInfoMap) {
        if (MapUtils.isNotEmpty(menuExtInfoMap)) {
            vo.setPath(menuExtInfoMap.getOrDefault("path", null));
            vo.setComponent(menuExtInfoMap.getOrDefault("component", null));
            vo.setName(menuExtInfoMap.getOrDefault("name", null));
            vo.setRedirect(menuExtInfoMap.getOrDefault("redirect", null));
            vo.setHideInMenu("true".equals(menuExtInfoMap.getOrDefault("hideInMenu", "false")));
        }
    }

    /**
     * 将 FuncDto 列表转换为 FunctionVO 列表，并从 funcIdSet 中移除已处理的函数 ID。
     * @param funcList FuncDto 列表
     * @return 转换后的 FunctionVO 列表
     */
    private List<FunctionVO> convertFuncList(List<FuncDto> funcList) {
        List<FunctionVO> functionVOList = new ArrayList<>();
        for (FuncDto funcDto : funcList) {
            FunctionVO functionVO = new FunctionVO();
            functionVO.setId(funcDto.getResCode());
            functionVO.setTitle(funcDto.getName());
            functionVO.setPath(funcDto.getUrl());
            functionVOList.add(functionVO);
        }
        return functionVOList;
    }

    /**
     * 创建重定向路由对象
     * @param vo 原始路由对象
     * @return 重定向路由对象
     */
    private RouteVO createRedirectRoute(RouteVO vo) {
        RouteVO redirectRoute = new RouteVO();
        redirectRoute.setId(vo.getId());
        redirectRoute.setPath(vo.getPath());
        redirectRoute.setRedirect(vo.getRedirect());
        return redirectRoute;
    }

}
