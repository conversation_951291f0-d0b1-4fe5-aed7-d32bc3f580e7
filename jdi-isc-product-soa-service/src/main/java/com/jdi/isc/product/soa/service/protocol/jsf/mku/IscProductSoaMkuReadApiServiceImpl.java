package com.jdi.isc.product.soa.service.protocol.jsf.mku;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.mku.IscProductSoaMkuReadApiService;
import com.jdi.isc.product.soa.api.mku.req.*;
import com.jdi.isc.product.soa.api.mku.res.IscMkuAvailableSaleResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuLangsResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuResDTO;
import com.jdi.isc.product.soa.api.spu.res.SpuApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientPageReqApiDTO;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.domain.mku.biz.MkuClientPageReqVO;
import com.jdi.isc.product.soa.domain.mku.biz.SpecialAttrMkuClientReqVO;
import com.jdi.isc.product.soa.domain.mku.biz.SpecialAttrMkuReqVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuVO;
import com.jdi.isc.product.soa.service.manage.customerMku.CustomerMkuReadManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuEsManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuManageService;
import com.jdi.isc.product.soa.service.mapstruct.mku.MkuConvert;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class IscProductSoaMkuReadApiServiceImpl implements IscProductSoaMkuReadApiService {

    @Resource
    private MkuManageService mkuManageService;
    @Resource
    private MkuEsManageService mkuEsManageService;
    @Resource
    private CustomerMkuReadManageService customerMkuReadManageService;

    @Override
    public DataResponse<Long> queryBySkuId(QueryMkuReqDTO input) {
        log.info("IscProductSoaMkuReadApiServiceImpl.queryBySkuId param:{}", JSONObject.toJSONString(input));
        String skuID = input.getSkuId();
        if(StringUtils.isBlank(skuID)){
            log.error("IscProductSoaMkuReadApiServiceImpl.queryBySkuId skuID is null");
            return null;
        }
        Long skuId = Long.parseLong(skuID);
        return DataResponse.success(mkuManageService.queryMkuBySkuId(skuId));
    }

    @Override
    public DataResponse<PageInfo<SpuApiDTO>> queryPageForSpu(MkuClientPageReqApiDTO input) {
        log.info("IscProductSoaMkuReadApiServiceImpl.queryPageForSpu param:{}", JSONObject.toJSONString(input));
        MkuClientPageReqVO pageReqVO = MkuConvert.INSTANCE.mkuClientPageReqDTO2VO(input);
        LangContextHolder.init(StringUtils.isNotBlank(input.getLang())?input.getLang(): LangConstant.LANG_ZH);
        PageInfo<SpuVO> pageInfo = mkuEsManageService.queryPageForSpu(pageReqVO);
        if (null==pageInfo){
            log.warn("IscProductSoaMkuReadApiServiceImpl.queryPageForSpu pageInfo is null null.");
            return DataResponse.error("page record empty.");
        }
        PageInfo<SpuApiDTO> pageInfoDTO = SpuConvert.INSTANCE.spuPageVo2ApiDto(pageInfo);
        return DataResponse.success(pageInfoDTO);
    }

    /**
     * 对外查询查询mku信息
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long,IscMkuResDTO>> getIscMku(BatchQueryMkuReqDTO req) {
        return DataResponse.success(mkuManageService.getIscMku(req));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, Set<Long>>> getIscMkuIdByJdSkuId(BatchQueryMkuReqDTO req) {
        return DataResponse.success(mkuManageService.getIscMkuIdByJdSkuId(req));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, Map<String, String>>> querySpecialAttrMapByMkuIds(SpecialAttrMkuReqDTO input) {
        SpecialAttrMkuReqVO specialAttrMkuReqVO = MkuConvert.INSTANCE.specialAttrMkuReqDTO2VO(input);
        return mkuManageService.querySpecialAttrMapByMkuIds(specialAttrMkuReqVO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, Map<String, String>>> querySpecialAttrMapByClientCode(SpecialAttrMkuClientReqDTO input) {
        SpecialAttrMkuClientReqVO specialAttrMkuClientReqVO = MkuConvert.INSTANCE.specialAttrMkuClientDTO2VO(input);
        return mkuManageService.querySpecialAttrMapByClientCode(specialAttrMkuClientReqVO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, Long>> getIscMkuIdBySkuId(BatchQueryMkuReqDTO req) {
        return DataResponse.success(mkuManageService.getIscMkuIdBySkuId(req));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, IscMkuAvailableSaleResDTO>> queryMkuAvailable(IscMkuAvailableSaleReq req) {
        return DataResponse.success(customerMkuReadManageService.queryMkuAvailable(req));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, IscMkuLangsResDTO>> getIscMkuLangs(BatchQueryMkuLangsReqDTO req) {
        return DataResponse.success(mkuManageService.getIscMkuLangs(req));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, Long>> getIscSkuIdByMkuId(BatchQueryMkuReqDTO req) {
        return DataResponse.success(mkuManageService.getIscSkuIdByMkuId(req));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, Long>> getIscJdSkuIdByMkuId(BatchQueryMkuReqDTO req) {
        return DataResponse.success(mkuManageService.getIscJdSkuIdByMkuId(req));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, Boolean>> getIscSpuSkuRelationByMkuId(BatchQueryMkuReqDTO req) {
        return DataResponse.success(mkuManageService.getIscSpuSkuRelationByMkuId(req));
    }
}
