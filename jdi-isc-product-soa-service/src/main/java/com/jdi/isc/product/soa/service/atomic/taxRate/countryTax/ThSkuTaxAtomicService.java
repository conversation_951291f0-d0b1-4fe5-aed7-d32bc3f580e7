package com.jdi.isc.product.soa.service.atomic.taxRate.countryTax;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.ThSkuTaxPO;
import com.jdi.isc.product.soa.price.api.price.req.ThSkuTaxVO;
import com.jdi.isc.product.soa.repository.mapper.taxRate.countryTax.ThSkuTaxBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 泰国SKU税率原子服务
 * <AUTHOR>
 * @date 20250311
 */
@Service
@Slf4j
public class ThSkuTaxAtomicService extends ServiceImpl<ThSkuTaxBaseMapper, ThSkuTaxPO> {

    /**
     * 单个查询
     */
    public ThSkuTaxPO getOne(ThSkuTaxVO vo) {
        LambdaQueryWrapper<ThSkuTaxPO> query = Wrappers.<ThSkuTaxPO>lambdaQuery()
                .eq(ThSkuTaxPO::getJdSkuId, vo.getJdSkuId())
                .eq(ThSkuTaxPO::getYn, YnEnum.YES.getCode());
        return super.getOne(query);
    }

    public Map<Long,ThSkuTaxPO> listSkuTax(Set<Long> skuIds){
        LambdaQueryWrapper<ThSkuTaxPO> queryWrapper = Wrappers.<ThSkuTaxPO>lambdaQuery()
                .in(ThSkuTaxPO::getJdSkuId, skuIds)
                .eq(ThSkuTaxPO::getYn, YnEnum.YES.getCode());
        List<ThSkuTaxPO> res = super.list(queryWrapper);
        if(CollectionUtils.isNotEmpty(res)){
            return res.stream().collect(Collectors.toMap(ThSkuTaxPO::getJdSkuId, a -> a));
        }
        return new HashMap<>();
    }

    public boolean updateTax(ThSkuTaxVO vo) {
        LambdaUpdateWrapper<ThSkuTaxPO> wrapper = Wrappers.<ThSkuTaxPO>lambdaUpdate()
            .set(StringUtils.isNotBlank(vo.getHsCode()), ThSkuTaxPO::getHsCode, vo.getHsCode())
            .set(vo.getValueAddedTax() != null, ThSkuTaxPO::getValueAddedTax, vo.getValueAddedTax())
            .set(vo.getConsumptionTax() != null, ThSkuTaxPO::getConsumptionTax, vo.getConsumptionTax())
            .set(vo.getFormeTax() != null, ThSkuTaxPO::getFormeTax, vo.getFormeTax())
            .set(vo.getMfnTax() != null, ThSkuTaxPO::getMfnTax, vo.getMfnTax())
            .set(vo.getAntiDumpingTax() != null, ThSkuTaxPO::getAntiDumpingTax, vo.getAntiDumpingTax())
            .set(vo.getLocalTax() != null, ThSkuTaxPO::getLocalTax, vo.getLocalTax())
            .set(vo.getTisi() != null, ThSkuTaxPO::getTisi, vo.getTisi())
            .set(StringUtils.isNotBlank(vo.getRemark()), ThSkuTaxPO::getRemark, vo.getRemark())
            .set(ThSkuTaxPO::getUpdater, vo.getUpdater())
            .set(ThSkuTaxPO::getUpdateTime, new Date())
            .eq(ThSkuTaxPO::getId, vo.getId());
        return super.update(wrapper);
    }

}