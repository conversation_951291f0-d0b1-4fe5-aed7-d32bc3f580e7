package com.jdi.isc.product.soa.service.manage.category.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.AttributeCheckTypeEnum;
import com.jdi.isc.product.soa.common.enums.AttributeInputTypeEnum;
import com.jdi.isc.product.soa.common.enums.UseSceneEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.domain.attribute.biz.CrossAttributeChangeConfig;
import com.jdi.isc.product.soa.domain.category.biz.*;
import com.jdi.isc.product.soa.domain.category.po.*;
import com.jdi.isc.product.soa.domain.common.biz.BasicVO;
import com.jdi.isc.product.soa.domain.common.biz.KvVO;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.domain.spu.biz.GroupPropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;
import com.jdi.isc.product.soa.service.atomic.category.*;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.manage.category.GlobalAttributeManageService;
import com.jdi.isc.product.soa.service.manage.country.CountryLangManageService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.mapstruct.SkuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.attribute.AttributeConvert;
import com.jdi.isc.product.soa.service.mapstruct.category.GlobalAttributeConvert;
import com.jdi.isc.product.soa.service.mapstruct.category.GlobalAttributeLangConvert;
import com.jdi.isc.product.soa.service.mapstruct.category.GlobalAttributeValueConvert;
import com.jdi.isc.product.soa.service.mapstruct.category.GlobalAttributeValueLangConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.api.common.LangConstant.LANG_ZH;

/**
 * @Description: 国际跨境属性数据维护服务实现
 * @Author: taxuezheng1
 * @Date: 2024/07/12 13:17
 **/

@Slf4j
@Service
public class GlobalAttributeManageServiceImpl extends BaseManageSupportService<GlobalAttributeVO, GlobalAttributePO> implements GlobalAttributeManageService {

    @Resource
    private GlobalAttributeAtomicService globalAttributeAtomicService;
    @Resource
    private GlobalAttributeLangAtomicService globalAttributeLangAtomicService;
    @Resource
    private GlobalAttributeValueAtomicService globalAttributeValueAtomicService;
    @Resource
    private GlobalAttributeValueLangAtomicService globalAttributeValueLangAtomicService;
    @Resource
    private LangManageService langManageService;
    @Resource
    private GlobalCountryCategoryAttributeRelationAtomicService countryCategoryAttributeRelationAtomicService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    @Lazy
    private SpuConvertService spuConvertService;
    @Resource
    @Lazy
    private SkuConvertService skuConvertService;
    @Resource
    private CountryLangManageService countryLangManageService;

    @Resource
    private OperDuccConfig operDuccConfig;

    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> saveOrUpdate(GlobalAttributeVO input) {
        log.info("saveOrUpdate, input={}", JSONObject.toJSONString(input));

        // 参数业务校验
        DataResponse<Boolean> checkResult = checkAndInitInput(input);
        if (!checkResult.getSuccess()){
            log.info("saveOrUpdate, check input fail, input={}, checkResult={}", JSONObject.toJSONString(input), JSONObject.toJSONString(checkResult));
            return checkResult;
        }
        long timestamp = System.currentTimeMillis();
        // 保存对象
        GlobalAttributePO po = GlobalAttributeConvert.INSTANCE.vo2Po(input);
        po.setCreateTime(timestamp);
        if (po.getId() != null) {
            po.setCreateTime(null);
            po.setCreator(null);
        }
        po.setUpdateTime(timestamp);
        boolean saveRes = globalAttributeAtomicService.saveOrUpdate(po);
        Assert.isTrue(saveRes,"保存国际属性异常");
        //属性多语言
        List<GlobalAttributeLangPO> attributeLangPOList = GlobalAttributeLangConvert.INSTANCE.listVo2Po(input.getAttributeLangList());
        attributeLangPOList.forEach(a -> {
            a.setAttributeId(po.getId());
            a.setUpdater(input.getUpdater());
            a.setUpdateTime(timestamp);
            if (a.getId() == null) {
                a.setCreator(input.getCreator());
                a.setCreateTime(timestamp);
            }
        });
        boolean batchAttrLang = globalAttributeLangAtomicService.saveOrUpdateBatch(attributeLangPOList);
        Assert.isTrue(batchAttrLang,"保存国际属性多语言异常");

        List<GlobalAttributeValueVO> attributeValueList = input.getAttributeValueList();
        List<GlobalAttributeValueLangPO> attributeValueLangList = new ArrayList<>();
        for (GlobalAttributeValueVO attributeValue : attributeValueList) {
            GlobalAttributeValuePO attributeValuePO = GlobalAttributeValueConvert.INSTANCE.vo2Po(attributeValue);
            attributeValuePO.setUpdateTime(timestamp);
            attributeValuePO.setUpdater(input.getUpdater());
            attributeValuePO.setAttributeId(po.getId());
            attributeValuePO.setValidStatus(YnEnum.YES.getCode());
            if (attributeValuePO.getId() == null) {
                attributeValuePO.setCreateTime(timestamp);
                attributeValuePO.setCreator(input.getCreator());
            }

            boolean attrValueRes = globalAttributeValueAtomicService.saveOrUpdate(attributeValuePO);
            Assert.isTrue(attrValueRes,"保存属性值失败");
            List<GlobalAttributeValueLangPO> saveAttrValueLang= GlobalAttributeValueLangConvert.INSTANCE.listVo2Po(attributeValue.getAttributeValueLangList());
            saveAttrValueLang.forEach(a -> {
                a.setAttributeId(po.getId());
                a.setAttributeValueId(attributeValuePO.getId());
                a.setUpdater(input.getUpdater());
                a.setUpdateTime(timestamp);
                if (a.getId() == null) {
                    a.setCreateTime(timestamp);
                    a.setCreator(input.getCreator());
                }
            });
            attributeValueLangList.addAll(saveAttrValueLang);
        }
        boolean batchAttrValueLang = globalAttributeValueLangAtomicService.saveOrUpdateBatch(attributeValueLangList);
        Assert.isTrue(batchAttrValueLang,"保存属性值多语言失败");
        if (CollectionUtils.isNotEmpty(input.getDelAttrValueIds())) {
            boolean attrValueRes = globalAttributeValueAtomicService.removeBatchByIds(input.getDelAttrValueIds());
            Assert.isTrue(attrValueRes,"删除属性值失败,id:"+ StringUtils.join(input.getDelAttrValueIds(),","));
            boolean attrValueLangRes = globalAttributeValueLangAtomicService.removeBatchByAttrValueIds(input.getDelAttrValueIds());
            Assert.isTrue(attrValueLangRes,"删除属性值多语言失败，id："+ StringUtils.join(input.getDelAttrValueIds(),","));
        }
        return DataResponse.success(true);
    }

    @Override
    public GlobalAttributeVO detail(Long id) {
        GlobalAttributePO po = globalAttributeAtomicService.getValidById(id);
        if (null==po){
            log.info("detail, GlobalAttributePO null. id={}", id);
            return null;
        }
        Map<String, String> langMap = langManageService.getLangMap(null);
        //属性多语言
        List<GlobalAttributeLangPO> attributeLangPOList = globalAttributeLangAtomicService.queryByAttrIds(Collections.singleton(id));
        //属性值
        List<GlobalAttributeValuePO> attributeValuePOS = globalAttributeValueAtomicService.queryByAttrId(id);
        //属性值多语言
        List<GlobalAttributeValueLangPO> attributeValueLangPOS = globalAttributeValueLangAtomicService.queryByAttrId(id);

        GlobalAttributeVO vo = GlobalAttributeConvert.INSTANCE.po2Vo(po);
        List<GlobalAttributeLangVO> attributeLangVos = GlobalAttributeLangConvert.INSTANCE.listPo2Vo(attributeLangPOList);
        List<GlobalAttributeValueVO> attributeValueVos = GlobalAttributeValueConvert.INSTANCE.listPo2Vo(attributeValuePOS);
        List<GlobalAttributeValueLangVO> attributeValueLangVos = GlobalAttributeValueLangConvert.INSTANCE.listPo2Vo(attributeValueLangPOS);
        if (CollectionUtils.isNotEmpty(attributeValueLangVos)) {
            Map<Long, List<GlobalAttributeValueLangVO>> attrValueMap = attributeValueLangVos.stream().collect(Collectors.groupingBy(GlobalAttributeValueLangVO::getAttributeValueId));
            attributeValueVos.forEach(a -> {
                a.setAttributeValueLangList(attrValueMap.get(a.getId()));
            });
        }
        if (CollectionUtils.isNotEmpty(attributeLangVos)) {
            attributeLangVos.forEach(a -> {
                a.setLangName(langMap.get(a.getLang()));
            });
        }
        vo.setAttributeLangList(attributeLangVos);
        vo.setAttributeValueList(attributeValueVos);
        return vo;
    }

    @Override
    public PageInfo<GlobalAttributePageVO.Response> pageSearch(GlobalAttributePageVO.Request input) {

        PageInfo<GlobalAttributePageVO.Response> pageInfo = new PageInfo<>();
        pageInfo.setSize(input.getSize());
        pageInfo.setIndex(input.getIndex());

        //多语言查询
        Set<Long> attrIds = null;
        if (CollectionUtils.isNotEmpty(input.getAttributeLangList())) {
            GlobalAttributeLangVO globalAttributeLangVO = input.getAttributeLangList().get(0);
            List<GlobalAttributeLangPO> attributeLangPOS = globalAttributeLangAtomicService.queryLangAndLikeAttrName(globalAttributeLangVO.getLang(), globalAttributeLangVO.getLangAttributeName());
            attrIds = attributeLangPOS.stream().map(GlobalAttributeLangPO::getAttributeId).collect(Collectors.toSet());
        }

        // 查询列表
        Page<GlobalAttributePO> pageDB = new Page<>(input.getIndex(), input.getSize());
        LambdaQueryWrapper<GlobalAttributePO> wrapper = Wrappers.<GlobalAttributePO>lambdaQuery()
                .eq(GlobalAttributePO::getYn, YnEnum.YES.getCode())
                .eq(Objects.nonNull(input.getId()),GlobalAttributePO::getId,input.getId())
                .in(Objects.nonNull(attrIds),GlobalAttributePO::getId,attrIds)
                .orderByDesc(GlobalAttributePO::getUpdateTime);
        Page<GlobalAttributePO> dbRecord = globalAttributeAtomicService.page(pageDB, wrapper);
        pageInfo.setTotal(dbRecord.getTotal());
        if (CollectionUtils.isEmpty(dbRecord.getRecords())){
            return pageInfo;
        }

        List<GlobalAttributePageVO.Response> pageList = GlobalAttributeConvert.INSTANCE.pageListPo2Vo(dbRecord.getRecords());
        if (CollectionUtils.isNotEmpty(pageList)){
            //查询多语言信息
            Set<Long> attIds = dbRecord.getRecords().stream().map(GlobalAttributePO::getId).collect(Collectors.toSet());
            List<GlobalAttributeLangPO> attributeLangPOList = globalAttributeLangAtomicService.queryByAttrIds(attIds);
            List<GlobalAttributeLangVO> attributeLangVos = GlobalAttributeLangConvert.INSTANCE.listPo2Vo(attributeLangPOList);
            Map<Long, List<GlobalAttributeLangVO>> listMap = attributeLangVos.stream().collect(Collectors.groupingBy(GlobalAttributeLangVO::getAttributeId));
            pageList.forEach(p -> {
                p.setAttributeLangList(listMap.get(p.getId()));
            });
            pageInfo.setRecords(pageList);
        }

        return pageInfo;
    }


    /**
     * 参数校验
     * @param input 提交参数
     * @return 检查结果
     */
    private DataResponse<Boolean> checkAndInitInput(GlobalAttributeVO input){

        List<GlobalAttributeLangVO> attributeLangList = input.getAttributeLangList();
        GlobalAttributeVO checkParam = new GlobalAttributeVO();
        checkParam.setId(input.getId());
        StringBuilder errorMsg = new StringBuilder();
        for (GlobalAttributeLangVO langVO : attributeLangList) {
            checkParam.setAttributeLangList(Collections.singletonList(langVO));
            boolean checkRes = checkLangName(checkParam);
            if (!checkRes) {
                errorMsg.append(langVO.getLangAttributeName()).append("名称重复,");

            }
        }
        if (errorMsg.length() > 0) {
            String error = errorMsg.substring(0, errorMsg.length());
            return DataResponse.buildError(error);
        }
        // 跨境属性输入类型为文本，输入内容必填，1:字符串 2:数字
        if (AttributeInputTypeEnum.TEXT.getCode().equals(input.getAttributeInputType()) && Objects.isNull(input.getInputCheckType())){
            return DataResponse.buildError("属性值录入方式是文本时，属性值数据类型必填");
        }
        return DataResponse.success();
    }

    @Override
    public boolean checkLangName(GlobalAttributeVO input) {

        if (CollectionUtils.isEmpty(input.getAttributeLangList())) {
            return false;
        }

        GlobalAttributeLangVO langVO = input.getAttributeLangList().get(0);
        List<GlobalAttributeLangPO> attributeLangPOS = globalAttributeLangAtomicService.queryLangAndAttrName(langVO.getLang(), langVO.getLangAttributeName().trim());
        if (CollectionUtils.isEmpty(attributeLangPOS)) {
            return true;
        }

        if (attributeLangPOS.size() == 1) {
            return attributeLangPOS.get(0).getAttributeId().equals(input.getId());
        }
        return false;
    }

    @Override
    public List<GlobalAttributePageVO.Response> queryAttributeList(GlobalAttributePageVO.Request reqVo) {
        List<GlobalAttributePO> attributePOS = globalAttributeAtomicService.listByCondition(reqVo);
        List<GlobalAttributePageVO.Response> list = GlobalAttributeConvert.INSTANCE.pageListPo2Vo(attributePOS);
        if (CollectionUtils.isNotEmpty(list)) {
            Set<Long> attIds = list.stream().map(BasicVO::getId).collect(Collectors.toSet());
            List<GlobalAttributeLangPO> attributeLangPOList = globalAttributeLangAtomicService.queryByAttrIds(attIds);
            Map<String, String> langMap = langManageService.queryLangMap();
            List<GlobalAttributeLangVO> attributeLangVos = GlobalAttributeLangConvert.INSTANCE.listPo2Vo(attributeLangPOList, langMap);
            Map<Long, List<GlobalAttributeLangVO>> listMap = attributeLangVos.stream().collect(Collectors.groupingBy(GlobalAttributeLangVO::getAttributeId));
            list.forEach(p -> {
                p.setAttributeLangList(listMap.get(p.getId()));
            });
        }
        return list;
    }

    @Override
    public List<GlobalAttributeValueVO> queryAttributeValueList(GlobalAttributePageVO.Request reqVo) {
        List<GlobalAttributeValuePO> globalAttributeValue = globalAttributeValueAtomicService.queryByAttrIds(Collections.singleton(reqVo.getId()));
        List<GlobalAttributeValueVO> attributeValueVos = GlobalAttributeValueConvert.INSTANCE.listPo2Vo(globalAttributeValue);
        if (CollectionUtils.isNotEmpty(attributeValueVos)) {
            Map<Long, List<GlobalAttributeValueLangPO>> valueLangMap = globalAttributeValueLangAtomicService.getAttributeValueLangMap(Collections.singleton(reqVo.getId()));
            attributeValueVos.forEach(a -> {
                List<GlobalAttributeValueLangPO> attributeValueLangPOS = valueLangMap.get(a.getId());
                a.setAttributeValueLangList(GlobalAttributeValueLangConvert.INSTANCE.listPo2Vo(attributeValueLangPOS));
            });
        }
        return attributeValueVos;
    }

    @Override
    @PFTracing
    public List<GroupPropertyVO> queryGroupPropertyVos(Long catId, String sourceCountryCode, Collection<String> targetCountryCodes, Integer dimension, String lang) {
        return this.queryGroupPropertyVos(catId,sourceCountryCode,targetCountryCodes,dimension,lang,0);
    }

    @Override
    @PFTracing
    public List<GroupPropertyVO> queryGroupPropertyVos(Long catId, String sourceCountryCode, Collection<String> targetCountryCodes, Integer dimension, String lang, Integer isExport) {
        List<GlobalCountryCategoryAttributeRelationPO> countryCategoryAttributeRelationPOS = this.queryByCountry(catId,sourceCountryCode,targetCountryCodes,isExport);
        if (CollectionUtils.isEmpty(countryCategoryAttributeRelationPOS)) {
            log.error("GlobalAttributeManageServiceImpl.countryCategoryAttributeRelationPOS is null");
            return new ArrayList<>();
        }

        Set<Long> attributeIds = countryCategoryAttributeRelationPOS.stream().map(GlobalCountryCategoryAttributeRelationPO::getAttributeId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(attributeIds)) {
            log.error("attributeIds is null");
            return new ArrayList<>();
        }
        return this.queryGroupPropertyVos(attributeIds,dimension,lang,sourceCountryCode,new ArrayList<>(targetCountryCodes));
    }

    @Override
    public List<KvVO> queryAttributeName(List<Long> ids, String lang) {
        if(StringUtils.isBlank(lang) || CollectionUtils.isEmpty(ids)){
            log.error("GlobalAttributeManageServiceImpl.queryAttributeName param is null");
            return null;
        }
        Map<Long, List<GlobalAttributeLangPO>> attributeLangMap = globalAttributeLangAtomicService.getAttributeLangMap(ids);
        if(MapUtils.isEmpty(attributeLangMap)){
            log.error("GlobalAttributeManageServiceImpl.queryAttributeName attributeLangMap is null");
            return null;
        }

        List<KvVO> results = new ArrayList<>();
        for(Long id : ids){
            KvVO result = new KvVO();
            result.setId(id);
            List<GlobalAttributeLangPO> attributeLangPOS = attributeLangMap.get(id);
            if(CollectionUtils.isEmpty(attributeLangPOS)){
                continue;
            }
            Map<String,GlobalAttributeLangPO> langPOMap = attributeLangPOS.stream().collect(Collectors.toMap(GlobalAttributeLangPO::getLang,Function.identity()));
            result.setValue(langPOMap.get(lang) == null?langPOMap.get(LANG_ZH).getLangAttributeName():langPOMap.get(lang).getLangAttributeName());
            results.add(result);
        }

        return results;
    }
    @PFTracing
    public List<GroupPropertyVO> queryGroupPropertyVos(Collection<Long> attributeIds, Integer dimension,String lang,
        String sourceCountryCode,List<String> targetCountryCodes) {
        List<GlobalAttributePO> globalAttributePOS = globalAttributeAtomicService.getValidStatusByIds(attributeIds,dimension,null);
        if (CollectionUtils.isEmpty(globalAttributePOS)) {
            log.error("GlobalAttributeManageServiceImpl.globalAttributePOS is null");
            return null;
        }

        Map<Long, List<GlobalAttributeLangPO>> attributeLangPOMap = globalAttributeLangAtomicService.getAttributeLangMap(attributeIds);
        Map<Long, List<GlobalAttributeValuePO>> attributeValuePOMap = globalAttributeValueAtomicService.getAttributeValueMap(attributeIds);
        Map<Long, List<GlobalAttributeValueLangPO>> attributeValueLangMap = globalAttributeValueLangAtomicService.getAttributeValueLangMap(attributeIds);
        return buildGroupPropertyVos(globalAttributePOS,attributeLangPOMap,attributeValuePOMap,attributeValueLangMap,
            lang,sourceCountryCode,targetCountryCodes);
    }

    @PFTracing
    private List<GlobalCountryCategoryAttributeRelationPO> queryByCountry(Long catId, String sourceCountryCode, Collection<String> targetCountryCodes,Integer isExport) {
        if(catId == null || StringUtils.isBlank(sourceCountryCode)){
            log.error("GlobalAttributeManageServiceImpl.queryByCountry param is null");
            return null;
        }

        Set<Long> catIds = new HashSet<>();
        catIds.add(Constant.DEFAULT_ALL_CATEGORY_SELECT);
        catIds.add(catId);

        List<GlobalCountryCategoryAttributeRelationPO> allRelationPOS = new ArrayList<>();

        if(CollectionUtils.isEmpty(targetCountryCodes)){
            targetCountryCodes = new ArrayList<>();
        }

        // 本土
        if(targetCountryCodes.contains(sourceCountryCode)){
            List<GlobalCountryCategoryAttributeRelationPO> relationPOS = countryCategoryAttributeRelationAtomicService.queryByCountryCode(sourceCountryCode, catIds, UseSceneEnum.LOCAL.getCode());
            if(CollectionUtils.isNotEmpty(relationPOS)){
                allRelationPOS.addAll(relationPOS);
            }
        }

        // 出口
        if(targetCountryCodes.size() > 1
                || !targetCountryCodes.contains(sourceCountryCode)
                || Integer.valueOf(YesOrNoEnum.YES.getCode()).equals(isExport)){
            List<GlobalCountryCategoryAttributeRelationPO> relationPOS = countryCategoryAttributeRelationAtomicService.queryByCountryCode(sourceCountryCode,catIds, UseSceneEnum.EXPORT.getCode());
            if(CollectionUtils.isNotEmpty(relationPOS)){
                allRelationPOS.addAll(relationPOS);
            }
        }

        // 进口
        if((targetCountryCodes.size() == 1 && !targetCountryCodes.contains(sourceCountryCode))
                || targetCountryCodes.size() > 1){
            List<String> targetCountryCodesParam = new ArrayList<>(targetCountryCodes);
            targetCountryCodesParam.remove(sourceCountryCode);
            List<GlobalCountryCategoryAttributeRelationPO> relationPOS = countryCategoryAttributeRelationAtomicService.queryByCountryCodes(targetCountryCodesParam,catIds, UseSceneEnum.IMPORT.getCode());
            if(CollectionUtils.isNotEmpty(relationPOS)){
                allRelationPOS.addAll(relationPOS);
            }
        }
        return allRelationPOS;
    }
    @PFTracing
    private List<GroupPropertyVO> buildGroupPropertyVos(List<GlobalAttributePO> attributeList, Map<Long,
        List<GlobalAttributeLangPO>> attributeLangPOMap, Map<Long, List<GlobalAttributeValuePO>> attributeValuePOMap,
        Map<Long, List<GlobalAttributeValueLangPO>> attributeValueLangMap, String lang,String sourceCountryCode,
        List<String> targetCountryCodes) {
        List<GroupPropertyVO> groupPropertyVOS = new ArrayList<>();

        // 通过ducc 篡改属性的校验值
        this.changeAttributeCheckValue(attributeList, sourceCountryCode, targetCountryCodes);

        Map<Integer, List<GlobalAttributePO>> attributePOMap = attributeList.stream().collect(Collectors.groupingBy(GlobalAttributePO::getCheckType));
        AttributeCheckTypeEnum[] checkTypeEnums = AttributeCheckTypeEnum.values();
        for (AttributeCheckTypeEnum checkTypeEnum : checkTypeEnums) {
            List<GlobalAttributePO> partAttributePOS = attributePOMap.get(checkTypeEnum.getCode());
            if (CollectionUtils.isEmpty(partAttributePOS)) {
                continue;
            }
            GroupPropertyVO groupPropertyVO = new GroupPropertyVO();
            groupPropertyVO.setRequirement(checkTypeEnum.getCode());
            groupPropertyVO.setPropertyVOS(this.buildPropertyVos(partAttributePOS,attributeLangPOMap,
                attributeValuePOMap,attributeValueLangMap,lang,checkTypeEnum,sourceCountryCode,targetCountryCodes));
            groupPropertyVOS.add(groupPropertyVO);
        }
        return groupPropertyVOS;
    }

    /**
     * <a href="http://jagile.jd.com/demands/view/JL3R4IV4?demandId=3221326">变更跨境属性的校验方式</a>
     */
    @PFTracing
    private void changeAttributeCheckValue(List<GlobalAttributePO> attributeList, String sourceCountryCode, List<String> targetCountryCodes) {
        List<CrossAttributeChangeConfig> configs = operDuccConfig.getCrossAttributeChangeConfig();
        Map<Long, CrossAttributeChangeConfig> configMap = configs.stream()
                .collect(Collectors.toMap(CrossAttributeChangeConfig::getAttributeId, Function.identity(), (k1, k2) -> k1));

        for (GlobalAttributePO item : attributeList) {
            CrossAttributeChangeConfig config = configMap.get(item.getId());
            if (config == null) {
                continue;
            }
            try {
                if (config.anyMatch(0, item.getId(), sourceCountryCode, targetCountryCodes)) {
                    Integer targetCheckType = config.getTargetCheckType();
                    if (!AttributeCheckTypeEnum.exist(targetCheckType)) {
                        log.error("CrossAttributeChangeConfig is error, config:{}", JSONObject.toJSONString(config));
                        continue;

                    }
                    item.setCheckType(targetCheckType);
                }
            } catch (Exception e) {
                log.error("变更跨境属性ducc配置错误. ", e);
            }
        }

    }
    @PFTracing
    private List<PropertyVO> buildPropertyVos(List<GlobalAttributePO> attributeList, Map<Long,
        List<GlobalAttributeLangPO>> attributeLangPOMap, Map<Long, List<GlobalAttributeValuePO>> attributeValuePOMap,
        Map<Long, List<GlobalAttributeValueLangPO>> attributeValueLangMap, String lang,
        AttributeCheckTypeEnum checkTypeEnum,String sourceCountryCode,List<String> targetCountryCodes) {
        List<PropertyVO> propertyVOS = new ArrayList<>();
        for (GlobalAttributePO attributePO : attributeList) {
            PropertyVO propertyVO = buildPropertyVO(attributePO, attributeLangPOMap, attributeValuePOMap,
                attributeValueLangMap, lang, checkTypeEnum, sourceCountryCode,targetCountryCodes);
            propertyVOS.add(propertyVO);
        }

        return propertyVOS.stream().filter(Objects::nonNull).sorted(Comparator.comparing(PropertyVO::getSort)).collect(Collectors.toList());
    }

    private PropertyVO buildPropertyVO(GlobalAttributePO attributePO,
        Map<Long, List<GlobalAttributeLangPO>> attributeLangPOMap,
        Map<Long, List<GlobalAttributeValuePO>> attributeValuePOMap,
        Map<Long, List<GlobalAttributeValueLangPO>> attributeValueLangMap, String lang,
        AttributeCheckTypeEnum checkTypeEnum,String sourceCountryCode,List<String> targetCountryCodes) {
        PropertyVO propertyVO = new PropertyVO();
        propertyVO.setAttributeId(attributePO.getId());
        propertyVO.setSort(attributePO.getSort().intValue());
        propertyVO.setAttributeInputType(attributePO.getAttributeInputType());
        propertyVO.setRequired(attributePO.getCheckType().equals(checkTypeEnum.getCode()) ? Boolean.TRUE : Boolean.FALSE);
        propertyVO.setInputCheckType(attributePO.getInputCheckType());
        propertyVO.setShowSupplier(attributePO.getShowSupplier());

        List<GlobalAttributeLangPO> langPOS = attributeLangPOMap.get(attributePO.getId());
        Map<String, String> langAttributeNameMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(langPOS)){
            langAttributeNameMap.putAll(langPOS.stream().collect(Collectors.toMap(GlobalAttributeLangPO::getLang, GlobalAttributeLangPO::getLangAttributeName)));
        }
        propertyVO.setAttributeName(StringUtils.isNotBlank(langAttributeNameMap.get(lang)) ? langAttributeNameMap.get(lang) : langAttributeNameMap.get(LANG_ZH));

        List<GlobalAttributeValuePO> attributeValuePOS = attributeValuePOMap.get(attributePO.getId());

        List<PropertyValueVO> propertyValueVOList = new ArrayList<>();
        if(AttributeInputTypeEnum.TEXT.getCode().equals(attributePO.getAttributeInputType())){
            for (GlobalAttributeValuePO valuePO : attributeValuePOS) {
                propertyValueVOList = sortValueByLang(attributePO,valuePO, attributeValueLangMap ,sourceCountryCode, targetCountryCodes);
            }
        } else {
            propertyValueVOList = buildPropertyValueVOList(attributeValuePOS, attributeValueLangMap, lang);
        }


        propertyVO.setPropertyValueVOList(propertyValueVOList);
        return propertyVO;
    }

    private List<PropertyValueVO> buildPropertyValueVOList(List<GlobalAttributeValuePO> attributeValuePOS, Map<Long, List<GlobalAttributeValueLangPO>> attributeValueLangMap, String lang) {
        List<PropertyValueVO> propertyValueVOList = new ArrayList<>();
        if(CollectionUtils.isEmpty(attributeValuePOS)){
            return null;
        }

        for (GlobalAttributeValuePO valuePO : attributeValuePOS) {
            List<GlobalAttributeValueLangPO> attributeValueLangPOS = attributeValueLangMap.get(valuePO.getId());
            Map<String, String> attributeValueLangPOMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(attributeValueLangPOS)) {
                attributeValueLangPOMap.putAll(attributeValueLangPOS.stream().collect(Collectors.toMap(GlobalAttributeValueLangPO::getLang, GlobalAttributeValueLangPO::getLangAttributeValueName)));
            }

            PropertyValueVO propertyValueVO = new PropertyValueVO();
            propertyValueVO.setAttributeId(valuePO.getAttributeId());
            propertyValueVO.setAttributeValueId(valuePO.getId());
            propertyValueVO.setAttributeValueName(StringUtils.isNotBlank(attributeValueLangPOMap.get(lang)) ? attributeValueLangPOMap.get(lang) : attributeValueLangPOMap.get(LANG_ZH));

            propertyValueVOList.add(propertyValueVO);
        }
        return propertyValueVOList;
    }

    /**
     * 根据语言代码对属性值进行排序，并补充缺失的语言属性值。
     * @param valuePO 全局属性值对象
     * @param attributeValueLangMap 属性值语言映射
     * @param sourceCountryCode 源国家代码
     * @return 排序后的属性值列表
     */
    private List<PropertyValueVO> sortValueByLang(GlobalAttributePO attributePO,GlobalAttributeValuePO valuePO, Map<Long,
        List<GlobalAttributeValueLangPO>> attributeValueLangMap, String sourceCountryCode,List<String> targetCountryCodes) {
        List<PropertyValueVO> propertyValueVOList;
        List<GlobalAttributeValueLangPO> langList = attributeValueLangMap.get(valuePO.getId());
        List<String> LANG_LIST = Arrays.asList(LANG_ZH);

//        List<String> LANG_LIST = new ArrayList<>();
//        if(CollectionUtils.isEmpty(targetCountryCodes)){
//            LANG_LIST = countryLangManageService.getLangCodeByCountryCode(sourceCountryCode);
//        } else {
//            Set<String> countryLangLists = new HashSet<>();
//            for(String countryCode : targetCountryCodes){
//                List<String> langCodes = countryLangManageService.getLangCodeByCountryCode(countryCode);
//                if(CollectionUtils.isNotEmpty(langCodes)){
//                    countryLangLists.addAll(langCodes);
//                }
//            }
//            if(CollectionUtils.isNotEmpty(countryLangLists)){
//                LANG_LIST = new ArrayList<>(countryLangLists);
//            }
//        }

        if(CollectionUtils.isNotEmpty(langList)){
            List<String> finalLANG_LIST = LANG_LIST;
            langList = langList.stream().filter(item-> finalLANG_LIST.contains(item.getLang())).collect(Collectors.toList());
        }
        Set<String> langSet = langList.stream().filter(Objects::nonNull).map(GlobalAttributeValueLangPO::getLang).collect(Collectors.toSet());
        Map<String, Integer> sortLangMap = Maps.newHashMap();
        for (int i = 0; i < LANG_LIST.size(); i++) {
            String lang = LANG_LIST.get(i);
            sortLangMap.put(lang, i);
            // 多语言扩展属性值缺少时，补充缺失语种
            if (!langSet.contains(lang)) {
                GlobalAttributeValueLangPO valueLangVO = new GlobalAttributeValueLangPO();
                valueLangVO.setLang(lang);
                valueLangVO.setAttributeId(valueLangVO.getAttributeId());
                langList.add(valueLangVO);
            }
        }
        propertyValueVOList = langList.stream().filter(Objects::nonNull).map(langVo -> {
            PropertyValueVO valueVO = new PropertyValueVO();
            valueVO.setAttributeId(valuePO.getAttributeId());
            valueVO.setAttributeValueId(valuePO.getId());
            valueVO.setLang(langVo.getLang());
            valueVO.setLangName(langManageService.getLangNameByLangCode(langVo.getLang(), LangContextHolder.get()));
            // 文本类型扩展属性值默认给提示字段
            valueVO.setAttributeValueName(null);
            if(StringUtils.isNotBlank(langVo.getLangAttributeValueName())){
                valueVO.setPlaceholderValue(langVo.getLangAttributeValueName());
            } else {
                valueVO.setPlaceholderValue(attributePO.getAttributeDefinition());
            }
            valueVO.setSort(sortLangMap.get(langVo.getLang()) != null?sortLangMap.get(langVo.getLang()):0);
            return valueVO;
        }).sorted(Comparator.comparing(PropertyValueVO::getSort)).collect(Collectors.toList());
        return propertyValueVOList;
    }

    @Override
    public List<GlobalAttributeResVO> queryAttributeListByCatId(GlobalAttributeQueryReqVO queryReqVO) {

        List<GroupPropertyVO> groupPropertyVoList = this.queryGroupPropertyVos(queryReqVO.getCategoryId(), queryReqVO.getSourceCountryCode(), queryReqVO.getTargetCountryCodes(), queryReqVO.getAttributeDimension(), StringUtils.isNotBlank(queryReqVO.getLang()) ? queryReqVO.getLang() : LANG_ZH, queryReqVO.getExport());

        if (CollectionUtils.isEmpty(groupPropertyVoList)) {
            return Collections.emptyList();
        }

        List<GlobalAttributeResVO> globalAttributeResVOList = Lists.newArrayList();

        for (GroupPropertyVO groupPropertyVO : groupPropertyVoList) {
            if (CollectionUtils.isEmpty(groupPropertyVO.getPropertyVOS())) {
                continue;
            }
            // 跨境属性转换
            for (PropertyVO propertyVO : groupPropertyVO.getPropertyVOS()) {
                GlobalAttributeResVO resVO = AttributeConvert.INSTANCE.propertyVo2GlobalResVo(propertyVO);
                resVO.setRequirement(groupPropertyVO.getRequirement());
                globalAttributeResVOList.add(resVO);
            }
        }
        return globalAttributeResVOList;
    }

    @Override
    public List<GlobalAttributeValueVO> queryAttributeValueList(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            log.error("GlobalAttributeManageServiceImpl.queryAttributeValueList ids is null");
            return null;
        }
        List<GlobalAttributeValuePO> globalAttributeValue = globalAttributeValueAtomicService.queryByAttrValueIds(ids);
        List<GlobalAttributeValueVO> attributeValueVos = GlobalAttributeValueConvert.INSTANCE.listPo2Vo(globalAttributeValue);
        if (CollectionUtils.isNotEmpty(attributeValueVos)) {
            Map<Long, List<GlobalAttributeValueLangPO>> valueLangMap = globalAttributeValueLangAtomicService.getAttributeValueIdLangMap(ids);
            attributeValueVos.forEach(a -> {
                List<GlobalAttributeValueLangPO> attributeValueLangPOS = valueLangMap.get(a.getId());
                a.setAttributeValueLangList(GlobalAttributeValueLangConvert.INSTANCE.listPo2Vo(attributeValueLangPOS));
            });
        }
        return attributeValueVos;
    }
}
