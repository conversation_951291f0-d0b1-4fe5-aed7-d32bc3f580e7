package com.jdi.isc.product.soa.service.atomic.specialAttr;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.specialAttr.po.SpecialAttrValuePO;
import com.jdi.isc.product.soa.repository.mapper.specialAttr.SpecialAttrValueBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;

import java.util.stream.Collectors;

/**
 * @Description: 商品特殊属性原子服务
 * @Author: zhangjin176
 * @Date: 2024/12/11 09:55
 **/
@Slf4j
@Service
public class SpecialAttrValueAtomicService extends ServiceImpl<SpecialAttrValueBaseMapper, SpecialAttrValuePO> {


    public SpecialAttrValuePO getSpecialAttrValueByAttributeKeyIdAndValue(Long attributeKey, String attributeValue) {
       return this.getBaseMapper().selectOne(new LambdaQueryWrapper<SpecialAttrValuePO>()
               .eq(SpecialAttrValuePO::getAttributeKeyId, attributeKey)
               .eq(SpecialAttrValuePO::getAttributeValue, attributeValue)
               .eq(SpecialAttrValuePO::getYn, YnEnum.YES.getCode()));
    }


    /**
     * 根据一组 ID 查询 SpecialAttrPO 对象列表。
     * @param ids 要查询的 SpecialAttrPO 对象的 ID 集合。
     * @return 符合条件的 SpecialAttrPO 对象列表。
     */
    public List<SpecialAttrValuePO> queryByIds(Set<Long> ids){
        if(CollectionUtils.isEmpty(ids) ){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SpecialAttrValuePO> wrapper = Wrappers.<SpecialAttrValuePO>lambdaQuery()
            .in(CollectionUtils.isNotEmpty(ids),SpecialAttrValuePO::getId,ids)
            .eq(SpecialAttrValuePO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }



    public Map<Long, String> querySpecialAttrList() {
        LambdaQueryWrapper<SpecialAttrValuePO> langWrapper = Wrappers.<SpecialAttrValuePO>lambdaQuery()
                .select(SpecialAttrValuePO::getId, SpecialAttrValuePO::getAttributeValueName)
                .eq(SpecialAttrValuePO::getYn,  YnEnum.YES.getCode());
        List<SpecialAttrValuePO> list = baseMapper.selectList(langWrapper);
        return list.stream()
                .collect(Collectors.toMap(SpecialAttrValuePO::getId, SpecialAttrValuePO::getAttributeValueName));
    }

    public List<SpecialAttrValuePO> getAll() {
        LambdaQueryWrapper<SpecialAttrValuePO> wrapper = Wrappers.<SpecialAttrValuePO>lambdaQuery()
                .eq(SpecialAttrValuePO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    /**
     * 根据 ID 集查询属性值映射
     * @param ids 属性值 ID 集合
     * @return 属性值 ID 到值的映射
     */
    public Map<Long, String> queryAttrValueMapByIds(Set<Long> ids) {
        if(CollectionUtils.isEmpty(ids) ){
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<SpecialAttrValuePO> wrapper = Wrappers.<SpecialAttrValuePO>lambdaQuery()
                .select(SpecialAttrValuePO::getId,SpecialAttrValuePO::getAttributeValue)
                .in(CollectionUtils.isNotEmpty(ids),SpecialAttrValuePO::getId,ids)
                .eq(SpecialAttrValuePO::getYn, YnEnum.YES.getCode());
        List<SpecialAttrValuePO> specialAttrValuePOList = super.list(wrapper);

        return Optional.ofNullable(specialAttrValuePOList)
                .orElseGet(ArrayList::new)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(SpecialAttrValuePO::getId, SpecialAttrValuePO::getAttributeValue));
    }
}