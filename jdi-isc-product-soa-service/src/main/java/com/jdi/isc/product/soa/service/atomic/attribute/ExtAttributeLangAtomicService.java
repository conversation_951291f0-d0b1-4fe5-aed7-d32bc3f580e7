package com.jdi.isc.product.soa.service.atomic.attribute;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.attribute.biz.ExtAttributeLangQueryVO;
import com.jdi.isc.product.soa.domain.attribute.biz.ExtAttributeLangTransformVO;
import com.jdi.isc.product.soa.domain.attribute.po.ExtAttributeLangPO;
import com.jdi.isc.product.soa.domain.attribute.po.ExtAttributeTempPO;
import com.jdi.isc.product.soa.repository.jed.mapper.attribute.ExtAttributeLangBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 属性原子服务
 * <AUTHOR>
 * @date 20250219
 **/
@Slf4j
@Service
public class ExtAttributeLangAtomicService extends ServiceImpl<ExtAttributeLangBaseMapper, ExtAttributeLangPO> {

    public List<ExtAttributeLangPO> list(ExtAttributeLangPO po){
        LambdaQueryWrapper<ExtAttributeLangPO> wrapper = Wrappers.<ExtAttributeLangPO>lambdaQuery()
                .eq(StringUtils.isNotBlank(po.getLang()),ExtAttributeLangPO::getLang, po.getLang())
                .eq(StringUtils.isNotBlank(po.getExtAttributeName()),ExtAttributeLangPO::getExtAttributeName, po.getExtAttributeName())
                .eq(StringUtils.isNotBlank(po.getGroupId()),ExtAttributeLangPO::getGroupId, po.getGroupId())
                .eq(ExtAttributeLangPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }
    @PFTracing
    public boolean exist(ExtAttributeLangPO po){
        LambdaQueryWrapper<ExtAttributeLangPO> wrapper = Wrappers.<ExtAttributeLangPO>lambdaQuery()
                .select(ExtAttributeLangPO::getId)
                .eq(StringUtils.isNotBlank(po.getLang()),ExtAttributeLangPO::getLang, po.getLang())
                .eq(StringUtils.isNotBlank(po.getExtAttributeName()),ExtAttributeLangPO::getExtAttributeName, po.getExtAttributeName())
                .eq(StringUtils.isNotBlank(po.getGroupId()),ExtAttributeLangPO::getGroupId, po.getGroupId())
                .eq(ExtAttributeLangPO::getYn, YnEnum.YES.getCode());

        return CollectionUtils.isNotEmpty(super.list(wrapper));
    }

    public List<ExtAttributeLangPO> batchExist(String lang,Set<String> toCheck) {
        LambdaQueryWrapper<ExtAttributeLangPO> wrapper = Wrappers.<ExtAttributeLangPO>lambdaQuery()
                .eq(ExtAttributeLangPO::getLang, lang)
                .in(ExtAttributeLangPO::getExtAttributeName, toCheck)
                .eq(ExtAttributeLangPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }
    @PFTracing
    public ExtAttributeLangTransformVO search(Set<String> keyword, Set<String> langList){
        ExtAttributeLangTransformVO result = new ExtAttributeLangTransformVO();
        //先用中文关键字命中组id
        LambdaQueryWrapper<ExtAttributeLangPO> wrapper = Wrappers.<ExtAttributeLangPO>lambdaQuery()
            .eq(ExtAttributeLangPO::getLang, LangConstant.LANG_ZH)
            .in(ExtAttributeLangPO::getExtAttributeName, keyword)
            .eq(ExtAttributeLangPO::getYn, YnEnum.YES.getCode());
        List<ExtAttributeLangPO> zhList = super.list(wrapper);
        //使用组id&多语言查询
        if(CollectionUtils.isNotEmpty(zhList)){
            langList.add(LangConstant.LANG_ZH);
            LambdaQueryWrapper<ExtAttributeLangPO> wrapper2 = Wrappers.<ExtAttributeLangPO>lambdaQuery()
                .in(ExtAttributeLangPO::getGroupId, zhList.stream().map(ExtAttributeLangPO::getGroupId).collect(Collectors.toSet()))
                .in(ExtAttributeLangPO::getLang, langList)
                .eq(ExtAttributeLangPO::getYn, YnEnum.YES.getCode());
            //目标语种扩展属性
            result.setTargetList(super.list(wrapper2));
            //命中目标语种扩展属性对应的中文属性
            result.setMatchStr(zhList.stream().map(ExtAttributeLangPO::getExtAttributeName).collect(Collectors.toSet()));
        } else {
            result.setMatchStr(Collections.emptySet());
        }
        return result;
    }

    public Page<ExtAttributeLangPO> page(ExtAttributeLangQueryVO input){
        Page<ExtAttributeLangPO> page = new Page<>(Objects.isNull(input.getIndex()) ? 1 : input.getIndex(), Objects.isNull(input.getSize()) ? 20 : input.getSize());
        LambdaQueryWrapper<ExtAttributeLangPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(StringUtils.isNotBlank(input.getLang()), ExtAttributeLangPO::getLang, input.getLang())
                .eq(StringUtils.isNotBlank(input.getGroupId()), ExtAttributeLangPO::getGroupId, input.getGroupId())
                .like(StringUtils.isNotBlank(input.getExtAttributeName()), ExtAttributeLangPO::getExtAttributeName, input.getExtAttributeName())
                .eq(ExtAttributeLangPO::getYn, YnEnum.YES.getCode())
                .orderByDesc(ExtAttributeLangPO::getUpdateTime);
        return super.page(page, queryWrapper);
    }

    public List<ExtAttributeTempPO> listLackItem() {
        return this.baseMapper.getLackItem();
    }

    public List<ExtAttributeTempPO> getDulItem() {
        return this.baseMapper.getDulItem();
    }

    public boolean deleteByGroupId(String groupId) {
        LambdaQueryWrapper<ExtAttributeLangPO> wrapper = Wrappers.<ExtAttributeLangPO>lambdaQuery()
                .eq(ExtAttributeLangPO::getGroupId, groupId)
                .eq(ExtAttributeLangPO::getYn, YnEnum.YES.getCode());
        return remove(wrapper);
    }
}
