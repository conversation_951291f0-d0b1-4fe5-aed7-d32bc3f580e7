package com.jdi.isc.product.soa.service.atomic.sku;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.domain.sku.biz.SkuPageReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuResVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.repository.mapper.sku.SkuBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SkuAtomicService extends ServiceImpl<SkuBaseMapper, SkuPO> {


    /**
     * 查询sku信息
     * @param skuIds
     * @return
     */
    public Map<Long, SkuPO> batchQuerySkuPO(Set<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return new HashMap<>(1);
        }
        List<SkuPO> skuPOList = this.queryBySkuIds(skuIds);
        if(CollectionUtils.isEmpty(skuPOList)){
            return null;
        }
        return skuPOList.stream().collect(Collectors.toMap(SkuPO::getSkuId, Function.identity()));
    }

    /**
     * 查询sku信息
     * @param skuIds
     * @return
     */
    public List<SkuPO> queryBySkuIds(Set<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            log.error("SkuAtomicService.queryBySkuIds skuIds is null");
            return null;
        }
        LambdaQueryWrapper<SkuPO> wrapper = new LambdaQueryWrapper<SkuPO>()
                .in(SkuPO::getSkuId, skuIds)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(wrapper);
    }

    /**
     * 根据skuId和有效状态标识查询SKU信息
     */
    public SkuPO getBySkuIdsAndYn(Long skuId, Integer yn) {
        if (skuId == null) {
            return null;
        }
        List<SkuPO> list = listBySkuIdsAndYn(Lists.newArrayList(skuId), yn);

        if (CollectionUtils.isNotEmpty(list)) {
            SkuPO sku = list.get(0);
            if (sku != null && sku.getYn() == 0) {
                log.warn("sku不存在或者已归档. skuId={}", skuId);
            }
            return sku;
        }

        return null;
    }

    /**
     * 根据SKU ID集合和有效状态标识查询SKU信息列表
     */
    public List<SkuPO> listBySkuIdsAndYn(Collection<Long> skuIds, Integer yn) {
        if (CollectionUtils.isEmpty(skuIds)) {
            log.error("SkuAtomicService.listBySkuIdsAndYn skuIds is null");
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<SkuPO> wrapper = new LambdaQueryWrapper<SkuPO>()
                .in(SkuPO::getSkuId, skuIds)
                .eq(yn != null, SkuPO::getYn, yn);
        return super.getBaseMapper().selectList(wrapper);
    }

    /**
     * 根据商品ID集合和有效状态标识获取商品信息映射表
     */
    public Map<Long, SkuPO> mapBySkuIdsAndYn(Collection<Long> skuIds, Integer yn) {
        List<SkuPO> list = this.listBySkuIdsAndYn(skuIds, yn);
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }

        return list.stream().collect(Collectors.toMap(SkuPO::getSkuId, Function.identity(), (k1, k2) -> k1));
    }


    /**
     * 批量查询sku和京东sku映射
     * @param skuIds
     * @return
     */
    @PFTracing
    public Map<Long,Long> batchQueryJdSku(Set<Long> skuIds){
        if (CollectionUtils.isEmpty(skuIds)){
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<SkuPO> wrapper = new LambdaQueryWrapper<SkuPO>()
                .in(SkuPO::getSkuId, skuIds)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> skuPOList = super.getBaseMapper().selectList(wrapper);
        return skuPOList.stream().filter(po-> Objects.nonNull(po.getJdSkuId())).collect(Collectors.toMap(SkuPO::getSkuId,SkuPO::getJdSkuId));
    }

    public List<SkuResVO> listSearch(SkuPageReqVO reqVO){
        reqVO.setOffset((reqVO.getIndex()-1)*reqVO.getSize());
        return super.getBaseMapper().listSearch(reqVO);
    }

    public long listSearchTotal(SkuPageReqVO reqVO) {
        return super.getBaseMapper().listSearchTotal(reqVO);
    }


    public SkuPO getValidById(Long skuId) {
        if (skuId == null) {
            throw new ProductBizException("skuId不存在");
        }
        SkuPO sku = this.getSkuPoBySkuId(skuId);
        if (sku == null) {
            throw new ProductBizException("sku不存在 [%s]", skuId);
        }
        return sku;
    }

    /**
     * 单个查询sku
     *
     * @param skuId
     * @return
     */
    @PFTracing
    public SkuPO getSkuPoBySkuId(Long skuId) {
        return super.getBaseMapper().selectOne(new LambdaQueryWrapper<SkuPO>().eq(SkuPO::getSkuId, skuId).eq(SkuPO::getYn, YnEnum.YES.getCode()));
    }

    public SkuPO getAllSkuPoBySkuId(Long skuId) {
        return super.getBaseMapper().selectOne(new LambdaQueryWrapper<SkuPO>().eq(SkuPO::getSkuId, skuId));
    }

    /**
     * 单个查询sku
     *
     * @param jdSkuId
     * @return
     */
    public SkuPO getSkuPoByJdSkuId(Long jdSkuId) {
        return super.getBaseMapper().selectOne(new LambdaQueryWrapper<SkuPO>().eq(SkuPO::getJdSkuId, jdSkuId).eq(SkuPO::getYn, YnEnum.YES.getCode()));
    }

    /**
     * 原则上只有一个, 部分sku有脏数据.
     *
     * @param jdSkuId the jd sku id
     * @return the list
     */
    public List<SkuPO> listSkuPoByJdSkuId(Long jdSkuId) {
        return super.getBaseMapper().selectList(new LambdaQueryWrapper<SkuPO>().eq(SkuPO::getJdSkuId, jdSkuId).eq(SkuPO::getYn, YnEnum.YES.getCode()));
    }

    /**
     * 多sku查询
     */
    @PFTracing
    public Map<Long, SkuPO> getSkuMap(Set<Long> skuIds) {
        if (CollectionUtils.isNotEmpty(skuIds)) {
            List<SkuPO> listSku = super.getBaseMapper().selectList(new LambdaQueryWrapper<SkuPO>()
                    .in(SkuPO::getSkuId, skuIds)
                    .eq(SkuPO::getYn, YnEnum.YES.getCode()));
            if (CollectionUtils.isNotEmpty(listSku)) {
                return listSku.stream().collect(Collectors.toMap(SkuPO::getSkuId, Function.identity()));
            }
        }
        return new HashMap<>();
    }

    /**
     * 根据SpuId查询sku列表
     *
     * @param spuId
     * @return
     */
    public List<SkuPO> selectSkuPosBySpuId(Long spuId) {
        LambdaQueryWrapper<SkuPO> skuQueryWrapper = new LambdaQueryWrapper<SkuPO>().eq(SkuPO::getSpuId, spuId).eq(SkuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(skuQueryWrapper);
    }

    /**
     * 根据spuId查询sku个数
     *
     * @param spuId
     * @return
     */
    public long getSkuCountBySpuId(Long spuId) {
        LambdaQueryWrapper<SkuPO> skuQueryWrapper = new LambdaQueryWrapper<SkuPO>().eq(SkuPO::getSpuId, spuId).eq(SkuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectCount(skuQueryWrapper);
    }


    /**
     * 批量查询spuIc下sku个数
     *
     * @param spuIds
     * @return
     */
    public Map<Long, Integer> getBatchSkuCountBySpuIds(List<Long> spuIds) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(spuIds)) {
            return Collections.emptyMap();
        }
        QueryWrapper<SkuPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("spu_id as spuId, count(*) as count").in(org.apache.commons.collections4.CollectionUtils.isNotEmpty(spuIds), "spu_id", spuIds).eq("yn", YnEnum.YES.getCode()).groupBy("spu_id");
        List<Map<String, Object>> skuContList = super.getBaseMapper().selectMaps(queryWrapper);

        Map<Long, Integer> resultMap = Maps.newHashMap();
        for (Map<String, Object> map : skuContList) {
            resultMap.put(MapUtils.getLong(map, "spuId"), MapUtils.getInteger(map, "count"));
        }
        return resultMap;
    }

    public List<SkuPO> querySkuPoByJdSkuIds(List<Long> jdSkuIds) {
        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>()
            .in(SkuPO::getJdSkuId, jdSkuIds)
            .eq(SkuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(queryWrapper);
    }

    public Map<Long, Long> querySkuRelationByJdSkuIds(List<Long> jdSkuIds) {
        List<SkuPO> skuPoList = this.querySkuPoByJdSkuIds(jdSkuIds);

        return Optional.ofNullable(skuPoList).orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(SkuPO::getJdSkuId, SkuPO::getSkuId, (sku1, sku2) -> sku1));
    }


    /**
     * 根据spuIds查询sku列表
     *
     * @param spuIds spuId的集合
     * @return 返回一个Map，key为spuId，value为对应的sku列表
     * @throws NullPointerException 如果skuPoList为null时抛出
     */
    @PFTracing
    public Map<Long, List<SkuPO>> querySkuListBySpuIds(Set<Long> spuIds) {
        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>()
                .in(SkuPO::getSpuId, spuIds).eq(SkuPO::getYn, YnEnum.YES.getCode());

        List<SkuPO> skuPoList = super.getBaseMapper().selectList(queryWrapper);
        // 按spuId分组
        return Optional.ofNullable(skuPoList).orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(SkuPO::getSpuId));
    }


    public Page<SkuPO> pageSkuPo(long index, long size) {
        Page<SkuPO> page = new Page<>(index, size);
        LambdaQueryWrapper<SkuPO> queryWrapper = Wrappers.lambdaQuery(SkuPO.class).orderByAsc(SkuPO::getSkuId);
        return super.page(page, queryWrapper);
    }


    /**
     * 单个查询sku
     */
    public List<SkuPO> getSkuPoBySpuId(Long spuId) {
        List<SkuPO> skuPOList = super.getBaseMapper().selectList(new LambdaQueryWrapper<SkuPO>().eq(SkuPO::getSpuId, spuId).eq(SkuPO::getYn, YnEnum.YES.getCode()));
        if (CollectionUtils.isEmpty(skuPOList)) {
            return null;
        }

        return skuPOList;
    }

    public List<SkuPO> querySkuListBySpuIds(List<Long> spuIds) {
        if (CollectionUtils.isEmpty(spuIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>();
        queryWrapper.select(SkuPO::getSpuId,SkuPO::getSkuId, SkuPO::getSkuStatus)
            .in(SkuPO::getSpuId, spuIds)
            .eq(SkuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(queryWrapper);
    }


    /**
     * 根据品牌ID集合查询SKU列表
     */
    public List<SkuPO> querySkuListByBrandId(Collection<Long> brandIds){
        return super.getBaseMapper().querySkuListByBrandId(brandIds);
    }

    public Map<String,Set<Long>> queryJdVendorCodeAndSpuIds(List<Long> spuIds){
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(spuIds)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<SkuPO> queryWrapper = Wrappers.lambdaQuery(SkuPO.class).select(SkuPO::getSpuId, SkuPO::getJdVendorCode).in(SkuPO::getSpuId, spuIds).eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> skuPoList = this.getBaseMapper().selectList(queryWrapper);

        return Optional.ofNullable(skuPoList).orElseGet(ArrayList::new)
                .stream().filter(sku -> sku.getJdVendorCode() != null).collect(Collectors.groupingBy(SkuPO::getJdVendorCode, Collectors.mapping(SkuPO::getSpuId,Collectors.toSet())));
    }
    @PFTracing
    public Map<Long,Long> getSkuSpuMapBySkuIds(Collection<Long> skuIds) {
        LambdaQueryWrapper<SkuPO> queryWrapper = Wrappers.lambdaQuery(SkuPO.class)
            .select(SkuPO::getSpuId, SkuPO::getSkuId).in(SkuPO::getSkuId, skuIds)
            //.eq(SkuPO::getSourceCountryCode, CountryConstant.COUNTRY_ZH) //不过滤只有货源地是中国的商品了。
            .eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> skuPoList = this.getBaseMapper().selectList(queryWrapper);
        return Optional.ofNullable(skuPoList).orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(SkuPO::getSkuId,SkuPO::getSpuId));
    }
    @PFTracing
    public List<SkuPO> getSkuListBySpuIds(Collection<Long> spuIds){
        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>()
            .in(SkuPO::getSpuId, spuIds)
            .eq(SkuPO::getYn, YnEnum.YES.getCode());
        return this.list(queryWrapper);
    }

    /**
     * 查询sku信息
     * @param skuIds
     * @return
     */
    public List<SkuPO> queryInfoForPriceCompare(Set<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            log.error("SkuAtomicService.queryInfoForPriceCompare skuIds is null");
            return null;
        }
        LambdaQueryWrapper<SkuPO> wrapper = new LambdaQueryWrapper<SkuPO>()
                .select(SkuPO::getSpuId, SkuPO::getSkuId, SkuPO::getJdSkuId, SkuPO::getVendorCode)
                .in(SkuPO::getSkuId, skuIds)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(wrapper);
    }


    /**
     * 根据给定的 SKU IDs 和指定的列名查询 SKU 部分列信息。
     * @param skuIds SKU 的 ID 列表
     * @return 满足条件的 SKU 列表
     */
    public List<SkuPO> querySkuPartialColumnBySkuIds(Set<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            log.error("SkuAtomicService.querySkuPartBySkuIds skuIds is null");
            return Collections.emptyList();
        }

        LambdaQueryWrapper<SkuPO> wrapper = new LambdaQueryWrapper<SkuPO>()
                .select(SkuPO::getSkuId, SkuPO::getSourceCountryCode, SkuPO::getHsCode, SkuPO::getVendorCode,SkuPO::getJdCatId)
                .in(SkuPO::getSkuId, skuIds)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(wrapper);
    }

    public List<SkuPO> querySkuPoMapByJdSkuIds(Set<Long> jdSkuIds) {
        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>()
                .in(SkuPO::getJdSkuId, jdSkuIds)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(queryWrapper);
    }

    /** 根据京东SKUId查询国际sku与jdsku间的map */
    public Map<Long,Long> querySkuMapByJdSkuIds(Set<Long> jdSkuIds) {
        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>()
                .in(SkuPO::getJdSkuId, jdSkuIds)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> res = super.getBaseMapper().selectList(queryWrapper);
        return Optional.ofNullable(res).orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(SkuPO::getSkuId,SkuPO::getJdSkuId));
    }

    /** 根据国际SKUId查询国际sku与jdsku间的map */
    public Map<Long,Long> querySkuMapByIscSkuIds(Set<Long> iscSkuIds) {
        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>()
                .in(SkuPO::getSkuId, iscSkuIds)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> res = super.getBaseMapper().selectList(queryWrapper);
        return Optional.ofNullable(res).orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(SkuPO::getSkuId,SkuPO::getJdSkuId));
    }


    /** 根据京东SKUId查询国际jdSku与国际sku间的map */
    public Map<Long,Long> queryJdSkuMapByJdSkuIds(Set<Long> jdSkuIds) {
        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>()
                .in(SkuPO::getJdSkuId, jdSkuIds)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> res = super.getBaseMapper().selectList(queryWrapper);
        return Optional.ofNullable(res).orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(SkuPO::getJdSkuId,SkuPO::getSkuId));
    }

    @PFTracing
    public Page<SkuPO> pageSkuByCateAndCountry(Set<Long> catIds,String sourceCountryCode, Integer index, Integer size){
        Page<SkuPO> res = null;
        try {
            Page<SkuPO> page = new Page<>(index,size);
            LambdaQueryWrapper<SkuPO> wrapper = Wrappers.<SkuPO>lambdaQuery()
                    .select(SkuPO::getSkuId,SkuPO::getCreateTime)
                    .in(CollectionUtils.isNotEmpty(catIds),SkuPO::getJdCatId, catIds)
                    .eq(SkuPO::getSourceCountryCode, sourceCountryCode)
                    .eq(SkuPO::getYn, YnEnum.YES.getCode())
                    .orderByAsc(SkuPO::getCreateTime);
            res = super.page(page, wrapper);
        }finally {
            log.info("SkuAtomicService.pageSkuByCateAndCountry catIds:{} ,sourceCountryCode:{}, index:{}, size:{}, res:{}" ,  catIds,sourceCountryCode,index,size, res!=null?res.getSize():null);
        }
        return res;
    }

    public List<SkuPO> queryCnSkuByCatAndOffset(Set<Long> catIds,Integer offset,Integer pageSize,String sourceCountryCode) {
        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>()
                .select(SkuPO::getSkuId,SkuPO::getCreateTime)
                .in(CollectionUtils.isNotEmpty(catIds),SkuPO::getJdCatId, catIds)
                .eq(SkuPO::getSourceCountryCode, sourceCountryCode)
                .eq(SkuPO::getYn, YnEnum.YES.getCode())
                .orderByAsc(SkuPO::getCreateTime)
                .last("LIMIT " + offset+","+pageSize);
        return super.list(queryWrapper);
    }

    public Long queryCnSkuCountByCat(Set<Long> catIds,String sourceCountryCode) {
        if(StringUtils.isBlank(sourceCountryCode)){
            return 0L;
        }
        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>()
                .select(SkuPO::getSkuId)
                .in(CollectionUtils.isNotEmpty(catIds),SkuPO::getJdCatId, catIds)
                .eq(SkuPO::getSourceCountryCode, sourceCountryCode)
                .eq(SkuPO::getYn, YnEnum.YES.getCode())
                ;
        return super.count(queryWrapper);
    }

    public List<SkuPO> queryCnSkuOffset(Long startSkuId, Integer offset,Integer pageSize) {
        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>()
                .gt(startSkuId != null, SkuPO::getSkuId, startSkuId)
                .eq(SkuPO::getSourceCountryCode, CountryConstant.COUNTRY_ZH)
                .eq(SkuPO::getYn, YnEnum.YES.getCode())
                .orderByAsc(SkuPO::getSkuId)
                .last("LIMIT " + offset+","+pageSize);
        return super.list(queryWrapper);
    }

    public Long queryCnSkuCount(Long startSkuId) {
        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>()
                .select(SkuPO::getSkuId)
                .gt(startSkuId != null, SkuPO::getSkuId, startSkuId)
                .eq(SkuPO::getSourceCountryCode, CountryConstant.COUNTRY_ZH)
                .eq(SkuPO::getYn, YnEnum.YES.getCode())
                ;

        return super.count(queryWrapper);
    }

    public Set<Long> listSkuIdsByHsCode(String hsCode, String sourceCountryCode) {
        // 查询次数，最大查询10000次，即最大返回50万的skuId
        int count = 0;
        // 一页最大查询sku数量
        int size = 500;
        // 起始查询的skuId，开区间
        Long minSkuId = 0L;

        Set<Long> result = Sets.newHashSet();

        while (count < 1000) {
            List<Long> skuIds = listSkuIdsByHsCode(hsCode, sourceCountryCode, minSkuId, size);

            if (CollectionUtils.isNotEmpty(skuIds)) {
                result.addAll(skuIds);
            }

            if (skuIds.size() < size) {
                break;
            }

            minSkuId = skuIds.get(skuIds.size() - 1);
            count++;
        }

        return result;
    }

    public List<Long> listSkuIdsByHsCode(String hsCode, String sourceCountryCode, Long minSkuId, Integer size) {
        if (StringUtils.isAnyEmpty(hsCode, sourceCountryCode)) {
            log.warn("listSkuIdsByHsCode param is null, hsCode:{}, sourceCountryCode:{}", hsCode, sourceCountryCode);
            return Lists.newArrayList();
        }

        if (minSkuId == null) {
            minSkuId = 0L;
        }

        if (size == null) {
            size = 500;
        }

        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>()
                .select(SkuPO::getSkuId)
                .eq(SkuPO::getSourceCountryCode, sourceCountryCode)
                .eq(SkuPO::getHsCode, hsCode)
                .gt(SkuPO::getSkuId, minSkuId)
                .eq(SkuPO::getYn, YnEnum.YES.getCode())
                .orderByAsc(SkuPO::getSkuId)
                .last("LIMIT " + size)
                ;


        List<SkuPO> skus = super.getBaseMapper().selectList(queryWrapper);

        return skus.stream().map(SkuPO::getSkuId).collect(Collectors.toList());
    }

    public Set<Long> getExistSkuIds(List<Long> skuIds, Integer yn) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Sets.newHashSet();
        }

        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>()
                .select(SkuPO::getSkuId)
                .in(SkuPO::getSkuId, skuIds)
                .eq(SkuPO::getYn, yn);

        List<SkuPO> skus = super.getBaseMapper().selectList(queryWrapper);

        return skus.stream().map(SkuPO::getSkuId).collect(Collectors.toSet());
    }

    /**
     * 根据京东SKU ID列表查询对应的SKU数据
     * @param jdSkuIds 京东SKU ID列表
     * @return SKU列表
     */
    public List<SkuPO> listSkuPoByJdSkuIds(List<Long> jdSkuIds) {
        if (CollectionUtils.isEmpty(jdSkuIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>()
                .in(SkuPO::getJdSkuId, jdSkuIds)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(queryWrapper);
    }

    /**
     * 查询更新时间大于等于指定时间的所有京东SKU ID列表
     * @param updateTime 更新时间阈值
     * @return 京东SKU ID列表
     */
    public List<Long> queryAllJdSkuIdsByUpdateTime(String updateTime) {
        return super.getBaseMapper().queryAllJdSkuIdsByUpdateTime(updateTime);
    }
}




