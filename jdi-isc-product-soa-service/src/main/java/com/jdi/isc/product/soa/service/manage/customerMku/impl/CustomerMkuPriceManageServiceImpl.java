package com.jdi.isc.product.soa.service.manage.customerMku.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.CustomerMkuBindException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerMku.biz.CustomerMkuPricePageReqVO;
import com.jdi.isc.product.soa.domain.customerMku.biz.CustomerMkuPriceVO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPricePO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailPO;
import com.jdi.isc.product.soa.domain.enums.price.SkuSalePriceTypeEnums;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.mkuTag.biz.MkuTagIdVO;
import com.jdi.isc.product.soa.domain.mkuTag.biz.MkuTagPageVO;
import com.jdi.isc.product.soa.domain.mkuTag.po.MkuTagPO;
import com.jdi.isc.product.soa.domain.price.biz.MkuPriceReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.ProductIdVO;
import com.jdi.isc.product.soa.domain.specialAttr.biz.SpecialAttrTagVO;
import com.jdi.isc.product.soa.price.api.salePrice.req.SalePriceReqDTO;
import com.jdi.isc.product.soa.price.api.salePrice.res.SalePriceResDTO;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.mkuTag.MkuTagAtomicService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.customerMku.CustomerMkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.mkuTag.MkuTagManageService;
import com.jdi.isc.product.soa.service.manage.price.salePrice.SkuSalePriceManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.mapstruct.customer.CustomerConvert;
import com.jdi.isc.product.soa.service.mapstruct.customerMku.CustomerMkuPriceConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * mku固定sku读服务
 * <AUTHOR>
 * @date 20231221
 */
@Service
@Slf4j
public class CustomerMkuPriceManageServiceImpl extends BaseManageSupportService<CustomerMkuPriceVO, CustomerMkuPricePO> implements CustomerMkuPriceManageService {

    @Resource
    private CustomerMkuPriceAtomicService customerMkuPriceAtomicService;
    @Resource
    private SkuReadManageService skuReadManageService;
    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private MkuLangAtomicService mkuLangAtomicService;

    @Resource
    private MkuTagAtomicService mkuTagAtomicService;

    @Resource
    private MkuTagManageService mkuTagManageService;
    @Resource
    private CustomerSkuPriceDetailAtomicService customerSkuPriceDetailAtomicService;

    @Resource
    private Map<String, SkuSalePriceManageService> skuSalePriceManageServiceMap;

    /** 获取客户mku站点下固定sku列表。
     *  不需要SourceCountryCode参数了。
     * */
    @Deprecated
    public List<CustomerMkuPricePO> getCustomerMkuFixedSkuList(MkuPriceReqVO req){
        //获取当前客户站点下固定sku信息
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .in(CustomerMkuPricePO::getMkuId,req.getMkuIdList())
                .eq(CustomerMkuPricePO::getClientCode, req.getClientCode())
                .eq(CustomerMkuPricePO::getSourceCountryCode, req.getSourceCountryCode())
                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode())
                ;
        return customerMkuPriceAtomicService.list(wrapper);
    }

    /**
     * 获取客户mku站点下固定sku列表
     * @param req
     * @return
     */
    public List<CustomerMkuPricePO> getCustomerMkuFixedSkuListByMkuClient(MkuPriceReqVO req){
        //获取当前客户站点下固定sku信息
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .in(CustomerMkuPricePO::getMkuId,req.getMkuIdList())
                .eq(CustomerMkuPricePO::getClientCode, req.getClientCode())
                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode())
                ;
        return customerMkuPriceAtomicService.list(wrapper);
    }

    @Override
    public PageInfo<CustomerMkuPriceVO> page(CustomerMkuPricePageReqVO input) {
        Page<CustomerMkuPricePO> page = new Page<>(input.getIndex(), input.getSize());
        //查询条件
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery();
        if(Objects.nonNull(input.getClientIdVo())){
            wrapper.in(CollectionUtils.isNotEmpty(input.getClientIdVo().getClientIdList()), CustomerMkuPricePO::getClientCode, input.getClientIdVo().getClientIdList());
        }
        Set<Long > mkuIdSet = new HashSet<>();
        if(Objects.nonNull(input.getMkuId())){
            mkuIdSet.add(input.getMkuId());
        }
        if(CollectionUtils.isNotEmpty(input.getProductIds())){
            ProductIdVO productIdVO = skuReadManageService.getProductInfoByIds(input.getProductIds());
            if(CollectionUtils.isNotEmpty(productIdVO.getMkuIds())){
                mkuIdSet.addAll(productIdVO.getMkuIds());
            }
        }

        if(CollectionUtils.isNotEmpty(input.getTagIdList())){
            List<MkuTagPO> mkuTagPOList = new ArrayList<>();
            List<MkuTagIdVO> tagIdList = input.getTagIdList();
            tagIdList.forEach( vo ->{
                MkuTagPO mkuTagPO = new MkuTagPO();
                mkuTagPO.setAttributeKeyId(vo.getAttributeKeyId());
                mkuTagPO.setAttributeValueId(vo.getAttributeValueId());
                mkuTagPOList.add(mkuTagPO);
            } );
            Set<Long> mkuIdTagSet = mkuTagAtomicService.getMkuIdSetByList(mkuTagPOList,null);
            if(CollectionUtils.isNotEmpty(mkuIdSet)){
                mkuIdSet.retainAll(mkuIdTagSet);
            }else {
                mkuIdSet.addAll(mkuIdTagSet);
            }
            if(CollectionUtils.isEmpty(mkuIdSet)){
                mkuIdSet.add(-1L);
            }
        }
        input.setMkuIds(new ArrayList<>(mkuIdSet));

        wrapper.eq(StringUtils.isNotBlank(input.getClientCode()),CustomerMkuPricePO::getClientCode,input.getClientCode())
                .like(StringUtils.isNotBlank(input.getClientName()),CustomerMkuPricePO::getClientName,input.getClientName())
                .eq(StringUtils.isNotBlank(input.getSourceCountryCode()),CustomerMkuPricePO::getSourceCountryCode,input.getSourceCountryCode())
                .in(CollectionUtils.isNotEmpty(input.getMkuIds()) ,CustomerMkuPricePO::getMkuId,input.getMkuIds())
                .orderByDesc(CustomerMkuPricePO::getUpdateTime);
        Page<CustomerMkuPricePO> dbRecord = customerMkuPriceAtomicService.page(page, wrapper);
        PageInfo<CustomerMkuPriceVO> res = pageTransform(dbRecord, CustomerMkuPriceConvert.INSTANCE.listPo2vo(dbRecord.getRecords()));
        this.setCustomerPrice(res.getRecords());
        this.setMkuName(res.getRecords());

        setMkuTag(res.getRecords());
        return res;
    }
    public void setMkuTag(List<CustomerMkuPriceVO> voList){
        if(CollectionUtils.isEmpty(voList)){
            return;
        }
        for (CustomerMkuPriceVO customerMkuPriceVO : voList){
            if(Objects.isNull(customerMkuPriceVO.getMkuId()) || StringUtils.isBlank(customerMkuPriceVO.getTargetCountryCode())){
                continue;
            }
            MkuTagPageVO mkuTagVO = new MkuTagPageVO();
            List<Long> mkuList = new ArrayList<>();
            mkuList.add(customerMkuPriceVO.getMkuId());
            mkuTagVO.setMkuList(mkuList);
            mkuTagVO.setTargetCountryCode(customerMkuPriceVO.getTargetCountryCode());
            Map<Long,List<SpecialAttrTagVO>> mkuSpecialMap = mkuTagManageService.getMkuTagVOMap(mkuTagVO);

            if(mkuSpecialMap.containsKey(customerMkuPriceVO.getMkuId())){
                List<SpecialAttrTagVO> specialAttrTagVOList = mkuSpecialMap.get(customerMkuPriceVO.getMkuId());
                customerMkuPriceVO.setSpecialAttrTagVOList(specialAttrTagVOList);
            }
        }
    }


    //初始化mku客制价
    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    @PFTracing
    public Boolean saveOrUpdate(CustomerMkuPriceVO input) {
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .eq(CustomerMkuPricePO::getMkuId,input.getMkuId())
                .eq(CustomerMkuPricePO::getClientCode,input.getClientCode())
                .eq(CustomerMkuPricePO::getSourceCountryCode,input.getSourceCountryCode()) ;
        CustomerMkuPricePO record = customerMkuPriceAtomicService.getOne(wrapper);
        if(record!=null){
            LambdaUpdateWrapper<CustomerMkuPricePO> updateWrapper = Wrappers.<CustomerMkuPricePO>lambdaUpdate()
                    .eq(CustomerMkuPricePO::getMkuId,input.getMkuId())
                    .eq(CustomerMkuPricePO::getClientCode,input.getClientCode())
                    .eq(CustomerMkuPricePO::getSourceCountryCode,input.getSourceCountryCode());
            return customerMkuPriceAtomicService.update(new CustomerMkuPricePO(input.getFixedSkuId()),updateWrapper);
        }else {
            return customerMkuPriceAtomicService.save(CustomerMkuPriceConvert.INSTANCE.vo2po(input));
        }
    }

    //移除mku客制价<一定是mku解绑客户时触发>
    @Override
    public Boolean delete(CustomerMkuPO input) {
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .eq(CustomerMkuPricePO::getMkuId,input.getMkuId())
                .eq(CustomerMkuPricePO::getClientCode,input.getClientCode());
        boolean res = customerMkuPriceAtomicService.remove(wrapper);
        log.info("CustomerMkuPriceManageServiceImpl.delete 客户{} mku{}价格关系移除完毕 , 操作人:{}" , input.getClientCode(),input.getMkuId(), LoginContextHolder.getLoginContextHolder().getPin());
        return res;
    }

    @Override
    @PFTracing
    public Map<Long, CustomerMkuPriceVO> batchQueryCustomerSkuPriceMap(List<MkuRelationPO> mkuRelationPOList, String clientCode) {

        if (CollectionUtils.isEmpty(mkuRelationPOList)) {
            return Collections.emptyMap();
        }

        CustomerVO customerVO = customerManageService.detail(clientCode);

        List<CustomerMkuPriceVO> customerMkuPriceVOS =  Lists.newArrayList();
        for(MkuRelationPO mkuRelationPO : mkuRelationPOList){
            SalePriceReqDTO salePriceReqDTO = new SalePriceReqDTO();
            salePriceReqDTO.setSkuId(mkuRelationPO.getSkuId());
            salePriceReqDTO.setClientCode(customerVO.getClientCode());
            salePriceReqDTO.setCustomerDTO(CustomerConvert.INSTANCE.customerVo2Dto(customerVO));
            salePriceReqDTO.setCurrencyCode(customerVO.getSaleCurrency());
            SalePriceResDTO salePriceResDTO = skuSalePriceManageServiceMap.get(SkuSalePriceTypeEnums.getEnumByCountryCode(customerVO.getCountry()).getServiceName())
                    .getSalePrice(salePriceReqDTO);

            if(salePriceResDTO == null || salePriceResDTO.getSalePrice() == null){
                throw new CustomerMkuBindException(String.format("客户mku绑定失败:mku %s 未能获取到对应销售价格、请检查sku客制化价格或国家协议价",mkuRelationPO.getMkuId()));
            }
            if(CountryConstant.COUNTRY_BR.equals(customerVO.getCountry()) || CountryConstant.COUNTRY_ZH.equals(customerVO.getCountry())) {
                List<CustomerSkuPriceDetailPO> customerSkuPriceDetailPOS = customerSkuPriceDetailAtomicService.getListByClientCodeAndSkuId(customerVO.getClientCode(), mkuRelationPO.getSkuId());
                if (CollectionUtils.isEmpty(customerSkuPriceDetailPOS)) {
                    throw new CustomerMkuBindException(String.format("客户mku绑定失败:mku %s 未绑定sku客制化价格", mkuRelationPO.getMkuId()));
                }
            }

            customerMkuPriceVOS.add(new CustomerMkuPriceVO(mkuRelationPO.getMkuId(),clientCode,null,
                    mkuRelationPO.getSkuSourceCountryCode(),mkuRelationPO.getSkuId(),mkuRelationPO.getVendorCode()));
        }

        return Optional.ofNullable(customerMkuPriceVOS)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(CustomerMkuPriceVO::getMkuId, Function.identity()));
    }

    private void setCustomerPrice(List<CustomerMkuPriceVO> params){
        if(CollectionUtils.isEmpty(params)){
            log.error("CustomerMkuPriceManageServiceImpl.setCustomerPrice params is null");
            return;
        }

        Map<String,CustomerVO> customerVOMap = customerManageService.map();
        //补充sku客制化价格信息
        params.forEach(mku->{
            CustomerVO customerVO = customerVOMap.get(mku.getClientCode());
            SalePriceReqDTO salePriceReqDTO = new SalePriceReqDTO();
            salePriceReqDTO.setSkuId(mku.getFixedSkuId());
            salePriceReqDTO.setClientCode(mku.getClientCode());
            salePriceReqDTO.setCustomerDTO(CustomerConvert.INSTANCE.customerVo2Dto(customerVO));
            salePriceReqDTO.setCurrencyCode(customerVO.getSaleCurrency());
            SalePriceResDTO salePriceResDTO = skuSalePriceManageServiceMap.get(SkuSalePriceTypeEnums.getEnumByCountryCode(mku.getTargetCountryCode()).getServiceName())
                    .getSalePrice(salePriceReqDTO);
            mku.setCurrency(customerVO.getSaleCurrency());
            mku.setCustomerPrice(salePriceResDTO.getSalePrice());
            mku.setCustomerTaxPrice(salePriceResDTO.getTaxSalePrice());
            mku.setCurrency(salePriceResDTO.getTargetCurrencyCode());
        });
    }

    private void setMkuName(List<CustomerMkuPriceVO> params){
        if(CollectionUtils.isEmpty(params)){
            log.error("CustomerMkuPriceManageServiceImpl.setMkuName params is null");
            return;
        }

        String lang = LangContextHolder.get();

        Set<Long> mkuIds = params.stream().map(CustomerMkuPriceVO::getMkuId).collect(Collectors.toSet());
        // mku多语言信息
        Map<Long, Map<String, String>> mkuLangMaps = mkuLangAtomicService.getLangMapByMkuIds(mkuIds, Arrays.asList(lang,LangConstant.LANG_ZH));
        for(CustomerMkuPriceVO mkuPriceVO : params){
            Map<String,String> mapString = mkuLangMaps.get(mkuPriceVO.getMkuId());
            if(MapUtils.isEmpty(mapString)){
                continue;
            }
            String mkuName = StringUtils.isNotBlank(mapString.get(lang))?mapString.get(lang):mapString.get(LangConstant.LANG_ZH);
            mkuPriceVO.setMkuName(mkuName);
        }
    }
}
