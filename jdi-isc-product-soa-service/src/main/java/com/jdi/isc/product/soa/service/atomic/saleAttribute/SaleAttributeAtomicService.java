package com.jdi.isc.product.soa.service.atomic.saleAttribute;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributePO;
import com.jdi.isc.product.soa.repository.mapper.saleAttribute.SaleAttributeBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * 销售属性原子服务
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
public class SaleAttributeAtomicService extends ServiceImpl<SaleAttributeBaseMapper, SaleAttributePO> {

    /**
     * 保存或更新销售属性
     * @param jdCatId 中台类目ID
     * @param saleAttributeType 销售属性类型
     * @param saleAttributeName 销售属性名称
     * @param sort 排序字段
     * @return 返回原有或更新后的销售属性
     */
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public SaleAttributePO saveOrUpdateSaleAttribute(Long jdCatId, Integer saleAttributeType, String saleAttributeName, Integer sort) {
        log.info("SaleAttributeAtomicService.saveOrUpdateSaleAttribute 入参: jdCatId={}, saleAttributeType={}, saleAttributeName={}, sort={}",
                jdCatId, saleAttributeType, saleAttributeName, sort);

        if (jdCatId == null || saleAttributeType == null || StringUtils.isBlank(saleAttributeName)) {
            log.warn("SaleAttributeAtomicService.saveOrUpdateSaleAttribute 参数为空");
            return null;
        }

        // 查询是否已经存在该类目下的同类型销售属性
        List<SaleAttributePO> existingAttrs = this.getValidByJdCatIdAndType(jdCatId, saleAttributeType);

        SaleAttributePO saleAttributePO = new SaleAttributePO();
        if (!CollectionUtils.isEmpty(existingAttrs)) {
            // 如果存在，取第一个进行更新
            SaleAttributePO exist = existingAttrs.get(0);
            if (exist != null && !saleAttributeName.equals(exist.getSaleAttributeName())) {
                // 销售属性名称不相等时才更新
                saleAttributePO.setSaleAttributeName(saleAttributeName);
                saleAttributePO.setUpdateTime(new Date().getTime());
                saleAttributePO.setSort(sort);
                // 如果登录上下文为空，则设置更新者为系统
                if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                    saleAttributePO.setUpdater(Constant.PIN_SYSTEM);
                }
                saleAttributePO.setId(exist.getId());
                this.updateById(saleAttributePO);
                log.info("SaleAttributeAtomicService.saveOrUpdateSaleAttribute 更新销售属性成功: {}", JSON.toJSONString(saleAttributePO));
            } else {
                saleAttributePO = exist;
                log.info("SaleAttributeAtomicService.saveOrUpdateSaleAttribute 销售属性名称未变化，无需更新: {}", JSON.toJSONString(saleAttributePO));
            }
        } else {
            // 如果不存在，创建新的销售属性
            saleAttributePO = new SaleAttributePO();
            saleAttributePO.setJdCatId(jdCatId);
            saleAttributePO.setSaleAttributeType(saleAttributeType);
            saleAttributePO.setSaleAttributeName(saleAttributeName);
            saleAttributePO.setSort(sort);
            saleAttributePO.setYn(YnEnum.YES.getCode());
            long timestamp = new Date().getTime();
            saleAttributePO.setCreateTime(timestamp);
            saleAttributePO.setUpdateTime(timestamp);
            // 如果登录上下文为空，则设置创建者和更新者为系统
            if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                saleAttributePO.setCreator(Constant.PIN_SYSTEM);
                saleAttributePO.setUpdater(Constant.PIN_SYSTEM);
            }
            this.save(saleAttributePO);
            log.info("SaleAttributeAtomicService.saveOrUpdateSaleAttribute 创建销售属性成功: {}", JSON.toJSONString(saleAttributePO));
        }

        return saleAttributePO;

    }

    /**
     * 根据ID查询有效的销售属性
     * @param id 销售属性ID
     * @return 销售属性实体
     */
    public SaleAttributePO getValidById(Long id) {
        LambdaQueryWrapper<SaleAttributePO> queryWrapper = new LambdaQueryWrapper<SaleAttributePO>()
                .eq(SaleAttributePO::getId, id)
                .eq(SaleAttributePO::getYn, YnEnum.YES.getCode());
        return super.getOne(queryWrapper);
    }

    public List<SaleAttributePO> getValidByIdList(List<Long> idLsit) {
        LambdaQueryWrapper<SaleAttributePO> queryWrapper = new LambdaQueryWrapper<SaleAttributePO>()
                .in(SaleAttributePO::getId, idLsit)
                .eq(SaleAttributePO::getYn, YnEnum.YES.getCode());
        return super.list(queryWrapper);
    }


    /**
     * 根据主站类目ID查询有效的销售属性
     * @param jdCatId 主站类目ID
     * @return 销售属性列表
     */
    public List<SaleAttributePO> getValidByJdCatId(Long jdCatId) {
        LambdaQueryWrapper<SaleAttributePO> queryWrapper = new LambdaQueryWrapper<SaleAttributePO>()
                .eq(SaleAttributePO::getJdCatId, jdCatId)
                .eq(SaleAttributePO::getYn, YnEnum.YES.getCode())
                .orderByAsc(SaleAttributePO::getSort);
        return super.list(queryWrapper);
    }

    /**
     * 根据主站类目ID和销售属性类型查询有效的销售属性
     * @param jdCatId 主站类目ID
     * @param saleAttributeType 销售属性类型
     * @return 销售属性列表
     */
    public List<SaleAttributePO> getValidByJdCatIdAndType(Long jdCatId, Integer saleAttributeType) {
        LambdaQueryWrapper<SaleAttributePO> queryWrapper = new LambdaQueryWrapper<SaleAttributePO>()
                .eq(SaleAttributePO::getJdCatId, jdCatId)
                .eq(SaleAttributePO::getSaleAttributeType, saleAttributeType)
                .eq(SaleAttributePO::getYn, YnEnum.YES.getCode())
                .orderByAsc(SaleAttributePO::getSort);
        return super.list(queryWrapper);
    }

    /**
     * 根据销售属性名称查询有效的销售属性
     * @param saleAttributeName 销售属性名称
     * @return 销售属性列表
     */
    public List<SaleAttributePO> getValidBySaleAttributeName(String saleAttributeName) {
        LambdaQueryWrapper<SaleAttributePO> queryWrapper = new LambdaQueryWrapper<SaleAttributePO>()
                .eq(SaleAttributePO::getSaleAttributeName, saleAttributeName)
                .eq(SaleAttributePO::getYn, YnEnum.YES.getCode())
                .orderByAsc(SaleAttributePO::getSort);
        return super.list(queryWrapper);
    }
} 