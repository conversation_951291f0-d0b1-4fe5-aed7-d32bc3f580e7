package com.jdi.isc.product.soa.service.manage.customerSku.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.enums.PriceAvailableSaleStatusEnum;
import com.jdi.isc.product.soa.api.price.req.PriceAvailableSaleStatusReqDTO;
import com.jdi.isc.product.soa.api.price.res.PriceAvailableSaleStatusResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.common.util.BigDecimalUtil;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveOrderPO;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceWarningPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.sku.biz.ProductIdVO;
import com.jdi.isc.product.soa.rpc.suport.AlertHelper;
import com.jdi.isc.product.soa.rpc.utils.ExceptionUtil;
import com.jdi.isc.product.soa.service.atomic.approveorder.ApproveFormAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceWarningAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.manage.approveorder.template.create.AbstractCreateApproveOrderExe;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuSalePriceDetailManageService;
import com.jdi.isc.product.soa.service.manage.price.ProfitCalculateManageService;
import com.jdi.isc.product.soa.service.manage.price.warning.customerprice.manager.CustomerPriceWarningManager;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.support.transactional.ProductTransactionExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * The type Customer sku sale price detail manage service.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CustomerSkuSalePriceDetailManageServiceImpl implements CustomerSkuSalePriceDetailManageService {

    @Resource
    private ProductTransactionExecutor productTransactionExecutor;

    @Resource
    private CustomerSkuPriceDetailAtomicService customerSkuPriceDetailAtomicService;

    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;

    @Resource
    private CustomerManageService customerManageService;

    @Resource
    private ApproveFormAtomicService approveFormAtomicService;

    @Resource
    private CustomerPriceWarningManager customerPriceWarningManager;

    @Resource
    private SkuAtomicService skuAtomicService;



    @Override
    public DataResponse<PriceAvailableSaleStatusResDTO> updateAvailableSaleStatus(List<PriceAvailableSaleStatusReqDTO> input) {
        log.info("updateAvailableSaleStatus customer, input={}", JSONObject.toJSONString(input));
        if (CollectionUtils.isEmpty(input)) {
            log.info("updateAvailableSaleStatus, input is empty");
            return DataResponse.success();
        }

        long l = System.currentTimeMillis();

        try {
            long count = input.stream().filter(item -> item.getId() == null).count();
            if (count > 0) {
                log.warn("id不能为空, count={}, input={}", count, JSONObject.toJSONString(input));
                return DataResponse.success();
            }

            List<Long> ids = input.stream().map(PriceAvailableSaleStatusReqDTO::getId).distinct().collect(Collectors.toList());

            Map<Long, String> idUpdaterMap = input.stream().collect(Collectors.toMap(PriceAvailableSaleStatusReqDTO::getId, PriceAvailableSaleStatusReqDTO::getUpdater, (k1, k2) -> k1));

            List<CustomerSkuPriceDetailPO> list = customerSkuPriceDetailAtomicService.listValidByIds(ids);

            if (CollectionUtils.isEmpty(list)) {
                log.warn("更新客制化价格可售状态，协议价明细为空，ids={}", ids);
                return DataResponse.success();
            }

            log.info("updateAvailableSaleStatus listValidByIds, cost={}", System.currentTimeMillis() - l);

            // 查询客户信息
            List<String> clientCodes = list.stream().map(CustomerSkuPriceDetailPO::getClientCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<Long> skuIds = list.stream().map(CustomerSkuPriceDetailPO::getSkuId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<String, CustomerVO> customerMap = customerManageService.queryCustomerByClientCodes(clientCodes);

            log.info("updateAvailableSaleStatus queryCustomerByClientCodes, cost={}", System.currentTimeMillis() - l);

            Set<Long> existSkuIds = skuAtomicService.getExistSkuIds(skuIds, YnEnum.YES.getCode());

            List<CustomerSkuPriceDetailPO> updateList = new ArrayList<>();

            for (CustomerSkuPriceDetailPO item : list) {
                if (!existSkuIds.contains(item.getSkuId())) {
                    log.warn("skuId不存在, skuId={}, id={}", item.getSkuId(), item.getId());
                    continue;
                }
                CustomerVO customer = customerMap.get(item.getClientCode());

                if (customer == null || StringUtils.isEmpty(customer.getCountry())) {
                    log.warn("客户信息维护错误, clientCode={}, id={}", item.getClientCode(), item.getId());
                    continue;
                }

                CountryAgreementPricePO countryAgreementPrice = countryAgreementPriceAtomicService.getBySkuIdAndTargetCountryCode(item.getSkuId(), customer.getCountry());

                if (countryAgreementPrice == null) {
                    log.warn("协议价信息未维护, skuId={}, country={}, id={}", item.getSkuId(), customer.getCountry(), item.getId());
                    continue;
                }

                // 计算可售状态
                int availableSaleStatus = this.calculateAvailableSaleStatus(item, countryAgreementPrice);

                CustomerSkuPriceDetailPO update = new CustomerSkuPriceDetailPO();

                update.setId(item.getId());
                update.setUpdateTime(item.getUpdateTime());

                String updater = idUpdaterMap.get(item.getId());
                update.setUpdater(updater);

                update.setAvailableSaleStatus(availableSaleStatus);

                updateList.add(update);
            }

            List<Long> skuPriceDetailIds = updateList.stream().map(CustomerSkuPriceDetailPO::getId).collect(Collectors.toList());

            productTransactionExecutor.execute(() -> {
                customerSkuPriceDetailAtomicService.updateAvailableSaleStatusList(updateList);
                customerPriceWarningManager.syncAvailableSaleStatusList(skuPriceDetailIds);
            });
        } catch (ProductBizException e) {
            return DataResponse.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新客制化价格可售状态异常", e);
            return DataResponse.error(ExceptionUtil.getMessage(e, "更新客制化价格可售状态失败"));
        } finally{
            log.info("更新客制化价格可售状态完成，input={}, cost={}", JSONObject.toJSONString(input), System.currentTimeMillis() - l);
        }

        return DataResponse.success();
    }

    @Resource
    private CustomerSkuPriceWarningAtomicService customerSkuPriceWarningAtomicService;


    /**
     * 根据审批ID更新不可售阈值
     */
    @Override
    public void updateUnsellableThresholdByApproveId(ApproveOrderPO order) {
        Long approveId = order.getId();
        // 利润率 百分比
        BigDecimal profitRate = approveFormAtomicService.getFiledValue(approveId, AbstractCreateApproveOrderExe.PROFIT_RATE, BigDecimal.class);

        if(profitRate == null) {
            log.error("审批流中未获取到利润率，approveId={}", approveId);
            AlertHelper.p0("审批流中未获取到利润率，approveId=", String.valueOf(approveId));
            return;
        }

        profitRate = profitRate.divide(BigDecimal.valueOf(100), 6, RoundingMode.HALF_UP);

        CustomerSkuPriceWarningPO warning = customerSkuPriceWarningAtomicService.getValidById(Long.parseLong(order.getBizId()));

        if (warning == null) {
            log.warn("找不到客制化预警信息. id={}", order.getBizId());
            throw new ProductBizException("找不到客制化预警信息. %s", order.getBizId());
        }

        // 查询协议价
        CustomerSkuPriceDetailPO  skuPriceDetail = customerSkuPriceDetailAtomicService.getValidById(warning.getBizId());

        if (skuPriceDetail == null) {
            log.warn("找不到客制化价格信息. id={}", order.getBizId());
            throw new ProductBizException("找不到客制化价格信息. %s", order.getBizId());
        }

        // 查询客户信息
        CustomerVO customer = customerManageService.getValidByClientCode(skuPriceDetail.getClientCode());

        customerPriceWarningManager.updateUnsellableThreshold(skuPriceDetail, skuPriceDetail.getSkuId(), customer.getCountry(), profitRate, order.getUpdater());
    }

    @Resource
    private SkuReadManageService skuReadManageService;

    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;

    @Resource
    private OperDuccConfig operDuccConfig;

    @Override
    public List<Long> updatePoolStatus(Set<Long> agreementIds) {

        List<Long> customerDetailIds = Lists.newArrayList();

        long l = System.currentTimeMillis();
        List<CustomerSkuPriceDetailPO> updateList = Lists.newArrayList();
        try {
            Map<Long, CountryAgreementPricePO> priceMap = countryAgreementPriceAtomicService.mapByIds(agreementIds);

            Set<Long> customerSkuPriceDetailIds = Sets.newHashSet();

            for (Long agreementId : agreementIds) {
                CountryAgreementPricePO agreementPrice = priceMap.get(agreementId);

                if (agreementPrice == null) {
                    log.warn("find agreementPrice is null, agreementId={}", agreementId);
                    continue;
                }

                Long skuId = agreementPrice.getSkuId();
                String currency = agreementPrice.getCurrency();
                String targetCountryCode = agreementPrice.getTargetCountryCode();

                List<CustomerSkuPriceDetailPO> customerSkuPriceDetailPOS = customerSkuPriceDetailAtomicService.getValidBySkuIdAndCurrency(skuId, currency);
                if(CollectionUtils.isEmpty(customerSkuPriceDetailPOS)){
                    log.error("CustomerSkuPriceWarningManageServiceImpl.saveOrUpdateFromCostPrice is not need warning");
                    continue;
                }

                ProductIdVO productIdVO = skuReadManageService.getProductInfoByIds(Lists.newArrayList(skuId));

                if (CollectionUtils.isEmpty(productIdVO.getMkuIds())) {
                    log.warn("找不到mku信息. skuId={}", skuId);
                    continue;
                }

                for(CustomerSkuPriceDetailPO detail : customerSkuPriceDetailPOS){
                    if (customerSkuPriceDetailIds.contains(detail.getId())) {
                        continue;
                    }

                    CustomerSkuPriceDetailPO update = new CustomerSkuPriceDetailPO();
                    update.setId(detail.getId());

                    // 设置客户池状态
                    String bindStatus = customerMkuAtomicService.getBindStatus(productIdVO.getMkuIds().get(0), targetCountryCode, detail.getClientCode(), operDuccConfig.getPreseletorClientCodeList());

                    update.setCustomerMkuPoolStatus(bindStatus);

                    updateList.add(update);
                    customerDetailIds.add(detail.getId());
                }

                Set<Long> customerSkuPriceDetailIdList = customerSkuPriceDetailPOS.stream().map(CustomerSkuPriceDetailPO::getId).collect(Collectors.toSet());
                customerSkuPriceDetailIds.addAll(customerSkuPriceDetailIdList);
            }
            log.info("更新协议价客户池状态，agreementIds={}, updateList.size={}", agreementIds, updateList.size());
            if (CollectionUtils.isEmpty(updateList)) {
                return Lists.newArrayList();
            }
            // 更新客户池状态
            Map<Long, CustomerSkuPriceDetailPO> tempMap = updateList.stream().collect(Collectors.toMap(CustomerSkuPriceDetailPO::getId, Function.identity(), (k1, k2) -> k1));

            List<CustomerSkuPriceDetailPO> list = CollectionUtil.sort(tempMap.values(), Comparator.comparing(CustomerSkuPriceDetailPO::getId, Comparator.nullsLast(Long::compareTo)));

            customerSkuPriceDetailAtomicService.updatePoolStatus(list);
        } catch (Exception e) {
            log.info("更新协议价客户池状态异常，agreementIds={}, updateList={}, message={}", agreementIds, JSONObject.toJSONString(updateList), ExceptionUtil.getMessage(e, "更新协议价客户池状态失败"), e);
        } finally {
            log.info("更新协议价客户池状态完成，agreementIds={}, updateList={}, cost={}", agreementIds, JSONObject.toJSONString(updateList), System.currentTimeMillis() - l);
        }

        return customerDetailIds;
    }

    /**
     * 计算协议价可售状态
     * @param item 包含协议价、国家成本价和不可售阈值的对象
     * @return 可售状态枚举对应的代码值
     */
    private int calculateAvailableSaleStatus(CustomerSkuPriceDetailPO item, CountryAgreementPricePO countryAgreementPrice) {
        Preconditions.checkArgument(item != null && countryAgreementPrice != null, "参数不能为空");

        BigDecimal customerSalePrice = item.getCustomerSalePrice();
        BigDecimal countryCostPrice = countryAgreementPrice.getCountryCostPrice();
        BigDecimal unsellableThreshold = item.getUnsellableThreshold();

        if (customerSalePrice == null || countryCostPrice == null || unsellableThreshold == null) {
            log.warn("计算客制价可售状态，不可售，参数为空，item={}", JSONObject.toJSONString(item));
            return PriceAvailableSaleStatusEnum.DISABLE.getCode();
        }

        // 计算利润率
        BigDecimal profitRate = SpringUtil.getBean(ProfitCalculateManageService.class).calculateSalePriceProfitRate(customerSalePrice, countryCostPrice);

        if (profitRate == null) {
            log.warn("计算客制价可售状态，不可售，利润率为空，item={}", JSONObject.toJSONString(item));
            return PriceAvailableSaleStatusEnum.DISABLE.getCode();
        }

        if (BigDecimalUtil.lt(profitRate, unsellableThreshold)) {
            log.warn("计算客制价可售状态，不可售，利润率小于不可售阈值，item={}, profitRate={}, unsellableThreshold={}， countryCostPrice={}", JSONObject.toJSONString(item), profitRate, unsellableThreshold, countryCostPrice);
            return PriceAvailableSaleStatusEnum.DISABLE.getCode();
        }

        log.warn("计算客制价可售状态，可售，利润率不小于不可售阈值，item={}, profitRate={}, unsellableThreshold={}, countryCostPrice={}", JSONObject.toJSONString(item), profitRate, unsellableThreshold, countryCostPrice);

        return PriceAvailableSaleStatusEnum.ENABLE.getCode();
    }
}
