package com.jdi.isc.product.soa.service.protocol.jsf.devOps;


import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.devOps.DevOpsEsApiService;
import com.jdi.isc.product.soa.service.manage.tool.EsToolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

/**
 * <AUTHOR>
 * @description：DevOpsCateApiServiceImpl
 * @Date 2025-06-09
 */
@Slf4j
@Service
public class DevOpsEsApiServiceImpl implements DevOpsEsApiService {

    @Resource
    private EsToolService esToolService;

    @Override
    public DataResponse<String> initPoolFlag(String env,String targetCountryCode,Set<Long> mkuIds) {
        log.info("DevOpsEsApiServiceImpl.initPoolFlag  targetCountryCode={},mkuIds={}", targetCountryCode,JSON.toJSONString(mkuIds));
        String result = esToolService.updateMkuEsPoolFlag(env, targetCountryCode, mkuIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> initEsData(String env,String targetCountryCode,String clientCode, Set<Long> mkuIds) {
        log.info("DevOpsEsApiServiceImpl.initEsData  targetCountryCode={},mkuIds={}", targetCountryCode,JSON.toJSONString(mkuIds));
        String result = esToolService.initEsData(env, targetCountryCode,clientCode, mkuIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<Boolean> deleteDocument(String env, String index, String id) {
        log.info("DevOpsEsApiServiceImpl.deleteDocument  index={},id={}", index,id);
        Boolean result = esToolService.deleteDocument(env, index, id);
        return DataResponse.success(result);
    }
}
