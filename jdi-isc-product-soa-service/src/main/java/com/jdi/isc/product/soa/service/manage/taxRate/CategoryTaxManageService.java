package com.jdi.isc.product.soa.service.manage.taxRate;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.domain.taxRate.biz.CategoryTaxPageVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CategoryTaxVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Description: 类目税率信息数据维护服务
 * @Author: taxuezheng1
 * @Date: 2024/03/26 17:51
 **/

public interface CategoryTaxManageService {

    /**
     * 保存、更新
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<Boolean> saveOrUpdate(CategoryTaxVO input);

    /**
     * 详情
     * @param id 对象ID
     * @return VO对象
     */
    CategoryTaxVO detail(Long id);

    /**
     * 分页查询
     * @param input 查询条件
     * @return 结果
     */
    PageInfo<CategoryTaxPageVO.Response> page(CategoryTaxPageVO.Request input);

    /**
     * 移除类目
     * @param id
     * @return
     */
    Boolean remove(Long id);

    /**
     * 下载导入模板
     * @param type vn: 越南，th 泰国
     * @param response
     */
    void template(String type, HttpServletResponse response);


    /**
     * 移除类目
     * @param id
     * @return
     */
    Boolean validData(Long id);

    /**
     * 更新品类税率， yn=0.
     *
     * @param skuIds  the sku ids
     * @param updater the updater
     */
    void updateYn(List<Long> skuIds, String updater);
}
