package com.jdi.isc.product.soa.service.atomic.customerMku;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.customerMku.biz.CustomerMkuIdsQueryVO;
import com.jdi.isc.product.soa.domain.customerMku.biz.CustomerMkuPriceVO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPricePO;
import com.jdi.isc.product.soa.repository.mapper.customerMku.CustomerMkuPriceBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 客户MKU价格原子服务
 * <AUTHOR>
 * @date 20231128
 **/
@Service
@Slf4j
public class CustomerMkuPriceAtomicService extends ServiceImpl<CustomerMkuPriceBaseMapper, CustomerMkuPricePO> {



    public List<CustomerMkuPricePO> queryMkuListsByClientCode(CustomerMkuIdsQueryVO customerMkuIdsQueryVO){
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .in(CustomerMkuPricePO::getMkuId, customerMkuIdsQueryVO.getMkuIds())
                .eq(CustomerMkuPricePO::getClientCode, customerMkuIdsQueryVO.getClientCode())
                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode());
        return baseMapper.selectList(wrapper);
    }

    public Boolean exists(CustomerMkuPriceVO customerMkuPriceVO){
        if(customerMkuPriceVO.getMkuId()!=null && customerMkuPriceVO.getFixedSkuId()!=null){
            LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                    .eq(customerMkuPriceVO.getMkuId()!=null,CustomerMkuPricePO::getMkuId,customerMkuPriceVO.getMkuId())
                    .eq(customerMkuPriceVO.getFixedSkuId()!=null,CustomerMkuPricePO::getFixedSkuId, customerMkuPriceVO.getFixedSkuId())
                    .eq(StringUtils.isNotBlank(customerMkuPriceVO.getSourceCountryCode()),CustomerMkuPricePO::getSourceCountryCode,customerMkuPriceVO.getSourceCountryCode())
                    .eq(StringUtils.isNotBlank(customerMkuPriceVO.getClientCode()),CustomerMkuPricePO::getClientCode,customerMkuPriceVO.getClientCode());
            return baseMapper.exists(wrapper);
        }
        throw new RuntimeException("CustomerMkuPriceAtomicService.exists illegal params error");
    }

    public List<CustomerMkuPricePO> queryMkuListsBySkuIdClientCode(Collection<Long> skuIds, String clientCode){
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .in(CustomerMkuPricePO::getFixedSkuId, skuIds)
                .eq(CustomerMkuPricePO::getClientCode, clientCode)
                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode());
        return baseMapper.selectList(wrapper);
    }

    public CustomerMkuPricePO getOne(Long mkuId,String clientCode){
        if(StringUtils.isBlank(clientCode) || mkuId == null){
            return null;
        }
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .eq(CustomerMkuPricePO::getClientCode, clientCode)
                .eq(CustomerMkuPricePO::getMkuId, mkuId)
                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode());
        return baseMapper.selectOne(wrapper);
    }

    public List<CustomerMkuPricePO> getListByMkuIds(Collection<Long> mkuIds,String clientCode){
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .in(CustomerMkuPricePO::getMkuId, mkuIds)
                .eq(CustomerMkuPricePO::getClientCode, clientCode)
                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode());
        return baseMapper.selectList(wrapper);
    }

    /** 根据mku和客户编码查询mku固定sku信息*/
    public Map<Long,CustomerMkuPricePO> queryByMkuAndClient(Set<Long> mkuIds, String clientCode){
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .in(CustomerMkuPricePO::getMkuId, mkuIds)
                .eq(CustomerMkuPricePO::getClientCode, clientCode)
                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode());
        List<CustomerMkuPricePO> list = baseMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(list)){
            return list.stream().collect(Collectors.toMap(CustomerMkuPricePO::getMkuId, Function.identity()));
        }
        return null;
    }

    public List<CustomerMkuPricePO> queryMkuListsBySkuId(Collection<Long> skuIds){
        if(CollectionUtils.isEmpty(skuIds)){
            return null;
        }
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .in(CustomerMkuPricePO::getFixedSkuId, skuIds)
                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode());
        return baseMapper.selectList(wrapper);
    }
}
