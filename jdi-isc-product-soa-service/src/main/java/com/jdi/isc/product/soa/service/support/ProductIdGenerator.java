package com.jdi.isc.product.soa.service.support;

import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.common.util.JimUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 商品id发号器
 */
@Service
@Slf4j
public class ProductIdGenerator {

    //todo 并发单元测试

    @Resource
    private JimUtils jimUtils;
    //spu自增key
    public final static String JDI_ISC_KEY_SPU = "jdi_isc_key_spu_test1";
    //sku自增key
    public final static String JDI_ISC_KEY_SKU = "jdi_isc_key_sku_test1";
    //mku自增key
    public final static String JDI_ISC_KEY_MKU = "jdi_isc_key_mku_test1";
    //spu自增初始值
    private final static Long SPU_START_INDEX = 20000000000L;
    //sku自增初始值
    private final static Long SKU_START_INDEX = 80000000000L;
    // mku自增初始值
    private final static Long MKU_START_INDEX = 50000000000L;

    private static final String MARKUP_RATE = "RATE";

    private static final String PRICE = "PRICE";

    /** 生成spuId*/
    public Long genSpuId(){
        return jimUtils.incr(JDI_ISC_KEY_SPU);
    }

    /** 批量生成spuId*/
    public List<Long> batchGenSpuId(Long size){
        return batchGen(JDI_ISC_KEY_SPU,size);
    }

    /** 生成skuId*/
    public Long genSkuId(){
        return jimUtils.incr(JDI_ISC_KEY_SKU);
    }

    /** 批量生成skuId*/
    public List<Long> batchGenSkuId(Long size){
        return batchGen(JDI_ISC_KEY_SKU,size);
    }

    /** 生成mkuId*/
    public Long genMkuId(){
        return jimUtils.incr(JDI_ISC_KEY_MKU);
    }

    /** 批量生成mkuId*/
    public List<Long> batchGenMkuId(Long size){
        return batchGen(JDI_ISC_KEY_MKU,size);
    }

    //自增key初始化
    public void init(){
        jimUtils.del(JDI_ISC_KEY_SPU);
        jimUtils.del(JDI_ISC_KEY_SKU);
        jimUtils.del(JDI_ISC_KEY_MKU);
        Long spuIndex = jimUtils.incrBy(JDI_ISC_KEY_SPU, SPU_START_INDEX);
        Long skuIndex = jimUtils.incrBy(JDI_ISC_KEY_SKU, SKU_START_INDEX);
        Long mkuIndex = jimUtils.incrBy(JDI_ISC_KEY_MKU, MKU_START_INDEX);
        log.info("ProductIdGenerator.init spuIndex:{} , skuIndex:{}, mkuIndex:{}" ,  spuIndex, skuIndex, mkuIndex);
    }

    /** 批量生成spuId*/
    private List<Long> batchGen(String key , Long size){
        List<Long> result = new ArrayList<>();
        Long end = jimUtils.incrBy(key, size);
        for (long i = (end-size+1); i <= end; i++) {
            result.add(i);
        }
        return result;
    }


    public String getMarkupRateBizNo(){
        // 2312201451（分钟）+ 00001（数据库自增）
        String day = DateUtil.formatDate(new Date(), "yyMMddHHmm");
        String key = CacheKeyConstant.getKey(CacheKeyConstant.MARKUP_RATE_BIZ_NO, day);
        List<String> bizNo = getBizNo(1L,day,key,MARKUP_RATE);
        return bizNo.get(0);
    }

    public String getAgreementPriceBizNo(){
        // 2312201451（分钟）+ 00001（数据库自增）
        String day = DateUtil.formatDate(new Date(), "yyMMddHHmm");
        String key = CacheKeyConstant.getKey(CacheKeyConstant.AGREEMENT_PRICE_BIZ_NO, day);
        List<String> bizNo = getBizNo(1L,day,key,PRICE);
        return bizNo.get(0);
    }

    public Long getOutUrlTaskBizNo(){
        // 2312201451（分钟）+ 00001（数据库自增）
        String day = DateUtil.formatDate(new Date(), "yyMMddHHmm");
        String key = CacheKeyConstant.getKey(CacheKeyConstant.OUT_URL_TASK_BIZ_NO, day);
        List<String> bizNo = getBizNo(1L,day,key,"");
        return Long.valueOf(bizNo.get(0));
    }

    private List<String> getBizNo(Long size,String day,String key,String prefix ) {
        Long value = jimUtils.incrBy(key, size);
        if(value == null){
            return null;
        }
        jimUtils.expire(key, 10, TimeUnit.MINUTES);
        List<String> ids = new ArrayList<>();
        for(long index = value - size + 1; index <= value; index++){
            if(index > 99999){
                ids.add(prefix + day + String.valueOf(index));
            }else{
                ids.add(prefix + (Long.parseLong(day + "00000") + index));
            }
        }
        return ids;
    }

    public static void main(String[] args) {
        String str = "250421205200001";
        System.out.println(str);
        System.out.println(Long.valueOf(str));
    }

}
