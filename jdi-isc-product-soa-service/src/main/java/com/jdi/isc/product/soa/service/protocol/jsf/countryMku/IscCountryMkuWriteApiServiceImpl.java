package com.jdi.isc.product.soa.service.protocol.jsf.countryMku;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.countryMku.IscCountryMkuWriteApiService;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuApproveDTO;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuDTO;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuReqDTO;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuUpdateStatusDTO;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuApproveVO;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuReqVO;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuVO;
import com.jdi.isc.product.soa.service.manage.countryMku.CountryMkuManageService;
import com.jdi.isc.product.soa.service.mapstruct.countryMku.CountryMkuConvert;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;

/**
 * @Description: 商品国家池写服务
 * @Author: wangpeng965
 * @Date: 2024/12/02 21:55
 **/
@Slf4j
@Service
public class IscCountryMkuWriteApiServiceImpl implements IscCountryMkuWriteApiService {

    @Resource
    private CountryMkuManageService countryMkuManageService;

    final ExecutorService pool = new ThreadPoolExecutor(10, 10, 30L, TimeUnit.MICROSECONDS,
        new ArrayBlockingQueue<>(100000), new ThreadFactoryBuilder().setNamePrefix("country-pool").build(), new ThreadPoolExecutor.CallerRunsPolicy());


    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> mkuPoolJoinCountryPool(CountryMkuReqDTO dto) {
        log.info("IscCountryMkuWriteApiServiceImpl.mkuPoolJoinCountryPool param:{}", JSON.toJSONString(dto));
        CountryMkuReqVO countryMkuReqVO = CountryMkuConvert.INSTANCE.dto2Vo(dto);
        CompletableFuture.runAsync(() -> countryMkuManageService.mkuPoolJoinCountryPool(countryMkuReqVO),pool);
        return DataResponse.success(true);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> customerPoolJoinCountryPool(CountryMkuReqDTO dto) {
        log.info("IscCountryMkuWriteApiServiceImpl.customerPoolJoinCountryPool param:{}", JSON.toJSONString(dto));
        CountryMkuReqVO countryMkuReqVO = CountryMkuConvert.INSTANCE.dto2Vo(dto);
        return countryMkuManageService.customerPoolJoinCountryPool(countryMkuReqVO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> mkuMsgJoinCountryPool(CountryMkuReqDTO dto) {
        log.info("IscCountryMkuWriteApiServiceImpl.mkuMsgJoinCountryPool param:{}", JSON.toJSONString(dto));
        CountryMkuReqVO countryMkuReqVO = CountryMkuConvert.INSTANCE.dto2Vo(dto);
        return countryMkuManageService.mkuMsgJoinCountryPool(countryMkuReqVO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> jdSkuIdMsgJoinCountryPool(CountryMkuReqDTO dto) {
        log.info("IscCountryMkuWriteApiServiceImpl.jdSkuIdMsgJoinCountryPool param:{}", JSON.toJSONString(dto));
        CountryMkuReqVO countryMkuReqVO = CountryMkuConvert.INSTANCE.dto2Vo(dto);
        return countryMkuManageService.jdSkuIdMsgJoinCountryPool(countryMkuReqVO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchBlack(CountryMkuApproveDTO input) {
        log.info("IscCountryMkuWriteApiServiceImpl.batchBlack param:{}", JSON.toJSONString(input));
        ApiInitUtils.init(input);
        CountryMkuApproveVO param = CountryMkuConvert.INSTANCE.approveDto2Vo(input);
        return countryMkuManageService.batchBlack(param);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchOutBlack(CountryMkuApproveDTO input) {
        log.info("IscCountryMkuWriteApiServiceImpl.batchOutBlack param:{}", JSON.toJSONString(input));
        ApiInitUtils.init(input);
        CountryMkuApproveVO param = CountryMkuConvert.INSTANCE.approveDto2Vo(input);
        return countryMkuManageService.batchOutBlack(param);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchPool(CountryMkuApproveDTO input) {
        log.info("IscCountryMkuWriteApiServiceImpl.batchPool param:{}", JSON.toJSONString(input));
        ApiInitUtils.init(input);
        CountryMkuApproveVO param = CountryMkuConvert.INSTANCE.approveDto2Vo(input);
        return countryMkuManageService.batchPool(param);
    }

    @Override
    public DataResponse<String> delete(Long id) {
        countryMkuManageService.delete(id);
        return DataResponse.success();
    }

    @Override
    public DataResponse<String> batchDelete(Long beginId,Long endId) {
        countryMkuManageService.batchDelete(beginId,endId);
        return DataResponse.success();
    }

    @Override
    public DataResponse<String> saveOrUpdate(CountryMkuDTO input) {
        ApiInitUtils.init(input);
        countryMkuManageService.saveOrUpdate(CountryMkuConvert.INSTANCE.saveDto2Vo(input));
        return DataResponse.success();
    }

    /**
     * 新增MKU加入国家池的请求处理。
     * @param dto 包含国家信息和MKU信息的请求对象。
     * @return 处理结果，true表示成功，false表示失败。
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> anewMkuJoinCountryPool(CountryMkuReqDTO dto) {
        log.info("IscCountryMkuWriteApiServiceImpl.anewMkuJoinCountryPool param:{}", JSON.toJSONString(dto));
        CountryMkuReqVO countryMkuReqVO = CountryMkuConvert.INSTANCE.dto2Vo(dto);
        return countryMkuManageService.anewMkuJoinCountryPool(countryMkuReqVO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> ruleCheckCountryMku(String countryCode, Integer warnStatus) {
        log.info("IscCountryMkuWriteApiServiceImpl.anewMkuJoinCountryPool countryCode:{},warnStatus:{}", JSON.toJSONString(countryCode),warnStatus);
        return  countryMkuManageService.ruleCheckCountryMku(countryCode,warnStatus);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> updateMkuStatus(CountryMkuUpdateStatusDTO input) {
        ApiInitUtils.init(input);
        List<String> spuIdMsg = new ArrayList<>();
        for(Long id : input.getIdList()){
            CountryMkuVO countryMkuVO = countryMkuManageService.detail(id);
            if(Objects.nonNull(countryMkuVO)){
                countryMkuVO.setCountryMkuStatus(input.getCountryMkuStatus());
                countryMkuVO.setDownReason(input.getDownReason());
                countryMkuVO.setUpdater(input.getPin());
                countryMkuVO.setUpdateTime(new Date().getTime());
                DataResponse<String> response = countryMkuManageService.saveOrUpdate(countryMkuVO);
                if(!response.getSuccess()){
                    spuIdMsg.add(countryMkuVO.getMkuId() + ":" + response.getMessage());
                }
            }
        }
        if(CollectionUtils.isEmpty(spuIdMsg)){
            return DataResponse.success();
        }
        return DataResponse.error(String.join(",", spuIdMsg));
    }
}
