package com.jdi.isc.product.soa.service.atomic.saleAttribute;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeValueVO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeValueLangPO;
import com.jdi.isc.product.soa.repository.mapper.saleAttribute.SaleAttributeValueLangBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 销售属性值多语表原子服务
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
public class SaleAttributeValueLangAtomicService extends ServiceImpl<SaleAttributeValueLangBaseMapper, SaleAttributeValueLangPO> {

    /**
     * 保存销售属性值多语言到数据库
     */
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public void saveSaleAttributeValueLangToDB(List<SaleAttributeValueVO> saleAttributeValueVOList,
                                               Map<String, Map<String, String>> translateResult) {
        log.info("SaleAttributeValueLangAtomicService.saveSaleAttributeValueLangToDB 开始保存销售属性值多语言, 入参数量: {}", 
                CollectionUtils.isEmpty(saleAttributeValueVOList) ? 0 : saleAttributeValueVOList.size());
                
        if (CollectionUtils.isEmpty(saleAttributeValueVOList) || MapUtils.isEmpty(translateResult)) {
            return;
        }

        // 1. 收集所有需要检查的valueId和lang组合
        List<String> checkPairs = new ArrayList<>();
        Map<String, SaleAttributeValueLangPO> newLangPOMap = new HashMap<>(); // key: "valueId_lang", value: 待保存的PO
        
        for (SaleAttributeValueVO valueVO : saleAttributeValueVOList) {
            String saleAttributeValueName = valueVO.getSaleAttributeValueName();
            Map<String, String> langTranslations = translateResult.get(saleAttributeValueName);

            if (MapUtils.isNotEmpty(langTranslations)) {
                for (Map.Entry<String, String> langEntry : langTranslations.entrySet()) {
                    String lang = langEntry.getKey();
                    String langName = langEntry.getValue();

                    if (StringUtils.isNotBlank(langName) && valueVO.getId() != null) {
                        String checkKey = valueVO.getId() + Constant.UNDER_LINE + lang;
                        checkPairs.add(checkKey);
                        
                        // 预创建PO对象
                        SaleAttributeValueLangPO langPO = getSaleAttributeValueLangPO(valueVO.getId(), lang, langName);
                        newLangPOMap.put(checkKey, langPO);
                    }
                }
            }
        }

        if (CollectionUtils.isEmpty(checkPairs)) {
            log.info("SaleAttributeValueLangAtomicService.saveSaleAttributeValueLangToDB 没有需要保存的多语言数据");
            return;
        }

        // 2. 批量检查已存在的数据
        Map<String, SaleAttributeValueLangPO> existingMap = this.getValidBySaleAttributeValueIdAndLangBatch(checkPairs);
        log.info("SaleAttributeValueLangAtomicService.saveSaleAttributeValueLangToDB 批量检查完成, 已存在数量: {}", existingMap.size());

        // 3. 过滤出不存在的数据进行保存
        List<SaleAttributeValueLangPO> langPOList = new ArrayList<>();
        for (String checkKey : checkPairs) {
            if (!existingMap.containsKey(checkKey)) {
                SaleAttributeValueLangPO langPO = newLangPOMap.get(checkKey);
                if (langPO != null) {
                    langPOList.add(langPO);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(langPOList)) {
            this.saveBatch(langPOList);
            log.info("SaleAttributeValueLangAtomicService.saveSaleAttributeValueLangToDB 保存销售属性值多语言到数据库成功，数量: {}", langPOList.size());
        } else {
            log.info("SaleAttributeValueLangAtomicService.saveSaleAttributeValueLangToDB 所有多语言数据已存在，无需保存");
        }
    }

    @NotNull
    public static SaleAttributeValueLangPO getSaleAttributeValueLangPO(Long saleAttributeValueId, String lang, String langName) {
        SaleAttributeValueLangPO langPO = new SaleAttributeValueLangPO();
        langPO.setSaleAttributeValueId(saleAttributeValueId);
        langPO.setLang(lang);
        langPO.setLangName(langName);
        langPO.setYn(YnEnum.YES.getCode());
        long timestamp = new Date().getTime();
        langPO.setCreateTime(timestamp);
        langPO.setUpdateTime(timestamp);
        // 如果登录上下文为空，则设置创建者和更新者为系统
        if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
            langPO.setCreator(Constant.PIN_SYSTEM);
            langPO.setUpdater(Constant.PIN_SYSTEM);
        }
        return langPO;
    }

    /**
     * 根据ID获取有效的销售属性值多语言
     * @param id 销售属性值多语言ID
     * @return 销售属性值多语言对象
     */
    public SaleAttributeValueLangPO getValidById(Long id) {
        if (id == null) {
            return null;
        }
        LambdaQueryWrapper<SaleAttributeValueLangPO> wrapper = Wrappers.<SaleAttributeValueLangPO>lambdaQuery()
                .eq(SaleAttributeValueLangPO::getId, id)
                .eq(SaleAttributeValueLangPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    /**
     * 根据销售属性值ID获取有效的多语言列表
     * @param saleAttributeValueId 销售属性值ID
     * @return 多语言列表
     */
    public List<SaleAttributeValueLangPO> getValidBySaleAttributeValueId(Long saleAttributeValueId) {
        if (saleAttributeValueId == null) {
            return null;
        }
        LambdaQueryWrapper<SaleAttributeValueLangPO> wrapper = Wrappers.<SaleAttributeValueLangPO>lambdaQuery()
                .eq(SaleAttributeValueLangPO::getSaleAttributeValueId, saleAttributeValueId)
                .eq(SaleAttributeValueLangPO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }

    /**
     * 批量根据销售属性值ID列表获取有效的多语言列表
     * @param saleAttributeValueIds 销售属性值ID列表
     * @return 多语言列表
     */
    public List<SaleAttributeValueLangPO> getValidBySaleAttributeValueIds(List<Long> saleAttributeValueIds) {
        if (saleAttributeValueIds == null || saleAttributeValueIds.isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<SaleAttributeValueLangPO> wrapper = Wrappers.<SaleAttributeValueLangPO>lambdaQuery()
                .in(SaleAttributeValueLangPO::getSaleAttributeValueId, saleAttributeValueIds)
                .eq(SaleAttributeValueLangPO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }

    /**
     * 根据销售属性值ID和语言获取有效的多语言
     * @param saleAttributeValueId 销售属性值ID
     * @param lang 语言编码
     * @return 多语言对象
     */
    public SaleAttributeValueLangPO getValidBySaleAttributeValueIdAndLang(Long saleAttributeValueId, String lang) {
        if (saleAttributeValueId == null || lang == null) {
            return null;
        }
        LambdaQueryWrapper<SaleAttributeValueLangPO> wrapper = Wrappers.<SaleAttributeValueLangPO>lambdaQuery()
                .eq(SaleAttributeValueLangPO::getSaleAttributeValueId, saleAttributeValueId)
                .eq(SaleAttributeValueLangPO::getLang, lang)
                .eq(SaleAttributeValueLangPO::getYn, YnEnum.YES.getCode())
                .last("LIMIT 1");
        return super.getOne(wrapper);
    }

    /**
     * 批量根据销售属性值ID和语言获取有效的多语言
     * @param saleAttributeValueIdAndLangPairs 销售属性值ID和语言的组合列表，格式："valueId_lang"
     * @return Map<String, SaleAttributeValueLangPO> key:"valueId_lang", value:多语言对象
     */
    public Map<String, SaleAttributeValueLangPO> getValidBySaleAttributeValueIdAndLangBatch(List<String> saleAttributeValueIdAndLangPairs) {
        if (CollectionUtils.isEmpty(saleAttributeValueIdAndLangPairs)) {
            return new HashMap<>();
        }
        
        // 解析输入参数，构建查询条件
        List<Long> valueIds = new ArrayList<>();
        List<String> langs = new ArrayList<>();
        Map<String, String> keyMapping = new HashMap<>(); // 用于映射回原始key
        
        for (String pair : saleAttributeValueIdAndLangPairs) {
            if (StringUtils.isNotBlank(pair) && pair.contains(Constant.UNDER_LINE)) {
                // 修改代码，以Constant.UNDER_LINE为分界，找到第一个Constant.UNDER_LINE，然后将字符串分成两部分
                int firstUnderscoreIndex = pair.indexOf(Constant.UNDER_LINE);
                if (firstUnderscoreIndex == -1) {
                    continue;
                }
                String valueIdStr = pair.substring(0, firstUnderscoreIndex);
                String lang = pair.substring(firstUnderscoreIndex + 1);
                if (StringUtils.isNotBlank(valueIdStr) && StringUtils.isNotBlank(lang)) {
                    try {
                        Long valueId = Long.valueOf(valueIdStr);
                        if (valueId != null) {
                            valueIds.add(valueId);
                            langs.add(lang);
                            keyMapping.put(valueId + Constant.UNDER_LINE + lang, pair);
                        }
                    } catch (NumberFormatException e) {
                        log.warn("SaleAttributeValueLangAtomicService.getValidBySaleAttributeValueIdAndLangBatch 无效的valueId格式: {}", pair);
                    }
                }
            }
        }
        
        if (CollectionUtils.isEmpty(valueIds)) {
            return new HashMap<>();
        }
        
        // 批量查询
        LambdaQueryWrapper<SaleAttributeValueLangPO> wrapper = Wrappers.<SaleAttributeValueLangPO>lambdaQuery()
                .in(SaleAttributeValueLangPO::getSaleAttributeValueId, valueIds)
                .in(SaleAttributeValueLangPO::getLang, langs)
                .eq(SaleAttributeValueLangPO::getYn, YnEnum.YES.getCode());
        
        List<SaleAttributeValueLangPO> results = this.list(wrapper);
        
        // 构建返回结果
        Map<String, SaleAttributeValueLangPO> resultMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(results)) {
            for (SaleAttributeValueLangPO result : results) {
                String key = result.getSaleAttributeValueId() + Constant.UNDER_LINE + result.getLang();
                String originalKey = keyMapping.get(key);
                if (StringUtils.isNotBlank(originalKey)) {
                    resultMap.put(originalKey, result);
                }
            }
        }
        
        log.info("SaleAttributeValueLangAtomicService.getValidBySaleAttributeValueIdAndLangBatch 批量查询完成, 输入: {}, 输出: {}", 
                saleAttributeValueIdAndLangPairs.size(), resultMap.size());
        
        return resultMap;
    }
}