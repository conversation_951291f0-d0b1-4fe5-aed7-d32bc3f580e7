package com.jdi.isc.product.soa.service.manage.stock;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockSplitReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockOccupyResDTO;
import com.jdi.isc.product.soa.api.stock.res.StockResDTO;
import com.jdi.isc.product.soa.domain.stock.po.StockPO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 库存业务读写服务
 * <AUTHOR>
 * @date 2024/6/6
 */
public interface StockManageService {

    /** 库存预占*/
    @Deprecated
    DataResponse<Boolean> occupy(StockManageReqDTO req);

    /** 预占释放*/
    @Deprecated
    DataResponse<Boolean> release(StockManageReqDTO req);

    /** 预占回退*/
    @Deprecated
    DataResponse<Boolean> occupyReturn(StockManageReqDTO req);

    /** 现货回退*/
    @Deprecated
    DataResponse<Boolean> stockReturn(StockManageReqDTO req);

    /** 库存初始化*/
    DataResponse<Boolean> saveOrUpdate(StockManageReqDTO req);

    /** 根据工业国际skuId查询库存信息*/
    Map<Long, StockResDTO> getStock(StockManageReqDTO req);

    Boolean batchRemoveStockByIds(Set<Long> ids);

    /** 库存流水清空*/
    DataResponse<Integer> truncate();

    DataResponse<Boolean> resetStock(Set<Long> skuId);

    /** 新库存预占*/
    DataResponse<StockOccupyResDTO> occupyStock(StockManageReqDTO req);

    /**现货扣减**/
    DataResponse<Boolean> releaseStock(StockManageReqDTO req);

    /** 新预占回退*/
    DataResponse<Boolean> occupyStockReturn(StockManageReqDTO req);

    /**预占扣减现货库存**/
    DataResponse<StockOccupyResDTO> occupyRelease(StockManageReqDTO req);

    /** 新现货回退*/
    DataResponse<Boolean> stockNewReturn(StockManageReqDTO req);

    /**
     * 在途库存退回并返回操作结果。
     * @param req 库存回退请求对象，包含调拨的商品信息，仓库信息。
     * @return 调拨操作的结果，true表示成功，false表示失败。
     */
    DataResponse<Boolean> transitStockReturn(StockManageReqDTO req);
    /**在途库存转现货库存**/
    DataResponse<Boolean> transitStockToStock(StockManageReqDTO req);

    /** 将拆单的父单流水拆分到子单上，后续子单取消回退逻辑使用**/
    DataResponse<Boolean> splitStock(StockSplitReqDTO req);

    /** 将拆采购单的父单流水拆分到子采购单上，后续子采购单取消回退逻辑使用**/
    DataResponse<Boolean> splitPurchaseOrderStock(StockSplitReqDTO req);

    /**
     * 根据商品SKU ID和仓库ID获取仓库库存信息
     * @param skuId 商品SKU ID
     * @param warehouseId 仓库ID
     * @return 包含库存信息的StockPO对象
     */
    StockPO getWarehouseStock(Long skuId, Long warehouseId);


    /**
     * 批量获取仓库库存信息
     * @param stockItemManageReqDTOList 库存管理请求DTO列表，包含查询条件信息
     * @return 返回库存持久化对象列表，包含查询到的仓库库存信息
     */
    List<StockPO> batchGetWarehouseStock(List<StockItemManageReqDTO> stockItemManageReqDTOList);


    /**
     * 批量获取工厂库存信息
     * @param skuIds 需要查询的SKU ID集合
     * @return 返回对应SKU的工厂库存信息列表
     */
    List<StockPO> batchGetFactoryStock(Set<Long> skuIds);

}
