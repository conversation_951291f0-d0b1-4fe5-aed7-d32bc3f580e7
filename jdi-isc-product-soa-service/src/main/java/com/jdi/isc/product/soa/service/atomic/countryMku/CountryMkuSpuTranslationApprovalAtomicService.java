package com.jdi.isc.product.soa.service.atomic.countryMku;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuSpuTranslationApprovalPO;
import com.jdi.isc.product.soa.repository.jed.mapper.countryMku.CountryMkuSpuTranslationApprovalBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 国际池商品名称翻译审核表原子服务
 * @Author: zhaoyan316
 * @Date: 2025-08-26
 **/
@Slf4j
@Service
public class CountryMkuSpuTranslationApprovalAtomicService extends ServiceImpl<CountryMkuSpuTranslationApprovalBaseMapper, CountryMkuSpuTranslationApprovalPO> {

    /**
     * 根据 SPU ID 和目标国家代码获取 CountryMkuSpuTranslationApprovalPO 对象。
     * @param spuId SPU ID
     * @param targetCountryCode 目标国家代码
     * @return CountryMkuSpuTranslationApprovalPO 对象
     */
    public CountryMkuSpuTranslationApprovalPO getBySpuIdAndCountryCode(Long spuId, String targetCountryCode){
        if(spuId == null || StringUtils.isBlank(targetCountryCode)){
            return null;
        }
        LambdaQueryWrapper<CountryMkuSpuTranslationApprovalPO> qw = new LambdaQueryWrapper<>();
        qw.eq(CountryMkuSpuTranslationApprovalPO::getSpuId, spuId);
        qw.eq(CountryMkuSpuTranslationApprovalPO::getTargetCountryCode, targetCountryCode);
        qw.eq(CountryMkuSpuTranslationApprovalPO::getYn, YnEnum.YES.getCode());
        List<CountryMkuSpuTranslationApprovalPO> list = this.list(qw);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    /**
     * 根据 SPU ID 列表和目标国家代码获取 CountryMkuSpuTranslationApprovalPO 对象列表。
     * @param spuIds SPU ID 列表
     * @param targetCountryCode 目标国家代码
     * @return 满足条件的 CountryMkuSpuTranslationApprovalPO 对象列表
     */
    public List<CountryMkuSpuTranslationApprovalPO> getBySpuIdListAndCountryCode(List<Long> spuIds, String targetCountryCode){
        if(CollectionUtils.isEmpty(spuIds) || StringUtils.isBlank(targetCountryCode)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CountryMkuSpuTranslationApprovalPO> wrapper = Wrappers.<CountryMkuSpuTranslationApprovalPO>lambdaQuery()
            .in(CollectionUtils.isNotEmpty(spuIds), CountryMkuSpuTranslationApprovalPO::getSpuId, spuIds)
            .eq(CountryMkuSpuTranslationApprovalPO::getTargetCountryCode, targetCountryCode)
            .eq(CountryMkuSpuTranslationApprovalPO::getYn, YnEnum.YES.getCode());
        List<CountryMkuSpuTranslationApprovalPO> list = super.list(wrapper);
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 根据 SPU ID 列表获取 CountryMkuSpuTranslationApprovalPO 对象列表。
     * @param spuIds SPU ID 列表
     * @return CountryMkuSpuTranslationApprovalPO 列表
     */
    public List<CountryMkuSpuTranslationApprovalPO> getBySpuIds(Collection<Long> spuIds){
        if(CollectionUtils.isEmpty(spuIds)){
            return null;
        }
        LambdaQueryWrapper<CountryMkuSpuTranslationApprovalPO> qw = new LambdaQueryWrapper<>();
        qw.in(CountryMkuSpuTranslationApprovalPO::getSpuId, spuIds);
        qw.eq(CountryMkuSpuTranslationApprovalPO::getYn, YnEnum.YES.getCode());
        return this.list(qw);
    }

    /**
     * 根据审核状态获取 CountryMkuSpuTranslationApprovalPO 对象列表。
     * @param approvalStatus 审核状态
     * @return CountryMkuSpuTranslationApprovalPO 列表
     */
    public List<CountryMkuSpuTranslationApprovalPO> getByApprovalStatus(Boolean approvalStatus){
        LambdaQueryWrapper<CountryMkuSpuTranslationApprovalPO> qw = new LambdaQueryWrapper<>();
        qw.eq(CountryMkuSpuTranslationApprovalPO::getApprovalStatus, approvalStatus);
        qw.eq(CountryMkuSpuTranslationApprovalPO::getYn, YnEnum.YES.getCode());
        return this.list(qw);
    }

    /**
     * 根据目标国家代码和审核状态获取 CountryMkuSpuTranslationApprovalPO 对象列表。
     * @param targetCountryCode 目标国家代码
     * @param approvalStatus 审核状态
     * @return CountryMkuSpuTranslationApprovalPO 列表
     */
    public List<CountryMkuSpuTranslationApprovalPO> getByCountryCodeAndApprovalStatus(String targetCountryCode, Boolean approvalStatus){
        if(StringUtils.isBlank(targetCountryCode)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CountryMkuSpuTranslationApprovalPO> wrapper = Wrappers.<CountryMkuSpuTranslationApprovalPO>lambdaQuery()
            .eq(CountryMkuSpuTranslationApprovalPO::getTargetCountryCode, targetCountryCode)
            .eq(CountryMkuSpuTranslationApprovalPO::getApprovalStatus, approvalStatus)
            .eq(CountryMkuSpuTranslationApprovalPO::getYn, YnEnum.YES.getCode());
        List<CountryMkuSpuTranslationApprovalPO> list = super.list(wrapper);
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 写入待审核spu（单个）
     * @param spuId SPU ID
     * @param targetCountryCode 目标国家代码
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addNeedApprovalSpu(Long spuId, String targetCountryCode){
        if(spuId == null || StringUtils.isBlank(targetCountryCode)){
            log.info("CountryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu spuId or targetCountryCode is null spuId:{}，targetCountryCode:{}", spuId, targetCountryCode);
            return false;
        }
        CountryMkuSpuTranslationApprovalPO po = this.getBySpuIdAndCountryCode(spuId, targetCountryCode);
        if(po != null){
            log.info("CountryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu spuId:{}，targetCountryCode:{} already exists, po.approvalStatus:{}", spuId, targetCountryCode, po.getApprovalStatus());
            return false;
        }
        po = getCountryMkuSpuTranslationNeedApprovalPO(spuId, targetCountryCode);
        log.info("CountryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu spuId:{}，targetCountryCode:{}", spuId, targetCountryCode);
        return super.save(po);
    }

    @NotNull
    private static CountryMkuSpuTranslationApprovalPO getCountryMkuSpuTranslationNeedApprovalPO(Long spuId, String targetCountryCode) {
        CountryMkuSpuTranslationApprovalPO po = new CountryMkuSpuTranslationApprovalPO();
        po.setSpuId(spuId);
        po.setTargetCountryCode(targetCountryCode);
        long currentTime = System.currentTimeMillis();
        po.setCreateTime(currentTime);
        po.setUpdateTime(currentTime);
        // 如果登录上下文为空，则设置创建者和更新者为系统
        if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
           po.setCreator(Constant.PIN_SYSTEM);
           po.setUpdater(Constant.PIN_SYSTEM);
       }
        po.setApprovalStatus(Boolean.FALSE);
        po.setYn(YnEnum.YES.getCode());
        return po;
    }

    /**
     * 写入待审核spu（批量）
     * @param spuIds SPU ID 列表
     * @param targetCountryCode 目标国家代码
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addNeedApprovalSpuBatch(List<Long> spuIds, String targetCountryCode){
        if(CollectionUtils.isEmpty(spuIds) || StringUtils.isBlank(targetCountryCode)){
            return false;
        }   
        log.info("CountryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpuBatch 过滤前 spuIds:{}，targetCountryCode:{}", spuIds, targetCountryCode);
        List<CountryMkuSpuTranslationApprovalPO> poList = this.getBySpuIdListAndCountryCode(spuIds, targetCountryCode);

        if(CollectionUtils.isNotEmpty(poList)){
            // 去除已存在spuId
            spuIds.removeAll(poList.stream().map(CountryMkuSpuTranslationApprovalPO::getSpuId).collect(Collectors.toList()));
        }
        List<CountryMkuSpuTranslationApprovalPO> list = new ArrayList<>();
        for(Long spuId : spuIds){
            list.add(getCountryMkuSpuTranslationNeedApprovalPO(spuId, targetCountryCode));
        }
        if(CollectionUtils.isEmpty(list)){
            return false;
        }
        log.info("CountryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpuBatch 过滤后 spuIds:{}，targetCountryCode:{}", spuIds, targetCountryCode);
        return super.saveBatch(list);
    }

    /**
     * 单个审核通过
     * @param spuId SPU ID
     * @param targetCountryCode 目标国家代码
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean approvalSpu(Long spuId, String targetCountryCode){
        if(spuId == null || StringUtils.isBlank(targetCountryCode)){
            log.info("CountryMkuSpuTranslationApprovalAtomicService.approvalSpu spuId or targetCountryCode is null spuId:{}，targetCountryCode:{}", spuId, targetCountryCode);
            throw new BizException("spuId or targetCountryCode is null");
        }
        CountryMkuSpuTranslationApprovalPO po = this.getBySpuIdAndCountryCode(spuId, targetCountryCode);
        if(po == null){
            log.error("CountryMkuSpuTranslationApprovalAtomicService.approvalSpu 待审核spu不存在！ spuId:{}，targetCountryCode:{} not found", spuId, targetCountryCode);
            throw new BizException("待审核spu在翻译确认表中不存在");
        }
        if(po.getApprovalStatus()){
            log.info("CountryMkuSpuTranslationApprovalAtomicService.approvalSpu 待审核spu已经是审核通过状态 spuId:{}，targetCountryCode:{} already approved", spuId, targetCountryCode);
            return false;
        }
        LambdaUpdateWrapper<CountryMkuSpuTranslationApprovalPO> wrapper = Wrappers.<CountryMkuSpuTranslationApprovalPO>lambdaUpdate();
        wrapper.eq(CountryMkuSpuTranslationApprovalPO::getSpuId, spuId);
        setWrapper(targetCountryCode, wrapper);
        log.info("CountryMkuSpuTranslationApprovalAtomicService.approvalSpu 审核通过 spuId:{}，targetCountryCode:{}", spuId, targetCountryCode);
        return super.update(wrapper);
    }

    private static void setWrapper(String targetCountryCode, LambdaUpdateWrapper<CountryMkuSpuTranslationApprovalPO> wrapper) {
        wrapper.eq(CountryMkuSpuTranslationApprovalPO::getTargetCountryCode, targetCountryCode);
        wrapper.eq(CountryMkuSpuTranslationApprovalPO::getApprovalStatus, Boolean.FALSE);
        // 设置更新时间
        long currentTime = System.currentTimeMillis();
        wrapper.set(CountryMkuSpuTranslationApprovalPO::getUpdateTime, currentTime);
        // 设置更新者
        if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
            wrapper.set(CountryMkuSpuTranslationApprovalPO::getUpdater, Constant.PIN_SYSTEM);
        }
        // 设置审核通过状态
        wrapper.set(CountryMkuSpuTranslationApprovalPO::getApprovalStatus, Boolean.TRUE);
        wrapper.eq(CountryMkuSpuTranslationApprovalPO::getYn, YnEnum.YES.getCode());
    }

    /**
     * 批量审核通过
     * @param spuIds SPU ID 列表
     * @param targetCountryCode 目标国家代码
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean approvalSpuBatch(List<Long> spuIds, String targetCountryCode){
        if(CollectionUtils.isEmpty(spuIds) || StringUtils.isBlank(targetCountryCode)){
            return false;
        }
        log.info("CountryMkuSpuTranslationApprovalAtomicService.approvalSpuBatch 过滤前 spuIds:{}，targetCountryCode:{}", spuIds, targetCountryCode);
        List<CountryMkuSpuTranslationApprovalPO> poList = this.getBySpuIdListAndCountryCode(spuIds, targetCountryCode);
        // 找出不存在的spuId
        List<Long> notExistSpuIds = spuIds.stream().filter(spuId -> poList.stream().noneMatch(po -> po.getSpuId().equals(spuId))).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notExistSpuIds)){
            log.error("CountryMkuSpuTranslationApprovalAtomicService.approvalSpuBatch 待审核spu包含不存在的spuId！ spuIds:{}，targetCountryCode:{}", notExistSpuIds, targetCountryCode);
            // 去除不存在的spuId
            spuIds.removeAll(notExistSpuIds);
        }
        // 找出已存在的并且审核已通过的spuId
        List<Long> existApprovedSpuIds = poList.stream().filter(po -> po.getApprovalStatus() && po.getYn().equals(YnEnum.YES.getCode())).map(CountryMkuSpuTranslationApprovalPO::getSpuId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(existApprovedSpuIds)){
            log.info("CountryMkuSpuTranslationApprovalAtomicService.approvalSpuBatch 待审核spu包含已通过审核的spuId！ spuIds:{}，targetCountryCode:{}", existApprovedSpuIds, targetCountryCode);
            // 去除已通过审核的spuId
            spuIds.removeAll(existApprovedSpuIds);
        }
        if(CollectionUtils.isEmpty(spuIds)){
            log.error("CountryMkuSpuTranslationApprovalAtomicService.approvalSpuBatch 过滤掉不存在的spu和已通过审核的spuId后 spuIds为空！ spuIds:{}，targetCountryCode:{}", spuIds, targetCountryCode);
            return false;
        }
        LambdaUpdateWrapper<CountryMkuSpuTranslationApprovalPO> wrapper = Wrappers.<CountryMkuSpuTranslationApprovalPO>lambdaUpdate();
        wrapper.in(CountryMkuSpuTranslationApprovalPO::getSpuId, spuIds);
        setWrapper(targetCountryCode, wrapper);
        log.info("CountryMkuSpuTranslationApprovalAtomicService.approvalSpuBatch 过滤后 spuIds:{}，targetCountryCode:{}", spuIds, targetCountryCode);
        return super.update(wrapper);
    }

    /**
     * 判断是否已经审核通过
     * 如果spuId或targetCountryCode为空，则返回false
     * 如果待审核spu不存在，则返回false
     * 如果待审核spu存在，则返回待审核spu的审核状态
     * 
     * @param spuId SPU ID
     * @param targetCountryCode 目标国家代码
     * @return 是否已经审核通过
     */
    public boolean isApprovalSpu(Long spuId, String targetCountryCode){
        if(spuId == null || StringUtils.isBlank(targetCountryCode)){
            return false;
        }
        CountryMkuSpuTranslationApprovalPO po = this.getBySpuIdAndCountryCode(spuId, targetCountryCode);
        if(po == null){
            log.error("CountryMkuSpuTranslationApprovalAtomicService.isApprovalSpu 待审核spu不存在！ spuId:{}，targetCountryCode:{} not found", spuId, targetCountryCode);
            return false;
        }
        return po.getApprovalStatus();
    }
}
