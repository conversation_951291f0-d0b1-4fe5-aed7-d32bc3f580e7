package com.jdi.isc.product.soa.service.manage.countryMku.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.jd.k2.customer.pool.core.dto.JdiSaveCustomerPoolResp;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.api.common.CurrencyConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.PurchaseModelTypeEnum;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.*;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.domain.brand.po.BrandCountryPO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryComboBoxVO;
import com.jdi.isc.product.soa.domain.category.po.CategoryCountryPO;
import com.jdi.isc.product.soa.domain.common.biz.BaseErpVO;
import com.jdi.isc.product.soa.domain.countryMku.biz.*;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerMku.biz.CustomerMkuVO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.domain.enums.countryMku.*;
import com.jdi.isc.product.soa.domain.gd.GdPoolVO;
import com.jdi.isc.product.soa.domain.gms.req.JdProductQueryDTO;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuClientInPoolVO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuClientUpSaleStatusVO;
import com.jdi.isc.product.soa.domain.mku.po.MkuLangPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.mkuTag.biz.MkuTagIdVO;
import com.jdi.isc.product.soa.domain.mkuTag.biz.MkuTagPageVO;
import com.jdi.isc.product.soa.domain.mkuTag.po.MkuTagPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.extendPrice.biz.CountryExtendPriceReqVO;
import com.jdi.isc.product.soa.domain.price.extendPrice.po.CountryExtendPricePO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.sku.biz.ProductIdVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuFeaturePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuLogPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.specialAttr.biz.SpecialAttrTagVO;
import com.jdi.isc.product.soa.domain.specialAttr.po.SpecialAttrPO;
import com.jdi.isc.product.soa.domain.specialAttr.po.SpecialAttrRelationPO;
import com.jdi.isc.product.soa.domain.specialAttr.po.SpecialAttrValuePO;
import com.jdi.isc.product.soa.domain.spu.biz.GroupPropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.po.ProductGlobalAttributePO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.taxRate.res.CustomerSkuTaxResVO;
import com.jdi.isc.product.soa.rpc.gd.RpcGdProductService;
import com.jdi.isc.product.soa.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.product.soa.service.atomic.brand.BrandCountryAtomicService;
import com.jdi.isc.product.soa.service.atomic.category.CategoryBuyerRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.category.CategoryCountryAtomicService;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.mkuTag.MkuTagAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.extendPrice.CountryExtendPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuFeatureAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.specialAttr.SpecialAttrAtomicService;
import com.jdi.isc.product.soa.service.atomic.specialAttr.SpecialAttrRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.specialAttr.SpecialAttrValueAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.ProductGlobalAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.manage.brand.BrandOutService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.category.GlobalAttributeManageService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.countryMku.CountryMkuManageService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.customerMku.CustomerMkuManageService;
import com.jdi.isc.product.soa.service.manage.mkuTag.MkuTagManageService;
import com.jdi.isc.product.soa.service.manage.price.ExchangeRateManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuFeatureManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.validate.SkuValidateService;
import com.jdi.isc.product.soa.service.manage.stock.StockManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.CustomerTaxManageService;
import com.jdi.isc.product.soa.service.mapstruct.countryMku.CountryMkuConvert;
import com.jdi.isc.product.soa.service.mapstruct.sku.SkuConvert;
import com.jdi.isc.product.soa.service.protocol.http.ruleEngine.RuleEngineService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 商品国家池表数据维护服务实现
 * @Author: wangpeng965
 * @Date: 2024/12/02 21:49
 **/

@Slf4j
@Service
public class CountryMkuManageServiceImpl extends BaseManageSupportService<CountryMkuVO, CountryMkuPO> implements CountryMkuManageService {

    @Resource
    private CountryMkuAtomicService countryMkuAtomicService;
    @Resource
    private MkuLangAtomicService mkuLangAtomicService;
    @Resource
    private CategoryOutService categoryOutService;
    @Resource
    private BrandOutService brandOutService;
    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private BrandCountryAtomicService brandCountryAtomicService;
    @Resource
    private MkuAtomicService mkuAtomicService;
    @Resource
    private CategoryCountryAtomicService categoryCountryAtomicService;
    @Resource
    private SkuPriceAtomicService skuPriceAtomicService;
    @Resource
    private OperDuccConfig operDuccConfig;
    @Resource
    private RuleEngineService ruleEngineService;
    @Resource
    private CustomerManageService customerManageService;

    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;
    @Resource
    private SkuValidateService skuValidateService;
    @Resource
    private StockManageService stockManageService;
    @Resource
    private SkuReadManageService skuReadManageService;
    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    private CountryManageService countryManageService;
    @Resource
    private SkuInfoRpcService skuInfoRpcService;
    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;

    @LafValue("jdi.isc.stock.iop.switch.two")
    private Boolean iopSwitch;
    @LafValue("jdi.isc.country.pool.approveConfig")
    private String approveConfig;


    @Resource
    private SpecialAttrRelationAtomicService specialAttrRelationAtomicService;

    @Resource
    private SpecialAttrAtomicService specialAttrAtomicService;

    @Resource
    private SpecialAttrValueAtomicService specialAttrValueAtomicService;

    @Resource
    private MkuTagManageService mkuTagManageService;

    @Resource
    private MkuTagAtomicService mkuTagAtomicService;
    @Resource
    private SkuFeatureAtomicService skuFeatureAtomicService;

    @Resource
    private SkuLogAtomicService skuLogAtomicService;

    @Resource
    private CustomerMkuManageService customerMkuManageService;

    @Resource
    private SkuFeatureManageService skuFeatureManageService;

    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;

    @Resource
    private CustomerTaxManageService customerTaxManageService;

    @LafValue("jdi.isc.tag.white.list")
    private String tagStr;

    private static final String all = "all";
    private static final String pool = "pool";
    private static final String black = "black";
    private static final String waiting = "waiting";
    private static final String out = "out";
    private static final String customerNotCountry = "customerNotCountry";
    private static final String warn = "warn";

    private static final Long  TEST_PRODUCT = 8L;

    @Resource
    private GlobalAttributeManageService globalAttributeManageService;

    @Resource
    private RpcGdProductService rpcGdProductService;

    @Resource
    private CountryExtendPriceAtomicService countryExtendPriceAtomicService;

    @Resource
    private ExchangeRateManageService exchangeRateManageService;

    @Resource
    private CategoryBuyerRelationAtomicService categoryBuyerRelationAtomicService;

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<String> saveOrUpdate(CountryMkuVO input) {
        if(Objects.isNull(input) || !input.getValid()){
            return DataResponse.success();
        }
        // 增加开关
        CountryPoolSwitchVO countryPool = operDuccConfig.getCountryPool();
        if(!countryPool.getAddPoolSwitch().getOrDefault(input.getTargetCountryCode(),false)){
            return DataResponse.success();
        }
        log.info("CountryMkuManageServiceImpl.saveOrUpdate, input={}", JSONObject.toJSONString(input));
        CountryMkuPO po = CountryMkuConvert.INSTANCE.vo2Po(input);
        CountryMkuPO countryMkuPO = countryMkuAtomicService.getByMkuIdCountryCode(input.getMkuId(), input.getTargetCountryCode());
        if(Objects.nonNull(countryMkuPO)){
            po.setId(countryMkuPO.getId());
            po.setCreateTime(countryMkuPO.getCreateTime());
            if(Objects.equals(countryMkuPO.getPoolStatus(), CountryMkuPoolStatusEnum.BLACK.getCode())){
                log.info("CountryMkuManageServiceImpl.saveOrUpdate 已拉黑数据无法机器修改状态, input={}", JSONObject.toJSONString(input));
                return DataResponse.success();
            }
        }
        String mkuIdCountry = po.getTargetCountryCode() + po.getMkuId();
        if (Objects.nonNull(po.getId())){
            po.setMkuId(null);
        }
        boolean saveRes = countryMkuAtomicService.saveOrUpdate(po);
        if (!saveRes){
            log.warn("CountryMkuManageServiceImpl.saveOrUpdate, CountryMkuPO fail. CountryMkuPO={}", JSONObject.toJSONString(po));
            throw new BizException("保存失败");
        }

        sendLog(mkuIdCountry,countryMkuPO,po);
        return DataResponse.success();
    }

    @Override
    public DataResponse<Boolean> mkuPoolJoinCountryPool(CountryMkuReqVO reqVO) {
        if(StringUtils.isBlank(reqVO.getCountryCode())){
            return DataResponse.error("国家参数为空");
        }
        Long count = 0L;
        List<MkuPO> mkuPOList = new ArrayList<>();
        if(Objects.nonNull(reqVO.getStartTime())){
            Date beginDate = DateUtil.long2date(reqVO.getStartTime());
            Date endDate = reqVO.getEndTime() != null?DateUtil.long2date(reqVO.getEndTime()):new Date();
            mkuPOList = mkuAtomicService.listByTime(beginDate, endDate);
        }else if(Objects.nonNull(reqVO.getBeginId())){
            mkuPOList = mkuAtomicService.listById(reqVO.getBeginId(), reqVO.getEndId());
        }else if(CollectionUtils.isNotEmpty(reqVO.getBrandIdList())){
            mkuPOList = mkuAtomicService.listByBrandId(reqVO.getBrandIdList());
        }else if(CollectionUtils.isNotEmpty(reqVO.getMkuIdList())){
            mkuPOList = mkuAtomicService.listByMkuIds(reqVO.getMkuIdList());
        }
        if(CollectionUtils.isEmpty(mkuPOList)){
            return DataResponse.error("此ID段无数据");
        }
        count = (long) mkuPOList.size();
        log.info("CountryMkuManageServiceImpl mkuPoolJoinCountryPool count:{}",count);
        int num = 0;
        for (MkuPO mkuPO : mkuPOList){
            num++;
            log.info("CountryMkuManageServiceImpl mkuPoolJoinCountryPool mku:{},beginId:{}--endId:{},index:{},mkuId:{}",reqVO.getCountryCode(),reqVO.getBeginId(), reqVO.getEndId(),num,mkuPO.getMkuId());
            try {
                if(mkuPO.getSourceCountryCode().equals(CountryConstant.COUNTRY_ZH)) {
                    Set<String> countryCodeSet = getTargetCountryCodeByMku(mkuPO);
                    if(!countryCodeSet.contains(reqVO.getCountryCode())){
                        continue;
                    }
                    List<SkuVO> skuVOList = validateSkuByMku(reqVO, mkuPO);
                    if(CollectionUtils.isNotEmpty(skuVOList)){
                        CountryMkuVO countryMkuVO = createCountryMkuVO(mkuPO, skuVOList, reqVO.getCountryCode(), 0);
                        saveOrUpdate(countryMkuVO);
                    }
                }else if(mkuPO.getSourceCountryCode().equals(reqVO.getCountryCode())){
                    List<SkuVO> skuVOList = validateSkuByMku(reqVO, mkuPO);
                    CountryMkuVO countryMkuVO = createCountryMkuVO(mkuPO, skuVOList, reqVO.getCountryCode(), 0);
                    String skuProductionCycle = getSkuProductionCycle(mkuPO);
                    countryMkuVO.setProductionCycle(skuProductionCycle);
                    saveOrUpdate(countryMkuVO);
                }
            }catch (Exception e){
                log.error("CountryMkuManageServiceImpl mkuPoolJoinCountryPool mkuPO:{}e:{}",JSON.toJSONString(mkuPO),e.getMessage(),e);
            }
        }
        return DataResponse.success();
    }

    @Override
    public DataResponse<Boolean> mkuMsgJoinCountryPool(CountryMkuReqVO reqVO) {
        if(Objects.isNull(reqVO.getMkuId())){
            return DataResponse.error("mku参数为空");
        }
        MkuPO mkuPO = mkuAtomicService.getById(reqVO.getMkuId());
        if(mkuPO.getSourceCountryCode().equals(CountryConstant.COUNTRY_ZH)) {
            Set<String> countryCodeSet = getTargetCountryCodeByMku(mkuPO);
            if (reqVO.getCountryCode() != null) {
                countryCodeSet = countryCodeSet.stream()
                    .filter(code -> code.equals(reqVO.getCountryCode()))
                    .collect(Collectors.toSet());
            }
            for (String countryCode : countryCodeSet) {
                reqVO.setCountryCode(countryCode);
                List<SkuVO> skuVOList = validateSkuByMku(reqVO, mkuPO);
                if(CollectionUtils.isNotEmpty(skuVOList)){
                    CountryMkuVO countryMkuVO = createCountryMkuVO(mkuPO, skuVOList, countryCode, 0);
                    saveOrUpdate(countryMkuVO);
                }
            }
        }else {
            CountryMkuVO countryMkuVO;
            reqVO.setCountryCode(mkuPO.getSourceCountryCode());
            List<SkuVO> skuVOList = validateSkuByMku(reqVO, mkuPO);
            countryMkuVO = createCountryMkuVO(mkuPO, skuVOList, reqVO.getCountryCode(), 0);
            String skuProductionCycle = getSkuProductionCycle(mkuPO);
            countryMkuVO.setProductionCycle(skuProductionCycle);
            saveOrUpdate(countryMkuVO);
        }
        return DataResponse.success(true);
    }

    @Override
    public DataResponse<Boolean> jdSkuIdMsgJoinCountryPool(CountryMkuReqVO reqVO) {
        if(Objects.isNull(reqVO.getJdSkuId())){
            return DataResponse.error("jdSkuId参数为空");
        }
        Set<Long> skuSet = new HashSet<>();
        if(Objects.nonNull(reqVO.getJdSkuId())) {
            SkuPO skuPO = skuAtomicService.getSkuPoByJdSkuId(reqVO.getJdSkuId());
            if(Objects.nonNull(skuPO)){
                skuSet.add(skuPO.getSkuId());
            }
        }else if(Objects.nonNull(reqVO.getSkuId())){
            skuSet.add(reqVO.getSkuId());
        }else if(Objects.nonNull(reqVO.getSpuId())){
            List<SkuPO> skuPOS = skuAtomicService.getSkuPoBySpuId(reqVO.getSpuId());
            if(CollectionUtils.isNotEmpty(skuPOS)){
                skuSet.addAll(skuPOS.stream().map(SkuPO::getSkuId).collect(Collectors.toSet()));
            }
        }
        if(CollectionUtils.isEmpty(skuSet)){
            return DataResponse.error("skuId为空");
        }

        List<MkuRelationPO> mkuRelationPOS = mkuRelationAtomicService.queryBindListBySkuIds(new ArrayList<>(skuSet));
        if(CollectionUtils.isEmpty(mkuRelationPOS)){
            return DataResponse.error("mkuRelationPOS为空");
        }

        for(MkuRelationPO mkuRelationPO: mkuRelationPOS){
            try{
                CountryMkuReqVO mkuReqVO = new CountryMkuReqVO();
                mkuReqVO.setMkuId(mkuRelationPO.getMkuId());
                this.mkuMsgJoinCountryPool(mkuReqVO);
            }catch (Exception e){
                log.error("CountryMkuManageServiceImpl.jdSkuIdMsgJoinCountryPool error mkuRelationPO:{}",JSONObject.toJSONString(mkuRelationPO),e);
            }
        }

        return DataResponse.success();
    }



    private Set<String> getTargetCountryCodeByMku(MkuPO mkuPO){
        List<MkuRelationPO> mkuByMkuId = mkuRelationAtomicService.getMkuByMkuId(mkuPO.getMkuId());
        Set<Long> skuSet = mkuByMkuId.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toSet());
        List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(skuSet);
        List<Long> spuList = skuPOList.stream().map(SkuPO::getSpuId).collect(Collectors.toList());
        List<SpuPO> spuPOList = spuAtomicService.querySpuList(spuList);
        Set<String> countryCodeSet = new HashSet<>();
        spuPOList.forEach(spuPO -> {
            String[] split = spuPO.getAttributeScope().split(Constant.COMMA);
            countryCodeSet.addAll(Arrays.asList(split));
        } );
        return countryCodeSet;
    }



    private String getSkuProductionCycle(MkuPO mkuPO){
        try {
            List<MkuRelationPO> mkuByMkuId = mkuRelationAtomicService.getMkuByMkuId(mkuPO.getMkuId());
            Set<Long> skuSet = mkuByMkuId.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toSet());
            List<SkuFeaturePO> skuFeaturePOList = skuFeatureAtomicService.queryListByskuIds(skuSet);
            if(CollectionUtils.isEmpty(skuFeaturePOList)){
                return "";
            }
            SkuFeaturePO skuFeaturePO = skuFeaturePOList.get(0);
            return skuFeaturePO.getProductionCycle() == null ? null: skuFeaturePO.getProductionCycle().toString();
        }catch (Exception e){
            log.error("CountryMkuManageServiceImpl.getSkuProductionCycle err:{}",e.getMessage(),e);
            return null;
        }

    }

    @Override
    public DataResponse<Boolean> customerPoolJoinCountryPool(CountryMkuReqVO dto) {
        if(StringUtils.isBlank(dto.getCountryCode())){
            return DataResponse.error("参数为空");
        }
        List<CustomerVO> customerVOS = customerManageService.queryListByCountry(dto.getCountryCode());
        if(CollectionUtils.isEmpty(customerVOS)){
            return DataResponse.error("当前国家无客户");
        }
        for(CustomerVO customerVO : customerVOS){
            if(Objects.nonNull(dto.getClientCode()) && !customerVO.getClientCode().equals(dto.getClientCode())){
                continue;
            }
            Long count = 0L;
            List<CustomerMkuPO> customerMkuPOList = new ArrayList<>();
            if(Objects.nonNull(dto.getStartTime())){
                Date beginDate = DateUtil.long2date(dto.getStartTime());
                Date endDate = dto.getEndTime() != null?DateUtil.long2date(dto.getEndTime()):new Date();
                count = customerMkuAtomicService.countByClientCode(customerVO.getClientCode(),beginDate,endDate);
                customerMkuPOList = customerMkuAtomicService.listByClientCode(customerVO.getClientCode(),beginDate,endDate);
            }else {
                count = customerMkuAtomicService.countByMkuId(customerVO.getClientCode(),dto.getBeginId(), dto.getEndId());
                customerMkuPOList = customerMkuAtomicService.listByMkuId(customerVO.getClientCode(),dto.getBeginId(), dto.getEndId());
            }
            log.info("CountryMkuManageServiceImpl customerPoolJoinCountryPool count:{}",count);
            if(count == 0){
                continue;
            }
            List<Long> mkuIdList = customerMkuPOList.stream().map(CustomerMkuPO::getMkuId).collect(Collectors.toList());
            int batchSize = 100;
            int totalSize = mkuIdList.size();
            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<Long> sublist = mkuIdList.subList(i, endIndex);
                List<MkuPO> mkuPOList = mkuAtomicService.listByMkuIds(sublist);
                for (MkuPO mkuPO : mkuPOList){
                    try {
                        List<SkuVO> skuVOList = validateSkuByMku(dto, mkuPO);
                        if (CollectionUtils.isNotEmpty(skuVOList)) {
                            CountryMkuVO countryMkuVO = createCountryMkuVO(mkuPO, skuVOList, dto.getCountryCode(), 1);
                            saveOrUpdate(countryMkuVO);
                        }
                    }catch (Exception e){
                        log.error("CountryMkuManageServiceImpl mkuPoolJoinCountryPool mkuPO:{}e:{}",JSON.toJSONString(mkuPO),e.getMessage(),e);
                    }
                }
            }
        }
        return DataResponse.success();
    }



    private CountryMkuVO createCountryMkuVO(MkuPO mkuPO,List<SkuVO> skuVOList,String targetCountryCode,Integer type){

        CountryMkuVO countryMkuVO = new CountryMkuVO();
        countryMkuVO.setMkuId(mkuPO.getMkuId());
        countryMkuVO.setSourceCountryCode(mkuPO.getSourceCountryCode());
        countryMkuVO.setTargetCountryCode(targetCountryCode);
        countryMkuVO.setLastCatId(mkuPO.getJdCatId());
        countryMkuVO.setBrandId(mkuPO.getBrandId());
        countryMkuVO.setPoolStatus(CountryMkuPoolStatusEnum.POOL.getCode());
        countryMkuVO.setWarnStatus(CountryMkuWarnStatusEnum.NORMAL.getCode());
        countryMkuVO.setWarnReason(CountryMkuWarnReasonEnum.DRAFT.getCode().toString());
        countryMkuVO.setCreateTime(new Date().getTime());
        countryMkuVO.setUpdateTime(new Date().getTime());
        countryMkuVO.setCreator("System");
        countryMkuVO.setUpdater("System");
        countryMkuVO.setBuyer(mkuPO.getBuyer());
        if(CollectionUtils.isEmpty(skuVOList)){
            countryMkuVO.setYn(YnEnum.NO.getCode());
            return countryMkuVO;
            //countryMkuVO.setPoolStatus(CountryMkuPoolStatusEnum.NOT_POOL.getCode());
            //countryMkuVO.setWarnStatus(CountryMkuWarnStatusEnum.WARN.getCode());
            //countryMkuVO.setWarnReason(CountryMkuWarnReasonEnum.DELETE.getCode().toString());
            //return countryMkuVO;
        }
        boolean valid =  skuVOList.stream().anyMatch(SkuVO::getValid);
        if(!valid){
            countryMkuVO.setValid(valid);
        }
        // type = 0,是国际池，type = 1,是客户池
        boolean fixedRule = skuVOList.stream().anyMatch(SkuVO::getFixedRule);
        if(!fixedRule){
            CountryMkuPO countryMkuPO = countryMkuAtomicService.getByMkuIdCountryCode(mkuPO.getMkuId(),targetCountryCode);
            if(type == 0){
                if(Objects.isNull(countryMkuPO)){
                    countryMkuVO.setPoolStatus(CountryMkuPoolStatusEnum.NOT_POOL.getCode());
                }else {
                    countryMkuVO.setPoolStatus(countryMkuPO.getPoolStatus());
                }
            }else {
                countryMkuVO.setPoolStatus(CountryMkuPoolStatusEnum.POOL.getCode());
            }
            String warnReason = getWarnReason(skuVOList);
            countryMkuVO.setWarnReason(warnReason);
            countryMkuVO.setWarnStatus(CountryMkuWarnStatusEnum.WARN.getCode());
        }
        boolean normal = skuVOList.stream().anyMatch(SkuVO::getNormal);
        if(!normal){
            countryMkuVO.setWarnStatus(CountryMkuWarnStatusEnum.WARN.getCode());
            String warnReason = getWarnReason(skuVOList);
            countryMkuVO.setWarnReason(warnReason);
            String poolFailReason = skuVOList.stream()
                    .map(SkuVO::getPoolFailReason)
                    .filter(Objects::nonNull)
                    .filter(reason -> !reason.isEmpty())
                    .collect(Collectors.joining(","));

            String unSaleReason = skuVOList.stream()
                    .map(SkuVO::getUnSaleReason)
                    .filter(Objects::nonNull)
                    .filter(reason -> !reason.isEmpty())
                    .collect(Collectors.joining(","));

            countryMkuVO.setPoolFailReason(poolFailReason);
            countryMkuVO.setUnSaleReason(unSaleReason);
        }
        boolean businessRule = skuVOList.stream().anyMatch(SkuVO::getBusinessRule);
        if(!businessRule){
            countryMkuVO.setPoolStatus(CountryMkuPoolStatusEnum.DRAFT.getCode());
            countryMkuVO.setUndeterminedReason(getUndeterminedReason(skuVOList));
        }
        int skuStatus = 1;
        String downReason ="";
        for (SkuVO skuVO : skuVOList){
            if(Objects.nonNull(skuVO.getSkuStatus())){
                skuStatus = skuVO.getSkuStatus();
                downReason = skuVO.getDownReason();
            }
        }
        countryMkuVO.setCountryMkuStatus(skuStatus);
        countryMkuVO.setDownReason(downReason);
        return countryMkuVO;
    }

    private String getWarnReason(List<SkuVO> skuVOList){
        Set<String> warnReasonList = new LinkedHashSet<>();
        skuVOList.forEach(
            skuVO -> {
                warnReasonList.addAll(skuVO.getFixedResult());
            }
        );
        String warnReason  = StringUtils.join(warnReasonList,Constant.COMMA);
        return warnReason;
    }

    private String getUndeterminedReason(List<SkuVO> skuVOList){
        Set<String> undeterminedReasonList = new HashSet<>();
        skuVOList.forEach(
            skuVO -> {
                undeterminedReasonList.addAll(skuVO.getFixedResult());
            }
        );
        String undeterminedReason  = StringUtils.join(undeterminedReasonList,Constant.COMMA);
        return undeterminedReason;
    }


    private void addTag(List<SkuVO> skuVOList,CountryMkuReqVO reqVO){
        Set<Long> skuSet = skuVOList.stream().map(SkuVO::getSkuId).collect(Collectors.toSet());
        // 通过skuId查询 商品标  tag keyId， keyValueId
        List<SpecialAttrRelationPO> specialAttrRelationPOList = specialAttrRelationAtomicService.queryBySkus(skuSet);
        if(CollectionUtils.isEmpty(specialAttrRelationPOList)){
            return;
        }
        Set<Long> tagKeyIdList = specialAttrRelationPOList.stream().map(SpecialAttrRelationPO::getAttributeKeyId).collect(Collectors.toSet());
        // 通过tagKeyId查询 商品标名称和标授权国家
        List<SpecialAttrPO> specialAttrPOList = specialAttrAtomicService.queryByIds(tagKeyIdList);
        // 过滤和当前目标授权的国家
        Set<Long> tagKeyIdSet = specialAttrPOList.stream().filter(po -> {
            if (Objects.isNull(po.getCountryScope()) || po.getCountryScope().contains(reqVO.getCountryCode())) {
                return true;
            } else {
                return false;
            }
        }).map(SpecialAttrPO::getId).collect(Collectors.toSet());

        Map<Long,List<SpecialAttrRelationPO>> tagMap =  specialAttrRelationPOList.stream().filter(po->{
            if(tagKeyIdSet.contains(po.getAttributeKeyId())){
                return true;
            } else {
                return false;
            }
        }).collect(Collectors.groupingBy(SpecialAttrRelationPO::getSkuId));
        for (SkuVO skuVO : skuVOList){
            if(tagMap.containsKey(skuVO.getSkuId())){
                List<SpecialAttrRelationPO> specialAttrRelationPOS = tagMap.get(skuVO.getSkuId());
                skuVO.setSpecialAttrRelationPOList(specialAttrRelationPOS);
            }
        }
    }

    /**
     * 根据国家和MKU ID查询绑定的SKU列表，并进行固定规则和业务规则的验证。
     * @param reqVO 国家和MKU ID的请求对象
     * @return 绑定的SKU列表
     */
    public List<SkuVO> validateSkuByMku(CountryMkuReqVO reqVO,MkuPO mkuPO){
        List<MkuRelationPO> mkuRelationPOList = mkuRelationAtomicService.queryBindListByMkuId(mkuPO.getMkuId());
        Map<Long, MkuRelationPO> skuIdMkuMap = mkuRelationPOList.stream().collect(Collectors.toMap(MkuRelationPO::getSkuId, Function.identity()));
        List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(skuIdMkuMap.keySet());
        List<SkuVO> skuVOList = SkuConvert.INSTANCE.listPo2Vo(skuPOList);
        if(mkuPO.getSourceCountryCode().equals(CountryConstant.COUNTRY_ZH)){
            validateFixedRule(skuVOList,reqVO,mkuPO);
        }
        setSkuStatus(skuVOList,reqVO);
        validateAgreementPrice(skuVOList,reqVO);
        //validateBusinessRule(skuVOList,reqVO);
        //addTag(skuVOList,reqVO);
        if(!mkuPO.getSourceCountryCode().equals(CountryConstant.COUNTRY_ZH)) {
            validateSelection(skuVOList,reqVO);
        }
        return skuVOList;
    }

    private void setSkuStatus(List<SkuVO> skuVOList,CountryMkuReqVO reqVO){
        if(CollectionUtils.isEmpty(skuVOList)){
            return ;
        }
        String countryCode = reqVO.getCountryCode();
        Set<Long> skuIdSet = skuVOList.stream().map(SkuVO::getSkuId).collect(Collectors.toSet());
        Map<Long, Integer> skuIdPurchaseMap = skuFeatureManageService.querySkuPurchaseModel(skuIdSet, countryCode);
        for (SkuVO skuVO : skuVOList){
            Integer purchase = skuIdPurchaseMap.get(skuVO.getSkuId());
            if(!PurchaseModelTypeEnum.DIRECT_SUPPLY.getCode().equals(purchase)){
                skuVO.setSkuStatus(null);
                skuVO.setDownReason(null);
            }
        }
    }


    private void validateSelection(List<SkuVO> skuVOList,CountryMkuReqVO reqVO){
        Set<Long> skuIdSet = skuVOList.stream().map(SkuVO::getSkuId).collect(Collectors.toSet());
        List<SpecialAttrRelationPO> specialAttrRelationPOList = specialAttrRelationAtomicService.queryBySkus(skuIdSet);
        if(CollectionUtils.isEmpty(specialAttrRelationPOList)){
            return;
        }
        Map<Long, List<SpecialAttrRelationPO>> skuIdSpecialMap =
            specialAttrRelationPOList.stream().collect(Collectors.groupingBy(SpecialAttrRelationPO::getSkuId));
        for (SkuVO skuVO : skuVOList){
            if(skuIdSpecialMap.containsKey(skuVO.getSkuId())) {
                List<SpecialAttrRelationPO> specialAttrRelationPOs = skuIdSpecialMap.get(skuVO.getSkuId());
                for (SpecialAttrRelationPO specialAttrRelationPO : specialAttrRelationPOs){
                    if (Long.valueOf(9).equals(specialAttrRelationPO.getAttributeValueId())) {
                        skuVO.setValid(false);
                        skuVO.addFixedResult(CountryMkuWarnReasonEnum.BR_SELECTION.getCode());
                    }
                }
            }
        }

    }


    /**
     * 验证固定规则
     * @param skuVOList SKU列表
     * @param reqVO 请求对象
     */
    private void validateFixedRule(List<SkuVO> skuVOList,CountryMkuReqVO reqVO,MkuPO mkuPO){
        validateInterAttribute(skuVOList,reqVO);
        if(reqVO.getCountryCode().equals(CountryConstant.COUNTRY_HK) || reqVO.getCountryCode().equals(CountryConstant.COUNTRY_ZH)){
            validateHKInterAttribute(skuVOList);
        }
        validateBrandAuth(skuVOList, reqVO);
        validateJdSkuIdSell(skuVOList,reqVO,mkuPO);
    }

    private void validateHKInterAttribute(List<SkuVO> skuVOList){
        for (SkuVO skuVO : skuVOList){
            skuVO.removeFixedResult(CountryMkuWarnReasonEnum.IMPORT.getCode());
            skuVO.removeFixedResult(CountryMkuWarnReasonEnum.PRODUCT.getCode());
            skuVO.removeFixedResult(CountryMkuWarnReasonEnum.IMPORT_AUTH.getCode());
            skuVO.removeFixedResult(CountryMkuWarnReasonEnum.PRODUCT_AUTH.getCode());
            if(CollectionUtils.isEmpty(skuVO.getFixedResult())){
                skuVO.setFixedRule(Boolean.TRUE);
            }
        }
    }

    private void validateAgreementPrice(List<SkuVO> skuVOList,CountryMkuReqVO requestVO){
        List<Long> skuIdList = skuVOList.stream().map(SkuVO::getSkuId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(skuIdList)){
            return;
        }
        List<CountryAgreementPricePO> countryAgreementPriceList = countryAgreementPriceAtomicService.getCountryAgreementPriceList(skuIdList, requestVO.getCountryCode());
        Map<Long,CountryAgreementPricePO> skuIdAgreementPriceMap = countryAgreementPriceList.stream().filter(Objects::nonNull)
            .collect(Collectors.toMap(
                CountryAgreementPricePO::getSkuId,
                Function.identity(),
                (existing, replacement) -> existing
            ));
        for (SkuVO skuVO : skuVOList){
            if(skuIdAgreementPriceMap.containsKey(skuVO.getSkuId())){
                CountryAgreementPricePO countryAgreementPricePO = skuIdAgreementPriceMap.get(skuVO.getSkuId());
                if(Objects.isNull(countryAgreementPricePO.getCountryCostPrice()) || countryAgreementPricePO.getCountryCostPrice().compareTo(BigDecimal.ZERO) == 0){
                    skuVO.setFixedRule(false);
                    skuVO.addFixedResult(CountryMkuWarnReasonEnum.NOT_HAVE_COST_PRICE.getCode());
                }
            }else {
                skuVO.setFixedRule(false);
                skuVO.addFixedResult(CountryMkuWarnReasonEnum.NOT_HAVE_COST_PRICE.getCode());
            }
        }
    }

    /**
     * 验证京东SKU是否可售，并根据结果更新SkuVO的状态和固定结果。
     * @param skuVOList SKU列表
     * @param requestVO 请求对象，包含国家代码
     * @param mkuPO MkuPO对象
     */
    private void validateJdSkuIdSell(List<SkuVO> skuVOList,CountryMkuReqVO requestVO,MkuPO mkuPO){
        List<Long> skuIdList = skuVOList.stream().filter(skuVO -> Objects.nonNull(skuVO.getJdSkuId())).map(SkuVO::getSkuId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(skuIdList)){
            return;
        }
        try {
            if(iopSwitch) {
                Map<Long,String> failureMessageMap = new HashMap<>();
                CountryPoolSwitchVO countryPoolConfig = operDuccConfig.getCountryPool();
                List<Long> addPoolJdSkuIdList = new ArrayList<>();
                // 主站可售（包含MRO加池）
                List<Long> purchasableSkuIdList = skuValidateService.validateSkuIdCanPurchase(skuIdList,requestVO.getCountryCode(),failureMessageMap);
                skuVOList.forEach(skuVO -> {
                    if(!purchasableSkuIdList.contains(skuVO.getSkuId())){
                        List<String> joinPoolFailureMessages = countryPoolConfig.getJoinPoolFailInfo();
                        String failureMessage = failureMessageMap.get(skuVO.getSkuId());
                        if(joinPoolFailureMessages.contains(failureMessage)){
                            addPoolJdSkuIdList.add(skuVO.getJdSkuId());
                            skuVO.setNormal(false);
                            skuVO.addFixedResult(CountryMkuWarnReasonEnum.JOIN_POOL_FAIL.getCode());
                        }else {
                            skuVO.setNormal(false);
                            skuVO.addFixedResult(CountryMkuWarnReasonEnum.NO_POOL.getCode());
                            skuVO.setUnSaleReason(failureMessage);
                        }
                    }
                });
                log.info("CountryMkuManageServiceImpl.validateJdSkuIdSell addPoolJdSkuIdList:{},skuVOList:{}",JSON.toJSONString(addPoolJdSkuIdList),JSON.toJSONString(skuVOList));
                joinDomesticPool(addPoolJdSkuIdList,skuVOList,requestVO,mkuPO,countryPoolConfig);
                // 是否区域限售
                List<Long> areaRestrictedSkuIdList = skuValidateService.validateSkuIdAreaLimit(skuIdList,requestVO.getCountryCode());
                skuVOList.forEach(skuVO -> {
                    if(areaRestrictedSkuIdList.contains(skuVO.getSkuId())){
                        skuVO.setNormal(false);
                        skuVO.addFixedResult(CountryMkuWarnReasonEnum.POOL.getCode());
                    }
                });
                Thread.sleep(2);
            }
        }catch (Exception e){
            log.error("CountryMkuManageServiceImpl.validateJdSkuIdSell req:{},err:{},", JSON.toJSONString(skuVOList),e.getMessage(),e);
            for (SkuVO skuVO : skuVOList) {
                skuVO.setNormal(false);
                skuVO.addFixedResult(CountryMkuWarnReasonEnum.POOL.getCode());
            }
        }

    }



    /**
     * 将符合条件的 SKU 加入国内工鼎池。
     * @param poolSkuIdList 国内工鼎池的 SKU ID 列表。
     * @param skuVOList SKU 的详细信息列表。
     * @param reqVO 请求参数对象，包含国家代码等信息。
     * @param mkuPO MKU 的详细信息对象。
     * @param countryPool 国家工鼎池的开关信息对象。
     */
    private void joinDomesticPool(List<Long> poolSkuIdList,List<SkuVO> skuVOList,CountryMkuReqVO reqVO,MkuPO mkuPO,CountryPoolSwitchVO countryPool){
        Set<Long> poolJdSkuIds = new HashSet<>(poolSkuIdList);
        if(CollectionUtils.isEmpty(poolJdSkuIds)){
            return;
        }
        boolean hasFixedRule = skuVOList.stream().anyMatch(SkuVO::getFixedRule);
        if(!hasFixedRule){
            return;
        }
        CountryMkuPO countryMkuPO = countryMkuAtomicService.getByMkuIdCountryCode(mkuPO.getMkuId(), reqVO.getCountryCode());
        if(Objects.nonNull(countryMkuPO)){
            Set<String> warningReasons = Arrays.stream(countryMkuPO.getWarnReason().split(Constant.COMMA)).collect(Collectors.toSet());
            if(warningReasons.contains(CountryMkuWarnReasonEnum.JOIN_POOL_FAIL.getCode().toString())){
                log.error("CountryMkuManageServiceImpl.joinDomesticPool 重复执行入国内工鼎池 countryCode:{},countryMkuPO:{}",reqVO.getCountryCode(),JSON.toJSONString(countryMkuPO));
                for (SkuVO skuVO : skuVOList) {
                    skuVO.setPoolFailReason(countryMkuPO.getPoolFailReason());
                    skuVO.setUnSaleReason(countryMkuPO.getUnSaleReason());
                }
//                return;
            }
        }

        String contractCode = getContractCode(reqVO.getCountryCode(), countryPool);
        if(StringUtils.isBlank(contractCode)){
            log.error("CountryMkuManageServiceImpl.joinDomesticPool error countryCode:{},contractCode:{}",reqVO.getCountryCode(),contractCode);
            return;
        }
        String countryErp = getCountryErp(reqVO.getCountryCode(), countryPool);
        if(StringUtils.isBlank(countryErp)){
            log.error("CountryMkuManageServiceImpl.joinDomesticPool error countryCode:{},getCountryErp:{}",reqVO.getCountryCode(),countryErp);
            return;
        }
        String customerName = getCustomerName(reqVO.getCountryCode(), countryPool);
        for (SkuVO skuVO : skuVOList){
            if(poolJdSkuIds.contains(skuVO.getJdSkuId())){
                GdPoolVO gdPoolVO = new GdPoolVO();
                gdPoolVO.setSku(String.valueOf(skuVO.getJdSkuId()));
                gdPoolVO.setRemark("国际入池创建品");
                gdPoolVO.setContractCode(contractCode);
                gdPoolVO.setErp(countryErp);
                gdPoolVO.setCustomerName(customerName);
                JdiSaveCustomerPoolResp result = rpcGdProductService.saveGdProductInfo(gdPoolVO);
                log.info("CountryMkuManageServiceImpl.joinDomesticPool skuVO:{},result:{}",JSON.toJSONString(skuVO),result);
                if(result.isSuccess()){
                    Map<String, String> productFailReasonMap = result.getProductFailReason();
                    if(Objects.isNull(productFailReasonMap) || productFailReasonMap.isEmpty()) {
                        skuVO.setNormal(true);
                        skuVO.removeFixedResult(CountryMkuWarnReasonEnum.JOIN_POOL_FAIL.getCode());
                        try {
                            Thread.sleep(10000);
                        } catch (Exception e) {
                            log.error("CountryMkuManageServiceImpl.joinDomesticPool error",e);
                        }
                    }
                    else {
                        skuVO.setPoolFailReason(String.join(Constant.COMMA, productFailReasonMap.values()));
                    }
                } else {
                    skuVO.setPoolFailReason(CountryMkuWarnReasonEnum.JOIN_POOL_FAIL.getDesc());
                }
            }

        }
    }

    /**
     * 根据国家代码和国家池信息获取合同代码。
     * @param countryCode 国家代码
     * @param countryPool 国家池信息
     * @return 合同代码，若国家池信息为空或没有对应的合同代码则返回 null
     */
    private String getContractCode(String countryCode,CountryPoolSwitchVO countryPool){

        if(Objects.isNull(countryPool)){
            return null;
        }
        Map<String, String> contractCode = countryPool.getContractCode();
        if(MapUtils.isEmpty(contractCode)){
            return null;
        }
        return contractCode.get(countryCode);
    }

    /**
     * 根据国家代码和国家池信息获取对应的ERP系统国家代码。
     * @param countryCode 国家代码
     * @param countryPool 国家池信息
     * @return 对应的ERP系统国家代码，若国家池信息为空或国家代码在国家池中不存在则返回null
     */
    private String getCustomerName(String countryCode,CountryPoolSwitchVO countryPool){
        if(Objects.isNull(countryPool)){
            return null;
        }
        String customerName = countryPool.getCustomerName();
        if(StringUtils.isBlank(customerName)){
            customerName = "工业国际商品池";
        }
        return customerName;
    }

    private String getCountryErp(String countryCode,CountryPoolSwitchVO countryPool){
        if(Objects.isNull(countryPool)){
            return null;
        }
        Map<String, String> countryErpMap = countryPool.getCountryErpMap();
        if(MapUtils.isEmpty(countryErpMap)){
            return null;
        }
        return countryErpMap.get(countryCode);
    }
    /**
     * 验证业务规则。
     * @param skuVOList SKU信息列表
     * @param reqVO 请求信息
     */
    private void validateBusinessRule(List<SkuVO> skuVOList,CountryMkuReqVO reqVO){
        Set<Long> catIdSet = skuVOList.stream().filter(SkuVO::getValid).map(SkuVO::getCatId).collect(Collectors.toSet());
        List<CategoryCountryPO> categoryCountry = categoryCountryAtomicService.getCategoryCountry(reqVO.getCountryCode(), catIdSet, YnEnum.YES.getCode());
        if(CollectionUtils.isEmpty(categoryCountry)){
            return;
        }
        Set<Long> banCatSet = categoryCountry.stream().map(CategoryCountryPO::getLastCatId).collect(Collectors.toSet());

        for (SkuVO skuVO : skuVOList){
            if(!skuVO.getFixedRule()){
                continue;
            }
            if(banCatSet.contains(skuVO.getCatId())){
                skuVO.setBusinessRule(false);
                skuVO.addBusinessResult(CountryMkuUndeterminedReasonEnum.DRAFT.getCode());
            }
            if(skuVO.getSourceCountryCode().equals(CountryConstant.COUNTRY_ZH) && reqVO.getCountryCode().equals(CountryConstant.COUNTRY_TH)){
                SkuPricePO skuPricePO =  skuPriceAtomicService.querySkuTaxPrice(skuVO.getSkuId(), skuVO.getSourceCountryCode());
                BigDecimal taxPurchasePrice  = skuPricePO.getPrice();
                if(Objects.isNull(taxPurchasePrice)){
                    skuVO.setBusinessRule(false);
                    skuVO.addBusinessResult(CountryMkuUndeterminedReasonEnum.IMPORT.getCode());
                }else {
                    BigDecimal threshold = new BigDecimal("0.15");
                    BigDecimal factor = new BigDecimal("0.078");
                    BigDecimal length = new BigDecimal(skuVO.getLength());
                    BigDecimal width = new BigDecimal(skuVO.getWidth());
                    BigDecimal height = new BigDecimal(skuVO.getHeight());
                    // 计算基础运费
                    BigDecimal baseTransportPrice = taxPurchasePrice.multiply(factor);
                    // 计算体积运费
                    BigDecimal volume = length.multiply(width).multiply(height);
                    BigDecimal volumeTransportPrice = volume.divide(height, 6, RoundingMode.HALF_UP);
                    // 总运费
                    BigDecimal totalTransportPrice = baseTransportPrice.add(volumeTransportPrice);

                    // 比较总运费与阈值
                    if (totalTransportPrice.compareTo(threshold) > 0) {
                        skuVO.setBusinessRule(false);
                        skuVO.addBusinessResult(CountryMkuUndeterminedReasonEnum.IMPORT.getCode());
                    }
                }
            }
        }
    }

    /**
     * 验证品牌授权信息。
     * @param skuVOList SKU列表，用于获取品牌ID集合。
     * @param reqVO 请求对象，包含国家代码信息。
     */
    private void validateBrandAuth(List<SkuVO> skuVOList,CountryMkuReqVO reqVO){
        Set<Long> brandIdSet = skuVOList.stream().map(SkuVO::getBrandId).collect(Collectors.toSet());
        List<BrandCountryPO> brandCountryPOList = brandCountryAtomicService.queryByBrandIds(brandIdSet);
        if(CollectionUtils.isEmpty(brandCountryPOList)){
            return;
        }
        for(BrandCountryPO po : brandCountryPOList){
            boolean auth = false;
            if(StringUtils.isBlank(po.getAuthType())){

            }else if(BrandAuthorizeEnum.NO_AUTHORIZATION_REQUIRED.getValue() == Integer.parseInt(po.getAuthType())){
                auth = true;
            }else if(BrandAuthorizeEnum.PROJECT_AUTHORIZATION.getValue() == Integer.parseInt(po.getAuthType())){
                if(po.getAuthCountryCode().contains(reqVO.getCountryCode()) || po.getAuthCountryCode().contains(CountryConstant.GLOBAL)){
                    auth = true;
                }
            }else{
                if((po.getAuthCountryCode().contains(reqVO.getCountryCode()) || po.getAuthCountryCode().contains(CountryConstant.GLOBAL)) && Objects.nonNull(po.getAuthDate())
                    && StringUtils.isNotBlank(po.getAuthCert())){
                    if(po.getAuthDate() > System.currentTimeMillis()){
                        auth = true;
                    }
                }
            }
            if(!auth) {
                for (SkuVO skuVO : skuVOList) {
                    if(po.getBrandId().equals(skuVO.getBrandId())){
                        skuVO.setFixedRule(false);
                        skuVO.addFixedResult(CountryMkuWarnReasonEnum.BRAND.getCode());
                    }
                }
            }
        }
    }

    /**
     * 验证 SKU 列表中每个 SKU 的属性是否符合规则。
     * @param skuVOList SKU 列表
     * @param reqVO 请求参数对象
     */
    private void validateInterAttribute(List<SkuVO> skuVOList,CountryMkuReqVO reqVO){
        List<RuleEngineVO> ruleEngineVOS = operDuccConfig.ruleEngine();
        for(SkuVO skuVO : skuVOList){
            ruleEngine(skuVO,reqVO,ruleEngineVOS);
        }
    }




    /**
     * 根据规则引擎和商品信息，判断商品是否满足特定国家的进出口要求。
     * @param skuVO 商品信息对象
     * @param reqVO 请求对象，包含国家代码等信息
     * @param ruleEngineVOS 规则引擎对象列表
     */
    private void ruleEngine(SkuVO skuVO,CountryMkuReqVO reqVO,List<RuleEngineVO> ruleEngineVOS){
        int ruleNum = 0;
        initializeSkuVO(skuVO);
        Set<String> countryCodeSet = Collections.singleton(reqVO.getCountryCode());
        // TODO 跨境属性上移，by wang 。 2025-01-23
        List<GroupPropertyVO> groupPropertyVOList = globalAttributeManageService.queryGroupPropertyVos(skuVO.getCatId(), CountryConstant.COUNTRY_ZH,countryCodeSet,
            AttributeDimensionEnum.SKU.getCode(), LangConstant.LANG_ZH, 1);
        if(Objects.isNull(groupPropertyVOList) ||CollectionUtils.isEmpty(groupPropertyVOList)){
            log.error("CountryMkuManageServiceImpl.ruleEngine groupPropertyVOList is null,sku:{},reqVO:{}",JSON.toJSONString(skuVO),JSON.toJSONString(reqVO));
            return;
        }
        List<PropertyVO> propertyVOList = filterProperties(groupPropertyVOList);
        if (Objects.isNull(propertyVOList) || CollectionUtils.isEmpty(propertyVOList)) {
            log.error("CountryMkuManageServiceImpl.ruleEngine propertyVOList is null,sku:{},reqVO:{}",JSON.toJSONString(skuVO),JSON.toJSONString(reqVO));
            return;
        }
        List<ProductGlobalAttributePO> skuAttributePOS = productGlobalAttributeAtomicService.getBySkuId(skuVO.getSkuId());
        if(Objects.isNull(skuAttributePOS) || CollectionUtils.isEmpty(skuAttributePOS)){
            log.error("CountryMkuManageServiceImpl.ruleEngine skuAttributePOS is null,sku:{},reqVO:{}",JSON.toJSONString(skuVO),JSON.toJSONString(reqVO));
            return;
        }
        Map<Long, Long> attributeMap = skuAttributePOS.stream().collect(Collectors.toMap(ProductGlobalAttributePO::getAttributeId,ProductGlobalAttributePO::getAttributeValueId,(v1,v2) -> v1));
        List<ProductGlobalAttributePO> skuAttributePOSNoYn = productGlobalAttributeAtomicService.getBySkuIdNoYn(skuVO.getSkuId());
        if(Objects.nonNull(skuAttributePOSNoYn) && CollectionUtils.isNotEmpty(skuAttributePOSNoYn)) {
            Map<Long, Long> attributeNoYnMap = skuAttributePOSNoYn.stream()
                .filter(p -> Objects.nonNull(p.getAttributeId()))
                .filter(p -> Objects.nonNull(p.getAttributeValueId()))
                .collect(Collectors.toMap(ProductGlobalAttributePO::getAttributeId, ProductGlobalAttributePO::getAttributeValueId, (v1, v2) -> v1));
            for (Map.Entry<Long, Long> attributeNoYnEntry : attributeNoYnMap.entrySet()) {
                Long key = attributeNoYnEntry.getKey();
                if (!attributeMap.containsKey(key)) {
                    log.error("CountryMkuManageServiceImpl.ruleEngine 跨境属性缺失 skuId:{},key:{}", skuVO.getSkuId(),
                        key);
                    attributeMap.put(key, attributeNoYnEntry.getValue());
                }
            }
        }
        Set<Integer> idSet = getRuleIdSet(ruleEngineVOS);
        for (PropertyVO propertyVO : propertyVOList){
            if(!idSet.contains(propertyVO.getAttributeId().intValue())){
                continue;
            }
            RuleEngineVO ruleEngineVO = getRuleId(String.valueOf(propertyVO.getAttributeId()),ruleEngineVOS);
            if(Objects.isNull(ruleEngineVO)){
                continue;
            }
            Long attributeValueId = attributeMap.get(propertyVO.getAttributeId());
            String attributeValue = "";
            if(Objects.nonNull(attributeValueId)) {
                attributeValue = String.valueOf(attributeValueId);
            }
            Map<String, String> map = new HashMap<>();
            map.put(ruleEngineVO.getKey().get(0), reqVO.getCountryCode());
            map.put(ruleEngineVO.getKey().get(1), String.valueOf(propertyVO.getAttributeId()));
            map.put(ruleEngineVO.getKey().get(2), attributeValue);
            if(StringUtils.isBlank(attributeValue)){
                Boolean result = ruleEngineService.validateInterAttribute(ruleEngineVO.getRuleId(), map);
                if(result){
                    skuVO.addFixedResult(ruleEngineVO.getSecondType());
                    skuVO.removeFixedResult(ruleEngineVO.getType());
                }
                continue;
            }
            Boolean result = ruleEngineService.validateInterAttribute(ruleEngineVO.getRuleId(), map);
            if(result){
                ruleNum ++;
                skuVO.removeFixedResult(ruleEngineVO.getType());
            }
        }
        skuVO.setFixedRule(ruleNum >= getRuleNum(ruleEngineVOS));
    }

    private void initializeSkuVO(SkuVO skuVO) {
        skuVO.setFixedRule(false);
        skuVO.addFixedResult(CountryMkuWarnReasonEnum.IMPORT.getCode());
        skuVO.addFixedResult(CountryMkuWarnReasonEnum.EXPORT.getCode());
        skuVO.addFixedResult(CountryMkuWarnReasonEnum.PRODUCT.getCode());
    }

    private List<PropertyVO> filterProperties(List<GroupPropertyVO> groupPropertyVOList) {
        return groupPropertyVOList.stream()
            .filter(groupPropertyVO -> Objects.equals(groupPropertyVO.getRequirement(), AttributeCheckTypeEnum.NO_REQUIRED.getCode()))
            .flatMap(groupPropertyVO -> groupPropertyVO.getPropertyVOS().stream())
            .collect(Collectors.toList());
    }

    /**
     * 获取规则引擎列表中所有规则的ID集合
     * @param ruleEngineVOList 规则引擎列表
     * @return 规则ID集合
     */
    private Set<Integer> getRuleIdSet(List<RuleEngineVO> ruleEngineVOList){
        Set<Integer> idSet = new HashSet<>();
        for(RuleEngineVO ruleEngineVO : ruleEngineVOList){
            List<Integer> id = ruleEngineVO.getId();
            idSet.addAll(id);
        }
        return idSet;
    }

    /**
     * 根据ID获取规则引擎对象
     * @param id 规则引擎的ID
     * @param ruleEngineVOList 规则引擎对象列表
     * @return 匹配ID的规则引擎对象，若不存在则返回null
     */
    private RuleEngineVO getRuleId(String id,List<RuleEngineVO> ruleEngineVOList){
        for (RuleEngineVO ruleEngineVO : ruleEngineVOList){
            if(ruleEngineVO.getId().contains(Integer.valueOf(id)) && ruleEngineVO.isYn()){
                return ruleEngineVO;
            }
        }
        return null;
    }

    /**
     * 获取规则引擎列表中规则的数量。
     * @param ruleEngineVOList 规则引擎列表。
     * @return 规则的数量。
     */
    private int getRuleNum(List<RuleEngineVO> ruleEngineVOList){
        if(CollectionUtils.isEmpty(ruleEngineVOList)){
            return 0;
        }
        return ruleEngineVOList.size();
    }


    @Override
    public CountryMkuVO detail(Long id) {
        CountryMkuPO po = countryMkuAtomicService.getValidById(id);
        if (null==po){
            log.info("detail, CountryMkuPO null. id={}", id);
            return null;
        }

        CountryMkuVO vo = CountryMkuConvert.INSTANCE.po2Vo(po);
        return vo;
    }

    @Override
    public Boolean delete(Long id) {
        CountryMkuPO po = countryMkuAtomicService.getValidById(id);
        if (null==po){
            log.info("detail, CountryMkuPO null. id={}", id);
            return true;
        }
        return countryMkuAtomicService.removeById(po);
    }

    @Override
    public Boolean batchDelete(Long beginId,Long endId) {
        List<CountryMkuPO> poList = countryMkuAtomicService.getValidByIdRange(beginId,endId);
        if (CollectionUtils.isEmpty(poList)){
            log.info("detail, CountryMkuPO null. beginId={},endId:{}", beginId,endId);
            return null;
        }
        List<Long> idList = poList.stream().map(CountryMkuPO::getId).collect(Collectors.toList());
        return countryMkuAtomicService.removeBatchByIds(idList);
    }

    @Override
    public PageInfo<CountryMkuPageVO.Response> pageSearch(CountryMkuPageVO.Request input) {
        PageInfo<CountryMkuPageVO.Response> pageInfo = new PageInfo<>();
        pageInfo.setSize(input.getSize());
        pageInfo.setIndex(input.getIndex());

        // 构建查询条件
        Page<CountryMkuPO> dbRecord = new Page<>(input.getIndex(), input.getSize());
        // 构建查询条件
        this.buildQuery(input);
        this.convertStatus(input);

        long total = countryMkuAtomicService.pageSearchTotal(input);
        dbRecord.setTotal(total);

        if (total == 0) {
            pageInfo.setTotal(0);
            return pageInfo;
        }
        // 分页查询草稿
        List<CountryMkuPageVO.Response> pageList = countryMkuAtomicService.pageSearch(input);
        if (CollectionUtils.isEmpty(pageList)){
            return pageInfo;
        }
        // 补充采销ERP
        this.setBuyer(input.getTargetCountryCode(),pageList);
        // 补充国家名称
        this.setCountryName(pageList);
        // 补充品牌名称
        this.setMkuTitle(pageList);
        // 补充品牌名称
        this.setBrandName(pageList);
        // 补充类目名称
        this.setCatIdAndName(pageList);
        // 补充库存
        this.setStockNum(pageList);
        // 补充发货实效
        this.setProductCycle(pageList);
        // 设置标签
        this.setMkuTag(pageList);
        // 设置sku、国家成本价、国家跨境入仓价、预估跨境退税金额、国家协议价、国家VIP价
        this.setPrice(pageList);

        this.setPurchase(pageList);
        pageInfo.setTotal(total);
        pageInfo.setRecords(pageList);
        return pageInfo;
    }

    public void setPrice(List<CountryMkuPageVO.Response> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        List<Long> skuIds = inputs.stream().map(CountryMkuPageVO.Response::getSkuId).collect(Collectors.toList());
        CountryAgreementPriceReqVO  reqVO  = new CountryAgreementPriceReqVO();
        reqVO.setSkuIds(skuIds);
        reqVO.setTargetCountryCode(inputs.get(0).getTargetCountryCode());
        // 设置国家成本价 国家协议价
        List<CountryAgreementPricePO> countryAgreementPriceList = countryAgreementPriceAtomicService.getCountryAgreementPriceList(reqVO);
        if(CollectionUtils.isNotEmpty(countryAgreementPriceList)){
            Map<Long,CountryAgreementPricePO> countryAgreementPriceMap = countryAgreementPriceList.stream().collect(Collectors.toMap(CountryAgreementPricePO::getSkuId, Function.identity(), (existing, replacement) -> existing));
            for(CountryMkuPageVO.Response input : inputs) {
                if(countryAgreementPriceMap.containsKey(input.getSkuId())) {
                    CountryAgreementPricePO countryAgreementPricePO = countryAgreementPriceMap.get(input.getSkuId());
                    input.setCountryCostPrice(countryAgreementPricePO.getCountryCostPrice());
                    input.setCountryAgreementPrice(countryAgreementPricePO.getAgreementPrice());
                    input.setCurrency(countryAgreementPricePO.getCurrency());
                    input.setAgreementPriceMark(countryAgreementPricePO.getAgreementMark());
                    input.setCostPriceMark(countryAgreementPricePO.getCostMark());
                }
            }
        }
        // 预估跨境退税金额、国家跨境入仓价
        CountryExtendPriceReqVO extendReqVO = new CountryExtendPriceReqVO();
        extendReqVO.setSkuIds(skuIds);
        extendReqVO.setTargetCountryCode(inputs.get(0).getTargetCountryCode());
        List<CountryExtendPricePO> countryExtendPricePOList = countryExtendPriceAtomicService.getPricePoList(extendReqVO);

        if(CollectionUtils.isNotEmpty(countryExtendPricePOList)){
            Map<Long, List<CountryExtendPricePO>> skuExtendPriceMap = countryExtendPricePOList.stream()
                .collect(Collectors.groupingBy(CountryExtendPricePO::getSkuId));
            for(CountryMkuPageVO.Response input : inputs) {
                if(skuExtendPriceMap.containsKey(input.getSkuId())) {
                    List<CountryExtendPricePO> countryExtendPricePOS = skuExtendPriceMap.get(input.getSkuId());
                    for (CountryExtendPricePO countryExtendPricePO : countryExtendPricePOS){
                        if(CountryExtendPriceEnum.WAREHOUSING_PRICE.getCode().equals(countryExtendPricePO.getBusinessType())){
                            input.setCountryWarehousingPrice(countryExtendPricePO.getPrice());
                            input.setWarehousingPriceMark(countryExtendPricePO.getPriceMark());
                        }
                        if(CountryExtendPriceEnum.REFUND_TAX_PRICE.getCode().equals(countryExtendPricePO.getBusinessType())){
                            DataResponse<BigDecimal> exchangeRateResponse = exchangeRateManageService.getExchangeRateByCurrency( CurrencyConstant.CURRENCY_ZH, input.getCurrency());
                            BigDecimal exchangeRate = exchangeRateResponse.getData();
                            if(Objects.nonNull(countryExtendPricePO.getPrice()) && Objects.nonNull(exchangeRate)){
                                BigDecimal refundTaxPrice = countryExtendPricePO.getPrice().multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
                                input.setRefundTaxPrice(refundTaxPrice);
                                String refundTaxPriceMsg = String.format("预估跨境退税金额%s=预估跨境退税金额%s*汇率%s;\n",refundTaxPrice.stripTrailingZeros().toPlainString()
                                    ,countryExtendPricePO.getPrice().stripTrailingZeros().toPlainString(),exchangeRate.stripTrailingZeros().toPlainString());
                                String priceMark = refundTaxPriceMsg + countryExtendPricePO.getPriceMark();
                                input.setRefundTaxPriceMark(priceMark);
                            }
                        }
                        if(CountryExtendPriceEnum.JD_PRICE.getCode().equals(countryExtendPricePO.getBusinessType())){
                            input.setJdPrice(countryExtendPricePO.getPrice());
                            input.setJdPriceMark(countryExtendPricePO.getPriceMark());
                        }
                    }
                }
            }
        }

    }

    /**
     * 设置MKU标签
     * @param inputs 输入的CountryMkuPageVO.Response列表
     */
    public void setMkuTag(List<CountryMkuPageVO.Response> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Set<Long> mkuIds = inputs.stream().map(CountryMkuPageVO.Response::getMkuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(mkuIds)){
            log.error("CountryMkuManageServiceImpl.setProductCycle mkuIds is null");
            return;
        }
        MkuTagPageVO mkuTagVO = new MkuTagPageVO();
        mkuTagVO.setMkuList(new ArrayList<>(mkuIds));
        mkuTagVO.setTargetCountryCode(inputs.get(0).getTargetCountryCode());
        Map<Long,List<SpecialAttrTagVO>> mkuSpecialMap = mkuTagManageService.getMkuTagVOMap(mkuTagVO);
        for (CountryMkuPageVO.Response response : inputs){
            if(mkuSpecialMap.containsKey(response.getMkuId())){
                List<SpecialAttrTagVO> specialAttrTagVOList = mkuSpecialMap.get(response.getMkuId());
                response.setSpecialAttrTagVOList(specialAttrTagVOList);
            }
        }
    }

    public void setTag(List<CountryMkuPageVO.Response> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Set<Long> mkuIds = inputs.stream().map(CountryMkuPageVO.Response::getMkuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(mkuIds)){
            log.error("CountryMkuManageServiceImpl.setProductCycle mkuIds is null");
            return;
        }
        List<MkuRelationPO> mkuRelationPOList = mkuRelationAtomicService.queryBindListByMkuIds(new ArrayList<>(mkuIds));
        if(CollectionUtils.isEmpty(mkuRelationPOList)){
            log.error("CountryMkuManageServiceImpl.setProductCycle mkuRelationPOList is null");
            return;
        }

        Set<Long> skuSet = mkuRelationPOList.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(skuSet)){
            log.error("CountryMkuManageServiceImpl.setProductCycle skuIds is null.");
            return;
        }
       /* List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(skuIds);
        if(CollectionUtils.isEmpty(skuPOList)){
            log.error("CountryMkuManageServiceImpl.setProductCycle skuPOList is null");
            return;
        }*/

        //Set<Long> skuSet = skuVOList.stream().map(SkuVO::getSkuId).collect(Collectors.toSet());
        // 通过skuId查询 商品标  tag keyId， keyValueId
        List<SpecialAttrRelationPO> specialAttrRelationPOList = specialAttrRelationAtomicService.queryBySkus(skuSet);
        if(CollectionUtils.isEmpty(specialAttrRelationPOList)){
            return;
        }
        Set<Long> tagKeyIdList = specialAttrRelationPOList.stream().map(SpecialAttrRelationPO::getAttributeKeyId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(tagKeyIdList)){
            return;
        }
        // 通过tagKeyId查询 商品标名称和标授权国家
        List<SpecialAttrPO> specialAttrPOList = specialAttrAtomicService.queryByIds(tagKeyIdList);
        if(CollectionUtils.isEmpty(specialAttrPOList)){
            return;
        }
        // 过滤和当前目标授权的国家
        Set<Long> tagKeyIdSet = specialAttrPOList.stream().filter(po -> {
            if (Objects.nonNull(po.getCountryScope()) && po.getCountryScope().contains(inputs.get(0).getTargetCountryCode())) {
                return true;
            } else {
                return false;
            }
        }).map(SpecialAttrPO::getId).collect(Collectors.toSet());

        if(CollectionUtils.isEmpty(tagKeyIdSet)){
            return;
        }
        Map<Long,List<SpecialAttrRelationPO>> tagMap = specialAttrRelationPOList.stream().filter(po->{
            if(tagKeyIdSet.contains(po.getAttributeKeyId())){
                return true;
            } else {
                return false;
            }
        }).collect(Collectors.groupingBy(SpecialAttrRelationPO::getSkuId));
        // 使用 MapUtils 判断 tagMap 是否为空
        if (MapUtils.isEmpty(tagMap)) {
            return;
        }


        Map<Long, Long> mkuSkuMap = mkuRelationPOList.stream().collect(Collectors.toMap(MkuRelationPO::getMkuId, MkuRelationPO::getSkuId));
        if(MapUtils.isEmpty(mkuSkuMap)){
            return;
        }

        for (CountryMkuPageVO.Response response : inputs){
            if(mkuSkuMap.containsKey(response.getMkuId())){
                Long skuId = mkuSkuMap.get(response.getMkuId());
                if(tagMap.containsKey(skuId)){
                    List<SpecialAttrRelationPO> specialAttrRelationPOS = tagMap.get(skuId);
                    Set<Long> tagValueIdSet= specialAttrRelationPOS.stream().map(SpecialAttrRelationPO::getAttributeValueId).collect(Collectors.toSet());
                    if(CollectionUtils.isEmpty(tagValueIdSet)){
                        return;
                    }
                    List<SpecialAttrValuePO> specialAttrValuePOS = specialAttrValueAtomicService.queryByIds(tagValueIdSet);
                    if(CollectionUtils.isEmpty(specialAttrValuePOS)){
                        return;
                    }
                    List<String> tagNameList = specialAttrValuePOS.stream().map(SpecialAttrValuePO::getAttributeValueName).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(tagNameList)){
                        response.setTagNameList(tagNameList);
                    }
                }
            }
        }
    }

    @Override
    public List<CountryMkuPageVO.Request> auditStatusNum(CountryMkuPageVO.Request input) {

        // 转换传参
        this.buildQuery(input);

        List<CountryMkuPageVO.Request> auditNumVOResp = new ArrayList<>();
        input.setTypeKey(all);
        this.convertStatus(input);
        auditNumVOResp.add(this.getNumVO(input));

        input.setTypeKey(pool);
        this.convertStatus(input);
        auditNumVOResp.add(this.getNumVO(input));

        input.setTypeKey(waiting);
        this.convertStatus(input);
        auditNumVOResp.add(this.getNumVO(input));

        input.setTypeKey(black);
        this.convertStatus(input);
        auditNumVOResp.add(this.getNumVO(input));

        input.setTypeKey(out);
        this.convertStatus(input);
        auditNumVOResp.add(this.getNumVO(input));

        input.setTypeKey(warn);
        this.convertStatus(input);
        auditNumVOResp.add(this.getNumVO(input));

        input.setTypeKey(customerNotCountry);
        this.convertStatus(input);
        auditNumVOResp.add(this.getNumVO(input));
        return auditNumVOResp;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<String> batchBlack(CountryMkuApproveVO input) {
        this.checkParam(input);
        Set<Long> ids = input.getIds();
        List<CountryMkuPO> countryMkuPOS = countryMkuAtomicService.getValidByIds(ids);
        if(CollectionUtils.isEmpty(countryMkuPOS)){
            throw new BizException("查无数据请刷新页面");
        }

        countryMkuPOS = countryMkuPOS.stream().filter(item->
            CountryMkuPoolStatusEnum.POOL.getCode().equals(item.getPoolStatus())
                || CountryMkuPoolStatusEnum.DRAFT.getCode().equals(item.getPoolStatus())
                || CountryMkuPoolStatusEnum.NOT_POOL.getCode().equals(item.getPoolStatus())
        ).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(countryMkuPOS) || ids.size() != countryMkuPOS.size()){
            throw new BizException("您操作的数据已被变更，入池状态有错误，请刷新页面");
        }

        Set<String> targetCountryCodes = countryMkuPOS.stream().map(CountryMkuPO::getTargetCountryCode).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(targetCountryCodes) || targetCountryCodes.size() > 1){
            throw new BizException("系统错误，您跨国审批了");
        }

        //        String countryCode = new ArrayList<>(targetCountryCodes).get(0);
        //        List<String> approvers = this.getCountryApprover(countryCode);
        //        if(CollectionUtils.isEmpty(approvers) || !approvers.contains(LoginContextHolder.getLoginContextHolder().getPin())){
        //            throw new BizException("您无权审批");
        //        }

        List<CountryMkuPO> updates = new ArrayList<>();
        countryMkuPOS.forEach(item->{
            CountryMkuPO mkuPO = new CountryMkuPO();
            mkuPO.setId(item.getId());
            mkuPO.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
            mkuPO.setUpdateTime(new Date().getTime());
            mkuPO.setWarnStatus(item.getWarnStatus());
            mkuPO.setWarnReason(item.getWarnReason());
            mkuPO.setBlackReason(item.getBlackReason());
            mkuPO.setUndeterminedReason(item.getUndeterminedReason());
            mkuPO.setPoolOriginStatus(item.getPoolStatus());
            mkuPO.setWarnStatus(CountryMkuWarnStatusEnum.NORMAL.getCode());
            mkuPO.setPoolStatus(CountryMkuPoolStatusEnum.BLACK.getCode());
            mkuPO.setBlackReason(CountryMkuBlackReasonEnum.HANDLE.getCode().toString());
            updates.add(mkuPO);
        });
        if(CollectionUtils.isNotEmpty(updates)){
            boolean result = countryMkuAtomicService.updateBatchById(updates);
            if(result){
                for (int i = 0;i < updates.size();i++){
                    CountryMkuPO sourceData = countryMkuPOS.get(i);
                    CountryMkuPO targetData = updates.get(i);
                    sendLog(sourceData.getTargetCountryCode() + sourceData.getMkuId(),sourceData,targetData);
                }
            }
        }
        return DataResponse.success();
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<String> batchOutBlack(CountryMkuApproveVO input) {
        this.checkParam(input);
        Set<Long> ids = input.getIds();
        List<CountryMkuPO> countryMkuPOS = countryMkuAtomicService.getValidByIds(ids);
        if(CollectionUtils.isEmpty(countryMkuPOS)){
            throw new BizException("查无数据请刷新页面");
        }

        countryMkuPOS = countryMkuPOS.stream().filter(item->
                CountryMkuPoolStatusEnum.BLACK.getCode().equals(item.getPoolStatus()))
            .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(countryMkuPOS) || ids.size() != countryMkuPOS.size()){
            throw new BizException("您操作的数据已被变更，请刷新页面");
        }

        Set<String> targetCountryCodes = countryMkuPOS.stream().map(CountryMkuPO::getTargetCountryCode).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(targetCountryCodes) || targetCountryCodes.size() > 1){
            throw new BizException("系统错误，您跨国审批了");
        }

        //        String countryCode = new ArrayList<>(targetCountryCodes).get(0);
        //        List<String> approvers = this.getCountryApprover(countryCode);
        //        if(CollectionUtils.isEmpty(approvers) || !approvers.contains(LoginContextHolder.getLoginContextHolder().getPin())){
        //            throw new BizException("您无权审批");
        //        }

        List<CountryMkuPO> updates = new ArrayList<>();
        countryMkuPOS.forEach(item->{
            CountryMkuPO mkuPO = new CountryMkuPO();
            mkuPO.setId(item.getId());
            mkuPO.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
            mkuPO.setUpdateTime(new Date().getTime());
            mkuPO.setWarnStatus(item.getWarnStatus());
            mkuPO.setWarnReason(item.getWarnReason());
            mkuPO.setUndeterminedReason(item.getUndeterminedReason());
            // 显式控制
            mkuPO.setBlackReason(null);
            mkuPO.setPoolOriginStatus(null);

            mkuPO.setPoolStatus(CountryMkuPoolStatusEnum.NOT_POOL.getCode());
            updates.add(mkuPO);
        });
        if(CollectionUtils.isNotEmpty(updates)){
            boolean result = countryMkuAtomicService.updateBatchById(updates);
            if(result){
                for (int i = 0;i < updates.size();i++){
                    CountryMkuPO sourceData = countryMkuPOS.get(i);
                    CountryMkuPO targetData = updates.get(i);
                    sendLog(sourceData.getTargetCountryCode() + sourceData.getMkuId(),sourceData,targetData);
                }
                List<Long> mkuIdList = countryMkuPOS.stream().map(CountryMkuPO::getMkuId).collect(Collectors.toList());
                CountryMkuReqVO countryMkuReqVO = new CountryMkuReqVO();
                countryMkuReqVO.setMkuIdList(mkuIdList);
                countryMkuReqVO.setCountryCode(countryMkuPOS.get(0).getTargetCountryCode());
                this.mkuPoolJoinCountryPool(countryMkuReqVO);
            }
        }
        return DataResponse.success();
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<String> batchPool(CountryMkuApproveVO input) {
        this.checkParam(input);
        return DataResponse.success();
    }

    /**
     * 检查国家MKU审批参数
     * @param input 国家MKU审批的输入参数
     */
    private void checkParam(CountryMkuApproveVO input){
        if(CollectionUtils.isEmpty(input.getIds())){
            throw new BizException("id为空");
        }
        //        if(input.getApproveStatus() == null){
        //            throw new BizException("审批状态为空");
        //        }
    }

    /**
     * 构建查询条件。
     * @param input 包含查询条件的对象。
     */
    private void buildQuery(CountryMkuPageVO.Request input){
        Set<Long> mkuIdSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(input.getMkuIds())){
            mkuIdSet.addAll(input.getMkuIds());
        }

        if(CollectionUtils.isNotEmpty(input.getProductIds())){
            ProductIdVO productIdVO = skuReadManageService.getProductInfoByIds(input.getProductIds());
            if(CollectionUtils.isNotEmpty(productIdVO.getMkuIds())){
                mkuIdSet.addAll(productIdVO.getMkuIds());
            }
        }

        if(CollectionUtils.isNotEmpty(input.getSkuIds())){
            Map<Long, Long> skuIdMkuIdMap = mkuRelationAtomicService.skuIdMkuIdMapBySkuIds(input.getSkuIds());
            if(CollectionUtils.isNotEmpty(skuIdMkuIdMap.values())){
                if(CollectionUtils.isNotEmpty(mkuIdSet)){
                    mkuIdSet.retainAll(skuIdMkuIdMap.values());
                }else {
                    mkuIdSet.addAll(skuIdMkuIdMap.values());
                }
            }
            if(CollectionUtils.isEmpty(mkuIdSet)){
                mkuIdSet.add(-1L);
            }
        }

        if(StringUtils.isNotBlank(input.getMkuTitle())){
            // mku中文名称查询mkuId
            List<Long> mkuIds = mkuLangAtomicService.getMkuIds(input.getMkuTitle(), LangContextHolder.get());
            if(CollectionUtils.isNotEmpty(mkuIds)){
                if(CollectionUtils.isNotEmpty(mkuIdSet)){
                    mkuIdSet.retainAll(mkuIds);
                }else {
                    mkuIdSet.addAll(mkuIds);
                }
            }

            if(CollectionUtils.isEmpty(mkuIdSet)){
                mkuIdSet.add(-1L);
            }
        }

        if(CollectionUtils.isNotEmpty(input.getTagIdList())){
            List<MkuTagPO> mkuTagPOList = new ArrayList<>();
            List<MkuTagIdVO> tagIdList = input.getTagIdList();

            tagIdList.forEach( vo ->{
                MkuTagPO mkuTagPO = new MkuTagPO();
                mkuTagPO.setAttributeKeyId(vo.getAttributeKeyId());
                mkuTagPO.setAttributeValueId(vo.getAttributeValueId());
                mkuTagPOList.add(mkuTagPO);
            } );
            Set<Long> mkuIdTagSet = mkuTagAtomicService.getMkuIdSetByList(mkuTagPOList,input.getTargetCountryCode());
            if(CollectionUtils.isNotEmpty(mkuIdSet)){
                mkuIdSet.retainAll(mkuIdTagSet);
            }else {
                mkuIdSet.addAll(mkuIdTagSet);
            }
            if(CollectionUtils.isEmpty(mkuIdSet)){
                mkuIdSet.add(-1L);
            }
        }
        input.setMkuIds(new ArrayList<>(mkuIdSet));

        // 类目id转换为终极类目id
        Set<Long> catIdSet = new HashSet<>();
        Set<Long> lastCatIds = this.getLastCatIds(input);
        if(CollectionUtils.isNotEmpty(lastCatIds)){
            catIdSet.addAll(lastCatIds);
        }

        String buyer = input.getBuyer();
        if(StringUtils.isNotBlank(buyer)){
            Set<Long> catIdsBuyer = categoryBuyerRelationAtomicService.getCatIdByBuyer(buyer);
            if(CollectionUtils.isNotEmpty(catIdsBuyer)){
                if(CollectionUtils.isNotEmpty(catIdSet)){
                    catIdSet.retainAll(catIdsBuyer);
                }else {
                    catIdSet.addAll(catIdsBuyer);
                }
            }

            if(CollectionUtils.isEmpty(catIdSet)){
                catIdSet.add(-1L);
            }
        }
        input.setCatIds(catIdSet);
    }

    private void convertStatus(CountryMkuPageVO.Request input){
        if(StringUtils.equals(input.getTypeKey(),pool)){
            input.setPoolStatusSystem(CountryMkuPoolStatusEnum.POOL.getCode());
        }
        if(StringUtils.equals(input.getTypeKey(),black)){
            input.setPoolStatusSystem(CountryMkuPoolStatusEnum.BLACK.getCode());
        }
        if(StringUtils.equals(input.getTypeKey(),waiting)){
            input.setPoolStatusSystem(CountryMkuPoolStatusEnum.DRAFT.getCode());
        }
        if(StringUtils.equals(input.getTypeKey(),out)){
            input.setPoolStatusSystem(CountryMkuPoolStatusEnum.NOT_POOL.getCode());
        }
        if(StringUtils.equals(input.getTypeKey(),warn)){
            input.setPoolStatusSystem(CountryMkuPoolStatusEnum.POOL.getCode());
            input.setWarnStatusSystem(CountryMkuWarnStatusEnum.WARN.getCode());
        }
        if(StringUtils.equals(input.getTypeKey(),customerNotCountry)){
            input.setPoolStatusSystem(CountryMkuPoolStatusEnum.NOT_POOL.getCode());
        }
    }

    /**
     * 获取最后一级分类的ID集合。
     * @param input 包含分类ID信息的请求对象。
     * @return 最后一级分类的ID集合。
     */
    private Set<Long> getLastCatIds(CountryMkuPageVO.Request input){
        Long firstCatId = input.getFirstCatId();
        Long secondCatId = input.getSecondCatId();
        Long thirdCatId = input.getThirdCatId();
        Long lastCatId = input.getLastCatId();

        Set<Long> result = new HashSet<>();
        if(lastCatId != null){
            result.add(lastCatId);
            return result;
        }

        if(thirdCatId != null){
            List<Long> thirdCatIds = categoryOutService.categoryLevel4From1234(thirdCatId);
            if(CollectionUtils.isNotEmpty(thirdCatIds)){
                result.addAll(thirdCatIds);
            }
            return result;
        }

        if(secondCatId != null){
            List<Long> secondCatIds = categoryOutService.categoryLevel4From1234(secondCatId);
            if(CollectionUtils.isNotEmpty(secondCatIds)){
                result.addAll(secondCatIds);
            }
            return result;
        }

        if(firstCatId != null){
            List<Long> firstCatIds = categoryOutService.categoryLevel4From1234(firstCatId);
            if(CollectionUtils.isNotEmpty(firstCatIds)){
                result.addAll(firstCatIds);
            }
            return result;
        }
        return null;
    }

    /**
     * 根据品牌ID列表查询品牌名称并设置到输入列表中。
     * @param inputs 包含品牌ID的 CountryMkuPageVO.Response 列表
     */
    private void setBrandName(List<CountryMkuPageVO.Response> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Set<Long> brandIds = inputs.stream().map(CountryMkuPageVO.Response::getBrandId).collect(Collectors.toSet());
        Map<Long, String> brandNameMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(brandIds)){
            brandNameMap.putAll(brandOutService.queryNameByIds(brandIds,LangContextHolder.get()));
        }

        inputs.forEach(item->{
            item.setBrandName(brandNameMap.get(item.getBrandId()));
        });
    }

    /**
     * 根据国家代码设置国家名称。
     * @param inputs 要处理的国家信息列表。
     */
    private void setBuyer(String countryCode,List<CountryMkuPageVO.Response> inputs){
        if(StringUtils.isBlank(countryCode) || CollectionUtils.isEmpty(inputs)){
            log.info("CountryMkuManageServiceImpl.setBuyer param is null.countryCode:{}",countryCode);
            return;
        }

        Set<Long> catIds = inputs.stream().map(CountryMkuPageVO.Response::getLastCatId).collect(Collectors.toSet());
        Map<Long,String> catBuyerMap = categoryBuyerRelationAtomicService.getBuyerByCountryCodeAndCatIds(countryCode,catIds);
        if(MapUtils.isEmpty(catBuyerMap)){
            log.info("CountryMkuManageServiceImpl.setBuyer catBuyerMap is null");
            return;
        }

        inputs.forEach(item->{
            item.setBuyer(catBuyerMap.get(item.getLastCatId()));
        });
    }

    /**
     * 根据国家代码设置国家名称。
     * @param inputs 要处理的国家信息列表。
     */
    private void setCountryName(List<CountryMkuPageVO.Response> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Map<String, String> countryMap = countryManageService.getCountryMap(LangContextHolder.get());
        inputs.forEach(item->{
            item.setSourceCountryName(countryMap.get(item.getSourceCountryCode()));
            item.setTargetCountryName(countryMap.get(item.getTargetCountryName()));
        });
    }

    /**
     * 设置MKU标题
     * @param inputs 输入的CountryMkuPageVO.Response列表
     */
    private void setMkuTitle(List<CountryMkuPageVO.Response> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        List<Long> mkuIds = inputs.stream().map(CountryMkuPageVO.Response::getMkuId).collect(Collectors.toList());
        Map<Long, MkuLangPO> mkuTitleMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(mkuIds)){
            mkuTitleMap.putAll(mkuLangAtomicService.getMkuLangNameByMkuIds(mkuIds,LangContextHolder.get()));
        }

        inputs.forEach(item->{
            MkuLangPO mkuLangPO = mkuTitleMap.get(item.getMkuId());
            if(mkuLangPO != null){
                item.setMkuTitle(mkuLangPO.getMkuTitle());
            }
        });
    }

    /**
     * 设置每个 CountryMkuPageVO.Response 对象的 catName 属性，根据 lastCatId 从 categoryOutService 中获取对应的分类名称。
     * @param inputs 包含 lastCatId 的 CountryMkuPageVO.Response 对象列表
     */
    private void setCatIdAndName(List<CountryMkuPageVO.Response> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Set<Long> lastCatIds = inputs.stream().map(CountryMkuPageVO.Response::getLastCatId).collect(Collectors.toSet());
        Map<Long, List<CategoryComboBoxVO>> lastCatMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(lastCatIds)){
            Map<Long, List<CategoryComboBoxVO>> longListMap = categoryOutService.queryPath(lastCatIds, LangContextHolder.get());
            lastCatMap.putAll(longListMap);
        }

        inputs.forEach(item->{
            List<CategoryComboBoxVO> categoryComboBoxVOList = lastCatMap.get(item.getLastCatId());
            if(CollectionUtils.isNotEmpty(categoryComboBoxVOList)){
                if(categoryComboBoxVOList.size() > 3){
                    item.setFirstCatName(categoryComboBoxVOList.get(0).getName());
                    item.setSecondCatName(categoryComboBoxVOList.get(1).getName());
                    item.setThirdCatName(categoryComboBoxVOList.get(2).getName());
                    item.setLastCatName(categoryComboBoxVOList.get(3).getName());
                }else if(categoryComboBoxVOList.size() > 2){
                    item.setFirstCatName(categoryComboBoxVOList.get(0).getName());
                    item.setSecondCatName(categoryComboBoxVOList.get(1).getName());
                    item.setThirdCatName(categoryComboBoxVOList.get(2).getName());
                }else if(categoryComboBoxVOList.size() > 1){
                    item.setFirstCatName(categoryComboBoxVOList.get(0).getName());
                    item.setSecondCatName(categoryComboBoxVOList.get(1).getName());
                }else {
                    item.setFirstCatName(categoryComboBoxVOList.get(0).getName());
                }
            }
        });
    }

    /**
     * 设置库存数量
     * @param inputs CountryMkuPageVO.Response 对象列表，包含需要更新的 MKU ID
     */
    private void setStockNum(List<CountryMkuPageVO.Response> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Set<Long> mkuIds = inputs.stream().map(CountryMkuPageVO.Response::getMkuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(mkuIds)){
            log.error("CountryMkuManageServiceImpl.setStockNum mkuIds is null");
            return;
        }
        List<MkuRelationPO> mkuRelationPOList = mkuRelationAtomicService.queryBindListByMkuIds(new ArrayList<>(mkuIds));
        if(CollectionUtils.isEmpty(mkuRelationPOList)){
            log.error("CountryMkuManageServiceImpl.setStockNum mkuRelationPOList is null");
            return;
        }

        Set<Long> skuIds = mkuRelationPOList.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(skuIds)){
            log.error("CountryMkuManageServiceImpl.setStockNum skuIds is null");
            return;
        }
        List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(skuIds);
        if(CollectionUtils.isEmpty(skuPOList)){
            log.error("CountryMkuManageServiceImpl.setStockNum skuPOList is null");
            return;
        }
        Map<Long,List<MkuRelationPO>> mkuSkuRelationGroup = mkuRelationPOList.stream().collect(Collectors.groupingBy(MkuRelationPO::getMkuId));
        List<SkuPO> skuCNPOS = skuPOList.stream().filter(item-> CountryConstant.COUNTRY_ZH.equals(item.getSourceCountryCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(skuCNPOS)){
            Map<Long,Long> jdSkuMap = skuCNPOS.stream()
                .filter(sku -> sku.getJdSkuId() != null && sku.getJdSkuId() != 0)
                .collect(Collectors.toMap(
                    SkuPO::getSkuId,
                    SkuPO::getJdSkuId,
                    (v1, v2) -> v2
                ));
            List<Long> jdSkuIds = skuCNPOS.stream().map(SkuPO::getJdSkuId).collect(Collectors.toList());
            Map<Long, String> jdSkuStockMap = skuReadManageService.getJdSkuStockBySkuIds(jdSkuIds);
            if(MapUtils.isNotEmpty(jdSkuStockMap)){
                Set<Long> cnSkuIds = skuCNPOS.stream().map(SkuPO::getSkuId).collect(Collectors.toSet());
                for(CountryMkuPageVO.Response item : inputs){
                    List<MkuRelationPO> mkuRelationPOS = mkuSkuRelationGroup.get(item.getMkuId());
                    if(CollectionUtils.isEmpty(mkuRelationPOS)){
                        continue;
                    }
                    String totalStock = "";
                    for(MkuRelationPO mkuRelationPO : mkuRelationPOS){
                        Long skuId = mkuRelationPO.getSkuId();
                        if(!cnSkuIds.contains(skuId)){
                            continue;
                        }
                        Long jdSkuId = jdSkuMap.get(skuId);
                        if(jdSkuId == null){
                            continue;
                        }
                        String cnStock = jdSkuStockMap.get(jdSkuId);
                        if(StringUtils.isNotBlank(cnStock)){
                            totalStock = cnStock;
                        }
                    }
                    item.setStockNum(totalStock);
                }
            }
        }

        List<SkuPO> skuNoCNPOS = skuPOList.stream().filter(item-> !CountryConstant.COUNTRY_ZH.equals(item.getSourceCountryCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(skuNoCNPOS)){
            StockManageReqDTO stockManageReqDTO = getStockManageReqDTO(skuNoCNPOS.stream().map(SkuPO::getSkuId).collect(Collectors.toList()));
            Map<Long, StockResDTO> stockMap = stockManageService.getStock(stockManageReqDTO);
            Set<Long> cnNoSkuIds = skuNoCNPOS.stream().map(SkuPO::getSkuId).collect(Collectors.toSet());
            for(CountryMkuPageVO.Response item : inputs){
                List<MkuRelationPO> mkuRelationPOS = mkuSkuRelationGroup.get(item.getMkuId());
                if(CollectionUtils.isEmpty(mkuRelationPOS)){
                    continue;
                }
                Long totalStock = 0L;
                Boolean cnNo = Boolean.TRUE;
                for(MkuRelationPO mkuRelationPO : mkuRelationPOS){
                    Long skuId = mkuRelationPO.getSkuId();
                    if(!cnNoSkuIds.contains(skuId)){
                        cnNo = Boolean.FALSE;
                        continue;
                    }
                    StockResDTO stockResDTO = stockMap.get(skuId);
                    if(stockResDTO != null && stockResDTO.getStock() != null){
                        totalStock = totalStock+stockResDTO.getStock();
                    }
                }
                if(cnNo){
                    item.setStockNum(String.valueOf(totalStock));
                }
            }
        }

        Map<Long, Long> mkuSkuIdMap = mkuRelationPOList.stream()
            .collect(Collectors.toMap(
                MkuRelationPO::getMkuId, // 作为键
                MkuRelationPO::getSkuId, // 作为值
                (existing, replacement) -> replacement // 如果有重复的键，保留最新的值
            ));
        Map<Long, SkuPO> skuIdMap = skuPOList.stream()
            .collect(Collectors.toMap(
                SkuPO::getSkuId,  // 假设getId()方法返回Long类型的ID
                skuPO -> skuPO  // 或者简写为 Function.identity()
            ));

        for (CountryMkuPageVO.Response response : inputs) {
            if(mkuSkuIdMap.containsKey(response.getMkuId())){
                Long skuId = mkuSkuIdMap.get(response.getMkuId());
                response.setSkuId(skuId);
                SkuPO skuPO = skuIdMap.get(response.getSkuId());
                if(Objects.nonNull(skuPO)) {
                    response.setSpuId(skuPO.getSpuId());
                }
            }
        }
    }

    /**
     * 设置产品生命周期
     * @param inputs 输入列表，包含CountryMkuPageVO.Response对象
     */
    private void setProductCycle(List<CountryMkuPageVO.Response> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Set<Long> mkuIds = inputs.stream().map(CountryMkuPageVO.Response::getMkuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(mkuIds)){
            log.error("CountryMkuManageServiceImpl.setProductCycle mkuIds is null");
            return;
        }
        List<MkuRelationPO> mkuRelationPOList = mkuRelationAtomicService.queryBindListByMkuIds(new ArrayList<>(mkuIds));
        if(CollectionUtils.isEmpty(mkuRelationPOList)){
            log.error("CountryMkuManageServiceImpl.setProductCycle mkuRelationPOList is null");
            return;
        }

        Set<Long> skuIds = mkuRelationPOList.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(skuIds)){
            log.error("CountryMkuManageServiceImpl.setProductCycle skuIds is null");
            return;
        }
        List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(skuIds);
        if(CollectionUtils.isEmpty(skuPOList)){
            log.error("CountryMkuManageServiceImpl.setProductCycle skuPOList is null");
            return;
        }
        Map<Long,List<MkuRelationPO>> mkuSkuRelationGroup = mkuRelationPOList.stream().collect(Collectors.groupingBy(MkuRelationPO::getMkuId));
        List<SkuPO> skuCNPOS = skuPOList.stream().filter(item-> CountryConstant.COUNTRY_ZH.equals(item.getSourceCountryCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(skuCNPOS)){
            Map<Long,Long> jdSkuMap = skuCNPOS.stream().collect(Collectors.toMap(SkuPO::getSkuId,SkuPO::getJdSkuId,(v1,v2)->v2));
            List<Long> jdSkuIds = skuCNPOS.stream().map(SkuPO::getJdSkuId).collect(Collectors.toList());
            Map<Long, JdProductDTO> gmsSkuMap = skuInfoRpcService.querySkuMap(new JdProductQueryDTO(new ArrayList<>(jdSkuIds)));
            if(MapUtils.isNotEmpty(gmsSkuMap)){
                for(CountryMkuPageVO.Response item : inputs){
                    List<MkuRelationPO> mkuRelationPOS = mkuSkuRelationGroup.get(item.getMkuId());
                    if(CollectionUtils.isEmpty(mkuRelationPOS)){
                        continue;
                    }
                    Long productCycle = null;
                    for(MkuRelationPO mkuRelationPO : mkuRelationPOS){
                        Long skuId = mkuRelationPO.getSkuId();
                        Long jdSkuId = jdSkuMap.get(skuId);
                        if(jdSkuId == null){
                            continue;
                        }
                        JdProductDTO jdProductDTO = gmsSkuMap.get(jdSkuId);
                        if(jdProductDTO != null){
                            productCycle = jdProductDTO.getSendTemplate();
                        }
                    }
                    if(productCycle != null){
                        item.setProductionCycle(BigDecimal.valueOf(productCycle));
                    }

                }
            }
        }
    }

    /**
     * 根据国家代码获取对应的配置信息。
     * @param sourceCountryCode 国家代码
     * @return 配置信息列表，若国家代码为空或找不到对应配置则返回 null
     */
    public List<BaseErpVO> getCountryConfig(String sourceCountryCode){
        if(StringUtils.isBlank(sourceCountryCode)){
            log.error("CustomerSkuPriceManageServiceImpl.getCountryConfig sourceCountryCode is null");
            return null;
        }

        try {
            JSONObject configJson = JSONObject.parseObject(approveConfig);
            // 获取所有键
            Set<String> keys = configJson.keySet();
            // 遍历所有键，检查对应的值
            for (String key : keys) {
                if(!sourceCountryCode.equals(key)){
                    continue;
                }
                String configValue = configJson.getString(key);
                if(StringUtils.isBlank(configValue)){
                    return null;
                }
                // 将 configValue 转换为 List<String>
                return JSONArray.parseArray(configValue, BaseErpVO.class);
            }
        } catch (Exception e) {
            log.error("CustomerSkuPriceManageServiceImpl.getCountryConfig sourceCountryCode = {}", sourceCountryCode, e);
        }
        return null;
    }

    /**
     * 获取所有审批人ERP账号。
     *
     * @return 所有审批人ERP账号的集合。
     */
    public Set<String> getAllApprover(){
        // {"CN":[{"erp":"zhaokun51","erpName":"赵坤","email":"<EMAIL>"}],"VN":[{"erp":"zhaokun51","erpName":"赵坤","email":"<EMAIL>"}]}
        JSONObject configJson = JSONObject.parseObject(approveConfig);
        Set<String> keys = configJson.keySet();
        Set<String> results = new HashSet<>();
        // 遍历所有键，检查对应的值
        for (String key : keys) {
            String configValue = configJson.getString(key);
            if(StringUtils.isBlank(configValue)){
                continue;
            }
            // 将 configValue 转换为 List<String>
            List<BaseErpVO> erpVOList = JSONArray.parseArray(configValue, BaseErpVO.class);
            if(CollectionUtils.isEmpty(erpVOList)){
                continue;
            }
            results.addAll(erpVOList.stream().map(BaseErpVO::getErp).collect(Collectors.toList()));
        }
        return results;
    }

    /**
     * 根据源国家代码获取对应的审批人列表。
     * @param sourceCountryCode 源国家代码，不能为空。
     * @return 对应的审批人列表，若源国家代码为空则返回 null。
     */
    private List<String> getCountryApprover(String sourceCountryCode){
        if(StringUtils.isBlank(sourceCountryCode)){
            log.error("CustomerSkuPriceManageServiceImpl.getCountryApprover sourceCountryCode is null");
            return null;
        }

        List<BaseErpVO> approveConfigs = this.getCountryConfig(sourceCountryCode);
        if(CollectionUtils.isEmpty(approveConfigs)){
            log.error("CustomerSkuPriceManageServiceImpl.getCountryApprover approveConfigs is null");
            return null;
        }
        return approveConfigs.stream().map(BaseErpVO::getErp).collect(Collectors.toList());
    }

    /**
     * 根据输入的 CountryMkuPageVO.Request 对象，查询并设置总记录数，返回更新后的 Request 对象。
     * @param input 待查询的 CountryMkuPageVO.Request 对象。
     * @return 更新后的 CountryMkuPageVO.Request 对象。
     */
    private CountryMkuPageVO.Request getNumVO(CountryMkuPageVO.Request input) {
        long total = countryMkuAtomicService.pageSearchTotal(input);
        CountryMkuPageVO.Request result = new CountryMkuPageVO.Request();
        BeanUtil.copyProperties(input,result);
        result.setNum(total);
        return result;
    }

    /**
     * 根据给定的 SKU ID 列表，创建一个 StockManageReqDTO 对象。
     * @param skuIdList SKU ID 列表
     * @return StockManageReqDTO 对象，其中每个 StockItemManageReqDTO 的数量都设置为 1
     */
    private StockManageReqDTO getStockManageReqDTO(List<Long> skuIdList){
        StockManageReqDTO stockManageReqDTO = new StockManageReqDTO();
        List<StockItemManageReqDTO> stockItem = new ArrayList<>();
        for (Long id : skuIdList){
            StockItemManageReqDTO stockItemManageReqDTO = new StockItemManageReqDTO();
            stockItemManageReqDTO.setSkuId(id);
            stockItemManageReqDTO.setNum(1L);
            stockItem.add(stockItemManageReqDTO);
        }
        stockManageReqDTO.setStockItem(stockItem);
        return stockManageReqDTO;
    }

    /**
     * 发送日志到数据库。
     * @param mkuId MKU的唯一标识。
     * @param sourceData 日志的源数据。
     * @param targetData 日志的目标数据。
     */
    private void sendLog(String mkuId, CountryMkuPO sourceData, CountryMkuPO targetData) {
        try {
            SkuLogPO skuLogPO = new SkuLogPO();
            skuLogPO.setSourceJson(JSONObject.toJSONString(sourceData));
            skuLogPO.setTargetJson(JSONObject.toJSONString(targetData));
            skuLogPO.setKeyType(KeyTypeEnum.COUNTRY_POOL.getCode());
            skuLogPO.setKeyId(mkuId);
            skuLogPO.setCreator(targetData.getUpdater());
            skuLogPO.setUpdater(targetData.getUpdater());
            skuLogAtomicService.save(skuLogPO);
            log.info("日志保存成功，MKU: {}", mkuId);
        } catch (Exception e) {
            log.error("存储日志异常，MKU: {},sourceData:{},targetData:{} ,Error: {}", mkuId, JSONObject.toJSONString(sourceData), JSONObject.toJSONString(targetData), e.getMessage(), e);
        }
    }


    @Override
    public DataResponse<CountryMkuCheckVO> checkBlackData(CountryMkuCheckReqVO reqVO) {
        List<CountryMkuPO> countryMkuPOList = countryMkuAtomicService.getPoByMkuIdCountryPoolStatus(reqVO.getMkuIdList(), reqVO.getTargetCountryCode(),null);
        Map<Long, Long> mkuIdIdMap = countryMkuPOList.stream().collect(Collectors.toMap(CountryMkuPO::getMkuId, CountryMkuPO::getId));
        if(reqVO.getMkuIdList().size() != countryMkuPOList.size()){
            throw new BizException("mkuId查询数据量异常,请重新操作!");
        }
        Map<Long, List<CustomerMkuVO>> customerMkuMap = getCustomerMkuList(reqVO);
        List<Long> inPoolIdList = new ArrayList<>();
        List<Long> notPoolMkuList = new ArrayList<>();
        List<Long> notPoolIdList = new ArrayList<>();
        StringBuffer sb  = new StringBuffer();
        for (Long mkuId : reqVO.getMkuIdList()){
            if(customerMkuMap.containsKey(mkuId)){
                List<CustomerMkuVO> customerMkuVOList = customerMkuMap.get(mkuId);
                List<String> nameList = customerMkuVOList.stream().map(CustomerMkuVO::getClientName).collect(Collectors.toList());
                String nameStr = StringUtils.join(nameList, Constant.COMMA);
                String formatStr = String.format("MKUID%s,已在%s客户池,拉黑此商品,客户池内商品不可售;</br>",mkuId,nameStr);
                sb.append(formatStr);
                inPoolIdList.add(mkuIdIdMap.get(mkuId));
            }else {
                notPoolIdList.add(mkuIdIdMap.get(mkuId));
                notPoolMkuList.add(mkuId);
            }
        }
        if(CollectionUtils.isNotEmpty(notPoolMkuList)){
            String mkuIdStr = StringUtils.join(notPoolMkuList, Constant.COMMA);
            String formatStr = String.format("MKUID %s未入客户池,拉黑此商品,此商品不可入客户池进行销售;</br>",mkuIdStr);
            sb.append(formatStr);
        }
        CountryMkuCheckVO countryMkuCheckVO = new CountryMkuCheckVO();
        countryMkuCheckVO.setInPoolIdList(inPoolIdList);
        countryMkuCheckVO.setNotPoolIdList(notPoolIdList);
        countryMkuCheckVO.setNotice(sb.toString());
        return DataResponse.success(countryMkuCheckVO);
    }

    @Override
    public DataResponse<CountryMkuCheckVO> checkOutBlackData(CountryMkuCheckReqVO reqVO) {
        List<CountryMkuPO> countryMkuPOList = countryMkuAtomicService.getPoByMkuIdCountryPoolStatus(reqVO.getMkuIdList(), reqVO.getTargetCountryCode(),CountryMkuPoolStatusEnum.BLACK.getCode());
        Map<Long, Long> mkuIdIdMap = countryMkuPOList.stream().collect(Collectors.toMap(CountryMkuPO::getMkuId, CountryMkuPO::getId));
        if(reqVO.getMkuIdList().size() != countryMkuPOList.size()){
            throw new BizException("mkuId查询数据量异常,请重新操作!");
        }
        Map<Long, List<CustomerMkuVO>> customerMkuMap = getCustomerMkuList(reqVO);
        List<Long> inPoolIdList = new ArrayList<>();
        List<Long> notPoolMkuList = new ArrayList<>();
        List<Long> notPoolIdList = new ArrayList<>();
        StringBuffer sb  = new StringBuffer();
        for (Long mkuId : reqVO.getMkuIdList()){
            if(customerMkuMap.containsKey(mkuId)){
                List<CustomerMkuVO> customerMkuVOList = customerMkuMap.get(mkuId);
                List<String> nameList = customerMkuVOList.stream().map(CustomerMkuVO::getClientName).collect(Collectors.toList());
                String nameStr = StringUtils.join(nameList, Constant.COMMA);
                String formatStr = String.format("MKUID %s,已在%s客户池,解除拉黑将重新触发入国家池规则，若满足规则此商品在客户池内商品可售;</br>",mkuId,nameStr);
                sb.append(formatStr);
                inPoolIdList.add(mkuIdIdMap.get(mkuId));
            }else {
                notPoolIdList.add(mkuIdIdMap.get(mkuId));
                notPoolMkuList.add(mkuId);
            }
        }
        if(CollectionUtils.isNotEmpty(notPoolMkuList)){
            String mkuIdStr = StringUtils.join(notPoolMkuList, Constant.COMMA);
            String formatStr = String.format("MKUID %s 未入客户池,解除拉黑将重新触发入国家池规则，若满足则此商品可入客户池进行销售;</br>",mkuIdStr);
            sb.append(formatStr);
        }
        CountryMkuCheckVO countryMkuCheckVO = new CountryMkuCheckVO();
        countryMkuCheckVO.setInPoolIdList(inPoolIdList);
        countryMkuCheckVO.setNotPoolIdList(notPoolIdList);
        countryMkuCheckVO.setNotice(sb.toString());
        return DataResponse.success(countryMkuCheckVO);
    }



    private Map<Long, List<CustomerMkuVO>> getCustomerMkuList(CountryMkuCheckReqVO reqVO){
        List<CustomerMkuVO> customerMkuVOList = customerMkuManageService.listCustomerMkuByMkuIdCountry(reqVO.getMkuIdList(), reqVO.getTargetCountryCode());
        // 按照 mkuId 进行聚合操作并转换为 Map
        Map<Long, List<CustomerMkuVO>> customerMkuVOMap = customerMkuVOList.stream()
            .collect(Collectors.groupingBy(CustomerMkuVO::getMkuId));

        return customerMkuVOMap;
    }


    @Override
    public DataResponse<List<MkuClientInPoolVO>> checkInPool(CountryMkuCheckReqVO input) {
        List<MkuClientInPoolVO> voList = new ArrayList<>();
        if(input.getTargetCountryCode().equals(CountryConstant.COUNTRY_ZH)){
            for (Long mkuId : input.getMkuIdList()){
                MkuClientInPoolVO clientInPoolVO = new MkuClientInPoolVO();
                clientInPoolVO.setCountryPoolFlag(true);
                clientInPoolVO.setMkuId(mkuId);
                voList.add(clientInPoolVO);
            }
            return DataResponse.success(voList);
        }

        List<CountryMkuPO> countryMkuPOList = countryMkuAtomicService.getPoByMkuIdCountryPoolStatus(input.getMkuIdList(), input.getTargetCountryCode(),CountryMkuPoolStatusEnum.POOL.getCode());
        Set<Long> mkuIdSet = countryMkuPOList.stream().map(CountryMkuPO::getMkuId).collect(Collectors.toSet());
        for (Long mkuId : input.getMkuIdList()){
            MkuClientInPoolVO clientInPoolVO = new MkuClientInPoolVO();
            clientInPoolVO.setCountryPoolFlag(false);
            clientInPoolVO.setMkuId(mkuId);
            if(mkuIdSet.contains(mkuId)){
                clientInPoolVO.setCountryPoolFlag(true);
            }
            voList.add(clientInPoolVO);
        }
        return DataResponse.success(voList);
    }

    @Override
    public DataResponse<List<MkuClientUpSaleStatusVO>> checkUpSaleStatus(CountryMkuCheckReqVO input){
        List<MkuClientUpSaleStatusVO> voList = new ArrayList<>();
        List<CountryMkuPO> countryMkuPOList = countryMkuAtomicService.getPoByMkuIdCountrySaleStatus(input.getMkuIdList(), input.getTargetCountryCode(),CountryMkuStatusEnum.UP_SALE.getCode());
        Map<Long, CountryMkuPO> mkuIdMkuMap = countryMkuPOList.stream().collect(Collectors.toMap(CountryMkuPO::getMkuId, Function.identity(), (v1, v2) -> v2));
        for (Long mkuId : input.getMkuIdList()){
            MkuClientUpSaleStatusVO upSaleStatusVO = new MkuClientUpSaleStatusVO();
            upSaleStatusVO.setCountryMkuStatus(CountryMkuStatusEnum.DOWN_SALE.getCode());
            upSaleStatusVO.setMkuId(mkuId);
            if(mkuIdMkuMap.containsKey(mkuId)){
                upSaleStatusVO.setCountryMkuStatus(CountryMkuStatusEnum.UP_SALE.getCode());
            }
            voList.add(upSaleStatusVO);
        }
        return DataResponse.success(voList);
    }

    @Override
    public DataResponse<Boolean> anewMkuJoinCountryPool(CountryMkuReqVO reqVO) {
        CountryMkuPO countryMkuPO = countryMkuAtomicService.getByMkuIdCountryCode(reqVO.getMkuId(), reqVO.getCountryCode());
        if(Objects.isNull(countryMkuPO)){
            return DataResponse.success(true);
        }
        Boolean delete = delete(countryMkuPO.getId());
        if(delete){
            log.info("CountryMkuManageServiceImpl.anewMkuJoinCountryPool 删除国家池数据 data:{}",JSON.toJSONString(countryMkuPO));
            //DataResponse<Boolean> response = mkuMsgJoinCountryPool(reqVO);
            //return response;
            return DataResponse.success(true);
        }
        return DataResponse.error("触发预选池规则失败");
    }

    @Override
    public PageInfo<CountryMkuExportVO> queryCountryMku(CountryMkuPageVO.Request input) {
        PageInfo<CountryMkuExportVO> pageInfo = new PageInfo<>();
        pageInfo.setSize(input.getSize());
        pageInfo.setIndex(input.getIndex());
        PageInfo<CountryMkuPageVO.Response> responsePageInfo = this.pageSearch(input);
        List<CountryMkuPageVO.Response> pageList = responsePageInfo.getRecords();
        if(CollectionUtils.isEmpty(pageList)){
            return pageInfo;
        }
        // 国家Mku导出VOList
        List<CountryMkuExportVO> countryMkuExportVOList = CountryMkuConvert.INSTANCE.listVo2VO(pageList);
        // 设置导出国家池信息
        this.setExportCountryMkuInfo(countryMkuExportVOList);
        // 补充MKU名称
        this.setExportMkuTitle(countryMkuExportVOList);
        // 补充品牌名称
        this.setExportBrandName(countryMkuExportVOList);
        // 补充类目名称
        this.setExportCatIdAndName(countryMkuExportVOList);
        // 是否有标
        this.setExportHaveTag(countryMkuExportVOList);
        // sku jdSku 规格型号
        countryMkuExportVOList = this.setExportSkuInfo(countryMkuExportVOList);
        // 是否备货
        //this.setPurchase(countryMkuExportVOList);

        // 设置sku、国家成本价、国家跨境入仓价、预估跨境退税金额、国家协议价、国家VIP价
        //this.setExportPrice(countryMkuExportVOList);
        // 巴西设置NCM码，销售CST，审核状态，税务审核状态
        this.setExportBrazilInfo(input.getTargetCountryCode(),countryMkuExportVOList);

        pageInfo.setTotal(responsePageInfo.getTotal());
        pageInfo.setRecords(countryMkuExportVOList);
        return pageInfo;
    }

    private void setExportPrice(List<CountryMkuExportVO> countryMkuExportVOList){
        if(CollectionUtils.isEmpty(countryMkuExportVOList)){
            return;
        }
        List<Long> skuIds = countryMkuExportVOList.stream().map(CountryMkuExportVO::getSkuId).collect(Collectors.toList());
        CountryAgreementPriceReqVO  reqVO  = new CountryAgreementPriceReqVO();
        reqVO.setSkuIds(skuIds);
        reqVO.setTargetCountryCode(countryMkuExportVOList.get(0).getTargetCountryCode());
        // 设置国家成本价 国家协议价
        List<CountryAgreementPricePO> countryAgreementPriceList = countryAgreementPriceAtomicService.getCountryAgreementPriceList(reqVO);
        if(CollectionUtils.isNotEmpty(countryAgreementPriceList)){
            Map<Long,CountryAgreementPricePO> countryAgreementPriceMap = countryAgreementPriceList.stream().collect(Collectors.toMap(CountryAgreementPricePO::getSkuId, Function.identity(), (existing, replacement) -> existing));
            for(CountryMkuExportVO input : countryMkuExportVOList) {
                if(countryAgreementPriceMap.containsKey(input.getSkuId())) {
                    CountryAgreementPricePO countryAgreementPricePO = countryAgreementPriceMap.get(input.getSkuId());
                    input.setCountryCostPrice(countryAgreementPricePO.getCountryCostPrice());
                    input.setCountryAgreementPrice(countryAgreementPricePO.getAgreementPrice());
                    input.setCurrency(countryAgreementPricePO.getCurrency());
                    input.setAgreementPriceMark(countryAgreementPricePO.getAgreementMark());
                    input.setCostPriceMark(countryAgreementPricePO.getCostMark());
                }
            }
        }
        // 预估跨境退税金额、国家跨境入仓价
        CountryExtendPriceReqVO extendReqVO = new CountryExtendPriceReqVO();
        extendReqVO.setSkuIds(skuIds);
        extendReqVO.setTargetCountryCode(countryMkuExportVOList.get(0).getTargetCountryCode());
        List<CountryExtendPricePO> countryExtendPricePOList = countryExtendPriceAtomicService.getPricePoList(extendReqVO);

        if(CollectionUtils.isNotEmpty(countryExtendPricePOList)){
            Map<Long, List<CountryExtendPricePO>> skuExtendPriceMap = countryExtendPricePOList.stream()
                .collect(Collectors.groupingBy(CountryExtendPricePO::getSkuId));
            for(CountryMkuExportVO input: countryMkuExportVOList) {
                if(skuExtendPriceMap.containsKey(input.getSkuId())) {
                    List<CountryExtendPricePO> countryExtendPricePOS = skuExtendPriceMap.get(input.getSkuId());
                    for (CountryExtendPricePO countryExtendPricePO : countryExtendPricePOS){
                        if(CountryExtendPriceEnum.WAREHOUSING_PRICE.getCode().equals(countryExtendPricePO.getBusinessType())){
                            input.setCountryWarehousingPrice(countryExtendPricePO.getPrice());
                            input.setWarehousingPriceMark(countryExtendPricePO.getPriceMark());
                        }
                        if(CountryExtendPriceEnum.REFUND_TAX_PRICE.getCode().equals(countryExtendPricePO.getBusinessType())){
                            DataResponse<BigDecimal> exchangeRateResponse = exchangeRateManageService.getExchangeRateByCurrency( CurrencyConstant.CURRENCY_ZH, input.getCurrency());
                            BigDecimal exchangeRate = exchangeRateResponse.getData();
                            if(Objects.nonNull(countryExtendPricePO.getPrice()) && Objects.nonNull(exchangeRate)){
                                BigDecimal refundTaxPrice = countryExtendPricePO.getPrice().multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
                                input.setRefundTaxPrice(refundTaxPrice);
                                String refundTaxPriceMsg = String.format("预估跨境退税金额%s=预估跨境退税金额%s*汇率%s;\n",refundTaxPrice.stripTrailingZeros().toPlainString()
                                    ,countryExtendPricePO.getPrice().stripTrailingZeros().toPlainString()
                                    ,exchangeRate.stripTrailingZeros().toPlainString());
                                String priceMark = refundTaxPriceMsg + countryExtendPricePO.getPriceMark();
                                input.setRefundTaxPriceMark(priceMark);
                            }
                        }
                        if(CountryExtendPriceEnum.JD_PRICE.getCode().equals(countryExtendPricePO.getBusinessType())){
                            input.setJdPrice(countryExtendPricePO.getPrice());
                            input.setJdPriceMark(countryExtendPricePO.getPriceMark());
                        }
                    }
                }
            }
        }
    }

    private void setExportCountryMkuInfo(List<CountryMkuExportVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return ;
        }
        for (CountryMkuExportVO countryMkuExportVO : inputs){
            countryMkuExportVO.setPoolStatusStr(CountryMkuPoolStatusEnum.countryEnumByCode(countryMkuExportVO.getPoolStatus()).getDesc());
            if(CountryMkuPoolStatusEnum.POOL.getCode().equals(countryMkuExportVO.getPoolStatus())){
                countryMkuExportVO.setBlackReason(CountryMkuWarnReasonEnum.DRAFT.getDesc());
                String warnReasonStr = this.getWarnReasonStr(countryMkuExportVO);
                countryMkuExportVO.setWarnReason(warnReasonStr);
            }else if(CountryMkuPoolStatusEnum.BLACK.getCode().equals(countryMkuExportVO.getPoolStatus())){
                countryMkuExportVO.setBlackReason(CountryMkuPoolStatusEnum.BLACK.getDesc());
                countryMkuExportVO.setWarnReason(CountryMkuWarnReasonEnum.DRAFT.getDesc());
            }else {
                String warnReasonStr = this.getWarnReasonStr(countryMkuExportVO);
                countryMkuExportVO.setBlackReason(warnReasonStr);
                countryMkuExportVO.setWarnReason(CountryMkuWarnReasonEnum.DRAFT.getDesc());
            }
        }
    }

    private String getWarnReasonStr(CountryMkuExportVO countryMkuExportVO){
        String warnReason = countryMkuExportVO.getWarnReason();
        if(StringUtils.isBlank(warnReason)){
            return CountryMkuWarnReasonEnum.DRAFT.getDesc();
        }
        List<String> reasonList = new ArrayList<>();
        String[] split = warnReason.split(Constant.COMMA);
        for (String s : split) {
            String desc = CountryMkuWarnReasonEnum.countryEnumByCode(Integer.valueOf(s)).getDesc();
            reasonList.add(desc);
        }
        String reasonStr = String.join(Constant.COMMA,reasonList);
        return reasonStr;
    }

    private void setPurchase(List<CountryMkuPageVO.Response> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return ;
        }
        String countryCode = inputs.get(0).getTargetCountryCode();
        Set<Long> skuIdSet = inputs.stream().map(CountryMkuPageVO.Response::getSkuId).collect(Collectors.toSet());
        Map<Long, Integer> skuIdPurchaseMap = skuFeatureManageService.querySkuPurchaseModel(skuIdSet, countryCode);
        for (CountryMkuPageVO.Response response : inputs){
            Integer purchase = skuIdPurchaseMap.get(response.getSkuId());
            response.setPurchase(purchase.equals(YesOrNoEnum.NO.getCode()) ? YesOrNoEnum.NO.getDesc() :  YesOrNoEnum.YES.getDesc());
            response.setPurchaseCode(purchase.equals(YesOrNoEnum.NO.getCode()) ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
        }
    }

    private List<CountryMkuExportVO> setExportSkuInfo(List<CountryMkuExportVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return inputs;
        }
        Set<Long> mkuIds = inputs.stream().map(CountryMkuExportVO::getMkuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(mkuIds)){
            log.error("CountryMkuManageServiceImpl.setExportSkuInfo mkuIds is null");
            return inputs;
        }
        List<MkuRelationPO> mkuRelationPOList = mkuRelationAtomicService.queryBindListByMkuIds(new ArrayList<>(mkuIds));
        if(CollectionUtils.isEmpty(mkuRelationPOList)){
            log.error("CountryMkuManageServiceImpl.setExportSkuInfo mkuRelationPOList is null");
            return inputs;
        }

        Set<Long> skuIds = mkuRelationPOList.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(skuIds)){
            log.error("CountryMkuManageServiceImpl.setExportSkuInfo skuIds is null");
            return inputs;
        }
        List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(skuIds);
        if(CollectionUtils.isEmpty(skuPOList)){
            log.error("CountryMkuManageServiceImpl.setExportSkuInfo skuPOList is null");
            return inputs;
        }
        Set<Long> spuIdSet = skuPOList.stream().map(SkuPO::getSpuId).collect(Collectors.toSet());
        Map<Long, SpuPO> spuMap = spuAtomicService.getSpuMap(spuIdSet);
        Map<Long,List<MkuRelationPO>> mkuSkuRelationGroup = mkuRelationPOList.stream().collect(Collectors.groupingBy(MkuRelationPO::getMkuId));
        Map<Long, SkuPO> skuPOMap = skuPOList.stream().collect(Collectors.toMap(SkuPO::getSkuId, Function.identity()));
        List<CountryMkuExportVO> newList = new ArrayList<>();
        for(CountryMkuExportVO item : inputs){
            List<MkuRelationPO> mkuRelationPOS = mkuSkuRelationGroup.get(item.getMkuId());
            if(CollectionUtils.isEmpty(mkuRelationPOS)){
                newList.add(item);
                continue;
            }
            for (MkuRelationPO mkuRelationPO : mkuRelationPOS){
                CountryMkuExportVO countryMkuExportVO = new CountryMkuExportVO();
                BeanUtil.copyProperties(item,countryMkuExportVO);
                SkuPO skuPO = skuPOMap.get(mkuRelationPO.getSkuId());
                countryMkuExportVO.setSkuId(skuPO.getSkuId());
                countryMkuExportVO.setJdSkuId(skuPO.getJdSkuId());
                SpuPO spuPO = spuMap.getOrDefault(skuPO.getSpuId(), new SpuPO());
                countryMkuExportVO.setSpecification(spuPO.getSpecification());
                countryMkuExportVO.setSaleUnitId(spuPO.getSaleUnit());
                newList.add(countryMkuExportVO);
            }
        }

        List<SkuPO> skuCNPOS = skuPOList.stream().filter(item-> CountryConstant.COUNTRY_ZH.equals(item.getSourceCountryCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(skuCNPOS)){
            List<Long> jdSkuIds = skuCNPOS.stream().map(SkuPO::getJdSkuId).collect(Collectors.toList());
            Map<Long, String> jdSkuStockMap = skuReadManageService.getJdSkuStockBySkuIds(jdSkuIds);
            if(MapUtils.isNotEmpty(jdSkuStockMap)){
                Set<Long> cnSkuIds = skuCNPOS.stream().map(SkuPO::getSkuId).collect(Collectors.toSet());
                for(CountryMkuExportVO item : newList){
                    String totalStock = "";
                    if(!cnSkuIds.contains(item.getSkuId())){
                        continue;
                    }
                    if(Objects.isNull(item.getJdSkuId())){
                        continue;
                    }
                    String cnStock = jdSkuStockMap.get(item.getJdSkuId());
                    if(StringUtils.isNotBlank(cnStock)){
                        totalStock = cnStock;
                    }
                    item.setStockNum(totalStock);
                }
            }
        }

        List<SkuPO> skuNoCNPOS = skuPOList.stream().filter(item-> !CountryConstant.COUNTRY_ZH.equals(item.getSourceCountryCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(skuNoCNPOS)){
            StockManageReqDTO stockManageReqDTO = getStockManageReqDTO(skuNoCNPOS.stream().map(SkuPO::getSkuId).collect(Collectors.toList()));
            Map<Long, StockResDTO> stockMap = stockManageService.getStock(stockManageReqDTO);
            Set<Long> cnNoSkuIds = skuNoCNPOS.stream().map(SkuPO::getSkuId).collect(Collectors.toSet());
            for(CountryMkuExportVO item : newList){
                if(!cnNoSkuIds.contains(item.getSkuId())){
                    continue;
                }
                StockResDTO stockResDTO = stockMap.get(item.getSkuId());
                if(stockResDTO != null && stockResDTO.getStock() != null){
                    item.setStockNum("有货");
                }else {
                    item.setStockNum("无货");
                }
                //item.setStockNum(String.valueOf(totalStock));
            }
        }
        return newList;
    }

    private void setExportHaveTag(List<CountryMkuExportVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        for (CountryMkuExportVO countryMkuExportVO : inputs){
            countryMkuExportVO.setHaveTag(YesOrNoEnum.NO.getDesc());
            if(CollectionUtils.isEmpty(countryMkuExportVO.getSpecialAttrTagVOList())){
                continue;
            }
            List<SpecialAttrTagVO> specialAttrTagVOList = countryMkuExportVO.getSpecialAttrTagVOList();
            if(CollectionUtils.isEmpty(specialAttrTagVOList)){
                continue;
            }
            for (SpecialAttrTagVO specialAttrTagVO : specialAttrTagVOList){
                if(Objects.isNull(specialAttrTagVO.getAttributeKeyId())){
                    continue;
                }
                if(tagStr.contains(String.valueOf(specialAttrTagVO.getAttributeKeyId()))){
                    countryMkuExportVO.setHaveTag(YesOrNoEnum.YES.getDesc());
                }
                if(Objects.equals(specialAttrTagVO.getAttributeKeyId(), TEST_PRODUCT)){
                    countryMkuExportVO.setTestTag(true);
                }
            }
        }
    }

    /**
     * 设置MKU标题
     * @param inputs CountryMkuExportVO列表，包含要设置标题的MKU信息
     */
    private void setExportMkuTitle(List<CountryMkuExportVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        List<Long> mkuIds = inputs.stream().map(CountryMkuExportVO::getMkuId).collect(Collectors.toList());
        Map<Long, MkuLangPO> mkuTitleZhMap = new HashMap<>();
        Map<Long, MkuLangPO> mkuTitleEnMap = new HashMap<>();
        Map<Long, MkuLangPO> mkuTitleBrMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(mkuIds)){
            mkuTitleZhMap.putAll(mkuLangAtomicService.getMkuLangNameByMkuIds(mkuIds,LangContextHolder.get()));
            mkuTitleEnMap.putAll(mkuLangAtomicService.getMkuLangNameByMkuIds(mkuIds, LangConstant.LANG_EN));
            mkuTitleBrMap.putAll(mkuLangAtomicService.getMkuLangNameByMkuIds(mkuIds, LangConstant.LANG_BR));
        }

        inputs.forEach(item->{
            MkuLangPO mkuLangPOZh = mkuTitleZhMap.get(item.getMkuId());
            MkuLangPO mkuLangPOEn = mkuTitleEnMap.get(item.getMkuId());
            MkuLangPO mkuLangPOBr = mkuTitleBrMap.get(item.getMkuId());
            if(Objects.nonNull(mkuLangPOZh)){
                item.setMkuTitleZh(mkuLangPOZh.getMkuTitle());
            }
            if(Objects.nonNull(mkuLangPOEn)){
                item.setMkuTitleEn(mkuLangPOEn.getMkuTitle());
            }
            if(Objects.nonNull(mkuLangPOBr)){
                item.setMkuTitleBr(mkuLangPOBr.getMkuTitle());
                item.setMkuBrSubtitle(mkuLangPOBr.getMkuSubtitle());
            }
        });
    }

    /**
     * 根据品牌ID列表查询品牌名称并设置到输入列表中。
     * @param inputs 包含品牌ID的 CountryMkuPageVO.Response 列表
     */
    private void setExportBrandName(List<CountryMkuExportVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Set<Long> brandIds = inputs.stream().map(CountryMkuExportVO::getBrandId).collect(Collectors.toSet());
        Map<Long, String> brandNameZhMap = new HashMap<>();
        Map<Long, String> brandNameEnMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(brandIds)){
            brandNameZhMap.putAll(brandOutService.queryNameByIds(brandIds,LangContextHolder.get()));
            brandNameEnMap.putAll(brandOutService.queryNameByIds(brandIds,LangConstant.LANG_EN));
        }

        inputs.forEach(item->{
            item.setBrandNameZh(brandNameZhMap.get(item.getBrandId()));
            item.setBrandNameEn(brandNameEnMap.get(item.getBrandId()));
        });
    }

    /**
     * 设置每个 CountryMkuPageVO.Response 对象的第一、第二、第三和最后一个分类名称。
     * @param inputs 要处理的 CountryMkuPageVO.Response 对象列表。
     */
    private void setExportCatIdAndName(List<CountryMkuExportVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Set<Long> lastCatIds = inputs.stream().map(CountryMkuExportVO::getLastCatId).collect(Collectors.toSet());
        Map<Long, List<CategoryComboBoxVO>> lastCatZhMap = new HashMap<>();
        Map<Long, List<CategoryComboBoxVO>> lastCatEnMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(lastCatIds)){
            lastCatZhMap.putAll(categoryOutService.queryPath(lastCatIds, LangContextHolder.get()));
            lastCatEnMap.putAll(categoryOutService.queryPath(lastCatIds, LangConstant.LANG_EN));
        }

        inputs.forEach(item->{
            List<CategoryComboBoxVO> categoryComboBoxVOListZh = lastCatZhMap.get(item.getLastCatId());
            if(CollectionUtils.isNotEmpty(categoryComboBoxVOListZh)){
                if(categoryComboBoxVOListZh.size() > 3){
                    item.setFirstCatNameZh(categoryComboBoxVOListZh.get(0).getName());
                    item.setSecondCatNameZh(categoryComboBoxVOListZh.get(1).getName());
                    item.setThirdCatNameZh(categoryComboBoxVOListZh.get(2).getName());
                    item.setLastCatNameZh(categoryComboBoxVOListZh.get(3).getName());
                }else if(categoryComboBoxVOListZh.size() > 2){
                    item.setFirstCatNameZh(categoryComboBoxVOListZh.get(0).getName());
                    item.setSecondCatNameZh(categoryComboBoxVOListZh.get(1).getName());
                    item.setThirdCatNameZh(categoryComboBoxVOListZh.get(2).getName());
                }else if(categoryComboBoxVOListZh.size() > 1){
                    item.setFirstCatNameZh(categoryComboBoxVOListZh.get(0).getName());
                    item.setSecondCatNameZh(categoryComboBoxVOListZh.get(1).getName());
                }else {
                    item.setFirstCatNameZh(categoryComboBoxVOListZh.get(0).getName());
                }
            }

            List<CategoryComboBoxVO> categoryComboBoxVOListEn = lastCatEnMap.get(item.getLastCatId());
            if(CollectionUtils.isNotEmpty(categoryComboBoxVOListEn)){
                if(categoryComboBoxVOListEn.size() > 3){
                    item.setFirstCatNameEn(categoryComboBoxVOListEn.get(0).getName());
                    item.setSecondCatNameEn(categoryComboBoxVOListEn.get(1).getName());
                    item.setThirdCatNameEn(categoryComboBoxVOListEn.get(2).getName());
                    item.setLastCatNameEn(categoryComboBoxVOListEn.get(3).getName());
                }else if(categoryComboBoxVOListEn.size() > 2){
                    item.setFirstCatNameEn(categoryComboBoxVOListEn.get(0).getName());
                    item.setSecondCatNameEn(categoryComboBoxVOListEn.get(1).getName());
                    item.setThirdCatNameEn(categoryComboBoxVOListEn.get(2).getName());
                }else if(categoryComboBoxVOListEn.size() > 1){
                    item.setFirstCatNameEn(categoryComboBoxVOListEn.get(0).getName());
                    item.setSecondCatNameEn(categoryComboBoxVOListEn.get(1).getName());
                }else {
                    item.setFirstCatNameEn(categoryComboBoxVOListEn.get(0).getName());
                }
            }
        });
    }


    /**
     * 根据国家代码和警告状态检查并返回符合条件的MKU列表。
     * @param countryCode 国家代码
     * @param warnStatus 警告状态
     * @return DataResponse对象，包含一个布尔值表示操作是否成功
     */
    @Override
    public DataResponse<String> ruleCheckCountryMku(String countryCode, Integer warnStatus) {
        if(StringUtils.isBlank(countryCode) || Objects.isNull(warnStatus)){
            log.error("CountryMkuManageServiceImpl.ruleCheckCountryMku countryCode:{},warnStatus:{}",countryCode,warnStatus);
            return DataResponse.success("传的参数为空");
        }
        List<CountryMkuPO> countryMkuPOList = countryMkuAtomicService.getPoByCountryAndPool(countryCode, warnStatus);
        if(CollectionUtils.isEmpty(countryMkuPOList)){
            return DataResponse.success("查询的数据为空");
        }
        List<Long> mkuIdList = countryMkuPOList.stream().map(CountryMkuPO::getMkuId).collect(Collectors.toList());
        CountryMkuReqVO countryMkuReqVO = new CountryMkuReqVO();
        countryMkuReqVO.setMkuIdList(mkuIdList);
        countryMkuReqVO.setCountryCode(countryCode);
        DataResponse<Boolean> response = mkuPoolJoinCountryPool(countryMkuReqVO);
        return DataResponse.success("成功");
    }

    private void setExportBrazilInfo(String countryCode,List<CountryMkuExportVO> inputs){
        if(!StringUtils.equals(CountryConstant.COUNTRY_BR,countryCode)){
            log.error("CountryMkuManageServiceImpl.setExportBrazilInfo countryCode:{}",countryCode);
            return;
        }
        if(CollectionUtils.isEmpty(inputs)){
            log.error("CountryMkuManageServiceImpl.setExportBrazilInfo inputs is null");
            return;
        }
        Set<Long> skuIds = inputs.stream().map(CountryMkuExportVO::getSkuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(skuIds)){
            log.error("CountryMkuManageServiceImpl.setExportBrazilInfo skuIds is null");
            return;
        }
        Map<Long,Long> skuPOMap = skuAtomicService.getSkuSpuMapBySkuIds(skuIds);
        Set<Long> spuIds = Sets.newHashSet(skuPOMap.values());
        if(CollectionUtils.isEmpty(spuIds)){
            log.error("CountryMkuManageServiceImpl.setExportBrazilInfo spuIds is null");
            return;
        }
        Map<Long,SpuPO> spuPOMap = spuAtomicService.getSpuStatusBySpuIds(spuIds);
        Set<Long> brandIds = spuPOMap.values().stream().map(SpuPO::getBrandId).collect(Collectors.toSet());
        Map<Long, String> brandNameBrMap = brandOutService.queryNameByIds(brandIds,LangConstant.LANG_BR);
        Map<Long, CustomerSkuTaxResVO> customerSkuTaxMap = new HashMap<>();
        try{
            DataResponse<Map<Long, CustomerSkuTaxResVO>> customerSkuTaxResp = customerTaxManageService.batchQueryBrCustomerSkuTax(skuIds);
            if(customerSkuTaxResp.getSuccess()){
                customerSkuTaxMap = customerSkuTaxResp.getData();
            }
        }catch (Exception e){
            log.error("CountryMkuManageServiceImpl.setExportBrazilInfo customerTaxManageService.batchQueryBrCustomerSkuTax error",e);
        }

        for(CountryMkuExportVO item : inputs){
            Long skuId = item.getSkuId();
            Long spuId = skuPOMap.get(skuId);
            SpuPO spuPO = spuPOMap.get(spuId);
            item.setBrandNameBr(brandNameBrMap.get(spuPO.getBrandId()));
            item.setAuditStatus(spuPO.getAuditStatus());
            item.setTaxAuditStatus(spuPO.getTaxAuditStatus());
            CustomerSkuTaxResVO taxResVO = customerSkuTaxMap.get(skuId);
            if(taxResVO != null){
                if(taxResVO.getCst() != null){
                    item.setSaleCst(taxResVO.getCst());
                }
                if(taxResVO.getNcm() != null){
                    item.setNcmCode(taxResVO.getNcm());
                }
            }
        }
    }

}
