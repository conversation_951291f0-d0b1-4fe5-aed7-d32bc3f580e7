package com.jdi.isc.product.soa.service.manage.spu;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.enums.CacheLocationEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.PoolVerificationEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.util.NumberUtil;
import com.jdi.isc.product.soa.common.util.S3Utils;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeFlatVO;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailDraftPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDraftPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPricePO;
import com.jdi.isc.product.soa.domain.ducc.PreselectionVO;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.domain.enums.spu.DeleteVerificationEnum;
import com.jdi.isc.product.soa.domain.mku.biz.MkuPoolVerificationReqVO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuPoolVerificationResVO;
import com.jdi.isc.product.soa.domain.mku.po.MkuLangPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuFeaturePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuLangPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.stock.vo.SkuStockReqVO;
import com.jdi.isc.product.soa.domain.stock.vo.SkuStockVO;
import com.jdi.isc.product.soa.domain.task.dto.BaseWorkerDTO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseDetailVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseLangVO;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuDescLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuBizAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuFeatureAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.*;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseAtomicService;
import com.jdi.isc.product.soa.service.manage.attribute.AttributeOutService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceWarningManageService;
import com.jdi.isc.product.soa.service.manage.material.MkuMaterialManageService;
import com.jdi.isc.product.soa.service.manage.price.FulfillmentRateManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceWarningManageService;
import com.jdi.isc.product.soa.service.manage.price.markupRate.MarkupRateManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuWriteManageService;
import com.jdi.isc.product.soa.service.manage.specialAttr.SpecialAttrManageService;
import com.jdi.isc.product.soa.service.manage.stock.SkuStockManageService;
import com.jdi.isc.product.soa.service.manage.stock.StockManageService;
import com.jdi.isc.product.soa.service.manage.stock.impl.StockManageServiceImpl;
import com.jdi.isc.product.soa.service.manage.taxRate.CategoryTaxManageService;
import com.jdi.isc.product.soa.service.manage.warehouse.WarehouseManageService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.customerSku.CustomerSkuPriceConvert;
import com.jdi.isc.product.soa.service.mapstruct.sku.SkuConvert;
import com.jdi.isc.product.soa.service.support.TextTranslateService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/3/6
 **/
@Slf4j
@Service
public class ProductToolService {

    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private MkuAtomicService mkuAtomicService;

    @Resource
    private SpuDescLangAtomicService spuDescLangAtomicService;

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @Resource
    private MkuDescLangAtomicService mkuDescLangAtomicService;

    @Resource
    private SpuDescriptionReadManageService spuDescriptionReadManageService;

    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;

    @Resource
    private SkuFeatureAtomicService skuFeatureAtomicService;

    @Resource
    private SkuBizAtomicService skuBizAtomicService;

    @Resource
    private S3Utils s3Utils;

    @Resource
    private TextTranslateService baiduTranslateService;

    @Resource
    private CustomerMkuPriceAtomicService customerMkuPriceAtomicService;

    @Resource
    private MkuLangAtomicService mkuLangAtomicService;

    @Resource
    private SpuLangAtomicService spuLangAtomicService;

    @Resource
    private SpuConvertService spuConvertService;

    @Resource
    private AttributeOutService attributeOutService;

    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;

    @Resource
    private CountryMkuAtomicService countryMkuAtomicService;

    @Resource
    private SpuDescriptionManageService spuDescriptionManageService;

    @Resource
    private CustomerSkuPriceDraftAtomicService customerSkuPriceDraftAtomicService;
    @Resource
    private CustomerSkuPriceAtomicService customerSkuPriceAtomicService;
    @Resource
    private CustomerSkuPriceDetailDraftAtomicService customerSkuPriceDetailDraftAtomicService;
    @Resource
    private CustomerSkuPriceDetailAtomicService customerSkuPriceDetailAtomicService;
    @Resource
    private SkuDraftAtomicService skuDraftAtomicService;

    @Resource
    private MarkupRateManageService markupRateManageService;

    @Resource
    private FulfillmentRateManageService fulfillmentRateManageService;

    @Resource
    private SpecialAttrManageService specialAttrManageService;

    @Resource
    private MkuMaterialManageService mkuMaterialManageService;

    @Resource
    private CountryAgreementPriceManageService countryAgreementPriceManageService;

    @Resource
    private CategoryTaxManageService categoryTaxManageService;

    @Resource
    private SkuStockManageService skuStockManageService;

    @Resource
    private StockManageService stockManageService;

    @Resource
    private SkuWriteManageService skuWriteManageService;

    @Resource
    private CustomerSkuPriceWarningManageService customerSkuPriceWarningManageService;

    @Resource
    private CountryAgreementPriceWarningManageService countryAgreementPriceWarningManageService;

    @Resource
    private OperDuccConfig operDuccConfig;

    @Resource
    private SpuDeleteAtomicService spuDeleteAtomicService;

    // TODO 勿动，早晚有一天，我把这个归档优化了，by wang 20250708
    /*@Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<String> updateSpuYn(Long spuId, Integer yn, String lang) {
        SpuDeleteStatusReqVO spuDeleteStatusReqVO = new SpuDeleteStatusReqVO();
        spuDeleteStatusReqVO.setEnumCode(1);
        return null;
    }

    private SpuDeleteStatusVO verification(SpuDeleteStatusVO spuDeleteStatusVO ,SpuDeleteStatusReqVO spuDeleteStatusReqVO){
        DeleteVerificationEnum deleteVerificationEnum = DeleteVerificationEnum.getEnumByCode(spuDeleteStatusReqVO.getEnumCode());
        List<Map<String,Object>> dataList = spuDeleteAtomicService.searchData(deleteVerificationEnum.getTableName(), deleteVerificationEnum.getFieldName(), spuDeleteStatusReqVO.getIdMap().get(deleteVerificationEnum.getFieldName()));
        deleteVerificationEnum.check(spuDeleteStatusVO, dataList,spuDeleteStatusReqVO);
        if(spuDeleteStatusVO.getStatus().equals(1) && !DeleteVerificationEnum.SKU_INFO.getCode().equals(spuDeleteStatusReqVO.getEnumCode())){
            spuDeleteStatusReqVO.setEnumCode(DeleteVerificationEnum.getNextCode(spuDeleteStatusReqVO.getEnumCode()));
            return verification(spuDeleteStatusVO,spuDeleteStatusReqVO);
        }
        return spuDeleteStatusVO;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        SpuDeleteStatusReqVO spuDeleteStatusReqVO = new SpuDeleteStatusReqVO();
        Map<String,Long> idMap = new HashMap<>();
        idMap.put(DeleteVerificationEnum.SPU_INFO.getFieldName(), 20000000930L);
        spuDeleteStatusReqVO.setIdMap(idMap);
        spuDeleteStatusReqVO.setEnumCode(1);
        SpuDeleteStatusVO spuDeleteStatusVO = new SpuDeleteStatusVO();
        spuDeleteStatusVO.setStatus(1);
        verification(spuDeleteStatusVO,spuDeleteStatusReqVO);
    }*/

    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<String> updateSpuYn(Set<Long> spuIds, Integer yn, String lang) {
        Preconditions.checkArgument(yn != null && yn == 0, "yn is error， please set yn = 0");
        try {
            String msg = this.checkCustomerMku(spuIds,yn);
            if(StringUtils.isNotBlank(msg)){
                return DataResponse.error(msg);
            }

            // 校验此商品是否已备货，已备货且有备货库存的商品不允许归档，报错：此商品在xxxx（备货仓名）有库存，不允许归档，请先消化库存。‘
            msg = this.checkWarehouseStock(spuIds, lang);
            if(StringUtils.isNotBlank(msg)){
                return DataResponse.error(msg);
            }

            String pin = "";
            LoginContextHolder loginContextHolder = LoginContextHolder.getLoginContextHolder();
            if(loginContextHolder != null && StringUtils.isNotBlank(loginContextHolder.getPin())){
                pin = LoginContextHolder.getLoginContextHolder().getPin();
            } else {
                pin =  Constant.PIN_SYSTEM;
            }

            String finalPin = pin;
            List<SpuPO> spuPoList = spuIds.stream().map(id -> this.getSpuPo(id, yn, finalPin)).collect(Collectors.toList());
            AtomicLong atomicLong = new AtomicLong(0);
            for (SpuPO po : spuPoList) {
                atomicLong.addAndGet(1);
                Long spuId = po.getSpuId();
                // 1、spu yn = 0
                if(spuAtomicService.getSpuPoBySpuId(spuId) != null){
                    spuAtomicService.saveOrUpdate(po);
                    log.info("ProductToolService.updateSpuYn  修改spu状态为{},处理完成", yn);
                }

                // 2、spu 草稿 yn = 0
                if (YnEnum.NO.getCode().equals(yn)){
                    // 逻辑删除
                    SpuDraftPO spuDraftPO = spuDraftAtomicService.getBySpuId(spuId);
                    if (Objects.nonNull(spuDraftPO)){
                        spuDraftPO.setUpdater(finalPin);
                        spuDraftPO.setUpdateTime(new Date());
                        spuDraftPO.setYn(yn);
                        spuDraftPO.setSpuId(null);
                        spuDraftAtomicService.updateById(spuDraftPO);
                        log.info("ProductToolService.updateSpuYn  修改草稿状态为{},处理完成", yn);
                    }
                }

                LambdaQueryWrapper<SkuPO> skuPOLambdaQueryWrapper = Wrappers.lambdaQuery(SkuPO.class).select(SkuPO::getSkuId, SkuPO::getYn).eq(SkuPO::getSpuId, spuId)
                        .eq(SkuPO::getYn, YnEnum.YES.getCode());
                List<SkuPO> skuPOList = skuAtomicService.list(skuPOLambdaQueryWrapper);
                if(CollectionUtils.isEmpty(skuPOList)){
                    log.info("ProductToolService.updateSpuYn 修改spuId={} skuPOList is null", spuId);
                    continue;
                }
                skuPOList.forEach(skuPO -> {
                    skuPO.setYn(yn);
                    skuPO.setSpuId(null);
                    skuPO.setUpdater(finalPin);
                    skuPO.setUpdateTime(new Date());
                });
                // 3. sku yn = 0
                if(CollectionUtils.isNotEmpty(skuPOList)){
                    skuAtomicService.saveOrUpdateBatch(skuPOList);
                    log.info("ProductToolService.updateSpuYn 修改spuId={}下所有sku的yn状态为{} atomicLong={}", spuId, yn, atomicLong.get());
                }

                // sku草稿 jdi_isc_sku_draft_sharding yn = 0
                skuWriteManageService.updateDraftYn(spuId, finalPin);

                List<Long> skuIds = skuPOList.stream().map(SkuPO::getSkuId).collect(Collectors.toList());

                // 4. 客户sku 客制化价格-草稿 yn = 0
                List<CustomerSkuPriceDraftPO> customerSkuPriceDraftPOS = customerSkuPriceDraftAtomicService.queryListBySkuId(skuIds);
                if(CollectionUtils.isNotEmpty(customerSkuPriceDraftPOS)){
                    customerSkuPriceDraftPOS.forEach(customerSkuPriceDraftPO -> {
                        customerSkuPriceDraftPO.setYn(yn);
                        customerSkuPriceDraftPO.setSkuId(null);
                        customerSkuPriceDraftPO.setUpdater(finalPin);
                        customerSkuPriceDraftPO.setUpdateTime(new Date());
                    });
                    customerSkuPriceDraftAtomicService.saveOrUpdateBatch(customerSkuPriceDraftPOS);
                    log.info("ProductToolService.updateSpuYn 修改spuId={}下所有sku的yn状态为{} customerSkuPriceDraftPOS={}", spuId, yn, JSONObject.toJSONString(customerSkuPriceDraftPOS));
                }

                // 5. 客户sku 客制化价格草稿明细 yn = 0
                List<CustomerSkuPriceDetailDraftPO> customerSkuPriceDetailDraftPOS = customerSkuPriceDetailDraftAtomicService.queryListBySkuId(skuIds);
                if(CollectionUtils.isNotEmpty(customerSkuPriceDraftPOS)){
                    customerSkuPriceDetailDraftPOS.forEach(customerSkuPriceDetailDraftPO -> {
                        customerSkuPriceDetailDraftPO.setYn(yn);
                        customerSkuPriceDetailDraftPO.setSkuId(null);
                        customerSkuPriceDetailDraftPO.setUpdater(finalPin);
                        customerSkuPriceDetailDraftPO.setUpdateTime(new Date().getTime());
                    });
                    customerSkuPriceDetailDraftAtomicService.saveOrUpdateBatch(customerSkuPriceDetailDraftPOS);
                    log.info("ProductToolService.updateSpuYn 修改spuId={}下所有sku的yn状态为{} customerSkuPriceDetailDraftPOS={}", spuId, yn, JSONObject.toJSONString(customerSkuPriceDetailDraftPOS));
                }

                // 6. 客户sku 定制化价格 yn = 0
                List<CustomerSkuPricePO> customerSkuPricePOS = customerSkuPriceAtomicService.listBySkuIds(skuIds);
                if(CollectionUtils.isNotEmpty(customerSkuPricePOS)){
                    customerSkuPricePOS.forEach(customerSkuPricePO -> {
                        customerSkuPricePO.setYn(yn);
                        customerSkuPricePO.setSkuId(null);
                        customerSkuPricePO.setUpdater(finalPin);
                        customerSkuPricePO.setUpdateTime(new Date());
                    });
                    customerSkuPriceAtomicService.saveOrUpdateBatch(customerSkuPricePOS);
                    log.info("ProductToolService.updateSpuYn 修改spuId={}下所有sku的yn状态为{} customerSkuPricePOS={}", spuId, yn, JSONObject.toJSONString(customerSkuPricePOS));
                }

                // 7. 客户sku 定制化价格明细 yn = 0
                List<CustomerSkuPriceDetailPO> customerSkuPriceDetailPOS = customerSkuPriceDetailAtomicService.listBySkuIds(skuIds);
                if(CollectionUtils.isNotEmpty(customerSkuPriceDetailPOS)){
                    customerSkuPriceDetailPOS.forEach(customerSkuPriceDetailPO -> {
                        customerSkuPriceDetailPO.setYn(yn);
                        customerSkuPriceDetailPO.setSkuId(null);
                        customerSkuPriceDetailPO.setUpdater(finalPin);
                        customerSkuPriceDetailPO.setUpdateTime(new Date().getTime());
                    });
                    customerSkuPriceDetailAtomicService.saveOrUpdateBatch(customerSkuPriceDetailPOS);
                    log.info("ProductToolService.updateSpuYn 修改spuId={}下所有sku的yn状态为{} customerSkuPriceDetailPOS={}", spuId, yn, JSONObject.toJSONString(customerSkuPriceDetailPOS));
                }

                LambdaQueryWrapper<MkuRelationPO> queryWrapper = new LambdaQueryWrapper<MkuRelationPO>().in(MkuRelationPO::getSkuId, skuIds)
                        .eq(MkuRelationPO::getYn, 1);
                List<MkuRelationPO> mkuRelationPOList = mkuRelationAtomicService.list(queryWrapper);
                if(CollectionUtils.isEmpty(mkuRelationPOList)){
                    log.info("ProductToolService.updateSpuYn 修改spuId={} mkuRelationPOList is null", spuId);
                    continue;
                }

//                // 8. mku yn=0
//                List<Long> mkuIds = mkuRelationPOList.stream().map(MkuRelationPO::getMkuId).collect(Collectors.toList());
//                if(CollectionUtils.isEmpty(mkuIds)){
//                    log.info("ProductToolService.updateSpuYn 修改spuId={} mkuIds is null", spuId);
//                    continue;
//                }
//                // TODO 如果归档的mku下有其他sku，mku则不归档
//
//                List<MkuPO> mkuPoList = mkuIds.stream().map(id -> this.getMkuPo(id, yn, finalPin)).collect(Collectors.toList());
//                if(CollectionUtils.isNotEmpty(mkuPoList)){
//                    mkuAtomicService.saveOrUpdateBatch(mkuPoList);
//                    log.info("ProductToolService.updateSpuYn 修改spuId={}下所有mku的yn状态为{} atomicLong={}", spuId, yn, atomicLong.get());
//                }

                // 8. mku yn=0
                List<Long> mkuIds = mkuRelationPOList.stream().map(MkuRelationPO::getMkuId).filter(Objects::nonNull).collect(Collectors.toList());

                mkuRelationPOList.forEach(mkuRelationPO -> {
                    mkuRelationPO.setYn(yn);
                    mkuRelationPO.setMkuId(null);
                    mkuRelationPO.setUpdater(finalPin);
                    mkuRelationPO.setUpdateTime(new Date());
                });

                // 9. mku sku 关系 yn=0
                if(CollectionUtils.isNotEmpty(mkuRelationPOList)){
                    mkuRelationAtomicService.saveOrUpdateBatch(mkuRelationPOList);
                    log.info("ProductToolService.updateSpuYn 修改spuId={}下所有mku和sku关联信息的yn状态为{} atomicLong={}", spuId, yn, atomicLong.get());
                }

//                if(CollectionUtils.isEmpty(mkuIds)){
//                    log.info("ProductToolService.updateSpuYn 修改spuId={} mkuIds is null", spuId);
//                    continue;
//                }
                if (CollectionUtils.isNotEmpty(mkuIds)) {
                    // 如果归档的mku下有其他sku，mku则不归档
                    // 查询没有绑定关系的mkuId
                    LambdaQueryWrapper<MkuRelationPO> queryMkuRelationWrapper = new LambdaQueryWrapper<MkuRelationPO>().in(MkuRelationPO::getMkuId, mkuIds)
                            .eq(MkuRelationPO::getYn, 1);
                    List<MkuRelationPO> mkuRelationList = mkuRelationAtomicService.list(queryMkuRelationWrapper);
                    // TODO 需求暂停，待需求确认，https://joyspace.jd.com/h/personal/pages/OUJlPLFybCPqKeC6s72N

                    // 解除绑定关系后剩余的仍有绑定关系的mkuId，即除了有绑定关系的mkuId，其他mkuId都归档
                    List<Long> afterMkuIds = mkuRelationList.stream().map(MkuRelationPO::getMkuId).filter(Objects::nonNull).collect(Collectors.toList());

                    // 计算集合的单差集，即只返回【集合1】中有，但是【集合2】中没有的元素
                    mkuIds = CollUtil.subtractToList(mkuIds, afterMkuIds);

                    List<MkuPO> mkuPoList = mkuIds.stream().map(id -> this.getMkuPo(id, yn, finalPin)).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(mkuPoList)){
                        mkuAtomicService.saveOrUpdateBatch(mkuPoList);
                        log.info("ProductToolService.updateSpuYn 修改spuId={}下所有mku的yn状态为{} atomicLong={}", spuId, yn, atomicLong.get());
                    }

                    List<CountryMkuPO> countryMkuPOS = countryMkuAtomicService.getByMkuIds(mkuIds);
//                    if(CollectionUtils.isEmpty(countryMkuPOS)){
//                        log.info("ProductToolService.updateSpuYn 修改spuId={} countryMkuPOS is null", spuId);
//                        continue;
//                    }

                    if (CollectionUtils.isNotEmpty(countryMkuPOS)) {
                        countryMkuPOS.forEach(countryMkuPO -> {
                            countryMkuPO.setYn(yn);
                            countryMkuPO.setMkuId(null);
                            countryMkuPO.setUpdater(finalPin);
                            countryMkuPO.setUpdateTime(new Date().getTime());
                        });
                        // 10. 国家池 yn=0
                        if(CollectionUtils.isNotEmpty(countryMkuPOS)){
                            countryMkuAtomicService.saveOrUpdateBatch(countryMkuPOS);
                            log.info("ProductToolService.updateSpuYn 修改spuId={}下所有countryMku关联信息的yn状态为{} atomicLong={}", spuId, yn, atomicLong.get());
                        }
                    }

                    List<CustomerMkuPO> customerMkuPOList = customerMkuAtomicService.listCustomerMkuPOByMkuIds(mkuIds,null);
                    if(CollectionUtils.isNotEmpty(customerMkuPOList)){
                        customerMkuPOList.forEach( customerMkuPO -> {
                            customerMkuPO.setYn(YnEnum.NO.getCode());
                            customerMkuPO.setUpdater(finalPin);
                            customerMkuPO.setUpdateTime(new Date());
                            customerMkuPO.setMkuId(null);
                        });
                        customerMkuAtomicService.updateBatchById(customerMkuPOList);
                        log.info("ProductToolService.updateSpuYn 修改spuId={},customerMkuPOList{}", spuId,JSON.toJSONString(customerMkuPOList));
                    }

                }

                /*

                // 11. 国家协议加价率 jdi_isc_markup_rate_sharding，通过skuId
                markupRateManageService.updateYn(skuIds, finalPin);

                // 12. 国家协议价 jdi_isc_country_agreement_price_sharding, skuId, 是否需要判断国家
                countryAgreementPriceManageService.updateYn(skuIds, finalPin);

                // 13. 类目税率表 jdi_isc_category_tax，通过skuId归档 http://wimp-pre.jd.com/#/taxmanage/productTaxRate 越南本地税率，通过skuId归档

                categoryTaxManageService.updateYn(skuIds, finalPin);

                // 14. 此商品在履约费率表中的信息，一并归档 http://wimp-pre.jd.com/#/product/priceManage/rateList
                // 履约费率表，jdi_isc_fulfillment_rate，通过skuId归档
                fulfillmentRateManageService.updateYn(skuIds, finalPin);

                // 15. 商品特殊属性表中的信息，一并归档 http://wimp-pre.jd.com/#/product/productspecialattrmanage
                // 15. 商品特殊属性表 jdi_isc_product_special_attr_relation_sharding 三张表，只清理关系表
                specialAttrManageService.unbindBySpuId(spuId, finalPin);

                // 16. 物料编码信息，一并归档 http://wimp-pre.jd.com/#/customoperate/materialcode/list

                // jdi_isc_mku_material_sharding 通过mku，st_commodity_materials_relation 线上没有这个表
                mkuMaterialManageService.updateYn(mkuIds, finalPin);

                // 17. SKU客制化价格预警 http://wimp-api-pre.jd.com/oper/api/customer/sku/price/warningPage
                // jdi_isc_customer_sku_price_warning_sharding
                customerSkuPriceWarningManageService.updateYn(skuIds, finalPin);

                // 18. 国家协议价格预警 http://wimp-api-pre.jd.com/oper/api/agreementPrice/countryAgreementPrice/warningPage
                // jdi_isc_country_agreement_price_warning_sharding
                countryAgreementPriceWarningManageService.updateYn(skuIds, finalPin);
                */
                countryAgreementPriceWarningManageService.updateYn(skuIds, finalPin);
                customerSkuPriceWarningManageService.updateYn(skuIds, finalPin);
            }
        } catch (Exception e) {
            log.error("【系统异常】ProductToolService.updateSpuYn 发生异常 spuIds={},yn={}", JSON.toJSONString(spuIds), yn, e);
        }
        return DataResponse.success();
    }

    @Resource
    private WarehouseAtomicService warehouseAtomicService;

    /**
     * 校验非厂直仓库库存
     */
    public String checkWarehouseStock(Set<Long> spuIds, String lang) {
        log.info("checkWarehouseStock，校验库存. spuIds={}, lang={}", spuIds, lang);
        StringBuilder errorMsg = new StringBuilder();

        if (CollectionUtils.isEmpty(spuIds)) {
            return errorMsg.toString();
        }

        List<SkuPO> skuPOList = skuAtomicService.getSkuListBySpuIds(spuIds);

        Set<Long> skuIds = skuPOList.stream().map(SkuPO::getSkuId).collect(Collectors.toSet());

        SkuStockReqVO query = new SkuStockReqVO();
        query.setSkuIds(skuIds);

        // 查询sku库存, 过滤掉厂直库存
        Map<Long, Map<String, SkuStockVO>> warehouseStockMap = skuStockManageService.listSkuWarehouseStock(query);
        List<SkuStockVO> skuStocks = warehouseStockMap.values().stream()
                .filter(MapUtils::isNotEmpty).map(Map::values)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(item ->!String.valueOf(Constant.FACTORY_DEFAULT_ID).equals(item.getWarehouseId()))
                .collect(Collectors.toList());

        log.info("checkWarehouseStock, 查询库存. query={}, skuStocks={}", query, skuStocks);

        Map<String, Set<Long>> warehouseSkuMap = Maps.newHashMap();

        for (SkuStockVO item : skuStocks) {
            String warehouseId = item.getWarehouseId();
            Long skuId = item.getSkuId();

            if (StringUtils.isEmpty(warehouseId) || !StringUtils.isNumeric(warehouseId) || skuId == null) {
                log.warn("【系统异常】ProductToolService.checkWarehouseStock 仓库id为空或非数字 warehouseId={}, skuId={}", warehouseId, skuId);
                continue;
            }

            // 现货库存
            Long stockQty = item.getStock();

            // 预占库存数量
            Long occupyQty = item.getOccupy();

            // 在途库存
            Long onWayQty = item.getOnWayStock();

            if (NumberUtil.gt0(stockQty)
                    || NumberUtil.gt0(occupyQty)
                    || NumberUtil.gt0(onWayQty)
            ) {
                Set<Long> skuIdList = warehouseSkuMap.get(warehouseId);
                if (CollectionUtils.isEmpty(skuIdList)) {
                    warehouseSkuMap.put(warehouseId, Sets.newHashSet(skuId));
                } else {
                    skuIdList.add(skuId);
                }
            }
        }

        warehouseSkuMap.forEach((k, v) -> {
            String warehouseName = this.getWarehouseNameId(k, lang, k);
            errorMsg.append(String.format("商品SKU:%s, 在[%s]有库存\n", v, warehouseName));
        });

        if (StringUtils.isNotEmpty(errorMsg.toString())) {
            errorMsg.append("不允许归档，请先消化库存");
        }

        log.info("checkWarehouseStock， 校验库存. spuIds={}, skuIds={}, errorMsg={}", spuIds, skuIds, errorMsg);
        return errorMsg.toString();
    }

    /**
     * 校验仓库库存（包含厂直和非厂直）
     */
    public String checkProductStock(Set<Long> spuIds, String lang) {

        StringBuilder errorMsg = new StringBuilder();

        StockManageReqDTO request = new StockManageReqDTO();

        Map<String/*countryCode*/, Set<Long>> countrySkuIdMap = Maps.newHashMap();

        Map<Long, SpuPO> spus = spuAtomicService.getSpuStatusBySpuIds(spuIds);

        // 1.查询spu下所有sku
        List<SkuPO> skuPOList = skuAtomicService.getSkuListBySpuIds(spuIds);
        if(CollectionUtils.isEmpty(skuPOList)){
            log.info("找不到sku信息，spuIds. {}", spuIds);
            return "sku is not exists";
        }
        Set<Long> dbSpuIds = skuPOList.stream().map(SkuPO::getSpuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(dbSpuIds) || dbSpuIds.size() != spuIds.size()){
            spuIds.removeAll(dbSpuIds);
            return "Error spu exists，spuIds."+ String.join(Constant.COMMA,spuIds.stream().map(String::valueOf).collect(Collectors.toSet()));
        }
        // 2.查询sku下所有mku
        Map<Long/*spuId*/, Set<Long/*skuId*/>> spuIdSkuIdMap = skuPOList.stream().collect(Collectors.groupingBy(SkuPO::getSpuId, Collectors.mapping(SkuPO::getSkuId, Collectors.toSet())));

        spus.forEach((k, v) -> {
            // 属性范围, 判断是否有巴西
            String attributeScope = v.getAttributeScope();

            if (StringUtils.isBlank(attributeScope)) {
                log.warn("属性范围为空, spuId={}", k);
                errorMsg.append("spu attributeScope is error, spuId=").append(k).append(";");
                return;
            }
            List<String> countryCodes = Splitter.on(",").splitToList(attributeScope);

            Set<Long> skuIds = spuIdSkuIdMap.get(k);

            if (CollectionUtils.isEmpty(skuIds)) {
                log.warn("找不到sku, spuId={}", k);
                errorMsg.append("Error spu exists, spuId=").append(k).append(";");
                return;
            }

            for (String countryCode : countryCodes) {
                Set<Long> skuIdList = countrySkuIdMap.get(countryCode);
                if (CollectionUtils.isEmpty(skuIdList)) {
                    skuIdList = Sets.newHashSet(skuIds);
                    countrySkuIdMap.put(countryCode, skuIdList);
                } else {
                    skuIdList.addAll(skuIds);
                }
            }
        });

        Map<String, Set<Long>> warehouseSkuMap = Maps.newHashMap();

        countrySkuIdMap.forEach((countryCode, skuIds) -> {

            if (StringUtils.isEmpty(countryCode) || CollectionUtils.isEmpty(skuIds)) {
                return;
            }

            List<StockItemManageReqDTO> stockItems = skuIds.stream().map(item -> {
                StockItemManageReqDTO stockItem = new StockItemManageReqDTO();
                stockItem.setSkuId(item);
                return stockItem;
            }).collect(Collectors.toList());

            request.setStockItem(stockItems);
            request.setCountryCode(countryCode);

            Map<Long, StockResDTO> stockMap = stockManageService.getStock(request);

            if (MapUtils.isEmpty(stockMap)) {
                log.info("无库存信息. countryCode={}, skuIds={}", countryCode, skuIds);
                return;
            }

            stockMap.forEach((k, v) -> {
                if (v == null) {
                    return;
                }

                // 现货库存
                Long stockQty = v.getStock();

                // 预占库存数量
                Long occupyQty = v.getOccupy();

                // 可用库存
                Long availableQty = v.getAvailableStock();

                // 在途库存
                Long onWayQty = v.getOnWayStock();

                // 库存状态 : 33有货、34无货
                Integer stockStateType = v.getStockStateType();

                if (stockStateType != null && stockStateType == StockManageServiceImpl.HAVE_STOCK
                        || NumberUtil.gt0(stockQty)
                        || NumberUtil.gt0(occupyQty)
                        || NumberUtil.gt0(availableQty)
                        || NumberUtil.gt0(onWayQty)
                ) {
                    Set<Long> skuIdList = warehouseSkuMap.get(v.getWarehouseId());
                    if (CollectionUtils.isEmpty(skuIdList)) {
                        warehouseSkuMap.put(v.getWarehouseId(), Sets.newHashSet(k));
                    } else {
                        skuIdList.add(k);
                    }
                }
            });
        });

        warehouseSkuMap.forEach((k, v) -> {
            String warehouseName = this.getWarehouseNameId(k, lang, k);
            errorMsg.append(String.format("商品SKU:%s, 在%s有库存，不允许归档，请先消化库存", v, warehouseName));
        });

        return errorMsg.toString();
    }

    public String initSkuFeature() {
        long total = skuAtomicService.count();
        if (total < 1) {
            return "data null";
        }

        long index = 1;
        long size = 100;
        long count = 0;
        do {
            Page<SkuPO> skuPoPage = skuAtomicService.pageSkuPo(index, size);
            if (skuPoPage != null && CollectionUtils.isNotEmpty(skuPoPage.getRecords())) {
                List<SkuPO> records = skuPoPage.getRecords();
                log.info("初始化商品属性,total={},index={}", total, index);

                for (SkuPO po : records) {
                    count++;
                    log.info("初始化商品属性,total={},index={},count={}", total, index, count);
                    LambdaQueryWrapper<SkuFeaturePO> lambdaQueryWrapper = Wrappers.lambdaQuery(SkuFeaturePO.class).eq(SkuFeaturePO::getSkuId, po.getSkuId()).eq(SkuFeaturePO::getYn, YnEnum.YES.getCode());
                    SkuFeaturePO featurePO = skuFeatureAtomicService.getOne(lambdaQueryWrapper);
                    if (featurePO == null) {
                        SkuFeaturePO skuFeaturePO = SkuConvert.INSTANCE.skuPo2Feature(po);
                        Date now = new Date();
                        skuFeaturePO.setCreateTime(now.getTime());
                        skuFeaturePO.setUpdateTime(now.getTime());
                        // 补充原产地
                        if (StringUtils.isBlank(skuFeaturePO.getOriginCountry())) {
                            SpuPO spuPo = spuAtomicService.getOne(Wrappers.lambdaQuery(SpuPO.class).eq(SpuPO::getSpuId, po.getSpuId()));
                            if (Objects.nonNull(spuPo)) {
                                skuFeaturePO.setOriginCountry(spuPo.getOriginCountry());
                            }
                        }
                        skuFeatureAtomicService.save(skuFeaturePO);
                        log.info("初始化商品属性成功,total={},index={},count={},skuId={}", total, index, count, po.getSkuId());
                    }

                    // 补充sku业务表数据
/*                    LambdaQueryWrapper<SkuBizPO> skuBizWrapper = Wrappers.lambdaQuery(SkuBizPO.class).eq(SkuBizPO::getSkuId, po.getSkuId()).eq(SkuBizPO::getYn, YnEnum.YES.getCode());
                    SkuBizPO skuBizPO = skuBizAtomicService.getOne(skuBizWrapper);
                    if (skuBizPO == null) {
                        SkuBizPO newSkuBizPo = new SkuBizPO();
                        newSkuBizPo.setSkuId(po.getSkuId());
                        newSkuBizPo.setSkuOnlineTime(po.getUpdateTime());
                        newSkuBizPo.setCreateTime(po.getUpdateTime());
                        newSkuBizPo.setUpdateTime(new Date());
                        newSkuBizPo.setYn(YnEnum.YES.getCode());
                        newSkuBizPo.setCreator(po.getCreator());
                        newSkuBizPo.setUpdater(po.getUpdater());
                        skuBizAtomicService.save(newSkuBizPo);
                    }*/
                }

                index++;
                if (records.size() < size) {
                    break;
                }
            }
        } while (((index - 1) * size) <= total);
        return "success";
    }

    @Resource
    private WarehouseManageService warehouseManageService;

    /**
     * 获取仓库名称.
     *
     * @param warehouseId the warehouse id
     * @return the warehouse name id
     */
    public String getWarehouseNameId(String warehouseId, String lang, String defaultName) {
        if (!cn.hutool.core.util.NumberUtil.isNumber(warehouseId)) {
            return defaultName;
        }

        WarehouseDetailVO detail = warehouseManageService.detail(Long.parseLong(warehouseId));

        if (detail == null || CollectionUtils.isEmpty(detail.getWarehouseLangVOList())) {
            return defaultName;
        }

        Map<String, String> langMap = detail.getWarehouseLangVOList().stream().collect(Collectors.toMap(WarehouseLangVO::getLang, WarehouseLangVO::getLangName));


        return langMap.getOrDefault(lang, langMap.getOrDefault("zh", defaultName));
    }

    private SpuPO getSpuPo(Long spuId, Integer yn,String pin) {
        SpuPO spuPO = new SpuPO();
        spuPO.setSpuId(spuId);
        spuPO.setUpdater(pin);
        spuPO.setUpdateTime(new Date());
        spuPO.setYn(yn);
        return spuPO;
    }

    private MkuPO getMkuPo(Long mkuId, Integer yn,String pin) {
        MkuPO mkuPO = new MkuPO();
        mkuPO.setMkuId(mkuId);
        mkuPO.setUpdater(pin);
        mkuPO.setUpdateTime(new Date());
        mkuPO.setYn(yn);
        return mkuPO;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ImportSpuData extends BaseWorkerDTO {
        // * 商品功能	* 商品用途	* 商品原理	* 商品材质或核心属性
        @NotNull(message = "SPU ID必填")
        @ExcelProperty(value = "SPU ID", index = 0)
        private String spuId;
        @ExcelProperty(value = "商品功能", index = 1)
        private String extend1;
        @ExcelProperty(value = "商品用途", index = 2)
        private String extend2;
        @ExcelProperty(value = "商品原理", index = 3)
        private String extend3;
        @ExcelProperty(value = "商品材质或核心属性", index = 4)
        private String extend4;

    }


    public String updateYueProductName(String clientCode, Set<Long> mkuIds) {

        if (StringUtils.isBlank(clientCode)) {
            return "客户编码不能为空";
        }

        List<CustomerMkuPO> customerMkuPOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(mkuIds)) {
            customerMkuPOList = customerMkuAtomicService.list(Wrappers.lambdaQuery(CustomerMkuPO.class).in(CustomerMkuPO::getMkuId, mkuIds).eq(CustomerMkuPO::getClientCode, clientCode).eq(CustomerMkuPO::getYn, YnEnum.YES.getCode()));

        } else {
            customerMkuPOList = customerMkuAtomicService.list(Wrappers.lambdaQuery(CustomerMkuPO.class).eq(CustomerMkuPO::getClientCode, clientCode).eq(CustomerMkuPO::getYn, YnEnum.YES.getCode()));
        }


        if (CollectionUtils.isEmpty(customerMkuPOList)) {
            return "当前客户无绑定商品";
        }


        int total = customerMkuPOList.size();
        int count = 0;
        for (CustomerMkuPO pricePO : customerMkuPOList) {
            count++;
            MkuLangPO mkuLangPO = mkuLangAtomicService.getOne(Wrappers.lambdaQuery(MkuLangPO.class).eq(MkuLangPO::getMkuId, pricePO.getMkuId()).eq(MkuLangPO::getLang, LangConstant.LANG_ZH).eq(MkuLangPO::getYn, YnEnum.YES.getCode()));
            if (Objects.isNull(mkuLangPO)) {
                continue;
            }

            String yueTitle = baiduTranslateService.translate(mkuLangPO.getMkuTitle(), LangConstant.LANG_ZH, LangConstant.LANG_ZH_HANT);
            if (StringUtils.isBlank(yueTitle)) {
                log.error("updateYueProductName MKU翻译中文繁体失败，MkuId={},mkuTitle={},total={},count={}", pricePO.getMkuId(), mkuLangPO.getMkuTitle(), total, count);
                continue;
            }

            mkuLangPO.setMkuTitle(yueTitle);
            mkuLangPO.setMkuId(null);
            mkuLangPO.setUpdateTime(new Date());
            mkuLangAtomicService.updateById(mkuLangPO);
            log.info("updateYueProductName MKU更新中文繁体名称成功 mkuId={},yueTitle={},total={},count={}", pricePO.getMkuId(), yueTitle, total, count);


            SpuLangPO spuLangPO = spuLangAtomicService.getOne(Wrappers.lambdaQuery(SpuLangPO.class).eq(SpuLangPO::getSpuId, pricePO.getSpuId()).eq(SpuLangPO::getLang, LangConstant.LANG_ZH).eq(SpuLangPO::getYn, YnEnum.YES.getCode()));
            if (Objects.isNull(spuLangPO)) {
                log.error("updateYueProductName ，spuId={},mkuId={},total={},count={}", pricePO.getSpuId(), pricePO.getMkuId(), total, count);
                continue;
            }
            String yueSpuTitle = baiduTranslateService.translate(spuLangPO.getSpuTitle(), LangConstant.LANG_ZH, LangConstant.LANG_ZH_HANT);
            if (StringUtils.isBlank(yueTitle)) {
                log.error("updateYueProductName SPU翻译中文繁体失败，spuId={},spuTitle={},total={},count={}", pricePO.getSpuId(), spuLangPO.getSpuTitle(), total, count);
                continue;
            }
            spuLangPO.setSpuId(null);
            spuLangPO.setSpuTitle(yueSpuTitle);
            spuLangPO.setUpdateTime(new Date());
            spuLangAtomicService.updateById(spuLangPO);
            log.info("updateYueProductName SPU更新中文繁体名称成功 spuId={},yueTitle={},total={},count={}", pricePO.getSpuId(), yueSpuTitle, total, count);
        }

        return "success";
    }


    public String updateSpuDetailImage(Set<Long> spuIds, Integer flag,Set<String> langSet) {
        if (CollectionUtils.isEmpty(spuIds) || Objects.isNull(flag)) {
            return String.format("商品 %s 不能为空，类型%s不能为空", spuIds, flag);
        }

        if (flag == 2 && CollectionUtils.isEmpty(langSet)) {
            return String.format("类型为flag=%s时，语言不能为空",flag);
        }

        LambdaQueryWrapper<SpuPO> lambdaQueryWrapper = Wrappers.lambdaQuery(SpuPO.class).select(SpuPO::getSpuId, SpuPO::getDetailImg,SpuPO::getUpdater, SpuPO::getYn).in(SpuPO::getSpuId, spuIds).eq(SpuPO::getYn, YnEnum.YES.getCode());
        List<SpuPO> spuPOList = spuAtomicService.list(lambdaQueryWrapper);

        AtomicLong spuCount = new AtomicLong(0);
        for (SpuPO po : spuPOList) {
            spuCount.incrementAndGet();
            Long spuId = po.getSpuId();
            // 修改商品图为空的数据
            if (flag == 1) {
                if (StringUtils.isNotBlank(po.getDetailImg()) && po.getDetailImg().contains("null")) {
                    po.setDetailImg("");
                    po.setUpdateTime(new Date());
                    spuAtomicService.saveOrUpdate(po);
                    log.info("更新商品细节图为空 spuId={},count={}", spuId, spuCount);
                    LambdaQueryWrapper<SkuPO> querySkuWrapper = Wrappers.lambdaQuery(SkuPO.class).select(SkuPO::getSkuId, SkuPO::getDetailImg,SkuPO::getUpdater, SkuPO::getYn).eq(SkuPO::getSpuId, spuId).eq(SkuPO::getYn, YnEnum.YES.getCode());
                    List<SkuPO> skuPOList = skuAtomicService.list(querySkuWrapper);
                    if (CollectionUtils.isEmpty(skuPOList)) {
                        continue;
                    }
                    for (SkuPO skuPO : skuPOList) {
                        Long skuId = skuPO.getSkuId();
                        skuPO.setDetailImg("");
                        skuPO.setUpdateTime(new Date());
                        skuAtomicService.saveOrUpdate(skuPO);
                        log.info("更新商品SKU细节图为空 spuId={},skuId={},count={}", spuId, skuId, spuCount);
                        // 查询sku和mku关系
                        Map<Long, Long> skuMkuRelationMap = getMkuRelationMap(skuId);
                        if (MapUtils.isEmpty(skuMkuRelationMap)) {
                            continue;
                        }
                        for (Map.Entry<Long, Long> entry : skuMkuRelationMap.entrySet()) {
                            MkuPO mkuPO = new MkuPO();
                            mkuPO.setMkuId(entry.getValue());
                            mkuPO.setDetailImg("");
                            mkuPO.setUpdateTime(new Date());
                            mkuPO.setUpdater("system");
                            mkuAtomicService.saveOrUpdate(mkuPO);
                            log.info("更新商品MKU细节图为空 spuId={},skuId={},mkuId={},count={}", spuId, skuId, entry.getValue(), spuCount);
                        }
                    }
                }
            } else if (flag == 2) {
                List<SpuLangPO> spuLangPOList = spuLangAtomicService.list(Wrappers.lambdaQuery(SpuLangPO.class).eq(SpuLangPO::getSpuId, spuId).eq(SpuLangPO::getYn, YnEnum.YES.getCode()));
                if (CollectionUtils.isEmpty(spuLangPOList)) {
                    continue;
                }
                Optional<SpuLangPO> first = spuLangPOList.stream().filter(langPo -> LangConstant.LANG_ZH.equals(langPo.getLang())).findFirst();
                String zhName = null;
                if (first.isPresent()) {
                    zhName = first.get().getSpuTitle();
                }

                if (StringUtils.isBlank(zhName)) {
                    continue;
                }

                for (SpuLangPO spuLangPO : spuLangPOList) {
                    if (StringUtils.isNotBlank(spuLangPO.getSpuTitle()) || LangConstant.LANG_ZH.equals(spuLangPO.getLang()) || !langSet.contains(spuLangPO.getLang())) {
                        continue;
                    }

                    if (LangConstant.LANG_EN.equals(spuLangPO.getLang())) {
                        spuLangPO.setSpuTitle(baiduTranslateService.translate(zhName, LangConstant.LANG_ZH,LangConstant.LANG_EN));
                    } else if (LangConstant.LANG_VN.equals(spuLangPO.getLang())) {
                        spuLangPO.setSpuTitle(baiduTranslateService.translate(zhName, LangConstant.LANG_ZH,LangConstant.LANG_VN));
                    } else if (LangConstant.LANG_TH.equals(spuLangPO.getLang())) {
                        spuLangPO.setSpuTitle(baiduTranslateService.translate(zhName, LangConstant.LANG_ZH,LangConstant.LANG_TH));
                    }
                    spuLangPO.setUpdateTime(new Date());
                    spuLangPO.setSpuId(null);
                    spuLangAtomicService.saveOrUpdate(spuLangPO);
                    log.info("更新商品多语言名 spuId={},count={}", spuId,  spuCount);

                    LambdaQueryWrapper<SkuPO> querySkuWrapper = Wrappers.lambdaQuery(SkuPO.class).select(SkuPO::getSkuId, SkuPO::getDetailImg, SkuPO::getYn).eq(SkuPO::getSpuId, spuId).eq(SkuPO::getYn, YnEnum.YES.getCode());
                    List<SkuPO> skuPOList = skuAtomicService.list(querySkuWrapper);
                    if (CollectionUtils.isEmpty(skuPOList)) {
                        continue;
                    }
                    for (SkuPO skuPO : skuPOList) {
                        Long skuId = skuPO.getSkuId();
                        // 查询sku和mku关系
                        Map<Long, Long> skuMkuRelationMap = getMkuRelationMap(skuId);
                        if (MapUtils.isEmpty(skuMkuRelationMap)) {
                            continue;
                        }
                        for (Map.Entry<Long, Long> entry : skuMkuRelationMap.entrySet()) {
                            List<MkuLangPO> mkuLangPOList = mkuLangAtomicService.list(Wrappers.lambdaQuery(MkuLangPO.class).eq(MkuLangPO::getMkuId, entry.getValue()).eq(MkuLangPO::getYn, YnEnum.YES.getCode()));
                            if (CollectionUtils.isEmpty(mkuLangPOList)) {
                                continue;
                            }
                            Optional<MkuLangPO> firstMku = mkuLangPOList.stream().filter(langPo -> LangConstant.LANG_ZH.equals(langPo.getLang())).findFirst();
                            String zhMkuName = null;
                            if (firstMku.isPresent()) {
                                zhMkuName = firstMku.get().getMkuTitle();
                            }

                            if (StringUtils.isBlank(zhMkuName)) {
                                continue;
                            }

                            for (MkuLangPO mkuLangPO : mkuLangPOList) {
                                if (StringUtils.isNotBlank(mkuLangPO.getMkuTitle()) || LangConstant.LANG_ZH.equals(mkuLangPO.getLang()) || !langSet.contains(mkuLangPO.getLang())) {
                                    continue;
                                }

                                if (LangConstant.LANG_EN.equals(mkuLangPO.getLang())) {
                                    mkuLangPO.setMkuTitle(baiduTranslateService.translate(zhMkuName, LangConstant.LANG_ZH,LangConstant.LANG_EN));
                                } else if (LangConstant.LANG_VN.equals(mkuLangPO.getLang())) {
                                    mkuLangPO.setMkuTitle(baiduTranslateService.translate(zhMkuName, LangConstant.LANG_ZH,LangConstant.LANG_VN));
                                } else if (LangConstant.LANG_TH.equals(mkuLangPO.getLang())) {
                                    mkuLangPO.setMkuTitle(baiduTranslateService.translate(zhMkuName, LangConstant.LANG_ZH,LangConstant.LANG_TH));
                                }
                                mkuLangPO.setUpdateTime(new Date());
                                mkuLangPO.setMkuId(null);
                                mkuLangAtomicService.saveOrUpdate(mkuLangPO);
                                log.info("更新商品多语言名 spuId={},skuId={},mkuId={},lang={},count={}", spuId,entry.getKey(),entry.getValue(),mkuLangPO.getLang(),  spuCount);
                            }
                        }
                    }
                }
            }
        }
        return "success";
    }

    private Map<Long, Long> getMkuRelationMap(Long skuId) {
        List<MkuRelationPO> mkuRelationPOList = mkuRelationAtomicService.list(Wrappers.lambdaQuery(MkuRelationPO.class).select(MkuRelationPO::getMkuId, MkuRelationPO::getSkuId).eq(MkuRelationPO::getSkuId, skuId).eq(MkuRelationPO::getYn, YnEnum.YES.getCode()));
        if (CollectionUtils.isEmpty(mkuRelationPOList)) {
            return Collections.emptyMap();
        }

        Map<Long, Long> skuMkuRelationMap = mkuRelationPOList.stream().filter(Objects::nonNull).collect(Collectors.toMap(MkuRelationPO::getSkuId, MkuRelationPO::getMkuId));
        return skuMkuRelationMap;
    }


    public void downloadNullEnTitleSpu(HttpServletResponse response) {
        LambdaQueryWrapper<SpuLangPO> queryWrapper = Wrappers.lambdaQuery(SpuLangPO.class).select(SpuLangPO::getSpuId).eq(SpuLangPO::getLang, LangConstant.LANG_EN).eq(SpuLangPO::getSpuTitle,"").eq(SpuLangPO::getYn, YnEnum.YES.getCode());
        List<SpuLangPO> spuLangPOList = spuLangAtomicService.list(queryWrapper);
        if (CollectionUtils.isEmpty(spuLangPOList)) {
            throw new RuntimeException("数据为空");
        }
        List<NullEnTitleSpuDTO> dataList = spuLangPOList.stream().map(po -> new NullEnTitleSpuDTO(String.valueOf(po.getSpuId()))).collect(Collectors.toList());
        // 文件名
        String fileName = "nullEnTitleSpu" + Constant.DOT + Constant.XLSX;
        // 模版数据写入
        try (ServletOutputStream outputStream = response.getOutputStream(); ExcelWriter excelWriter = EasyExcel.write(outputStream).filedCacheLocation(CacheLocationEnum.NONE).build()) {
            // 设置content-disposition响应头控制浏览器以下载的形式打开文件，中文文件名要使用URLEncoder.encode方法进行编码，否则会出现文件名乱码
            response.setHeader("content-disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));
                // 数据写入文档
            // 写文件
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "sheet1")
                    .head(NullEnTitleSpuDTO.class)
                    .filedCacheLocation(CacheLocationEnum.NONE)
                    .build();
            excelWriter.write(dataList, writeSheet); // 写入数据
            excelWriter.finish();
        } catch (Exception e) {
            log.error("【系统异常】DynamicTemplateService.generateTemplate 生成模版异常, spuLangPOList={}", JSON.toJSONString(spuLangPOList), e);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
     class NullEnTitleSpuDTO{
        @ExcelProperty(value = "SPU id",index = 0)
        private String spuId;
     }


     public String updateVnSpuLangKeyPhrases(){
         LambdaQueryWrapper<SpuPO> queryWrapper = Wrappers.lambdaQuery(SpuPO.class).eq(SpuPO::getSourceCountryCode, CountryConstant.COUNTRY_VN).eq(SpuPO::getYn, YnEnum.YES.getCode());
         long total = spuAtomicService.count(queryWrapper);
         log.info("updateVnSpuLangKeyPhrases total={}",total);
         long size = 100;
         long current = 0;
         Page<SpuPO> page = new Page<>(current,size);
         long count = 0;
         do {
             current++;
             queryWrapper.select(SpuPO::getSpuId,SpuPO::getCreateTime).orderByDesc(SpuPO::getCreateTime);
             page.setCurrent(current);
             Page<SpuPO> resultPage = spuAtomicService.page(page, queryWrapper);
             log.info("updateVnSpuLangKeyPhrases 更新开始 total={} count={},current={},records={}",total,count,resultPage.getCurrent(),resultPage.getRecords().size());
             if (null != resultPage && CollectionUtils.isNotEmpty(resultPage.getRecords())){
                 List<SpuPO> records = resultPage.getRecords();
                 for (SpuPO po : records) {
                     count++;
                     log.info("updateVnSpuLangKeyPhrases 更新开始 total={} count={}",total,count);
                     List<SpuLangPO> spuLangPOList = spuLangAtomicService.list(Wrappers.lambdaQuery(SpuLangPO.class).eq(SpuLangPO::getSpuId, po.getSpuId()));
                     if (CollectionUtils.isEmpty(spuLangPOList)) {
                         continue;
                     }
                     List<SpuLangPO> langPOList = Lists.newArrayList();
                     spuLangPOList.forEach(spuLangPO -> {
                         if (StringUtils.isNotBlank(spuLangPO.getQualifier())){
                             spuLangPO.setKeyPhrases(spuLangPO.getQualifier());
                             spuLangPO.setUpdateTime(new Date());
                             spuLangPO.setSpuId(null);
                             langPOList.add(spuLangPO);
                         }
                     });
                     if (CollectionUtils.isNotEmpty(langPOList)) {
                         spuLangAtomicService.saveOrUpdateBatch(langPOList);
                     }
                     log.info("updateVnSpuLangKeyPhrases 更新结束 total={} count={}",total,count);
                 }
             }
         }while ((current * size) < total);

         return "success";
     }

     /**
      * 检查客户mku是否存在。
      * @param spuIds SPU的ID集合，用于查询关联的SKU。
      * @param yn 确认标志，1表示确认，0或null表示取消。
      */
     private String checkCustomerMku(Set<Long> spuIds,Integer yn){
         String msg = "";
        if(yn == null || Integer.valueOf(YesOrNoEnum.YES.getCode()).equals(yn) || CollectionUtils.isEmpty(spuIds)){
            log.error("ProductToolService.checkCustomerMku return");
            return msg;
        }

        // 1.查询spu下所有sku/**/
        List<SkuPO> skuPOList = skuAtomicService.getSkuListBySpuIds(spuIds);
        if(CollectionUtils.isEmpty(skuPOList)){
            return msg;
        }
        Set<Long> dbSpuIds = skuPOList.stream().map(SkuPO::getSpuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(dbSpuIds) || dbSpuIds.size() != spuIds.size()){
            spuIds.removeAll(dbSpuIds);
            msg = "Error spu exists，spuIds."+ String.join(Constant.COMMA,spuIds.stream().map(String::valueOf).collect(Collectors.toSet()));
            return msg;
        }
        // 2.查询sku下所有mku
        List<Long> skuIds = skuPOList.stream().map(SkuPO::getSkuId).collect(Collectors.toList());
        List<MkuRelationPO> mkuRelationPOS = mkuRelationAtomicService.queryBindListBySkuIds(skuIds);
        if(CollectionUtils.isEmpty(mkuRelationPOS)){
            msg = "mku relation is null";
            return msg;
        }
        Set<Long> dbSkuIds = mkuRelationPOS.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(dbSkuIds) || dbSkuIds.size() != skuIds.size()){
            skuIds.removeAll(dbSkuIds);
            msg = "Error sku exists，skuIds." + String.join(Constant.COMMA,skuIds.stream().map(String::valueOf).collect(Collectors.toSet()));
            return msg;
        }
        // 3.查询所有mku下的绑客关系
        List<Long> mkuIds = mkuRelationPOS.stream().map(MkuRelationPO::getMkuId).collect(Collectors.toList());
        List<CustomerMkuPO> mkuPOS = customerMkuAtomicService.listCustomerMkuPOByMkuIds(mkuIds,null);
        if(CollectionUtils.isEmpty(mkuPOS)){
            return msg;
        }
        List<String> mkuIdList = new ArrayList<>();
        List<PreselectionVO> preselectionVOList = operDuccConfig.preselectionList();
        for (PreselectionVO preselectionVO : preselectionVOList){
            for (CustomerMkuPO customerMkuPO : mkuPOS){
                if(!preselectionVO.getPreseletorClientCodeList().contains(customerMkuPO.getClientCode())){
                    mkuIdList.add(String.valueOf(customerMkuPO.getMkuId()));
                }
            }
        }
        if(CollectionUtils.isNotEmpty(mkuIdList)){
            msg = "Customer mku relation is exists,Please invalid。mkuIds." + String.join(Constant.COMMA, mkuIdList);
            return msg;
        }
        return msg;
     }

    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
     public String initCustomerSkuDetailDraftAndDetail(CustomerSkuPriceDraftPO customerSkuPriceDraftPO){
         try{
             CustomerSkuPriceDetailDraftPO customerSkuPriceDetailDraftPO = CustomerSkuPriceConvert.INSTANCE.skuPriceDraftPo2DetailDraftPo(customerSkuPriceDraftPO);
             customerSkuPriceDetailDraftPO.setId(null);
             customerSkuPriceDetailDraftPO.setBizId(customerSkuPriceDraftPO.getId());
             customerSkuPriceDetailDraftPO.setBeginTime(Constant.ZERO);
             customerSkuPriceDetailDraftPO.setEndTime(Constant.MAX);
             customerSkuPriceDetailDraftPO.setEnableStatus(YesOrNoEnum.YES.getCode());
             customerSkuPriceDetailDraftAtomicService.save(customerSkuPriceDetailDraftPO);

             CustomerSkuPricePO customerSkuPricePO = customerSkuPriceAtomicService.getOne(customerSkuPriceDetailDraftPO.getClientCode()
                     ,customerSkuPriceDetailDraftPO.getSkuId(),customerSkuPriceDetailDraftPO.getCustomerTradeType(),customerSkuPriceDetailDraftPO.getCurrency());
             if(customerSkuPricePO == null){
                 log.error("ProductToolService.initCustomerSkuDetailDraftAndDetail error,customerSkuPricePO is null");
                 return Constant.SUCCESS;
             }

             CustomerSkuPriceDetailPO customerSkuPriceDetailPO = CustomerSkuPriceConvert.INSTANCE.skuPricePo2DetailPo(customerSkuPricePO);
             customerSkuPriceDetailPO.setBizId(customerSkuPricePO.getId());
             customerSkuPriceDetailPO.setSourceId(customerSkuPriceDetailDraftPO.getId());
             customerSkuPriceDetailPO.setBeginTime(Constant.ZERO);
             customerSkuPriceDetailPO.setEndTime(Constant.MAX);
             customerSkuPriceDetailPO.setEnableStatus(YesOrNoEnum.YES.getCode());
             customerSkuPriceDetailAtomicService.save(customerSkuPriceDetailPO);
         }catch (Exception e){
             log.error("ProductToolService.initCustomerSkuDetailDraftAndDetail error",e);
         }
         return Constant.SUCCESS;
     }


    public String deleteCustomerSkuDetailDraft(Set<Long> detailDraftIds){
         if(CollectionUtils.isEmpty(detailDraftIds)){
             return Constant.SUCCESS;
         }
         customerSkuPriceDetailDraftAtomicService.removeBatchByIds(detailDraftIds);
         return Constant.SUCCESS;
    }


    /**
     * 更新SPU属性的适用范围
     * @param skuIds SKU的ID集合
     * @param attributeScope 属性的适用范围
     * @return 更新后的属性范围信息
     */
    public String updateSpuAttributeScope(List<Long> skuIds,String attributeScope){
        if (CollectionUtils.isEmpty(skuIds) || StringUtils.isBlank(attributeScope)){
            return "参数不能为空";
        }

        int total = skuIds.size();
        for (int i = 0; i < total; i++) {
            Long skuId = skuIds.get(i);
            SkuPO skuPo = skuAtomicService.getSkuPoBySkuId(skuId);

            if (Objects.isNull(skuPo)) {
                continue;
            }

            Long spuId = skuPo.getSpuId();

            SpuPO spuPo = spuAtomicService.getSpuPoBySpuId(spuId);
            if (Objects.isNull(spuPo)) {
                log.error("当前商品不存在 skuId={},spuId={} current={}",skuId,spuId,i);
                continue;
            }

            LambdaUpdateWrapper<SpuPO> updateSpuWrapper = Wrappers.lambdaUpdate(SpuPO.class).set(SpuPO::getAttributeScope, attributeScope).set(SpuPO::getUpdater, spuPo.getUpdater()).set(SpuPO::getUpdateTime, new Date()).eq(SpuPO::getSpuId, spuId);
            boolean update = spuAtomicService.update(updateSpuWrapper);
            if (update) {
                log.info("更新商品成功 skuId={},spuId={},current={}",skuId,spuId,i);
            }else {
                log.error("更新商品失败 skuId={},spuId={},current={}",skuId,spuId,i);
            }

            // 更新SPU草稿
            SpuDraftPO spuDraftPo = spuDraftAtomicService.getBySpuId(spuId);
            String spuJsonInfo = spuDraftPo.getSpuJsonInfo();
            SaveSpuVO saveSpuVO = JSON.parseObject(spuJsonInfo, SaveSpuVO.class);
            SpuVO spuVO = saveSpuVO.getSpuVO();
            spuVO.setAttributeScope(attributeScope);
            saveSpuVO.setSpuVO(spuVO);

            LambdaUpdateWrapper<SpuDraftPO> updateSpuDraftWrapper = Wrappers.lambdaUpdate(SpuDraftPO.class).set(SpuDraftPO::getSpuJsonInfo, JSON.toJSONString(saveSpuVO)).set(SpuDraftPO::getUpdater, spuPo.getUpdater()).set(SpuDraftPO::getUpdateTime, new Date()).eq(SpuDraftPO::getSpuId, spuId);
            boolean spuDraftUpdate = spuDraftAtomicService.update(updateSpuDraftWrapper);
            if (spuDraftUpdate) {
                log.info("更新商品SPU草稿成功 skuId={},spuId={},current={}",skuId,spuId,i);
            }else {
                log.error("更新商品SPU草稿失败 skuId={},spuId={},current={}",skuId,spuId,i);
            }
        }
        return  "success";
    }

    /**
     * 修复商品上异常的销售属性值
     * @param skuIds 商品IDs
     * @param saleAttribute 销售属性字符串
     */
    public String updateSaleAttribute(Set<Long> skuIds,String saleAttribute){
        if (CollectionUtils.isEmpty(skuIds) || StringUtils.isBlank(saleAttribute)) {
            return "商品和销售不能为空" ;
        }
        for (Long skuId : skuIds) {
            SkuPO dbSkuPO = skuAtomicService.getSkuPoBySkuId(skuId);
            if (Objects.isNull(dbSkuPO)) {
                log.error(String.format("SKU ID %s 不存在",skuId));
                continue;
            }
            // 修改SKU的销售属性
            LambdaUpdateWrapper<SkuPO> updateSkuWrapper = Wrappers.lambdaUpdate(SkuPO.class).set(SkuPO::getSaleAttribute, saleAttribute).set(SkuPO::getUpdateTime, new Date())
                    .set(SkuPO::getUpdater,dbSkuPO.getUpdater()).eq(SkuPO::getSkuId, skuId).eq(SkuPO::getYn, YnEnum.YES.getCode());
            boolean update = skuAtomicService.update(updateSkuWrapper);
            log.info("更新skuId={}的销售属性完成,结果:{}",skuId,update);
            // 修改SKU草稿的销售属性
            SkuDraftPO dbSkuDraftPo = skuDraftAtomicService.getValidBySkuId(skuId);
            if (Objects.nonNull(dbSkuDraftPo)) {
                SkuDraftPO newSkuDraftPo = new SkuDraftPO();
                newSkuDraftPo.setId(dbSkuDraftPo.getId());
                newSkuDraftPo.setUpdateTime(System.currentTimeMillis());
                List<PropertyValueVO> newSalePropertyValueList = Lists.newArrayList();
                PropertyValueVO newPropertyValueVo = new PropertyValueVO();
                // 100100:103901
                String[] split = saleAttribute.split(Constant.COLON);
                newPropertyValueVo.setAttributeId(Long.valueOf(split[0]));
                newPropertyValueVo.setAttributeValueId(Long.valueOf(split[1]));
                newSalePropertyValueList.add(newPropertyValueVo);
//                newSkuDraftPo.setSaleProperty(JSON.toJSONString(newSalePropertyValueList));
                newSkuDraftPo.setUpdater(dbSkuPO.getUpdater());
                boolean b = skuDraftAtomicService.updateById(newSkuDraftPo);
                log.info("更新skuId={}草稿的销售属性完成,结果:{}",skuId, b);
            }

            // 修改MKU主表的销售属性
            MkuRelationPO mkuRelationPo = mkuRelationAtomicService.getMkuBySkuId(skuId);
            if (Objects.isNull(mkuRelationPo)) {
                continue;
            }

            MkuPO mkuPo = mkuAtomicService.getPOById(mkuRelationPo.getMkuId());
            if (Objects.isNull(mkuPo)) {
                continue;
            }
            LambdaUpdateWrapper<MkuPO> updateMkuWrapper = Wrappers.lambdaUpdate(MkuPO.class).set(MkuPO::getSaleAttribute, saleAttribute).set(MkuPO::getUpdateTime, new Date())
                    .set(MkuPO::getUpdater,mkuPo.getUpdater()).eq(MkuPO::getMkuId, mkuPo.getMkuId()).eq(MkuPO::getYn, YnEnum.YES.getCode());
            boolean updated = mkuAtomicService.update(updateMkuWrapper);
            log.info("更新skuId={}关联的mkuId={}的销售属性完成,结果:{}",skuId,mkuPo.getMkuId(),updated);
        }

        return "success";
    }
}
