package com.jdi.isc.product.soa.service.manage.price.agreementPrice.cost;

import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.AddTaxPriceMsgEnum;
import com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum;
import com.jdi.isc.product.soa.domain.enums.product.FulfillmentRateTypeEnum;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.InitAgreementPriceVO;
import com.jdi.isc.product.soa.domain.price.extendPrice.biz.CurrencyPriceVO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.price.taxRefundRate.po.TaxRefundRatePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.po.ExportTaxRatePO;
import com.jdi.isc.product.soa.price.api.price.res.CrossBorderImportTaxResDTO;
import com.jdi.isc.product.soa.service.atomic.taxRate.ExportTaxRateAtomicService;
import com.jdi.isc.product.soa.service.manage.price.taxRefundRate.TaxRefundRateManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.CountryTaxManageService;
import com.jdi.isc.product.soa.rpc.suport.AlertHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * CnCostPriceService类用于处理国家成本价的计算。
 *
 * <AUTHOR>
 * @date [日期]
 */
@Service
@Slf4j
public class CnCostPriceService extends AbstractCostPriceService implements InitializingBean {

    /**
     * 国家税率管理服务
     */
    @Resource
    private CountryTaxManageService countryTaxManageService;


    @Resource
    private ExportTaxRateAtomicService exportTaxRateAtomicService;

    @Resource
    private TaxRefundRateManageService taxRefundRateManageService;

    @Resource
    private OperDuccConfig operDuccConfig;

    /**
     * 根据InitAgreementPriceVO对象计算并返回国家成本价。
     * @param vo InitAgreementPriceVO对象，包含skuId、sourceCountryCode和targetCountryCode等信息。
     * @return CurrencyPriceVO对象，包含国家成本价和对应的货币代码。
     */
    @Override
    @PFTracing
    public CurrencyPriceVO localizedCostPrice(InitAgreementPriceVO vo) {
        // 查询SKU价格信息
        SkuPricePO skuPriceInfo = skuPriceAtomicService.querySkuPrice(vo.getSkuId(), vo.getSourceCountryCode(), TradeDirectionEnum.SUPPLIER);
        if(Objects.isNull(skuPriceInfo)){
            return createErrorResponse("跨境品未查询到基础价格信息;");
        }
        // 验证采购价
        BigDecimal procurementPrice = skuPriceInfo.getPrice();
        if(Objects.isNull(procurementPrice) || procurementPrice.compareTo(BigDecimal.ZERO) == 0){
            return createErrorResponse("跨境品基础价格等于0或无值");
        }
        // 计算履约费用
        CurrencyPriceVO fulfillmentCostVO = calculateFulfillmentCost(vo,skuPriceInfo);
        if(!fulfillmentCostVO.getSuccess()){
            return fulfillmentCostVO;
        }

        BigDecimal fulfillmentCost = fulfillmentCostVO.getPrice();
        // 获取进口关税
        CrossBorderImportTaxResDTO taxResDTO = getImportTaxInfo(vo, fulfillmentCostVO.getCurrency(),fulfillmentCostVO);
        if (Objects.isNull(taxResDTO) || Objects.isNull(taxResDTO.getImportTax())) {
            return createErrorResponse(taxResDTO != null ? taxResDTO.getDesc() : "获取进口关税信息失败");
        }
        // 进口关税
        BigDecimal importDuty = taxResDTO.getImportTax().setScale(2, RoundingMode.HALF_UP);
        // 增值税金
        BigDecimal deductionAmount = taxResDTO.getDeductionAmount();
        // 基础价格 = 采购价 +  履约费 +  关税 保留2位小数，四舍五入
        //BigDecimal costPrice = procurementPrice.add(fulfillmentCost).add(importDuty).subtract(deductionAmount).setScale(2, RoundingMode.HALF_UP);
        // 汇率
        CurrencyPriceVO currencyExchangeRate = super.getExchangeRate(skuPriceInfo.getCurrency(), vo.getTargetCountryCode());
        if(!currencyExchangeRate.getSuccess()){
            return currencyExchangeRate;
        }

        // https://joyspace.jd.com/pages/4kVesU2f0CfcMdg34Qlc 2.5.1
        Pair<BigDecimal, String> jdTaxRefundPricePair = this.computeJdTaxRefundPrice(vo, procurementPrice);
        BigDecimal jdTaxRefundPrice = jdTaxRefundPricePair.getKey();
        // 国家成本价= 基础价格 * 汇率 保留2位小数，四舍五入
        if (jdTaxRefundPrice == null) {
//            return createErrorResponse(jdTaxRefundPricePair.getValue());
            jdTaxRefundPrice = BigDecimal.ZERO;
        }

        BigDecimal costPrice = procurementPrice
                .subtract(jdTaxRefundPrice)
                .add(fulfillmentCost)
                .add(importDuty)
                .subtract(deductionAmount)
                ;
        // 基础价格 = 采购价 - 国内退税金额 +  履约费 +  关税 - 增值税金(AddTaxPriceMsgEnum 不同国家名称不同)
        BigDecimal localizedCostPrice = costPrice.multiply(currencyExchangeRate.getPrice()).setScale(2, RoundingMode.HALF_UP);

        CurrencyPriceVO currencyPriceVO = new CurrencyPriceVO();
        currencyPriceVO.setPrice(localizedCostPrice);
        currencyPriceVO.setCurrency(currencyExchangeRate.getCurrency());

        String costMsg = Objects.equals(FulfillmentRateTypeEnum.WAREHOUSE_PRODUCT.getCode(),vo.getIsWareHouseProduct())
            ? "国家跨境入仓成本价" : "国家成本价";
        String totalMsg = "";

        if(deductionAmount.compareTo(BigDecimal.ZERO) == 0){
//            totalMsg = String.format("%s%s=(跨境含税采购价%s+履约费用%s+进口关税%s)*汇率%s;\n",costMsg,
            totalMsg = String.format("%s%s=(跨境含税采购价%s-国内退税金额%s+履约费用%s+进口关税%s)*汇率%s;\n",costMsg,
                    localizedCostPrice.stripTrailingZeros().toPlainString()
                    ,procurementPrice.stripTrailingZeros().toPlainString()
                    ,jdTaxRefundPrice.stripTrailingZeros().toPlainString()
                    ,fulfillmentCost.stripTrailingZeros().toPlainString()
                    ,importDuty.stripTrailingZeros().toPlainString()
                    ,currencyExchangeRate.getPrice().stripTrailingZeros().toPlainString());
        }else {
            String deductionMsg = AddTaxPriceMsgEnum.fromValueToDesc(vo.getTargetCountryCode());
//                totalMsg = String.format("%s%s=(跨境含税采购价%s+履约费用%s+进口关税%s-%s%s)*汇率%s;\n",costMsg,localizedCostPrice.stripTrailingZeros().toPlainString()
            totalMsg = String.format("%s%s=(跨境含税采购价%s-国内退税金额%s+履约费用%s+进口关税%s-%s%s)*汇率%s;\n",costMsg,localizedCostPrice.stripTrailingZeros().toPlainString()
                    ,procurementPrice.stripTrailingZeros().toPlainString()
                    ,jdTaxRefundPrice.stripTrailingZeros().toPlainString()
                    ,fulfillmentCost.stripTrailingZeros().toPlainString()
                    ,importDuty.stripTrailingZeros().toPlainString()
                    ,deductionMsg
                    ,deductionAmount.stripTrailingZeros().toPlainString()
                    ,currencyExchangeRate.getPrice().stripTrailingZeros().toPlainString());
        }

        currencyPriceVO.setMsg(totalMsg + jdTaxRefundPricePair.getValue() + "\n" + fulfillmentCostVO.getMsg() + taxResDTO.getDesc());
        return currencyPriceVO;
    }

    @NotNull
    private Pair<BigDecimal, String> computeJdTaxRefundPrice(InitAgreementPriceVO vo, BigDecimal procurementPrice) {

        if (!operDuccConfig.isComputeJdTaxRefundPrice(vo.getTargetCountryCode())) {
            return Pair.of(BigDecimal.ZERO, "非灰度国家，不计算国内退税金额");
        }

        if (!operDuccConfig.isComputeJdTaxRefundPriceSku(vo.getSkuId())) {
            return Pair.of(BigDecimal.ZERO, "非灰度SkuId，不计算国内退税金额");
        }

        Pair<BigDecimal, String> jdTaxRefundPricePair;
        try {
            jdTaxRefundPricePair = this.computeJdTaxRefundPrice(vo, procurementPrice, vo.getSkuId());
        } catch (Exception e) {
            AlertHelper.p0("计算国内退税金额失败", e.getMessage());
            // 这里之所以抛异常, 最主要的原因就是，防止成本价计算错误，因为成本价计算为空，数据库会更新为null
            log.error("计算国内退税金额失败, vo={}, e={}", vo, e);
            jdTaxRefundPricePair = Pair.of(null, "计算国内退税金额失败, 请检查业务数据完整性！");
//            throw new BizException("计算国内退税金额失败, 请检查业务数据完整性！");
        }

        log.info("成本价计算国内退税金额, skuId={}, result={}", vo.getSkuId(), jdTaxRefundPricePair);
        return jdTaxRefundPricePair;
    }

    /**
     * 计算国内退税金额
     * 国内退税金额 = 跨境含税采购价/（1+国内增值税率）* 退税率 * 历史实际退税成功率（2位小数，四舍五入）
     */
    private Pair<BigDecimal, String> computeJdTaxRefundPrice(InitAgreementPriceVO vo, BigDecimal purchaseTaxPrice, Long skuId) {

        if (purchaseTaxPrice == null || skuId == null) {
            log.warn("含税采购价或者skuId is null. skuId={}", skuId);
            return Pair.of(null, String.format("含税采购价或者skuId is null=%s", skuId));
        }

        SkuPO sku = super.getSkuBySkuId(skuId);

        if (sku == null) {
            log.warn("找不到sku信息. skuId={}", skuId);
            return Pair.of(null, String.format("找不到sku信息. skuId=%s", skuId));
        }

        // 历史实际退税成功率 配置表中获取
        BigDecimal historyRaxRefundRate = this.historyRaxRefundRate(vo, sku);

        if (historyRaxRefundRate == null) {
            return Pair.of(null, "未配置历史实际退税成功率");
        }

        // 国内增值税率 从国内接口获取
        BigDecimal jdVatRate = this.getJdVatRate(sku);

        if (jdVatRate == null) {
            return Pair.of(null, "未配置国内增值税");
        }

        // 出口退税率, 从sku上取hscode编码，根据hscode编码在【税务管理/出口退税率】取出口退税率
        BigDecimal exportRebateRate = this.getExportRebateRate(sku, vo);

        if (exportRebateRate == null) {
            return Pair.of(null, "未配置出口退税率");
        }

        BigDecimal exportRebateRateDiv100 = exportRebateRate.divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP).stripTrailingZeros();
        BigDecimal historyRaxRefundRateDiv100 = historyRaxRefundRate.divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP).stripTrailingZeros();
        // 国内退税金额
        BigDecimal jdTaxRefundPrice = purchaseTaxPrice.divide(BigDecimal.ONE.add(jdVatRate), 8, RoundingMode.HALF_UP)
                .multiply(exportRebateRateDiv100)
                .multiply(historyRaxRefundRateDiv100)
                .setScale(2, RoundingMode.HALF_UP);

        String jdTaxRefundPriceMessage = String.format("国内退税金额%s = 跨境含税采购价%s /（1+国内增值税率%s）* 出口退税率%s * 历史实际退税成功率%s（2位小数，四舍五入）", jdTaxRefundPrice, purchaseTaxPrice, jdVatRate, exportRebateRateDiv100, historyRaxRefundRateDiv100);

        log.info("computeJdTaxRefundPrice. 国内退税金额{} = 跨境含税采购价{} /（1+国内增值税率{}）* 出口退税率{} * 历史实际退税成功率{}（2位小数，四舍五入",
                jdTaxRefundPrice, purchaseTaxPrice, jdVatRate, exportRebateRate, historyRaxRefundRate);

        return Pair.of(jdTaxRefundPrice, jdTaxRefundPriceMessage);
    }

    /**
     * 根据商品信息获取出口退税率
     * @param sku 商品信息对象
     * @return 商品的出口退税率，以BigDecimal类型返回
     */
    private BigDecimal getExportRebateRate(SkuPO sku, InitAgreementPriceVO vo) {
        String hsCode = sku.getHsCode();

        if (StringUtils.isEmpty(hsCode)) {
            log.warn("sku hsCode is empty, skuId={}", sku.getSkuId());
            return null;
        }

        String sourceCountryCode = vo.getSourceCountryCode();

        if (StringUtils.isEmpty(sourceCountryCode)) {
            log.warn("sku sourceCountryCode is empty, skuId={}", sku.getSkuId());
            return null;
        }

        ExportTaxRatePO exportTaxRatePO = exportTaxRateAtomicService.getExportTaxRateByHsCode(hsCode, sourceCountryCode);

        BigDecimal exportRebateRate = exportTaxRatePO == null ? null : exportTaxRatePO.getExportRebateRate();

        log.info("sku出口退税率. sku={}, rate={}", sku.getSkuId(), exportRebateRate);

        return exportRebateRate;
    }

    /**
     * 根据商品信息获取京东增值税率
     * @param sku 商品信息对象
     * @return 商品对应的增值税率，以BigDecimal类型返回
     */
    private BigDecimal getJdVatRate(SkuPO sku) {
        BigDecimal jdVatRate = bWareReadRpcService.getJdVatRate(sku.getJdSkuId());
        if (jdVatRate == null) {
            log.warn("sku skuId={}, jdSkuId={} jdVatRate is null", sku.getSkuId(), sku.getJdSkuId());
        }
        return jdVatRate;
    }

    /**
     * 计算商品历史税率退款比例
     * @param sku 商品信息对象，包含计算所需的相关属性
     * @return 商品的历史税率退款比例，以BigDecimal类型返回
     */
    private BigDecimal historyRaxRefundRate(InitAgreementPriceVO vo, SkuPO sku) {
        // 货源国
        String sourceCountryCode = vo.getSourceCountryCode();
        // 目标国
        String targetCountryCode = vo.getTargetCountryCode();
        // 末级后台类目ID
        Long catId = sku.getJdCatId();

        // 使用sku信息中的末级类目id + 货源国 + 目标国查询，如果查询不到数据，按货源国 + 目标国查询，如果还查不到按0处理
        TaxRefundRatePO taxRefundRate = taxRefundRateManageService.queryTwiceFindRate(sourceCountryCode, targetCountryCode, catId);

        if (taxRefundRate == null) {
            log.warn("历史实际退税成功率未配置. skuId={}, query={}", sku.getSkuId(), new Object[]{sourceCountryCode, targetCountryCode, catId});
            return null;
        }

        if (taxRefundRate.getTaxRefundRate() == null) {
            log.warn("历史实际退税成功率, 税率为空. skuId={}, query={}", sku.getSkuId(), new Object[]{sourceCountryCode, targetCountryCode, catId});
            return null;
        }

        return taxRefundRate.getTaxRefundRate();
    }

    /**
     * 获取进口关税信息
     */
    @PFTracing
    private CrossBorderImportTaxResDTO getImportTaxInfo(InitAgreementPriceVO vo, String currency,CurrencyPriceVO fulfillmentCostVO) {
        CrossBorderImportTaxReqVO taxReqDTO = createCrossBorderImportTaxReqDTO(vo, currency,fulfillmentCostVO);
        DataResponse<Map<Long, CrossBorderImportTaxResDTO>> response = countryTaxManageService.queryCrossBorderSkuImportTax(taxReqDTO);
        if (response == null || response.getData() == null) {
            return null;
        }
        return response.getData().get(vo.getSkuId());
    }

    /**
     * 创建跨境进口税请求DTO
     * @param vo InitAgreementPriceVO对象
     * @param currency 货币代码
     * @return CrossBorderImportTaxReqDTO对象
     */
    private CrossBorderImportTaxReqVO createCrossBorderImportTaxReqDTO(InitAgreementPriceVO vo,String currency,CurrencyPriceVO fulfillmentCostVO){
        CrossBorderImportTaxReqVO crossBorderImportTaxReqDTO = new CrossBorderImportTaxReqVO();
        crossBorderImportTaxReqDTO.setCnSkuIds(Collections.singleton(vo.getSkuId()));
        crossBorderImportTaxReqDTO.setCountryCode(vo.getTargetCountryCode());
        crossBorderImportTaxReqDTO.setCurrency(currency);
        crossBorderImportTaxReqDTO.setIsWareHouseProduct(vo.getIsWareHouseProduct());
        Map<Long, CurrencyPriceVO> fulfillmentFee = new HashMap<>();
        fulfillmentFee.put(vo.getSkuId(),fulfillmentCostVO);
        crossBorderImportTaxReqDTO.setFulfillmentFee(fulfillmentFee);
        return crossBorderImportTaxReqDTO;
    }

    @Override
    public void afterPropertiesSet() {

//        // 货源国
//        String sourceCountryCode = CountryConstant.COUNTRY_ZH;
//        // 目标国
//        String targetCountryCode = "BR";
//
//        InitAgreementPriceVO vo = new InitAgreementPriceVO();
//        vo.setSourceCountryCode(sourceCountryCode);
//        vo.setTargetCountryCode(targetCountryCode);
//
//        Long skuId = 80000000113L;
//
//        this.computeJdTaxRefundPrice(vo, new BigDecimal(1000), skuId);
    }
}