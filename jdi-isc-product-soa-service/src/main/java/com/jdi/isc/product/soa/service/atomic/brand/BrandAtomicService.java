package com.jdi.isc.product.soa.service.atomic.brand;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.brand.biz.BrandPageVO;
import com.jdi.isc.product.soa.domain.brand.biz.BrandReqVO;
import com.jdi.isc.product.soa.domain.brand.biz.BrandResVO;
import com.jdi.isc.product.soa.domain.brand.po.BrandPO;
import com.jdi.isc.product.soa.repository.mapper.brand.BrandBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 品牌
 */
@Service
@Slf4j
public class BrandAtomicService extends ServiceImpl<BrandBaseMapper, BrandPO> {


    public Map<Long, BrandPO> queryBrandPOByIds(Set<Long> brandIds,Integer status){
        LambdaQueryWrapper<BrandPO> wrapper = Wrappers.<BrandPO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(brandIds),BrandPO::getId, brandIds)
                .eq(status != null,BrandPO::getStatus, YnEnum.YES.getCode())
                .eq(BrandPO::getYn, YnEnum.YES.getCode());
        List<BrandPO> brandPOList = super.list(wrapper);
        if(CollectionUtils.isNotEmpty(brandPOList)){
            return brandPOList.stream().collect(Collectors.toMap(BrandPO::getId, Function.identity()));
        }
        return null;
    }

    /**
     * 列表分页查询
     * @param reqVO 搜索条件
     * @return 列表数据
     */
    public List<BrandResVO> listSearch(BrandReqVO reqVO){
        reqVO.setOffset((reqVO.getIndex()-1)*reqVO.getSize());
        return super.getBaseMapper().listSearch(reqVO);
    }

    /**
     * 列表分页查询总条数
     * @param reqVO 搜索条件
     * @return 列表数据
     */
    public long listSearchTotal(BrandReqVO reqVO){
        return super.getBaseMapper().listSearchTotal(reqVO);
    }


    public BrandPO queryBrandPOById(Long brandId){
        LambdaQueryWrapper<BrandPO> wrapper = Wrappers.<BrandPO>lambdaQuery()
                .eq(BrandPO::getId, brandId)
                .eq(BrandPO::getYn, YnEnum.YES.getCode());
        List<BrandPO> brandPOList = super.list(wrapper);
        if(CollectionUtils.isNotEmpty(brandPOList)){
            return brandPOList.get(0);
        }
        return null;
    }


    /**
     * 列表分页查询
     */
    public List<BrandPageVO.Response> globalBrandSearch(BrandPageVO.Request request){
        request.setOffset((request.getIndex()-1)*request.getSize());
        return super.getBaseMapper().globalBrandSearch(request);
    }

    /**
     * 列表分页查询总条数
     */
    public long globalBrandTotal(BrandPageVO.Request request){
        return super.getBaseMapper().globalBrandTotal(request);
    }
}
