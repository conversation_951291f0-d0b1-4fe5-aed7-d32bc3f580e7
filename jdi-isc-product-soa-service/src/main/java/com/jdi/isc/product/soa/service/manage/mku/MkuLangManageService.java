package com.jdi.isc.product.soa.service.manage.mku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.mku.req.MkuLangReqDTO;
import com.jdi.isc.product.soa.api.subtitle.biz.MkuSubtitleImportReqDTO;
import com.jdi.isc.product.soa.api.subtitle.biz.MkuSubtitleImportResDTO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuLangVO;

import java.util.Map;

public interface MkuLangManageService {

    DataResponse<String> updateSubtitle(MkuLangVO dto);


    DataResponse<String> saveMkuLang(MkuLangVO dto);

    MkuSubtitleImportResDTO importMkuSubtitle(MkuSubtitleImportReqDTO dto);

    /**
     * 更新mku多语言商品名称
     * @param mkuId
     * @param langNameMap key:语言code, value:该语言的商品名称
     * @return
     */
    DataResponse<String> updateMkuTitleLang(Long mkuId, Map<String, String> langNameMap);
}
