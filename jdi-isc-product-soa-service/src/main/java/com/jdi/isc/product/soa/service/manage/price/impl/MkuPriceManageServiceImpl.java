package com.jdi.isc.product.soa.service.manage.price.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.enums.CustomerSkuBrTaxEnum;
import com.jdi.isc.product.soa.api.common.enums.PriceAvailableSaleStatusEnum;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPricePO;
import com.jdi.isc.product.soa.domain.enums.price.SkuJdPriceTypeEnums;
import com.jdi.isc.product.soa.domain.enums.price.SkuSalePriceTypeEnums;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.biz.*;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.price.api.jdPrice.req.JdPriceReqDTO;
import com.jdi.isc.product.soa.price.api.jdPrice.res.JdPriceResDTO;
import com.jdi.isc.product.soa.price.api.salePrice.req.SalePriceReqDTO;
import com.jdi.isc.product.soa.price.api.salePrice.res.SalePriceResDTO;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.customerMku.CustomerMkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.MkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.jdPrice.SkuJdPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.salePrice.SkuSalePriceManageService;
import com.jdi.isc.product.soa.service.mapstruct.customer.CustomerConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/27 21:03
 */
@Service
@Slf4j
public class MkuPriceManageServiceImpl implements MkuPriceManageService {

    @Autowired
    @Lazy
    private CustomerMkuPriceManageService customerMkuPriceManageService;

    @Autowired
    private CustomerManageService customerManageService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private Map<String, SkuSalePriceManageService> skuSalePriceManageServiceMap;

    @Resource
    private Map<String, SkuJdPriceManageService> skuJdPriceManageServiceMap;

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @Resource
    private ExecutorService listMkuPriceExecutorService;

    @Override
    @PFTracing
    public DataResponse<List<MkuPriceResVO>> listMkuPrice(MkuPriceReqVO req) {
        List<CustomerMkuPricePO> customerMkuPricePOS;
        if(req.getNotCustomerPool()){
            List<MkuRelationPO> mkuRelationPOList = mkuRelationAtomicService.queryBindListByMkuIds(new ArrayList<>(req.getMkuIdList()));
            Map<Long,CustomerMkuPricePO> customerMkuPricePOMap = new HashMap<>();
            for (MkuRelationPO mkuRelationPO : mkuRelationPOList){
                if(customerMkuPricePOMap.containsKey(mkuRelationPO.getMkuId())){
                    CustomerMkuPricePO customerMkuPricePO = customerMkuPricePOMap.get(mkuRelationPO.getMkuId());
                    if(mkuRelationPO.getCreateTime().before(customerMkuPricePO.getCreateTime())){
                        customerMkuPricePO.setCreateTime(mkuRelationPO.getCreateTime());
                        customerMkuPricePO.setFixedSkuId(mkuRelationPO.getSkuId());
                        customerMkuPricePOMap.put(customerMkuPricePO.getMkuId(),customerMkuPricePO);
                    }
                }else {
                    CustomerMkuPricePO customerMkuPricePO = new CustomerMkuPricePO();
                    customerMkuPricePO.setMkuId(mkuRelationPO.getMkuId());
                    customerMkuPricePO.setCreateTime(mkuRelationPO.getCreateTime());
                    customerMkuPricePO.setFixedSkuId(mkuRelationPO.getSkuId());
                    customerMkuPricePO.setClientCode(req.getClientCode());
                    customerMkuPricePOMap.put(mkuRelationPO.getMkuId(),customerMkuPricePO);
                }
            }
            customerMkuPricePOS = new ArrayList<>(customerMkuPricePOMap.values());
        }else {
            // 获取当前客户站点下固定sku信息
            customerMkuPricePOS = customerMkuPriceManageService.getCustomerMkuFixedSkuListByMkuClient(req);
            if (CollectionUtils.isEmpty(customerMkuPricePOS)) {
                log.info("MkuPriceManageServiceImpl.listMkuPrice, customerMkuPricePOS is empty, fixSkuList={}", JSONObject.toJSONString(customerMkuPricePOS));
                return DataResponse.error("查询价格失败");
            }
            log.info("MkuPriceManageServiceImpl.listMkuPrice, customerMkuPricePOS={}", JSONObject.toJSONString(customerMkuPricePOS));
        }
        Set<Long> allFixSkuIds = customerMkuPricePOS.stream().map(CustomerMkuPricePO::getFixedSkuId).collect(Collectors.toSet());
        Map<Long, SkuPO> skuPOS = skuAtomicService.batchQuerySkuPO(allFixSkuIds);
        if (MapUtils.isEmpty(skuPOS)){
            log.info("MkuPriceManageServiceImpl.listMkuPrice, skuMap empty, skip. fixSkuList={}", JSONObject.toJSONString(allFixSkuIds));
            return DataResponse.error("查询价格失败");
        }
        Set<Long> usefulSkuIdSet = skuPOS.values().stream().map(SkuPO::getSkuId).collect(Collectors.toSet());

        // 过滤归档sku
        List<CustomerMkuPricePO> usefulFixSkuList = customerMkuPricePOS.stream().filter(line->usefulSkuIdSet.contains(line.getFixedSkuId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(usefulFixSkuList)){
            log.info("MkuPriceManageServiceImpl.listMkuPrice,usefulFixSkuList is null. usefulSkuIdSet={}", JSONObject.toJSONString(usefulSkuIdSet));
            return DataResponse.error("查询价格失败");
        }

        Map<String, CustomerVO> customerVOMap = new ConcurrentHashMap<>(customerManageService.map());

        List<Future<MkuPriceResVO>> futures = usefulFixSkuList.stream()
                .map(customerMkuPricePO -> listMkuPriceExecutorService.submit(() ->
                        this.getListMkuPrice(customerMkuPricePO, customerVOMap, req.getTargetCurrencyCode())))
                .collect(Collectors.toList());

        List<MkuPriceResVO> priceResult = new CopyOnWriteArrayList<>();
        for (Future<MkuPriceResVO> future : futures) {
            try {
                // 设置2秒超时
                MkuPriceResVO result = future.get(2, TimeUnit.SECONDS);
                if (result != null) {
                    priceResult.add(result);
                }
            } catch (Exception e) {
                log.error("【系统异常】listMkuPrice 异步取回数据异常",e);
                if (null != future) {
                    future.cancel(true);
                }
            }
        }

        log.info("MkuPriceManageServiceImpl.listMkuPrice, priceResult={}", JSONObject.toJSONString(priceResult));
        return DataResponse.success(priceResult);
    }

    @Override
    @PFTracing
    public DataResponse<List<MkuPriceResVO>> listMkuJdPrice(MkuPriceReqVO req) {
        // 获取当前客户站点下固定sku信息
        List<CustomerMkuPricePO> customerMkuPricePOS = customerMkuPriceManageService.getCustomerMkuFixedSkuListByMkuClient(req);
        if (CollectionUtils.isEmpty(customerMkuPricePOS)) {
            log.info("MkuPriceManageServiceImpl.listMkuJdPrice, customerMkuPricePOS is empty, fixSkuList={}", JSONObject.toJSONString(customerMkuPricePOS));
            return DataResponse.error("查询价格失败");
        }
        log.info("MkuPriceManageServiceImpl.listMkuJdPrice, customerMkuPricePOS={}", JSONObject.toJSONString(customerMkuPricePOS));

        Set<Long> allFixSkuIds = customerMkuPricePOS.stream().map(CustomerMkuPricePO::getFixedSkuId).collect(Collectors.toSet());
        Map<Long, SkuPO> skuPOS = skuAtomicService.batchQuerySkuPO(allFixSkuIds);
        if (MapUtils.isEmpty(skuPOS)){
            log.info("MkuPriceManageServiceImpl.listMkuJdPrice, skuMap empty, skip. fixSkuList={}", JSONObject.toJSONString(allFixSkuIds));
            return DataResponse.error("查询价格失败");
        }
        Set<Long> usefulSkuIdSet = skuPOS.values().stream().map(SkuPO::getSkuId).collect(Collectors.toSet());

        // 过滤归档sku
        List<CustomerMkuPricePO> usefulFixSkuList = customerMkuPricePOS.stream().filter(line->usefulSkuIdSet.contains(line.getFixedSkuId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(usefulFixSkuList)){
            log.info("MkuPriceManageServiceImpl.listMkuJdPrice,usefulFixSkuList is null. usefulSkuIdSet={}", JSONObject.toJSONString(usefulSkuIdSet));
            return DataResponse.error("查询价格失败");
        }

        Map<String, CustomerVO> customerVOMap = new ConcurrentHashMap<>(customerManageService.map());

        List<Future<MkuPriceResVO>> futures = usefulFixSkuList.stream()
                .map(customerMkuPricePO -> listMkuPriceExecutorService.submit(() ->
                        this.getListMkuJdPrice(customerMkuPricePO, customerVOMap)))
                .collect(Collectors.toList());

        List<MkuPriceResVO> priceResult = new CopyOnWriteArrayList<>();
        for (Future<MkuPriceResVO> future : futures) {
            try {
                // 设置2秒超时
                MkuPriceResVO result = future.get(2, TimeUnit.SECONDS);
                if (result != null) {
                    priceResult.add(result);
                }
            } catch (Exception e) {
                log.error("【系统异常】listMkuJdPrice 异步取回数据异常",e);
                if (null != future) {
                    future.cancel(true);
                }
            }
        }
        log.info("MkuPriceManageServiceImpl.listMkuJdPrice, priceResult={}", JSONObject.toJSONString(priceResult));
        return DataResponse.success(priceResult);
    }



    /**
     * 填充税率列表
     * @param price MkuPriceResVO对象，用于存储填充后的税率列表
     * @param salePriceResDTO SalePriceCalculateResVO对象，包含计算出的税率信息
     */
    private void fillTaxRateList(MkuPriceResVO price, SalePriceResDTO salePriceResDTO) {
        Map<String,Object> salePriceBrRes = salePriceResDTO.getSalePriceTaxRes();
        if (MapUtils.isNotEmpty(salePriceBrRes)) {
            List<TaxRateResp> taxRateList = new ArrayList<>();
            TaxRateResp taxRateRespIPI = new TaxRateResp();
            Object ipi = salePriceBrRes.get(CustomerSkuBrTaxEnum.IPI.name());
            Object ipiAmount = salePriceBrRes.get(CustomerSkuBrTaxEnum.IPI_AMOUNT.name());
            taxRateRespIPI.setTaxCode(CustomerSkuBrTaxEnum.IPI.name());
            taxRateRespIPI.setTaxRate(ipi != null?(BigDecimal)ipi:null);
            taxRateRespIPI.setTaxValue(ipiAmount != null?(BigDecimal)ipiAmount:null);

            TaxRateResp taxRateRespPIS = new TaxRateResp();
            Object pis = salePriceBrRes.get(CustomerSkuBrTaxEnum.PIS.name());
            Object pisAmount = salePriceBrRes.get(CustomerSkuBrTaxEnum.PIS_AMOUNT.name());
            taxRateRespPIS.setTaxCode(CustomerSkuBrTaxEnum.PIS.name());
            taxRateRespPIS.setTaxRate(pis != null?(BigDecimal)pis:null);
            taxRateRespPIS.setTaxValue(pisAmount != null?(BigDecimal)pisAmount:null);

            TaxRateResp taxRateRespCOFINS = new TaxRateResp();
            Object cofins = salePriceBrRes.get(CustomerSkuBrTaxEnum.COFINS.name());
            Object cofinsAmount = salePriceBrRes.get(CustomerSkuBrTaxEnum.COFINS_AMOUNT.name());
            taxRateRespCOFINS.setTaxCode(CustomerSkuBrTaxEnum.COFINS.name());
            taxRateRespCOFINS.setTaxRate(cofins != null?(BigDecimal)cofins:null);
            taxRateRespCOFINS.setTaxValue(cofinsAmount != null?(BigDecimal)cofinsAmount:null);

            TaxRateResp taxRateRespICMS = new TaxRateResp();
            Object icms = salePriceBrRes.get(CustomerSkuBrTaxEnum.ICMS.name());
            Object icmsAmount = salePriceBrRes.get(CustomerSkuBrTaxEnum.ICMS_AMOUNT.name());
            taxRateRespICMS.setTaxCode(CustomerSkuBrTaxEnum.ICMS.name());
            taxRateRespICMS.setTaxRate(icms != null?(BigDecimal)icms:null);
            taxRateRespICMS.setTaxValue(icmsAmount != null?(BigDecimal)icmsAmount:null);

            TaxRateResp taxRateRespGROSS_UP_PRICE_INDEX = new TaxRateResp();
            taxRateRespGROSS_UP_PRICE_INDEX.setTaxCode(CustomerSkuBrTaxEnum.GROSS_UP_PRICE_INDEX.name());
            Object grossUp = salePriceBrRes.get(CustomerSkuBrTaxEnum.GROSS_UP_PRICE_INDEX.name());
            taxRateRespGROSS_UP_PRICE_INDEX.setTaxRate(grossUp != null?(BigDecimal)grossUp:null);

            TaxRateResp taxRateRespPER_UNIT_PRICE = new TaxRateResp();
            taxRateRespPER_UNIT_PRICE.setTaxCode(CustomerSkuBrTaxEnum.PER_UNIT_PRICE.name());
            Object perUnit = salePriceBrRes.get(CustomerSkuBrTaxEnum.PER_UNIT_PRICE.name());
            taxRateRespPER_UNIT_PRICE.setTaxValue(perUnit != null?(BigDecimal)perUnit:null);

            TaxRateResp unilizationResp = new TaxRateResp();
            unilizationResp.setTaxCode(CustomerSkuBrTaxEnum.UTILIZATION.name());
            Object unilization = salePriceBrRes.get(CustomerSkuBrTaxEnum.UTILIZATION.name());
            unilizationResp.setRateType(unilization != null?(String) unilization:null);

            taxRateList.add(taxRateRespIPI);
            taxRateList.add(taxRateRespPIS);
            taxRateList.add(taxRateRespCOFINS);
            taxRateList.add(taxRateRespICMS);
            taxRateList.add(taxRateRespGROSS_UP_PRICE_INDEX);
            taxRateList.add(taxRateRespPER_UNIT_PRICE);
            taxRateList.add(unilizationResp);
            price.setTaxRateList(taxRateList);
        }
    }


    @Override
    public DataResponse<MkuCalculateTaxSalePriceResVO> salePriceCalculateTaxSalePrice(MkuCalculateTaxSalePriceReqVO req) {
        Map<String,CustomerVO> customerVOMap = customerManageService.map();
        CustomerVO customerVO = customerVOMap.get(req.getClientCode());
        req.setCustomerVO(customerVO);
        if(StringUtils.isBlank(req.getTargetCurrencyCode())){
            req.setTargetCurrencyCode(customerVO.getSaleCurrency());
        }
        MkuCalculateTaxSalePriceResVO mkuCalculateTaxSalePriceResVO = skuSalePriceManageServiceMap.get(SkuSalePriceTypeEnums.getEnumByCountryCode(customerVO.getCountry()).getServiceName()).calculateTaxSalePrice(req);
        return DataResponse.success(mkuCalculateTaxSalePriceResVO);
    }

    private MkuPriceResVO getListMkuPrice(CustomerMkuPricePO customerMkuPricePO,Map<String,CustomerVO> customerVOMap,String targetCurrencyCode){
        CustomerVO customerVO = customerVOMap.get(customerMkuPricePO.getClientCode());
        SalePriceReqDTO salePriceReqDTO = new SalePriceReqDTO();
        salePriceReqDTO.setSkuId(customerMkuPricePO.getFixedSkuId());
        salePriceReqDTO.setClientCode(customerMkuPricePO.getClientCode());
        salePriceReqDTO.setCustomerDTO(CustomerConvert.INSTANCE.customerVo2Dto(customerVO));
        if(StringUtils.isNotBlank(targetCurrencyCode)){
            salePriceReqDTO.setCurrencyCode(targetCurrencyCode);
        }else {
            salePriceReqDTO.setCurrencyCode(customerVO.getSaleCurrency());
        }
        SalePriceResDTO salePriceResDTO = skuSalePriceManageServiceMap.get(SkuSalePriceTypeEnums.getEnumByCountryCode(customerVO.getCountry()).getServiceName())
                .getSalePrice(salePriceReqDTO);
        MkuPriceResVO target = new MkuPriceResVO();
        target.setMkuId(customerMkuPricePO.getMkuId());
        target.setSkuId(customerMkuPricePO.getFixedSkuId());
        if(salePriceResDTO != null){
            target.setSalePrice(salePriceResDTO.getSalePrice());
            target.setCurrency(salePriceResDTO.getTargetCurrencyCode());
            target.setTaxPrice(salePriceResDTO.getTaxPrice());
            target.setValueAddedTax(salePriceResDTO.getValueAddTax());
            target.setIncludeTaxPrice(salePriceResDTO.getTaxSalePrice());
            // 设置可售状态
            target.setAvailableSaleStatus(salePriceResDTO.getAvailableSaleStatus());
            // 补充税率列表
            this.fillTaxRateList(target, salePriceResDTO);
        }

        return target;
    }

    private MkuPriceResVO getListMkuJdPrice(CustomerMkuPricePO customerMkuPricePO,Map<String,CustomerVO> customerVOMap){
        CustomerVO customerVO = customerVOMap.get(customerMkuPricePO.getClientCode());
        JdPriceReqDTO jdPriceReqDTO = new JdPriceReqDTO();
        jdPriceReqDTO.setSkuId(customerMkuPricePO.getFixedSkuId());
        jdPriceReqDTO.setClientCode(customerMkuPricePO.getClientCode());
        jdPriceReqDTO.setCustomerDTO(CustomerConvert.INSTANCE.customerVo2Dto(customerVO));
        jdPriceReqDTO.setCurrencyCode(customerVO.getSaleCurrency());
        JdPriceResDTO jdPriceResDTO = skuJdPriceManageServiceMap.get(SkuJdPriceTypeEnums.getEnumByCountryCode(customerVO.getCountry()).getServiceName())
                .getJdPrice(jdPriceReqDTO);
        MkuPriceResVO target = new MkuPriceResVO();
        target.setMkuId(customerMkuPricePO.getMkuId());
        target.setSkuId(customerMkuPricePO.getFixedSkuId());
        if(jdPriceResDTO != null){
            target.setSalePrice(jdPriceResDTO.getJdPrice());
            target.setCurrency(jdPriceResDTO.getTargetCurrencyCode());
            target.setTaxPrice(jdPriceResDTO.getTaxPrice());
            target.setValueAddedTax(jdPriceResDTO.getValueAddTax());
            target.setIncludeTaxPrice(jdPriceResDTO.getTaxJdPrice());
            target.setAvailableSaleStatus(PriceAvailableSaleStatusEnum.ENABLE.getCode());
        }
        return target;
    }
}
