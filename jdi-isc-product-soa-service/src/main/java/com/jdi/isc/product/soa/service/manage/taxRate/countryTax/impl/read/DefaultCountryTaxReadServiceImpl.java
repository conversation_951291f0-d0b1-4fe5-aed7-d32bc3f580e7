package com.jdi.isc.product.soa.service.manage.taxRate.countryTax.impl.read;

import com.google.common.collect.Maps;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.CurrencyConstant;
import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxResVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.IscSkuImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.BrSkuTaxPO;
import com.jdi.isc.product.soa.price.api.price.res.CrossBorderImportTaxResDTO;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.BaseTaxReadService;
import com.jdi.isc.product.soa.service.support.TaxCalculateExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Map;

/**
 * 默认国家的关税获取策略
 * <AUTHOR>
 * @date 2025/3/10
 */
@Slf4j
@Service
@TaxCalculateExecutor(countryCode = CountryConstant.COUNTRY_DEFAULT)
public class DefaultCountryTaxReadServiceImpl implements BaseTaxReadService {

    @Override
    public Map<Long, CrossBorderImportTaxResDTO> queryCrossBorderSkuImportTax(CrossBorderImportTaxReqVO input) {
        //默认国家关税缺省为0
        Map<Long, CrossBorderImportTaxResDTO> res = Maps.newHashMapWithExpectedSize(input.getCnSkuIds().size());
        for(Long skuId : input.getCnSkuIds()){
            res.put(skuId,new CrossBorderImportTaxResDTO(skuId, BigDecimal.ZERO, CurrencyConstant.CURRENCY_ZH,input.getCountryCode(),"",BigDecimal.ZERO,BigDecimal.ZERO));
        }
        return res;
    }

    @Override
    public Map<Long, CrossBorderImportTaxResVO> queryImportTaxByIscSkuIds(IscSkuImportTaxReqVO input) {
        return Collections.emptyMap();
    }
}
