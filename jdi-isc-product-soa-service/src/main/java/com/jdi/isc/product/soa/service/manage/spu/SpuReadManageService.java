package com.jdi.isc.product.soa.service.manage.spu;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.spu.req.SupplierSpuIdVerifyDTO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.SpuLangPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.validation.ValidateSpuGroup;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * spu读接口
 *
 * <AUTHOR>
 * @date 2023/11/29
 **/
public interface SpuReadManageService {

    /**
     * 根据条件查询spu列表
     *
     * @param spuQueryReqVO 查询过滤条件对象
     * @return 返回spu列表
     */
    PageInfo<SpuVO> spuPage(SpuQueryReqVO spuQueryReqVO);

    /**
     * 根据给定的SPU ID列表获取对应的SPU VO列表。
     * @param spuIds 需要查询的SPU ID列表。
     * @return 对应的SPU VO列表。
     */
    List<SpuVO> getSpuList(List<Long> spuIds);

    /**
     * 查询创建商品所需信息
     *
     * @param lastCatId      末级类目ID
     * @param attributeScope 属性范围 VN CN等
     * @return
     */
    SpuPrepareInfoVO getPrepareVo(Long lastCatId, String attributeScope, String sourceCountryCode,Integer isExport,String vendorCode);

    /**
     * 根据spuId查询商品详情
     *
     * @param spuId spuId
     * @return
     */
    @Validated(ValidateSpuGroup.querySpu.class)
    SpuDetailVO getDetailBySpuId(Long spuId);

    /**
     * 根据spuId查询商品详情,不查草稿
     *
     * @param spuId
     * @return
     */
    SpuDetailVO getSpuDetailVoFromDb(Long spuId);

    /**
     * 根据商品类型查询供应商、属性范围
     *
     * @param spuType 1:跨境 2:本土
     * @return 返回发品所需基本信息
     */
    SpuBaseInfoVO getSpuBaseInfoVO(Integer spuType);

    /**
     * 获取装吧商详
     *
     * @param jdSkuId
     * @return
     */
    String getZbDescription(Long jdSkuId);

    /**
     * 获取装吧商详图片
     *
     * @param jdSkuId
     * @return
     */
    Map<Long, List<String>> getZbImageByJdSkuId(Long jdSkuId);

    /**
     * 销售单位列表
     *
     * @param lang 语言
     * @return 销售单位列表
     */
    List<SaleUnitVO> saleUnitList(String lang);

    /**
     * 获取SPU详细信息
     * @param reqVO SPU详细信息请求对象
     * @return SPU详细信息返回对象
     */
    @Validated(ValidateSpuGroup.querySpu.class)
    SpuDetailVO getSpuDetail(SpuDetailReqVO reqVO);


    /**
     * @function 根据商品SPU ID列表批量查询商品修订信息
     * @param spuIds 商品SPU ID的列表
     * @returns 返回一个SpuAmendVO列表，包含了指定SPU ID的商品修订信息
     */
    List<SpuAmendVO> batchQuerySpuAmendVOListBySpuIds(List<Long> spuIds);


    /**
     * 根据给定的SPU IDs、目标国家代码列表和检查类型，获取SPU修改信息列表。
     * @param spuIds SPU的ID列表
     * @param targetCountryCodeList 目标国家的代码列表
     * @param checkTypeList 检查类型列表
     * @return SPU修改信息的列表
     */
    List<SpuAmendVO> getSpuAmendVOList(List<Long> spuIds,List<String> targetCountryCodeList,List<Integer> checkTypeList);


    /**
     * 获取指定国家的审核状态数量
     * @param sourceCountryCode 国家代码
     * @return 审核状态数量列表
     */
    List<AuditNumVO> auditStatusNum(SpuPrepareReqVO spuPrepareReqVO);

    /**
     * 根据请求获取SPU详情
     * @param reqVO SPU详情请求对象
     * @return SPU详情响应对象
     */
    @Validated(ValidateSpuGroup.querySpu.class)
    SpuDetailVO getSpuDetailVOFromDb(SpuDetailReqVO reqVO);

    /**
     * 根据提供的SPU ID集合获取对应的SPU PO对象。
     * @param spuIds SPU ID集合
     * @return 包含SPU PO对象的Map，键为SPU ID，值为对应的SPU PO对象
     */
    Map<Long, SpuPO> listSpuPo(Set<Long> spuIds);

    /**
     * 根据指定的商品ID集合和语言集合查询商品语言信息。
     * @param spuIds 商品ID集合
     * @param langSet 语言集合
     * @return 商品ID与对应语言信息的映射关系
     */
    Map<Long, Map<String,SpuLangPO>> querySpuLangMap(Set<Long> spuIds, Set<String> langSet);

    /**
     * 验证供应商SPU ID是否有效
     * @param verifyDTO 包含待验证的供应商SPU ID信息的DTO对象
     * @return 验证结果，true表示有效，false表示无效
     */
    DataResponse<Boolean> verifySupplierSpuId(SupplierSpuIdVerifyDTO verifyDTO);
}
