package com.jdi.isc.product.soa.service.manage.spu.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.sku.biz.SkuLogSaveVO;
import com.jdi.isc.product.soa.domain.spu.biz.SaveSpuVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangExtendVO;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuLangPO;
import com.jdi.isc.product.soa.service.atomic.sku.SkuLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuLangAtomicService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jdi.isc.product.soa.domain.spu.biz.SpuVO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.service.manage.spu.SpuLangManageService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class SpuLangManageServiceImpl extends BaseManageSupportService<SpuVO, SpuPO> implements SpuLangManageService {

    @Autowired
    private SpuLangAtomicService spuLangAtomicService;

    @Autowired
    private SpuDraftAtomicService spuDraftAtomicService;
    
    @Resource
    private SkuLogAtomicService skuLogAtomicService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataResponse<String> updateSpuTitleLang(Long spuId, Map<String, String> langNameMap) {
        log.info("SpuLangManageServiceImpl.updateSpuTitleLang spuId:{}, langNameMap:{}", spuId, langNameMap);
        
        // 参数校验
        if (spuId == null) {
            log.info("SpuLangManageServiceImpl.updateSpuTitleLang spuId不能为空");
            throw new BizException("spuId不能为空");
        }
        
        if (langNameMap == null || langNameMap.isEmpty()) {
            log.info("SpuLangManageServiceImpl.updateSpuTitleLang 多语言名称映射不能为空");
            throw new BizException("多语言名称映射不能为空");
        }
        
        try {
            // 获取当前SPU的所有多语言信息
            Map<String, SpuLangPO> currentLangMap = spuLangAtomicService.langTitleMap(spuId);
            
            // 获取SPU草稿信息
            SpuDraftPO spuDraftPO = spuDraftAtomicService.getOneBySpuId(spuId);
            
            boolean hasChanges = false;
            Map<String, String> newLangNameMap = new HashMap<>();
            // 处理每种语言的名称更新
            for (Map.Entry<String, String> entry : langNameMap.entrySet()) {
                String lang = entry.getKey();
                String newTitle = entry.getValue();
                
                if (StringUtils.isBlank(lang) || StringUtils.isBlank(newTitle)) {
                    log.info("SpuLangManageServiceImpl.updateSpuTitleLang 语言{}的标题为空，跳过更新, spuId:{}", lang, spuId);
                    continue;
                }
                newLangNameMap.put(lang, newTitle);
                // 检查是否有变化
                SpuLangPO currentLangPO = currentLangMap.get(lang);
                if (currentLangPO != null && StringUtils.equals(currentLangPO.getSpuTitle(), newTitle)) {
                    log.info("SpuLangManageServiceImpl.updateSpuTitleLang 语言{}的标题没有变化，跳过更新, spuId:{}", lang, spuId);
                    continue;
                }
                
                hasChanges = true;
                
                // 更新SPU多语言表
                updateSpuLangTitle(spuId, lang, newTitle, currentLangPO);
            }

            // 更新SPU草稿表的JSON信息
            if (spuDraftPO != null) {
                updateSpuDraftJson(spuDraftPO, newLangNameMap);
            }
            
            if (!hasChanges) {
                log.info("SpuLangManageServiceImpl.updateSpuTitleLang 没有需要更新的内容, spuId:{}", spuId);
                return DataResponse.error("没有需要更新的内容");
            }
            
            log.info("SpuLangManageServiceImpl.updateSpuTitleLang SPU多语言标题更新成功, spuId:{}", spuId);
            return DataResponse.success("SPU多语言标题更新成功");
            
        } catch (Exception e) {
            log.error("SpuLangManageServiceImpl.updateSpuTitleLang 更新SPU多语言标题失败，spuId:{}, langNameMap:{}", spuId, JSON.toJSONString(langNameMap), e);
            throw e;
        }
    }
    
    /**
     * 更新SPU多语言标题
     */
    private void updateSpuLangTitle(Long spuId, String lang, String newTitle, SpuLangPO currentLangPO) {
        String sourceJsonString = null;
        SpuLangPO targetPO = new SpuLangPO();
        
        if (currentLangPO != null) {
            // 更新现有记录
            sourceJsonString = JSONObject.toJSONString(currentLangPO);
            targetPO.setId(currentLangPO.getId());
            targetPO.setSpuTitle(newTitle);
            if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                targetPO.setUpdater(Constant.SYSTEM);
            }
            targetPO.setUpdateTime(new Date());
            
            spuLangAtomicService.updateById(targetPO);
            
            // 合并数据用于日志记录
            targetPO.setSpuId(spuId);
            targetPO.setLang(lang);
            
        } else {
            // 新增记录
            targetPO.setSpuId(spuId);
            targetPO.setLang(lang);
            targetPO.setSpuTitle(newTitle);
            if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                targetPO.setCreator(Constant.SYSTEM);
                targetPO.setUpdater(Constant.SYSTEM);
            }
            targetPO.setCreateTime(new Date());
            targetPO.setUpdateTime(new Date());
            targetPO.setYn(YnEnum.YES.getCode());
            
            spuLangAtomicService.save(targetPO);
            
            sourceJsonString = "{}";
        }
        
        // 记录SPU多语言变更日志
        recordSpuLangLog(spuId, sourceJsonString, targetPO);
    }
    
    /**
     * 更新SPU草稿JSON信息
     */
    private void updateSpuDraftJson(SpuDraftPO spuDraftPO, Map<String, String> newLangNameMap) {
        try {
            SaveSpuVO saveSpuVO = new SaveSpuVO();
            // 如果SPU草稿JSON信息不为空且合法，则解析为SaveSpuVO对象
            if (StringUtils.isNotBlank(spuDraftPO.getSpuJsonInfo()) && JSON.isValid(spuDraftPO.getSpuJsonInfo())) {
                saveSpuVO = JSON.parseObject(spuDraftPO.getSpuJsonInfo(), SaveSpuVO.class);
            }
            
            if (saveSpuVO.getSpuVO() == null) {
                saveSpuVO.setSpuVO(new SpuVO());
            }
            SpuVO spuVO = saveSpuVO.getSpuVO();
            if (spuVO.getSpuLangExtendVOList() == null) {
                spuVO.setSpuLangExtendVOList(new ArrayList<>());
            }
            String sourceJsonString = JSON.toJSONString(spuVO.getSpuLangExtendVOList());
            // 更新对应语言的标题字段
            for (Map.Entry<String, String> entry : newLangNameMap.entrySet()) {
                String lang = entry.getKey();
                String newTitle = entry.getValue();
                switch (lang) {
                    case LangConstant.LANG_ZH:
                        spuVO.setSpuZhName(newTitle);
                        break;
                    case LangConstant.LANG_EN:
                        spuVO.setSpuEnName(newTitle);
                        break;
                    case LangConstant.LANG_VN:
                        spuVO.setSpuVnName(newTitle);
                        break;
                    case LangConstant.LANG_TH:
                        spuVO.setSpuThName(newTitle);
                        break;
                    default:
                        break;
                }
                // 更新spuLangExtendVOList
                updateSpuLangExtendList(spuVO, lang, newTitle);
            }
            List<SpuLangExtendVO> spuLangExtendVOList = spuVO.getSpuLangExtendVOList();
            // 如果spuLangExtendVOList不为空，则更新草稿表
            if (spuLangExtendVOList != null && !spuLangExtendVOList.isEmpty()) {
                SpuDraftPO newSpuDraftPO = new SpuDraftPO();
                newSpuDraftPO.setId(spuDraftPO.getId());
                newSpuDraftPO.setSpuJsonInfo(JSON.toJSONString(saveSpuVO));
                newSpuDraftPO.setUpdateTime(new Date());
                if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                    newSpuDraftPO.setUpdater(Constant.SYSTEM);
                }
                // 更新草稿表
                spuDraftAtomicService.updateById(newSpuDraftPO);

                // 记录SPU草稿变更日志
                recordSpuDraftLangLog(spuDraftPO.getSpuId(), sourceJsonString, JSON.toJSONString(spuLangExtendVOList), newSpuDraftPO.getUpdater());

                log.info("SpuLangManageServiceImpl.updateSpuDraftJson SPU草稿JSON信息更新成功, spuId:{}", spuDraftPO.getSpuId());
            }
           
            
        } catch (Exception e) {
            log.error("SpuLangManageServiceImpl.updateSpuDraftJson 更新SPU草稿JSON信息异常, spuId:{}", spuDraftPO.getSpuId(), e);
            throw e;
        }
    }
    
    /**
     * 更新spuLangExtendVOList中的语言信息
     */
    private void updateSpuLangExtendList(SpuVO spuVO, String lang, String newTitle) {
        List<SpuLangExtendVO> spuLangExtendVOList = spuVO.getSpuLangExtendVOList();
        // 查找是否已存在该语言的记录
        boolean found = false;
        for (SpuLangExtendVO langExtendVO : spuLangExtendVOList) {
            if (StringUtils.equalsIgnoreCase(langExtendVO.getLang(), lang)) {
                langExtendVO.setSpuTitle(newTitle);
                found = true;
                break;
            }
        }
        
        // 如果不存在，添加新的语言记录
        if (!found) {
            SpuLangExtendVO newLangExtendVO = new SpuLangExtendVO();
            newLangExtendVO.setLang(lang);
            newLangExtendVO.setSpuTitle(newTitle);
            spuLangExtendVOList.add(newLangExtendVO);
        }
    }
    
    /**
     * 记录SPU多语言变更日志
     */
    private void recordSpuLangLog(Long spuId, String sourceJsonString, SpuLangPO targetData) {
        try {
            SkuLogSaveVO logSaveVO = new SkuLogSaveVO();
            logSaveVO.setKeyType(KeyTypeEnum.SPU_LANG.getCode());
            logSaveVO.setKeyId(spuId.toString());
            logSaveVO.setSourceJson(sourceJsonString);
            logSaveVO.setTargetJson(JSONObject.toJSONString(targetData));
            logSaveVO.setUpdater(targetData.getUpdater());
            logSaveVO.setMark("SPU多语言名称修改");
            
            skuLogAtomicService.saveLog(logSaveVO);
            log.info("SpuLangManageServiceImpl.recordSpuLangLog SPU多语言变更日志保存成功，spuId: {}", spuId);
        } catch (Exception e) {
            log.error("SpuLangManageServiceImpl.recordSpuLangLog SPU多语言变更日志保存异常，spuId: {}", spuId, e);
        }
    }
    
    /**
     * 记录SPU草稿多语言变更日志
     */
    private void recordSpuDraftLangLog(Long spuId, String sourceJsonString, String targetJsonString, String updater) {
        try {
            SkuLogSaveVO logSaveVO = new SkuLogSaveVO();
            logSaveVO.setKeyType(KeyTypeEnum.SPU_DRFAT_LANG.getCode());
            logSaveVO.setKeyId(spuId.toString());
            logSaveVO.setSourceJson(sourceJsonString);
            logSaveVO.setTargetJson(targetJsonString);
            logSaveVO.setUpdater(updater);
            logSaveVO.setMark("SPU草稿多语言名称修改");
            
            skuLogAtomicService.saveLog(logSaveVO);
            log.info("SpuLangManageServiceImpl.recordSpuDraftLangLog SPU草稿多语言变更日志保存成功，spuId: {}", spuId);
        } catch (Exception e) {
            log.error("SpuLangManageServiceImpl.recordSpuDraftLangLog SPU草稿多语言变更日志保存异常，spuId: {}", spuId, e);
        }
    }

}
