package com.jdi.isc.product.soa.service.manage.customerSku.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jdi.isc.product.soa.api.common.enums.PriceAvailableSaleStatusEnum;
import com.jdi.isc.product.soa.common.constants.msgCode.ProductMessageEnum;
import com.jdi.isc.product.soa.common.util.StringUtils;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailPO;
import com.jdi.isc.product.soa.domain.price.biz.MkuPriceAvailableVO;
import com.jdi.isc.product.soa.rpc.suport.AlertHelper;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailAtomicService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceDetailManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: sku客制化价格草稿明细表数据维护服务实现
 * @Author: zhaokun51
 * @Date: 2025/02/27 14:53
 **/

@Slf4j
@Service
public class CustomerSkuPriceDetailManageServiceImpl implements CustomerSkuPriceDetailManageService {

    @Resource
    private CustomerSkuPriceDetailAtomicService customerSkuPriceDetailAtomicService;


    @Override
    public List<MkuPriceAvailableVO> listMkuPriceAvailableStatus(String clientCode, Collection<Long> skuIds, Map<Long, Long> mkuFixedSkuMap) {

        if (StringUtils.isEmpty(clientCode) || CollectionUtils.isEmpty(skuIds) || MapUtils.isEmpty(mkuFixedSkuMap)) {
            log.info("listMkuPriceAvailableStatus param is null, clientCode: {}, skuIds: {}", clientCode, skuIds);
            return Collections.emptyList();
        }

        Map<Long, CustomerSkuPriceDetailPO> customerPriceDetailMap = customerSkuPriceDetailAtomicService.mapByClientCodeAndSkuId(clientCode, skuIds);

        if (MapUtils.isEmpty(customerPriceDetailMap)) {
           return Collections.emptyList();
        }

        // 逆转mku和sku的关系
        Map<Long, Set<Long>> skuIdMkuIdMap = mkuFixedSkuMap.entrySet().stream().collect(Collectors.groupingBy(Map.Entry::getValue, Collectors.mapping(Map.Entry::getKey, Collectors.toSet())));

        List<MkuPriceAvailableVO> result = Lists.newArrayList();

        customerPriceDetailMap.forEach((k, v) -> {
            if (v == null) {
                log.info("无客制化价格. clientCode: {}, skuId: {}", clientCode, k);
                return;
            }
            if (v.getAvailableSaleStatus() == null) {
                AlertHelper.p0("可售状态未设置.", clientCode, String.valueOf(k));
                v.setAvailableSaleStatus(PriceAvailableSaleStatusEnum.ENABLE.getCode());
            }
            MkuPriceAvailableVO item = new MkuPriceAvailableVO(v.getSkuId(), v.getAvailableSaleStatus());
            if (!PriceAvailableSaleStatusEnum.isAvailable(v.getAvailableSaleStatus())) {
                log.info("客制化价格不满足可售条件. price={}", JSONObject.toJSONString(v));
                item.setUnAvailableSaleMessage(ProductMessageEnum.MCT50004.getMsg());
                item.setUnAvailableSaleCode(ProductMessageEnum.MCT50004.getCode());
            }

            Set<Long> mkuIds = skuIdMkuIdMap.get(item.getSkuId());

            if (CollectionUtils.isEmpty(mkuIds)) {
                log.warn("根据固定的skuId和mkuId的关系找不到mkuId, skuId={}, skuIdMkuIdMap={}", v.getSkuId(), skuIdMkuIdMap);
                AlertHelper.p0("根据固定的skuId和mkuId的关系找不到mkuId", String.valueOf(v.getSkuId()));
                return;
            }

            if (mkuIds.size() > 1) {
                log.warn("根据固定的skuId和mkuId的关系找到多个mkuId, skuId={}, mkuIds={}", v.getSkuId(), mkuIds);
                AlertHelper.p0("根据固定的skuId和mkuId的关系找到多个mkuId", String.valueOf(v.getSkuId()));
                return;
            }

            for (Long mkuId : mkuIds) {
                MkuPriceAvailableVO mkuPriceAvailable = BeanUtil.copyProperties(item, MkuPriceAvailableVO.class);
                mkuPriceAvailable.setMkuId(mkuId);
                result.add(mkuPriceAvailable);
            }
        });

        return result;
    }
}
