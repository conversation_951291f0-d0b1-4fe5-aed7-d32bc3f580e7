package com.jdi.isc.product.soa.service.manage.spu;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.spu.req.BatchProductReqDTO;
import com.jdi.isc.product.soa.api.spu.req.SpuUpdateApiDTO;
import com.jdi.isc.product.soa.api.spu.req.SpuUpdatePriceReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.validation.ValidateSpuGroup;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/11
 **/
public interface SpuManageService {

    /**
     * 创建商品
     *
     * @param saveSpuVO
     * @return
     */
    @Validated(ValidateSpuGroup.createProduct.class)
    DataResponse<Long> create(SaveSpuVO saveSpuVO);

    /**
     * 创建商品
     *
     * @param saveSpuVO
     * @return
     */
    @Validated(ValidateSpuGroup.updateProduct.class)
    DataResponse<Long> update(SaveSpuVO saveSpuVO);

    /**
     * 批量审核
     *
     * @param spuAuditRecordReqVO 请求对象
     * @return
     */
    @Validated(ValidateSpuGroup.approve.class)
    DataResponse<String> batchApprove(SpuAuditRecordUpdateVO spuAuditRecordReqVO);

    /**
     * 批量驳回
     *
     * @param spuAuditRecordReqVO 审核对象
     * @return
     */
    @Validated(ValidateSpuGroup.reject.class)
    DataResponse<String> batchReject(SpuAuditRecordUpdateVO spuAuditRecordReqVO);

    /**
     * 批量申请修改
     * @param spuAmendReqVOList 修改对象集合
     * @return
     */
    @Validated(ValidateSpuGroup.reject.class)
    DataResponse<String> batchAmend(List<SpuAmendReqVO> spuAmendReqVOList);

    /**
     * 保存草稿
     *
     * @param saveSpuVO 商品信息
     * @return 商品ID
     */
    DataResponse<Long> saveDraft(SaveSpuVO saveSpuVO);

    /**
     * 提交商品
     *
     * @param saveSpuVO 商品信息
     * @return 商品id
     */
    DataResponse<Long> submit(SaveSpuVO saveSpuVO);

    /**
     * 批量下架商品
     * @return 返回结果
     */
    DataResponse<String> batchDown(SpuBatchReqVO spuBatchReqVO);

    /**
     * 批量归档数据
     * @return 返回包含String类型数据的响应
     */
    DataResponse<String> batchArchive(SpuBatchReqVO spuBatchReqVO);

    /**
     * 批量更新商品信息
     * @param spuUpdateReqVOList 商品更新请求对象列表
     * @return 包含更新结果的DataResponse对象
     */
    DataResponse<List<SpuUpdateReqVO>> batchUpdate(List<SpuUpdateReqVO> spuUpdateReqVOList);


    /**
     * 更新商品价格
     * @param dto 包含需要更新价格的商品信息的列表
     * @return 更新结果，包括更新成功的商品列表和失败的错误信息
     */
    DataResponse<Long> updatePrice(SpuUpdatePriceReqVO dto);


    /**
     * 更新库存并保存草稿
     * @param dto 库存管理请求数据传输对象
     * @return 更新结果
     */
    DataResponse<Boolean> updateStockAndDraft(StockManageReqDTO dto);

    /**
     * 更新SPU的SKU和MKU信息。
     * @param spuUpdateReqVO 包含SPU更新请求的VO对象。
     * @return DataResponse对象，包含更新结果。
     */
    DataResponse<Boolean> updateSpuSkuMku(SpuUpdateReqVO spuUpdateReqVO);

    /**
     * 批量审核
     *
     * @param spuAuditTaxApproveVO 请求对象
     * @return
     */
    @Validated(ValidateSpuGroup.approve.class)
    DataResponse<String> batchApproveTax(SpuAuditTaxApproveVO spuAuditTaxApproveVO);

    /**
     * 批量驳回
     *
     * @param spuAuditTaxApproveVO 审核对象
     * @return
     */
    @Validated(ValidateSpuGroup.reject.class)
    DataResponse<String> batchRejectTax(SpuAuditTaxApproveVO spuAuditTaxApproveVO);

    /**
     * 更新商品批次的供应商编码。
     * @param param 包含商品批次信息和新的供应商编码的请求体。
     * @return 更新操作的结果。
     */
    DataResponse<String> updateSpuVendorCode(SpuBatchReqVO param);

    /**
     * 根据中台商品扩展属性变动消息重新从中台拉去刷新商品扩展属性
     *
     * @param skuId
     * @return
     */
    DataResponse<String> refreshSpuExtAttribute(Long skuId);


    /**
     * 根据SKU ID和语言代码获取SPU名称
     * @param skuId SKU的唯一标识符
     * @param lang 语言代码（如zh-CN、en-US等）
     * @return 返回对应语言的SPU名称
     */
    String getSpuNameBySkuId(Long skuId, String lang);
}
