package com.jdi.isc.product.soa.service.manage.price.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.biz.component.api.enums.ApprovalOperateEnum;
import com.jdi.isc.biz.component.api.enums.JoySkyBizFlowTypeEnum;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyCancelApiDTO;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyCreateReqApiDTO;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyOperateApiDTO;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyQueryApiDTO;
import com.jdi.isc.biz.component.api.joysky.res.JoySkyCreateRspApiDTO;
import com.jdi.isc.biz.component.api.joysky.res.JoySkyDetailInfoDTO;
import com.jdi.isc.biz.component.api.joysky.res.JoySkyProcessInstanceDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.CurrencyConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.frame.SystemContextHolder;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.domain.apply.po.ApplyInfoPO;
import com.jdi.isc.product.soa.domain.customerSku.biz.PurchasePriceApproveReqVO;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.SkuScopeEnum;
import com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceReqVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPurchasePriceAuditVO;
import com.jdi.isc.product.soa.domain.price.supplierPrice.po.SkuPriceDraftPO;
import com.jdi.isc.product.soa.domain.sku.biz.ExternalVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuExternalReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.domain.spu.biz.GroupPropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangVO;
import com.jdi.isc.product.soa.domain.spu.po.ProductGlobalAttributePO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.supplier.biz.SupplierBaseInfoVO;
import com.jdi.isc.product.soa.rpc.joySky.JoySkyApproveRpcService;
import com.jdi.isc.product.soa.service.atomic.apply.ApplyInfoAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.ProductGlobalAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPurchasePriceApproveManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuExternalManageService;
import com.jdi.isc.product.soa.service.manage.supplier.SupplierBaseInfoManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * spu税率抽象服务
 *
 * <AUTHOR>
 * @date 2024/09/25
 **/
@Slf4j
@Service
public class SkuPurchasePriceApproveManageServiceImpl implements SkuPurchasePriceApproveManageService {

    @Resource
    private SkuExternalManageService skuExternalManageService;
    @Resource
    private ApplyInfoAtomicService applyInfoAtomicService;
    @Resource
    private JoySkyApproveRpcService joySkyApproveRpcService;
    @Resource
    private SkuPriceManageService skuPriceManageService;
    @Resource
    private SkuDraftAtomicService skuDraftAtomicService;
    @Resource
    private SkuPriceDraftAtomicService skuPriceDraftAtomicService;
    @Resource
    private SupplierBaseInfoManageService supplierBaseInfoManageService;
    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;

    @Resource
    private OperDuccConfig operDuccConfig;


    /** 对应的joySky审批流 */
    private static final JoySkyBizFlowTypeEnum flowTypeEnum = JoySkyBizFlowTypeEnum.PURCHASE_EFFECTIVE_FLOW;
    @Value("${spring.profiles.active}")
    private String systemProfile;
    @LafValue("jdi.isc.joySky.vc.purchase.submitter")
    private String joySkySubmitter;

    // 税金跨境属性ID集合
    private static final Set<Long> taxRateAmountIdSet = Sets.newHashSet(74L, 78L, 80L, 106L);

    @Override
    public String batchSubmit(PurchasePriceApproveReqVO input) {
        log.info("AbstractSkuPurchasePriceApproveManageService.batchSubmit input:{}", JSONObject.toJSONString(input));
        JoySkyCreateReqApiDTO joySkyCreateReqApiDTO = new JoySkyCreateReqApiDTO();

        String erp = this.getJoySkySubmitter(input.getSpuId());
        if(StringUtils.isBlank(erp)){
            throw new BizException("获取审批人失败");
        }

        joySkyCreateReqApiDTO.setErp(erp);
        joySkyCreateReqApiDTO.setJoySkyFlowType(flowTypeEnum.getFlowTypeCode());
        Map<String, String> processFormDataModelMap = this.constructProcessFormDataModel(input,erp);
        joySkyCreateReqApiDTO.setProcessFromData(processFormDataModelMap);

        // 审批流流程表单数据 增加附件，附件比较特殊，需要单独处理
        Map<String/*文件名*/, String/*url*/> attachmentMap = com.jdi.isc.product.soa.common.util.StringUtils.getAttachmentMap(input.getAttachmentUrls());

        joySkyCreateReqApiDTO.addAttachment("附件", attachmentMap);

        // 回写表单数据
        input.setProcessFromData(joySkyCreateReqApiDTO.getProcessFromData());

        // 多附件
        joySkyCreateReqApiDTO.setMultiFile(true);

        joySkyCreateReqApiDTO.setProcessControlData(this.constructProcessControlData(input.getSourceCountryCode()));
        JoySkyCreateRspApiDTO joySkyCreateRspApiDTO = joySkyApproveRpcService.createSkyApprovalFlow(joySkyCreateReqApiDTO);
        JoySkyDetailInfoDTO joySkyDetailInfo = this.getJoySkyDetailInfo(joySkyCreateRspApiDTO.getProcessInstanceId(),Boolean.FALSE,Boolean.TRUE);

        ApplyInfoPO dbApplyInfoPO = applyInfoAtomicService.getByBizTypeAndBizId(flowTypeEnum,input.getId());
        ApplyInfoPO applyInfoPO = null;
        if(dbApplyInfoPO != null){
            applyInfoPO = this.getUpdateApplyInfo(dbApplyInfoPO.getId()
                    ,AuditStatusEnum.WAITING_APPROVED.getCode(), LoginContextHolder.getLoginContextHolder().getPin(),dbApplyInfoPO.getVersion()
                    ,LoginContextHolder.getLoginContextHolder().getPin());
            applyInfoPO.setProcessInstanceId(joySkyDetailInfo.getProcessInstanceId());
            applyInfoPO.setApplyCode(joySkyDetailInfo.getApplyCode());
            applyInfoAtomicService.update(applyInfoPO,dbApplyInfoPO.getVersion());
        }else{
            applyInfoPO = this.initApplyInfo(input.getId(),joySkyDetailInfo);
            applyInfoAtomicService.save(applyInfoPO);
        }
        return "";
    }


    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean messageApprove(SkuPurchasePriceAuditVO input) {
        log.info("AbstractSkuPurchasePriceApproveManageService.messageApprove input:{}", JSONObject.toJSONString(input));
        // 更新applyInfo
        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(flowTypeEnum,input.getProcessInstanceId());
        if(applyInfoPO == null || applyInfoPO.getAuditStatus() == null || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.error("messageApprove.当前记录非待审核状态，不允许重复审批，processInstanceId:{}", input.getProcessInstanceId());
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化审批消息通过异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "applyInfoPO status is error"));
            return Boolean.FALSE;
        }

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId(),AuditStatusEnum.APPROVED.getCode(), null,applyInfoPO.getVersion(),input.getAuditErp());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());



        // 获取与更新采购价草稿状态
        SkuPriceDraftPO skuPriceDraftPO = skuPriceDraftAtomicService.getValidById(Long.valueOf(applyInfoPO.getBizId()));
        if(skuPriceDraftPO == null){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化审批消息通过异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "draftPO is null"));
            return Boolean.FALSE;
        }

        SkuPriceDraftPO updateDraftPO = this.getUpdateDraftPO(skuPriceDraftPO,AuditStatusEnum.APPROVED,skuPriceDraftPO.getUpdater(),input.getAuditErp(),input.getRejectReason());
        skuPriceDraftAtomicService.saveOrUpdate(updateDraftPO);

        // 更新sku草稿中采购价
        SkuDraftPO skuDraftPO = skuDraftAtomicService.getValidBySkuId(skuPriceDraftPO.getSkuId());
        SkuVO skuVO = JSON.parseObject(skuDraftPO.getSkuJsonInfo(), SkuVO.class);
        skuVO.setPurchasePrice(skuPriceDraftPO.getPrice() == null?"":skuPriceDraftPO.getPrice().toPlainString());
        skuVO.setTaxPurchasePrice(skuPriceDraftPO.getTaxPrice()== null?"":skuPriceDraftPO.getTaxPrice().toPlainString());
        skuVO.setUpdater(skuPriceDraftPO.getUpdater());
        skuVO.setUpdateTime(new Date());
        skuDraftPO.setSkuJsonInfo(JSONObject.toJSONString(skuVO));
        skuDraftPO.setSpuId(null);
        skuDraftPO.setUpdater(skuPriceDraftPO.getUpdater());
        skuDraftPO.setUpdateTime(new Date().getTime());

        // 设置巴西跨境属性税金
        this.setBrDraftSkuInterProperty(skuDraftPO,skuPriceDraftPO);

        skuDraftAtomicService.updateById(skuDraftPO);

        if(StringUtils.equals(CountryConstant.COUNTRY_BR,skuPriceDraftPO.getSourceCountryCode())){
            List<ProductGlobalAttributePO> productGlobalAttributePOS = productGlobalAttributeAtomicService.getBySkuIdsAndAttributeIds(Lists.newArrayList(skuPriceDraftPO.getSkuId()),taxRateAmountIdSet);
            List<ProductGlobalAttributePO> updatePOS = this.setBrProductSkuInterProperty(skuPriceDraftPO,productGlobalAttributePOS);
            if(CollectionUtils.isNotEmpty(updatePOS)){
                productGlobalAttributeAtomicService.updateBatchById(updatePOS);
            }
        }

        // 更新正式采购价
        SkuPriceVO skuPriceVO = this.buildSkuPriceVO(skuPriceDraftPO);
        List<SkuPriceVO> skuPriceVOS = Lists.newArrayList(skuPriceVO);
        SkuPriceReqVO skuPriceReqVO = new SkuPriceReqVO();
        skuPriceReqVO.setSkuPriceVO(skuPriceVOS);
        Boolean savePriceFlag = skuPriceManageService.saveOrUpdate(skuPriceReqVO);
        if(savePriceFlag == null || !savePriceFlag){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku采购价审批消息通过异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "draftPO is null"));
            return Boolean.FALSE;
        }

        log.info("AbstractSkuPurchasePriceApproveManageService.messageApprove " +
                        "pin:{},skuId:{},price:{},taxPrice:{}",input.getAuditErp(),skuPriceDraftPO.getSkuId()
                ,skuPriceDraftPO.getPrice(),skuPriceDraftPO.getTaxPrice());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean messageReject(SkuPurchasePriceAuditVO input) {
        log.info("AbstractSkuPurchasePriceApproveManageService.messageReject input:{}", JSONObject.toJSONString(input));
        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(flowTypeEnum,input.getProcessInstanceId());
        if(applyInfoPO == null || applyInfoPO.getAuditStatus() == null || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.error("messageReject.当前记录非待审核状态，不允许重复审批，processInstanceId:{}", input.getProcessInstanceId());
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化审批消息驳回异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "applyInfoPO status is error"));
            return Boolean.FALSE;
        }

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId(),AuditStatusEnum.REJECTED.getCode(), null,applyInfoPO.getVersion(),input.getAuditErp());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());

        // 获取与更新采购价草稿状态
        SkuPriceDraftPO skuPriceDraftPO = skuPriceDraftAtomicService.getValidById(Long.valueOf(applyInfoPO.getBizId()));
        if(skuPriceDraftPO == null){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化审批消息驳回异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "draftPO is null"));
            return Boolean.FALSE;
        }

        SkuPriceDraftPO updateDraftPO = this.getUpdateDraftPO(skuPriceDraftPO,AuditStatusEnum.REJECTED,skuPriceDraftPO.getUpdater(),input.getAuditErp(),input.getRejectReason());
        skuPriceDraftAtomicService.saveOrUpdate(updateDraftPO);

        log.info("AbstractSkuPurchasePriceApproveManageService.messageReject " +
                        "pin:{},skuId:{},price:{},taxPrice:{}",input.getAuditErp(),skuPriceDraftPO.getSkuId()
                ,skuPriceDraftPO.getPrice(),skuPriceDraftPO.getTaxPrice());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean messageRevoke(SkuPurchasePriceAuditVO input) {
        log.info("AbstractSkuPurchasePriceApproveManageService.messageRevoke input:{}", JSONObject.toJSONString(input));
        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(flowTypeEnum,input.getProcessInstanceId());
        if(applyInfoPO == null || applyInfoPO.getAuditStatus() == null || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.error("messageRevoke.当前记录非待审核状态，不允许重复审批，processInstanceId:{}", input.getProcessInstanceId());
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化审批消息撤回异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "applyInfoPO status is error"));
            return Boolean.FALSE;
        }

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId(),AuditStatusEnum.DRAFT.getCode(), null,applyInfoPO.getVersion(),input.getAuditErp());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
        // 获取与更新采购价草稿状态
        SkuPriceDraftPO skuPriceDraftPO = skuPriceDraftAtomicService.getValidById(Long.valueOf(applyInfoPO.getBizId()));
        if(skuPriceDraftPO == null){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化审批消息驳回异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "draftPO is null"));
            return Boolean.FALSE;
        }

        SkuPriceDraftPO updateDraftPO = this.getUpdateDraftPO(skuPriceDraftPO,AuditStatusEnum.DRAFT,skuPriceDraftPO.getUpdater(),input.getAuditErp(),input.getRejectReason());
        skuPriceDraftAtomicService.saveOrUpdate(updateDraftPO);

        log.info("AbstractSkuPurchasePriceApproveManageService.messageRevoke " +
                        "pin:{},skuId:{},price:{},taxPrice:{}",input.getAuditErp(),skuPriceDraftPO.getSkuId()
                ,skuPriceDraftPO.getPrice(),skuPriceDraftPO.getTaxPrice());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean messageApproving(SkuPurchasePriceAuditVO input) {
        log.info("AbstractSkuPurchasePriceApproveManageService.messageApproving input:{}", JSONObject.toJSONString(input));
        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(flowTypeEnum,input.getProcessInstanceId());
        if(applyInfoPO == null){
            log.error("messageApproving.当前记录不存在，processInstanceId:{}", input.getProcessInstanceId());
            return Boolean.FALSE;
        }

        if(applyInfoPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.error("messageApproving.当前记录非待审核状态，不允许重复审批，processInstanceId:{}", input.getProcessInstanceId());
            return Boolean.FALSE;
        }

        JoySkyDetailInfoDTO joySkyDetailInfo = this.getJoySkyDetailInfo(applyInfoPO.getProcessInstanceId(),Boolean.FALSE,Boolean.TRUE);
        String currentAuditor = null;
        if(CollectionUtils.isNotEmpty(joySkyDetailInfo.getTodoList())){
            currentAuditor = String.join(",", joySkyDetailInfo.getTodoList());
        }
        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId(),AuditStatusEnum.WAITING_APPROVED.getCode(), currentAuditor,applyInfoPO.getVersion(),input.getAuditErp());

        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
        log.info("AbstractSkuPurchasePriceApproveManageService.messageApproving " +
                "processInstanceId:{},auditErp:{}",input.getProcessInstanceId(),input.getAuditErp());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean singleApprove(SkuPriceDraftPO skuPriceDraftPO, SkuPurchasePriceAuditVO input, ApplyInfoPO applyInfoPO) {
        log.info("AbstractSkuPurchasePriceApproveManageService.singleApprove start draftPO:{},input:{},applyInfoPO:{}",JSONObject.toJSONString(skuPriceDraftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));

        if(applyInfoPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        if(skuPriceDraftPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != skuPriceDraftPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        Boolean flag = this.operateJoySky(ApprovalOperateEnum.AGREE,applyInfoPO.getProcessInstanceId()
                ,input.getRejectReason(),LoginContextHolder.getLoginContextHolder().getPin());

        if(!flag){
            throw new BizException("审批通过失败");
        }
        JoySkyDetailInfoDTO joySkyDetailInfo = this.getJoySkyDetailInfo(applyInfoPO.getProcessInstanceId(),Boolean.FALSE,Boolean.TRUE);
        if(joySkyDetailInfo != null && joySkyDetailInfo.getAuditStatus().equals(AuditStatusEnum.APPROVING.getCode())){
            String currentAuditor = null;
            if(CollectionUtils.isNotEmpty(joySkyDetailInfo.getTodoList())){
                currentAuditor = String.join(",", joySkyDetailInfo.getTodoList());
            }
            ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId()
                    ,AuditStatusEnum.WAITING_APPROVED.getCode(), currentAuditor ,applyInfoPO.getVersion()
                    ,LoginContextHolder.getLoginContextHolder().getPin());
            applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
            log.info("AbstractSkuPurchasePriceApproveManageService.singleApprove 流程未结束 draftPO:{},input:{},applyInfo:{}",JSONObject.toJSONString(skuPriceDraftPO)
                    ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));
            return Boolean.TRUE;
        }

        // 更新sku草稿中采购价
        SkuDraftPO skuDraftPO = skuDraftAtomicService.getValidBySkuId(skuPriceDraftPO.getSkuId());
        SkuVO skuVO = JSON.parseObject(skuDraftPO.getSkuJsonInfo(), SkuVO.class);
        skuVO.setPurchasePrice(skuPriceDraftPO.getPrice() == null?"":skuPriceDraftPO.getPrice().toPlainString());
        skuVO.setTaxPurchasePrice(skuPriceDraftPO.getTaxPrice()== null?"":skuPriceDraftPO.getTaxPrice().toPlainString());
        skuVO.setUpdater(skuPriceDraftPO.getUpdater());
        skuVO.setUpdateTime(new Date());
        skuDraftPO.setSkuJsonInfo(JSONObject.toJSONString(skuVO));
        skuDraftPO.setSpuId(null);
        skuDraftPO.setUpdater(skuPriceDraftPO.getUpdater());
        skuDraftPO.setUpdateTime(new Date().getTime());

        // 设置巴西跨境属性税金
        this.setBrDraftSkuInterProperty(skuDraftPO,skuPriceDraftPO);
        skuDraftAtomicService.updateById(skuDraftPO);

        SkuPriceDraftPO updatePO = this.getUpdateDraftPO(skuPriceDraftPO,AuditStatusEnum.APPROVED
                ,skuPriceDraftPO.getUpdater(),LoginContextHolder.getLoginContextHolder().getPin(), input.getRejectReason());
        skuPriceDraftAtomicService.saveOrUpdate(updatePO);

        if(StringUtils.equals(CountryConstant.COUNTRY_BR,skuPriceDraftPO.getSourceCountryCode())){
            List<ProductGlobalAttributePO> productGlobalAttributePOS = productGlobalAttributeAtomicService.getBySkuIdsAndAttributeIds(Lists.newArrayList(skuPriceDraftPO.getSkuId()),taxRateAmountIdSet);
            List<ProductGlobalAttributePO> updatePOS = this.setBrProductSkuInterProperty(skuPriceDraftPO,productGlobalAttributePOS);
            if(CollectionUtils.isNotEmpty(updatePOS)){
                productGlobalAttributeAtomicService.updateBatchById(updatePOS);
            }
        }

        SkuPriceVO skuPriceVO = this.buildSkuPriceVO(skuPriceDraftPO);
        List<SkuPriceVO> skuPriceVOS = Lists.newArrayList(skuPriceVO);
        SkuPriceReqVO skuPriceReqVO = new SkuPriceReqVO();
        skuPriceReqVO.setSkuPriceVO(skuPriceVOS);
        Boolean savePriceFlag = skuPriceManageService.saveOrUpdate(skuPriceReqVO);
        if(savePriceFlag == null || !savePriceFlag){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku采购价审批消息通过异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "draftPO is null"));
            return Boolean.FALSE;
        }

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId()
                ,AuditStatusEnum.APPROVED.getCode(), LoginContextHolder.getLoginContextHolder().getPin(),applyInfoPO.getVersion()
                ,LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());

        log.info("AbstractSkuPurchasePriceApproveManageService.singleApprove end draftPO:{},input:{},applyInfoMap:{}",JSONObject.toJSONString(skuPriceDraftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean singleReject(SkuPriceDraftPO skuPriceDraftPO, SkuPurchasePriceAuditVO input, ApplyInfoPO applyInfoPO) {
        log.info("AbstractSkuPurchasePriceApproveManageService.singleReject start draftPO:{},input:{},applyInfoPO:{}",JSONObject.toJSONString(skuPriceDraftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));

        if(applyInfoPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        if(skuPriceDraftPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != skuPriceDraftPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        SkuPriceDraftPO updatePO = this.getUpdateDraftPO(skuPriceDraftPO,AuditStatusEnum.REJECTED
                ,skuPriceDraftPO.getUpdater(),LoginContextHolder.getLoginContextHolder().getPin(), input.getRejectReason());
        skuPriceDraftAtomicService.saveOrUpdate(updatePO);

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId()
                ,AuditStatusEnum.REJECTED.getCode(), LoginContextHolder.getLoginContextHolder().getPin(),applyInfoPO.getVersion()
                ,LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());

        this.operateJoySky(ApprovalOperateEnum.REJECT_TO_SUBMIT_NODE,applyInfoPO.getProcessInstanceId()
                ,input.getRejectReason(),LoginContextHolder.getLoginContextHolder().getPin());

        log.info("AbstractSkuPurchasePriceApproveManageService.singleReject end draftPO:{},input:{},applyInfoMap:{}",JSONObject.toJSONString(skuPriceDraftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean singleRevoke(SkuPriceDraftPO skuPriceDraftPO, SkuPurchasePriceAuditVO input, ApplyInfoPO applyInfoPO) {
        log.info("AbstractSkuPurchasePriceApproveManageService.singleRevoke start draftPO:{},input:{},applyInfoPO:{}",JSONObject.toJSONString(skuPriceDraftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));

        if(applyInfoPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        if(skuPriceDraftPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != skuPriceDraftPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        SkuPriceDraftPO updatePO = this.getUpdateDraftPO(skuPriceDraftPO,AuditStatusEnum.DRAFT
                ,skuPriceDraftPO.getUpdater(),null, input.getRejectReason());
        skuPriceDraftAtomicService.saveOrUpdate(updatePO);

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId()
                ,AuditStatusEnum.DRAFT.getCode(), LoginContextHolder.getLoginContextHolder().getPin(),applyInfoPO.getVersion()
                ,LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
        String processInstanceId = applyInfoPO.getProcessInstanceId();

        JoySkyDetailInfoDTO joySkyDetailInfo = this.getJoySkyDetailInfo(applyInfoPO.getProcessInstanceId(),Boolean.FALSE,Boolean.FALSE);
        JoySkyProcessInstanceDTO joySkyProcessInstanceDTO = joySkyDetailInfo.getProcessInstance();

        this.cancelJoySky(processInstanceId,joySkyProcessInstanceDTO.getStarterUserName());

        log.info("AbstractSkuPurchasePriceApproveManageService.singleRevoke end draftPO:{},input:{},processInstanceId:{}",JSONObject.toJSONString(skuPriceDraftPO)
                ,JSONObject.toJSONString(input),processInstanceId);
        return Boolean.TRUE;
    }

    /**
     * 构建采购价格审批表单数据模型。
     * @param input 采购价格审批请求对象，包含国际SKUID、类目ID、币种、采购价、含税采购价等信息。
     * @return 表单数据模型，包含国际SKUID、商品名称、类目名称、未税采购价、含税采购价、提交人等字段。
     * @throws BizException 如果输入参数中国际SKUID、类目ID、币种、采购价、含税采购价等信息为空或不合法。
     */
    private Map<String, String> constructProcessFormDataModel(PurchasePriceApproveReqVO input,String erp){
        if(input.getSkuId() == null){
            throw new BizException("国际SKUID不可为空");
        }
        if(StringUtils.isBlank(input.getCurrency())){
            throw new BizException("币种不可为空");
        }
        if(input.getPurchasePrice() == null){
            throw new BizException("采购价不可为空");
        }
        if(input.getOriginPurchasePrice() == null){
            throw new BizException("原采购价不可为空");
        }
        if(input.getTaxPurchasePrice() == null){
            throw new BizException("含税采购价不可为空");
        }
        if(input.getCountryCostPrice() == null){
            throw new BizException("国家成本价不可为空");
        }
        if(input.getCountryAgreementPrice() == null){
            throw new BizException("国家协议价不可为空");
        }
        if(input.getProfitRate() == null){
            throw new BizException("利润率为空");
        }
        if(input.getProfitLimitRate() == null){
            throw new BizException("利润率阈值为空");
        }
        if(input.getLowProfitLimitRate() == null){
            throw new BizException("超低利润率阈值为空");
        }
        if(StringUtils.isBlank(input.getCostCurrency())){
            throw new BizException("成本价币种为空");
        }

        String originPurchasePrice = input.getOriginPurchasePrice().stripTrailingZeros().toPlainString();

        Map<String, String> fieldCommentToFieldNameMap = new HashMap<>(9);
        fieldCommentToFieldNameMap.put("国际SKUID", input.getSkuId().toString());
        fieldCommentToFieldNameMap.put("商品名称", this.querySkuName(input.getSkuId()));
        fieldCommentToFieldNameMap.put("未税采购价（调价后）", input.getPurchasePrice().stripTrailingZeros().toPlainString() + input.getCurrency());
        fieldCommentToFieldNameMap.put("未税采购价数字", input.getPurchasePrice().stripTrailingZeros().toPlainString());
        fieldCommentToFieldNameMap.put("原未税采购价数字", originPurchasePrice);
        fieldCommentToFieldNameMap.put("含税采购价（新）", input.getTaxPurchasePrice().stripTrailingZeros().toPlainString() + input.getCurrency());
        fieldCommentToFieldNameMap.put("国家成本价（新）", input.getCountryCostPrice().stripTrailingZeros().toPlainString() + input.getCostCurrency());
        fieldCommentToFieldNameMap.put("国家协议价", input.getCountryAgreementPrice().stripTrailingZeros().toPlainString() + input.getCostCurrency());
        fieldCommentToFieldNameMap.put("利润率-前毛（调价后）", input.getProfitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());

        fieldCommentToFieldNameMap.put("提交人", erp);
        fieldCommentToFieldNameMap.put("利润率预警信息",input.getWarningMsg());
        fieldCommentToFieldNameMap.put("利润率阈值-前毛", input.getProfitLimitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        fieldCommentToFieldNameMap.put("超低利润率阈值-前毛", input.getLowProfitLimitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        fieldCommentToFieldNameMap.put("调价原因", input.getAdjustmentPriceReason());

        fieldCommentToFieldNameMap.put("未税采购价（调价前）", String.format("%s%s", originPurchasePrice, input.getCurrency()));
        fieldCommentToFieldNameMap.put("未税采购价调整幅度（调价前后对比）", input.getPurchasePriceAdjustment());
        fieldCommentToFieldNameMap.put("利润率-前毛（调价前）", input.getOriginProfitRate() == null ? StringUtils.EMPTY : input.getOriginProfitRate().stripTrailingZeros().toPlainString());
        fieldCommentToFieldNameMap.put("未税采购价人民币参考金额（调价前）", input.getOriginPurchasePriceRmb() == null ? StringUtils.EMPTY : input.getOriginPurchasePriceRmb().stripTrailingZeros().toPlainString() + CurrencyConstant.CURRENCY_ZH);
        fieldCommentToFieldNameMap.put("未税采购价人民币参考金额（调价后）", input.getPurchasePriceRmb() == null ? StringUtils.EMPTY : input.getPurchasePriceRmb().stripTrailingZeros().toPlainString() + CurrencyConstant.CURRENCY_ZH);

        // 去掉前毛 http://jagile.jd.com/demands/view/LPRZMTB2?demandId=3423569
        fieldCommentToFieldNameMap.put("利润率（调价后）", input.getProfitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        fieldCommentToFieldNameMap.put("利润率（调价前）", input.getOriginProfitRate() == null ? StringUtils.EMPTY : input.getOriginProfitRate().stripTrailingZeros().toPlainString());
        fieldCommentToFieldNameMap.put("利润率阈值", input.getProfitLimitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        fieldCommentToFieldNameMap.put("超低利润率阈值", input.getLowProfitLimitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        // 边际负毛阈值
        fieldCommentToFieldNameMap.put("边际负毛阈值", operDuccConfig.getGrossProfit(input.getSourceCountryCode()).multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        return fieldCommentToFieldNameMap;
    }

    /**
     * 构建流程控制数据。
     * @param sourceCountryCode 商品来源国代码
     * @return 流程控制参数映射
     */
    private Map<String, Object> constructProcessControlData(String sourceCountryCode) {
        //流程控制参数校验
        if (StringUtils.isBlank(sourceCountryCode)) {
            throw new BizException("流程控制参数不能为空!,商品来源国:"+sourceCountryCode);
        }

        //流程参数赋值
        Map<String, Object> flowCalculeVarMap = new HashMap<>(2);
        flowCalculeVarMap.put("productCountry", sourceCountryCode);
        return flowCalculeVarMap;
    }

    /**
     * 根据 SKU ID 查询商品标题信息。
     * @param skuId SKU 的唯一标识符。
     * @return 中文商品标题，若查询不到则返回 null。
     */
    private String querySkuName(Long skuId) {
        SkuExternalReqVO skuQuery = new SkuExternalReqVO();
        skuQuery.setSkuIds(Lists.newArrayList(skuId));
        skuQuery.setScopeEnums(Sets.newHashSet(SkuScopeEnum.BASE, SkuScopeEnum.LANG));

        Map<Long, ExternalVO> skuExternalVOMap = skuExternalManageService.querySkuInfo(skuQuery);
        if(MapUtils.isEmpty(skuExternalVOMap)){
            return null;
        }
        ExternalVO externalVO = skuExternalVOMap.get(skuId);
        if(externalVO == null){
            return null;
        }
        SpuLangVO spuLangVO = externalVO.getLangVOList().stream()
                .filter(line -> LangConstant.LANG_ZH.equals(line.getLang()))
                .findFirst().orElse(null);
        return spuLangVO != null ? spuLangVO.getSpuTitle() : null;
    }

    /**
     * 获取审批流程的详细信息。
     * @param processInstanceId 审批流程的实例ID。
     * @param subData 是否需要子表数据。
     * @param todoList 是否需要待办列表。
     * @return JoySkyDetailInfoDTO 审批流程的详细信息。
     */
    private JoySkyDetailInfoDTO getJoySkyDetailInfo(String processInstanceId
            ,Boolean subData,Boolean todoList){
        // 审批通过获取回填数据
        JoySkyQueryApiDTO joySkyQueryApiDTO = new JoySkyQueryApiDTO();
        joySkyQueryApiDTO.setJoySkyFlowType(flowTypeEnum.getFlowTypeCode());
        joySkyQueryApiDTO.setProcessInstanceId(processInstanceId);
        joySkyQueryApiDTO.setNeedSubTableData(subData);
        joySkyQueryApiDTO.setNeedTodoList(todoList);
        JoySkyDetailInfoDTO joySkyDetailInfoDTO = joySkyApproveRpcService.queryFlowModel(joySkyQueryApiDTO);
        return joySkyDetailInfoDTO;
    }

    /**
     * 获取更新后的申请信息对象
     * @param id 申请信息ID
     * @param auditStatus 审核状态
     * @param currentAuditor 当前审核人
     * @param version 版本号
     * @param updater 更新人
     * @return 更新后的申请信息对象
     */
    private ApplyInfoPO getUpdateApplyInfo(Long id,Integer auditStatus,String currentAuditor,Integer version,String updater){
        ApplyInfoPO updateApplyInfoPO = new ApplyInfoPO();
        updateApplyInfoPO.setId(id);
        updateApplyInfoPO.setAuditStatus(auditStatus);
        updateApplyInfoPO.setCurrentAuditor(currentAuditor);
        updateApplyInfoPO.setUpdater(updater);
        updateApplyInfoPO.setVersion(version+1);
        return updateApplyInfoPO;
    }

    /**
     * 初始化申请信息实体
     * @param id 申请ID
     * @param joySkyDetailInfoDTO 申请详细信息DTO
     * @return 初始化后的申请信息实体
     */
    private ApplyInfoPO initApplyInfo(Long id,JoySkyDetailInfoDTO joySkyDetailInfoDTO){
        ApplyInfoPO applyInfoPO = new ApplyInfoPO();
        applyInfoPO.setProcessType(flowTypeEnum.getFlowTypeCode());
        applyInfoPO.setBizId(id.toString());
        applyInfoPO.setProcessInstanceId(joySkyDetailInfoDTO.getProcessInstanceId());
        applyInfoPO.setApplyCode(joySkyDetailInfoDTO.getApplyCode());
        applyInfoPO.setAuditStatus(AuditStatusEnum.WAITING_APPROVED.getCode());
        if(CollectionUtils.isNotEmpty(joySkyDetailInfoDTO.getTodoList())){
            String currentAuditor = String.join(",", joySkyDetailInfoDTO.getTodoList());
            applyInfoPO.setCurrentAuditor(currentAuditor);
        }
        applyInfoPO.setVersion(0);
        applyInfoPO.setCreator(LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoPO.setCreateTime(new Date().getTime());
        applyInfoPO.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoPO.setUpdateTime(new Date().getTime());
        return applyInfoPO;
    }

    /**
     * 执行JoySky审批操作
     * @param operateEnum 审批操作枚举
     * @param processInstanceId 流程实例ID
     * @param comment 审批备注
     * @param erp ERP系统信息
     * @return 操作结果
     */
    private Boolean operateJoySky(ApprovalOperateEnum operateEnum,String processInstanceId,String comment,String erp){
        JoySkyOperateApiDTO joySkyOperateApiDTO = new JoySkyOperateApiDTO();
        joySkyOperateApiDTO.setJoySkyFlowType(flowTypeEnum.getFlowTypeCode());
        joySkyOperateApiDTO.setOperateEnum(operateEnum);
        joySkyOperateApiDTO.setProcessInstanceId(processInstanceId);
        joySkyOperateApiDTO.setComment(comment);
        joySkyOperateApiDTO.setErp(erp);
        return joySkyApproveRpcService.operateSky(joySkyOperateApiDTO);
    }

    /**
     * 取消JoySky审批流程
     * @param processInstanceId 流程实例ID
     * @param erp ERP系统标识
     * @return 取消结果
     */
    private Boolean cancelJoySky(String processInstanceId,String erp){
        JoySkyCancelApiDTO joySkyCancelApiDTO = new JoySkyCancelApiDTO();
        joySkyCancelApiDTO.setErp(erp);
        joySkyCancelApiDTO.setJoySkyFlowType(JoySkyBizFlowTypeEnum.PURCHASE_EFFECTIVE_FLOW.getFlowTypeCode());
        joySkyCancelApiDTO.setCancelComment("用户主动取消");
        joySkyCancelApiDTO.setProcessInstanceId(processInstanceId);
        return joySkyApproveRpcService.cancelSkyApprovalFlow(joySkyCancelApiDTO);
    }

    /**
     * 根据给定的参数更新 SkuPriceDraftPO 对象并返回。
     * @param draftPO 原始的 SkuPriceDraftPO 对象。
     * @param auditStatusEnum 审核状态枚举值。
     * @param updater 更新人。
     * @param auditErp 审核人ERP。
     * @param rejectReason 拒绝理由。
     * @return 更新后的 SkuPriceDraftPO 对象。
     */
    private SkuPriceDraftPO getUpdateDraftPO(SkuPriceDraftPO draftPO
            , AuditStatusEnum auditStatusEnum, String updater, String auditErp, String rejectReason){
        SkuPriceDraftPO updatePO = new SkuPriceDraftPO();
        updatePO.setId(draftPO.getId());
        updatePO.setAuditStatus(auditStatusEnum.getCode());
        updatePO.setAuditor(auditErp);
        updatePO.setUpdater(updater);
        updatePO.setUpdateTime(new Date());
        updatePO.setRejectReason(rejectReason);
        return updatePO;
    }

    /**
     * 构建 SkuPriceVO 对象。
     * @param skuPriceDraftPO SkuPriceDraftPO 对象，包含了构建 SkuPriceVO 所需的信息。
     * @return 一个新的 SkuPriceVO 对象。
     */
    private SkuPriceVO buildSkuPriceVO(SkuPriceDraftPO skuPriceDraftPO) {
        SkuPriceVO skuPriceVO = new SkuPriceVO();
        skuPriceVO.setSpuId(skuPriceDraftPO.getSpuId());
        skuPriceVO.setSkuId(skuPriceDraftPO.getSkuId());
        skuPriceVO.setSourceCountryCode(skuPriceDraftPO.getSourceCountryCode());
        skuPriceVO.setCurrency(skuPriceDraftPO.getCurrency());
        skuPriceVO.setTradeDirection(TradeDirectionEnum.SUPPLIER);
        skuPriceVO.setTradeType(skuPriceDraftPO.getTradeType());
        skuPriceVO.setTaxPrice(skuPriceDraftPO.getTaxPrice());
        skuPriceVO.setPrice(skuPriceDraftPO.getPrice());
        skuPriceVO.setUpdater(skuPriceDraftPO.getUpdater());
        skuPriceVO.setUpdateTime(new Date());
        return skuPriceVO;
    }

    /**
     * 根据国家代码获取 JoySky 提交者信息。
     * @param countryCode 国家代码
     * @return JoySky 提交者信息，若国家代码为空则返回 null
     */
    private String getJoySkySubmitter(String countryCode) {
        if(StringUtils.isBlank(countryCode)){
            log.error("SkuPurchasePriceApproveManageServiceImpl.getJoySkySubmitter param is null");
            return null;
        }
        return ConfigUtils.getStringByKey(joySkySubmitter,countryCode);
    }

    /**
     * 获取提交者ERP号码。
     * @param spuId 商品ID
     * @return 提交者ERP号码
     */
    private String getJoySkySubmitter(Long spuId){
        String erp = "";
        if(Constant.SYSTEM_CODE.equals(SystemContextHolder.get())){
            erp = LoginContextHolder.getLoginContextHolder().getPin();
        } else {
            SpuPO spuPO = spuAtomicService.getSpuPoBySpuId(spuId);
            erp = this.getJoySkySubmitter(spuPO.getSourceCountryCode());
            if(StringUtils.isBlank(erp)){
                SupplierBaseInfoVO supplierBaseInfoVO = supplierBaseInfoManageService.getByCode(spuPO.getVendorCode());
                erp = supplierBaseInfoVO.getUpdater();
            }
        }
        return erp;
    }

    /**
     * 设置巴西 SKU 的国际属性。
     * @param skuDraftPO SKU 草稿信息
     * @param skuPriceDraftPO SKU 价格草稿信息
     */
    private void setBrDraftSkuInterProperty(SkuDraftPO skuDraftPO,SkuPriceDraftPO skuPriceDraftPO){
        if(!StringUtils.equals(CountryConstant.COUNTRY_BR,skuPriceDraftPO.getSourceCountryCode())){
            log.info("SkuPurchasePriceApproveManageServiceImpl.setBrDraftSkuInterProperty param is not BR");
            return;
        }
        String value1 = skuPriceDraftPO.getValue1();
        if(StringUtils.isBlank(value1)){
            log.info("SkuPurchasePriceApproveManageServiceImpl.setBrDraftSkuInterProperty value1 is null");
            return;
        }
        List<PropertyVO> value1Propertys = JSON.parseObject(value1,
                new TypeReference<List<PropertyVO>>() {});
        value1Propertys = value1Propertys.stream().filter(item->taxRateAmountIdSet.contains(item.getAttributeId())).collect(Collectors.toList());


        String skuInterProperty = skuDraftPO.getSkuInterProperty();
        if(StringUtils.isBlank(skuInterProperty)){
            log.error("SkuPurchasePriceApproveManageServiceImpl.setBrDraftSkuInterProperty，skuDraftPO:{}", JSONObject.toJSONString(skuDraftPO));
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s 采购价通过异常,skuId: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , skuDraftPO.getSkuId()
                    , "skuInterProperty is null"));
            return;
        }
        List<GroupPropertyVO> skuGroupInterProperties = JSON.parseObject(skuDraftPO.getSkuInterProperty(),
                new TypeReference<List<GroupPropertyVO>>() {});

        for(PropertyVO valuePropertyVO : value1Propertys){
            skuGroupInterProperties.forEach(skuInterPropertyVO -> {
                List<PropertyVO> propertyVOS = skuInterPropertyVO.getPropertyVOS();
                if(CollectionUtils.isEmpty(propertyVOS)){
                    return;
                }
                propertyVOS.forEach(propertyVO -> {
                    if(valuePropertyVO.getAttributeId().equals(propertyVO.getAttributeId())){
                        propertyVO.setPropertyValueVOList(valuePropertyVO.getPropertyValueVOList());
                    }
                });
            });
        }

        skuDraftPO.setSkuInterProperty(JSON.toJSONString(skuGroupInterProperties));
    }


    /**
     * 设置巴西产品 SKU 的交叉属性。
     * @param skuPriceDraftPO SKU 价格草稿对象
     * @param productGlobalAttributePOS 全局属性列表
     * @return 更新后的全局属性列表
     */
    private List<ProductGlobalAttributePO> setBrProductSkuInterProperty(SkuPriceDraftPO skuPriceDraftPO,List<ProductGlobalAttributePO> productGlobalAttributePOS){
        if(!StringUtils.equals(CountryConstant.COUNTRY_BR,skuPriceDraftPO.getSourceCountryCode())){
            log.info("SkuPurchasePriceApproveManageServiceImpl.setBrProductSkuInterProperty param is not BR");
            return null;
        }
        String value1 = skuPriceDraftPO.getValue1();
        if(StringUtils.isBlank(value1)){
            log.info("SkuPurchasePriceApproveManageServiceImpl.setBrProductSkuInterProperty value1 is null");
            return null;
        }
        List<PropertyVO> value1Propertys = JSON.parseObject(value1,
                new TypeReference<List<PropertyVO>>() {});
        value1Propertys = value1Propertys.stream().filter(item->taxRateAmountIdSet.contains(item.getAttributeId())).collect(Collectors.toList());

        Map<Long,ProductGlobalAttributePO> productGlobalAttributePOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(productGlobalAttributePOS)){
            productGlobalAttributePOS.forEach(productGlobalAttributePO -> {
                productGlobalAttributePOMap.put(productGlobalAttributePO.getAttributeId(),productGlobalAttributePO);
            });
        }

        List<ProductGlobalAttributePO> results = new ArrayList<>();
        for(PropertyVO propertyVO : value1Propertys){
            ProductGlobalAttributePO productGlobalAttributePO = productGlobalAttributePOMap.get(propertyVO.getAttributeId());
            if(productGlobalAttributePO == null){
                 continue;
            }
            List<PropertyValueVO> propertyValueVOList = propertyVO.getPropertyValueVOList();
            if(CollectionUtils.isEmpty(propertyValueVOList)){
                continue;
            }
            PropertyValueVO propertyValueVO = propertyValueVOList.get(0);
            String attributeValueName = propertyValueVO.getAttributeValueName();
            if(StringUtils.isBlank(attributeValueName)){
                continue;
            }

            ProductGlobalAttributePO updatePO = new ProductGlobalAttributePO();
            updatePO.setId(productGlobalAttributePO.getId());
            updatePO.setAttributeValue(attributeValueName);
            updatePO.setUpdater(skuPriceDraftPO.getUpdater());
            updatePO.setUpdateTime(new Date().getTime());
            results.add(updatePO);
        }
        return results;
    }
}
