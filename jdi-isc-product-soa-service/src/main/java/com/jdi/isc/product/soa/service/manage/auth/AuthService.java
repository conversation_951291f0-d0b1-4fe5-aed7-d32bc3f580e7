package com.jdi.isc.product.soa.service.manage.auth;

import com.jd.auth.facade.response.Response;
import com.jd.auth.facade.response.menu.FuncDto;
import com.jd.auth.facade.response.menu.MenuDto;
import com.jd.auth.facade.response.res.DataResDto;
import com.jd.auth.facade.response.res.DimResourceDto;
import com.jd.auth.facade.response.role.RoleDto;
import com.jdi.isc.product.soa.domain.auth.AuthReqVO;
import com.jdi.isc.product.soa.domain.auth.RouteResVO;

import java.util.List;

public interface AuthService {
    Response<Boolean> checkResource(AuthReqVO vo);

    Response<List<String>> filterResourcesByErp(AuthReqVO vo);

    Response<List<MenuDto>> querySystemAllMenu(AuthReqVO vo);

    Response<List<MenuDto>> getRootMenu(AuthReqVO vo);

    RouteResVO getMenuTree(AuthReqVO vo);

    Response<List<FuncDto>> getMenuFun(AuthReqVO vo);

    RouteResVO getAllMenuFunc(AuthReqVO vo);

    Response<List<RoleDto>> getRoleByResCode(AuthReqVO vo);

    Response<List<DimResourceDto>> queryDimResValueList(AuthReqVO vo);
}
