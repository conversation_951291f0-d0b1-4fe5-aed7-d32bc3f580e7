package com.jdi.isc.product.soa.service.manage.category;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.domain.category.biz.*;
import com.jdi.isc.product.soa.domain.common.biz.KvVO;
import com.jdi.isc.product.soa.domain.spu.biz.GroupPropertyVO;

import java.util.Collection;
import java.util.List;

/**
 * @Description: 国际跨境属性数据维护服务
 * @Author: taxuezheng1
 * @Date: 2024/07/12 13:17
 **/

public interface GlobalAttributeManageService {

    /**
     * 保存、更新
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<Boolean> saveOrUpdate(GlobalAttributeVO input);

    /**
     * 详情
     * @param id 对象ID
     * @return VO对象
     */
    GlobalAttributeVO detail(Long id);

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    PageInfo<GlobalAttributePageVO.Response> pageSearch(GlobalAttributePageVO.Request input);


    /**
     * 校验多语言名称重复
     */
    boolean checkLangName(GlobalAttributeVO input);

    /**
     * 查询属性列表
     */
    List<GlobalAttributePageVO.Response> queryAttributeList(GlobalAttributePageVO.Request reqVo);

    /**
     * 查询属性值列表
     */
    List<GlobalAttributeValueVO> queryAttributeValueList(GlobalAttributePageVO.Request reqVo);

    /**
     * 根据具体属性组装分组
     */
    List<GroupPropertyVO> queryGroupPropertyVos(Long catId, String sourceCountryCode,
        Collection<String> targetCountryCodes, Integer dimension,String lang);

    /**
     * 根据具体属性组装分组
     */
    List<GroupPropertyVO> queryGroupPropertyVos(Long catId, String sourceCountryCode,
        Collection<String> targetCountryCodes, Integer dimension,String lang,Integer isExport);

    /**
     * 根据id查询属性名称
     */
    List<KvVO> queryAttributeName(List<Long> ids, String lang);

    /**
     * 根据分类ID查询全局属性列表
     * @param queryReqDTO 包含查询条件的DTO对象
     * @return 全局属性列表的VO对象集合
     */
    List<GlobalAttributeResVO> queryAttributeListByCatId(GlobalAttributeQueryReqVO queryReqDTO);

    /**
     * 根据给定的ID列表查询全局属性值对象列表。
     * @param ids 要查询的全局属性值对象的ID列表。
     * @return 对应ID的全局属性值对象列表。
     */
    List<GlobalAttributeValueVO> queryAttributeValueList(List<Long> ids);

    /**
     * 更具具体的跨境属性ID查询跨境属性列表
     * @param attributeIds
     * @param dimension
     * @param lang
     * @param sourceCountryCode
     * @param targetCountryCodes
     * @return
     */
    List<GroupPropertyVO> queryGroupPropertyVos(Collection<Long> attributeIds, Integer dimension,String lang,
                                                String sourceCountryCode,List<String> targetCountryCodes);
}
