package com.jdi.isc.product.soa.service.manage.userCrm.impl;

import com.jdi.isc.product.soa.rpc.userCrm.UserCrmRpcService;
import com.jdi.isc.product.soa.service.manage.userCrm.UserCrmManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class UserCrmManageServiceImpl implements UserCrmManageService {

    @Resource
    private UserCrmRpcService userCrmRpcService;

    @Override
    public List<String> queryContractList(String erp) {
        List<String> contractNoList = userCrmRpcService.queryContractPage(erp);
        return contractNoList;
    }
}
