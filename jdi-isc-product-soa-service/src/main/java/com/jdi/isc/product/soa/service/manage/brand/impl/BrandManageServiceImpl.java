package com.jdi.isc.product.soa.service.manage.brand.impl;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.BrandAuditStatusEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.util.CommonUtils;
import com.jdi.isc.product.soa.domain.brand.biz.*;
import com.jdi.isc.product.soa.domain.brand.po.BrandCategoryPO;
import com.jdi.isc.product.soa.domain.brand.po.BrandCountryPO;
import com.jdi.isc.product.soa.domain.brand.po.BrandLangPO;
import com.jdi.isc.product.soa.domain.brand.po.BrandPO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryComboBoxVO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryTreeVo;
import com.jdi.isc.product.soa.domain.common.biz.BaseVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.draft.CountryAgreementPriceAuditResVO;
import com.jdi.isc.product.soa.service.atomic.brand.BrandAtomicService;
import com.jdi.isc.product.soa.service.atomic.brand.BrandCategoryAtomicService;
import com.jdi.isc.product.soa.service.atomic.brand.BrandCountryAtomicService;
import com.jdi.isc.product.soa.service.atomic.brand.BrandLangAtomicService;
import com.jdi.isc.product.soa.service.manage.brand.BrandAuditRecordManageService;
import com.jdi.isc.product.soa.service.manage.brand.BrandDraftManageService;
import com.jdi.isc.product.soa.service.manage.brand.BrandManageService;
import com.jdi.isc.product.soa.service.manage.brand.BrandOutService;
import com.jdi.isc.product.soa.service.manage.brand.xbp.XbpTicketBrandService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.category.impl.CategoryTreeBuilder;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.mapstruct.brand.BrandConvert;
import com.jdi.isc.product.soa.service.mapstruct.brand.BrandCountryConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/29 2:18 下午
 */

@Slf4j
@Service
public class BrandManageServiceImpl implements BrandManageService {

    @Resource
    private BrandAtomicService brandAtomicService;

    @Resource
    private BrandCountryAtomicService brandCountryAtomicService;

    @Resource
    private BrandLangAtomicService brandLangAtomicService;

    @Resource
    private BrandCategoryAtomicService brandCategoryAtomicService;

    @Resource
    private CategoryTreeBuilder categoryTreeBuilder;

    @Resource
    private CategoryOutService categoryOutService;

    @Resource
    private CountryManageService countryManageService;

    @Resource
    private XbpTicketBrandService xbpTicketBrandService;

    @Resource
    private BrandDraftManageService brandDraftManageService;

    @Resource
    private BrandAuditRecordManageService brandAuditRecordManageService;

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> saveOrUpdate(BrandVO input) {
        DataResponse<Boolean> checkResult = checkInput(input);
        if (!checkResult.getSuccess()) {
            return checkResult;
        }
        //处理全球国家码
        handleCountry(input);
   /*     Map<Long, Long> existCateIds = new HashMap<>();
        if (null != input.getId()) {
            existCateIds.putAll(getAllBrandCateIds(input.getId()));
        }
        //需要删除的类目id
        Set<Long> needDelIds = new HashSet<>(existCateIds.keySet());
        Set<Long> saveCateIds = new HashSet<>();
        if (!input.isAllCategoryScope()) {
            //非全类目的情况下，需要查询出选中的四级类目节点
            Set<Long> categoryIds = categoryTreeBuilder.buildFourCategoryIds(input.getCategoryTreeList());
            saveCateIds.addAll(categoryIds);
        }
        needDelIds.removeAll(saveCateIds);
        log.error("BrandManageServiceImpl 品牌保存，移除类目id:{}", JSON.toJSONString(needDelIds));
        //全选类目时，使用固定默认值
        if (input.isAllCategoryScope()) {
            saveCateIds.add(Constant.DEFAULT_ALL_CATEGORY_SELECT);
            needDelIds.remove(Constant.DEFAULT_ALL_CATEGORY_SELECT);
        }*/
        //品牌主表
        BrandPO brandPO = BrandConvert.INSTANCE.vo2po(input);
        //品牌国家表
        BrandCountryPO brandCountryPO = BrandCountryConvert.INSTANCE.vo2Po(input.getBrandCountry());
        //品牌多语言
        List<BrandLangPO> brandLangPOList = BrandConvert.INSTANCE.listLangVO2LangPO(input.getLangList());
        Date current = new Date();
        boolean isNew = Objects.isNull(brandPO.getId());
        if (isNew) {
            brandPO.setCreator(input.getCreator());
            brandPO.setCreateTime(current);
        }
        brandPO.setUpdater(input.getUpdater());
        brandPO.setUpdateTime(current);
        brandPO.setAuditStatus(BrandAuditStatusEnum.PENDING.getCode());
        boolean brandRes = brandAtomicService.saveOrUpdate(brandPO);
        Assert.isTrue(brandRes, "保存品牌主表异常");
        doSaveBrandCountry(brandCountryPO, current, input, brandPO);
        doSaveBrandLang(brandLangPOList, current, input, brandPO);
        //doSaveBrandCategory(saveCateIds, brandPO, input, existCateIds,needDelIds);
/*        if (isNew) {
            brandPO.setCreator(input.getCreator());
            brandPO.setCreateTime(current);
            brandPO.setUpdater(input.getUpdater());
            brandPO.setUpdateTime(current);
            brandPO.setAuditStatus(BrandAuditStatusEnum.PENDING.getCode());
            boolean brandRes = brandAtomicService.saveOrUpdate(brandPO);
            Assert.isTrue(brandRes, "保存品牌主表异常");
            // 创建待审核 走xbp create
            input.setId(brandPO.getId());
            this.firstCreatBrandXbp(input);
        }else {
            // 修改品牌待审核 走xbp modify  存草稿  记录审核数据
            this.modifyBrandXbp(input);
        }*/
        return DataResponse.success(true);
    }

    private DataResponse<Boolean> checkInput(BrandVO input) {
        // 校验授权国家
        BrandCountryVO brandCountry = input.getBrandCountry();
        if (Objects.nonNull(brandCountry) && CountryConstant.COUNTRY_ZH.equals(brandCountry.getCountryCode()) && StringUtils.isBlank(brandCountry.getAuthCountryCode())) {
            return DataResponse.error("注册国为中国，品牌授权可售国家及地区必填");
        }
        // 校验授权方式
        if (Objects.nonNull(brandCountry) && CountryConstant.COUNTRY_ZH.equals(brandCountry.getCountryCode()) && StringUtils.isBlank(brandCountry.getAuthType())) {
            return DataResponse.error("注册国为中国，跨境品牌国家授权方式必填");
        }
        // 过滤掉品牌名称为空的数据
        List<BrandLangVO> langList = input.getLangList().stream().filter(Objects::nonNull).filter(brandLangVO -> StringUtils.isNotBlank(brandLangVO.getLangName())).collect(Collectors.toList());
        input.setLangList(langList);
        // 校验中文、英文名称必填
        boolean noneMatch = langList.stream().filter(Objects::nonNull).noneMatch(brandLangVO ->
                (LangConstant.LANG_EN.equals(brandLangVO.getLang()) || LangConstant.LANG_ZH.equals(brandLangVO.getLang()) ) && StringUtils.isNotBlank(brandLangVO.getLangName()));
        if (noneMatch) {
            return DataResponse.error("中文、英文品牌名称必填");
        }

        BrandPageVO.Request param = new BrandPageVO.Request();
        param.setBrandId(input.getId());
        StringBuilder errorMsg = new StringBuilder();
        for (BrandLangVO brandLang : langList) {
            param.setBrandLangList(Collections.singletonList(brandLang));
            boolean result = checkLangName(param);
            if (!result) {
                errorMsg.append(brandLang.getLangName()).append("名称重复,");
            }
        }
        if (errorMsg.length() > 0) {
           String error = errorMsg.substring(0, errorMsg.length());
            return DataResponse.buildError(error);
        }
        return DataResponse.success();
    }


    private void handleCountry(BrandVO input) {
        BrandCountryVO brandCountry = input.getBrandCountry();
        if (brandCountry.getAuthCountryCode().contains(CountryConstant.GLOBAL)) {
            brandCountry.setAuthCountryCode(CountryConstant.GLOBAL);
        }
    }

    private void firstCreatBrandXbp(BrandVO input) {
        DataResponse<Integer> approval = xbpTicketBrandService.createBrandXbpForApproval(input);
        if (!approval.getSuccess()) {
            log.error("BrandManageServiceImpl.saveOrUpdate 创建品牌创建审批流失败,input={},approval={}",JSON.toJSONString(input),JSON.toJSONString(approval));
            throw new RuntimeException("创建品牌创建审批流失败,brandId=" + input.getId());
        }

        this.addBrandAuditRecord(input, approval.getData());
    }

    private void addBrandAuditRecord(BrandVO input,Integer xbpTicketId) {
        BrandAuditRecordVO auditRecordVO = new BrandAuditRecordVO();
        auditRecordVO.setBrandId(input.getId());
        auditRecordVO.setXbpTicketId(xbpTicketId);
        auditRecordVO.setAuditErp(input.getCreator());
        auditRecordVO.setAuditStatus(BrandAuditStatusEnum.PENDING.getCode());
        auditRecordVO.setCreator(input.getCreator());
        auditRecordVO.setUpdater(input.getUpdater());
        long now = System.currentTimeMillis();
        auditRecordVO.setCreateTime(now);
        auditRecordVO.setUpdateTime(now);
        auditRecordVO.setYn(YnEnum.YES.getCode());
        brandAuditRecordManageService.addBrandAuditRecord(auditRecordVO);
    }


    private void modifyBrandXbp(BrandVO input) {
        BrandDraftVO brandDraftVO = brandDraftManageService.getBrandDraftVo(input.getId());
        // 保存草稿
        if (Objects.isNull(brandDraftVO)) {
            brandDraftVO = new BrandDraftVO();
            brandDraftVO.setBrandId(input.getId());
            brandDraftVO.setBrandJsonInfo(JSON.toJSONString(input));
            brandDraftVO.setCreator(input.getCreator());
            brandDraftVO.setUpdater(input.getUpdater());
            long now = System.currentTimeMillis();
            brandDraftVO.setCreateTime(now);
            brandDraftVO.setUpdateTime(now);
            brandDraftVO.setYn(YnEnum.YES.getCode());
        }else {
            brandDraftVO.setBrandJsonInfo(JSON.toJSONString(input));
            brandDraftVO.setUpdater(input.getCreator());
            brandDraftVO.setUpdateTime(System.currentTimeMillis());
        }
        brandDraftManageService.saveOrUpdate(brandDraftVO);


        BrandVO originBrandVO = this.detail(input);
        DataResponse<Integer> modifyApproval = xbpTicketBrandService.modifyBrandXbpForApproval(input, originBrandVO);
        if (!modifyApproval.getSuccess()) {
            log.error("BrandManageServiceImpl.saveOrUpdate 修改品牌创建审批流失败,input={},approval={}",JSON.toJSONString(input),JSON.toJSONString(modifyApproval));
            throw new RuntimeException("修改品牌创建审批流失败,brandId=" + input.getId());
        }else {
            this.addBrandAuditRecord(input, modifyApproval.getData());
        }
    }

    /**
     * 保存或更新品牌类目关系表
     */
    private void doSaveBrandCategory(Set<Long> saveCateIds, BrandPO brandPO, BrandVO input, Map<Long, Long> existCateIds, Set<Long> needDelIds) {

        if (!needDelIds.isEmpty()) {
            Set<Long> ids = needDelIds.stream().map(existCateIds::get).collect(Collectors.toSet());
            boolean batchDel = brandCategoryAtomicService.removeBatchByIds(ids);
            Assert.isTrue(batchDel,"删除品牌类目关系失败，id："+ JSONObject.toJSONString(ids));
        }
        long timestamp = System.currentTimeMillis();
        List<BrandCategoryPO> saveOrUpdateList = new ArrayList<>();
        for (Long cate : saveCateIds) {
            BrandCategoryPO saveBrandCate = new BrandCategoryPO();
            saveBrandCate.setBrandId(brandPO.getId());
            saveBrandCate.setCatId(cate);
            saveBrandCate.setYn(YnEnum.YES.getCode());
            saveBrandCate.setUpdateTime(timestamp);
            saveBrandCate.setCreateTime(timestamp);
            saveBrandCate.setUpdater(input.getUpdater());
            saveBrandCate.setCreator(input.getCreator());
            if (existCateIds.containsKey(cate)) {
                Long id = existCateIds.get(cate);
                saveBrandCate.setId(id);
                saveBrandCate.setCreateTime(null);
                saveBrandCate.setCreator(null);
                saveBrandCate.setBrandId(null);
            }
            saveOrUpdateList.add(saveBrandCate);
        }
        boolean batchBrandCateRes = brandCategoryAtomicService.saveOrUpdateBatch(saveOrUpdateList);
        Assert.isTrue(batchBrandCateRes, "保存品牌类目关系表异常");
    }

    /**
     * 保存或更新品牌多语言信息
     */
    private void doSaveBrandLang(List<BrandLangPO> brandLangPOList, Date current, BrandVO input, BrandPO brandPO) {
        brandLangPOList.stream()
                .filter(Objects::nonNull)
                .filter(brandLangPO -> StringUtils.isNotBlank(brandLangPO.getLangName()))
                .forEach(b -> {
            if (Objects.isNull(b.getId())) {
                b.setCreator(input.getCreator());
                b.setCreateTime(current);
            }
            b.setBrandId(brandPO.getId());
            b.setUpdater(input.getUpdater());
            b.setUpdateTime(current);
        });
        boolean batchRes = brandLangAtomicService.saveOrUpdateBatch(brandLangPOList);
        Assert.isTrue(batchRes, "保存品牌多语言表异常");
    }

    /**
     * 保存或更新品牌国家信息
     */
    private void doSaveBrandCountry(BrandCountryPO brandCategoryPO, Date current, BrandVO input, BrandPO brandPO) {

        if (Objects.isNull(brandCategoryPO.getId())) {
            brandCategoryPO.setCreator(input.getCreator());
            brandCategoryPO.setCreateTime(current);
            brandCategoryPO.setBrandId(brandPO.getId());
            brandCategoryPO.setUpdater(input.getUpdater());
            brandCategoryPO.setUpdateTime(current);
            boolean brandCountryRes = brandCountryAtomicService.save(brandCategoryPO);
            Assert.isTrue(brandCountryRes, "保存品牌国家表异常");
            return;
        }

        //修改时使用set方式，是为了设置授权时间authDate为null，
        LambdaUpdateWrapper<BrandCountryPO> updateWrapper = Wrappers.<BrandCountryPO>lambdaUpdate()
                .set(BrandCountryPO::getCountryCode,brandCategoryPO.getCountryCode())
                .set(BrandCountryPO::getAuthCountryCode,brandCategoryPO.getAuthCountryCode())
                .set(BrandCountryPO::getAuthCert,brandCategoryPO.getAuthCert())
                .set(BrandCountryPO::getAuthType,brandCategoryPO.getAuthType())
                .set(BrandCountryPO::getAuthDate,brandCategoryPO.getAuthDate())
                .set(BrandCountryPO::getRegisterCert,brandCategoryPO.getRegisterCert())
                .set(BrandCountryPO::getQualificationCert,brandCategoryPO.getQualificationCert())
                .set(BrandCountryPO::getDescription,brandCategoryPO.getDescription())
                .set(BrandCountryPO::getUpdater,input.getUpdater())
                .set(BrandCountryPO::getUpdateTime,current)
                .eq(BrandCountryPO::getId, brandCategoryPO.getId());
        boolean update = brandCountryAtomicService.update(updateWrapper);
        Assert.isTrue(update, "修改品牌国家表异常");


    }

    @Override
    public BrandVO detail(BrandVO param) {
        BrandVO result = new BrandVO();
        BrandPO brandPO = brandAtomicService.queryBrandPOById(param.getId());
        if (Objects.isNull(brandPO)) {
            return result;
        }
        BrandVO brandVo = BrandConvert.INSTANCE.po2vo(brandPO);
        BrandCountryPO brandCountryPO = brandCountryAtomicService.queryByBrandId(param.getId());
        brandVo.setBrandCountry(BrandCountryConvert.INSTANCE.po2Vo(brandCountryPO));
        Set<Long> brandIds = new HashSet<>();
        brandIds.add(param.getId());
        List<BrandLangPO> brandLangPOList = brandLangAtomicService.queryNameByIds(brandIds);
        brandVo.setLangList(BrandConvert.INSTANCE.langListPo2dto(brandLangPOList));
/*        Map<Long, Long> brandCateIds = getAllBrandCateIds(param.getId());
        if (MapUtils.isNotEmpty(brandCateIds)) {
            if (brandCateIds.containsKey(Constant.DEFAULT_ALL_CATEGORY_SELECT)) {
                brandVo.setAllCategoryScope(true);
                return brandVo;
            }
            Map<Long, List<CategoryComboBoxVO>> longListMap = categoryOutService.queryPath(brandCateIds.keySet(), LangConstant.LANG_ZH);
            fillCategoryInfo(longListMap, brandVo);
            brandVo.setAllCategoryScope(false);
        }*/
        return brandVo;
    }

    @Override
    public PageInfo<BrandPageVO.Response> pageSearch(BrandPageVO.Request input) {

        PageInfo<BrandPageVO.Response> pageInfo = new PageInfo<>();
        pageInfo.setSize(input.getSize());
        pageInfo.setIndex(input.getIndex());

        Set<Long> brandIds = new HashSet<>();
        input.setBrandIds(brandIds);

        //多语言查询
        if (CollectionUtils.isNotEmpty(input.getBrandLangList())) {
            BrandLangVO brandLangVO = input.getBrandLangList().get(0);
            List<BrandLangPO> brandLang = brandLangAtomicService.queryByNameAndLang(brandLangVO);
            if (CollectionUtils.isEmpty(brandLang)) {
                return pageInfo;
            }
            brandIds.addAll(brandLang.stream().map(BrandLangPO::getBrandId).collect(Collectors.toSet()));
        }

        //类目id
        if (Objects.nonNull(input.getCategoryId())){
            Map<Long, Long> brandIdsMap = getAllBrandByCateId(input.getCategoryId());
            if (brandIdsMap.isEmpty()) {
                return pageInfo;
            }
            brandIds.addAll(brandIdsMap.keySet());
        }

        if (Objects.nonNull(input.getBrandId())) {
            if (!brandIds.isEmpty() && !brandIds.contains(input.getBrandId())) {
                return pageInfo;
            }
            brandIds.add(input.getBrandId());
        }

        long total = brandAtomicService.globalBrandTotal(input);
        if (total <= 0) {
            return pageInfo;
        }
        List<BrandPageVO.Response> responses = brandAtomicService.globalBrandSearch(input);
        pageInfo.setTotal(total);
        pageInfo.setRecords(responses);
        if (CollectionUtils.isNotEmpty(responses)) {
            //查询全量国家
            Map<String, String> countryMap = countryManageService.getCountryMap(null);

            Set<Long> brandIdSet = responses.stream().map(BaseVO::getId).collect(Collectors.toSet());
            //品牌多语言
            Map<Long, List<BrandLangPO>> longListMap = brandLangAtomicService.queryNameByIdsAndLang(brandIdSet, null);
            //国家品牌关系
            List<BrandCountryPO> brandCountryPO = brandCountryAtomicService.queryByBrandIds(brandIdSet);
            List<BrandCountryVO> countryVoList = BrandCountryConvert.INSTANCE.listPo2Vo(brandCountryPO);
            Map<Long, BrandCountryVO> countryBrandMap = countryVoList.stream().peek(c -> c.setCountryName(countryMap.get(c.getCountryCode()))).collect(HashMap::new, (m, v) -> m.put(v.getBrandId(), v), HashMap::putAll);
            //品牌类目
            //Map<Long,Set<Long>> brandCateMap = brandCategoryAtomicService.queryByBrandIdsWithLimit(brandIdSet);
            responses.forEach(r -> {
                BrandCountryVO brandCountryVO = countryBrandMap.get(r.getId());
                r.setBrandCountryVO(brandCountryVO);
                List<BrandLangPO> brandLangPoList = longListMap.get(r.getId());
                List<BrandLangVO> langVo = BrandConvert.INSTANCE.langListPo2dto(brandLangPoList);
                r.setBrandLangList(langVo);
                /*Set<Long> categoryIds = brandCateMap.get(r.getId());
                if (CollectionUtils.isNotEmpty(categoryIds)) {
                    if (categoryIds.contains(Constant.DEFAULT_ALL_CATEGORY_SELECT)){
                        r.setAllCategoryScope(true);
                        return;
                    }
                    Map<Long, String> cateNameMap = categoryOutService.queryPathStr(categoryIds, LangConstant.LANG_ZH);
                    r.setCategoryFullNameList(new ArrayList<>(cateNameMap.values()));

                }*/
            });

        }
        return pageInfo;
    }


    @Override
    public List<BrandVO> queryExpiredBrand(Long expiredStartTime,Long expiredEndTime) {
        Page<BrandCountryPO> page = new Page<>(1,Constant.BATCH_SIZE);
        Page<BrandCountryPO> pageInfo = queryBrandCountryPage(expiredStartTime, expiredEndTime, page);
        if (pageInfo.getTotal() <= 0 || CollectionUtils.isEmpty(pageInfo.getRecords())) {
            return Collections.emptyList();
        }
        List<BrandVO> result = new ArrayList<>();
        result.addAll(buildBrandLangInfo(pageInfo.getRecords(),LangConstant.LANG_EN));
        long pageTotal = CommonUtils.calculateTotalPages(pageInfo.getTotal(),pageInfo.getSize());
        long index = 2;
        while (pageTotal >= index) {
            page.setCurrent(index);
            pageInfo = queryBrandCountryPage(expiredStartTime, expiredEndTime, page);;
            if (CollectionUtils.isEmpty(pageInfo.getRecords())){
                break;
            }
            result.addAll(buildBrandLangInfo(pageInfo.getRecords(),LangConstant.LANG_EN));
            index++;
        }

        return result;
    }

    @Override
    public boolean checkLangName(BrandPageVO.Request request) {

        if (CollectionUtils.isEmpty(request.getBrandLangList())) {
            return false;
        }

        BrandLangVO langVO = request.getBrandLangList().get(0);
        langVO.setLangName(langVO.getLangName().trim());
        List<BrandLangPO> brandLang = brandLangAtomicService.queryByNameAndLangEq(langVO);
        if (CollectionUtils.isEmpty(brandLang)) {
            return true;
        }

        if (brandLang.size() == 1) {
            return brandLang.get(0).getBrandId().equals(request.getBrandId());
        }
        return false;
    }

    @Override
    public List<BrandCountryVO> queryBrandCountry(BrandCountryVO brandCountryVO) {
        log.info("BrandManageServiceImpl.queryBrandCountry req:{}",JSON.toJSONString(brandCountryVO));
        List<BrandCountryPO> brandCountryPOList = brandCountryAtomicService.queryByUpdateTime(brandCountryVO);
        List<BrandCountryVO> brandCountryVOList = BrandCountryConvert.INSTANCE.listPo2Vo(brandCountryPOList);
        log.info("BrandManageServiceImpl.queryBrandCountry req:{},res:{}",JSON.toJSONString(brandCountryVO),JSON.toJSONString(brandCountryVOList));
        return brandCountryVOList;
    }

    @Resource
    private BrandOutService brandOutService;

    @Override
    public <T> void setBrandName(List<T> list, String brandIdFieldName, String brandNameFieldName) {

        if(CollectionUtils.isEmpty(list)){
            return;
        }

        T temp = list.get(0);

        if (!ReflectUtil.hasField(temp.getClass(), brandIdFieldName)) {
            log.warn("setCatIdAndName， 对象未含有字段，jdCatIdFieldName={}", brandIdFieldName);
            throw new ProductBizException("对象未含有字段，%s", brandIdFieldName);
        }

        if (!ReflectUtil.hasField(temp.getClass(), brandNameFieldName)) {
            log.warn("setCatIdAndName， 对象未含有字段，jdCatNameFieldName={}", brandNameFieldName);
            throw new ProductBizException("对象未含有字段，%s", brandNameFieldName);
        }

        Preconditions.checkArgument(StringUtils.isNotEmpty(brandIdFieldName), "jdCatIdFieldName is ont null");

        Set<Long> brandIds = Sets.newHashSet();
        for (T obj : list) {
            Long jdCatId = this.getBrandIdValue(obj, brandIdFieldName);
            if (jdCatId == null) {
                continue;
            }

            brandIds.add(jdCatId);
        }

        if (CollectionUtils.isEmpty(brandIds)) {
            log.warn("lastCatIds is empty");
            return;
        }

        Map<Long, String> brandMap = brandOutService.queryNameByIds(brandIds, LangContextHolder.get());
        if (MapUtils.isEmpty(brandMap)) {
            return;
        }
        list.forEach(item -> {
            String brandName = brandMap.get(this.getBrandIdValue(item, brandIdFieldName));
            if (brandName != null) {
                ReflectUtil.setFieldValue(item, brandNameFieldName, brandName);
            }
        });
    }

    private List<BrandVO> buildBrandLangInfo(List<BrandCountryPO> records, String lang) {
        List<BrandCountryVO> brandCountryList = BrandCountryConvert.INSTANCE.listPo2Vo(records);
        Set<Long> brandIds = records.stream().map(BrandCountryPO::getBrandId).collect(Collectors.toSet());
        List<BrandLangPO> brandLangList = brandLangAtomicService.queryNameByIds(brandIds, lang);
        Map<Long, List<BrandLangPO>> langMap = brandLangList.stream().collect(Collectors.groupingBy(BrandLangPO::getBrandId));
        List<BrandVO> result = new ArrayList<>();
        for (BrandCountryVO brandCountryVO : brandCountryList) {
            BrandVO brandVO = new BrandVO();
            brandVO.setId(brandCountryVO.getBrandId());
            brandVO.setAuthDate(brandCountryVO.getAuthDate());
            List<BrandLangPO> brandLang = langMap.get(brandCountryVO.getBrandId());
            List<BrandLangVO> brandLangVOS = BrandConvert.INSTANCE.langListPo2dto(brandLang);
            brandVO.setBrandCountry(brandCountryVO);
            brandVO.setLangList(brandLangVOS);
            result.add(brandVO);
        }
        return result;
    }

    private <T> Long getBrandIdValue(T obj, String brandIdFieldName) {
        Object brandId = ReflectUtil.getFieldValue(obj, brandIdFieldName);

        if (brandId == null) {
            return null;
        }

        if (!(brandId instanceof Long) && !(brandId instanceof Integer)) {
            log.warn("setCatIdAndName， 字段类型错误，brandIdFieldName={}", brandIdFieldName);
            return null;
        }
        return (long) brandId;
    }


    //TODO 判断生效状态
    /**
     * 查询品牌国家的分页信息，根据授权日期范围和有效性过滤
     */
    private Page<BrandCountryPO> queryBrandCountryPage(Long expiredStartTime,Long expiredEndTime,Page<BrandCountryPO> page) {
        LambdaQueryWrapper<BrandCountryPO> wrapper = Wrappers.<BrandCountryPO>lambdaQuery()
                .lt(Objects.nonNull(expiredEndTime),BrandCountryPO::getAuthDate, expiredEndTime)
                .ge(Objects.nonNull(expiredStartTime),BrandCountryPO::getAuthDate, expiredStartTime)
                .eq(BrandCountryPO::getYn, YnEnum.YES.getCode());
        return brandCountryAtomicService.page(page, wrapper);
    }


    private Map<Long, Long> getAllBrandCateIds(Long brandId) {
        BrandCategoryPageVO.Request request = new BrandCategoryPageVO.Request();
        request.setBrandId(brandId);
        request.setIndex(1L);
        request.setSize(Constant.BATCH_SIZE);
        PageInfo<BrandCategoryPageVO.Response> pageInfo = brandCategoryAtomicService.pageQuery(request);
        Map<Long, Long> result = new HashMap<>(getIdAndCateId(pageInfo.getRecords()));
        long pageTotal = CommonUtils.calculateTotalPages(pageInfo.getTotal(), pageInfo.getSize());
        long index = 2;
        while (pageTotal >= index) {
            request.setIndex(index);
            pageInfo = brandCategoryAtomicService.pageQuery(request);
            if (CollectionUtils.isEmpty(pageInfo.getRecords())) {
                break;
            }
            result.putAll(getIdAndCateId(pageInfo.getRecords()));
            index++;
        }
        return result;

    }
    private Map<Long, Long> getAllBrandByCateId(Long categoryId) {
        Map<Long, Long> result = new HashMap<>();
        BrandCategoryPageVO.Request request = new BrandCategoryPageVO.Request();
        request.setCatId(categoryId);
        request.setIndex(1L);
        request.setSize(Constant.BATCH_SIZE);
        PageInfo<BrandCategoryPageVO.Response> pageInfo = brandCategoryAtomicService.pageQuery(request);
        if (pageInfo.getTotal() <= 0 || CollectionUtils.isEmpty(pageInfo.getRecords())) {
            return result;
        }
        result.putAll(pageInfo.getRecords().stream().collect(HashMap::new,(m,v)->m.put(v.getBrandId(),v.getCatId()),HashMap::putAll));

        long pageTotal = CommonUtils.calculateTotalPages(pageInfo.getTotal(), pageInfo.getSize());
        long index = 2;
        while (pageTotal >= index) {
            request.setIndex(index);
            pageInfo = brandCategoryAtomicService.pageQuery(request);
            if (CollectionUtils.isEmpty(pageInfo.getRecords())) {
                break;
            }
            result.putAll(pageInfo.getRecords().stream().collect(HashMap::new,(m,v)->m.put(v.getBrandId(),v.getCatId()),HashMap::putAll));
            index++;
        }
        return result;
    }

    private Map<Long, Long> getIdAndCateId(List<BrandCategoryPageVO.Response> records) {
        if (CollectionUtils.isNotEmpty(records)) {
            return records.stream().collect(HashMap::new, (m, v) -> m.put(v.getCatId(), v.getId()), HashMap::putAll);
        }
        return new HashMap<>();
    }


    private void fillCategoryInfo(Map<Long, List<CategoryComboBoxVO>> longListMap, BrandVO result) {
        List<CategoryTreeVo> categoryTree = categoryTreeBuilder.buildCategoryTree(longListMap);
        log.error("categoryTree:{}", JSONObject.toJSONString(categoryTree));
        List<CategoryTreeVo> categoryChildrenFullNode = categoryTreeBuilder.getCategoryChildrenFullNode(categoryTree);
        log.error("categoryChildrenFullNode:{}", JSONObject.toJSONString(categoryChildrenFullNode));
        List<CategoryTreeVo> selectCategory = categoryTreeBuilder.getSelectCategory(categoryChildrenFullNode);
        log.error("selectCategory:{}", JSONObject.toJSONString(selectCategory));
        result.setSelectCategoryNodes(selectCategory);
        result.setCategoryTreeList(categoryChildrenFullNode);
    }

}
