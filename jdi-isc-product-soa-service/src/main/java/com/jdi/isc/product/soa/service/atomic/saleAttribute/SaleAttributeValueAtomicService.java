package com.jdi.isc.product.soa.service.atomic.saleAttribute;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeValuePO;
import com.jdi.isc.product.soa.repository.mapper.saleAttribute.SaleAttributeValueBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售属性值原子服务
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
public class SaleAttributeValueAtomicService extends ServiceImpl<SaleAttributeValueBaseMapper, SaleAttributeValuePO> {

    /**
     * 根据ID查询有效的销售属性值
     * @param id 销售属性值ID
     * @return 销售属性值实体
     */
    public SaleAttributeValuePO getValidById(Long id) {
        LambdaQueryWrapper<SaleAttributeValuePO> queryWrapper = new LambdaQueryWrapper<SaleAttributeValuePO>()
                .eq(SaleAttributeValuePO::getId, id)
                .eq(SaleAttributeValuePO::getYn, YnEnum.YES.getCode());
        return super.getOne(queryWrapper);
    }

    /**
     * 根据spuId查询有效的销售属性值
     * @param spuId spuId
     * @return 销售属性值列表
     */
    public List<SaleAttributeValuePO> getSaleAttributeValueBySpuId(Long spuId) {
        LambdaQueryWrapper<SaleAttributeValuePO> queryWrapper = new LambdaQueryWrapper<SaleAttributeValuePO>()
                .eq(SaleAttributeValuePO::getSpuId, spuId)
                .eq(SaleAttributeValuePO::getYn, YnEnum.YES.getCode());
        return super.list(queryWrapper);
    }

    /**
     * 根据销售属性ID查询有效的销售属性值列表
     * @param saleAttributeId 销售属性ID
     * @return 销售属性值列表
     */
    public List<SaleAttributeValuePO> getValidBySaleAttributeId(Long saleAttributeId) {
        LambdaQueryWrapper<SaleAttributeValuePO> queryWrapper = new LambdaQueryWrapper<SaleAttributeValuePO>()
                .eq(SaleAttributeValuePO::getSaleAttributeId, saleAttributeId)
                .eq(SaleAttributeValuePO::getYn, YnEnum.YES.getCode())
                .orderByAsc(SaleAttributeValuePO::getSort);
        return super.list(queryWrapper);
    }

    /**
     * 批量查询有效的销售属性值
     * @param ids 销售属性值ID列表
     * @return 销售属性值列表
     */
    public List<SaleAttributeValuePO> getValidByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<SaleAttributeValuePO> queryWrapper = new LambdaQueryWrapper<SaleAttributeValuePO>()
                .in(SaleAttributeValuePO::getId, ids)
                .eq(SaleAttributeValuePO::getYn, YnEnum.YES.getCode())
                .orderByAsc(SaleAttributeValuePO::getSort);
        return super.list(queryWrapper);
    }

    /**
     * 保存销售属性值列表，自动处理重复检查和排序
     * @param spuId 销售属性值所属的spuId
     * @param saleAttributeValuePOList 待保存的销售属性值列表
     * @return 实际保存的销售属性值列表（包含生成的ID）
     */
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public List<SaleAttributeValuePO> saveWithSortAndReturnWithId(Long spuId, List<SaleAttributeValuePO> saleAttributeValuePOList) {
        log.info("SaleAttributeValueAtomicService.saveWithSortAndReturnWithId 开始保存销售属性值, 入参数量: {}", 
                saleAttributeValuePOList != null ? saleAttributeValuePOList.size() : 0);
        
        if (CollectionUtils.isEmpty(saleAttributeValuePOList) || spuId == null) {
            log.error("SaleAttributeValueAtomicService.saveWithSortAndReturnWithId 入参为空 spuId: {}", spuId);
            return new ArrayList<>();
        }

        // spuId赋值到saleAttributeValuePOList中
        saleAttributeValuePOList.forEach(po -> po.setSpuId(spuId));

        // 保存销售属性值
        boolean saveSuccess = super.saveBatch(saleAttributeValuePOList);
        if (saveSuccess) {
            log.info("SaleAttributeValueAtomicService.saveWithSortAndReturnWithId 保存销售属性值成功, spuId: {}, 新增: {}", spuId, JSON.toJSONString(saleAttributeValuePOList));
        } else {
            log.error("SaleAttributeValueAtomicService.saveWithSortAndReturnWithId 保存销售属性值失败, spuId: {}", spuId);
        }
        // saveBatch后已经包含id
        return saleAttributeValuePOList;
    }

}