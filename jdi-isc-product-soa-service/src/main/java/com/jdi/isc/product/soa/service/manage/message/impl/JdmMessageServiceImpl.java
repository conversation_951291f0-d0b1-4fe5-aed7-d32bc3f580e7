package com.jdi.isc.product.soa.service.manage.message.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.jdm.push.req.JdmSupplierMessageReqDTO;
import com.jdi.isc.product.soa.common.exception.PushMsgException;
import com.jdi.isc.product.soa.domain.push.PushMessageVO;
import com.jdi.isc.product.soa.rpc.vc.PinMessageRpcService;
import com.jdi.isc.product.soa.rpc.vc.VCUserQueryRpcService;
import com.jdi.isc.product.soa.service.manage.message.JdmMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 推送消息实现服务
 * <AUTHOR>
 * @date 2024/7/9
 **/
@Slf4j
@Service
public class JdmMessageServiceImpl implements JdmMessageService {

    // 查询供应商pin接口
    @Resource
    private VCUserQueryRpcService vcUserQueryRpcService;
    // 推送消息接口
    @Resource
    private PinMessageRpcService pinMessageRpcService;

    private static final int SIZE = 20;

    @LafValue("jdi.isc.product.soa.message.switch")
    private Boolean messageSwitch;


    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> pushMessageToJm(JdmSupplierMessageReqDTO reqDTO) {

        List<JdmSupplierMessageReqDTO.JdmSupplierMessageDTO> messageDTOList = reqDTO.getJdmSupplierMessageDTOList();
        if (CollectionUtils.isEmpty(messageDTOList)) {
            return DataResponse.success();
        }

        if (messageSwitch){
            List<CompletableFuture<Void>> completableFutureList = Lists.newArrayList();
            for (JdmSupplierMessageReqDTO.JdmSupplierMessageDTO messageDTO : messageDTOList) {
                completableFutureList.add(CompletableFuture.runAsync(() -> this.pushMessageByVendorCode(messageDTO)));
            }
            // 等待发送结果
            CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).join();
        }
        return DataResponse.success();
    }

    private void pushMessageByVendorCode(JdmSupplierMessageReqDTO.JdmSupplierMessageDTO messageDTO) {
        // 查询供应商pin集合
        Set<String> pinSet = vcUserQueryRpcService.queryPinsByVendorCode(messageDTO.getJdVendorCode());
        if (pinSet.size() <= SIZE){
            this.pushMessageByPins(messageDTO.getMessage(),messageDTO.getUrl(), pinSet);
        }else {
            ArrayList<String> pinList = Lists.newArrayList(pinSet);
            List<List<String>> partitionList = Lists.partition(pinList, SIZE);
            for (List<String> subList : partitionList) {
                this.pushMessageByPins(messageDTO.getMessage(),messageDTO.getUrl(),  Sets.newHashSet(subList));
            }
        }
    }

    private void pushMessageByPins(String message,String url, Set<String> pinSet) {
        List<Map<String,String>>  pinMapList = Lists.newArrayList();

        pinSet.forEach(pin -> {
            Map<String,String> map = new HashMap<>(1);
            map.put("pin", pin);
            pinMapList.add(map);
        });
        // 发送消息
        PushMessageVO pushMessageVO = new PushMessageVO();
        PushMessageVO.Property property = new PushMessageVO.Property();
        property.setContent(message);
        if (StringUtils.isNotBlank(url)) {
            property.setUrlPC(url);
        }
        pushMessageVO.setProperty(property);
        pushMessageVO.setPinlist(pinMapList);
        String pushMsg = pinMessageRpcService.pushMsg(pushMessageVO);
        if (!DataResponseCode.SUCCESS.getMessage().equals(pushMsg)) {
            throw new PushMsgException("发送消息异常，pushMessageVO="+ JSON.toJSONString(pushMessageVO));
        }
    }
}
