package com.jdi.isc.product.soa.service.manage.price.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.util.CommonUtils;
import com.jdi.isc.product.soa.domain.price.biz.FulfillmentRateReqVO;
import com.jdi.isc.product.soa.domain.price.biz.FulfillmentRateVO;
import com.jdi.isc.product.soa.domain.price.po.FulfillmentRatePO;
import com.jdi.isc.product.soa.domain.price.po.PriceLogPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum;
import com.jdi.isc.product.soa.price.api.price.req.FulfillmentRateChangeReqVO;
import com.jdi.isc.product.soa.service.atomic.category.CategoryAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.FulfillmentRateAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.PriceLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.price.FulfillmentRateManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.mapstruct.price.FulfillmentRateConvert;
import com.jdi.isc.product.soa.rpc.suport.AlertHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 履约费率表数据维护服务实现
 * @Author: zhaokun51
 * @Date: 2024/10/29 21:47
 **/
@Slf4j
@Service
public class FulfillmentRateManageServiceImpl extends BaseManageSupportService<FulfillmentRateVO, FulfillmentRatePO> implements FulfillmentRateManageService {

    @Resource
    private FulfillmentRateAtomicService fulfillmentRateAtomicService;
    @Resource
    private CountryManageService countryManageService;
    @Resource
    private CategoryOutService categoryOutService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private CategoryAtomicService categoryAtomicService;
    @Resource
    private SkuPriceManageService skuPriceManageService;
    @Resource
    private SkuReadManageService skuReadManageService;
    @Resource
    private PriceLogAtomicService priceLogAtomicService;

    @Override
    public void updateYn(List<Long> skuIds, String updater) {
        log.info("更新履约费率配置yn=0, skuIds={}, updater={}", skuIds, updater);
        if (CollectionUtils.isEmpty(skuIds)) {
            log.info("更新国家协议加价率yn=0, skuIds is empty");
            return;
        }

        List<FulfillmentRatePO> list = fulfillmentRateAtomicService.listBySkuIds(skuIds);

        List<Long> ids = list.stream().map(FulfillmentRatePO::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            log.info("更新履约费率配置yn=0, ids is empty");
            return;
        }

        LambdaUpdateWrapper<FulfillmentRatePO> wrapper = Wrappers.<FulfillmentRatePO>lambdaUpdate()
                .in(FulfillmentRatePO::getId, ids);
        FulfillmentRatePO update = new FulfillmentRatePO();
        update.setYnAndUpdateInfo(YnEnum.NO.getCode(), updater);

        boolean result = fulfillmentRateAtomicService.update(update, wrapper);
        if (!result) {
            log.error("更新履约费率配置yn=0, 更新失败. id={}, updater={}", ids, updater);
            throw new BizException("更新国家协议加价率失败");
        }
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean saveOrUpdate(FulfillmentRateVO input) {
        log.info("FulfillmentRateManageServiceImpl.saveOrUpdate, input={}", JSONObject.toJSONString(input));
        // 参数业务校验
        this.checkAndInitInput(input);
        // 保存对象
        FulfillmentRatePO po = FulfillmentRateConvert.INSTANCE.vo2Po(input);
        if(input.getCoefficientValue()!=null){
            po.setCoefficientValue(input.getCoefficientValue().divide(Constant.DECIMAL_HUNDRED,6, RoundingMode.HALF_DOWN));
        }
        FulfillmentRatePO dbRatePO = fulfillmentRateAtomicService.getOne(input.getSourceCountryCode(),input.getTargetCountryCode(),input.getFirstCatId(),input.getSecondCatId(),
                input.getThirdCatId(),input.getLastCatId(),input.getSkuId(),input.getIsWarehouseProduct());
        if(input.getId() != null){
            if(dbRatePO != null && !dbRatePO.getId().equals(input.getId())){
                throw new BizException("当前履约费率值已存在，请查询后再变更");
            }
        } else {
            if(dbRatePO != null){
                po.setId(dbRatePO.getId());
            }
        }
        boolean saveRes = fulfillmentRateAtomicService.saveOrUpdate(po);
        if (!saveRes){
            log.warn("FulfillmentRateManageServiceImpl.saveOrUpdate, FulfillmentRatePO fail. FulfillmentRatePO={}", JSONObject.toJSONString(po));
            throw new BizException("保存失败");
        }
        recordLog(po);
        return Boolean.TRUE;
    }

    /** 记录日志*/
    private void recordLog(FulfillmentRatePO po) {
        CompletableFuture.runAsync(()->{
            PriceLogPO target = new PriceLogPO();
            target.setBizId(String.valueOf(po.getId()));
            target.setBizType(PriceTypeEnum.FULFILLMENT_RATE.getCode());
            target.setBizValue(po.getCoefficientValue());
            target.setValue1(JSON.toJSONString(po));
            target.setCreateTime(po.getCreateTime().getTime());
            target.setCreator(po.getCreator());
            target.setUpdateTime(po.getUpdateTime().getTime());
            target.setUpdater(po.getUpdater());
            priceLogAtomicService.save(target);
        });
    }

    @Override
    public Boolean deleteById(Long id) {
        if(id == null){
            log.error("FulfillmentRateManageServiceImpl.deleteById id is null");
            return Boolean.FALSE;
        }
        return fulfillmentRateAtomicService.removeById(id);
    }

    @Override
    public FulfillmentRateVO detail(Long id) {
        if(id == null){
            log.error("FulfillmentRateManageServiceImpl.detail id is null");
            return null;
        }
        FulfillmentRatePO po = fulfillmentRateAtomicService.getValidById(id);
        if (null==po){
            log.info("FulfillmentRateManageServiceImpl.detail, FulfillmentRatePO null. id={}", id);
            return null;
        }

        FulfillmentRateVO fulfillmentRateVO = FulfillmentRateConvert.INSTANCE.po2Vo(po);
        // 设置国家名称
        this.setCountryName(fulfillmentRateVO);
        // 设置分类
        this.setCategoryName(fulfillmentRateVO);
        // 百分位率
        this.setFulfillmentRate(fulfillmentRateVO);

        return fulfillmentRateVO;
    }

    @Override
    public PageInfo<FulfillmentRateVO> pageSearch(FulfillmentRateReqVO input) {
        PageInfo<FulfillmentRateVO> pageInfo = new PageInfo<>();
        pageInfo.setSize(input.getSize());
        pageInfo.setIndex(input.getIndex());

        // 查询列表
        Page<FulfillmentRatePO> pageDB = new Page<>(input.getIndex(), input.getSize());
        // 构建查询条件
        LambdaQueryWrapper<FulfillmentRatePO> wrapper = this.buildWrapper(input);

        Page<FulfillmentRatePO> dbRecord = fulfillmentRateAtomicService.page(pageDB, wrapper);
        pageInfo.setTotal(dbRecord.getTotal());
        if (CollectionUtils.isEmpty(dbRecord.getRecords())){
            return pageInfo;
        }

        List<FulfillmentRateVO> pageList = FulfillmentRateConvert.INSTANCE.listPo2Vo(dbRecord.getRecords());
        if (CollectionUtils.isNotEmpty(pageList)){
            // 设置国家名称
            this.setCountryName(pageList);
            // 设置分类
            this.setCategoryName(pageList);
            // 百分位利润率
            this.setFulfillmentRate(pageList);

            pageInfo.setRecords(pageList);
        }

        return pageInfo;
    }

    @Override
    @PFTracing
    public FulfillmentRatePO getFulfillmentRate(FulfillmentRateVO input) {
        Long start = System.currentTimeMillis();
        Assert.isTrue(
            StringUtils.isNotBlank(input.getSourceCountryCode()) &&
                StringUtils.isNotBlank(input.getTargetCountryCode()) && input.getIsWarehouseProduct()!=null &&
                input.getSkuId()!=null, "履约费率查询货源国、目的国、SKUID、备货模式不能为空");

        Long lastCatId = input.getLastCatId();
        //填充末级类目Id
        if(lastCatId == null){
            SkuPO skuPo = skuAtomicService.getSkuPoBySkuId(input.getSkuId());
            if(skuPo==null){
                log.error("FulfillmentRateManageService.getFulfillmentRate skuId not exist {}", JSON.toJSONString(input));
                return null;
            }
            lastCatId = skuPo.getJdCatId();
        }
        //先用货源国&目的国&SkuId定位履约费率,找不到则使用类目降级逻辑寻找
        FulfillmentRatePO fulfillmentRateBySku = fulfillmentRateAtomicService.getBySkuId(input);
        if(fulfillmentRateBySku!=null){
            log.info("FulfillmentRateManageService.getFulfillmentRate sku规则命中履约费率配置last:{}  req:{} , res:{}" , (System.currentTimeMillis()-start),JSON.toJSONString(input) , JSON.toJSONString(fulfillmentRateBySku));
            return fulfillmentRateBySku;
        }
        input.setCategoryPathVO(categoryOutService.queryPath(new HashSet<>(Collections.singletonList(lastCatId))).get(lastCatId));
        //按类目降级逻辑获取费率
        List<FulfillmentRatePO> fulfillmentList = fulfillmentRateAtomicService.list(input);
        log.info("FulfillmentRateManageServiceImpl.getFulfillmentRate req:{} , res:{}" ,  JSON.toJSONString(input), JSON.toJSONString(fulfillmentList));
        if(CollectionUtils.isEmpty(fulfillmentList)){
            log.error("FulfillmentRateManageService.getFulfillmentRate 未查询到任何履约规则 last{} , {} fulfillmentList is null", (System.currentTimeMillis()-start),JSON.toJSONString(input));
            return null;
        }
        Map<String,FulfillmentRatePO> fulfillmentRatePOMap = fulfillmentList.stream().collect(Collectors.toMap(item->String.join("_"
                ,input.getSourceCountryCode(),input.getTargetCountryCode(),String.valueOf(item.getFirstJdCatId()),String.valueOf(item.getSecondJdCatId())
                ,String.valueOf(item.getThirdJdCatId()),String.valueOf(item.getLastJdCatId())), Function.identity(),(v1, v2)->v2));
        String lastKey = String.join("_",input.getSourceCountryCode(),input.getTargetCountryCode()
                ,String.valueOf(input.getCategoryPathVO().getId1()),String.valueOf(input.getCategoryPathVO().getId2())
                ,String.valueOf(input.getCategoryPathVO().getId3()),String.valueOf(input.getCategoryPathVO().getId4()));
        FulfillmentRatePO fulfillmentRatePO = fulfillmentRatePOMap.get(lastKey);
        if(fulfillmentRatePO != null){
            log.info("FulfillmentRateManageService.getFulfillmentRate 末级类目命中履约费率配置last:{}, req:{} , res:{}" , (System.currentTimeMillis()-start),JSON.toJSONString(input) , JSON.toJSONString(fulfillmentRatePO));
            return fulfillmentRatePO;
        }
        String nullKey = "null";
        String thirdKey = String.join("_",input.getSourceCountryCode(),input.getTargetCountryCode()
                ,String.valueOf(input.getCategoryPathVO().getId1()),String.valueOf(input.getCategoryPathVO().getId2()),String.valueOf(input.getCategoryPathVO().getId3())
                ,nullKey);
        FulfillmentRatePO thirdPO = fulfillmentRatePOMap.get(thirdKey);
        if(thirdPO != null){
            log.info("FulfillmentRateManageService.getFulfillmentRate 三级类目命中履约费率配置 last:{},req:{} , res:{}" , (System.currentTimeMillis()-start),JSON.toJSONString(input) , JSON.toJSONString(thirdPO));
            return thirdPO;
        }
        String secondKey = String.join("_",input.getSourceCountryCode(),input.getTargetCountryCode()
                ,String.valueOf(input.getCategoryPathVO().getId1()),String.valueOf(input.getCategoryPathVO().getId2())
                ,nullKey,nullKey);
        FulfillmentRatePO secondPO = fulfillmentRatePOMap.get(secondKey);
        if(secondPO != null){
            log.info("FulfillmentRateManageService.getFulfillmentRate 二级类目命中履约费率配置 last:{}, req:{} , res:{}" , (System.currentTimeMillis()-start),JSON.toJSONString(input) , JSON.toJSONString(secondPO));
            return secondPO;
        }
        String firstKey = String.join("_",input.getSourceCountryCode(),input.getTargetCountryCode()
                ,String.valueOf(input.getCategoryPathVO().getId1())
                ,nullKey,nullKey,nullKey);
        FulfillmentRatePO firstPO = fulfillmentRatePOMap.get(firstKey);
        if(firstPO != null){
            log.info("FulfillmentRateManageService.getFulfillmentRate 一级类目命中履约费率配置 last:{}, req:{} , res:{}" , (System.currentTimeMillis()-start), JSON.toJSONString(input) , JSON.toJSONString(firstPO));
            return firstPO;
        }
        String countryKey = String.join("_",input.getSourceCountryCode(),input.getTargetCountryCode()
                ,nullKey,nullKey,nullKey,nullKey);
        FulfillmentRatePO countryPo = fulfillmentRatePOMap.get(countryKey);
        if(countryPo != null){
            log.info("FulfillmentRateManageService.getFulfillmentRate 国家维度命中履约费率配置 last:{},req:{} , res:{}" , (System.currentTimeMillis()-start),JSON.toJSONString(input) , JSON.toJSONString(countryPo));
            return countryPo;
        }
        log.error("FulfillmentRateManageService.getFulfillmentRate end. 未命中任何履约费率规则last:{}, {} fulfillmentList is null", (System.currentTimeMillis()-start),JSON.toJSONString(input));
        return null;
    }

    /** 批量获取履约费率*/
    @Override
    @PFTracing
    public Map<Long, FulfillmentRatePO> batchGetFulfillmentRate(List<FulfillmentRateVO> input) {
        Map<Long, FulfillmentRatePO> res = Maps.newHashMapWithExpectedSize(input.size());
        if(CollectionUtils.isEmpty(input)){
            return Collections.emptyMap();
        }
        for(FulfillmentRateVO vo : input){
            res.put(vo.getSkuId(),getFulfillmentRate(vo));
        }
        return res;
    }

    /**
     * 列表查询条件生成
     * @param input 查询参数
     * @return 查询条件Wrapper
     */
    private LambdaQueryWrapper<FulfillmentRatePO> buildWrapper(FulfillmentRateReqVO input){
        LambdaQueryWrapper<FulfillmentRatePO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(CollectionUtils.isNotEmpty(input.getSkuIds()),FulfillmentRatePO::getSkuId,input.getSkuIds());
        wrapper.eq(StringUtils.isNotBlank(input.getSourceCountryCode()),FulfillmentRatePO::getSourceCountryCode,input.getSourceCountryCode());
        wrapper.eq(StringUtils.isNotBlank(input.getTargetCountryCode()),FulfillmentRatePO::getTargetCountryCode,input.getTargetCountryCode());
        wrapper.eq(input.getFirstCatId() != null,FulfillmentRatePO::getFirstJdCatId,input.getFirstCatId());
        wrapper.eq(input.getSecondCatId() != null,FulfillmentRatePO::getSecondJdCatId,input.getSecondCatId());
        wrapper.eq(input.getThirdCatId() != null,FulfillmentRatePO::getThirdJdCatId,input.getThirdCatId());
        wrapper.eq(input.getLastCatId() != null,FulfillmentRatePO::getLastJdCatId,input.getLastCatId());
        wrapper.eq(input.getIsWarehouseProduct() != null,FulfillmentRatePO::getIsWarehouseProduct,input.getIsWarehouseProduct());
        wrapper.eq(FulfillmentRatePO::getYn, YnEnum.YES.getCode());
        wrapper.orderByDesc(FulfillmentRatePO::getUpdateTime);
        return wrapper;
    }

    /**
     * 参数校验
     * @param input 提交参数
     * @return 检查结果
     */
    private void checkAndInitInput(FulfillmentRateVO input){
        if(input.getIsWarehouseProduct()==null){
            throw new BizException("是否备货模式不能为空");
        }
        Map<String,String> countryMap = countryManageService.getCountryMap(LangConstant.LANG_ZH);
        if(!countryMap.containsKey(input.getSourceCountryCode())){
            throw new BizException("商品来源国不合法");
        }
        if(!countryMap.containsKey(input.getTargetCountryCode())){
            throw new BizException("商品销售国不合法");
        }
        if(input.getSkuId()!=null && input.getFixedFulfillmentCost()==null){
            throw new BizException("按sku粒度配置履约费率时固定履约费用必填");
        }
        if(input.getFirstCatId()!=null && input.getCoefficientValue()==null){
            throw new BizException("按类目粒度配置履约费率时货值系数必填");
        }
        if(input.getFirstCatId()!=null && input.getVolumeValue()==null){
            throw new BizException("按类目粒度配置履约费率时体积金额必填");
        }
        if(input.getSkuId()!=null){
            if(!CommonUtils.isIscSku(input.getSkuId())){
                throw new BizException("商品级履约费用配置只能维护国际sku信息,请输入8开头的国际skuId");
            }
        }
    }

    /**
     * 设置国家名称。
     * @param param 对象，包含源国家代码和目标国家代码。
     */
    private void setCountryName(FulfillmentRateVO param){
        if(param == null){
            log.info("FulfillmentRateManageServiceImpl.setCountryName, param is null");
            return;
        }
        Map<String, String> countryNameMap = countryManageService.getCountryMap(LangContextHolder.get());
        param.setSourceCountryName(countryNameMap.get(param.getSourceCountryCode()));
        param.setTargetCountryName(countryNameMap.get(param.getTargetCountryCode()));
    }

    /**
     * 设置国家名称。
     * @param param 对象，包含源国家代码和目标国家代码。
     */
    private void setCategoryName(FulfillmentRateVO param){
        if(param == null){
            log.info("FulfillmentRateManageServiceImpl.setCategoryName, param is null");
            return;
        }
        Set<Long> catIds = new HashSet<>();
        if(param.getFirstCatId() != null){
            catIds.add(param.getFirstCatId());
        }
        if(param.getSecondCatId() != null){
            catIds.add(param.getSecondCatId());
        }
        if(param.getThirdCatId() != null){
            catIds.add(param.getThirdCatId());
        }
        if(param.getLastCatId() != null){
            catIds.add(param.getLastCatId());
        }
        if(CollectionUtils.isEmpty(catIds)){
            log.info("FulfillmentRateManageServiceImpl.setCategoryName, catId is null");
            return;
        }
        String lang = StringUtils.isNotBlank(LangContextHolder.get())?LangContextHolder.get(): LangConstant.LANG_ZH;
        Set<String> langSet = new HashSet<>();
        langSet.add(lang);
        Map<Long, Map<String, String>> catLangMap = categoryOutService.queryCatLangNameMap(catIds, langSet);
        if(param.getFirstCatId() != null){
            Map<String,String> firstLangMap = catLangMap.get(param.getFirstCatId());
            param.setFirstCatName(firstLangMap.get(lang));
        }
        if(param.getSecondCatId() != null){
            Map<String,String> secondLangMap = catLangMap.get(param.getSecondCatId());
            param.setSecondCatName(secondLangMap.get(lang));
        }
        if(param.getThirdCatId() != null){
            Map<String,String> thirdLangMap = catLangMap.get(param.getThirdCatId());
            param.setThirdCatName(thirdLangMap.get(lang));
        }
        if(param.getLastCatId() != null){
            Map<String,String> lastLangMap = catLangMap.get(param.getLastCatId());
            param.setLastCatName(lastLangMap.get(lang));
        }
    }

    /**
     * 设置履约率
     * @param param FulfillmentRateVO 对象，包含需要转换的满足率
     */
    private void setFulfillmentRate(FulfillmentRateVO param){
        if(param == null){
            log.info("FulfillmentRateManageServiceImpl.setProfitRate, param is null");
            return;
        }
        if(param.getCoefficientValue()!=null){
            param.setCoefficientValue(param.getCoefficientValue().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros());
        }
    }

    /**
     * 设置国家名称
     * @param params FulfillmentRateVO列表，包含源国家代码和目标国家代码
     */
    private void setCountryName(List<FulfillmentRateVO> params){
        if(CollectionUtils.isEmpty(params)){
            log.info("FulfillmentRateManageServiceImpl.setCountryName, params is null");
            return;
        }
        Map<String, String> countryNameMap = countryManageService.getCountryMap(LangContextHolder.get());
        params.forEach(item->{
            item.setSourceCountryName(countryNameMap.get(item.getSourceCountryCode()));
            item.setTargetCountryName(countryNameMap.get(item.getTargetCountryCode()));
        });
    }

    /**
     * 设置分类名称
     * @param params FulfillmentRateVO对象列表，用于设置分类名称
     */
    private void setCategoryName(List<FulfillmentRateVO> params){
        if(CollectionUtils.isEmpty(params)){
            log.info("FulfillmentRateManageServiceImpl.setCategoryName, params is null");
            return;
        }

        Set<Long> catIds = new HashSet<>();
        params.forEach(item->{
            if(item.getFirstCatId() != null){
                catIds.add(item.getFirstCatId());
            }
            if(item.getSecondCatId() != null){
                catIds.add(item.getSecondCatId());
            }
            if(item.getThirdCatId() != null){
                catIds.add(item.getThirdCatId());
            }
            if(item.getLastCatId() != null){
                catIds.add(item.getLastCatId());
            }
        });

        if(CollectionUtils.isEmpty(catIds)){
            log.info("FulfillmentRateManageServiceImpl.setCategoryName, catIds is null");
            return;
        }
        String lang = StringUtils.isNotBlank(LangContextHolder.get())?LangContextHolder.get(): LangConstant.LANG_ZH;
        Set<String> langSet = new HashSet<>();
        langSet.add(lang);
        Map<Long, Map<String, String>> catLangMap = categoryOutService.queryCatLangNameMap(catIds, langSet);

        params.forEach(item->{
            if(item.getFirstCatId() != null){
                Map<String,String> firstLangMap = catLangMap.getOrDefault(item.getFirstCatId(), new HashMap<>());
                item.setFirstCatName(firstLangMap.get(lang));
            }
            if(item.getSecondCatId() != null){
                Map<String,String> secondLangMap = catLangMap.getOrDefault(item.getSecondCatId(), new HashMap<>());
                item.setSecondCatName(secondLangMap.get(lang));
            }
            if(item.getThirdCatId() != null){
                Map<String,String> thirdLangMap = catLangMap.getOrDefault(item.getThirdCatId(), new HashMap<>());
                item.setThirdCatName(thirdLangMap.get(lang));
            }
            if(item.getLastCatId() != null){
                Map<String,String> lastLangMap = catLangMap.getOrDefault(item.getLastCatId(), new HashMap<>());
                item.setLastCatName(lastLangMap.get(lang));
            }
        });
    }

    /**
     * 设置履约率。
     * @param params 履约率参数列表。
     */
    private void setFulfillmentRate(List<FulfillmentRateVO> params){
        if(CollectionUtils.isEmpty(params)){
            log.info("FulfillmentRateManageServiceImpl.setFulfillmentRate, params is null");
            return;
        }
        params.stream().filter(line->line.getCoefficientValue()!=null).
                forEach(line-> line.setCoefficientValue(line.getCoefficientValue().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros()));
    }

    /** 履约费率变更触发国家成本价、入仓成本价刷新*/
    @Override
    public Boolean rateChangeToPriceRefresh(List<FulfillmentRateChangeReqVO> input) {
        log.info("FulfillmentRateManageServiceImpl.rateChangeToPriceRefresh input:{} " ,  JSON.toJSONString(input));
        if(CollectionUtils.isEmpty(input)){
            return true;
        }
        for(FulfillmentRateChangeReqVO vo : input){
            // 规则细化到海关编码
            if (StringUtils.isNotBlank(vo.getHsCode())) {
                skuPriceManageService.triggerCostPriceRefresh(getTargetRefreshSkuByHsCode(vo.getHsCode(), vo));
                continue;
            }

            //规则细化到末级类目
            if (vo.getTaxRefundRate() != null) {
                skuPriceManageService.triggerCostPriceRefresh(getTargetRefreshSkuByCat(vo.getTaxRefundRate().getLastCatId(),vo));
                continue;
            }

            //规则细化到skuId
            if(vo.getSkuId()!=null){
                FulfillmentRateChangeReqVO item = new FulfillmentRateChangeReqVO(vo.getSourceCountryCode(), vo.getTargetCountryCode(), vo.getSkuId());
                item.setDataStatusSource(vo.getDataStatusSource());
                skuPriceManageService.triggerCostPriceRefresh(Lists.newArrayList(item));
                continue;
            }
            //规则细化到末级类目
            if(vo.getLastCatId()!=null){
                skuPriceManageService.triggerCostPriceRefresh(getTargetRefreshSkuByCat(vo.getLastCatId(),vo));
                continue;
            }
            //规则细化到三级类目
            if(vo.getThirdCatId()!=null){
                skuPriceManageService.triggerCostPriceRefresh(getTargetRefreshSkuByCat(vo.getThirdCatId(),vo));
                continue;
            }
            //规则细化到二级类目
            if(vo.getSecondCatId()!=null){
                skuPriceManageService.triggerCostPriceRefresh(getTargetRefreshSkuByCat(vo.getSecondCatId(),vo));
                continue;
            }
            //规则细化到一级类目
            if(vo.getFirstCatId()!=null){
                skuPriceManageService.triggerCostPriceRefresh(getTargetRefreshSkuByCat(vo.getFirstCatId(),vo));
            }
        }
        return true;
    }

    private List<FulfillmentRateChangeReqVO> getTargetRefreshSkuByHsCode(String hsCode, FulfillmentRateChangeReqVO vo) {
        List<FulfillmentRateChangeReqVO> result = new ArrayList<>();
        Set<Long> skuIds = skuAtomicService.listSkuIdsByHsCode(hsCode, vo.getSourceCountryCode());

        skuIds.forEach(skuId-> {
            FulfillmentRateChangeReqVO item = new FulfillmentRateChangeReqVO(vo.getSourceCountryCode(), vo.getTargetCountryCode(), skuId, vo.getIsWareHouseProduct());
            item.setDataStatusSource(vo.getDataStatusSource());
            result.add(item);
        });

        if (AlertHelper.isDevOrTest()) {
            log.info("FulfillmentRateManageServiceImpl.getTargetRefreshSkuByHsCode. skuIds={}", skuIds);
        }

        log.info("FulfillmentRateManageServiceImpl.getTargetRefreshSkuByHsCode hsCode:{} , vo:{}, refreshSize:{}" , hsCode , JSON.toJSONString(vo), result.size());
        return result;
    }

    private List<FulfillmentRateChangeReqVO> getTargetRefreshSkuByCat(Long catId,FulfillmentRateChangeReqVO vo){
        List<FulfillmentRateChangeReqVO> result = new ArrayList<>();
        List<Long> cat4Ids = new ArrayList<>();
        // getTaxRefundRate 为 ExportTaxRateBinLakeListener
        if(catId != null && vo.isDefaultQueryCategory()){
            cat4Ids = categoryAtomicService.categoryLevel4From1234(catId);
        }

        if (catId != null && !vo.isDefaultQueryCategory()) {
            cat4Ids.add(catId);
        }

        //分页查询目标sku
        int index = 1;
        Integer size = 500;
        Page<SkuPO> items = skuAtomicService.pageSkuByCateAndCountry(CollectionUtils.isNotEmpty(cat4Ids) ? new HashSet<>(cat4Ids) : new HashSet<>(), vo.getSourceCountryCode(), index, size);
        while (items!=null && CollectionUtils.isNotEmpty(items.getRecords())){
            Set<Long> allSku = items.getRecords().stream().map(SkuPO::getSkuId).collect(Collectors.toSet());
//            allSku.forEach(skuId-> result.add(new FulfillmentRateChangeReqVO(vo.getSourceCountryCode(),vo.getTargetCountryCode(),skuId,vo.getIsWareHouseProduct())));

            allSku.forEach(skuId-> {
                FulfillmentRateChangeReqVO fulfillmentRateChangeReqVO = new FulfillmentRateChangeReqVO(vo.getSourceCountryCode(), vo.getTargetCountryCode(), skuId, vo.getIsWareHouseProduct());
                fulfillmentRateChangeReqVO.setDataStatusSource(vo.getDataStatusSource());
                result.add(fulfillmentRateChangeReqVO);
            });

            index = index +1 ;
            items = skuAtomicService.pageSkuByCateAndCountry(CollectionUtils.isNotEmpty(cat4Ids) ? new HashSet<>(cat4Ids) : new HashSet<>(), vo.getSourceCountryCode(), index, size);
        }

        if (AlertHelper.isDevOrTest()) {
            List<Long> skuIds = result.stream().map(FulfillmentRateChangeReqVO::getSkuId).collect(Collectors.toList());
            log.info("FulfillmentRateManageServiceImpl.getTargetRefreshSkuByCat. skuIds={}", skuIds);
        }

        log.info("FulfillmentRateManageServiceImpl.getTargetRefreshSkuByCat catId:{} , vo:{}, refreshSize:{}" , catId , JSON.toJSONString(vo),result.size());
        return result;
    }

//
//    private List<FulfillmentRateChangeReqVO> getTargetRefreshSkuByCat(Long catId,FulfillmentRateChangeReqVO vo){
//        log.info("FulfillmentRateManageServiceImpl.getTargetRefreshSkuByCat req:{} , res:{}" , catId , JSON.toJSONString(vo));
//        List<FulfillmentRateChangeReqVO> result = new ArrayList<>();
//        List<Long> cat4Ids = new ArrayList<>();
//        if(catId!=null){
//            cat4Ids = categoryAtomicService.categoryLevel4From1234(catId);
//        }
//        //分页查询目标sku
//        int index = 1;
//        Integer size = 100;
//        Page<SkuPO> items = skuAtomicService.pageSkuByCateAndCountry(CollectionUtils.isNotEmpty(cat4Ids) ? new HashSet<>(cat4Ids) : new HashSet<>(), vo.getSourceCountryCode(), index, size);
//        while (items!=null && CollectionUtils.isNotEmpty(items.getRecords())){
//            Set<Long> allSku = items.getRecords().stream().map(SkuPO::getSkuId).collect(Collectors.toSet());
//            //商品入仓标记
//            Map<Long, Integer> warehouseFlagMap = skuReadManageService.querySkuPurchaseModelMap(items.getRecords().stream().map(SkuPO::getSkuId).collect(Collectors.toSet()), vo.getTargetCountryCode());
//            Set<Long> warehouseSku = warehouseFlagMap.entrySet().stream().filter(entry -> entry.getValue().equals(1)).map(Map.Entry::getKey).collect(Collectors.toSet());
//            //如果是入仓品的履约规则，则只影响入仓品的成本价重刷
//            if(FulfillmentRateTypeEnum.WAREHOUSE_PRODUCT.getCode() == vo.getIsWareHouseProduct()){
//                log.info("FulfillmentRateManageServiceImpl.getTargetRefreshSkuByCat 入仓品成本价重刷 warehouseFlagMap {} " , warehouseSku.size());
//                if(CollectionUtils.isNotEmpty(warehouseSku)){
//                    warehouseSku.forEach(skuId-> result.add(new FulfillmentRateChangeReqVO(vo.getSourceCountryCode(),vo.getTargetCountryCode(),skuId)));
//                }
//            //直送品的履约规则，则只触发直送品成本价重刷
//            }else if(FulfillmentRateTypeEnum.DIRECT_DELIVERY.getCode() == vo.getIsWareHouseProduct()){
//                log.info("FulfillmentRateManageServiceImpl.getTargetRefreshSkuByCat 直送品成本价重刷 warehouseSku {} , allSku{}" , warehouseSku.size(),allSku.size());
//                //直送品=全部-备货品
//                Collection<Long> directSendSku = CollectionUtils.subtract(allSku, warehouseSku);
//                directSendSku.forEach(skuId-> result.add(new FulfillmentRateChangeReqVO(vo.getSourceCountryCode(),vo.getTargetCountryCode(),skuId)));
//            }
//            index = index +1 ;
//            items = skuAtomicService.pageSkuByCateAndCountry(CollectionUtils.isNotEmpty(cat4Ids) ? new HashSet<>(cat4Ids) : new HashSet<>(), vo.getSourceCountryCode(), index, size);
//        }
//        return result;
//    }

    public static void main(String[] args) {

        BigDecimal a1 = new BigDecimal("123.456");
        BigDecimal a2 = new BigDecimal("4123.456");
        BigDecimal a3 = new BigDecimal("4123.45");

        System.out.println(a1.divide(Constant.DECIMAL_HUNDRED,6, RoundingMode.HALF_DOWN));
        System.out.println(a2.divide(Constant.DECIMAL_HUNDRED,6, RoundingMode.HALF_DOWN));
        System.out.println(a3.divide(Constant.DECIMAL_HUNDRED,6, RoundingMode.HALF_DOWN));

    }

}
