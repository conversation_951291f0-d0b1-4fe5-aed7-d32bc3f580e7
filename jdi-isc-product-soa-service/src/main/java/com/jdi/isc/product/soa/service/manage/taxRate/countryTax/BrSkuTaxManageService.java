package com.jdi.isc.product.soa.service.manage.taxRate.countryTax;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.price.api.price.req.BrSkuTaxVO;

import java.util.List;

/**
 * 巴西SKU税率服务
 * <AUTHOR>
 * @date 20250311
 */
public interface BrSkuTaxManageService {

    /** 税率保存更新*/
    DataResponse<Boolean> saveOrUpdate(BrSkuTaxVO input);

    DataResponse<Integer> batchFixBrNcmCode(List<Long> jdSkuIds, boolean update, String updater);

    DataResponse<Integer> batchFixBrNcmCode(List<Long> jdSkuIds, String updater);
}
