package com.jdi.isc.product.soa.service.atomic.taxRate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.taxRate.biz.ExportTaxRatePageVO;
import com.jdi.isc.product.soa.domain.taxRate.po.ExportTaxRatePO;
import com.jdi.isc.product.soa.repository.mapper.taxRate.ExportTaxRateBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 中国出口税率信息原子服务
 * @Author: zhaojianguo21
 * @Date: 2024/11/25 20:58
 **/
@Slf4j
@Service
public class ExportTaxRateAtomicService extends ServiceImpl<ExportTaxRateBaseMapper, ExportTaxRatePO> {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    public List<ExportTaxRatePageVO.Response> pageSearch(ExportTaxRatePageVO.Request input){
        input.setOffset((input.getIndex()-1)*input.getSize());
        return super.getBaseMapper().pageSearch(input);
    }

    /**
     * 分页查询的列表总数
     * @param input 查询条件
     * @return 总条数
     */
    public long pageSearchTotal(ExportTaxRatePageVO.Request input){
        return super.getBaseMapper().pageSearchTotal(input);
    }

    /**
     * 未删除的对象
     * @param id id
     * @return 对象
     */
    public ExportTaxRatePO getValidById(Long id){
        LambdaQueryWrapper<ExportTaxRatePO> wrapper = Wrappers.<ExportTaxRatePO>lambdaQuery()
                .eq(ExportTaxRatePO::getId, id)
                .eq(ExportTaxRatePO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }


    /**
     * 未删除的对象
     * @param hsCode hsCode
     * @param countryCode countryCode
     * @return 对象
     */
    public ExportTaxRatePO getExportTaxRateByHsCode(String hsCode, String countryCode){
        if (StringUtils.isBlank(hsCode) || StringUtils.isBlank(countryCode)) {
            return null;
        }
        LambdaQueryWrapper<ExportTaxRatePO> wrapper = Wrappers.<ExportTaxRatePO>lambdaQuery()
                .eq(ExportTaxRatePO::getHsCode, hsCode)
                .eq(ExportTaxRatePO::getCountryCode, countryCode)
                .eq(ExportTaxRatePO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }
}
