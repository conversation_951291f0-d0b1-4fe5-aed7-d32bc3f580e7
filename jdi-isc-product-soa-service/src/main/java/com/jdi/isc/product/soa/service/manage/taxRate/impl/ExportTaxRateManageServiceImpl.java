package com.jdi.isc.product.soa.service.manage.taxRate.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.util.BigDecimalUtil;
import com.jdi.isc.product.soa.domain.taxRate.biz.ExportTaxRatePageVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.ExportTaxRateVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.GetExportTaxRateVO;
import com.jdi.isc.product.soa.domain.taxRate.po.ExportTaxRatePO;
import com.jdi.isc.product.soa.service.atomic.taxRate.ExportTaxRateAtomicService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.ExportTaxRateManageService;
import com.jdi.isc.product.soa.service.mapstruct.taxRate.ExportTaxRateConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 中国出口税率信息数据维护服务实现
 * @Author: zhaojianguo21
 * @Date: 2024/11/25 20:58
 **/

@Slf4j
@Service
public class ExportTaxRateManageServiceImpl extends BaseManageSupportService<ExportTaxRateVO, ExportTaxRatePO> implements ExportTaxRateManageService {

    @Resource
    private ExportTaxRateAtomicService exportTaxRateAtomicService;
    @Resource
    private CountryManageService countryManageService;

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> saveOrUpdate(ExportTaxRateVO input) {
        log.info("saveOrUpdate, input={}", JSONObject.toJSONString(input));

        // 参数业务校验
        DataResponse<Boolean> checkResult = this.checkAndInitInput(input);
        if (!checkResult.getSuccess()){
            log.info("saveOrUpdate, check input fail, input={}, checkResult={}", JSONObject.toJSONString(input), JSONObject.toJSONString(checkResult));
            return checkResult;
        }

        // 保存对象
        ExportTaxRatePO po = ExportTaxRateConvert.INSTANCE.vo2Po(input);
        po.setYn(YnEnum.YES.getCode());
        if (null!=po.getId()){
            // 编辑时不允许修改如下字段。重置为空，就不会更新
            po.setUpdateTime(System.currentTimeMillis());
        }else {
            // 如果存在，则根据国家和HsCode更新
            ExportTaxRatePO existTaxRatePo = exportTaxRateAtomicService.getExportTaxRateByHsCode(input.getHsCode(), input.getCountryCode());
            if (Objects.nonNull(existTaxRatePo)){
                po.setId(existTaxRatePo.getId());
            }
            po.setCreator(input.getUpdater());
            long now = System.currentTimeMillis();
            po.setCreateTime(now);
            po.setUpdateTime(now);
        }

        boolean saveRes = exportTaxRateAtomicService.saveOrUpdate(po);
        if (!saveRes){
            log.warn("saveOrUpdate, ExportTaxRatePO fail. ExportTaxRatePO={}", JSONObject.toJSONString(po));
            return DataResponse.error("保存失败");
        }

        return DataResponse.success(true);
    }

    @Override
    public ExportTaxRateVO detail(GetExportTaxRateVO input) {
        ExportTaxRatePO po = null;
        if (Objects.nonNull(input.getId())) {
            po = exportTaxRateAtomicService.getValidById(input.getId());
        } else if (StringUtils.isNotBlank(input.getHsCode()) && StringUtils.isNotBlank(input.getCountryCode())) {
            po = exportTaxRateAtomicService.getExportTaxRateByHsCode(input.getHsCode(),input.getCountryCode());
        }

        if (null==po){
            log.info("ExportTaxRateManageServiceImpl.detail, ExportTaxRatePO null. 入参:[{}]", JSON.toJSONString(input));
            return null;
        }

        // 补充国家名称
        ExportTaxRateVO exportTaxRateVO = ExportTaxRateConvert.INSTANCE.po2Vo(po);
        String name = countryManageService.getCountryNameByCountryCode(po.getCountryCode(), LangConstant.LANG_ZH);
        exportTaxRateVO.setCountryName(name);
        return exportTaxRateVO;
    }

    @Override
    public PageInfo<ExportTaxRatePageVO.Response> pageSearch(ExportTaxRatePageVO.Request input) {
        PageInfo<ExportTaxRatePageVO.Response> pageInfo = new PageInfo<>();
        pageInfo.setIndex(Objects.nonNull(input.getIndex()) ? input.getIndex() : 1);
        pageInfo.setSize(Objects.nonNull(input.getSize()) ? input.getSize() : 20);

        // 总条数
        LambdaQueryWrapper<ExportTaxRatePO> queryWrapper = Wrappers.lambdaQuery(ExportTaxRatePO.class)
                .eq(StringUtils.isNotBlank(input.getHsCode()), ExportTaxRatePO::getHsCode, input.getHsCode())
                .eq(StringUtils.isNotBlank(input.getCountryCode()),ExportTaxRatePO::getCountryCode,input.getCountryCode())
                .eq(ExportTaxRatePO::getYn, YnEnum.YES.getCode());
        long total = exportTaxRateAtomicService.count(queryWrapper);
        pageInfo.setTotal(total);
        if (0 == total) {
            log.info("page, total is zero. input={}", JSONObject.toJSONString(input));
            return pageInfo;
        }


        // 分页查询
        Page<ExportTaxRatePO> page = new Page<>(Objects.nonNull(input.getIndex()) ? input.getIndex() : 1,Objects.nonNull(input.getSize()) ? input.getSize() : 20);
        queryWrapper.orderByDesc(ExportTaxRatePO::getUpdateTime);
        Page<ExportTaxRatePO> taxRatePOPage = exportTaxRateAtomicService.page(page, queryWrapper);
        pageInfo.setTotal(taxRatePOPage.getTotal());
        if (CollectionUtils.isEmpty(taxRatePOPage.getRecords())){
            return pageInfo;
        }

        List<ExportTaxRatePageVO.Response> list = ExportTaxRateConvert.INSTANCE.pageListPo2Vo(taxRatePOPage.getRecords());
        // 补充国家名称
        Map<String, String> countryMap = countryManageService.getCountryMap(LangConstant.LANG_ZH);
        for (ExportTaxRatePageVO.Response response : list) {
            response.setCountryName( countryMap.getOrDefault(response.getCountryCode(),response.getCountryCode()));
        }
        pageInfo.setRecords(list);
        return pageInfo;
    }


    /**
     * 参数校验
     * @param input 提交参数
     * @return 检查结果
     */
    private DataResponse<Boolean> checkAndInitInput(ExportTaxRateVO input){
        // ID如果空，当称新增校验
        if (Objects.isNull(input)) {
            return DataResponse.error("参数不能为空");
        }

        // 校验国家编码是否存在
        List<String> countryCodeList = countryManageService.getCountryCodeList();
        if (CollectionUtils.isEmpty(countryCodeList) || !countryCodeList.contains(input.getCountryCode())) {
            return DataResponse.error(String.format("国家编码 %s 不正确，请检查", input.getCountryCode()));
        }

        // 已经存在相同的国家+hsCode编码组合的税率
        if (Objects.nonNull(input.getId())) {
            ExportTaxRatePO existTaxRatePo = exportTaxRateAtomicService.getExportTaxRateByHsCode(input.getHsCode(), input.getCountryCode());
            if (!input.getId().equals(existTaxRatePo.getId())) {
                return DataResponse.error(String.format("同一个国家%s HsCode编码 %s 已存在，请检查",input.getCountryCode(), input.getHsCode()));
            }
        }

        // 校验出口退税率
        BigDecimal exportRebateRate = input.getExportRebateRate();
        if (exportRebateRate == null || BigDecimalUtil.lt0(exportRebateRate) || BigDecimalUtil.gt(exportRebateRate, new BigDecimal(100))) {
            log.info("出口退税率需要控制在[0-100]之间，请检查, exportRebateRate={}", exportRebateRate);
            return DataResponse.error("出口退税率需要控制在[0-100]之间，请检查");
        }

        return DataResponse.success();
    }


}
