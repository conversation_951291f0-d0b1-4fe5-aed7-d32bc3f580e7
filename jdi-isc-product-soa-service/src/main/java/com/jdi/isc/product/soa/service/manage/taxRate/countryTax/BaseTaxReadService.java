package com.jdi.isc.product.soa.service.manage.taxRate.countryTax;

import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxResVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.IscSkuImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.HuSkuTaxPO;
import com.jdi.isc.product.soa.price.api.price.req.CrossBorderImportTaxReqDTO;
import com.jdi.isc.product.soa.price.api.price.res.CrossBorderImportTaxResDTO;

import java.util.Map;
import java.util.Set;

/**
 * 基础关税读服务接口
 * <AUTHOR>
 * @date 2025/3/10
 */
public interface BaseTaxReadService {

    /** 通过skuId、国家码查询跨境sku关税*/
    Map<Long, CrossBorderImportTaxResDTO> queryCrossBorderSkuImportTax(CrossBorderImportTaxReqVO input);

    /**
     * 根据 ISKU IDs 查询跨境进口税信息。
     * @param input 要查询的 ISKU IDs 集合。
     * @return 包含每个 ISKU ID 对应的 CrossBorderImportTaxResVO 的 Map。
     */
    Map<Long, CrossBorderImportTaxResVO> queryImportTaxByIscSkuIds(IscSkuImportTaxReqVO input);
}
