package com.jdi.isc.product.soa.service.manage.sku;


import com.jdi.isc.product.soa.domain.sku.biz.QuerySkuAvailableSaleReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuAvailableSaleResultVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuLimitBuyVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 商品销售状态查询服务
 * @Date 2025-02-20
 */
public interface SkuCrossBorderSaleStatusReadManageService {

    /**
     * 根据给定的 SKU ID 集合查询每个 SKU 的可售情况。
     * @param reqVO 需要查询的 SKU ID 集合。
     * @return 每个 SKU ID 对应的可售情况结果。
     */
    Map<Long, SkuAvailableSaleResultVO> querySkuAvailableCanPurchaseMap(QuerySkuAvailableSaleReqVO reqVO);

    /**
     * 根据给定的 SKU ID 集合查询每个 SKU 的可售区域。
     * @param reqVO 需要查询的 SKU ID 集合。
     * @return 每个 SKU ID 对应的可售区域结果。
     */
    Map<Long, SkuAvailableSaleResultVO> querySkuAvailableLimitArea(QuerySkuAvailableSaleReqVO reqVO);

    /**
     * 查询标准 SKU 可售情况
     * @param reqVO 查询请求对象
     * @return SKU ID 到可售结果的映射关系
     */
    Map<Long, SkuAvailableSaleResultVO> queryStandardSkuAvailableSale(QuerySkuAvailableSaleReqVO reqVO);

    /**
     * 区域限售，内贸段所有地址区域限售，既区域限售
     * @param jdSkuIds
     * @return
     */
    List<Long> queryStandardSkuAreaLimitAllAreaByJdSkuIds(List<Long> jdSkuIds);

    /**
     * 可售，内贸段有一个地址可售，既可售
     * @param jdSkuIds
     * @return
     */
    List<Long> queryStandardSkuAvailableSaleAllAreaByJdSkuIds(List<Long> jdSkuIds);


    /**
     * 根据 SKU ID 列表和客户信息，查询所有受限地区的标准 SKU。
     * @param skuIds SKU ID 列表
     * @param countryCode 国家简码
     * @return 所有受限地区的标准 SKU ID 列表
     */
    List<Long> queryStandardSkuAreaLimitForCountryPool(List<Long> skuIds, String countryCode);


    /**
     * 根据京东 SKU ID 列表和客户信息，查询所有地区可售的标准 SKU。
     * @param jdSkuIds 京东 SKU ID 列表
     * @param  countryCode 国家简码
     * @return 所有地区可售的标准 SKU ID 列表
     */
    List<Long> queryStandardSkuAvailableSaleForCountryPool(List<Long> jdSkuIds, String countryCode, Map<Long,String> failMsgMap);

    /**
     * 查询所有地区的跨境商品可售区域限制信息。
     * @param jdSkuIds 需要查询的跨境商品ID集合。
     * @return 包含所有地区的跨境商品可售区域限制信息的列表。
     */
    List<SkuAvailableSaleResultVO> queryCrossBorderSkuLimitAreaForAllAreaList(Set<Long> jdSkuIds);
    /**
     * 查询所有地区的跨境商品可售信息。
     * @param jdSkuIds 跨境商品的京东 SKU ID 集合。
     * @return 包含所有地区的跨境商品可售信息的 DataResponse 对象。
     */
    List<SkuAvailableSaleResultVO> queryCrossBorderSkuAvailableSaleForAllAreaList(Set<Long> jdSkuIds);

    /**
     * 根据条件查询京东商品限购信息。
     * @param reqVO 查询请求对象，包含查询条件。
     * @return Map对象，key为商品ID，value为对应的限购信息。
     */
    Map<Long, SkuLimitBuyVO> queryJdSkuLimitBuy(QuerySkuAvailableSaleReqVO reqVO);
}
