package com.jdi.isc.product.soa.service.manage.spu.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.gms.crs.model.AttributeSettingResult;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.isc.product.soa.api.attribute.common.AttributeDTO;
import com.jdi.isc.product.soa.api.attribute.req.AttributeQueryReqDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.AttributeInputTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.AttributeTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuApproveCountryTypeEnums;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.common.constants.TaxConstant;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.frame.SaleAttributeUNhandleContextHolder;
import com.jdi.isc.product.soa.common.frame.SystemContextHolder;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.enums.spu.SpuTaxCountryEnums;
import com.jdi.isc.product.soa.domain.enums.spu.SpuWriteCountryTypeEnums;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCalculatePriceReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCalculateTaxPriceVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.SpuLangPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.service.atomic.mku.MkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuLangAtomicService;
import com.jdi.isc.product.soa.service.manage.attribute.AttributeOutService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.*;
import com.jdi.isc.product.soa.service.manage.spu.validate.SpuValidateService;
import com.jdi.isc.product.soa.service.manage.stock.StockManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/11
 **/
@Slf4j
@Service
public class SpuManageServiceImpl extends BaseManageSupportService<SpuVO, SpuPO> implements SpuManageService {
    @Resource
    private SpuReadManageService spuReadManageService;

    @Resource
    private SkuReadManageService skuReadManageService;

    @Resource
    private SpuDraftManageService spuDraftManageService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private StockManageService stockManageService;

    @Resource
    @Lazy
    private Map<String, SpuWriteManageService> spuWriteManageServiceMap;

    @Resource
    private Map<String, SpuApproveManageService> spuApproveManageServiceMap;

    @Resource
    private Map<String, SpuTaxManageService> spuTaxManageServiceMap;

    @Resource
    private SpuValidateService spuValidateService;

    @Resource
    private ExecutorService spuApproveExecutorService;

    @Resource
    private ExecutorService spuTaxApproveExecutorService;

    @Resource
    private AttributeOutService attributeOutService;

    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private MkuAtomicService mkuAtomicService;

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @Override
    public DataResponse<Long> create(SaveSpuVO saveSpuVO) {
        return spuWriteManageService(saveSpuVO.getSpuVO().getSourceCountryCode()).create(saveSpuVO);
    }

    @Override
    public DataResponse<Long> update(SaveSpuVO saveSpuVO) {
        return spuWriteManageService(saveSpuVO.getSpuVO().getSourceCountryCode()).update(saveSpuVO);
    }

    @Override
    public DataResponse<String> batchApprove(SpuAuditRecordUpdateVO spuAuditRecordReqVO) {
        SpuApproveManageService spuApproveManageService = this.spuApproveManageService(spuAuditRecordReqVO.getCountryType());
        // 校验
        if (SpuApproveCountryTypeEnums.CROSSBORDER.getCode() == spuAuditRecordReqVO.getCountryType()){
            spuValidateService.validateApproved(spuAuditRecordReqVO);
        }

        // erp
        final String erp = LoginContextHolder.getLoginContextHolder().getPin();
        final String systemCode = SystemContextHolder.get();
        final String lang = LangContextHolder.get();
        // 税务审核标识
        String scene = spuAuditRecordReqVO.getTaxStatus() == null?null: TaxConstant.TAX_APPROVE;

        List<Long> spuIds = spuAuditRecordReqVO.getSpuIds();
        List<CompletableFuture<String>> completableFutureList = Lists.newArrayListWithExpectedSize(spuIds.size());
        for (Long spuId : spuIds) {
            completableFutureList.add(CompletableFuture.supplyAsync(() -> {
                String msg = "";
                try {
                    ApiInitUtils.init(erp, lang, systemCode);
                    // 设置跳过销售属性处理 ZHAOYAN_SALE_ATTR
                    SaleAttributeUNhandleContextHolder.set();
                    spuApproveManageService.singleApproved(spuId, scene);
                } catch (Exception e) {
                    msg = String.format("%s：%s%s", spuId, e.getMessage(),"\n");
                } finally {
                    // 移除跳过销售属性处理 ZHAOYAN_SALE_ATTR
                    SaleAttributeUNhandleContextHolder.remove();
                }
                return msg;
            }, spuApproveExecutorService));
        }

        // 使用 allOf 等待所有 CompletableFuture 完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]));

        try {
            allFutures.get(20, TimeUnit.SECONDS); // 等待所有任务完成，最多等待20秒
        } catch (TimeoutException e) {
            // 如果超时，取消所有未完成的任务
            completableFutureList.forEach(future -> {
                if (!future.isDone() && !future.isCancelled()) {
                    future.cancel(true);
                }
            });
            log.error("SpuManageServiceImpl.batchApprove 等待所有审核结果超时", e);
        } catch (Exception e) {
            log.error("SpuManageServiceImpl.batchApprove 等待所有审核结果异常", e);
        }

        // 取回结果
        String message = null;
        for (CompletableFuture<String> future : completableFutureList) {
            try {
                message = future.get(20, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("SpuManageServiceImpl.batchApprove 取回单个审核结果异常", e);
                if (null != future) {
                    future.cancel(Boolean.TRUE);
                }
            }
        }
        return StringUtils.isNotBlank(message) ? DataResponse.error(message) : DataResponse.success(DataResponseCode.SUCCESS.getMessage());
    }

    @Override
    public DataResponse<String> batchReject(SpuAuditRecordUpdateVO spuAuditRecordReqVO) {
        spuValidateService.validateSpu(spuAuditRecordReqVO.getSpuIds());
        return this.spuApproveManageService(spuAuditRecordReqVO.getCountryType()).batchReject(spuAuditRecordReqVO);
    }

    @Override
    public DataResponse<String> batchAmend(List<SpuAmendReqVO> spuAmendReqVOList) {
        return this.spuApproveManageService(spuAmendReqVOList.get(0).getCountryType()).batchAmend(spuAmendReqVOList);
    }


    @Override
    public DataResponse<Long> saveDraft(SaveSpuVO saveSpuVO) {
        return spuWriteManageService(saveSpuVO.getSpuVO().getSourceCountryCode()).saveDraft(saveSpuVO);
    }

    @Override
    public DataResponse<Long> submit(SaveSpuVO saveSpuVO) {
        return spuWriteManageService(saveSpuVO.getSpuVO().getSourceCountryCode()).submit(saveSpuVO);
    }

    @Override
    public DataResponse<String> batchDown(SpuBatchReqVO spuBatchReqVO) {
        spuValidateService.validateSpu(new ArrayList<>(spuBatchReqVO.getSpuIds()));
        return spuWriteManageService(spuBatchReqVO.getSourceCountryCode()).batchDown(spuBatchReqVO);
    }

    @Override
    public DataResponse<String> batchArchive(SpuBatchReqVO spuBatchReqVO) {
        return spuWriteManageService(spuBatchReqVO.getSourceCountryCode()).batchArchive(spuBatchReqVO);
    }

    @Override
    public DataResponse<List<SpuUpdateReqVO>> batchUpdate(List<SpuUpdateReqVO> spuUpdateReqVOList) {
        return spuWriteManageService(spuUpdateReqVOList.get(0).getSourceCountryCode()).batchUpdate(spuUpdateReqVOList);
    }

    private SpuWriteManageService spuWriteManageService(String sourceCountryCode) {
        return spuWriteManageServiceMap.get(SpuWriteCountryTypeEnums.getEnumByCountryCode(sourceCountryCode).getServiceName());
    }

    private SpuApproveManageService spuApproveManageService(int type) {
        return spuApproveManageServiceMap.get(Objects.requireNonNull(SpuApproveCountryTypeEnums.getEnumByCode(type)).getServiceName());
    }

    private SpuTaxManageService spuTaxManageService(int type) {
        return spuTaxManageServiceMap.get(Objects.requireNonNull(SpuTaxCountryEnums.getEnumByCode(type)).getServiceName());
    }



    /**
     * 更新商品价格
     * @param dto SpuUpdatePriceReqVO 对象，包含需要更新的商品信息
     * @return DataResponse<Long> 对象，表示更新操作的结果
     */
    @Override
    public DataResponse<Long> updatePrice(SpuUpdatePriceReqVO dto) {
        // 1。 spuId skuId  货源国code，供应商code，未税采购价，含税采购价，未税销售价，含税销售价格，
        // updateType 1 未税采购价更新含税采购价， 2 含税采购价，更新未税采购价，
        // 3 未税销售价，更新含税销售价格，4 含税销售价格，更新未税销售价
        SkuVO skuBySkuId = skuReadManageService.getSkuBySkuId(dto.getSkuId());
        SpuDetailVO spuDetailVO = spuReadManageService.getDetailBySpuId(skuBySkuId.getSpuId());
        List<SkuVO> skuVOList = spuDetailVO.getSkuVOList();
        // 通过未税采购价 获取 其他的税价格getSkuCalculateSalePrice
        SkuCalculatePriceReqVO skuCalculatePriceReqVO = new SkuCalculatePriceReqVO();
        if(dto.getUpdateType().equals(1)){
            skuCalculatePriceReqVO.setPurchasePrice(dto.getPurchasePrice());
        }
        if(dto.getUpdateType().equals(2)){
            skuCalculatePriceReqVO.setTaxPurchasePrice(dto.getTaxPurchasePrice());
        }
        if(dto.getUpdateType().equals(3)){
            skuCalculatePriceReqVO.setSalePrice(dto.getSalePrice());
        }
        if(dto.getUpdateType().equals(4)){
            skuCalculatePriceReqVO.setTaxSalePrice(dto.getTaxSalePrice());
        }
        skuCalculatePriceReqVO.setCatId(skuBySkuId.getCatId());
        skuCalculatePriceReqVO.setSourceCountryCode(skuBySkuId.getSourceCountryCode());
        SkuCalculateTaxPriceVO skuCalculateSalePrice = skuReadManageService.getSkuCalculateSalePrice(skuCalculatePriceReqVO);

        for(SkuVO skuVO : skuVOList){
            if(dto.getSkuId().equals(skuVO.getSkuId())){
                if(Objects.nonNull(skuCalculateSalePrice.getPurchasePrice())) {
                    skuVO.setPurchasePrice(String.valueOf(skuCalculateSalePrice.getPurchasePrice()));
                }
                if(Objects.nonNull(skuCalculateSalePrice.getTaxPurchasePrice())) {
                    skuVO.setTaxPurchasePrice(String.valueOf(skuCalculateSalePrice.getTaxPurchasePrice()));
                }
                if(Objects.nonNull(skuCalculateSalePrice.getSalePrice())) {
                    skuVO.setSalePrice(String.valueOf(skuCalculateSalePrice.getSalePrice()));
                }
                if(Objects.nonNull(skuCalculateSalePrice.getTaxSalePrice())) {
                    skuVO.setTaxSalePrice(String.valueOf(skuCalculateSalePrice.getTaxSalePrice()));
                }
            }
        }
        spuDetailVO.setSkuVOList(skuVOList);
        DataResponse<Long> submit = submit(spuDetailVO);
        return submit;
    }



    @Override
    public DataResponse<Boolean> updateStockAndDraft(StockManageReqDTO req) {
        DataResponse<Boolean> response = stockManageService.saveOrUpdate(req);
        Boolean result = response.getData();
        if(result){
            Map<Long, Long> skuIdMap = req.getStockItem().stream().collect(
                Collectors.toMap(StockItemManageReqDTO::getSkuId, StockItemManageReqDTO::getNum));
            List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(skuIdMap.keySet());
            LangContextHolder.init(LangConstant.LANG_ZH);
            for (SkuPO skuPO : skuPOList) {
                SaveSpuVO saveSpuVO =  spuDraftManageService.getSaveSpuVoFromDraftBySpuId(skuPO.getSpuId());
                List<SkuVO> skuVOList = saveSpuVO.getSkuVOList();
                for (SkuVO skuVO : skuVOList){
                    if(skuPO.getSkuId().equals(skuVO.getSkuId())){
                        Long stockNum = skuIdMap.get(skuPO.getSkuId());
                        if(Objects.nonNull(skuVO.getStockNum())){
                            Long stock = stockNum + Long.parseLong(skuVO.getStockNum());
                            skuVO.setStockNum(String.valueOf(stock));
                        }else {
                            skuVO.setStockNum(String.valueOf(stockNum));
                        }
                    }
                }
                saveSpuVO.setSkuVOList(skuVOList);
                saveSpuVO.setPin(req.getOperator());
                spuDraftManageService.saveOrUpdateDraft(saveSpuVO);
            }
        }
        return response;
    }

    @Override
    public DataResponse<Boolean> updateSpuSkuMku(SpuUpdateReqVO spuUpdateReqVO) {
        return spuWriteManageService(CountryConstant.COUNTRY_ZH).updateSpuSkuMku(spuUpdateReqVO);
    }

    @Override
    public DataResponse<String> batchApproveTax(SpuAuditTaxApproveVO spuAuditTaxApproveVO) {
        SpuTaxManageService spuTaxManageService = this.spuTaxManageService(spuAuditTaxApproveVO.getCountryType());

        // erp
        final String erp = LoginContextHolder.getLoginContextHolder().getPin();
        final String systemCode = SystemContextHolder.get();
        final String lang = LangContextHolder.get();

        spuAuditTaxApproveVO.setAuditErp(erp);

        List<Long> spuIds = spuAuditTaxApproveVO.getSpuIds();

        List<SaveSpuVO> saveSpuVOS = spuDraftManageService.getSaveSpuVoFromDraftListBySpuIds(new HashSet<>(spuIds));

        List<CompletableFuture<String>> completableFutureList = Lists.newArrayListWithExpectedSize(spuIds.size());
        for(SaveSpuVO saveSpuVO : saveSpuVOS){
            SpuVO spuVO = saveSpuVO.getSpuVO();
            Long spuId = spuVO.getSpuId();

            completableFutureList.add(CompletableFuture.supplyAsync(() -> {
                String msg = "";
                try {
                    ApiInitUtils.init(erp, lang, systemCode);
                    // 设置跳过销售属性处理 ZHAOYAN_SALE_ATTR
                    SaleAttributeUNhandleContextHolder.set();
                    spuTaxManageService.singleApprove(saveSpuVO, spuAuditTaxApproveVO);
                } catch (Exception e) {
                    msg = String.format("%s：%s%s", spuId, e.getMessage(),"\n");
                } finally {
                    // 移除跳过销售属性处理 ZHAOYAN_SALE_ATTR
                    SaleAttributeUNhandleContextHolder.remove();
                }
                return msg;
            }, spuTaxApproveExecutorService));
        }

        // 使用 allOf 等待所有 CompletableFuture 完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]));

        try {
            allFutures.get(20, TimeUnit.SECONDS); // 等待所有任务完成，最多等待20秒
        } catch (TimeoutException e) {
            // 如果超时，取消所有未完成的任务
            completableFutureList.forEach(future -> {
                if (!future.isDone() && !future.isCancelled()) {
                    future.cancel(true);
                }
            });
            log.error("SpuManageServiceImpl.batchApproveTax 等待所有审核结果超时", e);
        } catch (Exception e) {
            log.error("SpuManageServiceImpl.batchApproveTax 等待所有审核结果异常", e);
        }

        // 收集所有结果
        String message = "";
        for (CompletableFuture<String> future : completableFutureList) {
            try {
                String result = future.get(); // 此处不需要超时，因为已经在 allOf 中等待过了
                message += result;
            } catch (Exception e) {
                log.error("SpuManageServiceImpl.batchApproveTax 取回单个审核结果异常", e);
                // 处理异常情况，比如记录日志
            }
        }
        return StringUtils.isNotBlank(message) ? DataResponse.error(message) : DataResponse.success(DataResponseCode.SUCCESS.getMessage());
    }

    @Override
    public DataResponse<String> batchRejectTax(SpuAuditTaxApproveVO spuAuditTaxApproveVO) {
        SpuTaxManageService spuTaxManageService = this.spuTaxManageService(spuAuditTaxApproveVO.getCountryType());

        // erp
        final String erp = LoginContextHolder.getLoginContextHolder().getPin();
        final String systemCode = SystemContextHolder.get();
        final String lang = LangContextHolder.get();

        spuAuditTaxApproveVO.setAuditErp(erp);

        List<Long> spuIds = spuAuditTaxApproveVO.getSpuIds();

        List<SaveSpuVO> saveSpuVOS = spuDraftManageService.getSaveSpuVoFromDraftListBySpuIds(new HashSet<>(spuIds));

        List<CompletableFuture<String>> completableFutureList = Lists.newArrayListWithExpectedSize(spuIds.size());

        for(SaveSpuVO saveSpuVO : saveSpuVOS){
            SpuVO spuVO = saveSpuVO.getSpuVO();
            Long spuId = spuVO.getSpuId();

            completableFutureList.add(CompletableFuture.supplyAsync(() -> {
                String msg = "";
                try {
                    ApiInitUtils.init(erp, lang, systemCode);
                    spuTaxManageService.singleReject(saveSpuVO, spuAuditTaxApproveVO);
                } catch (Exception e) {
                    msg = String.format("%s：%s%s", spuId, e.getMessage(),"\n");
                }
                return msg;
            }, spuTaxApproveExecutorService));
        }

// 使用 allOf 等待所有 CompletableFuture 完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]));

        try {
            allFutures.get(20, TimeUnit.SECONDS); // 等待所有任务完成，最多等待20秒
        } catch (TimeoutException e) {
            // 如果超时，取消所有未完成的任务
            completableFutureList.forEach(future -> {
                if (!future.isDone() && !future.isCancelled()) {
                    future.cancel(true);
                }
            });
            log.error("SpuManageServiceImpl.batchRejectTax 等待所有审核结果超时", e);
        } catch (Exception e) {
            log.error("SpuManageServiceImpl.batchRejectTax 等待所有审核结果异常", e);
        }

        // 取回结果
        String message = null;
        for (CompletableFuture<String> future : completableFutureList) {
            try {
                message = future.get();
            } catch (Exception e) {
                log.error("SpuManageServiceImpl.batchRejectTax 取回单个审核结果异常", e);
                if (null != future) {
                    future.cancel(Boolean.TRUE);
                }
            }
        }
        return StringUtils.isNotBlank(message) ? DataResponse.error(message) : DataResponse.success(DataResponseCode.SUCCESS.getMessage());
    }

    @Override
    public DataResponse<String> updateSpuVendorCode(SpuBatchReqVO param) {
        if(CollectionUtils.isEmpty(param.getSpuIds())){
            throw new BizException("spuIds不可为空");
        }

        String sourceCountryCode = param.getSourceCountryCode();
        if(StringUtils.isBlank(sourceCountryCode)){
            throw new BizException("来源国不可为空");
        }
        if(StringUtils.equals(CountryConstant.COUNTRY_ZH,param.getVendorCode())){
            throw new BizException("跨境不支持供应商编码变更");
        }
        String vendorCode = param.getVendorCode();
        if(StringUtils.isBlank(vendorCode)){
            throw new BizException("供应商编码不可为空");
        }

        return spuWriteManageService(sourceCountryCode).updateSpuVendorCode(param);
    }


    @Override
    public DataResponse<String> refreshSpuExtAttribute(Long jdSkuId) {
        // 使用SkuAtomicService获取skuVO
        SkuPO skuPO = skuAtomicService.getSkuPoByJdSkuId(jdSkuId);
        if(Objects.isNull(skuPO)){
            log.error("SpuManageServiceImpl.refreshSpuExtAttribute 获取skuPO失败，jdSkuId:{}", jdSkuId);
            return DataResponse.success();
        }
        // 获取扩展属性
        List<PropertyVO> propertyApiDTOList = attributeOutService.obtainJDSkuExtAttributeList(skuPO.getJdCatId(), jdSkuId, LangConstant.LANG_ZH);
        if(CollectionUtils.isEmpty(propertyApiDTOList)){
            return DataResponse.success();
        }
        // 获取属性id与level的map
        Map<Long,Integer> attributeIdLevelMap = new HashMap<>();
        for(PropertyVO propertyVO : propertyApiDTOList){
            attributeIdLevelMap.put(propertyVO.getAttributeId(), propertyVO.getLevel());
        }
        Map<String,PropertyValueVO> mkuSelectedPropertyValueVOMap = new HashMap<>();
        Map<String,PropertyValueVO> spuSelectedPropertyValueVOMap = new HashMap<>();
        Map<String,PropertyValueVO> skuSelectedPropertyValueVOMap = new HashMap<>();
        // 将propertyApiDTOList转换为map，key为comGroupId_attributeId_attributeValueId,value为PropertyValueVO
        for(PropertyVO propertyVO : propertyApiDTOList){
            if(CollectionUtils.isNotEmpty(propertyVO.getPropertyValueVOList())){
                for(PropertyValueVO propertyValueVO : propertyVO.getPropertyValueVOList()){
                    // 只处理选中的属性值
                    if(propertyValueVO.getSelected() != null && propertyValueVO.getSelected()){
                        String key = getKey(propertyVO.getComGroupId(),propertyVO.getAttributeId(),propertyValueVO.getAttributeValueId(), propertyValueVO.getLang());
                        // 所有的中台sku属性都放入mku属性map
                        mkuSelectedPropertyValueVOMap.put(key, propertyValueVO);
                        // 中台spu属性放入spu属性map
                        if(propertyVO.getLevel()==null || propertyVO.getLevel() == PropertyVO.ONLY_SPU_LEVEL_EXT_ATTR_VALUE){ 
                            spuSelectedPropertyValueVOMap.put(key, propertyValueVO);
                        } else {
                            // 中台sku属性放入sku属性map
                            skuSelectedPropertyValueVOMap.put(key, propertyValueVO);
                        }
                    }
                }
            }
        }
        if(CollectionUtils.isEmpty(mkuSelectedPropertyValueVOMap.keySet()) && CollectionUtils.isEmpty(spuSelectedPropertyValueVOMap.keySet())){
            return DataResponse.success();
        }

        // 根据skuPO.getSkuId()从SpuAtomicService和MkuAtomicService获取对象
        SpuPO spuPO = spuAtomicService.getSpuPoBySpuId(skuPO.getSpuId());
        if(Objects.nonNull(spuPO)){
            String groupExtAttribute = updateGroupExtAttribute(spuPO.getGroupExtAttribute(), spuSelectedPropertyValueVOMap, attributeIdLevelMap);
            log.info("SpuManageServiceImpl.refreshSpuExtAttribute 更新spuPO扩展属性，更新前groupExtAttribute:{}，更新后groupExtAttribute:{}", spuPO.getGroupExtAttribute(),groupExtAttribute);
            spuPO.setGroupExtAttribute(groupExtAttribute);
            // 更新spuPO扩展属性
            spuAtomicService.updateById(spuPO);
        }
        if(Objects.nonNull(skuPO)){
            String groupExtAttribute = updateGroupExtAttribute(skuPO.getGroupExtAttribute(), skuSelectedPropertyValueVOMap, attributeIdLevelMap);
            log.info("SpuManageServiceImpl.refreshSpuExtAttribute 更新skuPO扩展属性，更新前groupExtAttribute:{}，更新后groupExtAttribute:{}", skuPO.getGroupExtAttribute(),groupExtAttribute);
            skuPO.setGroupExtAttribute(groupExtAttribute);
            // 更新skuPO扩展属性
            skuAtomicService.updateById(skuPO);
        }
        MkuRelationPO mkuRelationPO = mkuRelationAtomicService.getMkuBySkuId(skuPO.getSkuId());
        if(Objects.nonNull(mkuRelationPO)){
            MkuPO mkuPO = mkuAtomicService.getPOById(mkuRelationPO.getMkuId());
            if(Objects.nonNull(mkuPO)){
                String groupExtAttribute = updateGroupExtAttribute(mkuPO.getGroupExtAttribute(), mkuSelectedPropertyValueVOMap, attributeIdLevelMap);
                log.info("SpuManageServiceImpl.refreshSpuExtAttribute 更新mkuPO扩展属性，更新前groupExtAttribute:{}，更新后groupExtAttribute:{}", mkuPO.getGroupExtAttribute(),groupExtAttribute);
                mkuPO.setGroupExtAttribute(groupExtAttribute);
                // 更新mkuPO扩展属性
                mkuAtomicService.updateById(mkuPO);
            }
        }

        return DataResponse.success();
    }

    @Resource
    private SpuLangAtomicService spuLangAtomicService;



    @Override
    public String getSpuNameBySkuId(Long skuId, String lang) {

        if (skuId == null || StringUtils.isEmpty(lang)) {
            log.warn("参数不合法，skuId:{}，lang:{}", skuId, lang);
            return null;
        }


        SkuPO sku = skuAtomicService.getSkuPoBySkuId(skuId);

        if (sku == null) {
            log.info("找不到sku信息，skuId:{}", skuId);
            return null;
        }

        SpuLangPO spuLang = spuLangAtomicService.getSpuNameBuSpuIdAndLang(sku.getSpuId(), lang);

        if (spuLang == null) {
            log.info("找不到spuLang信息，skuId:{}", skuId);
            return null;
        }

        return spuLang.getSpuTitle();

    }

    private static final String UNDERLINE = "_";

    /**
     * 获取key
     * @param comGroupId
     * @param attributeId
     * @param attributeValueId
     * @param lang
     * @return
     */
    private static String getKey(Integer comGroupId,Long attributeId,Long attributeValueId, String lang) {
        if(Objects.isNull(comGroupId) || Objects.isNull(attributeId) || Objects.isNull(attributeValueId)){
            throw new BizException("comGroupId,attributeId,attributeValueId不可为空");
        }
        // 文本类型属性有lang，非文本类型只存储了属性值id 
        if(StringUtils.isBlank(lang)){
            // 下划线使用常量
            return comGroupId + UNDERLINE + attributeId + UNDERLINE + attributeValueId;
        }
        return comGroupId + UNDERLINE + attributeId + UNDERLINE + attributeValueId + UNDERLINE + lang;
    }

    /**
     * 从getKey方法中的返回值获取comGroupId,attributeId,attributeValueId,lang
     * @param key
     * @return
     */
    private static List<String> getIdList(String key) {
        String[] split = key.split(UNDERLINE);
        return Arrays.asList(split);
    }

    /**
     * 更新groupExtAttribute
     * @param groupExtAttribute
     * @param selectedPropertyValueVOMap
     * @return
     */
    private String updateGroupExtAttribute(String groupExtAttribute,Map<String,PropertyValueVO> selectedPropertyValueVOMap,Map<Long,Integer> attributeIdLevelMap){
        List<PropertyVO> propertyVOList = ExtendPropertyGroupVO.convertToPropertyVOList(groupExtAttribute);
        Map<String,PropertyValueVO> storePropertyValueVOMap = new HashMap<>();
        // 将groupExtAttribute转换为map，key为comGroupId_attributeId_attributeValueId,value为PropertyValueVO
        for(PropertyVO propertyVO : propertyVOList){
            if(Objects.nonNull(propertyVO.getPropertyValueVOList())){
                for(PropertyValueVO propertyValueVO : propertyVO.getPropertyValueVOList()){
                    storePropertyValueVOMap.put(getKey(propertyVO.getComGroupId(),propertyVO.getAttributeId(),propertyValueVO.getAttributeValueId(), propertyValueVO.getLang()), propertyValueVO);
                }
            }
        }
        // 将selectedPropertyValueVOMap中的属性值加入到storePropertyValueVOMap中，有的覆盖，没有的新增
        for(String key : selectedPropertyValueVOMap.keySet()){
            storePropertyValueVOMap.put(key, selectedPropertyValueVOMap.get(key));
        }
        // 将storePropertyValueVOMap转换为PropertyVOList
        List<PropertyVO> resultPropertyVOList = new ArrayList<>();

        // key是comGroupId_attributeId，value是list PropertyValueVO
        Map<String,List<PropertyValueVO>> propertyValueVOMap = new HashMap<>();
        for(String key : storePropertyValueVOMap.keySet()){
            List<String> idList = getIdList(key);
            if(propertyValueVOMap.containsKey(idList.get(0) + UNDERLINE + idList.get(1))){
                propertyValueVOMap.get(idList.get(0) + UNDERLINE + idList.get(1)).add(storePropertyValueVOMap.get(key));
            } else {
                List<PropertyValueVO> propertyValueVOList = new ArrayList<>();  
                propertyValueVOList.add(storePropertyValueVOMap.get(key));
                propertyValueVOMap.put(idList.get(0) + UNDERLINE + idList.get(1), propertyValueVOList);
            }
        }

        for(String key : propertyValueVOMap.keySet()){
            List<PropertyValueVO> propertyValueVOList = propertyValueVOMap.get(key);
            String[] idArray = key.split(UNDERLINE);
            Integer comGroupId = Integer.parseInt(idArray[0]);
            Long attributeId = Long.parseLong(idArray[1]);
            PropertyVO propertyVO = new PropertyVO();
            propertyVO.setComGroupId(comGroupId);
            propertyVO.setAttributeId(attributeId);
            propertyVO.setLevel(attributeIdLevelMap.get(attributeId));
            propertyVO.setPropertyValueVOList(propertyValueVOList);
            resultPropertyVOList.add(propertyVO);
        }
        return ExtendPropertyGroupVO.serializeExtendPropertyGroups(ExtendPropertyGroupVO.getExtendPropertyGroupVOS(resultPropertyVOList));
    }
}
