package com.jdi.isc.product.soa.service.protocol.jsf.auth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.auth.facade.request.role.RoleListRequest;
import com.jd.auth.facade.response.Response;
import com.jd.auth.facade.response.user.UserDto;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.attribute.common.AttributeDTO;
import com.jdi.isc.product.soa.api.attribute.req.AttributeQueryReqDTO;
import com.jdi.isc.product.soa.api.auth.IscProductSoaAuthReadApiService;
import com.jdi.isc.product.soa.api.auth.req.RoleListRequestDTO;
import com.jdi.isc.product.soa.api.auth.res.UserResDTO;
import com.jdi.isc.product.soa.api.common.enums.AttributeTypeEnum;
import com.jdi.isc.product.soa.rpc.auth.AuthRpcService;
import com.jdi.isc.product.soa.service.mapstruct.auth.AuthConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 权限读服务
 * <AUTHOR>
 * @date 2025/06/21
 **/
@Slf4j
@Service
public class IscProductSoaAuthReadApiServiceImpl implements IscProductSoaAuthReadApiService {

    @Resource
    private AuthRpcService authRpcService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<UserResDTO>> getUserByRoleList(RoleListRequestDTO reqDTO) {
        log.info("IscProductSoaAuthReadApiServiceImpl.getUserByRoleList param:{}", JSONObject.toJSONString(reqDTO));
        if(CollectionUtils.isEmpty(reqDTO.getRoleCodeList())){
            return DataResponse.error("参数为空");
        }
        RoleListRequest request = new RoleListRequest();
        request.setRoleCodeList(reqDTO.getRoleCodeList());
        Response<List<UserDto>> userListResp = authRpcService.getUserByRoleList(request);
        if (Objects.isNull(userListResp) || !"SUCCESS".equals(userListResp.getCode()) || CollectionUtils.isEmpty(userListResp.getData())) {
            log.warn("IscProductSoaAuthReadApiServiceImpl.getUserByRoleList 获取角色用户失败,request:{},response={}", JSON.toJSONString(request), JSON.toJSONString(userListResp));
            return DataResponse.error("返回接口为空");
        }
        List<UserDto> data = userListResp.getData();
        return DataResponse.success(AuthConvert.INSTANCE.listUserDto2ApiDto(data));
    }

}
