package com.jdi.isc.product.soa.service.protocol.jsf;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.stock.IscProductSoaStockWriteApiService;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockSplitReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockOccupyResDTO;
import com.jdi.isc.product.soa.service.manage.stock.StockManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

/**
 * 库存写服务
 * <AUTHOR>
 * @date 2024/6/3
 */
@Service
@Slf4j
public class IscProductSoaStockWriteApiServiceImpl implements IscProductSoaStockWriteApiService {

    @Resource
    private StockManageService stockManageService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> occupy(StockManageReqDTO req) {
        return stockManageService.occupy(req);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> release(StockManageReqDTO req) {
        return stockManageService.release(req);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> occupyReturn(StockManageReqDTO req) {
        return stockManageService.occupyReturn(req);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> stockReturn(StockManageReqDTO req) {
        return stockManageService.stockReturn(req);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> saveOrUpdate(StockManageReqDTO req) {
        return stockManageService.saveOrUpdate(req);
    }

    @Override
    public DataResponse<Boolean> resetStock(Set<Long> skuId) {
        return stockManageService.resetStock(skuId);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<StockOccupyResDTO> occupyRelease(StockManageReqDTO req) {
        return stockManageService.occupyRelease(req);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> transitStockReturn(StockManageReqDTO req) {
        return stockManageService.transitStockReturn(req);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> transitStockToStock(StockManageReqDTO req) {
        return stockManageService.transitStockToStock(req);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<StockOccupyResDTO> occupyStock(StockManageReqDTO req) {
        return stockManageService.occupyStock(req);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> releaseStock(StockManageReqDTO req) {
        return stockManageService.releaseStock(req);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> occupyStockReturn(StockManageReqDTO req) {
        return stockManageService.occupyStockReturn(req);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> stockNewReturn(StockManageReqDTO req) {
        return stockManageService.stockNewReturn(req);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> splitStock(StockSplitReqDTO req) {
        return stockManageService.splitStock(req);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> splitPurchaseOrderStock(StockSplitReqDTO req) {
        return stockManageService.splitPurchaseOrderStock(req);
    }
}
