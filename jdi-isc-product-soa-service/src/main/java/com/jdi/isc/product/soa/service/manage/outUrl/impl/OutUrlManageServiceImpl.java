package com.jdi.isc.product.soa.service.manage.outUrl.impl;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.sec_api.SecApi;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.outUrl.req.OutUrlMapDTO;
import com.jdi.isc.product.soa.common.util.S3Utils;
import com.jdi.isc.product.soa.domain.enums.FileTypeEnum;
import com.jdi.isc.product.soa.domain.url.po.OutUrlMapPO;
import com.jdi.isc.product.soa.domain.url.vo.OutUrlMapQueryVO;
import com.jdi.isc.product.soa.domain.url.vo.OutUrlTransformVO;
import com.jdi.isc.product.soa.service.atomic.url.OutUrlAtomicService;
import com.jdi.isc.product.soa.service.manage.outUrl.OutUrlManageService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 外链读写服务
 * <AUTHOR>
 * @date 20250401
 */
@Slf4j
@Service
public class OutUrlManageServiceImpl implements OutUrlManageService {

    @Resource
    private AsyncTaskExecutor dataHandleExecutor;

    @LafValue("out.url.transform.batch.size")
    private Integer batchSize;
    @LafValue("out.url.transform.file.max.size")
    private Long maxSize;
    private final static BigDecimal MB = new BigDecimal(1048576);
    private final static Integer TIME_OUT = 3;
    @Resource
    private S3Utils s3Utils;
    @Resource
    private OutUrlAtomicService outUrlAtomicService;

    /** 提交外链转换任务*/
    @Override
    @ToolKit(exceptionWrap = true)
    public void submitTransformTask(OutUrlMapDTO req) {
        List<OutUrlMapPO> target = buildPO(req);
        outUrlAtomicService.saveBatch(target);

        //todo 1.落库外链内链转换任务表,同时置状态为待处理
        //todo 2.定时扫描待处理转换任务,拉取创建时间最早的一个batch任务集乐观锁方式置为处理中
        //todo 3.批量处理外链转换，处理完写回结果，并发送 商品域处理状态


//        productCenterMqService.sendProductSoaMessageRetry(req.getBatchId(),req,outUrlTransformTopic);
    }

    /** 根据外链查询内链*/
    @Override
    public Map<String, OutUrlMapPO> getJdUrl(OutUrlMapQueryVO req) {
        Assert.isTrue(StringUtils.isBlank(req.getBatchId()) && CollectionUtils.isEmpty(req.getOutUrl()), "批次号及外链不能同时为空");
        Map<String, OutUrlMapPO> result = new HashMap<>(16);
        try {
            List<OutUrlMapPO> res = outUrlAtomicService.list(req);
            if(CollectionUtils.isNotEmpty(res)){
                result = res.stream().collect(Collectors.toMap(OutUrlMapPO::getOutUrl, Function.identity()));
            }
        }finally {
            log.info("OutUrlManageServiceImpl.getJdUrl req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(result));
        }
        return result;
    }

    /** 外链URL批量转内链*/
    @Override
    public DataResponse<Boolean> doUrlTransform(OutUrlMapDTO req) {
        List<OutUrlTransformVO> transformFinishedItem = new ArrayList<>(req.getOutUrl().size());
        //待转换集合
        List<List<String>> partitions = Lists.partition(new ArrayList<>(req.getOutUrl().keySet()), batchSize);
        for(List<String> batch : partitions){
            Map<String,Future<OutUrlTransformVO>> futures = new HashMap<>();
            try {
                for(String outUrl : batch){
                    futures.put(outUrl,dataHandleExecutor.submit(() -> downloadAndUpload(outUrl)));
                }
                List<OutUrlTransformVO> transformRes = waitToSuccess(futures);
                //如果有必填类文件转换失败的则整个任务都失败
                List<OutUrlTransformVO> fatalErr = transformRes.stream().filter(line -> !line.getSuccess() && line.getIsRequired()).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(fatalErr)){
                    buildErrMsg(req,fatalErr);
                    return DataResponse.success();
                }
                transformFinishedItem.addAll(transformRes);
            }catch (Exception e){
                String errMsg = String.format("DisposableProductIdentifyImportHandler.run invoke error,errMsg:%s", JSON.toJSONString(batch));
                log.error(errMsg);
            }
        }
        buildSuccessMsg(req,transformFinishedItem);
        return DataResponse.success();
    }

    /** 批量外链转内链*/
    @Override
    public List<OutUrlTransformVO> batchTransform(LinkedHashSet<String> outUrls) {
        List<OutUrlTransformVO> transformFinishedItem = new ArrayList<>(outUrls.size());
        //待转换集合
        List<List<String>> partitions = Lists.partition(new ArrayList<>(outUrls), batchSize);
        for(List<String> batch : partitions){
            Map<String,Future<OutUrlTransformVO>> futures = new HashMap<>();
            try {
                for(String outUrl : batch){
                    futures.put(outUrl,dataHandleExecutor.submit(() -> downloadAndUpload(outUrl)));
                }
                List<OutUrlTransformVO> transformRes = waitToSuccess(futures);
                transformFinishedItem.addAll(transformRes);
            }catch (Exception e){
                String errMsg = String.format("DisposableProductIdentifyImportHandler.run invoke error,errMsg:%s", JSON.toJSONString(batch));
                log.error(errMsg,e);
            }
        }
        return transformFinishedItem;
    }

    /** 组装转换失败结果并向商品发品发起完毕通知*/
    @Deprecated
    private void buildErrMsg(OutUrlMapDTO req, List<OutUrlTransformVO> fatalErr) {
        //todo 向下游通知等待@孙磊提供接口
        log.info("IscOutUrlApiServiceImpl.buildErrMsg req:{} , res:{}" ,  JSON.toJSONString(req), JSON.toJSONString(fatalErr));
        //todo 将该批次转换任务置为无效,并记录失败原因
    }

    /** 组装转换成功结果并向商品发品发起完毕通知*/
    @Deprecated
    private void buildSuccessMsg(OutUrlMapDTO req, List<OutUrlTransformVO> transformFinishedItem) {
        //todo 等待@孙磊提供接口
        log.info("IscOutUrlApiServiceImpl.buildSuccessMsg req:{} , res:{}" ,  JSON.toJSONString(req), JSON.toJSONString(transformFinishedItem));
        //todo 将该批次转换任务置为完成,并记录jdUrl
    }

    private OutUrlTransformVO downloadAndUpload(String outUrl){
        OutUrlTransformVO result = new OutUrlTransformVO(outUrl);
        try {
            if(!SecApi.validator().jdSsrfExternalCheck(outUrl)){
                String errMsg = "目标链接非有效连接,请检查并替换后重新上传发布";
                log.error("IscOutUrlApiServiceImpl.downloadAndUpload invoke error, errMsg:{}",errMsg);
                result.error(errMsg);
                return result;
            }
            String key = UUID.randomUUID().toString();
            byte[] bytes = HttpUtil.downloadBytes(outUrl);
            //文件为空
            if(bytes==null){
                String errMsg = "目标链接为无效附件,请尝试重新上传发布";
                log.error("IscOutUrlApiServiceImpl.downloadAndUpload invoke error, errMsg:{}",errMsg);
                result.error(errMsg);
                return result;
            }
            //文件太大
            if(bytes.length > maxSize){
                BigDecimal max = new BigDecimal(String.valueOf(maxSize)).divide(MB, 2, RoundingMode.HALF_UP);
                BigDecimal current = new BigDecimal(String.valueOf(bytes.length)).divide(MB, 2, RoundingMode.HALF_UP);
                String errMsg = String.format("最大支持%sMB的文件,目标链接大小为%s,请尝试更换更小的文件", max,current);
                log.error("IscOutUrlApiServiceImpl.downloadAndUpload invoke error, errMsg:{}",errMsg);
                result.error(errMsg);
                return result;
            }
            result.success(s3Utils.upload(new ByteArrayInputStream(bytes), FileTypeEnum.EXTERNAL_FILE.getCode(), key+".jpg"));
        }catch (Exception e){
            String errMsg = "目标链接无效或受保护导致无法解析,请尝试更换其他外链 ";
            log.error("IscOutUrlApiServiceImpl.downloadAndUpload invoke error, errMsg:{}", errMsg,e);
            result.error(errMsg);
        }finally {
            log.info("IscOutUrlApiServiceImpl.downloadAndUpload req:{} , res:{}" , outUrl , JSON.toJSONString(result));
        }
        return result;
    }

    @SneakyThrows
    private List<OutUrlTransformVO> waitToSuccess(Map<String,Future<OutUrlTransformVO>> futures) {
        List<OutUrlTransformVO> res = new ArrayList<>(futures.size());
        for (Map.Entry<String,Future<OutUrlTransformVO>> entry : futures.entrySet()) {
            try {
                res.add(entry.getValue().get(TIME_OUT, TimeUnit.SECONDS));
            }catch (Exception e){
                OutUrlTransformVO result = new OutUrlTransformVO(entry.getKey());
                result.error("目标链接转内链超时,请重试或更换");
                log.error("OutUrlManageServiceImpl.waitToSuccess invoke error ", e);
                res.add(result);
            }
        }
        return res;
    }

    private List<OutUrlMapPO> buildPO(OutUrlMapDTO req) {
        List<OutUrlMapPO> target = new ArrayList<>();
        for (Map.Entry<String,Boolean> entry : req.getOutUrl().entrySet()) {
            target.add(new OutUrlMapPO(req.getBatchId(),entry.getKey(),req.getSource(),entry.getValue()));
        }
        return target;
    }

}

