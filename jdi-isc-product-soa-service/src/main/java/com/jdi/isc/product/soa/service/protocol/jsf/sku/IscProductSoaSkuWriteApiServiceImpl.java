package com.jdi.isc.product.soa.service.protocol.jsf.sku;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.api.common.BaseResDTO;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.sku.IscProductSoaSkuWriteApiService;
import com.jdi.isc.product.soa.api.sku.req.BatchUpdateCustomsApiReqDTO;
import com.jdi.isc.product.soa.api.sku.req.SkuGlobalAttributeApiDTO;
import com.jdi.isc.product.soa.api.sku.req.SkuSyncNcmDTO;
import com.jdi.isc.product.soa.api.sku.req.SkuUpdateApiDTO;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.sku.biz.SkuGlobalAttributeVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.ProductGlobalAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.countryTax.BrSkuTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.sku.SkuWriteManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.BrSkuTaxManageService;
import com.jdi.isc.product.soa.service.mapstruct.sku.SkuConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * sku写服务
 * <AUTHOR>
 * @date 2024/08/26
 */
@Service
@Slf4j
public class IscProductSoaSkuWriteApiServiceImpl implements IscProductSoaSkuWriteApiService {

    @Resource
    private SkuWriteManageService skuWriteManageService;

    @Resource
    private BrSkuTaxAtomicService brSkuTaxAtomicService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;

    @Resource
    private BrSkuTaxManageService brSkuTaxManageService;

    @Override
    public DataResponse<List<SkuUpdateApiDTO>> updateSku(List<SkuUpdateApiDTO> skuUpdateApiDTOList){
        log.info("IscProductSoaSkuWriteApiServiceImpl.create param:{}", JSONObject.toJSONString(skuUpdateApiDTOList));
        BaseReqDTO dto = new BaseReqDTO();
        dto.setPin(skuUpdateApiDTOList.get(0).getUpdater());
        dto.setLang(LangConstant.LANG_ZH);
        ApiInitUtils.init(dto);
        Map<Long, SkuUpdateApiDTO> resultMap = skuUpdateApiDTOList.stream().collect(Collectors.toMap(SkuUpdateApiDTO::getSkuId, Function.identity()));
        List<SkuVO> skuVOList = SkuConvert.INSTANCE.apiDtoList2SkuVO(skuUpdateApiDTOList);
        Map<Long, SkuUpdateApiDTO> skuResultMap = skuWriteManageService.updateSkuInfo(skuVOList, resultMap);
        log.info("IscProductSoaSkuWriteApiServiceImpl.end param:{}", JSONObject.toJSONString(skuResultMap));
        return DataResponse.success(new ArrayList<>(skuResultMap.values()));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> updateGlobalAttribute(SkuGlobalAttributeApiDTO input) {
        log.info("IscProductSoaSkuWriteApiServiceImpl.updateGlobalAttribute param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        SkuGlobalAttributeVO param = SkuConvert.INSTANCE.skuGlobalAttributeDto2Vo(input);
        return DataResponse.success(skuWriteManageService.updateGlobalAttribute(param));
    }

    @Override
    public DataResponse<Void> saveProductGlobalNcmAttribute(SkuSyncNcmDTO input) {
        Long jdSkuId = input.getJdSkuId();
        String ncmCode = input.getNcmCode();
        String updater = input.getPin();
        log.info("更新商品的Ncm码, jdSkuId={}, ncmCode={}, updater={}", jdSkuId, ncmCode, updater);
        try {
            skuWriteManageService.saveProductGlobalNcmAttribute(input);

            return DataResponse.success();
        } catch (Exception e) {
            log.error("更新商品的Ncm码异常, jdSkuId={}, ncmCode={}, updater={}", jdSkuId, ncmCode, updater, e);
            if (e instanceof BizException) {
                return DataResponse.error(((BizException) e).getMsg());
            }
            return DataResponse.error("更新商品的Ncm码失败!");
        }
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<String, BaseResDTO>> batchUpdateCustoms(BatchUpdateCustomsApiReqDTO reqDTO) {
        return skuWriteManageService.batchUpdateCustoms(reqDTO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Integer> batchFixBrNcmCode(List<Long> jdSkuIds, boolean update, String updater) {
        return brSkuTaxManageService.batchFixBrNcmCode(jdSkuIds, update, updater);
    }
}
