package com.jdi.isc.product.soa.service.atomic.taxRate.countryTax;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.BrSkuTaxPO;
import com.jdi.isc.product.soa.price.api.price.req.BrSkuTaxVO;
import com.jdi.isc.product.soa.repository.mapper.taxRate.countryTax.BrSkuTaxBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 巴西SKU税率原子服务
 * <AUTHOR>
 * @date 20250311
 */
@Service
@Slf4j
public class BrSkuTaxAtomicService extends ServiceImpl<BrSkuTaxBaseMapper, BrSkuTaxPO> {

    /**
     * 单个查询
     */
    public BrSkuTaxPO getOne(BrSkuTaxVO vo) {
        LambdaQueryWrapper<BrSkuTaxPO> query = Wrappers.<BrSkuTaxPO>lambdaQuery()
                .eq(BrSkuTaxPO::getJdSkuId, vo.getJdSkuId())
                .eq(BrSkuTaxPO::getYn, YnEnum.YES.getCode());
        return super.getOne(query);
    }

    public BrSkuTaxPO getOneByJdSkuId(Long jdSkuId) {
        BrSkuTaxVO query = new BrSkuTaxVO();
        query.setJdSkuId(jdSkuId);
        return this.getOne(query);
    }

    public Map<Long, BrSkuTaxPO> mapByJdSkuId(Collection<Long> jdSkuIds) {
        if (CollectionUtils.isEmpty(jdSkuIds)) {
            return Maps.newHashMap();
        }
        LambdaQueryWrapper<BrSkuTaxPO> query = Wrappers.<BrSkuTaxPO>lambdaQuery()
                .in(BrSkuTaxPO::getJdSkuId, jdSkuIds)
                .eq(BrSkuTaxPO::getYn, YnEnum.YES.getCode());

        List<BrSkuTaxPO> list = super.list(query);

        return list.stream().collect(Collectors.toMap(BrSkuTaxPO::getJdSkuId, Function.identity()));
    }

    @PFTracing
    public Map<Long,BrSkuTaxPO> listSkuTax(Set<Long> skuIds){
        LambdaQueryWrapper<BrSkuTaxPO> queryWrapper = Wrappers.<BrSkuTaxPO>lambdaQuery()
                .in(BrSkuTaxPO::getJdSkuId, skuIds)
                .eq(BrSkuTaxPO::getYn, YnEnum.YES.getCode());
        List<BrSkuTaxPO> res = super.list(queryWrapper);
        if(CollectionUtils.isNotEmpty(res)){
            return res.stream().collect(Collectors.toMap(BrSkuTaxPO::getJdSkuId, a -> a));
        }
        return new HashMap<>();
    }

    public boolean updateTax(BrSkuTaxVO vo) {
        LambdaUpdateWrapper<BrSkuTaxPO> wrapper = Wrappers.<BrSkuTaxPO>lambdaUpdate()
                .set(StringUtils.isNotBlank(vo.getNcmCode()), BrSkuTaxPO::getNcmCode, vo.getNcmCode())
                .set(vo.getImportTax() != null, BrSkuTaxPO::getImportTax, vo.getImportTax())
                .set(vo.getIndustryProductTax() != null, BrSkuTaxPO::getIndustryProductTax, vo.getIndustryProductTax())
                .set(vo.getSocialIntegrationTax() != null, BrSkuTaxPO::getSocialIntegrationTax, vo.getSocialIntegrationTax())
                .set(vo.getConfinsTax() != null, BrSkuTaxPO::getConfinsTax, vo.getConfinsTax())
                .set(vo.getAntiDumpingTax() != null, BrSkuTaxPO::getAntiDumpingTax, vo.getAntiDumpingTax())
                .set(vo.getIcmsFlowTax() != null, BrSkuTaxPO::getIcmsFlowTax, vo.getIcmsFlowTax())
                .set(vo.getIsImportLimit() != null, BrSkuTaxPO::getIsImportLimit, vo.getIsImportLimit())
                .set(StringUtils.isNotBlank(vo.getRemark()), BrSkuTaxPO::getRemark, vo.getRemark())
                .set(BrSkuTaxPO::getUpdater, vo.getUpdater())
                .set(BrSkuTaxPO::getUpdateTime, new Date())
                .eq(BrSkuTaxPO::getId, vo.getId());
        return super.update(wrapper);
    }

    public Set<Long> getJdSkuIds(int index, int size) {
        Page<BrSkuTaxPO> page = new Page<>(index, size);
        LambdaQueryWrapper<BrSkuTaxPO> queryWrapper = Wrappers.<BrSkuTaxPO>lambdaQuery()
                .eq(BrSkuTaxPO::getYn, YnEnum.YES.getCode())
                .orderByDesc(BrSkuTaxPO::getUpdateTime);

        Page<BrSkuTaxPO> pageInfo = super.page(page, queryWrapper);

        if (pageInfo.getTotal() <= 0 || CollectionUtils.isEmpty(pageInfo.getRecords())) {
            return Sets.newHashSet();
        }

        return pageInfo.getRecords().stream().map(BrSkuTaxPO::getJdSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
    }
}
