package com.jdi.isc.product.soa.service.manage.approveorder.template.create;


import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyCreateReqApiDTO;
import com.jdi.isc.product.soa.api.approveorder.common.AuditActionEnum;
import com.jdi.isc.product.soa.api.approveorder.common.ApproveOrderStatusEnum;
import com.jdi.isc.product.soa.api.approveorder.common.AuditStatusEnum;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderCreateMqDTO;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderMqDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveDataDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveFormDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderSaveReqDTO;
import com.jdi.isc.product.soa.api.approveorder.res.ApproveOrderSaveResDTO;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.common.util.validation.ValidationUtil;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveOrderPO;
import com.jdi.isc.product.soa.domain.enums.AuditFormEnum;
import com.jdi.isc.product.soa.rpc.mq.ProductCenterMqService;
import com.jdi.isc.product.soa.rpc.utils.ExceptionUtil;
import com.jdi.isc.product.soa.service.atomic.approveorder.ApproveDataAtomicService;
import com.jdi.isc.product.soa.service.atomic.approveorder.ApproveFormAtomicService;
import com.jdi.isc.product.soa.service.atomic.approveorder.ApproveOrderAtomicService;
import com.jdi.isc.product.soa.service.support.audit.AuditService;
import com.jdi.isc.product.soa.service.support.transactional.ProductTransactionExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * The type Abstract create approve order exe.
 */
@Slf4j
public abstract class AbstractCreateApproveOrderExe {

    @Resource
    protected ProductTransactionExecutor transactionExecutor;

    @Resource
    protected ApproveOrderAtomicService approveOrderAtomicService;

    @Resource
    protected ApproveFormAtomicService approveFormAtomicService;

    @Resource
    protected ApproveDataAtomicService approveDataAtomicService;

    @Resource
    protected JimUtils jimUtils;

    @Resource
    protected AuditService auditService;

    public static final String PROFIT_RATE = "profitRate";
    public static final String PROFIT_LIMIT_RATE = "profitLimitRate";

    /**
     * 获取业务类型
     *
     * @return 业务类型标识符 biz type
     */
    public abstract int getBizType();

    /**
     * 获取当前bean的名称
     *
     * @return 返回当前bean的名称字符串 bean name
     */
    public abstract String getBeanName();

    @Resource
    protected OperDuccConfig operDuccConfig;

    @Resource
    private ProductCenterMqService productCenterMqService;


    /**
     * Execute data response.
     *
     * @param input the input
     * @return the data response
     */
    public DataResponse<ApproveOrderSaveResDTO> execute(ApproveOrderSaveReqDTO input) {

        Instant start = Instant.now();
        // 校验数据
        Pair<Boolean, String> checkParam = this.checkParam(input);

        if (!checkParam.getKey()) {
            log.info("AbstractCreateApproveOrderExe, 校验参数未通过. param={}, checkParam={}", JSONObject.toJSONString(input), checkParam.getValue());
            return DataResponse.error(checkParam.getValue());
        }

        Pair<Boolean, String> lockPair = this.tryLock(input);

        if (!lockPair.getKey()) {
            return DataResponse.error(lockPair.getValue());
        }

        try {
            // 校验数据
            this.checkData(input);

            // 补充属性
            this.fillDefaultValue(input);

            if (this.isAutoAuditPass(input)) {
               return executeAutoAuditPass(input);
            }
            // 构建
            JoySkyCreateReqApiDTO joySkyData = this.buildJoySkyCreateReqApiDTO(input);

            // 执行事务，创建审批单
            this.saveApproveOrder(input, joySkyData);

            // 执行事务，创建审批单
            ApproveOrderMqDTO<?> mqData = this.buildMqData(input);

            // 发送消息
            this.sendMessageRetry(mqData);
        } catch (Exception e) {
            log.error("保存审批单失败, param={}", JSONObject.toJSONString(input), e);
            return DataResponse.error(ExceptionUtil.getMessage(e, "发起审核失败，请稍后重试！"));
        } finally {
            log.error("保存审批单完成, param={}, cost={}", JSONObject.toJSONString(input), Duration.between(start, Instant.now()).toMillis());
            // 释放锁
            this.releaseLock(input);
        }

        // 返回结果
        return DataResponse.success();
    }

    /**
     * 是否走动审批通过
     */
    protected abstract boolean isAutoAuditPass(ApproveOrderSaveReqDTO input);

    /**
     * 自动审批通过业务逻辑
     */
    protected abstract DataResponse<ApproveOrderSaveResDTO> executeAutoAuditPass(ApproveOrderSaveReqDTO input);

    private void checkData(ApproveOrderSaveReqDTO input) {
        ApproveOrderDTO order = input.getOrder();

        // 查询进行中的审批单
        List<ApproveOrderPO> dbOrders = approveOrderAtomicService.listByBizTypeAndBizIdAndStatus(this.getBizType(), order.getBizId(), ApproveOrderStatusEnum.PROCESSING.getCode());

        if (CollectionUtils.isNotEmpty(dbOrders)) {
            throw new ProductBizException("[%s]正在审批中，请勿重复提交！", StringUtils.isEmpty(dbOrders.get(0).getApplyCode()) ? dbOrders.get(0).getId() : dbOrders.get(0).getApplyCode());
        }
    }

    protected Pair<Boolean, String> tryLock(ApproveOrderSaveReqDTO input) {
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.PRODUCT_APPROVE_CREATE_KEY, String.valueOf(input.getOrder().getBizType()), input.getOrder().getBizId());

        Boolean lock = jimUtils.simpleLock(lockKey, String.valueOf(Thread.currentThread().getId()), 60);

        if (!lock) {
            log.warn("当前记录正在操作，请勿重复操作！lockKey={}, threadId={}", lockKey, Thread.currentThread().getId());
            return Pair.of(false, "当前记录正在操作，请勿重复操作！");
        }

        return Pair.of(true, "");
    }


    protected void releaseLock(ApproveOrderSaveReqDTO input) {
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.PRODUCT_APPROVE_CREATE_KEY, String.valueOf(input.getOrder().getBizType()), input.getOrder().getBizId());

        try {
            jimUtils.simpleLockRelease(lockKey, String.valueOf(Thread.currentThread().getId()));
        } catch (Exception e) {
            log.error("释放所失败. lockKey={}, requestId={}", lockKey, Thread.currentThread().getId(), e);
        }
    }

    private Pair<Boolean, String> checkParam(ApproveOrderSaveReqDTO input) {

        String message = ValidationUtil.validateFindFirstError(input);

        if (StringUtils.isNotEmpty(message)) {
            return Pair.of(false, message);
        }

        if (input.getPin() == null) {
            return Pair.of(false, "操作人不能为空");
        }

        return Pair.of(true, "");
    }

    private void saveApproveOrder(ApproveOrderSaveReqDTO input, JoySkyCreateReqApiDTO joySkyData) {

        ApproveDataDTO approveData = this.buildApproveOrderDTO(input, joySkyData);

        transactionExecutor.execute(() -> {
            ApproveOrderDTO order = input.getOrder();
            List<ApproveFormDTO> orderFormList = input.getOrderFormList();

            // 创建审批单
            ApproveOrderPO orderPO = approveOrderAtomicService.insert(order);

            // 创建审批单明细
            approveFormAtomicService.batchInsert(orderPO, orderFormList);
            // 创建流程数据
            approveDataAtomicService.insert(orderPO, approveData);

            input.getOrder().setId(orderPO.getId());
        });
    }

    protected ApproveDataDTO buildApproveOrderDTO(ApproveOrderSaveReqDTO input, JoySkyCreateReqApiDTO joySkyData) {
        ApproveOrderDTO approveOrder = input.getOrder();
        ApproveDataDTO data = new ApproveDataDTO();
        data.setVersion(0);
        data.setBizType(approveOrder.getBizType());
        data.setFlowType(approveOrder.getFlowType());
        data.setBizId(approveOrder.getBizId());
        data.setApplyCode(approveOrder.getApplyCode());
        data.setApproveData(JSONObject.toJSONString(joySkyData));
        data.setRemark(StringUtils.EMPTY);
        return data;
    }

    /**
     * 填充数据.
     *
     * @param input the input
     */
    protected void fillDefaultValue(ApproveOrderSaveReqDTO input) {

        // 设置默认值
        fillOrderDefaultValue(input);

        // 设置默认值
        fillOrderFormDefaultValue(input);
    }

    protected void fillOrderFormDefaultValue(ApproveOrderSaveReqDTO input) {
        List<ApproveFormDTO> orderFormList = input.getOrderFormList();
        ApproveOrderDTO order = input.getOrder();

        if (CollectionUtils.isEmpty(orderFormList)) {
            return;
        }

        for (ApproveFormDTO item : orderFormList) {
            item.setBizType(this.getBizType());
            item.setFlowType(this.getFlowTypeCode());
            item.setBizId(order.getBizId());
            item.setApplyCode(order.getApplyCode());
            item.setVersion(0);
        }
    }

    protected void fillOrderDefaultValue(ApproveOrderSaveReqDTO input) {
        ApproveOrderDTO order = input.getOrder();
        order.setBizType(this.getBizType());
        order.setFlowType(this.getFlowTypeCode());
        order.setProcessInstanceId(StringUtils.EMPTY);
        order.setApplyCode(null);
        order.setAuditStatus(AuditStatusEnum.STATUS_1.getCode());
        order.setStatus(ApproveOrderStatusEnum.PROCESSING.getCode());
        order.setCurrentNodeAuditor(StringUtils.EMPTY);
        order.setCurrentNodeErp(StringUtils.EMPTY);
        order.setCurrentNodeName(StringUtils.EMPTY);
        order.setCurrentNodeAuditStatus(AuditStatusEnum.STATUS_1.getCode());
        order.setPreApproveFlag(null);
        order.setPreApproveComment(StringUtils.EMPTY);
        order.setApplyReason(StringUtils.isEmpty(order.getApplyReason()) ? StringUtils.EMPTY : order.getApplyReason());
    }

    /**
     * 构建mq对象
     */
    private ApproveOrderMqDTO<?> buildMqData(ApproveOrderSaveReqDTO input) {

        Integer auditAction = AuditActionEnum.CREATE.getCode();

        // mq key
        String businessId = String.format("%d_%d_%s", auditAction, input.getOrder().getBizType(), input.getOrder().getBizId());

        // 审核数据
        ApproveOrderCreateMqDTO data = new ApproveOrderCreateMqDTO(input.getOrder().getId());
        data.setBizId(input.getOrder().getBizId());
        data.setBizType(input.getOrder().getBizType());

        return new ApproveOrderMqDTO<>(input.getPin(), auditAction, businessId, data);
    }

    /**
     * 发送消息
     *
     * @param mqData the mq data
     */
    protected void sendMessageRetry(ApproveOrderMqDTO<?> mqData) {
        productCenterMqService.sendProductSoaMessageRetry(mqData.getBusinessId(), mqData, operDuccConfig.getApproveOrderTopic());
    }

    /**
     * 构建JoySky对象
     *
     * @param input the input
     * @return the joy sky create req api dto
     */
    JoySkyCreateReqApiDTO buildJoySkyCreateReqApiDTO(ApproveOrderSaveReqDTO input) {
        // 构建审批流对象
        JoySkyCreateReqApiDTO joySkyCreateReqApiDTO = new JoySkyCreateReqApiDTO();
        // 申请人erp
        joySkyCreateReqApiDTO.setErp(input.getOrder().getApplyUserErp());
        // 流程编码
        joySkyCreateReqApiDTO.setJoySkyFlowType(this.getFlowTypeCode());
        // 表单数据
        joySkyCreateReqApiDTO.setProcessFromData(this.constructProcessFormDataModel(input));
        // 流程控制数据
        joySkyCreateReqApiDTO.setProcessControlData(this.constructProcessControlData(input));

        return joySkyCreateReqApiDTO;
    }

    /**
     * Gets flow type code.
     *
     * @return the flow type code
     */
    public abstract int getFlowTypeCode();

    /**
     * Construct process form data model map.
     *
     * @param input the input
     * @return the map
     */
    protected Map<String, String> constructProcessFormDataModel(ApproveOrderSaveReqDTO input) {
        return input.getFieldCommentMap();
    }

    /**
     * Construct process control data map.
     *
     * @param input the input
     * @return the map
     */
    public abstract  Map<String, Object> constructProcessControlData(ApproveOrderSaveReqDTO input);


    /**
     * 获取审批表单key
     * {@link AuditFormEnum}
     */
    public abstract String getAuditFormKey();


    /**
     * 审核表单的键值对映射（键为字段名，值为字段标题）
     * @return 包含字段名与字段标题映射关系的Map集合
     */
    public Map<String, String> getKeyTitleMap() {
        return auditService.getKeyTitleMap(this.getAuditFormKey());
    }

    protected void addFiled(Map<String, String> keyTitleMap, ApproveOrderSaveReqDTO input, String fieldName, String fieldValue) {

        if (StringUtils.isEmpty(fieldName)) {
            throw new ProductBizException("fieldName is not empty");
        }

        String title = keyTitleMap.get(fieldName);

        if (StringUtils.isEmpty(title)) {
            throw new ProductBizException("title is not empty %s", fieldName);
        }

        input.addFiled(fieldName, title, fieldValue);
    }
}
