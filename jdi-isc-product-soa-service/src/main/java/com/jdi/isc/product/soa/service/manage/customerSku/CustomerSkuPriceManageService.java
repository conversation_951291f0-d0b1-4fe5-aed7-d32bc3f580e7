package com.jdi.isc.product.soa.service.manage.customerSku;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.TradeTypeConstant;
import com.jdi.isc.product.soa.api.common.enums.CustomerSkuDataSourceTypeEnums;
import com.jdi.isc.product.soa.api.common.enums.EnableStatusEnum;
import com.jdi.isc.product.soa.api.price.req.PriceUnsellableThresholdImportReqDTO;
import com.jdi.isc.product.soa.api.price.res.PriceUnsellableThresholdImportResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceDraftReqVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceDraftVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceVO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailDraftPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPricePO;
import com.jdi.isc.product.soa.domain.enums.SkuScopeEnum;
import com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.domain.enums.customerSku.CustomerTypeEnum;
import com.jdi.isc.product.soa.domain.enums.price.SkuSalePriceTypeEnums;
import com.jdi.isc.product.soa.domain.enums.spu.SkuTaxRateDraftEnums;
import com.jdi.isc.product.soa.domain.price.biz.MkuPriceReqVO;
import com.jdi.isc.product.soa.domain.price.po.PriceLogPO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.sku.biz.ExternalVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuExternalReqVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangVO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRateVO;
import com.jdi.isc.product.soa.price.api.salePrice.req.SalePriceReqDTO;
import com.jdi.isc.product.soa.price.api.salePrice.res.SalePriceResDTO;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.PriceLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.price.ExchangeRateManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.salePrice.SkuSalePriceManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuExternalManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.TaxRateManageService;
import com.jdi.isc.product.soa.service.mapstruct.customer.CustomerConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户sku价格读写服务
 * <AUTHOR>
 * @date 20231204
 */
@Service
@Slf4j
public class CustomerSkuPriceManageService extends BaseManageSupportService<CustomerSkuPriceVO, CustomerSkuPricePO> {

    /** sku价格服务*/
    @Resource
    private SkuPriceAtomicService skuPriceAtomicService;
    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private CustomerSkuPriceAtomicService customerSkuPriceAtomicService;
    @Resource
    private SkuPriceManageService skuPriceManageService;
    @Resource
    private CountryManageService countryManageService;
    @Resource
    private SkuExternalManageService skuExternalManageService;
    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    private CustomerSkuPriceDetailDraftAtomicService customerSkuPriceDetailDraftAtomicService;
    @Resource
    private CustomerSkuPriceDetailAtomicService customerSkuPriceDetailAtomicService;
    @Resource
    private PriceLogAtomicService priceLogAtomicService;
    @Resource
    private CustomerSkuPriceWarningManageService customerSkuPriceWarningManageService;
    @Resource
    private Map<String, SkuSalePriceManageService> skuSalePriceManageServiceMap;
    @Resource
    @Lazy
    private Map<String, TaxRateManageService> taxRateManageServiceMap;

    @Resource
    private ExchangeRateManageService exchangeRateManageService;

    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    @ToolKit(umpFlag = false)
    public String saveOrUpdate(CustomerSkuPriceVO input) {
        SkuPO sku = this.getAndValidateSku(input.getSkuId());
        CustomerVO customer = this.getAndValidateCustomer(input.getClientCode());
        String customerTradeType = CountryConstant.COUNTRY_ZH.equals(sku.getSourceCountryCode()) ? TradeTypeConstant.EXW : TradeTypeConstant.BD;

        Long detailDraftId = input.getId();
        CustomerSkuPriceDetailDraftPO customerSkuPriceDetailDraftPO = customerSkuPriceDetailDraftAtomicService.getValidById(detailDraftId);
        if(customerSkuPriceDetailDraftPO == null){
            throw new BizException("sku客制化价格明细草稿为空");
        }

        CustomerSkuPricePO customerSkuPricePO = this.getOrCreateSkuPricePO(customer.getClientCode(), customerTradeType, sku, customer,customerSkuPriceDetailDraftPO);
        customerSkuPriceAtomicService.saveOrUpdate(customerSkuPricePO);
        CustomerSkuPriceDetailPO customerSkuPriceDetailPO = this.getCustomerSkuPriceDetailPO(customerSkuPricePO, customerSkuPriceDetailDraftPO);
        customerSkuPriceDetailAtomicService.saveOrUpdate(customerSkuPriceDetailPO);
        // 写生效价格标
        this.triggerPriceEffective(customerSkuPriceDetailPO);

        // 价格预警
        customerSkuPriceWarningManageService.saveOrUpdateFromCustomerSku(detailDraftId, CustomerSkuDataSourceTypeEnums.CUSTOMER);

        // 添加价格变更记录
        PriceLogPO priceLogPO = this.getPriceLogPO(customerSkuPriceDetailPO);
        priceLogAtomicService.save(priceLogPO);

        // 设置正式态巴西税率
        if (customer.getCountry().equals(CountryConstant.COUNTRY_BR)) {
            this.approvedTaxRate(customerSkuPriceDetailPO.getClientCode(), customer.getCountry(), sku.getSkuId());
        }
        return Constant.SUCCESS;
    }

    @ToolKit(umpFlag = false)
    public PageInfo<CustomerSkuPriceVO> page(CustomerSkuPriceDraftReqVO input) {
        Page<CustomerSkuPriceVO> dbRecord = new Page<>(input.getIndex(), input.getSize());
        long total = customerSkuPriceAtomicService.getTotal(input);
        dbRecord.setTotal(total);

        if (total == 0) {
            return super.pageTransformVO(dbRecord);
        }

        List<CustomerSkuPriceVO> dbRecords = customerSkuPriceAtomicService.listSearch(input);
        dbRecord.setRecords(dbRecords);

        if (CollectionUtils.isEmpty(dbRecord.getRecords())) {
            return null;
        }

        PageInfo<CustomerSkuPriceVO> result = pageTransformVO(dbRecord);
        enrichSkuDetails(result);

        return result;
    }

    /**
     * 扩充 SKU 详细信息。
     * @param result 包含 CustomerSkuPriceVO 对象的 PageInfo 对象。
     */
    private void enrichSkuDetails(PageInfo<CustomerSkuPriceVO> result) {
        List<Long> skuIds = extractSkuIds(result);
        Map<Long, ExternalVO> skuMap = querySkuInfo(skuIds);

        for (CustomerSkuPriceVO target : result.getRecords()) {
            enrichSkuInfo(target, skuMap);
        }
    }

    /**
     * 从结果集中提取 SKU IDs。
     * @param result 包含 CustomerSkuPriceVO 对象的分页信息。
     * @return SKU IDs 的列表。
     */
    private List<Long> extractSkuIds(PageInfo<CustomerSkuPriceVO> result) {
        return result.getRecords().stream()
                .map(CustomerSkuPriceVO::getSkuId)
                .collect(Collectors.toList());
    }

    /**
     * 根据给定的 SKU IDs 查询对应的 ExternalVO 信息。
     * @param skuIds 需要查询的 SKU IDs 列表。
     * @return 包含查询结果的 Map 对象，键为 SKU ID，值为对应的 ExternalVO 对象。
     */
    private Map<Long, ExternalVO> querySkuInfo(List<Long> skuIds) {
        SkuExternalReqVO skuQuery = new SkuExternalReqVO();
        skuQuery.setSkuIds(skuIds);
        skuQuery.setScopeEnums(Sets.newHashSet(SkuScopeEnum.BASE, SkuScopeEnum.LANG));
        return skuExternalManageService.querySkuInfo(skuQuery);
    }

    /**
     * 增强 SKU 信息，包括设置客户购买价格、SKU 名称、计算毛利率和添加税率。
     * @param target 目标 CustomerSkuPriceVO 对象。
     * @param skuMap SKU 的外部信息映射表。
     */
    private void enrichSkuInfo(CustomerSkuPriceVO target, Map<Long, ExternalVO> skuMap) {
        setSkuName(target, skuMap);
        calculateGrossRate(target);
        addTaxRate(target);
        setEnableStatus(target);
        setBeginAndEndTime(target);
    }

    /**
     * 根据提供的 SKU ID 从 SKU 映射表中获取对应的中文名称，并设置到目标对象中。
     * @param target 目标对象，用于存储解析后的 SKU 名称。
     * @param skuMap SKU 映射表，包含 SKU ID 到 ExternalVO 的映射关系。
     */
    private void setSkuName(CustomerSkuPriceVO target, Map<Long, ExternalVO> skuMap) {
        ExternalVO skuLang = skuMap.get(target.getSkuId());
        if (skuLang != null) {
            SpuLangVO zhName = skuLang.getLangVOList().stream()
                    .filter(line -> LangConstant.LANG_ZH.equals(line.getLang()))
                    .findFirst().orElse(null);
            target.setSkuName(zhName != null ? zhName.getSpuTitle() : null);
        }
    }

    /**
     * 计算毛利率并设置到目标对象中。
     * @param target CustomerSkuPriceVO 对象，包含了客户的购买价格和销售价格。
     */
    private void calculateGrossRate(CustomerSkuPriceVO target) {
        if (Objects.nonNull(target.getCustomerPurchasePrice()) && Objects.nonNull(target.getCustomerSalePrice())) {
            BigDecimal divide = target.getCustomerSalePrice()
                    .subtract(target.getCustomerPurchasePrice())
                    .divide(target.getCustomerSalePrice(), 4, RoundingMode.HALF_UP);
            BigDecimal grossRate = divide.multiply(new BigDecimal("100"));
            target.setGrossRate(grossRate);
        }
    }

    /**
     * 为指定客户的商品添加税率信息。
     * @param target 客户商品价格信息对象，包含客户编码和商品ID等信息。
     */
    private void addTaxRate(CustomerSkuPriceVO target) {
        TaxRateVO taxRateVO = new TaxRateVO();
        taxRateVO.setClientCode(target.getClientCode());
        CustomerVO customerVO = customerManageService.detail(target.getClientCode());
        taxRateVO.setCountryCode(customerVO.getCountry());
        taxRateVO.setKeyId(String.valueOf(target.getSkuId()));
        taxRateVO.setClientType(CustomerTypeEnum.Client.getCode());

        TaxRateManageService taxRateManageService = taxRateManageService(SkuTaxRateDraftEnums.PASS.getType());
        List<TaxRateVO> taxRateVOList = taxRateManageService.listTaxRate(taxRateVO);
        target.setTaxRateList(taxRateVOList);
    }

    /**
     * 设置生效状态。
     * @param target 目标对象，包含客户代码、目标国家代码和 SKU ID。
     */
    private void setEnableStatus(CustomerSkuPriceVO target) {
        Long current = DateUtil.getCurrentTime();
        Long beginTime = target.getBeginTime();
        Long endTime = target.getEndTime();
        if (beginTime == null || endTime == null) {
            return;
        }
        if(YnEnum.NO.getCode().equals(target.getEnableStatus())){
            return;
        }

        // 根据当前时间设置生效状态
        if (current < beginTime) {
            target.setEnableStatus(EnableStatusEnum.NOT_EFFECTIVE.getCode()); // 未生效
        } else if (current <= endTime) {
            target.setEnableStatus(EnableStatusEnum.EFFECTIVE.getCode()); // 生效中
        } else {
            target.setEnableStatus(EnableStatusEnum.EXPIRED.getCode()); // 已过期
        }
    }

    /**
     * 根据条件设置开始和结束时间
     * @param input CustomerSkuPriceDraftVO 对象，包含开始和结束时间信息
     */
    private void setBeginAndEndTime(CustomerSkuPriceVO input){
        if(input.getBeginTime() != null && Constant.ZERO.equals(input.getBeginTime())){
            input.setBeginTime(null);
        }
        if(input.getEndTime() != null && Constant.MAX.equals(input.getEndTime())){
            input.setEndTime(null);
        }
    }

    /**
     * 获取符合条件的总数
     * @param reqVO 查询条件对象
     * @return 符合条件的记录总数
     */
    public long getTotal(CustomerSkuPriceDraftReqVO reqVO){
        return customerSkuPriceAtomicService.getTotal(reqVO);
    }

    /** 获取未税销价和采购价,客户价不存在则降级查base销价*/
    @Transactional(rollbackFor = Exception.class, timeout = 60)
    @PFTracing
    public Map<Long, CustomerSkuPricePO> getBaseSaleAndSupplierPrice(MkuPriceReqVO input){
        // 查询sku基础信息
        Map<Long, SkuPO> skuPOMap = skuAtomicService.getSkuMap(input.getSkuIdList());
        // 查询价格
        List<SkuPricePO> skuBaseAllPriceList = skuPriceAtomicService.getPriceMap(input.getSkuIdList());
        //base采购价map
        Map<Long, SkuPricePO> baseSupplyPriceMap = skuBaseAllPriceList.stream().filter(line -> TradeDirectionEnum.SUPPLIER.equals(line.getTradeDirection()))
                .collect(Collectors.toMap(SkuPricePO::getSkuId, Function.identity()));
        CustomerVO customerVO = customerManageService.detail(input.getClientCode());

        List<CustomerSkuPricePO> customerSkuPriceList = new ArrayList<>();
        for(Long skuId : input.getSkuIdList()){
            SalePriceReqDTO salePriceReqDTO = new SalePriceReqDTO();
            salePriceReqDTO.setSkuId(skuId);
            salePriceReqDTO.setClientCode(input.getClientCode());
            salePriceReqDTO.setCustomerDTO(CustomerConvert.INSTANCE.customerVo2Dto(customerVO));
            salePriceReqDTO.setCurrencyCode(customerVO.getSaleCurrency());
            SalePriceResDTO salePriceResDTO = skuSalePriceManageServiceMap.get(SkuSalePriceTypeEnums.getEnumByCountryCode(customerVO.getCountry()).getServiceName())
                    .getSalePrice(salePriceReqDTO);
            CustomerSkuPricePO customerSkuPricePO = new CustomerSkuPricePO();
            customerSkuPricePO.setClientCode(customerVO.getClientCode());
            customerSkuPricePO.setSkuId(skuId);
            customerSkuPricePO.setCurrency(customerVO.getSaleCurrency());
            if(salePriceResDTO != null){
                customerSkuPricePO.setCustomerSalePrice(salePriceResDTO.getSalePrice());
            }
            SkuPO skuPO = skuPOMap.get(skuId);
            if(skuPO != null){
                String customerTradeType = CountryConstant.COUNTRY_ZH.equals(skuPO.getSourceCountryCode()) ? TradeTypeConstant.EXW : TradeTypeConstant.BD;
                customerSkuPricePO.setCustomerTradeType(customerTradeType);
                customerSkuPricePO.setVendorCode(skuPO.getVendorCode());
            }

            SkuPricePO skuPricePO = baseSupplyPriceMap.getOrDefault(customerSkuPricePO.getSkuId(),new SkuPricePO());
            if(Objects.nonNull(skuPricePO)){
                if(!StringUtils.equals(skuPricePO.getCurrency(),customerVO.getSaleCurrency())){
                    DataResponse<BigDecimal> exchangeRate = exchangeRateManageService.getExchangeRateByCurrency(skuPricePO.getCurrency(), customerVO.getSaleCurrency());
                    if(exchangeRate.getSuccess()){
                        customerSkuPricePO.setCustomerPurchasePrice(skuPricePO.getPrice().multiply(exchangeRate.getData()));
                    }
                } else{
                    customerSkuPricePO.setCustomerPurchasePrice(skuPricePO.getPrice());
                }
            }

            customerSkuPriceList.add(customerSkuPricePO);
        }
        return customerSkuPriceList.stream().collect(Collectors.toMap(CustomerSkuPricePO::getSkuId, Function.identity()));
    }

    /**
     * 根据税率类型获取对应的 TaxRateManageService 实例。
     * @param type 税率类型，例如 "vat" 或 "gst"。
     * @return 对应税率类型的 TaxRateManageService 实例。
     */
    public TaxRateManageService taxRateManageService(String type) {
        return taxRateManageServiceMap.get(SkuTaxRateDraftEnums.getEnumByType(type).getServiceName());
    }

    /**
     * 根据 SKU ID 获取并验证 SKU 是否存在。
     * @param skuId SKU 的唯一标识符。
     * @return 对应的 SkuPO 对象。
     * @throws BizException 如果 SKU 不存在。
     */
    public SkuPO getAndValidateSku(Long skuId) {
        SkuPO sku = skuAtomicService.getSkuPoBySkuId(skuId);
        if (sku == null) {
            throw new BizException(String.format("国际SKU%s不存在", skuId));
        }
        return sku;
    }

    /**
     * 根据SPU ID获取并验证SPU信息
     * @param spuId SPU的唯一标识
     * @return 对应的SPU信息
     */
    public SpuPO getAndValidateSpu(Long spuId) {
        SpuPO spu = spuAtomicService.getSpuPoBySpuId(spuId);
        if (spu == null) {
            throw new BizException(String.format("国际SPU%s不存在", spuId));
        }
        return spu;
    }

    /**
     * 根据客户代码获取并验证客户信息。
     * @param clientCode 客户代码
     * @return 验证通过的客户信息
     */
    public CustomerVO getAndValidateCustomer(String clientCode) {
        CustomerVO customer = customerManageService.detail(clientCode);
        if (customer == null) {
            throw new BizException(String.format("客户%s不存在", clientCode));
        }
        return customer;
    }

    /**
     * 创建或更新巴西税率。
     * @param input 客户 SKU 价格草稿对象
     * @param customer 客户对象
     * @param skuTaxRateDraftEnums SKU 税率草稿枚举类型
     */
    public void createBRTaxRate(CustomerSkuPriceDraftVO input, CustomerVO customer, SkuTaxRateDraftEnums skuTaxRateDraftEnums){
        if(!customer.getCountry().equals(CountryConstant.COUNTRY_BR)){
            log.info("CustomerSkuPriceManageServiceImpl.setBRTaxRate");
            return;
        }
        TaxRateManageService taxRateManageService = taxRateManageService(skuTaxRateDraftEnums.getType());
        List<TaxRateVO> taxRateList = input.getTaxRateList();
        for (TaxRateVO taxRateVO : taxRateList){
            if(StringUtils.isBlank(taxRateVO.getCountryCode())){
                taxRateVO.setCountryCode(CountryConstant.COUNTRY_BR);
            }
        }
        // 保存货更新税率
        taxRateManageService.saveOrUpdateBatch(taxRateList);
    }

    /**
     * 根据给定的客户代码、客户交易类型、商品ID、货币和其他相关信息，获取或创建一个客户商品价格草稿对象。
     * @param clientCode 客户代码
     * @param customerTradeType 客户交易类型
     * @param sku 商品对象
     * @param customer 客户对象
     * @return 客户商品价格草稿对象
     */
    private CustomerSkuPricePO getOrCreateSkuPricePO(String clientCode, String customerTradeType
            , SkuPO sku, CustomerVO customer,CustomerSkuPriceDetailDraftPO input) {
        CustomerSkuPricePO customerSkuPricePO = customerSkuPriceAtomicService.getOne(clientCode, sku.getSkuId(),customerTradeType , input.getCurrency());
        if (customerSkuPricePO == null) {
            customerSkuPricePO = new CustomerSkuPricePO();
            customerSkuPricePO.setClientCode(clientCode);
            customerSkuPricePO.setSkuId(sku.getSkuId());
            customerSkuPricePO.setCurrency(input.getCurrency());
            customerSkuPricePO.setCustomerTradeType(customerTradeType);
            customerSkuPricePO.setCustomerSalePrice(new BigDecimal(9999));
            customerSkuPricePO.setClientName(customer.getClientName());
            customerSkuPricePO.setVendorCode(sku.getVendorCode());
            customerSkuPricePO.setCreateTime(DateUtil.long2date(input.getCreateTime()));
            customerSkuPricePO.setCreator(input.getCreator());
        }
        customerSkuPricePO.setUpdater(input.getUpdater());
        customerSkuPricePO.setUpdateTime(DateUtil.long2date(input.getUpdateTime()));
        return customerSkuPricePO;
    }

    /**
     * 根据输入参数和其他对象信息，创建并返回一个 CustomerSkuPriceDetailDraftPO 对象。
     * @param customerSkuPriceDetailDraftPO CustomerSkuPriceDetailDraftPO 对象，用于存储生成的客户 SKU 价格详细信息。
     * @return CustomerSkuPriceDetailDraftPO 对象，包含生成的客户 SKU 价格详细信息。
     */
    private CustomerSkuPriceDetailPO getCustomerSkuPriceDetailPO(CustomerSkuPricePO customerSkuPricePO, CustomerSkuPriceDetailDraftPO customerSkuPriceDetailDraftPO) {
        CustomerSkuPriceDetailPO customerSkuPriceDetailPO = customerSkuPriceDetailAtomicService.getValidBySourceId(customerSkuPriceDetailDraftPO.getId());
        if(customerSkuPriceDetailPO == null){
            customerSkuPriceDetailPO = new CustomerSkuPriceDetailPO();
            customerSkuPriceDetailPO.setCreator(customerSkuPriceDetailDraftPO.getUpdater());
            customerSkuPriceDetailPO.setCreateTime(new Date().getTime());
            customerSkuPriceDetailPO.setSkuId(customerSkuPriceDetailDraftPO.getSkuId());
        } else {
            customerSkuPriceDetailPO.setSkuId(null);
        }
        customerSkuPriceDetailPO.setBizId(customerSkuPricePO.getId());
        customerSkuPriceDetailPO.setSourceId(customerSkuPriceDetailDraftPO.getId());
        customerSkuPriceDetailPO.setClientCode(customerSkuPriceDetailDraftPO.getClientCode());
        customerSkuPriceDetailPO.setCustomerTradeType(customerSkuPriceDetailDraftPO.getCustomerTradeType());
        customerSkuPriceDetailPO.setCurrency(customerSkuPriceDetailDraftPO.getCurrency());
        customerSkuPriceDetailPO.setEnableStatus(YesOrNoEnum.YES.getCode());
        customerSkuPriceDetailPO.setCreator(customerSkuPriceDetailDraftPO.getCreator());
        customerSkuPriceDetailPO.setCreateTime(new Date().getTime());
        customerSkuPriceDetailPO.setBeginTime(customerSkuPriceDetailDraftPO.getBeginTime());
        customerSkuPriceDetailPO.setEndTime(customerSkuPriceDetailDraftPO.getEndTime());
        customerSkuPriceDetailPO.setCustomerSalePrice(customerSkuPriceDetailDraftPO.getCustomerSalePrice());
        customerSkuPriceDetailPO.setUpdater(customerSkuPriceDetailDraftPO.getUpdater());
        customerSkuPriceDetailPO.setUpdateTime(new Date().getTime());
        customerSkuPriceDetailPO.setEndDay(getEndDay(customerSkuPriceDetailDraftPO.getEndTime()));
        customerSkuPriceDetailPO.setEffectiveStatus(0);

        return customerSkuPriceDetailPO;
    }

    /** 获取yyyy-MM-dd HH:mm:ss 的 yyyy-MM-dd 00:00:00 时间戳(便于后续定时任务by日期扫描过期客制化价格)*/
    private Long getEndDay(Long endTime) {
        try {
            if(Constant.MAX.equals(endTime)){
                return endTime;
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(endTime);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            return calendar.getTimeInMillis();
        }catch (Exception e){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING,endTime+"解析异常");
            e.printStackTrace();
        }
        return endTime;
    }

    /** 写生效价格标*/
    private void triggerPriceEffective(CustomerSkuPriceDetailPO customerSkuPriceDetailPO){
        long now = System.currentTimeMillis();
        //如果当前记录是生效状态,则需要将同clientCode\SkuId下的其他记录进行生效态轮换
        if(YesOrNoEnum.YES.getCode() == customerSkuPriceDetailPO.getEnableStatus() && now >= customerSkuPriceDetailPO.getBeginTime() && now <= customerSkuPriceDetailPO.getEndTime()){
            //1.查询库中已生效的sku价格,将其置为无效
            List<CustomerSkuPriceDetailPO> existItem = customerSkuPriceDetailAtomicService.listByEffectiveStatus(customerSkuPriceDetailPO.getClientCode(), customerSkuPriceDetailPO.getSkuId(),1);
            if(CollectionUtils.isNotEmpty(existItem)){
                existItem.forEach(line->{
                    line.setEffectiveStatus(0);
                    customerSkuPriceDetailAtomicService.updateEffectiveStatusById(line);
                });
            }
            //2.将当前价格明细设置为有效
            customerSkuPriceDetailPO.setEffectiveStatus(1);
            boolean res = customerSkuPriceDetailAtomicService.updateEffectiveStatusById(customerSkuPriceDetailPO);
            log.info("CustomerSkuPriceManageService.triggerPriceEffective req:{} , res:{}" ,  JSON.toJSONString(customerSkuPriceDetailPO), res);
        }
    }

    private void approvedTaxRate(String clientCode,String targetCountryCode,Long skuId){
        log.info("BrCustomerSkuPriceApproveService.approvedTaxRate clientCode:{},targetCountryCode:{},skuId:{}", clientCode,targetCountryCode,skuId);
        TaxRateVO taxRateVO = new TaxRateVO();
        taxRateVO.setCountryCode(targetCountryCode);
        taxRateVO.setClientCode(clientCode);
        taxRateVO.setKeyType(KeyTypeEnum.SKU.getCode());
        taxRateVO.setKeyId(String.valueOf(skuId));
        taxRateVO.setClientType(CustomerTypeEnum.Client.getCode());
        TaxRateManageService draftTaxRateManageService = taxRateManageService(SkuTaxRateDraftEnums.DRAFT.getType());
        List<TaxRateVO> taxRateVOList = draftTaxRateManageService.listTaxRate(taxRateVO);
        TaxRateManageService passTaxRateManageService = taxRateManageService(SkuTaxRateDraftEnums.PASS.getType());
        DataResponse<Boolean> response = passTaxRateManageService.saveOrUpdateBatch(taxRateVOList);
        log.info("BrCustomerSkuPriceApproveService.approvedTaxRate draft:{},pass:{}", JSONObject.toJSONString(taxRateVOList),JSONObject.toJSONString(response));
    }

    /**
     * 根据CustomerSkuPriceDetailPO对象创建并返回PriceLogPO对象。
     * @param param CustomerSkuPriceDetailPO对象，包含客户SKU价格详细信息。
     */
    private PriceLogPO getPriceLogPO(CustomerSkuPriceDetailPO param){
        PriceLogPO priceLogPO = new PriceLogPO();
        priceLogPO.setBizId(param.getId().toString());
        priceLogPO.setBizType(com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum.CUSTOMER_PRICE.getCode());
        priceLogPO.setBizValue(param.getCustomerSalePrice());
        priceLogPO.setValue1(param.getBeginTime() == null?null:param.getBeginTime().toString());
        priceLogPO.setValue2(param.getEndTime() == null?null:param.getEndTime().toString());
        priceLogPO.setCreateTime(DateUtil.getCurrentTime());
        priceLogPO.setCreator(param.getCreator());
        priceLogPO.setUpdater(param.getUpdater());
        priceLogPO.setUpdateTime(DateUtil.getCurrentTime());
        return priceLogPO;
    }

    public DataResponse<PriceUnsellableThresholdImportResDTO> updateUnsellableThreshold(PriceUnsellableThresholdImportReqDTO input) {

        PriceUnsellableThresholdImportResDTO result = new PriceUnsellableThresholdImportResDTO();

        List<PriceUnsellableThresholdImportResDTO.Item> items = Lists.newArrayList();
        for (PriceUnsellableThresholdImportReqDTO.Item item : input.getItems()) {
            Long id = item.getId();
            BigDecimal unsellableThreshold = item.getUnsellableThreshold();

            try {
                customerSkuPriceDetailAtomicService.updateUnsellableThreshold(id, unsellableThreshold, input.getPin());
                items.add(new PriceUnsellableThresholdImportResDTO.Item(id, unsellableThreshold, true, "success"));
            } catch (Exception e) {
                log.error("updateUnsellableThreshold error, item={}", JSONObject.toJSONString(item), e);
                items.add(new PriceUnsellableThresholdImportResDTO.Item(id, unsellableThreshold, false, e instanceof BizException ? e.getMessage() : "更新不可售阈值失败"));
            } finally {
                log.info("更新客制化价格不可售阈值. id={}, unsellableThreshold={}", id, unsellableThreshold);
            }
        }

        result.setItems(items);

        return DataResponse.success(result);
    }
}
