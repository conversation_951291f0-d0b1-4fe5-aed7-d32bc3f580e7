package com.jdi.isc.product.soa.service.mapstruct.auth;

import com.jd.auth.facade.response.user.UserDto;
import com.jdi.isc.product.soa.api.auth.res.UserResDTO;
import com.jdi.isc.product.soa.domain.area.biz.AreaInfoVO;
import com.jdi.isc.product.soa.domain.area.biz.AreaNameInfoVO;
import com.jdi.isc.product.soa.domain.area.biz.AreaNodeTreeRenderVO;
import com.jdi.isc.product.soa.domain.area.biz.GlobalAreaInfoVO;
import com.jdi.isc.product.soa.domain.area.po.AreaNameInfoPO;
import com.jdi.isc.product.soa.domain.area.po.GlobalAreaInfoPO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * @Description: 权限人员
 * @Author: zhaokun51
 * @Date: 2025/06/21
 */
@Mapper
public interface AuthConvert {

    AuthConvert INSTANCE = Mappers.getMapper(AuthConvert.class);

    List<UserResDTO> listUserDto2ApiDto(List<UserDto> inputs);
}
