package com.jdi.isc.product.soa.service.atomic.mku;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.ScriptSortType;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.SourceConfig;
import co.elastic.clients.elasticsearch.core.search.SourceFilter;
import com.jdi.isc.product.soa.common.frame.EsAtomicSupportService;
import com.jdi.isc.product.soa.domain.mku.po.es.MkuEsPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import co.elastic.clients.elasticsearch._types.query_dsl.Operator;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: MKU ES 原子服务 V2 (纯粹使用 ES 7.x Client)
 * @Author: YourName (请替换为您的名字)
 * @Date: 2024/07/26 (请替换为当前日期)
 */
@Component
@Slf4j
public class MkuEsAtomicServiceV2 extends EsAtomicSupportService<MkuEsPO> {

    @Resource
    private ElasticsearchClient elasticsearchClient;

    public SearchResponse<MkuEsPO> search(List<String> keywords, List<Long> catIds, String clientCode, String indexName, int index, int size, Set<Integer> featureTags, Integer stockFlag, String langCode) {
        try {
            SearchRequest.Builder requestBuilder = new SearchRequest.Builder().index(indexName);

            // 构建查询条件
            BoolQuery boolQuery = buildSearchQuery(keywords, catIds, clientCode, featureTags, stockFlag, langCode);
            requestBuilder.query(q -> q.bool(boolQuery));
            // -- 排序逻辑 --
            if (CollectionUtils.isEmpty(featureTags)) {
                // 未指定标签时，使用48小时标+库存分层排序
                // 优先级：48小时有货 > 48小时无货 > 非48小时有货 > 非48小时无货
                String priorityScriptSource =
                        "int score = 0;" +
                                "boolean has48Tag = false;" +
                                "for (item in doc['feature_tags']) { " +
                                "    if (item == 48) { " +
                                "        has48Tag = true; " +
                                "        break; " +
                                "    } " +
                                "}" +
                                "boolean hasStock = doc['has_stock'].size() > 0 && doc['has_stock'].value == 2;" +
                                "if (has48Tag && hasStock) { score = 100; }" +
                                "else if (has48Tag && !hasStock) { score = 90; }" +
                                "else if (!has48Tag && hasStock) { score = 80; }" +
                                "else { score = 70; }" +
                                "return score;";

                requestBuilder.sort(s -> s.script(sc -> sc
                        .type(ScriptSortType.Number)
                        .order(SortOrder.Desc)
                        .script(p -> p.inline(i -> i.source(priorityScriptSource)))
                ));
            } else {
                // 指定了标签时，只按库存排序
                requestBuilder.sort(s -> s.field(f -> f.field("has_stock").order(SortOrder.Desc)));
            }

            // 2. 相关度分数排序
            requestBuilder.sort(s -> s.score(sc -> sc.order(SortOrder.Desc)));

            // 3. ID 排序：保证结果稳定
            requestBuilder.sort(s -> s.field(f -> f.field("_id").order(SortOrder.Asc)));

            requestBuilder.source(SourceConfig.of(sc -> sc
                    .filter(SourceFilter.of(sf -> sf
                            .includes("mku_id","feature_tags")
                    ))
            ));

            // 分页
            int from = (index > 0) ? (index - 1) * size : 0;
            requestBuilder.from(from).size(size);
            requestBuilder.trackTotalHits(t -> t.enabled(true));

            // 执行查询
            SearchRequest searchRequest = requestBuilder.build();
            log.info("MkuEsAtomicServiceV2.search request: {}", super.getRequestBody(searchRequest));
            SearchResponse<MkuEsPO> response = elasticsearchClient.search(searchRequest, MkuEsPO.class);
            log.info("MkuEsAtomicServiceV2.search response took: {}ms", response.took());
            return response;

        } catch (IOException e) {
            log.error("【系统异常】MkuEsAtomicServiceV2.search error", e);
            return null;
        }
    }

    private BoolQuery buildSearchQuery(List<String> keywords, List<Long> catIds, String clientCode, Set<Integer> featureTags, Integer stockFlag, String langCode) {
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        // 基础过滤
        boolQueryBuilder.filter(f -> f.term(t -> t.field("yn").value(1)));
        boolQueryBuilder.filter(f -> f.term(t -> t.field("client_code").value(clientCode)));

        // 分类过滤
        if (CollectionUtils.isNotEmpty(catIds)) {
            List<FieldValue> catIdValues = catIds.stream().map(FieldValue::of).collect(Collectors.toList());
            boolQueryBuilder.filter(f -> f.terms(t -> t.field("jd_cat_id").terms(ts -> ts.value(catIdValues))));
        }

        // 标签过滤
        if (CollectionUtils.isNotEmpty(featureTags)) {
            List<FieldValue> featureTagValues = featureTags.stream().map(FieldValue::of).collect(Collectors.toList());
            boolQueryBuilder.filter(f -> f.terms(t -> t.field("feature_tags").terms(ts -> ts.value(featureTagValues))));
        }

        // 库存过滤
        if (stockFlag != null) {
            boolQueryBuilder.filter(f -> f.term(t -> t.field("has_stock").value(stockFlag)));
        }

        // 关键词查询
        if (CollectionUtils.isNotEmpty(keywords)) {
            // 第一个关键词是用户的原始查询，用于加权。其余的是提炼后的关键词，用于精确匹配。
            String originalQuery = keywords.get(0);
            List<String> refinedKeywords = keywords.size() > 1 ? keywords.subList(1, keywords.size()) : keywords;
            String refinedKeywordsString = String.join(" ", refinedKeywords);

            // -- 定义字段组 --
            // 标题字段
            List<String> titleFields = new ArrayList<>();
            if ("en".equalsIgnoreCase(langCode)) {
                titleFields.add("mku_title_en^2"); titleFields.add("mku_title"); titleFields.add("mku_title_zh");
            } else if ("zh".equalsIgnoreCase(langCode)) {
                titleFields.add("mku_title_zh^2"); titleFields.add("mku_title"); titleFields.add("mku_title_en");
            } else {
                titleFields.add("mku_title^2"); titleFields.add("mku_title_en"); titleFields.add("mku_title_zh");
            }
            // 其他相关字段
            List<String> otherFields = Arrays.asList("pre_word", "brand_name");

            // -- 构建布尔查询 --
            // 1. MUST (必须) - 所有返回结果必须精确匹配所有提炼后的关键词
            List<String> allSearchFields = new ArrayList<>(titleFields);
            allSearchFields.addAll(otherFields);
            boolQueryBuilder.must(m -> m.multiMatch(mm -> mm
                    .query(refinedKeywordsString)
                    .fields(allSearchFields)
                    .operator(Operator.And)
            ));

            // 2. SHOULD (加分项) - 根据匹配字段不同，给予不同程度的加分
            // a) 如果原始查询词匹配到标题，给予超高加分
            boolQueryBuilder.should(s -> s.multiMatch(mm -> mm
                    .query(originalQuery)
                    .fields(titleFields)
                    .operator(Operator.And)
                    .boost(10.0f) 
            ));
            // b) 如果原始查询词匹配到其他字段，给予普通加分
            boolQueryBuilder.should(s -> s.multiMatch(mm -> mm
                    .query(originalQuery)
                    .fields(otherFields)
                    .operator(Operator.And)
                    .boost(2.0f)
            ));
        }

        return boolQueryBuilder.build();
    }
}
