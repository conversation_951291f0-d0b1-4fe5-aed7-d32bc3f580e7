package com.jdi.isc.product.soa.service.manage.mku.impl;

import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.fulfillment.soa.api.promiseTime.res.PromiseTimeTagResApiDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.FeatureTagOperatorEnum;
import com.jdi.isc.product.soa.api.common.enums.MkuFeatureTagEnum;
import com.jdi.isc.product.soa.api.common.enums.PromiseTimeTagEnum;
import com.jdi.isc.product.soa.api.common.enums.SkuSpecialAttrEnum;
import com.jdi.isc.product.soa.api.stock.common.StockConstants;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientDetailReqApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuEsStockTagReqDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuPoolFlagDTO;
import com.jdi.isc.product.soa.common.constants.EsConstants;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.util.StockNumUtils;
import com.jdi.isc.product.soa.domain.brand.po.BrandPO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryComboBoxVO;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPricePO;
import com.jdi.isc.product.soa.domain.ducc.ChatGptProperties;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.domain.enums.mku.MkuRelationBindStatusEnum;
import com.jdi.isc.product.soa.domain.mku.biz.*;
import com.jdi.isc.product.soa.domain.mku.po.MkuLangPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.mku.po.es.MkuEsPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuVO;
import com.jdi.isc.product.soa.domain.spu.po.SpuLangPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.stock.po.StockPO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseResVO;
import com.jdi.isc.product.soa.rpc.promise.PromiseTimeInfoRpcService;
import com.jdi.isc.product.soa.service.atomic.brand.BrandAtomicService;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuEsAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.stock.StockAtomicService;
import com.jdi.isc.product.soa.service.manage.brand.BrandOutService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.chatGpt.ChatGptService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuEsManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuReadManageService;
import com.jdi.isc.product.soa.service.manage.warehouse.WarehouseManageService;
import com.jdi.isc.product.soa.service.mapstruct.mku.MkuConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * @Description: 客户端服务类
 * @Author: zhaokun51
 * @Date: 2024/06/24 16:30
 **/
@Slf4j
@Service
public class MkuEsManageServiceImpl implements MkuEsManageService {

    @Resource
    private MkuAtomicService mkuAtomicService;
    @Resource
    private MkuLangAtomicService mkuLangAtomicService;
    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;
    @Resource
    private CustomerMkuPriceAtomicService customerMkuPriceAtomicService;
    @Resource
    private BrandAtomicService brandAtomicService;
    @Resource
    private CategoryOutService categoryOutService;
    @Resource
    private BrandOutService brandOutService;
    @Resource
    private ChatGptService chatGptService;
    @Resource
    private MkuEsAtomicService mkuEsAtomicService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;
    @Resource
    private SpuLangAtomicService spuLangAtomicService;
    @Resource
    private CountryManageService countryManageService;
    @Resource
    private SpuReadManageService spuReadManageService;
    @Resource
    private OperDuccConfig operDuccConfig;
    @Resource
    private CountryMkuAtomicService countryMkuAtomicService;
    @Resource
    private WarehouseManageService warehouseManageService;
    @Resource
    private StockAtomicService stockAtomicService;
    @Resource
    private PromiseTimeInfoRpcService promiseTimeInfoRpcService;
    @Resource
    private ExecutorService chatGptExecutorService;

    @Value("${spring.profiles.active}")
    private String systemProfile;
    @LafValue("isc.message.broker.warehouseSku.country")
    private Set<String> warehouseSkuCountry;


    @Override
    public Boolean upsert2Es(MkuClientDetailReqVO input) {

        // 获取数据
        Long mkuId = input.getMkuId();
        String clientCode = input.getClientCode();

        try {
            // 校验传参
            this.checkParam(input);

            // ES实体类
            MkuEsPO mkuEsPO = new MkuEsPO();

            // 客户相关校验及赋值、true:上品，false:下品
            CustomerVO customerVO = customerManageService.detail(clientCode);
            Boolean flag = this.setCustomerInfo(customerVO,mkuId,mkuEsPO);

            String lang = countryManageService.getLangByCountryCode(customerVO.getCountry());
            if (isJinMen(clientCode)) {
                lang = LangConstant.LANG_ZH_HANT;
            }
            if(StringUtils.isBlank(lang)){
                log.info("MkuEsManageServiceImpl.upsert2Es lang is null");
                return Boolean.FALSE;
            }

            Set<String> preWords = new HashSet<>();


            String indexName = EsConstants.getMkuEsName(customerVO.getCountry());
            if (isJinMen(clientCode)) {
                indexName = EsConstants.getMkuEsName(CountryConstant.COUNTRY_HK);
            }

            // 在国家池标识
            Integer poolFlag = this.getPoolFlag(mkuId,customerVO.getClientCode(),customerVO.getCountry());

            MkuPO mkuPO = mkuAtomicService.getById(mkuId);

            // 设置id
            mkuEsPO.setId(String.join("_",clientCode,mkuId.toString()));

            // 下品
            if(!flag
                    || mkuPO==null
                    || mkuPO.getYn().equals(YnEnum.NO.getCode())
                    || poolFlag.equals(YnEnum.NO.getCode())
            ){
                log.info("MkuEsManageServiceImpl.upsertToEs delete document");
                mkuEsAtomicService.delete(indexName,mkuEsPO.getId());
                return Boolean.TRUE;
            }

            // 上品-设置mkuInfo
            this.setMkuInfo(mkuId,mkuEsPO,lang,preWords);

            // 上品-设置品牌
            this.setBrandInfo(mkuPO.getBrandId(),lang,mkuEsPO,preWords);

            // 上品-设置类目
            this.setCategoryInfo(mkuPO.getJdCatId(),lang,mkuEsPO,preWords);

            // 上品-设置关键字
            this.setKeysInfo(mkuPO.getMkuId(),lang,mkuEsPO,preWords);

            // 上品-组装聚合KEY
            mkuEsPO.setAggKey(this.getAggKey(mkuEsPO.getClientCode(),mkuEsPO.getMkuId()));

            // todo 上线前刷数据预留，上完线可以干掉此逻辑 上品-设置预命中分词集合
            String preWord = String.join(" ", preWords);
            mkuEsPO.setPreWord(preWord);

            // 设置时效标签，例如48小时备货标、7日达等时效标
            mkuEsPO.setFeatureTags(buildPromiseTimeTag(mkuEsPO, customerVO));

            //设置库存标识
            mkuEsPO.setHasStock(buildMkuEsStockFlag(mkuEsPO, customerVO.getCountry()));

            mkuEsPO.setCreateTime(new Date().getTime());
            mkuEsPO.setUpdateTime(new Date().getTime());
            mkuEsPO.setYn(YnEnum.YES.getCode());

            return mkuEsAtomicService.upsert(indexName,mkuEsPO);
        }catch (Exception e){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s 同步es异常,clientCode: %s,mkuId: %s, "
                    + "异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , clientCode
                    , mkuId
                    , e.getMessage()));
            throw new BizException(e.getMessage());
        }
    }

    /**
     * 根据商品信息和客户国家构建承诺时效标签集合
     * @param mkuEsPO 商品信息对象
     * @param customerVO 客户信息对象，包含国家等属性
     * @return 包含承诺时效标签的集合（48小时标签或7天标签）
     */
    private  Set<Integer>  buildPromiseTimeTag(MkuEsPO mkuEsPO, CustomerVO customerVO) {
        Set<Integer> featureTags = mkuEsPO.getFeatureTags();
        try {
            if (CollectionUtils.isEmpty(featureTags)) {
                featureTags = new HashSet<>();
            }

            Integer promiseTag = this.getPromiseDeliveryTag(mkuEsPO, customerVO.getCountry());
            if (Objects.isNull(promiseTag) || Objects.equals(promiseTag, PromiseTimeTagEnum.DEFAULT_EMPTY_TAG.getCode())) {
                //初始化es时效数据，没有获取到履约时效标签，则不做处理
                return featureTags;
            } else {
                //添加时效标签
                MkuFeatureTagEnum mkuFeatureTagEnum = MkuFeatureTagEnum.queryByFulfilPromise(SkuSpecialAttrEnum.FULFIL_PROMISE, promiseTag);
                if (mkuFeatureTagEnum != null) {
                    featureTags.add(mkuFeatureTagEnum.getEsCode());
                }
            }
        } catch (Exception e) {
            log.error("MkuManageServiceImpl.buildPromiseTimeTag error, mkuEsPO:{},country:{}", JSONObject.toJSONString(mkuEsPO), customerVO.getCountry(), e);
        } finally {
            log.info("MkuManageServiceImpl.buildPromiseTimeTag , mkuEsPO:{},country:{}, featureTags:{}", JSONObject.toJSONString(mkuEsPO), customerVO.getCountry(), featureTags);
        }

        return featureTags;
    }

    /**
     * 获取商品履约时效标(例如48小时达、7日达等等)
     * @param mkuEsPO 商品ES模型对象，包含商品基本信息及特征标签集合
     * @param country 国家代码，用于日志记录和业务判断
     * @return 更新后的商品特征标签集合（包含7天交付承诺标签值）
     */
    public Integer getPromiseDeliveryTag(MkuEsPO mkuEsPO, String country) {
        Integer promiseValue = PromiseTimeTagEnum.DEFAULT_EMPTY_TAG.getCode();
        try {
            String clientCode = mkuEsPO.getClientCode();
            Long mkuId = mkuEsPO.getMkuId();
            Map<Long, PromiseTimeTagResApiDTO> promiseTimeTagResApiDTOMap = promiseTimeInfoRpcService.batchQueryPromiseTags(clientCode, Lists.newArrayList(mkuId));
            if (org.springframework.util.CollectionUtils.isEmpty(promiseTimeTagResApiDTOMap) || !promiseTimeTagResApiDTOMap.containsKey(mkuId)) {
                log.warn("addPromiseSevenDayDeliveryTag empty promiseVal, clientCode:{}, mkuId:{}", clientCode, mkuId);
                return promiseValue;
            }

            PromiseTimeTagResApiDTO promiseTimeTagResApiDTO = promiseTimeTagResApiDTOMap.get(mkuId);
            promiseValue = promiseTimeTagResApiDTO.getTagVal() != null ? promiseTimeTagResApiDTO.getTagVal() : PromiseTimeTagEnum.DEFAULT_EMPTY_TAG.getCode();
            return promiseValue;
        } catch (Exception e) {
            //告警
            log.error("MkuManageServiceImpl.addPromiseSevenDayDeliveryTag error,mkuEsPO:{},country:{}", JSONObject.toJSONString(mkuEsPO), country, e);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_MKU_TAG_WARNING, String.format("【%s】 %s 同步es时查mku时效打标信息, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , e.getMessage()));
        } finally {
            log.info("addPromiseSevenDayDeliveryTag finished country:{},mkuId:{},promiseValue:{}", country, mkuEsPO.getMkuId(), promiseValue);
        }

        return PromiseTimeTagEnum.DEFAULT_EMPTY_TAG.getCode();
    }

    /**
     * 根据mkuId、clientCode和targetCountryCode获取非国家池标志。
     * @param mkuId MKU的唯一标识符。
     * @param clientCode 客户端代码。
     * @param targetCountryCode 目标国家代码。
     * @return 非国家池标志，1表示是，0表示否。
     */
    private Integer getPoolFlag(Long mkuId,String clientCode,String targetCountryCode){
        List<CountryMkuPO> countryMkuPOS = countryMkuAtomicService.getPoByMkuIdCountryPoolStatus(Lists.newArrayList(mkuId),targetCountryCode,
                CountryMkuPoolStatusEnum.POOL.getCode());
        if(CollectionUtils.isEmpty(countryMkuPOS)){
            log.info("MkuEsManageServiceImpl.getPoolFlag countryMkuPOS is null,mkuId:{},clientCode:{},targetCountryCode:{}", mkuId,clientCode,targetCountryCode);
            return YesOrNoEnum.NO.getCode();
        }
        for(CountryMkuPO countryMkuPO : countryMkuPOS){
            if(countryMkuPO != null && countryMkuPO.getMkuId().equals(mkuId)){
                return YesOrNoEnum.YES.getCode();
            }
        }
        return YesOrNoEnum.NO.getCode();
    }

    /**
     * 根据商品信息和国家代码构建商品库存标记
     * @param mkuEsPO 商品ES信息对象，包含商品ID等基本信息
     * @param countryCode 国家代码，用于确定合适的库存
     * @return 库存标记值(StockConstants.IN_STOCK表示有货，其他值表示无货)
     */
    private Integer buildMkuEsStockFlag(MkuEsPO mkuEsPO, String countryCode) {
        //新绑定商品默认标记有货
        Integer hasStockFlag = StockConstants.IN_STOCK;

        try {
            Long mkuId = mkuEsPO.getMkuId();
            List<MkuRelationPO> mkuRelationList = mkuRelationAtomicService.queryBindListByMkuId(mkuId);
            if (CollectionUtils.isEmpty(mkuRelationList)) {
                log.warn("MkuManageServiceImpl.buildMkuEsStockFlag queryBindListByMkuId empty, mkuId:{}", mkuId);
                return hasStockFlag;
            }

            //查询库存信息
            Set<Long> skuIds = mkuRelationList.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toSet());
            Map<Long, List<StockPO>> skuStockMap = stockAtomicService.getStockMap(skuIds);
            if (org.springframework.util.CollectionUtils.isEmpty(skuStockMap)) {
                log.warn("MkuManageServiceImpl.buildMkuEsStockFlag getStockMap empty, skuIds:{}", skuIds);
                return hasStockFlag;
            }

            //选择并计算库存标签(一个mku只对应一个sku)
            Map.Entry<Long, List<StockPO>> firstEntry = skuStockMap.entrySet().iterator().next();
            Long skuId = firstEntry.getKey();
            List<StockPO> stockList = firstEntry.getValue();
            StockPO suitableStock = decisionSuitableStockBySkuId(stockList, countryCode);
            if (suitableStock == null) {
                log.warn("MkuManageServiceImpl.buildMkuEsStockFlag decisionSuitableStockBySkuId error, skuId:{}", skuId);
                return hasStockFlag;
            }

            hasStockFlag = calculateStockFlag(suitableStock);
        } catch (Exception e) {
            log.error("MkuManageServiceImpl.buildMkuEsStockFlag failed! mkuEsPO:{}", JSON.toJSONString(mkuEsPO));
        } finally {
            log.info("MkuManageServiceImpl.buildMkuEsStockFlag country:{}, mkuId:{},clientCode:{}, hasStockFlag:{}", countryCode, mkuEsPO.getMkuId(), mkuEsPO.getClientCode(), hasStockFlag);
        }
        return hasStockFlag;
    }

    /**
     * 根据库存信息计算库存标识
     * @param suitableStock 库存信息对象，包含仓库ID和库存数量等信息
     * @return 库存标识，可能值为：备货仓库存(IN_STOCK)、在途库存(IN_TRANSIT_STOCK)或无库存(NO_STOCK)
     */
    private  Integer calculateStockFlag(StockPO suitableStock) {
        Integer hasStockFlag;
        if (suitableStock.getWarehouseId() != null) {
            //备货仓库存
            Long inStockAvailableStock = StockNumUtils.calculateAvailableStock(suitableStock, Boolean.FALSE);
            if (inStockAvailableStock > 0) {
                hasStockFlag = StockConstants.IN_STOCK;
            } else {
                Long onWayAvailableStock = StockNumUtils.calculateAvailableStock(suitableStock, Boolean.TRUE);
                if (onWayAvailableStock > 0) {
                    hasStockFlag = StockConstants.IN_TRANSIT_STOCK;
                } else {
                    hasStockFlag = StockConstants.NO_STOCK;
                }
            }
        } else {
            //厂直库存
            Long inStockAvailableStock = StockNumUtils.calculateAvailableStock(suitableStock, Boolean.FALSE);
            if (inStockAvailableStock > 0) {
                hasStockFlag = StockConstants.IN_STOCK;
            } else {
                hasStockFlag = StockConstants.NO_STOCK;
            }
        }
        return hasStockFlag;
    }

    /**
     * 根据 SKU ID 和客户国家代码从库存列表中决策出最合适的库存
     * @param stockList 库存列表，包含多个库存对象
     * @param clientCountryCode 客户国家代码，用于匹配备货仓
     * @return 返回最匹配的库存对象，若无匹配则返回 null
     */
    private StockPO decisionSuitableStockBySkuId(List<StockPO> stockList, String clientCountryCode) {
        if (CollectionUtils.isEmpty(stockList)) {
            return null;
        }

        if (stockList.size() == 1) {
            return stockList.get(0);
        }

        // 筛选出有备货仓 ID 的库存
        List<StockPO> warehouseStockList = stockList.stream()
                .filter(stock -> StringUtils.isNotBlank(stock.getWarehouseId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(warehouseStockList)) {
            // 只有厂直库存
            return stockList.get(0);
        }

        // 将备货仓库存分组
        Map<Long, List<StockPO>> warehouseStockMap = warehouseStockList.stream()
                .collect(Collectors.groupingBy(stock -> Long.valueOf(stock.getWarehouseId())));

        // 查询备货仓信息
        List<WarehouseResVO> warehouseDetails = warehouseManageService.queryByWarehouseIds(warehouseStockMap.keySet());

        // 根据国家代码匹配备货仓
        WarehouseResVO matchingWarehouse = warehouseDetails.stream()
                .filter(warehouse -> clientCountryCode.equals(warehouse.getCountryCode()))
                .findFirst()
                .orElse(null);

        if (matchingWarehouse != null) {
            List<StockPO> matchedStocks = warehouseStockMap.get(matchingWarehouse.getId());
            if (!CollectionUtils.isEmpty(matchedStocks)) {
                //todo 后续sku一国多仓时此处还需要迭代(需要再增加条件进行判断)
                return matchedStocks.get(0);
            }
        }

        return null;
    }


    @Override
    public Boolean upsert2Es4Mku(MkuClientDetailReqVO input) {
        Long mkuId = input.getMkuId();
        if(mkuId == null){
            log.error("MkuManageServiceImpl.upsert2Es4Mku mkuId is null");
            return Boolean.FALSE;
        }

        // 如果clientCode不为空，说明是绑定和非绑定的关系
        if(StringUtils.isNotBlank(input.getClientCode()) ){
            return this.upsert2Es(input);
        }

        List<CustomerMkuPO> mkuPOS = customerMkuAtomicService.listNoClientCode(mkuId);
        if(CollectionUtils.isEmpty(mkuPOS)){
            log.info("MkuManageServiceImpl.upsert2Es4Mku mkuPOS is null");
            return Boolean.FALSE;
        }

        // 批量处理
        mkuPOS.forEach(item->{
            input.setClientCode(item.getClientCode());
            this.upsert2Es(input);
        });

        return Boolean.TRUE;
    }

    @Override
    public Boolean upsert2Es4MkuPage(String clientCode, Integer startPageNum, Integer endPageNum) {
        if (StringUtils.isBlank(clientCode) || startPageNum<1 || startPageNum>endPageNum ){
            log.info("MkuManageServiceImpl.upsertToEsBatchPage, fail, param error. clientCode={}, startPageNum={}, endPageNum={}", clientCode, startPageNum, endPageNum);
            return Boolean.FALSE;
        }
        Stopwatch stopwatch = Stopwatch.createStarted();

        int pageSize = 1000;
        log.info("MkuManageServiceImpl.upsertToEsBatchPage, start. clientCode={}, startPageNum={}, endPageNum={}, pageSize={}", clientCode, startPageNum, endPageNum, pageSize);
        for(int pageNum=startPageNum; pageNum<=endPageNum; pageNum++){
            try {
                int offset = (pageNum - 1)*pageSize;

                LambdaQueryWrapper<CustomerMkuPO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.select(CustomerMkuPO::getId,CustomerMkuPO::getClientCode,
                        CustomerMkuPO::getMkuId,CustomerMkuPO::getCreateTime);
                queryWrapper.eq(CustomerMkuPO::getClientCode,clientCode);
                queryWrapper.eq(CustomerMkuPO::getYn, YnEnum.YES.getCode());
                queryWrapper.eq(CustomerMkuPO::getBindStatus, MkuRelationBindStatusEnum.BIND);
                queryWrapper.orderByAsc(CustomerMkuPO::getCreateTime);
                queryWrapper.last("LIMIT " + offset+","+pageSize);

                List<CustomerMkuPO> dbRecords = customerMkuAtomicService.list(queryWrapper);
                if (CollectionUtils.isEmpty(dbRecords)){
                    log.info("MkuManageServiceImpl.upsert2Es4MkuPage, break, dbRecords is  empty, i={}, startPageNum={}, endPageNum={}", pageNum, startPageNum, endPageNum);
                    break;
                }
                Set<Long> mkuIds = dbRecords.stream().filter(o->null!=o.getMkuId()).map(o->o.getMkuId()).collect(Collectors.toSet());

                if(CollectionUtils.isEmpty(mkuIds)){
                    log.info("MkuManageServiceImpl.upsert2Es4MkuPage, break, dbRecords is  empty, i={}, startPageNum={}, endPageNum={}", pageNum, startPageNum, endPageNum);
                    return Boolean.FALSE;
                }

                for(Long mkuId : mkuIds){
                    MkuClientDetailReqVO reqVO = new MkuClientDetailReqVO();
                    reqVO.setMkuId(mkuId);
                    reqVO.setClientCode(clientCode);
                    try{
                        this.upsert2Es(reqVO);
                    }catch (Exception e){
                        log.error("【系统异常】MkuManageServiceImpl.upsertToEsBatchPage, input={}, rpcResponse={}", JSONObject.toJSONString(reqVO), e);
                    }

                }

                Thread.sleep(100);
            } catch (Exception e) {
                log.error("【系统异常】eptSetStockAllSkus, i={}", pageNum, e);
            }
        }
        log.info("MkuManageServiceImpl, end. storeCodes={}, startPageNum={}, endPageNum={}, elapsed= {} ms", clientCode, startPageNum, endPageNum, stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return Boolean.TRUE;
    }

    /**
     * 转换商品标识
     */
    private  Set<Integer> transformFeatureTags(MkuClientPageReqVO input) {
        Set<String> featureTagStr = input.getFeatureTags();
        Set<Integer> featureTags = new HashSet<>();
        if (CollectionUtils.isNotEmpty(featureTagStr)) {

            featureTagStr.forEach(f -> {
                MkuFeatureTagEnum tagEnum = MkuFeatureTagEnum.queryCode(f);
                if (Objects.nonNull(tagEnum)) {
                    featureTags.add(tagEnum.getEsCode());
                }
            });
        }
        return featureTags;
    }

    @Override
    public List<String> attempt(MkuClientPageReqVO input) {
        CustomerVO customerVO = customerManageService.detail(input.getClientCode());
        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
            log.error("MkuManageServiceImpl.attempt, no match customerVO. input={}", input);
            throw new BizException("无匹配的客户");
        }

        String lang = input.getLang();
        if(StringUtils.isBlank(lang)){
            log.error("MkuManageServiceImpl.attempt, lang is null.");
            return new ArrayList<>();
        }

        String indexName = EsConstants.getMkuEsName(customerVO.getCountry());
        if (isJinMen(input.getClientCode())) {
            indexName = EsConstants.getMkuEsName(CountryConstant.COUNTRY_HK);
        }

        SearchResponse<MkuEsPO> response = mkuEsAtomicService.attempt(this.processKeyword(input.getKeyword()),input.getCatIds(),input.getClientCode()
                ,input.getLang(),indexName,input.getSize().intValue());
        if(response.hits() == null || response.hits().total() == null || response.hits().total().value() < 1){
            log.info("MkuManageServiceImpl.page response is null");
            return new ArrayList<>();
        }
        List<Hit<MkuEsPO>> hits = response.hits().hits();
        List<String> results = new ArrayList<>();

        hits.forEach(hit->{
            MkuEsPO esPO = hit.source();
            if(esPO != null){
                if(LangConstant.LANG_EN.equals(lang) && StringUtils.isNotBlank(esPO.getMkuTitleEn())){
                    results.add(esPO.getMkuTitleEn());
                } else if (LangConstant.LANG_ZH.equals(lang) && StringUtils.isNotBlank(esPO.getMkuTitleZh())){
                    results.add(esPO.getMkuTitleZh());
                } else {
                    results.add(esPO.getMkuTitle());
                }
            }
        });
        return results;
    }

    @Override
    public PageInfo<SpuVO> queryPageForSpu(MkuClientPageReqVO input) {
        // 校验传参
        Boolean checkFlag = this.checkPageParam(input);
        if(!checkFlag){
            throw new BizException("查询参数为空");
        }
        Set<Integer> featureTags = transformFeatureTags(input);
        CustomerVO customerVO = customerManageService.detail(input.getClientCode());
        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
            log.error("MkuEsManageServiceImpl.queryPageForSpu, no match customerVO. input={}", input);
            throw new BizException("无匹配的客户");
        }

        String indexName = EsConstants.getMkuEsName(customerVO.getCountry());
        if (isJinMen(input.getClientCode())) {
            indexName = EsConstants.getMkuEsName(CountryConstant.COUNTRY_HK);
        }

        SearchResponse<MkuEsPO> response = mkuEsAtomicService.search(this.gptGenerateWords(input.getKeyword(),input.getLang()),input.getCatIds()
                ,input.getClientCode(),indexName,input.getIndex().intValue(),input.getSize().intValue(),featureTags, input.getStockFlag(),input.getLang());

        PageInfo<SpuVO> result = new PageInfo<>();
        result.setIndex(input.getIndex());
        result.setSize(input.getSize());
        if(response.hits() == null || response.hits().total() == null || response.hits().total().value() < 1){
            log.info("MkuEsManageServiceImpl.queryPageForSpu response is null");
            result.setTotal(0L);
            result.setRecords(Collections.emptyList());
            return result;
        }

        result.setTotal(response.hits().total().value());
        result.setRecords(this.buildSpuVO(response
                ,input.getClientCode()));

        return result;
    }


    /**
     * 校验参数
     * */
    private void checkParam(MkuClientDetailReqVO input){
        if (null == input.getMkuId()) {
            log.error("MkuEsManageServiceImpl.checkParam, mkuId error. input={}", JSONObject.toJSONString(input));
            throw new BizException("MkuEsManageServiceImpl.checkParam, mkuId is null");
        }

        if (StringUtils.isBlank(input.getClientCode())) {
            log.warn("MkuEsManageServiceImpl.checkParam, clientCode error. input={}", JSONObject.toJSONString(input));
            throw new BizException("MkuEsManageServiceImpl.checkParam, clientCode is null");
        }
    }

    private void checkSearchParam(MkuClientDetailReqVO input) {
        //mku及clientCode创建
        checkParam(input);

        //国家信息创建
        if (StringUtils.isBlank(input.getCountry())) {
            log.warn("MkuEsManageServiceImpl.checkParam, CountryCode error. input={}", JSONObject.toJSONString(input));
            throw new BizException("MkuEsManageServiceImpl.checkParam, CountryCode is null");
        }
    }

    private void checkUpdateParam(MkuEsStockTagReqDTO input) {
        if (null == input.getMkuId()) {
            log.warn("MkuEsManageServiceImpl.checkUpdateParam, mkuId error. input={}", JSONObject.toJSONString(input));
            throw new BizException("MkuEsManageServiceImpl.checkParam, mkuId is null");
        }

        if (StringUtils.isBlank(input.getClientCode())) {
            log.warn("MkuEsManageServiceImpl.checkUpdateParam, clientCode error. input={}", JSONObject.toJSONString(input));
            throw new BizException("MkuEsManageServiceImpl.checkParam, clientCode is null");
        }

        //国家校验
        if (StringUtils.isBlank(input.getCountry())) {
            log.warn("MkuEsManageServiceImpl.checkUpdateParam, CountryCode error. input={}", JSONObject.toJSONString(input));
            throw new BizException("MkuEsManageServiceImpl.checkParam, CountryCode is null");
        }

        if (input.getHasStock() ==null ) {
            log.warn("MkuEsManageServiceImpl.checkUpdateParam, stockTag error. input={}", JSONObject.toJSONString(input));
            throw new BizException("MkuEsManageServiceImpl.checkParam, stockTag is null");
        }
    }

    /**
     * 客户相关校验及赋值、true:上品，false:下品
     * */
    private Boolean setCustomerInfo(CustomerVO customerVO,Long mkuId,MkuEsPO mkuEsPO){
        String clientCode = customerVO.getClientCode();
        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
            log.error("MkuEsManageServiceImpl.setCustomerInfo, no match customerVO. clientCode={},mkuId={}", clientCode,mkuId);
            throw new BizException("MkuEsManageServiceImpl.setCustomerInfo, no match customerVO");
        }

        mkuEsPO.setClientCode(clientCode);
        mkuEsPO.setContractNo(customerVO.getContractCode());

        CustomerMkuPO customerMkuPO = customerMkuAtomicService.getOneValid(mkuId, clientCode);
        if (null == customerMkuPO) {
            log.error("MkuEsManageServiceImpl.setCustomerInfo, no match CustomerMkuPO. clientCode={},mkuId={}", clientCode,mkuId);
            return Boolean.FALSE;
        }
        // 客户在售
        if (customerMkuPO.getBindStatus() == null || !CustomerMkuBindEnum.BIND.equals(customerMkuPO.getBindStatus())) {
            log.warn("MkuEsManageServiceImpl.setCustomerInfo, baseInfo, customerMkuPO is null. mkuId={}，customerMkuPO={}", mkuId,JSONObject.toJSONString(customerMkuPO));
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    private boolean isJinMen(String clientCode) {
        Set<String> jinMenCustomers = operDuccConfig.getJinMenCustomers();
        return jinMenCustomers.contains(clientCode);
    }

    /**
     * 设置mku主体信息
     * */
    private MkuPO setMkuInfo(Long mkuId,MkuEsPO mkuEsPO,String lang,Set<String> preWords){
        MkuPO mkuPO = mkuAtomicService.getById(mkuId);

        // 名称多语言
        Map<String, MkuLangPO> mkuLangMap = mkuLangAtomicService.listLangByMkuId(mkuId);
        if(MapUtils.isEmpty(mkuLangMap)){
            log.error("MkuEsManageServiceImpl.setMkuInfo, baseInfo, mkuLangMap is null. mkuId={}",mkuId);
            throw new BizException("MkuEsManageServiceImpl.setMkuInfo, baseInfo, mkuLangMap is null.");
        }

        Set<String> preTitleWords = new HashSet<>();

        mkuEsPO.setMkuId(mkuId);

        MkuLangPO zh = mkuLangMap.get(LangConstant.LANG_ZH);
        if(zh != null){
            mkuEsPO.setMkuTitleZh(zh.getMkuTitle());
            preTitleWords.add(zh.getMkuTitle());
        }
        MkuLangPO en = mkuLangMap.get(LangConstant.LANG_EN);
        if(en != null){
            mkuEsPO.setMkuTitleEn(en.getMkuTitle());
            preTitleWords.add(en.getMkuTitle());
        }
        MkuLangPO current = mkuLangMap.get(lang);
        if(current != null){
            mkuEsPO.setMkuTitle(current.getMkuTitle());
            preTitleWords.add(current.getMkuTitle());
        }
        mkuEsPO.setMkuCreateTime(mkuPO.getCreateTime().getTime());
        mkuEsPO.setBrandId(mkuPO.getBrandId());
        mkuEsPO.setCatId(mkuPO.getCatId().toString());
        mkuEsPO.setJdCatId(mkuPO.getJdCatId().toString());
        mkuEsPO.setYn(mkuPO.getYn());

//        String paramContent = "请将["+ mkuEsPO.getMkuTitle() + "]按照["+ lang +"]的习惯按照空格重新分词，并以空格形式返回";
        String result = getChatGptResult(mkuEsPO.getClientCode(),mkuEsPO.getMkuId(),mkuEsPO.getMkuTitle(),lang);
        if(StringUtils.isNotBlank(result)){
            preTitleWords.add(result);
        }

        preWords.addAll(preTitleWords);

        String preWord = String.join(" ", preTitleWords);
        mkuEsPO.setPreWordTitle(preWord);

        return mkuPO;
    }

    /**
     * 上品-设置品牌
     * */
    private void setBrandInfo(Long brandId,String lang,MkuEsPO mkuEsPO,Set<String> preWords){
        // 品牌
        BrandPO brandPo = brandAtomicService.getById(brandId);
        if(brandPo == null){
            log.error("MkuEsManageServiceImpl.setBrandInfo, baseInfo, brandPo is null. branId={}", brandId);
            return;
        }
        // 品牌多语
        Map<String, String> brandMap = brandOutService.queryBrandNameByIdAndLangs(brandId, this.getLangs(lang));
        if(MapUtils.isEmpty(brandMap)){
            log.error("MkuEsManageServiceImpl.setBrandInfo, baseInfo, brandMap is null. brandId={},lang={}", brandId,lang);
            throw new BizException("MkuEsManageServiceImpl.setBrandInfo, baseInfo, brandMap is null.");
        }

        mkuEsPO.setBrandId(brandPo.getId());
        mkuEsPO.setBrandName(brandMap.get(lang));

        Set<String> preBrandWords = new HashSet<>();
        if(StringUtils.isNotBlank(brandMap.get(lang))){
            preBrandWords.add(brandMap.get(lang));
        }
        if(StringUtils.isNotBlank(brandMap.get(LangConstant.LANG_ZH))){
            preBrandWords.add(brandMap.get(LangConstant.LANG_ZH));
        }
        if(StringUtils.isNotBlank(brandMap.get(LangConstant.LANG_EN))){
            preBrandWords.add(brandMap.get(LangConstant.LANG_EN));
        }

        preWords.addAll(preBrandWords);

        String preWord = String.join(" ", preBrandWords);
        mkuEsPO.setPreWordBrand(preWord);
    }

    /**
     * 上品-设置类目
     * */
    private void setCategoryInfo(Long jdCatId,String lang,MkuEsPO mkuEsPO,Set<String> preWords){
        // 类目
        List<CategoryComboBoxVO> categoryPath = categoryOutService.queryPath(jdCatId, lang);
        if(CollectionUtils.isEmpty(categoryPath)){
            categoryPath = categoryOutService.queryPath(jdCatId, LangConstant.PRODUCT_DEFAULT_LANG);
        }
        String catId = categoryPath.stream()
                .map(item-> item.getId().toString())
                .collect(Collectors.joining(" "));
        String catName = categoryPath.stream()
                .map(CategoryComboBoxVO::getName)
                .collect(Collectors.joining("#"));
        if (StringUtils.isNotBlank(mkuEsPO.getJdCatId()) && mkuEsPO.getJdCatId().equals(catId) && StringUtils.isNotBlank(mkuEsPO.getJdCatName()) && mkuEsPO.getJdCatName().equals(catName)) {
            return;
        }

        mkuEsPO.setJdCatId(catId);
        mkuEsPO.setJdCatName(catName);
        mkuEsPO.setCatId(jdCatId != null ? jdCatId + "" : "");

        // 类目
        List<CategoryComboBoxVO> categoryComboBoxVOS = categoryOutService.queryPath(new HashSet<>(Collections.singletonList(jdCatId))
                , this.getLangs(lang));
        if(CollectionUtils.isEmpty(categoryComboBoxVOS)){
            log.error("MkuEsManageServiceImpl.setCategoryInfo categoryComboBoxVOS is null");
            return;
        }
        Set<String> preWordCategory = categoryComboBoxVOS.stream().map(CategoryComboBoxVO::getName).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(preWordCategory)){
            log.error("MkuEsManageServiceImpl.setCategoryInfo preWordCategory is null");
            return;
        }
        String preWord = String.join(" ", preWordCategory);
        mkuEsPO.setPreWordCategory(preWord);

        preWords.addAll(preWordCategory);
    }

    /**
     * 上品-设置关键字
     * */
    private void setKeysInfo(Long mkuId,String lang,MkuEsPO mkuEsPO,Set<String> preWords){
        List<MkuRelationPO> mkuRelations = mkuRelationAtomicService.queryBindListByMkuId(mkuId);
        if(CollectionUtils.isEmpty(mkuRelations)){
            log.error("MkuEsManageServiceImpl.setKeysInfo mkuRelations is null");
            return;
        }

        Set<Long> skuIds = mkuRelations.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toSet());
        List<SkuPO> skuPOS = skuAtomicService.queryBySkuIds(skuIds);
        if(CollectionUtils.isEmpty(skuPOS)){
            log.error("MkuEsManageServiceImpl.setKeysInfo skuPOS is null");
            return;
        }

        Set<Long> spuIds = skuPOS.stream().map(SkuPO::getSpuId).collect(Collectors.toSet());
        List<SpuLangPO> spuLangPOS = spuLangAtomicService.listLang(spuIds,this.getLangs(lang));
        if(CollectionUtils.isEmpty(spuLangPOS)){
            log.error("MkuEsManageServiceImpl.setKeysInfo spuLangPOS is null");
            return;
        }
        Set<String> preWordsKeyPhrase = spuLangPOS.stream().map(SpuLangPO::getKeyPhrases).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(preWordsKeyPhrase)){
            log.error("MkuEsManageServiceImpl.setKeysInfo preWordsKeyPhrase is null");
            return;
        }

        // 上品-关键字集合-其它
        String preWord = String.join(" ", preWordsKeyPhrase);
        mkuEsPO.setPreWordOther(preWord);

        preWords.addAll(preWordsKeyPhrase);
    }

    /**
     * 上品-获取聚合KEY
     * 1、宁愿不聚合，也不能聚错
     * 2、不允许返回为空，默认UUID，保证不重复
     * */
    private String getAggKey(String clientCode,Long mkuId){

        String result = UUID.randomUUID().toString();

        CustomerMkuPricePO customerMkuPricePO = customerMkuPriceAtomicService.getOne(mkuId,clientCode);
        if(customerMkuPricePO == null){
            log.error("MkuEsManageServiceImpl.getAggKey customerMkuPricePO is null.clientCode:{},mkuId:{}",clientCode,mkuId);
            return result;
        }
        Long fixedSkuId = customerMkuPricePO.getFixedSkuId();
        if(fixedSkuId == null){
            log.error("MkuEsManageServiceImpl.getAggKey skuId is null.clientCode:{},mkuId:{}",clientCode,mkuId);
            return result;
        }
        SkuPO skuPO = skuAtomicService.getSkuPoBySkuId(fixedSkuId);
        if(skuPO == null){
            log.error("MkuEsManageServiceImpl.getAggKey skuPO is null.clientCode:{},mkuId:{},fixedSkuId:{}",clientCode,mkuId,fixedSkuId);
            return result;
        }

        String sourceCountryCode = skuPO.getSourceCountryCode();
        if(StringUtils.isBlank(sourceCountryCode)){
            log.error("MkuEsManageServiceImpl.getAggKey sourceCountryCode is null.clientCode:{},mkuId:{},fixedSkuId:{}",clientCode,mkuId,fixedSkuId);
            return result;
        }

        // 跨境品聚合逻辑
        if(CountryConstant.COUNTRY_ZH.equals(sourceCountryCode)){
            if(skuPO.getJdMainSkuId() != null){
                return skuPO.getJdMainSkuId().toString();
            } else if(skuPO.getJdSpuId() != null){
                return skuPO.getJdSpuId().toString();
            } else {
                return result;
            }
        } else {
            // 本土品聚合逻辑
            // 1.若不是单一sku则直接用SpuId
            long skuCount = skuAtomicService.getSkuCountBySpuId(skuPO.getSpuId());
            if(skuCount > 1L) {
                return skuPO.getSpuId().toString();
            }
            SpuPO spuPO = spuAtomicService.getSpuPoBySpuId(skuPO.getSpuId());
            if(spuPO == null){
                return result;
            }
            // 2.若是单一sku则取相同的供应商+类目+品牌+型号
            String vendorCode = spuPO.getVendorCode();
            // 要求增加的一个维度
            Long jdCatId = spuPO.getJdCatId();
            Long brandId = spuPO.getBrandId();
            String specification = spuPO.getSpecification();

            if(StringUtils.isBlank(vendorCode) || jdCatId == null || brandId == null || StringUtils.isBlank(specification)){
                log.error("MkuEsManageServiceImpl.getAggKey vendorCode/jdCatId/brandId/specification is null.clientCode:{},mkuId:{},fixedSkuId:{},spuId:{}",clientCode,mkuId,fixedSkuId,spuPO.getSpuId());
                return result;
            }

            return StringUtils.joinWith("_",vendorCode,jdCatId,brandId,specification);
        }
    }

    /**
     * 校验搜索时参数
     * */
    private Boolean checkPageParam(MkuClientPageReqVO input){
        if(StringUtils.isBlank(input.getLang())){
            log.error("MkuEsAtomicService.checkPageParam lang is null");
            return Boolean.FALSE;
        }
        if(StringUtils.isBlank(input.getClientCode())){
            log.error("MkuEsAtomicService.checkPageParam clientCode is null");
            return Boolean.FALSE;
        }
//        if(StringUtils.isBlank(input.getKeyword()) && CollectionUtils.isEmpty(input.getCatIds())){
//            log.error("MkuEsAtomicService.checkPageParam keyword or catIds is null");
//            return Boolean.FALSE;
//        }
        return Boolean.TRUE;
    }

    /**
     * 查询时分词
     * */
    private List<String> gptGenerateWords(String keyword,String lang){
        if(StringUtils.isBlank(keyword)){
            return new ArrayList<>();
        }
        List<String> preWords = new ArrayList<>();
        preWords.add(keyword);
        preWords.addAll(this.processKeyword(keyword));

        if(this.checkEnglish(keyword)){
            log.info("MkuClientManageServiceImpl.gptGenerateWords checkEnglish is true");
            return preWords;
        }

//        String paramContent = "Please rephrase ["+ keyword +"] according to the agreement of ["+ lang +"]+\n" +
//                "If the input is not an industrial product, add 2 intent recognition words related to industrial products\n" +
//                "If the input is industrial products, add 3 synonyms related to industrial products\n" +
//                "Do not use words that do not match the original meaning";
        String paramContent = "Please rephrase the string ["+keyword+"] following the conventions of ["+lang+"] and return it as a string separated by spaces";
        ChatGptProperties chatGptProperties = operDuccConfig.preseChatGpt() ;
        String result = chatGptService.postGpt(paramContent,chatGptProperties.getChatGptModel().get(ChatGptProperties.SEARCH)) ;
        if(StringUtils.isNotBlank(result)){
            preWords.add(result);
        }
        return preWords;
    }

    /**
     * 处理关键字字符串，将其拆分成多个单词，并转换为小写
     *
     * @param keyword 要处理的关键字字符串
     * @return 处理后的关键字列表
     */
    public List<String> processKeyword(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return null;
        }

        return Arrays.stream(keyword.trim().split("\\s+"))
                .map(String::toLowerCase)
                .collect(Collectors.toList());
    }

    /**
     * 获取语言集合
     * */
    private List<String> getLangs(String lang){
        List<String> langList = new ArrayList<>();
        langList.add(lang);
        langList.add(LangConstant.LANG_ZH);
        langList.add(LangConstant.LANG_EN);
        return langList;
    }

    /**
     * 判断是否是英文且无空格
     * */
    private Boolean checkEnglish(String input){
        if(StringUtils.isBlank(input) || input.contains(" ")){
            return Boolean.FALSE;
        }
        return mkuEsAtomicService.containsEnglish(input);
    }

    /**
     * 构建返回结果
     * */
    private List<SpuVO> buildSpuVO(SearchResponse<MkuEsPO> response
            ,String clientCode){
        if (response == null || response.hits() == null) {
            log.warn("MkuManageServiceImpl.buildSpuVO response is null");
            return null;
        }

        List<Hit<MkuEsPO>> hits = response.hits().hits();
        List<MkuEsPO> esPOS = new ArrayList<>();

        hits.forEach(hit->{
            MkuEsPO esPO = hit.source();
            if(esPO != null){
                esPOS.add(esPO);
            }
        });

        List<SpuVO> results = new ArrayList<>();
        if(CollectionUtils.isEmpty(esPOS)){
            log.info("MkuManageServiceImpl.buildSpuVO esPOS is null");
            return results;
        }

        CustomerVO customerVO = customerManageService.detail(clientCode);
        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
            log.warn("MkuManageServiceImpl.buildSpuVO, customerVO error. clientCode={}, customerVO={}", clientCode, JSONObject.toJSONString(customerVO));
            return results;
        }

        Set<Long> mkuIds = esPOS.stream().filter(o -> {
                    return null != o && Objects.nonNull(o.getMkuId());
                })
                .map(MkuEsPO::getMkuId).collect(Collectors.toSet());

        List<CustomerMkuPricePO> mkuPOList = customerMkuPriceAtomicService.getListByMkuIds(Lists.newArrayList(mkuIds),clientCode);
        if(CollectionUtils.isEmpty(mkuPOList)){
            log.warn("MkuManageServiceImpl.buildSpuVO, mkuPOList. input={}", JSONObject.toJSONString(mkuIds));
            return results;
        }
        List<Long> skuIds = mkuPOList.stream().map(CustomerMkuPricePO::getFixedSkuId).collect(Collectors.toList());
        Map<Long,Long> skuSpuMap = skuAtomicService.getSkuSpuMapBySkuIds(skuIds);
        if(MapUtils.isEmpty(skuSpuMap)){
            log.warn("MkuManageServiceImpl.buildSpuVO, skuSpuMap. input={}", JSONObject.toJSONString(skuIds));
            return results;
        }
        List<Long> spuIds = new ArrayList<>(skuSpuMap.values());
        List<SpuPO> spuPoList = spuAtomicService.querySpuList(spuIds);
        if(CollectionUtils.isEmpty(spuPoList)){
            log.warn("MkuManageServiceImpl.buildSpuVO, spuPoList. input={}", JSONObject.toJSONString(spuIds));
            return results;
        }

        List<SpuVO> spuVOS = spuReadManageService.getSpuList(spuIds);
        if(CollectionUtils.isEmpty(spuVOS)){
            log.warn("MkuManageServiceImpl.buildSpuVO, spuVOS is null. input={}", JSONObject.toJSONString(spuVOS));
            return results;
        }

        Map<Long,Long> mkuSkuMap = mkuPOList.stream().collect(Collectors.toMap(CustomerMkuPricePO::getMkuId,CustomerMkuPricePO::getFixedSkuId,(v1,v2)->v2));

        List<SpuVO> middleResult = new ArrayList<>();
        // 拆开mkuId,并过滤
        for(SpuVO spuVO : spuVOS){
            if(spuVO.getMkuIds().size() > 1){
                List<Long> partMkuIds = new ArrayList<>(spuVO.getMkuIds());
                partMkuIds.forEach(item->{
                    SpuVO spuVO1 = JSONObject.parseObject(JSONObject.toJSONString(spuVO),SpuVO.class);
                    List<Long> partMkuIds1 = new ArrayList<>();
                    partMkuIds1.add(item);
                    spuVO1.setMkuIds(partMkuIds1);
                    Long skuId = mkuSkuMap.get(item);
                    if(skuId != null){
                        List<Long> partSkuIds = new ArrayList<>();
                        partSkuIds.add(skuId);
                        spuVO1.setSkuIds(partSkuIds);
                        middleResult.add(spuVO1);
                    }
                });
            } else {
                middleResult.add(spuVO);
            }
        }


        // 为了保证ES中取出后的排序
        for(MkuEsPO esPO : esPOS){
            for(SpuVO spuVO: middleResult){
                if(spuVO.getMkuIds().contains(esPO.getMkuId())){
                    results.add(spuVO);
                }
            }
        }

        return results;
    }

    @Override
    public boolean updateMkuCustomerFeatureTag(MkuFeatureTagVO mkuFeatureTagVo) {
        try {
            if (Objects.isNull(mkuFeatureTagVo.getMkuId())) {
                MkuRelationPO mkuRelationPO = mkuRelationAtomicService.getMkuBySkuId(mkuFeatureTagVo.getSkuId());
                if (Objects.nonNull(mkuRelationPO) && Objects.nonNull(mkuRelationPO.getMkuId())) {
                    mkuFeatureTagVo.setMkuId(mkuRelationPO.getMkuId());
                } else {
                    log.error("sku无对应的mkuId 忽略，param:{}", JSONObject.toJSONString(mkuFeatureTagVo));
                    return true;
                }
            }

            MkuFeatureTagEnum mkuFeatureTagEnum = MkuFeatureTagEnum.queryByRelatCode(mkuFeatureTagVo.getTagType());
            if (Objects.isNull(mkuFeatureTagEnum)) {
                log.error("tagType 对应的标信息不存在，param:{}", JSONObject.toJSONString(mkuFeatureTagVo));
                return true;
            }

            String indexName = EsConstants.getMkuEsName(mkuFeatureTagVo.getCountryCode());
            List<MkuEsPO> mkuEsPOS = mkuEsAtomicService.queryByMkuId(indexName, mkuFeatureTagVo.getMkuId());
            if (CollectionUtils.isEmpty(mkuEsPOS)) {
                return true;
            }
            //1.查询客户池中所有的mku列表
            mkuEsPOS.forEach(u -> {
                Set<Integer> featureTags = u.getFeatureTags();
                if (CollectionUtils.isEmpty(featureTags)) {
                    featureTags = new HashSet<>();
                }
                if (FeatureTagOperatorEnum.ADD.getCode().equals(mkuFeatureTagVo.getOperator())) {
                    featureTags.add(MkuFeatureTagEnum.SHIPPING_48_HOUR.getEsCode());
                }else if (FeatureTagOperatorEnum.REMOVE.getCode().equals(mkuFeatureTagVo.getOperator())) {
                    featureTags.remove(MkuFeatureTagEnum.SHIPPING_48_HOUR.getEsCode());
                }

                u.setId(String.join("_",u.getClientCode(),String.valueOf(u.getMkuId())));
                u.setFeatureTags(featureTags);
                //2.根据结果列表更新标
                Boolean res = mkuEsAtomicService.updateFeatureTags(indexName, u);
            });

            return true;
        } catch (Exception e) {
            log.error("updateMkuCustomerFeatureTag param:{},e:", JSON.toJSONString(mkuFeatureTagVo), e);
            //告警
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_MKU_TAG_WARNING, String.format("【%s】 %s 客户池mku打标保存es, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , e.getMessage()));
        }
        return false;
    }

    @Override
    public MkuEsClientVO getEsMkuByCondition(MkuClientDetailReqVO input) {
        //参数校验
        this.checkSearchParam(input);

        //索引名称
        String indexName = EsConstants.getMkuEsName(input.getCountry());
        if (isJinMen(input.getClientCode())) {
            indexName = EsConstants.getMkuEsName("hk");
        }

        //设置id
        String documentId = String.join("_", input.getClientCode(), input.getMkuId().toString());
        MkuEsPO mkuEsPO = mkuEsAtomicService.queryById(indexName, documentId);
        if(mkuEsPO!=null) {
            MkuEsClientVO mkuEsClientVO = MkuConvert.INSTANCE.mkuEsPo2EsClientVO(mkuEsPO);
            return mkuEsClientVO;
        }

        return null;
    }

    @Override
    public boolean updateMkuEsStockTag(MkuEsStockTagReqDTO input) {
        try {
            //必填参数校验
            this.checkUpdateParam(input);

            //获取索引
            String indexName = EsConstants.getMkuEsName(input.getCountry());
            if (isJinMen(input.getClientCode())) {
                indexName = EsConstants.getMkuEsName("hk");
            }

            //设置条件
            String documentId = String.join("_", input.getClientCode(), input.getMkuId().toString());
            MkuEsPO mkuEsPO = new MkuEsPO();
            mkuEsPO.setId(documentId);
            mkuEsPO.setHasStock(input.getHasStock());

            return mkuEsAtomicService.updateHasStockFlag(indexName, mkuEsPO);
        } catch (Exception e) {
            log.error("failed to updateMkuEsStockTag, input:{}", JSON.toJSONString(input), e);
        }
        return false;
    }

    @Override
    public boolean updateMkuPromiseFeatureTag(MkuPromiseFeatureTagVO input) {
        try {
            if (StringUtils.isBlank(input.getClientCode()) || StringUtils.isBlank(input.getCountryCode()) || input.getMkuId() == null) {
                log.warn("MkuManageServiceImpl.checkParam, clientCode error. input={}", JSONObject.toJSONString(input));
                throw new BizException("MkuManageServiceImpl.checkParam, clientCode is null");
            }

            String indexName = EsConstants.getMkuEsName(input.getCountryCode());
            if (isJinMen(input.getClientCode())) {
                indexName = EsConstants.getMkuEsName("hk");
            }

            String documentId = String.join("_", input.getClientCode(), input.getMkuId().toString());
            Integer promiseValue = input.getPromiseValue() != null ? input.getPromiseValue() : PromiseTimeTagEnum.DEFAULT_EMPTY_TAG.getCode();

            //计算时效标签
            Set<Integer> featureTags = input.getFeatureTags();
            Set<Integer> newPromiseFeatureTags =  calculateNewPromiseFeatureTags(featureTags, promiseValue);

            MkuEsPO mkuEsPO = new MkuEsPO();
            mkuEsPO.setId(documentId);
            mkuEsPO.setFeatureTags(newPromiseFeatureTags);
            //2.根据结果列表更新标
            return mkuEsAtomicService.updateFeatureTags(indexName, mkuEsPO);

        } catch (Exception e) {
            log.error("updateMkuPromiseFeatureTag param:{},e:", JSON.toJSONString(input), e);
            //告警
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_MKU_TAG_WARNING, String.format("【%s】 %s 客户池mku打时效标标保存es, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , e.getMessage()));
        }
        return false;
    }

    /**
     * 根据承诺值计算并更新特征标签集合中的时效标签
     * @param featureTags 原始特征标签集合，若为null则会被初始化为空集合
     * @param promiseValue 承诺时效值，当值为DEFAULT_EMPTY_TAG时会移除时效标签，否则添加对应时效标签
     * @return 更新后的特征标签集合（包含处理后的时效标签）
     */
    private  Set<Integer> calculateNewPromiseFeatureTags(Set<Integer> featureTags, Integer promiseValue) {
        if (Objects.isNull(featureTags)) {
            featureTags = new HashSet<>();
        }

        //计算时效标签
        if (Objects.equals(promiseValue, PromiseTimeTagEnum.DEFAULT_EMPTY_TAG.getCode())) {
            for (Integer skuFeatureTag : featureTags) {
                if (Objects.equals(skuFeatureTag, PromiseTimeTagEnum.DEFAULT_EMPTY_TAG.getCode())) {
                    //清理历史数据
                    featureTags.remove(skuFeatureTag);
                    continue;
                }

                MkuFeatureTagEnum mkuFeatureTagEnum = MkuFeatureTagEnum.queryEsCode(skuFeatureTag);
                if (mkuFeatureTagEnum.getSkuSpecialAttrEnum() == SkuSpecialAttrEnum.FULFIL_PROMISE) {
                    //移除时效标签
                    log.info("calculateNewPromiseFeatureTags remove skuFeatureTag:{}, promiseValue:{}", skuFeatureTag, promiseValue);
                    featureTags.remove(skuFeatureTag);
                }
            }
        } else {
            //添加时效标签
            MkuFeatureTagEnum mkuFeatureTagEnum = MkuFeatureTagEnum.queryByFulfilPromise(SkuSpecialAttrEnum.FULFIL_PROMISE, promiseValue);
            if (mkuFeatureTagEnum != null) {
                log.info("calculateNewPromiseFeatureTags add skuFeatureTag:{}, promiseValue:{}", mkuFeatureTagEnum.getEsCode(), promiseValue);
                featureTags.add(mkuFeatureTagEnum.getEsCode());
            }
        }

        return featureTags;
    }

    @Override
    public boolean updateMkuEsForCategory(CustomerMkuPO input) {
        String clientCode = input.getClientCode();
        Long mkuId = input.getMkuId();

        MkuPO mkuPO = mkuAtomicService.getById(mkuId);
        if (Objects.isNull(mkuPO)) {
            return false;
        }
        // 客户相关校验及赋值、true:上品，false:下品
        CustomerVO customerVO = customerManageService.detail(input.getClientCode());

        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
            log.error("MkuEsManageServiceImpl.updateMkuEsForCategory, no match customerVO. clientCode={},mkuId={}", clientCode,mkuId);
            throw new BizException("MkuEsManageServiceImpl.setCustomerInfo, no match customerVO");
        }

        Boolean flag = true;
        // 客户在售
        if (input.getBindStatus() == null || CustomerMkuBindEnum.INVALID.equals(input.getBindStatus())) {
            log.warn("MkuEsManageServiceImpl.updateMkuEsForCategory, baseInfo, customerMkuPO is null. mkuId={}，customerMkuPO={}", mkuId,JSONObject.toJSONString(input));
            flag =  Boolean.FALSE;
        }

        String lang = countryManageService.getLangByCountryCode(customerVO.getCountry());
        if (isJinMen(input.getClientCode())) {
            lang = LangConstant.LANG_ZH_HANT;
        }
        if(StringUtils.isBlank(lang)){
            log.info("MkuEsManageServiceImpl.updateMkuEsForCategory lang is null");
            return Boolean.FALSE;
        }

        String indexName = EsConstants.getMkuEsName(customerVO.getCountry());
        if (isJinMen(input.getClientCode())) {
            indexName = EsConstants.getMkuEsName(CountryConstant.COUNTRY_HK);
        }

        // 设置id
        String esId = String.join("_",input.getClientCode(),mkuId.toString());

        // 下品
        if(!flag || mkuPO.getYn().equals(YnEnum.NO.getCode())){
            log.info("MkuEsManageServiceImpl.updateMkuEsForCategory delete document");
            mkuEsAtomicService.delete(indexName,esId);
            return Boolean.TRUE;
        }

        MkuEsPO mkuEsPO = mkuEsAtomicService.queryById(indexName, esId);

        if (Objects.isNull(mkuEsPO)) {
            log.info("MkuEsManageServiceImpl.updateMkuEsForCategory 文档不存在 mkuId={} clientCode={}",mkuId,clientCode);
            return Boolean.FALSE;
        }

        // todo 新版搜索上线后可逻辑删除
        Set<String> preWords = Sets.newHashSet();
        this.setCategoryInfo(mkuPO.getJdCatId(),lang,mkuEsPO,preWords);

        String esPreWord = mkuEsPO.getPreWord();
        if (CollectionUtils.isEmpty(preWords)) {
            return Boolean.TRUE;
        }

        // 上品-设置预命中分词集合
        String preWord = String.join(" ", preWords);
        mkuEsPO.setPreWord(esPreWord + " " + preWord);

        Boolean upsert = mkuEsAtomicService.upsert(indexName, mkuEsPO);
        return upsert;
    }

    @Override
    public Boolean updateMkuPoolFlag(MkuPoolFlagDTO input) {
        log.info("MkuEsManageServiceImpl.updateMkuPoolFlag, param={}", JSONObject.toJSONString(input));
        Long mkuId = input.getMkuId();
        String targetCountryCode = input.getTargetCountryCode();
        if(mkuId == null){
            log.error("MkuEsManageServiceImpl.updateMkuPoolFlag, mkuId is null.");
            return Boolean.FALSE;
        }
        if(StringUtils.isBlank(targetCountryCode)){
            log.error("MkuEsManageServiceImpl.updateMkuPoolFlag, targetCountryCode is null.");
            return Boolean.FALSE;
        }
        MkuPO mkuPO = mkuAtomicService.getById(mkuId);
        if (Objects.isNull(mkuPO)) {
            log.error("MkuEsManageServiceImpl.updateMkuPoolFlag, mkuPO is null.");
            return Boolean.FALSE;
        }

        List<CustomerMkuPO> customerMkuPOList = customerMkuAtomicService.listNoClientCode(mkuId);
        if(CollectionUtils.isEmpty(customerMkuPOList)){
            log.error("MkuEsManageServiceImpl.updateMkuPoolFlag, customerMkuPOList is null.");
            return Boolean.FALSE;
        }

        customerMkuPOList = customerMkuPOList.stream().filter(item->
                targetCountryCode.equals(item.getTargetCountryCode())
                        && CustomerMkuBindEnum.BIND == item.getBindStatus()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(customerMkuPOList)){
            log.error("MkuEsManageServiceImpl.updateMkuPoolFlag, customerMkuPOList filter is null.");
            return Boolean.FALSE;
        }

        for(CustomerMkuPO customerMkuPO : customerMkuPOList){
            String clientCode = customerMkuPO.getClientCode();
            CustomerVO customerVO = customerManageService.detail(clientCode);
            if (null == customerVO) {
                log.error("MkuEsManageServiceImpl.updateMkuPoolFlag, customerVO is null. clientCode={},mkuId={}", clientCode,mkuId);
                continue;
            }
            String indexName = EsConstants.getMkuEsName(targetCountryCode);
            if (isJinMen(clientCode)) {
                indexName = EsConstants.getMkuEsName(CountryConstant.COUNTRY_HK);
            }
            // 设置id
            String esId = String.join("_",clientCode,mkuId.toString());
            MkuEsPO mkuEsPO = mkuEsAtomicService.queryById(indexName, esId);
            if(mkuEsPO == null){
                log.error("MkuEsManageServiceImpl.updateMkuPoolFlag, mkuEsPO is null. clientCode={},mkuId={}", clientCode,mkuId);
                continue;
            }
            if(!YnEnum.YES.getCode().equals(mkuPO.getYn())){
                log.error("MkuEsManageServiceImpl.updateMkuPoolFlag, mkuEsPO yn not equal 1. clientCode={},mkuId={}", clientCode,mkuId);
                continue;
            }
            Integer poolFlag = this.getPoolFlag(mkuId,clientCode,targetCountryCode);
            mkuEsPO.setYn(poolFlag);
            mkuEsPO.setUpdateTime(new Date().getTime());
            if(poolFlag.equals(YesOrNoEnum.NO.getCode())){
                mkuEsAtomicService.upsert(indexName, mkuEsPO);
            } else if (poolFlag.equals(YesOrNoEnum.YES.getCode())) {
                MkuClientDetailReqVO inputs = new MkuClientDetailReqVO();
                inputs.setClientCode(clientCode);
                inputs.setMkuId(customerMkuPO.getMkuId());
                this.upsert2Es(inputs);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateAggKey(MkuClientDetailReqApiDTO input) {
        log.info("MkuEsManageServiceImpl.updateAggKey, param={}", JSONObject.toJSONString(input));
        Long mkuId = input.getMkuId();
        String clientCode = input.getClientCode();

        if(mkuId == null){
            log.error("MkuEsManageServiceImpl.updateAggKey, mkuId is null.");
            return Boolean.FALSE;
        }
        if(StringUtils.isBlank(clientCode)){
            log.error("MkuEsManageServiceImpl.updateAggKey, clientCode is null.");
            return Boolean.FALSE;
        }
        MkuPO mkuPO = mkuAtomicService.getById(mkuId);
        if (Objects.isNull(mkuPO)) {
            log.error("MkuEsManageServiceImpl.updateAggKey, mkuPO is null.");
            return Boolean.FALSE;
        }

        CustomerVO customerVO = customerManageService.detail(input.getClientCode());
        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
            log.error("MkuEsManageServiceImpl.updateAggKey, no match customerVO. clientCode={},mkuId={}", clientCode,mkuId);
            return Boolean.FALSE;
        }

        CustomerMkuPO customerMkuPO = customerMkuAtomicService.getOneValid(mkuId,clientCode);
        if (customerMkuPO == null || customerMkuPO.getBindStatus() == null || CustomerMkuBindEnum.INVALID.equals(customerMkuPO.getBindStatus())) {
            log.warn("MkuEsManageServiceImpl.updateAggKey, customerMkuPO is null. mkuId={}，customerMkuPO={}", mkuId,JSONObject.toJSONString(input));
            return Boolean.FALSE;
        }

        String indexName = EsConstants.getMkuEsName(customerVO.getCountry());
        if (isJinMen(input.getClientCode())) {
            indexName = EsConstants.getMkuEsName(CountryConstant.COUNTRY_HK);
        }

        // 设置id
        String esId = String.join("_",input.getClientCode(),mkuId.toString());

        MkuEsPO mkuEsPO = mkuEsAtomicService.queryById(indexName, esId);
        if (Objects.isNull(mkuEsPO)) {
            log.info("MkuEsManageServiceImpl.updateAggKey 文档不存在 mkuId={} clientCode={}",mkuId,clientCode);
            return Boolean.FALSE;
        }

        String aggKey = this.getAggKey(clientCode,mkuId);
        mkuEsPO.setAggKey(aggKey);
        mkuEsPO.setUpdateTime(new Date().getTime());

        mkuEsAtomicService.upsert(indexName, mkuEsPO);
        return Boolean.TRUE;
    }

    /**
     * 使用ChatGPT服务重写指定的MKU标题，按照指定语言的约定进行格式化。
     * @param clientCode 客户端代码
     * @param mkuId MKU的唯一标识符
     * @param mkuTitle MKU的原始标题
     * @param lang 目标语言的代码
     * @return 重写后的MKU标题，若请求失败则返回空字符串
     */
    // 抽取的异步方法
    private String getChatGptResult(String clientCode,Long mkuId,String mkuTitle,String lang) {
        if(StringUtils.isBlank(mkuTitle)){
            return "";
        }

        String paramContent = "Please rephrase the string ["+mkuTitle+"] following the conventions of ["+lang+"] and return it as a string separated by spaces";
        ChatGptProperties chatGptProperties = operDuccConfig.preseChatGpt();

        try {
            CompletableFuture<String> future = CompletableFuture
                    .supplyAsync(() -> {
                        try {
                            return chatGptService.postGpt(paramContent,chatGptProperties.getChatGptModel().get(ChatGptProperties.LONGTITLE)) ;
                        } catch (Exception e) {
                            log.warn("获取getChatGptResult异常, clientCode:{},mkuId:{},mkuTitle:{},lang:{}", clientCode,mkuId,mkuTitle,lang, e);
                            return null;
                        }
                    }, chatGptExecutorService);

            String result = future.get(10, TimeUnit.SECONDS);

            if(StringUtils.isBlank(result)){
                return "";
            }

            return result;

        } catch (TimeoutException e) {
            log.warn("获取getChatGptResult异常10秒超时, clientCode:{},mkuId:{},mkuTitle:{},lang:{}", clientCode,mkuId,mkuTitle,lang, e);
            return "";
        } catch (Exception e) {
            log.warn("获取getChatGptResult异常, clientCode:{},mkuId:{},mkuTitle:{},lang:{}", clientCode,mkuId,mkuTitle,lang, e);
            return "";
        }
    }
}
