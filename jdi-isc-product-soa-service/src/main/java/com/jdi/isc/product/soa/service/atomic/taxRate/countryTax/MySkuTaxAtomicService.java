package com.jdi.isc.product.soa.service.atomic.taxRate.countryTax;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.MySkuTaxPO;
import com.jdi.isc.product.soa.price.api.price.req.MySkuTaxVO;
import com.jdi.isc.product.soa.repository.mapper.taxRate.countryTax.MySkuTaxBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 马来SKU税率原子服务
 * <AUTHOR>
 * @date 20250311
 */
@Service
@Slf4j
public class MySkuTaxAtomicService extends ServiceImpl<MySkuTaxBaseMapper, MySkuTaxPO> {

    /**
     * 单个查询
     */
    public MySkuTaxPO getOne(MySkuTaxVO vo) {
        LambdaQueryWrapper<MySkuTaxPO> query = Wrappers.<MySkuTaxPO>lambdaQuery()
                .eq(MySkuTaxPO::getJdSkuId, vo.getJdSkuId())
                .eq(MySkuTaxPO::getYn, YnEnum.YES.getCode());
        return super.getOne(query);
    }

    public Map<Long,MySkuTaxPO> listSkuTax(Set<Long> skuIds){
        LambdaQueryWrapper<MySkuTaxPO> queryWrapper = Wrappers.<MySkuTaxPO>lambdaQuery()
                .in(MySkuTaxPO::getJdSkuId, skuIds)
                .eq(MySkuTaxPO::getYn, YnEnum.YES.getCode());
        List<MySkuTaxPO> res = super.list(queryWrapper);
        if(CollectionUtils.isNotEmpty(res)){
            return res.stream().collect(Collectors.toMap(MySkuTaxPO::getJdSkuId, a -> a));
        }
        return new HashMap<>();
    }

    public boolean updateTax(MySkuTaxVO vo) {
        LambdaUpdateWrapper<MySkuTaxPO> wrapper = Wrappers.<MySkuTaxPO>lambdaUpdate()
            .set(StringUtils.isNotBlank(vo.getHsCode()), MySkuTaxPO::getHsCode, vo.getHsCode())
            .set(vo.getMfnTax() != null, MySkuTaxPO::getMfnTax, vo.getMfnTax())
            .set(vo.getOriginMfnTax() != null, MySkuTaxPO::getOriginMfnTax, vo.getOriginMfnTax())
            .set(vo.getSaleTax() != null, MySkuTaxPO::getSaleTax, vo.getSaleTax())
            .set(vo.getAntiDumpingTax() != null, MySkuTaxPO::getAntiDumpingTax, vo.getAntiDumpingTax())
            .set(vo.getIsOriginCertificate() != null, MySkuTaxPO::getIsOriginCertificate, vo.getIsOriginCertificate())
            .set(StringUtils.isNotBlank(vo.getRemark()), MySkuTaxPO::getRemark, vo.getRemark())
            .set(MySkuTaxPO::getUpdater, vo.getUpdater())
            .set(MySkuTaxPO::getUpdateTime, new Date())
            .eq(MySkuTaxPO::getId, vo.getId());
        return super.update(wrapper);
    }

}
