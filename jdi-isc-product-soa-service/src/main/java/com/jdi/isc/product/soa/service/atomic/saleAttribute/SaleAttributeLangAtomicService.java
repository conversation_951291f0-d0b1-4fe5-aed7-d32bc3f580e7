package com.jdi.isc.product.soa.service.atomic.saleAttribute;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeLangPO;
import com.jdi.isc.product.soa.repository.mapper.saleAttribute.SaleAttributeLangBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 销售属性名多语表原子服务
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
public class SaleAttributeLangAtomicService extends ServiceImpl<SaleAttributeLangBaseMapper, SaleAttributeLangPO> {

    /**
     * 根据ID获取有效的销售属性多语言
     * @param id 销售属性多语言ID
     * @return 销售属性多语言对象
     */
    public SaleAttributeLangPO getValidById(Long id) {
        if (id == null) {
            return null;
        }
        LambdaQueryWrapper<SaleAttributeLangPO> wrapper = Wrappers.<SaleAttributeLangPO>lambdaQuery()
                .eq(SaleAttributeLangPO::getId, id)
                .eq(SaleAttributeLangPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    /**
     * 根据销售属性编码获取有效的多语言列表
     * @param saleAttributeCode 销售属性编码
     * @return 多语言列表
     */
    public List<SaleAttributeLangPO> getValidBySaleAttributeCode(String saleAttributeCode) {
        if (saleAttributeCode == null) {
            return null;
        }
        LambdaQueryWrapper<SaleAttributeLangPO> wrapper = Wrappers.<SaleAttributeLangPO>lambdaQuery()
                .eq(SaleAttributeLangPO::getSaleAttributeCode, saleAttributeCode)
                .eq(SaleAttributeLangPO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }

    /**
     * 根据销售属性编码和语言获取有效的多语言
     * @param saleAttributeCode 销售属性编码
     * @param lang 语言编码
     * @return 多语言对象
     */
    public SaleAttributeLangPO getValidBySaleAttributeCodeAndLang(String saleAttributeCode, String lang) {
        if (saleAttributeCode == null || lang == null) {
            return null;
        }
        LambdaQueryWrapper<SaleAttributeLangPO> wrapper = Wrappers.<SaleAttributeLangPO>lambdaQuery()
                .eq(SaleAttributeLangPO::getSaleAttributeCode, saleAttributeCode)
                .eq(SaleAttributeLangPO::getLang, lang)
                .eq(SaleAttributeLangPO::getYn, YnEnum.YES.getCode())
                .last("LIMIT 1");
        return super.getOne(wrapper);
    }

    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public void saveSaleAttributeLangToDB(Map<String, Map<String, String>> translateResult) {
        if (MapUtils.isEmpty(translateResult)) {
            return;
        }

        List<SaleAttributeLangPO> langPOList = new ArrayList<>();

        for (Map.Entry<String, Map<String, String>> entry : translateResult.entrySet()) {
            String saleAttributeName = entry.getKey();
            Map<String, String> langTranslations = entry.getValue();

            for (Map.Entry<String, String> langEntry : langTranslations.entrySet()) {
                String lang = langEntry.getKey();
                String langName = langEntry.getValue();

                if (StringUtils.isNotBlank(langName)) {
                    // 检查是否已存在
                    SaleAttributeLangPO existingPO = this
                            .getValidBySaleAttributeCodeAndLang(saleAttributeName, lang);

                    if (existingPO == null) {
                        SaleAttributeLangPO langPO = new SaleAttributeLangPO();
                        langPO.setSaleAttributeCode(saleAttributeName);
                        langPO.setLang(lang);
                        langPO.setLangName(langName);
                        langPO.setYn(1);
                        long timestamp = new Date().getTime();
                        langPO.setCreateTime(timestamp);
                        langPO.setUpdateTime(timestamp);
                        // 如果登录上下文为空，则设置创建者和更新者为系统
                        if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                            langPO.setCreator(Constant.PIN_SYSTEM);
                            langPO.setUpdater(Constant.PIN_SYSTEM);
                        }
                        langPOList.add(langPO);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(langPOList)) {
            this.saveBatch(langPOList);
            log.info("保存销售属性多语言到数据库成功, 数据: {}", JSON.toJSONString(langPOList));
        }
    }

    /**
     * 根据语言获取有效的多语言列表
     * @param lang 语言编码
     * @return 多语言列表
     */
    public List<SaleAttributeLangPO> getValidByLang(String lang) {
        if (lang == null) {
            return null;
        }
        LambdaQueryWrapper<SaleAttributeLangPO> wrapper = Wrappers.<SaleAttributeLangPO>lambdaQuery()
                .eq(SaleAttributeLangPO::getLang, lang)
                .eq(SaleAttributeLangPO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }

    /**
     * 根据销售属性编码列表批量获取有效的多语言列表
     * @param saleAttributeCodeList 销售属性编码列表
     * @return 多语言列表
     */
    public List<SaleAttributeLangPO> getValidBySaleAttributeCodeList(List<String> saleAttributeCodeList) {
        if (CollectionUtils.isEmpty(saleAttributeCodeList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SaleAttributeLangPO> wrapper = Wrappers.<SaleAttributeLangPO>lambdaQuery()
                .in(SaleAttributeLangPO::getSaleAttributeCode, saleAttributeCodeList)
                .eq(SaleAttributeLangPO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }
} 