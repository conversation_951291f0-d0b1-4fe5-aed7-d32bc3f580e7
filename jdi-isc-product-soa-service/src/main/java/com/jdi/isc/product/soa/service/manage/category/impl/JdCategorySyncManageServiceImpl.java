package com.jdi.isc.product.soa.service.manage.category.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.auth.facade.request.role.RoleListRequest;
import com.jd.auth.facade.response.Response;
import com.jd.auth.facade.response.user.UserDto;
import com.jd.gms.greatdane.category.domain.Category;
import com.jd.gms.greatdane.category.domain.CategoryTree;
import com.jd.gms.greatdane.category.request.GetCategoryByIdsParam;
import com.jd.gms.greatdane.category.request.GetCategoryTreeParam;
import com.jd.gms.greatdane.category.request.GetRelationParam;
import com.jd.gms.greatdane.domain.Language;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.biz.component.api.enums.RobotTypeEnum;
import com.jdi.isc.biz.component.api.jme.req.SendMessageReqDTO;
import com.jdi.isc.product.soa.api.category.biz.CategoryOperationSourceTypeEnum;
import com.jdi.isc.product.soa.api.category.biz.GmsCategoryChangeDTO;
import com.jdi.isc.product.soa.api.category.biz.GmsCategoryReqDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.CategoryAttrTypeEnum;
import com.jdi.isc.product.soa.api.translate.req.MultiTranslateReqDTO;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.enums.CategoryMsgNoticeEnum;
import com.jdi.isc.product.soa.common.enums.RoleEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.domain.category.biz.CategoryPathVO;
import com.jdi.isc.product.soa.domain.category.biz.JdCategorySyncReqVO;
import com.jdi.isc.product.soa.domain.category.po.CategoryAttributePO;
import com.jdi.isc.product.soa.domain.category.po.CategoryLangPO;
import com.jdi.isc.product.soa.domain.category.po.CategoryPO;
import com.jdi.isc.product.soa.domain.enums.CategoryLevelEnum;
import com.jdi.isc.product.soa.domain.enums.StatusEnum;
import com.jdi.isc.product.soa.domain.enums.category.JdCategoryOpTypeEnum;
import com.jdi.isc.product.soa.domain.price.po.FulfillmentRatePO;
import com.jdi.isc.product.soa.domain.price.po.ProfitRatePO;
import com.jdi.isc.product.soa.domain.supplier.po.BusinessLineRelationPO;
import com.jdi.isc.product.soa.domain.taxRate.po.CategoryTaxPO;
import com.jdi.isc.product.soa.rpc.auth.AuthRpcService;
import com.jdi.isc.product.soa.rpc.gms.CategoryRpcService;
import com.jdi.isc.product.soa.rpc.jme.DongDongMessageRcpService;
import com.jdi.isc.product.soa.service.atomic.category.CategoryAtomicService;
import com.jdi.isc.product.soa.service.atomic.category.CategoryAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.category.CategoryBuyerRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.category.CategoryLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.FulfillmentRateAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.ProfitRateAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.BusinessLineRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.CategoryTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.category.JdCategorySyncManageService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.manage.translate.TextTranslateManageService;
import com.jdi.isc.product.soa.service.mapstruct.category.CategoryConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description：同步国内类目服务
 * @Date 2025-05-21
 */
@Slf4j
@Service
public class JdCategorySyncManageServiceImpl implements JdCategorySyncManageService {

    @Resource
    private CategoryRpcService categoryRpcService;

    @Resource
    private CategoryAtomicService categoryAtomicService;

    @Resource
    private CategoryLangAtomicService categoryLangAtomicService;

    @Resource
    private TextTranslateManageService textTranslateManageService;

    @Resource
    private LangManageService langManageService;

    @Resource
    private DongDongMessageRcpService dongDongMessageRcpService;

    @Resource
    private FulfillmentRateAtomicService fulfillmentRateAtomicService;

    @Resource
    private ProfitRateAtomicService profitRateAtomicService;

    @Resource
    private CategoryBuyerRelationAtomicService categoryBuyerRelationAtomicService;

    @Resource
    private ExecutorService categoryNoticeExecutorService;

    @Resource
    private BusinessLineRelationAtomicService businessLineRelationAtomicService;

    @Resource
    private CategoryTaxAtomicService categoryTaxAtomicService;

    @Resource
    private CategoryAttributeAtomicService categoryAttributeAtomicService;

    @LafValue("jdi.isc.product.soa.jd.category.sync.receivers")
    private Set<String> receivers;

    @LafValue("jdi.isc.product.soa.updateFather.category.switch")
    private Boolean updateFatherCategorySwitch;

    @Value("${spring.profiles.active}")
    private String systemProfile;

    @LafValue("jdi.isc.category.msg.notice.template")
    private String msgNoticeTemplate;

    @LafValue("jdi.isc.category.msg.notice.roles")
    private String msgNoticeRoles;


    @Resource
    private JimUtils jimUtils;

    @Resource
    private AuthRpcService authRpcService;

    private static final String SYNC_CATEGORY_UPDATER = "-updateGmsCategoryFrom";

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class)
    public boolean syncJdCategoryByFirstCatId(JdCategorySyncReqVO reqVO, Category category) {
        String key = CacheKeyConstant.getKey(CacheKeyConstant.PRODUCT_CATEGORY_FIRST_KEY);
        String jdFirstCatId = reqVO.getJdFirstCatId().toString();
        try {
            // 将当前一级类目ID缓存起来，防止重复同步
            jimUtils.sAdd(key, jdFirstCatId);
            syncJdCategoryFromGreatDane(reqVO, category);
        } catch (Exception e) {
            log.error("JdCategorySyncManageServiceImpl.syncJdCategoryFromGreatDane 同步国内类目异常 入参:[reqVO={},category={}]", JSON.toJSONString(reqVO), JSON.toJSONString(category), e);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SYNC_JD_CATEGORY_WARNING, String.format("【%s】%s 同步国内类目发生异常,一级类目ID: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , reqVO.getJdFirstCatId()
                    , e.getMessage()));
            throw e;
        }finally {
            log.info("JdCategorySyncManageServiceImpl.syncJdCategoryFromGreatDane 同步国内类目结束 入参:[reqVO={},category={}]", JSON.toJSONString(reqVO), JSON.toJSONString(category));
            jimUtils.sRem(key, jdFirstCatId);
        }
        return true;
    }

    /**
     * 从GreatDane同步京东一级类目到本地系统。
     * @param reqVO 同步请求对象，包含京东一级类目ID等信息。
     * @param category 京东一级类目对象。
     */
    private void syncJdCategoryFromGreatDane(JdCategorySyncReqVO reqVO, Category category) {
        // 查询国家简码列表
        List<String> langCodeList = getLangCodeList();
        // 一级类目不存在时，增加一级类目
        String operator = reqVO.getOperator();
        CategoryPO categoryPO = convertJdCategoryToPO(operator,0L, category, Maps.newHashMap());
        // 一级类目默认非末级
        categoryPO.setLeafNodeFlag(Boolean.FALSE);
        categoryAtomicService.save(categoryPO);
        // 一级类目多语言信息
        int firstCategoryId = reqVO.getJdFirstCatId().intValue();
        Map<Integer, Category> categoryMap = getCategoryMapByLang(firstCategoryId,Language.en);
        buildCategoryLang(categoryPO, category,categoryMap.get(firstCategoryId),langCodeList, operator,null);
        //  获取类目树
        buildCategoryTree(reqVO.getJdFirstCatId().intValue(),categoryPO.getId(), operator, langCodeList);
        // 发送消息
        String message = String.format("京东一级类目 %s[%s] 同步成功", category.getCategoryName(), category.getCategoryId());
        receivers.add(operator);
        log.info("syncJdCategoryFromGreatDane 同步一级类目，通知人列表,receivers={} 一级类目ID={}",JSON.toJSONString(receivers), reqVO.getJdFirstCatId());
        sendDongDongMessage(message,Lists.newArrayList(receivers));
    }

    private Map<Integer, Category> getCategoryMapByLang(int firstCategoryId,Language language) {
        GetCategoryByIdsParam param = new GetCategoryByIdsParam();
        param.setCategoryIds(Collections.singleton(firstCategoryId));
        param.setLanguage(language);
        return categoryRpcService.getCategoryByIds(param);
    }

    /**
     * 发送东东消息给指定接收者
     * @param message 消息内容
     * @param receivers 接收者列表
     */
    private void sendDongDongMessage(String message, List<String> receivers) {
        SendMessageReqDTO messageReqDTO = new SendMessageReqDTO();
        messageReqDTO.setMessage(message);
        messageReqDTO.setReceiveErps(receivers);
        messageReqDTO.setRobotType(RobotTypeEnum.CATEGORY_CHANGE_ROBOT.getType());
        messageReqDTO.setTraceId(UUID.randomUUID().toString());
        dongDongMessageRcpService.sendMessage(messageReqDTO);
    }

    private void buildCategoryTree(Integer jdParentCategoryId,Long parentCatId, String operator, List<String> langCodeList) {
        GetCategoryTreeParam treeParam = new GetCategoryTreeParam();
        treeParam.setRootCategoryId(jdParentCategoryId);
        treeParam.setLanguage(Language.zh_CN);
        CategoryTree categoryTree = categoryRpcService.getCategoryTree(treeParam);

        // 查询英文类目树
        treeParam.setLanguage(Language.en);
        CategoryTree enCategoryTree = categoryRpcService.getCategoryTree(treeParam);
        Map<Integer, Category> enCategoryMap = enCategoryTree.getCategories();

        if (null != categoryTree && MapUtils.isNotEmpty(categoryTree.getCategories())) {
            // 类目ID和类目信息映射
            Map<Integer, Category> categories = categoryTree.getCategories();
            Map<Integer, Set<Integer>> relations = categoryTree.getRelations();
            buildSonCategory(jdParentCategoryId,parentCatId, operator, langCodeList, relations, categories, enCategoryMap);
        }
    }

    private void buildSonCategory(Integer jdParentCategoryId,Long parentCatId, String operator, List<String> langCodeList, Map<Integer, Set<Integer>> relations, Map<Integer, Category> categories, Map<Integer, Category> enCategoryMap) {
        Set<Integer> sonCategory = relations.get(jdParentCategoryId);
        if (CollectionUtils.isNotEmpty(sonCategory)) {
            // 查询类目是否已经存在
            Set<Long> jdCatIds = sonCategory.stream().map(Long::valueOf).collect(Collectors.toSet());
            Map<Long, CategoryPO> jdCategoryPOMap = categoryAtomicService.queryCategoryMapByJdCatIds(jdCatIds);
            // 保存类目
            List<CategoryPO> categoryPOList = Lists.newArrayList();
            sonCategory.forEach(jdCatId -> {
                Category category = categories.get(jdCatId);
                CategoryPO categoryPO = null;
                if (MapUtils.isNotEmpty(jdCategoryPOMap) && jdCategoryPOMap.containsKey(Long.valueOf(jdCatId))) {
                    categoryPO = jdCategoryPOMap.get(Long.valueOf(jdCatId));
                    categoryPO.setUpdater(operator);
                    categoryPO.setUpdateTime(new Date());
                    categoryPO.setJdParentCatId(jdParentCategoryId.longValue());
                }else {
                    categoryPO = convertJdCategoryToPO(operator, parentCatId, category, relations);
                }
                categoryPOList.add(categoryPO);
            });
            categoryAtomicService.saveOrUpdateBatch(categoryPOList);


            Set<Long> catIds = categoryPOList.stream().filter(Objects::nonNull).map(CategoryPO::getJdCatId).collect(Collectors.toSet());
            Map<Long, List<CategoryLangPO>> dbCatLangMap = categoryLangAtomicService.queryCategoryLangMapByCatIds(catIds);

            for (CategoryPO po : categoryPOList) {
                Category jdCategory = categories.get(po.getJdCatId().intValue());
                Category jdEnCategory = enCategoryMap.get(po.getJdCatId().intValue());
                // 翻译类目名称
                buildCategoryLang(po, jdCategory,jdEnCategory, langCodeList, operator,dbCatLangMap);
                // 处理子级类目
                buildSonCategory(po.getJdCatId().intValue(),po.getId(), operator, langCodeList, relations, categories, enCategoryMap);
            }
        }
    }

    /**
     * 构建类目多语言名称。
     * @param categoryPO 类目PO对象
     * @param category 类目对象
     * @param jdEnCategory 英文类目对象
     * @param langCodeList 语言代码列表
     * @param operator 操作人
     * @param dbCatLangMap 数据库中类目多语言名称映射
     */
    private void buildCategoryLang(CategoryPO categoryPO, Category category,Category jdEnCategory, List<String> langCodeList, String operator,Map<Long, List<CategoryLangPO>> dbCatLangMap) {
        log.info("JdCategorySyncManageServiceImpl.buildCategoryLang 新增或更新类目多语言名称开始 jdCatId={}，operator={}",categoryPO.getJdCatId(),operator);
        Long id = categoryPO.getId();
        List<CategoryLangPO> langPOList = Lists.newArrayList();
        if (MapUtils.isNotEmpty(dbCatLangMap) && dbCatLangMap.containsKey(id)) {
            log.info("JdCategorySyncManageServiceImpl.buildCategoryLang 已经存在类目多语言名称，需要更新多语言 jdCatId={}，operator={}",categoryPO.getJdCatId(),operator);
            langPOList = dbCatLangMap.get(id);
            langPOList.forEach(langPo-> {
                langPo.setJdCatId(categoryPO.getJdCatId());
                langPo.setUpdater(operator);
                langPo.setUpdateTime(new Date());
            });
        }else {
            // 有英文名称不翻译
            if (Objects.nonNull(jdEnCategory) && StringUtils.isNotBlank(jdEnCategory.getCategoryName())){
                langCodeList.remove(LangConstant.LANG_EN);
            }
            // 翻译类目名称
            DataResponse<Map<String, String>> response = textTranslateManageService.translateMultiLangText(new MultiTranslateReqDTO(category.getCategoryName(), LangConstant.LANG_ZH, langCodeList, null));
            if (null != response && response.getSuccess() && MapUtils.isNotEmpty(response.getData())) {
                Map<String, String> multiLangText = response.getData();
                multiLangText.put(LangConstant.LANG_ZH, category.getCategoryName());
                // 补充英文名称
                if (!multiLangText.containsKey(LangConstant.LANG_EN)) {
                    multiLangText.put(LangConstant.LANG_EN, jdEnCategory.getCategoryName());
                }
                langPOList = Lists.newArrayListWithExpectedSize(multiLangText.size());
                for (Map.Entry<String, String> entry : multiLangText.entrySet()) {
                    CategoryLangPO langPO = getUpdateCategoryLangPO(entry.getKey(), entry.getValue(), id, category, operator);
                    langPOList.add(langPO);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(langPOList)) {
            categoryLangAtomicService.saveOrUpdateBatch(langPOList);
        }
        log.info("JdCategorySyncManageServiceImpl.buildCategoryLang 新增或更新类目多语言名称结束 jdCatId={}，operator={},多语言名称个数={}",categoryPO.getJdCatId(),operator,langPOList.size());
    }

    private CategoryLangPO getUpdateCategoryLangPO(String lang, String text, Long id, Category category, String operator) {
        CategoryLangPO langPO = new CategoryLangPO();
        langPO.setCatId(id);
        langPO.setJdCatId(category.getCategoryId().longValue());
        langPO.setLang(lang);
        langPO.setLangName(text);
        langPO.setCreator(operator);
        langPO.setUpdater(operator);
        Date now = new Date();
        langPO.setCreateTime(now);
        langPO.setUpdateTime(now);
        return langPO;
    }

    private List<String> getLangCodeList() {
        List<String> langCodeList = langManageService.getLangCodeList();
        langCodeList.remove(LangConstant.LANG_ZH);
        return langCodeList;
    }

    private CategoryPO convertJdCategoryToPO(String operator,Long parentCatId, Category category, Map<Integer, Set<Integer>> jdBatchRelationMap) {
        CategoryPO categoryPO = new CategoryPO();
        categoryPO.setJdCatId(Long.valueOf(category.getCategoryId()));
        // 国际层级比国内+1
        categoryPO.setCatLevel(category.getCategoryClass()+1);
        categoryPO.setParentCatId(parentCatId);
        categoryPO.setJdParentCatId(category.getFatherCategoryId().longValue());
        categoryPO.setSort(category.getOrderSort());
        categoryPO.setStatus(category.getStatus());
        categoryPO.setCreator(operator);
        categoryPO.setUpdater(operator);
        Date now = new Date();
        categoryPO.setCreateTime(now);
        categoryPO.setUpdateTime(now);
        // 是否叶子节点 根据子级数量判断
        buildLeafNodeFlag(category, jdBatchRelationMap, categoryPO);
        return categoryPO;
    }

    private void buildLeafNodeFlag(Category category, Map<Integer, Set<Integer>> jdBatchRelationMap, CategoryPO categoryPO) {
        if (MapUtils.isNotEmpty(jdBatchRelationMap) && jdBatchRelationMap.containsKey(category.getCategoryId()) && CollectionUtils.isNotEmpty(jdBatchRelationMap.get(category.getCategoryId()))) {
            categoryPO.setLeafNodeFlag(Boolean.FALSE);
        } else if (Constant.ONE == category.getCategoryClass()) {
            // 特殊处理，二级不能作为末级类目
            categoryPO.setLeafNodeFlag(Boolean.FALSE);
        } else {
            categoryPO.setLeafNodeFlag(Boolean.TRUE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 60)
    public DataResponse<Boolean> changeCategory(GmsCategoryReqDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getChangeDTOList())) {
            return DataResponse.success(Boolean.TRUE);
        }
        // 定义是批量，实际使用只支持单个操作，为了防止后发先至问题，每次只处理一条消息，有问题就重试
        List<GmsCategoryChangeDTO> changeDTOList = reqDTO.getChangeDTOList();

        GmsCategoryChangeDTO categoryChangeDTO = changeDTOList.get(0);
        if (JdCategoryOpTypeEnum.CHANGE.getCode().equals(categoryChangeDTO.getOptType())) {
            updateGmsCategory(categoryChangeDTO);
        }else  {
            insertGmsCategory(categoryChangeDTO);
        }
        return DataResponse.success(Boolean.TRUE);
    }

    /**
     * 插入GMS分类信息
     * @param changeDTO GMS分类变更DTO对象，包含分类信息和变更操作类型
     */
    private void insertGmsCategory(GmsCategoryChangeDTO changeDTO) {
        Long fidCatId = changeDTO.getFidCatId();
        Long catId = changeDTO.getCatId();
        // 查询父类目和当前是否存在
        Map<Long, CategoryPO> categoryPOMap = categoryAtomicService.queryCategoryMapByJdCatIds(Sets.newHashSet(fidCatId, catId));
        if (MapUtils.isEmpty(categoryPOMap) || !categoryPOMap.containsKey(fidCatId)) {
            String format = String.format("当前主站类目ID [%s] 的父级类目ID [%s] 不存在", catId, fidCatId);
            log.warn("JdCategorySyncManageServiceImpl.insertGmsCategory {}",format);
            throw new BizException(format);
        }

        if (categoryPOMap.containsKey(catId)) {
            return;
        }

        // 插入当前类目和多语言信息
        CategoryPO categoryPO = buildInsertCategoryPO(changeDTO, categoryPOMap);
        boolean insertCategory = categoryAtomicService.saveOrUpdate(categoryPO);
        if (!insertCategory) {
            throw new BizException("新增京东类目失败 类目ID:"+  changeDTO.getCatId());
        }

        // 插入默认销售属性
        insertDefaultCategorySaleAttribute(categoryPO);

        // 翻译多语言
        List<String> langCodeList = getLangCodeList();
        DataResponse<Map<String, String>> response = translateMultiLang(changeDTO, Sets.newHashSet(langCodeList));
        // 翻译类目名称
        if (response.getSuccess() && MapUtils.isNotEmpty(response.getData())) {
            // 插入多语言信息
            langCodeList.add(LangConstant.LANG_ZH);
            List<CategoryLangPO> insertCategoryLangPoList = getInsertCategoryLangPoList(changeDTO, response,langCodeList,categoryPO.getId());
            if (CollectionUtils.isNotEmpty(insertCategoryLangPoList)) {
                categoryLangAtomicService.saveOrUpdateBatch(insertCategoryLangPoList);
            }
        }else {
            log.warn("JdCategorySyncManageServiceImpl.insertGmsCategory 类目名称翻译失败,catId={},changeDTO={},response={}", catId, JSON.toJSONString(changeDTO), JSON.toJSONString(response));
        }
        // 如果当前是新增的四级，将三级设置为非末级节点
        if (CategoryLevelEnum.FOUR.getCode().equals(changeDTO.getLevel())){
            LambdaUpdateWrapper<CategoryPO> updateWrapper = Wrappers.lambdaUpdate(CategoryPO.class)
                    .set(CategoryPO::getLeafNodeFlag, Boolean.FALSE)
                    .set(CategoryPO::getUpdater, Constant.SYSTEM + SYNC_CATEGORY_UPDATER)
                    .set(CategoryPO::getUpdateTime, new Date())
                    .eq(CategoryPO::getJdCatId, changeDTO.getFidCatId());
            categoryAtomicService.update(updateWrapper);
            log.info("JdCategorySyncManageServiceImpl.insertGmsCategory 新增四级类目，将父级类目设置为非末级节点,catId={}，fidCatId={},changeDTO={}", catId,fidCatId, JSON.toJSONString(changeDTO));
        }
        // 人工操作不发消息
        if (CategoryOperationSourceTypeEnum.ARTIFICIAL.equals(changeDTO.getOperationSourceType())) {
            return;
        }
        // 通知
        this.insertNotifyAsync(categoryPO);
    }

    private void insertDefaultCategorySaleAttribute(CategoryPO categoryPO) {
        if (categoryPO.getLeafNodeFlag()) {
            CategoryAttributePO updatePo = new CategoryAttributePO();
            updatePo.setCatId(categoryPO.getId());
            updatePo.setJdCatId(categoryPO.getJdCatId());
            updatePo.setAttributeId(Constant.DEFAULT_SALE_ATTRIBUTE_ID);
            updatePo.setCateAttributeType(CategoryAttrTypeEnum.SELL.getCode());
            updatePo.setSort(Constant.ATTRIBUTE_VAL_OTHER_SORT);
            updatePo.setRequired(Constant.ZERO.intValue());
            updatePo.setStatus(StatusEnum.ENABLE.getCode());
            updatePo.setCreator(Constant.SYSTEM+SYNC_CATEGORY_UPDATER);
            updatePo.setUpdater(Constant.SYSTEM+SYNC_CATEGORY_UPDATER);
            Date date = new Date();
            updatePo.setCreateTime(date);
            updatePo.setUpdateTime(date);
            categoryAttributeAtomicService.saveOrUpdate(updatePo);
        }
    }

    private @NotNull CategoryPO buildInsertCategoryPO(GmsCategoryChangeDTO changeDTO, Map<Long, CategoryPO> categoryPOMap) {
        CategoryPO fatherCategoryPo = categoryPOMap.get(changeDTO.getFidCatId());
        CategoryPO categoryPO = CategoryConvert.INSTANCE.changeDto2Po(changeDTO);
        categoryPO.setParentCatId(fatherCategoryPo.getId());
        categoryPO.setSort(Constant.ATTRIBUTE_VAL_OTHER_SORT);
        Date date = new Date();
        categoryPO.setCreateTime(date);
        categoryPO.setUpdateTime(date);
        categoryPO.setCreator(Constant.SYSTEM + SYNC_CATEGORY_UPDATER);
        categoryPO.setUpdater(Constant.SYSTEM + SYNC_CATEGORY_UPDATER);
        categoryPO.setYn(YnEnum.YES.getCode());
        // 末级标识需要根据子级数量判断
        if (CategoryLevelEnum.TWO.getCode().equals(changeDTO.getLevel())){
            categoryPO.setLeafNodeFlag(Boolean.FALSE);
        } else if (CategoryLevelEnum.THREE.getCode().equals(changeDTO.getLevel())) {
            // 查询是否存在子级类判断末级
            categoryPO.setLeafNodeFlag(this.isLeafNode(changeDTO));
        }else if (CategoryLevelEnum.FOUR.getCode().equals(changeDTO.getLevel())){
            categoryPO.setLeafNodeFlag(Boolean.TRUE);
        }
        return categoryPO;
    }

    private boolean isLeafNode(GmsCategoryChangeDTO changeDTO) {
        GetRelationParam relationParam = new GetRelationParam();
        relationParam.setCategoryId(changeDTO.getCatId().intValue());
        relationParam.setRelation(GetRelationParam.Relation.GET_SON);
        List<Category> relation = categoryRpcService.getRelation(relationParam);
        // 没有子级，返回true 有子级返回false
        return CollectionUtils.isEmpty(relation);
    }


    /**
     * 更新GMS类目
     * @param changeDTO GMS类目变更DTO对象，包含类目ID和状态信息
     */
    private void updateGmsCategory(GmsCategoryChangeDTO changeDTO) {
        // 主站类目ID
        Long catId = changeDTO.getCatId();
        // 查询是否存在有效的类目
        CategoryPO categoryPO = categoryAtomicService.getCategoryByJdCatId(catId);
        if (Objects.isNull(categoryPO)) {
            log.warn("JdCategorySyncManageServiceImpl.updateGmsCategory 类目不存在,不需要更新 catId={},changeDTO={}", catId,JSON.toJSONString(changeDTO));
            return;
        }
        // 更新类目状态
        this.updateCategoryStatus(changeDTO, categoryPO, catId);
        // 类目名称变更同步更新
        this.updateCategoryName(changeDTO, catId);
        // 父级类目变更时，更新类目关联数据
        this.updateFatherCategoryId(changeDTO, categoryPO);
    }

    /**
     * 更新类目父级ID
     * @param changeDTO GmsCategoryChangeDTO对象，包含要更新的类目信息
     * @param categoryPO CategoryPO对象，表示要更新的类目实体
     */
    private void updateFatherCategoryId(GmsCategoryChangeDTO changeDTO, CategoryPO categoryPO) {
        if (Objects.isNull(updateFatherCategorySwitch) || !updateFatherCategorySwitch){
            return;
        }

        // 一级类目跳过
        if (CategoryLevelEnum.ONE.getCode().equals(changeDTO.getLevel())) {
            return;
        }

        Long catId = changeDTO.getCatId();
        if (!changeDTO.getFidCatId().equals(categoryPO.getJdParentCatId())){
            long startTime = System.currentTimeMillis();
            log.info("JdCategorySyncManageServiceImpl.updateGmsCategory  类目父级变更开始,catId={},fidCatId={},originFatherId={} startTime={} changeDTO={}", catId, changeDTO.getFidCatId(), categoryPO.getJdParentCatId(),startTime, JSON.toJSONString(changeDTO));
            CategoryPO fatherCategoryPO = categoryAtomicService.getCategoryByJdCatId(changeDTO.getFidCatId());
            if (Objects.isNull(fatherCategoryPO)) {
                Profiler.businessAlarm(UmpKeyConstant.JDI_ISC_CATEGORY_ALERT, String.format("【%s】 %s 类目ID[%s]父级变更，不存在有效的新父级和上级类目,请检查处理"
                        , systemProfile
                        , LevelCode.P1.getMessage()
                        , changeDTO.getCatId()
                ));
                log.error("JdCategorySyncManageServiceImpl.updateGmsCategory 类目父级变更失败，类目ID={},父级类目ID={} 成功",catId,changeDTO.getFidCatId());
                return;
            }

            CategoryPO updatePo = new CategoryPO();
            updatePo.setId(categoryPO.getId());
            updatePo.setParentCatId(fatherCategoryPO.getId());
            updatePo.setJdParentCatId(changeDTO.getFidCatId());
            updatePo.setUpdater(Constant.SYSTEM+SYNC_CATEGORY_UPDATER);
            updatePo.setUpdateTime(new Date());
            boolean updated = categoryAtomicService.updateById(updatePo);
            if (!updated) {
                log.warn("JdCategorySyncManageServiceImpl.updateGmsCategory 更新类目父级失败,catId={},fidCatId={},originFatherId={} changeDTO={}", catId, changeDTO.getFidCatId(), categoryPO.getJdParentCatId(), JSON.toJSONString(changeDTO));
                throw new BizException(String.format("更新类目父级失败,类目ID=%s,父级类目ID=%s", catId, changeDTO.getFidCatId()));
            }

            /*
             * 1、类目上级变更时，需要将当前类目关联的履约费率、利润率表的数据复制新增一份。
             * 2、不走更新逻辑，保持原数据不变，增加新数据查询逻辑，查询出关联的履约费率、利润率表的数据，
             */
            if (CategoryLevelEnum.TWO.getCode().equals(changeDTO.getLevel())) {
                // 查询子级履约费率列表，增加履约费率信息jdi_isc_country_fulfillment_rate
                LambdaQueryWrapper<FulfillmentRatePO> fulfillmentRateQueryWrapper = Wrappers.lambdaQuery(FulfillmentRatePO.class)
                        .eq(FulfillmentRatePO::getSecondJdCatId, catId)
                        .eq(FulfillmentRatePO::getYn, YnEnum.YES.getCode());
                List<FulfillmentRatePO> secondDbfulfillmentRatePOList = fulfillmentRateAtomicService.list(fulfillmentRateQueryWrapper);
                if (CollectionUtils.isNotEmpty(secondDbfulfillmentRatePOList)) {
                    // 复制履约费率
                    List<FulfillmentRatePO> addFulfillmentRatePOList = Lists.newArrayListWithExpectedSize(secondDbfulfillmentRatePOList.size());
                    for (FulfillmentRatePO dbFulfillmentRatePO : secondDbfulfillmentRatePOList) {
                        FulfillmentRatePO addFulfillmentRatePO = copyFulfillmentPOFromDbFulfillment(dbFulfillmentRatePO,Constant.ID);
                        addFulfillmentRatePO.setFirstJdCatId(changeDTO.getFidCatId());
                        addFulfillmentRatePOList.add(addFulfillmentRatePO);
                    }

                    if (CollectionUtils.isNotEmpty(addFulfillmentRatePOList)) {
                        List<List<FulfillmentRatePO>> partitionList = Lists.partition(addFulfillmentRatePOList, Constant.PAGE_SIZE);
                        for (List<FulfillmentRatePO> partition : partitionList) {
                            fulfillmentRateAtomicService.saveBatch(partition);
                        }
                    }
                }
                log.info("JdCategorySyncManageServiceImpl.updateGmsCategory 更新新履约费率表，二级类目ID={},父级/一级类目ID={} 成功",catId,changeDTO.getFidCatId());

                // 更新类目关联的利润率阈值表 jdi_isc_profit_rate
                LambdaQueryWrapper<ProfitRatePO> profitRateQueryWrapper = Wrappers.lambdaQuery(ProfitRatePO.class)
                        .eq(ProfitRatePO::getSecondJdCatId, catId)
                        .eq(ProfitRatePO::getYn, YnEnum.YES.getCode());
                List<ProfitRatePO> secondDbProfitRatePoList = profitRateAtomicService.list(profitRateQueryWrapper);
                if (CollectionUtils.isNotEmpty(secondDbProfitRatePoList)) {
                    // 复制利润率阈值
                    List<ProfitRatePO> addProfitRatePOList = Lists.newArrayListWithExpectedSize(secondDbProfitRatePoList.size());
                    for (ProfitRatePO dbProfitRatePO : secondDbProfitRatePoList) {
                        ProfitRatePO addProfitRatePO = copyProfitRatePOFromDbProfitRate(dbProfitRatePO,Constant.ID);
                        addProfitRatePO.setFirstJdCatId(changeDTO.getFidCatId());
                        addProfitRatePOList.add(addProfitRatePO);
                    }

                    if (CollectionUtils.isNotEmpty(addProfitRatePOList)) {
                        List<List<ProfitRatePO>> partitionList = Lists.partition(addProfitRatePOList, Constant.PAGE_SIZE);
                        for (List<ProfitRatePO> partition : partitionList) {
                            profitRateAtomicService.saveBatch(partition);
                        }
                    }
                }
                log.info("JdCategorySyncManageServiceImpl.updateGmsCategory 更新利润率阈值表，二级类目ID={},父级/一级类目ID={} 成功",catId,changeDTO.getFidCatId());

            }else if (CategoryLevelEnum.THREE.getCode().equals(changeDTO.getLevel())){
                // 更新类目关联的履约费率数据 jdi_isc_country_fulfillment_rate
                CategoryPO firstCategoryPo = categoryAtomicService.getCategoryByJdCatId(changeDTO.getFidCatId());
                LambdaQueryWrapper<FulfillmentRatePO> fulfillmentRateQueryWrapper = Wrappers.lambdaQuery(FulfillmentRatePO.class)
                        .eq(FulfillmentRatePO::getThirdJdCatId, catId)
                        .eq(FulfillmentRatePO::getYn, YnEnum.YES.getCode());
                List<FulfillmentRatePO> thirdDbfulfillmentRatePOList = fulfillmentRateAtomicService.list(fulfillmentRateQueryWrapper);
                if (CollectionUtils.isNotEmpty(thirdDbfulfillmentRatePOList)) {
                    // 复制履约费率
                    List<FulfillmentRatePO> addFulfillmentRatePOList = Lists.newArrayListWithExpectedSize(thirdDbfulfillmentRatePOList.size());
                    for (FulfillmentRatePO dbFulfillmentRatePO : thirdDbfulfillmentRatePOList) {
                        FulfillmentRatePO addFulfillmentRatePO = copyFulfillmentPOFromDbFulfillment(dbFulfillmentRatePO,Constant.ID);
                        addFulfillmentRatePO.setSecondJdCatId(changeDTO.getFidCatId());
                        addFulfillmentRatePO.setFirstJdCatId(firstCategoryPo.getJdParentCatId());
                        addFulfillmentRatePOList.add(addFulfillmentRatePO);
                    }

                    if (CollectionUtils.isNotEmpty(addFulfillmentRatePOList)) {
                        List<List<FulfillmentRatePO>> partitionList = Lists.partition(addFulfillmentRatePOList, Constant.PAGE_SIZE);
                        for (List<FulfillmentRatePO> partition : partitionList) {
                            fulfillmentRateAtomicService.saveBatch(partition);
                        }
                    }
                }
                log.info("JdCategorySyncManageServiceImpl.updateGmsCategory 更新新履约费率表，三级类目ID={},父级/二级类目ID={} 成功",catId,changeDTO.getFidCatId());

                // 更新类目关联的利润率阈值表 jdi_isc_profit_rate
                LambdaQueryWrapper<ProfitRatePO> profitRateQueryWrapper = Wrappers.lambdaQuery(ProfitRatePO.class)
                        .eq(ProfitRatePO::getThirdJdCatId, catId)
                        .eq(ProfitRatePO::getYn, YnEnum.YES.getCode());
                List<ProfitRatePO> thirdDbProfitRatePoList = profitRateAtomicService.list(profitRateQueryWrapper);
                if (CollectionUtils.isNotEmpty(thirdDbProfitRatePoList)) {
                    // 复制利润率阈值
                    List<ProfitRatePO> addProfitRatePOList = Lists.newArrayListWithExpectedSize(thirdDbProfitRatePoList.size());
                    for (ProfitRatePO dbFulfillmentRatePO : thirdDbProfitRatePoList) {
                        ProfitRatePO addProfitRatePO = copyProfitRatePOFromDbProfitRate(dbFulfillmentRatePO,Constant.ID);
                        addProfitRatePO.setSecondJdCatId(changeDTO.getFidCatId());
                        addProfitRatePO.setFirstJdCatId(firstCategoryPo.getJdParentCatId());
                        addProfitRatePOList.add(addProfitRatePO);
                    }

                    if (CollectionUtils.isNotEmpty(addProfitRatePOList)) {
                        List<List<ProfitRatePO>> partitionList = Lists.partition(addProfitRatePOList, Constant.PAGE_SIZE);
                        for (List<ProfitRatePO> partition : partitionList) {
                            profitRateAtomicService.saveBatch(partition);
                        }
                    }
                }
                log.info("JdCategorySyncManageServiceImpl.updateGmsCategory 更新利润率阈值表，三级类目ID={},父级/二级类目ID={} 成功",catId,changeDTO.getFidCatId());
            }else if (CategoryLevelEnum.FOUR.getCode().equals(changeDTO.getLevel())){
                List<CategoryPathVO> categoryPathVOList = categoryAtomicService.path(Sets.newHashSet(changeDTO.getCatId()), StatusEnum.ENABLE.getCode());
                Map<Long, CategoryPathVO> categoryPathVoMap = Optional.ofNullable(categoryPathVOList).orElseGet(ArrayList::new).stream().filter(Objects::nonNull).filter(pathVo -> Objects.nonNull(pathVo.getId4())).collect(Collectors.toMap(CategoryPathVO::getId4, Function.identity()));
                if (MapUtils.isEmpty(categoryPathVoMap) || !categoryPathVoMap.containsKey(changeDTO.getCatId())) {
                    Profiler.businessAlarm(UmpKeyConstant.JDI_ISC_CATEGORY_ALERT, String.format("【%s】 %s 四级类目ID[%s]父级变更，不存在有效的新父级和上级类目,不能更新履约费率/利润率表,请检查处理"
                            , systemProfile
                            , LevelCode.P1.getMessage()
                            , changeDTO.getCatId()
                    ));
                    log.error("JdCategorySyncManageServiceImpl.updateGmsCategory 查询四级类目完整关系数据异常，四级类目ID={},父级/三级类目ID={} 成功",catId,changeDTO.getFidCatId());
                    return;
                }

                CategoryPathVO categoryPathVO = categoryPathVoMap.get(changeDTO.getCatId());
                // 更新类目关联的履约费率数据 jdi_isc_country_fulfillment_rate
                LambdaQueryWrapper<FulfillmentRatePO> fulfillmentRateQueryWrapper = Wrappers.lambdaQuery(FulfillmentRatePO.class)
                        .eq(FulfillmentRatePO::getLastJdCatId, catId)
                        .eq(FulfillmentRatePO::getYn, YnEnum.YES.getCode());
                List<FulfillmentRatePO> fourDbfulfillmentRatePOList = fulfillmentRateAtomicService.list(fulfillmentRateQueryWrapper);
                if (CollectionUtils.isNotEmpty(fourDbfulfillmentRatePOList)) {
                    // 复制履约费率
                    List<FulfillmentRatePO> addFulfillmentRatePOList = Lists.newArrayListWithExpectedSize(fourDbfulfillmentRatePOList.size());
                    for (FulfillmentRatePO dbFulfillmentRatePO : fourDbfulfillmentRatePOList) {
                        FulfillmentRatePO addFulfillmentRatePO = copyFulfillmentPOFromDbFulfillment(dbFulfillmentRatePO,Constant.ID);
                        addFulfillmentRatePO.setThirdJdCatId(changeDTO.getFidCatId());
                        addFulfillmentRatePO.setSecondJdCatId(categoryPathVO.getId2());
                        addFulfillmentRatePO.setFirstJdCatId(categoryPathVO.getId1());
                        addFulfillmentRatePOList.add(addFulfillmentRatePO);
                    }

                    if (CollectionUtils.isNotEmpty(addFulfillmentRatePOList)) {
                        List<List<FulfillmentRatePO>> partitionList = Lists.partition(addFulfillmentRatePOList, Constant.PAGE_SIZE);
                        for (List<FulfillmentRatePO> partition : partitionList) {
                            fulfillmentRateAtomicService.saveBatch(partition);
                        }
                    }
                }
                log.info("JdCategorySyncManageServiceImpl.updateGmsCategory 更新新履约费率表，四级类目ID={},父级/三级类目ID={} 成功 类目路径={}",catId,changeDTO.getFidCatId(),JSON.toJSONString(categoryPathVO));

                // 更新类目关联的利润率阈值表 jdi_isc_profit_rate
                LambdaQueryWrapper<ProfitRatePO> profitRateQueryWrapper = Wrappers.lambdaQuery(ProfitRatePO.class)
                        .eq(ProfitRatePO::getLastJdCatId, catId)
                        .eq(ProfitRatePO::getYn, YnEnum.YES.getCode());
                List<ProfitRatePO> fourDbProfitRatePoList = profitRateAtomicService.list(profitRateQueryWrapper);
                if (CollectionUtils.isNotEmpty(fourDbProfitRatePoList)) {
                    // 复制利润率阈值
                    List<ProfitRatePO> addProfitRatePOList = Lists.newArrayListWithExpectedSize(fourDbProfitRatePoList.size());
                    for (ProfitRatePO dbFulfillmentRatePO : fourDbProfitRatePoList) {
                        ProfitRatePO addProfitRatePO = copyProfitRatePOFromDbProfitRate(dbFulfillmentRatePO,Constant.ID);
                        addProfitRatePO.setThirdJdCatId(changeDTO.getFidCatId());
                        addProfitRatePO.setSecondJdCatId(categoryPathVO.getId2());
                        addProfitRatePO.setFirstJdCatId(categoryPathVO.getId1());
                        addProfitRatePOList.add(addProfitRatePO);
                    }

                    if (CollectionUtils.isNotEmpty(addProfitRatePOList)) {
                        List<List<ProfitRatePO>> partitionList = Lists.partition(addProfitRatePOList, Constant.PAGE_SIZE);
                        for (List<ProfitRatePO> partition : partitionList) {
                            profitRateAtomicService.saveBatch(partition);
                        }
                    }
                }
                log.info("JdCategorySyncManageServiceImpl.updateGmsCategory 更新利润率阈值表，四级类目ID={},父级/三级类目ID={} 成功 类目路径={}",catId,changeDTO.getFidCatId(),JSON.toJSONString(categoryPathVO));
            }

            // 人工操作不发消息
            if (CategoryOperationSourceTypeEnum.ARTIFICIAL.equals(changeDTO.getOperationSourceType())) {
                return;
            }
            // 通知消息
            this.changeNotifyParentAsync(updatePo,categoryPO);

            long endTime = System.currentTimeMillis();
            long costTime = endTime - startTime;
            log.info("JdCategorySyncManageServiceImpl.updateGmsCategory  类目父级变更结束,catId={},fidCatId={},originFatherId={} endTime={},costTime={} changeDTO={}", catId, changeDTO.getFidCatId(), categoryPO.getJdParentCatId(), endTime,costTime, JSON.toJSONString(changeDTO));
        }
    }

    private void updateCategoryName(GmsCategoryChangeDTO changeDTO, Long catId) {
        // 类目名称变更同步更新
        if (StringUtils.isBlank(changeDTO.getCatName())) {
            return;
        }
        // 查询类目多语言
        Map<String, CategoryLangPO> categoryLangPOMap = categoryLangAtomicService.queryCategoryLangMapByJdCatId(catId);
        if (MapUtils.isEmpty(categoryLangPOMap) || !categoryLangPOMap.containsKey(LangConstant.LANG_ZH)) {
            return;
        }
        List<CategoryLangPO> updateCategoryLangPoList = new ArrayList<>();
        // 类目名称不同时更新
        if (!changeDTO.getCatName().equals(categoryLangPOMap.get(LangConstant.LANG_ZH).getLangName())) {
            // 翻译类目名称
            DataResponse<Map<String, String>> response = translateMultiLang(changeDTO, categoryLangPOMap.keySet());
            if (response.getSuccess() && MapUtils.isNotEmpty(response.getData())) {
                // 更新类目多语言
                updateCategoryLangPoList = getUpdateCategoryLangPoList(changeDTO, response, categoryLangPOMap);
                if (CollectionUtils.isNotEmpty(updateCategoryLangPoList)) {
                    categoryLangAtomicService.updateBatchById(updateCategoryLangPoList);
                }
            }else {
                log.warn("JdCategorySyncManageServiceImpl.updateGmsCategory 类目名称翻译失败,catId={},changeDTO={},response={}", catId, JSON.toJSONString(changeDTO), JSON.toJSONString(response));
            }
        }
        // 人工操作不发消息
        if (CategoryOperationSourceTypeEnum.ARTIFICIAL.equals(changeDTO.getOperationSourceType())) {
            return;
        }
        // 异步消息通知
        this.changeNotifyNameAsync(updateCategoryLangPoList,categoryLangPOMap);
    }

    private void updateCategoryStatus(GmsCategoryChangeDTO changeDTO, CategoryPO categoryPO, Long catId) {
        // 当主站类目状态为禁用，国际类目状态是启用时，禁用类目
        if (StatusEnum.FORBIDDEN.getCode().equals(changeDTO.getStatus())  && StatusEnum.ENABLE.getCode().equals(categoryPO.getStatus())){
            CategoryPO updatePo = new CategoryPO();
            updatePo.setId(categoryPO.getId());
            updatePo.setJdCatId(categoryPO.getJdCatId());
            updatePo.setStatus(StatusEnum.FORBIDDEN.getCode());
            updatePo.setUpdater(this.getOperator(changeDTO));
            updatePo.setUpdateTime(new Date());
            categoryAtomicService.updateById(updatePo);
            log.info("JdCategorySyncManageServiceImpl.updateGmsCategory 更新类目为禁用状态成功,catId={},changeDTO={}", catId,JSON.toJSONString(changeDTO));
            // 批量禁用子级类目
            batchForbiddenStatus(updatePo, changeDTO);
            // 四级类目禁用时特殊处理
            if (CategoryLevelEnum.FOUR.getCode().equals(changeDTO.getLevel())) {
                upgradeLeafNodFromFourToThree(changeDTO);
            }
            // 人工操作不发消息
            if (CategoryOperationSourceTypeEnum.ARTIFICIAL.equals(changeDTO.getOperationSourceType())) {
                return;
            }
            // 异步通知消息
            this.changeNotifyStatusAsync(Lists.newArrayList(updatePo));
        }
    }

    /**
     * 当主站类目是四级禁用时，
     * 1、判断父级类目下所有自己都为禁用状态，则将父级设置为叶子节点
     * 2、增加三级类目（新叶子节点）的产品线
     * 3、增加三级类目（新叶子节点）的利润率阈值
     * 4、增加三级类目（新叶子节点）的履约费率
     * 5、增加三级类目（新叶子节点）的越南税率
     *
     * @param changeDTO GmsCategoryChangeDTO对象，包含要更新的类目信息
     */
    private void upgradeLeafNodFromFourToThree(GmsCategoryChangeDTO changeDTO) {
        // 1、判断父级类目下所有自己都为禁用状态，则将父级设置为叶子节点
        boolean upgraded = upgradeCategoryLeafNodeLevel(changeDTO);
        if (!upgraded) {
            return;
        }
        // 2、查询四级类目的产品线，然后三级（新末级）的产品线
        upgradeBusinessLineRelation(changeDTO);
        // 3、查询四级和三级的利润率阈值，增加三级类目（新叶子节点）的利润率阈值
        upgradeProfitRate(changeDTO);
        // 4、查询四级和三级的履约利润率，增加三级类目（新叶子节点）的履约费率
        upgradeCountryFulfillmentRate(changeDTO);
        // 5、查询四级和三级类目的越南税率，增加三级类目（新叶子节点）的越南税率
        upgradeCategoryTax(changeDTO);
    }

    /**
     * 升级商品类别税率
     * @param changeDTO 包含商品类别和新税率的DTO对象
     */
    private void upgradeCategoryTax(GmsCategoryChangeDTO changeDTO){
        LambdaQueryWrapper<CategoryTaxPO> queryWrapper = Wrappers.lambdaQuery(CategoryTaxPO.class)
                .eq(CategoryTaxPO::getJdCatId, changeDTO.getCatId())
                .eq(CategoryTaxPO::getCountryCode, CountryConstant.COUNTRY_VN)
                .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode());
        List<CategoryTaxPO> dbFourCategoryTaxPoList = categoryTaxAtomicService.list(queryWrapper);
        // 类目未设置越南税率，发送消息提醒业务
        if (CollectionUtils.isEmpty(dbFourCategoryTaxPoList)) {
            log.info("upgradeCategoryTax 越南税率缺失，发送三级类目缺失税率消息 changeDTO={}", JSON.toJSONString(changeDTO));
            // 类目状态变更导致的类目越南税率问题，发送消息通知税务
            Map<String, CategoryLangPO> langMap = categoryLangAtomicService.queryCategoryLangMapByJdCatId(changeDTO.getFidCatId());

            List<String> erps = this.getRoleErps(RoleEnum.TAX_BR.getCode());

            CategoryLangPO catNameZh = langMap.get(LangConstant.LANG_ZH);
            CategoryLangPO catNameEn = langMap.get(LangConstant.LANG_EN);
            // 发送消息
            String messageText = ConfigUtils.getStringByKey(msgNoticeTemplate, CategoryMsgNoticeEnum.VN_RATE_LOSE.name());
            if (StringUtils.isBlank(messageText)){
                log.warn("upgradeCategoryTax 越南税率缺失，消息体结构不存在 key={},msgNoticeTemplate={}",CategoryMsgNoticeEnum.VN_RATE_LOSE.name(),msgNoticeRoles);
            }else {
                String msg = String.format(messageText,
                        Objects.nonNull(catNameZh) ? catNameZh.getLangName() : changeDTO.getCatName()
                        , Objects.nonNull(catNameEn) ? catNameEn.getLangName() : changeDTO.getCatName()
                        , changeDTO.getFidCatId());
                this.sendCategoryNotify(msg, changeDTO.getFidCatId(), Sets.newHashSet(erps), "JdCategorySyncManageServiceImpl.upgradeCategoryTax");
            }
        }else {
            log.info("upgradeCategoryTax 类目从四级升到三级，复制四级的类目越南税率到三级开始，四级类目ID:{} 三级类目ID:{} 四级越南类目税率:[{}]",changeDTO.getCatId(),changeDTO.getFidCatId(),JSON.toJSONString(dbFourCategoryTaxPoList));
            // 查询三级类目是否设置了越南税率
            LambdaQueryWrapper<CategoryTaxPO> thirdQueryWrapper = Wrappers.lambdaQuery(CategoryTaxPO.class)
                    .eq(CategoryTaxPO::getJdCatId, changeDTO.getFidCatId())
                    .eq(CategoryTaxPO::getCountryCode, CountryConstant.COUNTRY_VN)
                    .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode());
            List<CategoryTaxPO> dbThirdCategoryTaxPoList = categoryTaxAtomicService.list(thirdQueryWrapper);
            // 三级未设置过越南税率时，将四级的税率复制到三级
            if (CollectionUtils.isEmpty(dbThirdCategoryTaxPoList)) {
                List<CategoryTaxPO> addCategoryTaxPOList = Lists.newArrayListWithExpectedSize(dbFourCategoryTaxPoList.size());
                for (CategoryTaxPO dbFourCategoryTaxPo : dbFourCategoryTaxPoList){
                    CategoryTaxPO addCategoryTaxPO = copyCategoryTaxPOFromLastCatId(dbFourCategoryTaxPo);
                    addCategoryTaxPO.setJdCatId(changeDTO.getFidCatId());
                    addCategoryTaxPO.setCategoryId(changeDTO.getFidCatId());
                    addCategoryTaxPOList.add(addCategoryTaxPO);
                }

                if (CollectionUtils.isNotEmpty(addCategoryTaxPOList)) {
                    categoryTaxAtomicService.saveOrUpdateBatch(addCategoryTaxPOList);
                }
                log.info("upgradeCategoryTax 类目从四级升到三级，复制四级的类目越南税率到三级结束，四级类目ID:{} 三级类目ID:{} 新越南税率列表:[{}]",changeDTO.getCatId(),changeDTO.getFidCatId(),JSON.toJSONString(addCategoryTaxPOList));
            }
        }
    }

    /**
     * 升级国家的履约率。
     * @param changeDTO GmsCategoryChangeDTO 对象，包含更新信息。
     */
    private void upgradeCountryFulfillmentRate(GmsCategoryChangeDTO changeDTO) {
        // 查询四级类目是否有履约费率
        LambdaQueryWrapper<FulfillmentRatePO> queryWrapper = Wrappers.lambdaQuery(FulfillmentRatePO.class)
                .eq(FulfillmentRatePO::getLastJdCatId, changeDTO.getCatId()).eq(FulfillmentRatePO::getYn, YnEnum.YES.getCode());
        List<FulfillmentRatePO> dbLastCatIdFulfillmentPoList = fulfillmentRateAtomicService.list(queryWrapper);
        if (CollectionUtils.isEmpty(dbLastCatIdFulfillmentPoList)) {
            log.info("upgradeCountryFulfillmentRate 类目四级升级到三级，四级类目履约费率为空，不需要复制履约费率到三级 四级类目ID:{} 三级类目ID:{}",changeDTO.getCatId(),changeDTO.getFidCatId());
            return;
        }

        log.info("upgradeCountryFulfillmentRate 类目四级升级到三级，复制履约费率到三级开始 四级类目ID:{} 三级类目ID:{} 四级履约费率:[{}]",changeDTO.getCatId(),changeDTO.getFidCatId(),JSON.toJSONString(dbLastCatIdFulfillmentPoList));
        // 查询只设置到三级的履约费率
        LambdaQueryWrapper<FulfillmentRatePO> thirdQueryWrapper = Wrappers.lambdaQuery(FulfillmentRatePO.class)
                .eq(FulfillmentRatePO::getThirdJdCatId, changeDTO.getFidCatId())
                .isNull(FulfillmentRatePO::getLastJdCatId)
                .eq(FulfillmentRatePO::getYn, YnEnum.YES.getCode());
        List<FulfillmentRatePO> dbThirdProfitRatePoList = fulfillmentRateAtomicService.list(thirdQueryWrapper);
        // 三级的履约费率不存在，插入一条新的数据
        if (CollectionUtils.isEmpty(dbThirdProfitRatePoList)) {
            List<FulfillmentRatePO> addFulfillmentPoList = Lists.newArrayListWithExpectedSize(dbLastCatIdFulfillmentPoList.size());
            for (FulfillmentRatePO dbLastCatProfitPo : dbLastCatIdFulfillmentPoList){
                FulfillmentRatePO addProfitRatePo = copyFulfillmentPOFromDbFulfillment(dbLastCatProfitPo,"id","updater","updateTime","lastCatId","lastJdCatId");
                addFulfillmentPoList.add(addProfitRatePo);
            }

            if (CollectionUtils.isNotEmpty(addFulfillmentPoList)) {
                fulfillmentRateAtomicService.saveOrUpdateBatch(addFulfillmentPoList);
            }
            log.info("upgradeCountryFulfillmentRate 类目四级升级到三级，复制履约费率到三级结束 四级类目ID:{} 三级类目ID:{}，新类目履约费率:[{}]",changeDTO.getCatId(),changeDTO.getFidCatId(),JSON.toJSONString(addFulfillmentPoList));
        }
    }


    /**
     * 升级利润率
     * @param changeDTO GmsCategoryChangeDTO对象，包含了需要升级的类目信息
     */
    private void upgradeProfitRate(GmsCategoryChangeDTO changeDTO) {
        // 查询四级类目是否有利润率
        LambdaQueryWrapper<ProfitRatePO> queryWrapper = Wrappers.lambdaQuery(ProfitRatePO.class)
                .eq(ProfitRatePO::getLastJdCatId, changeDTO.getCatId()).eq(ProfitRatePO::getYn, YnEnum.YES.getCode());
        List<ProfitRatePO> dbLastCatIdProfitPoList = profitRateAtomicService.list(queryWrapper);
        if (CollectionUtils.isEmpty(dbLastCatIdProfitPoList)) {
            log.info("upgradeProfitRate 类目四级升级到三级,不需要复制四级的利润率到三级 四级类目ID:{} 三级类目ID:{}",changeDTO.getCatId(),changeDTO.getFidCatId());
            return;
        }

        log.info("upgradeProfitRate 类目四级升级到三级,复制四级的利润率到三级开始 四级类目ID:{} 三级类目ID:{}，四级利润率:[{}]",changeDTO.getCatId(),changeDTO.getFidCatId(),JSON.toJSONString(dbLastCatIdProfitPoList));
        // 查询只设置到三级的利润率
        LambdaQueryWrapper<ProfitRatePO> thirdQueryWrapper = Wrappers.lambdaQuery(ProfitRatePO.class)
                .eq(ProfitRatePO::getThirdJdCatId, changeDTO.getFidCatId())
                .isNull(ProfitRatePO::getLastJdCatId)
                .eq(ProfitRatePO::getYn, YnEnum.YES.getCode());
        List<ProfitRatePO> dbThirdProfitRatePoList = profitRateAtomicService.list(thirdQueryWrapper);
        // 三级的利润率不存在，插入一条新的数据
        if (CollectionUtils.isEmpty(dbThirdProfitRatePoList)) {
            List<ProfitRatePO> addProfitRatePoList = Lists.newArrayListWithExpectedSize(dbLastCatIdProfitPoList.size());
            for (ProfitRatePO dbLastCatProfitPo : dbLastCatIdProfitPoList){
                ProfitRatePO addProfitRatePo = copyProfitRatePOFromDbProfitRate(dbLastCatProfitPo,"id","updater","updateTime","lastCatId","lastJdCatId");
                addProfitRatePoList.add(addProfitRatePo);
            }

            if (CollectionUtils.isNotEmpty(addProfitRatePoList)) {
                profitRateAtomicService.saveOrUpdateBatch(addProfitRatePoList);
                log.info("upgradeProfitRate 类目四级升级到三级,复制四级的利润率到三级结束 四级类目ID:{} 三级类目ID:{}，三级利润率:[{}]",changeDTO.getCatId(),changeDTO.getFidCatId(),JSON.toJSONString(addProfitRatePoList));
            }
        }
    }

    private CategoryTaxPO copyCategoryTaxPOFromLastCatId(CategoryTaxPO dbLastCatTaxPo) {
        CategoryTaxPO addCategoryTaxPo = new CategoryTaxPO();
        BeanUtil.copyProperties(dbLastCatTaxPo, addCategoryTaxPo, "id","updater","updateTime","jdCatId","categoryId");
        addCategoryTaxPo.setCreator(Constant.SYSTEM+SYNC_CATEGORY_UPDATER);
        addCategoryTaxPo.setUpdater(Constant.SYSTEM+SYNC_CATEGORY_UPDATER);
        long millis = System.currentTimeMillis();
        addCategoryTaxPo.setCreateTime(millis);
        addCategoryTaxPo.setUpdateTime(millis);
        return addCategoryTaxPo;
    }

    private FulfillmentRatePO copyFulfillmentPOFromDbFulfillment(FulfillmentRatePO dbLastCatFulfillmentPo, String... ignoreProperties) {
        FulfillmentRatePO addFulfillmentPo = new FulfillmentRatePO();
        BeanUtil.copyProperties(dbLastCatFulfillmentPo, addFulfillmentPo, ignoreProperties);
        addFulfillmentPo.setCreator(Constant.SYSTEM+SYNC_CATEGORY_UPDATER);
        addFulfillmentPo.setUpdater(Constant.SYSTEM+SYNC_CATEGORY_UPDATER);
        Date date = new Date();
        addFulfillmentPo.setCreateTime(date);
        addFulfillmentPo.setUpdateTime(date);
        return addFulfillmentPo;
    }

    private ProfitRatePO copyProfitRatePOFromDbProfitRate(ProfitRatePO dbLastCatProfitPo, String... ignoreProperties) {
        ProfitRatePO addProfitRatePo = new ProfitRatePO();
        BeanUtil.copyProperties(dbLastCatProfitPo, addProfitRatePo, ignoreProperties);
        addProfitRatePo.setCreator(Constant.SYSTEM+SYNC_CATEGORY_UPDATER);
        addProfitRatePo.setUpdater(Constant.SYSTEM+SYNC_CATEGORY_UPDATER);
        Date date = new Date();
        addProfitRatePo.setCreateTime(date);
        addProfitRatePo.setUpdateTime(date);
        return addProfitRatePo;
    }


    /**
     * 根据GmsCategoryChangeDTO升级业务线关系。
     * @param changeDTO GmsCategoryChangeDTO对象，包含要升级的分类ID。
     */
    private void upgradeBusinessLineRelation(GmsCategoryChangeDTO changeDTO) {
        LambdaQueryWrapper<BusinessLineRelationPO> businessLineQueryWrapper = Wrappers.lambdaQuery(BusinessLineRelationPO.class)
                .select(BusinessLineRelationPO::getSupplierCode, BusinessLineRelationPO::getBrandId)
                .eq(BusinessLineRelationPO::getJdCatId, changeDTO.getCatId())
                .eq(BusinessLineRelationPO::getYn, YnEnum.YES.getCode());
        List<BusinessLineRelationPO> fourBusinessLineRelationPOList = businessLineRelationAtomicService.list(businessLineQueryWrapper);

        if (CollectionUtils.isNotEmpty(fourBusinessLineRelationPOList)) {
            log.info("upgradeBusinessLineRelation 将四级类目产品线升级到新末级（三级）类目 开始，changeDTO={}，四级类目产品线:[{}]", JSON.toJSONString(changeDTO),JSON.toJSONString(fourBusinessLineRelationPOList));
            List<BusinessLineRelationPO> addBusinessLineRelationPOList = Lists.newArrayList();
            for (BusinessLineRelationPO fourDbPO : fourBusinessLineRelationPOList) {
                // 查询三级产品线是否存在
                BusinessLineRelationPO existRelationPO = businessLineRelationAtomicService.getOne(Wrappers.lambdaQuery(BusinessLineRelationPO.class).eq(BusinessLineRelationPO::getSupplierCode, fourDbPO.getSupplierCode())
                        .eq(BusinessLineRelationPO::getBrandId, fourDbPO.getBrandId()).eq(BusinessLineRelationPO::getJdCatId, changeDTO.getFidCatId())
                        .eq(BusinessLineRelationPO::getYn, YnEnum.YES.getCode()));
                if (Objects.isNull(existRelationPO)){
                    BusinessLineRelationPO addBusinessLineRelationPO = getBusinessLineRelationPO(changeDTO, fourDbPO);
                    addBusinessLineRelationPOList.add(addBusinessLineRelationPO);
                }
            }

            if (CollectionUtils.isNotEmpty(addBusinessLineRelationPOList)) {
                businessLineRelationAtomicService.saveOrUpdateBatch(addBusinessLineRelationPOList);
            }
            log.info("upgradeBusinessLineRelation 将四级类目产品线升级到新末级（三级）类目 结束，changeDTO={} ,新增的产品线信息：[{}]", JSON.toJSONString(changeDTO),JSON.toJSONString(addBusinessLineRelationPOList));
        }
    }

    /**
     * 将指定父类目下的所有子类目都为禁用状态时，更新该父类目为叶子节点。
     * @param changeDTO 包含父类目ID的DTO对象
     * @return 是否成功更新为叶子节点
     */
    private boolean upgradeCategoryLeafNodeLevel(GmsCategoryChangeDTO changeDTO) {
        LambdaQueryWrapper<CategoryPO> queryWrapper = Wrappers.lambdaQuery(CategoryPO.class)
                .select(CategoryPO::getId, CategoryPO::getJdCatId, CategoryPO::getJdParentCatId, CategoryPO::getStatus, CategoryPO::getYn, CategoryPO::getCatLevel)
                .eq(CategoryPO::getJdParentCatId, changeDTO.getFidCatId())
                .eq(CategoryPO::getStatus, StatusEnum.ENABLE.getCode())
                .eq(CategoryPO::getYn, YnEnum.YES.getCode());
        List<CategoryPO> lastCategoryPOList = categoryAtomicService.list(queryWrapper);
        // 判断父级类目下所有自己都为禁用状态，不为空则跳过，为空则设置为叶子节点
        if (CollectionUtils.isNotEmpty(lastCategoryPOList)) {
            return false;
        }
        // 更新三级类目为末级
        LambdaUpdateWrapper<CategoryPO> updateThirdWrapper = Wrappers.lambdaUpdate(CategoryPO.class)
                .set(CategoryPO::getLeafNodeFlag, Boolean.TRUE)
                .set(CategoryPO::getUpdater, this.getOperator(changeDTO))
                .set(CategoryPO::getUpdateTime, new Date())
                .eq(CategoryPO::getJdCatId, changeDTO.getFidCatId());
        categoryAtomicService.update(updateThirdWrapper);

        return true;
    }

    private BusinessLineRelationPO getBusinessLineRelationPO(GmsCategoryChangeDTO changeDTO, BusinessLineRelationPO fourDbPO) {
        BusinessLineRelationPO addBusinessLineRelationPO = new BusinessLineRelationPO();
        addBusinessLineRelationPO.setCategoryId(changeDTO.getFidCatId());
        addBusinessLineRelationPO.setJdCatId(changeDTO.getFidCatId());
        addBusinessLineRelationPO.setSupplierCode(fourDbPO.getSupplierCode());
        addBusinessLineRelationPO.setBrandId(fourDbPO.getBrandId());
        addBusinessLineRelationPO.setYn(YnEnum.YES.getCode());
        addBusinessLineRelationPO.setCreator(this.getOperator(changeDTO));
        addBusinessLineRelationPO.setUpdater(this.getOperator(changeDTO));
        long now = System.currentTimeMillis();
        addBusinessLineRelationPO.setCreateTime(now);
        addBusinessLineRelationPO.setUpdateTime(now);
        addBusinessLineRelationPO.setGroupId(fourDbPO.getGroupId());
        return addBusinessLineRelationPO;
    }


    private DataResponse<Map<String, String>> translateMultiLang(GmsCategoryChangeDTO changeDTO,Set<String> langSet) {
        MultiTranslateReqDTO translateReqDTO = new MultiTranslateReqDTO();
        translateReqDTO.setText(changeDTO.getCatName());
        translateReqDTO.setFrom(LangConstant.LANG_ZH);
        translateReqDTO.setToLangList(langSet.stream().filter(lang -> !LangConstant.LANG_ZH.equals(lang)).collect(Collectors.toList()));
        return textTranslateManageService.translateMultiLangText(translateReqDTO);
    }

    private  List<CategoryLangPO> getUpdateCategoryLangPoList(GmsCategoryChangeDTO changeDTO, DataResponse<Map<String, String>> response, Map<String, CategoryLangPO> categoryLangPOMap) {
        Map<String, String> translatedMap = response.getData();
        List<CategoryLangPO> updateCategoryLangPoList = Lists.newArrayListWithExpectedSize(categoryLangPOMap.size());
        for (Map.Entry<String, CategoryLangPO> entry : categoryLangPOMap.entrySet()) {
            CategoryLangPO updatePo = this.getUpdateCategoryLangPO(changeDTO, entry, translatedMap);
            updateCategoryLangPoList.add(updatePo);
        }
        return updateCategoryLangPoList;
    }

    private  CategoryLangPO getUpdateCategoryLangPO(GmsCategoryChangeDTO changeDTO, Map.Entry<String, CategoryLangPO> entry, Map<String, String> translatedMap) {
        String lang = entry.getKey();
        CategoryLangPO dbCategoryLangPo = entry.getValue();
        CategoryLangPO updatePo = new CategoryLangPO();
        updatePo.setId(dbCategoryLangPo.getId());
        updatePo.setLang(dbCategoryLangPo.getLang());
        updatePo.setLangName(LangConstant.LANG_ZH.equals(lang) ? changeDTO.getCatName() : translatedMap.getOrDefault(lang,dbCategoryLangPo.getLangName()));
        updatePo.setUpdater(this.getOperator(changeDTO));
        updatePo.setUpdateTime(new Date());
        return updatePo;
    }

    private  List<CategoryLangPO> getInsertCategoryLangPoList(GmsCategoryChangeDTO changeDTO, DataResponse<Map<String, String>> response,List<String> langSet,Long catId) {
        Map<String, String> translatedMap = response.getData();
        List<CategoryLangPO> insertCategoryLangPoList = Lists.newArrayListWithExpectedSize(langSet.size());
        for (String lang : langSet) {
            String catName = LangConstant.LANG_ZH.equals(lang) ? changeDTO.getCatName() : translatedMap.getOrDefault(lang,changeDTO.getCatName());
            CategoryLangPO updatePo = this.getInsertCategoryLangPO(catId, lang, catName, changeDTO.getCatId());
            insertCategoryLangPoList.add(updatePo);
        }
        return insertCategoryLangPoList;
    }

    private CategoryLangPO getInsertCategoryLangPO(Long id,String lang,String catName,Long jdCatId) {
        CategoryLangPO insertPo = new CategoryLangPO();
        insertPo.setCatId(id);
        insertPo.setJdCatId(jdCatId);
        insertPo.setLang(lang);
        insertPo.setLangName(catName);
        insertPo.setCreator(Constant.SYSTEM+SYNC_CATEGORY_UPDATER);
        insertPo.setCreateTime(new Date());
        insertPo.setUpdater(Constant.SYSTEM+SYNC_CATEGORY_UPDATER);
        insertPo.setUpdateTime(new Date());
        insertPo.setYn(YnEnum.YES.getCode());
        insertPo.setPreYn(YnEnum.YES.getCode());
        return insertPo;
    }

    /**
     * 批量禁用分类状态。
     * @param input 包含要更新的分类ID和目标状态的CategoryVO对象。
     *               如果ID为空或目标状态为启用，则不执行任何操作。
     */
    private void batchForbiddenStatus(CategoryPO input, GmsCategoryChangeDTO changeDTO){
        if(input.getId() == null || Objects.equals(StatusEnum.ENABLE.getCode(),input.getStatus())){
            log.info("JdCategorySyncManageServiceImpl.batchUpdateStatus, id is null.or status is enable, categoryId={},status:{}", input.getId(), input.getStatus());
            return;
        }
        Long catId = input.getId();
        List<CategoryPO> categoryPOS = categoryAtomicService.queryAllChildById(catId);
        if (CollectionUtils.isEmpty(categoryPOS)) {
            log.info("JdCategorySyncManageServiceImpl.batchUpdateStatus, categoryPOS is null, categoryId={}", input.getId());
            return;
        }

        List<CategoryPO> updateCategoryPOS = new ArrayList<>();
        for (CategoryPO categoryPO : categoryPOS) {
            // 判断是否已经是禁用状态，是就跳过，不处理
            if(categoryPO.getStatus()!= null
                    && Objects.equals(StatusEnum.FORBIDDEN.getCode(),categoryPO.getStatus())){
                continue;
            }
            CategoryPO updateCategoryPO = new CategoryPO();
            updateCategoryPO.setId(categoryPO.getId());
            updateCategoryPO.setJdCatId(categoryPO.getJdCatId());
            updateCategoryPO.setStatus(input.getStatus());
            updateCategoryPO.setUpdater(this.getOperator(changeDTO));
            updateCategoryPO.setUpdateTime(DateUtil.date());
            updateCategoryPOS.add(updateCategoryPO);
        }

        if(CollectionUtils.isEmpty(updateCategoryPOS)){
            log.info("JdCategorySyncManageServiceImpl.batchUpdateStatus, updateCategoryPOS is null");
            return;
        }

        log.info("JdCategorySyncManageServiceImpl.batchUpdateStatus, updateCategoryPOS.size:{},ids:{}", updateCategoryPOS.size()
                , JSONObject.toJSONString(updateCategoryPOS.stream().map(CategoryPO::getId).collect(Collectors.toList())));
        categoryAtomicService.saveOrUpdateBatch(updateCategoryPOS);

        // 人工操作不发消息
        if (CategoryOperationSourceTypeEnum.ARTIFICIAL.equals(changeDTO.getOperationSourceType())) {
            return;
        }
        // 异步通知消息
        this.changeNotifyStatusAsync(updateCategoryPOS);
    }

    /**
     * 异步-通知新增类目
     * 防止占用主线程时长
     */
    private void insertNotifyAsync(CategoryPO insertPO) {
        if (insertPO == null) {
            log.info("JdCategorySyncManageServiceImpl.insertNotifyAsync: insertPO is empty.");
            return;
        }
        try {
            CompletableFuture.runAsync(() -> {
                this.insertNotify(insertPO);
            }, categoryNoticeExecutorService).exceptionally(throwable -> {throw new RuntimeException(throwable);});
        } catch (Exception e) {
            log.error("JdCategorySyncManageServiceImpl.insertNotifyAsync error, catId={}", insertPO.getJdCatId(), e);
        }
    }

    /**
     * 通知-新增子集类目
     */
    private void insertNotify(CategoryPO insertPO) {
        if (insertPO == null) {
            log.info("JdCategorySyncManageServiceImpl.insertNotify: insertPO is empty.");
            return;
        }
        try {
            List<Long> catIds = Arrays.asList(insertPO.getJdCatId(), insertPO.getJdParentCatId());
            Map<Long, Map<String, String>> categoryLangMap =
                    categoryLangAtomicService.queryNameByCatIdsAndLangCodes(catIds, Arrays.asList(LangConstant.LANG_ZH, LangConstant.LANG_EN));

            List<String> erps = this.getRoleErps(RoleEnum.PRODUCT_OPERATOR.getCode(), RoleEnum.STORE_MANAGER.getCode(),RoleEnum.TAX_BR.getCode());

            Map<String, String> langMap = categoryLangMap.get(insertPO.getJdCatId());
            Map<String, String> parentLangMap = categoryLangMap.get(insertPO.getJdParentCatId());

            if (MapUtils.isEmpty(langMap) || MapUtils.isEmpty(parentLangMap)) {
                log.warn("JdCategorySyncManageServiceImpl.insertNotify，多语言名称不存在，catId:{}, parent:{}",
                        insertPO.getJdCatId(), insertPO.getJdParentCatId());
                return;
            }

            String template = ConfigUtils.getStringByKey(msgNoticeTemplate, CategoryMsgNoticeEnum.CATEGORY_INSERT.name());
            if(StringUtils.isBlank(template)){
                log.warn("JdCategorySyncManageServiceImpl.insertNotify.消息体结构不存在 key={},msgNoticeTemplate={}",CategoryMsgNoticeEnum.CATEGORY_INSERT.name(),msgNoticeRoles);
                return;
            }

            String msg = String.format(template,
                    parentLangMap.get(LangConstant.LANG_ZH), parentLangMap.get(LangConstant.LANG_EN), insertPO.getJdParentCatId(),
                    langMap.get(LangConstant.LANG_ZH), insertPO.getJdCatId(),langMap.get(LangConstant.LANG_EN)
            );

            this.sendCategoryNotify(msg, insertPO.getJdCatId(), new HashSet<>(erps), "JdCategorySyncManageServiceImpl.insertNotify");

        } catch (Exception e) {
            log.error("JdCategorySyncManageServiceImpl.changeNotifyParent error, catId={}", insertPO.getJdCatId(), e);
            Profiler.businessAlarm(
                    UmpKeyConstant.JDI_ISC_CATEGORY_ALERT,
                    String.format("【%s】%s 类目新增类目消息通知异常,catId: %s, 异常信息:%s",
                            systemProfile, LevelCode.P1.getMessage(), insertPO.getJdCatId(), e.getMessage())
            );
        }
    }

    /**
     * 异步-通知父级变更
     * 防止占用主线程时长
     */
    private void changeNotifyParentAsync(CategoryPO updatePO, CategoryPO dbPO) {
        if (updatePO == null || dbPO == null) {
            log.info("JdCategorySyncManageServiceImpl.changeNotifyParentAsync: param is empty.");
            return;
        }
        try {
            CompletableFuture.runAsync(() -> {
                this.changeNotifyParent(updatePO,dbPO);
            }, categoryNoticeExecutorService).exceptionally(throwable -> {throw new RuntimeException(throwable);});
        } catch (Exception e) {
            log.error("JdCategorySyncManageServiceImpl.changeNotifyParent error, catId={}", dbPO.getJdCatId(), e);
        }
    }

    /**
     * 类目父类变更通知
     */
    private void changeNotifyParent(CategoryPO updatePO, CategoryPO dbPO) {
        if (updatePO == null || dbPO == null) {
            log.info("JdCategorySyncManageServiceImpl.changeNotifyParent: param is empty.");
            return;
        }
        try {
            List<Long> catIds = Arrays.asList(dbPO.getJdCatId(), dbPO.getJdParentCatId(), updatePO.getJdParentCatId());
            Map<Long, Map<String, String>> categoryLangMap =
                    categoryLangAtomicService.queryNameByCatIdsAndLangCodes(catIds, Arrays.asList(LangConstant.LANG_ZH, LangConstant.LANG_EN));

            List<String> erps = this.getRoleErps(RoleEnum.PRODUCT_OPERATOR.getCode(), RoleEnum.STORE_MANAGER.getCode());
            Set<Long> buyerCatIds = this.collectAllCatIds(dbPO.getJdCatId());
            Map<Long, List<String>> buyerMap = categoryBuyerRelationAtomicService.getBuyerByCatIds(buyerCatIds);

            Map<String, String> oldParentLangMap = categoryLangMap.get(dbPO.getJdParentCatId());
            Map<String, String> newParentLangMap = categoryLangMap.get(updatePO.getJdParentCatId());
            Map<String, String> langMap = categoryLangMap.get(dbPO.getJdCatId());

            if (MapUtils.isEmpty(oldParentLangMap) || MapUtils.isEmpty(newParentLangMap) || MapUtils.isEmpty(langMap)) {
                log.warn("JdCategorySyncManageServiceImpl.changeNotifyParent，多语言名称不存在，catId:{}, oldParent:{}, newParent:{}",
                        dbPO.getJdCatId(), dbPO.getJdParentCatId(), updatePO.getJdParentCatId());
                return;
            }

            String template = ConfigUtils.getStringByKey(msgNoticeTemplate, CategoryMsgNoticeEnum.PARENT_UPDATE.name());
            if(StringUtils.isBlank(template)){
                log.warn("JdCategorySyncManageServiceImpl.changeNotifyParent.消息体结构不存在 key={},msgNoticeTemplate={}",CategoryMsgNoticeEnum.PARENT_UPDATE.name(),msgNoticeRoles);
                return;
            }

            String msg = String.format(template,
                    langMap.get(LangConstant.LANG_ZH), langMap.get(LangConstant.LANG_EN), dbPO.getJdCatId(),
                    oldParentLangMap.get(LangConstant.LANG_ZH), oldParentLangMap.get(LangConstant.LANG_EN), dbPO.getJdParentCatId(),
                    newParentLangMap.get(LangConstant.LANG_ZH), newParentLangMap.get(LangConstant.LANG_EN), updatePO.getJdParentCatId()
            );

            Set<String> receivers = this.mergeReceivers(MapUtils.isEmpty(buyerMap)?null:buyerMap.values().stream()
                            .filter(list -> list != null && !list.isEmpty())
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList())
                    , erps);
            this.sendCategoryNotify(msg, dbPO.getJdCatId(), receivers, "JdCategorySyncManageServiceImpl.changeNotifyParent");
        } catch (Exception e) {
            log.error("JdCategorySyncManageServiceImpl.changeNotifyParent error, catId={}", dbPO.getJdCatId(), e);
            Profiler.businessAlarm(
                    UmpKeyConstant.JDI_ISC_CATEGORY_ALERT,
                    String.format("【%s】%s 类目父类变更消息通知异常,catId: %s, 异常信息:%s",
                            systemProfile, LevelCode.P1.getMessage(), dbPO.getJdCatId(), e.getMessage())
            );
        }
    }

    /**
     * 异步-通知名称
     * 防止占用主线程时长
     * @param updateLangList 需要修改通知状态的类别列表
     */
    private void changeNotifyNameAsync(List<CategoryLangPO> updateLangList, Map<String, CategoryLangPO> dbLangMap) {
        if (CollectionUtils.isEmpty(updateLangList) || MapUtils.isEmpty(dbLangMap)) {
            log.info("JdCategorySyncManageServiceImpl.changeNotifyNameAsync: params is empty.");
            return;
        }
        try {
             CompletableFuture.runAsync(() -> {
                this.changeNotifyName(updateLangList,dbLangMap);
            }, categoryNoticeExecutorService).exceptionally(throwable -> {throw new RuntimeException(throwable);});
        } catch (Exception e) {
            log.error("JdCategorySyncManageServiceImpl.changeNotifyName error, updateLangList={}", JSONObject.toJSONString(updateLangList), e);
        }
    }

    /**
     * 类目中文名称变更通知
     */
    private void changeNotifyName(List<CategoryLangPO> updateLangList, Map<String, CategoryLangPO> dbLangMap) {
        if (CollectionUtils.isEmpty(updateLangList) || MapUtils.isEmpty(dbLangMap)) {
            log.info("JdCategorySyncManageServiceImpl.changeNotifyName: params is empty.");
            return;
        }

        CategoryLangPO zhCategoryLangPO = updateLangList.stream()
                .filter(item -> StringUtils.equals(LangConstant.LANG_ZH, item.getLang()))
                .findFirst().orElse(null);

        if (zhCategoryLangPO == null) {
            log.info("JdCategorySyncManageServiceImpl.changeNotifyName: no zh entry.");
            return;
        }

        try {
            // id -> dbLangPO
            Map<Long, CategoryLangPO> dbMap = dbLangMap.values().stream()
                    .collect(Collectors.toMap(CategoryLangPO::getId, Function.identity()));

            List<String> erps = this.getRoleErps(RoleEnum.PRODUCT_OPERATOR.getCode());
            Long catId = dbLangMap.values().stream().findFirst().map(CategoryLangPO::getJdCatId).orElse(null);
            if (catId == null) {
                log.warn("JdCategorySyncManageServiceImpl.changeNotifyName: catId is null.");
                return;
            }
            Set<Long> buyerCatIds = collectAllCatIds(catId);
            Map<Long, List<String>> buyerMap = categoryBuyerRelationAtomicService.getBuyerByCatIds(buyerCatIds);

            CategoryLangPO dbCategoryLangPO = dbMap.get(zhCategoryLangPO.getId());
            if (dbCategoryLangPO == null) {
                log.warn("JdCategorySyncManageServiceImpl.changeNotifyName：未找到db记录，id={}", zhCategoryLangPO.getId());
                return;
            }
            if (Objects.equals(dbCategoryLangPO.getLangName(), zhCategoryLangPO.getLangName())) {
                return;
            }

            String oldCatNameZh = Optional.ofNullable(dbLangMap.get(LangConstant.LANG_ZH)).map(CategoryLangPO::getLangName).orElse("");
            String oldCatNameEn = Optional.ofNullable(dbLangMap.get(LangConstant.LANG_EN)).map(CategoryLangPO::getLangName).orElse("");

            String template = ConfigUtils.getStringByKey(msgNoticeTemplate, CategoryMsgNoticeEnum.NAME_UPDATE.name());
            if(StringUtils.isBlank(template)){
                log.warn("JdCategorySyncManageServiceImpl.changeNotifyName.消息体结构不存在 key={},msgNoticeTemplate={}",CategoryMsgNoticeEnum.NAME_UPDATE.name(),msgNoticeRoles);
                return;
            }

            String msg = String.format(template, oldCatNameZh, oldCatNameEn, catId, zhCategoryLangPO.getLangName());

            Set<String> receivers = this.mergeReceivers(MapUtils.isEmpty(buyerMap)?null:buyerMap.get(catId), erps);
            this.sendCategoryNotify(msg, catId, receivers, "JdCategorySyncManageServiceImpl.changeNotifyName");
        } catch (Exception e) {
            log.error("JdCategorySyncManageServiceImpl.changeNotifyName error, id={}", zhCategoryLangPO.getId(), e);
            Profiler.businessAlarm(
                    UmpKeyConstant.JDI_ISC_CATEGORY_ALERT,
                    String.format("【%s】%s 类目名称变更消息通知异常,catId: %s, 异常信息:%s",
                            systemProfile, LevelCode.P1.getMessage(), zhCategoryLangPO.getId(), e.getMessage())
            );
        }
    }

    /**
     * 异步-通知状态
     * 防止占用主线程时长
     * @param params 需要修改通知状态的类别列表
     */
    private void changeNotifyStatusAsync(List<CategoryPO> params) {
        if (CollectionUtils.isEmpty(params)) {
            log.info("JdCategorySyncManageServiceImpl.changeNotifyStatusAsync: params is empty.");
            return;
        }
        try {
             CompletableFuture.runAsync(() -> {
                this.changeNotifyStatus(params);
            }, categoryNoticeExecutorService).exceptionally(throwable -> {throw new RuntimeException(throwable);});
        } catch (Exception e) {
            log.error("JdCategorySyncManageServiceImpl.changeNotifyStatusAsync error, params={}", JSONObject.toJSONString(params), e);
        }
    }

    /**
     * 类目状态变更通知
     */
    private void changeNotifyStatus(List<CategoryPO> params) {
        if (CollectionUtils.isEmpty(params)) {
            log.info("JdCategorySyncManageServiceImpl.changeNotifyStatus: params is empty.");
            return;
        }
        try {
            List<Long> catIds = params.stream().map(CategoryPO::getJdCatId).collect(Collectors.toList());
            Map<Long, Map<String, String>> categoryLangMap =
                    categoryLangAtomicService.queryNameByCatIdsAndLangCodes(catIds, Arrays.asList(LangConstant.LANG_ZH, LangConstant.LANG_EN));

            List<String> erps = this.getRoleErps(RoleEnum.PRODUCT_OPERATOR.getCode());
            Map<Long, List<String>> buyerMap = categoryBuyerRelationAtomicService.getBuyerByCatIds(catIds);

            for (CategoryPO categoryPO : params) {
                try {
                    Long catId = categoryPO.getJdCatId();
                    Map<String, String> langMap = categoryLangMap.get(catId);
                    if (MapUtils.isEmpty(langMap)) {
                        log.warn("JdCategorySyncManageServiceImpl.changeNotifyStatus，多语言名称不存在，catId:{}", catId);
                        continue;
                    }
                    String catNameZh = langMap.get(LangConstant.LANG_ZH);
                    String catNameEn = langMap.get(LangConstant.LANG_EN);

                    String msg = null;
                    if (StatusEnum.ENABLE.getCode().equals(categoryPO.getStatus())) {
                        String template = ConfigUtils.getStringByKey(msgNoticeTemplate, CategoryMsgNoticeEnum.STATUS_ENABLE_UPDATE.name());
                        if(StringUtils.isBlank(template)){
                            log.warn("JdCategorySyncManageServiceImpl.changeNotifyStatus.STATUS_ENABLE_UPDATE.消息体结构不存在 key={},msgNoticeTemplate={}",CategoryMsgNoticeEnum.STATUS_ENABLE_UPDATE.name(),msgNoticeRoles);
                            return;
                        }
                        msg = String.format(template, catNameZh, catNameEn, catId, StatusEnum.ENABLE.getDesc());
                    } else if (StatusEnum.FORBIDDEN.getCode().equals(categoryPO.getStatus())) {
                        String template = ConfigUtils.getStringByKey(msgNoticeTemplate, CategoryMsgNoticeEnum.STATUS_FORBIDDEN_UPDATE.name());
                        if(StringUtils.isBlank(template)){
                            log.warn("JdCategorySyncManageServiceImpl.changeNotifyStatus.STATUS_FORBIDDEN_UPDATE.消息体结构不存在 key={},msgNoticeTemplate={}",CategoryMsgNoticeEnum.STATUS_FORBIDDEN_UPDATE.name(),msgNoticeRoles);
                            return;
                        }
                        msg = String.format(template, catNameZh, catNameEn, catId, StatusEnum.FORBIDDEN.getDesc());
                    }

                    Set<String> receivers = this.mergeReceivers(MapUtils.isEmpty(buyerMap)?null:buyerMap.get(catId), erps);
                    this.sendCategoryNotify(msg, catId, receivers, "JdCategorySyncManageServiceImpl.changeNotifyStatus");

                } catch (Exception e) {
                    log.error("JdCategorySyncManageServiceImpl.changeNotifyStatus error, catId={}", categoryPO.getJdCatId(), e);
                    Profiler.businessAlarm(
                            UmpKeyConstant.JDI_ISC_CATEGORY_ALERT,
                            String.format("【%s】%s 类目状态变更消息通知异常,catId: %s, 异常信息:%s",
                                    systemProfile, LevelCode.P1.getMessage(), categoryPO.getJdCatId(), e.getMessage())
                    );
                }
            }
        }catch (Exception e){
            log.error("JdCategorySyncManageServiceImpl.changeNotifyStatus error, params={}", JSONObject.toJSONString(params), e);
            Profiler.businessAlarm(
                    UmpKeyConstant.JDI_ISC_CATEGORY_ALERT,
                    String.format("【%s】%s 类目状态变更消息通知异常,catId: %s, 异常信息:%s",
                            systemProfile, LevelCode.P1.getMessage(), "一堆子类目都未通知", e.getMessage())
            );
        }
    }

    /**
     * 合并采购员和运营角色ERP
     */
    private Set<String> mergeReceivers(List<String> buyerValues, List<String> erps) {
        Set<String> receivers = new LinkedHashSet<>();
        if (CollectionUtils.isNotEmpty(buyerValues)) {
            receivers.addAll(buyerValues);
        }
        if (CollectionUtils.isNotEmpty(erps)) {
            receivers.addAll(erps);
        }
        return receivers;
    }

    /**
     * 获取角色ERP列表
     */
    private List<String> getRoleErps(String... roleKeys) {
        List<String> roleList = new ArrayList<>();
        for (String key : roleKeys) {
            String roles = ConfigUtils.getStringByKey(msgNoticeRoles, key);
            if (StringUtils.isNotBlank(roles)) {
                roleList.addAll(Arrays.asList(roles.split(",")));
            }
        }
        return this.getReceivers(roleList);
    }

    /**
     * 获取类目及所有下级类目的catId集合
     */
    private Set<Long> collectAllCatIds(Long rootCatId) {
        Set<Long> result = new HashSet<>();
        result.add(rootCatId);
        List<CategoryPO> categoryPOS = categoryAtomicService.queryAllChildById(rootCatId);
        if(CollectionUtils.isEmpty(categoryPOS)){
            return result;
        }
        Set<Long> catIds = categoryPOS.stream()
                .map(CategoryPO::getJdCatId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if(CollectionUtils.isNotEmpty(catIds)){
            result.addAll(catIds);
        }
        return result;
    }

    /**
     * 通用消息发送校验
     */
    private void sendCategoryNotify(String msg, Long catId, Set<String> receivers, String logPrefix) {
        if (StringUtils.isBlank(msg)) {
            log.warn("{} 消息内容为空，catId:{}", logPrefix, catId);
            return;
        }
        if (CollectionUtils.isEmpty(receivers)) {
            log.warn("{} 无消息接收人，catId:{}", logPrefix, catId);
            return;
        }
        this.sendDongDongMessage(msg, new ArrayList<>(receivers));
    }

    /**
     * 根据角色资源代码列表获取接收者ERP列表
     * @param roleResourceCodeList 角色资源代码列表
     * @return 接收者ERP列表
     */
    private List<String> getReceivers(List<String> roleResourceCodeList){
        if(CollectionUtils.isEmpty(roleResourceCodeList)){
            return Collections.emptyList();
        }
        RoleListRequest request = new RoleListRequest();
        request.setRoleCodeList(roleResourceCodeList);
        Response<List<UserDto>> response = authRpcService.getUserByRoleList(request);
        if (Objects.isNull(response) || !"SUCCESS".equals(response.getCode()) || CollectionUtils.isEmpty(response.getData())) {
            log.warn("JdCategorySyncManageServiceImpl.getReceivers 获取角色用户失败,request:{},response={}",JSON.toJSONString(request), JSON.toJSONString(response));
            return Collections.emptyList();
        }

        List<UserDto> data = response.getData();
        return Optional.ofNullable(data).orElse(Collections.emptyList()).stream().map(UserDto::getErp).collect(Collectors.toList());
    }


    private String getOperator(GmsCategoryChangeDTO changeDTO) {
        return StringUtils.isNotBlank(changeDTO.getOperator()) ? changeDTO.getOperator() : Constant.SYSTEM + SYNC_CATEGORY_UPDATER;
    }
}
