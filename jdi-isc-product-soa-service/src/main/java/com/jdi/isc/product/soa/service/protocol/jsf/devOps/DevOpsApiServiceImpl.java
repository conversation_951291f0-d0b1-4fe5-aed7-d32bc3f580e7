package com.jdi.isc.product.soa.service.protocol.jsf.devOps;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyCancelApiDTO;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.CustomerSkuDataSourceTypeEnums;
import com.jdi.isc.product.soa.api.devOps.DevOpsApiService;
import com.jdi.isc.product.soa.api.devOps.req.DevCustomerMkuStatusQueryDTO;
import com.jdi.isc.product.soa.api.pricewarn.enums.PriceWarnTypeEnum;
import com.jdi.isc.product.soa.api.sku.req.QuerySkuReqDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuFeatureApiDTO;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.taxRate.req.TaxRateReqDTO;
import com.jdi.isc.product.soa.api.wimp.brand.req.BrandApiDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.AttributeDimensionEnum;
import com.jdi.isc.product.soa.common.enums.AttributeInputTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.util.AESIVUtil;
import com.jdi.isc.product.soa.domain.brand.biz.BrandVO;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerMku.biz.CustomerMkuStatusNumResVO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDraftPO;
import com.jdi.isc.product.soa.domain.enums.AuditLevelEnum;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum;
import com.jdi.isc.product.soa.domain.mku.po.MkuLangPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.biz.FulfillmentRateVO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuStockRelationVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuFeaturePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.*;
import com.jdi.isc.product.soa.domain.stock.po.StockLogPO;
import com.jdi.isc.product.soa.domain.stock.po.StockPO;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRateVO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehousePO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehouseSkuPO;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeFlatVO;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeValueFlatVO;
import com.jdi.isc.product.soa.rpc.b2b.ware.BWareReadRpcService;
import com.jdi.isc.product.soa.rpc.gms.CategoryRpcService;
import com.jdi.isc.product.soa.rpc.joySky.JoySkyApproveRpcService;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuFeatureAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.*;
import com.jdi.isc.product.soa.service.atomic.stock.StockAtomicService;
import com.jdi.isc.product.soa.service.atomic.stocklog.StockLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseSkuAtomicService;
import com.jdi.isc.product.soa.service.manage.attribute.ExtAttributeLangService;
import com.jdi.isc.product.soa.service.manage.brand.BrandManageService;
import com.jdi.isc.product.soa.service.manage.brand.xbp.XbpTicketBrandService;
import com.jdi.isc.product.soa.service.manage.category.GlobalAttributeManageService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.customs.ProductCustomInfoService;
import com.jdi.isc.product.soa.service.manage.price.FulfillmentRateManageService;
import com.jdi.isc.product.soa.service.manage.pricewarn.manager.PriceWarnManagerService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.ProductToolService;
import com.jdi.isc.product.soa.service.manage.spu.SpuDraftManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuReadManageService;
import com.jdi.isc.product.soa.service.manage.tool.SkuStockToolService;
import com.jdi.isc.product.soa.service.manage.taxRate.TaxRateManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.BrSkuTaxManageService;
import com.jdi.isc.product.soa.service.mapstruct.ProductAttributeConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SkuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.brand.BrandConvert;
import com.jdi.isc.product.soa.service.mapstruct.taxRate.TaxRateConvert;
import com.jdi.isc.product.soa.service.manage.attribute.AttributeOutService;
import com.jdi.isc.product.soa.common.util.JimUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.Constant.COMMA;

/**
 * 研发使用接口实现
 *
 * <AUTHOR>
 * @date 2024/7/29
 **/
@Slf4j
@Service
public class DevOpsApiServiceImpl implements DevOpsApiService, InitializingBean {


    @Resource
    private WarehouseAtomicService warehouseAtomicService;

    @Resource
    private XbpTicketBrandService xbpTicketBrandService;

    @Resource
    private StockAtomicService stockAtomicService;
    @Resource
    private StockLogAtomicService stockLogAtomicService;
    @Resource
    private WarehouseSkuAtomicService warehouseSkuAtomicService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private SpuDraftManageService spuDraftManageService;
    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;
    @Resource
    private SkuDraftAtomicService skuDraftAtomicService;
    @Resource
    private SkuFeatureAtomicService skuFeatureAtomicService;

    @Resource
    private BrandManageService brandManageService;
    @Resource
    private JoySkyApproveRpcService joySkyApproveRpcService;
    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private SkuPriceAtomicService skuPriceAtomicService;
    @Resource
    private SpuAuditRecordAtomicService spuAuditRecordAtomicService;
    @Resource
    private CategoryRpcService categoryRpcService;
    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private ProductAttributeConvertService productAttributeConvertService;
    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;
    @Resource
    private SpuConvertService spuConvertService;
    @Resource
    private SkuConvertService skuConvertService;
    @Resource
    private GlobalAttributeManageService globalAttributeManageService;
    @Resource
    private AsyncTaskExecutor dataHandleExecutor;
    @Resource
    private SkuStockToolService skuStockToolService;
    @Resource
    private CustomerSkuPriceDraftAtomicService customerSkuPriceDraftAtomicService;
    @Resource
    private ProductToolService productToolService;
    @Resource
    private SkuReadManageService skuReadManageService;
    @Resource
    private FulfillmentRateManageService fulfillmentRateManageService;
    @Resource
    private ExtAttributeLangService extAttributeLangService;
    @Resource
    private BrSkuTaxManageService brSkuTaxManageService;

    @Autowired
    private ProductCustomInfoService productCustomInfoService;

    @Resource
    private AttributeOutService attributeOutService;

    @Resource
    private JimUtils jimUtils;

    @Resource
    private PriceWarnManagerService priceWarnManagerService;

    @Override
    @ToolKit
    public DataResponse<String> removeWarehouse(Set<Long> ids, String env) {

        if (StringUtils.isNotBlank(env) && !"uat_env".equals(env)){
            return DataResponse.error("环境标识错误");
        }

        if (CollectionUtils.isEmpty(ids)) {
            warehouseAtomicService.remove(Wrappers.lambdaQuery(WarehousePO.class).in(WarehousePO::getYn, Arrays.stream(YnEnum.values()).map(YnEnum::getCode)));
        }else {
            warehouseAtomicService.remove(Wrappers.lambdaQuery(WarehousePO.class).in(WarehousePO::getId,ids));
        }
        return DataResponse.success();
    }

    @Override
    @ToolKit
    public DataResponse<String> addBrandXbp(BrandApiDTO brandApiDTO) {
        BrandVO brandVO = BrandConvert.INSTANCE.apiDto2Vo(brandApiDTO);
        DataResponse<Integer> approval = xbpTicketBrandService.createBrandXbpForApproval(brandVO);
        log.info("DevOpsApiServiceImpl.addBrandXbp  brandApiDTO={},approval={}", JSON.toJSONString(brandApiDTO),approval);
        return  approval.getSuccess() ? DataResponse.success() : DataResponse.error(approval.getMessage());
    }

    @Override
    @ToolKit
    public DataResponse<String> updateStockForWarehouse(StockItemManageReqDTO reqDTO) {
        LambdaQueryWrapper<StockPO> queryWrapper = Wrappers.lambdaQuery(StockPO.class).eq(StockPO::getSkuId, reqDTO.getSkuId())
                .eq(StockPO::getYn,YnEnum.YES.getCode());
        StockPO stockPO = stockAtomicService.getOne(queryWrapper);
        if (Objects.isNull(stockPO)) {
            return DataResponse.error("库存不存在");
        }

        stockPO.setWarehouseId(reqDTO.getWarehouseId());
        stockPO.setAvailableStock(reqDTO.getNum());
        stockPO.setStock(stockPO.getOccupy()+reqDTO.getNum());
        stockPO.setUpdateTime(new Date());
        boolean saved = stockAtomicService.saveOrUpdate(stockPO);
        return saved ? DataResponse.success() : DataResponse.error("修改失败");
    }

    @Override
    public DataResponse<String> modifyBrandXbp(BrandApiDTO brandApiDTO) {
        BrandVO brandVO = BrandConvert.INSTANCE.apiDto2Vo(brandApiDTO);
        BrandVO originBrandVO = brandManageService.detail(brandVO);
        DataResponse<Integer> approval = xbpTicketBrandService.modifyBrandXbpForApproval(brandVO,originBrandVO);
        log.info("DevOpsApiServiceImpl.modifyBrandXbp  brandApiDTO={},approval={}", JSON.toJSONString(brandApiDTO),approval);
        return  approval.getSuccess() ? DataResponse.success() : DataResponse.error(approval.getMessage());
    }
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> removeWrongStockInfo(Set<Long> spuIds, String env) {
        if (StringUtils.isNotBlank(env) && !"uat_env".equals(env)){
            return DataResponse.error("环境标识错误");
        }
        // 查询所有spu和sku关系
        Map<Long, List<SkuPO>> spuSkuMap = skuAtomicService.querySkuListBySpuIds(spuIds);
        if (MapUtils.isEmpty(spuSkuMap)) {
            return DataResponse.success("商品信息不存在");
        }

        spuSkuMap.forEach((spuId,skuList)->{
            log.info("removeWrongStockInfo 删除当前spu的备货库存信息 spuId={}",spuId);
            Set<Long> skuIds = skuList.stream().map(SkuPO::getSkuId).collect(Collectors.toSet());
            List<WarehouseSkuPO> warehouseSkuPOList = warehouseSkuAtomicService.list(Wrappers.lambdaQuery(WarehouseSkuPO.class).in(WarehouseSkuPO::getSkuId, skuIds));
            // 删除仓库和sku绑定关系
            if (CollectionUtils.isNotEmpty(warehouseSkuPOList)){
                boolean remove = warehouseSkuAtomicService.remove(Wrappers.lambdaQuery(WarehouseSkuPO.class).in(WarehouseSkuPO::getSkuId, skuIds));
                log.info("removeWrongStockInfo 删除备货仓和sku关系成功 spuId={} skuIds={},remove={}",spuId,JSON.toJSONString(skuIds),remove);
            }
            // 删除库存
            List<StockPO> stockPOList = stockAtomicService.list(Wrappers.lambdaQuery(StockPO.class).in(StockPO::getSkuId, skuIds));
            if (CollectionUtils.isNotEmpty(stockPOList)){
                stockAtomicService.remove(Wrappers.lambdaQuery(StockPO.class).in(StockPO::getSkuId, skuIds));
                log.info("removeWrongStockInfo 删除备货仓库存完成 spuId={} skuIds={}",spuId,JSON.toJSONString(skuIds));
            }
            // 删除库存日志
            List<StockLogPO> stockLogList = stockLogAtomicService.list(Wrappers.lambdaQuery(StockLogPO.class).in(StockLogPO::getSkuId, skuIds));
            if (CollectionUtils.isNotEmpty(stockLogList)) {
                stockLogAtomicService.remove(Wrappers.lambdaQuery(StockLogPO.class).in(StockLogPO::getSkuId, skuIds));
                log.info("removeWrongStockInfo 删除备货仓库存日志完成 spuId={} skuIds={}",spuId,JSON.toJSONString(skuIds));
            }

            // 更新草稿，删除不需要的仓信息
            SaveSpuVO saveSpuVo = spuDraftManageService.getSaveSpuVoFromDraftBySpuId(spuId);
            if (Objects.nonNull(saveSpuVo) && CollectionUtils.isNotEmpty(saveSpuVo.getSkuVOList())) {
                log.info("removeWrongStockInfo 更新草稿开始 spuId={} ",spuId );
                boolean existWarehouse = false;
                List<SkuVO> skuVOList = saveSpuVo.getSkuVOList();
                for (SkuVO skuVO : skuVOList) {
                    if (CollectionUtils.isNotEmpty(skuVO.getSkuStockRelationList())) {
                        existWarehouse = skuVO.getSkuStockRelationList().stream().anyMatch(vo -> !Constant.FACTORY_DEFAULT_ID_STR.equals(vo.getWarehouseId()));

                        List<SkuStockRelationVO> skuStockRelationVOList = Lists.newArrayList();
                        for (SkuStockRelationVO skuStockRelationVO : skuVO.getSkuStockRelationList()) {
                            if (Constant.FACTORY_DEFAULT_ID_STR.equals(skuStockRelationVO.getWarehouseId())) {
                                skuStockRelationVOList.add(skuStockRelationVO);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(skuStockRelationVOList)) {
                            skuVO.setSkuStockRelationList(skuStockRelationVOList);
                        }
                    }
                }
                // 更新草稿
                if (existWarehouse){
                    spuDraftManageService.saveOrUpdateDraft(saveSpuVo);
                    log.info("removeWrongStockInfo 更新草稿 spuId={} ",spuId );
                }
                log.info("removeWrongStockInfo 更新草稿结束 spuId={} ",spuId );
            }
        });
        return DataResponse.success("成功");
    }

    /**
     * 从指定环境中删除错误的股票数据。
     * @param ids 需要删除的股票ID集合。
     * @param type 股票类型。1:SKU和-1仓关联 2：SKU仓为-1库存 3：SKU仓-1库存日志
     * @param env 环境标识。
     * @return 删除操作的结果。
     */
    @Override
    public DataResponse<String> removeWrongStock(Set<Long> ids, Integer type, String env) {
        if (StringUtils.isNotBlank(env) && !"uat_env".equals(env)){
            return DataResponse.error("环境标识错误");
        }

        if (type == 1){
            warehouseSkuAtomicService.removeBatchByIds(ids);
            log.info("removeWrongStock 删除SKU和-1绑定关系完成 ids={}", ids);
        }else if (type == 2){
            stockAtomicService.removeBatchByIds(ids);
            log.info("removeWrongStock 删除SKU和-1仓库存完成 ids={}", ids);
        }else if (type ==3){
            stockLogAtomicService.removeBatchByIds(ids);
            log.info("removeWrongStock 删除SKU和-1仓库存日志完成 ids={}", ids);
        }
        return DataResponse.success();
    }

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @Resource
    private SpuLangAtomicService spuLangAtomicService;

    @Resource
    private MkuLangAtomicService mkuLangAtomicService;

    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;

    @Override
    public DataResponse<List<Long>> spuLang2mkuLang(List<Long> mkuIds,String lang) {
        log.info("DevOpsApiServiceImpl spuLang2mkuLang mkuIds:{},lang:{}",JSON.toJSONString(mkuIds),lang);
        List<MkuRelationPO> mkuRelationPOS = mkuRelationAtomicService.queryBindListByMkuIds(mkuIds);
        Map<Long, MkuLangPO> mkuIdMkuLang = mkuLangAtomicService.getMkuLangNameByMkuIds(mkuIds, lang);
        Map<Long, Long> mkuSkuMap = mkuRelationPOS.stream().collect(Collectors.toMap(MkuRelationPO::getMkuId, MkuRelationPO::getSkuId));
        Map<Long, Long> skuSpuMap = skuAtomicService.getSkuSpuMapBySkuIds(new HashSet<>( mkuSkuMap.values()));
        Map<Long, SpuLangPO> spuIdSpuLangMap = spuLangAtomicService.getSpuLangNameBySpuIds(new ArrayList<>(skuSpuMap.values()), lang);

        List<MkuLangPO> updateMkuList  = new ArrayList<>();
        List<Long> mkuIdList = new ArrayList<>();
        log.info("DevOpsApiServiceImpl spuLang2mkuLang mkuIdMkuLang:{}",JSON.toJSONString(mkuIdMkuLang.values()));
        mkuIdMkuLang.values().forEach(mkuLangPO -> {
            if(StringUtils.isBlank(mkuLangPO.getMkuTitle())){
                Long skuId = mkuSkuMap.get(mkuLangPO.getMkuId());
                Long spuId = skuSpuMap.get(skuId);
                SpuLangPO spuLangPO = spuIdSpuLangMap.get(spuId);
                if(Objects.nonNull(spuLangPO)){
                    mkuIdList.add(mkuLangPO.getMkuId());
                    mkuLangPO.setMkuTitle(spuLangPO.getSpuTitle());
                    mkuLangPO.setRemark("spuLangTitle同步到mkuLangTitle");
                    mkuLangPO.setUpdateTime(new Date());
                    mkuLangPO.setMkuId(null);
                    mkuLangPO.setLang(null);
                    updateMkuList.add(mkuLangPO);
                }
            }
        });

        boolean updateMku = mkuLangAtomicService.updateBatchById(updateMkuList);
        log.info("DevOpsApiServiceImpl spuLang2mkuLang updateMku:{},result:{}",JSON.toJSONString(updateMkuList),updateMku);


        Map<Long, Long> spuMkuMap = customerMkuAtomicService.getSpuMkuMapBySpuIds(skuSpuMap.values());
        List<MkuLangPO> createMkuList  = new ArrayList<>();
        spuIdSpuLangMap.values().forEach(spuLangPO -> {
            if(spuMkuMap.containsKey(spuLangPO.getSpuId())){
                Long mku = spuMkuMap.get(spuLangPO.getSpuId());
                if(!mkuIdMkuLang.containsKey(mku)){
                    MkuLangPO mkuLangPO = new MkuLangPO();
                    mkuLangPO.setMkuTitle(spuLangPO.getSpuTitle());
                    mkuLangPO.setLang(spuLangPO.getLang());
                    mkuLangPO.setCreator("wangpeng965");
                    mkuLangPO.setUpdater("wangpeng965");
                    mkuLangPO.setCreateTime(new Date());
                    mkuLangPO.setUpdateTime(new Date());
                    mkuLangPO.setRemark("spuLang信息同步到mkuLang");
                    createMkuList.add(mkuLangPO);
                }
            }
        } );
        log.info("DevOpsApiServiceImpl spuLang2mkuLang createMkuList:{},result:{}",JSON.toJSONString(createMkuList),"未执行");

        return DataResponse.success(mkuIdList);
    }


    @Resource
    private MkuAtomicService mkuAtomicService;

    @Override
    public DataResponse<List<Long>> updateMkuStatus(List<Long> mkuIds, Integer status){
        log.info("DevOpsApiServiceImpl updateMkuStatus mkuIds:{},status:{}",JSON.toJSONString(mkuIds),status);
        List<MkuPO> mkuPOList = mkuAtomicService.listByMkuIds(mkuIds);
        mkuPOList.forEach(mkuPO -> {
            mkuPO.setMkuStatus(status);
            mkuPO.setRemark("更新mku状态");
            mkuPO.setUpdateTime(new Date());
        } );
        boolean result = mkuAtomicService.updateBatchById(mkuPOList);
        log.info("DevOpsApiServiceImpl updateMkuStatus mkuPOList:{},result:{}",JSON.toJSONString(mkuPOList),result);
        return DataResponse.success(mkuIds);
    }

    @Override
    public DataResponse<String> updateSkuFeature(Set<Long> skuIds, String env) {
        if (StringUtils.isBlank(env) || !"uat_env".equals(env)){
            return DataResponse.error("环境参数设置错误");
        }

        List<SkuPO> skuPOList = skuAtomicService.list(Wrappers.lambdaQuery(SkuPO.class).in(SkuPO::getSkuId, skuIds).eq(SkuPO::getYn,YnEnum.YES.getCode()));
        if (CollectionUtils.isEmpty(skuPOList)) {
            return DataResponse.error("商品都不存在");
        }

        List<SkuFeaturePO> skuFeaturePOList = Lists.newArrayList();
        for (SkuPO skuPO : skuPOList) {
            SkuFeaturePO skuFeaturePO = new SkuFeaturePO();
            BeanUtil.copyProperties(skuPO, skuFeaturePO,"createTime","updateTime");
            skuFeaturePO.setCreateTime(System.currentTimeMillis());
            skuFeaturePO.setUpdateTime(System.currentTimeMillis());
            skuFeaturePOList.add(skuFeaturePO);
        }

        log.info("updateSkuFeature 入参:[{}]",JSON.toJSONString(skuFeaturePOList));
        skuFeatureAtomicService.saveOrUpdateBatch(skuFeaturePOList);
        return DataResponse.success();
    }

    @Resource
    private SpuReadManageService spuReadManageService;

    @Override
    public DataResponse<String> createSkuDraftBySkuId(Set<Long> skuIds) {
        if(CollectionUtils.isEmpty(skuIds)){
            return null;
        }
        log.info("DevOpsApiServiceImpl createSkuDraftBySkuId skuIds:{}",JSON.toJSONString(skuIds));
        List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(skuIds);
        for (SkuPO skuPO : skuPOList){
            SpuDetailVO spuDetailVO = spuReadManageService.getDetailBySpuId(skuPO.getSpuId());
            if(!Integer.valueOf(AuditStatusEnum.WAITING_APPROVED.getCode()).equals(spuDetailVO.getSpuVO().getAuditStatus())){
                spuDetailVO.getSpuVO().setLevel(AuditLevelEnum.ZERO.getLevel());
            }
            spuDraftManageService.saveOrUpdateDraft(spuDetailVO);
            log.info("DevOpsApiServiceImpl createSkuDraftBySkuId skuIds:{},spuDetailVO:{}",JSON.toJSONString(skuPO.getSkuId()),JSON.toJSONString(spuDetailVO));
        }
        return DataResponse.success();
    }

    @Override
    public DataResponse<String> updateSkuStockYn(Set<String> skuAndId, String env,Integer yn) {
        if (StringUtils.isBlank(env) || !"uat_env".equals(env) || Objects.isNull(yn)){
            return DataResponse.error("环境参数设置错误");
        }

        for (String str : skuAndId) {
            String[] split = str.split(Constant.COMMA);
            String skuId = split[0];
            String warehouseId = split[1];
            StockPO stockPO = stockAtomicService.getOne(Wrappers.lambdaQuery(StockPO.class).eq(StockPO::getWarehouseId, warehouseId).eq(StockPO::getSkuId, skuId));
            if (Objects.isNull(stockPO)) {
                log.info("updateSkuStockYn 库存不存在 skuId={},warehouseId={}",skuId,warehouseId);
                continue;
            }
            if (yn.equals(stockPO.getYn())) {
                log.info("updateSkuStockYn 当前商品仓库状态已经是YN={} skuId={},warehouseId={}",yn, skuId, warehouseId);
                continue;
            }
            stockPO.setSkuId(null);
            stockPO.setYn(yn);
            stockPO.setUpdateTime(new Date());
            stockAtomicService.updateById(stockPO);
            log.info("updateSkuStockYn 更新库存状态成功 skuId={},warehouseId={}",skuId,warehouseId);
        }

        return DataResponse.success();
    }
    @Override
    public DataResponse<String> revokeJoySky(String erp, String processInstance,Integer joySkyType) {
        JoySkyCancelApiDTO joySkyCancelApiDTO = new JoySkyCancelApiDTO();
        joySkyCancelApiDTO.setJoySkyFlowType(joySkyType);
        joySkyCancelApiDTO.setErp(erp);
        joySkyCancelApiDTO.setProcessInstanceId(processInstance);
        joySkyCancelApiDTO.setCancelComment("系统取消");
        joySkyApproveRpcService.cancelSkyApprovalFlow(joySkyCancelApiDTO);
        return null;
    }

    @Override
    public DataResponse<String> updateSpuExtAttribute(Long spuId,String attribute){
        if(spuId == null || StringUtils.isBlank(attribute)){
            return DataResponse.error("更新失败,param");
        }
        SpuPO spuPO = spuAtomicService.getSpuPoBySpuId(spuId);
        if(spuPO == null){
            return DataResponse.error("更新失败,spuPO");
        }
        // 判断attribute是否是JSON数组
        if (!JSON.isValidArray(attribute)) {
            return DataResponse.error("更新失败,参数必须是JSON数组格式");
        }
        spuPO.setGroupExtAttribute(attribute);
        spuPO.setUpdateTime(new Date());
        spuAtomicService.updateById(spuPO);
        return DataResponse.success();
    }

    @Override
    public DataResponse<String> updateSpuAttributeContainSymbol(Set<Long> spuIds) {
        if(CollectionUtils.isEmpty(spuIds)){
            return DataResponse.error("商品ID集合不能为空");
        }

        for (Long id : spuIds) {
            SaveSpuVO spuDetailVO = spuDraftManageService.getSaveSpuVoFromDraftBySpuId(id);
            if (Objects.isNull(spuDetailVO)) {
                continue;
            }

            List<PropertyVO> storeExtendPropertyList = spuDetailVO.getStoreExtendPropertyList();
            if (CollectionUtils.isNotEmpty(storeExtendPropertyList)) {
/*                storeExtendPropertyList.forEach(propertyVO -> {
                    if ((Objects.isNull(propertyVO.getAttributeInputType()) || AttributeInputTypeEnum.TEXT.getCode().equals(propertyVO.getAttributeInputType()))
                            && CollectionUtils.isNotEmpty(propertyVO.getPropertyValueVOList())) {
                        propertyVO.getPropertyValueVOList().forEach(propertyValueVO -> {
                            if (StringUtils.isNotBlank(propertyValueVO.getAttributeValueName())) {
                                propertyValueVO.setAttributeValueName(propertyValueVO.getAttributeValueName().replace(Constant.COLON,Constant.CHINESE_COLON).replace(Constant.COMMA,Constant.CHINESE_COMMA).replace(Constant.HASHTAG,Constant.CHINESE_HASHTAG));
                            }
                        });
                    }
                });*/
                this.replacePropertyValueSymbol(storeExtendPropertyList);
            }

            List<GroupPropertyVO> spuInterPropertyList = spuDetailVO.getSpuInterPropertyList();
            if (CollectionUtils.isNotEmpty(spuInterPropertyList)) {
                for (GroupPropertyVO groupPropertyVO : spuInterPropertyList) {
                    if (CollectionUtils.isEmpty(groupPropertyVO.getPropertyVOS())) {
                        continue;
                    }
                    List<PropertyVO> propertyVOList = groupPropertyVO.getPropertyVOS();
                    this.replacePropertyValueSymbol(propertyVOList);
                }
            }

            List<SkuVO> skuVOList = spuDetailVO.getSkuVOList();
            if (CollectionUtils.isNotEmpty(skuVOList)) {
                for (SkuVO skuVO : skuVOList) {
                    List<GroupPropertyVO> skuInterPropertyList = skuVO.getSkuInterPropertyList();
                    if (CollectionUtils.isEmpty(skuInterPropertyList)) {
                        continue;
                    }

                    for (GroupPropertyVO groupPropertyVO : skuInterPropertyList) {
                        if (CollectionUtils.isEmpty(groupPropertyVO.getPropertyVOS())) {
                            continue;
                        }
                        List<PropertyVO> propertyVOList = groupPropertyVO.getPropertyVOS();
                        this.replacePropertyValueSymbol(propertyVOList);
                    }
                }
            }
            spuDraftManageService.saveOrUpdateDraft(spuDetailVO);
        }
        return DataResponse.success();
    }

    private void replacePropertyValueSymbol(List<PropertyVO> propertyVOList) {
        propertyVOList.forEach(propertyVO -> {
            if ((Objects.isNull(propertyVO.getAttributeInputType()) || AttributeInputTypeEnum.TEXT.getCode().equals(propertyVO.getAttributeInputType()))
                    && CollectionUtils.isNotEmpty(propertyVO.getPropertyValueVOList())){
                propertyVO.getPropertyValueVOList().forEach(propertyValueVO -> {
                    log.info("替换之前的属性值 attributeValueName={}",propertyValueVO.getAttributeValueName());
                    String replaced = propertyValueVO.getAttributeValueName().replace(Constant.COLON, Constant.CHINESE_COLON).replace(Constant.COMMA, Constant.CHINESE_COMMA).replace(Constant.HASHTAG, Constant.CHINESE_HASHTAG);
                    log.info("替换之后的属性值 attributeValueName={}",replaced);
                    propertyValueVO.setAttributeValueName(replaced);
                });
            }
        });
    }


    @Resource(name="taxRateManageServiceImpl")
    private TaxRateManageService taxRateManageService;
    @Override
    public DataResponse<Boolean> delete(TaxRateReqDTO dto) {
        TaxRateVO taxRateVO = TaxRateConvert.INSTANCE.dto2Vo(dto);
        taxRateVO.setCreator(dto.getPin());
        taxRateVO.setUpdater(dto.getPin());
        boolean result = taxRateManageService.delete(taxRateVO);
        if(result){
            return DataResponse.success(true);
        }
        log.info("IscTaxRateWriteApiServiceImpl.delete req:{}，res:{}", JSON.toJSONString(dto), JSON.toJSONString(result));
        return DataResponse.error("删除失败");

    }

    @Override
    public DataResponse<String> updateSpuDraftJson(Long spuId,String spuDraft){
        if(spuId == null || StringUtils.isBlank(spuDraft)){
            return DataResponse.error("更新失败,param");
        }
//        SpuDraftVO spuDraftVO = spuDraftManageService.getDraftBySpuId(spuId);
//        if(spuDraftVO == null){
//            return DataResponse.error("更新失败,spuDraftVO");
//        }
//        SaveSpuVO saveSpuVO = JSONObject.parseObject(spuDraft,SaveSpuVO.class);
//        spuDraftVO.setSpuJsonInfo(JSONObject.toJSONString(saveSpuVO));
//        spuDraftManageService.saveOrUpdateDraft(saveSpuVO);
        return DataResponse.success();
    }

    @Override
    public DataResponse<String> updateSkuPrice(Long id, BigDecimal price) {
        SkuPricePO skuPricePO = skuPriceAtomicService.getById(id);
        if(skuPricePO == null){
            return DataResponse.error("失败");
        }

        SkuPricePO updatePO = new SkuPricePO();
        updatePO.setId(skuPricePO.getId());
        updatePO.setPrice(price);
//        updatePO.setTaxPrice(taxPrice);
        updatePO.setUpdater(skuPricePO.getUpdater());
        updatePO.setUpdateTime(new Date());
        skuPriceAtomicService.updateById(updatePO);
        return DataResponse.success();
    }

    @Override
    public DataResponse<String> updateSkuTaxPrice(Long id, BigDecimal taxPrice) {
        SkuPricePO skuPricePO = skuPriceAtomicService.getById(id);
        if(skuPricePO == null){
            return DataResponse.error("失败");
        }

        SkuPricePO updatePO = new SkuPricePO();
        updatePO.setId(skuPricePO.getId());
//        updatePO.setPrice(price);
        updatePO.setTaxPrice(taxPrice);
        updatePO.setUpdater(skuPricePO.getUpdater());
        updatePO.setUpdateTime(new Date());
        skuPriceAtomicService.updateById(updatePO);
        return DataResponse.success();
    }

    @Override
    public DataResponse<String> updateSpuRecordYn(Long id) {
        SpuAuditRecordPO spuAuditRecordPO = spuAuditRecordAtomicService.getById(id);
        if(spuAuditRecordPO == null){
            return DataResponse.error("失败");
        }

        SpuAuditRecordPO updatePO = new SpuAuditRecordPO();
        updatePO.setId(spuAuditRecordPO.getId());
//        updatePO.setPrice(price);

        updatePO.setUpdater(spuAuditRecordPO.getUpdater());
        updatePO.setUpdateTime(new Date());
        updatePO.setYn(YnEnum.NO.getCode());
        spuAuditRecordAtomicService.updateById(updatePO);
        return DataResponse.success();
    }

    @Override
    public Map<String, Map<String, Long>> queryClientMkuStatusMap(DevCustomerMkuStatusQueryDTO queryDTO) {
        try {
            List<CustomerMkuStatusNumResVO> clientMkuStatusNumList = customerMkuAtomicService.getClientMkuStatusNum(queryDTO);
            if (CollectionUtils.isEmpty(clientMkuStatusNumList)) {
                return Collections.emptyMap();
            }

            Map<String, Map<String, Long>> resultMap = Maps.newHashMap();

            Map<String, List<CustomerMkuStatusNumResVO>> clientMkuMap = clientMkuStatusNumList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(CustomerMkuStatusNumResVO::getClientCode));
            clientMkuMap.forEach((clientCode, mkus) -> {
                // 绑定状态转为上架 无效转为下架状态
                mkus.forEach(mku -> {
                    if (CustomerMkuBindEnum.BIND.name().equals(mku.getBindStatus())) {
                        mku.setBindStatus(Constant.UP);
                    } else if (CustomerMkuBindEnum.INVALID.name().equals(mku.getBindStatus())) {
                        mku.setBindStatus(Constant.DOWN);
                    }
                });
                // 查询客户信息
                CustomerVO customerVO = customerManageService.detail(clientCode);
                resultMap.put(customerVO.getClientName(), mkus.stream().collect(Collectors.toMap(CustomerMkuStatusNumResVO::getBindStatus, CustomerMkuStatusNumResVO::getNum)));
            });
            return resultMap;
        } catch (Exception e) {
            log.error("查询客户mku状态失败,queryVO={},err={}", JSON.toJSONString(queryDTO), e.getMessage(), e);
        }
        return Collections.emptyMap();
    }

    @Override
    public DataResponse<String> initSpuSkuJSON(List<Long> spuIds) {
        if(CollectionUtils.isEmpty(spuIds)){
            LambdaQueryWrapper<SpuDraftPO> qw = new LambdaQueryWrapper<>();
            qw.select(SpuDraftPO::getSpuId);
            qw.eq(SpuDraftPO::getAuditStatus, AuditStatusEnum.APPROVED.getCode());
            qw.eq(SpuDraftPO::getYn, YnEnum.YES.getCode());
            spuIds =  spuDraftAtomicService.list(qw)
                    .stream().map(SpuDraftPO::getSpuId)
                    .collect(Collectors.toList());
        }
        for(Long spuId : spuIds){
            SaveSpuVO saveSpuVO = null;
            try{
                saveSpuVO = spuDraftManageService.getSaveSpuVoFromDraftBySpuId(spuId);
                this.saveOrUpdateDraft(saveSpuVO);
            }catch (Exception e){
                log.error("DevOpsApiServiceImpl.initSpuSkuJSON error spuId:{},saveSpuVO:{}"
                        , spuId,JSONObject.toJSONString(saveSpuVO),e);
            }
        }

        return DataResponse.success();
    }

    @Override
    public DataResponse<String> initSpuInter(Set<Long> spuIds) {

        LambdaQueryWrapper<SpuPO> qw = new LambdaQueryWrapper<>();
        qw.in(CollectionUtils.isNotEmpty(spuIds),SpuPO::getSpuId,spuIds);
        qw.eq(SpuPO::getYn,YnEnum.YES.getCode());
        List<SpuPO> spuPOList = spuAtomicService.list(qw);
        if(CollectionUtils.isEmpty(spuPOList)){
            return DataResponse.error("spuPOList为空");
        }

        for (SpuPO spuPO : spuPOList) {
            CompletableFuture<Void> task = CompletableFuture.runAsync(() -> {
                LoginContextHolder loginContextHolder = new LoginContextHolder();
                loginContextHolder.setPin("system");
                LoginContextHolder.setLoginContextHolder(loginContextHolder);
                this.insertSpuGlobalAttribute(spuPO);
            }, dataHandleExecutor);
        }
        return DataResponse.success();
    }

    @Override
    public DataResponse<String> deleteSpuInter(Set<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return DataResponse.error("参数为空");
        }
        productGlobalAttributeAtomicService.removeBatchByIds(ids);
        return DataResponse.success();
    }

    @Override
    public DataResponse<String> deleteAllInter() {
        LambdaQueryWrapper<ProductGlobalAttributePO> qw = new LambdaQueryWrapper<>();
        qw.gt(ProductGlobalAttributePO::getId,10000);
//        productGlobalAttributeAtomicService.remove(qw);
        return DataResponse.success();
    }


    @Resource
    private CountryMkuAtomicService countryMkuAtomicService;

    @Override
    public DataResponse<String> updateCountryMkuStatus(String country, Integer sourceStatus, Integer targetStatus) {
        log.info("DevOpsApiServiceImpl.updateCountryMkuStatus country:{},sourceStatus:{}，targetStatus:{}", JSON.toJSONString(country), JSON.toJSONString(sourceStatus), JSON.toJSONString(targetStatus));

        Boolean result = countryMkuAtomicService.updateStatus(country, sourceStatus, targetStatus);
        return DataResponse.success();
    }

    private void insertSpuGlobalAttribute(SpuPO spuPO){
        try {
            String spuInterAttribute = spuPO.getSpuInterAttribute();
            if(StringUtils.isNotBlank(spuInterAttribute)){
                List<String> attributeScopeList = StringUtils.isNotBlank(spuPO.getAttributeScope()) ? Arrays.asList(spuPO.getAttributeScope().split(COMMA)) : Lists.newArrayList();
                List<GroupPropertyVO> interGroupPropertyVOS = globalAttributeManageService.queryGroupPropertyVos(spuPO.getSpuId(), spuPO.getSourceCountryCode(), attributeScopeList, AttributeDimensionEnum.SPU.getCode(), LangConstant.LANG_ZH, null);
                List<GroupPropertyVO> spuGroupProperVOS = new ArrayList<>();
                spuConvertService.fillInterAttributeSelected(interGroupPropertyVOS,spuGroupProperVOS,spuConvertService.splitInterAttributeStr(spuInterAttribute));
                List<ProductGlobalAttributePO> attributePOS = productAttributeConvertService.convertSpuAttribute(spuPO.getSpuId(),spuGroupProperVOS);
                if(CollectionUtils.isNotEmpty(attributePOS)){
                    productGlobalAttributeAtomicService.saveBatch(attributePOS);
                }
            }

            List<SkuPO> skuPOList = skuAtomicService.getSkuPoBySpuId(spuPO.getSpuId());
            if(CollectionUtils.isEmpty(skuPOList)){
                return;
            }
            List<String> attributeScopeList = StringUtils.isNotBlank(spuPO.getAttributeScope()) ? Arrays.asList(spuPO.getAttributeScope().split(COMMA)) : Lists.newArrayList();
            List<GroupPropertyVO> interGroupPropertyVOS = globalAttributeManageService.queryGroupPropertyVos(spuPO.getSpuId(), spuPO.getSourceCountryCode(), attributeScopeList, AttributeDimensionEnum.SKU.getCode(), LangConstant.LANG_ZH, null);
            for(SkuPO skuPO : skuPOList){
                String skuInterAttribute = skuPO.getSkuInterAttribute();
                List<GroupPropertyVO> skuGroupProperVOS = skuConvertService.fillInterAttributeSelected(interGroupPropertyVOS,skuConvertService.splitInterAttributeStr(skuInterAttribute));
                List<ProductGlobalAttributePO> attributePOS = productAttributeConvertService.convertSkuAttribute(skuPO.getSkuId(),skuGroupProperVOS);
                if(CollectionUtils.isNotEmpty(attributePOS)){
                    productGlobalAttributeAtomicService.saveBatch(attributePOS);
                }
            }
        }catch (Exception e){
            log.error("DevOpsApiServiceImpl.insertSpuGlobalAttribute error spuId:{}",spuPO.getSpuId(),e);
        }
    }

    private Long saveOrUpdateDraft(SaveSpuVO saveSpuVOParam) {
        // 深copy一下
        SaveSpuVO saveSpuVO = com.jd.fastjson.JSONObject.parseObject(com.jd.fastjson.JSONObject.toJSONString(saveSpuVOParam),SaveSpuVO.class);
        final Long spuId = saveSpuVO.getSpuVO().getSpuId();
        SpuDraftPO spuDraftPo = spuDraftAtomicService.getBySpuId(spuId);
        List<SkuDraftPO> skuDraftPOS = skuDraftAtomicService.getBySpuId(spuId);
        if (Objects.nonNull(spuDraftPo)) {
            spuDraftPo.setSpuId(null);
        } else {
            spuDraftPo = new SpuDraftPO();
            spuDraftPo.setSpuId(spuId);
            if(LoginContextHolder.getLoginContextHolder()!= null && StringUtils.isNotBlank(LoginContextHolder.getLoginContextHolder().getPin())){
                spuDraftPo.setCreator(LoginContextHolder.getLoginContextHolder().getPin());
                spuDraftPo.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
            } else {
                spuDraftPo.setCreator(Constant.SYSTEM);
                spuDraftPo.setUpdater(Constant.SYSTEM);
            }
            spuDraftPo.setCreateTime(new Date());
        }

        List<SkuDraftPO> inertSkuDrafts = this.buildSkuDraftPos(saveSpuVO);

        this.buildDraftPo(saveSpuVO, spuDraftPo);

        if(CollectionUtils.isNotEmpty(skuDraftPOS)){
            skuDraftAtomicService.removeBatchByIds(skuDraftPOS);
        }
        if(CollectionUtils.isNotEmpty(inertSkuDrafts)){
            skuDraftAtomicService.saveBatch(inertSkuDrafts);
        }
        // 保存更新草稿
        spuDraftAtomicService.saveOrUpdate(spuDraftPo);
        log.error("保存草稿完成，spuId={}", spuId);
        return spuId;
    }


    /**
     * 草稿
     *
     * @param saveSpuVO 保存spu对象
     */
    public void buildDraftPo(SaveSpuVO saveSpuVO, SpuDraftPO draftPo) {
        draftPo.setYn(YnEnum.YES.getCode());
        SpuVO spuVO = saveSpuVO.getSpuVO();
        draftPo.setVendorCode(spuVO.getVendorCode());
        draftPo.setAuditStatus(spuVO.getAuditStatus());
        draftPo.setTaxAuditStatus(spuVO.getTaxAuditStatus());
        draftPo.setCatId(spuVO.getCatId());
        draftPo.setJdCatId(spuVO.getCatId());
        draftPo.setSourceCountryCode(spuVO.getSourceCountryCode());
        draftPo.setLevel(spuVO.getLevel());
        if (MapUtils.isNotEmpty(saveSpuVO.getPcDescriptionMap())) {
            draftPo.setPcDescription(JSON.toJSONString(saveSpuVO.getPcDescriptionMap()));
            saveSpuVO.setPcDescriptionMap(null);
        }
        if (MapUtils.isNotEmpty(saveSpuVO.getAppDescriptionMap())) {
            draftPo.setAppDescription(JSON.toJSONString(saveSpuVO.getAppDescriptionMap()));
            saveSpuVO.setAppDescriptionMap(null);
        }
        if (StringUtils.isNotBlank(draftPo.getSpuJsonInfo())) {
            SaveSpuVO draftSaveSpuVo = JSON.parseObject(draftPo.getSpuJsonInfo(), SaveSpuVO.class);
            saveSpuVO.getSpuVO().setBuyer(draftSaveSpuVo.getSpuVO().getBuyer());
        }
        if(CollectionUtils.isNotEmpty(saveSpuVO.getSpuCertificateVOList())){
            draftPo.setSpuCertificate(JSON.toJSONString(saveSpuVO.getSpuCertificateVOList()));
//            saveSpuVO.setSpuCertificateVOList(null);
        }
        if(CollectionUtils.isNotEmpty(saveSpuVO.getSpuInterPropertyList())){
            draftPo.setSpuInterProperty(JSON.toJSONString(saveSpuVO.getSpuInterPropertyList()));
//            saveSpuVO.setSpuInterPropertyList(null);
        }
        if(CollectionUtils.isNotEmpty(saveSpuVO.getStoreExtendPropertyList())){
            draftPo.setGroupExtAttribute(spuConvertService.getGroupExtAttribute(saveSpuVO.getStoreExtendPropertyList()));
//            saveSpuVO.setStoreExtendPropertyList(null);
        }
        draftPo.setSpuJsonInfo(JSON.toJSONString(saveSpuVO));
        draftPo.setRemark("");
        draftPo.setUpdateTime(new Date());
    }

    /**
     * 草稿
     *
     * @param saveSpuVO 保存spu对象
     */
    public List<SkuDraftPO> buildSkuDraftPos(SaveSpuVO saveSpuVO) {
        List<SkuVO> skuVOList = saveSpuVO.getSkuVOList();
        if(CollectionUtils.isEmpty(skuVOList)){
            log.error("SpuDraftManageServiceImpl.buildSkuDraftPos skuVOList is null");
            return Collections.emptyList();
        }
        Long spuId = saveSpuVO.getSpuVO().getSpuId();

        List<SkuDraftPO> results = new ArrayList<>();
        for(SkuVO skuVO : skuVOList){
            SkuDraftPO skuDraftPO = new SkuDraftPO();
            skuDraftPO.setSpuId(spuId);
            skuDraftPO.setSkuId(skuVO.getSkuId());

            String skuKey = skuVO.getSkuKey();
            if(StringUtils.isBlank(skuKey)){
                skuKey = this.generateSkuKey(spuId,skuVO.getStoreSalePropertyList());
            }
            skuDraftPO.setSkuKey(skuKey);
            if(CollectionUtils.isNotEmpty(skuVO.getSkuInterPropertyList())){
                skuDraftPO.setSkuInterProperty(JSON.toJSONString(skuVO.getSkuInterPropertyList()));
                skuVO.setSkuInterPropertyList(null);
            }
            if(CollectionUtils.isNotEmpty(skuVO.getSkuCertificateVOList())){
                skuDraftPO.setSkuCertificate(JSON.toJSONString(skuVO.getSkuCertificateVOList()));
                skuVO.setSkuCertificateVOList(null);
            }
            if(CollectionUtils.isNotEmpty(skuVO.getSkuStockRelationList())){
                skuDraftPO.setSkuStockRelation(JSON.toJSONString(skuVO.getSkuStockRelationList()));
                skuVO.setSkuStockRelationList(null);
            }
            if(CollectionUtils.isNotEmpty(skuVO.getStoreSalePropertyList())){
//                skuDraftPO.setSaleProperty(JSON.toJSONString(skuVO.getStoreSalePropertyList()));
                skuVO.setStoreSalePropertyList(null);
            }
            skuDraftPO.setSkuJsonInfo(JSON.toJSONString(skuVO));
            skuDraftPO.setCreator(saveSpuVO.getSpuVO().getCreator());
            Long createTime = new Date().getTime();
            if(saveSpuVO.getSpuVO().getCreateTime() != null){
                createTime = saveSpuVO.getSpuVO().getCreateTime().getTime();
            }
            skuDraftPO.setCreateTime(createTime);
            skuDraftPO.setUpdater(saveSpuVO.getSpuVO().getUpdater());
            Long updateTime = new Date().getTime();
            if(saveSpuVO.getSpuVO().getUpdateTime() != null){
                updateTime = saveSpuVO.getSpuVO().getUpdateTime().getTime();
            }
            skuDraftPO.setUpdateTime(updateTime);
            results.add(skuDraftPO);
        }
//        saveSpuVO.setSkuVOList(null);
        return results;
    }

    /**
     * 生成 SKU 唯一键。
     * @param spuId SPu ID
     * @param propertyValueVOList 销售属性列表
     * @return SKU 唯一键
     */
    private String generateSkuKey(Long spuId,List<PropertyValueVO> propertyValueVOList){
        if(CollectionUtils.isEmpty(propertyValueVOList)){
            throw new BizException("生成sku唯一键失败,销售属性为空");
        }
        String result = "";
        try {
            List<PropertyValueVO> propertyValueVOList1 = new ArrayList<>();
            propertyValueVOList.forEach(item->{
                PropertyValueVO newPropertyValueVO = new PropertyValueVO();
                newPropertyValueVO.setAttributeId(item.getAttributeId());
                newPropertyValueVO.setAttributeValueId(item.getAttributeValueId());
                propertyValueVOList1.add(newPropertyValueVO);
            });
            String originInfo = spuId + JSON.toJSONString(propertyValueVOList1);
            result =  AESIVUtil.skuEncrypt(originInfo);
            if(StringUtils.isNotBlank(result) && result.length() > 49){
                result = result.substring(0, 49);
            }
        } catch (Exception e) {
            throw new BizException("生成sku唯一键失败");
        }
        return result;
    }

    @Override
    public DataResponse<String> addSkuFeaturePackageSpecification(List<SkuFeatureApiDTO> apiDTOList) {
        if (CollectionUtils.isEmpty(apiDTOList)) {
            return DataResponse.error("数据不能为空");
        }


        for (SkuFeatureApiDTO apiDTO : apiDTOList) {
            if (apiDTO.getSkuId() == null) {
                return DataResponse.error("skuId不能为空");
            }

            SkuFeaturePO skuFeaturePO = skuFeatureAtomicService.getSkuFeatureBySkuId(apiDTO.getSkuId());
            if (Objects.isNull(skuFeaturePO)) {
                skuFeaturePO = new SkuFeaturePO();
                skuFeaturePO.setSkuId(apiDTO.getSkuId());
                skuFeaturePO.setPackageSpecification(apiDTO.getPackageSpecification());
                skuFeaturePO.setPackageSpecificationUnit(apiDTO.getPackageSpecificationUnit());
                skuFeaturePO.setCreator(Constant.SYSTEM);
                skuFeaturePO.setUpdater(Constant.SYSTEM);
                long now = System.currentTimeMillis();
                skuFeaturePO.setCreateTime(now);
                skuFeaturePO.setUpdateTime(now);
            }else {
                skuFeaturePO.setSkuId(null);
                skuFeaturePO.setPackageSpecification(apiDTO.getPackageSpecification());
                 skuFeaturePO.setPackageSpecificationUnit(apiDTO.getPackageSpecificationUnit());
                 skuFeaturePO.setUpdater(Constant.SYSTEM);
                 long now = System.currentTimeMillis();
                 skuFeaturePO.setUpdateTime(now);
            }
            boolean b = skuFeatureAtomicService.saveOrUpdate(skuFeaturePO);
            log.info("初始化商品包装规格 addSkuFeaturePackageSpecification skuId={} ,result={}", apiDTO.getSkuId(),b);
        }
        return DataResponse.success();
    }

    @Override
    public DataResponse<String> initStockThreshold(String env) {
        return skuStockToolService.initStockThreshold(env);
    }

    @Override
    public DataResponse<String> truncateStockThreshold(String env) {
        return skuStockToolService.truncate(env);
    }

    @Override
    public DataResponse<JSONObject> queryProductInfo(String productId) {
        if (StringUtils.isBlank(productId)) {
            return DataResponse.success();
        }
        long id = Long.parseLong(productId);
        JSONObject result = new JSONObject();
        // SPU
        if (productId.startsWith("2")){
            SpuPO spuPo = spuAtomicService.getSpuPoBySpuId(id);
            if (Objects.isNull(spuPo)) {
                return DataResponse.success();
            }
            // 查询所有SKU
            Map<Long, SkuPO> skuPoMap = skuReadManageService.getSkuBaseInfoBySku(id);
            if (MapUtils.isEmpty(skuPoMap)) {
                return DataResponse.success();
            }
            Set<Long> skuIds = skuPoMap.keySet();
            // 查询SKU属性
            Map<String, SkuFeatureApiDTO> featureApiDTOMap = skuReadManageService.getSkuFeatureBySku(new QuerySkuReqDTO(skuIds.stream().map(String::valueOf).collect(Collectors.toSet()), spuPo.getSourceCountryCode()));
            // 查询价格
            Map<Long,List<SkuPricePO>> priceMap = skuPriceAtomicService.batchQuerySkuPrice(skuIds);
            for (Map.Entry<Long, SkuPO> entry : skuPoMap.entrySet()) {
                Long skuId = entry.getKey();
                SkuPO skuPO = entry.getValue();
                JSONObject baseInfo = new JSONObject();
                baseInfo.put("Length", skuPO.getLength());
                baseInfo.put("Width", skuPO.getWidth());
                baseInfo.put("Height", skuPO.getHeight());
                baseInfo.put("Weight", skuPO.getWeight());
                // 基本信息
                result.put("基本信息",baseInfo);
                //属性
                SkuFeatureApiDTO featureApiDTO = featureApiDTOMap.get(String.valueOf(skuId));
                JSONObject feature = new JSONObject();
                feature.put("发货时效", featureApiDTO.getProductionCycle());
                feature.put("备货模式", featureApiDTO.getPurchaseModel());
                result.put("属性", feature);
                // 价格
                List<SkuPricePO> pricePOList = priceMap.get(skuId);
                JSONObject basePrice =  new JSONObject();
                for (SkuPricePO pricePO : pricePOList) {
                    if (TradeDirectionEnum.SUPPLIER.equals(pricePO.getTradeDirection())) {
                        basePrice.put("采购价", pricePO.getPrice());
                        basePrice.put("含税采购价", pricePO.getTaxPrice());
                    }else {
                        basePrice.put("销售价", pricePO.getPrice());
                        basePrice.put("含税销售价", pricePO.getTaxPrice());
                    }
                }
                result.put("价格", basePrice);
                // 库存

                // 绑定客户
            }
        }else if (productId.startsWith("8")){
            // SKU
        }else if (productId.startsWith("5")){
            // MKU
        }else {
            // 国内SKU
        }
        return null;
    }

    @Override
    public DataResponse<String> initCustomerSkuDetailDraft(Set<Long> draftIds) {
        if(CollectionUtils.isEmpty(draftIds)){
            return DataResponse.error("draftIds不能为空");
        }

        List<CustomerSkuPriceDraftPO> customerSkuPriceDraftPOS = customerSkuPriceDraftAtomicService.listByIds(draftIds);
        for(CustomerSkuPriceDraftPO customerSkuPriceDraftPO : customerSkuPriceDraftPOS){
            productToolService.initCustomerSkuDetailDraftAndDetail(customerSkuPriceDraftPO);
        }

        return DataResponse.success();
    }

    @Override
    public DataResponse<String> deleteCustomerSkuDetailDraft(Set<Long> detailDraftIds) {
        productToolService.deleteCustomerSkuDetailDraft(detailDraftIds);
        return DataResponse.success();
    }

    @Override
    @ToolKit
    public DataResponse<String> getFulfillmentRate(Long skuId, String sourceCountryCode, String targetCountryCode) {
        FulfillmentRateVO in = new FulfillmentRateVO();
        in.setSkuId(skuId);
        in.setSourceCountryCode(sourceCountryCode);
        in.setTargetCountryCode(targetCountryCode);
        return DataResponse.success(JSON.toJSONString(fulfillmentRateManageService.getFulfillmentRate(in)));
    }

    @Override
    @ToolKit
    public DataResponse<String> updateSpuAttributeScope(List<Long> skuIds, String attributeScope) {
        return DataResponse.success(productToolService.updateSpuAttributeScope(skuIds,attributeScope));
    }

    @Override
    public DataResponse<Void> removeDulExtAttr(Boolean delFlag) {
        extAttributeLangService.removeDul(delFlag);
        return DataResponse.success();
    }

//    @Override
//    @ToolKit(exceptionWrap = true)
//    public DataResponse<Integer> batchFixBrNcmCode(List<Long> jdSkuIds, boolean update) {
//        return brSkuTaxManageService.batchFixBrNcmCode(jdSkuIds, update, "liuzhaoming.10");
//    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> updateTransferStock(Long skuId, Long warehouseId, Long transferStockNum) {
        return skuStockToolService.updateTransferStock(skuId,warehouseId,transferStockNum);
    }

    @Override
    public DataResponse<String> updateSaleAttribute(List<Long> skuIds, String saleAttribute) {
        return DataResponse.success(productToolService.updateSaleAttribute(Sets.newHashSet(skuIds),saleAttribute));
    }

    @Override
    public DataResponse<String> productCustomsInit(Long startSpuId, int batchSize) {
        String lastSkuId = productCustomInfoService.productCustomInfoInit(startSpuId, batchSize);
        return DataResponse.success(lastSkuId);
    }

    /**
     * 带缓存的查询类目扩展属性方法
     * 
     * @param catId 类目ID
     * @param lang 语言
     * @return 属性列表
     */
    private List<AttributeFlatVO> queryExtAttrDetailWithCache(Long catId, String lang) {
        // 构建缓存key，添加前缀防止冲突
        String cacheKey = "ext_attr_detail:" + catId + ":" + lang;
        
        try {
            // 先从缓存获取
            String cacheValue = jimUtils.get(cacheKey);
            if (StringUtils.isNotBlank(cacheValue)) {
                log.debug("从缓存获取类目{}的扩展属性", catId);
                return JSON.parseArray(cacheValue, AttributeFlatVO.class);
            }
            
            // 缓存未命中，查询数据库
            log.debug("缓存未命中，查询数据库获取类目{}的扩展属性", catId);
            List<AttributeFlatVO> attributeFlatList = attributeOutService.queryExtAttrDetail(catId, lang);

            // 将结果存入缓存，过期时间1小时
            if (CollectionUtils.isNotEmpty(attributeFlatList)) {
                jimUtils.setEx(cacheKey, JSON.toJSONString(attributeFlatList), 1, TimeUnit.HOURS);
                log.debug("将类目{}的扩展属性存入缓存", catId);
            }
            
            return attributeFlatList;
            
        } catch (Exception e) {
            log.error("查询类目{}扩展属性时发生异常，降级为直接查询数据库", catId, e);
            // 发生异常时降级为直接查询数据库
            return attributeOutService.queryExtAttrDetail(catId, lang);
        }
    }

    /**
     * 分页处理SPU扩展属性数据，将extAttribute转换并分类为SPU和SKU属性
     * 
     * @param pageIndex 页码，从1开始
     * @param pageSize 每页大小
     * @return 处理结果
     */
    public DataResponse<String> processSpuExtAttributes(String updateTime,Long pageIndex, Long pageSize) {
        log.info("开始处理SPU扩展属性数据，页码：{}, 页大小：{}", pageIndex, pageSize);
        
        try {
            // 1. 分页读取SPU数据
            List<SpuPO> spuList = spuAtomicService.listAllSpuWithPage(updateTime, pageIndex, pageSize);
            if (CollectionUtils.isEmpty(spuList)) {
                return DataResponse.success("当前页无数据");
            }
            
            log.info("获取到{}条SPU数据", spuList.size());
            
            int successCount = 0;
            int errorCount = 0;
            
            for (SpuPO spuPO : spuList) {
                try {
                    if (handleSpu(spuPO)) continue;

                    successCount++;
                    
                } catch (Exception e) {
                    errorCount++;
                    log.error("处理SpuId: {} 的扩展属性时发生异常", spuPO.getSpuId(), e);
                }
            }
            
            log.info("第{}页处理完成，成功：{}, 失败：{}", pageIndex, successCount, errorCount);
            return DataResponse.success("处理完成，成功：" + successCount + "，失败：" + errorCount);
                                      
        } catch (Exception e) {
            log.error("处理SPU扩展属性数据时发生异常，页码：{}, 页大小：{}", pageIndex, pageSize, e);
            return DataResponse.error("处理失败：" + e.getMessage());
        }
    }

    /**
     * 处理单条SPU扩展属性数据
     * @return
     */
    @Override
    public DataResponse<String> processSpuExtAttributesBySpuId(Long spuId) {
        SpuPO spuPO = spuAtomicService.getSpuPoBySpuId(spuId);
        if (spuPO == null) {
            log.error("SpuId: {} 的SPU不存在", spuId);
            return DataResponse.error("SpuId: " + spuId + " 的SPU不存在");
        }
        handleSpu(spuPO);
        return DataResponse.success();
    }



    private boolean handleSpu(SpuPO spuPO) {
        // 检查extAttribute是否非空
        if (StringUtils.isBlank(spuPO.getExtAttribute())) {
            log.debug("SpuId: {} 的扩展属性为空，跳过处理", spuPO.getSpuId());
            return true;
        }
        String extAttribute = spuPO.getExtAttribute();
        String spuGroupExtAttribute = "";

        // 2. 调用SpuConvertService将extAttribute转换为List<PropertyVO>
        List<PropertyVO> propertyList = spuConvertService.convertStoreExtAttributeToPropertyList(extAttribute);
        if (CollectionUtils.isEmpty(propertyList)) {
            log.debug("SpuId: {} 转换后的属性列表为空，跳过处理", spuPO.getSpuId());
            return true;
        }

        // 3. 根据catId查询属性详情（使用缓存）
        List<AttributeFlatVO> attributeFlatList = queryExtAttrDetailWithCache(spuPO.getJdCatId(), "zh");
        log.info("类目{}的扩展属性：{}", spuPO.getJdCatId(), JSON.toJSONString(attributeFlatList));
        if (CollectionUtils.isEmpty(attributeFlatList)) {
            log.debug("CatId: {} 的扩展属性配置为空，跳过处理", spuPO.getJdCatId());
            return true;
        }

        // 4. 创建属性ID到AttributeFlatVO的映射
        Map<Long, AttributeFlatVO> attributeMap = attributeFlatList.stream()
                .collect(Collectors.toMap(attr -> attr.getId(), attr -> attr, (v1, v2) -> v1));

        // 存储SPU级别属性列表
        List<PropertyVO> spuLevelProperties = Lists.newArrayList();
        // 存储SKU级别属性列表
        List<PropertyVO> skuLevelProperties = Lists.newArrayList();

        // 5. 补齐PropertyVO的属性信息并分类
        for (PropertyVO propertyVO : propertyList) {
            AttributeFlatVO attributeFlatVO = attributeMap.get(propertyVO.getAttributeId());
            if (attributeFlatVO == null) {
                log.warn("SpuId: {}, AttributeId: {} 在类目属性中未找到对应配置",
                        spuPO.getSpuId(), propertyVO.getAttributeId());
                continue;
            }

            // 补齐基本属性信息
            propertyVO.setComGroupId(attributeFlatVO.getComGroupId());
            propertyVO.setComGroupName(attributeFlatVO.getComGroupName());
            propertyVO.setLevel(attributeFlatVO.getLevel());

            // 补齐属性值信息
            if (CollectionUtils.isNotEmpty(propertyVO.getPropertyValueVOList()) &&
                CollectionUtils.isNotEmpty(attributeFlatVO.getAttributeValueList())) {

                // 创建属性值ID到AttributeValueFlatVO的映射
                Map<Long, AttributeValueFlatVO> valueMap = attributeFlatVO.getAttributeValueList()
                        .stream()
                        .collect(Collectors.toMap(val -> val.getId(), val -> val, (v1, v2) -> v1));

                for (PropertyValueVO propertyValueVO : propertyVO.getPropertyValueVOList()) {
                    AttributeValueFlatVO valueFlatVO = valueMap.get(propertyValueVO.getAttributeValueId());
                    if (valueFlatVO != null) {
                        // 如果原来没有设置属性值ID，则设置
                        if (propertyValueVO.getAttributeValueId() == null) {
                            propertyValueVO.setAttributeValueId(valueFlatVO.getId());
                        }
                        // 如果原来没有设置属性值名称，则设置
                        if (StringUtils.isBlank(propertyValueVO.getAttributeValueName())) {
                            propertyValueVO.setAttributeValueName(valueFlatVO.getLangName());
                        }
                    }
                }
            }

            // 6. 根据level分类存储（level=0为SPU级别，其他为SKU级别）
            if (PropertyVO.ONLY_SPU_LEVEL_EXT_ATTR_VALUE.equals(attributeFlatVO.getLevel())) {
                spuLevelProperties.add(propertyVO);
            } else {
                skuLevelProperties.add(propertyVO);
            }
        }

        // 7. 调用spuConvertService转换并更新SPU
        if (CollectionUtils.isNotEmpty(spuLevelProperties)) {
            spuGroupExtAttribute = spuConvertService.getGroupExtAttribute(spuLevelProperties);

            // 更新SPU的groupExtAttribute字段
            SpuPO updateSpuPO = new SpuPO();
            updateSpuPO.setSpuId(spuPO.getSpuId());
            updateSpuPO.setGroupExtAttribute(spuGroupExtAttribute);
            updateSpuPO.setUpdater("system");
            spuAtomicService.updateById(updateSpuPO);
            log.debug("更新SpuId: {} 的groupExtAttribute字段", spuPO.getSpuId());
        }
        String groupExtAttribute = "";
        // 8. 处理SKU级别属性
        if (CollectionUtils.isNotEmpty(skuLevelProperties)) {
            String skuGroupExtAttribute = spuConvertService.getGroupExtAttribute(skuLevelProperties);
            groupExtAttribute=ExtendPropertyGroupVO.serializeExtendPropertyGroups(ExtendPropertyGroupVO.mergeAndDeserializeExtendPropertyGroups(spuGroupExtAttribute, skuGroupExtAttribute));
            // 根据spuId查询所有SKU
            List<SkuPO> skuList = skuAtomicService.selectSkuPosBySpuId(spuPO.getSpuId());
            if (CollectionUtils.isNotEmpty(skuList)) {
                // 批量更新SKU的groupExtAttribute字段
                List<SkuPO> updateSkuList = Lists.newArrayList();
                for (SkuPO skuPO : skuList) {
                    SkuPO updateSkuPO = new SkuPO();
                    updateSkuPO.setSkuId(skuPO.getSkuId());
                    updateSkuPO.setGroupExtAttribute(skuGroupExtAttribute);
                    updateSkuPO.setUpdater("system");
                    updateSkuList.add(updateSkuPO);
                    System.out.println("---skuGroupExtAttribute:"+skuGroupExtAttribute+"skuId:"+skuPO.getSkuId());
                }

                 skuAtomicService.updateBatchById(updateSkuList);
                log.debug("更新SpuId: {} 下的{}个SKU的groupExtAttribute字段", spuPO.getSpuId(), updateSkuList.size());
            }
            // 校验
            boolean isValid = validateSpuData(spuPO.getSpuId(),extAttribute,groupExtAttribute);
            if(!isValid){
                log.error("--------------------------------spuId: {} 的groupExtAttribute字段转换结果验证失败，extAttribute: {}, groupExtAttribute: {}", spuPO.getSpuId(),extAttribute,groupExtAttribute);
            }
        }
        return false;
    }

    private static Long sleepTime = 1000*3L;
    /**
     * 批量处理所有SPU扩展属性数据
     * 1、从SpuAtomicService的分页读取spu方法分页读取spu对象，读取spu中的extAttribute非空字符串，调用SpuConvertService的convertStoreExtAttributeToPropertyList将extAttribute转换为List<PropertyVO>
     * 2、根据spu对象的catId，通过attributeOutService的queryExtAttrDetail接口（lang=zh）读取该catid下的所有属性对象List<AttributeFlatVO>，并且用redis缓存（JimUtils，失效时间1小时）
     * 3、attributeFlatVO中的id是属性id、PropertyVO中的attributeId是属性id，以id相等为条件，从List<AttributeFlatVO>中获取数据将List<PropertyVO>的每一个PropertyVO对象的comGroupId、comGroupName、level以及propertyValueVOList中的PropertyValueVO对象里的attributeValueId和attributeValueName补齐
     * 4、将新的填满值的List<PropertyVO>对象根据level=0放到spulist、否则放到skulist分两份存放
     * 5、调用spuConvertService.getGroupExtAttribute将分别将spulist和skulist转化为字符串spuGroupExtAttribute、skuGroupExtAttribute，然后调用spuAtomicService将spuGroupExtAttribute更新到spu的groupExtAttribute字段，然后调用SkuAtomicService根据spuid查询skulist，将skuGroupExtAttribute更新到每一个sku的groupExtAttribute字段
     * @return 处理结果
     */
    @Override
    public DataResponse<String> processAllSpuExtAttributes(String updateTime) {
        if(StringUtils.isBlank(updateTime)){
            updateTime = UPDATE_TIME;
        }
        int batchSize = 50;

        log.info("开始批量处理所有SPU扩展属性数据，批大小：{}", batchSize);
        
        try {
            // 获取总数
            long totalCount = spuAtomicService.listAllSpuTotal(updateTime);
            if (totalCount == 0) {
                return DataResponse.success("无数据需要处理");
            }
            
            long totalPages = (totalCount + batchSize - 1) / batchSize;
            log.info("总记录数：{}, 总页数：{}", totalCount, totalPages);
            
            int processedCount = 0;
            int errorCount = 0;
            
            for (long currentPage = 1; currentPage <= totalPages; currentPage++) {
                try {
                    DataResponse<String> result = this.processSpuExtAttributes(updateTime, currentPage, (long) batchSize);
                    if (result.getSuccess()) {
                        processedCount++;
                        log.info("第{}/{}页处理完成", currentPage, totalPages);
                    } else {
                        errorCount++;
                        log.error("第{}/{}页处理失败：{}", currentPage, totalPages, result.getMessage());
                    }
                    
                    // 添加适当的延时，避免对数据库造成过大压力
                    Thread.sleep(sleepTime);
                    
                } catch (Exception e) {
                    errorCount++;
                    log.error("第{}/{}页处理异常", currentPage, totalPages, e);
                }
            }
            
            log.info("批量处理完成，成功页数：{}, 失败页数：{}", processedCount, errorCount);
            return DataResponse.success("批量处理完成，成功：" + processedCount + "页，失败：" + errorCount + "页");
            
        } catch (Exception e) {
            log.error("批量处理SPU扩展属性数据时发生异常", e);
            return DataResponse.error("批量处理失败：" + e.getMessage());
        }
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Integer> batchFixBrNcmCode(List<Long> jdSkuIds, boolean update, String updater) {
        return brSkuTaxManageService.batchFixBrNcmCode(jdSkuIds, update, updater);
    }

    /**
     * 分页处理MKU扩展属性数据
     * 将旧格式的extAttribute转换为新格式的groupExtAttribute
     * 
     * @param pageIndex 页码，从1开始
     * @param pageSize 每页大小
     * @return 处理结果
     */
    public DataResponse<String> processMkuExtAttributes(String updateTime, Long pageIndex, Long pageSize) {
        // 参数校验
        if (pageIndex == null || pageIndex < 1) {
            return DataResponse.error("页码必须大于等于1");
        }
        if (pageSize == null || pageSize < 1 || pageSize > 1000) {
            return DataResponse.error("页大小必须在1-1000之间");
        }
        
        log.info("开始处理第{}页MKU扩展属性数据，每页{}条", pageIndex, pageSize);
        
        try {
            // 1. 获取当前页的MKU数据
            List<MkuPO> mkuList = mkuAtomicService.listAllMkuWithPage(updateTime, pageIndex, pageSize);
            
            if (CollectionUtils.isEmpty(mkuList)) {
                log.info("第{}页无MKU数据", pageIndex);
                return DataResponse.success("当前页无数据");
            }
            
            log.info("获取到{}条MKU数据", mkuList.size());
            
            int successCount = 0;
            int errorCount = 0;
            
            for (MkuPO mkuPO : mkuList) {
                try {
                    if (extracted(mkuPO)) continue;

                    successCount++;
                    
                } catch (Exception e) {
                    errorCount++;
                    log.error("处理MkuId: {} 的扩展属性时发生异常", mkuPO.getMkuId(), e);
                }
            }
            
            log.info("第{}页处理完成，成功：{}, 失败：{}", pageIndex, successCount, errorCount);
            return DataResponse.success("处理完成，成功：" + successCount + "，失败：" + errorCount);
                                      
        } catch (Exception e) {
            log.error("处理MKU扩展属性数据时发生异常，页码：{}, 页大小：{}", pageIndex, pageSize, e);
            return DataResponse.error("处理失败：" + e.getMessage());
        }
    }

    /**
     * 根据mkuid处理单条MKU扩展属性数据
     * 
     * @param mkuId
     * @return
     */
    @Override
    public DataResponse<String> processMkuExtAttributesByMkuId(Long mkuId) {
        try {
            // 1. 获取MKU数据
            MkuPO mkuPO = mkuAtomicService.getPOById(mkuId);
            if (mkuPO == null) {
                return DataResponse.error("MKU不存在");
            }
            extracted(mkuPO);
            return DataResponse.success();
        } catch (Exception e) {
            log.error("处理MKU扩展属性数据时发生异常，mkuId：{}", mkuId, e);
            return DataResponse.error("处理失败：" + e.getMessage());
        }
    }

    private boolean extracted(MkuPO mkuPO) {
        // 检查extAttribute是否非空
        if (StringUtils.isBlank(mkuPO.getExtAttribute())) {
            log.debug("MkuId: {} 的扩展属性为空，跳过处理", mkuPO.getMkuId());
            return true;
        }
        Long mkuId = mkuPO.getMkuId();
        String extAttribute = mkuPO.getExtAttribute();
        // 2. 调用SpuConvertService将extAttribute转换为List<PropertyVO>
        List<PropertyVO> propertyList = spuConvertService.convertStoreExtAttributeToPropertyList(extAttribute);
        if (CollectionUtils.isEmpty(propertyList)) {
            log.debug("MkuId: {} 转换后的属性列表为空，跳过处理", mkuPO.getMkuId());
            return true;
        }

        // 3. 根据catId查询属性详情（使用缓存）
        List<AttributeFlatVO> attributeFlatList = queryExtAttrDetailWithCache(mkuPO.getJdCatId(), "zh");
        log.info("获取到的京东扩展属性信息：jdcatid: {}, attributeFlatList: {}", mkuPO.getJdCatId()  , attributeFlatList);
        if (CollectionUtils.isEmpty(attributeFlatList)) {
            log.debug("CatId: {} 的扩展属性配置为空，跳过处理", mkuPO.getJdCatId());
            return true;
        }

        // 4. 创建属性ID到AttributeFlatVO的映射
        Map<Long, AttributeFlatVO> attributeMap = attributeFlatList.stream()
                .collect(Collectors.toMap(attr -> attr.getId(), attr -> attr, (v1, v2) -> v1));

        // 5. 补齐PropertyVO的属性信息
        List<PropertyVO> enrichedPropertyList = Lists.newArrayList();

        for (PropertyVO propertyVO : propertyList) {
            AttributeFlatVO attributeFlatVO = attributeMap.get(propertyVO.getAttributeId());
            if (attributeFlatVO == null) {
                log.warn("MkuId: {}, jdcatid：{}, AttributeId: {} 在类目属性中未找到对应配置",
                        mkuPO.getMkuId(), mkuPO.getJdCatId(), propertyVO.getAttributeId());
                continue;
            }

            // 补齐基本属性信息
            propertyVO.setComGroupId(attributeFlatVO.getComGroupId());
            propertyVO.setComGroupName(attributeFlatVO.getComGroupName());
            propertyVO.setLevel(attributeFlatVO.getLevel());

            // 补齐属性值信息
            if (CollectionUtils.isNotEmpty(propertyVO.getPropertyValueVOList()) &&
                CollectionUtils.isNotEmpty(attributeFlatVO.getAttributeValueList())) {

                // 创建属性值ID到AttributeValueFlatVO的映射
                Map<Long, AttributeValueFlatVO> valueMap = attributeFlatVO.getAttributeValueList()
                        .stream()
                        .collect(Collectors.toMap(val -> val.getId(), val -> val, (v1, v2) -> v1));

                for (PropertyValueVO propertyValueVO : propertyVO.getPropertyValueVOList()) {
                    AttributeValueFlatVO valueFlatVO = valueMap.get(propertyValueVO.getAttributeValueId());
                    if (valueFlatVO != null) {
                        // 如果原来没有设置属性值ID，则设置
                        if (propertyValueVO.getAttributeValueId() == null) {
                            propertyValueVO.setAttributeValueId(valueFlatVO.getId());
                        }
                        // 如果原来没有设置属性值名称，则设置
                        if (StringUtils.isBlank(propertyValueVO.getAttributeValueName())) {
                            propertyValueVO.setAttributeValueName(valueFlatVO.getLangName());
                        }
                    }
                }
            }

            enrichedPropertyList.add(propertyVO);
        }

        // 6. 转换为groupExtAttribute字符串并更新MKU
        if (CollectionUtils.isNotEmpty(enrichedPropertyList)) {
            String mkuGroupExtAttribute = spuConvertService.getGroupExtAttribute(enrichedPropertyList);
            System.out.println(mkuGroupExtAttribute);
            // 更新MKU的groupExtAttribute字段
            MkuPO updateMkuPO = new MkuPO();
            updateMkuPO.setMkuId(mkuPO.getMkuId());
            updateMkuPO.setGroupExtAttribute(mkuGroupExtAttribute);
            updateMkuPO.setUpdater("system");
            mkuAtomicService.updateById(updateMkuPO);

            log.debug("更新MkuId: {} 的groupExtAttribute字段", mkuPO.getMkuId());

            // 校验
            boolean isValid = validateMkuData(mkuId,extAttribute,mkuGroupExtAttribute);
            if(!isValid){
                log.error("--------------------------------MkuId: {} 的groupExtAttribute字段转换结果验证失败，extAttribute: {}, groupExtAttribute: {}", mkuId,extAttribute,mkuGroupExtAttribute);
            }
        }
        return false;
    }

    public static final String UPDATE_TIME = "2000-06-10 10:37:39";
    /**
     * 批量处理所有MKU扩展属性数据
     * 将旧格式的extAttribute转换为新格式的groupExtAttribute
     * 
     * MKU扩展属性处理流程：
     * 1. 分页读取MKU数据 (extAttribute ≠ '' && groupExtAttribute is null)
     * 2. 转换 extAttribute → List<PropertyVO>  
     * 3. 根据catId查询属性配置 (缓存)
     * 4. 补齐PropertyVO的完整信息
     * 5. 转换 List<PropertyVO> → groupExtAttribute字符串
     * 6. 更新MKU.groupExtAttribute字段
     * 
     * 
     * @return 处理结果
     */
    @Override
    public DataResponse<String> processAllMkuExtAttributes(String updateTime) {
        if(StringUtils.isBlank(updateTime)){
            updateTime = UPDATE_TIME;
        }
        int batchSize = 50;

        log.info("开始批量处理所有MKU扩展属性数据，批大小：{}", batchSize);
        
        try {
            // 获取总数
            long totalCount = mkuAtomicService.listAllMkuTotal(updateTime);
            if (totalCount == 0) {
                return DataResponse.success("无数据需要处理");
            }
            
            long totalPages = (totalCount + batchSize - 1) / batchSize;
            log.info("总记录数：{}, 总页数：{}", totalCount, totalPages);
            
            int processedCount = 0;
            int errorCount = 0;
            
            for (long currentPage = 1; currentPage <= totalPages; currentPage++) {
                try {
                    DataResponse<String> result = this.processMkuExtAttributes(updateTime, currentPage, (long) batchSize);
                    if (result.getSuccess()) {
                        processedCount++;
                        log.info("第{}/{}页处理完成", currentPage, totalPages);
                    } else {
                        errorCount++;
                        log.error("第{}/{}页处理失败：{}", currentPage, totalPages, result.getMessage());
                    }
                    
                    // 添加适当的延时，避免对数据库造成过大压力
                    Thread.sleep(sleepTime);
                    
                } catch (Exception e) {
                    errorCount++;
                    log.error("第{}/{}页处理异常", currentPage, totalPages, e);
                }
            }
            
            log.info("批量处理完成，成功页数：{}, 失败页数：{}", processedCount, errorCount);
            return DataResponse.success("批量处理完成，成功：" + processedCount + "页，失败：" + errorCount + "页");
            
        } catch (Exception e) {
            log.error("批量处理MKU扩展属性数据时发生异常", e);
            return DataResponse.error("批量处理失败：" + e.getMessage());
        }
    }

    /**
     * 分页处理SpuDraft扩展属性数据
     * 将extProperty字段转换为新格式的groupExtAttribute
     * 
     * @param pageIndex 页码，从1开始
     * @param pageSize 每页大小
     * @return 处理结果
     */
    public DataResponse<String> processSpuDraftExtProperties(String updateTime,Long pageIndex, Long pageSize) {
        // 参数校验
        if (pageIndex == null || pageIndex < 1) {
            return DataResponse.error("页码必须大于等于1");
        }
        if (pageSize == null || pageSize < 1 || pageSize > 1000) {
            return DataResponse.error("页大小必须在1-1000之间");
        }
        
        log.info("开始处理第{}页SpuDraft扩展属性数据，每页{}条", pageIndex, pageSize);
        
        try {
            // 1. 获取当前页的SpuDraft数据
            List<SpuDraftPO> spuDraftList = spuDraftAtomicService.listAllSpuDraftWithPage(updateTime, pageIndex, pageSize);
            
            if (CollectionUtils.isEmpty(spuDraftList)) {
                log.info("第{}页无SpuDraft数据", pageIndex);
                return DataResponse.success("当前页无数据");
            }
            
            log.info("获取到{}条SpuDraft数据", spuDraftList.size());
            
            int successCount = 0;
            int errorCount = 0;
            
            for (SpuDraftPO spuDraftPO : spuDraftList) {
                try {
                    if (handleSpuDraftPO(spuDraftPO)) continue;

                    successCount++;
                    
                } catch (Exception e) {
                    errorCount++;
                    log.error("处理SpuId: {} 的SpuDraft扩展属性时发生异常", spuDraftPO.getSpuId(), e);
                }
            }
            
            log.info("第{}页处理完成，成功：{}, 失败：{}", pageIndex, successCount, errorCount);
            return DataResponse.success("处理完成，成功：" + successCount + "，失败：" + errorCount);
                                      
        } catch (Exception e) {
            log.error("处理SpuDraft扩展属性数据时发生异常，页码：{}, 页大小：{}", pageIndex, pageSize, e);
            return DataResponse.error("处理失败：" + e.getMessage());
        }
    }

    /**
     * 处理单条SpuDraft扩展属性数据
     * @return
     */
    @Override
    public DataResponse<String>  processSpuDraftExtAttributesBySpuId(Long spuId) {
        SpuDraftPO spuDraftPO = spuDraftAtomicService.getBySpuId(spuId);
        if (spuDraftPO == null) {
            log.error("SpuId: {} 的SpuDraft不存在", spuId);
            return DataResponse.error("SpuId: " + spuId + " 的SpuDraft不存在");
        }
        handleSpuDraftPO(spuDraftPO);
        return DataResponse.success();
    }


    private boolean handleSpuDraftPO(SpuDraftPO spuDraftPO) {
        // 检查extProperty是否非空
        if (StringUtils.isBlank(spuDraftPO.getExtProperty())) {
            log.debug("SpuId: {} 的扩展属性为空，跳过处理", spuDraftPO.getSpuId());
            return true;
        }
        String extProperty = spuDraftPO.getExtProperty();

        // 2. 解析extProperty JSON字符串为List<PropertyVO>
        List<PropertyVO> propertyList;
        try {
            propertyList = JSON.parseObject(spuDraftPO.getExtProperty(), new TypeReference<List<PropertyVO>>() {});
        } catch (Exception e) {
            log.error("SpuId: {} 的extProperty JSON解析失败", spuDraftPO.getSpuId(), e);
            return true;
        }

        if (CollectionUtils.isEmpty(propertyList)) {
            log.debug("SpuId: {} 解析后的属性列表为空，跳过处理", spuDraftPO.getSpuId());
            return true;
        }

        // 3. 根据catId查询属性详情（使用缓存）
        List<AttributeFlatVO> attributeFlatList = queryExtAttrDetailWithCache(spuDraftPO.getJdCatId(), "zh");
        log.info("类目{}的扩展属性：{}", spuDraftPO.getJdCatId(), JSON.toJSONString(attributeFlatList));
        if (CollectionUtils.isEmpty(attributeFlatList)) {
            log.debug("CatId: {} 的扩展属性配置为空，跳过处理", spuDraftPO.getJdCatId());
            return true;
        }

        // 4. 创建属性ID到AttributeFlatVO的映射
        Map<Long, AttributeFlatVO> attributeMap = attributeFlatList.stream()
                .collect(Collectors.toMap(attr -> attr.getId(), attr -> attr, (v1, v2) -> v1));

        // 存储SPU级别属性列表
        List<PropertyVO> spuLevelProperties = Lists.newArrayList();
        // 存储SKU级别属性列表
        List<PropertyVO> skuLevelProperties = Lists.newArrayList();

        // 5. 补齐PropertyVO的属性信息并分类
        for (PropertyVO propertyVO : propertyList) {
            AttributeFlatVO attributeFlatVO = attributeMap.get(propertyVO.getAttributeId());
            if (attributeFlatVO == null) {
                log.warn("SpuId: {}, AttributeId: {} 在类目属性中未找到对应配置",
                        spuDraftPO.getSpuId(), propertyVO.getAttributeId());
                continue;
            }

            // 补齐基本属性信息
            propertyVO.setComGroupId(attributeFlatVO.getComGroupId());
            propertyVO.setComGroupName(attributeFlatVO.getComGroupName());
            propertyVO.setLevel(attributeFlatVO.getLevel());

            // 补齐属性值信息
            if (CollectionUtils.isNotEmpty(propertyVO.getPropertyValueVOList()) &&
                    CollectionUtils.isNotEmpty(attributeFlatVO.getAttributeValueList())) {

                // 创建属性值ID到AttributeValueFlatVO的映射
                Map<Long, AttributeValueFlatVO> valueMap = attributeFlatVO.getAttributeValueList()
                        .stream()
                        .collect(Collectors.toMap(val -> val.getId(), val -> val, (v1, v2) -> v1));

                for (PropertyValueVO propertyValueVO : propertyVO.getPropertyValueVOList()) {
                    AttributeValueFlatVO valueFlatVO = valueMap.get(propertyValueVO.getAttributeValueId());
                    if (valueFlatVO != null) {
                        // 如果原来没有设置属性值ID，则设置
                        if (propertyValueVO.getAttributeValueId() == null) {
                            propertyValueVO.setAttributeValueId(valueFlatVO.getId());
                        }
                        // 如果原来没有设置属性值名称，则设置
                        if (StringUtils.isBlank(propertyValueVO.getAttributeValueName())) {
                            propertyValueVO.setAttributeValueName(valueFlatVO.getLangName());
                        }
                    }
                }
            }

            // 6. 根据level分类存储（level=0为SPU级别，其他为SKU级别）
            if (PropertyVO.ONLY_SPU_LEVEL_EXT_ATTR_VALUE.equals(attributeFlatVO.getLevel())) {
                spuLevelProperties.add(propertyVO);
            } else {
                skuLevelProperties.add(propertyVO);
            }
        }

        String groupExtAttribute = "";
        String spuGroupExtAttribute = "";
        // 7. 调用spuConvertService转换并更新SpuDraft
        if (CollectionUtils.isNotEmpty(spuLevelProperties)) {
            spuGroupExtAttribute = spuConvertService.getGroupExtAttribute(spuLevelProperties);

            // 更新SpuDraft的groupExtAttribute字段
            SpuDraftPO updateSpuDraftPO = new SpuDraftPO();
            updateSpuDraftPO.setId(spuDraftPO.getId());
            updateSpuDraftPO.setGroupExtAttribute(spuGroupExtAttribute);
            updateSpuDraftPO.setUpdater("system");
            spuDraftAtomicService.updateById(updateSpuDraftPO);

            log.debug("更新SpuId: {} 的SpuDraft groupExtAttribute字段", spuDraftPO.getSpuId());
        }

        // 8. 处理SKU级别属性
        if (CollectionUtils.isNotEmpty(skuLevelProperties)) {
            String skuGroupExtAttribute = spuConvertService.getGroupExtAttribute(skuLevelProperties);
            groupExtAttribute=ExtendPropertyGroupVO.serializeExtendPropertyGroups(ExtendPropertyGroupVO.mergeAndDeserializeExtendPropertyGroups(spuGroupExtAttribute, skuGroupExtAttribute));
            // 根据spuId查询所有SkuDraft
            List<SkuDraftPO> skuDraftList = skuDraftAtomicService.getBySpuId(spuDraftPO.getSpuId());
            if (CollectionUtils.isNotEmpty(skuDraftList)) {
                // 批量更新SkuDraft的groupExtAttribute字段
                List<SkuDraftPO> updateSkuDraftList = Lists.newArrayList();
                for (SkuDraftPO skuDraftPO : skuDraftList) {
                    SkuDraftPO updateSkuDraftPO = new SkuDraftPO();
                    updateSkuDraftPO.setId(skuDraftPO.getId());
                    updateSkuDraftPO.setGroupExtAttribute(skuGroupExtAttribute);
                    updateSkuDraftPO.setUpdater("system");
                    updateSkuDraftList.add(updateSkuDraftPO);
                }

                skuDraftAtomicService.updateBatchById(updateSkuDraftList);
                log.debug("更新SpuId: {} 下的{}个SkuDraft的groupExtAttribute字段", spuDraftPO.getSpuId(), updateSkuDraftList.size());
            }
        }
        if(StringUtils.isBlank(groupExtAttribute)){
            groupExtAttribute = spuGroupExtAttribute;
        }
        // 校验
        boolean isValid = validateSpuDraftData(spuDraftPO.getSpuId(),extProperty,groupExtAttribute);
        if(!isValid){
            log.error("--------------------------------spuId: {} 的groupExtAttribute字段转换结果验证失败，extProperty: {}, groupExtAttribute: {}", spuDraftPO.getSpuId(),extProperty,groupExtAttribute);
        }
        return false;
    }

    /**
     * 批量处理所有SpuDraft扩展属性数据
     * 将extProperty字段转换为新格式的groupExtAttribute
     * 
     * SpuDraft扩展属性处理流程：
     * 1. 分页读取SpuDraft数据 (extProperty ≠ '' && groupExtAttribute is null)
     * 2. 解析 extProperty JSON → List<PropertyVO>  
     * 3. 根据catId查询属性配置 (缓存)
     * 4. 补齐PropertyVO的完整信息
     * 5. 根据level分类：level=0放到spudraftlist，其他放到skudraftlist
     * 6. 转换并更新SpuDraft和SkuDraft的groupExtAttribute字段
     * 
     * @return 处理结果
     */
    @Override
    public DataResponse<String> processAllSpuDraftExtProperties(String updateTime) {
        if(StringUtils.isBlank(updateTime)){
            updateTime = UPDATE_TIME;
        }
        int batchSize = 50;

        log.info("开始批量处理所有SpuDraft扩展属性数据，批大小：{}", batchSize);
        
        try {
            // 获取总数
            long totalCount = spuDraftAtomicService.listAllSpuDraftTotal(updateTime);
            if (totalCount == 0) {
                return DataResponse.success("无数据需要处理");
            }
            
            long totalPages = (totalCount + batchSize - 1) / batchSize;
            log.info("总记录数：{}, 总页数：{}", totalCount, totalPages);
            
            int processedCount = 0;
            int errorCount = 0;
            
            for (long currentPage = 1; currentPage <= totalPages; currentPage++) {
                try {
                    DataResponse<String> result = this.processSpuDraftExtProperties(updateTime, currentPage, (long) batchSize);
                    if (result.getSuccess()) {
                        processedCount++;
                        log.info("第{}/{}页处理完成", currentPage, totalPages);
                    } else {
                        errorCount++;
                        log.error("第{}/{}页处理失败：{}", currentPage, totalPages, result.getMessage());
                    }
                    
                    // 添加适当的延时，避免对数据库造成过大压力
                    Thread.sleep(sleepTime);
                    
                } catch (Exception e) {
                    errorCount++;
                    log.error("第{}/{}页处理异常", currentPage, totalPages, e);
                }
            }
            
            log.info("批量处理完成，成功页数：{}, 失败页数：{}", processedCount, errorCount);
            return DataResponse.success("批量处理完成，成功：" + processedCount + "页，失败：" + errorCount + "页");
            
        } catch (Exception e) {
            log.error("批量处理SpuDraft扩展属性数据时发生异常", e);
            return DataResponse.error("批量处理失败：" + e.getMessage());
        }
    }

    /**
     * 验证单个SPU数据的转换正确性
     */
    private boolean validateSpuData(Long spuId,String extAttribute,String groupExtAttribute) {
        try {
            // 解析原字段extAttribute
            Set<String> originalAttributeIds = Sets.newHashSet();
            Set<String> originalValueIds = Sets.newHashSet();
            
            // extAttribute格式：attributeId:valueId1,attributeId:valueId2#attributeId2:EN^valueName
            String[] attributeGroups = extAttribute.split("#");
            for (String attributeGroup : attributeGroups) {
                String[] attributeValues = attributeGroup.split(",");
                for (String attributeValue : attributeValues) {
                    if (attributeValue.contains(":")) {
                        String[] parts = attributeValue.split(":");
                        String attributeId = parts[0];
                        originalAttributeIds.add(attributeId);
                        
                        if (parts.length > 1) {
                            String valueInfo = parts[1];
                            // 处理文本属性（包含^）和普通属性
                            if (valueInfo.contains("^")) {
                                // 文本属性，不需要验证valueId
                            } else {
                                // 普通属性值ID
                                originalValueIds.add(valueInfo);
                            }
                        }
                    }
                }
            }
            
            // 解析新字段groupExtAttribute
            Set<String> newAttributeIds = Sets.newHashSet();
            Set<String> newValueIds = Sets.newHashSet();
            
            try {
                List<ExtendPropertyGroupVO> extendGroups = ExtendPropertyGroupVO.obtainExtendPropertyGroupVOList(groupExtAttribute);
                for (ExtendPropertyGroupVO group : extendGroups) {
                    if (group.getExtendPropertyList() != null) {
                        for (ShotPropertyVO property : group.getExtendPropertyList()) {
                            if (property.getAttributeId() != null) {
                                newAttributeIds.add(property.getAttributeId().toString());
                                
                                if (property.getPropertyValueVOList() != null) {
                                    for (ShotPropertyValueVO propertyValue : property.getPropertyValueVOList()) {
                                        if (propertyValue.getAttributeValueId() != null) {
                                            newValueIds.add(propertyValue.getAttributeValueId().toString());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("SpuId: {} 的groupExtAttribute不是有效的JSON数组格式: {}", spuId, groupExtAttribute);
                return false;
            }
            
            // 验证JSON数组格式
            if (!isValidJsonArray(groupExtAttribute)) {
                log.error("SpuId: {} 的groupExtAttribute不是JSON数组格式: {}", spuId, groupExtAttribute);
                return false;
            }
            
            // 检查原字段的属性ID是否都能在新字段中找到
            Set<String> missingAttributeIds = Sets.difference(originalAttributeIds, newAttributeIds);
            if (!missingAttributeIds.isEmpty()) {
                log.error("SpuId: {} 缺少属性ID: 原字段={}, 新字段={}, 缺少={}", 
                        spuId, originalAttributeIds, newAttributeIds, missingAttributeIds);
                return false;
            }
            
            // 检查原字段的属性值ID是否都能在新字段中找到（排除文本属性）
            Set<String> missingValueIds = Sets.difference(originalValueIds, newValueIds);
            if (!missingValueIds.isEmpty()) {
                log.error("SpuId: {} 缺少属性值ID: 原字段值={}, 新字段值={}, 缺少={}", 
                        spuId, originalValueIds, newValueIds, missingValueIds);
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("验证SpuId: {} 数据时发生异常", spuId, e);
            return false;
        }
    }

    /**
     * 验证单个MKU数据的转换正确性
     */
    private boolean validateMkuData(Long mkuId,String extAttribute,String groupExtAttribute) {
        try {
            // 解析原字段extAttribute（格式与SPU相同）
            Set<String> originalAttributeIds = Sets.newHashSet();
            Set<String> originalValueIds = Sets.newHashSet();
            
            String[] attributeGroups = extAttribute.split("#");
            for (String attributeGroup : attributeGroups) {
                String[] attributeValues = attributeGroup.split(",");
                for (String attributeValue : attributeValues) {
                    if (attributeValue.contains(":")) {
                        String[] parts = attributeValue.split(":");
                        String attributeId = parts[0];
                        originalAttributeIds.add(attributeId);
                        
                        if (parts.length > 1) {
                            String valueInfo = parts[1];
                            if (valueInfo.contains("^")) {
                                // 文本属性
                            } else {
                                originalValueIds.add(valueInfo);
                            }
                        }
                    }
                }
            }
            
            // 解析新字段groupExtAttribute
            Set<String> newAttributeIds = Sets.newHashSet();
            Set<String> newValueIds = Sets.newHashSet();
            
            try {
                List<ExtendPropertyGroupVO> extendGroups = ExtendPropertyGroupVO.obtainExtendPropertyGroupVOList(groupExtAttribute);
                for (ExtendPropertyGroupVO group : extendGroups) {
                    if (group.getExtendPropertyList() != null) {
                        for (ShotPropertyVO property : group.getExtendPropertyList()) {
                            if (property.getAttributeId() != null) {
                                newAttributeIds.add(property.getAttributeId().toString());
                                
                                if (property.getPropertyValueVOList() != null) {
                                    for (ShotPropertyValueVO propertyValue : property.getPropertyValueVOList()) {
                                        if (propertyValue.getAttributeValueId() != null) {
                                            newValueIds.add(propertyValue.getAttributeValueId().toString());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("MkuId: {} 的groupExtAttribute不是有效的JSON数组格式: {}", mkuId, groupExtAttribute);
                return false;
            }
            
            // 验证JSON数组格式
            if (!isValidJsonArray(groupExtAttribute)) {
                log.error("MkuId: {} 的groupExtAttribute不是JSON数组格式: {}", mkuId, groupExtAttribute);
                return false;
            }
            
            // 检查缺失的属性ID和值ID
            Set<String> missingAttributeIds = Sets.difference(originalAttributeIds, newAttributeIds);
            if (!missingAttributeIds.isEmpty()) {
                log.error("MkuId: {} 缺少属性ID: 原字段={}, 新字段={}, 缺少={}", 
                        mkuId, originalAttributeIds, newAttributeIds, missingAttributeIds);
                return false;
            }
            
            Set<String> missingValueIds = Sets.difference(originalValueIds, newValueIds);
            if (!missingValueIds.isEmpty()) {
                log.error("MkuId: {} 缺少属性值ID: 原字段值={}, 新字段值={}, 缺少={}", 
                        mkuId, originalValueIds, newValueIds, missingValueIds);
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("验证MkuId: {} 数据时发生异常", mkuId, e);
            return false;
        }
    }

    /**
     * 验证单个SpuDraft数据的转换正确性
     */
    private boolean validateSpuDraftData(Long spuId,String extProperty,String groupExtAttribute) {
        try {
            // 解析原字段extProperty（JSON格式的List<PropertyVO>）
            Set<String> originalAttributeIds = Sets.newHashSet();
            Set<String> originalValueIds = Sets.newHashSet();
            
            try {
                List<PropertyVO> originalProperties = JSON.parseObject(extProperty, new TypeReference<List<PropertyVO>>() {});
                for (PropertyVO property : originalProperties) {
                    if (property.getAttributeId() != null) {
                        originalAttributeIds.add(property.getAttributeId().toString());
                        
                        if (property.getPropertyValueVOList() != null) {
                            for (PropertyValueVO propertyValue : property.getPropertyValueVOList()) {
                                if (propertyValue.getAttributeValueId() != null) {
                                    originalValueIds.add(propertyValue.getAttributeValueId().toString());
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("SpuDraftId: {} 的extProperty不是有效的JSON格式: {}", spuId, extProperty);
                return false;
            }
            
            // 解析新字段groupExtAttribute
            Set<String> newAttributeIds = Sets.newHashSet();
            Set<String> newValueIds = Sets.newHashSet();
            
            try {
                List<ExtendPropertyGroupVO> extendGroups = ExtendPropertyGroupVO.obtainExtendPropertyGroupVOList(groupExtAttribute);
                for (ExtendPropertyGroupVO group : extendGroups) {
                    if (group.getExtendPropertyList() != null) {
                        for (ShotPropertyVO property : group.getExtendPropertyList()) {
                            if (property.getAttributeId() != null) {
                                newAttributeIds.add(property.getAttributeId().toString());
                                
                                if (property.getPropertyValueVOList() != null) {
                                    for (ShotPropertyValueVO propertyValue : property.getPropertyValueVOList()) {
                                        if (propertyValue.getAttributeValueId() != null) {
                                            newValueIds.add(propertyValue.getAttributeValueId().toString());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("SpuDraftId: {} 的groupExtAttribute不是有效的JSON数组格式: {}", spuId, groupExtAttribute);
                return false;
            }
            
            // 验证JSON数组格式
            if (!isValidJsonArray(groupExtAttribute)) {
                log.error("SpuDraftId: {} 的groupExtAttribute不是JSON数组格式: {}", spuId, groupExtAttribute);
                return false;
            }
            
            // 检查缺失的属性ID和值ID
            Set<String> missingAttributeIds = Sets.difference(originalAttributeIds, newAttributeIds);
            if (!missingAttributeIds.isEmpty()) {
                log.error("SpuDraftId: {} 缺少属性ID: 原字段={}, 新字段={}, 缺少={}", 
                        spuId, originalAttributeIds, newAttributeIds, missingAttributeIds);
                return false;
            }
            
            Set<String> missingValueIds = Sets.difference(originalValueIds, newValueIds);
            if (!missingValueIds.isEmpty()) {
                log.error("SpuDraftId: {} 缺少属性值ID: 原字段值={}, 新字段值={}, 缺少={}", 
                        spuId, originalValueIds, newValueIds, missingValueIds);
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("验证SpuDraftId: {} 数据时发生异常", spuId, e);
            return false;
        }
    }

    /**
     * 验证字符串是否为有效的JSON数组格式
     */
    private boolean isValidJsonArray(String jsonStr) {
        if (StringUtils.isBlank(jsonStr)) {
            return false;
        }
        
        try {
            JSONArray.parseArray(jsonStr);
            return jsonStr.trim().startsWith("[") && jsonStr.trim().endsWith("]");
        } catch (Exception e) {
            return false;
        }
    }

    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;

//    @Resource(name = "devOpsIscCountryAgreementPriceWriteApiService")
//    private IscCountryAgreementPriceWriteApiService iscCountryAgreementPriceWriteApiService;

    @Override
    public void refreshCountryAgreementWarningPrice() {
        long start = System.currentTimeMillis();
        Set<Long> allIds = countryAgreementPriceAtomicService.getAllIds();
        if (CollectionUtils.isEmpty(allIds)) {
            return;
        }
        log.info("初始化预警表. allIds.size={}", allIds.size());

        for (List<Long> ids : Lists.partition(Lists.newArrayList(allIds), 100)) {
            try {
//                iscCountryAgreementPriceWriteApiService.initCountryAgreementWarningPrice(Sets.newHashSet(ids));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        log.info("refreshCountryAgreementWarningPrice, cost: {}", System.currentTimeMillis() - start);
    }


    @Resource
    protected BWareReadRpcService bWareReadRpcService;

    @Override
    public BigDecimal getJdVatRate(Long jdSkuId) {
        BigDecimal jdVatRate = bWareReadRpcService.getJdVatRate(jdSkuId);
        if (jdVatRate == null) {
            log.warn("sku jdVatRate is null, jdSkuId={}", jdSkuId);
            return categoryRpcService.getJdCategoryVatRate(jdSkuId);
        }
        return null;
    }

    @Override
    public BigDecimal getJdSkuVatRate(Long jdSkuId) {
        return bWareReadRpcService.getJdVatRate(jdSkuId);
    }

    @Override
    public BigDecimal getJdCategoryVatRate(Long jdSkuId) {
        return categoryRpcService.getJdCategoryVatRate(jdSkuId);
    }

    @Override
    public void sendInitCustomerWarningPriceMessage(Set<Long> customerDetailIds) {
        if (CollectionUtils.isEmpty(customerDetailIds)) {
            return;
        }
        for (Long customerDetailId : customerDetailIds) {
            priceWarnManagerService.sendMessage(customerDetailId, PriceWarnTypeEnum.CUSTOMER_PRICE.getCode(), CustomerSkuDataSourceTypeEnums.COST.getCode());
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
//        this.refreshCountryAgreementWarningPrice();
    }
}
