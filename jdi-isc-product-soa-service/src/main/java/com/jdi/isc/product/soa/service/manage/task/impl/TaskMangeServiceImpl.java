package com.jdi.isc.product.soa.service.manage.task.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.domain.enums.FileTypeEnum;
import com.jdi.isc.product.soa.domain.enums.TaskBizTypeEnum;
import com.jdi.isc.product.soa.domain.enums.TaskCreateTypeEnum;
import com.jdi.isc.product.soa.domain.enums.TaskStatusEnum;
import com.jdi.isc.product.soa.domain.task.po.TaskPO;
import com.jdi.isc.product.soa.domain.task.vo.TaskReqVO;
import com.jdi.isc.product.soa.domain.task.vo.TaskVO;
import com.jdi.isc.product.soa.service.atomic.task.TaskAtomicService;
import com.jdi.isc.product.soa.service.manage.task.TaskMangeService;
import com.jdi.isc.product.soa.service.mapstruct.task.TaskConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.Date;

/**
 * 任务读写服务
 *
 * <AUTHOR>
 * @date 20240111
 */
@Service
@Slf4j
public class TaskMangeServiceImpl extends BaseManageSupportService<TaskVO, TaskPO> implements TaskMangeService {

    private final static Integer MAX_ERR_CNT = 2;
    private final static Long LIMIT = 1L;

    @Resource
    private TaskAtomicService taskAtomicService;

    @Value("${jdi.isc.task.env}")
    private String env;

    @Override
    public TaskPO saveOrUpdate(TaskVO taskVO) {
        TaskPO input = null;
        boolean res = false;
        try {
            input = TaskConvert.INSTANCE.vo2po(taskVO);
            res = taskAtomicService.save(input);
        }finally {
            log.info("TaskMangeServiceImpl.saveOrUpdate 异步{}任务初始化{},任务Id:{}", taskVO.getBizType(), res, input!=null?input.getId():null);
        }
        return input;
    }

    @Override
    public TaskPO save(String fileName, String pin, String key, TaskBizTypeEnum bizType, TaskCreateTypeEnum taskCreateType) {
        TaskVO task = getTask(fileName, pin, key, bizType, taskCreateType, FileTypeEnum.BATCH_FILE);
        return saveOrUpdate(task);
    }

    @Override
    public TaskPO save(String fileName, String pin, String key, TaskBizTypeEnum bizType, TaskCreateTypeEnum taskCreateType, FileTypeEnum fileType) {
        TaskVO task = getTask(fileName, pin, key, bizType, taskCreateType, fileType);
        return saveOrUpdate(task);
    }

    @Override
    public Boolean update(TaskVO taskVO) {
        LambdaUpdateWrapper<TaskPO> wrapper = Wrappers.<TaskPO>lambdaUpdate()
                .eq(TaskPO::getId, taskVO.getId())
                .set(TaskPO::getStatus, taskVO.getStatus())
                .set(StringUtils.isNotBlank(taskVO.getResFileUrl()), TaskPO::getResFileUrl, taskVO.getResFileUrl());
        return taskAtomicService.update(wrapper);
    }

    @Override
    public PageInfo<TaskVO> page(TaskReqVO input) {
        Page<TaskPO> page = new Page<>(input.getIndex(), input.getSize());
        LambdaQueryWrapper<TaskPO> wrapper = Wrappers.<TaskPO>lambdaQuery()
                .eq(input.getId() != null, TaskPO::getId, input.getId())
                .eq(input.getCreator() != null, TaskPO::getCreator, input.getCreator())
                .eq(input.getBizType() != null, TaskPO::getBizType, input.getBizType())
                .in(CollectionUtils.isNotEmpty(input.getStatus()), TaskPO::getStatus, input.getStatus())
                .eq(input.getCreateType() != null, TaskPO::getCreateType, input.getCreateType())
                // 按环境隔离数据
                .eq(StringUtils.isNotBlank(env) ,TaskPO::getEnv , env)
                .like(StringUtils.isNotBlank(input.getTaskName()),TaskPO::getName,input.getTaskName())
                .orderByDesc(TaskPO::getCreateTime);
        Page<TaskPO> dbRecord = taskAtomicService.page(page, wrapper);
        return pageTransform(dbRecord, TaskConvert.INSTANCE.listPo2vo(dbRecord.getRecords()));
    }

    @Override
    public Page<TaskPO> hashList(TaskReqVO taskReqVO) {
        Page<TaskPO> page = Page.of(taskReqVO.getIndex(), taskReqVO.getSize());
        LambdaQueryWrapper<TaskPO> wrapper = Wrappers.<TaskPO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(taskReqVO.getStatus()), TaskPO::getStatus, taskReqVO.getStatus())
                .in(CollectionUtils.isNotEmpty(taskReqVO.getBizTypeList()), TaskPO::getBizType, taskReqVO.getBizTypeList())
                .eq(StringUtils.isNotBlank(taskReqVO.getEnv()), TaskPO::getEnv, taskReqVO.getEnv())
                .le(taskReqVO.getErrCnt() != null, TaskPO::getErrCnt, taskReqVO.getErrCnt())
                .apply("mod(id," + taskReqVO.getShardTotal() + ") =" + taskReqVO.getShardIndex() + " ")
                .orderByAsc(TaskPO::getCreateTime);
        return taskAtomicService.page(page, wrapper);
    }


    private TaskVO getTask(String fileName, String pin, String key, TaskBizTypeEnum bizType, TaskCreateTypeEnum taskCreateType, FileTypeEnum fileType) {
        Date now = new Date();
        TaskVO res = new TaskVO();
        res.setName(bizType.getName());
        res.setBizType(bizType);
        res.setStatus(TaskStatusEnum.PENDING);
        res.setCreateType(taskCreateType);
        res.setReqFileName(fileName);
        res.setReqFileUrl(TaskCreateTypeEnum.IMPORT.equals(taskCreateType)?key:bizType.getName());
        res.setReqParam(TaskCreateTypeEnum.IMPORT.equals(taskCreateType)?(fileType.getPath() + File.separator + key):key);
        res.setEnv(env);
        res.setCreator(pin);
        res.setUpdater(pin);
        res.setCreateTime(now);
        res.setUpdateTime(now);
        log.info("CategoryUploadService.getTask pin:{} , res:{}", pin, JSON.toJSONString(res));
        return res;
    }

    @Override
    public Boolean removeTask(Long id) {
        return taskAtomicService.removeById(id);
    }

    @Override
    public Boolean error(TaskVO taskInfo) {
        if (taskInfo.getId() == null) {
            return false;
        }
        LambdaUpdateWrapper<TaskPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TaskPO::getId, taskInfo.getId());
        // 增加记录异常信息
        updateWrapper.set(TaskPO::getStatus, TaskStatusEnum.FAILURE).set(StringUtils.isNotBlank(taskInfo.getRemark()),TaskPO::getRemark, taskInfo.getRemark());
        updateWrapper.setSql("err_cnt = err_cnt + " + 1);
        return taskAtomicService.update(updateWrapper);
    }


    @Override
    public TaskPO getTaskPo(Long taskId) {
        LambdaQueryWrapper<TaskPO> wrapper = Wrappers.lambdaQuery(TaskPO.class).eq(TaskPO::getId, taskId).eq(TaskPO::getYn, YnEnum.YES.getCode());
        return taskAtomicService.getOne(wrapper);
    }
}
