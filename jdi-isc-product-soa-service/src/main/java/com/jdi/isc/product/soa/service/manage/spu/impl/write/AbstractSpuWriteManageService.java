package com.jdi.isc.product.soa.service.manage.spu.impl.write;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.aggregate.read.api.price.res.JdiSkuPriceResDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.SpuStatusEnum;
import com.jdi.isc.product.soa.api.specialAttr.biz.SpecialAttrExcelReq;
import com.jdi.isc.product.soa.api.specialAttr.biz.SpecialAttrReq;
import com.jdi.isc.product.soa.api.spu.req.SpuLangApiDTO;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.exception.SkuPriceException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.domain.common.biz.BaseLangVO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPricePO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDraftPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPricePO;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.mku.MkuStatusEnum;
import com.jdi.isc.product.soa.domain.enums.sku.SkuWeightSourceEnum;
import com.jdi.isc.product.soa.domain.gms.req.JdProductQueryDTO;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceReqVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceVO;
import com.jdi.isc.product.soa.domain.price.supplierPrice.po.SkuPriceDraftPO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuLogPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.specialAttr.res.SpecialAttrVO;
import com.jdi.isc.product.soa.domain.specialAttr.res.SpecialAttrValueVO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.*;
import com.jdi.isc.product.soa.price.api.jdPrice.res.IscEachSkuPriceResDTO;
import com.jdi.isc.product.soa.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.product.soa.rpc.vcs.VendorPriceRpcService;
import com.jdi.isc.product.soa.service.atomic.category.CategoryBuyerRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.specialAttr.SpecialAttrAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.*;
import com.jdi.isc.product.soa.service.atomic.supplier.SupplierSettlementAccountAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseSkuAtomicService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuTaxPriceManageService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeManageService;
import com.jdi.isc.product.soa.service.manage.price.domestic.DomesticSkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuWriteManageService;
import com.jdi.isc.product.soa.service.manage.specialAttr.SpecialAttrManageService;
import com.jdi.isc.product.soa.service.manage.spu.*;
import com.jdi.isc.product.soa.service.manage.spu.validate.SkuValidateService;
import com.jdi.isc.product.soa.service.manage.spu.validate.SpuValidateService;
import com.jdi.isc.product.soa.service.manage.stock.SkuStockManageService;
import com.jdi.isc.product.soa.service.manage.stock.StockManageService;
import com.jdi.isc.product.soa.service.mapstruct.ProductAttributeConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SkuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuConvert;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import com.jdi.isc.product.soa.service.support.DataResponseMessageService;
import com.jdi.isc.product.soa.service.support.ProductIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant.SPU_AUDIT_LOCK_FAILED;


/**
 * <AUTHOR>
 * @date 2023/12/11
 **/
@Slf4j
@Service
public abstract class AbstractSpuWriteManageService implements SpuWriteManageService {

    @Resource
    private SpuAuditRecordManageService spuAuditRecordManageService;

    @Resource
    private SpuConvertService spuConvertService;

    @Resource
    private SpuValidateService spuValidateService;

    @Resource
    private SkuConvertService skuConvertService;

    @Resource
    private ProductIdGenerator productIdGenerator;

    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private SpuLangAtomicService spuLangAtomicService;

    @Resource
    private SpuDescLangAtomicService spuDescLangAtomicService;

    @Resource
    private SpuCertificateAtomicService spuCertificateAtomicService;

    @Resource
    private SpuDraftManageService spuDraftManageService;

    @Resource
    private SkuWriteManageService skuWriteManageService;

    @Resource
    private SkuPriceManageService skuPriceManageService;

    @Resource
    private SkuStockManageService skuStockManageService;

    @Resource
    private DataResponseMessageService dataResponseMessageService;

    @Resource
    private SkuValidateService skuValidateService;

    @Resource
    protected SpuCompareService spuCompareService;

    @Resource
    private JimUtils jimUtils;

    @Resource
    protected SkuAtomicService skuAtomicService;

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @Resource
    private MkuAtomicService mkuAtomicService;

    @Resource
    private CountryManageService countryManageService;

    @Resource
    private SaleAttributeManageService saleAttributeManageService;

    @Value("${spring.profiles.active}")
    private String systemProfile;

    @Resource
    private WarehouseAtomicService warehouseAtomicService;

    @Resource
    private StockManageService stockManageService;

    @Resource
    private WarehouseSkuAtomicService warehouseSkuAtomicService;

    @Resource
    private SkuLogAtomicService skuLogAtomicService;

    @Resource
    private ProductAttributeConvertService productAttributeConvertService;

    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;

    @Resource
    private SpecialAttrAtomicService specialAttrAtomicService;

    @Resource
    private SpecialAttrManageService specialAttrManageService;

    @Resource
    @Lazy
    private CustomerSkuPriceAtomicService customerSkuPriceAtomicService;

    @Resource
    @Lazy
    private CustomerSkuPriceDraftAtomicService customerSkuPriceDraftAtomicService;

    @Resource
    @Lazy
    private SkuPriceDraftAtomicService skuPriceDraftAtomicService;

    @Resource
    @Lazy
    private CustomerMkuPriceAtomicService customerMkuPriceAtomicService;

    @Resource
    @Lazy
    protected Map<String, SkuTaxPriceManageService> skuTaxPriceManageServiceMap;
    @Resource
    private SupplierSettlementAccountAtomicService supplierSettlementAccountAtomicService;

    @Resource
    private ProductMessageService productMessageService;

    @Resource
    protected CategoryBuyerRelationAtomicService categoryBuyerRelationAtomicService;

    @Resource
    private SkuInfoRpcService skuInfoRpcService;

    @Resource
    private DomesticSkuPriceManageService domesticSkuPriceManageService;

    @Resource
    private SkuDraftAtomicService skuDraftAtomicService;

    /**
     * 图片前缀
     */
    @LafValue("jdi.isc.sku.img.origin.prefix")
    private Set<String> imgPrefixSet;

    /**
     * 发品
     * 1、本地、跨境工厂区分
     * 2、spu基本信息处理
     * 3、扩展属性处理
     * 4、发号器生成spuId，spu信息入库
     * 5、详描
     * 6、资质
     * 7、sku销售属性处理，发号器批量生成skuId，sku信息入库
     * 8、初始化价格、库存
     * 9、保存完全成功后返回数据
     */
    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Long> create(SaveSpuVO saveSpuVO) {
        try {
            // 校验参数
            this.validateSaveParams(saveSpuVO);
            // 校验sku是否存在
            this.checkSkuList(saveSpuVO);
            List<SkuVO> skuVoList = saveSpuVO.getSkuVOList();
            this.validateCreateSkuVoList(skuVoList,saveSpuVO.getSpuVO());
            // 发品默认待审核
            if (Objects.isNull(saveSpuVO.getSpuVO().getSpuStatus())) {
                saveSpuVO.getSpuVO().setSpuStatus(SpuStatusEnum.ON_SALE.getStatus());
            }
            // 保存spu
            this.createSpu(saveSpuVO);
            // 处理商品详描
            this.saveOrUpdateSpuDescLang(saveSpuVO);
            // 处理商品资质
            this.initSpuCertificate(saveSpuVO);
            // 处理跨境属性
            this.initSpuGlobalAttribute(saveSpuVO);
            // 保存SKU信息
            // sku补充spu信息
            this.fillSpuInfoToSkuVo(saveSpuVO, skuVoList);
            // AbstractSpuWriteManageService.create 发跨境品时根据jdSkuId查询中台获取jdMainSkuId和jdSpuId
            this.fillCrossBorderSkuMainInfo(skuVoList, saveSpuVO.getSpuVO().getSourceCountryCode());
            // 初始化sku数组
            skuWriteManageService.addSkus(skuVoList);

            // 保存价格
            String sourceCountryCode = saveSpuVO.getSpuVO().getSourceCountryCode();
            this.initSkuPrice(skuVoList, sourceCountryCode);
            // 保存库存
            skuConvertService.initSkuStock(skuVoList, saveSpuVO.getSpuVO());
            // 插入新供应商
            this.recordVendor(skuVoList);
            // 增加草稿
            this.saveOrUpdateDraft(saveSpuVO);

            // 需要确保草稿生成后再调用跨境品销售属性生成，因为销售属性生成需要草稿中skuKey信息
            if(saveSpuVO.isCrossBorder()){
                // 跨境品：正式商品skuId生成后，保存销售属性  ZHAOYAN_SALE_ATTR
                // 注意：跨境品只会在首次创建的时候调用create方法，修改和修改后审核通过都不会调用create方法，更新会调用update和updateForJdm方法，更新spuId和skuId不变的
                this.saveSaleAttributeForCrossBorder(saveSpuVO);
            }else{
                // 本土品：正式商品skuId生成后，为草稿阶段的销售属性关系添加skuId ZHAOYAN_SALE_ATTR
                this.updateDraftSaleAttributeRelationsToFormal(saveSpuVO);
            }

            if(Objects.nonNull(saveSpuVO.getTagKeyId())){
                createSpecialAttr(saveSpuVO,skuVoList);
            }

            // 跨境品先发后审批，发送消息在创建方法中
            if (CountryConstant.COUNTRY_ZH.equals(sourceCountryCode)) {
                saveSpuVO.setCreateFlag(Boolean.TRUE);
                productMessageService.sendMessage(saveSpuVO);
                // 比较上架时的结果
                String compared = spuCompareService.compareCreateSpu(saveSpuVO);
                Long spuId = saveSpuVO.getSpuVO().getSpuId();
                String pin = LoginContextHolder.getLoginContextHolder().getPin();
                addAuditRecord(spuId,pin, AuditStatusEnum.WAITING_APPROVED,compared);
            }

            return DataResponse.success(saveSpuVO.getSpuVO().getSpuId());
        } catch (BizException bizException) {
            log.error("AbstractSpuWriteManageService.create warning saveSpuVO={},message={}", JSON.toJSONString(saveSpuVO), bizException.getMessage());
            throw bizException;
        } catch (SkuPriceException skuPriceException) {
            log.error("AbstractSpuWriteManageService.create warning saveSpuVO={},message={}", JSON.toJSONString(saveSpuVO), skuPriceException.getMessage());
            throw new BizException(skuPriceException.getCode(), skuPriceException.getMsg());
        } catch (Exception e) {
            log.error("【系统异常】创建商品异常,saveSpuVO={}", JSON.toJSONString(saveSpuVO), e);
            String errorMessage = dataResponseMessageService.getErrorMessage(DataResponseCodeConstant.SPU_CREATE_ERROR);
            log.error("【系统异常】AbstractSpuWriteManageService.create error ,errorMessage={}", errorMessage);
//            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】 %s 发品创建商品异常, 异常信息:%s"
//                    , systemProfile
//                    , LevelCode.P1.getMessage()
//                    , e.getMessage()));
            throw new BizException(errorMessage, e);
        }
    }

    /**
     * 保存跨境品销售属性
     *
     * @param saveSpuVO 保存SPU请求对象
     */
    private void saveSaleAttributeForCrossBorder(SaveSpuVO saveSpuVO) {
        try {
            log.info("CrossborderSpuWriteManageServiceImpl.saveSaleAttributeForCrossBorder 开始保存跨境品销售属性, spuId={}", saveSpuVO.getSpuVO().getSpuId());

            Long spuId = saveSpuVO.getSpuVO().getSpuId();
            List<SkuVO> skuVOList = saveSpuVO.getSkuVOList();

            if (spuId == null || CollectionUtils.isEmpty(skuVOList)) {
                log.warn("CrossborderSpuWriteManageServiceImpl.saveSaleAttributeForCrossBorder spuId或SKU列表为空, spuId={}", spuId);
                return;
            }

            for (SkuVO skuVO : skuVOList) {
                if (skuVO.getSkuId() != null && skuVO.getJdSkuId() != null) {
                    Boolean result = saleAttributeManageService.createSkuSaleAttributeForCrossBorder(spuId, skuVO.getSkuId(), skuVO.getJdSkuId()); // ZHAOYAN_SALE_ATTR

                    if (Boolean.TRUE.equals(result)) {
                        log.info("CrossborderSpuWriteManageServiceImpl.saveSaleAttributeForCrossBorder 跨境品销售属性保存成功, spuId={}, skuId={}, jdSkuId={}",
                                spuId, skuVO.getSkuId(), skuVO.getJdSkuId());
                    } else {
                        log.warn("CrossborderSpuWriteManageServiceImpl.saveSaleAttributeForCrossBorder 跨境品销售属性保存失败, spuId={}, skuId={}, jdSkuId={}",
                                spuId, skuVO.getSkuId(), skuVO.getJdSkuId());
                    }
                } else {
                    log.warn("CrossborderSpuWriteManageServiceImpl.saveSaleAttributeForCrossBorder SKU ID或JD SKU ID为空, spuId={}, skuId={}, jdSkuId={}",
                            spuId, skuVO.getSkuId(), skuVO.getJdSkuId());
                }
            }
        } catch (Exception e) {
            log.error("CrossborderSpuWriteManageServiceImpl.saveSaleAttributeForCrossBorder 跨境品销售属性保存异常, spuId={}", saveSpuVO.getSpuVO().getSpuId(), e);
            throw e;
        }
    }

    /**
     * 转换本土品草稿阶段的销售属性关系为正式关系
     *
     * @param saveSpuVO 保存SPU请求对象
     */
    private void updateDraftSaleAttributeRelationsToFormal(SaveSpuVO saveSpuVO) {
        try {
            // 只处理本土品（非跨境品）
            String sourceCountryCode = saveSpuVO.getSpuVO().getSourceCountryCode();
            if (CountryConstant.COUNTRY_ZH.equals(sourceCountryCode)) {
                log.info("AbstractSpuWriteManageService.updateDraftSaleAttributeRelationsToFormal 跨境品跳过销售属性转换, spuId={}", saveSpuVO.getSpuVO().getSpuId());
                return;
            }

            log.info("AbstractSpuWriteManageService.updateDraftSaleAttributeRelationsToFormal 开始转换本土品销售属性关系, spuId={}", saveSpuVO.getSpuVO().getSpuId());

            List<SkuVO> skuVOList = saveSpuVO.getSkuVOList();
            if (CollectionUtils.isEmpty(skuVOList)) {
                log.warn("AbstractSpuWriteManageService.updateDraftSaleAttributeRelationsToFormal SKU列表为空, spuId={}", saveSpuVO.getSpuVO().getSpuId());
                return;
            }

            // 构建skuKey到skuId的映射
            Map<String, Long> skuKeyToSkuIdMap = new HashMap<>();
            for (SkuVO skuVO : skuVOList) {
                if (StringUtils.isNotBlank(skuVO.getSkuKey()) && skuVO.getSkuId() != null) {
                    skuKeyToSkuIdMap.put(skuVO.getSkuKey(), skuVO.getSkuId());
                }
            }

            if (skuKeyToSkuIdMap.isEmpty()) {
                log.warn("AbstractSpuWriteManageService.updateDraftSaleAttributeRelationsToFormal 没有有效的SKU Key到SKU ID映射, spuId={}", saveSpuVO.getSpuVO().getSpuId());
                return;
            }

            Boolean result = saleAttributeManageService.updateDraftSaleAttributeRelationsForLocalSkuApprovel(skuKeyToSkuIdMap); // ZHAOYAN_SALE_ATTR

            if (Boolean.TRUE.equals(result)) {
                log.info("AbstractSpuWriteManageService.updateDraftSaleAttributeRelationsToFormal 本土品销售属性关系转换成功, spuId={}, 转换数量={}",
                        saveSpuVO.getSpuVO().getSpuId(), skuKeyToSkuIdMap.size());
            } else {
                log.warn("AbstractSpuWriteManageService.updateDraftSaleAttributeRelationsToFormal 本土品销售属性关系转换失败, spuId={}", saveSpuVO.getSpuVO().getSpuId());
            }
        } catch (Exception e) {
            log.error("AbstractSpuWriteManageService.updateDraftSaleAttributeRelationsToFormal 本土品销售属性关系转换异常, spuId={}", saveSpuVO.getSpuVO().getSpuId(), e);
            throw e;
        }
    }

    private void createSpecialAttr(SaveSpuVO saveSpuVO,List<SkuVO> skuVoList){
        String orgId = saveSpuVO.getOrgId();
        String pin = saveSpuVO.getPin();
        Long tagKeyId = saveSpuVO.getTagKeyId();
        SpecialAttrReq input = new SpecialAttrReq();
        input.setPin(pin);
        input.setOrgId(orgId);
        List<SpecialAttrVO> specialAttrVOList = specialAttrAtomicService.search(input);
        String attributeKey = null;
        List<String> attributeValueList = new ArrayList<>();
        for (SpecialAttrVO specialAttrVO : specialAttrVOList){
            if(Objects.equals(tagKeyId, specialAttrVO.getId())){
                attributeKey = specialAttrVO.getAttributeKey();
                List<String> attributeValueIdList = specialAttrVO.getValues().stream().map(SpecialAttrValueVO::getAttributeValue).collect(Collectors.toList());;
                attributeValueList.addAll(attributeValueIdList);
            }
        }
        if(StringUtils.isNotBlank(attributeKey) && CollectionUtils.isNotEmpty(attributeValueList)){
            for (String attributeValueId : attributeValueList){
                for (SkuVO skuVO : skuVoList) {
                    saveSpecialAttr(skuVO.getSkuId(),pin,orgId,attributeKey,attributeValueId);
                }
            }
        }
    }

    private void saveSpecialAttr(Long skuId,String pin,String orgId,String attributeKey,String attributeValueId){
        SpecialAttrExcelReq apiDTO = new SpecialAttrExcelReq();
        apiDTO.setPin(pin);
        apiDTO.setOrgId(orgId);
        apiDTO.setSkuId(String.valueOf(skuId));
        apiDTO.setAttributeKey(attributeKey);
        apiDTO.setAttributeValue(attributeValueId);
        DataResponse<String> response = specialAttrManageService.saveOrUpdate(apiDTO);
        if(!response.getSuccess()){
            throw new BizException(response.getMessage());
        }
    }

    /**
     * 修改
     * 1、sku销售属性校验和对比，数据入库
     * 2、更新商品状态为“待审核”
     * 3、增加一条待审核记录
     * 4、保存草稿
     */
    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Long> update(SaveSpuVO saveSpuVO) {
        final Long spuId = saveSpuVO.getSpuVO().getSpuId();
        String erp = LoginContextHolder.getLoginContextHolder().getPin();

        String requestId = UUID.randomUUID().toString();
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.SPU_AUDIT_OPERATE_LOCK_PRE, String.valueOf(spuId));
        try {
            AssertValidation.isTrue(!jimUtils.simpleLock(lockKey, requestId, 10), SPU_AUDIT_LOCK_FAILED, "加锁失败");
            // 校验参数
            this.validateSaveParams(saveSpuVO);
            // 校验sku是否存在
            this.checkSkuList(saveSpuVO);
            Boolean isUpdateJdSkuId = null;

            // 校验销跨境品售属性  ZHAOYAN_SALE_ATTR
            if(saveSpuVO.isCrossBorder()) {
                // 判断是否修改了jdSkuId
                isUpdateJdSkuId = this.isUpdateJdSkuId(saveSpuVO);
                this.validateSaleAttributeUpdate(isUpdateJdSkuId, saveSpuVO.getSpuVO(), saveSpuVO.getSkuVOList());
            }

            this.validateUpdateSkuVoList(saveSpuVO.getSkuVOList(), saveSpuVO.getSpuVO());
            // 比较保存商品对象，返回修改内容
            saveSpuVO.getSpuVO().setDetailImg(SpuConvert.INSTANCE.detailImage2Str(saveSpuVO.getSpuVO().getDetailImgList()));
            this.validateDbSaveSpuVoAndViewSaveSpuVo(saveSpuVO);

            // 跨境补充待审核状态
            if (Objects.isNull(saveSpuVO.getSpuVO().getSpuStatus()) && CountryConstant.COUNTRY_ZH.equals(saveSpuVO.getSpuVO().getSourceCountryCode())) {
                saveSpuVO.getSpuVO().setSpuStatus(SpuStatusEnum.ON_SALE.getStatus());
            }
            saveSpuVO.getSpuVO().setAuditStatus(AuditStatusEnum.WAITING_APPROVED.getCode());
            // 过滤商品资质
            this.filterSpuCertificate(saveSpuVO);
            // 处理跨境属性
            this.initSpuGlobalAttribute(saveSpuVO);
            // sku补充spu信息
            this.fillSpuInfoToSkuVo(saveSpuVO, saveSpuVO.getSkuVOList());
            // 保存库存
            skuConvertService.initSkuStock(saveSpuVO.getSkuVOList(), saveSpuVO.getSpuVO());

            // 如果是跨境品，更新销售属性值多语言翻译信息 ZHAOYAN_SALE_ATTR
            if(saveSpuVO.isCrossBorder()){
                // 判断是否修改了jdSkuId
                if (Objects.nonNull(isUpdateJdSkuId) && isUpdateJdSkuId) {
                    // 判断是否是excel导入的，没有用户输入销售属性的情况
                    if (saveSpuVO.getSkuVOList() != null && CollectionUtils.isEmpty(saveSpuVO.getSkuVOList().stream().filter(skuVO -> skuVO != null && CollectionUtils.isEmpty(skuVO.getStoreSalePropertyList())).collect(Collectors.toList()))) {
                        // excel导入
                        for (SkuVO skuVO : saveSpuVO.getSkuVOList()) {
                            // 通过skuId恢复skuKey
                            this.recoverSkuKeyFromSkuId(skuVO);
                            saleAttributeManageService.updateSkuSaleAttributeForCrossBorderWithoutUserInput(spuId, skuVO.getSkuId(), skuVO.getJdSkuId());
                            log.info("AbstractSpuWriteManageService.update updateSkuSaleAttributeForCrossBorderWithoutUserInput 修改了jdSkuId, 更新跨境品销售属性 spuId: {}, skuId: {}, jdSkuId: {}", spuId, skuVO.getSkuId(), skuVO.getJdSkuId());
                        }
                    } else {
                        for (SkuVO skuVO : saveSpuVO.getSkuVOList()) {
                            // 通过skuId恢复skuKey
                            this.recoverSkuKeyFromSkuId(skuVO);
                        }
                        saleAttributeManageService.updateSkuSaleAttributeForCrossBorder(spuId, saveSpuVO.getSkuVOList());
                        log.info("AbstractSpuWriteManageService.update updateSkuSaleAttributeForCrossBorder 修改了jdSkuId, 更新跨境品销售属性 spuId: {}", spuId);
                    }
                } else if (Objects.nonNull(isUpdateJdSkuId) && !isUpdateJdSkuId) {
                    saleAttributeManageService.updateSkuSaleAttributeValueLangNameForCrossBorder(saveSpuVO.getSkuVOList());
                    log.info("AbstractSpuWriteManageService.update 没有修改jdSkuId, 仅更新跨境品销售属性值多语言信息 spuId: {}", spuId);
                } else {
                    log.error("AbstractSpuWriteManageService.update isUpdateJdSkuId为空，无法判断是跟新销售属性还是更新销售属性值多语言信息 spuId: {}", spuId);
                }
            }

            // 添加审核记录
            String compareResult = spuCompareService.compareSaveSpu(saveSpuVO);
            log.info("AbstractSpuWriteManageService.update spuId={} 比较结果 compareResult={}", spuId, compareResult);
            this.addAuditRecord(spuId, erp, AuditStatusEnum.WAITING_APPROVED, compareResult);
            // 保存更新草稿
            this.saveOrUpdateDraft(saveSpuVO);
            // spu为“上架”状态时，修改状态为“待审核”
            if(saveSpuVO.getSpuVO().getAuditStatus() != null){
                this.updateStatusToWaitApprove(spuId);
            }

            return DataResponse.buildSuccess(spuId);
        } catch (BizException bizException) {
            log.error("AbstractSpuWriteManageService.update warning saveSpuVO={},message={}", JSON.toJSONString(saveSpuVO), bizException.getMessage());
            throw bizException;
        } catch (Exception e) {
            log.error("【系统异常】保存商品草稿异常,spuId={},erp={}", spuId, erp, e);
            String errorMessage = dataResponseMessageService.getErrorMessage(DataResponseCodeConstant.SPU_UPDATE_ERROR);
            log.error("【系统异常】AbstractSpuWriteManageService.update error ,errorMessage={}", errorMessage);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】 %s 发品修改商品异常, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , e.getMessage()));
            throw new BizException(errorMessage, e);
        } finally {
            jimUtils.simpleLockRelease(lockKey, requestId);
        }
    }

    @Nullable
    private void recoverSkuKeyFromSkuId(SkuVO skuVO) {
        // 因为这种情况用户没有输入销售属性，所以无法恢复在下游根据销售属性恢复skuKey，所以在这里用skuId来恢复skuKey
        Long skuId = skuVO.getSkuId();
        if(skuId==null){
            throw new BizException("跨境品编辑时传入的skuId不能为空");
        }
        // 根据skuId恢复skuKey
        SkuDraftPO sku = skuDraftAtomicService.getValidBySkuId(skuId);
        if(sku == null && StringUtils.isBlank(skuVO.getSkuKey())){
            throw new BizException("跨境品编辑时传入的skuId对应的skuKey不能为空");
        }
        skuVO.setSkuKey(sku.getSkuKey());
    }

    /**
     * 校验销售属性信息
     */
    public void validateSaleAttributeUpdate(Boolean isUpdateJdSkuId, SpuVO spuVO, List<SkuVO> skuVOList) {
        if(Objects.nonNull(isUpdateJdSkuId) && isUpdateJdSkuId){
            // 跨境品更换jdSkuId  sku销售属性校验 ZHAOYAN_SALE_ATTR
            skuValidateService.validateUpdateSkuSaleAttributeJdSkuIdForCrossBorder(spuVO, skuVOList);
        } else if(Objects.nonNull(isUpdateJdSkuId) && !isUpdateJdSkuId){
            // 跨境品更新多语言 sku销售属性校验  ZHAOYAN_SALE_ATTR
            skuValidateService.validateUpdateSkuSaleAttributeValueLangNameForCrossBorder(spuVO, skuVOList);
        }
    }

    /**
     * 仅供跨境品使用，判断是否修改了jdSkuId
     *
     * @param saveSpuVO
     * @return
     */
    public Boolean isUpdateJdSkuId(SaveSpuVO saveSpuVO){
        SpuVO spuVO = saveSpuVO.getSpuVO();
        List<SkuVO> skuVOList = saveSpuVO.getSkuVOList();

        // 基础校验
        if(CollectionUtils.isEmpty(skuVOList) || spuVO == null || spuVO.getSpuId() == null){
            throw new BizException("跨境品sku不能为空");
        }

        // 获取传入的jdSkuId和skuId
        SkuVO targetSkuVO = null;
        for(SkuVO skuVO : skuVOList){
            if(Objects.nonNull(skuVO.getJdSkuId()) && Objects.nonNull(skuVO.getSkuId())){
                targetSkuVO = skuVO;
                break;
            }
        }

        if(targetSkuVO == null){
            throw new BizException("跨境品sku不能为空");
        }

        // 查询数据库中对应的SkuPO
        List<SkuDraftPO> skuPOList = skuDraftAtomicService.getBySpuId(spuVO.getSpuId());
        if(CollectionUtils.isEmpty(skuPOList)){
            throw new BizException("跨境品sku不存在");
        }

        // 找到对应的SkuPO进行比较
        for(SkuDraftPO skuPO : skuPOList){
            if(Objects.equals(skuPO.getSkuId(), targetSkuVO.getSkuId())){
                if(Objects.nonNull(skuPO.getJdSkuId())){
                    // 比较是否发生变化
                    return !Objects.equals(skuPO.getJdSkuId(), targetSkuVO.getJdSkuId());
                } else {
                    // 数据库中原来没有jdSkuId，现在有了，说明是新增，算修改
                    return true;
                }
            }
        }

        // 没找到对应的SkuPO，可能是新增场景
        return true;
    }

    @Override
    public DataResponse<String> batchDown(SpuBatchReqVO spuBatchReqVO){
        String pin = spuBatchReqVO.getPin();
        try {
            // 1、查询商品是否归当前操作人所有、当前商品是否是上架状态，下架状态跳过
            Set<Long> spuIds = spuBatchReqVO.getSpuIds();
            List<SpuPO> spuPOList = spuAtomicService.querySpuList(spuIds, spuBatchReqVO.getVendorCode());
            AssertValidation.isTrue(spuIds.size() > spuPOList.size(), "存在不属于当前供应商的商品ID");
            AssertValidation.isEmpty(spuPOList, "当前商品不存在");
            // 筛选出上架的spu
            List<SpuPO> upSpuList = spuPOList.stream().filter(Objects::nonNull).filter(spu -> SpuStatusEnum.DOWN.getStatus() == spu.getSpuStatus()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(upSpuList)) {
                return DataResponse.success();
            }

            upSpuList.forEach(u -> {
                u.setSpuStatus(SpuStatusEnum.DOWN.getStatus());
                u.setUpdater(pin);
                u.setUpdateTime(new Date());
            });
            // 批量下架spu
            spuAtomicService.saveOrUpdateBatch(upSpuList);
            // 2、下架所有sku
            List<SkuPO> skuPOList = skuAtomicService.querySkuListBySpuIds(upSpuList.stream().map(SpuPO::getSpuId).collect(Collectors.toList()));
            skuPOList.forEach(sku -> {
                sku.setSkuStatus(SpuStatusEnum.DOWN.getStatus());
                sku.setUpdater(pin);
                sku.setUpdateTime(new Date());
            });
            skuAtomicService.saveOrUpdateBatch(skuPOList);
            // TODO 3、关联的mku和客户绑定关系解除
            // 查询固定sku和mku关系  客户编码信息
            // 根据mku和客户编码 调用绑定接口
            return DataResponse.success();
        } catch (BizException bizException) {
            log.error("AbstractSpuWriteManageService.batchDown warning saveSpuVO={},message={}", JSON.toJSONString(spuBatchReqVO), bizException.getMessage());
            throw bizException;
        } catch (Exception e) {
            log.error("【系统异常】批量下柜商品异常,spuBatchReqVO={},pin={}", JSON.toJSONString(spuBatchReqVO), pin, e);
            String errorMessage = dataResponseMessageService.getErrorMessage(DataResponseCodeConstant.SPU_UPDATE_ERROR);
            log.error("【系统异常】AbstractSpuWriteManageService.batchDown error ,errorMessage={}", errorMessage);
            throw new BizException(errorMessage, e);
        }
    }

    /**
     * 批量归档数据
     * @return 返回包含String类型数据的响应
     */
    @Override
    public DataResponse<String> batchArchive(SpuBatchReqVO spuBatchReqVO){
        String pin = spuBatchReqVO.getPin();
        try {
            // 1、查询商品确认是否未当前供应商所有，当前商品是否未下架状态，上架状态报错
            Set<Long> spuIds = spuBatchReqVO.getSpuIds();
            List<SpuPO> spuPOList = spuAtomicService.querySpuList(spuIds, spuBatchReqVO.getVendorCode());
            // 校验商品
            AssertValidation.isTrue(spuIds.size() > spuPOList.size(), "存在不属于当前供应商的商品ID");
            AssertValidation.isEmpty(spuPOList, "当前商品不存在");
            List<String> saleSpuIds = spuPOList.stream().filter(spu -> SpuStatusEnum.ON_SALE.getStatus() == spu.getSpuStatus()).map(String::valueOf).collect(Collectors.toList());
            AssertValidation.isTrue(CollectionUtils.isNotEmpty(saleSpuIds), String.format("商品ID %s 在售状态，请先下架", String.join(Constant.COMMA, saleSpuIds)));
            // 设置SPU yn=0，表示归档
            spuPOList.forEach(po -> {
                po.setUpdater(pin);
                po.setYn(YnEnum.NO.getCode());
                po.setUpdateTime(new Date());
            });
            spuAtomicService.saveOrUpdateBatch(spuPOList);

            // 2、spu、sku、mku、mkuRelation均设置yn=0，归档数据
            List<Long> upSpuIds = saleSpuIds.stream().map(Long::valueOf).collect(Collectors.toList());
            List<SkuPO> skuPOList = skuAtomicService.querySkuListBySpuIds(upSpuIds);
            skuPOList.forEach(skuPO -> {
                skuPO.setUpdater(pin);
                skuPO.setUpdateTime(new Date());
                skuPO.setYn(YnEnum.NO.getCode());
            });
            skuAtomicService.saveOrUpdateBatch(skuPOList);

            // 查询sku关联mku
            List<Long> upSkuIds = skuPOList.stream().map(SkuPO::getSkuId).collect(Collectors.toList());
            List<MkuRelationPO> mkuRelationPOList = mkuRelationAtomicService.queryBindListBySkuIds(upSkuIds);

            if (CollectionUtils.isEmpty(mkuRelationPOList)) {
                return DataResponse.success();
            }

            // 归档mku和mku关联数据
            List<Long> mkuIds = mkuRelationPOList.stream().map(MkuRelationPO::getMkuId).collect(Collectors.toList());
            mkuRelationPOList.forEach(relation -> {
                relation.setMkuId(null);
                relation.setYn(YnEnum.NO.getCode());
                relation.setUpdater(pin);
                relation.setUpdateTime(new Date());
            });
            mkuRelationAtomicService.saveOrUpdateBatch(mkuRelationPOList);


            if (CollectionUtils.isNotEmpty(mkuIds)) {
                List<MkuPO> mkuPOList = mkuAtomicService.listByMkuIds(mkuIds);
                mkuPOList.forEach(mkuPO -> {
                    mkuPO.setYn(YnEnum.NO.getCode());
                    mkuPO.setUpdater(pin);
                    mkuPO.setUpdateTime(new Date());
                });
                mkuAtomicService.saveOrUpdateBatch(mkuPOList);
            }
            return DataResponse.success();
        }  catch (BizException bizException) {
            log.error("AbstractSpuWriteManageService.batchArchive warning saveSpuVO={},message={}", JSON.toJSONString(spuBatchReqVO), bizException.getMessage());
            throw bizException;
        } catch (Exception e) {
            log.error("【系统异常】AbstractSpuWriteManageService.batchArchive 批量归档商品异常,spuBatchReqVO={},pin={}", JSON.toJSONString(spuBatchReqVO), pin, e);
            String errorMessage = dataResponseMessageService.getErrorMessage(DataResponseCodeConstant.SPU_UPDATE_ERROR);
            log.error("【系统异常】AbstractSpuWriteManageService.batchArchive error ,errorMessage={}", errorMessage);
            throw new BizException(errorMessage, e);
        }
    }

    @Override
    //@Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<List<SpuUpdateReqVO>> batchUpdate(List<SpuUpdateReqVO> spuUpdateList){
        log.info("AbstractSpuWriteManageService.batchUpdate req:{}", JSON.toJSONString(spuUpdateList));
        Set<Long> skuIdSet = getSkuIdSet(spuUpdateList);
        Map<Long, SkuPO> skuMap = skuAtomicService.getSkuMap(skuIdSet);
        log.info("AbstractSpuWriteManageService.batchUpdate skuIdSet:{},skuMap:{}", JSON.toJSONString(skuIdSet),JSON.toJSONString(skuMap));

        // 国际SKU ID和京东SKU ID关系
        Map<Long, Long> skuJdSkuMap = Optional.of(spuUpdateList).orElseGet(ArrayList::new).stream().filter(po -> Objects.nonNull(po.getJdSkuId())).collect(Collectors.toMap(SpuUpdateReqVO::getSkuId, SpuUpdateReqVO::getJdSkuId));
        // 查询国内sku的采购价，校验采购价 sunlei61 2025-06-24
        Map<Long, JdProductDTO> jdProductDTOMap = Maps.newHashMap();
        Map<Long, IscEachSkuPriceResDTO> jdSkuPriceMap = Maps.newHashMap();
        if (MapUtils.isNotEmpty(skuJdSkuMap)) {
            jdProductDTOMap = skuInfoRpcService.querySkuMap(new JdProductQueryDTO(Lists.newArrayList(skuJdSkuMap.values())));
            log.info("batchUpdate skuIds = {}, jdProductDTOMap = {}", JSON.toJSONString(skuJdSkuMap), JSON.toJSONString(jdProductDTOMap));
            // 查询新采购价
            jdSkuPriceMap = domesticSkuPriceManageService.queryJdiSkuPrice(Lists.newArrayList(skuJdSkuMap.values()));
            log.info("batchUpdate skuIds = {}, jdSkuPriceMap = {}", JSON.toJSONString(skuJdSkuMap), JSON.toJSONString(jdSkuPriceMap));
        }

        Set<Long> spuIdSet = new HashSet<>();
        List<SpuUpdateReqVO> validSpuUpdateList = new ArrayList<>();
        for(SpuUpdateReqVO spuUpdateReqVO : spuUpdateList){
            SkuPO skuPO = skuMap.get(spuUpdateReqVO.getSkuId());
            if (Objects.isNull(skuPO)) {
                spuUpdateReqVO.failed("商品中无此SKUID");
                continue;
            }
            // 校验图片前缀
            if (!checkImagePrefix(spuUpdateReqVO)) {
                continue;
            }

            if(skuPO.getSourceCountryCode().equals(CountryConstant.COUNTRY_ZH) && !CountryConstant.COUNTRY_ZH.equals(spuUpdateReqVO.getSourceCountryCode())){
                spuUpdateReqVO.failed("此商品是跨境商品，无法在国际品修改");
            }else if (!skuPO.getSourceCountryCode().equals(CountryConstant.COUNTRY_ZH) && CountryConstant.COUNTRY_ZH.equals(spuUpdateReqVO.getSourceCountryCode())){
                spuUpdateReqVO.failed("此商品是国际商品，无法在中国品修改");
            }else if (Objects.nonNull(spuUpdateReqVO.getJdSkuId())){
                checkJdSkuAndPurchasePrice(spuUpdateReqVO, skuPO, jdSkuPriceMap, jdProductDTOMap, spuIdSet, validSpuUpdateList);
            }else {
                Long spuId = skuPO.getSpuId();
                spuUpdateReqVO.setSpuId(spuId);
                spuIdSet.add(spuId);
                validSpuUpdateList.add(spuUpdateReqVO);
            }
        }
        Map<Long, SpuUpdateReqVO> resultMap = spuUpdateList.stream().collect(Collectors.toMap(SpuUpdateReqVO::getSkuId, Function.identity()));
        log.info("AbstractSpuWriteManageService.batchUpdate skuIdSet:{},validSpuUpdateList:{},spuIdSet:{}", JSON.toJSONString(skuIdSet)
            ,JSON.toJSONString(validSpuUpdateList),JSON.toJSONString(spuIdSet));
        if(CollectionUtils.isEmpty(validSpuUpdateList)){
            return DataResponse.success(new ArrayList<>(resultMap.values()));
        }
        this.updateDraftList(validSpuUpdateList,spuIdSet,resultMap);
        return DataResponse.success(new ArrayList<>(resultMap.values()));
    }

    private boolean checkImagePrefix(SpuUpdateReqVO reqVO) {
        if (StringUtils.isNotBlank(reqVO.getMainImg()) && imgPrefixSet.stream().noneMatch(reqVO.getMainImg()::startsWith)) {
            reqVO.failed(String.format("图片链接: [%s] 不是京东内部图片,请使用图床图片链接",reqVO.getMainImg()));
            return Boolean.FALSE;
        }

        if (CollectionUtils.isNotEmpty(reqVO.getDetailImgList()) && reqVO.getDetailImgList().stream().map(String::trim).anyMatch(img-> imgPrefixSet.stream().noneMatch(img::startsWith))) {
            reqVO.failed(String.format("图片链接: [%s] 存在不是京东内部图片,请使用图床图片链接",reqVO.getDetailImgList()));
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 检查京东SKU和采购价格是否合法，并更新SPU信息。
     * @param spuUpdateReqVO SPU更新请求对象，包含京东SKU ID和其他相关信息。
     * @param skuPO SKU持久化对象，用于获取当前SKU的ID和SPU ID。
     * @param jdSkuPriceMap 京东SKU价格映射，用于查找指定京东SKU的采购价格。
     * @param jdProductDTOMap 京东产品映射，用于查找指定京东SKU对应的供应商代码。
     * @param spuIdSet 已经处理过的SPU ID集合，用于去重。
     * @param validSpuUpdateList 有效的SPU更新请求列表，用于存储合法的更新请求。
     */
    private void checkJdSkuAndPurchasePrice(SpuUpdateReqVO spuUpdateReqVO, SkuPO skuPO, Map<Long, IscEachSkuPriceResDTO> jdSkuPriceMap, Map<Long, JdProductDTO> jdProductDTOMap, Set<Long> spuIdSet, List<SpuUpdateReqVO> validSpuUpdateList) {
        // 校验京东SKU ID是否存在
        if(!jdProductDTOMap.containsKey(spuUpdateReqVO.getJdSkuId())) {
            spuUpdateReqVO.failed(String.format("主站SKU ID: [%s]不存在,请检查", spuUpdateReqVO.getJdSkuId()));
            return;
        }
        //  校验国内SKU ID是否已经存在绑定关系
        List<Long> existSkuIds = skuValidateService.validateJdSkuExistSku(spuUpdateReqVO.getJdSkuId(), skuPO.getSkuId());
        if (CollectionUtils.isNotEmpty(existSkuIds)) {
            spuUpdateReqVO.failed(String.format("主站SKU ID: [%s]已绑定国际SKU ID：[%s]，请勿重复二次发品！", spuUpdateReqVO.getJdSkuId(),StringUtils.join(existSkuIds,Constant.COMMA)));
        }else if (MapUtils.isNotEmpty(jdSkuPriceMap)
                && (!jdSkuPriceMap.containsKey(spuUpdateReqVO.getJdSkuId())
                    || Objects.isNull(jdSkuPriceMap.get(spuUpdateReqVO.getJdSkuId()))
                    || Objects.isNull(jdSkuPriceMap.get(spuUpdateReqVO.getJdSkuId()).getJdSkuPurchasePrice()))) {
            // 校验国内SKU ID的采购价是否存在；采购价是否大于当前采购价，大于时提示
            spuUpdateReqVO.failed(String.format("主站SKU ID: [%s]采购价拉取失败，请检查是否维护仓报价、渠道价、通用品溢价率。", spuUpdateReqVO.getJdSkuId()));
        }else {
            IscEachSkuPriceResDTO skuPriceResDTO = jdSkuPriceMap.get(spuUpdateReqVO.getJdSkuId());
            // 京东采购价
            BigDecimal jdSkuPurchasePrice = skuPriceResDTO.getJdSkuPurchasePrice();
            // 跟当前采购价比较大小，如果大于现在的采购价时，返回提示
            spuUpdateReqVO.setPurchasePrice(jdSkuPurchasePrice.toPlainString());
            Long spuId = skuPO.getSpuId();
            spuUpdateReqVO.setSpuId(spuId);
            JdProductDTO jdProductDTO = jdProductDTOMap.get(spuUpdateReqVO.getJdSkuId());
            spuUpdateReqVO.setJdVendorCode(jdProductDTO.getSupplyUnit());
            spuIdSet.add(spuId);
            validSpuUpdateList.add(spuUpdateReqVO);
        }
    }

    private void updateDraftList(List<SpuUpdateReqVO> updateSpuList,Set<Long> spuIdSet,Map<Long, SpuUpdateReqVO> resultMap){
        try {
            List<SaveSpuVO> saveSpuVoList = spuDraftManageService.getSaveSpuVoFromDraftListBySpuIds(spuIdSet);
            Map<Long, List<SpuUpdateReqVO>> updateSpuMap = updateSpuList.stream().collect(Collectors.groupingBy(SpuUpdateReqVO::getSpuId));
            for(SaveSpuVO saveSpuVO : saveSpuVoList){
                List<SkuVO> drafSkuVOList = saveSpuVO.getSkuVOList();
                List<SpuUpdateReqVO> spuUpdateReqVOList = updateSpuMap.get(saveSpuVO.getSpuVO().getSpuId());
                List<SkuVO> resultSkuVOList = convertSkuVOList(spuUpdateReqVOList, drafSkuVOList);
                saveSpuVO.setSkuVOList(resultSkuVOList);
                SpuUpdateReqVO spuUpdateReqVO = spuUpdateReqVOList.get(0);
                SpuVO spuVO = updateSpuVOProperties(spuUpdateReqVO,saveSpuVO.getSpuVO());
                if(CollectionUtils.isNotEmpty(spuUpdateReqVO.getSpuLangList())){
                    List<SpuLangApiDTO> spuLangList = spuUpdateReqVO.getSpuLangList();

                    List<SpuLangExtendVO> spuLangExtendVOList = updateSpuLangList(spuVO.getSpuLangExtendVOList(),spuLangList);
                    spuVO.setSpuLangExtendVOList(spuLangExtendVOList);

                    Map<String, String> pcDescriptionMap = saveSpuVO.getPcDescriptionMap();
                    pcDescriptionMap = updatePcDescription(pcDescriptionMap,spuLangList);
                    saveSpuVO.setPcDescriptionMap(pcDescriptionMap);
                }
                saveSpuVO.setSpuVO(spuVO);
                updateSpuDraftInfo(resultMap,saveSpuVO);
            }
        }catch (Exception e){
            log.error("【系统异常】AbstractSpuWriteManageService.updateDraftList updateSpuList:{},err:{}", JSON.toJSONString(updateSpuList),e.getMessage(),e);
            updateSpuList.forEach(skuPO -> {
                SpuUpdateReqVO spuUpdateReqVO = resultMap.get(skuPO.getSkuId());
                spuUpdateReqVO.setValid(false);
                spuUpdateReqVO.setResult("【系统异常】处理商品Spu草稿信息失败");
                resultMap.put(spuUpdateReqVO.getSkuId(),spuUpdateReqVO);
            } );
            throw new RuntimeException("【系统异常】处理商品Spu草稿信息失败");
        }
    }

    private SpuVO updateSpuVOProperties(SpuUpdateReqVO spuUpdateReqVO, SpuVO spuVO) {
        spuVO.setUpdater(spuUpdateReqVO.getUpdater());
        if (Objects.nonNull(spuUpdateReqVO.getVendorSpuId())) {
            spuVO.setVendorSpuId(spuUpdateReqVO.getVendorSpuId());
        }
        if (Objects.nonNull(spuUpdateReqVO.getSpecification())) {
            spuVO.setSpecification(spuUpdateReqVO.getSpecification());
        }
        if (Objects.nonNull(spuUpdateReqVO.getBrandId())) {
            spuVO.setBrandId(spuUpdateReqVO.getBrandId());
        }
        if (Objects.nonNull(spuUpdateReqVO.getOriginCountry())) {
            spuVO.setOriginCountry(spuUpdateReqVO.getOriginCountry());
        }
        if (Objects.nonNull(spuUpdateReqVO.getSaleUnit())) {
            spuVO.setSaleUnit(spuUpdateReqVO.getSaleUnit());
        }
        if (Objects.nonNull(spuUpdateReqVO.getWeight())) {
            spuVO.setWeight(spuUpdateReqVO.getWeight().toEngineeringString());
        }
        if (Objects.nonNull(spuUpdateReqVO.getLength())) {
            spuVO.setLength(spuUpdateReqVO.getLength().toEngineeringString());
        }
        if (Objects.nonNull(spuUpdateReqVO.getWidth())) {
            spuVO.setWidth(spuUpdateReqVO.getWidth().toEngineeringString());
        }
        if (Objects.nonNull(spuUpdateReqVO.getHeight())) {
            spuVO.setHeight(spuUpdateReqVO.getHeight().toEngineeringString());
        }
        if (Objects.nonNull(spuUpdateReqVO.getHsCode())) {
            spuVO.setHsCode(spuUpdateReqVO.getHsCode());
        }
        if (Objects.nonNull(spuUpdateReqVO.getMainImg())) {
            spuVO.setMainImg(spuUpdateReqVO.getMainImg());
        }
        if (Objects.nonNull(spuUpdateReqVO.getDetailImg())) {
            spuVO.setDetailImg(spuUpdateReqVO.getDetailImg());
        }
        if (CollectionUtils.isNotEmpty(spuUpdateReqVO.getDetailImgList())) {
            spuVO.setDetailImgList(spuUpdateReqVO.getDetailImgList());
        }
        return spuVO;
    }

    private void updateSpuDraftInfo(Map<Long, SpuUpdateReqVO> resultMap,SaveSpuVO saveSpuVO){
        try {
            submit(saveSpuVO);
        }catch (BizException bizException){
            log.error("【业务异常】AbstractSpuWriteManageService.updateDraftInfo saveSpuVO:{},err:{}", JSON.toJSONString(saveSpuVO),bizException.getMessage(),bizException);
            saveSpuVO.getSkuVOList().forEach(skuPO -> {
                SpuUpdateReqVO spuUpdateReqVO = resultMap.get(skuPO.getSkuId());
                spuUpdateReqVO.setValid(false);
                spuUpdateReqVO.setResult("【业务异常】" +bizException.getMsg());
                resultMap.put(spuUpdateReqVO.getSkuId(),spuUpdateReqVO);
            } );
        }catch (Exception e){
            log.error("【系统异常】AbstractSpuWriteManageService.updateSpuDraftInfo saveSpuVO:{},err:{}", JSON.toJSONString(saveSpuVO),e.getMessage(),e);
            saveSpuVO.getSkuVOList().forEach(skuPO -> {
                SpuUpdateReqVO spuUpdateReqVO = resultMap.get(skuPO.getSkuId());
                spuUpdateReqVO.setValid(false);
                spuUpdateReqVO.setResult("【系统异常】更新商品Spu草稿信息失败");
                resultMap.put(spuUpdateReqVO.getSkuId(),spuUpdateReqVO);
            } );
        }
    }


    private List<SkuVO> convertSkuVOList(List<SpuUpdateReqVO> spuUpdateReqVOList,List<SkuVO> drafSkuVOList){
        List<SkuVO> resultList = new ArrayList<>();
        Map<Long, SkuVO> targetMap = drafSkuVOList.stream().collect(Collectors.toMap(SkuVO::getSkuId, Function.identity()));
        for (SpuUpdateReqVO spuUpdateReqVO : spuUpdateReqVOList){
            if(targetMap.containsKey(spuUpdateReqVO.getSkuId())){
                SkuVO drafSkuVO = targetMap.get(spuUpdateReqVO.getSkuId());
                if(Objects.nonNull(spuUpdateReqVO.getJdSkuId())){
                    drafSkuVO.setJdSkuId(spuUpdateReqVO.getJdSkuId());
                }
                if(Objects.nonNull(spuUpdateReqVO.getSpecification())){
                    drafSkuVO.setSpecification(spuUpdateReqVO.getSpecification());
                }
                if(Objects.nonNull(spuUpdateReqVO.getProductionCycle())){
                    drafSkuVO.setProductionCycle(spuUpdateReqVO.getProductionCycle());
                }
                if(Objects.nonNull(spuUpdateReqVO.getCurrency())){
                    drafSkuVO.setCurrency(spuUpdateReqVO.getCurrency());
                }
                if(Objects.nonNull(spuUpdateReqVO.getUpcCode())){
                    drafSkuVO.setUpcCode(spuUpdateReqVO.getUpcCode());
                }
                // 比较商品采购价大小并设置消息
                if (Objects.nonNull(spuUpdateReqVO.getJdSkuId()) && StringUtils.isNotBlank(spuUpdateReqVO.getPurchasePrice())){
                    log.info("convertSkuVOList 比较跨境品的新旧采购价，skuId={} jdSkuId={} 原采购价={},新采购价={}",spuUpdateReqVO.getSkuId(),spuUpdateReqVO.getJdSkuId(),drafSkuVO.getPurchasePrice(),spuUpdateReqVO.getPurchasePrice());
                    if (StringUtils.isBlank(drafSkuVO.getPurchasePrice()) || new BigDecimal(spuUpdateReqVO.getPurchasePrice()).compareTo(new BigDecimal(drafSkuVO.getPurchasePrice()))> 0) {
                        spuUpdateReqVO.setResult(String.format(DataResponseCode.SUCCESS.getMessage()+"。 默认采购价上涨，可能触发价格风险。原采购价:[%s],新采购价:[%s]",drafSkuVO.getPurchasePrice(),spuUpdateReqVO.getPurchasePrice()));
                    }
                }

                if(Objects.nonNull(spuUpdateReqVO.getPurchasePrice())){
                    drafSkuVO.setPurchasePrice(spuUpdateReqVO.getPurchasePrice());
                }
                if(Objects.nonNull(spuUpdateReqVO.getSalePrice())){
                    drafSkuVO.setSalePrice(spuUpdateReqVO.getSalePrice());
                }
                if(Objects.nonNull(spuUpdateReqVO.getBrandId())){
                    drafSkuVO.setBrandId(spuUpdateReqVO.getBrandId());
                }
                if(Objects.nonNull(spuUpdateReqVO.getOriginCountry())){
                    drafSkuVO.setOriginCountry(spuUpdateReqVO.getOriginCountry());
                }
                if(Objects.nonNull(spuUpdateReqVO.getWeight())){
                    drafSkuVO.setWeight(spuUpdateReqVO.getWeight().toEngineeringString());
                }
                if(Objects.nonNull(spuUpdateReqVO.getLength())){
                    drafSkuVO.setLength(spuUpdateReqVO.getLength().toEngineeringString());
                }
                if(Objects.nonNull(spuUpdateReqVO.getWidth())){
                    drafSkuVO.setWidth(spuUpdateReqVO.getWidth().toEngineeringString());
                }
                if(Objects.nonNull(spuUpdateReqVO.getHeight())){
                    drafSkuVO.setHeight(spuUpdateReqVO.getHeight().toEngineeringString());
                }
                if(Objects.nonNull(spuUpdateReqVO.getHsCode())){
                    drafSkuVO.setHsCode(spuUpdateReqVO.getHsCode());
                }
                if(Objects.nonNull(spuUpdateReqVO.getMainImg())){
                    drafSkuVO.setMainImg(spuUpdateReqVO.getMainImg());
                }
                if(Objects.nonNull(spuUpdateReqVO.getDetailImg())){
                    drafSkuVO.setDetailImg(spuUpdateReqVO.getDetailImg());
                }
                if(Objects.nonNull(spuUpdateReqVO.getMoq())){
                    drafSkuVO.setMoq(spuUpdateReqVO.getMoq());
                }
                if(Objects.nonNull(spuUpdateReqVO.getVendorSkuId())){
                    drafSkuVO.setVendorSkuId(spuUpdateReqVO.getVendorSkuId());
                }
                if(CollectionUtils.isNotEmpty(spuUpdateReqVO.getDetailImgList())){
                    drafSkuVO.setDetailImgList(spuUpdateReqVO.getDetailImgList());
                }
                if (StringUtils.isNotBlank(spuUpdateReqVO.getJdVendorCode())) {
                    drafSkuVO.setJdVendorCode(spuUpdateReqVO.getJdVendorCode());
                }
                resultList.add(drafSkuVO);
                targetMap.remove(spuUpdateReqVO.getSkuId());
            }
        }
        resultList.addAll(targetMap.values());
        return resultList;
    }


    private Map<String, String> updatePcDescription(Map<String, String> pcDescriptionMap,List<SpuLangApiDTO> spuLangList){
        Map<String, SpuLangApiDTO> spuLangMap = spuLangList.stream().collect(Collectors.toMap(SpuLangApiDTO::getLang, Function.identity(),(v0,v1) -> v0));
        if(MapUtils.isEmpty(pcDescriptionMap)){
            pcDescriptionMap = new HashMap<String, String>();
        }
        for (Map.Entry<String,String> entry : pcDescriptionMap.entrySet()){
            String key = entry.getKey();
            SpuLangApiDTO spuLangApiDTO = spuLangMap.getOrDefault(key,new SpuLangApiDTO());
            if(Objects.nonNull(spuLangApiDTO.getPcDescription())){
                String value = "<p>"+spuLangApiDTO.getPcDescription()+"</p>";
                pcDescriptionMap.put(key,value);
            }
            spuLangMap.remove(key);
        }
        for (Map.Entry<String, SpuLangApiDTO> entry : spuLangMap.entrySet()){
            SpuLangApiDTO spuLangApiDTO = entry.getValue();
            if(StringUtils.isNotBlank(spuLangApiDTO.getPcDescription())){
                String value = "<p>"+spuLangApiDTO.getPcDescription()+"</p>";
                pcDescriptionMap.put(entry.getKey(),value);
            }
        }
        return pcDescriptionMap;
    }

    private List<SpuLangExtendVO> updateSpuLangList(List<SpuLangExtendVO> spuLangExtendVOList,List<SpuLangApiDTO> spuLangList){
        Map<String, SpuLangApiDTO> spuLangMap = spuLangList.stream().collect(Collectors.toMap(SpuLangApiDTO::getLang, Function.identity(),(v0,v1) -> v0));
        if(CollectionUtils.isEmpty(spuLangExtendVOList)){
            spuLangExtendVOList = new ArrayList<>();
        }
        for(SpuLangExtendVO spuLangExtendVO : spuLangExtendVOList){
            SpuLangApiDTO spuLangApiDTO = spuLangMap.getOrDefault(spuLangExtendVO.getLang(),new SpuLangApiDTO());
            if(Objects.nonNull(spuLangApiDTO.getSpuTitle())){
                spuLangExtendVO.setSpuTitle(spuLangApiDTO.getSpuTitle());
            }
            if(Objects.nonNull(spuLangApiDTO.getQualifier())){
                spuLangExtendVO.setQualifier(spuLangApiDTO.getQualifier());
            }
            if(Objects.nonNull(spuLangApiDTO.getSpecification())){
                spuLangExtendVO.setSpecification(spuLangApiDTO.getSpecification());
            }
            if(Objects.nonNull(spuLangApiDTO.getKeyPhrases())){
                spuLangExtendVO.setKeyPhrases(spuLangApiDTO.getKeyPhrases());
            }
            spuLangMap.remove(spuLangExtendVO.getLang());
        }
        for (Map.Entry<String, SpuLangApiDTO> entry : spuLangMap.entrySet()){
            SpuLangApiDTO spuLangApiDTO = entry.getValue();
            SpuLangExtendVO spuLangExtendVO  = new SpuLangExtendVO();
            spuLangExtendVO.setSpuTitle(spuLangApiDTO.getSpuTitle());
            spuLangExtendVO.setLang(spuLangApiDTO.getLang());
            spuLangExtendVO.setLangName(spuLangApiDTO.getLangName());
            spuLangExtendVO.setSpecification(spuLangApiDTO.getSpecification());
            spuLangExtendVO.setQualifier(spuLangApiDTO.getQualifier());
            spuLangExtendVO.setKeyPhrases(spuLangApiDTO.getKeyPhrases());
            spuLangExtendVOList.add(spuLangExtendVO);
        }
        return spuLangExtendVOList;
    }

    private void updateSpuDescLangList(List<SpuDescLangPO> spuDescLangPOList,Set<Long> spuIdSet,Map<Long, SpuUpdateReqVO> resultMap){
        log.info("AbstractSpuWriteManageService.updateSpuDescLangList spuIdSet:{},spuDescLangPOList:{}", JSON.toJSONString(spuIdSet)
            ,JSON.toJSONString(spuDescLangPOList));
        try {
            Map<String, SpuDescLangPO> poMap = spuDescLangAtomicService.getMapBySpuId(spuIdSet);
            List<SpuDescLangPO> spuDescLangPOS = new ArrayList<>();
            for (SpuDescLangPO spuDescLangPO : spuDescLangPOList){
                if(poMap.containsKey(spuDescLangPO.getSpuId() + spuDescLangPO.getLang())) {
                    SpuDescLangPO po = poMap.get(spuDescLangPO.getSpuId() + spuDescLangPO.getLang());
                    spuDescLangPO.setSpuId(null);
                    spuDescLangPO.setId(po.getId());
                    if (Objects.nonNull(spuDescLangPO.getPcDescription())) {
                        spuDescLangPO.setPcDescription("<p>" + spuDescLangPO.getPcDescription() + "</p>");
                    }
                    spuDescLangPOS.add(spuDescLangPO);
                }else {
                    spuDescLangPO.setCreator(spuDescLangPO.getUpdater());
                    spuDescLangPO.setCreateTime(new Date());
                    spuDescLangPO.setUpdateTime(new Date());
                    spuDescLangPOS.add(spuDescLangPO);
                }
            }
            log.info("AbstractSpuWriteManageService.updateSpuDescLangList spuIdSet:{},spuDescLangPOList:{}", JSON.toJSONString(spuIdSet)
                ,JSON.toJSONString(spuDescLangPOS));
            if (CollectionUtils.isEmpty(spuDescLangPOS)){
                return;
            }
            //spuDescLangAtomicService.updateBatchById(spuDescLangPOS);
            spuDescLangAtomicService.saveOrUpdateBatch(spuDescLangPOS);
        }catch (Exception e){
            log.error("【系统异常】AbstractSpuWriteManageService.updateSpuDescLangList spuDescLangPOList:{},err:{}", JSON.toJSONString(spuDescLangPOList),e.getMessage(),e);
            for (Map.Entry<Long,SpuUpdateReqVO> entry : resultMap.entrySet()){
                SpuUpdateReqVO spuUpdateReqVO = entry.getValue();
                spuUpdateReqVO.setValid(false);
                spuUpdateReqVO.setResult("更新商品Spu商详多语言信息失败");
                resultMap.put(spuUpdateReqVO.getSkuId(),spuUpdateReqVO);
            }
            throw new RuntimeException("更新商品Spu商详多语言信息失败");
        }
    }

    private void updateSpuLangList(List<SpuLangPO> spuLangPOList,Set<Long> spuIdSet,Map<Long, SpuUpdateReqVO> resultMap){
        log.info("AbstractSpuWriteManageService.updateSpuLangList spuIdSet:{},spuLangPOList:{}", JSON.toJSONString(spuIdSet),JSON.toJSONString(spuLangPOList));
        try {
            Map<String, SpuLangPO> spuLangMap = spuLangAtomicService.getMapBySpuId(spuIdSet);
            List<SpuLangPO> spuLangPOS = new ArrayList<>();
            for (SpuLangPO spuLangPO :spuLangPOList){
                SpuLangPO zhSpuLangPO = spuLangMap.get(spuLangPO.getSpuId() + LangConstant.LANG_ZH);
                if(spuLangMap.containsKey(spuLangPO.getSpuId() + spuLangPO.getLang())) {
                    SpuLangPO spuLang = spuLangMap.get(spuLangPO.getSpuId() + spuLangPO.getLang());
                    spuLangPO.setSpuId(null);
                    spuLangPO.setId(spuLang.getId());
                    spuLangPOS.add(spuLangPO);
                }else {
                    if(StringUtils.isBlank(spuLangPO.getMainImg())){
                        spuLangPO.setMainImg(zhSpuLangPO.getMainImg());
                    }
                    if(StringUtils.isBlank(spuLangPO.getSpuTitle())){
                        spuLangPO.setSpuTitle("");
                    }
                    spuLangPO.setCreator(spuLangPO.getUpdater());
                    spuLangPO.setCreateTime(new Date());
                    spuLangPO.setUpdateTime(new Date());
                    spuLangPOS.add(spuLangPO);
                }
            }
            log.info("AbstractSpuWriteManageService.updateSpuLangList spuIdSet:{},spuLangPOList:{}", JSON.toJSONString(spuIdSet),JSON.toJSONString(spuLangPOList));
            if (CollectionUtils.isEmpty(spuLangPOS)){
                return;
            }
            //spuLangAtomicService.updateBatchById(spuLangPOS);
            spuLangAtomicService.saveOrUpdateBatch(spuLangPOS);
        }catch (Exception e){
            log.error("【系统异常】AbstractSpuWriteManageService.updateSpuLangList spuLangPOList:{},err:{}", JSON.toJSONString(spuLangPOList),e.getMessage(),e);
            for (Map.Entry<Long,SpuUpdateReqVO> entry : resultMap.entrySet()){
                SpuUpdateReqVO spuUpdateReqVO = entry.getValue();
                spuUpdateReqVO.setValid(false);
                spuUpdateReqVO.setResult("更新商品SpuLang信息失败");
                resultMap.put(spuUpdateReqVO.getSkuId(),spuUpdateReqVO);
            }
            throw new RuntimeException("更新商品SpuLang信息失败");
        }
    }



    private Map<Long, SpuUpdateReqVO> getSkuSpuMap(List<SpuUpdateReqVO> spuUpdateReqVOList){
        Map<Long, SpuUpdateReqVO> spuUpdateReqVOMap = spuUpdateReqVOList.stream()
            .collect(Collectors.toMap(SpuUpdateReqVO::getSkuId, Function.identity()));
        return spuUpdateReqVOMap;
    }

    private Set<Long> getSkuIdSet(List<SpuUpdateReqVO> spuUpdateList){
        Set<Long> skuIdSet = spuUpdateList.stream().map(SpuUpdateReqVO::getSkuId).collect(Collectors.toSet());
        return skuIdSet;
    }

    private void filterSpuCertificate(SaveSpuVO saveSpuVO) {
        List<SpuCertificateVO> spuCertificateVOList = saveSpuVO.getSpuCertificateVOList();
        if (CollectionUtils.isNotEmpty(spuCertificateVOList)) {
            List<SpuCertificateVO> filterCertificateList = spuCertificateVOList.stream().filter(c -> StringUtils.isNotBlank(c.getCertificatePath())).collect(Collectors.toList());
            saveSpuVO.setSpuCertificateVOList(filterCertificateList);
        }
    }


    protected void fillSpuInfoToSkuVo(SaveSpuVO saveSpuVO, List<SkuVO> skuVoList) {
        SpuVO spuVO = saveSpuVO.getSpuVO();
        skuVoList.forEach(vo -> {
            vo.setSpuId(spuVO.getSpuId());
            vo.setVendorCode(spuVO.getVendorCode());
            vo.setCatId(spuVO.getCatId());
            vo.setBrandId(spuVO.getBrandId());
            vo.setSourceCountryCode(spuVO.getSourceCountryCode());
            vo.setMainImg(StringUtils.isBlank(vo.getMainImg()) ? spuVO.getMainImg() : vo.getMainImg());
            vo.setDetailImg(StringUtils.isBlank(vo.getDetailImg()) ? spuVO.getDetailImg() : vo.getDetailImg());
            vo.setDetailImgList(spuVO.getDetailImgList());
            vo.setCurrency(supplierSettlementAccountAtomicService.getCurrencyBySupplierCode( spuVO.getVendorCode()));
            // countryManageService.getCurrencyByCountryCode(
        });
    }

    /**
     * 填充销售属性值ID,SPU与SKU维度
     * @param saveSpuVO 保存SPU的VO对象
     */
    protected void fillSaleAttributeValueId(SaveSpuVO saveSpuVO) {
        List<PropertyVO> salePropertyList = new ArrayList<>();//saveSpuVO.getSalePropertyList();
        Set<Integer> existingIds = salePropertyList.stream()
                .map(PropertyVO::getPropertyValueVOList)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .map(PropertyValueVO::getAttributeValueId)
                .filter(Objects::nonNull)
                .map(Long::intValue)
                .collect(Collectors.toSet());
        int nextId = 1; // 从1开始寻找下一个可用ID
        for(PropertyVO propertyVO : salePropertyList){
            List<PropertyValueVO> propertyValueVOList = propertyVO.getPropertyValueVOList();
            for(PropertyValueVO propertyValueVO : propertyValueVOList){
                if(propertyValueVO.getAttributeValueId() == null) {
                    while (existingIds.contains(nextId)) {
                        nextId++;
                    }
                    propertyValueVO.setAttributeValueId(Long.valueOf(nextId));
                    existingIds.add(nextId);
                }
            }
        }

        Map<String,Long> attributeValueIdMap = new HashMap<>();
        for(PropertyVO propertyVO : salePropertyList){
            List<PropertyValueVO> propertyValueVOList = propertyVO.getPropertyValueVOList();
            for(PropertyValueVO propertyValueVO : propertyValueVOList){
                List<BaseLangVO> baseLangVOS = propertyValueVO.getLangList();
                for(BaseLangVO baseLangVO : baseLangVOS){
                    if(LangConstant.LANG_ZH.equals(baseLangVO.getLang())){
                        attributeValueIdMap.put(StringUtils.joinWith("_",propertyValueVO.getAttributeId(),baseLangVO.getLangName()),propertyValueVO.getAttributeValueId());
                    }
                }
            }
        }

        List<SkuVO> skuVoList = saveSpuVO.getSkuVOList();
        for(SkuVO skuVO : skuVoList){
            List<PropertyValueVO> storeSalePropertyList = skuVO.getStoreSalePropertyList();
            for(PropertyValueVO propertyValueVO : storeSalePropertyList){
                List<BaseLangVO> baseLangVOS = propertyValueVO.getLangList();
                Long attributeValueId = null;
                for(BaseLangVO baseLangVO : baseLangVOS){
                    if(LangConstant.LANG_ZH.equals(baseLangVO.getLang())){
                        attributeValueId = attributeValueIdMap.get(StringUtils.joinWith("_",propertyValueVO.getAttributeId(),baseLangVO.getLangName()));
                    }
                }
                propertyValueVO.setAttributeValueId(attributeValueId);
            }
        }
    }

    /**
     * 初始化价格
     *
     * @param skuVoList         sku列表
     * @param sourceCountryCode 发货国
     */
    private void initSkuPrice(List<SkuVO> skuVoList, String sourceCountryCode) {
        // 将sku分每批20个
        List<List<SkuVO>> partition = Lists.partition(skuVoList, Constant.PARTITION_SIZE);
        for (List<SkuVO> subList : partition) {
            List<SkuPriceVO> subPriceVoList = skuConvertService.convertPartitionSkuPriceVoFromSkuVo(sourceCountryCode, subList);
            // 分批初始化价格
            SkuPriceReqVO priceReqVo = new SkuPriceReqVO();
            priceReqVo.setSkuPriceVO(subPriceVoList);
            Boolean savePrice = skuPriceManageService.saveOrUpdate(priceReqVo);
            if (Objects.equals(savePrice, Boolean.FALSE)) {
                log.error("SpuWriteManageServiceImpl.initSkuPrice sku初始化价格失败,priceReqVo={}", JSON.toJSONString(priceReqVo));
            }
            log.info("SpuWriteManageServiceImpl.initSkuPrice 初始化sku价格成功,priceReqVo={}", JSON.toJSONString(priceReqVo));
        }
    }


    private void initSpuCertificate(SaveSpuVO saveSpuVO) {
        final Long spuId = saveSpuVO.getSpuVO().getSpuId();
        if (CollectionUtils.isEmpty(saveSpuVO.getSpuCertificateVOList())) {
            return;
        }

        // 过滤未上传资质文件的资质数据
        List<SpuCertificateVO> spuCertificateVOList = saveSpuVO.getSpuCertificateVOList().stream().filter(c -> StringUtils.isNotBlank(c.getCertificatePath())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(spuCertificateVOList)) {
            return;
        }
        List<SpuCertificatePO> spuCertificatePoList = SpuConvert.INSTANCE.listCertificateVo2po(spuId, spuCertificateVOList);
        spuCertificateAtomicService.saveBatch(spuCertificatePoList);
    }

    /**
     * 初始化SPU跨境属性。
     * @param saveSpuVO 保存SPU的VO对象，包含SPU的ID和SPU的跨境属性列表。
     */
    private void initSpuGlobalAttribute(SaveSpuVO saveSpuVO){
        final Long spuId = saveSpuVO.getSpuVO().getSpuId();
        if (CollectionUtils.isEmpty(saveSpuVO.getSpuInterPropertyList())) {
            return;
        }

        List<ProductGlobalAttributePO> attributePOS = productAttributeConvertService.convertSpuAttribute(spuId,saveSpuVO.getSpuInterPropertyList());

        // 更新时、如果存在就删除
        List<ProductGlobalAttributePO> globalAttributePOS = productGlobalAttributeAtomicService.getBySpuId(spuId);
        if(CollectionUtils.isNotEmpty(globalAttributePOS)){
            globalAttributePOS.forEach(item->{
                item.setKeyId(null);
                item.setUpdateTime(new Date().getTime());
                item.setYn(YnEnum.NO.getCode());
            });
            productGlobalAttributeAtomicService.saveOrUpdateBatch(globalAttributePOS);
        }

        productGlobalAttributeAtomicService.saveBatch(attributePOS);
    }

    private void saveOrUpdateSpuDescLang(SaveSpuVO saveSpuVO) {
        List<SpuDescLangPO> spuDescLangPoList = spuConvertService.toDescList(saveSpuVO);
        if (CollectionUtils.isNotEmpty(spuDescLangPoList)) {
            spuDescLangAtomicService.saveOrUpdateBatch(spuDescLangPoList);
            log.info("保存商品详描完成,spuId={}", saveSpuVO.getSpuVO().getSpuId());
        }
    }

    /**
     * 创建spu
     *
     * @param saveSpuVO 保存spu对象
     */
    private void createSpu(SaveSpuVO saveSpuVO) {
        // final String buyer = LoginContextHolder.getLoginContextHolder().getPin();
        // SPU数据转换
        SpuVO spuVO = saveSpuVO.getSpuVO();
        SpuPO spuPo = SpuConvert.INSTANCE.dto2po(saveSpuVO.getSpuVO());
        // 设置细节图
        saveSpuVO.getSpuVO().setDetailImg(spuPo.getDetailImg());
        spuPo.setAttributeScope(spuConvertService.convertAttributeScope(saveSpuVO.getSpuVO().getAttributeScope()));
        // 调用发号器
        log.info("创建spu调用发号器,operation={}", spuVO.getBuyer());
        Long spuId = Objects.isNull(spuVO.getSpuId()) ? productIdGenerator.genSpuId() : spuVO.getSpuId();
        log.info("创建spu调用发号器，spuId={}", spuId);
        spuPo.setSpuId(spuId);
        spuVO.setSpuId(spuId);
        // 采购员
        String buyer = categoryBuyerRelationAtomicService.getBuyerByCountryCodeAndCatId(spuVO.getSourceCountryCode(),spuVO.getCatId());
        spuPo.setBuyer(buyer);
        spuVO.setBuyer(buyer);
        // 扩展属性拼接规则  属性id1：属性值1ID1#属性id2:属性值2id1,属性id2:属性值2id2#属性id3:CN_属性值3ID1,属性id3:EN_属性值3ID2
        // TODO 使用新扩展属性字段groupExtAttribute，代替extAttribute，json格式存储，后续删除extAttribute相关操作
        this.fillAttribute(saveSpuVO, spuPo);
        // 调用spu保存方法
        spuAtomicService.save(spuPo);
        log.info("SpuWriteManageServiceImpl.spuSaveOrUpdate  保存SPU信息完成,spuId={}", spuPo.getSpuId());
        // 保存商品名多语言
        spuLangAtomicService.saveBatch(spuConvertService.convertVoToLangPo(saveSpuVO.getSpuVO()));
        log.info("SpuWriteManageServiceImpl.spuSaveOrUpdate  保存SPU多语言信息完成,spouId={}", spuPo.getSpuId());
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> updateSpuSkuMku(SpuUpdateReqVO spuUpdateReqVO) {

        List<List<SkuPO>> skuPOList = filterSkuList(spuUpdateReqVO);
        if(CollectionUtils.isEmpty(skuPOList) || CollectionUtils.isEmpty(skuPOList.get(0))){
            log.info("AbstractSpuWriteManageService updateSpuSkuMku jdSkuId:{} 不存在",spuUpdateReqVO.getJdSkuId());
            return DataResponse.success(true);
        }
        updateSpuList(skuPOList.get(0));
        updateSpuDraft(skuPOList.get(0));
        updateMkuList(skuPOList.get(0));
        updateSkuList(skuPOList.get(0),skuPOList.get(1));
        return DataResponse.success(true);
    }

    @Override
    public DataResponse<String> updateSpuVendorCode(SpuBatchReqVO param) {
        final String pin = LoginContextHolder.getLoginContextHolder().getPin();
        Set<Long> spuIdList = param.getSpuIds();
        for(Long spuId : spuIdList){
            singleUpdateSpuVendorCode(spuId,param.getSourceCountryCode(),param.getVendorCode(),pin);
        }
        return DataResponse.success();
    }

    /**
     * 根据给定的 SpuUpdateReqVO 过滤 SkuPO 列表，返回新旧 SkuPO 列表。
     * @param spuUpdateReqVO SpuUpdateReqVO 对象，包含更新信息
     * @return 一个包含两个 List<SkuPO> 的 List，第一个是更新后的 SkuPO 列表，第二个是更新前的 SkuPO 列表
     */
    private List<List<SkuPO>> filterSkuList(SpuUpdateReqVO spuUpdateReqVO){
        List<List<SkuPO>> resultList = new ArrayList<>();
        List<Long> jdSkuIds = new ArrayList<>();
        jdSkuIds.add(spuUpdateReqVO.getJdSkuId());
        List<SkuPO> skuPOList = skuAtomicService.querySkuPoByJdSkuIds(jdSkuIds);
        if(CollectionUtils.isEmpty(skuPOList)){
            log.info("AbstractSpuWriteManageService filterSkuList jdSkuId:{} 不存在",spuUpdateReqVO.getJdSkuId());
            return resultList;
        }
        List<SkuPO> oldSkuPoList = skuPOList.stream().filter( skuPO -> !(skuPO.getLength().equals(spuUpdateReqVO.getLength()) &&
            skuPO.getWidth().equals(spuUpdateReqVO.getWidth()) &&
            skuPO.getHeight().equals(spuUpdateReqVO.getHeight()) &&
            skuPO.getWeight().equals(spuUpdateReqVO.getWeight())))
            .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(oldSkuPoList)){
            log.info("AbstractSpuWriteManageService filterSkuList jdSkuId:{} 不存在",spuUpdateReqVO.getJdSkuId());
            return resultList;
        }
        List<SkuPO> newSkuPoList = new ArrayList<>(oldSkuPoList);
        for(SkuPO skuPO : newSkuPoList){
            skuPO.setLength(spuUpdateReqVO.getLength());
            skuPO.setWidth(spuUpdateReqVO.getWidth());
            skuPO.setHeight(spuUpdateReqVO.getHeight());
            skuPO.setWeight(spuUpdateReqVO.getWeight());
        }
        resultList.add(newSkuPoList);
        resultList.add(oldSkuPoList);
        return resultList;
    }

    private void updateMkuList(List<SkuPO> skuPOList){
        try {
            List<Long> skuIdList = skuPOList.stream().map(SkuPO::getSkuId).collect(Collectors.toList());
            List<MkuRelationPO> mkuRelationPO = mkuRelationAtomicService.queryBindListBySkuIds(skuIdList);
            Map<Long, List<Long>> skuMkuMap = mkuRelationPO.stream()
                .collect(Collectors.groupingBy(
                    MkuRelationPO::getSkuId,
                    Collectors.mapping(MkuRelationPO::getMkuId, Collectors.toList())
                ));;
            List<Long> mkuIdList = mkuRelationPO.stream().map(MkuRelationPO::getMkuId).collect(Collectors.toList());
            List<MkuPO> mkuPOList = mkuAtomicService.listByMkuIds(mkuIdList);
            Map<Long, MkuPO> mkuIdMkuMap = mkuPOList.stream().collect(Collectors.toMap(MkuPO::getMkuId, Function.identity()));
            List<MkuPO> updateMkuList = new ArrayList<>();
            List<SkuLogPO> skuLogPOList = new ArrayList<>();

            for (SkuPO skuPO : skuPOList){
                List<Long> mkuIds = skuMkuMap.getOrDefault(skuPO.getSkuId(),new ArrayList<>());
                for (Long mkuId : mkuIds) {
                    MkuPO mkuPO = mkuIdMkuMap.get(mkuId);
                    if(Objects.isNull(mkuPO)){
                        continue;
                    }
                    SkuLogPO skuLogPO = new SkuLogPO();
                    skuLogPO.setSourceJson(JSON.toJSONString(mkuPO));

                    mkuPO.setLength(skuPO.getLength());
                    mkuPO.setWidth(skuPO.getWidth());
                    mkuPO.setHeight(skuPO.getHeight());
                    mkuPO.setWeight(skuPO.getWeight());
                    mkuPO.setMkuStatus(MkuStatusEnum.WAIT_APPROVE.getStatus());

                    skuLogPO.setTargetJson(JSON.toJSONString(mkuPO));
                    skuLogPO.setKeyType(KeyTypeEnum.MKU.getCode());
                    skuLogPO.setKeyId(String.valueOf(mkuPO.getMkuId()));
                    skuLogPO.setCreator(Constant.PIN_SYSTEM);
                    skuLogPO.setUpdater(Constant.PIN_SYSTEM);
                    skuLogPOList.add(skuLogPO);

                    updateMkuList.add(mkuPO);
                }
            }
            if(CollectionUtils.isNotEmpty(updateMkuList)) {
                mkuAtomicService.updateBatchById(updateMkuList);
            }
            if(CollectionUtils.isNotEmpty(skuLogPOList)) {
                skuLogAtomicService.saveBatch(skuLogPOList);
            }
            if(CollectionUtils.isNotEmpty(mkuIdList)) {
                List<Long> mkuIds = mkuPOList.stream().filter(mkuPo -> mkuPo.getMkuStatus().equals(MkuStatusEnum.ON_SALE.getStatus())).map(MkuPO::getMkuId).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(mkuIds)) {
                    updateMkuStatus(mkuIds, MkuStatusEnum.ON_SALE.getStatus());
                }
            }
        }catch (Exception e){
            log.error("【系统异常】AbstractSpuWriteManageService.updateMkuList skuPOList:{},err:{}", JSON.toJSONString(skuPOList),e.getMessage(),e);
            throw new RuntimeException("更新商品mku信息失败");
        }
    }

    /**
     * 批量更新 MKU 状态
     * @param mkuIds 需要更新的 MKU ID 列表
     * @param status 新的 MKU 状态
     * @return 更新成功的 MKU ID 列表
     */
    private DataResponse<List<Long>> updateMkuStatus(List<Long> mkuIds, Integer status){
        log.info("DevOpsApiServiceImpl updateMkuStatus mkuIds:{},status:{}",JSON.toJSONString(mkuIds),status);
        List<MkuPO> mkuPOList = mkuAtomicService.listByMkuIds(mkuIds);
        mkuPOList.forEach(mkuPO -> {
            mkuPO.setMkuStatus(status);
            mkuPO.setRemark("MQ更新mku信息和状态");
            mkuPO.setUpdateTime(new Date());
        } );
        boolean result = mkuAtomicService.updateBatchById(mkuPOList);
        log.info("DevOpsApiServiceImpl updateMkuStatus mkuPOList:{},result:{}",JSON.toJSONString(mkuPOList),result);
        return DataResponse.success(mkuIds);
    }


    private void updateSpuDraft(List<SkuPO> skuPOList){
        try {
            for (SkuPO skuPO : skuPOList){
                SaveSpuVO saveSpuVO = spuDraftManageService.getSaveSpuVoFromDraftBySpuId(skuPO.getSpuId());
                if(Objects.isNull(saveSpuVO)){
                    continue;
                }
                SkuLogPO skuLogPO = new SkuLogPO();
                skuLogPO.setSourceJson(JSON.toJSONString(saveSpuVO));
                SpuVO spuVO = saveSpuVO.getSpuVO();
                if(Objects.nonNull(spuVO)){
                    spuVO.setLength(skuPO.getLength() == null?"0":skuPO.getLength().toEngineeringString());
                    spuVO.setWidth(skuPO.getWidth()== null?"0":skuPO.getWidth().toEngineeringString());
                    spuVO.setHeight(skuPO.getHeight()== null?"0":skuPO.getHeight().toEngineeringString());
                    spuVO.setWeight(skuPO.getWeight()== null?"0":skuPO.getWeight().toEngineeringString());
                }

                List<SkuVO> skuVOList = saveSpuVO.getSkuVOList();
                if(CollectionUtils.isNotEmpty(skuVOList)){
                    for (SkuVO skuVO : skuVOList){
                        if(skuPO.getSkuId().equals(skuVO.getSkuId())){
                            skuVO.setLength(skuPO.getLength() == null?"0":skuPO.getLength().toEngineeringString());
                            skuVO.setWidth(skuPO.getWidth()== null?"0":skuPO.getWidth().toEngineeringString());
                            skuVO.setHeight(skuPO.getHeight()== null?"0":skuPO.getHeight().toEngineeringString());
                            skuVO.setWeight(skuPO.getWeight()== null?"0":skuPO.getWeight().toEngineeringString());
                        }
                    }
                }
                skuLogPO.setTargetJson(JSON.toJSONString(saveSpuVO));
                skuLogPO.setKeyType(KeyTypeEnum.SPU_DRAFT.getCode());
                skuLogPO.setKeyId(String.valueOf(saveSpuVO.getSpuVO().getSpuId()));
                skuLogPO.setCreator(Constant.PIN_SYSTEM);
                skuLogPO.setUpdater(Constant.PIN_SYSTEM);
                log.info("IscProductSoaSpuWriteApiServiceImpl updateSpuDraft source:{}" , JSON.toJSONString(saveSpuVO));
                spuDraftManageService.saveOrUpdateDraft(saveSpuVO);
                skuLogAtomicService.save(skuLogPO);
            }
        }catch (Exception e){
            log.error("【系统异常】AbstractSpuWriteManageService.updateSpuDraft skuPOList:{},err:{}", JSON.toJSONString(skuPOList),e.getMessage(),e);
            throw new RuntimeException("更新商品SpuDraft信息失败");
        }
    }

    /**
     * 更新商品 SKU 列表
     * @param newSkuPoList 新的 SKU 列表
     * @param sourceSkuList 原始的 SKU 列表
     */
    private void updateSkuList(List<SkuPO> newSkuPoList,List<SkuPO> sourceSkuList){
        SkuLogPO skuLogPO = new SkuLogPO();
        skuLogPO.setSourceJson(JSON.toJSONString(sourceSkuList));
        skuLogPO.setTargetJson(JSON.toJSONString(newSkuPoList));
        skuLogPO.setKeyType(KeyTypeEnum.SKU.getCode());
        skuLogPO.setKeyId(String.valueOf(newSkuPoList.get(0).getSkuId()));
        skuLogPO.setCreator(Constant.PIN_SYSTEM);
        skuLogPO.setUpdater(Constant.PIN_SYSTEM);
        try {
            newSkuPoList.forEach(po -> {
                // 更新时不能更新spuId，因为有索引，需要设置为null
                po.setSpuId(null);
                po.setUpdateTime(new Date());
                po.setWeightSource(SkuWeightSourceEnum.WAREHOUSE_SYNC.getCode());
            });
            log.info("SkuWriteManageServiceImpl.updateSku skuPoList:{}", JSON.toJSONString(newSkuPoList));
            skuAtomicService.updateBatchById(newSkuPoList);
            skuLogAtomicService.save(skuLogPO);
        }catch (Exception e){
            log.error("【系统异常】SkuWriteManageServiceImpl.updateSku skuPoList:{},err:{}", JSON.toJSONString(newSkuPoList),e.getMessage(),e);
            throw new RuntimeException("更新商品信息失败");
        }
    }

    private void updateSpuList(List<SkuPO> skuPOList){
        Set<Long> spuIdSet = new HashSet<>();
        skuPOList.forEach(skuPO -> {
            spuIdSet.add(skuPO.getSpuId());
        });
        try {
            Map<Long, SpuPO> spuMap = spuAtomicService.getSpuMap(spuIdSet);
            List<SkuLogPO> skuLogPOList = new ArrayList<>();
            for (SkuPO skuPO:skuPOList){
                SpuPO spu = spuMap.get(skuPO.getSpuId());
                SkuLogPO skuLogPO = new SkuLogPO();
                skuLogPO.setSourceJson(JSON.toJSONString(spu));

                spu.setLength(skuPO.getLength());
                spu.setWidth(skuPO.getWidth());
                spu.setHeight(skuPO.getHeight());
                spu.setWeight(skuPO.getWeight());

                skuLogPO.setTargetJson(JSON.toJSONString(spu));
                skuLogPO.setKeyType(KeyTypeEnum.SPU.getCode());
                skuLogPO.setKeyId(String.valueOf(spu.getSpuId()));
                skuLogPO.setCreator(Constant.PIN_SYSTEM);
                skuLogPO.setUpdater(Constant.PIN_SYSTEM);
                skuLogPOList.add(skuLogPO);
            }
            log.info("SkuWriteManageServiceImpl.updateSpuList skuPoList:{}", JSON.toJSONString(skuPOList));
            spuAtomicService.updateBatchById(spuMap.values());
            skuLogAtomicService.saveBatch(skuLogPOList);
        }catch (Exception e){
            log.error("【系统异常】AbstractSpuWriteManageService.updateSpuList skuPOList:{},err:{}", JSON.toJSONString(skuPOList),e.getMessage(),e);
            throw new RuntimeException("更新商品Spu信息失败");
        }
    }

    private void fillAttribute(SaveSpuVO saveSpuVO, SpuPO spuPo) {
        // 扩展属性拼接规则  属性id1：属性值1ID1#属性id2:属性值2id1,属性id2:属性值2id2#属性id3:CN_属性值3ID1,属性id3:EN_属性值3ID2
//        spuPo.setExtAttribute(spuConvertService.getExtAttribute(saveSpuVO.getStoreExtendPropertyList())); 2025del_service
        // 使用新扩展属性字段groupExtAttribute，代替extAttribute，json格式存储, 后续删除extAttribute相关操作
        spuPo.setGroupExtAttribute(spuConvertService.getGroupExtAttribute(saveSpuVO.getStoreExtendPropertyList()));
        log.info("AbstractSpuWriteManageService.fillAttribute spuPo.getGroupExtAttribute:{}, saveSpuVO.getStoreExtendPropertyList():{}",spuPo.getGroupExtAttribute(),saveSpuVO.getStoreExtendPropertyList   ());
//        spuPo.setSaleAttribute(spuConvertService.getSaleAttribute(saveSpuVO.getSkuVOList()));   删除旧版销售属性赋值，跨境品会抛异常  ZHAOYAN_SALE_ATTR
    }

    abstract void updateStatusToWaitApprove(Long spuId);

    /**
     * 添加审核记录
     *
     * @param spuId           spuId
     * @param erp             当前操作人
     * @param auditStatusEnum 审核状态枚举
     * @param remark          审核原因
     */
    protected void addAuditRecord(Long spuId, String erp, AuditStatusEnum auditStatusEnum, String remark) {
        SpuAuditRecordVO recordVO = new SpuAuditRecordVO();
        recordVO.setAuditStatus(auditStatusEnum.getCode());
        recordVO.setSpuId(spuId);
        recordVO.setRemark(remark);
        recordVO.setAuditErp(erp);
        recordVO.setCreator(erp);
        recordVO.setUpdater(erp);
        spuAuditRecordManageService.add(recordVO);
    }

    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public void singleUpdateSpuVendorCode(Long spuId,String sourceCountryCode,String targetVendorCode,String pin){
        try{
            SpuPO spuPO = spuAtomicService.getSpuPoBySpuId(spuId);
            if(spuPO == null){
                throw new BizException("商品不存在");
            }

            // 校验供应商是否存在
            spuValidateService.validateVendorCountry(targetVendorCode,sourceCountryCode);

            // 校验供应商生产线是否存在
            String supplierCode = spuValidateService.validateBusinessLineRelation(targetVendorCode,spuPO.getJdCatId(),spuPO.getBrandId());

            // 更新spu主表
            SpuPO updateSpuPO = new SpuPO();
            updateSpuPO.setSpuId(spuId);
            updateSpuPO.setVendorCode(supplierCode);
            updateSpuPO.setUpdater(pin);
            updateSpuPO.setUpdateTime(new Date());
            spuAtomicService.updateById(updateSpuPO);

            // 更新sku主表
            List<SkuPO> skuPOS = skuAtomicService.getSkuPoBySpuId(spuId);
            if(CollectionUtils.isEmpty(skuPOS)){
                throw new BizException("sku集合不存在");
            }
            List<SkuPO> updateSkuPOS = new ArrayList<>();
            for(SkuPO skuPO : skuPOS){
                SkuPO updateSkuPO = new SkuPO();
                updateSkuPO.setSkuId(skuPO.getSkuId());
                updateSkuPO.setVendorCode(supplierCode);
                updateSkuPO.setUpdater(pin);
                updateSkuPO.setUpdateTime(new Date());
                updateSkuPOS.add(updateSkuPO);
            }
            skuAtomicService.updateBatchById(updateSkuPOS);

            // 更新spu草稿
            SaveSpuVO saveSpuVO = spuDraftManageService.getSaveSpuVoFromDraftBySpuId(spuId);
            saveSpuVO.getSpuVO().setVendorCode(supplierCode);
            List<SkuVO> skuVOList = saveSpuVO.getSkuVOList();
            for(SkuVO skuVO : skuVOList){
                skuVO.setVendorCode(supplierCode);
            }
            spuDraftManageService.saveOrUpdateDraft(saveSpuVO);
            // 更新vip价格草稿表
            Set<Long> skuIds = skuVOList.stream().map(SkuVO::getSkuId).collect(Collectors.toSet());
            List<CustomerSkuPriceDraftPO> skuPriceDraftPOS = customerSkuPriceDraftAtomicService.listBySkuIds(skuIds);
            if(CollectionUtils.isEmpty(skuPriceDraftPOS)){
                log.error("AbstractSpuWriteManageService.singleUpdateSpuVendorCode skuPriceDraftPOS is null,spuId:{},vendorCode:{},pin:{}",spuId,supplierCode,pin);
            } else {
                List<CustomerSkuPriceDraftPO> updateSkuPriceDraftPOS = new ArrayList<>();
                for(CustomerSkuPriceDraftPO skuPriceDraftPO : skuPriceDraftPOS){
                    CustomerSkuPriceDraftPO updateSkuPriceDraftPO = new CustomerSkuPriceDraftPO();
                    updateSkuPriceDraftPO.setId(skuPriceDraftPO.getId());
                    updateSkuPriceDraftPO.setVendorCode(supplierCode);
                    updateSkuPriceDraftPO.setUpdater(pin);
                    updateSkuPriceDraftPO.setUpdateTime(new Date());
                    updateSkuPriceDraftPOS.add(updateSkuPriceDraftPO);
                }
                customerSkuPriceDraftAtomicService.updateBatchById(updateSkuPriceDraftPOS);
            }

            // 更新vip价格正式表
            List<CustomerSkuPricePO> skuPricePOS = customerSkuPriceAtomicService.listBySkuIds(skuIds);
            if(CollectionUtils.isEmpty(skuPricePOS)){
                log.error("AbstractSpuWriteManageService.singleUpdateSpuVendorCode skuPricePOS is null,spuId:{},vendorCode:{},pin:{}",spuId,supplierCode,pin);
            } else {
                List<CustomerSkuPricePO> updatePricePOS = new ArrayList<>();
                for(CustomerSkuPricePO skuPricePO : skuPricePOS){
                    CustomerSkuPricePO customerSkuPricePO = new CustomerSkuPricePO();
                    customerSkuPricePO.setId(skuPricePO.getId());
                    customerSkuPricePO.setVendorCode(supplierCode);
                    customerSkuPricePO.setUpdater(pin);
                    customerSkuPricePO.setUpdateTime(new Date());
                    updatePricePOS.add(customerSkuPricePO);
                }
                customerSkuPriceAtomicService.updateBatchById(updatePricePOS);
            }

            // 更新采购价草稿表
            List<SkuPriceDraftPO> skuPurchasePriceDraftPOS = skuPriceDraftAtomicService.getValidBySkuIds(skuIds);
            if(CollectionUtils.isEmpty(skuPurchasePriceDraftPOS)){
                log.error("AbstractSpuWriteManageService.singleUpdateSpuVendorCode skuPurchasePriceDraftPOS is null,spuId:{},vendorCode:{},pin:{}",spuId,supplierCode,pin);
            } else {
                List<SkuPriceDraftPO> updatePurchasePriceDraftPOS = new ArrayList<>();
                for(SkuPriceDraftPO purchasePriceDraftPO : skuPurchasePriceDraftPOS){
                    SkuPriceDraftPO skuPriceDraftPO = new SkuPriceDraftPO();
                    skuPriceDraftPO.setId(purchasePriceDraftPO.getId());
                    skuPriceDraftPO.setVendorCode(supplierCode);
                    skuPriceDraftPO.setUpdater(pin);
                    skuPriceDraftPO.setUpdateTime(new Date());
                    updatePurchasePriceDraftPOS.add(skuPriceDraftPO);
                }
                skuPriceDraftAtomicService.updateBatchById(updatePurchasePriceDraftPOS);
            }

            // 更新客户mku价格表
            List<CustomerMkuPricePO> customerMkuPricePOS = customerMkuPriceAtomicService.queryMkuListsBySkuId(skuIds);
            if(CollectionUtils.isEmpty(customerMkuPricePOS)){
                log.error("AbstractSpuWriteManageService.singleUpdateSpuVendorCode customerMkuPricePOS is null,spuId:{},vendorCode:{},pin:{}",spuId,supplierCode,pin);
            } else {
                List<CustomerMkuPricePO> updateCustomerMkuPricePOS = new ArrayList<>();
                for(CustomerMkuPricePO customerMkuPricePO : customerMkuPricePOS){
                    CustomerMkuPricePO updateCustomerMkuPricePO = new CustomerMkuPricePO();
                    updateCustomerMkuPricePO.setId(customerMkuPricePO.getId());
                    updateCustomerMkuPricePO.setVendorCode(supplierCode);
                    updateCustomerMkuPricePO.setUpdater(pin);
                    updateCustomerMkuPricePO.setUpdateTime(new Date());
                    updateCustomerMkuPricePOS.add(updateCustomerMkuPricePO);
                }
                customerMkuPriceAtomicService.updateBatchById(updateCustomerMkuPricePOS);
            }

            // 更新mku/sku关系表
            List<MkuRelationPO> mkuRelationPOList = mkuRelationAtomicService.queryBySkuIds(skuIds);
            if(CollectionUtils.isEmpty(mkuRelationPOList)){
                log.error("AbstractSpuWriteManageService.singleUpdateSpuVendorCode mkuRelationPOList is null,spuId:{},vendorCode:{},pin:{}",spuId,supplierCode,pin);
            } else {
                List<MkuRelationPO> updateMkuRelationPOS = new ArrayList<>();
                for(MkuRelationPO mkuRelationPO : mkuRelationPOList){
                    MkuRelationPO updateMkuRelationPO = new MkuRelationPO();
                    updateMkuRelationPO.setId(mkuRelationPO.getId());
                    updateMkuRelationPO.setVendorCode(supplierCode);
                    updateMkuRelationPO.setUpdater(pin);
                    updateMkuRelationPO.setUpdateTime(new Date());
                    updateMkuRelationPOS.add(updateMkuRelationPO);
                }
                mkuRelationAtomicService.updateBatchById(updateMkuRelationPOS);
            }

            log.info("AbstractSpuWriteManageService.singleUpdateSpuVendorCode 更新vendorCode成功,spuId:{},vendorCode:{},pin:{}",spuId,supplierCode,pin);
        } catch(Exception e){
            log.error("AbstractSpuWriteManageService.singleUpdateSpuVendorCode 更新vendorCode失败,spuId:{},vendorCode:{},pin:{},err:{}",spuId,targetVendorCode,pin,e.getMessage(),e);
        }
    }

    protected Long generateId() {
        return productIdGenerator.genSpuId();
    }

    protected void saveOrUpdateDraft(SaveSpuVO saveSpuVO) {
        spuDraftManageService.saveOrUpdateDraft(saveSpuVO);
    }

    abstract void validateSaveParams(SaveSpuVO saveSpuVO);

    abstract void validateCreateSkuVoList(List<SkuVO> skuVOList,SpuVO spuVO);

    abstract void validateUpdateSkuVoList(List<SkuVO> skuVOList, SpuVO spuVO);

    abstract void validateDbSaveSpuVoAndViewSaveSpuVo(SaveSpuVO saveSpuVO);


    abstract void recordVendor(List<SkuVO> skuVOList);

    protected String getMessage(String code) {
        return dataResponseMessageService.getErrorMessage(code);
    }

    public void checkSkuList(SaveSpuVO saveSpuVO){
        List<@Valid SkuVO> skuVOList = saveSpuVO.getSkuVOList();
        AssertValidation.isEmpty(skuVOList, "", "sku集合不存在，请检查");
    }

    /**
     * 发跨境品时根据jdSkuId查询中台获取jdMainSkuId和jdSpuId并设置到SkuVO中
     * @param skuVoList SKU列表
     * @param sourceCountryCode 货源国代码
     */
    protected void fillCrossBorderSkuMainInfo(List<SkuVO> skuVoList, String sourceCountryCode) {
        // 只处理跨境品
        if (!CountryConstant.COUNTRY_ZH.equals(sourceCountryCode)) {
            log.info("AbstractSpuWriteManageService.fillCrossBorderSkuMainInfo 非跨境品，跳过处理，sourceCountryCode: {}", sourceCountryCode);
            return;
        }

        if (CollectionUtils.isEmpty(skuVoList)) {
            log.info("AbstractSpuWriteManageService.fillCrossBorderSkuMainInfo SKU列表为空，跳过处理");
            return;
        }

        log.info("AbstractSpuWriteManageService.fillCrossBorderSkuMainInfo 开始处理跨境品jdMainSkuId和jdSpuId查询，SKU数量: {}", skuVoList.size());

        try {
            // 批量查询中台SKU信息
            List<Long> jdSkuIds = skuVoList.stream()
                    .filter(Objects::nonNull)
                    .map(SkuVO::getJdSkuId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(jdSkuIds)) {
                log.info("AbstractSpuWriteManageService.fillCrossBorderSkuMainInfo 没有有效的jdSkuId，跳过处理");
                return;
            }

            // 调用中台接口查询SKU信息
            JdProductQueryDTO queryParam = new JdProductQueryDTO();
            queryParam.setSkuIds(jdSkuIds);
            Map<Long, JdProductDTO> jdProductMap = skuInfoRpcService.querySkuMap(queryParam);

            if (MapUtils.isEmpty(jdProductMap)) {
                log.warn("AbstractSpuWriteManageService.fillCrossBorderSkuMainInfo 查询中台SKU信息为空，jdSkuIds: {}", jdSkuIds);
                return;
            }

            // 设置jdMainSkuId和jdSpuId到SkuVO
            for (SkuVO skuVO : skuVoList) {
                if (Objects.isNull(skuVO.getJdSkuId())) {
                    continue;
                }

                JdProductDTO jdProductDTO = jdProductMap.get(skuVO.getJdSkuId());
                if (Objects.nonNull(jdProductDTO)) {
                    // 设置京东主站主skuId
                    if (Objects.nonNull(jdProductDTO.getJdMainSkuId())) {
                        skuVO.setJdMainSkuId(jdProductDTO.getJdMainSkuId());
                        log.info("AbstractSpuWriteManageService.fillCrossBorderSkuMainInfo 设置jdMainSkuId，skuId: {}, jdSkuId: {}, jdMainSkuId: {}",
                                skuVO.getSkuId(), skuVO.getJdSkuId(), jdProductDTO.getJdMainSkuId());
                    }

                    // 设置京东主站spuId
                    if (Objects.nonNull(jdProductDTO.getJdSpuId())) {
                        skuVO.setJdSpuId(jdProductDTO.getJdSpuId());
                        log.info("AbstractSpuWriteManageService.fillCrossBorderSkuMainInfo 设置jdSpuId，skuId: {}, jdSkuId: {}, jdSpuId: {}",
                                skuVO.getSkuId(), skuVO.getJdSkuId(), jdProductDTO.getJdSpuId());
                    }
                } else {
                    log.warn("AbstractSpuWriteManageService.fillCrossBorderSkuMainInfo 未找到对应的中台SKU信息，jdSkuId: {}", skuVO.getJdSkuId());
                }
            }

            log.info("AbstractSpuWriteManageService.fillCrossBorderSkuMainInfo 完成跨境品jdMainSkuId和jdSpuId设置");

        } catch (Exception e) {
            log.error("AbstractSpuWriteManageService.fillCrossBorderSkuMainInfo 查询中台SKU信息异常，sourceCountryCode: {}, jdSkuIds: {}",
                    sourceCountryCode, skuVoList.stream().map(SkuVO::getJdSkuId).collect(Collectors.toList()), e);
            // 异常不影响正常流程，只记录日志
        }
    }

}
