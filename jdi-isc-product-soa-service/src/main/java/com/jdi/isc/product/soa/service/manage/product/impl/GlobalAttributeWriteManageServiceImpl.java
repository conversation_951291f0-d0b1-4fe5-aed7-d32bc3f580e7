package com.jdi.isc.product.soa.service.manage.product.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.AttributeInputTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuTaxAuditStatusEnum;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.AttributeCheckTypeEnum;
import com.jdi.isc.product.soa.common.enums.AttributeDimensionEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.domain.category.biz.*;
import com.jdi.isc.product.soa.domain.enums.product.AuditLevelEnum;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCalculatePriceReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCalculateTaxPriceVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.service.atomic.category.GlobalAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDescLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuLangAtomicService;
import com.jdi.isc.product.soa.service.manage.category.GlobalAttributeManageService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.product.GlobalAttributeWriteManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuAuditRecordManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuCompareService;
import com.jdi.isc.product.soa.service.manage.spu.SpuDraftManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuReadManageService;
import com.jdi.isc.product.soa.service.mapstruct.ProductAttributeConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuCompareDetailConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.api.common.LangConstant.LANG_ZH;

/**
 * 更新跨境属性
 * <AUTHOR>
 * @date 2024/9/12
 **/
@Slf4j
@Service
public class GlobalAttributeWriteManageServiceImpl implements GlobalAttributeWriteManageService {

    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private GlobalAttributeAtomicService globalAttributeAtomicService;
    @Resource
    private SpuLangAtomicService spuLangAtomicService;
    @Resource
    private SpuDescLangAtomicService spuDescLangAtomicService;
    @Resource
    private GlobalAttributeManageService globalAttributeManageService;
    @Resource
    private SpuConvertService spuConvertService;
    @Resource
    private SpuReadManageService spuReadManageService;
    @Resource
    private SpuDraftManageService spuDraftManageService;
    @Resource
    private CountryManageService countryManageService;
    @Resource
    private ProductAttributeConvertService productAttributeConvertService;
    @Resource
    private SkuReadManageService skuReadManageService;
    @Resource
    private SpuCompareService spuCompareService;
    @Resource
    private SpuAuditRecordManageService spuAuditRecordManageService;
    // 越南增值税率跨境属性ID
    public static final Long VN_TAX_RATE = 68L;
    // 巴西税率跨境属性ID集合
    public static final Set<Long> taxRateIdSet = Sets.newHashSet(73L, 76L, 77L, 79L);
    // 税金跨境属性ID集合
    public static final Set<Long> taxRateAmountIdSet = Sets.newHashSet(74L, 78L, 80L, 106L);
    // 巴西IPI税金金额(不能改，改了会影响巴西未税算含税)
    public static final Set<Long> taxIPIAmount = Sets.newHashSet(74L);

    /**
     * 巴西 IPI 金额 ID
     */
    public static final Long IPI_AMOUNT_ID = 74L;


    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 60)
    public UpdateGlobalAttributeResVO updateGlobalAttribute(UpdateProductGlobalAttributeReqVO reqVO) {
        UpdateGlobalAttributeResVO resVO = new UpdateGlobalAttributeResVO();
        // skuId不为空，处理单个sku和spu
        try {
            if (Objects.isNull(reqVO.getSkuId()) && Objects.isNull(reqVO.getSpuId())) {
                resVO.setResult("商品SKU ID和SPU ID不能同时为空");
                return resVO;
            }

            // 删除可出口属性
            if (CollectionUtils.isNotEmpty(reqVO.getTargetCountryCodes())) {
                reqVO.getTargetCountryCodes().remove("1");
            }
            if (Objects.nonNull(reqVO.getSkuId())) {
                SkuPO skuPo = skuAtomicService.getSkuPoBySkuId(reqVO.getSkuId());
                if (Objects.isNull(skuPo)) {
                    return new UpdateGlobalAttributeResVO(reqVO.getSpuId(), reqVO.getSkuId(), String.format("商品%s不存在", reqVO.getSkuId()));
                }
                if (!skuPo.getSourceCountryCode().equals(reqVO.getSourceCountryCode())) {
                    return new UpdateGlobalAttributeResVO(reqVO.getSpuId(), reqVO.getSkuId(), String.format("商品%s不当前货源国%s", reqVO.getSkuId(), reqVO.getSourceCountryCode()));
                }

                Long spuId = skuPo.getSpuId();
                reqVO.setSpuId(spuId);
                // 类目ID 用于查询下面的跨境属性
                Long catId = skuPo.getJdCatId();
                // 查询商品信息
                SpuPO spuPo = spuAtomicService.getSpuPoBySpuId(reqVO.getSpuId());
                fillSpuAttributeScope(reqVO, spuPo);
                // 如果是审核状态的商品直接查商品详情接口
                SpuDetailVO spuDetail = this.getSpuDetailVO(reqVO);
                // 深拷贝商品详情信息
                SpuDetailVO dbSpuDetail = SpuCompareDetailConvert.INSTANCE.dbCopy(spuDetail);
                // spu跨境属性
                if (CollectionUtils.isNotEmpty(reqVO.getSpuPropertyApiDTOList())) {
                    reqVO.setSpuId(spuId);
                    spuDetail.setSpuInterPropertyList(this.handleSpuGlobalAttributeList(reqVO, catId, spuDetail.getSpuInterPropertyList()));
                }
                // 补充 reqVO ncm码 跨境属性
                //  sku跨境属性
                if (CollectionUtils.isNotEmpty(reqVO.getSkuPropertyApiDTOList())) {
                    // 补充跨境属性ncm码
                    spuConvertService.handleNcmCodeTry(spuDetail.getSkuVOList(), spuDetail.getSpuVO());
                    this.handleSkuGlobalAttributeList(reqVO, catId, spuDetail.getSkuVOList());
                }
                // 销售国家
                //this.processAttributeScope(reqVO, spuDetail);
                // 商品多语言名称
                this.handleSpuTitle(reqVO, spuDetail);
                // 商详
                this.handleSpuDescription(reqVO, spuDetail);
                // 更新草稿和商品状态
                this.processDraftAndStatus(spuDetail, reqVO, dbSpuDetail);
            }
            // spuId不为空 处理多个sku和spu
            else if (Objects.nonNull(reqVO.getSpuId())) {
                SpuPO spuPo = spuAtomicService.getSpuPoBySpuId(reqVO.getSpuId());
                if (Objects.isNull(spuPo)) {
                    return new UpdateGlobalAttributeResVO(reqVO.getSpuId(), reqVO.getSkuId(), String.format("商品%s不存在", reqVO.getSpuId()));
                }
                if (!spuPo.getSourceCountryCode().equals(reqVO.getSourceCountryCode())) {
                    return new UpdateGlobalAttributeResVO(reqVO.getSpuId(), reqVO.getSkuId(), String.format("商品%s不当前货源国%s", reqVO.getSpuId(), reqVO.getSourceCountryCode()));
                }
                // 类目ID 用于查询下面的跨境属性
                Long catId = spuPo.getJdCatId();
                fillSpuAttributeScope(reqVO, spuPo);
                SpuDetailVO spuDetail = this.getSpuDetailVO(reqVO);
                // 深拷贝商品详情信息
                SpuDetailVO dbSpuDetail = SpuCompareDetailConvert.INSTANCE.dbCopy(spuDetail);
                // spu跨境属性
                if (CollectionUtils.isNotEmpty(reqVO.getSpuPropertyApiDTOList())) {
                    spuDetail.setSpuInterPropertyList(this.handleSpuGlobalAttributeList(reqVO, catId, spuDetail.getSpuInterPropertyList()));
                }
                // 补充 reqVO ncm码 跨境属性

                //  sku跨境属性
                if (CollectionUtils.isNotEmpty(reqVO.getSkuPropertyApiDTOList())) {
                    // 补充跨境属性ncm码
                    spuConvertService.handleNcmCodeTry(spuDetail.getSkuVOList(), spuDetail.getSpuVO());
                    this.handleSkuGlobalAttributeList(reqVO, catId, spuDetail.getSkuVOList());
                }
                // 更新销售国和可出口
                //this.processAttributeScope(reqVO, spuDetail);
                // 商品多语言名称
                this.handleSpuTitle(reqVO, spuDetail);
                // 商详
                this.handleSpuDescription(reqVO, spuDetail);
                // 更新草稿和商品状态
                this.processDraftAndStatus(spuDetail, reqVO, dbSpuDetail);
            }
        } catch (BizException bizException) {
            log.error("GlobalAttributeWriteManageServiceImpl.updateGlobalAttribute bizException spuId={},skuId={}", reqVO.getSpuId(), reqVO.getSkuId(), bizException);
            throw new RuntimeException(bizException.getMessage());
        } catch (Exception e) {
            log.error("【系统异常】GlobalAttributeWriteManageServiceImpl.updateGlobalAttribute system error spuId={},skuId={}", reqVO.getSpuId(), reqVO.getSkuId(), e);
            throw new RuntimeException("更新商品跨境属性失败");
        }
        resVO.setSpuId(reqVO.getSpuId());
        resVO.setSkuId(reqVO.getSkuId());
        resVO.setResult(DataResponseCode.SUCCESS.getMessage());
        return resVO;
    }

    private void fillSpuAttributeScope(UpdateProductGlobalAttributeReqVO reqVO, SpuPO spuPo) {
        if (StringUtils.isNotBlank(spuPo.getAttributeScope())) {
            String attributeScope = spuPo.getAttributeScope();
            if (CollectionUtils.isEmpty(reqVO.getTargetCountryCodes())) {
                reqVO.setTargetCountryCodes(Arrays.stream(attributeScope.split(Constant.COMMA)).collect(Collectors.toSet()));
            }else {
                reqVO.getTargetCountryCodes().addAll(Arrays.stream(attributeScope.split(Constant.COMMA)).collect(Collectors.toSet()));
            }
        }
    }

    /**
     * 处理SPU草稿和审核状态。
     * @param spuDetail SPU详细信息对象
     * @param reqVO SPU的ID
     */
    private void processDraftAndStatus(SpuDetailVO spuDetail, UpdateProductGlobalAttributeReqVO reqVO,SpuDetailVO dbSpuDetail) {
        boolean taxRateChangeFlag = isTaxRateChangeFlag(reqVO);
        if (taxRateChangeFlag) {
            spuDetail.getSpuVO().setTaxAuditStatus(SpuTaxAuditStatusEnum.WAITING_APPROVED.getCode());
        }
        spuDetail.getSpuVO().setAuditStatus(SpuAuditStatusEnum.WAITING_APPROVED.getCode());
        spuDetail.getSpuVO().setLevel(AuditLevelEnum.ZERO.getLevel());
        spuDraftManageService.saveOrUpdateDraft(spuDetail);

        SpuPO spuPO = new SpuPO();
        spuPO.setSpuId(reqVO.getSpuId());
        spuPO.setAuditStatus(SpuAuditStatusEnum.WAITING_APPROVED.getCode());
        if (taxRateChangeFlag) {
            spuPO.setTaxAuditStatus(SpuTaxAuditStatusEnum.WAITING_APPROVED.getCode());
        }
        spuPO.setUpdater(reqVO.getUpdater());
        spuPO.setUpdateTime(new Date());
        spuAtomicService.saveOrUpdate(spuPO);
        // 异步处理
        //CompletableFuture.runAsync(() -> this.recordDiffInfo(spuDetail, reqVO, dbSpuDetail));
    }

    private void recordDiffInfo(SpuDetailVO spuDetail, UpdateProductGlobalAttributeReqVO reqVO,SpuDetailVO dbSpuDetail) {
        try {
            // 比对修改的记录
            String compareResult = spuCompareService.compareSpuDetail(dbSpuDetail,spuDetail);
            if (StringUtils.isNotBlank(compareResult)) {
                SpuAuditRecordVO recordVO = new SpuAuditRecordVO();
                recordVO.setAuditStatus(SpuAuditStatusEnum.WAITING_APPROVED.getCode());
                recordVO.setSpuId(reqVO.getSpuId());
                recordVO.setRemark(compareResult);
                recordVO.setAuditErp(reqVO.getUpdater());
                spuAuditRecordManageService.add(recordVO);
            }
        } catch (Exception e) {
            log.error("processDraftAndStatus 比对写入数据和DB数据异常,reqVO={}",JSON.toJSONString(reqVO));
        }
    }

    /**
     * 判断是否有税率相关属性的更新请求
     * @param reqVO 更新产品全局属性请求对象
     * @return 是否有税率相关属性的更新请求
     */
    private boolean isTaxRateChangeFlag(UpdateProductGlobalAttributeReqVO reqVO) {
        boolean taxRateChangeFlag = Boolean.FALSE;
        if (CountryConstant.COUNTRY_VN.equals(reqVO.getSourceCountryCode())) {
            taxRateChangeFlag = Optional.ofNullable(reqVO.getSkuPropertyApiDTOList()).orElseGet(ArrayList::new).stream().anyMatch(vo-> VN_TAX_RATE.equals(vo.getAttributeId()));
        }else if (CountryConstant.COUNTRY_BR.equals(reqVO.getSourceCountryCode())){
            taxRateChangeFlag = Optional.ofNullable(reqVO.getSkuPropertyApiDTOList()).orElseGet(ArrayList::new).stream().anyMatch(vo-> taxRateIdSet.contains(vo.getAttributeId()) || taxRateAmountIdSet.contains(vo.getAttributeId()));
        }
        return taxRateChangeFlag;
    }

    /**
     * 处理产品全局属性的范围和出口设置。
     * @param reqVO 更新产品全局属性请求对象。
     * @param spuDetail 产品详细信息对象。
     */
    private void processAttributeScope(UpdateProductGlobalAttributeReqVO reqVO, SpuDetailVO spuDetail) {
        // 合并已经存储和新增的销售国家
        String attributeScope = spuDetail.getSpuVO().getAttributeScope();
        Set<String> atrributeSet = Sets.newHashSet();
        if (StringUtils.isNotBlank(attributeScope)) {
            atrributeSet = Arrays.stream(attributeScope.split(Constant.COMMA)).collect(Collectors.toSet());
            atrributeSet.addAll(reqVO.getTargetCountryCodes());
        }
        spuDetail.getSpuVO().setAttributeScope(String.join(Constant.COMMA, atrributeSet));
        spuDetail.getSpuVO().setIsExport(reqVO.getExport());
    }

    /**
     * 根据UpdateProductGlobalAttributeReqVO获取SpuDetailVO对象。
     * @param reqVO UpdateProductGlobalAttributeReqVO对象，包含SPU ID、语言、是否导出以及目标国家代码等信息。
     * @return SpuDetailVO对象，包含SPU的详细信息。
     */
    private SpuDetailVO getSpuDetailVO(UpdateProductGlobalAttributeReqVO reqVO) {
        SpuDetailReqVO detailReqVO = new SpuDetailReqVO();
        detailReqVO.setSpuId(reqVO.getSpuId());
        detailReqVO.setLang(LANG_ZH);
        detailReqVO.setIsExport(reqVO.getExport());
        detailReqVO.setAttributeScope(String.join(Constant.COMMA, reqVO.getTargetCountryCodes()));
        return spuReadManageService.getSpuDetail(detailReqVO);
    }

    /**
     * 处理商品 SKU 全球属性列表。
     * @param reqVO 更新商品全球属性请求对象
     * @param catId 类目 ID
     * @param skuVOList SKU 列表
     */
    private void handleSkuGlobalAttributeList(UpdateProductGlobalAttributeReqVO reqVO, Long catId,List<SkuVO> skuVOList) {
        GlobalAttributeQueryReqVO queryReqVO = this.getQueryReqVO(catId, AttributeDimensionEnum.SKU, reqVO);
        List<String> countryCodeList = CountryConstant.COUNTRY_ZH.equals(reqVO.getSourceCountryCode()) ? countryManageService.getCountryCodeList() : Lists.newArrayList(reqVO.getTargetCountryCodes());
        List<GroupPropertyVO> groupPropertyVOList = globalAttributeManageService.queryGroupPropertyVos(queryReqVO.getCategoryId(), queryReqVO.getSourceCountryCode(), countryCodeList, queryReqVO.getAttributeDimension(), StringUtils.isNotBlank(queryReqVO.getLang()) ? queryReqVO.getLang() : LANG_ZH, queryReqVO.getExport());
        if (CollectionUtils.isEmpty(groupPropertyVOList)) {
            throw new BizException(String.format("商品 SKU=%s 类目没有绑定%s跨境属性", Objects.nonNull(reqVO.getSkuId()) ? reqVO.getSkuId() : reqVO.getSpuId(),AttributeDimensionEnum.SKU));
        }

        // 跨境属性映射
        Map<Long,PropertyVO> skuGlobalResMap = Maps.newHashMap();

        for (GroupPropertyVO groupPropertyVO : groupPropertyVOList) {
            if (CollectionUtils.isNotEmpty(groupPropertyVO.getPropertyVOS())) {
                groupPropertyVO.getPropertyVOS().forEach(propertyVO -> skuGlobalResMap.put(propertyVO.getAttributeId(), propertyVO));
            }
        }

        // 发品必填属性ID集合，校验必填跨境属性
        Map<Long, PropertyVO> publishCheckAttributeMap = this.getPublishCheckAttributeMap(groupPropertyVOList);
        // 校验SKU跨境属性是否属于当前类目
        this.validateGlobalAttribute(reqVO, skuGlobalResMap, Objects.nonNull(reqVO.getSkuId()) ? reqVO.getSkuId() : reqVO.getSpuId(), AttributeDimensionEnum.SKU);

        // 处理SKU跨境属性
        int size = skuVOList.size();
        if (size == Constant.ONE) {
            this.processEachSkuGlobalPropertyList(reqVO, skuVOList.get(0), groupPropertyVOList, publishCheckAttributeMap);
        }else {
            for (SkuVO skuVO : skuVOList) {
                this.processEachSkuGlobalPropertyList(reqVO, skuVO, groupPropertyVOList, publishCheckAttributeMap);
            }
        }
    }

    /**
     * 获取发布必填检查属性映射。
     * @param groupPropertyVOList 组属性VO列表。
     * @return 发布必填检查属性映射。
     */
    @NotNull
    private Map<Long, PropertyVO> getPublishCheckAttributeMap(List<GroupPropertyVO> groupPropertyVOList) {
        Map<Long,PropertyVO> publishCheckAttributeMap = Maps.newHashMap();
        for (GroupPropertyVO groupPropertyVO : groupPropertyVOList) {
            if (Objects.equals(AttributeCheckTypeEnum.PUBLISH_PRODUCT.getCode(), groupPropertyVO.getRequirement())) {
                publishCheckAttributeMap = groupPropertyVO.getPropertyVOS().stream().filter(Objects::nonNull).collect(Collectors.toMap(PropertyVO::getAttributeId, Function.identity()));
            }
        }
        return publishCheckAttributeMap;
    }

    /**
     * 处理每个 SKU 的跨境属性列表。
     * @param reqVO UpdateProductGlobalAttributeReqVO 对象，包含请求信息。
     * @param skuVO SkuVO 对象，表示当前处理的 SKU。
     * @param groupPropertyVOList GroupPropertyVO 列表，表示全局属性组的信息。
     */
    private void processEachSkuGlobalPropertyList(UpdateProductGlobalAttributeReqVO reqVO, SkuVO skuVO, List<GroupPropertyVO> groupPropertyVOList, Map<Long,PropertyVO> publishCheckAttributeMap) {
        if (CollectionUtils.isEmpty(skuVO.getSkuInterPropertyList())){
            this.validateNoExistedPublishCheckAttribute(reqVO.getSkuPropertyApiDTOList(), publishCheckAttributeMap, reqVO.getSkuId());
            skuVO.setSkuInterPropertyList(this.processGroupPropertyVoList(groupPropertyVOList, reqVO.getSkuPropertyApiDTOList()));
        }else {
            List<GroupPropertyVO> skuInterPropertyList = skuVO.getSkuInterPropertyList();
             // 过滤已存储的跨境属性 只保留selected属性true的属性值
            this.filterSelectedPropertyValueVo(skuInterPropertyList);

            // 发品必填跨境属性必填校验；1、商品上不存在 2、修改入参里不存在

            List<GroupPropertyVO> storedGroupPropertyVOList = productAttributeConvertService.convertAttributeFromDraft(skuInterPropertyList,groupPropertyVOList);

//            // 已存储的跨境属性
//            Map<String, List<String>> spuGlobalAttributeMap = spuConvertService.splitInterAttributeStr(spuConvertService.getInterAttribute(skuInterPropertyList));
//            List<GroupPropertyVO> storedGroupPropertyVOList = Lists.newArrayList();
//            spuConvertService.fillInterAttributeSelected(groupPropertyVOList, storedGroupPropertyVOList, spuGlobalAttributeMap);

            // 解析已存储的跨境属性
            List<PropertyVO> storedSkuPropertyApiDTOList = Lists.newArrayList();
            storedGroupPropertyVOList.forEach(groupPropertyVO -> storedSkuPropertyApiDTOList.addAll(groupPropertyVO.getPropertyVOS()));

            List<PropertyVO> inputSkuPropertyApiDTOList = reqVO.getSkuPropertyApiDTOList();
            // 将inputSpuPropertyApiDTOList中的内容merge到storedSpuPropertyApiDTOList里面
            this.mergePropertyLists(storedSkuPropertyApiDTOList,inputSkuPropertyApiDTOList);
            // 校验发品必填跨境属性
            this.validateNoExistedPublishCheckAttribute(storedSkuPropertyApiDTOList, publishCheckAttributeMap, reqVO.getSkuId());
            // 转换为修改后的跨境属性列表
            skuVO.setSkuInterPropertyList(this.processGroupPropertyVoList(groupPropertyVOList, storedSkuPropertyApiDTOList));
        }
        // 重新计算未税采购价
        this.calculatePurchasePrice(reqVO, skuVO);
    }

    /**
     * 1、越南税率修改，联动计算未税采购价
     * 2、巴西修改税率、联动修改税金和未税采购价
     * */
    private void calculatePurchasePrice(UpdateProductGlobalAttributeReqVO reqVO, SkuVO skuVO) {
        if (CountryConstant.COUNTRY_VN.equals(reqVO.getSourceCountryCode())
                && reqVO.getSkuPropertyApiDTOList().stream().anyMatch(dto-> VN_TAX_RATE.equals(dto.getAttributeId()))) {
            // 过滤出越南税率
            Optional<PropertyVO> first = reqVO.getSkuPropertyApiDTOList().stream().filter(dto -> VN_TAX_RATE.equals(dto.getAttributeId())).findFirst();
            if (!first.isPresent()) {
                log.warn("processEachSkuGlobalPropertyList 越南税率填写为空，不需要重新计算 reqVO.getSkuPropertyApiDTOList={}",JSON.toJSONString(reqVO.getSkuPropertyApiDTOList()));
                return;
            }
            // 计算越南未税采购价
            SkuCalculatePriceReqVO priceReqVO = getVnSkuCalculatePriceReqVO(reqVO, skuVO, first.get());
            SkuCalculateTaxPriceVO calculateTaxPriceVO = skuReadManageService.getSkuCalculateSalePrice(priceReqVO);
            if (!skuVO.getPurchasePrice().equals(calculateTaxPriceVO.getPurchasePrice())) {
                // 计算的采购价不同时，修改
                skuVO.setPurchasePrice(Objects.nonNull(calculateTaxPriceVO.getPurchasePrice()) ?  calculateTaxPriceVO.getPurchasePrice().toPlainString() : skuVO.getPurchasePrice());
                log.info("processEachSkuGlobalPropertyList 越南税率修改，重新计算未税采购价 入参:{},出参:{}",JSON.toJSONString(priceReqVO),JSON.toJSONString(calculateTaxPriceVO));
            }
        } else if (CountryConstant.COUNTRY_BR.equals(reqVO.getSourceCountryCode())) {
            // 巴西计算未税采购价的逻辑
            /**
             * 四个税率对应的值都需要有才可以计算，如果表格里都填填时，不计算；有一个存在，就要在sku上补全后计算
             * 73: 巴西 IPI 税率%
             * 76：供应商ICMS税率 %
             * 77: 巴西 PIS 税率 %
             * 79: 巴西COFINS税率 %
             *
             * 74: 巴西 IPI 金额
             * 78: 巴西 PIS 金额
             * 80: 巴西COFINS金额
             * 106: 巴西 ICMS 金额
             * */
            List<PropertyVO> skuPropertyApiDTOList = reqVO.getSkuPropertyApiDTOList();
            if (skuPropertyApiDTOList.stream().noneMatch(apiDto -> taxRateIdSet.contains(apiDto.getAttributeId()) || taxRateAmountIdSet.contains(apiDto.getAttributeId()))) {
                return;
            }
            // 税金跨境属性映射
            Map<Long, String> skuTaxRateAmountMap = this.getTaxRelationMap(skuVO, taxRateAmountIdSet);

            SkuCalculatePriceReqVO priceReqVO = this.getBrSkuCalculatePriceReqVO(reqVO, skuVO, taxRateIdSet);
            SkuCalculateTaxPriceVO calculateTaxPriceVO = skuReadManageService.getSkuCalculateSalePrice(priceReqVO);
            // 未税采购价变更状态
            boolean purchaseChangedStatus = calculateTaxPriceVO.getPurchasePrice().compareTo(new BigDecimal(skuVO.getPurchasePrice())) != 0;
            // 计算后的税金映射
            Map<Long, BigDecimal> calculateTaxMap = calculateTaxPriceVO.getTaxMap();
            // 输入修改的税金映射
            Map<Long, String> inputTaxRateAmountMap = this.getAttributeAndValueRelationMap(skuPropertyApiDTOList);
            Map<Long,String> diffTaxAmountMap = Maps.newHashMap();
            // 比对税金修改结果
            this.diffTaxAmountMap(calculateTaxMap, inputTaxRateAmountMap, diffTaxAmountMap, skuTaxRateAmountMap);
            // 税金有变化时，将新的税金赋值给SKU上的税金
            if (MapUtils.isNotEmpty(diffTaxAmountMap) || purchaseChangedStatus) {
                // 将新的税金设置到sku跨境属性上，并将未税采购价重新计算
                this.fillNewTaxAmountAndPurchasePrice(skuVO, taxRateAmountIdSet, diffTaxAmountMap, calculateTaxPriceVO);
                // 采购价和现在存储的数据不同时，修改采购价和税金信息
                skuVO.setPurchasePrice(calculateTaxPriceVO.getPurchasePrice().toPlainString());
            }
        }
    }

    private void fillNewTaxAmountAndPurchasePrice(SkuVO skuVO, Set<Long> taxRateAmountIdSet, Map<Long, String> diffTaxAmountMap, SkuCalculateTaxPriceVO calculateTaxPriceVO) {
        List<GroupPropertyVO> skuInterPropertyList = skuVO.getSkuInterPropertyList();
        if (CollectionUtils.isNotEmpty(skuInterPropertyList)) {
            Map<Long,String> finalTaxAmountMap = Maps.newHashMap();

            for (GroupPropertyVO groupPropertyVO : skuInterPropertyList) {
                List<PropertyVO> propertyVOList = groupPropertyVO.getPropertyVOS();
                if (CollectionUtils.isEmpty(propertyVOList)) {
                    continue;
                }
                for (PropertyVO vo : propertyVOList) {
                    if (!taxRateAmountIdSet.contains(vo.getAttributeId())) {
                        continue;
                    }
                    // 不存在差异跳过
                    if (!diffTaxAmountMap.containsKey(vo.getAttributeId())){
                        PropertyValueVO propertyValueVO = vo.getPropertyValueVOList().get(0);
                        finalTaxAmountMap.put(vo.getAttributeId(), propertyValueVO.getAttributeValueName());
                        continue;
                    }else {
                        finalTaxAmountMap.put(vo.getAttributeId(), diffTaxAmountMap.get(vo.getAttributeId()));
                    }
                    // 存在差异税金，将差异补充到跨境属性列表
                    if (CollectionUtils.isNotEmpty(vo.getPropertyValueVOList())){
                        PropertyValueVO propertyValueVO = vo.getPropertyValueVOList().get(0);
                        propertyValueVO.setAttributeValueName(diffTaxAmountMap.get(vo.getAttributeId()));
                    }else {
                        PropertyValueVO propertyValueVO = new PropertyValueVO();
                        propertyValueVO.setAttributeId(vo.getAttributeId());
                        propertyValueVO.setLang(LANG_ZH);
                        propertyValueVO.setAttributeValueName(diffTaxAmountMap.get(vo.getAttributeId()));
                        vo.setPropertyValueVOList(Lists.newArrayList(propertyValueVO));
                    }
                    log.info("GlobalAttributeWriteManageServiceImpl 将差异税金跨境属性设置成新值, 跨境属性ID={}，税金值={}",vo.getAttributeId(), diffTaxAmountMap.get(vo.getAttributeId()));
                }
            }
            // 税金的总和
            BigDecimal taxAmountTotal = finalTaxAmountMap.values().stream().map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 采购价=含税采购价-税金总和
            BigDecimal purchasePrice = calculateTaxPriceVO.getTaxPurchasePrice().subtract(taxAmountTotal);
            calculateTaxPriceVO.setPurchasePrice(purchasePrice);
        }
    }

    /**
     * 比较和更新税金金额映射。
     * @param calculateTaxMap 重新计算的税金金额映射。
     * @param inputTaxRateAmountMap 人工修改的税金金额映射。
     * @param diffTaxAmountMap 用于存储差异的税金金额映射。
     * @param skuTaxRateAmountMap SKU存储的税金金额映射。
     */
    private void diffTaxAmountMap(Map<Long, BigDecimal> calculateTaxMap, Map<Long, String> inputTaxRateAmountMap, Map<Long, String> diffTaxAmountMap, Map<Long, String> skuTaxRateAmountMap) {
        /**
         * 1、税金无修改，使用新计算的税金
         * 2、税金有修改，对比税金前后是否一致，不一致，重新计算采购价
         * */
        for (Map.Entry<Long, BigDecimal> entry : calculateTaxMap.entrySet()) {
            Long key = entry.getKey();
            BigDecimal afterValue = entry.getValue();
            String inputValue = inputTaxRateAmountMap.get(key);
            // 税金有人工修改，直接使用传入的值
            if (StringUtils.isNotBlank(inputValue) && afterValue.compareTo(new BigDecimal(inputValue)) != 0) {
                diffTaxAmountMap.put(key, inputValue);
                continue;
            }

            // 无修改时，比对sku存储的和计算出的结果对比
            String beforeValue = skuTaxRateAmountMap.get(entry.getKey());
            if (Objects.nonNull(afterValue) && StringUtils.isBlank(beforeValue)) {
                diffTaxAmountMap.put(key, afterValue.toPlainString());
            }else if (afterValue.compareTo(new BigDecimal(beforeValue)) != 0){
                diffTaxAmountMap.put(key, afterValue.toPlainString());
            }
        }
        log.info("GlobalAttributeWriteManageServiceImpl.diffTaxAmountMap 税金比对结果，输入的税金inputTaxRateAmountMap={},sku存储的税金skuTaxRateAmountMap={},重新计算的税金calculateTaxMap={},比对结果diffTaxAmountMap={}"
                ,JSON.toJSONString(inputTaxRateAmountMap),JSON.toJSONString(skuTaxRateAmountMap),JSON.toJSONString(calculateTaxMap),JSON.toJSONString(diffTaxAmountMap) );
    }

    private Map<Long, String> getTaxRelationMap(SkuVO skuVO, Set<Long> idSet) {
        // 税金、税率跨境属性列表
        List<PropertyVO> taxRateAmountPropertyVOList = skuVO.getSkuInterPropertyList().stream().filter(Objects::nonNull)
                .flatMap(groupPropertyVO -> groupPropertyVO.getPropertyVOS().stream())
                .filter(vo -> idSet.contains(vo.getAttributeId())).collect(Collectors.toList());

        // 税金/税率映射
        Map<Long, String> taxRelationMap = this.getAttributeAndValueRelationMap(taxRateAmountPropertyVOList);
        return taxRelationMap;
    }

    private Map<Long, String> getAttributeAndValueRelationMap(List<PropertyVO> taxRateAmountPropertyVOList) {
        Map<Long, String> taxRelationMap = taxRateAmountPropertyVOList.stream().filter(Objects::nonNull)
                .filter(propertyVO -> CollectionUtils.isNotEmpty(propertyVO.getPropertyValueVOList()))
                .collect(Collectors.toMap(PropertyVO::getAttributeId
                        , vo -> Objects.isNull(vo.getPropertyValueVOList().get(0)) ?   ""
                                : (StringUtils.isNotBlank(vo.getPropertyValueVOList().get(0).getAttributeValueName()) ? vo.getPropertyValueVOList().get(0).getAttributeValueName() : "")));
        return taxRelationMap;
    }

    /**
     * 根据给定的请求对象、SKU信息和属性信息，生成VN SKU价格计算请求对象。
     * @param reqVO 更新产品全球属性请求对象，用于获取源国家/地区代码。
     * @param skuVO SKU信息对象，用于获取SKU ID和类目ID。
     * @param propertyVO 属性信息对象，用于获取税率。
     * @return VN SKU价格计算请求对象。
     */
    private SkuCalculatePriceReqVO getVnSkuCalculatePriceReqVO(UpdateProductGlobalAttributeReqVO reqVO, SkuVO skuVO, PropertyVO propertyVO) {
        SkuCalculatePriceReqVO priceReqVO = new SkuCalculatePriceReqVO();
        priceReqVO.setSkuId(skuVO.getSkuId());
        priceReqVO.setCatId(skuVO.getCatId());
        priceReqVO.setSourceCountryCode(reqVO.getSourceCountryCode());
        priceReqVO.setTaxPurchasePrice(new BigDecimal(skuVO.getTaxPurchasePrice()));
        priceReqVO.setSupplierCode(skuVO.getVendorCode());
        PropertyValueVO propertyValueVO = propertyVO.getPropertyValueVOList().get(0);
        priceReqVO.setTaxRate(new BigDecimal(propertyValueVO.getAttributeValueName()));
        return priceReqVO;
    }

    /**
     * 根据给定的请求体、SKU信息和税率ID集合生成SkuCalculatePriceReqVO对象。
     * @param reqVO UpdateProductGlobalAttributeReqVO对象，包含源国家/地区代码等信息。
     * @param skuVO SkuVO对象，包含SKU的ID、分类ID和含税采购价格等信息。
     * @param taxRateIdSet Set<Long>类型的集合，包含税率ID。
     * @return SkuCalculatePriceReqVO对象，用于计算SKU价格。
     */
    private SkuCalculatePriceReqVO getBrSkuCalculatePriceReqVO(UpdateProductGlobalAttributeReqVO reqVO, SkuVO skuVO,Set<Long> taxRateIdSet) {
        SkuCalculatePriceReqVO priceReqVO = new SkuCalculatePriceReqVO();
        priceReqVO.setSkuId(skuVO.getSkuId());
        priceReqVO.setCatId(skuVO.getCatId());
        priceReqVO.setSourceCountryCode(reqVO.getSourceCountryCode());
        priceReqVO.setTaxPurchasePrice(new BigDecimal(skuVO.getTaxPurchasePrice()));
        priceReqVO.setSupplierCode(skuVO.getVendorCode());
        Map<Long, String> taxRateMap = this.getTaxRelationMap(skuVO, taxRateIdSet);
        priceReqVO.setTaxRateMap(taxRateMap);
        return priceReqVO;
    }

    /**
     * 验证商品跨境属性是否必填。
     * @param inputPropertyApiDTOList 输入的属性列表。
     * @param publishCheckAttributeMap 已经存在的发布检查属性映射。
     * @param id 商品ID。
     * @throws BizException 如果有必填的跨境属性未提供。
     */
    private void validateNoExistedPublishCheckAttribute(List<PropertyVO> inputPropertyApiDTOList, Map<Long, PropertyVO> publishCheckAttributeMap,Long id) {
        if (MapUtils.isNotEmpty(publishCheckAttributeMap)) {
            Set<String> attributeNameSet = Sets.newHashSet();
            if (CollectionUtils.isEmpty(inputPropertyApiDTOList)){
                publishCheckAttributeMap.forEach((attributeId, vo)-> attributeNameSet.add(vo.getAttributeName()));
            }else {
                // 已存储的发品必填跨境属性
                Set<Long> existAttributeSet = inputPropertyApiDTOList.stream().filter(Objects::nonNull)
                        .filter(propertyVO -> publishCheckAttributeMap.containsKey(propertyVO.getAttributeId()) &&
                                (CollectionUtils.isNotEmpty(Optional.ofNullable(propertyVO.getPropertyValueVOList())
                                        .orElseGet(ArrayList::new)
                                        .stream().filter(Objects::nonNull)
                                        .filter(propertyValueVO -> Objects.nonNull(propertyValueVO.getSelected()) && propertyValueVO.getSelected()).collect(Collectors.toList()))))
                        .map(PropertyVO::getAttributeId).collect(Collectors.toSet());

                Collection<Long> noExistAttributeSet = CollectionUtils.subtract(publishCheckAttributeMap.keySet(), existAttributeSet);
                 // 必填属性不存在
                 if (CollectionUtils.isNotEmpty(noExistAttributeSet)) {
                      noExistAttributeSet.forEach(attributeId -> attributeNameSet.add(publishCheckAttributeMap.get(attributeId).getAttributeName()));
                 }
            }
            // 发品必填跨境属性名称提示
            if (CollectionUtils.isNotEmpty(attributeNameSet)) {
                throw new BizException(String.format("商品 %s 跨境属性 %s 必填", id, String.join(Constant.COMMA, attributeNameSet)));
            }
        }
    }

    /**
     * 过滤选中的属性值对象列表，仅保留selected为true的属性值。
     * @param interPropertyList 属性组列表
     */
    private void filterSelectedPropertyValueVo(List<GroupPropertyVO> interPropertyList) {
        for (GroupPropertyVO groupPropertyVO : interPropertyList) {
            if (CollectionUtils.isEmpty(groupPropertyVO.getPropertyVOS())) {
                continue;
            }
            List<PropertyVO> propertyVOList = groupPropertyVO.getPropertyVOS();
             // 过滤已存储的跨境属性值 只保留selected属性true的属性值
            for (PropertyVO propertyVO : propertyVOList) {
                if (CollectionUtils.isEmpty(propertyVO.getPropertyValueVOList())) {
                    continue;
                }

                List<PropertyValueVO> propertyValueVOList = propertyVO.getPropertyValueVOList().stream().filter(Objects::nonNull)
                        .filter(propertyValueVO -> Objects.nonNull(propertyValueVO.getSelected()) && propertyValueVO.getSelected()).collect(Collectors.toList());
                propertyVO.setPropertyValueVOList(propertyValueVOList);
            }
        }
    }

    /**
     * 处理SPU全局属性列表。
     * @param reqVO 更新产品全局属性请求体
     * @param catId 类目ID
     * @param storedPropertyVOList 已存储的扩展属性列表
     * @return 处理后的SPU全局属性列表
     */
    private List<GroupPropertyVO>  handleSpuGlobalAttributeList(UpdateProductGlobalAttributeReqVO reqVO, Long catId,List<GroupPropertyVO> storedPropertyVOList) {
        GlobalAttributeQueryReqVO queryReqVO = this.getQueryReqVO(catId, AttributeDimensionEnum.SPU, reqVO);
        List<String> countryCodeList = CountryConstant.COUNTRY_ZH.equals(reqVO.getSourceCountryCode()) ? countryManageService.getCountryCodeList() : Lists.newArrayList(reqVO.getTargetCountryCodes());
        List<GroupPropertyVO> groupPropertyVOList = globalAttributeManageService.queryGroupPropertyVos(queryReqVO.getCategoryId(), queryReqVO.getSourceCountryCode(), countryCodeList, queryReqVO.getAttributeDimension(), StringUtils.isNotBlank(queryReqVO.getLang()) ? queryReqVO.getLang() : LANG_ZH, queryReqVO.getExport());
        if (CollectionUtils.isEmpty(groupPropertyVOList)) {
            throw new BizException(String.format("商品 SPU=%s SKU=%s 类目没有绑定%s跨境属性", reqVO.getSpuId(), reqVO.getSkuId(),AttributeDimensionEnum.SPU));
        }

        // 跨境属性映射
        Map<Long,PropertyVO> spuGlobalResMap = Maps.newHashMap();
        for (GroupPropertyVO groupPropertyVO : groupPropertyVOList) {
            if (CollectionUtils.isNotEmpty(groupPropertyVO.getPropertyVOS())) {
                groupPropertyVO.getPropertyVOS().forEach(propertyVO -> spuGlobalResMap.put(propertyVO.getAttributeId(), propertyVO));
            }
        }
        // 校验SPU跨境属性是否属于当前类目
        this.validateGlobalAttribute(reqVO, spuGlobalResMap, reqVO.getSpuId(), AttributeDimensionEnum.SPU);

        // 发品必填属性ID集合，校验必填跨境属性
        Map<Long, PropertyVO> publishCheckAttributeMap = this.getPublishCheckAttributeMap(groupPropertyVOList);

        if (CollectionUtils.isEmpty(storedPropertyVOList)) {
            this.validateNoExistedPublishCheckAttribute(reqVO.getSpuPropertyApiDTOList(), publishCheckAttributeMap, reqVO.getSpuId());
            return this.processGroupPropertyVoList(groupPropertyVOList, reqVO.getSpuPropertyApiDTOList());
        }else {
            // 过滤selected属性true的属性值
            this.filterSelectedPropertyValueVo(storedPropertyVOList);

            List<GroupPropertyVO> storedGroupPropertyVOList = productAttributeConvertService.convertAttributeFromDraft(storedPropertyVOList,groupPropertyVOList);

//            // 已存储的跨境属性
//            Map<String, List<String>> spuGlobalAttributeMap = spuConvertService.splitInterAttributeStr(spuConvertService.getInterAttribute(storedPropertyVOList));
//            List<GroupPropertyVO> storedGroupPropertyVOList = Lists.newArrayList();
//            spuConvertService.fillInterAttributeSelected(groupPropertyVOList, storedGroupPropertyVOList, spuGlobalAttributeMap);

            // 解析已存储的跨境属性
            List<PropertyVO> storedSpuPropertyApiDTOList = Lists.newArrayList();
            storedGroupPropertyVOList.forEach(groupPropertyVO -> storedSpuPropertyApiDTOList.addAll(groupPropertyVO.getPropertyVOS()));

            // 将inputSpuPropertyApiDTOList中的内容merge到storedSpuPropertyApiDTOList里面
            this.mergePropertyLists(storedSpuPropertyApiDTOList,reqVO.getSpuPropertyApiDTOList());
            this.validateNoExistedPublishCheckAttribute(storedSpuPropertyApiDTOList, publishCheckAttributeMap, reqVO.getSpuId());
            // 处理成存储跨境属性结构
            return this.processGroupPropertyVoList(groupPropertyVOList, storedSpuPropertyApiDTOList);
        }
    }

    /**
     * 根据给定的 GroupPropertyVO 列表和 PropertyVO 列表，处理并返回符合要求的 GroupPropertyVO 列表。
     * @param groupPropertyVOList GroupPropertyVO 对象列表，表示要处理的属性组。
     * @param storedSpuPropertyApiDTOList PropertyVO 对象列表，表示已存储的属性。
     * @return 处理后的 GroupPropertyVO 对象列表。
     */
    private List<GroupPropertyVO> processGroupPropertyVoList(List<GroupPropertyVO> groupPropertyVOList,List<PropertyVO> storedSpuPropertyApiDTOList) {
        List<GroupPropertyVO> resultGroupPropertyVoList = Lists.newArrayList();

        Map<Integer,Set<Long>> checkTypeAttributeIdsMap = Maps.newHashMap();
        for (GroupPropertyVO vo : groupPropertyVOList) {
            if (checkTypeAttributeIdsMap.containsKey(vo.getRequirement()) && CollectionUtils.isNotEmpty(checkTypeAttributeIdsMap.get(vo.getRequirement()))) {
                checkTypeAttributeIdsMap.get(vo.getRequirement()).addAll(Optional.ofNullable(vo.getPropertyVOS()).orElseGet(ArrayList::new).stream().map(PropertyVO::getAttributeId).collect(Collectors.toSet()));
            }else {
                checkTypeAttributeIdsMap.put(vo.getRequirement(),Optional.ofNullable(vo.getPropertyVOS()).orElseGet(ArrayList::new).stream().map(PropertyVO::getAttributeId).collect(Collectors.toSet()));
            }
        }
        // 跨境属性按照校验时机来分组
        Map<Integer,List<PropertyVO>> checkTypePropertyListMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(storedSpuPropertyApiDTOList)) {
            for (PropertyVO vo : storedSpuPropertyApiDTOList) {
                for (Map.Entry<Integer, Set<Long>> entry : checkTypeAttributeIdsMap.entrySet()) {
                    if (checkTypePropertyListMap.containsKey(entry.getKey()) && entry.getValue().contains(vo.getAttributeId())){
                        checkTypePropertyListMap.get(entry.getKey()).add(vo);
                    }else if (entry.getValue().contains(vo.getAttributeId())){
                        checkTypePropertyListMap.put(entry.getKey(), Lists.newArrayList(vo));
                    }
                }
            }

            checkTypePropertyListMap.forEach((checkType,propertyVoList) -> {
                GroupPropertyVO groupPropertyVO = new GroupPropertyVO();
                groupPropertyVO.setRequirement(checkType);
                groupPropertyVO.setPropertyVOS(propertyVoList);
                resultGroupPropertyVoList.add(groupPropertyVO);
            });
        }
        return resultGroupPropertyVoList;
    }

    private void validateGlobalAttribute(UpdateProductGlobalAttributeReqVO reqVO, Map<Long,PropertyVO> globalResMap, Long spuId, AttributeDimensionEnum spu) {
        List<PropertyVO> propertyVOList = null;
        if(AttributeDimensionEnum.SPU == spu){
            propertyVOList = reqVO.getSpuPropertyApiDTOList();
        } else {
            propertyVOList = reqVO.getSkuPropertyApiDTOList();
        }

        if(CollectionUtils.isEmpty(propertyVOList)){
            log.error("GlobalAttributeWriteManageServiceImpl.validateGlobalAttribute propertyVOList is null");
            return;
        }

        for (PropertyVO propertyVO : propertyVOList) {
            if (!globalResMap.containsKey(propertyVO.getAttributeId())) {
                throw new BizException(String.format("商品%s类目未绑定当前%s跨境属性 %s", spuId, spu, propertyVO.getAttributeName()));
            }

            PropertyVO attributeResVO = globalResMap.get(propertyVO.getAttributeId());
            if ((attributeResVO.getAttributeInputType().equals(AttributeInputTypeEnum.RADIO.getCode()) || attributeResVO.getAttributeInputType().equals(AttributeInputTypeEnum.CHECKBOX.getCode()))
                    && CollectionUtils.isNotEmpty(propertyVO.getPropertyValueVOList())) {
                List<PropertyValueVO> attributeValueList = attributeResVO.getPropertyValueVOList();
                if (CollectionUtils.isEmpty(attributeResVO.getPropertyValueVOList())) {
                    throw new BizException(String.format("商品 %s 类目绑定的%s跨境属性无跨境属性值，请不要填写错误信息", spuId, spu));
                }

                List<PropertyValueVO> propertyValueVOList = propertyVO.getPropertyValueVOList();
                PropertyValueVO propertyValueVO = propertyValueVOList.get(0);
                if (!attributeValueList.stream().anyMatch(valueResVo -> valueResVo.getAttributeValueId().equals(propertyValueVO.getAttributeValueId()))) {
                    throw new BizException(String.format("商品 %s 类目绑定的%s跨境属性无此跨境属性值 %s ，请不要填写错误信息", spuId, spu, propertyValueVO.getAttributeValueName()));
                }
            }
        }
    }

    private GlobalAttributeQueryReqVO getQueryReqVO(Long catId, AttributeDimensionEnum spu, UpdateProductGlobalAttributeReqVO reqVO) {
        GlobalAttributeQueryReqVO queryReqVO = new GlobalAttributeQueryReqVO();
        queryReqVO.setCategoryId(catId);
        queryReqVO.setAttributeDimension(spu.getCode());
        queryReqVO.setLang(LangConstant.LANG_ZH);
        queryReqVO.setExport(reqVO.getExport());
        queryReqVO.setSourceCountryCode(reqVO.getSourceCountryCode());
        queryReqVO.setTargetCountryCodes(reqVO.getTargetCountryCodes());
        return queryReqVO;
    }

    /**
     * 处理SPU描述信息更新。
     * @param reqVO 更新商品全局属性请求对象，包含SPU描述信息。
     * @param spuDetailVO SPU详细信息对象，用于存储更新后的SPU描述信息。
     */
    private void handleSpuDescription(UpdateProductGlobalAttributeReqVO reqVO, SpuDetailVO spuDetailVO) {
        if (MapUtils.isNotEmpty(reqVO.getDescriptionMap())) {
            Map<String, String> storedPcDescriptionMap = spuDetailVO.getPcDescriptionMap();
            if (MapUtils.isNotEmpty(storedPcDescriptionMap)) {
                storedPcDescriptionMap.putAll(reqVO.getDescriptionMap());
                spuDetailVO.setPcDescriptionMap(storedPcDescriptionMap);
            }else {
                spuDetailVO.setPcDescriptionMap(reqVO.getDescriptionMap());
            }
            log.info("GlobalAttributeWriteManageServiceImpl.updateGlobalAttribute 更新商品详情完成spuId={}", reqVO.getSpuId());
        }
    }

    /**
     * 更新商品标题的多语言信息。
     * @param reqVO 更新商品全局属性请求对象。
     * @param spuDetailVO 商品详细信息对象。
     */
    private void handleSpuTitle(UpdateProductGlobalAttributeReqVO reqVO, SpuDetailVO spuDetailVO) {
        if (CollectionUtils.isNotEmpty(reqVO.getSpuLangApiDTOList())){
            // 多语言数据信息
            List<SpuLangExtendVO> extendPropertyList = spuDetailVO.getSpuVO().getSpuLangExtendVOList();
            if (CollectionUtils.isNotEmpty(extendPropertyList)) {
                this.processSpuLangExtendList(reqVO, spuDetailVO, extendPropertyList);
            }else {
                spuDetailVO.getSpuVO().setSpuLangExtendVOList(reqVO.getSpuLangApiDTOList());
            }
            log.info("GlobalAttributeWriteManageServiceImpl.updateGlobalAttribute 更新商品名称完成,spuId={}", reqVO.getSpuId());
        }
    }

    /**
     * 处理SPU的多语言扩展属性列表。
     * @param reqVO 更新产品全局属性请求对象，包含多语言信息。
     * @param spuDetailVO SPU详细信息对象，用于存储处理后的结果。
     * @param extendPropertyList SPU的多语言扩展属性列表。
     */
    private void processSpuLangExtendList(UpdateProductGlobalAttributeReqVO reqVO, SpuDetailVO spuDetailVO, List<SpuLangExtendVO> extendPropertyList) {
        Map<String, SpuLangExtendVO> spuLangExtendVoMap = extendPropertyList.stream().filter(Objects::nonNull).collect(Collectors.toMap(SpuLangExtendVO::getLang, Function.identity()));
        // 多语言处理
        for (SpuLangExtendVO extendVO : reqVO.getSpuLangApiDTOList()) {
            String lang = extendVO.getLang();
            if (spuLangExtendVoMap.containsKey(lang)) {
                SpuLangExtendVO spuLangExtendVO = spuLangExtendVoMap.get(lang);
                spuLangExtendVO.setSpuTitle(extendVO.getSpuTitle());
            }else {
                spuLangExtendVoMap.put(lang, extendVO);
            }
        }
        spuDetailVO.getSpuVO().setSpuLangExtendVOList(Lists.newArrayList(spuLangExtendVoMap.values()));
    }

    /**
     * 将输入的属性列表合并到存储的属性列表中
     *
     * @param storedList 存储的属性列表
     * @param inputList 输入的属性列表
     */
    private void mergePropertyLists(List<PropertyVO> storedList, List<PropertyVO> inputList) {
        for (PropertyVO inputProperty : inputList) {
            Optional<PropertyVO> existingPropertyOpt = storedList.stream()
                    .filter(sortedProperty -> sortedProperty.getAttributeId().equals(inputProperty.getAttributeId()))
                    .findFirst();
            if (existingPropertyOpt.isPresent()) {
                // 如果存储的列表中已经存在相同的键，如果不存在属性值列表，将存储的替换到input属性中
                List<PropertyValueVO> storedPropertyValueVOList = existingPropertyOpt.get().getPropertyValueVOList();
                if (CollectionUtils.isEmpty(storedPropertyValueVOList)) {
                    existingPropertyOpt.get().setPropertyValueVOList(inputProperty.getPropertyValueVOList());
                }else {
                    this.mergePropertyValueVoList(storedPropertyValueVOList,inputProperty.getPropertyValueVOList(),inputProperty);
                }
            } else {
                // 如果存储的列表中不存在相同的键，则添加新的属性
                storedList.add(inputProperty);
            }
        }
    }

    public void mergePropertyValueVoList(List<PropertyValueVO> storedPropertyValueList, List<PropertyValueVO> inputPropertyValueList,PropertyVO storedProperty) {
        log.info("GlobalAttributeWriteManageServiceImpl.mergePropertyValueVoList 入参 storedPropertyValueList={},inputPropertyValueList={},storedProperty={}", JSON.toJSONString(storedPropertyValueList),JSON.toJSONString(inputPropertyValueList),JSON.toJSONString(storedProperty));
        // 单选和多选跨境属性值根据属性值ID和属性值名称 merge
        if (AttributeInputTypeEnum.RADIO.getCode().equals(storedProperty.getAttributeInputType())) {
            // 将存储的不存在的值merge到输入的属性值列表
            if (CollectionUtils.isNotEmpty(inputPropertyValueList)) {
                PropertyValueVO inputPropertyValueVO = inputPropertyValueList.get(0);
                for (PropertyValueVO storedPropertyValueVO : storedPropertyValueList) {
                    storedPropertyValueVO.setSelected(storedPropertyValueVO.getAttributeValueId().equals(inputPropertyValueVO.getAttributeValueId()) ? Boolean.TRUE : Boolean.FALSE);
                }

            }
        }else if (AttributeInputTypeEnum.CHECKBOX.getCode().equals(storedProperty.getAttributeInputType())){
            if (CollectionUtils.isNotEmpty(inputPropertyValueList)) {
                Map<Long, PropertyValueVO> inputValueMap = inputPropertyValueList.stream().filter(Objects::nonNull).collect(Collectors.toMap(PropertyValueVO::getAttributeValueId, Function.identity()));
                Set<Long> valueIds = storedPropertyValueList.stream().filter(Objects::nonNull).map(PropertyValueVO::getAttributeValueId).collect(Collectors.toSet());
                inputValueMap.forEach((valueId,value)-> {
                    if (!valueIds.contains(valueId)) {
                        storedPropertyValueList.add(value);
                    }
                });
            }
        }else { // 文本类型的根据属性值和语种merge
            Map<String, PropertyValueVO> langPropertyValueMap = inputPropertyValueList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(PropertyValueVO::getLang, Function.identity()));
            // 将存储的不存在的值merge到输入的属性值列表
            for (PropertyValueVO storePropertyValueVo : storedPropertyValueList) {
                if (langPropertyValueMap.containsKey(storePropertyValueVo.getLang())) {
                    PropertyValueVO propertyValueVO = langPropertyValueMap.get(storePropertyValueVo.getLang());
                    if (Objects.nonNull(propertyValueVO) && StringUtils.isNotBlank(propertyValueVO.getAttributeValueName()) && !propertyValueVO.getAttributeValueName().equals(storePropertyValueVo.getAttributeValueName())) {
                        storePropertyValueVo.setAttributeValueName(propertyValueVO.getAttributeValueName());
                        storePropertyValueVo.setSelected(Boolean.TRUE);
                        // 已经处理的值从map中删除
                        langPropertyValueMap.remove(storePropertyValueVo.getLang());
                    }
                }
            }

            log.info("GlobalAttributeWriteManageServiceImpl.mergePropertyValueVoList merge跨境属性值，不匹配属性值 langPropertyValueMap={}", JSON.toJSONString(langPropertyValueMap));
            if (MapUtils.isNotEmpty(langPropertyValueMap)) {
                for (PropertyValueVO valueVO : storedPropertyValueList) {
                    if (langPropertyValueMap.containsKey(valueVO.getLang())) {
                        valueVO.setAttributeValueName(langPropertyValueMap.get(valueVO.getLang()).getAttributeValueName());
                        valueVO.setSelected(Boolean.TRUE);
                    }
                }
                // storedPropertyValueList.addAll(langPropertyValueMap.values());
            }
        }
        log.info("GlobalAttributeWriteManageServiceImpl.mergePropertyValueVoList 出参 storedPropertyValueList={}", JSON.toJSONString(storedPropertyValueList));
    }
}
