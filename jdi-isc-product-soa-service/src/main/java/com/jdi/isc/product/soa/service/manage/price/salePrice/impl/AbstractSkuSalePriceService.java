package com.jdi.isc.product.soa.service.manage.price.salePrice.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.product.soa.api.common.enums.TaxTypeEnum;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailPO;
import com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum;
import com.jdi.isc.product.soa.domain.enums.taxRate.CalculateSaleTaxTypeEnum;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceVO;
import com.jdi.isc.product.soa.domain.price.biz.*;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CategoryTaxVO;
import com.jdi.isc.product.soa.domain.taxRate.po.CategoryTaxPO;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.SupplierSettlementAccountAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.CategoryTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.price.ExchangeRateManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.salePrice.SkuSalePriceManageService;
import com.jdi.isc.product.soa.service.mapstruct.taxRate.CategoryTaxConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;


/**
 * 销价审批
 *
 * <AUTHOR>
 * @date 2024/11/25
 **/
@Slf4j
@Service
public abstract class AbstractSkuSalePriceService implements SkuSalePriceManageService {


    @Resource
    protected SkuAtomicService skuAtomicService;
    @Resource
    protected SkuPriceManageService skuPriceManageService;
    @Resource
    protected CustomerSkuPriceDetailAtomicService customerSkuPriceDetailAtomicService;
    @Resource
    private CategoryTaxAtomicService categoryTaxAtomicService;
    @Resource
    private SupplierSettlementAccountAtomicService supplierSettlementAccountAtomicService;
    @Resource
    protected CountryAgreementPriceManageService countryAgreementPriceManageService;
    @Resource
    protected ExchangeRateManageService exchangeRateManageService;

    @LafValue("jdi.isc.default.vat")
    protected String defaultVatConfig;

    @Value("${spring.profiles.active}")
    protected String systemProfile;

    /**
     * 根据商品类别ID、商品SKU ID和客户所在国家，计算含税的购买价格。
     * @param skuPO 商品SKU ID
     * @return 含税的购买价格
     */
    @PFTracing
    protected SkuPricePO getPurchasePricePO(SkuPO skuPO){
        return skuPriceManageService.getOne(new SkuPriceVO(skuPO.getSkuId(), TradeDirectionEnum.SUPPLIER
                , skuPO.getSourceCountryCode()
                , supplierSettlementAccountAtomicService.getCurrencyBySupplierCode(skuPO.getVendorCode())));
    }

    @PFTracing
    protected BigDecimal getSalePrice(SkuPO skuPO, CustomerVO customerVO,String currencyCode) {
        Pair<BigDecimal, Integer> salePriceInfo = this.getSalePriceInfo(skuPO, customerVO, currencyCode);
        return salePriceInfo.getLeft();
    }

    /**
     * 获取商品的销售价格。
     * @param skuPO SKU信息对象。
     * @param customerVO 客户信息对象。
     * @return 商品的销售价格。
     */
    @PFTracing
    protected Pair<BigDecimal, Integer> getSalePriceInfo(SkuPO skuPO, CustomerVO customerVO, String currencyCode) {
        CustomerSkuPriceDetailPO detailPO = customerSkuPriceDetailAtomicService.getByClientCodeAndSkuId(
                customerVO.getClientCode(), skuPO.getSkuId());

        if (detailPO != null) {
//            return this.calculateExchangePrice(detailPO.getCustomerSalePrice()
//                    , detailPO.getCurrency(), currencyCode);
            BigDecimal price = this.calculateExchangePrice(detailPO.getCustomerSalePrice()
                    , detailPO.getCurrency(), currencyCode);
            return Pair.of(price, detailPO.getAvailableSaleStatus());
        }

        log.info("AbstractSkuSalePriceService.getSalePriceVO customerSkuPricePOS is null,获取国家协议价");
        CountryAgreementPriceReqVO countryAgreementPriceReqVO = new CountryAgreementPriceReqVO();
        countryAgreementPriceReqVO.setSkuId(skuPO.getSkuId());
        countryAgreementPriceReqVO.setSourceCountryCode(skuPO.getSourceCountryCode());
        countryAgreementPriceReqVO.setTargetCountryCode(customerVO.getCountry());
        CountryAgreementPriceVO priceVO = countryAgreementPriceManageService.getCountryAgreementPrice(countryAgreementPriceReqVO);
        if (priceVO == null || priceVO.getAgreementPrice() == null) {
            return Pair.of(null, null);
        }

//        return this.calculateExchangePrice(priceVO.getAgreementPrice(), priceVO.getCurrency(), currencyCode);
        BigDecimal price = this.calculateExchangePrice(priceVO.getAgreementPrice(), priceVO.getCurrency(), currencyCode);
        return Pair.of(price, priceVO.getAvailableSaleStatus());
    }

    /**
     * 计算价格，根据源货币和目标货币进行转换。
     * @param price 原始价格。
     * @param sourceCurrencyCode 源货币代码。
     * @param targetCurrencyCode 目标货币代码。
     * @return 转换后的价格。
     */
    @PFTracing
    protected BigDecimal calculateExchangePrice(BigDecimal price, String sourceCurrencyCode, String targetCurrencyCode) {
        if (StringUtils.isBlank(sourceCurrencyCode) || StringUtils.isBlank(targetCurrencyCode)) {
            return null;
        }
        // 设置了默认的价格的四舍五入，子逸要求
        if (StringUtils.equals(sourceCurrencyCode, targetCurrencyCode)) {
            return price.setScale(2, RoundingMode.HALF_UP);
        }
        DataResponse<BigDecimal> exchangeRate = exchangeRateManageService.getExchangeRateByCurrency(sourceCurrencyCode, targetCurrencyCode);
        if (!exchangeRate.getSuccess()) {
            return null;
        }
        // 汇率换算是2位向上进位,原需求
        return price.multiply(exchangeRate.getData()).setScale(2, RoundingMode.UP);
    }

    /**
     * 获取增值税值。
     * @param valueAddTax 增值税值，若不为空则直接返回该值。
     * @return 增值税值。
     */
    @PFTracing
    protected BigDecimal getValueAddTax(BigDecimal valueAddTax,String sourceCountryCode){
        if(valueAddTax != null){
            return valueAddTax;
        }
        return ConfigUtils.getValueByKey(defaultVatConfig, sourceCountryCode, BigDecimal.class);
    }


    /**
     * 根据客户信息、商品ID和计算方式获取增值税税率。
     * @param id 商品ID
     * @param customerVO 客户信息对象
     * @param calculateEnum 计算方式枚举
     * @return 增值税税率，豁免客户返回0
     */
    @PFTracing
    protected BigDecimal getBaseTaxValue(Long id,CustomerVO customerVO, CalculateSaleTaxTypeEnum calculateEnum) {
        //豁免客户增值税为0
        if(customerVO.getExcludeDefaultVatFlag()){
            log.info("MkuPriceManageServiceImpl.getCustomerCatTax 客户{}执行价格查询免税逻辑", customerVO.getClientCode());
            return BigDecimal.ZERO;
        }

        BigDecimal valueAddTax = null;
        CategoryTaxVO categoryTaxVO = null;
        if(CalculateSaleTaxTypeEnum.SKU.equals(calculateEnum)){
            // 有类目税率时 按照skuId查询税率
            categoryTaxVO = this.getCategoryTaxPOBySkuId(customerVO,id);
        }else {
            // 按照catId查询税率
            categoryTaxVO = this.getCategoryTaxPOByCatId(customerVO,id);
        }

        if (Objects.nonNull(categoryTaxVO)){
            valueAddTax = categoryTaxVO.getDestinationVatRate();
        } else {
            // 默认配置税率27%
            valueAddTax = this.getValueAddTax(null, customerVO.getCountry());
        }
        return valueAddTax;

    }

    /**
     * 根据 SKU ID 获取对应的分类税率信息。
     * @param customer 客户信息对象
     * @param skuId SKU ID
     * @return 对应的分类税率信息，若不存在则返回 null
     */
    @PFTracing
    protected CategoryTaxVO getCategoryTaxPOBySkuId(CustomerVO customer, Long skuId){
        SkuPO skuPO = skuAtomicService.getSkuPoBySkuId(skuId);
        Map<Long, CategoryTaxPO> categoryTaxPOMap = categoryTaxAtomicService.batchSkuCatTax(customer, Lists.newArrayList(skuPO));
        if (MapUtils.isNotEmpty(categoryTaxPOMap) && Objects.nonNull(categoryTaxPOMap.get(skuId))) {
            return CategoryTaxConvert.INSTANCE.po2Vo(categoryTaxPOMap.get(skuId));
        }
        return null;
    }

    /**
     * 根据客户和类目ID获取类目税信息。
     * @param customer 客户信息。
     * @param catId 类目ID。
     * @return 类目税信息VO对象。
     */
    @PFTracing
    protected CategoryTaxVO getCategoryTaxPOByCatId(CustomerVO customer, Long catId){
        Map<Long, CategoryTaxPO> categoryTaxPOMap = categoryTaxAtomicService.batchSkuCatTaxByCatId(customer, catId);
        if (MapUtils.isNotEmpty(categoryTaxPOMap) && Objects.nonNull(categoryTaxPOMap.get(catId))) {
            return CategoryTaxConvert.INSTANCE.po2Vo(categoryTaxPOMap.get(catId));
        }
        return null;
    }

    /**
     * 计算税后价格
     * @param salePrice 销售价格
     * @param taxSalePrice 税前销售价格
     * @return 税后价格
     */
    @PFTracing
    public BigDecimal calculateTaxPrice(BigDecimal salePrice,BigDecimal taxSalePrice,Integer scale){
        if(salePrice == null || taxSalePrice == null){
            log.error("AbstractSkuSalePriceService.calculateTaxPrice,salePrice:{},taxSalePrice:{}", salePrice,taxSalePrice);
            return null;
        }
        return taxSalePrice.subtract(salePrice).setScale(scale, RoundingMode.HALF_UP);
    }

    /**
     * 计算增值税。
     * @return 增值税率。
     */
    public BigDecimal calculateValueAddTax(BigDecimal salePrice,BigDecimal taxSalePrice,Integer scale){
        if(salePrice == null || taxSalePrice == null){
            log.error("AbstractSkuSalePriceService.calculateValueAddTax,salePrice:{},taxSalePrice:{}", salePrice,taxSalePrice);
            return null;

        }
        // （含税价/未税价-1）
        return taxSalePrice.divide(salePrice,scale,RoundingMode.HALF_UP).subtract(BigDecimal.ONE);
    }

    /**
     * 计算含税销售价格。
     * @param salePrice 销售价格
     * @param taxRate 税率
     * @param scale 精度，保留几位小数
     * @return 含税销售价格，如果输入参数有误则返回 null
     */
    @PFTracing
    public BigDecimal calculateTaxSalePrice(BigDecimal salePrice,BigDecimal taxRate,Integer scale) {
        if(salePrice == null || taxRate == null){
            log.error("AbstractSkuSalePriceService.calculateTaxSalePrice,salePrice:{},taxRate:{}", salePrice,taxRate);
            return null;

        }
        // 未税价*（1+税率）
        return salePrice.multiply(BigDecimal.ONE.add(taxRate)).setScale(scale, RoundingMode.HALF_UP);
    }


    @Override
    public MkuCalculateTaxSalePriceResVO calculateTaxSalePrice(MkuCalculateTaxSalePriceReqVO reqVO) {
        log.info("AbstractSkuSalePriceService.calculateTaxSalePrice reqVO:{}", JSONObject.toJSONString(reqVO));
        try{
            CustomerVO customerVO = reqVO.getCustomerVO();
            BigDecimal valueAddTax = this.getTaxValue(reqVO.getCatId(), customerVO,CalculateSaleTaxTypeEnum.CAT);
            BigDecimal taxSalePrice = this.calculateTaxSalePrice(reqVO.getSourceSalePrice(), valueAddTax, 2);
            BigDecimal taxPrice = this.calculateTaxPrice(reqVO.getSourceSalePrice(), taxSalePrice, 2);
            BigDecimal targetTaxSalePrice = this.calculateExchangePrice(taxSalePrice, reqVO.getSourceCurrencyCode(), reqVO.getTargetCurrencyCode());
            BigDecimal targetSalePrice = this.calculateExchangePrice(reqVO.getSourceSalePrice(), reqVO.getSourceCurrencyCode(), reqVO.getTargetCurrencyCode());
            BigDecimal targetTaxPrice = this.calculateExchangePrice(taxPrice, reqVO.getSourceCurrencyCode(), reqVO.getTargetCurrencyCode());

            MkuCalculateTaxSalePriceResVO mkuCalculateTaxSalePriceResVO = new MkuCalculateTaxSalePriceResVO();
            mkuCalculateTaxSalePriceResVO.setClientCode(reqVO.getClientCode());
            mkuCalculateTaxSalePriceResVO.setSourceSalePrice(reqVO.getSourceSalePrice());
            mkuCalculateTaxSalePriceResVO.setSourceCurrencyCode(reqVO.getSourceCurrencyCode());
            mkuCalculateTaxSalePriceResVO.setTargetCurrencyCode(reqVO.getTargetCurrencyCode());
            mkuCalculateTaxSalePriceResVO.setTargetSalePrice(targetSalePrice);
            mkuCalculateTaxSalePriceResVO.setTargetTaxSalePrice(targetTaxSalePrice);
            mkuCalculateTaxSalePriceResVO.setValueAddedTax(targetTaxPrice);
            mkuCalculateTaxSalePriceResVO.setTargetCountryCode(reqVO.getTargetCountryCode());

            DataResponse<BigDecimal>  exchangeRate = exchangeRateManageService.getExchangeRateByCurrency(reqVO.getSourceCurrencyCode(), reqVO.getTargetCurrencyCode());
            if (exchangeRate.getSuccess()) {
                mkuCalculateTaxSalePriceResVO.setExchangeRate(exchangeRate.getData());
            }
            return mkuCalculateTaxSalePriceResVO;
        } catch (Exception e){
            log.error("AbstractSkuSalePriceService.calculateTaxSalePrice param:{}",JSONObject.toJSONString(reqVO),e);
            Profiler.businessAlarm(UmpKeyConstant.CALCULATE_TAX_SALE_PRICE_WARNING, String.format("【%s】%s 获取销售价异常,clientCode:%s,catId: %s, 异常信息:%s"
                , systemProfile
                , LevelCode.P1.getMessage()
                , reqVO.getClientCode()
                , reqVO.getCatId()
                , e.getMessage()));
            return null;
        }
    }

    /**
     * 批量获取商品税率信息。
     * @param batchReqVO 包含批量请求的商品信息。
     * @return 以商品ID为键，SkuTaxResVO对象为值的Map集合。
     */
    @Override
    public Map<Long, SkuTaxResVO> batchGetTaxValueBySkuIds(SkuTaxBatchReqVO batchReqVO){
        Set<Long> skuIds = batchReqVO.getSkuIds();
        CustomerVO customerVO = batchReqVO.getCustomerVO();
        Map<Long, SkuTaxResVO> skuTaxResVOMap = Maps.newHashMapWithExpectedSize(skuIds.size());
        //豁免客户增值税为0
        if(customerVO.getExcludeDefaultVatFlag()){
            log.info("MkuPriceManageServiceImpl.batchGetTaxValueBySkuIds 客户{}执行价格查询免税逻辑", customerVO.getClientCode());
            defaultSkuTax(skuIds, skuTaxResVOMap);
            return skuTaxResVOMap;
        }
        // 批量查询SKU列表
        List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(skuIds);
        // 批量查询税率信息
        Map<Long, CategoryTaxPO> categoryTaxPOMap = categoryTaxAtomicService.batchSkuCatTax(batchReqVO.getCustomerVO(), skuPOList);
        if (MapUtils.isNotEmpty(categoryTaxPOMap)) {
            defaultSkuTax(skuIds, skuTaxResVOMap);
            return skuTaxResVOMap;
        }

        for (Long skuId : skuIds) {
            CategoryTaxPO categoryTaxVO = categoryTaxPOMap.get(skuId);
            // 默认配置国家税率 泰国7% 匈牙利27%
            BigDecimal valueAddTax = Objects.nonNull(categoryTaxVO) ? categoryTaxVO.getDestinationVatRate() : this.getValueAddTax(null, customerVO.getCountry());
            skuTaxResVOMap.put(skuId,new SkuTaxResVO(skuId,fillVat(valueAddTax)));
        }
        return skuTaxResVOMap;
    }

    private void defaultSkuTax(Set<Long> skuIds, Map<Long, SkuTaxResVO> skuTaxResVOMap) {
        Map<String, BigDecimal> taxMap = fillVat(null);
        skuIds.forEach(skuId-> skuTaxResVOMap.put(skuId,new SkuTaxResVO(skuId,taxMap)));
    }

    private Map<String, BigDecimal> fillVat(BigDecimal vat) {
        Map<String,BigDecimal> taxMap = Maps.newHashMap();
        taxMap.put(TaxTypeEnum.VAT.name(),Objects.isNull(vat) ? BigDecimal.ZERO : vat);
        return taxMap;
    }
}
