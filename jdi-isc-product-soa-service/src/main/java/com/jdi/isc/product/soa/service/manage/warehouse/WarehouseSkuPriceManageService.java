package com.jdi.isc.product.soa.service.manage.warehouse;

import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseSkuPriceBatchVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseSkuPriceResVO;

import java.util.List;

/**
 * @author：xubing82
 * @date：2025/8/7 11:03
 * @description：WarehouseSkuPriceManageService
 */
public interface WarehouseSkuPriceManageService {


    /**
     * 批量查询仓库 SKU 价格信息
     * @param warehouseSkuPriceBatchVO 包含查询条件的批量查询对象
     * @return 符合条件的仓库 SKU 价格信息列表
     */
    List<WarehouseSkuPriceResVO> batchQueryWarehouseSkuPricesByCondition(WarehouseSkuPriceBatchVO warehouseSkuPriceBatchVO);

}
