package com.jdi.isc.product.soa.service.manage.tool;


import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.gms.greatdane.category.domain.Category;
import com.jd.gms.greatdane.category.request.GetCategoryByIdsParam;
import com.jd.gms.greatdane.category.request.GetOrginByIdParam;
import com.jd.gms.greatdane.category.request.GetRelationParam;
import com.jd.gms.greatdane.domain.Language;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.price.res.ProfitRateApiDTO;
import com.jdi.isc.product.soa.api.translate.req.MultiTranslateReqDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.domain.category.po.*;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDraftPO;
import com.jdi.isc.product.soa.domain.enums.CategoryLevelEnum;
import com.jdi.isc.product.soa.domain.enums.StatusEnum;
import com.jdi.isc.product.soa.domain.enums.supplier.TreeLeafEnum;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPriceDraftPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPriceWarningPO;
import com.jdi.isc.product.soa.domain.price.extendPrice.po.CountryExtendPricePO;
import com.jdi.isc.product.soa.domain.price.markupRate.po.MarkupRatePO;
import com.jdi.isc.product.soa.domain.price.po.FulfillmentRatePO;
import com.jdi.isc.product.soa.domain.price.po.ProfitRatePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.supplier.po.BusinessLineRelationPO;
import com.jdi.isc.product.soa.domain.taxRate.po.CategoryTaxPO;
import com.jdi.isc.product.soa.rpc.gms.CategoryRpcService;
import com.jdi.isc.product.soa.service.atomic.category.*;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.FulfillmentRateAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.ProfitRateAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceWarningAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.extendPrice.CountryExtendPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.markupRate.MarkupRateAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.BusinessLineRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.CategoryTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuEsManageService;
import com.jdi.isc.product.soa.service.manage.translate.TextTranslateManageService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description：类目数据刷新服务
 * @Date 2025-05-26
 */
@Slf4j
@Service
public class CategoryTookService {

    @Resource
    private CategoryAtomicService categoryAtomicService;

    @Resource
    private CategoryRpcService categoryRpcService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private MkuAtomicService mkuAtomicService;

    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;

    @Resource
    private CategoryLangAtomicService categoryLangAtomicService;

    @Resource
    private CategoryTaxAtomicService categoryTaxAtomicService;

    @Resource
    private CategoryCountryAtomicService categoryCountryAtomicService;

    @Resource
    private GlobalCountryCategoryAttributeRelationAtomicService globalCountryCategoryAttributeRelationAtomicService;

    @Resource
    private GlobalCountryQualificationCategoryRelationAtomicService globalCountryQualificationCategoryRelationAtomicService;

    @Resource
    private CountryMkuAtomicService countryMkuAtomicService;

    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;

    @Resource
    private BusinessLineRelationAtomicService businessLineRelationAtomicService;

    @Resource
    private MarkupRateAtomicService markupRateAtomicService;

    @Resource
    private FulfillmentRateAtomicService fulfillmentRateAtomicService;

    @Resource
    private ProfitRateAtomicService profitRateAtomicService;

    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;

    @Resource
    private CountryAgreementPriceDraftAtomicService countryAgreementPriceDraftAtomicService;

    @Resource
    private CountryAgreementPriceWarningAtomicService countryAgreementPriceWarningAtomicService;

    @Resource
    private CountryExtendPriceAtomicService countryExtendPriceAtomicService;

    @Resource
    private CustomerManageService customerManageService;

    @Resource
    private CategoryAttributeAtomicService categoryAttributeAtomicService;

    @Resource
    private CustomerSkuPriceDraftAtomicService customerSkuPriceDraftAtomicService;

    @Resource
    private LangManageService langManageService;

    @Resource
    private TextTranslateManageService textTranslateManageService;

    @Resource
    private MkuEsManageService mkuEsManageService;


    private static final String uat_env = "uat_env";

    final ExecutorService pool = new ThreadPoolExecutor(10, 50, 30L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(10000), new ThreadFactoryBuilder().setNamePrefix("product-soa-updateEs-").build());

    /**
     * 更新类目的京东类目ID
     * @return
     */
    public String updateJdCategoryForCategory(String env,Integer level,Set<Long> catIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }

        if (Objects.isNull(level) || CollectionUtils.isEmpty(catIds)) {
            return "类目层级或者类目ID不能为空";
        }

        if (!CategoryLevelEnum.THREE.getCode().equals(level) && !CategoryLevelEnum.FOUR.getCode().equals(level)) {
            return "类目层级只能为三级或者四级";
        }

//        if (CollectionUtils.isEmpty(catIds)){
            // 查询所有四级类目ID
        List<CategoryPO> categoryPOList = categoryAtomicService.queryAllCategoryList();
//        }else {
//            LambdaQueryWrapper<CategoryPO> queryWrapper = Wrappers.lambdaQuery(CategoryPO.class).in(CategoryPO::getId, catIds).eq(CategoryPO::getYn, YnEnum.YES.getCode());
//            categoryPOList = categoryAtomicService.list(queryWrapper);
//        }

        if (CollectionUtils.isEmpty(categoryPOList)) {
            return "类目不存在"+ JSON.toJSONString(catIds);
        }

        // 按照层级分类映射
        // 四级类目映射
        Map<Long, CategoryPO> fourCategoryPoMap = categoryPOList.stream().filter(po-> Objects.equals(CategoryLevelEnum.FOUR.getCode(), po.getCatLevel())).collect(Collectors.toMap(CategoryPO::getId, Function.identity()));
        Map<Long, CategoryPO> thirdCategoryPoMap = categoryPOList.stream().filter(po-> Objects.equals(CategoryLevelEnum.THREE.getCode(), po.getCatLevel())).collect(Collectors.toMap(CategoryPO::getId, Function.identity()));
        Map<Long, CategoryPO> secondCategoryPoMap = categoryPOList.stream().filter(po-> Objects.equals(CategoryLevelEnum.TWO.getCode(), po.getCatLevel())).collect(Collectors.toMap(CategoryPO::getId, Function.identity()));
        Map<Long, CategoryPO> firstCategoryPoMap = categoryPOList.stream().filter(po-> Objects.equals(CategoryLevelEnum.ONE.getCode(), po.getCatLevel())).collect(Collectors.toMap(CategoryPO::getId, Function.identity()));

        if (CategoryLevelEnum.FOUR.getCode().equals(level)) {
            // 处理四级类目ID
            int total = fourCategoryPoMap.size();
            log.info("CategoryTookService.updateJdCategoryForCategory 更新京东类目ID 四级类目总数：{}",total);
            int count = 1;
            for (Map.Entry<Long, CategoryPO> four : fourCategoryPoMap.entrySet()) {
                Long fourCatId = four.getKey();
                if (!catIds.contains(fourCatId)) {
                    continue;
                }
                CategoryPO fourCategoryValue = four.getValue();
                Long jdFourCatId = fourCategoryValue.getJdCatId();
                Long parentCatId = fourCategoryValue.getParentCatId();
                log.info("CategoryTookService.updateJdCategoryForCategory 更新京东类目ID===================开始 总数:{},当前第:{}个，四级类目ID：{}，京东四级类目ID:{}",total,count,fourCatId,jdFourCatId);
                try {
                    List<CategoryPO> updateCategoryPoList = Lists.newArrayList();

                    // 京东四级类目ID不为空，处理京东类目ID
                    if (Objects.nonNull(jdFourCatId)) {
                        log.info("CategoryTookService.updateJdCategoryForCategory 更新京东类目ID 四级类目ID：{},京东四级类目ID:{}", fourCatId, jdFourCatId);
                        GetOrginByIdParam param = new GetOrginByIdParam();
                        param.setCategoryId(jdFourCatId.intValue());
                        param.setLanguage(Language.zh_CN);
                        List<Category> categoryList = categoryRpcService.getOriginById(param);
                        log.info("CategoryTookService.updateJdCategoryForCategory 查询四级类目ID:{} 京东末级类目:{} 的所有父级类目结果:{}", fourCatId,jdFourCatId, JSON.toJSONString(categoryList));
                        Map<Integer, Category> levelCategoryMap = categoryList.stream().filter(Objects::nonNull).peek(c -> c.setCategoryClass(c.getCategoryClass() + 1)).collect(Collectors.toMap(Category::getCategoryClass, Function.identity()));

                        CategoryPO fourCategoryPO = convertCategoryPo(fourCategoryValue);
                        Category fourCategory = levelCategoryMap.get(CategoryLevelEnum.FOUR.getCode());
                        fourCategoryPO.setJdParentCatId(fourCategory.getFatherCategoryId().longValue());
                        updateCategoryPoList.add(fourCategoryPO);

                        // 三级类目信息
                        if (MapUtils.isNotEmpty(thirdCategoryPoMap) && thirdCategoryPoMap.containsKey(parentCatId) && Objects.nonNull(thirdCategoryPoMap.get(parentCatId))) {
                            CategoryPO thirdCategoryPO = convertCategoryPo(thirdCategoryPoMap.get(parentCatId));
                            Category thirdCategory = levelCategoryMap.get(CategoryLevelEnum.THREE.getCode());
                            thirdCategoryPO.setJdCatId(thirdCategory.getCategoryId().longValue());
                            thirdCategoryPO.setJdParentCatId(thirdCategory.getFatherCategoryId().longValue());
                            updateCategoryPoList.add(thirdCategoryPO);
                            thirdCategoryPoMap.remove(parentCatId);

                            // 二级类目ID
                            Long secondCategoryId = thirdCategoryPO.getParentCatId();
                            // 补充二级类目京东类目
                            if (MapUtils.isNotEmpty(secondCategoryPoMap) && secondCategoryPoMap.containsKey(secondCategoryId) && Objects.nonNull(secondCategoryPoMap.get(secondCategoryId))) {
                                CategoryPO secondCategoryPO = convertCategoryPo(secondCategoryPoMap.get(secondCategoryId));
                                Category secondCategory = levelCategoryMap.get(CategoryLevelEnum.TWO.getCode());
                                secondCategoryPO.setJdCatId(secondCategory.getCategoryId().longValue());
                                secondCategoryPO.setJdParentCatId(secondCategory.getFatherCategoryId().longValue());
                                updateCategoryPoList.add(secondCategoryPO);
                                secondCategoryPoMap.remove(secondCategoryId);
                                // 一级类目ID
                                Long firstCategoryId = secondCategoryPO.getParentCatId();
                                // 补充一级类目的京东类目
                                if (MapUtils.isNotEmpty(firstCategoryPoMap) && firstCategoryPoMap.containsKey(firstCategoryId) && Objects.nonNull(firstCategoryPoMap.get(firstCategoryId))) {
                                    CategoryPO firstCategoryPO = convertCategoryPo(firstCategoryPoMap.get(firstCategoryId));
                                    Category firstCategory = levelCategoryMap.get(CategoryLevelEnum.ONE.getCode());
                                    firstCategoryPO.setJdCatId(firstCategory.getCategoryId().longValue());
                                    firstCategoryPO.setJdParentCatId(firstCategory.getFatherCategoryId().longValue());
                                    updateCategoryPoList.add(firstCategoryPO);
                                    firstCategoryPoMap.remove(firstCategoryId);
                                }
                            }
                        }
                    }

                    // 批量更新类目信息
                    if (CollectionUtils.isNotEmpty(updateCategoryPoList)) {
                        categoryAtomicService.updateBatchById(updateCategoryPoList);
                        log.info("CategoryTookService.updateJdCategoryForCategory  更新京东类目ID 四级类目ID：{} 更新类目信息：{}", fourCatId, JSON.toJSONString(updateCategoryPoList));
                    }

                    log.info("CategoryTookService.updateJdCategoryForCategory 更新京东类目ID===================结束 总数:{},当前第:{}个，四级类目ID：{}，京东四级类目ID:{}",total,count,fourCatId,jdFourCatId);

                } catch (Exception e) {
                    log.error("CategoryTookService.updateJdCategoryForCategory 更新京东类目ID 四级类目ID：{} 更新类目信息异常：{}", fourCatId, e.getMessage());
                }finally {
                    count++;
                }
            }
        }else if (CategoryLevelEnum.THREE.getCode().equals(level)) {
            // 处理四级类目ID
            int thirdTotal = thirdCategoryPoMap.size();
            log.info("CategoryTookService.updateJdCategoryForCategory 更新京东类目ID 三级类目总数：{}",thirdTotal);
            int thirdCount = 1;
            for (Map.Entry<Long, CategoryPO> third : thirdCategoryPoMap.entrySet()) {
                Long thirdCatId = third.getKey();
                if (!catIds.contains(thirdCatId)) {
                    continue;
                }
                CategoryPO thirdCategoryValue = third.getValue();
                Long jdThirdCatId = thirdCategoryValue.getJdCatId();
                Long parentCatId = thirdCategoryValue.getParentCatId();
                log.info("CategoryTookService.updateJdCategoryForCategory 更新京东类目ID===================开始 总数:{},当前第:{}个，三级类目ID：{}，京东三级类目ID:{}",thirdTotal,thirdCount,thirdCatId,jdThirdCatId);
                try {
                    List<CategoryPO> updateCategoryPoList = Lists.newArrayList();

                    // 京东三级类目ID不为空，处理京东类目ID
                    if (Objects.nonNull(jdThirdCatId)) {
                        log.info("CategoryTookService.updateJdCategoryForCategory 更新京东类目ID 三级类目ID：{},京东三级类目ID:{}", thirdCatId, jdThirdCatId);
                        GetOrginByIdParam param = new GetOrginByIdParam();
                        param.setCategoryId(jdThirdCatId.intValue());
                        param.setLanguage(Language.zh_CN);
                        List<Category> categoryList = categoryRpcService.getOriginById(param);
                        log.info("CategoryTookService.updateJdCategoryForCategory 查询三级类目ID:{} 京东末级类目:{} 的所有父级类目结果:{}", thirdCatId,jdThirdCatId, JSON.toJSONString(categoryList));
                        Map<Integer, Category> levelCategoryMap = categoryList.stream().filter(Objects::nonNull).peek(c -> c.setCategoryClass(c.getCategoryClass() + 1)).collect(Collectors.toMap(Category::getCategoryClass, Function.identity()));

                        CategoryPO thirdCategoryPO = convertCategoryPo(thirdCategoryValue);
                        Category thirdCategory = levelCategoryMap.get(CategoryLevelEnum.THREE.getCode());
                        thirdCategoryPO.setJdParentCatId(thirdCategory.getFatherCategoryId().longValue());
                        updateCategoryPoList.add(thirdCategoryPO);

                        // 二级类目ID
                        Long secondCategoryId = thirdCategoryPO.getParentCatId();
                        // 补充二级类目京东类目
                        if (MapUtils.isNotEmpty(secondCategoryPoMap) && secondCategoryPoMap.containsKey(secondCategoryId) && Objects.nonNull(secondCategoryPoMap.get(secondCategoryId))) {
                            CategoryPO secondCategoryPO = convertCategoryPo(secondCategoryPoMap.get(secondCategoryId));
                            Category secondCategory = levelCategoryMap.get(CategoryLevelEnum.TWO.getCode());
                            secondCategoryPO.setJdCatId(secondCategory.getCategoryId().longValue());
                            secondCategoryPO.setJdParentCatId(secondCategory.getFatherCategoryId().longValue());
                            updateCategoryPoList.add(secondCategoryPO);
                            secondCategoryPoMap.remove(secondCategoryId);
                            // 一级类目ID
                            Long firstCategoryId = secondCategoryPO.getParentCatId();
                            // 补充一级类目的京东类目
                            if (MapUtils.isNotEmpty(firstCategoryPoMap) && firstCategoryPoMap.containsKey(firstCategoryId) && Objects.nonNull(firstCategoryPoMap.get(firstCategoryId))) {
                                CategoryPO firstCategoryPO = convertCategoryPo(firstCategoryPoMap.get(firstCategoryId));
                                Category firstCategory = levelCategoryMap.get(CategoryLevelEnum.ONE.getCode());
                                firstCategoryPO.setJdCatId(firstCategory.getCategoryId().longValue());
                                firstCategoryPO.setJdParentCatId(firstCategory.getFatherCategoryId().longValue());
                                updateCategoryPoList.add(firstCategoryPO);
                                firstCategoryPoMap.remove(firstCategoryId);
                            }
                        }
                    }

                    // 批量更新类目信息
                    if (CollectionUtils.isNotEmpty(updateCategoryPoList)) {
                        categoryAtomicService.updateBatchById(updateCategoryPoList);
                        log.info("CategoryTookService.updateJdCategoryForCategory  更新京东类目ID 三级类目ID：{} 更新类目信息：{}", thirdCatId, JSON.toJSONString(updateCategoryPoList));
                    }

                    log.info("CategoryTookService.updateJdCategoryForCategory 更新京东类目ID===================结束 总数:{},当前第:{}个，三级类目ID：{}，京东三级类目ID:{}",thirdTotal,thirdCount,thirdCatId,jdThirdCatId);

                } catch (Exception e) {
                    log.error("CategoryTookService.updateJdCategoryForCategory 更新京东类目ID 三级类目ID：{} 更新类目信息异常：{}", thirdCatId, e.getMessage());
                }finally {
                    thirdCount++;
                }
            }
        }
        return "success";
    }

    private CategoryPO convertCategoryPo(CategoryPO originCategoryPO) {
        CategoryPO categoryPO = new CategoryPO();
        categoryPO.setId(originCategoryPO.getId());
        categoryPO.setUpdater(originCategoryPO.getUpdater());
        categoryPO.setUpdateTime(new Date());
        categoryPO.setParentCatId(originCategoryPO.getParentCatId());
        return categoryPO;
    }

    /**
     * 更新类目多语言京东类目信息。
     *
     * @return 更新结果，成功返回"success"。
     */
    public String updateCatLangJdCategory(String env,Set<Long> catIds){
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }

        LambdaQueryWrapper<CategoryPO> categoryQueryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId, CategoryPO::getJdCatId).in(CollectionUtils.isNotEmpty(catIds), CategoryPO::getId, catIds);
        List<CategoryPO> categoryPoList = categoryAtomicService.list(categoryQueryWrapper);
        int total = categoryPoList.size();
        List<List<CategoryPO>> partitionList = Lists.partition(categoryPoList, 10);

        // 国际类目和京东类目的关系
        Map<Long, Long> catAndJdCatMap = categoryPoList.stream().filter(Objects::nonNull).filter(po-> Objects.nonNull(po.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));
        int count = 0;
        for (List<CategoryPO> subList : partitionList) {
            List<CategoryLangPO> updateCategoryLangPOList = Lists.newArrayList();
            try {
                Set<Long> Ids = subList.stream().filter(Objects::nonNull).map(CategoryPO::getId).collect(Collectors.toSet());
                LambdaQueryWrapper<CategoryLangPO> categoryLangPOLambdaQueryWrapper = Wrappers.lambdaQuery(CategoryLangPO.class).select(CategoryLangPO::getId, CategoryLangPO::getCatId,CategoryLangPO::getUpdater)
                        .in(CategoryLangPO::getCatId, Ids);

                List<CategoryLangPO> categoryLangPOList = categoryLangAtomicService.list(categoryLangPOLambdaQueryWrapper);

                if (CollectionUtils.isEmpty(categoryLangPOList)) {
                    continue;
                }


                Map<Long, List<CategoryLangPO>> catLangMap = categoryLangPOList.stream().collect(Collectors.groupingBy(CategoryLangPO::getCatId));

                for (Map.Entry<Long, List<CategoryLangPO>> entry : catLangMap.entrySet()) {
                    Long catId = entry.getKey();
                    Long jdCatId = catAndJdCatMap.get(catId);
                    log.info("updateCatLangJdCategory 更新类目多语言京东类目信息开始 catId={} jdCatId={} count={},total={}",catId,jdCatId,count,total);
                    if (Objects.isNull(jdCatId)) {
                        count++;
                        continue;
                    }
                    List<CategoryLangPO> langPOList = entry.getValue();
                    for (CategoryLangPO po : langPOList) {
                        CategoryLangPO updateCategoryLangPO = new CategoryLangPO();
                        updateCategoryLangPO.setId(po.getId());
                        updateCategoryLangPO.setJdCatId(jdCatId);
                        updateCategoryLangPO.setUpdater(po.getUpdater());
                        updateCategoryLangPO.setUpdateTime(new Date());
                        updateCategoryLangPOList.add(updateCategoryLangPO);
                    }
                    count++;
                }

                if (CollectionUtils.isNotEmpty(updateCategoryLangPOList)) {
                    categoryLangAtomicService.updateBatchById(updateCategoryLangPOList);
                }
            } catch (Exception e) {
                log.error("updateCatLangJdCategory 更新类目多语言京东类目异常 subList={}  count={},total={}",JSON.toJSONString(subList),count,total,e);
            }finally {
                log.info("updateCatLangJdCategory 更新类目多语言京东类目信息开始 subList={} updateCategoryLangPOList={}  count={},total={}",JSON.toJSONString(subList),JSON.toJSONString(updateCategoryLangPOList),count,total);
            }
        }

        return "success";
    }


    /**
     * 更新商品的京东类目信息。
     *
     * @return 更新结果，"success"表示更新成功，否则返回错误信息。
     */
    public String updateSkuJdCategory(String env,Set<Long> skuIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<SkuPO> queryWrapper = Wrappers.lambdaQuery(SkuPO.class).select(SkuPO::getSkuId, SkuPO::getSpuId, SkuPO::getCatId, SkuPO::getUpdater)
                .in(CollectionUtils.isNotEmpty(skuIds),SkuPO::getSkuId,skuIds).orderByAsc(SkuPO::getSkuId);
        List<SkuPO> list = skuAtomicService.list(queryWrapper);
        int  total = list.size();


        Set<Long> catIds = list.stream().map(SkuPO::getCatId).collect(Collectors.toSet());
        List<CategoryPO> categoryPoList = getCategoryPOList(catIds);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        Map<Long, RelationVO> catIdRealtionVoMap = this.getCatAndThirdCatIdRealtionVoMap(categoryPoList);

        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(categoryPO -> Objects.nonNull(categoryPO.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));

        List<List<SkuPO>> partitionList = Lists.partition(list, 100);
        int count = 1;
        for (List<SkuPO> subList : partitionList) {
            List<SkuPO> updateSkuList = Lists.newArrayListWithExpectedSize(subList.size());
            List<SpuPO> updateSpuList = Lists.newArrayListWithExpectedSize(subList.size());
            Set<Long> spuIdSet = new HashSet<>();
            try {
                for (SkuPO skuPO : subList) {
                    Long skuId = skuPO.getSkuId();
                    Long catId = skuPO.getCatId();
                    Long spuId = skuPO.getSpuId();
                    String updater = skuPO.getUpdater();
                    log.info("updateSkuJdCategory 更新商品京东类目信息开始 skuId={},catId={} count={},total={}",skuId,catId,count,total);

                    SkuPO updateSkuPo = new SkuPO();
                    updateSkuPo.setSkuId(skuId);
                    updateSkuPo.setUpdater(updater);
                    updateSkuPo.setUpdateTime(new Date());

                    Long lastCatId = getLastJdCatId(cateAndJdCategoryMap, catId, catIdRealtionVoMap);
                    updateSkuPo.setJdCatId(lastCatId);
                    updateSkuList.add(updateSkuPo);
                    // SPU
                    if (!spuIdSet.contains(spuId)) {
                        SpuPO spuPO = new SpuPO();
                        spuPO.setSpuId(spuId);
                        spuPO.setUpdater(updater);
                        spuPO.setUpdateTime(new Date());
                        spuPO.setJdCatId(lastCatId);
                        updateSpuList.add(spuPO);
                        spuIdSet.add(spuId);
                    }
                }
                if (CollectionUtils.isNotEmpty(updateSkuList)) {
                    skuAtomicService.updateBatchById(updateSkuList);
                }
                if (CollectionUtils.isNotEmpty(updateSpuList)) {
                    spuAtomicService.updateBatchById(updateSpuList);
                }

            } catch (Exception e) {
                log.info("updateSkuJdCategory 更新商品类目信息异常：skuId={}",JSON.toJSONString(subList),e );
            }finally {
                count++;
                log.info("updateSkuJdCategory 更新商品京东类目信息结束 skuId={},spu={} count={},total={}",JSON.toJSONString(updateSkuList),JSON.toJSONString(updateSpuList),count,total);
            }
        }
        return "success";
    }


    /**
     * 更新MKU的京东类目ID
     * @return
     */
    public String updateMkuJdCategory(String  env,Set<Long> mkuIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<MkuPO> queryWrapper = Wrappers.lambdaQuery(MkuPO.class).select(MkuPO::getMkuId, MkuPO::getCatId, MkuPO::getUpdater)
                .in(CollectionUtils.isNotEmpty(mkuIds),MkuPO::getMkuId,mkuIds).orderByAsc(MkuPO::getMkuId);
        List<MkuPO> list = mkuAtomicService.list(queryWrapper);
        int  total = list.size();


        Set<Long> catIds = list.stream().map(MkuPO::getCatId).collect(Collectors.toSet());
        List<CategoryPO> categoryPoList = getCategoryPOList(catIds);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        Map<Long, RelationVO> catIdRealtionVoMap = this.getCatAndThirdCatIdRealtionVoMap(categoryPoList);

        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(categoryPO -> Objects.nonNull(categoryPO.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));

        List<List<MkuPO>> partitionList = Lists.partition(list, 100);
        int count = 1;
        for (List<MkuPO> subList : partitionList) {
            List<MkuPO> updateMkuList = Lists.newArrayListWithExpectedSize(subList.size());
            try {
                 for (MkuPO mkuPO : subList) {
                    Long mkuId = mkuPO.getMkuId();
                    Long catId = mkuPO.getCatId();
                    String updater = mkuPO.getUpdater();
                    log.info("updateMkuJdCategory 更新商品京东类目信息开始 mkuId={},catId={} count={},total={}",mkuId,catId,count,total);

                     Long jdCatId = getLastJdCatId(cateAndJdCategoryMap, catId, catIdRealtionVoMap);

                     if (Objects.isNull(jdCatId)) {
                         log.info("updateMkuJdCategory 更新商品京东类目信息 国际类目ID={},没有京东类目ID,mkuId={}",catId,mkuId);
                         count++;
                         continue;
                     }
                     MkuPO updateMkuPo = new MkuPO();
                     updateMkuPo.setMkuId(mkuId);
                     updateMkuPo.setUpdater(updater);
                     updateMkuPo.setUpdateTime(new Date());
                     updateMkuPo.setJdCatId(jdCatId);
                    updateMkuList.add(updateMkuPo);
                    count++;
                }
                if (CollectionUtils.isNotEmpty(updateMkuList)) {
                    mkuAtomicService.updateBatchById(updateMkuList);
                }
            } catch (Exception e) {
                log.info("updateMkuJdCategory 更新商品MKU类目信息异常：mkuId={}，类目Id={}",JSON.toJSONString(subList),e );
            }finally {
                log.info("updateMkuJdCategory 更新商品MKU京东类目信息结束 mkuId={}, count={},total={}",JSON.toJSONString(updateMkuList),count,total);
            }
        }

        return "success";
    }

    private List<CategoryPO> getCategoryPOList(Set<Long> catIds) {
        LambdaQueryWrapper<CategoryPO> categoryQueryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId, CategoryPO::getJdCatId,CategoryPO::getJdParentCatId).in(CategoryPO::getId, catIds);
        return categoryAtomicService.list(categoryQueryWrapper);
    }

    private static Long getLastJdCatId(Map<Long, Long> cateAndJdCategoryMap, Long catId, Map<Long, RelationVO> catIdRealtionVoMap) {
        Long jdCatId = cateAndJdCategoryMap.get(catId);

        RelationVO relationVO = catIdRealtionVoMap.get(catId);
        if (Objects.nonNull(relationVO) && relationVO.isLeafNode) {
            jdCatId = relationVO.getThreeCatId();
        }
        return jdCatId;
    }


    /**
     * 更新SPU草稿的京东类目信息。
     *
     * @return 更新结果，"success"表示更新成功，否则返回错误信息。
     */
    public String updateSpuDraftJdCategory(String env,Set<Long> spuIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<SpuDraftPO> queryWrapper = Wrappers.lambdaQuery(SpuDraftPO.class).select(SpuDraftPO::getSpuId, SpuDraftPO::getCatId, SpuDraftPO::getUpdater)
                .in(CollectionUtils.isNotEmpty(spuIds),SpuDraftPO::getSpuId,spuIds).orderByAsc(SpuDraftPO::getSpuId);
        List<SpuDraftPO> list = spuDraftAtomicService.list(queryWrapper);
        int  total = list.size();


        Set<Long> catIds = list.stream().map(SpuDraftPO::getCatId).collect(Collectors.toSet());
        LambdaQueryWrapper<CategoryPO> categoryQueryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId, CategoryPO::getJdCatId,CategoryPO::getJdParentCatId,CategoryPO::getParentCatId).in(CategoryPO::getId, catIds);
        List<CategoryPO> categoryPoList = categoryAtomicService.list(categoryQueryWrapper);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        Map<Long, RelationVO> catIdRetionMap = getCatAndThirdCatIdRealtionVoMap(categoryPoList);

        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(categoryPO -> Objects.nonNull(categoryPO.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));

        List<List<SpuDraftPO>> partitionList = Lists.partition(list, 100);
        int count = 1;
        for (List<SpuDraftPO> subList : partitionList) {
            List<SpuDraftPO> updateSpuList = Lists.newArrayListWithExpectedSize(subList.size());
            try {
                // spuId集合
                Set<Long> dbSpuIds = subList.stream().filter(Objects::nonNull).map(SpuDraftPO::getSpuId).collect(Collectors.toSet());
                LambdaQueryWrapper<SpuDraftPO> querySpuWrapper = Wrappers.lambdaQuery(SpuDraftPO.class)
                        .select(SpuDraftPO::getId, SpuDraftPO::getCatId, SpuDraftPO::getUpdater).in(SpuDraftPO::getSpuId, dbSpuIds);

                List<SpuDraftPO> dbSubList = spuDraftAtomicService.list(querySpuWrapper);

                for (SpuDraftPO spuDraftPO : dbSubList) {
                    Long spuId = spuDraftPO.getSpuId();
                    Long catId = spuDraftPO.getCatId();
                    String updater = spuDraftPO.getUpdater();
                    log.info("updateSpuDraftJdCategory 更新商品SPU草稿京东类目信息开始 spuId={},catId={} count={},total={}",spuId,catId,count,total);

                    Long jdCatId = cateAndJdCategoryMap.get(catId);
                    if (Objects.isNull(jdCatId)) {
                        log.info("updateSpuDraftJdCategory 更新商品SPU草稿京东类目信息 国际类目ID={},没有京东类目ID,spuId={}",catId,spuId);
                        count++;
                        continue;
                    }

                    RelationVO relationVO = catIdRetionMap.get(catId);
                    // 三级是末级节点
                    if (Objects.nonNull(relationVO) && relationVO.isLeafNode){
                        jdCatId = relationVO.getThreeCatId();
                    }

                    SpuDraftPO updateSpuDraftPo = new SpuDraftPO();
                    updateSpuDraftPo.setId(spuDraftPO.getId());
                    updateSpuDraftPo.setUpdater(updater);
                    updateSpuDraftPo.setUpdateTime(new Date());
                    updateSpuDraftPo.setJdCatId(jdCatId);
                    updateSpuList.add(updateSpuDraftPo);
                    count++;
                }
                if (CollectionUtils.isNotEmpty(updateSpuList)) {
                    spuDraftAtomicService.updateBatchById(updateSpuList);
                }
            } catch (Exception e) {
                log.info("updateSpuDraftJdCategory 更新商品SPU草稿类目信息异常：spuId={}，类目Id={}",JSON.toJSONString(subList),e );
            }finally {
                log.info("updateSpuDraftJdCategory 更新商品SPU草稿京东类目信息结束 spuId={}, count={},total={}",JSON.toJSONString(updateSpuList),count,total);
            }
        }

        return "success";
    }

    private  Map<Long, RelationVO> getCatAndThirdCatIdRealtionVoMap(List<CategoryPO> categoryPoList) {
        Map<Long, RelationVO> catIdRetionMap = Maps.newHashMap();
        // 类目关系 国际类目ID 、京东四级类目ID 京东三级类目 京东三级类目ID是否为末级
        List<RelationVO> relationVOList = Lists.newLinkedList();
        for (CategoryPO po : categoryPoList) {
            relationVOList.add(new RelationVO(po));
        }

        Set<Long> thirdJdCatIds = categoryPoList.stream().filter(Objects::nonNull).map(CategoryPO::getJdParentCatId).collect(Collectors.toSet());
        // 查询三级类目ID为末级的类目ID
        LambdaQueryWrapper<CategoryPO> parentCategoryQueryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId, CategoryPO::getJdCatId)
                .eq(CategoryPO::getCatLevel,CategoryLevelEnum.THREE.getCode())
                .eq(CategoryPO::getLeafNodeFlag,TreeLeafEnum.Y.getCode())
                .in(CategoryPO::getJdCatId, thirdJdCatIds);
        List<CategoryPO> categoryPOList = categoryAtomicService.list(parentCategoryQueryWrapper);

        if (CollectionUtils.isEmpty(categoryPOList)) {
            return catIdRetionMap;
        }

        // 末级三级类目ID集合
        Set<Long> lastThirdCatIds = categoryPOList.stream().filter(Objects::nonNull).map(CategoryPO::getJdCatId).collect(Collectors.toSet());

        catIdRetionMap = relationVOList.stream().filter(Objects::nonNull)
                .peek(vo -> vo.setLeafNode(lastThirdCatIds.contains(vo.getThreeCatId()))).collect(Collectors.toMap(RelationVO::getCatId, Function.identity()));
        return catIdRetionMap;
    }


    /**
     * 更新少量数据表四级类目的京东类目ID。
     *
     * @return 更新结果，"success"表示更新成功，否则返回错误信息。
     */
    public String updateJdCategoryForLowData(String env,Set<Long> categoryIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        // jdi_isc_category_tax
        LambdaQueryWrapper<CategoryTaxPO> categoryTaxWrapper = Wrappers.lambdaQuery(CategoryTaxPO.class).select(CategoryTaxPO::getCategoryId, CategoryTaxPO::getId, CategoryTaxPO::getUpdater)
                .in(CollectionUtils.isNotEmpty(categoryIds),CategoryTaxPO::getCategoryId,categoryIds);
        List<CategoryTaxPO> categoryTaxPoList = categoryTaxAtomicService.list(categoryTaxWrapper);
        // jdi_isc_category_country
        LambdaQueryWrapper<CategoryCountryPO> categoryCountryWrapper = Wrappers.lambdaQuery(CategoryCountryPO.class).select(CategoryCountryPO::getLastCatId, CategoryCountryPO::getId, CategoryCountryPO::getUpdater)
                .in(CollectionUtils.isNotEmpty(categoryIds),CategoryCountryPO::getLastCatId,categoryIds);
        List<CategoryCountryPO> categoryCountryPoList = categoryCountryAtomicService.list(categoryCountryWrapper);
        //jdi_isc_global_country_category_attribute_relation
        LambdaQueryWrapper<GlobalCountryCategoryAttributeRelationPO> globalCountryCategoryWrapper = Wrappers.lambdaQuery(GlobalCountryCategoryAttributeRelationPO.class).select(GlobalCountryCategoryAttributeRelationPO::getCategoryId, GlobalCountryCategoryAttributeRelationPO::getId, GlobalCountryCategoryAttributeRelationPO::getUpdater)
                .in(CollectionUtils.isNotEmpty(categoryIds),GlobalCountryCategoryAttributeRelationPO::getCategoryId,categoryIds);
        List<GlobalCountryCategoryAttributeRelationPO> globalCountryCategoryPoList = globalCountryCategoryAttributeRelationAtomicService.list(globalCountryCategoryWrapper);
        //jdi_isc_global_country_qualification_category_relation
        LambdaQueryWrapper<GlobalCountryQualificationCategoryRelationPO> globalCountryQualificationWrapper = Wrappers.lambdaQuery(GlobalCountryQualificationCategoryRelationPO.class).select(GlobalCountryQualificationCategoryRelationPO::getCategoryId, GlobalCountryQualificationCategoryRelationPO::getId, GlobalCountryQualificationCategoryRelationPO::getUpdater)
                .in(CollectionUtils.isNotEmpty(categoryIds),GlobalCountryQualificationCategoryRelationPO::getCategoryId,categoryIds);
        List<GlobalCountryQualificationCategoryRelationPO> globalCountryQualificationPoList = globalCountryQualificationCategoryRelationAtomicService.list(globalCountryQualificationWrapper);
        LambdaQueryWrapper<FulfillmentRatePO> fulfillmentRatePOWrapper = Wrappers.lambdaQuery(FulfillmentRatePO.class).select(FulfillmentRatePO::getFirstCatId,FulfillmentRatePO::getSecondCatId,FulfillmentRatePO::getThirdCatId,FulfillmentRatePO::getLastCatId, FulfillmentRatePO::getId, FulfillmentRatePO::getUpdater,FulfillmentRatePO::getIsWarehouseProduct).in(CollectionUtils.isNotEmpty(categoryIds),FulfillmentRatePO::getFirstCatId,categoryIds);
        List<FulfillmentRatePO> fulfillmentRatePOList = fulfillmentRateAtomicService.list(fulfillmentRatePOWrapper);

        Set<Long> catIds = categoryTaxPoList.stream().map(CategoryTaxPO::getCategoryId).collect(Collectors.toSet());
        catIds.addAll(categoryCountryPoList.stream().map(CategoryCountryPO::getLastCatId).collect(Collectors.toSet()));
        catIds.addAll(globalCountryCategoryPoList.stream().map(GlobalCountryCategoryAttributeRelationPO::getCategoryId).collect(Collectors.toSet()));
        catIds.addAll(globalCountryQualificationPoList.stream().map(GlobalCountryQualificationCategoryRelationPO::getCategoryId).collect(Collectors.toSet()));
/*
        catIds.addAll(fulfillmentRatePOList.stream().filter(Objects::nonNull)
                .flatMap(ratePO -> {
                    Set<Long> ids = Sets.newHashSet();
                    if (Objects.nonNull(ratePO.getFirstCatId())) {
                        ids.add(ratePO.getFirstCatId());
                    }
                    if (Objects.nonNull(ratePO.getSecondCatId())) {
                        ids.add(ratePO.getSecondCatId());
                    }
                    if (Objects.nonNull(ratePO.getThirdCatId())) {
                        ids.add(ratePO.getThirdCatId());
                    }
                    if (Objects.nonNull(ratePO.getLastCatId())) {
                        ids.add(ratePO.getLastCatId());
                    }
                    return ids.stream(); // 修复类型不匹配问题，将 Set<Long> 转换为 Stream<Long>
                }).collect(Collectors.toSet()));
*/

        List<CategoryPO> categoryPoList = getCategoryPOList(catIds);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        Map<Long, RelationVO> catIdRealtionVoMap = this.getCatAndThirdCatIdRealtionVoMap(categoryPoList);

        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(po-> Objects.nonNull(po.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));
        // 更新类目税率京东类目ID
        updateCategoryTaxJDCategory(categoryTaxPoList, cateAndJdCategoryMap, catIdRealtionVoMap);
        // 更新类目国家关系京东类目ID
        updateCategoryCountryPOJDCategory(categoryCountryPoList, cateAndJdCategoryMap, catIdRealtionVoMap);
        // 更新国家类目属性关系京东类目ID
        updateGlobalCountryCategoryAttributeRelationPOJDCategory(globalCountryCategoryPoList, cateAndJdCategoryMap,catIdRealtionVoMap);
        // 更新国家类目资质关系京东类目ID
        updateGlobalCountryQualificationCategoryRelationPOJDCategory(globalCountryQualificationPoList, cateAndJdCategoryMap,catIdRealtionVoMap);
        // 更新履约费率
        //updateFulfillmentRatePOJDCategory(fulfillmentRatePOList, cateAndJdCategoryMap);
        return "success";
    }

    private void updateCategoryTaxJDCategory(List<CategoryTaxPO> categoryTaxPoList, Map<Long, Long> cateAndJdCategoryMap,Map<Long, RelationVO> catIdRealtionVoMap) {
        List<List<CategoryTaxPO>> partitionList = Lists.partition(categoryTaxPoList, 100);
        int total = categoryTaxPoList.size();
        int count = 1;
        for (List<CategoryTaxPO> subList : partitionList) {
            try {
                List<CategoryTaxPO> updateCategoryTaxPoList = Lists.newArrayList();
                for (CategoryTaxPO taxPO : subList) {
                    Long jdCatId = getLastJdCatId(cateAndJdCategoryMap, taxPO.getCategoryId(), catIdRealtionVoMap);
                    if (Objects.isNull(jdCatId)) {
                        log.error("updateCategoryTaxJDCategory 更新类目税率表， count={},total={}类目税率信息:{}",count,total,JSON.toJSONString(taxPO));
                        count++;
                        continue;
                    }
                    taxPO.setJdCatId(jdCatId);
                    taxPO.setUpdateTime(System.currentTimeMillis());
                    updateCategoryTaxPoList.add(taxPO);
                    count++;
                }

                if(CollectionUtils.isNotEmpty(updateCategoryTaxPoList)){
                    categoryTaxAtomicService.updateBatchById(updateCategoryTaxPoList);
                }
            } catch (Exception e) {
                log.error("updateCategoryTaxJDCategory 更新类目税率表异常：{}",JSON.toJSONString(subList), e);
            }
        }
    }

    private void updateCategoryCountryPOJDCategory(List<CategoryCountryPO> categoryCountryPoList, Map<Long, Long> cateAndJdCategoryMap,Map<Long, RelationVO> catIdRealtionVoMap) {
        int total = categoryCountryPoList.size();
        int count = 1;
        List<CategoryCountryPO> updateCategoryCountryPoList = Lists.newArrayList();
        for (CategoryCountryPO taxPO : categoryCountryPoList) {
            Long jdCatId = getLastJdCatId(cateAndJdCategoryMap, taxPO.getLastCatId(), catIdRealtionVoMap);
            log.info("updateCategoryCountryPOJDCategory 更新类目国家关系表开始，id={},catId={},jdCatId={}, count={},total={}类目税率信息:{}",taxPO.getId(),taxPO.getLastCatId(),jdCatId ,count,total,JSON.toJSONString(taxPO));
            if (Objects.isNull(jdCatId)) {
                log.error("updateCategoryCountryPOJDCategory 更新类目国家关系表 国际类目和京东类目关系为空， count={},total={}类目税率信息:{}",count,total,JSON.toJSONString(taxPO));
                count++;
                continue;
            }
            try {
                taxPO.setJdCatId(jdCatId);
                taxPO.setUpdateTime(System.currentTimeMillis());
                updateCategoryCountryPoList.add(taxPO);
                count++;
            } catch (Exception e) {
                log.error("updateCategoryCountryPOJDCategory 更新类目国家关系表异常：catId={},jdCatId={}",taxPO.getLastCatId(),jdCatId, e);
            }
            log.info("updateCategoryCountryPOJDCategory 更新类目国家关系表结束，id={},catId={},jdCatId={}, count={},total={}类目税率信息:{}",taxPO.getId(),taxPO.getLastCatId(),jdCatId ,count,total,JSON.toJSONString(taxPO));
        }

        if(CollectionUtils.isNotEmpty(updateCategoryCountryPoList)){
            categoryCountryAtomicService.updateBatchById(updateCategoryCountryPoList);
            log.info("updateCategoryCountryPOJDCategory 更新类目国家关系表成功 count={},total={}类目税率信息:{}",count,total,JSON.toJSONString(updateCategoryCountryPoList));

        }
    }


    private void updateGlobalCountryCategoryAttributeRelationPOJDCategory(List<GlobalCountryCategoryAttributeRelationPO> categoryCountryPoList, Map<Long, Long> cateAndJdCategoryMap,Map<Long, RelationVO> catIdRealtionVoMap) {
        int total = categoryCountryPoList.size();
        int count = 1;
        List<GlobalCountryCategoryAttributeRelationPO> updateCategoryCountryPoList = Lists.newArrayList();
        for (GlobalCountryCategoryAttributeRelationPO taxPO : categoryCountryPoList) {
            Long jdCatId = getLastJdCatId(cateAndJdCategoryMap, taxPO.getCategoryId(), catIdRealtionVoMap);
            if (!taxPO.getCategoryId().equals(-100L)){
                log.info("updateGlobalCountryCategoryAttributeRelationPOJDCategory 更新国家类目属性关系表开始，id={},catId={},jdCatId={}, count={},total={}类目税率信息:{}",taxPO.getId(),taxPO.getCategoryId(),jdCatId ,count,total,JSON.toJSONString(taxPO));                if (Objects.isNull(jdCatId)) {
                    log.error("updateGlobalCountryCategoryAttributeRelationPOJDCategory 更新国家类目属性关系表 国际类目和京东类目关系为空， count={},total={}类目税率信息:{}",count,total,JSON.toJSONString(taxPO));
                    count++;
                    continue;
                }
                taxPO.setJdCatId(jdCatId);
            }else {
                // -100时，京东类目ID为国际类目ID
                taxPO.setJdCatId(taxPO.getCategoryId());
            }
            try {
                taxPO.setUpdateTime(System.currentTimeMillis());
                updateCategoryCountryPoList.add(taxPO);
                count++;
            } catch (Exception e) {
                log.error("updateGlobalCountryCategoryAttributeRelationPOJDCategory 更新国家类目属性关系表异常：catId={},jdCatId={}",taxPO.getCategoryId(),jdCatId, e);
            }
            log.info("updateGlobalCountryCategoryAttributeRelationPOJDCategory 更新国家类目属性关系表结束，id={},catId={},jdCatId={}, count={},total={}类目税率信息:{}",taxPO.getId(),taxPO.getCategoryId(),jdCatId ,count,total,JSON.toJSONString(taxPO));
        }

        if(CollectionUtils.isNotEmpty(updateCategoryCountryPoList)){
            globalCountryCategoryAttributeRelationAtomicService.updateBatchById(updateCategoryCountryPoList);
            log.info("updateGlobalCountryCategoryAttributeRelationPOJDCategory 更新国家类目属性关系表成功 count={},total={}类目税率信息:{}",count,total,JSON.toJSONString(updateCategoryCountryPoList));

        }
    }


    private void updateGlobalCountryQualificationCategoryRelationPOJDCategory(List<GlobalCountryQualificationCategoryRelationPO> categoryCountryPoList, Map<Long, Long> cateAndJdCategoryMap,Map<Long, RelationVO> catIdRealtionVoMap) {
        int total = categoryCountryPoList.size();
        int count = 1;
        List<GlobalCountryQualificationCategoryRelationPO> updateCategoryCountryPoList = Lists.newArrayList();
        for (GlobalCountryQualificationCategoryRelationPO taxPO : categoryCountryPoList) {
            Long jdCatId = getLastJdCatId(cateAndJdCategoryMap, taxPO.getCategoryId(), catIdRealtionVoMap);
            if (Objects.isNull(jdCatId) && Constant.DEFAULT_ALL_CATEGORY_SELECT.equals(taxPO.getCategoryId())) {
                jdCatId = Constant.DEFAULT_ALL_CATEGORY_SELECT;
            }

            log.info("updateGlobalCountryQualificationCategoryRelationPOJDCategory 更新类目资质关系表开始，id={},catId={},jdCatId={}, count={},total={}类目税率信息:{}",taxPO.getId(),taxPO.getCategoryId(),jdCatId ,count,total,JSON.toJSONString(taxPO));
            if (Objects.isNull(jdCatId)) {
                log.error("updateGlobalCountryQualificationCategoryRelationPOJDCategory 更新类目资质关系表 国际类目和京东类目关系为空， count={},total={}类目税率信息:{}",count,total,JSON.toJSONString(taxPO));
                count++;
                continue;
            }
            try {
                taxPO.setJdCatId(jdCatId);
                taxPO.setUpdateTime(System.currentTimeMillis());
                updateCategoryCountryPoList.add(taxPO);
                count++;
            } catch (Exception e) {
                log.error("updateGlobalCountryQualificationCategoryRelationPOJDCategory 更新类目资质关系表异常：catId={},jdCatId={}",taxPO.getCategoryId(),jdCatId, e);
            }
            log.info("updateGlobalCountryQualificationCategoryRelationPOJDCategory 更新类目资质关系表结束，id={},catId={},jdCatId={}, count={},total={}类目税率信息:{}",taxPO.getId(),taxPO.getCategoryId(),jdCatId ,count,total,JSON.toJSONString(taxPO));
        }

        if(CollectionUtils.isNotEmpty(updateCategoryCountryPoList)){
            globalCountryQualificationCategoryRelationAtomicService.updateBatchById(updateCategoryCountryPoList);
            log.info("updateGlobalCountryQualificationCategoryRelationPOJDCategory 更新类目资质关系表成功 count={},total={}类目税率信息:{}",count,total,JSON.toJSONString(updateCategoryCountryPoList));

        }
    }

    private void updateFulfillmentRatePOJDCategory(List<FulfillmentRatePO> fulfillmentRatePOList, Map<Long, Long> cateAndJdCategoryMap) {
        int total = fulfillmentRatePOList.size();
        int count = 1;
        List<FulfillmentRatePO> updateCategoryCountryPoList = Lists.newArrayList();
        for (FulfillmentRatePO taxPO : fulfillmentRatePOList) {
            if (Objects.isNull(taxPO.getFirstCatId()) && Objects.isNull(taxPO.getSecondCatId()) && Objects.isNull(taxPO.getThirdCatId()) && Objects.isNull(taxPO.getLastCatId())){
                log.info("updateFulfillmentRatePOJDCategory 更新履约费率表 国际类目和京东类目关系为空， count={},total={}类目税率信息:{}",count,total,JSON.toJSONString(taxPO));
                count++;
                continue;
            }


            log.info("updateFulfillmentRatePOJDCategory 更新履约费率表开始，id={},catId={}, count={},total={}类目税率信息:{}",taxPO.getId(),taxPO.getFirstCatId() ,count,total,JSON.toJSONString(taxPO));
            try {
                Long jdFirstCatId = cateAndJdCategoryMap.get(taxPO.getFirstCatId());
                Long jdSecondCatId = cateAndJdCategoryMap.get(taxPO.getSecondCatId());
                Long jdThirdCatId = cateAndJdCategoryMap.get(taxPO.getThirdCatId());
                Long jdLastCatId = cateAndJdCategoryMap.get(taxPO.getLastCatId());
                taxPO.setFirstJdCatId(jdFirstCatId);
                if (Objects.nonNull(jdSecondCatId)) {
                    taxPO.setSecondJdCatId(jdSecondCatId);
                }
                if (Objects.nonNull(jdThirdCatId)) {
                    taxPO.setThirdJdCatId(jdThirdCatId);
                }
                if (Objects.nonNull(jdLastCatId)) {
                    taxPO.setLastJdCatId(jdLastCatId);
                }
                taxPO.setUpdateTime(new Date());
                updateCategoryCountryPoList.add(taxPO);
                count++;
            } catch (Exception e) {
                log.error("updateFulfillmentRatePOJDCategory 更新履约费率表异常：catId={}",JSON.toJSONString(taxPO), e);
            }
            log.info("updateFulfillmentRatePOJDCategory 更新履约费率表结束，id={},catId={},jdCatId={}, count={},total={}类目税率信息:{}",taxPO.getId(),taxPO.getFirstCatId(),taxPO.getFirstJdCatId() ,count,total,JSON.toJSONString(taxPO));
        }

        if(CollectionUtils.isNotEmpty(updateCategoryCountryPoList)){
            fulfillmentRateAtomicService.updateBatchById(updateCategoryCountryPoList);
            log.info("updateGlobalCountryQualificationCategoryRelationPOJDCategory 更新履约费率表成功 count={},total={}类目税率信息:{}",count,total,JSON.toJSONString(updateCategoryCountryPoList));

        }
    }


    /**
     * 更新国家池的京东类目ID
     * @return
     */
    public String updateCountryMkuJdCategory(String env,Set<Long> mkuIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<CountryMkuPO> queryWrapper = Wrappers.lambdaQuery(CountryMkuPO.class).select(CountryMkuPO::getId, CountryMkuPO::getMkuId, CountryMkuPO::getLastCatId, CountryMkuPO::getUpdater,CountryMkuPO::getWarnReason)
                .in(CollectionUtils.isNotEmpty(mkuIds), CountryMkuPO::getMkuId, mkuIds);
        List<CountryMkuPO> list = countryMkuAtomicService.list(queryWrapper);
        int  total = list.size();


        Set<Long> catIds = list.stream().map(CountryMkuPO::getLastCatId).collect(Collectors.toSet());
        List<CategoryPO> categoryPoList = getCategoryPOList(catIds);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        Map<Long, RelationVO> catIdRealtionVoMap = this.getCatAndThirdCatIdRealtionVoMap(categoryPoList);

        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(categoryPO -> Objects.nonNull(categoryPO.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));

        List<List<CountryMkuPO>> partitionList = Lists.partition(list, 100);
        int count = 1;
        for (List<CountryMkuPO> subList : partitionList) {
            List<CountryMkuPO> updateMkuList = Lists.newArrayListWithExpectedSize(subList.size());
            try {
                for (CountryMkuPO mkuPO : subList) {
                    Long mkuId = mkuPO.getMkuId();
                    Long catId = mkuPO.getLastCatId();
                    String updater = mkuPO.getUpdater();
                    log.info("updateCountryMkuJdCategory 更新国家池的京东类目ID开始 mkuId={},catId={} count={},total={}",mkuId,catId,count,total);

                    Long jdCatId = getLastJdCatId(cateAndJdCategoryMap, catId,catIdRealtionVoMap);
                    if (Objects.isNull(jdCatId)) {
                        log.info("updateCountryMkuJdCategory 更新国家池的京东类目ID 国际类目ID={},没有京东类目ID,mkuId={}",catId,mkuId);
                        count++;
                        continue;
                    }
                    CountryMkuPO updateMkuPo = new CountryMkuPO();
                    updateMkuPo.setId(mkuPO.getId());
                    updateMkuPo.setWarnReason(mkuPO.getWarnReason());
                    updateMkuPo.setUpdater(updater);
                    updateMkuPo.setUpdateTime(System.currentTimeMillis());
                    updateMkuPo.setJdCatId(jdCatId);
                    updateMkuList.add(updateMkuPo);
                    count++;
                }
                if (CollectionUtils.isNotEmpty(updateMkuList)) {
                    countryMkuAtomicService.updateBatchById(updateMkuList);
                }
            } catch (Exception e) {
                log.info("updateCountryMkuJdCategory 更新国家池的京东类目ID异常：mkuId={}，类目Id={}",JSON.toJSONString(subList),e );
            }finally {
                log.info("updateCountryMkuJdCategory 更新国家池的京东类目ID结束 mkuId={}, count={},total={}",JSON.toJSONString(updateMkuList),count,total);
            }
        }

        return "success";
    }

    /**
     * 更新客户池的京东类目ID
     * @return
     */
    public String updateCustomerMkuJdCategory(String env,Set<Long> mkuIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<CustomerMkuPO> queryWrapper = Wrappers.lambdaQuery(CustomerMkuPO.class).select(CustomerMkuPO::getId, CustomerMkuPO::getMkuId, CustomerMkuPO::getCatId, CustomerMkuPO::getUpdater)
                .in(CollectionUtils.isNotEmpty(mkuIds), CustomerMkuPO::getMkuId, mkuIds);
        List<CustomerMkuPO> list = customerMkuAtomicService.list(queryWrapper);
        int  total = list.size();


        Set<Long> catIds = list.stream().map(CustomerMkuPO::getCatId).collect(Collectors.toSet());
        List<CategoryPO> categoryPoList = getCategoryPOList(catIds);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        Map<Long, RelationVO> catIdRealtionVoMap = this.getCatAndThirdCatIdRealtionVoMap(categoryPoList);

        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(categoryPO -> Objects.nonNull(categoryPO.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));

        List<List<CustomerMkuPO>> partitionList = Lists.partition(list, 100);
        int count = 1;
        for (List<CustomerMkuPO> subList : partitionList) {
            List<CustomerMkuPO> updateMkuList = Lists.newArrayListWithExpectedSize(subList.size());
            try {
                for (CustomerMkuPO mkuPO : subList) {
                    Long mkuId = mkuPO.getMkuId();
                    Long catId = mkuPO.getCatId();
                    String updater = mkuPO.getUpdater();
                    log.info("updateCustomerMkuJdCategory 更新客户池的京东类目ID开始 mkuId={},catId={} count={},total={}",mkuId,catId,count,total);

                    Long jdCatId = getLastJdCatId(cateAndJdCategoryMap,catId,catIdRealtionVoMap);
                    if (Objects.isNull(jdCatId)) {
                        log.info("updateCustomerMkuJdCategory 更新客户池的京东类目ID 国际类目ID={},没有京东类目ID,mkuId={}",catId,mkuId);
                        count++;
                        continue;
                    }
                    CustomerMkuPO updateMkuPo = new CustomerMkuPO();
                    updateMkuPo.setId(mkuPO.getId());
                    updateMkuPo.setUpdater(updater);
                    updateMkuPo.setUpdateTime(new Date());
                    updateMkuPo.setJdCatId(jdCatId);
                    updateMkuList.add(updateMkuPo);
                    count++;
                }
                if (CollectionUtils.isNotEmpty(updateMkuList)) {
                    customerMkuAtomicService.updateBatchById(updateMkuList);
                }
            } catch (Exception e) {
                log.info("updateCustomerMkuJdCategory 更新客户池的京东类目ID异常：mkuId={}，类目Id={}",JSON.toJSONString(subList),e );
            }finally {
                log.info("updateCustomerMkuJdCategory 更新客户池的京东类目ID结束 mkuId={}, count={},total={}",JSON.toJSONString(updateMkuList),count,total);
            }
        }

        return "success";
    }

    /**
     * 更新产品线的京东类目ID
     * jdi_isc_business_line_relation_sharding
     * @return
     */
    public String updateBusinessLineJdCategory(String env,Set<Long> businessLineIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<BusinessLineRelationPO> queryWrapper = Wrappers.lambdaQuery(BusinessLineRelationPO.class)
                .select(BusinessLineRelationPO::getId, BusinessLineRelationPO::getCategoryId, BusinessLineRelationPO::getUpdater).in(CollectionUtils.isNotEmpty(businessLineIds), BusinessLineRelationPO::getId, businessLineIds);
        List<BusinessLineRelationPO> list = businessLineRelationAtomicService.list(queryWrapper);
        int  total = list.size();


        Set<Long> catIds = list.stream().map(BusinessLineRelationPO::getCategoryId).collect(Collectors.toSet());
        List<CategoryPO> categoryPoList = getCategoryPOList(catIds);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        Map<Long, RelationVO> catIdRealtionVoMap = this.getCatAndThirdCatIdRealtionVoMap(categoryPoList);
        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(categoryPO -> Objects.nonNull(categoryPO.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));

        List<List<BusinessLineRelationPO>> partitionList = Lists.partition(list, 100);
        int count = 1;
        for (List<BusinessLineRelationPO> subList : partitionList) {
            List<BusinessLineRelationPO> updateMkuList = Lists.newArrayListWithExpectedSize(subList.size());
            try {
                for (BusinessLineRelationPO mkuPO : subList) {
                    Long catId = mkuPO.getCategoryId();
                    String updater = mkuPO.getUpdater();
                    log.info("updateBusinessLineJdCategory 更新产品线的京东类目ID开始 catId={} count={},total={}",catId,count,total);

                    Long jdCatId = getLastJdCatId(cateAndJdCategoryMap,catId,catIdRealtionVoMap);
                    if (Objects.isNull(jdCatId)) {
                        log.info("updateBusinessLineJdCategory 更新产品线的京东类目ID 国际类目ID={},没有京东类目ID",catId);
                        count++;
                        continue;
                    }
                    BusinessLineRelationPO updateMkuPo = new BusinessLineRelationPO();
                    updateMkuPo.setId(mkuPO.getId());
                    updateMkuPo.setUpdater(updater);
                    updateMkuPo.setUpdateTime(System.currentTimeMillis());
                    updateMkuPo.setJdCatId(jdCatId);
                    updateMkuList.add(updateMkuPo);
                    count++;
                }
                if (CollectionUtils.isNotEmpty(updateMkuList)) {
                    businessLineRelationAtomicService.updateBatchById(updateMkuList);
                }
            } catch (Exception e) {
                log.info("updateBusinessLineJdCategory 更新产品线的京东类目ID异常：businessRelation={}，类目Id={}",JSON.toJSONString(subList),e );
            }finally {
                log.info("updateBusinessLineJdCategory 更新产品线的京东类目ID结束 businessRelation={}, count={},total={}",JSON.toJSONString(updateMkuList),count,total);
            }
        }
        return "success";
    }


    /**
     * 更新国家协议加价率的京东类目ID
     * jdi_isc_business_line_relation_sharding
     * @return
     */
    public String updateMakeUpJdCategory(String env,Set<Long> catIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<MarkupRatePO> queryWrapper = Wrappers.lambdaQuery(MarkupRatePO.class)
                .select(MarkupRatePO::getId, MarkupRatePO::getLastCatId, MarkupRatePO::getUpdater)
                .isNotNull(MarkupRatePO::getLastCatId).in(CollectionUtils.isNotEmpty(catIds),MarkupRatePO::getLastCatId,catIds);
        List<MarkupRatePO> list = markupRateAtomicService.list(queryWrapper);
        int  total = list.size();


        Set<Long> lastCatIds = list.stream().map(MarkupRatePO::getLastCatId).collect(Collectors.toSet());
        LambdaQueryWrapper<CategoryPO> categoryQueryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId, CategoryPO::getJdCatId).in(CategoryPO::getId, lastCatIds);
        List<CategoryPO> categoryPoList = categoryAtomicService.list(categoryQueryWrapper);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(po-> Objects.nonNull(po.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));

        List<List<MarkupRatePO>> partitionList = Lists.partition(list, 100);
        int count = 1;
        for (List<MarkupRatePO> subList : partitionList) {
            List<MarkupRatePO> updateMkuList = Lists.newArrayListWithExpectedSize(subList.size());
            try {
                for (MarkupRatePO mkuPO : subList) {
                    Long catId = mkuPO.getLastCatId();
                    String updater = mkuPO.getUpdater();
                    log.info("updateMakeUpJdCategory 更新国家协议加价率的京东类目IDID开始 catId={} count={},total={}",catId,count,total);

                    Long jdCatId = cateAndJdCategoryMap.get(catId);
                    if (Objects.isNull(jdCatId)) {
                        log.info("updateMakeUpJdCategory 更新国家协议加价率的京东类目ID 国际类目ID={},没有京东类目ID",catId);
                        count++;
                        continue;
                    }
                    MarkupRatePO updateMkuPo = new MarkupRatePO();
                    updateMkuPo.setId(mkuPO.getId());
                    updateMkuPo.setUpdater(updater);
                    updateMkuPo.setUpdateTime(System.currentTimeMillis());
                    updateMkuPo.setJdCatId(jdCatId);
                    updateMkuList.add(updateMkuPo);
                    count++;
                }
                if (CollectionUtils.isNotEmpty(updateMkuList)) {
                    markupRateAtomicService.updateBatchById(updateMkuList);
                }
            } catch (Exception e) {
                log.info("updateMakeUpJdCategory 更新国家协议加价率的京东类目ID异常：makeUp={}，类目Id={}",JSON.toJSONString(subList),e );
            }finally {
                log.info("updateMakeUpJdCategory  更新国家协议加价率的京东类目ID结束 makeUp={}, count={},total={}",JSON.toJSONString(updateMkuList),count,total);
            }
        }
        return "success";
    }


    /**
     * 更新利润率阈值的京东类目ID
     * jdi_isc_business_line_relation_sharding
     * @return
     */
    public String updateProfitRatePOJdCategory(String env,Set<Long> catIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<ProfitRatePO> queryWrapper = Wrappers.lambdaQuery(ProfitRatePO.class)
                .select(ProfitRatePO::getId,ProfitRatePO::getFirstCatId,ProfitRatePO::getSecondCatId,ProfitRatePO::getThirdCatId, ProfitRatePO::getLastCatId, ProfitRatePO::getUpdater).in(CollectionUtils.isNotEmpty(catIds),ProfitRatePO::getLastCatId,catIds);
        List<ProfitRatePO> list = profitRateAtomicService.list(queryWrapper);
        int  total = list.size();


        Set<Long> dbCatIds = list.stream().filter(Objects::nonNull)
                .flatMap(profitRatePO -> Stream.of(profitRatePO.getFirstCatId(), profitRatePO.getSecondCatId(), profitRatePO.getThirdCatId(), profitRatePO.getLastCatId()))
                .collect(Collectors.toSet());

        dbCatIds.remove(null);
        LambdaQueryWrapper<CategoryPO> categoryQueryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId, CategoryPO::getJdCatId).in(CategoryPO::getId, dbCatIds);
        List<CategoryPO> categoryPoList = categoryAtomicService.list(categoryQueryWrapper);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(categoryPO -> Objects.nonNull(categoryPO.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));

        List<List<ProfitRatePO>> partitionList = Lists.partition(list, 100);
        int count = 1;
        for (List<ProfitRatePO> subList : partitionList) {
            List<ProfitRatePO> updateMkuList = Lists.newArrayListWithExpectedSize(subList.size());
            try {
                for (ProfitRatePO mkuPO : subList) {
                    Long catId = mkuPO.getLastCatId();
                    String updater = mkuPO.getUpdater();
                    log.info("updateMakeUpJdCategory 更新利润率阈值的京东类目ID开始 catId={} count={},total={}",catId,count,total);

                    Long jdFirstCatId = cateAndJdCategoryMap.get(mkuPO.getFirstCatId());
                    Long jdSecondCatId = cateAndJdCategoryMap.get(mkuPO.getSecondCatId());
                    Long jdThirdCatId = cateAndJdCategoryMap.get(mkuPO.getThirdCatId());
                    Long jdLastCatId = cateAndJdCategoryMap.get(mkuPO.getLastCatId());

                    if (Objects.isNull(jdFirstCatId) && Objects.isNull(jdSecondCatId) && Objects.isNull(jdThirdCatId) && Objects.isNull(jdLastCatId)) {
                        log.info("updateMakeUpJdCategory 更新利润率阈值的京东类目ID 国际类目ID={},没有京东类目ID",catId);
                        count++;
                        continue;
                    }

                    ProfitRatePO updateMkuPo = new ProfitRatePO();
                    updateMkuPo.setId(mkuPO.getId());
                    updateMkuPo.setUpdater(updater);
                    updateMkuPo.setUpdateTime(new Date());
                    if (Objects.nonNull(jdFirstCatId)) {
                        updateMkuPo.setFirstJdCatId(jdFirstCatId);
                    }
                    if (Objects.nonNull(jdSecondCatId)) {
                        updateMkuPo.setSecondJdCatId(jdSecondCatId);
                    }
                    if (Objects.nonNull(jdThirdCatId)) {
                        updateMkuPo.setThirdJdCatId(jdThirdCatId);
                    }
                    if (Objects.nonNull(jdLastCatId)) {
                        updateMkuPo.setLastJdCatId(jdLastCatId);
                    }
                    updateMkuList.add(updateMkuPo);
                    count++;
                }
                if (CollectionUtils.isNotEmpty(updateMkuList)) {
                    profitRateAtomicService.updateBatchById(updateMkuList);
                }
            } catch (Exception e) {
                log.info("updateMakeUpJdCategory 更新利润率阈值的京东类目ID异常：ProfitRate={}，类目Id={}",JSON.toJSONString(subList),e );
            }finally {
                log.info("updateMakeUpJdCategory  更新利润率阈值的京东类目ID结束 ProfitRate={}, count={},total={}",JSON.toJSONString(updateMkuList),count,total);
            }
        }
        return "success";
    }


    /**
     * 更新国家协议价草稿的京东类目ID
     * jdi_isc_country_agreement_price_draft_sharding
     * @return
     */
    public String updateCountryAgreementPriceDraftJdCategory(String env,Set<Long> lastCatIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<CountryAgreementPriceDraftPO> queryWrapper = Wrappers.lambdaQuery(CountryAgreementPriceDraftPO.class)
                .select(CountryAgreementPriceDraftPO::getId, CountryAgreementPriceDraftPO::getLastCatId, CountryAgreementPriceDraftPO::getUpdater)
                .isNotNull(CountryAgreementPriceDraftPO::getLastCatId)
                .in(CollectionUtils.isNotEmpty(lastCatIds), CountryAgreementPriceDraftPO::getLastCatId, lastCatIds);
        List<CountryAgreementPriceDraftPO> list = countryAgreementPriceDraftAtomicService.list(queryWrapper);
        int  total = list.size();


        Set<Long> catIds = list.stream().map(CountryAgreementPriceDraftPO::getLastCatId).collect(Collectors.toSet());
        List<CategoryPO> categoryPoList = getCategoryPOList(catIds);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        Map<Long, RelationVO> catIdRealtionVoMap = this.getCatAndThirdCatIdRealtionVoMap(categoryPoList);

        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(categoryPO -> Objects.nonNull(categoryPO.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));

        List<List<CountryAgreementPriceDraftPO>> partitionList = Lists.partition(list, 100);
        int count = 1;
        for (List<CountryAgreementPriceDraftPO> subList : partitionList) {
            List<CountryAgreementPriceDraftPO> updatePlList = Lists.newArrayListWithExpectedSize(subList.size());
            try {
                for (CountryAgreementPriceDraftPO dbPO : subList) {
                    Long catId = dbPO.getLastCatId();
                    String updater = dbPO.getUpdater();
                    log.info("updateCountryAgreementPriceDraftJdCategory 更新国家协议价草稿的京东类目ID开始 catId={} count={},total={}",catId,count,total);

                    Long jdCatId = getLastJdCatId(cateAndJdCategoryMap, catId, catIdRealtionVoMap);
                    if (Objects.isNull(jdCatId)) {
                        log.info("updateCountryAgreementPriceDraftJdCategory 更新国家协议价草稿的京东类目ID 国际类目ID={},没有京东类目ID",catId);
                        count++;
                        continue;
                    }
                    CountryAgreementPriceDraftPO updatePo = new CountryAgreementPriceDraftPO();
                    updatePo.setId(dbPO.getId());
                    updatePo.setUpdater(updater);
                    updatePo.setUpdateTime(System.currentTimeMillis());
                    updatePo.setJdCatId(jdCatId);
                    updatePlList.add(updatePo);
                    count++;
                }
                if (CollectionUtils.isNotEmpty(updatePlList)) {
                    countryAgreementPriceDraftAtomicService.updateBatchById(updatePlList);
                }
            } catch (Exception e) {
                log.info("updateCountryAgreementPriceDraftJdCategory 更新国家协议价草稿的京东类目ID异常：makeUp={}，类目Id={}",JSON.toJSONString(subList),e );
            }finally {
                log.info("updateCountryAgreementPriceDraftJdCategory  更新国家协议价草稿的京东类目ID结束 makeUp={}, count={},total={}",JSON.toJSONString(updatePlList),count,total);
            }
        }
        return "success";
    }


    /**
     * 更新国家协议价的京东类目ID
     * jdi_isc_country_agreement_price_sharding
     * @return
     */
    public String updateCountryAgreementPriceJdCategory(String env,Set<Long> lastCatIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<CountryAgreementPricePO> queryWrapper = Wrappers.lambdaQuery(CountryAgreementPricePO.class)
                .select(CountryAgreementPricePO::getId, CountryAgreementPricePO::getLastCatId, CountryAgreementPricePO::getUpdater)
                .isNotNull(CountryAgreementPricePO::getLastCatId)
                .in(CollectionUtils.isNotEmpty(lastCatIds), CountryAgreementPricePO::getLastCatId, lastCatIds);
        List<CountryAgreementPricePO> list = countryAgreementPriceAtomicService.list(queryWrapper);
        int  total = list.size();


        Set<Long> catIds = list.stream().map(CountryAgreementPricePO::getLastCatId).collect(Collectors.toSet());
        List<CategoryPO> categoryPoList = getCategoryPOList(catIds);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        Map<Long, RelationVO> catIdRealtionVoMap = this.getCatAndThirdCatIdRealtionVoMap(categoryPoList);

        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(categoryPO -> Objects.nonNull(categoryPO.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));

        List<List<CountryAgreementPricePO>> partitionList = Lists.partition(list, 100);
        int count = 1;
        for (List<CountryAgreementPricePO> subList : partitionList) {
            List<CountryAgreementPricePO> updatePlList = Lists.newArrayListWithExpectedSize(subList.size());
            try {
                for (CountryAgreementPricePO dbPO : subList) {
                    Long catId = dbPO.getLastCatId();
                    String updater = dbPO.getUpdater();
                    log.info("updateCountryAgreementPriceJdCategory 更新国家协议价的京东类目ID开始 catId={} count={},total={}",catId,count,total);

                    Long jdCatId = getLastJdCatId(cateAndJdCategoryMap, catId, catIdRealtionVoMap);
                    if (Objects.isNull(jdCatId)) {
                        log.info("updateCountryAgreementPriceJdCategory 更新国家协议价的京东类目ID 国际类目ID={},没有京东类目ID",catId);
                        count++;
                        continue;
                    }
                    CountryAgreementPricePO updatePo = new CountryAgreementPricePO();
                    updatePo.setId(dbPO.getId());
                    updatePo.setUpdater(updater);
                    updatePo.setUpdateTime(System.currentTimeMillis());
                    updatePo.setJdCatId(jdCatId);
                    updatePlList.add(updatePo);
                    count++;
                }
                if (CollectionUtils.isNotEmpty(updatePlList)) {
                    countryAgreementPriceAtomicService.updateBatchById(updatePlList);
                }
            } catch (Exception e) {
                log.info("updateCountryAgreementPriceJdCategory 更新国家协议价的京东类目ID异常：makeUp={}，类目Id={}",JSON.toJSONString(subList),e );
            }finally {
                log.info("updateCountryAgreementPriceJdCategory  更新国家协议价的京东类目ID结束 makeUp={}, count={},total={}",JSON.toJSONString(updatePlList),count,total);
            }
        }
        return "success";
    }


    /**
     * 更新国家协议价预警的京东类目ID
     * jdi_isc_country_agreement_price_warning_sharding
     * @return
     */
    public String updateCountryAgreementPriceWarningPOJdCategory(String env,Set<Long> lastCatIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<CountryAgreementPriceWarningPO> queryWrapper = Wrappers.lambdaQuery(CountryAgreementPriceWarningPO.class)
                .select(CountryAgreementPriceWarningPO::getId, CountryAgreementPriceWarningPO::getLastCatId, CountryAgreementPriceWarningPO::getUpdater)
                .isNotNull(CountryAgreementPriceWarningPO::getLastCatId)
                .in(CollectionUtils.isNotEmpty(lastCatIds), CountryAgreementPriceWarningPO::getLastCatId, lastCatIds);
        List<CountryAgreementPriceWarningPO> list = countryAgreementPriceWarningAtomicService.list(queryWrapper);
        int  total = list.size();


        Set<Long> catIds = list.stream().map(CountryAgreementPriceWarningPO::getLastCatId).collect(Collectors.toSet());
        List<CategoryPO> categoryPoList = getCategoryPOList(catIds);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        Map<Long, RelationVO> catIdRealtionVoMap = this.getCatAndThirdCatIdRealtionVoMap(categoryPoList);

        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(categoryPO -> Objects.nonNull(categoryPO.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));

        List<List<CountryAgreementPriceWarningPO>> partitionList = Lists.partition(list, 100);
        int count = 1;
        for (List<CountryAgreementPriceWarningPO> subList : partitionList) {
            List<CountryAgreementPriceWarningPO> updatePlList = Lists.newArrayListWithExpectedSize(subList.size());
            try {
                for (CountryAgreementPriceWarningPO dbPO : subList) {
                    Long catId = dbPO.getLastCatId();
                    String updater = dbPO.getUpdater();
                    log.info("updateCountryAgreementPriceWarningPOJdCategory 更新国家协议价预警的京东类目ID开始 catId={} count={},total={}",catId,count,total);

                    Long jdCatId = getLastJdCatId(cateAndJdCategoryMap, catId, catIdRealtionVoMap);
                    if (Objects.isNull(jdCatId)) {
                        log.info("updateCountryAgreementPriceWarningPOJdCategory 更新国家协议价预警的京东类目ID 国际类目ID={},没有京东类目ID",catId);
                        count++;
                        continue;
                    }
                    CountryAgreementPriceWarningPO updatePo = new CountryAgreementPriceWarningPO();
                    updatePo.setId(dbPO.getId());
                    updatePo.setUpdater(updater);
                    updatePo.setUpdateTime(System.currentTimeMillis());
                    updatePo.setJdCatId(jdCatId);
                    updatePlList.add(updatePo);
                    count++;
                }
                if (CollectionUtils.isNotEmpty(updatePlList)) {
                    countryAgreementPriceWarningAtomicService.updateBatchById(updatePlList);
                }
            } catch (Exception e) {
                log.info("updateCountryAgreementPriceWarningPOJdCategory 更新国家协议价预警的京东类目ID 异常：makeUp={}，类目Id={}",JSON.toJSONString(subList),e );
            }finally {
                log.info("updateCountryAgreementPriceWarningPOJdCategory  更新国家协议价预警的京东类目ID 结束 makeUp={}, count={},total={}",JSON.toJSONString(updatePlList),count,total);
            }
        }
        return "success";
    }

    /**
     * 更新国家扩展价的京东类目ID
     * jdi_isc_country_extend_price_sharding
     * @return
     */
    public String updateCountryExtendPriceJdCategory(String env,Set<Long> lastCatIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<CountryExtendPricePO> queryWrapper = Wrappers.lambdaQuery(CountryExtendPricePO.class)
                .select(CountryExtendPricePO::getId, CountryExtendPricePO::getLastCatId, CountryExtendPricePO::getUpdater)
                .isNotNull(CountryExtendPricePO::getLastCatId)
                .in(CollectionUtils.isNotEmpty(lastCatIds), CountryExtendPricePO::getLastCatId, lastCatIds);
        List<CountryExtendPricePO> list = countryExtendPriceAtomicService.list(queryWrapper);
        int  total = list.size();


        Set<Long> catIds = list.stream().map(CountryExtendPricePO::getLastCatId).collect(Collectors.toSet());
        List<CategoryPO> categoryPoList = getCategoryPOList(catIds);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        Map<Long, RelationVO> catIdRealtionVoMap = this.getCatAndThirdCatIdRealtionVoMap(categoryPoList);

        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(categoryPO -> Objects.nonNull(categoryPO.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));

        List<List<CountryExtendPricePO>> partitionList = Lists.partition(list, 100);
        int count = 1;
        for (List<CountryExtendPricePO> subList : partitionList) {
            List<CountryExtendPricePO> updatePlList = Lists.newArrayListWithExpectedSize(subList.size());
            try {
                for (CountryExtendPricePO dbPO : subList) {
                    Long catId = dbPO.getLastCatId();
                    String updater = dbPO.getUpdater();
                    log.info("updateCountryAgreementPriceWarningPOJdCategory 更新国家扩展价的京东类目ID 开始 catId={} count={},total={}",catId,count,total);

                    Long jdCatId = getLastJdCatId(cateAndJdCategoryMap,catId, catIdRealtionVoMap);
                    if (Objects.isNull(jdCatId)) {
                        log.info("updateCountryAgreementPriceWarningPOJdCategory 更新国家扩展价的京东类目ID 国际类目ID={},没有京东类目ID",catId);
                        count++;
                        continue;
                    }
                    CountryExtendPricePO updatePo = new CountryExtendPricePO();
                    updatePo.setId(dbPO.getId());
                    updatePo.setUpdater(updater);
                    updatePo.setUpdateTime(System.currentTimeMillis());
                    updatePo.setJdCatId(jdCatId);
                    updatePlList.add(updatePo);
                    count++;
                }
                if (CollectionUtils.isNotEmpty(updatePlList)) {
                    countryExtendPriceAtomicService.updateBatchById(updatePlList);
                }
            } catch (Exception e) {
                log.info("updateCountryExtendPriceJdCategory 更新国家扩展价的京东类目ID 异常：makeUp={}，类目Id={}",JSON.toJSONString(subList),e );
            }finally {
                log.info("updateCountryExtendPriceJdCategory  更新国家扩展价的京东类目ID 结束 makeUp={}, count={},total={}",JSON.toJSONString(updatePlList),count,total);
            }
        }
        return "success";
    }

    /**
     * 将四级类目映射国内类目ID为3级的更新到3级类目
     * @param env
     * @param catIds
     * @return
     */
    public String updateThirdCategoryJdCategoryForFourCategory(String env,Set<Long> catIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }

        if (CollectionUtils.isEmpty(catIds)) {
            return "类目ID不能为空";
        }

        // 查询自己类目信息
        LambdaQueryWrapper<CategoryPO> queryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId, CategoryPO::getJdCatId,CategoryPO::getParentCatId,CategoryPO::getUpdater).in(CategoryPO::getId, catIds);
        List<CategoryPO> categoryPOList = categoryAtomicService.list(queryWrapper);

        int total = categoryPOList.size();
        log.info("updateThirdCategoryJdCategoryForFourCategory 更新三级类目信息京东类目ID 总数 total={}",total);
        List<List<CategoryPO>> partitionList = Lists.partition(categoryPOList, 100);

        int count = 1;
        for (List<CategoryPO> subList : partitionList) {
            List<CategoryPO> updatePlList = Lists.newArrayListWithExpectedSize(subList.size());
            try {
                for (CategoryPO dbPO : subList) {
                    Long jdCatId = dbPO.getJdCatId();
                    Long parentCatId = dbPO.getParentCatId();
                    log.info("updateThirdCategoryJdCategoryForFourCategory 更新三级类目信息京东类目ID 开始 总数 total={}，count={}, 四级类目ID={},三级类目ID={},京东类目ID={}",total,count,dbPO.getId(),parentCatId,jdCatId);
                    CategoryPO updateCategoryPo = new CategoryPO();
                    updateCategoryPo.setId(parentCatId);
                    updateCategoryPo.setJdCatId(jdCatId);
                    updateCategoryPo.setUpdateTime(new Date());
                    updateCategoryPo.setUpdater(dbPO.getUpdater());
                    updatePlList.add(updateCategoryPo);
                    count++;
                }

                // 批量更新三级类目信息
                if (CollectionUtils.isNotEmpty(updatePlList)) {
                    categoryAtomicService.updateBatchById(updatePlList);
                    log.info("updateThirdCategoryJdCategoryForFourCategory 更新三级类目信息京东类目ID 开始 总数 total={}，count={}, 三级类目信息={}",total,count,JSON.toJSONString(updatePlList));
                }
            } catch (Exception e) {
                log.error("updateThirdCategoryJdCategoryForFourCategory 更新三级类目信息京东类目ID 异常 总数 total={}，count={}",total,count,e);
            }finally {
                log.info("updateThirdCategoryJdCategoryForFourCategory 更新三级类目信息京东类目ID 开始 总数 total={}，count={}, 四级类目信息={}",total,count,JSON.toJSONString(subList));
            }
        }
        return "success";
    }


    /**
     * 更新MKU ES的京东类目ID
     * @return
     */
    public String updateMkuEsJdCategory(String  env,Set<Long> mkuIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<MkuPO> queryWrapper = Wrappers.lambdaQuery(MkuPO.class).select(MkuPO::getMkuId, MkuPO::getCatId, MkuPO::getUpdater,MkuPO::getJdCatId).in(CollectionUtils.isNotEmpty(mkuIds),MkuPO::getMkuId,mkuIds).orderByAsc(MkuPO::getMkuId);
        List<MkuPO> list = mkuAtomicService.list(queryWrapper);
        int  total = list.size();

        List<List<MkuPO>> partitionList = Lists.partition(list, 10);
        AtomicLong count = new AtomicLong(1);
        for (List<MkuPO> subList : partitionList) {
            List<CompletableFuture<Boolean>> completableFutureList = Lists.newArrayListWithExpectedSize(subList.size());
            for (MkuPO mkuPO : subList) {
                count.getAndIncrement();
                completableFutureList.add(CompletableFuture.supplyAsync(() -> updateEsSync(subList, mkuPO, count, total), pool));
            }

            try {
                // 取回结果，等候5秒
                CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).get(10, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("updateMkuEsJdCategory 更新MKU ES的京东类目ID 取回结果异常",e);
            }
        }
        return "success";
    }

    private boolean updateEsSync(List<MkuPO> subList, MkuPO mkuPO, AtomicLong count, int total) {
        try {
            Long mkuId = mkuPO.getMkuId();
            Long jdCatId = mkuPO.getJdCatId();
            log.info("updateMkuEsJdCategory 更新MKU ES的京东类目ID 开始 mkuId={},catId={} count={},total={}",mkuId,jdCatId, count, total);

            if (Objects.isNull(jdCatId)) {
                log.info("updateMkuEsJdCategory 更新MKU ES的京东类目ID 国际类目ID={},没有京东类目ID,mkuId={}",jdCatId,mkuId);
                return false;
            }

            List<CustomerMkuPO> customerMkuPOList = customerMkuAtomicService.listNoClientCode(mkuId);

            if (CollectionUtils.isEmpty(customerMkuPOList)) {
                return false;
            }

            for (CustomerMkuPO customerMkuPO: customerMkuPOList){
                boolean updated = mkuEsManageService.updateMkuEsForCategory(customerMkuPO);
                if (updated) {
                    log.info("updateMkuEsJdCategory 更新MKU ES的京东类目ID mkuId={}，更新ES结果成功:{}",mkuId, updated);
                }else {
                    log.warn("updateMkuEsJdCategory 更新MKU ES的京东类目ID mkuId={}，更新ES结果失败:{}",mkuId, updated);
                }
            }
        } catch (Exception e) {
            log.error("updateMkuEsJdCategory 更新MKU ES的京东类目ID异常：mkuId={}",JSON.toJSONString(subList),e );
        }finally {
            log.info("updateMkuEsJdCategory 更新MKU ES的京东类目ID结束 count={},total={}", count, total);
        }
        return true;
    }

    public String updateProfitRateById(List<ProfitRateApiDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "参数不能为空";
        }

        for (ProfitRateApiDTO apiDTO : list) {
            ProfitRatePO profitRatePO = new ProfitRatePO();
            profitRatePO.setId(apiDTO.getId());
            profitRatePO.setSecondCatId(apiDTO.getSecondCatId());
            profitRatePO.setThirdCatId(apiDTO.getThirdCatId());
            profitRatePO.setLastCatId(apiDTO.getLastCatId());
            profitRatePO.setUpdater(apiDTO.getUpdater());
            profitRatePO.setUpdateTime(new Date());
            profitRateAtomicService.updateById(profitRatePO);
        }

        return "success";
    }



    /**
     * 更新类目和属性关系的京东类目ID
     * jdi_isc_category_attribute_sharding
     * @return
     */
    public String updateCategoryAttributePOJdCategory(String env,Set<Long> categoryIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<CategoryAttributePO> queryWrapper = Wrappers.lambdaQuery(CategoryAttributePO.class)
                .select(CategoryAttributePO::getId, CategoryAttributePO::getCatId, CategoryAttributePO::getUpdater)
                .isNotNull(CategoryAttributePO::getCatId).in(CollectionUtils.isNotEmpty(categoryIds), CategoryAttributePO::getCatId, categoryIds);
        List<CategoryAttributePO> list = categoryAttributeAtomicService.list(queryWrapper);
        int  total = list.size();


        Set<Long> catIds = list.stream().map(CategoryAttributePO::getCatId).collect(Collectors.toSet());
        LambdaQueryWrapper<CategoryPO> categoryQueryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId, CategoryPO::getJdCatId).in(CategoryPO::getId, catIds);
        List<CategoryPO> categoryPoList = categoryAtomicService.list(categoryQueryWrapper);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(categoryPO -> Objects.nonNull(categoryPO.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));

        List<List<CategoryAttributePO>> partitionList = Lists.partition(list, 100);
        int count = 1;
        for (List<CategoryAttributePO> subList : partitionList) {
            List<CategoryAttributePO> updatePlList = Lists.newArrayListWithExpectedSize(subList.size());
            try {
                for (CategoryAttributePO dbPO : subList) {
                    Long catId = dbPO.getCatId();
                    try {
                        String updater = dbPO.getUpdater();

                        Long jdCatId = cateAndJdCategoryMap.get(catId);
                        log.info("updateCategoryAttributePOJdCategory 更新类目和属性关系的京东类目ID 开始 catId={} jdCatId={} count={},total={}", catId, jdCatId, count, total);
                        if (Objects.isNull(jdCatId)) {
                            log.info("updateCategoryAttributePOJdCategory 更新类目和属性关系的京东类目ID 国际类目ID={},没有京东类目ID", catId);
                            count++;
                            continue;
                        }
                        CategoryAttributePO updatePo = new CategoryAttributePO();
                        updatePo.setId(dbPO.getId());
                        updatePo.setUpdater(updater);
                        updatePo.setUpdateTime(new Date());
                        updatePo.setJdCatId(jdCatId);
                        updatePlList.add(updatePo);
                        count++;
                        categoryAttributeAtomicService.updateById(updatePo);
                    } catch (Exception e) {
                        log.error("updateCategoryAttributePOJdCategory 更新类目和属性关系的京东类目ID 异常： catId={}",catId,e);
                    }finally {
                        log.info("更新类目和属性关系的京东类目ID 结束 catId={}, count={},total={}",catId,count,total);
                    }
                }
/*                if (CollectionUtils.isNotEmpty(updatePlList)) {
                    categoryAttributeAtomicService.updateBatchById(updatePlList);
                }*/
            } catch (Exception e) {
                log.info("updateCategoryAttributePOJdCategory 更新类目和属性关系的京东类目ID 异常：subList={}，类目Id={}",JSON.toJSONString(subList),e );
            }finally {
                log.info("updateCategoryAttributePOJdCategory  更新类目和属性关系的京东类目ID 结束 subList={}, count={},total={}",JSON.toJSONString(updatePlList),count,total);
            }
        }
        return "success";
    }


    /**
     * 更新sku客制化价格草稿表的京东类目ID
     * jdi_isc_customer_sku_price_draft_sharding
     * @return
     */
    public String updateCustomerSkuPriceDraftPOJdCategory(String env,Set<Long> categoryIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        LambdaQueryWrapper<CustomerSkuPriceDraftPO> queryWrapper = Wrappers.lambdaQuery(CustomerSkuPriceDraftPO.class)
                .select(CustomerSkuPriceDraftPO::getId, CustomerSkuPriceDraftPO::getCatId, CustomerSkuPriceDraftPO::getUpdater)
                .isNotNull(CustomerSkuPriceDraftPO::getCatId)
                .in(CollectionUtils.isNotEmpty(categoryIds), CustomerSkuPriceDraftPO::getCatId, categoryIds);
        List<CustomerSkuPriceDraftPO> list = customerSkuPriceDraftAtomicService.list(queryWrapper);
        int  total = list.size();


        Set<Long> catIds = list.stream().map(CustomerSkuPriceDraftPO::getCatId).collect(Collectors.toSet());
        List<CategoryPO> categoryPoList = getCategoryPOList(catIds);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "四级类目不存在";
        }

        Map<Long, RelationVO> catIdRealtionVoMap = this.getCatAndThirdCatIdRealtionVoMap(categoryPoList);

        // 国际四级类目和京东类目ID映射
        Map<Long, Long> cateAndJdCategoryMap = categoryPoList.stream().filter(categoryPO -> Objects.nonNull(categoryPO.getJdCatId())).collect(Collectors.toMap(CategoryPO::getId, CategoryPO::getJdCatId));

        List<List<CustomerSkuPriceDraftPO>> partitionList = Lists.partition(list, 100);
        int count = 1;
        for (List<CustomerSkuPriceDraftPO> subList : partitionList) {
            List<CustomerSkuPriceDraftPO> updatePlList = Lists.newArrayListWithExpectedSize(subList.size());
            try {
                for (CustomerSkuPriceDraftPO dbPO : subList) {
                    Long catId = dbPO.getCatId();
                    String updater = dbPO.getUpdater();

                    Long jdCatId = getLastJdCatId(cateAndJdCategoryMap, catId, catIdRealtionVoMap);
                    log.info("updateCustomerSkuPriceDraftPOJdCategory 更新sku客制化价格草稿表的京东类目ID 开始 catId={} jdCatId={} count={},total={}",catId,jdCatId,count,total);
                    if (Objects.isNull(jdCatId)) {
                        log.info("updateCustomerSkuPriceDraftPOJdCategory 更新sku客制化价格草稿表的京东类目ID 国际类目ID={},没有京东类目ID",catId);
                        count++;
                        continue;
                    }
                    CustomerSkuPriceDraftPO updatePo = new CustomerSkuPriceDraftPO();
                    updatePo.setId(dbPO.getId());
                    updatePo.setUpdater(updater);
                    updatePo.setUpdateTime(new Date());
                    updatePo.setJdCatId(jdCatId);
                    updatePlList.add(updatePo);
                    count++;
                }
                if (CollectionUtils.isNotEmpty(updatePlList)) {
                    customerSkuPriceDraftAtomicService.updateBatchById(updatePlList);
                }
            } catch (Exception e) {
                log.info("updateCustomerSkuPriceDraftPOJdCategory 更新sku客制化价格草稿表的京东类目ID 异常：subList={}，类目Id={}",JSON.toJSONString(subList),e );
            }finally {
                log.info("updateCustomerSkuPriceDraftPOJdCategory  更新sku客制化价格草稿表的京东类目ID 结束 subList={}, count={},total={}",JSON.toJSONString(updatePlList),count,total);
            }
        }
        return "success";
    }


    /**
     * 根据给定的类目ID集合更新对应的叶子节点信息。
     * @param env 环境参数，必须为uat_env。
     * @param leaf 叶子节点信息。
     * @param catIds 类目ID集合。
     * @return 更新结果，success表示更新成功，否则返回相应的错误信息。
     */
    public String updateCategoryLeafByCatIds(String env,Integer leaf,Set<Long> catIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }

        if (CollectionUtils.isEmpty(catIds)) {
            return "类目ID不能为空";
        }

        LambdaQueryWrapper<CategoryPO> categoryQueryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId, CategoryPO::getJdCatId,CategoryPO::getCatLevel,CategoryPO::getUpdater).in(CategoryPO::getId, catIds);
        List<CategoryPO> categoryPoList = categoryAtomicService.list(categoryQueryWrapper);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "类目不存在，catIds={}"+ JSON.toJSONString(catIds);
        }

        int total = categoryPoList.size();
        log.info("updateCategoryLeafByCatIds 更新类目叶子节点信息 开始 total={}",total);
        int count = 1;
        List<List<CategoryPO>> partitionList = Lists.partition(categoryPoList, 100);
        for (List<CategoryPO> sub : partitionList) {
            List<CategoryPO> updateList = Lists.newArrayListWithExpectedSize(sub.size());
            try {
                for (CategoryPO categoryPO : sub) {
                    log.info("updateCategoryLeafByCatIds 更新类目叶子节点信息 开始 类目ID={} level={} 当前={} total={}", categoryPO.getId(), categoryPO.getJdCatId(), count, total);
                    if (CategoryLevelEnum.ONE.getCode().equals(categoryPO.getCatLevel()) || CategoryLevelEnum.TWO.getCode().equals(categoryPO.getCatLevel())) {
                        log.info("updateCategoryLeafByCatIds 更新类目叶子节点信息 二级或一级不能修改 类目ID={} level={} 当前={} total={}", categoryPO.getId(), categoryPO.getCatLevel(), count, total);
                        count++;
                        continue;
                    }
                    CategoryPO updatePo = new CategoryPO();
                    updatePo.setId(categoryPO.getId());
                    updatePo.setLeafNodeFlag(BooleanUtils.toBoolean(leaf));
                    updatePo.setUpdater(categoryPO.getUpdater());
                    updatePo.setUpdateTime(new Date());
                    updateList.add(updatePo);
                    count++;
                }

                if (CollectionUtils.isNotEmpty(updateList)) {
                    categoryAtomicService.updateBatchById(updateList);
                }
            } catch (Exception e) {
                log.error("updateCategoryLeafByCatIds 更新类目叶子节点信息 异常 类目ID={} 当前={} total={}", JSON.toJSONString(updateList), count, total, e);
            }finally {
                log.info("updateCategoryLeafByCatIds 更新类目叶子节点信息 结束 类目ID={} 当前={} total={}", JSON.toJSONString(updateList), count, total);
            }
        }

        return "success";
    }



    /**
     * 根据类目ID集合更新类目有效标志
     * @param env 环境参数
     * @param yn 有效的标志
     * @param catIds 类目ID集合
     * @return 更新结果
     */
    public String updateCategoryYnByCatIds(String env,Integer yn,Set<Long> catIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }

        if (CollectionUtils.isEmpty(catIds)) {
            return "类目ID不能为空";
        }

        LambdaQueryWrapper<CategoryPO> categoryQueryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId, CategoryPO::getJdCatId,CategoryPO::getCatLevel,CategoryPO::getUpdater).in(CategoryPO::getId, catIds);
        List<CategoryPO> categoryPoList = categoryAtomicService.list(categoryQueryWrapper);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "类目不存在，catIds={}"+ JSON.toJSONString(catIds);
        }

        int total = categoryPoList.size();
        log.info("updateCategoryYnByCatIds 根据类目ID集合更新类目有效标志 开始 total={}",total);
        int count = 1;
        List<List<CategoryPO>> partitionList = Lists.partition(categoryPoList, 100);
        for (List<CategoryPO> sub : partitionList) {
            List<CategoryPO> updateList = Lists.newArrayListWithExpectedSize(sub.size());
            try {
                // 类目ID集合，用于查询类目多语言信息
                Set<Long> categoryIds = Sets.newHashSet();
                for (CategoryPO categoryPO : sub) {
                    log.info("updateCategoryYnByCatIds 根据类目ID集合更新类目有效标志 开始 类目ID={} level={} 当前={} total={}", categoryPO.getId(), categoryPO.getJdCatId(), count, total);
                    if (CategoryLevelEnum.ONE.getCode().equals(categoryPO.getCatLevel()) || CategoryLevelEnum.TWO.getCode().equals(categoryPO.getCatLevel())) {
                        log.info("updateCategoryYnByCatIds 根据类目ID集合更新类目有效标志 二级或一级不能修改 类目ID={} level={} 当前={} total={}", categoryPO.getId(), categoryPO.getCatLevel(), count, total);
                        count++;
                        continue;
                    }

                    categoryIds.add(categoryPO.getId());
                    CategoryPO updatePo = new CategoryPO();
                    updatePo.setId(categoryPO.getId());
                    updatePo.setYn(yn);
                    updatePo.setUpdater(categoryPO.getUpdater());
                    updatePo.setUpdateTime(new Date());
                    updateList.add(updatePo);
                    count++;
                }

                if (CollectionUtils.isNotEmpty(updateList)) {
                    categoryAtomicService.updateBatchById(updateList);
                }

                LambdaQueryWrapper<CategoryLangPO> lambdaQueryWrapper = Wrappers.lambdaQuery(CategoryLangPO.class).select(CategoryLangPO::getId, CategoryLangPO::getCatId, CategoryLangPO::getUpdater, CategoryLangPO::getYn).in(CategoryLangPO::getCatId, categoryIds);
                List<CategoryLangPO> categoryLangPOList = categoryLangAtomicService.list(lambdaQueryWrapper);
                if (CollectionUtils.isNotEmpty(categoryLangPOList)) {
                    log.info("updateCategoryYnByCatIds 根据类目ID集合更新类目有效标志 处理多语言数据 开始 categoryIds={} yn={} 当前={} total={}",JSON.toJSONString(categoryIds), yn, count, total);
                    List<CategoryLangPO> updateCategoryLangList = Lists.newArrayList();
                    for (CategoryLangPO langPO : categoryLangPOList) {
                        CategoryLangPO updateLangPO = new CategoryLangPO();
                        updateLangPO.setId(langPO.getId());
                        updateLangPO.setYn(yn);
                        updateLangPO.setUpdater(langPO.getUpdater());
                        updateLangPO.setUpdateTime(new Date());
                        updateCategoryLangList.add(updateLangPO);
                    }

                    if (CollectionUtils.isNotEmpty(updateCategoryLangList)) {
                        categoryLangAtomicService.updateBatchById(updateCategoryLangList);
                    }
                    log.info("updateCategoryYnByCatIds 根据类目ID集合更新类目有效标志 处理多语言数据 结束 categoryIds={} yn={} 当前={} total={}",JSON.toJSONString(categoryIds), yn, count, total);
                }
            } catch (Exception e) {
                log.error("updateCategoryYnByCatIds 根据类目ID集合更新类目有效标志 异常 类目ID={} 当前={} total={}", JSON.toJSONString(updateList), count, total, e);
            }finally {
                log.info("updateCategoryYnByCatIds 根据类目ID集合更新类目有效标志 结束 类目ID={} 当前={} total={}", JSON.toJSONString(updateList), count, total);
            }
        }

        return "success";
    }


    /**
     * 根据指定的类目ID集合更新类目定义的JD类目ID。
     * @param env 环境参数，必须为uat_env。
     * @param start 是否启用该功能，1表示启用，0表示禁用。
     * @param catIds 需要更新的类目ID集合。
     * @return 更新结果信息。
     */
    public String updateCategoryDefinitionJdCatIdByCatIds(String env,Long start,Set<Long> catIds){
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }

        if (CollectionUtils.isEmpty(catIds)) {
            return "类目ID不能为空";
        }

        if (Objects.isNull(start)) {
            start = 10000000L;
        }

        LambdaQueryWrapper<CategoryPO> categoryQueryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId, CategoryPO::getJdCatId,CategoryPO::getCatLevel,CategoryPO::getUpdater)
                .in(CategoryPO::getId, catIds).orderByAsc(CategoryPO::getId);
        List<CategoryPO> categoryPoList = categoryAtomicService.list(categoryQueryWrapper);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "类目不存在，catIds={}"+ JSON.toJSONString(catIds);
        }

        int total = categoryPoList.size();
        int count = 1;
        for (CategoryPO categoryPO : categoryPoList) {
            CategoryPO updatePo = new CategoryPO();
            try {
                updatePo.setId(categoryPO.getId());
                updatePo.setJdCatId(start);
                updatePo.setUpdater(categoryPO.getUpdater());
                updatePo.setUpdateTime(new Date());
                categoryAtomicService.updateById(updatePo);
                log.info("updateCategoryDefinitionJdCatIdByCatIds 根据类目ID集合更新类目定义的JD类目ID 当前={} total={} start={}", count, total, start);
                count++;
                start++;
            } catch (Exception e) {
                log.error("updateCategoryDefinitionJdCatIdByCatIds 根据类目ID集合更新类目定义的JD类目ID 异常 入参:{}",JSON.toJSONString(categoryPO),e);
            }
        }
        return "success";
    }


    /**
     * 根据指定的类目ID集合更新类目定义的父级JD类目ID。
     * @param env 环境参数，必须为uat_env。
     * @param catIds 需要更新的类目ID集合。
     * @return 更新结果信息。
     */
    public String updateCategoryDefinitionJdParentCatIdByCatIds(String env,Set<Long> catIds){
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }

        if (CollectionUtils.isEmpty(catIds)) {
            return "类目ID不能为空";
        }


        LambdaQueryWrapper<CategoryPO> categoryQueryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId,CategoryPO::getParentCatId, CategoryPO::getJdCatId,CategoryPO::getCatLevel,CategoryPO::getUpdater)
                .in(CategoryPO::getId, catIds).orderByAsc(CategoryPO::getId);
        List<CategoryPO> categoryPoList = categoryAtomicService.list(categoryQueryWrapper);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "类目不存在，catIds={}"+ JSON.toJSONString(catIds);
        }

        int total = categoryPoList.size();
        int count = 1;
        for (CategoryPO categoryPO : categoryPoList) {
            CategoryPO updatePo = new CategoryPO();
            try {
                updatePo.setId(categoryPO.getId());
                if (categoryPO.getCatLevel() == 1) {
                    updatePo.setJdParentCatId(0L);
                } else {
                    // 查询父级类目信息，替换父级类目ID
                    CategoryPO parentCategoryPo = categoryAtomicService.getOne(new LambdaQueryWrapper<CategoryPO>().eq(CategoryPO::getId, categoryPO.getParentCatId()));
                    updatePo.setJdParentCatId(parentCategoryPo.getJdCatId());
                }
                updatePo.setUpdater(categoryPO.getUpdater());
                updatePo.setUpdateTime(new Date());
                categoryAtomicService.updateById(updatePo);
                log.info("updateCategoryDefinitionJdParentCatIdByCatIds 根据指定的类目ID集合更新类目定义的父级JD类目ID 当前={} total={}", count, total);
                count++;
            } catch (Exception e) {
                log.error("updateCategoryDefinitionJdParentCatIdByCatIds 根据指定的类目ID集合更新类目定义的父级JD类目ID 异常 入参:{}",JSON.toJSONString(categoryPO),e);
            }
        }
        return "success";
    }



    /**
     * 根据指定的类目ID集合更新类目定义的父级JD类目ID。
     * @param env 环境参数，必须为uat_env。
     * @param catIds 类目ID集合，不能为null或空。
     * @return 更新结果，success表示更新成功，否则返回错误信息。
     */
    public String updateCategoryDefaultSaleAttribute(String env,Set<Long> catIds){
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }

        LambdaQueryWrapper<CategoryPO> categoryQueryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId,CategoryPO::getParentCatId, CategoryPO::getJdCatId,CategoryPO::getStatus,CategoryPO::getLeafNodeFlag, CategoryPO::getCatLevel,CategoryPO::getUpdater)
                .eq(CategoryPO::getYn, YnEnum.YES.getCode())
                .eq(CategoryPO::getLeafNodeFlag, TreeLeafEnum.Y.getCode())
                .in(CollectionUtils.isNotEmpty(catIds), CategoryPO::getId, catIds).orderByAsc(CategoryPO::getId);
        List<CategoryPO> categoryPoList = categoryAtomicService.list(categoryQueryWrapper);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "类目不存在，catIds="+ JSON.toJSONString(catIds);
        }

        int total = categoryPoList.size();
        int count = 1;
        for (CategoryPO categoryPO : categoryPoList) {

            List<CategoryAttributePO> list = categoryAttributeAtomicService.list(Wrappers.lambdaQuery(CategoryAttributePO.class).eq(CategoryAttributePO::getCatId, categoryPO.getId()).eq(CategoryAttributePO::getAttributeId, 100100L));
            if (CollectionUtils.isNotEmpty(list)) {
                log.info("updateCategoryDefaultSaleAttribute 根据指定的类目ID集合更新类目定义的父级JD类目ID 已经存在类目 当前={} total={}", count, total);
                CategoryAttributePO categoryAttributePO = list.get(0);
                if (YnEnum.NO.getCode().equals(categoryAttributePO.getYn())) {
                    LambdaUpdateWrapper<CategoryAttributePO> updateWrapper = Wrappers.lambdaUpdate(CategoryAttributePO.class)
                            .set(CategoryAttributePO::getYn, YnEnum.YES.getCode())
                            .set(CategoryAttributePO::getUpdater, Constant.SYSTEM)
                            .set(CategoryAttributePO::getUpdateTime, new Date())
                            .eq(CategoryAttributePO::getId, categoryAttributePO.getId());
                    categoryAttributeAtomicService.update(updateWrapper);
                }
                count++;
                continue;
            }

            CategoryAttributePO updatePo = new CategoryAttributePO();
            try {
                updatePo.setCatId(categoryPO.getId());
                updatePo.setJdCatId(categoryPO.getJdCatId());
                updatePo.setAttributeId(100100L);
                updatePo.setCateAttributeType(1);
                updatePo.setSort(Constant.ATTRIBUTE_VAL_OTHER_SORT);
                updatePo.setRequired(0);
                updatePo.setStatus(1);
                updatePo.setCreator(Constant.SYSTEM);
                updatePo.setUpdater(Constant.SYSTEM);
                Date date = new Date();
                updatePo.setCreateTime(date);
                updatePo.setUpdateTime(date);
                categoryAttributeAtomicService.saveOrUpdate(updatePo);
                log.info("updateCategoryDefaultSaleAttribute 根据指定的类目ID集合更新类目定义的父级JD类目ID 当前={} total={}", count, total);
                count++;
            } catch (Exception e) {
                log.error("updateCategoryDefaultSaleAttribute 根据指定的类目ID集合更新类目定义的父级JD类目ID 异常 入参:{}",JSON.toJSONString(categoryPO),e);
            }
        }
        return "success";
    }



    /**
     * 根据给定的环境和业务线ID集合，移除重复的业务线，并更新其对应的京东类目ID。
     * jdi_isc_business_line_relation_sharding
     * @param env 环境参数，不能为空且必须为uat环境。
     * @param businessLineIds 业务线ID集合，不能为空。
     * @return 如果操作成功，返回"success"；否则返回错误信息。
     */
    public String removeRepeatBusinessLine(String env,Set<Long> businessLineIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }

        List<BusinessLineRelationPO> businessLineRelationPOList = businessLineRelationAtomicService.selectDuplicateRelations(businessLineIds);
        int  total = businessLineRelationPOList.size();
        int count = 1;

        for (BusinessLineRelationPO po : businessLineRelationPOList) {
            log.info("removeRepeatBusinessLine 移除重复的业务线开始 supplierCode={} jdCatId={},branId={} count={},total={}",po.getSupplierCode(),po.getJdCatId(),po.getBrandId(),count,total);
            try {
                LambdaQueryWrapper<BusinessLineRelationPO> lambdaQueryWrapper = Wrappers.lambdaQuery(BusinessLineRelationPO.class)
                        .eq(BusinessLineRelationPO::getSupplierCode, po.getSupplierCode())
                        .eq(BusinessLineRelationPO::getJdCatId, po.getJdCatId())
                        .eq(BusinessLineRelationPO::getBrandId, po.getBrandId())
                        .eq(BusinessLineRelationPO::getYn, YnEnum.YES.getCode())
                        .orderByDesc(BusinessLineRelationPO::getCreateTime);
                List<BusinessLineRelationPO> lineRelationPOList = businessLineRelationAtomicService.list(lambdaQueryWrapper);

                if (CollectionUtils.isNotEmpty(lineRelationPOList) && lineRelationPOList.size() > 1) {
                    int size = lineRelationPOList.size();
                    // 保留第一条，其他数据都更新成yn=0
                    List<BusinessLineRelationPO> removeList = Lists.newArrayList();
                    for (int i = 1; i < size; i++) {
                        BusinessLineRelationPO removePo = lineRelationPOList.get(i);
                        BusinessLineRelationPO updateMkuPo = new BusinessLineRelationPO();
                        updateMkuPo.setId(removePo.getId());
                        updateMkuPo.setUpdater(Constant.SYSTEM+"removeRepeatBusinessLine");
                        updateMkuPo.setUpdateTime(System.currentTimeMillis());
                        updateMkuPo.setYn(0);
                        removeList.add(updateMkuPo);
                    }

                    log.info("removeRepeatBusinessLine 移除重复的业务线开始 supplierCode={} jdCatId={},branId={}  count={},total={},removeList={}",po.getSupplierCode(),po.getJdCatId(),po.getBrandId(),count,total,JSON.toJSONString(removeList));
                    if (CollectionUtils.isNotEmpty(removeList)) {
                        System.out.println("removeRepeatBusinessLine removeList={} "+JSON.toJSONString(removeList));
                        businessLineRelationAtomicService.updateBatchById(removeList);
                    }
                }
            } catch (Exception e) {
                log.info("updateBusinessLineJdCategory 移除重复的业务线开始异常：businessRelation={}，",JSON.toJSONString(po),e );

            }finally {
                log.info("updateBusinessLineJdCategory 移除重复的业务线开始结束 businessRelation={}, count={},total={}",JSON.toJSONString(po),count,total);
                count++;
            }
        }
        return "success";
    }


    /**
     * 从GMS更新指定环境下的商品分类名称。
     * @param env 环境标识，例如"dev"、"test"或"prod"。
     * @param catIds 需要更新的分类ID集合。
     * @return 更新成功的商品分类数量。
     */
    public String updateCategoryNameFromGms(String env,Set<Long> catIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }

        LambdaQueryWrapper<CategoryPO> categoryQueryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId,CategoryPO::getParentCatId, CategoryPO::getJdCatId,CategoryPO::getStatus,CategoryPO::getLeafNodeFlag, CategoryPO::getCatLevel,CategoryPO::getUpdater)
                .eq(CategoryPO::getYn, YnEnum.YES.getCode())
                .in(CollectionUtils.isNotEmpty(catIds), CategoryPO::getId, catIds).orderByAsc(CategoryPO::getId);
        List<CategoryPO> categoryPoList = categoryAtomicService.list(categoryQueryWrapper);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "类目不存在，catIds={}"+ JSON.toJSONString(catIds);
        }

        int total = categoryPoList.size();
        int count = 1;
        List<List<CategoryPO>> categoryPoListList = Lists.partition(categoryPoList, 100);

        int size = categoryPoListList.size();
        int pageNum = 1;
        for (List<CategoryPO> sub : categoryPoListList) {
            // 京东类目ID集合
            Set<Long> jdCatIds = sub.stream().map(CategoryPO::getJdCatId).filter(Objects::nonNull).filter(jdCatId -> jdCatId < 10000000).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(jdCatIds)) {
                continue;
            }


            // 查询类目多语言
            Map<Long, CategoryLangPO> categoryLangMap = categoryLangAtomicService.queryIdPoMap(jdCatIds, LangConstant.LANG_ZH);

            GetCategoryByIdsParam param = new GetCategoryByIdsParam();
            param.setCategoryIds(jdCatIds.stream().map(Long::intValue).collect(Collectors.toSet()));
            param.setLanguage(Language.zh_CN);
            Map<Integer, Category> categoryMap = categoryRpcService.getCategoryByIds(param);

            if (MapUtils.isEmpty(categoryMap)) {
                log.error("updateCategoryNameFromGms 更新类目名称，中台类目不存在 param={}", JSON.toJSONString(param));
                continue;
            }
            // 更新类目名称
            List<CategoryLangPO> updateCategoryLangList = Lists.newArrayList();
            for (Map.Entry<Long, CategoryLangPO> entry : categoryLangMap.entrySet()) {
                Long jdCatId = entry.getKey();
                CategoryLangPO categoryLangPO = categoryLangMap.get(jdCatId);
                log.info("updateCategoryNameFromGms 更新类目名称 第{}批/{}总数 开始 类目ID={},类目名称={} 第{}个/{}总数",pageNum,size, jdCatId,categoryLangPO.getLangName(),count,total );
                Category category = categoryMap.get(jdCatId.intValue());
                if (Objects.nonNull(category)
                        && !categoryLangPO.getLangName().equals(category.getCategoryName())) {
                    CategoryLangPO updatePo = new CategoryLangPO();
                    updatePo.setId(categoryLangPO.getId());
                    updatePo.setUpdater(Constant.SYSTEM);
                    updatePo.setUpdateTime(new Date());
                    updatePo.setLangName(category.getCategoryName());
                    updateCategoryLangList.add(updatePo);
                }
                log.info("updateCategoryNameFromGms 更新类目名称 第{}批/{}总数 结束 类目ID={},类目名称={} 第{}个/{}",pageNum,size, jdCatId,categoryLangPO.getLangName(),count,total );
                count++;
            }

            if (CollectionUtils.isNotEmpty(updateCategoryLangList)) {
                categoryLangAtomicService.updateBatchById(updateCategoryLangList);
            }
            log.info("updateCategoryNameFromGms 更新类目名称 第{}批/{}总数 结束",pageNum,size);
            pageNum++;
        }
        return "success";
    }



    /**
     * 从GMS更新指定环境下的子类别信息。
     * @param env 环境名称，例如"uat"。
     * @param catIds 需要更新的子类别ID集合。
     * @return 更新成功的子类别数量。
     */
    public String updateSonCategoryFromGms(String env,Set<Long> catIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }

        // 查询一级类目集合
        LambdaQueryWrapper<CategoryPO> categoryQueryWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId,CategoryPO::getParentCatId, CategoryPO::getJdCatId,CategoryPO::getStatus,CategoryPO::getLeafNodeFlag, CategoryPO::getCatLevel,CategoryPO::getUpdater)
                .eq(CategoryPO::getYn, YnEnum.YES.getCode())
                .eq(CategoryPO::getCatLevel,CategoryLevelEnum.ONE.getCode())
                .in(CollectionUtils.isNotEmpty(catIds), CategoryPO::getId, catIds).orderByAsc(CategoryPO::getId);
        List<CategoryPO> categoryPoList = categoryAtomicService.list(categoryQueryWrapper);
        if (CollectionUtils.isEmpty(categoryPoList)) {
            return "类目不存在，catIds={}"+ JSON.toJSONString(catIds);
        }

        int total = categoryPoList.size();
        int count = 1;
        for (CategoryPO categoryPO : categoryPoList) {
            int sonCategoryCount = 0;
            Long jdCatId = categoryPO.getJdCatId();
            log.info("updateSonCategoryFromGms 拉取主站子级类目信息 开始 一级类目ID={} 第{}个/{}总数 新增类目数量={}",jdCatId,count,total,sonCategoryCount);
            // 查询国际二级类目集合
            Map<Long, CategoryPO> twoCategoryPoMap = getIscSonCategory(categoryPO.getJdCatId(), CategoryLevelEnum.TWO);
            // 查询主站子类目关系
            List<Category> twoRelationCategoryList = getGmsSonCategories(jdCatId);
            if (CollectionUtils.isEmpty(twoRelationCategoryList)) {
                count++;
                continue;
            }


            // 处理主站二级类目数据
            for (Category twoCat : twoRelationCategoryList) {
                log.info("updateSonCategoryFromGms 拉取主站子级类目信息 开始处理二级类目 一级类目ID={} 二级类目ID={} 第{}个/{}总数 新增类目数量={}",jdCatId,twoCat.getCategoryId(),count,total,sonCategoryCount );
                CategoryPO twoCategoryPO = twoCategoryPoMap.get(twoCat.getCategoryId().longValue());
                if (!twoCategoryPoMap.containsKey(twoCat.getCategoryId().longValue())) {
                    // 不存在的主站二级类目ID，插入类目表
                    twoCategoryPO = insertGmsCategory(categoryPO, twoCat.getCategoryId().longValue(), CategoryLevelEnum.TWO, twoCat);
                    sonCategoryCount++;
                }

                // 查询三级类目ID集合
                Map<Long, CategoryPO> threeCategoryPoMap = getIscSonCategory(twoCat.getCategoryId().longValue(), CategoryLevelEnum.THREE);

                List<Category> threeRelationCategoryList = getGmsSonCategories(twoCat.getCategoryId().longValue());
                if (CollectionUtils.isEmpty(threeRelationCategoryList)) {
                    continue;
                }

                for (Category threeCat : threeRelationCategoryList) {
                    log.info("updateSonCategoryFromGms 拉取主站子级类目信息 开始处理三级类目 一级类目ID={} 二级类目ID={} 三级类目ID={} 第{}个/{}总数 新增类目数量={}",jdCatId,twoCat.getCategoryId(),threeCat.getCategoryId(),count,total,sonCategoryCount );
                    CategoryPO threeCategoryPO = threeCategoryPoMap.get(threeCat.getCategoryId().longValue());
                    if (!threeCategoryPoMap.containsKey(threeCat.getCategoryId().longValue())) {
                        // 不存在的主站三级类目ID，插入类目表
                        threeCategoryPO =  insertGmsCategory(twoCategoryPO,threeCat.getCategoryId().longValue(),CategoryLevelEnum.THREE,threeCat);
                        sonCategoryCount++;
                    }
                    // 查询四级类目ID集合
                    Map<Long, CategoryPO> fourCategoryPoMap = getIscSonCategory(threeCat.getCategoryId().longValue(), CategoryLevelEnum.FOUR);
                    // 查询主站四级类目
                    List<Category> fourRelationCategoryList = getGmsSonCategories(threeCat.getCategoryId().longValue());

                    if (CollectionUtils.isEmpty(fourRelationCategoryList)) {
                        continue;
                    }

                    for (Category fourCat : fourRelationCategoryList) {
                        log.info("updateSonCategoryFromGms 拉取主站子级类目信息 开始处理四级类目 一级类目ID={} 二级类目ID={} 三级类目ID={} 四级类目ID={} 第{}个/{}总数 新增类目数量={}",jdCatId,twoCat.getCategoryId(),threeCat.getCategoryId(),fourCat.getCategoryId(),count,total ,sonCategoryCount);

                        if (!fourCategoryPoMap.containsKey(fourCat.getCategoryId().longValue())) {
                            // 不存在的主站四级类目ID，插入类目表
                            insertGmsCategory(threeCategoryPO,fourCat.getCategoryId().longValue(),CategoryLevelEnum.FOUR,fourCat);
                            sonCategoryCount++;
                        }
                        log.info("updateSonCategoryFromGms 拉取主站子级类目信息 结束处理四级类目 一级类目ID={} 二级类目ID={} 三级类目ID={} 四级类目ID={} 第{}个/{}总数 新增类目数量={}",jdCatId,twoCat.getCategoryId(),threeCat.getCategoryId(),fourCat.getCategoryId(),count,total ,sonCategoryCount);
                    }
                    log.info("updateSonCategoryFromGms 拉取主站子级类目信息 结束处理三级类目 一级类目ID={} 二级类目ID={} 三级类目ID={} 第{}个/{}总数 新增类目数量={}",jdCatId,twoCat.getCategoryId(),threeCat.getCategoryId(),count,total ,sonCategoryCount);
                }
                log.info("updateSonCategoryFromGms 拉取主站子级类目信息 结束处理二级类目 一级类目ID={} 二级类目ID={} 第{}个/{}总数 新增类目数量={}",jdCatId,twoCat.getCategoryId(),count,total, sonCategoryCount);
            }
            log.info("updateSonCategoryFromGms 拉取主站子级类目信息 结束 一级类目ID={} 第{}个/{}总数,新增类目数量={}",jdCatId,count,total ,sonCategoryCount);
            count++;
        }

        return "success";
    }

    private CategoryPO insertGmsCategory(CategoryPO parentCategoryPO,Long jdCatId,CategoryLevelEnum categoryLevelEnum,Category category){
        // 插入当前类目和多语言信息
        CategoryPO categoryPO = buildInsertCategoryPO(parentCategoryPO, jdCatId,categoryLevelEnum);
        boolean insertCategory = categoryAtomicService.saveOrUpdate(categoryPO);
        if (!insertCategory) {
            throw new BizException("新增京东类目失败 类目ID:"+  jdCatId);
        }

        // 翻译多语言
        List<String> langCodeList = getLangCodeList();
        DataResponse<Map<String, String>> response = translateMultiLang(category.getCategoryName(), Sets.newHashSet(langCodeList));
        // 翻译类目名称
        if (response.getSuccess() && MapUtils.isNotEmpty(response.getData())) {
            // 插入多语言信息
            langCodeList.add(LangConstant.LANG_ZH);
            List<CategoryLangPO> insertCategoryLangPoList = getInsertCategoryLangPoList(category.getCategoryName(), response,langCodeList,categoryPO.getId(),jdCatId);
            if (CollectionUtils.isNotEmpty(insertCategoryLangPoList)) {
                categoryLangAtomicService.saveOrUpdateBatch(insertCategoryLangPoList);
            }
        }else {
            log.warn("updateSonCategoryFromGms---insertGmsCategory 类目名称翻译失败,catId={},changeDTO={},response={}", jdCatId, JSON.toJSONString(parentCategoryPO), JSON.toJSONString(response));
        }
        return categoryPO;
    }

    private  List<CategoryLangPO> getInsertCategoryLangPoList(String categoryName, DataResponse<Map<String, String>> response,List<String> langSet,Long catId,Long jdCatId) {
        Map<String, String> translatedMap = response.getData();
        List<CategoryLangPO> insertCategoryLangPoList = Lists.newArrayListWithExpectedSize(langSet.size());
        for (String lang : langSet) {
            String catName = LangConstant.LANG_ZH.equals(lang) ? categoryName : translatedMap.getOrDefault(lang,categoryName);
            CategoryLangPO updatePo = this.getInsertCategoryLangPO(catId, lang, catName, jdCatId);
            insertCategoryLangPoList.add(updatePo);
        }
        return insertCategoryLangPoList;
    }

    private CategoryLangPO getInsertCategoryLangPO(Long id,String lang,String catName,Long jdCatId) {
        CategoryLangPO insertPo = new CategoryLangPO();
        insertPo.setCatId(id);
        insertPo.setJdCatId(jdCatId);
        insertPo.setLang(lang);
        insertPo.setLangName(catName);
        insertPo.setCreator(Constant.SYSTEM+ "-insertGmsSonCategory");
        insertPo.setCreateTime(new Date());
        insertPo.setUpdater(Constant.SYSTEM+ "-insertGmsSonCategory");
        insertPo.setUpdateTime(new Date());
        insertPo.setYn(YnEnum.YES.getCode());
        insertPo.setPreYn(YnEnum.YES.getCode());
        return insertPo;
    }

    private DataResponse<Map<String, String>> translateMultiLang(String catName,Set<String> langSet) {
        MultiTranslateReqDTO translateReqDTO = new MultiTranslateReqDTO();
        translateReqDTO.setText(catName);
        translateReqDTO.setFrom(LangConstant.LANG_ZH);
        translateReqDTO.setToLangList(langSet.stream().filter(lang -> !LangConstant.LANG_ZH.equals(lang)).collect(Collectors.toList()));
        return textTranslateManageService.translateMultiLangText(translateReqDTO);
    }

    private @NotNull CategoryPO buildInsertCategoryPO(CategoryPO parentCategoryPO,Long jdCatId,CategoryLevelEnum categoryLevelEnum) {
        CategoryPO categoryPO = new CategoryPO();
        categoryPO.setParentCatId(parentCategoryPO.getId());
        categoryPO.setJdParentCatId(parentCategoryPO.getJdCatId());
        categoryPO.setJdCatId(jdCatId);
        categoryPO.setSort(Constant.ATTRIBUTE_VAL_OTHER_SORT);
        categoryPO.setCatLevel(categoryLevelEnum.getCode());
        Date date = new Date();
        categoryPO.setCreateTime(date);
        categoryPO.setUpdateTime(date);
        String updater = Constant.SYSTEM + "-insertGmsSonCategory";
        categoryPO.setCreator(updater);
        categoryPO.setUpdater(updater);
        categoryPO.setYn(YnEnum.YES.getCode());
        categoryPO.setStatus(StatusEnum.FORBIDDEN.getCode());
        // 末级标识需要根据子级数量判断
        if (CategoryLevelEnum.TWO.equals(categoryLevelEnum)){
            categoryPO.setLeafNodeFlag(Boolean.FALSE);
        } else if (CategoryLevelEnum.THREE.equals(categoryLevelEnum)) {
            // 查询是否存在子级类判断末级
            categoryPO.setLeafNodeFlag(this.isLeafNode(jdCatId));
        }else if (CategoryLevelEnum.FOUR.equals(categoryLevelEnum)){
            categoryPO.setLeafNodeFlag(Boolean.TRUE);
        }
        return categoryPO;
    }

    private List<String> getLangCodeList() {
        List<String> langCodeList = langManageService.getLangCodeList();
        langCodeList.remove(LangConstant.LANG_ZH);
        return langCodeList;
    }

    private boolean isLeafNode(Long jdCatId) {
        GetRelationParam relationParam = new GetRelationParam();
        relationParam.setCategoryId(jdCatId.intValue());
        relationParam.setRelation(GetRelationParam.Relation.GET_SON);
        List<Category> relation = categoryRpcService.getRelation(relationParam);
        // 没有子级，返回true 有子级返回false
        return CollectionUtils.isEmpty(relation);
    }


    private List<Category> getGmsSonCategories(Long jdCatId) {
        GetRelationParam relationParam = new GetRelationParam();
        relationParam.setCategoryId(jdCatId.intValue());
        relationParam.setRelation(GetRelationParam.Relation.GET_SON);
        relationParam.setLanguage(Language.zh_CN);
        List<Category> twoRelationCategoryList = categoryRpcService.getRelation(relationParam);
        return twoRelationCategoryList;
    }

    private @NotNull Map<Long,CategoryPO> getIscSonCategory(Long jdCatId,CategoryLevelEnum catLevel) {
        LambdaQueryWrapper<CategoryPO> queryTwoWrapper = Wrappers.lambdaQuery(CategoryPO.class).select(CategoryPO::getId,CategoryPO::getParentCatId, CategoryPO::getJdCatId,CategoryPO::getStatus,CategoryPO::getLeafNodeFlag, CategoryPO::getCatLevel,CategoryPO::getUpdater)
                .eq(CategoryPO::getYn, YnEnum.YES.getCode())
                .eq(CategoryPO::getCatLevel,catLevel.getCode())
                .eq(CategoryPO::getJdParentCatId,jdCatId);
        List<CategoryPO> twoCategoryPoList = categoryAtomicService.list(queryTwoWrapper);

        return twoCategoryPoList.stream().collect(Collectors.toMap(CategoryPO::getJdCatId, Function.identity()));
    }

    public String jadeTestExecutor(String env){
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            return "环境参数不能为空，或者不正确";
        }
        for (int i = 0; i < 100; i++) {
            try {
                int finalI = i;
                CompletableFuture.runAsync(() -> {
                    log.error("jadeTestExecutor 测试动态线程池 i={}", finalI);
                    GetRelationParam relationParam = new GetRelationParam();
                    relationParam.setCategoryId(737);
                    relationParam.setRelation(GetRelationParam.Relation.GET_SON);
                    List<Category> relation = categoryRpcService.getRelation(relationParam);
                    log.info("jadeTestExecutor 查询子类目={}",JSON.toJSONString(relation));
                });
                Thread.sleep(500);
            }catch (Exception e){
                log.error("jadeTestExecutor 测试动态线程池异常",e);
            }
        }

        return "success";
    }


    @Data
    public class RelationVO{
        private Long catId;
        private Long jdFourCatId;
        private Long threeCatId;
        private boolean isLeafNode = false;

        public RelationVO(CategoryPO categoryPO) {
            this.catId = categoryPO.getId();
            this.jdFourCatId = categoryPO.getJdCatId();
            this.threeCatId = categoryPO.getJdParentCatId();
        }
    }
}
