package com.jdi.isc.product.soa.service.protocol.jsf.taxRate;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.taxRate.IscTaxRateWriteApiService;
import com.jdi.isc.product.soa.api.taxRate.req.TaxRateReqDTO;
import com.jdi.isc.product.soa.api.taxRate.res.TaxRateApiDTO;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRateVO;
import com.jdi.isc.product.soa.service.manage.taxRate.TaxRateManageService;
import com.jdi.isc.product.soa.service.mapstruct.taxRate.TaxRateConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class IscTaxRateWriteApiServiceImpl implements IscTaxRateWriteApiService {
    @Resource(name="taxRateManageServiceImpl")
    private TaxRateManageService taxRateManageService;
    @Override
    public DataResponse<TaxRateApiDTO> saveOrUpdate(TaxRateReqDTO dto) {
        TaxRateVO taxRateVO = TaxRateConvert.INSTANCE.dto2Vo(dto);
        DataResponse<TaxRateVO> response = taxRateManageService.saveOrUpdate(taxRateVO);

        TaxRateApiDTO taxRateApiDTO = TaxRateConvert.INSTANCE.vo2ApiDto(response.getData());
        return DataResponse.success(taxRateApiDTO);
    }

    @Override
    public DataResponse<Boolean> delete(TaxRateReqDTO dto) {
        TaxRateVO taxRateVO = TaxRateConvert.INSTANCE.dto2Vo(dto);
        taxRateVO.setCreator(dto.getPin());
        taxRateVO.setUpdater(dto.getPin());
        boolean result = taxRateManageService.delete(taxRateVO);
        if(result){
            return DataResponse.success(true);
        }
        log.info("IscTaxRateWriteApiServiceImpl.delete req:{}，res:{}", JSON.toJSONString(dto), JSON.toJSONString(result));
        return DataResponse.error("删除失败");
    }

}
