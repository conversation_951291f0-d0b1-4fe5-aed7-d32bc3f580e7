package com.jdi.isc.product.soa.service.atomic.category;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.SystemContextHolder;
import com.jdi.isc.product.soa.domain.category.biz.GlobalAttributeVO;
import com.jdi.isc.product.soa.domain.category.biz.GlobalAttributePageVO;
import com.jdi.isc.product.soa.domain.category.po.GlobalAttributePO;
import com.jdi.isc.product.soa.repository.jed.mapper.category.GlobalAttributeBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 国际跨境属性原子服务
 * @Author: taxuezheng1
 * @Date: 2024/07/12 13:17
 **/
@Slf4j
@Service
public class GlobalAttributeAtomicService extends ServiceImpl<GlobalAttributeBaseMapper, GlobalAttributePO> {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    public List<GlobalAttributePageVO.Response> pageSearch(GlobalAttributePageVO.Request input){
        input.setOffset((input.getIndex()-1)*input.getSize());
        return super.getBaseMapper().pageSearch(input);
    }

    /**
     * 分页查询的列表总数
     * @param input 查询条件
     * @return 总条数
     */
    public long pageSearchTotal(GlobalAttributePageVO.Request input){
        return super.getBaseMapper().pageSearchTotal(input);
    }

    /**
     * 未删除的对象
     * @param id id
     * @return 对象
     */
    public GlobalAttributePO getValidById(Long id){
        LambdaQueryWrapper<GlobalAttributePO> wrapper = Wrappers.<GlobalAttributePO>lambdaQuery()
                .eq(GlobalAttributePO::getId, id)
                .eq(GlobalAttributePO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }


    public List<GlobalAttributePO> getByIds(Collection<Long> ids){
        LambdaQueryWrapper<GlobalAttributePO> wrapper = Wrappers.<GlobalAttributePO>lambdaQuery()
                .in(GlobalAttributePO::getId, ids)
                .eq(GlobalAttributePO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }
    @PFTracing
    public List<GlobalAttributePO> getValidStatusByIds(Collection<Long> ids,Integer dimension,Integer checkType){
        if(CollectionUtils.isEmpty(ids)){
            return null;
        }
        LambdaQueryWrapper<GlobalAttributePO> wrapper = Wrappers.<GlobalAttributePO>lambdaQuery()
                .in(GlobalAttributePO::getId, ids)
                .eq(dimension != null,GlobalAttributePO::getAttributeDimension,dimension)
                .eq(checkType != null,GlobalAttributePO::getCheckType,checkType)
                .eq(StringUtils.equals(Constant.VC_SYSTEM_CODE,SystemContextHolder.get())
                        ,GlobalAttributePO::getShowSupplier, YnEnum.YES.getCode())
                .eq(GlobalAttributePO::getValidStatus, YnEnum.YES.getCode())
                .eq(GlobalAttributePO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    public List<GlobalAttributePO> listByCondition(GlobalAttributePageVO.Request condition){
        LambdaQueryWrapper<GlobalAttributePO> wrapper = Wrappers.<GlobalAttributePO>lambdaQuery()
                .eq(Objects.nonNull(condition.getAttributeDimension()),GlobalAttributePO::getAttributeDimension,condition.getAttributeDimension())
                .in(CollectionUtils.isNotEmpty(condition.getInputTypeList()),GlobalAttributePO::getAttributeInputType,condition.getInputTypeList())
                .eq(GlobalAttributePO::getValidStatus, YnEnum.YES.getCode())
                .eq(GlobalAttributePO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

}
