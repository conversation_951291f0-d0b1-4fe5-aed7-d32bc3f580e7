package com.jdi.isc.product.soa.service.mapstruct.saleAttribute;

import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeValueLangVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeValueVO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributePO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeValueLangPO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeValuePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 销售属性MapStruct转换接口
 * <AUTHOR>
 * @date 2025-06-26
 */
@Mapper
public interface SaleAttributeConvert {

    SaleAttributeConvert INSTANCE = Mappers.getMapper(SaleAttributeConvert.class);

    /**
     * SaleAttributePO转SaleAttributeVO
     * @param saleAttributePO PO对象
     * @return VO对象
     */
    SaleAttributeVO toVO(SaleAttributePO saleAttributePO);

    /**
     * SaleAttributeVO转SaleAttributePO
     * @param saleAttributeVO VO对象
     * @return PO对象
     */
    SaleAttributePO toPO(SaleAttributeVO saleAttributeVO);

    /**
     * SaleAttributePO列表转SaleAttributeVO列表
     * @param saleAttributePOList PO列表
     * @return VO列表
     */
    List<SaleAttributeVO> toVOList(List<SaleAttributePO> saleAttributePOList);

    /**
     * SaleAttributeVO列表转SaleAttributePO列表
     * @param saleAttributeVOList VO列表
     * @return PO列表
     */
    List<SaleAttributePO> toPOList(List<SaleAttributeVO> saleAttributeVOList);

    /**
     * SaleAttributeValuePO转SaleAttributeValueVO
     * @param saleAttributeValuePO PO对象
     * @return VO对象
     */
    SaleAttributeValueVO toValueVO(SaleAttributeValuePO saleAttributeValuePO);

    /**
     * SaleAttributeValueVO转SaleAttributeValuePO
     * @param saleAttributeValueVO VO对象
     * @return PO对象
     */
    SaleAttributeValuePO toValuePO(SaleAttributeValueVO saleAttributeValueVO);

    SaleAttributeValueLangVO toValueLangVO(SaleAttributeValueLangPO saleAttributeValueLangPO);

    List<SaleAttributeValueLangVO> toValueLangVOList(List<SaleAttributeValueLangPO> saleAttributeValueLangPOList);

    /**
     * SaleAttributeValuePO列表转SaleAttributeValueVO列表
     * @param saleAttributeValuePOList PO列表
     * @return VO列表
     */
    List<SaleAttributeValueVO> toValueVOList(List<SaleAttributeValuePO> saleAttributeValuePOList);

    /**
     * SaleAttributeValueVO列表转SaleAttributeValuePO列表
     * @param saleAttributeValueVOList VO列表
     * @return PO列表
     */
    List<SaleAttributeValuePO> toValuePOList(List<SaleAttributeValueVO> saleAttributeValueVOList);
} 