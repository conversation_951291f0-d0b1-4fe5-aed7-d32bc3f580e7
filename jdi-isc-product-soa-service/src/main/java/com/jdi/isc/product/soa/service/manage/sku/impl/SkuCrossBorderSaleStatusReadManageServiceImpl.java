package com.jdi.isc.product.soa.service.manage.sku.impl;


import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.available.sale.dict.domain.BaseAreaVo;
import com.available.sale.dict.domain.api.result.SkuAvailableSaleResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.order.center.api.config.biz.CrossBorderSkuIopInfoDTO;
import com.jdi.isc.order.center.api.ofc.biz.StoreHouseConsigneeInfoDTO;
import com.jdi.isc.order.center.api.ofc.biz.rsp.CustomerSkuDecisionResult;
import com.jdi.isc.order.center.api.ofc.biz.rsp.SkuInfoResDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.enums.PurchaseModelTypeEnum;
import com.jdi.isc.product.soa.domain.sku.biz.CrossBorderSkuCheckSwitchVO;
import com.jdi.isc.product.soa.domain.sku.biz.QuerySkuAvailableSaleReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuAvailableSaleResultVO;
import com.jdi.isc.product.soa.rpc.gd.RpcGdProductService;
import com.jdi.isc.product.soa.rpc.order.FulfillmentPolicyRpcService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuCrossBorderSaleStatusReadManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuFeatureManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description：跨境商品查询销售状态服务
 * @Date 2025-02-20
 */
@Slf4j
@Service
public class SkuCrossBorderSaleStatusReadManageServiceImpl implements SkuCrossBorderSaleStatusReadManageService {

    @Resource
    private FulfillmentPolicyRpcService fulfillmentPolicyRpcService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private RpcGdProductService rpcGdProductService;

    @Resource
    private CustomerManageService customerManageService;

    @Resource
    private SkuFeatureManageService skuFeatureManageService;

    @LafValue("jdi.isc.stock.iop.switch")
    private String iopSwitch;
    @LafValue("jdi.isc.stock.country.iop.switch")
    private String countryIopSwitch;
    final ExecutorService pool = new ThreadPoolExecutor(20, 20, 30L, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(10000), new ThreadFactoryBuilder().setNamePrefix("jdSkuId-canPurchase").build(), new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    @PFTracing
    public Map<Long, SkuAvailableSaleResultVO> querySkuAvailableCanPurchaseMap(QuerySkuAvailableSaleReqVO reqVO) {
        Map<Long, SkuAvailableSaleResultVO> resultVOMap = Maps.newHashMap();
        // 查询商品集运中心地址信息
        Optional<CustomerSkuDecisionResult> customerSkuDecisionResultOpt = this.getCustomerSkuDecisionResult(reqVO);

        // 为空设置默认地址 读取DUCC配置
        if (!customerSkuDecisionResultOpt.isPresent() || MapUtils.isEmpty(customerSkuDecisionResultOpt.get().getSkuFulfillmentDecisionResult())) {
            log.warn("querySkuAvailableCanPurchaseMap 商品集运中心地址信息为空,入参:[{}]",JSON.toJSONString(reqVO));
            return resultVOMap;
        }

        // 查询商品是否为备货模式
        String countryCode = StringUtils.isNotBlank(reqVO.getCountryCode()) ? reqVO.getCountryCode() : StringUtils.isNotBlank(reqVO.getClientCode()) ? customerManageService.detail(reqVO.getClientCode()).getCountry() : CountryConstant.COUNTRY_ZH;
        Map<Long, Integer> skuPurchaseModelMap = skuFeatureManageService.querySkuPurchaseModel(reqVO.getSkuIds(), countryCode);

        // 集运中心和商品信息
        CustomerSkuDecisionResult decisionResult = customerSkuDecisionResultOpt.get();
        // 获取商品集运中心地址信息
        Map<String, List<SkuInfoResDTO>> skuInforResDtoMap = decisionResult.getSkuFulfillmentDecisionResult();
        // 遍历商品集运中心地址信息，查询商品区域限售信息
        for (Map.Entry<String, List<SkuInfoResDTO>> entry : skuInforResDtoMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }

            List<SkuInfoResDTO> skuInfoResDTOList = entry.getValue();
            Set<Long> skuIds = skuInfoResDTOList.stream().map(SkuInfoResDTO::getSkuId).collect(Collectors.toSet());
            // 查询国际和国内SKU映射
            Map<Long, Long> jdSkuMap = skuAtomicService.batchQueryJdSku(skuIds);
            this.filterPurchaseModelSku(jdSkuMap, skuPurchaseModelMap, resultVOMap);

            // 同组地址相同，取第一个即可
            StoreHouseConsigneeInfoDTO consigneeInfoDTO = skuInfoResDTOList.get(0).getStoreHouseConsigneeInfoDTO();
            if (MapUtils.isNotEmpty(jdSkuMap)) {
                // 查询区域限售接口
                Map<Long, SkuAvailableSaleResult> standardSkuCanPurchasetMap = rpcGdProductService.queryStandardSkuCanPurchaseMap(Sets.newHashSet(jdSkuMap.values()), this.buildBaseAreaVo(consigneeInfoDTO), decisionResult.getIopPin());
                if (MapUtils.isEmpty(standardSkuCanPurchasetMap)) {
                    continue;
                }
                // 将SKU-》JDSKU 反转成 JDSKU-》SKU
                this.handleAvailableResultMap(jdSkuMap, standardSkuCanPurchasetMap, resultVOMap);
            }
        }
        return resultVOMap;
    }

    /**
     * 根据备货模式筛选商品，更新结果VO Map。
     * @param jdSkuMap 京东商品关系Map
     * @param skuPurchaseModelMap 商品备货模式Map
     * @param resultVOMap 结果VO Map
     */
    @PFTracing
    private void filterPurchaseModelSku(Map<Long, Long> jdSkuMap, Map<Long, Integer> skuPurchaseModelMap, Map<Long, SkuAvailableSaleResultVO> resultVOMap) {
        Set<Long> purchaseModelSkuSet = Sets.newHashSet();
        for (Map.Entry<Long, Long> skuRelation : jdSkuMap.entrySet()) {
            Long skuId = skuRelation.getKey();
            if (skuPurchaseModelMap.containsKey(skuId) && (Objects.equals(PurchaseModelTypeEnum.STOCK_UP.getCode(), skuPurchaseModelMap.get(skuId)) ||
                    Objects.equals(PurchaseModelTypeEnum.CONSIGNMENT.getCode(), skuPurchaseModelMap.get(skuId)))) {
                purchaseModelSkuSet.add(skuId);
                resultVOMap.put(skuId, new SkuAvailableSaleResultVO(skuId,Boolean.TRUE,""));
            }
        }

        // jdSkuMap移除备货模式商品，不查内贸段区域限售和可采接口
        if (CollectionUtils.isNotEmpty(purchaseModelSkuSet)) {
            purchaseModelSkuSet.forEach(id -> jdSkuMap.remove(id));
        }
    }


    @Override
    @PFTracing
    public Map<Long, SkuAvailableSaleResultVO> querySkuAvailableLimitArea(QuerySkuAvailableSaleReqVO reqVO) {
        Map<Long, SkuAvailableSaleResultVO> resultVOMap = Maps.newHashMap();

        // 查询商品集运中心地址信息
        Optional<CustomerSkuDecisionResult> customerSkuDecisionResultOpt = this.getCustomerSkuDecisionResult(reqVO);

        // 为空设置默认地址 读取DUCC配置
        if (!customerSkuDecisionResultOpt.isPresent() || MapUtils.isEmpty(customerSkuDecisionResultOpt.get().getSkuFulfillmentDecisionResult())) {
            log.warn("querySkuAvailableLimitArea 商品集运中心地址信息为空,入参:[{}]",JSON.toJSONString(reqVO));
            for (Long skuId : reqVO.getSkuIds()){
                SkuAvailableSaleResultVO skuAvailableSaleResultVO = new SkuAvailableSaleResultVO();
                skuAvailableSaleResultVO.setSkuId(skuId);
                skuAvailableSaleResultVO.setIsAvailableSale(false);
                skuAvailableSaleResultVO.setUnAvailableSaleMessage("商品集运中心地址信息为空");
                resultVOMap.put(skuId,skuAvailableSaleResultVO);
            }
            return resultVOMap;
        }
        // 集运中心和商品信息
        CustomerSkuDecisionResult decisionResult = customerSkuDecisionResultOpt.get();
        // 获取商品集运中心地址信息
        Map<String, List<SkuInfoResDTO>> skuInforResDtoMap = decisionResult.getSkuFulfillmentDecisionResult();
        // 查询商品是否为备货模式
        String countryCode = StringUtils.isNotBlank(reqVO.getCountryCode()) ? reqVO.getCountryCode() : StringUtils.isNotBlank(reqVO.getClientCode()) ? customerManageService.detail(reqVO.getClientCode()).getCountry() : CountryConstant.COUNTRY_ZH;
        Map<Long, Integer> skuPurchaseModelMap = skuFeatureManageService.querySkuPurchaseModel(reqVO.getSkuIds(), countryCode);

        // 遍历商品集运中心地址信息，查询商品区域限售信息
        for (Map.Entry<String, List<SkuInfoResDTO>> entry : skuInforResDtoMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            List<SkuInfoResDTO> skuInfoResDTOList = entry.getValue();
            Set<Long> skuIds = skuInfoResDTOList.stream().map(SkuInfoResDTO::getSkuId).collect(Collectors.toSet());
            // 查询国际和国内SKU映射
            Map<Long, Long> jdSkuMap = skuAtomicService.batchQueryJdSku(skuIds);
            this.filterPurchaseModelSku(jdSkuMap, skuPurchaseModelMap, resultVOMap);
            // 同组地址相同，取第一个即可
            StoreHouseConsigneeInfoDTO consigneeInfoDTO = skuInfoResDTOList.get(0).getStoreHouseConsigneeInfoDTO();
            if (MapUtils.isNotEmpty(jdSkuMap)) {
                // 查询区域限售接口
                Map<Long, SkuAvailableSaleResult> standardSkuAreaLimitMap = rpcGdProductService.queryStandardSkuAreaLimitMap(Sets.newHashSet(jdSkuMap.values()), this.buildBaseAreaVo(consigneeInfoDTO), decisionResult.getIopPin());
                if (MapUtils.isEmpty(standardSkuAreaLimitMap)) {
                    continue;
                }
                // 将SKU-》JDSKU 反转成 JDSKU-》SKU
                this.handleAvailableResultMap(jdSkuMap, standardSkuAreaLimitMap, resultVOMap);
            }
        }
        return resultVOMap;
    }

    @Override
    @PFTracing
    public Map<Long, SkuAvailableSaleResultVO> queryStandardSkuAvailableSale(QuerySkuAvailableSaleReqVO reqVO) {
        Map<Long, SkuAvailableSaleResultVO> resultVOMap = Maps.newHashMap();

        // 查询商品集运中心地址信息
        Optional<CustomerSkuDecisionResult> customerSkuDecisionResultOpt = this.getCustomerSkuDecisionResult(reqVO);

        // 为空设置默认地址 读取DUCC配置
        if (!customerSkuDecisionResultOpt.isPresent() || MapUtils.isEmpty(customerSkuDecisionResultOpt.get().getSkuFulfillmentDecisionResult())) {
            log.warn("queryStandardSkuAvailableSale 商品集运中心地址信息为空,入参:[{}]",JSON.toJSONString(reqVO));
            //return resultVOMap;
            for (Long skuId : reqVO.getSkuIds()){
                SkuAvailableSaleResultVO skuAvailableSaleResultVO = new SkuAvailableSaleResultVO();
                skuAvailableSaleResultVO.setSkuId(skuId);
                skuAvailableSaleResultVO.setIsAvailableSale(false);
                skuAvailableSaleResultVO.setUnAvailableSaleMessage("商品集运中心地址信息为空");
                resultVOMap.put(skuId,skuAvailableSaleResultVO);
            }
            return resultVOMap;
        }
        // 集运中心和商品信息
        CustomerSkuDecisionResult decisionResult = customerSkuDecisionResultOpt.get();
        // 获取商品集运中心地址信息
        Map<String, List<SkuInfoResDTO>> skuInforResDtoMap = decisionResult.getSkuFulfillmentDecisionResult();
        // 查询商品是否为备货模式
        String countryCode = StringUtils.isNotBlank(reqVO.getCountryCode()) ? reqVO.getCountryCode() : StringUtils.isNotBlank(reqVO.getClientCode()) ? customerManageService.detail(reqVO.getClientCode()).getCountry() : CountryConstant.COUNTRY_ZH;
        Map<Long, Integer> skuPurchaseModelMap = skuFeatureManageService.querySkuPurchaseModel(reqVO.getSkuIds(), countryCode);

        // 遍历商品集运中心地址信息，查询商品区域限售信息
        for (Map.Entry<String, List<SkuInfoResDTO>> entry : skuInforResDtoMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            List<SkuInfoResDTO> skuInfoResDTOList = entry.getValue();
            Set<Long> skuIds = skuInfoResDTOList.stream().map(SkuInfoResDTO::getSkuId).collect(Collectors.toSet());
            // 查询国际和国内SKU映射
            Map<Long, Long> jdSkuMap = skuAtomicService.batchQueryJdSku(skuIds);
            this.filterPurchaseModelSku(jdSkuMap, skuPurchaseModelMap, resultVOMap);
            // 同组地址相同，取第一个即可
            StoreHouseConsigneeInfoDTO consigneeInfoDTO = skuInfoResDTOList.get(0).getStoreHouseConsigneeInfoDTO();
            if (MapUtils.isNotEmpty(jdSkuMap)) {
                // 查询区域限售接口
                Map<Long, SkuAvailableSaleResult> standardSkuAreaSaleMap = rpcGdProductService.queryStandardSkuAvailableSaleMap(Lists.newArrayList(jdSkuMap.values()), this.buildBaseAreaVo(consigneeInfoDTO), decisionResult.getIopPin());
                if (MapUtils.isEmpty(standardSkuAreaSaleMap)) {
                    continue;
                }
                // 将SKU-》JDSKU 反转成 JDSKU-》SKU
                this.handleAvailableResultMap(jdSkuMap, standardSkuAreaSaleMap, resultVOMap);
            }
        }
        return resultVOMap;
    }

    /**
     * 处理可用结果映射，生成 SkuAvailableSaleResultVO 对象并存储在 resultVOMap 中。
     * @param jdSkuMap 京东 SKU 映射表，key 为京东 SKU ID，value 为标准 SKU ID。
     * @param standardSkuAreaLimitMap 标准 SKU 的销售限制结果映射表，key 为京东 SKU ID，value 为 SkuAvailableSaleResult 对象。
     * @param resultVOMap 结果 VO 映射表，key 为标准 SKU ID，value 为 SkuAvailableSaleResultVO 对象。
     */
    @PFTracing
    private void handleAvailableResultMap(Map<Long, Long> jdSkuMap, Map<Long, SkuAvailableSaleResult> standardSkuAreaLimitMap, Map<Long, SkuAvailableSaleResultVO> resultVOMap) {
        // 国际skuId和国内skuId映射关系互换，京东skuId可能被多次绑定
        Map<Long, Set<Long>> invertMap = this.invertMap(jdSkuMap);
        standardSkuAreaLimitMap.forEach((jdSkuId, saleResult) -> {
            Set<Long> skuIdSet = invertMap.get(jdSkuId);
            if (CollectionUtils.isEmpty(skuIdSet)) {
                return;
            }
            Boolean isAvailableSale = saleResult.getSkuInfo().getIsAvailableSale();
            String unAvailableSaleMessage = saleResult.getSkuInfo().getUnAvailableSaleMessage();
            skuIdSet.forEach(skuId -> resultVOMap.put(skuId, new SkuAvailableSaleResultVO(skuId,isAvailableSale,unAvailableSaleMessage)));
        });
    }


    /**
     * 根据国家代码或客户代码获取商品库存决策结果。
     * @param reqVO 查询请求对象，包含国家代码或客户代码和商品ID列表。
     * @return 商品库存决策结果的Optional对象，若未找到匹配的决策结果则返回空的Optional对象。
     */
    @PFTracing
    private Optional<CustomerSkuDecisionResult> getCustomerSkuDecisionResult(QuerySkuAvailableSaleReqVO reqVO) {
        if (StringUtils.isNotBlank(reqVO.getCountryCode())) {
            return Optional.ofNullable(fulfillmentPolicyRpcService.querySkuMultiWarehouseDecisionByCountry(reqVO.getCountryCode(), reqVO.getSkuIds()));
        } else if (StringUtils.isNotBlank(reqVO.getClientCode())) {
            return Optional.ofNullable(fulfillmentPolicyRpcService.querySkuMultiWarehouseDecisionByCustomer(reqVO.getClientCode(), reqVO.getSkuIds()));
        }
        return Optional.empty();
    }

    /**
     * 构建基础地区信息对象
     * @param consigneeInfoDTO 存储库收货人信息DTO
     * @return 基础地区信息对象
     */
    private BaseAreaVo buildBaseAreaVo(StoreHouseConsigneeInfoDTO consigneeInfoDTO) {
        BaseAreaVo baseAreaVo = new BaseAreaVo();
        baseAreaVo.setProvinceCode(Objects.isNull(consigneeInfoDTO.getConsigneeProvinceId()) ? null : consigneeInfoDTO.getConsigneeProvinceId().intValue());
        baseAreaVo.setCityCode(Objects.isNull(consigneeInfoDTO.getConsigneeCityId()) ? null : consigneeInfoDTO.getConsigneeCityId().intValue());
        baseAreaVo.setCountyCode(Objects.isNull(consigneeInfoDTO.getConsigneeCountyId()) ? null : consigneeInfoDTO.getConsigneeCountyId().intValue());
        baseAreaVo.setTownCode(Objects.isNull(consigneeInfoDTO.getConsigneeTownId()) ? null : consigneeInfoDTO.getConsigneeTownId().intValue());
        return baseAreaVo;
    }

    private BaseAreaVo buildBaseAreaVoFromCrossBorderSkuIopInfoDTO(CrossBorderSkuIopInfoDTO crossBorderSkuIopInfoDTO) {
        BaseAreaVo baseAreaVo = new BaseAreaVo();
        baseAreaVo.setProvinceCode(Objects.isNull(crossBorderSkuIopInfoDTO.getProvince()) ? null : crossBorderSkuIopInfoDTO.getProvince().intValue());
        baseAreaVo.setCityCode(Objects.isNull(crossBorderSkuIopInfoDTO.getCity()) ? null : crossBorderSkuIopInfoDTO.getCity().intValue());
        baseAreaVo.setCountyCode(Objects.isNull(crossBorderSkuIopInfoDTO.getCounty()) ? null : crossBorderSkuIopInfoDTO.getCounty().intValue());
        baseAreaVo.setTownCode(Objects.isNull(crossBorderSkuIopInfoDTO.getTown()) ? null : crossBorderSkuIopInfoDTO.getTown().intValue());
        return baseAreaVo;
    }

    @Override
    public List<Long> queryStandardSkuAreaLimitAllAreaByJdSkuIds(List<Long> jdSkuIds) {

        CrossBorderSkuCheckSwitchVO crossBorderSkuCheckSwitchVO = JSON.parseObject(iopSwitch, CrossBorderSkuCheckSwitchVO.class);
        if(!crossBorderSkuCheckSwitchVO.isValidateJdSkuIdAreaLimit()){
            log.info("SkuCrossBorderSaleStatusReadManageServiceImpl.isValidateJdSkuIdAreaLimit is false");
            return Collections.emptyList();
        }

        //  查询所有地址区域限售逻辑
        List<SkuAvailableSaleResultVO> resultVOList = this.queryCrossBorderSkuLimitAreaForAllAreaList(Sets.newHashSet(jdSkuIds));
        List<Long> notAreaLimitIds = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(resultVOList)){
            for (SkuAvailableSaleResultVO saleResult : resultVOList) {
                // 区域不限售
                Boolean isAvailableSale = saleResult.getIsAvailableSale();
                if(isAvailableSale){
                    notAreaLimitIds.add(saleResult.getSkuId());
                }
            }
        }
        List<Long> areaLimitIds = Lists.newArrayList(jdSkuIds);
        areaLimitIds.removeAll(notAreaLimitIds);
        log.error("SkuCrossBorderSaleStatusReadManageServiceImpl.queryStandardSkuAreaLimitAllArea, notAreaLimitIds = {}, areaLimitIds = {}", JSON.toJSONString(jdSkuIds), JSON.toJSONString(areaLimitIds));
        return areaLimitIds;
    }

    @Override
    public List<Long> queryStandardSkuAvailableSaleAllAreaByJdSkuIds(List<Long> jdSkuIds) {
        CrossBorderSkuCheckSwitchVO crossBorderSkuCheckSwitchVO = JSON.parseObject(iopSwitch, CrossBorderSkuCheckSwitchVO.class);
        if(!crossBorderSkuCheckSwitchVO.isValidateJdSkuIdCanPurchase()){
            log.info("SkuCrossBorderSaleStatusReadManageServiceImpl.queryStandardSkuAvailableSaleAllAreaByJdSkuIds is false");
            return jdSkuIds;
        }

        List<SkuAvailableSaleResultVO> resultVOList = this.queryCrossBorderSkuAvailableSaleForAllAreaList(Sets.newHashSet(jdSkuIds));
        List<Long> canSaleSkuIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(resultVOList)){
            List<Long> subSkuIds = resultVOList.stream().filter(skuAvailableSaleResult -> skuAvailableSaleResult != null && skuAvailableSaleResult.getIsAvailableSale()).map(SkuAvailableSaleResultVO::getSkuId).collect(Collectors.toList());
            canSaleSkuIds.addAll(subSkuIds);
        }
        log.info("SkuCrossBorderSaleStatusReadManageServiceImpl.queryStandardSkuAvailableSaleAllAreaByJdSkuIds, canSaleSkuIds = {}", JSON.toJSONString(canSaleSkuIds));
        return canSaleSkuIds;
    }

    @Override
    @PFTracing
    public List<Long> queryStandardSkuAreaLimitForCountryPool(List<Long> skuIds, String countryCode) {
        if(CollectionUtils.isEmpty(skuIds)){
            return skuIds;
        }
        CrossBorderSkuCheckSwitchVO crossBorderSkuCheckSwitchVO = JSON.parseObject(countryIopSwitch, CrossBorderSkuCheckSwitchVO.class);
        if(!crossBorderSkuCheckSwitchVO.isValidateJdSkuIdAreaLimit()){
            log.info("SkuCrossBorderSaleStatusReadManageServiceImpl.queryStandardSkuAreaLimitForCountryPool is false");
            return Collections.emptyList();
        }
        // 国家编码和sku集合查询区域限售
        QuerySkuAvailableSaleReqVO limitReqVO = new QuerySkuAvailableSaleReqVO(Sets.newHashSet(skuIds),countryCode);
        Map<Long, SkuAvailableSaleResultVO> resultVOMap = this.querySkuAvailableLimitArea(limitReqVO);
        if (MapUtils.isEmpty(resultVOMap)) {
            return skuIds;
        }
        List<Long> notAreaLimitIds = new ArrayList<>();

        for (Map.Entry<Long, SkuAvailableSaleResultVO> limitSkuResult : resultVOMap.entrySet()) {
            SkuAvailableSaleResultVO value = limitSkuResult.getValue();
            // 区域不限售
            Boolean isAvailableSale = value.getIsAvailableSale();
            if(isAvailableSale){
                notAreaLimitIds.add(value.getSkuId());
            }
        }

        List<Long> areaLimitIds = Lists.newArrayList(skuIds);
        areaLimitIds.removeAll(notAreaLimitIds);
        log.error("SkuCrossBorderSaleStatusReadManageServiceImpl.queryStandardSkuAreaLimitForCountryPool, notAreaLimitIds = {}, areaLimitIds = {}", JSON.toJSONString(skuIds), JSON.toJSONString(areaLimitIds));
        return areaLimitIds;
    }

    @Override
    @PFTracing
    public List<Long> queryStandardSkuAvailableSaleForCountryPool(List<Long> skuIds, String countryCode, Map<Long, String> failMsgMap) {
        CrossBorderSkuCheckSwitchVO crossBorderSkuCheckSwitchVO = JSON.parseObject(countryIopSwitch, CrossBorderSkuCheckSwitchVO.class);
        if(!crossBorderSkuCheckSwitchVO.isValidateJdSkuIdCanPurchase()){
            log.info("SkuCrossBorderSaleStatusReadManageServiceImpl.isValidateJdSkuIdCanPurchase is false");
            return skuIds;
        }
        // 国家编码和sku集合查询可售性
        QuerySkuAvailableSaleReqVO saleReqVO = new QuerySkuAvailableSaleReqVO(Sets.newHashSet(skuIds),countryCode);
        Map<Long, SkuAvailableSaleResultVO> saleResultVOMap = this.queryStandardSkuAvailableSale(saleReqVO);
        List<Long> canSaleSkuIds = new ArrayList<>();
        if (MapUtils.isEmpty(saleResultVOMap)){
            return canSaleSkuIds;
        }

        for (Map.Entry<Long,SkuAvailableSaleResultVO> saleResult : saleResultVOMap.entrySet()) {
            SkuAvailableSaleResultVO value = saleResult.getValue();
            if (Objects.nonNull(value)) {
                // 是否可售
                Boolean isAvailableSale = value.getIsAvailableSale();
                if(isAvailableSale){
                    canSaleSkuIds.add(saleResult.getKey());
                } else if(Objects.nonNull(failMsgMap)) {// 是否入池
                    failMsgMap.put(saleResult.getKey(), value.getUnAvailableSaleMessage());
                }
            }
        }
        log.error("SkuCrossBorderSaleStatusReadManageServiceImpl.queryStandardSkuAvailableSaleForCountryPool, canSaleSkuIds = {}", JSON.toJSONString(canSaleSkuIds));
        return canSaleSkuIds;
    }


    @Override
    public List<SkuAvailableSaleResultVO> queryCrossBorderSkuLimitAreaForAllAreaList(Set<Long> jdSkuIds) {
        //  查询所有地址信息
        List<CrossBorderSkuIopInfoDTO> iopInfoDTOS = fulfillmentPolicyRpcService.queryAllIopPinAndAddressConfigInfo();
        List<CompletableFuture<List<SkuAvailableSaleResult>>> futures = Optional.ofNullable(iopInfoDTOS).orElseGet(ArrayList::new).stream().map(iopParamObj -> {
            BaseAreaVo baseAreaVo = this.buildBaseAreaVoFromCrossBorderSkuIopInfoDTO(iopParamObj);
            return CompletableFuture.supplyAsync(() -> rpcGdProductService.queryStandardSkuAreaLimit(Lists.newArrayList(jdSkuIds), baseAreaVo ,iopParamObj.getIopPin()), pool)
                    .exceptionally(e -> {
                        throw new RuntimeException(e);
                    });
        }).collect(Collectors.toList());

        List<SkuAvailableSaleResultVO> resultVOList = new ArrayList<>();
        futures.forEach(future -> {
            try {
                List<SkuAvailableSaleResult> skuAvailableSaleResults = future.get();
                if(CollectionUtils.isNotEmpty(skuAvailableSaleResults)){
                    for (SkuAvailableSaleResult saleResult : skuAvailableSaleResults) {
                        resultVOList.add(new SkuAvailableSaleResultVO(saleResult.getSkuId(), saleResult.getSkuInfo().getIsAvailableSale(), saleResult.getSkuInfo().getUnAvailableSaleMessage()));
                    }
                }
            } catch (Exception e) {
                log.error("SkuCrossBorderSaleStatusReadManageServiceImpl.queryCrossBorderSkuLimitAreaForAllAreaList", e);
            }
        });
        return resultVOList;
    }

    @Override
    public List<SkuAvailableSaleResultVO> queryCrossBorderSkuAvailableSaleForAllAreaList(Set<Long> jdSkuIds) {
        //  查询所有地址信息
        List<CrossBorderSkuIopInfoDTO> iopInfoDTOS = fulfillmentPolicyRpcService.queryAllIopPinAndAddressConfigInfo();
        List<CompletableFuture<Map<Long,SkuAvailableSaleResult>>> futures = Optional.ofNullable(iopInfoDTOS).orElseGet(ArrayList::new).stream().map(iopParamObj -> {
            BaseAreaVo baseAreaVo = this.buildBaseAreaVoFromCrossBorderSkuIopInfoDTO(iopParamObj);
            return CompletableFuture.supplyAsync(() -> rpcGdProductService.queryStandardSkuAvailableSaleMap(Lists.newArrayList(jdSkuIds), baseAreaVo, iopParamObj.getIopPin()), pool)
                    .exceptionally(e -> {
                        throw new RuntimeException(e);
                    });
        }).collect(Collectors.toList());

        List<SkuAvailableSaleResultVO> resultVOList = Lists.newArrayList();
        futures.forEach(future -> {
            try {
                Map<Long, SkuAvailableSaleResult> skuAvailableSaleResults = future.get();
                if(MapUtils.isNotEmpty(skuAvailableSaleResults)){
                    for (Map.Entry<Long, SkuAvailableSaleResult> entry : skuAvailableSaleResults.entrySet()) {
                        SkuAvailableSaleResult saleResult = entry.getValue();
                        resultVOList.add(new SkuAvailableSaleResultVO(saleResult.getSkuId(), saleResult.getSkuInfo().getIsAvailableSale(), saleResult.getSkuInfo().getUnAvailableSaleMessage()));
                    }
                }
            } catch (Exception e) {
                log.error("SkuCrossBorderSaleStatusReadManageServiceImpl.queryCrossBorderSkuAvailableSaleForAllAreaList", e);
            }
        });
        return resultVOList;
    }

    /**
     * 将给定的 Map 进行反转，即将 key 和 value 交换位置。
     * @param skuAndJdSkuMap 原始的 SKU 和京东 SKU 的映射关系
     * @return 反转后的 Map，其中 key 为京东 SKU，value 为对应的 SKU 集合
     */
    @PFTracing
    private Map<Long, Set<Long>> invertMap(Map<Long, Long> skuAndJdSkuMap) {
        if (MapUtils.isEmpty(skuAndJdSkuMap)) {
            return Collections.emptyMap();
        }

        Map<Long, Set<Long>> result = new HashMap<>();
        for (Map.Entry<Long, Long> entry : skuAndJdSkuMap.entrySet()) {
            if (result.containsKey(entry.getValue())) {
                result.get(entry.getValue()).add(entry.getKey());
            }else {
                result.put(entry.getValue(), Sets.newHashSet(entry.getKey()));
            }
        }

        return result;
    }
}
