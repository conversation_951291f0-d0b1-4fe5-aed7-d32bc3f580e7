package com.jdi.isc.product.soa.service.manage.price.agreementPrice.agreement.approve;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.biz.component.api.enums.ApprovalOperateEnum;
import com.jdi.isc.biz.component.api.enums.JoySkyBizFlowTypeEnum;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyCancelApiDTO;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyCreateReqApiDTO;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyOperateApiDTO;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyQueryApiDTO;
import com.jdi.isc.biz.component.api.joysky.res.JoySkyCreateRspApiDTO;
import com.jdi.isc.biz.component.api.joysky.res.JoySkyDetailInfoDTO;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.AgreementPriceDataSourceTypeEnums;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.util.BigDecimalUtil;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.domain.apply.po.ApplyInfoPO;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.SkuScopeEnum;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceAuditVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceCalculateResVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPriceDraftPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.biz.ProfitRateVO;
import com.jdi.isc.product.soa.domain.sku.biz.ExternalVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuExternalReqVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangVO;
import com.jdi.isc.product.soa.rpc.joySky.JoySkyApproveRpcService;
import com.jdi.isc.product.soa.service.atomic.apply.ApplyInfoAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.SupplierSettlementAccountAtomicService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.price.ProfitCalculateManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.agreement.AgreementPriceApproveService;
import com.jdi.isc.product.soa.service.manage.sku.SkuExternalManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 国家协议价审批
 *
 * <AUTHOR>
 * @date 2025/03/08
 **/
@Slf4j
@Service
public class CountryAgreementPriceApproveServiceImpl implements AgreementPriceApproveService {

    @Resource
    private CountryManageService countryManageService;
    @Resource
    private JoySkyApproveRpcService joySkyApproveRpcService;
    @Resource
    private SkuExternalManageService skuExternalManageService;
    @Resource
    private ApplyInfoAtomicService applyInfoAtomicService;
    @Resource
    @Lazy
    private ProfitCalculateManageService profitCalculateManageService;
    @Resource
    @Lazy
    private CountryAgreementPriceDraftAtomicService countryAgreementPriceDraftAtomicService;
    @Resource
    @Lazy
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;
    @Resource
    @Lazy
    private CountryAgreementPriceManageService countryAgreementPriceManageService;
    @Resource
    private SkuPriceManageService skuPriceManageService;
    @Resource
    private SupplierSettlementAccountAtomicService supplierSettlementAccountAtomicService;

    @Resource
    private OperDuccConfig operDuccConfig;


    /** 对应的joySky审批流 */
    protected static final JoySkyBizFlowTypeEnum flowTypeEnum = JoySkyBizFlowTypeEnum.COUNTRY_AGREEMENT_EFFECTIVE_FLOW;
    @Value("${spring.profiles.active}")
    protected String systemProfile;


    @Override
    public CountryAgreementPriceCalculateResVO getCalculateResVO(CountryAgreementPricePO countryAgreementPrice, BigDecimal agreementPrice) {
        log.info("CountryAgreementPriceApproveServiceImpl.getCalculateResVO start, countryAgreementPrice:{},agreementPrice:{}"
                , JSONObject.toJSONString(countryAgreementPrice),agreementPrice);

        ProfitRateVO profitRateVO = profitCalculateManageService.getProfitLimitRate(countryAgreementPrice.getJdCatId()
                ,countryAgreementPrice.getSourceCountryCode(),countryAgreementPrice.getTargetCountryCode());
        if(profitRateVO == null || profitRateVO.getProfitRate() == null){
            throw new BizException("利润率阈值为空");
        }
        if(profitRateVO.getLowProfitRate() == null){
            throw new BizException("超低利润率阈值为空");
        }

        BigDecimal countryCostPrice = countryAgreementPrice.getCountryCostPrice();
        if(countryCostPrice == null){
            throw new BizException("国家成本价为空");
        }

        BigDecimal profitRate = profitCalculateManageService.calculateAgreementProfitRate(agreementPrice,countryCostPrice);
        if(profitRate == null){
            throw new BizException("利润率为空");
        }

        CountryAgreementPriceCalculateResVO resVO = new CountryAgreementPriceCalculateResVO();
        resVO.setCountryCostPrice(countryCostPrice);
        resVO.setSkuId(countryAgreementPrice.getSkuId());
        resVO.setSourceCountryCode(countryAgreementPrice.getSourceCountryCode());
        resVO.setTargetCountryCode(countryAgreementPrice.getTargetCountryCode());
        resVO.setCurrency(countryAgreementPrice.getCurrency());
        resVO.setCountryAgreementPrice(agreementPrice);
        resVO.setProfitRate(profitRate);
        resVO.setProfitLimitRate(profitRateVO.getProfitRate());
        resVO.setLowProfitLimitRate(profitRateVO.getLowProfitRate());

        log.info("CountryAgreementPriceApproveServiceImpl.getCalculateResVO end, resVO:{}", JSONObject.toJSONString(resVO));
        return resVO;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public String submit(CountryAgreementPriceVO input) {
        log.info("CountryAgreementPriceApproveServiceImpl.submit input:{}", JSONObject.toJSONString(input));
        JoySkyCreateReqApiDTO joySkyCreateReqApiDTO = new JoySkyCreateReqApiDTO();
        joySkyCreateReqApiDTO.setErp(StringUtils.isNotBlank(input.getUpdater())?input.getUpdater(): LoginContextHolder.getLoginContextHolder().getPin());
        joySkyCreateReqApiDTO.setJoySkyFlowType(flowTypeEnum.getFlowTypeCode());
        Map<String, String> processFormDataModelMap = this.constructProcessFormDataModel(input);
        joySkyCreateReqApiDTO.setProcessFromData(processFormDataModelMap);
        joySkyCreateReqApiDTO.setProcessControlData(this.constructProcessControlData(input.getSourceCountryCode(),input.getTargetCountryCode()));

        // 审批流流程表单数据 增加附件，附件比较特殊，需要单独处理
        Map<String/*文件名*/, String/*url*/> attachmentMap = com.jdi.isc.product.soa.common.util.StringUtils.getAttachmentMap(input.getAttachmentUrls());
        joySkyCreateReqApiDTO.addAttachment("附件", attachmentMap);
        // 多附件
        joySkyCreateReqApiDTO.setMultiFile(true);

        // 更新表单数据
        countryAgreementPriceDraftAtomicService.updateProcessFromData(input.getId(), joySkyCreateReqApiDTO.getProcessFromData());


        JoySkyCreateRspApiDTO joySkyCreateRspApiDTO = joySkyApproveRpcService.createSkyApprovalFlow(joySkyCreateReqApiDTO);
        JoySkyDetailInfoDTO joySkyDetailInfo = this.getJoySkyDetailInfo(joySkyCreateRspApiDTO.getProcessInstanceId(),Boolean.FALSE,Boolean.TRUE);

        ApplyInfoPO dbApplyInfoPO = applyInfoAtomicService.getByBizTypeAndBizId(flowTypeEnum,input.getId());
        ApplyInfoPO applyInfoPO = null;
        if(dbApplyInfoPO != null){
            applyInfoPO = this.getUpdateApplyInfo(dbApplyInfoPO.getId()
                    ,AuditStatusEnum.WAITING_APPROVED.getCode(), LoginContextHolder.getLoginContextHolder().getPin(),dbApplyInfoPO.getVersion()
                    ,LoginContextHolder.getLoginContextHolder().getPin());
            applyInfoPO.setProcessInstanceId(joySkyDetailInfo.getProcessInstanceId());
            applyInfoPO.setApplyCode(joySkyDetailInfo.getApplyCode());
            applyInfoAtomicService.update(applyInfoPO,dbApplyInfoPO.getVersion());
        }else{
            applyInfoPO = this.initApplyInfo(input.getId(),joySkyDetailInfo);
            applyInfoAtomicService.save(applyInfoPO);
        }
        return "";
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean messageApprove(CountryAgreementPriceAuditVO input) {
        log.info("CountryAgreementPriceApproveServiceImpl.messageApprove input:{}", JSONObject.toJSONString(input));
        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(flowTypeEnum,input.getProcessInstanceId());
        if(applyInfoPO == null || applyInfoPO.getAuditStatus() == null || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.error("messageApprove.当前记录非待审核状态，不允许重复审批，processInstanceId:{}", input.getProcessInstanceId());
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s 国家协议价审批消息通过异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "applyInfoPO status is error"));
            return Boolean.FALSE;
        }

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId(),AuditStatusEnum.APPROVED.getCode(), null,applyInfoPO.getVersion(),input.getAuditErp());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
        CountryAgreementPriceDraftPO draftPO = countryAgreementPriceDraftAtomicService.getValidById(Long.valueOf(applyInfoPO.getBizId()));

        if(draftPO == null){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s 国家协议价审批消息通过异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "draftPO is null"));
            return Boolean.FALSE;
        }

        CountryAgreementPriceDraftPO updateDraftPO = this.getUpdateDraftPO(draftPO,AuditStatusEnum.APPROVED,draftPO.getUpdater(),input.getAuditErp(),input.getRejectReason());
        countryAgreementPriceDraftAtomicService.saveOrUpdate(updateDraftPO);
        countryAgreementPriceManageService.saveOrUpdate(this.getCountryAgreementUpdate(draftPO));

        log.info("CountryAgreementPriceApproveServiceImpl.messageApprove " +
                        "pin:{},skuId:{},salePrice:{}",input.getAuditErp(),updateDraftPO.getSkuId(),updateDraftPO.getAgreementPrice());
        return Boolean.TRUE;

    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean messageReject(CountryAgreementPriceAuditVO input) {
        log.info("CountryAgreementPriceApproveServiceImpl.messageReject input:{}", JSONObject.toJSONString(input));
        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(flowTypeEnum,input.getProcessInstanceId());
        if(applyInfoPO == null || applyInfoPO.getAuditStatus() == null || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.error("messageReject.当前记录非待审核状态，不允许重复审批，processInstanceId:{}", input.getProcessInstanceId());
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s 国家协议价审批消息驳回异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "applyInfoPO status is error"));
            return Boolean.FALSE;
        }

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId(),AuditStatusEnum.REJECTED.getCode(), null,applyInfoPO.getVersion(),input.getAuditErp());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
        CountryAgreementPriceDraftPO draftPO = countryAgreementPriceDraftAtomicService.getValidById(Long.valueOf(applyInfoPO.getBizId()));

        if(draftPO == null){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s 国家协议价审批消息驳回异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "draftPO is null"));
            return Boolean.FALSE;
        }

        CountryAgreementPriceDraftPO updateDraftPO = this.getUpdateDraftPO(draftPO,AuditStatusEnum.REJECTED,draftPO.getUpdater(),input.getAuditErp(),input.getRejectReason());
        countryAgreementPriceDraftAtomicService.saveOrUpdate(updateDraftPO);

        log.info("CountryAgreementPriceApproveServiceImpl.messageReject " +
                        "pin:{},skuId:{},salePrice:{}",input.getAuditErp(),updateDraftPO.getSkuId(),updateDraftPO.getAgreementPrice());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean messageRevoke(CountryAgreementPriceAuditVO input) {
        log.info("CountryAgreementPriceApproveServiceImpl.messageRevoke input:{}", JSONObject.toJSONString(input));
        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(flowTypeEnum,input.getProcessInstanceId());
        if(applyInfoPO == null || applyInfoPO.getAuditStatus() == null || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.error("messageRevoke.当前记录非待审核状态，不允许重复审批，processInstanceId:{}", input.getProcessInstanceId());
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s 国家协议价审批消息撤回异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "applyInfoPO status is error"));
            return Boolean.FALSE;
        }
        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId(),AuditStatusEnum.DRAFT.getCode(), null,applyInfoPO.getVersion(),input.getAuditErp());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
        CountryAgreementPriceDraftPO draftPO = countryAgreementPriceDraftAtomicService.getValidById(Long.valueOf(applyInfoPO.getBizId()));

        if(draftPO == null){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s 国家协议价审批消息撤回异常,ids: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , input.getIds().stream().map(String::valueOf).collect(Collectors.joining(","))
                    , "draftPO is null"));
            return Boolean.FALSE;
        }

        CountryAgreementPriceDraftPO updateDraftPO = this.getUpdateDraftPO(draftPO,AuditStatusEnum.DRAFT,draftPO.getUpdater(),input.getAuditErp(),input.getRejectReason());
        countryAgreementPriceDraftAtomicService.saveOrUpdate(updateDraftPO);

        log.info("CountryAgreementPriceApproveServiceImpl.messageRevoke " +
                        "pin:{},skuId:{},salePrice:{}",input.getAuditErp(),draftPO.getSkuId(),draftPO.getAgreementPrice());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean messageApproving(CountryAgreementPriceAuditVO input) {
        log.info("CountryAgreementPriceApproveServiceImpl.messageApproving input:{}", JSONObject.toJSONString(input));
        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(flowTypeEnum,input.getProcessInstanceId());
        if(applyInfoPO == null){
            log.error("messageApproving.当前记录不存在，processInstanceId:{}", input.getProcessInstanceId());
            return Boolean.FALSE;
        }

        if(applyInfoPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.error("messageApproving.当前记录非待审核状态，不允许重复审批，processInstanceId:{}", input.getProcessInstanceId());
            return Boolean.FALSE;
        }

        JoySkyDetailInfoDTO joySkyDetailInfo = this.getJoySkyDetailInfo(applyInfoPO.getProcessInstanceId(),Boolean.FALSE,Boolean.TRUE);
        String currentAuditor = null;
        if(CollectionUtils.isNotEmpty(joySkyDetailInfo.getTodoList())){
            currentAuditor = String.join(",", joySkyDetailInfo.getTodoList());
        }
        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId(),AuditStatusEnum.WAITING_APPROVED.getCode(), currentAuditor,applyInfoPO.getVersion(),input.getAuditErp());

        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
        log.info("CountryAgreementPriceApproveServiceImpl.messageApproving processInstanceId:{},auditErp:{}",input.getProcessInstanceId(),input.getAuditErp());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean singleApprove(CountryAgreementPriceDraftPO draftPO, CountryAgreementPriceAuditVO input, ApplyInfoPO applyInfoPO) {
        log.info("CountryAgreementPriceApproveServiceImpl.singleApprove start draftPO:{},input:{},applyInfoPO:{}",JSONObject.toJSONString(draftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));

        if(applyInfoPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        if(draftPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != draftPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        Boolean flag = this.operateJoySky(ApprovalOperateEnum.AGREE,applyInfoPO.getProcessInstanceId()
                ,input.getRejectReason(),LoginContextHolder.getLoginContextHolder().getPin());

        if(!flag){
            throw new BizException("审批通过失败");
        }
        JoySkyDetailInfoDTO joySkyDetailInfo = this.getJoySkyDetailInfo(applyInfoPO.getProcessInstanceId(),Boolean.FALSE,Boolean.TRUE);
        if(joySkyDetailInfo != null && joySkyDetailInfo.getAuditStatus().equals(AuditStatusEnum.APPROVING.getCode())){
            String currentAuditor = null;
            if(CollectionUtils.isNotEmpty(joySkyDetailInfo.getTodoList())){
                currentAuditor = String.join(",", joySkyDetailInfo.getTodoList());
            }
            ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId()
                    ,AuditStatusEnum.WAITING_APPROVED.getCode(), currentAuditor ,applyInfoPO.getVersion()
                    ,LoginContextHolder.getLoginContextHolder().getPin());
            applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
            log.info("CountryAgreementPriceApproveServiceImpl.singleApprove 流程未结束 draftPO:{},input:{},applyInfo:{}",JSONObject.toJSONString(draftPO)
                    ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));
            return Boolean.TRUE;
        }

        CountryAgreementPriceDraftPO updatePO = this.getUpdateDraftPO(draftPO,AuditStatusEnum.APPROVED
                ,draftPO.getUpdater(),LoginContextHolder.getLoginContextHolder().getPin(), input.getRejectReason());
        countryAgreementPriceDraftAtomicService.saveOrUpdate(updatePO);
        countryAgreementPriceManageService.saveOrUpdate(this.getCountryAgreementUpdate(draftPO));

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId()
                ,AuditStatusEnum.APPROVED.getCode(), LoginContextHolder.getLoginContextHolder().getPin(),applyInfoPO.getVersion()
                ,LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());

        log.info("CountryAgreementPriceApproveServiceImpl.singleApprove end draftPO:{},input:{},applyInfoMap:{}",JSONObject.toJSONString(draftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean singleReject(CountryAgreementPriceDraftPO draftPO,CountryAgreementPriceAuditVO input,ApplyInfoPO applyInfoPO) {
        log.info("CountryAgreementPriceApproveServiceImpl.singleReject start draftPO:{},input:{},applyInfoPO:{}",JSONObject.toJSONString(draftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));

        if(applyInfoPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        if(draftPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != draftPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        CountryAgreementPriceDraftPO updatePO = this.getUpdateDraftPO(draftPO,AuditStatusEnum.REJECTED
                ,draftPO.getUpdater(),LoginContextHolder.getLoginContextHolder().getPin(), input.getRejectReason());
        countryAgreementPriceDraftAtomicService.saveOrUpdate(updatePO);


        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId()
                ,AuditStatusEnum.REJECTED.getCode(), LoginContextHolder.getLoginContextHolder().getPin(),applyInfoPO.getVersion()
                ,LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());

        this.operateJoySky(ApprovalOperateEnum.REJECT_TO_SUBMIT_NODE,applyInfoPO.getProcessInstanceId()
                ,input.getRejectReason(),LoginContextHolder.getLoginContextHolder().getPin());

        log.info("CountryAgreementPriceApproveServiceImpl.singleReject end draftPO:{},input:{},applyInfoMap:{}",JSONObject.toJSONString(draftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));
        return Boolean.TRUE;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean singleRevoke(CountryAgreementPriceDraftPO draftPO,CountryAgreementPriceAuditVO input,ApplyInfoPO applyInfoPO) {
        log.info("CountryAgreementPriceApproveServiceImpl.singleRevoke start draftPO:{},input:{},applyInfoPO:{}",JSONObject.toJSONString(draftPO)
                ,JSONObject.toJSONString(input),JSONObject.toJSONString(applyInfoPO));

        if(applyInfoPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        if(draftPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != draftPO.getAuditStatus()){
            throw new BizException("当前记录非待审核状态，不允许重复审批，单号:" + applyInfoPO.getApplyCode());
        }

        CountryAgreementPriceDraftPO updatePO = this.getUpdateDraftPO(draftPO,AuditStatusEnum.DRAFT
                ,draftPO.getUpdater(),null, input.getRejectReason());
        countryAgreementPriceDraftAtomicService.saveOrUpdate(updatePO);

        ApplyInfoPO updateApplyInfo = this.getUpdateApplyInfo(applyInfoPO.getId()
                ,AuditStatusEnum.DRAFT.getCode(), LoginContextHolder.getLoginContextHolder().getPin(),applyInfoPO.getVersion()
                ,LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoAtomicService.update(updateApplyInfo,applyInfoPO.getVersion());
        String processInstanceId = applyInfoPO.getProcessInstanceId();

        this.cancelJoySky(processInstanceId,draftPO.getUpdater());

        log.info("CountryAgreementPriceApproveServiceImpl.singleRevoke end draftPO:{},input:{},processInstanceId:{}",JSONObject.toJSONString(draftPO)
                ,JSONObject.toJSONString(input),processInstanceId);
        return Boolean.TRUE;
    }
    

    /**
     * 根据 CustomerSkuPriceDraftPO 对象创建并返回一个新的 CustomerSkuPriceVO 对象。
     * @param draftPO CustomerSkuPriceDraftPO 对象，包含要设置到 CustomerSkuPriceVO 对象的属性信息。
     * @return 一个新的 CustomerSkuPriceVO 对象，包含从 draftPO 对象中获取的属性值。
     */
    public CountryAgreementPriceVO getCountryAgreementUpdate(CountryAgreementPriceDraftPO draftPO){
        CountryAgreementPriceReqVO countryAgreementPriceReqVO = new CountryAgreementPriceReqVO();
        countryAgreementPriceReqVO.setSkuId(draftPO.getSkuId());
        countryAgreementPriceReqVO.setCurrency(draftPO.getCurrency());
        countryAgreementPriceReqVO.setSourceCountryCode(draftPO.getSourceCountryCode());
        countryAgreementPriceReqVO.setTargetCountryCode(draftPO.getTargetCountryCode());
        CountryAgreementPricePO countryAgreementPrice = countryAgreementPriceAtomicService.getCountryAgreementPrice(countryAgreementPriceReqVO);
        if(countryAgreementPrice == null){
            throw new BizException("国家协议价为空");
        }

        CountryAgreementPriceVO countryAgreementPriceVO = new CountryAgreementPriceVO();
        BeanUtil.copyProperties(countryAgreementPrice,countryAgreementPriceVO);
        countryAgreementPriceVO.setAgreementPrice(draftPO.getAgreementPrice());
        countryAgreementPriceVO.setUpdater(draftPO.getUpdater());
        countryAgreementPriceVO.setUpdateTime(DateUtil.getCurrentTime());
        countryAgreementPriceVO.setDataSourceTypeEnums(AgreementPriceDataSourceTypeEnums.AGREE);
        countryAgreementPriceVO.setAgreementUpdateTime(Instant.now().toEpochMilli());
        return countryAgreementPriceVO;
    }

    /**
     * 获取审批流程详细信息。
     * @param processInstanceId 流程实例ID。
     * @param subData 是否需要子表数据。
     * @param todoList 是否需要待办列表。
     * @return 审批流程详细信息DTO。
     */
    protected JoySkyDetailInfoDTO getJoySkyDetailInfo(String processInstanceId
            ,Boolean subData,Boolean todoList){
        // 审批通过获取回填数据
        JoySkyQueryApiDTO joySkyQueryApiDTO = new JoySkyQueryApiDTO();
        joySkyQueryApiDTO.setJoySkyFlowType(flowTypeEnum.getFlowTypeCode());
        joySkyQueryApiDTO.setProcessInstanceId(processInstanceId);
        joySkyQueryApiDTO.setNeedSubTableData(subData);
        joySkyQueryApiDTO.setNeedTodoList(todoList);
        JoySkyDetailInfoDTO joySkyDetailInfoDTO = joySkyApproveRpcService.queryFlowModel(joySkyQueryApiDTO);
        return joySkyDetailInfoDTO;
    }

    protected ApplyInfoPO initApplyInfo(Long id,JoySkyDetailInfoDTO joySkyDetailInfoDTO){
        ApplyInfoPO applyInfoPO = new ApplyInfoPO();
        applyInfoPO.setProcessType(flowTypeEnum.getFlowTypeCode());
        applyInfoPO.setBizId(id.toString());
        applyInfoPO.setProcessInstanceId(joySkyDetailInfoDTO.getProcessInstanceId());
        applyInfoPO.setApplyCode(joySkyDetailInfoDTO.getApplyCode());
        applyInfoPO.setAuditStatus(AuditStatusEnum.WAITING_APPROVED.getCode());
        if(CollectionUtils.isNotEmpty(joySkyDetailInfoDTO.getTodoList())){
            String currentAuditor = String.join(",", joySkyDetailInfoDTO.getTodoList());
            applyInfoPO.setCurrentAuditor(currentAuditor);
        }
        applyInfoPO.setVersion(0);
        applyInfoPO.setCreator(LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoPO.setCreateTime(new Date().getTime());
        applyInfoPO.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
        applyInfoPO.setUpdateTime(new Date().getTime());
        return applyInfoPO;
    }

    /**
     * 更新申请信息
     * @param id 申请信息ID
     * @param auditStatus 审核状态
     * @param currentAuditor 当前审核人
     * @param version 版本号
     * @param updater 更新人
     * @return 更新后的申请信息对象
     */
    protected ApplyInfoPO getUpdateApplyInfo(Long id,Integer auditStatus,String currentAuditor,Integer version,String updater){
        ApplyInfoPO updateApplyInfoPO = new ApplyInfoPO();
        updateApplyInfoPO.setId(id);
        updateApplyInfoPO.setAuditStatus(auditStatus);
        updateApplyInfoPO.setCurrentAuditor(currentAuditor);
        updateApplyInfoPO.setUpdater(updater);
        updateApplyInfoPO.setVersion(version+1);
        return updateApplyInfoPO;
    }

    /**
     * 执行JoySky审批操作
     * @param operateEnum 审批操作枚举
     * @param processInstanceId 流程实例ID
     * @param comment 审批备注
     * @param erp ERP系统信息
     * @return 操作结果
     */
    protected Boolean operateJoySky(ApprovalOperateEnum operateEnum,String processInstanceId,String comment,String erp){
        JoySkyOperateApiDTO joySkyOperateApiDTO = new JoySkyOperateApiDTO();
        joySkyOperateApiDTO.setJoySkyFlowType(flowTypeEnum.getFlowTypeCode());
        joySkyOperateApiDTO.setOperateEnum(operateEnum);
        joySkyOperateApiDTO.setProcessInstanceId(processInstanceId);
        joySkyOperateApiDTO.setComment(comment);
        joySkyOperateApiDTO.setErp(erp);
        return joySkyApproveRpcService.operateSky(joySkyOperateApiDTO);
    }

    /**
     * 取消产品销售价格审批流程
     * @param processInstanceId 流程实例ID
     * @param erp ERP系统标识
     * @return 取消结果
     */
    protected Boolean cancelJoySky(String processInstanceId,String erp){
        JoySkyCancelApiDTO joySkyCancelApiDTO = new JoySkyCancelApiDTO();
        joySkyCancelApiDTO.setErp(erp);
        joySkyCancelApiDTO.setJoySkyFlowType(JoySkyBizFlowTypeEnum.PRODUCT_SALE_PRICE_FLOW.getFlowTypeCode());
        joySkyCancelApiDTO.setCancelComment("用户主动取消");
        joySkyCancelApiDTO.setProcessInstanceId(processInstanceId);
        return joySkyApproveRpcService.cancelSkyApprovalFlow(joySkyCancelApiDTO);
    }

    /**
     * 根据给定的参数更新 CustomerSkuPriceDetailDraftPO 对象并返回。
     * @param draftPO 要更新的 CustomerSkuPriceDetailDraftPO 对象。
     * @param auditStatusEnum 审核状态枚举值。
     * @param updater 更新人。
     * @param auditErp 审核ERP。
     * @param rejectReason 拒绝原因。
     * @return 更新后的 CustomerSkuPriceDetailDraftPO 对象。
     */
    protected CountryAgreementPriceDraftPO getUpdateDraftPO(CountryAgreementPriceDraftPO draftPO
            , AuditStatusEnum auditStatusEnum, String updater, String auditErp, String rejectReason){
        CountryAgreementPriceDraftPO updatePO = new CountryAgreementPriceDraftPO();
        updatePO.setId(draftPO.getId());
        updatePO.setAuditStatus(auditStatusEnum.getCode());
        updatePO.setAuditor(auditErp);
        updatePO.setUpdater(updater);
        updatePO.setUpdateTime(new Date().getTime());
        updatePO.setRejectReason(rejectReason);
        return updatePO;
    }

    /**
     * 构建过程表单数据模型。
     * @param input 客户协议价格信息。
     * @return 表单数据模型。
     */
    private Map<String, String> constructProcessFormDataModel(CountryAgreementPriceVO input){
        if(input.getAgreementPrice() == null){
            throw new BizException("国家协议价为空");
        }
        CountryAgreementPriceCalculateResVO calculateResVO = input.getCountryAgreementPriceCalculateResVO();
        if(calculateResVO.getProfitLimitRate() == null){
            throw new BizException("利润率阈值为空");
        }
        if(calculateResVO.getLowProfitLimitRate() == null){
            throw new BizException("超低利润率阈值为空");
        }
        if(calculateResVO.getCountryCostPrice() == null){
            throw new BizException("国家成本价为空");
        }
        if(calculateResVO.getProfitRate() == null){
            throw new BizException("利润率为空");
        }

        Map<String, String> fieldCommentToFieldNameMap = new HashMap<>(9);
        fieldCommentToFieldNameMap.put("国际SKUID", input.getSkuId().toString());
        fieldCommentToFieldNameMap.put("商品名称", this.querySkuName(input.getSkuId()));
        fieldCommentToFieldNameMap.put("国家协议价", calculateResVO.getCountryAgreementPrice().stripTrailingZeros().toPlainString() + calculateResVO.getCurrency());
        fieldCommentToFieldNameMap.put("国家成本价", calculateResVO.getCountryCostPrice().stripTrailingZeros().toPlainString() + calculateResVO.getCurrency());
        fieldCommentToFieldNameMap.put("利润率", calculateResVO.getProfitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        fieldCommentToFieldNameMap.put("提交人", LoginContextHolder.getLoginContextHolder().getPin());
        fieldCommentToFieldNameMap.put("利润率预警信息",calculateResVO.getWarningMsg());
        fieldCommentToFieldNameMap.put("利润率阈值", calculateResVO.getProfitLimitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        fieldCommentToFieldNameMap.put("超低利润率阈值", calculateResVO.getLowProfitLimitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        fieldCommentToFieldNameMap.put("备注", input.getRemark());

        // 国家协议价（调价后）
        fieldCommentToFieldNameMap.put("国家协议价（调价后）", calculateResVO.getCountryAgreementPrice().stripTrailingZeros().toPlainString() + calculateResVO.getCurrency());
        // 利润率-前毛（调价后）
        fieldCommentToFieldNameMap.put("利润率-前毛（调价后）", calculateResVO.getProfitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        // 超低利润率阈值-前毛
        fieldCommentToFieldNameMap.put("超低利润率阈值-前毛", calculateResVO.getLowProfitLimitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        // 利润率阈值-前毛
        fieldCommentToFieldNameMap.put("利润率阈值-前毛", calculateResVO.getProfitLimitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        // 国家协议价调整幅度（调价前后对比） = 国家协议价（调价后）/ 国家协议价（调价前） - 1，两位四舍五入，15.65%百分比
        fieldCommentToFieldNameMap.put("国家协议价调整幅度（调价前后对比）", this.getAgreementPriceAdjustment(input.getOriginAgreementPrice(), calculateResVO.getCountryAgreementPrice()));

        // 国家协议价（调价前）
        fieldCommentToFieldNameMap.put("国家协议价（调价前）", input.getOriginAgreementPrice() == null ? Constant.MINUS : input.getOriginAgreementPrice().stripTrailingZeros().toPlainString() + calculateResVO.getCurrency());

        // 利润率-前毛（调价前）
        fieldCommentToFieldNameMap.put("利润率-前毛（调价前）", this.calculateSalePriceProfitRate(input.getOriginAgreementPrice(), input.getOriginCountryCostPrice()));

        // 调价原因
        fieldCommentToFieldNameMap.put("调价原因", input.getAdjustmentPriceReason());
        // 附件（用于页面展示）
        fieldCommentToFieldNameMap.put("调价附件", com.jdi.isc.product.soa.common.util.StringUtils.listToString(input.getAttachmentUrls()));
        // 边际负毛阈值
        fieldCommentToFieldNameMap.put("边际负毛阈值", operDuccConfig.getGrossProfit(input.getTargetCountryCode()).multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());

        return fieldCommentToFieldNameMap;
    }

    private String calculateSalePriceProfitRate(BigDecimal agreementPrice, BigDecimal countryCostPrice) {

        if (agreementPrice == null || BigDecimalUtil.eq0(agreementPrice)) {
            return StringUtils.EMPTY;
        }

        if (countryCostPrice == null || BigDecimalUtil.eq0(countryCostPrice)) {
            return StringUtils.EMPTY;
        }

        return SpringUtil.getBean(ProfitCalculateManageService.class).calculateAgreementProfitRate(agreementPrice, countryCostPrice)
                .multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString();
    }

    /**
     * 计算协议价格调整百分比
     * @param originCustomerSalePrice 原始客户销售价格
     * @param customerSalePrice 调整后的客户销售价格
     * @return 返回价格调整百分比字符串，保留两位小数(如"-"表示无法计算)
     */
    private String getAgreementPriceAdjustment(BigDecimal originCustomerSalePrice, BigDecimal customerSalePrice) {

        if (originCustomerSalePrice == null || customerSalePrice == null) {
            return StringUtils.EMPTY;
        }

        if (originCustomerSalePrice.compareTo(BigDecimal.ZERO) == 0) {
            return StringUtils.EMPTY;
        }

        return customerSalePrice
                .divide(originCustomerSalePrice, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.ONE)
                .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toPlainString();
    }

    /**
     * 构建流程控制数据。
     * @param sourceCountryCode 商品来源国代码
     * @param targetCountryCode 客户所在国代码
     * @return 流程控制参数映射
     */
    private Map<String, Object> constructProcessControlData(String sourceCountryCode,String targetCountryCode) {
        //流程控制参数校验
        if (StringUtils.isBlank(sourceCountryCode) || StringUtils.isBlank(targetCountryCode)) {
            throw new BizException("流程控制参数不能为空!,商品来源国:"+sourceCountryCode+",客户所在国:" + targetCountryCode);
        }

        //流程参数赋值
        Map<String, Object> flowCalculeVarMap = new HashMap<>(3);
        flowCalculeVarMap.put("productCountry", sourceCountryCode);
        flowCalculeVarMap.put("customerCountry", targetCountryCode);
        return flowCalculeVarMap;
    }

    /**
     * 根据 SKU ID 查询商品标题信息。
     * @param skuId SKU 的唯一标识符。
     * @return 中文商品标题，若查询不到则返回 null。
     */
    private String querySkuName(Long skuId) {
        SkuExternalReqVO skuQuery = new SkuExternalReqVO();
        skuQuery.setSkuIds(Lists.newArrayList(skuId));
        skuQuery.setScopeEnums(Sets.newHashSet(SkuScopeEnum.BASE, SkuScopeEnum.LANG));

        Map<Long,ExternalVO> skuExternalVOMap = skuExternalManageService.querySkuInfo(skuQuery);
        if(MapUtils.isEmpty(skuExternalVOMap)){
            return null;
        }
        ExternalVO externalVO = skuExternalVOMap.get(skuId);
        if(externalVO == null){
            return null;
        }
        SpuLangVO spuLangVO = externalVO.getLangVOList().stream()
                .filter(line -> LangConstant.LANG_ZH.equals(line.getLang()))
                .findFirst().orElse(null);
        return spuLangVO != null ? spuLangVO.getSpuTitle() : null;
    }
}
