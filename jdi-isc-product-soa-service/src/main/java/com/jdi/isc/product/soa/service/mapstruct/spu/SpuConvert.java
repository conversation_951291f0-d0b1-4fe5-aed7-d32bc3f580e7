package com.jdi.isc.product.soa.service.mapstruct.spu;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.spu.req.*;
import com.jdi.isc.product.soa.api.spu.req.BatchProductReqDTO;
import com.jdi.isc.product.soa.api.spu.req.SaveSpuApiDTO;
import com.jdi.isc.product.soa.api.spu.req.SpuAmendReqDTO;
import com.jdi.isc.product.soa.api.spu.req.SpuAuditRecordUpdateApiDTO;
import com.jdi.isc.product.soa.api.spu.res.*;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.domain.attribute.biz.*;
import com.jdi.isc.product.soa.domain.category.biz.GlobalQualificationVO;
import com.jdi.isc.product.soa.domain.certificate.biz.CertificateVO;
import com.jdi.isc.product.soa.domain.common.biz.BaseLangVO;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.domain.enums.product.SaleUnitEnum;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.*;
import com.jdi.isc.product.soa.domain.task.excel.ImportSpuExcelDTO;
import com.jdi.isc.product.soa.domain.task.excel.ImportThSpuExcelDTO;
import com.jdi.isc.product.soa.domain.task.excel.ImportVnSpuExcelDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.*;

/**
 * Spu对象转化
 *
 * <AUTHOR>
 * @date 2023/11/26
 **/
@Mapper
public interface SpuConvert {

    SpuConvert INSTANCE = Mappers.getMapper(SpuConvert.class);

    @InheritConfiguration
    @Mapping(target = "detailImg", expression = "java(detailImage2Str(spuVO.getDetailImgList()))")
    @Mapping(source = "catId", target = "jdCatId")
    SpuPO dto2po(SpuVO spuVO);

    default String detailImage2Str(List<String> detailImageList) {
        if (CollectionUtils.isEmpty(detailImageList)) {
            return "";
        }
        return String.join(Constant.HASHTAG, detailImageList);
    }

    @InheritConfiguration
    @Mapping(target = "detailImgList",expression = "java(detailImage2ImageList(spuPO.getDetailImg()))")
    @Mapping(source = "jdCatId", target = "catId")
    SpuVO po2vo(SpuPO spuPO);

    default String saleUnit(Integer saleUnit){
        if (Objects.isNull(saleUnit)) {
            return "";
        }
        return SaleUnitEnum.unitNameByCodeLang(saleUnit, LangConstant.LANG_ZH);
    }

    default List<String> detailImage2ImageList(String detailImage) {
        if (StringUtils.isBlank(detailImage)) {
            return null;
        }

        String[] imageArray = detailImage.split(Constant.HASHTAG);
        return Arrays.asList(imageArray);
    }

    /**
     * 页面保存数据转换为PO
     *
     * @param saveSpuVO
     * @return
     */
    @InheritConfiguration
    SpuPO saveVOtoPO(SaveSpuVO saveSpuVO);

    /**
     * spuPo转为页面详情对象
     *
     * @param spuPO
     * @return
     */
    @InheritConfiguration
    SpuDetailVO spuPo2DetailVO(SpuPO spuPO);

    /**
     * 审核记录对象转换
     *
     * @param spuAuditRecordPO
     * @return
     */
    @InheritConfiguration
    SpuAuditRecordPO recordPo2Dto(SpuAuditRecordPO spuAuditRecordPO);

    /**
     * 类目中属性值对象转化商品属性值
     *
     * @param attributeValueVO
     * @return
     */
    @Mapping(source = "id", target = "attributeValueId")
    PropertyValueVO attributeValueVo2PropertyValueVo(AttributeValueVO attributeValueVO);

    /**
     * 类目中属性值对象转化商品属性值
     *
     * @param attributeValueFlatVO
     * @return
     */
    @Mapping(source = "id", target = "attributeValueId")
    @Mapping(source = "langName", target = "attributeValueName")
    default PropertyValueVO attributeFlatValueVo2PropertyValueVo(AttributeValueFlatVO attributeValueFlatVO) {
        if (attributeValueFlatVO == null) {
            return null;
        }

        PropertyValueVO propertyValueVO = new PropertyValueVO();

        propertyValueVO.setAttributeValueId(attributeValueFlatVO.getId());
        propertyValueVO.setAttributeValueName(attributeValueFlatVO.getLangName());
        propertyValueVO.setAttributeId(attributeValueFlatVO.getAttributeId());
        propertyValueVO.setSort(attributeValueFlatVO.getSort());
        // for循环设置langList
        List<AttributeValueLangVO> langList = attributeValueFlatVO.getLangList();
        propertyValueVO.setLangList(new ArrayList<>());
        if(CollectionUtils.isNotEmpty(langList)){
            List<BaseLangVO> newLangList = new ArrayList<>();
            for(AttributeValueLangVO langVO : langList){
                if(langVO != null){
                    BaseLangVO newLangVO = new BaseLangVO();    
                    newLangVO.setLang(langVO.getLang());
                    newLangVO.setLangName(langVO.getLangName());
                    newLangList.add(newLangVO);
                }
            }
            propertyValueVO.setLangList(newLangList);
        }
        return propertyValueVO;
    }


    List<PropertyValueVO> listAttributeFlatValueVo2PropertyValueVo(List<AttributeValueFlatVO> list);

    /**
     * 类目的属性转换商品用属性
     *
     * @param attributeVO
     * @return
     */
    @Mapping(source = "id", target = "attributeId")
    // @Mapping(source = "attributeValueList", target = "propertyValueVOList")
    PropertyVO attributeVo2PropertyVo(AttributeVO attributeVO);

    /**
     * 类目的属性转换商品用属性
     *
     * @param attributeFlatVO
     * @return
     */
    @Mapping(source = "id", target = "attributeId")
    // @Mapping(source = "attributeValueList", target = "propertyValueVOList")
    @Mapping(target = "required", expression = "java(attributeFlatVO.getRequired() != null &&  attributeFlatVO.getRequired() > 0 ? true : false)", resultType = Boolean.class)
    PropertyVO attributeFlatVo2PropertyVo(AttributeFlatVO attributeFlatVO);

    @Mapping(source = "id", target = "certificateId")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "updater", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    SpuCertificateVO certificateVo2SpuCertificateVo(CertificateVO certificateVO);

    /**
     * spu资质vo->po
     *
     * @param spuCertificateVO
     * @return
     */
    @InheritConfiguration
    SpuCertificatePO certificateVo2po(SpuCertificateVO spuCertificateVO);


    default List<SpuCertificatePO> listCertificateVo2po(Long spuId, List<SpuCertificateVO> spuCertificateVOList) {
        if (CollectionUtils.isEmpty(spuCertificateVOList)) {
            return null;
        }

        List<SpuCertificatePO> poList = Lists.newArrayList();
        for (SpuCertificateVO vo : spuCertificateVOList) {
            Optional.ofNullable(vo.getIsLong()).ifPresent(isLong->{
                if(isLong.equals(YesOrNoEnum.YES.getCode())){
                    vo.setCertEndTime(null);
                }
            });
            poList.add(certificateVo2PoSetSpuId(spuId, vo));
        }
        return poList;
    }

    default SpuCertificatePO certificateVo2PoSetSpuId(Long spuId, SpuCertificateVO spuCertificateVO) {
        SpuCertificatePO spuCertificatePo = certificateVo2po(spuCertificateVO);
        spuCertificatePo.setCreator(null);
        spuCertificatePo.setCreateTime(null);
        spuCertificatePo.setUpdater(null);
        spuCertificatePo.setUpdateTime(null);
        spuCertificatePo.setYn(null);
        spuCertificatePo.setSpuId(spuId);
        return spuCertificatePo;
    }

    /**
     * spu资质po->vo
     *
     * @param spuCertificatePO
     * @return
     */
    @InheritConfiguration
    SpuCertificateVO certificatePo2Vo(SpuCertificatePO spuCertificatePO);

    /**
     * spu资质数组vo->po
     *
     * @param spuCertificateVOList
     * @return
     */
    @InheritConfiguration
    List<SpuCertificatePO> listSpuCertificateVo2Po(List<SpuCertificateVO> spuCertificateVOList);

    List<SpuLangVO> listLangPo2Vo(List<SpuLangPO> spuLangPOList);

    List<SpuDescLangVO> listDescLangPo2Vo(List<SpuDescLangPO> spuDescLangPOList);

    SpuDetailVO saveVo2DetailVo(SaveSpuVO saveVo);

    @Mapping(source = "catId",target = "jdCatId")
    SpuDraftPO draftVo2Po(SpuDraftVO draftVO);

    @Mapping(source = "jdCatId",target = "catId")
    SpuDraftVO draftPo2Vo(SpuDraftPO draftPo);

    SaveSpuVO detailVo2SaveVo(SpuDetailVO detailVO);

    @Mapping(target = "propertyValueVOList", ignore = true)
    PropertyVO oldPropertyVo2NewVo(PropertyVO propertyVO);

    @Mapping(source = "pin",target = "buyer")
    @Mapping(source = "fourCatId",target = "catId")
    @Mapping(source = "spuMainImage",target = "mainImg")
    @Mapping(source = "spuDetailImageList",target = "detailImgList")
    SpuVO importSpuVo2SpuVo(ImportSpuExcelDTO excelDTO);


    @Mapping(source = "fourCatId",target = "catId")
    @Mapping(source = "spuMainImage",target = "mainImg")
    @Mapping(source = "spuDetailImageList",target = "detailImgList")
    @Mapping(target = "saleUnit",expression = "java(parseSaleUnit(excelDTO.getSaleUnit()))")
    SpuVO vnImportExcelVo2SpuVo(ImportVnSpuExcelDTO excelDTO);

    default Integer parseSaleUnit(String saleUnit) {
        if (StringUtils.isNotBlank(saleUnit) && StringUtils.isNumeric(saleUnit)){
            return Integer.parseInt(saleUnit);
        }
        return null;
    }

    @Mapping(source = "fourCatId",target = "catId")
    @Mapping(source = "spuMainImage",target = "mainImg")
    @Mapping(source = "spuDetailImageList",target = "detailImgList")
    @Mapping(target = "saleUnit",expression = "java(parseSaleUnit(excelDTO.getSaleUnit()))")
    SpuVO thImportExcelVo2SpuVo(ImportThSpuExcelDTO excelDTO);

    SpuPrepareInfoApiDTO spuPrepareInfoVo2Dto(SpuPrepareInfoVO input);

    SaveSpuVO saveSpuDto2Vo(SaveSpuApiDTO input);

    SpuDetailApiDTO detailVo2DTO(SpuDetailVO result);

    List<SaleUnitApiDTO> listSaleUnitVO2Dto(List<SaleUnitVO> input);

    SpuAuditRecordUpdateVO spuAuditDto2Vo(SpuAuditRecordUpdateApiDTO input);

    SpuBatchReqVO productBatchDto2Vo(BatchProductReqDTO input);

    @Mapping(source = "id", target = "certificateId")
    @Mapping(source = "checkType", target = "requirement")
    @Mapping(source = "qualificationRemark", target = "certificateNote")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "updater", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    SpuCertificateVO globalCertificateVo2SpuCertificateVo(GlobalQualificationVO input);

    List<SpuLangExtendVO> listLangExtendPo2Vo(List<SpuLangPO> inputs);


    List<SpuUpdateReqVO> apiList2ReqVO (List<SpuUpdateApiDTO> list);

    List<SpuUpdateApiDTO> reqVO2ApiList (List<SpuUpdateReqVO> list);

    @Mapping(source = "createTime", target = "createTime",ignore = true)
    @Mapping(source = "updateTime", target = "updateTime",ignore = true)
    List<SpuPO> reqVoList2SpuPoList(List<SpuUpdateReqVO> list);

    List<SpuLangPO> langApiList2spuLangList(List<SpuLangApiDTO> list);

    List<SpuLangApiDTO> spuLangList2langApiList(List<SpuLangPO> spuLangPOList);

    List<SpuDescLangPO> langApiList2spuDescLangList(List<SpuLangApiDTO> list);
    default Long map(Date value) {
        return value != null ? value.getTime() : null;
    }

    default Date map(Long value) {
        return value != null ? new Date(value) : null;
    }
    List<SpuAmendReqVO> listAmendReqDto2Vo(List<SpuAmendReqDTO> inputs);

    SpuDetailReqVO reqApiDTO2ReqVO(SpuDetailReqApiDTO reqApiDTO);

    List<SpuAmendApiDTO>  listSpuAmendVO2ListSpuAmendApiDTO (List<SpuAmendVO> spuAmendVOList);


    List<SpuAmendVO>  listSpuAmendApiDTO2ListSpuAmendVO (List<SpuAmendApiDTO> SpuAmendApiDTOList);

    SpuQueryReqVO spuQueryApiDto2ReqVO(SpuQueryReqApiDTO input);

    PageInfo<SpuApiDTO> spuPageVo2ApiDto(PageInfo<SpuVO> input);

    /**
     * 将 AuditNumVo 列表转换为 AuditNumDTO 列表。
     * @param auditNumVOList AuditNumVo 对象列表
     * @return 转换后的 AuditNumDTO 对象列表
     */
    List<AuditNumDTO> auditNumVoList2DtoList(List<AuditNumVO> auditNumVOList);


    /**
     * 将SpuUpdatePriceReqDTO对象转换为SpuUpdatePriceReqVO对象
     * @param dto SpuUpdatePriceReqDTO对象，包含需要更新的商品价格信息
     * @return 转换后的SpuUpdatePriceReqVO对象
     */
    SpuUpdatePriceReqVO spuUpdatePriceDTO2VO(SpuUpdatePriceReqDTO dto);

    /**
     * 将 SpuPrepareReqApiDTO 对象转换为 SpuPrepareReqApiVO 对象。
     * @param dto SpuPrepareReqApiDTO 对象，待转换的数据传输对象。
     * @return 转换后的 SpuPrepareReqApiVO 对象。
     */
    SpuPrepareReqVO spuPrepareReqApiDTO2VO (SpuPrepareReqApiDTO dto);


    SpuUpdateReqVO api2ReqVO (SpuUpdateApiDTO dto);

    SpuUpdateApiDTO reqVO2Api (SpuUpdateReqVO reqVO);

    SpuDescriptionUpdateVO apiDescription2ReqVO (SpuDescriptionUpdateApiDTO dto);

    SpuAuditTaxApproveVO spuTaxAuditDto2Vo(SpuAuditTaxApproveDTO input);
}
