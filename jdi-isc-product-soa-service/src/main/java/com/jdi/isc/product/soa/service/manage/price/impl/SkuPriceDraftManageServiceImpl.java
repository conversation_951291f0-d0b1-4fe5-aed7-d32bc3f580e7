package com.jdi.isc.product.soa.service.manage.price.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.biz.component.api.enums.JoySkyBizFlowTypeEnum;
import com.jdi.isc.product.soa.api.common.*;
import com.jdi.isc.product.soa.api.common.enums.CalculatePriceEnums;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.domain.apply.po.ApplyInfoPO;
import com.jdi.isc.product.soa.domain.customerSku.biz.PurchasePriceApproveReqVO;
import com.jdi.isc.product.soa.domain.enums.AuditFormEnum;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.SkuScopeEnum;
import com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPriceDraftPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.biz.ProfitRateVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPurchasePriceAuditVO;
import com.jdi.isc.product.soa.domain.price.po.PriceLogPO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.price.supplierPrice.biz.*;
import com.jdi.isc.product.soa.domain.price.supplierPrice.draft.SkuPriceDraftVO;
import com.jdi.isc.product.soa.domain.price.supplierPrice.po.SkuPriceDraftPO;
import com.jdi.isc.product.soa.domain.sku.biz.ExternalVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCalculatePriceReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCalculateTaxPriceVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuExternalReqVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuLogPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangVO;
import com.jdi.isc.product.soa.domain.spu.po.ProductGlobalAttributePO;
import com.jdi.isc.product.soa.domain.spu.po.SpuLangPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.taxRate.req.PurchaseTaxQueryReqVO;
import com.jdi.isc.product.soa.domain.taxRate.res.PurchaseSkuTaxResVO;
import com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum;
import com.jdi.isc.product.soa.service.atomic.apply.ApplyInfoAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.PriceLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.ProductGlobalAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.SupplierAllInfoAtomicService;
import com.jdi.isc.product.soa.service.manage.brand.BrandOutService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.price.*;
import com.jdi.isc.product.soa.service.manage.sku.SkuExternalManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.PurchaseTaxManageService;
import com.jdi.isc.product.soa.service.manage.vendor.VendorManageService;
import com.jdi.isc.product.soa.service.mapstruct.ProductAttributeConvertService;
import com.jdi.isc.product.soa.service.mapstruct.price.SkuPriceDraftConvert;
import com.jdi.isc.product.soa.service.support.audit.AuditService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.service.manage.product.impl.GlobalAttributeWriteManageServiceImpl.*;

@Slf4j
@Service
public class SkuPriceDraftManageServiceImpl implements SkuPriceDraftManageService {

    @Resource
    private SkuPriceDraftAtomicService skuPriceDraftAtomicService;
    @Resource
    private SkuPriceAtomicService skuPriceAtomicService;
    @Resource
    private SkuLogAtomicService skuLogAtomicService;
    @Resource
    private PriceLogAtomicService priceLogAtomicService;
    @Resource
    @Lazy
    private SkuPurchasePriceApproveManageService skuPurchasePriceApproveManageService;
    @Resource
    private ApplyInfoAtomicService applyInfoAtomicService;
    @Resource
    private SkuExternalManageService skuExternalManageService;
    @Resource
    private ProfitCalculateManageService profitCalculateManageService;
    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private SkuPriceManageService skuPriceManageService;
    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;
    @Resource
    private CountryManageService countryManageService;
    @Resource
    private CategoryOutService categoryOutService;
    @Resource
    private SupplierAllInfoAtomicService supplierAllInfoAtomicService;
    @Resource
    private VendorManageService vendorManageService;
    @Resource
    private BrandOutService brandOutService;

    @Resource
    private SkuReadManageService skuReadManageService;

    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;

    @Resource
    private SpuLangAtomicService spuLangAtomicService;

    @Resource
    private PurchaseTaxManageService purchaseTaxManageService;

    @Resource
    private ProductAttributeConvertService productAttributeConvertService;

    @Resource
    private ExchangeRateManageService exchangeRateManageService;

    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;

    @Resource
    private AuditService auditService;

    @Resource
    private OperDuccConfig operDuccConfig;

    private DataResponse<Boolean> save(SkuPriceDraftVO vo) {
        log.info("SkuPriceDraftManageServiceImpl save SkuPriceDraftVO:{}",JSON.toJSONString(vo));
        SkuPriceQueryReqVO skuPriceQueryReqVO = new SkuPriceQueryReqVO();
        skuPriceQueryReqVO.setSkuId(vo.getSkuId());
        skuPriceQueryReqVO.setSourceCountryCode(vo.getSourceCountryCode());
        skuPriceQueryReqVO.setTradeDirection(TradeDirectionEnum.SUPPLIER);
        SkuPricePO skuPricePO = skuPriceAtomicService.getSkuPricePO(skuPriceQueryReqVO);
        if (skuPricePO == null) {
            log.info("基础采购价未查询到信息");
            return DataResponse.error("基础采购价未查询到信息");
        }
        ProfitRateVO profitRateVO = profitCalculateManageService.getProfitLimitRate(vo.getCatId(), vo.getSourceCountryCode(), vo.getSourceCountryCode());
        if (profitRateVO == null) {
            log.info("利润率阈值为空");
            return DataResponse.error("利润率阈值为空");
        }
        if(StringUtils.equals(CountryConstant.COUNTRY_BR,vo.getSourceCountryCode())
                && CollectionUtils.isEmpty(vo.getPropertyVos())){
            throw new BizException("巴西税金不许为空");
        }

        this.setWarningMsg(vo, profitRateVO);

        SkuPriceDraftPO po = this.prepareDraftPO(skuPricePO, vo, profitRateVO);
        if (!saveDraft(po)) {
            log.warn("saveOrUpdate, SkuPriceDraftPO fail. SkuPriceDraftPO={}", JSONObject.toJSONString(po));
            return DataResponse.success(false);
        }

        PurchasePriceApproveReqVO data = this.submitApproval(po, skuPricePO, vo, profitRateVO);
        Map<String, String> processFromData = data.getProcessFromData();

        // 更新表单数据
        this.updateProcessFromData(po.getId(), processFromData);

        this.logPriceChange(po);
        this.sendLog(String.valueOf(po.getSkuId()), vo, po);
        return DataResponse.success(Boolean.TRUE);
    }

    private void updateProcessFromData(Long id, Map<String, String> processFromData) {
        Preconditions.checkArgument(id != null, "保存失败， id is null");
        if (MapUtils.isNotEmpty(processFromData)) {
            SkuPriceDraftPO update = new SkuPriceDraftPO();
            update.setId(id);
            update.setProcessFromData(JSONObject.toJSONString(processFromData));
            boolean flag = skuPriceDraftAtomicService.updateById(update);
            Preconditions.checkArgument(flag, "更新表单数据失败！");
        }
    }

    /**
     * 提交采购价格审批请求。
     * @param po SkuPriceDraftPO 对象，包含待提交审批的 SKU 价格信息。
     * @param skuPricePO SkuPricePO 对象，包含 SKU 的基本价格信息。
     * @param vo SkuPriceDraftVO 对象，包含 SKU 的分类信息。
     * @param profitRateVO ProfitRateVO 对象，包含 SKU 的利润率信息。
     */
    private PurchasePriceApproveReqVO submitApproval(SkuPriceDraftPO po,SkuPricePO skuPricePO, SkuPriceDraftVO vo, ProfitRateVO profitRateVO) {
        PurchasePriceApproveReqVO purchasePriceApproveReqVO = new PurchasePriceApproveReqVO();
        purchasePriceApproveReqVO.setId(po.getId());
        purchasePriceApproveReqVO.setSpuId(skuPricePO.getSpuId());
        purchasePriceApproveReqVO.setSkuId(po.getSkuId());
        purchasePriceApproveReqVO.setCatId(vo.getCatId());
        purchasePriceApproveReqVO.setSourceCountryCode(skuPricePO.getSourceCountryCode());
        purchasePriceApproveReqVO.setCurrency(po.getCurrency());
        purchasePriceApproveReqVO.setPurchasePrice(po.getPrice());
        purchasePriceApproveReqVO.setTaxPurchasePrice(po.getTaxPrice());
        purchasePriceApproveReqVO.setCostCurrency(po.getCostCurrency());
        purchasePriceApproveReqVO.setCountryCostPrice(po.getCountryCostPrice());
        purchasePriceApproveReqVO.setCountryAgreementPrice(po.getAgreementPrice());
        purchasePriceApproveReqVO.setProfitRate(po.getProfitRate());
        purchasePriceApproveReqVO.setProfitLimitRate(profitRateVO.getProfitRate());
        purchasePriceApproveReqVO.setLowProfitLimitRate(profitRateVO.getLowProfitRate());
        purchasePriceApproveReqVO.setWarningMsg(po.getWarningMsg());
        purchasePriceApproveReqVO.setRemark(po.getRemark());
        purchasePriceApproveReqVO.setAdjustmentPriceReason(po.getAdjustmentPriceReason());
        purchasePriceApproveReqVO.setOriginPurchasePrice(skuPricePO.getPrice());
        purchasePriceApproveReqVO.setOriginTaxPurchasePrice(skuPricePO.getTaxPrice());

        purchasePriceApproveReqVO.setAttachmentUrls(vo.getAttachmentUrls());

        // 利润率(原)
        CountryAgreementPricePO countryAgreementPrice = countryAgreementPriceAtomicService.getBySkuIdAndTargetCountryCode(skuPricePO.getSkuId(), skuPricePO.getSourceCountryCode());
        if (countryAgreementPrice != null) {
            BigDecimal agreementProfitRate = profitCalculateManageService.calculateAgreementProfitRate(countryAgreementPrice.getAgreementPrice(), countryAgreementPrice.getCountryCostPrice());
            purchasePriceApproveReqVO.setOriginProfitRate(agreementProfitRate.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
            log.info("利润率（原）{} = 1 - 国家成本价:{} / 国家协议价:{}", purchasePriceApproveReqVO.getOriginProfitRate(), countryAgreementPrice.getCountryCostPrice(), countryAgreementPrice.getAgreementPrice());
        } else {
            log.warn("submitApproval, 找不到协议价. sku={}, sourceCountryCode={}", skuPricePO.getSkuId(), skuPricePO.getSourceCountryCode());
        }

        // 汇率 币种怎么取
        DataResponse<BigDecimal> dataResponse = exchangeRateManageService.getExchangeRateByCurrency(po.getCurrency(), CurrencyConstant.CURRENCY_ZH);

        if (dataResponse.getSuccess()) {
            // 采购价人民币参考金额(原)
            purchasePriceApproveReqVO.setOriginPurchasePriceRmb(purchasePriceApproveReqVO.getOriginPurchasePrice().multiply(dataResponse.getData()).setScale(2, RoundingMode.HALF_UP));

            // 采购价人民币参考金额(新)
            purchasePriceApproveReqVO.setPurchasePriceRmb(purchasePriceApproveReqVO.getPurchasePrice().multiply(dataResponse.getData()).setScale(2, RoundingMode.HALF_UP));
        } else {
            log.warn("submitApproval, 找不到协汇率. sku={}, costCurrency={}, data={}", skuPricePO.getSkuId(), po.getCurrency(), JSONObject.toJSONString(dataResponse));
        }

        skuPurchasePriceApproveManageService.batchSubmit(purchasePriceApproveReqVO);

        return purchasePriceApproveReqVO;
    }

    /**
     * 记录商品价格变更日志
     * @param po SkuPriceDraftPO 对象，包含商品价格变更信息
     */
    private void logPriceChange(SkuPriceDraftPO po) {
        PriceLogPO priceLogPO = getPriceLogPO(po);
        priceLogAtomicService.save(priceLogPO);
    }

    /**
     * 设置警告消息
     * @param vo SkuPriceDraftVO 对象，用于存储警告消息
     * @param profitRateVO ProfitRateVO 对象，包含利润率阈值信息
     */
    private void setWarningMsg(SkuPriceDraftVO vo,ProfitRateVO profitRateVO){
        log.info("getWarningMsg vo={}, profitRateVO={}", JSON.toJSONString(vo), JSONObject.toJSONString(profitRateVO));
        StringBuilder stringBuilder = new StringBuilder();

        if(profitRateVO.getProfitRate() == null){
            stringBuilder.append("请配置利润率阈值.");
        }
        if(profitRateVO.getLowProfitRate() == null){
            stringBuilder.append("请配置超低利润率阈值.");
        }

        if(vo.getProfitRate() != null && profitRateVO.getLowProfitRate() != null
                && vo.getProfitRate().compareTo(profitRateVO.getLowProfitRate()) < 0){
            stringBuilder.append("利润率低于品类超低利润率阈值，将由大区总审批.");
        }

        BigDecimal grossProfit = operDuccConfig.getGrossProfit(vo.getSourceCountryCode());

        log.info("不可售阈值:grossProfit={}, sourceCountryCode={}, profitRate={}", grossProfit, vo.getSourceCountryCode(), vo.getProfitRate());

        if(vo.getProfitRate() != null
                && vo.getProfitRate().compareTo(grossProfit) < 0){
            stringBuilder.append("利润率低于边际负毛阈值，将由经分审批.");
        }
        if(stringBuilder.length() < 1){
            return;
        }
        vo.setWarningMsg("重点关注!!!" + stringBuilder);
    }

    /**
     * 构建 SkuPriceVO 对象。
     * @param skuPriceDraftPO SkuPriceDraftPO 对象，包含了构建 SkuPriceVO 所需的信息。
     * @return 一个新的 SkuPriceVO 对象。
     */
    private SkuPriceVO buildSkuPriceVO(SkuPriceDraftPO skuPriceDraftPO) {
        SkuPriceVO skuPriceVO = new SkuPriceVO();
        skuPriceVO.setSpuId(skuPriceDraftPO.getSpuId());
        skuPriceVO.setSkuId(skuPriceDraftPO.getSkuId());
        skuPriceVO.setSourceCountryCode(skuPriceDraftPO.getSourceCountryCode());
        skuPriceVO.setCurrency(skuPriceDraftPO.getCurrency());
        skuPriceVO.setTradeDirection(TradeDirectionEnum.SUPPLIER);
        skuPriceVO.setTradeType(skuPriceDraftPO.getTradeType());
        skuPriceVO.setTaxPrice(skuPriceDraftPO.getTaxPrice());
        skuPriceVO.setPrice(skuPriceDraftPO.getPrice());
        skuPriceVO.setUpdater(skuPriceDraftPO.getUpdater());
        skuPriceVO.setUpdateTime(new Date());
        return skuPriceVO;
    }

    private SkuPriceDraftPO prepareDraftPO(SkuPricePO skuPricePO,SkuPriceDraftVO vo, ProfitRateVO profitRateVO) {
        SkuPriceDraftPO po = SkuPriceDraftConvert.INSTANCE.po2Po(skuPricePO);
        po.setId(null);
        po.setSkuId(skuPricePO.getSkuId());
        po.setVendorCode(vo.getVendorCode());
        po.setAuditStatus(AuditStatusEnum.WAITING_APPROVED.getCode());
        po.setProfitRate(vo.getProfitRate());
        po.setProfitLimitRate(profitRateVO.getProfitRate());
        po.setLowProfitLimitRate(profitRateVO.getLowProfitRate());
        po.setWarningMsg(vo.getWarningMsg());
        po.setCostCurrency(vo.getCostCurrency());
        po.setCountryCostPrice(vo.getCountryCostPrice());
        po.setAgreementPrice(vo.getAgreementPrice());
        po.setAgreementMark(vo.getAgreementMark());
        po.setCostMark(vo.getCostMark());
        po.setPrice(vo.getPrice());
        po.setTaxPrice(vo.getTaxPrice());
        po.setSourceCountryCode(vo.getSourceCountryCode());
        po.setValue1(JSONObject.toJSONString(vo.getPropertyVos()));
        po.setCreateTime(new Date());
        po.setCreator(LoginContextHolder.getLoginContextHolder().getPin());
        po.setUpdateTime(new Date());
        po.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());

        // 增加附件和调价原因
        po.setRemark(vo.getRemark());
        po.setAdjustmentPriceReason(vo.getAdjustmentPriceReason());

        List<String> attachmentUrls = vo.getAttachmentUrls();

        if (CollectionUtils.isNotEmpty(attachmentUrls)) {
            po.setAttachmentUrls(Joiner.on(",").join(attachmentUrls));
        }

        return po;
    }


    private boolean saveDraft(SkuPriceDraftPO po) {
        return skuPriceDraftAtomicService.save(po);
    }

    private PriceLogPO getPriceLogPO(SkuPriceDraftPO param){
        PriceLogPO priceLogPO = new PriceLogPO();
        priceLogPO.setBizId(param.getId().toString());
        priceLogPO.setBizType(PriceTypeEnum.SKU_SUPPLIER_PRICE_DRAFT.getCode());
        priceLogPO.setBizValue(param.getPrice());
        priceLogPO.setValue1(param.getSourceCountryCode());
        priceLogPO.setValue2(param.getSourceCountryCode());
        priceLogPO.setCreateTime(DateUtil.getCurrentTime());
        priceLogPO.setCreator(param.getCreator());
        priceLogPO.setUpdater(param.getUpdater());
        priceLogPO.setUpdateTime(DateUtil.getCurrentTime());
        return priceLogPO;
    }


    /**
     * 发送日志到数据库。
     * @param skuId sku的唯一标识。
     * @param sourceData 日志的源数据。
     * @param targetData 日志的目标数据。
     */
    private void sendLog(String skuId, SkuPriceDraftVO sourceData, SkuPriceDraftPO targetData) {
        try {
            SkuLogPO skuLogPO = new SkuLogPO();
            skuLogPO.setSourceJson(JSONObject.toJSONString(sourceData));
            skuLogPO.setTargetJson(JSONObject.toJSONString(targetData));
            skuLogPO.setKeyType(KeyTypeEnum.SKU_PRICE_DRAFT.getCode());
            skuLogPO.setKeyId(skuId);
            skuLogPO.setCreator(targetData.getUpdater());
            skuLogPO.setUpdater(targetData.getUpdater());
            skuLogAtomicService.save(skuLogPO);
            log.info("日志保存成功，skuId: {}", skuId);
        } catch (Exception e) {
            log.error("存储日志异常，skuId: {},sourceData:{},targetData:{} ,Error: {}", skuId, JSONObject.toJSONString(sourceData), JSONObject.toJSONString(targetData), e.getMessage(), e);
        }
    }

    private SkuPricePageReqVO createRequestVO(SkuPriceDraftVO vo) {
        SkuPricePageReqVO reqVO = new SkuPricePageReqVO();
        reqVO.setSourceCountryCode(vo.getSourceCountryCode());
        reqVO.setSkuId(vo.getSkuId());
        reqVO.setTradeDirection(TradeDirectionEnum.SUPPLIER);
        reqVO.setAuditStatus(AuditStatusEnum.WAITING_APPROVED.getCode());
        return reqVO;
    }


    @Override
    public PageInfo<SkuPricePageVO> pageSearch(SkuPricePageReqVO vo) {
        PageInfo<SkuPricePageVO> pageInfo = new PageInfo<>();
        pageInfo.setSize(vo.getSize());
        pageInfo.setIndex(vo.getIndex());
        this.buildQuery(vo);
        long total = skuPriceDraftAtomicService.pageSearchTotal(vo);
        if (total == 0) {
            pageInfo.setTotal(0);
            return pageInfo;
        }
        List<SkuPricePageVO> priceList = skuPriceDraftAtomicService.pageSearch(vo);
        if (CollectionUtils.isEmpty(priceList)){
            return pageInfo;
        }
        this.setPageSkuName(priceList);
        this.setPageMkuId(priceList);
        this.setPageCountryName(priceList);
        this.setPageSpuInfo(priceList);
        this.setSkuVatRate(priceList);

        pageInfo.setTotal(total);
        pageInfo.setRecords(priceList);
        return pageInfo;
    }

    /**
     * 根据商品价格列表设置每个商品的增值税率。
     * @param priceList 商品价格列表，包含商品ID和其他相关信息。
     */
    private void setSkuVatRate(List<SkuPricePageVO> priceList) {
        if (CollectionUtils.isEmpty(priceList)) {
            return;
        }
        Set<Long> skuIds = priceList.stream().filter(Objects::nonNull).map(SkuPricePageVO::getSkuId).collect(Collectors.toSet());
        Map<Long, PurchaseSkuTaxResVO> skuTaxResVOMap = purchaseTaxManageService.batchQueryPurchaseTax(new PurchaseTaxQueryReqVO(skuIds));
        if (MapUtils.isNotEmpty(skuTaxResVOMap)){
            priceList.forEach(priceVo -> {
                PurchaseSkuTaxResVO purchaseSkuTaxResVO = skuTaxResVOMap.get(priceVo.getSkuId());
                if (Objects.nonNull(purchaseSkuTaxResVO)) {
                    priceVo.setVatRate(StringUtils.isNotBlank(purchaseSkuTaxResVO.getComplexTaxRate()) ? new BigDecimal(purchaseSkuTaxResVO.getComplexTaxRate()) : null);
                }
            });
        }
    }

    private void buildQuery(SkuPricePageReqVO vo){

    }

    private void buildApproveQuery(SkuPriceAuditPageReqVO param){
        param.setCurrentAuditor(LoginContextHolder.getLoginContextHolder().getPin());

        List<String> bizIds = applyInfoAtomicService.selectWaitAuditBizId(JoySkyBizFlowTypeEnum.PURCHASE_EFFECTIVE_FLOW,param.getApplyCode(),param.getCurrentAuditor());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(bizIds)){
            bizIds = new ArrayList<>();
            bizIds.add("-1");
        }

        param.setIds(bizIds);
    }

    @Override
    public SkuPriceDraftVO detail(SkuPriceQueryReqVO reqVO) {
        SkuPricePageReqVO skuPricePageReqVO = new SkuPricePageReqVO();
        skuPricePageReqVO.setSkuId(reqVO.getSkuId());
        skuPricePageReqVO.setSourceCountryCode(reqVO.getSourceCountryCode());
        skuPricePageReqVO.setTradeDirection(TradeDirectionEnum.SUPPLIER);
        skuPricePageReqVO.setAuditStatus(AuditStatusEnum.WAITING_APPROVED.getCode());
        SkuPriceDraftPO skuPriceDraftPO = skuPriceDraftAtomicService.getSkuPriceDraftPO(skuPricePageReqVO);
        SkuPriceDraftVO skuPriceDraftVO = SkuPriceDraftConvert.INSTANCE.po2Vo(skuPriceDraftPO);
        return skuPriceDraftVO;
    }

    @Override
    public Boolean batchJoySkyApprove(String erp, Integer status, String processInstanceId, String msg) {
        if(status == null || StringUtils.isBlank(processInstanceId)){
            throw new BizException("审批人或流程id为空");
        }

        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(JoySkyBizFlowTypeEnum.PURCHASE_EFFECTIVE_FLOW,processInstanceId);
        if(applyInfoPO != null && applyInfoPO.getAuditStatus() != null && AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.info(String.format("SkuPriceDraftManageServiceImpl.batchJoySkyApprove applyInfoPO 审批记录为空或审批状态不匹配,processInstanceId:%s",processInstanceId));
            return Boolean.FALSE;
        }

        SkuPriceDraftPO draftPO = skuPriceDraftAtomicService.getValidById(Long.valueOf(applyInfoPO.getBizId()));
        if(draftPO == null ||draftPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != draftPO.getAuditStatus()){
            throw new BizException(String.format("SkuPriceDraftManageServiceImpl.batchJoySkyApprove detailDraftPO 记录为空或审批状态不匹配,applyInfoPO:%s",applyInfoPO.getBizId()));
        }

        SkuPurchasePriceAuditVO param = new SkuPurchasePriceAuditVO();
        param.setProcessInstanceId(processInstanceId);
        param.setAuditErp(erp);
        param.setRejectReason(msg);
        if(status.equals(AuditStatusEnum.APPROVED.getCode())){
            return skuPurchasePriceApproveManageService.messageApprove(param);
        } else if (status.equals(AuditStatusEnum.REJECTED.getCode())){
            return skuPurchasePriceApproveManageService.messageReject(param);
        } else if (status.equals(AuditStatusEnum.REVOKE.getCode())){
            return skuPurchasePriceApproveManageService.messageRevoke(param);
        } else if (status.equals(AuditStatusEnum.APPROVING.getCode())){
            return skuPurchasePriceApproveManageService.messageApproving(param);
        } else {
            log.error("status is error");
            return null;
        }
    }

    @Override
    public String batchApprove(SkuPurchasePriceAuditVO input) {
        List<Long> ids = input.getIds();
        List<SkuPriceDraftPO> draftPOS = skuPriceDraftAtomicService.getValidByIds(ids);
        if(CollectionUtils.isEmpty(draftPOS)){
            return Constant.SUCCESS;
        }

        List<ApplyInfoPO> applyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.PURCHASE_EFFECTIVE_FLOW,input.getIds());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(applyInfoPOS)){
            throw new BizException("无匹配数据,数据已变更，请刷新页面");
        }
        Map<String,ApplyInfoPO> applyInfoPOMap = applyInfoPOS.stream().collect(Collectors.toMap(ApplyInfoPO::getBizId, Function.identity()));

        String message = "";
        for(SkuPriceDraftPO draftPO : draftPOS){
            ApplyInfoPO applyInfoPO = applyInfoPOMap.get(draftPO.getId().toString());
            try{
                input.setIds(Lists.newArrayList(draftPO.getId()));
                skuPurchasePriceApproveManageService.singleApprove(draftPO,input,applyInfoPO);
            }catch (Exception e){
                log.error("SkuPriceDraftManageServiceImpl.singleApprove error, draftPO:{}",draftPO,e);
                message = String.format("%s：%s%s", applyInfoPO.getApplyCode(), e.getMessage(),"\n");
            }
        }
        return StringUtils.isNotBlank(message) ? message : Constant.SUCCESS;
    }

    @Override
    public String batchReject(SkuPurchasePriceAuditVO input) {
        List<Long> ids = input.getIds();
        List<SkuPriceDraftPO> draftPOS = skuPriceDraftAtomicService.getValidByIds(ids);
        if(CollectionUtils.isEmpty(draftPOS)){
            return Constant.SUCCESS;
        }

        List<ApplyInfoPO> applyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.PURCHASE_EFFECTIVE_FLOW,input.getIds());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(applyInfoPOS)){
            throw new BizException("无匹配数据,数据已变更，请刷新页面");
        }
        Map<String,ApplyInfoPO> applyInfoPOMap = applyInfoPOS.stream().collect(Collectors.toMap(ApplyInfoPO::getBizId, Function.identity()));

        String message = "";
        for(SkuPriceDraftPO draftPO : draftPOS){
            ApplyInfoPO applyInfoPO = applyInfoPOMap.get(draftPO.getId().toString());
            try{
                input.setIds(Lists.newArrayList(draftPO.getId()));
                skuPurchasePriceApproveManageService.singleReject(draftPO,input,applyInfoPOMap.get(draftPO.getId().toString()));
            }catch (Exception e){
                log.error("SkuPriceDraftManageServiceImpl.singleReject error, draftPO:{}",draftPO,e);
                message = String.format("%s：%s%s", applyInfoPO.getApplyCode(), e.getMessage(),"\n");
            }
        }
        return StringUtils.isNotBlank(message) ? message : Constant.SUCCESS;
    }

    @Override
    public String batchRevoke(SkuPurchasePriceAuditVO input) {
        List<Long> ids = input.getIds();
        List<SkuPriceDraftPO> draftPOS = skuPriceDraftAtomicService.getValidByIds(ids);
        if(CollectionUtils.isEmpty(draftPOS)){
            return Constant.SUCCESS;
        }

        List<ApplyInfoPO> applyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.PURCHASE_EFFECTIVE_FLOW,input.getIds());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(applyInfoPOS)){
            throw new BizException("无匹配数据,数据已变更，请刷新页面");
        }
        Map<String,ApplyInfoPO> applyInfoPOMap = applyInfoPOS.stream().collect(Collectors.toMap(ApplyInfoPO::getBizId, Function.identity()));

        for(SkuPriceDraftPO draftPO : draftPOS){
            try{
                input.setIds(Lists.newArrayList(draftPO.getId()));
                skuPurchasePriceApproveManageService.singleRevoke(draftPO,input,applyInfoPOMap.get(draftPO.getId().toString()));
            }catch (Exception e){
                log.error("SkuPriceDraftManageServiceImpl.singleRevoke error, draftPO:{}",draftPO,e);
            }
        }
        return Constant.SUCCESS;
    }

    @Override
    public PageInfo<SkuPriceAuditPageVO> approveSearch(SkuPriceAuditPageReqVO input) {
        PageInfo<SkuPriceAuditPageVO> pageInfo = new PageInfo<>();
        pageInfo.setSize(input.getSize());
        pageInfo.setIndex(input.getIndex());
        this.buildApproveQuery(input);
        long total = skuPriceDraftAtomicService.approvePageSearchTotal(input);
        pageInfo.setTotal(total);
        if (total == 0) {
            pageInfo.setRecords(Collections.emptyList());
            return pageInfo;
        }
        // 分页查询草稿
        List<SkuPriceAuditPageVO> dbRecordList = skuPriceDraftAtomicService.approvePageSearch(input);

        // 处理表单数据
        this.handleProcessFromData(dbRecordList);

        pageInfo.setRecords(dbRecordList);
        if(CollectionUtils.isNotEmpty(pageInfo.getRecords())){
            // 设置skuName
            this.setAuditPageSkuName(pageInfo.getRecords());

            this.setAuditPageMkuId(pageInfo.getRecords());

            this.setAuditPageCountryName(pageInfo.getRecords());

            this.setAuditPageSpuInfo(pageInfo.getRecords());
            // 设置单号
            this.setApplyCode(pageInfo.getRecords());
            // 设置利润率乘以百
            this.setProfitRate(pageInfo.getRecords());
        }
        return pageInfo;
    }

    /**
     * 处理表单数据
     */
    private void handleProcessFromData(List<SkuPriceAuditPageVO> dbRecordList) {
        if (CollectionUtils.isEmpty(dbRecordList)) {
            return;
        }

        for (SkuPriceAuditPageVO item : dbRecordList) {
            String processFromData = item.getProcessFromData();

            // 获取动态列
            Map<String, String> customColumns = auditService.getCustomColumnMap(processFromData, AuditFormEnum.PURCHASE_PRICE_AUDIT.getCode());

            item.setCustomColumns(customColumns);
        }
    }


    /**
     * 设置 SKU 名称。
     * @param targets 目标列表，包含 CustomerSkuPriceWarningVO 对象。
     */
    private void setPageSkuName(List<SkuPricePageVO> targets) {
        if(CollectionUtils.isEmpty(targets)){
            return;
        }
        Set<Long> spuIds = targets.stream().filter(Objects::nonNull).map(SkuPricePageVO::getSpuId).collect(Collectors.toSet());
        Map<Long, SpuLangPO> spuLangMap = spuLangAtomicService.getSpuLangNameBySpuIds(Lists.newArrayList(spuIds),LangConstant.LANG_ZH);

        for(SkuPricePageVO target :targets){
            if (MapUtils.isNotEmpty(spuLangMap) && Objects.nonNull(spuLangMap.get(target.getSpuId()))) {
                target.setSkuName(spuLangMap.get(target.getSpuId()).getSpuTitle());
            }
        }
    }

    /**
     * 设置商品页面的 MKU ID。
     * @param targets 包含 SkuPricePageVO 对象的列表，用于设置 MKU ID。
     */
    private void setPageMkuId(List<SkuPricePageVO> targets) {
        if(CollectionUtils.isEmpty(targets)){
            return;
        }
        Set<Long> skuIds = targets.stream().map(SkuPricePageVO::getSkuId).collect(Collectors.toSet());
        Map<Long, Long> skuMkuMap = mkuRelationAtomicService.queryMkuIdBySkuIds(skuIds);
        for(SkuPricePageVO target :targets){
            target.setMkuId(skuMkuMap.get(target.getSkuId()));
        }
    }

    /**
     * 设置商品价格页面的国家名称。
     * @param targets SkuPricePageVO 列表，包含需要设置国家名称的商品价格页面信息。
     */
    private void setPageCountryName(List<SkuPricePageVO> targets) {
        if(CollectionUtils.isEmpty(targets)){
            return;
        }
        Map<String, String> countryMap = countryManageService.getCountryMap(LangContextHolder.get());
        for(SkuPricePageVO target :targets){
            target.setSourceCountryName(countryMap.get(target.getSourceCountryCode()));
        }
    }

    /**
     * 设置商品价格页面的国家名称。
     * @param inputs SkuPricePageVO 列表，包含需要设置国家名称的商品价格页面信息。
     */
    private void setPageSpuInfo(List<SkuPricePageVO> inputs) {
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Set<Long> skuIds = inputs.stream().map(SkuPricePageVO::getSkuId).collect(Collectors.toSet());
        Map<Long, SkuPO> skuPOMap = skuAtomicService.getSkuMap(skuIds);
        Set<Long> spuIds = inputs.stream().map(SkuPricePageVO::getSpuId).collect(Collectors.toSet());
        Map<Long, SpuPO> spuPOMap = spuAtomicService.getSpuMap(spuIds);
        Set<Long> brandIds = skuPOMap.values().stream().map(SkuPO::getBrandId).collect(Collectors.toSet());
        Map<Long, String> brandNameMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(brandIds)){
            brandNameMap.putAll(brandOutService.queryNameByIds(brandIds,LangContextHolder.get()));
        }

        Set<Long> categoryIds = skuPOMap.values().stream().map(SkuPO::getJdCatId).collect(Collectors.toSet());
        Map<Long, String> catMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(categoryIds)){
            catMap.putAll(categoryOutService.queryPathStr(categoryIds,LangContextHolder.get()));
        }

        Map<String,Set<String>> countrySupplierCodeMap = spuPOMap.values().stream()
                .filter(item -> StringUtils.isNotBlank(item.getVendorCode())
                        && StringUtils.isNotBlank(item.getSourceCountryCode()))
                .collect(Collectors.groupingBy(
                        SpuPO::getSourceCountryCode,
                        Collectors.mapping(SpuPO::getVendorCode, Collectors.toSet())
                ));
        if(MapUtils.isEmpty(countrySupplierCodeMap)){
            return;
        }
        Map<String, String> supplierMap = new HashMap<>();
        countrySupplierCodeMap.forEach((k,v)->{
            if(CountryConstant.COUNTRY_ZH.equals(k)){
                return;
            }
            Map<String, String> resultPart = supplierAllInfoAtomicService.querySupplierNameByCodes(v, k);
            supplierMap.putAll(resultPart);
        });
        countrySupplierCodeMap.forEach((k,v)->{
            if(!CountryConstant.COUNTRY_ZH.equals(k)){
                return;
            }
            Map<String, String> resultPart = vendorManageService.getVendorMapByCodes(v);
            supplierMap.putAll(resultPart);
        });

        inputs.forEach(item->{
            SkuPO skuPO = skuPOMap.get(item.getSkuId());
            if(skuPO != null){
                item.setCatId(skuPO.getJdCatId());
                item.setCatName(catMap.get(skuPO.getJdCatId()));

                item.setBrandId(skuPO.getBrandId());
                item.setBrandName(brandNameMap.get(skuPO.getBrandId()));
            }

            SpuPO spuPO = spuPOMap.get(item.getSpuId());
            if(spuPO != null){
                item.setBuyer(spuPO.getBuyer());
                item.setSpuStatus(spuPO.getSpuStatus());
                item.setVendorName(supplierMap.get(spuPO.getVendorCode()));
            }
        });
    }

    /**
     * 设置 SKU 名称。
     * @param targets 目标列表，包含 CustomerSkuPriceWarningVO 对象。
     */
    private void setAuditPageSkuName(List<SkuPriceAuditPageVO> targets) {
        if(CollectionUtils.isEmpty(targets)){
            return;
        }
        List<Long> skuIds = targets.stream().map(SkuPriceAuditPageVO::getSkuId).collect(Collectors.toList());
        Map<Long, ExternalVO> externalVOMap = querySkuInfo(skuIds);
        for(SkuPriceAuditPageVO target :targets){
            ExternalVO skuLang = externalVOMap.get(target.getSkuId());
            if (skuLang != null) {
                SpuLangVO zhName = skuLang.getLangVOList().stream()
                        .filter(line -> LangConstant.LANG_ZH.equals(line.getLang()))
                        .findFirst().orElse(null);
                target.setSkuName(zhName != null ? zhName.getSpuTitle() : null);
            }
        }
    }

    /**
     * 设置商品页面的 MKU ID。
     * @param targets 包含 SkuPricePageVO 对象的列表，用于设置 MKU ID。
     */
    private void setAuditPageMkuId(List<SkuPriceAuditPageVO> targets) {
        if(CollectionUtils.isEmpty(targets)){
            return;
        }
        Set<Long> skuIds = targets.stream().map(SkuPricePageVO::getSkuId).collect(Collectors.toSet());
        Map<Long, Long> skuMkuMap = mkuRelationAtomicService.queryMkuIdBySkuIds(skuIds);
        for(SkuPricePageVO target :targets){
            target.setMkuId(skuMkuMap.get(target.getSkuId()));
        }
    }

    /**
     * 设置商品价格页面的国家名称。
     * @param targets SkuPricePageVO 列表，包含需要设置国家名称的商品价格页面信息。
     */
    private void setAuditPageCountryName(List<SkuPriceAuditPageVO> targets) {
        if(CollectionUtils.isEmpty(targets)){
            return;
        }
        Map<String, String> countryMap = countryManageService.getCountryMap(LangContextHolder.get());
        for(SkuPricePageVO target :targets){
            target.setSourceCountryName(countryMap.get(target.getSourceCountryCode()));
        }
    }

    /**
     * 设置商品价格页面的国家名称。
     * @param inputs SkuPricePageVO 列表，包含需要设置国家名称的商品价格页面信息。
     */
    private void setAuditPageSpuInfo(List<SkuPriceAuditPageVO> inputs) {
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Set<Long> skuIds = inputs.stream().map(SkuPriceAuditPageVO::getSkuId).collect(Collectors.toSet());
        Map<Long, SkuPO> skuPOMap = skuAtomicService.getSkuMap(skuIds);
        Set<Long> spuIds = inputs.stream().map(SkuPriceAuditPageVO::getSpuId).collect(Collectors.toSet());
        Map<Long, SpuPO> spuPOMap = spuAtomicService.getSpuMap(spuIds);
        Set<Long> brandIds = skuPOMap.values().stream().map(SkuPO::getBrandId).collect(Collectors.toSet());
        Map<Long, String> brandNameMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(brandIds)){
            brandNameMap.putAll(brandOutService.queryNameByIds(brandIds,LangContextHolder.get()));
        }

        Set<Long> categoryIds = skuPOMap.values().stream().map(SkuPO::getJdCatId).collect(Collectors.toSet());
        Map<Long, String> catMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(categoryIds)){
            catMap.putAll(categoryOutService.queryPathStr(categoryIds,LangContextHolder.get()));
        }

        Map<String,Set<String>> countrySupplierCodeMap = spuPOMap.values().stream()
                .filter(item -> StringUtils.isNotBlank(item.getVendorCode())
                        && StringUtils.isNotBlank(item.getSourceCountryCode()))
                .collect(Collectors.groupingBy(
                        SpuPO::getSourceCountryCode,
                        Collectors.mapping(SpuPO::getVendorCode, Collectors.toSet())
                ));
        if(MapUtils.isEmpty(countrySupplierCodeMap)){
            return;
        }
        Map<String, String> supplierMap = new HashMap<>();
        countrySupplierCodeMap.forEach((k,v)->{
            if(CountryConstant.COUNTRY_ZH.equals(k)){
                return;
            }
            Map<String, String> resultPart = supplierAllInfoAtomicService.querySupplierNameByCodes(v, k);
            supplierMap.putAll(resultPart);
        });
        countrySupplierCodeMap.forEach((k,v)->{
            if(!CountryConstant.COUNTRY_ZH.equals(k)){
                return;
            }
            Map<String, String> resultPart = vendorManageService.getVendorMapByCodes(v);
            supplierMap.putAll(resultPart);
        });

        inputs.forEach(item->{
            SkuPO skuPO = skuPOMap.get(item.getSkuId());
            if(skuPO != null){
                item.setCatId(skuPO.getJdCatId());
                item.setCatName(catMap.get(skuPO.getJdCatId()));

                item.setBrandId(skuPO.getBrandId());
                item.setBrandName(brandNameMap.get(skuPO.getBrandId()));
            }

            SpuPO spuPO = spuPOMap.get(item.getSpuId());
            if(spuPO != null){
                item.setBuyer(spuPO.getBuyer());
                item.setSpuStatus(spuPO.getSpuStatus());
                item.setVendorName(supplierMap.get(spuPO.getVendorCode()));
            }
        });
    }

    /**
     * 根据给定的 SKU IDs 查询对应的 ExternalVO 信息。
     * @param skuIds 需要查询的 SKU IDs 列表。
     * @return 包含查询结果的 Map 对象，键为 SKU ID，值为对应的 ExternalVO 对象。
     */
    private Map<Long, ExternalVO> querySkuInfo(List<Long> skuIds) {
        SkuExternalReqVO skuQuery = new SkuExternalReqVO();
        skuQuery.setSkuIds(skuIds);
        skuQuery.setScopeEnums(Sets.newHashSet(SkuScopeEnum.BASE, SkuScopeEnum.LANG));
        return skuExternalManageService.querySkuInfo(skuQuery);
    }

    /**
     * 设置单号
     * @param inputs 包含供应商代码和国家代码的列表
     */
    private void setApplyCode(List<SkuPriceAuditPageVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }

        List<Long> ids = inputs.stream().map(SkuPriceAuditPageVO::getId).collect(Collectors.toList());
        List<ApplyInfoPO> applyInfoPOS =  applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.PURCHASE_EFFECTIVE_FLOW,ids);
        if(CollectionUtils.isEmpty(applyInfoPOS)){
            return;
        }

        Map<String, String> applyCodeMap = applyInfoPOS.stream()
                .collect(Collectors.toMap(
                        ApplyInfoPO::getBizId,           // key mapper
                        ApplyInfoPO::getApplyCode,    // value mapper
                        (existing, replacement) -> existing  // 处理重复key的情况
                ));
        inputs.forEach(item->{
            item.setApplyCode(applyCodeMap.get(item.getId().toString()));
        });
    }

    /**
     * 设置利润率的百分比表示。
     * @param inputs 国家的协议价格警告信息列表。
     */
    private void setProfitRate(List<SkuPriceAuditPageVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }

        inputs.forEach(item->{
            item.setProfitRate(item.getProfitRate() != null?item.getProfitRate().multiply(Constant.DECIMAL_HUNDRED):null);
            item.setProfitRateLimit(item.getProfitRateLimit() != null?item.getProfitRateLimit().multiply(Constant.DECIMAL_HUNDRED):null);
            item.setProfitRateLowLimit(item.getProfitRateLowLimit()!= null?item.getProfitRateLowLimit().multiply(Constant.DECIMAL_HUNDRED):null);
        });
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class)
    public DataResponse<Boolean> saveSkuPriceDraft(SkuPriceDraftVO input) {
        DataResponse<Boolean> response = checkSkuPriceDraft(input);
        if(!response.getSuccess()){
            return response;
        }
        SkuCalculateTaxPriceVO skuCalculateTaxPriceVO = calculateSkuTaxPrice(input);
        if(Objects.isNull(skuCalculateTaxPriceVO)){
            return DataResponse.error("无法计算出国家成本价");
        }
        DataResponse<Boolean> checkResponse = checkPrice(input, skuCalculateTaxPriceVO);
        if(!checkResponse.getSuccess()){
            return checkResponse;
        }
        DataResponse<Boolean> saveResponse = save(input);
        return saveResponse;
    }

    private DataResponse<Boolean> checkPrice(SkuPriceDraftVO input,SkuCalculateTaxPriceVO skuCalculateTaxPriceVO){
        if(Objects.isNull(input.getPrice())){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("未税采购价为空");
        }
        if(Objects.isNull(skuCalculateTaxPriceVO.getPurchasePrice())){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("未税采购价计算失败");
        }
        if(!StringUtils.equals(CountryConstant.COUNTRY_BR,input.getSourceCountryCode())
                && input.getPrice().compareTo(skuCalculateTaxPriceVO.getPurchasePrice()) != 0){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("未税采购价校验后不正确，请重新设置价格");
        }
        if(Objects.isNull(input.getTaxPrice())){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("含税采购价为空");
        }
        if(Objects.isNull(skuCalculateTaxPriceVO.getTaxPurchasePrice())){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("含税采购价计算失败");
        }
        if(!StringUtils.equals(CountryConstant.COUNTRY_BR,input.getSourceCountryCode())
                && input.getTaxPrice().compareTo(skuCalculateTaxPriceVO.getTaxPurchasePrice()) != 0){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("含税采购价校验后不正确，请重新设置价格");
        }
        if(Objects.isNull(input.getCountryCostPrice())){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("预估国家成本价为空");
        }
        if( Objects.isNull(skuCalculateTaxPriceVO.getCountryCostPrice())){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("预估国家成本价计算失败");
        }
        if(!StringUtils.equals(CountryConstant.COUNTRY_BR, input.getSourceCountryCode())
                && input.getCountryCostPrice().compareTo(skuCalculateTaxPriceVO.getCountryCostPrice()) != 0){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("预估国家成本价校验后不正确，请重新设置价格");
        }
        if(Objects.isNull(input.getAgreementPrice())){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("预估国家协议价为空");
        }
        if(Objects.isNull(skuCalculateTaxPriceVO.getAgreementPrice())){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("预估国家协议价计算失败");
        }
        if(input.getAgreementPrice().compareTo(skuCalculateTaxPriceVO.getAgreementPrice()) != 0){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("预估国家协议价校验后不正确，请重新设置价格");
        }
        if(Objects.isNull(input.getProfitRate())){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("预估利润率为空");
        }
        if(StringUtils.equals(CountryConstant.COUNTRY_BR,input.getSourceCountryCode())){
            this.checkSkuPriceDraftForBrazil(input);
        }
        if(Objects.isNull(skuCalculateTaxPriceVO.getAgreementProfitRate())){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("预估利润率计算失败");
        }
        if(!StringUtils.equals(CountryConstant.COUNTRY_BR, input.getSourceCountryCode())
                && input.getProfitRate().compareTo(skuCalculateTaxPriceVO.getAgreementProfitRate()) != 0){
            log.info("input:{},skuCalculateTaxPriceVO:{}",JSONObject.toJSONString(input) ,JSONObject.toJSONString(skuCalculateTaxPriceVO));
            return DataResponse.error("预估利润率校验后不正确，请重新设置价格");
        }
        return DataResponse.success();
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class)
    public DataResponse<Boolean> createSkuPriceDraft(SkuPriceDraftVO input) {
        DataResponse<Boolean> response = checkSkuPriceDraft(input);
        if(!response.getSuccess()){
            return response;
        }
        SkuCalculateTaxPriceVO skuCalculateTaxPriceVO = calculateSkuTaxPrice(input);
        if(Objects.isNull(skuCalculateTaxPriceVO)){
            return DataResponse.error("无法计算出国家成本价");
        }
        if(Objects.isNull(skuCalculateTaxPriceVO.getCountryCostPrice()) || skuCalculateTaxPriceVO.getCountryCostPrice().compareTo(BigDecimal.ZERO) <= 0){
            return DataResponse.error("国家成本价为空");
        }
        if(Objects.isNull(skuCalculateTaxPriceVO.getAgreementPrice()) || skuCalculateTaxPriceVO.getAgreementPrice().compareTo(BigDecimal.ZERO) <= 0){
            return DataResponse.error("国家协议价为空");
        }
        input.setPrice(skuCalculateTaxPriceVO.getPurchasePrice());
        input.setTaxPrice(skuCalculateTaxPriceVO.getTaxPurchasePrice());
        input.setAgreementPrice(skuCalculateTaxPriceVO.getAgreementPrice());
        input.setCountryCostPrice(skuCalculateTaxPriceVO.getCountryCostPrice());
        input.setProfitRate(skuCalculateTaxPriceVO.getAgreementProfitRate());
        input.setCostMark(skuCalculateTaxPriceVO.getCostMark());
        input.setAgreementMark(skuCalculateTaxPriceVO.getAgreementMark());
        input.setCostCurrency(skuCalculateTaxPriceVO.getCostCurrency());
        input.setCurrency(skuCalculateTaxPriceVO.getCurrency());
        input.setTradeDirection(TradeDirectionEnum.SUPPLIER);
        if(CountryConstant.COUNTRY_BR.equals(input.getSourceCountryCode())){
            Set<Long> skuIds = new HashSet<>();
            skuIds.add(input.getSkuId());
            Set<Long> attributeIds = new HashSet<>(taxRateIdSet);
            attributeIds.addAll(taxRateAmountIdSet);
            List<ProductGlobalAttributePO> brGlobalAttr = productGlobalAttributeAtomicService.getBySkuIdsAndAttributeIds(skuIds, attributeIds);
            if(CollectionUtils.isNotEmpty(brGlobalAttr)){
                Map<Long, BigDecimal> taxMap = skuCalculateTaxPriceVO.getTaxMap();
                for (ProductGlobalAttributePO po : brGlobalAttr){
                    if(taxRateAmountIdSet.contains(po.getAttributeId())){
                        BigDecimal bigDecimal = taxMap.get(po.getAttributeId());
                        po.setAttributeValue(bigDecimal.stripTrailingZeros().toPlainString());
                    }
                }
            }
            List<PropertyVO> propertyVOS = productAttributeConvertService.convertSkuAttributeVOSFromDB(input.getSourceCountryCode(), Lists.newArrayList(input.getSourceCountryCode()), brGlobalAttr);
            input.setPropertyVos(propertyVOS);
        }
        DataResponse<Boolean> saveResponse = save(input);
        return saveResponse;
    }

    private SkuCalculateTaxPriceVO calculateSkuTaxPrice(SkuPriceDraftVO input){
        SkuCalculatePriceReqVO skuCalculatePriceReqVO = new SkuCalculatePriceReqVO();
        skuCalculatePriceReqVO.setSkuId(input.getSkuId());
        skuCalculatePriceReqVO.setPurchasePrice(input.getPrice());
        skuCalculatePriceReqVO.setTaxPurchasePrice(input.getTaxPrice());
        skuCalculatePriceReqVO.setCatId(input.getCatId());
        skuCalculatePriceReqVO.setSupplierCode(input.getVendorCode());
        skuCalculatePriceReqVO.setSkuPriceFlag(Boolean.FALSE);
        skuCalculatePriceReqVO.setCalculatePriceEnums(CalculatePriceEnums.CALCULATED);
        skuCalculatePriceReqVO.setSourceCountryCode(input.getSourceCountryCode());
        if(CountryConstant.COUNTRY_BR.equals(input.getSourceCountryCode())){
            Set<Long> skuIds = new HashSet<>();
            skuIds.add(input.getSkuId());
            Set<Long> attributeIds = new HashSet<>(taxRateIdSet);
            List<ProductGlobalAttributePO> brGlobalAttr = productGlobalAttributeAtomicService.getBySkuIdsAndAttributeIds(skuIds, attributeIds);
            if(CollectionUtils.isNotEmpty(brGlobalAttr)){
                Map<Long,String> taxRateMap = new HashMap<>();
                for (ProductGlobalAttributePO po : brGlobalAttr){
                    if(taxRateIdSet.contains(po.getAttributeId())){
                        BigDecimal taxRate = new BigDecimal(po.getAttributeValue());
                        taxRateMap.put(po.getAttributeId(),taxRate.stripTrailingZeros().toPlainString());
                    }
                }
                skuCalculatePriceReqVO.setTaxRateMap(taxRateMap);
            }
            skuCalculatePriceReqVO.setPurchasePrice(null);
        }else if(CountryConstant.COUNTRY_VN.equals(input.getSourceCountryCode())){
            Set<Long> skuIds = new HashSet<>();
            skuIds.add(input.getSkuId());
            /*Set<Long> attributeIds = new HashSet<>();
            attributeIds.add(VN_TAX_RATE);
            List<ProductGlobalAttributePO> vnGlobalAttr = productGlobalAttributeAtomicService.getBySkuIdsAndAttributeIds(skuIds, attributeIds);
            if(CollectionUtils.isNotEmpty(vnGlobalAttr)){
                ProductGlobalAttributePO productGlobalAttributePO = vnGlobalAttr.get(0);
                BigDecimal taxRateVar = new BigDecimal(productGlobalAttributePO.getAttributeValue());
                skuCalculatePriceReqVO.setTaxRate(taxRateVar);
            }*/
            Map<Long, PurchaseSkuTaxResVO> skuTaxResVOMap = purchaseTaxManageService.batchQueryPurchaseTax(new PurchaseTaxQueryReqVO(skuIds));
            if (MapUtils.isNotEmpty(skuTaxResVOMap)) {
                if(skuTaxResVOMap.containsKey(input.getSkuId())) {
                    PurchaseSkuTaxResVO purchaseSkuTaxResVO = skuTaxResVOMap.get(input.getSkuId());
                    if (Objects.nonNull(purchaseSkuTaxResVO)) {
                        skuCalculatePriceReqVO.setTaxRate(StringUtils.isNotBlank(purchaseSkuTaxResVO.getComplexTaxRate()) ? new BigDecimal(purchaseSkuTaxResVO.getComplexTaxRate()).multiply(new BigDecimal(100)) : null);
                    }
                }
            }
            skuCalculatePriceReqVO.setPurchasePrice(null);
        }else {
            skuCalculatePriceReqVO.setTaxPurchasePrice(null);
        }
        SkuCalculateTaxPriceVO skuCalculateSalePrice = skuReadManageService.getSkuCalculateSalePrice(skuCalculatePriceReqVO);
        return skuCalculateSalePrice;
    }

    @Resource
    private CountryAgreementPriceDraftAtomicService countryAgreementPriceDraftAtomicService;

    private DataResponse<Boolean> checkSkuPriceDraft(SkuPriceDraftVO input){
        SkuPricePageReqVO reqVO = this.createRequestVO(input);
        SkuPriceDraftPO draftPO = skuPriceDraftAtomicService.getSkuPriceDraftPO(reqVO);
        if (Objects.nonNull(draftPO) && draftPO.getAuditStatus().equals(AuditStatusEnum.WAITING_APPROVED.getCode())) {
            log.info("有数据在审批中，无法新增信息:{}",JSON.toJSONString(input));
            return DataResponse.error("有数据在审批中，无法新增信息");
        }
        SkuPriceQueryReqVO skuPriceQueryReqVO = new SkuPriceQueryReqVO();
        skuPriceQueryReqVO.setSkuId(reqVO.getSkuId());
        skuPriceQueryReqVO.setSourceCountryCode(reqVO.getSourceCountryCode());
        skuPriceQueryReqVO.setTradeDirection(TradeDirectionEnum.SUPPLIER);
        SkuPricePO skuPricePO = skuPriceAtomicService.getSkuPricePO(skuPriceQueryReqVO);
        if (skuPricePO == null) {
            log.info("基础采购价未查询到信息:{}",JSON.toJSONString(input));
            return DataResponse.error("基础采购价未查询到信息");
        }
        SkuPO skuPO = skuAtomicService.getSkuPoBySkuId(skuPricePO.getSkuId());
        if (skuPO == null) {
            log.info("SKU未查询到信息:{}",JSON.toJSONString(input));
            return DataResponse.error("SKU未查询到信息");
        }
        if(!Objects.equals(skuPO.getVendorCode(),input.getVendorCode())){
            log.info("此供应商和skuId不符合:{}",JSON.toJSONString(input));
            return DataResponse.error("此供应商和skuId不符合");
        }

        CountryAgreementPriceReqVO countryAgreementPriceReqVO = new CountryAgreementPriceReqVO();
        countryAgreementPriceReqVO.setSkuId(input.getSkuId());
        countryAgreementPriceReqVO.setSourceCountryCode(input.getSourceCountryCode());
        countryAgreementPriceReqVO.setTargetCountryCode(input.getSourceCountryCode());
        countryAgreementPriceReqVO.setAuditStatus(AuditStatusEnum.WAITING_APPROVED.getCode());
        CountryAgreementPriceDraftPO countryAgreementPriceDraft = countryAgreementPriceDraftAtomicService.getCountryAgreementPriceDraft(countryAgreementPriceReqVO);
        if(Objects.nonNull(countryAgreementPriceDraft)){
            log.info("国家协议价在审批中,countryAgreementPriceDraft:{}", JSON.toJSONString(countryAgreementPriceDraft));
            return DataResponse.error("国家协议价在审批中");
        }

        input.setCatId(skuPO.getJdCatId());
        input.setVendorCode(skuPO.getVendorCode());
        input.setSpuId(skuPO.getSpuId());
        input.setSourceCountryCode(skuPO.getSourceCountryCode());
        return DataResponse.success();
    }

    /**
     * 检查巴西 SKU 价格草稿的有效性。
     * @param input SkuPriceDraftVO 对象，包含巴西 SKU 的价格和税金信息。
     */
    public void checkSkuPriceDraftForBrazil(SkuPriceDraftVO input){
        if(CollectionUtils.isEmpty(input.getPropertyVos())){
            throw new BizException("巴西税金为空");
        }
        if(input.getPrice() == null){
            throw new BizException("未税采购价为空");
        }
        if(input.getTaxPrice() == null){
            throw new BizException("含税采购价为空");
        }
        BigDecimal taxAmount = BigDecimal.ZERO;
        List<PropertyVO> propertyVOS = input.getPropertyVos();
        for (PropertyVO propertyVO : propertyVOS) {
            if(propertyVO.getAttributeId() == null){
                throw new BizException("跨境属性id为空");
            }
            if(taxRateAmountIdSet.contains(propertyVO.getAttributeId())){
                List<PropertyValueVO> valueVOS = propertyVO.getPropertyValueVOList();
                if(CollectionUtils.isEmpty(valueVOS)){
                     throw new BizException("巴西跨境属性值税金为空");
                }
                PropertyValueVO valueVO = valueVOS.get(0);
                if(valueVO == null || StringUtils.isBlank(valueVO.getAttributeValueName())){
                    throw new BizException("巴西跨境属性值税金值为空");
                }
                BigDecimal amount = new BigDecimal(valueVO.getAttributeValueName());
                taxAmount = taxAmount.add(amount);
            }
        }

        BigDecimal calculateTaxAmount = taxAmount.add(input.getPrice());
        if(calculateTaxAmount.compareTo(input.getTaxPrice()) != 0){
            throw new BizException("未税采购价与税金之和不等于含税采购价,请检查");
        }
    }

    @Override
    public DataResponse<List<AuditColumnConfigDTO>> getCustomColumns(String key) {
        List<AuditColumnConfigDTO> list = auditService.getCustomColumns(key);
        return DataResponse.success(list);
    }
}
