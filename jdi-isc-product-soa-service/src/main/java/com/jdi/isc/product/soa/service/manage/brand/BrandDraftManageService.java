package com.jdi.isc.product.soa.service.manage.brand;

import com.jd.gms.greatdane.brand.domain.Brand;
import com.jdi.isc.product.soa.domain.brand.biz.BrandDraftVO;

/**
 * <AUTHOR>
 * @date 2024/8/2
 **/
public interface BrandDraftManageService {


    
    /**
     * @function 保存或更新品牌草稿信息
     * @param brandDraftVO - 待保存或更新的品牌草稿对象
     * @returns boolean - 操作成功返回true，失败返回false
     */
    boolean saveOrUpdate(BrandDraftVO brandDraftVO);


    /**
     * @function 根据品牌ID获取品牌草稿信息对象
     * @param brandId 品牌的唯一标识ID
     * @returns 返回对应品牌ID的草稿信息封装对象BrandDraftVO
     */
    BrandDraftVO getBrandDraftVo(Long brandId);


    /**
     * @function 根据品牌ID移除对应的品牌信息
     * @param brandId 需要被移除的品牌的唯一标识符
     * @returns 返回一个布尔值，如果成功移除则为true，否则为false
     */
    boolean remove(Long brandId);
}
