package com.jdi.isc.product.soa.service.atomic.customerSku;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceAuditReqVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceAuditResVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceDraftReqVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceDraftVO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDraftPO;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.repository.mapper.customerSku.CustomerSkuPriceDraftBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * @Description: sku客制化价格-草稿表原子服务
 * @Author: zhaokun51
 * @Date: 2024/10/21 13:30
 **/
@Slf4j
@Service
public class CustomerSkuPriceDraftAtomicService extends ServiceImpl<CustomerSkuPriceDraftBaseMapper, CustomerSkuPriceDraftPO> {

    /**
     * 未删除的对象
     * @param id id
     * @return 对象
     */
    public CustomerSkuPriceDraftPO getValidById(Long id){
        LambdaQueryWrapper<CustomerSkuPriceDraftPO> wrapper = Wrappers.<CustomerSkuPriceDraftPO>lambdaQuery()
                .eq(CustomerSkuPriceDraftPO::getId, id)
                .eq(CustomerSkuPriceDraftPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    /**
     * 未删除的对象
     * @param ids id
     * @return 对象
     */
    public List<CustomerSkuPriceDraftPO> getValidByIds(Collection<Long> ids){
        if(CollectionUtils.isEmpty(ids)){
            return null;
        }
        LambdaQueryWrapper<CustomerSkuPriceDraftPO> wrapper = Wrappers.<CustomerSkuPriceDraftPO>lambdaQuery()
                .in(CustomerSkuPriceDraftPO::getId, ids)
                .eq(CustomerSkuPriceDraftPO::getYn, YnEnum.YES.getCode());
        return this.list(wrapper);
    }

    public CustomerSkuPriceDraftPO getOne(String clientCode,String customerTradeType,Long skuId,String currency){
        LambdaQueryWrapper<CustomerSkuPriceDraftPO> queryWrapper = Wrappers.<CustomerSkuPriceDraftPO>lambdaQuery()
                .eq(CustomerSkuPriceDraftPO::getSkuId,skuId)
                .eq(CustomerSkuPriceDraftPO::getClientCode,clientCode)
                .eq(CustomerSkuPriceDraftPO::getCustomerTradeType,customerTradeType)
                .eq(CustomerSkuPriceDraftPO::getCurrency,currency)
                ;
        return getOne(queryWrapper);
    }

    /**
     * 获取符合条件的总数
     * @param reqVO 查询条件对象
     * @return 符合条件的记录总数
     */
    public long getTotal(CustomerSkuPriceDraftReqVO reqVO){
        return baseMapper.getTotal(reqVO);
    }

    /**
     * 获取符合条件的总数
     * @param reqVO 查询条件对象
     * @return 符合条件的记录总数
     */
    public List<CustomerSkuPriceDraftVO> listSearch(CustomerSkuPriceDraftReqVO reqVO){
        return baseMapper.listSearch(reqVO);
    }

    /**
     * 根据审核状态查询客户SKU价格草稿列表。
     * @param auditStatusEnum 审核状态枚举值。
     * @return 符合条件的客户SKU价格草稿列表。
     */
    public List<CustomerSkuPriceDraftPO> queryList(AuditStatusEnum auditStatusEnum){
        if(auditStatusEnum == null){
            log.info("CustomerSkuPriceDraftAtomicService.queryList param is null");
            return null;
        }
        LambdaQueryWrapper<CustomerSkuPriceDraftPO> qw = new LambdaQueryWrapper<CustomerSkuPriceDraftPO>();
        qw.eq(CustomerSkuPriceDraftPO::getAuditStatus,auditStatusEnum.getCode());
        qw.eq(CustomerSkuPriceDraftPO::getYn,YnEnum.YES.getCode());
        return this.list(qw);
    }

    /**
     * 根据流程实例ID查询客户SKU价格草稿信息。
     * @param processInstanceId 流程实例ID
     * @return 对应流程实例ID的客户SKU价格草稿信息，若无则返回null
     */
    public CustomerSkuPriceDraftPO queryByProcessInstanceId(String processInstanceId){
        if(StringUtils.isBlank(processInstanceId)){
            log.info("CustomerSkuPriceDraftAtomicService.queryByProcessInstanceId param is null");
            return null;
        }
        LambdaQueryWrapper<CustomerSkuPriceDraftPO> qw = new LambdaQueryWrapper<CustomerSkuPriceDraftPO>();
        qw.eq(CustomerSkuPriceDraftPO::getProcessInstanceId,processInstanceId);
        qw.eq(CustomerSkuPriceDraftPO::getYn,YnEnum.YES.getCode());
        return this.getOne(qw);
    }

    /**
     * 获取待审批符合条件的总数
     * @param reqVO 查询条件对象
     * @return 符合条件的记录总数
     */
    public long getApproveTotal(CustomerSkuPriceAuditReqVO reqVO){
        return baseMapper.getApproveTotal(reqVO);
    }

    /**
     * 获取符合条件的总数
     * @param reqVO 查询条件对象
     * @return 符合条件的记录总数
     */
    public List<CustomerSkuPriceAuditResVO> listApproveSearch(CustomerSkuPriceAuditReqVO reqVO){
        return baseMapper.listApproveSearch(reqVO);
    }

    /**
     * 根据商品ID集合查询有效的商品价格草稿信息。
     * @param skuIds 商品ID集合
     * @return 对应商品ID的商品价格草稿信息列表，若无匹配或传入参数为空则返回null
     */
    public List<CustomerSkuPriceDraftPO> queryListBySkuId(Collection<Long> skuIds){
        if(CollectionUtils.isEmpty(skuIds)){
            return null;
        }
        LambdaQueryWrapper<CustomerSkuPriceDraftPO> qw = new LambdaQueryWrapper<CustomerSkuPriceDraftPO>();
        qw.in(CustomerSkuPriceDraftPO::getSkuId,skuIds);
        qw.eq(CustomerSkuPriceDraftPO::getYn,YnEnum.YES.getCode());
        return this.list(qw);
    }

    public List<CustomerSkuPriceDraftPO> listBySkuIds(Collection<Long> skuIds){
        if(CollectionUtils.isEmpty(skuIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CustomerSkuPriceDraftPO> qw = new LambdaQueryWrapper<CustomerSkuPriceDraftPO>();
        qw.in(CustomerSkuPriceDraftPO::getSkuId,skuIds);
        qw.eq(CustomerSkuPriceDraftPO::getYn,YnEnum.YES.getCode());
        return this.list(qw);
    }
}
