package com.jdi.isc.product.soa.service.protocol.jsf.taxRate;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.taxRate.IscTaxRateReadApiService;
import com.jdi.isc.product.soa.api.taxRate.req.CustomerTaxQueryReqDTO;
import com.jdi.isc.product.soa.api.taxRate.req.PurchaseTaxQueryReqDTO;
import com.jdi.isc.product.soa.api.taxRate.req.TaxRateReqDTO;
import com.jdi.isc.product.soa.api.taxRate.res.CustomerTaxResDTO;
import com.jdi.isc.product.soa.api.taxRate.res.PurchaseSkuTaxResDTO;
import com.jdi.isc.product.soa.api.taxRate.res.TaxRateApiDTO;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRatePageVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRateVO;
import com.jdi.isc.product.soa.domain.taxRate.req.CustomerTaxQueryReqVO;
import com.jdi.isc.product.soa.domain.taxRate.req.PurchaseTaxQueryReqVO;
import com.jdi.isc.product.soa.domain.taxRate.res.CustomerTaxResVO;
import com.jdi.isc.product.soa.domain.taxRate.res.PurchaseSkuTaxResVO;
import com.jdi.isc.product.soa.service.manage.taxRate.CustomerTaxManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.PurchaseTaxManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.TaxRateManageService;
import com.jdi.isc.product.soa.service.mapstruct.taxRate.TaxRateConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class IscTaxRateReadApiServiceImpl implements IscTaxRateReadApiService {

    @Resource(name="taxRateManageServiceImpl")
    private TaxRateManageService taxRateManageServiceImpl;
    @Resource
    private CustomerTaxManageService customerTaxManageService;
    @Resource
    private PurchaseTaxManageService purchaseTaxManageService;

    @Override
    public DataResponse<TaxRateApiDTO> getDetailById(TaxRateReqDTO dto) {
        TaxRateVO vo = TaxRateConvert.INSTANCE.dto2Vo(dto);
        TaxRateVO taxRateVO = taxRateManageServiceImpl.getDetailById(vo);
        TaxRateApiDTO taxRateApiDTO = TaxRateConvert.INSTANCE.vo2ApiDto(taxRateVO);
        return DataResponse.success(taxRateApiDTO);
    }

    @Override
    public DataResponse<PageInfo<TaxRateApiDTO>> pageTaxRate(TaxRateReqDTO dto) {
        TaxRatePageVO taxRatePageVO = TaxRateConvert.INSTANCE.dto2PageVo(dto);
        PageInfo<TaxRateVO> pageInfo = taxRateManageServiceImpl.pageTaxRate(taxRatePageVO);
        List<TaxRateVO> records = pageInfo.getRecords();
        List<TaxRateApiDTO> apiDTOList = TaxRateConvert.INSTANCE.listVo2Dto(records);
        PageInfo<TaxRateApiDTO> pageInfoDTO = new PageInfo<>();
        pageInfoDTO.setRecords(apiDTOList);
        pageInfoDTO.setSize(pageInfo.getSize());
        pageInfoDTO.setTotal(pageInfo.getTotal());
        log.info("IscTaxRateReadApiServiceImpl.pageTaxRate param:{},res:{}", JSON.toJSONString(dto), JSON.toJSONString(apiDTOList));
        return DataResponse.success(pageInfoDTO);
    }

    @Override
    public DataResponse<Boolean> exists(TaxRateReqDTO dto) {
        TaxRateVO vo = TaxRateConvert.INSTANCE.dto2Vo(dto);
        return DataResponse.success(taxRateManageServiceImpl.exists(vo));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<CustomerTaxResDTO> batchQueryCustomerTax(CustomerTaxQueryReqDTO reqDTO) {
        CustomerTaxQueryReqVO customerTaxQueryReqVO = TaxRateConvert.INSTANCE.reqDto2Vo(reqDTO);
        DataResponse<CustomerTaxResVO> dataResponse = customerTaxManageService.batchQueryCustomerTax(customerTaxQueryReqVO);
        CustomerTaxResDTO customerTaxResDTO = TaxRateConvert.INSTANCE.taxResVo2ResDTO(dataResponse.getData());
        return DataResponse.success(customerTaxResDTO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, PurchaseSkuTaxResDTO>> batchQueryPurchaseTax(PurchaseTaxQueryReqDTO reqDTO) {
        PurchaseTaxQueryReqVO purchaseTaxQueryReqVO = TaxRateConvert.INSTANCE.purchaseReqDto2Vo(reqDTO);
        Map<Long, PurchaseSkuTaxResVO> skuTaxResVOMap = purchaseTaxManageService.batchQueryPurchaseTax(purchaseTaxQueryReqVO);
        Map<Long, PurchaseSkuTaxResDTO> skuTaxResDTOMap = TaxRateConvert.INSTANCE.purchaseResVoMap2Dto(skuTaxResVOMap);
        return DataResponse.success(skuTaxResDTOMap);
    }
}
