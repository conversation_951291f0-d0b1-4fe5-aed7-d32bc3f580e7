package com.jdi.isc.product.soa.service.mapstruct.price;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.price.req.FulfillmentRateReqApiDTO;
import com.jdi.isc.product.soa.api.price.res.FulfillmentRateApiDTO;
import com.jdi.isc.product.soa.domain.price.biz.FulfillmentRateReqVO;
import com.jdi.isc.product.soa.domain.price.biz.FulfillmentRateVO;
import com.jdi.isc.product.soa.domain.price.po.FulfillmentRatePO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 履约费率表对象转换
 * @Author: zhaokun51
 * @Date: 2024/10/29 21:47
 **/
@Mapper
public interface FulfillmentRateConvert {

    FulfillmentRateConvert INSTANCE = Mappers.getMapper(FulfillmentRateConvert.class);

    @Mapping(source = "firstCatId", target = "firstJdCatId")
    @Mapping(source = "secondCatId", target = "secondJdCatId")
    @Mapping(source = "thirdCatId", target = "thirdJdCatId")
    @Mapping(source = "lastCatId", target = "lastJdCatId")
    FulfillmentRatePO vo2Po(FulfillmentRateVO vo);

    @Mapping(source = "firstJdCatId", target = "firstCatId")
    @Mapping(source = "secondJdCatId", target = "secondCatId")
    @Mapping(source = "thirdJdCatId", target = "thirdCatId")
    @Mapping(source = "lastJdCatId", target = "lastCatId")
    FulfillmentRateVO po2Vo(FulfillmentRatePO po);

    List<FulfillmentRateVO> listPo2Vo(List<FulfillmentRatePO> poList);

    List<FulfillmentRatePO> listVo2Po(List<FulfillmentRateVO> voList);

    /** api jsf实现层使用开始 */
    FulfillmentRateReqVO reqApiDTO2ReqVo(FulfillmentRateReqApiDTO input);

    FulfillmentRateVO apiDTO2Vo(FulfillmentRateApiDTO input);

    FulfillmentRateApiDTO vo2ResReqApiDto(FulfillmentRateVO input);

    PageInfo<FulfillmentRateApiDTO> pageVo2PageResApi(PageInfo<FulfillmentRateVO> input);
    /** api jsf实现层使用结束 */

}
