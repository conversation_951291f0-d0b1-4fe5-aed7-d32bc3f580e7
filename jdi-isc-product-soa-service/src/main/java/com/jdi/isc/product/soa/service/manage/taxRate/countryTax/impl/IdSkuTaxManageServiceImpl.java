package com.jdi.isc.product.soa.service.manage.taxRate.countryTax.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.IdSkuTaxPO;
import com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum;
import com.jdi.isc.product.soa.price.api.price.req.IdSkuTaxVO;
import com.jdi.isc.product.soa.service.adapter.mapstruct.countryTax.IdSkuTaxConvert;
import com.jdi.isc.product.soa.service.atomic.taxRate.countryTax.IdSkuTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.IdSkuTaxManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 印尼税率管理服务
 * <AUTHOR>
 * @date 2025/3/10
 */
@Slf4j
@Service
public class IdSkuTaxManageServiceImpl extends BaseTaxManageService implements IdSkuTaxManageService {

    @Resource
    private IdSkuTaxAtomicService idSkuTaxAtomicService;

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> saveOrUpdate(IdSkuTaxVO input) {
        boolean flag;
        //将关税除以100保存
        input.setNormalImportTax(input.getNormalImportTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setOriginMfnTax(input.getOriginMfnTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setLuxuryTax(input.getLuxuryTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setValueAddedTax(input.getValueAddedTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setWithholdingTax(input.getWithholdingTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setTradeProtectionTax(input.getTradeProtectionTax().divide(factor,5, RoundingMode.HALF_UP));
        JdProductDTO skuPO = getSku(input.getJdSkuId());
        if(skuPO==null){
            return DataResponse.error(String.format("零售skuId:%s 不存在,请检查",input.getJdSkuId()));
        }
        IdSkuTaxPO res = idSkuTaxAtomicService.getOne(input);
        if(res==null){
            IdSkuTaxPO target = IdSkuTaxConvert.INSTANCE.vo2Po(input);
            target.setCreateTime(new Date());
            target.setUpdateTime(target.getCreateTime());
            flag = idSkuTaxAtomicService.save(target);
            recordLog(target.getJdSkuId(), PriceTypeEnum.ID_TAX, target.getNormalImportTax(),target,flag);
        }else {
            input.setId(res.getId());
            flag = idSkuTaxAtomicService.updateTax(input);
            recordLog(input.getJdSkuId(), PriceTypeEnum.ID_TAX, input.getNormalImportTax(),input,flag);
        }
        return DataResponse.success(flag);
    }

}
