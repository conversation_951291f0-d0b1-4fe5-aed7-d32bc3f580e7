package com.jdi.isc.product.soa.service.manage.tool;


import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.fastjson.JSON;
import com.jd.limitbuy.domain.request.limit.StrategyBatchRequest;
import com.jd.limitbuy.domain.request.limit.StrategyRequest;
import com.jd.limitbuy.domain.response.limit.StrategyBatchResponse;
import com.jd.limitbuy.domain.response.limit.StrategyResponse;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.order.center.api.ofc.biz.StoreHouseConsigneeInfoDTO;
import com.jdi.isc.order.center.api.ofc.biz.rsp.CustomerSkuDecisionResult;
import com.jdi.isc.order.center.api.ofc.biz.rsp.SkuInfoResDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.sku.SkuStatusEnum;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.gms.req.JdProductQueryDTO;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuLogSaveVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.biz.UpdateSkuStatusReqVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuAuditRecordPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.product.soa.rpc.gms.SkuLimitBuyRpcService;
import com.jdi.isc.product.soa.rpc.order.FulfillmentPolicyRpcService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAuditRecordAtomicService;
import com.jdi.isc.product.soa.service.manage.sku.SkuStatusWriteManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * SKU数据工具类
 * <AUTHOR>
 * @description：SkuToolService
 * @Date 2025-07-16
 */
@Slf4j
@Service
public class SkuTool2Service {

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private SpuAuditRecordAtomicService spuAuditRecordAtomicService;

    @Resource
    private SkuInfoRpcService skuInfoRpcService;

    @Resource
    private SkuStatusWriteManageService skuStatusWriteManageService;

    @Resource
    private SkuLimitBuyRpcService skuLimitBuyRpcService;

    @Resource
    private FulfillmentPolicyRpcService fulfillmentPolicyRpcService;

    @Resource
    private SkuDraftAtomicService skuDraftAtomicService;

    @Resource
    private SkuLogAtomicService skuLogAtomicService;

    final ExecutorService pool = new ThreadPoolExecutor(10, 50, 30L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(10000), new ThreadFactoryBuilder().setNamePrefix("product-soa-mainSkuStatus-").build());


    public String updateSkuStatus(String env, Set<Long> skuIds){

        if (StringUtils.isBlank(env) || !"uat_env".equals(env)) {
            return "环境参数错误";
        }

        LambdaQueryWrapper<SkuPO> queryWrapper = Wrappers.lambdaQuery(SkuPO.class)
                .select(SkuPO::getSkuId, SkuPO::getJdSkuId, SkuPO::getUpdater, SkuPO::getSkuStatus)
                .in(CollectionUtils.isNotEmpty(skuIds), SkuPO::getSkuId, skuIds)
                .isNotNull(SkuPO::getJdSkuId)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> skuPOList = skuAtomicService.list(queryWrapper);

        int total = skuPOList.size();
        AtomicInteger current = new AtomicInteger(1);

        List<List<SkuPO>>  partition = Lists.partition(skuPOList, 10);

        for (List<SkuPO> sub : partition) {
            List<Long> jdSkuIds = sub.stream().map(SkuPO::getJdSkuId).collect(Collectors.toList());
            // 查询京东商品信息
            Map<Long, JdProductDTO> productDTOMap = skuInfoRpcService.querySkuMap(new JdProductQueryDTO(jdSkuIds));
            List<CompletableFuture<Boolean>> completableFutureList = Lists.newArrayListWithExpectedSize(sub.size());
            for (SkuPO skuPO : sub) {
                log.info("SkuToolService.updateSkuStatus 同步国内商品状态开始  skuId={},jdSkuId={}, current={} , total={}", skuPO.getSkuId(),skuPO.getJdSkuId(),current,total);
                if (Objects.isNull(skuPO.getJdSkuId())){
                    log.error("SkuToolService.updateSkuStatus 国内商品信息 京东SKU ID为空 skuId={} current={} , total={}", skuPO.getSkuId(),current,total);
                    current.incrementAndGet();
                    continue;
                }

                JdProductDTO productDTO = productDTOMap.get(skuPO.getJdSkuId());
                if (Objects.isNull(productDTO)) {
                    log.error("SkuToolService.updateSkuStatus 国内商品信息为空 skuId={},jdSkuId={} current={} , total={}", skuPO.getSkuId(),skuPO.getJdSkuId(),current,total);
                    current.incrementAndGet();
                    continue;
                }
                log.info("SkuToolService.updateSkuStatus 国内商品信息  skuId={},jdSkuId={},国内skuStatus={} current={} , total={}", skuPO.getSkuId(),skuPO.getJdSkuId(),productDTO.getSkuStatus() ,current,total);
                skuPO.setSkuStatus(Integer.valueOf(productDTO.getSkuStatus()));
                completableFutureList.add(CompletableFuture.supplyAsync(() -> asyncSkuStatus(skuPO, current, total), pool));
                current.incrementAndGet();
            }

            try {
                // 取回结果，等候5秒
                CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).get(10, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("SkuToolService.updateSkuStatus 同步主站SKU状态 取回结果异常",e);
            }
        }

        return "success";
    }

    private Boolean asyncSkuStatus(SkuPO skuPO, AtomicInteger current, int total) {
        UpdateSkuStatusReqVO skuStatusReqVO = new UpdateSkuStatusReqVO();
        skuStatusReqVO.setSkuId(skuPO.getSkuId());
        skuStatusReqVO.setSkuStatus(SkuStatusEnum.defaultSkuStatus(skuPO.getSkuStatus()));
        skuStatusReqVO.setUpdater("同步主站skuStatus");
        if (SkuStatusEnum.DOWN.getStatus() == skuStatusReqVO.getSkuStatus()){
            skuStatusReqVO.setDownReason("同步主站下架状态");
        }
        DataResponse<Boolean> response = skuStatusWriteManageService.skuUpOrDown(skuStatusReqVO);
        log.info("SkuToolService.updateSkuStatus 同步国内商品状态结束更新结果:{}, skuId={},jdSkuId={}, current={} , total={}", JSON.toJSONString(response), skuPO.getSkuId(), skuPO.getJdSkuId(), current, total);
        return response.getData();
    }


    /**
     * 添加跨境SPU审核记录。
     * @param env 环境参数，必须为'uat_env'。
     * @param spuIds SPU ID集合，用于查询需要添加审核记录的SPU。
     * @return 添加成功返回'success'，否则返回错误信息。
     */
    public String addCrossBorderSpuAuditRecord(String env,Set<Long> spuIds){
        if (StringUtils.isBlank(env) || !"uat_env".equals(env)) {
            return "环境参数错误";
        }

        LambdaQueryWrapper<SpuPO> queryWrapper = Wrappers.lambdaQuery(SpuPO.class)
                .in(CollectionUtils.isNotEmpty(spuIds), SpuPO::getSpuId, spuIds)
                .eq(SpuPO::getSourceCountryCode, CountryConstant.COUNTRY_ZH)
                .eq(SpuPO::getYn, YnEnum.YES.getCode());

        List<SpuPO> spuPOList = spuAtomicService.list(queryWrapper);

        if (CollectionUtils.isEmpty(spuPOList)) {
            return "商品信息为空";
        }

        int total = spuPOList.size();
        log.info("addCrossBorderSpuAuditRecord: 总共处理 {} 个SPU", total);

        for (int i = 0; i < total; i++) {
            SpuPO spuPO = spuPOList.get(i);
            log.info("addCrossBorderSpuAuditRecord: 正在处理第 {} 个SPU开始，SpuId: {}", i + 1, spuPO.getSpuId());
            List<SpuAuditRecordPO> recordList = spuAuditRecordAtomicService.getBySpuId(spuPO.getSpuId());
            if (CollectionUtils.isEmpty(recordList)) {
                SpuAuditRecordPO spuAuditRecordPO = getSpuAuditRecordPO(spuPO);
                spuAuditRecordAtomicService.save(spuAuditRecordPO);
            }else {
                SpuAuditRecordPO first = recordList.stream().min(Comparator.comparing(SpuAuditRecordPO::getCreateTime)).orElseGet(null);
                if (Objects.nonNull(first) && SpuAuditStatusEnum.APPROVED.getCode().equals(first.getAuditStatus())) {
                    SpuAuditRecordPO spuAuditRecordPO = getSpuAuditRecordPO(spuPO);
                    spuAuditRecordAtomicService.save(spuAuditRecordPO);
                }
            }
            log.info("addCrossBorderSpuAuditRecord: 正在处理第 {} 个SPU结束，SpuId: {}", i + 1, spuPO.getSpuId());

        }
        return "success";
    }

    private SpuAuditRecordPO getSpuAuditRecordPO(SpuPO spuPO) {
        SpuAuditRecordPO spuAuditRecordPO = new SpuAuditRecordPO();
        spuAuditRecordPO.setSpuId(spuPO.getSpuId());
        spuAuditRecordPO.setAuditStatus(SpuAuditStatusEnum.WAITING_APPROVED.getCode());
        spuAuditRecordPO.setYn(YnEnum.YES.getCode());
        spuAuditRecordPO.setAuditErp(spuPO.getCreator());
        spuAuditRecordPO.setCreator(spuPO.getCreator());
        spuAuditRecordPO.setCreateTime(spuPO.getCreateTime());
        spuAuditRecordPO.setUpdater(spuPO.getCreator());
        spuAuditRecordPO.setUpdateTime(spuPO.getCreateTime());
        return spuAuditRecordPO;
    }


    public DataResponse<String> queryJdSkuLimitBuy(Long skuId, String countryCode){
        if (StringUtils.isBlank(countryCode)) {
            return DataResponse.error("国家编码不能为空");
        }

        if (Objects.isNull(skuId)) {
            return DataResponse.error("skuId不能为空");
        }

        CustomerSkuDecisionResult customerSkuDecisionResult = fulfillmentPolicyRpcService.querySkuMultiWarehouseDecisionByCountry(countryCode, Sets.newHashSet(skuId));
        if (Objects.isNull(customerSkuDecisionResult) || MapUtils.isEmpty(customerSkuDecisionResult.getSkuFulfillmentDecisionResult())){
            log.info("queryJdSkuLimitBuy 查询结果为空，skuId={}, countryCode={} customerSkuDecisionResult={}", skuId, countryCode,JSON.toJSONString(customerSkuDecisionResult));
            return DataResponse.error("商品履约地址策略，查询结果为空");
        }

        Map<String, List<SkuInfoResDTO>> resultMap = customerSkuDecisionResult.getSkuFulfillmentDecisionResult();
        String result = "";

        Map<String,String> batchStrategyMap = new HashMap<>();

        for (Map.Entry<String, List<SkuInfoResDTO>> entry : resultMap.entrySet()) {
            List<SkuInfoResDTO> skuInfoResDTOS = entry.getValue();
            for (SkuInfoResDTO dto : skuInfoResDTOS) {
                if (StringUtils.isNotBlank(result)) {
                    continue;
                }
                SkuPO skuPO = skuAtomicService.getSkuPoBySkuId(skuId);
                StrategyRequest request = new StrategyRequest();
                request.setSkuId(String.valueOf(skuPO.getJdSkuId()));
                StoreHouseConsigneeInfoDTO storeHouseConsigneeInfoDTO = dto.getStoreHouseConsigneeInfoDTO();
                request.setProvinceId(storeHouseConsigneeInfoDTO.getConsigneeProvinceId().intValue());
                request.setCityId(storeHouseConsigneeInfoDTO.getConsigneeCityId().intValue());
                request.setTownId(storeHouseConsigneeInfoDTO.getConsigneeTownId().intValue());
                request.setCountyId(storeHouseConsigneeInfoDTO.getConsigneeCountyId().intValue());
                StrategyResponse strategyResponse = skuLimitBuyRpcService.getStrategy(request);
                result = JSON.toJSONString(strategyResponse);

                batchStrategyMap.put("EachSku", JSON.toJSONString(strategyResponse));
            }
        }


        for (Map.Entry<String, List<SkuInfoResDTO>> entry : resultMap.entrySet()) {
            List<SkuInfoResDTO> skuInfoResDTOS = entry.getValue();
            Set<Long> skuIds = skuInfoResDTOS.stream().map(SkuInfoResDTO::getSkuId).collect(Collectors.toSet());

            List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(skuIds);
            if (CollectionUtils.isEmpty(skuPOList)) {
                continue;
            }
            List<String> jdSkuIds = skuPOList.stream().filter(po -> Objects.nonNull(po.getJdSkuId())).map(skuPo-> String.valueOf(skuPo.getJdSkuId())).collect(Collectors.toList());
            StrategyBatchRequest batchRequest = new StrategyBatchRequest();
            batchRequest.setSkuIdList(jdSkuIds);
            SkuInfoResDTO dto = skuInfoResDTOS.get(0);
            StoreHouseConsigneeInfoDTO storeHouseConsigneeInfoDTO = dto.getStoreHouseConsigneeInfoDTO();
            batchRequest.setProvinceId(storeHouseConsigneeInfoDTO.getConsigneeProvinceId().intValue());
            batchRequest.setCityId(storeHouseConsigneeInfoDTO.getConsigneeCityId().intValue());
            batchRequest.setTownId(storeHouseConsigneeInfoDTO.getConsigneeTownId().intValue());
            batchRequest.setCountyId(storeHouseConsigneeInfoDTO.getConsigneeCountyId().intValue());
            StrategyBatchResponse strategyBatch = skuLimitBuyRpcService.getStrategyBatch(batchRequest);

            batchStrategyMap.put("BatchSku", JSON.toJSONString(strategyBatch));

        }
        result = JSON.toJSONString(batchStrategyMap);
        return DataResponse.success(result);
    }

    public DataResponse<String> updateVendorSkuId(String env,Set<Long> skuIds){
        if (StringUtils.isBlank(env) || !"uat_env".equals(env)) {
            return DataResponse.error("环境参数错误");
        }

        LambdaQueryWrapper<SkuPO> queryWrapper = Wrappers.lambdaQuery(SkuPO.class)
                .eq(SkuPO::getYn, YnEnum.YES.getCode())
                .in(CollectionUtils.isNotEmpty(skuIds), SkuPO::getSkuId, skuIds)
                .isNotNull(SkuPO::getVendorSkuId);

        List<SkuPO> skuPOList = skuAtomicService.list(queryWrapper);

        if (CollectionUtils.isEmpty(skuPOList)) {
            return DataResponse.error("没有需要更新的数据");
        }

        int total = skuPOList.size();
        List<List<SkuPO>> partition = Lists.partition(skuPOList, 100);

        String updater = "更新供应商SKUID新字段";
        int current = 1;
        for (List<SkuPO> skuPOS : partition) {
            for (SkuPO skuPO : skuPOS) {

                SpuPO spuPO = spuAtomicService.getSpuPoBySpuId(skuPO.getSpuId());
                if (!SpuAuditStatusEnum.APPROVED.getCode().equals(spuPO.getAuditStatus())) {
                    log.info("updateVendorSkuId 更新供应商SKUID到新字段，不是审核状态，不能修改  skuId={} vendorSkuId={}", skuPO.getSkuId(),skuPO.getVendorSkuId());
                    current++;
                    continue;
                }


                String originSkuPo = JSON.toJSONString(skuPO);
                log.info("updateVendorSkuId 更新供应商SKUID到新字段开始，当前进度：{}/{}  skuId={} vendorSkuId={}", current, total ,skuPO.getSkuId(),skuPO.getVendorSkuId());
                skuPO.setVendorSkuIdNew(String.valueOf(skuPO.getVendorSkuId()));

                skuPO.setUpdater(updater);
                skuPO.setUpdateTime(new Date());
                skuPO.setSpuId(null);
                skuAtomicService.updateById(skuPO);

                SkuLogSaveVO logSaveVO = new SkuLogSaveVO(KeyTypeEnum.SKU.getCode(),String.valueOf(skuPO.getSkuId()),originSkuPo
                        , JSON.toJSONString(skuPO), updater);
                skuLogAtomicService.saveLog(logSaveVO);

                // 查询草稿数据
                LambdaQueryWrapper<SkuDraftPO> skuDraftPOLambdaQueryWrapper = Wrappers.lambdaQuery(SkuDraftPO.class)
                        .select(SkuDraftPO::getId, SkuDraftPO::getSkuJsonInfo, SkuDraftPO::getUpdater)
                        .eq(SkuDraftPO::getYn, YnEnum.YES.getCode())
                        .eq(SkuDraftPO::getSkuId, skuPO.getSkuId());

                SkuDraftPO skuDraftPo = skuDraftAtomicService.getOne(skuDraftPOLambdaQueryWrapper);

                if (skuDraftPo != null && StringUtils.isNotBlank(skuDraftPo.getSkuJsonInfo())) {
                    SkuVO skuVO = JSON.parseObject(skuDraftPo.getSkuJsonInfo(), SkuVO.class);
                    skuVO.setVendorSkuIdNew(skuPO.getVendorSkuIdNew());

                    skuDraftPo.setSkuJsonInfo(JSON.toJSONString(skuVO));
                    skuDraftPo.setUpdateTime(System.currentTimeMillis());
                    skuDraftPo.setUpdater(updater);
                    skuDraftAtomicService.updateById(skuDraftPo);
                }
                log.info("updateVendorSkuId 更新供应商SKUID到新字段结束，当前进度：{}/{}  skuId={} vendorSkuId={}", current, total ,skuPO.getSkuId(),skuPO.getVendorSkuId());
                current++;
            }
        }
        return DataResponse.success();
    }

}
