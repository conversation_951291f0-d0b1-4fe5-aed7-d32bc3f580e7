package com.jdi.isc.product.soa.service.mapstruct.mku;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.mku.req.SpecialAttrMkuClientReqDTO;
import com.jdi.isc.product.soa.api.mku.req.SpecialAttrMkuReqDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuDescLangDTO;
import com.jdi.isc.product.soa.api.sku.req.SkuUpdateApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.*;
import com.jdi.isc.product.soa.domain.customerMku.biz.CustomerLatestMkuReqVO;
import com.jdi.isc.product.soa.domain.mku.biz.*;
import com.jdi.isc.product.soa.domain.mku.po.MkuDescLangPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import com.jdi.isc.product.soa.domain.mku.po.es.MkuEsPO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.stock.mku.res.IscMkuStockResDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * mku对象转换
 *
 * <AUTHOR>
 * @date 2023/11/26
 **/
@Mapper
public interface MkuConvert {

    MkuConvert INSTANCE = Mappers.getMapper(MkuConvert.class);


    @Mappings({
            @Mapping(source="key", target = "keyword" )
    })
    MkuClientPageReqVO mkuClientPageReqDTO2VO(MkuClientPageReqApiDTO pageReqApiDTO);

    PageInfo<MkuClientApiDTO> mkuClientPageVO2DTO(PageInfo<MkuClientVO> pageInfo);

    List<MkuClientApiDTO> listMkuClientVO2DTO(List<MkuClientVO> pageInfo);

    MkuClientDetailReqVO mkuClientDetailReqDTO2VO(MkuClientDetailReqApiDTO input);

    List<MkuClientDetailReqVO> listMkuReqDTO2VO(List<MkuClientDetailReqApiDTO> dtoList);

    List<MkuClientInPoolApiDTO> listVO2DTO(List<MkuClientInPoolVO> dtoList);
    @Mappings({
            @Mapping(source = "mainImg", target = "mkuImage"),
            @Mapping(source = "jdCatId", target = "catId")
    })
    MkuClientVO mkuPo2mkuClientVo(MkuPO mkuPO);

    List<MkuClientVO> listMkuPo2mkuClientVo(List<MkuPO> mkuPOList);

    @Mappings({
            @Mapping(ignore = true, target = "createTime"),
            @Mapping(source = "catId", target = "jdCatId")
    })
    MkuPO skuVo2mkuPO(SkuVO skuVO);


    @Mapping(source = "createTime", target = "createTime")
    @Mapping(source = "updateTime", target = "updateTime")
    List<MkuVO> apiDtoList2MkuVO(List<SkuUpdateApiDTO> skuUpdateApiDTOList);

    default Long map(Date value) {
        return value != null ? value.getTime() : null;
    }

    default Date map(Long value) {
        return value != null ? new Date(value) : null;
    }

    SpecialAttrMkuReqVO specialAttrMkuReqDTO2VO(SpecialAttrMkuReqDTO input);

    CustomerLatestMkuReqVO latestWareReqDTO2VO(MkuClientLatestWareReqApiDTO input);

    MkuListInfoReqReqVO queryWaresInfoReqDTO2VO(MkuListInfoReqApiDTO input);

    SpecialAttrMkuClientReqVO specialAttrMkuClientDTO2VO(SpecialAttrMkuClientReqDTO input);

    MkuClientStockResApiDTO mkuClientStockVO2ApiDto(MkuClientStockVO input);

    MkuFeatureTagVO mkuFeatureTagDto2Vo(MkuFeatureTagDTO mkuFeatureTagDTO);

    IscMkuDescLangDTO mkuDescLangPo2Dto(MkuDescLangPO input);

    List<IscMkuDescLangDTO> listMkuDescLangPo2Dto(List<MkuDescLangPO> input);

    MkuEsClientVO mkuEsPo2EsClientVO(MkuEsPO input);

    MkuEsClientDTO mkuEsClientVO2DTO(MkuEsClientVO mkuEsClientVO);

    MkuClientDetailReqVO mkuEsClientDetailReqDTO2VO(MkuEsDetailReqApiDTO input);

    MkuPromiseFeatureTagVO mkuPromiseTagDto2Vo(MkuPromiseTagDTO mkuPromiseTagDTO);

}
