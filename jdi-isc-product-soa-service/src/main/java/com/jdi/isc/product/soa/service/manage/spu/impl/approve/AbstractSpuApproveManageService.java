package com.jdi.isc.product.soa.service.manage.spu.impl.approve;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.enums.SpuApproveCountryTypeEnums;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuStatusEnum;
import com.jdi.isc.product.soa.api.jdm.push.req.JdmSupplierMessageReqDTO;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.frame.SystemContextHolder;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.domain.category.biz.MailMessageContextVO;
import com.jdi.isc.product.soa.domain.enums.AmendEnum;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.SendMailTemplateEnum;
import com.jdi.isc.product.soa.domain.enums.product.AuditLevelEnum;
import com.jdi.isc.product.soa.domain.enums.spu.SpuTaxCountryEnums;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceReqVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceResVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.*;
import com.jdi.isc.product.soa.domain.supplier.biz.SupplierAccountVO;
import com.jdi.isc.product.soa.service.atomic.category.CategoryBuyerRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.*;
import com.jdi.isc.product.soa.service.atomic.taxRate.CategoryTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.message.JdmMessageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuWriteManageService;
import com.jdi.isc.product.soa.service.manage.spu.*;
import com.jdi.isc.product.soa.service.manage.spu.validate.SpuValidateService;
import com.jdi.isc.product.soa.service.manage.supplier.SupplierAccountManageService;
import com.jdi.isc.product.soa.service.mapstruct.ProductAttributeConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SkuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuConvert;
import com.jdi.isc.product.soa.service.protocol.http.mail.MailService;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import com.jdi.isc.product.soa.service.support.DataResponseMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant.SPU_AUDIT_LOCK_FAILED;


/**
 * spu审核抽象服务
 *
 * <AUTHOR>
 * @date 2023/12/18
 **/
@Slf4j
@Service
public abstract class AbstractSpuApproveManageService implements SpuApproveManageService {

    @Resource
    private SpuAuditRecordManageService spuAuditRecordManageService;

    @Resource
    @Lazy
    private SpuConvertService spuConvertService;

    @Resource
    protected SkuConvertService skuConvertService;

    @Resource
    protected SpuAtomicService spuAtomicService;

    @Resource
    private SpuLangAtomicService spuLangAtomicService;

    @Resource
    private SpuDescLangAtomicService spuDescLangAtomicService;

    @Resource
    private SpuCertificateAtomicService spuCertificateAtomicService;

    @Resource
    protected SpuValidateService spuValidateService;

    @Resource
    protected SpuDraftManageService spuDraftManageService;

    @Resource
    protected SkuWriteManageService skuWriteManageService;

    @Resource
    private SkuPriceManageService skuPriceManageService;

    @Resource
    private SpuCompareService spuCompareService;

    @Resource
    protected DataResponseMessageService dataResponseMessageService;

    @Resource
    private AuditRelationAtomicService auditRelationAtomicService;

    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;

    @Resource
    protected JimUtils jimUtils;

    @Resource
    @Lazy
    protected MkuManageService mkuManageService;

    @Resource
    private JdmMessageService jdmMessageService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private SpuAmendAtomicService spuAmendService;

    @Resource
    private MailService mailService;

    @Resource
    private SupplierAccountManageService supplierAccountManageService;

    @Resource
    protected CategoryTaxAtomicService categoryTaxAtomicService;

    @Resource
    @Lazy
    protected Map<String, SpuTaxManageService> spuTaxManageServiceMap;

    @Resource
    private ProductAttributeConvertService productAttributeConvertService;

    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;

    protected static final String APPROVED_TEXT = "商品审批通过!";

    @LafValue("jdi.isc.spu.wvc.reject.message")
    private String rejectedMessage;

    @LafValue("jdi.isc.spu.wvc.reject.message.url")
    private String rejectedMessageUrl;

    @LafValue("jdi.isc.spu.wvc.amend.message")
    private String amendSpuMessage;

    @Value("${spring.profiles.active}")
    protected String systemProfile;

    @Resource
    private ExecutorService spuApproveExecutorService;

    @Resource
    private ProductMessageService productMessageService;

    @Resource
    protected CategoryBuyerRelationAtomicService categoryBuyerRelationAtomicService;

    /**
     * 审核操作，审核通过
     *
     * @param spuAuditRecordReqVO 审核对象
     * @return 审核结果
     */
    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 150)
    public DataResponse<String> batchApprove(SpuAuditRecordUpdateVO spuAuditRecordReqVO) {
        // erp
        final String erp = LoginContextHolder.getLoginContextHolder().getPin();
        final String systemCode = SystemContextHolder.get();
        final String lang = LangContextHolder.get();
        List<Long> spuIdList = spuAuditRecordReqVO.getSpuIds();
        try {
            // 校验
            spuValidateService.validateApproved(spuAuditRecordReqVO);
            String message = null;
            for (Long spuId : spuIdList) {
                try {
                    CompletableFuture<Void> task = CompletableFuture.runAsync(() -> {
                        ApiInitUtils.init(erp,lang,systemCode);
                        this.singleApproved(spuId,null);
                    }, spuApproveExecutorService);
                } catch (Exception e) {
                    message = String.format("%s：%s%s", spuId, e.getMessage(),"\n");
                }
            }
            return StringUtils.isNotBlank(message) ? DataResponse.error(message) : DataResponse.success(DataResponseCode.SUCCESS.getMessage());
        } catch (BizException bizException) {
            log.error("AbstractSpuApproveManageService.batchApprove warning spuAuditRecordReqVO={},message={}", JSON.toJSONString(spuAuditRecordReqVO), bizException.getMessage());
            throw bizException;
        } catch (Exception e) {
            log.error("【系统异常】AbstractSpuApproveManageService.batchApprove error erp={},spuAuditRecordReqVO={}", erp, JSON.toJSONString(spuAuditRecordReqVO), e);
            String errorMessage = dataResponseMessageService.getErrorMessage(DataResponseCodeConstant.SPU_APPROVED_ERROR);
            log.error("【系统异常】AbstractSpuApproveManageService.batchApprove error ,errorMessage={}", errorMessage);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s 发品审批通过异常,spuIds: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                , spuIdList.stream().map(String::valueOf).collect(Collectors.joining(","))
                , e.getMessage()));
            throw new BizException(errorMessage, e);
        }
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<String> batchReject(SpuAuditRecordUpdateVO spuAuditRecordReqVO) {
        // 审核人
        final String auditErp = LoginContextHolder.getLoginContextHolder().getPin();
        final String rejectReason = spuAuditRecordReqVO.getRejectReason();
        List<Long> spuIdList = spuAuditRecordReqVO.getSpuIds();
        try {
            // 前置校验
            spuValidateService.validateReject(spuAuditRecordReqVO);
            List<Long> successSpuIds = Lists.newArrayList();
            String message = null;
            for (Long spuId : spuIdList) {
                try {
                    this.singleReject(spuId, auditErp, rejectReason);
                    successSpuIds.add(spuId);
                } catch (Exception e) {
                    message = String.format("%s：%s%s", spuId, e.getMessage(),"\n");
                }
            }

            // 驳回成功的商品发送消息
            if (CollectionUtils.isNotEmpty(successSpuIds)) {
                spuAuditRecordReqVO.setSpuIds(successSpuIds);
                spuAuditRecordReqVO.setAuditErp(auditErp);
                CompletableFuture.runAsync(() -> sendRejectMail(spuAuditRecordReqVO));
            }

            return StringUtils.isNotBlank(message) ? DataResponse.error(message) : DataResponse.success(DataResponseCode.SUCCESS.getMessage());
        } catch (BizException bizException) {
            log.error("AbstractSpuApproveManageService.batchReject warning spuAuditRecordReqVO={},message={}", JSON.toJSONString(spuAuditRecordReqVO), bizException.getMessage());
            throw bizException;
        } catch (Exception e) {
            log.error("【系统异常】SpuWriteManageServiceImpl.batchReject error 驳回操作异常,auditErp={},spuAuditRecordReqVO={}", auditErp, JSON.toJSONString(spuAuditRecordReqVO), e);
            String errorMessage = dataResponseMessageService.getErrorMessage(DataResponseCodeConstant.SPU_REJECT_ERROR);
            log.error("【系统异常】AbstractSpuApproveManageService.batchReject error ,errorMessage={}", errorMessage);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s 发品审批驳回异常,spuIds: %s, 异常信息:%s"
                ,systemProfile
                    , LevelCode.P1.getMessage()
                , spuIdList.stream().map(String::valueOf).collect(Collectors.joining(","))
                , e.getMessage()));
            throw new BizException(errorMessage, e);
        }
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<String> batchAmend(List<SpuAmendReqVO> spuAmendReqVOList) {
        final String auditErp = LoginContextHolder.getLoginContextHolder().getPin();
        try {
            // 校验PUS是否在维护
            // 通过传递过来的列表，做supId集合
            List<Long> allSpuList = Lists.newArrayList();
            Map<Long,SpuAmendReqVO> spuAmendMap = Maps.newHashMap();
            for (SpuAmendReqVO spuAmendReqVO : spuAmendReqVOList) {
                allSpuList.add(spuAmendReqVO.getSpuId());
                if(StringUtils.isEmpty(spuAmendReqVO.getReason())){
                    String amendStr = arrayToString(spuAmendReqVO.getAmendArray());
                    spuAmendReqVO.setReason(amendStr+"字段错误或缺失;");
                }
                spuAmendMap.put(spuAmendReqVO.getSpuId(),spuAmendReqVO);
            }
            List<Long> amendingList = this.filterAmendingList(allSpuList);
            // 状态都是修改状态，直接校验返回
            if(amendingList.size() == spuAmendReqVOList.size()){
                return DataResponse.success(DataResponseCode.FAILURE.getMessage());
            }
            // 通过集合减法，获取到需要修改的集合列表id
            List<Long> normalList = new ArrayList<>(CollectionUtils.subtract(allSpuList, amendingList));
            // 商品主数据 查扩展属性 SpoPO auditStatus 需维护、VC审核中状态，就不发消息
            boolean result = this.sendMessaage(spuAmendMap, normalList);
            // 删除修改状态中的数据
            for (Long l:amendingList){
                spuAmendMap.remove(l);
            }
            // 存储到数据库
            this.saveAmendInfo(spuAmendMap);
            this.updateSpuAuditStatus(spuAmendMap, SpuAuditStatusEnum.WAITING_VENDOR_MAINTAIN);
            this.batchAddAuditRecord(spuAmendMap,auditErp,SpuAuditStatusEnum.WAITING_VENDOR_MAINTAIN,AuditLevelEnum.ZERO.getLevel());
            return DataResponse.success(DataResponseCode.SUCCESS.getMessage());
        } catch (BizException bizException) {
            log.error("AbstractSpuApproveManageService.batchAmend warning spuAmendReqVOList={},message={}", JSON.toJSONString(spuAmendReqVOList), bizException.getMessage());
            throw bizException;
        } catch (Exception e) {
            log.error("【系统异常】SpuWriteManageServiceImpl.batchAmend error 请求修改操作异常,auditErp={},spuAmendReqVOList={}", auditErp, JSON.toJSONString(spuAmendReqVOList), e);
            String errorMessage = dataResponseMessageService.getErrorMessage(DataResponseCodeConstant.SPU_REJECT_ERROR);
            log.error("【系统异常】AbstractSpuApproveManageService.batchAmend error ,errorMessage={}", errorMessage);
            throw new BizException(errorMessage, e);
        }
    }

    /**
     * 驳回给供应商发邮件
     */
    public void sendRejectMail(SpuAuditRecordUpdateVO spuAuditRecordReqVO){
        try {
            log.info("AbstractSpuApproveManageService.sendRejectMail req:{}", JSON.toJSONString(spuAuditRecordReqVO));
            // 如果审核通过直接返回
            if(spuAuditRecordReqVO.getStatus() != null && !Integer.valueOf(AuditStatusEnum.REJECTED.getCode()).equals(spuAuditRecordReqVO.getStatus())){
                return;
            }
            List<SpuDraftPO> spuList = spuDraftAtomicService.batchQuerySpuDraftPoList(new HashSet<>(spuAuditRecordReqVO.getSpuIds()));
            if(CollectionUtils.isNotEmpty(spuList)){
                // 通过简码创建 key-supIdList信息
                Map<String, List<SpuDraftPO>> vendorSpusMap = spuList.stream().collect(Collectors.groupingBy(SpuDraftPO::getVendorCode));
                // 通过供应商简码+国家码 查询账户信息 key- accountList
                Set<String> vendorSet = vendorSpusMap.keySet();
                //List<SupplierContactInfoPO> supplierContactInfoPOList =  supplierContactInfoAtomicService.queryAccountBySupplierCodes(vendorSet);
                //Map<String, List<SupplierContactInfoPO>> supplierAccountMap = supplierContactInfoPOList.stream().collect(Collectors.groupingBy(SupplierContactInfoPO::getSupplierCode));

                List<SupplierAccountVO> supplierAccountList = supplierAccountManageService.queryAccountBySupplierCodes(vendorSet);
                Map<String, List<SupplierAccountVO>> supplierAccountMap = supplierAccountList.stream().collect(Collectors.groupingBy(SupplierAccountVO::getSupplierCode));

                sendMail(vendorSpusMap,supplierAccountMap,spuAuditRecordReqVO);
            }
        }catch (Exception e){
            log.error("【系统异常】AbstractSpuApproveManageService.sendRejectMail error ,errorMessage={}", e.getMessage(),e);
        }
    }


    private void sendMail(Map<String, List<SpuDraftPO>> vendorSpusMap,Map<String, List<SupplierAccountVO>> supplierAccountMap,SpuAuditRecordUpdateVO spuAuditRecordReqVO){
        log.info("AbstractSpuApproveManageService.sendMail vendorSpusMap:{},vendorAccountMap:{},spuAuditRecordReqVO:{}"
            , JSON.toJSONString(vendorSpusMap),JSON.toJSONString(supplierAccountMap),JSON.toJSONString(spuAuditRecordReqVO));
        for (Map.Entry<String, List<SupplierAccountVO>> entry : supplierAccountMap.entrySet()) {
            String vendorCode = entry.getKey();
            List<SupplierAccountVO> accountVOList = entry.getValue();
            List<String> emailList = accountVOList.stream().map(SupplierAccountVO::getAccountEmail).collect(Collectors.toList());

            List<SpuDraftPO> spuPOS = vendorSpusMap.get(vendorCode);
            List<String> spuIdList = spuPOS.stream().map(spuPO -> String.valueOf(spuPO.getSpuId())).collect(Collectors.toList());
            String spuIdStr = String.join(",", spuIdList);

            MailMessageContextVO messageContextVO = new MailMessageContextVO();
            if(CollectionUtils.isNotEmpty(emailList)) {
                messageContextVO.setEmail(emailList.get(0));
                emailList.remove(0);
                if(CollectionUtils.isNotEmpty(emailList)){
                    String emailTo =  String.join(",", emailList);
                    messageContextVO.setCustomMailCopyTo(emailTo);
                }
                messageContextVO.setAccount(spuAuditRecordReqVO.getAuditErp());
                Map<String,Object> contextMap = new HashMap<>();
                contextMap.put("spuId",spuIdStr);
                contextMap.put("reason",spuAuditRecordReqVO.getRejectReason());
                messageContextVO.setContextMap(contextMap);
                String code = SpuApproveCountryTypeEnums.countryEnumByCode(spuAuditRecordReqVO.getCountryType()).getCountry();
                messageContextVO.setTemplateEnum(SendMailTemplateEnum.getMailTemplateEnumByCode(code));
                log.info("AbstractSpuApproveManageService.sendMail info:{}",JSON.toJSONString(messageContextVO));
                mailService.sendMailWithType(messageContextVO);
            }
        }
    }


    protected void fillSkuVo(SpuVO spuVO, List<SkuVO> skuVOList) {
        for (SkuVO skuVO : skuVOList) {
            if (StringUtils.isNotBlank(spuVO.getDetailImg())) {
                skuVO.setDetailImg(spuVO.getDetailImg());
            }
            if (CollectionUtils.isEmpty(skuVO.getDetailImgList()) && CollectionUtils.isNotEmpty(spuVO.getDetailImgList())) {
                skuVO.setDetailImgList(spuVO.getDetailImgList());
            }
            if (Objects.isNull(skuVO.getSpuId())) {
                skuVO.setSpuId(spuVO.getSpuId());
            }
            skuVO.setCatId(spuVO.getCatId());
        }
    }

    /**
     * 单个商品驳回
     * @param spuId        spuId
     * @param erp          erp
     * @param rejectReason rejectReason
     */
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public void singleReject(Long spuId, String erp, String rejectReason) {
        String requestId = UUID.randomUUID().toString();
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.SPU_AUDIT_OPERATE_LOCK_PRE, spuId.toString());
        try {
            AssertValidation.isTrue(!jimUtils.simpleLock(lockKey, requestId, 10), SPU_AUDIT_LOCK_FAILED, "加锁失败");
            // 查询草稿
            SpuDraftPO draftPo = spuDraftAtomicService.getOneBySpuId(spuId);
            if (!CountryConstant.COUNTRY_ZH.equals(draftPo.getSourceCountryCode())) {
                Integer erpLevel = auditRelationAtomicService.getLevelByErp(erp, draftPo.getSourceCountryCode());
                if (draftPo.getLevel() < auditRelationAtomicService.getMaxAuditLevel(draftPo.getSourceCountryCode())){
                    int level = draftPo.getLevel() + 1;
                    AssertValidation.isTrue(erpLevel < level, String.format("ERP %s权限不足，需要 %s 以上层级审批", erp, AuditLevelEnum.descByLevel(level)));
                }
            }

            // 修改草稿为驳回状态
            draftPo.setAuditStatus(SpuAuditStatusEnum.REJECTED.getCode());
            draftPo.setLevel(AuditLevelEnum.ZERO.getLevel());
            draftPo.setUpdateTime(new Date());
            draftPo.setSpuId(null);
            spuDraftAtomicService.updateDraft(draftPo);

            // 更新商品审核状态为驳回
            spuAtomicService.updateSpuAuditStatus(spuId, SpuAuditStatusEnum.REJECTED);
            // 采销审批驳回，税务撤回
            spuTaxManageService(draftPo.getSourceCountryCode()).revoke(spuId);
            // 添加驳回记录
            this.addAuditRecord(spuId, erp, AuditStatusEnum.REJECTED, rejectReason, AuditLevelEnum.ZERO.getLevel());
        }  catch (BizException bizException) {
            log.error("AbstractSpuApproveManageService.singleReject biz error spuId={}", JSON.toJSONString(spuId), bizException);
            throw bizException;
        } catch (Exception e) {
            log.error("【系统异常】AbstractSpuApproveManageService.singleReject error spuId={},erp={},rejectReason={}", spuId, erp, rejectReason, e);
            String errorMessage = dataResponseMessageService.getErrorMessage(DataResponseCodeConstant.SPU_REJECT_ERROR);
            log.error("【系统异常】AbstractSpuApproveManageService.singleReject error spuId={},message={}", spuId, errorMessage);
            throw new BizException(errorMessage, e);
        } finally {
            jimUtils.simpleLockRelease(lockKey, requestId);
        }
    }


    /**
     * 更新SPU信息
     * @param saveSpuVO 包含SPU信息的VO对象
     */
    protected void updateSpu(SaveSpuVO saveSpuVO) {
        // SPU数据转换
        SpuPO spuPo = SpuConvert.INSTANCE.dto2po(saveSpuVO.getSpuVO());
        saveSpuVO.getSpuVO().setDetailImg(spuPo.getDetailImg());
        spuPo.setAttributeScope(spuConvertService.convertAttributeScope(saveSpuVO.getSpuVO().getAttributeScope()));
        spuPo.setGroupExtAttribute(spuConvertService.getGroupExtAttribute(saveSpuVO.getStoreExtendPropertyList()));
//        spuPo.setSaleAttribute(spuConvertService.getSaleAttribute(saveSpuVO.getSkuVOList())); 去除旧版销售属性赋值  ZHAOYAN_SALE_ATTR
        Date now = new Date();
        spuPo.setUpdateTime(now);
        saveSpuVO.getSpuVO().setUpdateTime(now);
        // 调用spu保存方法
        spuAtomicService.updateById(spuPo);
        log.info("SpuWriteManageServiceImpl.updateSpu  保存SPU信息完成,spuId={}", spuPo.getSpuId());
        // 保存商品名多语言
        spuLangAtomicService.saveOrUpdateBatch(spuConvertService.convertVoToLangPo(saveSpuVO.getSpuVO()));
        log.info("SpuWriteManageServiceImpl.updateSpu  保存SPU多语言信息完成,spouId={}", spuPo.getSpuId());
    }

    /**
     * 对商品草稿进行审核分级审批。-本土品用
     * @param draftVo 草稿信息对象，包含商品的基本信息和审核等级。
     * @param erp 当前ERP系统的标识。
     * @return 是否返回 true返回，false继续
     */
    protected Boolean auditLevel(SpuDraftVO draftVo,String erp,int level,int maxLevel){
        // 校验采销权限
        AssertValidation.isTrue(!auditRelationAtomicService.checkErpLevel(level, erp, draftVo.getSourceCountryCode()), String.format("当前ERP %s不是当前审批层级%s", erp, AuditLevelEnum.descByLevel(level)));

        if (level < maxLevel) {
            // 更新草稿等级信息
            this.updateAuditLevel(draftVo);
            // 添加审核记录
            this.addAuditRecord(draftVo.getSpuId(), erp, AuditStatusEnum.APPROVED, AuditLevelEnum.descByLevel(level) + "," + APPROVED_TEXT, level);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 将指定SPU的状态更新为上架，并将审核状态更新为已通过。
     * @param spuId SPU的ID
     */
    protected void updateSpuToSaleStatus(Long spuId) {
        SpuPO spuPo = spuAtomicService.getSpuPoBySpuId(spuId);
        if (SpuStatusEnum.ON_SALE.getStatus() != spuPo.getSpuStatus()) {
            spuAtomicService.updateSpuStatus(spuId, SpuStatusEnum.ON_SALE);
        }
        if (!Objects.equals(SpuAuditStatusEnum.APPROVED.getCode(), spuPo.getAuditStatus())){
            spuAtomicService.updateSpuAuditStatus(spuId, SpuAuditStatusEnum.APPROVED);
        }
    }

    /**
     * 添加商品审核记录
     * @param spuId 商品ID
     * @param erp ERP系统名称
     * @param auditStatusEnum 审核状态枚举
     * @param remark 审核备注
     * @param level 审核等级
     */
    protected void addAuditRecord(Long spuId, String erp, AuditStatusEnum auditStatusEnum, String remark, Integer level) {
        SpuAuditRecordVO recordVO = new SpuAuditRecordVO();
        recordVO.setAuditStatus(auditStatusEnum.getCode());
        recordVO.setSpuId(spuId);
        recordVO.setRemark(remark);
        recordVO.setAuditErp(erp);
        recordVO.setLevel(level);
        spuAuditRecordManageService.add(recordVO);
    }

    /**
     * 初始化价格
     * @param skuVoList         sku列表
     * @param sourceCountryCode 发货国
     */
    protected void initSkuPrice(List<SkuVO> skuVoList, String sourceCountryCode) {
        // 将sku分每批20个
        List<List<SkuVO>> partition = Lists.partition(skuVoList, Constant.PARTITION_SIZE);
        for (List<SkuVO> subList : partition) {
            List<SkuPriceVO> subPriceVoList = skuConvertService.convertPartitionSkuPriceVoFromSkuVo(sourceCountryCode, subList);
            // 分批初始化价格
            SkuPriceReqVO priceReqVo = new SkuPriceReqVO();
            priceReqVo.setSkuPriceVO(subPriceVoList);

            // 非跨境品不支持修改商品价格，发品审核商品时支持设置价格，修改商品时不支持价格修改 2025-04-17 sunlei61
            if (!CountryConstant.COUNTRY_ZH.equals(sourceCountryCode)) {
                Map<Long, SkuPriceResVO> map = skuPriceManageService.list(priceReqVo);
                if (MapUtils.isNotEmpty(map)) {
                    List<SkuPriceVO> filterSkuPriceVoList = subPriceVoList.stream().filter(vo -> !map.containsKey(vo.getSkuId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(filterSkuPriceVoList)) {
                        return;
                    }
                    priceReqVo.setSkuPriceVO(filterSkuPriceVoList);
                }
            }
            Boolean savePrice = skuPriceManageService.saveOrUpdate(priceReqVo);
            if (Objects.equals(savePrice, Boolean.FALSE)) {
                log.error("SpuWriteManageServiceImpl.initSkuPrice sku初始化价格失败,priceReqVo={}", JSON.toJSONString(priceReqVo));
            }
            log.info("SpuWriteManageServiceImpl.initSkuPrice 初始化sku价格成功,priceReqVo={}", JSON.toJSONString(priceReqVo));
        }
    }

    protected void saveOrUpdateSpuDescLang(SaveSpuVO saveSpuVO) {
        List<SpuDescLangPO> spuDescLangPoList = spuConvertService.toDescList(saveSpuVO);
        if (CollectionUtils.isNotEmpty(spuDescLangPoList)) {
            spuDescLangAtomicService.saveOrUpdateBatch(spuDescLangPoList);
            log.info("保存商品详描完成,spuId={}", saveSpuVO.getSpuVO().getSpuId());
        }
    }

    protected void saveOrUpdateSpuCertificate(SaveSpuVO saveSpuVO) {
        List<SpuCertificatePO> spuCertificatePoList = this.getSpuCertificatePoList(saveSpuVO);
        if (CollectionUtils.isNotEmpty(spuCertificatePoList)) {
            spuCertificateAtomicService.saveOrUpdateBatch(spuCertificatePoList);
        }
        log.info("保存批量商品资质完成,spuId={}", saveSpuVO.getSpuVO().getSpuId());
    }

    protected List<SpuCertificatePO> getSpuCertificatePoList(SaveSpuVO saveSpuVO) {
        final Long spuId = saveSpuVO.getSpuVO().getSpuId();
        if (CollectionUtils.isEmpty(saveSpuVO.getSpuCertificateVOList())) {
            log.info("AbstractSpuWriteManageService.getSpuCertificatePoList spuCertificateVOList is null");
            return Collections.emptyList();
        }
        List<SpuCertificatePO> spuCertificatePoList = SpuConvert.INSTANCE.listCertificateVo2po(spuId, saveSpuVO.getSpuCertificateVOList());
        // 过滤未上传资质文件的资质数据
        spuCertificatePoList = spuCertificatePoList.stream().filter(c -> StringUtils.isNotBlank(c.getCertificatePath())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(spuCertificatePoList)) {
            log.info("AbstractSpuWriteManageService.getSpuCertificatePoList spuCertificatePoList is null");
            return Collections.emptyList();
        }

        // 查询已经存在商品资质
        List<SpuCertificatePO> dbCertificateList = spuCertificateAtomicService.listCertificateBySpuId(spuId);
        if (CollectionUtils.isEmpty(dbCertificateList)) {
            log.info("AbstractSpuWriteManageService.getSpuCertificatePoList dbCertificateList is null");
            this.removeSpuId(spuCertificatePoList);
            return spuCertificatePoList;
        }
        log.info("AbstractSpuWriteManageService.getSpuCertificatePoList  商品资质列表 spuCertificatePoList={}", JSON.toJSONString(spuCertificatePoList));
        Map<Long, SpuCertificatePO> certificatePoMap = dbCertificateList.stream().filter(Objects::nonNull).collect(Collectors.toMap(SpuCertificatePO::getId, Function.identity()));
        // 过滤相同资质列表
        List<SpuCertificatePO> removeList = spuCertificatePoList.stream().filter(c -> certificateEquals(c, certificatePoMap)).collect(Collectors.toList());
        if (spuCertificatePoList.size() == removeList.size()) {
            log.info("AbstractSpuWriteManageService.getSpuCertificatePoList size equal");
            return Collections.emptyList();
        }
        spuCertificatePoList.removeAll(removeList);
        this.removeSpuId(spuCertificatePoList);
        return spuCertificatePoList;
    }

    protected void removeSpuId(List<SpuCertificatePO> spuCertificatePoList) {
        // 更新时不能更新spuId
        spuCertificatePoList.forEach(c -> {
            if (Objects.nonNull(c.getId()) && Objects.nonNull(c.getSpuId())) {
                c.setSpuId(null);
            }
        });
    }

    protected boolean certificateEquals(SpuCertificatePO viewPo, Map<Long, SpuCertificatePO> certificatePoMap) {
        // 资质对像比对
        return Objects.nonNull(viewPo.getId()) && certificatePoMap.containsKey(viewPo.getId()) && spuCompareService.compareSpuCertificate(viewPo, certificatePoMap.get(viewPo.getId()));
    }

    abstract public void singleApproved(Long spuId,String scene);

    abstract void recordVendor(List<SkuVO> skuVOList);

    abstract void createSpu(SaveSpuVO saveSpuVO);

    abstract void validateSkuList(List<SkuVO> skuVOList,SpuVO spuVO);

    /**
     * 更新草稿审核等级
     *
     * @param draftVo 草稿信息
     */
    abstract void updateAuditLevel(SpuDraftVO draftVo);


    abstract  void handleSkuList(List<SkuVO> skuVoList);
    /**
     * 数组转换成字符串
     * @param array
     * @return
     */
    private String arrayToString(String[] array){
        if(array == null || array.length ==0){
            return "";
        }
        StringBuffer sb = new StringBuffer();
        for (String code : array){
            sb.append(AmendEnum.getDescByCode(code));
            sb.append("、");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    /**
     * 通过全部supList过滤出修改中的supList
     * @param allSpuList
     * @return
     */
    protected List<Long> filterAmendingList(List<Long> allSpuList){
        // 通过spuId集合查询 供应商简码、状态信息
        List<SpuPO> spuPOs = spuAtomicService.getSpuCountryCodeByIds(allSpuList);
        // 过滤状态为修改状态的,需供应商维护,供应商提交审核
        List<Long> amendingList = spuPOs.stream().filter(spuPO -> SpuAuditStatusEnum.WAITING_VENDOR_MAINTAIN.getCode().equals(spuPO.getAuditStatus())
                || SpuAuditStatusEnum.WAITING_APPROVED_VENDOR_SUBMIT.getCode().equals(spuPO.getAuditStatus())).map(SpuPO::getSpuId).collect(Collectors.toList());
        return amendingList;
    }

    /**
     * 拼接消息，发送消息
     * @param spuAmendMap
     * @param normalList
     * @return
     */
    protected boolean sendMessaage(Map<Long,SpuAmendReqVO> spuAmendMap,List<Long> normalList){
        // jdVendorCode -> spuId集合 -> 消息并集
        Map<String, Set<Long>> jdVendorCodeSpuIdSetMap = skuAtomicService.queryJdVendorCodeAndSpuIds(normalList);
        JdmSupplierMessageReqDTO reqDTO = new JdmSupplierMessageReqDTO();
        List<JdmSupplierMessageReqDTO.JdmSupplierMessageDTO> messageDTOList = Lists.newArrayList();
        jdVendorCodeSpuIdSetMap.forEach((jdVendorCode,set) -> messageDTOList.add(new JdmSupplierMessageReqDTO.JdmSupplierMessageDTO(jdVendorCode, amendSpuMessage,rejectedMessageUrl)));
        reqDTO.setJdmSupplierMessageDTOList(messageDTOList);
        DataResponse<String> result = jdmMessageService.pushMessageToJm(reqDTO);
        return result.getSuccess();
    }

    /**
     * 保存到数据库修改信息
     * @param map
     */
    protected void saveAmendInfo(Map<Long,SpuAmendReqVO> map){
        // 保存到数据库修改信息
        List<SpuAmendPO> spuAmendPOList = Lists.newArrayList();
        for(SpuAmendReqVO spuAmendReqVO : map.values()){
            SpuAmendPO spuAmendPO = new SpuAmendPO();
            spuAmendPO.setSpuId(spuAmendReqVO.getSpuId());
            spuAmendPO.setAmendArray(arrayToString(spuAmendReqVO.getAmendArray()));
            spuAmendPO.setReason(spuAmendReqVO.getReason());
            spuAmendPO.setYn(YnEnum.YES.getCode());
            spuAmendPO.setType(0);
            spuAmendPOList.add(spuAmendPO);
        }
        log.info("AbstractSpuApproveManageService.saveAmendInfo 申请修改SKU的信息,saveSpuAmendPO={}",JSON.toJSONString(spuAmendPOList));
        spuAmendService.saveBatch(spuAmendPOList);
    }

    /**
     * 更新spu的审核权限
     * @param map
     */
    protected void updateSpuAuditStatus(Map<Long,SpuAmendReqVO> map, SpuAuditStatusEnum spuAuditStatusEnum){
        List<SpuPO> updateSpuPoList = Lists.newArrayListWithCapacity(map.size());
        for(SpuAmendReqVO spuAmendReqVO : map.values()) {
            SpuPO spuPO = new SpuPO();
            spuPO.setSpuId(spuAmendReqVO.getSpuId());
            spuPO.setAuditStatus(spuAuditStatusEnum.getCode());
            spuPO.setUpdateTime(new Date());
            updateSpuPoList.add(spuPO);
        }
        // 批量更新商品审核状态
        spuAtomicService.saveOrUpdateBatch(updateSpuPoList);
        // 批量更新商品草稿审核状态
        List<SpuDraftPO> spuDraftPOList = spuDraftAtomicService.batchQuerySpuDraftPoList(map.keySet());
        spuDraftPOList.forEach(draft -> {draft.setSpuId(null); draft.setUpdateTime(new Date()); draft.setAuditStatus(spuAuditStatusEnum.getCode());});
        spuDraftAtomicService.saveOrUpdateBatch(spuDraftPOList);
    }

    /**
     * 批量添加审核记录
     * @param spuAmendMap 存储SpuAmendReqVO对象的Map
     * @param erp ERP系统名称
     * @param auditStatusEnum 审核状态枚举
     * @param level 审核级别
     * @throws NullPointerException 如果spuAmendMap为空
     */
    private void batchAddAuditRecord(Map<Long,SpuAmendReqVO> spuAmendMap,String erp,SpuAuditStatusEnum auditStatusEnum,Integer level){
        if (MapUtils.isNotEmpty(spuAmendMap)){
            List<SpuAuditRecordVO> auditRecordVOList = Lists.newArrayList();
            spuAmendMap.forEach((k,v) -> {
                SpuAuditRecordVO recordVO = new SpuAuditRecordVO();
                recordVO.setLevel(level);
                recordVO.setSpuId(k);
                recordVO.setAuditErp(erp);
                recordVO.setRemark(v.getReason());
                recordVO.setAuditStatus(auditStatusEnum.getCode());
                auditRecordVOList.add(recordVO);
            });
            spuAuditRecordManageService.batchAdd(auditRecordVOList);

        }
    }

    /**
     * 初始化SPU跨境属性。
     * @param saveSpuVO 保存SPU的VO对象，包含SPU的ID和SPU的跨境属性列表。
     */
    protected void initSpuGlobalAttribute(SaveSpuVO saveSpuVO){
        log.info("AbstractSpuApproveManageService.initSpuGlobalAttribute start");
        final Long spuId = saveSpuVO.getSpuVO().getSpuId();
        if (CollectionUtils.isEmpty(saveSpuVO.getSpuInterPropertyList())) {
            return;
        }

        List<ProductGlobalAttributePO> attributePOS = productAttributeConvertService.convertSpuAttribute(spuId,saveSpuVO.getSpuInterPropertyList());

        // 更新时、如果存在就删除
        List<ProductGlobalAttributePO> globalAttributePOS = productGlobalAttributeAtomicService.getBySpuId(spuId);
        if(CollectionUtils.isNotEmpty(globalAttributePOS)){
            globalAttributePOS.forEach(item->{
                item.setKeyId(null);
                item.setUpdateTime(new Date().getTime());
                item.setYn(YnEnum.NO.getCode());
            });
            productGlobalAttributeAtomicService.saveOrUpdateBatch(globalAttributePOS);
        }

        productGlobalAttributeAtomicService.saveBatch(attributePOS);
    }

    protected SpuTaxManageService spuTaxManageService(String sourceCountryCode) {
        return spuTaxManageServiceMap.get(SpuTaxCountryEnums.getEnumByCountryCode(sourceCountryCode).getServiceName());
    }

    /**
     * 发送SKU维度的消息
     * @param saveSpuVO
     */
    protected void sendSkuMsg(SaveSpuVO saveSpuVO) {
        productMessageService.sendMessage(saveSpuVO);
    }
}
