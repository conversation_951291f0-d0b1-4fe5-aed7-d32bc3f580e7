package com.jdi.isc.product.soa.service.manage.specialAttr.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.enums.SkuSpecialAttrEnum;
import com.jdi.isc.product.soa.api.common.enums.specialAttrEnum.SkuSpecialAttrValueEnum;
import com.jdi.isc.product.soa.api.specialAttr.biz.SkuSpecialAttrMergeReqDTO;
import com.jdi.isc.product.soa.api.specialAttr.biz.SpecialAttrExcelReq;
import com.jdi.isc.product.soa.api.specialAttr.res.SpecialAttrRelationDTO;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.msgCode.ProductMessageCodeConstant;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.mkuTag.biz.MkuTagVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuLogPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.specialAttr.biz.SpecialAttrSkuReqVO;
import com.jdi.isc.product.soa.domain.specialAttr.po.SpecialAttrPO;
import com.jdi.isc.product.soa.domain.specialAttr.po.SpecialAttrRelationPO;
import com.jdi.isc.product.soa.domain.specialAttr.po.SpecialAttrValuePO;
import com.jdi.isc.product.soa.rpc.suport.AlertHelper;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.specialAttr.SpecialAttrAtomicService;
import com.jdi.isc.product.soa.service.atomic.specialAttr.SpecialAttrRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.specialAttr.SpecialAttrValueAtomicService;
import com.jdi.isc.product.soa.service.manage.mkuTag.MkuTagManageService;
import com.jdi.isc.product.soa.service.manage.specialAttr.SpecialAttrManageService;
import com.jdi.isc.product.soa.service.mapstruct.specialAttr.SpecialAttrRelationConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description：特殊属性Manage层接口实现类
 * @Date 2024-12-25
 */
@Slf4j
@Service
public class SpecialAttrManageServiceImpl implements SpecialAttrManageService {
    @Resource
    private SpecialAttrAtomicService specialAttrAtomicService;
    @Resource
    private SpecialAttrValueAtomicService specialAttrValueAtomicService;

    @Resource
    private SpecialAttrRelationAtomicService specialAttrRelationAtomicService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;


    @Resource
    private SkuLogAtomicService skuLogAtomicService;
    @Resource
    private MkuTagManageService mkuTagManageService;

    @Resource
    private JimUtils jimUtils;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, Map<String, String>>> querySpecialAttrMapBySkuIds(SpecialAttrSkuReqVO input) {
        List<Long> skuIds = input.getSkuIds();

        Map<String, Long> attrKeyAndIdMap = specialAttrAtomicService.getAttrKeyAndIdMap();
        // 查询指定特殊属性的ID集合
        Set<Long> specialAttrIds = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(input.getSkuSpecialAttrEnumSet())) {
            input.getSkuSpecialAttrEnumSet().forEach((skuSpecialAttrEnum ->  specialAttrIds.add(attrKeyAndIdMap.get(skuSpecialAttrEnum.getKey()))));
        }

        // 查询SKU的所有特殊属性
        Map<Long, List<SpecialAttrRelationPO>> skuSpecialAttrRelationPoListMap = specialAttrRelationAtomicService.getAttrKeyAndValueMapBySkuIds(Sets.newHashSet(skuIds), specialAttrIds);

        if (MapUtils.isEmpty(skuSpecialAttrRelationPoListMap)) {
            return DataResponse.success();
        }

        // 特殊属性主键ID和KEY的关系
        Map<Long, String> attrIdAndKeyMap = MapUtils.invertMap(attrKeyAndIdMap);

        // 查询特殊属性值ID和值的映射
        Set<Long> specialValueIds = Sets.newHashSet();
        skuSpecialAttrRelationPoListMap.forEach((skuId, specialAttrRelationPoList) ->
                specialValueIds.addAll(specialAttrRelationPoList.stream().map(SpecialAttrRelationPO::getAttributeValueId).collect(Collectors.toSet())));
        Map<Long, String> attrValueMap = specialAttrValueAtomicService.queryAttrValueMapByIds(specialValueIds);

        Map<Long, Map<String, String>> resultMap = Maps.newHashMapWithExpectedSize(skuIds.size());
        skuIds.forEach(skuId -> {
            Map<String, String> specialMap = Maps.newHashMap();
            // 转换为特殊属性标识和属性值
            List<SpecialAttrRelationPO> specialAttrRelationPOList = skuSpecialAttrRelationPoListMap.get(skuId);
            if (CollectionUtils.isNotEmpty(specialAttrRelationPOList)) {
                specialAttrRelationPOList.forEach(specialAttrRelationPO ->
                        // KEY：特殊属性标识和ID的映射得出标识 Value：特殊属性值ID和值的映射得出值
                        specialMap.put(attrIdAndKeyMap.get(specialAttrRelationPO.getAttributeKeyId()), attrValueMap.get(specialAttrRelationPO.getAttributeValueId())));
                log.info("querySpecialAttrMapBySkuIds skuId={},specialMap={}",skuId, JSON.toJSONString(specialMap));
            }
            resultMap.put(skuId, specialMap);
        });

        return DataResponse.success(resultMap);
    }

    @Override
    public DataResponse<String> saveOrUpdate(SpecialAttrExcelReq input) {
        //sku 是否有效
        SkuPO skuPO = skuAtomicService.getSkuPoBySkuId(Long.valueOf(input.getSkuId()));
        if (skuPO == null) {
            return DataResponse.error("SKUID已失效");
        }

        if(SkuSpecialAttrEnum.PRE_SELECTED.getKey().equals(input.getAttributeKey())) {
            MkuRelationPO mkuRelationPO = mkuRelationAtomicService.getMkuBySkuId(skuPO.getSkuId());
            if (Objects.nonNull(mkuRelationPO)) {
                List<CustomerMkuPO> customerMkuPOList = customerMkuAtomicService.listNoClientCode(mkuRelationPO.getMkuId());
                if (CollectionUtils.isNotEmpty(customerMkuPOList)) {
                    List<String> clientNameList = customerMkuPOList.stream().map(CustomerMkuPO::getClientName).collect(Collectors.toList());
                    String join = String.join(Constant.COMMA, clientNameList);
                    String str = "此商品已经在%s客户池中，请出池后再操作打标预选品!";
                    String format = String.format(str, join);
                    return DataResponse.error(format);
                }
            }
        }
        //特殊属性是否有效：
        SpecialAttrPO keyPO = specialAttrAtomicService.getSpecialAttrByAttributeKey(input.getAttributeKey());
        if (keyPO == null) {
            return DataResponse.error("特殊属性key已失效");
        }
        //特殊属性归属权
        if (!checkAuthKey(keyPO, input)) {
            return DataResponse.error("该特殊属性不属于您，请查询特殊属性key归属");
        }
        //此属性key下是否有此value
        SpecialAttrValuePO valuePO = null;
        if (StringUtils.isNotBlank(input.getAttributeValue())) {
            valuePO = specialAttrValueAtomicService.getSpecialAttrValueByAttributeKeyIdAndValue(keyPO.getId(), input.getAttributeValue());
            if (null == valuePO) {
                return DataResponse.error("该属性key=" + input.getAttributeKey() + "下不存在此value=" + input.getAttributeValue());
            }
        }
        //该特殊属性是否为空， 空代表全部国家
        if (StringUtils.isNotBlank(keyPO.getCountryScope())) {
            if (!keyPO.getCountryScope().contains(skuPO.getSourceCountryCode())) {
                return DataResponse.error("该SKUID所属国家不存在此属性key=" + input.getAttributeKey());
            }
        }
        String attributeValue = input.getAttributeValue();
        if (StringUtils.isBlank(attributeValue)) {
            //需要取消标 前提 sku + attrKey 唯一
            LambdaQueryWrapper<SpecialAttrRelationPO> wrapper = Wrappers.<SpecialAttrRelationPO>lambdaQuery()
                .eq(SpecialAttrRelationPO::getSkuId, input.getSkuId())
                .eq(SpecialAttrRelationPO::getAttributeKeyId, keyPO.getId());
            SpecialAttrRelationPO sourceData = specialAttrRelationAtomicService.getOne(wrapper);
            if (null != sourceData) {
                specialAttrRelationAtomicService.cancelRelationBySku(Long.valueOf(input.getSkuId()), input.getPin(), keyPO.getId(), System.currentTimeMillis(), YnEnum.NO.getCode());
                SpecialAttrRelationPO targetData = specialAttrRelationAtomicService.getById(sourceData.getId());
                //发送国家池同步数据
                sendCountryMku(input.getSkuId());
                //保存日志信息
                sendLog(input.getSkuId(), sourceData, targetData);
            }
        } else {
            //添加标/修改标 前提 sku + attrKey 唯一
            LambdaQueryWrapper<SpecialAttrRelationPO> wrapper = Wrappers.<SpecialAttrRelationPO>lambdaQuery()
                .eq(SpecialAttrRelationPO::getSkuId, input.getSkuId())
                .eq(SpecialAttrRelationPO::getAttributeKeyId, keyPO.getId());
            SpecialAttrRelationPO sourceData = specialAttrRelationAtomicService.getOne(wrapper);
            if (null == sourceData) {
                //添加
                SpecialAttrRelationPO targetData = new SpecialAttrRelationPO();
                targetData.setSpuId(skuPO.getSpuId());
                targetData.setSkuId(Long.valueOf(input.getSkuId()));
                targetData.setAttributeKeyId(keyPO.getId());
                targetData.setAttributeValueId(null != valuePO ? valuePO.getId() : null);
                targetData.setSourceCountryCode(skuPO.getSourceCountryCode());
                targetData.setCreator(input.getPin());
                targetData.setUpdater(input.getPin());
                targetData.setCreateTime(System.currentTimeMillis());
                targetData.setUpdateTime(System.currentTimeMillis());
                targetData.setYn(YnEnum.YES.getCode());
                specialAttrRelationAtomicService.save(targetData);
                //保存日志信息
                sendLog(input.getSkuId(), new SpecialAttrRelationPO(), targetData);
                //发送国家池同步数据
                sendCountryMku(input.getSkuId());
            } else {
                //重置标
                if (sourceData.getUpdater().equals(input.getPin()) && sourceData.getYn().equals(YnEnum.YES.getCode())&& sourceData.getAttributeValueId().equals(valuePO.getId())) {
                    return DataResponse.error("该特殊属性归属于您,请勿重复操作");
                }
                specialAttrRelationAtomicService.updateRelationBySku(Long.valueOf(input.getSkuId()), input.getPin(), valuePO.getId(),keyPO.getId(), System.currentTimeMillis(), YnEnum.YES.getCode());
                SpecialAttrRelationPO targetData = specialAttrRelationAtomicService.getById(sourceData.getId());
                //发送国家池同步数据
                sendCountryMku(input.getSkuId());
                //发送日志信息
                sendLog(input.getSkuId(), sourceData, targetData);
            }
        }
        return DataResponse.success();
    }

    @Override
    public void unbindBySpuId(Long spuId, String updater) {
        log.info("解绑特殊属性，spuId={}, updater={}", spuId, updater);
        if (spuId == null) {
            return;
        }
        List<SpecialAttrRelationPO> list = specialAttrRelationAtomicService.listBySpuId(spuId);

        List<Long> ids = list.stream().map(SpecialAttrRelationPO::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            log.info("解绑特殊属性yn=0, ids is empty");
            return;
        }

        LambdaUpdateWrapper<SpecialAttrRelationPO> wrapper = Wrappers.<SpecialAttrRelationPO>lambdaUpdate()
                .in(SpecialAttrRelationPO::getId, ids);
        SpecialAttrRelationPO update = new SpecialAttrRelationPO();
        update.setYnAndUpdateInfo(YnEnum.NO.getCode(), updater);

        boolean result = specialAttrRelationAtomicService.update(update, wrapper);

        log.info("解绑特殊属性，spuId={}, updater={}, result={}", spuId, updater, result);

        if (!result) {
            log.error("解绑特殊属性yn=0, 更新失败. id={}, updater={}", ids, updater);
            throw new BizException("解绑特殊属性失败");
        }

    }

    @Override
    public Map<Long, Integer> isTestProducts(Collection<Long> skuIds) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(skuIds), "skuId is null, unknown test product");

        SpecialAttrSkuReqVO query = new SpecialAttrSkuReqVO();
        query.setSkuIds(Lists.newArrayList(skuIds));
        query.setSkuSpecialAttrEnumSet(Sets.newHashSet(SkuSpecialAttrEnum.TEST_PRODUCT));

        DataResponse<Map<Long, Map<String, String>>> mapDataResponse = this.querySpecialAttrMapBySkuIds(query);

        if (!mapDataResponse.getSuccess()) {
            AlertHelper.p0("查询是否测试品失败.", String.valueOf(skuIds));
            throw new ProductBizException("查询是否测试品失败.");
        }

        Map<Long, Map<String, String>> data = mapDataResponse.getData();
        if (data == null) {
            data = Maps.newHashMap();
        }

        Map<Long, Integer> result = Maps.newHashMapWithExpectedSize(skuIds.size());
        for (Long skuId : skuIds) {
            Map<String, String> attrMap = data.get(skuId);

            if (MapUtils.isEmpty(attrMap)) {
                attrMap = Maps.newHashMap();
            }

            String value = attrMap.get(SkuSpecialAttrEnum.TEST_PRODUCT.getKey());

            if (StringUtils.isNotEmpty(value) && StringUtils.equals(value, String.valueOf(YesOrNoEnum.YES.getCode()))) {
                result.put(skuId, YesOrNoEnum.YES.getCode());
            } else {
                result.put(skuId, YesOrNoEnum.NO.getCode());
            }
        }

        return result;
    }

    @Override
    public Integer isTestProduct(Long skuId) {
        Preconditions.checkArgument(skuId != null, "skuId is null, unknown test product");
        Map<Long, Integer> testProducts = this.isTestProducts(Sets.newHashSet(skuId));
        return testProducts.get(skuId);
    }

    private void sendLog(String skuId, SpecialAttrRelationPO sourceData, SpecialAttrRelationPO targetData) {
        try {
            SkuLogPO skuLogPO = new SkuLogPO();
            skuLogPO.setSourceJson(JSONObject.toJSONString(sourceData));
            skuLogPO.setTargetJson(JSONObject.toJSONString(targetData));
            skuLogPO.setKeyType(KeyTypeEnum.SPECIAL_ATTR.getCode());
            skuLogPO.setKeyId(skuId);
            skuLogPO.setCreator(targetData.getUpdater());
            skuLogPO.setUpdater(targetData.getUpdater());
            skuLogAtomicService.save(skuLogPO);
            log.info("日志保存成功，SKU: {}", skuId);
        } catch (Exception e) {
            log.error("存储日志异常，SKU: {},sourceData:{},targetData:{} ,Error: {}", skuId, JSONObject.toJSONString(sourceData), JSONObject.toJSONString(targetData), e.getMessage(), e);
        }
    }


    private void sendCountryMku(String skuId) {
        try {
            MkuTagVO mkuTagVO = new MkuTagVO();
            mkuTagVO.setSkuId(Long.valueOf(skuId));
            DataResponse<Boolean> response = mkuTagManageService.resetBinding(mkuTagVO);
            if (response.getSuccess()) {
                log.info("发送国家池 sendCountryMku ，SKU: {}", skuId);
            } else {
                log.error("发送国家池异常 sendCountryMku ，SKU: {} ,msg :{}", skuId, response.getMessage());
            }
        } catch (Exception e) {
            log.error("发送国家池异常，SKU: {},Error: {}", skuId, e.getMessage(), e);
        }
    }

    private boolean checkAuthKey(SpecialAttrPO specialAttrPO, SpecialAttrExcelReq input) {
        boolean result = false;
        if (StringUtils.isNotBlank(specialAttrPO.getOperableUsers())) {
            if (specialAttrPO.getOperableUsers().contains(input.getPin())) {
                return true;
            }
        }
        if (StringUtils.isNotBlank(specialAttrPO.getOperableDepartments())) {
            if (specialAttrPO.getOperableDepartments().contains(input.getOrgId())) {
                return true;
            }
        }
        return result;
    }


    @Override
    public DataResponse<Boolean> mergeSkuSpecialAttr(SkuSpecialAttrMergeReqDTO input) {

        Map<String, String> specialAttrMap = input.getSpecialAttrMap();
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.SKU_UPDATE_SPECIAL_ATTR_LOCK_PRE, String.valueOf(input.getSkuId()));
        try {
            // 给当前sku加锁，防止并发
            Boolean simpleLock = jimUtils.simpleLock(lockKey, String.valueOf(input.getSkuId()), 2);
            if (!simpleLock) {
                log.warn("mergeSkuSpecialAttrMap SKU ID={} 加锁失败,入参:{}", input.getSkuId(), JSON.toJSONString(input));
                return DataResponse.error(String.format("商品打标加锁失败，SKU ID=%s", input.getSkuId()));
            }

            Set<String> specialEnumKeys = Arrays.stream(SkuSpecialAttrEnum.values()).map(SkuSpecialAttrEnum::getKey).collect(Collectors.toSet());
            Set<String> notExistKey = specialAttrMap.keySet().stream().filter(key -> !specialEnumKeys.contains(key)).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(notExistKey)) {
                log.warn("mergeSkuSpecialAttrMap SKU ID={} 特殊属性KEY {}不存在", input.getSkuId(), JSON.toJSONString(notExistKey));
                return DataResponse.error(ProductMessageCodeConstant.SKU_SPECIAL_ATTR_NOT_EXIST_ERROR, String.format("特殊属性KEY [%s]不存在", String.join(",", notExistKey)));
            }

            // 不支持打预选标
            if (specialAttrMap.containsKey(SkuSpecialAttrEnum.PRE_SELECTED.getKey())) {
                return DataResponse.error("当前接口暂不支持打预选标，请联系研发人员");
            }
            // 校验属性值是否存在
            Set<String> specialValueEnumNames = Arrays.stream(SkuSpecialAttrValueEnum.values()).map(Enum::name).collect(Collectors.toSet());
            Set<String> notExistName = specialAttrMap.values().stream().filter(StringUtils::isNotBlank).filter(key -> !specialValueEnumNames.contains(key)).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(notExistName)) {
                log.warn("mergeSkuSpecialAttrMap SKU ID={} 特殊属性值 {}不存在", input.getSkuId(), JSON.toJSONString(notExistName));
                return DataResponse.error(ProductMessageCodeConstant.SKU_SPECIAL_ATTR_VALUE_NOT_EXIST_ERROR, String.format("特殊属性值 [%s]不存在", String.join(",", notExistName)));
            }

            // 校验属性和属性值的关系
            for (Map.Entry<String, String> entry : specialAttrMap.entrySet()) {
                if (StringUtils.isNotBlank(entry.getValue())) {
                    SkuSpecialAttrEnum enumByKey = SkuSpecialAttrEnum.getEnumByKey(entry.getKey());
                    List<SkuSpecialAttrValueEnum> skuSpecialAttrValueEnumList = SkuSpecialAttrValueEnum.getValueEnumFromAttr(enumByKey);
                    if (CollectionUtils.isEmpty(skuSpecialAttrValueEnumList) || skuSpecialAttrValueEnumList.stream().noneMatch(value -> value.name().equals(entry.getValue()))) {
                        return DataResponse.error(ProductMessageCodeConstant.SKU_SPECIAL_ATTR_VALUE_NOT_EXIST_ERROR, String.format("SKU ID [%s] 特殊属性 [%s] 值 [%s] 不存在", input.getSkuId(), entry.getKey(), entry.getValue()));
                    }
                }
            }

            SkuPO skuPo = skuAtomicService.getSkuPoBySkuId(input.getSkuId());
            if (Objects.isNull(skuPo)) {
                log.warn("mergeSkuSpecialAttrMap SKU ID={} 不存在", input.getSkuId());
                return DataResponse.error(ProductMessageCodeConstant.SKU_NOT_EXIST_ERROR, String.format("商品SKU ID [%s]不存在", input.getSkuId()));
            }

            // 查询特殊属性定义
            Map<String, SpecialAttrPO> attributeKeyMap = specialAttrAtomicService.queryAttributeKeyMap(specialAttrMap.keySet());
            for (String key : specialAttrMap.keySet()) {
                SpecialAttrPO specialAttrPO = attributeKeyMap.get(key);
                // 校验特殊属性归属国家
                if (StringUtils.isNotBlank(specialAttrPO.getCountryScope()) && Arrays.stream(specialAttrPO.getCountryScope().split(Constant.COMMA)).noneMatch(countryCode -> skuPo.getSourceCountryCode().equals(countryCode))) {
                    return DataResponse.error(ProductMessageCodeConstant.SKU_SPECIAL_ATTR_COUNTRY_NOT_EXIST_ERROR, String.format("SKU ID [%s]所属国家 [%s] 不存在特殊属性 [%s]", input.getSkuId(), skuPo.getSourceCountryCode(), key));
                }
                // 校验特殊属性归属用户
                if (StringUtils.isNotBlank(specialAttrPO.getOperableUsers()) && Arrays.stream(specialAttrPO.getOperableUsers().split(Constant.COMMA)).noneMatch(erp -> input.getOperator().equals(erp))) {
                    return DataResponse.error(ProductMessageCodeConstant.SKU_SPECIAL_ATTR_ERP_NOT_EXIST_ERROR, String.format("SKU ID [%s] 特殊属性 [%s] 归属用户 [%s] 不存在", input.getSkuId(), key, input.getOperator()));
                }
            }

            // 查询sku的特殊属性
            List<SpecialAttrRelationPO> relationList = specialAttrRelationAtomicService.getAttrRelationBySkuId(input.getSkuId());

            if (CollectionUtils.isEmpty(relationList)) {
                List<SpecialAttrRelationPO> insertRelationPOList = Lists.newArrayListWithExpectedSize(specialAttrMap.size());
                specialAttrMap.forEach((key, value) -> {
                    if (StringUtils.isNotBlank(value)){
                        SpecialAttrRelationPO relationPO = getSpecialAttrRelationPO(input, attributeKeyMap.get(key).getId(), SkuSpecialAttrValueEnum.getByName(value).getRelationId(), skuPo);
                        insertRelationPOList.add(relationPO);
                    }
                });

                if (CollectionUtils.isNotEmpty(insertRelationPOList)){
                    specialAttrRelationAtomicService.saveBatch(insertRelationPOList);
                    // 记录修改记录
                    for (SpecialAttrRelationPO relationPO : insertRelationPOList) {
                        sendLog(String.valueOf(relationPO.getSkuId()), new SpecialAttrRelationPO(), relationPO);
                    }
                    // 触发国家池同步
                    CompletableFuture.runAsync(() -> sendCountryMku(String.valueOf(input.getSkuId())));
                }
            } else {
                // 特殊属性ID和属性信息关系
                Map<Long, SpecialAttrRelationDTO> needUpdateSpecialAttrRelationMap = getNeedUpdateSpecialAttrRelationMap(attributeKeyMap, specialAttrMap);
                // 更新特殊属性列表
                List<SpecialAttrRelationPO> updateSpecialAttrRelationPOList = Lists.newArrayList();
                // 需要删除标的属性列表
                List<SpecialAttrRelationDTO> removeRelationDTOList = Lists.newArrayList();

                for (SpecialAttrRelationPO relationPO : relationList) {
                    if (Objects.isNull(relationPO.getAttributeValueId())) {
                        removeRelationDTOList.add(SpecialAttrRelationConvert.INSTANCE.po2dto(relationPO));
                        continue;
                    }

                    // 包含需要修改的特殊属性、属性值和更新的属性值不同，更新此条数据
                    if (needUpdateSpecialAttrRelationMap.containsKey(relationPO.getAttributeKeyId())
                            && !relationPO.getAttributeValueId().equals(needUpdateSpecialAttrRelationMap.get(relationPO.getAttributeKeyId()).getAttributeValueId())) {
                        SpecialAttrRelationDTO specialAttrRelationDTO = needUpdateSpecialAttrRelationMap.get(relationPO.getAttributeKeyId());
                        // 特殊属性值ID
                        Long attributeValueId = specialAttrRelationDTO.getAttributeValueId();
                        if (Objects.isNull(attributeValueId)) {
                            specialAttrRelationDTO.setId(relationPO.getId());
                            removeRelationDTOList.add(specialAttrRelationDTO);
                            continue;
                        }
                        SpecialAttrRelationPO updateRelationPO = new SpecialAttrRelationPO();
                        updateRelationPO.setId(relationPO.getId());
                        updateRelationPO.setAttributeKeyId(relationPO.getAttributeKeyId());
                        updateRelationPO.setAttributeValueId(attributeValueId);
                        updateRelationPO.setUpdateTime(System.currentTimeMillis());
                        updateRelationPO.setUpdater(input.getOperator());
                        updateRelationPO.setYn(YnEnum.YES.getCode());
                        updateSpecialAttrRelationPOList.add(updateRelationPO);
                    }
                }

                // 新增特殊属性处理
                Map<Long, SpecialAttrRelationPO> existDbAttrKeyId = relationList.stream().collect(Collectors.toMap(SpecialAttrRelationPO::getAttributeKeyId, Function.identity()));
                needUpdateSpecialAttrRelationMap.forEach((keyId, relationDTO) -> {
                    // 1、DB不存在当前特殊属性keyID，并且属性值ID不为空。属性值ID为空，则不新增这个属性值关系
                    if (!existDbAttrKeyId.containsKey(keyId) && Objects.nonNull(relationDTO.getAttributeValueId())) {
                        SpecialAttrRelationPO relationPO = getSpecialAttrRelationPO(input, keyId, relationDTO.getAttributeValueId(), skuPo);
                        updateSpecialAttrRelationPOList.add(relationPO);
                    }
                });

                // 更新特殊属性列表
                if (CollectionUtils.isNotEmpty(updateSpecialAttrRelationPOList)) {
                    specialAttrRelationAtomicService.saveOrUpdateBatch(updateSpecialAttrRelationPOList);
                    // 记录修改记录
                    for (SpecialAttrRelationPO relationPO : updateSpecialAttrRelationPOList) {
                        sendLog(String.valueOf(relationPO.getSkuId()), existDbAttrKeyId.getOrDefault(relationPO.getAttributeKeyId(), new SpecialAttrRelationPO()), relationPO);
                    }
                    // 触发国家池同步
                    CompletableFuture.runAsync(() -> sendCountryMku(String.valueOf(input.getSkuId())));
                }
                // 删除特殊属性列表
                if (CollectionUtils.isNotEmpty(removeRelationDTOList)) {
                    for (SpecialAttrRelationDTO relationDTO : removeRelationDTOList) {
                        specialAttrRelationAtomicService.cancelRelationBySku(input.getSkuId()
                                ,input.getOperator(),relationDTO.getAttributeKeyId(),System.currentTimeMillis(),YnEnum.NO.getCode());
                        SpecialAttrRelationPO targetData = specialAttrRelationAtomicService.getById(relationDTO.getId());
                        //保存日志信息
                        sendLog(String.valueOf(input.getSkuId()), existDbAttrKeyId.get(relationDTO.getAttributeKeyId()), targetData);
                    }
                    //发送国家池同步数据
                    sendCountryMku(String.valueOf(input.getSkuId()));
                }
            }
        } catch (Exception e) {
            log.error("mergeSkuSpecialAttr 商品打标异常 SKU ID={} input={} 异常", input.getSkuId(),JSON.toJSONString(input), e);
        }finally {
            log.info("mergeSkuSpecialAttr 商品打标结束 SKU ID={} input={}", input.getSkuId(),JSON.toJSONString(input));
            Boolean simpleLock = jimUtils.simpleLockRelease(lockKey, String.valueOf(input.getSkuId()));
            log.info("mergeSkuSpecialAttr  商品打标结束 释放锁 SKU ID={},释放结果:{}",input.getSkuId(),simpleLock);
        }
        return DataResponse.success();
    }

    private SpecialAttrRelationPO getSpecialAttrRelationPO(SkuSpecialAttrMergeReqDTO input, Long keyId, Long valueId, SkuPO skuPo) {
        SpecialAttrRelationPO relationPO = new SpecialAttrRelationPO();
        relationPO.setSpuId(skuPo.getSpuId());
        relationPO.setSkuId(input.getSkuId());
        relationPO.setSourceCountryCode(skuPo.getSourceCountryCode());
        relationPO.setAttributeKeyId(keyId);
        relationPO.setAttributeValueId(valueId);
        relationPO.setCreator(input.getOperator());
        relationPO.setUpdater(input.getOperator());
        long now = System.currentTimeMillis();
        relationPO.setCreateTime(now);
        relationPO.setUpdateTime(now);
        relationPO.setYn(YnEnum.YES.getCode());
        return relationPO;
    }

    /**
     * 获取需要更新的特殊属性和属性值的关系
     * @param attributeKeyMap 特殊属性ID和属性信息关系
     * @param specialAttrMap 更新的特殊属性关系
     * @return 特殊属性ID和属性、属性值的关系
     */
    private Map<Long, SpecialAttrRelationDTO> getNeedUpdateSpecialAttrRelationMap(Map<String, SpecialAttrPO> attributeKeyMap,Map<String, String> specialAttrMap) {
        Map<Long, SpecialAttrRelationDTO> relationMap = Maps.newHashMapWithExpectedSize(attributeKeyMap.size());

        for (Map.Entry<String, SpecialAttrPO> entry : attributeKeyMap.entrySet()) {
            SpecialAttrRelationDTO relationDTO = new SpecialAttrRelationDTO();
            SpecialAttrPO value = entry.getValue();
            relationDTO.setAttributeKey(entry.getKey());
            relationDTO.setAttributeKeyId(value.getId());
            String valueName = specialAttrMap.get(entry.getKey());
            if (StringUtils.isNotBlank(valueName)) {
                relationDTO.setAttributeValueId(SkuSpecialAttrValueEnum.getByName(valueName).getRelationId());
            }
            relationMap.put(value.getId(), relationDTO);
        }
        return relationMap;
    }
}



