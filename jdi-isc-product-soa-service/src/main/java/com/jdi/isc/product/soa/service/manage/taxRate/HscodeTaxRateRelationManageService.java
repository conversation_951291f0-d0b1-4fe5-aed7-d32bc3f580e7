package com.jdi.isc.product.soa.service.manage.taxRate;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.domain.taxRate.biz.HscodeTaxRateRelationPageVO;
import com.jdi.isc.product.soa.domain.taxRate.res.HscodeTaxRateRelationResVO;
import com.jdi.isc.product.soa.service.taxRate.req.HscodeTaxRateRelationReqVO;

/**
 * @Description: 跨境税率关系数据维护服务
 * @Author: chengliwei
 * @Date: 2024/12/11 10:47
 **/

public interface HscodeTaxRateRelationManageService {

    /**
     * 保存、更新
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<HscodeTaxRateRelationResVO> saveOrUpdate(HscodeTaxRateRelationReqVO input);

    /**
     * 详情
     * @param id 对象ID
     * @return VO对象
     */
    HscodeTaxRateRelationResVO detail(Long id);

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    PageInfo<HscodeTaxRateRelationPageVO.Response> pageSearch(HscodeTaxRateRelationPageVO.Request input);
}