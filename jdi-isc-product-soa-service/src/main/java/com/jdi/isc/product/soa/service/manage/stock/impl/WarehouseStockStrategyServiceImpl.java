package com.jdi.isc.product.soa.service.manage.stock.impl;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockItemOccupyResDTO;
import com.jdi.isc.product.soa.api.stock.res.StockOccupyResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.StockOperateResEnum;
import com.jdi.isc.product.soa.common.enums.StockOperateTypeEnum;
import com.jdi.isc.product.soa.common.exception.StockWriteException;
import com.jdi.isc.product.soa.common.util.StockNumUtils;
import com.jdi.isc.product.soa.domain.enums.stock.StockOccupyTypeEnum;
import com.jdi.isc.product.soa.domain.enums.stock.StockPriorityEnum;
import com.jdi.isc.product.soa.domain.enums.stock.StockWriteTypeEnum;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.stock.po.StockLogPO;
import com.jdi.isc.product.soa.domain.stock.po.StockPO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehouseSkuPO;
import com.jdi.isc.product.soa.service.manage.stock.StockWriteStrategyService;
import com.jdi.isc.product.soa.service.manage.stock.TransitStockWriteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description：备货库存写服务
 * @Date 2025-03-10
 */
@Slf4j
@Service
public class WarehouseStockStrategyServiceImpl extends AbstractStockWriteService implements StockWriteStrategyService, TransitStockWriteService {

    @Override
    public DataResponse<Boolean> saveOrUpdate(StockManageReqDTO req) {
        DataResponse<Boolean> validatedResponse = this.validateWarehouseExistForInit(req);
        if (!validatedResponse.getSuccess()) {
            log.error("WarehouseStockStrategyServiceImpl.saveOrUpdate 请求参数业务校验失败！req:{}, validResponse:{}", JSON.toJSONString(req), JSON.toJSONString(validatedResponse));
            return validatedResponse;
        }

        //在途追加幂等校验
        if (Objects.equals(StockWriteTypeEnum.APPEND_TRANSIT_STOCK.getCode(), req.getStockWriteType())) {
            if (StringUtils.isBlank(req.getOrderId())) {
                return DataResponse.buildError(String.format("WarehouseStockStrategyServiceImpl.saveOrUpdate必填参数为空! req=%s", JSON.toJSONString(req)));
            }

            //只要存在父单加在途记录，子单则不用再增加在途
            List<StockLogPO> existsOrder = stockLogAtomicService.list(req.getOrderId(), req.getOrderId(), StockOperateTypeEnum.APPEND_TRANSIT_STOCK, null);
            if (CollectionUtils.isNotEmpty(existsOrder)) {
                log.warn("WarehouseStockStrategyServiceImpl.saveOrUpdate父单已存在在途追加记录，无需处理! 入参:{}", JSON.toJSONString(req));
                return DataResponse.success(true);
            }

            //父单+子单是否加过在途，加过则幂等处理
            List<StockLogPO> existsBizOrder = stockLogAtomicService.list(req.getOrderId(), req.getBizNo(), StockOperateTypeEnum.APPEND_TRANSIT_STOCK, null);
            if (CollectionUtils.isNotEmpty(existsBizOrder)) {
                log.warn("WarehouseStockStrategyServiceImpl.saveOrUpdate父单+子单已存在在途追加记录，无需处理! 入参:{}", JSON.toJSONString(req));
                return DataResponse.success(true);
            }
        }

        for(StockItemManageReqDTO item : req.getStockItem()){
            StockPO target = this.buildStockPo(item, req.getStockWriteType());
            // 仓库ID+SKU ID
            if (stockAtomicService.existsByWarehouseId(item.getSkuId(),item.getWarehouseId())){
                this.updateStockByWarehouseId(req, item, target);
            }else {
                this.initStock(req, item, target);
            }
        }
        return DataResponse.success(true);
    }

    @Override
    public DataResponse<StockOccupyResDTO> occupyStock(StockManageReqDTO req) {
        DataResponse<StockOccupyResDTO> response = new DataResponse<>();
        Set<Long> skuSet = req.getStockItem().stream().map(StockItemManageReqDTO::getSkuId).collect(Collectors.toSet());
        /**** 支持重试，已经预占成功时，直接返回上次预占的结果
         *  1、查询当前订单的预占记录 现货预占和在途预占，如果存在就返回
         *  2、所有sku不存在预占记录，走正常逻辑
         * */
        response = super.retryOccupyStock(req, skuSet,Lists.newArrayList(StockOperateTypeEnum.OCCUPY, StockOperateTypeEnum.WAITING_ALLOCATION));
        log.info("WarehouseStockStrategyServiceImpl.occupyStock 预占接口重入，比对结果,入参:[{}],出参:[{}]",JSON.toJSONString(req), JSON.toJSONString(response));
        if (response != null && response.getSuccess() && Objects.nonNull(response.getData())) {
            return response;
        }

        // 校验仓库是否存在
        DataResponse<Boolean> dataResponse = validateWarehouseExist(req);
        if (!dataResponse.getSuccess()){
            return response.buildError(dataResponse.getCode(),dataResponse.getMessage());
        }

        Map<Long, List<StockPO>> stockMap = stockAtomicService.getStockMap(skuSet);
        //未查询到库存直接返回
        if(MapUtils.isEmpty(stockMap)){
            return buildError(StockOperateResEnum.EXCEPTION_NULL_STOCK,false,skuSet);
        }
        // 校验可售库存是否足够
        DataResponse<StockOccupyResDTO> processStockValidate = this.processStockValidate(req, stockMap);
        if (!processStockValidate.getSuccess()) {
            return processStockValidate;
        }

        return handleStockPriority(req);
    }

    /**
     * 处理库存优先级并预占库存。
     * 现货优先时：
     * 1. 在途可售：
     *    - 商品现货预占。
     *    - 如果商品在途 + 现货 - 预占可用库存足够，但现货不足，现货预占，剩余数量进入待分配状态，增加预占数量。
     * 2. 非在途可售：
     *    - 仅考虑现货预占。
     *    - 如果现货 - 预占可用库存足够，预占现货；否则，报错库存不足。
     *
     * 当非现货优先时：
     * 1. 在途可售：
     *    - 商品现货预占。
     *    - 如果商品在途 + 现货 - 预占可用库存足够，但现货不足，现货不预占，进入待分配状态，增加预占数量。
     *
     * 2. 非在途可售：
     *    - 仅考虑现货预占。
     *    - 如果现货 - 预占可用库存足够，现货预占；否则，报错库存不足。
     *
     * @param req 请求对象（StockManageReqDTO），包含订单ID、库存项等信息。
     * @return 响应对象（DataResponse<StockOccupyResDTO>），包含预占库存的结果。
     */
    private DataResponse<StockOccupyResDTO> handleStockPriority(StockManageReqDTO req) {
        StockOccupyResDTO occupyResDTO = new StockOccupyResDTO();
        occupyResDTO.setOrderId(req.getOrderId());
        List<StockItemOccupyResDTO> stockItemOccupy = Lists.newArrayListWithCapacity(req.getStockItem().size());
        Set<Long> skuSet = req.getStockItem().stream().map(StockItemManageReqDTO::getSkuId).collect(Collectors.toSet());
        // 遍历所有SKU
        int totalCnt = 0;
        for (StockItemManageReqDTO item : req.getStockItem()) {
            int occupyCnt = this.occupyStockEachItem(req, item, stockItemOccupy);
            totalCnt = totalCnt + occupyCnt;
        }
        // 预占操作总数和商品数量不一致，返回错误
        if (totalCnt != req.getStockItem().size()) {
            return buildError(StockOperateResEnum.EXCEPTION_OCCUPY, true, skuSet);
        }

        occupyResDTO.setStockItemOccupy(stockItemOccupy);
        return DataResponse.success(occupyResDTO);
    }

    private int occupyStockEachItem(StockManageReqDTO req, StockItemManageReqDTO item, List<StockItemOccupyResDTO> stockItemOccupy) {
        int occupyCnt = 0;
        WarehouseSkuPO warehouseSkuPO = warehouseSkuAtomicService.queryWarehouseSku(item.getSkuId(), Long.valueOf(item.getWarehouseId()));
        // 在途可售标识判断
        boolean onWaySale = BooleanUtils.toBoolean(warehouseSkuPO.getOnWaySale());
        // 查询当前仓有效库存
        StockPO validStock = stockAtomicService.getValidStock(item.getSkuId(), Long.valueOf(item.getWarehouseId()));
        validStock.setUpdater(item.getUpdater());
        /**当前预占现货库存数量  待分配数量存在时，现货预占总数量 = 预占总数-待分配的数量*/
        Long currentOccupyStockNum = validStock.getOccupy() - validStock.getWaitAllocationStock();
        // 当前现货数量
        Long currentStockNum = validStock.getStock() - currentOccupyStockNum;
        log.info("WarehouseStockStrategyServiceImpl.occupyStockEachItem 当前商品现货库存总数量:{},总预占数量:{},当前预占现货库存数量:{},当前剩余现货数量:{}", validStock.getStock(), validStock.getOccupy(), currentOccupyStockNum, currentStockNum);
        // 现货-现货预占满足，预占现货
        if (currentStockNum >= item.getNum()) {
            occupyCnt = occupyStockItem(req, item, stockItemOccupy, validStock);
        }else if (onWaySale){
            /** 有货先发，现货不足，在途库存+现货库存-预占库存满足，进入待分配逻辑**/
            if (StockPriorityEnum.STOCK_PRIORITY.getCode() == req.getStockPriority()){
                occupyCnt = occupyStockWaitingAllocation(req, item, stockItemOccupy, validStock, currentStockNum);
            }else {
                /**非有货先发时，现货不足，可售库存（含在途）满足，只是预占，等待分配**/
                occupyCnt = occupyWaitingAllocation(req, item, stockItemOccupy, validStock);
            }
        }
        return occupyCnt;
    }

    private int occupyWaitingAllocation(StockManageReqDTO req, StockItemManageReqDTO item, List<StockItemOccupyResDTO> stockItemOccupy, StockPO validStock) {
        int occupyCnt;
        // 现货库存不足，在途可售,可售库存（含在途）满足下单数量
        occupyCnt = stockAtomicService.occupyEachStockFromWarehouse(item, validStock.getVersion(),item.getNum());
        if (occupyCnt > 0){
            // 预占现货库存数量和在途库存数量返回
            stockItemOccupy.add(new StockItemOccupyResDTO(item.getSkuId(), Constant.ZERO, item.getNum()));
        }
        validStock.setUpdater(item.getUpdater());
        log.info("StockAtomicService.occupyEachStockFromWarehouse 现货不预占，只有待分配 req:{} ,warehouseId={}, res:{} , occupyResult:{}" ,  item.getSkuId(), item.getWarehouseId(), item.getNum(), occupyCnt);
        // 在途预占库存记录
        stockLogAtomicService.saveLog(validStock, StockOperateTypeEnum.WAITING_ALLOCATION, req.getBizNo(), req.getOrderId(), item.getNum());
        return occupyCnt;
    }

    private int occupyStockWaitingAllocation(StockManageReqDTO req, StockItemManageReqDTO item, List<StockItemOccupyResDTO> stockItemOccupy, StockPO validStock,Long currentStockNum) {
        int occupyCnt;
        // 现货库存不足，在途可售，并且在途库存满足
        long addWaitingAllocationStockNum = item.getNum() - currentStockNum;
        occupyCnt = stockAtomicService.occupyEachStockFromWarehouse(item, validStock.getVersion(),addWaitingAllocationStockNum);
        if (occupyCnt > 0){
            // 预占现货库存数量和在途库存数量返回
            stockItemOccupy.add(new StockItemOccupyResDTO(item.getSkuId(), currentStockNum, addWaitingAllocationStockNum));
        }
        validStock.setUpdater(item.getUpdater());
        log.info("StockAtomicService.occupyEachStockFromWarehouse 现货和在途同时预占 req:{} ,warehouseId={}, res:{} , occupyResult:{}" ,  item.getSkuId(), item.getWarehouseId(), item.getNum(), occupyCnt);
        // 现货预占库存记录 现货不为0，记录预占库存
        if (!Constant.ZERO.equals(currentStockNum)){
            stockLogAtomicService.saveLog(validStock, StockOperateTypeEnum.OCCUPY, req.getBizNo(), req.getOrderId(), validStock.getStock());
        }
        // 在途预占库存记录
        stockLogAtomicService.saveLog(validStock, StockOperateTypeEnum.WAITING_ALLOCATION, req.getBizNo(), req.getOrderId(), addWaitingAllocationStockNum);
        return occupyCnt;
    }

    private int occupyStockItem(StockManageReqDTO req, StockItemManageReqDTO item, List<StockItemOccupyResDTO> stockItemOccupy, StockPO validStock) {
        int occupyCnt;
        // 备货品预占现货库存
        occupyCnt = stockAtomicService.occupyEachStockFromWarehouse(item, validStock.getVersion(),Constant.ZERO);
        if (occupyCnt > 0){
            stockItemOccupy.add(new StockItemOccupyResDTO(item.getSkuId(), item.getNum(), 0L));
        }
        validStock.setUpdater(item.getUpdater());
        log.info("StockAtomicService.occupyEachStockFromWarehouse req:{} ,warehouseId={}, res:{} , occupyResult:{}" ,  item.getSkuId(), item.getWarehouseId(), item.getNum(), occupyCnt);
        // 现货预占库存记录
        stockLogAtomicService.saveLog(validStock, StockOperateTypeEnum.OCCUPY, req.getBizNo(), req.getOrderId(), item.getNum());
        return occupyCnt;
    }

    @Override
    public DataResponse<Boolean> releaseStock(StockManageReqDTO req) {
        DataResponse<Boolean> validatedResponse = this.validateWarehouseExist(req);
        if (!validatedResponse.getSuccess()){
            return validatedResponse;
        }
        int effectiveStock = stockAtomicService.releaseStockWarehouse(req);
        //预占释放数量与请求数量不一致
        if(effectiveStock != req.getStockItem().size()){
            return buildErr(StockOperateResEnum.EXCEPTION_OCCUPY_RELEASE,true,req.getStockItem());
        }
        return stockLogAtomicService.save(req, StockOperateTypeEnum.RELEASE);
    }

    @Override
    public DataResponse<Boolean> occupyStockReturn(StockManageReqDTO req) {
        // 重入校验，如果已经存在当前库存操作的流水，则直接返回成功，不再重复处理
        DataResponse<Boolean> response =  super.retryStockReturn(req, req.getStockItem().stream().map(StockItemManageReqDTO::getSkuId).collect(Collectors.toSet()), Lists.newArrayList(StockOperateTypeEnum.OCCUPY_RETURN,StockOperateTypeEnum.TRANSIT_STOCK_OCCUPY_RETURN));
        log.info("FactoryStockStrategyServiceImpl.occupyStockReturn 预占回退接口重入，比对结果,入参:[{}],出参:[{}]",JSON.toJSONString(req), JSON.toJSONString(response));
        if (response != null && response.getSuccess() && Objects.nonNull(response.getData())) {
            return response;
        }
        DataResponse<Boolean> validatedResponse = this.validateWarehouseExist(req);
        if (!validatedResponse.getSuccess()){
            return validatedResponse;
        }

        int effectiveStock = stockAtomicService.occupyStockReturnWarehouse(req);
        //预占退回量与请求数量不一致
        if(effectiveStock != req.getStockItem().size()){
            return buildErr(StockOperateResEnum.EXCEPTION_OCCUPY_RETURN,true,req.getStockItem());
        }
        return DataResponse.success(Boolean.TRUE);
    }

    @Override
    public DataResponse<StockOccupyResDTO> occupyRelease(StockManageReqDTO req) {
        StockOccupyResDTO occupyResDTO = new StockOccupyResDTO();
        occupyResDTO.setOrderId(req.getBizNo());
        List<StockItemOccupyResDTO> stockItemOccupy = Lists.newArrayListWithCapacity(req.getStockItem().size());
        // 在途预占转现货预占条件
        boolean occupyRelease = stockAtomicService.verifyOccupyRelease(req);
        if (!occupyRelease) {
            log.debug("FactoryStockStrategyServiceImpl.occupyRelease 在途预占转现货预占条件校验失败, 入参:{}", JSON.toJSONString(req));
            return this.buildError(StockOperateResEnum.EXCEPTION_ON_WAY_STOCK_TRANSFER_STOCK, false, false, req.getStockItem());
        }

        List<StockItemManageReqDTO> stockItem = req.getStockItem();
        Set<Long> skuIds = stockItem.stream().map(StockItemManageReqDTO::getSkuId).collect(Collectors.toSet());
        // 查询当前仓库现货库存预占记录 查询父单下的预占记录
        String warehouseId = stockItem.get(0).getWarehouseId();
        //Map<Long, Long> occupyNumMap = stockLogAtomicService.getSkuStockOccupyLogForWarehouseMap(null, req.getBizNo(), warehouseId, skuIds);

        // 在途预占库存数量 > 现货 直接返回报错
        int totalCnt = 0;
        for (StockItemManageReqDTO item : stockItem) {
            int transferedCnt=0;
            // 查询当前仓库存信息
            StockPO validStock = stockAtomicService.getValidStock(item.getSkuId(), Long.valueOf(item.getWarehouseId()));
            // sku现货预占总数= sku总预占数量-sku总在途预占数量
            Long occupyStockNum = validStock.getOccupy() - validStock.getWaitAllocationStock();
            // sku现货库存数量 - sku总现货预占库存数量 >= sku在途转占转现货预占数量
            if (validStock.getStock() - occupyStockNum >= item.getNum()) {
                //sku在途预占库存数量转现货成功
                StockItemOccupyResDTO successStockItemOccupy = new StockItemOccupyResDTO(item.getSkuId(), item.getNum(), 0L);
                successStockItemOccupy.setStockFlag(true);
                stockItemOccupy.add(successStockItemOccupy);
                validStock.setUpdater(item.getUpdater());
                transferedCnt = stockAtomicService.onWayOccupyTransferToStock(item, validStock);
                stockLogAtomicService.saveLog(validStock, StockOperateTypeEnum.TRANSIT_STOCK_TRANSFER_STOCK_OCCUPY, req.getBizNo(), req.getOrderId(), item.getNum());
            } else {
                //整单转换返回失败
                if(Objects.equals(req.getStockOccupyType(), StockOccupyTypeEnum.BY_ORDER.getCode())){
                    return this.buildError(StockOperateResEnum.EXCEPTION_ON_WAY_STOCK_TRANSFER_STOCK,false,req.getStockItem());
                }

                //sku在途预占转现货预占失败
                StockItemOccupyResDTO failStockItemOccupy = new StockItemOccupyResDTO(item.getSkuId(), 0L, validStock.getWaitAllocationStock());
                failStockItemOccupy.setStockFlag(false);
                stockItemOccupy.add(failStockItemOccupy);
            }

            totalCnt = totalCnt + transferedCnt;
        }

        //预占转现货结果数校验
        if (verifyIllegalResultCnt(totalCnt, req)) {
            log.debug("FactoryStockStrategyServiceImpl.occupyRelease 在途预占转现货预占结果失败, 入参:{}, totalCnt:{}", JSON.toJSONString(req), totalCnt);
            return buildError(StockOperateResEnum.EXCEPTION_ON_WAY_STOCK_TRANSFER_STOCK, false, false, req.getStockItem());
        }

        occupyResDTO.setStockItemOccupy(stockItemOccupy);
        return DataResponse.success(occupyResDTO);
    }

    /**
     * 验证库存非法结果数量
     * @param totalCnt 总数量值
     * @param req 库存管理请求DTO对象
     * @return 当库存占用类型为按订单占用时返回总数量与库存项数量是否不等，否则返回总数量是否为0
     */
    private boolean verifyIllegalResultCnt(Integer totalCnt, StockManageReqDTO req) {
        if (Objects.equals(req.getStockOccupyType(), StockOccupyTypeEnum.BY_ORDER.getCode())) {
            return totalCnt != req.getStockItem().size();
        } else {
            return totalCnt == 0;
        }
    }

    @Override
    public DataResponse<Boolean> stockReturn(StockManageReqDTO req) {
        // 重入校验，如果已经存在当前库存操作的流水，则直接返回成功，不再重复处理
        DataResponse<Boolean> response =  super.retryStockReturn(req, req.getStockItem().stream().map(StockItemManageReqDTO::getSkuId).collect(Collectors.toSet()), Lists.newArrayList(StockOperateTypeEnum.STOCK_RETURN));
        log.info("FactoryStockStrategyServiceImpl.stockReturn 现货回退接口重入，比对结果,入参:[{}],出参:[{}]",JSON.toJSONString(req), JSON.toJSONString(response));
        if (response != null && response.getSuccess() && Objects.nonNull(response.getData())) {
            return response;
        }
        DataResponse<Boolean> validatedResponse = this.validateWarehouseExist(req);
        if (!validatedResponse.getSuccess()){
            return validatedResponse;
        }
        int effectiveStock = stockAtomicService.stockReturn(req);
        //现货回退生效数量与请求数量不一致
        if(effectiveStock != req.getStockItem().size()){
            return buildErr(StockOperateResEnum.EXCEPTION_STOCK_RETURN,true,req.getStockItem());
        }
        return stockLogAtomicService.save(req, StockOperateTypeEnum.STOCK_RETURN);
    }

    @Override
    public DataResponse<Boolean> transitStockReturn(StockManageReqDTO req) {
        DataResponse<Boolean> validatedResponse = super.validateWarehouseExist(req);
        if (!validatedResponse.getSuccess()){
            return validatedResponse;
        }
        // 1、在途库存回退
        int effectiveStock = stockAtomicService.transitStockReturn(req);
        //预占释放数量与请求数量不一致
        if(effectiveStock != req.getStockItem().size()){
            return super.buildErr(StockOperateResEnum.EXCEPTION_ON_WAY_STOCK_RETURN,true,req.getStockItem());
        }
        stockLogAtomicService.save(req, StockOperateTypeEnum.TRANSIT_STOCK_RETURN);

        // 2、厂直现货回退 要判断是否跨境品
        Set<Long> skuIds = req.getStockItem().stream().map(StockItemManageReqDTO::getSkuId).collect(Collectors.toSet());
        List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(skuIds);
        // 非跨境品
        Set<Long> noCrossBorderSku = skuPOList.stream().filter(skuPO -> !CountryConstant.COUNTRY_ZH.equals(skuPO.getSourceCountryCode())).map(SkuPO::getSkuId).collect(Collectors.toSet());
        List<StockItemManageReqDTO> itemList = req.getStockItem().stream().filter(item -> noCrossBorderSku.contains(item.getSkuId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(itemList)) {
            // 厂直回退，需要设置仓库id为null
            itemList.forEach(item -> item.setWarehouseId(null));
            req.setStockItem(itemList);
            int effectiveReturnStock = stockAtomicService.stockReturn(req);
            //预占释放数量与请求数量不一致
            if(effectiveReturnStock != itemList.size()){
                return super.buildErr(StockOperateResEnum.EXCEPTION_ON_WAY_STOCK_RETURN,true,req.getStockItem());
            }
            stockLogAtomicService.save(req, StockOperateTypeEnum.STOCK_RETURN);
        }
        return DataResponse.success(Boolean.TRUE);
    }

    @Override
    public DataResponse<Boolean> transitStockToStock(StockManageReqDTO req) {
        DataResponse<Boolean> validatedResponse = this.validateWarehouseExist(req);
        if (!validatedResponse.getSuccess()){
            return validatedResponse;
        }
        int effectiveStock = stockAtomicService.transitStockToStock(req);
        //在途转现货数量与请求数量不一致
        if(effectiveStock != req.getStockItem().size()){
            return buildErr(StockOperateResEnum.EXCEPTION_ON_WAY_STOCK_TRANSFER_STOCK,true,req.getStockItem());
        }
        return stockLogAtomicService.save(req, StockOperateTypeEnum.TRANSIT_STOCK_TRANSFER_STOCK);
    }


    /**
     * 根据仓库ID更新库存
     * @param req 库存管理请求数据传输对象
     * @param item 库存项管理请求数据传输对象
     * @param target 目标库存持久化对象
     */
    private void updateStockByWarehouseId(StockManageReqDTO req, StockItemManageReqDTO item, StockPO target) {
        int stockCntFlag;
        boolean stockUptLogFlag;
        StockPO validStock = stockAtomicService.getValidStock(item.getSkuId(), Long.valueOf(item.getWarehouseId()));
        if (Objects.isNull(validStock)) {
            throw new StockWriteException("StockManageServiceImpl.updateStockByWarehouseId 指定仓库存不存在，不能更新 error" + JSON.toJSONString(item));
        }
        // 设置当前版本
        target.setVersion(validStock.getVersion());
        if(req.getStockWriteType()==null || Objects.equals(StockWriteTypeEnum.UPDATE_STOCK.getCode(), req.getStockWriteType())){
            stockCntFlag = stockAtomicService.updateStockByWarehouseId(target);
            stockUptLogFlag = stockLogAtomicService.saveLog(target, StockOperateTypeEnum.UPDATE_STOCK, req.getBizNo(), req.getOrderId(),item.getNum());
        }else if (Objects.equals(StockWriteTypeEnum.APPEND_STOCK.getCode(), req.getStockWriteType())){
            stockCntFlag = stockAtomicService.appendStockByWarehouseId(target);
            stockUptLogFlag = stockLogAtomicService.saveLog(target, StockOperateTypeEnum.APPEND_STOCK, req.getBizNo(), req.getOrderId(),item.getNum());
        }else {
            stockCntFlag = stockAtomicService.appendTransitStockByWarehouseId(target);
            stockUptLogFlag = stockLogAtomicService.saveLog(target, StockOperateTypeEnum.APPEND_TRANSIT_STOCK, req.getBizNo(), req.getOrderId(),item.getNum());
        }
        if(stockCntFlag!=1 || !stockUptLogFlag){
            throw new StockWriteException("StockManageServiceImpl.指定仓库存写入异常 error" + JSON.toJSONString(item));
        }
    }

    /**
     * 验证库存是否足够，处理库存占用请求。
     * @param req StockManageReqDTO类型的请求对象，包含待处理的库存项信息。
     * @param stockMap Map类型的库存映射，key为SKU ID，value为对应的StockPO列表。
     * @return DataResponse<StockOccupyResDTO>类型的响应对象，包含处理结果和可能的错误信息。
     */
    private DataResponse<StockOccupyResDTO> processStockValidate(StockManageReqDTO req, Map<Long, List<StockPO>> stockMap) {
        Set<Long> insufficientSku = new HashSet<>();
        //可用库存判断
        for(StockItemManageReqDTO stockReq : req.getStockItem()){
            List<StockPO> stockDbList = stockMap.get(stockReq.getSkuId());
            // 库存为空
            if (CollectionUtils.isEmpty(stockDbList)) {
                return buildError(StockOperateResEnum.EXCEPTION_NULL_STOCK, false, stockReq.getSkuId());
            }

            // 仓库库存
            Optional<StockPO> first = stockDbList.stream().filter(stockPO -> StringUtils.isNotBlank(stockPO.getWarehouseId()) && stockPO.getWarehouseId().equals(stockReq.getWarehouseId())).findFirst();
            if (!first.isPresent()){
                insufficientSku.add(stockReq.getSkuId());
                continue;
            }

            StockPO stockPO = first.get();
            // 当前仓库库存为空或者可用库存为空
            WarehouseSkuPO warehouseSkuPO = warehouseSkuAtomicService.queryWarehouseSku(stockPO.getSkuId(), Long.valueOf(stockPO.getWarehouseId()));
            // 在途可售标识判断
            boolean onWaySale = Objects.nonNull(warehouseSkuPO.getOnWaySale()) && new Integer(1).equals(warehouseSkuPO.getOnWaySale());
            // 可售库存不足，提示库存不足
            Long availableStock = StockNumUtils.calculateAvailableStock(stockPO, onWaySale);
            log.info("processStockValidate ,skuId={} warehouseId={} availableStock:{},num={}",stockReq.getSkuId(),stockReq.getWarehouseId(), availableStock,stockReq.getNum());
            if (availableStock < stockReq.getNum()){
                insufficientSku.add(stockReq.getSkuId());
            }
        }
        if(CollectionUtils.isNotEmpty(insufficientSku)){
            return buildError(StockOperateResEnum.WARNING_INSUFFICIENT_STOCK, false, insufficientSku);
        }
        return DataResponse.success();
    }
}
