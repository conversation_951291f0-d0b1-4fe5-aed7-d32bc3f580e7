package com.jdi.isc.product.soa.service.manage.saleAttribute.impl;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.translate.req.BatchMultiTranslateReqDTO;
import com.jdi.isc.product.soa.api.translate.req.MultiTranslateReqDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeValueLangVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeValueVO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeLangPO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeValueLangPO;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SaleAttributeLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SaleAttributeValueLangAtomicService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeLangManageService;
import com.jdi.isc.product.soa.service.manage.translate.TextTranslateManageService;

import com.jdi.isc.product.soa.service.mapstruct.saleAttribute.SaleAttributeConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 销售属性多语言管理服务实现
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
public class SaleAttributeLangManageServiceImpl implements SaleAttributeLangManageService {

    @Resource
    private SaleAttributeLangAtomicService saleAttributeLangAtomicService;

    @Resource
    private SaleAttributeValueLangAtomicService saleAttributeValueLangAtomicService;

    @Resource
    private TextTranslateManageService textTranslateManageService;

    @Resource
    private LangManageService langManageService;

    public static final ExecutorService pool = new ThreadPoolExecutor(8, 16, 90L, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(200), new ThreadFactoryBuilder().setNamePrefix("sale-attr").build());

    /**
     * 异步处理超时时间（秒）
     */
    private static final int ASYNC_TIMEOUT_SECONDS = 5;

    @Override
    public Map<String, String> getSaleAttributeNameLangsByCode(List<String> saleAttributeCodeList, String lang) {
        log.info("SaleAttributeLangManageServiceImpl.getSaleAttributeNameLangsByCode 入参: saleAttributeCodeList={}, lang={}", 
                JSON.toJSONString(saleAttributeCodeList), lang);

        if (CollectionUtils.isEmpty(saleAttributeCodeList) || StringUtils.isBlank(lang)) {
            log.warn("getSaleAttributeNameLangsByCode 参数为空");
            return new HashMap<>();
        }

        Map<String, String> result = new HashMap<>();

        try {
            // 批量查询所有相关的销售属性多语言记录
            List<SaleAttributeLangPO> allLangPOList = saleAttributeLangAtomicService.getValidBySaleAttributeCodeList(saleAttributeCodeList);
            log.info("批量查询销售属性多语言记录，查询条件数量: {}, 查询结果数量: {}", 
                    saleAttributeCodeList.size(), allLangPOList != null ? allLangPOList.size() : 0);
            
            // 按销售属性编码分组
            Map<String, List<SaleAttributeLangPO>> langPOMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(allLangPOList)) {
                langPOMap = allLangPOList.stream()
                        .collect(Collectors.groupingBy(SaleAttributeLangPO::getSaleAttributeCode));
            }
            
            // 为每个销售属性编码获取对应的多语言名称
            for (String saleAttributeCode : saleAttributeCodeList) {
                List<SaleAttributeLangPO> langPOList = langPOMap.getOrDefault(saleAttributeCode, new ArrayList<>());
                
                String langName = null;
                if (CollectionUtils.isNotEmpty(langPOList)) {
                    // 优先使用指定语言的翻译
                    Optional<SaleAttributeLangPO> targetLang = langPOList.stream()
                            .filter(po -> lang.equals(po.getLang()))
                            .findFirst();
                    
                    if (targetLang.isPresent()) {
                        langName = targetLang.get().getLangName();
                    } else {
                        // 如果没有指定语言，则获取英文
                        Optional<SaleAttributeLangPO> enLang = langPOList.stream()
                                .filter(po -> LangConstant.LANG_EN.equals(po.getLang()))
                                .findFirst();
                        
                        if (enLang.isPresent()) {
                            langName = enLang.get().getLangName();
                        } else {
                            // 如果英文也没有，则获取中文
                            Optional<SaleAttributeLangPO> zhLang = langPOList.stream()
                                    .filter(po -> LangConstant.LANG_ZH.equals(po.getLang()))
                                    .findFirst();
                            
                            if (zhLang.isPresent()) {
                                langName = zhLang.get().getLangName();
                            }
                        }
                    }
                }
                
                // 如果都没有，则用saleAttributeCode兜底
                result.put(saleAttributeCode, StringUtils.isNotBlank(langName) ? langName : saleAttributeCode);
            }

            log.info("getSaleAttributeNameLangsByCode 返回结果: {}", JSON.toJSONString(result));
            return result;

        } catch (Exception e) {
            log.error("getSaleAttributeNameLangsByCode 异常", e);
            // 异常情况下返回saleAttributeCode作为兜底
            for (String saleAttributeCode : saleAttributeCodeList) {
                result.put(saleAttributeCode, saleAttributeCode);
            }
            return result;
        }
    }

    @Override
    public Map<Long, List<SaleAttributeValueLangVO>> getSaleAttributeValueNameLangsByIds(List<Long> saleAttributeValueIdList) {
        log.info("SaleAttributeLangManageServiceImpl.getSaleAttributeValueLangsByIds 入参: saleAttributeValueIdList={}", 
                JSON.toJSONString(saleAttributeValueIdList));

        if (CollectionUtils.isEmpty(saleAttributeValueIdList)) {
            log.warn("getSaleAttributeValueLangsByIds 参数为空");
            return new HashMap<>();
        }

        try {
            Map<Long, List<SaleAttributeValueLangVO>> result = new HashMap<>();

            // 批量查询销售属性值的多语言
            List<SaleAttributeValueLangPO> langPOList = saleAttributeValueLangAtomicService.getValidBySaleAttributeValueIds(saleAttributeValueIdList);

            if (CollectionUtils.isNotEmpty(langPOList)) {
                // 按销售属性值ID分组
                Map<Long, List<SaleAttributeValueLangPO>> groupedByValueId = langPOList.stream()
                        .collect(Collectors.groupingBy(SaleAttributeValueLangPO::getSaleAttributeValueId));

                // 转换为VO
                for (Map.Entry<Long, List<SaleAttributeValueLangPO>> entry : groupedByValueId.entrySet()) {
                    List<SaleAttributeValueLangVO> voList = SaleAttributeConvert.INSTANCE.toValueLangVOList(entry.getValue());
                    result.put(entry.getKey(), voList);
                }
            }

            // 对于没有多语言记录的ID，返回空列表
            for (Long saleAttributeValueId : saleAttributeValueIdList) {
                result.putIfAbsent(saleAttributeValueId, new ArrayList<>());
            }

            log.info("getSaleAttributeValueLangsByIds 返回结果: {}", JSON.toJSONString(result));
            return result;

        } catch (Exception e) {
            log.error("getSaleAttributeValueLangsByIds 异常", e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, String> getLocalTranslateSaleAttributeNamesByLang(Set<String> saleAttributeNameSet, String lang) {
        log.info("SaleAttributeLangManageServiceImpl.getLocalTranslateSaleAttributeNamesByLang 入参: saleAttributeNameSet={}, lang={}", 
                JSON.toJSONString(saleAttributeNameSet), lang);
        if (CollectionUtils.isEmpty(saleAttributeNameSet) || StringUtils.isBlank(lang)) {
            log.warn("SaleAttributeLangManageServiceImpl.getLocalTranslateSaleAttributeNamesByLang 参数为空");
            return new HashMap<>();
        }
        List<String> langList = langManageService.getLangCodeList();
        Set<String> langSet = Sets.newHashSet(langList);
        langSet.add(lang);
        Map<String, Map<String, String>> result = getLocalTranslateSaleAttributeNamesByLangSet(saleAttributeNameSet, langSet);
        Map<String, String> res = new HashMap<>();
        if(!MapUtils.isEmpty(result)){
            result.keySet().forEach(key->{
                if(result.get(key)!=null){
                    res.put(key, result.get(key).get(lang));
                } else{
                    // 兜底
                    res.put(key, key);
                    log.info("SaleAttributeLangManageServiceImpl.getLocalTranslateSaleAttributeNamesByLang 兜底: key={}", key);
                }
            });
        }
        return res;
    }
    private Map<String, Map<String, String>> getLocalTranslateSaleAttributeNamesByLangSet(Set<String> saleAttributeNameSet, Set<String> langSet) {
        log.info("SaleAttributeLangManageServiceImpl.getLocalTranslateSaleAttributeNamesByLangSet 入参: saleAttributeNameSet={}, langSet={}", 
                JSON.toJSONString(saleAttributeNameSet), JSON.toJSONString(langSet));

        if (CollectionUtils.isEmpty(saleAttributeNameSet) || CollectionUtils.isEmpty(langSet)) {
            log.warn("getLocalTranslateSaleAttributeNamesByLangSet 参数为空");
            return new HashMap<>();
        }

        try {
            // 1. 批量查询数据库，获取现有的翻译
            List<String> saleAttributeNameList = new ArrayList<>(saleAttributeNameSet);
            List<SaleAttributeLangPO> allLangPOList = saleAttributeLangAtomicService.getValidBySaleAttributeCodeList(saleAttributeNameList);
            log.info("批量查询销售属性名多语言记录，查询条件数量: {}, 查询结果数量: {}", 
                    saleAttributeNameList.size(), allLangPOList != null ? allLangPOList.size() : 0);
            
            // 按销售属性名分组
            Map<String, List<SaleAttributeLangPO>> langPOGroupMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(allLangPOList)) {
                langPOGroupMap = allLangPOList.stream()
                        .collect(Collectors.groupingBy(SaleAttributeLangPO::getSaleAttributeCode));
            }
            // saleAttributeName: lang: langName
            Map<String, Map<String, String>> existingTranslations = new HashMap<>();
            for (String saleAttributeName : saleAttributeNameSet) {
                List<SaleAttributeLangPO> langPOList = langPOGroupMap.getOrDefault(saleAttributeName, new ArrayList<>());
                
                Map<String, String> langMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(langPOList)) {
                    langMap = langPOList.stream()
                            .collect(Collectors.toMap(
                                    SaleAttributeLangPO::getLang,
                                    SaleAttributeLangPO::getLangName,
                                    (existing, replacement) -> existing
                            ));
                }
                existingTranslations.put(saleAttributeName, langMap);
            }

            // 2. 构建需要翻译的请求
            Set<MultiTranslateReqDTO> translateRequests = Sets.newHashSet();
            for (String saleAttributeName : saleAttributeNameSet) {
                Map<String, String> existingLangs = existingTranslations.getOrDefault(saleAttributeName, new HashMap<>());
                
                Set<String> missingLangs = langSet.stream()
                        .filter(lang -> !existingLangs.containsKey(lang) || StringUtils.isBlank(existingLangs.get(lang)))
                        .collect(Collectors.toSet());
                missingLangs.remove(LangConstant.LANG_ZH); // 去除中文
                if (CollectionUtils.isNotEmpty(missingLangs)) {
                    MultiTranslateReqDTO translateReq = new MultiTranslateReqDTO();
                    translateReq.setText(saleAttributeName);
                    translateReq.setFrom(LangConstant.LANG_ZH);
                    translateReq.setToLangList(new ArrayList<>(missingLangs));
                    translateRequests.add(translateReq);
                }
            }

            // 3. 异步执行翻译和写入数据库
            CompletableFuture<Map<String, Map<String, String>>> translateFuture = CompletableFuture.supplyAsync(() -> 
                    translateAndSaveSaleAttributeNames(translateRequests), pool);

            Map<String, Map<String, String>> translateResult;
            try {
                // 设置超时时间
                translateResult = translateFuture.get(ASYNC_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.warn("翻译超时，使用中文兜底", e);
                // 超时则使用中文兜底
                translateResult = createFallbackTranslations(saleAttributeNameSet, langSet);
            }

            // 4. 合并现有翻译和新翻译结果
            Map<String, Map<String, String>> finalResult = new HashMap<>(existingTranslations);
            for (Map.Entry<String, Map<String, String>> entry : translateResult.entrySet()) {
                String saleAttributeName = entry.getKey();
                Map<String, String> newTranslations = entry.getValue();
                
                Map<String, String> mergedTranslations = finalResult.getOrDefault(saleAttributeName, new HashMap<>());
                Map<String, String> result = new HashMap<>();
                result.putAll(newTranslations); // 先设置新翻译
                result.putAll(mergedTranslations);  // 然后设置原有值，如果有重复，原值覆盖机翻值，因为原值可能是人工修改的
                finalResult.put(saleAttributeName, result);
            }

            if (MapUtils.isEmpty(finalResult)) {
                log.warn("getLocalTranslateSaleAttributeNamesByLangSet 未获取到任何翻译结果 saleAttributeNameSet={}, langSet={}",
                        JSON.toJSONString(saleAttributeNameSet), JSON.toJSONString(langSet));
                finalResult = createFallbackTranslations(saleAttributeNameSet, langSet);  // 使用中文兜底，所有语言都会返回中文值
            }
            log.info("getLocalTranslateSaleAttributeNamesByLangSet 返回结果: {}", JSON.toJSONString(finalResult));
            return finalResult;

        } catch (Exception e) {
            log.error("getLocalTranslateSaleAttributeNamesByLangSet 异常", e);
            return createFallbackTranslations(saleAttributeNameSet, langSet);  // 使用中文兜底，所有语言都会返回中文值
        }
    }

    /**
     * 异步翻译和写入数据库
     */
    private Map<String, Map<String, String>> translateAndSaveSaleAttributeNames(Set<MultiTranslateReqDTO> translateRequests) {
        log.info("开始异步翻译和保存销售属性名称，请求数量: {}", translateRequests.size());

        if (CollectionUtils.isEmpty(translateRequests)) {
            return new HashMap<>();
        }

        try {

            BatchMultiTranslateReqDTO batchRequest = new BatchMultiTranslateReqDTO();
            Map<String, Map<String, String>> translateResult = new HashMap<>();
            //以每10个批次处理
            List<List<MultiTranslateReqDTO>> subList = Lists.partition(new ArrayList<>(translateRequests), 10);
            for(List<MultiTranslateReqDTO> req : subList){
                batchRequest.setMultiTranslateReqDTOSet(new HashSet<>(req));
                DataResponse<Map<String, Map<String, String>>> res = textTranslateManageService.batchTranslateMultiLangText(batchRequest);

                // 调用翻译服务
                DataResponse<Map<String, Map<String, String>>> translateResponse =
                        textTranslateManageService.batchTranslateMultiLangText(batchRequest);
                if (translateResponse.getSuccess() && MapUtils.isNotEmpty(translateResponse.getData())) {
                    translateResult.putAll(translateResponse.getData());
                    log.info("翻译成功，结果: {}", JSON.toJSONString(translateResult));
                    // 添加中文值
                    this.addChinese(translateResult);
                    // 保存翻译结果到数据库
                    saleAttributeLangAtomicService.saveSaleAttributeLangToDB(translateResult);
                } else {
                    log.warn("翻译失败: {}", translateResponse.getMessage());
                }
            }
            return translateResult;

        } catch (Exception e) {
            log.error("翻译和保存异常", e);
            return new HashMap<>();
        }
    }

    private static void addChinese(Map<String, Map<String, String>> translateResult) {
        // 添加中文值
        if(MapUtils.isNotEmpty(translateResult)){
            translateResult.keySet().forEach(key->{
                if(translateResult.get(key)!=null && translateResult.get(key).get(LangConstant.LANG_ZH)==null){
                    translateResult.get(key).put(LangConstant.LANG_ZH, key);
                }
            });
        }
    }

    /**
     * 创建中文兜底翻译
     */
    private Map<String, Map<String, String>>  createFallbackTranslations(Set<String> saleAttributeNameSet, Set<String> langSet) {
        Map<String, Map<String, String>> fallbackResult = new HashMap<>();

        for (String saleAttributeName : saleAttributeNameSet) {
            Map<String, String> langMap = new HashMap<>();
            for (String lang : langSet) {
                langMap.put(lang, saleAttributeName); // 使用中文名称作为兜底
            }
            fallbackResult.put(saleAttributeName, langMap);
        }

        return fallbackResult;
    }

    /**
     * 如果调用getSaleAttributeValueNameLangsByIds没有获取到数据，则使用中文名称作为兜底
     * 仅对数据库中不存在的词条id+lang组合进行翻译
     * @param saleAttributeValueVOList 从中台拉取的销售属性值，已经存入了数据库
     */
    @Override
    public void createCrossBorderSaleAttributeValueLangs(List<SaleAttributeValueVO> saleAttributeValueVOList) {
        log.info("SaleAttributeLangManageServiceImpl.createCrossBorderSaleAttributeValueLangs 入参: saleAttributeValueVOList={}", 
                JSON.toJSONString(saleAttributeValueVOList));

        if (CollectionUtils.isEmpty(saleAttributeValueVOList)) {
            log.warn("SaleAttributeLangManageServiceImpl.createCrossBorderSaleAttributeValueLangs 参数为空");
            return;
        }

        try {
            // 获取需要翻译的语言列表
            List<String> langList = langManageService.getLangCodeList();
            // 中文不需要翻译
            langList.remove(LangConstant.LANG_ZH);

            if (CollectionUtils.isEmpty(langList)) {
                log.warn("SaleAttributeLangManageServiceImpl.createCrossBorderSaleAttributeValueLangs 没有可用的翻译语言");
                return;
            }

            // 1. 查询已存在的翻译数据
            List<Long> saleAttributeValueIds = saleAttributeValueVOList.stream()
                    .filter(Objects::nonNull)
                    .filter(vo -> vo.getId() != null)
                    .map(SaleAttributeValueVO::getId)
                    .collect(Collectors.toList());
            
            Map<Long, List<SaleAttributeValueLangVO>> existingTranslationsMap = 
                    getSaleAttributeValueNameLangsByIds(saleAttributeValueIds);

            // 2. 创建已存在翻译的映射：词条ID + 语言 -> 是否存在
            Set<String> existingTranslationKeys = new HashSet<>();
            for (Map.Entry<Long, List<SaleAttributeValueLangVO>> entry : existingTranslationsMap.entrySet()) {
                Long valueId = entry.getKey();
                List<SaleAttributeValueLangVO> langList2 = entry.getValue();
                if (CollectionUtils.isNotEmpty(langList2)) {
                    for (SaleAttributeValueLangVO langVO : langList2) {
                        if (StringUtils.isNotBlank(langVO.getLang())) {
                            existingTranslationKeys.add(valueId + Constant.UNDER_LINE + langVO.getLang());
                        }
                    }
                }
            }

            log.info("SaleAttributeLangManageServiceImpl.createCrossBorderSaleAttributeValueLangs 已存在翻译数: {}", existingTranslationKeys.size());

            // 3. 构建翻译请求，只对未翻译的内容进行翻译
            Map<String, Set<String>> needTranslateMap = new HashMap<>(); // 文本 -> 需要翻译的语言集合
            
            for (SaleAttributeValueVO valueVO : saleAttributeValueVOList) {
                if (valueVO.getId() != null && StringUtils.isNotBlank(valueVO.getSaleAttributeValueName())) {
                    Set<String> needTranslateLangs = new HashSet<>();
                    
                    for (String lang : langList) {
                        String key = valueVO.getId() + Constant.UNDER_LINE + lang;
                        if (!existingTranslationKeys.contains(key)) {
                            needTranslateLangs.add(lang);
                        }
                    }
                    
                    if (CollectionUtils.isNotEmpty(needTranslateLangs)) {
                        needTranslateMap.put(valueVO.getSaleAttributeValueName(), needTranslateLangs);
                    }
                }
            }

            if (MapUtils.isEmpty(needTranslateMap)) {
                log.info("SaleAttributeLangManageServiceImpl.createCrossBorderSaleAttributeValueLangs 所有翻译已存在，无需进行机翻");
                return;
            }

            log.info("SaleAttributeLangManageServiceImpl.createCrossBorderSaleAttributeValueLangs 需要翻译的内容: {}", JSON.toJSONString(needTranslateMap));

            Map<String, Map<String, String>> allTranslateResult = getTranslateResult(needTranslateMap);

            if (MapUtils.isNotEmpty(allTranslateResult)) {
                // 保存翻译结果到数据库
                saleAttributeValueLangAtomicService.saveSaleAttributeValueLangToDB(saleAttributeValueVOList, allTranslateResult);
                log.info("SaleAttributeLangManageServiceImpl.createCrossBorderSaleAttributeValueLangs 跨境品销售属性值翻译成功，保存到数据库，结果: {}", JSON.toJSONString(allTranslateResult));
            }

        } catch (Exception e) {
            log.error("SaleAttributeLangManageServiceImpl.createCrossBorderSaleAttributeValueLangs 异常", e);
        }
    }

    @Nullable
    public Map<String, Map<String, String>> getTranslateResult(Map<String, Set<String>> needTranslateMap) {
        // 如果待翻译文字为“默认”，且只有一个，则返回默认翻译值
        if (needTranslateMap!=null && needTranslateMap.size() == 1 && needTranslateMap.containsKey(Constant.DEFAULT_SALE_ATTRIBUTE_VALUE_NAME)) {
            // 如果进一步待翻译的语言与默认值的语言完全相同才返回默认
            Set<String> needLangSet = needTranslateMap.get(Constant.DEFAULT_SALE_ATTRIBUTE_VALUE_NAME);
            Set<String> defaultLangSet = Constant.DEFAULT_SALE_ATTRIBUTE_VALUE_NAME_LANG_MAP.keySet();
            defaultLangSet.remove(Constant.DEFAULT_LANG);
            needLangSet.remove(Constant.DEFAULT_LANG);
            if (needLangSet!=null && defaultLangSet != null && needLangSet.containsAll(defaultLangSet) && defaultLangSet.containsAll(needLangSet)) {
                Map<String, Map<String, String>> res = new HashMap<>();
                res.put(Constant.DEFAULT_SALE_ATTRIBUTE_VALUE_NAME, Constant.DEFAULT_SALE_ATTRIBUTE_VALUE_NAME_LANG_MAP);
                log.info("SaleAttributeLangManageServiceImpl.getTranslateResult 待翻译文字为默认，且只有一个，返回默认翻译值, needTranslateMap: {}, res: {}", JSON.toJSONString(needTranslateMap), JSON.toJSONString(res));
                return res;
            }
        }
        // 4. 构建翻译请求
        Set<MultiTranslateReqDTO> translateRequests = Sets.newHashSet();
        for (Map.Entry<String, Set<String>> entry : needTranslateMap.entrySet()) {
            String text = entry.getKey();
            Set<String> toLangs = entry.getValue();
            toLangs.remove(LangConstant.LANG_ZH);

            if (StringUtils.isNotBlank(text) && CollectionUtils.isNotEmpty(toLangs)) {
                MultiTranslateReqDTO translateReq = new MultiTranslateReqDTO();
                translateReq.setText(text);
                translateReq.setFrom(LangConstant.LANG_ZH);
                translateReq.setToLangList(new ArrayList<>(toLangs));
                translateRequests.add(translateReq);
            }
        }

        if (CollectionUtils.isEmpty(translateRequests)) {
            log.warn("SaleAttributeLangManageServiceImpl.createCrossBorderSaleAttributeValueLangs 没有需要翻译的销售属性值");
            return null;
        }

        // 5. 调用翻译服务
        BatchMultiTranslateReqDTO batchRequest = new BatchMultiTranslateReqDTO();
        // 每次处理10个
        List<List<MultiTranslateReqDTO>> subList = Lists.partition(new ArrayList<>(translateRequests), 10);
        Map<String, Map<String, String>> allTranslateResult = new HashMap<>();
        for(List<MultiTranslateReqDTO> req : subList) {
            batchRequest.setMultiTranslateReqDTOSet(new HashSet<>(req));

            DataResponse<Map<String, Map<String, String>>> translateResponse =
                    textTranslateManageService.batchTranslateMultiLangText(batchRequest);

            if (translateResponse.getSuccess() && MapUtils.isNotEmpty(translateResponse.getData())) {
                Map<String, Map<String, String>> translateResult = translateResponse.getData();
                // 添加中文值
                this.addChinese(translateResult);
                allTranslateResult.putAll(translateResult);
                log.info("SaleAttributeLangManageServiceImpl.createCrossBorderSaleAttributeValueLangs 跨境品销售属性值翻译成功，结果: {}", JSON.toJSONString(translateResult));
            } else {
                log.warn("SaleAttributeLangManageServiceImpl.createCrossBorderSaleAttributeValueLangs 跨境品销售属性值翻译失败: {}, 请求: {}", translateResponse.getMessage(), JSON.toJSONString(batchRequest));
            }
        }
        return allTranslateResult;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public void updateCrossBorderSaleAttributeValueLangs(List<SaleAttributeValueLangVO> saleAttributeValueLangVOList) {
        log.info("SaleAttributeLangManageServiceImpl.updateCrossBorderSaleAttributeValueLangs 入参: saleAttributeValueLangVOList数量={}", 
                CollectionUtils.isEmpty(saleAttributeValueLangVOList) ? 0 : saleAttributeValueLangVOList.size());

        if (CollectionUtils.isEmpty(saleAttributeValueLangVOList)) {
            log.warn("SaleAttributeLangManageServiceImpl.updateCrossBorderSaleAttributeValueLangs 参数为空");
            return;
        }

        // 过滤掉中文
        saleAttributeValueLangVOList = saleAttributeValueLangVOList.stream()
                .filter(langVO ->langVO.getLang() != null && !LangConstant.LANG_ZH.equals(langVO.getLang()))
                .collect(Collectors.toList());
                
        if(CollectionUtils.isEmpty(saleAttributeValueLangVOList)){
            log.warn("SaleAttributeLangManageServiceImpl.updateCrossBorderSaleAttributeValueLangs 过滤掉中文后，参数为空");
            return;
        }

        // 1. 收集所有有效的输入数据和检查key
        List<String> checkPairs = new ArrayList<>();
        Map<String, SaleAttributeValueLangVO> validInputMap = new HashMap<>(); // key: "valueId_lang", value: 输入的VO
        
        for (SaleAttributeValueLangVO langVO : saleAttributeValueLangVOList) {
            if (langVO.getSaleAttributeValueId() != null &&
                    StringUtils.isNotBlank(langVO.getLang()) &&
                    StringUtils.isNotBlank(langVO.getLangName())) {
                
                String checkKey = langVO.getSaleAttributeValueId() + Constant.UNDER_LINE + langVO.getLang();
                checkPairs.add(checkKey);
                validInputMap.put(checkKey, langVO);
            }
        }

        if (CollectionUtils.isEmpty(checkPairs)) {
            log.warn("SaleAttributeLangManageServiceImpl.updateCrossBorderSaleAttributeValueLangs 没有有效的输入数据");
            return;
        }

        // 2. 批量检查已存在的数据
        Map<String, SaleAttributeValueLangPO> existingMap = saleAttributeValueLangAtomicService
                .getValidBySaleAttributeValueIdAndLangBatch(checkPairs);
        log.info("SaleAttributeLangManageServiceImpl.updateCrossBorderSaleAttributeValueLangs 批量检查完成, 已存在数量: {}", existingMap.size());

        // 3. 构建待保存的数据
        List<SaleAttributeValueLangPO> langPOList = new ArrayList<>();

        for (String checkKey : checkPairs) {
            SaleAttributeValueLangVO langVO = validInputMap.get(checkKey);
            if (langVO == null) {
                continue;
            }

            SaleAttributeValueLangPO existingPO = existingMap.get(checkKey);
            SaleAttributeValueLangPO langPO = new SaleAttributeValueLangPO();
            
            // 只有存在且名称不一致时才更新
            if (existingPO != null && StringUtils.isNotBlank(langVO.getLangName()) && !langVO.getLangName().equals(existingPO.getLangName())) {
                // 修改已存在的记录
                langPO.setId(existingPO.getId());
                langPO.setLangName(langVO.getLangName());
                langPO.setUpdateTime(new Date().getTime());
                // 如果登录上下文为空，则设置更新者为系统
                if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                    langPO.setUpdater(Constant.PIN_SYSTEM);
                }
                langPOList.add(langPO);
            } 
        }

        if (CollectionUtils.isNotEmpty(langPOList)) {
            saleAttributeValueLangAtomicService.updateBatchById(langPOList);
            log.info("SaleAttributeLangManageServiceImpl.updateCrossBorderSaleAttributeValueLangs 更新销售属性值多语言成功，数量: {}", langPOList.size());
        } else {
            log.info("SaleAttributeLangManageServiceImpl.updateCrossBorderSaleAttributeValueLangs 没有需要保存的数据");
        }
    }

    @Override
    public Map<String, Map<String, String>> getSaleAttributeNameLangsByCodeList(List<String> saleAttributeCodeList, List<String> langList) {
        log.info("SaleAttributeLangManageServiceImpl.getSaleAttributeNameLangsByCodeList 入参: saleAttributeCodeList={}, langList={}", 
                JSON.toJSONString(saleAttributeCodeList), JSON.toJSONString(langList));

        if (CollectionUtils.isEmpty(saleAttributeCodeList) || CollectionUtils.isEmpty(langList)) {
            log.warn("getSaleAttributeNameLangsByCodeList 参数为空");
            return new HashMap<>();
        }

        Map<String, Map<String, String>> result = new HashMap<>();

        try {
            // 批量查询所有相关的销售属性多语言记录
            List<SaleAttributeLangPO> allLangPOList = saleAttributeLangAtomicService.getValidBySaleAttributeCodeList(saleAttributeCodeList);
            log.info("getSaleAttributeNameLangsByCodeList 批量查询销售属性多语言记录，查询条件数量: {}, 查询结果数量: {}", 
                    saleAttributeCodeList.size(), allLangPOList != null ? allLangPOList.size() : 0);
            
            // 按销售属性编码分组
            Map<String, List<SaleAttributeLangPO>> langPOMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(allLangPOList)) {
                langPOMap = allLangPOList.stream()
                        .collect(Collectors.groupingBy(SaleAttributeLangPO::getSaleAttributeCode));
            }
            
            // 为每个销售属性编码获取对应的多语言名称
            for (String saleAttributeCode : saleAttributeCodeList) {
                List<SaleAttributeLangPO> langPOList = langPOMap.getOrDefault(saleAttributeCode, new ArrayList<>());
                
                Map<String, String> langNameMap = new HashMap<>();
                
                if (CollectionUtils.isNotEmpty(langPOList)) {
                    // 按语言分组
                    Map<String, SaleAttributeLangPO> langMap = langPOList.stream()
                            .collect(Collectors.toMap(
                                SaleAttributeLangPO::getLang, 
                                po -> po, 
                                (existing, replacement) -> existing
                            ));
                    
                    // 为每个请求的语言获取翻译
                    for (String lang : langList) {
                        if (langMap.containsKey(lang)) {
                            String langName = langMap.get(lang).getLangName();
                            if (StringUtils.isNotBlank(langName)) {
                                langNameMap.put(lang, langName);
                            }
                        }
                    }
                }
                
                // 如果没有找到任何翻译，或者缺少某些语言的翻译，使用属性编码作为兜底
                for (String lang : langList) {
                    if (!langNameMap.containsKey(lang)) {
                        langNameMap.put(lang, saleAttributeCode);
                    }
                }
                
                result.put(saleAttributeCode, langNameMap);
            }

            log.info("getSaleAttributeNameLangsByCodeList 返回结果: {}", JSON.toJSONString(result));
            return result;

        } catch (Exception e) {
            log.error("getSaleAttributeNameLangsByCodeList 异常", e);
            // 异常情况下返回兜底数据
            for (String saleAttributeCode : saleAttributeCodeList) {
                Map<String, String> fallbackMap = new HashMap<>();
                for (String lang : langList) {
                    fallbackMap.put(lang, saleAttributeCode);
                }
                result.put(saleAttributeCode, fallbackMap);
            }
            return result;
        }
    }
} 