package com.jdi.isc.product.soa.service.manage.price.impl.tax;


import com.alibaba.fastjson.JSONObject;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.isc.product.soa.api.common.enums.SupplierCountryTypeEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.domain.enums.spu.SpuTaxCountryEnums;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceResVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCalculatePriceReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCalculateTaxPriceVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.supplier.biz.SupplierBaseInfoVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CategoryTaxVO;
import com.jdi.isc.product.soa.service.manage.supplier.SupplierBaseInfoManageService;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

import static com.jdi.isc.product.soa.common.constants.Constant.CNSJGY;

/**
 * <AUTHOR>
 * @description：匈牙利税率计算
 * @Date 2024-12-13
 */
@Slf4j
@Service
public class HuSkuTaxPriceManageService extends AbstractSkuTaxPriceManageService{

    @LafValue("jdi.isc.countryType.vat")
    protected String vatTaxForCountryType;

    @Resource
    private SupplierBaseInfoManageService supplierBaseInfoManageService;

    @Override
    public SkuCalculateTaxPriceVO skuTaxPrice(SkuCalculatePriceReqVO input){
        log.info("HuSkuTaxPriceManageService.skuTaxPrice input:{}", JSONObject.toJSONString(input));
        this.validateParam(input);
        this.initVal(input);
        this.handleCategoryTax(input);
        String sourceCountryCode = input.getSourceCountryCode();
        String currencyCode = null;
        if(StringUtils.isNotBlank(input.getSupplierCode())){
            currencyCode = supplierSettlementAccountAtomicService.getCurrencyBySupplierCode(input.getSupplierCode());
        }else if(Objects.nonNull(input.getSkuId()) ){
            SkuPO sku = skuAtomicService.getSkuPoBySkuId(input.getSkuId());
            currencyCode = supplierSettlementAccountAtomicService.getCurrencyBySupplierCode(sku.getVendorCode());
        }
        //默认增值税
        CategoryTaxVO categoryTaxVO = input.getCategoryTaxVO();
        BigDecimal categoryDestinationTax = categoryTaxVO != null?categoryTaxVO.getDestinationVatRate():null;

        BigDecimal valueAddTax = this.getFinalTaxRate(input.getTaxRate(),this.skuTaxRate(input.getSkuId()),categoryTaxVO != null?categoryTaxVO.getVatRate():null,sourceCountryCode);
        BigDecimal destinationVatRate = this.getValueAddTax(input.getTaxRate()==null?(categoryTaxVO != null?categoryDestinationTax:null):valueAddTax,sourceCountryCode);
        SkuPriceResVO skuPriceResVO = this.getSkuPrice(input.getSkuId(),input.getSkuPriceFlag(),sourceCountryCode,currencyCode);

        // 各种计算
        SkuCalculateTaxPriceVO skuCalculateTaxPriceVO = new SkuCalculateTaxPriceVO();
        skuCalculateTaxPriceVO.setCatId(input.getCatId());
        skuCalculateTaxPriceVO.setSourceCountryCode(sourceCountryCode);
        skuCalculateTaxPriceVO.setTaxRate(valueAddTax);
        skuCalculateTaxPriceVO.setDestinationVatRate(destinationVatRate);
        skuCalculateTaxPriceVO.setCurrency(currencyCode);
        if(skuPriceResVO != null){
            input.setPurchasePrice(skuPriceResVO.getPurchasePrice());
            input.setTaxPurchasePrice(skuPriceResVO.getTaxPurchasePrice());
            input.setSalePrice(skuPriceResVO.getSalePrice());
            input.setTaxSalePrice(skuPriceResVO.getTaxSalePrice());
        }
        skuCalculateTaxPriceVO.setPurchasePrice(super.calculatePurchase(input.getPurchasePrice(),valueAddTax,input.getTaxPurchasePrice()));
        skuCalculateTaxPriceVO.setTaxPurchasePrice(this.calculateTaxPurchase(input.getTaxPurchasePrice(),valueAddTax,input.getPurchasePrice()));
        skuCalculateTaxPriceVO.setSalePrice(super.calculateSale(input.getSalePrice(),valueAddTax,input.getTaxSalePrice()));
        skuCalculateTaxPriceVO.setTaxSalePrice(super.calculateTaxSale(input.getTaxSalePrice(),destinationVatRate,input.getSalePrice()));
        skuCalculateTaxPriceVO.setNoTaxGrossRate(super.calculateNoTaxGross(skuCalculateTaxPriceVO.getPurchasePrice(),skuCalculateTaxPriceVO.getSalePrice(),valueAddTax));
        skuCalculateTaxPriceVO.setTaxGrossRate(super.calculateTaxGross(skuCalculateTaxPriceVO.getPurchasePrice(),skuCalculateTaxPriceVO.getSalePrice()));
        skuCalculateTaxPriceVO.setMakeUpRate(super.getMakeUpRate(sourceCountryCode));
        // 设置国家成本价、国家协议价、利润率
        this.setCountryAgreementPriceAndRate(input, skuCalculateTaxPriceVO);
        return skuCalculateTaxPriceVO;
    }

    /**
     * 采购价 计算含税采购价
     * @param purchasePrice 采购价。
     * @param valueAddTax 增值税率。
     * @return 含税采购价。
     */
    protected BigDecimal calculateTaxPurchase(BigDecimal taxPurchasePrice, BigDecimal valueAddTax,BigDecimal purchasePrice){
        if(taxPurchasePrice != null){
            log.info("HuSkuTaxPriceManageService.calculateTaxPurchase not need calculate");
            return taxPurchasePrice;
        }
        if(purchasePrice == null || valueAddTax == null){
            log.error("HuSkuTaxPriceManageService.calculateTaxPurchase param is null,purchasePrice:{},valueAddTax:{}"
                    , JSONObject.toJSONString(purchasePrice),JSONObject.toJSONString(valueAddTax));
            return null;
        }
        BigDecimal taxPrice = purchasePrice.multiply(valueAddTax).setScale(priceScale, RoundingMode.HALF_UP);
        return purchasePrice.add(taxPrice).setScale(priceScale, RoundingMode.HALF_UP);
    }

    /**
     * 处理商品类目税率，根据供应商国家类型获取对应的税率信息。
     * @param input SkuCalculatePriceReqVO对象，包含商品的SKU ID、类目ID、源国家代码等信息。
     */
    private void handleCategoryTax(SkuCalculatePriceReqVO input) {
        String supplierCode = null;
        if (Objects.isNull(input.getSkuId())) {
            supplierCode = input.getSupplierCode();
        } else {
            SkuPO skuPo = skuAtomicService.getSkuPoBySkuId(input.getSkuId());
            supplierCode = skuPo.getVendorCode();
        }
        CategoryTaxVO categoryTaxVO = null;
        if (!CNSJGY.equals(supplierCode)) {
            AssertValidation.isBlank(supplierCode, "供应商简码不能为空");
            // 查询供应商“国家类型”，类型是“当地”时，首先查询类目税率，类目税率不存在时，使用默认税率27%
            SupplierBaseInfoVO baseInfoVo = supplierBaseInfoManageService.getByCode(supplierCode);
            if (Objects.isNull(baseInfoVo)) {
                throw new BizException(String.format("供应商简码 %s 不存在", supplierCode));
            }

            if (Objects.nonNull(baseInfoVo.getCountryType()) && SupplierCountryTypeEnum.LOCAL.getType() == baseInfoVo.getCountryType()) {
                categoryTaxVO = input.getCategoryTaxVO() == null
                        ? spuTaxManageService.getCatTaxVO(input.getCatId(), input.getSkuId(), input.getSourceCountryCode(), null) : input.getCategoryTaxVO();

                if (Objects.isNull(categoryTaxVO)) {
                    BigDecimal valueAddTax = super.getValueAddTax(null, input.getSourceCountryCode());
                    categoryTaxVO = new CategoryTaxVO();
                    categoryTaxVO.setVatRate(valueAddTax);
                    categoryTaxVO.setDestinationVatRate(valueAddTax);
                }
            } else {
                // 非当地国家时，税率默认返回0%
                categoryTaxVO = new CategoryTaxVO();
                BigDecimal vatTax = ConfigUtils.getValueByKey(vatTaxForCountryType, input.getSourceCountryCode(), BigDecimal.class);
                categoryTaxVO.setVatRate(vatTax);
                categoryTaxVO.setDestinationVatRate(vatTax);
            }
        }
        input.setCategoryTaxVO(categoryTaxVO);
    }

    @Override
    void validateParam(SkuCalculatePriceReqVO priceReqVO) {

    }

    @Override
    void initVal(SkuCalculatePriceReqVO priceReqVO) {
        priceScale = 4;
        spuTaxManageService = spuTaxManageServiceMap.get(SpuTaxCountryEnums.getEnumByCountryCode(priceReqVO.getSourceCountryCode()).getServiceName());
    }
}
