package com.jdi.isc.product.soa.service.manage.pricewarn.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.pricewarn.dto.PriceWarnMqDTO;
import com.jdi.isc.product.soa.service.manage.pricewarn.PriceWarnProducer;
import com.jdi.isc.product.soa.service.manage.pricewarn.factory.PriceWarnBeanFactory;
import com.jdi.isc.product.soa.service.manage.pricewarn.manager.PriceWarnManagerService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * The type Price warn manager service.
 *
 * <AUTHOR>
 */
@Component
public class PriceWarnManagerServiceImpl implements PriceWarnManagerService {

    @Resource
    private PriceWarnProducer priceWarnProducer;

    @Override
    public DataResponse<String> writePriceWarn(PriceWarnMqDTO input) {
        if (input == null || input.getWarnType() == null) {
            return DataResponse.error("参数错误");
        }
        return PriceWarnBeanFactory.select(input.getWarnType()).execute(input);
    }

    @Override
    public void sendMessage(Long bizId, Integer warnType, Integer dataSourceType) {
        PriceWarnMqDTO warn = new PriceWarnMqDTO(warnType, bizId, dataSourceType);

        priceWarnProducer.sendMessage(warn);
    }
}
