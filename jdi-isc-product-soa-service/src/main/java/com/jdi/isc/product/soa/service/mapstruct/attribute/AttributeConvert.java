package com.jdi.isc.product.soa.service.mapstruct.attribute;

import com.jdi.isc.product.soa.api.attribute.common.AttributeDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeLangDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeValueDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeValueLangDTO;
import com.jdi.isc.product.soa.api.attribute.res.AttributeFlatDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyValueApiDTO;
import com.jdi.isc.product.soa.domain.attribute.biz.*;
import com.jdi.isc.product.soa.domain.attribute.po.AttributePO;
import com.jdi.isc.product.soa.domain.category.biz.GlobalAttributeResVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 属性对象转换
 * <AUTHOR>
 * @date 20231124
 */
@Mapper
public interface AttributeConvert {

    AttributeConvert INSTANCE = Mappers.getMapper(AttributeConvert.class);

    @InheritConfiguration
    AttributePO dto2po(AttributeVO attributeDTO);

    @InheritConfiguration
    AttributeVO po2dto(AttributePO attributePO);

    @InheritConfiguration
    List<AttributeVO> listPo2dto(List<AttributePO> attributePOS);

//    @Mapping(target = "attributeValueList",ignore = true)
//    AttributeReadResp flatVo2AttributeRespDTO(AttributeFlatVO attributeFlatVO);

    @Mapping(target = "countryCodeList",expression = "java(countryList2String(attributeVO.getCountryList()))")
    AttributeDTO attributeVO2DTO(AttributeVO attributeVO);

    List<AttributeDTO> listAttributeVo2DTO(List<AttributeVO>  attributeVOList);

    List<PropertyApiDTO> listPropertyVo2Dto(List<PropertyVO> inputs);

    List<PropertyValueApiDTO> listPropertyValueVo2Dto(List<PropertyValueVO> inputs);

    List<AttributeLangDTO> listAttributeLangVo2DTO(List<AttributeLangVO> attributeLangVOList);

    AttributeValueDTO attributeValueVo2DTO(AttributeValueVO input);

    List<AttributeValueDTO> listAttributeValueVo2DTO(List<AttributeValueVO> attributeValueVOList);

    List<AttributeValueLangDTO> listValueLangVO2DTO(List<AttributeValueLangVO>  attributeValueLangVOList);

    default List<String> countryList2String(List<AttributeCountryVO> countryList) {
        if (CollectionUtils.isEmpty(countryList)) {
            return null;
        }

        return countryList.stream().map(AttributeCountryVO::getCountryCode).collect(Collectors.toList());
    }

    List<GlobalAttributeResVO> listPropertyVo2GlobalResVO(List<PropertyVO> propertyVOList);

    @Mapping(source = "propertyValueVOList",target = "attributeValueList")
    GlobalAttributeResVO propertyVo2GlobalResVo(PropertyVO propertyVO);


    List<AttributeFlatDTO> flatVo2DTOList(List<AttributeFlatVO> flatVOS);

    AttributeFlatVO attributeVo2FlatVo(AttributeVO attributeVO);

    /**
     * PropertyVO转换为AttributeFlatVO
     * @param propertyVO 属性VO对象
     * @return 属性扁平化VO对象
     */
    default AttributeFlatVO salePropertyVo2FlatVo(PropertyVO propertyVO) {
        AttributeFlatVO attributeFlatVO = new AttributeFlatVO();
        attributeFlatVO.setId(propertyVO.getAttributeId());
        attributeFlatVO.setAttributeName(propertyVO.getAttributeName());
        attributeFlatVO.setSort(propertyVO.getSort());
        attributeFlatVO.setAttributeInputType(propertyVO.getAttributeInputType());
        attributeFlatVO.setAttributeType(propertyVO.getAttributeType());
        return attributeFlatVO;
    }

    /**
     * Date转换为Long时间戳
     * @param value Date对象
     * @return Long时间戳，如果Date为null则返回null
     */
    default Long map(Date value) {
        return value != null ? value.getTime() : null;
    }

    /**
     * Long时间戳转换为Date
     * @param value Long时间戳
     * @return Date对象，如果Long为null则返回null
     */
    default Date map(Long value) {
        return value != null ? new Date(value) : null;
    }
}
