package com.jdi.isc.product.soa.service.manage.taxRate.countryTax.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.MySkuTaxPO;
import com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum;
import com.jdi.isc.product.soa.price.api.price.req.MySkuTaxVO;
import com.jdi.isc.product.soa.service.adapter.mapstruct.countryTax.MySkuTaxConvert;
import com.jdi.isc.product.soa.service.atomic.taxRate.countryTax.MySkuTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.MySkuTaxManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 马来税率管理服务
 * <AUTHOR>
 * @date 2025/3/10
 */
@Slf4j
@Service
public class MySkuTaxManageServiceImpl extends BaseTaxManageService implements MySkuTaxManageService {

    @Resource
    private MySkuTaxAtomicService mySkuTaxAtomicService;

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> saveOrUpdate(MySkuTaxVO input) {
        boolean flag;
        //将关税除以100保存
        input.setMfnTax(input.getMfnTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setOriginMfnTax(input.getOriginMfnTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setSaleTax(input.getSaleTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setAntiDumpingTax(input.getAntiDumpingTax().divide(factor,5, RoundingMode.HALF_UP));
        JdProductDTO skuPO = getSku(input.getJdSkuId());
        if(skuPO==null){
            return DataResponse.error(String.format("零售skuId:%s 不存在,请检查",input.getJdSkuId()));
        }
        MySkuTaxPO res = mySkuTaxAtomicService.getOne(input);
        if(res==null){
            MySkuTaxPO target = MySkuTaxConvert.INSTANCE.vo2Po(input);
            target.setCreateTime(new Date());
            target.setUpdateTime(target.getCreateTime());
            flag = mySkuTaxAtomicService.save(target);
            recordLog(target.getJdSkuId(), PriceTypeEnum.MY_TAX, target.getMfnTax(),target,flag);
        }else {
            input.setId(res.getId());
            flag = mySkuTaxAtomicService.updateTax(input);
            recordLog(input.getJdSkuId(), PriceTypeEnum.MY_TAX, input.getMfnTax(),input,flag);
        }
        return DataResponse.success(flag);
    }

}
