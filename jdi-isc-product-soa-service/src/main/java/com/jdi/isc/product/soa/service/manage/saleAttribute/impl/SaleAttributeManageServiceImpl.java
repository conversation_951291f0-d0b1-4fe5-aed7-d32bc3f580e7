package com.jdi.isc.product.soa.service.manage.saleAttribute.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Sets;
import com.jd.gms.greatdane.category.domain.CategorySaleAtt;
import com.jd.gms.greatdane.category.domain.SaleAttribute;
import com.jd.gms.greatdane.category.request.GetCategorySaleAttByCategoryIdsParam;
import com.jd.gms.greatdane.category.service.read.CategorySaleAttReadService;
import com.jd.gms.greatdane.response.GreatDaneResult;
import com.jdi.isc.product.soa.api.attribute.common.AttributeDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeValueDTO;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.SaleAttributeTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.frame.SaleAttributeUNhandleContextHolder;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeLangVO;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeVO;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeValueLangVO;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeValueVO;
import com.jdi.isc.product.soa.domain.common.biz.BaseLangVO;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.CategorySaleAttributeVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.JdSkuSaleAttributeVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeValueLangVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SpuSaleAttributeVO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributePO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeValueLangPO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeValuePO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SkuSaleAttributeValueRelationPO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.rpc.gms.BaseGmsClientService;
import com.jdi.isc.product.soa.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SaleAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SaleAttributeValueAtomicService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SaleAttributeValueLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SkuSaleAttributeValueRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDraftAtomicService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeLangManageService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeManageService;
import com.jdi.isc.product.soa.service.mapstruct.attribute.AttributeConvert;
import com.jdi.isc.product.soa.service.mapstruct.saleAttribute.SaleAttributeConvert;
import com.jdi.isc.product.soa.service.mapstruct.saleAttribute.SaleAttributeConvertService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售属性管理服务实现
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
public class SaleAttributeManageServiceImpl implements SaleAttributeManageService {

    @Autowired
    private SaleAttributeAtomicService saleAttributeAtomicService;

    @Autowired
    private SaleAttributeValueAtomicService saleAttributeValueAtomicService;

    @Autowired
    private SkuSaleAttributeValueRelationAtomicService skuSaleAttributeValueRelationAtomicService;

    @Autowired
    private SaleAttributeValueLangAtomicService saleAttributeValueLangAtomicService;

    @Resource
    private SkuInfoRpcService skuInfoRpcService;

    @Resource
    private SaleAttributeConvertService saleAttributeConvertService;

    @Resource
    private CategorySaleAttReadService categorySaleAttReadService;

    @Resource
    private BaseGmsClientService baseGmsClientService;

    @Resource
    private SaleAttributeLangManageService saleAttributeLangManageService;

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @Resource
    private LangManageService langManageService;

    @Autowired
    private Environment env;

    @Resource
    private SkuDraftAtomicService skuDraftAtomicService;

    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;

    public String getCurrentProfile() {
        return env.getProperty("spring.profiles.active");
    }

    /**
     * 校验销售属性的唯一性
     * 同一个jdCatId下只能有一个图销和一个文销name
     */
    private boolean validateSaleAttributeUniqueness(SaleAttributePO saleAttributePO) {
        try {


            SaleAttributeTypeEnum typeEnum = SaleAttributeTypeEnum.forCode(saleAttributePO.getSaleAttributeType());
            if (typeEnum == null) {
                log.warn("SaleAttributeManageServiceImpl.validateSaleAttributeUniqueness 无效的销售属性类型: {}",
                        saleAttributePO.getSaleAttributeType());
                return false;
            }

            List<SaleAttributePO> existingAttributes = saleAttributeAtomicService.getValidByJdCatIdAndType(
                    saleAttributePO.getJdCatId(), saleAttributePO.getSaleAttributeType());

            if (CollectionUtils.isEmpty(existingAttributes)) {
                return true;
            }

            // 如果是更新操作，排除自身
            if (saleAttributePO.getId() != null) {
                existingAttributes = existingAttributes.stream()
                        .filter(attr -> !attr.getId().equals(saleAttributePO.getId()))
                        .collect(Collectors.toList());
            }

            // 检查是否已存在相同类型的销售属性名
            return existingAttributes.stream()
                    .noneMatch(attr -> attr.getSaleAttributeName().equals(saleAttributePO.getSaleAttributeName()));

        } catch (Exception e) {
            log.error("校验销售属性唯一性失败", e);
            throw e;
        }
    }

    @Override
    public SpuSaleAttributeVO getSpuSaleAttributeDetail(Long spuId, String lang) {
        log.info("SaleAttributeManageServiceImpl.getSpuSaleAttributeDetail spuId: {}, lang: {}", spuId, lang);

        if (spuId == null || StringUtils.isBlank(lang)) {
            log.warn("SaleAttributeManageServiceImpl.getSpuSaleAttributeDetail spuId或lang为空");
            return null;
        }

        try {
            // 1. 通过spuId获取全部销售属性值信息
            List<SaleAttributeValuePO> spuSaleAttributeValuePOList = saleAttributeValueAtomicService.getSaleAttributeValueBySpuId(spuId);
            if (CollectionUtils.isEmpty(spuSaleAttributeValuePOList)) {
                log.info("SaleAttributeManageServiceImpl.getSpuSaleAttributeDetail SPU下没有销售属性值数据, spuId: {}", spuId);
                return new SpuSaleAttributeVO(new ArrayList<>(), new HashMap<>(), new HashMap<>());
            }

            // 提取全部销售属性值ID
            Set<Long> spuSaleAttributeValueIds = spuSaleAttributeValuePOList.stream()
                    .filter(Objects::nonNull)
                    .filter(e -> e.getId() != null)
                    .map(SaleAttributeValuePO::getId)
                    .collect(Collectors.toSet());

            log.info("SaleAttributeManageServiceImpl.getSpuSaleAttributeDetail 找到销售属性值数量: {}", spuSaleAttributeValueIds.size());

            // 2. 通过SKU读取SKU的销售属性值关系（批量查询模式）
            Map<Long, List<SaleAttributeValuePO>> skuSaleAttributeValuesMap = new HashMap<>();
            Map<String, List<SaleAttributeValuePO>> draftSkuSaleAttributeValuesMap = new HashMap<>();
            Set<Long> allSaleAttributeIds = new HashSet<>();

            // 批量查询SPU下全部销售属性值的关系
            List<SkuSaleAttributeValueRelationPO> allRelations = skuSaleAttributeValueRelationAtomicService.getValidBySaleAttributeValueIds(spuSaleAttributeValueIds);
            log.info("SaleAttributeManageServiceImpl.getSpuSaleAttributeDetail 批量查询SKU销售属性值关系, 总数: {}", allRelations.size());

            if (!CollectionUtils.isEmpty(allRelations)) {
                // 创建销售属性值ID到PO的映射（修复：使用正确的变量名）
                Map<Long, SaleAttributeValuePO> valueIdToPOMap = spuSaleAttributeValuePOList.stream()
                        .filter(Objects::nonNull)
                        .filter(e -> e.getId() != null)
                        .collect(Collectors.toMap(SaleAttributeValuePO::getId, po -> po, (existing, replacement) -> existing));

                // 分别处理有skuId和有skuKey的关系数据
                for (SkuSaleAttributeValueRelationPO relation : allRelations) {
                    if (relation == null || relation.getSaleAttributeValueId() == null) {
                        continue;
                    }

                    SaleAttributeValuePO saleAttributeValue = valueIdToPOMap.get(relation.getSaleAttributeValueId());
                    if (saleAttributeValue == null) {
                        log.warn("SaleAttributeManageServiceImpl.getSpuSaleAttributeDetail 找不到销售属性值, valueId: {}", relation.getSaleAttributeValueId());
                        continue;
                    }

                    // 根据skuId是否为空来分别处理
                    if (relation.getSkuId() != null) {
                        // skuId不为空，存储到skuSaleAttributeValuesMap（正式SKU）
                        skuSaleAttributeValuesMap.computeIfAbsent(relation.getSkuId(), k -> new ArrayList<>()).add(saleAttributeValue);
                    } else if (StringUtils.isNotBlank(relation.getSkuKey())) {
                        // skuId为空但skuKey不为空，存储到draftSkuSaleAttributeValuesMap（草稿SKU）
                        draftSkuSaleAttributeValuesMap.computeIfAbsent(relation.getSkuKey(), k -> new ArrayList<>()).add(saleAttributeValue);
                    }

                    // 收集所有销售属性ID
                    if (saleAttributeValue.getSaleAttributeId() != null) {
                        allSaleAttributeIds.add(saleAttributeValue.getSaleAttributeId());
                    }
                }

                log.info("SaleAttributeManageServiceImpl.getSpuSaleAttributeDetail 批量处理完成, 销售属性值总数: {}, 销售属性总数: {}, 正式SKU数量: {}, 草稿SKU数量: {}",
                        spuSaleAttributeValueIds.size(), allSaleAttributeIds.size(), skuSaleAttributeValuesMap.size(), draftSkuSaleAttributeValuesMap.size());
            } else {
                log.warn("SaleAttributeManageServiceImpl.getSpuSaleAttributeDetail 没有找到SKU销售属性值关系数据, spuId: {}", spuId);
                return new SpuSaleAttributeVO(new ArrayList<>(), new HashMap<>(), new HashMap<>());
            }

            // 3. 获取销售属性值的多语言信息
            Map<Long, List<SaleAttributeValueLangVO>> saleAttributeValueLangsMap = new HashMap<>();
            if (!spuSaleAttributeValueIds.isEmpty()) {
                saleAttributeValueLangsMap = saleAttributeLangManageService.getSaleAttributeValueNameLangsByIds(new ArrayList<>(spuSaleAttributeValueIds));
                log.info("SaleAttributeManageServiceImpl.getSpuSaleAttributeDetail 获取销售属性值多语言信息, 数量: {}", saleAttributeValueLangsMap.size());
            }

            // 4. 获取销售属性及其对应语言的名称列表
            List<PropertyVO> spuSalePropertyList = getSaleAttributeListByIdList(allSaleAttributeIds, lang);
            log.info("SaleAttributeManageServiceImpl.getSpuSaleAttributeDetail 获取销售属性列表, 数量: {}", spuSalePropertyList.size());
            if(CollectionUtils.isEmpty(spuSalePropertyList)){
                log.warn("SaleAttributeManageServiceImpl.getSpuSaleAttributeDetail 获取销售属性列表为空, spuId: {}", spuId);
                return new SpuSaleAttributeVO(new ArrayList<>(), new HashMap<>(), new HashMap<>());
            }

            // 5. 为每个销售属性添加属性值( 这里添加的是全部的销售属性值，包括未被选中的 )
            Map<Long, List<SaleAttributeValuePO>> attributeValuesMap = new HashMap<>();
            // 合并所有sku和草稿sku的销售属性值
            List<SaleAttributeValuePO> allSkuSaleAttributeValues = new ArrayList<>();
            allSkuSaleAttributeValues.addAll(skuSaleAttributeValuesMap.values().stream().filter(Objects::nonNull)
                    .flatMap(List::stream).collect(Collectors.toList()));
            allSkuSaleAttributeValues.addAll(draftSkuSaleAttributeValuesMap.values().stream().filter(Objects::nonNull)
                    .flatMap(List::stream).collect(Collectors.toList()));

            for (SaleAttributeValuePO value : allSkuSaleAttributeValues) {
                attributeValuesMap.computeIfAbsent(value.getSaleAttributeId(), k -> new ArrayList<>()).add(value);
            }

            for (PropertyVO propertyVO : spuSalePropertyList) {
                List<SaleAttributeValuePO> values = attributeValuesMap.getOrDefault(propertyVO.getAttributeId(), new ArrayList<>());
                List<PropertyValueVO> propertyValueVOList = saleAttributeConvertService.convertToPropertyValueVOList(
                        values, saleAttributeValueLangsMap, lang, true);
                // 需要对propertyValueVOList中相同attributeValueId去重
                if(CollectionUtils.isNotEmpty(propertyValueVOList)){
                propertyValueVOList = propertyValueVOList.stream().filter(Objects::nonNull).filter(e->e.getAttributeValueId()!=null)
                        .collect(Collectors.groupingBy(PropertyValueVO::getAttributeValueId))
                        .values()
                        .stream().filter(Objects::nonNull)
                            .map(list -> list.get(0))
                            .collect(Collectors.toList());
                    try {
                        propertyValueVOList.sort(Comparator.comparing(PropertyValueVO::getSort));
                    } catch (Exception e) {
                        log.error("SaleAttributeManageServiceImpl.getSpuSaleAttributeDetail 排序销售属性值失败, spuId: {}", spuId, e);
                    }
                    propertyVO.setPropertyValueVOList(propertyValueVOList);
                }
            }
            // 获取spuSalePropertyid与sort的map
            Map<Long, Integer> spuSalePropertySortMap = spuSalePropertyList.stream()
                    .filter(Objects::nonNull)
                    .filter(e -> e.getAttributeId() != null)
                    .collect(Collectors.toMap(PropertyVO::getAttributeId, PropertyVO::getSort));
            // 6. 构建SKU销售属性值映射（这里添加的是sku的选中销售属性）
            Map<Long, List<PropertyValueVO>> skuSalePropertyListMap = new HashMap<>();
            for (Map.Entry<Long, List<SaleAttributeValuePO>> entry : skuSaleAttributeValuesMap.entrySet()) {
                Long skuId = entry.getKey();
                List<SaleAttributeValuePO> values = entry.getValue();
                List<PropertyValueVO> propertyValueVOList = saleAttributeConvertService.convertToPropertyValueVOList(
                        values, saleAttributeValueLangsMap, lang, false);
                this.sortPropertyValueList(propertyValueVOList, spuSalePropertySortMap);
                skuSalePropertyListMap.put(skuId, propertyValueVOList);
            }

            // 7. 构建草稿SKU销售属性值映射
            Map<String, List<PropertyValueVO>> draftSkuSalePropertyListMap = new HashMap<>();
            for (Map.Entry<String, List<SaleAttributeValuePO>> entry : draftSkuSaleAttributeValuesMap.entrySet()) {
                String skuKey = entry.getKey();
                List<SaleAttributeValuePO> values = entry.getValue();
                List<PropertyValueVO> propertyValueVOList = saleAttributeConvertService.convertToPropertyValueVOList(
                        values, saleAttributeValueLangsMap, lang, false);
                this.sortPropertyValueList(propertyValueVOList, spuSalePropertySortMap);
                draftSkuSalePropertyListMap.put(skuKey, propertyValueVOList);
            }

            SpuSaleAttributeVO result = new SpuSaleAttributeVO(spuSalePropertyList, skuSalePropertyListMap, draftSkuSalePropertyListMap);
            log.info("SaleAttributeManageServiceImpl.getSpuSaleAttributeDetail 获取SPU销售属性详情成功, spuId: {}, result: {}", spuId, JSON.toJSONString(result));
            return result;

        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.getSpuSaleAttributeDetail 获取SPU销售属性详情失败, spuId: {}, lang: {}", spuId, lang, e);
            return null;
        }
    }

    private static void sortPropertyValueList(List<PropertyValueVO> propertyValueVOList, Map<Long, Integer> spuSalePropertySortMap) {
        try {
            if(CollectionUtils.isNotEmpty(propertyValueVOList)){
                for (PropertyValueVO valueVO : propertyValueVOList) {
                    if(valueVO !=null && spuSalePropertySortMap !=null){
                        valueVO.setSort(spuSalePropertySortMap.getOrDefault(valueVO.getAttributeId(),0));
                    }
                }
                propertyValueVOList.sort(Comparator.comparing(PropertyValueVO::getSort));
            }
        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.sortPropertyValueList 排序销售属性值失败", e);
        }
    }

    /**
     * 根据销售属性ID集合获取销售属性列表及其对应语言的名称
     * @param lang 语言编码
     * @param allSaleAttributeIds 销售属性ID集合
     *
     * @return 销售属性VO列表
     */
    private List<PropertyVO> getSaleAttributeListByIdList(Set<Long> allSaleAttributeIds, String lang) {
        // 获取销售属性及其多语言信息
        List<SaleAttributePO> saleAttributePOList = new ArrayList<>();
        Map<String, String> saleAttributeCodeLangNameMap = new HashMap<>();
        if (allSaleAttributeIds!=null && !allSaleAttributeIds.isEmpty()) {
            saleAttributePOList = saleAttributeAtomicService.getValidByIdList(new ArrayList<>(allSaleAttributeIds));

            if (!CollectionUtils.isEmpty(saleAttributePOList)) {
                // 根据saleAttributePOList的saleAttributeType进行排序，图销在前，文销在后
                saleAttributePOList.sort(Comparator.comparingInt(SaleAttributePO::getSaleAttributeType));
                Set<String> saleAttributeNameSet = saleAttributePOList.stream()
                        .filter(Objects::nonNull)
                        .map(SaleAttributePO::getSaleAttributeName)
                        .collect(Collectors.toSet());
                saleAttributeCodeLangNameMap = saleAttributeLangManageService.getSaleAttributeNameLangsByCode(new ArrayList<>(saleAttributeNameSet), lang);
                log.info("SaleAttributeManageServiceImpl.getSaleAttributeListByIdList 获取销售属性多语言信息, 数量: {}", saleAttributeCodeLangNameMap.size());
            }
        } else {
            log.warn("SaleAttributeManageServiceImpl.getSaleAttributeListByIdList 没有找到销售属性数据");
            return new ArrayList<>();
        }

        // 使用转换服务进行PO到VO的转换
        List<PropertyVO> spuSalePropertyList = saleAttributeConvertService.convertToPropertyVOList(
                saleAttributePOList, saleAttributeCodeLangNameMap);
        return spuSalePropertyList;
    }

    /**
     * 从销售属性值列表构建PropertyVO列表的公共方法
     * 
     * @param saleAttributeValues 销售属性值列表
     * @param lang 语言编码
     * @param includeAllValues 是否包含所有属性值（true：包含全部可选值，false：只包含当前选中值）
     * @return PropertyVO列表
     */
    private List<PropertyVO> buildPropertyVOListFromSaleAttributeValues(List<SaleAttributeValuePO> saleAttributeValues, String lang, boolean includeAllValues) {
        if (CollectionUtils.isEmpty(saleAttributeValues)) {
            log.warn("SaleAttributeManageServiceImpl.buildPropertyVOListFromSaleAttributeValues 销售属性值列表为空");
            return new ArrayList<>();
        }

        try {
            // 1. 获取销售属性信息
            SaleAttributeInfoResult result = getSaleInfo(saleAttributeValues);
            if(Objects.isNull(result)){
                log.warn("SaleAttributeManageServiceImpl.buildPropertyVOListFromSaleAttributeValuesWithLangList 没有找到销售属性数据");
                return new ArrayList<>();
            }

           List<PropertyVO> saleAttributeList = this.getSaleAttributeListByIdList(result.saleAttributeIds, lang);
           if(CollectionUtils.isEmpty(saleAttributeList)){
            log.warn("SaleAttributeManageServiceImpl.buildPropertyVOListFromSaleAttributeValues 没有找到销售属性数据");
            return new ArrayList<>();
           }

            // 4. 为每个销售属性添加对应的属性值
            Map<Long, List<SaleAttributeValuePO>> attributeValuesMap = saleAttributeValues.stream()
                    .filter(Objects::nonNull)
                    .filter(value -> value.getSaleAttributeId() != null)
                    .collect(Collectors.groupingBy(SaleAttributeValuePO::getSaleAttributeId));

            for (PropertyVO propertyVO : saleAttributeList) {
                List<SaleAttributeValuePO> values = attributeValuesMap.getOrDefault(propertyVO.getAttributeId(), new ArrayList<>());
                List<PropertyValueVO> propertyValueVOList = saleAttributeConvertService.convertToPropertyValueVOList(
                        values, result.saleAttributeValueLangsMap, lang, includeAllValues);
                propertyVO.setPropertyValueVOList(propertyValueVOList);
            }

            log.info("SaleAttributeManageServiceImpl.buildPropertyVOListFromSaleAttributeValues 构建PropertyVO列表成功, 数量: {}", saleAttributeList.size());
            return saleAttributeList;

        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.buildPropertyVOListFromSaleAttributeValues 构建PropertyVO列表失败", e);
            return new ArrayList<>();
        }
    }

    
    @Override
    public List<PropertyVO> getSkuSaleAttributeDetail(Long skuId, String lang) {
        log.info("SaleAttributeManageServiceImpl.getSkuSaleAttributeDetail skuId: {}, lang: {}", skuId, lang);
        
        if (skuId == null || StringUtils.isBlank(lang)) {
            log.warn("SaleAttributeManageServiceImpl.getSkuSaleAttributeDetail skuId或lang为空");
            return new ArrayList<>();
        }

        try {
           // 1. 查询销售属性值详情
           List<SaleAttributeValuePO> saleAttributeValues = this.obtainSaleAttributeValues(skuId);
           if (CollectionUtils.isEmpty(saleAttributeValues)) {
               log.warn("SaleAttributeManageServiceImpl.getSkuSaleAttributeDetailWithLangList 没有找到销售属性值数据, skuId: {}", skuId);
               return new ArrayList<>();
           }

            // 4. 使用公共方法构建PropertyVO列表（不包含全部可选值，只包含该SKU选中的值）
            List<PropertyVO> result = buildPropertyVOListFromSaleAttributeValues(saleAttributeValues, lang, false);

            log.info("SaleAttributeManageServiceImpl.getSkuSaleAttributeDetail 获取SKU销售属性详情成功, skuId: {}, result: {}", 
                    skuId, JSON.toJSONString(result));
            return result;

        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.getSkuSaleAttributeDetail 获取SKU销售属性详情失败, skuId: {}, lang: {}", skuId, lang, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<PropertyVO> getSkuSaleAttributeDetailWithLangList(Long skuId, List<String> langList) {
        log.info("SaleAttributeManageServiceImpl.getSkuSaleAttributeDetailWithLangList skuId: {}, langList: {}", skuId, JSON.toJSONString(langList));
        
        if (skuId == null || CollectionUtils.isEmpty(langList)) {
            log.warn("SaleAttributeManageServiceImpl.getSkuSaleAttributeDetailWithLangList skuId或langList为空");
            return new ArrayList<>();
        }

        try {
            // 1. 查询销售属性值详情
            List<SaleAttributeValuePO> saleAttributeValues = this.obtainSaleAttributeValues(skuId);
            if (CollectionUtils.isEmpty(saleAttributeValues)) {
                log.warn("SaleAttributeManageServiceImpl.getSkuSaleAttributeDetailWithLangList 没有找到销售属性值数据, skuId: {}", skuId);
                return new ArrayList<>();
            }

            // 2. 使用专门的多语言构建方法
            List<PropertyVO> result = buildPropertyVOListFromSaleAttributeValuesWithLangList(saleAttributeValues, langList);

            log.info("SaleAttributeManageServiceImpl.getSkuSaleAttributeDetailWithLangList 获取SKU销售属性详情成功, skuId: {}, result: {}", 
                    skuId, JSON.toJSONString(result));
            return result;

        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.getSkuSaleAttributeDetailWithLangList 获取SKU销售属性详情失败, skuId: {}, langList: {}", skuId, JSON.toJSONString(langList), e);
            return new ArrayList<>();
        }
    }

    private List<SaleAttributeValuePO> obtainSaleAttributeValues(Long  skuId){
        if(Objects.isNull(skuId)){
            log.warn("SaleAttributeManageServiceImpl.obtainSaleAttributeValues skuId为空");
            return new ArrayList<>();
        }
        // 1. 查询该SKU的销售属性值关系
        List<SkuSaleAttributeValueRelationPO> skuRelations = skuSaleAttributeValueRelationAtomicService.getValidBySkuId(skuId);
        if (CollectionUtils.isEmpty(skuRelations)) {
            log.info("SaleAttributeManageServiceImpl.obtainSaleAttributeValues SKU没有销售属性值关系数据, skuId: {}", skuId);
            return new ArrayList<>();
        }

        log.info("SaleAttributeManageServiceImpl.obtainSaleAttributeValues 查询SKU销售属性值关系, 数量: {}", skuRelations.size());

        // 2. 提取销售属性值ID并查询详情
        Set<Long> saleAttributeValueIds = skuRelations.stream()
                .filter(Objects::nonNull)
                .filter(relation -> relation.getSaleAttributeValueId() != null)
                .map(SkuSaleAttributeValueRelationPO::getSaleAttributeValueId)
                .collect(Collectors.toSet());

        if (saleAttributeValueIds.isEmpty()) {
            log.warn("SaleAttributeManageServiceImpl.obtainSaleAttributeValues 没有有效的销售属性值ID, skuId: {}", skuId);
            return new ArrayList<>();
        }

        // 3. 查询销售属性值详情
        List<SaleAttributeValuePO> saleAttributeValues = saleAttributeValueAtomicService.getValidByIds(new ArrayList<>(saleAttributeValueIds));
        if (CollectionUtils.isEmpty(saleAttributeValues)) {
            log.warn("SaleAttributeManageServiceImpl.obtainSaleAttributeValues 没有找到销售属性值数据, skuId: {}", skuId);
            return new ArrayList<>();
        }
        return saleAttributeValues;
    }


    /**
     * 从销售属性值列表构建PropertyVO列表的公共方法（多语言版本）
     * 
     * @param saleAttributeValues 销售属性值列表
     * @param langList 语言列表
     * @return PropertyVO列表
     */
    private List<PropertyVO> buildPropertyVOListFromSaleAttributeValuesWithLangList(List<SaleAttributeValuePO> saleAttributeValues, List<String> langList) {
        if (CollectionUtils.isEmpty(saleAttributeValues)) {
            log.warn("SaleAttributeManageServiceImpl.buildPropertyVOListFromSaleAttributeValuesWithLangList 销售属性值列表为空");
            return new ArrayList<>();
        }

        try {
            // 1. 获取销售属性信息
            SaleAttributeInfoResult result = getSaleInfo(saleAttributeValues);
            if(Objects.isNull(result)){
                log.warn("SaleAttributeManageServiceImpl.buildPropertyVOListFromSaleAttributeValuesWithLangList 没有找到销售属性数据");
                return new ArrayList<>();
            }

            // 2. 获取销售属性列表及其多语言信息
            List<PropertyVO> saleAttributeList = this.getSaleAttributeListByIdListWithLangList(result.saleAttributeIds, langList);
            if(CollectionUtils.isEmpty(saleAttributeList)){
                log.warn("SaleAttributeManageServiceImpl.buildPropertyVOListFromSaleAttributeValuesWithLangList 没有找到销售属性数据");
                return new ArrayList<>();
            }

            // 3. 为每个销售属性添加对应的属性值
            Map<Long, List<SaleAttributeValuePO>> attributeValuesMap = saleAttributeValues.stream()
                    .filter(Objects::nonNull)
                    .filter(value -> value.getSaleAttributeId() != null)
                    .collect(Collectors.groupingBy(SaleAttributeValuePO::getSaleAttributeId));

            for (PropertyVO propertyVO : saleAttributeList) {
                List<SaleAttributeValuePO> values = attributeValuesMap.getOrDefault(propertyVO.getAttributeId(), new ArrayList<>());
                List<PropertyValueVO> propertyValueVOList = saleAttributeConvertService.convertToPropertyValueVOListWithLangList(
                        values, result.saleAttributeValueLangsMap);
                propertyVO.setPropertyValueVOList(propertyValueVOList);
            }
            // saleAttributeList中的langlist，仅仅保存传入的相关语言
            if (CollectionUtils.isNotEmpty(langList)) {
                saleAttributeList.forEach(propertyVO -> {
                    if (CollectionUtils.isNotEmpty(propertyVO.getLangList())) {
                        propertyVO.getPropertyValueVOList().forEach(propertyValueVO -> {
                            if (propertyValueVO != null && CollectionUtils.isNotEmpty(propertyValueVO.getLangList())) {
                                propertyValueVO.getLangList().removeIf(baseLangVO -> !langList.contains(baseLangVO.getLang()));
                            }
                        });
                    }
                });
            }
            log.info("SaleAttributeManageServiceImpl.buildPropertyVOListFromSaleAttributeValuesWithLangList 构建PropertyVO列表成功, 数量: {}", saleAttributeList.size());
            return saleAttributeList;

        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.buildPropertyVOListFromSaleAttributeValuesWithLangList 构建PropertyVO列表失败", e);
            return new ArrayList<>();
        }
    }

    private SaleAttributeInfoResult getSaleInfo(List<SaleAttributeValuePO> saleAttributeValues) {
        // 1. 提取销售属性值ID
        Set<Long> saleAttributeValueIds = saleAttributeValues.stream()
                .filter(Objects::nonNull)
                .filter(value -> value.getId() != null)
                .map(SaleAttributeValuePO::getId)
                .collect(Collectors.toSet());

        // 2. 提取销售属性ID
        Set<Long> saleAttributeIds = saleAttributeValues.stream()
                .filter(Objects::nonNull)
                .filter(value -> value.getSaleAttributeId() != null)
                .map(SaleAttributeValuePO::getSaleAttributeId)
                .collect(Collectors.toSet());

        if (saleAttributeIds.isEmpty()) {
            log.warn("SaleAttributeManageServiceImpl.getSaleInfo 没有有效的销售属性ID");
            return null;
        }

        // 3. 获取销售属性值的多语言
        Map<Long, List<SaleAttributeValueLangVO>> saleAttributeValueLangsMap =
                saleAttributeLangManageService.getSaleAttributeValueNameLangsByIds(new ArrayList<>(saleAttributeValueIds));
        SaleAttributeInfoResult result = new SaleAttributeInfoResult(saleAttributeIds, saleAttributeValueLangsMap);
        return result;
    }

    private static class SaleAttributeInfoResult {
        public final Set<Long> saleAttributeIds;
        public final Map<Long, List<SaleAttributeValueLangVO>> saleAttributeValueLangsMap;

        public SaleAttributeInfoResult(Set<Long> saleAttributeIds, Map<Long, List<SaleAttributeValueLangVO>> saleAttributeValueLangsMap) {
            this.saleAttributeIds = saleAttributeIds;
            this.saleAttributeValueLangsMap = saleAttributeValueLangsMap;
        }
    }

    /**
     * 获取销售属性列表及其多语言信息
     * 
     * @param allSaleAttributeIds 销售属性ID集合
     * @param langList 语言列表
     * @return PropertyVO列表
     */
    private List<PropertyVO> getSaleAttributeListByIdListWithLangList(Set<Long> allSaleAttributeIds, List<String> langList) {
        // 获取销售属性及其多语言信息
        List<SaleAttributePO> saleAttributePOList = new ArrayList<>();
        Map<String, Map<String, String>> saleAttributeCodeLangNameMap = new HashMap<>();
        if (allSaleAttributeIds!=null && !allSaleAttributeIds.isEmpty()) {
            saleAttributePOList = saleAttributeAtomicService.getValidByIdList(new ArrayList<>(allSaleAttributeIds));

            if (!CollectionUtils.isEmpty(saleAttributePOList)) {
                // 根据saleAttributePOList的saleAttributeType进行排序，图销在前，文销在后
                saleAttributePOList.sort(Comparator.comparingInt(SaleAttributePO::getSaleAttributeType));
                Set<String> saleAttributeNameSet = saleAttributePOList.stream()
                        .filter(Objects::nonNull)
                        .map(SaleAttributePO::getSaleAttributeName)
                        .collect(Collectors.toSet());
                saleAttributeCodeLangNameMap = saleAttributeLangManageService.getSaleAttributeNameLangsByCodeList(new ArrayList<>(saleAttributeNameSet), langList);
                log.info("SaleAttributeManageServiceImpl.getSaleAttributeListByIdListWithLangList 获取销售属性多语言信息, 数量: {}", saleAttributeCodeLangNameMap.size());
            }
        } else {
            log.warn("SaleAttributeManageServiceImpl.getSaleAttributeListByIdListWithLangList 没有找到销售属性数据");
            return new ArrayList<>();
        }

        // 使用转换服务进行PO到VO的转换（多语言版本）
        List<PropertyVO> spuSalePropertyList = saleAttributeConvertService.convertToPropertyVOListWithLangList(
                saleAttributePOList, saleAttributeCodeLangNameMap);
        return spuSalePropertyList;
    }

    @Override
    public List<PropertyVO> getMkuSaleAttributeDetail(Long mkuId, String lang) {
        log.info("SaleAttributeManageServiceImpl.getMkuSaleAttributeDetail mkuId: {}, lang: {}", mkuId, lang);

        if (mkuId == null || StringUtils.isBlank(lang)) {
            log.warn("SaleAttributeManageServiceImpl.getMkuSaleAttributeDetail mkuId或lang为空");
            return new ArrayList<>();
        }

        try {
            // 1. 通过mkuId获取mku关系
            List<MkuRelationPO> mkuRelationList = mkuRelationAtomicService.getMkuByMkuId(mkuId);
            if (CollectionUtils.isEmpty(mkuRelationList)) {
                log.warn("SaleAttributeManageServiceImpl.getMkuSaleAttributeDetail 没有找到mku关系, mkuId: {}", mkuId);
                return new ArrayList<>();
            }
            // 目前一个mku只有一个sku，所以取第一个   后续看业务变化需要优化
            MkuRelationPO mkuRelation = mkuRelationList.get(0);
            Long skuId = mkuRelation.getSkuId();
            if (skuId == null) {
                log.warn("SaleAttributeManageServiceImpl.getMkuSaleAttributeDetail mkuId: {} 没有找到skuId", mkuId);
                return new ArrayList<>();
            }
            return this.getSkuSaleAttributeDetail(skuId, lang);
        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.getMkuSaleAttributeDetail 获取mku关系失败, mkuId: {}, lang: {}", mkuId, lang, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<PropertyValueVO> getJdSkuSaleAttributeDetail(Long jdSkuId, String lang) {
        log.info("SaleAttributeManageServiceImpl.getJdSkuSaleAttributeDetail jdSkuId: {}, lang: {}", jdSkuId, lang);
        
        if (jdSkuId == null || StringUtils.isBlank(lang)) {
            log.warn("SaleAttributeManageServiceImpl.getJdSkuSaleAttributeDetail jdSkuId为空 或 lang为空");
            return new ArrayList<>();
        }
        
        try {
            // 1. 通过jdSkuId获取中台商品信息
            JdProductDTO jdProductDTO = skuInfoRpcService.getSkuById(jdSkuId);
            if (jdProductDTO == null) {
                log.warn("SaleAttributeManageServiceImpl.getJdSkuSaleAttributeDetail 中台SKU不存在, jdSkuId: {}", jdSkuId);
                return new ArrayList<>();
            }
            
            // 2. 获取类目ID
            Long jdCatId = jdProductDTO.getUnLimitCid() != null ? Long.valueOf(jdProductDTO.getUnLimitCid()) : null;
            if (jdCatId == null) {
                log.warn("SaleAttributeManageServiceImpl.getJdSkuSaleAttributeDetail 中台SKU没有类目信息, jdSkuId: {}", jdSkuId);
                return new ArrayList<>();
            }
            
            log.info("SaleAttributeManageServiceImpl.getJdSkuSaleAttributeDetail 获取到中台SKU信息, jdSkuId: {}, jdCatId: {}", jdSkuId, jdCatId);
            
            // 3. 获取中台sku的销售属性数据，分为图销和文销，用/拼接
            Result saleAttributeResult = getResultJdSkuSaleAttribute(null, jdSkuId, jdProductDTO, null, null);
            String imageSaleStr = saleAttributeResult.imageSaleStr; // 中台图销字符串（已用/拼接）
            String textSaleStr = saleAttributeResult.textSaleStr;   // 中台文销字符串（已用/拼接）
            log.info("SaleAttributeManageServiceImpl.getJdSkuSaleAttributeDetail 获取到的中台销售属性数据, 图销: {}, 文销: {}", imageSaleStr, textSaleStr);
            
            // 获取需要翻译的语言列表
            List<String> langList = langManageService.getLangCodeList();
            // 获取机翻
            Map<String, Set<String>> needTranslateMap = new HashMap<>();
            if (StringUtils.isNotBlank(imageSaleStr)) {
                needTranslateMap.put(imageSaleStr, new HashSet<>(langList));
            }
            if (StringUtils.isNotBlank(textSaleStr)) {
                needTranslateMap.put(textSaleStr, new HashSet<>(langList));
            }
            Map<String, Map<String, String>> translateResult = saleAttributeLangManageService.getTranslateResult(needTranslateMap);


            // 4. 通过类目ID获取销售属性信息
            List<PropertyVO> categorySaleAttributes = this.getCategorySaleAttributesByJdCatId(jdCatId, lang);
            log.info("SaleAttributeManageServiceImpl.getJdSkuSaleAttributeDetail 获取到的类目销售属性: {}", JSON.toJSONString(categorySaleAttributes));
            
            if (CollectionUtils.isEmpty(categorySaleAttributes)) {
                log.warn("SaleAttributeManageServiceImpl.getJdSkuSaleAttributeDetail 类目销售属性为空, jdCatId: {}", jdCatId);
                return new ArrayList<>();
            }
            
            // 5. 组装返回数据
            List<PropertyValueVO> result = new ArrayList<>();
            
            // 按图销、文销拼接数据返回p
            for (PropertyVO categoryAttribute : categorySaleAttributes) {
                PropertyValueVO propertyValueVO = new PropertyValueVO();
                propertyValueVO.setAttributeId(categoryAttribute.getAttributeId());
                propertyValueVO.setSort(categoryAttribute.getSort());
                propertyValueVO.setLang(lang);
                // 根据销售属性类型拼接对应的属性值
                SaleAttributeTypeEnum attributeType = SaleAttributeTypeEnum.forCode(categoryAttribute.getAttributeInputType());
                if (attributeType == SaleAttributeTypeEnum.IMAGE && StringUtils.isNotBlank(imageSaleStr)) {
                    // 图销：使用已拼接的图销属性值
                    propertyValueVO.setAttributeValueName(imageSaleStr);
                    propertyValueVO.setLangList(this.obtainLangList(translateResult, imageSaleStr));
                    result.add(propertyValueVO);
                    log.info("SaleAttributeManageServiceImpl.getJdSkuSaleAttributeDetail 添加图销属性值: {}", imageSaleStr);
                } else if (attributeType == SaleAttributeTypeEnum.TEXT && StringUtils.isNotBlank(textSaleStr)) {
                    // 文销：使用已拼接的文销属性值
                    propertyValueVO.setAttributeValueName(textSaleStr);
                    propertyValueVO.setLangList(this.obtainLangList(translateResult, textSaleStr));
                    result.add(propertyValueVO);
                    log.info("SaleAttributeManageServiceImpl.getJdSkuSaleAttributeDetail 添加文销属性值: {}", textSaleStr);
                }
            }
            
            log.info("SaleAttributeManageServiceImpl.getJdSkuSaleAttributeDetail 返回结果: {}", JSON.toJSONString(result));
            return result;
            
        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.getJdSkuSaleAttributeDetail 获取中台SKU销售属性失败, jdSkuId: {}", jdSkuId, e);
            return new ArrayList<>();
        }
    }

    private List<BaseLangVO> obtainLangList(Map<String, Map<String, String>> translateResult, String valueName){
        if (translateResult == null || StringUtils.isBlank(valueName)) {
            return new ArrayList<>();
        }
        List<BaseLangVO> langList = new ArrayList<>();
        if (translateResult.containsKey(valueName)) {
            Map<String, String> langMap = translateResult.get(valueName);
            if (langMap == null) {
                return new ArrayList<>();
            }
            langMap.forEach((lang, langName) -> {
                BaseLangVO baseLangVO = new BaseLangVO();
                baseLangVO.setLang(lang);
                baseLangVO.setLangName(langName);
                langList.add(baseLangVO);
            });
        }
        return langList;
    }

    @Override
    public List<PropertyVO> getCategorySaleAttributesByJdCatId(Long jdCatId, String lang) {
        log.info("SaleAttributeManageServiceImpl.getCategorySaleAttributesByJdCatId jdCatId: {}, lang: {}", jdCatId, lang);
        
        if (jdCatId == null || StringUtils.isBlank(lang)) {
            log.warn("SaleAttributeManageServiceImpl.getCategorySaleAttributesByJdCatId jdCatId为空或lang为空");
            return new ArrayList<>();
        }

        try {
            // 1. 调用中台接口获取销售属性
            GetCategorySaleAttByCategoryIdsParam param = new GetCategorySaleAttByCategoryIdsParam();
            param.setClientInfo(baseGmsClientService.buildClientInfo());
            param.setCategoryIds(Sets.newHashSet(jdCatId.intValue()));
            GreatDaneResult<Map<Integer, List<CategorySaleAtt>>> result = categorySaleAttReadService.getCategorySaleAttListByCategoryIds(param);
            log.info("SaleAttributeManageServiceImpl.getCategorySaleAttributesByJdCatId 获取类目销售属性成功, jdCatId: {}, result: {}", jdCatId, JSON.toJSONString(result));
            List<CategorySaleAtt> categorySaleAttList = new ArrayList<>();
            boolean defaultFlag = false;
            // 各种异常情况，都为销售属性赋兜底值
            if (result.isSuccess()) {
                Map<Integer, List<CategorySaleAtt>> data = result.getResult();
                if (MapUtils.isEmpty(data)) {
                    log.error("SaleAttributeManageServiceImpl.getCategorySaleAttributesByJdCatId 类目销售属性为空, 赋默认值, jdCatId: {}", jdCatId);
                    // 如果类目销售属性为空，赋默认值
                    categorySaleAttList = getDefaultSaleAttributeList();
                    defaultFlag = true;
                }

                categorySaleAttList = data.get(jdCatId.intValue());
                if (CollectionUtils.isEmpty(categorySaleAttList)) {
                    log.error("SaleAttributeManageServiceImpl.getCategorySaleAttributesByJdCatId 从中台获取的类目销售属性为空, 赋默认值，jdCatId: {}", jdCatId);
                    // 如果类目销售属性为空，赋默认值
                    categorySaleAttList = getDefaultSaleAttributeList();
                    defaultFlag = true;
                }
            }
            else{
                log.error("SaleAttributeManageServiceImpl.getCategorySaleAttributesByJdCatId 从中台获取类目销售属性错误，赋默认值, jdCatId: {}, errorMessage: {}", jdCatId, result.getErrorMessage());
                // 如果获取失败，赋默认值
                categorySaleAttList = getDefaultSaleAttributeList();
                defaultFlag = true;
            }

            // 打印获取到的类目销售属性
            log.info("SaleAttributeManageServiceImpl.getCategorySaleAttributesByJdCatId 获取到的类目 jdCatId: {} 销售属性: {} 是否默认值: {}", jdCatId, JSON.toJSONString(categorySaleAttList), defaultFlag ? "是" : "否");
            
            if (CollectionUtils.isNotEmpty(categorySaleAttList)) {
                // 2. 获取销售属性，对获取到的销售属性名进行排序（中文排序）
                List<String> picAttList = new ArrayList<>(); // 图销
                List<String> textAttList = new ArrayList<>(); // 文销
                categorySaleAttList.forEach(e -> {
                    SaleAttribute saleAttribute = e.getSaleAttribute();
                    if (saleAttribute != null) {
                        String saleAttName = saleAttribute.getSaleAttName();
                        // 图销（color=true），文销（color=false或空）
                        if (saleAttribute.getColor() != null && saleAttribute.getColor() && StringUtils.isNotBlank(saleAttName)) {
                            picAttList.add(saleAttName);
                        } else {
                            textAttList.add(saleAttName);
                        }
                    }
                });
                // 如果picAttList为空，设置默认值
                if (CollectionUtils.isEmpty(picAttList)) {
                    picAttList.add(Constant.DEFAULT_IMAGE_SALE_ATTRIBUTE_NAME);
                }
                // 如果textAttList为空，设置默认值
                if (CollectionUtils.isEmpty(textAttList)) {
                    textAttList.add(Constant.DEFAULT_TEXT_SALE_ATTRIBUTE_NAME);
                }

                List<CategorySaleAttributeVO> categoryResult = translateAndSaveCategorySaleAttributeList(picAttList, textAttList, jdCatId, lang);
                log.info("SaleAttributeManageServiceImpl.getCategorySaleAttributesByJdCatId 获取类目销售属性成功, jdCatId: {}, lang: {}, categoryResult: {}", jdCatId, lang, JSON.toJSONString(categoryResult));
                // 5. 转换为PropertyVO
                List<PropertyVO> res = saleAttributeConvertService.convertCategorySaleAttributeToPropertyVO(categoryResult);

                log.info("SaleAttributeManageServiceImpl.getCategorySaleAttributesByJdCatId 获取类目销售属性成功, jdCatId: {}, lang: {}, result: {}", jdCatId, lang, JSON.toJSONString(res));
                return res;
            } else {
                log.error("SaleAttributeManageServiceImpl.getCategorySaleAttributesByJdCatId 没有获取到类目销售属性，也没有默认值, jdCatId: {}, errorMessage: {}", jdCatId, result.getErrorMessage());
                return new ArrayList<>();
            }
        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.getCategorySaleAttributesByJdCatId 获取类目销售属性失败, jdCatId: {}, lang: {}", jdCatId, lang, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取默认的商品销售属性列表
     *
     * @return 包含默认销售属性（颜色和尺寸）的CategorySaleAtt对象列表
     */
    @NotNull
    private static List<CategorySaleAtt> getDefaultSaleAttributeList() {
        List<CategorySaleAtt> categorySaleAttList;
        categorySaleAttList = new ArrayList<>();
        CategorySaleAtt categorySaleAtt = new CategorySaleAtt();
        SaleAttribute picSaleAttribute = new SaleAttribute();
        picSaleAttribute.setSaleAttName(Constant.DEFAULT_IMAGE_SALE_ATTRIBUTE_NAME);
        picSaleAttribute.setColor(true);
        categorySaleAtt.setSaleAttribute(picSaleAttribute);
        categorySaleAttList.add(categorySaleAtt);
        categorySaleAtt = new CategorySaleAtt();
        SaleAttribute textSaleAttribute = new SaleAttribute();
        textSaleAttribute.setSaleAttName(Constant.DEFAULT_TEXT_SALE_ATTRIBUTE_NAME);
        textSaleAttribute.setColor(false);
        categorySaleAtt.setSaleAttribute(textSaleAttribute);
        categorySaleAttList.add(categorySaleAtt);
        return categorySaleAttList;
    }

    /**
     * 根据中台类目销售属性，进行机翻，写入数据库，写入数据库包括销售属性表和销售属性多语言表
     *
     * @param picAttList  图销属性列表
     * @param textAttList 文销属性列表
     * @param jdCatId     中台类目ID
     * @param lang        语言
     * @return 类目销售属性列表
     */
    @NotNull
    private List<CategorySaleAttributeVO> translateAndSaveCategorySaleAttributeList(List<String> picAttList, List<String> textAttList, Long jdCatId, String lang) {
        log.info("SaleAttributeManageServiceImpl.translateAndSaveCategorySaleAttributeList picAttList: {}, textAttList: {}, jdCatId: {}, lang: {}", picAttList, textAttList, jdCatId, lang);
        Result result = getResult(picAttList, textAttList);

        // 4. 保存或更新销售属性name到数据库（根据catid查询是否已经存在销售属性，对name不相等的进行更新，否则不动，然后返回数据库对象，这时候会有ID）
        List<CategorySaleAttributeVO> categoryResult = new ArrayList<>();
        Set<String> saleAttributeNameSet = Sets.newHashSet();

        // 处理图销属性
        if (StringUtils.isNotBlank(result.imageSaleStr)) {
            saleAttributeNameSet.add(result.imageSaleStr);
            SaleAttributePO imageSaleAttr = saleAttributeAtomicService.saveOrUpdateSaleAttribute(jdCatId, SaleAttributeTypeEnum.IMAGE.getCode(), result.imageSaleStr, SaleAttributeTypeEnum.IMAGE.getSort());
            if (imageSaleAttr != null) {
                CategorySaleAttributeVO imageAttrVO = getCategorySaleAttributeVO(imageSaleAttr, SaleAttributeTypeEnum.IMAGE);
                categoryResult.add(imageAttrVO);
            }
        }

        // 处理文销属性
        if (StringUtils.isNotBlank(result.textSaleStr)) {
            saleAttributeNameSet.add(result.textSaleStr);
            SaleAttributePO textSaleAttr = saleAttributeAtomicService.saveOrUpdateSaleAttribute(jdCatId, SaleAttributeTypeEnum.TEXT.getCode(), result.textSaleStr, SaleAttributeTypeEnum.TEXT.getSort());
            if (textSaleAttr != null) {
                CategorySaleAttributeVO textAttrVO = getCategorySaleAttributeVO(textSaleAttr, SaleAttributeTypeEnum.TEXT);
                categoryResult.add(textAttrVO);
            }
        }
        // 处理多语言，判断多语言是否存在，不存在异步机翻，存入多语言表，然后返回对应lang的信息
        Map<String, String> localTranslateSaleAttributeNamesByLang = saleAttributeLangManageService.getLocalTranslateSaleAttributeNamesByLang(saleAttributeNameSet, lang);
        // 设置当前语言的销售属性名称
        categoryResult.forEach(item -> {
            if (localTranslateSaleAttributeNamesByLang != null && StringUtils.isNotBlank(localTranslateSaleAttributeNamesByLang.get(item.getSaleAttributeName()))) {
                item.setSaleAttributeName(localTranslateSaleAttributeNamesByLang.get(item.getSaleAttributeName()));
            } else {
                log.warn("SaleAttributeManageServiceImpl.getCategorySaleAttributesByJdCatId 多语言翻译不存在, jdCatId: {},  lang: {}, saleAttributeName: {}", jdCatId, lang, item.getSaleAttributeName());
            }
        });
        return categoryResult;
    }

    @NotNull
    public static Result getResult(List<String> picAttList, List<String> textAttList) {
        // 去除空值
        picAttList.removeIf(StringUtils::isBlank);
        textAttList.removeIf(StringUtils::isBlank);
        // 排序，以保证属性名稳定性
        Collator collator = Collator.getInstance(Locale.CHINA);
        picAttList.sort(collator);
        textAttList.sort(collator);
        // 3. 按照排序的顺序使用/进行拼接，图销一组、文销一组
        String imageSaleName = String.join(Constant.SLASH, picAttList);
        String textSaleName = String.join(Constant.SLASH, textAttList);
        Result result = new Result(imageSaleName, textSaleName);
        return result;
    }

    public static class Result {
        public final String imageSaleStr;
        public final String textSaleStr;

        public Result(String imageSaleStr, String textSaleStr) {
            this.imageSaleStr = imageSaleStr;
            this.textSaleStr = textSaleStr;
        }
    }

    @NotNull
    private static CategorySaleAttributeVO getCategorySaleAttributeVO(SaleAttributePO saleAttr, SaleAttributeTypeEnum type) {
        CategorySaleAttributeVO imageAttrVO = new CategorySaleAttributeVO();
        imageAttrVO.setSaleAttributeId(saleAttr.getId());
        imageAttrVO.setSaleAttributeType(type.getCode());
        imageAttrVO.setSaleAttributeName(saleAttr.getSaleAttributeName());
        imageAttrVO.setSort(type.getSort());
        return imageAttrVO;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean createSkuSaleAttributeForCrossBorder(Long spuId, Long skuId, Long jdSkuId) {
        // 不处理销售属性
        if(SaleAttributeUNhandleContextHolder.get() !=null && SaleAttributeUNhandleContextHolder.get()){
            log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder 不处理销售属性");
            return true;
        }
        log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder spuId: {}, skuId: {}, jdSkuId: {}", spuId, skuId, jdSkuId);

        if (spuId == null || skuId == null || jdSkuId == null) {
            log.warn("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder 入参不能为空");
            throw new BizException("入参不能为空");
        }

        // 通过skuId获取skuKey
        SkuDraftPO skuDraftPO = skuDraftAtomicService.getValidBySkuId(skuId);
        if (skuDraftPO == null || StringUtils.isBlank(skuDraftPO.getSkuKey())) {
            log.warn("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder skuId: {} 没有获取到skuKey", skuId);
            throw new BizException("skuId: " + skuId + " 没有获取到skuKey");
        }
        String skuKey = skuDraftPO.getSkuKey();
        log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder skuId: {} 获取到的skuKey: {}", skuId, skuKey);

        // 判断spu是否已经存在销售属性值，如果存在，则直接返回
        List<SaleAttributeValuePO> spuSaleAttributeValuePOList = saleAttributeValueAtomicService.getSaleAttributeValueBySpuId(spuId);
        if (!CollectionUtils.isEmpty(spuSaleAttributeValuePOList)) {
            log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder spuId: {} 已经存在销售属性值，不能继续新增销售属性，跳过处理，skuId: {}, jdSkuId: {}", spuId, skuId, jdSkuId);
            return true;
        }

        // 1. 通过jdskuid获取中台sku的销售属性数据
        JdProductDTO jdProductDTO = skuInfoRpcService.getSkuById(jdSkuId);
        if (jdProductDTO == null) {
            log.warn("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder 中台SKU不存在, jdSkuId: {}", jdSkuId);
            throw new BizException("中台SKU不存在");
        }
        Long jdCatId = jdProductDTO.getUnLimitCid()!=null?Long.valueOf(jdProductDTO.getUnLimitCid()):null;
        if (jdCatId == null) {
            log.warn("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder 中台SKU没有类目信息, jdSkuId: {}", jdSkuId);
            throw new BizException("中台SKU没有类目信息");
        }
        log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder 获取到中台SKU信息: {}", JSON.toJSONString(jdProductDTO));

        // 通过spuId获取发品类目Id
        SpuDraftPO spuDraftPO = spuDraftAtomicService.getBySpuId(spuId);
        Long spuJdCatId = null;
        if(spuDraftPO != null) {
            spuJdCatId = spuDraftPO.getJdCatId();
        }

        // 2. 根据jdsku返回的销售属性数据拼接销售属性名称，并且进行机翻，写入数据库，写入数据库包括销售属性表和销售属性多语言表
        List<PropertyVO> categorySaleAttributes = this.getCategorySaleAttributesByJdCatId(jdCatId, Constant.DEFAULT_LANG);
        String imageSaleName = null;
        String textSaleName = null;
        for (PropertyVO categorySaleAttribute : categorySaleAttributes) {
            if (Objects.equals(categorySaleAttribute.getAttributeInputType(), SaleAttributeTypeEnum.IMAGE.getCode())) {
                imageSaleName = categorySaleAttribute.getAttributeName();
            } else if(Objects.equals(categorySaleAttribute.getAttributeInputType(), SaleAttributeTypeEnum.TEXT.getCode())){
                textSaleName = categorySaleAttribute.getAttributeName();
            }
        }

        // 3. 获取中台sku的销售属性值数据，分为图销和文销，用/拼接
        Result result = getResultJdSkuSaleAttribute(spuJdCatId, jdSkuId, jdProductDTO, imageSaleName, textSaleName);
        log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder jdCatId: {} 获取到的中台销售属性数据: {}", jdCatId, JSON.toJSONString(categorySaleAttributes));
        // 4、销售属性值写入数据库
        List<SaleAttributeValuePO> saleAttributeValuePOList = new ArrayList<>();
        // 组装SaleAttributeValuePO，categorySaleAttributes size应该等于2，一个是图销，一个文销
        int sort=1;
        for (PropertyVO categorySaleAttribute : categorySaleAttributes) {
            SaleAttributeValuePO saleAttributeValuePO = new SaleAttributeValuePO();
            saleAttributeValuePO.setSpuId(spuId);
            saleAttributeValuePO.setSort(sort++); // 设置sort，同一个spu内部有序
            // 如果登录上下文为空，则设置创建者和更新者为系统
            if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                saleAttributeValuePO.setCreator(Constant.PIN_SYSTEM);
                saleAttributeValuePO.setUpdater(Constant.PIN_SYSTEM);
            }
            if (SaleAttributeTypeEnum.IMAGE.getCode().equals(categorySaleAttribute.getAttributeInputType()) && StringUtils.isNotBlank(result.imageSaleStr)) {
                saleAttributeValuePO.setSaleAttributeId(categorySaleAttribute.getAttributeId());
                saleAttributeValuePO.setSaleAttributeValueName(result.imageSaleStr);
                // 确保销售属性值有值才能写入数据库
                saleAttributeValuePOList.add(saleAttributeValuePO);
            } else if (SaleAttributeTypeEnum.TEXT.getCode().equals(categorySaleAttribute.getAttributeInputType()) && StringUtils.isNotBlank(result.textSaleStr)) {
                saleAttributeValuePO.setSaleAttributeId(categorySaleAttribute.getAttributeId());
                saleAttributeValuePO.setSaleAttributeValueName(result.textSaleStr);
                // 确保销售属性值有值才能写入数据库
                saleAttributeValuePOList.add(saleAttributeValuePO);
            } else {
                log.warn("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder 销售属性类型不匹配, jdSkuId: {}, categorySaleAttributes: {}",
                        jdSkuId, JSON.toJSONString(categorySaleAttributes));
                continue;
            }
            
        }
        log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder jdSkuId: {} 待保存的销售属性值: {}", jdSkuId, JSON.toJSONString(saleAttributeValuePOList));

        // 销售属性值写入数据库
        List<SaleAttributeValuePO> savedSaleAttributeValuePOList = saleAttributeValueAtomicService.saveWithSortAndReturnWithId(spuId, saleAttributeValuePOList);
        if (CollectionUtils.isEmpty(savedSaleAttributeValuePOList)) {
            log.warn("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder 没有保存任何销售属性值, skuId: {}, jdSkuId: {}", skuId, jdSkuId);
            throw new BizException("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder 没有保存任何销售属性值, skuId" + skuId + " jdSkuId: " + jdSkuId);
        } else {
            log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder 销售属性值保存成功, skuId: {}, jdSkuId: {}, 销售属性值: {}",
                    skuId, jdSkuId, JSON.toJSONString(savedSaleAttributeValuePOList));
        }

        // 5、销售属性值多语言写入数据库，（调用createCrossBorderSaleAttributeValueLangs），写入数据库，销售属性值多语言表
        if (!CollectionUtils.isEmpty(savedSaleAttributeValuePOList)) {
            try {
                saleAttributeLangManageService.createCrossBorderSaleAttributeValueLangs(SaleAttributeConvert.INSTANCE.toValueVOList(savedSaleAttributeValuePOList));
                log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder 销售属性值多语言处理完成, skuId: {}, jdSkuId: {}, 数量: {}",
                        skuId, jdSkuId, savedSaleAttributeValuePOList.size());
            } catch (Exception e) {
                log.error("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder 销售属性值多语言处理失败, skuId: {}, jdSkuId: {}", skuId, jdSkuId, e);
                // 多语言失败不影响主流程，继续执行
            }
        }

        // 6、sku和销售属性值关联写入数据库
        if (!CollectionUtils.isEmpty(savedSaleAttributeValuePOList)) {
            try {
                Map<Long, Set<Map<Long, String>>> saleAttributeValueIdToSkuIdSkuKeyMap = new HashMap<>();
                for (SaleAttributeValuePO saleAttributeValuePO : savedSaleAttributeValuePOList) {
                    Map<Long, String> skuIdSkuKeyMap = new HashMap<>();
                    skuIdSkuKeyMap.put(skuId, skuKey);
                    saleAttributeValueIdToSkuIdSkuKeyMap.put(saleAttributeValuePO.getId(), new HashSet<>(Arrays.asList(skuIdSkuKeyMap)));
                }
                boolean batchResult = skuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuId(saleAttributeValueIdToSkuIdSkuKeyMap);
                if (batchResult) {
                    log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder SKU销售属性值关联关系处理完成, skuId: {}", skuId);
                } else {
                    log.error("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder SKU销售属性值关联关系处理失败, skuId: {}", skuId);
                }
                return batchResult;
            } catch (Exception e) {
                log.error("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder SKU销售属性值关联关系处理失败, skuId: {}, exception: {}", skuId, e);
                throw e;
            }
        }
        log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeForCrossBorder 处理跨境品发品销售属性完成, skuId: {}", skuId);
        return true;
    }

    @NotNull
    public Result getResultJdSkuSaleAttribute(Long guojiCatId, Long jdSkuId, JdProductDTO jdProductDTO, String imageSaleName, String textSaleName) {
        // 2. 转换销售属性数据
        List<JdSkuSaleAttributeVO> jdSaleAttributes = saleAttributeConvertService.convertJdSaleAttributes(
                jdProductDTO.getSaleAttributes(), jdProductDTO.getSaleAtts());
        String picAtrributeValue = jdProductDTO.getColor();
        String currentProfile = this.getCurrentProfile();
        // 如果获取不到销售属性值，设置默认属性值，默认值为图销类型，名称为“默认”
        if(CollectionUtils.isEmpty(jdSaleAttributes)){
            log.error("SaleAttributeManageServiceImpl.getResultJdSkuSaleAttribute jdSkuId: {} 没有获取到中台sku销售属性值数据, 设置默认属性为默认，且为图销", jdSkuId);
            picAtrributeValue = Constant.DEFAULT_SALE_ATTRIBUTE_VALUE_NAME;
            // 设置默认属性
            jdSaleAttributes = new ArrayList<>();
            JdSkuSaleAttributeVO jdSaleAttribute = new JdSkuSaleAttributeVO();
            jdSaleAttribute.setSaleValue(Constant.DEFAULT_SALE_ATTRIBUTE_VALUE_NAME);
            jdSaleAttributes.add(jdSaleAttribute);
        }
        log.info("SaleAttributeManageServiceImpl.getResultJdSkuSaleAttribute jdSkuId: {} 获取到的中台sku销售属性值数据: {}", jdSkuId, JSON.toJSONString(jdSaleAttributes));
        if(CollectionUtils.isEmpty(jdSaleAttributes)){
            log.error("SaleAttributeManageServiceImpl.getResultJdSkuSaleAttribute jdSkuId: {} 没有获取到中台sku销售属性值数据", jdSkuId);
            throw new BizException(String.format("没有获取到中台sku销售属性值数据，无法创建跨境品 jdSkuId:%s",jdSkuId));
        }
        log.info("SaleAttributeManageServiceImpl.getResultJdSkuSaleAttribute jdSkuId: {} 获取到的中台sku销售属性值数据: {}", jdSkuId, JSON.toJSONString(jdSaleAttributes));
        // 2.1 获取中台销售属性名称列表
        List<String> picAttValueList = new ArrayList<>(); // 图销属性value名称list
        List<String> textAttValueList = new ArrayList<>(); // 文销属性value名称list
        Map<String, String> picJdSaleNameValueMap = new HashMap<>(); // 图销
        Map<String, String> textJdSaleNameValueMap = new HashMap<>(); // 文销
        for (JdSkuSaleAttributeVO jdSaleAttribute : jdSaleAttributes) {
            if (StringUtils.isNotBlank(jdSaleAttribute.getSaleValue()) && !jdSaleAttribute.getSaleValue().equals(picAtrributeValue)) {
                textAttValueList.add(jdSaleAttribute.getSaleValue());
                textJdSaleNameValueMap.put(jdSaleAttribute.getSaleName(), jdSaleAttribute.getSaleValue());
            } else {
                picAttValueList.add(jdSaleAttribute.getSaleValue());
                picJdSaleNameValueMap.put(jdSaleAttribute.getSaleName(), jdSaleAttribute.getSaleValue());
            }
        }
        //  获取拼接销售属性值
        Result result = getResult(picAttValueList, textAttValueList);
        // 判断跨境品是否与中台类目一致，不一致使用getResult，一致需要根据类目销售属性名称排序，如果处理顺序时抛异常，则仍然使用getResult
        try {
            if (guojiCatId != null && Objects.equals(guojiCatId.intValue(), jdProductDTO.getUnLimitCid())) {
                // 如果类目一致，则需要根据类目销售属性名称排序

                String imageSaleStr = null;
                String textSaleStr = null;
                // 如果图销属性名称不为空，则进行排序
                if (MapUtils.isNotEmpty(picJdSaleNameValueMap) && StringUtils.isNotBlank(imageSaleName)) {
                    List<String> expectedValueOrderList = getExpectedValueOrderList(imageSaleName, picJdSaleNameValueMap);
                    imageSaleStr = String.join(Constant.SLASH, expectedValueOrderList);
                }
                // 如果文销属性名称不为空，则进行排序
                if (MapUtils.isNotEmpty(textJdSaleNameValueMap) && StringUtils.isNotBlank(textSaleName)) {
                    List<String> expectedValueOrderList = getExpectedValueOrderList(textSaleName, textJdSaleNameValueMap);
                    textSaleStr = String.join(Constant.SLASH, expectedValueOrderList);
                }
                if (StringUtils.isNotBlank(imageSaleStr) || StringUtils.isNotBlank(textSaleStr)) {
                    result = new Result(imageSaleStr, textSaleStr);
                }
            }
        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.getResultJdSkuSaleAttribute jdSkuId: {} 处理销售属性名称顺序失败, exception: {}", jdSkuId, e);
            // 处理顺序失败不影响主流程，继续执行
        }
        return result;
    }

    /**
     * 根据销售属性名称获取期望的销售属性值顺序，销售属性名称用/分割
     * @param attributeName
     * @param jdSaleNameValueMap
     * @return
     */
    public static List<String> getExpectedValueOrderList(String attributeName, Map<String, String> jdSaleNameValueMap) {
        // 构建期望的顺序
        List<String> expectedValueOrder = new ArrayList<>();
        if (StringUtils.isNotBlank(attributeName)) {
            String[] attributeNames = attributeName.split(Constant.SLASH);
            for (int i = 0; i < attributeNames.length; i++) {
                String name = attributeNames[i].trim();
                String expectedValue = jdSaleNameValueMap.get(name);
                if (StringUtils.isNotBlank(expectedValue)) {
                    expectedValueOrder.add(expectedValue.trim());
                } else {
                    // 如果没有找到匹配的属性值，用 "-" 进行占位符处理
                    expectedValueOrder.add(Constant.MINUS);
                    log.debug("DevOpsSaleApiServiceImpl.reorderSaleAttributeValueByCategory 属性名称 {} 在京东销售属性中未找到匹配值，使用占位符 '-'", name.trim());
                }
            }
        }
        return expectedValueOrder;
    }

    /**
     * 对用户输入的销售属性值名称进行trim
     */
    private void trimSkuVOAttibuteValueName(List<SkuVO> updateSkuVOList) {
        if (CollectionUtils.isEmpty(updateSkuVOList)) {
            return;
        }
        for (SkuVO skuVO : updateSkuVOList) {
            if (skuVO.getStoreSalePropertyList() != null) {
                for (PropertyValueVO propertyValueVO : skuVO.getStoreSalePropertyList()) {
                    // 对销售属性值名称进行trim
                    if (propertyValueVO != null && StringUtils.isNotBlank(propertyValueVO.getAttributeValueName())) {
                        propertyValueVO.setAttributeValueName(propertyValueVO.getAttributeValueName().trim());
                    }
                    // 对销售属性值多语言名称进行trim
                    if (propertyValueVO != null && CollectionUtils.isNotEmpty(propertyValueVO.getLangList())) {
                        for (BaseLangVO baseLangVO : propertyValueVO.getLangList()) {
                            if (baseLangVO != null && StringUtils.isNotBlank(baseLangVO.getLangName())) {
                                baseLangVO.setLangName(baseLangVO.getLangName().trim());
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 根据spuId获取销售属性值名称列表
     * @param spuId spuId
     * @return 销售属性值名称列表
     */
    private Set<String> getChineseAttributeValueNameSetBySpuId(Long spuId) {
        List<SaleAttributeValuePO> saleAttributeValuePOList = saleAttributeValueAtomicService.getSaleAttributeValueBySpuId(spuId);
        if (CollectionUtils.isEmpty(saleAttributeValuePOList)) {
            return new HashSet<>();
        }
        Set<String> chineseAttributeValueNameSet = new HashSet<>();
        for (SaleAttributeValuePO saleAttributeValuePO : saleAttributeValuePOList) {
            chineseAttributeValueNameSet.add(saleAttributeValuePO.getSaleAttributeValueName());
        }
        return chineseAttributeValueNameSet;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean updateSkuSaleAttributeForCrossBorder(Long spuId, List<SkuVO> updateSkuVOList) {
        // 不处理销售属性
        if(SaleAttributeUNhandleContextHolder.get() !=null && SaleAttributeUNhandleContextHolder.get()){
            log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder 不处理销售属性");
            return true;
        }
        log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder 开始更新跨境品销售属性, spuId: {}, updateSkuVOList: {}", 
                spuId, JSON.toJSONString(updateSkuVOList));
        
        if (spuId == null || CollectionUtils.isEmpty(updateSkuVOList)) {
            log.warn("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder 入参不能为空");
            throw new BizException("入参不能为空");
        }

        // 对用户输入的销售属性值名称进行trim
        this.trimSkuVOAttibuteValueName(updateSkuVOList);

        try {
            this.deleteSpuSaleAttribute(spuId);

            // 2. 收集用户填写的销售属性信息
            Map<String, List<BaseLangVO>> attributeValueLangMap = new HashMap<>();
            Map<String, Set<Long>> saleAttributeValueChineseNameSkuIdSetMap = new HashMap<>();
            Set<String> chineseAttributeValueNameSet = this.getChineseAttributeValueNameSetBySpuId(spuId);
            List<SaleAttributeValuePO> saleAttributeValuePOList = new ArrayList<>();
            // 记录skuId和skuKey
            Map<Long, String> skuIdSkuKeyMap = new HashMap<>();
            for (SkuVO skuVO : updateSkuVOList) {
                if (skuVO.getSkuId() == null || StringUtils.isBlank(skuVO.getSkuKey())) {
                    log.warn("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder SKU ID为空或skuKey为空, 跳过处理: {}", JSON.toJSONString(skuVO));
                    throw new BizException("updateSkuSaleAttributeForCrossBorder SKU ID为空或skuKey为空, 请检查");
                }
                skuIdSkuKeyMap.put(skuVO.getSkuId(), skuVO.getSkuKey());
                List<PropertyValueVO> storeSalePropertyList = skuVO.getStoreSalePropertyList();
                if (CollectionUtils.isEmpty(storeSalePropertyList)) {
                    log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder SKU没有销售属性, skuId: {}", skuVO.getSkuId());
                    throw new BizException("updateSkuSaleAttributeForCrossBorder SKU没有销售属性, 请检查 skuId: " + skuVO.getSkuId());
                }
                
                // 收集销售属性值信息
                for (PropertyValueVO propertyValueVO : storeSalePropertyList) {
                    // 从langList中提取中文名称作为销售属性值名称
                    String chineseAttributeValueName = extractChineseNameFromLangList(propertyValueVO.getLangList());
                    
                    if (StringUtils.isNotBlank(chineseAttributeValueName) && !chineseAttributeValueNameSet.contains(chineseAttributeValueName)) {
                        chineseAttributeValueNameSet.add(chineseAttributeValueName);
                        
                        SaleAttributeValuePO saleAttributeValuePO = new SaleAttributeValuePO();
                        saleAttributeValuePO.setSpuId(spuId);
                        saleAttributeValuePO.setSaleAttributeId(propertyValueVO.getAttributeId());
                        saleAttributeValuePO.setSaleAttributeValueName(chineseAttributeValueName);
                        saleAttributeValuePO.setYn(YnEnum.YES.getCode());
                        saleAttributeValuePO.setSort(propertyValueVO.getSort());
                        
                        // 设置创建者和更新者
                        if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                            saleAttributeValuePO.setCreator(Constant.PIN_SYSTEM);
                            saleAttributeValuePO.setUpdater(Constant.PIN_SYSTEM);
                        }
                        
                        saleAttributeValuePOList.add(saleAttributeValuePO);
                        attributeValueLangMap.put(chineseAttributeValueName, propertyValueVO.getLangList());
                    }
                    
                    // 记录SKU与销售属性值的关联关系
                    if (StringUtils.isNotBlank(chineseAttributeValueName)) {
                        saleAttributeValueChineseNameSkuIdSetMap.computeIfAbsent(chineseAttributeValueName, k -> new HashSet<>()).add(skuVO.getSkuId());
                    }
                }
            }
            
            if (CollectionUtils.isEmpty(saleAttributeValuePOList)) {
                log.warn("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder 没有需要保存的销售属性值, spuId: {}", spuId);
                return true;
            }
            
            // 3. 保存新的销售属性值
            List<SaleAttributeValuePO> savedSaleAttributeValuePOList = saleAttributeValueAtomicService.saveWithSortAndReturnWithId(spuId, saleAttributeValuePOList);
            if (CollectionUtils.isEmpty(savedSaleAttributeValuePOList)) {
                log.warn("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder 销售属性值保存失败, spuId: {}", spuId);
                throw new BizException("销售属性值保存失败");
            }
            
            log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder 销售属性值保存成功, spuId: {}, 数量: {}", 
                    spuId, savedSaleAttributeValuePOList.size());
            
            // 4. 保存销售属性值的多语言信息
            List<SaleAttributeValueLangPO> saleAttributeValueLangPOList = new ArrayList<>();
            for (SaleAttributeValuePO saleAttributeValuePO : savedSaleAttributeValuePOList) {
                List<BaseLangVO> langList = attributeValueLangMap.get(saleAttributeValuePO.getSaleAttributeValueName());
                
                if (!CollectionUtils.isEmpty(langList)) {
                    for (BaseLangVO langVO : langList) {
                        if (StringUtils.isNotBlank(langVO.getLang()) && StringUtils.isNotBlank(langVO.getLangName())) {
                            SaleAttributeValueLangPO langPO = SaleAttributeValueLangAtomicService.getSaleAttributeValueLangPO(
                                    saleAttributeValuePO.getId(), langVO.getLang(), langVO.getLangName());
                            saleAttributeValueLangPOList.add(langPO);
                        }
                    }
                }
            }
            
            if (!CollectionUtils.isEmpty(saleAttributeValueLangPOList)) {
                saleAttributeValueLangAtomicService.saveBatch(saleAttributeValueLangPOList);
                log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder 销售属性值多语言信息保存成功, spuId: {}, 数量: {}", 
                        spuId, saleAttributeValueLangPOList.size());
            }
            
            // 5. 保存SKU与销售属性值的关联关系
            Map<Long, Set<Map<Long, String>>> saleAttributeValueIdToSkuIdSkuKeyMap = new HashMap<>();
            for (SaleAttributeValuePO saleAttributeValuePO : savedSaleAttributeValuePOList) {
                Set<Long> skuIdSet = saleAttributeValueChineseNameSkuIdSetMap.get(saleAttributeValuePO.getSaleAttributeValueName());
                if (skuIdSet != null) {
                    Set<Map<Long, String>> skuIdSkuKeySet = new HashSet<>();
                    Map<Long, String> map = new HashMap<>();
                    for (Long skuId : skuIdSet) {
                        if (skuIdSkuKeyMap.containsKey(skuId) && StringUtils.isNotBlank(skuIdSkuKeyMap.get(skuId))) {
                            map.put(skuId, skuIdSkuKeyMap.get(skuId));
                        } else {
                            log.warn("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder skuId: {} 没有获取到skuKey", skuId);
                        }
                    }
                    // 保存当前销售属性值对应的skuId和SKU Key信息，用来最终保存到关系表
                    skuIdSkuKeySet.add(map);
                    saleAttributeValueIdToSkuIdSkuKeyMap.put(saleAttributeValuePO.getId(), skuIdSkuKeySet);
                }
            }
            
            boolean batchResult = false;
            if (!saleAttributeValueIdToSkuIdSkuKeyMap.isEmpty()) {
                try {
                    batchResult = skuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuId(saleAttributeValueIdToSkuIdSkuKeyMap);
                    if (batchResult) {
                        log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder SKU销售属性值关联关系保存成功, spuId: {}, 关联数量: {}", 
                                spuId, saleAttributeValueIdToSkuIdSkuKeyMap.size());
                    } else {
                        log.error("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder SKU销售属性值关联关系保存失败, spuId: {}", spuId);
                    }
                } catch (Exception e) {
                    log.error("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder SKU销售属性值关联关系保存异常, spuId: {}", spuId, e);
                    throw e;
                }
            }
            
            log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder 跨境品销售属性更新完成, spuId: {}, 处理结果: {}", spuId, batchResult);
            return batchResult;
            
        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorder 跨境品销售属性更新异常, spuId: {}", spuId, e);
            throw e; // 重新抛出异常以触发事务回滚
        }
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean updateSkuSaleAttributeForCrossBorderWithoutUserInput(Long spuId, Long skuId, Long jdSkuId) {
        // 不处理销售属性
        if(SaleAttributeUNhandleContextHolder.get() !=null && SaleAttributeUNhandleContextHolder.get()){
            log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorderWithoutUserInput 不处理销售属性");
            return true;
        }
        log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorderWithoutUserInput 开始更新跨境品销售属性, spuId: {}, skuId: {}, jdSkuId: {}", 
                spuId, skuId, jdSkuId);

        // 1. 参数校验
        if (spuId == null || skuId == null || jdSkuId == null) {
            log.warn("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorderWithoutUserInput 入参不能为空");
            throw new BizException("入参不能为空");
        }
        
        try {
            // 2. 删除spu下的销售属性值及其关联关系
            this.deleteSpuSaleAttribute(spuId);
            // 3. 创建销售属性值及其关联关系
            return this.createSkuSaleAttributeForCrossBorder(spuId, skuId, jdSkuId);
        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.updateSkuSaleAttributeForCrossBorderWithoutUserInput 跨境品销售属性更新异常, spuId: {}, skuId: {}, jdSkuId: {}", 
                    spuId, skuId, jdSkuId, e);
            throw e;
        }
    }

    /**
     * 物理删除某个spu的销售属性
     * @param spuId
     */
    public void physicalDeleteSpuSaleAttribute(Long spuId) {
        // 1. 逻辑删除原有的销售属性值及其关联关系
        List<SaleAttributeValuePO> existingSaleAttributeValuePOList = saleAttributeValueAtomicService.getSaleAttributeValueBySpuId(spuId);
        if (!CollectionUtils.isEmpty(existingSaleAttributeValuePOList)) {
            log.info("SaleAttributeManageServiceImpl.physicalDeleteSpuSaleAttribute 物理删除原有销售属性值, spuId: {}, 数量: {}",
                    spuId, existingSaleAttributeValuePOList.size());

            List<Long> saleAttributeValueIds = existingSaleAttributeValuePOList.stream()
                    .map(SaleAttributeValuePO::getId)
                    .collect(Collectors.toList());

            // 逻辑删除SKU与销售属性值的关联关系
            List<SkuSaleAttributeValueRelationPO> relationPOList = skuSaleAttributeValueRelationAtomicService.getValidBySaleAttributeValueIds(new HashSet<>(saleAttributeValueIds));
            if (!CollectionUtils.isEmpty(relationPOList)) {
                List<SkuSaleAttributeValueRelationPO> updateRelationPOList = new ArrayList<>();
                relationPOList.forEach(relationPO -> {
                    SkuSaleAttributeValueRelationPO updateRelationPO = new SkuSaleAttributeValueRelationPO();
                    updateRelationPO.setId(relationPO.getId());
                    updateRelationPOList.add(updateRelationPO);
                });
                skuSaleAttributeValueRelationAtomicService.removeBatchByIds(updateRelationPOList);
            }

            // 逻辑删除销售属性值多语言
            List<SaleAttributeValueLangPO> langPOList = saleAttributeValueLangAtomicService.getValidBySaleAttributeValueIds(saleAttributeValueIds);
            if (!CollectionUtils.isEmpty(langPOList)) {
                List<SaleAttributeValueLangPO> updateLangPOList = new ArrayList<>();
                langPOList.forEach(langPO -> {
                    SaleAttributeValueLangPO updateLangPO = new SaleAttributeValueLangPO();
                    updateLangPO.setId(langPO.getId());
                    updateLangPOList.add(updateLangPO);
                });
                saleAttributeValueLangAtomicService.removeBatchByIds(updateLangPOList);
            }

            // 逻辑删除销售属性值
            List<SaleAttributeValuePO> updateValuePOList = new ArrayList<>();
            existingSaleAttributeValuePOList.forEach(saleAttributeValuePO -> {
                SaleAttributeValuePO updateValuePO = new SaleAttributeValuePO();
                updateValuePO.setId(saleAttributeValuePO.getId());
                updateValuePOList.add(updateValuePO);
            });
            saleAttributeValueAtomicService.removeBatchByIds(updateValuePOList);

            log.info("SaleAttributeManageServiceImpl.physicalDeleteSpuSaleAttribute 原有销售属性相关数据物理删除完成, spuId: {}", spuId);
        }
    }

    /**
     * 删除spu下的销售属性
     *
     * @param spuId
     */
    private void deleteSpuSaleAttribute(Long spuId) {
        // 1. 逻辑删除原有的销售属性值及其关联关系
        List<SaleAttributeValuePO> existingSaleAttributeValuePOList = saleAttributeValueAtomicService.getSaleAttributeValueBySpuId(spuId);
        if (!CollectionUtils.isEmpty(existingSaleAttributeValuePOList)) {
            log.info("SaleAttributeManageServiceImpl.deleteSpuSaleAttribute 删除原有销售属性值, spuId: {}, 数量: {}",
                    spuId, existingSaleAttributeValuePOList.size());

            List<Long> saleAttributeValueIds = existingSaleAttributeValuePOList.stream()
                    .map(SaleAttributeValuePO::getId)
                    .collect(Collectors.toList());

            // 逻辑删除SKU与销售属性值的关联关系
            List<SkuSaleAttributeValueRelationPO> relationPOList = skuSaleAttributeValueRelationAtomicService.getValidBySaleAttributeValueIds(new HashSet<>(saleAttributeValueIds));
            if (!CollectionUtils.isEmpty(relationPOList)) {
                List<SkuSaleAttributeValueRelationPO> updateRelationPOList = new ArrayList<>();
                relationPOList.forEach(relationPO -> {
                    SkuSaleAttributeValueRelationPO updateRelationPO = new SkuSaleAttributeValueRelationPO();
                    updateRelationPO.setId(relationPO.getId());
                    updateRelationPO.setYn(YnEnum.NO.getCode());
                    updateRelationPO.setUpdateTime(System.currentTimeMillis());
                    if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                        updateRelationPO.setUpdater(Constant.PIN_SYSTEM);
                    }
                    updateRelationPOList.add(updateRelationPO);
                });
                skuSaleAttributeValueRelationAtomicService.updateBatchById(updateRelationPOList);
            }

            // 逻辑删除销售属性值多语言
            List<SaleAttributeValueLangPO> langPOList = saleAttributeValueLangAtomicService.getValidBySaleAttributeValueIds(saleAttributeValueIds);
            if (!CollectionUtils.isEmpty(langPOList)) {
                List<SaleAttributeValueLangPO> updateLangPOList = new ArrayList<>();
                langPOList.forEach(langPO -> {
                    SaleAttributeValueLangPO updateLangPO = new SaleAttributeValueLangPO();
                    updateLangPO.setId(langPO.getId());
                    updateLangPO.setYn(YnEnum.NO.getCode());
                    updateLangPO.setUpdateTime(System.currentTimeMillis());
                    if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                        updateLangPO.setUpdater(Constant.PIN_SYSTEM);
                    }
                    updateLangPOList.add(updateLangPO);
                });
                saleAttributeValueLangAtomicService.updateBatchById(updateLangPOList);
            }

            // 逻辑删除销售属性值
            List<SaleAttributeValuePO> updateValuePOList = new ArrayList<>();
            existingSaleAttributeValuePOList.forEach(saleAttributeValuePO -> {
                SaleAttributeValuePO updateValuePO = new SaleAttributeValuePO();
                updateValuePO.setId(saleAttributeValuePO.getId());
                updateValuePO.setYn(YnEnum.NO.getCode());
                updateValuePO.setUpdateTime(System.currentTimeMillis());
                if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                    updateValuePO.setUpdater(Constant.PIN_SYSTEM);
                }
                updateValuePOList.add(updateValuePO);
            });
            saleAttributeValueAtomicService.updateBatchById(updateValuePOList);

            log.info("SaleAttributeManageServiceImpl.deleteSpuSaleAttribute 原有销售属性相关数据逻辑删除完成, spuId: {}", spuId);
        }
    }

    @Override
    public Boolean validateJdSkuSaleAttribute(Long jdSkuId, List<PropertyValueVO> storeSalePropertyList) {
        log.info("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 开始校验销售属性, jdSkuId: {}, storeSalePropertyList: {}", 
                jdSkuId, JSON.toJSONString(storeSalePropertyList));
        
        // 1. 参数校验
        if (jdSkuId == null || CollectionUtils.isEmpty(storeSalePropertyList)) {
            log.warn("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 参数不能为空, jdSkuId: {}", jdSkuId);
            throw new BizException("入参不能为空");
        }
        
        try {
            // 2. 通过jdskuid获取中台sku的销售属性数据
            JdProductDTO jdProductDTO = skuInfoRpcService.getSkuById(jdSkuId);
            if (jdProductDTO == null) {
                log.warn("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 中台SKU不存在, jdSkuId: {}", jdSkuId);
                throw new BizException("中台SKU不存在");
            }
            Long jdCatId = jdProductDTO.getUnLimitCid()!=null?Long.valueOf(jdProductDTO.getUnLimitCid()):null;
            if (jdCatId == null) {
                log.warn("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 中台SKU没有类目信息, jdSkuId: {}", jdSkuId);
                throw new BizException("中台SKU没有类目信息");
            }
            log.info("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 获取到中台SKU信息: {}", JSON.toJSONString(jdProductDTO));

            // 3. 获取中台sku的销售属性数据，分为图销和文销，用/拼接
            Result result = getResultJdSkuSaleAttribute(null, jdSkuId, jdProductDTO, null, null);
            String jdImageSaleStr = result.imageSaleStr; // 中台图销字符串
            String jdTextSaleStr = result.textSaleStr;   // 中台文销字符串
            log.info("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 中台销售属性, jdSkuId: {}, 图销: {}, 文销: {}", 
                    jdSkuId, jdImageSaleStr, jdTextSaleStr);

            // 4. 获取用户传入的销售属性ID列表，查询属性类型
            Set<Long> attributeIds = storeSalePropertyList.stream()
                    .filter(Objects::nonNull)
                    .filter(prop -> prop.getAttributeId() != null)
                    .map(PropertyValueVO::getAttributeId)
                    .collect(Collectors.toSet());
            
            if (attributeIds.isEmpty()) {
                log.warn("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 没有有效的属性ID, jdSkuId: {}", jdSkuId);
                throw new BizException("没有有效的属性ID");
            }
            
            // 5. 查询销售属性信息，获取属性类型
            List<SaleAttributePO> saleAttributePOList = saleAttributeAtomicService.getValidByIdList(new ArrayList<>(attributeIds));
            if (CollectionUtils.isEmpty(saleAttributePOList)) {
                log.warn("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 没有找到销售属性信息, attributeIds: {}", attributeIds);
                throw new BizException("没有找到销售属性信息");
            }
            
            // 6. 构建属性ID到属性类型的映射
            Map<Long, Integer> attributeTypeMap = saleAttributePOList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(SaleAttributePO::getId, SaleAttributePO::getSaleAttributeType));
            
            // 7. 分别验证图销和文销属性值
            String userImageSaleStr = null; // 用户传入的图销字符串
            String userTextSaleStr = null;  // 用户传入的文销字符串
            
            for (PropertyValueVO propertyValueVO : storeSalePropertyList) {
                Long attributeId = propertyValueVO.getAttributeId();
                Integer attributeType = attributeTypeMap.get(attributeId);
                
                if (attributeType == null) {
                    log.warn("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 未找到属性类型, attributeId: {}", attributeId);
                    throw new BizException("未找到属性类型");
                }
                
                // 提取中文属性值名称
                String chineseValueName = extractChineseNameFromLangList(propertyValueVO.getLangList());
                if (StringUtils.isBlank(chineseValueName)) {
                    log.warn("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 未找到中文属性值, attributeId: {}", attributeId);
                    throw new BizException("未找到中文属性值");
                }
                
                // 根据属性类型分类
                if (SaleAttributeTypeEnum.IMAGE.getCode().equals(attributeType)) {
                    // 图销属性
                    userImageSaleStr = chineseValueName;
                    log.info("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 用户图销属性值: {}", userImageSaleStr);
                } else if (SaleAttributeTypeEnum.TEXT.getCode().equals(attributeType)) {
                    // 文销属性
                    userTextSaleStr = chineseValueName;
                    log.info("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 用户文销属性值: {}", userTextSaleStr);
                } else {
                    log.warn("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 未知的属性类型, attributeId: {}, attributeType: {}", 
                            attributeId, attributeType);
                    throw new BizException("未知的属性类型");
                }
            }
            
            // 8. 比较图销属性值
            if (StringUtils.isNotBlank(jdImageSaleStr) && StringUtils.isNotBlank(userImageSaleStr)) {
                if (!jdImageSaleStr.equals(userImageSaleStr)) {
                    log.warn("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 图销属性值不匹配, jdSkuId: {}, 中台图销: {}, 用户图销: {}", 
                            jdSkuId, jdImageSaleStr, userImageSaleStr);
                    throw new BizException("图销属性值不匹配");
                }
                log.info("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 图销属性值校验通过, 值: {}", userImageSaleStr);
            }
            
            // 9. 比较文销属性值
            if (StringUtils.isNotBlank(jdTextSaleStr) && StringUtils.isNotBlank(userTextSaleStr)) {
                if (!jdTextSaleStr.equals(userTextSaleStr)) {
                    log.warn("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 文销属性值不匹配, jdSkuId: {}, 中台文销: {}, 用户文销: {}", 
                            jdSkuId, jdTextSaleStr, userTextSaleStr);
                    throw new BizException("文销属性值不匹配");
                }
                log.info("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 文销属性值校验通过, 值: {}", userTextSaleStr);
            }
            
            log.info("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 销售属性校验完成, jdSkuId: {}, 校验结果: 通过", jdSkuId);
            return true;
            
        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.validateJdSkuSaleAttribute 销售属性校验异常, jdSkuId: {}", jdSkuId, e);
            throw e;
        }
    }

    @Override
    public Boolean updateSkuSaleAttributeValueLangNameForCrossBorder(List<SkuVO> skuVOList) {
        // 不处理销售属性
        if(SaleAttributeUNhandleContextHolder.get() !=null && SaleAttributeUNhandleContextHolder.get()){
            log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeValueLangNameForCrossBorder 不处理销售属性");
            return true;
        }
        log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeValueLangNameForCrossBorder 开始处理跨境品销售属性值多语言更新, skuVOList: {}",
                JSON.toJSONString(skuVOList.size()));

        if (CollectionUtils.isEmpty(skuVOList)) {
            log.warn("SaleAttributeManageServiceImpl.updateSkuSaleAttributeValueLangNameForCrossBorder 参数为空");
            throw new BizException("skuVOList 不能为空");
        }

        // 对用户输入的销售属性值名称进行trim
        this.trimSkuVOAttibuteValueName(skuVOList);

        try {
            // 1. 从SkuVO列表中提取销售属性值的多语言信息
            List<SaleAttributeValueLangVO> saleAttributeValueLangVOList = new ArrayList<>();
            
            for (SkuVO skuVO : skuVOList) {
                List<PropertyValueVO> storeSalePropertyList = skuVO.getStoreSalePropertyList();
                if (CollectionUtils.isEmpty(storeSalePropertyList)) {
                    log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeValueLangNameForCrossBorder SKU没有销售属性, skuId: {}", skuVO.getSkuId());
                    continue;
                }
                
                // 遍历SKU的销售属性值
                for (PropertyValueVO propertyValueVO : storeSalePropertyList) {
                    Long attributeValueId = propertyValueVO.getAttributeValueId();
                    List<BaseLangVO> langList = propertyValueVO.getLangList();
                    
                    if (attributeValueId == null || CollectionUtils.isEmpty(langList)) {
                        log.warn("SaleAttributeManageServiceImpl.updateSkuSaleAttributeValueLangNameForCrossBorder 销售属性值ID或多语言信息为空, attributeValueId: {}, skuId: {}", 
                                attributeValueId, skuVO.getSkuId());
                        continue;
                    }
                    
                    // 将每个多语言信息转换为SaleAttributeValueLangVO
                    for (BaseLangVO baseLangVO : langList) {
                        if (StringUtils.isNotBlank(baseLangVO.getLang()) && StringUtils.isNotBlank(baseLangVO.getLangName())) {
                            SaleAttributeValueLangVO langVO = new SaleAttributeValueLangVO();
                            langVO.setSaleAttributeValueId(attributeValueId);
                            langVO.setLang(baseLangVO.getLang());
                            langVO.setLangName(baseLangVO.getLangName());
                            saleAttributeValueLangVOList.add(langVO);
                        }
                    }
                }
            }
            
            if (CollectionUtils.isEmpty(saleAttributeValueLangVOList)) {
                log.warn("SaleAttributeManageServiceImpl.updateSkuSaleAttributeValueLangNameForCrossBorder 没有提取到有效的多语言数据");
                // 没有传递langlist证明没有编辑跨境品多语言数据，无需处理，直接返回
                return true;
            }
            
            log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeValueLangNameForCrossBorder 提取多语言数据完成, 数量: {}", saleAttributeValueLangVOList.size());
            
            // 2. 调用多语言更新服务
            saleAttributeLangManageService.updateCrossBorderSaleAttributeValueLangs(saleAttributeValueLangVOList);
            
            log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeValueLangNameForCrossBorder 跨境品销售属性值多语言更新完成");
            return true;
            
        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.updateSkuSaleAttributeValueLangNameForCrossBorder 跨境品销售属性值多语言更新异常", e);
            throw e;
        }
    }

    private void updateSkuSaleAttributeSort(List<SkuVO> skuVOList){
        try {
            if(CollectionUtils.isEmpty(skuVOList)){
                return;
            }
            // 1、读取销售属性值的id以及sort
            Map<Long, Integer> attributeValueIdToSortMap = new HashMap<>();
            for(SkuVO skuVO : skuVOList){
                if(CollectionUtils.isNotEmpty(skuVO.getStoreSalePropertyList())){
                    for(PropertyValueVO propertyValueVO : skuVO.getStoreSalePropertyList()){
                        if(propertyValueVO.getAttributeValueId() != null && propertyValueVO.getSort() != null){
                            attributeValueIdToSortMap.put(propertyValueVO.getAttributeValueId(), propertyValueVO.getSort());
                        }
                    }
                }
            }
            // 2、根据销售属性值的id读取销售属性值，并设置sort
            if(MapUtils.isNotEmpty(attributeValueIdToSortMap)){
                List<SaleAttributeValuePO> updateSaleAttributeValuePOList = new ArrayList<>();
                List<SaleAttributeValuePO> saleAttributeValuePOList = saleAttributeValueAtomicService.getValidByIds(new ArrayList<>(attributeValueIdToSortMap.keySet()));
                if(CollectionUtils.isNotEmpty(saleAttributeValuePOList)){
                    for(SaleAttributeValuePO saleAttributeValuePO : saleAttributeValuePOList){
                        Integer sort = attributeValueIdToSortMap.get(saleAttributeValuePO.getId());
                        if(sort != null && !sort.equals(saleAttributeValuePO.getSort())){
                            SaleAttributeValuePO updateSaleAttributeValuePO = new SaleAttributeValuePO();
                            updateSaleAttributeValuePO.setId(saleAttributeValuePO.getId());
                            updateSaleAttributeValuePO.setSort(sort);
                            updateSaleAttributeValuePO.setUpdateTime(System.currentTimeMillis());
                            if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                                updateSaleAttributeValuePO.setUpdater(Constant.PIN_SYSTEM);
                            }
                            updateSaleAttributeValuePOList.add(updateSaleAttributeValuePO);
                        }
                    }
                }
                // 3、更新销售属性值的sort
                if(CollectionUtils.isNotEmpty(updateSaleAttributeValuePOList)){
                    log.info("SaleAttributeManageServiceImpl.updateSkuSaleAttributeSort 更新销售属性值的sort, updateSaleAttributeValuePOList: {}", JSON.toJSONString(updateSaleAttributeValuePOList));
                    saleAttributeValueAtomicService.updateBatchById(updateSaleAttributeValuePOList);
                }
            }
        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.updateSkuSaleAttributeSort 更新销售属性值的sort异常", e);
            // 更新sort失败，不抛出异常，不影响主流程
        }
    }


    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean createSkuSaleAttributeForLocalSkuDraft(Long spuId, List<SkuVO> createSkuVOList) {
        // 不处理销售属性
        if(SaleAttributeUNhandleContextHolder.get() !=null && SaleAttributeUNhandleContextHolder.get()){
            log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 不处理销售属性");
            return true;
        }
        log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 开始处理本土品销售属性, spuId: {}, createSkuVOList : {}", spuId,
                JSON.toJSONString(createSkuVOList));

        // 1. 参数校验
        if (spuId == null || CollectionUtils.isEmpty(createSkuVOList)) {
            log.warn("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 入参spuId: {}, createSkuVOList为空", spuId);
            throw new BizException("spuId: " + spuId + " 或 createSkuVOList 不能为空");
        }

        // 对用户输入的销售属性值名称进行trim
        this.trimSkuVOAttibuteValueName(createSkuVOList);

        // 更新sku销售属性的sort
        this.updateSkuSaleAttributeSort(createSkuVOList);

        // 去除已经存在关系的sku，只保留需要创建销售属性的sku
        List<SkuVO> skuVOList = new ArrayList<>();
        Set<String> skuKeys = createSkuVOList.stream().filter(skuVO -> skuVO!=null && skuVO.getSkuKey() != null).map(SkuVO::getSkuKey).collect(Collectors.toSet());
        List<SkuSaleAttributeValueRelationPO> skuSaleAttributeValueRelationPOList = skuSaleAttributeValueRelationAtomicService.getValidBySkuKeys(new ArrayList<>(skuKeys));
        if (!CollectionUtils.isEmpty(skuSaleAttributeValueRelationPOList)) {
            Set<String> existSkuKeys = skuSaleAttributeValueRelationPOList.stream().map(SkuSaleAttributeValueRelationPO::getSkuKey).collect(Collectors.toSet());
            skuVOList = createSkuVOList.stream().filter(skuVO -> skuVO!=null && skuVO.getSkuKey() != null && !existSkuKeys.contains(skuVO.getSkuKey())).collect(Collectors.toList());
        } else {
            skuVOList = createSkuVOList;
        }

        if (CollectionUtils.isEmpty(skuVOList)) {
            log.warn("SaleAttributeManageServiceImpl.saveSkuSaleAttributeForLocal 去除已存在销售属性的sku后，skuVOList为空");
            return false;
        }

        log.info("SaleAttributeManageServiceImpl.saveSkuSaleAttributeForLocal 需要创建销售属性的sku: {}", JSON.toJSONString(skuVOList));


//
//        // 将createSkuVOList分成两组，一组是更新销售属性关系的sku（所有销售属性值都有id（值id的个数与销售属性值的个数相等）），一组是新增sku（如果sku的销售属性值中存在没有值id的销售属性值，则认为是新增）
//        List<SkuVO> updateSkuVOListWithSaleAttributeValueIdList = new ArrayList<>();
//        List<SkuVO> createNewSkuList = new ArrayList<>();
//        for (SkuVO skuVO : createSkuVOList) {
//            int valudIdCount = 0;
//            if (CollectionUtils.isNotEmpty(skuVO.getStoreSalePropertyList())) {
//                for (PropertyValueVO propertyValueVO : skuVO.getStoreSalePropertyList()) {
//                    if (propertyValueVO.getAttributeValueId() != null) {
//                        valudIdCount++;
//                    }
//                }
//                if (valudIdCount == skuVO.getStoreSalePropertyList().size()) {
//                    // 如果sku的所有销售属性值都有id（值id的个数与销售属性值的个数相等）, 则认为需要更新销售属性关系的skuKey
//                    updateSkuVOListWithSaleAttributeValueIdList.add(skuVO);
//                } else {
//                    // 如果sku的销售属性值中存在没有值id的销售属性值，则认为是新增
//                    createNewSkuList.add(skuVO);
//                }
//            } else {
//                throw new BizException("销售属性值为空，请检查");
//            }
//        }
//        log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 销售属性创建 spuId: {}, createNewSkuList.size: {}, updateSkuVOListWithSaleAttributeValueIdList.size: {}", spuId,
//                createNewSkuList.size(), updateSkuVOListWithSaleAttributeValueIdList.size());
        // 注意顺序，必须先更新关系在创建新的销售属性，因为需要用valudId来找原有关系，如果先新增，会导致无法找到原关系，因为变多了，这个设计也是没有办法，因为关系id传不到前端
        // 更新已存在的sku的销售属性关系中的skuKey  在上层已经让skuKey不能改变，全局唯一，所以不存在更新skuKey的场景
//        if (CollectionUtils.isNotEmpty(updateSkuVOListWithSaleAttributeValueIdList)) {
//            this.updateSkuSaleAttributeRelationForLocalSkuDraft(spuId, updateSkuVOListWithSaleAttributeValueIdList);
//        }
        // 创建新sku的销售属性
        this.createSkuDraftSaleAttributes(spuId, skuVOList);
        return true;
    }

    /**
     * 更新销售属性关系中的skuKey
     * 
     * @param spuId
     * @param skuVOList
     * @return
     */
    @Deprecated
    private boolean updateSkuSaleAttributeRelationForLocalSkuDraft(Long spuId, List<SkuVO> skuVOList) {
        try {
            // 获取需要更新关系的saleAttributeValueId与skuKey list的map
            Map<Long, Set<String>> saleAttributeValueIdToSkuKeySetMap = new HashMap<>();
            for (SkuVO skuVO : skuVOList) {
                for (PropertyValueVO propertyValueVO : skuVO.getStoreSalePropertyList()) {
                    if (propertyValueVO.getAttributeValueId() != null) {
                        if (saleAttributeValueIdToSkuKeySetMap.containsKey(propertyValueVO.getAttributeValueId())) {
                            saleAttributeValueIdToSkuKeySetMap.get(propertyValueVO.getAttributeValueId()).add(skuVO.getSkuKey());
                        } else {
                            saleAttributeValueIdToSkuKeySetMap.put(propertyValueVO.getAttributeValueId(), new HashSet<>(Arrays.asList(skuVO.getSkuKey())));
                        }
                    }
                }
            }
            this.updateSkuKeyRelation(saleAttributeValueIdToSkuKeySetMap);
        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.updateSkuSaleAttributeRelationForLocalSkuDraft 更新销售属性关系中的skuKey异常, spuId: {}", spuId, e);
            throw e;
        }
        return true;
    }

    /**
     * 用于更新已存在的销售属性值的skuKey关系，注意，传入的销售属性值id必须在关系中已存在，否则会抛异常
     * @param saleAttributeValueIdToSkuKeySetMap
     */
    private void updateSkuKeyRelation(Map<Long, Set<String>> saleAttributeValueIdToSkuKeySetMap) {
        // 获取skuVOList中所有销售属性值id
        Set<Long> saleAttributeValueIdSet = saleAttributeValueIdToSkuKeySetMap.keySet();
        List<SkuSaleAttributeValueRelationPO> relationPOList = skuSaleAttributeValueRelationAtomicService.getValidBySaleAttributeValueIds(saleAttributeValueIdSet);
        if (CollectionUtils.isEmpty(relationPOList)) {
            log.warn("SaleAttributeManageServiceImpl.updateSkuSaleAttributeRelationForLocalSkuDraft 没有找到已存在的销售属性值");
            throw new BizException("没有找到已存在的销售属性值");
        }
        // 获取已存在的saleAttributeValueId与skuKey list的map
        Map<Long, List<SkuSaleAttributeValueRelationPO>> oldSaleAttributeValueIdToSkuKeySetMap = new HashMap<>();
        for (SkuSaleAttributeValueRelationPO relationPO : relationPOList) {
            if (relationPO.getSaleAttributeValueId() != null) {
                oldSaleAttributeValueIdToSkuKeySetMap.computeIfAbsent(relationPO.getSaleAttributeValueId(), k -> new ArrayList<>()).add(relationPO);
            }
        }
        List<SkuSaleAttributeValueRelationPO> updateRelationPOList = new ArrayList<>();
        // 判断saleAttributeValueIdToSkuKeySetMap中的saleAttributeValueId是否都在oldSaleAttributeValueIdToSkuKeySetMap中，且sku的个数是否相等
        for (Long saleAttributeValueId : saleAttributeValueIdToSkuKeySetMap.keySet()) {
            if (!oldSaleAttributeValueIdToSkuKeySetMap.containsKey(saleAttributeValueId)) {
                log.error("SaleAttributeManageServiceImpl.updateSkuSaleAttributeRelationForLocalSkuDraft 没有找到已存在的销售属性值, saleAttributeValueId: {}", saleAttributeValueId);
                throw new BizException("没有在原销售属性关系中找到对应的销售属性值, saleAttributeValueId: " + saleAttributeValueId);
            }
            List<SkuSaleAttributeValueRelationPO> oldSkuSaleAttributeValueRelationPO = oldSaleAttributeValueIdToSkuKeySetMap.get(saleAttributeValueId);
            Set<String> newSkuKeySet = saleAttributeValueIdToSkuKeySetMap.get(saleAttributeValueId);
            if (oldSkuSaleAttributeValueRelationPO == null || newSkuKeySet == null) {
                log.error("SaleAttributeManageServiceImpl.updateSkuSaleAttributeRelationForLocalSkuDraft 没有找到已存在的或传入的销售属性值对应的sku, saleAttributeValueId: {}", saleAttributeValueId);
                throw new BizException("没有找到已存在的或传入的销售属性值对应的sku, saleAttributeValueId: " + saleAttributeValueId);
            }
            if (oldSkuSaleAttributeValueRelationPO.size() != newSkuKeySet.size()) {
                log.error("SaleAttributeManageServiceImpl.updateSkuSaleAttributeRelationForLocalSkuDraft 销售属性值对应的sku个数不相等, saleAttributeValueId: {}", saleAttributeValueId);
                throw new BizException("销售属性值对应的sku个数不相等, saleAttributeValueId: " + saleAttributeValueId);
            }
            // 更新skuKey
            for (SkuSaleAttributeValueRelationPO relationPO : oldSkuSaleAttributeValueRelationPO) {
                SkuSaleAttributeValueRelationPO updateRelationPO = new SkuSaleAttributeValueRelationPO();
                updateRelationPO.setId(relationPO.getId());
                updateRelationPO.setSkuKey(newSkuKeySet.iterator().next());
                updateRelationPO.setUpdateTime(System.currentTimeMillis());
                if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                    updateRelationPO.setUpdater(Constant.PIN_SYSTEM);
                }
                updateRelationPOList.add(updateRelationPO);
                newSkuKeySet.remove(relationPO.getSkuKey());
            }
        }
        // 更新skuKey
        if (CollectionUtils.isNotEmpty(updateRelationPOList)) {
            skuSaleAttributeValueRelationAtomicService.updateBatchById(updateRelationPOList);
        }
    }

    /**
     * 创建销售属性
     * 
     * @param spuId
     * @param skuVOList
     * @return
     */
    public boolean createSkuDraftSaleAttributes(Long spuId, List<SkuVO> skuVOList) {
        try {
            // 新增也有完全新增和有一个销售属性是有销售属性值id的情况（即完全新建和编辑商品中新建）
            if (CollectionUtils.isEmpty(skuVOList)) {
                log.warn("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 去除已存在销售属性的sku后，skuVOList为空");
                return false;
            }

            log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 需要创建销售属性的sku: {}", JSON.toJSONString(skuVOList));

            // 2. 收集所有销售属性信息
            Set<Long> existingSaleAttributeIds = new HashSet<>();
            // 存储销售属性值的多语言信息，用于保存销售属性值多语言信息和sku与销售属性值关联关系
            Map<String, List<BaseLangVO>> attributeValueLangMap = new HashMap<>();
            Map<String, Set<String>> saleAttributeValueChineseNameSkuKeySetMap = new HashMap<>();
            // 收集销售属性中文值（中文必填），此处用set去重，即用销售属性值中文名做id，避免重复创建销售属性值
            Set<String> chineseAttributeValueNameSet = this.getChineseAttributeValueNameSetBySpuId(spuId);
            // 得到所有需要保存的销售属性值po、chineseAttributeValueNameSet、attributeValueLangMap、existingSaleAttributeIds（销售属性是否存在校验）
            List<SaleAttributeValuePO> saleAttributeValuePOList = new ArrayList<>();

            // 6. SKU与销售属性值ID list的关联关系, saleAttributeValueChineseNameSkuIdMap
            Map<Long, Set<String>> createSaleAttributeValueIdToSkuKeySetMap = new HashMap<>();

            for (SkuVO skuVO : skuVOList) {
                if (StringUtils.isBlank(skuVO.getSkuKey())) {
                    log.warn("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft SKU Key为空, 跳过处理: {}", JSON.toJSONString(skuVO));
                    throw new BizException("SKU Key不能为空:");
                }

                List<PropertyValueVO> storeSalePropertyList = skuVO.getStoreSalePropertyList();
                if (CollectionUtils.isEmpty(storeSalePropertyList)) {
                    log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft SKU没有销售属性, skuKey: {}", skuVO.getSkuKey());
                    throw new BizException("SKU没有销售属性, skuKey: " + skuVO.getSkuKey());
                }
                // 收集所有销售属性名称（从langList中获取zh的langName）和已存在的销售属性ID
                for (PropertyValueVO propertyValueVO : storeSalePropertyList) {
                    if (propertyValueVO.getAttributeId() != null) {
                        existingSaleAttributeIds.add(propertyValueVO.getAttributeId());
                    }
                    if(propertyValueVO.getAttributeValueId() == null){
                        // 完全新增
                        // 从langList中提取中文名称作为销售属性值名称，中文必填
                        String chineseAttributeValueName = extractChineseNameFromLangList(propertyValueVO.getLangList());
                        // 去重添加SaleAttributeValuePO，chineseAttributeValueNameSet中存储的是中文销售属性值名称，用于去重
                        if (StringUtils.isNotBlank(chineseAttributeValueName) && !chineseAttributeValueNameSet.contains(chineseAttributeValueName)) {
                            chineseAttributeValueNameSet.add(chineseAttributeValueName);
                            SaleAttributeValuePO saleAttributeValuePO = new SaleAttributeValuePO();
                            saleAttributeValuePO.setSpuId(spuId);
                            saleAttributeValuePO.setSaleAttributeValueName(chineseAttributeValueName);
                            saleAttributeValuePO.setSaleAttributeId(propertyValueVO.getAttributeId());
                            saleAttributeValuePO.setYn(YnEnum.YES.getCode());
                            saleAttributeValuePO.setSort(propertyValueVO.getSort());
                            long now = System.currentTimeMillis();
                            saleAttributeValuePO.setCreateTime(now);
                            saleAttributeValuePO.setUpdateTime(now);
                            // 如果登录上下文为空，则设置创建者和更新者为系统
                            if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                                saleAttributeValuePO.setCreator(Constant.PIN_SYSTEM);
                                saleAttributeValuePO.setUpdater(Constant.PIN_SYSTEM);
                            }
                            saleAttributeValuePOList.add(saleAttributeValuePO);
                            attributeValueLangMap.put(chineseAttributeValueName, propertyValueVO.getLangList());
                        }
                        if(StringUtils.isNotBlank(chineseAttributeValueName)){
                            saleAttributeValueChineseNameSkuKeySetMap.computeIfAbsent(chineseAttributeValueName, k -> new HashSet<>()).add(skuVO.getSkuKey());
                        }
                    } else {
                        // 从数据库查询销售属性值名称
                        SaleAttributeValuePO saleAttributeValuePO = saleAttributeValueAtomicService.getValidById(propertyValueVO.getAttributeValueId());
                        if (saleAttributeValuePO == null) {
                            log.error("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 没有找到销售属性值, saleAttributeValueId: {}", propertyValueVO.getAttributeValueId());
                            throw new BizException("没有找到销售属性值, saleAttributeValueId: " + propertyValueVO.getAttributeValueId());
                        }
                        // 添加到更新skuKey关系map
                        createSaleAttributeValueIdToSkuKeySetMap.computeIfAbsent(propertyValueVO.getAttributeValueId(), k -> new HashSet<>()).add(skuVO.getSkuKey());
                    }

                }
            }

            if (existingSaleAttributeIds.isEmpty()) {
                log.error("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 没有找到已存在的销售属性ID");
                throw new BizException("没有找到已存在的销售属性ID");
            }

            // 3. 验证销售属性是否存在
            List<SaleAttributePO> existingSaleAttributes = saleAttributeAtomicService.getValidByIdList(new ArrayList<>(existingSaleAttributeIds));
            if (CollectionUtils.isEmpty(existingSaleAttributes) || existingSaleAttributes.size() != existingSaleAttributeIds.size()) {
                log.error("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 销售属性不存在, 销售属性ID list: {}", JSON.toJSONString(existingSaleAttributeIds));
                throw new BizException("销售属性不存在, 销售属性ID list: " + JSON.toJSONString(existingSaleAttributeIds));
            }

            log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 验证销售属性成功, 找到销售属性数量: {}", existingSaleAttributes.size());

            // 4. 保存销售属性值
            List<SaleAttributeValuePO> savedSaleAttributeValuePOList = saleAttributeValueAtomicService.saveWithSortAndReturnWithId(spuId, saleAttributeValuePOList);
            if (CollectionUtils.isEmpty(savedSaleAttributeValuePOList)) {
                log.warn("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 没有保存任何销售属性值");
                throw new BizException("没有保存任何销售属性值");
            }
            log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 销售属性值保存成功, 保存的属性为: {}", JSON.toJSONString(savedSaleAttributeValuePOList));

            // 5. 保存销售属性值的多语言信息
            List<SaleAttributeValueLangPO> saleAttributeValueLangPOList = new ArrayList<>();

            for (SaleAttributeValuePO saleAttributeValuePO : saleAttributeValuePOList) {

                String langMapKey = saleAttributeValuePO.getSaleAttributeValueName();
                List<BaseLangVO> langList = attributeValueLangMap.get(langMapKey);

                if (!CollectionUtils.isEmpty(langList)) {
                    for (BaseLangVO langVO : langList) {
                        if (StringUtils.isNotBlank(langVO.getLang()) && StringUtils.isNotBlank(langVO.getLangName())) {
                            SaleAttributeValueLangPO langPO = SaleAttributeValueLangAtomicService.getSaleAttributeValueLangPO(saleAttributeValuePO.getId(), langVO.getLang(), langVO.getLangName());
                            saleAttributeValueLangPOList.add(langPO);
                        }
                    }
                }
            }
            if (!CollectionUtils.isEmpty(saleAttributeValueLangPOList)) {

                saleAttributeValueLangAtomicService.saveBatch(saleAttributeValueLangPOList);
                log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 销售属性值多语言信息保存成功, 数量: {}",
                        saleAttributeValueLangPOList.size());
            } else {
                log.warn("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 没有需要保存的销售属性值多语言信息");
            }


            for (SaleAttributeValuePO saleAttributeValuePO : saleAttributeValuePOList) {
                Set<String> skuKeySet = saleAttributeValueChineseNameSkuKeySetMap.get(saleAttributeValuePO.getSaleAttributeValueName());
                if (skuKeySet != null) {
                    createSaleAttributeValueIdToSkuKeySetMap.put(saleAttributeValuePO.getId(), skuKeySet);
                }
            }

            // 批量处理所有SKU的销售属性值关联关系
            boolean batchResult = false;
            if (!createSaleAttributeValueIdToSkuKeySetMap.isEmpty()) {
                try {
                    // 添加新的关系
                    batchResult = skuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuKey(createSaleAttributeValueIdToSkuKeySetMap);
                    if (batchResult) {
                        log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 批量处理SKU销售属性值关联关系成功, 处理SKU数量: {}",
                                createSaleAttributeValueIdToSkuKeySetMap.size());
                    } else {
                        log.error("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 批量处理SKU销售属性值关联关系失败");
                    }
                } catch (Exception e) {
                    log.error("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 批量处理SKU销售属性值关联关系异常", e);
                    throw e;
                }
            } else {
                log.warn("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 没有有效的SKU销售属性值数据需要处理");
            }

            log.info("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 本土品销售属性处理完成, 有效SKU数量: {}, 批量处理结果: {}",
                    skuVOList.size(), batchResult);

            return batchResult;

        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft 本土品销售属性处理异常", e);
            throw e; // 重新抛出异常以触发事务回滚
        }
    }

    /**
     * 从多语言列表中提取中文名称
     *
     * @param langList 多语言列表
     * @return 中文名称，如果未找到则返回null
     */
    private String extractChineseNameFromLangList(List<BaseLangVO> langList) {
        if (CollectionUtils.isEmpty(langList)) {
            return null;
        }

        for (BaseLangVO langVO : langList) {
            if (LangConstant.LANG_ZH.equals(langVO.getLang()) && StringUtils.isNotBlank(langVO.getLangName())) {
                return langVO.getLangName();
            }
        }

        return null;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean updateDraftSaleAttributeRelationsForLocalSkuApprovel(Map<String, Long> skuKeyToSkuIdMap) {
        log.info("SaleAttributeManageServiceImpl.updateDraftSaleAttributeRelationsToFormal 开始转换草稿关系为正式关系, SKU数量: {}",
                skuKeyToSkuIdMap != null ? skuKeyToSkuIdMap.size() : 0);

        if (MapUtils.isEmpty(skuKeyToSkuIdMap)) {
            log.warn("SaleAttributeManageServiceImpl.updateDraftSaleAttributeRelationsToFormal 入参为空");
            return true; // 没有数据也算成功
        }

        try {
            boolean result = skuSaleAttributeValueRelationAtomicService.updateDraftRelationsToFormal(skuKeyToSkuIdMap);
            log.info("SaleAttributeManageServiceImpl.updateDraftSaleAttributeRelationsToFormal 转换结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.updateDraftSaleAttributeRelationsToFormal 转换草稿关系异常", e);
            throw e; // 重新抛出异常以触发事务回滚
        }
    }

    @Override
    public Boolean transferSkuSaleAttribute(Long targetCatId, Long spuId) {
        log.info("SaleAttributeManageServiceImpl.transferSkuSaleAttribute 开始迁移销售属性, targetCatId: {}, spuId: {}", targetCatId, spuId);
        if (targetCatId == null || spuId == null) {
            log.warn("SaleAttributeManageServiceImpl.transferSkuSaleAttribute 入参不能为空");
            return false;
        }
        // 1、读取spu下的所有销售属性值
        List<SaleAttributeValuePO> saleAttributeValuePOList = saleAttributeValueAtomicService.getSaleAttributeValueBySpuId(spuId);
        if (CollectionUtils.isEmpty(saleAttributeValuePOList)) {
            log.info("SaleAttributeManageServiceImpl.transferSkuSaleAttribute spuId: {} 没有销售属性值", spuId);
            return false;
        }
        log.info("SaleAttributeManageServiceImpl.transferSkuSaleAttribute spuId: {} 读取到的销售属性值: {}", spuId, JSON.toJSONString(saleAttributeValuePOList));
        // 2、读取目标类目下的销售属性
        List<PropertyVO> tartetSaleAttributes = this.getCategorySaleAttributesByJdCatId(targetCatId, Constant.DEFAULT_LANG);
        // 根据attributeInputType建立与tartetSaleAttributes attributeId的map
        Map<Integer, Long> targetSaleAttributeTypeToIdMap = tartetSaleAttributes.stream().filter(Objects::nonNull).filter(e -> e.getAttributeInputType() != null).collect(Collectors.toMap(PropertyVO::getAttributeInputType, PropertyVO::getAttributeId));
        if (CollectionUtils.isEmpty(tartetSaleAttributes)) {
            log.info("SaleAttributeManageServiceImpl.transferSkuSaleAttribute targetCatId: {} 没有销售属性", targetCatId);
            return false;
        }
        // 3、根据saleAttributeValuePOList中的销售属性ID读取原销售属性信息
        List<Long> saleAtrributeIdList = saleAttributeValuePOList.stream().filter(Objects::nonNull).filter(e -> e.getSaleAttributeId() != null).map(SaleAttributeValuePO::getSaleAttributeId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saleAtrributeIdList)) {
            log.info("SaleAttributeManageServiceImpl.transferSkuSaleAttribute spuId: {} 没有销售属性id", spuId);
            return false;
        }
        List<SaleAttributePO> saleAttributePOList = saleAttributeAtomicService.getValidByIdList(saleAtrributeIdList);
        if (CollectionUtils.isEmpty(saleAttributePOList)) {
            log.info("SaleAttributeManageServiceImpl.transferSkuSaleAttribute spuId: {} 没有销售属性", spuId);
            return false;
        }
        // 4、按照SaleAttributePO saleAttributeType图销和文销分类，建立SaleAttributeValueId与类型的映射
        Map<Long, Integer> saleAttributeValueIdToTypeMap = new HashMap<>();
        Map<Long, SaleAttributeValuePO> saleAttributeValueIdToPOMap = new HashMap<>();
        for (SaleAttributeValuePO saleAttributeValuePO : saleAttributeValuePOList) {
            saleAttributeValueIdToPOMap.put(saleAttributeValuePO.getId(), saleAttributeValuePO);
            for (SaleAttributePO saleAttributePO : saleAttributePOList) {
                if (saleAttributePO.getId().equals(saleAttributeValuePO.getSaleAttributeId())) {
                    saleAttributeValueIdToTypeMap.put(saleAttributeValuePO.getId(), saleAttributePO.getSaleAttributeType());
                    break;
                }
            }
        }
        List<SaleAttributeValuePO> savedSaleAttributeValuePOList = new ArrayList<>();
        // 5、根据文销或图销，将saleAttributeValuePOList中的销售属性值绑定到目标类目的销售属性ID，用targetSaleAttributeTypeToIdMap
        for (SaleAttributeValuePO saleAttributeValuePO : saleAttributeValuePOList) {
            Long targetAttributeId = targetSaleAttributeTypeToIdMap.get(saleAttributeValueIdToTypeMap.get(saleAttributeValuePO.getId()));
            if (targetAttributeId != null) {
                SaleAttributeValuePO savePo = new SaleAttributeValuePO();
                savePo.setId(saleAttributeValuePO.getId());
                savePo.setSaleAttributeId(targetAttributeId);
                if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                    savePo.setUpdater(Constant.PIN_SYSTEM);
                }
                savedSaleAttributeValuePOList.add(savePo);
            }
        }
        // 6、保存到数据库
        saleAttributeValueAtomicService.updateBatchById(savedSaleAttributeValuePOList);
        return true;
    }

    @Override
    public List<AttributeVO> convertPropertyVo2AttributeVos(List<PropertyVO> inputs) {
        log.info("SaleAttributeManageServiceImpl.convertPropertyVo2AttributeVos inputs: {}", JSONObject.toJSONString(inputs));
        if (CollectionUtils.isEmpty(inputs)) {
            return Collections.emptyList();
        }
        List<AttributeVO> results = new ArrayList<>(inputs.size());
        for (PropertyVO input : inputs) {
            if (input == null) {
                continue;
            }
            // 属性值为空，跳过
            List<PropertyValueVO> propertyValueVOList = input.getPropertyValueVOList();
            if (CollectionUtils.isEmpty(propertyValueVOList)) {
                continue;
            }

            AttributeVO attributeVO = new AttributeVO();
            attributeVO.setId(input.getAttributeId());
            attributeVO.setAttributeType(input.getAttributeType());
            attributeVO.setAttributeInputType(input.getAttributeInputType());
            attributeVO.setComGroupId(input.getComGroupId());
            attributeVO.setShield(input.getShield());
            attributeVO.setIsQuJianZhi(input.getIsQuJianZhi());
            attributeVO.setLevel(input.getLevel());
            attributeVO.setSort(input.getSort());

            // 属性多语言处理
            List<BaseLangVO> inputLangList = input.getLangList();
            List<AttributeLangVO> langVOS = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(inputLangList)) {
                for (BaseLangVO baseLangVO : inputLangList) {
                    if (baseLangVO == null) {
                        continue;
                    }
                    AttributeLangVO attributeLangVO = new AttributeLangVO();
                    attributeLangVO.setLang(baseLangVO.getLang());
                    attributeLangVO.setLangName(baseLangVO.getLangName());
                    langVOS.add(attributeLangVO);
                }
            }
            attributeVO.setLangList(langVOS);

            // 属性值转换
            List<AttributeValueVO> attributeValueVOList = new ArrayList<>(propertyValueVOList.size());
            for (PropertyValueVO propertyValueVO : propertyValueVOList) {
                if (propertyValueVO == null) {
                    continue;
                }
                // 多语种为空，跳过
                List<BaseLangVO> langList = propertyValueVO.getLangList();
                if (CollectionUtils.isEmpty(langList)) {
                    continue;
                }

                AttributeValueVO attributeValueVO = new AttributeValueVO();
                attributeValueVO.setId(propertyValueVO.getAttributeValueId());
                attributeValueVO.setAttributeId(propertyValueVO.getAttributeId());
                attributeValueVO.setSort(propertyValueVO.getSort());

                // 多语种转换
                List<AttributeValueLangVO> langVOList = new ArrayList<>(langList.size());
                for (BaseLangVO baseLangVO : langList) {
                    if (baseLangVO == null) {
                        continue;
                    }
                    AttributeValueLangVO langVO = new AttributeValueLangVO();
                    langVO.setLang(baseLangVO.getLang());
                    langVO.setLangName(baseLangVO.getLangName());
                    langVOList.add(langVO);
                }
                // 多语种全空则跳过该属性值
                if (langVOList.isEmpty()) {
                    continue;
                }
                attributeValueVO.setLangList(langVOList);
                attributeValueVOList.add(attributeValueVO);
            }
            // 属性值列表全空则跳过该属性
            if (attributeValueVOList.isEmpty()) {
                continue;
            }
            attributeVO.setAttributeValueList(attributeValueVOList);

            results.add(attributeVO);
        }
        return results;
    }

    @Override
    public List<AttributeDTO> convertAttributeVOsToDTOs(List<AttributeVO> attributeVOS) {
        if (CollectionUtils.isEmpty(attributeVOS)) {
            return Collections.emptyList();
        }
        return attributeVOS.stream()
                .filter(Objects::nonNull)
                .filter(attributeVO -> {
                    boolean valid = CollectionUtils.isNotEmpty(attributeVO.getAttributeValueList());
                    if (!valid) {
                        log.warn("convertAttributeVOsToDTOs: attributeValueList is empty, attributeId={}", attributeVO.getId());
                    }
                    return valid;
                })
                .map(this::convertAttributeVOToDTO)
                .collect(Collectors.toList());
    }

    private AttributeDTO convertAttributeVOToDTO(AttributeVO attributeVO) {
        AttributeDTO attrDto = new AttributeDTO();
        attrDto.setId(attributeVO.getId());
        attrDto.setAttributeType(attributeVO.getAttributeType());
        attrDto.setAttributeInputType(attributeVO.getAttributeInputType());
        attrDto.setSort(attributeVO.getSort());
        attrDto.setLangList(AttributeConvert.INSTANCE.listAttributeLangVo2DTO(attributeVO.getLangList()));

        List<AttributeValueDTO> valueDTOList = attributeVO.getAttributeValueList().stream()
                .filter(Objects::nonNull)
                .map(valueVO -> {
                    AttributeValueDTO valueDTO = new AttributeValueDTO();
                    valueDTO.setId(valueVO.getId());
                    valueDTO.setSort(valueVO.getSort());
                    valueDTO.setLangList(AttributeConvert.INSTANCE.listValueLangVO2DTO(valueVO.getLangList()));
                    return valueDTO;
                })
                .collect(Collectors.toList());
        attrDto.setAttributeValueList(valueDTOList);
        return attrDto;
    }

    @Override
    public Map<String, List<PropertyValueVO>> getSkuDraftSaleAttributeDetailBySkuKeySet(Set<String> skuKeySet, String lang) {
        log.info("SaleAttributeManageServiceImpl.getSkuDraftSaleAttributeDetailBySkuKeySet skuKeySet: {}, lang: {}",
                JSON.toJSONString(skuKeySet), lang);
        if (CollectionUtils.isEmpty(skuKeySet) || StringUtils.isBlank(lang)) {
            log.warn("SaleAttributeManageServiceImpl.getSkuDraftSaleAttributeDetailBySkuKeySet skuKeySet为空或lang为空");
            return new HashMap<>();
        }
        try {
            return getSaleAttributeDetailBySkuKeySetOrSkuIdSet(null, skuKeySet, lang);
        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.getSkuDraftSaleAttributeDetailBySkuKeySet 获取草稿SKU销售属性失败, skuKeySet: {}, lang: {}",
                    JSON.toJSONString(skuKeySet), lang, e);
            return new HashMap<>();
        }
    }

    public Map<String, List<PropertyValueVO>> getSaleAttributeDetailBySkuKeySetOrSkuIdSet(Set<Long> skuIdSet, Set<String> skuKeySet, String lang) {
        log.info("SaleAttributeManageServiceImpl.getSaleAttributeDetailBySkuKeySetOrSkuIdSet skuKeySet: {}, skuIdSet: {}, lang: {}", 
                JSON.toJSONString(skuKeySet), JSON.toJSONString(skuIdSet),  lang);
        
        if ((CollectionUtils.isEmpty(skuKeySet) && CollectionUtils.isEmpty(skuIdSet)) || StringUtils.isBlank(lang)) {
            log.warn("SaleAttributeManageServiceImpl.getSaleAttributeDetailBySkuKeySetOrSkuIdSet skuKeySet和skuIdSet为空或lang为空");
            return new HashMap<>();
        }

        try {
            // 1. 根据skuKeySet或skuIdSet查询SKU与销售属性值的关系
            List<SkuSaleAttributeValueRelationPO> relationList = new ArrayList<>();
            Map<String, List<SkuSaleAttributeValueRelationPO>> skuKeyToRelationsMap = new HashMap<>();
            Map<Long, List<SkuSaleAttributeValueRelationPO>> skuIdToRelationsMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(skuIdSet)){
                log.info("SaleAttributeManageServiceImpl.getSaleAttributeDetailBySkuKeySetOrSkuIdSet 根据skuId查询，skuIdSet: {}", JSON.toJSONString(skuIdSet));
                relationList = skuSaleAttributeValueRelationAtomicService.getValidBySkuIds(new ArrayList<>(skuIdSet));
                // 2. 按skuKey分组关系数据
                skuIdToRelationsMap = relationList.stream()
                    .filter(relation -> relation.getSkuId() != null)
                    .collect(Collectors.groupingBy(SkuSaleAttributeValueRelationPO::getSkuId));
            } else {
                log.info("SaleAttributeManageServiceImpl.getSaleAttributeDetailBySkuKeySetOrSkuIdSet 根据skuKey查询，skuKeySet: {}", JSON.toJSONString(skuKeySet));
                relationList = skuSaleAttributeValueRelationAtomicService.getValidBySkuKeys(new ArrayList<>(skuKeySet));
                // 2. 按skuKey分组关系数据
                skuKeyToRelationsMap = relationList.stream()
                    .filter(relation -> StringUtils.isNotBlank(relation.getSkuKey()))
                    .collect(Collectors.groupingBy(SkuSaleAttributeValueRelationPO::getSkuKey));
            } 

            // 3. 提取所有销售属性值ID
            Set<Long> allSaleAttributeValueIds = relationList.stream()
                    .filter(relation -> relation.getSaleAttributeValueId() != null)
                    .map(SkuSaleAttributeValueRelationPO::getSaleAttributeValueId)
                    .collect(Collectors.toSet());

            if (allSaleAttributeValueIds.isEmpty()) {
                log.warn("SaleAttributeManageServiceImpl.getSaleAttributeDetailBySkuKeySetOrSkuIdSet 没有有效的销售属性值ID");
                return new HashMap<>();
            }

            // 4. 查询销售属性值详情
            List<SaleAttributeValuePO> saleAttributeValues = saleAttributeValueAtomicService.getValidByIds(new ArrayList<>(allSaleAttributeValueIds));
            if (CollectionUtils.isEmpty(saleAttributeValues)) {
                log.warn("SaleAttributeManageServiceImpl.getSaleAttributeDetailBySkuKeySetOrSkuIdSet 没有找到销售属性值数据");
                return new HashMap<>();
            }

            // 5. 提取销售属性ID并查询销售属性详情和多语言
            Set<Long> saleAttributeIds = saleAttributeValues.stream()
                    .filter(value -> value.getSaleAttributeId() != null)
                    .map(SaleAttributeValuePO::getSaleAttributeId)
                    .collect(Collectors.toSet());

            // 查询销售属性详情
            List<SaleAttributePO> saleAttributes = saleAttributeAtomicService.getValidByIdList(new ArrayList<>(saleAttributeIds));
            Map<Long, String> saleAttributeNameMap = saleAttributes.stream()
                    .filter(attr -> attr.getId() != null && StringUtils.isNotBlank(attr.getSaleAttributeName()))
                    .collect(Collectors.toMap(SaleAttributePO::getId, SaleAttributePO::getSaleAttributeName));

            // 获取saleAttributes id的sort，根据图销和文销分裂
            Map<Long, Integer> saleAttributeSortMap = saleAttributes.stream()
                    .filter(attr -> attr.getId() != null)
                    .collect(Collectors.toMap(SaleAttributePO::getId, SaleAttributePO::getSort));

            // 获取销售属性名称多语言（使用saleAttributeName作为code）
            List<String> saleAttributeNames = new ArrayList<>(saleAttributeNameMap.values());
            Map<String, String> saleAttributeNameByCodeMap = saleAttributeLangManageService.getSaleAttributeNameLangsByCode(saleAttributeNames, lang);

            // 构建ID到多语言名称的映射
            Map<Long, String> saleAttributeNameLangMap = new HashMap<>();
            for (Map.Entry<Long, String> entry : saleAttributeNameMap.entrySet()) {
                Long attributeId = entry.getKey();
                String attributeName = entry.getValue();
                String attributeLangName = saleAttributeNameByCodeMap.get(attributeName);
                // 如果没有找到多语言名称，使用原名称
                saleAttributeNameLangMap.put(attributeId, StringUtils.isNotBlank(attributeLangName) ? attributeLangName : attributeName);
            }

            // 6. 查询销售属性值的多语言
            Map<Long, List<SaleAttributeValueLangVO>> saleAttributeValueLangsMap = saleAttributeLangManageService.getSaleAttributeValueNameLangsByIds(new ArrayList<>(allSaleAttributeValueIds));

            // 7. 构建销售属性值ID到PropertyValueVO的映射
            Map<Long, PropertyValueVO> saleAttributeValueIdToPropertyValueVOMap = buildPropertyValueVOMap(saleAttributeValues, saleAttributeNameLangMap, saleAttributeValueLangsMap, saleAttributeSortMap, lang);

            // 8. 为每个skuKey构建PropertyValueVO列表
            Map<String, List<PropertyValueVO>> result = new HashMap<>();
            if (CollectionUtils.isNotEmpty(skuKeySet)) {
                for (String skuKey : skuKeySet) {
                    List<SkuSaleAttributeValueRelationPO> skuRelations = skuKeyToRelationsMap.getOrDefault(skuKey, new ArrayList<>());
                    List<PropertyValueVO> propertyValueVOList = skuRelations.stream()
                            .filter(relation -> relation.getSaleAttributeValueId() != null)
                            .map(relation -> saleAttributeValueIdToPropertyValueVOMap.get(relation.getSaleAttributeValueId()))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    // 排序
                    try {
                        propertyValueVOList.sort(Comparator.comparing(PropertyValueVO::getSort));
                    } catch (Exception e) {
                        log.error("SaleAttributeManageServiceImpl.getSaleAttributeDetailBySkuKeySetOrSkuIdSet 排序销售属性值失败, skuKey: {}", skuKey, e);
                    }

                    result.put(skuKey, propertyValueVOList);
                }
            } else {
                for (Long skuId : skuIdSet) {
                    List<SkuSaleAttributeValueRelationPO> skuRelations = skuIdToRelationsMap.getOrDefault(skuId, new ArrayList<>());
                    List<PropertyValueVO> propertyValueVOList = skuRelations.stream()
                            .filter(relation -> relation.getSaleAttributeValueId() != null)
                            .map(relation -> saleAttributeValueIdToPropertyValueVOMap.get(relation.getSaleAttributeValueId()))
                            .filter(Objects::nonNull)   
                            .collect(Collectors.toList());
                    result.put(String.valueOf(skuId), propertyValueVOList);
                }
            }

            log.info("SaleAttributeManageServiceImpl.getSaleAttributeDetailBySkuKeySetOrSkuIdSet 获取SKU销售属性成功, 结果数量: {}",
                    result.size());
            return result;

        } catch (Exception e) {
            log.error("SaleAttributeManageServiceImpl.getSaleAttributeDetailBySkuKeySetOrSkuIdSet 获取SKU销售属性失败, skuKeySet: {}, skuIdSet: {},     lang: {}",
                    JSON.toJSONString(skuKeySet), JSON.toJSONString(skuIdSet), lang, e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<Long, List<PropertyValueVO>> getSkuSaleAttributeDetailBySkuIdSet(Set<Long> skuIdSet, String lang) {
        log.info("SaleAttributeManageServiceImpl.getSkuSaleAttributeDetailBySkuIdSet skuIdSet: {}, lang: {}",
                JSON.toJSONString(skuIdSet), lang);
        // 
        Map<String, List<PropertyValueVO>> result = getSaleAttributeDetailBySkuKeySetOrSkuIdSet(skuIdSet, null, lang);
        if(MapUtils.isEmpty(result)){
            return new HashMap<>();
        }
        // 将String类型的key转换为Long类型
        Map<Long, List<PropertyValueVO>> longResult = new HashMap<>();
        for (Map.Entry<String, List<PropertyValueVO>> entry : result.entrySet()) {
            if(StringUtils.isNotBlank(entry.getKey())){
                longResult.put(Long.parseLong(entry.getKey()), entry.getValue());
            }
        }
        return longResult;
    }

    /**
     * 构建销售属性值ID到PropertyValueVO的映射
     * @param saleAttributeValues 销售属性值列表
     * @param saleAttributeNameMap 销售属性名称映射
     * @param saleAttributeValueLangsMap 销售属性值多语言映射
     * @param lang 目标语言
     * @return 销售属性值ID到PropertyValueVO的映射
     */
    private Map<Long, PropertyValueVO> buildPropertyValueVOMap(List<SaleAttributeValuePO> saleAttributeValues,
                                                              Map<Long, String> saleAttributeNameMap,
                                                              Map<Long, List<SaleAttributeValueLangVO>> saleAttributeValueLangsMap, Map<Long, Integer> saleAttributeSortMap,
                                                              String lang) {
        Map<Long, PropertyValueVO> resultMap = new HashMap<>();
        
        for (SaleAttributeValuePO saleAttributeValue : saleAttributeValues) {
            if (saleAttributeValue == null || saleAttributeValue.getId() == null) {
                continue;
            }
            
            PropertyValueVO propertyValueVO = new PropertyValueVO();
            
            // 设置销售属性值ID
            propertyValueVO.setAttributeValueId(saleAttributeValue.getId());
            
            // 设置销售属性ID
            propertyValueVO.setAttributeId(saleAttributeValue.getSaleAttributeId());
            
            // 设置销售属性名称（指定语言）
            String saleAttributeName = saleAttributeNameMap.get(saleAttributeValue.getSaleAttributeId());
            propertyValueVO.setAttributeName(saleAttributeName);
            
            // 设置销售属性值名称（指定语言）
            String saleAttributeValueName = getSaleAttributeValueNameByLang(saleAttributeValue.getId(), saleAttributeValueLangsMap, lang);
            propertyValueVO.setAttributeValueName(saleAttributeValueName);
            
            // 设置排序，此处设置的应该是图销和文销的属性，而不是属性值的顺序
            if (saleAttributeSortMap != null) {
                propertyValueVO.setSort(saleAttributeSortMap.getOrDefault(saleAttributeValue.getSaleAttributeId(), 0));
            }

            resultMap.put(saleAttributeValue.getId(), propertyValueVO);
        }
        
        return resultMap;
    }

    /**
     * 根据语言获取销售属性值名称
     * @param saleAttributeValueId 销售属性值ID
     * @param saleAttributeValueLangsMap 销售属性值多语言映射
     * @param lang 目标语言
     * @return 销售属性值名称
     */
    private String getSaleAttributeValueNameByLang(Long saleAttributeValueId,
                                                  Map<Long, List<SaleAttributeValueLangVO>> saleAttributeValueLangsMap,
                                                  String lang) {
        List<SaleAttributeValueLangVO> langList = saleAttributeValueLangsMap.get(saleAttributeValueId);
        if (CollectionUtils.isEmpty(langList)) {
            return null;
        }
        
        // 查找指定语言的名称
        for (SaleAttributeValueLangVO langVO : langList) {
            if (langVO != null && lang.equals(langVO.getLang())) {
                return langVO.getLangName();
            }
        }
        
        // 如果没有找到指定语言，返回第一个可用的名称
        return langList.get(0).getLangName();
    }



} 