package com.jdi.isc.product.soa.service.manage.approveorder;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.biz.component.api.joysky.res.JoySkyDetailInfoDTO;
import com.jdi.isc.product.soa.api.applyinfo.req.ApplyInfoBatchReqDTO;
import com.jdi.isc.product.soa.api.applyinfo.res.ApplyInfoBatchResDTO;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderAuditMqDTO;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderCreateMqDTO;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderMqDTO;
import com.jdi.isc.product.soa.api.approveorder.mq.JoySkyBizApprovalResultMqDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderSaveReqDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderPageQueryDTO;
import com.jdi.isc.product.soa.api.approveorder.res.ApproveOrderPageResDTO;
import com.jdi.isc.product.soa.api.approveorder.res.ApproveOrderSaveResDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiReqDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiResDTO;
import com.jdi.isc.product.soa.domain.approveorder.res.ApproveOrderResVO;

/**
 * 商品审核数据维护服务
 *
 * <AUTHOR>
 */
public interface ApproveOrderManageService {

    /**
     * 详情
     *
     * @param id 对象ID
     * @return VO对象 approve order res vo
     */
    ApproveOrderResVO detail(Long id);

    /**
     * 创建审计API记录
     *
     * @param input 审计API请求数据传输对象，包含创建审计记录所需的请求参数
     * @return 包含审计API响应数据传输对象的数据响应实体 ，封装操作结果及返回数据
     */
    DataResponse<ApproveOrderSaveResDTO> create(ApproveOrderSaveReqDTO input);

    /**
     * 审核API请求并返回审核结果
     *
     * @param input 包含审核请求信息的DTO对象
     * @return 包含审核结果响应的DataResponse对象 data response
     */
    DataResponse<AuditApiResDTO> audit(AuditApiReqDTO input);

    /**
     * 创建审核表后mq回调.
     *
     * @param input the input
     * @return the data response
     */
    DataResponse<String> handleCreateMessage(ApproveOrderMqDTO<ApproveOrderCreateMqDTO> input);

    /**
     * 审核数据后mq回调.
     *
     * @param input the input
     * @return the data response
     */
    DataResponse<String> handleAuditMessage(ApproveOrderMqDTO<ApproveOrderAuditMqDTO> input);

    /**
     * 批量处理JoySky业务审批结果
     * @param approve JoySky业务审批结果MQ传输对象，包含审批相关信息
     */
    void batchJoySkyApprove(JoySkyBizApprovalResultMqDTO approve);

    /**
     * 分页查询审批订单列表
     * @param input 审批订单分页查询参数DTO，包含分页及查询条件
     * @return 包含分页信息的审批订单响应DTO列表
     */
    PageInfo<ApproveOrderPageResDTO> listWithPage(ApproveOrderPageQueryDTO input);

    /**
     * 批量更新不可售阈值
     * @param input 包含批量更新不可售阈值请求信息的DTO对象
     * @return 包含批量更新结果响应的数据封装对象
     */
    DataResponse<ApplyInfoBatchResDTO> updateUnsellableThreshold(ApplyInfoBatchReqDTO input);

    /**
     * 处理超时数据并返回处理结果
     * @return 包含处理结果的响应对象，布尔值表示处理是否成功
     */
    DataResponse<Boolean> handleTimeoutData();
}