package com.jdi.isc.product.soa.service.adapter.mapstruct.countryTax;

import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.BrSkuTaxPO;
import com.jdi.isc.product.soa.price.api.price.req.BrSkuTaxVO;
import com.jdi.isc.product.soa.price.api.price.req.CrossBorderImportTaxReqDTO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 跨境税率查询对象转换
 * <AUTHOR>
 * @datete 20250311
 */
@Mapper
public interface CrossBorderImportTaxConvert {

    CrossBorderImportTaxConvert INSTANCE = Mappers.getMapper(CrossBorderImportTaxConvert.class);

    @InheritConfiguration
    CrossBorderImportTaxReqVO dto2Vo(CrossBorderImportTaxReqDTO dto);


}
