package com.jdi.isc.product.soa.service.manage.customerSku;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.enums.CustomerSkuDataSourceTypeEnums;
import com.jdi.isc.product.soa.api.customerSku.req.CustomerSkuPriceWarningDTO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceWarningPageReqVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceWarningVO;

import java.util.List;

/**
 * @Description: sku客制化价格预警表数据维护服务
 * @Author: zhaokun51
 * @Date: 2025/03/17 16:51
 **/

public interface CustomerSkuPriceWarningManageService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    PageInfo<CustomerSkuPriceWarningVO> pageSearch(CustomerSkuPriceWarningPageReqVO input);

    /**
     * 从客户端 SKU 数据源保存或更新商品详细信息。
     * @param detailDraftId 商品详细信息草稿 ID
     * @param dataSourceTypeEnums 客户端 SKU 数据源类型枚举
     * @return 是否成功保存或更新
     */
    Boolean saveOrUpdateFromCustomerSku(Long detailDraftId, CustomerSkuDataSourceTypeEnums dataSourceTypeEnums);

    /**
     * 从成本价保存或更新数据
     * @param agreementPriceId 协议价格ID
     * @param dataSourceTypeEnums 数据源类型枚举
     * @return 操作是否成功
     */
    Boolean saveOrUpdateFromCostPrice(Long agreementPriceId, CustomerSkuDataSourceTypeEnums dataSourceTypeEnums);

    /**
     * 更新SKU客制化价格预警， yn=0.
     *
     * @param skuIds  the sku ids
     * @param updater the updater
     */
    void updateYn(List<Long> skuIds, String updater);

    /**
     * 根据ID列表查询客户商品价格预警信息
     * @param ids 客户商品ID列表
     * @return 包含客户商品价格预警信息的DTO列表
     */
    List<CustomerSkuPriceWarningDTO> listWarningsByIds(List<Long> ids);
}
