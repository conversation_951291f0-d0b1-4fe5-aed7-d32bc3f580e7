package com.jdi.isc.product.soa.service.manage.customerSku;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.enums.CustomerSkuDataSourceTypeEnums;
import com.jdi.isc.product.soa.api.customerSku.req.CustomerSkuPriceWarningDTO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceWarningPageReqVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceWarningVO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceWarningPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.po.PriceLogPO;

import java.util.List;

/**
 * @Description: sku客制化价格预警表数据维护服务
 * @Author: zhaokun51
 * @Date: 2025/03/17 16:51
 **/

public interface CustomerSkuPriceWarningManageService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    PageInfo<CustomerSkuPriceWarningVO> pageSearch(CustomerSkuPriceWarningPageReqVO input);

    /**
     * 从客户端 SKU 数据源保存或更新商品详细信息。
     * @param detailDraftId 商品详细信息草稿 ID
     * @param dataSourceTypeEnums 客户端 SKU 数据源类型枚举
     * @return 是否成功保存或更新
     */
    Boolean saveOrUpdateFromCustomerSku(Long detailDraftId, CustomerSkuDataSourceTypeEnums dataSourceTypeEnums);

    /**
     * 从成本价保存或更新数据
     * @param agreementPriceId 协议价格ID
     * @param dataSourceTypeEnums 数据源类型枚举
     * @return 操作是否成功
     */
    Boolean saveOrUpdateFromCostPrice(Long agreementPriceId, CustomerSkuDataSourceTypeEnums dataSourceTypeEnums);

    /**
     * 更新SKU客制化价格预警， yn=0.
     *
     * @param skuIds  the sku ids
     * @param updater the updater
     */
    void updateYn(List<Long> skuIds, String updater);

    /**
     * 根据ID列表查询客户商品价格预警信息
     * @param ids 客户商品ID列表
     * @return 包含客户商品价格预警信息的DTO列表
     */
    List<CustomerSkuPriceWarningDTO> listWarningsByIds(List<Long> ids);

    /**
     * 根据参数获取或创建客户商品价格预警PO对象
     * @param param 客户商品价格明细PO对象，包含商品价格相关信息
     * @param dataSourceTypeEnums 商品数据源类型枚举，标识价格数据的来源类型
     * @return 客户商品价格预警PO对象，包含价格预警相关信息
     */
    CustomerSkuPriceWarningPO getOrCreateSkuPriceWarningPO(CustomerSkuPriceDetailPO param,CustomerSkuDataSourceTypeEnums dataSourceTypeEnums);

    /**
     * 设置利润率及状态信息
     * @param customerSkuPriceWarningPO 客户SKU价格预警信息对象
     * @param countryAgreementPricePO 国家协议价格信息对象
     * @param customerSkuPriceDetailPO 客户SKU价格明细信息对象
     */
    void setProfitRateAndStatus(CustomerSkuPriceWarningPO customerSkuPriceWarningPO, CountryAgreementPricePO countryAgreementPricePO, CustomerSkuPriceDetailPO customerSkuPriceDetailPO);

    /**
     * 使用客户SKU价格预警数据填充客户SKU价格明细数据
     * @param customerSkuPriceDetailPO 需要被填充的客户SKU价格明细数据对象
     * @param customerSkuPriceWarningPO 包含预警信息的客户SKU价格预警数据对象
     */
    void fillData(CustomerSkuPriceDetailPO customerSkuPriceDetailPO, CustomerSkuPriceWarningPO customerSkuPriceWarningPO);

    /**
     * 根据客户商品价格预警信息和SKU ID获取价格日志记录
     * @param customerSkuPriceWarningPO 客户商品价格预警信息，包含价格预警相关数据
     * @param skuId 商品SKU ID，用于标识具体商品
     * @return 返回与预警信息和SKU对应的价格日志记录对象
     */
    PriceLogPO getPriceLogPO(CustomerSkuPriceWarningPO customerSkuPriceWarningPO, Long skuId);
}
