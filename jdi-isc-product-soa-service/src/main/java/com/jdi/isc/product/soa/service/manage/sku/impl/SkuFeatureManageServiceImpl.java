package com.jdi.isc.product.soa.service.manage.sku.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.api.common.enums.PurchaseModelTypeEnum;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.enums.StatusEnum;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehousePO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehouseSkuPO;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseSkuAtomicService;
import com.jdi.isc.product.soa.service.manage.sku.SkuFeatureManageService;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description：SkuFeatureManageServiceImpl
 * @Date 2025-04-09
 */
@Slf4j
@Service
public class SkuFeatureManageServiceImpl implements SkuFeatureManageService {

    @Resource
    private WarehouseSkuAtomicService warehouseSkuAtomicService;

    @Resource
    private WarehouseAtomicService warehouseAtomicService;

    @Override
    @PFTracing
    public Map<Long, Integer> querySkuPurchaseModel(Set<Long> skuIds, String countryCode) {
        Map<Long, Integer> resultMap = new HashMap<>();
        // 查询SKU和备货仓绑定关系
        List<WarehouseSkuPO> warehouseSkuPOList = warehouseSkuAtomicService.getBaseMapper().selectList(Wrappers.lambdaQuery(WarehouseSkuPO.class)
                .in(WarehouseSkuPO::getSkuId, skuIds).eq(WarehouseSkuPO::getBindStatus, Constant.ONE).eq(WarehouseSkuPO::getYn, YnEnum.YES.getCode()));
        // 无绑定仓关系时，返回非备货模式
        if (CollectionUtils.isEmpty(warehouseSkuPOList)) {
            skuIds.forEach(id -> resultMap.put(id, PurchaseModelTypeEnum.DIRECT_SUPPLY.getCode()));
            return resultMap;
        }

        // 商品集合绑定的所有备货仓集合
        Set<Long> warehouseIds = warehouseSkuPOList.stream().filter(Objects::nonNull).map(WarehouseSkuPO::getWarehouseId).collect(Collectors.toSet());
        // 备货仓集合为空，返回非备货模式
        if (CollectionUtils.isEmpty(warehouseIds)) {
            skuIds.forEach(id -> resultMap.put(id, PurchaseModelTypeEnum.DIRECT_SUPPLY.getCode()));
            return resultMap;
        }
        // 查询备货仓库列表
        List<WarehousePO> warehousePOList = warehouseAtomicService.getBaseMapper()
                .selectList(Wrappers.lambdaQuery(WarehousePO.class).in(WarehousePO::getId, warehouseIds)
                        .eq(WarehousePO::getCountryCode, countryCode)
                        .eq(WarehousePO::getYn, YnEnum.YES.getCode()).eq(WarehousePO::getStatus, StatusEnum.ENABLE.getCode())
                        .eq(WarehousePO::getType, 0));

        // 备货仓ID和信息映射
        Map<Long, WarehousePO> warehousePOMap = Optional.ofNullable(warehousePOList).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(WarehousePO::getId, Function.identity()));
        // 如果SKU和仓库绑定关系存在 仓库映射中包括商品绑定的仓ID 则返回1 否则返回0
        Map<Long, List<WarehouseSkuPO>> warehouseSkuMap = warehouseSkuPOList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(WarehouseSkuPO::getSkuId));

        for (Long skuId : skuIds) {
            if (!warehouseSkuMap.containsKey(skuId)) {
                resultMap.put(skuId, PurchaseModelTypeEnum.DIRECT_SUPPLY.getCode());
            }else if (CollectionUtils.isNotEmpty(warehouseSkuMap.get(skuId))) {
                List<WarehouseSkuPO> list = warehouseSkuMap.get(skuId);

                //过滤出当前国家的商品-仓绑定关系
                List<WarehouseSkuPO> warehouseSkuListOfCountry = list.stream()
                        .filter(po -> warehousePOMap.containsKey(po.getWarehouseId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(warehouseSkuListOfCountry)) {
                    //跨境备货品，绑了多个国家的仓，但在当前国家属于非备货模式
                    resultMap.put(skuId, PurchaseModelTypeEnum.DIRECT_SUPPLY.getCode());
                    continue;
                }

                //todo: 一个sku当前仅属于一个备货仓，如果一个sku同一个国家可绑定多仓时此处需要修改
                int purchaseModel = warehouseSkuListOfCountry.get(0).getPurchaseModel();
                int skuPurchaseModelType;
                if (purchaseModel == PurchaseModelTypeEnum.CONSIGNMENT.getCode()) {
                    skuPurchaseModelType = purchaseModel;
                } else {
                    skuPurchaseModelType = BooleanUtils.toInteger(list.stream().anyMatch(po -> warehousePOMap.containsKey(po.getWarehouseId())));
                }

                PurchaseModelTypeEnum purchaseModelTypeEnum = PurchaseModelTypeEnum.forCode(skuPurchaseModelType);
                resultMap.put(skuId, CollectionUtils.isEmpty(list) ? PurchaseModelTypeEnum.DIRECT_SUPPLY.getCode() : purchaseModelTypeEnum.getCode());
            }
        }
        return resultMap;
    }
}
