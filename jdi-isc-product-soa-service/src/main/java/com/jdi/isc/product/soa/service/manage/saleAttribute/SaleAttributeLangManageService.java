package com.jdi.isc.product.soa.service.manage.saleAttribute;

import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeValueLangVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeValueVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 销售属性多语言管理服务
 * 用来管理销售属性名称的多语言和销售属性值的多语言
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface SaleAttributeLangManageService {


    /**
     * 获取机翻结果，key是待翻译中文，value是要翻译的语言langList，返回值key是待翻译中文，value是翻译结果（key：lang，value：翻译值），返回结果中包含中文
     *
     * @param needTranslateMap
     * @return
     */
    public Map<String, Map<String, String>> getTranslateResult(Map<String, Set<String>> needTranslateMap);

    /**
     * 根据SaleAttributeCode list获取销售属性名称的数据库存储的多语言翻译接口
     * @param saleAttributeCodeList 销售属性编码列表
     * @param lang 需要的语言
     * @return Map<String,String> key：saleAttributeCode、value：对应的语言值langName
     */
    Map<String, String> getSaleAttributeNameLangsByCode(List<String> saleAttributeCodeList, String lang);

    /**
     * 根据saleAttributeValueId list读取销售属性值的数据库多语言翻译接口
     * @param saleAttributeValueIdList 销售属性值ID列表
     * @return Map<Long, List<SaleAttributeValueLangVO>> key:saleAttributeValueId、value：多语言翻译list
     */
    Map<Long, List<SaleAttributeValueLangVO>> getSaleAttributeValueNameLangsByIds(List<Long> saleAttributeValueIdList);

    /**
     * 本土发品prepare阶段：根据langList获取saleAttributeNameSet（为中文CN）的对应的翻译、不存在的进行机翻，然后存储到数据库
     * @param saleAttributeNameSet 销售属性名称集合（中文）
     * @param lang 目标语言
     * @return Map<String, String> key：saleAttributeName、value：langname
     */
    Map<String, String> getLocalTranslateSaleAttributeNamesByLang(Set<String> saleAttributeNameSet, String lang);

    /**
     * 跨境品销售属性值SaleAttributeValueLang翻译保存，只有在新建跨境品的时候才调用
     * @param saleAttributeValueVOList 从中台拉取的销售属性值，已经存入了数据库
     */
    void createCrossBorderSaleAttributeValueLangs(List<SaleAttributeValueVO> saleAttributeValueVOList);

    /**
     * 跨境品销售属性值编辑时，SaleAttributeValueLang翻译保存，仅更新非中文部分
     * @param saleAttributeValueLangVOList 用户输入的销售属性值多语言列表
     */
    void updateCrossBorderSaleAttributeValueLangs(List<SaleAttributeValueLangVO> saleAttributeValueLangVOList);

    /**
     * 根据SaleAttributeCode list获取销售属性名称的多语言翻译接口（支持多语言列表）
     * @param saleAttributeCodeList 销售属性编码列表
     * @param langList 需要的语言列表
     * @return Map<String, Map<String, String>> key1：saleAttributeCode、key2：lang、value：对应的语言值langName
     */
    Map<String, Map<String, String>> getSaleAttributeNameLangsByCodeList(List<String> saleAttributeCodeList, List<String> langList);
} 