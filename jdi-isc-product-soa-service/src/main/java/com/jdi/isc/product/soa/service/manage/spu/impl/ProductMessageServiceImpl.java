package com.jdi.isc.product.soa.service.manage.spu.impl;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.domain.enums.attribute.GlobalAttributeEnum;
import com.jdi.isc.product.soa.domain.msg.SkuMsgVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.SaveSpuVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuVO;
import com.jdi.isc.product.soa.domain.spu.po.ProductGlobalAttributePO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.rpc.mq.ProductCenterMqService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.ProductGlobalAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.manage.spu.ProductMessageService;
import com.jdi.isc.product.soa.service.mapstruct.sku.SkuConvert;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description：ProductMessageServiceImpl
 * @Date 2025-04-29
 */
@Slf4j
@Service
public class ProductMessageServiceImpl implements ProductMessageService {

    private static final Logger log = LoggerFactory.getLogger(ProductMessageServiceImpl.class);
    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private ProductCenterMqService productCenterMqService;

    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;

    @Value("${topic.jmq4.product.isc.skuChangeMsg}")
    private String skuChangeMsgTopic;

    @Override
    @ToolKit(logFlag = false)
    public void sendMessage(SaveSpuVO saveSpuVO) {
        try {
            SpuVO spuVO = saveSpuVO.getSpuVO();

            Long spuId = spuVO.getSpuId();
            SpuPO spuPo = spuAtomicService.getSpuPoBySpuId(spuId);
            if (Objects.isNull(spuPo)) {
                return;
            }
            // 查询所有SKU信息
            List<SkuPO> skuPoList = skuAtomicService.getSkuPoBySpuId(spuId);
            if (CollectionUtils.isEmpty(skuPoList)) {
                return;
            }

            // 查询SPU的跨境属性 申报要素等信息
            SkuMsgVO dbGlobalMsgVo = this.getSpuGlobalAttributeVO(spuId);

            // 组装商品信息
            List<SkuMsgVO> skuMsgVOList = Lists.newArrayListWithExpectedSize(skuPoList.size());
            for (SkuPO skuPO : skuPoList) {
                skuMsgVOList.add(this.buildSkuMsgVO(saveSpuVO, skuPO, spuPo, dbGlobalMsgVo));
            }
            // 遍历发送消息
            skuMsgVOList.forEach((skuMsgVO -> {
                productCenterMqService.sendProductSoaMessageRetry(String.valueOf(skuMsgVO.getSkuId()), skuMsgVO,skuChangeMsgTopic);
            }));
        } catch (Exception e) {
            log.error("ProductMessageServiceImpl.sendMessage 发送消息失败 入参:[{}]",null,e);
        }
    }


    @Override
    public void sendMessage4CustomsInit(List<SkuPO> skuPoList) {
        if (CollectionUtils.isEmpty(skuPoList)) {
            log.info("ProductMessageServiceImpl.sendMessage4CustomsInit, skuPoList is empty");
            return;
        }

        // 查询所有SPU信息
        Set<Long> spuIds = skuPoList.stream().map(SkuPO::getSpuId).collect(Collectors.toSet());
        Map<Long, SpuPO> spuPoMap = spuAtomicService.getSpuMap(spuIds);

        // 查询SPU的跨境属性 申报要素等信息
        Map<Long, SkuMsgVO> spuMsgVoMap = batchGetSpuGlobalAttributeVO(spuIds);

        // 组装商品信息
        Map<String, Object> messageMap = new HashMap<>();
        for (SkuPO skuPO : skuPoList) {
            SpuPO spuPo = spuPoMap.get(skuPO.getSpuId());
            log.info("ProductMessageServiceImpl.sendMessage4CustomsInit, skuId = {}, spuPo = {}", skuPO.getSkuId(), JSON.toJSONString(spuPo));
            if(spuPo != null){
                SkuMsgVO skuMsgVO = spuMsgVoMap.get(skuPO.getSpuId());
                messageMap.put(String.valueOf(skuPO.getSkuId()), this.buildSkuMsgVO(1, skuPO, spuPo, skuMsgVO));
            }
        }

        if(MapUtils.isNotEmpty(messageMap)){
            productCenterMqService.batchSendProductSoaMessageRetry(messageMap , skuChangeMsgTopic, false);
        }

    }

    @Override
    public void sendSkuChangeMsg(Collection<Long> spuIds) {
        if (CollectionUtils.isEmpty(spuIds)) {
            log.info("sendSkuChangeMsg spuIds is null");
            return;
        }

        List<Long> sendSpuIds = Lists.newArrayList();

        try {
            for (List<Long> spuIdList : Lists.partition(Lists.newArrayList(spuIds), 100)) {
                Map<Long, SpuPO> spuMap = spuAtomicService.getSpuMap(Sets.newHashSet(spuIdList));

                if (MapUtils.isEmpty(spuMap)) {
                    log.info("sendSkuChangeMsg spus is null, spuIds={}", spuIds);
                    return;
                }

                Map<Long/*spuId*/, List<SkuPO>> skuMap = skuAtomicService.querySkuListBySpuIds(Sets.newHashSet(spuIds));

                if (MapUtils.isEmpty(skuMap)) {
                    log.info("sendSkuChangeMsg skuMap is null, spuIds={}", spuIds);
                    return;
                }

                Map<String, Object> messageMap = new HashMap<>();

                skuMap.forEach((spuId, v) -> {
                    SpuPO spuPO = spuMap.get(spuId);
                    if (spuPO == null) {
                        log.warn("找不到spu信息. spuId={}", spuId);
                        return;
                    }
                    for (SkuPO skuPO : v) {
                        SkuMsgVO skuMsgVO = this.buildSkuMsgVO(2, skuPO, spuPO, null);
                        messageMap.put(String.valueOf(skuPO.getSkuId()), skuMsgVO);
                    }
                });

                // 发送消息
                if(MapUtils.isNotEmpty(messageMap)){
                    productCenterMqService.batchSendProductSoaMessageRetry(messageMap , skuChangeMsgTopic, true);
                }

                sendSpuIds.addAll(spuIdList);
            }
        } finally {
            log.info("sendSkuChangeMsg finish={}, sendSpuIds={}", spuIds.size() == sendSpuIds.size(), sendSpuIds);
        }
    }

    /**
     * 构建SkuMsgVO对象。
     * @param saveSpuVO 保存SPU的VO对象，用于获取操作类型。
     * @param skuPO SKU的PO对象，用于获取SKU信息。
     * @param spuPo SPU的PO对象，用于获取买家和更新人信息。
     * @param dbGlobalMsgVo 数据库中已有的全局消息VO对象，用于获取声明元素和名称。
     * @return 构建好的SkuMsgVO对象。
     */
    private SkuMsgVO buildSkuMsgVO(SaveSpuVO saveSpuVO, SkuPO skuPO, SpuPO spuPo, SkuMsgVO dbGlobalMsgVo) {
        /*SkuMsgVO msgVO = SkuConvert.INSTANCE.skuPo2MsgVo(skuPO);
        msgVO.setBuyer(spuPo.getBuyer());
        msgVO.setDeclarationElements(dbGlobalMsgVo.getDeclarationElements());
        msgVO.setDeclarationEnName(dbGlobalMsgVo.getDeclarationEnName());
        msgVO.setDeclarationZhName(dbGlobalMsgVo.getDeclarationZhName());
        msgVO.setOperatorType(saveSpuVO.isCreateFlag() ? 1 : 2);
        msgVO.setUpdater(spuPo.getUpdater());
        msgVO.setUpdateTime(spuPo.getUpdateTime().getTime());*/
        return buildSkuMsgVO(saveSpuVO.isCreateFlag() ? 1 : 2, skuPO, spuPo, dbGlobalMsgVo);
    }

    private SkuMsgVO buildSkuMsgVO(int operateType, SkuPO skuPO, SpuPO spuPo, SkuMsgVO dbGlobalMsgVo) {
        SkuMsgVO msgVO = SkuConvert.INSTANCE.skuPo2MsgVo(skuPO);
        msgVO.setBuyer(spuPo.getBuyer());
        if(dbGlobalMsgVo != null){
            msgVO.setDeclarationElements(dbGlobalMsgVo.getDeclarationElements());
            msgVO.setDeclarationEnName(dbGlobalMsgVo.getDeclarationEnName());
            msgVO.setDeclarationZhName(dbGlobalMsgVo.getDeclarationZhName());
        }
        msgVO.setOperatorType(operateType);
        msgVO.setUpdater(spuPo.getUpdater());
        msgVO.setUpdateTime(spuPo.getUpdateTime().getTime());
        return msgVO;
    }

    /**
     * 根据SPU ID获取对应的全球属性信息。
     * @param spuId SPU的ID
     * @return SkuMsgVO对象，包含了SPU的申报要素跨境属性信息
     */
    private SkuMsgVO getSpuGlobalAttributeVO(Long spuId) {
        SkuMsgVO dbGlobalMsgVo = new SkuMsgVO();
        Map<Long, Map<Long, List<ProductGlobalAttributePO>>> spuGlobalAttributeMap = productGlobalAttributeAtomicService.queryProductGlobalAttributeMap(Sets.newHashSet(spuId), GlobalAttributeEnum.spuAttributeIdSet(), KeyTypeEnum.SPU.getCode());
        if (MapUtils.isEmpty(spuGlobalAttributeMap)) {
            return dbGlobalMsgVo;
        }
        Map<Long, List<ProductGlobalAttributePO>>  globalAttributeMap = spuGlobalAttributeMap.get(spuId);
        if (MapUtils.isNotEmpty(globalAttributeMap)){
            if (globalAttributeMap.containsKey(GlobalAttributeEnum.SPU_DECLARATION_ELEMENTS.getId()) && CollectionUtils.isNotEmpty(globalAttributeMap.get(GlobalAttributeEnum.SPU_DECLARATION_ELEMENTS.getId()))) {
                ProductGlobalAttributePO attributePO = globalAttributeMap.get(GlobalAttributeEnum.SPU_DECLARATION_ELEMENTS.getId()).get(0);
                dbGlobalMsgVo.setDeclarationElements(attributePO.getAttributeValue());
            }
            if (globalAttributeMap.containsKey(GlobalAttributeEnum.SPU_DECLARATION_NAME.getId()) && CollectionUtils.isNotEmpty(globalAttributeMap.get(GlobalAttributeEnum.SPU_DECLARATION_NAME.getId()))) {
                ProductGlobalAttributePO attributePO = globalAttributeMap.get(GlobalAttributeEnum.SPU_DECLARATION_NAME.getId()).get(0);
                dbGlobalMsgVo.setDeclarationZhName(attributePO.getAttributeValue());
            }
            if (globalAttributeMap.containsKey(GlobalAttributeEnum.SPU_DECLARATION_EN_NAME.getId()) && CollectionUtils.isNotEmpty(globalAttributeMap.get(GlobalAttributeEnum.SPU_DECLARATION_EN_NAME.getId()))) {
                ProductGlobalAttributePO attributePO = globalAttributeMap.get(GlobalAttributeEnum.SPU_DECLARATION_EN_NAME.getId()).get(0);
                dbGlobalMsgVo.setDeclarationElements(attributePO.getAttributeValue());
            }
        }
        log.info("ProductMessageServiceImpl.getSpuGlobalAttributeVO 查询SPU ID={} 的申报要素跨境属性",spuId, JSON.toJSONString(dbGlobalMsgVo));
        return dbGlobalMsgVo;
    }


    /**
     * 批量根据SPU ID获取对应的全球属性信息。
     * @param spuIds SPU的ID
     * @return SkuMsgVO对象，包含了SPU的申报要素跨境属性信息
     */
    private Map<Long, SkuMsgVO> batchGetSpuGlobalAttributeVO(Set<Long> spuIds) {
        SkuMsgVO dbGlobalMsgVo = new SkuMsgVO();
        Map<Long, Map<Long, List<ProductGlobalAttributePO>>> spuGlobalAttributeMap = productGlobalAttributeAtomicService.queryProductGlobalAttributeMap(spuIds, GlobalAttributeEnum.spuAttributeIdSet(), KeyTypeEnum.SPU.getCode());
        if (MapUtils.isEmpty(spuGlobalAttributeMap)) {
            return Collections.emptyMap();
        }

        Map<Long, SkuMsgVO> skuMsgVoMap = new HashMap<>();
        spuIds.forEach(spuId -> {
            Map<Long, List<ProductGlobalAttributePO>>  globalAttributeMap = spuGlobalAttributeMap.get(spuId);
            if (MapUtils.isNotEmpty(globalAttributeMap)){
                if (globalAttributeMap.containsKey(GlobalAttributeEnum.SPU_DECLARATION_ELEMENTS.getId()) && CollectionUtils.isNotEmpty(globalAttributeMap.get(GlobalAttributeEnum.SPU_DECLARATION_ELEMENTS.getId()))) {
                    ProductGlobalAttributePO attributePO = globalAttributeMap.get(GlobalAttributeEnum.SPU_DECLARATION_ELEMENTS.getId()).get(0);
                    dbGlobalMsgVo.setDeclarationElements(attributePO.getAttributeValue());
                }
                if (globalAttributeMap.containsKey(GlobalAttributeEnum.SPU_DECLARATION_NAME.getId()) && CollectionUtils.isNotEmpty(globalAttributeMap.get(GlobalAttributeEnum.SPU_DECLARATION_NAME.getId()))) {
                    ProductGlobalAttributePO attributePO = globalAttributeMap.get(GlobalAttributeEnum.SPU_DECLARATION_NAME.getId()).get(0);
                    dbGlobalMsgVo.setDeclarationZhName(attributePO.getAttributeValue());
                }
                if (globalAttributeMap.containsKey(GlobalAttributeEnum.SPU_DECLARATION_EN_NAME.getId()) && CollectionUtils.isNotEmpty(globalAttributeMap.get(GlobalAttributeEnum.SPU_DECLARATION_EN_NAME.getId()))) {
                    ProductGlobalAttributePO attributePO = globalAttributeMap.get(GlobalAttributeEnum.SPU_DECLARATION_EN_NAME.getId()).get(0);
                    dbGlobalMsgVo.setDeclarationElements(attributePO.getAttributeValue());
                }
            }
            log.info("ProductMessageServiceImpl.batchGetSpuGlobalAttributeVO 查询SPU ID={} 的申报要素跨境属性",spuId, JSON.toJSONString(dbGlobalMsgVo));
            skuMsgVoMap.put(spuId, dbGlobalMsgVo);
        });
        return skuMsgVoMap;
    }
}
