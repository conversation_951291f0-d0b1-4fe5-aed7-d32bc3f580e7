package com.jdi.isc.product.soa.service.manage.spu.impl.approve;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.TradeTypeConstant;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuStatusEnum;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant;
import com.jdi.isc.product.soa.common.constants.TaxConstant;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.product.AuditLevelEnum;
import com.jdi.isc.product.soa.domain.enums.vendor.VendorTypeEnum;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.spu.biz.SaveSpuVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuAuditRecordUpdateVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuDraftVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuVO;
import com.jdi.isc.product.soa.service.manage.spu.SpuApproveManageService;
import com.jdi.isc.product.soa.service.manage.spu.validate.SkuValidateService;
import com.jdi.isc.product.soa.service.manage.vendor.VendorManageService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.Constant.COMMA;
import static com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant.SPU_AUDIT_LOCK_FAILED;

/**
 * <AUTHOR>
 * @date 2023/12/18
 **/
@Slf4j
@Service("crossborderSpuApproveManageService")
public class CrossborderSpuApproveManageServiceImpl extends AbstractSpuApproveManageService implements SpuApproveManageService {

    @Resource
    private VendorManageService vendorManageService;

    @Resource
    private SkuValidateService skuValidateService;

    @Resource
    private SpuConvertService spuConvertService;

    @Override
    public DataResponse<String> batchApprove(SpuAuditRecordUpdateVO spuAuditRecordReqVO) {
        return super.batchApprove(spuAuditRecordReqVO);
    }

    @Override
    public DataResponse<String> batchReject(SpuAuditRecordUpdateVO spuAuditRecordReqVO) {
        return super.batchReject(spuAuditRecordReqVO);
    }

    @Override
    void recordVendor(List<SkuVO> skuVOList) {
        String sourceCountryCode = skuVOList.get(0).getSourceCountryCode();
        Set<String> jdVendorSet = skuVOList.stream().filter(vo -> StringUtils.isNotBlank(vo.getJdVendorCode())).map(SkuVO::getJdVendorCode).collect(Collectors.toSet());
        log.info("CrossborderSpuApproveManageServiceImpl.recordVendor 记录新供应商信息 入参:[sourceCountryCode={},jdVendorSet={}]", sourceCountryCode, JSON.toJSONString(jdVendorSet));
        vendorManageService.insertJdVendor(jdVendorSet, sourceCountryCode, VendorTypeEnum.DOMESTIC.getType());
        log.info("CrossborderSpuApproveManageServiceImpl.recordVendor 记录新供应商信息完成，jdVendorSet={}",JSON.toJSONString(jdVendorSet));
    }

    @Override
    void createSpu(SaveSpuVO saveSpuVO) {

    }

    @Override
    void validateSkuList(List<SkuVO> skuVOList, SpuVO spuVO) {
        skuValidateService.validateJdSkuNull(skuVOList);
        skuValidateService.validateCreateSkuVoList(skuVOList);
        skuValidateService.validateSkuStockList(skuVOList,spuVO.getSourceCountryCode());
        //  校验国内skuId重复
        skuValidateService.validateJdSkuRelation(skuVOList);
        List<Long> skuIds = skuVOList.stream().map(SkuVO::getJdSkuId).collect(Collectors.toList());

        //skuValidateService.validateSkuIdCanPurchase(skuIds);
        //skuValidateService.validateSkuIdAreaLimit(skuIds);
        // 校验价格
        skuValidateService.validateSkuPrice(skuVOList, spuVO.getSourceCountryCode());
        // 校验长宽高重
        skuValidateService.validateFourDimension(skuVOList);
        // 校验跨境属性
        skuValidateService.validateInterProperty(spuVO.getCatId()
                ,spuVO.getSourceCountryCode()
                ,StringUtils.isNotBlank(spuVO.getAttributeScope()) ? Arrays.asList(spuVO.getAttributeScope().split(COMMA)) : Lists.newArrayList()
                ,skuVOList,spuVO.getIsExport());
        // 校验跨境资质
        skuValidateService.validateCertificate(spuVO.getCatId()
                ,spuVO.getSourceCountryCode()
                ,StringUtils.isNotBlank(spuVO.getAttributeScope()) ? Arrays.asList(spuVO.getAttributeScope().split(COMMA)) : Lists.newArrayList()
                ,skuVOList,spuVO.getIsExport());
        // 校验出口HsCode
        skuValidateService.validateHsCode(skuVOList);
        // 校验条形码
        skuValidateService.validateUpcCode(skuVOList);
    }

    @Override
    void updateAuditLevel(SpuDraftVO draftVo) {

    }

    @Override
    void handleSkuList(List<SkuVO> skuVOList) {
        if (CollectionUtils.isNotEmpty(skuVOList)) {
            skuVOList.forEach(skuVO -> {
                if (StringUtils.isBlank(skuVO.getCustomerTradeType()) && !TradeTypeConstant.EXW.equals(skuVO.getCustomerTradeType())) {
                    skuVO.setCustomerTradeType(TradeTypeConstant.EXW);
                }
                if (StringUtils.isBlank(skuVO.getVendorTradeType())&& !TradeTypeConstant.EXW.equals(skuVO.getVendorTradeType())) {
                    skuVO.setVendorTradeType(TradeTypeConstant.EXW);
                }
            });
        }
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 30)
    public void singleApproved(Long spuId, String scene) {
        final String erp = LoginContextHolder.getLoginContextHolder().getPin();
        String requestId = UUID.randomUUID().toString();
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.SPU_AUDIT_OPERATE_LOCK_PRE, String.valueOf(spuId));
        try {
            // 加排他锁，同时只有一个审核操作可以审核当前商品
            AssertValidation.isTrue(!jimUtils.simpleLock(lockKey, requestId, 60), SPU_AUDIT_LOCK_FAILED, "加锁失败");
            // 查询草稿
            SpuDraftVO draftVo = spuDraftManageService.getDraftBySpuId(spuId);
            if (Objects.isNull(draftVo)) {
                updateSpuToSaleStatus(spuId);
                // 修改sku上架时间
                skuWriteManageService.saveOrUpdateSkuBiz(spuId, new Date());
                return;
            }

            SaveSpuVO saveSpuVO = spuDraftManageService.getSaveSpuVoFromDraftBySpuId(spuId);
            // 设置采销
            String buyer = categoryBuyerRelationAtomicService.getBuyerByCountryCodeAndCatId(saveSpuVO.getSpuVO().getSourceCountryCode(),saveSpuVO.getSpuVO().getCatId());
            saveSpuVO.getSpuVO().setBuyer(buyer);

            // 校验参数
            saveSpuVO.setApproveFlag(Constant.ONE);
            saveSpuVO.getSpuVO().setApproveFlag(Constant.ONE);
            spuValidateService.validateSaveParams(saveSpuVO);
            this.validateSkuList(saveSpuVO.getSkuVOList(),saveSpuVO.getSpuVO());
            // 审核通过后，更新商品
            if (Objects.nonNull(spuAtomicService.getSpuPoBySpuId(spuId))) {
                // 更新spu信息、spu状态为上架、审核通过
                saveSpuVO.getSpuVO().setSpuStatus(SpuStatusEnum.ON_SALE.getStatus());
                saveSpuVO.getSpuVO().setAuditStatus(SpuAuditStatusEnum.APPROVED.getCode());
                this.updateSpu(saveSpuVO);
                // 更新详描
                this.saveOrUpdateSpuDescLang(saveSpuVO);
                // 更新sku列表、sku价格、库存、扩展属性更新上架时间
                List<SkuVO> skuVoList = saveSpuVO.getSkuVOList();
                List<SkuVO> newSkuList = Lists.newArrayList();
                this.fillSkuVo(saveSpuVO.getSpuVO(), skuVoList);
                skuWriteManageService.updateSkus(skuVoList, newSkuList, spuId);
                // 修改sku上架时间
                skuWriteManageService.saveOrUpdateSkuBiz(spuId, saveSpuVO.getSpuVO().getUpdateTime());
                this.handleSkuList(skuVoList);
                // 更新sku价格
                this.initSkuPrice(skuVoList, saveSpuVO.getSpuVO().getSourceCountryCode());
                // 保存库存/在途标识
                skuConvertService.initSkuStock(skuVoList, saveSpuVO.getSpuVO());
                // 更新资质列表
                this.saveOrUpdateSpuCertificate(saveSpuVO);
                // 处理SPU跨境属性
                super.initSpuGlobalAttribute(saveSpuVO);
                // 记录新的国内供应商
                this.recordVendor(skuVoList);
                // 备货仓库绑定更新
                //this.bindWarehouse(saveSpuVO.getSpuVO(), skuVoList);
            } else {
                // 更新spu信息、spu状态为上架、审批通过
                saveSpuVO.getSpuVO().setSpuStatus(SpuStatusEnum.ON_SALE.getStatus());
                if(!StringUtils.equals(TaxConstant.TAX_APPROVE,scene)){
                    saveSpuVO.getSpuVO().setAuditStatus(SpuAuditStatusEnum.APPROVED.getCode());
                }

                saveSpuVO.getSpuVO().setCreator(draftVo.getCreator());
                saveSpuVO.getSpuVO().setUpdater(draftVo.getCreator());
                Date now = new Date();
                saveSpuVO.getSpuVO().setCreateTime(now);
                saveSpuVO.getSpuVO().setUpdateTime(now);
                // sku状态为上架
                List<@Valid SkuVO> skuVOList = saveSpuVO.getSkuVOList();
                skuVOList.forEach(skuVO -> {
                    skuVO.setSkuStatus(SpuStatusEnum.ON_SALE.getStatus());
                    skuVO.setCreator(draftVo.getCreator());
                    skuVO.setUpdater(draftVo.getCreator());
                    skuVO.setCreateTime(now);
                    skuVO.setUpdateTime(now);
                });
                this.handleSkuList(skuVOList);
            }

            // 更新草稿审核状态
            log.info("CrossborderSpuApproveManageServiceImpl.singleApproved 更新草稿前参数,saveSpuVO={}", com.alibaba.fastjson.JSON.toJSONString(saveSpuVO));
            spuDraftManageService.saveOrUpdateDraft(saveSpuVO);

            // 添加审核记录 跨境商品审核等级不变一直是0，本土时设置为3级
            this.addAuditRecord(spuId, erp, AuditStatusEnum.APPROVED, APPROVED_TEXT, AuditLevelEnum.ZERO.getLevel());

            // 商品审核通过后，只能识别到修改，跨境新增逻辑在发品方法
            super.sendSkuMsg(saveSpuVO);

            //审批通过MKU与SPU、SKU的信息同步
            log.info("CrossborderSpuApproveManageServiceImpl.singleApproved 审批通过MKU与SPU、SKU的信息同步,saveSpuVO={}", com.alibaba.fastjson.JSON.toJSONString(saveSpuVO));
            mkuManageService.syncMkuInfoBySpuAndSku(saveSpuVO);
        } catch (BizException bizException) {
            log.error("【系统异常】CrossborderSpuApproveManageServiceImpl.singleApproved biz error spuId={}", com.alibaba.fastjson.JSON.toJSONString(spuId), bizException);
            throw bizException;
        } catch (Exception e) {
            log.error("【系统异常】CrossborderSpuApproveManageServiceImpl.singleApproved error spuId={}", spuId, e);
            String errorMessage = dataResponseMessageService.getErrorMessage(DataResponseCodeConstant.SPU_APPROVED_ERROR);
            log.error("【系统异常】CrossborderSpuApproveManageServiceImpl.singleApproved error spuId={},message={}", spuId, errorMessage);
            throw new BizException(errorMessage, e);
        } finally {
            jimUtils.simpleLockRelease(lockKey, requestId);
        }
    }
}
