package com.jdi.isc.product.soa.service.manage.taxRate.countryTax.impl.read;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.CurrencyConstant;
import com.jdi.isc.product.soa.domain.price.extendPrice.biz.CurrencyPriceVO;
import com.jdi.isc.product.soa.domain.price.po.FulfillmentRatePO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxResVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.IscSkuImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.BrSkuTaxPO;
import com.jdi.isc.product.soa.price.api.price.req.CrossBorderImportTaxReqDTO;
import com.jdi.isc.product.soa.price.api.price.res.CrossBorderImportTaxResDTO;
import com.jdi.isc.product.soa.service.atomic.taxRate.countryTax.BrSkuTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.BaseTaxReadService;
import com.jdi.isc.product.soa.service.support.TaxCalculateExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 巴西关税读服务
 * <AUTHOR>
 * @date 2025/3/10
 */
@Slf4j
@Service
@TaxCalculateExecutor(countryCode = CountryConstant.COUNTRY_BR)
public class BrCountryTaxReadServiceImpl extends BaseTaxSupportService implements BaseTaxReadService {

    @Resource
    private BrSkuTaxAtomicService brSkuTaxAtomicService;

    /** 通过skuId、国家码查询跨境sku关税*/
    @Override
    @PFTracing
    public Map<Long, CrossBorderImportTaxResDTO> queryCrossBorderSkuImportTax(CrossBorderImportTaxReqVO input) {
        Map<Long, CrossBorderImportTaxResDTO> result = Maps.newHashMapWithExpectedSize(input.getCnSkuIds().size());
        //获取跨境采购价
        Map<Long, SkuPricePO> priceMap = skuPriceAtomicService.batchQuerySkuTaxPrice(input.getCnSkuIds(), CountryConstant.COUNTRY_ZH);
        //获取巴西关税率
        Map<Long, BrSkuTaxPO> skuTaxMap = getSkuTaxMap(input.getCnSkuIds());
        for(Long skuId : input.getCnSkuIds()){
            CrossBorderImportTaxResDTO target = new CrossBorderImportTaxResDTO(skuId,input.getCountryCode(),CurrencyConstant.CURRENCY_ZH);
            StringBuilder msg = new StringBuilder();
            if(priceMap.get(skuId)==null){
                msg.append("skuId:").append(skuId).append(" 采购价为空,无法计算关税;");
                target.setDesc(msg.toString());
                result.put(skuId,target);
                continue;
            }
            if(skuTaxMap.get(skuId)==null){
                msg.append("skuId:").append(skuId).append(" 关税配置为空,无法计算关税;");
                target.setDesc(msg.toString());
                result.put(skuId,target);
                continue;
            }
            //巴西关税明细
            BrSkuTaxPO taxInfo = skuTaxMap.get(skuId);
            //履约费用
            CurrencyPriceVO fulfillmentPrice;
            if(!input.getFulfillmentFee().isEmpty() && input.getFulfillmentFee().get(skuId)!=null){
                fulfillmentPrice = input.getFulfillmentFee().get(skuId);
            }else {
                fulfillmentPrice = getFulfillmentPrice(input.getCountryCode(), skuId);
            }
            if(fulfillmentPrice==null || fulfillmentPrice.getPrice()==null){
                target.setDesc(fulfillmentPrice!=null&& StringUtils.isNotBlank(fulfillmentPrice.getMsg())?fulfillmentPrice.getMsg():"履约费用为空,无法计算关税;");
                result.put(skuId,target);
                continue;
            }
            //CIF价格=采购价（含税）+ 履约费用
            BigDecimal cif = priceMap.get(skuId).getPrice().add(fulfillmentPrice.getPrice());
            //进口关税金II = CIF价格 x 进口关税率II%；
            BigDecimal importDuties = cif.multiply(taxInfo.getImportTax());
            //工业产品税金IPI = (CIF+ 进口关税金II ) x 工业产品税率IPI%；
            BigDecimal industryProduct = cif.add(importDuties).multiply(taxInfo.getIndustryProductTax());
            //社会一体化费PIS =CIF价格 x 社会一体化费率PIS%
            BigDecimal socialIntegration = cif.multiply(taxInfo.getSocialIntegrationTax());
            //社会保险融资税金 CONFINS= CIF价格 x CONFINS率%
            BigDecimal confins = cif.multiply(taxInfo.getConfinsTax());
            //反倾销税金=  CIF价格 x  反倾销税率%
            BigDecimal antiDumping = cif.multiply(taxInfo.getAntiDumpingTax());
            //流转税金ICMS= ( CIF价格  + 进口关税金II + 工业产品税金IPI  + 社会一体化费PIS+ 社会保险融资税金 CONFINS+ 反倾销税金)/(1-ICMS流转税率%) x ICMS流转税率%
            BigDecimal icmsFlow = cif.add(importDuties).add(industryProduct).add(socialIntegration).add(confins).add(antiDumping).divide(BigDecimal.ONE.subtract(taxInfo.getIcmsFlowTax()),8,RoundingMode.HALF_UP).multiply(taxInfo.getIcmsFlowTax());
            //进口税费(进口关税)=进口关税金II + 工业产品税金IPI +社会一体化费PIS +社会保险融资税金 CONFINS+反倾销税金+流转税金ICMS  (中间值不舍位，最终结果四舍五入)
            BigDecimal importTax = importDuties.add(industryProduct).add(socialIntegration).add(confins).add(antiDumping).add(icmsFlow).setScale(2,RoundingMode.HALF_UP);

            //优化小数位展示
            String importTaxStr = importTax.stripTrailingZeros().toPlainString();
            String importDutiesStr = importDuties.stripTrailingZeros().toPlainString();
            String industryProductStr = industryProduct.stripTrailingZeros().toPlainString();
            String socialIntegrationStr = socialIntegration.stripTrailingZeros().toPlainString();
            String confinsStr = confins.stripTrailingZeros().toPlainString();
            String antiDumpingStr = antiDumping.stripTrailingZeros().toPlainString();
            String icmsFlowStr = icmsFlow.stripTrailingZeros().toPlainString();
            String cifStr = cif.stripTrailingZeros().toPlainString();

            //计算过程消息
//            String totalMsg = String.format("进口税费%s = 进口关税金II%s + 工业产品税金IPI%s + 社会一体化费PIS%s + 社会保险融资税金CONFINS%s + 反倾销税金%s + 流转税金ICMS%s",
            String totalMsg = String.format("进口税费%s = 进口关税金II%s ", importTaxStr,importDutiesStr);
            String cifMsg = String.format("CIF价格%s=采购价（含税）%s+ 履约费用%s",cifStr,priceMap.get(skuId).getPrice(),fulfillmentPrice.getPrice());
            String importDutiesMsg = String.format("进口关税金II%s = CIF%s x 进口关税率II%s",importDutiesStr,cifStr,taxInfo.getImportTax().stripTrailingZeros().toPlainString());
            String industryProductMsg = String.format("工业产品税金IPI%s = (CIF%s + 进口关税金II%s) x 工业产品税率IPI%s",industryProductStr,cifStr,importDutiesStr,taxInfo.getIndustryProductTax().stripTrailingZeros().toPlainString());
            String socialIntegrationMsg = String.format("社会一体化费PIS%s =CIF%s x 社会一体化费率PIS%s",socialIntegrationStr,cifStr,taxInfo.getSocialIntegrationTax().stripTrailingZeros().toPlainString());
            String confinsMsg = String.format("社会保险融资税金CONFINS%s = CIF%s x CONFINS%s",confinsStr,cifStr,taxInfo.getConfinsTax().stripTrailingZeros().toPlainString());
            String antiDumpingMsg = String.format("反倾销税金%s =  CIF%s x  反倾销税率%s",antiDumpingStr,cifStr,taxInfo.getAntiDumpingTax().stripTrailingZeros().toPlainString());
            String icmsFlowMsg = String.format("流转税金ICMS%s = ( CIF%s  + 进口关税金II%s + 工业产品税金IPI%s  + 社会一体化费PIS%s + 社会保险融资税金CONFINS%s + 反倾销税金%s)/(1-ICMS流转税率%s) x ICMS流转税率%s",
                    icmsFlowStr,cifStr,importDutiesStr,industryProductStr,socialIntegrationStr,confinsStr,antiDumpingStr,taxInfo.getIcmsFlowTax().stripTrailingZeros().toPlainString(),taxInfo.getIcmsFlowTax().stripTrailingZeros().toPlainString());
            target.setDesc(msg.append(totalMsg).append("\n").
                    append(cifMsg).append("\n").
                    append(importDutiesMsg).append("\n").
                    append(industryProductMsg).append("\n").
                    append(socialIntegrationMsg).append("\n").
                    append(confinsMsg).append("\n").
                    append(antiDumpingMsg).append("\n").
                    append(icmsFlowMsg).toString());
            //进口关税最终只使用关税II,其他项仅做展示
            target.setImportTax(importDuties.setScale(2,RoundingMode.HALF_UP));
            target.setDeductionAmount(BigDecimal.ZERO);
            log.info("BrCountryTaxReadServiceImpl.queryCrossBorderSkuImportTax skuId:{} , res:{}" , skuId , JSON.toJSONString(target));
            result.put(skuId,target);
        }
        return result;
    }

    private Map<Long, BrSkuTaxPO> getSkuTaxMap(Set<Long> cnSkuIds) {
        Map<Long, BrSkuTaxPO> result = Maps.newHashMapWithExpectedSize(cnSkuIds.size());
        //国际sku批量转jdSku
        Map<Long, Long> res = skuAtomicService.querySkuMapByIscSkuIds(cnSkuIds);
        if(res.isEmpty()){
            log.error("BrCountryTaxReadServiceImpl.getSkuTaxMap invoke error, 给定的国际sku未能查询到正确的sku与jdSku映射关系 {}", cnSkuIds);
            return new HashMap<>();
        }
        //根据jdSku查关税率
        Map<Long, BrSkuTaxPO> skuTaxMap = brSkuTaxAtomicService.listSkuTax(new HashSet<>(res.values()));
        if(skuTaxMap.isEmpty()){
            log.error("BrCountryTaxReadServiceImpl.getSkuTaxMap invoke error, 给定的jdSku未能查询到关税配置 res {}", JSON.toJSONString(res));
            return new HashMap<>();
        }
        //构建国际sku&关税实体关系
        for (Map.Entry<Long, Long> entry : res.entrySet()) {
            result.put(entry.getKey(), skuTaxMap.get(entry.getValue()));
        }
        return result;
    }

    @Override
    public Map<Long, CrossBorderImportTaxResVO> queryImportTaxByIscSkuIds(IscSkuImportTaxReqVO input) {
        Map<Long, BrSkuTaxPO> skuTaxMap = this.getSkuTaxMap(input.getSkuIds());
        // 构建返回结果
        Map<Long, CrossBorderImportTaxResVO> resultMap = Maps.newHashMapWithExpectedSize(skuTaxMap.size());
        skuTaxMap.forEach((skuId,taxPo)-> resultMap.put(skuId, new CrossBorderImportTaxResVO(skuId,taxPo.getJdSkuId(), taxPo.getNcmCode())));
        return resultMap;
    }
}
