package com.jdi.isc.product.soa.service.manage.mku.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.OrderMsgStatusEnum;
import com.jdi.isc.product.soa.api.subtitle.biz.MkuSubtitleImportReqDTO;
import com.jdi.isc.product.soa.api.subtitle.biz.MkuSubtitleImportResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import com.jdi.isc.product.soa.domain.ducc.ChatGptProperties;
import com.jdi.isc.product.soa.domain.enums.mku.DealTypeEnum;
import com.jdi.isc.product.soa.domain.enums.msg.MsgTypeEnum;
import com.jdi.isc.product.soa.domain.mku.biz.MkuLangVO;
import com.jdi.isc.product.soa.domain.mku.biz.SubtitleResultVO;
import com.jdi.isc.product.soa.domain.mku.po.MkuLangPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuLogPO;
import com.jdi.isc.product.soa.domain.wiop.msg.MkuChangeMsg;
import com.jdi.isc.product.soa.domain.wiop.msg.OpenMsgPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuDraftPO;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.msg.OpenMsgAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuLogAtomicService;
import com.jdi.isc.product.soa.service.manage.chatGpt.ChatGptService;
import com.jdi.isc.product.soa.service.manage.mku.MkuLangManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuManageService;
import com.jdi.isc.product.soa.service.mapstruct.mku.MkuLangConvert;
import com.jdi.isc.product.soa.service.mapstruct.subtitle.SubtitleConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MkuLangManageServiceImpl implements MkuLangManageService {

    @Resource
    private MkuLangAtomicService mkuLangAtomicService;

    @Resource
    private MkuDraftAtomicService mkuDraftAtomicService;

    @Resource
    private SkuLogAtomicService skuLogAtomicService;

    @Resource
    private OpenMsgAtomicService openMsgAtomicService;

    @Value("${spring.profiles.active}")
    private String systemProfile;

    @Value("${chatgpt.model}")
    private String chatGptModel;

    @Resource
    private OperDuccConfig operDuccConfig;

    @Resource
    private ChatGptService chatGptService;

    @Resource
    private CountryMkuAtomicService countryMkuAtomicService;

    @Override
    public DataResponse<String> updateSubtitle(MkuLangVO vo) {
        // 验证输入参数
        validateInput(vo);
        log.info("MkuLangManageServiceImpl.update, vo:{}", JSONObject.toJSONString(vo));
        MkuLangPO po = MkuLangConvert.INSTANCE.vo2PO(vo);
        MkuLangPO dbMkuLangPO = mkuLangAtomicService.getPoById(po.getId());
        if(Objects.isNull(dbMkuLangPO)) {
            return DataResponse.error("数据不存在");
        }
        if(!Objects.equals(po.getMkuId(),dbMkuLangPO.getMkuId())){
            return DataResponse.error("数据异常，无法更新！");
        }
        List<MkuLangPO> mkuLangPoBySubtitle = mkuLangAtomicService.getMkuLangPoBySubtitle(vo.getMkuSubtitle(), dbMkuLangPO.getLang());
        if(CollectionUtils.isNotEmpty(mkuLangPoBySubtitle)){
            List<String> mkuIdStrList = mkuLangPoBySubtitle.stream().map(mkuLangPO -> String.valueOf(mkuLangPO.getMkuId())).collect(Collectors.toList());
            String mkuIdStr = String.join(Constant.COMMA, mkuIdStrList);
            return DataResponse.error("短标题已存在,mkuId"+mkuIdStr);
        }
        String sourceJsonString = JSON.toJSONString(dbMkuLangPO);
        dbMkuLangPO.setMkuId(null);
        dbMkuLangPO.setUpdater(vo.getUpdater());
        dbMkuLangPO.setUpdateTime(new Date());
        dbMkuLangPO.setMkuSubtitle(po.getMkuSubtitle());
        dbMkuLangPO.setSubtitleLength(po.getSubtitleLength());
        dbMkuLangPO.setSubtitleType(po.getSubtitleType());
        dbMkuLangPO.setRemark("");
        boolean result = mkuLangAtomicService.updateById(dbMkuLangPO);
        if (!result){
            log.warn("MkuLangManageServiceImpl.update, MkuLangVO fail. MkuLangPO={}", JSONObject.toJSONString(po));
            throw new BizException("保存失败");
        }
        dbMkuLangPO.setMkuId(vo.getMkuId());
        notifyMsg(dbMkuLangPO);
        sendLog(po.getMkuId().toString(),sourceJsonString,dbMkuLangPO);
        return DataResponse.success("更新短标题成功");
    }

    @Override
    public DataResponse<String> saveMkuLang(MkuLangVO mkuLangVO) {
        if(Objects.isNull(mkuLangVO) ){
            log.info("MkuLangManageServiceImpl.saveMkuLang 标题为空 mkuLangPo:{}", JSON.toJSONString(mkuLangVO));
            return DataResponse.error("无数据");
        }
        if(StringUtils.isBlank(mkuLangVO.getMkuTitle())){
            mkuLangVO.setMkuTitle("");
        }
        MkuLangPO mkuLangPo = MkuLangConvert.INSTANCE.vo2PO(mkuLangVO);
        try {
            SubtitleResultVO subtitleResultVO = validateTitle(mkuLangPo);
            ChatGptProperties chatGptProperties = operDuccConfig.preseChatGpt();
            if(subtitleResultVO.getIsSuccess()){
                Thread.sleep(10000);
                int cycleNumber = 0;
                handleSubtitle(subtitleResultVO,mkuLangPo.getMkuTitle(), mkuLangPo.getLang(),chatGptProperties,cycleNumber);
            }
            mkuLangPo.setMkuSubtitle(subtitleResultVO.getSubtitle());
            mkuLangPo.setSubtitleLength(subtitleResultVO.getLength());
            mkuLangPo.setRemark(subtitleResultVO.getMessage());
            mkuLangPo.setSubtitleType(DealTypeEnum.MACHINE.getCode());
            if(subtitleResultVO.getLength() > 40){
                mkuLangPo.setRemark("短标题超长");
            }
             /*当第一次创建SKU并审批通过后，会调用大模型进行生成少于40字符的短标题，标题的更新人应该为system_GPT4o0806,而不是当前操作人；
            只有当在页面操作并有人工进行很对人工校验后才会更新标题，所以这里需要判断是否为人工已经编辑过，根据subtitleType 枚举值来判断，*/
            String modelName = chatGptProperties.getChatGptModel().get(ChatGptProperties.SUBTITLE);
            mkuLangPo.setUpdater("system-"+ modelName);
            if(Objects.nonNull(mkuLangPo.getId())){
                mkuLangPo.setMkuId(null);
                mkuLangAtomicService.updateById(mkuLangPo);
            }else if(Objects.nonNull(mkuLangPo.getMkuId())){
                MkuLangPO mkuLang = mkuLangAtomicService.getMkuLangPoByMkuId(mkuLangPo.getMkuId(), LangConstant.LANG_BR);
                if(Objects.nonNull(mkuLang)){
                    mkuLangPo.setId(mkuLang.getId());
                    mkuLangPo.setMkuId(null);
                    mkuLangAtomicService.updateById(mkuLangPo);
                }else {
                    mkuLangAtomicService.saveOrUpdate(mkuLangPo);
                }
            }
            return DataResponse.success("成功");
        }catch (Exception e){
            log.error("MkuManageServiceImpl.updateSubtitle error mkuLangPo:{}", JSON.toJSONString(mkuLangPo), e);
            return DataResponse.error("失败");
        }finally {
            log.info("MkuManageServiceImpl.updateSubtitle update mkuLangPo:{}", com.alibaba.fastjson.JSON.toJSONString(mkuLangPo));
        }
    }

    @Override
    public MkuSubtitleImportResDTO importMkuSubtitle(MkuSubtitleImportReqDTO data) {

        if (data == null || CollectionUtils.isEmpty(data.getItems())) {
            return new MkuSubtitleImportResDTO();
        }

        List<MkuSubtitleImportReqDTO.Item> items = data.getItems();

        // excel基本校验
        excelBasicValid(items);

        // excel数据库校验
        Map<Long, MkuLangPO> mkuMap = excelDbValid(items);

        items.stream().filter(MkuSubtitleImportReqDTO.Item::getValid).forEach(item -> {

            // 获取mku
            MkuLangPO dbMkuLangPO = mkuMap.get(Long.parseLong(item.getMkuId()));

            String sourceJsonString = JSON.toJSONString(dbMkuLangPO);

            dbMkuLangPO.setMkuId(null);
            dbMkuLangPO.setUpdater(item.getPin());
            dbMkuLangPO.setUpdateTime(new Date());
            dbMkuLangPO.setMkuSubtitle(item.getMkuSubtitle());
            dbMkuLangPO.setSubtitleLength(item.getMkuSubtitle().length());
            dbMkuLangPO.setSubtitleType(DealTypeEnum.MACHINE.getCode());
            dbMkuLangPO.setRemark("");

            try {
                boolean result = mkuLangAtomicService.updateById(dbMkuLangPO);
                if (!result){
                    log.warn("MkuLangManageServiceImpl.importMkuSubtitle, MkuLangVO fail. MkuLangPO={}", JSONObject.toJSONString(dbMkuLangPO));
                    item.setValid(false);
                    item.addErrorMsg("导入失败请稍后重试!");
                }
            } catch (Exception e) {
                log.warn("MkuLangManageServiceImpl.importMkuSubtitle, MkuLangVO fail. MkuLangPO={}", JSONObject.toJSONString(dbMkuLangPO), e);
                item.setValid(false);
                item.addErrorMsg("导入失败请联系管理员!");
            }

            dbMkuLangPO.setMkuId(Long.parseLong(item.getMkuId()));
            try {
                notifyMsg(dbMkuLangPO);
            } catch (Exception e) {
                log.error("MkuLangManageServiceImpl.importMkuSubtitle, notifyMsg fail. MkuLangPO={}", JSONObject.toJSONString(dbMkuLangPO), e);
            }
            sendLog(dbMkuLangPO.getMkuId().toString(), sourceJsonString, dbMkuLangPO);
        });

        // 包装返回结果
        return SubtitleConvert.INSTANCE.convert(data);
    }

    private Map<Long, MkuLangPO> excelDbValid(List<MkuSubtitleImportReqDTO.Item> items) {
        // 第一次未通过校验的数据不重复处理
        List<Long> mkuIds = items.stream().filter(MkuSubtitleImportReqDTO.Item::getValid).map(MkuSubtitleImportReqDTO.Item::getMkuId).map(Long::parseLong).collect(Collectors.toList());
        List<String> mkuTitles = items.stream().filter(MkuSubtitleImportReqDTO.Item::getValid).map(MkuSubtitleImportReqDTO.Item::getMkuSubtitle).collect(Collectors.toList());

        // (2）若短标题导入结果与历史短标题重复，报错：短标题导入结果与MKUIDxxxxxx重复，请人工修改短标题！
        //（4）若该MKUID不存在，报错：MKUID不存在，请检查！
        //（5）若该MKUID未入国家池，报错：MKUID未入国家池，请检查！

        Map<Long, MkuLangPO> existMkuIdMap = mkuLangAtomicService.getMkuLangNameByMkuIds(mkuIds, LangConstant.LANG_BR);
        Map<String, MkuLangPO> existSubtitleMap = mkuLangAtomicService.getMkuLangPoBySubtitles(mkuTitles, LangConstant.LANG_BR);

        // 查询巴西的国家池
        List<CountryMkuPO> countryMkus = countryMkuAtomicService.getPoByMkuIdAndCountryCode(mkuIds, CountryConstant.COUNTRY_BR);
        Set<Long> poolMkuIds = countryMkus.stream().map(CountryMkuPO::getMkuId).collect(Collectors.toSet());

        // 有多个标题相同返回第一个

        Map<String, MkuSubtitleImportReqDTO.Item> itemMap = items.stream().filter(item -> item.getMkuId() != null).collect(Collectors.toMap(MkuSubtitleImportReqDTO.Item::getMkuId, Function.identity(), (k1, k2) -> k1));

        for (MkuSubtitleImportReqDTO.Item item : items) {
            if (!item.getValid()) {
                continue;
            }
            Long mkuId = Long.parseLong(item.getMkuId());

            String mkuSubtitle = item.getMkuSubtitle();

            if (!existMkuIdMap.containsKey(mkuId)) {
                item.addErrorMsg("MKUID不存在，请检查！");
                continue;
            }

            if (existSubtitleMap.containsKey(mkuSubtitle)) {
                MkuLangPO mku = existSubtitleMap.get(mkuSubtitle);
                MkuSubtitleImportReqDTO.Item excelItem = itemMap.get(String.valueOf(mku.getMkuId()));
                if (excelItem == null && !mku.getMkuId().equals(mkuId)) {
                    item.addErrorMsg(String.format("短标题导入结果与MKUID:%d 重复，请人工修改短标题！", mku.getMkuId()));
                }
                continue;
            }

            if (!poolMkuIds.contains(mkuId)) {
                item.addErrorMsg("MKUID未入国家池，请检查！");
            }
        }

        return existMkuIdMap;
    }

    private static void excelBasicValid(List<MkuSubtitleImportReqDTO.Item> items) {

        // 校验重复数据
        Map<String, Long> titleCountMap = items.stream().filter(item -> StringUtils.isNotEmpty(item.getMkuSubtitle()))
                .collect(Collectors.groupingBy(MkuSubtitleImportReqDTO.Item::getMkuSubtitle, Collectors.counting()));

        Map<String, Long> mkuIdCountMap = items.stream().filter(item -> StringUtils.isNotEmpty(item.getMkuId()))
                .collect(Collectors.groupingBy(MkuSubtitleImportReqDTO.Item::getMkuId, Collectors.counting()));

        Set<String> titleDuplicates = titleCountMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        Set<String> mkuIdDuplicates = mkuIdCountMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        // 初步校验数据
        for (MkuSubtitleImportReqDTO.Item item : items) {
            if (!item.getValid()) {
                continue;
            }
            String mkuId = item.getMkuId();

            // 校验
            String mkuSubtitle = item.getMkuSubtitle();

            if (titleDuplicates.contains(mkuSubtitle)) {
                item.addErrorMsg("导入短标题重复，请检查！");
                continue;
            }

            if (mkuIdDuplicates.contains(mkuId)) {
                item.addErrorMsg("MKUID重复，请检查！");
                continue;
            }

            if (!org.apache.commons.lang.StringUtils.isNumeric(mkuId)) {
                item.addErrorMsg("MKUID不存在，请检查！");
            }
        }
    }

    private SubtitleResultVO validateTitle(MkuLangPO mkuLangPO) {
        List<MkuLangPO> mkuLangPoByTitle = mkuLangAtomicService.getMkuLangPoByTitle(mkuLangPO.getMkuId(),mkuLangPO.getMkuTitle(), mkuLangPO.getLang());
        mkuLangPoByTitle.removeIf(po ->
            Objects.equals(po.getMkuId(), mkuLangPO.getMkuId())
        );
        SubtitleResultVO subtitleResultVO = new SubtitleResultVO();
        if(CollectionUtils.isNotEmpty(mkuLangPoByTitle)){
            Set<String> mkuIds = mkuLangPoByTitle.stream().map(MkuLangPO::getMkuId).map(String::valueOf).collect(Collectors.toSet());
            String mkuIdStr = String.join(Constant.COMMA, mkuIds);
            subtitleResultVO.setIsSuccess(Boolean.FALSE);
            subtitleResultVO.setTitleMkuIds(mkuIds);
            String content = String.format("重复发品（葡文长标题相同），重复MKUID %s，请删除同品或修正标题！", mkuIdStr);
            subtitleResultVO.setMessage(content);
            return subtitleResultVO;
        }
        if(mkuLangPO.getMkuTitle().length() <= 40) {
            List<MkuLangPO> mkuLangPoBySubtitle = mkuLangAtomicService.getMkuLangPoBySubtitle(mkuLangPO.getMkuTitle(), mkuLangPO.getLang());
            if(CollectionUtils.isNotEmpty(mkuLangPoBySubtitle)){
                Set<String> mkuIds = mkuLangPoBySubtitle.stream().map(MkuLangPO::getMkuId).map(String::valueOf).collect(Collectors.toSet());
                String mkuIdStr = String.join(Constant.COMMA, mkuIds);
                subtitleResultVO.setIsSuccess(Boolean.FALSE);
                subtitleResultVO.setSubtitleMkuIds(mkuIds);
                String content = String.format("短标题生成结果与MKUID %s重复，请人工修改短标题！", mkuIdStr);
                subtitleResultVO.setMessage(content);
                return subtitleResultVO;
            }else {
                subtitleResultVO.setIsSuccess(Boolean.FALSE);
                subtitleResultVO.setSubtitle(mkuLangPO.getMkuTitle());
                subtitleResultVO.setLength(mkuLangPO.getMkuTitle().length());
                subtitleResultVO.setMessage("");
                return subtitleResultVO;
            }
        }
        return subtitleResultVO;
    }

    private void handleSubtitle(SubtitleResultVO subtitleResultVO,String title,String lang,ChatGptProperties chatGptProperties,int cycleNumber){
        CallerInfo callerInfo = Profiler.registerInfo(systemProfile + "-MkuLangManageServiceImpl.updateSubtitle");
        String prompt = null;
        String result = "";
        cycleNumber ++;
        try {
            Integer length = chatGptProperties.getContentLength().get(lang) - 3;
            prompt = chatGptProperties.getPrompt().get(lang);
            String paramContent = String.format(prompt,title,length, lang);
            String modelName = chatGptProperties.getChatGptModel().get(ChatGptProperties.SUBTITLE);
            result = chatGptService.postGpt(paramContent,modelName);
            List<String> filterList = chatGptProperties.getFilterList();
            if(filterList.contains(result)){
                result = "";
            }
            if(cycleNumber >= chatGptProperties.getCycleNumber()){
                return;
            }
            if(StringUtils.isBlank(result)){
                subtitleResultVO.setIsSuccess(Boolean.FALSE);
                subtitleResultVO.setMessage("大模型翻译失败");
                subtitleResultVO.setSubtitle("");
                handleSubtitle(subtitleResultVO,title,lang,chatGptProperties,cycleNumber);
            }else{
                List<MkuLangPO> mkuLangPoBySubtitle = mkuLangAtomicService.getMkuLangPoBySubtitle(result, lang);
                if(CollectionUtils.isNotEmpty(mkuLangPoBySubtitle)){
                    Set<String> mkuIds = mkuLangPoBySubtitle.stream().map(MkuLangPO::getMkuId).map(String::valueOf).collect(Collectors.toSet());
                    String mkuIdStr = String.join(Constant.COMMA, mkuIds);
                    subtitleResultVO.setIsSuccess(Boolean.FALSE);
                    subtitleResultVO.setSubtitleMkuIds(mkuIds);
                    String content = String.format("短标题生成结果与MKUID %s重复，请人工修改短标题！", mkuIdStr);
                    subtitleResultVO.setMessage(content);
                    subtitleResultVO.setSubtitle("");
                    subtitleResultVO.setLength(result.length());
                    handleSubtitle(subtitleResultVO,title,lang,chatGptProperties,cycleNumber);
                }else {
                    subtitleResultVO.setIsSuccess(Boolean.TRUE);
                    subtitleResultVO.setSubtitle(result);
                    subtitleResultVO.setLength(result.length());
                    subtitleResultVO.setMessage("");
                }
            }
        }catch (Exception e){
            log.error("MkuManageServiceImpl.updateSubtitle error mkuLangPo:{}", JSON.toJSONString(title),e);
            Profiler.businessAlarm(UmpKeyConstant.CHATGPT_SUBTITEL_WARNING, String.format("【%s】 %s 转换短标题异常, 异常信息:%s"
                , systemProfile
                , LevelCode.P1.getMessage()
                , e.getMessage()));
            Profiler.functionError(callerInfo);
        }finally {
            log.info("MkuManageServiceImpl.updateSubtitle update mkuLangPo:{},prompt:{}", JSON.toJSONString(title), JSON.toJSONString(prompt));
            Profiler.registerInfoEnd(callerInfo);
        }
    }



    private void notifyMsg(MkuLangPO mkuLangPO){
        //SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        MkuChangeMsg target = new MkuChangeMsg(mkuLangPO.getMkuId(), OrderMsgStatusEnum.UPDATE.getCode());
        List<OpenMsgPO> openMsgPOList = openMsgAtomicService.fission(target, MsgTypeEnum.MKU_INFO.getCode(), mkuLangPO.getUpdater(),
                mkuLangPO.getUpdateTime());
        if(CollectionUtils.isNotEmpty(openMsgPOList)) {
            openMsgAtomicService.batchAdd(openMsgPOList);
        }
    }

    private void validateInput(MkuLangVO vo) {
        if (Objects.isNull(vo) || Objects.isNull(vo.getId())) {
            throw new BizException("数据或id不能为空");
        }
    }

    @Override
    public DataResponse<String> updateMkuTitleLang(Long mkuId, Map<String, String> langNameMap) {
        log.info("MkuLangManageServiceImpl.updateMkuTitleLang, mkuId:{}, langNameMap:{}", mkuId, langNameMap);
        
        // 参数校验
        if (mkuId == null) {
            log.info("MkuLangManageServiceImpl.updateMkuTitleLang mkuId不能为空");
            throw new BizException("mkuId不能为空");
        }
        
        if (langNameMap == null || langNameMap.isEmpty()) {
            log.info("MkuLangManageServiceImpl.updateMkuTitleLang 多语言名称映射不能为空");
            throw new BizException("多语言名称映射不能为空");
        }
        
        try {
            // 获取当前MKU的所有多语言信息
            Map<String, MkuLangPO> currentLangMap = mkuLangAtomicService.listLangByMkuId(mkuId);
            
            // 获取MKU草稿信息
            MkuDraftPO mkuDraftPO = mkuDraftAtomicService.queryDraftByMkuId(mkuId);
            
            boolean hasChanges = false;
            Map<String, String> newLangNameMap = new HashMap<>();
            // 处理每种语言的名称更新
            for (Map.Entry<String, String> entry : langNameMap.entrySet()) {
                String lang = entry.getKey();
                String newTitle = entry.getValue();
                
                if (StringUtils.isBlank(lang) || StringUtils.isBlank(newTitle)) {
                    log.info("MkuLangManageServiceImpl.updateMkuTitleLang, 语言{}的标题为空，跳过更新, mkuId:{}", lang, mkuId);
                    continue;
                }
                newLangNameMap.put(lang, newTitle);
                
                // 检查是否有变化
                MkuLangPO currentLangPO = currentLangMap.get(lang);
                if (currentLangPO != null && StringUtils.equals(currentLangPO.getMkuTitle(), newTitle)) {
                    log.info("MkuLangManageServiceImpl.updateMkuTitleLang, 语言{}的标题没有变化，跳过更新, mkuId:{}", lang, mkuId);
                    continue;
                }
                
                hasChanges = true;

                // 更新MKU多语言表，如果当前语言不存在，则新增，如果存在，则更新
                updateMkuLangTitle(mkuId, lang, newTitle, currentLangPO);
            }

            // 更新MKU草稿表的JSON信息
            if (mkuDraftPO != null) {
                updateMkuDraftJson(mkuDraftPO, newLangNameMap);
            }
            
            if (!hasChanges) {
                log.info("MkuLangManageServiceImpl.updateMkuTitleLang, 没有需要更新的内容，mkuId:{}", mkuId);
                return DataResponse.success("没有需要更新的内容");
            }
            
            log.info("MkuLangManageServiceImpl.updateMkuTitleLang, MKU多语言标题更新成功，mkuId:{}", mkuId);
            return DataResponse.success("MKU多语言标题更新成功");
            
        } catch (Exception e) {
            log.error("MkuLangManageServiceImpl.updateMkuTitleLang, 更新MKU多语言标题失败，mkuId:{}, langNameMap:{}", mkuId, langNameMap, e);
            throw e;
        }
    }
    
    /**
     * 更新MKU多语言标题
     */
    private void updateMkuLangTitle(Long mkuId, String lang, String newTitle, MkuLangPO currentLangPO) {
        String sourceJsonString = null;
        MkuLangPO targetPO = new MkuLangPO();
        
        if (currentLangPO != null) {
            // 更新现有记录
            sourceJsonString = JSONObject.toJSONString(currentLangPO);
            targetPO.setId(currentLangPO.getId());
            targetPO.setMkuId(mkuId);
            targetPO.setLang(lang);
            targetPO.setMkuTitle(newTitle);
            if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                targetPO.setUpdater(Constant.SYSTEM);
            } 
            targetPO.setUpdateTime(new Date());
            
            mkuLangAtomicService.updateById(targetPO);
            
            // 合并数据用于日志记录
            targetPO.setMkuId(mkuId);
            targetPO.setLang(lang);
            
        } else {
            // 新增记录
            targetPO.setMkuId(mkuId);
            targetPO.setLang(lang);
            targetPO.setMkuTitle(newTitle);
            if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                targetPO.setCreator(Constant.SYSTEM);
                targetPO.setUpdater(Constant.SYSTEM);
            }
            targetPO.setCreateTime(new Date());
            targetPO.setUpdateTime(new Date());
            targetPO.setYn(YnEnum.YES.getCode());
            
            mkuLangAtomicService.save(targetPO);
            
            sourceJsonString = "{}";
        }
        
        // 记录日志
        sendLog(mkuId.toString(), sourceJsonString, targetPO);
    }
    
    /**
     * 更新MKU草稿JSON信息
     */
    private void updateMkuDraftJson(MkuDraftPO mkuDraftPO, Map<String, String> langNameMap) {
        try {
            if (StringUtils.isBlank(mkuDraftPO.getMkuJsonInfo())) {
                log.info("MkuLangManageServiceImpl.updateMkuDraftJson, MKU草稿JSON信息为空，跳过更新, mkuId:{}", mkuDraftPO.getMkuId());
                return;
            }
            
            JSONObject mkuJsonInfoObject = JSONObject.parseObject(mkuDraftPO.getMkuJsonInfo());
            if (mkuJsonInfoObject == null) {
                log.info("MkuLangManageServiceImpl.updateMkuDraftJson, MKU草稿JSON信息解析失败，跳过更新, mkuId:{}", mkuDraftPO.getMkuId());
                return;
            }
            
            // 获取或创建mkuLangVOS数组
            JSONArray mkuLangVOS = mkuJsonInfoObject.getJSONArray("mkuLangVOS");
            if (mkuLangVOS == null) {
                mkuLangVOS = new JSONArray();
                mkuJsonInfoObject.put("mkuLangVOS", mkuLangVOS);
            }
            // 更新mkuLangVOS
            for (Map.Entry<String, String> entry : langNameMap.entrySet()) {
                String lang = entry.getKey();
                String newTitle = entry.getValue();
                // 查找是否已存在该语言的记录
                boolean found = false;
                for (int i = 0; i < mkuLangVOS.size(); i++) {
                    JSONObject langObj = mkuLangVOS.getJSONObject(i);
                    if (langObj != null && StringUtils.equals(langObj.getString("lang"), lang)) {
                        langObj.put("mkuTitle", newTitle);
                        found = true;
                        break;
                    }
                }
                
                // 如果不存在，添加新的语言记录
                if (!found) {
                    JSONObject newLangObj = new JSONObject();
                    newLangObj.put("lang", lang);
                    newLangObj.put("mkuTitle", newTitle);
                    mkuLangVOS.add(newLangObj);
                }
            }

            // 更新草稿表
            mkuDraftPO.setMkuJsonInfo(mkuJsonInfoObject.toJSONString());
            if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                mkuDraftPO.setUpdater(Constant.SYSTEM);
            }
            mkuDraftPO.setUpdateTime(new Date());
            
            mkuDraftAtomicService.updateById(mkuDraftPO);
            
            log.info("MkuLangManageServiceImpl.updateMkuDraftJson, MKU草稿JSON信息更新成功, mkuId:{}", mkuDraftPO.getMkuId());
            
        } catch (Exception e) {
            log.error("MkuLangManageServiceImpl.updateMkuDraftJson, 更新MKU草稿JSON信息异常, mkuId:{}", mkuDraftPO.getMkuId(), e);
            throw e;
        }
    }

    /**
     * 发送日志到数据库。
     * @param mkuId MKU的唯一标识。
     * @param sourceJsonString 日志的源数据。
     * @param targetData 日志的目标数据。
     */
    private void sendLog(String mkuId, String sourceJsonString, MkuLangPO targetData) {
        try {
            SkuLogPO skuLogPO = new SkuLogPO();
            skuLogPO.setSourceJson(sourceJsonString);
            skuLogPO.setTargetJson(JSONObject.toJSONString(targetData));
            skuLogPO.setKeyType(KeyTypeEnum.MKU_LANG.getCode());
            skuLogPO.setKeyId(mkuId);
            skuLogPO.setCreator(targetData.getUpdater());
            skuLogPO.setUpdater(targetData.getUpdater());
            skuLogAtomicService.save(skuLogPO);
            log.info("MkuLangManageServiceImpl.sendLog, 日志保存成功，MKU: {}", mkuId);
        } catch (Exception e) {
            log.error("MkuLangManageServiceImpl.sendLog, 存储日志异常，MKU: {},sourceData:{},targetData:{} ,Error: {}", mkuId, sourceJsonString, JSONObject.toJSONString(targetData), e.getMessage(), e);
        }
    }

}
