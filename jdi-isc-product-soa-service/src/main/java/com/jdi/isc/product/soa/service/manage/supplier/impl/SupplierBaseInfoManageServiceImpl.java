package com.jdi.isc.product.soa.service.manage.supplier.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.VcLoginConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.util.DateTimeUtils;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.domain.enums.supplier.*;
import com.jdi.isc.product.soa.domain.supplier.biz.SupplierAllInfoExtendVO;
import com.jdi.isc.product.soa.domain.supplier.biz.SupplierAllInfoVO;
import com.jdi.isc.product.soa.domain.supplier.biz.SupplierBaseInfoPageVO;
import com.jdi.isc.product.soa.domain.supplier.biz.SupplierBaseInfoVO;
import com.jdi.isc.product.soa.domain.supplier.po.*;
import com.jdi.isc.product.soa.domain.vendor.po.VendorPO;
import com.jdi.isc.product.soa.service.atomic.supplier.*;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.service.manage.supplier.SupplierBaseInfoManageService;
import com.jdi.isc.product.soa.service.manage.vendor.VendorManageService;
import com.jdi.isc.product.soa.service.mapstruct.supplier.BusinessLineConvert;
import com.jdi.isc.product.soa.service.mapstruct.supplier.SupplierAccountConvert;
import com.jdi.isc.product.soa.service.mapstruct.supplier.SupplierBaseInfoConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 供应商基础信息数据维护服务实现
 * @Author: zhaojianguo21
 * @Date: 2024/03/21 11:17
 **/

@Slf4j
@Service
public class SupplierBaseInfoManageServiceImpl extends BaseManageSupportService<SupplierBaseInfoVO, SupplierBaseInfoPO> implements SupplierBaseInfoManageService {

    @Resource
    private SupplierBaseInfoAtomicService supplierBaseInfoAtomicService;

    @Resource
    private SupplierAuditRecordAtomicService supplierAuditRecordAtomicService;

    @Resource
    private BusinessLineAtomicService businessLineAtomicService;

    @Resource
    private SupplierAccountAtomicService supplierAccountAtomicService;

    @Resource
    private JimUtils jimUtils;

    @Resource
    private VendorManageService vendorManageService;

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> saveOrUpdate(SupplierAllInfoVO input) {
        SupplierBaseInfoVO baseInfo = input.getBaseInfo();
        log.info("saveOrUpdate, input={}", JSONObject.toJSONString(baseInfo));

        // 参数业务校验
        DataResponse<Boolean> checkResult = checkAndInitInput(input);
        if (!checkResult.getSuccess()){
            log.info("saveOrUpdate, check input fail, input={}, checkResult={}", JSONObject.toJSONString(baseInfo), JSONObject.toJSONString(checkResult));
            return checkResult;
        }

        // 保存对象
        SupplierBaseInfoPO po = SupplierBaseInfoConvert.INSTANCE.vo2Po(baseInfo);

        boolean newSupplier = Objects.isNull(po.getId());
        if (!newSupplier){
            // 编辑时不允许修改如下字段。重置为空，就不会更新

            // 分片字段不可以被更新，需要设置为空
            po.setSupplierCode(null);
        }

        boolean saveRes = supplierBaseInfoAtomicService.saveOrUpdate(po);
        if (!saveRes){
            log.warn("saveOrUpdate, SupplierBaseInfoPO fail. SupplierBaseInfoPO={}", JSONObject.toJSONString(po));
            throw new BizException("保存供应商基础信息失败");
        }
        // 缓存集合中无当前供应商新增
        String key = VcLoginConstant.JIM_PREFIX_SUPPLIER_COUNTRY + po.getBusinessLicenseCountry();
        if (newSupplier && !jimUtils.sIsMember(key, po.getSupplierCode())) {
            jimUtils.sAdd(key, po.getSupplierCode());
        }
        log.warn("saveOrUpdate, SupplierBaseInfoPO success. SupplierBaseInfoPO={}", JSONObject.toJSONString(po));
        return DataResponse.success(true);
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> delete(Long id){
        boolean delRes = supplierBaseInfoAtomicService.delByIds(Sets.newHashSet(id));
        if (!delRes){
            log.warn("delete, SupplierBaseInfoPO fail. id={}", id);
            throw new BizException("删除供应商基础信息失败");
        }

        return DataResponse.success(true);
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> deleteBatch(Set<Long> ids){
        boolean delRes = supplierBaseInfoAtomicService.delByIds(ids);
        if (!delRes){
            log.warn("deleteBatch, SupplierBaseInfoPO fail. ids={}", JSONObject.toJSONString(ids));
            throw new BizException("批量删除供应商基础信息失败");
        }

        return DataResponse.success(true);
    }

    @Override
    public SupplierBaseInfoVO detail(Long id) {
        SupplierBaseInfoPO po = supplierBaseInfoAtomicService.getValidById(id);
        if (null==po){
            log.info("detail, SupplierBaseInfoPO null. id={}", id);
            return null;
        }

        SupplierBaseInfoVO vo = convertPo2Vo(po);
        fullEnumFieldName(vo);
        return vo;
    }

    @Override
    public SupplierBaseInfoVO getByCode(String supplierCode){
        SupplierBaseInfoPO po = supplierBaseInfoAtomicService.getByCode(supplierCode);
        if (null==po){
            log.info("getByCode, SupplierBaseInfoPO null. supplierCode={}", supplierCode);
            return null;
        }

        SupplierBaseInfoVO vo = convertPo2Vo(po);
        fullEnumFieldName(vo);
        return vo;
    }


    @Override
    public PageInfo<SupplierBaseInfoPageVO.Response> page(SupplierBaseInfoPageVO.Request input) {
        PageInfo<SupplierBaseInfoPageVO.Response> output = new PageInfo<>();
        output.setSize(input.getSize());
        output.setIndex(input.getIndex());

        // 查询列表
        Page<SupplierBaseInfoPO> pageDB = new Page<>(input.getIndex(), input.getSize());
        LambdaQueryWrapper<SupplierBaseInfoPO> wrapper = Wrappers.<SupplierBaseInfoPO>lambdaQuery()
                .eq(SupplierBaseInfoPO::getYn, YnEnum.YES.getCode())
                .orderByDesc(SupplierBaseInfoPO::getUpdateTime);
        Page<SupplierBaseInfoPO> dbRecord = supplierBaseInfoAtomicService.page(pageDB, wrapper);
        output.setTotal(dbRecord.getTotal());
        if (CollectionUtils.isEmpty(dbRecord.getRecords())){
            return output;
        }

        List<SupplierBaseInfoPageVO.Response> pageList = SupplierBaseInfoConvert.INSTANCE.pageListPo2Vo(dbRecord.getRecords());
        if (CollectionUtils.isNotEmpty(pageList)){
            output.setRecords(pageList);
        }

        return output;
    }

    @Override
    public void fullEnumFieldName(SupplierBaseInfoVO input) {
        if (null==input){
            return;
        }
        SupplierNatureEnum natureEnum = SupplierNatureEnum.forCode(input.getNature());
        input.setNatureName(null!=natureEnum?natureEnum.getDesc():"");

        SupplierEnterpriseTypeEnum enterpriseTypeEnum = SupplierEnterpriseTypeEnum.forCode(input.getEnterpriseType());
        input.setEnterpriseTypeName(null!=enterpriseTypeEnum?enterpriseTypeEnum.getDesc():"");

        SupplierAuditStatusEnum auditStatusEnum = SupplierAuditStatusEnum.forCode(input.getAuditStatus());
        input.setAuditStatusName(null!=auditStatusEnum?auditStatusEnum.getDesc():"");

        SupplierStatusEnum supplierStatusEnum = SupplierStatusEnum.forCode(input.getSupplierStatus());
        input.setSupplierStatusName(null!=supplierStatusEnum?supplierStatusEnum.getDesc():"");
    }

    @Override
    public DataResponse<Boolean> initReqParam(SupplierAllInfoVO input){
        SupplierBaseInfoVO baseInfo = input.getBaseInfo();
        SupplierAllInfoExtendVO extend = input.getExtend();

        if (StringUtils.isNotBlank(baseInfo.getBusinessLicenseRegistTimeFmt())){
            long milli = DateTimeUtils.parseFmtDayToMilli(baseInfo.getBusinessLicenseRegistTimeFmt(), DateTimeUtils.getZoneOffset(baseInfo.getBusinessLicenseCountry()));
            baseInfo.setBusinessLicenseRegistTime(milli);
        }


        if (SupplierOptActionEnum.SAVE_DRAFT.getCode().equals(extend.getOptAction())){
            baseInfo.setAuditStatus(SupplierAuditStatusEnum.DRAFT.getCode());
        }else if (SupplierOptActionEnum.SUBMIT_APPROVAL.getCode().equals(extend.getOptAction())){
            baseInfo.setAuditStatus(SupplierAuditStatusEnum.WAIT_APPROVAL.getCode());
        }else {
            log.warn("initReqParam, OptAction val invalid. optAction={}", extend.getOptAction());
            return DataResponse.error("异常请求");
        }

        if (null==baseInfo.getId()){
            // 初始化状态
            baseInfo.setSupplierStatus(SupplierStatusEnum.STOP.getCode());
        }else {
            // 修改操作禁止变更状态字段值。
            baseInfo.setSupplierStatus(null);
        }

        return DataResponse.success();
    }


    /**
     * 参数校验
     * @param input 提交参数
     * @return 检查结果
     */
    private DataResponse<Boolean> checkAndInitInput(SupplierAllInfoVO input){
        SupplierBaseInfoVO baseInfo = input.getBaseInfo();
        SupplierAllInfoExtendVO extend = input.getExtend();

        SupplierBaseInfoPO supplierBaseInfoPO = supplierBaseInfoAtomicService.getByCode(baseInfo.getSupplierCode());
        // 已存在商家
        if (null!=supplierBaseInfoPO){
            SupplierAuditRecordPO auditRecordPO = supplierAuditRecordAtomicService.queryByBatchNum(supplierBaseInfoPO.getBatchNum());
            if (null==auditRecordPO){
                log.warn("checkAndInitInput, SupplierAuditRecordPO not exists. input={}", JSONObject.toJSONString(input));
                return DataResponse.error("未查到商家状态记录。");
            }
            if (!SupplierAuditStatusEnum.DRAFT.getCode().equals(auditRecordPO.getAuditStatus()) &&
                    !SupplierAuditStatusEnum.REJECT.getCode().equals(auditRecordPO.getAuditStatus()) ){
                log.warn("checkAndInitInput, invalid option. ");
                return DataResponse.error("不允许修改商家信息。");
            }
        }

        return DataResponse.success();
    }

    private SupplierBaseInfoVO convertPo2Vo(SupplierBaseInfoPO po){
        SupplierBaseInfoVO vo = SupplierBaseInfoConvert.INSTANCE.po2Vo(po);
        String fmtDay = DateTimeUtils.milliToFmtDay(vo.getBusinessLicenseRegistTime(), DateTimeUtils.getZoneOffset(vo.getBusinessLicenseCountry()));
        vo.setBusinessLicenseRegistTimeFmt(fmtDay);
        return vo;
    }

    /**
     * 根据品牌ID集合查询供应商信息
     */
    @Override
    public List<SupplierAllInfoVO> querySupplierInfoByBrandId(Set<Long> brandIds) {
        List<SupplierAllInfoVO> result = new ArrayList<>();
        List<Long> brandList = new ArrayList<>(brandIds);
        List<List<Long>> partition = Lists.partition(brandList, Constant.PAGE_SIZE);
        List<BusinessLinePO> lineRelationList = new ArrayList<>();
        Map<String, List<SupplierAccountPO>> allSupplierAccountMap = new HashMap<>();
        Map<String, SupplierBaseInfoPO> allBaseMap = new HashMap<>();
        for (List<Long> part : partition) {
            List<BusinessLinePO> relationList = businessLineAtomicService.queryByBrandId(part);
            if (CollectionUtils.isNotEmpty(relationList)) {
                lineRelationList.addAll(relationList);
            }
            //查询供应商账户信息
            Set<String> supplierCodeSet = relationList.stream().map(BusinessLinePO::getSupplierCode).filter(c -> !allBaseMap.containsKey(c)).collect(Collectors.toSet());
            if (supplierCodeSet.isEmpty()) {
                continue;
            }
            Map<String, SupplierBaseInfoPO> baseInfoMap = supplierBaseInfoAtomicService.batchQueryByCode(supplierCodeSet);
            if (MapUtils.isNotEmpty(baseInfoMap)) {
                allBaseMap.putAll(baseInfoMap);
            }
            //查询邮箱信息
            List<SupplierAccountPO> accountList = supplierAccountAtomicService.batchQueryByCode(supplierCodeSet);
            if (CollectionUtils.isNotEmpty(accountList)) {
                //品牌供应商
                allSupplierAccountMap.putAll(accountList.stream().collect(Collectors.groupingBy(SupplierAccountPO::getSupplierCode)));
            }
        }

        //按照供应商分组
        Map<String, List<BusinessLinePO>> supplierBrandList = lineRelationList.stream()
                .collect(Collectors.groupingBy(BusinessLinePO::getSupplierCode));
        supplierBrandList.forEach((k, v) -> {
            SupplierAllInfoVO supplierVo = new SupplierAllInfoVO();
            supplierVo.setBusinessLineList(BusinessLineConvert.INSTANCE.po2Vo(v));
            List<SupplierAccountPO> accountList = allSupplierAccountMap.get(k);
            supplierVo.setAccountList(SupplierAccountConvert.INSTANCE.poToVOList(accountList));
            SupplierBaseInfoPO supplierBaseInfoPO = allBaseMap.get(k);
            supplierVo.setBaseInfo(SupplierBaseInfoConvert.INSTANCE.po2Vo(supplierBaseInfoPO));
            result.add(supplierVo);
        });
        return result;
    }

    @Override
    public Map<String, SupplierBaseInfoPO> batchQueryByCode(Set<String> supplierCodeSet) {
        if (CollectionUtils.isEmpty(supplierCodeSet)) {
            return Collections.emptyMap();
        }
        // 本土供应商批量查询
        Map<String, SupplierBaseInfoPO> supplierBaseInfoPOMap = supplierBaseInfoAtomicService.batchQueryByCode(supplierCodeSet);
        // 国内供应商批量查询
        Map<String, VendorPO> vendorPOMap = vendorManageService.queryVendorByJdVendorCodeMap(supplierCodeSet);

        Map<String,SupplierBaseInfoPO> resultMap = Maps.newHashMap();
        // 本土供应商直接赋值
        if (MapUtils.isNotEmpty(supplierBaseInfoPOMap)) {
            resultMap.putAll(supplierBaseInfoPOMap);
        }
        // 国内供应商补充信息后增加到返回结果中
        if (MapUtils.isNotEmpty(vendorPOMap)) {
            vendorPOMap.forEach((k,v) -> {
                if (!supplierBaseInfoPOMap.containsKey(k)){
                    SupplierBaseInfoPO baseInfoPO = new SupplierBaseInfoPO();
                    baseInfoPO.setSupplierCode(k);
                    baseInfoPO.setBusinessLicenseName(v.getVendorName());
                    baseInfoPO.setMerchantCode(v.getMCode());
                    baseInfoPO.setTaxId(v.getTaxCode());
                    resultMap.put(k, baseInfoPO);
                }
            });
        }
        return resultMap;
    }
}
