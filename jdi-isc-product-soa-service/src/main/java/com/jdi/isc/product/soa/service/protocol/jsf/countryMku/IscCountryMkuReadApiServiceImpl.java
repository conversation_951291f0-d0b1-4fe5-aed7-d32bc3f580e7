package com.jdi.isc.product.soa.service.protocol.jsf.countryMku;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.countryMku.IscCountryMkuReadApiService;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuCheckDTO;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuCheckReqDTO;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuExportDTO;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuPageApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientDetailReqApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientInPoolApiDTO;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuCheckReqVO;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuCheckVO;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuExportVO;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuPageVO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuClientDetailReqVO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuClientInPoolVO;
import com.jdi.isc.product.soa.service.manage.countryMku.CountryMkuManageService;
import com.jdi.isc.product.soa.service.mapstruct.countryMku.CountryMkuConvert;
import com.jdi.isc.product.soa.service.mapstruct.mku.MkuConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class IscCountryMkuReadApiServiceImpl implements IscCountryMkuReadApiService {

    @Resource
    private CountryMkuManageService countryMkuManageService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<CountryMkuPageApiDTO.Response>> pageSearch(CountryMkuPageApiDTO.Request input) {
        log.info("IscCountryMkuReadApiServiceImpl.pageSearch input:{}", JSONObject.toJSONString(input));
        LangContextHolder.init(StringUtils.isNotBlank(input.getLang())?input.getLang(): LangConstant.LANG_ZH);
        CountryMkuPageVO.Request param = CountryMkuConvert.INSTANCE.pageApiReq2PageVoReq(input);
        PageInfo<CountryMkuPageVO.Response> result = countryMkuManageService.pageSearch(param);
        return DataResponse.success(CountryMkuConvert.INSTANCE.pageVoResponse2PageApiRes(result));
    }



    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<CountryMkuPageApiDTO.Request>> auditStatusNum(CountryMkuPageApiDTO.Request input) {
        log.info("IscCountryMkuReadApiServiceImpl.auditStatusNum input:{}", JSONObject.toJSONString(input));
        LangContextHolder.init(StringUtils.isNotBlank(input.getLang())?input.getLang(): LangConstant.LANG_ZH);
        CountryMkuPageVO.Request param = CountryMkuConvert.INSTANCE.pageApiReq2PageVoReq(input);
        List<CountryMkuPageVO.Request> result = countryMkuManageService.auditStatusNum(param);
        return DataResponse.success(CountryMkuConvert.INSTANCE.listReqVo2Api(result));
    }

    @Override
    public DataResponse<CountryMkuCheckDTO> checkBlackData(CountryMkuCheckReqDTO input) {
        log.info("IscCountryMkuReadApiServiceImpl.checkBlackData input:{}", JSONObject.toJSONString(input));
        CountryMkuCheckReqVO countryMkuCheckReqVO = CountryMkuConvert.INSTANCE.checkDto2Vo(input);
        DataResponse<CountryMkuCheckVO> response = countryMkuManageService.checkBlackData(countryMkuCheckReqVO);
        CountryMkuCheckVO data = response.getData();
        CountryMkuCheckDTO countryMkuCheckDTO = CountryMkuConvert.INSTANCE.checkVo2Dto(data);
        log.info("IscCountryMkuReadApiServiceImpl.auditStatusNum res:{}", JSONObject.toJSONString(countryMkuCheckDTO));
        return DataResponse.success(countryMkuCheckDTO);
    }

    @Override
    public DataResponse<CountryMkuCheckDTO> checkOutBlackData(CountryMkuCheckReqDTO input) {
        log.info("IscCountryMkuReadApiServiceImpl.checkOutBlackData input:{}", JSONObject.toJSONString(input));
        CountryMkuCheckReqVO countryMkuCheckReqVO = CountryMkuConvert.INSTANCE.checkDto2Vo(input);
        DataResponse<CountryMkuCheckVO> response = countryMkuManageService.checkOutBlackData(countryMkuCheckReqVO);
        CountryMkuCheckVO data = response.getData();
        CountryMkuCheckDTO countryMkuCheckDTO = CountryMkuConvert.INSTANCE.checkVo2Dto(data);
        log.info("IscCountryMkuReadApiServiceImpl.auditStatusNum res:{}", JSONObject.toJSONString(countryMkuCheckDTO));
        return DataResponse.success(countryMkuCheckDTO);
    }

    @Override
    public DataResponse<List<MkuClientInPoolApiDTO>> checkInPool(CountryMkuCheckReqDTO input) {
        log.info("MkuClientApiServiceImpl.checkInPool, checkInPool：{}", JSONObject.toJSONString(input));
        CountryMkuCheckReqVO countryMkuCheckReqVO = CountryMkuConvert.INSTANCE.checkDto2Vo(input);
        DataResponse<List<MkuClientInPoolVO>> response =countryMkuManageService.checkInPool(countryMkuCheckReqVO);
        List<MkuClientInPoolVO> mkuList = response.getData();
        List<MkuClientInPoolApiDTO> apiDTOList = MkuConvert.INSTANCE.listVO2DTO(mkuList);
        log.info("MkuClientApiServiceImpl.checkInPool, req：{},res:{}", JSONObject.toJSONString(input),JSONObject.toJSONString(apiDTOList));
        return DataResponse.success(apiDTOList);
    }

    @Override
    public DataResponse<List<CountryMkuExportDTO>> queryCountryMku(CountryMkuPageApiDTO.Request input) {
        log.info("IscCountryMkuReadApiServiceImpl.queryCountryMku input:{}", JSONObject.toJSONString(input));
        LangContextHolder.init(StringUtils.isNotBlank(input.getLang())?input.getLang(): LangConstant.LANG_ZH);
        CountryMkuPageVO.Request param = CountryMkuConvert.INSTANCE.pageApiReq2PageVoReq(input);
        PageInfo<CountryMkuExportVO> result = countryMkuManageService.queryCountryMku(param);
        List<CountryMkuExportVO> records = result.getRecords();
        log.info("IscCountryMkuReadApiServiceImpl.queryCountryMku input:{},res:{}", JSONObject.toJSONString(input),JSONObject.toJSONString(result));
        return DataResponse.success(CountryMkuConvert.INSTANCE.listVo2DTO(records));
    }
}
