package com.jdi.isc.product.soa.service.manage.taxRate;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.domain.taxRate.biz.ExportTaxRatePageVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.ExportTaxRateVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.GetExportTaxRateVO;

/**
 * @Description: 中国出口税率信息数据维护服务
 * @Author: zhaojianguo21
 * @Date: 2024/11/25 20:58
 **/

public interface ExportTaxRateManageService {

    /**
     * 保存、更新
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<Boolean> saveOrUpdate(ExportTaxRateVO input);

    /**
     * 详情
     * @param input 中国税率入参
     * @return VO对象
     */
    ExportTaxRateVO detail(GetExportTaxRateVO input);

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    PageInfo<ExportTaxRatePageVO.Response> pageSearch(ExportTaxRatePageVO.Request input);
}
