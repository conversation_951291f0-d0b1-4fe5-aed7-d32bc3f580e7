package com.jdi.isc.product.soa.service.atomic.price.agreementPrice;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceWarningPageReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceWarningVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPriceWarningPO;
import com.jdi.isc.product.soa.repository.mapper.price.agreementPrice.CountryAgreementPriceWarningBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Description: 国家协议价预警表原子服务
 * @Author: zhaokun51
 * @Date: 2025/03/18 10:59
 **/
@Slf4j
@Service
public class CountryAgreementPriceWarningAtomicService extends ServiceImpl<CountryAgreementPriceWarningBaseMapper, CountryAgreementPriceWarningPO> {

    /**
     * 未删除的对象
     * @param id id
     * @return 对象
     */
    public CountryAgreementPriceWarningPO getValidById(Long id){
        if(id == null){
            return null;
        }
        LambdaQueryWrapper<CountryAgreementPriceWarningPO> wrapper = Wrappers.<CountryAgreementPriceWarningPO>lambdaQuery()
                .eq(CountryAgreementPriceWarningPO::getId, id)
                .eq(CountryAgreementPriceWarningPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }


    public List<CountryAgreementPriceWarningPO> listByBizNos(List<String> bizNos){
        if(CollectionUtils.isEmpty(bizNos)){
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<CountryAgreementPriceWarningPO> wrapper = Wrappers.<CountryAgreementPriceWarningPO>lambdaQuery()
                .in(CountryAgreementPriceWarningPO::getBizNo, bizNos)
                .eq(CountryAgreementPriceWarningPO::getYn, YnEnum.YES.getCode())
                .orderByDesc(CountryAgreementPriceWarningPO::getUpdateTime);

        List<CountryAgreementPriceWarningPO> warningPOS = this.list(wrapper);
        if(CollectionUtils.isEmpty(warningPOS)){
            return Lists.newArrayList();
        }

        Map<String, CountryAgreementPriceWarningPO> map = warningPOS.stream().collect(Collectors.toMap(CountryAgreementPriceWarningPO::getBizNo, Function.identity(), (k1, k2) -> k1));

        return CollectionUtil.sort(map.values(), Comparator.comparing(CountryAgreementPriceWarningPO::getId, Comparator.nullsLast(Long::compareTo)).reversed());
    }

    /**
     * 未删除的对象
     * @param bizNo id
     * @return 对象
     */
    public CountryAgreementPriceWarningPO getValidByBizNo(String bizNo){
        if(StringUtils.isBlank(bizNo)){
            return null;
        }
        LambdaQueryWrapper<CountryAgreementPriceWarningPO> wrapper = Wrappers.<CountryAgreementPriceWarningPO>lambdaQuery()
                .eq(CountryAgreementPriceWarningPO::getBizNo, bizNo)
                .eq(CountryAgreementPriceWarningPO::getYn, YnEnum.YES.getCode());
        List<CountryAgreementPriceWarningPO> warningPOS = this.list(wrapper);
        if(CollectionUtils.isEmpty(warningPOS)){
            return null;
        }
        return warningPOS.stream().sorted(Comparator.comparing(CountryAgreementPriceWarningPO::getUpdateTime).reversed()).collect(Collectors.toList()).get(0);
    }

    public List<CountryAgreementPriceWarningPO> listBySkuIds(List<Long> skuIds) {
        if(CollectionUtils.isEmpty(skuIds)){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CountryAgreementPriceWarningPO> wrapper = Wrappers.<CountryAgreementPriceWarningPO>lambdaQuery()
                .in(CountryAgreementPriceWarningPO::getSkuId, skuIds)
                .eq(CountryAgreementPriceWarningPO::getYn, YnEnum.YES.getCode());

        return this.list(wrapper);
    }

    public List<CountryAgreementPriceWarningPO> listWarningsByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CountryAgreementPriceWarningPO> wrapper = Wrappers.<CountryAgreementPriceWarningPO>lambdaQuery()
                .in(CountryAgreementPriceWarningPO::getId, ids)
                .eq(CountryAgreementPriceWarningPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    public long getTotal(CountryAgreementPriceWarningPageReqVO input) {
        return super.getBaseMapper().getTotal(input);
    }

    public List<CountryAgreementPriceWarningVO> listSearch(CountryAgreementPriceWarningPageReqVO input) {
        input.setOffset((input.getIndex() - 1) * input.getSize());
        return super.getBaseMapper().listSearch(input);
    }

    public void updateAvailableSaleStatusList(List<CountryAgreementPriceWarningPO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }

        // 排序
        CollectionUtil.sort(updateList, Comparator.comparing(CountryAgreementPriceWarningPO::getId, Comparator.nullsLast(Long::compareTo)));

        for (CountryAgreementPriceWarningPO item : updateList) {
            this.updateAvailableSaleStatus(item);
        }
    }

    public void updateAvailableSaleStatus(CountryAgreementPriceWarningPO update) {
        Preconditions.checkArgument(update.getId() != null, "id不能为空");

        LambdaUpdateWrapper<CountryAgreementPriceWarningPO> updateWrapper = Wrappers.lambdaUpdate(CountryAgreementPriceWarningPO.class)
                .set(CountryAgreementPriceWarningPO::getAvailableSaleStatus, update.getAvailableSaleStatus())
                .set(CountryAgreementPriceWarningPO::getCountryMkuPoolStatus, update.getCountryMkuPoolStatus())
                .set(CountryAgreementPriceWarningPO::getCustomerMkuPoolStatus, update.getCustomerMkuPoolStatus())

//                .set(CountryAgreementPriceWarningPO::getUpdateTime, new Date().getTime())
                .set(CountryAgreementPriceWarningPO::getUpdater, update.getUpdater())

                .eq(CountryAgreementPriceWarningPO::getId, update.getId())
                .eq(CountryAgreementPriceWarningPO::getYn, YnEnum.YES.getCode());

        boolean flag = super.update(updateWrapper);

        log.info("更新vip价预警可售状态. result=[{}], update={}", flag, JSONObject.toJSONString(update));

        if (!flag) {
            log.info("更新vip价预警可售状态失败, update={}", JSONObject.toJSONString(update));
            throw new ProductBizException("客制化价格预警正在进行其他业务操作，请稍后重试 %s", update.getSkuId());
        }
    }
}
