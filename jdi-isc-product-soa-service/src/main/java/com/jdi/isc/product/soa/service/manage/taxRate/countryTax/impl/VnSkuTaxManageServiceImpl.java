package com.jdi.isc.product.soa.service.manage.taxRate.countryTax.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.VnSkuTaxPO;
import com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum;
import com.jdi.isc.product.soa.price.api.price.req.VnSkuTaxVO;
import com.jdi.isc.product.soa.service.adapter.mapstruct.countryTax.VnSkuTaxConvert;
import com.jdi.isc.product.soa.service.atomic.taxRate.countryTax.VnSkuTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.VnSkuTaxManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 越南税率管理服务
 * <AUTHOR>
 * @date 2025/3/10
 */
@Slf4j
@Service
public class VnSkuTaxManageServiceImpl extends BaseTaxManageService implements VnSkuTaxManageService {

    @Resource
    private VnSkuTaxAtomicService vnSkuTaxAtomicService;

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> saveOrUpdate(VnSkuTaxVO input) {
        boolean flag;
        //将关税除以100保存
        input.setMfnTax(input.getMfnTax().divide(factor,5,RoundingMode.HALF_UP));
        input.setOriginMfnTax(input.getOriginMfnTax().divide(factor,5,RoundingMode.HALF_UP));
        input.setConsumptionTax(input.getConsumptionTax().divide(factor,5,RoundingMode.HALF_UP));
        input.setAntiDumpingTax(input.getAntiDumpingTax().divide(factor,5,RoundingMode.HALF_UP));
        input.setValueAddedTax(input.getValueAddedTax().divide(factor,5,RoundingMode.HALF_UP));
        JdProductDTO skuPO = getSku(input.getJdSkuId());
        if(skuPO==null){
            return DataResponse.error(String.format("零售skuId:%s 不存在,请检查",input.getJdSkuId()));
        }
        VnSkuTaxPO res = vnSkuTaxAtomicService.getOne(input);
        if(res==null){
            VnSkuTaxPO target = VnSkuTaxConvert.INSTANCE.vo2Po(input);
            target.setCreateTime(new Date());
            target.setUpdateTime(target.getCreateTime());
            flag = vnSkuTaxAtomicService.save(target);
            recordLog(target.getJdSkuId(), PriceTypeEnum.VN_TAX, target.getMfnTax(),target,flag);
        }else {
            input.setId(res.getId());
            flag = vnSkuTaxAtomicService.updateTax(input);
            recordLog(input.getJdSkuId(), PriceTypeEnum.VN_TAX, input.getMfnTax(),input,flag);
        }
        return DataResponse.success(flag);
    }

    public static void main(String[] args) {
        BigDecimal asd = new BigDecimal("20.578");
        System.out.println(asd.divide(factor,5, RoundingMode.HALF_UP));
    }


}
