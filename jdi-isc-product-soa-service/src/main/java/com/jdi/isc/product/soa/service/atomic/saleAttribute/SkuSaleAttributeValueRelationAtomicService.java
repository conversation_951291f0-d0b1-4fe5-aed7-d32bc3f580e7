package com.jdi.isc.product.soa.service.atomic.saleAttribute;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeValuePO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SkuSaleAttributeValueRelationPO;
import com.jdi.isc.product.soa.repository.mapper.saleAttribute.SkuSaleAttributeValueRelationBaseMapper;
import com.sun.org.apache.bcel.internal.generic.RETURN;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * SKU销售属性值关系表原子服务
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
public class SkuSaleAttributeValueRelationAtomicService extends ServiceImpl<SkuSaleAttributeValueRelationBaseMapper, SkuSaleAttributeValueRelationPO> {

    /**
     * 根据ID获取有效的关系记录
     * @param id 关系记录ID
     * @return 关系记录对象
     */
    public SkuSaleAttributeValueRelationPO getValidById(Long id) {
        if (id == null) {
            return null;
        }
        LambdaQueryWrapper<SkuSaleAttributeValueRelationPO> wrapper = Wrappers.<SkuSaleAttributeValueRelationPO>lambdaQuery()
                .eq(SkuSaleAttributeValueRelationPO::getId, id)
                .eq(SkuSaleAttributeValueRelationPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    /**
     * 根据SKUID查询有效的销售属性值关系列表
     * @param skuId SKUID
     * @return 销售属性值关系列表
     */
    public List<SkuSaleAttributeValueRelationPO> getValidBySkuId(Long skuId) {
        LambdaQueryWrapper<SkuSaleAttributeValueRelationPO> queryWrapper = new LambdaQueryWrapper<SkuSaleAttributeValueRelationPO>()
                .eq(SkuSaleAttributeValueRelationPO::getSkuId, skuId)
                .eq(SkuSaleAttributeValueRelationPO::getYn, YnEnum.YES.getCode());
        return super.list(queryWrapper);
    }

    /**
     * 根据skuKey查询有效的销售属性值关系列表（用于草稿阶段）
     * @param skuKey SKU唯一键
     * @return 销售属性值关系列表
     */
    public List<SkuSaleAttributeValueRelationPO> getValidBySkuKey(String skuKey) {
        if (StringUtils.isBlank(skuKey)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SkuSaleAttributeValueRelationPO> queryWrapper = new LambdaQueryWrapper<SkuSaleAttributeValueRelationPO>()
                .eq(SkuSaleAttributeValueRelationPO::getSkuKey, skuKey)
                .eq(SkuSaleAttributeValueRelationPO::getYn, YnEnum.YES.getCode());
        return super.list(queryWrapper);
    }

    /**
     * 根据销售属性值ID查询有效的关系列表
     * @param saleAttributeValueId 销售属性值ID
     * @return 销售属性值关系列表
     */
    public List<SkuSaleAttributeValueRelationPO> getValidBySaleAttributeValueId(Long saleAttributeValueId) {
        LambdaQueryWrapper<SkuSaleAttributeValueRelationPO> queryWrapper = new LambdaQueryWrapper<SkuSaleAttributeValueRelationPO>()
                .eq(SkuSaleAttributeValueRelationPO::getSaleAttributeValueId, saleAttributeValueId)
                .eq(SkuSaleAttributeValueRelationPO::getYn, YnEnum.YES.getCode());
        return super.list(queryWrapper);
    }

    /**
     * 根据SKU ID和销售属性值ID查询关系
     * @param skuId SKUID
     * @param saleAttributeValueId 销售属性值ID
     * @return 销售属性值关系
     */
    public SkuSaleAttributeValueRelationPO getValidBySkuIdAndSaleAttributeValueId(Long skuId, Long saleAttributeValueId) {
        LambdaQueryWrapper<SkuSaleAttributeValueRelationPO> queryWrapper = new LambdaQueryWrapper<SkuSaleAttributeValueRelationPO>()
                .eq(SkuSaleAttributeValueRelationPO::getSkuId, skuId)
                .eq(SkuSaleAttributeValueRelationPO::getSaleAttributeValueId, saleAttributeValueId)
                .eq(SkuSaleAttributeValueRelationPO::getYn, YnEnum.YES.getCode());
        return super.getOne(queryWrapper);
    }

    /**
     * 批量写入多个sku的销售属性关系
     * @param saleAttributeValueIdToSkuIdSkuKeyMap key: saleAttributeValueId, value: 该销售属性值的SKU ID和SKU Key的映射集合
     * @return 操作是否成功
     */
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public boolean batchSaveSkuAttributeValueRelationListForSkuId(Map<Long, Set<Map<Long, String>>> saleAttributeValueIdToSkuIdSkuKeyMap) {
        log.info("SkuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuId 开始批量处理SKU销售属性值关联关系, saleAttributeValueIdToSkuIdSkuKeyMap: {}", 
                JSON.toJSONString(saleAttributeValueIdToSkuIdSkuKeyMap));

        if (MapUtils.isEmpty(saleAttributeValueIdToSkuIdSkuKeyMap)) {
            log.warn("SkuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuId SKU销售属性值映射为空");
            return false;
        }

        try {
            List<SkuSaleAttributeValueRelationPO> allRelationPOsToSave = new ArrayList<>();
            for (Map.Entry<Long, Set<Map<Long, String>>> entry : saleAttributeValueIdToSkuIdSkuKeyMap.entrySet()) {
                Long saleAttributeValueId = entry.getKey();
                if (saleAttributeValueId == null) {
                    log.error("SkuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuId saleAttributeValueId为空");
                    continue;
                }
                Set<Map<Long, String>> skuIdSkuKeyMapSet = entry.getValue();
                if (CollectionUtils.isEmpty(skuIdSkuKeyMapSet)) {
                    log.error("SkuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuId skuIdSkuKeyMapSet为空");
                    continue;
                }
                for (Map<Long, String> skuIdSkuKeyMap : skuIdSkuKeyMapSet) {
                    if (MapUtils.isEmpty(skuIdSkuKeyMap)) {
                        log.error("SkuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuId skuIdSkuKeyMap为空");
                        continue;
                    }
                    Long skuId = skuIdSkuKeyMap.keySet().iterator().next();
                    String skuKey = skuIdSkuKeyMap.get(skuId);
                    if (skuId == null || StringUtils.isBlank(skuKey)) {
                        log.error("SkuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuId skuId: {} 或 skuKey: {} 为空", skuId, skuKey);
                        continue;
                    }
                    // 创建SKU和销售属性值的关联关系
                    SkuSaleAttributeValueRelationPO relationPO = getSkuSaleAttributeValueRelationPO(skuId, skuKey, saleAttributeValueId);
                    allRelationPOsToSave.add(relationPO);
                }
            }

            // 5. 批量保存新的关联关系
            if (!CollectionUtils.isEmpty(allRelationPOsToSave)) {
                return batchSave(allRelationPOsToSave);
            }
        } catch (Exception e) {
            log.error("SkuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuId 批量处理SKU销售属性值关联关系异常", e);
            throw e; // 重新抛出异常以触发事务回滚
        }
        return false;
    }

    /**
     * 批量写入多个sku的销售属性关系
     * @param saleAttributeValueIdToSkuKeySetMap key: saleAttributeValueId, value: 该销售属性值的SKU ID集合
     * @return 操作是否成功
     */
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public boolean batchSaveSkuAttributeValueRelationListForSkuKey(Map<Long, Set<String>> saleAttributeValueIdToSkuKeySetMap) {
        log.info("SkuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuKey 开始批量处理SKU销售属性值关联关系, SKU数量: {}", 
                saleAttributeValueIdToSkuKeySetMap == null ? 0 : saleAttributeValueIdToSkuKeySetMap.size());

        if (CollectionUtils.isEmpty(saleAttributeValueIdToSkuKeySetMap)) {
            log.warn("SkuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuKey SKU销售属性值映射为空");
            return false;
        }

        try {
            List<SkuSaleAttributeValueRelationPO> allRelationPOsToSave = new ArrayList<>();
            for (Map.Entry<Long, Set<String>> entry : saleAttributeValueIdToSkuKeySetMap.entrySet()) {
                Long saleAttributeValueId = entry.getKey();
                Set<String> skuKeySet = entry.getValue();
                for (String skuKey : skuKeySet) {
                    // 创建SKU和销售属性值的关联关系
                    SkuSaleAttributeValueRelationPO relationPO = getSkuSaleAttributeValueRelationPO(null, skuKey, saleAttributeValueId);
                    allRelationPOsToSave.add(relationPO);
                }
            }

            // 5. 批量保存新的关联关系
            if (!CollectionUtils.isEmpty(allRelationPOsToSave)) {
                return batchSave(allRelationPOsToSave);
            }
        } catch (Exception e) {
            log.error("SkuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuKey 批量处理SKU销售属性值关联关系异常", e);
            throw e; // 重新抛出异常以触发事务回滚
        }
        return false;
    }

    private boolean batchSave(List<SkuSaleAttributeValueRelationPO> allRelationPOsToSave) {
        boolean saveSuccess = this.saveBatch(allRelationPOsToSave);
        if (saveSuccess) {
            log.info("SkuSaleAttributeValueRelationAtomicService.batchSave 批量保存SKU销售属性值关联关系成功, 新增关联关系: {}",
                    JSON.toJSONString(allRelationPOsToSave));
            return true;
        } else {
            log.error("SkuSaleAttributeValueRelationAtomicService.batchSave 批量保存SKU销售属性值关联关系失败");
            return false;
        }
    }

    @NotNull
    private static SkuSaleAttributeValueRelationPO getSkuSaleAttributeValueRelationPO(Long skuId, String skuKey, Long saleAttributeValueId) {
        SkuSaleAttributeValueRelationPO relationPO = new SkuSaleAttributeValueRelationPO();
        if(skuId!=null){
            relationPO.setSkuId(skuId);
        }
        relationPO.setSkuKey(skuKey);
        relationPO.setSaleAttributeValueId(saleAttributeValueId);
        relationPO.setYn(YnEnum.YES.getCode());
        // 如果登录上下文为空，则设置创建者和更新者为系统
        if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
            relationPO.setCreator(Constant.PIN_SYSTEM);
            relationPO.setUpdater(Constant.PIN_SYSTEM);
        }
        long now = System.currentTimeMillis();
        relationPO.setCreateTime(now);
        relationPO.setUpdateTime(now);
        return relationPO;
    }

    /**
     * 批量根据SKU ID列表查询有效的销售属性值关系列表
     * @param skuIds SKU ID列表
     * @return 销售属性值关系列表
     */
    public List<SkuSaleAttributeValueRelationPO> getValidBySkuIds(List<Long> skuIds) {
        if (skuIds == null || skuIds.isEmpty()) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SkuSaleAttributeValueRelationPO> queryWrapper = new LambdaQueryWrapper<SkuSaleAttributeValueRelationPO>()
                .in(SkuSaleAttributeValueRelationPO::getSkuId, skuIds)
                .eq(SkuSaleAttributeValueRelationPO::getYn, YnEnum.YES.getCode());
        return super.list(queryWrapper);
    }

    /**
     * 根据销售属性值ID列表查询有效的销售属性值关系列表
     * @param saleAttributeValueIds 销售属性值ID列表
     * @return 销售属性值关系列表
     */
    public List<SkuSaleAttributeValueRelationPO> getValidBySaleAttributeValueIds(Set<Long> saleAttributeValueIds) {
        if (CollectionUtils.isEmpty(saleAttributeValueIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SkuSaleAttributeValueRelationPO> queryWrapper = new LambdaQueryWrapper<SkuSaleAttributeValueRelationPO>()
                .in(SkuSaleAttributeValueRelationPO::getSaleAttributeValueId, saleAttributeValueIds)
                .eq(SkuSaleAttributeValueRelationPO::getYn, YnEnum.YES.getCode());
        return super.list(queryWrapper);
    }

    /**
     * 根据SKU Key列表查询有效的销售属性值关系列表
     * @param skuKeys SKU Key列表
     * @return 销售属性值关系列表
     */
    public List<SkuSaleAttributeValueRelationPO> getValidBySkuKeys(List<String> skuKeys) {
        if (CollectionUtils.isEmpty(skuKeys)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SkuSaleAttributeValueRelationPO> queryWrapper = new LambdaQueryWrapper<SkuSaleAttributeValueRelationPO>()
                .in(SkuSaleAttributeValueRelationPO::getSkuKey, skuKeys)
                .eq(SkuSaleAttributeValueRelationPO::getYn, YnEnum.YES.getCode());
        return super.list(queryWrapper);
    }

    /**
     * 将草稿阶段的销售属性关系更新为正式关系（将skuKey转为skuId）
     * @param skuKeyToSkuIdMap SKU Key到SKU ID的映射
     * @return 操作是否成功
     */
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public boolean updateDraftRelationsToFormal(Map<String, Long> skuKeyToSkuIdMap) {
        log.info("SkuSaleAttributeValueRelationAtomicService.updateDraftRelationsToFormal 开始更新草稿关系为正式关系, SKU数量: {}", 
                skuKeyToSkuIdMap.size());
        
        if (MapUtils.isEmpty(skuKeyToSkuIdMap)) {
            log.warn("SkuSaleAttributeValueRelationAtomicService.updateDraftRelationsToFormal 入参为空");
            return true;  // 没有数据也算成功
        }

        try {
            Set<String> skuKeys = skuKeyToSkuIdMap.keySet();
            
            // 查询所有草稿阶段的关系数据
            LambdaQueryWrapper<SkuSaleAttributeValueRelationPO> queryWrapper = new LambdaQueryWrapper<SkuSaleAttributeValueRelationPO>()
                    .in(SkuSaleAttributeValueRelationPO::getSkuKey, skuKeys)
                    .eq(SkuSaleAttributeValueRelationPO::getYn, YnEnum.YES.getCode())
                    .isNull(SkuSaleAttributeValueRelationPO::getSkuId);  // 只处理skuId为null的草稿数据
            
            List<SkuSaleAttributeValueRelationPO> draftRelations = super.list(queryWrapper);
            
            if (CollectionUtils.isEmpty(draftRelations)) {
                log.info("SkuSaleAttributeValueRelationAtomicService.updateDraftRelationsToFormal 没有找到草稿关系数据");
                return true;
            }

            // 更新skuId
            for (SkuSaleAttributeValueRelationPO relation : draftRelations) {
                String skuKey = relation.getSkuKey();
                Long skuId = skuKeyToSkuIdMap.get(skuKey);
                if (skuId != null) {
                    relation.setSkuId(skuId);
                    relation.setUpdateTime(System.currentTimeMillis());
                    if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
                        relation.setUpdater(Constant.PIN_SYSTEM);
                    }
                }
            }

            // 批量更新
            boolean updateResult = super.updateBatchById(draftRelations);
            log.info("SkuSaleAttributeValueRelationAtomicService.updateDraftRelationsToFormal 草稿关系更新完成, 更新数量: {}, 结果: {}", 
                    draftRelations.size(), updateResult);
            
            return updateResult;

        } catch (Exception e) {
            log.error("SkuSaleAttributeValueRelationAtomicService.updateDraftRelationsToFormal 草稿关系更新异常", e);
            throw e; // 重新抛出异常以触发事务回滚
        }
    }
} 