package com.jdi.isc.product.soa.service.manage.tool;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.service.atomic.sku.SkuDraftAtomicService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;


@Slf4j
@Service
public class SkuDraftToolService {


    @Resource
    private SkuDraftAtomicService skuDraftAtomicService;

    /**
     * 批量更新商品草稿的京东SKU ID信息
     * @param env 环境参数，当前仅支持'uat_env'
     * @param skuIds 需要更新的商品ID集合
     * @return 执行结果字符串，成功返回'success'，参数错误时返回错误提示
     */
    public String updateSkuDraftJdSkuId(String env, Set<Long> skuIds) {
        if (StringUtils.isBlank(env) || !"uat_env".equals(env)) {
            return "环境参数不能为空，或者不正确";
        }


        List<SkuDraftPO> skuDraftPOList = skuDraftAtomicService.list(Wrappers.lambdaQuery(SkuDraftPO.class).select(SkuDraftPO::getId).eq(SkuDraftPO::getYn, YnEnum.YES.getCode()).isNull(SkuDraftPO::getJdSkuId));
        long total = skuDraftPOList.size();
        int current = 1;

        int updateCount = 0;

        for (SkuDraftPO skuDraftPO : skuDraftPOList) {
            log.info("updateSkuDraftJdSkuId 将草稿中的jdSkuId单独出来 开始执行，skuId={} jdSkuId={} count={},total={}",skuDraftPO.getSkuId(), skuDraftPO.getJdSkuId(),current, total );
            SkuDraftPO one = skuDraftAtomicService.getOne(Wrappers.lambdaQuery(SkuDraftPO.class).select(SkuDraftPO::getId,SkuDraftPO::getSkuJsonInfo,SkuDraftPO::getUpdater).eq(SkuDraftPO::getId, skuDraftPO.getId()));

            String skuJsonInfo = one.getSkuJsonInfo();
            if (StringUtils.isBlank(skuJsonInfo)) {
                current++;
                continue;
            }

            SkuVO skuVO = JSON.parseObject(skuJsonInfo, SkuVO.class);
            if (Objects.isNull(skuVO.getJdSkuId())) {
                current++;
                continue;
            }
            SkuDraftPO updateSkuDraftPO = new SkuDraftPO();
            updateSkuDraftPO.setId(skuDraftPO.getId());
            updateSkuDraftPO.setJdSkuId(skuVO.getJdSkuId());
            updateSkuDraftPO.setUpdater(one.getUpdater());
            updateSkuDraftPO.setUpdateTime(System.currentTimeMillis());
            skuDraftAtomicService.updateById(updateSkuDraftPO);
            updateCount++;
            log.info("updateSkuDraftJdSkuId 将草稿中的jdSkuId单独出来 结束，skuId={} jdSkuId={} count={},total={},updateCount={}",skuDraftPO.getSkuId(),skuVO.getJdSkuId(),current,total,updateCount);
            current++;
        }
        return "success";
    }

    public static void main(String[] args) {
        long count = 10001;
        long page = count / 100;
        System.out.println(page);
    }
}
