package com.jdi.isc.product.soa.service.manage.taxRate.countryTax.impl.read;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.CurrencyConstant;
import com.jdi.isc.product.soa.domain.price.extendPrice.biz.CurrencyPriceVO;
import com.jdi.isc.product.soa.domain.price.po.FulfillmentRatePO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxResVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.IscSkuImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.ThSkuTaxPO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.VnSkuTaxPO;
import com.jdi.isc.product.soa.price.api.price.req.CrossBorderImportTaxReqDTO;
import com.jdi.isc.product.soa.price.api.price.res.CrossBorderImportTaxResDTO;
import com.jdi.isc.product.soa.service.atomic.taxRate.countryTax.VnSkuTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.BaseTaxReadService;
import com.jdi.isc.product.soa.service.support.TaxCalculateExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 越南关税读服务
 * <AUTHOR>
 * @date 2025/3/10
 */
@Slf4j
@Service
@TaxCalculateExecutor(countryCode = CountryConstant.COUNTRY_VN)
public class VnCountryTaxReadServiceImpl extends BaseTaxSupportService implements BaseTaxReadService {
    @Resource
    private VnSkuTaxAtomicService vnSkuTaxAtomicService;

    @Override
    public Map<Long, CrossBorderImportTaxResDTO> queryCrossBorderSkuImportTax(CrossBorderImportTaxReqVO input) {
        Map<Long, CrossBorderImportTaxResDTO> result = Maps.newHashMapWithExpectedSize(input.getCnSkuIds().size());
        //获取跨境采购价
        Map<Long, SkuPricePO> priceMap = skuPriceAtomicService.batchQuerySkuTaxPrice(input.getCnSkuIds(), CountryConstant.COUNTRY_ZH);
        //获取越南关税率
        Map<Long, VnSkuTaxPO> skuTaxMap = getSkuTaxMap(input.getCnSkuIds());
        for(Long skuId : input.getCnSkuIds()){
            CrossBorderImportTaxResDTO target = new CrossBorderImportTaxResDTO(skuId,input.getCountryCode(),CurrencyConstant.CURRENCY_ZH);
            StringBuilder msg = new StringBuilder();
            if(priceMap.get(skuId)==null){
                msg.append("skuId:").append(skuId).append(" 采购价为空,无法计算关税;");
                target.setDesc(msg.toString());
                result.put(skuId,target);
                continue;
            }
            if(skuTaxMap.get(skuId)==null){
                msg.append("skuId:").append(skuId).append(" 关税配置为空,无法计算关税;");
                target.setDesc(msg.toString());
                result.put(skuId,target);
                continue;
            }
            VnSkuTaxPO taxInfo = skuTaxMap.get(skuId);
            //履约费用
            CurrencyPriceVO fulfillmentPrice;
            if(!input.getFulfillmentFee().isEmpty() && input.getFulfillmentFee().get(skuId)!=null){
                fulfillmentPrice = input.getFulfillmentFee().get(skuId);
            }else {
                fulfillmentPrice = getFulfillmentPrice(input.getCountryCode(), skuId);
            }
            if(fulfillmentPrice==null || fulfillmentPrice.getPrice()==null){
                target.setDesc(fulfillmentPrice!=null&& StringUtils.isNotBlank(fulfillmentPrice.getMsg())?fulfillmentPrice.getMsg():"履约费用为空,无法计算关税;");
                result.put(skuId,target);
                continue;
            }
            //CIF价格=采购价（含税）+ 履约费用
            BigDecimal cif = priceMap.get(skuId).getPrice().add(fulfillmentPrice.getPrice());
            //进口关税金= CIF价格 x min（越南最惠国关税率%    越南原产地优惠关税率%）
            BigDecimal importDuties = cif.multiply(taxInfo.getOriginMfnTax().min(taxInfo.getMfnTax()));
            //消费税金 = （CIF价格+进口关税金）x 越南消费税率%；
            BigDecimal consumption = cif.add(importDuties).multiply(taxInfo.getConsumptionTax());
            //反倾销税金 =  CIF价格 x 反倾销税率%；
            BigDecimal antiDumping = cif.multiply(taxInfo.getAntiDumpingTax());
            //增值税金 =（ CIF价格+进口关税金+消费税金+环保税金+反倾销税金）x 增值税税率%；
            BigDecimal valueAdd = (cif.add(importDuties).add(consumption).add(taxInfo.getEnvironmentalTaxUnitPrice()).add(antiDumping)).multiply(taxInfo.getValueAddedTax());
            //进口税费=进口关税金+消费税金+环保税金+反倾销税金 +增值税金  (中间值不舍位，最终结果四舍五入)
            BigDecimal importTax = (importDuties.add(consumption).add(taxInfo.getEnvironmentalTaxUnitPrice()).add(antiDumping).add(valueAdd)).setScale(2, RoundingMode.HALF_UP);

            //优化小数位展示
            String cifStr = cif.stripTrailingZeros().toPlainString();
            String importDutiesStr = importDuties.stripTrailingZeros().toPlainString();
            String consumptionStr = consumption.stripTrailingZeros().toPlainString();
            String antiDumpingStr = antiDumping.stripTrailingZeros().toPlainString();
            String valueAddStr = valueAdd.stripTrailingZeros().toPlainString();
            String importTaxStr = importTax.stripTrailingZeros().toPlainString();

            //计算过程消息
            String totalMsg = String.format("进口税费%s=进口关税金%s+消费税金%s+环保税金%s+反倾销税金%s+增值税金%s ",importTaxStr,importDutiesStr,consumptionStr,taxInfo.getEnvironmentalTaxUnitPrice().stripTrailingZeros().toPlainString(),antiDumpingStr,valueAddStr);
            String cifMsg = String.format("CIF价格%s=采购价（含税）%s+ 履约费用%s",cif.stripTrailingZeros().toPlainString(),priceMap.get(skuId).getPrice(),fulfillmentPrice.getPrice());
            String importDutiesMsg = String.format("进口关税金%s= CIF价格%s x min（越南最惠国关税率%s,越南原产地优惠关税率%s）",importDutiesStr,cifStr,taxInfo.getMfnTax(),taxInfo.getOriginMfnTax().stripTrailingZeros().toPlainString());
            String consumptionMsg = String.format("消费税金%s=（CIF价格%s + 进口关税金%s）x 越南消费税率%s",consumptionStr,cifStr,importDutiesStr,taxInfo.getConsumptionTax().stripTrailingZeros().toPlainString());
            String antiDumpingMsg = String.format("反倾销税金%s = CIF价格%s x 反倾销税率%s",antiDumpingStr,cifStr,taxInfo.getAntiDumpingTax().stripTrailingZeros().toPlainString());
            String valueAddMsg = String.format("增值税金%s =（ CIF价格%s+进口关税金%s+消费税金%s+环保税金%s+反倾销税金%s）x 增值税税率%s",valueAddStr,cifStr,importDutiesStr,consumptionStr,taxInfo.getEnvironmentalTaxUnitPrice().stripTrailingZeros().toPlainString(),antiDumpingStr,taxInfo.getValueAddedTax().stripTrailingZeros().toPlainString());
            target.setDesc(msg.append(totalMsg).append("\n").
                    append(importDutiesMsg).append("\n").
                    append(cifMsg).append("\n").
                    append(consumptionMsg).append("\n").
                    append(antiDumpingMsg).append("\n").
                    append(valueAddMsg).toString());
            target.setImportTax(importTax);
            target.setDeductionAmount(valueAdd.setScale(2, RoundingMode.HALF_UP));
            log.info("ThCountryTaxManageServiceImpl.queryCrossBorderSkuImportTax skuId:{} , res:{}" , skuId , JSON.toJSONString(target));
            result.put(skuId,target);
        }
        return result;
    }

    private Map<Long, VnSkuTaxPO> getSkuTaxMap(Set<Long> cnSkuIds) {
        Map<Long, VnSkuTaxPO> result = Maps.newHashMapWithExpectedSize(cnSkuIds.size());
        //国际sku批量转jdSku
        Map<Long, Long> res = skuAtomicService.querySkuMapByIscSkuIds(cnSkuIds);
        if(res.isEmpty()){
            log.error("BrCountryTaxReadServiceImpl.getSkuTaxMap invoke error, 给定的国际sku未能查询到正确的sku与jdSku映射关系 {}", cnSkuIds);
            return new HashMap<>();
        }
        //根据jdSku查关税率
        Map<Long, VnSkuTaxPO> skuTaxMap = vnSkuTaxAtomicService.listSkuTax(new HashSet<>(res.values()));
        if(skuTaxMap.isEmpty()){
            log.error("BrCountryTaxReadServiceImpl.getSkuTaxMap invoke error, 给定的jdSku未能查询到关税配置 res {}", JSON.toJSONString(res));
            return new HashMap<>();
        }
        //构建国际sku&关税实体关系
        for (Map.Entry<Long, Long> entry : res.entrySet()) {
            result.put(entry.getKey(), skuTaxMap.get(entry.getValue()));
        }
        return result;
    }

    @Override
    public Map<Long, CrossBorderImportTaxResVO> queryImportTaxByIscSkuIds(IscSkuImportTaxReqVO input) {
        Map<Long, VnSkuTaxPO> skuTaxMap = this.getSkuTaxMap(input.getSkuIds());
        // 构建返回结果
        Map<Long, CrossBorderImportTaxResVO> resultMap = Maps.newHashMapWithExpectedSize(skuTaxMap.size());
        skuTaxMap.forEach((skuId,taxPo)-> resultMap.put(skuId, new CrossBorderImportTaxResVO(skuId,taxPo.getJdSkuId(), taxPo.getHsCode())));
        return resultMap;
    }
}
