package com.jdi.isc.product.soa.service.atomic.mku;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuPageReqVO;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.repository.mapper.mku.MkuBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【jdi_isc_mku_sharding(mku表)】的数据库操作Service实现
 * @createDate 2023-11-26 17:24:53
 */
@Slf4j
@Service
public class MkuAtomicService extends ServiceImpl<MkuBaseMapper, MkuPO> {

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @PFTracing
    public MkuPO getPOById(Long mkuId){
        LambdaQueryWrapper<MkuPO> wrapper = Wrappers.<MkuPO>lambdaQuery()
                .eq(MkuPO::getMkuId, mkuId).eq(MkuPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    /** 批量查询mku*/
    public Map<Long,MkuPO> getByMkuIds(Set<Long> mkuIds){
        LambdaQueryWrapper<MkuPO> wrapper = Wrappers.<MkuPO>lambdaQuery().in(MkuPO::getMkuId, mkuIds).eq(MkuPO::getYn, YnEnum.YES.getCode());
        List<MkuPO> list = super.list(wrapper);
        if(CollectionUtils.isNotEmpty(list)){
            return list.stream().collect(Collectors.toMap(MkuPO::getMkuId, Function.identity()));
        }
        return new HashMap<>();
    }

    public List<MkuPO> listByMkuIds(List<Long> mkuIds){
        LambdaQueryWrapper<MkuPO> wrapper = Wrappers.<MkuPO>lambdaQuery()
                .in(MkuPO::getMkuId, mkuIds).eq(MkuPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    public List<MkuPO> listMkusByMkuIds(List<Long> mkuIds) {
        if (CollectionUtils.isEmpty(mkuIds)) {
            return Lists.newArrayList();
        }

        List<MkuPO> result = Lists.newArrayList();

        for (List<Long> ids : Lists.partition(mkuIds, 100)) {
            LambdaQueryWrapper<MkuPO> query = new LambdaQueryWrapper<MkuPO>().select(MkuPO::getId, MkuPO::getBuyer, MkuPO::getMkuId)
                    .in(MkuPO::getMkuId, ids)
                    .eq(MkuPO::getYn, YnEnum.YES.getCode());
            List<MkuPO> list = super.getBaseMapper().selectList(query);
            if (CollectionUtils.isNotEmpty(list)) {
                result.addAll(list);
            }
        }

        return result;
    }

    public MkuPO getById(Long mkuId){
        LambdaQueryWrapper<MkuPO> wrapper = Wrappers.<MkuPO>lambdaQuery()
                .eq(MkuPO::getMkuId, mkuId);
        return super.getOne(wrapper);
    }

    public Page<MkuPO> listSearchByMkuReqVO(MkuPageReqVO mkuPageReqVO){
        try {
            Page<MkuPO> page = new Page<>(mkuPageReqVO.getIndex(), mkuPageReqVO.getSize());
            page.setTotal(super.getBaseMapper().countSearchByMkuReqVO(mkuPageReqVO));
            page.setRecords(super.getBaseMapper().listSearchByMkuReqVO(mkuPageReqVO));
            return page;
        } catch (Exception e) {
            log.error("【系统异常】MkuRelationManageServiceImpl.page", e);
        }
        return new Page<>();
    }

    public boolean updateMkuPO(MkuPO mkuPO){
        LambdaUpdateWrapper<MkuPO> wrapper = Wrappers.<MkuPO>lambdaUpdate()
                .eq(MkuPO::getMkuId, mkuPO.getMkuId());
        mkuPO.setMkuId(null);
        return super.update(mkuPO, wrapper);
    }

    /**
     * 根据时间范围和是否有效统计MkuPO记录数量。
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @return MkuPO记录数量
     */
    public Long countByTime( Date beginDate, Date endDate){
        LambdaQueryWrapper<MkuPO> wrapper = Wrappers.<MkuPO>lambdaQuery()
            .gt(MkuPO::getUpdateTime, beginDate)
            .lt(MkuPO::getUpdateTime, endDate)
            .eq(MkuPO::getYn, YnEnum.YES.getCode());
        return super.count(wrapper);
    }

    /**
     * 根据更新时间范围和有效标志查询 MkuPO 列表。
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @return 满足条件的 MkuPO 对象列表
     */
    public List<MkuPO> listByTime(Date beginDate, Date endDate){
        LambdaQueryWrapper<MkuPO> wrapper = Wrappers.<MkuPO>lambdaQuery()
            .gt(MkuPO::getUpdateTime, beginDate)
            .lt(MkuPO::getUpdateTime, endDate)
            .eq(MkuPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }


    public List<MkuPO> listById(Long beginId, Long  endId){
        LambdaQueryWrapper<MkuPO> wrapper = Wrappers.<MkuPO>lambdaQuery()
            .gt(MkuPO::getMkuId, beginId)
            .lt(MkuPO::getMkuId, endId)
            .eq(MkuPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }


    public Long countById(Long beginId, Long endId){
        LambdaQueryWrapper<MkuPO> wrapper = Wrappers.<MkuPO>lambdaQuery()
            .gt(MkuPO::getMkuId, beginId)
            .lt(MkuPO::getMkuId, endId)
            .eq(MkuPO::getYn, YnEnum.YES.getCode());
        return super.count(wrapper);
    }

    /**
     * 根据品牌ID列表统计符合条件的MkuPO数量
     * @param brandIds 品牌ID列表
     * @return 符合条件的MkuPO数量
     */
    public Long countByBrandId(List<Long> brandIds){
        LambdaQueryWrapper<MkuPO> wrapper = Wrappers.<MkuPO>lambdaQuery()
            .in(MkuPO::getBrandId,brandIds)
            .eq(MkuPO::getYn, YnEnum.YES.getCode());
        return super.count(wrapper);
    }

    /**
     * 根据品牌ID列表查询所有有效的MkuPO对象。
     * @param brandIds 品牌ID列表
     * @return 符合条件的MkuPO对象列表
     */
    public List<MkuPO> listByBrandId(List<Long> brandIds){
        LambdaQueryWrapper<MkuPO> wrapper = Wrappers.<MkuPO>lambdaQuery()
            .in(MkuPO::getBrandId,brandIds)
            .eq(MkuPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    /**
     * 分页获取全部MkuPO数据（仅获取有效数据且有扩展属性但没有新格式扩展属性的记录）
     *
     * @param index 页码，从1开始
     * @param size 每页大小
     * @return MkuPO列表
     */
    public List<MkuPO> listAllMkuWithPage(String updateTime, Long index, Long size) {
        // 参数校验
        if (index == null || index < 1) {
            throw new IllegalArgumentException("页码必须大于等于1");
        }
        if (size == null || size < 1 || size > 1000) {
            throw new IllegalArgumentException("页大小必须在1-1000之间");
        }

        // 计算偏移量
        Long offset = (index - 1) * size;

        return super.getBaseMapper().listAllMkuWithPage(updateTime, offset, size);
    }

    /**
     * 获取全部有效MkuPO数据的总数（仅有扩展属性但没有新格式扩展属性的记录）
     *
     * @return 总记录数
     */
    public long listAllMkuTotal(String updateTime) {
        return super.getBaseMapper().listAllMkuTotal(updateTime);
    }

    public List<MkuPO> listBySourceCountryCodeAndJdCatId(String sourceCountryCode, Long jdCatId) {

        if (StringUtils.isEmpty(sourceCountryCode) || jdCatId == null) {
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<MkuPO> wrapper = Wrappers.<MkuPO>lambdaQuery()
                .select(MkuPO::getMkuId, MkuPO::getBuyer, MkuPO::getUpdateTime)
                .eq(MkuPO::getSourceCountryCode, sourceCountryCode)
                .eq(MkuPO::getJdCatId, jdCatId)
                .eq(MkuPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    public void updateBuyer(List<MkuPO> mkus, String buyer, String updater) {
        if (CollectionUtils.isEmpty(mkus)) {
            return;
        }
        // 更新
        for (MkuPO mku : mkus) {
            log.info("更新mku的采销信息. mkuId={}, buyer={}, updater={}", mku.getMkuId(), buyer, updater);

            if (StringUtils.equals(mku.getBuyer(), buyer)) {
                log.info("更新mku的采销信息. buyer 相同无需更新, mkuId={}, buyer={}, updater={}", mku.getMkuId(), buyer, updater);
                continue;
            }

            LambdaUpdateWrapper<MkuPO> updateWrapper = Wrappers.lambdaUpdate(MkuPO.class)
                    .set(MkuPO::getBuyer, buyer)
                    .set(MkuPO::getUpdateTime, new Date())
                    .set(MkuPO::getUpdater, StringUtils.isNotBlank(updater) ? updater : Constant.SYSTEM)
                    .eq(MkuPO::getMkuId, mku.getMkuId())
                    .eq(MkuPO::getYn, YnEnum.YES.getCode())
                    .eq(MkuPO::getUpdateTime, mku.getUpdateTime());
            boolean update = super.update(updateWrapper);

            log.info("更新mku的采销信息. result=[{}] mkuId={}, buyer={}, updater={}", update, mku.getMkuId(), buyer, updater);

            if (!update) {
                log.info("更新mku的采销信息失败. result=[{}] mkuId={}, buyer={}, updater={}", false, mku.getMkuId(), buyer, updater);
                throw new ProductBizException("mku正在进行其他业务操作，请稍后重试 %s", mku.getMkuId());
            }
        }
    }

    /**
     * 更新商品的MKU信息，包括更新人和更新时间，并触发国家池的状态变更。
     * @param skuId SKU的ID
     * @param updater 更新人
     * @param date 更新时间
     * @return 是否成功更新MKU信息
     */
    public boolean updateMkuBySkuId(Long skuId,String updater, Date date) {
        // 更新mku时间和更新人，通过binlake触发国家池的状态变更
        MkuRelationPO mkuRelationPo = mkuRelationAtomicService.getMkuBySkuId(skuId);
        if (Objects.nonNull(mkuRelationPo)) {
            LambdaUpdateWrapper<MkuPO> updateMkuWrapper = Wrappers.lambdaUpdate(MkuPO.class)
                    .set(MkuPO::getUpdater, updater)
                    .set(MkuPO::getUpdateTime, date)
                    .eq(MkuPO::getMkuId, mkuRelationPo.getMkuId());
            return super.update(updateMkuWrapper);
        }
        return Boolean.TRUE;
    }
}




