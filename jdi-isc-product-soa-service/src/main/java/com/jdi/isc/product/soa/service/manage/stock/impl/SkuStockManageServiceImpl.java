package com.jdi.isc.product.soa.service.manage.stock.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.ka.gpt.soa.client.goods.request.AreaBaseInfoGoodsReq;
import com.jd.ka.gpt.soa.client.goods.request.GetStockByIdGoodsReq;
import com.jd.ka.gpt.soa.client.goods.request.SkuNumBaseGoodsReq;
import com.jd.ka.gpt.soa.client.goods.resp.GetStockByIdGoodsResp;
import com.jd.stock.state.export.vo.param.AreaParam;
import com.jd.stock.state.export.vo.param.AreaStockStateGlobalParam;
import com.jd.stock.state.export.vo.param.SkuNumParam;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.ofc.biz.StoreHouseConsigneeInfoDTO;
import com.jdi.isc.order.center.api.ofc.biz.rsp.CustomerSkuDecisionResult;
import com.jdi.isc.order.center.api.ofc.biz.rsp.SkuInfoResDTO;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.PurchaseModelTypeEnum;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.util.StockNumUtils;
import com.jdi.isc.product.soa.domain.enums.SkuScopeEnum;
import com.jdi.isc.product.soa.domain.enums.warehouse.OnWaySaleTypeEnum;
import com.jdi.isc.product.soa.domain.sku.biz.ExternalVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuExternalReqVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangVO;
import com.jdi.isc.product.soa.domain.stock.po.StockPO;
import com.jdi.isc.product.soa.domain.stock.vo.*;
import com.jdi.isc.product.soa.domain.stockThreshold.biz.SkuStockThresholdDataVO;
import com.jdi.isc.product.soa.domain.stockThreshold.biz.SkuStockThresholdVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.QuerySkuRelationReqVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseBatchGetReqVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseResVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseSkuVO;
import com.jdi.isc.product.soa.rpc.gms.AreaStockStateRpcService;
import com.jdi.isc.product.soa.rpc.iop.SkuStockRpcService;
import com.jdi.isc.product.soa.rpc.order.FulfillmentPolicyRpcService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.stock.StockAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseAtomicService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuExternalManageService;
import com.jdi.isc.product.soa.service.manage.stock.SkuStockManageService;
import com.jdi.isc.product.soa.service.manage.stock.StockManageService;
import com.jdi.isc.product.soa.service.manage.stockThreshold.SkuStockThresholdManageService;
import com.jdi.isc.product.soa.service.manage.warehouse.WarehouseManageService;
import com.jdi.isc.product.soa.service.manage.warehouse.WarehouseSkuManageService;
import com.jdi.isc.product.soa.service.mapstruct.stock.SkuStockConvert;
import com.jdi.isc.product.soa.stock.sku.req.StockItemReqDTO;
import com.jdi.isc.product.soa.stock.sku.req.StockReadReqDTO;
import com.jdi.isc.product.soa.stock.sku.res.SkuStockPageResDTO;
import com.jdi.isc.product.soa.stock.sku.res.StockReadResDTO;
import com.jdi.isc.product.soa.stock.threshold.biz.req.BatchQueryStockThresholdDTO;
import com.jdi.isc.product.soa.stock.threshold.biz.req.SkuStockThresholdReqDTO;
import com.jdi.isc.product.soa.stock.threshold.biz.res.SkuStockThresholdResDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * sku库存业务服务
 *
 * <AUTHOR>
 * @date 20231130
 */
@Slf4j
@Service
public class SkuStockManageServiceImpl extends BaseManageSupportService<SkuStockVO, StockPO> implements SkuStockManageService {

    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private StockAtomicService stockAtomicService;
    @Resource
    private SkuExternalManageService skuExternalManageService;
    @Resource
    private StockManageService stockManageService;
    @Resource
    private WarehouseAtomicService warehouseAtomicService;
    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private FulfillmentPolicyRpcService fulfillmentPolicyRpcService;
    @Resource
    private SkuStockRpcService skuStockRpcService;
    @Resource
    private AreaStockStateRpcService areaStockStateRpcService;

    @Resource
    private WarehouseManageService warehouseManageService;

    @Resource
    private SkuStockThresholdManageService skuStockThresholdManageService;

    @Resource
    @Lazy
    private WarehouseSkuManageService warehouseSkuManageService;


    //默认中国国家地址id
    private final static int DEFAULT_STATE_ID = 156;

    @Override
    @ToolKit(umpFlag = false)
    public Map<Long, SkuStockVO> list(SkuStockReqVO skuStockReqVO) {
        if (CollectionUtils.isEmpty(skuStockReqVO.getSkuIds())) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<StockPO> wrapper = Wrappers.<StockPO>lambdaQuery()
                .in(StockPO::getSkuId, skuStockReqVO.getSkuIds());
        List<StockPO> dbRecord = stockAtomicService.list(wrapper);
        List<SkuStockVO> res = SkuStockConvert.INSTANCE.listPo2Vo(dbRecord);
        // 补充仓库存信息
        this.fillStockFulfillmentTag(skuStockReqVO, res);
        return Optional.ofNullable(res).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(SkuStockVO::getSkuId, Function.identity()));
    }

    @Override
    @ToolKit(umpFlag = false)
    public PageInfo<SkuStockVO> page(SkuStockReqVO skuStockReqVO) {
        PageInfo<SkuStockVO> list = stockAtomicService.page(skuStockReqVO);
        if (CollectionUtils.isNotEmpty(list.getRecords())) {
            SkuExternalReqVO input = new SkuExternalReqVO(list.getRecords().stream().map(SkuStockVO::getSkuId).collect(Collectors.toList()), Sets.newHashSet(SkuScopeEnum.BASE, SkuScopeEnum.LANG));
            Map<Long, ExternalVO> externalVoMap = skuExternalManageService.querySkuInfo(input);
            log.info("SkuStockManageServiceImpl.page.querySkuInfo req:{} , res:{}", JSON.toJSONString(input), JSON.toJSONString(externalVoMap));
            list.getRecords().forEach(stock -> {
                ExternalVO po = externalVoMap.get(stock.getSkuId());
                stock.setVendorCode(po.getSpuVO().getVendorCode());
                SpuLangVO lang = po.getLangVOList().stream().filter(line -> LangConstant.LANG_ZH.equals(line.getLang())).findFirst().orElse(null);
                stock.setSkuName(lang != null ? lang.getSpuTitle() : null);
                stock.setBuyer(po.getSpuVO().getBuyer());
            });
        }
        return list;
    }

    @Override
    public PageInfo<SkuStockPageResDTO> pageQuery(SkuStockReqVO skuStockReqVO) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        PageInfo<SkuStockVO> list = stockAtomicService.pageQuery(skuStockReqVO);
        long pageQueryTime = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("Page query time: {} ms", pageQueryTime);

        if (CollectionUtils.isNotEmpty(list.getRecords())) {
            List<SkuStockVO> skuStockVOList = list.getRecords();

            //设置商品扩展信息字段
            Stopwatch fillExtraInfoStopwatch = Stopwatch.createStarted();
            this.fillStockExtraInfo(skuStockReqVO, skuStockVOList);
            fillExtraInfoStopwatch.stop();
            log.info("Fill stock extra info time: {} ms", fillExtraInfoStopwatch.elapsed(TimeUnit.MILLISECONDS));

            //设置安全库存阈值、库存预警通知范围等信息
            Stopwatch fillThresholdStopwatch = Stopwatch.createStarted();
            this.fillStockThreshold(skuStockVOList);
            fillThresholdStopwatch.stop();
            log.info("Fill stock threshold time: {} ms", fillThresholdStopwatch.elapsed(TimeUnit.MILLISECONDS));

            //设置商品履约标识、在途可售、寄售等标识
            Stopwatch fillFulfillmentTagStopwatch = Stopwatch.createStarted();
            this.fillStockFulfillmentTag(skuStockReqVO, skuStockVOList);
            fillFulfillmentTagStopwatch.stop();
            log.info("Fill stock fulfillment tag time: {} ms", fillFulfillmentTagStopwatch.elapsed(TimeUnit.MILLISECONDS));

            //过滤不匹配的无效数据
            skuStockVOList = skuStockVOList.stream().filter(SkuStockVO::getValidStatus).collect(Collectors.toList());
            List<SkuStockPageResDTO> skuStockPageResDTOList = SkuStockConvert.INSTANCE.skuStockReqToSkuStockPageResDTO(skuStockVOList);

            log.info("SkuStockManageServiceImpl.pageQuery req:{} , res:{}", JSON.toJSONString(skuStockReqVO), JSON.toJSONString(skuStockPageResDTOList));
            return pageTransformStockPage(list, skuStockPageResDTOList);
        }

        return pageTransformStockPage(list, null);
    }

    /**
     * 填充库存额外信息，包括商品供应商、名称、采销员以及仓库名称和编号
     *
     * @param skuStockVOList 需要填充额外信息的库存VO列表
     */
    private void fillStockExtraInfo(SkuStockReqVO reqVO, List<SkuStockVO> skuStockVOList) {
        Set<String> warehouseIds = Sets.newHashSet();
        List<Long> skuIds = com.google.common.collect.Lists.newArrayList();
        skuStockVOList.forEach(skuStockVO -> {
            if (Objects.nonNull(skuStockVO.getWarehouseId())) {
                warehouseIds.add(skuStockVO.getWarehouseId());
            }
            skuIds.add(skuStockVO.getSkuId());
        });

        SkuExternalReqVO input = new SkuExternalReqVO(skuIds, Sets.newHashSet(SkuScopeEnum.BASE, SkuScopeEnum.LANG));
        Map<Long, ExternalVO> externalVoMap = skuExternalManageService.querySkuInfo(input);
        log.info("SkuStockManageServiceImpl.page.querySkuInfo req:{} , res:{}", JSON.toJSONString(input), JSON.toJSONString(externalVoMap));

        Map<String, WarehouseResVO> warehouseMap = queryWarehouseMap(warehouseIds);
        log.info("SkuStockManageServiceImpl.page.queryWarehouseNameMap req:{} , warehouseNameMap:{}", JSON.toJSONString(warehouseIds), JSON.toJSONString(warehouseMap));

        for (SkuStockVO stock : skuStockVOList) {
            ExternalVO po = externalVoMap.get(stock.getSkuId());
            if (Objects.nonNull(po) && Objects.nonNull(po.getSpuVO())) {
                //供应商编号
                stock.setVendorCode(po.getSpuVO().getVendorCode());
                SpuLangVO lang = Optional.ofNullable(po.getLangVOList()).orElseGet(ArrayList::new).stream()
                        .filter(line -> LangConstant.LANG_ZH.equals(line.getLang())).findFirst().orElse(null);
                //商品名称、采销员erp
                stock.setSkuName(lang != null ? lang.getSpuTitle() : null);
                //设置商品名称多语言
                Map<String, String> skuNameMap = Optional.ofNullable(po.getLangVOList())
                        .orElse(Collections.emptyList())
                        .stream()
                        .collect(Collectors.toMap(SpuLangVO::getLang, SpuLangVO::getSpuTitle));
                stock.setSkuNameMap(skuNameMap);

                stock.setBuyer(po.getSpuVO().getBuyer());

                //供应商编码有效性校验(仅sku搜索场景)
                if (StringUtils.isNotBlank(reqVO.getVendorCode()) && !Objects.equals(reqVO.getVendorCode(), stock.getVendorCode())) {
                    stock.setValidStatus(false);
                    log.warn("SkuStockManageServiceImpl.fillStockExtraInfo filter invalid data for vendorCode, stock:{}", JSON.toJSONString(stock));
                }
            }
            // 仓库名称、仓库编号补充
            if (warehouseMap.containsKey(stock.getWarehouseId())) {
                WarehouseResVO warehouseResVO = warehouseMap.get(stock.getWarehouseId());
                Map<String, String> warehouseNameMap = warehouseResVO.getWarehouseNameMap();

                stock.setWarehouseName(warehouseNameMap.get(LangConstant.LANG_ZH));
                stock.setWarehouseNameMap(warehouseNameMap);
                stock.setWarehouseNo(warehouseResVO.getWarehouseNo());
            }
        }
    }

    /**
     * 将分页的SkuStockVO对象转换为SkuStockPageResDTO分页对象
     *
     * @param target 源分页对象，包含分页信息和SkuStockVO记录集合
     * @param item   需要设置到结果分页对象中的SkuStockPageResDTO记录集合
     * @return 转换后的分页对象，包含原始分页参数和新的记录集合
     */
    public PageInfo<SkuStockPageResDTO> pageTransformStockPage(PageInfo<SkuStockVO> target, List<SkuStockPageResDTO> item) {
        PageInfo<SkuStockPageResDTO> output = new PageInfo<>();
        output.setSize(target.getSize());
        output.setIndex(target.getIndex());
        if (CollectionUtils.isNotEmpty(target.getRecords()) && CollectionUtils.isNotEmpty(item)) {
            output.setTotal(target.getTotal());
            output.setRecords(item);
        }
        return output;
    }


    /**
     * 为SKU库存记录填充在途可售标签、可售库存和采购模式信息
     *
     * @param records SKU库存记录列表，不可为null但可以为空列表
     */
    private void fillStockFulfillmentTag(SkuStockReqVO reqVO, List<SkuStockVO> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        Set<String> skuIdSet = records.stream()
                .map(SkuStockVO::getSkuId)
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .collect(Collectors.toSet());

        if (skuIdSet.isEmpty()) {
            log.error("SkuStockManageService.fillStockFulfillmentTag skuIdSet is empty req:{}", JSON.toJSONString(records));
            return;
        }

        //查询仓绑定关系
        Map<String, Map<String, WarehouseSkuVO>> warehouseSkuRelation = warehouseSkuManageService.querySkuWarehouseRelationMap(new QuerySkuRelationReqVO(skuIdSet));

        for (SkuStockVO record : records) {
            String warehouseId = record.getWarehouseId();
            if (StringUtils.isNotEmpty(warehouseId)) {
                if (org.springframework.util.CollectionUtils.isEmpty(warehouseSkuRelation)) {
                    log.error("SkuStockManageService.fillStockFulfillmentTag warehouseSkuRelation is empty req:{}", JSON.toJSONString(skuIdSet));
                    continue;
                }

                Map<String, WarehouseSkuVO> warehouseMap = warehouseSkuRelation.get(String.valueOf(record.getSkuId()));
                if (warehouseMap != null) {
                    WarehouseSkuVO warehouseSkuDTO = warehouseMap.get(warehouseId);
                    if (warehouseSkuDTO != null) {
                        //设置在途可售标识
                        record.setOnWaySale(warehouseSkuDTO.getOnWaySale());

                        //计算可售库存
                        Long availableStock = StockNumUtils.calculateAvailableStock(record.getStock(), record.getOnWayStock(), record.getOccupy(),
                                Objects.nonNull(warehouseSkuDTO.getOnWaySale()) ?
                                        OnWaySaleTypeEnum.SUPPORTED.getCode() == warehouseSkuDTO.getOnWaySale() : Boolean.FALSE);
                        record.setAvailableStock(availableStock);

                        //设置寄售标识
                        record.setPurchaseModel(warehouseSkuDTO.getPurchaseModel());

                        //寄售标识有效性校验
                        validAndSetPurchaseModelTag(reqVO, record);
                    }
                }
            } else {
                //厂直库存
                //计算可售库存
                Long availableStock = StockNumUtils.calculateAvailableStock(record.getStock(), 0L, record.getOccupy(), Boolean.FALSE);
                record.setAvailableStock(availableStock);
            }
        }
    }

    /**
     * 验证寄售标签，若不合法则标记记录为无效状态(仅sku搜索场景)
     *
     * @param reqVO  包含请求数据的值对象
     * @param record 需要验证和设置的库存记录值对象
     */
    private void validAndSetPurchaseModelTag(SkuStockReqVO reqVO, SkuStockVO record) {
        Integer reqPurchaseModel = reqVO.getPurchaseModel();
        if (reqPurchaseModel != null) {
            boolean isInvalid;

            if (PurchaseModelTypeEnum.forCode(reqPurchaseModel) == PurchaseModelTypeEnum.STOCK_UP) {
                isInvalid = !(record.getPurchaseModel() == null || record.getPurchaseModel().equals(PurchaseModelTypeEnum.STOCK_UP.getCode()));
            } else {
                isInvalid = !reqPurchaseModel.equals(record.getPurchaseModel());
            }

            if (isInvalid) {
                record.setValidStatus(false);
                log.warn("SkuStockManageServiceImpl.fillStockExtraInfo filter invalid data for purchaseModel, stock:{}", JSON.toJSONString(record));
            }
        }
    }

    /**
     * 为SKU库存列表填充库存安全阈值
     *
     * @param skuStockVOList SKU库存VO列表，包含skuId和warehouseId信息
     */
    private void fillStockThreshold(List<SkuStockVO> skuStockVOList) {
        if (CollectionUtils.isEmpty(skuStockVOList)) {
            return;
        }

        // 查询库存安全阈值列表
        BatchQueryStockThresholdDTO reqDTO = new BatchQueryStockThresholdDTO();
        List<SkuStockThresholdReqDTO> skuStockThresholdReqDTOList = com.google.common.collect.Lists.newArrayList();
        skuStockVOList.forEach(vo -> skuStockThresholdReqDTOList.add(
                new SkuStockThresholdReqDTO(vo.getSkuId(), vo.getWarehouseId() != null ? Long.valueOf(vo.getWarehouseId()) : null))
        );

        reqDTO.setSkuStockThresholdReqDTOList(skuStockThresholdReqDTOList);
        List<SkuStockThresholdResDTO> resDTOList = skuStockThresholdManageService.batchQuerySkuStockThreshold(reqDTO);
        if (CollectionUtils.isEmpty(resDTOList)) {
            log.error("SkuStockManageService.fillStockThreshold failed to get SkuStockThresholdResDTO for empty! req:{}", JSON.toJSONString(reqDTO));
            return;
        }

        Map<String, SkuStockThresholdResDTO> map = Maps.newHashMap();
        resDTOList.forEach(
                dto -> map.put(Objects.nonNull(dto.getWarehouseId()) ?
                        ("" + dto.getSkuId() + dto.getWarehouseId()) :
                        ("" + dto.getSkuId()), dto));
        // 将安全阈值补充到sku库存信息中
        skuStockVOList.forEach(vo -> {
            SkuStockThresholdResDTO dto = map.getOrDefault(Objects.nonNull(vo.getWarehouseId()) ?
                    ("" + vo.getSkuId() + vo.getWarehouseId()) :
                    ("" + vo.getSkuId()), null);

            //设置安全库存
            vo.setStockThreshold(Objects.nonNull(dto) ? dto.getThreshold() : null);
            //设置库存通知范围
            vo.setStockNoticeType(dto.getStockNoticeType());
        });
    }

    /**
     * 根据仓库ID集合查询仓库信息映射表
     *
     * @param warehouseIds 仓库ID集合
     * @return 以仓库ID为key，仓库信息为value的映射表
     */
    private Map<String, WarehouseResVO> queryWarehouseMap(Set<String> warehouseIds) {
        if (CollectionUtils.isEmpty(warehouseIds)) {
            return Collections.emptyMap();
        }
        WarehouseBatchGetReqVO reqDTO = new WarehouseBatchGetReqVO();
        reqDTO.setWarehouseIds(warehouseIds);
        List<WarehouseResVO> warehouseResDTOList = warehouseManageService.queryWarehouseByCondition(reqDTO);
        if (CollectionUtils.isEmpty(warehouseResDTOList)) {
            return Collections.emptyMap();
        }

        Map<String, WarehouseResVO> resultMap = Maps.newHashMap();
        warehouseResDTOList.forEach(warehouseResDTO -> {
            resultMap.put(String.valueOf(warehouseResDTO.getId()), warehouseResDTO);
        });
        return resultMap;
    }

    @Override
    public Map<Long, Map<String, SkuStockVO>> listSkuWarehouseStock(SkuStockReqVO skuStockReqVO) {
        if (CollectionUtils.isEmpty(skuStockReqVO.getSkuIds())) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<StockPO> wrapper = Wrappers.<StockPO>lambdaQuery()
                .in(StockPO::getSkuId, skuStockReqVO.getSkuIds())
                .eq(StockPO::getYn, YnEnum.YES.getCode());
        List<StockPO> dbRecord = stockAtomicService.list(wrapper);

        if (CollectionUtils.isEmpty(dbRecord)) {
            return Collections.emptyMap();
        }
        // 转换库存信息
        List<SkuStockVO> res = convertSkuStockVO(dbRecord);
        // 补充仓库SKU绑定关系
        this.fillStockFulfillmentTag(skuStockReqVO, res);

        return Optional.of(res).orElseGet(ArrayList::new).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(SkuStockVO::getSkuId, Collectors.toMap(SkuStockVO::getWarehouseId, Function.identity())));
    }

    private List<SkuStockVO> convertSkuStockVO(List<StockPO> dbRecord) {
        List<SkuStockVO> res = Lists.newArrayList();
        Set<Long> warehouseIds = dbRecord.stream().filter(Objects::nonNull).filter(po -> StringUtils.isNotBlank(po.getWarehouseId())).map(po -> Long.parseLong(po.getWarehouseId())).collect(Collectors.toSet());
        Set<Long> validWarehouseIds = warehouseAtomicService.queryValidWarehouse(warehouseIds);

        for (StockPO stockPO : dbRecord) {
            // 排除未启用的仓库存
            if (StringUtils.isNotBlank(stockPO.getWarehouseId()) && !validWarehouseIds.contains(Long.parseLong(stockPO.getWarehouseId()))) {
                continue;
            }
            SkuStockVO skuStockVO = SkuStockConvert.INSTANCE.stockPOToSkuStockVO(stockPO);
            if (StringUtils.isBlank(stockPO.getWarehouseId())) {
                skuStockVO.setWarehouseId(Constant.FACTORY_DEFAULT_ID_STR);
                skuStockVO.setStockStr(Objects.nonNull(skuStockVO.getStock()) ? String.valueOf(skuStockVO.getStock()) : "");
            }
            res.add(skuStockVO);
        }
        return res;
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, StockReadResDTO>> queryCnStock(StockReadReqDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getStockItems())) {
            return DataResponse.success(Collections.emptyMap());
        }

        if (StringUtils.isBlank(reqDTO.getCountryCode()) && StringUtils.isBlank(reqDTO.getClientCode())) {
            return DataResponse.error("查询国内商品库存接口，国家编码和客户编码不能同时为空");
        }

        // 根据客户和商品信息查询集运中心地址
        Set<Long> skuIds = Sets.newHashSet();
        Map<Long, StockItemReqDTO> itemReqDTOMap = Maps.newHashMap();
        reqDTO.getStockItems().forEach(item -> {
            skuIds.add(item.getSkuId());
            itemReqDTOMap.put(item.getSkuId(), item);
        });
        // 查询履约决策，返回仓地址和IOP客户信息
        Optional<CustomerSkuDecisionResult> resultOptional = this.getCustomerSkuDecisionResult(reqDTO);
        // 为空直接返回空
        if (!resultOptional.isPresent() || MapUtils.isEmpty(resultOptional.get().getSkuFulfillmentDecisionResult())) {
            log.warn("queryCnStock 商品集运中心地址信息为空,入参:[{}]", JSON.toJSONString(reqDTO));
            return DataResponse.success(Collections.emptyMap());
        }

        // 集运中心和商品信息
        CustomerSkuDecisionResult decisionResult = resultOptional.get();
        // 获取商品集运中心地址信息
        Map<String, List<SkuInfoResDTO>> skuInforResDtoMap = decisionResult.getSkuFulfillmentDecisionResult();
        Map<Long, StockReadResDTO> stockReadResDTOMap = Maps.newHashMap();
        // 遍历商品集运中心地址信息，查询商品区域限售信息
        for (Map.Entry<String, List<SkuInfoResDTO>> entry : skuInforResDtoMap.entrySet()) {
            // 查询国内库存
            Map<Long, StockReadResDTO> subStockReadResMap = this.getSubStockResDTOMap(entry.getValue(), decisionResult, itemReqDTOMap);
            stockReadResDTOMap.putAll(subStockReadResMap);
        }
        return DataResponse.success(stockReadResDTOMap);
    }

    @Override
    public Map<Long, StockReadResDTO> queryCnRealStock(StockReadReqDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getStockItems())) {
            return Collections.emptyMap();
        }

        if (StringUtils.isBlank(reqDTO.getCountryCode()) && StringUtils.isBlank(reqDTO.getClientCode())) {
            throw new BizException("查询国内商品库存接口，国家编码和客户编码不能同时为空");
        }

        // 根据客户和商品信息查询集运中心地址
        Set<Long> skuIds = Sets.newHashSet();
        Map<Long, StockItemReqDTO> itemReqDTOMap = Maps.newHashMap();
        reqDTO.getStockItems().forEach(item -> {
            skuIds.add(item.getSkuId());
            itemReqDTOMap.put(item.getSkuId(), item);
        });

        // 查询履约决策，返回仓地址和IOP客户信息
        Optional<CustomerSkuDecisionResult> resultOptional = this.getCustomerSkuDecisionResult(reqDTO);
        // 为空直接返回空
        if (!resultOptional.isPresent() || MapUtils.isEmpty(resultOptional.get().getSkuFulfillmentDecisionResult())) {
            log.warn("queryCnRealStock 商品集运中心地址信息为空,入参:[{}]", JSON.toJSONString(reqDTO));
            return Collections.emptyMap();
        }
        // 集运中心和商品信息
        CustomerSkuDecisionResult decisionResult = resultOptional.get();
        // 获取商品集运中心地址信息
        Map<String, List<SkuInfoResDTO>> skuInforResDtoMap = decisionResult.getSkuFulfillmentDecisionResult();
        Map<Long, StockReadResDTO> stockReadResDTOMap = Maps.newHashMap();
        // 遍历商品集运中心地址信息，查询商品区域限售信息
        for (Map.Entry<String, List<SkuInfoResDTO>> entry : skuInforResDtoMap.entrySet()) {
            Map<Long, StockReadResDTO> subStockReadResMap = this.getSubCnRealStockMap(entry.getValue(), itemReqDTOMap);
            stockReadResDTOMap.putAll(subStockReadResMap);
        }
        return stockReadResDTOMap;
    }

    @NotNull
    private Map<Long, StockReadResDTO> getSubCnRealStockMap(List<SkuInfoResDTO> skuInfoResDTOList, Map<Long, StockItemReqDTO> itemReqDTOMap) {
        if (CollectionUtils.isEmpty(skuInfoResDTOList)) {
            return Collections.emptyMap();
        }
        Map<Long, StockReadResDTO> subStockReadResMap = Maps.newHashMap();
        Set<Long> skuIds = skuInfoResDTOList.stream().map(SkuInfoResDTO::getSkuId).collect(Collectors.toSet());
        // 取第一个商品地址
        SkuInfoResDTO skuInfoResDTO = skuInfoResDTOList.get(0);
        StoreHouseConsigneeInfoDTO consigneeInfoDTO = skuInfoResDTO.getStoreHouseConsigneeInfoDTO();
        // 查询国际和国内SKU映射
        Map<Long, Long> jdSkuMap = skuAtomicService.batchQueryJdSku(skuIds);
        // 查询国内库存
        AreaStockStateGlobalParam req = this.buildCnRealStockParam(itemReqDTOMap, jdSkuMap, consigneeInfoDTO);

        Map<Long, Long> invertMap = MapUtils.invertMap(jdSkuMap);
        Map<Long, MkuStockInfoVO> rpcRes = areaStockStateRpcService.queryAreaStockStateGlobal(req);
        if (MapUtils.isNotEmpty(rpcRes)) {
            //结果中的jdSkuId转回国际mkuId
            rpcRes.values().forEach(mkuStockInfo -> {
                StockReadResDTO target = new StockReadResDTO();
                target.setSkuId(invertMap.get(mkuStockInfo.getMkuId()));
                target.setRemainNum(mkuStockInfo.getNum().longValue());
                target.setStockStateType(Integer.valueOf(mkuStockInfo.getStockStateType()));
                subStockReadResMap.put(mkuStockInfo.getMkuId(), target);
            });
        }
        return subStockReadResMap;
    }

    private AreaStockStateGlobalParam buildCnRealStockParam(Map<Long, StockItemReqDTO> itemReqDTOMap, Map<Long, Long> jdSkuMap, StoreHouseConsigneeInfoDTO consigneeInfoDTO) {
        AreaStockStateGlobalParam req = new AreaStockStateGlobalParam();
        //商品请求数量信息
        List<SkuNumParam> skuNumList = new ArrayList<>();
        jdSkuMap.forEach((skuId, jdSkuId) -> {
            SkuNumParam stockReq = new SkuNumParam();
            stockReq.setSkuId(jdSkuId);
            StockItemReqDTO item = itemReqDTOMap.get(skuId);
            stockReq.setNum(item.getNum());
            skuNumList.add(stockReq);
        });
        //地址信息
        AreaParam areaParam = new AreaParam();
        areaParam.setStateId(DEFAULT_STATE_ID);
        areaParam.setProvinceId(consigneeInfoDTO.getConsigneeProvinceId().intValue());
        areaParam.setCityId(consigneeInfoDTO.getConsigneeCityId().intValue());
        areaParam.setCountyId(consigneeInfoDTO.getConsigneeCountyId().intValue());
        areaParam.setTownId(consigneeInfoDTO.getConsigneeTownId().intValue());
        req.setAreaParam(areaParam);
        req.setSkuNumList(skuNumList);
        return req;
    }

    @NotNull
    private Map<Long, StockReadResDTO> getSubStockResDTOMap(List<SkuInfoResDTO> skuInfoResDTOList, CustomerSkuDecisionResult decisionResult, Map<Long, StockItemReqDTO> itemReqDTOMap) {
        if (CollectionUtils.isEmpty(skuInfoResDTOList)) {
            return Collections.emptyMap();
        }
        Map<Long, StockReadResDTO> stockReadResDTOMap = Maps.newHashMapWithExpectedSize(skuInfoResDTOList.size());
        Set<Long> skuIds = skuInfoResDTOList.stream().map(SkuInfoResDTO::getSkuId).collect(Collectors.toSet());
        // 查询国际和国内SKU映射
        Map<Long, Long> jdSkuMap = skuAtomicService.batchQueryJdSku(skuIds);
        if (MapUtils.isEmpty(jdSkuMap)) {
            log.warn("SkuStockManageServiceImpl.getSubStockResDTOMap 查询商品信息为空 入参:[{}]", JSON.toJSONString(skuIds));
            return Collections.emptyMap();
        }
        GetStockByIdGoodsReq req = buildGetStockParam(skuInfoResDTOList, decisionResult, itemReqDTOMap, jdSkuMap);
        // 查询国内商品库存
        Map<Long, GetStockByIdGoodsResp> jdSkuStockMap = skuStockRpcService.getCnStockBySkuIdMap(req);

        if (MapUtils.isEmpty(jdSkuStockMap)) {
            log.warn("SkuStockManageServiceImpl.getSubStockResDTOMap 查询国内商品库存为空 入参:[{}]", JSON.toJSONString(req));
            return Collections.emptyMap();
        }
        jdSkuMap.forEach((skuId, jdSkuId) -> {
            GetStockByIdGoodsResp goodsResp = jdSkuStockMap.getOrDefault(jdSkuId, new GetStockByIdGoodsResp());
            StockReadResDTO readResDTO = SkuStockConvert.INSTANCE.goodsStock2StockReadResDTO(goodsResp);
            stockReadResDTOMap.put(skuId, readResDTO);
        });
        return stockReadResDTOMap;
    }

    @NotNull
    private GetStockByIdGoodsReq buildGetStockParam(List<SkuInfoResDTO> skuInfoResDTOList, CustomerSkuDecisionResult decisionResult, Map<Long, StockItemReqDTO> itemReqDTOMap, Map<Long, Long> jdSkuMap) {
        // 集运仓地址信息
        StoreHouseConsigneeInfoDTO consigneeInfoDTO = skuInfoResDTOList.get(0).getStoreHouseConsigneeInfoDTO();
        // 查询国内库存参数构建
        GetStockByIdGoodsReq req = new GetStockByIdGoodsReq();
        // 集运仓地址
        req.setAreaInfo(this.buildBaseAreaVo(consigneeInfoDTO));
        req.setPin(decisionResult.getIopPin());
        req.setClientId(decisionResult.getIopClientId());
        // sku查询数量
        List<SkuNumBaseGoodsReq> skuNumInfoList = new ArrayList<>();
        jdSkuMap.forEach((skuId, jdSkuId) -> {
            SkuNumBaseGoodsReq goodsReq = new SkuNumBaseGoodsReq();
            goodsReq.setSkuId(jdSkuId);
            StockItemReqDTO stockItemReqDTO = itemReqDTOMap.get(skuId);
            goodsReq.setSkuNumber(Objects.nonNull(stockItemReqDTO) ? stockItemReqDTO.getNum() : 1);
            skuNumInfoList.add(goodsReq);
        });
        req.setSkuNumInfoList(skuNumInfoList);
        return req;
    }

    /**
     * 构建基础地区信息对象
     *
     * @param consigneeInfoDTO 存储库收货人信息DTO
     * @return 基础地区信息对象
     */
    private AreaBaseInfoGoodsReq buildBaseAreaVo(StoreHouseConsigneeInfoDTO consigneeInfoDTO) {
        AreaBaseInfoGoodsReq baseAreaVo = new AreaBaseInfoGoodsReq();
        baseAreaVo.setProvinceId(consigneeInfoDTO.getConsigneeProvinceId());
        baseAreaVo.setCityId(consigneeInfoDTO.getConsigneeCityId());
        baseAreaVo.setCountyId(consigneeInfoDTO.getConsigneeCountyId());
        baseAreaVo.setTownId(consigneeInfoDTO.getConsigneeTownId());
        return baseAreaVo;
    }

    /**
     * 根据国家代码或客户代码获取商品库存决策结果。
     *
     * @param reqVO 查询请求对象，包含国家代码或客户代码和商品ID列表。
     * @return 商品库存决策结果的Optional对象，若未找到匹配的决策结果则返回空的Optional对象。
     */
    private Optional<CustomerSkuDecisionResult> getCustomerSkuDecisionResult(StockReadReqDTO reqVO) {
        Set<Long> skuIds = reqVO.getStockItems().stream().map(StockItemReqDTO::getSkuId).collect(Collectors.toSet());
        if (StringUtils.isNotBlank(reqVO.getCountryCode())) {
            return Optional.ofNullable(fulfillmentPolicyRpcService.querySkuMultiWarehouseDecisionByCountry(reqVO.getCountryCode(), skuIds));
        } else if (StringUtils.isNotBlank(reqVO.getClientCode())) {
            return Optional.ofNullable(fulfillmentPolicyRpcService.querySkuMultiWarehouseDecisionByCustomer(reqVO.getClientCode(), skuIds));
        }
        return Optional.empty();
    }

    /**
     * 更新商品库存信息（包括现货库存和安全库存阈值）
     *
     * @param skuStockReqVO 包含skuId、stock、stockThreshold等更新信息的请求对象
     * @return 返回操作结果，成功时包含true，失败时包含错误信息
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public DataResponse<Boolean> updateStockInfo(SkuStockUpdateReqVO skuStockReqVO) {
        log.info("SkuStockManageServiceImpl.updateStockInfo start, req:{}", JSON.toJSONString(skuStockReqVO));
        //1、更新供应商现货库存
        if (Objects.isNull(skuStockReqVO.getWarehouseId())) {
            String bizId = UUID.randomUUID().toString();
            StockManageReqDTO req = buildStockManageReqDTO(skuStockReqVO, bizId);
            DataResponse<Boolean> stockResult = stockManageService.saveOrUpdate(req);
            log.info("SkuStockManageServiceImpl.updateStockInfo saveOrUpdate req:{} , res:{}", JSON.toJSONString(req), stockResult);
            if (!stockResult.getSuccess()) {
                String errorMsg = String.format("更新商品 %s 厂直现货库存 %s 失败!", skuStockReqVO.getSkuId(), skuStockReqVO.getStock());
                DataResponse.error(errorMsg);
            }
        }

        //2、更新安全库存阈值
        if (Objects.nonNull(skuStockReqVO.getStockThreshold())) {
            // 更新安全库存参数
            SkuStockThresholdVO skuStockThresholdVO = buildSkuStockThresholdVO(skuStockReqVO);
            DataResponse<Boolean> dataResponse = skuStockThresholdManageService.saveOrUpdate(skuStockThresholdVO);
            if (!dataResponse.getSuccess()) {
                log.error("SkuStockManageServiceImpl.update 更新安全库存阈值失败, req:{},dataResponse={}", JSON.toJSONString(skuStockThresholdVO), JSON.toJSONString(dataResponse));

                String errorMsg = String.format("更新商品 %s 安全库存阈值 %s 失败", skuStockReqVO.getSkuId(), skuStockReqVO.getStockThreshold());
                return DataResponse.error(errorMsg);
            }
        }

        //3、更新库存预警通知范围
        if (Objects.nonNull(skuStockReqVO.getStockNoticeType())) {
            SkuStockThresholdDataVO skuStockThresholdDataVO = buildSkuStockThresholdDataVO(skuStockReqVO);

            DataResponse<String> dataResponse = skuStockThresholdManageService.updateSkuStockThresholdInfo(skuStockThresholdDataVO);
            if (!dataResponse.getSuccess()) {
                log.error("SkuStockManageServiceImpl.updateSkuStockThresholdInfo 更新库存通知范围失败, req:{},dataResponse={}", JSON.toJSONString(skuStockThresholdDataVO), JSON.toJSONString(dataResponse));

                String errorMsg = String.format("更新商品 %s 库存预警通知范围 %s 失败", skuStockReqVO.getSkuId(), skuStockReqVO.getStockNoticeType());
                return DataResponse.error(errorMsg);
            }
        }

        return DataResponse.success(Boolean.TRUE);
    }

    /**
     * 构建SKU库存阈值数据VO对象
     * @param skuStockReqVO SKU库存更新请求VO对象，包含库存通知类型、SKU ID、仓库ID等信息
     * @return 构建完成的SKU库存阈值数据VO对象，包含库存通知类型、SKU ID、仓库ID等字段及创建/更新时间
     */
    private @NotNull SkuStockThresholdDataVO buildSkuStockThresholdDataVO(SkuStockUpdateReqVO skuStockReqVO) {
        SkuStockThresholdDataVO skuStockThresholdDataVO = new SkuStockThresholdDataVO();

        //设置库存通知范围类型
        skuStockThresholdDataVO.setStockNoticeType(skuStockReqVO.getStockNoticeType());

        skuStockThresholdDataVO.setSkuId(skuStockReqVO.getSkuId());
        skuStockThresholdDataVO.setWarehouseId(skuStockReqVO.getWarehouseId());
        skuStockThresholdDataVO.setCreator(skuStockReqVO.getCreator());
        skuStockThresholdDataVO.setUpdater(skuStockReqVO.getUpdater());
        long now = System.currentTimeMillis();
        skuStockThresholdDataVO.setCreateTime(now);
        skuStockThresholdDataVO.setUpdateTime(now);
        return skuStockThresholdDataVO;
    }

    /**
     * 构建SKU库存阈值VO对象
     * @param skuStockReqVO SKU库存更新请求VO，包含库存阈值、SKU ID、仓库ID、创建人和更新人信息
     * @return 返回构建好的SKU库存阈值VO对象，包含库存阈值、SKU ID、仓库ID、创建人、更新人及创建更新时间
     */
    private @NotNull SkuStockThresholdVO buildSkuStockThresholdVO(SkuStockUpdateReqVO skuStockReqVO) {
        SkuStockThresholdVO skuStockThresholdVO = new SkuStockThresholdVO();

        //设置安全库存
        skuStockThresholdVO.setStockThreshold(skuStockReqVO.getStockThreshold());

        skuStockThresholdVO.setSkuId(skuStockReqVO.getSkuId());
        skuStockThresholdVO.setWarehouseId(skuStockReqVO.getWarehouseId());
        skuStockThresholdVO.setCreator(skuStockReqVO.getCreator());
        skuStockThresholdVO.setUpdater(skuStockReqVO.getUpdater());
        long now = System.currentTimeMillis();
        skuStockThresholdVO.setCreateTime(now);
        skuStockThresholdVO.setUpdateTime(now);
        return skuStockThresholdVO;
    }

    /**
     * 构建库存管理请求DTO对象
     * @param skuStockReqVO SKU库存更新请求VO，包含skuId、stock、updater等字段
     * @param bizId 业务编号
     * @return 组装好的库存管理请求DTO对象
     */
    private @NotNull StockManageReqDTO buildStockManageReqDTO(SkuStockUpdateReqVO skuStockReqVO, String bizId) {
        StockManageReqDTO req = new StockManageReqDTO();
        List<StockItemManageReqDTO> stockItem = new ArrayList<>(1);
        StockItemManageReqDTO target = new StockItemManageReqDTO();
        target.setSkuId(skuStockReqVO.getSkuId());
        target.setNum(skuStockReqVO.getStock());
        target.setUpdater(skuStockReqVO.getUpdater());
        if (Objects.nonNull(skuStockReqVO.getWarehouseId())) {
            target.setWarehouseId(skuStockReqVO.getWarehouseId().toString());
        }
        stockItem.add(target);
        req.setStockItem(stockItem);
        req.setBizNo(bizId);
        return req;
    }

}
