package com.jdi.isc.product.soa.service.mapstruct;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.SystemEnum;
import com.jdi.isc.product.soa.api.common.enums.CategoryAttrInputTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.PurchaseModelTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.sku.SkuStatusEnum;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockResDTO;
import com.jdi.isc.product.soa.common.constants.AttributeConstants;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.ProductConstant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.enums.AttributeCheckTypeEnum;
import com.jdi.isc.product.soa.common.enums.AttributeDimensionEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.util.BigDecimalUtil;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeFlatVO;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeValueFlatVO;
import com.jdi.isc.product.soa.domain.category.biz.GlobalQualificationVO;
import com.jdi.isc.product.soa.domain.category.po.CategoryPO;
import com.jdi.isc.product.soa.domain.common.biz.BaseVO;
import com.jdi.isc.product.soa.domain.enums.CompanyTypeEnum;
import com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.domain.enums.mku.MkuRelationBindStatusEnum;
import com.jdi.isc.product.soa.domain.enums.spu.SkuTaxCalculateCountryEnums;
import com.jdi.isc.product.soa.domain.enums.spu.SpuTaxCountryEnums;
import com.jdi.isc.product.soa.domain.enums.warehouse.OnWaySaleTypeEnum;
import com.jdi.isc.product.soa.domain.enums.warehouse.WarehouseUnBindType;
import com.jdi.isc.product.soa.domain.gms.req.JdProductQueryDTO;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceVO;
import com.jdi.isc.product.soa.domain.sku.biz.*;
import com.jdi.isc.product.soa.domain.sku.po.SkuCertificatePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.taxRate.biz.CategoryTaxVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseSkuUnBindResVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseSkuUnBindVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseSkuVO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehousePO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehouseSkuPO;
import com.jdi.isc.product.soa.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.product.soa.service.atomic.category.CategoryAtomicService;
import com.jdi.isc.product.soa.service.atomic.category.GlobalAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseSkuAtomicService;
import com.jdi.isc.product.soa.service.manage.attribute.AttributeOutService;
import com.jdi.isc.product.soa.service.manage.price.SkuTaxPriceManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuAuditRecordManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuTaxManageService;
import com.jdi.isc.product.soa.service.manage.stock.StockManageService;
import com.jdi.isc.product.soa.service.manage.warehouse.WarehouseSkuManageService;
import com.jdi.isc.product.soa.service.mapstruct.sku.SkuConvert;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.AttributeConstants.*;
import static com.jdi.isc.product.soa.common.constants.Constant.COMMA;
import static com.jdi.isc.product.soa.common.constants.Constant.FACTORIAL;
import static com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum.CUSTOMER;
import static com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum.SUPPLIER;

/**
 * <AUTHOR>
 * @date 2023/11/30
 **/
@Service
@Slf4j
public class SkuConvertService {

    @Resource
    private AttributeOutService attributeOutService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    @Lazy
    private Map<String, SpuTaxManageService> spuTaxManageServiceMap;
    @Resource
    @Lazy
    private Map<String, SkuTaxPriceManageService> skuTaxPriceManageServiceMap;

    @LafValue("jdi.isc.product.soa.makeUpRateConfig")
    public String makeUpRateConfig;

    @Resource
    private WarehouseAtomicService warehouseAtomicService;
    @Resource
    private WarehouseSkuAtomicService warehouseSkuAtomicService;
    @Resource
    private StockManageService stockManageService;
    @Resource
    private SpuConvertService spuConvertService;
    @Resource
    private GlobalAttributeAtomicService globalAttributeAtomicService;
    @Resource
    private CategoryAtomicService categoryAtomicService;

    @Lazy
    @Resource
    private WarehouseSkuManageService warehouseSkuManageService;
    @Resource
    private SpuAuditRecordManageService spuAuditRecordManageService;
    @Resource
    private SkuInfoRpcService skuInfoRpcService;

    /**
     * 转换sku对象
     *
     * @param skuVoList skuVo对象
     * @param skuIds    新生成的skuId
     */
    public List<SkuPO> convertSkuVoListToSkuPo(List<SkuVO> skuVoList, List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuVoList)) {
            return Collections.emptyList();
        }
        final Long spuId = skuVoList.get(0).getSpuId();
        final int len = skuVoList.size();
        List<SkuPO> skuPoList = new ArrayList<>(len);
        Map<Long, Long> jdCatAndIscCatMap = getJdCatAndIscCatMap(skuVoList);
        // 查询主站商品上下架状态
        Map<Long, Integer> jdSkuStatusMap = getJdSkuStatusMap(skuVoList);
        for (int i = 0; i < len; i++) {
            SkuVO skuVO = skuVoList.get(i);
            skuVO.setSkuId(skuIds.get(i));
            skuVO.setSpuId(spuId);
            // 跨境品新增时默认同步主站状态，不存在默认上架；本土默认上架
            skuVO.setSkuStatus(getSkuStatus(jdSkuStatusMap, skuVO));
            skuVO.setDownReason(SkuStatusEnum.ON_SALE.getStatus() == skuVO.getSkuStatus() ? Constant.MINUS : ProductConstant.DOWN_REASON);
            SkuPO skuPo = SkuConvert.INSTANCE.vo2po(skuVO);
            // 填补国际类目ID
            skuPo.setCatId(jdCatAndIscCatMap.getOrDefault(skuVO.getCatId(),skuVO.getCatId()));
            // 添加按群组划分的扩展属性, json字符串
            skuPo.setGroupExtAttribute(spuConvertService.getGroupExtAttribute(skuVO.getStoreExtendPropertyList()));
            // 销售属性
//            skuPo.setSaleAttribute(this.saleAttributeFromSkuVo(skuVO));
            skuPoList.add(skuPo);
        }
        return skuPoList;
    }

    @PFTracing
    public int getSkuStatus(Map<Long, Integer> jdSkuStatusMap, SkuVO skuVO) {
        if (MapUtils.isNotEmpty(jdSkuStatusMap) && Objects.nonNull(skuVO.getJdSkuId())){
            Integer skuStatus = jdSkuStatusMap.getOrDefault(skuVO.getJdSkuId(), SpuStatusEnum.ON_SALE.getStatus());
            return SkuStatusEnum.defaultSkuStatus(skuStatus);
        }else {
            return SpuStatusEnum.ON_SALE.getStatus();
        }
    }

    public String saleAttributeFromSkuVo(SkuVO skuVO) {
        if (CollectionUtils.isEmpty(skuVO.getStoreSalePropertyList())) {
            return "";
        }
        // sku销售属性拼接
        StringBuilder skuSaleStr = new StringBuilder();
        for (PropertyValueVO valueVO : skuVO.getStoreSalePropertyList()) {
            skuSaleStr.append(Constant.HASHTAG).append(valueVO.getAttributeId()).append(Constant.COLON).append(valueVO.getAttributeValueId());
        }
        // 剔除第一个字符
        return skuSaleStr.substring(1);
    }


    public List<SkuPriceVO> convertSkuPriceVoFromSkuVo(String sourceCountryCode, SkuVO skuVo) {
        List<SkuPriceVO> skuPriceVOList = Lists.newArrayList();
        for (TradeDirectionEnum tradeDirectionEnum : TradeDirectionEnum.values()) {
            SkuPriceVO skuPriceVO = new SkuPriceVO();
            skuPriceVO.setSkuId(skuVo.getSkuId());
            skuPriceVO.setSpuId(skuVo.getSpuId());
            skuPriceVO.setCurrency(skuVo.getCurrency());
            skuPriceVO.setSourceCountryCode(sourceCountryCode);
            // 销售价格且为空时跳过执行
            if (tradeDirectionEnum.equals(CUSTOMER) && StringUtils.isBlank(skuVo.getSalePrice())) {
                continue;
            }
            // 销售价如果为空，默认填采购价
            skuPriceVO.setPrice(new BigDecimal(tradeDirectionEnum.equals(SUPPLIER) ? skuVo.getPurchasePrice() : (Objects.isNull(skuVo.getSalePrice()) ? skuVo.getPurchasePrice() : skuVo.getSalePrice())));
            String taxPrice = "";
            if (tradeDirectionEnum.equals(SUPPLIER)) {
                taxPrice = skuVo.getTaxPurchasePrice();
            } else {
                taxPrice = skuVo.getTaxSalePrice();
            }

            skuPriceVO.setTaxPrice(StringUtils.isNotBlank(taxPrice) ? new BigDecimal(taxPrice) : null);
            skuPriceVO.setTradeType(tradeDirectionEnum.equals(SUPPLIER) ? skuVo.getVendorTradeType() : skuVo.getCustomerTradeType());
            skuPriceVO.setTradeDirection(tradeDirectionEnum);
            skuPriceVOList.add(skuPriceVO);
        }
        return skuPriceVOList;
    }

    public List<SkuPriceVO> convertPartitionSkuPriceVoFromSkuVo(String sourceCountryCode, List<SkuVO> skuVOList) {
        List<SkuPriceVO> skuPriceVOList = Lists.newArrayList();
        skuVOList.forEach(skuVO -> skuPriceVOList.addAll(this.convertSkuPriceVoFromSkuVo(sourceCountryCode, skuVO)));
        return skuPriceVOList;
    }


    public void convertSaleAttributeValueForView(SkuVO skuVo, List<PropertyVO> salePropertyList) {
        if (StringUtils.isBlank(skuVo.getSaleAttribute())) {
            return;
        }

        String saleAttribute = skuVo.getSaleAttribute();
        Map<Long, Map<Long, String>> saleAttributeMap = Maps.newHashMap();
        for (PropertyVO vo : salePropertyList) {
            saleAttributeMap.put(vo.getAttributeId(), vo.getPropertyValueVOList().stream().collect(Collectors.toMap(PropertyValueVO::getAttributeValueId, PropertyValueVO::getAttributeValueName)));
        }

        List<PropertyValueVO> saleValueList = Lists.newArrayList();
        // 拆分为多个维度销售属性
        String[] attributeArray = saleAttribute.split(Constant.HASHTAG);
        for (String att : attributeArray) {
            PropertyValueVO valueVO = new PropertyValueVO();
            // 拆分为销售属性ID、属性值ID
            String[] attributeValueArray = att.split(Constant.COLON);
            Long attributeId = Long.valueOf(attributeValueArray[0]);
            valueVO.setAttributeId(attributeId);
            // 补充属性值ID、属性值名称
            Map<Long, String> attributeValueMap = saleAttributeMap.get(attributeId);
            Long attributeValueId = Long.valueOf(attributeValueArray[1]);
            valueVO.setAttributeValueId(attributeValueId);
            valueVO.setAttributeValueName(MapUtils.isNotEmpty(attributeValueMap) ? attributeValueMap.get(attributeValueId) : "");
            saleValueList.add(valueVO);
        }
        skuVo.setStoreSalePropertyList(saleValueList);
    }

    public void convertSaleAttributeAndFillSelectedForView(List<PropertyValueVO> storeSaleValueList, List<PropertyVO> viewSalePropertyList) {
        if (CollectionUtils.isEmpty(viewSalePropertyList)) {
            return;
        }

        Map<Long, Set<Long>> storeSaleAttributeAndValueMap = CollectionUtils.isNotEmpty(storeSaleValueList) ? storeSaleValueList.stream()
                .collect(Collectors.groupingBy(PropertyValueVO::getAttributeId, Collectors.mapping(PropertyValueVO::getAttributeValueId, Collectors.toSet()))) : Maps.newHashMap();

        // 回填销售属性值选中状态
        for (PropertyVO propertyVO : viewSalePropertyList) {
            if (!storeSaleAttributeAndValueMap.containsKey(propertyVO.getAttributeId())) {
                continue;
            }
            List<PropertyValueVO> propertyValueVOList = propertyVO.getPropertyValueVOList();
            Set<Long> valueSet = storeSaleAttributeAndValueMap.get(propertyVO.getAttributeId());
            for (PropertyValueVO propertyValueVO : propertyValueVOList) {
                if (valueSet.contains(propertyValueVO.getAttributeValueId())) {
                    propertyValueVO.setSelected(Boolean.TRUE);
                }
            }
        }
    }

    private PropertyVO propertyVo(AttributeFlatVO extendFlatVo) {
        PropertyVO propertyVO = SpuConvert.INSTANCE.attributeFlatVo2PropertyVo(extendFlatVo);
        List<AttributeValueFlatVO> attributeValueFlatVOList = extendFlatVo.getAttributeValueList();

        List<PropertyValueVO> propertyValueVOList = Optional.ofNullable(attributeValueFlatVOList).orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull).map(SpuConvert.INSTANCE::attributeFlatValueVo2PropertyValueVo).sorted(Comparator.comparing(PropertyValueVO::getSort)).collect(Collectors.toList());

        propertyVO.setPropertyValueVOList(propertyValueVOList);
        return propertyVO;
    }

    /**
     * 跨境
     *
     * @param sourceCountryCode 发货国
     * @return 跨境返回true 本地返回false
     */
    public boolean crossborder(String sourceCountryCode) {
        return CountryConstant.COUNTRY_ZH.equals(sourceCountryCode);
    }

    /**
     * 填充JD供应商代码
     *
     * @param skuVOList SKU值对象列表
     */
    public void fillJdVendorCode(List<SkuVO> skuVOList) {
        Set<Long> skuIds = skuVOList.stream().map(SkuVO::getSkuId).collect(Collectors.toSet());
        Map<Long, SkuPO> skuMap = skuAtomicService.batchQuerySkuPO(skuIds);
        if (MapUtils.isNotEmpty(skuMap)) {
            for (SkuVO skuVO : skuVOList) {
                if (StringUtils.isBlank(skuVO.getJdVendorCode()) && skuMap.containsKey(skuVO.getSkuId()) && Objects.nonNull(skuMap.get(skuVO.getSkuId()))) {
                    skuVO.setJdVendorCode(skuMap.get(skuVO.getSkuId()).getJdVendorCode());
                }
            }
        }
    }

    /**
     * 初始化越南税值。
     *
     * @param skuGroupPropertyVOS SKU组属性值对象列表
     * @param sourceCountryCode   源国代码
     */
    @PFTracing
    public void initVnTaxValue(List<GroupPropertyVO> skuGroupPropertyVOS, String sourceCountryCode, Long catId) {
        if (!CountryConstant.COUNTRY_VN.equals(sourceCountryCode)) {
            log.info("SpuReadManageServiceImpl.initVnTaxValue sourceCountryCode is not {}", CountryConstant.COUNTRY_VN);
            return;
        }

        if (CollectionUtils.isEmpty(skuGroupPropertyVOS)) {
            log.info("SpuReadManageServiceImpl.initVnTaxValue skuGroupPropertyVOS is null");
            return;
        }
        SpuTaxManageService spuTaxManageService = spuTaxManageServiceMap.get(SpuTaxCountryEnums.getEnumByCountryCode(sourceCountryCode).getServiceName());

        CategoryTaxVO categoryTaxVO = spuTaxManageService.getCatTaxVO(catId, null, sourceCountryCode, CompanyTypeEnum.FDI);
        if (categoryTaxVO == null) {
            log.info("SpuReadManageServiceImpl.initVnTaxValue categoryTaxVO is null");
            return;
        }

        String taxId = spuTaxManageService.getAttributeId(AttributeDimensionEnum.SKU, AttributeConstants.TAX_ID);

        skuGroupPropertyVOS.forEach(item1 -> {
            item1.getPropertyVOS().forEach(item2 -> {
                if (Long.valueOf(taxId).equals(item2.getAttributeId())) {
                    if (CollectionUtils.isEmpty(item2.getPropertyValueVOList())) {
                        return;
                    }
                    PropertyValueVO propertyValueVO = item2.getPropertyValueVOList().get(0);
                    if (StringUtils.isBlank(propertyValueVO.getAttributeValueName())) {
                        propertyValueVO.setAttributeValueName(categoryTaxVO.getVatRate() != null ? categoryTaxVO.getVatRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString() : null);
                    }
                }
            });
        });
    }

    public List<GroupPropertyVO> fillInterAttributeSelected(List<GroupPropertyVO> interPropertyList, Map<String, List<String>> storeExtendMap) {
        List<GroupPropertyVO> spuInterExtendPropertyList = new ArrayList<>();
        for (GroupPropertyVO groupPropertyVO : interPropertyList) {
            for (PropertyVO propertyVO : groupPropertyVO.getPropertyVOS()) {
                Long attributeId = propertyVO.getAttributeId();
                if (!storeExtendMap.containsKey(String.valueOf(attributeId))) {
                    continue;
                }
                PropertyVO storePropertyVO = SpuConvert.INSTANCE.oldPropertyVo2NewVo(propertyVO);
                storePropertyVO.setPropertyValueVOList(Lists.newArrayList());
                this.fillInterAttributeValueSelected(propertyVO, propertyVO.getPropertyValueVOList(), storePropertyVO.getPropertyValueVOList(), storeExtendMap.get(String.valueOf(attributeId)));
            }
            spuInterExtendPropertyList.add(groupPropertyVO);
        }
        log.info("SkuConvertService.fillInterAttributeSelected propertyValueVOList={}", JSON.toJSONString(interPropertyList));
        return spuInterExtendPropertyList;


    }

    /**
     * 回填扩展属性用于界面展示
     *
     * @param propertyValueVOList 扩展属性值列表
     * @param storeValueList      已存储扩展属性值列表
     */
    private void fillInterAttributeValueSelected(PropertyVO propertyVO, List<PropertyValueVO> propertyValueVOList, List<PropertyValueVO> storePropertyValueVOList, List<String> storeValueList) {
        for (PropertyValueVO valueVO : propertyValueVOList) {
            if (CollectionUtils.isEmpty(storeValueList)) {
                continue;
            }

            // 存储的扩展属性，包括当前属性Id或者属性值前缀为语言的，即为选中
            if (propertyVO != null
                    && CategoryAttrInputTypeEnum.TEXT.getCode().equals(propertyVO.getAttributeInputType())
                    && storeValueList.contains(valueVO.getAttributeValueId().toString())) {
                valueVO.setSelected(Boolean.TRUE);
            }

            log.info("SkuConvertService.fillInterAttributeValueSelected storeValueList={},valueVO={}", JSON.toJSONString(storeValueList), JSON.toJSONString(valueVO));
            if (StringUtils.isNotBlank(valueVO.getLang())
                    && storeValueList.stream().filter(Objects::nonNull).anyMatch(v -> v.startsWith(valueVO.getLang()))) {
                // 截掉en_ 或 en^ 前缀
                Optional<String> valueNameOptional = storeValueList.stream().filter(Objects::nonNull).filter(v -> v.startsWith(valueVO.getLang())).findFirst();
                if (valueNameOptional.isPresent()) {
                    String valueName = valueNameOptional.get();
                    String substring = valueName.substring((valueVO.getLang() + FACTORIAL).length());
                    valueVO.setAttributeValueName(substring);
                    valueVO.setSelected(Boolean.TRUE);
                }
            }

            if (propertyVO != null
                    && !CategoryAttrInputTypeEnum.TEXT.getCode().equals(propertyVO.getAttributeInputType())) {
                if (CollectionUtils.isNotEmpty(storeValueList) && storeValueList.contains(valueVO.getAttributeValueId().toString())) {
                    valueVO.setSelected(Boolean.TRUE);
                }
            }

            storePropertyValueVOList.add(valueVO);
        }
        log.info("SkuConvertService.fillInterAttributeValueSelected propertyValueVOList={}", JSON.toJSONString(propertyValueVOList));
    }

    /**
     * 拆分扩展属性字符串
     *
     * @param extAttribute 扩展属性字符串
     * @return 拆分后的扩展属性和属性值映射
     */
    public Map<String, List<String>> splitInterAttributeStr(String extAttribute) {
        if (StringUtils.isBlank(extAttribute)) {
            return new HashMap<>();
        }
        Map<String, List<String>> storeExtendMap = Maps.newHashMap();
        String[] extAttributeArray = extAttribute.split(Constant.HASHTAG);
        for (String attribute : extAttributeArray) {
            String[] attributeAndValueArray = attribute.split(COMMA);
            for (String attributeValue : attributeAndValueArray) {
                String[] valueArray = attributeValue.split(Constant.COLON);
                String key = valueArray[0];
                if (storeExtendMap.containsKey(key)) {
                    storeExtendMap.get(key).add(valueArray[1]);
                } else {
                    storeExtendMap.put(valueArray[0], Lists.newArrayList(valueArray[1]));
                }
            }
        }
        log.info("SkuConvertService.splitInterAttributeStr storeExtendMap={}", JSON.toJSONString(storeExtendMap));
        return storeExtendMap;
    }

    public void convertGroupSkuInterCertificateVO(SkuVO skuVo, List<SkuCertificatePO> skuCertificateVOList
            , List<GlobalQualificationVO> globalQualificationVOS) {
        if (CollectionUtils.isEmpty(globalQualificationVOS) && CollectionUtils.isEmpty(skuCertificateVOList)) {
            log.error("SkuConvertService.convertGroupSkuInterCertificateVO params is null");
            return;
        }

        Map<Long, SkuCertificatePO> skuCertificateVOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(skuCertificateVOList)) {
            skuCertificateVOMap.putAll(skuCertificateVOList.stream().collect(Collectors.toMap(SkuCertificatePO::getCertificateId, Function.identity(), (v1, v2) -> v2)));
        }

        // 读草稿时，将草稿数据合并进来
        if (CollectionUtils.isNotEmpty(skuVo.getSkuCertificateVOList())) {
            skuVo.getSkuCertificateVOList().forEach(item -> {
                if (CollectionUtils.isNotEmpty(item.getSkuCertificateVOS())) {
                    skuCertificateVOMap.putAll(item.getSkuCertificateVOS().stream().filter(Objects::nonNull).collect(Collectors.toMap(SkuCertificateVO::getCertificateId, SkuConvert.INSTANCE::certificateVo2Po, (v1, v2) -> v2)));
                }
            });
        }

        // 商品资质PO -> 商品资质VO，并补充资质名、资质说明等信息
        List<SkuCertificateVO> results = Lists.newArrayList();
        for (GlobalQualificationVO globalQualificationVO : globalQualificationVOS) {
            if (MapUtils.isNotEmpty(skuCertificateVOMap) && skuCertificateVOMap.containsKey(globalQualificationVO.getId())) {
                SkuCertificatePO skuCertificatePO = skuCertificateVOMap.get(globalQualificationVO.getId());
                SkuCertificateVO skuCertificateVO = SkuConvert.INSTANCE.certificatePo2Vo(skuCertificatePO);
                if (globalQualificationVO.getGlobalQualificationLangVOList() != null && !globalQualificationVO.getGlobalQualificationLangVOList().isEmpty()) {
                    skuCertificateVO.setCertificateName(globalQualificationVO.getGlobalQualificationLangVOList().get(0).getQualificationName());
                }
                skuCertificateVO.setCertificateNote(globalQualificationVO.getQualificationRemark());
                skuCertificateVO.setClassification1(globalQualificationVO.getClassification1());
                skuCertificateVO.setClassification2(globalQualificationVO.getClassification2());
                results.add(skuCertificateVO);
            } else {
                SkuCertificateVO skuCertificateVO = SkuConvert.INSTANCE.globalCertVo2SkuCertVo(globalQualificationVO);
                if (globalQualificationVO.getGlobalQualificationLangVOList() != null && !globalQualificationVO.getGlobalQualificationLangVOList().isEmpty()) {
                    skuCertificateVO.setCertificateName(globalQualificationVO.getGlobalQualificationLangVOList().get(0).getQualificationName());
                }
                results.add(skuCertificateVO);
            }
        }

        Map<Integer, List<SkuCertificateVO>> skuCertMap = results.stream().collect(Collectors.groupingBy(SkuCertificateVO::getRequirement));
        List<GroupSkuCertificateVO> resultList = new ArrayList<>();
        skuCertMap.forEach((k, v) -> {
            GroupSkuCertificateVO groupSkuCertificateVO = new GroupSkuCertificateVO();
            groupSkuCertificateVO.setSkuId(skuVo.getSkuId());
            groupSkuCertificateVO.setRequirement(k);
            groupSkuCertificateVO.setSkuCertificateVOS(v);
            resultList.add(groupSkuCertificateVO);
        });
        skuVo.setSkuCertificateVOList(resultList);

    }

    public void convertSkuSaleAttributeAndFillSelectedForView(List<SkuVO> skuVoList, List<PropertyVO> salePropertyVoList) {
//        skuVoList.forEach(vo -> this.convertSaleAttributeAndFillSelectedForView(vo.getStoreSalePropertyList(), salePropertyVoList));
    }

    /**
     * 填充创建产品时的含税采购价
     *
     * @param systemCode 系统编码
     * @param spuVO      spu实体
     * @param skuVOList  SKU信息列表
     */
    public void fillTaxPurchasePrice(String systemCode, SpuVO spuVO, List<SkuVO> skuVOList) {
        log.info("SkuConvertService.fillTaxPurchasePrice 补充含税采购价 systemCode={},spuVO={},skuVOList={}", systemCode, JSONObject.toJSONString(spuVO), JSONObject.toJSONString(skuVOList));
        skuVOList.forEach(skuvo -> {
            if (StringUtils.isNotBlank(skuvo.getPurchasePrice())) {
                BigDecimal purchasePrice = new BigDecimal(skuvo.getPurchasePrice());

                SkuCalculatePriceReqVO input = new SkuCalculatePriceReqVO();
                input.setCatId(spuVO.getCatId());
                input.setSkuId(skuvo.getSkuId());
                input.setSourceCountryCode(spuVO.getSourceCountryCode());
                input.setPurchasePrice(purchasePrice);
                input.setSkuPriceFlag(Boolean.FALSE);
                input.setSupplierCode(spuVO.getVendorCode());
                SkuCalculateTaxPriceVO calculateTaxPriceVO = skuTaxPriceManageServiceMap.get(SkuTaxCalculateCountryEnums.getEnumByCountryCode(spuVO.getSourceCountryCode()).getServiceName()).skuTaxPrice(input);
                if (calculateTaxPriceVO != null && calculateTaxPriceVO.getTaxPurchasePrice() != null) {
                    log.info("SkuConvertService.fillTaxPurchasePrice 补充含税采购价 systemCode={},sourceCountryCode={},taxPurchasePrice={}", systemCode, spuVO.getSourceCountryCode(), calculateTaxPriceVO.getTaxPurchasePrice());
                    skuvo.setTaxPurchasePrice(calculateTaxPriceVO.getTaxPurchasePrice().toString());
                }
            }
        });
    }

    /**
     * 填充创建产品时的销售价
     *
     * @param systemCode        系统编码
     * @param sourceCountryCode 来源国家编码
     * @param skuVOList         SKU信息列表
     */
    public void fillSalePriceForCreateProduct(String systemCode, String sourceCountryCode, List<SkuVO> skuVOList) {
        log.info("SkuConvertService.fillSalePriceForCreateProduct VC发品销售价补充 systemCode={},sourceCountryCode={},skuVOList={}", systemCode, sourceCountryCode, skuVOList);
        // 最终获取sku对应发货国的销售价、采购价
        if (!SystemEnum.VC.name().equals(systemCode) || CollectionUtils.isEmpty(skuVOList)) {
            return;
        }
        // 加价率
        String makeUpRate = ConfigUtils.getStringByKey(makeUpRateConfig, sourceCountryCode);
        log.info("SkuConvertService.fillSalePriceForCreateProduct VC发品销售价补充 systemCode={},sourceCountryCode={},makeUpRate={}", systemCode, sourceCountryCode, makeUpRate);
        if (StringUtils.isBlank(makeUpRate)) {
            return;
        }
        skuVOList.forEach(skuvo -> {
            if (StringUtils.isNotBlank(skuvo.getPurchasePrice()) && StringUtils.isBlank(skuvo.getSalePrice())) {
                BigDecimal purchasePrice = new BigDecimal(skuvo.getPurchasePrice());
                BigDecimal salePrice = purchasePrice.add(purchasePrice.multiply(new BigDecimal(makeUpRate)));
                // 越南向上百位取整，其他2位向上取整。
                salePrice = CountryConstant.COUNTRY_VN.equals(sourceCountryCode) ? BigDecimalUtil.roundUpAtHundreds(salePrice) : salePrice.setScale(2, RoundingMode.UP);
                log.info("SkuConvertService.fillSalePriceForCreateProduct VC发品销售价补充,加价后的销售价 systemCode={},sourceCountryCode={},saleAttribute={},salePrice={}", systemCode, sourceCountryCode, JSON.toJSONString(skuvo.getStoreSalePropertyList()), salePrice);
                skuvo.setSalePrice(salePrice.toString());
            }

        });
    }


    /**
     * 将销售属性字符串拆分成多个维度的销售属性ID和属性值ID的映射关系。
     *
     * @param saleAttribute 销售属性字符串，格式为"attributeId1:attributeValueId1#attributeId2:attributeValueId2#..."
     * @return 销售属性ID到属性值ID的映射关系。
     */
    public Map<Long, Long> splitSaleAttribute(String saleAttribute) {
        if (StringUtils.isBlank(saleAttribute)) {
            return Maps.newHashMap();
        }
        Map<Long, Long> saleMap = Maps.newHashMap();
        // 拆分为多个维度销售属性
        String[] attributeArray = saleAttribute.split(Constant.HASHTAG);
        for (String att : attributeArray) {
            // 拆分为销售属性ID、属性值ID
            String[] attributeValueArray = att.split(Constant.COLON);
            Long attributeId = Long.valueOf(attributeValueArray[0]);
            Long attributeValueId = Long.valueOf(attributeValueArray[1]);
            saleMap.put(attributeId, attributeValueId);
        }

        return saleMap;
    }


    /**
     * 初始化 SKU 库存信息。
     *
     * @param skuVoList SKU 信息列表。
     * @param spuVO     SPU 信息。
     */
    @Transactional(rollbackFor = Exception.class, timeout = 30)
    public void initSkuStock(List<SkuVO> skuVoList, SpuVO spuVO) {
        // 更新人
        String updater = LoginContextHolder.getLoginContextHolder().getPin();
        // 货源国
        String sourceCountryCode = spuVO.getSourceCountryCode();
        // 本土商品设置长直库存
        this.processLocalFactoryStock(skuVoList, sourceCountryCode, updater);
        // 销售国
        String attributeScope = spuVO.getAttributeScope();
        // 查询备货仓库列表
        List<String> countryCodeList = Arrays.stream(attributeScope.split(COMMA)).filter(countryCode -> !CountryConstant.COUNTRY_ZH.equals(countryCode)).collect(Collectors.toList());
        List<WarehousePO> warehousePOList = warehouseAtomicService.queryWarehouseListByCountryCode(countryCodeList);
        if (CollectionUtils.isEmpty(warehousePOList)) {
            return;
        }
        // 校验仓库是否存在，存在设置库存；不存在报错提示
        Map<Long, String> warehouIdNoMap = warehousePOList.stream().filter(Objects::nonNull).collect(Collectors.toMap(WarehousePO::getId, WarehousePO::getWarehouseNo));
        // 查询sku和仓库绑定关系
        Set<Long> skuIds = skuVoList.stream().map(SkuVO::getSkuId).collect(Collectors.toSet());
        List<WarehouseSkuPO> warehouseSkuListFromDb = warehouseSkuAtomicService.queryListBySkuIds(skuIds);
        // 商品设置备货仓库存
        for (SkuVO skuVO : skuVoList) {
            this.processEachSku(skuVO, warehouIdNoMap, warehouseSkuListFromDb, updater,spuVO);
        }
    }

    /**
     * 处理本地厂直库存。
     *
     * @param skuVoList         SKU信息列表
     * @param sourceCountryCode 源国家代码
     * @param updater           更新人
     */
    private void processLocalFactoryStock(List<SkuVO> skuVoList, String sourceCountryCode, String updater) {
        if (!CountryConstant.COUNTRY_ZH.equals(sourceCountryCode)) {
            // 分批
            List<List<SkuVO>> partitionList = Lists.partition(skuVoList, Constant.PARTITION_SIZE);
            // 本土商品初始化库存
            for (List<SkuVO> subSkuVoList : partitionList) {
                // 查询已经设置的库存
                Map<Long, StockResDTO> stockResDTOMap = stockManageService.getStock(this.createStockManageReqDtoForQuery(subSkuVoList, updater));
                // 更新库存
                StockManageReqDTO stockManageReqDTO = this.createStockManageReqDtoForFactory(subSkuVoList, updater, stockResDTOMap);
                if (CollectionUtils.isNotEmpty(stockManageReqDTO.getStockItem())) {
                    DataResponse<Boolean> response = stockManageService.saveOrUpdate(stockManageReqDTO);
                    if (Objects.equals(response.getSuccess(), Boolean.FALSE)) {
                        log.error("sku初始化库存失败,skuStockReqVO={}", JSON.toJSONString(stockManageReqDTO));
                    }
                    log.info("保存sku库存成功,skuStockReqVO={}", JSON.toJSONString(stockManageReqDTO));
                }
            }
        }
    }

    /**
     * 处理每个 SKU 的库存信息和仓库绑定。
     *
     * @param skuVO                  SKU 对象
     * @param warehouIdNoMap         仓库 ID 和编号的映射关系
     * @param warehouseSkuListFromDb 仓库数据库信息
     * @param updater                更新人
     */
    private void processEachSku(SkuVO skuVO, Map<Long, String> warehouIdNoMap, List<WarehouseSkuPO> warehouseSkuListFromDb, String updater, SpuVO spuVO) {
        // 库存信息
        List<SkuStockRelationVO> resourceSkuStockRelationList = skuVO.getSkuStockRelationList();
        // 库存列表为空或不存在非默认仓库库存的跳过设置库存
        if (CollectionUtils.isEmpty(resourceSkuStockRelationList) || resourceSkuStockRelationList.stream().noneMatch(s -> Constant.FACTORY_DEFAULT_ID.toString().equals(s.getWarehouseId()))) {
            log.info("库存列表为空或不存在非默认仓库库存的跳过设置库存 skuId :{}", skuVO.getSkuId());
            return;
        }
        // sku设置的编码不存在时返回报错
        if (resourceSkuStockRelationList.stream().filter(s -> !Constant.FACTORY_DEFAULT_ID_STR.equals(s.getWarehouseId()))
                .anyMatch(s -> MapUtils.isNotEmpty(warehouIdNoMap) && StringUtils.isNotBlank(s.getWarehouseId()) && !warehouIdNoMap.containsKey(Long.parseLong(s.getWarehouseId())))) {
            throw new BizException(String.format("商品 %s 绑定的仓库不存在,请检查", skuVO.getSkuId()));
        }
        // sku已绑定的仓库集合
        Set<Long> warehouseIdSet = warehouseSkuListFromDb.stream()
                .filter(po -> skuVO.getSkuId().equals(po.getSkuId()))
                .map(WarehouseSkuPO::getWarehouseId)
                .collect(Collectors.toSet());
        // 处理已经绑定备货仓的SKU库存
        handleAlreadyBoundSkuStock(skuVO, warehouseSkuListFromDb, updater, resourceSkuStockRelationList, warehouseIdSet);
        // 处理解绑备货仓的SKU库存
        handleUnBindSkuStock(skuVO,warehouseIdSet, updater,spuVO);
    }

    /**
     * 处理解绑SKU备货仓关系。
     * @param skuVO SKU信息
     */
    private void handleUnBindSkuStock(SkuVO skuVO,Set<Long> warehouseIdSet, String updater, SpuVO spuVO) {
        List<SkuStockRelationVO> resourceSkuStockRelationList = skuVO.getSkuStockRelationList();
        log.info("SkuConvertService.handleUnBindSkuStock  解绑商品备货仓 skuId:[{}] updater:[{}] resourceSkuStockRelationList:[{}]",skuVO.getSkuId(),updater,JSON.toJSONString(resourceSkuStockRelationList));
        Set<Long> bindWarehouseIds = resourceSkuStockRelationList.stream()
                .filter(Objects::nonNull)
                .filter(vo-> StringUtils.isNotBlank(vo.getWarehouseId()))
                .map(vo-> Long.valueOf(vo.getWarehouseId()))
                // 过滤出解绑状态数据
                .filter(warehouseId -> !Constant.FACTORY_DEFAULT_ID.equals(warehouseId))
                .collect(Collectors.toSet());

        // 数据库中绑定的仓ID集合大于
        Set<Long> removeWarehouseIds = new HashSet<>(warehouseIdSet);
        removeWarehouseIds.removeAll(bindWarehouseIds);
        log.info("SkuConvertService.handleUnBindSkuStock 商品需要解除绑定的仓ID集合 warehouseIdSet={},bindWarehouseIds={},removeWarehouseIds={}"
                ,JSON.toJSONString(warehouseIdSet),JSON.toJSONString(bindWarehouseIds),JSON.toJSONString(removeWarehouseIds));
        if (CollectionUtils.isNotEmpty(removeWarehouseIds)) {
            // 批量解绑SKU备货仓关系
            Set<WarehouseSkuUnBindVO> unBindRelationVOList = removeWarehouseIds.stream()
                    .map(warehouseId-> convertSkuStockRelationToUnBindVO(skuVO.getSkuId(),warehouseId))
                    .collect(Collectors.toSet());
            // 查询仓库是否支持解绑，不能返回原因并提示
            List<WarehouseSkuUnBindResVO> warehouseSkuUnBindResVOList = warehouseSkuManageService.queryWarehouseSkuUnbindRelation(unBindRelationVOList);

            Set<WarehouseSkuUnBindResVO> unBindResVOS = warehouseSkuUnBindResVOList.stream().filter(Objects::nonNull).filter(resVo -> !resVo.getValidResult()).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(unBindResVOS)) {
                // 不能解绑，提示消息
                Set<String> errorMsgSet = unBindResVOS.stream().map(resVo -> resVo.getWarehouseNo()+resVo.getResultMsg()).collect(Collectors.toSet());
                String msg = String.join("\\n",errorMsgSet);
                throw new BizException(String.format("商品 [%s] 和备货仓库不能解绑。原因: %s", skuVO.getSkuId(),msg));
            }

            // 审核操作时，操作人取最新的审核修改记录数据操作人
            String operator = getOperator(updater, spuVO);
            Boolean unbound = warehouseSkuManageService.unbindSkuWarehouseBindRelation(unBindRelationVOList, operator, updater);
            log.info("SkuConvertService.handleUnBindSkuStock 商品解绑备货仓 入参:[unBindRelationVOList={},operator={},updater={}],出参:[{}]",JSON.toJSONString(unBindRelationVOList),operator,updater, unbound);
            if (!unbound) {
                throw new BizException(String.format("商品 [%s] 和备货仓库解绑失败", skuVO.getSkuId()));
            }
        }
    }

    private String getOperator(String updater, SpuVO spuVO) {
        String operator = updater;
        // 审核操作时，操作人取最新的审核修改记录数据操作人
        if (Objects.nonNull(spuVO) && Objects.nonNull(spuVO.getApproveFlag()) && Constant.ONE == spuVO.getApproveFlag()){
            List<SpuAuditRecordVO> auditRecordVOList = spuAuditRecordManageService.getAuditRecordByStatus(spuVO.getSpuId(), SpuAuditStatusEnum.WAITING_APPROVED.getCode());
            if (CollectionUtils.isNotEmpty(auditRecordVOList)) {
                operator = auditRecordVOList.stream().max(Comparator.comparing(SpuAuditRecordVO::getUpdateTime)).map(BaseVO::getUpdater).orElse(updater);
            }
        }
        return operator;
    }

    /**
     * 处理已经绑定的 SKU 库存。
     * @param skuVO SKU 信息对象
     * @param warehouseSkuListFromDb 从数据库中获取的仓库 SKU 列表
     * @param updater 更新人
     * @param resourceSkuStockRelationList SKU 库存关系列表
     * @param warehouseIdSet 仓库 ID 集合
     */
    private void handleAlreadyBoundSkuStock(SkuVO skuVO, List<WarehouseSkuPO> warehouseSkuListFromDb, String updater, List<SkuStockRelationVO> resourceSkuStockRelationList, Set<Long> warehouseIdSet) {
        // 1. 获取所有的 WarehouseSkuPO 对象（不做过滤）
        List<WarehouseSkuPO> allWarehouseSkuPOList = resourceSkuStockRelationList.stream()
                .filter(Objects::nonNull)
                .filter(stockRelationVO -> !Constant.FACTORY_DEFAULT_ID.toString().equals(stockRelationVO.getWarehouseId()))
                // 过滤出绑定状态数据
                .filter(skuStockRelationVO -> Objects.isNull(skuStockRelationVO.getBindStatus()) || YnEnum.YES.getCode().equals(skuStockRelationVO.getBindStatus()))
                .map(vo -> this.createWarehouseSkuPO(skuVO, vo, updater))
                .collect(Collectors.toList());

        log.info("SkuConvertService.handleAlreadyBoundSkuStock SKU绑定的所有备货仓信息 skuId:[{}] updater:[{}] allWarehouseSkuPOList:[{}]",skuVO.getSkuId(),updater,JSON.toJSONString(allWarehouseSkuPOList));
        // 2. 拆分已有的列表
        List<WarehouseSkuPO> existingWarehouseSkuPOList = allWarehouseSkuPOList.stream()
                .filter(po -> CollectionUtils.isNotEmpty(warehouseIdSet) && warehouseIdSet.contains(po.getWarehouseId()))
                .collect(Collectors.toList());
        //批量更新
        updateWarehouseSkus(existingWarehouseSkuPOList, warehouseSkuListFromDb, skuVO.getSkuId(), updater);
        if (CollectionUtils.isNotEmpty(existingWarehouseSkuPOList)){
            log.info("SkuConvertService.handleAlreadyBoundSkuStock SKU绑定的已绑定状态的货仓信息 skuId:[{}] updater:[{}] existingWarehouseSkuPOList:[{}]",skuVO.getSkuId(),updater,JSON.toJSONString(existingWarehouseSkuPOList));
        }
        // 3. 拆分新增的列表
        List<WarehouseSkuPO> newWarehouseSkuPOList = allWarehouseSkuPOList.stream()
                .filter(po -> CollectionUtils.isEmpty(warehouseIdSet) || !warehouseIdSet.contains(po.getWarehouseId()))
                .collect(Collectors.toList());
        addWarehouseSkus(newWarehouseSkuPOList, skuVO.getSkuId(), updater);
        if (CollectionUtils.isNotEmpty(newWarehouseSkuPOList)){
            log.info("SkuConvertService.handleAlreadyBoundSkuStock SKU绑定的新增绑定货仓信息 skuId:[{}] updater:[{}] newWarehouseSkuPOList:[{}]",skuVO.getSkuId(),updater,JSON.toJSONString(newWarehouseSkuPOList));
        }
    }

    void updateWarehouseSkus(List<WarehouseSkuPO> updates, List<WarehouseSkuPO> warehouseSkuListFromDb, Long skuId, String updater) {
        if (CollectionUtils.isEmpty(updates) || CollectionUtils.isEmpty(warehouseSkuListFromDb)) {
            return;
        }
        Iterator<WarehouseSkuPO> iterator = updates.iterator();
        List<WarehouseSkuPO> updatedList = new ArrayList<>();
        while (iterator.hasNext()) {
            WarehouseSkuPO update = iterator.next();
            Optional<WarehouseSkuPO> existing = warehouseSkuListFromDb.stream()
                    .filter(dbSku -> isSameSku(dbSku, update))
                    .findFirst();
            if (existing.isPresent()) {
                WarehouseSkuPO dbWarehouseSkuPO = existing.get();
                // 仓库库存模式
                boolean purchaseModelSame = (Objects.isNull(update.getPurchaseModel()) && Objects.isNull(dbWarehouseSkuPO.getPurchaseModel())) || (Objects.nonNull(update.getPurchaseModel()) && update.getPurchaseModel().equals(dbWarehouseSkuPO.getPurchaseModel()));
                if(update.getOnWaySale().equals(dbWarehouseSkuPO.getOnWaySale()) && purchaseModelSame){
                    iterator.remove();
                }else {
                    update.setId(dbWarehouseSkuPO.getId());
                    updatedList.add(update);
                }
            }else {
                log.error("数据库中无查询到该数据 WarehouseSkuPO:{}",JSON.toJSONString(update));
                new BizException("Warehouse Sku Database record not found");
            }
        }
        log.info("SkuConvertService.updateWarehouseSkus skuId={},updatedList={}", skuId, JSON.toJSONString(updatedList));
        // 执行批量更新操作
        if (!CollectionUtils.isEmpty(updatedList)) {
            updatedList.forEach(update->{
                WarehouseSkuVO skuVO = new WarehouseSkuVO();
                skuVO.setUpdater(updater);
                skuVO.setWarehouseId(update.getWarehouseId());
                skuVO.setSkuId(update.getSkuId());
                skuVO.setOnWaySale(update.getOnWaySale());
                skuVO.setPurchaseModel(update.getPurchaseModel());
                Boolean success = updateOnWaySale(skuVO);
                if (!success) {
                    log.error("Failed to update warehouseSku records");
                }
            });
        } else {
            log.info("No data to update, no changes required for skuId: {}", skuId);
        }
    }

    public Boolean updateOnWaySale(WarehouseSkuVO skuVO) {
        UpdateWrapper<WarehouseSkuPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(WarehouseSkuPO::getWarehouseId, skuVO.getWarehouseId())
                .eq(WarehouseSkuPO::getSkuId, skuVO.getSkuId())
                .eq(WarehouseSkuPO::getBindStatus, MkuRelationBindStatusEnum.BIND.getCode())
                .eq(WarehouseSkuPO::getYn, YnEnum.YES.getCode());
        WarehouseSkuPO updatePO = new WarehouseSkuPO();
        updatePO.setOnWaySale(skuVO.getOnWaySale());
        updatePO.setPurchaseModel(skuVO.getPurchaseModel());
        updatePO.setUpdater(skuVO.getUpdater());
        updatePO.setUpdateTime(System.currentTimeMillis());
        return warehouseSkuAtomicService.update(updatePO, updateWrapper);
    }

    /**
     * 判断两个 WarehouseSkuPO 是否在四个维度上完全相同
     */
    private boolean isSameSku(WarehouseSkuPO dbSku, WarehouseSkuPO update) {
        return dbSku.getWarehouseId().equals(update.getWarehouseId()) &&
                dbSku.getSkuId().equals(update.getSkuId()) &&
                dbSku.getBindStatus().equals(update.getBindStatus());
    }


    void addWarehouseSkus(List<WarehouseSkuPO> adds, Long skuId, String updater) {
        if (CollectionUtils.isEmpty(adds)) {
            log.info("SkuConvertService.addWarehouseSkus sku没有需要新绑定的备货仓 skuId={},skuStockRelationList={}", skuId, JSON.toJSONString(adds));
            return;
        }
        adds.forEach(add->{
            add.setUpdater(updater);
            add.setCreator(updater);
        });
        boolean saved = warehouseSkuAtomicService.saveBatch(adds);
        if (!saved) {
            Set<Long> warehouseIds = adds.stream().map(WarehouseSkuPO::getWarehouseId).collect(Collectors.toSet());
            log.error("AbstractSpuWriteManageService.initSkuStock sku绑定备货仓失败 warehouseSkuPOList={},saved={}", JSON.toJSONString(adds), saved);
            String errMsg = String.format("%s SkuConvertService.buildWarehouse error skuId=%s sku绑定备货仓失败: %s", LevelCode.P0.getMessage()
                    , JSON.toJSONString(adds.stream().map(WarehouseSkuPO::getSkuId).collect(Collectors.toSet())), JSON.toJSONString(warehouseIds));
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING, errMsg);
        } else {
            // 仓库绑定成功，然后初始化仓库库存
            adds.forEach(po -> {
                StockManageReqDTO reqDTO = this.createStockManageReqDtoForWarehouse(po);
                DataResponse<Boolean> dataResponse = stockManageService.saveOrUpdate(reqDTO);
                log.info("AbstractSpuWriteManageService.initSkuStock sku设置备货仓库存 入参:[reqDTO={}] 出参:[dataResponse={}]", JSON.toJSONString(reqDTO), JSON.toJSONString(dataResponse));
            });
        }
    }


    /**
     * 根据仓库SKU信息创建库存管理请求DTO。
     *
     * @param po 仓库SKU信息对象
     * @return 库存管理请求DTO
     */
    private StockManageReqDTO createStockManageReqDtoForWarehouse(WarehouseSkuPO po) {
        StockManageReqDTO reqDTO = new StockManageReqDTO();
        List<StockItemManageReqDTO> stockItemManageReqDTOList = Lists.newArrayList();
        stockItemManageReqDTOList.add(new StockItemManageReqDTO(po.getSkuId(), 0L, po.getUpdater(), String.valueOf(po.getWarehouseId())));
        reqDTO.setStockItem(stockItemManageReqDTOList);
        reqDTO.setBizNo(UUID.randomUUID().toString());
        return reqDTO;
    }

    /**
     * 创建用于查询的 StockManageReqDTO 对象。
     *
     * @param subSkuVoList 子 SKU 列表
     * @param updater      更新人
     * @return StockManageReqDTO 对象
     */
    private StockManageReqDTO createStockManageReqDtoForQuery(List<SkuVO> subSkuVoList, String updater) {
        StockManageReqDTO stockManageReqDTO = new StockManageReqDTO();
        List<StockItemManageReqDTO> itemDtoList = Lists.newArrayList();
        // 处理库存逻辑
        subSkuVoList.forEach(skuVO -> itemDtoList.add(new StockItemManageReqDTO(skuVO.getSkuId(), 1L, updater, null)));
        stockManageReqDTO.setStockItem(itemDtoList);
        stockManageReqDTO.setBizNo(UUID.randomUUID().toString());
        return stockManageReqDTO;
    }

    /**
     * 创建 StockManageReqDTO 对象，用于处理库存逻辑。
     *
     * @param subSkuVoList 子 SKU 列表
     * @param updater      更新人
     * @return StockManageReqDTO 对象
     */
    private StockManageReqDTO createStockManageReqDtoForFactory(List<SkuVO> subSkuVoList, String updater, Map<Long, StockResDTO> stockResDTOMap) {
        StockManageReqDTO stockManageReqDTO = new StockManageReqDTO();
        stockManageReqDTO.setStockItem(this.createStockItemList(subSkuVoList, updater, stockResDTOMap));
        stockManageReqDTO.setBizNo(UUID.randomUUID().toString());
        return stockManageReqDTO;
    }

    private List<StockItemManageReqDTO> createStockItemList(List<SkuVO> subSkuVoList, String updater, Map<Long, StockResDTO> stockResDTOMap) {
        List<StockItemManageReqDTO> itemDtoList = Lists.newArrayList();
        // 处理库存逻辑
        for (SkuVO skuVO : subSkuVoList) {
            this.processEachSkuStockItem(updater, stockResDTOMap, skuVO, itemDtoList);
        }
        return itemDtoList;
    }

    private void processEachSkuStockItem(String updater, Map<Long, StockResDTO> stockResDTOMap, SkuVO skuVO, List<StockItemManageReqDTO> itemDtoList) {
        // 兼容老库存逻辑
        List<SkuStockRelationVO> skuStockRelationList = this.compatiblePreSkuStock(skuVO);
        Optional<SkuStockRelationVO> first = skuStockRelationList.stream().filter(skuStockRelationVO -> Constant.FACTORY_DEFAULT_ID_STR.equals(skuStockRelationVO.getWarehouseId())).findFirst();
        if (first.isPresent()) {
            StockResDTO stockResDTO = stockResDTOMap.get(skuVO.getSkuId());
            SkuStockRelationVO relationVO = first.get();
            if (StringUtils.isNotBlank(relationVO.getStock()) && Objects.nonNull(stockResDTO) && stockResDTO.getAvailableStock().equals(Long.parseLong(relationVO.getStock()))) {
                log.info("SkuConvertService.createStockManageReqDtoForFactory skuId={} 库存已存在,跳过设置本土库存 stockNum={}", skuVO.getSkuId(), stockResDTO.getAvailableStock());
                return;
            }
            itemDtoList.add(new StockItemManageReqDTO(skuVO.getSkuId()
                    , StringUtils.isNotBlank(relationVO.getStock()) ? Long.parseLong(relationVO.getStock()) : 0L
                    , updater, null));
        }
    }

    /**
     * 获取兼容的之前 SKU 库存关系列表。
     * 如果 SKU 没有库存关系，则默认设置厂直仓库库存。
     *
     * @param skuVO SKU 对象
     * @return 兼容的前端 SKU 库存关系列表
     */
    private List<SkuStockRelationVO> compatiblePreSkuStock(SkuVO skuVO) {
        List<SkuStockRelationVO> skuStockRelationList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(skuVO.getSkuStockRelationList())) {
            // 兼容老本土品，无SKU和仓库关系，默认设置厂直仓库库存
            skuStockRelationList.add(new SkuStockRelationVO(Constant.FACTORY_DEFAULT_ID_STR, skuVO.getSpuId(), skuVO.getSkuId(), skuVO.getStockNum()));
        } else {
            skuStockRelationList = skuVO.getSkuStockRelationList();
        }
        return skuStockRelationList;
    }

    /**
     * 创建仓库商品信息实体对象
     *
     * @param skuVO           商品信息
     * @param stockRelationVO 库存关系信息
     * @param updater         更新人
     * @return 创建好的仓库商品信息实体对象
     */
    private WarehouseSkuPO createWarehouseSkuPO(SkuVO skuVO, SkuStockRelationVO stockRelationVO, String updater) {
        WarehouseSkuPO warehouseSkuPO = new WarehouseSkuPO();
        warehouseSkuPO.setWarehouseId(Long.parseLong(stockRelationVO.getWarehouseId()));
        warehouseSkuPO.setSkuId(skuVO.getSkuId());
        warehouseSkuPO.setBindStatus(YesOrNoEnum.YES.getCode());
        warehouseSkuPO.setCreator(updater);
        warehouseSkuPO.setUpdater(updater);
        warehouseSkuPO.setOnWaySale(stockRelationVO.getOnWaySale() == null ? OnWaySaleTypeEnum.NOT_SUPPORTED.getCode() : stockRelationVO.getOnWaySale());
        warehouseSkuPO.setPurchaseModel(stockRelationVO.getPurchaseModel() == null ? PurchaseModelTypeEnum.STOCK_UP.getCode() : stockRelationVO.getPurchaseModel());
        long now = System.currentTimeMillis();
        warehouseSkuPO.setCreateTime(now);
        warehouseSkuPO.setUpdateTime(now);
        return warehouseSkuPO;
    }

    private WarehouseSkuUnBindVO convertSkuStockRelationToUnBindVO(Long skuId,Long warehouseId) {
        WarehouseSkuUnBindVO warehouseSkuUnBindVO = new WarehouseSkuUnBindVO();
        warehouseSkuUnBindVO.setSkuId(skuId);
        warehouseSkuUnBindVO.setWarehouseId(warehouseId);
        warehouseSkuUnBindVO.setUnBindType(WarehouseUnBindType.UNBIND.getCode());
        return warehouseSkuUnBindVO;
    }

    /**
     * 填充巴西税率映射表。
     *
     * @param vo                  SkuVO 对象，包含商品的属性信息。
     * @param spuTaxManageService SpuTaxManageService 对象，用于获取税率相关的属性ID。
     * @param taxRateMap          Map对象，用于存储税率信息，键为属性ID，值为税率值。
     */
    public void fillBrTaxRateMap(SkuVO vo, SpuTaxManageService spuTaxManageService, Map<Long, String> taxRateMap) {
        if (CollectionUtils.isEmpty(vo.getSkuInterPropertyList())) {
            log.error("SkuConvertService.fillBrTaxRateMap params is null");
            return;
        }
        for (GroupPropertyVO groupPropertyVO : vo.getSkuInterPropertyList()) {
            if (AttributeCheckTypeEnum.PUBLISH_PRODUCT.getCode().equals(groupPropertyVO.getRequirement())
                    && CollectionUtils.isNotEmpty(groupPropertyVO.getPropertyVOS())) {
                List<PropertyVO> propertyVOList = groupPropertyVO.getPropertyVOS();
                for (PropertyVO propertyVO : propertyVOList) {
                    if (CollectionUtils.isEmpty(propertyVO.getPropertyValueVOList())) {
                        continue;
                    }
                    // IPI 税率
                    String brIpiId = spuTaxManageService.getAttributeId(AttributeDimensionEnum.SKU, BR_IPI);
                    if (propertyVO.getAttributeId().equals(Long.parseLong(brIpiId))) {
                        String ipiValue = propertyVO.getPropertyValueVOList().get(0).getAttributeValueName();
                        taxRateMap.put(propertyVO.getAttributeId(), ipiValue);
                    }
                    // PIS税率
                    String brPisId = spuTaxManageService.getAttributeId(AttributeDimensionEnum.SKU, BR_PIS);
                    if (propertyVO.getAttributeId().equals(Long.parseLong(brPisId))) {
                        String pisValue = propertyVO.getPropertyValueVOList().get(0).getAttributeValueName();
                        taxRateMap.put(propertyVO.getAttributeId(), pisValue);
                    }
                    // ICMS 税率
                    String brIcmsId = spuTaxManageService.getAttributeId(AttributeDimensionEnum.SKU, BR_ICMS_TAX);
                    if (propertyVO.getAttributeId().equals(Long.parseLong(brIcmsId))) {
                        String icmsValue = propertyVO.getPropertyValueVOList().get(0).getAttributeValueName();
                        taxRateMap.put(propertyVO.getAttributeId(), icmsValue);
                    }
                    // CONFINS 税率
                    String brConfinsId = spuTaxManageService.getAttributeId(AttributeDimensionEnum.SKU, BR_CONFINS_TAX);
                    if (propertyVO.getAttributeId().equals(Long.parseLong(brConfinsId))) {
                        String confinsValue = propertyVO.getPropertyValueVOList().get(0).getAttributeValueName();
                        taxRateMap.put(propertyVO.getAttributeId(), confinsValue);
                    }
                }
            }
        }
    }

    /**
     * 根据SkuVO列表获取京东类目ID与ISC类目ID的映射关系。
     * @param skuVOList SkuVO对象列表
     * @return 京东类目ID与ISC类目ID的映射关系，Map的key为京东类目ID，value为ISC类目ID
     */
    private Map<Long,Long> getJdCatAndIscCatMap(List<SkuVO> skuVOList){
        // 京东类目ID集合
        Set<Long> jdCatIds = Optional.ofNullable(skuVOList).orElseGet(ArrayList::new).stream()
                .filter(Objects::nonNull).map(SkuVO::getCatId).filter(Objects::nonNull).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(jdCatIds)) {
            return Maps.newHashMap();
        }

        List<CategoryPO> categoryPOList = categoryAtomicService.queryCategoryByIds(jdCatIds);
        return Optional.ofNullable(categoryPOList).orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(CategoryPO::getJdCatId, CategoryPO::getId,(o1,o2)-> o1));
    }

    /**
     * 获取跨境品上下架状态
     * @param skuVoList sku列表
     * @return 京东SKU ID和上下架状态的映射关系
     */
    @PFTracing
    public Map<Long,Integer> getJdSkuStatusMap(List<SkuVO> skuVoList){
        if (CollectionUtils.isEmpty(skuVoList)) {
            return Collections.emptyMap();
        }
        // 本土品直接跳过
        if (skuVoList.stream().filter(Objects::nonNull).noneMatch(skuVo-> CountryConstant.COUNTRY_ZH.equals(skuVo.getSourceCountryCode()))){
            return Collections.emptyMap();
        }

        List<Long> jdSkuIds = skuVoList.stream().filter(Objects::nonNull).map(SkuVO::getJdSkuId).collect(Collectors.toList());

        Map<Long, JdProductDTO> productDTOMap = skuInfoRpcService.querySkuMap(new JdProductQueryDTO(jdSkuIds));
        if (MapUtils.isEmpty(productDTOMap)) {
            return Collections.emptyMap();
        }

        Map<Long,Integer> resultMap = Maps.newHashMapWithExpectedSize(productDTOMap.size());
        productDTOMap.forEach((jdSkuId, productDTO)-> resultMap.put(jdSkuId, Integer.valueOf(productDTO.getSkuStatus())));
        log.info("查询跨境品JD SKU ID上下架状态，入参:[{}] 商品状态关系:[{}] ", JSON.toJSONString(jdSkuIds),JSON.toJSONString(resultMap));
        return resultMap;
    }
}
