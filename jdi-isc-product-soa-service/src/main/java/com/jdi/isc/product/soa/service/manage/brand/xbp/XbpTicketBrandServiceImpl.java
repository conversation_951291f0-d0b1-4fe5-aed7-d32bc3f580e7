package com.jdi.isc.product.soa.service.manage.brand.xbp;

import com.jd.xbp.jsf.api.request.ticket.CreateParam;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.common.constants.BrandConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.BrandAuthorizeEnum;
import com.jdi.isc.product.soa.common.enums.BrandLevelTypeEnum;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.domain.brand.biz.BrandCountryVO;
import com.jdi.isc.product.soa.domain.brand.biz.BrandLangVO;
import com.jdi.isc.product.soa.domain.brand.biz.BrandVO;
import com.jdi.isc.product.soa.rpc.xbp.XbpTicketRpcService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 品牌xbp服务
 * <AUTHOR>
 * @date 2024/7/30
 **/
@Slf4j
@Service
public class XbpTicketBrandServiceImpl implements XbpTicketBrandService{

    @Resource
    private XbpTicketRpcService xbpTicketRpcService;

    @Resource
    private CountryManageService countryManageService;

    @Resource
    private LangManageService langManageService;

    @Resource
    private OperDuccConfig operDuccConfig;

    @Override
    public DataResponse<Integer> createBrandXbpForApproval(BrandVO input) {
        BrandCountryVO brandCountry = input.getBrandCountry();
        CreateParam createParam = new CreateParam();
        createParam.setProcessId(operDuccConfig.getBrandCreateProcessorId(Objects.nonNull(brandCountry) && StringUtils.isNotBlank(brandCountry.getCountryCode()) ? brandCountry.getCountryCode() : CountryConstant.COUNTRY_ZH  ));
        createParam.setUsername(input.getUpdater());

        Map<String, Object> applicationInfo = new HashMap<>();
        applicationInfo.put(BrandConstant.JD_BRAND_ID,input.getJdBrandId());
        Map<String, String> countryMap = countryManageService.getCountryMap(null);
        if (Objects.nonNull(brandCountry)) {
            String countryCode = brandCountry.getCountryCode();
            applicationInfo.put(BrandConstant.REGISTER_COUNTRY,countryMap.get(countryCode));
            applicationInfo.put(BrandConstant.CROSS_BORDER_BRAND_EXPORT_AUTHORIZATION_SNAPSHOT,this.replaceBySemicolon(brandCountry.getAuthCert()));
            applicationInfo.put(BrandConstant.CROSS_BORDER_BRAND_EXPORT_AUTHORIZATION_METHOD, BrandAuthorizeEnum.fromValueToDesc(Integer.valueOf(brandCountry.getAuthType())));
            applicationInfo.put(BrandConstant.BRAND_AUTHORIZATION_EXPORT_COUNTRIES_AND_REGIONS, this.convertCountry(brandCountry.getAuthCountryCode(),countryMap));
            applicationInfo.put(BrandConstant.BRAND_AUTHORIZATION_VALIDITY, Objects.nonNull(brandCountry.getAuthDate()) ? DateUtil.formatDate(DateUtil.long2date(brandCountry.getAuthDate())) :"");
            applicationInfo.put(BrandConstant.BRAND_ACCESSIBLE_CATEGORIES, "");
            applicationInfo.put(BrandConstant.BRAND_TIER_TYPE, BrandLevelTypeEnum.fromValueToDesc(input.getLevelType()));
            applicationInfo.put(BrandConstant.BRAND_REGISTRATION_CERTIFICATE, this.replaceBySemicolon(brandCountry.getRegisterCert()));
            applicationInfo.put(BrandConstant.BRAND_HOMEPAGE_OR_DESCRIPTION, brandCountry.getDescription());
            applicationInfo.put(BrandConstant.BRAND_QUALIFICATION_CERTIFICATE, this.replaceBySemicolon(brandCountry.getQualificationCert()));
        }
        createParam.setApplicationInfo(applicationInfo);

        //多语言表格
        Map<String, CreateParam.TableInfo> tableInfoMap = new HashMap<>();
        CreateParam.TableInfo tableInfo = getCreateTableInfo(input);
        tableInfoMap.put(BrandConstant.MULTI_BRAND_NAME, tableInfo);
        createParam.setTableInfo(tableInfoMap);
        // 文件
        DataResponse<Integer> response = xbpTicketRpcService.create(createParam);
        return response;
    }

    @Override
    public DataResponse<Integer> modifyBrandXbpForApproval(BrandVO input,BrandVO originInput) {
        BrandCountryVO brandCountry = input.getBrandCountry();
        CreateParam createParam = new CreateParam();
        createParam.setProcessId(operDuccConfig.getBrandModifyProcessorId(Objects.nonNull(brandCountry) && StringUtils.isNotBlank(brandCountry.getCountryCode()) ? brandCountry.getCountryCode() : CountryConstant.COUNTRY_ZH  ));
        createParam.setUsername(input.getUpdater());

        Map<String, Object> applicationInfo = new HashMap<>();
        applicationInfo.put(BrandConstant.JD_BRAND_ID,input.getJdBrandId());
        Map<String, String> countryMap = countryManageService.getCountryMap(null);
        if (Objects.nonNull(brandCountry)) {
            String countryCode = brandCountry.getCountryCode();
            applicationInfo.put(BrandConstant.REGISTER_COUNTRY,countryMap.get(countryCode));
            applicationInfo.put(BrandConstant.CROSS_BORDER_BRAND_EXPORT_AUTHORIZATION_SNAPSHOT,this.replaceBySemicolon(brandCountry.getAuthCert()));
            applicationInfo.put(BrandConstant.CROSS_BORDER_BRAND_EXPORT_AUTHORIZATION_METHOD, BrandAuthorizeEnum.fromValueToDesc(Integer.valueOf(brandCountry.getAuthType())));
            applicationInfo.put(BrandConstant.BRAND_AUTHORIZATION_EXPORT_COUNTRIES_AND_REGIONS, this.convertCountry(brandCountry.getAuthCountryCode(),countryMap));
            applicationInfo.put(BrandConstant.BRAND_AUTHORIZATION_VALIDITY, Objects.nonNull(brandCountry.getAuthDate()) ? DateUtil.formatDate(DateUtil.long2date(brandCountry.getAuthDate())) :"");
            applicationInfo.put(BrandConstant.BRAND_ACCESSIBLE_CATEGORIES, "");
            applicationInfo.put(BrandConstant.BRAND_TIER_TYPE, BrandLevelTypeEnum.fromValueToDesc(input.getLevelType()));
            applicationInfo.put(BrandConstant.BRAND_REGISTRATION_CERTIFICATE, this.replaceBySemicolon(brandCountry.getRegisterCert()));
            applicationInfo.put(BrandConstant.BRAND_HOMEPAGE_OR_DESCRIPTION, brandCountry.getDescription());
            applicationInfo.put(BrandConstant.BRAND_QUALIFICATION_CERTIFICATE, this.replaceBySemicolon(brandCountry.getQualificationCert()));
        }

        // 修改信息列表
        Map<String, CreateParam.TableInfo> tableInfoMap = new HashMap<>();
        CreateParam.TableInfo tableInfo = getModifyTableInfo(input);
        tableInfoMap.put(BrandConstant.CHANGE_BRAND_INFO, tableInfo);
        createParam.setTableInfo(tableInfoMap);
        return null;
    }

    private CreateParam.TableInfo getCreateTableInfo(BrandVO input) {
        CreateParam.TableInfo  tableInfo = new CreateParam.TableInfo();
        List<Map<String, String>> data  = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(input.getLangList())) {
            for (BrandLangVO brandLangVO : input.getLangList()) {
                Map<String, String> langMap = new HashMap<>(2);
                langMap.put(BrandConstant.MULTI_BRAND_NAME_LANG,
                    langManageService.getLangNameByLangCode(brandLangVO.getLang(), LangContextHolder.get()));
                langMap.put(BrandConstant.MULTI_BRAND_LANG_NAME, brandLangVO.getLangName());
                data.add(langMap);
            }
        }
        tableInfo.setData(data);
        return tableInfo;
    }

    private CreateParam.TableInfo getModifyTableInfo(BrandVO input) {
        CreateParam.TableInfo  tableInfo = new CreateParam.TableInfo();
        List<Map<String, String>> data  = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(input.getLangList())) {
            for (BrandLangVO brandLangVO : input.getLangList()) {
                Map<String, String> langMap = new HashMap<>(2);
                langMap.put(BrandConstant.CHANGE_BRAND_TABLE_ONE,
                    langManageService.getLangNameByLangCode(brandLangVO.getLang(),LangContextHolder.get()));
                langMap.put(BrandConstant.CHANGE_BRAND_TABLE_ORIGIN, brandLangVO.getLangName());
                langMap.put(BrandConstant.CHANGE_BRAND_TABLE_TARGET, brandLangVO.getLangName());
                data.add(langMap);
            }
        }
        tableInfo.setData(data);
        return tableInfo;
    }

    /**
     * @function 将包含国家代码的字符串转换为对应的国家名称字符串
     * @param authCountryCode 待转换的含有国家代码的字符串
     * @param countryMap 国家代码与国家名称的映射关系表
     * @returns 转换后的国家名称字符串，国家名称间以逗号分隔
     */
    private String convertCountry(String authCountryCode,Map<String, String> countryMap){
        if (StringUtils.isBlank(authCountryCode)) {
            return "";
        }
       return Arrays.stream(authCountryCode.split(Constant.COMMA)).map(code -> countryMap.getOrDefault(code, "")).collect(Collectors.joining(Constant.COMMA));
    }

    /**
     * @function 将字符串中的逗号替换为分号
     * @param str [待处理的字符串]
     * @returns [逗号被替换为分号后的字符串，或者原始字符串（如果不包含逗号），若输入为空或仅包含空白字符，则返回空字符串]
     */
    private String replaceBySemicolon(String str){
        if (StringUtils.isBlank(str)) {
            return "";
        }

        return str.contains(Constant.COMMA) ? str.replace(Constant.COMMA,Constant.SEMICOLON) : str;
    }
}
