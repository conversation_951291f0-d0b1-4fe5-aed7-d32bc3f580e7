package com.jdi.isc.product.soa.service.manage.price.impl;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.domain.category.biz.CategoryPathVO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailPO;
import com.jdi.isc.product.soa.domain.price.biz.ProfitRateVO;
import com.jdi.isc.product.soa.domain.price.po.ProfitRatePO;
import com.jdi.isc.product.soa.service.atomic.price.ProfitRateAtomicService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.price.ProfitCalculateManageService;
import com.jdi.isc.product.soa.service.mapstruct.price.ProfitRateConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProfitCalculateManageServiceImpl implements ProfitCalculateManageService {

    @Resource
    private ProfitRateAtomicService profitRateAtomicService;
    @Resource
    private CategoryOutService categoryOutService;

    @Override
    public BigDecimal calculateSalePriceProfitRate(BigDecimal targetCurrencyPrice,BigDecimal countryCostPrice){
        log.info("ProfitCalculateManageServiceImpl.calculateSalePriceProfitRate targetCurrencyPrice:{},countryCostPrice:{}"
                , targetCurrencyPrice,countryCostPrice);
        if(targetCurrencyPrice == null
                || countryCostPrice == null){
            log.error("ProfitCalculateManageServiceImpl.calculateSalePriceProfitRate param is null");
            throw new BizException("利润率计算失败，参数为空");
        }
        if(BigDecimal.ZERO.compareTo(targetCurrencyPrice) == 0){
            log.error("ProfitCalculateManageServiceImpl.calculateSalePriceProfitRate targetCurrencyPrice is ZERO");
            throw new BizException("sku客制化未税销售价不可为0");
        }

        if(BigDecimal.ZERO.compareTo(countryCostPrice) == 0){
            log.error("ProfitCalculateManageServiceImpl.calculateSalePriceProfitRate countryCostPrice is ZERO");
            throw new BizException("国家成本价不可为0");
        }

        return BigDecimal.ONE.subtract(countryCostPrice.divide(targetCurrencyPrice,4, RoundingMode.HALF_UP));

    }

    @Override
    public BigDecimal calculateAgreementProfitRate(BigDecimal targetCurrencyPrice,BigDecimal countryCostPrice) {
        log.info("ProfitCalculateManageServiceImpl.calculateAgreementProfitRate targetCurrencyPrice:{},countryCostPrice:{}"
                , targetCurrencyPrice,countryCostPrice);
        if(targetCurrencyPrice == null
                || countryCostPrice == null){
            log.error("ProfitCalculateManageServiceImpl.calculateAgreementProfitRate param is null");
            throw new BizException("利润率计算失败，参数为空");
        }
        if(BigDecimal.ZERO.compareTo(targetCurrencyPrice) == 0){
            log.error("ProfitCalculateManageServiceImpl.calculateAgreementProfitRate targetCurrencyPrice is ZERO");
            throw new BizException("国家协议价不可为0");
        }

        if(BigDecimal.ZERO.compareTo(countryCostPrice) == 0){
            log.error("ProfitCalculateManageServiceImpl.calculateAgreementProfitRate countryCostPrice is ZERO");
            throw new BizException("国家成本价不可为0");
        }


        return BigDecimal.ONE.subtract(countryCostPrice.divide(targetCurrencyPrice,4, RoundingMode.HALF_UP));
    }

    @Override
    public ProfitRateVO getProfitLimitRate(Long catId, String sourceCountryCode, String targetCountryCode) {
        log.info("ProfitCalculateManageServiceImpl.getProfitLimitRate catId:{},sourceCountryCode:{},targetCountryCode:{}", catId,sourceCountryCode,targetCountryCode);
        Map<Long, CategoryPathVO> categoryPathVOMap = categoryOutService.queryPath(new HashSet<>(Collections.singletonList(catId)));
        CategoryPathVO categoryPathVO = categoryPathVOMap.get(catId);

        List<ProfitRatePO> profitRatePOList = profitRateAtomicService.list(sourceCountryCode,targetCountryCode
                ,categoryPathVO.getId1(),categoryPathVO.getId2(),categoryPathVO.getId3(),categoryPathVO.getId4());
        if(CollectionUtils.isEmpty(profitRatePOList)){
            log.info("ProfitCalculateManageServiceImpl.getProfitLimitRate profitRatePOList is null");
            return null;
        }
        Map<String,ProfitRatePO> profitRatePOMap = profitRatePOList.stream().sorted(Comparator.comparing(ProfitRatePO::getId))
                .collect(Collectors.toMap(item->String.join("_"
                ,String.valueOf(item.getFirstJdCatId()),String.valueOf(item.getSecondJdCatId()),String.valueOf(item.getThirdJdCatId())
                ,String.valueOf(item.getLastJdCatId())), Function.identity(),(v1,v2)->v2));
//        log.info("ProfitCalculateManageServiceImpl.getProfitLimitRate profitRatePOMap:{}", JSONObject.toJSONString(profitRatePOMap));
        String lastKey = String.join("_",String.valueOf(categoryPathVO.getId1())
                ,String.valueOf(categoryPathVO.getId2()),String.valueOf(categoryPathVO.getId3()),String.valueOf(categoryPathVO.getId4()));
        ProfitRatePO profitRatePO = profitRatePOMap.get(lastKey);
        if(profitRatePO != null){
            return ProfitRateConvert.INSTANCE.po2Vo(profitRatePO);
        }
        String nullKey = "null";
        String thirdKey = String.join("_",String.valueOf(categoryPathVO.getId1())
                ,String.valueOf(categoryPathVO.getId2()),String.valueOf(categoryPathVO.getId3()),nullKey);
        ProfitRatePO thirdPO = profitRatePOMap.get(thirdKey);
        if(thirdPO != null){
            return ProfitRateConvert.INSTANCE.po2Vo(thirdPO);
        }

        String secondKey = String.join("_",String.valueOf(categoryPathVO.getId1())
                ,String.valueOf(categoryPathVO.getId2()),nullKey,nullKey);
        ProfitRatePO secondPO = profitRatePOMap.get(secondKey);
        if(secondPO != null){
            return ProfitRateConvert.INSTANCE.po2Vo(secondPO);
        }

        String firstKey = String.join("_",String.valueOf(categoryPathVO.getId1())
                ,nullKey,nullKey,nullKey);
        ProfitRatePO firstPO = profitRatePOMap.get(firstKey);
        if(firstPO != null){
            return ProfitRateConvert.INSTANCE.po2Vo(firstPO);
        }

        return null;
    }

    @Override
    public ProfitRateVO matchProfitLimitRate(Long lastJdCatId, String sourceCountryCode, String targetCountryCode) {
        log.info("ProfitCalculateManageServiceImpl.matchProfitLimitRate catId:{},sourceCountryCode:{},targetCountryCode:{}", lastJdCatId, sourceCountryCode, targetCountryCode);
        if (lastJdCatId == null || StringUtils.isAnyEmpty(sourceCountryCode, targetCountryCode)) {
            log.info("ProfitCalculateManageServiceImpl.matchProfitLimitRate param is null, catId:{},sourceCountryCode:{},targetCountryCode:{}", lastJdCatId, sourceCountryCode, targetCountryCode);
            return null;
        }
        Map<Long, CategoryPathVO> categoryPathVOMap = categoryOutService.queryPath(new HashSet<>(Collections.singletonList(lastJdCatId)));
        CategoryPathVO categoryPathVO = categoryPathVOMap.get(lastJdCatId);

        if (categoryPathVO == null) {
            log.warn("找不到类目信息, catId:{}", lastJdCatId);
            return null;
        }

        Long id1 = categoryPathVO.getId1();
        Long id2 = categoryPathVO.getId2();
        Long id3 = categoryPathVO.getId3();
        Long id4 = categoryPathVO.getId4();

        // 4级类目
        if (id4 != null) {
            ProfitRatePO profitRate = profitRateAtomicService.getOne(sourceCountryCode, targetCountryCode, id1, id2, id3, id4);
            if (profitRate != null) {
                log.info("matchProfitLimitRate, 匹配到4级类目利润率, catId:{},sourceCountryCode:{},targetCountryCode:{}", lastJdCatId, sourceCountryCode, targetCountryCode);
                return ProfitRateConvert.INSTANCE.po2Vo(profitRate);
            } else {
                log.info("ProfitCalculateManageServiceImpl.matchProfitLimitRate by id4 profitRate is null, catId:{},sourceCountryCode:{},targetCountryCode:{}", lastJdCatId, sourceCountryCode, targetCountryCode);
            }
        }

        // 3级类目
        if (id3 != null) {
            ProfitRatePO profitRate = profitRateAtomicService.getOne(sourceCountryCode, targetCountryCode, id1, id2, id3, null);
            if (profitRate != null) {
                log.info("matchProfitLimitRate, 匹配到3级类目利润率, catId:{},sourceCountryCode:{},targetCountryCode:{}", lastJdCatId, sourceCountryCode, targetCountryCode);
                return ProfitRateConvert.INSTANCE.po2Vo(profitRate);
            } else {
                log.info("ProfitCalculateManageServiceImpl.matchProfitLimitRate by id3 profitRate is null, catId:{},sourceCountryCode:{},targetCountryCode:{}", lastJdCatId, sourceCountryCode, targetCountryCode);
            }
        }
        // 2级类目
        if (id2 != null) {
            ProfitRatePO profitRate = profitRateAtomicService.getOne(sourceCountryCode, targetCountryCode, id1, id2, null, null);
            if (profitRate != null) {
                log.info("matchProfitLimitRate, 匹配到2级类目利润率, catId:{},sourceCountryCode:{},targetCountryCode:{}", lastJdCatId, sourceCountryCode, targetCountryCode);
                return ProfitRateConvert.INSTANCE.po2Vo(profitRate);
            } else {
                log.info("ProfitCalculateManageServiceImpl.matchProfitLimitRate by id2 profitRate is null, catId:{},sourceCountryCode:{},targetCountryCode:{}", lastJdCatId, sourceCountryCode, targetCountryCode);
            }
        }
        // 1级类目
        if (id1 != null) {
            ProfitRatePO profitRate = profitRateAtomicService.getOne(sourceCountryCode, targetCountryCode, id1, null, null, null);
            if (profitRate != null) {
                log.info("matchProfitLimitRate, 匹配到1级类目利润率, catId:{},sourceCountryCode:{},targetCountryCode:{}", lastJdCatId, sourceCountryCode, targetCountryCode);
                return ProfitRateConvert.INSTANCE.po2Vo(profitRate);
            } else {
                log.info("ProfitCalculateManageServiceImpl.matchProfitLimitRate by id1 profitRate is null, catId:{},sourceCountryCode:{},targetCountryCode:{}", lastJdCatId, sourceCountryCode, targetCountryCode);
            }
        }

        log.info("ProfitCalculateManageServiceImpl.matchProfitLimitRate math fail, catId:{},sourceCountryCode:{},targetCountryCode:{}", lastJdCatId, sourceCountryCode, targetCountryCode);
        return null;
    }

    @Override
    public DataResponse<Boolean> checkProfitRate(ProfitRateVO profitRate) {

        if (profitRate == null) {
            log.warn("利润率阈值未设置");
            return DataResponse.error("利润率阈值未设置");
        }
        if (profitRate.getProfitRate() == null) {
            log.warn("未配置利润率阈值, id={}", profitRate.getId());
            return DataResponse.error(String.format("未配置利润率阈值. %s", profitRate.getId()));
        }

        if (profitRate.getLowProfitRate() == null) {
            log.warn("未配置超低利润率阈值, id={}", profitRate.getId());
            return DataResponse.error(String.format("未配置超低利润率阈值. %s", profitRate.getId()));
        }

        return DataResponse.success();
    }
}
