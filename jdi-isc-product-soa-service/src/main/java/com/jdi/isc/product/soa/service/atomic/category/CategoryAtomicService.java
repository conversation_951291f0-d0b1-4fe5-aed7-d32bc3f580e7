package com.jdi.isc.product.soa.service.atomic.category;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.util.IscCollectUtils;
import com.jdi.isc.product.soa.domain.category.biz.CategoryComboBoxReqVO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryComboBoxVO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryPathVO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryQueryPageVO;
import com.jdi.isc.product.soa.domain.category.po.CategoryPO;
import com.jdi.isc.product.soa.domain.enums.StatusEnum;
import com.jdi.isc.product.soa.repository.mapper.category.CategoryBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 类目原子服务
 * <AUTHOR>
 * @date 20231109
 **/
@Service
@Slf4j
public class CategoryAtomicService extends ServiceImpl<CategoryBaseMapper, CategoryPO> {

    /**
     * 列表查询
     * @param offset 偏移量
     * @param pageSize 分页大小
     * @param reqVO 查询条件
     * @return
     */
    public List<CategoryQueryPageVO.Response> listSearch(long offset, long pageSize, CategoryQueryPageVO.Request reqVO){
        return super.getBaseMapper().listSearch(offset, pageSize, reqVO);
    }

    /**
     * 列表总数
     * @param reqVO 查询条件
     * @return
     */
    public long listSearchTotal(CategoryQueryPageVO.Request reqVO){
        return super.getBaseMapper().listSearchTotal(reqVO);
    }


    /**
     * 根据子类目id查询类目路径
     * @param catIds 类目ID集合
     * @return 类目路径
     */
    @PFTracing
    public List<CategoryPathVO> path(Set<Long> catIds,Integer status){
        return super.getBaseMapper().path(catIds,status);
    }

    /**
     * 根据子类目id查询类目路径
     * @param catIds 类目ID集合
     * @return 类目路径
     */
    public List<CategoryPathVO> pathByStatus(Set<Long> catIds,Integer status){
        return super.getBaseMapper().pathByStatus(catIds,status);
    }


    /**
     * 根据状态和ERP过滤条件获取正式类目路径。
     * @param catIds 类目ID集合
     * @param status 状态
     * @param filterErp ERP过滤条件集合
     * @return 类目路径VO列表
     */
    public List<CategoryPathVO> pathByStatusFormalCat(Set<Long> catIds,Integer status,List<String> filterErp){
        return super.getBaseMapper().pathByStatusFormalCat(catIds,status,filterErp);
    }

    /**
     * 根据子类目id查询类目路径
     * @return 类目路径
     */
    public List<CategoryPathVO> pathBySizeStatus(Long index,Long size,Integer status){
        Long offset = null;
        if(index != null && size != null){
            offset = (index - 1) * size;
        }
        return super.getBaseMapper().pathBySizeStatus(offset,size,status);
    }

    /**
     * 查2，3，4级类目下的4级类目id集合
     * @param catId 类目ID
     * @return 4级类目id
     */
    public List<Long> categoryLevel4From1234Set(Set<Long> catIds){
        return super.getBaseMapper().categoryLevel4From1234Set(catIds);
    }

    /**
     * 查某个类目下的4级类目id集合
     * @param catId 类目ID
     * @return 4级类目id
     */
    public List<Long> categoryLevel4From1234(Long catId){
        return super.getBaseMapper().categoryLevel4From1234(catId);
    }

    /**
     * 查询子类目，级联使用
     * @param input 参数
     * @return
     */
    public List<CategoryComboBoxVO> children(CategoryComboBoxReqVO input){
        return super.getBaseMapper().children(input);
    }

    /**
     * 获取有效未删除的类目
     * @param id 类目id
     * @return
     */
    @PFTracing
    public CategoryPO getValidPOById(Long id){
        LambdaQueryWrapper<CategoryPO> wrapper = Wrappers.<CategoryPO>lambdaQuery()
                .eq(CategoryPO::getJdCatId,id).eq(CategoryPO::getYn, YnEnum.YES.getCode());
        List<CategoryPO> list = super.list(wrapper);
        if (CollectionUtils.isEmpty(list)){
            return null;
        } else {
            return list.get(0);
        }
    }

    public List<CategoryPO> queryChildByIds(Set<Long> parentId){
        LambdaQueryWrapper<CategoryPO> wrapper = Wrappers.<CategoryPO>lambdaQuery()
                .in(CategoryPO::getJdParentCatId,parentId)
                .eq(CategoryPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    public List<CategoryPO> queryCategoryByIds(Set<Long> catIds){
        LambdaQueryWrapper<CategoryPO> wrapper = Wrappers.<CategoryPO>lambdaQuery()
                .in(CategoryPO::getJdCatId,catIds)
                .eq(CategoryPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    public List<CategoryPO> queryCategorySimpleInfoByIds(Set<Long> catIds){
        List<CategoryPO> result = new ArrayList<>();
        // 分批查询
        List<Set<Long>> partition = IscCollectUtils.partition(catIds, 1000);
        for(Set<Long> partCatIds : partition){
            LambdaQueryWrapper<CategoryPO> wrapper = Wrappers.<CategoryPO>lambdaQuery()
                    .select(CategoryPO::getId,CategoryPO::getParentCatId,CategoryPO::getJdCatId,CategoryPO::getJdParentCatId)
                    .in(CategoryPO::getJdCatId,partCatIds)
                    .eq(CategoryPO::getYn, YnEnum.YES.getCode());

            result.addAll(super.list(wrapper));
        }
        return result;
    }


    /**
     * 查询所有启用的分类
     * @return 启用的分类列表
     */
    public List<CategoryPO> queryAllCategory(){
        LambdaQueryWrapper<CategoryPO> wrapper = Wrappers.<CategoryPO>lambdaQuery()
            .eq(CategoryPO::getStatus, StatusEnum.ENABLE.getCode())
            .eq(CategoryPO::getYn, YnEnum.YES.getCode());
        List<CategoryPO> list = super.list(wrapper);
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list;
    }


    /** 查询零售类目Id不为空的所有类目*/
    public List<CategoryPO> queryAllRetailCategory(Set<Long> cateSet){
        LambdaQueryWrapper<CategoryPO> wrapper = Wrappers.<CategoryPO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(cateSet),CategoryPO::getJdCatId, cateSet)
                .eq(CategoryPO::getStatus, StatusEnum.ENABLE.getCode())
                .eq(CategoryPO::getYn, YnEnum.YES.getCode())
                .isNotNull(CategoryPO::getJdCatId);
        return super.list(wrapper);
    }

    /**
     * 根据京东类目id查询国际类目id
     * @param jdCatId
     * @return
     */
    public CategoryPO queryByJdCatId(Long jdCatId){
        if(jdCatId==null){
            return null;
        }
        LambdaQueryWrapper<CategoryPO> wrapper = Wrappers.<CategoryPO>lambdaQuery()
                .eq(CategoryPO::getJdCatId, jdCatId)
                .eq(CategoryPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    /**
     * 根据京东类目ID查询类目信息
     * @param jdCatId 京东类目ID
     * @return 类目信息
     */
    public CategoryPO getCategoryByJdCatId(Long jdCatId) {
        LambdaQueryWrapper<CategoryPO> wrapper = Wrappers.<CategoryPO>lambdaQuery()
                .eq(CategoryPO::getJdCatId, jdCatId)
                .eq(CategoryPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }


    /**
     * 根据京东类目ID集合查询对应的类目信息，并返回一个以京东类目ID为键的Map。
     * @param jdcCatIds 京东类目ID集合
     * @return 以京东类目ID为键的类目信息Map
     */
    public Map<Long, CategoryPO> queryCategoryMapByJdCatIds(Set<Long> jdcCatIds) {
        LambdaQueryWrapper<CategoryPO> wrapper = Wrappers.<CategoryPO>lambdaQuery()
                .in(CategoryPO::getJdCatId, jdcCatIds).eq(CategoryPO::getYn, YnEnum.YES.getCode());
        List<CategoryPO> list = super.list(wrapper);
        return Optional.ofNullable(list)
                .orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(CategoryPO::getJdCatId, Function.identity(), (o1, o2) -> o1));
    }


    /**
     * 查询所有有效的分类列表
     * @return 分类列表
     */
    public List<CategoryPO> queryAllCategoryList() {
        LambdaQueryWrapper<CategoryPO> wrapper = Wrappers.<CategoryPO>lambdaQuery()
                .eq(CategoryPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    /**
     * 根据父类别ID查询所有子类别
     * @param parentId 父类别ID
     * @return 子类别列表
     */
    public List<CategoryPO> queryAllChildById(Long parentId) {
        List<CategoryPO> results = new ArrayList<>();
        if (parentId == null) {
            return results;
        }
        fetchAllChildren(parentId, results);
        return results;
    }

    /**
     * 递归获取所有子类别
     * @param parentId 当前父类别ID
     * @param results  结果列表，用于累积所有子类别
     */
    private void fetchAllChildren(Long parentId, List<CategoryPO> results) {
        List<CategoryPO> children = this.queryChildById(parentId);
        if (CollectionUtils.isNotEmpty(children)) {
            results.addAll(children);
            for (CategoryPO child : children) {
                fetchAllChildren(child.getId(), results);  // 递归调用
            }
        }
    }

    /**
     * 根据父类别ID查询次级子类别
     * @param parentId 父类别ID
     * @return 子类别列表
     */
    private List<CategoryPO> queryChildById(Long parentId){
        if(parentId == null){
            return null;
        }
        LambdaQueryWrapper<CategoryPO> wrapper = Wrappers.<CategoryPO>lambdaQuery()
                .eq(CategoryPO::getParentCatId,parentId)
                .eq(CategoryPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }
}
