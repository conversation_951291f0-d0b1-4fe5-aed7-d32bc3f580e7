package com.jdi.isc.product.soa.service.atomic.mku;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.search.*;
import com.alibaba.fastjson.JSONObject;
import com.jd.fastjson.JSON;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.EsAtomicSupportService;
import com.jdi.isc.product.soa.domain.common.biz.SortOption;
import com.jdi.isc.product.soa.domain.mku.po.es.MkuEsPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @createDate 2024-06-26 17:24:53
 */
@Component
@Slf4j
public class MkuEsAtomicService extends EsAtomicSupportService<MkuEsPO> {

    @Resource
    private ElasticsearchClient elasticsearchClient;

    // 定义一个正则表达式模式来匹配中文字符
    private static final Pattern CHINESE_PATTERN = Pattern.compile("[\\u4e00-\\u9fa5]");

    // 定义一个正则表达式模式来匹配英文字符
    private static final Pattern ENGLISH_PATTERN = Pattern.compile("^[A-Za-z\\s.,!?]+$");

    /**
     * 查询
     * */
    public SearchResponse<MkuEsPO> search(List<String> keywords, List<Long> catIds,String clientCode
            , String indexName, Integer index, Integer size,Set<Integer> featureTags, Integer stockFlag, String langCode) {

        // 构建请求体
        SearchRequest.Builder requestBuilder = this.buildSearchRequest(indexName);
        // 返回真实数量
        requestBuilder.trackTotalHits(TrackHits.of(tr->tr.enabled(Boolean.TRUE)));

        // 过滤条件
        BoolQuery query = this.buildSearchQuery(keywords,catIds,clientCode,featureTags, stockFlag);
        requestBuilder.query(q->q.bool(query));

        // 设置排序
        this.setSearchSort(requestBuilder);

        requestBuilder.source(SourceConfig.of(sc -> sc
                .filter(SourceFilter.of(sf -> sf
                        .includes("mku_id","feature_tags")
                ))
        ));

        // 避免深度分页
        super.setPage(requestBuilder,index,size);

//        // 折叠功能
//        requestBuilder.collapse(c -> c
//                .field("agg_key")
//                .innerHits(ih -> ih.name("agg_key_hits").size(20)) // 设置inner_hits的名称和大小
//        );

        SearchResponse<MkuEsPO> response = null;
        try {
            SearchRequest searchRequest = requestBuilder.build();
            log.info("MkuEsAtomicService.search request:{}", super.getRequestBody(searchRequest));
            response = elasticsearchClient.search(searchRequest,MkuEsPO.class);
            log.info("Total Hits: " + response.hits().total().value());
        } catch (IOException e) {
            log.error("【系统异常】MkuEsAtomicService.search error",e);
        }
        return response;
    }

    public SearchResponse<MkuEsPO> attempt(List<String> keywords,List<Long> catIds, String clientCode
            ,String lang, String indexName, Integer size){

        String paramField = "mku_title";
        if(LangConstant.LANG_ZH.equals(lang) || LangConstant.LANG_EN.equals(lang)){
            paramField = paramField + "_" + lang;
        }

        // 过滤条件
        SearchRequest.Builder requestBuilder = this.buildSearchRequest(indexName);
        BoolQuery query = this.buildAttemptSearchQuery(keywords,paramField,catIds,clientCode);
        requestBuilder.query(q->q.bool(query));

        requestBuilder.source(SourceConfig.of(sc -> sc
                .filter(SourceFilter.of(sf -> sf
                        .includes("mku_id","mku_title_zh","mku_title_en","mku_title")
                ))
        ));

        // 避免深度分页
        super.setPage(requestBuilder,1,size);

        // 构建提示语
//        Suggester suggester = this.buildSuggest(paramField,keyword,size);
//        requestBuilder.suggest(suggester);

        try {
            SearchRequest searchRequest = requestBuilder.build();
            log.info("MkuEsAtomicService.attempt request:{}", super.getRequestBody(searchRequest));
            SearchResponse<MkuEsPO> response = elasticsearchClient.search(searchRequest, MkuEsPO.class);
//            return this.parseSuggestResponse(response);
            log.info("Total Hits: " + response.hits().total().value());
            return response;
        } catch (IOException e) {
            log.error("【系统异常】MkuEsAtomicService.attempt error", e);
        }

        return null;


    }

    /**
     * 根据mkuId获取es详情
     * */
    public MkuEsPO queryById(String indexName, String id){
        if(StringUtils.isBlank(indexName) || StringUtils.isBlank(id)){
            log.error("MkuEsService.queryByMkuId indexName is null or id is null");
            return null;
        }

        try{

            GetRequest getRequest = GetRequest.of(g -> g
                    .index(indexName)
                    .id(id)
            );

            GetResponse<MkuEsPO> response = elasticsearchClient.get(getRequest,MkuEsPO.class);
            return response.source();
        } catch (Exception e){
            log.error("【系统异常】MkuEsService.queryByMkuId error index:{},id:{}",indexName,id,e);
        }
        return null;
    }

    /**
     * 新增
     * */
    public Boolean upsert(String indexName, MkuEsPO mkuEsPO){
        if(StringUtils.isBlank(indexName)){
            log.error("MkuEsService.upsert indexName is null");
            return Boolean.FALSE;
        }
        try {
            IndexRequest<MkuEsPO> indexRequest = IndexRequest.of(g->g
                    .index(indexName)
                    .id(mkuEsPO.getId())
                    .document(mkuEsPO));
            elasticsearchClient.index(indexRequest);
        } catch (IOException e) {
            log.error("【系统异常】es插入文档失败",e);
        }
        return Boolean.TRUE;
    }

    /**
     * 删除
     * */
    public Boolean delete(String indexName, String id){
        if(StringUtils.isBlank(indexName) || StringUtils.isBlank(id)){
            log.error("MkuEsService.queryByMkuId indexName is null or id is null");
            return null;
        }
        MkuEsPO esPO = this.queryById(indexName,id);
        if(esPO == null){
            log.error("MkuEsService.queryByMkuId esPO is null");
            return null;
        }
        esPO.setYn(YnEnum.NO.getCode());
        esPO.setUpdateTime(new Date().getTime());

        return this.upsert(indexName,esPO);
    }

    /**
     * 删除,真删了
     * */
    public Boolean deleteDocument(String indexName, String id){
        if(StringUtils.isBlank(indexName)){
            log.error("MkuEsService.deleteDocument indexName is null");
            return Boolean.FALSE;
        }
        try {
            DeleteRequest indexRequest = DeleteRequest.of(g->g
                    .index(indexName)
                    .id(id));
            elasticsearchClient.delete(indexRequest);
        } catch (IOException e) {
            log.error("es删除文档失败",e);
        }
        return Boolean.TRUE;
    }

    /**
     * 构建分页查询条件
     * */
    private BoolQuery buildSearchQuery(List<String> keywords, List<Long> catIds, String clientCode, Set<Integer> featureTags, Integer stockFlag){

        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        this.setBase(clientCode,boolQueryBuilder);

        this.setCategory(catIds,boolQueryBuilder);

        this.setPreWord(keywords,boolQueryBuilder);

        this.setFeatureTags(featureTags,boolQueryBuilder);

        this.setStockFlag(stockFlag, boolQueryBuilder);

        return boolQueryBuilder.build();
    }

    private void setFeatureTags(Set<Integer> featureTags, BoolQuery.Builder boolQueryBuilder) {
        if (featureTags == null || featureTags.isEmpty()) {
            return;
        }

        // 使用terms查询来匹配feature_tags字段中的任意一个值
        boolQueryBuilder.must(m -> m.terms(TermsQuery.of(tq -> tq
                .field("feature_tags")
                .terms(p -> p.value(featureTags.stream()
                        .map(FieldValue::of)
                        .collect(Collectors.toList())))
        )));
    }

    /**
     * 构建联想词查询条件
     * */
    private BoolQuery buildAttemptSearchQuery(List<String> keywords,String paramField,List<Long> catIds
        ,String clientCode){

        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        this.setBase(clientCode,boolQueryBuilder);

        this.setCategory(catIds,boolQueryBuilder);

        this.setAssociate(keywords,paramField,boolQueryBuilder);

        return boolQueryBuilder.build();
    }

    /**
     * 设置排序
     */
    private void setSearchSort(SearchRequest.Builder requestBuilder) {
        // 排序条件
        SortOption sortOption = this.buildSearchSort("has_stock",SortOrder.Desc.jsonValue());
        SortOption sortScoreOption = this.buildSearchSort("_score",SortOrder.Desc.jsonValue());
        if(sortOption != null){
            requestBuilder.sort(sort -> sort.field(f -> f
                    .field(sortOption.getField())
                    .order(sortOption.getOrder())));
        }
        if(sortScoreOption != null){
            requestBuilder.sort(sort -> sort.field(f -> f
                    .field(sortScoreOption.getField())
                    .order(sortScoreOption.getOrder())));
        }
    }

    /**
     * 排序条件
     */
    private SortOption buildSearchSort(String orderField, String orderType) {
        if (StringUtils.isBlank(orderField)) {
            return null;
        }

        SortOrder sortOrder; // 默认排序顺序

        // 添加排序顺序
        if (StringUtils.equals(SortOrder.Desc.jsonValue(), orderType)) {
            sortOrder = SortOrder.Desc;
        } else {
            sortOrder = SortOrder.Asc;
        }

        SortOption sortOption = new SortOption(orderField, sortOrder);
        return sortOption;
    }


    /**
     * 构建请求体
     * */
    private SearchRequest.Builder buildSearchRequest(String indexName){
        SearchRequest.Builder searchRequestBuilder = new SearchRequest.Builder()
                .index(indexName);
        return searchRequestBuilder;
    }

    /**
     * 过滤出包含中文字符的字符串
     *
     * @param inputList 输入的字符串列表
     * @return 包含中文字符的字符串列表
     */
    public List<String> filterChineseStrings(List<String> inputList) {
        List<String> chineseStrings = new ArrayList<>();
        for (String str : inputList) {
            if (this.containsChinese(str)) {
                chineseStrings.add(str);
            }
        }
        return chineseStrings;
    }

    /**
     * 判断是否中文
     * */
    public boolean containsChinese(String str) {
        if(StringUtils.isBlank(str)){
            return Boolean.FALSE;
        }
        return CHINESE_PATTERN.matcher(str).find();
    }

    /**
     * 判断是否英文
     * */
    public boolean containsEnglish(String str) {
        if(StringUtils.isBlank(str)){
            return Boolean.FALSE;
        }
        return ENGLISH_PATTERN.matcher(str).find();
    }

    /**
     * @function 构建一个建议器对象，用于完成建议查询
     * @param field 字段名称，建议查询将基于此字段进行
     * @param keyword 关键字，建议查询将基于此关键字进行匹配
     * @param size 返回的建议数量
     * @returns Suggester 对象，包含构建的建议查询
     */
    private Suggester buildSuggest(String field, String keyword, Integer size) {
        return Suggester.of(su->su
                .suggesters("my_suggestions",FieldSuggester.of(fs->fs.completion(cs->cs
                        .field(field + ".suggest")
                        .skipDuplicates(Boolean.TRUE)
                        .size(size))))
                .text(keyword)
        );
    }

    /**
     * @function 设置基础过滤条件
     * @param clientCode 客户代码，用于过滤条件
     * @param boolQueryBuilder 构建布尔查询的构建器对象
     * @returns [无返回值]
     */
    private void setBase(String clientCode,BoolQuery.Builder boolQueryBuilder){
        boolQueryBuilder.filter(f -> f.term(TermQuery.of(tq -> tq
                .field("yn")
                .value(YnEnum.YES.getCode())
        )));
        boolQueryBuilder.filter(f -> f.term(TermQuery.of(tq -> tq
                .field("client_code")
                .value(clientCode)
        )));
    }

    /**
     * 根据库存标志设置布尔查询的过滤条件
     *
     * @param stockFlag        库存标志值，用于筛选是否有库存
     * @param boolQueryBuilder 布尔查询构建器，用于添加过滤条件
     */
    private void setStockFlag(Integer stockFlag, BoolQuery.Builder boolQueryBuilder) {
        if (stockFlag != null) {
            boolQueryBuilder.filter(f -> f.term(TermQuery.of(tq -> tq
                    .field("has_stock")
                    .value(stockFlag)
            )));
        }
    }

    /**
     * @function 设置分类过滤条件
     * @param catIds 分类ID列表，如果为空则不进行任何操作
     * @param boolQueryBuilder 用于构建布尔查询的构建器
     * @returns [无返回值]
     */
    private void setCategory(List<Long> catIds,BoolQuery.Builder boolQueryBuilder){
        if(CollectionUtils.isEmpty(catIds)){
            return;
        }
        boolQueryBuilder.must(m -> m.terms(TermsQuery.of(tq -> tq
                //.field("cat_id")
                .field("jd_cat_id")
                .terms(p->p.value(catIds.stream()
                        .map(FieldValue::of)
                        .collect(Collectors.toList())))
        )));
    }

    private void setAssociate(List<String> keywords,String paramField,BoolQuery.Builder boolQueryBuilder){
        if(CollectionUtils.isEmpty(keywords)){
            return;
        }

        BoolQuery.Builder childBuilder = new BoolQuery.Builder();
        List<String> chineseStrings = this.filterChineseStrings(keywords);
        if(CollectionUtils.isNotEmpty(chineseStrings)){

            Query otherQuery = MatchQuery.of(m -> m
                    .field("pre_word.ik")
                    .query(String.join(" ",chineseStrings))
                    .boost(0.9F)
            )._toQuery();
            childBuilder.should(otherQuery);
        }

        Query otherQuery = MatchQuery.of(t -> t
                .field("pre_word.standard")
                .query(String.join(" ",keywords))
                .boost(1.5F)
        )._toQuery();
        childBuilder.should(otherQuery);

        Query query = MatchQuery.of(m -> m
                    .field(paramField)
                    .query(String.join(" ",keywords))
                    .boost(1.2F)
            )._toQuery();
        childBuilder.should(query);
        boolQueryBuilder.must(childBuilder.build()._toQuery());
    }

    /**
     * @function 构建并设置用于查询的布尔查询对象
     * @param keywords 包含关键词的列表，用于生成查询条件
     * @param boolQueryBuilder 用于构建布尔查询的构建器对象
     * @returns [无返回值]
     */
    private void setPreWord(List<String> keywords, BoolQuery.Builder boolQueryBuilder) {
        if (CollectionUtils.isEmpty(keywords)) {
            return;
        }
        List<String> chineseStrings = this.filterChineseStrings(keywords);

        // 标题 must 命中
        if (CollectionUtils.isNotEmpty(chineseStrings)) {
            Query titleIkQuery = MatchQuery.of(m -> m
                    .field("pre_word_title.ik")
                    .query(String.join(" ", chineseStrings))
                    .boost(10F)
            )._toQuery();
            boolQueryBuilder.must(titleIkQuery);
        }

        Query titleStdQuery = MatchQuery.of(t -> t
                .field("pre_word_title.standard")
                .query(String.join(" ", keywords))
                .boost(8F)
        )._toQuery();
        boolQueryBuilder.must(titleStdQuery);

        // 品牌、类目等 should 加分
        if (CollectionUtils.isNotEmpty(chineseStrings)) {
            Query categoryIkQuery = MatchQuery.of(m -> m
                    .field("pre_word_category.ik")
                    .query(String.join(" ", chineseStrings))
                    .boost(7F)
            )._toQuery();
            boolQueryBuilder.should(categoryIkQuery);

            Query brandIkQuery = MatchQuery.of(m -> m
                    .field("pre_word_brand.ik")
                    .query(String.join(" ", chineseStrings))
                    .boost(5F)
            )._toQuery();
            boolQueryBuilder.should(brandIkQuery);

            Query otherIkQuery = MatchQuery.of(m -> m
                    .field("pre_word_other.ik")
                    .query(String.join(" ", chineseStrings))
                    .boost(3F)
            )._toQuery();
            boolQueryBuilder.should(otherIkQuery);
        }

        Query categoryStdQuery = MatchQuery.of(t -> t
                .field("pre_word_category.standard")
                .query(String.join(" ", keywords))
                .boost(6F)
        )._toQuery();
        boolQueryBuilder.should(categoryStdQuery);

        Query brandStdQuery = MatchQuery.of(t -> t
                .field("pre_word_brand.standard")
                .query(String.join(" ", keywords))
                .boost(4F)
        )._toQuery();
        boolQueryBuilder.should(brandStdQuery);

        Query otherStdQuery = MatchQuery.of(t -> t
                .field("pre_word_other.standard")
                .query(String.join(" ", keywords))
                .boost(2F)
        )._toQuery();
        boolQueryBuilder.should(otherStdQuery);
    }


    public List<MkuEsPO> queryByMkuId(String indexName, Long mkuId) {
        List<MkuEsPO> resultList = new ArrayList<>();

        if (indexName == null || indexName.isEmpty() || mkuId == null) {
            log.error("MkuEsService.queryByMkuId indexName is null or mkuId is null");
            return resultList;
        }

        try {
            SearchRequest searchRequest = SearchRequest.of(s -> s
                    .index(indexName)
                    .query(q -> q
                            .bool(b -> b
                                    .must(m -> m
                                            .term(t -> t
                                                    .field("mku_id")
                                                    .value(mkuId)
                                            )
                                    )
                                    .must(m -> m
                                            .term(t -> t
                                                    .field("yn")
                                                    .value(1)
                                            )
                                    )
                            )
                    )
            );

            SearchResponse<MkuEsPO> response = elasticsearchClient.search(searchRequest, MkuEsPO.class);

            for (Hit<MkuEsPO> hit : response.hits().hits()) {
                resultList.add(hit.source());
            }
        } catch (Exception e) {
            log.error("MkuEsService.queryByMkuId error es-index:{}, mkuId:{},e", indexName, mkuId, e);
        }

        return resultList;
    }


    public Boolean updateFeatureTags(String indexName, MkuEsPO mkuEsPO) {
        if (indexName == null || indexName.isEmpty() || Objects.isNull(mkuEsPO) || Objects.isNull(mkuEsPO.getId())) {
            log.error("MkuEsService.updateFeatureTags indexName or id is null");
            return Boolean.FALSE;
        }
        boolean result =false;
        try {
            Map<String, Object> updateDoc = new HashMap<>();
            updateDoc.put("feature_tags", mkuEsPO.getFeatureTags());
            UpdateRequest<MkuEsPO, Map<String, Object>> updateRequest = UpdateRequest.of(u -> u
                    .index(indexName)
                    .id(mkuEsPO.getId())
                    .doc(updateDoc)
            );

            UpdateResponse<MkuEsPO> updateResponse = elasticsearchClient.update(updateRequest, MkuEsPO.class);

            result = updateResponse.result().name().equalsIgnoreCase("UPDATED");
            return result;
        } catch (IOException e) {
            log.error("updateFeatureTags es更新文档失败，param:{},index:{},e:", JSON.toJSONString(mkuEsPO),indexName,e);
        }finally {
            log.error("updateFeatureTags param:{},index:{},result:{}",JSONObject.toJSONString(mkuEsPO),indexName,result);
        }

        return result;
    }

    public Boolean updateHasStockFlag(String indexName, MkuEsPO mkuEsPO) {
        if (StringUtils.isBlank(indexName) || Objects.isNull(mkuEsPO) || StringUtils.isBlank(mkuEsPO.getId())) {
            log.error("MkuEsService.updateHasStockFlag indexName or id is null");
            return Boolean.FALSE;
        }

        boolean result = false;
        try {
            Map<String, Object> updateDoc = new HashMap<>();
            updateDoc.put("has_stock", mkuEsPO.getHasStock());
            UpdateRequest<MkuEsPO, Map<String, Object>> updateRequest = UpdateRequest.of(u -> u
                    .index(indexName)
                    .id(mkuEsPO.getId())
                    .doc(updateDoc)
            );

            UpdateResponse<MkuEsPO> updateResponse = elasticsearchClient.update(updateRequest, MkuEsPO.class);

            result = updateResponse.result().name().equalsIgnoreCase("UPDATED");
            return result;
        } catch (IOException e) {
            log.error("updateHasStockFlag es更新文档失败，param:{},index:{},e:", JSON.toJSONString(mkuEsPO), indexName, e);
        } finally {
            log.error("updateHasStockFlag finished, id:{},index:{},result:{}", mkuEsPO.getId(), indexName, result);
        }

        return result;
    }

//    /**
//     * @function 解析搜索响应中的建议词，并根据语言提取相应的标题
//     * @param response 搜索响应对象，包含建议词信息
//     * @returns 包含建议词的列表，根据语言返回相应的标题列表
//     */
//    private List<String> parseSuggestResponse(SearchResponse<MkuEsPO> response){
//        // 处理响应，提取出建议词
//        List<String> suggestions = new ArrayList<>();
//        response.suggest().get("my_suggestions").forEach(suggestion -> {
//            if(!suggestion.isCompletion()){
//                return;
//            }
//            CompletionSuggest<MkuEsPO> completionSuggest = suggestion.completion();
//            List<CompletionSuggestOption<MkuEsPO>> options = completionSuggest.options();
//            if(CollectionUtils.isEmpty(options)){
//                return;
//            }
//            options.forEach(option -> {
//                suggestions.add(option.text());
//            });
//        });
//        log.info("MkuEsAtomicService.parseSuggestResponse: {}", JSONObject.toJSONString(suggestions));
//        return suggestions;
//    }
}