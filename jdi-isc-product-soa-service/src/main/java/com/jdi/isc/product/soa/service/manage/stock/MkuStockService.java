package com.jdi.isc.product.soa.service.manage.stock;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.enums.PurchaseModelTypeEnum;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockResDTO;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.util.CommonUtils;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPricePO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuIdRefVO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuSplitVO;
import com.jdi.isc.product.soa.domain.price.biz.MkuPriceReqVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.stock.vo.StockItemManageVO;
import com.jdi.isc.product.soa.rpc.gms.AreaStockStateRpcService;
import com.jdi.isc.product.soa.rpc.iop.SkuStockRpcService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.customerMku.CustomerMkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuFeatureManageService;
import com.jdi.isc.product.soa.service.mapstruct.stock.MkuStockConvert;
import com.jdi.isc.product.soa.stock.mku.req.IscMkuStockItemReadReqDTO;
import com.jdi.isc.product.soa.stock.mku.req.IscMkuStockReadReqDTO;
import com.jdi.isc.product.soa.stock.mku.res.IscMkuStockResDTO;
import com.jdi.isc.product.soa.stock.sku.req.StockItemReqDTO;
import com.jdi.isc.product.soa.stock.sku.req.StockReadReqDTO;
import com.jdi.isc.product.soa.stock.sku.res.StockReadResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * mku库存查询服务
 */
@Service
@Slf4j
public class MkuStockService {

    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private CustomerMkuPriceManageService customerMkuPriceManageService;
    @Resource
    private SkuStockRpcService skuStockRpcService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private StockManageService stockManageService;
    @Resource
    private AreaStockStateRpcService areaStockStateRpcService;
    @Resource
    private SkuFeatureManageService skuFeatureManageService;
    @Resource
    private SkuStockManageService skuStockManageService;
    // 33 有货 39 40 有货，需要调配
    public final static Set<Integer> HAVE_STOCK = Sets.newHashSet(33,39,40);

    /** 查询库存数量*/
    public Map<Long, IscMkuStockResDTO> getStock(IscMkuStockReadReqDTO input) {
        Map<Long,IscMkuStockResDTO> cnStock;
        MkuSplitVO typeMku = getTypeMku(input);
        //库存分别获取
        cnStock = getCnStock(input,typeMku);
        Map<Long,IscMkuStockResDTO> localStock = getLocalStock(typeMku);
        cnStock.putAll(localStock);
        return cnStock;
    }

    /** 获取真实库存(10w阈值)*/
    public Map<Long, IscMkuStockResDTO> getRealStock(IscMkuStockReadReqDTO input) {
        Map<Long,IscMkuStockResDTO> cnStock;
        MkuSplitVO typeMku = getTypeMku(input);
        //库存分别获取
        cnStock = getCnRealStock(input,typeMku);
        Map<Long,IscMkuStockResDTO> localStock = getLocalStock(typeMku);
        cnStock.putAll(localStock);
        return cnStock;
    }

    private MkuSplitVO getTypeMku(IscMkuStockReadReqDTO input){
        //获取mku和sku关系
        // 查询客户国家编码
        CustomerVO customerVO = customerManageService.detail(input.getClientCode());
        Map<Long, MkuIdRefVO> mkuMap = getMkuRef(customerVO, input.getStockItem().stream().map(IscMkuStockItemReadReqDTO::getMkuId).collect(Collectors.toSet()));
        List<StockItemManageVO> cnMku = new ArrayList<>();
        List<StockItemManageVO> localMku = new ArrayList<>();
        List<StockItemManageVO> stockingMku = new ArrayList<>();
        for(IscMkuStockItemReadReqDTO item : input.getStockItem()){
            MkuIdRefVO ref = mkuMap.get(item.getMkuId());
            if (Objects.isNull(ref)) {
                log.warn("MkuStockService.getTypeMku MkuIdRefVO is empty skip it, mkuId:{}", item.getMkuId());
                continue;
            }

            StockItemManageVO itemManageVO = MkuStockConvert.INSTANCE.itemReqDTO2VO(item);
            // 本土品
            if(!CountryConstant.COUNTRY_ZH.equals(ref.getPo().getSourceCountryCode())){
                itemManageVO.setSkuId(ref.getPo().getSkuId());
                localMku.add(itemManageVO);
                continue;
            }
            // 筛选出备货模式商品(跨境备货、跨境寄售)
            if (Objects.nonNull(ref.getPurchaseModel())  && (Objects.equals(PurchaseModelTypeEnum.STOCK_UP.getCode(), ref.getPurchaseModel()) ||
                    Objects.equals(PurchaseModelTypeEnum.CONSIGNMENT.getCode(), ref.getPurchaseModel()))) {
                itemManageVO.setSkuId(ref.getPo().getSkuId());
                stockingMku.add(itemManageVO);
            }else {
                //跨境直发
                itemManageVO.setSkuId(ref.getPo().getSkuId());
                cnMku.add(itemManageVO);
            }
        }

        return new MkuSplitVO(cnMku,localMku,mkuMap,stockingMku,customerVO.getCountry());
    }

    /** 获取本土品库存信息*/
    private Map<Long, IscMkuStockResDTO> getLocalStock(MkuSplitVO mkuMap) {
        if(CollectionUtils.isEmpty(mkuMap.getLocalMku()) && CollectionUtils.isEmpty(mkuMap.getStockingMku())){
            return new HashMap<>();
        }
        List<StockItemManageVO> stockItemList = Lists.newArrayList(mkuMap.getLocalMku());
        stockItemList.addAll(mkuMap.getStockingMku());
        Map<Long, IscMkuStockResDTO> result = Maps.newHashMapWithExpectedSize(stockItemList.size());
        List<StockItemManageReqDTO> stockItem = new ArrayList<>();
        for(StockItemManageVO item : stockItemList){
            StockItemManageReqDTO stockReq = new StockItemManageReqDTO();
            if(item.getSkuId()!=null){
                stockReq.setSkuId(item.getSkuId());
                stockReq.setNum(Long.valueOf(item.getNum()));
                stockItem.add(stockReq);
            }
        }
        StockManageReqDTO req = new StockManageReqDTO();
        req.setStockItem(stockItem);
        // 增加客户国家编码
        req.setCountryCode(mkuMap.getCountryCode());
        Map<Long, StockResDTO> res = stockManageService.getStock(req);
        if(MapUtils.isNotEmpty(res)){
            //sku&mku
            Map<Long, Long> skuMap = CommonUtils.getSkuIdMap(mkuMap.getMkuMap());
            for (Map.Entry<Long, StockResDTO> entry : res.entrySet()) {
                IscMkuStockResDTO target = new IscMkuStockResDTO();
                target.setMkuId(skuMap.get(entry.getKey()));
                target.setNum(entry.getValue().getStock());
                target.setAvailableStock(entry.getValue().getRemainNum());
                target.setStockStateType(entry.getValue().getStockStateType());
                target.setOccupy(entry.getValue().getOccupy());
                target.setTransitStock(entry.getValue().getOnWayStock());
                target.setOnWaySale(entry.getValue().getOnWaySale());
                result.put(target.getMkuId(),target);
            }
        }
        log.info("MkuStockService.getLocalStock req:{} , res:{}" , JSON.toJSONString(mkuMap.getLocalMku()), JSON.toJSONString(result));
        return result;
    }
    /** 获取跨境品库存信息*/
    private Map<Long, IscMkuStockResDTO> getCnStock(IscMkuStockReadReqDTO input, MkuSplitVO mkuMap) {
        List<IscMkuStockResDTO> result = new ArrayList<>(mkuMap.getCnMku().size());
        if(CollectionUtils.isEmpty(mkuMap.getCnMku())){
            return new HashMap<>();
        }
        //jdSku&mku
        Map<Long, Long> skuMap = CommonUtils.getSkuIdMap(mkuMap.getMkuMap());
        StockReadReqDTO readReqDTO = this.buildStockReadReqDTO(input, mkuMap);
        DataResponse<Map<Long, StockReadResDTO>> response = skuStockManageService.queryCnStock(readReqDTO);

        //请求查询库存
        if(null != response && response.getSuccess() && MapUtils.isNotEmpty(response.getData())){
            response.getData().forEach((skuId,stock)->{
                IscMkuStockResDTO res = new IscMkuStockResDTO();
                res.setMkuId(skuMap.get(skuId));
                res.setStockStateType(stock.getStockStateType());
                //查询国内库存 小于200 真实数字  大于等于200展示-1
                res.setNum(HAVE_STOCK.contains(stock.getStockStateType()) ? Long.valueOf(stock.getRemainNum()) : 0L);
                //为了补齐和本土/备货相同逻辑
                res.setAvailableStock(res.getNum());
                res.setOccupy(0L);
                res.setTransitStock(0L);
                res.setOnWaySale(0);
                //为了补齐和本土/备货相同逻辑
                result.add(res);
            });
        }
        Map<Long, IscMkuStockResDTO> fResult = result.stream().collect(Collectors.toMap(IscMkuStockResDTO::getMkuId, Function.identity()));
        log.info("MkuStockService.getCnStock req:{} , res:{}" , JSON.toJSONString(mkuMap.getCnMku()), JSON.toJSONString(fResult));
        return fResult;
    }

    private StockReadReqDTO buildStockReadReqDTO(IscMkuStockReadReqDTO input, MkuSplitVO mkuMap) {
        List<StockItemReqDTO> skuNumInfoList = Lists.newArrayList();
        for(StockItemManageVO item : mkuMap.getCnMku()){
            if(item.getSkuId()!=null){
                skuNumInfoList.add(new StockItemReqDTO(item.getSkuId(),item.getNum()!=null?item.getNum():1));
            }
        }
        StockReadReqDTO readReqDTO = new StockReadReqDTO();
        readReqDTO.setStockItems(skuNumInfoList);
        readReqDTO.setClientCode(input.getClientCode());
        return readReqDTO;
    }

    /** 获取跨境10w阈值库存*/
    private Map<Long, IscMkuStockResDTO> getCnRealStock(IscMkuStockReadReqDTO input, MkuSplitVO mkuMap) {
        if(CollectionUtils.isEmpty(mkuMap.getCnMku())){
            return new HashMap<>();
        }
        Map<Long, IscMkuStockResDTO> result = Maps.newHashMapWithExpectedSize(mkuMap.getCnMku().size());
        Map<Long, Long> skuMap = CommonUtils.getSkuIdMap(mkuMap.getMkuMap());
        StockReadReqDTO readReqDTO = new StockReadReqDTO();
        readReqDTO.setClientCode(input.getClientCode());
        readReqDTO.setStockItems(mkuMap.getCnMku().stream().map(item->new StockItemReqDTO(item.getSkuId(),item.getNum()!=null?item.getNum():1)).collect(Collectors.toList()));
        Map<Long, StockReadResDTO> cnRealStockMap = skuStockManageService.queryCnRealStock(readReqDTO);
        if(MapUtils.isNotEmpty(cnRealStockMap)){
            //结果中的jdSkuId转回国际mkuId
            cnRealStockMap.values().forEach(skuStockInfo-> {
                IscMkuStockResDTO target = new IscMkuStockResDTO();
                target.setNum(skuStockInfo.getRemainNum());
                target.setMkuId(skuMap.get(skuStockInfo.getSkuId()));
                target.setStockStateType(skuStockInfo.getStockStateType());
                //为了补齐和本土/备货相同逻辑
                target.setAvailableStock(target.getNum());
                target.setOccupy(0L);
                target.setTransitStock(0L);
                target.setOnWaySale(0);
                //为了补齐和本土/备货相同逻辑
                result.put(target.getMkuId(),target);
            });
        }
        return result;
    }

    public Map<Long, MkuIdRefVO> getMkuRef(CustomerVO customerVO, Set<Long> mkuIds){
        Map<Long,MkuIdRefVO> result = new HashMap<>();
        MkuPriceReqVO priceReqVO = new MkuPriceReqVO();
        priceReqVO.setClientCode(customerVO.getClientCode());
        priceReqVO.setMkuIdList(mkuIds);
        List<CustomerMkuPricePO> fixedSkuList = customerMkuPriceManageService.getCustomerMkuFixedSkuListByMkuClient(priceReqVO);
        if (CollectionUtils.isEmpty(fixedSkuList)) {
            throw new BizException(String.format("客户%s和商品%s绑定关系不存在，请检查！",customerVO.getClientCode(),JSON.toJSONString(mkuIds)));
        }
        //sku&mku
        Map<Long, Long> mkuMap = fixedSkuList.stream().collect(Collectors.toMap(CustomerMkuPricePO::getFixedSkuId,CustomerMkuPricePO::getMkuId));
        Set<Long> skuIds = fixedSkuList.stream().map(CustomerMkuPricePO::getFixedSkuId).collect(Collectors.toSet());
        //sku&skuPo
        Map<Long, SkuPO> skuPOMap = skuAtomicService.batchQuerySkuPO(skuIds);
        // 补全备货模式
        Map<Long, Integer> skuPurchaseModelMap = skuFeatureManageService.querySkuPurchaseModel(skuIds, customerVO.getCountry());
        for (Map.Entry<Long,SkuPO> entry : skuPOMap.entrySet()) {
            MkuIdRefVO res = new MkuIdRefVO();
            SkuPO skuPo = entry.getValue();
            res.setPo(skuPo);
            res.setPurchaseModel(skuPurchaseModelMap.get(skuPo.getSkuId()));
            result.put(mkuMap.get(entry.getKey()),res);
        }
        return result;
    }

}
