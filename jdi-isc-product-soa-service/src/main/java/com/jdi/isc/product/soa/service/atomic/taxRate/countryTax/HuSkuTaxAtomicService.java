package com.jdi.isc.product.soa.service.atomic.taxRate.countryTax;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.HuSkuTaxPO;
import com.jdi.isc.product.soa.price.api.price.req.HuSkuTaxVO;
import com.jdi.isc.product.soa.repository.mapper.taxRate.countryTax.HuSkuTaxBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 匈牙利SKU税率原子服务
 * <AUTHOR>
 * @date 20250311
 */
@Service
@Slf4j
public class HuSkuTaxAtomicService extends ServiceImpl<HuSkuTaxBaseMapper, HuSkuTaxPO> {

    /**
     * 单个查询
     */
    public HuSkuTaxPO getOne(HuSkuTaxVO vo) {
        LambdaQueryWrapper<HuSkuTaxPO> query = Wrappers.<HuSkuTaxPO>lambdaQuery()
                .eq(HuSkuTaxPO::getJdSkuId, vo.getJdSkuId())
                .eq(HuSkuTaxPO::getYn, YnEnum.YES.getCode());
        return super.getOne(query);
    }

    public Map<Long,HuSkuTaxPO> listSkuTax(Set<Long> skuIds){
        LambdaQueryWrapper<HuSkuTaxPO> queryWrapper = Wrappers.<HuSkuTaxPO>lambdaQuery()
                .in(HuSkuTaxPO::getJdSkuId, skuIds)
                .eq(HuSkuTaxPO::getYn, YnEnum.YES.getCode());
        List<HuSkuTaxPO> res = super.list(queryWrapper);
        if(CollectionUtils.isNotEmpty(res)){
            return res.stream().collect(Collectors.toMap(HuSkuTaxPO::getJdSkuId, a -> a));
        }
        return new HashMap<>();
    }

    public boolean updateTax(HuSkuTaxVO vo) {
        LambdaUpdateWrapper<HuSkuTaxPO> wrapper = Wrappers.<HuSkuTaxPO>lambdaUpdate()
            .set(StringUtils.isNotBlank(vo.getHsCode()), HuSkuTaxPO::getHsCode, vo.getHsCode())
            .set(vo.getImportTax() != null, HuSkuTaxPO::getImportTax, vo.getImportTax())
            .set(vo.getValueAddedTax() != null, HuSkuTaxPO::getValueAddedTax, vo.getValueAddedTax())
            .set(vo.getAntiSubsidyTax() != null, HuSkuTaxPO::getAntiSubsidyTax, vo.getAntiSubsidyTax())
            .set(vo.getAntiDumpingTax() != null, HuSkuTaxPO::getAntiDumpingTax, vo.getAntiDumpingTax())
            .set(vo.getIsOriginCertificate() != null, HuSkuTaxPO::getIsOriginCertificate, vo.getIsOriginCertificate())
            .set(StringUtils.isNotBlank(vo.getRemark()), HuSkuTaxPO::getRemark, vo.getRemark())
            .set(HuSkuTaxPO::getUpdater, vo.getUpdater())
            .set(HuSkuTaxPO::getUpdateTime, new Date())
            .eq(HuSkuTaxPO::getId, vo.getId());
        return super.update(wrapper);
    }

}
