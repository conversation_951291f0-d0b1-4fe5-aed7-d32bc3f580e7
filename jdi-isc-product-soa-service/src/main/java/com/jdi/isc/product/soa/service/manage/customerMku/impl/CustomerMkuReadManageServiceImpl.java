package com.jdi.isc.product.soa.service.manage.customerMku.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.enums.PriceAvailableSaleStatusEnum;
import com.jdi.isc.product.soa.api.mku.req.IscMkuAvailableSaleReq;
import com.jdi.isc.product.soa.api.mku.res.IscMkuAvailableSaleResDTO;
import com.jdi.isc.product.soa.common.constants.msgCode.ProductMessageEnum;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.util.StringUtils;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuCheckReqVO;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPricePO;
import com.jdi.isc.product.soa.domain.enums.countryMku.CountryMkuStatusEnum;
import com.jdi.isc.product.soa.domain.mku.biz.MkuClientInPoolVO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuClientUpSaleStatusVO;
import com.jdi.isc.product.soa.domain.price.biz.MkuPriceAvailableVO;
import com.jdi.isc.product.soa.domain.price.biz.MkuPriceReqVO;
import com.jdi.isc.product.soa.domain.price.biz.MkuPriceResVO;
import com.jdi.isc.product.soa.domain.sku.biz.QuerySkuAvailableSaleReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuAvailableSaleResultVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.rpc.suport.AlertHelper;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.manage.countryMku.CountryMkuManageService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.customerMku.CustomerMkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.customerMku.CustomerMkuReadManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceDetailManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.clientPrice.MkuClientPriceManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuCrossBorderSaleStatusReadManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuFeatureManageService;
import com.jdi.isc.product.soa.service.mapstruct.customerMku.CustomerMkuPriceConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description：商品客户池读服务
 * @Date 2025-05-14
 */
@Slf4j
@Service
public class CustomerMkuReadManageServiceImpl implements CustomerMkuReadManageService, CommandLineRunner {

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private CustomerManageService customerManageService;

    @Resource
    private SkuFeatureManageService skuFeatureManageService;

    @Resource
    private CountryMkuManageService countryMkuManageService;

    @Resource
    private CustomerMkuPriceManageService customerMkuPriceManageService;

    @Resource
    private SkuCrossBorderSaleStatusReadManageService skuCrossBorderSaleStatusReadManageService;

    @Resource
    private MkuClientPriceManageService mkuClientPriceManageService;

    @Resource
    private OperDuccConfig operDuccConfig;

    @Override
    public Map<Long, IscMkuAvailableSaleResDTO> queryMkuAvailable(IscMkuAvailableSaleReq req) {
        Map<Long,IscMkuAvailableSaleResDTO> resultMap = Maps.newHashMap();

        // 查询客户信息
        CustomerVO customerVO = customerManageService.detail(req.getClientCode());
        if (Objects.isNull(customerVO)) {
            log.error("CustomerMkuReadManageServiceImpl.queryMkuAvailable 客户信息不存在 clientCode={}",req.getClientCode());
            throw new BizException(String.format("客户%s的信息不存在，请检查",req.getClientCode()));
        }

        // 查询客户和mku绑定的关联关系
        List<CustomerMkuPricePO> customerMkuPricePOList = customerMkuPriceManageService.getCustomerMkuFixedSkuListByMkuClient(CustomerMkuPriceConvert.INSTANCE.availableReq2VO(req));

        if (CollectionUtils.isEmpty(customerMkuPricePOList)) {
            log.error("CustomerMkuReadManageServiceImpl.queryMkuAvailable customerMkuPricePOList 客户绑定mku价格为空,入参:[{}]", JSON.toJSONString(req));
            Set<Long> mkuIds = req.getMkuIds();
            return emptyResultMap(mkuIds);
        }
        // mku固定sku关系
        Map<Long, Long> mkuFixedSkuMap = getMkuFixedSkuMap(customerMkuPricePOList);
        // 过滤不在客户池的商品
        filterNoCustomerPoolMku(req, mkuFixedSkuMap, resultMap);
        // 查询MKU是否在国家池
        Set<Long> noCountryPoolMkuIds = filterNoCountryPoolMku(mkuFixedSkuMap, customerVO);
        fillNoCountryPoolMkuResult(noCountryPoolMkuIds, resultMap);
        // 移除不在国家池商品
        mkuFixedSkuMap.keySet().removeAll(noCountryPoolMkuIds);
        if (MapUtils.isEmpty(mkuFixedSkuMap)) {
            return resultMap;
        }
        Set<Long> downSaleMkuIds = filterDownStatusMku(mkuFixedSkuMap, customerVO,resultMap);
        // 移除不是上架的国家池商品
        mkuFixedSkuMap.keySet().removeAll(downSaleMkuIds);
        if (MapUtils.isEmpty(mkuFixedSkuMap)) {
            return resultMap;
        }
        // 查询国内SKU可采信息
        fillCrossSkuCanPurchase(req, mkuFixedSkuMap, resultMap);
        // 填充价格可售状态
        long l = System.currentTimeMillis();
        try {
            fillPriceAvailableSaleStatus(req, resultMap, mkuFixedSkuMap);
        } catch (Exception e) {
            log.error("fillPriceAvailableSaleStatus error param={}", JSONObject.toJSONString(req), e);
            AlertHelper.p0("fillPriceAvailableSaleStatus error");
        } finally {
            log.info("queryMkuAvailable.fillPriceAvailableSaleStatus, mkuSize={} cost:{}", req.getMkuIds().size(), System.currentTimeMillis() - l);
        }

        // 本土SKU可采信息
        fillLocalSkuCanPurchase(req, resultMap);
        return resultMap;
    }

    /**
     * 根据价格可售状态填充商品的可售状态
     * @param req 包含客户端信息的请求对象
     * @param resultMap 商品ID与可售状态结果的映射表
     * @param mkuFixedSkuMap 商品ID与SKU ID的映射表
     */
    @PFTracing
    private void fillPriceAvailableSaleStatus(IscMkuAvailableSaleReq req, Map<Long, IscMkuAvailableSaleResDTO> resultMap, Map<Long, Long> mkuFixedSkuMap) {

        // 是否使用价格可售状态过滤数据
        if (!operDuccConfig.isQueryMkuAvailableUseStatus()) {
            log.info("是否使用价格可售状态过滤数据，未开启配置. jdi.isc.queryMkuAvailable.useStatus, 请检查");
            return;
        }

        if (MapUtils.isEmpty(resultMap) || MapUtils.isEmpty(mkuFixedSkuMap)) {
            return;
        }

        String clientCode = req.getClientCode();
        if (StringUtils.isEmpty(clientCode)) {
            log.info("fillPriceAvailableSaleStatus clientCode is null, req={}", JSONObject.toJSONString(req));
            AlertHelper.p0("查询sku价格. clientCode is null");
            return;
        }

        // 查询价格可售状态
        List<MkuPriceAvailableVO> list = this.listMkuPriceAvailableStatus(req, resultMap, mkuFixedSkuMap);

        if (CollectionUtils.isEmpty(list)) {
            log.info("fillPriceAvailableSaleStatus list is empty, req={}", JSONObject.toJSONString(req));
            return;
        }

//        List<MkuPriceResVO> list = listMkuPrices(req, resultMap);

        // 可售状态Map
        Map<Long, MkuPriceAvailableVO> mkutAvailableSaleStatusMap = list.stream()
                .filter(item -> item.getMkuId() != null)
                .filter(item -> item.getAvailableSaleStatus() != null)
                .collect(Collectors.toMap(MkuPriceAvailableVO::getMkuId, Function.identity(), (l1, l2) -> l1));

        resultMap.forEach((mkuId, v) -> {
            if (v == null) {
                log.warn("fillPriceAvailableSaleStatus v is null");
                AlertHelper.p0("fillPriceAvailableSaleStatus v is null", String.valueOf(mkuId));
                return;
            }
            if (v.getIsAvailableSale() == null || !v.getIsAvailableSale()) {
                log.info("fillPriceAvailableSaleStatus isAvailableSale is null or false, mkuId={}, remark={}", mkuId, v.getUnAvailableSaleMessage());
                return;
            }

            Long skuId = mkuFixedSkuMap.get(mkuId);
            if (skuId == null) {
                log.warn("根据mkuId找不到skuId, mkuId={}", mkuId);
                AlertHelper.p0("查询sku价格. 根据mkuId找不到skuId", String.valueOf(mkuId));
                return;
            }

            MkuPriceAvailableVO mkuPriceAvailableVO = mkutAvailableSaleStatusMap.get(mkuId);

            // 如果不存在，则证明即没有客制化价格也没有协议价
            if (mkuPriceAvailableVO == null) {
                log.warn("availableSaleStatus is null, mkuId={}, skuId={}, clientCode={}", mkuId, skuId, clientCode);
                v.setIsAvailableSale(false);
                v.setUnAvailableSaleMessage(ProductMessageEnum.MCT50005.getMsg());
                v.setUnAvailableSaleCode(ProductMessageEnum.MCT50005.getCode());
                AlertHelper.p0("availableSaleStatus is null", String.valueOf(mkuId));
                return;
            }

            // 如果不是可售则设置为不可售
            if (mkuPriceAvailableVO.getAvailableSaleStatus() == null || mkuPriceAvailableVO.getAvailableSaleStatus() != PriceAvailableSaleStatusEnum.ENABLE.getCode()) {
                log.warn("fillPriceAvailableSaleStatus 设置可售状态为不可售, mkuId={}, skuId={}， clientCode={}", mkuId, skuId, req.getClientCode());
                v.setIsAvailableSale(false);
                v.setUnAvailableSaleMessage(mkuPriceAvailableVO.getUnAvailableSaleMessage());
                v.setUnAvailableSaleCode(mkuPriceAvailableVO.getUnAvailableSaleCode());
            }
        });

    }

    @Resource
    private CustomerSkuPriceDetailManageService customerSkuPriceDetailManageService;

    @Resource
    private CountryAgreementPriceManageService agreementPriceManageService;

    private List<MkuPriceAvailableVO> listMkuPriceAvailableStatus(IscMkuAvailableSaleReq req, Map<Long, IscMkuAvailableSaleResDTO> resultMap, Map<Long, Long> mkuFixedSkuMap) {


        CustomerVO customerVO = customerManageService.detail(req.getClientCode());

        if (customerVO == null) {
            log.info("fillPriceAvailableSaleStatus customerVO is null, req={}", JSONObject.toJSONString(req));
            AlertHelper.p0("查询sku价格. customerVO is null", req.getClientCode());
            return Lists.newArrayList();
        }

        List<MkuPriceAvailableVO> result = Lists.newArrayList();

        if (StringUtils.isNotEmpty(customerVO.getOpenArea())) {
            // 开启公域比价，则都可售
            resultMap.forEach((k, v) -> {
                MkuPriceAvailableVO item = new MkuPriceAvailableVO(v.getMkuId(), mkuFixedSkuMap.get(v.getMkuId()), PriceAvailableSaleStatusEnum.ENABLE.getCode());
                result.add(item);
            });
            return result;
        }

        // 所有可售的sku
        List<Long> availableSkuIds = Lists.newArrayList();

        // 过滤需要查询的sku
        resultMap.forEach((k, v) -> {
            if (v.getIsAvailableSale() == null || !v.getIsAvailableSale()) {
                return;
            }

            Long skuId = mkuFixedSkuMap.get(k);
            if (skuId == null) {
                log.warn("listMkuPriceAvailableStatus 根据mkuId找不到skuId, mkuId={}", k);
                AlertHelper.p0("listMkuPriceAvailableStatus 查询sku价格. 根据mkuId找不到skuId", String.valueOf(k));
                return;
            }

            availableSkuIds.add(skuId);
        });

        // 为空则证明都是不可售，无需再次查询价格可售状态
        if (CollectionUtils.isEmpty(availableSkuIds)) {
            return Lists.newArrayList();
        }

        // 客制化价格可售状态
        List<MkuPriceAvailableVO> customerAvailableList = customerSkuPriceDetailManageService.listMkuPriceAvailableStatus(customerVO.getClientCode(), availableSkuIds, mkuFixedSkuMap);
        List<Long> customerSkuIds = customerAvailableList.stream().map(MkuPriceAvailableVO::getSkuId).distinct().collect(Collectors.toList());

        // 可售的sku中，没有客制化价格记录的sku
        List<Long> agreementPriceSkuIds = CollectionUtil.subtractToList(availableSkuIds, customerSkuIds);

        List<MkuPriceAvailableVO> agreementAvaliableList = agreementPriceManageService.listMkuPriceAvailableStatus(customerVO.getCountry(), agreementPriceSkuIds, mkuFixedSkuMap);


        if (CollectionUtils.isNotEmpty(customerAvailableList)) {
            result.addAll(customerAvailableList);
        }

        if (CollectionUtils.isNotEmpty(agreementAvaliableList)) {
            result.addAll(agreementAvaliableList);
        }

        return result;
    }

    @Deprecated
    private List<MkuPriceResVO> listMkuPrices(IscMkuAvailableSaleReq req, Map<Long,IscMkuAvailableSaleResDTO> resultMap) {

        if (MapUtils.isEmpty(resultMap)) {
            log.info("listMkuPrices param is null");
            return Lists.newArrayList();
        }

        if (StringUtils.isEmpty(req.getClientCode())) {
            log.info("listMkuPrices clientCode is null, req={}", JSONObject.toJSONString(req));
            AlertHelper.p0("查询sku价格. clientCode is null");
            return Lists.newArrayList();
        }

        Set<Long> mkuIds = resultMap.values().stream()
                .filter(item -> item.getIsAvailableSale() != null && item.getIsAvailableSale())
                .map(IscMkuAvailableSaleResDTO::getMkuId)
                .filter(Objects::nonNull).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(mkuIds)) {
            log.info("listMkuPrices mkuIds is null");
            return Lists.newArrayList();
        }


        MkuPriceReqVO mkuPriceReqVO = new MkuPriceReqVO(mkuIds, req.getClientCode());

        DataResponse<List<MkuPriceResVO>> priceResponse = mkuClientPriceManageService.listMkuPrice(mkuPriceReqVO);

        if (priceResponse == null) {
            log.error("查询mku价格，出现异常. priceResponse is null, mkuPriceReq={}", JSONObject.toJSONString(mkuPriceReqVO));
            resultMap.forEach((k, v) -> {
                if (mkuIds.contains(k)) {
                    v.setIsAvailableSale(false);
                    v.setUnAvailableSaleMessage("查询mku价格失败");
                }
            });
            return Lists.newArrayList();
        }

        if (!priceResponse.getSuccess()) {
            AlertHelper.p0("查询mku价格，出现异常");
            log.error("查询mku价格，出现异常. priceResponse is null, mkuPriceReq={}, response={}", JSONObject.toJSONString(mkuPriceReqVO), JSONObject.toJSONString(priceResponse));
            resultMap.forEach((k, v) -> {
                if (mkuIds.contains(k)) {
                    v.setIsAvailableSale(false);
                    v.setUnAvailableSaleMessage(priceResponse.getMessage());
                }
            });
            return Lists.newArrayList();
        }

        return priceResponse.getData();
    }

    /**
     * 将请求中的 MKU ID 添加到结果映射中，如果该 MKU ID 不存在。
     * @param req IscMkuAvailableSaleReq 对象，包含需要处理的 MKU ID 列表。
     * @param resultMap 结果映射，用于存储处理后的 MKU ID 和可购买状态。
     */
    private void fillLocalSkuCanPurchase(IscMkuAvailableSaleReq req, Map<Long, IscMkuAvailableSaleResDTO> resultMap) {
        for (Long mkuId : req.getMkuIds()) {
            if (!resultMap.containsKey(mkuId)) {
                resultMap.put(mkuId, new IscMkuAvailableSaleResDTO(mkuId, Boolean.TRUE));
            }
        }
    }

    /**
     * 根据给定的请求和映射表，填充跨境 SKU 的可购买状态到结果映射中。
     * @param req IscMkuAvailableSaleReq 对象，包含客户端代码等信息。
     * @param mkuFixedSkuMap Long 到 Long 的映射表，表示 MKU ID 到固定 SKU ID 的映射关系。
     * @param resultMap Long 到 IscMkuAvailableSaleResDTO 的映射表，用于存储每个 MKU ID 对应的可购买状态。
     */
    private void fillCrossSkuCanPurchase(IscMkuAvailableSaleReq req, Map<Long, Long> mkuFixedSkuMap, Map<Long, IscMkuAvailableSaleResDTO> resultMap) {
        Collection<Long> skuIds = mkuFixedSkuMap.values();
        List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(Sets.newHashSet(skuIds));
        Set<Long> cnSkuIds = skuPOList.stream().filter(skuPO -> CountryConstant.COUNTRY_ZH.equals(skuPO.getSourceCountryCode())).map(SkuPO::getSkuId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(cnSkuIds)) {
            QuerySkuAvailableSaleReqVO reqVO = new QuerySkuAvailableSaleReqVO();
            reqVO.setSkuIds(cnSkuIds);
            reqVO.setClientCode(req.getClientCode());
            Map<Long, SkuAvailableSaleResultVO> skuAvailableMap = skuCrossBorderSaleStatusReadManageService.querySkuAvailableCanPurchaseMap(reqVO);
            log.info("CustomerMkuReadManageServiceImpl.queryMkuAvailable 查询跨境品的可采状态 入参:[{}], skuIds={},skuAvailableMap={}",JSON.toJSONString(req),JSON.toJSONString(cnSkuIds),JSON.toJSONString(skuAvailableMap));
            Map<Long, Long> fixedSkuMkuMap = MapUtils.invertMap(mkuFixedSkuMap);
            skuAvailableMap.forEach((skuId,available) -> {
                Long mkuId = fixedSkuMkuMap.get(skuId);
                resultMap.put(mkuId,new IscMkuAvailableSaleResDTO(mkuId,available.getIsAvailableSale(),available.getUnAvailableSaleCode(), available.getUnAvailableSaleMessage()));
            });
        }
    }

    /**
     * 根据客户的MKU价格列表，生成一个MKU ID到固定SKU ID的映射。
     * @param customerMkuPricePOList 客户的MKU价格列表
     * @return MKU ID到固定SKU ID的映射
     */
    private  Map<Long, Long> getMkuFixedSkuMap(List<CustomerMkuPricePO> customerMkuPricePOList) {
        Map<Long,Long> mkuFixedSkuMap = Maps.newHashMap();
        for (CustomerMkuPricePO customerMkuPricePO : customerMkuPricePOList) {
            mkuFixedSkuMap.put(customerMkuPricePO.getMkuId(), customerMkuPricePO.getFixedSkuId());
        }
        return mkuFixedSkuMap;
    }

    /**
     * 将不在国家池的 MKU ID 添加到结果集中。
     * @param noCountryPoolMkuIds 不在国家池的 MKU ID 集合。
     * @param resultMap 结果集，用于存储 MKU ID 和对应的销售状态。
     */
    private void fillNoCountryPoolMkuResult(Set<Long> noCountryPoolMkuIds, Map<Long, IscMkuAvailableSaleResDTO> resultMap) {
        if (CollectionUtils.isNotEmpty(noCountryPoolMkuIds)) {
            for (Long mkuId : noCountryPoolMkuIds) {
                resultMap.put(mkuId, this.getFailedResDTO(mkuId,ProductMessageEnum.MCP40001));
            }
        }
    }

    /**
     * 根据客户所在国家和固定商品 SKU，过滤出不在国家池的商品 ID。
     * @param mkuFixedSkuMap 固定商品 SKU 和对应的 MKU ID 的映射关系。
     * @param customerVO 客户信息，包含客户所在国家。
     * @return 不在国家池的商品 ID 集合。
     */
    private Set<Long> filterNoCountryPoolMku(Map<Long, Long> mkuFixedSkuMap, CustomerVO customerVO) {
        Set<Long> noCountryPoolMkuIds = Sets.newHashSet();
        DataResponse<List<MkuClientInPoolVO>> dataResponse = countryMkuManageService.checkInPool(new CountryMkuCheckReqVO(Lists.newArrayList(mkuFixedSkuMap.keySet()), customerVO.getCountry()));
        if (Objects.nonNull(dataResponse) && dataResponse.getSuccess()) {
            List<MkuClientInPoolVO> clientInPoolVOList = dataResponse.getData();
            // 不在国家池商品列表
            noCountryPoolMkuIds = clientInPoolVOList.stream().filter(Objects::nonNull).filter(vo -> !vo.getCountryPoolFlag()).map(MkuClientInPoolVO::getMkuId).collect(Collectors.toSet());
        }
        return noCountryPoolMkuIds;
    }

    private Set<Long> filterDownStatusMku(Map<Long, Long>  mkuFixedSkuMap, CustomerVO customerVO,Map<Long, IscMkuAvailableSaleResDTO> resultMap){
        Set<Long> downSaleMkuIds = Sets.newHashSet();
        DataResponse<List<MkuClientUpSaleStatusVO>> dataResponse = countryMkuManageService.checkUpSaleStatus(new CountryMkuCheckReqVO(Lists.newArrayList(mkuFixedSkuMap.keySet()), customerVO.getCountry()));
        if (Objects.nonNull(dataResponse) && dataResponse.getSuccess()) {
            List<MkuClientUpSaleStatusVO> clientUpSaleVOList = dataResponse.getData();
            // 不在国家池商品列表
            downSaleMkuIds = clientUpSaleVOList.stream().filter(Objects::nonNull).filter(vo -> !CountryMkuStatusEnum.UP_SALE.getCode().equals(vo.getCountryMkuStatus())).map(MkuClientUpSaleStatusVO::getMkuId).collect(Collectors.toSet());
        }
        if (CollectionUtils.isNotEmpty(downSaleMkuIds)) {
            for (Long mkuId : downSaleMkuIds) {
                resultMap.put(mkuId, this.getFailedResDTO(mkuId,ProductMessageEnum.MCP40002));
            }
        }
        return downSaleMkuIds;
    }


    /**
     * 过滤不在客户池的 MKU。
     * @param req 请求对象，包含 MKU Ids。
     * @param mkuFixedSkuMap 已经在客户池的 MKU Id 和对应的 SKU Id 映射。
     * @param resultMap 过滤结果，MKU Id 和对应的 IscMkuAvailableSaleResDTO 对象的映射。
     */
    private void filterNoCustomerPoolMku(IscMkuAvailableSaleReq req, Map<Long, Long> mkuFixedSkuMap, Map<Long, IscMkuAvailableSaleResDTO> resultMap) {
        if (req.getMkuIds().size() != mkuFixedSkuMap.size()) {
            for (Long mkuId : req.getMkuIds()) {
                if (!mkuFixedSkuMap.containsKey(mkuId)) {
                    resultMap.put(mkuId, this.getFailedResDTO(mkuId, ProductMessageEnum.MCT50001));
                }
            }
        }
    }

    /**
     * 根据给定的 MKU ID 集合生成一个空的可用销售结果映射。
     * @param mkuIds MKU ID 集合
     * @return 包含每个 MKU ID 的空可用销售结果的映射
     */
    private Map<Long, IscMkuAvailableSaleResDTO> emptyResultMap(Set<Long> mkuIds) {
       return mkuIds.stream()
               .map(mkuId -> this.getFailedResDTO(mkuId, ProductMessageEnum.MCT50001))
               .collect(Collectors.toMap(IscMkuAvailableSaleResDTO::getMkuId, Function.identity()));
    }

    private IscMkuAvailableSaleResDTO getFailedResDTO(Long mkuId, ProductMessageEnum messageEnum) {
        return IscMkuAvailableSaleResDTO.buildFailed(mkuId, messageEnum.getCode(), messageEnum.getMsg());
    }

    @Override
    public void run(String... args) throws Exception {
//        IscMkuAvailableSaleReq input = new IscMkuAvailableSaleReq();
//        input.setClientCode("yPpvH0qjoPodSR8M4LCb");
//        input.setMkuIds(Sets.newHashSet(50000000027L, 50000000055L, 50000000099L, 50000000125L, 50000000024L, 50000000060L));
//
//        Map<Long, IscMkuAvailableSaleResDTO> response = this.queryMkuAvailable(input);
//        log.info("response={}", JSONObject.toJSONString(response));
    }
}
