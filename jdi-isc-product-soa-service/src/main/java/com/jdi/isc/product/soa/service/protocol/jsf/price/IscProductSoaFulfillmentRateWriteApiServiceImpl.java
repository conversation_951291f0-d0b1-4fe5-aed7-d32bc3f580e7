package com.jdi.isc.product.soa.service.protocol.jsf.price;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.price.IscProductSoaFulfillmentRateWriteApiService;
import com.jdi.isc.product.soa.api.price.res.FulfillmentRateApiDTO;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.price.biz.FulfillmentRateVO;
import com.jdi.isc.product.soa.service.manage.price.FulfillmentRateManageService;
import com.jdi.isc.product.soa.service.mapstruct.price.FulfillmentRateConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: 履约费率表api服务实现
 * @Author: zhaokun51
 * @Date: 2024/10/29 21:47
 **/

@Slf4j
@Service
public class IscProductSoaFulfillmentRateWriteApiServiceImpl implements IscProductSoaFulfillmentRateWriteApiService {

    @Resource
    private FulfillmentRateManageService fulfillmentRateManageService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> saveOrUpdate(FulfillmentRateApiDTO input) {
        FulfillmentRateVO rateVO = FulfillmentRateConvert.INSTANCE.apiDTO2Vo(input);
        ApiInitUtils.init(input);
        return DataResponse.success(fulfillmentRateManageService.saveOrUpdate(rateVO));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> deleteById(Long id) {
        return DataResponse.success(fulfillmentRateManageService.deleteById(id));
    }
}
