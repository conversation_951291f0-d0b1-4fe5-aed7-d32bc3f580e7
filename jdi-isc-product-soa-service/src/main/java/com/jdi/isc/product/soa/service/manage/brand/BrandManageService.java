package com.jdi.isc.product.soa.service.manage.brand;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.domain.brand.biz.BrandCountryVO;
import com.jdi.isc.product.soa.domain.brand.biz.BrandPageVO;
import com.jdi.isc.product.soa.domain.brand.biz.BrandVO;
import com.jdi.isc.product.soa.domain.brand.po.BrandCountryPO;

import java.util.List;

/**
 * @Description: 品牌类目关系数据维护服务
 * @Author: taxuezheng1
 * @Date: 2024/07/25 16:32
 **/

public interface BrandManageService {

    /**
     * 保存、更新
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<Boolean> saveOrUpdate(BrandVO input);

    /**
     * 详情
     */
    BrandVO detail(BrandVO brandVO);

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    PageInfo<BrandPageVO.Response> pageSearch(BrandPageVO.Request input);

    /**
     * 查询过期的品牌
     */
    List<BrandVO> queryExpiredBrand(Long expiredStartTime,Long expiredEndTime);

    /**
     * 校验多语言名称是否重复
     * @param pageVO
     * @return
     */
    boolean checkLangName(BrandPageVO.Request pageVO);


    List<BrandCountryVO> queryBrandCountry(BrandCountryVO brandCountryVO);

    <T> void setBrandName(List<T> list, String brandIdFieldName, String brandNameFieldName);
}
