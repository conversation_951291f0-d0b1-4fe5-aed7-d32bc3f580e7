package com.jdi.isc.product.soa.service.manage.approveorder.factory;

import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Maps;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.service.manage.approveorder.template.audit.AbstractAuditApproveOrderExe;
import com.jdi.isc.product.soa.service.manage.approveorder.template.callback.joysky.AbstractJoySkyCallbackMessageExe;
import com.jdi.isc.product.soa.service.manage.approveorder.template.callback.joysky.DefaultJoySkyCallbackMessageExe;
import com.jdi.isc.product.soa.service.manage.approveorder.template.create.AbstractCreateApproveOrderExe;
import com.jdi.isc.product.soa.service.manage.approveorder.template.audit.DefaultAuditApproveOrderExe;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * The type Approve order bean factory.
 *
 * <AUTHOR>
 */
@Component
public class ApproveOrderBeanFactory implements CommandLineRunner {


    @Autowired
    private List<AbstractCreateApproveOrderExe> createApproveOrderExeList;

    @Autowired
    private List<AbstractAuditApproveOrderExe> auditApproveOrderExeList;

    @Autowired
    private List<AbstractJoySkyCallbackMessageExe> joySkyCallbackMessageExeList;

    /**
     * 创建beanMap
     */
    private volatile static Map<Integer, AbstractCreateApproveOrderExe> createBeanMap = Maps.newHashMap();

    /**
     * 审核beanMap
     */
    private volatile static Map<Integer, AbstractAuditApproveOrderExe> auditBeanMap = Maps.newHashMap();

    /**
     * 审核beanMap
     */
    private volatile static Map<Integer, AbstractJoySkyCallbackMessageExe> joySkyCallbackBeanMap = Maps.newHashMap();

    @Override
    public void run(String... args) {
        this.initCreateBeanMap();

        this.initAuditBeanMap();

        this.initJoySkyCallbackBeanMap();
    }

    public static AbstractCreateApproveOrderExe getCreateBean(Integer bizType) {
        if (MapUtils.isEmpty(createBeanMap)) {
            SpringUtil.getBean(ApproveOrderBeanFactory.class).initCreateBeanMap();
        }
        AbstractCreateApproveOrderExe bean = createBeanMap.get(bizType);

        if (bean == null) {
            throw new ProductBizException("AbstractCreateApproveOrderExe is not exist, bizType %s", bizType);
        }

        return bean;
    }

    public static AbstractAuditApproveOrderExe getAuditBean(Integer bizType) {
        if (MapUtils.isEmpty(auditBeanMap)) {
            SpringUtil.getBean(ApproveOrderBeanFactory.class).initAuditBeanMap();
        }
        AbstractAuditApproveOrderExe bean = auditBeanMap.get(bizType);

        if (bean == null) {
            return SpringUtil.getBean(DefaultAuditApproveOrderExe.BEAN_NAME);
        }

        return bean;
    }

    public static AbstractJoySkyCallbackMessageExe getJoySkyCallbackBean(Integer bizType) {
        if (MapUtils.isEmpty(joySkyCallbackBeanMap)) {
            SpringUtil.getBean(ApproveOrderBeanFactory.class).initJoySkyCallbackBeanMap();
        }
        AbstractJoySkyCallbackMessageExe bean = joySkyCallbackBeanMap.get(bizType);

        if (bean == null) {
            return SpringUtil.getBean(DefaultJoySkyCallbackMessageExe.BEAN_NAME);
        }

        return bean;
    }

    public synchronized void initCreateBeanMap() {
        Map<Integer, AbstractCreateApproveOrderExe> tempMap = Maps.newHashMap();
        for (AbstractCreateApproveOrderExe item : createApproveOrderExeList) {
            tempMap.put(item.getBizType(), SpringUtil.getBean(item.getBeanName()));
        }
        createBeanMap = tempMap;
    }

    public synchronized void initAuditBeanMap() {
        Map<Integer, AbstractAuditApproveOrderExe> tempMap = Maps.newHashMap();
        for (AbstractAuditApproveOrderExe item : auditApproveOrderExeList) {
            tempMap.put(item.getBizType(), SpringUtil.getBean(item.getBeanName()));
        }
        auditBeanMap = tempMap;
    }

    private void initJoySkyCallbackBeanMap() {
        Map<Integer, AbstractJoySkyCallbackMessageExe> tempMap = Maps.newHashMap();
        for (AbstractJoySkyCallbackMessageExe item : joySkyCallbackMessageExeList) {
            tempMap.put(item.getBizType(), SpringUtil.getBean(item.getBeanName()));
        }
        joySkyCallbackBeanMap = tempMap;

    }
}
