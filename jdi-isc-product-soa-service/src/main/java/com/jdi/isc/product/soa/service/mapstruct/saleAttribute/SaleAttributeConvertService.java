package com.jdi.isc.product.soa.service.mapstruct.saleAttribute;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.jdi.isc.product.soa.api.common.enums.AttributeTypeEnum;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.domain.common.biz.BaseLangVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.CategorySaleAttributeVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.JdSkuSaleAttributeVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeValueLangVO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeLangPO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributePO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeValueLangPO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeValuePO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售属性转换服务
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
public class SaleAttributeConvertService {

    /**
     * 将中台销售属性JSON字符串转换为JdSkuSaleAttributeDTO列表
     * 同时处理saleAttributes和saleAtts字段，以saleValue去重，取并集
     * 
     * @param saleAttributes 销售属性JSON字符串
     * @param saleAtts 销售属性附加JSON字符串
     * @return 转换后的销售属性VO列表
     */
    public List<JdSkuSaleAttributeVO> convertJdSaleAttributes(String saleAttributes, String saleAtts) {
        log.info("SaleAttributeConvertService.convertJdSaleAttributes saleAttributes: {}, saleAtts: {}", 
                 saleAttributes, saleAtts);
        
        Map<String, JdSkuSaleAttributeVO> saleValueMap = new HashMap<>();
        
         // 处理saleAtts
         if (StringUtils.isNotBlank(saleAtts)) {
            try {
                List<JdSkuSaleAttributeVO> attrs = JSON.parseObject(saleAtts, 
                    new TypeReference<List<JdSkuSaleAttributeVO>>() {});
                if (CollectionUtils.isNotEmpty(attrs)) {
                    for (JdSkuSaleAttributeVO attr : attrs) {
                        if (StringUtils.isNotBlank(attr.getSaleValue())) {
                            saleValueMap.put(attr.getSaleValue(), attr);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析saleAtts失败: {}", saleAtts, e);
            }
        }
        
        // 处理saleAttributes
        if (StringUtils.isNotBlank(saleAttributes)) {
            try {
                List<JdSkuSaleAttributeVO> attrs = JSON.parseObject(saleAttributes, 
                    new TypeReference<List<JdSkuSaleAttributeVO>>() {});
                if (CollectionUtils.isNotEmpty(attrs)) {
                    for (JdSkuSaleAttributeVO attr : attrs) {
                        if (StringUtils.isNotBlank(attr.getSaleValue())) {
                            saleValueMap.put(attr.getSaleValue(), attr);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析saleAttributes失败: {}", saleAttributes, e);
            }
        }
        
        
        List<JdSkuSaleAttributeVO> result = new ArrayList<>(saleValueMap.values());
        log.info("SaleAttributeConvertService.convertJdSaleAttributes 转换结果: {}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 将SaleAttributePO列表转换为PropertyVO列表
     * 
     * @param saleAttributes 销售属性PO列表
     * @param saleAttributeNameLangMap 销售属性name多语言MAP，key：saleAttributeCode、value：对应的语言值langName
     * @return PropertyVO列表
     */
    public List<PropertyVO> convertToPropertyVOList(List<SaleAttributePO> saleAttributes,
                                                    Map<String, String> saleAttributeNameLangMap) {
        if (CollectionUtils.isEmpty(saleAttributes)) {
            return new ArrayList<>();
        }
        
        // 构建多语言映射
        List<PropertyVO> result = new ArrayList<>();
        for (SaleAttributePO saleAttribute : saleAttributes) {
            PropertyVO propertyVO = new PropertyVO();
            propertyVO.setAttributeId(saleAttribute.getId());
            
            // 优先使用多语言名称，否则使用默认名称
            String attributeName = saleAttributeNameLangMap.getOrDefault(saleAttribute.getSaleAttributeName(),
                                                       saleAttribute.getSaleAttributeName());
            propertyVO.setAttributeName(attributeName);
            propertyVO.setSort(saleAttribute.getSort());
            
            // 设置为销售属性类型
            propertyVO.setAttributeType(AttributeTypeEnum.SELL.getCode());
            // 设置是图销还是文销
            propertyVO.setAttributeInputType(saleAttribute.getSaleAttributeType());
            
            propertyVO.setRequired(false); // 销售属性默认非必填
            propertyVO.setPropertyValueVOList(new ArrayList<>()); // 初始化为空列表
            
            result.add(propertyVO);
        }
        
        return result;
    }

    /**
     * 将SaleAttributeValuePO列表转换为PropertyValueVO列表
     * 
     * @param saleAttributeValues 销售属性值PO列表
     * @param saleAttributeValueLangsMap key:saleAttributeValueId、value：多语言翻译list
     * @param lang 语言编码
     * @param needAll 是否需要全部多语言信息
     * @return PropertyValueVO列表
     */
    public List<PropertyValueVO> convertToPropertyValueVOList(List<SaleAttributeValuePO> saleAttributeValues,
                                                              Map<Long, List<SaleAttributeValueLangVO>> saleAttributeValueLangsMap,
                                                              String lang, boolean needAll) {
        log.info("SaleAttributeConvertService.convertToPropertyValueVOList 入参: saleAttributeValues size={}, lang={}, needAll={}", 
                CollectionUtils.isEmpty(saleAttributeValues) ? 0 : saleAttributeValues.size(), lang, needAll);
        
        if (CollectionUtils.isEmpty(saleAttributeValues)) {
            return new ArrayList<>();
        }
        
        // 构建当前语言的映射
        Map<Long, String> langMap = new HashMap<>();
        if (MapUtils.isNotEmpty(saleAttributeValueLangsMap)) {
            for (Map.Entry<Long, List<SaleAttributeValueLangVO>> entry : saleAttributeValueLangsMap.entrySet()) {
                List<SaleAttributeValueLangVO> langList = entry.getValue();
                if (CollectionUtils.isNotEmpty(langList)) {
                    for (SaleAttributeValueLangVO langVO : langList) {
                        if (lang.equals(langVO.getLang())) {
                            langMap.put(entry.getKey(), langVO.getLangName());
                            break;
                        }
                    }
                }
            }
        }
        
        List<PropertyValueVO> result = new ArrayList<>();
        for (SaleAttributeValuePO saleAttributeValue : saleAttributeValues) {
            PropertyValueVO propertyValueVO = new PropertyValueVO();
            propertyValueVO.setAttributeId(saleAttributeValue.getSaleAttributeId());
            propertyValueVO.setAttributeValueId(saleAttributeValue.getId());
            
            // 优先使用多语言名称，否则使用默认名称
            String attributeValueName = langMap.getOrDefault(saleAttributeValue.getId(), 
                                                           saleAttributeValue.getSaleAttributeValueName());
            propertyValueVO.setAttributeValueName(attributeValueName);
            propertyValueVO.setSort(saleAttributeValue.getSort());
            propertyValueVO.setLang(lang);
            propertyValueVO.setLangName(attributeValueName);
            propertyValueVO.setSelected(true); // 新版销售属性不存在没有被选择的属性值

            // 当needAll=true时，设置当前AttributeValue的全部多语言翻译
            if (needAll) {
                List<BaseLangVO> langList = new ArrayList<>();
                List<SaleAttributeValueLangVO> allLangs = saleAttributeValueLangsMap.get(saleAttributeValue.getId());
                
                if (CollectionUtils.isNotEmpty(allLangs)) {
                    for (SaleAttributeValueLangVO langVO : allLangs) {
                        if (StringUtils.isNotBlank(langVO.getLang()) && StringUtils.isNotBlank(langVO.getLangName())) {
                            BaseLangVO baseLangVO = new BaseLangVO();
                            baseLangVO.setId(langVO.getId());
                            baseLangVO.setLang(langVO.getLang());
                            baseLangVO.setLangName(langVO.getLangName());
                            langList.add(baseLangVO);
                        }
                    }
                }
                
                // 如果没有多语言数据，至少添加中文兜底
                if (CollectionUtils.isEmpty(langList) && StringUtils.isNotBlank(saleAttributeValue.getSaleAttributeValueName())) {
                    BaseLangVO baseLangVO = new BaseLangVO();
                    baseLangVO.setLang(Constant.DEFAULT_LANG);
                    baseLangVO.setLangName(saleAttributeValue.getSaleAttributeValueName());
                    langList.add(baseLangVO);
                }
                
                propertyValueVO.setLangList(langList);
                log.info("SaleAttributeConvertService.convertToPropertyValueVOList 设置多语言, attributeValueId={}, langList {}",
                        saleAttributeValue.getId(), JSON.toJSONString(langList));
            }
            
            result.add(propertyValueVO);
        }
        
        log.info("SaleAttributeConvertService.convertToPropertyValueVOList 转换完成, result size={}", result.size());
        return result;
    }

    /**
     * 根据PropertyValueVO列表组装完整的PropertyVO列表
     * 
     * @param propertyVOList 销售属性列表
     * @param propertyValueVOList 销售属性值列表
     * @return 组装后的PropertyVO列表
     */
    public List<PropertyVO> assemblePropertyVOWithValues(List<PropertyVO> propertyVOList,
                                                        List<PropertyValueVO> propertyValueVOList) {
        if (CollectionUtils.isEmpty(propertyVOList)) {
            return new ArrayList<>();
        }
        
        // 按属性ID分组属性值
        Map<Long, List<PropertyValueVO>> valueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(propertyValueVOList)) {
            valueMap = propertyValueVOList.stream()
                    .collect(Collectors.groupingBy(PropertyValueVO::getAttributeId));
        }
        
        // 为每个属性设置属性值列表
        for (PropertyVO propertyVO : propertyVOList) {
            List<PropertyValueVO> values = valueMap.getOrDefault(propertyVO.getAttributeId(), new ArrayList<>());
            // 按sort字段排序
            values.sort(Comparator.comparing(PropertyValueVO::getSort, Comparator.nullsLast(Integer::compareTo)));
            propertyVO.setPropertyValueVOList(values);
        }
        
        return propertyVOList;
    }

    /**
     * 将CategorySaleAttributeVO列表转换为PropertyVO列表
     * attributeInputType用来接收saleAttributeType
     * 
     * @param categorySaleAttributes 类目销售属性VO列表
     * @return PropertyVO列表
     */
    public List<PropertyVO> convertCategorySaleAttributeToPropertyVO(List<CategorySaleAttributeVO> categorySaleAttributes) {
        if (CollectionUtils.isEmpty(categorySaleAttributes)) {
            return new ArrayList<>();
        }
        
        List<PropertyVO> result = new ArrayList<>();
        for (CategorySaleAttributeVO categoryAttr : categorySaleAttributes) {
            PropertyVO propertyVO = new PropertyVO();
            
            // 设置属性ID（使用数据库存储后的销售属性ID）
            propertyVO.setAttributeId(categoryAttr.getSaleAttributeId());
            
            // 设置属性名称
            propertyVO.setAttributeName(categoryAttr.getSaleAttributeName());
            
            // attributeInputType用来接收saleAttributeType
            propertyVO.setAttributeInputType(categoryAttr.getSaleAttributeType());
            
            // 设置排序
            propertyVO.setSort(categoryAttr.getSort());
            
            // 设置为销售属性类型
            propertyVO.setAttributeType(AttributeTypeEnum.SELL.getCode());
            
            // 销售属性默认非必填
            propertyVO.setRequired(false);
            
            // 初始化空的属性值列表
            propertyVO.setPropertyValueVOList(new ArrayList<>());
            
            result.add(propertyVO);
        }
        
        log.info("SaleAttributeConvertService.convertCategorySaleAttributeToPropertyVO 转换完成, " +
                "input size: {}, output size: {}", categorySaleAttributes.size(), result.size());
        
        return result;
    }

    /**
     * 将SaleAttributePO列表转换为PropertyVO列表（多语言版本）
     * 
     * @param saleAttributes 销售属性PO列表
     * @param saleAttributeNameLangMap 销售属性name多语言MAP，key1：saleAttributeCode、key2：lang、value：对应的语言值langName
     * @return PropertyVO列表
     */
    public List<PropertyVO> convertToPropertyVOListWithLangList(List<SaleAttributePO> saleAttributes,
                                                               Map<String, Map<String, String>> saleAttributeNameLangMap) {
        if (CollectionUtils.isEmpty(saleAttributes)) {
            return new ArrayList<>();
        }

        List<PropertyVO> result = new ArrayList<>();
        for (SaleAttributePO saleAttribute : saleAttributes) {
            PropertyVO propertyVO = new PropertyVO();
            propertyVO.setAttributeId(saleAttribute.getId());
            propertyVO.setAttributeName(saleAttribute.getSaleAttributeName());
            propertyVO.setAttributeType(AttributeTypeEnum.SELL.getCode());
            propertyVO.setAttributeInputType(saleAttribute.getSaleAttributeType());
            propertyVO.setSort(saleAttribute.getSort());
            propertyVO.setRequired(false);
            propertyVO.setPropertyValueVOList(new ArrayList<>());
            
            // 设置多语言信息
            if (MapUtils.isNotEmpty(saleAttributeNameLangMap)) {
                Map<String, String> langMap = saleAttributeNameLangMap.get(saleAttribute.getSaleAttributeName());
                if (MapUtils.isNotEmpty(langMap)) {
                    List<BaseLangVO> langList = new ArrayList<>();
                    for (Map.Entry<String, String> entry : langMap.entrySet()) {
                        if (StringUtils.isNotBlank(entry.getKey()) && StringUtils.isNotBlank(entry.getValue())) {
                            BaseLangVO baseLangVO = new BaseLangVO();
                            baseLangVO.setLang(entry.getKey());
                            baseLangVO.setLangName(entry.getValue());
                            langList.add(baseLangVO);
                        }
                    }
                    propertyVO.setLangList(langList);
                }
            }
            
            result.add(propertyVO);
        }

        return result;
    }

    /**
     * 将销售属性值转换为PropertyValueVO列表（多语言版本，包含全部语言信息）
     * 
     * @param saleAttributeValues 销售属性值列表
     * @param saleAttributeValueLangsMap 销售属性值多语言信息
     * @return PropertyValueVO列表
     */
    public List<PropertyValueVO> convertToPropertyValueVOListWithLangList(List<SaleAttributeValuePO> saleAttributeValues,
                                                                          Map<Long, List<SaleAttributeValueLangVO>> saleAttributeValueLangsMap) {
        log.info("SaleAttributeConvertService.convertToPropertyValueVOListWithLangList 入参: saleAttributeValues size={}", 
                CollectionUtils.isEmpty(saleAttributeValues) ? 0 : saleAttributeValues.size());
        
        if (CollectionUtils.isEmpty(saleAttributeValues)) {
            return new ArrayList<>();
        }
        
        List<PropertyValueVO> result = new ArrayList<>();
        for (SaleAttributeValuePO saleAttributeValue : saleAttributeValues) {
            PropertyValueVO propertyValueVO = new PropertyValueVO();
            propertyValueVO.setAttributeId(saleAttributeValue.getSaleAttributeId());
            propertyValueVO.setAttributeValueId(saleAttributeValue.getId());
            propertyValueVO.setAttributeValueName(saleAttributeValue.getSaleAttributeValueName());
            propertyValueVO.setSort(saleAttributeValue.getSort());
            propertyValueVO.setSelected(true); // 新版销售属性不存在没有被选择的属性值

            // 设置当前AttributeValue的全部多语言翻译
            List<BaseLangVO> langList = new ArrayList<>();
            List<SaleAttributeValueLangVO> allLangs = saleAttributeValueLangsMap.get(saleAttributeValue.getId());
            
            if (CollectionUtils.isNotEmpty(allLangs)) {
                for (SaleAttributeValueLangVO langVO : allLangs) {
                    if (StringUtils.isNotBlank(langVO.getLang()) && StringUtils.isNotBlank(langVO.getLangName())) {
                        BaseLangVO baseLangVO = new BaseLangVO();
                        baseLangVO.setLang(langVO.getLang());
                        baseLangVO.setLangName(langVO.getLangName());
                        langList.add(baseLangVO);
                    }
                }
            }
            
            // 如果没有多语言数据，至少添加中文兜底
            if (CollectionUtils.isEmpty(langList) && StringUtils.isNotBlank(saleAttributeValue.getSaleAttributeValueName())) {
                BaseLangVO baseLangVO = new BaseLangVO();
                baseLangVO.setLang(Constant.DEFAULT_LANG);
                baseLangVO.setLangName(saleAttributeValue.getSaleAttributeValueName());
                langList.add(baseLangVO);
            }
            
            propertyValueVO.setLangList(langList);
            log.info("SaleAttributeConvertService.convertToPropertyValueVOListWithLangList 设置多语言, attributeValueId={}, langList {}",
                    saleAttributeValue.getId(), JSON.toJSONString(langList));
            
            result.add(propertyValueVO);
        }
        
        log.info("SaleAttributeConvertService.convertToPropertyValueVOListWithLangList 转换完成, result size={}", result.size());
        return result;
    }
} 