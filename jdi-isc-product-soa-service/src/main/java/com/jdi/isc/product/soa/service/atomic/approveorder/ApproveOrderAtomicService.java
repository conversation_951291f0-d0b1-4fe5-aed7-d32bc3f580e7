package com.jdi.isc.product.soa.service.atomic.approveorder;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jdi.isc.biz.component.api.joysky.res.JoySkyDetailInfoDTO;
import com.jdi.isc.product.soa.api.approveorder.common.ApproveOrderStatusEnum;
import com.jdi.isc.product.soa.api.approveorder.common.AuditActionEnum;
import com.jdi.isc.product.soa.api.approveorder.common.AuditStatusEnum;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderPageQueryDTO;
import com.jdi.isc.product.soa.api.approveorder.res.ApproveOrderPageResDTO;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.approveorder.biz.ApproveCallbackVO;
import com.jdi.isc.product.soa.domain.approveorder.biz.ApproveNodeVO;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveOrderPO;
import com.jdi.isc.product.soa.repository.mapper.approveorder.ApproveOrderBaseMapper;
import com.jdi.isc.product.soa.service.mapstruct.approveorder.ApproveOrderConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品审核原子服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ApproveOrderAtomicService extends ServiceImpl<ApproveOrderBaseMapper, ApproveOrderPO> {

    /**
     * 分页查询
     *
     * @param input 查询条件
     * @return 分页查询结果 list
     */
    public List<ApproveOrderPageResDTO> pageSearch(ApproveOrderPageQueryDTO input) {
        input.setOffset((input.getIndex() - 1) * input.getSize());
        return super.getBaseMapper().pageSearch(input);
    }

    /**
     * 分页查询的列表总数
     *
     * @param input 查询条件
     * @return 总条数 long
     */
    public long pageSearchTotal(ApproveOrderPageQueryDTO input) {
        return super.getBaseMapper().pageSearchTotal(input);
    }

    /**
     * 未删除的对象
     *
     * @param id id
     * @return 对象 approve order po
     */
    public ApproveOrderPO getValidById(Long id) {
        LambdaQueryWrapper<ApproveOrderPO> wrapper = Wrappers.<ApproveOrderPO>lambdaQuery()
                .eq(ApproveOrderPO::getId, id)
                .eq(ApproveOrderPO::getYn, YnEnum.YES.getCode());

        ApproveOrderPO order = super.getOne(wrapper);
        if (order == null) {
            throw new ProductBizException("找不到审批单. id=" + id);
        }

        return super.getOne(wrapper);
    }

    /**
     * Insert.
     *
     * @param order the order
     */
    public ApproveOrderPO insert(ApproveOrderDTO order) {
        ApproveOrderPO po = ApproveOrderConvert.INSTANCE.convert(order);

        if (po.getId() != null) {
            throw new ProductBizException("id不能为空");
        }

        super.save(po);
        return po;
    }

    /**
     * 根据业务类型、业务ID和状态查询审批订单列表
     *
     * @param bizType 业务类型编号
     * @param bizId   业务唯一标识
     * @param status  审批状态代码
     * @return 符合查询条件的审批订单集合 ，可能为空列表
     */
    public List<ApproveOrderPO> listByBizTypeAndBizIdAndStatus(Integer bizType, String bizId, Integer status) {
        LambdaQueryWrapper<ApproveOrderPO> wrapper = Wrappers.<ApproveOrderPO>lambdaQuery()
                .eq(ApproveOrderPO::getBizType, bizType)
                .eq(ApproveOrderPO::getBizId, bizId)
                .eq(ApproveOrderPO::getStatus, status)
                .eq(ApproveOrderPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    /**
     * 回填joySky信息.
     *
     * @param order            the order
     * @param joySkyDetailInfo the joy sky detail info
     */
    public void updateApplyCode(ApproveOrderPO order, JoySkyDetailInfoDTO joySkyDetailInfo) {

        LambdaUpdateWrapper<ApproveOrderPO> wrapper = Wrappers.<ApproveOrderPO>lambdaUpdate()

                // 流程信息
                .set(ApproveOrderPO::getProcessInstanceId, joySkyDetailInfo.getProcessInstanceId())
                .set(ApproveOrderPO::getApplyCode, joySkyDetailInfo.getApplyCode())
                .set(ApproveOrderPO::getAuditStatus, AuditStatusEnum.STATUS_2.getCode())

                // 通用信息
                .set(ApproveOrderPO::getUpdater, order.getUpdater())
                .set(ApproveOrderPO::getUpdateTime, new Date().getTime())

                // 乐关锁
                .eq(ApproveOrderPO::getId, order.getId())
                .eq(ApproveOrderPO::getVersion, order.getVersion());

        if (CollectionUtils.isNotEmpty(joySkyDetailInfo.getTodoList())) {
            String currentAuditor = String.join(",", joySkyDetailInfo.getTodoList());
            wrapper.set(ApproveOrderPO::getCurrentNodeErp, currentAuditor)
                    .set(ApproveOrderPO::getCurrentNodeAuditor, StringUtils.EMPTY)
                    .set(ApproveOrderPO::getCurrentNodeAuditStatus, AuditStatusEnum.STATUS_2.getCode());
        } else {
            //TODO 埋点 报警
        }

        this.updateByWrapper(order, wrapper);
    }

    /**
     * 更新审批单节点信息
     * @param order 审批单实体对象
     * @param node 审批节点数据传输对象
     */
    public void updateAuditNodeInfo(ApproveOrderPO order, ApproveNodeVO node) {

        Integer auditStatus = node.getAuditStatus();

        LambdaUpdateWrapper<ApproveOrderPO> wrapper = Wrappers.<ApproveOrderPO>lambdaUpdate()

                // 审核状态
                .set(node.getAuditStatus() != null, ApproveOrderPO::getAuditStatus, node.getAuditStatus())
                .set(ApproveOrderPO::getPreApproveFlag, node.getAuditAction())
                .set(ApproveOrderPO::getPreApproveComment, node.getApproveComment() == null ? StringUtils.EMPTY : node.getApproveComment())

                .set(StringUtils.isNotBlank(node.getCurrentNodeErp()), ApproveOrderPO::getCurrentNodeErp, node.getCurrentNodeErp())
                .set(ApproveOrderPO::getCurrentNodeAuditor, StringUtils.EMPTY)
                .set(node.getCurrentNodeAuditStatus() != null, ApproveOrderPO::getCurrentNodeAuditStatus, node.getCurrentNodeAuditStatus())

                // 通用信息
                .set(ApproveOrderPO::getUpdater, order.getUpdater())
                .set(ApproveOrderPO::getUpdateTime, new Date().getTime())

                // 乐关锁
                .eq(ApproveOrderPO::getId, order.getId())
                .eq(ApproveOrderPO::getVersion, order.getVersion());

        if (auditStatus != null) {
            if (auditStatus == AuditStatusEnum.STATUS_4.getCode() || auditStatus == AuditStatusEnum.STATUS_5.getCode() || auditStatus == AuditStatusEnum.STATUS_6.getCode()) {
                wrapper.set(ApproveOrderPO::getStatus, ApproveOrderStatusEnum.COMPLETED.getCode());
            }
        }

        this.updateByWrapper(order, wrapper);
    }

    private void updateByWrapper(ApproveOrderPO order, LambdaUpdateWrapper<ApproveOrderPO> wrapper) {
        boolean update = super.update(wrapper);
        if (!update) {
            log.warn("审批单正在进行其他操作，请稍后再试, id ={}, applyCode={}", order.getId(), order.getApplyCode());
            throw new ProductBizException("审批单正在进行其他操作，请稍后再试");
        }
    }

    /**
     * 根据审核动作，更新审核状态.
     *
     * @param order       the order
     * @param auditStatus the audit status
     */
    public void updateAuditStatus(ApproveOrderPO order, Integer auditStatus, Integer nodeAuditStatus, Integer actionType, String approveComment) {

        if (StringUtils.isEmpty(approveComment)) {
            approveComment = StringUtils.EMPTY;
        }

        LambdaUpdateWrapper<ApproveOrderPO> wrapper = Wrappers.<ApproveOrderPO>lambdaUpdate()

                // 审核状态
                .set(auditStatus !=  null, ApproveOrderPO::getAuditStatus, auditStatus)
                .set(ApproveOrderPO::getCurrentNodeAuditStatus, nodeAuditStatus)
                .set(ApproveOrderPO::getPreApproveComment, approveComment)
                .set(ApproveOrderPO::getPreApproveFlag, actionType)

                // 通用信息
                .set(ApproveOrderPO::getUpdater, order.getUpdater())
                .set(ApproveOrderPO::getUpdateTime, new Date().getTime())

                // 乐关锁
                .eq(ApproveOrderPO::getId, order.getId())
                .eq(ApproveOrderPO::getVersion, order.getVersion());

        this.updateByWrapper(order, wrapper);
    }

    /**
     * Gets next audit status by action.
     *
     * @param auditAction the audit action
     * @return the next audit status by action
     */
    public int getNextAuditStatusByAction(Integer auditAction) {
        AuditActionEnum auditActionEnum = AuditActionEnum.codeOf(auditAction);

        if (auditActionEnum == AuditActionEnum.PASS) {
            return AuditStatusEnum.STATUS_6.getCode();
        } else if (auditActionEnum == AuditActionEnum.REJECT) {
            return AuditStatusEnum.STATUS_4.getCode();
        } else if (auditActionEnum == AuditActionEnum.CANCEL) {
            return AuditStatusEnum.STATUS_5.getCode();
        } else {
            throw new ProductBizException("不支持的审核动作. auditAction=%s", auditAction);
        }
    }

    /**
     * 根据审核动作获取进行中的状态
     *
     * @param auditAction 审核动作编码，对应AuditActionEnum中的枚举值
     * @return 返回对应审核状态码 ，取值自AuditStatusEnum中的枚举值
     */
    public int getIngAuditStatusByAction(Integer auditAction) {
        AuditActionEnum auditActionEnum = AuditActionEnum.codeOf(auditAction);

        if (auditActionEnum == AuditActionEnum.PASS) {
            return AuditStatusEnum.STATUS_3.getCode();
        } else if (auditActionEnum == AuditActionEnum.REJECT) {
            return AuditStatusEnum.STATUS_3.getCode();
        } else if (auditActionEnum == AuditActionEnum.CANCEL) {
            return AuditStatusEnum.STATUS_3.getCode();
        } else {
            throw new ProductBizException("不支持的审核动作. auditAction=%s", auditAction);
        }
    }

    /**
     * Gets by process type and process instance id.
     *
     * @param flowType          the flow type
     * @param processInstanceId the process instance id
     * @return the by process type and process instance id
     */
    public ApproveOrderPO getByProcessTypeAndProcessInstanceId(Integer flowType, String processInstanceId) {
        LambdaQueryWrapper<ApproveOrderPO> wrapper = Wrappers.<ApproveOrderPO>lambdaQuery()
                .eq(ApproveOrderPO::getProcessInstanceId, processInstanceId)
                .eq(ApproveOrderPO::getFlowType, flowType)
                .eq(ApproveOrderPO::getYn, YnEnum.YES.getCode());

        ApproveOrderPO order = super.getOne(wrapper);
        if (order == null) {
            throw new ProductBizException("找不到审批单. processInstanceId=" + processInstanceId);
        }

        return super.getOne(wrapper);
    }

    /**
     * 获取最新的审核状态
     */
    public Map<String, ApproveOrderPO> getLatestAuditStatusMap(Integer bizType, List<String> bizIds) {
        Preconditions.checkArgument(bizType != null, "bizType不能为空");

        if (CollectionUtils.isEmpty(bizIds)) {
            return Maps.newHashMap();
        }

        List<ApproveOrderPO> list = super.getBaseMapper().selectLatestModels(bizType, bizIds);

        return list.stream().collect(Collectors.toMap(ApproveOrderPO::getBizId, Function.identity(), (v1, v2) -> v1));
    }

    public void batchDelete(List<ApproveOrderPO> list, String updater) {

        if (CollectionUtils.isEmpty(list)) {
            log.warn("batchDelete, list is empty");
            return;
        }
        for (ApproveOrderPO order : list) {
            LambdaUpdateWrapper<ApproveOrderPO> wrapper = Wrappers.<ApproveOrderPO>lambdaUpdate()

                    // 审核状态
                    .set(ApproveOrderPO::getYn, YnEnum.NO.getCode())

                    // 通用信息
                    .set(ApproveOrderPO::getUpdater, updater)
                    .set(ApproveOrderPO::getUpdateTime, new Date().getTime())

                    // 乐关锁
                    .eq(ApproveOrderPO::getId, order.getId())
                    .eq(ApproveOrderPO::getVersion, order.getVersion());

            this.updateByWrapper(order, wrapper);
        }
    }

    public void updateCallbackFailInfo(ApproveOrderPO order, ApproveCallbackVO callbackVO) {

        String callbackFailMessage = null;
        Integer callbackFailCount = 0;

        if (callbackVO.getCallbackFailAction() == null) {
            throw new ProductBizException("审核动作不能为空");
        }

        if (StringUtils.isEmpty(callbackVO.getCallbackFailMessage())) {
            callbackFailMessage = StringUtils.EMPTY;
        }


        LambdaUpdateWrapper<ApproveOrderPO> wrapper = Wrappers.<ApproveOrderPO>lambdaUpdate()

                .set(callbackVO.getStatus() != null, ApproveOrderPO::getStatus, callbackVO.getStatus())

                .set(ApproveOrderPO::getCallbackFailMessage, callbackFailMessage)
                .set(ApproveOrderPO::getCallbackFailCount, callbackFailCount)
                .set(callbackVO.getCallbackFailAction() != null, ApproveOrderPO::getCallbackFailAction, callbackVO.getCallbackFailAction())
                .set(StringUtils.isEmpty(callbackVO.getCallbackFailParam()), ApproveOrderPO::getCallbackFailParam, callbackVO.getCallbackFailParam())

                // 通用信息
                .set(ApproveOrderPO::getUpdater, LoginContextHolder.getOperator())
                .set(ApproveOrderPO::getUpdateTime, new Date().getTime())

                // 乐关锁
                .eq(ApproveOrderPO::getId, order.getId())
                .eq(ApproveOrderPO::getVersion, order.getVersion());

        this.updateByWrapper(order, wrapper);

    }

    public List<ApproveOrderPO> listTimeoutData() {
        List<ApproveOrderPO> result = Lists.newArrayList();
        // 1. 查询所有提交中的状态

        LambdaQueryWrapper<ApproveOrderPO> wrapper1 = Wrappers.<ApproveOrderPO>lambdaQuery()
                .select(ApproveOrderPO::getId, ApproveOrderPO::getVersion)
                .eq(ApproveOrderPO::getAuditStatus, AuditStatusEnum.STATUS_1.getCode())
                .eq(ApproveOrderPO::getStatus, ApproveOrderStatusEnum.PROCESSING.getCode())
                .eq(ApproveOrderPO::getYn, YnEnum.YES.getCode())
                .lt(ApproveOrderPO::getUpdateTime, new Date().getTime() - 1000 * 60 * 60);

        List<ApproveOrderPO> list1 = super.getBaseMapper().selectList(wrapper1);

        if (CollectionUtils.isNotEmpty(list1)) {
            result.addAll(list1);
        }

        // 2. 查询所有审核节点状态是审核中的数据
        LambdaQueryWrapper<ApproveOrderPO> wrapper2 = Wrappers.<ApproveOrderPO>lambdaQuery()
                .select(ApproveOrderPO::getId, ApproveOrderPO::getVersion)
                .eq(ApproveOrderPO::getCurrentNodeAuditStatus, AuditStatusEnum.STATUS_3.getCode())
                .eq(ApproveOrderPO::getStatus, ApproveOrderStatusEnum.PROCESSING.getCode())
                .eq(ApproveOrderPO::getYn, YnEnum.YES.getCode())
                .lt(ApproveOrderPO::getUpdateTime, new Date().getTime() - 1000 * 60 * 60);

        List<ApproveOrderPO> list2 = super.getBaseMapper().selectList(wrapper2);

        if (CollectionUtils.isNotEmpty(list2)) {
            result.addAll(list2);
        }


        Map<Long, ApproveOrderPO> map = result.stream().collect(Collectors.toMap(ApproveOrderPO::getId, Function.identity(), (v1, v2) -> v1));

        return CollectionUtil.sort(map.values(), Comparator.comparing(ApproveOrderPO::getId, Comparator.nullsLast(Long::compareTo)));
    }
}