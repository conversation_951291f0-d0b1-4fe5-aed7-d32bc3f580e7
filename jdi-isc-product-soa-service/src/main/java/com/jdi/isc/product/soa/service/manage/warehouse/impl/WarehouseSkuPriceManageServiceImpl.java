package com.jdi.isc.product.soa.service.manage.warehouse.impl;

import com.alibaba.fastjson.JSON;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseSkuPriceBatchVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseSkuPriceReqVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseSkuPriceResVO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehouseSkuPricePO;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseSkuPriceAtomicService;
import com.jdi.isc.product.soa.service.manage.warehouse.WarehouseSkuPriceManageService;
import com.jdi.isc.product.soa.service.mapstruct.warehouse.WarehouseSkuConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author：xubing82
 * @date：2025/8/7 11:20
 * @description：仓报价查询服务实现类
 */
@Slf4j
@Service
public class WarehouseSkuPriceManageServiceImpl implements WarehouseSkuPriceManageService {

    @Autowired
    private WarehouseSkuPriceAtomicService warehouseSkuPriceAtomicService;

    @Override
    public List<WarehouseSkuPriceResVO> batchQueryWarehouseSkuPricesByCondition(WarehouseSkuPriceBatchVO warehouseSkuPriceBatchVO) {

        String countryCode = warehouseSkuPriceBatchVO.getCountryCode();
        List<WarehouseSkuPriceReqVO> warehouseSkuPriceReqList = warehouseSkuPriceBatchVO.getWarehouseSkuPriceReqList();
        if (CollectionUtils.isEmpty(warehouseSkuPriceReqList)) {
            log.warn("WarehouseSkuPriceManageServiceImpl.batchQueryWarehouseSkuPricesByCondition 必填参数warehouseSkuPriceReqList缺失！");
            return Collections.emptyList();
        }

        //请求参数分组
        Map<Long, Set<Long>> warehouseSkuMap = warehouseSkuPriceReqList.stream()
                .collect(Collectors.groupingBy(
                        WarehouseSkuPriceReqVO::getWarehouseId,
                        Collectors.mapping(
                                WarehouseSkuPriceReqVO::getSkuId, // 提取 skuId
                                Collectors.toSet())));

        // 定义结果列表
        List<WarehouseSkuPriceResVO> warehouseSkuPriceResVOList = new ArrayList<>();
        Long yesterday = DateUtil.getStartOfDayBeforeNum(1L);

        // 遍历 warehouseSkuMap
        for (Map.Entry<Long, Set<Long>> entry : warehouseSkuMap.entrySet()) {
            Long warehouseId = entry.getKey();
            Set<Long> skuIds = entry.getValue();

            List<WarehouseSkuPricePO> warehouseSkuPricePOList =
                    warehouseSkuPriceAtomicService.queryWarehouseSkuPriceListByParams(countryCode, warehouseId, skuIds, yesterday);
            if (CollectionUtils.isNotEmpty(warehouseSkuPricePOList)) {
                List<WarehouseSkuPriceResVO> warehouseSkuPriceResVOS = WarehouseSkuConvert.INSTANCE.listPo2ResVo(warehouseSkuPricePOList);
                warehouseSkuPriceResVOList.addAll(warehouseSkuPriceResVOS);
            }
        }

        // 排序获取最新一条仓报价记录
        if (CollectionUtils.isNotEmpty(warehouseSkuPriceResVOList)) {
            // 按照 SKU 分组，并按照 createTime 倒序排序后保留最新的一条记录
            Map<Long, WarehouseSkuPriceResVO> latestSkuPriceMap = warehouseSkuPriceResVOList.stream()
                    .collect(Collectors.toMap(WarehouseSkuPriceResVO::getSkuId, Function.identity(),
                            BinaryOperator.maxBy(Comparator.comparing(WarehouseSkuPriceResVO::getCreateTime))));

            // 将结果转换回 List
            warehouseSkuPriceResVOList = new ArrayList<>(latestSkuPriceMap.values());
        }

        log.info("WarehouseSkuPriceManageServiceImpl.batchQueryWarehouseSkuPricesByCondition request:{}, result:{}", JSON.toJSONString(warehouseSkuPriceBatchVO), JSON.toJSONString(warehouseSkuPriceResVOList));
        return warehouseSkuPriceResVOList;
    }

}
