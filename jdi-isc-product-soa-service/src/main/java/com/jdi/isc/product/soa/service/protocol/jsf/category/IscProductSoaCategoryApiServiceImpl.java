package com.jdi.isc.product.soa.service.protocol.jsf.category;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.category.IscProductSoaCategoryApiService;
import com.jdi.isc.product.soa.api.category.biz.CategoryReadDTO;
import com.jdi.isc.product.soa.api.category.biz.GmsCategoryReqDTO;
import com.jdi.isc.product.soa.api.category.biz.JdCategorySyncReqDTO;
import com.jdi.isc.product.soa.api.wisp.category.biz.CategoryDTO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryVO;
import com.jdi.isc.product.soa.domain.category.biz.JdCategorySyncReqVO;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.mapstruct.category.CategoryConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description：类目服务实现类
 * @Date 2025-05-22
 */
@Slf4j
@Service
public class IscProductSoaCategoryApiServiceImpl implements IscProductSoaCategoryApiService {

    @Resource
    private CategoryOutService categoryOutService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> syncJdCategoryByFirstCatId(JdCategorySyncReqDTO reqDTO) {
        JdCategorySyncReqVO reqVO = CategoryConvert.INSTANCE.syncDto2ReqVO(reqDTO);
        return DataResponse.success(categoryOutService.syncJdCategoryByFirstCatId(reqVO));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<CategoryDTO> getJdCategoryDTO(JdCategorySyncReqDTO reqDTO) {
        JdCategorySyncReqVO reqVO = CategoryConvert.INSTANCE.syncDto2ReqVO(reqDTO);
        CategoryVO jdCategory = categoryOutService.getJdCategory(reqVO);
        CategoryDTO categoryDTO = CategoryConvert.INSTANCE.vo2DTO(jdCategory);
        return DataResponse.success(categoryDTO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> changeCategory(GmsCategoryReqDTO reqDTO) {
        return categoryOutService.changeCategory(reqDTO);
    }

    /** 根据id查询类目信息*/
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, Map<String, String>>> getCategoryById(CategoryReadDTO req) {
        return DataResponse.success(categoryOutService.queryCatLangNameMap(req.getCatId(), req.getLang()));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long,CategoryDTO>> queryCategoryByIds(CategoryReadDTO req) {
        Map<Long, CategoryVO> categoryVOMap = categoryOutService.queryCategoryByIds(req);
        Map<Long, CategoryDTO> categortyDtoMap = CategoryConvert.INSTANCE.mapVo2DTO(categoryVOMap);
        return DataResponse.success(categortyDtoMap);
    }
}
