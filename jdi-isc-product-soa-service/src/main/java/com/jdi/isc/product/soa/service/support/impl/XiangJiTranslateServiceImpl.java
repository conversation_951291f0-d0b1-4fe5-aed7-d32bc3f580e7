package com.jdi.isc.product.soa.service.support.impl;


import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.biz.component.api.enums.RobotTypeEnum;
import com.jdi.isc.biz.component.api.jme.req.SendMessageReqDTO;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.util.CommonUtils;
import com.jdi.isc.product.soa.domain.enums.translate.TranslationEngineEnum;
import com.jdi.isc.product.soa.domain.translate.Result;
import com.jdi.isc.product.soa.domain.translate.TranslateBatchReqVO;
import com.jdi.isc.product.soa.domain.translate.TranslateTextReqVO;
import com.jdi.isc.product.soa.domain.translate.XiangJiTranslationRequest;
import com.jdi.isc.product.soa.rpc.jme.DongDongMessageRcpService;
import com.jdi.isc.product.soa.service.support.TextTranslateService;
import com.jdi.isc.product.soa.service.support.cache.LocalCacheService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.pool.PoolStats;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description：象寄文本翻译服务
 * @Date 2025-01-17
 */
@Slf4j
@Service("xiangJiTranslateService")
public class XiangJiTranslateServiceImpl extends AbstractTranslateService implements TextTranslateService {
    /**
     *文档 ：https://www.xiangjifanyi.com/doc/apiDoc/
     *
     */
    private static final String ACTION = "GetTextTranslate";
    private static final String TEXT_TRANS_KEY = "1568135039";
    private static final String TEXT_USER_KEY = "8933357192";
    private static final String TEXT_TRANS_URL = "http://api.tosoiot.com";
    private static final String TEXT_TRANS_REMAINING_URL = "http://www.tosoiot.com/open/user/amount-query";

    private static final Integer HTTP_TIME_OUT = 60000;
    private static final String AUTO = "AUTO";
    private static final int TEXT_TRANS_REMAINING_TYPE = 2;
    private static final long TEXT_THRESHOLD_COUNT = 500000L;
    @Resource
    private LocalCacheService localCacheService;

    @LafValue("jdi.isc.translate.xiangji.defaultEngine.config")
    private Integer defaultEngine;

    @Value("${spring.profiles.active}")
    protected String systemProfile;

    @LafValue("jdi.isc.translate.xiangji.remaining.count.receivers")
    private Set<String> remainingCountReceivers;

    @Resource
    private RestTemplate xiangjiRestTemplate;

    @Autowired(required = false)
    private PoolingHttpClientConnectionManager xiangjiConnManager;

    @Resource
    private DongDongMessageRcpService dongDongMessageRcpService;

    @Override
    @PFTracing(name = "XiangJiTranslateServiceImpl.translate",metricTags = "xiangji_text")
    public String translate(String input, String sourceLang, String targetLang) {
        CallerInfo callerInfo = Profiler.registerInfo("XiangJiTranslateServiceImpl.translate");
        String result = "";
        try {
            Map<String, String> langMap = localCacheService.xiangJiPlatLangMap();
            if (!langMap.containsKey(targetLang)) {
                CommonUtils.errCallBack("XiangJiTranslateServiceImpl.translate", UmpKeyConstant.BUSINESS_KEY_TRANSLATE_WARNING, String.format("象寄翻译失败:%s ,目标语种%s不存在", input,targetLang));
                return result;
            }

            InputParam inputParam = buildInputParam(input, langMap.getOrDefault(sourceLang, AUTO), langMap.get(targetLang),TranslationEngineEnum.fromType(defaultEngine));

            log.info("XiangJiTranslateServiceImpl.translate 入参:[{}]", JSON.toJSONString(inputParam));

            return translate(inputParam, new TranslateTextReqVO(input,sourceLang,targetLang));
        } catch (Exception e) {
            log.error("XiangJiTranslateServiceImpl.translate error input={},sourceLang={},targetLang={}",input,sourceLang,targetLang, e);
            Profiler.functionError(callerInfo);
        }finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return result;
    }


    @Override
    public Map<String, Map<String, String>> batchTranslate(Set<TranslateBatchReqVO> translateBatchReqVOSet, boolean defaultInputContent) {
        return super.batchTranslateMap(translateBatchReqVOSet, defaultInputContent);
    }


    @Override
    public String translate(String input, String sourceLang, String targetLang, Integer type) {
        String result = "";
        try {
            InputParam inputParam = buildInputParam(input, sourceLang, targetLang, TranslationEngineEnum.fromType(type));
            result = translate(inputParam, new TranslateTextReqVO(input,sourceLang,targetLang));
        } catch (Exception e) {
            log.error("XiangJiTranslateServiceImpl.translate error input={},sourceLang={},targetLang={}",input,sourceLang,targetLang, e);
        }

        return result;
    }


    private String translate(InputParam inputParam, TranslateTextReqVO textReqVO) {
        String result = "";
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        inputParam.toMap().forEach((key,value)-> {
            params.add(key,String.valueOf(value));
        });

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);

        Result responseEntity = xiangjiRestTemplate.postForObject(TEXT_TRANS_URL, requestEntity, Result.class);
        log.info("XiangJiTranslateServiceImpl.translate 入参:[requestEntity={},textReqVO={}],出参:[{}]", JSON.toJSONString(requestEntity),JSON.toJSONString(textReqVO), responseEntity);
        this.checkTcpPoolStatus();
        if (Objects.isNull(responseEntity) || Objects.isNull(responseEntity.getCode())){
            return result;
        }else {
            if (HttpStatus.OK.value() == responseEntity.getCode()) {
                result = responseEntity.getData().getText();
                this.checkText(textReqVO.getText(),result,textReqVO.getTargetLang());
                return result;
            } else {
                log.error("【系统异常】XiangJiTranslateServiceImpl.translate 出参:{}", JSONObject.toJSONString(responseEntity));
                Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_TRANSLATE_WARNING, String.format("【%s】 %s 翻译异常, 异常信息: %s"
                        , systemProfile
                        , LevelCode.P2.getMessage()
                        , responseEntity.getMessage()
                ));
            }
        }
        return result ;
    }

    @Override
    public String translateHook(String input, String sourceLang, String targetLang) {
        return translate(input, sourceLang, targetLang);
    }

    /**
     * 构建输入参数对象。
     * @param input 待翻译的文本。
     * @param sourceLang 源语言。
     * @param targetLang 目标语言。
     * @param engineEnum 翻译引擎类型。
     * @return 构建好的输入参数对象。
     */
    @PFTracing
    private InputParam buildInputParam(String input, String sourceLang, String targetLang,TranslationEngineEnum engineEnum) {
        InputParam inputParam = new InputParam();
        inputParam.setAction(ACTION);
        inputParam.setTextTransKey(TEXT_TRANS_KEY);
        inputParam.setText(input);
        inputParam.setSourceLanguage(sourceLang);
        inputParam.setTargetLanguage(targetLang);
        inputParam.setType(null != engineEnum ? engineEnum.getType() : TranslationEngineEnum.GOOGLE_LLM.getType() );
        long now =  Instant.now().getEpochSecond();
        inputParam.setCommitTime(String.valueOf(now));
        String sign = MD5.create().digestHex(now + "_" + TEXT_USER_KEY + "_" + TEXT_TRANS_KEY);
        inputParam.setSign(sign);
        return inputParam;
    }

    @PFTracing
    private void checkText(String source,String target,String targetLang){
        if(LangConstant.LANG_ZH.equals(targetLang) || LangConstant.LANG_ZH_HANT.equals(targetLang)){
            return;
        }

        if (StringUtils.isBlank(target)) {
            log.error("【业务数据异常】XiangJiTranslateServiceImpl.translate 翻译结果为空 targetLang:{},source:{},target:{}",targetLang,source,target);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_TRANSLATE_WARNING, String.format("【%s】 %s 没有返回翻译结果, 异常信息: targetLang:%s,source:%s,target:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , targetLang
                    , source
                    , target
            ));
            return;
        }

        boolean simpleZh = target.matches(".*[\\u4E00-\\u9FFF].*");
        if(simpleZh){
            log.error("【业务数据异常】XiangJiTranslateServiceImpl.translate 翻译结果包含中文符号 source:{},target:{},targetLang:{}",source,target,targetLang);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_TRANSLATE_WARNING, String.format("【%s】 %s 翻译异常, 异常信息: targetLang:%s,source:%s,target:%s"
                    , systemProfile
                    , LevelCode.P3.getMessage()
                    , targetLang
                    , source
                    , target
            ));
        }
    }

    private void checkTcpPoolStatus() {
        PoolStats totalStats = xiangjiConnManager.getTotalStats();
        log.info("XiangJiTranslateServiceImpl 当前连接池状态：{}",totalStats.toString());
        if (totalStats.getPending() > 0) {
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_TRANSLATE_WARNING, String.format("【%s】 %s 连接池有等待获取连接请求, 信息: maxTotal:%s,pending:%s,leased:%s,available:%s"
                    , systemProfile
                    , LevelCode.P1.getMessage()
                    , totalStats.getMax()
                    , totalStats.getPending()
                    , totalStats.getLeased()
                    , totalStats.getAvailable()
            ));
        }
    }

    @Data
    public class InputParam {
        private  String Action;//	是	String	GetTextTranslate	服务类型，GetTextTranslate 指“文本翻译”服务
        private  String SourceLanguage;//	是	String	CHS	来源语⾔，参考[语⾔列表]
        private  String TargetLanguage;//	是	String	KOR	目标语言，参考[语⾔列表]
        private  String Text;//	是	String	我是中国人	需翻译的原文
        private  String TextTransKey;//	是	String	参照[访问控制]	文本翻译服务标识码
        private  String CommitTime;//	是	String	1653229753	秒级时间戳
        private  String Sign;//	是	String	044a0bdea4128bb46aa59214ca821d6b	签名， 签名⽅法: md5(CommitTime + "_" + UserKey + "_" + TextTransKey) 小写
        private  Integer Type;//	否	Integer	2	（非必需字段）翻译引擎。Aliyun=0;Google=1;Papago=2;Baidu=3;DeepL=4;Chatgpt=5;GoogleLLM=6;默认是 Aliyun


        public Map<String,Object> toMap(){
            Map<String,Object> map = Maps.newHashMap();
            map.put("Action", Action);
            map.put("SourceLanguage", SourceLanguage);
            map.put("TargetLanguage", TargetLanguage);
            map.put("Text", Text);
            map.put("TextTransKey", TextTransKey);
            map.put("CommitTime", CommitTime);
            map.put("Sign", Sign);
            map.put("Type", Type);
            return map;
        }
    }


    /**
     *  查询剩余翻译次数文档地址：https://www.xiangjifanyi.com/doc/apiDoc/#%E4%BA%94%E7%BF%BB%E8%AF%91%E9%A2%9D%E5%BA%A6%E6%9F%A5%E8%AF%A2-api
     */
    @Override
    public String queryRemainingCount() {
        String result = "-1";
        try{
            String sign = MD5.create().digestHex(TEXT_TRANS_REMAINING_TYPE + "_" + TEXT_USER_KEY + "_" + TEXT_TRANS_KEY);
            XiangJiTranslationRequest request = new XiangJiTranslationRequest(TEXT_TRANS_REMAINING_TYPE,TEXT_TRANS_KEY,sign);
            /**
             * 返回结果示例
             * {
             *   "code": "0", // Web 端，0 为正常值，非0 为异常值
             *   "msg": "ok", // 非0 时，为对应的错误信息
             *   "data": {
             *     "type": 1,
             *     "leftCount": "80828"
             *   }
             * }
             */

            JSONObject resultJsonObject = xiangjiRestTemplate.postForObject(TEXT_TRANS_REMAINING_URL, request, JSONObject.class);
            log.info("XiangJiTranslateServiceImpl.queryRemainingCount 入参:[{}],出参:[{}]",JSON.toJSONString(request) ,JSON.toJSONString(resultJsonObject));
            if (Objects.nonNull(resultJsonObject) &&  Constant.ZERO.equals(resultJsonObject.getLong("code"))) {
                JSONObject data = resultJsonObject.getJSONObject("data");
                Long leftCount = data.getLong("leftCount");
                result = String.valueOf(leftCount);
                // 小于阈值时提醒关注人
                if (leftCount <= TEXT_THRESHOLD_COUNT) {
                    log.info("XiangJiTranslateServiceImpl.queryRemainingCount 入参:[{}],剩余次数:[{}]",JSON.toJSONString(request) ,leftCount);
                    SendMessageReqDTO sendMessageReqDTO = new SendMessageReqDTO();
                    sendMessageReqDTO.setTraceId(UUID.randomUUID().toString());
                    sendMessageReqDTO.setRobotType(RobotTypeEnum.GONG_XIAO_JI_ROBOT.getType());
                    sendMessageReqDTO.setReceiveErps(Lists.newArrayList(remainingCountReceivers));
                    sendMessageReqDTO.setMessage(String.format("象寄文本翻译剩余次数不足，剩余次数：%s，请及时充值", leftCount));
                    dongDongMessageRcpService.sendMessage(sendMessageReqDTO);
                }
            }
        }catch (Exception e) {
            log.error("XiangJiTranslateServiceImpl.queryRemainingCount error", e);
        }
        return result;
    }
}
