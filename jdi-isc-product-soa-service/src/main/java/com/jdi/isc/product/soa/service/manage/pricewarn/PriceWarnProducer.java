package com.jdi.isc.product.soa.service.manage.pricewarn;

import com.jdi.isc.product.soa.api.pricewarn.dto.PriceWarnMqDTO;
import com.jdi.isc.product.soa.api.pricewarn.enums.PriceWarnTypeEnum;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.util.StringUtils;
import com.jdi.isc.product.soa.rpc.mq.ProductCenterMqService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 价格预警发送者.
 *
 * <AUTHOR>
 */
@Component
public class PriceWarnProducer {

    @Resource
    protected OperDuccConfig operDuccConfig;

    @Resource
    private ProductCenterMqService productCenterMqService;

    public void sendMessage(PriceWarnTypeEnum priceWarnType, Long bizId, Integer dataSourceType) {

        PriceWarnMqDTO priceWarnMqDTO = new PriceWarnMqDTO(priceWarnType.getCode(), bizId, dataSourceType);

        priceWarnMqDTO.setBusinessId(String.format("%d_%d_%d", priceWarnType.getCode(), bizId, dataSourceType));

        this.sendMessage(priceWarnMqDTO);
    }

    public void sendMessage(PriceWarnTypeEnum priceWarnType, Long bizId, Integer dataSourceType, Long agreementPriceId) {

        PriceWarnMqDTO priceWarnMqDTO = new PriceWarnMqDTO(priceWarnType.getCode(), bizId, dataSourceType);
        priceWarnMqDTO.setAgreementPriceId(agreementPriceId);

        priceWarnMqDTO.setBusinessId(String.format("%d_%d_%d", priceWarnType.getCode(), bizId, dataSourceType));

        this.sendMessage(priceWarnMqDTO);
    }

    public void sendMessage(PriceWarnMqDTO warn) {
        if (StringUtils.isEmpty(warn.getBusinessId())) {
            warn.setBusinessId(String.format("%d_%d_%d", warn.getWarnType(), warn.getBizId(), warn.getDataSourceType()));
        }
        productCenterMqService.sendProductSoaMessageRetry(warn.getBusinessId(), warn, operDuccConfig.getPriceWarnTopic());
    }
}
