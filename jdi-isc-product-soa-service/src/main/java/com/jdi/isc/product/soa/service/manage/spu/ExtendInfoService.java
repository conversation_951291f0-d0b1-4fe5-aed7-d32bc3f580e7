package com.jdi.isc.product.soa.service.manage.spu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.saleUnit.res.CountrySaleUnitResDTO;
import com.jdi.isc.product.soa.api.spu.res.SaleUnitApiDTO;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.domain.saleUnit.SaleUnitRelationVO;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.api.common.LangConstant.LANG_ZH;


/**
 * <AUTHOR>
 * @date 2024/5/29
 **/
@Slf4j
@Service
public class ExtendInfoService {


    @LafValue("jdi.isc.product.sale.unit")
    private String saleUnit;

    @LafValue("jdi.isc.product.stocking.country")
    private String stockingCountry;

    /**
     * 比亚迪销售单位配置
     */
    @LafValue("jdi.isc.open.byd.sale.unit")
    private String bydSaleUnit;

    @LafValue("jdi.isc.open.country.sale.unit")
    private String countrySaleUnit;

    @Resource
    private LangManageService langManageService;

    @PFTracing
    public String saleUnit(Integer code,String lang){
        if (Objects.isNull(code)){
            return "";
        }
        JSONObject saleUnitJson = ConfigUtils.getJsonByKey(saleUnit, String.valueOf(code));
        if (Objects.isNull(saleUnitJson)) {
            return "";
        }
        return ConfigUtils.getStringByLang(saleUnitJson, StringUtils.isNotBlank(lang) ? lang : LANG_ZH);

    }

    /** 根据销售单位id&语种查询销售单位名称*/
    public Map<String,String> saleUnit(Integer code,Set<String> lang){
        if (Objects.isNull(code)){
            return new HashMap<>();
        }
        JSONObject saleUnitJson = ConfigUtils.getJsonByKey(saleUnit, String.valueOf(code));
        if (Objects.isNull(saleUnitJson)) {
            return new HashMap<>();
        }
        return ConfigUtils.getStringByLangSet(saleUnitJson, CollectionUtils.isNotEmpty(lang) ? lang : Sets.newHashSet(LANG_ZH));
    }

    /**
     * 查询指定语言销售单位
     * @param lang 语种
     * @return 指定语种销售单位列表
     */
    public LinkedHashMap<Integer,String> listSaleUnitByLang(String lang){
        // 语种
        lang = StringUtils.isBlank(lang) ? LANG_ZH : lang;

        // 销售单位配置获取
        JSONObject saleUnitJson = JSON.parseObject(saleUnit);
        if (Objects.isNull(saleUnitJson)) {
            return new LinkedHashMap<>();
        }

        // 查询指定语言销售单位
        LinkedHashMap<Integer,String> resultMap = Maps.newLinkedHashMap();
        Set<String> keySet = saleUnitJson.keySet();
        for (String key : keySet) {
            JSONObject langUnit = saleUnitJson.getJSONObject(key);
            resultMap.put(Integer.valueOf(key), langUnit.getString(lang));
        }

        return resultMap;
    }

    public LinkedHashMap<Integer, LinkedHashMap<String,String>> listLangSaleUnit(){
        // 销售单位配置获取
        JSONObject saleUnitJson = JSON.parseObject(saleUnit);
        if (Objects.isNull(saleUnitJson)) {
            return new LinkedHashMap<>();
        }

        // 查询指定语言销售单位
        LinkedHashMap<Integer, LinkedHashMap<String,String>> resultMap = Maps.newLinkedHashMap();
        Set<String> keySet = saleUnitJson.keySet();
        for (String key : keySet) {
            JSONObject langUnit = saleUnitJson.getJSONObject(key);
            LinkedHashMap<String,String> langUnitMap = Maps.newLinkedHashMap();
            for (String lang : langManageService.getLangCodeList()) {
                langUnitMap.put(lang, langUnit.getString(lang));
            }
            resultMap.put(Integer.valueOf(key), langUnitMap);
        }
        return resultMap;
    }

    /**
     * 根据销售单位代码和语言列表查询比亚迪对应的销售单位名称。
     * @param codes 销售单位代码
     * @param langList 语言列表
     * @return 比亚迪销售单位名称映射，键为语言代码，值为对应的销售单位名称
     */
    public List<SaleUnitApiDTO> queryBydSaleUnitByCodeAndLangList(Set<Integer> codes, Set<String> langList) {
        // 语种
        langList = langList == null ? Sets.newHashSet(LangConstant.LANG_ZH) : langList;

        // 销售单位配置获取
        JSONObject saleUnitJson = JSON.parseObject(bydSaleUnit);
        if (Objects.isNull(saleUnitJson)) {
            return Collections.emptyList();
        }

        // 查询指定语言销售单位
        LinkedHashMap<Integer, LinkedHashMap<String, String>> saleUnitMap = convertJsonToSaleUnitMap(codes, Sets.newHashSet(langList), saleUnitJson);

        return convertSaleUnitMapToSaleUnitApiDTO(langList, saleUnitMap);
    }


    public LinkedHashMap<Integer, LinkedHashMap<String,String>> getSaleUnitByCodesAndLang(Set<Integer> codes,Set<String> langSet){
        // 销售单位配置获取
        JSONObject saleUnitJson = JSON.parseObject(saleUnit);
        if (Objects.isNull(saleUnitJson)) {
            return new LinkedHashMap<>();
        }

        if (CollectionUtils.isEmpty(langSet)) {
            langSet = Sets.newHashSet(LangConstant.LANG_ZH);
        }

        // 查询指定语言销售单位
        return convertJsonToSaleUnitMap(codes, langSet, saleUnitJson);
    }

    private  LinkedHashMap<Integer, LinkedHashMap<String, String>> convertJsonToSaleUnitMap(Set<Integer> codes, Set<String> langSet, JSONObject saleUnitJson) {
        LinkedHashMap<Integer, LinkedHashMap<String,String>> resultMap = Maps.newLinkedHashMap();
        Set<String> keySet = saleUnitJson.keySet();
        for (String key : keySet) {
            if (!codes.contains(Integer.valueOf(key))) {
                continue;
            }
            JSONObject langUnit = saleUnitJson.getJSONObject(key);
            LinkedHashMap<String,String> langUnitMap = Maps.newLinkedHashMap();
            for (String lang : langSet) {
                langUnitMap.put(lang, langUnit.getString(lang));
            }
            resultMap.put(Integer.valueOf(key), langUnitMap);
        }
        return resultMap;
    }


    /**
     * 根据给定的代码集合批量查询销售单位。
     * @param codes 销售单位的代码集合。
     * @param langSet 查询的语言类型。
     * @return 符合条件的销售单位信息列表。
     */
    @PFTracing
    public List<SaleUnitApiDTO> batchQuerySaleUnitByCode(Set<Integer> codes,Set<String> langSet) {
        // 销售单位配置获取
        JSONObject saleUnitJson = JSON.parseObject(saleUnit);
        if (Objects.isNull(saleUnitJson)) {
            return Collections.emptyList();
        }
        LinkedHashMap<Integer, LinkedHashMap<String, String>>  saleUnitMap = this.getSaleUnitByCodesAndLang(codes, langSet);

        return convertSaleUnitMapToSaleUnitApiDTO(langSet, saleUnitMap);
    }

    /**
     * 将销售单位映射转换为 SaleUnitApiDTO 列表。
     * @param langSet 多语言编码集合
     * @param saleUnitMap 销售单位映射，key 为销售单位代码，value 为多语言名称映射
     * @return 转换后的 SaleUnitApiDTO 列表
     */
    private List<SaleUnitApiDTO>  convertSaleUnitMapToSaleUnitApiDTO(Set<String> langSet, LinkedHashMap<Integer, LinkedHashMap<String, String>> saleUnitMap) {
        List<SaleUnitApiDTO> resultList = Lists.newArrayListWithExpectedSize(saleUnitMap.size());
        // 入参长度为1时，获取多语言编码
        String lang = LANG_ZH;
        if (langSet.size() == 1) {
            lang = Lists.newArrayList(langSet).get(0);
        }

        for (Map.Entry<Integer, LinkedHashMap<String, String>> entry : saleUnitMap.entrySet()){
            Integer code = entry.getKey();
            LinkedHashMap<String, String> langMap = entry.getValue();
            resultList.add(new SaleUnitApiDTO(code, langMap.get(lang) , langMap));
        }
        return resultList;
    }

    /**
     * 查询指定国家所有销售单位
     * @param countryCode 请求参数
     * @param lang 请求参数
     * @return 销售单位列表
     */
    public List<CountrySaleUnitResDTO> getAllCountrySaleUnit(String countryCode,String lang) {

        List<SaleUnitRelationVO> relationVOList = this.getRealtionList(countryCode, lang);

        return convertCountrySaleUnitRes(lang, relationVOList);
    }


    private List<SaleUnitRelationVO> getRealtionList(String countryCode, String lang) {
        JSONObject countrySaleUnitJson = JSON.parseObject(countrySaleUnit);
        if (Objects.isNull(countrySaleUnitJson)) {
            log.warn("getAllCountrySaleUnit 国家销售单位配置为空，DUCC KEY={} countryCode={},lang={}","jdi.isc.open.country.sale.unit", countryCode, lang);
            return Collections.emptyList();
        }

        // 获取国家销售单位配置
        JSONArray jsonArray = countrySaleUnitJson.getJSONArray(countryCode);
        if (Objects.isNull(jsonArray)) {
            log.warn("countrySaleUnitJson is null countryCode={},lang={}", countryCode, lang);
            return Collections.emptyList();
        }

        List<SaleUnitRelationVO> relationVOList  = Lists.newArrayListWithExpectedSize(jsonArray.size());
        jsonArray.forEach(item -> {
            SaleUnitRelationVO saleUnitRelationVO = JSON.parseObject(item.toString(), SaleUnitRelationVO.class);
            relationVOList.add(saleUnitRelationVO);
        });
        return relationVOList;
    }

    private List<CountrySaleUnitResDTO> convertCountrySaleUnitRes(String lang, List<SaleUnitRelationVO> relationVOList) {
        if (CollectionUtils.isEmpty(relationVOList)) {
            return Collections.emptyList();
        }

        List<CountrySaleUnitResDTO> resultList = Lists.newArrayList();

        Set<Integer> unitCodes = relationVOList.stream().map(SaleUnitRelationVO::getIscUnitCode).collect(Collectors.toSet());

        // 根据销售单位编码和语种返回销售单位信息
        LinkedHashMap<Integer, LinkedHashMap<String, String>> saleUnitByCodesLangMap = this.getSaleUnitByCodesAndLang(unitCodes, Sets.newHashSet(lang));

        for (SaleUnitRelationVO relationVo : relationVOList) {
            CountrySaleUnitResDTO dto = new CountrySaleUnitResDTO();
            dto.setUnitCode(relationVo.getIscUnitCode());
            dto.setUnitName(saleUnitByCodesLangMap.get(relationVo.getIscUnitCode()).getOrDefault(lang,""));
            dto.setCountryUnitCode(relationVo.getCountryUnitCode());
            Map<String, String> countryUnitNameMap = relationVo.getCountryUnitNameMap();
            dto.setCountryUnitName(countryUnitNameMap.containsKey(lang) ?  countryUnitNameMap.get(lang) : countryUnitNameMap.get(LangConstant.LANG_EN));
            resultList.add(dto);
        }
        return resultList;
    }

    /**
     * 根据国家代码和ISC单元代码集合查询批量销售单元信息。
     * @param countryCode 国家代码
     * @param iscUnitCodes ISC单元代码集合
     * @param lang 语言代码
     * @return 销售单元信息列表
     */
    public List<CountrySaleUnitResDTO> queryBatchSaleUnitByCountryCode(String countryCode,Set<Integer> iscUnitCodes, String lang){

        List<SaleUnitRelationVO> realtionList = this.getRealtionList(countryCode, lang);

        List<SaleUnitRelationVO> existRelationVoList = Optional.ofNullable(realtionList)
                .orElseGet(ArrayList::new)
                .stream().filter(relationVo -> iscUnitCodes.contains(relationVo.getIscUnitCode()))
                .collect(Collectors.toList());

        return convertCountrySaleUnitRes(lang, existRelationVoList);
    }

}
