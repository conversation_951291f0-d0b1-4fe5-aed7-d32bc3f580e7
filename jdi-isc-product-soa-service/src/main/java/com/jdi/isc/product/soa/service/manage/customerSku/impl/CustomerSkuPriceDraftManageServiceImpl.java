package com.jdi.isc.product.soa.service.manage.customerSku.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.biz.component.api.enums.JoySkyBizFlowTypeEnum;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.TradeTypeConstant;
import com.jdi.isc.product.soa.api.common.enums.CustomerSkuBrTaxEnum;
import com.jdi.isc.product.soa.api.common.enums.EnableStatusEnum;
import com.jdi.isc.product.soa.api.customerSku.res.CustomerSkuPriceDraftExportApiDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.domain.apply.po.ApplyInfoPO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryComboBoxVO;
import com.jdi.isc.product.soa.domain.common.biz.BaseErpVO;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.*;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailDraftPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDraftPO;
import com.jdi.isc.product.soa.domain.enums.AuditFormEnum;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.SkuScopeEnum;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.domain.enums.customerSku.CustomerSkuApproveCountryEnums;
import com.jdi.isc.product.soa.domain.enums.customerSku.CustomerSkuEnableApproveCountryEnums;
import com.jdi.isc.product.soa.domain.enums.customerSku.CustomerSkuPriceCountryEnums;
import com.jdi.isc.product.soa.domain.enums.customerSku.CustomerTypeEnum;
import com.jdi.isc.product.soa.domain.enums.spu.SkuTaxRateDraftEnums;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPricePageReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPricePageVO;
import com.jdi.isc.product.soa.domain.price.markupRate.biz.MarkupRatePageVO;
import com.jdi.isc.product.soa.domain.sku.biz.ExternalVO;
import com.jdi.isc.product.soa.domain.sku.biz.ProductIdVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuExternalReqVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangVO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRateVO;
import com.jdi.isc.product.soa.service.atomic.apply.ApplyInfoAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.SupplierAllInfoAtomicService;
import com.jdi.isc.product.soa.service.manage.brand.BrandOutService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.*;
import com.jdi.isc.product.soa.service.manage.customerSku.approve.LocalCustomerSkuPriceApproveService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuExternalManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.TaxRateManageService;
import com.jdi.isc.product.soa.service.manage.vendor.VendorManageService;
import com.jdi.isc.product.soa.service.mapstruct.customerSku.CustomerSkuPriceConvert;
import com.jdi.isc.product.soa.service.support.audit.AuditService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: sku客制化价格-草稿表数据维护服务
 * @Author: zhaokun51
 * @Date: 2024/10/21 13:30
 **/

@Slf4j
@Service
public class CustomerSkuPriceDraftManageServiceImpl extends BaseManageSupportService<CustomerSkuPriceDraftVO, CustomerSkuPriceDraftPO> implements CustomerSkuPriceDraftManageService {

    @Resource
    private CustomerSkuPriceDraftAtomicService customerSkuPriceDraftAtomicService;
    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private CustomerSkuPriceManageService customerSkuPriceManageService;
    @Resource
    private SkuPriceManageService skuPriceManageService;
    @Resource
    private SkuExternalManageService skuExternalManageService;
    @Resource
    private LocalCustomerSkuPriceApproveService localCustomerSkuPriceApproveService;
    @Resource
    private Map<String, CustomerSkuPriceApproveService> customerSkuPriceApproveServiceMap;
    @Resource
    private Map<String, CustomerSkuPriceEnableApproveService> customerSkuPriceEnableApproveServiceMap;
    @Resource
    private Map<String, CustomerSkuSalePriceManageService> customerSkuSalePriceManageServiceMap;
    @Resource
    @Lazy
    private Map<String, TaxRateManageService> taxRateManageServiceMap;
    @Resource
    private CategoryOutService categoryOutService;
    @Resource
    private BrandOutService brandOutService;
    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;
    @Resource
    private VendorManageService vendorManageService;
    @Resource
    private SupplierAllInfoAtomicService supplierAllInfoAtomicService;
    @Resource
    private ApplyInfoAtomicService applyInfoAtomicService;
    @Resource
    private CustomerSkuPriceDetailDraftAtomicService customerSkuPriceDetailDraftAtomicService;

    @Resource
    private AuditService auditService;

    @Value("${spring.profiles.active}")
    protected String systemProfile;
    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public String saveOrUpdate(CustomerSkuPriceDraftVO input) {
        log.info("CustomerSkuPriceDraftManageServiceImpl.saveOrUpdate. input={}", JSONObject.toJSONString(input));
        String processInstanceId = null;
        try{
            String clientCode = input.getClientCode();
            Long skuId = input.getSkuId();

            this.checkInput(input);
            // 取值并校验
            SkuPO sku = customerSkuPriceManageService.getAndValidateSku(skuId);
            SpuPO spu = customerSkuPriceManageService.getAndValidateSpu(sku.getSpuId());
            CustomerVO customer = customerSkuPriceManageService.getAndValidateCustomer(clientCode);

            // 获取币种
            String currency = countryManageService.getCurrencyByCountryCode(customer.getCountry());

            if (StringUtils.isEmpty(currency)) {
                throw new BizException(String.format("%s %s 国家暂未维护币种", customer.getClientName(), customer.getCountry()));
            }

            // 赋值
            // 同一客户vip价仅有一条长期的有效的价格。重复导入是update，需要按正常阈值发起审批
            CustomerSkuPriceDetailDraftPO customerSkuPriceDetailDraftPO = this.getCustomerSkuPriceDetailDraftPO(input, currency);
            this.validateApprovalStatus(customerSkuPriceDetailDraftPO);

            input.setSourceCountryCode(sku.getSourceCountryCode());
            input.setTargetCountryCode(customer.getCountry());

            CustomerSkuPriceApproveService customerSkuApproveManageService = this.getAndValidateCustomerSkuApproveManageService(input.getTargetCountryCode());
            String customerTradeType = CountryConstant.COUNTRY_ZH.equals(sku.getSourceCountryCode()) ? TradeTypeConstant.EXW : TradeTypeConstant.BD;

            customerSkuApproveManageService.setSubmitAuditStatus(input);
            SalePriceCalculateResVO salePriceCalculateResVO = customerSkuApproveManageService.getCalculateResVO(input);
            input.setCalculateResVO(salePriceCalculateResVO);
            this.validateAndSetProfitRate(input, salePriceCalculateResVO);

            // 创建实体并干事
            CustomerSkuPriceDraftPO skuPriceDraftPO = this.getOrCreateSkuPriceDraftPO(customerTradeType, sku, customer, spu);
            skuPriceDraftPO.setCurrency(currency);
            customerSkuPriceDraftAtomicService.saveOrUpdate(skuPriceDraftPO);
            customerSkuPriceDetailDraftPO = this.getCustomerSkuPriceDetailDraftPO(input, customerSkuPriceDetailDraftPO, skuPriceDraftPO, salePriceCalculateResVO, customerTradeType, currency);
            customerSkuPriceDetailDraftAtomicService.saveOrUpdate(customerSkuPriceDetailDraftPO);
            input.setId(customerSkuPriceDetailDraftPO.getId());

            // 设置巴西税率，自动通过逻辑等
            processInstanceId = this.handleAuditStatus(input, customerSkuApproveManageService, customer);
        }catch (BizException e) {
            log.error("CustomerSkuPriceDraftManageServiceImpl.saveOrUpdate has biz error input:{}", JSONObject.toJSONString(input), e);
            throw e;
        }catch (DuplicateKeyException e){
            log.error("CustomerSkuPriceDraftManageServiceImpl.saveOrUpdate has system error input:{}", JSONObject.toJSONString(input),e);
            throw new BizException("sku客制化价格设置重复,请勿重复提交");
        }catch (Exception e){
            log.error("CustomerSkuPriceDraftManageServiceImpl.saveOrUpdate has system error input:{}", JSONObject.toJSONString(input),e);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化价格设置异常,clientCode: %s, skuId:%s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P2.getMessage()
                    , input.getClientCode()
                    , input.getSkuId()
                    , e.getMessage()));
            throw new BizException("系统异常，请联系管理员");
        }

        return processInstanceId;
    }

    @Resource
    private CountryManageService countryManageService;

    /**
     * 同一客户vip价仅有一条长期的有效的价格。重复导入是update，需要按正常阈值发起审批
     */
    private CustomerSkuPriceDetailDraftPO getCustomerSkuPriceDetailDraftPO(CustomerSkuPriceDraftVO input, String currency) {
        // 如果开始时间和结束时间都为空，则表示长期有效
        if (input.getBeginTime() == null && input.getEndTime() == null && input.getId() == null) {
            // 查询长期vip价是否维护过，如果维护过，则直接返回距离当前时间最近都一条数据
            List<CustomerSkuPriceDetailDraftPO> list = customerSkuPriceDetailDraftAtomicService.listLongTermDetailDrafts(input.getClientCode(), input.getSkuId(), currency);

            if (CollectionUtils.isNotEmpty(list)) {
                CustomerSkuPriceDetailDraftPO draft = list.stream().max(Comparator.comparing(CustomerSkuPriceDetailDraftPO::getCreateTime)).orElse(null);
                if (draft != null && AuditStatusEnum.getEnumByCode(draft.getAuditStatus()) == AuditStatusEnum.WAITING_APPROVED) {
                    throw new BizException("最新创建的数据正在审批中，请等待审批通过后再进行操作");
                }
                return draft;
            }
        }
        return this.getCustomerSkuPriceDetailDraftPO(input.getId());
    }

    /**
     * 根据ID获取客户SKU价格明细草稿PO对象。
     * @param id 客户SKU价格明细草稿的ID。
     * @return 对应ID的客户SKU价格明细草稿PO对象。
     */
    private CustomerSkuPriceDetailDraftPO getCustomerSkuPriceDetailDraftPO(Long id) {
        CustomerSkuPriceDetailDraftPO customerSkuPriceDetailDraftPO = customerSkuPriceDetailDraftAtomicService.getValidById(id);
        if (id != null && customerSkuPriceDetailDraftPO == null) {
            log.error("CustomerSkuPriceDraftManageServiceImpl.saveOrUpdate id:{}", id);
            throw new BizException("对应的记录已不存在，请刷新页面");
        }
        return customerSkuPriceDetailDraftPO;
    }

    /**
     * 验证客户SKU价格明细草稿的审批状态是否为等待审批中。
     * @param customerSkuPriceDetailDraftPO 客户SKU价格明细草稿对象。
     */
    private void validateApprovalStatus(CustomerSkuPriceDetailDraftPO customerSkuPriceDetailDraftPO) {
        if(customerSkuPriceDetailDraftPO == null){
            return;
        }
        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndBizId(JoySkyBizFlowTypeEnum.PRODUCT_SALE_PRICE_FLOW, customerSkuPriceDetailDraftPO.getId());
        if (applyInfoPO != null && applyInfoPO.getAuditStatus() != null && applyInfoPO.getAuditStatus().equals(AuditStatusEnum.WAITING_APPROVED.getCode())) {
            throw new BizException("sku客制化价格审批中，请勿重复操作");
        }
    }

    /**
     * 根据目标国家代码获取并验证客户 SKU 价格审批管理服务。
     * @param targetCountryCode 目标国家的国家代码。
     * @return 验证通过的客户 SKU 价格审批管理服务。
     * @throws BizException 如果国家未开通客制化价格策略。
     */
    private CustomerSkuPriceApproveService getAndValidateCustomerSkuApproveManageService(String targetCountryCode) {
        CustomerSkuPriceApproveService customerSkuApproveManageService = this.getCustomerSkuApproveManageService(targetCountryCode);
        if (customerSkuApproveManageService == null) {
            throw new BizException("国家未开通客制化价格生效策略，请联系管理员");
        }
        return customerSkuApproveManageService;
    }

    /**
     * 根据目标国家代码获取并验证客户 SKU 价格审批管理服务。
     * @param targetCountryCode 目标国家的国家代码。
     * @return 验证通过的客户 SKU 价格审批管理服务。
     * @throws BizException 如果国家未开通客制化价格策略。
     */
    private CustomerSkuPriceEnableApproveService getAndValidateCustomerSkuEnableApproveManageService(String targetCountryCode) {
        CustomerSkuPriceEnableApproveService customerSkuPriceEnableApproveService = this.getCustomerSkuPriceEnableApproveService(targetCountryCode);
        if (customerSkuPriceEnableApproveService == null) {
            throw new BizException("国家未开通客制化价格失效策略，请联系管理员");
        }
        return customerSkuPriceEnableApproveService;
    }

    /**
     * 验证利润率是否超过限制。
     * @param input 客户商品价格草稿信息。
     * @param salePriceCalculateResVO 销售价格计算结果。
     */
    private void validateAndSetProfitRate(CustomerSkuPriceDraftVO input, SalePriceCalculateResVO salePriceCalculateResVO) {
        if (salePriceCalculateResVO == null) {
            throw new BizException("销价计算失败");
        }
        if (salePriceCalculateResVO.getProfitRate() == null) {
            throw new BizException("利润率为空");
        }
        if (salePriceCalculateResVO.getProfitRate().compareTo(salePriceCalculateResVO.getProfitLimitRate()) >= 0) {
            input.setAuditStatus(AuditStatusEnum.APPROVED.getCode());
            input.setLevel(0);
        }
    }

    /**
     * 根据给定的客户代码、客户交易类型、商品ID、货币和其他相关信息，获取或创建一个客户商品价格草稿对象。
     * @param customerTradeType 客户交易类型
     * @param sku 商品对象
     * @param customer 客户对象
     * @param spu 供应商商品对象
     * @return 客户商品价格草稿对象
     */
    private CustomerSkuPriceDraftPO getOrCreateSkuPriceDraftPO(String customerTradeType, SkuPO sku, CustomerVO customer, SpuPO spu) {
        CustomerSkuPriceDraftPO skuPriceDraftPO = customerSkuPriceDraftAtomicService.getOne(customer.getClientCode(), customerTradeType
                , sku.getSkuId(), customer.getSaleCurrency());
        if (skuPriceDraftPO == null) {
            skuPriceDraftPO = new CustomerSkuPriceDraftPO();
            skuPriceDraftPO.setClientCode(customer.getClientCode());
            skuPriceDraftPO.setSkuId(sku.getSkuId());
            skuPriceDraftPO.setCurrency(customer.getSaleCurrency());
            skuPriceDraftPO.setCustomerTradeType(customerTradeType);
            skuPriceDraftPO.setSourceCountryCode(sku.getSourceCountryCode());
            skuPriceDraftPO.setCustomerSalePrice(new BigDecimal(9999));
            skuPriceDraftPO.setClientName(customer.getClientName());
            skuPriceDraftPO.setTargetCountryCode(customer.getCountry());
            skuPriceDraftPO.setVendorCode(sku.getVendorCode());
            skuPriceDraftPO.setCatId(sku.getCatId());
            skuPriceDraftPO.setJdCatId(sku.getJdCatId());
            skuPriceDraftPO.setBrandId(sku.getBrandId());
            skuPriceDraftPO.setBuyer(spu.getBuyer());
        } else {
            skuPriceDraftPO.setSkuId(null);
        }
        return skuPriceDraftPO;
    }

    /**
     * 根据输入参数和其他对象信息，创建并返回一个 CustomerSkuPriceDetailDraftPO 对象。
     * @param input CustomerSkuPriceDraftVO 对象，包含客户 SKU 价格的基本信息。
     * @param customerSkuPriceDetailDraftPO CustomerSkuPriceDetailDraftPO 对象，用于存储生成的客户 SKU 价格详细信息。
     * @param skuPriceDraftPO CustomerSkuPriceDraftPO 对象，包含 SKU 价格的基本信息。
     * @param salePriceCalculateResVO SalePriceCalculateResVO 对象，包含销售价格计算的结果信息。
     * @param customerTradeType String 类型，表示客户交易类型。
     * @param currency String 类型，表示货币类型。
     * @return CustomerSkuPriceDetailDraftPO 对象，包含生成的客户 SKU 价格详细信息。
     */
    private CustomerSkuPriceDetailDraftPO getCustomerSkuPriceDetailDraftPO(CustomerSkuPriceDraftVO input, CustomerSkuPriceDetailDraftPO customerSkuPriceDetailDraftPO
            , CustomerSkuPriceDraftPO skuPriceDraftPO, SalePriceCalculateResVO salePriceCalculateResVO
            , String customerTradeType, String currency) {
        if (customerSkuPriceDetailDraftPO == null) {
            customerSkuPriceDetailDraftPO = new CustomerSkuPriceDetailDraftPO();
            customerSkuPriceDetailDraftPO.setCreator(LoginContextHolder.getLoginContextHolder().getPin());
            customerSkuPriceDetailDraftPO.setCreateTime(new Date().getTime());
            customerSkuPriceDetailDraftPO.setSkuId(input.getSkuId());
        } else {
            customerSkuPriceDetailDraftPO.setSkuId(null);
        }
        customerSkuPriceDetailDraftPO.setBizId(skuPriceDraftPO.getId());
        customerSkuPriceDetailDraftPO.setClientCode(skuPriceDraftPO.getClientCode());
        customerSkuPriceDetailDraftPO.setCustomerSalePrice(input.getCustomerSalePrice());

        customerSkuPriceDetailDraftPO.setCustomerTradeType(customerTradeType);
        customerSkuPriceDetailDraftPO.setCurrency(currency);
        customerSkuPriceDetailDraftPO.setBeginTime(input.getBeginTime());
        customerSkuPriceDetailDraftPO.setEndTime(input.getEndTime());
        customerSkuPriceDetailDraftPO.setEnableStatus(YesOrNoEnum.YES.getCode());
        customerSkuPriceDetailDraftPO.setAuditStatus(input.getAuditStatus());
        customerSkuPriceDetailDraftPO.setLevel(input.getLevel());
        if(input.getBeginTime() == null){
            customerSkuPriceDetailDraftPO.setBeginTime(Constant.ZERO);
        }
        if(input.getEndTime() == null){
            customerSkuPriceDetailDraftPO.setEndTime(Constant.MAX);
        }
        if (salePriceCalculateResVO != null) {
            customerSkuPriceDetailDraftPO.setProfitRate(salePriceCalculateResVO.getProfitRate());
            customerSkuPriceDetailDraftPO.setWarningMsg(salePriceCalculateResVO.getWarningMsg());
        }
        customerSkuPriceDetailDraftPO.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
        customerSkuPriceDetailDraftPO.setUpdateTime(new Date().getTime());

        return customerSkuPriceDetailDraftPO;
    }

    /**
     * 处理客户SKU价格审批状态和创建巴西税率。
     * @param input 审批状态和相关信息的输入对象。
     * @param customerSkuApproveManageService 客户SKU价格审批管理服务。
     * @param customer 客户信息对象。
     */
    private String handleAuditStatus(CustomerSkuPriceDraftVO input, CustomerSkuPriceApproveService customerSkuApproveManageService, CustomerVO customer) {
        String processInstanceId = null;

        // 设置草稿态巴西税率
        if (customer.getCountry().equals(CountryConstant.COUNTRY_BR)) {
            customerSkuPriceManageService.createBRTaxRate(input, customer, SkuTaxRateDraftEnums.DRAFT);
        }

        // 提交审核
        if (input.getAuditStatus().equals(AuditStatusEnum.WAITING_APPROVED.getCode())) {
            processInstanceId = customerSkuApproveManageService.batchSubmit(input);
        }

        // 自动审批通过
        if (input.getAuditStatus().equals(AuditStatusEnum.APPROVED.getCode())) {
            input.setUpdater(null);
            input.setCreateTime(null);
            input.setCreator(null);
            input.setUpdateTime(null);
            customerSkuPriceManageService.saveOrUpdate(CustomerSkuPriceConvert.INSTANCE.draftVo2vo(input));
            //如果是自动审批且 新增 vip价格场景, 需插入一条 applyInfoAtomicService 记录，且状态为审核通过
            ApplyInfoPO dbApplyInfoPO = applyInfoAtomicService.getByBizTypeAndBizId(JoySkyBizFlowTypeEnum.PRODUCT_SALE_PRICE_FLOW,input.getId());
            if(dbApplyInfoPO == null){
                processInstanceId = customerSkuApproveManageService.submitDefaultApplyInfo(input.getId());
            }
        }
        return processInstanceId;
    }

    @Resource
    private SkuReadManageService skuReadManageService;

    @Override
    public PageInfo<CustomerSkuPriceDraftVO> page(CustomerSkuPriceDraftReqVO input) {
        input.setNowTime(DateUtil.getCurrentTime());
        buildQuery(input);
        if (isApprovedAuditStatus(input)) {
            return handleApprovedStatus(input);
        }

        Page<CustomerSkuPriceDraftVO> dbRecord = new Page<>(input.getIndex(), input.getSize());
        long total = customerSkuPriceDraftAtomicService.getTotal(input);
        dbRecord.setTotal(total);

        if (total == 0) {
            return super.pageTransformVO(dbRecord);
        }

        List<CustomerSkuPriceDraftVO> dbRecordList = customerSkuPriceDraftAtomicService.listSearch(input);
        dbRecord.setRecords(dbRecordList);

        if (CollectionUtils.isEmpty(dbRecord.getRecords())) {
            return null;
        }

        PageInfo<CustomerSkuPriceDraftVO> result = pageTransformVO(dbRecord);
        enrichSkuDetails(result);

        return result;
    }

    private void buildQuery(CustomerSkuPriceDraftReqVO vo){
        if(CollectionUtils.isNotEmpty(vo.getProductIds())){
            ProductIdVO productIdVO = skuReadManageService.getProductInfoByIds(vo.getProductIds());
            if(CollectionUtils.isNotEmpty(productIdVO.getSkuIds())){
                vo.setSkuIds(productIdVO.getSkuIds());
            }
        }
    }

    /**
     * 判断客户SKU价格草稿的审核状态是否为已批准
     * @param input 客户SKU价格草稿请求对象
     * @return true 如果审核状态为已批准，否则 false
     */
    private boolean isApprovedAuditStatus(CustomerSkuPriceDraftReqVO input) {
        return input.getAuditStatus() != null && AuditStatusEnum.APPROVED.getCode() == input.getAuditStatus();
    }

    /**
     * 处理已批准状态的客户 SKU 价格草稿分页查询结果。
     * @param input 客户 SKU 价格草稿分页查询请求对象。
     * @return 已批准状态的客户 SKU 价格草稿分页查询结果。
     */
    private PageInfo<CustomerSkuPriceDraftVO> handleApprovedStatus(CustomerSkuPriceDraftReqVO input) {
        log.info("CustomerSkuPriceManageServiceImpl.page in effect");
        PageInfo<CustomerSkuPriceDraftVO> result = CustomerSkuPriceConvert.INSTANCE.vo2DraftVo(customerSkuPriceManageService.page(input));
        if (result == null) {
            return null;
        }
        for (CustomerSkuPriceDraftVO resultRecord : result.getRecords()) {
            resultRecord.setAuditStatus(AuditStatusEnum.APPROVED.getCode());
        }
        return result;
    }

    /**
     * 通过查询外部信息和基础价格信息来丰富客户 SKU 价格草稿的详细信息。
     * @param result 包含待丰富信息的客户 SKU 价格草稿的分页结果。
     */
    private void enrichSkuDetails(PageInfo<CustomerSkuPriceDraftVO> result) {
        List<Long> skuIds = result.getRecords().stream()
                .map(CustomerSkuPriceDraftVO::getSkuId)
                .collect(Collectors.toList());

        Map<Long, ExternalVO> skuMap = querySkuInfo(skuIds);

        for (CustomerSkuPriceDraftVO target : result.getRecords()) {
            enrichSkuInfo(target, skuMap);
        }
    }

    /**
     * 根据 SKU ID 列表查询 SKU 信息。
     * @param skuIds SKU ID 列表
     * @return SKU 信息映射，key 为 SKU ID，value 为 ExternalVO 对象
     */
    private Map<Long, ExternalVO> querySkuInfo(List<Long> skuIds) {
        SkuExternalReqVO skuQuery = new SkuExternalReqVO();
        skuQuery.setSkuIds(skuIds);
        skuQuery.setScopeEnums(Sets.newHashSet(SkuScopeEnum.BASE, SkuScopeEnum.LANG));
        return skuExternalManageService.querySkuInfo(skuQuery);
    }

    /**
     * 从外部数据源中获取商品信息并填充到目标对象中。
     * @param target 目标对象，用于存储填充后的商品信息。
     * @param skuMap SKU的外部数据源。
     */
    private void enrichSkuInfo(CustomerSkuPriceDraftVO target, Map<Long, ExternalVO> skuMap) {
        ExternalVO skuLang = skuMap.get(target.getSkuId());
        if (skuLang != null) {
            SpuLangVO zhName = skuLang.getLangVOList().stream()
                    .filter(line -> LangConstant.LANG_ZH.equals(line.getLang()))
                    .findFirst().orElse(null);
            target.setSkuName(zhName != null ? zhName.getSpuTitle() : null);
        }

        // 计算毛利
        calculateGrossRate(target);
        // 添加巴西税率
        addTaxRate(target);
        // 设置生效状态
        setEnableStatus(target);
        // 处理开始与结束时间
        setBeginAndEndTime(target);
    }

    /**
     * 计算毛利率并设置到目标对象中。
     * @param target CustomerSkuPriceDraftVO 对象，用于存储计算结果。
     */
    private void calculateGrossRate(CustomerSkuPriceDraftVO target) {
        if (Objects.nonNull(target.getCustomerPurchasePrice()) && Objects.nonNull(target.getCustomerSalePrice())) {
            BigDecimal divide = target.getCustomerSalePrice()
                    .subtract(target.getCustomerPurchasePrice())
                    .divide(target.getCustomerSalePrice(), 4, RoundingMode.HALF_UP);
            BigDecimal grossRate = divide.multiply(new BigDecimal("100"));
            target.setGrossRate(grossRate);
        }
    }

    /**
     * 为目标对象添加税率信息。
     * @param target 目标对象，包含客户代码、目标国家代码和 SKU ID。
     */
    private void addTaxRate(CustomerSkuPriceDraftVO target) {
        TaxRateVO taxRateVO = new TaxRateVO();
        taxRateVO.setClientCode(target.getClientCode());
        taxRateVO.setCountryCode(target.getTargetCountryCode());
        taxRateVO.setKeyId(String.valueOf(target.getSkuId()));
        taxRateVO.setClientType(CustomerTypeEnum.Client.getCode());

        TaxRateManageService taxRateManageService = taxRateManageService(SkuTaxRateDraftEnums.DRAFT.getType());
        List<TaxRateVO> taxRateVOList = taxRateManageService.listTaxRate(taxRateVO);
        target.setTaxRateList(taxRateVOList);
    }

    /**
     * 设置生效状态。
     * @param target 目标对象，包含客户代码、目标国家代码和 SKU ID。
     */
    private void setEnableStatus(CustomerSkuPriceDraftVO target) {
        Long current = DateUtil.getCurrentTime();
        Long beginTime = target.getBeginTime();
        Long endTime = target.getEndTime();
        if (beginTime == null || endTime == null) {
            return;
        }
        if(YnEnum.NO.getCode().equals(target.getEnableStatus())){
            return;
        }

        // 根据当前时间设置生效状态
        if (current < beginTime) {
            target.setEnableStatus(EnableStatusEnum.NOT_EFFECTIVE.getCode()); // 未生效
        } else if (current <= endTime) {
            target.setEnableStatus(EnableStatusEnum.EFFECTIVE.getCode()); // 生效中
        } else {
            target.setEnableStatus(EnableStatusEnum.EXPIRED.getCode()); // 已过期
        }
    }

    /**
     * 根据条件设置开始和结束时间
     * @param input CustomerSkuPriceDraftVO 对象，包含开始和结束时间信息
     */
    private void setBeginAndEndTime(CustomerSkuPriceDraftVO input){
        if(input.getBeginTime() != null && Constant.ZERO.equals(input.getBeginTime())){
            input.setBeginTime(null);
        }
        if(input.getEndTime() != null && Constant.MAX.equals(input.getEndTime())){
            input.setEndTime(null);
        }
    }

    /**
     * 根据当前时间和指定的生效时间范围，设置目标对象的生效状态。
     * @param inputs 客户SKU价格审核结果列表，用于获取当前时间和生效时间范围。
     */
    private void setEnableStatus(List<CustomerSkuPriceAuditResVO> inputs) {
        inputs.forEach(item->{
            Long current = DateUtil.getCurrentTime();
            Long beginTime = item.getBeginTime();
            Long endTime = item.getEndTime();
            if (beginTime == null || endTime == null) {
                return;
            }
            if(YnEnum.NO.getCode().equals(item.getEnableStatus())){
                return;
            }

            // 根据当前时间设置生效状态
            if (current < beginTime) {
                item.setEnableStatus(EnableStatusEnum.NOT_EFFECTIVE.getCode()); // 未生效
            } else if (current <= endTime) {
                item.setEnableStatus(EnableStatusEnum.EFFECTIVE.getCode()); // 生效中
            } else {
                item.setEnableStatus(EnableStatusEnum.EXPIRED.getCode()); // 已过期
            }
        });
    }

    /**
     * 设置客户SKU价格审计结果的生效状态。
     * @param inputs 客户SKU价格审计结果列表。
     */
    private void setBeginAndEndTime(List<CustomerSkuPriceAuditResVO> inputs) {
        inputs.forEach(item->{
            if(item.getBeginTime() != null && Constant.ZERO.equals(item.getBeginTime())){
                item.setBeginTime(null);
            }
            if(item.getEndTime() != null && Constant.MAX.equals(item.getEndTime())){
                item.setEndTime(null);
            }
        });
    }

    @Override
    public List<CustomerSkuPriceAuditNumVO> auditStatusNum(CustomerSkuPriceDraftReqVO reqVO) {
        List<CustomerSkuPriceAuditNumVO> auditNumVOResp = new ArrayList<>();
        reqVO.setTypeKey("all");
        auditNumVOResp.add(this.getDraftAuditNumVO(reqVO));
        reqVO.setTypeKey("effect");
        auditNumVOResp.add(this.getAuditNumVO(reqVO));

        reqVO.setTypeKey("reject");
        reqVO.setAuditStatus(AuditStatusEnum.REJECTED.getCode());
        auditNumVOResp.add(this.getDraftAuditNumVO(reqVO));
        return auditNumVOResp;
    }

    @Override
    public List<BaseErpVO> waitingAudit() {
        List<CustomerSkuPriceDraftPO> draftPOS = customerSkuPriceDraftAtomicService.queryList(AuditStatusEnum.WAITING_APPROVED);
        if(CollectionUtils.isEmpty(draftPOS)){
            log.info("CustomerSkuPriceManageServiceImpl.waitingAudit draftPOS is null");
            return null;
        }
        Map<Integer,List<CustomerSkuPriceDraftPO>> draftMap = draftPOS.stream().collect(Collectors.groupingBy(CustomerSkuPriceDraftPO::getLevel));
        List<CustomerSkuPriceDraftPO> cnDrafts = draftMap.get(YesOrNoEnum.NO.getCode());
        List<BaseErpVO> results = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(cnDrafts)){
            List<BaseErpVO> approveEmails = localCustomerSkuPriceApproveService.getCountryConfig(CountryConstant.COUNTRY_ZH);
            if(CollectionUtils.isNotEmpty(approveEmails)){
                results.addAll(approveEmails);
            }
        }

        List<CustomerSkuPriceDraftPO> notCnDrafts = draftMap.get(YesOrNoEnum.YES.getCode());
        if(CollectionUtils.isEmpty(notCnDrafts)){
            return results;
        }
        Map<String,List<CustomerSkuPriceDraftPO>> countryDraftMap = notCnDrafts.stream().collect(Collectors.groupingBy(CustomerSkuPriceDraftPO::getTargetCountryCode));
        Set<String> countryCodes = countryDraftMap.keySet();
        for(String countryCode : countryCodes){
            List<BaseErpVO> approveEmails = localCustomerSkuPriceApproveService.getCountryConfig(countryCode);
            if(CollectionUtils.isNotEmpty(approveEmails)){
                results.addAll(approveEmails);
            }
        }
        Map<String, BaseErpVO> baseErpVOMap = results.stream().collect(Collectors.toMap(BaseErpVO::getErp, Function.identity(),(v1, v2)->v2));
        return new ArrayList<>(baseErpVOMap.values());
    }

    @Override
    public String batchApprove(CustomerSkuAuditVO input) {
        List<Long> ids = input.getIds();
        List<CustomerSkuPriceDetailDraftPO> detailDraftPOS = customerSkuPriceDetailDraftAtomicService.getValidByIds(ids);
        if(CollectionUtils.isEmpty(detailDraftPOS)){
            return Constant.SUCCESS;
        }
        Set<Long> draftIds = detailDraftPOS.stream().map(CustomerSkuPriceDetailDraftPO::getBizId).collect(Collectors.toSet());
        List<CustomerSkuPriceDraftPO> draftPOS = customerSkuPriceDraftAtomicService.getValidByIds(draftIds);
        if(CollectionUtils.isEmpty(draftPOS)){
            return Constant.SUCCESS;
        }

        List<ApplyInfoPO> applyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.PRODUCT_SALE_PRICE_FLOW,input.getIds());
        if(CollectionUtils.isEmpty(applyInfoPOS)){
            throw new BizException("无匹配数据,数据已变更，请刷新页面");
        }
        Map<String,ApplyInfoPO> applyInfoPOMap = applyInfoPOS.stream().collect(Collectors.toMap(ApplyInfoPO::getBizId, Function.identity()));


        Map<Long,String> targetCountrtMap = draftPOS.stream().collect(Collectors.toMap(CustomerSkuPriceDraftPO::getId,CustomerSkuPriceDraftPO::getTargetCountryCode));

        String message = "";
        for(CustomerSkuPriceDetailDraftPO detailDraftPO : detailDraftPOS){
            ApplyInfoPO applyInfoPO = applyInfoPOMap.get(detailDraftPO.getId().toString());
            try{
                String targetCountryCode = targetCountrtMap.get(detailDraftPO.getBizId());
                if(StringUtils.isBlank(targetCountryCode)){
                    log.error("CustomerSkuPriceDraftManageServiceImpl.batchApprove targetCountryCode is null, detailDraftPO:{}",detailDraftPO);
                    continue;
                }

                input.setIds(Lists.newArrayList(detailDraftPO.getId()));
                Objects.requireNonNull(this.getCustomerSkuApproveManageService(targetCountryCode)).singleApprove(detailDraftPO,input,applyInfoPO);
            }catch (Exception e){
                log.error("CustomerSkuPriceDraftManageServiceImpl.getCustomerSkuApproveManageService.singleApprove error, detailDraftPO:{}",detailDraftPO,e);
                message = String.format("%s：%s%s", applyInfoPO.getApplyCode(), e.getMessage(),"\n");
            }
        }
        return StringUtils.isNotBlank(message) ? message : Constant.SUCCESS;
    }

    @Override
    public String batchReject(CustomerSkuAuditVO input) {
        List<Long> ids = input.getIds();
        List<CustomerSkuPriceDetailDraftPO> detailDraftPOS = customerSkuPriceDetailDraftAtomicService.getValidByIds(ids);
        if(CollectionUtils.isEmpty(detailDraftPOS)){
            return Constant.SUCCESS;
        }
        Set<Long> draftIds = detailDraftPOS.stream().map(CustomerSkuPriceDetailDraftPO::getBizId).collect(Collectors.toSet());
        List<CustomerSkuPriceDraftPO> draftPOS = customerSkuPriceDraftAtomicService.getValidByIds(draftIds);
        if(CollectionUtils.isEmpty(draftPOS)){
            return Constant.SUCCESS;
        }

        List<ApplyInfoPO> applyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.PRODUCT_SALE_PRICE_FLOW,input.getIds());
        if(CollectionUtils.isEmpty(applyInfoPOS)){
            throw new BizException("无匹配数据,数据已变更，请刷新页面");
        }
        Map<String,ApplyInfoPO> applyInfoPOMap = applyInfoPOS.stream().collect(Collectors.toMap(ApplyInfoPO::getBizId, Function.identity()));

        Map<Long,String> targetCountrtMap = draftPOS.stream().collect(Collectors.toMap(CustomerSkuPriceDraftPO::getId,CustomerSkuPriceDraftPO::getTargetCountryCode));

        String message = "";
        for(CustomerSkuPriceDetailDraftPO detailDraftPO : detailDraftPOS){
            ApplyInfoPO applyInfoPO = applyInfoPOMap.get(detailDraftPO.getId().toString());
            try{
                String targetCountryCode = targetCountrtMap.get(detailDraftPO.getBizId());
                if(StringUtils.isBlank(targetCountryCode)){
                    log.error("CustomerSkuPriceDraftManageServiceImpl.batchReject targetCountryCode is null, detailDraftPO:{}",detailDraftPO);
                    continue;
                }

                input.setIds(Lists.newArrayList(detailDraftPO.getId()));
                Objects.requireNonNull(this.getCustomerSkuApproveManageService(targetCountryCode)).singleReject(detailDraftPO,input,applyInfoPO);
            }catch (Exception e){
                log.error("CustomerSkuPriceDraftManageServiceImpl.getCustomerSkuApproveManageService.singleReject error, detailDraftPO:{}",detailDraftPO,e);
                message = String.format("%s：%s%s", applyInfoPO.getApplyCode(), e.getMessage(),"\n");
            }
        }
        return StringUtils.isNotBlank(message) ? message : Constant.SUCCESS;
    }

    @Override
    public String batchRevoke(CustomerSkuAuditVO input) {
        List<Long> ids = input.getIds();
        List<CustomerSkuPriceDetailDraftPO> detailDraftPOS = customerSkuPriceDetailDraftAtomicService.getValidByIds(ids);
        if(CollectionUtils.isEmpty(detailDraftPOS)){
            return Constant.SUCCESS;
        }

        Set<Long> draftIds = detailDraftPOS.stream().map(CustomerSkuPriceDetailDraftPO::getBizId).collect(Collectors.toSet());
        List<CustomerSkuPriceDraftPO> draftPOS = customerSkuPriceDraftAtomicService.getValidByIds(draftIds);
        if(CollectionUtils.isEmpty(draftPOS)){
            return Constant.SUCCESS;
        }

        List<ApplyInfoPO> allApplyInfoPOS = Lists.newArrayList();
        List<ApplyInfoPO> effectiveApplyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.PRODUCT_SALE_PRICE_FLOW,input.getIds());
        if(CollectionUtils.isNotEmpty(effectiveApplyInfoPOS)){
            allApplyInfoPOS.addAll(effectiveApplyInfoPOS);
        }
        List<ApplyInfoPO> failEffectiveApplyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.SALE_RATE_FAILED_EFFECTIVE_FLOW,input.getIds());
        if(CollectionUtils.isNotEmpty(failEffectiveApplyInfoPOS)){
            allApplyInfoPOS.addAll(failEffectiveApplyInfoPOS);
        }

        if(CollectionUtils.isEmpty(allApplyInfoPOS)){
            throw new BizException("无匹配数据,数据已变更，请刷新页面");
        }

        allApplyInfoPOS = allApplyInfoPOS.stream().filter(item->item.getAuditStatus() != null && item.getAuditStatus().equals(AuditStatusEnum.WAITING_APPROVED.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(allApplyInfoPOS)){
            throw new BizException("无匹配数据,数据已变更，请刷新页面");
        }

        Map<Integer,List<ApplyInfoPO>> applyInfoPOSMap = allApplyInfoPOS.stream().collect(Collectors.groupingBy(ApplyInfoPO::getProcessType));
        Map<Long,String> targetCountrtMap = draftPOS.stream().collect(Collectors.toMap(CustomerSkuPriceDraftPO::getId,CustomerSkuPriceDraftPO::getTargetCountryCode));

        applyInfoPOSMap.forEach((processType,applyInfoPOS)->{
            Map<String,ApplyInfoPO> applyInfoPOMap = applyInfoPOS.stream().collect(Collectors.toMap(ApplyInfoPO::getBizId,Function.identity()));
            if(JoySkyBizFlowTypeEnum.PRODUCT_SALE_PRICE_FLOW.getFlowTypeCode().equals(processType)){
                for(CustomerSkuPriceDetailDraftPO detailDraftPO : detailDraftPOS){
                    String targetCountryCode = targetCountrtMap.get(detailDraftPO.getBizId());
                    if(StringUtils.isBlank(targetCountryCode)){
                        log.error("CustomerSkuPriceDraftManageServiceImpl.batchRevoke targetCountryCode is null, detailDraftPO:{}",detailDraftPO);
                        continue;
                    }

                    input.setIds(Lists.newArrayList(detailDraftPO.getId()));
                    Objects.requireNonNull(this.getCustomerSkuApproveManageService(targetCountryCode)).singleRevoke(detailDraftPO,input,applyInfoPOMap.get(detailDraftPO.getId().toString()));
                }
            } else if (JoySkyBizFlowTypeEnum.SALE_RATE_FAILED_EFFECTIVE_FLOW.getFlowTypeCode().equals(processType)){
                for(CustomerSkuPriceDetailDraftPO detailDraftPO : detailDraftPOS){
                    String targetCountryCode = targetCountrtMap.get(detailDraftPO.getBizId());
                    if(StringUtils.isBlank(targetCountryCode)){
                        log.error("CustomerSkuPriceDraftManageServiceImpl.batchEnableRevoke targetCountryCode is null, detailDraftPO:{}",detailDraftPO);
                        continue;
                    }

                    input.setIds(Lists.newArrayList(detailDraftPO.getId()));
                    Objects.requireNonNull(this.getCustomerSkuPriceEnableApproveService(targetCountryCode)).singleRevoke(detailDraftPO,input,applyInfoPOMap.get(detailDraftPO.getId().toString()));
                }
            }
        });




        return Constant.SUCCESS;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public String updateEnableStatusById(Long detailDraftId) {
        CustomerSkuPriceDetailDraftPO detailDraftPO = customerSkuPriceDetailDraftAtomicService.getValidById(detailDraftId);
        if(Objects.isNull(detailDraftPO)){
            throw new BizException("无匹配数据");
        }
        if(detailDraftPO.getAuditStatus() != null && AuditStatusEnum.WAITING_APPROVED.getCode() == detailDraftPO.getAuditStatus()){
            log.error("CustomerSkuPriceDraftManageServiceImpl.updateEnableStatusById,审批中detailDraftPO，无法修改状态,detailDraftId:{}",detailDraftId);
            throw new BizException("待审批数据无法失效，clientCode:"+detailDraftPO.getClientCode()+",skuId:"+detailDraftPO.getSkuId());
        }

        CustomerSkuPriceDraftPO customerSkuPriceDraftPO = customerSkuPriceDraftAtomicService.getValidById(detailDraftPO.getBizId());
        if(customerSkuPriceDraftPO == null){
            log.error("CustomerSkuPriceDraftManageServiceImpl.updateEnableStatusById,审批中applyInfoPO，customerSkuPriceDraftPO is null,detailDraftId:{}",detailDraftId);
            throw new BizException("无匹配数据,数据已变更，clientCode:"+detailDraftPO.getClientCode()+",skuId:"+detailDraftPO.getSkuId());
        }

        detailDraftPO.setSkuId(null);
        detailDraftPO.setAuditStatus(AuditStatusEnum.WAITING_APPROVED.getCode());
        detailDraftPO.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
        detailDraftPO.setUpdateTime(new Date().getTime());
        customerSkuPriceDetailDraftAtomicService.updateById(detailDraftPO);

        CustomerSkuPriceEnableApproveService customerSkuPriceEnableApproveService = this.getAndValidateCustomerSkuEnableApproveManageService(customerSkuPriceDraftPO.getTargetCountryCode());

        CustomerSkuPriceDraftVO inputDraft = this.convertEnabledApproveInfo(customerSkuPriceDraftPO,detailDraftPO);

        SalePriceCalculateResVO salePriceCalculateResVO = customerSkuPriceEnableApproveService.getCalculateResVO(inputDraft);
        inputDraft.setCalculateResVO(salePriceCalculateResVO);
        customerSkuPriceEnableApproveService.submit(inputDraft);

        return Constant.SUCCESS;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean batchJoySkyApprove(String erp, Integer status, String processInstanceId, String msg) {
        if(status == null || StringUtils.isBlank(processInstanceId)){
            throw new BizException("审批人或流程id为空");
        }

        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(JoySkyBizFlowTypeEnum.PRODUCT_SALE_PRICE_FLOW,processInstanceId);
        if(applyInfoPO != null && applyInfoPO.getAuditStatus() != null && AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.info(String.format("SkuPriceDraftManageServiceImpl.batchJoySkyApprove applyInfoPO 审批记录为空或审批状态不匹配,processInstanceId:%s",processInstanceId));
            return Boolean.FALSE;
        }

        CustomerSkuPriceDetailDraftPO detailDraftPO = customerSkuPriceDetailDraftAtomicService.getValidById(Long.valueOf(applyInfoPO.getBizId()));
        if(detailDraftPO == null ||detailDraftPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != detailDraftPO.getAuditStatus()){
            throw new BizException(String.format("CustomerSkuPriceDraftManageServiceImpl.batchJoySkyApprove detailDraftPO 记录为空或审批状态不匹配,applyInfoPO:%s",applyInfoPO.getBizId()));
        }

        CustomerSkuPriceDraftPO draftPO = customerSkuPriceDraftAtomicService.getValidById(detailDraftPO.getBizId());
        if(draftPO == null){
            throw new BizException(String.format("CustomerSkuPriceDraftManageServiceImpl.batchJoySkyApprove draftPO 记录为空,draftId:%s",detailDraftPO.getBizId()));
        }

        CustomerSkuPriceApproveService customerSkuApproveManageService = this.getCustomerSkuApproveManageService(draftPO.getTargetCountryCode());
        if(customerSkuApproveManageService == null){
            throw new BizException(String.format("CustomerSkuPriceDraftManageServiceImpl.batchJoySkyApprove customerSkuApproveManageService 为空,countryCode:%s",draftPO.getTargetCountryCode()));
        }

        CustomerSkuAuditVO param = new CustomerSkuAuditVO();
        param.setProcessInstanceId(processInstanceId);
        param.setAuditErp(erp);
        param.setRejectReason(msg);
        if(status.equals(AuditStatusEnum.APPROVED.getCode())){
            return customerSkuApproveManageService.messageApprove(param);
        } else if (status.equals(AuditStatusEnum.REJECTED.getCode())){
            return customerSkuApproveManageService.messageReject(param);
        } else if (status.equals(AuditStatusEnum.REVOKE.getCode())){
            return customerSkuApproveManageService.messageRevoke(param);
        } else if (status.equals(AuditStatusEnum.APPROVING.getCode())){
            return customerSkuApproveManageService.messageApproving(param);
        } else {
            log.error("status is error");
            return null;
        }
    }

    @Override
    public Boolean deleteById(Long id){
        if(id == null){
            log.error("CustomerSkuPriceManageServiceImpl.deleteById id is null");
            return Boolean.FALSE;
        }
        return customerSkuPriceDraftAtomicService.removeById(id);
    }

    @Override
    public SalePriceCalculateResVO calculateCustomerSalePrice(SalePriceCalculateVO input) {
        log.info("CustomerSkuPriceManageServiceImpl.calculateCustomerSalePrice");
        CustomerVO customerVO = customerSkuPriceManageService.getAndValidateCustomer(input.getClientCode());
        return this.getCustomerSkuPriceManageService(customerVO.getCountry()).calculateCustomerSalePrice(input);
    }

    @Override
    public PageInfo<CustomerSkuPriceAuditResVO> approvePage(CustomerSkuPriceAuditReqVO param) {
        this.buildQuery(param);
        long total = customerSkuPriceDraftAtomicService.getApproveTotal(param);
        PageInfo<CustomerSkuPriceAuditResVO> result = new PageInfo<>();
        result.setIndex(param.getIndex());
        result.setSize(param.getSize());
        result.setTotal(total);
        if (total == 0) {
            result.setRecords(Collections.emptyList());
            return result;
        }
        // 分页查询草稿
        List<CustomerSkuPriceAuditResVO> dbRecordList = customerSkuPriceDraftAtomicService.listApproveSearch(param);
        result.setRecords(dbRecordList);
        if(CollectionUtils.isNotEmpty(result.getRecords())){
            // 设置skuName
            this.setSkuName(result.getRecords());
            // 设置mkuId
            this.setMkuId(result.getRecords());
            // 设置品牌名称
            this.setBrandName(result.getRecords());
            // 设置类目名称
            this.setCatIdAndName(result.getRecords());
            // 设置客户名称
            this.setClientName(result.getRecords());
            // 设置供应商名称
            this.setVendorName(result.getRecords());
            // 设置单号
            this.setApplyCode(result.getRecords());
            // 设置利润率乘以百
            this.setProfitRate(result.getRecords());
            // 设置有效失效
            this.setEnableStatus(result.getRecords());
            // 设置客户SKU价格审计结果的生效时间
            this.setBeginAndEndTime(result.getRecords());
        }
        // 设置自定义列
        this.handleProcessFromData(result.getRecords());
        return result;
    }

    /**
     * 处理表单数据
     */
    private void handleProcessFromData(List<CustomerSkuPriceAuditResVO> dbRecordList) {
        if (CollectionUtils.isEmpty(dbRecordList)) {
            return;
        }

        for (CustomerSkuPriceAuditResVO item : dbRecordList) {
            String processFromData = item.getProcessFromData();

            // 获取动态列
            Map<String, String> customColumns = auditService.getCustomColumnMap(processFromData, AuditFormEnum.VIP_PRICE_AUDIT.getCode());

            item.setCustomColumns(customColumns);
        }
    }

    /**
     * 获取审计数量的VO对象
     * @param reqVo 客户SKU价格草稿请求VO，包含查询条件
     * @return 审计数量的VO对象，包含审计状态和数量
     */
    private CustomerSkuPriceAuditNumVO getAuditNumVO(CustomerSkuPriceDraftReqVO reqVo) {
        buildQuery(reqVo);
        long total = customerSkuPriceManageService.getTotal(reqVo);
        return CustomerSkuPriceAuditNumVO.builder()
                .auditStatus(AuditStatusEnum.APPROVED.getCode())
                .typeKey(reqVo.getTypeKey())
                .num(total)
                .build();
    }

    /**
     * 获取草稿审核数量的VO对象。
     * @param reqVo 草稿请求VO，包含审核状态等信息。
     * @return 审核数量的VO对象，包括审核状态和总数。
     */
    private CustomerSkuPriceAuditNumVO getDraftAuditNumVO(CustomerSkuPriceDraftReqVO reqVo) {
        buildQuery(reqVo);
        long total = customerSkuPriceDraftAtomicService.getTotal(reqVo);
        return CustomerSkuPriceAuditNumVO.builder()
                .auditStatus(reqVo.getAuditStatus())
                .level(reqVo.getLevel())
                .sourceCountryCode(reqVo.getSourceCountryCode())
                .targetCountryCodes(reqVo.getTargetCountryCodes())
                .typeKey(reqVo.getTypeKey())
                .num(total)
                .build();
    }

    /**
     * 根据国家代码获取对应的客户 SKU 价格管理服务。
     * @param countryCode 国家代码
     * @return 对应的客户 SKU 价格管理服务，若国家代码为空则返回 null
     */
    private CustomerSkuPriceApproveService getCustomerSkuApproveManageService(String countryCode){
        if(StringUtils.isBlank(countryCode)){
            return null;
        }
        return customerSkuPriceApproveServiceMap.get(CustomerSkuApproveCountryEnums.getEnumByCountryCode(countryCode).getServiceName());
    }

    /**
     * 根据国家代码获取对应的客户 SKU 价格管理服务。
     * @param countryCode 国家代码
     * @return 对应的客户 SKU 价格管理服务，若国家代码为空则返回 null
     */
    private CustomerSkuPriceEnableApproveService getCustomerSkuPriceEnableApproveService(String countryCode){
        if(StringUtils.isBlank(countryCode)){
            return null;
        }
        return customerSkuPriceEnableApproveServiceMap.get(CustomerSkuEnableApproveCountryEnums.getEnumByCountryCode(countryCode).getServiceName());
    }

    /**
     * 根据国家代码获取对应的客户 SKU 价格管理服务。
     * @param countryCode 国家代码
     * @return 对应的客户 SKU 价格管理服务，若国家代码为空则返回 null
     */
    private CustomerSkuSalePriceManageService getCustomerSkuPriceManageService(String countryCode){
        if(StringUtils.isBlank(countryCode)){
            return null;
        }
        return customerSkuSalePriceManageServiceMap.get(CustomerSkuPriceCountryEnums.getEnumByCountryCode(countryCode).getServiceName());
    }

    private TaxRateManageService taxRateManageService(String type) {
        return taxRateManageServiceMap.get(SkuTaxRateDraftEnums.getEnumByType(type).getServiceName());
    }

    /**
     * 创建或更新巴西税率。
     * @param input 客户 SKU 价格草稿对象
     * @param customer 客户对象
     * @param skuTaxRateDraftEnums SKU 税率草稿枚举类型
     */
    private void createBRTaxRate(CustomerSkuPriceDraftVO input,CustomerVO customer,SkuTaxRateDraftEnums skuTaxRateDraftEnums){
        if(!customer.getCountry().equals(CountryConstant.COUNTRY_BR)){
            log.info("CustomerSkuPriceManageServiceImpl.setBRTaxRate");
            return;
        }
        TaxRateManageService taxRateManageService = taxRateManageService(skuTaxRateDraftEnums.getType());
        List<TaxRateVO> taxRateList = input.getTaxRateList();
        for (TaxRateVO taxRateVO : taxRateList){
            if(StringUtils.isBlank(taxRateVO.getCountryCode())){
                taxRateVO.setCountryCode(CountryConstant.COUNTRY_BR);
            }
        }
        // 保存货更新税率
        taxRateManageService.saveOrUpdateBatch(taxRateList);
    }

    private void buildQuery(CustomerSkuPriceAuditReqVO param){

        param.setCurrentAuditor(LoginContextHolder.getLoginContextHolder().getPin());

        Set<Long> skuIdSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(param.getSkuIds())){
            skuIdSet.addAll(param.getSkuIds());
        }

        if(CollectionUtils.isNotEmpty(param.getMkuIds())){
            List<MkuRelationPO> mkuRelationPOS = mkuRelationAtomicService.queryBindListByMkuIds(param.getMkuIds());
            Set<Long> skuIds = new HashSet<>();
            if(CollectionUtils.isNotEmpty(mkuRelationPOS)){
                skuIds = mkuRelationPOS.stream().filter(item->item.getSkuId() != null)
                        .map(MkuRelationPO::getSkuId).collect(Collectors.toSet());
            }
            if(CollectionUtils.isNotEmpty(skuIds)){
                if(CollectionUtils.isNotEmpty(skuIdSet)){
                    skuIdSet.retainAll(skuIds);
                }else {
                    skuIdSet.addAll(skuIds);
                }
            }
            if(CollectionUtils.isEmpty(skuIdSet)){
                skuIdSet.add(-1L);
            }
        }
        param.setSkuIds(new ArrayList<>(skuIdSet));

        // 类目id转换为终极类目id
/*        Set<Long> lastCatIds = this.getLastCatIds(param);
        param.setCatIds(lastCatIds);*/

        List<String> bizIds = applyInfoAtomicService.selectWaitAuditBizId(
                Lists.newArrayList(JoySkyBizFlowTypeEnum.PRODUCT_SALE_PRICE_FLOW,JoySkyBizFlowTypeEnum.SALE_RATE_FAILED_EFFECTIVE_FLOW)
                ,param.getApplyCode(),param.getCurrentAuditor());
        if(CollectionUtils.isEmpty(bizIds)){
            bizIds = new ArrayList<>();
            bizIds.add("-1");
        }

        param.setIds(bizIds);

        if(param.getProfitRateBegin() != null){
            param.setProfitRateBegin(param.getProfitRateBegin().divide(Constant.DECIMAL_HUNDRED,4, RoundingMode.HALF_UP));
        }

        if(param.getProfitRateEnd() != null){
            param.setProfitRateEnd(param.getProfitRateEnd().divide(Constant.DECIMAL_HUNDRED,4, RoundingMode.HALF_UP));
        }
    }

    /**
     * 获取最后一级分类的ID集合。
     * @param input 包含分类ID信息的请求对象。
     * @return 最后一级分类的ID集合。
     */
    public Set<Long> getLastCatIds(CustomerSkuPriceAuditReqVO input){
        Long firstCatId = input.getFirstCatId();
        Long secondCatId = input.getSecondCatId();
        Long thirdCatId = input.getThirdCatId();
        Long lastCatId = input.getLastCatId();

        Set<Long> result = new HashSet<>();
        if(lastCatId != null){
            result.add(lastCatId);
            return result;
        }

        if(thirdCatId != null){
            List<Long> thirdCatIds = categoryOutService.categoryLevel4From1234(thirdCatId);
            if(CollectionUtils.isNotEmpty(thirdCatIds)){
                result.addAll(thirdCatIds);
            }
            return result;
        }

        if(secondCatId != null){
            List<Long> secondCatIds = categoryOutService.categoryLevel4From1234(secondCatId);
            if(CollectionUtils.isNotEmpty(secondCatIds)){
                result.addAll(secondCatIds);
            }
            return result;
        }

        if(firstCatId != null){
            List<Long> firstCatIds = categoryOutService.categoryLevel4From1234(firstCatId);
            if(CollectionUtils.isNotEmpty(firstCatIds)){
                result.addAll(firstCatIds);
            }
            return result;
        }
        return null;
    }

    private void setSkuName(List<CustomerSkuPriceAuditResVO> params){
        if(CollectionUtils.isEmpty(params)){
            return;
        }
        List<Long> skuIds = params.stream().map(CustomerSkuPriceAuditResVO::getSkuId).collect(Collectors.toList());
        //补充sku名称
        SkuExternalReqVO skuQuery = new SkuExternalReqVO();
        skuQuery.setSkuIds(skuIds);
        skuQuery.setScopeEnums(Sets.newHashSet(SkuScopeEnum.BASE,SkuScopeEnum.LANG));
        Map<Long, ExternalVO> skuMap = skuExternalManageService.querySkuInfo(skuQuery);
        for (CustomerSkuPriceAuditResVO target : params) {
            ExternalVO skuLang = skuMap.get(target.getSkuId());
            if(skuLang!=null){
                SpuLangVO zhName = skuLang.getLangVOList().stream().filter(line -> LangConstant.LANG_ZH.equals(line.getLang())).findFirst().orElse(null);
                target.setSkuName(zhName!=null?zhName.getSpuTitle():null);
            }
        }
    }

    private void setMkuId(List<CustomerSkuPriceAuditResVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        List<Long> skuIds = inputs.stream().map(CustomerSkuPriceAuditResVO::getSkuId).collect(Collectors.toList());
        List<MkuRelationPO> mkuRelationPOS = mkuRelationAtomicService.queryBindListBySkuIds(skuIds);
        Map<Long,Long> mkuIdMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(mkuRelationPOS)){
            mkuIdMap.putAll(mkuRelationPOS.stream()
                    .filter(item -> item.getSkuId() != null && item.getMkuId() != null)
                    .collect(Collectors.toMap(
                            MkuRelationPO::getSkuId,
                            MkuRelationPO::getMkuId,
                            (existing, replacement) -> existing  // 如果有重复的key，保留现有的值
                    )));
        }
        inputs.forEach(item->{
            item.setMkuId(mkuIdMap.get(item.getSkuId()));
        });
    }

    /**
     * 根据品牌ID列表查询品牌名称并设置到输入列表中。
     * @param inputs 包含品牌ID的 CountryMkuPageVO.Response 列表
     */
    private void setBrandName(List<CustomerSkuPriceAuditResVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Set<Long> brandIds = inputs.stream().map(CustomerSkuPriceAuditResVO::getBrandId).collect(Collectors.toSet());
        Map<Long, String> brandNameMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(brandIds)){
            brandNameMap.putAll(brandOutService.queryNameByIds(brandIds,LangContextHolder.get()));
        }

        inputs.forEach(item->{
            item.setBrandName(brandNameMap.get(item.getBrandId()));
        });
    }

    /**
     * 设置每个 CountryMkuPageVO.Response 对象的 catName 属性，根据 lastCatId 从 categoryOutService 中获取对应的分类名称。
     * @param inputs 包含 lastCatId 的 CountryMkuPageVO.Response 对象列表
     */
    private void setCatIdAndName(List<CustomerSkuPriceAuditResVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Set<Long> lastCatIds = inputs.stream().map(CustomerSkuPriceAuditResVO::getCatId).collect(Collectors.toSet());
        Map<Long, String> lastCatMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(lastCatIds)){
            Map<Long, String> tempMap = categoryOutService.queryPathStr(lastCatIds, LangContextHolder.get());
            if (MapUtils.isNotEmpty(tempMap)) {
                lastCatMap.putAll(tempMap);
            }
        }
        inputs.forEach(item->{
            item.setCatName(lastCatMap.get(item.getCatId()));
        });
    }


    /**
     * 设置客户名称
     * @param inputs 客户信息列表
     */
    private void setClientName(List<CustomerSkuPriceAuditResVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Map<String,CustomerVO> customerVOMap = customerManageService.map();
        if(MapUtils.isEmpty(customerVOMap)){
            return;
        }

        inputs.forEach(item->{
            CustomerVO customerVO = customerVOMap.get(item.getClientCode());
            if(customerVO != null){
                item.setClientName(customerVO.getClientName());
            }
        });
    }

    /**
     * 设置供应商名称
     * @param inputs 设置供应商名称
     */
    private void setVendorName(List<CustomerSkuPriceAuditResVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }

        Map<String,Set<String>> countrySupplierCodeMap = inputs.stream()
                .filter(item -> StringUtils.isNotBlank(item.getVendorCode())
                        && StringUtils.isNotBlank(item.getSourceCountryCode()))
                .collect(Collectors.groupingBy(
                        CustomerSkuPriceAuditResVO::getSourceCountryCode,
                        Collectors.mapping(CustomerSkuPriceAuditResVO::getVendorCode, Collectors.toSet())
                ));
        if(MapUtils.isEmpty(countrySupplierCodeMap)){
            return;
        }
        Map<String, String> supplierMap = new HashMap<>();
        countrySupplierCodeMap.forEach((k,v)->{
            if(CountryConstant.COUNTRY_ZH.equals(k)){
                return;
            }
            Map<String, String> resultPart = supplierAllInfoAtomicService.querySupplierNameByCodes(v, k);
            supplierMap.putAll(resultPart);
        });
        countrySupplierCodeMap.forEach((k,v)->{
            if(!CountryConstant.COUNTRY_ZH.equals(k)){
                return;
            }
            Map<String, String> resultPart = vendorManageService.getVendorMapByCodes(v);
            supplierMap.putAll(resultPart);
        });
        inputs.forEach(item->{
            item.setVendorName(supplierMap.get(item.getVendorCode()));
        });
    }

    /**
     * 设置单号
     * @param inputs 包含供应商代码和国家代码的列表
     */
    private void setApplyCode(List<CustomerSkuPriceAuditResVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }

        List<Long> ids = inputs.stream().map(CustomerSkuPriceAuditResVO::getId).collect(Collectors.toList());
        List<ApplyInfoPO> allApplyInfoPOS = new ArrayList<>();
        List<ApplyInfoPO> applyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.PRODUCT_SALE_PRICE_FLOW,ids);
        if(CollectionUtils.isNotEmpty(applyInfoPOS)){
            allApplyInfoPOS.addAll(applyInfoPOS);
        }
        List<ApplyInfoPO> applyInfoPOS2 = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.SALE_RATE_FAILED_EFFECTIVE_FLOW,ids);
        if(CollectionUtils.isNotEmpty(applyInfoPOS2)){
            allApplyInfoPOS.addAll(applyInfoPOS2);
        }
        if(CollectionUtils.isEmpty(allApplyInfoPOS)){
            return;
        }

        Map<String, String> applyCodeMap = allApplyInfoPOS.stream()
                .collect(Collectors.toMap(
                        ApplyInfoPO::getBizId,           // key mapper
                        ApplyInfoPO::getApplyCode,    // value mapper
                        (existing, replacement) -> existing  // 处理重复key的情况
                ));
        inputs.forEach(item->{
            item.setApplyCode(applyCodeMap.get(item.getId().toString()));
        });
    }

    private void setProfitRate(List<CustomerSkuPriceAuditResVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }

        inputs.forEach(item->{
            item.setProfitRate(item.getProfitRate()!= null?item.getProfitRate().multiply(Constant.DECIMAL_HUNDRED):null);
        });
    }

    @Override
    public String batchEnableApprove(CustomerSkuAuditVO input) {
        List<Long> ids = input.getIds();
        List<CustomerSkuPriceDetailDraftPO> detailDraftPOS = customerSkuPriceDetailDraftAtomicService.getValidByIds(ids);
        if(CollectionUtils.isEmpty(detailDraftPOS)){
            return Constant.SUCCESS;
        }
        Set<Long> draftIds = detailDraftPOS.stream().map(CustomerSkuPriceDetailDraftPO::getBizId).collect(Collectors.toSet());
        List<CustomerSkuPriceDraftPO> draftPOS = customerSkuPriceDraftAtomicService.getValidByIds(draftIds);
        if(CollectionUtils.isEmpty(draftPOS)){
            return Constant.SUCCESS;
        }

        List<ApplyInfoPO> applyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.SALE_RATE_FAILED_EFFECTIVE_FLOW,input.getIds());
        if(CollectionUtils.isEmpty(applyInfoPOS)){
            throw new BizException("无匹配数据,数据已变更，请刷新页面");
        }
        Map<String,ApplyInfoPO> applyInfoPOMap = applyInfoPOS.stream().collect(Collectors.toMap(ApplyInfoPO::getBizId, Function.identity()));

        Map<Long,String> targetCountrtMap = draftPOS.stream().collect(Collectors.toMap(CustomerSkuPriceDraftPO::getId,CustomerSkuPriceDraftPO::getTargetCountryCode));

        String message = "";
        for(CustomerSkuPriceDetailDraftPO detailDraftPO : detailDraftPOS){
            ApplyInfoPO applyInfoPO = applyInfoPOMap.get(detailDraftPO.getId().toString());
            try {
                String targetCountryCode = targetCountrtMap.get(detailDraftPO.getBizId());
                if(StringUtils.isBlank(targetCountryCode)){
                    log.error("CustomerSkuPriceDraftManageServiceImpl.batchEnableApprove targetCountryCode is null, detailDraftPO:{}",detailDraftPO);
                    continue;
                }

                input.setIds(Lists.newArrayList(detailDraftPO.getId()));
                Objects.requireNonNull(this.getCustomerSkuPriceEnableApproveService(targetCountryCode)).singleApprove(detailDraftPO,input,applyInfoPO);
            }catch (Exception e){
                log.error("CustomerSkuPriceDraftManageServiceImpl.getCustomerSkuPriceEnableApproveService.singleApprove error, detailDraftPO:{}",detailDraftPO,e);
                message = String.format("%s：%s%s", applyInfoPO.getApplyCode(), e.getMessage(),"\n");
            }
        }
        return StringUtils.isNotBlank(message) ? message : Constant.SUCCESS;
    }

    @Override
    public String batchEnableReject(CustomerSkuAuditVO input) {
        List<Long> ids = input.getIds();
        List<CustomerSkuPriceDetailDraftPO> detailDraftPOS = customerSkuPriceDetailDraftAtomicService.getValidByIds(ids);
        if(CollectionUtils.isEmpty(detailDraftPOS)){
            return Constant.SUCCESS;
        }
        Set<Long> draftIds = detailDraftPOS.stream().map(CustomerSkuPriceDetailDraftPO::getBizId).collect(Collectors.toSet());
        List<CustomerSkuPriceDraftPO> draftPOS = customerSkuPriceDraftAtomicService.getValidByIds(draftIds);
        if(CollectionUtils.isEmpty(draftPOS)){
            return Constant.SUCCESS;
        }

        List<ApplyInfoPO> applyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.SALE_RATE_FAILED_EFFECTIVE_FLOW,input.getIds());
        if(CollectionUtils.isEmpty(applyInfoPOS)){
            throw new BizException("无匹配数据,数据已变更，请刷新页面");
        }
        Map<String,ApplyInfoPO> applyInfoPOMap = applyInfoPOS.stream().collect(Collectors.toMap(ApplyInfoPO::getBizId, Function.identity()));

        Map<Long,String> targetCountrtMap = draftPOS.stream().collect(Collectors.toMap(CustomerSkuPriceDraftPO::getId,CustomerSkuPriceDraftPO::getTargetCountryCode));

        String message = "";
        for(CustomerSkuPriceDetailDraftPO detailDraftPO : detailDraftPOS){
            ApplyInfoPO applyInfoPO = applyInfoPOMap.get(detailDraftPO.getId().toString());
            try{
                String targetCountryCode = targetCountrtMap.get(detailDraftPO.getBizId());
                if(StringUtils.isBlank(targetCountryCode)){
                    log.error("CustomerSkuPriceDraftManageServiceImpl.batchEnableReject targetCountryCode is null, detailDraftPO:{}",detailDraftPO);
                    continue;
                }

                input.setIds(Lists.newArrayList(detailDraftPO.getId()));
                Objects.requireNonNull(this.getCustomerSkuPriceEnableApproveService(targetCountryCode)).singleReject(detailDraftPO,input,applyInfoPO);
            }catch (Exception e){
                log.error("CustomerSkuPriceDraftManageServiceImpl.getCustomerSkuPriceEnableApproveService.singleReject error, detailDraftPO:{}",detailDraftPO,e);
                message = String.format("%s：%s%s", applyInfoPO.getApplyCode(), e.getMessage(),"\n");
            }
        }
        return StringUtils.isNotBlank(message) ? message : Constant.SUCCESS;
    }

    @Override
    public String batchEnableRevoke(CustomerSkuAuditVO input) {
        List<Long> ids = input.getIds();
        List<CustomerSkuPriceDetailDraftPO> detailDraftPOS = customerSkuPriceDetailDraftAtomicService.getValidByIds(ids);
        if(CollectionUtils.isEmpty(detailDraftPOS)){
            return Constant.SUCCESS;
        }

        Set<Long> draftIds = detailDraftPOS.stream().map(CustomerSkuPriceDetailDraftPO::getBizId).collect(Collectors.toSet());
        List<CustomerSkuPriceDraftPO> draftPOS = customerSkuPriceDraftAtomicService.getValidByIds(draftIds);
        if(CollectionUtils.isEmpty(draftPOS)){
            return Constant.SUCCESS;
        }

        List<ApplyInfoPO> applyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.SALE_RATE_FAILED_EFFECTIVE_FLOW,input.getIds());
        if(CollectionUtils.isEmpty(applyInfoPOS)){
            throw new BizException("无匹配数据,数据已变更，请刷新页面");
        }
        Map<String,ApplyInfoPO> applyInfoPOMap = applyInfoPOS.stream().collect(Collectors.toMap(ApplyInfoPO::getBizId, Function.identity()));

        Map<Long,String> targetCountrtMap = draftPOS.stream().collect(Collectors.toMap(CustomerSkuPriceDraftPO::getId,CustomerSkuPriceDraftPO::getTargetCountryCode));

        for(CustomerSkuPriceDetailDraftPO detailDraftPO : detailDraftPOS){
            String targetCountryCode = targetCountrtMap.get(detailDraftPO.getBizId());
            if(StringUtils.isBlank(targetCountryCode)){
                log.error("CustomerSkuPriceDraftManageServiceImpl.batchEnableRevoke targetCountryCode is null, detailDraftPO:{}",detailDraftPO);
                continue;
            }

            input.setIds(Lists.newArrayList(detailDraftPO.getId()));
            Objects.requireNonNull(this.getCustomerSkuPriceEnableApproveService(targetCountryCode)).singleRevoke(detailDraftPO,input,applyInfoPOMap.get(detailDraftPO.getId().toString()));
        }
        return Constant.SUCCESS;
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean batchJoySkyEnableApprove(String erp, Integer status, String processInstanceId, String msg) {
        if(status == null || StringUtils.isBlank(processInstanceId)){
            throw new BizException("审批人或流程id为空");
        }

        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(JoySkyBizFlowTypeEnum.SALE_RATE_FAILED_EFFECTIVE_FLOW,processInstanceId);
        if(applyInfoPO != null && applyInfoPO.getAuditStatus() != null && AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.info(String.format("CustomerSkuPriceDraftManageServiceImpl.batchJoySkyEnableApprove applyInfoPO 审批记录为空或审批状态不匹配,processInstanceId:%s",processInstanceId));
            return Boolean.FALSE;
        }

        CustomerSkuPriceDetailDraftPO detailDraftPO = customerSkuPriceDetailDraftAtomicService.getValidById(Long.valueOf(applyInfoPO.getBizId()));
        if(detailDraftPO == null ||detailDraftPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != detailDraftPO.getAuditStatus()){
            throw new BizException(String.format("CustomerSkuPriceDraftManageServiceImpl.batchJoySkyEnableApprove detailDraftPO 记录为空或审批状态不匹配,applyInfoPO:%s",applyInfoPO.getBizId()));
        }

        CustomerSkuPriceDraftPO draftPO = customerSkuPriceDraftAtomicService.getValidById(detailDraftPO.getBizId());
        if(draftPO == null){
            throw new BizException(String.format("CustomerSkuPriceDraftManageServiceImpl.batchJoySkyEnableApprove draftPO 记录为空,draftId:%s",detailDraftPO.getBizId()));
        }

        CustomerSkuPriceEnableApproveService customerSkuPriceEnableApproveService = this.getCustomerSkuPriceEnableApproveService(draftPO.getTargetCountryCode());
        if(customerSkuPriceEnableApproveService == null){
            throw new BizException(String.format("CustomerSkuPriceDraftManageServiceImpl.batchJoySkyEnableApprove customerSkuPriceEnableApproveService 为空,countryCode:%s",draftPO.getTargetCountryCode()));
        }

        CustomerSkuAuditVO param = new CustomerSkuAuditVO();
        param.setProcessInstanceId(processInstanceId);
        param.setAuditErp(erp);
        param.setRejectReason(msg);
        if(status.equals(AuditStatusEnum.APPROVED.getCode())){
            return customerSkuPriceEnableApproveService.messageApprove(param);
        } else if (status.equals(AuditStatusEnum.REJECTED.getCode())){
            return customerSkuPriceEnableApproveService.messageReject(param);
        } else if (status.equals(AuditStatusEnum.REVOKE.getCode())){
            return customerSkuPriceEnableApproveService.messageRevoke(param);
        } else if (status.equals(AuditStatusEnum.APPROVING.getCode())){
            return customerSkuPriceEnableApproveService.messageApproving(param);
        } else {
            log.error("status is error");
            return null;
        }
    }

    @Override
    public List<CustomerSkuPriceDraftExportApiDTO> queryCustomerSkuDraft(CustomerSkuPriceDraftReqVO input) {
        input.setNowTime(DateUtil.getCurrentTime());
        input.setTargetCountryCodes(Sets.newHashSet(CountryConstant.COUNTRY_BR));
        input.setOffset(null);
        buildQuery(input);
        Page<CustomerSkuPriceDraftVO> dbRecord = new Page<>(input.getIndex(), input.getSize());
        long total = customerSkuPriceDraftAtomicService.getTotal(input);
        dbRecord.setTotal(total);

        if (total == 0) {
            return Collections.emptyList();
        }

        List<CustomerSkuPriceDraftVO> dbRecordList = customerSkuPriceDraftAtomicService.listSearch(input);
        dbRecord.setRecords(dbRecordList);

        List<CustomerSkuPriceDraftVO> records = dbRecord.getRecords();

        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        Map<Long, ExternalVO> skuMap = querySkuInfo(records.stream().map(CustomerSkuPriceDraftVO::getSkuId).collect(Collectors.toList()));
        records.forEach(target -> processRecord(target, skuMap));

        Map<String, String> supplierMap = buildSupplierMap(records);

        Map<String,CustomerVO> customerVOMap = customerManageService.map();

        TaxRateManageService draftTaxRateManageService = taxRateManageService(SkuTaxRateDraftEnums.DRAFT.getType());
        return buildResultList(records, supplierMap, customerVOMap, draftTaxRateManageService);
    }

    private void processRecord(CustomerSkuPriceDraftVO target, Map<Long, ExternalVO> skuMap) {
        ExternalVO skuLang = skuMap.get(target.getSkuId());
        if (skuLang != null) {
            SpuLangVO zhName = skuLang.getLangVOList().stream()
                    .filter(line -> LangConstant.LANG_ZH.equals(line.getLang()))
                    .findFirst().orElse(null);
            target.setSkuName(zhName != null ? zhName.getSpuTitle() : null);
        }
        setEnableStatus(target);
        setBeginAndEndTime(target);
    }

    private Map<String, String> buildSupplierMap(List<CustomerSkuPriceDraftVO> records) {
        Map<String, Set<String>> countrySupplierCodeMap = records.stream()
                .filter(item -> StringUtils.isNotBlank(item.getVendorCode()) && StringUtils.isNotBlank(item.getSourceCountryCode()))
                .collect(Collectors.groupingBy(CustomerSkuPriceDraftVO::getSourceCountryCode, Collectors.mapping(CustomerSkuPriceDraftVO::getVendorCode, Collectors.toSet())));

        Map<String, String> supplierMap = new HashMap<>();
        countrySupplierCodeMap.forEach((countryCode, vendorCodes) -> {
            Map<String, String> resultPart = CountryConstant.COUNTRY_ZH.equals(countryCode)
                    ? vendorManageService.getVendorMapByCodes(vendorCodes)
                    : supplierAllInfoAtomicService.querySupplierNameByCodes(vendorCodes, countryCode);
            supplierMap.putAll(resultPart);
        });

        return supplierMap;
    }

    private List<CustomerSkuPriceDraftExportApiDTO> buildResultList(List<CustomerSkuPriceDraftVO> records, Map<String, String> supplierMap, Map<String,CustomerVO> customerVOMap, TaxRateManageService draftTaxRateManageService) {
        List<CustomerSkuPriceDraftExportApiDTO> results = new ArrayList<>();
        for (CustomerSkuPriceDraftVO vo : records) {
            CustomerSkuPriceDraftExportApiDTO dto = new CustomerSkuPriceDraftExportApiDTO();
            populateDtoFields(dto, vo, supplierMap, customerVOMap);
            try{
                SalePriceCalculateVO calculateVO = createSalePriceCalculateVO(vo);
                if (CountryConstant.COUNTRY_BR.equals(vo.getTargetCountryCode())) {
                    calculateVO.setSalePriceTaxReq(buildSalePriceTaxReq(vo, draftTaxRateManageService));
                }

                SalePriceCalculateResVO salePriceCalculateResVO = getCustomerSkuPriceManageService(vo.getTargetCountryCode()).calculateCustomerSalePrice(calculateVO);
                populateDtoWithSalePrice(salePriceCalculateResVO, dto);

                results.add(dto);
            }catch (Exception e){
                log.error("CustomerSkuPriceDraftManageServiceImpl.buildResultList is error, vo={}", JSONObject.toJSONString(vo), e);
                results.add(dto);
            }

        }
        return results;
    }

    private SalePriceCalculateVO createSalePriceCalculateVO(CustomerSkuPriceDraftVO vo) {
        SalePriceCalculateVO calculateVO = new SalePriceCalculateVO();
        calculateVO.setSkuId(vo.getSkuId());
        calculateVO.setSalePrice(vo.getCustomerSalePrice());
        calculateVO.setCurrencyCode(vo.getCurrency());
        calculateVO.setClientCode(vo.getClientCode());
        return calculateVO;
    }

    private Map<String, Object> buildSalePriceTaxReq(CustomerSkuPriceDraftVO vo, TaxRateManageService draftTaxRateManageService) {
        TaxRateVO taxRateVO = new TaxRateVO();
        taxRateVO.setCountryCode(vo.getTargetCountryCode());
        taxRateVO.setClientCode(vo.getClientCode());
        taxRateVO.setKeyType(KeyTypeEnum.SKU.getCode());
        taxRateVO.setKeyId(String.valueOf(vo.getSkuId()));
        taxRateVO.setClientType(CustomerTypeEnum.Client.getCode());

        List<TaxRateVO> taxRateVOList = draftTaxRateManageService.listTaxRate(taxRateVO);
        if(CollectionUtils.isEmpty(taxRateVOList)){
            return Collections.emptyMap();
        }
        Map<String, Object> taxRateMap = new HashMap<>();
        for(TaxRateVO taxRateVO1 : taxRateVOList){
            if(CustomerSkuBrTaxEnum.UTILIZATION.name().equals(taxRateVO1.getTaxCode()) && taxRateVO1.getTaxRate() != null){
                taxRateMap.put(taxRateVO1.getTaxCode(),taxRateVO1.getTaxRate().intValue());
            } else {
                taxRateMap.put(taxRateVO1.getTaxCode(),taxRateVO1.getTaxRate());
            }
        }


        return taxRateMap;
    }

    private void populateDtoFields(CustomerSkuPriceDraftExportApiDTO dto, CustomerSkuPriceDraftVO vo, Map<String, String> supplierMap, Map<String,CustomerVO> customerVOMap) {
        dto.setSkuId(vo.getSkuId());
        dto.setSkuName(vo.getSkuName());
        dto.setClientCode(vo.getClientCode());
        CustomerVO customerVO = customerVOMap.get(vo.getClientCode());
        if(customerVO != null){
            dto.setClientName(customerVO.getClientName());
        } else {
            dto.setClientName(vo.getClientName());
        }
        dto.setTargetCountryCode(vo.getTargetCountryCode());
        dto.setCustomerSalePrice(vo.getCustomerSalePrice());
        dto.setVendorCode(vo.getVendorCode());
        dto.setVendorName(supplierMap.get(vo.getVendorCode()));
        dto.setSourceCountryCode(vo.getSourceCountryCode());
        dto.setCurrency(vo.getCurrency());
        dto.setBeginTime(vo.getBeginTime());
        dto.setEndTime(vo.getEndTime());
        dto.setEnableStatus(vo.getEnableStatus());
    }

    private void populateDtoWithSalePrice(SalePriceCalculateResVO resVO, CustomerSkuPriceDraftExportApiDTO dto) {
        dto.setTaxCustomerSalePrice(resVO.getTaxSalePrice());
        dto.setCountryCostPrice(resVO.getCountryCostPrice());
        dto.setAgreementPrice(resVO.getAgreementPrice());
        dto.setRefundTaxPrice(resVO.getRefundTaxPrice());
        dto.setJdPrice(resVO.getJdPrice());
        dto.setCountryWarehousingPrice(resVO.getCountryWarehousingPrice());
        dto.setProfitRate(resVO.getProfitRate());
        dto.setSalePriceTaxRes(resVO.getSalePriceTaxRes());
    }


    /**
     * 将 CustomerSkuPriceDraftPO 和 CustomerSkuPriceDetailDraftPO 对象转换为 CustomerSkuPriceDraftVO 对象。
     * @param customerSkuPriceDraftPO 包含源国代码和目标国代码的 CustomerSkuPriceDraftPO 对象。
     * @param detailDraftPO 包含 SKU ID、客户代码、客户销售价格、开始时间和结束时间的 CustomerSkuPriceDetailDraftPO 对象。
     * @return 转换后的 CustomerSkuPriceDraftVO 对象。
     */
    private CustomerSkuPriceDraftVO convertEnabledApproveInfo(CustomerSkuPriceDraftPO customerSkuPriceDraftPO,CustomerSkuPriceDetailDraftPO detailDraftPO){
        CustomerSkuPriceDraftVO customerSkuPriceDraftVO = new CustomerSkuPriceDraftVO();
        customerSkuPriceDraftVO.setId(detailDraftPO.getId());
        customerSkuPriceDraftVO.setSourceCountryCode(customerSkuPriceDraftPO.getSourceCountryCode());
        customerSkuPriceDraftVO.setTargetCountryCode(customerSkuPriceDraftPO.getTargetCountryCode());
        customerSkuPriceDraftVO.setSkuId(customerSkuPriceDraftPO.getSkuId());
        customerSkuPriceDraftVO.setClientCode(detailDraftPO.getClientCode());
        customerSkuPriceDraftVO.setCustomerSalePrice(detailDraftPO.getCustomerSalePrice());
        customerSkuPriceDraftVO.setBeginTime(detailDraftPO.getBeginTime());
        customerSkuPriceDraftVO.setEndTime(detailDraftPO.getEndTime());

        if(CountryConstant.COUNTRY_BR.equals(customerSkuPriceDraftPO.getTargetCountryCode())){
            TaxRateVO taxRateVO = new TaxRateVO();
            taxRateVO.setCountryCode(customerSkuPriceDraftPO.getTargetCountryCode());
            taxRateVO.setClientCode(detailDraftPO.getClientCode());
            taxRateVO.setKeyType(KeyTypeEnum.SKU.getCode());
            taxRateVO.setKeyId(String.valueOf(detailDraftPO.getSkuId()));
            taxRateVO.setClientType(CustomerTypeEnum.Client.getCode());
            TaxRateManageService draftTaxRateManageService = taxRateManageService(SkuTaxRateDraftEnums.DRAFT.getType());
            List<TaxRateVO> taxRateVOList = draftTaxRateManageService.listTaxRate(taxRateVO);
            customerSkuPriceDraftVO.setTaxRateList(taxRateVOList);
        }
        return customerSkuPriceDraftVO;
    }

    /**
     * 检查输入参数是否合法。
     * @param input CustomerSkuPriceDraftVO 对象，包含开始时间和结束时间。
     */
    private void checkInput(CustomerSkuPriceDraftVO input){
        Long beginTime = input.getBeginTime();
        if(beginTime != null && beginTime.compareTo(DateUtil.getCurrentTime()) < 0){
            throw new BizException("开始时间不能小于当前时间");
        }
        Long endTime = input.getEndTime();
        if(endTime != null && endTime.compareTo(DateUtil.getCurrentTime()) < 0){
            throw new BizException("结束时间时间不能小于当前时间");
        }
    }
}
