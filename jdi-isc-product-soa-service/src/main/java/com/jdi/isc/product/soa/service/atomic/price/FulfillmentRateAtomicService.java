package com.jdi.isc.product.soa.service.atomic.price;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.price.biz.FulfillmentRateVO;
import com.jdi.isc.product.soa.domain.price.po.FulfillmentRatePO;
import com.jdi.isc.product.soa.repository.mapper.price.FulfillmentRateBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 履约费率表原子服务
 * @Author: zhaokun51
 * @Date: 2024/10/29 21:47
 **/
@Slf4j
@Service
public class FulfillmentRateAtomicService extends ServiceImpl<FulfillmentRateBaseMapper, FulfillmentRatePO> {


    /**
     * 未删除的对象
     * @param id id
     * @return 对象
     */
    public FulfillmentRatePO getValidById(Long id){
        LambdaQueryWrapper<FulfillmentRatePO> wrapper = Wrappers.<FulfillmentRatePO>lambdaQuery()
                .eq(FulfillmentRatePO::getId, id)
                .eq(FulfillmentRatePO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    /**
     * 根据国家代码和类目ID获取满足条件的FulfillmentRatePO对象。
     * @param sourceCountryCode 源国家代码
     * @param targetCountryCode 目标国家代码
     * @param firstCatId 第一类目ID
     * @param secondCatId 第二类目ID
     * @param thirdCatId 第三类目ID
     * @param lastCatId 最后类目ID
     * @return 满足条件的FulfillmentRatePO对象
     */
    public FulfillmentRatePO getOne(String sourceCountryCode,String targetCountryCode
            ,Long firstCatId,Long secondCatId,Long thirdCatId,Long lastCatId,Long skuId,Integer isWarehouseProduct){
        LambdaQueryWrapper<FulfillmentRatePO> wrapper = Wrappers.<FulfillmentRatePO>lambdaQuery()
                .eq(FulfillmentRatePO::getSourceCountryCode, sourceCountryCode)
                .eq(FulfillmentRatePO::getTargetCountryCode, targetCountryCode)
                .eq(FulfillmentRatePO::getIsWarehouseProduct, isWarehouseProduct);
        if(firstCatId == null){
            wrapper.isNull(FulfillmentRatePO::getFirstJdCatId);
        } else {
            wrapper.eq(FulfillmentRatePO::getFirstJdCatId, firstCatId);
        }
        if(secondCatId == null){
            wrapper.isNull(FulfillmentRatePO::getSecondJdCatId);
        } else {
            wrapper.eq(FulfillmentRatePO::getSecondJdCatId, secondCatId);
        }
        if(thirdCatId == null){
            wrapper.isNull(FulfillmentRatePO::getThirdJdCatId);
        } else {
            wrapper.eq(FulfillmentRatePO::getThirdJdCatId, thirdCatId);
        }
        if(lastCatId == null){
            wrapper.isNull(FulfillmentRatePO::getLastJdCatId);
        } else {
            wrapper.eq(FulfillmentRatePO::getLastJdCatId, lastCatId);
        }
        if(skuId == null){
            wrapper.isNull(FulfillmentRatePO::getSkuId);
        } else {
            wrapper.eq(FulfillmentRatePO::getSkuId, skuId);
        }
        wrapper.eq(FulfillmentRatePO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    /**
     * 根据国家代码和类目ID获取满足条件的FulfillmentRatePO列表。
     * @return 满足条件的FulfillmentRatePO列表
     */
    @PFTracing
    public List<FulfillmentRatePO> list(FulfillmentRateVO input){
        LambdaQueryWrapper<FulfillmentRatePO> wrapper = Wrappers.<FulfillmentRatePO>lambdaQuery()
            .eq(FulfillmentRatePO::getSourceCountryCode, input.getSourceCountryCode())
            .eq(FulfillmentRatePO::getTargetCountryCode, input.getTargetCountryCode())
            .eq(FulfillmentRatePO::getIsWarehouseProduct, input.getIsWarehouseProduct())
            .and(w-> w
                .apply("1=1")
                .or()
                .eq(input.getCategoryPathVO().getId1()!= null,FulfillmentRatePO::getFirstJdCatId, input.getCategoryPathVO().getId1())
                .or()
                .eq(input.getCategoryPathVO().getId2() != null,FulfillmentRatePO::getSecondJdCatId,input.getCategoryPathVO().getId2())
                .or()
                .eq(input.getCategoryPathVO().getId3() != null,FulfillmentRatePO::getThirdJdCatId,input.getCategoryPathVO().getId3())
                .or()
                .eq(input.getCategoryPathVO().getId4() != null,FulfillmentRatePO::getLastJdCatId,input.getCategoryPathVO().getId4()))
                .isNull(FulfillmentRatePO::getSkuId)
                .eq(FulfillmentRatePO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    /** 根据货源国、目的国和sku查询履约费率*/
    public FulfillmentRatePO getBySkuId(FulfillmentRateVO input){
        LambdaQueryWrapper<FulfillmentRatePO> wrapper = Wrappers.<FulfillmentRatePO>lambdaQuery()
                .eq(FulfillmentRatePO::getSourceCountryCode, input.getSourceCountryCode())
                .eq(FulfillmentRatePO::getTargetCountryCode, input.getTargetCountryCode())
                .eq(FulfillmentRatePO::getSkuId,input.getSkuId())
                .eq(FulfillmentRatePO::getIsWarehouseProduct,input.getIsWarehouseProduct())
                .eq(FulfillmentRatePO::getYn,YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    public List<FulfillmentRatePO> listBySkuIds(List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Lists.newArrayList();

        }
        LambdaQueryWrapper<FulfillmentRatePO> wrapper = new LambdaQueryWrapper<FulfillmentRatePO>()
                .in(FulfillmentRatePO::getSkuId, skuIds)
                .eq(FulfillmentRatePO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(wrapper);
    }

    /**
     * 根据源国家代码、目标国家代码和一级京东分类ID查询满足条件的履约率PO列表。
     * @param sourceCountryCode 源国家代码
     * @param targetCountryCode 目标国家代码
     * @param firstJdCatId 一级京东分类ID
     * @return 履约率PO列表
     */
    public List<FulfillmentRatePO> queryListByCountryCodeAndFirstJdCatId(String sourceCountryCode,String targetCountryCode,Long firstJdCatId) {
        if(StringUtils.isBlank(sourceCountryCode) || StringUtils.isBlank(targetCountryCode) || firstJdCatId == null){
            return null;

        }
        LambdaQueryWrapper<FulfillmentRatePO> wrapper = new LambdaQueryWrapper<FulfillmentRatePO>()
                .select(FulfillmentRatePO::getSourceCountryCode,
                        FulfillmentRatePO::getTargetCountryCode,
                        FulfillmentRatePO::getFirstJdCatId,
                        FulfillmentRatePO::getSecondJdCatId,
                        FulfillmentRatePO::getThirdJdCatId,
                        FulfillmentRatePO::getLastJdCatId)
                .eq(FulfillmentRatePO::getSourceCountryCode, sourceCountryCode)
                .eq(FulfillmentRatePO::getTargetCountryCode, targetCountryCode)
                .eq(FulfillmentRatePO::getFirstJdCatId, firstJdCatId)
                .eq(FulfillmentRatePO::getYn, YnEnum.YES.getCode())
                .orderByDesc(FulfillmentRatePO::getCreateTime);

        return this.list(wrapper);
    }
}
