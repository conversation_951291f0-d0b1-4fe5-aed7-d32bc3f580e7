package com.jdi.isc.product.soa.service.manage.approveorder.template.create;


import cn.hutool.extra.spring.SpringUtil;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.biz.component.api.enums.JoySkyBizFlowTypeEnum;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveFormDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderSaveReqDTO;
import com.jdi.isc.product.soa.api.approveorder.common.AuditBizTypeEnum;
import com.jdi.isc.product.soa.api.approveorder.res.ApproveOrderSaveResDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.common.util.BigDecimalUtil;
import com.jdi.isc.product.soa.common.util.StringUtils;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceWarningPO;
import com.jdi.isc.product.soa.domain.enums.AuditFormEnum;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.biz.ProfitRateVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.rpc.utils.ExceptionUtil;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceWarningAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.price.ProfitCalculateManageService;
import com.jdi.isc.product.soa.service.manage.price.warning.customerprice.manager.CustomerPriceWarningManager;
import com.jdi.isc.product.soa.service.manage.spu.SpuManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component(value = CustomerPriceWarningCreateApproveOrderExe.BEAN_NAME)
public class CustomerPriceWarningCreateApproveOrderExe extends AbstractCreateApproveOrderExe {

    @Resource
    private CustomerSkuPriceWarningAtomicService customerSkuPriceWarningAtomicService;

    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private CustomerManageService customerManageService;

    @Resource
    protected CountryManageService countryManageService;

    @Resource
    protected CustomerSkuPriceDetailAtomicService customerSkuPriceDetailAtomicService;

    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;

    @Resource
    private ProfitCalculateManageService profitCalculateManageService;

    @Resource
    private CustomerPriceWarningManager customerPriceWarningManager;

    public static final String BEAN_NAME = "customerPriceWarningCreateApproveOrderExe";

    @Override
    public int getBizType() {
        return AuditBizTypeEnum.TYPE_2.getCode();
    }

    @Override
    public String getBeanName() {
        return BEAN_NAME;
    }

    public DataResponse<ApproveOrderSaveResDTO> execute(ApproveOrderSaveReqDTO input) {
        return super.execute(input);
    }

    @Override
    protected boolean isAutoAuditPass(ApproveOrderSaveReqDTO input) {
        List<ApproveFormDTO> orderFormList = input.getOrderFormList();

        Map<String, String> fieldValueMap = orderFormList.stream().collect(Collectors.toMap(ApproveFormDTO::getFieldName, ApproveFormDTO::getFieldValue));

        BigDecimal profitRate = new BigDecimal(fieldValueMap.get(PROFIT_RATE));
        BigDecimal profitLimitRate = new BigDecimal(fieldValueMap.get(PROFIT_LIMIT_RATE));

        boolean isAutoAuditPass = BigDecimalUtil.gt(profitRate, profitLimitRate);

        log.info("客制价-不可售阈值审批. isAutoAuditPass={}, profitRate={}, profitLimitRate={}", isAutoAuditPass, profitRate, profitLimitRate);

        return isAutoAuditPass;
    }

    @Override
    protected DataResponse<ApproveOrderSaveResDTO> executeAutoAuditPass(ApproveOrderSaveReqDTO input) {

        String warnId = input.getOrder().getBizId();

        Map<String, String> fieldValueMap = input.getOrderFormList().stream().collect(Collectors.toMap(ApproveFormDTO::getFieldName, ApproveFormDTO::getFieldValue));

        BigDecimal profitRate = new BigDecimal(fieldValueMap.get(PROFIT_RATE));

        try {
            customerPriceWarningManager.updateUnsellableThreshold(Long.parseLong(warnId), profitRate, input.getPin());
        } catch (Exception e) {
            log.info("更新客制价-不可售阈值失败. warnId={}, profitRate={}", warnId, profitRate, e);
            return DataResponse.error(ExceptionUtil.getMessage(e, "更新不可售阈值失败!"));
        } finally {
            log.info("更新客制价-不可售阈值完成. warnId={}, profitRate={}", warnId, profitRate);
        }
        return DataResponse.success();
    }

    @Override
    public int getFlowTypeCode() {
        return JoySkyBizFlowTypeEnum.CUSTOMER_SKU_PRICE_LINE_FLOW.getFlowTypeCode();
    }

    @Override
    protected void fillOrderFormDefaultValue(ApproveOrderSaveReqDTO input) {

        ApproveOrderDTO order = input.getOrder();
        String customerPriceWarnId = order.getBizId();

        CustomerSkuPriceWarningPO warning = customerSkuPriceWarningAtomicService.getValidById(Long.valueOf(customerPriceWarnId));

        if (warning == null) {
            throw new ProductBizException("不存在的预警信息. %s", customerPriceWarnId);
        }

        Long skuId = warning.getSkuId();

        // 获取sku名称
        String spuName = SpringUtil.getBean(SpuManageService.class).getSpuNameBySkuId(skuId, StringUtils.isEmpty(input.getLang()) ? LangConstant.LANG_ZH : input.getLang());

        SkuPO sku = skuAtomicService.getBySkuIdsAndYn(skuId, null);
        if (sku == null) {
            log.warn("sku不存. skuId={}", skuId);
            throw new ProductBizException("sku不存. %s", skuId);
        }

        CustomerVO customer = customerManageService.getValidByClientCode(warning.getClientCode());

        String countryName = countryManageService.getCountryNameByCountryCode(customer.getCountry(), LangConstant.LANG_ZH);

        Long bizId = warning.getBizId();

        if (bizId == null) {
            throw new ProductBizException("找不到关联的客制化价格id. %s", customerPriceWarnId);
        }

        CustomerSkuPriceDetailPO skuPriceDetail = customerSkuPriceDetailAtomicService.getValidById(bizId);

        if (skuPriceDetail == null) {
            log.warn("找不到客制化价格信息. id={}", bizId);
            throw new ProductBizException("找不到客制化价格信息. %s", bizId);
        }

        CountryAgreementPricePO countryAgreementPrice = countryAgreementPriceAtomicService.getValidBySkuIdAndTargetCountryCode(skuId, customer.getCountry());

        BigDecimal customerSalePrice = skuPriceDetail.getCustomerSalePrice();
        BigDecimal countryCostPrice = countryAgreementPrice.getCountryCostPrice();
        if (customerSalePrice == null || countryCostPrice == null) {
            throw new ProductBizException("未设置销售价或成本价. %s", skuId);
        }

        if (StringUtils.isEmpty(skuPriceDetail.getCurrency())) {
            throw new ProductBizException("VIP价未设置币种. %s", skuId);
        }

        // 利润率
        String profitRate = SpringUtil.getBean(ProfitCalculateManageService.class)
                .calculateSalePriceProfitRate(customerSalePrice, countryCostPrice)
                .multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString();

        ProfitRateVO profitRateVO = profitCalculateManageService.getProfitLimitRate(sku.getJdCatId(), sku.getSourceCountryCode(), customer.getCountry());

        DataResponse<Boolean> profitRateResponse = profitCalculateManageService.checkProfitRate(profitRateVO);
        if (!profitRateResponse.getSuccess()) {
            throw new ProductBizException(profitRateResponse.getMessage());
        }

        Map<String, String> keyTitleMap = this.getKeyTitleMap();

        // 国际SKUID
        super.addFiled(keyTitleMap, input, "skuId", String.valueOf(skuId));
        // 商品名称
        super.addFiled(keyTitleMap, input, "spuName", spuName);
        // 客户名称
        super.addFiled(keyTitleMap, input, "clientName", customer.getClientName());
        // 客户国家
        super.addFiled(keyTitleMap, input, "countryName", countryName);
        // 客制化销售价
        super.addFiled(keyTitleMap, input, "customerSalePrice", customerSalePrice.stripTrailingZeros().toPlainString() + skuPriceDetail.getCurrency());
        // 国家成本价
        super.addFiled(keyTitleMap, input, "countryCostPrice", countryCostPrice.stripTrailingZeros().toPlainString() + skuPriceDetail.getCurrency());
        // 利润率-前毛
        super.addFiled(keyTitleMap, input, PROFIT_RATE, profitRate);
        // 利润率阈值-前毛
        super.addFiled(keyTitleMap, input, PROFIT_LIMIT_RATE, profitRateVO.getProfitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        // 超低利润率阈值-前毛
        super.addFiled(keyTitleMap, input, "lowProfitLimitRate", profitRateVO.getLowProfitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        // 生效时间-北京时间
        super.addFiled(keyTitleMap, input, "bjEffectiveTime", CustomerSkuPriceAtomicService.getEffectiveTime(skuPriceDetail.getBeginTime(), skuPriceDetail.getEndTime(), CountryConstant.COUNTRY_ZH));
        // 生效时间-当地时间
        super.addFiled(keyTitleMap, input, "bdEffectiveTime", CustomerSkuPriceAtomicService.getEffectiveTime(skuPriceDetail.getBeginTime(), skuPriceDetail.getEndTime(),customer.getCountry()));
        // 申请原因
        super.addFiled(keyTitleMap, input, "applyReason", order.getApplyReason());
        // 提交人
        super.addFiled(keyTitleMap, input, "applyUserErp", order.getApplyUserErp());
        super.fillOrderFormDefaultValue(input);

        // 补充货源国和目标国
        input.setSourceCountryCode(sku.getSourceCountryCode());
        input.setTargetCountryCode(customer.getCountry());
    }

    @Override
    public Map<String, Object> constructProcessControlData(ApproveOrderSaveReqDTO input) {

        String sourceCountryCode = input.getSourceCountryCode();
        String targetCountryCode = input.getTargetCountryCode();


        //流程控制参数校验
        if (org.apache.commons.lang3.StringUtils.isBlank(sourceCountryCode) || org.apache.commons.lang3.StringUtils.isBlank(targetCountryCode)) {
            throw new BizException("流程控制参数不能为空!,商品来源国:"+sourceCountryCode+",客户所在国:" + targetCountryCode);
        }

        //流程参数赋值
        Map<String, Object> flowCalculeVarMap = new HashMap<>(3);
        flowCalculeVarMap.put("productCountry", sourceCountryCode);
        flowCalculeVarMap.put("customerCountry", targetCountryCode);
        return flowCalculeVarMap;
    }

    @Override
    public String getAuditFormKey() {
        return AuditFormEnum.VIP_PRICE_LINE_AUDIT.getCode();
    }

}
