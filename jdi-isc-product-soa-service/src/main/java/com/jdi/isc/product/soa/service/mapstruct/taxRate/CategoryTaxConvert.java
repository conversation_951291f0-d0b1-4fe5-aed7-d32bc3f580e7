package com.jdi.isc.product.soa.service.mapstruct.taxRate;

import com.jdi.isc.product.soa.domain.taxRate.biz.CategoryTaxPageVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CategoryTaxVO;
import com.jdi.isc.product.soa.domain.taxRate.po.CategoryTaxPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 类目税率信息对象转换
 * @Author: taxuezheng1
 * @Date: 2024/03/26 17:51
 **/
@Mapper
public interface CategoryTaxConvert {

    CategoryTaxConvert INSTANCE = Mappers.getMapper(CategoryTaxConvert.class);

    @Mapping(source = "categoryId",target = "jdCatId")
    CategoryTaxPO vo2Po(CategoryTaxVO vo);

    @Mapping(source = "jdCatId",target = "categoryId")
    CategoryTaxVO po2Vo(CategoryTaxPO po);

    List<CategoryTaxVO> listPo2Vo(List<CategoryTaxPO> poList);

    List<CategoryTaxPO> listVo2Po(List<CategoryTaxVO> voList);

    List<CategoryTaxPageVO.Response> pageListPo2Vo(List<CategoryTaxPO> pos);
}
