package com.jdi.isc.product.soa.service.protocol.jsf.price;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.AuditColumnConfigDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.IscSkuPriceReadApiService;
import com.jdi.isc.product.soa.api.price.supplierPrice.req.SkuPriceAuditPageReqDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.req.SkuPricePageReqDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.req.SkuPriceQueryReqDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.req.SkuPurchasePriceReqDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.res.SkuPriceApiDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.res.SkuPriceAuditPageApiDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.res.SkuPricePageApiDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.res.SkuPurchasePriceApiDTO;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceVO;
import com.jdi.isc.product.soa.domain.price.supplierPrice.biz.*;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceDraftManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceManageService;
import com.jdi.isc.product.soa.service.mapstruct.price.SkuPriceConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.List;

@Slf4j
@Service
public class IscSkuPriceReadApiServiceImpl implements IscSkuPriceReadApiService, InitializingBean {
    private static final String DRAFT = "draft";
    private static final String FORMAL = "formal";

    @Resource
    private SkuPriceManageService skuPriceManageService;
    @Resource
    private SkuPriceDraftManageService skuPriceDraftManageService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<SkuPricePageApiDTO>> pageSearch(SkuPricePageReqDTO dto) {
        log.info("IscSkuPriceReadApiServiceImpl.pageSearch input:{}", JSONObject.toJSONString(dto));
        ApiInitUtils.init(dto);
        SkuPricePageReqVO reqVo = SkuPriceConvert.INSTANCE.reqDto2reqVo(dto);
        reqVo.setTradeDirection(TradeDirectionEnum.SUPPLIER);
        PageInfo<SkuPricePageVO> pageInfoRes = null;
        if(StringUtils.equals(reqVo.getTypeKey(),FORMAL)){
            pageInfoRes = skuPriceManageService.pageSearch(reqVo);
        }else  if(StringUtils.equals(reqVo.getTypeKey(),DRAFT)){
            pageInfoRes = skuPriceDraftManageService.pageSearch(reqVo);
        }
        PageInfo<SkuPricePageApiDTO> pageInfoApiRes = SkuPriceConvert.INSTANCE.pageVo2PageDto(pageInfoRes);
        return DataResponse.success(pageInfoApiRes);
    }

    @Override
    public DataResponse<SkuPriceApiDTO> detail(SkuPriceQueryReqDTO dto) {
        log.info("IscSkuPriceReadApiServiceImpl.detail input:{}", JSONObject.toJSONString(dto));
        ApiInitUtils.init(dto);
        SkuPriceQueryReqVO reqVo = SkuPriceConvert.INSTANCE.reqDto2Vo(dto);
        SkuPriceVO detail = skuPriceManageService.detailById(reqVo);
        SkuPriceApiDTO skuPriceApiDTO = SkuPriceConvert.INSTANCE.vo2Dto(detail);
        return DataResponse.success(skuPriceApiDTO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<SkuPriceAuditPageApiDTO>> approvePageSearch(SkuPriceAuditPageReqDTO dto) {
        log.info("IscSkuPriceReadApiServiceImpl.approvePageSearch input:{}", JSONObject.toJSONString(dto));
        ApiInitUtils.init(dto);
        SkuPriceAuditPageReqVO reqVo = SkuPriceConvert.INSTANCE.reqDto2reqVo(dto);
        PageInfo<SkuPriceAuditPageVO> skuPricePageInfo = skuPriceDraftManageService.approveSearch(reqVo);
        PageInfo<SkuPriceAuditPageApiDTO> pageInfoApiRes = SkuPriceConvert.INSTANCE.auditPageVo2PageDto(skuPricePageInfo);
        return DataResponse.success(pageInfoApiRes);
    }


    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<SkuPurchasePriceApiDTO>> getPurchasePrice(SkuPurchasePriceReqDTO reqDTO) {
        log.info("IscSkuPriceReadApiServiceImpl.getPurchasePrice input:{}", JSONObject.toJSONString(reqDTO));
        SkuPurchasePriceReqVO reqVO = SkuPriceConvert.INSTANCE.reqDto2PriceReqVo(reqDTO);
        List<SkuPurchasePriceVO> purchasePriceList = skuPriceManageService.getPurchasePrice(reqVO);
        List<SkuPurchasePriceApiDTO> skuPurchasePriceApiDTOList = SkuPriceConvert.INSTANCE.priceReqVo2ReqDto(purchasePriceList);
        return DataResponse.success(skuPurchasePriceApiDTOList);
    }

    @Override
    public DataResponse<List<AuditColumnConfigDTO>> getCustomColumns(String key) {
        return skuPriceDraftManageService.getCustomColumns(key);
    }

    @Override
    public void afterPropertiesSet() {

//        SkuPriceAuditPageReqDTO dto = JSONObject.parseObject("{\"index\":1,\"lang\":\"zh\",\"offset\":0,\"pin\":\"liuzhaoming.10\",\"size\":20,\"systemCode\":\"WIMP\"}", SkuPriceAuditPageReqDTO.class);
//
//        DataResponse<PageInfo<SkuPriceAuditPageApiDTO>> pageInfoDataResponse = this.approvePageSearch(dto);
//        log.error("pageInfoDataResponse={}", pageInfoDataResponse);
    }
}
