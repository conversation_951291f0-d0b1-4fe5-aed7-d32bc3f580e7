package com.jdi.isc.product.soa.service.protocol.jsf.price.impl;

import com.alibaba.fastjson.JSONObject;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.price.SkuPriceMonitorApiService;
import com.jdi.isc.product.soa.api.price.biz.SkuPriceMonitorSpuPageApiDTO;
import com.jdi.isc.product.soa.api.price.biz.SkuPurchasePriceCompareApiDTO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceMonitorSpuPageVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPurchasePriceCompareVO;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceMonitorManageService;
import com.jdi.isc.product.soa.service.mapstruct.price.SkuPriceMonitorConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: 价格监控api服务实现
 * @Author: zhaojianguo21
 * @Date: 2024/12/11 17:13
 **/

@Slf4j
@Service
public class SkuPriceMonitorApiServiceImpl implements SkuPriceMonitorApiService {

    @Resource
    private SkuPriceMonitorManageService skuPriceMonitorManageService;

    @Value("${jdi.isc.task.env}")
    private String env;

    @ToolKit
    @Override
    public DataResponse<PageInfo<SkuPriceMonitorSpuPageApiDTO.Response>> pageSearchSpu(SkuPriceMonitorSpuPageApiDTO.Request input){
        PageInfo<SkuPriceMonitorSpuPageVO.Response> invokeResult = null;
        try {
            SkuPriceMonitorSpuPageVO.Request reqVO = SkuPriceMonitorConvert.INSTANCE.pageReqApi2PageReqVo(input);
            invokeResult = skuPriceMonitorManageService.pageSearchSpu(reqVO);
            if (null==invokeResult){
                log.warn("pageSearchSpu, invokeResult fail. input={}", JSONObject.toJSONString(input));
                return DataResponse.error("查询失败");
            }

            PageInfo<SkuPriceMonitorSpuPageApiDTO.Response> pageInfoRes = SkuPriceMonitorConvert.INSTANCE.pageResVo2PageResApi(invokeResult);
            return DataResponse.success(pageInfoRes);
        } catch (Exception e) {
            log.error("pageSearchSpu exception. input={}", JSONObject.toJSONString(input), e);
            return DataResponse.error("发生异常。");
        }
    }

    @Override
    public DataResponse<SkuPurchasePriceCompareApiDTO.Response> comparePurchasePrice(SkuPurchasePriceCompareApiDTO.Request input) {
        SkuPurchasePriceCompareVO.Response invokeResult = null;
        CallerInfo callerInfo = Profiler.registerInfo(env+"-com.jdi.isc.product.soa.service.protocol.jsf.price.impl.SkuPriceMonitorApiServiceImpl.comparePurchasePrice");
        try {
            SkuPurchasePriceCompareVO.Request reqVO = SkuPriceMonitorConvert.INSTANCE.skuPurchasePriceCompareApiDtoReq2Vo(input);
            invokeResult = skuPriceMonitorManageService.comparePurchasePrice(reqVO);
            if (null==invokeResult){
                log.warn("comparePurchasePrice, invokeResult fail. input={}", JSONObject.toJSONString(input));
                return DataResponse.error("比价失败");
            }

            SkuPurchasePriceCompareApiDTO.Response res = SkuPriceMonitorConvert.INSTANCE.skuPurchasePriceCompareVoRes2ApiDto(invokeResult);
            return DataResponse.success(res);
        } catch (Exception e) {
            log.error("comparePurchasePrice exception. input={}", JSONObject.toJSONString(input), e);
            Profiler.functionError(callerInfo);
            return DataResponse.error("发生异常。");
        }finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }
}