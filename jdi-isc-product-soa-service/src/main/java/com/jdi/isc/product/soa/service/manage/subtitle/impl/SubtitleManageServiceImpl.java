package com.jdi.isc.product.soa.service.manage.subtitle.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.domain.category.biz.CategoryIdVO;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import com.jdi.isc.product.soa.domain.ducc.ChatGptProperties;
import com.jdi.isc.product.soa.domain.mku.po.MkuLangPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPricePageVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.subtitle.biz.SubtitlePageReqVO;
import com.jdi.isc.product.soa.domain.subtitle.biz.SubtitlePageResVO;
import com.jdi.isc.product.soa.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.subtitle.SubtitleAtomicService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.mku.MkuRelationManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.subtitle.SubtitleManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SubtitleManageServiceImpl extends BaseManageSupportService<SubtitlePageResVO, CountryMkuPO> implements SubtitleManageService {

    @Resource
    private CategoryOutService categoryOutService;

    @Resource
    private MkuRelationManageService mkuRelationManageService;

    @Resource
    private SkuReadManageService skuReadManageService;

    @Resource
    private SubtitleAtomicService subtitleAtomicService;

    @Resource
    private MkuLangAtomicService mkuLangAtomicService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private OperDuccConfig operDuccConfig;

    private static final String ALL = "all";
    private static final String MAINTAIN = "maintain";

    @Override
    public PageInfo<SubtitlePageResVO> pageSearch(SubtitlePageReqVO vo) {
        PageInfo<SubtitlePageResVO> pageInfo = new PageInfo<>();
        pageInfo.setSize(vo.getSize());
        pageInfo.setIndex(vo.getIndex());
        this.buildQuery(vo);
        long total = subtitleAtomicService.pageSearchTotal(vo);
        if (total == 0) {
            pageInfo.setTotal(0);
            return pageInfo;
        }
        List<SubtitlePageResVO> subtitleList = subtitleAtomicService.pageSearch(vo);
        if (CollectionUtils.isEmpty(subtitleList)){
            return pageInfo;
        }
        this.setMkuTitle(subtitleList);
        this.setCatIdAndName(subtitleList);
        this.setSkuId(subtitleList);
        this.setSpuId(subtitleList);
        pageInfo.setTotal(total);
        pageInfo.setRecords(subtitleList);
        return pageInfo;
    }
    /**
     * 获取指定类型的字幕审核状态数量。
     * @param input 包含查询条件的对象，包括字幕类型、语言等信息。
     * @return 包含不同类型字幕审核状态数量的列表。
     */
    @Override
    public List<SubtitlePageReqVO> auditStatusNum(SubtitlePageReqVO input) {
        this.buildQuery(input);
        List<SubtitlePageReqVO> auditNumVOResp = new ArrayList<>();
        input.setTypeKey(ALL);
        auditNumVOResp.add(this.getNumVO(input));
        input.setTypeKey(MAINTAIN);
        auditNumVOResp.add(this.getNumVO(input));
        return auditNumVOResp;
    }

    /**
     * 根据输入的 CountryMkuPageVO.Request 对象，查询并设置总记录数，返回更新后的 Request 对象。
     * @param input 待查询的 CountryMkuPageVO.Request 对象。
     * @return 更新后的 CountryMkuPageVO.Request 对象。
     */
    private SubtitlePageReqVO getNumVO(SubtitlePageReqVO input) {
        long total = subtitleAtomicService.pageSearchTotal(input);
        SubtitlePageReqVO result = new SubtitlePageReqVO();
        BeanUtil.copyProperties(input,result);
        result.setNum(total);
        return result;
    }

    /**
     * 构建查询条件。
     * @param vo 包含查询条件的对象。
     */
    private void buildQuery(SubtitlePageReqVO vo){
        Set<Long> mkuIdSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(vo.getMkuIds())){
            mkuIdSet.addAll(vo.getMkuIds());
        }
        if(CollectionUtils.isNotEmpty(vo.getSpuIds())){
            List<SkuPO> skuList = skuReadManageService.getSkuListBySpuIds(vo.getSpuIds());
            List<Long> skuIdList = Optional.ofNullable(skuList).orElseGet(ArrayList::new)
                .stream().map(SkuPO::getSkuId).collect(Collectors.toList());
            List<Long> skuIds = vo.getSkuIds();
            if(CollectionUtils.isEmpty(skuIds)){
                skuIds = new ArrayList<>();
            }
            if(CollectionUtils.isNotEmpty(vo.getSkuIds())) {
                skuIds.retainAll(skuIdList);
                vo.setSkuIds(skuIds);
            }else {
                vo.setSkuIds(skuIdList);
            }
        }
        if(CollectionUtils.isNotEmpty(vo.getSkuIds())){
            Map<Long, Long> skuMkuMap = mkuRelationManageService.queryMkuIdBySkuIds(new HashSet<>(vo.getSkuIds()));
            if(CollectionUtils.isNotEmpty(mkuIdSet)){
                mkuIdSet.retainAll(skuMkuMap.values());
                if(CollectionUtils.isEmpty(mkuIdSet)){
                    mkuIdSet.add(-1L);
                }
            }else {
                if(MapUtils.isEmpty(skuMkuMap)){
                    mkuIdSet.retainAll(skuMkuMap.values());
                }else {
                    mkuIdSet.addAll(skuMkuMap.values());
                }
            }
        }
        // 类目id转换为终极类目id
        Set<Long> lastCatIds = this.getLastCatIds(vo);
        vo.setCatIds(lastCatIds);
        vo.setMkuIds(new ArrayList<>(mkuIdSet));
        ChatGptProperties chatGptProperties = operDuccConfig.preseChatGpt();
        Integer contentLength = chatGptProperties.getContentLength().get(vo.getTargetCountryCode());
        vo.setContentLength(contentLength);
    }


    /**
     * 根据输入的类目ID获取最后一级类目的ID集合。
     * @param input SubtitlePageReqVO对象，包含要查询的类目ID信息。
     * @return 最后一级类目的ID集合，若无符合条件的类目则返回null。
     */
    private Set<Long> getLastCatIds(SubtitlePageReqVO input){
        CategoryIdVO categoryIdVO = new CategoryIdVO();
        categoryIdVO.setFirstCatId(input.getFirstCatId());
        categoryIdVO.setSecondCatId(input.getSecondCatId());
        categoryIdVO.setThirdCatId(input.getThirdCatId());
        categoryIdVO.setLastCatId(input.getLastCatId());
        Set<Long> catIdSet = categoryOutService.queryLastCatId(categoryIdVO);
        return catIdSet;
    }



    /**
     * 设置字幕页的MKU标题（中文）
     * @param subtitleVOList 字幕页列表
     */
    private void setMkuTitle(List<SubtitlePageResVO> subtitleVOList){
        if(CollectionUtils.isEmpty(subtitleVOList)){
            return;
        }
        List<Long> mkuIds = subtitleVOList.stream().map(SubtitlePageResVO::getMkuId).collect(Collectors.toList());
        Map<Long, MkuLangPO> mkuLangMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(mkuIds)){
            mkuLangMap = mkuLangAtomicService.getMkuLangNameByMkuIds(mkuIds,LangConstant.LANG_ZH);
        }
        for (SubtitlePageResVO vo : subtitleVOList){
            MkuLangPO mkuLangPOzh = mkuLangMap.get(vo.getMkuId());
            if(Objects.nonNull(mkuLangPOzh)){
                vo.setZhMkuTitle(mkuLangPOzh.getMkuTitle());
            }
        }
    }


    /**
     * 设置短标题的分类 ID 和名称。
     * @param subtitleVOList 字幕页列表
     */
    private void setCatIdAndName(List<SubtitlePageResVO> subtitleVOList){
        if(CollectionUtils.isEmpty(subtitleVOList)){
            return;
        }
        Set<Long> lastCatIds = subtitleVOList.stream().map(SubtitlePageResVO::getLastCatId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> lastCatMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(lastCatIds)){
            Map<Long, String> tempMap = categoryOutService.queryPathStr(lastCatIds, LangContextHolder.get());
            if (MapUtils.isNotEmpty(tempMap)) {
                lastCatMap.putAll(tempMap);
            }
        }
        subtitleVOList.forEach(item->{
            item.setCatName(lastCatMap.get(item.getLastCatId()));
        });
    }

    /**
     * 设置子标题页面的 SKU ID 列表。
     * @param subtitleVOList 子标题页面列表。
     */
    private void setSkuId(List<SubtitlePageResVO> subtitleVOList){
        if(CollectionUtils.isEmpty(subtitleVOList)){
            return;
        }
        List<Long> mkuList = subtitleVOList.stream().map(SubtitlePageResVO::getMkuId).collect(Collectors.toList());
        Map<Long, List<MkuRelationPO>> mkuIdPoListMap = mkuRelationManageService.queryMkuBindSkuListMap(mkuList);
        for (SubtitlePageResVO subtitlePageResVO : subtitleVOList){
            if(mkuIdPoListMap.containsKey(subtitlePageResVO.getMkuId())){
                List<MkuRelationPO> mkuRelationPOS = mkuIdPoListMap.get(subtitlePageResVO.getMkuId());
                List<Long> skuIdList = mkuRelationPOS.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toList());
                subtitlePageResVO.setSkuIds(skuIdList);
            }
        }
    }
    /**
     * 设置字幕页资源对象列表的SPU ID。
     * @param subtitleVOList 字幕页资源对象列表
     */
    private void setSpuId(List<SubtitlePageResVO> subtitleVOList){
        if(CollectionUtils.isEmpty(subtitleVOList)){
            return;
        }
        List<Long> skuIdList = subtitleVOList.stream()
            .map(SubtitlePageResVO::getSkuIds)
            .filter(Objects::nonNull)
            .flatMap(List::stream)
            .collect(Collectors.toList());
        Map<Long, SkuPO> skuMap = skuAtomicService.getSkuMap(new HashSet<>(skuIdList));
        for (SubtitlePageResVO subtitlePageResVO : subtitleVOList){
            List<Long> skuIds = subtitlePageResVO.getSkuIds();
            List<Long> spuIdList = new ArrayList<>();
            for (Long skuId : skuIds){
                if(skuMap.containsKey(skuId)){
                    SkuPO skuPO = skuMap.get(skuId);
                    spuIdList.add(skuPO.getSpuId());
                }
            }
            subtitlePageResVO.setSpuIds(spuIdList);
        }
    }

}

