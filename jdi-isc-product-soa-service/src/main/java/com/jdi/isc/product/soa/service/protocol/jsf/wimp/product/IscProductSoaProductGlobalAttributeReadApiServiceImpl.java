package com.jdi.isc.product.soa.service.protocol.jsf.wimp.product;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.spu.res.GroupPropertyDTO;
import com.jdi.isc.product.soa.api.wimp.product.IscProductSoaProductGlobalAttributeReadApiService;
import com.jdi.isc.product.soa.api.wimp.product.req.GetProductGlobalAttributeReqDTO;
import com.jdi.isc.product.soa.api.wimp.product.res.ProductGlobalAttributeResDTO;
import com.jdi.isc.product.soa.common.enums.AttributeDimensionEnum;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.domain.spu.biz.GroupPropertyVO;
import com.jdi.isc.product.soa.service.atomic.sku.SkuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDraftAtomicService;
import com.jdi.isc.product.soa.service.manage.category.GlobalAttributeManageService;
import com.jdi.isc.product.soa.service.mapstruct.ProductAttributeConvertService;
import com.jdi.isc.product.soa.service.mapstruct.category.GlobalAttributeConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description：查询商品跨境属性信息
 * @Date 2025-02-10
 */
@Slf4j
@Service
public class IscProductSoaProductGlobalAttributeReadApiServiceImpl implements IscProductSoaProductGlobalAttributeReadApiService {

    @Resource
    private SkuDraftAtomicService skuDraftAtomicService;

    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;

    @Resource
    private GlobalAttributeManageService globalAttributeManageService;

    @Resource
    private ProductAttributeConvertService productAttributeConvertService;

    @LafValue("jdi.isc.product.soa.sku.global.attribute.config")
    private String globalAttributeConfig;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<ProductGlobalAttributeResDTO>> batchQuerySkuDraftGlobalAttributeMap(GetProductGlobalAttributeReqDTO reqDTO) {
        List<ProductGlobalAttributeResDTO> resDTOList = Lists.newArrayList();
        // 查询SKU维度的跨境属性列表
        if (AttributeDimensionEnum.SKU.getCode() == reqDTO.getDimension()) {
            Set<String> reqDTOSkuIds = reqDTO.getSkuIds();
            // 跨境属性配置为空直接返回
            if (StringUtils.isBlank(globalAttributeConfig)) {
                return DataResponse.success(resDTOList);
            }
            // 国家编码
            String countryCode = reqDTO.getCountryCode();
            Set<Long> attributeIds = ConfigUtils.getValueByKey(globalAttributeConfig, countryCode, Set.class);
            if (CollectionUtils.isEmpty(attributeIds)) {
                return DataResponse.success(resDTOList);
            }
            // 查询制定跨境属性信息
            List<GroupPropertyVO> groupPropertyVOS = globalAttributeManageService.queryGroupPropertyVos(attributeIds, AttributeDimensionEnum.SKU.getCode(), LangConstant.LANG_ZH, countryCode, Lists.newArrayList(countryCode));
            // 根据skuId集合查询跨境属性草稿
            Set<Long> skuIds = reqDTOSkuIds.stream().filter(StringUtils::isNumeric).map(Long::valueOf).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(skuIds)) {
                Map<Long, SkuDraftPO> skuDraftMap = skuDraftAtomicService.querySkuInterPropertyMapBySkuIds(skuIds);
                for (Map.Entry<Long, SkuDraftPO> entry : skuDraftMap.entrySet()) {
                    resDTOList.add(this.getProductGlobalAttributeResDTO(entry.getValue(), groupPropertyVOS));
                }
            }
            // 根据sku key集合查询跨境属性草稿
            Set<String> skuKeys = reqDTOSkuIds.stream().filter(id -> !StringUtils.isNumeric(id)).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(skuKeys)) {
                Map<String, SkuDraftPO> skuKeyDraftMap = skuDraftAtomicService.querySkuInterPropertyMapBySkuKeys(skuKeys);
                for (Map.Entry<String, SkuDraftPO> entry : skuKeyDraftMap.entrySet()) {
                    resDTOList.add(this.getProductGlobalAttributeResDTO(entry.getValue(), groupPropertyVOS));
                }
            }
        }
        return DataResponse.success(resDTOList);
    }

    private ProductGlobalAttributeResDTO getProductGlobalAttributeResDTO(SkuDraftPO skuDraftPO, List<GroupPropertyVO> groupPropertyVOS) {
        ProductGlobalAttributeResDTO resDTO = new ProductGlobalAttributeResDTO();
        resDTO.setSpuId(skuDraftPO.getSpuId());
        resDTO.setSkuId(skuDraftPO.getSkuId());
        resDTO.setSkuKey(skuDraftPO.getSkuKey());

        List<GroupPropertyVO> draftGroupPropertyVos = JSON.parseObject(skuDraftPO.getSkuInterProperty(), new TypeReference<List<GroupPropertyVO>>() {
        });

        if (CollectionUtils.isEmpty(draftGroupPropertyVos)) {
            resDTO.setGroupPropertyDTOList(GlobalAttributeConvert.INSTANCE.listGroupPropertyVo2Dto(groupPropertyVOS));
        }else {
            // 将草稿中的数据和跨境属性信息标记为已选中
            List<GroupPropertyVO> handledGroupPropertyVos = productAttributeConvertService.convertAttributeFromDraft(draftGroupPropertyVos, groupPropertyVOS);
            resDTO.setGroupPropertyDTOList(GlobalAttributeConvert.INSTANCE.listGroupPropertyVo2Dto(handledGroupPropertyVos));
        }
        return resDTO;
    }
}
