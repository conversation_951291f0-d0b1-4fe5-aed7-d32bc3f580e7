package com.jdi.isc.product.soa.service.manage.mkuTag.impl;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuReqVO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.mkuTag.biz.MkuTagPageVO;
import com.jdi.isc.product.soa.domain.mkuTag.biz.MkuTagVO;
import com.jdi.isc.product.soa.domain.mkuTag.po.MkuTagPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.specialAttr.biz.SpecialAttrTagVO;
import com.jdi.isc.product.soa.domain.specialAttr.po.SpecialAttrPO;
import com.jdi.isc.product.soa.domain.specialAttr.po.SpecialAttrRelationPO;
import com.jdi.isc.product.soa.domain.specialAttr.po.SpecialAttrValuePO;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.mkuTag.MkuTagAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.specialAttr.SpecialAttrAtomicService;
import com.jdi.isc.product.soa.service.atomic.specialAttr.SpecialAttrRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.specialAttr.SpecialAttrValueAtomicService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.countryMku.CountryMkuManageService;
import com.jdi.isc.product.soa.service.manage.mkuTag.MkuTagManageService;
import com.jdi.isc.product.soa.service.mapstruct.mkuTag.MkuTagConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 国际商品标签表数据维护服务实现
 * @Author: wangpeng965
 * @Date: 2024/12/17 13:47
 **/

@Slf4j
@Service
public class MkuTagManageServiceImpl extends BaseManageSupportService<MkuTagVO, MkuTagPO> implements MkuTagManageService {

    @Resource
    private MkuTagAtomicService mkuTagAtomicService;
    @Resource
    private SpecialAttrRelationAtomicService specialAttrRelationAtomicService;
    @Resource
    private SpecialAttrAtomicService specialAttrAtomicService;
    @Resource
    private CountryManageService countryManageService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;
    @Resource
    private SpecialAttrValueAtomicService specialAttrValueAtomicService;

    @Resource
    @Lazy
    private CountryMkuManageService countryMkuManageService;

    @Override
    public DataResponse<Boolean> resetBinding(MkuTagVO mkuTagVO) {
        Set<Long> skuSet = new HashSet<>();
        skuSet.add(mkuTagVO.getSkuId());
        List<MkuTagPO> mkuTagPOList = new ArrayList<>();
        List<String> countryCodeList = countryManageService.getCountryCodeList();
        SkuPO skuPo = skuAtomicService.getSkuPoBySkuId(mkuTagVO.getSkuId());
        MkuRelationPO mkuPo = mkuRelationAtomicService.getMkuBySkuId(mkuTagVO.getSkuId());
        // 通过skuId查询 商品标  tag keyId， keyValueId
        List<SpecialAttrRelationPO> specialAttrRelationPOList = specialAttrRelationAtomicService.queryBySkus(skuSet);
        if(CollectionUtils.isEmpty(specialAttrRelationPOList)){
            return DataResponse.success();
        }
        Set<Long> tagKeyIdList = specialAttrRelationPOList.stream().map(SpecialAttrRelationPO::getAttributeKeyId).collect(Collectors.toSet());
        // 通过tagKeyId查询 商品标名称和标授权国家
        List<SpecialAttrPO> specialAttrPOList = specialAttrAtomicService.queryByIds(tagKeyIdList);
        Set<String> countrySet = new HashSet<>();
        for (String countryCode : countryCodeList) {
            // 过滤和当前目标授权的国家
            Set<Long> tagKeyIdSet = specialAttrPOList.stream().filter(po -> {
                if (StringUtils.isBlank(po.getCountryScope()) || po.getCountryScope().contains(countryCode)) {
                    return true;
                } else {
                    return false;
                }
            }).map(SpecialAttrPO::getId).collect(Collectors.toSet());

            List<SpecialAttrRelationPO> poList = specialAttrRelationPOList.stream().filter(po -> {
                if (tagKeyIdSet.contains(po.getAttributeKeyId())) {
                    return true;
                } else {
                    return false;
                }
            }).collect(Collectors.toList());

            for (SpecialAttrRelationPO specialAttrRelationPO : poList){
                MkuTagPO mkuTagPO = new MkuTagPO();
                mkuTagPO.setSkuId(skuPo.getSkuId());
                mkuTagPO.setSpuId(skuPo.getSpuId());
                mkuTagPO.setSourceCountryCode(skuPo.getSourceCountryCode());
                mkuTagPO.setMkuId(mkuPo.getMkuId());
                mkuTagPO.setTargetCountryCode(countryCode);
                if(specialAttrRelationPO.getAttributeKeyId() == 9L){
                    countrySet.add(countryCode);
                }
                mkuTagPO.setAttributeKeyId(specialAttrRelationPO.getAttributeKeyId());
                mkuTagPO.setAttributeValueId(specialAttrRelationPO.getAttributeValueId());
                mkuTagPO.setCreateTime(new Date().getTime());
                mkuTagPO.setUpdateTime(new Date().getTime());
                mkuTagPO.setCreator("System");
                mkuTagPO.setUpdater("System");
                mkuTagPOList.add(mkuTagPO);
            }
        }
        Boolean result = removeBySkuId(mkuTagVO);
        if(result){
            result = mkuTagAtomicService.saveOrUpdateBatch(mkuTagPOList);
        }
        if(result){
            for (MkuTagPO mkuTagPO : mkuTagPOList) {
                if(countrySet.contains(mkuTagPO.getTargetCountryCode())) {
                    CountryMkuReqVO reqVO = new CountryMkuReqVO();
                    reqVO.setCountryCode(mkuTagPO.getTargetCountryCode());
                    reqVO.setMkuId(mkuTagPO.getMkuId());
                    DataResponse<Boolean> response = countryMkuManageService.anewMkuJoinCountryPool(reqVO);
                }
            }
        }
        return DataResponse.success(result);
    }



    /**
     * 根据 MkuTagPageVO 对象查询对应的 MKU ID 列表。
     * @param pageVO 包含查询条件的 MkuTagPageVO 对象。
     * @return 符合条件的 MKU ID 集合。
     */
    @Override
    public Set<Long> queryMkuIdList(MkuTagPageVO pageVO) {
        Set<Long> mkuIdSet = mkuTagAtomicService.getMkuIdSet(pageVO);
        return mkuIdSet;
    }

    /**
     * 根据 SKU ID 删除 MkuTagPO 对象。
     * @param mkuTagVO 包含 SKU ID 的 MkuTagVO 对象。
     * @return 删除操作是否成功。
     */
    private Boolean removeBySkuId(MkuTagVO mkuTagVO) {
        List<MkuTagPO> poList = mkuTagAtomicService.getPoBySkuId(mkuTagVO);
        if(CollectionUtils.isEmpty(poList)){
            return true;
        }
        List<Long> idList = poList.stream().map(MkuTagPO::getId).collect(Collectors.toList());
        boolean result = mkuTagAtomicService.removeByIds(idList);
        return result;
    }



    /**
     * 获取MkuTagPO列表
     * @param pageVO 分页信息
     * @return MkuTagPO对象列表
     */
    public Map<Long,List<SpecialAttrTagVO>> getMkuTagVOMap(MkuTagPageVO pageVO){
        List<MkuTagPO> poList = mkuTagAtomicService.getPoList(pageVO);
        List<MkuTagVO> mkuTagVOList = MkuTagConvert.INSTANCE.listPo2Vo(poList);
        Set<Long> tagKeyIdSet = mkuTagVOList.stream().map(MkuTagVO::getAttributeKeyId).collect(Collectors.toSet());
        Set<Long> tagValueIdSet = mkuTagVOList.stream().map(MkuTagVO::getAttributeValueId).collect(Collectors.toSet());

        List<SpecialAttrPO> specialAttrPOList = specialAttrAtomicService.queryByIds(tagKeyIdSet);
        List<SpecialAttrValuePO> specialAttrValuePOList = specialAttrValueAtomicService.queryByIds(tagValueIdSet);
        Map<Long, SpecialAttrPO> tagKeyMap = specialAttrPOList.stream().collect(Collectors.toMap(SpecialAttrPO::getId, Function.identity()));
        Map<Long, SpecialAttrValuePO> tagValueMap = specialAttrValuePOList.stream().collect(Collectors.toMap(SpecialAttrValuePO::getId, Function.identity()));

        List<SpecialAttrTagVO> specialAttrTagVOList = MkuTagConvert.INSTANCE.listTagVo2SpecialAttrTagVO(mkuTagVOList);
        for (SpecialAttrTagVO vo : specialAttrTagVOList){
            if(tagKeyMap.containsKey(vo.getAttributeKeyId())){
                SpecialAttrPO specialAttrPO = tagKeyMap.get(vo.getAttributeKeyId());
                vo.setAttributeKeyName(specialAttrPO.getAttributeKeyName());
                vo.setAttributeKey(specialAttrPO.getAttributeKey());
                if(StringUtils.isNotBlank(specialAttrPO.getRemark())){
                    Map<String,String> colorMap =  JSON.parseObject(specialAttrPO.getRemark(), Map.class);
                    vo.setIconColor(colorMap.get("iconColor"));
                }
            }
            if(tagValueMap.containsKey(vo.getAttributeValueId())){
                SpecialAttrValuePO specialAttrValuePO = tagValueMap.get(vo.getAttributeValueId());
                vo.setAttributeValueName(specialAttrValuePO.getAttributeValueName());
                if(StringUtils.isNotBlank(specialAttrValuePO.getRemark())){
                    Map<String,String> dispalyNameMap =  JSON.parseObject(specialAttrValuePO.getRemark(), Map.class);
                    vo.setDisplayName(dispalyNameMap.get("displayName"));
                }
            }
        }
        Map<Long, List<SpecialAttrTagVO>> groupedByMkuId = specialAttrTagVOList.stream()
            .collect(Collectors.groupingBy(SpecialAttrTagVO::getMkuId));
        return groupedByMkuId;
    }


}
