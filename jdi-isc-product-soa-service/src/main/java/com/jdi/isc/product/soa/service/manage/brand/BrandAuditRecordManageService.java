package com.jdi.isc.product.soa.service.manage.brand;

import com.jdi.isc.product.soa.domain.brand.biz.BrandAuditRecordVO;

/**
 * <AUTHOR>
 * @date 2024/8/2
 **/
public interface BrandAuditRecordManageService {

    /**
     * @function 保存或更新品牌审核记录
     * @param brandAuditRecordVO - 待保存或更新的品牌审核记录对象
     * @returns boolean - 成功与否的标志，成功为true，失败为false
     */
    boolean addBrandAuditRecord(BrandAuditRecordVO brandAuditRecordVO);


    /**
     * @function 根据品牌ID获取品牌审核记录信息
     * @param brandId 品牌的唯一标识ID
     * @returns 返回与给定品牌ID相关联的品牌审核记录视图对象
     */
    BrandAuditRecordVO getBrandAuditRecordVOByBrandId(Long brandId);
}
