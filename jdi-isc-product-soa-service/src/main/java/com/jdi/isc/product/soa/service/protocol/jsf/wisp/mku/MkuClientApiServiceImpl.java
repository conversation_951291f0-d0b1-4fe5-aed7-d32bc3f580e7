package com.jdi.isc.product.soa.service.protocol.jsf.wisp.mku;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.wisp.mku.MkuClientApiService;
import com.jdi.isc.product.soa.api.wisp.mku.biz.*;
import com.jdi.isc.product.soa.domain.customerMku.biz.CustomerLatestMkuReqVO;
import com.jdi.isc.product.soa.domain.mku.biz.*;
import com.jdi.isc.product.soa.service.manage.mku.MkuEsManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuManageService;
import com.jdi.isc.product.soa.service.mapstruct.mku.MkuConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2023/12/15 14:46
 **/
@Slf4j
@Service
public class MkuClientApiServiceImpl implements MkuClientApiService {

    @Resource
    private MkuManageService mkuClientManageService;
    @Resource
    private MkuEsManageService mkuEsManageService;


    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<MkuClientApiDTO>> page(MkuClientPageReqApiDTO input) {
        log.info("MkuClientApiServiceImpl.page, param={}", JSONObject.toJSONString(input));
//        MkuClientPageReqVO pageReqVO = MkuConvert.INSTANCE.mkuClientPageReqDTO2VO(input);
//        PageInfo<MkuClientVO> pageInfo = mkuEsManageService.page(pageReqVO);
//        if (null==pageInfo){
//            log.warn("PageInfo<MkuClientVO> null. input={}", JSONObject.toJSONString(input));
//            return DataResponse.error("page record empty.");
//        }
//        PageInfo<MkuClientApiDTO> pageInfoDTO = MkuConvert.INSTANCE.mkuClientPageVO2DTO(pageInfo);
        return  DataResponse.success();
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> upsertToEsBatchPage(String clientCode, Integer startPageNum, Integer endPageNum) {
        if (StringUtils.isBlank(clientCode) || startPageNum<1 || startPageNum>endPageNum ){
            log.info("MkuClientApiServiceImpl.upsertToEsBatchPage, fail, param error. clientCode={}, startPageNum={}, endPageNum={}", clientCode, startPageNum, endPageNum);
            return DataResponse.error("参数错误");
        }

        return  DataResponse.success(mkuEsManageService.upsert2Es4MkuPage(clientCode,startPageNum,endPageNum));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> upsertToEsBatch(MkuClientListInfoReqApiDTO input) {
        log.info("MkuClientApiServiceImpl.upsertToEsBatch, input={}", JSONObject.toJSONString(input));
        List<Long> mkuIds = input.getMkuIds();
        if(CollectionUtils.isEmpty(mkuIds)){
            return DataResponse.success(Boolean.FALSE);
        }
        for(Long mkuId : mkuIds){
            MkuClientDetailReqVO reqVO = new MkuClientDetailReqVO();
            reqVO.setMkuId(mkuId);
            reqVO.setClientCode(input.getClientCode());
            mkuEsManageService.upsert2Es(reqVO);
        }
        return DataResponse.success(Boolean.TRUE);
    }

    @Override
    public DataResponse<Boolean> upsertToEs(MkuClientDetailReqApiDTO input) {
        log.info("MkuClientApiServiceImpl.upsertToEs, input={}", JSONObject.toJSONString(input));
        MkuClientDetailReqVO param = MkuConvert.INSTANCE.mkuClientDetailReqDTO2VO(input);
        return DataResponse.success(mkuEsManageService.upsert2Es4Mku(param));
    }

    @Override
    public DataResponse<Long> existsMku(MkuClientPageReqApiDTO input) {
        log.info("MkuClientApiServiceImpl.existsMku, input：{}", JSONObject.toJSONString(input));
        MkuClientPageReqVO pageReqVO = MkuConvert.INSTANCE.mkuClientPageReqDTO2VO(input);
        return DataResponse.success(mkuClientManageService.existsMku(pageReqVO));
    }

    @Override
    public DataResponse<List<String>> attempt(MkuClientPageReqApiDTO input) {
        log.info("MkuClientApiServiceImpl.attempt, input：{}", JSONObject.toJSONString(input));
        MkuClientPageReqVO pageReqVO = MkuConvert.INSTANCE.mkuClientPageReqDTO2VO(input);
        return DataResponse.success(mkuEsManageService.attempt(pageReqVO));
    }

    /**
     * 检查客户端是否在池中且不在黑名单中。
     * @param dtoList 客户端详细信息请求对象列表。
     * @return 包含客户端在池中且不在黑名单中的信息的 DataResponse 对象。
     */
    @Override
    public DataResponse<List<MkuClientInPoolApiDTO>> checkInPoolNoBlack(List<MkuClientDetailReqApiDTO> dtoList) {
        log.info("MkuClientApiServiceImpl.checkInPoolNoBlack, req：{}", JSONObject.toJSONString(dtoList));
        List<MkuClientDetailReqVO> mkuReqList = MkuConvert.INSTANCE.listMkuReqDTO2VO(dtoList);
        List<MkuClientInPoolVO> mkuClientInPoolList = mkuClientManageService.checkInPoolNoBlack(mkuReqList);
        List<MkuClientInPoolApiDTO> apiDTOList = MkuConvert.INSTANCE.listVO2DTO(mkuClientInPoolList);
        log.info("MkuClientApiServiceImpl.checkInPoolNoBlack, req：{},res:{}", JSONObject.toJSONString(dtoList),JSONObject.toJSONString(apiDTOList));
        return DataResponse.success(apiDTOList);
    }


    @Override
    public DataResponse<List<MkuClientApiDTO>> latestWares(MkuClientLatestWareReqApiDTO input) {
        log.info("MkuClientApiServiceImpl.latestWares, param={}", JSONObject.toJSONString(input));
        CustomerLatestMkuReqVO reqVO = MkuConvert.INSTANCE.latestWareReqDTO2VO(input);
        DataResponse<List<MkuClientVO>> res = mkuClientManageService.latestWares(reqVO);
        if (null==res){
            log.warn("latestWares<MkuClientVO> null. input={}", JSONObject.toJSONString(input));
            return DataResponse.error("record empty.");
        }
        List<MkuClientApiDTO> pageInfoDTO = MkuConvert.INSTANCE.listMkuClientVO2DTO(res.getData());
        return  DataResponse.success(pageInfoDTO);
    }

    @Override
    public DataResponse<List<MkuClientApiDTO>> queryWaresInfo(MkuListInfoReqApiDTO input) {
        log.info("MkuClientApiServiceImpl.queryWaresInfo, param={}", JSONObject.toJSONString(input));
        MkuListInfoReqReqVO reqVO = MkuConvert.INSTANCE.queryWaresInfoReqDTO2VO(input);
        DataResponse<List<MkuClientVO>> res = mkuClientManageService.queryWaresInfo(reqVO);
        if (null==res){
            log.warn("queryWaresInfo<MkuClientVO> null. input={}", JSONObject.toJSONString(input));
            return DataResponse.error("record empty.");
        }
        List<MkuClientApiDTO> pageInfoDTO = MkuConvert.INSTANCE.listMkuClientVO2DTO(res.getData());
        return  DataResponse.success(pageInfoDTO);
    }

    @Override
    public DataResponse<Boolean> updateMkuCustomerFeature(MkuFeatureTagDTO mkuFeatureTagDTO) {
        if (Objects.isNull(mkuFeatureTagDTO.getSkuId()) || StringUtils.isBlank(mkuFeatureTagDTO.getTagType()) || StringUtils.isBlank(mkuFeatureTagDTO.getOperator())
                    || StringUtils.isBlank(mkuFeatureTagDTO.getCountryCode())){
            return DataResponse.buildError("必填参数不能为空");
        }
        MkuFeatureTagVO mkuFeatureTagVo = MkuConvert.INSTANCE.mkuFeatureTagDto2Vo(mkuFeatureTagDTO);
        boolean res = mkuEsManageService.updateMkuCustomerFeatureTag(mkuFeatureTagVo);
        if (res) {
            return DataResponse.success(Boolean.TRUE);
        }
        return DataResponse.success(Boolean.FALSE);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> updateMkuPoolFlag(MkuPoolFlagDTO input) {
        return DataResponse.success(mkuEsManageService.updateMkuPoolFlag(input));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> updateAggKey(MkuClientDetailReqApiDTO input) {
        return DataResponse.success(mkuEsManageService.updateAggKey(input));
    }

    @Override
    public DataResponse<MkuEsClientDTO> queryEsMkuByCondition(MkuEsDetailReqApiDTO input) {
        MkuClientDetailReqVO request = MkuConvert.INSTANCE.mkuEsClientDetailReqDTO2VO(input);
        MkuEsClientVO mkuEsClientVO = mkuEsManageService.getEsMkuByCondition(request);
        if (null == mkuEsClientVO) {
            log.warn("queryEsMkuByCondition is null. input={}", JSONObject.toJSONString(input));
            return DataResponse.error("record empty!");
        }

        MkuEsClientDTO mkuEsClientDTO = MkuConvert.INSTANCE.mkuEsClientVO2DTO(mkuEsClientVO);
        return DataResponse.success(mkuEsClientDTO);
    }

    @Override
    public DataResponse<Boolean> updateMkuEsStockTag(MkuEsStockTagReqDTO mkuEsStockTagReqDTO) {
        boolean res = mkuEsManageService.updateMkuEsStockTag(mkuEsStockTagReqDTO);
        if (res) {
            return DataResponse.success(Boolean.TRUE);
        }
        return DataResponse.success(Boolean.FALSE);
    }

    @Override
    public DataResponse<Boolean> updateMkuPromiseFeature(MkuPromiseTagDTO mkuPromiseTagDTO) {
        MkuPromiseFeatureTagVO mkuPromiseFeatureTagVO = MkuConvert.INSTANCE.mkuPromiseTagDto2Vo(mkuPromiseTagDTO);

        boolean res = mkuEsManageService.updateMkuPromiseFeatureTag(mkuPromiseFeatureTagVO);
        if (res) {
            return DataResponse.success(Boolean.TRUE);
        }
        return DataResponse.success(Boolean.FALSE);
    }
}
