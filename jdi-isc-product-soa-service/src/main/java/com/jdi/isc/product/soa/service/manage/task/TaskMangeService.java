package com.jdi.isc.product.soa.service.manage.task;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.domain.enums.FileTypeEnum;
import com.jdi.isc.product.soa.domain.enums.TaskBizTypeEnum;
import com.jdi.isc.product.soa.domain.enums.TaskCreateTypeEnum;
import com.jdi.isc.product.soa.domain.task.po.TaskPO;
import com.jdi.isc.product.soa.domain.task.vo.TaskReqVO;
import com.jdi.isc.product.soa.domain.task.vo.TaskVO;

public interface TaskMangeService {

    TaskPO saveOrUpdate(TaskVO taskVO);

    TaskPO save(String fileName, String pin, String key, TaskBizTypeEnum bizType, TaskCreateTypeEnum taskCreateType);

    TaskPO save(String fileName, String pin, String key, TaskBizTypeEnum bizType, TaskCreateTypeEnum taskCreateType, FileTypeEnum fileType);

    Boolean update(TaskVO taskPO);

    PageInfo<TaskVO> page(TaskReqVO input);

    Page<TaskPO> hashList(TaskReqVO taskReqVO);

    Boolean error(TaskVO taskInfo);

    Boolean removeTask(Long id);

    TaskPO getTaskPo(Long taskId);
}
