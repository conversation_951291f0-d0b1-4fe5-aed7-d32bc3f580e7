package com.jdi.isc.product.soa.service.manage.taxRate.countryTax.impl.read;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.CurrencyConstant;
import com.jdi.isc.product.soa.domain.price.extendPrice.biz.CurrencyPriceVO;
import com.jdi.isc.product.soa.domain.price.po.FulfillmentRatePO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxResVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.IscSkuImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.HuSkuTaxPO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.IdSkuTaxPO;
import com.jdi.isc.product.soa.price.api.price.req.CrossBorderImportTaxReqDTO;
import com.jdi.isc.product.soa.price.api.price.res.CrossBorderImportTaxResDTO;
import com.jdi.isc.product.soa.service.atomic.taxRate.countryTax.IdSkuTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.BaseTaxReadService;
import com.jdi.isc.product.soa.service.support.TaxCalculateExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 印尼关税读服务
 * <AUTHOR>
 * @date 2025/3/10
 */
@Slf4j
@Service
@TaxCalculateExecutor(countryCode = CountryConstant.COUNTRY_ID)
public class IdCountryTaxReadServiceImpl extends BaseTaxSupportService implements BaseTaxReadService {

    @Resource
    private IdSkuTaxAtomicService idSkuTaxAtomicService;

    /** 通过skuId、国家码查询跨境sku关税*/
    @Override
    public Map<Long, CrossBorderImportTaxResDTO> queryCrossBorderSkuImportTax(CrossBorderImportTaxReqVO input) {
        Map<Long, CrossBorderImportTaxResDTO> result = Maps.newHashMapWithExpectedSize(input.getCnSkuIds().size());
        //获取跨境采购价
        Map<Long, SkuPricePO> priceMap = skuPriceAtomicService.batchQuerySkuTaxPrice(input.getCnSkuIds(), CountryConstant.COUNTRY_ZH);
        //获取印尼关税率
        Map<Long, IdSkuTaxPO> skuTaxMap = getSkuTaxMap(input.getCnSkuIds());
        for(Long skuId : input.getCnSkuIds()){
            CrossBorderImportTaxResDTO target = new CrossBorderImportTaxResDTO(skuId,input.getCountryCode(),CurrencyConstant.CURRENCY_ZH);
            StringBuilder msg = new StringBuilder();
            if(priceMap.get(skuId)==null){
                msg.append("skuId:").append(skuId).append(" 采购价为空,无法计算关税;");
                target.setDesc(msg.toString());
                result.put(skuId,target);
                continue;
            }
            if(skuTaxMap.get(skuId)==null){
                msg.append("skuId:").append(skuId).append(" 关税配置为空,无法计算关税;");
                target.setDesc(msg.toString());
                result.put(skuId,target);
                continue;
            }
            //印尼关税明细
            IdSkuTaxPO taxInfo = skuTaxMap.get(skuId);
            //履约费用
            CurrencyPriceVO fulfillmentPrice;
            if(!input.getFulfillmentFee().isEmpty() && input.getFulfillmentFee().get(skuId)!=null){
                fulfillmentPrice = input.getFulfillmentFee().get(skuId);
            }else {
                fulfillmentPrice = getFulfillmentPrice(input.getCountryCode(), skuId);
            }
            if(fulfillmentPrice==null || fulfillmentPrice.getPrice()==null){
                target.setDesc(fulfillmentPrice!=null&& StringUtils.isNotBlank(fulfillmentPrice.getMsg())?fulfillmentPrice.getMsg():"履约费用为空,无法计算关税;");
                result.put(skuId,target);
                continue;
            }

            //CIF价格=采购价（含税）+ 履约费用
            BigDecimal cif = priceMap.get(skuId).getPrice().add(fulfillmentPrice.getPrice());
            //进口关税金BM = CIF价格 x min（普通关税率，原产地优惠关税率）；
            BigDecimal importDutiesBm = cif.multiply(taxInfo.getNormalImportTax().min(taxInfo.getOriginMfnTax()));
            //奢侈品税金PPnBM=(CIF + 进口关税金BM)  x  奢侈品税率PPnBM% ；
            BigDecimal luxury = cif.add(importDutiesBm).multiply(taxInfo.getLuxuryTax());
            //增值税金PPN = （CIF价格 + 进口关税金BM+奢侈品税金PPnBM）x 增值税率PPN %
            BigDecimal valueAdd = cif.add(importDutiesBm).add(luxury).multiply(taxInfo.getValueAddedTax());
            //预扣税金PPH = (CIF价格 + 进口关税金BM) x 预扣税率PPH%
            BigDecimal withholding = cif.add(importDutiesBm).multiply(taxInfo.getWithholdingTax());
            //贸易保护关税金BMT = CIF价格 * 贸易保护关税BMT率%
            BigDecimal tradeProtection = cif.multiply(taxInfo.getTradeProtectionTax());
            //进口税费=进口关税金BM+奢侈品税金PPnBM+增值税金PPN+ 预扣税金PPH + 贸易保护关税金BMT (中间值不舍位，最终结果四舍五入, 保留2为小数)
            BigDecimal importTax = importDutiesBm.add(luxury).add(valueAdd).add(withholding).add(tradeProtection).setScale(2,RoundingMode.HALF_UP);

            //优化小数位展示
            String cifStr = cif.stripTrailingZeros().toPlainString();
            String importDutiesBmStr = importDutiesBm.stripTrailingZeros().toPlainString();
            String luxuryStr = luxury.stripTrailingZeros().toPlainString();
            String valueAddStr = valueAdd.stripTrailingZeros().toPlainString();
            String withholdingStr = withholding.stripTrailingZeros().toPlainString();
            String tradeProtectionStr = tradeProtection.stripTrailingZeros().toPlainString();
            String importTaxStr = importTax.stripTrailingZeros().toPlainString();

            //计算过程消息
            String totalMsg = String.format("进口税费%s=进口关税金BM%s + 奢侈品税金PPnBM%s + 增值税金PPN%s + 预扣税金PPH%s + 贸易保护关税金BMT%s",
                    importTaxStr,importDutiesBmStr,luxuryStr,valueAddStr,withholdingStr,tradeProtectionStr);
            String cifMsg = String.format("CIF价格%s = 采购价（含税）%s+ 履约费用%s",cifStr,priceMap.get(skuId).getPrice(),fulfillmentPrice.getPrice());
            String importDutiesBmMsg = String.format("进口关税金BM%s = CIF%s x min（普通关税率%s，原产地优惠关税率%s）",importDutiesBmStr,cifStr,taxInfo.getNormalImportTax().stripTrailingZeros().toPlainString(),taxInfo.getOriginMfnTax().stripTrailingZeros().toPlainString());
            String luxuryMsg = String.format("奢侈品税金PPnBM%s =(CIF%s + 进口关税金BM%s) x 奢侈品税率PPnBM%s ",luxuryStr,cifStr,importDutiesBmStr,taxInfo.getLuxuryTax().stripTrailingZeros().toPlainString());
            String valueAddMsg = String.format("增值税金PPN%s = （CIF%s + 进口关税金BM%s + 奢侈品税金PPnBM%s）x 增值税率PPN%s",valueAddStr,cifStr,importDutiesBmStr,luxuryStr,taxInfo.getValueAddedTax().stripTrailingZeros().toPlainString());
            String withholdingMsg = String.format("预扣税金PPH%s = (CIF%s + 进口关税金BM%s) x 预扣税率PPH%s",withholdingStr,cifStr,importDutiesBmStr,taxInfo.getWithholdingTax().stripTrailingZeros().toPlainString());
            String tradeProtectionMsg = String.format("贸易保护关税金BMT%s = CIF%s * 贸易保护关税BMT率%s",tradeProtectionStr,cifStr,taxInfo.getTradeProtectionTax().stripTrailingZeros().toPlainString());
            target.setDesc(msg.append(totalMsg).append("\n").
                    append(cifMsg).append("\n").
                    append(importDutiesBmMsg).append("\n").
                    append(luxuryMsg).append("\n").
                    append(valueAddMsg).append("\n").
                    append(withholdingMsg).append("\n").
                    append(tradeProtectionMsg).append("\n").toString());
            target.setImportTax(importTax);
            target.setDeductionAmount(valueAdd.setScale(2, RoundingMode.HALF_UP));
            log.info("IdCountryTaxReadServiceImpl.queryCrossBorderSkuImportTax skuId:{} , res:{}" , skuId , JSON.toJSONString(target));
            result.put(skuId,target);
        }
        return result;
    }

    private Map<Long, IdSkuTaxPO> getSkuTaxMap(Set<Long> cnSkuIds) {
        Map<Long, IdSkuTaxPO> result = Maps.newHashMapWithExpectedSize(cnSkuIds.size());
        //国际sku批量转jdSku
        Map<Long, Long> res = skuAtomicService.querySkuMapByIscSkuIds(cnSkuIds);
        if(res.isEmpty()){
            log.error("BrCountryTaxReadServiceImpl.getSkuTaxMap invoke error, 给定的国际sku未能查询到正确的sku与jdSku映射关系 {}", cnSkuIds);
            return new HashMap<>();
        }
        //根据jdSku查关税率
        Map<Long, IdSkuTaxPO> skuTaxMap = idSkuTaxAtomicService.listSkuTax(new HashSet<>(res.values()));
        if(skuTaxMap.isEmpty()){
            log.error("BrCountryTaxReadServiceImpl.getSkuTaxMap invoke error, 给定的jdSku未能查询到关税配置 res {}", JSON.toJSONString(res));
            return new HashMap<>();
        }
        //构建国际sku&关税实体关系
        for (Map.Entry<Long, Long> entry : res.entrySet()) {
            result.put(entry.getKey(), skuTaxMap.get(entry.getValue()));
        }
        return result;
    }

    @Override
    public Map<Long, CrossBorderImportTaxResVO> queryImportTaxByIscSkuIds(IscSkuImportTaxReqVO input) {
        Map<Long, IdSkuTaxPO> skuTaxMap = this.getSkuTaxMap(input.getSkuIds());
        // 构建返回结果
        Map<Long, CrossBorderImportTaxResVO> resultMap = Maps.newHashMapWithExpectedSize(skuTaxMap.size());
        skuTaxMap.forEach((skuId,taxPo)-> resultMap.put(skuId, new CrossBorderImportTaxResVO(skuId,taxPo.getJdSkuId(), taxPo.getHsCode())));
        return resultMap;
    }
}
