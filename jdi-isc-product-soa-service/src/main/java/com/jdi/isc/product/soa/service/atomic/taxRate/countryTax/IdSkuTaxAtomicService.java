package com.jdi.isc.product.soa.service.atomic.taxRate.countryTax;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.IdSkuTaxPO;
import com.jdi.isc.product.soa.price.api.price.req.IdSkuTaxVO;
import com.jdi.isc.product.soa.repository.mapper.taxRate.countryTax.IdSkuTaxBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 印尼SKU税率原子服务
 * <AUTHOR>
 * @date 20250311
 */
@Service
@Slf4j
public class IdSkuTaxAtomicService extends ServiceImpl<IdSkuTaxBaseMapper, IdSkuTaxPO> {

    /**
     * 单个查询
     */
    public IdSkuTaxPO getOne(IdSkuTaxVO vo) {
        LambdaQueryWrapper<IdSkuTaxPO> query = Wrappers.<IdSkuTaxPO>lambdaQuery()
                .eq(IdSkuTaxPO::getJdSkuId, vo.getJdSkuId())
                .eq(IdSkuTaxPO::getYn, YnEnum.YES.getCode());
        return super.getOne(query);
    }

    public Map<Long,IdSkuTaxPO> listSkuTax(Set<Long> skuIds){
        LambdaQueryWrapper<IdSkuTaxPO> queryWrapper = Wrappers.<IdSkuTaxPO>lambdaQuery()
                .in(IdSkuTaxPO::getJdSkuId, skuIds)
                .eq(IdSkuTaxPO::getYn, YnEnum.YES.getCode());
        List<IdSkuTaxPO> res = super.list(queryWrapper);
        if(CollectionUtils.isNotEmpty(res)){
            return res.stream().collect(Collectors.toMap(IdSkuTaxPO::getJdSkuId, a -> a));
        }
        return new HashMap<>();
    }

    public boolean updateTax(IdSkuTaxVO vo) {
        LambdaUpdateWrapper<IdSkuTaxPO> wrapper = Wrappers.<IdSkuTaxPO>lambdaUpdate()
            .set(StringUtils.isNotBlank(vo.getHsCode()), IdSkuTaxPO::getHsCode, vo.getHsCode())
            .set(vo.getNormalImportTax() != null, IdSkuTaxPO::getNormalImportTax, vo.getNormalImportTax())
            .set(vo.getOriginMfnTax() != null, IdSkuTaxPO::getOriginMfnTax, vo.getOriginMfnTax())
            .set(vo.getLuxuryTax() != null, IdSkuTaxPO::getLuxuryTax, vo.getLuxuryTax())
            .set(vo.getValueAddedTax() != null, IdSkuTaxPO::getValueAddedTax, vo.getValueAddedTax())
            .set(vo.getWithholdingTax() != null, IdSkuTaxPO::getWithholdingTax, vo.getWithholdingTax())
            .set(vo.getTradeProtectionTax() != null, IdSkuTaxPO::getTradeProtectionTax, vo.getTradeProtectionTax())
            .set(vo.getIsOriginCertificate() != null, IdSkuTaxPO::getIsOriginCertificate, vo.getIsOriginCertificate())
            .set(StringUtils.isNotBlank(vo.getRemark()), IdSkuTaxPO::getRemark, vo.getRemark())
            .set(IdSkuTaxPO::getUpdater, vo.getUpdater())
            .set(IdSkuTaxPO::getUpdateTime, new Date())
            .eq(IdSkuTaxPO::getId, vo.getId());
        return super.update(wrapper);
    }
    
}
