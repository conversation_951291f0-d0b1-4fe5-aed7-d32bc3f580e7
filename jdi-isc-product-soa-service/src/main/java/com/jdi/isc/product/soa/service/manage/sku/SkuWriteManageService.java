package com.jdi.isc.product.soa.service.manage.sku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.BaseResDTO;
import com.jdi.isc.product.soa.api.sku.req.BatchUpdateCustomsApiReqDTO;
import com.jdi.isc.product.soa.api.sku.req.SkuSyncNcmDTO;
import com.jdi.isc.product.soa.api.sku.req.SkuUpdateApiDTO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuGlobalAttributeVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/12/5
 **/
public interface SkuWriteManageService {

    /**
     * 批量创建sku
     *
     * @param skuVOList skuVo对象
     * @return 返回skuId列表
     */
    List<Long> addSkus(List<SkuVO> skuVOList);


    /**
     * 更新sku
     *
     * @param skuVOList skuVo列表
     * @return 返回skuId列表
     */
    List<Long> updateSkus(List<SkuVO> skuVOList, List<SkuVO> newSkuList, Long spuId);

    /**
     * 更新sku业务
     *
     * @param spuId spuId
     * @param now   上架时间
     * @return 更新结果
     */
    List<Long> saveOrUpdateSkuBiz(Long spuId, Date now);


    /**
     * spu修改为“待审核”时，逻辑删除sku业务记录
     *
     * @param spuId
     * @return
     */
    List<Long> removeSkuBiz(Long spuId);

    /**
     * 更新sku信息、并联动更新mku信息
     *
     * @param skuVO sku业务对象
     * @return 返回更新结果
     */
    DataResponse<Boolean> updateSkuInfoRelationMku(SkuVO skuVO);


    /**
     * 更新SKU信息
     * @param skuVOList SKU值对象列表
     * @param resultMap SKU更新结果映射
     * @return 更新后的SKU信息映射
     */
    Map<Long, SkuUpdateApiDTO> updateSkuInfo(List<SkuVO> skuVOList, Map<Long, SkuUpdateApiDTO> resultMap);


    /**
     * 更新全局属性
     * @param param 全局属性更新参数列表
     * @return 更新结果
     */
    String updateGlobalAttribute(SkuGlobalAttributeVO param);

    /**
     * 每次只能更新一个sku, 这里传入多个是因为有可能有脏数据, 原则上还是一个sku.
     *
     * @param input the input
     */
    void saveProductGlobalNcmAttribute(SkuSyncNcmDTO input);

    /**
     * 更新草稿中的NCM码跨境属性.
     *
     * @param ncmCode    the ncm code
     * @param skuDraftPO the sku draft po
     * @param updater    the updater
     */
    void syncUpdateSkuDraftNcmCode(String ncmCode, SkuDraftPO skuDraftPO, String updater);

    /**
     * 更新sku草稿，yn=0.
     *
     * @param spuId the spu id
     */
    void updateDraftYn(Long spuId, String updater);


    /**
     * 批量更新报关信息
     * @param reqDTO 批量更新报关信息请求参数
     * @return 更新结果，包含每个报关信息的更新状态
     */
    DataResponse<Map<String, BaseResDTO>> batchUpdateCustoms(BatchUpdateCustomsApiReqDTO reqDTO);

    /**
     * 初始化ncm码
     */
    PropertyVO initPropertyVO(String ncmCode);

    List<PropertyValueVO> initPropertyValueVOS(String ncmCode);

    Boolean removeNeedUnbindSkuRelation(Set<Long> skuIds, String warehouseId);
}
