package com.jdi.isc.product.soa.service.manage.stock.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockSplitReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockItemOccupyResDTO;
import com.jdi.isc.product.soa.api.stock.res.StockOccupyResDTO;
import com.jdi.isc.product.soa.api.stock.res.StockResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.enums.StockOperateResEnum;
import com.jdi.isc.product.soa.common.enums.StockOperateTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.util.StockNumUtils;
import com.jdi.isc.product.soa.domain.enums.warehouse.OnWaySaleTypeEnum;
import com.jdi.isc.product.soa.domain.enums.warehouse.WarehouseTypeEnum;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.stock.po.StockLogPO;
import com.jdi.isc.product.soa.domain.stock.po.StockPO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseSkuResVO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehouseSkuPO;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.stock.StockAtomicService;
import com.jdi.isc.product.soa.service.atomic.stocklog.StockLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseSkuAtomicService;
import com.jdi.isc.product.soa.service.manage.stock.StockManageService;
import com.jdi.isc.product.soa.service.manage.stock.StockWriteStrategyFactory;
import com.jdi.isc.product.soa.service.manage.stock.StockWriteStrategyService;
import com.jdi.isc.product.soa.service.manage.stock.TransitStockWriteService;
import com.jdi.isc.product.soa.service.mapstruct.stock.StockConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.service.manage.stock.StockWriteStrategyFactory.FACTORY;
import static com.jdi.isc.product.soa.service.manage.stock.StockWriteStrategyFactory.WAREHOUSE;

/**
 * 库存业务读写服务
 * <AUTHOR>
 * @date 2024/6/6
 */
@Service
@Slf4j
public class StockManageServiceImpl implements StockManageService {
    @Resource
    private StockAtomicService stockAtomicService;
    @Resource
    private StockLogAtomicService stockLogAtomicService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private WarehouseSkuAtomicService warehouseSkuAtomicService;
    @Resource
    private WarehouseAtomicService warehouseAtomicService;
    @Value("${spring.profiles.active}")
    private String systemProfile;

    @Resource
    private StockWriteStrategyFactory stockWriteStrategyFactory;

    public final static int OUT_OF_STOCK = 34;
    public final static int HAVE_STOCK = 33;
    public final static int ON_WAY_STOCK = 39;

    private final static String OUT_OF_STOCK_STR = "无货";
    private final static String HAVE_STOCK_STR = "有货";
    private final static String ON_WAY_STOCK_STR = "在途";

    /** 库存预占(提单前)*/
    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 30)
    public DataResponse<Boolean> occupy(StockManageReqDTO req) {
        DataResponse<Boolean> validatedResponse = this.validateWarehouseExist(req);
        if (!validatedResponse.getSuccess()){
            return validatedResponse;
        }
        Set<Long> skuSet = req.getStockItem().stream().map(StockItemManageReqDTO::getSkuId).collect(Collectors.toSet());
        Map<Long, List<StockPO>> stockMap = stockAtomicService.getStockMap(skuSet);
        //未查询到库存直接返回
        if(MapUtils.isEmpty(stockMap)){
            return buildErr(StockOperateResEnum.EXCEPTION_NULL_STOCK,false,skuSet);
        }
        DataResponse<Boolean> validateStockResponse = processStockValidate(req, stockMap);
        if (validateStockResponse != null && !validateStockResponse.getSuccess()) {
            return validateStockResponse;
        }
        //执行预占行为
        int effectiveStock = stockAtomicService.occupy(req.getStockItem());
        //预占成功数量与请求数量不一致
        if(effectiveStock != req.getStockItem().size()){
            return buildErr(StockOperateResEnum.EXCEPTION_OCCUPY,true,skuSet);
        }
        return stockLogAtomicService.save(req, StockOperateTypeEnum.OCCUPY);
    }

    private DataResponse<Boolean> processStockValidate(StockManageReqDTO req, Map<Long, List<StockPO>> stockMap) {
        Set<Long> insufficientSku = new HashSet<>();
        //可用库存判断
        for(StockItemManageReqDTO stockReq : req.getStockItem()){
            List<StockPO> stockDbList = stockMap.get(stockReq.getSkuId());
            // 库存为空
            if(CollectionUtils.isEmpty(stockDbList)){
                return buildErr(StockOperateResEnum.EXCEPTION_NULL_STOCK, false, stockReq.getSkuId());
            }
            // 仓库库存
            if (StringUtils.isNotBlank(stockReq.getWarehouseId())) {
                Optional<StockPO> first = stockDbList.stream().filter(stockPO -> StringUtils.isNotBlank(stockPO.getWarehouseId()) && stockPO.getWarehouseId().equals(stockReq.getWarehouseId())).findFirst();
                // 当前仓库库存为空或者可用库存为空
                if (!first.isPresent() || first.get().getAvailableStock() == null) {
                    return buildErr(StockOperateResEnum.EXCEPTION_NULL_STOCK, false, stockReq.getSkuId());
                }else if (first.get().getAvailableStock() <stockReq.getNum()){
                    insufficientSku.add(stockReq.getSkuId());
                }
            }else {
                // 厂直库存
                Optional<StockPO> first = stockDbList.stream().filter(stockPO -> StringUtils.isBlank(stockPO.getWarehouseId())).findFirst();
                //库存为空
                if (!first.isPresent() || first.get().getAvailableStock() == null) {
                    return buildErr(StockOperateResEnum.EXCEPTION_NULL_STOCK, false, stockReq.getSkuId());
                }else {
                    StockPO stockDb = first.get();
                    //可用库存不足
                    Long availableStock = StockNumUtils.calculateAvailableStock(stockDb, Boolean.FALSE);
                    if (availableStock < stockReq.getNum()) {
                        insufficientSku.add(stockReq.getSkuId());
                    }
                }
            }
        }
        if(CollectionUtils.isNotEmpty(insufficientSku)){
            return buildErr(StockOperateResEnum.WARNING_INSUFFICIENT_STOCK, false, insufficientSku);
        }
        return DataResponse.success();
    }

    /** 预占扣减(提单支付后的正向流程)*/
    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 30)
    public DataResponse<Boolean> release(StockManageReqDTO req) {
        DataResponse<Boolean> validatedResponse = this.validateWarehouseExist(req);
        if (!validatedResponse.getSuccess()){
            return validatedResponse;
        }
        int effectiveStock = stockAtomicService.release(req);
        //预占释放数量与请求数量不一致
        if(effectiveStock != req.getStockItem().size()){
            return buildErr(StockOperateResEnum.EXCEPTION_OCCUPY_RELEASE,true,req.getStockItem());
        }
        return stockLogAtomicService.save(req, StockOperateTypeEnum.RELEASE);
    }

    /** 预占回退(提单失败)*/
    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 30)
    public DataResponse<Boolean> occupyReturn(StockManageReqDTO req) {
        DataResponse<Boolean> validatedResponse = this.validateWarehouseExist(req);
        if (!validatedResponse.getSuccess()){
            return validatedResponse;
        }
        int effectiveStock = stockAtomicService.occupyReturn(req);
        //预占退回量与请求数量不一致
        if(effectiveStock != req.getStockItem().size()){
            return buildErr(StockOperateResEnum.EXCEPTION_OCCUPY_RETURN,true,req.getStockItem());
        }
        return stockLogAtomicService.save(req, StockOperateTypeEnum.OCCUPY_RETURN);
    }

    /** 现货回退(订单取消或售后)*/
    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 30)
    public DataResponse<Boolean> stockReturn(StockManageReqDTO req) {
        DataResponse<Boolean> validatedResponse = this.validateWarehouseExist(req);
        if (!validatedResponse.getSuccess()){
            return validatedResponse;
        }
        int effectiveStock = stockAtomicService.stockReturn(req);
        //现货回退生效数量与请求数量不一致
        if(effectiveStock != req.getStockItem().size()){
            return buildErr(StockOperateResEnum.EXCEPTION_STOCK_RETURN,true,req.getStockItem());
        }
        return stockLogAtomicService.save(req, StockOperateTypeEnum.STOCK_RETURN);
    }

    /** 库存更新(运营或供应商维护库存)*/
    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 30)
    public DataResponse<Boolean> saveOrUpdate(StockManageReqDTO req) {
        // 获取策略
        List<StockWriteStrategyService> strategyServiceList = this.getStrategyList(req);
        List<StockItemManageReqDTO> originList = Lists.newArrayList(req.getStockItem());
        for (StockWriteStrategyService service : strategyServiceList) {
            List<StockItemManageReqDTO> itemList = this.filterStockItems(originList, service);
            req.setStockItem(itemList);
            DataResponse<Boolean> response = service.saveOrUpdate(req);
            if (!response.getSuccess()) {
                log.error("StockManageServiceImpl.saveOrUpdate库存更新失败,service={} 入参:[{}],出参:[{}]",service.getClass().getName(), JSON.toJSONString(req),JSON.toJSONString(response));
                return response;
            }
        }
        return DataResponse.success(Boolean.TRUE);
    }

    /** 根据工业国际skuId查询库存信息*/
    @Override
    public Map<Long, StockResDTO> getStock(StockManageReqDTO req) {
        Map<Long, Long> skuReqNum = new HashMap<>();
        for(StockItemManageReqDTO reqItem : req.getStockItem()){
            skuReqNum.put(reqItem.getSkuId(), reqItem.getNum());
        }

        // 查询商品关联备货仓
        Map<Long, List<WarehouseSkuResVO>> warehouseSkuResVOFromDb = warehouseSkuAtomicService.querySkuWarehouseList(req);
        List<StockItemManageReqDTO> factoryDirectStockItem = Lists.newArrayList();
        List<StockItemManageReqDTO> purchaseModelStockItem = Lists.newArrayList();
        // 区分厂直和备货模式商品
        req.getStockItem().forEach(item -> {
            if (warehouseSkuResVOFromDb.containsKey(item.getSkuId())) {
                List<WarehouseSkuResVO> warehouseSkuResVOS = warehouseSkuResVOFromDb.get(item.getSkuId());
                WarehouseSkuResVO warehouseSkuResVO = warehouseSkuResVOS.get(0);
                // 备货仓ID
                item.setWarehouseId(String.valueOf(warehouseSkuResVO.getWarehouseId()));
                purchaseModelStockItem.add(item);
            }else {
                factoryDirectStockItem.add(item);
            }
        });
        // 当前国家无备货仓，查询厂直库存
        Map<Long,StockResDTO> stockResDTOMap = Maps.newHashMap();
        stockResDTOMap.putAll(this.getStockMapFromPurchaseModelOrFactoryDirect(factoryDirectStockItem, skuReqNum, Boolean.TRUE));
        // 当前有备货仓关系，查询备货库存
        stockResDTOMap.putAll(this.getStockMapFromPurchaseModelOrFactoryDirect(purchaseModelStockItem, skuReqNum, Boolean.FALSE));
        return stockResDTOMap;
    }

    /**
     * 批量获取仓库库存信息
     *
     * @param stockItemManageReqDTOList 库存管理请求DTO列表，包含需要查询的仓库ID和商品信息
     * @return 返回对应仓库的商品库存信息列表
     */
    @Override
    public List<StockPO> batchGetWarehouseStock(List<StockItemManageReqDTO> stockItemManageReqDTOList) {
        if (CollectionUtils.isEmpty(stockItemManageReqDTOList)) {
            return Collections.emptyList();
        }

        Map<String, List<StockItemManageReqDTO>> warehouseSkuItemMap = stockItemManageReqDTOList.stream()
                .collect(Collectors.groupingBy(StockItemManageReqDTO::getWarehouseId));

        List<StockPO> stockPOList = Lists.newArrayList();
        for (Map.Entry<String, List<StockItemManageReqDTO>> entry : warehouseSkuItemMap.entrySet()) {
            String warehouseId = entry.getKey();
            List<StockItemManageReqDTO> skuItems = entry.getValue();
            List<StockPO> stockDbList = stockAtomicService.listStockByWarehouseId(skuItems);
            stockPOList.addAll(stockDbList);
        }

        return stockPOList;
    }

    /**
     * 根据skuId集合批量获取厂直库存信息
     * @param skuIds 商品skuId集合，不可为空
     * @return 包含库存信息的列表，若未找到则返回空列表
     */
    @Override
    public List<StockPO> batchGetFactoryStock(Set<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }

        List<StockPO> stockDbList = stockAtomicService.listStockBySkuIds(skuIds);

        return stockDbList;
    }


    /**
     * 根据订单ID和业务单号获取在途转现货预占的库存记录
     * @param orderId 订单ID，为空时返回空列表
     * @param bizNo 业务单号，为空时返回空列表
     * @param warehouseId 仓库ID，用于筛选特定仓库的库存记录
     * @return 返回在途订单转现货预占的库存记录列表，无结果时返回空列表
     */
    @Override
    public List<StockLogPO> getOccupyStockForBill(String orderId, String bizNo, String warehouseId) {
        if (StringUtils.isBlank(orderId) || StringUtils.isBlank(bizNo)) {
            return Collections.emptyList();
        }

        //获取在途订单在途预占转现货预占的sku数据
        List<StockLogPO> transferExists = stockLogAtomicService.listForWarehouse(
                null,
                bizNo,
                StockOperateTypeEnum.TRANSIT_STOCK_TRANSFER_STOCK_OCCUPY,
                null,
                warehouseId);

        return transferExists;
    }


    /**
     * 根据SKU ID和仓库ID获取有效的仓库库存信息
     * @param skuId 商品SKU ID
     * @param warehouseId 仓库ID
     * @return 库存信息对象
     */
    @Override
    public StockPO getWarehouseStock(Long skuId, Long warehouseId){
        return stockAtomicService.getValidStock(skuId, warehouseId);
    }

    @Override
    public Boolean batchRemoveStockByIds(Set<Long> ids){
        return stockAtomicService.removeBatchByIds(ids);
    }

    /**
     * 查询库存信息入口 兼容 厂直发 和  采购单
     * @param stockItems
     * @param skuReqNum
     * @param isFactoryDirect
     * @return
     */
    @NotNull
    private Map<Long, StockResDTO> getStockMapFromPurchaseModelOrFactoryDirect(List<StockItemManageReqDTO> stockItems, Map<Long, Long> skuReqNum, boolean isFactoryDirect) {
        Map<Long, StockResDTO> stockResDTOMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(stockItems)) {
            StockManageReqDTO stockRequest = new StockManageReqDTO();
            stockRequest.setStockItem(stockItems);
            List<StockPO> stockDbList = isFactoryDirect ? stockAtomicService.listStock(stockRequest)
                    : stockAtomicService.listStockByWarehouseId(stockRequest);
            List<StockResDTO> res = processStockList(skuReqNum, stockDbList, isFactoryDirect);
            if (CollectionUtils.isNotEmpty(res)) {
                stockResDTOMap.putAll(res.stream().collect(Collectors.toMap(StockResDTO::getSkuId, Function.identity())));
            }
        }
        return stockResDTOMap;
    }

    /**
     * 获取仓库信息采购单（需要查询在途可售的状态信息）
     * @param skuReqNum
     * @param stockDbList
     * @param isFactoryDirect
     * @return
     */
    private List<StockResDTO> processStockList(Map<Long, Long> skuReqNum, List<StockPO> stockDbList, boolean isFactoryDirect) {
        List<StockResDTO> res = StockConvert.INSTANCE.listPo2dto(stockDbList);
        for (StockResDTO target : res) {
            boolean onWaySale = false;
            if (!isFactoryDirect) {
                WarehouseSkuPO warehouseSkuPO = warehouseSkuAtomicService.queryWarehouseSku(target.getSkuId(), Long.valueOf(target.getWarehouseId()));
                onWaySale = OnWaySaleTypeEnum.SUPPORTED.getCode() == warehouseSkuPO.getOnWaySale();
            }
            processStockRes(target, onWaySale, skuReqNum);
        }
        return res;
    }

    /**
     * 获取库存信息出口 兼容 厂直发 和  采购单
     * @param target
     * @param onWaySale
     * @param skuReqNum
     */
    @NotNull
    private void processStockRes(StockResDTO target, boolean onWaySale, Map<Long, Long> skuReqNum) {
        // 含在途可用库存
        Long onWayAvailableStock = StockNumUtils.calculateAvailableStock(target.getStock(), target.getOnWayStock(), target.getOccupy(), Boolean.TRUE);
        // 纯现货可用库存
        Long availableStock = StockNumUtils.calculateAvailableStock(target.getStock(), target.getOnWayStock(), target.getOccupy(), Boolean.FALSE);
        target.setAvailableStock(onWaySale ? onWayAvailableStock : availableStock);
        target.setRemainNum(onWaySale ? onWayAvailableStock : availableStock);
        target.setOnWaySale(onWaySale ? OnWaySaleTypeEnum.SUPPORTED.getCode() : OnWaySaleTypeEnum.NOT_SUPPORTED.getCode());
        Long requestedQty = skuReqNum.get(target.getSkuId());
        if (requestedQty != null && requestedQty > 0) {
            if (availableStock >= requestedQty) {
                target.setStockStateType(HAVE_STOCK);
                target.setStockStateDesc(HAVE_STOCK_STR);
            }
            /** 在途可售，购买数量 > 纯现货可售库存 并且 购买数量 <= 含在途可售库存 返回在途库存**/
            else if (onWaySale && availableStock < requestedQty && requestedQty <= onWayAvailableStock) {
                target.setStockStateType(ON_WAY_STOCK);
                target.setStockStateDesc(ON_WAY_STOCK_STR);
            }  else {
                target.setStockStateType(OUT_OF_STOCK);
                target.setStockStateDesc(OUT_OF_STOCK_STR);
            }
        } else {
            if (target.getAvailableStock() > 0) {
                target.setStockStateType(HAVE_STOCK);
                target.setStockStateDesc(HAVE_STOCK_STR);
            } else {
                target.setStockStateType(OUT_OF_STOCK);
                target.setStockStateDesc(OUT_OF_STOCK_STR);
            }
        }
    }





    private DataResponse<Boolean> buildErr(StockOperateResEnum stockEnum, Boolean isWarning, Object... target){
        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        String errMsg = String.format("【"+systemProfile+"】"+ stockEnum.getDesc(),target);
        log.error("StockManageServiceImpl.buildErr invoke error {}", errMsg);
        if(isWarning){
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
        }
        return DataResponse.error(stockEnum.getCode(),errMsg);
    }

    /** 库存流水清空*/
    @Override
    public DataResponse<Integer> truncate() {
        int effectiveStock = stockAtomicService.truncate();
        return DataResponse.success(effectiveStock);
    }

    @Override
    public DataResponse<Boolean> resetStock(Set<Long> skuId) {
        stockAtomicService.resetStock(skuId);
        return DataResponse.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 30)
    public DataResponse<StockOccupyResDTO> occupyStock(StockManageReqDTO req) {
        StockOccupyResDTO occupyResDTO = new StockOccupyResDTO();
        occupyResDTO.setOrderId(req.getOrderId());
        List<StockItemOccupyResDTO> stockItemOccupy = Lists.newArrayList();
        // 获取策略
        List<StockWriteStrategyService> strategyServiceList = this.getStrategyList(req);
        List<StockItemManageReqDTO> originList = Lists.newArrayList(req.getStockItem());
        for (StockWriteStrategyService service : strategyServiceList) {
            List<StockItemManageReqDTO> itemList = this.filterStockItems(originList, service);
            req.setStockItem(itemList);
            DataResponse<StockOccupyResDTO> response = service.occupyStock(req);
            if (!response.getSuccess()) {
                log.warn("StockManageServiceImpl.occupyStock 预占库存失败，service={},入参:[{}],出参:[{}]",service.getClass().getSimpleName(),JSON.toJSONString(req),JSON.toJSONString(response.getData()));
                return response;
            }
            stockItemOccupy.addAll(response.getData().getStockItemOccupy());
        }
        occupyResDTO.setStockItemOccupy(stockItemOccupy);
        return DataResponse.success(occupyResDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 30)
    public DataResponse<Boolean> releaseStock(StockManageReqDTO req) {
        // 获取策略
        List<StockWriteStrategyService> strategyServiceList = this.getStrategyList(req);
        List<StockItemManageReqDTO> originList = Lists.newArrayList(req.getStockItem());
        for (StockWriteStrategyService service : strategyServiceList) {
            List<StockItemManageReqDTO> itemList = this.filterStockItems(originList, service);
            req.setStockItem(itemList);
            DataResponse<Boolean> response = service.releaseStock(req);
            if (!response.getSuccess()) {
                log.warn("StockManageServiceImpl.releaseStock 释放（扣减）库存失败，service={},入参:[{}],出参:[{}]",service.getClass().getSimpleName(),JSON.toJSONString(req),JSON.toJSONString(response.getData()));
                return response;
            }
        }
        return DataResponse.success(Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 30)
    public DataResponse<Boolean> occupyStockReturn(StockManageReqDTO req) {
        // 获取策略
        List<StockWriteStrategyService> strategyServiceList = this.getStrategyList(req);
        List<StockItemManageReqDTO> originList = Lists.newArrayList(req.getStockItem());
        for (StockWriteStrategyService service : strategyServiceList) {
            List<StockItemManageReqDTO> itemList = this.filterStockItems(originList, service);
            req.setStockItem(itemList);
            DataResponse<Boolean> response = service.occupyStockReturn(req);
            if (!response.getSuccess()) {
                log.warn("StockManageServiceImpl.occupyStockReturn 预占库存回退失败，service={},入参:[{}],出参:[{}]",service.getClass().getSimpleName(),JSON.toJSONString(req),JSON.toJSONString(response.getData()));
                return response;
            }
        }
        return DataResponse.success(Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 30)
    public DataResponse<Boolean> stockNewReturn(StockManageReqDTO req) {
        // 获取策略
        List<StockWriteStrategyService> strategyServiceList = this.getStrategyList(req);
        List<StockItemManageReqDTO> originList = Lists.newArrayList(req.getStockItem());
        for (StockWriteStrategyService service : strategyServiceList) {
            List<StockItemManageReqDTO> itemList = this.filterStockItems(originList, service);
            req.setStockItem(itemList);
            DataResponse<Boolean> response = service.stockReturn(req);
            if (!response.getSuccess()) {
                log.warn("StockManageServiceImpl.stockNewReturn 现货库存回退失败，service={},入参:[{}],出参:[{}]",service.getClass().getSimpleName(),JSON.toJSONString(req),JSON.toJSONString(response.getData()));
                return response;
            }
        }
        return DataResponse.success(Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 30)
    public DataResponse<StockOccupyResDTO> occupyRelease(StockManageReqDTO req) {
        StockOccupyResDTO occupyResDTO = new StockOccupyResDTO();
        occupyResDTO.setOrderId(req.getOrderId());
        List<StockItemOccupyResDTO> stockItemOccupy = Lists.newArrayList();
        // 获取策略
        List<StockWriteStrategyService> strategyServiceList = this.getStrategyList(req);
        List<StockItemManageReqDTO> originList = Lists.newArrayList(req.getStockItem());
        for (StockWriteStrategyService service : strategyServiceList) {
            List<StockItemManageReqDTO> filteredItems = filterStockItems(originList, service);
            req.setStockItem(filteredItems);

            DataResponse<StockOccupyResDTO> response = service.occupyRelease(req);
            if (!response.getSuccess()) {
                return response;
            }
            stockItemOccupy.addAll(response.getData().getStockItemOccupy());
        }

        occupyResDTO.setStockItemOccupy(stockItemOccupy);
        return DataResponse.success(occupyResDTO);
    }

    /**
     * 根据 StockWriteStrategyService 策略过滤 StockItemManageReqDTO 中的 StockItem。
     * @param stockItem StockItemManageReqDTO 对象，包含待过滤的 StockItem 列表。
     * @param service StockWriteStrategyService 对象，用于确定过滤策略。
     * @return 过滤后的 StockItem 列表。
     */
    private List<StockItemManageReqDTO> filterStockItems(List<StockItemManageReqDTO> stockItem, StockWriteStrategyService service) {
        if (service instanceof FactoryStockStrategyServiceImpl) {
            // 厂直库存
            return stockItem.stream()
                    .filter(item -> StringUtils.isBlank(item.getWarehouseId()))
                    .collect(Collectors.toList());
        } else if (service instanceof WarehouseStockStrategyServiceImpl) {
            // 备货仓库存
            return stockItem.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getWarehouseId()))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**在途回退 备货采购单取消**/
    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 30)
    public DataResponse<Boolean> transitStockReturn(StockManageReqDTO req) {
        TransitStockWriteService transitStockService = stockWriteStrategyFactory.getTransitStockService(WAREHOUSE);
        return transitStockService.transitStockReturn(req);
    }

    /**在途转现货 备货采购单入库**/
    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 30)
    public DataResponse<Boolean> transitStockToStock(StockManageReqDTO req) {
        TransitStockWriteService transitStockService = stockWriteStrategyFactory.getTransitStockService(WAREHOUSE);
        return transitStockService.transitStockToStock(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 30)
    public DataResponse<Boolean> splitStock(StockSplitReqDTO req) {
        List<StockManageReqDTO> manageReqDTOList = req.getStockManageReqDTOList();
        // 子单ID
        Set<String> childOrderIds = manageReqDTOList.stream().map(StockManageReqDTO::getBizNo).collect(Collectors.toSet());
        // 查询子单预占流水是否已经存在 现货预占+在途预占
        Map<String, Map<Long, List<StockLogPO>>> childOrderSkuLogMap = stockLogAtomicService.getStockLogForMultiOperateTpeMap(childOrderIds, Lists.newArrayList(StockOperateTypeEnum.OCCUPY, StockOperateTypeEnum.WAITING_ALLOCATION));
        // 存在流水校验数量是否一致
        if (MapUtils.isNotEmpty(childOrderSkuLogMap)) {
            return checkParentOrderStockLog(manageReqDTOList, childOrderSkuLogMap);
        }

        // 父单ID
        String parentOrderId = manageReqDTOList.get(0).getOrderId();
        // 查询父单号的流水，遍历子单和商品处理增加记录流水
        Map<String, Map<Long, List<StockLogPO>>> parentOrderSkuLogMap = stockLogAtomicService.getStockLogForMultiOperateTpeMap(Sets.newHashSet(parentOrderId),
                Lists.newArrayList(StockOperateTypeEnum.OCCUPY, StockOperateTypeEnum.RELEASE, StockOperateTypeEnum.WAITING_ALLOCATION, StockOperateTypeEnum.TRANSIT_STOCK_TRANSFER_STOCK_OCCUPY));
        if (MapUtils.isEmpty(parentOrderSkuLogMap)) {
            log.warn("StockManageServiceImpl.splitStock 订单没有预占库存流水，订单号：{}", parentOrderId);
            return buildErr(StockOperateResEnum.ORDER_SPLIT_STOCK_NO_RECORD,true,parentOrderId);
        }

        List<StockLogPO> stockLogPOList = Lists.newArrayList();
        Map<Long, List<StockLogPO>> skuStockLogMap = parentOrderSkuLogMap.get(parentOrderId);
        for (StockManageReqDTO childOrder : manageReqDTOList) {
            List<StockItemManageReqDTO> stockItem = childOrder.getStockItem();
            for (StockItemManageReqDTO item : stockItem) {
                if (!verifySkuSplitItem(item, skuStockLogMap)) {
                    log.warn("StockManageServiceImpl.splitStock 订单:{}对应的sku:{}在拆单流水中找不到对应记录, skuNum:{}", childOrder.getBizNo(), item.getSkuId(), item.getNum());
                    return buildErr(StockOperateResEnum.ORDER_SPLIT_STOCK_SKU_NO_RECORD, true, parentOrderId, item.getSkuId());
                }

                List<StockLogPO> list = skuStockLogMap.get(item.getSkuId());

                this.buildChildStockLog(childOrder, item, list, stockLogPOList);
            }
        }

        // 保存子单流水
        stockLogAtomicService.save(stockLogPOList);
        return DataResponse.success(Boolean.TRUE);
    }

    /**
     * 验证库存拆分项是否匹配库存变更记录
     * @param item 库存项管理请求DTO，包含skuId和num等信息
     * @param skuStockLogMap 库存变更记录映射表，key为skuId，value为对应库存变更记录列表
     * @return 当存在与请求数量匹配的库存变更记录时返回true，否则返回false
     */
    private boolean verifySkuSplitItem(StockItemManageReqDTO item, Map<Long, List<StockLogPO>> skuStockLogMap) {
        // 检查 skuId 在 map 中是否存在
        List<StockLogPO> stockLogList = skuStockLogMap.get(item.getSkuId());
        if (CollectionUtils.isEmpty(stockLogList)) {
            return false;
        }

        return true;
//        // 检查是否存在一条记录的 num 与 item.getNum() 相匹配
//        boolean hasMatchingNum = stockLogList.stream()
//                .anyMatch(stockLog -> Objects.equals(stockLog.getNum(), item.getNum()));
//
//        return hasMatchingNum;
    }

    @NotNull
    private DataResponse<Boolean> checkParentOrderStockLog(List<StockManageReqDTO> manageReqDTOList, Map<String, Map<Long, List<StockLogPO>>> childOrderSkuLogMap) {
        for (StockManageReqDTO order : manageReqDTOList) {
            String bizNo = order.getBizNo();
            Map<Long, List<StockLogPO>> skuStockLogMap = childOrderSkuLogMap.get(bizNo);
            if (CollectionUtils.isNotEmpty(order.getStockItem()) && MapUtils.isEmpty(skuStockLogMap)) {
                log.warn("StockManageServiceImpl.splitStock 子单[{}]预占流水不存在 order={}",bizNo,JSON.toJSONString(order));
                return buildErr(StockOperateResEnum.ORDER_SPLIT_STOCK_NO_RECORD, false, bizNo);
            }
            // 校验同一个商品预占记录是否相同
            for (StockItemManageReqDTO item : order.getStockItem()) {
                boolean anyMatch = skuStockLogMap.getOrDefault(item.getSkuId(), Lists.newArrayList()).stream()
                        .anyMatch(logPO -> item.getNum().equals(logPO.getNum()) && Objects.equals(item.getWarehouseId(), logPO.getWarehouseId()));
                if (!anyMatch) {
                    log.warn("StockManageServiceImpl.splitStock 子单[{}]商品[{}]没有父单[{}]对应的预占流水不存在 order={}",bizNo,item.getSkuId(),order.getOrderId(),JSON.toJSONString(order));
                    return buildErr(StockOperateResEnum.ORDER_SPLIT_STOCK_NO_RECORD, false, bizNo);
                 }
            }
        }
        return DataResponse.success(Boolean.TRUE);
    }

    private void buildChildStockLog(StockManageReqDTO childOrder, StockItemManageReqDTO item, List<StockLogPO> list, List<StockLogPO> stockLogPOList) {
        Date date = new Date();
        for (StockLogPO po : list) {
            StockLogPO tempPo = StockConvert.INSTANCE.po2TempPo(po);
            // 跳过不同的
            if (!Objects.equals(item.getWarehouseId(),po.getWarehouseId())) {
                continue;
            }
            tempPo.setId(null);
            tempPo.setNum(item.getNum());
            tempPo.setUpdateTime(date);
            tempPo.setCreateTime(date);
            tempPo.setCreator(item.getUpdater());
            tempPo.setUpdater(item.getUpdater());
            tempPo.setOrderId(childOrder.getOrderId());
            tempPo.setBizNo(childOrder.getBizNo());
            stockLogPOList.add(tempPo);
        }
    }

    private void buildPurchaseChildStockLog(StockManageReqDTO childOrder, StockItemManageReqDTO item, List<StockLogPO> list, List<StockLogPO> stockLogPOList) {
        Date date = new Date();
        for (StockLogPO po : list) {
            StockLogPO tempPo = StockConvert.INSTANCE.po2TempPo(po);
            // 跳过不同的
/*            if (!Objects.equals(item.getWarehouseId(),po.getWarehouseId())) {
                continue;
            }*/
            tempPo.setId(null);
            tempPo.setNum(item.getNum());
            tempPo.setUpdateTime(date);
            tempPo.setCreateTime(date);
            tempPo.setCreator(item.getUpdater());
            tempPo.setUpdater(item.getUpdater());
            tempPo.setOrderId(childOrder.getOrderId());
            tempPo.setBizNo(childOrder.getBizNo());
            stockLogPOList.add(tempPo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class,timeout = 30)
    public DataResponse<Boolean> splitPurchaseOrderStock(StockSplitReqDTO req) {
        List<StockManageReqDTO> manageReqDTOList = req.getStockManageReqDTOList();
        /**
         * 1、根据父采购单号查询下面所有的子采购单的数量，去除父采购单流水记录
         * 2、如果传入的数量少于现有子采购单的数量，丢弃 不处理
         * 3、如果传入的数量大于现有子采购单的数量，删除现有子采购单记录，增加新子采购单记录
         */

        // 将父单和拆单后的子单列表处理,按照升序
        TreeMap<String, List<StockManageReqDTO>> parentOrderAndChildOrderListMap = manageReqDTOList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(StockManageReqDTO::getOrderId,TreeMap::new,Collectors.toList()));

        // 遍历每个父单，处理父单和子单的库存业务流水
        for (Map.Entry<String, List<StockManageReqDTO>> entry : parentOrderAndChildOrderListMap.entrySet()) {
            // 父单ID
            String parentOrderId = entry.getKey();
            List<StockManageReqDTO> childOrderList = entry.getValue();
            // 子单ID
            Set<String> childOrderIds = childOrderList.stream().map(StockManageReqDTO::getBizNo).collect(Collectors.toSet());
            ArrayList<StockOperateTypeEnum> stockOperateTypeEnums = Lists.newArrayList(StockOperateTypeEnum.OCCUPY, StockOperateTypeEnum.RELEASE, StockOperateTypeEnum.APPEND_TRANSIT_STOCK);
            // 已记录的子单库存记录
            Map<String, Map<Long, List<StockLogPO>>> childOrderSkuLogMap = stockLogAtomicService.getParentOrderIdForMultiOperateTpeMap(parentOrderId, stockOperateTypeEnums);
            // 子单的数量大于当前传入的子单数量时，表示已经拆分业务流水，不需要处理
            if (MapUtils.isNotEmpty(childOrderSkuLogMap) &&  childOrderSkuLogMap.size() >= childOrderIds.size()) {
                // 记录告警日志
                log.warn("StockManageServiceImpl.splitPurchaseOrderStock子单的数量 ({}) 大于或等于当前传入的子单数量 ({}), 表示已经拆分业务流水，不需要处理。", childOrderSkuLogMap.size(), childOrderIds.size());
                continue;
            }
            // 父拆子单后，如果子单数量不同时，不能拆业务流水，报错处理
            if (childOrderIds.size() < 2) {
                return buildErr(StockOperateResEnum.PURCHASE_ORDER_SPLIT_STOCK_NO_RECORD, true, parentOrderId);
            }

            // 先清理老的子单库存记录数据
            this.removeHistoryChildStockLog(childOrderSkuLogMap);
            // 查询父单号的流水，遍历子单和商品处理增加记录流水
            Map<String, Map<Long, List<StockLogPO>>> parentOrderSkuLogMap = stockLogAtomicService.getStockLogForMultiOperateTpeMap(Sets.newHashSet(parentOrderId),stockOperateTypeEnums);
            if (MapUtils.isEmpty(parentOrderSkuLogMap)) {
                log.warn("StockManageServiceImpl.splitPurchaseOrderStock 采购单/预报备货单没有处理库存流水，父采购单号：{}", parentOrderId);
                return buildErr(StockOperateResEnum.PURCHASE_ORDER_SPLIT_STOCK_NO_RECORD, true, parentOrderId);
            }
            List<StockLogPO> stockLogPOList = Lists.newArrayList();
            Map<Long, List<StockLogPO>> skuStockLogMap = parentOrderSkuLogMap.get(parentOrderId);
            for (StockManageReqDTO childOrder : childOrderList) {
                List<StockItemManageReqDTO> stockItem = childOrder.getStockItem();
                for (StockItemManageReqDTO item : stockItem) {
                    if (!verifySkuSplitItem(item, skuStockLogMap)) {
                        log.warn("StockManageServiceImpl.splitPurchaseOrderStock 采购单/预报备货单:{}对应的sku:{}在拆单流水中找不到对应记录, skuNum:{}", childOrder.getBizNo(), item.getSkuId(), item.getNum());
                        return buildErr(StockOperateResEnum.PURCHASE_ORDER_SPLIT_STOCK_SKU_NO_RECORD, true, parentOrderId, item.getSkuId());
                    }

                    List<StockLogPO> list = skuStockLogMap.get(item.getSkuId());

                    this.buildPurchaseChildStockLog(childOrder, item, list, stockLogPOList);
                }
            }
            // 保存子单流水
            stockLogAtomicService.save(stockLogPOList);
        }
        return DataResponse.success(Boolean.TRUE);
    }

    private void removeHistoryChildStockLog(Map<String, Map<Long, List<StockLogPO>>> childOrderSkuLogMap) {
        if (MapUtils.isEmpty(childOrderSkuLogMap)) {
            return;
        }
        List<StockLogPO> logPOList = Lists.newArrayList();
        for (Map.Entry<String, Map<Long, List<StockLogPO>>> entry : childOrderSkuLogMap.entrySet()) {
            Map<Long, List<StockLogPO>> skuLogPoMap = entry.getValue();
            if (MapUtils.isEmpty(skuLogPoMap)) {
                continue;
            }
            // sku的日志记录
            for (Map.Entry<Long, List<StockLogPO>> skuLogEntry : skuLogPoMap.entrySet()) {
                List<StockLogPO> list = skuLogEntry.getValue();
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                for (StockLogPO po : list) {
                    StockLogPO temp = new StockLogPO();
                    temp.setId(po.getId());
                    temp.setYn(YnEnum.NO.getCode());
                    temp.setUpdater(Constant.SYSTEM);
                    temp.setUpdateTime(new Date());
                    logPOList.add(temp);
                }
            }
        }
        boolean updated = stockLogAtomicService.removeBatchByIds(logPOList);
        log.info("StockManageServiceImpl.removeHistoryChildStockLog 清理历史子单库存日志 logPOList={} updated={}",JSON.toJSONString(logPOList),updated);
    }

    /**
     * 验证仓库是否存在
     * @param reqDTO 包含仓库信息的请求对象
     * @return 验证结果，true表示仓库存在，false表示仓库不存在
     */
    private DataResponse<Boolean> validateWarehouseExist(StockManageReqDTO reqDTO) {
        // 入参有仓ID时，查询仓库列表
        if (reqDTO.getStockItem().stream().anyMatch(stockItem -> StringUtils.isNotBlank(stockItem.getWarehouseId()))){
            // 查询备货仓
            Set<Long> existWarehouseIds = warehouseAtomicService.queryValidateWarehouseByType(WarehouseTypeEnum.STOCKING);
            for (StockItemManageReqDTO stockItem : reqDTO.getStockItem()) {
                if (StringUtils.isNotBlank(stockItem.getWarehouseId()) && !existWarehouseIds.contains(Long.parseLong(stockItem.getWarehouseId()))) {
                    return this.buildErr(StockOperateResEnum.EXCEPTION_WAREHOUSE_EXIST, true,stockItem.getSkuId(),stockItem.getWarehouseId());
                }
            }
        }else {
            // 无仓ID时校验商品跨境本土
            Map<Long, SkuPO> skuMap = skuAtomicService.batchQuerySkuPO(reqDTO.getStockItem().stream().map(StockItemManageReqDTO::getSkuId).collect(Collectors.toSet()));
            for (Map.Entry<Long, SkuPO> entry : skuMap.entrySet()) {
                SkuPO skuPo = entry.getValue();
                // 跨境品不能设置供应商库存
                if (CountryConstant.COUNTRY_ZH.equals(skuPo.getSourceCountryCode())) {
                    this.buildErr(StockOperateResEnum.WARNING_CROSS_BORDER_STOCK, true, entry.getKey());
                }
            }
        }
        return DataResponse.success();
    }




    /**
     * 根据 StockManageReqDTO 中的 StockItemManageReqDTO 列表处理并返回相应的 StockWriteStrategyService 列表。
     * @param req StockManageReqDTO 对象，包含 StockItemManageReqDTO 列表。
     * @return 处理后的 StockWriteStrategyService 列表。
     */
    private List<StockWriteStrategyService> getStrategyList(StockManageReqDTO req){
        List<StockWriteStrategyService> strategyList = new ArrayList<>();
        List<StockItemManageReqDTO> stockItem = req.getStockItem();
        if (stockItem.stream().allMatch(item -> StringUtils.isNotBlank(item.getWarehouseId()))) {
            strategyList.add(stockWriteStrategyFactory.getStrategy(WAREHOUSE)) ;
        }else if (stockItem.stream().allMatch(item -> StringUtils.isBlank(item.getWarehouseId()))){
            strategyList.add( stockWriteStrategyFactory.getStrategy(FACTORY));
        }else {
            strategyList.add(stockWriteStrategyFactory.getStrategy(FACTORY));
            strategyList.add(stockWriteStrategyFactory.getStrategy(WAREHOUSE));
        }
        return strategyList;
    }
}
