package com.jdi.isc.product.soa.service.support.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.frame.BaiduClient;
import com.jdi.isc.product.soa.common.util.CommonUtils;
import com.jdi.isc.product.soa.domain.common.biz.BaiduAccessTokenVO;
import com.jdi.isc.product.soa.domain.translate.TranslateBatchReqVO;
import com.jdi.isc.product.soa.service.support.TextTranslateService;
import com.jdi.isc.product.soa.service.support.cache.LocalCacheService;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;

/**
 * 百度翻译服务
 *
 * <AUTHOR>
 * @date 20230710
 */
@Slf4j
@Service(value = "baiduTranslateService")
public class BaiduTranslateServiceImpl extends AbstractTranslateService implements TextTranslateService {
    //
//    public static final String IMG_TRANS_REQUEST_URL = "https://aip.baidubce.com/file/2.0/mt/pictrans/v1?access_token=";
    public static final String TEXT_TRANS_REQUEST_URL = "https://aip.baidubce.com/rpc/2.0/mt/texttrans/v1?access_token=";
    private static final Integer HTTP_TIME_OUT = 60000;

    static {
        Unirest.setTimeouts(HTTP_TIME_OUT, HTTP_TIME_OUT);
    }

    @Resource
    private BaiduClient baiduClient;

    private static final String AUTO = "auto";

    @Resource
    private LocalCacheService localCacheService;

    /**
     * 百度文本翻译
     * token应缓存获取使用
     *
     * @see <a href="https://ai.baidu.com/ai-doc/MT/4kqryjku9">api</a>
     */
    @PFTracing
    @Override
    public String translate(String inputStr, String source, String target) {
        String result = "";
        BaiduAccessTokenVO res = baiduClient.getToken();
        log.info("BaiduTranslateServiceImpl.translate get token  res={}", res != null ? JSON.toJSONString(res) : null);
        try {
            Map<String, String> langMap = localCacheService.baiduPlatLangMap();
            if (!langMap.containsKey(target)) {
                CommonUtils.errCallBack("TextTranslateService.translate", UmpKeyConstant.BUSINESS_KEY_TRANSLATE_WARNING, String.format("baidu翻译失败:%s ,目标语种%s不存在", inputStr,target));
                return result;
            }

            JSONObject input = new JSONObject();
            input.put("from", langMap.getOrDefault(source,AUTO));
            input.put("to", langMap.get(target));
            input.put("q", inputStr);
            HttpResponse<String> response = Unirest.post(TEXT_TRANS_REQUEST_URL + res.getAccessToken())
                    .header("accept", "application/json")
                    .header("Content-Type", "application/json")
                    .body(input.toJSONString())
                    .asString();
            log.info("BaiduTranslateServiceImpl.chnToEng req:{} , res:{}", input, response.getBody());
            JSONObject jObj = JSONObject.parseObject(response.getBody());
            if (jObj != null && jObj.getJSONObject("result") != null) {
                JSONArray transRes = jObj.getJSONObject("result").getJSONArray("trans_result");
                StringBuilder resSb = new StringBuilder();
                if (transRes != null && !transRes.isEmpty()) {
                    for (int i = 0; i < transRes.size(); i++) {
                        JSONObject paragraph = transRes.getJSONObject(i);
                        resSb.append(paragraph.getString("dst"));
                    }
                    result = resSb.toString();
                }
            }
        } catch (Exception e) {
            CommonUtils.errCallBack("TextTranslateService.translate", UmpKeyConstant.BUSINESS_KEY_TRANSLATE_WARNING, "baidu翻译失败:" + inputStr, e);
        }
        return result;
    }


    @Override
    public Map<String, Map<String, String>> batchTranslate(Set<TranslateBatchReqVO> translateBatchReqVOSet, boolean defaultInputContent){
        return super.batchTranslateMap(translateBatchReqVOSet, defaultInputContent);
    }

    @Override
    public String translateHook(String input, String sourceLang, String targetLang) {
        return translate(input,sourceLang,targetLang);
    }

    @Override
    public String translate(String input, String sourceLang, String targetLang, Integer type) {
        return "";
    }

    @Override
    public String queryRemainingCount() {
        log.info("BaiduTranslateServiceImpl.queryRemainingCount 百度翻译剩余次数查询未实现");
        return "";
    }
}
