package com.jdi.isc.product.soa.service.atomic.category;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.category.po.CategoryBuyerRelationPO;
import com.jdi.isc.product.soa.repository.mapper.category.CategoryBuyerRelationBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 类目采销关系表原子服务
 * @Author: zhaokun51
 * @Date: 2025/06/10 13:20
 **/
@Slf4j
@Service
public class CategoryBuyerRelationAtomicService extends ServiceImpl<CategoryBuyerRelationBaseMapper, CategoryBuyerRelationPO> {

    /**
     * 根据国家代码和京东类目ID获取对应的CategoryBuyerRelationPO对象。
     * @param countryCode 国家代码
     * @param jdCatId 京东类目ID
     * @return 对应的CategoryBuyerRelationPO对象，若不存在则返回null
     */
    public CategoryBuyerRelationPO getByCountryCodeAndJdCatId(String countryCode, Long jdCatId){
        if(StringUtils.isBlank(countryCode) || jdCatId == null){
            return null;
        }
        LambdaQueryWrapper<CategoryBuyerRelationPO> wrapper = Wrappers.<CategoryBuyerRelationPO>lambdaQuery()
                // todo zhaokun51 租户上线时删除
                .eq(CategoryBuyerRelationPO::getTenantId, "JDI")
                .eq(CategoryBuyerRelationPO::getCountryCode, countryCode)
                .eq(CategoryBuyerRelationPO::getJdCatId, jdCatId)
                .eq(CategoryBuyerRelationPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    /**
     * 根据国家代码和类目ID获取类目采销信息。
     * @param countryCode 国家代码
     * @param catId 京东类目ID
     * @return 类目采销信息
     */
    public String getBuyerByCountryCodeAndCatId(String countryCode,Long catId){
        if(StringUtils.isBlank(countryCode) || catId == null){
            log.info("CategoryBuyerRelationAtomicService.getBuyerByCountryCodeAndCatId param is null,countryCode:{},jdCatId:{}",countryCode,catId);
            return null;
        }
        CategoryBuyerRelationPO categoryBuyerRelationPO = this.getByCountryCodeAndJdCatId(countryCode,catId);
        if(categoryBuyerRelationPO == null){
            log.info("CategoryBuyerRelationAtomicService.getBuyerByCountryCodeAndCatId categoryBuyerRelationPO is null");
            return null;
        }

        return categoryBuyerRelationPO.getBuyer();
    }

    /**
     * 根据采销ERP获取分类ID集合。
     * @param buyer 买家信息
     * @return 分类ID集合，若采销ERP信息为空或未找到相关数据则返回null
     */
    public Set<Long> getCatIdByBuyer(String buyer){
        if(StringUtils.isBlank(buyer)){
            log.info("CategoryBuyerRelationAtomicService.getCatIdByBuyer param is null");
            return new HashSet<>();
        }

        LambdaQueryWrapper<CategoryBuyerRelationPO> wrapper = Wrappers.<CategoryBuyerRelationPO>lambdaQuery()
                .select(CategoryBuyerRelationPO::getJdCatId,CategoryBuyerRelationPO::getCountryCode,CategoryBuyerRelationPO::getBuyer)
                // todo zhaokun51 租户上线时删除
                .eq(CategoryBuyerRelationPO::getTenantId, "JDI")
                .eq(CategoryBuyerRelationPO::getBuyer, buyer)
                .eq(CategoryBuyerRelationPO::getYn, YnEnum.YES.getCode());

        List<CategoryBuyerRelationPO> categoryBuyerRelationPOS = this.list(wrapper);
        if(CollectionUtils.isEmpty(categoryBuyerRelationPOS)){
            log.info("CategoryBuyerRelationAtomicService.getCatIdByBuyer categoryBuyerRelationPOS is null");
            return new HashSet<>();
        }

        return categoryBuyerRelationPOS.stream()
                .map(CategoryBuyerRelationPO::getJdCatId)
                .collect(Collectors.toSet());
    }

    /**
     * 根据商品类目ID集合获取商品类目与买家关系列表
     * @param jdCatIds 商品类目ID集合
     * @return 符合条件的商品类目与买家关系列表
     */
    public List<CategoryBuyerRelationPO> getByCountryCodeJdCatIds(String countryCode,Collection<Long> jdCatIds){
        if(StringUtils.isBlank(countryCode) || CollectionUtils.isEmpty(jdCatIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CategoryBuyerRelationPO> wrapper = Wrappers.<CategoryBuyerRelationPO>lambdaQuery()
                .select(CategoryBuyerRelationPO::getJdCatId,CategoryBuyerRelationPO::getBuyer
                        ,CategoryBuyerRelationPO::getUpdater,CategoryBuyerRelationPO::getUpdateTime)
                // todo zhaokun51 租户上线时删除
                .eq(CategoryBuyerRelationPO::getTenantId, "JDI")
                .eq(CategoryBuyerRelationPO::getCountryCode, countryCode)
                .in(CategoryBuyerRelationPO::getJdCatId, jdCatIds)
                .eq(CategoryBuyerRelationPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    /**
     * 根据国家代码和类目ID集合获取采销ERP信息。
     * @param countryCode 国家代码
     * @param catIds 类目ID集合
     * @return 买家信息的Map对象，其中key为买家ID，value为买家名称；如果参数为空或未找到相关数据，则返回null。
     */
    public Map<Long,String> getBuyerByCountryCodeAndCatIds(String countryCode,Collection<Long> catIds){
        if(StringUtils.isBlank(countryCode) || CollectionUtils.isEmpty(catIds)){
            log.info("CategoryBuyerRelationAtomicService.getBuyerByCountryCodeAndCatIds param is null,countryCode:{},catIds:{}",countryCode, JSONObject.toJSONString(catIds));
            return null;
        }
        List<CategoryBuyerRelationPO> categoryBuyerRelationPOS = this.getByCountryCodeJdCatIds(countryCode,catIds);
        if(CollectionUtils.isEmpty(categoryBuyerRelationPOS)){
            log.info("CategoryBuyerRelationAtomicService.getBuyerByCountryCodeAndCatIds categoryBuyerRelationPOS is null");
            return null;
        }

        return categoryBuyerRelationPOS.stream().collect(Collectors.toMap(CategoryBuyerRelationPO::getJdCatId,CategoryBuyerRelationPO::getBuyer));
    }

    /**
     * 根据商品类目ID集合获取对应的买家ID列表
     * @param jdCatIds 商品类目ID集合
     * @return 以商品类目ID为键，买家ID列表为值的Map对象
     */
    public Map<Long,List<String>> getBuyerByCatIds(Collection<Long> jdCatIds){
        if(CollectionUtils.isEmpty(jdCatIds)){
            log.info("CategoryBuyerRelationAtomicService.getBuyerByCatIds param is null,jdCatIds:{}", JSONObject.toJSONString(jdCatIds));
            return null;
        }
        LambdaQueryWrapper<CategoryBuyerRelationPO> wrapper = Wrappers.<CategoryBuyerRelationPO>lambdaQuery()
                .select(CategoryBuyerRelationPO::getJdCatId,CategoryBuyerRelationPO::getBuyer)
                // todo zhaokun51 租户上线时删除
                .eq(CategoryBuyerRelationPO::getTenantId, "JDI")
                .in(CategoryBuyerRelationPO::getJdCatId, jdCatIds)
                .eq(CategoryBuyerRelationPO::getYn, YnEnum.YES.getCode());
        List<CategoryBuyerRelationPO> categoryBuyerRelationPOS = this.list(wrapper);
        if(CollectionUtils.isEmpty(categoryBuyerRelationPOS)){
            log.info("CategoryBuyerRelationAtomicService.getBuyerByCatIds categoryBuyerRelationPOS is null");
            return null;
        }

        return categoryBuyerRelationPOS.stream()
                .collect(Collectors.groupingBy(
                        CategoryBuyerRelationPO::getJdCatId,
                        Collectors.mapping(CategoryBuyerRelationPO::getBuyer, Collectors.toList())
                ));
    }

}
