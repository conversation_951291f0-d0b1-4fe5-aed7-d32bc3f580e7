package com.jdi.isc.product.soa.service.manage.price.markupRate.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jd.fastjson.JSON;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.domain.brand.po.BrandLangPO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryComboBoxVO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryIdVO;
import com.jdi.isc.product.soa.domain.price.markupRate.biz.MarkupRatePageReqVO;
import com.jdi.isc.product.soa.domain.price.markupRate.biz.MarkupRatePageVO;
import com.jdi.isc.product.soa.domain.price.markupRate.biz.MarkupRateReqVO;
import com.jdi.isc.product.soa.domain.price.markupRate.biz.MarkupRateVO;
import com.jdi.isc.product.soa.domain.price.markupRate.po.MarkupRatePO;
import com.jdi.isc.product.soa.domain.mku.po.MkuLangPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.po.PriceLogPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuLogPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum;
import com.jdi.isc.product.soa.service.atomic.brand.BrandLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.markupRate.MarkupRateAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.PriceLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuLogAtomicService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.price.markupRate.MarkupRateManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuRelationManageService;
import com.jdi.isc.product.soa.service.mapstruct.markupRate.MarkupRateConvert;
import com.jdi.isc.product.soa.service.support.ProductIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 国家协议加价率数据维护服务实现
 * @Author: wangpeng965
 * @Date: 2025/02/28 10:31
 **/

@Slf4j
@Service
public class MarkupRateManageServiceImpl extends BaseManageSupportService<MarkupRateVO, MarkupRatePO> implements MarkupRateManageService {

    @Resource
    private MarkupRateAtomicService markupRateAtomicService;

    @Resource
    private MkuRelationManageService mkuRelationManageService;

    @Resource
    private CategoryOutService categoryOutService;

    @Resource
    private MkuLangAtomicService mkuLangAtomicService;

    @Resource
    private SkuLogAtomicService skuLogAtomicService;

    @Resource
    private ProductIdGenerator productIdGenerator;

    @Resource
    private BrandLangAtomicService brandLangAtomicService;

    @Resource
    private PriceLogAtomicService priceLogAtomicService;

    @Override
    public PageInfo<MarkupRatePageVO> pageSearch(MarkupRatePageReqVO vo) {
        PageInfo<MarkupRatePageVO> pageInfo = new PageInfo<>();
        pageInfo.setSize(vo.getSize());
        pageInfo.setIndex(vo.getIndex());
        this.buildQuery(vo);
        long total = markupRateAtomicService.pageSearchTotal(vo);
        if (total == 0) {
            pageInfo.setTotal(0);
            return pageInfo;
        }
        List<MarkupRatePageVO> markupRateList = markupRateAtomicService.pageSearch(vo);
        if (CollectionUtils.isEmpty(markupRateList)){
            return pageInfo;
        }
        this.setSkuId(markupRateList);
        this.setMkuId(markupRateList);
        this.setMkuTitle(markupRateList);
        this.setCatIdAndName(markupRateList);
        this.setBrandInfo(markupRateList);
        pageInfo.setTotal(total);
        pageInfo.setRecords(markupRateList);
        return pageInfo;
    }

    private void buildQuery(MarkupRatePageReqVO vo){
        Set<Long> skuIdSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(vo.getSkuIds())){
            skuIdSet.addAll(vo.getSkuIds());
        }
        if(CollectionUtils.isNotEmpty(vo.getMkuIds())){
            Map<Long, SkuPO> mkuSkuPOMap = mkuRelationManageService.queryMkuIdSkuPoMapByMkuIds(vo.getMkuIds());
            // 将 mkuSkuPOMap 转换为 mkuIdSkuIdMap
            Map<Long, Long> mkuIdSkuIdMap = mkuSkuPOMap.entrySet().stream()
                .collect(Collectors.toMap(
                    Map.Entry::getKey, // 使用原始 Map 的键
                    entry -> entry.getValue().getSkuId() // 从 SkuPO 对象中提取出 Long 类型的 SKU ID
                ));
            if(CollectionUtils.isNotEmpty(skuIdSet)){
                skuIdSet.retainAll(mkuIdSkuIdMap.values());
                if(CollectionUtils.isEmpty(skuIdSet)){
                    skuIdSet.add(-1L);
                }
            }else {
                if(MapUtils.isEmpty(mkuIdSkuIdMap)){
                    skuIdSet.retainAll(mkuIdSkuIdMap.values());
                }else {
                    skuIdSet.addAll(mkuIdSkuIdMap.values());
                }
            }
        }
        // 类目id转换为终极类目id
        Set<Long> lastCatIds = this.getLastCatIds(vo);
        vo.setCatIds(lastCatIds);
        vo.setSkuIds(new ArrayList<>(skuIdSet));
    }

    /**
     * 根据输入的类目信息，查询并返回最后一级类目的ID集合。
     * @param markupRatePageReqVO 包含所有类目信息的对象。
     */
    private Set<Long> getLastCatIds(MarkupRatePageReqVO markupRatePageReqVO){
        CategoryIdVO categoryIdVO = new CategoryIdVO();
        categoryIdVO.setFirstCatId(markupRatePageReqVO.getFirstCatId());
        categoryIdVO.setSecondCatId(markupRatePageReqVO.getSecondCatId());
        categoryIdVO.setThirdCatId(markupRatePageReqVO.getThirdCatId());
        categoryIdVO.setLastCatId(markupRatePageReqVO.getLastCatId());
        Set<Long> catIdSet = categoryOutService.queryLastCatId(categoryIdVO);
        return catIdSet;
    }

    /**
     * 设置字幕页的MKU标题（中文）
     * @param markupRateVOList 字幕页列表
     */
    private void setMkuTitle(List<MarkupRatePageVO> markupRateVOList){
        if(CollectionUtils.isEmpty(markupRateVOList)){
            return;
        }
        Set<Long> mkuIds = markupRateVOList.stream().filter(Objects::nonNull).filter(vo -> vo.getMkuId() != null).map(MarkupRatePageVO::getMkuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(mkuIds)){
            return;
        }
        Map<Long, MkuLangPO> mkuLangMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(mkuIds)){
            mkuLangMap = mkuLangAtomicService.getMkuLangNameByMkuIds(new ArrayList<>(mkuIds), LangConstant.LANG_ZH);
        }
        for (MarkupRatePageVO vo : markupRateVOList){
            MkuLangPO mkuLangPOzh = mkuLangMap.get(vo.getMkuId());
            if(Objects.nonNull(mkuLangPOzh)){
                vo.setMkuTitle(mkuLangPOzh.getMkuTitle());
            }
        }
    }


    private void setBrandInfo(List<MarkupRatePageVO> markupRateList){
        if(CollectionUtils.isEmpty(markupRateList)){
            return;
        }
        Set<Long> brandIdSet = markupRateList.stream().filter(Objects::nonNull).filter(vo -> vo.getBrandId() != null).map(MarkupRatePageVO::getBrandId).collect(Collectors.toSet());
        Map<Long, BrandLangPO> brandMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(brandIdSet)){
            brandMap = brandLangAtomicService.queryBrandMapByIdLang(brandIdSet, LangConstant.LANG_ZH);
        }
        if(CollectionUtils.isEmpty(brandMap)){
            return;
        }
        for (MarkupRatePageVO markupRatePageVO : markupRateList){
            if(brandMap.containsKey(markupRatePageVO.getBrandId())){
                BrandLangPO brandLangPO = brandMap.get(markupRatePageVO.getBrandId());
                markupRatePageVO.setBrandName(brandLangPO.getLangName());
            }
        }
    }


    /**
     * 设置短标题的分类 ID 和名称。
     * @param markupRateVOList 字幕页列表
     */
    private void setCatIdAndName(List<MarkupRatePageVO> markupRateVOList){
        if(CollectionUtils.isEmpty(markupRateVOList)){
            return;
        }
        try {
            Set<Long> lastCatIds = new HashSet<>();
            for (MarkupRatePageVO markupRatePageVO : markupRateVOList){
                if(Objects.nonNull(markupRatePageVO.getLastCatId())) {
                    lastCatIds.add(markupRatePageVO.getLastCatId());
                }
            }
            if(CollectionUtils.isEmpty(lastCatIds)) {
                return;
            }
            Map<Long, List<CategoryComboBoxVO>> pathMap = categoryOutService.queryPath(lastCatIds, LangContextHolder.get());
            if (Objects.isNull(pathMap) || MapUtils.isEmpty(pathMap)){
                return;
            }
            Map<Long, String> lastCatMap = new HashMap<>(pathMap.entrySet().stream()
                .collect(HashMap::new, (m, e) -> m.put(e.getKey(), categoryOutService.joinPath(e.getValue())), HashMap::putAll));
            markupRateVOList.forEach(item->{
                item.setCatName(lastCatMap.get(item.getLastCatId()));
                List<CategoryComboBoxVO> categoryComboBoxVOList = pathMap.get(item.getLastCatId());
                if(CollectionUtils.isNotEmpty(categoryComboBoxVOList)) {
                    item.setFirstCatId(categoryComboBoxVOList.get(0).getId());
                    item.setSecondCatId(categoryComboBoxVOList.get(1).getId());
                    item.setThirdCatId(categoryComboBoxVOList.get(2).getId());
                }
            });
        }catch (Exception e){
            log.error("MarkupRateManageServiceImpl.setCatIdAndName markupRateVOList:{}",JSON.toJSONString(markupRateVOList),e);
        }
    }

    /**
     * 设置子标题页面的 SKU ID 列表。
     * @param markupRateVOList 子标题页面列表。
     */
    private void setSkuId(List<MarkupRatePageVO> markupRateVOList){
        if(CollectionUtils.isEmpty(markupRateVOList)){
            return;
        }
        List<Long> mkuList = markupRateVOList.stream().filter(Objects::nonNull).filter(vo -> vo.getMkuId() != null).map(MarkupRatePageVO::getMkuId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(mkuList)){
            return;
        }
        Map<Long, List<MkuRelationPO>> mkuIdPoListMap = mkuRelationManageService.queryMkuBindSkuListMap(mkuList);
        for (MarkupRatePageVO markupRatePageVO : markupRateVOList){
            if(mkuIdPoListMap.containsKey(markupRatePageVO.getMkuId())){
                List<MkuRelationPO> mkuRelationPOS = mkuIdPoListMap.get(markupRatePageVO.getMkuId());
                List<Long> skuIdList = mkuRelationPOS.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toList());
                markupRatePageVO.setSkuIds(skuIdList);
            }
        }
    }

    private void setMkuId(List<MarkupRatePageVO> markupRateVOList){
        if(CollectionUtils.isEmpty(markupRateVOList)){
            return;
        }
        Set<Long> skuIdS = markupRateVOList.stream().filter(Objects::nonNull).filter(vo -> vo.getSkuId() != null).map(MarkupRatePageVO::getSkuId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(skuIdS)){
            return;
        }
        Map<Long, Long> skuIdMkuIdMap = mkuRelationManageService.queryMkuIdBySkuIds(skuIdS);
        for (MarkupRatePageVO markupRatePageVO : markupRateVOList){
            if(skuIdMkuIdMap.containsKey(markupRatePageVO.getSkuId())){
                Long mkuId = skuIdMkuIdMap.get(markupRatePageVO.getSkuId());
                markupRatePageVO.setMkuId(mkuId);
            }
        }
    }
    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> saveOrUpdate(MarkupRateVO markupRateVO) {
        try {
            log.info("MarkupRateManageServiceImpl.saveOrUpdate, input={}", JSONObject.toJSONString(markupRateVO));
            MarkupRateReqVO markupRateReqVO = createMarkupRateReqVO(markupRateVO);
            MarkupRatePO markupRatePo = markupRateAtomicService.getMarkupRate(markupRateReqVO);
            String bizNo = "";
            String sourceJson = "";

            if(Objects.nonNull(markupRatePo)){
                sourceJson = JSON.toJSONString(markupRatePo);
                bizNo = markupRatePo.getBizNo();
                markupRatePo.setAgreementMarkupRate(markupRateVO.getAgreementMarkupRate());
                markupRatePo.setJdMarkupRate(markupRateVO.getJdMarkupRate());
                markupRatePo.setUpdater(markupRateVO.getUpdater());
                markupRatePo.setUpdateTime(new Date().getTime());
                markupRatePo.setBizNo(null);
            }else {
                markupRatePo = MarkupRateConvert.INSTANCE.vo2Po(markupRateVO);
                if(Objects.nonNull(markupRatePo.getJdCatId())){
                    Set<Long> catIdSet = new HashSet<>();
                    catIdSet.add(markupRatePo.getJdCatId());
                    Map<Long, String> longStringMap = categoryOutService.queryPathStr(catIdSet, LangContextHolder.get());
                    if(longStringMap.isEmpty() || !longStringMap.containsKey(markupRatePo.getJdCatId())){
                        return DataResponse.error("类目Id不存在");
                    }
                }
                DataResponse<Boolean> checkResult = checkAndInitInput(markupRatePo);
                if (!checkResult.getSuccess()){
                    log.info("MarkupRateManageServiceImpl.saveOrUpdate MarkupRateBizNo fail, input={}, checkResult={}", JSONObject.toJSONString(markupRateVO), JSONObject.toJSONString(checkResult));
                    return DataResponse.error("生成业务Id失败。");
                }
                bizNo = markupRatePo.getBizNo();
            }
            // 保存对象
            if(Objects.nonNull(markupRatePo.getJdMarkupRate())){
                markupRatePo.setJdMarkupRate(markupRatePo.getJdMarkupRate().setScale(2, RoundingMode.HALF_UP));
            }
            if(Objects.nonNull(markupRatePo.getAgreementMarkupRate())){
                markupRatePo.setAgreementMarkupRate(markupRatePo.getAgreementMarkupRate().setScale(2, RoundingMode.HALF_UP));
            }
            boolean saveRes = markupRateAtomicService.saveOrUpdate(markupRatePo);
            if (!saveRes){
                log.warn("MarkupRateManageServiceImpl.saveOrUpdate, MarkupRatePO fail. MarkupRatePO={}", JSONObject.toJSONString(markupRatePo));
                return DataResponse.error("协议加价率,保存失败。");
            }
            PriceLogPO priceLogPO = getPriceLogPO(markupRatePo);
            priceLogAtomicService.save(priceLogPO);
            sendLog(bizNo,sourceJson,markupRatePo);
        }catch (Exception e){
            log.error("MarkupRateManageServiceImpl.saveOrUpdate markupRateVO:{}", JSON.toJSONString(markupRateVO), e);
            return DataResponse.error("协议加价率,保存失败，请联系管理员。");
        }
        return DataResponse.success();
    }

    private PriceLogPO getPriceLogPO(MarkupRatePO param){
        PriceLogPO priceLogPO = new PriceLogPO();
        priceLogPO.setBizId(param.getId().toString());
        priceLogPO.setBizType(PriceTypeEnum.MARKUP_RATE.getCode());
        priceLogPO.setBizValue(param.getAgreementMarkupRate());
        priceLogPO.setValue1(param.getSourceCountryCode());
        priceLogPO.setValue2(param.getTargetCountryCode());
        priceLogPO.setCreateTime(DateUtil.getCurrentTime());
        priceLogPO.setCreator(param.getCreator());
        priceLogPO.setUpdater(param.getUpdater());
        priceLogPO.setUpdateTime(DateUtil.getCurrentTime());
        return priceLogPO;
    }

    /**
     * 将MarkupRateVO对象转换为MarkupRateReqVO对象。
     * @param markupRateVO 包含商品标记率信息的对象。
     * @return 转换后的MarkupRateReqVO对象。
     */
    private MarkupRateReqVO createMarkupRateReqVO(MarkupRateVO markupRateVO){
        MarkupRateReqVO markupRateReqVO = new MarkupRateReqVO();
        markupRateReqVO.setLastCatId(markupRateVO.getLastCatId());
        markupRateReqVO.setSkuId(markupRateVO.getSkuId());
        markupRateReqVO.setBrandId(markupRateVO.getBrandId());
        markupRateReqVO.setSourceCountryCode(markupRateVO.getSourceCountryCode());
        markupRateReqVO.setTargetCountryCode(markupRateVO.getTargetCountryCode());
        return markupRateReqVO;
    }

    @Override
    public DataResponse<List<MarkupRateVO>> batchSaveOrUpdate(List<MarkupRateVO> markupRateVOList) {
        log.info("MarkupRateManageServiceImpl.saveOrUpdate, input={}", JSONObject.toJSONString(markupRateVOList));
        // 参数业务校验
        for (MarkupRateVO markupRateVO : markupRateVOList){
            DataResponse<Boolean> response = saveOrUpdate(markupRateVO);
            if(response.getSuccess()){
                markupRateVO.setValid(response.getSuccess());
            }
        }
        return DataResponse.success(markupRateVOList);
    }

    /**
     * 参数校验
     * @param markupRatePO 提交参数
     * @return 检查结果
     */
    private DataResponse<Boolean> checkAndInitInput(MarkupRatePO markupRatePO){
        if(Objects.isNull(markupRatePO.getBizNo())){
            markupRatePO.setBizNo(productIdGenerator.getMarkupRateBizNo());
        }
        return DataResponse.success();
    }

    /**
     * 根据ID获取标记率详细信息
     * @param reqVO 标记率请求对象，包含要查询的ID
     * @return 标记率详细信息对象，如果ID对应的标记率不存在则返回null
     */
    @Override
    public MarkupRateVO detail(MarkupRateReqVO reqVO) {
        MarkupRatePO po = markupRateAtomicService.getById(reqVO.getId());
        if (Objects.isNull(po)){
            log.info("MarkupRateManageServiceImpl.detail, MarkupRatePO null. id={}", reqVO.getId());
            return null;
        }
        MarkupRateVO vo = MarkupRateConvert.INSTANCE.po2Vo(po);
        setCatIdAndName(vo);
        return vo;
    }

    /**
     * 设置短标题的分类 ID 和名称。
     * @param markupRateVO 字幕页列表
     */
    private void setCatIdAndName(MarkupRateVO markupRateVO){
        if(Objects.isNull(markupRateVO) || Objects.isNull(markupRateVO.getLastCatId())){
            return;
        }
        try {
            Set<Long> lastCatIds = new HashSet<>();
            lastCatIds.add(markupRateVO.getLastCatId());
            Map<Long, List<CategoryComboBoxVO>> pathMap = categoryOutService.queryPath(lastCatIds, LangContextHolder.get());
            if (Objects.isNull(pathMap) || MapUtils.isEmpty(pathMap)){
                return;
            }
            List<CategoryComboBoxVO> categoryComboBoxVOList = pathMap.get(markupRateVO.getLastCatId());
            if(CollectionUtils.isNotEmpty(categoryComboBoxVOList)) {
                markupRateVO.setFirstCatId(categoryComboBoxVOList.get(0).getId());
                markupRateVO.setSecondCatId(categoryComboBoxVOList.get(1).getId());
                markupRateVO.setThirdCatId(categoryComboBoxVOList.get(2).getId());
            }
        }catch (Exception e){
            log.error("MarkupRateManageServiceImpl.setCatIdAndName markupRateVO:{}",JSON.toJSONString(markupRateVO),e);
        }
    }


    @Override
    @PFTracing
    public MarkupRateVO getMarkupRate(MarkupRateReqVO vo) {
        // 如果vo为null，直接返回null
        if (vo == null) {
            return null;
        }
        MarkupRateVO markupRateVO = null;
        MarkupRateReqVO reqVO = new MarkupRateReqVO();
        reqVO.setTargetCountryCode(vo.getTargetCountryCode());
        reqVO.setSourceCountryCode(vo.getSourceCountryCode());
        if(Objects.nonNull(vo.getSkuId())){
            reqVO.setSkuId(vo.getSkuId());
            MarkupRatePO markupRate = markupRateAtomicService.getMarkupRateBySkuId(reqVO);
            if(Objects.nonNull(markupRate)) {
                markupRateVO = MarkupRateConvert.INSTANCE.po2Vo(markupRate);
                return markupRateVO;
            }else {
                reqVO.setSkuId(null);
            }
        }
        if(Objects.nonNull(vo.getBrandId()) && Objects.nonNull(vo.getLastCatId())){
            reqVO.setBrandId(vo.getBrandId());
            reqVO.setLastCatId(vo.getLastCatId());
            reqVO.setSkuId(null);
            MarkupRatePO markupRate = markupRateAtomicService.getMarkupRate(reqVO);
            if(Objects.nonNull(markupRate)) {
                markupRateVO = MarkupRateConvert.INSTANCE.po2Vo(markupRate);
                return markupRateVO;
            }else {
                reqVO.setBrandId(null);
                reqVO.setLastCatId(null);
            }
        }

        if(Objects.nonNull(vo.getBrandId())){
            reqVO.setBrandId(vo.getBrandId());
            reqVO.setLastCatId(null);
            reqVO.setSkuId(null);
            MarkupRatePO markupRate = markupRateAtomicService.getMarkupRate(reqVO);
            if(Objects.nonNull(markupRate)) {
                markupRateVO = MarkupRateConvert.INSTANCE.po2Vo(markupRate);
                return markupRateVO;
            }else {
                reqVO.setBrandId(null);
            }
        }

        if(Objects.nonNull(vo.getLastCatId())){
            reqVO.setLastCatId(vo.getLastCatId());
            reqVO.setBrandId(null);
            reqVO.setSkuId(null);
            MarkupRatePO markupRate = markupRateAtomicService.getMarkupRate(reqVO);
            if(Objects.nonNull(markupRate)) {
                markupRateVO = MarkupRateConvert.INSTANCE.po2Vo(markupRate);
                return markupRateVO;
            }else {
                reqVO.setLastCatId(null);
            }
        }
        MarkupRatePO markupRate = markupRateAtomicService.getMarkupRate(reqVO);
        if(Objects.nonNull(markupRate)) {
            markupRateVO = MarkupRateConvert.INSTANCE.po2Vo(markupRate);
            return markupRateVO;
        }
        return markupRateVO;
    }

    @Override
    public void updateYn(Collection<Long> skuIds, String updater) {
        log.info("更新国家协议加价率yn=0, skuIds={}, updater={}", skuIds, updater);
        if (CollectionUtils.isEmpty(skuIds)) {
            log.info("更新国家协议加价率yn=0, skuIds is empty");
            return;
        }

        List<MarkupRatePO> list = markupRateAtomicService.listMarkupsRateBySkuIds(skuIds);

        List<Long> ids = list.stream().map(MarkupRatePO::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            log.info("更新国家协议加价率yn=0, ids is empty");
            return;
        }

        LambdaUpdateWrapper<MarkupRatePO> wrapper = Wrappers.<MarkupRatePO>lambdaUpdate()
                .in(MarkupRatePO::getId, ids);
        MarkupRatePO update = new MarkupRatePO();
        update.setYn(YnEnum.NO.getCode());
        update.setUpdateInfo(updater);

        boolean result = markupRateAtomicService.update(update, wrapper);
        log.info("更新国家协议加价率yn=0, 更新结果={}, ids={}, updater={}", result, ids, updater);
        if (!result) {
            log.error("更新国家协议加价率yn=0, 更新失败. id={}, updater={}", ids, updater);
            throw new BizException("更新国家协议加价率失败");
        }
    }

    /**
     * 发送日志到数据库。
     * @param mkuId MKU的唯一标识。
     * @param sourceJson 日志的源数据。
     * @param targetData 日志的目标数据。
     */
    private void sendLog(String mkuId, String sourceJson, MarkupRatePO targetData) {
        try {
            SkuLogPO skuLogPO = new SkuLogPO();
            skuLogPO.setSourceJson(sourceJson);
            skuLogPO.setTargetJson(JSONObject.toJSONString(targetData));
            skuLogPO.setKeyType(KeyTypeEnum.MARKUP_RATE.getCode());
            skuLogPO.setKeyId(mkuId);
            skuLogPO.setCreator(targetData.getUpdater());
            skuLogPO.setUpdater(targetData.getUpdater());
            skuLogAtomicService.save(skuLogPO);
            log.info("日志保存成功，MKU: {}", mkuId);
        } catch (Exception e) {
            log.error("存储日志异常，MKU: {},sourceData:{},targetData:{} ,Error: {}", mkuId, sourceJson, JSONObject.toJSONString(targetData), e.getMessage(), e);
        }
    }

}
