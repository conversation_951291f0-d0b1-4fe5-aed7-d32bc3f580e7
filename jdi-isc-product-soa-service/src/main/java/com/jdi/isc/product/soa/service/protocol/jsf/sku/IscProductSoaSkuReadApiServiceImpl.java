package com.jdi.isc.product.soa.service.protocol.jsf.sku;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.sku.IscProductSoaSkuReadApiService;
import com.jdi.isc.product.soa.api.sku.req.*;
import com.jdi.isc.product.soa.api.sku.res.*;
import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.brand.biz.BrandVO;
import com.jdi.isc.product.soa.domain.sku.biz.*;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.po.SpuLangPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.vendor.po.VendorPO;
import com.jdi.isc.product.soa.service.manage.brand.BrandOutService;
import com.jdi.isc.product.soa.service.manage.product.ProductGlobalAttributeManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuCrossBorderSaleStatusReadManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuReadManageService;
import com.jdi.isc.product.soa.service.mapstruct.category.GlobalAttributeConvert;
import com.jdi.isc.product.soa.service.mapstruct.sku.SkuConvert;
import com.jdi.isc.product.soa.service.mapstruct.vendor.VendorConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * sku读服务
 * <AUTHOR>
 * @date 2024/08/06
 */
@Service
@Slf4j
public class IscProductSoaSkuReadApiServiceImpl implements IscProductSoaSkuReadApiService {

    @Resource
    private SkuReadManageService skuReadManageService;
    @Resource
    private SpuReadManageService spuReadManageService;
    @Resource
    private ProductGlobalAttributeManageService productGlobalAttributeManageService;
    @Resource
    private SkuCrossBorderSaleStatusReadManageService skuCrossBorderSaleStatusReadManageService;
    @Resource
    private BrandOutService brandOutService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<SkuCalculateTaxPriceDTO> getSkuCalculateSalePrice(SkuCalculatePriceReqDTO input){
        log.info("IscProductSoaSkuReadApiServiceImpl.getSkuCalculateSalePrice param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        SkuCalculatePriceReqVO param = SkuConvert.INSTANCE.priceReqDto2Vo(input);
        SkuCalculateTaxPriceVO priceVO = skuReadManageService.getSkuCalculateSalePrice(param);
        return DataResponse.success(SkuConvert.INSTANCE.priceResVo2Dto(priceVO));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<String, SkuBaseInfoApiDTO>> querySkuBaseInfo(QuerySkuReqDTO reqDTO) {
        return skuReadManageService.querySkuBaseInfo(reqDTO);
    }


    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<String, SkuBaseInfoApiDTO>> querySkuBaseInfoByVendorSkuIds(QuerySkuReqDTO reqDTO) {
        Set<Long> vendorSkuIds = reqDTO.getVendorSkuIds().stream().map(Long::parseLong).collect(Collectors.toSet());
        Map<Long, SkuPO> skuBaseMap = skuReadManageService.getSkuBaseInfoByVendorSkuIds(vendorSkuIds);
        if (MapUtils.isEmpty(skuBaseMap)) {
            return DataResponse.success(Collections.emptyMap());
        }
        Map<String, SkuBaseInfoApiDTO> resultMap = Maps.newHashMap();
        skuBaseMap.forEach((vendorSkuId,skuPo) -> resultMap.put(String.valueOf(vendorSkuId),SkuConvert.INSTANCE.po2BaseInfoApiDTO(skuPo)));
        return DataResponse.success(resultMap);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<SkuGlobalAttributeDetailApiDTO>> selectGlobalAttribute(SkuGlobalAttributeApiDTO input) {
        log.info("IscProductSoaSkuReadApiServiceImpl.selectGlobalAttribute param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        SkuGlobalAttributeVO param = SkuConvert.INSTANCE.skuGlobalAttributeDto2Vo(input);
        List<SkuGlobalAttributeDetailVO> details = skuReadManageService.selectGlobalAttribute(param);
        return DataResponse.success(SkuConvert.INSTANCE.skuListGlobalAttributeVo2Dto(details));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<String, SkuFeatureApiDTO>> querySkuFeature(QuerySkuReqDTO reqDTO) {
        return DataResponse.success(skuReadManageService.getSkuFeatureBySku(reqDTO));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<String, SkuBaseInfoApiDTO>> querySkuBaseInfoBySpuId(QuerySkuReqDTO reqDTO) {
        log.info("IscProductSoaSkuReadApiServiceImpl.querySkuBaseInfoBySpuId param:{}", JSONObject.toJSONString(reqDTO));
        String spuID = reqDTO.getSpuId();
        if(StringUtils.isBlank(spuID)){
            log.error("IscProductSoaSkuReadApiServiceImpl.querySkuBaseInfoBySpuId spuID is null");
            return null;
        }
        Long spuId = Long.parseLong(spuID);
        Map<Long, SkuPO> skuBaseMap = skuReadManageService.getSkuBaseInfoBySku(spuId);
        if (MapUtils.isEmpty(skuBaseMap)) {
            return DataResponse.success(Collections.emptyMap());
        }
        Map<String, SkuBaseInfoApiDTO> resultMap = Maps.newHashMap();
        skuBaseMap.forEach((skuId,skuPo) -> resultMap.put(String.valueOf(skuId),SkuConvert.INSTANCE.po2BaseInfoApiDTO(skuPo)));
        return DataResponse.success(resultMap);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<String, Map<String, PropertyApiDTO>>> queryProductGlobalAttributeMap(GetProductGlobalAttributeReqDTO reqDTO) {
        Map<String, Map<String, PropertyVO>> skuPropertyMap = productGlobalAttributeManageService.queryProductGlobalAttributeMap(reqDTO);
        return DataResponse.success(GlobalAttributeConvert.INSTANCE.propertyMap2ApiMap(skuPropertyMap));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, SkuAvailableSaleResDTO>> queryCrossBorderSkuLimitAreaMap(QuerySkuAvailableSaleReqDTO reqDTO) {
        QuerySkuAvailableSaleReqVO reqVO = SkuConvert.INSTANCE.saleReqDTO2VO(reqDTO);
        Map<Long, SkuAvailableSaleResultVO> resultVOMap = skuCrossBorderSaleStatusReadManageService.querySkuAvailableLimitArea(reqVO);
        Map<Long, SkuAvailableSaleResDTO> saleResDTOMap = SkuConvert.INSTANCE.resultVoMap2ResDTOMap(resultVOMap);
        return DataResponse.success(saleResDTOMap);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, SkuAvailableSaleResDTO>> queryCrossBorderSkuCanPurchaseMap(QuerySkuAvailableSaleReqDTO reqDTO) {
        QuerySkuAvailableSaleReqVO reqVO = SkuConvert.INSTANCE.saleReqDTO2VO(reqDTO);
        Map<Long, SkuAvailableSaleResultVO> resultVOMap = skuCrossBorderSaleStatusReadManageService.querySkuAvailableCanPurchaseMap(reqVO);
        Map<Long, SkuAvailableSaleResDTO> saleResDTOMap = SkuConvert.INSTANCE.resultVoMap2ResDTOMap(resultVOMap);
        return DataResponse.success(saleResDTOMap);
    }

    @Override
    public DataResponse<List<SkuAvailableSaleResDTO>> queryCrossBorderSkuLimitAreaForAllAreaList(Set<Long> jdSkuIds) {
        List<SkuAvailableSaleResultVO> resultVOList = skuCrossBorderSaleStatusReadManageService.queryCrossBorderSkuLimitAreaForAllAreaList(jdSkuIds);
        List<SkuAvailableSaleResDTO> dtoList = SkuConvert.INSTANCE.listResultVo2ResDTO(resultVOList);
        return DataResponse.success(dtoList);
    }

    @Override
    public DataResponse<List<SkuAvailableSaleResDTO>> queryCrossBorderSkuAvailableSaleForAllAreaList(Set<Long> jdSkuIds) {
        List<SkuAvailableSaleResultVO> resultVOList = skuCrossBorderSaleStatusReadManageService.queryCrossBorderSkuAvailableSaleForAllAreaList(jdSkuIds);
        List<SkuAvailableSaleResDTO> dtoList = SkuConvert.INSTANCE.listResultVo2ResDTO(resultVOList);
        return DataResponse.success(dtoList);
    }

    @Override
    public DataResponse<Map<Long, VendorDTO>> queryJdVendor(Set<Long> jdSkuIds) {
        if (jdSkuIds.size() > 50) {
            return DataResponse.buildError("批量查询超过最大限制");
        }
        Map<Long, VendorPO> map = skuReadManageService.queryVendorBySkuIds(jdSkuIds);
        Map<Long, VendorDTO> result = VendorConvert.INSTANCE.mapPo2Dto(map);
        return DataResponse.success(result);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<SkuBaseInfoApiDTO>> querySkuInfoByJdSkuId(QuerySkuReqDTO reqDTO) {
        List<SkuPO> db = skuReadManageService.getSkuBaseInfoByJdSkuId(reqDTO.getJdSkuIds());
        return DataResponse.success(SkuConvert.INSTANCE.listPo2BaseInfoApiDTO(db));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<SkuPredictPriceResDTO> calculateBrPredictCountryCostPrice(SkuPredictPriceReqDTO query) {
        return skuReadManageService.calculateBrPredictCountryCostPrice(query);
    }

    @Override
    public DataResponse<ProductIdDTO> getProductInfoByIds(List<Long> productIds) {
        ProductIdVO productIdVO = skuReadManageService.getProductInfoByIds(productIds);
        ProductIdDTO productIdDTO = SkuConvert.INSTANCE.productVO2DTO(productIdVO);
        return DataResponse.success(productIdDTO);
    }
}
