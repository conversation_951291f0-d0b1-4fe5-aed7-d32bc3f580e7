package com.jdi.isc.product.soa.service.atomic.sku;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.sku.biz.SkuStockRelationVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.domain.spu.biz.GroupPropertyVO;
import com.jdi.isc.product.soa.repository.mapper.sku.SkuDraftBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: sku草稿表原子服务
 * @Author: zhaokun51
 * @Date: 2024/12/18 11:27
 **/
@Slf4j
@Service
public class SkuDraftAtomicService extends ServiceImpl<SkuDraftBaseMapper, SkuDraftPO> {


    /**
     * 未删除的对象
     * @param id id
     * @return 对象
     */
    public SkuDraftPO getValidById(Long id){
        LambdaQueryWrapper<SkuDraftPO> wrapper = Wrappers.<SkuDraftPO>lambdaQuery()
                .eq(SkuDraftPO::getId, id)
                .eq(SkuDraftPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    /**
     * 查询无状态的草稿
     *
     * @param spuId 商品ID
     * @return 草稿数据
     */
    @PFTracing
    public List<SkuDraftPO> getBySpuId(Long spuId) {
        if(spuId == null){
            return null;
        }

        LambdaQueryWrapper<SkuDraftPO> queryWrapper = Wrappers.lambdaQuery(SkuDraftPO.class)
                .eq(SkuDraftPO::getSpuId, spuId).eq(SkuDraftPO::getYn, YnEnum.YES.getCode())
                .orderByDesc(SkuDraftPO::getUpdateTime);
        return this.list(queryWrapper);
    }

    /**
     * 批量查询SpuDraftPO列表
     * @param spuIds 包含SpuId的集合
     * @return 返回SpuDraftPO列表
     */
    @PFTracing
    public List<SkuDraftPO> queryListBySpuIds(Collection<Long> spuIds) {
        if(CollectionUtils.isEmpty(spuIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SkuDraftPO> queryWrapper = Wrappers.lambdaQuery(SkuDraftPO.class)
                .in(SkuDraftPO::getSpuId, spuIds)
                .eq(SkuDraftPO::getYn, YnEnum.YES.getCode())
                .orderByDesc(SkuDraftPO::getUpdateTime);

        List<SkuDraftPO> skuDraftPOList = super.list(queryWrapper);
        return Optional.ofNullable(skuDraftPOList).orElseGet(Collections::emptyList);
    }

    /**
     * 批量查询SpuDraftPO列表
     * @param spuIds 包含SpuId的集合
     * @return 返回SpuDraftPO列表
     */
    @PFTracing
    public Map<Long,List<SkuDraftPO>> queryMapBySpuIds(Collection<Long> spuIds) {
        if(CollectionUtils.isEmpty(spuIds)){
            return Collections.emptyMap();
        }
        List<SkuDraftPO> skuDraftPOS = this.queryListBySpuIds(spuIds);
        if(CollectionUtils.isEmpty(skuDraftPOS)){
            return Collections.emptyMap();
        }
        return skuDraftPOS.stream().collect(Collectors.groupingBy(SkuDraftPO::getSpuId));
    }

    /**
     * 根据商品ID删除草稿SKU。
     * @param spuId 商品ID
     * @return 删除是否成功
     */
    public boolean deleteBySpuId(Long spuId) {
        if (spuId == null) {
            return false;
        }

        // 创建查询条件
        LambdaUpdateWrapper<SkuDraftPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SkuDraftPO::getYn, YnEnum.NO.getCode());
        updateWrapper.eq(SkuDraftPO::getSpuId, spuId);
        updateWrapper.eq(SkuDraftPO::getYn, YnEnum.YES.getCode());

        // 执行删除操作
        return this.update(updateWrapper);
    }

    /**
     * 根据给定的 SKU IDs 查询对应的 SKU Draft PO，并以 SKU ID 为键返回一个 Map。
     * @param skuIds 要查询的 SKU IDs 集合。
     * @return 包含查询结果的 Map，键为 SKU ID，值为对应的 SkuDraftPO 对象。
     */
    public Map<Long, SkuDraftPO> querySkuInterPropertyMapBySkuIds(Set<Long> skuIds) {
        LambdaQueryWrapper<SkuDraftPO> queryWrapper = Wrappers.lambdaQuery(SkuDraftPO.class)
                .select(SkuDraftPO::getSpuId,SkuDraftPO::getSkuId,SkuDraftPO::getSkuKey,SkuDraftPO::getSkuInterProperty,SkuDraftPO::getSkuJsonInfo)
                .in(SkuDraftPO::getSkuId, skuIds)
                .eq(SkuDraftPO::getYn, YnEnum.YES.getCode());

        List<SkuDraftPO> skuDraftPOList = super.list(queryWrapper);
        return Optional.ofNullable(skuDraftPOList)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(SkuDraftPO::getSkuId, Function.identity()));
    }

    /**
     * 根据 SKU 键集合查询 SKU 的互斥属性映射关系。
     * @param skuKeys SKU 键集合
     * @return SKU 键到 SKU 互斥属性的映射关系
     */
    public Map<String, SkuDraftPO> querySkuInterPropertyMapBySkuKeys(Set<String> skuKeys) {
        LambdaQueryWrapper<SkuDraftPO> queryWrapper = Wrappers.lambdaQuery(SkuDraftPO.class)
                .select(SkuDraftPO::getSpuId,SkuDraftPO::getSkuId,SkuDraftPO::getSkuKey,SkuDraftPO::getSkuInterProperty,SkuDraftPO::getSkuJsonInfo)
                .in(SkuDraftPO::getSkuKey, skuKeys)
                .eq(SkuDraftPO::getYn, YnEnum.YES.getCode());

        List<SkuDraftPO> skuDraftPOList = super.list(queryWrapper);
        return Optional.ofNullable(skuDraftPOList)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(SkuDraftPO::getSkuKey, Function.identity()));
    }

    /**
     * 根据 SKU ID 获取有效的 SKU 草稿信息。
     * @param skuId SKU ID
     * @return 对应的 SKU 草稿信息
     */
    public SkuDraftPO getValidBySkuId(Long skuId) {
        LambdaQueryWrapper<SkuDraftPO> queryWrapper = Wrappers.lambdaQuery(SkuDraftPO.class)
                .eq(SkuDraftPO::getSkuId, skuId)
                .eq(SkuDraftPO::getYn, YnEnum.YES.getCode());

        return super.getOne(queryWrapper);
    }


    /**
     * 根据 SKU 键获取有效的 SKU 草稿信息。
     * @param skuKey SKU 键。
     * @return 对应的 SKU 草稿信息。
     */
    public SkuDraftPO getValidBySkuKey(String skuKey) {
        LambdaQueryWrapper<SkuDraftPO> queryWrapper = Wrappers.lambdaQuery(SkuDraftPO.class)
                .eq(SkuDraftPO::getSkuKey, skuKey)
                .eq(SkuDraftPO::getYn, YnEnum.YES.getCode());

        return super.getOne(queryWrapper);
    }

    /**
     * 根据 SKU ID 更新 SKU 草稿的跨境属性信息。
     * @param vos 互通属性信息列表
     * @param skuId SKU ID
     * @return 更新是否成功
     */
    public boolean updateSkuDraftInterPropertyBySkuId(List<GroupPropertyVO> vos, Long skuId, String updater) {
        LambdaUpdateWrapper<SkuDraftPO> updateWrapper = Wrappers.lambdaUpdate(SkuDraftPO.class)
                .set(SkuDraftPO::getSkuInterProperty, JSON.toJSONString(vos))
                .set(SkuDraftPO::getUpdateTime,System.currentTimeMillis())
                .set(SkuDraftPO::getUpdater, StringUtils.isNotBlank(updater) ? updater : Constant.SYSTEM)
                .eq(SkuDraftPO::getSkuId, skuId)
                .eq(SkuDraftPO::getYn, YnEnum.YES.getCode());
        return super.update(updateWrapper);
    }


    /**
     * 根据 SkuKey 更新 SKU 草稿的跨境属性信息。
     * @param vos 互通属性信息列表
     * @param skuKey sku唯一标识
     * @return 更新是否成功
     */
    public boolean updateSkuDraftInterPropertyBySkuKey(List<GroupPropertyVO> vos, String skuKey, String updater) {
        LambdaUpdateWrapper<SkuDraftPO> updateWrapper = Wrappers.lambdaUpdate(SkuDraftPO.class)
                .set(SkuDraftPO::getSkuInterProperty, JSON.toJSONString(vos))
                .set(SkuDraftPO::getUpdateTime,System.currentTimeMillis())
                .set(SkuDraftPO::getUpdater,StringUtils.isNotBlank(updater) ? updater : Constant.SYSTEM)
                .eq(SkuDraftPO::getSkuKey, skuKey)
                .eq(SkuDraftPO::getYn, YnEnum.YES.getCode());
        return super.update(updateWrapper);
    }

    public List<SkuDraftPO> listSkuDraftByGroupExtAttributePage(Long offset, Integer pageSize){
        // 先查询ID列表
        List<Long> ids = super.getBaseMapper().listSkuDraftIdsByGroupExtAttributePage(offset, pageSize);
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        // 再根据ID列表查询详情
        return super.getBaseMapper().listSkuDraftByIds(ids);
    }

    /**
     * 根据skuId更新SKU库存关联属性信息
     * @param vos SKU库存关联属性值对象列表
     * @param skuId SKU ID
     * @param updater 更新人名称
     * @return 是否更新成功
     */
    public boolean updateSkuStockRelationPropertyBySkuId(List<SkuStockRelationVO> vos, Long skuId, String updater) {
        LambdaUpdateWrapper<SkuDraftPO> updateWrapper = Wrappers.lambdaUpdate(SkuDraftPO.class)
                .set(SkuDraftPO::getSkuStockRelation, JSON.toJSONString(vos))
                .set(SkuDraftPO::getUpdateTime,System.currentTimeMillis())
                .set(SkuDraftPO::getUpdater, org.apache.commons.lang3.StringUtils.isNotBlank(updater) ? updater : Constant.SYSTEM)
                .eq(SkuDraftPO::getSkuId, skuId)
                .eq(SkuDraftPO::getYn, YnEnum.YES.getCode());
        return super.update(updateWrapper);
    }
}