package com.jdi.isc.product.soa.service.protocol.jsf.attribute;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.gms.crs.model.AttributeSettingResult;
import com.jd.gms.greatdane.category.domain.NewAttribute;
import com.jd.gms.greatdane.category.domain.NewAttributeValue;
import com.jd.gms.greatdane.category.request.GetNewAttributeParam;
import com.jd.gms.greatdane.category.request.GetNewAttributeValueParam;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.attribute.IscAttributeReadApiService;
import com.jdi.isc.product.soa.api.attribute.common.AttributeDTO;
import com.jdi.isc.product.soa.api.attribute.req.AttributeQueryReqDTO;
import com.jdi.isc.product.soa.api.attribute.req.AttributeVcQueryReqDTO;
import com.jdi.isc.product.soa.api.attribute.res.AttributeFlatDTO;
import com.jdi.isc.product.soa.api.common.enums.AttributeInputTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.AttributeTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.CategoryAttrTypeEnum;
import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyValueApiDTO;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeFlatVO;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeVO;
import com.jdi.isc.product.soa.domain.category.po.CategoryAttributePO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;
import com.jdi.isc.product.soa.rpc.gms.GmsAttributeRpcService;
import com.jdi.isc.product.soa.service.atomic.category.CategoryAttributeAtomicService;
import com.jdi.isc.product.soa.service.manage.attribute.AttributeOutService;
import com.jdi.isc.product.soa.service.manage.attribute.ExtAttributeLangService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeManageService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.attribute.AttributeConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 属性查询服务
 * <AUTHOR>
 * @date 2024/9/1
 **/
@Slf4j
@Service
public class IscAttributeReadApiServiceImpl implements IscAttributeReadApiService {

    @Resource
    private AttributeOutService attributeOutService;
    @Resource
    private CategoryAttributeAtomicService categoryAttributeAtomicService;
    @Resource
    private ExtAttributeLangService extAttributeLangService;
    @Resource
    private GmsAttributeRpcService gmsAttributeRpcService;
    @Resource
    private SpuConvertService spuConvertService;
    @Resource
    private SaleAttributeManageService saleAttributeManageService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<AttributeDTO>> queryAttributeListByCatId(AttributeQueryReqDTO reqDTO) {
        if(AttributeTypeEnum.SELL == reqDTO.getAttributeTypeEnum()){
            return DataResponse.error("已不支持通过此接口查询销售属性，请使用IscAttributeReadApiService.querySellAttrDetail");
        } else if (AttributeTypeEnum.EXTEND == reqDTO.getAttributeTypeEnum()){
            return this.queryExtAttributeListByCatId(reqDTO);
        } else {
            return DataResponse.error("查询类型不正确");
        }
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<AttributeFlatDTO>> querySellAttrDetail(Long categoryId, String lang) {
        log.info("IscAttributeReadApiServiceImpl.querySellAttrDetail categoryId:{},lang:{}", categoryId,lang);
        List<AttributeFlatVO> flatVOS = attributeOutService.querySellAttrDetail(categoryId, lang);
        List<AttributeFlatDTO> res = AttributeConvert.INSTANCE.flatVo2DTOList(flatVOS);
        return DataResponse.success(res);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<AttributeFlatDTO>> queryExtAttrDetail(Long categoryId, String lang) {
        log.info("IscAttributeReadApiServiceImpl.queryExtAttrDetail categoryId:{},lang:{}", categoryId,lang);
        List<AttributeFlatVO> flatVOS = attributeOutService.queryExtAttrDetail(categoryId, lang);
        List<AttributeFlatDTO> res = AttributeConvert.INSTANCE.flatVo2DTOList(flatVOS);
        return DataResponse.success(res);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<PropertyApiDTO>> queryExtAttrDetailForVc(AttributeVcQueryReqDTO input) {
        log.info("IscAttributeReadApiServiceImpl.queryExtAttrDetailForVc param:{}", JSONObject.toJSONString(input));
        List<PropertyVO> propertyVOS = spuConvertService.getExtendAttribute(input.getCategoryId(),input.getAttributeScope(), input.getLang(),input.getSourceCountryCode());
        List<PropertyApiDTO> res = AttributeConvert.INSTANCE.listPropertyVo2Dto(propertyVOS);
        return DataResponse.success(res);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<PropertyApiDTO>> querySellAttrDetailForVc(AttributeVcQueryReqDTO input) {
        log.info("IscAttributeReadApiServiceImpl.querySaleAttrDetailForVc param:{}", JSONObject.toJSONString(input));
        List<PropertyVO> propertyVOS = spuConvertService.getSaleAttribute(input.getCategoryId(), input.getLang());
        List<PropertyApiDTO> res = AttributeConvert.INSTANCE.listPropertyVo2Dto(propertyVOS);
        return DataResponse.success(res);
    }

    @Override
    public DataResponse<Void> extAttributeLangMapInit(Set<Long> cateSet) {
        extAttributeLangService.init(cateSet);
        return DataResponse.success();
    }

    @Override
    public DataResponse<String> getAttributeById(Integer attributeId) {
        GetNewAttributeParam in = new GetNewAttributeParam();
        in.setComAttIds(Sets.newHashSet(attributeId));
        Map<Integer, NewAttribute> res = gmsAttributeRpcService.getNewAttribute(in);
        if(MapUtils.isNotEmpty(res)){
            NewAttribute target = res.get(attributeId);
            return DataResponse.success(target.getName());
        }
        return DataResponse.success();
    }

    @Override
    public DataResponse<String> getAttributeValueById(Integer attributeId) {
        GetNewAttributeValueParam in = new GetNewAttributeValueParam();
        in.setComValueIds(Sets.newHashSet(attributeId));
        Map<Integer, NewAttributeValue> res = gmsAttributeRpcService.getNewAttributeValue(in);
        if(MapUtils.isNotEmpty(res)){
            NewAttributeValue target = res.get(attributeId);
            return DataResponse.success(target.getName());
        }
        return DataResponse.success();
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<String, Map<String, String>>> getExtAttrLangMapByCnName(Set<String> keyword, Set<String> langList) {
        return DataResponse.success(extAttributeLangService.getExtAttrLangMapByCnName(keyword,langList));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Integer, Map<String, String>>> getExtAttrLangMapById(Set<Integer> attributeIds, Set<String> langList) {
        return DataResponse.success(extAttributeLangService.getExtAttrLangMapById(attributeIds,langList));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Integer, Map<String, String>>> getExtAttrValueLangMapById(Set<Integer> attributeValueIds, Set<String> langList) {
        return DataResponse.success(extAttributeLangService.getExtAttrValueLangMapById(attributeValueIds,langList));
    }

    @Override
    public DataResponse<Void> removeDirtyData() {
        extAttributeLangService.removeDirtyKeyword();
        return DataResponse.success();
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<PropertyApiDTO>> getCategorySaleAttributesByJdCatId(Long jdCatId, String lang) {
        return DataResponse.success(AttributeConvert.INSTANCE.listPropertyVo2Dto(saleAttributeManageService.getCategorySaleAttributesByJdCatId(jdCatId,lang)));
    }

    @Override
    public DataResponse<List<PropertyApiDTO>> obtainJDSkuExtAttributeList(Long catId, Long jdSkuId, String lang) {
        List<PropertyVO> propertyVOS = attributeOutService.obtainJDSkuExtAttributeList(catId, jdSkuId, lang);
        List<PropertyApiDTO> res = AttributeConvert.INSTANCE.listPropertyVo2Dto(propertyVOS);
        return DataResponse.success(res);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<PropertyValueApiDTO>> getJdSkuSaleAttributeDetail(Long jdSkuId, String lang) {
        log.info("IscAttributeReadApiServiceImpl.getJdSkuSaleAttributeDetail jdSkuId: {}, lang: {}", jdSkuId, lang);
        
        if (jdSkuId == null || StringUtils.isBlank(lang)) {
            log.warn("IscAttributeReadApiServiceImpl.getJdSkuSaleAttributeDetail jdSkuId为空 或 lang为空");
            return DataResponse.error("jdSkuId不能为空 或 lang不能为空");
        }
        
        try {
            List<PropertyValueVO> propertyValueVOList = saleAttributeManageService.getJdSkuSaleAttributeDetail(jdSkuId, lang);
            List<PropertyValueApiDTO> result = AttributeConvert.INSTANCE.listPropertyValueVo2Dto(propertyValueVOList);
            log.info("IscAttributeReadApiServiceImpl.getJdSkuSaleAttributeDetail 返回结果: {}", JSON.toJSONString(result));
            return DataResponse.success(result);
        } catch (Exception e) {
            log.error("IscAttributeReadApiServiceImpl.getJdSkuSaleAttributeDetail 获取中台SKU销售属性失败, jdSkuId: {}", jdSkuId, e);
            return DataResponse.error("获取中台SKU销售属性失败: " + e.getMessage());
        }
    }

    @Override
    public DataResponse<List<PropertyApiDTO>> obtainSkuSaleAttributeList(Long skuId, String lang) {
        log.info("IscAttributeReadApiServiceImpl.obtainSkuSaleAttributeList skuId: {}, lang: {}", skuId, lang);
        List<PropertyVO> propertyVOS = saleAttributeManageService.getSkuSaleAttributeDetail(skuId, lang);
        log.info("IscAttributeReadApiServiceImpl.obtainSkuSaleAttributeList 返回结果: {}", JSON.toJSONString(propertyVOS));
        return DataResponse.success(AttributeConvert.INSTANCE.listPropertyVo2Dto(propertyVOS));
    }

    @Override
    public DataResponse<List<PropertyApiDTO>> obtainMkuSaleAttributeList(Long mkuId, String lang) {
        log.info("IscAttributeReadApiServiceImpl.obtainMkuSaleAttributeList mkuId: {}, lang: {}", mkuId, lang);
        List<PropertyVO> propertyVOS = saleAttributeManageService.getMkuSaleAttributeDetail(mkuId, lang);
        log.info("IscAttributeReadApiServiceImpl.obtainMkuSaleAttributeList 返回结果: {}", JSON.toJSONString(propertyVOS));
        return DataResponse.success(AttributeConvert.INSTANCE.listPropertyVo2Dto(propertyVOS));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<PropertyApiDTO>> querySellAttrByCatId(Long categoryId, String lang) {
        log.info("IscAttributeReadApiServiceImpl.querySellAttrWithoutValue categoryId: {}, lang: {}", categoryId, lang);
        List<PropertyVO> propertyVOS = attributeOutService.querySellAttrByCatId(categoryId, lang);
        log.info("IscAttributeReadApiServiceImpl.querySellAttrWithoutValue 返回结果: {}", JSON.toJSONString(propertyVOS));
        return DataResponse.success(AttributeConvert.INSTANCE.listPropertyVo2Dto(propertyVOS));
    }


    /**
     * 根据类目ID和语言集查询扩展属性列表。
     * @param reqDTO 查询请求对象，包含类目ID和语言集信息。
     * @return 扩展属性列表的DataResponse对象。
     */
    private DataResponse<List<AttributeDTO>> queryExtAttributeListByCatId(AttributeQueryReqDTO reqDTO){
        List<AttributeVO> attributeVOList = attributeOutService.queryExtAttrDetail(reqDTO.getCategoryId(),reqDTO.getLangSet());
        // 属性ID与绑定对象映射
        List<AttributeDTO> attributeDTOList = AttributeConvert.INSTANCE.listAttributeVo2DTO(attributeVOList);
        return DataResponse.success(attributeDTOList);
    }

//    /**
//     * 根据类目ID和属性类型查询销售属性列表
//     * @param reqDTO 查询请求对象，包含类目ID和属性类型
//     * @return 包含AttributeDTO的DataResponse对象
//     */
//    private DataResponse<List<AttributeDTO>> querySaleAttributeListByCatId(AttributeQueryReqDTO reqDTO){
//        // 查询绑定关系
//        List<CategoryAttributePO> bindRelations = categoryAttributeAtomicService.getBoundAttr(reqDTO.getCategoryId(), CategoryAttrTypeEnum.forCode(AttributeTypeEnum.SELL.getCode()));
//        if (CollectionUtils.isEmpty(bindRelations)) {
//            log.info("IscAttributeReadApiServiceImpl.BindRelations is empty. reqDTO={}", JSON.toJSONString(reqDTO));
//            return DataResponse.success();
//        }
//        log.info("BindRelations={}", JSON.toJSONString(bindRelations));
//        // 属性ID与绑定对象映射
//        Map<Long/*属性ID*/, CategoryAttributePO> bindMap = bindRelations.stream().filter(Objects::nonNull)
//                .collect(Collectors.toMap(CategoryAttributePO::getAttributeId, o -> o));
//        // 所有属性ID集合
//        Set<Long> attributeIds = bindRelations.stream().filter(Objects::nonNull).map(CategoryAttributePO::getAttributeId).collect(Collectors.toSet());
//        // 查询已绑定部分属性
//        List<AttributeVO> attributeVOList = attributeOutService.queryAttributeVoByIds(attributeIds, CollectionUtils.isEmpty(reqDTO.getLangSet()) ? null : Lists.newArrayList(reqDTO.getLangSet()));
//
//        List<AttributeDTO> attributeDTOList = AttributeConvert.INSTANCE.listAttributeVo2DTO(attributeVOList);
//        if (CollectionUtils.isNotEmpty(attributeDTOList)) {
//            attributeDTOList.forEach(attributeDTO -> {
//                if (bindMap.containsKey(attributeDTO.getId()) && Objects.nonNull(bindMap.get(attributeDTO.getId()))) {
//                    Integer required = bindMap.get(attributeDTO.getId()).getRequired();
//                    attributeDTO.setRequired(required);
//                }else {
//                    attributeDTO.setRequired(0);
//                }
//            });
//        }
//        return DataResponse.success(attributeDTOList);
//    }

}
