package com.jdi.isc.product.soa.service.manage.attribute.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.gms.crs.model.AttributeSettingResult;
import com.jd.gms.greatdane.category.domain.CategoryGroupAtt;
import com.jd.gms.greatdane.category.domain.CategoryGroupAttValue;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.attribute.common.AttributeDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeLangDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeValueDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeValueLangDTO;
import com.jdi.isc.product.soa.api.attribute.req.AttributeQueryReqDTO;
import com.jdi.isc.product.soa.api.attribute.res.AttributeFlatDTO;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.*;
import com.jdi.isc.product.soa.api.translate.req.BatchMultiTranslateReqDTO;
import com.jdi.isc.product.soa.api.translate.req.MultiTranslateReqDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.SaleAttributeTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.CommonDUCCConfig;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.domain.attribute.biz.*;
import com.jdi.isc.product.soa.domain.attribute.po.*;
import com.jdi.isc.product.soa.domain.category.po.CategoryAttributePO;
import com.jdi.isc.product.soa.domain.category.po.CategoryPO;
import com.jdi.isc.product.soa.domain.common.biz.BaseLangVO;
import com.jdi.isc.product.soa.domain.enums.RequiredEnum;
import com.jdi.isc.product.soa.domain.enums.StatusEnum;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;
import com.jdi.isc.product.soa.rpc.gms.CategoryRpcService;
import com.jdi.isc.product.soa.rpc.gms.MaterialRpcService;
import com.jdi.isc.product.soa.service.atomic.attribute.*;
import com.jdi.isc.product.soa.service.atomic.category.CategoryAtomicService;
import com.jdi.isc.product.soa.service.atomic.category.CategoryAttributeAtomicService;
import com.jdi.isc.product.soa.service.manage.attribute.AttributeOutService;
import com.jdi.isc.product.soa.service.manage.attribute.ExtAttributeLangService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeManageService;
import com.jdi.isc.product.soa.service.manage.translate.TextTranslateManageService;
import com.jdi.isc.product.soa.service.mapstruct.attribute.AttributeConvert;
import com.jdi.isc.product.soa.service.mapstruct.attribute.AttributeCountryConvert;
import com.jdi.isc.product.soa.service.mapstruct.attribute.AttributeValueLangConvert;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.Objects;

/**
 * @Description: 属性对外服务
 * @Author: zhaojianguo21
 * @Date: 2023/11/27 16:43
 **/
@Slf4j
@Service
public class AttributeOutServiceImpl extends BaseManageSupportService<AttributeVO, AttributePO> implements AttributeOutService {

    /**
     * 有效的scope值列表
     */
    private static final List<Integer> VALID_SCOPE_VALUES = Arrays.asList(null, 1, 3, 5, 7);

    @Resource
    private LangManageService langManageService;

    @Resource
    private TextTranslateManageService textTranslateManageService;

    @Resource
    private MaterialRpcService materialRpcService;

    @Resource
    private AttributeAtomicService attributeAtomicService;

    @Resource
    private AttributeLangAtomicService attributeLangAtomicService;

    @Resource
    private AttributeCountryAtomicService attributeCountryAtomicService;

    @Resource
    private AttributeValueAtomicService valueAtomicService;
    @Resource
    private AttributeValueLangAtomicService valueLangAtomicService;

    @Resource
    private CategoryAttributeAtomicService categoryAttributeAtomicService;

    @Resource
    private CommonDUCCConfig commonDUCCConfig;

    @Resource
    private CategoryRpcService categoryRpcService;

    @Resource
    private CountryManageService countryManageService;

    @Resource
    private CategoryAtomicService categoryAtomicService;

    @Resource
    private ExtAttributeLangService extAttributeLangService;

    @Resource
    private SaleAttributeManageService saleAttributeManageService;

    @LafValue("jdi.isc.product.soa.query.switch")
    private Boolean queryWordsSwitch;

    @LafValue("jdi.isc.product.soa.attribute.sell")
    private String sellAttribute;

    @Override
    public List<AttributeFlatVO> querySellAttr(String lang) {
        if(StringUtils.isBlank(lang)){
            lang = LangConstant.LANG_ZH;
        }

        List<AttributeVO> attributeVOS = this.querySellAttr();
        List<AttributeFlatVO> attributeFlatVOS = new ArrayList<>();
        for(AttributeVO attributeVO : attributeVOS){
            AttributeFlatVO attributeFlatVO = AttributeConvert.INSTANCE.attributeVo2FlatVo(attributeVO);
            attributeFlatVO.setRequired(RequiredEnum.REQUIRED.getCode());
            List<AttributeLangVO> attributeLangVOS  = attributeVO.getLangList();
            if(CollectionUtils.isEmpty(attributeLangVOS)){
                continue;
            }
            for(AttributeLangVO attributeLangVO : attributeLangVOS){
                if(lang.equals(attributeLangVO.getLang())){
                    attributeFlatVO.setAttributeName(attributeLangVO.getLangName());
                }
            }
            attributeFlatVOS.add(attributeFlatVO);
        }

        return attributeFlatVOS;
    }

    public List<AttributeVO> querySellAttr() {
        return JSONArray.parseArray(sellAttribute,AttributeVO.class);
    }
    @Override
    @PFTracing
    public List<AttributeFlatVO> querySellAttrDetail(Long categoryId, String lang) {

        // 1. 参数校验
        if (categoryId == null) {
            log.warn("AttributeOutServiceImpl.querySellAttrByCatId 类目ID为空");
            return Collections.emptyList();
        }

        // 2. 语言校验
        if(StringUtils.isBlank(lang)){
            log.warn("AttributeOutServiceImpl.querySellAttrByCatId 语言为空");
            return Collections.emptyList();
        }

        List<AttributeFlatVO> attributeFlatVOS = this.querySellAttrWithoutValue(categoryId,lang);
        if(CollectionUtils.isEmpty(attributeFlatVOS)){
            return Collections.emptyList();
        }
        // 设置默认属性值
        attributeFlatVOS.forEach(item -> {
            if (item != null) {
                List<AttributeValueFlatVO> attributeValueFlatVOS = new ArrayList<>();
                AttributeValueFlatVO attributeValueFlatVO = new AttributeValueFlatVO();
                attributeValueFlatVO.setLangList(new ArrayList<>());
                attributeValueFlatVO.setAttributeId(item.getId());
                // 根据销售属性类型设置默认属性值ID
                if(item.getAttributeInputType().intValue() == SaleAttributeTypeEnum.IMAGE.getCode().intValue()){
                    attributeValueFlatVO.setId(Constant.DEFAULT_IMAGE_SALE_ATTRIBUTE_VALUE_ID);
                } else if(item.getAttributeInputType().intValue() == SaleAttributeTypeEnum.TEXT.getCode().intValue()){
                    attributeValueFlatVO.setId(Constant.DEFAULT_TEXT_SALE_ATTRIBUTE_VALUE_ID);
                }
                // 设置属性值默认多语言
                Constant.DEFAULT_SALE_ATTRIBUTE_VALUE_NAME_LANG_MAP.forEach((langCode, langName) -> {
                    AttributeValueLangVO baseLangVO = new AttributeValueLangVO();
                    baseLangVO.setLang(langCode);
                    baseLangVO.setLangName(langName);
                    if (StringUtils.equals(langCode, lang)) {
                        attributeValueFlatVO.setLangName(langName);
                    }
                    attributeValueFlatVO.getLangList().add(baseLangVO);
                });
                attributeValueFlatVOS.add(attributeValueFlatVO);
                item.setAttributeValueList(attributeValueFlatVOS);
            }

        });
        return attributeFlatVOS;
    }

    @Deprecated
    public List<AttributeFlatVO> querySellAttrDetailOld(Long categoryId, String lang) {
        if (null == categoryId) {
            log.warn("param error. categoryId={}", categoryId);
            return Collections.emptyList();
        }
        CategoryAttrTypeEnum attrTypeEnum = CategoryAttrTypeEnum.SELL;
        lang = Optional.ofNullable(lang).orElse(LangConstant.LANG_ZH);
        // 查询绑定关系
        List<CategoryAttributePO> bindRelations = categoryAttributeAtomicService.getBoundAttr(categoryId, attrTypeEnum);
        if (CollectionUtils.isEmpty(bindRelations)) {
            log.info("BindRelations is empty. categoryId={}, attrTypeEnum={}", categoryId, attrTypeEnum);
            return Collections.emptyList();
        }
        log.info("BindRelations={}", JSON.toJSONString(bindRelations));
        // 获取返回值中catId与categoryId相等的对象
        bindRelations = bindRelations.stream().filter(Objects::nonNull)
                .filter(categoryAttributePO -> categoryAttributePO.getCatId().equals(categoryId))
                .collect(Collectors.toList());
        // 属性ID与绑定对象映射
        Map<Long/*属性ID*/, CategoryAttributePO> bindMap = bindRelations.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(CategoryAttributePO::getAttributeId, Function.identity(),
                        (existing, replacement) -> existing));
        // 所有属性ID集合
        Set<Long> attributeIds = bindMap.keySet();

        // 查询所有属性基本信息
        List<AttributeFlatVO> attributeFlatVOList = attributeAtomicService.queryAttrBaseInfo(attributeIds, lang);
        if (CollectionUtils.isEmpty(attributeFlatVOList)) {
            log.info("AttributeFlatVOList is empty. categoryId={}, attrTypeEnum={}", categoryId, attrTypeEnum);
            return Collections.emptyList();
        }

        // 查询适用范围
        List<AttributeCountryPO> countryPOList = attributeCountryAtomicService.queryCountryCodeByIds(attributeIds);
        log.info("CountryPOList={}", JSON.toJSONString(countryPOList));
        // 属性ID与适用范围映射
        Map<Long/*属性ID*/, List<AttributeCountryVO>> countryMap = null;
        if (CollectionUtils.isNotEmpty(countryPOList)) {
            List<AttributeCountryVO> countryVOList = AttributeCountryConvert.INSTANCE.listPo2dto(countryPOList);
            countryMap = countryVOList.stream().filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(AttributeCountryVO::getAttributeId));
        }

        // 获取属性和属性值映射
        Map<Long, List<AttributeValueFlatVO>> attrValueMap = this.getAttrValueMap(lang, attrTypeEnum, attributeFlatVOList);

        // 组装属性各字段值
        for (AttributeFlatVO flatVO : attributeFlatVOList) {
            if (MapUtils.isNotEmpty(countryMap) && null != countryMap.get(flatVO.getId())) {
                flatVO.setCountryList(countryMap.get(flatVO.getId()));
            }

            if (MapUtils.isNotEmpty(attrValueMap) && null != attrValueMap.get(flatVO.getId())) {
                flatVO.setAttributeValueList(attrValueMap.get(flatVO.getId()));
            }

            if (MapUtils.isNotEmpty(bindMap) && null != bindMap.get(flatVO.getId())) {
                flatVO.setSort(bindMap.get(flatVO.getId()).getSort());
                flatVO.setRequired(bindMap.get(flatVO.getId()).getRequired());
            }
            flatVO.setAttributeType(attrTypeEnum.getCode());
        }

        if (null!=commonDUCCConfig && Objects.equals(Boolean.TRUE,commonDUCCConfig.getLogDebugSwitch()) ){
            log.info("queryAttrDetail, attributeFlatVOList={}", JSONObject.toJSONString(attributeFlatVOList));
        }
        return attributeFlatVOList;

    }

    @Override
    public List<AttributeFlatVO> querySellAttrWithDefaultValue(Long categoryId, String lang) {
        return Collections.emptyList();
    }

    @Override
    public List<AttributeFlatVO> querySellAttrWithoutValue(Long categoryId, String lang) {
        log.info("AttributeOutServiceImpl.querySellAttrWithoutValue 开始查询类目销售属性(不包含属性值), categoryId: {}, lang: {}", categoryId, lang);
        
        // 1. 参数校验
        if (categoryId == null) {
            log.warn("AttributeOutServiceImpl.querySellAttrWithoutValue 类目ID为空");
            return Collections.emptyList();
        }
        
        // 2. 语言校验
        if(StringUtils.isBlank(lang)){
            log.warn("AttributeOutServiceImpl.querySellAttrWithoutValue 语言为空");
            return Collections.emptyList();
        }
        
        try {
            // 3. 查询销售属性列表
            List<PropertyVO> saleAttributeList = saleAttributeManageService.getCategorySaleAttributesByJdCatId(categoryId, lang);
            
            if (CollectionUtils.isEmpty(saleAttributeList)) {
                log.info("AttributeOutServiceImpl.querySellAttrWithoutValue 未查询到销售属性, categoryId: {}, lang: {}", categoryId, lang);
                return Collections.emptyList();
            }
            
            log.info("AttributeOutServiceImpl.querySellAttrWithoutValue 查询到销售属性: {}, categoryId: {}", 
                    JSON.toJSONString(saleAttributeList), categoryId);
            
            // 4. 对象转换
            List<AttributeFlatVO> result = saleAttributeList.stream()
                    .filter(Objects::nonNull)
                    .map(propertyVO -> {
                        try {
                            return AttributeConvert.INSTANCE.salePropertyVo2FlatVo(propertyVO);
                        } catch (Exception e) {
                            log.error("AttributeOutServiceImpl.querySellAttrWithoutValue 对象转换失败, propertyVO: {}", 
                                    JSON.toJSONString(propertyVO), e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            
            log.info("AttributeOutServiceImpl.querySellAttrWithoutValue 转换完成: {}, categoryId: {}", 
                    JSON.toJSONString(result), categoryId);
            
            return result;
            
        } catch (Exception e) {
            log.error("AttributeOutServiceImpl.querySellAttrWithoutValue 查询销售属性异常, categoryId: {}, lang: {}, error: {}", 
                    categoryId, lang, e.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public Set<Long> querySellAttrIds(Long categoryId) {
        if (null == categoryId) {
            log.warn("querySellAttrIds, param error. categoryId={}", categoryId);
            return Collections.emptySet();
        }
        CategoryAttrTypeEnum attrTypeEnum = CategoryAttrTypeEnum.SELL;
        // 查询绑定关系
        List<CategoryAttributePO> bindRelations = categoryAttributeAtomicService.getBoundAttr(categoryId, attrTypeEnum);
        if (CollectionUtils.isEmpty(bindRelations)) {
            log.info("querySellAttrIds, bindRelations is empty. categoryId={}, attrTypeEnum={}", categoryId, attrTypeEnum);
            return Collections.emptySet();
        }
        // 所有属性ID集合
        Set<Long> attributeIds = bindRelations.stream().filter(Objects::nonNull)
                        .map(CategoryAttributePO::getAttributeId).collect(Collectors.toSet());
        log.info("querySellAttrIds, attributeIds={}", JSON.toJSONString(attributeIds));

        return attributeIds;
    }
    @PFTracing
    private Map<Long, List<AttributeValueFlatVO>> getAttrValueMap(String lang, CategoryAttrTypeEnum attrTypeEnum, List<AttributeFlatVO> attributeFlatVOList) {
        List<AttributeValueFlatVO> valueFlatVOList = this.getAttributeValueFlatVOList(lang, attrTypeEnum, attributeFlatVOList);

        // 属性ID与属性值映射
        return Optional.of(valueFlatVOList)
                .orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(AttributeValueFlatVO::getAttributeId));
    }

    /**
     * 获取属性值扁平化视图列表
     *
     * @param lang                语言
     * @param attrTypeEnum        属性类型枚举
     * @param attributeFlatVOList 属性扁平化视图列表
     * @return 属性值扁平化视图列表
     */
    @PFTracing
    private List<AttributeValueFlatVO> getAttributeValueFlatVOList(String lang, CategoryAttrTypeEnum attrTypeEnum, List<AttributeFlatVO> attributeFlatVOList) {
        Set<Long> attributeIds = attributeFlatVOList.stream().map(AttributeFlatVO::getId).collect(Collectors.toSet());
        // 查属性值
        LambdaQueryWrapper<AttributeValuePO> wrapperValue = Wrappers.<AttributeValuePO>lambdaQuery()
                .select(AttributeValuePO::getId, AttributeValuePO::getAttributeId, AttributeValuePO::getSort)
                .in(AttributeValuePO::getAttributeId, attributeIds)
                .eq(AttributeValuePO::getStatus, StatusEnum.ENABLE.getCode())
                .eq(AttributeValuePO::getYn, YnEnum.YES.getCode())
                .orderByAsc(AttributeValuePO::getSort);
        List<AttributeValuePO> valuePOList = valueAtomicService.list(wrapperValue);
        log.info("ValuePOList={}", JSON.toJSONString(valuePOList));

        if (CollectionUtils.isEmpty(valuePOList)) {
            return Collections.emptyList();
        }

        // 遍历所有属性值
        // 查属性值多语言
        LambdaQueryWrapper<AttributeValueLangPO> wrapperValueLang = Wrappers.<AttributeValueLangPO>lambdaQuery()
                .select(AttributeValueLangPO::getAttributeValueId, AttributeValueLangPO::getLangName)
                .in(AttributeValueLangPO::getAttributeId, attributeIds)
                .eq(AttributeValueLangPO::getLang, lang)
                .eq(AttributeValueLangPO::getYn, YnEnum.YES.getCode());
        List<AttributeValueLangPO> valueLangPOList = valueLangAtomicService.list(wrapperValueLang);
        log.info("ValueLangPOList={}", JSON.toJSONString(valueLangPOList));
        // 属性值ID与属性值多语言映射
        Map<Long/*属性值ID*/, String/*属性值多语言名称*/> valueLangMap = Optional.ofNullable(valueLangPOList).orElseGet(ArrayList::new).stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(AttributeValueLangPO::getAttributeValueId, AttributeValueLangPO::getLangName));

        // 非中文查询，属性值为空时，补充中文属性值
        if (!LangConstant.LANG_ZH.equals(lang) && (CollectionUtils.isEmpty(valueLangPOList) ||  valuePOList.size() > valueLangMap.size())) {
            fillMissingValueLangMap(attributeIds, valueLangMap);
        }

        // 文本类型的属性ID
        Set<Long> attributeTextIds = attributeFlatVOList.stream()
                .filter(o -> CategoryAttrInputTypeEnum.TEXT.getCode().equals(o.getAttributeInputType()))
                .map(AttributeFlatVO::getId)
                .collect(Collectors.toSet());
        // 查文本类型的属性值多语言
        Map<Long/*属性值ID*/, List<AttributeValueLangPO>/*属性值多语言*/> textValueLangMap = null;
        // 扩展属性中文本类型的值，需要属性值的所有语言
        if (CategoryAttrTypeEnum.EXTEND.equals(attrTypeEnum) && CollectionUtils.isNotEmpty(attributeTextIds)) {
            // 查询文本类型属性值的所有语言
            LambdaQueryWrapper<AttributeValueLangPO> textValueLang = Wrappers.<AttributeValueLangPO>lambdaQuery()
                    .select(AttributeValueLangPO::getAttributeValueId, AttributeValueLangPO::getLang, AttributeValueLangPO::getLangName)
                    .in(AttributeValueLangPO::getAttributeId, attributeTextIds)
                    .eq(AttributeValueLangPO::getYn, YnEnum.YES.getCode());
            List<AttributeValueLangPO> textValueLangPOList = valueLangAtomicService.list(textValueLang);
            textValueLangMap = Optional.ofNullable(textValueLangPOList).orElseGet(ArrayList::new).stream().collect(Collectors.groupingBy(AttributeValueLangPO::getAttributeValueId));
        }

        // 属性值对象转换
        List<AttributeValueFlatVO> valueFlatVOList = new ArrayList<>(valuePOList.size());
        for (AttributeValuePO valuePO : valuePOList) {
            AttributeValueFlatVO valueFlatVO = new AttributeValueFlatVO();
            valueFlatVO.setId(valuePO.getId());
            valueFlatVO.setAttributeId(valuePO.getAttributeId());
            valueFlatVO.setSort(valuePO.getSort());
            // 给各属性值设置对应的多语言
            if (CategoryAttrTypeEnum.EXTEND.equals(attrTypeEnum) && CollectionUtils.isNotEmpty(attributeTextIds)
                    && attributeTextIds.contains(valuePO.getAttributeId()) && (MapUtils.isNotEmpty(textValueLangMap) && textValueLangMap.containsKey(valuePO.getId()))) {
                // 扩展属性且文本类型属性值 多语言设置
                valueFlatVO.setLangList(AttributeValueLangConvert.INSTANCE.listPo2dto(textValueLangMap.get(valuePO.getId())));
            } else if (MapUtils.isNotEmpty(valueLangMap)) {
                valueFlatVO.setLangName(valueLangMap.getOrDefault(valuePO.getId(), ""));
            }

            valueFlatVOList.add(valueFlatVO);
        }
        return valueFlatVOList;
    }

    /**
     * 填充缺失的属性值语言映射关系。
     * @param attributeIds 需要填充的属性ID集合。
     * @param valueLangMap 属性值语言映射关系。
     */
    private void fillMissingValueLangMap(Set<Long> attributeIds, Map<Long, String> valueLangMap) {
        LambdaQueryWrapper<AttributeValueLangPO> queryZhWrapperValueLang = Wrappers.<AttributeValueLangPO>lambdaQuery()
                .select(AttributeValueLangPO::getAttributeValueId, AttributeValueLangPO::getLangName)
                .in(AttributeValueLangPO::getAttributeId, attributeIds)
                .eq(AttributeValueLangPO::getLang, LangConstant.LANG_ZH)
                .eq(AttributeValueLangPO::getYn, YnEnum.YES.getCode());
        List<AttributeValueLangPO> valueZhLangPOList = valueLangAtomicService.list(queryZhWrapperValueLang);
        Map<Long, String> missingValueLangMap = Optional.ofNullable(valueZhLangPOList).orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull).filter(langPo -> !valueLangMap.containsKey(langPo.getAttributeValueId()))
                .collect(Collectors.toMap(AttributeValueLangPO::getAttributeValueId, AttributeValueLangPO::getLangName));
        if (MapUtils.isNotEmpty(missingValueLangMap)) {
            valueLangMap.putAll(missingValueLangMap);
        }
    }

//    public List<AttributeVO> queryAttributeVoByIds(Set<Long> attributeIds, List<String> langList) {
//        // 查询属性列表
//        List<AttributePO> attributePoList = attributeAtomicService.queryAttributePOSelfInfo(attributeIds);
//
//        // 查询属性多语言名称
//        LambdaQueryWrapper<AttributeLangPO> queryWrapper = new LambdaQueryWrapper<AttributeLangPO>()
//                .select(AttributeLangPO::getId, AttributeLangPO::getAttributeId, AttributeLangPO::getLang, AttributeLangPO::getLangName)
//                .in(AttributeLangPO::getAttributeId, attributeIds)
//                .in(CollectionUtils.isNotEmpty(langList),AttributeLangPO::getLang, langList)
//                .eq(AttributeLangPO::getYn, YnEnum.YES.getCode());
//        List<AttributeLangPO> attributeLangPoList = attributeLangAtomicService.list(queryWrapper);
//
//        Map<Long, List<AttributeLangPO>> attributeLangMap = Optional.ofNullable(attributeLangPoList).orElseGet(ArrayList::new)
//                .stream().collect(Collectors.groupingBy(AttributeLangPO::getAttributeId));
//
//        // 查询属性值并处理多语言名称
//        LambdaQueryWrapper<AttributeValueLangPO> wrapperValueLang = Wrappers.<AttributeValueLangPO>lambdaQuery()
//                .in(AttributeValueLangPO::getAttributeId, attributeIds)
//                .in(CollectionUtils.isNotEmpty(langList),AttributeValueLangPO::getLang, langList)
//                .eq(AttributeValueLangPO::getYn, YnEnum.YES.getCode());
//        List<AttributeValueLangPO> valueLangPoList = valueLangAtomicService.list(wrapperValueLang);
//
//        // 属性ID 、属性值ID -》属性语言的关系
//        Map<Long, Map<Long, List<AttributeValueLangPO>>> valueLangMap = Optional.ofNullable(valueLangPoList).orElseGet(ArrayList::new)
//                .stream().collect(Collectors.groupingBy(AttributeValueLangPO::getAttributeId, Collectors.groupingBy(AttributeValueLangPO::getAttributeValueId)));
//
//        // 查属性值
//        LambdaQueryWrapper<AttributeValuePO> wrapperValue = Wrappers.<AttributeValuePO>lambdaQuery()
//                .in(AttributeValuePO::getAttributeId, attributeIds)
//                .eq(AttributeValuePO::getYn, YnEnum.YES.getCode())
//                .orderByAsc(AttributeValuePO::getSort);
//        List<AttributeValuePO> valuePoList = valueAtomicService.list(wrapperValue);
//
//        Map<Long, List<AttributeValuePO>> attributeValueMap = Optional.ofNullable(valuePoList).orElseGet(ArrayList::new).stream()
//                .map(value -> {
//                    // 属性值多语言映射包含属性值多语言列表
//                    if (MapUtils.isNotEmpty(valueLangMap.get(value.getAttributeId())) && valueLangMap.get(value.getAttributeId()).containsKey(value.getId())) {
//                        value.setLangList(valueLangMap.get(value.getAttributeId()).get(value.getId()));
//                    }
//                    return value;
//                })
//                .collect(Collectors.groupingBy(AttributeValuePO::getAttributeId));
//
//        // 补充属性值和属性名多语言
//        Optional.ofNullable(attributePoList).orElseGet(ArrayList::new).forEach(po -> {
//            po.setAttributeValueList(attributeValueMap.get(po.getId()));
//            po.setLangList(attributeLangMap.get(po.getId()));
//        });
//
//        return AttributeConvert.INSTANCE.listPo2dto(attributePoList);
//    }

    @Override
    public List<AttributeVO> queryExtAttrDetail(Long categoryId, Set<String> langSet) {
        // 根据国际末级类目获取jdCatId
        CategoryPO categoryPO = categoryAtomicService.getValidPOById(categoryId);
        if(categoryPO == null){
            log.info("AttributeOutServiceImpl.queryExtAttrDetail1,categoryPO is null.categoryId:{}", categoryId);
            return Collections.emptyList();
        }
        Long jdCatId = categoryPO.getJdCatId();
        if(jdCatId == null){
            log.info("AttributeOutServiceImpl.queryExtAttrDetail1,categoryPO.jdSkuId is null.categoryId:{}", categoryId);
            return Collections.emptyList();
        }

        // 根据jdCatId获取扩展属性
        Map<Integer, CategoryGroupAtt> attMap = categoryRpcService.getCategoryGroupAttByCatId(jdCatId.intValue());
        if(MapUtils.isEmpty(attMap)){
            log.info("AttributeOutServiceImpl.queryExtAttrDetail1,attMap is empty.categoryId:{},jdCatId:{}", categoryId,jdCatId);
            return Collections.emptyList();
        }

        Set<Long> attrIds = attMap.values().stream().filter(Objects::nonNull).map(categoryGroupAtt -> Long.valueOf(categoryGroupAtt.getId().toString())).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(attrIds)){
            log.info("AttributeOutServiceImpl.queryExtAttrDetail1,attrIds is empty.categoryId:{},jdCatId:{}", categoryId,jdCatId);
            return Collections.emptyList();
        }

        // 根据属性ID获取属性值集合
        Map<Long, List<CategoryGroupAttValue>> attrValVoMap = categoryRpcService.queryCategoryGroupAttValueByAttMapId(attrIds);
        if(MapUtils.isEmpty(attrValVoMap)){
            log.info("AttributeOutServiceImpl.queryExtAttrDetail1,attrVoMap is empty.categoryId:{},jdCatId:{}", categoryId,jdCatId);
            return Collections.emptyList();
        }

        // 合法性过滤,根据是否包含属性值，输入类型是否正确进行过滤
        Map<Integer, CategoryGroupAtt> filterAttMap = new HashMap<>();
        Map<Long, List<CategoryGroupAttValue>> filterAttrValVoMap = new HashMap<>();
        attrValVoMap.forEach((k,v)->{
            Boolean isContainAttr = this.isContainAttr(k.intValue(),attMap,v);
            if(isContainAttr){
                filterAttMap.put(k.intValue(),attMap.get(k.intValue()));
                filterAttrValVoMap.put(k,v);
            }
        });

        if(MapUtils.isEmpty(filterAttMap) || MapUtils.isEmpty(filterAttrValVoMap)){
            log.info("AttributeOutServiceImpl.queryExtAttrDetail1,filterAttMap or filterAttrValVoMap is empty.categoryId:{},jdCatId:{}", categoryId,jdCatId);
            return Collections.emptyList();
        }

        // 获取返回的词条
        Set<String> waitingWords = this.getWaitingWords(filterAttMap,filterAttrValVoMap);

        // 获取翻译平台接口
        Map<String,Map<String,String>> wordMap = this.queryMapByWordsAndLangs(waitingWords,langSet);

        // 组装返回结果
        return this.convertToAttributeVO(filterAttMap,filterAttrValVoMap,wordMap);
    }

    /**
     * 获取京东SKU扩展属性列表
     */
    @Override
    public List<PropertyVO> obtainJDSkuExtAttributeList(Long catId, Long jdSkuId, String lang) {
        log.info("AttributeOutServiceImpl.obtainJDSkuExtAttributeList catId:{},jdSkuId:{}", catId,jdSkuId);
        List<PropertyVO> res = this.obtainExtAttributeList(catId, jdSkuId, lang);
        log.info("AttributeOutServiceImpl.obtainJDSkuExtAttributeList res:{}", JSON.toJSONString(res));
        return res;
    }

    @Override
    public List<PropertyVO> querySellAttrByCatId(Long categoryId, String lang) {
        log.info("AttributeOutServiceImpl.querySellAttrByCatId 开始查询类目销售属性(包含默认属性值), categoryId: {}, lang: {}", categoryId, lang);

        List<AttributeFlatVO> attributeFlatVOS = this.querySellAttrDetail(categoryId, lang);
        if(CollectionUtils.isEmpty(attributeFlatVOS)){
            return Collections.emptyList();
        }
        return attributeFlatVOS.stream().filter(Objects::nonNull).map(this::getSalePropertyVO).sorted(Comparator.comparing(PropertyVO::getSort)).collect(Collectors.toList());
    }

    /**
     * 将AttributeFlatVO转换为PropertyVO，并按sort字段排序属性值。同时需要返回默认属性值
     * @param extendFlatVo 属性扩展信息
     * @return 转换后的PropertyVO对象
     */
    private PropertyVO getSalePropertyVO(AttributeFlatVO extendFlatVo) {
        PropertyVO propertyVO = SpuConvert.INSTANCE.attributeFlatVo2PropertyVo(extendFlatVo);
        List<AttributeValueFlatVO> attributeValueFlatVOList = extendFlatVo.getAttributeValueList();

        List<PropertyValueVO> propertyValueVOList = Optional.ofNullable(attributeValueFlatVOList).orElseGet(ArrayList::new).stream().map(SpuConvert.INSTANCE::attributeFlatValueVo2PropertyValueVo).sorted(Comparator.comparing(PropertyValueVO::getSort)).collect(Collectors.toList());
        propertyVO.setPropertyValueVOList(propertyValueVOList);
        return propertyVO;
    }

    /**
     * 处理扩展属性
     * 设置extendPropertyList（全部属性）
     *
     * @param catId 类目ID
     * @param jdSkuId 京东SKU ID
     */
    private List<PropertyVO> obtainExtAttributeList(Long catId, Long jdSkuId, String lang){
        log.info("开始处理SKU扩展属性，jdSkuId: {}, catId: {}", jdSkuId, catId);

        try {

            if (jdSkuId == null || jdSkuId <= 0) {
                log.warn("京东SKU ID无效，跳过处理，jdSkuId: {}", jdSkuId);
                return Collections.emptyList();
            }

            if (catId == null || catId <= 0) {
                log.warn("类目ID无效，跳过处理，catId: {}", catId);
                return Collections.emptyList();
            }

            // 1. 获取语言类型列表
            List<String> langList = langManageService.getLangCodeList();
            if (CollectionUtils.isEmpty(langList)) {
                log.warn("语言列表为空，跳过处理");
                return Collections.emptyList();
            }
            log.info("获取到语言列表: {}", langList);

            // 2. 通过MaterialRpcService获取SKU的所有已存在扩展属性
            AttributeSettingResult attributeSettingResult = materialRpcService.getCateAttributeBySkuId(jdSkuId);
            log.info("AttributeOutServiceImpl.obtainExtAttributeList attributeSettingResult:{}", JSON.toJSONString(attributeSettingResult));
            if (attributeSettingResult == null || CollectionUtils.isEmpty(attributeSettingResult.getExpandGroups())) {
                log.warn("获取SKU扩展属性为空，jdSkuId: {}", jdSkuId);
                return Collections.emptyList();
            }

            // 3. 通过IscAttributeReadApiService读取catId下包含多语言信息的所有属性对象
            AttributeQueryReqDTO reqDTO = new AttributeQueryReqDTO();
            reqDTO.setCategoryId(Long.valueOf(catId));
            reqDTO.setAttributeTypeEnum(AttributeTypeEnum.EXTEND);
            reqDTO.setLangSet(new HashSet<>(langList));

            List<AttributeVO> allAttributes = this.queryExtAttrDetail(catId, reqDTO.getLangSet());
            if (CollectionUtils.isEmpty(allAttributes)) {
                log.warn("获取类目扩展属性为空，catId: {}", catId);
                // 设置空列表
                return Collections.emptyList();
            }


            // 4. 创建已存在属性值的映射（用于标记selected状态）
            Map<Long, Set<Long>> selectedAttributeValueMap = Maps.newHashMap();
            // 存储文本类型属性值，key：attributeID
            Map<Long, String> textAttributeValueMap = Maps.newHashMap();

            if (attributeSettingResult != null && CollectionUtils.isNotEmpty(attributeSettingResult.getExpandGroups())) {
                attributeSettingResult.getExpandGroups().forEach(group -> {
                    if (CollectionUtils.isNotEmpty(group.getAtts())) {
                        // 获取属组id，如果为null，则赋值为0
                        Integer groupId = group.getGroupId();
                        if (groupId == null) {
                            group.setGroupId(Constant.DEFAULT_ATTR_GROUP_ID);
                        }
                        group.getAtts().forEach(propAttribute -> {

                            Long attributeId = Long.valueOf(propAttribute.getAttId());
                            Set<Long> selectedValueIds = Sets.newHashSet();
                            if (CollectionUtils.isNotEmpty(propAttribute.getAttVals())) {
                                propAttribute.getAttVals().forEach(attrValue -> {
                                    selectedValueIds.add(Long.valueOf(attrValue.getAttValId()));
                                    if(propAttribute.getInputType() == AttributeInputTypeEnum.TEXT.getCode()){
                                        // 中台返回的文本类型属性值只会有一个值，即中文文本值，需要在后面获取多国语言的机翻文本值, KEY获取封装方法
                                        if(attributeId != null){
                                            textAttributeValueMap.put(attributeId, attrValue.getAttValName());
                                        }
                                    }
                                });
                            }
                            selectedAttributeValueMap.put(attributeId, selectedValueIds);
                        });
                    }
                });
            }

            // 5. 转换全部属性为PropertyVO列表（extendPropertyList）
            List<PropertyVO> extendPropertyList = convertAttributesToPropertyList(allAttributes, selectedAttributeValueMap, textAttributeValueMap, lang, langList);
            log.info("设置extendPropertyList，数量: {}", extendPropertyList.size());
            log.info("SKU扩展属性处理完成，jdSkuId: {}", jdSkuId);
            return extendPropertyList;
        } catch (Exception e) {
            log.error("处理SKU扩展属性时发生异常，jdSkuId: {}, catId: {}", jdSkuId, catId, e);
        }
        return Lists.newArrayList();
    }

    /**
     * 将AttributeDTO列表转换为PropertyVO列表
     */
    private List<PropertyVO> convertAttributesToPropertyList(List<AttributeVO>  attributeDTOList,
                                                             Map<Long, Set<Long>> selectedAttributeValueMap,
                                                             Map<Long, String> textAttributeValueMap,
                                                             String concurrentLang,List<String> langList) {
        if (CollectionUtils.isEmpty(attributeDTOList)) {
            return Lists.newArrayList();
        }

        return attributeDTOList.stream().map(attributeDTO -> {
            PropertyVO propertyVO = new PropertyVO();

            // 基本属性
            Long attributeId = Long.valueOf(attributeDTO.getId());
            propertyVO.setAttributeId(attributeId);

            // 获取默认语言属性名称
            String defaultLangName = attributeDTO.getLangList().stream()
                    .filter(e -> e.getLang().equals(Constant.DEFAULT_LANG))
                    .findFirst()
                    .map(AttributeLangVO::getLangName)
                    .orElse("");
            // 获取当前语言属性名称，如果当前语言不存在，则使用默认语言
            propertyVO.setAttributeName(attributeDTO.getLangList().stream()
                    .filter(e -> e.getLang().equals(concurrentLang))
                    .findFirst()
                    .map(AttributeLangVO::getLangName)
                    .orElse(defaultLangName));

            propertyVO.setAttributeType(attributeDTO.getAttributeType());
            propertyVO.setAttributeInputType(attributeDTO.getAttributeInputType());
            propertyVO.setLevel(attributeDTO.getLevel());
            propertyVO.setSort(attributeDTO.getSort());
            propertyVO.setComGroupId(attributeDTO.getComGroupId());

            // 属性组名称
            if (MapUtils.isNotEmpty(attributeDTO.getLangComGroupNameMap())) {
                // 使用当前语言属性组名称，如果当前语言不存在，则使用默认语言
                String defaultComGroupName = attributeDTO.getLangComGroupNameMap().get(Constant.DEFAULT_LANG);
                String comGroupName = attributeDTO.getLangComGroupNameMap().get(concurrentLang);
                if (StringUtils.isBlank(comGroupName)) {
                    comGroupName = defaultComGroupName;
                }
                propertyVO.setComGroupName(comGroupName);
            }

            // 转换属性值列表
            List<PropertyValueVO> propertyValueList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(attributeDTO.getAttributeValueList())) {
                Set<Long> selectedValueIds = selectedAttributeValueMap.getOrDefault(attributeId, Sets.newHashSet());
                Map<String, Map<String, String>> translateValueMap = obtainTranslateMap(textAttributeValueMap, langList);
                for (AttributeValueVO attributeValueDTO : attributeDTO.getAttributeValueList()) {
                    // 如果属性类型是文本，则所有语言都要有值，即7个value，如果不是文本，则只有当前对应当前语言的1个value
                    if (attributeDTO.getAttributeInputType() == AttributeInputTypeEnum.TEXT.getCode()) {
                        // 获取翻译，for循环赋值给翻译个数的propertyValueVO
                        if(translateValueMap != null && MapUtils.isNotEmpty(translateValueMap)){
                            Map<String,String> valueMap = translateValueMap.get(textAttributeValueMap.get(attributeId));
                            log.info("获取翻译的key: {}, valueMap: {}", textAttributeValueMap.get(attributeId) , JSON.toJSONString(valueMap));
                            if(MapUtils.isNotEmpty(valueMap)){
                            valueMap.forEach((key, value) -> {
                                PropertyValueVO propertyValueVO = new PropertyValueVO();
                                propertyValueVO.setAttributeId(attributeId);
                                propertyValueVO.setAttributeValueId(attributeValueDTO.getId());
                                // 这个地方的属性值需要在textAttributeValueMap中获取，然后获取翻译
                                propertyValueVO.setAttributeValueName(value);
                                propertyValueVO.setLang(key);
                                propertyValueVO.setLangName(value);
                                propertyValueVO.setSelected(true);
                                propertyValueList.add(propertyValueVO);
                                });
                            }
                        }
                    } else{
                        PropertyValueVO propertyValueVO = new PropertyValueVO();
                        Long valueId = attributeValueDTO.getId();
                        propertyValueVO.setAttributeId(attributeId);
                        propertyValueVO.setAttributeValueId(valueId);
                        // 获取默认语言属性值名称
                        String defaultAttName = attributeValueDTO.getLangList().stream()
                                .filter(e -> e.getLang().equals(Constant.DEFAULT_LANG))
                                .findFirst()
                                .map(AttributeValueLangVO::getLangName)
                                .orElse("");
                        // 在langList中获取当前语言的属性值名称，如果当前语言不存在，则使用默认语言
                        String attName = attributeValueDTO.getLangList().stream()
                                .filter(e -> e.getLang().equals(concurrentLang))
                                .findFirst()
                                .map(AttributeValueLangVO::getLangName)
                                .filter(e->StringUtils.isNotBlank(e))
                                .orElse(defaultAttName);

                        propertyValueVO.setAttributeValueName(attName);
                        propertyValueVO.setSort(attributeValueDTO.getSort());

                        // 根据是否存在于expandGroups中设置选中状态
                        propertyValueVO.setSelected(selectedValueIds.contains(valueId));
                        propertyValueList.add(propertyValueVO);
                    }
                }
            }

            propertyVO.setPropertyValueVOList(propertyValueList);
            return propertyVO;

        }).collect(Collectors.toList());
    }

    private Map<String, Map<String, String>> obtainTranslateMap(Map<Long, String> textAttributeValueMap, List<String> langList) {
        if(MapUtils.isEmpty(textAttributeValueMap) || CollectionUtils.isEmpty(langList) || CollectionUtils.isEmpty(textAttributeValueMap.values())){
            return Maps.newHashMap();
        }
        // 获取所有文本类型的属性值，然后进行翻译
        List<String> textValueList = textAttributeValueMap.values().stream().collect(Collectors.toList());
        // 默认翻译平台
        TranslatePlatEnum xiangJi = TranslatePlatEnum.XIANG_JI;
        BatchMultiTranslateReqDTO batchMultiTranslateReqDTO = new BatchMultiTranslateReqDTO();
        batchMultiTranslateReqDTO.setPlat(xiangJi.getCode());
        // 设置翻译请求的具体内容，包括文本内容和目标语言
        Set<MultiTranslateReqDTO> multiTranslateReqDTOSet = Sets.newHashSet();
        for (String textValue : textValueList) {
            MultiTranslateReqDTO multiTranslateReqDTO = new MultiTranslateReqDTO();
            multiTranslateReqDTO.setText(textValue);
            multiTranslateReqDTO.setToLangList(langList);
            multiTranslateReqDTO.setPlat(xiangJi.getCode());
            multiTranslateReqDTOSet.add(multiTranslateReqDTO);
        }
        batchMultiTranslateReqDTO.setMultiTranslateReqDTOSet(multiTranslateReqDTOSet);
        // 翻译, 返回值格式为text:lang:value
        DataResponse<Map<String,Map<String,String>>> translateValueMapResponse = textTranslateManageService.batchTranslateMultiLangText(batchMultiTranslateReqDTO);
        Map<String,Map<String,String>> translateValueMap = Maps.newHashMap();
        if(translateValueMapResponse.getSuccess()){
            translateValueMap = translateValueMapResponse.getData();
            log.info("多语言查询入参multiTranslateReqDTOSet: {}, 返回值translateValueMap: {}", JSON.toJSONString(multiTranslateReqDTOSet), JSON.toJSONString(translateValueMap));
        }
        return translateValueMap;
    }

    @Override
    @PFTracing
    public List<AttributeFlatVO> queryExtAttrDetail(Long categoryId, String lang) {

        // 根据国际末级类目获取jdCatId   TODO 后续直接用零售id，这个可以删除
        CategoryPO categoryPO = categoryAtomicService.getValidPOById(categoryId);
        if(categoryPO == null){
            log.info("AttributeOutServiceImpl.queryExtAttrDetail,categoryPO is null.categoryId:{}", categoryId);
            return Collections.emptyList();
        }
        Long jdCatId = categoryPO.getJdCatId();
        if(jdCatId == null){
            log.info("AttributeOutServiceImpl.queryExtAttrDetail,categoryPO.jdSkuId is null.categoryId:{}", categoryId);
            return Collections.emptyList();
        }

        // 根据jdCatId获取扩展属性
        Map<Integer, CategoryGroupAtt> attMap = categoryRpcService.getCategoryGroupAttByCatId(jdCatId.intValue());
        if(MapUtils.isEmpty(attMap)){
            log.info("AttributeOutServiceImpl.queryExtAttrDetail,attMap is empty.categoryId:{},jdCatId:{}", categoryId,jdCatId);
            return Collections.emptyList();
        }

        Set<Long> attrIds = attMap.values().stream().filter(Objects::nonNull).map(categoryGroupAtt -> Long.valueOf(categoryGroupAtt.getId().toString())).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(attrIds)){
            log.info("AttributeOutServiceImpl.queryExtAttrDetail,attrIds is empty.categoryId:{},jdCatId:{}", categoryId,jdCatId);
            return Collections.emptyList();
        }

        // 根据属性ID获取属性值集合
        Map<Long, List<CategoryGroupAttValue>> attrValVoMap = categoryRpcService.queryCategoryGroupAttValueByAttMapId(attrIds);
        if(MapUtils.isEmpty(attrValVoMap)){
            log.info("AttributeOutServiceImpl.queryExtAttrDetail,attrVoMap is empty.categoryId:{},jdCatId:{}", categoryId,jdCatId);
            return Collections.emptyList();
        }

        // 合法性过滤,根据是否包含属性值，输入类型是否正确进行过滤
        Map<Integer, CategoryGroupAtt> filterAttMap = new HashMap<>();
        Map<Long, List<CategoryGroupAttValue>> filterAttrValVoMap = new HashMap<>();
        attrValVoMap.forEach((k,v)->{
            Boolean isContainAttr = this.isContainAttr(k.intValue(),attMap,v);
            if(isContainAttr){
                filterAttMap.put(k.intValue(),attMap.get(k.intValue()));
                filterAttrValVoMap.put(k,v);
            }
        });

        if(MapUtils.isEmpty(filterAttMap) || MapUtils.isEmpty(filterAttrValVoMap)){
            log.info("AttributeOutServiceImpl.queryExtAttrDetail,filterAttMap or filterAttrValVoMap is empty.categoryId:{},jdCatId:{}", categoryId,jdCatId);
            return Collections.emptyList();
        }

        // 获取需要翻译的词条，词条为所有属性的名字和属性值的名字，新增属性组名称
        Set<String> waitingWords = this.getWaitingWords(filterAttMap,filterAttrValVoMap);

        // 获取翻译平台接口，    TODO 属性组名称问题处理，需要重构底层实现，主要是ExtAttributeLangPO extAttributeName，这个名称需要改成通用名称，因为它包含属性名称、属性值名称和属性组名称，不只是属性名称
        Map<String,Map<String,String>> wordMap = this.queryMapByWordsAndLangs(waitingWords,Sets.newHashSet(lang));

        // 组装返回结果，在返回对象中增加属性组id和name，属性组名称已经翻译
        return this.convertToAttributeFlatVO(filterAttMap,filterAttrValVoMap,wordMap,lang);
    }

    /**
     * 判断指定的key在attMap中是否存在对应的CategoryGroupAtt对象，并且该对象的inputType属性是否为有效的枚举值。属性范围scope需要为自营   TODO ，增加区间属性
     * @param key 要查找的key值
     * @param attMap 存储CategoryGroupAtt对象的Map集合
     * @return 如果指定的key在attMap中存在对应的CategoryGroupAtt对象，并且该对象的inputType属性为有效的枚举值，则返回true；否则返回false。
     */
    @PFTracing
    private boolean isContainAttr(Integer key, Map<Integer, CategoryGroupAtt> attMap,List<CategoryGroupAttValue> attrValueList) {
        CategoryGroupAtt categoryGroupAtt = attMap.get(key);
        if (categoryGroupAtt == null) {
            return Boolean.FALSE;
        }

        // 只过滤展示适用范围含自营的属性
        // scope: 1自营 2vc 4pop，例如：3=1+2 自营+vc 7=1+2+4 全支持。所以包含自营的范围为1、3、5、7
        // 检查scope属性，只有当scope值在有效列表中时才继续处理
        Integer scope = categoryGroupAtt.getScope();
        if (!VALID_SCOPE_VALUES.contains(scope)) {
            log.warn("属性scope值不符合要求，属性ID：{}，scope值：{}", key, scope);
            return Boolean.FALSE;
        }

        CategoryAttrInputTypeEnum categoryAttrInputTypeEnum = CategoryAttrInputTypeEnum.forCodeFromAttr(categoryGroupAtt.getInputType());
        if(categoryAttrInputTypeEnum == null){
            return Boolean.FALSE;
        }
        // todo 需要产品出方案，目前批量发品不支持多选
        if(CategoryAttrInputTypeEnum.CHECKBOX == categoryAttrInputTypeEnum){
            return Boolean.FALSE;
        }
        if(CategoryAttrInputTypeEnum.TEXT != categoryAttrInputTypeEnum){
            if(CollectionUtils.isEmpty(attrValueList)){
                return Boolean.FALSE;
            }
            attrValueList = attrValueList.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(attrValueList)){
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 将属性值映射到AttributeFlatVO对象列表中
     * @param attMap 属性组与属性的映射关系
     * @param attrValueMap 属性值的映射关系
     * @return AttributeFlatVO对象列表
     */
    @PFTracing
    private List<AttributeFlatVO> convertToAttributeFlatVO(Map<Integer, CategoryGroupAtt> attMap, Map<Long, List<CategoryGroupAttValue>> attrValueMap
            ,Map<String,Map<String,String>> wordMap,String lang) {
        List<String> countryCodes = countryManageService.getCountryCodeList();
        List<AttributeFlatVO> attributeFlatVOS = new ArrayList<>();

        attrValueMap.forEach((k, attValues) -> {
            CategoryGroupAtt categoryGroupAtt = attMap.get(k.intValue());
            CategoryAttrInputTypeEnum categoryAttrInputTypeEnum = CategoryAttrInputTypeEnum.forCodeFromAttr(categoryGroupAtt.getInputType());
            if (CategoryAttrInputTypeEnum.TEXT != categoryAttrInputTypeEnum && CollectionUtils.isEmpty(attValues)) {
                return;
            }
            AttributeFlatVO attributeFlatVO = this.getAttributeFlatVO(categoryGroupAtt, countryCodes ,wordMap,lang);
            if(CollectionUtils.isNotEmpty(attValues)){
                List<AttributeValueFlatVO> attributeValueFlatVOS = attValues.stream()
                        .filter(Objects::nonNull)
                        .map(item -> this.createAttributeValueFlatVO(attributeFlatVO, item,wordMap,lang))
                        .sorted(Comparator.comparing(AttributeValueFlatVO::getSort)
                                .thenComparing(AttributeValueFlatVO::getId))
                        .collect(Collectors.toList());
                attributeFlatVO.setAttributeValueList(attributeValueFlatVOS);
            } else {
                List<AttributeValueFlatVO> attributeValueFlatVOS = new ArrayList<>();
                AttributeValueFlatVO attributeValueFlatVO = new AttributeValueFlatVO();
                attributeValueFlatVO.setAttributeId(attributeFlatVO.getId());
                attributeValueFlatVO.setId((long) YesOrNoEnum.YES.getCode());
                attributeValueFlatVO.setStatus(YesOrNoEnum.YES.getCode());
                attributeValueFlatVO.setSort(YesOrNoEnum.YES.getCode());

                attributeValueFlatVO.setLangList(Lists.newArrayList());
                attributeValueFlatVOS.add(attributeValueFlatVO);
                attributeFlatVO.setAttributeValueList(attributeValueFlatVOS);
            }
            attributeFlatVOS.add(attributeFlatVO);
        });

        if(CollectionUtils.isNotEmpty(attributeFlatVOS)){
            List<Long> attributeIds = attributeFlatVOS.stream().map(AttributeFlatVO::getId).collect(Collectors.toList());
            Set<Long> duplicates = this.findDuplicates(attributeIds);
            if (!duplicates.isEmpty()) {
                attributeFlatVOS.removeIf(item ->
                        duplicates.contains(item.getId()) &&
                                CategoryAttrInputTypeEnum.forCode(item.getAttributeInputType()) != CategoryAttrInputTypeEnum.TEXT);
            }
        }

        return attributeFlatVOS.stream()
                .sorted(Comparator.comparing(AttributeFlatVO::getSort)
                        .thenComparing(AttributeFlatVO::getId))
                .collect(Collectors.toList());
    }

    /**
     * 创建属性值平面VO对象。
     * @param attributeFlatVO 属性。
     * @param item 属性值信息。
     * @return 创建的属性值平面VO对象。
     */
    @PFTracing
    private AttributeValueFlatVO createAttributeValueFlatVO(AttributeFlatVO attributeFlatVO, CategoryGroupAttValue item
            ,Map<String,Map<String,String>> wordMap,String lang) {
        AttributeValueFlatVO attributeValueFlatVO = new AttributeValueFlatVO();
        attributeValueFlatVO.setAttributeId(attributeFlatVO.getId());
        attributeValueFlatVO.setId(Long.valueOf(item.getComAttValueId()));
        attributeValueFlatVO.setStatus(YesOrNoEnum.YES.getCode());
        attributeValueFlatVO.setSort(item.getOrderSort()!=null?item.getOrderSort():YesOrNoEnum.YES.getCode());

        String attributeValueName = item.getName();
        Map<String,String> langNameMap = wordMap.get(attributeValueName);
        if(StringUtils.isNotBlank(langNameMap.get(lang))){
            attributeValueName = langNameMap.get(lang);
        }
        attributeValueFlatVO.setLangName(attributeValueName);

        List<AttributeValueLangVO> attributeValueLangVOS = Lists.newArrayList();
        langNameMap.forEach((k,v)->{
            AttributeValueLangVO attributeValueLangVO = new AttributeValueLangVO();
            attributeValueLangVO.setAttributeId(attributeFlatVO.getId());
            attributeValueLangVO.setAttributeValueId(Long.valueOf(item.getComAttValueId()));
            attributeValueLangVO.setLang(k);
            attributeValueLangVO.setLangName(v);
            attributeValueLangVOS.add(attributeValueLangVO);
        });
        attributeValueFlatVO.setLangList(attributeValueLangVOS);

        return attributeValueFlatVO;
    }

    /**
     * 获取需要翻译的词条
     * */
    @PFTracing
    private Set<String> getWaitingWords(Map<Integer, CategoryGroupAtt> filterAttMap,Map<Long, List<CategoryGroupAttValue>> filterAttrValVoMap){
        Set<String> waitingWords = new HashSet<>();
        filterAttMap.forEach((k,v)->{
            if(Objects.isNull(v)){
                return;
            }
            String attrName = v.getName();
            if(StringUtils.isNotBlank(attrName)){
                waitingWords.add(attrName);
            }
            // 增加属性组名称
            String groupName = v.getComGroupName();
            if(StringUtils.isNotBlank(groupName)){
                waitingWords.add(groupName);
            }
        });
        filterAttrValVoMap.forEach((k,v)->{
            if(CollectionUtils.isEmpty(v)){
                return;
            }
            v.forEach(item->{
                if(Objects.isNull(item) || StringUtils.isBlank(item.getName())){
                    return;
                }
                waitingWords.add(item.getName());
            });
        });
        return waitingWords;
    }

    /**
     * 将词条翻译成对应的语种
     * @return {"电压":{"zh":"电压","en":"magic"}}
     * */
    @PFTracing
    private Map<String,Map<String,String>> queryMapByWordsAndLangs(Set<String> waitingWords,Set<String> langSet){
        if(CollectionUtils.isEmpty(waitingWords)){
            return Collections.emptyMap();
        }
        log.info("AttributeOutServiceImpl.queryMapByWordsAndLangs waitingWords:{}",JSONObject.toJSONString(waitingWords));

        Map<String,Map<String,String>> resultMap = new HashMap<>();
        Map<String, Map<String,String>> extAttributeLangNameMap = new HashMap<>();
        if(queryWordsSwitch){
            extAttributeLangNameMap = extAttributeLangService.getExtAttrLangMapByCnName(waitingWords, langSet);
        }
        Set<String> notExist = Sets.newHashSet();
        for(String word : waitingWords){
            if(MapUtils.isEmpty(extAttributeLangNameMap)){
                Map<String, String> innerMap = new HashMap<>();
                innerMap.put(LangConstant.LANG_ZH, word);
                resultMap.put(word, innerMap);
                continue;
            }
            Map<String,String> langNameMap = extAttributeLangNameMap.get(word);
            if(MapUtils.isEmpty(langNameMap)){
                Map<String, String> innerMap = new HashMap<>();
                innerMap.put(LangConstant.LANG_ZH, word);
                resultMap.put(word, innerMap);
                notExist.add(word);
                continue;
            }
            if(!langNameMap.containsKey(LangConstant.LANG_ZH) || StringUtils.isBlank(langNameMap.get(LangConstant.LANG_ZH))){
                langNameMap.put(LangConstant.LANG_ZH, word);
                resultMap.put(word, langNameMap);
                continue;
            }
            resultMap.put(word,langNameMap);
        }
        if(CollectionUtils.isNotEmpty(notExist)){
            log.warn("AttributeOutServiceImpl.queryMapByWordsAndLangs not exist in ext attribute lang map, waiting words:{}",JSONObject.toJSONString(notExist));
        }

        if(MapUtils.isNotEmpty(extAttributeLangNameMap)){
            Set<String> extWords = extAttributeLangNameMap.keySet();
            Set<String> extraWords = new HashSet<>(extWords);
            extraWords.removeAll(waitingWords);
            if (CollectionUtils.isNotEmpty(extraWords)) {
                log.warn("AttributeOutServiceImpl.queryMapByWordsAndLangs extra words in ext attribute lang map: {}", JSONObject.toJSONString(extraWords));
            }
        }

        return resultMap;
    }

    /**
     * 根据CategoryGroupAtt对象和国家代码列表，生成AttributeFlatVO对象。
     * @param categoryGroupAtt CategoryGroupAtt对象，包含属性名称、ID、输入类型、排序等信息。
     * @param countryCodes 国家代码列表，用于设置属性的适用国家。
     * @return AttributeFlatVO对象，包含属性的详细信息。
     */
    @PFTracing
    private AttributeFlatVO getAttributeFlatVO(CategoryGroupAtt categoryGroupAtt,List<String> countryCodes
            ,Map<String,Map<String,String>> wordMap,String lang){
        AttributeFlatVO attributeFlatVO = new AttributeFlatVO();
        attributeFlatVO.setId(Long.valueOf(categoryGroupAtt.getComAttId()));
        attributeFlatVO.setAttributeInputType(CategoryAttrInputTypeEnum.forCodeFromAttr(categoryGroupAtt.getInputType()).getCode());
        attributeFlatVO.setAttributeType(CategoryAttrTypeEnum.EXTEND.getCode());
        attributeFlatVO.setSort(categoryGroupAtt.getOrderSort() == null ? 1 : categoryGroupAtt.getOrderSort());
        attributeFlatVO.setCountryList(this.setCountryVos(Long.valueOf(categoryGroupAtt.getComAttId()),countryCodes));
        attributeFlatVO.setStatus(YesOrNoEnum.YES.getCode());

        // 设置属性组ID
        attributeFlatVO.setComGroupId(categoryGroupAtt.getComGroupId());
        // 设置属性组名称, 获取对应语音的翻译，兜底是默认中文
        String comGroupName = categoryGroupAtt.getComGroupName();
        Map<String,String> langNameMap = wordMap.get(comGroupName);
        log.info("AttributeOutServiceImpl.getAttributeFlatVO comGroupName={},langNameMap={}", comGroupName, JSON.toJSONString(langNameMap));
        if(langNameMap != null && StringUtils.isNotBlank(langNameMap.get(lang))){
            comGroupName = langNameMap.get(lang);
        }
        attributeFlatVO.setComGroupName(comGroupName);
        // 设置属性级别
        attributeFlatVO.setLevel(categoryGroupAtt.getLevel());
        // 设置隐藏属性
        attributeFlatVO.setShield(categoryGroupAtt.getShield());
        // 设置区间属性
        if(categoryGroupAtt.getFeatures()!=null){
            attributeFlatVO.setIsQuJianZhi(categoryGroupAtt.getFeatures().get(AttributeFlatVO.IS_QUJIANZHI_KEY));
        }

        String attributeName = categoryGroupAtt.getName();
        langNameMap = wordMap.get(categoryGroupAtt.getName());
        if(langNameMap != null && StringUtils.isNotBlank(langNameMap.get(lang))){
            attributeName = langNameMap.get(lang);
        }
        attributeFlatVO.setAttributeName(attributeName);

        return attributeFlatVO;
    }

    /**
     * 根据属性ID和国家代码列表，设置国家适用范围
     * @param attributeId 属性ID
     * @param countryCodes 国家代码列表
     * @return AttributeCountryVO对象列表
     */
    private List<AttributeCountryVO> setCountryVos(Long attributeId,List<String> countryCodes){
        List<AttributeCountryVO> attributeCountryVOS = new ArrayList<>();
        countryCodes.forEach(countryCode -> {
            AttributeCountryVO attributeCountryVO = new AttributeCountryVO();
            attributeCountryVO.setAttributeId(attributeId);
            attributeCountryVO.setCountryCode(countryCode);
            attributeCountryVOS.add(attributeCountryVO);
        });
        return attributeCountryVOS;
    }

    /**
     * 将属性值映射到AttributeFlatVO对象列表中
     * @param attMap 属性组与属性的映射关系
     * @param attrValueMap 属性值的映射关系
     * @return AttributeFlatVO对象列表
     */
    private List<AttributeVO> convertToAttributeVO(Map<Integer, CategoryGroupAtt> attMap, Map<Long, List<CategoryGroupAttValue>> attrValueMap
            ,Map<String,Map<String,String>> wordMap) {
        List<String> countryCodes = countryManageService.getCountryCodeList();
        List<AttributeVO> attributeVOS = new ArrayList<>();

        attrValueMap.forEach((k, attValues) -> {
            CategoryGroupAtt categoryGroupAtt = attMap.get(k.intValue());
            CategoryAttrInputTypeEnum categoryAttrInputTypeEnum = CategoryAttrInputTypeEnum.forCodeFromAttr(categoryGroupAtt.getInputType());
            if (CategoryAttrInputTypeEnum.TEXT != categoryAttrInputTypeEnum && CollectionUtils.isEmpty(attValues)) {
                return;
            }

            AttributeVO attributeVO = this.getAttributeVO(categoryGroupAtt,countryCodes,wordMap);

            if(CollectionUtils.isNotEmpty(attValues)){
                List<AttributeValueVO> attributeValueFlatVOS = attValues.stream()
                        .filter(Objects::nonNull)
                        .map(item -> this.createAttributeValueVO(attributeVO.getId(), item ,wordMap))
                        .sorted(Comparator.comparing(AttributeValueVO::getSort)
                                .thenComparing(AttributeValueVO::getId))
                        .collect(Collectors.toList());
                attributeVO.setAttributeValueList(attributeValueFlatVOS);
            } else {
                List<AttributeValueVO> attributeValueFlatVOS = new ArrayList<>();
                AttributeValueVO attributeValueVO = new AttributeValueVO();
                attributeValueVO.setAttributeId(attributeVO.getId());
                attributeValueVO.setId((long) YesOrNoEnum.YES.getCode());
                attributeValueVO.setStatus(YesOrNoEnum.YES.getCode());
                attributeValueVO.setSort(YesOrNoEnum.YES.getCode());

                attributeValueVO.setLangList(Lists.newArrayList());
                attributeValueFlatVOS.add(attributeValueVO);
                attributeVO.setAttributeValueList(attributeValueFlatVOS);
            }
            attributeVOS.add(attributeVO);
        });

        if(CollectionUtils.isNotEmpty(attributeVOS)){
            List<Long> attributeIds = attributeVOS.stream().map(AttributeVO::getId).collect(Collectors.toList());
            Set<Long> duplicates = this.findDuplicates(attributeIds);
            if (!duplicates.isEmpty()) {
                attributeVOS.removeIf(item ->
                        duplicates.contains(item.getId()) &&
                                CategoryAttrInputTypeEnum.forCode(item.getAttributeInputType()) != CategoryAttrInputTypeEnum.TEXT);
            }
        }

        return attributeVOS.stream()
                .sorted(Comparator.comparing(AttributeVO::getSort)
                        .thenComparing(AttributeVO::getId))
                .collect(Collectors.toList());
    }

    /**
     * 根据CategoryGroupAtt对象和国家代码列表生成AttributeVO对象。
     * @param categoryGroupAtt CategoryGroupAtt对象，包含属性的基本信息。
     * @param countryCodes 国家代码列表，用于设置属性的可用国家。
     * @return 生成的AttributeVO对象。
     */
    private AttributeVO getAttributeVO(CategoryGroupAtt categoryGroupAtt,List<String> countryCodes,Map<String,Map<String,String>> wordMap){
        AttributeVO attributeVO = new AttributeVO();
        attributeVO.setId(Long.valueOf(categoryGroupAtt.getComAttId()));
        attributeVO.setAttributeType(CategoryAttrTypeEnum.EXTEND.getCode());
        attributeVO.setAttributeInputType(CategoryAttrInputTypeEnum.forCodeFromAttr(categoryGroupAtt.getInputType()).getCode());
        attributeVO.setSort(categoryGroupAtt.getOrderSort() == null ? 1 : categoryGroupAtt.getOrderSort());
        attributeVO.setCountryList(this.setCountryVos(Long.valueOf(categoryGroupAtt.getComAttId()),countryCodes));
        attributeVO.setStatus(YesOrNoEnum.YES.getCode());

        // 设置属性组ID
        attributeVO.setComGroupId(categoryGroupAtt.getComGroupId());

        // 设置属性组名对应语音的全部翻译
        String comGroupName = categoryGroupAtt.getComGroupName();
        Map<String,String> langNameMap = wordMap.get(comGroupName);
        attributeVO.setLangComGroupNameMap(langNameMap);
        // 设置属性级别
        attributeVO.setLevel(categoryGroupAtt.getLevel());
        // 设置隐藏属性
        attributeVO.setShield(categoryGroupAtt.getShield());
        // 设置区间属性
        if(categoryGroupAtt.getFeatures()!=null){
            attributeVO.setIsQuJianZhi(categoryGroupAtt.getFeatures().get(AttributeFlatVO.IS_QUJIANZHI_KEY));
        }

        if(MapUtils.isNotEmpty(wordMap)){
            langNameMap = wordMap.get(categoryGroupAtt.getName());
            if(MapUtils.isNotEmpty(langNameMap)){
                List<AttributeLangVO> attributeLangVOS = new ArrayList<>();
                langNameMap.forEach((k,v)->{
                    AttributeLangVO attributeLangVO = new AttributeLangVO();
                    attributeLangVO.setAttributeId(Long.valueOf(categoryGroupAtt.getComAttId()));
                    attributeLangVO.setAttributeType(CategoryAttrTypeEnum.EXTEND.getCode());
                    attributeLangVO.setLang(k);
                    attributeLangVO.setLangName(v);
                    attributeLangVOS.add(attributeLangVO);
                });
                attributeVO.setLangList(attributeLangVOS);
            }
        }

        return attributeVO;
    }

    /**
     * 根据给定的参数创建一个AttributeValueVO对象。
     * @param attributeId 属性ID
     * @param item CategoryGroupAttValue对象，包含属性值的ID和名称
     * @param wordMap 一个Map，用于存储语言相关的信息
     * @return 创建的AttributeValueVO对象
     */
    private AttributeValueVO createAttributeValueVO(Long attributeId, CategoryGroupAttValue item,Map<String,Map<String,String>> wordMap) {
        AttributeValueVO attributeValueVO = new AttributeValueVO();
        attributeValueVO.setAttributeId(attributeId);
        attributeValueVO.setId(Long.valueOf(item.getComAttValueId()));
        attributeValueVO.setStatus(YesOrNoEnum.YES.getCode());
        attributeValueVO.setSort(item.getOrderSort() != null?item.getOrderSort():YesOrNoEnum.YES.getCode());
        if(MapUtils.isNotEmpty(wordMap)){
            Map<String,String> langNameMap = wordMap.get(item.getName());
            if(MapUtils.isNotEmpty(langNameMap)){
                List<AttributeValueLangVO> attributeValueLangVOS = new ArrayList<>();
                langNameMap.forEach((k,v)->{
                    AttributeValueLangVO attributeValueLangVO = new AttributeValueLangVO();
                    attributeValueLangVO.setAttributeId(attributeId);
                    attributeValueLangVO.setAttributeValueId(Long.valueOf(item.getComAttValueId()));
                    attributeValueLangVO.setLang(k);
                    attributeValueLangVO.setLangName(v);
                    attributeValueLangVOS.add(attributeValueLangVO);
                });
                attributeValueVO.setLangList(attributeValueLangVOS);
            }
        }

        return attributeValueVO;
    }

    /**
     * 查找列表中的重复元素。
     * @param list 要查找的 Long 类型列表。
     * @return 包含所有重复元素的集合。
     */
    public Set<Long> findDuplicates(List<Long> list) {
        Set<Long> seen = new HashSet<>();
        Set<Long> duplicates = new HashSet<>();

        for (Long id : list) {
            if (!seen.add(id)) {
                duplicates.add(id);
            }
        }
        return duplicates;
    }

    @Override
    public List<PropertyVO> obtainJDSkuStoreExtAttributeList(Long jdCatId,Long jdSkuId) {
        log.info("AttributeOutServiceImpl.obtainJDSkuStoreExtAttributeList jdCatId:{},jdSkuId:{}", jdCatId,jdSkuId);
        List<PropertyVO> res = this.obtainStoreExtAttributeList(jdCatId,jdSkuId);
        log.info("AttributeOutServiceImpl.obtainJDSkuStoreExtAttributeList res:{}", JSON.toJSONString(res));
        return res;
    }


    /**
     * 处理扩展属性
     * 设置extendPropertyList（全部属性）
     * @param categoryId 类目ID
     * @param jdSkuId 京东SKU ID
     */
    private List<PropertyVO> obtainStoreExtAttributeList(Long categoryId,Long jdSkuId){
        log.info("开始处理SKU store扩展属性，categoryId:{},jdSkuId: {}", categoryId,jdSkuId);

        try {

            if (jdSkuId == null || jdSkuId <= 0) {
                log.warn("京东SKU ID无效，跳过处理，jdSkuId: {}", jdSkuId);
                return Collections.emptyList();
            }

            // 根据国际末级类目获取jdCatId, 如果获取不到，表面这个sku上品不在国际类目中，返回空列表
            CategoryPO categoryPO = categoryAtomicService.getValidPOById(categoryId);
            if (categoryPO == null) {
                log.info("AttributeOutServiceImpl.obtainStoreExtAttributeList,categoryPO is null.categoryId:{}", categoryId);
                return Collections.emptyList();
            }
            Long jdCatId = categoryPO.getJdCatId();
            if (jdCatId == null) {
                log.info("AttributeOutServiceImpl.obtainStoreExtAttributeList,categoryPO.jdSkuId is null.categoryId:{}", categoryId);
                return Collections.emptyList();
            }

            // 根据jdCatId获取扩展属性组信息
            Map<Integer, CategoryGroupAtt> attMap = categoryRpcService.getCategoryGroupAttByCatId(jdCatId.intValue());
            if (MapUtils.isEmpty(attMap) || CollectionUtils.isEmpty(attMap.values())) {
                log.info("AttributeOutServiceImpl.obtainStoreExtAttributeList,attMap is empty.categoryId:{},jdCatId:{}", categoryId, jdCatId);
                return Collections.emptyList();
            }
            // 获取属性id与属性对象的映射
            final Map<Integer, CategoryGroupAtt> attrIdGroupMap = attMap.values().stream().filter(Objects::nonNull).collect(Collectors.toMap(CategoryGroupAtt::getComAttId, Function.identity(),(a, b)->a));
            log.info("AttributeOutServiceImpl.obtainStoreExtAttributeList attMap:{} attrIdGroupMap:{}", JSON.toJSONString(attMap), JSON.toJSONString(attrIdGroupMap));
            // 1. 通过MaterialRpcService获取SKU的所有已存在扩展属性
            AttributeSettingResult attributeSettingResult = materialRpcService.getCateAttributeBySkuId(jdSkuId);
            log.info("AttributeOutServiceImpl.obtainStoreExtAttributeList attributeSettingResult:{}", JSON.toJSONString(attributeSettingResult));
            if (attributeSettingResult == null || CollectionUtils.isEmpty(attributeSettingResult.getExpandGroups())) {
                log.warn("获取SKU扩展属性为空，jdSkuId: {}", jdSkuId);
                return Collections.emptyList();
            }

            // 存储文本类型属性值，key：attributeID
            Map<Long, String> textAttributeValueMap = Maps.newHashMap();
            if (attributeSettingResult != null && CollectionUtils.isNotEmpty(attributeSettingResult.getExpandGroups())) {
                attributeSettingResult.getExpandGroups().forEach(group -> {
                    if (CollectionUtils.isNotEmpty(group.getAtts())) {
                        group.getAtts().forEach(propAttribute -> {
                            Long attributeId = Long.valueOf(propAttribute.getAttId());
                            if (CollectionUtils.isNotEmpty(propAttribute.getAttVals())) {
                                propAttribute.getAttVals().forEach(attrValue -> {
                                    if(propAttribute.getInputType() == AttributeInputTypeEnum.TEXT.getCode()){
                                        // 中台返回的文本类型属性值只会有一个值，即中文文本值，需要在后面获取多国语言的机翻文本值, KEY获取封装方法
                                        if(attributeId != null){
                                            textAttributeValueMap.put(attributeId, attrValue.getAttValName());
                                        }
                                    }
                                });
                            }
                        });
                    }
                });
            }
            if(MapUtils.isNotEmpty(textAttributeValueMap)){
                log.info("AttributeOutServiceImpl.obtainStoreExtAttributeList 获取到的文本值 skuid:{} 文本值:{}", jdSkuId, JSON.toJSONString(textAttributeValueMap));
            }
            // 获取语言类型列表
            List<String> langList = langManageService.getLangCodeList();
            // 获取翻译值
            Map<String, Map<String, String>> translateValueMap = obtainTranslateMap(textAttributeValueMap, langList);
            // 2. 转换全部属性为PropertyVO列表（extendPropertyList）
            List<PropertyVO> storeExtendPropertyList = new ArrayList<>();
            if (attributeSettingResult != null && CollectionUtils.isNotEmpty(attributeSettingResult.getExpandGroups())) {
                attributeSettingResult.getExpandGroups().stream().filter(Objects::nonNull).forEach(group -> {
                    if (CollectionUtils.isNotEmpty(group.getAtts())) {
                        group.getAtts().stream().filter(Objects::nonNull).forEach(propAttribute -> {
                            Long attributeId = Long.valueOf(propAttribute.getAttId());
                            PropertyVO propertyVO = new PropertyVO();
                            propertyVO.setAttributeId(attributeId);
                            propertyVO.setAttributeName(propAttribute.getAttName());
                            propertyVO.setAttributeInputType(propAttribute.getInputType());
                            CategoryGroupAtt categoryGroupAtt = attrIdGroupMap.get(attributeId.intValue());
                            if(categoryGroupAtt != null){
                                propertyVO.setLevel(categoryGroupAtt.getLevel());
                                propertyVO.setComGroupId(categoryGroupAtt.getComGroupId());
                                propertyVO.setComGroupName(categoryGroupAtt.getComGroupName());
                                propertyVO.setShield(categoryGroupAtt.getShield());
                            }
                            propertyVO.setSort(propAttribute.getSort());

                            if (CollectionUtils.isNotEmpty(propAttribute.getAttVals())) {
                                List<PropertyValueVO> propertyValueList = new ArrayList<>();
                                propAttribute.getAttVals().stream().filter(Objects::nonNull).filter(e->e.getAttValId() != null).forEach(attrValue -> {
                                    Long attributeValueId = Long.valueOf(attrValue.getAttValId());
                                    if(propAttribute.getInputType() == AttributeInputTypeEnum.TEXT.getCode()){
                                        // 中台返回的文本类型属性值只会有一个值，即中文文本值，需要在后面获取多国语言的机翻文本值, KEY获取封装方法
                                        if (translateValueMap != null && MapUtils.isNotEmpty(translateValueMap)) {
                                            Map<String, String> valueMap = translateValueMap.get(textAttributeValueMap.get(attributeId));
                                            log.info("获取翻译的key: {}, valueMap: {}", textAttributeValueMap.get(attributeId), JSON.toJSONString(valueMap));

                                            if (MapUtils.isNotEmpty(valueMap)) {
                                                valueMap.forEach((key, value) -> {
                                                    PropertyValueVO propertyValueVO = new PropertyValueVO();
                                                    propertyValueVO.setAttributeId(attributeId);
                                                    propertyValueVO.setAttributeValueId(attributeValueId);
                                                    // 这个地方的属性值需要在textAttributeValueMap中获取，然后获取翻译
                                                    propertyValueVO.setAttributeValueName(value);
                                                    propertyValueVO.setLang(key);
                                                    propertyValueVO.setLangName(value);
                                                    propertyValueVO.setSelected(true);
                                                    propertyValueVO.setSort(attrValue.getOrderSort());
                                                    propertyValueList.add(propertyValueVO);
                                                });
                                            }
                                        }
                                    } else {
                                        PropertyValueVO propertyValueVO = new PropertyValueVO();
                                        propertyValueVO.setAttributeId(attributeId);
                                        propertyValueVO.setAttributeValueId(attributeValueId);
                                        propertyValueVO.setAttributeValueName(attrValue.getAttValName());
                                        propertyValueVO.setSort(attrValue.getOrderSort());
                                        propertyValueVO.setSelected(true);
                                        propertyValueVO.setSort(attrValue.getOrderSort());
                                        propertyValueList.add(propertyValueVO);
                                    }

                                });
                                propertyVO.setPropertyValueVOList(propertyValueList);
                            }
                            storeExtendPropertyList.add(propertyVO);
                        });
                    }
                });
            }
            log.info("SKU扩展属性处理完成，jdSkuId: {}, storeExtendPropertyList: {}", jdSkuId, JSON.toJSONString(storeExtendPropertyList));
            return storeExtendPropertyList;
        } catch (Exception e) {
            log.error("处理SKU扩展属性时发生异常，jdSkuId: {}, categoryId: {}", jdSkuId, categoryId, e);
        }
        return Lists.newArrayList();
    }
}
