package com.jdi.isc.product.soa.service.mapstruct;

import cn.hutool.core.bean.BeanUtil;
import com.jdi.isc.product.soa.common.enums.AttributeCheckTypeEnum;
import com.jdi.isc.product.soa.common.enums.AttributeDimensionEnum;
import com.jdi.isc.product.soa.common.enums.AttributeInputTypeEnum;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.domain.category.po.GlobalAttributePO;
import com.jdi.isc.product.soa.domain.category.po.GlobalAttributeValuePO;
import com.jdi.isc.product.soa.domain.spu.biz.GroupPropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;
import com.jdi.isc.product.soa.domain.spu.po.ProductGlobalAttributePO;
import com.jdi.isc.product.soa.service.atomic.category.GlobalAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.category.GlobalAttributeValueAtomicService;
import com.jdi.isc.product.soa.service.manage.category.GlobalAttributeManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * spu/sku共用一套
 * <AUTHOR>
 * @date 2024/12/16
 **/
@Slf4j
@Service
public class ProductAttributeConvertService {

    @Resource
    private GlobalAttributeAtomicService globalAttributeAtomicService;
    @Resource
    private GlobalAttributeValueAtomicService globalAttributeValueAtomicService;
    @Resource
    @Lazy
    private GlobalAttributeManageService globalAttributeManageService;


    public List<ProductGlobalAttributePO> convertSpuAttribute(Long spuId,List<GroupPropertyVO> spuInterPropertyList){
        if(CollectionUtils.isEmpty(spuInterPropertyList)){
            log.error("SpuAttributeConvertService.convertSpuAttribute spuInterPropertyList is empty");
            return null;
        }

        // 过滤未选择和为空的跨境属性
        List<PropertyVO> spuProperties = spuInterPropertyList.stream()
                .filter(Objects::nonNull)
                .filter(groupProperty -> CollectionUtils.isNotEmpty(groupProperty.getPropertyVOS()))
                .flatMap(groupProperty -> groupProperty.getPropertyVOS().stream())
                .filter(property -> CollectionUtils.isNotEmpty(property.getPropertyValueVOList()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(spuProperties)) {
            log.error("SpuAttributeConvertService.convertSpuAttribute spuProperties is empty");
            return null;
        }

        // 获取后补足用
        List<Long> attributeIds = spuProperties.stream().filter(Objects::nonNull).map(PropertyVO::getAttributeId).collect(Collectors.toList());
        List<GlobalAttributePO> attributePOS = globalAttributeAtomicService.getValidStatusByIds(attributeIds,null,null);
        Map<Long,GlobalAttributePO> globalAttributePOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(attributePOS)){
            globalAttributePOMap.putAll(attributePOS.stream().collect(Collectors.toMap(GlobalAttributePO::getId,Function.identity())));
        }
        List<GlobalAttributeValuePO> attributeValuePOS = globalAttributeValueAtomicService.queryByAttrIds(attributeIds);
        Map<Long,List<GlobalAttributeValuePO>> globalAttributeValuePOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(attributeValuePOS)){
            globalAttributeValuePOMap.putAll(attributeValuePOS.stream().collect(Collectors.groupingBy(GlobalAttributeValuePO::getAttributeId)));
        }

        List<ProductGlobalAttributePO> results = new ArrayList<>();
        for(PropertyVO propertyVO : spuProperties){
            GlobalAttributePO attributePO = globalAttributePOMap.get(propertyVO.getAttributeId());
            if(attributePO == null){
                continue;
            }
            List<GlobalAttributeValuePO> attributeValuePOList = globalAttributeValuePOMap.get(propertyVO.getAttributeId());
            if(CollectionUtils.isEmpty(attributeValuePOList)){
                continue;
            }
            for(PropertyValueVO propertyValueVO : propertyVO.getPropertyValueVOList()){
                if(propertyValueVO == null){
                    continue;
                }
                if(AttributeInputTypeEnum.TEXT.getCode().equals(attributePO.getAttributeInputType())){
                    if(StringUtils.isBlank(propertyValueVO.getAttributeValueName())){
                        continue;
                    }
                    if(propertyValueVO.getAttributeValueId() == null && attributeValuePOList.get(0) != null){
                        propertyValueVO.setAttributeValueId(attributeValuePOList.get(0).getId());
                    }
                } else {
                    if(propertyValueVO.getSelected() == null || !propertyValueVO.getSelected() ){
                        continue;
                    }
                }
                ProductGlobalAttributePO productAttributePO = new ProductGlobalAttributePO();
                productAttributePO.setKeyId(spuId);
                productAttributePO.setKeyType(KeyTypeEnum.SPU.getCode());
                productAttributePO.setAttributeId(propertyVO.getAttributeId());
                productAttributePO.setAttributeValueId(propertyValueVO.getAttributeValueId());
                if(AttributeInputTypeEnum.TEXT.getCode().equals(attributePO.getAttributeInputType())){
                    productAttributePO.setAttributeValue(propertyValueVO.getAttributeValueName());
                }
                results.add(productAttributePO);
            }
        }
        return results;
    }


    public List<ProductGlobalAttributePO> convertSkuAttribute(Long skuId,List<GroupPropertyVO> skuInterPropertyList){
        if(CollectionUtils.isEmpty(skuInterPropertyList)){
            log.error("SpuAttributeConvertService.convertSkuAttribute spuInterPropertyList is empty");
            return null;
        }

        // 过滤未选择和为空的跨境属性
        List<PropertyVO> skuProperties = skuInterPropertyList.stream()
                .filter(Objects::nonNull)
                .filter(groupProperty -> CollectionUtils.isNotEmpty(groupProperty.getPropertyVOS()))
                .flatMap(groupProperty -> groupProperty.getPropertyVOS().stream())
                .filter(property -> CollectionUtils.isNotEmpty(property.getPropertyValueVOList()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuProperties)) {
            log.error("SpuAttributeConvertService.convertSkuAttribute spuProperties is empty");
            return null;
        }

        // 获取跨境属性相关信息、补足用
        List<Long> attributeIds = skuProperties.stream().filter(Objects::nonNull).map(PropertyVO::getAttributeId).collect(Collectors.toList());
        List<GlobalAttributePO> attributePOS = globalAttributeAtomicService.getValidStatusByIds(attributeIds,null,null);
        Map<Long,GlobalAttributePO> globalAttributePOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(attributePOS)){
            globalAttributePOMap.putAll(attributePOS.stream().collect(Collectors.toMap(GlobalAttributePO::getId,Function.identity())));
        }
        List<GlobalAttributeValuePO> attributeValuePOS = globalAttributeValueAtomicService.queryByAttrIds(attributeIds);
        Map<Long,List<GlobalAttributeValuePO>> globalAttributeValuePOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(attributeValuePOS)){
            globalAttributeValuePOMap.putAll(attributeValuePOS.stream().collect(Collectors.groupingBy(GlobalAttributeValuePO::getAttributeId)));
        }

        List<ProductGlobalAttributePO> results = new ArrayList<>();
        for(PropertyVO propertyVO : skuProperties){
            GlobalAttributePO attributePO = globalAttributePOMap.get(propertyVO.getAttributeId());
            if(attributePO == null){
                continue;
            }
            List<GlobalAttributeValuePO> attributeValuePOList = globalAttributeValuePOMap.get(propertyVO.getAttributeId());
            if(CollectionUtils.isEmpty(attributeValuePOList)){
                continue;
            }
            for(PropertyValueVO propertyValueVO : propertyVO.getPropertyValueVOList()){
                if(propertyValueVO == null){
                    continue;
                }
                if(AttributeInputTypeEnum.TEXT.getCode().equals(attributePO.getAttributeInputType())){
                    if(StringUtils.isBlank(propertyValueVO.getAttributeValueName())){
                        continue;
                    }
                    if(propertyValueVO.getAttributeValueId() == null && attributeValuePOList.get(0) != null){
                        propertyValueVO.setAttributeValueId(attributeValuePOList.get(0).getId());
                    }
                } else {
                    if(propertyValueVO.getSelected() == null || !propertyValueVO.getSelected() ){
                        continue;
                    }
                }
                ProductGlobalAttributePO productAttributePO = new ProductGlobalAttributePO();
                productAttributePO.setKeyId(skuId);
                productAttributePO.setKeyType(KeyTypeEnum.SKU.getCode());
                productAttributePO.setAttributeId(propertyVO.getAttributeId());
                productAttributePO.setAttributeValueId(propertyValueVO.getAttributeValueId());
                if(AttributeInputTypeEnum.TEXT.getCode().equals(attributePO.getAttributeInputType())){
                    productAttributePO.setAttributeValue(propertyValueVO.getAttributeValueName());
                }
                results.add(productAttributePO);
            }
        }
        return results;
    }

    /**
     * 全量的相关的属性列表，里面标记了哪些是已选或者已填
     * @param interPropertyList 草稿状态的属性列表,spu或者sku的
     * @param groupPropertyVOS 全量的相关的属性列表
     * @return 转换后的正式发布的属性列表
     */
    public List<GroupPropertyVO> convertAttributeFromDraft(List<GroupPropertyVO> interPropertyList,
                                                              List<GroupPropertyVO> groupPropertyVOS) {
        if (CollectionUtils.isEmpty(interPropertyList)) {
            log.error("SpuAttributeConvertService.convertSkuAttributeFromDraft skuInterPropertyList is empty");
            this.setPublishProductRequired(groupPropertyVOS,AttributeCheckTypeEnum.PUBLISH_PRODUCT);
            return groupPropertyVOS;
        }

        // 过滤未选择和为空的跨境属性
        List<PropertyVO> skuProperties = interPropertyList.stream()
                .filter(Objects::nonNull)
                .filter(groupProperty -> CollectionUtils.isNotEmpty(groupProperty.getPropertyVOS()))
                .flatMap(groupProperty -> groupProperty.getPropertyVOS().stream())
                .filter(property -> CollectionUtils.isNotEmpty(property.getPropertyValueVOList()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(skuProperties)) {
            log.error("SpuAttributeConvertService.convertSkuAttributeFromDraft skuProperties is empty");
            this.setPublishProductRequired(groupPropertyVOS,AttributeCheckTypeEnum.PUBLISH_PRODUCT);
            return groupPropertyVOS;
        }

        // 创建一个 Map 来存储 attributeId 到 PropertyVO 的映射
        Map<Long, PropertyVO> skuPropertyMap = skuProperties.stream()
                .collect(Collectors.toMap(PropertyVO::getAttributeId, Function.identity()));

        // 遍历并更新 groupPropertyVOS
        List<GroupPropertyVO> results = groupPropertyVOS.stream().map(groupPropertyVO -> {
            GroupPropertyVO newGroupPropertyVO = new GroupPropertyVO();
            BeanUtil.copyProperties(groupPropertyVO, newGroupPropertyVO);

            if (CollectionUtils.isNotEmpty(groupPropertyVO.getPropertyVOS())) {
                List<PropertyVO> newPropertyVOList = groupPropertyVO.getPropertyVOS().stream()
                        .map(propertyVO -> updatePropertyFromDraft(propertyVO, skuPropertyMap))
                        .collect(Collectors.toList());
                newGroupPropertyVO.setPropertyVOS(newPropertyVOList);
            }

            return newGroupPropertyVO;
        }).collect(Collectors.toList());
        this.setPublishProductRequired(results,AttributeCheckTypeEnum.PUBLISH_PRODUCT);
        return results;
    }

    private PropertyVO updatePropertyFromDraft(PropertyVO propertyVO, Map<Long, PropertyVO> skuPropertyMap) {
        PropertyVO newPropertyVO = new PropertyVO();
        BeanUtil.copyProperties(propertyVO, newPropertyVO);

        if (skuPropertyMap.containsKey(propertyVO.getAttributeId())) {
            PropertyVO draftPropertyVO = skuPropertyMap.get(propertyVO.getAttributeId());

            // 更新 PropertyValueVOList
            if (CollectionUtils.isNotEmpty(propertyVO.getPropertyValueVOList()) &&
                    CollectionUtils.isNotEmpty(draftPropertyVO.getPropertyValueVOList())) {

                List<PropertyValueVO> newPropertyValueVOList = propertyVO.getPropertyValueVOList().stream()
                        .map(propertyValueVO -> updatePropertyValueFromDraft(propertyValueVO, draftPropertyVO.getPropertyValueVOList(), propertyVO.getAttributeInputType()))
                        .collect(Collectors.toList());

                newPropertyVO.setPropertyValueVOList(newPropertyValueVOList);
            }
        }

        return newPropertyVO;
    }

    private PropertyValueVO updatePropertyValueFromDraft(PropertyValueVO propertyValueVO, List<PropertyValueVO> draftValueList,Integer inputCheckType) {
        PropertyValueVO newPropertyValueVO = new PropertyValueVO();
        BeanUtil.copyProperties(propertyValueVO, newPropertyValueVO);

        if(CollectionUtils.isNotEmpty(draftValueList)){
            if(AttributeInputTypeEnum.TEXT.getCode().equals(inputCheckType)){
                PropertyValueVO draftValueVO = draftValueList.get(0);
                if(draftValueVO != null && StringUtils.isNotBlank(draftValueVO.getAttributeValueName())){
                    newPropertyValueVO.setSelected(Boolean.TRUE);
                    newPropertyValueVO.setAttributeValueName(draftValueVO.getAttributeValueName());
                }
            } else {
                Map<Long,PropertyValueVO> draftValueMap = draftValueList.stream().filter(Objects::nonNull)
                        .collect(Collectors.toMap(PropertyValueVO::getAttributeValueId, Function.identity()));
                PropertyValueVO draftValueVO = draftValueMap.get(propertyValueVO.getAttributeValueId());
                if(draftValueVO != null){
                    newPropertyValueVO.setSelected(draftValueVO.getSelected());
                }
            }
        }

        return newPropertyValueVO;
    }

    /**
     * 全量的相关的属性列表，里面标记了哪些是已选或者已填
     * @param skuInterPropertyList 从数据库中获取的跨境属性列表。spu或者sku的
     * @param groupPropertyVOS 全量的相关的属性列表
     * @return 转换后的属性组对象列表。
     */
    public List<GroupPropertyVO> convertAttributeFromDB(List<ProductGlobalAttributePO> skuInterPropertyList,
                                                     List<GroupPropertyVO> groupPropertyVOS) {
        if (CollectionUtils.isEmpty(skuInterPropertyList)) {
            log.error("SpuAttributeConvertService.convertAttributeFromDB groupPropertyVOS is empty");
            this.setPublishProductRequired(groupPropertyVOS,AttributeCheckTypeEnum.PUBLISH_PRODUCT);
            return groupPropertyVOS;
        }

        Map<Long, List<ProductGlobalAttributePO>> skuGlobalAttributePOMap = skuInterPropertyList.stream()
                .collect(Collectors.groupingBy(ProductGlobalAttributePO::getAttributeId));

        List<GroupPropertyVO> results = groupPropertyVOS.stream().map(groupPropertyVO -> {
            GroupPropertyVO newGroupPropertyVO = new GroupPropertyVO();
            BeanUtil.copyProperties(groupPropertyVO, newGroupPropertyVO);

            if (CollectionUtils.isNotEmpty(groupPropertyVO.getPropertyVOS())) {
                List<PropertyVO> newPropertyVOList = groupPropertyVO.getPropertyVOS().stream()
                        .map(propertyVO -> processPropertyVO(propertyVO, skuGlobalAttributePOMap))
                        .collect(Collectors.toList());
                newGroupPropertyVO.setPropertyVOS(newPropertyVOList);
            }

            return newGroupPropertyVO;
        }).collect(Collectors.toList());
        this.setPublishProductRequired(results,AttributeCheckTypeEnum.PUBLISH_PRODUCT);
        return results;
    }

    private PropertyVO processPropertyVO(PropertyVO propertyVO, Map<Long, List<ProductGlobalAttributePO>> skuGlobalAttributePOMap) {
        PropertyVO newPropertyVO = new PropertyVO();
        BeanUtil.copyProperties(propertyVO, newPropertyVO);

        if (!skuGlobalAttributePOMap.containsKey(propertyVO.getAttributeId())) {
            return newPropertyVO;
        }

        List<ProductGlobalAttributePO> spuValuePOS = skuGlobalAttributePOMap.get(propertyVO.getAttributeId());
        Map<Long, ProductGlobalAttributePO> attributeMap = spuValuePOS.stream()
                .collect(Collectors.toMap(ProductGlobalAttributePO::getAttributeValueId, Function.identity(),(v1,v2)->v2));

        if (CollectionUtils.isNotEmpty(propertyVO.getPropertyValueVOList())) {
            List<PropertyValueVO> newPropertyValueVOList = propertyVO.getPropertyValueVOList().stream()
                    .map(propertyValueVO -> processPropertyValueVO(propertyValueVO, attributeMap, propertyVO.getAttributeInputType()))
                    .collect(Collectors.toList());
            newPropertyVO.setPropertyValueVOList(newPropertyValueVOList);
        }

        return newPropertyVO;
    }

    private PropertyValueVO processPropertyValueVO(PropertyValueVO propertyValueVO, Map<Long, ProductGlobalAttributePO> attributeMap, Integer inputCheckType) {
        PropertyValueVO newPropertyValueVO = new PropertyValueVO();
        BeanUtil.copyProperties(propertyValueVO, newPropertyValueVO);

        if (attributeMap.containsKey(propertyValueVO.getAttributeValueId())) {
            newPropertyValueVO.setSelected(Boolean.TRUE);
            if (AttributeInputTypeEnum.TEXT.getCode().equals(inputCheckType)) {
                ProductGlobalAttributePO attributePO = attributeMap.get(propertyValueVO.getAttributeValueId());
                newPropertyValueVO.setAttributeValueName(attributePO.getAttributeValue());
            }
        }

        return newPropertyValueVO;
    }

    /**
     * 设置必填属性
     * @param spuGroupPropertyVOS 属性组列表
     */
    public void setPublishProductRequired(List<GroupPropertyVO> spuGroupPropertyVOS, AttributeCheckTypeEnum checkTypeEnum){
        if(CollectionUtils.isEmpty(spuGroupPropertyVOS)){
            log.info("SpuConvertService.setRequired params is null");
            return;
        }
        for(GroupPropertyVO groupPropertyVO: spuGroupPropertyVOS){
            List<PropertyVO> propertyVOS = groupPropertyVO.getPropertyVOS();
            if(CollectionUtils.isEmpty(propertyVOS)){
                continue;
            }
            if(groupPropertyVO.getRequirement() != null
                    && groupPropertyVO.getRequirement().equals(checkTypeEnum.getCode())){
                for (PropertyVO propertyVO : propertyVOS) {
                    if(propertyVO == null){
                        continue;
                    }
                    propertyVO.setRequired(Boolean.TRUE);
                    List<PropertyValueVO> propertyValueVOList = propertyVO.getPropertyValueVOList();
                    if(CollectionUtils.isEmpty(propertyValueVOList)){
                        continue;
                    }
                    for(PropertyValueVO propertyValueVO : propertyValueVOList){
                        propertyValueVO.setRequired(Boolean.TRUE);
                    }
                }
            } else {
                for (PropertyVO propertyVO : propertyVOS) {
                    if(propertyVO == null){
                        continue;
                    }
                    propertyVO.setRequired(Boolean.FALSE);
                    List<PropertyValueVO> propertyValueVOList = propertyVO.getPropertyValueVOList();
                    if(CollectionUtils.isEmpty(propertyValueVOList)){
                        continue;
                    }
                    for(PropertyValueVO propertyValueVO : propertyValueVOList){
                        propertyValueVO.setRequired(Boolean.FALSE);
                    }
                }
            }
        }
        this.sorted(spuGroupPropertyVOS);
    }

    /**
     * 对给定的GroupPropertyVO列表进行排序，按照requirement字段的值逆序排列。
     * @param groupPropertyVOS 需要排序的GroupPropertyVO对象列表。
     */
    private void sorted(List<GroupPropertyVO> groupPropertyVOS){
        if(CollectionUtils.isEmpty(groupPropertyVOS)){
            return;
        }
        groupPropertyVOS.stream().filter(item->item.getRequirement() != null)
                .sorted(Comparator.comparing(GroupPropertyVO::getRequirement).reversed());
    }

    /**
     * 将数据库中的 SKU 属性转换为前端可用的 PropertyVO 对象。
     * @param sourceCountryCode 源国家/地区代码
     * @param targetCountryCodes 目标国家/地区代码列表
     * @param interPropertyList 中间属性列表
     * @return 转换后的 PropertyVO 对象列表
     */
    public List<PropertyVO> convertSkuAttributeVOSFromDB(String sourceCountryCode,List<String> targetCountryCodes
            ,List<ProductGlobalAttributePO> interPropertyList) {
        if (CollectionUtils.isEmpty(interPropertyList)) {
            log.error("SpuAttributeConvertService.convertAttributeVOSFromDB propertyVOS is empty");
            return new ArrayList<>();
        }
        Set<Long> attributeIds = interPropertyList.stream().map(ProductGlobalAttributePO::getAttributeId).collect(Collectors.toSet());
        List<GroupPropertyVO> groupPropertyVOS = globalAttributeManageService.queryGroupPropertyVos(attributeIds, AttributeDimensionEnum.SKU.getCode()
                , LangContextHolder.get(),sourceCountryCode,targetCountryCodes);
        if(CollectionUtils.isEmpty(groupPropertyVOS)){
            log.error("SpuAttributeConvertService.convertAttributeVOSFromDB groupPropertyVOS is empty");
            return new ArrayList<>();
        }

        Map<Long, List<ProductGlobalAttributePO>> skuGlobalAttributePOMap = interPropertyList.stream()
                .collect(Collectors.groupingBy(ProductGlobalAttributePO::getAttributeId));

        List<PropertyVO> propertyVOS = new ArrayList<>();
        groupPropertyVOS.stream().forEach(groupPropertyVO->{
            if (CollectionUtils.isNotEmpty(groupPropertyVO.getPropertyVOS())) {
                List<PropertyVO> newPropertyVOList = groupPropertyVO.getPropertyVOS().stream()
                        .map(propertyVO -> processPropertyVO(propertyVO, skuGlobalAttributePOMap))
                        .collect(Collectors.toList());
                propertyVOS.addAll(newPropertyVOList);
            }
        });
        return propertyVOS;
    }
}