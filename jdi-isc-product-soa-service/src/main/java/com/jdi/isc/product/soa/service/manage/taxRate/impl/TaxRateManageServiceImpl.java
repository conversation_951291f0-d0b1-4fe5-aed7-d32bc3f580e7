package com.jdi.isc.product.soa.service.manage.taxRate.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRatePageVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRateVO;
import com.jdi.isc.product.soa.domain.taxRate.po.TaxRateDraftPO;
import com.jdi.isc.product.soa.domain.taxRate.po.TaxRateLogPO;
import com.jdi.isc.product.soa.domain.taxRate.po.TaxRatePO;
import com.jdi.isc.product.soa.service.atomic.taxRate.TaxRateAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.TaxRateLogAtomicService;
import com.jdi.isc.product.soa.service.manage.taxRate.TaxRateManageService;
import com.jdi.isc.product.soa.service.mapstruct.taxRate.TaxRateConvert;
import com.jdi.isc.product.soa.service.mapstruct.taxRate.TaxRateDraftConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TaxRateManageServiceImpl extends BaseManageSupportService<TaxRateVO, TaxRatePO> implements TaxRateManageService {


    @Resource
    private TaxRateAtomicService taxRateAtomicService;

    @Resource
    private TaxRateLogAtomicService taxRateLogAtomicService;

    @Override
    public DataResponse<TaxRateVO> saveOrUpdate(TaxRateVO vo) {
        TaxRatePO taxRatePO = TaxRateConvert.INSTANCE.vo2Po(vo);
        boolean result = taxRateAtomicService.saveOrUpdate(taxRatePO);
        return DataResponse.success(vo);
    }

    @Override
    public DataResponse<Boolean> saveOrUpdateBatch(List<TaxRateVO> voList) {
        List<TaxRatePO> taxRatePOList = TaxRateConvert.INSTANCE.listVo2Po(voList);
        List<TaxRatePO> dbTaxRatePOList = taxRateAtomicService.queryListPoByList(taxRatePOList);
        Map<String, TaxRatePO> taxRateMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dbTaxRatePOList)) {
            taxRateMap = dbTaxRatePOList.stream().collect(Collectors.toMap(taxRatePO -> {
                return taxRatePO.getKeyId() + taxRatePO.getTaxCode();
            }, Function.identity()));
        }
        for (TaxRatePO taxRatePO : taxRatePOList) {
            if (taxRateMap.containsKey(taxRatePO.getKeyId() + taxRatePO.getTaxCode())) {
                TaxRatePO tempTaxRatePO = taxRateMap.get(taxRatePO.getKeyId() + taxRatePO.getTaxCode());
                taxRatePO.setCreator(tempTaxRatePO.getUpdater());
                taxRatePO.setUpdateTime(new Date().getTime());
                taxRatePO.setCountryCode(null);
                taxRatePO.setClientCode(null);
                taxRatePO.setKeyType(null);
                taxRatePO.setKeyId(null);
                taxRatePO.setClientType(null);
                taxRatePO.setId(tempTaxRatePO.getId());
            } else {
                taxRatePO.setId(null);
                taxRatePO.setCreateTime(new Date().getTime());
                taxRatePO.setUpdateTime(new Date().getTime());
            }
        }
        boolean result = taxRateAtomicService.saveOrUpdateBatch(taxRatePOList);
        return DataResponse.success(result);
    }

    @Override
    public TaxRateVO getDetailById(TaxRateVO vo) {
        if (Objects.isNull(vo.getId())) {
            return null;
        }
        TaxRatePO taxRatePO = taxRateAtomicService.getById(vo.getId());
        TaxRateVO taxRateVO = TaxRateConvert.INSTANCE.po2Vo(taxRatePO);
        return taxRateVO;
    }

    @Override
    public PageInfo<TaxRateVO> pageTaxRate(TaxRatePageVO vo) {
        log.info("TaxRateManageServiceImpl.pageTaxRate param:{}", JSON.toJSONString(vo));
        Page<TaxRatePO> taxRatePOPage = taxRateAtomicService.pageTaxRatePO(vo);
        List<TaxRateVO> mkuMaterialVOList = TaxRateConvert.INSTANCE.listPo2Vo(taxRatePOPage.getRecords());
        log.info("TaxRateManageServiceImpl.pageTaxRate param:{},res:{}", JSON.toJSONString(vo), JSON.toJSONString(mkuMaterialVOList));
        return super.pageTransform(taxRatePOPage, mkuMaterialVOList);
    }

    /**
     * 根据条件查询税率列表
     *
     * @param vo 查询条件对象
     * @return 符合条件的税率列表
     */
    @Override
    public List<TaxRateVO> listTaxRate(TaxRateVO vo) {
        log.info("TaxRateManageServiceImpl.listTaxRate param:{}", JSON.toJSONString(vo));
        List<TaxRatePO> taxRatePOList = new ArrayList<>();
        TaxRatePO taxRatePO = TaxRateConvert.INSTANCE.vo2Po(vo);
        taxRatePOList.add(taxRatePO);
        List<TaxRatePO> resultPoList = taxRateAtomicService.queryListPoByList(taxRatePOList);
        List<TaxRateVO> taxRateVOList = TaxRateConvert.INSTANCE.listPo2Vo(resultPoList);
        log.info("TaxRateManageServiceImpl.listTaxRate param:{},res:{}", JSON.toJSONString(vo), JSON.toJSONString(taxRateVOList));
        return taxRateVOList;
    }

    @Override
    public Boolean delete(TaxRateVO vo) {
        TaxRatePO taxRatePO = taxRateAtomicService.getById(vo.getId());
        TaxRateLogPO taxRateLogPO = TaxRateConvert.INSTANCE.po2po(taxRatePO);
        log.info("TaxRateManageServiceImpl delete taxRatePO:{}", JSON.toJSONString(taxRatePO));
        taxRateLogPO.setCreator(vo.getCreator());
        taxRateLogPO.setUpdater(vo.getCreator());
        taxRateLogPO.setCreateTime(new Date().getTime());
        taxRateLogPO.setUpdateTime(new Date().getTime());
        taxRateLogPO.setId(null);
        taxRateLogPO.setRemark("删除信息");
        boolean result = taxRateAtomicService.removeById(vo.getId());
        try {
            boolean save = taxRateLogAtomicService.saveOrUpdate(taxRateLogPO);
        } catch (Exception e) {
            log.error("TaxRateManageServiceImpl delete e:{}", e.getMessage(), e);
        }

        return result;

    }

    @Override
    public Boolean exists(TaxRateVO vo) {
        LambdaQueryWrapper<TaxRatePO> wrapper = Wrappers.<TaxRatePO>lambdaQuery()
                .eq(TaxRatePO::getCountryCode, vo.getCountryCode())
                .eq(TaxRatePO::getClientCode, vo.getClientCode())
                .eq(TaxRatePO::getKeyId, vo.getKeyId())
                .eq(TaxRatePO::getTaxCode, vo.getTaxCode())
                .eq(TaxRatePO::getKeyType, vo.getKeyType())
                .eq(TaxRatePO::getYn, YnEnum.YES.getCode());
        return taxRateAtomicService.getBaseMapper().exists(wrapper);
    }

    @Override
    public List<TaxRateVO> listByCountryCodeAndSkuId(TaxRateVO vo) {
        LambdaQueryWrapper<TaxRatePO> wrapper = Wrappers.<TaxRatePO>lambdaQuery()
                .eq(TaxRatePO::getCountryCode, vo.getCountryCode())
                .eq(TaxRatePO::getKeyId, vo.getKeyId())
                .eq(TaxRatePO::getKeyType, vo.getKeyType())
                .eq(TaxRatePO::getYn, YnEnum.YES.getCode());
        List<TaxRatePO> taxRatePOS = taxRateAtomicService.getBaseMapper().selectList(wrapper);
        return TaxRateConvert.INSTANCE.listPo2Vo(taxRatePOS);
    }
}
