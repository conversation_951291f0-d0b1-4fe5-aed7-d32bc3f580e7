package com.jdi.isc.product.soa.service.atomic.spu;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.spu.po.SpuLangPO;
import com.jdi.isc.product.soa.repository.mapper.spu.SpuLangBasicMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【jdi_isc_spu_lang_sharding(spu多语言表)】的数据库操作Service实现
 * @createDate 2023-11-25 16:00:13
 */
@Service
public class SpuLangAtomicService extends ServiceImpl<SpuLangBasicMapper, SpuLangPO> {

    /**
     * 根据spuId查询商品名多语言
     *
     * @param spuId
     * @return
     */
    public List<SpuLangPO> listLangTitle(Long spuId) {
        LambdaQueryWrapper<SpuLangPO> lq = new LambdaQueryWrapper<SpuLangPO>().eq(SpuLangPO::getSpuId, spuId).eq(SpuLangPO::getYn, YnEnum.YES.getCode());
        return super.list(lq);
    }


    public Map<String, SpuLangPO> langTitleMap(Long spuId) {
        List<SpuLangPO> langPoList = listLangTitle(spuId);
        if (CollectionUtils.isEmpty(langPoList)) {
            return Collections.emptyMap();
        }
        // 根据lang来分类数据，lang值相同时，保留createTime晚的那条
        return langPoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(SpuLangPO::getLang, Function.identity(),
                (o1, o2) -> o1.getCreateTime().compareTo(o2.getCreateTime()) < 0 ? o1 : o2));
    }

    /**
     * 批量查询商品名
     *
     * @param spuIds
     * @return
     */
    @PFTracing
    public Map<Long, SpuLangPO> getSpuLangNameBySpuIds(List<Long> spuIds, String lang) {
        if (CollectionUtils.isEmpty(spuIds)) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<SpuLangPO> lq = new LambdaQueryWrapper<SpuLangPO>()
                .in(SpuLangPO::getSpuId, spuIds)
                .eq(SpuLangPO::getLang, StringUtils.isNotBlank(lang) ? lang : LangConstant.LANG_ZH)
                .eq(SpuLangPO::getYn, YnEnum.YES.getCode());
        List<SpuLangPO> langPoList = super.getBaseMapper().selectList(lq);
        if (CollectionUtils.isEmpty(langPoList)) {
            return Collections.emptyMap();
        }

        return CollectionUtils.isEmpty(langPoList) ? Collections.emptyMap()
                : langPoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(SpuLangPO::getSpuId, Function.identity(), (key1, key2) -> key2));
    }

    public SpuLangPO getSpuNameBuSpuIdAndLang(Long spuId, String lang) {
        LambdaQueryWrapper<SpuLangPO> lq = new LambdaQueryWrapper<SpuLangPO>().eq(SpuLangPO::getSpuId, spuId).eq(SpuLangPO::getLang, lang).eq(SpuLangPO::getYn, YnEnum.YES.getCode());
        return getBaseMapper().selectOne(lq);
    }

    /**
     * 根据spuIds查询商品名多语言
     *
     * @param spuIds
     * @return
     */
    public List<SpuLangPO> listLang(Set<Long> spuIds,List<String> langs) {
        LambdaQueryWrapper<SpuLangPO> lq = new LambdaQueryWrapper<SpuLangPO>()
                .in(SpuLangPO::getSpuId, spuIds)
                .in(SpuLangPO::getLang, langs)
                .eq(SpuLangPO::getYn, YnEnum.YES.getCode());
        return super.list(lq);
    }
    @PFTracing
    public Map<String, SpuLangPO> getMapBySpuId(Set<Long> spuIds) {
        LambdaQueryWrapper<SpuLangPO> lq = new LambdaQueryWrapper<SpuLangPO>()
            .in(SpuLangPO::getSpuId, spuIds)
            .eq(SpuLangPO::getYn, YnEnum.YES.getCode());
        List<SpuLangPO> langPoList = super.list(lq);
        if (CollectionUtils.isEmpty(langPoList)) {
            return Collections.emptyMap();
        }

        return CollectionUtils.isEmpty(langPoList) ? Collections.emptyMap()
            : langPoList.stream().filter(Objects::nonNull).collect(Collectors.toMap( spuLangPO -> {
                return spuLangPO.getSpuId()+spuLangPO.getLang();
        }, Function.identity(),(po1,po2) -> po1));
    }


    /**
     * 根据商品ID集合和语言集合查询商品多语言信息映射表
     * @param spuIds 商品ID集合，若为空则返回空Map
     * @param langSet 语言集合，若为空则默认查询中英文
     * @return 返回嵌套Map结构，外层key为商品ID，内层key为语言代码，value为对应语言的多语言信息对象
     */
    public Map<Long, Map<String, SpuLangPO>> querySpuLangMap(Set<Long> spuIds, Set<String> langSet) {
        if (CollectionUtils.isEmpty(spuIds)) {
            return Collections.emptyMap();
        }
        if (CollectionUtils.isEmpty(langSet)) {
            langSet = Sets.newHashSet(LangConstant.LANG_ZH, LangConstant.LANG_EN);
        }
        // 批量查询spuId的多语言名称
        List<SpuLangPO> spuLangPOList = this.listLang(spuIds, Lists.newArrayList(langSet));

        return Optional.ofNullable(spuLangPOList).orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(SpuLangPO::getSpuId,
                        Collectors.toMap(SpuLangPO::getLang,Function.identity() ,(o1, o2) -> o1)));
    }
}




