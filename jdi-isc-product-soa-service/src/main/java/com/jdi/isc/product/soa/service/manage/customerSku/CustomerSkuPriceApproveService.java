package com.jdi.isc.product.soa.service.manage.customerSku;

import com.jdi.isc.product.soa.domain.apply.po.ApplyInfoPO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuAuditVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceDraftVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.SalePriceCalculateResVO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailDraftPO;

/**
 * @Description: sku客制化价格-草稿表数据维护服务-审批
 * @Author: zhaokun51
 * @Date: 2024/11/25 13:30
 **/

public interface CustomerSkuPriceApproveService {

    /**
     * 提交参数设置
     * @param input 提交参数
     * @return 结果
     */
    void setSubmitAuditStatus(CustomerSkuPriceDraftVO input);

    /**
     * 提交
     * @param input 提交参数
     * @return 结果
     */
    String batchSubmit(CustomerSkuPriceDraftVO input);

    /**
     * 通过
     * @param input 提交参数
     * @return 结果
     */
    Boolean messageApprove(CustomerSkuAuditVO input);

    /**
     * 驳回
     * @param input 提交参数
     * @return 结果
     */
    Boolean messageReject(CustomerSkuAuditVO input);

    /**
     * 撤回-消息
     * @param input 提交参数
     * @return 结果
     */
    Boolean messageRevoke(CustomerSkuAuditVO input);


    /**
     * 阶段审批
     * @param input 提交参数
     * @return 结果
     */
    Boolean messageApproving(CustomerSkuAuditVO input);

    /**
     * 根据客户SKU价格草稿计算销售价格并返回结果。
     * @param draftVO 客户SKU价格草稿信息。
     * @return 计算后的销售价格结果。
     */
    SalePriceCalculateResVO getCalculateResVO(CustomerSkuPriceDraftVO draftVO);

    /**
     * 通过-单个
     * @param input 提交参数
     * @return 结果
     */
    Boolean singleApprove(CustomerSkuPriceDetailDraftPO detailDraftPO, CustomerSkuAuditVO input, ApplyInfoPO applyInfoPO);

    /**
     * 驳回-单个
     * @param input 提交参数
     * @return 结果
     */
    Boolean singleReject(CustomerSkuPriceDetailDraftPO detailDraftPO,CustomerSkuAuditVO input,ApplyInfoPO applyInfoPO);

    /**
     * 撤回-单个
     * @param input 提交参数
     * @return 结果
     */
    Boolean singleRevoke(CustomerSkuPriceDetailDraftPO detailDraftPO,CustomerSkuAuditVO input,ApplyInfoPO applyInfoPO);

    /**
     * 提交默认申请单信息(落库但不会触发调用joySky审批流)
     * @param id 价格明细草稿id
     * @return 审批实例id
     */
    String submitDefaultApplyInfo(Long id);


}
