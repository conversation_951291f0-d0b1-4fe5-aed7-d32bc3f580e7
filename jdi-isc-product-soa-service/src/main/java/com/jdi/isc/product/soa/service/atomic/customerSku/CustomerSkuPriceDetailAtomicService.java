package com.jdi.isc.product.soa.service.atomic.customerSku;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.repository.mapper.customerSku.CustomerSkuPriceDetailBaseMapper;
import com.jdi.isc.product.soa.service.support.helper.BasicPOHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * The type Customer sku price detail atomic service.
 *
 * @Description: sku客制化价格草稿明细表原子服务
 * @Author: zhaokun51
 * @Date: 2025 /02/27 14:53
 */
@Slf4j
@Service
public class CustomerSkuPriceDetailAtomicService extends ServiceImpl<CustomerSkuPriceDetailBaseMapper, CustomerSkuPriceDetailPO> {

    /**
     * 未删除的对象
     *
     * @param id id
     * @return 对象 customer sku price detail po
     */
    public CustomerSkuPriceDetailPO getValidById(Long id){
        LambdaQueryWrapper<CustomerSkuPriceDetailPO> wrapper = Wrappers.<CustomerSkuPriceDetailPO>lambdaQuery()
                .eq(CustomerSkuPriceDetailPO::getId, id)
                .eq(CustomerSkuPriceDetailPO::getYn, YnEnum.YES.getCode());
        CustomerSkuPriceDetailPO result = super.getOne(wrapper);

//        if (result == null) {
//            throw new ProductBizException("不存在客制化价格. %s", id);
//        }

        return result;
    }

    /**
     * 根据sourceId获取有效的CustomerSkuPriceDetailPO对象。
     *
     * @param sourceId 来源ID
     * @return 对应的CustomerSkuPriceDetailPO对象 customer sku price detail po
     */
    public CustomerSkuPriceDetailPO getValidBySourceId(Long sourceId){
        if(sourceId == null){
            return null;
        }
        LambdaQueryWrapper<CustomerSkuPriceDetailPO> wrapper = Wrappers.<CustomerSkuPriceDetailPO>lambdaQuery()
                .eq(CustomerSkuPriceDetailPO::getSourceId, sourceId)
                .eq(CustomerSkuPriceDetailPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    public List<CustomerSkuPriceDetailPO> listBySourceIds(List<Long> sourceIds){
        if(CollectionUtils.isEmpty(sourceIds)){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CustomerSkuPriceDetailPO> wrapper = Wrappers.<CustomerSkuPriceDetailPO>lambdaQuery()
                .in(CustomerSkuPriceDetailPO::getSourceId, sourceIds)
                .eq(CustomerSkuPriceDetailPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    /**
     * 根据 SKU ID 和货币获取有效的客户 SKU 价格明细列表。
     *
     * @param skuId    SKU ID
     * @param currency 货币代码
     * @return 客户 SKU 价格明细列表
     */
    public List<CustomerSkuPriceDetailPO> getValidBySkuIdAndCurrency(Long skuId, String currency){
        if(skuId == null || StringUtils.isBlank(currency)){
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<CustomerSkuPriceDetailPO> wrapper = Wrappers.<CustomerSkuPriceDetailPO>lambdaQuery()
                .eq(CustomerSkuPriceDetailPO::getSkuId, skuId)
                .eq(CustomerSkuPriceDetailPO::getCurrency, currency)
                .eq(CustomerSkuPriceDetailPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    /**
     * 根据商品ID集合查询客户商品价格明细。
     *
     * @param skuIds 商品ID集合
     * @return 客户商品价格明细列表 list
     */
    public List<CustomerSkuPriceDetailPO> listBySkuIds(Collection<Long> skuIds){
        if(CollectionUtils.isEmpty(skuIds)){
            return null;
        }
        LambdaQueryWrapper<CustomerSkuPriceDetailPO> queryWrapper = Wrappers.<CustomerSkuPriceDetailPO>lambdaQuery();
        queryWrapper.in(CustomerSkuPriceDetailPO::getSkuId,skuIds);
        queryWrapper.eq(CustomerSkuPriceDetailPO::getYn, YnEnum.YES.getCode());
        return this.list(queryWrapper);
    }

    /**
     * 根据客户代码和商品ID查询客户商品价格列表。
     *
     * @param clientCode 客户代码。
     * @param skuId      商品ID。
     * @param timestamp  the timestamp
     * @return 客户商品价格列表 。
     */
    @PFTracing
    public List<CustomerSkuPriceDetailPO> listByClientCodeAndSkuId(String clientCode, Long skuId,Long timestamp){
        if(StringUtils.isBlank(clientCode) || skuId == null || timestamp == null){
            return null;
        }

        LambdaQueryWrapper<CustomerSkuPriceDetailPO> queryWrapper = Wrappers.<CustomerSkuPriceDetailPO>lambdaQuery()
                .eq(CustomerSkuPriceDetailPO::getSkuId,skuId)
                .eq(CustomerSkuPriceDetailPO::getClientCode,clientCode)
                .eq(CustomerSkuPriceDetailPO::getEnableStatus,YnEnum.YES.getCode())
                .le(CustomerSkuPriceDetailPO::getBeginTime,timestamp)
                .ge(CustomerSkuPriceDetailPO::getEndTime,timestamp)
                ;
        return this.list(queryWrapper);
    }

    @PFTracing
    public List<CustomerSkuPriceDetailPO> listByClientCodeAndSkuIds(String clientCode, Collection<Long> skuIds, Long timestamp){
        if(StringUtils.isBlank(clientCode) || CollectionUtils.isEmpty(skuIds) || timestamp == null){
            return null;
        }

        LambdaQueryWrapper<CustomerSkuPriceDetailPO> queryWrapper = Wrappers.<CustomerSkuPriceDetailPO>lambdaQuery()
                .in(CustomerSkuPriceDetailPO::getSkuId, skuIds)
                .eq(CustomerSkuPriceDetailPO::getClientCode, clientCode)
                .eq(CustomerSkuPriceDetailPO::getEnableStatus, YnEnum.YES.getCode())
                .le(CustomerSkuPriceDetailPO::getBeginTime, timestamp)
                .ge(CustomerSkuPriceDetailPO::getEndTime, timestamp)
                ;
        return this.list(queryWrapper);
    }

    /**
     * 根据客户代码和商品ID查询客户商品价格列表。
     *
     * @param clientCode 客户代码。
     * @param skuId      商品ID。
     * @return 客户商品价格列表 。
     */
    @PFTracing
    public CustomerSkuPriceDetailPO getByClientCodeAndSkuId(String clientCode, Long skuId){
        if(StringUtils.isBlank(clientCode) || skuId == null){
            return null;
        }

        List<CustomerSkuPriceDetailPO> detailPOS = this.listByClientCodeAndSkuId(clientCode,skuId,DateUtil.getCurrentTime());
        if(CollectionUtils.isEmpty(detailPOS)) {
            return null;
        }
        detailPOS = detailPOS.stream().sorted(Comparator.comparing(CustomerSkuPriceDetailPO::getCreateTime).reversed())
                .collect(Collectors.toList());
        return detailPOS.get(0);
    }

    @PFTracing
    public Map<Long, CustomerSkuPriceDetailPO> mapByClientCodeAndSkuId(String clientCode, Collection<Long> skuIds){
        if(StringUtils.isBlank(clientCode) || CollectionUtils.isEmpty(skuIds)){
            return Maps.newHashMap();
        }

        Map<Long, CustomerSkuPriceDetailPO> result = Maps.newHashMap();

        List<CustomerSkuPriceDetailPO> detailPOS = this.listByClientCodeAndSkuIds(clientCode, skuIds, DateUtil.getCurrentTime());
        if(CollectionUtils.isEmpty(detailPOS)) {
            return Maps.newHashMap();
        }

        Map<Long, List<CustomerSkuPriceDetailPO>> detailMap = detailPOS.stream().collect(Collectors.groupingBy(CustomerSkuPriceDetailPO::getSkuId));

        detailMap.forEach((k, v) -> {
            List<CustomerSkuPriceDetailPO> list = v.stream().sorted(Comparator.comparing(CustomerSkuPriceDetailPO::getCreateTime).reversed()).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(list)) {
                result.put(k, null);
            } else {
                result.put(k, list.get(0));
            }
        });
        return result;
    }


    /**
     * List by effective status list.
     *
     * @param clientCode      the client code
     * @param skuId           the sku id
     * @param effectiveStatus the effective status
     * @return the list
     */
    public List<CustomerSkuPriceDetailPO> listByEffectiveStatus(String clientCode, Long skuId,Integer effectiveStatus){
        LambdaQueryWrapper<CustomerSkuPriceDetailPO> queryWrapper = Wrappers.<CustomerSkuPriceDetailPO>lambdaQuery()
                .eq(CustomerSkuPriceDetailPO::getSkuId,skuId)
                .eq(CustomerSkuPriceDetailPO::getClientCode,clientCode)
                .eq(CustomerSkuPriceDetailPO::getEffectiveStatus,effectiveStatus)
                .eq(CustomerSkuPriceDetailPO::getEnableStatus,YnEnum.YES.getCode());
        return this.list(queryWrapper);
    }


    /**
     * 扫描当前生效但3分钟内即将过期的记录 @return the list
     */
    public List<CustomerSkuPriceDetailPO> lisExpiringSoonItem(){
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return super.getBaseMapper().lisExpiringSoonItem(calendar.getTimeInMillis());
    }

    /**
     * 列举生效但过期的数据 @return the list
     */
    public List<CustomerSkuPriceDetailPO> lisExpiredItem(){
        LambdaQueryWrapper<CustomerSkuPriceDetailPO> queryWrapper = Wrappers.<CustomerSkuPriceDetailPO>lambdaQuery()
                .eq(CustomerSkuPriceDetailPO::getEffectiveStatus,1)
                .le(CustomerSkuPriceDetailPO::getEndTime,System.currentTimeMillis())
                .eq(CustomerSkuPriceDetailPO::getEnableStatus,YnEnum.YES.getCode())
                .last("limit 5000");
        return this.list(queryWrapper);
    }

    /**
     * 根据Id更新有效状态 @param po the po
     *
     * @return the boolean
     */
    public boolean updateEffectiveStatusById(CustomerSkuPriceDetailPO po){
        LambdaUpdateWrapper<CustomerSkuPriceDetailPO> wrapper = Wrappers.<CustomerSkuPriceDetailPO>lambdaUpdate()
                .set(CustomerSkuPriceDetailPO::getEffectiveStatus,po.getEffectiveStatus())
                .set(StringUtils.isNotBlank(po.getRemark()),CustomerSkuPriceDetailPO::getRemark,po.getRemark())
                .set(CustomerSkuPriceDetailPO::getUpdateTime,System.currentTimeMillis())
                .eq(CustomerSkuPriceDetailPO::getId,po.getId());
        return super.update(wrapper);
    }

    /**
     * 根据客户代码和商品ID查询客户商品价格列表。
     *
     * @param clientCode 客户代码。
     * @param skuId      商品ID。
     * @return 客户商品价格列表 。
     */
    @PFTracing
    public List<CustomerSkuPriceDetailPO> getListByClientCodeAndSkuId(String clientCode, Long skuId){
        if(StringUtils.isBlank(clientCode) || skuId == null){
            return null;
        }

        LambdaQueryWrapper<CustomerSkuPriceDetailPO> queryWrapper = Wrappers.<CustomerSkuPriceDetailPO>lambdaQuery()
                .eq(CustomerSkuPriceDetailPO::getSkuId,skuId)
                .eq(CustomerSkuPriceDetailPO::getClientCode,clientCode)
                .eq(CustomerSkuPriceDetailPO::getEnableStatus,YnEnum.YES.getCode())
                ;

        return this.list(queryWrapper);
    }

    /**
     * 更新客户商品价格的不可售阈值
     * @param update 客户商品价格明细对象，包含需要更新的id和不可售阈值等信息，不能为null且id不能为空
     * @param optimisticLock 是否启用乐观锁校验，为true时会添加阈值条件校验
     */
    public void updateUnsellableThreshold(CustomerSkuPriceDetailPO update, boolean optimisticLock) {
        Preconditions.checkArgument(update != null && update.getId() != null, "id不能为空");
        Preconditions.checkArgument(update.getUnsellableThreshold() != null, "不可售阈值不能为空");

        // 设置更新人和更新时间，MyMetaObjectHandler 此时失效，手动设置更新人和更新时间
        BasicPOHelper.setUpdateInfo(update);

        LambdaUpdateWrapper<CustomerSkuPriceDetailPO> updateWrapper = Wrappers.lambdaUpdate(CustomerSkuPriceDetailPO.class)
                .set(CustomerSkuPriceDetailPO::getUnsellableThreshold, update.getUnsellableThreshold())
                .set(CustomerSkuPriceDetailPO::getUnsellableThresholdTime, new Date().getTime())
                .set(CustomerSkuPriceDetailPO::getUpdater, update.getUpdater())
                .set(CustomerSkuPriceDetailPO::getUpdateTime, update.getUpdateTime())

                .eq(CustomerSkuPriceDetailPO::getId, update.getId())
                .eq(CustomerSkuPriceDetailPO::getYn, YnEnum.YES.getCode());
        boolean execute = super.update(updateWrapper);

        if (optimisticLock) {
            updateWrapper.ge(CustomerSkuPriceDetailPO::getUnsellableThreshold, update.getUnsellableThreshold());
        }

        if (!execute) {
            log.warn("不可售阈值发生变化，更新不可售阈值失败. update={}", JSONObject.toJSONString(update));
            throw new ProductBizException("不可售阈值发生变化，更新不可售阈值失败. id=%s", update.getId());
        }
    }

    /**
     * 更新不可售阈值
     * @param id 客制化价格明细ID
     * @param unsellableThreshold 新的不可售阈值
     */
    public void updateUnsellableThreshold(Long id, BigDecimal unsellableThreshold, String updater) {
        CustomerSkuPriceDetailPO customerSkuPriceDetailPO = this.getValidById(id);

        if (customerSkuPriceDetailPO == null) {
            log.error("更新不可售阈值，找不到客制化价格明细. id={}", id);
            return;
        }

        CustomerSkuPriceDetailPO update = new CustomerSkuPriceDetailPO();

        update.setUnsellableThreshold(unsellableThreshold);
        update.setId(id);
        update.setUpdater(updater);


        this.updateUnsellableThreshold(update, false);
    }

    public List<CustomerSkuPriceDetailPO> listValidByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();

        }
        LambdaQueryWrapper<CustomerSkuPriceDetailPO> wrapper = Wrappers.<CustomerSkuPriceDetailPO>lambdaQuery()
                .in(CustomerSkuPriceDetailPO::getId, ids)
                .eq(CustomerSkuPriceDetailPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }


    public void updateAvailableSaleStatusList(List<CustomerSkuPriceDetailPO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }

        // 排序
        CollectionUtil.sort(updateList, Comparator.comparing(CustomerSkuPriceDetailPO::getId, Comparator.nullsLast(Long::compareTo)));

        for (CustomerSkuPriceDetailPO item : updateList) {
            this.updateAvailableSaleStatus(item);
        }
    }

    public void updateAvailableSaleStatus(CustomerSkuPriceDetailPO update) {
        Preconditions.checkArgument(update.getId() != null, "id不能为空");

        LambdaUpdateWrapper<CustomerSkuPriceDetailPO> updateWrapper = Wrappers.lambdaUpdate(CustomerSkuPriceDetailPO.class)
                .set(CustomerSkuPriceDetailPO::getAvailableSaleStatus, update.getAvailableSaleStatus())
                .set(CustomerSkuPriceDetailPO::getAvailableSaleStatusTime, new Date().getTime())
                .set(CustomerSkuPriceDetailPO::getUpdateTime, new Date().getTime())
                .set(CustomerSkuPriceDetailPO::getUpdater, update.getUpdater())

                .eq(CustomerSkuPriceDetailPO::getId, update.getId())
                .eq(CustomerSkuPriceDetailPO::getYn, YnEnum.YES.getCode())
                .eq(CustomerSkuPriceDetailPO::getUpdateTime, update.getUpdateTime());

        boolean flag = super.update(updateWrapper);

        log.info("更新vip价可售状态. result=[{}], update={}", flag, JSONObject.toJSONString(update));

        if (!flag) {
            log.info("更新vip价可售状态失败, update={}", JSONObject.toJSONString(update));
            throw new ProductBizException("客制化价格正在进行其他业务操作，请稍后重试 %s", update.getSkuId());
        }
    }

    public Map<Long, CustomerSkuPriceDetailPO> getMapByIds(List<Long> ids) {

        LambdaQueryWrapper<CustomerSkuPriceDetailPO> wrapper = Wrappers.<CustomerSkuPriceDetailPO>lambdaQuery()
                .in(CustomerSkuPriceDetailPO::getId, ids)
                .eq(CustomerSkuPriceDetailPO::getYn, YnEnum.YES.getCode());
        List<CustomerSkuPriceDetailPO> list = super.list(wrapper);
        return list.stream().collect(Collectors.toMap(CustomerSkuPriceDetailPO::getId, Function.identity(), (a, b) -> a));
    }

    public void updatePoolStatus(List<CustomerSkuPriceDetailPO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }

        for (CustomerSkuPriceDetailPO update : updateList) {
            LambdaUpdateWrapper<CustomerSkuPriceDetailPO> updateWrapper = Wrappers.lambdaUpdate(CustomerSkuPriceDetailPO.class)
                    .set(CustomerSkuPriceDetailPO::getCustomerMkuPoolStatus, update.getCustomerMkuPoolStatus())

                    .eq(CustomerSkuPriceDetailPO::getId, update.getId())
                    .eq(CustomerSkuPriceDetailPO::getYn, YnEnum.YES.getCode());
            boolean result = super.update(updateWrapper);
            log.info("updatePoolStatus, result={}", result);
        }
    }
}
