package com.jdi.isc.product.soa.service.manage.brand.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.domain.brand.biz.BrandAuditRecordVO;
import com.jdi.isc.product.soa.domain.brand.po.BrandAuditRecordPO;
import com.jdi.isc.product.soa.service.atomic.brand.BrandAuditRecordAtomicService;
import com.jdi.isc.product.soa.service.manage.brand.BrandAuditRecordManageService;
import com.jdi.isc.product.soa.service.mapstruct.brand.BrandConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/2
 **/
@Slf4j
@Service
public class BrandAuditRecordManageServiceImpl extends BaseManageSupportService<BrandAuditRecordVO, BrandAuditRecordPO> implements BrandAuditRecordManageService {

    @Resource
    private BrandAuditRecordAtomicService brandAuditRecordAtomicService;

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 30)
    public boolean addBrandAuditRecord(BrandAuditRecordVO brandAuditRecordVO) {
        BrandAuditRecordPO brandAuditRecordPO = BrandConvert.INSTANCE.recordVO2PO(brandAuditRecordVO);
        // 增加审核记录
        return brandAuditRecordAtomicService.saveOrUpdate(brandAuditRecordPO);
    }

    @Override
    public BrandAuditRecordVO getBrandAuditRecordVOByBrandId(Long brandId) {

        LambdaQueryWrapper<BrandAuditRecordPO> queryWrapper = Wrappers.lambdaQuery(BrandAuditRecordPO.class).eq(BrandAuditRecordPO::getBrandId, brandId).eq(BrandAuditRecordPO::getYn, YnEnum.YES.getCode());
        List<BrandAuditRecordPO> list = brandAuditRecordAtomicService.list(queryWrapper);

        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        Optional<BrandAuditRecordPO> max = list.stream().filter(Objects::nonNull).max(Comparator.comparing(BrandAuditRecordPO::getUpdateTime));
        return max.map(BrandConvert.INSTANCE::recordPO2VO).orElse(null);
    }
}
