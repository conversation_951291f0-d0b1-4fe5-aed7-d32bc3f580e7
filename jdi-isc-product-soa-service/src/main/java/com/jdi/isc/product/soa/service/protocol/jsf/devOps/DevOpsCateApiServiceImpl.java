package com.jdi.isc.product.soa.service.protocol.jsf.devOps;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.devOps.DevOpsCateApiService;
import com.jdi.isc.product.soa.api.price.res.ProfitRateApiDTO;
import com.jdi.isc.product.soa.service.manage.tool.CategoryTookService;
import com.jdi.isc.product.soa.service.manage.tool.SkuDraftToolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description：DevOpsCateApiServiceImpl
 * @Date 2025-06-09
 */
@Slf4j
@Service
public class DevOpsCateApiServiceImpl implements DevOpsCateApiService {

    @Resource
    private CategoryTookService categoryTookService;

    @Resource
    private SkuDraftToolService skuDraftToolService;

    @Override
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> updateJdCategoryForCategory(String env, Set<Long> catIds) {
        //String result = categoryTookService.updateJdCategoryForCategory(env,4, catIds);
        return DataResponse.success();
    }

    @Override
    @ToolKit(exceptionWrap = true, validFlag = false)
    public DataResponse<String> updateCatLangJdCategory(String env, Set<Long> catIds) {
        String result = categoryTookService.updateCatLangJdCategory(env, catIds);
        return DataResponse.success(result);
    }

    @Override
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> updateSkuJdCategory(String env, Set<Long> skuIds) {
        String result = categoryTookService.updateSkuJdCategory(env, skuIds);
        return DataResponse.success(result);
    }

    @Override
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> updateMkuJdCategory(String env, Set<Long> mkuIds) {
        String result = categoryTookService.updateMkuJdCategory(env, mkuIds);
        return DataResponse.success(result);
    }

    @Override
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> updateSpuDraftJdCategory(String env, Set<Long> spuIds) {
        String result = categoryTookService.updateSpuDraftJdCategory(env, spuIds);
        return DataResponse.success(result);
    }

    @Override
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> updateJdCategoryForLowData(String env, Set<Long> categoryIds) {
        String result = categoryTookService.updateJdCategoryForLowData(env, categoryIds);
        return DataResponse.success(result);
    }

    @Override
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> updateCountryMkuJdCategory(String env, Set<Long> mkuIds) {
        String result = categoryTookService.updateCountryMkuJdCategory(env, mkuIds);
        return DataResponse.success(result);
    }

    @Override
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> updateCustomerMkuJdCategory(String env, Set<Long> mkuIds) {
        String result = categoryTookService.updateCustomerMkuJdCategory(env, mkuIds);
        return DataResponse.success(result);
    }

    @Override
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> updateBusinessLineJdCategory(String env, Set<Long> businessLineIds) {
        String result = categoryTookService.updateBusinessLineJdCategory(env, businessLineIds);
        return DataResponse.success(result);
    }

    @Override
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> updateMakeUpJdCategory(String env, Set<Long> catIds) {
        String result = categoryTookService.updateMakeUpJdCategory(env, catIds);
        return DataResponse.success(result);
    }

    @Override
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> updateProfitRatePOJdCategory(String env, Set<Long> catIds) {
        String result = categoryTookService.updateProfitRatePOJdCategory(env, catIds);
        return DataResponse.success(result);
    }

    @Override
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> updateCountryAgreementPriceDraftJdCategory(String env, Set<Long> lastCatIds) {
        String result = categoryTookService.updateCountryAgreementPriceDraftJdCategory(env, lastCatIds);
        return DataResponse.success(result);
    }

    @Override
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> updateCountryAgreementPriceJdCategory(String env, Set<Long> lastCatIds) {
        String result = categoryTookService.updateCountryAgreementPriceJdCategory(env, lastCatIds);
        return DataResponse.success(result);
    }

    @Override
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> updateCountryAgreementPriceWarningPOJdCategory(String env, Set<Long> lastCatIds) {
        String result = categoryTookService.updateCountryAgreementPriceWarningPOJdCategory(env, lastCatIds);
        return DataResponse.success(result);
    }

    @Override
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> updateCountryExtendPriceJdCategory(String env, Set<Long> lastCatIds) {
        String result = categoryTookService.updateCountryExtendPriceJdCategory(env, lastCatIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> updateJdCategoryForCategoryByLevel(String env, Integer level, Set<Long> catIds) {
        String result = categoryTookService.updateJdCategoryForCategory(env, level,catIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> updateThirdCategoryJdCategoryForFourCategory(String env, Set<Long> catIds) {
        String result = categoryTookService.updateThirdCategoryJdCategoryForFourCategory(env, catIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> updateMkuEsJdCategory(String env, Set<Long> mkuIds) {
        String result = categoryTookService.updateMkuEsJdCategory(env, mkuIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> updateProfitRateById(List<ProfitRateApiDTO> list) {
        String result = categoryTookService.updateProfitRateById(list);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> updateCategoryAttributePOJdCategory(String env, Set<Long> categoryIds) {
        String result = categoryTookService.updateCategoryAttributePOJdCategory(env,categoryIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> updateCustomerSkuPriceDraftPOJdCategory(String env, Set<Long> categoryIds) {
        String result = categoryTookService.updateCustomerSkuPriceDraftPOJdCategory(env,categoryIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> updateCategoryLeafByCatIds(String env, Integer leaf, Set<Long> catIds) {
        String result = categoryTookService.updateCategoryLeafByCatIds(env, leaf, catIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> updateCategoryYnByCatIds(String env, Integer yn, Set<Long> catIds) {
        String result = categoryTookService.updateCategoryYnByCatIds(env, yn, catIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> updateCategoryDefinitionJdCatIdByCatIds(String env, Long start, Set<Long> catIds) {
        String result = categoryTookService.updateCategoryDefinitionJdCatIdByCatIds(env, start, catIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> updateCategoryDefinitionJdParentCatIdByCatIds(String env, Set<Long> catIds) {
        String result = categoryTookService.updateCategoryDefinitionJdParentCatIdByCatIds(env,catIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> updateCategoryDefaultSaleAttribute(String env, Set<Long> catIds) {
        String result = categoryTookService.updateCategoryDefaultSaleAttribute(env,catIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> removeRepeatBusinessLine(String env, Set<Long> businessLineIds) {
        String result = categoryTookService.removeRepeatBusinessLine(env,businessLineIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> updateCategoryNameFromGms(String env, Set<Long> catIds) {
        String result = categoryTookService.updateCategoryNameFromGms(env,catIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> updateSonCategoryFromGms(String env, Set<Long> catIds) {
        String result = categoryTookService.updateSonCategoryFromGms(env,catIds);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> jadeTestExecutor(String env) {
        String result = categoryTookService.jadeTestExecutor(env);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<String> updateSkuDraftJdSkuId(String env, Set<Long> skuIds) {
        String result = skuDraftToolService.updateSkuDraftJdSkuId(env, skuIds);
        return DataResponse.success(result);
    }
}
