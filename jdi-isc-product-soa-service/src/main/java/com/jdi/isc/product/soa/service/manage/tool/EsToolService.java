package com.jdi.isc.product.soa.service.manage.tool;


import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientDetailReqApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuPoolFlagDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuClientDetailReqVO;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.manage.mku.MkuEsManageService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuEsAtomicService;
import com.jdi.isc.product.soa.service.manage.mku.MkuManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @description：es增加功能后工具类
 * @Date 2025-06-17
 */
@Slf4j
@Service
public class EsToolService {


    @Resource
    private CountryMkuAtomicService countryMkuAtomicService;
    @Resource
    private MkuManageService mkuManageService;
    @Resource
    private MkuEsManageService mkuEsManageService;
    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;
    @Resource
    private MkuEsAtomicService mkuEsAtomicService;


    private static final String uat_env = "uat_env";

    final ExecutorService pool = new ThreadPoolExecutor(10, 50, 30L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(10000), new ThreadFactoryBuilder().setNamePrefix("product-soa-updateEs-").build());

    /**
     * 更新MKU ES的京东类目ID
     * @return
     */
    public String updateMkuEsPoolFlag(String env,String targetCountryCode,Set<Long> mkuIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            throw new BizException("环境参数不能为空，或者不正确");
        }

        if (StringUtils.isBlank(targetCountryCode)) {
            throw new BizException("目标国家不可为空");
        }

        LambdaQueryWrapper<CountryMkuPO> qw = new LambdaQueryWrapper<CountryMkuPO>();
        qw.select(CountryMkuPO::getTargetCountryCode,CountryMkuPO::getMkuId,CountryMkuPO::getPoolStatus);
        qw.eq(CountryMkuPO::getTargetCountryCode,targetCountryCode);
        qw.in(CollectionUtils.isNotEmpty(mkuIds),CountryMkuPO::getMkuId,mkuIds);
        qw.eq(CountryMkuPO::getYn,YnEnum.YES.getCode());
        List<CountryMkuPO> list = countryMkuAtomicService.list(qw);

        int total = list.size();

        List<List<CountryMkuPO>> partitionList = Lists.partition(list, 10);
        AtomicLong count = new AtomicLong(0);
        for (List<CountryMkuPO> subList : partitionList) {
            List<CompletableFuture<Boolean>> completableFutureList = Lists.newArrayListWithExpectedSize(subList.size());
            for (CountryMkuPO countryMkuPO : subList) {
                count.getAndIncrement();
                completableFutureList.add(CompletableFuture.supplyAsync(() -> updatePoolFlagEsSync(countryMkuPO, count, total), pool));
            }

            try {
                // 取回结果，等候5秒
                CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).get(10, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("updateMkuEsJdCategory 更新MKU ES的京东类目ID 取回结果异常",e);
            }
        }
        return Constant.SUCCESS;
    }

    private boolean updatePoolFlagEsSync(CountryMkuPO countryMkuPO, AtomicLong count, int total) {
        try {
            MkuPoolFlagDTO input = new MkuPoolFlagDTO();
            input.setMkuId(countryMkuPO.getMkuId());
            input.setTargetCountryCode(countryMkuPO.getTargetCountryCode());

            boolean updated = mkuEsManageService.updateMkuPoolFlag(input);
            if (updated) {
                log.info("EsToolService.updatePoolFlagEsSync 更新MKU ES入池状态 mkuId={}，更新ES结果成功:{}",countryMkuPO.getMkuId(), updated);
            }else {
                log.warn("EsToolService.updatePoolFlagEsSync 更新MKU ES入池状态 mkuId={}，更新ES结果失败:{}",countryMkuPO.getMkuId(), updated);
            }
        } catch (Exception e) {
            log.error("EsToolService.updatePoolFlagEsSync 更新MKU ES入池状态异常：mkuId={}",JSON.toJSONString(countryMkuPO),e);
        }finally {
            log.info("EsToolService.updatePoolFlagEsSync 更新MKU ES入池状态结束 count={},total={}", count, total);
        }
        return true;
    }

    /**
     * 更新MKU ES的京东类目ID
     * @return
     */
    public String initEsData(String env,String targetCountryCode,String clientCode,Set<Long> mkuIds) {
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            throw new BizException("环境参数不能为空，或者不正确");
        }

        if (StringUtils.isBlank(targetCountryCode) && StringUtils.isBlank(clientCode)) {
            throw new BizException("目标国家或客户编码不可为空");
        }

        LambdaQueryWrapper<CustomerMkuPO> qw = new LambdaQueryWrapper<CustomerMkuPO>();
        qw.select(CustomerMkuPO::getTargetCountryCode,CustomerMkuPO::getMkuId,CustomerMkuPO::getClientCode);
        qw.eq(StringUtils.isNotBlank(targetCountryCode),CustomerMkuPO::getTargetCountryCode,targetCountryCode);
        qw.eq(StringUtils.isNotBlank(clientCode),CustomerMkuPO::getClientCode,clientCode);
        qw.in(CollectionUtils.isNotEmpty(mkuIds),CustomerMkuPO::getMkuId,mkuIds);
        qw.eq(CustomerMkuPO::getBindStatus, CustomerMkuBindEnum.BIND);
        qw.eq(CustomerMkuPO::getYn,YnEnum.YES.getCode());
        List<CustomerMkuPO> list = customerMkuAtomicService.list(qw);

        int total = list.size();

        List<List<CustomerMkuPO>> partitionList = Lists.partition(list, 50);
        AtomicLong count = new AtomicLong(0);
        for (List<CustomerMkuPO> subList : partitionList) {
            List<CompletableFuture<Boolean>> completableFutureList = Lists.newArrayListWithExpectedSize(subList.size());
            for (CustomerMkuPO customerMkuPO : subList) {
                count.getAndIncrement();
                completableFutureList.add(CompletableFuture.supplyAsync(() -> upsert2EsSync(customerMkuPO, count, total), pool));
            }

            try {
                // 取回结果，等候5秒
                CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).get(10, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("EsToolService.initEsData 更新MKU ES的京东类目ID 取回结果异常",e);
            }
        }
        return Constant.SUCCESS;
    }

    private boolean upsert2EsSync(CustomerMkuPO customerMkuPO, AtomicLong count, int total) {
        try {
            MkuClientDetailReqVO input = new MkuClientDetailReqVO();
            input.setMkuId(customerMkuPO.getMkuId());
            input.setClientCode(customerMkuPO.getClientCode());

            boolean updated = mkuEsManageService.upsert2Es(input);
            if (updated) {
                log.info("EsToolService.upsert2EsSync 更新MKU ES入池状态 mkuId={}，更新ES结果成功:{}",customerMkuPO.getMkuId(), updated);
            }else {
                log.warn("EsToolService.upsert2EsSync 更新MKU ES入池状态 mkuId={}，更新ES结果失败:{}",customerMkuPO.getMkuId(), updated);
            }
        } catch (Exception e) {
            log.error("EsToolService.upsert2EsSync 更新MKU ES入池状态异常：mkuId={}",JSON.toJSONString(customerMkuPO),e);
        }finally {
            log.info("EsToolService.upsert2EsSync 更新MKU ES入池状态结束 count={},total={}", count, total);
        }
        return true;
    }

    public Boolean deleteDocument(String env,String indexName,String id){
        if (StringUtils.isBlank(env) || !uat_env.equals(env)) {
            throw new BizException("环境参数不能为空，或者不正确");
        }

        if (StringUtils.isBlank(indexName)) {
            throw new BizException("索引名称不可为空");
        }

        if (StringUtils.isBlank(id)) {
            throw new BizException("id不可为空");
        }

        return mkuEsAtomicService.deleteDocument(indexName,id);
    }
}
