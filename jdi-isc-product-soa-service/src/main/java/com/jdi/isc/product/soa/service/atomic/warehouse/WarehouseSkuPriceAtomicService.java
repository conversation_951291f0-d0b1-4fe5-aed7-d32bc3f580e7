package com.jdi.isc.product.soa.service.atomic.warehouse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehouseSkuPricePO;
import com.jdi.isc.product.soa.repository.mapper.warehouse.WarehouseSkuPriceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * @author：xubing82
 * @date：2025/8/7 10:07
 * @description：WarehouseSkuPriceAtomicService
 */
@Slf4j
@Service
public class WarehouseSkuPriceAtomicService extends ServiceImpl<WarehouseSkuPriceMapper, WarehouseSkuPricePO> {


    /**
     * 根据国家代码、仓库ID和商品ID集合查询仓库商品价格列表。
     * @param countryCode 国家代码
     * @param warehouseId 仓库ID
     * @param skuIds 商品ID集合
     * @return 符合条件的仓库商品价格列表
     * SQL样例：——需要通过代码分组，找到最新的一条
     * select * from jdi_isc_warehouse_price_sharding where country_code='VN' and warehouse_id=1013 and sku_id in(80000094492,80000090747)
     * and create_time>=1754505985529 order by create_time desc;
     *
     */
    public List<WarehouseSkuPricePO> queryWarehouseSkuPriceListByParams(String countryCode, Long warehouseId, Set<Long> skuIds, Long createTimeStart) {
        LambdaQueryWrapper<WarehouseSkuPricePO> queryWrapper = Wrappers.lambdaQuery(WarehouseSkuPricePO.class)
                .eq(WarehouseSkuPricePO::getCountryCode, countryCode)
                .eq(WarehouseSkuPricePO::getWarehouseId, warehouseId)
                .in(WarehouseSkuPricePO::getSkuId, skuIds)
                .ge(WarehouseSkuPricePO::getCreateTime, createTimeStart)
                .eq(WarehouseSkuPricePO::getYn, YnEnum.YES.getCode())
                .orderByDesc(WarehouseSkuPricePO::getCreateTime);

        return this.getBaseMapper().selectList(queryWrapper);
    }

}
