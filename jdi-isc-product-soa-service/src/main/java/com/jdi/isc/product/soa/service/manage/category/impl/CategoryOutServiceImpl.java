package com.jdi.isc.product.soa.service.manage.category.impl;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.gms.greatdane.category.domain.Category;
import com.jd.gms.greatdane.category.request.GetCategoryByIdsParam;
import com.jd.gms.greatdane.domain.Language;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.isc.product.soa.api.category.biz.CategoryReadDTO;
import com.jdi.isc.product.soa.api.category.biz.GmsCategoryReqDTO;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.domain.category.biz.*;
import com.jdi.isc.product.soa.domain.category.po.CategoryBuyerRelationPO;
import com.jdi.isc.product.soa.domain.category.po.CategoryLangPO;
import com.jdi.isc.product.soa.domain.category.po.CategoryPO;
import com.jdi.isc.product.soa.domain.enums.StatusEnum;
import com.jdi.isc.product.soa.domain.supplier.biz.BusinessLineVO;
import com.jdi.isc.product.soa.rpc.gms.CategoryRpcService;
import com.jdi.isc.product.soa.service.atomic.category.CategoryAtomicService;
import com.jdi.isc.product.soa.service.atomic.category.CategoryBuyerRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.category.CategoryLangAtomicService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.category.JdCategorySyncManageService;
import com.jdi.isc.product.soa.service.manage.supplier.BusinessLineManageService;
import com.jdi.isc.product.soa.service.mapstruct.category.CategoryConvert;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 类目对外服务
 * @Author: zhaojianguo21
 * @Date: 2023/11/27 16:01
 **/
@Slf4j
@Service
public class CategoryOutServiceImpl implements CategoryOutService {

    @Resource
    private CategoryAtomicService categoryAtomicService;
    @Resource
    private CategoryLangAtomicService categoryLangAtomicService;
    @Resource
    @Lazy
    private BusinessLineManageService businessLineManageService;
    @Resource
    private CategoryBuyerRelationAtomicService categoryBuyerRelationAtomicService;

    @Resource
    private CategoryRpcService categoryRpcService;

    @Resource
    private JdCategorySyncManageService jdCategorySyncManageService;

    final ExecutorService pool = new ThreadPoolExecutor(5, 10, 30L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNamePrefix("product-soa-jdSync").build());

    private final static String CATEGORY_PREFIX = "categoryName_";

    @Resource
    private OperDuccConfig operDuccConfig;

    @Resource
    private JimUtils jimUtils;

    @Override
    public List<CategoryComboBoxVO> queryChildren(CategoryComboBoxReqVO input) {
        if (null==input.getId()){
            input.setId(Constant.CATEGORY_ROOT_ID);
        }
        if (StringUtils.isBlank(input.getLang())){
            input.setLang(LangConstant.LANG_ZH);
        }
        return categoryAtomicService.children(input);
    }

    @Override
    public String queryPathStr(Long categoryId, String lang) {
        Map<Long, List<CategoryComboBoxVO>> result = queryPath(new HashSet<>(Collections.singletonList(categoryId)), lang);
        if (null==result || !result.containsKey(categoryId)){
            return "";
        }

        return joinPath(result.get(categoryId));
    }

    @Override
    @PFTracing
    public List<CategoryComboBoxVO> queryPath(Long categoryId, String lang) {
        Map<Long, List<CategoryComboBoxVO>> result = queryPath(new HashSet<>(Collections.singletonList(categoryId)), lang);
        return null!=result?result.get(categoryId):null;
    }

    @Override
    public Map<Long, String> queryPathStr(Set<Long> categoryIds, String lang) {
        Map<Long, List<CategoryComboBoxVO>> pathMap = queryPath(categoryIds, lang);
        if (MapUtils.isEmpty(pathMap)){
            return new HashMap<>();
        }

        return pathMap.entrySet().stream()
                .collect(HashMap::new, (m, e)-> m.put(e.getKey(), joinPath(e.getValue())), HashMap::putAll);
    }

    @Override
    @PFTracing
    public Map<Long, List<CategoryComboBoxVO>> queryPath(Set<Long> categoryIds, String lang) {
        List<CategoryPathVO> list = categoryAtomicService.path(categoryIds,null);
        if (CollectionUtils.isEmpty(list)){
            log.warn("category path collection empty. categoryIds={}", JSONObject.toJSONString(categoryIds));
            return Maps.newHashMap();
        }
        String langQuery = StringUtils.isBlank(lang)? LangConstant.LANG_ZH:lang;

        Set<Long> catAllIds = new HashSet<>();
        list.forEach(o->{catAllIds.add(o.getId1());catAllIds.add(o.getId2());
            catAllIds.add(o.getId3());catAllIds.add(o.getId4());});
        // 避免空元素
        catAllIds.remove(null);

        LambdaQueryWrapper<CategoryLangPO> langWrapper = Wrappers.<CategoryLangPO>lambdaQuery()
                .select(CategoryLangPO::getJdCatId, CategoryLangPO::getLangName)
                .in(CategoryLangPO::getJdCatId, catAllIds)
                .eq(CategoryLangPO::getLang, langQuery)
                .eq(CategoryLangPO::getYn, YnEnum.YES.getCode());
        List<CategoryLangPO> langPOList = categoryLangAtomicService.list(langWrapper);
        if (CollectionUtils.isEmpty(langPOList)){
            log.warn("category lang collection empty. categoryIds={}", JSONObject.toJSONString(categoryIds));
            return Maps.newHashMap();
        }

        Map<Long, List<CategoryComboBoxVO>> resultFrom4 = getCategoryComboBoxMap(langPOList, list);
        return resultFrom4;
    }

    @Override
    public Map<Long, List<CategoryComboBoxVO>> queryPathByStatus(Set<Long> categoryIds, Integer status, String lang) {
        List<CategoryPathVO> list = categoryAtomicService.pathByStatus(categoryIds,status);
        if (CollectionUtils.isEmpty(list)){
            log.warn("queryPathByStatus.category path collection empty. categoryIds={}", JSONObject.toJSONString(categoryIds));
            new HashMap<>();
        }
        String langQuery = StringUtils.isBlank(lang)? LangConstant.LANG_ZH:lang;

        Set<Long> catAllIds = new HashSet<>();
        list.forEach(o->{catAllIds.add(o.getId1());catAllIds.add(o.getId2());
            catAllIds.add(o.getId3());catAllIds.add(o.getId4());});
        // 避免空元素
        catAllIds.remove(null);

        LambdaQueryWrapper<CategoryLangPO> langWrapper = Wrappers.<CategoryLangPO>lambdaQuery()
                .select(CategoryLangPO::getCatId, CategoryLangPO::getLangName,CategoryLangPO::getJdCatId)
                .in(CategoryLangPO::getJdCatId, catAllIds)
                .eq(CategoryLangPO::getLang, langQuery)
                .eq(CategoryLangPO::getYn, YnEnum.YES.getCode());
        List<CategoryLangPO> langPOList = categoryLangAtomicService.list(langWrapper);
        if (CollectionUtils.isEmpty(langPOList)){
            log.warn("queryPathByStatus.category lang collection empty. categoryIds={}", JSONObject.toJSONString(categoryIds));
            return new HashMap<>();
        }

        return getCategoryComboBoxMap(langPOList, list);
    }

    private Map<Long, List<CategoryComboBoxVO>> getCategoryComboBoxMap(List<CategoryLangPO> langPOList, List<CategoryPathVO> list) {
        Map<Long,CategoryLangPO> langPOMap = langPOList.stream().collect(Collectors.toMap(CategoryLangPO::getJdCatId, o->o));
        Map<Long, List<CategoryComboBoxVO>> resultFrom4 = list.stream().filter(boxVo-> null != boxVo.getId4()).collect(Collectors.toMap(CategoryPathVO::getId4, o->{
            CategoryComboBoxVO.CategoryComboBoxVOBuilder path1 = CategoryComboBoxVO.builder();
            path1.id(o.getId1()).level(o.getLevel1()).name(null!=langPOMap.get(o.getId1())?langPOMap.get(o.getId1()).getLangName():"").leafNodeFlag(o.getLeafNodeFlag1());

            CategoryComboBoxVO.CategoryComboBoxVOBuilder path2 = CategoryComboBoxVO.builder();
            path2.id(o.getId2()).level(o.getLevel2()).name(null!=langPOMap.get(o.getId2())?langPOMap.get(o.getId2()).getLangName():"").leafNodeFlag(o.getLeafNodeFlag2());

            CategoryComboBoxVO.CategoryComboBoxVOBuilder path3 = CategoryComboBoxVO.builder();
            path3.id(o.getId3()).level(o.getLevel3()).name(null!=langPOMap.get(o.getId3())?langPOMap.get(o.getId3()).getLangName():"").leafNodeFlag(o.getLeafNodeFlag3());

            CategoryComboBoxVO.CategoryComboBoxVOBuilder path4 = CategoryComboBoxVO.builder();
            path4.id(o.getId4()).level(o.getLevel4()).name(null!=langPOMap.get(o.getId4())?langPOMap.get(o.getId4()).getLangName():"").leafNodeFlag(o.getLeafNodeFlag4());
            return new ArrayList<CategoryComboBoxVO>(Arrays.asList(path1.build(), path2.build(), path3.build(), path4.build()));
        }));

        Map<Long, List<CategoryComboBoxVO>> resultFrom3 = list.stream().filter(boxVo-> null == boxVo.getId4()).collect(Collectors.toMap(CategoryPathVO::getId3, o->{
            CategoryComboBoxVO.CategoryComboBoxVOBuilder path1 = CategoryComboBoxVO.builder();
            path1.id(o.getId1()).level(o.getLevel1()).name(null!=langPOMap.get(o.getId1())?langPOMap.get(o.getId1()).getLangName():"").leafNodeFlag(o.getLeafNodeFlag1());

            CategoryComboBoxVO.CategoryComboBoxVOBuilder path2 = CategoryComboBoxVO.builder();
            path2.id(o.getId2()).level(o.getLevel2()).name(null!=langPOMap.get(o.getId2())?langPOMap.get(o.getId2()).getLangName():"").leafNodeFlag(o.getLeafNodeFlag2());

            CategoryComboBoxVO.CategoryComboBoxVOBuilder path3 = CategoryComboBoxVO.builder();
            path3.id(o.getId3()).level(o.getLevel3()).name(null!=langPOMap.get(o.getId3())?langPOMap.get(o.getId3()).getLangName():"").leafNodeFlag(o.getLeafNodeFlag3());

            return new ArrayList<CategoryComboBoxVO>(Arrays.asList(path1.build(), path2.build(), path3.build()));
        }));
        resultFrom4.putAll(resultFrom3);
        return resultFrom4;
    }

    @Override
    public List<Long> categoryLevel4From1234Set(Set<Long> parentCatId){
        return categoryAtomicService.categoryLevel4From1234Set(parentCatId);
    }

    @Override
    public List<Long> categoryLevel4From1234(Long parentCatId){
        return categoryAtomicService.categoryLevel4From1234(parentCatId);
    }

    @Override
    public List<CategoryComboBoxVO> queryPath(Set<Long> categoryIds, List<String> langs) {
        List<CategoryPathVO> list = categoryAtomicService.path(categoryIds,null);
        if (CollectionUtils.isEmpty(list)){
            log.warn("category path collection empty. categoryIds={}", JSONObject.toJSONString(categoryIds));
            return new ArrayList<>();
        }

        if(CollectionUtils.isEmpty(langs)){
            langs = new ArrayList<>();
            langs.add(LangConstant.LANG_ZH);
        }


        Set<Long> catAllIds = new HashSet<>();
        list.forEach(o->{catAllIds.add(o.getId1());catAllIds.add(o.getId2());
            catAllIds.add(o.getId3());catAllIds.add(o.getId4());});
        // 避免空元素
        catAllIds.remove(null);

        LambdaQueryWrapper<CategoryLangPO> langWrapper = Wrappers.<CategoryLangPO>lambdaQuery()
                .select(CategoryLangPO::getCatId, CategoryLangPO::getLang,CategoryLangPO::getLangName,CategoryLangPO::getJdCatId)
                .in(CategoryLangPO::getJdCatId, catAllIds)
                .in(CategoryLangPO::getLang, langs)
                .eq(CategoryLangPO::getYn, YnEnum.YES.getCode());
        List<CategoryLangPO> langPOList = categoryLangAtomicService.list(langWrapper);
        if (CollectionUtils.isEmpty(langPOList)){
            log.warn("category lang collection empty. categoryIds={}", JSONObject.toJSONString(categoryIds));
            return new ArrayList<>();
        }

        Map<String,CategoryLangPO> langPOMap = langPOList.stream()
                .collect(Collectors.toMap(item->StringUtils.joinWith("_",item.getJdCatId(),item.getLang()), Function.identity()));

        List<CategoryComboBoxVO> results = new ArrayList<>();
        for(CategoryPathVO item : list){
            for(String lang: langs){
                CategoryComboBoxVO.CategoryComboBoxVOBuilder path1 = CategoryComboBoxVO.builder();
                path1.id(item.getId1()).level(item.getLevel1());
                CategoryLangPO langPO1 = langPOMap.get(StringUtils.joinWith("_",item.getId1(),lang));
                if(langPO1 != null){
                    path1.name(langPO1.getLangName());
                }
                results.add(path1.build());

                CategoryComboBoxVO.CategoryComboBoxVOBuilder path2 = CategoryComboBoxVO.builder();
                path2.id(item.getId2()).level(item.getLevel2());
                CategoryLangPO langPO2 = langPOMap.get(StringUtils.joinWith("_",item.getId2(),lang));
                if(langPO2 != null){
                    path2.name(langPO2.getLangName());
                }
                results.add(path2.build());

                CategoryComboBoxVO.CategoryComboBoxVOBuilder path3 = CategoryComboBoxVO.builder();
                path3.id(item.getId3()).level(item.getLevel3());
                CategoryLangPO langPO3 = langPOMap.get(StringUtils.joinWith("_",item.getId3(),lang));
                if(langPO3 != null){
                    path3.name(langPO3.getLangName());
                }
                results.add(path3.build());

                if (Objects.nonNull(item.getId4())){
                    CategoryComboBoxVO.CategoryComboBoxVOBuilder path4 = CategoryComboBoxVO.builder();
                    path4.id(item.getId4()).level(item.getLevel4());
                    CategoryLangPO langPO4 = langPOMap.get(StringUtils.joinWith("_",item.getId4(),lang));
                    if(langPO4 != null){
                        path4.name(langPO4.getLangName());
                    }
                    results.add(path4.build());
                }
            }
        }

        return results;
    }

    @Override
    public CategoryVO queryCategoryById(Long categoryId) {
        LambdaQueryWrapper<CategoryPO> queryWrapper = new LambdaQueryWrapper<CategoryPO>().select(CategoryPO::getId,CategoryPO::getYn,CategoryPO::getStatus,CategoryPO::getCatLevel,CategoryPO::getLeafNodeFlag,CategoryPO::getJdCatId)
                .eq(CategoryPO::getJdCatId, categoryId).eq(CategoryPO::getYn, YnEnum.YES.getCode());
        CategoryPO categoryPo = categoryAtomicService.getOne(queryWrapper);
        if (Objects.isNull(categoryPo)) {
            return null;
        }
        return CategoryConvert.INSTANCE.po2dto(categoryPo);
    }

    @Override
    @PFTracing
    public Map<Long, CategoryPathVO> queryPath(Set<Long> categoryIds) {
        List<CategoryPathVO> list = categoryAtomicService.path(categoryIds,null);
        if (CollectionUtils.isEmpty(list)){
            log.warn("category path collection empty. categoryIds={}", JSONObject.toJSONString(categoryIds));
            return Maps.newHashMap();
        }

        Map<Long, CategoryPathVO> groupedByLevel3 = list.stream()
                .filter(path -> path.getId4() == null)
                .collect(Collectors.toMap(CategoryPathVO::getId3,Function.identity()));

        Map<Long, CategoryPathVO> groupedByLevel4 = list.stream()
                .filter(path -> path.getId4() != null)
                .collect(Collectors.toMap(CategoryPathVO::getId4,Function.identity()));
        // 合并两个 Map
        groupedByLevel4.putAll(groupedByLevel3);
        return groupedByLevel4;
    }

    @Override
    public Map<Long, Map<String, String>> queryCatLangNameMap(Set<Long> catIds, Set<String> langs) {
        LambdaQueryWrapper<CategoryLangPO> queryWrapper = Wrappers.lambdaQuery(CategoryLangPO.class).in(CategoryLangPO::getJdCatId, catIds).in(CategoryLangPO::getLang,langs).eq(CategoryLangPO::getYn, YnEnum.YES.getCode());
        List<CategoryLangPO> categoryPOList = categoryLangAtomicService.getBaseMapper().selectList(queryWrapper);

        return Optional.ofNullable(categoryPOList).orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(CategoryLangPO::getJdCatId,Collectors.toMap(CategoryLangPO::getLang,CategoryLangPO::getLangName)));
    }

    /**
     * 拼接类目路径
     * @param pathList 类目路径集合
     * @return
     */
    @Override
    public String joinPath(List<CategoryComboBoxVO> pathList){
        if (CollectionUtils.isEmpty(pathList)){
            return "";
        }
        StringJoiner joiner = new StringJoiner(Constant.CATEGORY_PATH_SEPARATOR);
        pathList.forEach(o->{
            joiner.add(o.getName());
        });
        return joiner.toString();
    }

    @Override
    public List<CategoryVO> queryChildByIds(Set<Long> catIds) {
        List<CategoryPO> categoryList = categoryAtomicService.queryChildByIds(catIds);
        return CategoryConvert.INSTANCE.listPo2dto(categoryList);
    }

    @Override
    public Map<Long, List<CategoryVO>> queryCategoryByIds(Set<Long> catIds,Set<String> langSet,Integer status,Boolean onlyFormalCat) {
        // 一级、二级、三级、四级关系
        List<CategoryPathVO> list = new ArrayList<>();
        if(onlyFormalCat){
            list= categoryAtomicService.pathByStatusFormalCat(catIds,status,operDuccConfig.getFilterErp());
        }else {
            list= categoryAtomicService.path(catIds,status);
        }

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        // 全部类目ID
        Set<Long> catAllIds = new HashSet<>();
        list.forEach(o->{catAllIds.add(o.getId1());catAllIds.add(o.getId2());
        catAllIds.add(o.getId3());catAllIds.add(o.getId4());});
        // 避免空元素
        catAllIds.remove(null);

        List<CategoryPO> categoryList = categoryAtomicService.queryCategoryByIds(catAllIds);
        List<CategoryVO> categoryVOList = CategoryConvert.INSTANCE.listPo2dto(categoryList);
        if (CollectionUtils.isEmpty(categoryVOList)) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<CategoryLangPO> langWrapper = Wrappers.<CategoryLangPO>lambdaQuery()
                .select(CategoryLangPO::getCatId,CategoryLangPO::getLang, CategoryLangPO::getLangName,CategoryLangPO::getJdCatId)
                .in(CategoryLangPO::getJdCatId, catAllIds)
                .in(CollectionUtils.isNotEmpty(langSet), CategoryLangPO::getLang, langSet)
                .eq(CategoryLangPO::getYn, YnEnum.YES.getCode());
        List<CategoryLangPO> langPOList = categoryLangAtomicService.list(langWrapper);
        if (CollectionUtils.isNotEmpty(langPOList)){
            Map<Long, List<CategoryLangPO>> catLangMap = langPOList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(CategoryLangPO::getJdCatId));

            categoryVOList.forEach(item -> {
                if (catLangMap.containsKey(item.getJdCatId())){
                    item.setLangList(CategoryConvert.INSTANCE.listLangPo2Vo(catLangMap.get(item.getJdCatId())));
                }
            });
        }
        Map<Long, CategoryVO> categoryVOMap = categoryVOList.stream().collect(Collectors.toMap(CategoryVO::getJdCatId, Function.identity()));

        Map<Long,List<CategoryVO>> fourthCategoriesResult = Maps.newHashMap();
        list.forEach(categoryPathVO -> {
            if (categoryVOMap.containsKey(categoryPathVO.getId4()) && Objects.nonNull(categoryPathVO.getId4())) {
                fourthCategoriesResult.put(categoryPathVO.getId4(), Lists.newArrayList(categoryVOMap.get(categoryPathVO.getId1()),categoryVOMap.get(categoryPathVO.getId2()),categoryVOMap.get(categoryPathVO.getId3()),categoryVOMap.get(categoryPathVO.getId4())));
            }else if (categoryVOMap.containsKey(categoryPathVO.getId3())) {
                fourthCategoriesResult.put(categoryPathVO.getId3(), Lists.newArrayList(categoryVOMap.get(categoryPathVO.getId1()),categoryVOMap.get(categoryPathVO.getId2()),categoryVOMap.get(categoryPathVO.getId3())));
            }
        });

        return fourthCategoriesResult;
    }

    @Override
    public Map<Long, List<CategoryVO>> queryCategory(Long catId,Set<String> langSet,Boolean onlyFormalCat) {
        Set<Long> catIds = new HashSet<>();
        if(catId != null){
            catIds.add(catId);
        }
        return this.queryCategoryByIds(catIds,langSet,YnEnum.YES.getCode(),onlyFormalCat);
    }

    @Override
    public Set<Long> queryLastCatId(CategoryIdVO categoryIdVO) {
        Long firstCatId = categoryIdVO.getFirstCatId();
        Long secondCatId = categoryIdVO.getSecondCatId();
        Long thirdCatId = categoryIdVO.getThirdCatId();
        Long lastCatId = categoryIdVO.getLastCatId();

        Set<Long> result = new HashSet<>();
        if(lastCatId != null){
            result.add(lastCatId);
            return result;
        }

        if(thirdCatId != null){
            List<Long> thirdCatIds = this.categoryLevel4From1234(thirdCatId);
            if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(thirdCatIds)){
                result.addAll(thirdCatIds);
            }
            return result;
        }

        if(secondCatId != null){
            List<Long> secondCatIds = this.categoryLevel4From1234(secondCatId);
            if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(secondCatIds)){
                result.addAll(secondCatIds);
            }
            return result;
        }

        if(firstCatId != null){
            List<Long> firstCatIds = this.categoryLevel4From1234(firstCatId);
            if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(firstCatIds)){
                result.addAll(firstCatIds);
            }
            return result;
        }
        return new HashSet<>();
    }

    @Override
    public List<CategoryTreeVo> queryListBySupplierCode(String supplierCode) {
        if (StringUtils.isBlank(supplierCode)) {
            throw new BizException(DataResponseCode.PARAM_ERROR.getCode(), DataResponseCode.PARAM_ERROR.getMessage());
        }
        BusinessLineVO businessLineVO = new BusinessLineVO();
        businessLineVO.setSupplierCode(supplierCode);
        String lang = LangContextHolder.get();
        businessLineVO.setLangId(StringUtils.isBlank(lang) ? LangConstant.LANG_ZH:lang);
        return businessLineManageService.queryCategoryTree(businessLineVO);
    }

    @Override
    public List<CategoryPathVO> queryPathByStatus(Long categoryId, String lang, Integer status) {
        return categoryAtomicService.pathByStatus(Sets.newHashSet(categoryId),status);
    }

    @Override
    public Map<Long, CategoryPathNameVO> queryCategoryByIds(Set<Long> catIds, Integer status) {
        if(CollectionUtils.isEmpty(catIds)){
            return Collections.emptyMap();
        }
        List<CategoryPathVO> categoryPathVOS = categoryAtomicService.pathByStatus(catIds,status);
        List<CategoryPathNameVO> categoryPathNameVOS = CategoryConvert.INSTANCE.listPathVO2PathNameVO(categoryPathVOS);
        this.setLangName(categoryPathNameVOS,StringUtils.isBlank(LangContextHolder.get())?LangConstant.LANG_ZH:LangContextHolder.get());
        if(CollectionUtils.isEmpty(categoryPathNameVOS)){
            return Collections.emptyMap();
        }
        return categoryPathNameVOS.stream().collect(Collectors.toMap(CategoryPathNameVO::getJdCatId
                , Function.identity(),(o1,o2)->o2));
    }

    @Override
    public List<CategoryPathNameVO> queryAllCategory(Integer status) {
        List<CategoryPathVO> categoryPathVOS = categoryAtomicService.pathByStatus(null,status);
        List<CategoryPathNameVO> categoryPathNameVOS = CategoryConvert.INSTANCE.listPathVO2PathNameVO(categoryPathVOS);
        this.setLangName(categoryPathNameVOS,StringUtils.isBlank(LangContextHolder.get())?LangConstant.LANG_ZH:LangContextHolder.get());
        return categoryPathNameVOS;
    }

    @Override
    public List<String> queryAllCategoryTile(Long index,Long size,List<String> langCodes) {
        List<CategoryPathVO> categoryPathVOS = categoryAtomicService.pathBySizeStatus(index,size,null);
        if(CollectionUtils.isEmpty(categoryPathVOS)){
            return Collections.emptyList();
        }

        if(CollectionUtils.isEmpty(langCodes)){
            langCodes = new ArrayList<>();
            langCodes.add(LangConstant.LANG_ZH);
        }

        Set<Long> catAllIds = new HashSet<>();
        categoryPathVOS.forEach(o->{catAllIds.add(o.getId1());catAllIds.add(o.getId2());
            catAllIds.add(o.getId3());catAllIds.add(o.getId4());});
        // 避免空元素
        catAllIds.remove(null);

        LambdaQueryWrapper<CategoryLangPO> langWrapper = Wrappers.<CategoryLangPO>lambdaQuery()
                .select(CategoryLangPO::getCatId, CategoryLangPO::getLang,CategoryLangPO::getLangName,CategoryLangPO::getJdCatId)
                .in(CategoryLangPO::getJdCatId, catAllIds)
                .in(CategoryLangPO::getLang, langCodes)
                .eq(CategoryLangPO::getYn, YnEnum.YES.getCode());
        List<CategoryLangPO> langPOList = categoryLangAtomicService.list(langWrapper);
        if (CollectionUtils.isEmpty(langPOList)){
            log.warn("queryAllCategoryTile langPOList is empty.");
            return new ArrayList<>();
        }

        Map<String,CategoryLangPO> langPOMap = langPOList.stream()
                .collect(Collectors.toMap(item->StringUtils.joinWith("_",item.getJdCatId(),item.getLang()), Function.identity()));

        List<String> results = new ArrayList<>();
        for(CategoryPathVO item : categoryPathVOS){
            JSONObject categoryPathVOObj = JSONObject.parseObject(JSONObject.toJSONString(item));

            categoryPathVOObj.put("status1", StatusEnum.forCode(item.getStatus1()).getDesc());
            categoryPathVOObj.put("status2", StatusEnum.forCode(item.getStatus2()).getDesc());
            categoryPathVOObj.put("status3", StatusEnum.forCode(item.getStatus3()).getDesc());
            if (Objects.nonNull(item.getStatus4())){
                categoryPathVOObj.put("status4", StatusEnum.forCode(item.getStatus4()).getDesc());
            }

            for(String lang: langCodes){
                CategoryLangPO langPO1 = langPOMap.get(StringUtils.joinWith("_",item.getId1(),lang));
                if(langPO1 != null){
                    categoryPathVOObj.put(CATEGORY_PREFIX + item.getLevel1() + Constant.UNDER_LINE + lang,langPO1.getLangName());
                }

                CategoryLangPO langPO2 = langPOMap.get(StringUtils.joinWith("_",item.getId2(),lang));
                if(langPO2 != null){
                    categoryPathVOObj.put(CATEGORY_PREFIX + item.getLevel2() + Constant.UNDER_LINE + lang,langPO2.getLangName());
                }

                CategoryLangPO langPO3 = langPOMap.get(StringUtils.joinWith("_",item.getId3(),lang));
                if(langPO3 != null){
                    categoryPathVOObj.put(CATEGORY_PREFIX + item.getLevel3() + Constant.UNDER_LINE + lang,langPO3.getLangName());
                }

                CategoryLangPO langPO4 = langPOMap.get(StringUtils.joinWith("_",item.getId4(),lang));
                if(langPO4 != null){
                    categoryPathVOObj.put(CATEGORY_PREFIX + item.getLevel4() + Constant.UNDER_LINE + lang,langPO4.getLangName());
                }
            }
            results.add(JSONObject.toJSONString(categoryPathVOObj));
        }

        return results;
    }

    @Override
    public List<String> queryCategoryBuyerAllCategoryTile(Long index,Long size,String countryCode, List<String> langCodes) {
        List<CategoryPathVO> categoryPathVOS = categoryAtomicService.pathBySizeStatus(index,size,StatusEnum.ENABLE.getCode());
        if(CollectionUtils.isEmpty(categoryPathVOS)){
            return Collections.emptyList();
        }

        if(CollectionUtils.isEmpty(langCodes)){
            langCodes = new ArrayList<>();
            langCodes.add(LangConstant.LANG_ZH);
        }

        Set<Long> catAllIds = new HashSet<>();
        categoryPathVOS.forEach(o->{catAllIds.add(o.getId1());catAllIds.add(o.getId2());
            catAllIds.add(o.getId3());catAllIds.add(o.getId4());});
        // 避免空元素
        catAllIds.remove(null);

        LambdaQueryWrapper<CategoryLangPO> langWrapper = Wrappers.<CategoryLangPO>lambdaQuery()
                .select(CategoryLangPO::getCatId, CategoryLangPO::getLang,CategoryLangPO::getLangName,CategoryLangPO::getJdCatId)
                .in(CategoryLangPO::getJdCatId, catAllIds)
                .in(CategoryLangPO::getLang, langCodes)
                .eq(CategoryLangPO::getYn, YnEnum.YES.getCode());
        List<CategoryLangPO> langPOList = categoryLangAtomicService.list(langWrapper);
        if (CollectionUtils.isEmpty(langPOList)){
            log.warn("queryCategoryBuyerAllCategoryTile langPOList is empty.");
            return null;
        }

        Map<String,CategoryLangPO> langPOMap = langPOList.stream()
                .collect(Collectors.toMap(item->StringUtils.joinWith("_",item.getJdCatId(),item.getLang()), Function.identity()));


        List<CategoryBuyerRelationPO> buyerRelationPOS = categoryBuyerRelationAtomicService.getByCountryCodeJdCatIds(countryCode,catAllIds);
        Map<Long,String> jdCatIdBuyerMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(buyerRelationPOS)){
            jdCatIdBuyerMap.putAll(buyerRelationPOS.stream().collect(Collectors.toMap(CategoryBuyerRelationPO::getJdCatId,CategoryBuyerRelationPO::getBuyer)));
        }

        List<String> results = new ArrayList<>();
        for(CategoryPathVO item : categoryPathVOS){
            JSONObject categoryPathVOObj = JSONObject.parseObject(JSONObject.toJSONString(item));

            categoryPathVOObj.put("status1", StatusEnum.forCode(item.getStatus1()).getDesc());
            categoryPathVOObj.put("status2", StatusEnum.forCode(item.getStatus2()).getDesc());
            categoryPathVOObj.put("status3", StatusEnum.forCode(item.getStatus3()).getDesc());
            if (Objects.nonNull(item.getStatus4())){
                categoryPathVOObj.put("status4", StatusEnum.forCode(item.getStatus4()).getDesc());
            }

            for(String lang: langCodes){
                CategoryLangPO langPO1 = langPOMap.get(StringUtils.joinWith("_",item.getId1(),lang));
                if(langPO1 != null){
                    categoryPathVOObj.put(CATEGORY_PREFIX + item.getLevel1() + Constant.UNDER_LINE + lang,langPO1.getLangName());
                }

                CategoryLangPO langPO2 = langPOMap.get(StringUtils.joinWith("_",item.getId2(),lang));
                if(langPO2 != null){
                    categoryPathVOObj.put(CATEGORY_PREFIX + item.getLevel2() + Constant.UNDER_LINE + lang,langPO2.getLangName());
                }

                CategoryLangPO langPO3 = langPOMap.get(StringUtils.joinWith("_",item.getId3(),lang));
                if(langPO3 != null){
                    categoryPathVOObj.put(CATEGORY_PREFIX + item.getLevel3() + Constant.UNDER_LINE + lang,langPO3.getLangName());
                }

                CategoryLangPO langPO4 = langPOMap.get(StringUtils.joinWith("_",item.getId4(),lang));
                if(langPO4 != null){
                    categoryPathVOObj.put(CATEGORY_PREFIX + item.getLevel4() + Constant.UNDER_LINE + lang,langPO4.getLangName());
                }
            }
            Long lastCatId = item.getId4() != null?item.getId4():item.getId3();
            categoryPathVOObj.put("lastCatId", lastCatId);
            categoryPathVOObj.put("buyer", jdCatIdBuyerMap.get(lastCatId));
            results.add(JSONObject.toJSONString(categoryPathVOObj));
        }

        return results;
    }

    /**
     * 设置语言名称
     * @param inputs CategoryPathNameVO对象列表，用于存储路径和名称信息
     * @param lang 语言标识符，用于指定语言版本
     */
    private void setLangName(List<CategoryPathNameVO> inputs,String lang){
        if(CollectionUtils.isEmpty(inputs) || StringUtils.isBlank(lang)){
            return;
        }
        Set<Long> catAllIds = new HashSet<>();
        inputs.forEach(o->{catAllIds.add(o.getId1());catAllIds.add(o.getId2());
            catAllIds.add(o.getId3());catAllIds.add(o.getId4());});
        // 避免空元素
        catAllIds.remove(null);

        LambdaQueryWrapper<CategoryLangPO> langWrapper = Wrappers.<CategoryLangPO>lambdaQuery()
                .select(CategoryLangPO::getJdCatId, CategoryLangPO::getLang,CategoryLangPO::getLangName)
                .in(CategoryLangPO::getJdCatId, catAllIds)
                .eq(CategoryLangPO::getLang, lang)
                .eq(CategoryLangPO::getYn, YnEnum.YES.getCode());
        List<CategoryLangPO> langPOList = categoryLangAtomicService.list(langWrapper);
        if (CollectionUtils.isEmpty(langPOList)){
            log.warn("setLangName langPOList is empty.");
            return;
        }

        Map<String,CategoryLangPO> langPOMap = langPOList.stream()
                .collect(Collectors.toMap(item->StringUtils.joinWith("_",item.getJdCatId(),item.getLang()), Function.identity()));
        for(CategoryPathNameVO item : inputs){
            CategoryLangPO langPO1 = langPOMap.get(StringUtils.joinWith("_",item.getId1(),lang));
            if(langPO1 != null){
                item.setName1(langPO1.getLangName());
            }
            CategoryLangPO langPO2 = langPOMap.get(StringUtils.joinWith("_",item.getId2(),lang));
            if(langPO2 != null){
                item.setName2(langPO2.getLangName());
            }
            CategoryLangPO langPO3 = langPOMap.get(StringUtils.joinWith("_",item.getId3(),lang));
            if(langPO3 != null){
                item.setName3(langPO3.getLangName());
            }
            CategoryLangPO langPO4 = langPOMap.get(StringUtils.joinWith("_",item.getId4(),lang));
            if(langPO4 != null){
                item.setName4(langPO4.getLangName());
            }
        }


    }

    @Override
    public boolean syncJdCategoryByFirstCatId(JdCategorySyncReqVO reqVO) {
        AssertValidation.isEmpty(reqVO, "请求参数不能为空");
        AssertValidation.isEmpty(reqVO.getJdFirstCatId(), "京东一级类目ID不能为空");

        // 查询一级类目信息
        int firstCatId = reqVO.getJdFirstCatId().intValue();
        Map<Integer, Category> categoryMap = getCategoryMapByLang(firstCatId, Language.zh_CN);
        // 判断国内一级类目是否存在
        boolean notExisted = categoryMap == null || categoryMap.isEmpty() || !categoryMap.containsKey(firstCatId) || Objects.isNull(categoryMap.get(firstCatId));
        AssertValidation.isEmpty(notExisted, String.format("京东一级类目ID: %s 不存在", reqVO.getJdFirstCatId()));

        // 检查类目状态
        Category category = categoryMap.get(firstCatId);
        AssertValidation.isTrue(Constant.ZERO.intValue() != category.getCategoryClass() ,String.format("当前类目 %s 不是一级类目，请检查。", reqVO.getJdFirstCatId()));
        AssertValidation.isTrue(StatusEnum.FORBIDDEN.getCode().equals(category.getStatus()), String.format("国内一级类目 %s 是下柜状态，不支持同步，请检查。", reqVO.getJdFirstCatId()));

        // 检查是否存在当前类目
        CategoryPO existingCategory = categoryAtomicService.getCategoryByJdCatId(reqVO.getJdFirstCatId());
        AssertValidation.isNotEmpty(existingCategory, "已经存在当前一级类目");

        // 校验当前一级类目是否在做同步
        String key = CacheKeyConstant.getKey(CacheKeyConstant.PRODUCT_CATEGORY_FIRST_KEY);
        boolean isMember = jimUtils.sIsMember(key,String.valueOf(firstCatId));
        if (isMember) {
            throw new RuntimeException(String.format("当前一级类目ID [%s] 正在同步中，请稍后再试",reqVO.getJdFirstCatId()));
        }
        // 异步同步国内类目
        CompletableFuture.runAsync(() -> jdCategorySyncManageService.syncJdCategoryByFirstCatId(reqVO, category), pool);
        //jdCategorySyncManageService.syncJdCategoryByFirstCatId(reqVO, category);
        return true;
    }
    @Override
    public CategoryVO getJdCategory(JdCategorySyncReqVO reqVO) {
        if (reqVO == null || reqVO.getJdFirstCatId() == null) {
            log.error("JdCategorySyncManageServiceImpl.getJdCategory failed, invalid input: {}", reqVO);
            return null;
        }

        GetCategoryByIdsParam param = new GetCategoryByIdsParam();
        param.setCategoryIds(Collections.singleton(reqVO.getJdFirstCatId().intValue()));

        Map<Integer, Category> categoryMap = categoryRpcService.getCategoryByIds(param);
        if (categoryMap == null || categoryMap.isEmpty()) {
            log.warn("JdCategorySyncManageServiceImpl.getJdCategory: No category found for id: {}", reqVO.getJdFirstCatId());
            throw new BizException("未查询到国内类目信息");
        }

        Category category = categoryMap.get(reqVO.getJdFirstCatId().intValue());
        if (category == null) {
            log.warn("JdCategorySyncManageServiceImpl.getJdCategory: Category not found for id: {}", reqVO.getJdFirstCatId());
            throw new BizException("未查询到国内类目信息");
        }
        return convertToCategoryVO(category);
    }

    private CategoryVO convertToCategoryVO(Category category) {
        CategoryVO categoryVO = new CategoryVO();
        categoryVO.setJdCatId(Long.valueOf(category.getCategoryId()));
        categoryVO.setCatName(category.getCategoryName());
        categoryVO.setCatLevel(category.getCategoryClass() + 1);
        categoryVO.setJdParentCatId(Long.valueOf(category.getFatherCategoryId()));
        categoryVO.setParentCatId(Long.valueOf(category.getFatherCategoryId()));
        categoryVO.setSort(category.getOrderSort());
        categoryVO.setStatus(category.getStatus());
        return categoryVO;
    }

    private Map<Integer, Category> getCategoryMapByLang(int firstCategoryId,Language language) {
        GetCategoryByIdsParam param = new GetCategoryByIdsParam();
        param.setCategoryIds(Collections.singleton(firstCategoryId));
        param.setLanguage(language);
        Map<Integer, Category> categoryMap = categoryRpcService.getCategoryByIds(param);
        return categoryMap;
    }

    @Override
    public DataResponse<Boolean> changeCategory(GmsCategoryReqDTO reqDTO) {
        try {
            return jdCategorySyncManageService.changeCategory(reqDTO);
        } catch (BizException e) {
            log.error("JdCategorySyncManageServiceImpl.changeCategory 类目处理发生业务异常 入参：[{}] message={}", JSON.toJSONString(reqDTO), e.getMessage());
            return DataResponse.error(e.getMessage());
        } catch (Exception e) {
            log.error("JdCategorySyncManageServiceImpl.changeCategory 类目处理发生系统异常 入参：[{}]", JSON.toJSONString(reqDTO), e);
            return DataResponse.error(DataResponseCode.SERVICE_ERROR.getCode(),DataResponseCode.SERVICE_ERROR.getMessage());
        }
    }

    @Override
    public Map<Long,CategoryVO> queryCategoryByIds(CategoryReadDTO req) {
        if (CollectionUtils.isEmpty(req.getCatId())) {
            return Collections.emptyMap();
        }
        // 批量查询类目列表
        List<CategoryPO> categoryPOList = categoryAtomicService.queryCategoryByIds(req.getCatId());
        log.info("queryCategoryByIds 查询类目列表 入参：[{}] 出参：[{}]", JSON.toJSONString(req), JSON.toJSONString(categoryPOList));
        if (CollectionUtils.isEmpty(req.getLang())) {
            req.setLang(Sets.newHashSet(LangConstant.LANG_ZH));
        }
        List<CategoryLangPO> langPOList = categoryLangAtomicService.queryListByCatIdsAndLangCodes(req.getCatId(), req.getLang());
        if (CollectionUtils.isNotEmpty(langPOList)) {
            Map<Long, List<CategoryLangPO>> categoryLangMap = langPOList.stream().collect(Collectors.groupingBy(CategoryLangPO::getJdCatId));
            categoryPOList.forEach(catPo-> catPo.setLangList(categoryLangMap.getOrDefault(catPo.getJdCatId(), Lists.newArrayList())));
        }

        return Optional.ofNullable(categoryPOList).orElseGet(ArrayList::new)
                .stream()
                .collect(Collectors.toMap(CategoryPO::getJdCatId, CategoryConvert.INSTANCE::po2dto));
    }
}
