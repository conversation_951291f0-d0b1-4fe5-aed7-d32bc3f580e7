package com.jdi.isc.product.soa.service.manage.approveorder.template.create;


import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.biz.component.api.enums.JoySkyBizFlowTypeEnum;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveFormDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderSaveReqDTO;
import com.jdi.isc.product.soa.api.approveorder.common.AuditBizTypeEnum;
import com.jdi.isc.product.soa.api.approveorder.res.ApproveOrderSaveResDTO;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.common.util.BigDecimalUtil;
import com.jdi.isc.product.soa.common.util.StringUtils;
import com.jdi.isc.product.soa.domain.enums.AuditFormEnum;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPriceWarningPO;
import com.jdi.isc.product.soa.domain.price.biz.ProfitRateVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.rpc.utils.ExceptionUtil;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceWarningAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.price.ProfitCalculateManageService;
import com.jdi.isc.product.soa.service.manage.price.warning.aggreementprice.manager.CountryAgreementPriceWarningManager;
import com.jdi.isc.product.soa.service.manage.spu.SpuManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component(value = AgreementPriceWarningCreateApproveOrderExe.BEAN_NAME)
public class AgreementPriceWarningCreateApproveOrderExe extends AbstractCreateApproveOrderExe {

    public static final String BEAN_NAME = "vipPriceWarningCreateApproveOrderExe";

    @Resource
    private CountryAgreementPriceWarningAtomicService countryAgreementPriceWarningAtomicService;

    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;

    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private CustomerManageService customerManageService;

    @Resource
    protected CountryManageService countryManageService;

    @Resource
    private ProfitCalculateManageService profitCalculateManageService;

    @Resource
    private CountryAgreementPriceWarningManager countryAgreementPriceWarningManager;


    @Override
    public int getBizType() {
        return AuditBizTypeEnum.TYPE_1.getCode();
    }

    @Override
    public String getBeanName() {
        return BEAN_NAME;
    }

    public DataResponse<ApproveOrderSaveResDTO> execute(ApproveOrderSaveReqDTO input) {
        return super.execute(input);
    }

    @Override
    protected boolean isAutoAuditPass(ApproveOrderSaveReqDTO input) {
        List<ApproveFormDTO> orderFormList = input.getOrderFormList();

        Map<String, String> fieldValueMap = orderFormList.stream().collect(Collectors.toMap(ApproveFormDTO::getFieldName, ApproveFormDTO::getFieldValue));

        BigDecimal profitRate = new BigDecimal(fieldValueMap.get(PROFIT_RATE));
        BigDecimal profitLimitRate = new BigDecimal(fieldValueMap.get(PROFIT_LIMIT_RATE));

        boolean isAutoAuditPass = BigDecimalUtil.gt(profitRate, profitLimitRate);

        log.info("协议价-不可售阈值审批. isAutoAuditPass={}, profitRate={}, profitLimitRate={}", isAutoAuditPass, profitRate, profitLimitRate);

        return isAutoAuditPass;
    }

    @Override
    protected DataResponse<ApproveOrderSaveResDTO> executeAutoAuditPass(ApproveOrderSaveReqDTO input) {
        String warnId = input.getOrder().getBizId();

        Map<String, String> fieldValueMap = input.getOrderFormList().stream().collect(Collectors.toMap(ApproveFormDTO::getFieldName, ApproveFormDTO::getFieldValue));

        BigDecimal profitRate = new BigDecimal(fieldValueMap.get(PROFIT_RATE));

        try {
            countryAgreementPriceWarningManager.updateUnsellableThreshold(Long.parseLong(warnId), profitRate, input.getPin());
        } catch (Exception e) {
            log.info("更新协议价-不可售阈值失败. warnId={}, profitRate={}", warnId, profitRate, e);
            return DataResponse.error(ExceptionUtil.getMessage(e, "更新不可售阈值失败!"));
        } finally {
            log.info("更新协议价-不可售阈值完成. warnId={}, profitRate={}", warnId, profitRate);
        }
        return DataResponse.success();
    }

    @Override
    public int getFlowTypeCode() {
        return JoySkyBizFlowTypeEnum.COUNTRY_AGREEMENT_PRICE_LINE_FLOW.getFlowTypeCode();
    }

    @Override
    public Map<String, Object> constructProcessControlData(ApproveOrderSaveReqDTO input) {
        String sourceCountryCode = input.getSourceCountryCode();
        String targetCountryCode = input.getTargetCountryCode();


        //流程控制参数校验
        if (org.apache.commons.lang3.StringUtils.isBlank(sourceCountryCode) || org.apache.commons.lang3.StringUtils.isBlank(targetCountryCode)) {
            throw new BizException("流程控制参数不能为空!,商品来源国:"+sourceCountryCode+",客户所在国:" + targetCountryCode);
        }

        //流程参数赋值
        Map<String, Object> flowCalculeVarMap = new HashMap<>(3);
        flowCalculeVarMap.put("productCountry", sourceCountryCode);
        flowCalculeVarMap.put("customerCountry", targetCountryCode);
        return flowCalculeVarMap;
    }

    @Override
    protected void fillDefaultValue(ApproveOrderSaveReqDTO input) {

        ApproveOrderDTO order = input.getOrder();
        String customerPriceWarnId = order.getBizId();
        CountryAgreementPriceWarningPO warning = countryAgreementPriceWarningAtomicService.getValidById(Long.valueOf(customerPriceWarnId));

        if (warning == null) {
            throw new ProductBizException("不存在的预警信息. %s", customerPriceWarnId);
        }

        Long skuId = warning.getSkuId();

        // 获取sku名称
        String spuName = SpringUtil.getBean(SpuManageService.class).getSpuNameBySkuId(skuId, StringUtils.isEmpty(input.getLang()) ? LangConstant.LANG_ZH : input.getLang());

        SkuPO sku = skuAtomicService.getBySkuIdsAndYn(skuId, null);
        if (sku == null) {
            log.warn("sku不存. skuId={}", skuId);
            throw new ProductBizException("sku不存. %s", skuId);
        }

        String bizNo = warning.getBizNo();

        if (StringUtils.isEmpty(bizNo)) {
            log.info("找不到关联的客制化价格id, 不填充默认值. warning={}", JSONObject.toJSONString(warning));
            throw new ProductBizException("找不到关联的客制化价格id. %s", customerPriceWarnId);
        }

        CountryAgreementPricePO countryAgreementPrice = countryAgreementPriceAtomicService.getValidBySkuIdAndTargetCountryCode(skuId, warning.getTargetCountryCode());

        String targetCountryCode = countryAgreementPrice.getTargetCountryCode();
        String currency = countryAgreementPrice.getCurrency();

        BigDecimal agreementPrice = countryAgreementPrice.getAgreementPrice();
        BigDecimal countryCostPrice = countryAgreementPrice.getCountryCostPrice();
        if (agreementPrice == null || countryCostPrice == null) {
            log.info("未设置协议价或成本价, countryAgreementPrice={}", JSONObject.toJSONString(countryAgreementPrice));
            throw new ProductBizException("未设置协议价或成本价. %s", skuId);
        }

        if (StringUtils.isEmpty(currency)) {
            log.info("协议价未设置币种, countryAgreementPrice={}", JSONObject.toJSONString(countryAgreementPrice));
            throw new ProductBizException("协议价未设置币种. %s", skuId);
        }

        // 利润率
        String profitRate = SpringUtil.getBean(ProfitCalculateManageService.class)
                .calculateSalePriceProfitRate(agreementPrice, countryCostPrice)
                .multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString();

        ProfitRateVO profitRateVO = profitCalculateManageService.getProfitLimitRate(sku.getJdCatId(), sku.getSourceCountryCode(), targetCountryCode);

        DataResponse<Boolean> profitRateResponse = profitCalculateManageService.checkProfitRate(profitRateVO);
        if (!profitRateResponse.getSuccess()) {
            throw new ProductBizException(profitRateResponse.getMessage());
        }

        Map<String, String> keyTitleMap = this.getKeyTitleMap();

        // 国际SKUID
        super.addFiled(keyTitleMap, input, "skuId", String.valueOf(skuId));
        // 商品名称
         super.addFiled(keyTitleMap, input, "spuName", spuName);
        // 国家协议价
        super.addFiled(keyTitleMap, input, "agreementPrice", countryAgreementPrice.getAgreementPrice().stripTrailingZeros().toPlainString() + currency);
        // 国家成本价
        super.addFiled(keyTitleMap, input, "countryCostPrice", countryCostPrice.stripTrailingZeros().toPlainString() + currency);
        // 利润率-前毛
        super.addFiled(keyTitleMap, input, PROFIT_RATE, profitRate);
        // 利润率阈值-前毛
        super.addFiled(keyTitleMap, input, PROFIT_LIMIT_RATE, profitRateVO.getProfitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        // 超低利润率阈值-前毛
        super.addFiled(keyTitleMap, input, "lowProfitLimitRate", profitRateVO.getLowProfitRate().multiply(Constant.DECIMAL_HUNDRED).stripTrailingZeros().toPlainString());
        // 申请原因
        super.addFiled(keyTitleMap, input, "applyReason", order.getApplyReason());
        // 提交人
        super.addFiled(keyTitleMap, input, "applyUserErp", order.getApplyUserErp());

        super.fillDefaultValue(input);

        // 补充货源国和目标国
        input.setSourceCountryCode(sku.getSourceCountryCode());
        input.setTargetCountryCode(targetCountryCode);
    }

    @Override
    public String getAuditFormKey() {
        return AuditFormEnum.AGREEMENT_PRICE_LINE_AUDIT.getCode();
    }
}
