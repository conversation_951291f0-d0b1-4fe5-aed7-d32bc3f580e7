package com.jdi.isc.product.soa.service.protocol.jsf.wimp.category;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.KvDTO;
import com.jdi.isc.product.soa.api.spu.res.GroupPropertyDTO;
import com.jdi.isc.product.soa.api.wimp.category.GlobalAttributeApiService;
import com.jdi.isc.product.soa.api.wimp.category.req.*;
import com.jdi.isc.product.soa.api.wimp.category.res.GlobalAttributeApiResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.frame.SystemContextHolder;
import com.jdi.isc.product.soa.domain.category.biz.*;
import com.jdi.isc.product.soa.domain.common.biz.KvVO;
import com.jdi.isc.product.soa.domain.spu.biz.GroupPropertyVO;
import com.jdi.isc.product.soa.service.manage.category.GlobalAttributeManageService;
import com.jdi.isc.product.soa.service.mapstruct.category.GlobalAttributeConvert;
import com.jdi.isc.product.soa.service.mapstruct.category.GlobalAttributeLangConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 国际跨境属性api服务实现
 * @Author: taxuezheng1
 * @Date: 2024/07/12 13:17
 **/

@Slf4j
@Service
public class GlobalAttributeApiServiceImpl implements GlobalAttributeApiService {

    @Resource
    private GlobalAttributeManageService globalAttributeManageService;

    @Override
    public DataResponse<PageInfo<GlobalAttributePageApiDTO.Response>> pageSearch(GlobalAttributePageApiDTO.Request input){
        log.info("pageSearch, input={}", JSONObject.toJSONString(input));
        try {
            GlobalAttributePageVO.Request reqVo = GlobalAttributeConvert.INSTANCE.pageApiReq2PageVoReq(input);
            PageInfo<GlobalAttributePageVO.Response> pageInfoRes = globalAttributeManageService.pageSearch(reqVo);
            PageInfo<GlobalAttributePageApiDTO.Response> pageInfoApiRes = GlobalAttributeConvert.INSTANCE.pageVoResponse2PageApiRes(pageInfoRes);
            return DataResponse.success(pageInfoApiRes);
        } catch (Exception e) {
            log.error("GlobalAttributeApiService#pageSearch param:{},e:", JSON.toJSONString(input),e);
        }
        return DataResponse.error("服务异常");
    }

    @Override
    public DataResponse<Boolean> saveOrUpdate(GlobalAttributeApiDTO input) {
        try {
            GlobalAttributeVO reqVo = GlobalAttributeConvert.INSTANCE.apiDto2Vo(input);
            return globalAttributeManageService.saveOrUpdate(reqVo);
        } catch (Exception e) {
            log.error("GlobalAttributeApiService#saveOrUpdate param:{},e:", JSON.toJSONString(input),e);
        }
        return DataResponse.error("服务异常");
    }

    @Override
    public DataResponse<GlobalAttributeApiDTO> detail(GlobalAttributeApiDTO input) {
        try {
            GlobalAttributeVO detail = globalAttributeManageService.detail(input.getId());
            GlobalAttributeApiDTO result = GlobalAttributeConvert.INSTANCE.vo2ApiDTO(detail);
            return DataResponse.success(result);
        } catch (Exception e) {
            log.error("GlobalAttributeApiService#detail param:{},e:", JSON.toJSONString(input),e);
        }
        return DataResponse.error("服务异常");
    }

    @Override
    public DataResponse<Boolean> checkLangName(GlobalAttributeApiDTO input) {
        try {
            GlobalAttributeVO reqVo = GlobalAttributeConvert.INSTANCE.apiDto2Vo(input);
            boolean checkRes = globalAttributeManageService.checkLangName(reqVo);
            return DataResponse.success(checkRes);
        } catch (Exception e) {
            log.error("GlobalAttributeApiService#checkLangName param:{},e:", JSON.toJSONString(input),e);
        }
        return DataResponse.error("服务异常");
    }

    @Override
    public DataResponse<List<GlobalAttributePageApiDTO.Response>> queryAttributeList(GlobalAttributePageApiDTO.Request input) {

        try {
            GlobalAttributePageVO.Request reqVo = GlobalAttributeConvert.INSTANCE.pageApiReq2PageVoReq(input);
            List<GlobalAttributePageVO.Response> list = globalAttributeManageService.queryAttributeList(reqVo);
            List<GlobalAttributePageApiDTO.Response> response = GlobalAttributeConvert.INSTANCE.pageRes2ApiPageRes(list);
            return DataResponse.success(response);
        } catch (Exception e) {
            log.error("GlobalAttributeApiService#checkLangName queryAttributeList:{},e:", JSON.toJSONString(input), e);
        }
        return DataResponse.error("服务异常");
    }

    @Override
    public DataResponse<List<GlobalAttributeValueApiDTO>> queryAttributeValueList(GlobalAttributePageApiDTO.Request input) {

        try {
            GlobalAttributePageVO.Request reqVo = GlobalAttributeConvert.INSTANCE.pageApiReq2PageVoReq(input);
            List<GlobalAttributeValueVO> globalAttributeValueVOS = globalAttributeManageService.queryAttributeValueList(reqVo);
            List<GlobalAttributeValueApiDTO> result = GlobalAttributeLangConvert.INSTANCE.listVo2ApiDto(globalAttributeValueVOS);
            return DataResponse.success(result);
        } catch (Exception e) {
            log.error("GlobalAttributeApiService#queryAttributeValueList param:{},e:", JSON.toJSONString(input), e);
        }
        return DataResponse.error("服务异常");
    }

    @Override
    public DataResponse<List<KvDTO>> queryAttributeName(List<Long> ids, String lang) {
        log.info("queryAttributeName, ids={},lang={}", JSONObject.toJSONString(ids),lang);
        try {
            List<KvVO> resultList = globalAttributeManageService.queryAttributeName(ids,lang);
            List<KvDTO> results = GlobalAttributeConvert.INSTANCE.listKvDto2Vo(resultList);
            return DataResponse.success(results);
        } catch (Exception e) {
            log.error("GlobalAttributeApiService#queryAttributeName param:{},e:", JSON.toJSONString(ids),e);
        }
        return DataResponse.error("服务异常");
    }

    @Override
    public DataResponse<List<GlobalResApiDTO>> queryRequireAttribute(List<GlobalReqApiDTO> inputs) {
        log.info("queryOrderAttribute, inputs={}", JSONObject.toJSONString(inputs));
        return DataResponse.success();
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<GlobalAttributeApiResDTO>> queryAttributeListByCatId(GlobalAttributeQueryReqDTO input) {
        try {
            SystemContextHolder.init(Constant.SYSTEM_CODE);
            GlobalAttributeQueryReqVO reqVo = GlobalAttributeConvert.INSTANCE.queryReqDTO2ReqVO(input);
            List<GlobalAttributeResVO> list = globalAttributeManageService.queryAttributeListByCatId(reqVo);
            List<GlobalAttributeApiResDTO> response = GlobalAttributeConvert.INSTANCE.restVO2ApiResDTO(list);
            return DataResponse.success(response);
        } catch (Exception e) {
            log.error("GlobalAttributeApiService#queryAttributeListByCatId 入参:[{}],e:", JSON.toJSONString(input), e);
        }
        return DataResponse.error(DataResponseCode.BUSINESS_ERROR.getMessage());
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<GroupPropertyDTO>> queryGroupGlobalAttributeByCatId(GlobalAttributeQueryReqDTO input) {
        log.info("GlobalAttributeApiServiceImpl.queryGroupGlobalAttributeByCatId, input={}", JSON.toJSONString(input));
        List<GroupPropertyVO> groupPropertyVOS = globalAttributeManageService.queryGroupPropertyVos(input.getCategoryId(),input.getSourceCountryCode()
                ,input.getTargetCountryCodes(),input.getAttributeDimension(),input.getLang());
        return DataResponse.success(GlobalAttributeConvert.INSTANCE.listGroupPropertyVo2Dto(groupPropertyVOS));
    }
}
