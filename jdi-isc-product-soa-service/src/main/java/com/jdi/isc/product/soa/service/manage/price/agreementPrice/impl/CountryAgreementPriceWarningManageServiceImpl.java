package com.jdi.isc.product.soa.service.manage.price.agreementPrice.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.agreementPrice.res.CountryAgreementPriceWarningDTO;
import com.jdi.isc.product.soa.api.approveorder.common.ApproveOrderStatusEnum;
import com.jdi.isc.product.soa.api.approveorder.common.AuditBizTypeEnum;
import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.AgreementPriceDataSourceTypeEnums;
import com.jdi.isc.product.soa.api.common.enums.CustomerSkuPriceWarningEnums;
import com.jdi.isc.product.soa.api.common.enums.PriceAvailableSaleStatusEnum;
import com.jdi.isc.product.soa.api.pricewarn.enums.PriceWarnTypeEnum;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveOrderPO;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceAuditReqVO;
import com.jdi.isc.product.soa.domain.enums.SkuScopeEnum;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceWarningPageReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceWarningVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPriceWarningPO;
import com.jdi.isc.product.soa.domain.price.biz.ProfitRateVO;
import com.jdi.isc.product.soa.domain.price.po.PriceLogPO;
import com.jdi.isc.product.soa.domain.sku.biz.ExternalVO;
import com.jdi.isc.product.soa.domain.sku.biz.ProductIdVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuExternalReqVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuVO;
import com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum;
import com.jdi.isc.product.soa.service.adapter.mapstruct.agreementPrice.CountryAgreementPriceConvert;
import com.jdi.isc.product.soa.service.atomic.approveorder.ApproveOrderAtomicService;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.PriceLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceWarningAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.manage.brand.BrandManageService;
import com.jdi.isc.product.soa.service.manage.category.CategoryManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.impl.CustomerSkuPriceDraftManageServiceImpl;
import com.jdi.isc.product.soa.service.manage.price.ProfitCalculateManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceWarningManageService;
import com.jdi.isc.product.soa.service.manage.pricewarn.PriceWarnProducer;
import com.jdi.isc.product.soa.service.manage.sku.SkuExternalManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.specialAttr.SpecialAttrManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: 国家协议价预警表数据维护服务实现
 * @Author: zhaokun51
 * @Date: 2025/03/18 10:59
 **/

@Slf4j
@Service
public class CountryAgreementPriceWarningManageServiceImpl extends BaseManageSupportService<CountryAgreementPriceWarningVO, CountryAgreementPriceWarningPO> implements
    CountryAgreementPriceWarningManageService {

    @Resource
    private CountryAgreementPriceWarningAtomicService countryAgreementPriceWarningAtomicService;
    @Resource
    private SkuExternalManageService skuExternalManageService;
    @Resource
    private ProfitCalculateManageService profitCalculateManageService;
    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;
    @Resource
    private PriceLogAtomicService priceLogAtomicService;

    @Value("${spring.profiles.active}")
    protected String systemProfile;

    @Resource
    private SkuReadManageService skuReadManageService;

    @Resource
    private ApproveOrderAtomicService approveOrderAtomicService;

    @Resource
    private CategoryManageService categoryManageService;

    @Resource
    private BrandManageService brandManageService;

    @Resource
    private SpecialAttrManageService specialAttrManageService;

    @Resource
    private OperDuccConfig operDuccConfig;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Override
    public PageInfo<CountryAgreementPriceWarningVO> pageSearch(CountryAgreementPriceWarningPageReqVO input) {

        PageInfo<CountryAgreementPriceWarningVO> pageInfo = new PageInfo<>();
        pageInfo.setSize(input.getSize());
        pageInfo.setIndex(input.getIndex());
        input.setTestProduct(0);

        if(CollectionUtils.isNotEmpty(input.getProductIds())){
            ProductIdVO productIdVO = skuReadManageService.getProductInfoByIds(input.getProductIds());
            if(CollectionUtils.isNotEmpty(productIdVO.getSkuIds())){
                input.setSkuIds(productIdVO.getSkuIds());
            }
        }

        // 查询类目信息 换种实现方式
        Set<Long> lastCatIds;

        if (input.queryCategory()) {

            CustomerSkuPriceAuditReqVO query = new CustomerSkuPriceAuditReqVO(input.getFirstCatId(), input.getSecondCatId(), input.getThirdCatId(), input.getLastCatId());

            lastCatIds = SpringUtil.getBean(CustomerSkuPriceDraftManageServiceImpl.class).getLastCatIds(query);

            if (CollectionUtils.isEmpty(lastCatIds)) {
                return pageInfo;
            }
            input.setLastCatIds(lastCatIds);
        }

        // 查询列表
//        Page<CountryAgreementPriceWarningVO> pageDB = new Page<>(input.getIndex(), input.getSize());


        // 构建查询条件
//        LambdaQueryWrapper<CountryAgreementPriceWarningPO> wrapper = this.buildWrapper(input);

//        Page<CountryAgreementPriceWarningPO> page = new Page<>(input.getIndex(), input.getSize());
//        Page<CountryAgreementPriceWarningPO> dbRecord = countryAgreementPriceWarningAtomicService.page(page, wrapper);
//        pageInfo.setTotal(dbRecord.getTotal());
//        if (CollectionUtils.isEmpty(dbRecord.getRecords())){
//            return pageInfo;
//        }


        long total = countryAgreementPriceWarningAtomicService.getTotal(input);
        pageInfo.setTotal(total);
        if (total <= 0) {
            return pageInfo;
        }

        // 分页查询
        List<CountryAgreementPriceWarningVO> pageList = countryAgreementPriceWarningAtomicService.listSearch(input);

//        // 构建查询条件
//        LambdaQueryWrapper<CountryAgreementPriceWarningPO> wrapper = this.buildWrapper(input);
//
//        Page<CountryAgreementPriceWarningPO> dbRecord = countryAgreementPriceWarningAtomicService.page(pageDB, wrapper);
//        pageInfo.setTotal(dbRecord.getTotal());
//        if (CollectionUtils.isEmpty(dbRecord.getRecords())){
//            return pageInfo;
//        }

//        List<CountryAgreementPriceWarningVO> pageList = CountryAgreementPriceConvert.INSTANCE.warningListPo2Vo(dbRecord.getRecords());
        if (CollectionUtils.isNotEmpty(pageList)){
            this.setSkuName(pageList);
            this.setProfitRate(pageList);
            // 设置价格可售状态、不可售阈值
            this.setPriceStatusInfo(pageList);
            // 设置审批状态
            this.setLatestAuditStatus(pageList);
            // 设置类目名称
            categoryManageService.setCatIdAndName(pageList, "jdCatId", "catName");
            // 设置品牌名称
            brandManageService.setBrandName(pageList, "brandId", "brandName");
            pageInfo.setRecords(pageList);
        }

        return pageInfo;
    }

    /**
     * 设置最新的审批状态
     */
    private void setLatestAuditStatus(List<CountryAgreementPriceWarningVO> pageList) {
        List<String> ids = pageList.stream().map(item -> String.valueOf(item.getId())).collect(Collectors.toList());
        Map<String/*id*/, ApproveOrderPO> auditStatusMap = approveOrderAtomicService.getLatestAuditStatusMap(AuditBizTypeEnum.TYPE_1.getCode(), ids);

        List<Long> skuIds = pageList.stream().map(CountryAgreementPriceWarningVO::getSkuId).collect(Collectors.toList());
        Map<Long, SkuPO> skuMap = skuAtomicService.mapBySkuIdsAndYn(skuIds, null);

        for (CountryAgreementPriceWarningVO item : pageList) {

            SkuPO skuPO = skuMap.get(item.getSkuId());

            if (skuPO == null) {
                log.info("不存在sku信息，不补充审核信息，data={}", item.getSkuId());
                continue;
            }

            ApproveOrderPO approveOrder = auditStatusMap.get(String.valueOf(item.getId()));
            if (approveOrder == null) {
                log.info("找不到审核数据. id={}", item.getId());
                item.setShowAuditSubmitButton(true);
            } else {
                item.setAuditStatus(approveOrder.getAuditStatus());
                item.setApproveStatus(approveOrder.getStatus());
                item.setApproveId(approveOrder.getId());
                item.setApplyCode(approveOrder.getApplyCode());

                if (ApproveOrderStatusEnum.codeOf(approveOrder.getStatus()) == ApproveOrderStatusEnum.PROCESSING) {
                    if (StringUtils.isNotEmpty(approveOrder.getApplyCode())) {
                        item.setShowAuditRevokeButton(true);
                    }
                } else {
                    item.setShowAuditSubmitButton(true);
                }
            }

            // 可售不展示submit按钮
            if (item.getAvailableSaleStatus() != null && item.getAvailableSaleStatus() == PriceAvailableSaleStatusEnum.ENABLE.getCode()) {
                item.setShowAuditSubmitButton(false);
            }
        }
    }

    private void setPriceStatusInfo(List<CountryAgreementPriceWarningVO> pageList) {
        if (CollectionUtils.isEmpty(pageList)) {
            return;
        }

        List<String> bizNos = pageList.stream().map(CountryAgreementPriceWarningVO::getBizNo).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        Map<String, CountryAgreementPricePO> priceMap = countryAgreementPriceAtomicService.getMapByBizNos(bizNos);

        for (CountryAgreementPriceWarningVO item : pageList) {
            CountryAgreementPricePO price = priceMap.get(item.getBizNo());
            if (price == null) {
                log.warn("找不到协议价价格信息. id={}", item.getId());
                continue;
            }
            item.setUnsellableThreshold(price.getUnsellableThreshold() != null ? price.getUnsellableThreshold().multiply(Constant.DECIMAL_HUNDRED) : null);
            item.setUnsellableThresholdTime(price.getUnsellableThresholdTime());
            item.setAvailableSaleStatus(price.getAvailableSaleStatus());
            item.setAvailableSaleStatusTime(price.getAvailableSaleStatusTime());
        }

    }

    @Resource
    private CountryMkuAtomicService countryMkuAtomicService;

    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;

    @Resource
    private PriceWarnProducer priceWarnProducer;

    @Override
    @PFTracing
    public Boolean saveOrUpdate(Long agreementPriceId, AgreementPriceDataSourceTypeEnums dataSourceType) {
//        CountryAgreementPricePO agreementPricePO = countryAgreementPriceAtomicService.getValidById(agreementPriceId);

//        try{
////            agreementPricePO = countryAgreementPriceAtomicService.getValidById(agreementPriceId);
//
//            CountryAgreementPriceWarningPO countryAgreementPriceWarningPO = this.getOrCreateSkuPriceWarningPO(agreementPricePO);
//            this.setProfitRateAndStatus(countryAgreementPriceWarningPO);
//            this.fillData(countryAgreementPriceWarningPO, agreementPricePO);
//
//            countryAgreementPriceWarningPO.setDataStatusSource(dataSourceType.getCode());
//            countryAgreementPriceWarningAtomicService.saveOrUpdate(countryAgreementPriceWarningPO);
//            PriceLogPO priceLogPO = this.getPriceLogPO(countryAgreementPriceWarningPO);
//            priceLogAtomicService.save(priceLogPO);
//        }catch (Exception e){
//            log.error("CountryAgreementPriceWarningManageServiceImpl.saveOrUpdate error, agreementPriceId:{}, dataSourceType:{}", agreementPriceId, dataSourceType, e);
//            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s 国家协议价预警,skuId: %s, agreementPrice:%s, 异常信息:%s"
//                    , systemProfile
//                    , LevelCode.P2.getMessage()
//                    , agreementPricePO.getSkuId()
//                    , agreementPricePO.getAgreementPrice()
//                    , e.getMessage()));
//            return Boolean.FALSE;
//        }
        priceWarnProducer.sendMessage(PriceWarnTypeEnum.AGREEMENT_PRICE, agreementPriceId, dataSourceType.getCode());


        return Boolean.TRUE;
    }

    @Override
    public void fillData(CountryAgreementPriceWarningPO countryAgreementPriceWarningPO, CountryAgreementPricePO agreementPricePO) {
        if (countryAgreementPriceWarningPO == null || agreementPricePO == null) {
            log.warn("fillData 协议价为空. countryAgreementPriceWarningPO={}, agreementPricePO={}", JSONObject.toJSONString(countryAgreementPriceWarningPO), JSONObject.toJSONString(agreementPricePO));
            return;
        }

        // 设置国家池状态
        CountryMkuPO countryMkuPO = countryMkuAtomicService.getByMkuIdCountryCode(countryAgreementPriceWarningPO.getMkuId(), countryAgreementPriceWarningPO.getTargetCountryCode());
        if (countryMkuPO != null) {
            countryAgreementPriceWarningPO.setCountryMkuPoolStatus(countryMkuPO.getPoolStatus());
        } else {
            countryAgreementPriceWarningPO.setCountryMkuPoolStatus(CountryMkuPoolStatusEnum.NOT_POOL.getCode());
            log.info("找不到国家池信息. mkuId={}", countryAgreementPriceWarningPO.getMkuId());
        }

        // 设置客户池状态
        String bindStatus = customerMkuAtomicService.getBindStatus(countryAgreementPriceWarningPO.getMkuId(), countryAgreementPriceWarningPO.getTargetCountryCode(), null, operDuccConfig.getPreseletorClientCodeList());
        countryAgreementPriceWarningPO.setCustomerMkuPoolStatus(bindStatus);

        // 设置可售状态
        countryAgreementPriceWarningPO.setAvailableSaleStatus(agreementPricePO.getAvailableSaleStatus());

        Long skuId = countryAgreementPriceWarningPO.getSkuId();
        if (skuId != null) {
            try {
                Integer testProduct = specialAttrManageService.isTestProduct(skuId);
                countryAgreementPriceWarningPO.setTestProduct(testProduct);
            } catch (Exception e) {
                log.warn("协议价获取测试产品失败, skuId={}", skuId, e);
            }
        }

        log.info("协议价填充字段, countryAgreementPriceWarningPO={}", JSONObject.toJSONString(countryAgreementPriceWarningPO));
    }

    @Override
    public void updateYn(List<Long> skuIds, String updater) {
        log.info("更新国家协议价格预警yn=0, skuIds={}, updater={}", skuIds, updater);
        if (CollectionUtils.isEmpty(skuIds)) {
            log.info("更新国家协议价格预警yn=0, skuIds is empty");
            return;
        }

        List<CountryAgreementPriceWarningPO> list = countryAgreementPriceWarningAtomicService.listBySkuIds(skuIds);

        List<Long> ids = list.stream().map(CountryAgreementPriceWarningPO::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            log.info("更新国家协议价格预警yn=0, ids is empty");
            return;
        }

        LambdaUpdateWrapper<CountryAgreementPriceWarningPO> wrapper = Wrappers.<CountryAgreementPriceWarningPO>lambdaUpdate()
                .in(CountryAgreementPriceWarningPO::getId, ids);
        CountryAgreementPriceWarningPO update = new CountryAgreementPriceWarningPO();
        update.setYn(YnEnum.NO.getCode());
        update.setUpdateInfo(updater);

        boolean result = countryAgreementPriceWarningAtomicService.update(update, wrapper);
        log.info("更新国家协议价格预警yn=0, 更新结果={}, ids={}, updater={}", result, ids, updater);
        if (!result) {
            log.error("更新国家协议价格预警yn=0, 更新失败. id={}, updater={}", ids, updater);
            throw new BizException("更新国家协议价格预警失败");
        }
    }

    @Override
    public List<CountryAgreementPriceWarningDTO> listWarningsByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<CountryAgreementPriceWarningPO> list = countryAgreementPriceWarningAtomicService.listWarningsByIds(ids);

        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        return CountryAgreementPriceConvert.INSTANCE.convert(list);
    }

    /**
     * 列表查询条件生成
     * @return 查询条件Wrapper
     */
    private LambdaQueryWrapper<CountryAgreementPriceWarningPO> buildWrapper(CountryAgreementPriceWarningPageReqVO input){
        LambdaQueryWrapper<CountryAgreementPriceWarningPO> wrapper = Wrappers.<CountryAgreementPriceWarningPO>lambdaQuery();
        wrapper.eq(input.getWarningStatus()!= null, CountryAgreementPriceWarningPO::getWarningStatus, input.getWarningStatus());
        wrapper.eq(StringUtils.isNotBlank(input.getTargetCountryCode()),CountryAgreementPriceWarningPO::getTargetCountryCode, input.getTargetCountryCode());
        wrapper.in(CollectionUtils.isNotEmpty(input.getSkuIds()),CountryAgreementPriceWarningPO::getSkuId, input.getSkuIds());
        wrapper.isNotNull(CountryAgreementPriceWarningPO::getAgreementPrice);
        wrapper.eq(CountryAgreementPriceWarningPO::getYn, YnEnum.YES.getCode());
        wrapper.orderByDesc(CountryAgreementPriceWarningPO::getUpdateTime);

        return wrapper;
    }

    /**
     * 设置 SKU 名称。
     * @param targets 目标列表，包含 CustomerSkuPriceWarningVO 对象。
     */
    private void setSkuName(List<CountryAgreementPriceWarningVO> targets) {
        if(CollectionUtils.isEmpty(targets)){
            return;
        }
        List<Long> skuIds = targets.stream().map(CountryAgreementPriceWarningVO::getSkuId).collect(Collectors.toList());
        Map<Long, ExternalVO> externalVOMap = querySkuInfo(skuIds);
        for(CountryAgreementPriceWarningVO target :targets){
            ExternalVO skuLang = externalVOMap.get(target.getSkuId());
            if (skuLang != null) {
                SpuLangVO zhName = skuLang.getLangVOList().stream()
                        .filter(line -> LangConstant.LANG_ZH.equals(line.getLang()))
                        .findFirst().orElse(null);
                target.setSkuName(zhName != null ? zhName.getSpuTitle() : null);

                // 设置采销erp
                SpuVO spuVO = skuLang.getSpuVO();

                if (spuVO != null) {
                    target.setBuyerErp(spuVO.getBuyer());
                }

            }
        }

    }

    private Map<Long, ExternalVO> querySkuInfo(List<Long> skuIds) {
        SkuExternalReqVO skuQuery = new SkuExternalReqVO();
        skuQuery.setSkuIds(skuIds);
        skuQuery.setScopeEnums(Sets.newHashSet(SkuScopeEnum.BASE, SkuScopeEnum.LANG));
        return skuExternalManageService.querySkuInfo(skuQuery);
    }

    /**
     * 根据 CustomerSkuPriceDetailPO 对象获取或创建 CustomerSkuPriceWarningPO 对象。
     * @param param CustomerSkuPriceDetailPO 对象，包含客户 SKU 价格详细信息。
     * @return CustomerSkuPriceWarningPO 对象，包含客户 SKU 价格警告信息。
     */
    @Override
    public CountryAgreementPriceWarningPO getOrCreateSkuPriceWarningPO(CountryAgreementPricePO param){
        CountryAgreementPriceWarningPO countryAgreementPriceWarningPO = countryAgreementPriceWarningAtomicService.getValidByBizNo(param.getBizNo());
        if (countryAgreementPriceWarningPO == null) {
            countryAgreementPriceWarningPO = new CountryAgreementPriceWarningPO();
            countryAgreementPriceWarningPO.setCreateTime(DateUtil.getCurrentTime());
            countryAgreementPriceWarningPO.setCreator(param.getCreator());
            countryAgreementPriceWarningPO.setBizNo(param.getBizNo());
        } else {
            countryAgreementPriceWarningPO.setBizNo(null);
        }
        countryAgreementPriceWarningPO.setMkuId(param.getMkuId());
        countryAgreementPriceWarningPO.setSkuId(param.getSkuId());
        countryAgreementPriceWarningPO.setSourceCountryCode(param.getSourceCountryCode());
        countryAgreementPriceWarningPO.setTargetCountryCode(param.getTargetCountryCode());
        countryAgreementPriceWarningPO.setLastCatId(param.getLastCatId());
        countryAgreementPriceWarningPO.setJdCatId(param.getJdCatId());
        countryAgreementPriceWarningPO.setBrandId(param.getBrandId());
        countryAgreementPriceWarningPO.setAgreementPrice(param.getAgreementPrice());
        countryAgreementPriceWarningPO.setCountryCostPrice(param.getCountryCostPrice());
        countryAgreementPriceWarningPO.setCurrency(param.getCurrency());
        countryAgreementPriceWarningPO.setAgreementMark(param.getAgreementMark());
        countryAgreementPriceWarningPO.setCostMark(param.getCostMark());
        countryAgreementPriceWarningPO.setUpdater(param.getUpdater());
        countryAgreementPriceWarningPO.setUpdateTime(param.getUpdateTime());
        return countryAgreementPriceWarningPO;
    }

    /**
     * 设置利润率和状态。
     * @param warningPO 包含利润率和其他相关信息的对象。
     */
    @Override
    public void setProfitRateAndStatus(CountryAgreementPriceWarningPO warningPO){
        BigDecimal rate = null;
        try{
            rate = profitCalculateManageService.calculateAgreementProfitRate(warningPO.getAgreementPrice(),warningPO.getCountryCostPrice());
        }catch (Exception e){
            log.error("CountryAgreementPriceWarningManageServiceImpl.setProfitRateAndStatus error 利润率为空",e);
        }
        warningPO.setProfitRate(rate);

        // 利润率阈值不能为空
        BigDecimal profitRate = null;
        // 超低利润率阈值不能为空
        BigDecimal lowProfitRate = null;
        ProfitRateVO profitRateVO = profitCalculateManageService.getProfitLimitRate(warningPO.getJdCatId(), warningPO.getSourceCountryCode(), warningPO.getTargetCountryCode());
        if(profitRateVO != null){
            profitRate = profitRateVO.getProfitRate();
            lowProfitRate = profitRateVO.getLowProfitRate();
        }

        warningPO.setProfitRateLimit(profitRate);
        warningPO.setProfitRateLowLimit(lowProfitRate);

        if(rate == null || profitRate == null || lowProfitRate == null){
            log.error("CountryAgreementPriceWarningManageServiceImpl.setProfitRateAndStatus rate :{},profitRate:{},"
                + "lowProfitRate:{}, warningPO={}",rate,profitRate,lowProfitRate, JSONObject.toJSONString(warningPO));
//            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.DEEP_RED.getCode());
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.PROFIT_RATE_LOSS.getCode());
            return;
        }

        BigDecimal grossProfit = operDuccConfig.getGrossProfit(warningPO.getTargetCountryCode());

        log.info("CountryAgreementPriceWarningManageServiceImpl.setProfitRateAndStatus, grossProfit={}, rate={}, profitRate={}, lowProfitRate={}, warningPO={}", grossProfit, rate, profitRate, lowProfitRate, JSONObject.toJSONString(warningPO));

        if(rate.compareTo(profitRate)>=0){
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.GREEN.getCode());
        } else if(rate.compareTo(lowProfitRate) >= 0 && rate.compareTo(profitRate) < 0){
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.YELLOW.getCode());
        } else if (rate.compareTo(grossProfit) > 0 && rate.compareTo(lowProfitRate) < 0){
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.RED.getCode());
        } else {
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.DEEP_RED.getCode());
        }
    }

    /**
     * 设置利润率的百分比表示。
     * @param inputs 国家的协议价格警告信息列表。
     */
    private void setProfitRate(List<CountryAgreementPriceWarningVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }

        inputs.forEach(item->{
            if (item.getProfitRate() != null) {
                item.setProfitRate(item.getProfitRate().multiply(Constant.DECIMAL_HUNDRED));
            }
            item.setProfitRateLimit(item.getProfitRateLimit()!=null?item.getProfitRateLimit().multiply(Constant.DECIMAL_HUNDRED):null);
            item.setProfitRateLowLimit(item.getProfitRateLimit()!=null?item.getProfitRateLowLimit().multiply(Constant.DECIMAL_HUNDRED):null);
        });
    }

    /**
     * 根据CountryAgreementPriceWarningPO对象生成对应的PriceLogPO对象。
     * @param param CountryAgreementPriceWarningPO对象，包含警告数据源的相关信息。
     * @return 生成的PriceLogPO对象。
     */
    public PriceLogPO getPriceLogPO(CountryAgreementPriceWarningPO param){
        PriceLogPO priceLogPO = new PriceLogPO();
        priceLogPO.setBizId(param.getId().toString());
        priceLogPO.setBizType(PriceTypeEnum.COUNTRY_WARNING_DATA_SOURCE.getCode());
        priceLogPO.setBizValue(BigDecimal.valueOf(param.getDataStatusSource()));
        priceLogPO.setValue1(param.getTargetCountryCode());
        priceLogPO.setValue2(param.getSkuId().toString());
        priceLogPO.setCreateTime(DateUtil.getCurrentTime());
        priceLogPO.setCreator(param.getCreator());
        priceLogPO.setUpdater(param.getUpdater());
        priceLogPO.setUpdateTime(DateUtil.getCurrentTime());
        return priceLogPO;
    }
}
