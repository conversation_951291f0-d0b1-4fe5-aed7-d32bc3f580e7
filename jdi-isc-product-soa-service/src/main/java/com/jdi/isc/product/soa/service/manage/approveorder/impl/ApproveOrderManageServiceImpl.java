package com.jdi.isc.product.soa.service.manage.approveorder.impl;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.applyinfo.req.ApplyInfoBatchReqDTO;
import com.jdi.isc.product.soa.api.applyinfo.res.ApplyInfoBatchResDTO;
import com.jdi.isc.product.soa.api.approveorder.common.ApproveOrderStatusEnum;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderAuditMqDTO;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderCreateMqDTO;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderMqDTO;
import com.jdi.isc.product.soa.api.approveorder.mq.JoySkyBizApprovalResultMqDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderSaveReqDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderPageQueryDTO;
import com.jdi.isc.product.soa.api.approveorder.res.ApproveOrderPageResDTO;
import com.jdi.isc.product.soa.api.approveorder.res.ApproveOrderSaveResDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiReqDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiResDTO;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.util.StringUtils;
import com.jdi.isc.product.soa.common.util.validation.ValidationUtil;
import com.jdi.isc.product.soa.domain.approveorder.biz.ApproveCallbackVO;
import com.jdi.isc.product.soa.domain.approveorder.biz.ApproveOrderVO;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveOrderPO;
import com.jdi.isc.product.soa.domain.approveorder.res.ApproveOrderResVO;
import com.jdi.isc.product.soa.service.atomic.approveorder.ApproveOrderAtomicService;
import com.jdi.isc.product.soa.service.manage.approveorder.ApproveOrderManageService;
import com.jdi.isc.product.soa.service.manage.approveorder.command.ApproveOrderPageQryExe;
import com.jdi.isc.product.soa.service.manage.approveorder.command.UpdateUnsellableThresholdCmdExe;
import com.jdi.isc.product.soa.service.manage.approveorder.template.callback.borker.BrokerCallbackJoySkyAuditOrderExe;
import com.jdi.isc.product.soa.service.manage.approveorder.template.callback.borker.BrokerCallbackJoySkyCreateOrderExe;
import com.jdi.isc.product.soa.service.manage.approveorder.factory.ApproveOrderBeanFactory;
import com.jdi.isc.product.soa.service.manage.approveorder.template.audit.AbstractAuditApproveOrderExe;
import com.jdi.isc.product.soa.service.manage.approveorder.template.callback.joysky.AbstractJoySkyCallbackMessageExe;
import com.jdi.isc.product.soa.service.manage.approveorder.template.create.AbstractCreateApproveOrderExe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品审核数据维护服务实现
 * <AUTHOR>
 **/

@Slf4j
@Service
public class ApproveOrderManageServiceImpl extends BaseManageSupportService<ApproveOrderVO, ApproveOrderPO> implements ApproveOrderManageService {

    @Resource
    private ApproveOrderAtomicService approveOrderAtomicService;

    @Resource
    private BrokerCallbackJoySkyCreateOrderExe brokerCallbackJoySkyCreateOrderExe;

    @Resource
    private BrokerCallbackJoySkyAuditOrderExe brokerCallbackJoySkyAuditOrderExe;

    @Resource
    private ApproveOrderPageQryExe approveOrderPageQryExe;

    @Resource
    private UpdateUnsellableThresholdCmdExe updateUnsellableThresholdCmdExe;


    @Override
    public ApproveOrderResVO detail(Long id) {
        return null;
    }

    @Override
    public DataResponse<ApproveOrderSaveResDTO> create(ApproveOrderSaveReqDTO input) {
        AbstractCreateApproveOrderExe createBean = ApproveOrderBeanFactory.getCreateBean(input.getOrder().getBizType());
        return createBean.execute(input);
    }

    @Override
    public DataResponse<AuditApiResDTO> audit(AuditApiReqDTO input) {
        AbstractAuditApproveOrderExe auditBean = ApproveOrderBeanFactory.getAuditBean(input.getBizType());
        return auditBean.execute(input);
    }

    @Override
    public DataResponse<String> handleCreateMessage(ApproveOrderMqDTO<ApproveOrderCreateMqDTO> input) {
        return brokerCallbackJoySkyCreateOrderExe.execute(input);
    }

    @Override
    public DataResponse<String> handleAuditMessage(ApproveOrderMqDTO<ApproveOrderAuditMqDTO> input) {
        return brokerCallbackJoySkyAuditOrderExe.execute(input);
    }

    @Override
    public void batchJoySkyApprove(JoySkyBizApprovalResultMqDTO approve) {

        log.info("batchJoySkyApprove, approve={}", JSONObject.toJSONString(approve));

        String errorMessage = ValidationUtil.validateFindFirstError(approve);

        if (StringUtils.isNotEmpty(errorMessage)) {
            throw new ProductBizException("审批流回调消息校验失败，请检查参数！%s", errorMessage);
        }

        String processInstanceId = approve.getProcessInstanceId();

        ApproveOrderPO order = approveOrderAtomicService.getByProcessTypeAndProcessInstanceId(approve.getProcessType(), processInstanceId);

        AbstractJoySkyCallbackMessageExe auditBean = ApproveOrderBeanFactory.getJoySkyCallbackBean(order.getBizType());
        DataResponse<AuditApiResDTO> execute = auditBean.execute(approve, order);
        if (!execute.getSuccess()) {
            log.info("batchJoySkyApprove, error, approve={}, execute={}", JSONObject.toJSONString(approve), JSONObject.toJSONString(execute));
            throw new ProductBizException(execute.getMessage());
        }
        log.info("batchJoySkyApprove, success, approve={}, execute={}", JSONObject.toJSONString(approve), JSONObject.toJSONString(execute));
    }

    @Override
    public PageInfo<ApproveOrderPageResDTO> listWithPage(ApproveOrderPageQueryDTO input) {
        return approveOrderPageQryExe.execute(input);
    }

    @Override
    public DataResponse<ApplyInfoBatchResDTO> updateUnsellableThreshold(ApplyInfoBatchReqDTO input) {
        return updateUnsellableThresholdCmdExe.execute(input);
    }

    @Override
    public DataResponse<Boolean> handleTimeoutData() {
        List<ApproveOrderPO> list = approveOrderAtomicService.listTimeoutData();

        for (ApproveOrderPO item : list) {
            ApproveCallbackVO approveCallbackVO = new ApproveCallbackVO(ApproveOrderStatusEnum.CLOSED.getCode(), "审批超时自动关闭", item.getCallbackFailCount() + 1, null, null);
            approveOrderAtomicService.updateCallbackFailInfo(item, approveCallbackVO);
        }

        return DataResponse.success();
    }
}