package com.jdi.isc.product.soa.service.protocol.jsf.wimp.category;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.wimp.category.CategoryBuyerRelationApiService;
import com.jdi.isc.product.soa.api.wimp.category.biz.CategoryBuyerRelationDTO;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.category.biz.CategoryBuyerRelationVO;
import com.jdi.isc.product.soa.service.manage.category.CategoryBuyerRelationManageService;
import com.jdi.isc.product.soa.service.mapstruct.category.CategoryBuyerRelationConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: 类目采销关系表api服务实现
 * @Author: zhaokun51
 * @Date: 2025/06/10 13:20
 **/

@Slf4j
@Service
public class CategoryBuyerRelationApiServiceImpl implements CategoryBuyerRelationApiService {

    @Resource
    private CategoryBuyerRelationManageService categoryBuyerRelationManageService;



    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> saveOrUpdate(CategoryBuyerRelationDTO input) {
        log.info("CategoryBuyerRelationApiServiceImpl.saveOrUpdate param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CategoryBuyerRelationVO param = CategoryBuyerRelationConvert.INSTANCE.categoryBuyerRelationApiDTO2Vo(input);
        return DataResponse.success(categoryBuyerRelationManageService.saveOrUpdate(param));
    }

}
