package com.jdi.isc.product.soa.service.protocol.jsf;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.stock.IscProductSoaStockReadApiService;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockResDTO;
import com.jdi.isc.product.soa.service.manage.stock.StockManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 库存写服务实现类
 * <AUTHOR>
 * @date 2024/6/15
 */
@Service
@Slf4j
public class IscProductSoaStockReadApiServiceImpl implements IscProductSoaStockReadApiService {

    @Resource
    private StockManageService stockManageService;

    /** 根据工业国际skuId查询库存信息*/
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, StockResDTO>> getStock(StockManageReqDTO req) {
        return DataResponse.success(stockManageService.getStock(req));
    }

}
