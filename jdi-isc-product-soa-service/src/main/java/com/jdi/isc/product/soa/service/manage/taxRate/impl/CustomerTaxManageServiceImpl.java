package com.jdi.isc.product.soa.service.manage.taxRate.impl;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.fastjson.JSON;
import com.jd.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.enums.TaxTypeEnum;
import com.jdi.isc.product.soa.api.taxRate.res.CustomerSaleTaxRateApiDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.TaxConstant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPricePO;
import com.jdi.isc.product.soa.domain.enums.price.SkuSalePriceTypeEnums;
import com.jdi.isc.product.soa.domain.price.biz.MkuPriceReqVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuTaxBatchReqVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuTaxResVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.po.ProductGlobalAttributePO;
import com.jdi.isc.product.soa.domain.taxRate.biz.BrIcmsRateConfig;
import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxResVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.IscSkuImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.BrSkuTaxPO;
import com.jdi.isc.product.soa.domain.taxRate.req.CustomerTaxQueryReqVO;
import com.jdi.isc.product.soa.domain.taxRate.res.CustomerMkuTaxResVO;
import com.jdi.isc.product.soa.domain.taxRate.res.CustomerSkuTaxResVO;
import com.jdi.isc.product.soa.domain.taxRate.res.CustomerTaxResVO;
import com.jdi.isc.product.soa.price.api.salePrice.req.SalePriceReqDTO;
import com.jdi.isc.product.soa.price.api.salePrice.res.SalePriceResDTO;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.ProductGlobalAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.countryTax.BrSkuTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.customerMku.CustomerMkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuRelationManageService;
import com.jdi.isc.product.soa.service.manage.price.salePrice.SkuSalePriceManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.CustomerTaxManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.CountryTaxManageService;
import com.jdi.isc.product.soa.service.mapstruct.customer.CustomerConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description：客制化价格和税金管理服务
 * @Date 2024-12-16
 */
@Slf4j
@Service
public class CustomerTaxManageServiceImpl implements CustomerTaxManageService, InitializingBean {

    @Resource
    private CustomerMkuPriceManageService customerMkuPriceManageService;
    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;
    @Resource
    private Map<String, SkuSalePriceManageService> skuSalePriceManageServiceMap;
    @Resource
    private MkuRelationManageService mkuRelationManageService;
    @Resource
    private CountryTaxManageService countryTaxManageService;
    @Resource
    private ExecutorService skuCustomerTaxExecutorService;
    /**是否进口商品 跨境属性**/
    private static final Long IMPORT_ATTRIBUTE_ID = 75L;
    /**NCM码属性ID**/
    public static final Long NCM_ATTRIBUTE_ID = 71L;
    /**
     *  巴西销售CST跨境属性ID
     */
    private static final Long BR_SALE_CST_ATTRIBUTE_ID = 122L;

    private static final Long ATTRIBUTE_YES_ID = 100082L;
    private static final Long ATTRIBUTE_NO_ID = 100083L;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<CustomerTaxResVO> batchQueryCustomerTax(CustomerTaxQueryReqVO reqDTO) {
        // MKU和SKU ID关系
        Map<Long, Long> mkuSkuMap = getMkuFixedSkuMap(reqDTO);

        CustomerTaxResVO customerTaxResVO = new CustomerTaxResVO();
        customerTaxResVO.setClientCode(reqDTO.getClientCode());
        // 查询客户信息
        CustomerVO customerVO = customerManageService.detail(reqDTO.getClientCode());

        // 处理销侧税率
        switch (customerVO.getCountry()){
            //巴西销项税
            case CountryConstant.COUNTRY_BR:
                this.handleBrSaleTaxMap(mkuSkuMap, customerTaxResVO,customerVO);
                break;
            default:
                this.handleDefaultTax(mkuSkuMap, customerTaxResVO, customerVO);
                break;
        }
        return DataResponse.success(customerTaxResVO);
    }

    private Map<Long, Long> getMkuFixedSkuMap(CustomerTaxQueryReqVO reqDTO) {
        Map<Long, Long> mkuSkuMap = Maps.newHashMap();
        // 查询客户MKU固定SKU信息
        MkuPriceReqVO mkuPriceReqVO = new MkuPriceReqVO();
        mkuPriceReqVO.setMkuIdList(reqDTO.getMkuIds());
        mkuPriceReqVO.setClientCode(reqDTO.getClientCode());
        List<CustomerMkuPricePO> customerMkuPricePoList = customerMkuPriceManageService.getCustomerMkuFixedSkuListByMkuClient(mkuPriceReqVO);

        Set<Long> inputMkuIds = reqDTO.getMkuIds();
        if (CollectionUtils.isEmpty(customerMkuPricePoList)) {
            mkuSkuMap.putAll(mkuRelationManageService.queryMkuSkuIdMapByMkuIds(Lists.newArrayList(inputMkuIds)));
        }else {
            customerMkuPricePoList.forEach(mkuPricePO -> mkuSkuMap.put(mkuPricePO.getMkuId(),mkuPricePO.getFixedSkuId()));
            if (inputMkuIds.size() != mkuSkuMap.size()) {
                Collection<Long> subtract = CollectionUtils.subtract(inputMkuIds, mkuSkuMap.keySet());
                mkuSkuMap.putAll(mkuRelationManageService.queryMkuSkuIdMapByMkuIds(Lists.newArrayList(subtract)));
            }
        }
        return mkuSkuMap;
    }

    @Override
    public DataResponse<Map<Long, CustomerSkuTaxResVO>> batchQueryBrCustomerSkuTax(Set<Long> skuIds) {
        // 查询商品上的“是否进口商品”、NCM、销售CST 跨境属性
        Map<Long, List<ProductGlobalAttributePO>> skuGlobalAttributeMap = productGlobalAttributeAtomicService.queryGlobalAttributeMapBySkuAndIds(skuIds, Sets.newHashSet(IMPORT_ATTRIBUTE_ID,NCM_ATTRIBUTE_ID,BR_SALE_CST_ATTRIBUTE_ID));
        // 查询SKU信息 货源国
        List<SkuPO> skuPOList = skuAtomicService.querySkuPartialColumnBySkuIds(skuIds);

        if (CollectionUtils.isEmpty(skuPOList)) {
            return DataResponse.success();
        }

        Map<Long, CustomerSkuTaxResVO> resultMap = Maps.newHashMap();
        skuPOList.forEach(skuPO -> {
            Long skuId = skuPO.getSkuId();
            resultMap.put(skuId, new CustomerSkuTaxResVO(skuId,this.getNcm(skuGlobalAttributeMap.get(skuId)),this.getCst(skuGlobalAttributeMap, skuPO)));
        });
        return DataResponse.success(resultMap);
    }



    @Resource
    private OperDuccConfig operDuccConfig;

    @Resource
    private BrSkuTaxAtomicService brSkuTaxAtomicService;

    @Override
    public CustomerSaleTaxRateApiDTO getBrCustomerInitSaleTaxRate(String clientCode, Long skuId) {


        SkuPO sku = skuAtomicService.getSkuPoBySkuId(skuId);

        if (sku == null) {
            log.info("sku不存在. skuId:{}", skuId);
            return null;
        }

        // ipi rate
        BigDecimal ipiRate = this.getIpiRate(sku);

        // icms rate
        BigDecimal icmsRate = getIcmsRate(skuId, clientCode);

        CustomerSaleTaxRateApiDTO result = new CustomerSaleTaxRateApiDTO(clientCode, skuId);

        result.addTax("ICMS", icmsRate);
        result.addTax("IPI", ipiRate);
        result.addTax("COFINS", new BigDecimal("7.6"));
        result.addTax("PIS", new BigDecimal("1.65"));

        return result;
    }

    private BigDecimal getIcmsRate(Long skuId, String clientCode) {

        CustomerVO customerVO = customerManageService.detail(clientCode);
        if (customerVO == null) {
            log.info("客户不存在. clientCode:{}", clientCode);
            return null;
        }

        String jdMainCode = customerVO.getJdMainCode();
        String customerMainCode = customerVO.getCustomerMainCode();

        if (StringUtils.isAnyEmpty(jdMainCode, customerMainCode)) {
            log.info("非巴西自动计算iscms客户. clientCode:{}", clientCode);
            return null;
        }

        SkuPO sku = skuAtomicService.getSkuPoBySkuId(skuId);

        if (sku == null) {
            log.info("getIcmsRate sku不存在. skuId:{}", skuId);
            return null;
        }

        BigDecimal icmsRate = null;
        String firstCtsCode = null;
        Map<Long, List<ProductGlobalAttributePO>> skuGlobalAttributeMap = productGlobalAttributeAtomicService.queryGlobalAttributeMapBySkuAndIds(Sets.newHashSet(skuId), Sets.newHashSet(IMPORT_ATTRIBUTE_ID,NCM_ATTRIBUTE_ID,BR_SALE_CST_ATTRIBUTE_ID));

//        Map<Long, List<ProductGlobalAttributePO>> skuGlobalAttributeMap = Maps.newHashMap();
//        if (productGlobalAttribute != null && StringUtils.isNotBlank(productGlobalAttribute.getAttributeValue())) {
//            skuGlobalAttributeMap.put(skuId, Lists.newArrayList(productGlobalAttribute));
//        } else {
//            ProductGlobalAttributePO newAttribute = new ProductGlobalAttributePO();
//            newAttribute.setKeyId(skuId);
//            newAttribute.setAttributeId(IMPORT_ATTRIBUTE_ID);
//            newAttribute.setAttributeValueId(BR_SALE_CST_ATTRIBUTE_ID);
//            skuGlobalAttributeMap.put(skuId, Lists.newArrayList(newAttribute));
//        }

        String cst = this.getCst(skuGlobalAttributeMap, sku);

        log.info("getIcmsRate 获取cst. skuId:{}， cst={}", skuId, cst);

        if (StringUtils.isNotEmpty(cst)) {
            firstCtsCode = StringUtils.substring(cst, 0, 1);
        }

        if (firstCtsCode == null) {
            log.info("cst 为空，不执行匹配逻辑. skuId:{}", skuId);
            return null;
        }


        List<BrIcmsRateConfig> brIcmsRateConfigList = operDuccConfig.getBrIcmsRateConfigList();

        for (BrIcmsRateConfig brIcmsRateConfig : brIcmsRateConfigList) {
            if (!jdMainCode.equals(brIcmsRateConfig.getJdMainCode()) || !customerMainCode.equals(brIcmsRateConfig.getCustomerMainCode())) {
                continue;
            }

            // 如果都是空
//            if (StringUtils.isEmpty(firstCtsCode)) {
//                if (StringUtils.isEmpty(brIcmsRateConfig.getCtsFirstLetter())) {
//                    icmsRate = brIcmsRateConfig.getRate();
//                    break;
//                } else {
//                    continue;
//                }
//            }

            if (StringUtils.isNotEmpty(brIcmsRateConfig.getCtsFirstLetter()) && brIcmsRateConfig.getCtsFirstLetter().contains(firstCtsCode)) {
                icmsRate = brIcmsRateConfig.getRate();
                break;
            }
        }
        return icmsRate;
    }


    private BigDecimal getIpiRate(SkuPO sku) {
        if (sku == null) {
            return null;
        }

        if (!CountryConstant.isChina(sku.getSourceCountryCode())) {
            return BigDecimal.ZERO;
        }

        if (sku.getJdSkuId() == null) {
            log.info("jdSkuId不存在. skuId:{}", sku.getSkuId());
            return null;
        }

        BrSkuTaxPO brSkuTaxPO = brSkuTaxAtomicService.getOneByJdSkuId(sku.getJdSkuId());
        if (brSkuTaxPO == null) {
            log.info("巴西进口税不存在. skuId:{}", sku.getSkuId());
            return null;
        }
        BigDecimal industryProductTax = brSkuTaxPO.getIndustryProductTax();

        if (industryProductTax == null) {
            log.info("巴西进口税不存在. skuId:{}", sku.getSkuId());
            return null;
        }

        return industryProductTax.multiply(new BigDecimal(100));
    }

    /**
     * 处理巴西销项税率映射。
     * @param mkuSkuMap 客户商品价格列表。
     * @param customerTaxResVO 客户税务信息返回对象。
     */
    private void handleBrSaleTaxMap(Map<Long, Long> mkuSkuMap, CustomerTaxResVO customerTaxResVO,CustomerVO customerVO) {
        Map<Long, CustomerMkuTaxResVO> mkuTaxResDTOMap = Maps.newHashMap();
        // 查询SKU的hsCode、ncm码和CST号
        Set<Long> skuIds = Sets.newHashSet(mkuSkuMap.values());

        // 查询商品上的“是否进口商品”跨境属性、NCM、销售CST 跨境属性
        Map<Long, List<ProductGlobalAttributePO>> skuGlobalAttributeMap = productGlobalAttributeAtomicService.queryGlobalAttributeMapBySkuAndIds(skuIds, Sets.newHashSet(IMPORT_ATTRIBUTE_ID,NCM_ATTRIBUTE_ID,BR_SALE_CST_ATTRIBUTE_ID));
        // 查询SKU信息 货源国
        List<SkuPO> skuPOList = skuAtomicService.querySkuPartialColumnBySkuIds(skuIds);
        Map<Long, SkuPO> skuPOMap = Optional.ofNullable(skuPOList).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(SkuPO::getSkuId, Function.identity()));

        // 批量查询SKU的销价
        Map<Long, SalePriceResDTO> skuSalePriceMap = asyncBatchGetSkuSalePriceMap(customerVO, skuIds);

        mkuSkuMap.forEach((mkuId,skuId)->{
            CustomerMkuTaxResVO mkuTaxResVO = new CustomerMkuTaxResVO();
            mkuTaxResVO.setMkuId(mkuId);
            // 设置NCM码
            mkuTaxResVO.setNcm(this.getNcm(skuGlobalAttributeMap.get(skuId)));
            // 处理CST
            mkuTaxResVO.setCst(this.getCst(skuGlobalAttributeMap, skuPOMap.get(skuId)));
            // 处理巴西税率
            SalePriceResDTO salePriceResDTO = skuSalePriceMap.getOrDefault(skuId, new SalePriceResDTO());
            if(salePriceResDTO != null && MapUtils.isNotEmpty(salePriceResDTO.getSalePriceTaxRes())){
                Map<String,String> taxRateMap = Maps.newHashMap();
                salePriceResDTO.getSalePriceTaxRes().forEach((key,value)-> taxRateMap.put(key, Objects.nonNull(value) ? String.valueOf(value) : null));
                mkuTaxResVO.setTaxRateMap(taxRateMap);
            }
            mkuTaxResDTOMap.put(mkuId, mkuTaxResVO);
        });
        customerTaxResVO.setMkuTaxResDTOMap(mkuTaxResDTOMap);
    }


    /**
     * 异步批量获取商品的销售价格信息。
     * @param customerVO 客户信息对象，用于确定国家和获取相应的价格服务。
     * @param skuIds 需要查询销售价格的商品ID集合。
     * @return 键为商品ID，值为对应的销售价格信息的Map。
     */
    private Map<Long, SalePriceResDTO> asyncBatchGetSkuSalePriceMap(CustomerVO customerVO, Set<Long> skuIds) {
        // 根据国家获取价格服务
        SkuSalePriceManageService skuSalePriceManageService = skuSalePriceManageServiceMap.get(SkuSalePriceTypeEnums.getEnumByCountryCode(customerVO.getCountry()).getServiceName());
        Map<Long,SalePriceResDTO> skuSalePriceMap = Maps.newHashMapWithExpectedSize(skuIds.size());
        List<List<Long>> partitionSkuIdList = Lists.partition(Lists.newArrayList(skuIds), 5);
        for (List<Long> sub : partitionSkuIdList) {
            List<Future<SalePriceResDTO>> salePriceResFutureList = Lists.newArrayListWithExpectedSize(sub.size());
            for (Long skuId : sub) {
                salePriceResFutureList.add(skuCustomerTaxExecutorService.submit(() -> skuSalePriceManageService.getSalePrice(this.getSalePriceReqDTO(customerVO, skuId))));
            }

            salePriceResFutureList.forEach(future -> {
                try {
                    SalePriceResDTO salePriceResDTO = future.get(1, TimeUnit.SECONDS);
                    if (Objects.nonNull(salePriceResDTO)){
                        skuSalePriceMap.put(salePriceResDTO.getSkuId(), salePriceResDTO);
                    }
                } catch (Exception e) {
                    log.error("handleBrSaleTaxMap 异步处理巴西税率信息异常 ", e);
                    if (null != future) {
                        future.cancel(Boolean.TRUE);
                    }
                }
            });
        }
        return skuSalePriceMap;
    }

    private SalePriceReqDTO getSalePriceReqDTO(CustomerVO customerVO, Long skuId) {
        SalePriceReqDTO salePriceReqDTO = new SalePriceReqDTO();
        salePriceReqDTO.setSkuId(skuId);
        salePriceReqDTO.setClientCode(customerVO.getClientCode());
        salePriceReqDTO.setCustomerDTO(CustomerConvert.INSTANCE.customerVo2Dto(customerVO));
        salePriceReqDTO.setCurrencyCode(customerVO.getSaleCurrency());
        return salePriceReqDTO;
    }

    /**
     * 根据商品信息和税务规则计算并设置CST税率。
     * https://joyspace.jd.com/pages/0XT5BPnd4uFwcM9csGWU CST取数规则
     * 新逻辑：https://joyspace.jd.com/pages/bTc2m2xuIOjM1h0V9159
     * @param skuGlobalAttributeMap SKU的全局属性映射表。
     * @param skuPO SKU的PO对象映射表。
     */
    private String getCst(Map<Long, List<ProductGlobalAttributePO>> skuGlobalAttributeMap, SkuPO skuPO) {
        // https://joyspace.jd.com/pages/0XT5BPnd4uFwcM9csGWU CST取数规则
        if (Objects.isNull(skuPO)) {
            return null;
        }

        Long skuId = skuPO.getSkuId();
        AtomicReference<String> cst = new AtomicReference<>();
        // 在跨境属性取销售CST值
        if (skuGlobalAttributeMap.containsKey(skuId) && skuGlobalAttributeMap.get(skuId).stream().anyMatch(productGlobalAttributePO -> BR_SALE_CST_ATTRIBUTE_ID.equals(productGlobalAttributePO.getAttributeId()))) {
            skuGlobalAttributeMap.get(skuId).stream()
                    .filter(productGlobalAttributePO -> BR_SALE_CST_ATTRIBUTE_ID.equals(productGlobalAttributePO.getAttributeId()))
                    .findFirst().ifPresent(attributePo -> {
                        String sourceCountryCode = skuPO.getSourceCountryCode();
                        // 跨境和巴西本土品才会取销售CST的值
                        if (StringUtils.isNotBlank(attributePo.getAttributeValue()) && (CountryConstant.COUNTRY_BR.equals(sourceCountryCode) || CountryConstant.COUNTRY_ZH.equals(sourceCountryCode))) {
                            cst.set(attributePo.getAttributeValue());
                        }
                    });
        }

        if (StringUtils.isNotBlank(cst.get())){
            return cst.get();
        }

        // 计算CST
        if (CountryConstant.COUNTRY_ZH.equals(skuPO.getSourceCountryCode())) {
            cst.set(TaxConstant.CST_100);
        }
        if (skuGlobalAttributeMap.containsKey(skuId) && skuGlobalAttributeMap.get(skuId).stream().anyMatch(productGlobalAttributePO -> IMPORT_ATTRIBUTE_ID.equals(productGlobalAttributePO.getAttributeId()))) {
           skuGlobalAttributeMap.get(skuId).stream()
                    .filter(productGlobalAttributePO -> IMPORT_ATTRIBUTE_ID.equals(productGlobalAttributePO.getAttributeId()))
                    .findFirst().ifPresent(attributePo -> {
                        String sourceCountryCode = skuPO.getSourceCountryCode();
                        if (ATTRIBUTE_YES_ID.equals(attributePo.getAttributeValueId()) && CountryConstant.COUNTRY_BR.equals(sourceCountryCode)) {
                            cst.set(TaxConstant.CST_200);
                        }else if (ATTRIBUTE_NO_ID.equals(attributePo.getAttributeValueId()) && CountryConstant.COUNTRY_BR.equals(sourceCountryCode)){
                            cst.set(TaxConstant.CST_000);
                        }
                    });
        }
        return cst.get();
    }

    /**
     * 处理NCM属性并将其设置到mkuTaxResVO对象中。
     * @param productGlobalAttributePOList 全局属性列表。
     */
    private String getNcm(List<ProductGlobalAttributePO> productGlobalAttributePOList){
        if (CollectionUtils.isEmpty(productGlobalAttributePOList)) {
            return null;
        }

        AtomicReference<String> ncm = new AtomicReference<>();
        if (productGlobalAttributePOList.stream().anyMatch(productGlobalAttributePO -> NCM_ATTRIBUTE_ID.equals(productGlobalAttributePO.getAttributeId()))) {
            productGlobalAttributePOList.stream().filter(productGlobalAttributePO -> NCM_ATTRIBUTE_ID.equals(productGlobalAttributePO.getAttributeId()))
                    .findFirst()
                    .ifPresent(productGlobalAttributePO -> {
                        // 格式化NCM码
                        ncm.set(formatNcm(productGlobalAttributePO.getAttributeValue()));
            });
        }
        return ncm.get();
    }

    /**
     * WIOP税率接口返回数据拼装：格式xxxx.xx.xx，例如 8305.90.00 （从左到右第四位拼装"."，第六位拼装"."）
     * 1、超8位仍然按照这个逻辑来，4和6位插入
     * 2、小于6位，只在4位插入。小于4位，不插入点
     * @param ncm NCM码
     * @return  格式化后的NCM码
     */
    private static String formatNcm(String ncm) {
        log.info("CustomerTaxManageServiceImpl.formatNcm 初始值 ncm={}",ncm);
        if (StringUtils.isBlank(ncm)) {
            return ncm;
        }
        // 已经带点的数据，先替换为空串
        ncm = ncm.replace(Constant.DOT, "");
        int length = ncm.length();

        if (length <= 4) {
            // 小于4个字符，不插入点号
            return ncm;
        }

        StringBuilder sb = new StringBuilder(ncm);

        // 插入第一个点号
        sb.insert(4, '.');

        // 插入第二个点号（如果长度大于等于7）
        if (length >= 7) {
            sb.insert(7, '.');
        }
        log.info("CustomerTaxManageServiceImpl.formatNcm 格式化后的ncm值 ncm={}", sb);
        return sb.toString();
    }

    /**
     * 处理其他国家增值税率
     * @param mkuSkuMap MKU 和SKU的关系
     * @param customerTaxResVO 返回结果
     * @param customerVO 客户信息
     */
    private void handleDefaultTax(Map<Long, Long> mkuSkuMap, CustomerTaxResVO customerTaxResVO,CustomerVO customerVO) {
        Map<Long, CustomerMkuTaxResVO> mkuTaxResDTOMap = Maps.newHashMap();
        // 根据客户国家获取价格服务
        SkuSalePriceManageService skuSalePriceManageService = skuSalePriceManageServiceMap.get(SkuSalePriceTypeEnums.getEnumByCountryCode(customerVO.getCountry()).getServiceName());

        Set<Long> skuIds = Sets.newHashSet(mkuSkuMap.values());
        Map<Long, SkuTaxResVO> skuTaxResMap = skuSalePriceManageService.batchGetTaxValueBySkuIds(new SkuTaxBatchReqVO(skuIds, customerVO));
        // 获取海关编码
        Map<Long, String> skuHsCodeMap = this.getSkuHsCodeMap(skuIds, customerVO.getCountry());
        mkuSkuMap.forEach((mkuId,skuId)-> {
            CustomerMkuTaxResVO mkuTaxResVO = new CustomerMkuTaxResVO();
            mkuTaxResVO.setMkuId(mkuId);
            mkuTaxResVO.setHsCode(skuHsCodeMap.getOrDefault(skuId,null));
            SkuTaxResVO skuTaxResVO = skuTaxResMap.get(skuId);
            log.info("handleDefaultTax 查询商品销项税 clientCode={} countryCode={} skuId={},taxValue={}",customerVO.getClientCode(),customerVO.getCountry(), skuId, JSON.toJSONString(skuTaxResVO));
            Map<String,String> taxRateMap = Maps.newHashMap();
            taxRateMap.put(TaxTypeEnum.VAT.name(),  this.getRate(skuTaxResVO.getTaxMap().get(TaxTypeEnum.VAT.name())));
            mkuTaxResVO.setTaxRateMap(taxRateMap);
            mkuTaxResDTOMap.put(mkuId, mkuTaxResVO);
        });
        customerTaxResVO.setMkuTaxResDTOMap(mkuTaxResDTOMap);
    }

    private String getRate(BigDecimal taxValue) {
        if (Objects.isNull(taxValue)) {
            return "";
        }
        return new BigDecimal(100).multiply(taxValue).toPlainString();
    }


    /**
     * 根据给定的 SKU IDs 和国家代码，获取 SKU 的海关编码映射。
     * @param skuIds SKU IDs 集合
     * @param countryCode 国家代码
     * @return SKU ID 到海关编码的映射，若无匹配则返回空 Map
     */
    private Map<Long,String> getSkuHsCodeMap(Set<Long> skuIds,String countryCode){
        // 分批查询进口海关编码
        Map<Long, CrossBorderImportTaxResVO> crossBorderImportTaxResDTOMap = this.getImportTaxResMap(countryCode, skuIds);

        Map<Long,String> skuHsCodeMap = Maps.newHashMap();
        skuIds.forEach(skuId -> {
            CrossBorderImportTaxResVO importTaxResDTO = crossBorderImportTaxResDTOMap.get(skuId);
            skuHsCodeMap.put(skuId, Objects.nonNull(importTaxResDTO) ? importTaxResDTO.getHsCode() : null);
        });

        return skuHsCodeMap;
    }

    /**
     * 根据国家代码和商品 SKU ID 集合，分批查询并返回跨境进口税信息。
     * @param countryCode 国家代码
     * @param jdSkuIdSet 商品 SKU ID 集合
     * @return 跨境进口税信息 Map，key 为商品 SKU ID，value 为 CrossBorderImportTaxResDTO 对象
     */
    private  Map<Long, CrossBorderImportTaxResVO> getImportTaxResMap(String countryCode, Set<Long> skuIds) {
        // 查询进口税率海关编码信息
        DataResponse<Map<Long, CrossBorderImportTaxResVO>> response = countryTaxManageService.queryImportTaxByIscSkuIds(new IscSkuImportTaxReqVO(skuIds,countryCode));
        Map<Long, CrossBorderImportTaxResVO> crossBorderImportTaxResDTOMap = Maps.newHashMap();
        // 处理返回结果
        if (response.getSuccess() && MapUtils.isNotEmpty(response.getData())) {
            crossBorderImportTaxResDTOMap.putAll(response.getData());
        }
        return crossBorderImportTaxResDTOMap;
    }

    public static void main(String[] args) {
        String input1 = "83059000";
        String input2 = "830.5";
        String input3 = "830";
        String input4 = "830590";
        String input5 = "83059000123";
        String input6 = "1234.5";

        List<BrIcmsRateConfig> list = Lists.newArrayList();

        list.add(new BrIcmsRateConfig("SP", "BA", "1,2,7", new BigDecimal("0.04")));
        list.add(new BrIcmsRateConfig("SP", "BA", "0,3,4,5,6,8,9", new BigDecimal("0.07")));
        list.add(new BrIcmsRateConfig("BA", "BA", "", new BigDecimal("0.205")));

        System.out.println(JSONObject.toJSONString(list));

        System.out.println(formatNcm(input1)); // 输出: 8305.90.00
        System.out.println(formatNcm(input2)); // 输出: 8305
        System.out.println(formatNcm(input3)); // 输出: 830
        System.out.println(formatNcm(input4)); // 输出: 8305.90
        System.out.println(formatNcm(input5)); // 输出: 8305.90.00123
        System.out.println(formatNcm(input6)); // 输出: 1234.5
    }

    @Override
    public void afterPropertiesSet() throws Exception {
//        this.getBrCustomerInitSaleTaxRate("uDFnyS3iUOBc3D1EP1XP", 80000000137L);
    }
}
