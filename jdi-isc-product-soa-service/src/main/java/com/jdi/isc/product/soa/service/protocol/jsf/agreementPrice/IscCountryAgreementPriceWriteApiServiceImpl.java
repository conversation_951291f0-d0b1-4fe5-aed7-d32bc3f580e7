package com.jdi.isc.product.soa.service.protocol.jsf.agreementPrice;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.agreementPrice.IscCountryAgreementPriceWriteApiService;
import com.jdi.isc.product.soa.api.agreementPrice.req.CountryAgreementPriceAuditDTO;
import com.jdi.isc.product.soa.api.agreementPrice.req.CountryAgreementPriceReqDTO;
import com.jdi.isc.product.soa.api.agreementPrice.req.CountryAgreementPriceSyncReqDTO;
import com.jdi.isc.product.soa.api.agreementPrice.req.PriceRefreshDTO;
import com.jdi.isc.product.soa.api.agreementPrice.res.CountryAgreementPriceSyncResDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.enums.AgreementPriceDataSourceTypeEnums;
import com.jdi.isc.product.soa.api.common.enums.CustomerSkuDataSourceTypeEnums;
import com.jdi.isc.product.soa.api.price.req.PriceAvailableSaleStatusReqDTO;
import com.jdi.isc.product.soa.api.price.res.PriceAvailableSaleStatusResDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuPoolFlagDTO;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.domain.enums.product.FulfillmentRateTypeEnum;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceAuditVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceReqVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.InitAgreementPriceVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.extendPrice.biz.CurrencyPriceVO;
import com.jdi.isc.product.soa.domain.sku.biz.ProductIdVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCountryVO;
import com.jdi.isc.product.soa.rpc.suport.AlertHelper;
import com.jdi.isc.product.soa.rpc.utils.ExceptionUtil;
import com.jdi.isc.product.soa.service.adapter.mapstruct.agreementPrice.CountryAgreementPriceConvert;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceWarningManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuSalePriceDetailManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceDraftManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceWarningManageService;
import com.jdi.isc.product.soa.service.manage.price.extendPrice.CountryExtendPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.impl.SkuPriceManageServiceImpl;
import com.jdi.isc.product.soa.service.manage.price.warning.aggreementprice.manager.CountryAgreementPriceWarningManager;
import com.jdi.isc.product.soa.service.manage.price.warning.customerprice.manager.CustomerPriceWarningManager;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant.SPU_AUDIT_LOCK_FAILED;

/**
 * @Description: 国家协议价api服务实现
 * @Author: wangpeng965
 * @Date: 2025/03/03 16:45
 **/

@Slf4j
@Service
public class IscCountryAgreementPriceWriteApiServiceImpl implements IscCountryAgreementPriceWriteApiService, InitializingBean {
    @Resource
    private CountryAgreementPriceDraftManageService countryAgreementPriceDraftManageService;
    @Resource
    private CountryAgreementPriceWarningManageService countryAgreementPriceWarningManageService;
    @Resource
    private CountryAgreementPriceManageService countryAgreementPriceManageService;
    @Resource
    private JimUtils jimUtils;
    @Resource
    private CountryExtendPriceManageService countryExtendPriceManageService;
    @Resource
    private SkuReadManageService skuReadManageService;

    @Resource
    private CustomerSkuPriceWarningManageService customerSkuPriceWarningManageService;

    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @Resource
    private CustomerSkuSalePriceDetailManageService customerSkuSalePriceDetailManageService;

    @Resource
    private CustomerPriceWarningManager customerPriceWarningManager;

    @Resource
    private CountryAgreementPriceWarningManager countryAgreementPriceWarningManager;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> updateDraft(CountryAgreementPriceReqDTO dto) {
        log.info("IscCountryAgreementPriceWriteApiServiceImpl.saveOrUpdate input:{}", JSONObject.toJSONString(dto));
        ApiInitUtils.init(dto);
        CountryAgreementPriceVO countryAgreementPriceVO = CountryAgreementPriceConvert.INSTANCE.reqDto2Vo(dto);
        countryAgreementPriceVO.setUpdater(dto.getPin());
        String requestId = UUID.randomUUID().toString();
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.AGREEMENT_PRICE_DRAFT_SKU_LOCK, dto.getTargetCountryCode(), String.valueOf(dto.getSkuId()));
        try {
            AssertValidation.isTrue(!jimUtils.simpleLock(lockKey, requestId, 60),
                    DataResponseCodeConstant.SPU_AUDIT_LOCK_FAILED, "加锁失败");
            DataResponse<Boolean> response = countryAgreementPriceDraftManageService.save(countryAgreementPriceVO);
            return response;
        } catch (BizException e) {
            log.error("IscCountryAgreementPriceWriteApiServiceImpl.updateDraft error CountryAgreementPriceReqDTO={},err={}", JSON.toJSONString(dto), e.getMessage(), e);
            return DataResponse.error(e.getMessage());
        } catch (Exception e) {
            log.error("IscCountryAgreementPriceWriteApiServiceImpl.updateDraft error CountryAgreementPriceReqDTO={},err={}", JSON.toJSONString(dto), e.getMessage(), e);
            return DataResponse.error(e.getMessage());
        } finally {
            jimUtils.simpleLockRelease(lockKey, requestId);
        }
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchApprove(CountryAgreementPriceAuditDTO input) {
        log.info("IscCountryAgreementPriceWriteApiServiceImpl.batchApprove param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CountryAgreementPriceAuditVO reqVO = CountryAgreementPriceConvert.INSTANCE.auditDto2Vo(input);
        String result = countryAgreementPriceDraftManageService.batchApprove(reqVO);
        return StringUtils.equals(Constant.SUCCESS, result) ? DataResponse.success() : DataResponse.error(result);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchReject(CountryAgreementPriceAuditDTO input) {
        log.info("IscCountryAgreementPriceWriteApiServiceImpl.batchReject param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CountryAgreementPriceAuditVO reqVO = CountryAgreementPriceConvert.INSTANCE.auditDto2Vo(input);
        String result = countryAgreementPriceDraftManageService.batchReject(reqVO);
        return StringUtils.equals(Constant.SUCCESS, result) ? DataResponse.success() : DataResponse.error(result);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchRevoke(CountryAgreementPriceAuditDTO input) {
        log.info("IscCountryAgreementPriceWriteApiServiceImpl.batchRevoke param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        CountryAgreementPriceAuditVO reqVO = CountryAgreementPriceConvert.INSTANCE.auditDto2Vo(input);
        return DataResponse.success(countryAgreementPriceDraftManageService.batchRevoke(reqVO));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> updateAgreementAndJdPrice(CountryAgreementPriceReqDTO dto) {
        log.info("IscCountryAgreementPriceWriteApiServiceImpl.updateAgreementAndJdPrice param:{}", JSONObject.toJSONString(dto));
        ApiInitUtils.init(dto);
        CountryAgreementPriceReqVO countryAgreementPriceReqVO = CountryAgreementPriceConvert.INSTANCE.reqDto2ReqVo(dto);
        String requestId = UUID.randomUUID().toString();
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.AGREEMENT_PRICE_SKU_LOCK, dto.getTargetCountryCode(), String.valueOf(dto.getSkuId()));
        try {
            AssertValidation.isTrue(!jimUtils.simpleLock(lockKey, requestId, 60), DataResponseCodeConstant.SPU_AUDIT_LOCK_FAILED, "加锁失败");
            return countryAgreementPriceManageService.updateAgreementPriceAndJdPrice(countryAgreementPriceReqVO);
        } catch (Exception e) {
            log.error("IscCountryAgreementPriceWriteApiServiceImpl.updateAgreementAndJdPrice error CountryAgreementPriceReqDTO={},err={}", JSON.toJSONString(dto), e.getMessage(), e);
            return DataResponse.error(e.getMessage());
        } finally {
            jimUtils.simpleLockRelease(lockKey, requestId);
        }
    }

    /**
     * 申请更新协议和京东价格。
     *
     * @param dto 包含国家、协议和价格信息的请求对象。
     * @return 更新结果。
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> applyForUpdateAgreementAndJdPrice(CountryAgreementPriceReqDTO dto) {
        log.info("IscCountryAgreementPriceWriteApiServiceImpl.applyForUpdateAgreementAndJdPrice param:{}", JSONObject.toJSONString(dto));
        ApiInitUtils.init(dto);
        CountryAgreementPriceReqVO countryAgreementPriceReqVO = CountryAgreementPriceConvert.INSTANCE.reqDto2ReqVo(dto);
        String requestId = UUID.randomUUID().toString();
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.AGREEMENT_PRICE_DRAFT_SKU_LOCK, dto.getTargetCountryCode(), String.valueOf(dto.getSkuId()));
        try {
            AssertValidation.isTrue(!jimUtils.simpleLock(lockKey, requestId, 60), DataResponseCodeConstant.SPU_AUDIT_LOCK_FAILED, "加锁失败");
            return countryAgreementPriceManageService.applyForUpdateAgreementAndJdPrice(countryAgreementPriceReqVO);
        } catch (Exception e) {
            log.error("IscCountryAgreementPriceWriteApiServiceImpl.applyForUpdateAgreementAndJdPrice error CountryAgreementPriceReqDTO={},err={}", JSON.toJSONString(dto), e.getMessage(), e);
            return DataResponse.error(e.getMessage());
        } finally {
            jimUtils.simpleLockRelease(lockKey, requestId);
        }
    }

    @Override
    public DataResponse<String> initCountryAgreementWarningPrice(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("initCountryAgreementWarningPrice.ids is empty");
            return DataResponse.error("参数错误");
        }
        for (Long id : ids) {
            long start = System.currentTimeMillis();
            try {
                countryAgreementPriceWarningManageService.saveOrUpdate(id, AgreementPriceDataSourceTypeEnums.COST);
                customerSkuPriceWarningManageService.saveOrUpdateFromCostPrice(id, CustomerSkuDataSourceTypeEnums.COST);
            } catch (Exception e) {
                log.error("DevOpsApiTwoServiceImpl.initCountryAgreementWarningPrice error,id={}", id, e);
            } finally {
                log.info("initCountryAgreementWarningPrice, id={}, cost={}", id, System.currentTimeMillis() - start);
            }
        }
        return DataResponse.success();
    }

    @Override
    public DataResponse<String> initCountryAgreementWarningPriceV2(Set<Long> ids, Integer dataStatusSource) {
        if (CollectionUtils.isEmpty(ids)) {
            return DataResponse.error("参数错误");
        }
        for (Long id : ids) {
            long start = System.currentTimeMillis();
            try {

                AgreementPriceDataSourceTypeEnums dataStatusSourceEnum = AgreementPriceDataSourceTypeEnums.COST;
                if (dataStatusSource != null) {
                    dataStatusSourceEnum = AgreementPriceDataSourceTypeEnums.countryEnumByCode(dataStatusSource);
                }

                countryAgreementPriceWarningManageService.saveOrUpdate(id, dataStatusSourceEnum);

                if (dataStatusSourceEnum == AgreementPriceDataSourceTypeEnums.AGREE) {
                    log.info("协议价变更不更新客制化预警. ids={}, dataStatusSource={}", ids, dataStatusSource);
                    continue;
                }

                CustomerSkuDataSourceTypeEnums customerSkuDataSourceTypeEnums = CustomerSkuDataSourceTypeEnums.COST;
                if (dataStatusSource != null) {
                    customerSkuDataSourceTypeEnums = CustomerSkuDataSourceTypeEnums.countryEnumByCode(dataStatusSource);
                }

                customerSkuPriceWarningManageService.saveOrUpdateFromCostPrice(id, customerSkuDataSourceTypeEnums);
            } catch (Exception e) {
                log.error("IscCountryAgreementPriceWriteApiServiceImpl.initCountryAgreementWarningPriceV2 error,id={}", id, e);
                AlertHelper.p1("更新国家协议价预警失败, id = ", String.valueOf(id));
            } finally {
                log.info("IscCountryAgreementPriceWriteApiServiceImpl.initCountryAgreementWarningPriceV2, id={}, cost={}", id, System.currentTimeMillis() - start);
            }
        }
        return DataResponse.success();
    }

    /**
     * 初始化国家成本价格。
     *
     * @param dto 价格刷新DTO对象，包含刷新价格的相关信息。
     * @return 初始化结果，true表示成功，false表示失败。
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> initCountryCostPrice(PriceRefreshDTO dto) {
        log.info("IscCountryAgreementPriceWriteApiServiceImpl.initCountryCostPrice param:{}", JSONObject.toJSONString(dto));
        List<SkuCountryVO> sourceCountryBySkuIdList = skuReadManageService.getSourceCountryBySkuId(new HashSet<>(dto.getSkuIdList()));
        if (CollectionUtils.isEmpty(sourceCountryBySkuIdList)) {
            return DataResponse.buildError("skuId未查询出来sku基本信息");
        }
        log.info("IscCountryAgreementPriceWriteApiServiceImpl.initCountryCostPrice list:{},tagCountry:{}", JSONObject.toJSONString(sourceCountryBySkuIdList), dto.getTargetCountryCode());
        for (SkuCountryVO skuCountryVO : sourceCountryBySkuIdList) {
            if (skuCountryVO.getSourceCountryCode().equals(CountryConstant.COUNTRY_ZH) || skuCountryVO.getSourceCountryCode().equals(dto.getTargetCountryCode())) {
                InitAgreementPriceVO initAgreementPriceVO = new InitAgreementPriceVO();
                initAgreementPriceVO.setTargetCountryCode(dto.getTargetCountryCode());
                initAgreementPriceVO.setSourceCountryCode(skuCountryVO.getSourceCountryCode());
                initAgreementPriceVO.setSkuId(skuCountryVO.getSkuId());
                handlerUpdateCostPrice(initAgreementPriceVO);
            }
        }
        return DataResponse.success(Boolean.TRUE);
    }


    private Boolean handlerUpdateCostPrice(InitAgreementPriceVO initAgreementPriceVO) {
        String requestId = UUID.randomUUID().toString();
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.AGREEMENT_PRICE_SKU_LOCK, initAgreementPriceVO.getTargetCountryCode(), String.valueOf(initAgreementPriceVO.getSkuId()));
        try {
            AssertValidation.isTrue(!jimUtils.simpleLock(lockKey, requestId, 60), SPU_AUDIT_LOCK_FAILED, "加锁失败");
            initAgreementPriceVO.setIsWareHouseProduct(FulfillmentRateTypeEnum.DIRECT_DELIVERY.getCode());
            CurrencyPriceVO currencyPriceVO = countryAgreementPriceManageService.calculateCountryCostPrice(initAgreementPriceVO);
            Boolean agreementResult = countryAgreementPriceManageService.initAgreementPrice(initAgreementPriceVO, currencyPriceVO);
            initAgreementPriceVO.setIsWareHouseProduct(FulfillmentRateTypeEnum.WAREHOUSE_PRODUCT.getCode());
            Boolean warehousingPriceResult = countryExtendPriceManageService.addWarehousingPrice(initAgreementPriceVO);
            return agreementResult;
        } catch (Exception e) {
            log.error("IscCountryAgreementPriceWriteApiServiceImpl.handlerUpdateCostPrice error initAgreementPriceVO={},err={}", JSON.toJSONString(initAgreementPriceVO), e.getMessage(), e);
            return false;
        } finally {
            jimUtils.simpleLockRelease(lockKey, requestId);
        }
    }

    /**
     * 批量同步建议到协议价格。
     * @param dto 同步请求数据对象。
     * @return 同步结果数据对象。
     */
    @Override
    public DataResponse<CountryAgreementPriceSyncResDTO> batchSyncSuggestToAgreementPrice(CountryAgreementPriceSyncReqDTO dto) {
        CountryAgreementPriceSyncResDTO countryAgreementPriceSyncResDTO = new CountryAgreementPriceSyncResDTO();
        Map<Long,String> failSkuIdMap = new HashMap<>();
        List<Long> successIdList = new ArrayList<>();
        List<CountryAgreementPriceVO> countryAgreementPriceVOList = countryAgreementPriceManageService.listByIds(dto.getIdList());
        Map<Long, CountryAgreementPriceVO> idAgreementPriceMap = countryAgreementPriceVOList.stream().collect(Collectors.toMap(CountryAgreementPriceVO::getId, Function.identity(), (a, b) -> a));
        for (Long id : dto.getIdList()){
            if(!idAgreementPriceMap.containsKey(id)){
                failSkuIdMap.put(id,"id不存在");
                continue;
            }
            CountryAgreementPriceVO countryAgreementPriceVO = idAgreementPriceMap.get(id);
            if(Objects.isNull(countryAgreementPriceVO.getSuggestAgreementPrice())){
                failSkuIdMap.put(countryAgreementPriceVO.getSkuId(),"建议协议价为空");
                continue;
            }
            CountryAgreementPriceReqDTO reqDTO = new CountryAgreementPriceReqDTO();
            reqDTO.setSkuId(countryAgreementPriceVO.getSkuId());
            reqDTO.setSourceCountryCode(countryAgreementPriceVO.getSourceCountryCode());
            reqDTO.setTargetCountryCode(countryAgreementPriceVO.getTargetCountryCode());
            reqDTO.setAgreementPrice(countryAgreementPriceVO.getSuggestAgreementPrice());
            String totalMsg = String.format("同步时间:%s;\n 国家建议协议价同步到国家协议价%s", DateUtil.formatDate(new Date()), countryAgreementPriceVO.getSuggestAgreementMark());
            reqDTO.setAgreementMark(totalMsg);
            reqDTO.setPin(dto.getPin());
            reqDTO.setSystemCode(dto.getSystemCode());
            reqDTO.setLang(dto.getLang());
            DataResponse<Boolean> response = updateDraft(reqDTO);
            if(response.getSuccess()){
                successIdList.add(id);
            }else{
                failSkuIdMap.put(countryAgreementPriceVO.getSkuId(),response.getMessage());
            }
        }
        countryAgreementPriceSyncResDTO.setSuccessIdList(successIdList);
        countryAgreementPriceSyncResDTO.setFailSkuIdMap(failSkuIdMap);
        return DataResponse.success(countryAgreementPriceSyncResDTO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PriceAvailableSaleStatusResDTO> updateAvailableSaleStatus(List<PriceAvailableSaleStatusReqDTO> input) {
        return countryAgreementPriceManageService.updateAvailableSaleStatus(input);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> refreshCountryAgreementWarningPrice(List<MkuPoolFlagDTO> input) {
        if (CollectionUtils.isEmpty(input)) {
            return DataResponse.success();
        }

        List<Long> mkuIds = input.stream().map(MkuPoolFlagDTO::getMkuId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(mkuIds)) {
            return DataResponse.success();
        }

        List<MkuRelationPO> mkuRelations = mkuRelationAtomicService.queryBindListByMkuIds(mkuIds);

        if (CollectionUtils.isEmpty(mkuRelations)) {
            log.warn("mkuRelations is null,mkuIds={}", mkuIds);
            return DataResponse.success();
        }

        // mkuId -> skuId
        Map<Long, Set<Long>> mkuSkuMap = mkuRelations.stream().filter(item -> item.getSkuId() != null && item.getMkuId() != null)
                .collect(Collectors.groupingBy(MkuRelationPO::getMkuId, Collectors.mapping(MkuRelationPO::getSkuId, Collectors.toSet())));

        // 国家 -> mkuId
        Map<String, Set<Long>> targetCountryCodeMkuMap = input.stream().filter(item -> item.getMkuId() != null && item.getTargetCountryCode() != null)
                .collect(Collectors.groupingBy(MkuPoolFlagDTO::getTargetCountryCode, Collectors.mapping(MkuPoolFlagDTO::getMkuId, Collectors.toSet())));

        if (MapUtils.isEmpty(targetCountryCodeMkuMap) || MapUtils.isEmpty(mkuSkuMap)) {
            log.warn("targetCountryCodeMkuMap={}, mkuSkuMap={}", targetCountryCodeMkuMap, mkuSkuMap);
            return DataResponse.success();
        }

        targetCountryCodeMkuMap.forEach((targetCountryCode, mIds) -> {

            Set<Long> allSkuIds = Sets.newHashSet();

            for (Long mId : mIds) {
                Set<Long> skuIds = mkuSkuMap.get(mId);
                if (CollectionUtils.isNotEmpty(skuIds)) {
                    allSkuIds.addAll(skuIds);
                } else {
                    log.info("找不到skuId, mkuId={}, targetCountryCode={}", mId, targetCountryCode);
                }
            }

            if (CollectionUtils.isEmpty(allSkuIds)) {
                return;
            }

            List<Long> ids = CollectionUtil.sort(allSkuIds, Comparator.comparing(Long::longValue));

            List<CountryAgreementPricePO> list = countryAgreementPriceAtomicService.getCountryAgreementPriceList(ids, targetCountryCode);

            if (CollectionUtils.isEmpty(list)) {
                log.warn("CountryAgreementPricePO list is empty, ids={}, targetCountryCode={}", ids, targetCountryCode);
                return;
            }

            Set<Long> agreementIds = list.stream().map(CountryAgreementPricePO::getId).collect(Collectors.toSet());

            // 更新预警信息
            this.initCountryAgreementWarningPrice(agreementIds);

            // 更新预警信息
            this.initPricePoolStatus(agreementIds);
        });

        return DataResponse.success();
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> initPricePoolStatus(Set<Long> agreementIds) {
        if (CollectionUtils.isEmpty(agreementIds)) {
            log.warn("initPricePoolStatus, agreementIds is empty");
            return DataResponse.success();
        }
        long l = System.currentTimeMillis();
        try {
            // 更新协议价表的客户池和国家池状态
            countryAgreementPriceManageService.updatePoolStatus(agreementIds);

            // 更新国家池状态
            List<Long> customerDetailIds = customerSkuSalePriceDetailManageService.updatePoolStatus(agreementIds);

            // 更新客制价预警信息
            customerPriceWarningManager.syncAvailableSaleStatusList(customerDetailIds);

            // 更新协议价预警信息
            countryAgreementPriceWarningManager.syncAvailableSaleStatusList(agreementIds);
        } catch (Exception e) {
            log.error("初始化价格是否入池出现异常，initPricePoolStatus, agreementIds={}, message={}", agreementIds, ExceptionUtil.getMessage(e, "初始化价格是否入池失败"), e);
            return DataResponse.error(e.getMessage());
        } finally {
            log.error("初始化价格是否入池出现完成，initPricePoolStatus, agreementIds={}, cost={}", agreementIds,  System.currentTimeMillis() - l);
        }

        return DataResponse.success();
    }

    @Override
    public DataResponse<List<Long>> getAgreementPriceIds(Long minId) {
        return DataResponse.success(countryAgreementPriceAtomicService.getAllIds(minId, 500));
    }

    @Override
    public void afterPropertiesSet() throws Exception {

//        Set<Long> allIds = countryAgreementPriceAtomicService.getAllIds();

//        this.initPricePoolStatus(allIds);



//        refreshCountryAgreementWarningPrice();

//        List<MkuPoolFlagDTO> input = Lists.newArrayList();
//
//        input.add(new MkuPoolFlagDTO(50000000023L, "BR"));
//        input.add(new MkuPoolFlagDTO(50000000018L, "BR"));
//        input.add(new MkuPoolFlagDTO(50000000037L, "BR"));
//        input.add(new MkuPoolFlagDTO(50000000040L, "BR"));
//        input.add(new MkuPoolFlagDTO(50000000041L, "BR"));
//        input.add(new MkuPoolFlagDTO(50000000036L, "BR"));
//
//        this.refreshCountryAgreementWarningPrice(input);

//        this.initCountryAgreementWarningPrice(Sets.newHashSet(10100L));

//        String json = "{\"lang\":\"zh\",\"pin\":\"wangpeng965\",\"skuIdList\":[80000000113],\"systemCode\":\"WIMP\",\"targetCountryCode\":\"TH\"}";
//
//        PriceRefreshDTO priceRefreshDTO = JSONObject.parseObject(json, PriceRefreshDTO.class);
//
//        DataResponse<Boolean> booleanDataResponse = this.initCountryCostPrice(priceRefreshDTO);
//
//        System.out.println("booleanDataResponse:" + booleanDataResponse);
//
//        String json = "{\"agreementPrice\":201,\"bizNo\":\"TH80000000150\",\"lang\":\"zh\",\"mkuId\":50000000130,\"pin\":\"liuzhaoming.10\",\"skuId\":80000000150,\"sourceCountryCode\":\"TH\",\"systemCode\":\"WIMP\",\"targetCountryCode\":\"TH\"}";
//
//        CountryAgreementPriceReqDTO update = JSONObject.parseObject(json, CountryAgreementPriceReqDTO.class);
//
//        String url1 = "https://wiop-api.jdindustry.com/img/ks1/image%20%287%2934909803174299380.png?x-oss-process=img/sb/800/800/fmt/png";
//        String url3 = "https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/ks1/image%20%287%2934909803174299380.png";
//
//        update.setAdjustmentPriceReason("测试调整原因");
//        update.setAttachmentUrls(Lists.newArrayList(url1, url3));
//
//        DataResponse<Boolean> response = this.updateDraft(update);
//
//        log.info("updateDraft response={}", JSONObject.toJSONString(response));

    }

    public void refreshCountryAgreementWarningPrice() {
        long start = System.currentTimeMillis();
        Set<Long> allIds = countryAgreementPriceAtomicService.getAllIds();
        if (CollectionUtils.isEmpty(allIds)) {
            return;
        }
        log.info("初始化预警表. allIds.size={}", allIds.size());

        for (List<Long> ids : Lists.partition(Lists.newArrayList(allIds), 100)) {
            try {
                this.initCountryAgreementWarningPrice(Sets.newHashSet(ids));
            } catch (Exception e) {
                log.error("initCountryAgreementWarningPrice, error", e);
                throw e;
            }
        }

        log.info("refreshCountryAgreementWarningPrice, cost: {}", System.currentTimeMillis() - start);
    }
}
