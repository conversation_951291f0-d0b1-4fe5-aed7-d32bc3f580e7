package com.jdi.isc.product.soa.service.mapstruct.approveorder;

import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderDTO;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveOrderPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 商品审核对象转换
 *
 * <AUTHOR>
 */
@Mapper
public interface ApproveOrderConvert {

    /**
     * The constant INSTANCE.
     */
    ApproveOrderConvert INSTANCE = Mappers.getMapper(ApproveOrderConvert.class);

    /**
     * Convert approve order po.
     *
     * @param order the order
     * @return the approve order po
     */
    ApproveOrderPO convert(ApproveOrderDTO order);
}