package com.jdi.isc.product.soa.service.mapstruct.sku;

import com.jdi.isc.product.soa.api.jdm.sku.base.JdmSaveSpuDTO;
import com.jdi.isc.product.soa.api.jdm.sku.req.JdmSearchSkuReqDTO;
import com.jdi.isc.product.soa.api.jdm.sku.req.JdmUpdateSkuReqDTO;
import com.jdi.isc.product.soa.api.jdm.sku.res.JdmSearchSkuResDTO;
import com.jdi.isc.product.soa.api.sku.req.*;
import com.jdi.isc.product.soa.api.sku.res.*;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.domain.category.biz.GlobalQualificationVO;
import com.jdi.isc.product.soa.domain.enums.product.ReturnTypeEnum;
import com.jdi.isc.product.soa.domain.msg.SkuMsgVO;
import com.jdi.isc.product.soa.domain.sku.biz.*;
import com.jdi.isc.product.soa.domain.sku.po.SkuCertificatePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuFeaturePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuUniqueShardingPO;
import com.jdi.isc.product.soa.domain.spu.biz.SaveSpuVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuDetailVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.*;

/**
 * sku对象转换
 *
 * <AUTHOR>
 * @date 2023/11/26
 **/
@Mapper
public interface SkuConvert {

    SkuConvert INSTANCE = Mappers.getMapper(SkuConvert.class);

    List<SkuCountryVO> listPo2SkuCountry(List<SkuPO> skuPOList);

    @Mapping(source = "jdCatId",target = "catId")
    SkuDetailOrderReadVO skuPO2orderSkuDetail(SkuPO skuPO);

    @InheritConfiguration
    @Mapping(target = "detailImg", expression = "java(detailImage2Str(skuVO.getDetailImgList()))")
    @Mapping(source = "catId",target = "jdCatId")
    SkuPO vo2po(SkuVO skuVO);

    default String detailImage2Str(List<String> detailImageList) {
        if (CollectionUtils.isEmpty(detailImageList)) {
            return "";
        }
        return String.join(Constant.HASHTAG, detailImageList);
    }

    @InheritConfiguration
    @Mapping(target = "detailImgList", expression = "java(detailImage2ImageList(skuPO.getDetailImg()))")
    @Mapping(source = "jdCatId",target = "catId")
    SkuVO po2vo(SkuPO skuPO);

    default List<String> detailImage2ImageList(String detailImage) {
        if (StringUtils.isBlank(detailImage)) {
            return null;
        }

        String[] imageArray = detailImage.split(Constant.HASHTAG);
        return Arrays.asList(imageArray);
    }

    List<SkuPO> listVO2PO(List<SkuVO> skuVoList);

    List<SkuVO> listPo2Vo(List<SkuPO> skuPOList);

    @Mapping(source = "mainImg", target = "skuImage")
    SkuDetailVO skuVo2detailVo(SkuVO skuVO);

    @Mapping(target = "createTime",ignore = true)
    @Mapping(target = "updateTime",ignore = true)
    SkuUniqueShardingPO skuVo2UniquePo(SkuVO skuVO);

    List<SkuUniqueShardingPO> listSkuVo2UniquePo(List<SkuVO> skuVOList);

    @Mapping(target = "createTime",ignore = true)
    @Mapping(target = "updateTime",ignore = true)
    SkuFeatureVO skuVo2Feature(SkuVO skuVO);

    @Mapping(target = "createTime",ignore = true)
    @Mapping(target = "updateTime",ignore = true)
    SkuFeaturePO skuVo2FeaturePo(SkuVO skuVO);

    List<SkuFeaturePO> listSkuVo2FeaturePo(List<SkuVO> skuVOList);

    @Mapping(target = "createTime",ignore = true)
    @Mapping(target = "updateTime",ignore = true)
    SkuFeaturePO skuPo2Feature(SkuPO skuPO);

    SkuPageReqVO jdm2PageReqVo(JdmSearchSkuReqDTO reqDTO);

    List<JdmSearchSkuResDTO> listResVo2JdmResDTO(List<SkuResVO> skuResVOList);

    default SkuVO fillSkuFeature(SkuVO skuVO, SkuFeaturePO skuFeature) {
        if (Objects.isNull(skuFeature)) {
            return skuVO;
        }
        skuVO.setMagnetic(skuFeature.getMagnetic());
        skuVO.setElectric(skuFeature.getElectric());
        skuVO.setLiquid(skuFeature.getLiquid());
        skuVO.setPowder(skuFeature.getPowder());
        skuVO.setProductionCycle(skuFeature.getProductionCycle());
        skuVO.setOriginCountry(skuFeature.getOriginCountry());
        skuVO.setReturnType(skuFeature.getReturnType());
        skuVO.setReturnTypeValue(ReturnTypeEnum.descFromCode(skuFeature.getReturnType()));
        skuVO.setAdapterPlus(skuFeature.getAdapterPlus());
        skuVO.setVoltage(skuFeature.getVoltage());
        skuVO.setPurchaseModel(skuFeature.getPurchaseModel());
        skuVO.setEnterprise(skuFeature.getEnterprise());
        skuVO.setContactsName(skuFeature.getContactsName());
        skuVO.setContactsPhone(skuFeature.getContactsPhone());
        skuVO.setOrgCode(skuFeature.getOrgCode());
        skuVO.setSpecification(skuFeature.getSpecification());
        return skuVO;
    }

    @Mapping(source = "spuVO", target = "jdmSpuDTO")
    JdmSaveSpuDTO saveDetailVo2Jdm(SpuDetailVO spuDetailVO);

    @Mapping(source = "jdmSpuDTO", target = "spuVO")
    SaveSpuVO jdmUpdate2SaveSpuVo(JdmUpdateSkuReqDTO reqDTO);

    SkuCertificatePO skuCertVo2Po(SkuCertificateVO input);

    @Mapping(source = "id", target = "certificateId")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "updater", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    SkuCertificateVO globalCertVo2SkuCertVo(GlobalQualificationVO input);

    @InheritConfiguration
    SkuCertificateVO certificatePo2Vo(SkuCertificatePO input);

    @InheritConfiguration
    SkuCertificatePO certificateVo2Po(SkuCertificateVO input);

    @InheritConfiguration
    SkuCalculatePriceReqVO priceReqDto2Vo(SkuCalculatePriceReqDTO input);

    @InheritConfiguration
    SkuCalculateTaxPriceDTO priceResVo2Dto(SkuCalculateTaxPriceVO input);
    @InheritConfiguration
    @Mapping(source = "catId",target = "fourthCatId")
    SkuBaseInfoApiDTO po2BaseInfoApiDTO(SkuPO skuPO);
    @InheritConfiguration
    SkuFeatureApiDTO po2SkuFeatureApiDTO(SkuFeaturePO skuFeaturePO);


    @Mapping(source = "createTime", target = "createTime")
    @Mapping(source = "updateTime", target = "updateTime")
    List<SkuVO> apiDtoList2SkuVO(List<SkuUpdateApiDTO> skuUpdateApiDTOList);

    default Long map(Date value) {
        return value != null ? value.getTime() : null;
    }

    default Date map(Long value) {
        return value != null ? new Date(value) : null;
    }

    SkuGlobalAttributeVO skuGlobalAttributeDto2Vo(SkuGlobalAttributeApiDTO input);

    List<SkuGlobalAttributeDetailApiDTO> skuListGlobalAttributeVo2Dto(List<SkuGlobalAttributeDetailVO> inputs);

    QuerySkuAvailableSaleReqVO saleReqDTO2VO(QuerySkuAvailableSaleReqDTO reqDTO);

    SkuAvailableSaleResDTO saleResVo2DTO(SkuAvailableSaleResultVO resultVO);

    Map<Long, SkuAvailableSaleResDTO> resultVoMap2ResDTOMap(Map<Long, SkuAvailableSaleResultVO> map);

    List<SkuAvailableSaleResDTO> listResultVo2ResDTO(List<SkuAvailableSaleResultVO> resultVOList);

    SkuMsgVO skuPo2MsgVo(SkuPO skuPO);

    @InheritConfiguration
    List<SkuBaseInfoApiDTO> listPo2BaseInfoApiDTO(List<SkuPO> skuPO);

    ProductIdDTO productVO2DTO(ProductIdVO productIdVO);

}
