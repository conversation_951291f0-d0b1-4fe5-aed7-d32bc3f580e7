package com.jdi.isc.product.soa.service.manage.price.agreementPrice.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.fastjson.JSON;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.api.common.CurrencyConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.AgreementPriceDataSourceTypeEnums;
import com.jdi.isc.product.soa.api.common.enums.AgreementPriceTypeEnums;
import com.jdi.isc.product.soa.api.common.enums.PriceAvailableSaleStatusEnum;
import com.jdi.isc.product.soa.api.price.req.PriceAvailableSaleStatusReqDTO;
import com.jdi.isc.product.soa.api.price.req.PriceUnsellableThresholdImportReqDTO;
import com.jdi.isc.product.soa.api.price.res.PriceAvailableSaleStatusResDTO;
import com.jdi.isc.product.soa.api.price.res.PriceUnsellableThresholdImportResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.CountryExtendPriceEnum;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.util.BigDecimalUtil;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveOrderPO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryIdVO;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailPO;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.price.AgreementPriceSwitchEnums;
import com.jdi.isc.product.soa.domain.mku.po.MkuLangPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.*;
import com.jdi.isc.product.soa.domain.price.agreementPrice.draft.CountryAgreementPriceDraftVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPriceWarningPO;
import com.jdi.isc.product.soa.domain.price.extendPrice.biz.CurrencyPriceVO;
import com.jdi.isc.product.soa.domain.price.extendPrice.po.CountryExtendPricePO;
import com.jdi.isc.product.soa.domain.sku.biz.ProductIdVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuLogPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.rpc.suport.AlertHelper;
import com.jdi.isc.product.soa.rpc.utils.ExceptionUtil;
import com.jdi.isc.product.soa.service.adapter.mapstruct.agreementPrice.CountryAgreementPriceConvert;
import com.jdi.isc.product.soa.service.atomic.approveorder.ApproveFormAtomicService;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.PriceLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceWarningAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.extendPrice.CountryExtendPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuLogAtomicService;
import com.jdi.isc.product.soa.service.manage.approveorder.template.create.AbstractCreateApproveOrderExe;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuSalePriceDetailManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuRelationManageService;
import com.jdi.isc.product.soa.service.manage.price.ExchangeRateManageService;
import com.jdi.isc.product.soa.service.manage.price.ProfitCalculateManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceDraftManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceWarningManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.agreement.CalculatingAgreementPriceService;
import com.jdi.isc.product.soa.service.manage.price.extendPrice.CountryExtendPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.warning.aggreementprice.manager.CountryAgreementPriceWarningManager;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.support.ProductIdGenerator;
import com.jdi.isc.product.soa.service.support.transactional.ProductTransactionExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 国家协议价数据维护服务实现
 * @Author: wangpeng965
 * @Date: 2025/03/03 16:45
 **/

@Slf4j
@Service
public class CountryAgreementPriceManageServiceImpl extends BaseManageSupportService<CountryAgreementPriceVO, CountryAgreementPricePO> implements
    CountryAgreementPriceManageService {

    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;

    @Resource
    private CountryAgreementPriceWarningAtomicService countryAgreementPriceWarningAtomicService;

    @Resource
    private MkuRelationManageService mkuRelationManageService;

    @Resource
    private CategoryOutService categoryOutService;

    @Resource
    private MkuLangAtomicService mkuLangAtomicService;

    @Resource
    private SkuLogAtomicService skuLogAtomicService;

    @Resource
    private ProductIdGenerator productIdGenerator;

    @Resource
    private Map<String, CalculatingAgreementPriceService> calculatingPriceServiceMap;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @Resource
    @Lazy
    private CountryAgreementPriceDraftManageService countryAgreementPriceDraftManageService;

    @Resource
    private CountryExtendPriceAtomicService countryExtendPriceAtomicService;

    @Resource
    private ExchangeRateManageService exchangeRateManageService;

    @Resource
    private CountryManageService countryManageService;

    @Resource
    @Lazy
    private CountryAgreementPriceWarningManageService countryAgreementPriceWarningManageService;

    @Resource
    private PriceLogAtomicService priceLogAtomicService;

    @Resource
    private CountryExtendPriceManageService countryExtendPriceManageService;

    @Resource
    private ProfitCalculateManageService profitCalculateManageService;

    @Resource
    private OperDuccConfig operDuccConfig;

    @Resource
    private SkuReadManageService skuReadManageService;

    @Resource
    private ProductTransactionExecutor productTransactionExecutor;

    @Resource
    private ApproveFormAtomicService approveFormAtomicService;

    @Resource
    private CountryAgreementPriceWarningManager countryAgreementPriceWarningManager;

    @Resource
    private CountryMkuAtomicService countryMkuAtomicService;

    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;

    @Override
    public PageInfo<CountryAgreementPricePageVO> pageSearch(CountryAgreementPricePageReqVO vo) {
        PageInfo<CountryAgreementPricePageVO> pageInfo = new PageInfo<>();
        pageInfo.setSize(vo.getSize());
        pageInfo.setIndex(vo.getIndex());
        this.buildQuery(vo);
        long total = countryAgreementPriceAtomicService.pageSearchTotal(vo);
        if (total == 0) {
            pageInfo.setTotal(0);
            return pageInfo;
        }
        List<CountryAgreementPricePageVO> priceIdList = countryAgreementPriceAtomicService.pageSearch(vo);
        if (CollectionUtils.isEmpty(priceIdList)){
            return pageInfo;
        }
        List<Long> idList = priceIdList.stream().map(CountryAgreementPricePageVO::getId).collect(Collectors.toList());
        List<CountryAgreementPricePageVO> priceList = countryAgreementPriceAtomicService.pageSearchByIdList(idList);
        if (CollectionUtils.isEmpty(priceList)){
            return pageInfo;
        }
        this.setMkuTitle(priceList);
        this.setCatIdAndName(priceList);
        this.setSkuId(priceList);
        this.extendPrice(priceList);
        this.setProfitRateForPage(priceList);
        pageInfo.setTotal(total);
        pageInfo.setRecords(priceList);
        return pageInfo;
    }

    private void extendPrice(List<CountryAgreementPricePageVO> priceList){
        if(CollectionUtils.isEmpty(priceList)){
            return;
        }
        List<Long> skuIds = priceList.stream().map(CountryAgreementPricePageVO::getSkuId).collect(Collectors.toList());
        String targetCountryCode = priceList.get(0).getTargetCountryCode();
        Map<Long, Map<Integer, CountryExtendPricePO>> countryExtendPriceMap = countryExtendPriceManageService.getCountryExtendPriceMap(skuIds, targetCountryCode);
        log.info("CountryAgreementPriceManageServiceImpl.extendPrice priceList:{},countryExtendPricePOList:{}",JSONObject.toJSONString(priceList),JSONObject.toJSONString(countryExtendPriceMap));
        if (countryExtendPriceMap == null || countryExtendPriceMap.isEmpty()) {
            return;
        }
        for(CountryAgreementPricePageVO vo : priceList) {
            if(countryExtendPriceMap.containsKey(vo.getSkuId())){
                Map<Integer, CountryExtendPricePO> busTypeExtendPriceMap = countryExtendPriceMap.get(vo.getSkuId());
                if(busTypeExtendPriceMap.containsKey(CountryExtendPriceEnum.JD_PRICE.getCode())){
                    CountryExtendPricePO countryExtendPricePO = busTypeExtendPriceMap.get(CountryExtendPriceEnum.JD_PRICE.getCode());
                    vo.setJdPriceId(countryExtendPricePO.getId());
                    vo.setJdPrice(countryExtendPricePO.getPrice());
                    vo.setJdPriceMark(countryExtendPricePO.getPriceMark());
                }
                if(busTypeExtendPriceMap.containsKey(CountryExtendPriceEnum.WAREHOUSING_PRICE.getCode())){
                    CountryExtendPricePO countryExtendPricePO = busTypeExtendPriceMap.get(CountryExtendPriceEnum.WAREHOUSING_PRICE.getCode());
                    vo.setCountryWarehousingPrice(countryExtendPricePO.getPrice());
                    vo.setWarehousingPriceMark(countryExtendPricePO.getPriceMark());
                }
                if(busTypeExtendPriceMap.containsKey(CountryExtendPriceEnum.REFUND_TAX_PRICE.getCode())){
                    if(StringUtils.isBlank(vo.getCurrency())){
                        String currency = countryManageService.getCurrencyByCountryCode(vo.getTargetCountryCode());
                        vo.setCurrency(currency);
                    }
                    DataResponse<BigDecimal> exchangeRateResponse = exchangeRateManageService.getExchangeRateByCurrency(CurrencyConstant.CURRENCY_ZH, vo.getCurrency());
                    CountryExtendPricePO countryExtendPricePO = busTypeExtendPriceMap.get(CountryExtendPriceEnum.REFUND_TAX_PRICE.getCode());
                    BigDecimal exchangeRate = exchangeRateResponse.getData();
                    BigDecimal refundTaxPrice;
                    if(CountryConstant.COUNTRY_VN.equals(targetCountryCode) || CountryConstant.COUNTRY_ID.equals(targetCountryCode)){
                        refundTaxPrice = countryExtendPricePO.getPrice().multiply(exchangeRate).setScale(0, RoundingMode.UP);
                    }else {
                        refundTaxPrice = countryExtendPricePO.getPrice().multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
                    }
                    vo.setRefundTaxPrice(refundTaxPrice);
                    String refundTaxPriceMsg = String.format("预估跨境退税金额%s%s=预估跨境退税金额%s*汇率%s;\n", vo.getCurrency()
                        ,refundTaxPrice.stripTrailingZeros().toPlainString()
                        ,countryExtendPricePO.getPrice().stripTrailingZeros().toPlainString(),
                        exchangeRate.stripTrailingZeros().toPlainString());
                    String priceMark = refundTaxPriceMsg + countryExtendPricePO.getPriceMark();
                    vo.setRefundTaxPriceMark(priceMark);
                }
            }
        }
    }


    private void buildQuery(CountryAgreementPricePageReqVO vo){
        // 类目id转换为终极类目id
        Set<Long> lastCatIds = this.getLastCatIds(vo);
        vo.setCatIds(lastCatIds);
        if(CollectionUtils.isNotEmpty(vo.getProductIds())){
            ProductIdVO productIdVO = skuReadManageService.getProductInfoByIds(vo.getProductIds());
            if(CollectionUtils.isNotEmpty(productIdVO.getSkuIds())){
                vo.setSkuIds(productIdVO.getSkuIds());
            }
        }
    }


    /**
     * 根据 CountryAgreementPricePageReqVO 对象获取最后一级分类的 ID 集合。
     * @param countryAgreementPricePageReqVO 包含分类信息的请求对象。
     * @return 最后一级分类的 ID 集合。
     */
    private Set<Long> getLastCatIds(CountryAgreementPricePageReqVO countryAgreementPricePageReqVO){
        CategoryIdVO categoryIdVO = new CategoryIdVO();
        categoryIdVO.setFirstCatId(countryAgreementPricePageReqVO.getFirstCatId());
        categoryIdVO.setSecondCatId(countryAgreementPricePageReqVO.getSecondCatId());
        categoryIdVO.setThirdCatId(countryAgreementPricePageReqVO.getThirdCatId());
        categoryIdVO.setLastCatId(countryAgreementPricePageReqVO
            .getLastCatId());
        Set<Long> catIdSet = categoryOutService.queryLastCatId(categoryIdVO);
        return catIdSet;
    }

    /**
     * 设置MKU标题。
     * @param priceVOList 国际协议价格列表。
     */
    private void setMkuTitle(List<CountryAgreementPricePageVO> priceVOList){
        if(CollectionUtils.isEmpty(priceVOList)){
            return;
        }
        List<Long> mkuIds = priceVOList.stream().map(CountryAgreementPricePageVO::getMkuId).filter(Objects::nonNull).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(mkuIds)){
            return;
        }
        Map<Long, MkuLangPO> mkuLangMap = new HashMap<>();
        mkuLangMap = mkuLangAtomicService.getMkuLangNameByMkuIds(mkuIds, LangConstant.LANG_ZH);
        for (CountryAgreementPricePageVO vo : priceVOList){
            MkuLangPO mkuLangPOzh = mkuLangMap.get(vo.getMkuId());
            if(Objects.nonNull(mkuLangPOzh)){
                vo.setMkuTitle(mkuLangPOzh.getMkuTitle());
            }
        }
    }


    /**
     * 根据国家协议价格页列表设置对应的分类ID和名称。
     * @param pricePageVOList 国家协议价格页列表
     */
    private void setCatIdAndName(List<CountryAgreementPricePageVO> pricePageVOList){
        if(CollectionUtils.isEmpty(pricePageVOList)){
            return;
        }
        Set<Long> lastCatIds = pricePageVOList.stream().map(CountryAgreementPricePageVO::getLastCatId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> lastCatMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(lastCatIds)){
            Map<Long, String> tempMap = categoryOutService.queryPathStr(lastCatIds, LangContextHolder.get());
            if (MapUtils.isNotEmpty(tempMap)) {
                lastCatMap.putAll(tempMap);
            }
        }
        pricePageVOList.forEach(item->{
            item.setCatName(lastCatMap.get(item.getLastCatId()));
        });
    }


    /**
     * 根据国别协议价格页面VO列表设置对应的SKU ID列表。
     * @param priceVOList 国别协议价格页面VO列表。
     */
    private void setSkuId(List<CountryAgreementPricePageVO> priceVOList){
        if(CollectionUtils.isEmpty(priceVOList)){
            return;
        }
        for (CountryAgreementPricePageVO countryAgreementPricePageVO : priceVOList){
            List<Long> skuIds= new ArrayList<>();
            skuIds.add(countryAgreementPricePageVO.getSkuId());
            countryAgreementPricePageVO.setSkuIds(skuIds);
        }
    }

    /**
     * 设置每个页面的利润率。
     * @param priceVOList 包含国家协议价格和成本价格的列表。
     */
    private void setProfitRateForPage(List<CountryAgreementPricePageVO> priceVOList){
        if(CollectionUtils.isEmpty(priceVOList)){
            return;
        }
        for (CountryAgreementPricePageVO countryAgreementPricePageVO : priceVOList){
            setItemProfitRate(countryAgreementPricePageVO);
        }
    }

    /**
     * 设置国家协议价格页面的利润率。
     * @param countryAgreementPricePageVO 国家协议价格页面视图对象，包含国家成本价格和协议价格。
     */
    private void setItemProfitRate(CountryAgreementPricePageVO countryAgreementPricePageVO) {
        if(countryAgreementPricePageVO.getCountryCostPrice() == null || countryAgreementPricePageVO.getCountryCostPrice().compareTo(BigDecimal.ZERO) == 0
            || countryAgreementPricePageVO.getAgreementPrice() == null || countryAgreementPricePageVO.getAgreementPrice().compareTo(BigDecimal.ZERO) == 0 ){
            return;
        }
        countryAgreementPricePageVO.setProfitRate(profitCalculateManageService
                .calculateAgreementProfitRate(countryAgreementPricePageVO.getAgreementPrice(), countryAgreementPricePageVO.getCountryCostPrice())
                .multiply(Constant.DECIMAL_HUNDRED));
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    @PFTracing
    public DataResponse<Boolean> saveOrUpdate(CountryAgreementPriceVO agreementPriceVO)  {
        try {
            log.info("CountryAgreementPriceManageServiceImpl.saveOrUpdate, input={}", JSONObject.toJSONString(agreementPriceVO));
            String bizNo = "";
            if (Objects.nonNull(agreementPriceVO.getId())) {
                bizNo = agreementPriceVO.getBizNo();
                agreementPriceVO.setBizNo(null);
            }else {
                setBizNo(agreementPriceVO);
                bizNo = agreementPriceVO.getBizNo();
            }
            CountryAgreementPricePO agreementPricePO = CountryAgreementPriceConvert.INSTANCE.vo2Po(agreementPriceVO);
            agreementPricePO.setUpdateTime(new Date().getTime());

            if (agreementPriceVO.getDataSourceTypeEnums() != null) {
                agreementPricePO.setCostDataStatusSource(agreementPriceVO.getDataSourceTypeEnums().getCode());
            }

            // 计算并且设置不可售阈值
            this.setUnsellableThreshold(agreementPriceVO, agreementPricePO);

            boolean isSaveSuccessful = countryAgreementPriceAtomicService.saveOrUpdate(agreementPricePO);
            if (!isSaveSuccessful) {
                log.warn("CountryAgreementPriceManageServiceImpl.saveOrUpdate fail. agreementPriceVO={}", JSONObject.toJSONString(agreementPriceVO));
                return DataResponse.error("国家协议价,保存失败。");
            }
            sendLog(bizNo, agreementPriceVO, agreementPricePO);
        }catch (Exception e){
            log.error("CountryAgreementPriceManageServiceImpl.saveOrUpdate error:{}", JSONObject.toJSONString(agreementPriceVO), e);
            return DataResponse.error("国家协议价,保存失败,请联系管理员。");
        }
        return DataResponse.success(true);
    }

    private void setUnsellableThreshold(CountryAgreementPriceVO agreementPriceVO, CountryAgreementPricePO agreementPricePO) {
        try {
            BigDecimal grossProfit = operDuccConfig.getGrossProfit(agreementPriceVO.getTargetCountryCode());
            if (agreementPricePO.getId() == null) {
                log.info("协议价创建，计算并更新不可售阈值， skuId={}, targetCountryCode={}", agreementPricePO.getSkuId(), agreementPricePO.getTargetCountryCode());

                BigDecimal auditProfitRate = agreementPriceVO.getProfitRate();
                if (auditProfitRate == null) {

                    if (agreementPriceVO.getAgreementPrice() != null && agreementPriceVO.getCountryCostPrice() != null) {
                        auditProfitRate = SpringUtil.getBean(ProfitCalculateManageService.class).calculateAgreementProfitRate(agreementPriceVO.getAgreementPrice(), agreementPriceVO.getCountryCostPrice());
                        log.info("协议价创建，计算并更新不可售阈值, skuId={}, targetCountryCode={}, auditProfitRate={}", agreementPricePO.getSkuId(), agreementPricePO.getTargetCountryCode(), auditProfitRate);
                    }
                }

                if (auditProfitRate == null) {
                    auditProfitRate = grossProfit;
                }

                // 如果没有配置过不可售阈值，则初始化不可售阈值
                if (auditProfitRate != null && agreementPriceVO.getTargetCountryCode() != null && agreementPriceVO.getUnsellableThreshold() == null) {

                    // 2. 超低利润率和审核时的利润率取小
                    BigDecimal minProfitRate = auditProfitRate.min(grossProfit);
                    log.info("协议价创建，计算并更新不可售阈值, skuId={}, targetCountryCode={}, auditProfitRate={}, grossProfit={}, minProfitRate={}", agreementPricePO.getSkuId(), agreementPricePO.getTargetCountryCode(), auditProfitRate, grossProfit, minProfitRate);
                    agreementPricePO.setUnsellableThreshold(minProfitRate);
                    agreementPricePO.setUnsellableThresholdTime(new Date().getTime());
                }
                log.info("协议价创建，计算并更新不可售阈值, skuId={}, targetCountryCode={}, unsellableThreshold={}", agreementPricePO.getSkuId(), agreementPricePO.getTargetCountryCode(), agreementPricePO.getUnsellableThreshold());
            } else {
                // 如果是修改，有利润率，没有不可售阈值，则将边际负毛写入不可售阈值
                CountryAgreementPricePO agreement = countryAgreementPriceAtomicService.getValidById(agreementPricePO.getId());

                if (agreement != null  && agreement.getUnsellableThreshold() == null && agreementPricePO.getAgreementPrice() != null && agreementPricePO.getCountryCostPrice() != null) {
                    log.info("协议价和成本价都不为空，无不可售阈值，取边际负毛，agreementPriceVO={}, grossProfit={}", JSONObject.toJSONString(agreementPricePO), grossProfit);
                    agreementPricePO.setUnsellableThreshold(grossProfit);
                    agreementPricePO.setUnsellableThresholdTime(new Date().getTime());
                }
            }
        } catch (Exception e) {
            log.error("协议价创建，计算并更新不可售阈值失败, skuId={}, targetCountryCode={}, unsellableThreshold={}", agreementPricePO.getSkuId(), agreementPricePO.getTargetCountryCode(), agreementPricePO.getUnsellableThreshold(), e);
            AlertHelper.p0("协议价创建，计算并更新不可售阈值失败");
        }
    }

    @PFTracing
    private void setBizNo(CountryAgreementPriceVO agreementPriceVO){
        try {
            if(Objects.isNull(agreementPriceVO.getBizNo())){
                agreementPriceVO.setBizNo(agreementPriceVO.getTargetCountryCode()+agreementPriceVO.getSkuId());
            }
        }catch (Exception e){
            log.error("CountryAgreementPriceManageServiceImpl.setBizNo error",e);
            agreementPriceVO.setBizNo("" + System.currentTimeMillis());
        }
    }



    /**
     * 根据ID获取国家协议价格详细信息
     * @param reqVO 请求对象，包含要查询的国家协议价格的ID
     * @return 国家协议价格详细信息对象，如果ID对应的记录不存在则返回null
     */
    @Override
    public CountryAgreementPriceVO detailById(CountryAgreementPriceReqVO reqVO) {
        if(Objects.nonNull(reqVO.getId())) {
            CountryAgreementPricePO po = countryAgreementPriceAtomicService.getById(reqVO.getId());
            if (Objects.isNull(po)) {
                log.info("detail, MarkupRatePO null. id={}", reqVO.getId());
                return null;
            }
            CountryAgreementPriceVO vo = CountryAgreementPriceConvert.INSTANCE.po2Vo(po);
            this.setExtendPrice(vo);
            if(Objects.nonNull(vo.getAgreementPrice()) && Objects.nonNull(vo.getCountryCostPrice())) {
                vo.setProfitRate(profitCalculateManageService.calculateAgreementProfitRate(vo.getAgreementPrice(), vo.getCountryCostPrice()).multiply(Constant.DECIMAL_HUNDRED));
            }
            return vo;
        }
        return detailBySkuId(reqVO);
    }

    @Override
    public CountryAgreementPriceVO detailBySkuId(CountryAgreementPriceReqVO reqVO) {
        CountryAgreementPriceDraftVO draftCountryAgreementPriceVO = countryAgreementPriceDraftManageService.detail(reqVO);
        if (Objects.nonNull(draftCountryAgreementPriceVO) && draftCountryAgreementPriceVO.getAuditStatus().equals(AuditStatusEnum.WAITING_APPROVED.getCode())) {
            log.info("数据在审批中，无法再次申请");
            throw new BizException("数据在审批中，无法再次申请");
        }
        CountryAgreementPricePO po = countryAgreementPriceAtomicService.getCountryAgreementPrice(reqVO);
        CountryAgreementPriceVO vo = CountryAgreementPriceConvert.INSTANCE.po2Vo(po);
        return vo;
    }

    /**
     * 设置扩展价格信息。
     * @param vo CountryAgreementPriceVO 对象，包含 SKU ID 和目标国家代码。
     */
    private void setExtendPrice(CountryAgreementPriceVO vo){
        Long skuId = vo.getSkuId();
        Map<Long, Map<Integer, CountryExtendPricePO>> countryExtendPriceMap = countryExtendPriceManageService.getCountryExtendPriceMap(Lists.newArrayList(skuId), vo.getTargetCountryCode());
        log.info("CountryAgreementPriceManageServiceImpl.setExtendPrice vo:{},countryExtendPriceMap:{}",JSONObject.toJSONString(vo),JSONObject.toJSONString(countryExtendPriceMap));
        if(countryExtendPriceMap.containsKey(skuId)){
            Map<Integer, CountryExtendPricePO> busTypeExtendPriceMap = countryExtendPriceMap.get(skuId);
            if(busTypeExtendPriceMap.containsKey(CountryExtendPriceEnum.JD_PRICE.getCode())){
                CountryExtendPricePO countryExtendPricePO = busTypeExtendPriceMap.get(CountryExtendPriceEnum.JD_PRICE.getCode());
                vo.setJdPrice(countryExtendPricePO.getPrice());
                vo.setJdPriceMark(countryExtendPricePO.getPriceMark());
            }
            if(busTypeExtendPriceMap.containsKey(CountryExtendPriceEnum.WAREHOUSING_PRICE.getCode())){
                CountryExtendPricePO countryExtendPricePO = busTypeExtendPriceMap.get(CountryExtendPriceEnum.WAREHOUSING_PRICE.getCode());
                vo.setCountryWarehousingPrice(countryExtendPricePO.getPrice());
                vo.setWarehousingPriceMark(countryExtendPricePO.getPriceMark());
            }
            if(busTypeExtendPriceMap.containsKey(CountryExtendPriceEnum.REFUND_TAX_PRICE.getCode())){
                if(StringUtils.isBlank(vo.getCurrency())){
                    String currency = countryManageService.getCurrencyByCountryCode(vo.getTargetCountryCode());
                    vo.setCurrency(currency);
                }
                DataResponse<BigDecimal> exchangeRateResponse = exchangeRateManageService.getExchangeRateByCurrency(CurrencyConstant.CURRENCY_ZH, vo.getCurrency());
                CountryExtendPricePO countryExtendPricePO = busTypeExtendPriceMap.get(CountryExtendPriceEnum.REFUND_TAX_PRICE.getCode());
                BigDecimal exchangeRate = exchangeRateResponse.getData();
                BigDecimal refundTaxPrice = countryExtendPricePO.getPrice().multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
                vo.setRefundTaxPrice(refundTaxPrice);
                String refundTaxPriceMsg = String.format("预估跨境退税金额%s%s=预估跨境退税金额%s*汇率%s;\n", vo.getCurrency()
                    ,refundTaxPrice.stripTrailingZeros().toPlainString()
                    ,countryExtendPricePO.getPrice().stripTrailingZeros().toPlainString(),
                    exchangeRate.stripTrailingZeros().toPlainString());
                String priceMark = refundTaxPriceMsg + countryExtendPricePO.getPriceMark();
                vo.setRefundTaxPriceMark(priceMark);
            }
        }
    }
    @Override
    @PFTracing
    public CountryAgreementPriceVO getCountryAgreementPrice(CountryAgreementPriceReqVO reqVO) {
        // 如果vo为null，直接返回null
        if (reqVO == null) {
            return null;
        }
        CountryAgreementPricePO countryAgreementPricePO = countryAgreementPriceAtomicService.getCountryAgreementPrice(reqVO);
        if(Objects.nonNull(countryAgreementPricePO)) {
            CountryAgreementPriceVO countryAgreementPriceVO = CountryAgreementPriceConvert.INSTANCE.po2Vo(countryAgreementPricePO);
            return countryAgreementPriceVO;
        }
        return null;
    }

    /**
     * 发送日志到数据库
     * @param bizNo 业务编号
     * @param sourceData 源数据对象
     * @param targetData 目标数据对象
     */
    @PFTracing
    private void sendLog(String bizNo, CountryAgreementPriceVO sourceData, CountryAgreementPricePO targetData) {
        try {
            SkuLogPO skuLogPO = new SkuLogPO();
            skuLogPO.setSourceJson(JSONObject.toJSONString(sourceData));
            skuLogPO.setTargetJson(JSONObject.toJSONString(targetData));
            skuLogPO.setKeyType(KeyTypeEnum.AGREEMENT_PRICE.getCode());
            skuLogPO.setKeyId(bizNo);
            skuLogPO.setCreator(targetData.getUpdater());
            skuLogPO.setUpdater(targetData.getUpdater());
            skuLogAtomicService.save(skuLogPO);
            log.info("日志保存成功，bizNo: {}", bizNo);
        } catch (Exception e) {
            log.error("存储日志异常，bizNo: {},sourceData:{},targetData:{} ,Error: {}", bizNo, JSONObject.toJSONString(sourceData), JSONObject.toJSONString(targetData), e.getMessage(), e);
        }
    }

    protected CalculatingAgreementPriceService calculatingAgreementPriceService(String targetCountryCode) {
        return calculatingPriceServiceMap.get(Objects.requireNonNull(AgreementPriceTypeEnums.getEnumByCountryCode(targetCountryCode)).getServiceName());
    }

    /**
     * 根据目标国家代码和 InitAgreementPriceVO 对象，计算并初始化该国家的成本价格。
     * @param vo InitAgreementPriceVO 对象，包含用于计算成本价格的必要信息。
     * @return CurrencyPriceVO 对象，表示目标国家的成本价格。
     */
    @Override
    public CurrencyPriceVO calculateCountryCostPrice(InitAgreementPriceVO vo) {
        CurrencyPriceVO countryCostPrice = this.calculatingAgreementPriceService(vo.getTargetCountryCode()).calculateCountryCostPrice(vo);
        return countryCostPrice;
    }

    /**
     * 计算履约成本。
     * @param vo InitAgreementPriceVO 对象，包含目标国家代码等信息。
     * @return 履约成本的 CurrencyPriceVO 对象。
     */
    @Override
    public CurrencyPriceVO calculateFulfillmentCost(InitAgreementPriceVO vo){
        CurrencyPriceVO fulfillmentCost = this.calculatingAgreementPriceService(vo.getTargetCountryCode()).calculateFulfillmentCost(vo);
        return fulfillmentCost;
    }

    /**
     * 初始化协议价格。
     * @param vo 包含目标国家代码的协议价格初始化信息。
     * @return 初始化后的协议价格对象。
     */
    @Override
    @PFTracing
    public Boolean initAgreementPrice(InitAgreementPriceVO vo,CurrencyPriceVO countryCostPrice) {
        log.info("CountryAgreementPriceManageServiceImpl.initAgreementPrice vo:{}",JSONObject.toJSONString(vo));
        Boolean validateResult = validateSkuInfo(vo);
        if(!validateResult){
            return validateResult;
        }
        CountryAgreementPriceReqVO reqVO = new CountryAgreementPriceReqVO();
        reqVO.setSourceCountryCode(vo.getSourceCountryCode());
        reqVO.setTargetCountryCode(vo.getTargetCountryCode());
        reqVO.setSkuId(vo.getSkuId());
        CountryAgreementPriceVO countryAgreementPrice = getCountryAgreementPrice(reqVO);
        CountryAgreementPriceVO countryAgreementPriceVO;
        if (Objects.nonNull(countryAgreementPrice)) {
            countryAgreementPriceVO = countryAgreementPrice;
        } else {
            countryAgreementPriceVO = new CountryAgreementPriceVO();
            countryAgreementPriceVO.setSkuId(vo.getSkuId());
            countryAgreementPriceVO.setSourceCountryCode(vo.getSourceCountryCode());
            countryAgreementPriceVO.setTargetCountryCode(vo.getTargetCountryCode());
        }
        if(Objects.isNull(countryCostPrice)) {
            countryCostPrice = calculateCountryCostPrice(vo);
        }
        if(Objects.nonNull(countryCostPrice.getPrice())) {
            countryAgreementPriceVO.setCountryCostPrice(countryCostPrice.getPrice().setScale(2, RoundingMode.HALF_UP));
            countryAgreementPriceVO.setCurrency(countryCostPrice.getCurrency());
            CurrencyPriceVO suggestAgreementPrice = calculateAgreementPrice(vo,countryCostPrice);
            log.info("CountryAgreementPriceManageServiceImpl.suggestAgreementPrice source:{},target:{}",countryAgreementPriceVO.getAgreementPrice(),suggestAgreementPrice);
            countryAgreementPriceVO.setSuggestAgreementPrice(suggestAgreementPrice.getPrice());
            countryAgreementPriceVO.setSuggestAgreementMark(suggestAgreementPrice.getMsg());
        } else {
            countryAgreementPriceVO.setCountryCostPrice(null);
            countryAgreementPriceVO.setCostMark(countryCostPrice.getMsg());
            countryAgreementPriceVO.setSuggestAgreementPrice(null);
            countryAgreementPriceVO.setSuggestAgreementMark("国家成本价为空，无法计算出来建议国家协议价");
        }
        SkuPO skuPo = skuAtomicService.getSkuPoBySkuId(vo.getSkuId());
        countryAgreementPriceVO.setBrandId(skuPo.getBrandId());
        countryAgreementPriceVO.setLastCatId(skuPo.getJdCatId());
        MkuRelationPO mkuRelationPO = mkuRelationAtomicService.getMkuBySkuId(skuPo.getSkuId());
        if(Objects.nonNull(mkuRelationPO)) {
            countryAgreementPriceVO.setMkuId(mkuRelationPO.getMkuId());
        }
        countryAgreementPriceVO.setCostMark(countryCostPrice.getMsg());
        countryAgreementPriceVO.setDataSourceTypeEnums(AgreementPriceDataSourceTypeEnums.COST);
        if(StringUtils.isBlank(countryAgreementPriceVO.getUpdater())) {
            countryAgreementPriceVO.setCreator("System");
            countryAgreementPriceVO.setUpdater("System");
        }
        setAgreementPriceBySwitch(countryAgreementPriceVO);
        if (vo.getDataStatusSource() != null && AgreementPriceDataSourceTypeEnums.countryEnumByCode(vo.getDataStatusSource()) != null) {
            countryAgreementPriceVO.setDataSourceTypeEnums(AgreementPriceDataSourceTypeEnums.countryEnumByCode(vo.getDataStatusSource()));
        }
        DataResponse<Boolean> response = saveOrUpdate(countryAgreementPriceVO);
        return response.getSuccess();
    }


    private void setAgreementPriceBySwitch(CountryAgreementPriceVO countryAgreementPriceVO){
        Map<String, Integer> countryCodeSwitchTypeMap = operDuccConfig.agreementPriceSwitch();
        Integer type = countryCodeSwitchTypeMap.get(countryAgreementPriceVO.getTargetCountryCode());
        AgreementPriceSwitchEnums.forType(type).setAgreementPrice(countryAgreementPriceVO);
    }

    private Boolean validateSkuInfo(InitAgreementPriceVO vo){
        SkuPO skuPo = skuAtomicService.getSkuPoBySkuId(vo.getSkuId());
        if (skuPo == null) {
            log.info("skuPo is null vo:{}", JSON.toJSONString(vo));
            return false;
        }
        if(!Objects.equals(vo.getSourceCountryCode(),skuPo.getSourceCountryCode())){
            log.error("CountryAgreementPriceManageServiceImpl.validateSkuInfo fail sourceCountryCode error sku:{}",JSONObject.toJSONString(skuPo));
            return false;
        }
        if (!CountryConstant.COUNTRY_ZH.equals(vo.getSourceCountryCode())) {
            if(!Objects.equals(vo.getTargetCountryCode(),vo.getSourceCountryCode())){
                log.error("CountryAgreementPriceManageServiceImpl.validateSkuInfo fail targetCountryCode error sku:{}",JSONObject.toJSONString(vo));
                return false;
            }
        }
        if (CountryConstant.COUNTRY_ZH.equals(vo.getTargetCountryCode())) {
            if(!Objects.equals(vo.getSourceCountryCode(),CountryConstant.COUNTRY_ZH)){
                log.error("CountryAgreementPriceManageServiceImpl.validateSkuInfo fail sourceCountryCode error sku:{}",JSONObject.toJSONString(vo));
                return false;
            }
        }
        return true;
    }

    @Override
    public CountryAgreementPriceVO getCountryCostPrice(String sourceCountryCode,String targetCountryCode,Long skuId){

        CountryAgreementPriceReqVO vo = new CountryAgreementPriceReqVO();
        vo.setSourceCountryCode(sourceCountryCode);
        vo.setTargetCountryCode(targetCountryCode);
        vo.setSkuId(skuId);

        CountryAgreementPriceVO agreementPriceVO = null;
        try{
            agreementPriceVO = this.getCountryAgreementPrice(vo);
        }catch(Exception e){
            log.error("AbstractCustomerSkuPriceService.getCountryCostPrice currencyPriceVO is null",e);
        }

        if(agreementPriceVO == null || agreementPriceVO.getCountryCostPrice() == null){
            log.error("AbstractCustomerSkuPriceService.getCountryCostPrice currencyPriceVO or price is null");
            throw new BizException("国家成本价为空");
        }
        return agreementPriceVO;
    }

    /**
     * 更新协议价格和京东价格。
     * @param input 更新请求对象，包含协议价格和京东价格信息。
     * @return 更新结果，成功返回true，失败返回错误信息。
     */
    @Override
    public DataResponse<Boolean> updateAgreementPriceAndJdPrice(CountryAgreementPriceReqVO input) {
        CurrencyPriceVO currencyPriceVO = new CurrencyPriceVO();
        currencyPriceVO.setSuccess(true);
        if(Objects.nonNull(input.getJdPrice()) && input.getJdPrice().compareTo(BigDecimal.ZERO) >= 0){
            currencyPriceVO = countryExtendPriceManageService.updateJdPrice(input);
        }
        if(Objects.nonNull(input.getAgreementPrice()) && input.getAgreementPrice().compareTo(BigDecimal.ZERO) >= 0){
            CountryAgreementPriceVO countryAgreementPriceVO = detailBySkuId(input);
            countryAgreementPriceVO.setAgreementPrice(input.getAgreementPrice());
            if(Objects.nonNull(input.getAgreementMark())){
                countryAgreementPriceVO.setAgreementMark(input.getAgreementMark());
            }else {
                countryAgreementPriceVO.setAgreementMark("手动批量修改");
            }
            countryAgreementPriceVO.setUpdater(input.getPin());
            countryAgreementPriceVO.setAgreementUpdateTime(Instant.now().toEpochMilli());
            DataResponse<Boolean> response = saveOrUpdate(countryAgreementPriceVO);
            if(!currencyPriceVO.getSuccess() || !response.getSuccess()){
                return createErrorResponse(currencyPriceVO.getMsg(), response.getMessage());
            }
            return response;
        }
        if(currencyPriceVO.getSuccess()) {
            return DataResponse.success(true);
        }else {
            return DataResponse.error(currencyPriceVO.getMsg());
        }
    }

    /**
     * 更新草稿国家协议价格和京东价格。
     * @param input 包含国家协议价格和京东价格的请求对象。
     * @return 操作结果，成功返回true，失败返回错误信息。
     */
    @Override
    public DataResponse<Boolean> applyForUpdateAgreementAndJdPrice(CountryAgreementPriceReqVO input) {
        CurrencyPriceVO currencyPriceVO = new CurrencyPriceVO();
        currencyPriceVO.setSuccess(true);
        if(Objects.nonNull(input.getJdPrice()) && input.getJdPrice().compareTo(BigDecimal.ZERO) >= 0){
            currencyPriceVO = countryExtendPriceManageService.updateJdPrice(input);
        }
        if(Objects.nonNull(input.getAgreementPrice()) && input.getAgreementPrice().compareTo(BigDecimal.ZERO) >= 0){
            CountryAgreementPriceVO countryAgreementPriceVO = detailBySkuId(input);
            countryAgreementPriceVO.setAgreementPrice(input.getAgreementPrice());
            countryAgreementPriceVO.setAgreementMark("手动批量修改");
            long currentTime = new Date().getTime();
            String pin = input.getPin();
            countryAgreementPriceVO.setUpdater(pin);
            countryAgreementPriceVO.setCreator(pin);
            countryAgreementPriceVO.setCreateTime(currentTime);
            countryAgreementPriceVO.setUpdateTime(currentTime);
            countryAgreementPriceVO.setAdjustmentPriceReason(input.getAdjustmentPriceReason());
            DataResponse<Boolean> response = countryAgreementPriceDraftManageService.save(countryAgreementPriceVO);
            if(!currencyPriceVO.getSuccess() || !response.getSuccess()){
                return createErrorResponse(currencyPriceVO.getMsg(), response.getMessage());
            }
            return response;
        }
        if(currencyPriceVO.getSuccess()) {
            return DataResponse.success(true);
        }else {
            return DataResponse.error(currencyPriceVO.getMsg());
        }
    }


    /**
     * 创建包含多个错误消息的错误响应
     */
    private DataResponse<Boolean> createErrorResponse(String jdPriceError, String agreementPriceError) {
        StringBuilder errorMsg = new StringBuilder();
        if (StringUtils.isNotBlank(jdPriceError)) {
            errorMsg.append(jdPriceError);
        }
        if (StringUtils.isNotBlank(agreementPriceError)) {
            if (errorMsg.length() > 0) {
                errorMsg.append(Constant.COMMA);
            }
            errorMsg.append(agreementPriceError);
        }
        return DataResponse.error(errorMsg.toString());
    }

    /**
     * 计算协议价格。
     * @param vo 包含目标国家代码的初始化协议价格对象。
     * @return 协议价格对象。
     */
    private CurrencyPriceVO calculateAgreementPrice(InitAgreementPriceVO vo){
        CurrencyPriceVO agreementPrice = this.calculatingAgreementPriceService(vo.getTargetCountryCode()).calculateAgreementPrice(vo,null);
        return agreementPrice;
    }

    private CurrencyPriceVO calculateAgreementPrice(InitAgreementPriceVO vo,CurrencyPriceVO countryCostPrice){
        CurrencyPriceVO agreementPrice = this.calculatingAgreementPriceService(vo.getTargetCountryCode()).calculateAgreementPrice(vo,countryCostPrice);
        return agreementPrice;
    }

    @Override
    public Map<Long, CountryAgreementPricePageVO> queryCountryAgreementPriceBySkuIds(CountryAgreementPriceReqVO reqVO) {
        if (CollectionUtils.isEmpty(reqVO.getSkuIds()) || StringUtils.isBlank(reqVO.getTargetCountryCode())) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<CountryAgreementPricePO> queryWrapper = Wrappers.lambdaQuery(CountryAgreementPricePO.class).in(CountryAgreementPricePO::getSkuId, reqVO.getSkuIds()).eq(CountryAgreementPricePO::getTargetCountryCode, reqVO.getTargetCountryCode())
                .eq(CountryAgreementPricePO::getYn, YnEnum.YES.getCode());

        List<CountryAgreementPricePO> list = countryAgreementPriceAtomicService.list(queryWrapper);

        return Optional.ofNullable(list).orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull)
                .map(CountryAgreementPriceConvert.INSTANCE::po2PageVO)
                .peek(vo-> this.setItemProfitRate(vo))
                .collect(Collectors.toMap(CountryAgreementPricePageVO::getSkuId, Function.identity()));
    }

    @Override
    public AgreementAndCostPriceVO calculateAgreementAndCostPrice(InitAgreementPriceVO vo) {
        CurrencyPriceVO costPriceVO = this.calculateCountryCostPrice(vo);
        CurrencyPriceVO currencyPriceVO = this.calculateAgreementPrice(vo, costPriceVO);

        AgreementAndCostPriceVO agreementAndCostPriceVO = new AgreementAndCostPriceVO();
        agreementAndCostPriceVO.setAgreementPrice(currencyPriceVO.getPrice());
        agreementAndCostPriceVO.setCostPrice(costPriceVO.getPrice());
        agreementAndCostPriceVO.setCurrency(currencyPriceVO.getCurrency());
        agreementAndCostPriceVO.setCostMsg(costPriceVO.getMsg());
        agreementAndCostPriceVO.setAgreementMsg(currencyPriceVO.getMsg());
        agreementAndCostPriceVO.setSuccess(costPriceVO.getSuccess() && currencyPriceVO.getSuccess());
//        agreementAndCostPriceVO.setForceUpdateCostPrice(costPriceVO.isForceUpdateCostPrice());
        return agreementAndCostPriceVO;
    }

    @Override
    public void updateYn(List<Long> skuIds, String updater) {
        log.info("更新国家协议价yn=0, skuIds={}, updater={}", skuIds, updater);
        if (CollectionUtils.isEmpty(skuIds)) {
            log.info("更新国家协议价yn=0, skuIds is empty");
            return;
        }

        List<CountryAgreementPricePO> list = countryAgreementPriceAtomicService.getCountryAgreementPriceList(skuIds, null);

        List<Long> ids = list.stream().map(CountryAgreementPricePO::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            log.info("更新国家协议价yn=0, ids is empty");
            return;
        }

        LambdaUpdateWrapper<CountryAgreementPricePO> wrapper = Wrappers.<CountryAgreementPricePO>lambdaUpdate()
                .in(CountryAgreementPricePO::getId, ids);
        CountryAgreementPricePO update = new CountryAgreementPricePO();
        update.setYn(YnEnum.NO.getCode());
        update.setUpdateInfo(updater);

        boolean result = countryAgreementPriceAtomicService.update(update, wrapper);
        log.info("更新国家协议价yn=0, 更新结果={}, ids={}, updater={}", result, ids, updater);
        if (!result) {
            log.error("更新国家协议价yn=0, 更新失败. id={}, updater={}", ids, updater);
            throw new BizException("更新国家协议价失败");
        }
    }

    /**
     * 根据提供的 id 列表查询对应的国家协议价格信息。
     * @param ids 国家协议价格的 id 列表
     * @return 对应的国家协议价格信息列表
     */
    @Override
    public List<CountryAgreementPriceVO> listByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        List<CountryAgreementPricePO> pricePOList = countryAgreementPriceAtomicService.listByIds(ids);
        if(CollectionUtils.isEmpty(pricePOList)){
            return Collections.emptyList();
        }
        List<CountryAgreementPriceVO> countryAgreementPriceVOList = CountryAgreementPriceConvert.INSTANCE.listPo2Vo(pricePOList);
        return countryAgreementPriceVOList;
    }

    @Override
    public DataResponse<PriceUnsellableThresholdImportResDTO> updateUnsellableThreshold(PriceUnsellableThresholdImportReqDTO input) {
        PriceUnsellableThresholdImportResDTO result = new PriceUnsellableThresholdImportResDTO();

        List<PriceUnsellableThresholdImportResDTO.Item> items = Lists.newArrayList();
        for (PriceUnsellableThresholdImportReqDTO.Item item : input.getItems()) {
            Long id = item.getId();
            BigDecimal unsellableThreshold = item.getUnsellableThreshold();

            try {
                countryAgreementPriceAtomicService.updateUnsellableThreshold(id, unsellableThreshold);
                items.add(new PriceUnsellableThresholdImportResDTO.Item(id, unsellableThreshold, true, "success"));
            } catch (Exception e) {
                log.error("updateUnsellableThreshold error, item={}", JSONObject.toJSONString(item), e);
                items.add(new PriceUnsellableThresholdImportResDTO.Item(id, unsellableThreshold, false, e instanceof BizException ? e.getMessage() : "更新不可售阈值失败"));
            } finally {
                log.info("更新协议价不可售阈值. id={}, unsellableThreshold={}", id, unsellableThreshold);
            }
        }

        result.setItems(items);

        return DataResponse.success(result);
    }

    @Resource
    private CustomerSkuSalePriceDetailManageService customerSkuSalePriceDetailManageService;

    @Resource
    private CustomerSkuPriceDetailAtomicService customerSkuPriceDetailAtomicService;

    @Override
    public DataResponse<PriceAvailableSaleStatusResDTO> updateAvailableSaleStatus(List<PriceAvailableSaleStatusReqDTO> input) {

        long count = input.stream().filter(item -> item.getId() == null).count();
        if (count > 0) {
            log.warn("id不能为空, count={}, input={}", count, JSONObject.toJSONString(input));
            return DataResponse.error("参数异常，id不能为空");
        }

        List<Long> ids = input.stream().map(PriceAvailableSaleStatusReqDTO::getId).distinct().collect(Collectors.toList());

        Map<Long, String> idUpdaterMap = input.stream().collect(Collectors.toMap(PriceAvailableSaleStatusReqDTO::getId, PriceAvailableSaleStatusReqDTO::getUpdater, (k1, k2) -> k1));

        List<CountryAgreementPricePO> list = countryAgreementPriceAtomicService.listValidByIds(ids);

        List<CountryAgreementPricePO> updateList = new ArrayList<>();

        // 构建需要更新客制化价格可售状态更新的参数
        List<PriceAvailableSaleStatusReqDTO> customerUpdateList = this.buildCustomerUpdateList(input, list);

        for (CountryAgreementPricePO item : list) {
            // 计算可售状态
            int availableSaleStatus = this.calculateAvailableSaleStatus(item);

            CountryAgreementPricePO update = new CountryAgreementPricePO();

            update.setId(item.getId());
            update.setUpdateTime(item.getUpdateTime());

            String updater = idUpdaterMap.get(item.getId());
            update.setUpdater(updater);

            update.setAvailableSaleStatus(availableSaleStatus);

            updateList.add(update);
        }

        List<Long> agreementIds = updateList.stream().map(CountryAgreementPricePO::getId).collect(Collectors.toList());

        productTransactionExecutor.execute(() -> {
            countryAgreementPriceAtomicService.updateAvailableSaleStatusList(updateList);
            // 更新协议价预警信息
            countryAgreementPriceWarningManager.syncAvailableSaleStatusList(agreementIds);
            // 更新客户价可售状态
            customerSkuSalePriceDetailManageService.updateAvailableSaleStatus(customerUpdateList);
        });

        return DataResponse.success();
    }

    private List<PriceAvailableSaleStatusReqDTO> buildCustomerUpdateList(List<PriceAvailableSaleStatusReqDTO> input, List<CountryAgreementPricePO> list) {

        if (CollectionUtils.isEmpty(input) || CollectionUtils.isEmpty(list)) {
            log.info("buildCustomerUpdateList input is null || list is null");
        }

        long l = System.currentTimeMillis();

        List<PriceAvailableSaleStatusReqDTO> customerUpdateList = null;
        Map<Long, PriceAvailableSaleStatusReqDTO> inputMap = input.stream().filter(item -> item.getId() != null).collect(Collectors.toMap(PriceAvailableSaleStatusReqDTO::getId, Function.identity(), (k1, k2) -> k1));
        try {

            customerUpdateList = Lists.newArrayList();

            Set<Long> customerDetailIds = Sets.newHashSet();

            for (CountryAgreementPricePO item : list) {
                PriceAvailableSaleStatusReqDTO agreementReq = inputMap.get(item.getId());
                if (agreementReq == null) {
                    log.info("agreementReq is null, 协议价id={}", item.getId());
                    continue;
                }
                if (item.getSkuId() == null || item.getCurrency() == null) {
                    log.warn("协议价skuId或currency为空, item={}", JSONObject.toJSONString(item));
                    continue;
                }
                List<CustomerSkuPriceDetailPO> customerSkuPriceDetailList = customerSkuPriceDetailAtomicService.getValidBySkuIdAndCurrency(item.getSkuId(), item.getCurrency());
                if (CollectionUtils.isEmpty(customerSkuPriceDetailList)) {
                    log.info("不存在客户价. item={}", JSONObject.toJSONString(item));
                    continue;
                }

                Set<Long> customerDetailPriceIds = customerSkuPriceDetailList.stream().map(CustomerSkuPriceDetailPO::getId).collect(Collectors.toSet());
                for (Long customerDetailPriceId : customerDetailPriceIds) {
                    if (customerDetailIds.contains(customerDetailPriceId)) {
                        continue;
                    }
                    customerDetailIds.add(customerDetailPriceId);
                    customerUpdateList.add(new PriceAvailableSaleStatusReqDTO(customerDetailPriceId, agreementReq.getUpdater()));
                }
            }
        } catch (Exception e) {
            log.error("构建客制化价格更新可售状态参数失败, input={}, message={}", JSONObject.toJSONString(input), ExceptionUtil.getMessage(e, "构建客制化价格更新可售状态参数失败"), e);
        } finally {
            log.info("需要额外更新的客户可售状态. input={}, customerUpdateList={}, cost={}", JSONObject.toJSONString(input), JSONObject.toJSONString(customerUpdateList), System.currentTimeMillis() - l);
        }

        return customerUpdateList;
    }

    /**
     * 计算协议价可售状态
     * @param item 包含协议价、国家成本价和不可售阈值的对象
     * @return 可售状态枚举对应的代码值
     */
    private int calculateAvailableSaleStatus(CountryAgreementPricePO item) {

        BigDecimal agreementPrice = item.getAgreementPrice();
        BigDecimal countryCostPrice = item.getCountryCostPrice();
        BigDecimal unsellableThreshold = item.getUnsellableThreshold();

        if (agreementPrice == null || countryCostPrice == null || unsellableThreshold == null) {
            log.warn("计算协议价可售状态，不可售，参数为空，item={}", JSONObject.toJSONString(item));
            return PriceAvailableSaleStatusEnum.DISABLE.getCode();
        }

        // 计算利润率
        BigDecimal profitRate = SpringUtil.getBean(ProfitCalculateManageService.class).calculateAgreementProfitRate(agreementPrice, countryCostPrice);

        if (profitRate == null) {
            log.warn("计算协议价可售状态，不可售，利润率为空，item={}", JSONObject.toJSONString(item));
            return PriceAvailableSaleStatusEnum.DISABLE.getCode();
        }

        if (BigDecimalUtil.lt(profitRate, unsellableThreshold)) {
            log.warn("计算协议价可售状态，不可售，利润率小于不可售阈值，item={}, profitRate={}, unsellableThreshold={}", JSONObject.toJSONString(item), profitRate, unsellableThreshold);
            return PriceAvailableSaleStatusEnum.DISABLE.getCode();
        }

        log.warn("计算协议价可售状态，可售，利润率不小于不可售阈值，item={}, profitRate={}, unsellableThreshold={}", JSONObject.toJSONString(item), profitRate, unsellableThreshold);

        return PriceAvailableSaleStatusEnum.ENABLE.getCode();
    }


    /**
     * 根据审批ID更新不可售阈值
     */
    @Override
    public void updateUnsellableThresholdByApproveId(ApproveOrderPO order) {
        Long approveId = order.getId();
        // 利润率 百分比
        BigDecimal profitRate = approveFormAtomicService.getFiledValue(approveId, AbstractCreateApproveOrderExe.PROFIT_RATE, BigDecimal.class);

        if(profitRate == null) {
            log.error("审批流中未获取到利润率，approveId={}", approveId);
            AlertHelper.p0("审批流中未获取到利润率，approveId=", String.valueOf(approveId));
            return;
        }

        profitRate = profitRate.divide(BigDecimal.valueOf(100), 6, RoundingMode.HALF_UP);

        CountryAgreementPriceWarningPO warning = countryAgreementPriceWarningAtomicService.getValidById(Long.parseLong(order.getBizId()));

        if (warning == null) {
            log.warn("找不到协议价预警信息. warnId={}", order.getBizId());
            return;
        }

        // 查询协议价
        CountryAgreementPricePO agreementPrice = countryAgreementPriceAtomicService.getMapByBizNo(warning.getBizNo());

        if (agreementPrice == null) {
            log.warn("找不到协议价. bizNo={}", warning.getBizNo());
            return;
        }

        countryAgreementPriceWarningManager.updateUnsellableThreshold(agreementPrice.getSkuId(), agreementPrice.getTargetCountryCode(), profitRate, order.getUpdater());
    }

    @Override
    public void updatePoolStatus(Set<Long> agreementIds) {
        long l = System.currentTimeMillis();
        List<CountryAgreementPricePO> updateList = Lists.newArrayList();
        try {

            Map<Long, CountryAgreementPricePO> priceMap = countryAgreementPriceAtomicService.mapByIds(agreementIds);
            List<String> preseletorClientCodeList = operDuccConfig.getPreseletorClientCodeList();

            for (Long agreementId : agreementIds) {
                CountryAgreementPricePO agreementPrice = priceMap.get(agreementId);

                if (agreementPrice == null) {
                    log.info("agreementPrice 为空，agreementId={}", agreementId);
                    continue;
                }

                Long mkuId = agreementPrice.getMkuId();
                String targetCountryCode = agreementPrice.getTargetCountryCode();

                CountryAgreementPricePO update = new CountryAgreementPricePO();
                update.setId(agreementPrice.getId());

                // 设置国家池状态
                CountryMkuPO countryMkuPO = countryMkuAtomicService.getByMkuIdCountryCode(mkuId, targetCountryCode);
                if (countryMkuPO != null) {
                    update.setCountryMkuPoolStatus(countryMkuPO.getPoolStatus());
                } else {
                    update.setCountryMkuPoolStatus(CountryMkuPoolStatusEnum.NOT_POOL.getCode());
                    log.info("找不到国家池信息. mkuId={}", mkuId);
                }

                // 设置客户池状态
                String bindStatus = customerMkuAtomicService.getBindStatus(mkuId, targetCountryCode, null, preseletorClientCodeList);
                update.setCustomerMkuPoolStatus(bindStatus);

                updateList.add(update);
            }
            List<CountryAgreementPricePO> list = CollectionUtil.sort(updateList, Comparator.comparing(CountryAgreementPricePO::getId, Comparator.nullsLast(Long::compareTo)));
            countryAgreementPriceAtomicService.updatePoolStatus(list);
        } catch (Exception e) {
            log.info("更新协议价池状态异常，agreementIds={}, message={}, updateList={}", agreementIds, ExceptionUtil.getMessage(e, "更新协议价池状态失败"), JSONObject.toJSONString(updateList), e);
        } finally {
            log.info("更新协议价池状态完成，agreementIds={}, cost={}, updateList={}", agreementIds, System.currentTimeMillis() - l, JSONObject.toJSONString(updateList));
        }
    }
}
