package com.jdi.isc.product.soa.service.atomic.stocklog;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.enums.StockOperateResEnum;
import com.jdi.isc.product.soa.common.enums.StockOperateTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.domain.stock.po.StockLogPO;
import com.jdi.isc.product.soa.domain.stock.po.StockPO;
import com.jdi.isc.product.soa.repository.mapper.product.StockLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.*;
import java.util.stream.Collectors;

/**
 * sku库存原子服务
 * <AUTHOR>
 * @date 20231201
 */
@Service
@Slf4j
public class StockLogAtomicService extends ServiceImpl<StockLogMapper, StockLogPO> {

    public DataResponse<Boolean> save(StockManageReqDTO req, StockOperateTypeEnum stockLogTypeEnum){
        DataResponse<Boolean> result = DataResponse.success();
        try {
            List<StockLogPO> target = new ArrayList<>(req.getStockItem().size());
            for(StockItemManageReqDTO item : req.getStockItem()){
                StockLogPO po = new StockLogPO();
                po.setBizNo(req.getBizNo());
                po.setOrderId(req.getOrderId());
                po.setType(stockLogTypeEnum.getCode());
                po.setSkuId(item.getSkuId());
                po.setNum(item.getNum());
                po.setCreator(item.getUpdater());
                po.setUpdater(item.getUpdater());
                po.setCreateTime(new Date());
                po.setUpdateTime(po.getCreateTime());
                po.setWarehouseId(item.getWarehouseId());
                target.add(po);
            }
            boolean res = saveBatch(target);
            log.info("StockManageServiceImpl 库存流水写记录,类型:{}, req:{} , res:{}" ,  (stockLogTypeEnum+"-"+stockLogTypeEnum.getCode()), JSON.toJSONString(req),res);
            result.setCode(StockOperateResEnum.SUCCESS.getCode());
            return result;
        }catch (DuplicateKeyException e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            String errMsg = String.format(StockOperateResEnum.EXCEPTION_DUPLICATE_OPERATE.getDesc(),req.getBizNo());
            log.error(errMsg);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
            result = DataResponse.error(errMsg);
            result.setCode(StockOperateResEnum.EXCEPTION_DUPLICATE_OPERATE.getCode());
        }
        return result;
    }

    public boolean save(StockPO req, StockOperateTypeEnum stockLogTypeEnum, String bizNo, String orderId){
        StockLogPO target = new StockLogPO();
        target.setBizNo(bizNo);
        target.setOrderId(orderId);
        target.setType(stockLogTypeEnum.getCode());
        target.setSkuId(req.getSkuId());
        target.setNum(req.getAvailableStock());
        target.setCreator(req.getUpdater());
        target.setUpdater(req.getUpdater());
        target.setCreateTime(new Date());
        target.setUpdateTime(target.getCreateTime());
        target.setWarehouseId(req.getWarehouseId());
        boolean res = save(target);
        log.info("StockManageServiceImpl 库存流水写记录,类型:{}, req:{} , res:{}" ,  (stockLogTypeEnum+"-"+stockLogTypeEnum.getCode()), JSON.toJSONString(req),res);
        return res;
    }


    public boolean saveLog(StockPO req, StockOperateTypeEnum stockLogTypeEnum, String bizNo, String orderId,Long num){
        StockLogPO target = new StockLogPO();
        target.setBizNo(bizNo);
        target.setOrderId(orderId);
        target.setType(stockLogTypeEnum.getCode());
        target.setSkuId(req.getSkuId());
        target.setNum(num);
        // 如果登录上下文为空，则设置创建者和更新者为系统
        if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
            target.setCreator(Constant.PIN_SYSTEM);
            target.setUpdater(Constant.PIN_SYSTEM);
        }
        target.setCreateTime(new Date());
        target.setUpdateTime(target.getCreateTime());
        target.setWarehouseId(req.getWarehouseId());
        boolean res = save(target);
        log.info("StockManageServiceImpl.saveLog 库存流水写记录,类型:{}, req:{} , res:{} ,num={}" ,  (stockLogTypeEnum+"-"+stockLogTypeEnum.getCode()), JSON.toJSONString(req),res,num);
        return res;
    }

    public boolean exist(StockItemManageReqDTO req, String bizNo, StockOperateTypeEnum type){
        LambdaQueryWrapper<StockLogPO> wrapper = Wrappers.<StockLogPO>lambdaQuery()
                .eq(StockLogPO::getType, type.getCode())
                .eq(StockLogPO::getBizNo, bizNo)
                .eq(StockLogPO::getSkuId, req.getSkuId())
                .eq(StockLogPO::getNum, req.getNum())
                .eq(StockLogPO::getYn, YnEnum.YES.getCode())
                ;
        return this.baseMapper.exists(wrapper);
    }

    public List<StockLogPO> list(String orderId, String bizNo, StockOperateTypeEnum type, Set<Long> skuId){
        LambdaQueryWrapper<StockLogPO> wrapper = Wrappers.<StockLogPO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(skuId),StockLogPO::getSkuId,skuId)
                .eq(StockLogPO::getType, type.getCode())
                .eq(StringUtils.isNotBlank(bizNo),StockLogPO::getBizNo, bizNo)
                .eq(StringUtils.isNotBlank(orderId),StockLogPO::getOrderId, orderId)
                .eq(StockLogPO::getYn, YnEnum.YES.getCode())
                ;
        return this.baseMapper.selectList(wrapper);
    }

    public List<StockLogPO> listForWarehouse(String orderId, String bizNo, StockOperateTypeEnum type, Set<Long> skuId, String warehouseId){
        LambdaQueryWrapper<StockLogPO> wrapper = Wrappers.<StockLogPO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(skuId),StockLogPO::getSkuId,skuId)
                .eq(StockLogPO::getType, type.getCode())
                .eq(StringUtils.isNotBlank(bizNo),StockLogPO::getBizNo, bizNo)
                .eq(StringUtils.isNotBlank(orderId),StockLogPO::getOrderId, orderId)
                .eq(StringUtils.isNotBlank(warehouseId), StockLogPO::getWarehouseId, warehouseId)
                .eq(StockLogPO::getYn, YnEnum.YES.getCode())
                ;
        return this.baseMapper.selectList(wrapper);
    }

    /**查询多种日志类型**/
    public List<StockLogPO> listForMultiOperateTpe(String orderId, String bizNo, List<StockOperateTypeEnum> type, Set<Long> skuId){
        LambdaQueryWrapper<StockLogPO> wrapper = Wrappers.<StockLogPO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(skuId),StockLogPO::getSkuId,skuId)
                .in(StockLogPO::getType, type.stream().map(StockOperateTypeEnum::getCode).collect(Collectors.toList()))
                .eq(StringUtils.isNotBlank(bizNo),StockLogPO::getBizNo, bizNo)
                .eq(StringUtils.isNotBlank(orderId),StockLogPO::getOrderId, orderId)
                .eq(StockLogPO::getYn, YnEnum.YES.getCode())
                ;
        return this.baseMapper.selectList(wrapper);
    }

    public int truncate() {
        LambdaQueryWrapper<StockLogPO> wrapper = new LambdaQueryWrapper<StockLogPO>()
                .eq(StockLogPO::getYn, YnEnum.YES.getCode());
        return this.baseMapper.delete(wrapper);
    }

    /**
    * 根据订 /**
    * 根据订单号、业务号、操作类型和商品ID集合获取对应的库存日志映射。
    * @param orderId 订单号
    * @param bizNo 业务号
    * @param type 操作类型
    * @param skuId 商品ID集合
    * @return 商品ID到库存日志的映射关系
    */
    public Map<Long, List<StockLogPO>> getSkuStockLogMap(String orderId, String bizNo, StockOperateTypeEnum type, Set<Long> skuId) {
        List<StockLogPO> stockLogPOList = this.list(orderId, bizNo, type, skuId);
        return Optional.ofNullable(stockLogPOList)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.groupingBy(StockLogPO::getSkuId));
    }


    public Map<Long, List<StockLogPO>> getStockLogForMultiOperateTpeMap(String orderId, String bizNo, List<StockOperateTypeEnum> type, Set<Long> skuId) {
        List<StockLogPO> stockLogPOList = this.listForMultiOperateTpe(orderId, bizNo, type, skuId);
        return Optional.ofNullable(stockLogPOList)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.groupingBy(StockLogPO::getSkuId));
    }

    public boolean save(List<StockLogPO> stockLogPOList) {
        return this.saveBatch(stockLogPOList);
    }


    /**
     * 根据订单ID集合和操作类型列表获取对应的库存日志信息，返回一个Map，其中key为订单ID，value为一个Map，key为商品ID，value为对应的库存日志PO列表。
     * @param childOrderIds 订单ID集合
     * @param type 操作类型列表
     * @return 库存日志信息的Map
     */
    public Map<String,Map<Long,List<StockLogPO>>> getStockLogForMultiOperateTpeMap(Set<String> childOrderIds, List<StockOperateTypeEnum> type) {
        LambdaQueryWrapper<StockLogPO> wrapper = Wrappers.<StockLogPO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(childOrderIds),StockLogPO::getBizNo,childOrderIds)
                .in(CollectionUtils.isNotEmpty(type) ,StockLogPO::getType, type.stream().map(StockOperateTypeEnum::getCode).collect(Collectors.toSet()))
                .eq(StockLogPO::getYn, YnEnum.YES.getCode());
        List<StockLogPO> stockLogPOList = this.baseMapper.selectList(wrapper);

        return Optional.ofNullable(stockLogPOList).orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(StockLogPO::getBizNo,Collectors.groupingBy(StockLogPO::getSkuId)));
    }

    public Map<String,Map<Long,List<StockLogPO>>> getParentOrderIdForMultiOperateTpeMap(String parentOrderId, List<StockOperateTypeEnum> type) {
        LambdaQueryWrapper<StockLogPO> wrapper = Wrappers.<StockLogPO>lambdaQuery()
                .eq(StockLogPO::getOrderId,parentOrderId)
                .in(CollectionUtils.isNotEmpty(type) ,StockLogPO::getType, type.stream().map(StockOperateTypeEnum::getCode).collect(Collectors.toSet()))
                .eq(StockLogPO::getYn, YnEnum.YES.getCode());
        List<StockLogPO> stockLogPOList = this.baseMapper.selectList(wrapper);

        // 查询父单号下所有子单记录，并排除父单的记录
        return Optional.ofNullable(stockLogPOList).orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull)
                .filter(logPO -> !parentOrderId.equals(logPO.getBizNo()))
                .collect(Collectors.groupingBy(StockLogPO::getBizNo,Collectors.groupingBy(StockLogPO::getSkuId)));
    }

    /**
     * 查询订单里商品现货预占库存的总数量
     * @param orderId 父单号
     * @param bizNo 子单号
     * @param warehouseId 仓库ID
     * @param skuId sku集合
     * @return sku 商品ID 现货预占库存总数量
     */
    public Map<Long,Long> getSkuStockOccupyLogForWarehouseMap(String orderId, String bizNo,String warehouseId, Set<Long> skuId) {
        // 查询待分配库存日志
        List<StockLogPO> allocationLogPOList = this.getStockLogPOList(orderId, bizNo, StockOperateTypeEnum.WAITING_ALLOCATION, warehouseId, skuId);
        // 查询在途预占转现货预占库存日志
        List<StockLogPO> transferLogPOList = this.getStockLogPOList(orderId, bizNo, StockOperateTypeEnum.TRANSIT_STOCK_TRANSFER_STOCK_OCCUPY, warehouseId, skuId);
        // 查询预占回退库存日志
        //List<StockLogPO> occupyReturnLogPOList = this.getStockLogPOList(orderId, bizNo, StockOperateTypeEnum.OCCUPY_RETURN, warehouseId, skuId);

        Map<Long, Long> allocationNumMap = Optional.ofNullable(allocationLogPOList).orElseGet(ArrayList::new).stream().collect(Collectors.groupingBy(StockLogPO::getSkuId, Collectors.summingLong(StockLogPO::getNum)));
        Map<Long, Long> transferNumMap = Optional.ofNullable(transferLogPOList).orElseGet(ArrayList::new).stream().collect(Collectors.groupingBy(StockLogPO::getSkuId, Collectors.summingLong(StockLogPO::getNum)));
        //Map<Long, Long> occupyReturnNumMap = Optional.ofNullable(occupyReturnLogPOList).orElseGet(ArrayList::new).stream().collect(Collectors.groupingBy(StockLogPO::getSkuId, Collectors.summingLong(StockLogPO::getNum)));

        Map<Long, Long> stockOccupyNumMap = new HashMap<>(skuId.size());
        for (Long id : skuId) {
            // 现货预占库存 = 现货预占库存 - 现货扣减库存 - 预占回退库存
            stockOccupyNumMap.put(id,allocationNumMap.getOrDefault(id, 0L) - transferNumMap.getOrDefault(id, 0L));
        }
        return stockOccupyNumMap;
    }

    private List<StockLogPO> getStockLogPOList(String orderId, String bizNo, StockOperateTypeEnum type, String warehouseId, Set<Long> skuId) {
        LambdaQueryWrapper<StockLogPO> wrapper = Wrappers.<StockLogPO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(skuId),StockLogPO::getSkuId, skuId)
                .eq(StockLogPO::getType, type.getCode())
                .eq(StringUtils.isNotBlank(bizNo),StockLogPO::getBizNo, bizNo)
                .eq(StringUtils.isNotBlank(orderId),StockLogPO::getOrderId, orderId)
                .eq(StringUtils.isNotBlank(warehouseId),StockLogPO::getWarehouseId, warehouseId)
                .eq(StockLogPO::getYn, YnEnum.YES.getCode());
        List<StockLogPO> stockLogPOList = this.baseMapper.selectList(wrapper);
        return stockLogPOList;
    }
}




