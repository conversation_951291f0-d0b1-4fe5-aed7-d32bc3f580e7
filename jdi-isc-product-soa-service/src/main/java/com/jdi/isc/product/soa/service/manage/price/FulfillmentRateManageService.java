package com.jdi.isc.product.soa.service.manage.price;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.domain.price.biz.FulfillmentRateReqVO;
import com.jdi.isc.product.soa.domain.price.biz.FulfillmentRateVO;
import com.jdi.isc.product.soa.domain.price.po.FulfillmentRatePO;
import com.jdi.isc.product.soa.price.api.price.req.FulfillmentRateChangeReqVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description: 履约费率表数据维护服务
 * @Author: zhaokun51
 * @Date: 2024/10/29 21:47
 **/

public interface FulfillmentRateManageService {

    /**
     * 履约费率表, update yn = 0.
     *
     * @param skuIds   the sku ids
     * @param finalPin the final pin
     */
    void updateYn(List<Long> skuIds, String finalPin);

    /**
     * 保存、更新
     * @param input 提交参数
     * @return 结果
     */
    Boolean saveOrUpdate(FulfillmentRateVO input);

    /**
     * 根据ID删除记录
     * @param id 要删除的记录的ID
     * @return 删除是否成功
     */
    Boolean deleteById(Long id);

    /**
     * 详情
     * @param id 对象ID
     * @return VO对象
     */
    FulfillmentRateVO detail(Long id);

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    PageInfo<FulfillmentRateVO> pageSearch(FulfillmentRateReqVO input);

    /** 根据货源国、目标国、skuId、末级类目Id(可选)查询履约费率*/
    FulfillmentRatePO getFulfillmentRate(FulfillmentRateVO input);

    /** 批量获取履约费率*/
    Map<Long, FulfillmentRatePO> batchGetFulfillmentRate(List<FulfillmentRateVO> input);

    Boolean rateChangeToPriceRefresh(List<FulfillmentRateChangeReqVO> input);

}
