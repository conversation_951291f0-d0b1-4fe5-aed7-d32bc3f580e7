package com.jdi.isc.product.soa.service.mapstruct.category;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.category.biz.CategoryQueryPageApiDTO;
import com.jdi.isc.product.soa.api.category.biz.GmsCategoryChangeDTO;
import com.jdi.isc.product.soa.api.category.biz.JdCategorySyncReqDTO;
import com.jdi.isc.product.soa.api.common.BaseLangDTO;
import com.jdi.isc.product.soa.api.jdm.category.req.JdmCategoryReqDTO;
import com.jdi.isc.product.soa.api.jdm.category.res.JdmCategoryResDTO;
import com.jdi.isc.product.soa.api.supplier.res.CategoryTreeResDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.CategoryTreeApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.res.CategoryPathNameDTO;
import com.jdi.isc.product.soa.api.wisp.category.biz.CategoryDTO;
import com.jdi.isc.product.soa.api.wisp.category.biz.CategoryLangDTO;
import com.jdi.isc.product.soa.api.wisp.category.biz.CategoryTreeNodeDTO;
import com.jdi.isc.product.soa.domain.category.biz.*;
import com.jdi.isc.product.soa.domain.category.po.CategoryLangPO;
import com.jdi.isc.product.soa.domain.category.po.CategoryPO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;


/**
 * 类目对象转换
 *
 * <AUTHOR>
 * @date 20231110
 */
@Mapper
public interface CategoryConvert {

    CategoryConvert INSTANCE = Mappers.getMapper(CategoryConvert.class);

    @InheritConfiguration
    CategoryPO dto2po(CategoryVO categoryDTO);

    @InheritConfiguration
    CategoryVO po2dto(CategoryPO categoryPO);

    @InheritConfiguration
    List<CategoryVO> listPo2dto(List<CategoryPO> categoryPO);

    CategoryTreeNodeDTO comboBox2TreeNodeDTO(CategoryComboBoxVO comboBoxVO);

    List<CategoryTreeNodeDTO> comboBoxList2TreeNodeDTOList(List<CategoryComboBoxVO> comboBoxVOList);

    @Mapping(source = "parentCatId",target = "id")
    @Mapping(source = "childCatName",target = "categoryName")
    CategoryComboBoxReqVO jdmReq2ComboBoxReq(JdmCategoryReqDTO reqDTO);

    List<JdmCategoryResDTO> listComboBoxRes2Jdm(List<CategoryComboBoxVO> comboBoxVOList);

    List<CategoryTreeVo> treeVo2ApiDto(List<CategoryTreeApiDTO> list);

    List<CategoryLangVO> listLangPo2Vo(List<CategoryLangPO> pos);
    List<BaseLangDTO> listLangVo2Dto(List<CategoryLangVO> pos);

    @Mapping(target = "createTime",ignore = true)
    @Mapping(target = "updateTime",ignore = true)
    BaseLangDTO langVo2Dto(CategoryLangVO categoryLangVO);

    @InheritConfiguration
    PageInfo<CategoryDTO> pageVo2Dto(PageInfo<CategoryVO> pageInfo);

    List<CategoryTreeResDTO> listTreeVo2TreeDto(List<CategoryTreeVo> inputs);

    Map<Long, CategoryPathNameDTO> mapVo2Dto(Map<Long,CategoryPathNameVO> input);

    List<CategoryPathNameVO> listPathVO2PathNameVO(List<CategoryPathVO> inputs);

    CategoryQueryPageVO.Request pageApiReq2PageVoReq(CategoryQueryPageApiDTO.Request input);

    PageInfo<CategoryQueryPageApiDTO.Response> pageVoResponse2PageApiRes(PageInfo<CategoryQueryPageVO.Response> input);

    JdCategorySyncReqVO syncDto2ReqVO(JdCategorySyncReqDTO dto);

    @Mapping(source = "jdParentCatId",target = "parentCatId")
    CategoryDTO vo2DTO(CategoryVO vo);

    @Mapping(source = "level",target = "catLevel")
    @Mapping(source = "catId",target = "jdCatId")
    @Mapping(source = "fidCatId",target = "jdParentCatId")
    CategoryPO changeDto2Po(GmsCategoryChangeDTO dto);

    @Mapping(source = "jdCatId",target = "catId")
    CategoryLangDTO langVO2DTO(CategoryLangVO categoryLangVO);

    Map<Long,CategoryDTO> mapVo2DTO(Map<Long,CategoryVO> map);
}
