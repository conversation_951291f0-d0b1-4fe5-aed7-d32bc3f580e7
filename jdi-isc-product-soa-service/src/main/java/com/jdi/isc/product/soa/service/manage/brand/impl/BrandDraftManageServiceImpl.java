package com.jdi.isc.product.soa.service.manage.brand.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.domain.brand.biz.BrandDraftVO;
import com.jdi.isc.product.soa.domain.brand.po.BrandDraftPO;
import com.jdi.isc.product.soa.service.atomic.brand.BrandDraftAtomicService;
import com.jdi.isc.product.soa.service.manage.brand.BrandDraftManageService;
import com.jdi.isc.product.soa.service.mapstruct.brand.BrandConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/2
 **/
@Slf4j
@Service
public class BrandDraftManageServiceImpl extends BaseManageSupportService<BrandDraftVO, BrandDraftPO> implements BrandDraftManageService {


    @Resource
    private BrandDraftAtomicService brandDraftAtomicService;


    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 30)
    public boolean saveOrUpdate(BrandDraftVO brandDraftVO) {
        BrandDraftPO brandDraftPO = BrandConvert.INSTANCE.draftVO2PO(brandDraftVO);

        LambdaQueryWrapper<BrandDraftPO> queryWrapper = Wrappers.lambdaQuery(BrandDraftPO.class).eq(BrandDraftPO::getBrandId, brandDraftPO.getBrandId()).eq(BrandDraftPO::getYn, YnEnum.YES.getCode());

        BrandDraftPO selectOne = brandDraftAtomicService.getOne(queryWrapper);
        boolean result;
        if (Objects.nonNull(selectOne)) {
            brandDraftPO.setId(selectOne.getId());
            result =  brandDraftAtomicService.saveOrUpdate(brandDraftPO);
        }else {
            result =   brandDraftAtomicService.save(brandDraftPO);
        }
        return result;
    }

    @Override
    public BrandDraftVO getBrandDraftVo(Long brandId) {

        LambdaQueryWrapper<BrandDraftPO> queryWrapper = Wrappers.lambdaQuery(BrandDraftPO.class).eq(BrandDraftPO::getBrandId, brandId).eq(BrandDraftPO::getYn, YnEnum.YES.getCode());

        BrandDraftPO selectOne = brandDraftAtomicService.getOne(queryWrapper);
        return BrandConvert.INSTANCE.draftPo2VO(selectOne);
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 30)
    public boolean remove(Long brandId) {

        LambdaQueryWrapper<BrandDraftPO> queryWrapper = Wrappers.lambdaQuery(BrandDraftPO.class).eq(BrandDraftPO::getBrandId, brandId).eq(BrandDraftPO::getYn, YnEnum.YES.getCode());
        BrandDraftPO selectOne = brandDraftAtomicService.getOne(queryWrapper);
        if (Objects.nonNull(selectOne)) {
            return brandDraftAtomicService.removeById(selectOne);
        }
        return false;
    }
}
