package com.jdi.isc.product.soa.service.manage.spu.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.CategoryAttrTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuTaxAuditStatusEnum;
import com.jdi.isc.product.soa.api.common.enums.sku.SkuStatusEnum;
import com.jdi.isc.product.soa.api.spu.req.SupplierSpuIdVerifyDTO;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.AttributeCheckTypeEnum;
import com.jdi.isc.product.soa.common.enums.AttributeDimensionEnum;
import com.jdi.isc.product.soa.common.enums.CheckTypeEnum;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.domain.category.biz.GlobalQualificationLangVO;
import com.jdi.isc.product.soa.domain.category.biz.GlobalQualificationVO;
import com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum;
import com.jdi.isc.product.soa.domain.enums.spu.SpuTypeEnum;
import com.jdi.isc.product.soa.domain.enums.vendor.VendorTypeEnum;
import com.jdi.isc.product.soa.domain.mku.biz.MkuOperateVO;
import com.jdi.isc.product.soa.domain.mku.po.MkuLangPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPricePageVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceReqVO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuAmendVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuCertificatePO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.ProductGlobalAttributePO;
import com.jdi.isc.product.soa.domain.spu.po.SpuCertificatePO;
import com.jdi.isc.product.soa.domain.spu.po.SpuLangPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.supplier.AttributeScopeVO;
import com.jdi.isc.product.soa.domain.vendor.biz.VendorVO;
import com.jdi.isc.product.soa.rpc.market.ZbProductDetailRpcService;
import com.jdi.isc.product.soa.service.atomic.attribute.AttributeLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.attribute.AttributeValueLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuCertificateAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.ProductGlobalAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuCertificateAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.SupplierSettlementAccountAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.CustomerSkuTaxRateAtomicService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.category.GlobalAttributeManageService;
import com.jdi.isc.product.soa.service.manage.category.GlobalQualificationManageService;
import com.jdi.isc.product.soa.service.manage.certificate.CertificateManageService;
import com.jdi.isc.product.soa.service.manage.country.CountryLangManageService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceManageService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.ExtendInfoService;
import com.jdi.isc.product.soa.service.manage.spu.SpuDraftManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuReadManageService;
import com.jdi.isc.product.soa.service.manage.stock.StockManageService;
import com.jdi.isc.product.soa.service.manage.vendor.VendorManageService;
import com.jdi.isc.product.soa.service.mapstruct.ProductAttributeConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SkuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.sku.SkuConvert;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuConvert;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * Spu读服务
 *
 * <AUTHOR>
 * @date 2023/11/29
 **/
@Slf4j
@Service
public class SpuReadManageServiceImpl implements SpuReadManageService {

    @Resource
    private CategoryOutService categoryOutService;

    @Resource
    private CertificateManageService certificateManageService;

    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private SpuLangAtomicService spuLangAtomicService;

    @Resource
    @Lazy
    private SpuConvertService spuConvertService;

    @Resource
    private SpuDraftManageService spuDraftManageService;

    @Resource
    private VendorManageService vendorManageService;

    @Resource
    private ZbProductDetailRpcService zbProductDetailRpcService;

    @Resource
    @Lazy
    private GlobalQualificationManageService globalQualificationManageService;

    /**
     * 定义提取图片URL和height值的正则表达式，提取的字段用group的()语法
     */
    private static final Pattern patternBgImage = Pattern.compile("background-image:url\\(.*(//.*)\\)");

    /**
     * 匹配src正则
     */
    private static final Pattern patternSrc = Pattern.compile("src=\"(http://.*?)\"");

    @Resource
    private AsyncTaskExecutor searchSpuExecutor;

    @Resource
    private ExtendInfoService extendInfoService;

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @Resource
    private CustomerSkuTaxRateAtomicService customerSkuTaxRateAtomicService;
    @Resource
    private CountryManageService countryManageService;
    @Resource
    private LangManageService langManageService;

    private final static  int batch_size = 100;
    @Resource
    private SkuConvertService skuConvertService;

    @Resource
    private MkuLangAtomicService mkuLangAtomicService;
    @Resource
    private GlobalAttributeManageService globalAttributeManageService;
    @Resource
    private SpuCertificateAtomicService spuCertificateAtomicService;
    @Resource
    private CountryLangManageService countryLangManageService;

    @Resource
    private SkuCertificateAtomicService skuCertificateAtomicService;


    @Resource
    private AttributeLangAtomicService attributeLangAtomicService;

    @Resource
    private AttributeValueLangAtomicService attributeValueLangAtomicService;
    @Resource
    private SkuPriceAtomicService skuPriceAtomicService;
    @Resource
    private SkuReadManageService skuReadManageService;
    @Resource
    private StockManageService stockManageService;
    @Resource
    private ProductAttributeConvertService productAttributeConvertService;
    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;

    private final static String OUT_OF_STOCK_STR = "无货";
    private final static String HAVE_STOCK_STR = "有货";

    @Resource
    private SupplierSettlementAccountAtomicService supplierSettlementAccountAtomicService;
    @Resource
    private CountryAgreementPriceManageService countryAgreementPriceManageService;

    @Resource
    private SaleAttributeManageService saleAttributeManageService;

    /**
     * 根据条件查询spu列表
     *
     * @param spuQueryReqVO 查询过滤条件对象
     * @return
     */
    @Override
    public PageInfo<SpuVO> spuPage(SpuQueryReqVO spuQueryReqVO) {
        String lang = LangContextHolder.get();
        Page<SpuVO> page = new Page<>(spuQueryReqVO.getIndex(), spuQueryReqVO.getSize());
        page.setRecords(Lists.newArrayList());

        try {
            // 属性范围不为空时，使用%拼接，方便查询做模糊搜索
            if (CollectionUtils.isNotEmpty(spuQueryReqVO.getAttributeScopes())) {
                spuQueryReqVO.setAttributeScope(spuQueryReqVO.getAttributeScopes().stream().sorted().collect(Collectors.joining(Constant.COMMA)));
            }

            if (CollectionUtils.isNotEmpty(spuQueryReqVO.getMkuIds())) {
                List<MkuRelationPO> mkuRelationPOS = mkuRelationAtomicService.queryBindListByMkuIds(spuQueryReqVO.getMkuIds());
                if (CollectionUtils.isEmpty(mkuRelationPOS)) {
                    return pageTransform(page);
                }
                List<Long> skuIds = mkuRelationPOS.stream().map(MkuRelationPO::getSkuId).collect(Collectors.toList());
                // 如果传过来的skuIds不为空，则和mku绑定的skuid去交集
                if (CollectionUtils.isNotEmpty(spuQueryReqVO.getSkuIds())) {
                    skuIds = Lists.newArrayList(CollectionUtil.intersection(spuQueryReqVO.getSkuIds(), skuIds));
                    if (CollectionUtils.isEmpty(skuIds)) {
                        return pageTransform(page);
                    }
                }
                spuQueryReqVO.setSkuIds(skuIds);
            }

            long total = spuAtomicService.getTotal(spuQueryReqVO);
            if (total <= 0) {
                return pageTransform(page);
            }

            page.setTotal(total);
            // 分页查询spu
            List<SpuPO> spuPoList = spuAtomicService.listSearch(spuQueryReqVO);
            if (CollectionUtils.isEmpty(spuPoList)) {
                return pageTransform(page);
            }

            List<Long> spuIds = spuPoList.stream().map(SpuPO::getSpuId).collect(Collectors.toList());
            Set<Long> catIdSet = spuPoList.stream().map(SpuPO::getJdCatId).collect(Collectors.toSet());
            Set<String> vendorCodeSet = spuPoList.stream().filter(Objects::nonNull).map(SpuPO::getVendorCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());

            // 查询spu下sku列表
            CompletableFuture<Map<Long, List<SkuPO>>> skuMapFuture = CompletableFuture.supplyAsync(() -> skuAtomicService.querySkuListBySpuIds(Sets.newHashSet(spuIds)), searchSpuExecutor);
            // 查询商品名
            CompletableFuture<Map<Long, SpuLangPO>> spuLangFuture = CompletableFuture.supplyAsync(() -> spuLangAtomicService.getSpuLangNameBySpuIds(spuIds, lang), searchSpuExecutor);
            // 查询类目面包屑
            CompletableFuture<Map<Long, String>> catPathFuture = CompletableFuture.supplyAsync(() -> categoryOutService.queryPathStr(catIdSet, lang), searchSpuExecutor);
            // 查询spu下第一个sku的国内供应商信息
            CompletableFuture<Map<Long, VendorVO>> jdVendorFuture = CompletableFuture.supplyAsync(() -> this.querySpuJdVendorMapBySpuIds(spuIds), searchSpuExecutor);
            // 查询国际发品的供应商信息
            CompletableFuture<Map<String, String>> crossborderFuture = CompletableFuture.supplyAsync(() -> this.queryVendorVOByVendorCodeSet(vendorCodeSet), searchSpuExecutor);

            Map<Long, List<SkuPO>> spuAndSkuMap = skuMapFuture.get(30, TimeUnit.SECONDS);
            CompletableFuture<Map<Long, SpuVO>> mkuRelationMapFuture = CompletableFuture.supplyAsync(() -> spuConvertService.queryMkuAndSkuRelation(spuAndSkuMap), searchSpuExecutor);

            Map<Long, SpuLangPO> langNameMap = spuLangFuture.get(30, TimeUnit.SECONDS);
            Map<Long, String> categoryMap = catPathFuture.get(30, TimeUnit.SECONDS);
            Map<Long, VendorVO> spuJdVendorMap = jdVendorFuture.get(30, TimeUnit.SECONDS);
            Map<String, String> vendorMap = crossborderFuture.get(30, TimeUnit.SECONDS);
            Map<Long, SpuVO> mkuRelationMap = mkuRelationMapFuture.get(30, TimeUnit.SECONDS);

            // spuVo填补商品名、sku个数、面包屑信息
            List<SpuVO> spuVOList = Lists.newArrayList();
            List<Future<SpuVO>> spuFutureList = Lists.newArrayList();
            for (SpuPO po : spuPoList) {
                //spuVOList.add(this.getSpuVO(po, langNameMap, spuAndSkuMap, categoryMap, spuJdVendorMap, vendorMap, mkuRelationMap));
                //spuFutureList.add(CompletableFuture.supplyAsync(()-> this.getSpuVO(po, langNameMap, spuAndSkuMap, categoryMap, spuJdVendorMap, vendorMap, mkuRelationMap),searchSpuExecutor));
                spuFutureList.add(CompletableFuture.supplyAsync(()-> this.getSpuVO(po, langNameMap, spuAndSkuMap, categoryMap, spuJdVendorMap, vendorMap, mkuRelationMap,lang),
                        searchSpuExecutor));
            }

            if (CollectionUtils.isNotEmpty(spuFutureList)) {
                for (Future<SpuVO> future : spuFutureList) {
                    try {
                        spuVOList.add(future.get(2, TimeUnit.SECONDS));
                    } catch (Exception e) {
                        log.error("【系统异常】spuPage 异步处理SPU VO异常 ",e);
                        if (null != future) {
                            future.cancel(true);
                        }
                    }
                }
            }
            page.setRecords(spuVOList);
        } catch (Exception e) {
            log.error("【系统异常】SpuReadManageServiceImpl.spuPage error spuQueryReqVO={}", JSON.toJSONString(spuQueryReqVO), e);
        }
        return pageTransform(page);
    }

    @Override
    public List<SpuVO> getSpuList(List<Long> spuIds) {
        String lang = LangContextHolder.get();
        List<SpuPO> spuPoList = spuAtomicService.querySpuList(spuIds);
        Set<Long> catIdSet = spuPoList.stream().map(SpuPO::getJdCatId).collect(Collectors.toSet());
        Set<String> vendorCodeSet = spuPoList.stream().filter(Objects::nonNull).map(SpuPO::getVendorCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        List<SpuVO> spuVOList = Lists.newArrayList();
        try {
            // 查询spu下sku列表
            CompletableFuture<Map<Long, List<SkuPO>>> skuMapFuture = CompletableFuture.supplyAsync(() -> skuAtomicService.querySkuListBySpuIds(Sets.newHashSet(spuIds)), searchSpuExecutor);
            // 查询商品名
            CompletableFuture<Map<Long, SpuLangPO>> spuLangFuture = CompletableFuture.supplyAsync(() -> spuLangAtomicService.getSpuLangNameBySpuIds(spuIds, lang), searchSpuExecutor);
            // 查询类目面包屑
            CompletableFuture<Map<Long, String>> catPathFuture = CompletableFuture.supplyAsync(() -> categoryOutService.queryPathStr(catIdSet, lang), searchSpuExecutor);
            // 查询spu下第一个sku的国内供应商信息
            CompletableFuture<Map<Long, VendorVO>> jdVendorFuture = CompletableFuture.supplyAsync(() -> this.querySpuJdVendorMapBySpuIds(spuIds), searchSpuExecutor);
            // 查询国际发品的供应商信息
            CompletableFuture<Map<String, String>> crossborderFuture = CompletableFuture.supplyAsync(() -> this.queryVendorVOByVendorCodeSet(vendorCodeSet), searchSpuExecutor);

            Map<Long, List<SkuPO>> spuAndSkuMap = skuMapFuture.get(30, TimeUnit.SECONDS);
            CompletableFuture<Map<Long, SpuVO>> mkuRelationMapFuture = CompletableFuture.supplyAsync(() -> queryMkuAndSkuRelation(spuAndSkuMap), searchSpuExecutor);

            Map<Long, SpuLangPO> langNameMap = spuLangFuture.get(30, TimeUnit.SECONDS);
            Map<Long, String> categoryMap = catPathFuture.get(30, TimeUnit.SECONDS);
            Map<Long, VendorVO> spuJdVendorMap = jdVendorFuture.get(30, TimeUnit.SECONDS);
            Map<String, String> vendorMap = crossborderFuture.get(30, TimeUnit.SECONDS);
            Map<Long, SpuVO> mkuRelationMap = mkuRelationMapFuture.get(30, TimeUnit.SECONDS);

            // spuVo填补商品名、sku个数、面包屑信息
            for (SpuPO po : spuPoList) {
                spuVOList.add(this.getSpuVO(po, langNameMap, spuAndSkuMap, categoryMap, spuJdVendorMap, vendorMap,mkuRelationMap,lang));
            }

        }catch (Exception e){
            log.error("【系统异常】SpuReadManageServiceImpl.getSpuList error param={}", JSON.toJSONString(spuIds), e);
        }
        return spuVOList;
    }

    private Map<Long,SpuVO> queryMkuAndSkuRelation(Map<Long, List<SkuPO>> spuAndSkuMap) {
        Map<Long, SpuVO> result = new HashMap<>();
        try {
            List<Long> skuIds = spuAndSkuMap.values().stream().flatMap(List::stream).map(SkuPO::getSkuId).collect(Collectors.toList());
            List<List<Long>> partition = Lists.partition(skuIds, batch_size);
            List<MkuRelationPO> mkuResult = new ArrayList<>();
            for (List<Long> part : partition) {
                List<MkuRelationPO> mkuRelationPO = mkuRelationAtomicService.queryBindListBySkuIds(part);
                if (CollectionUtils.isNotEmpty(mkuRelationPO)) {
                    mkuResult.addAll(mkuRelationPO);
                }
            }
            Map<Long, Long> skuMkuMap = mkuResult.stream().collect(HashMap::new, (m, v) -> m.put(v.getSkuId(), v.getMkuId()), HashMap::putAll);
            spuAndSkuMap.forEach((spu, skus) -> {
                SpuVO spuVO = new SpuVO();
                List<Long> skuList = new ArrayList<>();
                List<Long> mkuList = new ArrayList<>();
                spuVO.setMkuIds(mkuList);
                spuVO.setSkuIds(skuList);
                if (CollectionUtils.isNotEmpty(skus)) {
                    skus.forEach(s -> {
                        Long mkuId = skuMkuMap.get(s.getSkuId());
                        if (Objects.nonNull(mkuId)) {
                            skuList.add(s.getSkuId());
                            mkuList.add(mkuId);
                        }
                    });
                }
                result.put(spu, spuVO);
            });
        } catch (Exception e) {
            log.error("【系统异常】queryMkuAndSkuRelation param:{}, error:", JSON.toJSONString(spuAndSkuMap), e);
        }
        return result;
    }
    /**
     * 将SpuPO对象转换为SpuVO对象，并根据传入的Map对象填充SpuVO的相关属性。
     * @param po SpuPO对象
     * @param langNameMap 语言名称Map对象，用于获取Spu的多语言名称
     * @param spuAndSkuMap Spu和Sku的关系Map对象，用于获取Spu下的所有Sku信息
     * @param categoryMap 类目Map对象，用于获取Spu所属的类目名称
     * @param spuVendorMap Spu和供应商的关系Map对象，用于获取Spu的供应商信息
     * @param vendorMap 供应商Map对象，用于获取供应商的名称
     * @param mkuRelationMap MKU关系Map对象，用于获取Spu的MKU信息
     * @return 转换后的SpuVO对象
     */
    private SpuVO getSpuVO(SpuPO po, Map<Long, SpuLangPO> langNameMap, Map<Long, List<SkuPO>> spuAndSkuMap, Map<Long, String> categoryMap, Map<Long, VendorVO> spuVendorMap, Map<String, String> vendorMap, Map<Long, SpuVO> mkuRelationMap,String lang) {
        Long spuId = po.getSpuId();
        SpuVO spuVO = SpuConvert.INSTANCE.po2vo(po);
        // 属性范围国家编码转文字
        if (StringUtils.isNotBlank(spuVO.getAttributeScope())) {
            String[] scopeArray = spuVO.getAttributeScope().split(Constant.COMMA);
            List<String> countryList =
                Arrays.stream(scopeArray).filter(StringUtils::isNotBlank).sorted().map(countryCode -> countryManageService.getCountryNameByCountryCode(countryCode, null)).collect(Collectors.toList());
            spuVO.setAttributeScope(String.join(Constant.COMMA, countryList));
        }
        spuVO.setSpuName(langNameMap.containsKey(spuId) ? langNameMap.get(spuId).getSpuTitle() : "");
        if (MapUtils.isNotEmpty(spuAndSkuMap) && CollectionUtils.isNotEmpty(spuAndSkuMap.get(spuId))) {
            List<SkuPO> skuPOList = spuAndSkuMap.get(spuId);
            spuVO.setSkuCount(skuPOList.size());
            List<String> skuIds = skuPOList.stream().filter(Objects::nonNull).map(skuPO -> skuPO.getSkuId().toString()).collect(Collectors.toList());
            spuVO.setSkuIdStr(String.join(Constant.COMMA, skuIds));
            List<SkuVO> skuVOs = SkuConvert.INSTANCE.listPo2Vo(skuPOList);
            List<SpuSkuStockVO> skuStockVOList = convertSkuListSaleInfo(skuVOs,lang);
            spuVO.setSkuStockList(skuStockVOList);
        } else {
            spuVO.setSkuCount(0);
            spuVO.setSkuIdStr("");
        }

        spuVO.setCategoryBreadCrumbs(MapUtils.isNotEmpty(categoryMap) && categoryMap.containsKey(spuVO.getCatId()) ? categoryMap.get(spuVO.getCatId()) : "");
        // 设置国内供应商编码
        if (spuVendorMap.containsKey(spuId) && Objects.nonNull(spuVendorMap.get(spuId))) {
            VendorVO vendorVO = spuVendorMap.get(spuId);
            spuVO.setJdVendorCode(vendorVO.getVendorCode());
            spuVO.setJdVendorName(vendorVO.getVendorName());
        }
        if (vendorMap.containsKey(po.getVendorCode()) && StringUtils.isNotBlank(vendorMap.get(po.getVendorCode()))) {
            spuVO.setVendorName(vendorMap.get(po.getVendorCode()));
        }
        if(spuVO.getAuditStatus() != null
                && spuVO.getTaxAuditStatus() != null
                && SpuAuditStatusEnum.APPROVED.getCode().equals(spuVO.getAuditStatus())
                && !SpuTaxAuditStatusEnum.APPROVED.getCode().equals(spuVO.getTaxAuditStatus())){
            spuVO.setAuditStatus(spuVO.getTaxAuditStatus());
            spuVO.setAuditStatusStr(SpuTaxAuditStatusEnum.descByCode(po.getAuditStatus()));
        } else {
            spuVO.setAuditStatusStr(SpuAuditStatusEnum.descByCode(po.getAuditStatus()));
        }

        spuVO.setSaleUnitStr(extendInfoService.saleUnit(po.getSaleUnit(), LangConstant.LANG_ZH));
        SpuVO tempSpuVo = mkuRelationMap.get(spuId);
        if (Objects.nonNull(tempSpuVo)) {
            spuVO.setMkuIds(tempSpuVo.getMkuIds());
            spuVO.setSkuIds(tempSpuVo.getSkuIds());
        }

        return spuVO;
    }

    /**
     * 将 SkuVO 列表转换为包含销售信息的 SkuVO 列表。
     * @param skuVOList SkuVO 对象列表，包含需要转换的销售信息。
     * @return 转换后的 SkuVO 对象列表，包含销售属性、对应的属性值、mkuId 和库存数量。
     */
    private List<SpuSkuStockVO>  convertSkuListSaleInfo(List<SkuVO> skuVOList,String lang){
//        List<Long> AttributeIdList = new ArrayList<>();
//        List<Long> AttributeValueIdList = new ArrayList<>();
        List<Long> skuIdList = new ArrayList<>();
        Map<Long,Long> skuAndJdSkuMap = Maps.newHashMap();
        String sourceCountryCode = skuVOList.get(0).getSourceCountryCode();
        for (SkuVO skuVO : skuVOList){
            skuIdList.add(skuVO.getSkuId());
            skuAndJdSkuMap.put(skuVO.getSkuId(), skuVO.getJdSkuId());
//            String saleAttribute = skuVO.getSaleAttribute();
//            String[] attributeArray = saleAttribute.split(Constant.HASHTAG);
//            for (String attr : attributeArray) {
//                // 拆分为销售属性ID、属性值ID
//                String[] attributeValueArray = attr.split(Constant.COLON);
//                AttributeIdList.add(Long.valueOf(attributeValueArray[0]));
//                AttributeValueIdList.add(Long.valueOf(attributeValueArray[1]));
//            }
        }
//        Map<Long, String> attrIdNameMap= attributeLangAtomicService.attrNameByAttrId(AttributeIdList,lang);
//        Map<Long, String> attrvalueIdNameMap = attributeValueLangAtomicService.attrValueNameByAttrValueId(AttributeValueIdList,lang);
        Map<Long, Long> skuIdMkuIdMap = mkuRelationAtomicService.skuIdMkuIdMapBySkuIds(skuIdList);
        // 查询商品国家协议价、国际成本价、利润率
        Map<Long, CountryAgreementPricePageVO> agreementPriceVOMap = CountryConstant.COUNTRY_ZH.equals(sourceCountryCode) ? Maps.newHashMap() : countryAgreementPriceManageService.queryCountryAgreementPriceBySkuIds(new CountryAgreementPriceReqVO(skuIdList, sourceCountryCode));

        // 跨境品的库存查询国内,本土品查本土库存
        Map<Long, String> skuStockMap = Maps.newHashMap();
        Map<Long, Long> skuIdStockMap = Maps.newHashMap();
        if (CountryConstant.COUNTRY_ZH.equals(sourceCountryCode) && MapUtils.isNotEmpty(skuAndJdSkuMap)) {
            skuStockMap = skuReadManageService.getJdSkuStockBySkuIds(skuAndJdSkuMap.values().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        }else {
            StockManageReqDTO stockManageReqDTO = getStockManageReqDTO(skuIdList);
            stockManageReqDTO.setCountryCode(sourceCountryCode);
            Map<Long, StockResDTO> stockMap = stockManageService.getStock(stockManageReqDTO);
            Optional.ofNullable(stockMap).ifPresent(map -> map.forEach((skuId, stock) -> skuIdStockMap.put(skuId, stock.getStock())));
        }
        // 这个税 跨境的怎么算呢？
        Map<Long, List<SkuPricePO>> skuIdSkuPriceMap = skuPriceAtomicService.batchQuerySkuPrice(new HashSet<>(skuIdList));

        List<SpuSkuStockVO> skuStockVOList  = new ArrayList<>();
        Map<Long, List<PropertyValueVO>> saleAttributeDetailResult = saleAttributeManageService.getSkuSaleAttributeDetailBySkuIdSet(new HashSet<>(skuIdList), lang);
        for (SkuVO skuVO : skuVOList){
            // 销售属性和值映射
//            Map<String, String> saleAttributeMap = this.getSaleAttributeMap(skuVO, attrIdNameMap, attrvalueIdNameMap);
            // 销售属性和值映射    ZHAOYAN_SALE_ATTR
            List<PropertyValueVO> saleAttributeDetailResultList = saleAttributeDetailResult.getOrDefault(skuVO.getSkuId(), new ArrayList<>());
            Map<String, String> saleAttributeMap = new HashMap<>();
            for (PropertyValueVO propertyValueVO : saleAttributeDetailResultList){
//                String key = attrIdNameMap.getOrDefault(propertyValueVO.getAttributeId(),propertyValueVO.getAttributeId().toString());
//                String value = attrvalueIdNameMap.getOrDefault(propertyValueVO.getAttributeValueId(),propertyValueVO.getAttributeValueId().toString());
                saleAttributeMap.put(propertyValueVO.getAttributeName(),propertyValueVO.getAttributeValueName());
            }
            // 库存信息
            String stockStr = this.getSkuStockDesc(skuVO, skuStockMap, skuAndJdSkuMap, skuIdStockMap);
            SpuSkuStockVO spuSkuStockVO = new SpuSkuStockVO();
            spuSkuStockVO.setSkuId(skuVO.getSkuId());
            spuSkuStockVO.setSpuId(skuVO.getSpuId());
            spuSkuStockVO.setMkuId(skuIdMkuIdMap.get(skuVO.getSkuId()));
            spuSkuStockVO.setStock(stockStr);
            spuSkuStockVO.setSaleAttributeMap(saleAttributeMap);
            // 设置SKU价格
            this.setSkuPriceInfo(skuVO, skuIdSkuPriceMap, spuSkuStockVO, agreementPriceVOMap);
            spuSkuStockVO.setUpcCode(skuVO.getUpcCode());
            spuSkuStockVO.setWeight(skuVO.getWeight());
            spuSkuStockVO.setLength(skuVO.getLength());
            spuSkuStockVO.setWidth(skuVO.getWidth());
            spuSkuStockVO.setHeight(skuVO.getHeight());
            spuSkuStockVO.setProductionCycle(skuVO.getProductionCycle() == null ? null : skuVO.getProductionCycle().toString());
            spuSkuStockVO.setMoq(skuVO.getMoq());
            // 补充上下架状态
            spuSkuStockVO.setSkuStatus(skuVO.getSkuStatus());
            // 下架原因
            spuSkuStockVO.setDownReason(Objects.nonNull(skuVO.getSkuStatus()) && SkuStatusEnum.DOWN.getStatus() == skuVO.getSkuStatus() ? skuVO.getDownReason() : "");
            // 主站商品上下架状态关联开关
            spuSkuStockVO.setMainSiteSynSwitch(skuVO.getMainSiteSynSwitch());
            skuStockVOList.add(spuSkuStockVO);
        }
        return skuStockVOList;
    }

    /**
     * 设置 SKU 价格信息
     * @param skuVO SKU 信息对象
     * @param skuIdSkuPriceMap SKU ID 与 SKU 价格对象的映射关系
     * @param spuSkuStockVO SPu SKU 库存信息对象
     * @param agreementPriceVOMap SKU ID 与国家协议价格对象的映射关系
     */
    private void setSkuPriceInfo(SkuVO skuVO, Map<Long, List<SkuPricePO>> skuIdSkuPriceMap, SpuSkuStockVO spuSkuStockVO, Map<Long, CountryAgreementPricePageVO> agreementPriceVOMap) {
        List<SkuPricePO> skuPricePOList = skuIdSkuPriceMap.get(skuVO.getSkuId());
        for (SkuPricePO skuPricePO : skuPricePOList){
            if(skuPricePO.getTradeDirection().equals(TradeDirectionEnum.CUSTOMER)){
                spuSkuStockVO.setSalePrice(skuPricePO.getPrice());
                spuSkuStockVO.setTaxSalePrice(skuPricePO.getTaxPrice());
            }
            if(skuPricePO.getTradeDirection().equals(TradeDirectionEnum.SUPPLIER)){
                spuSkuStockVO.setPurchasePrice(skuPricePO.getPrice());
                spuSkuStockVO.setTaxPurchasePrice(skuPricePO.getTaxPrice());
            }
        }
        if (MapUtils.isNotEmpty(agreementPriceVOMap)) {
            CountryAgreementPricePageVO agreementPricePageVO = agreementPriceVOMap.get(skuVO.getSkuId());
            spuSkuStockVO.setCountryCostPrice(Objects.nonNull(agreementPricePageVO) ? agreementPricePageVO.getCountryCostPrice() : null);
            spuSkuStockVO.setAgreementPrice(Objects.nonNull(agreementPricePageVO) ? agreementPricePageVO.getAgreementPrice() : null);
            spuSkuStockVO.setAgreementProfitRate(Objects.nonNull(agreementPricePageVO) ? agreementPricePageVO.getProfitRate() : null);
        }
    }

    private String getSkuStockDesc(SkuVO skuVO, Map<Long, String> skuStockMap, Map<Long, Long> skuAndJdSkuMap, Map<Long, Long> skuIdStockMap) {
        String stockStr = OUT_OF_STOCK_STR;
        if (CountryConstant.COUNTRY_ZH.equals(skuVO.getSourceCountryCode())) {
            stockStr = skuStockMap.getOrDefault(skuAndJdSkuMap.get(skuVO.getSkuId()),OUT_OF_STOCK_STR);
/*                if (!HAVE_STOCK_STR.equals(stockStr)) {
                stockStr = OUT_OF_STOCK_STR;
            }*/
        }else {
            Long stock = skuIdStockMap.get(skuVO.getSkuId());
            if(Objects.nonNull(stock) && stock > 0){
                stockStr = HAVE_STOCK_STR;
            }
        }
        return stockStr;
    }

    /**
     * 将SkuVO中的销售属性转换为Map格式。
     * @param skuVO SkuVO对象，包含销售属性信息。
     * @param attrIdNameMap 键为属性ID，值为属性名称的Map。
     * @param attrvalueIdNameMap 键为属性值ID，值为属性值名称的Map。
     * @return 键为销售属性名称，值为销售属性值名称的Map。
     */
    private Map<String, String> getSaleAttributeMap(SkuVO skuVO, Map<Long, String> attrIdNameMap, Map<Long, String> attrvalueIdNameMap) {
        Map<String,String> saleAttributeMap = Maps.newHashMap();
        String saleAttribute = skuVO.getSaleAttribute();
        String[] attributeArray = saleAttribute.split(Constant.HASHTAG);
        for (String attr : attributeArray) {
            // 拆分为销售属性ID、属性值ID
            String[] attributeValueArray = attr.split(Constant.COLON);
            String key = attrIdNameMap.getOrDefault(StringUtils.isNotBlank(attributeValueArray[0]) ? Long.parseLong(attributeValueArray[0]):"",attributeValueArray[0]);
            String value = attrvalueIdNameMap.getOrDefault(StringUtils.isNotBlank(attributeValueArray[1]) ? Long.parseLong(attributeValueArray[1]) : "",attributeValueArray[1]);
            saleAttributeMap.put(key,value);
        }
        return saleAttributeMap;
    }


    /**
     * 查询创建商品所需信息
     *
     * @param lastCatId      末级类目ID
     * @param attributeScope 属性范围使用逗号","拼接，例如 CN,VN,TH
     * @return 返回商品需要的信息
     */
    @Override
    public SpuPrepareInfoVO getPrepareVo(Long lastCatId, String attributeScope, String sourceCountryCode, Integer isExport,String vendorCode) {
        SpuPrepareInfoVO prepareInfoVO = new SpuPrepareInfoVO();
        String lang = LangContextHolder.get();
        try {
            // 查询类目下所有属性
            Map<String, List<PropertyVO>> attributeMap = spuConvertService.getExtendAndSaleAttributeMap(lastCatId, attributeScope, lang, sourceCountryCode);
            prepareInfoVO.setExtendPropertyList(attributeMap.get(CategoryAttrTypeEnum.EXTEND.name()));
            prepareInfoVO.setSalePropertyList(attributeMap.get(CategoryAttrTypeEnum.SELL.name()));

            Map<String,List<GroupPropertyVO>> interAttributeMap = spuConvertService.getInterAttributeMap(lastCatId
                ,attributeScope,lang,sourceCountryCode,isExport);
            prepareInfoVO.setSpuInterPropertyList(interAttributeMap.get(AttributeDimensionEnum.SPU.getDesc()));
            prepareInfoVO.setSkuInterPropertyList(interAttributeMap.get(AttributeDimensionEnum.SKU.getDesc()));

            // 填充资质
            List<GlobalQualificationVO> allCertificateListVOS = globalQualificationManageService.queryByCountry(lastCatId,sourceCountryCode
                    , Arrays.stream(attributeScope.split(Constant.COMMA)).collect(Collectors.toSet())
                    ,null, lang, isExport);
            spuConvertService.fillPrepare4Cert(allCertificateListVOS,prepareInfoVO);
            prepareInfoVO.setCurrency(supplierSettlementAccountAtomicService.getCurrencyBySupplierCode(vendorCode));
        } catch (Exception e) {
            log.error("【系统异常】根据类目查询属性、资质列表异常,lastCatId={},attributeScope={}", lastCatId, attributeScope, e);
        }
        return prepareInfoVO;
    }


    /**
     * 根据spuId查询商品详情
     *
     * @param spuId spuId
     * @return 返回spu页面信息
     */
    @Override
    @PFTracing
    public SpuDetailVO getDetailBySpuId(Long spuId) {
        //  编辑商品先查草稿
        SpuDetailVO draftDetailVo = spuDraftManageService.getSpuDetailVoFromDraftBySpuId(spuId);
        if (Objects.nonNull(draftDetailVo)
                && ((Objects.isNull(draftDetailVo.getSpuVO().getAuditStatus()) || !SpuAuditStatusEnum.APPROVED.getCode().equals(draftDetailVo.getSpuVO().getAuditStatus()))
                    || (!Objects.isNull(draftDetailVo.getSpuVO().getTaxAuditStatus()) && !SpuTaxAuditStatusEnum.APPROVED.getCode().equals(draftDetailVo.getSpuVO().getTaxAuditStatus())))
                ){
            return draftDetailVo;
        }
        return getSpuDetailVoFromDb(spuId);

    }

    @Override
    @PFTracing
    public SpuDetailVO getSpuDetailVoFromDb(Long spuId) {
        SpuDetailVO spuDetailVO = new SpuDetailVO();
        // 查询spu基础信息
        SpuPO spuPo = spuAtomicService.getById(spuId);
        AssertValidation.isEmpty(spuPo, "", "商品ID不存在，请检查");
        SpuVO spuVO = SpuConvert.INSTANCE.po2vo(spuPo);
        spuVO.setSaleUnitStr(extendInfoService.saleUnit(spuVO.getSaleUnit(),LangConstant.LANG_ZH));
        spuDetailVO.setSpuVO(spuVO);
        // 补全国家信息
        spuConvertService.fillSpuCountry(spuDetailVO);
        // 补全语种信息
        spuConvertService.fillSpuLang(spuDetailVO);
        // 查询商品详描
        spuConvertService.fillSpuDesc(spuDetailVO);
        // 查询供应商
        spuConvertService.fillVendorName(spuDetailVO);
        // 查询类目名
        List<PropertyVO> skuExtendPropertyList = spuConvertService.fillCategoryInfo(spuDetailVO);
        // 查询品牌名
        spuConvertService.fillBrandInfo(spuDetailVO);
        // 处理扩展属性
        spuConvertService.convertExtendAttributeAndFillSelectedForView(spuDetailVO);
        // 处理SPU跨境属性
        spuConvertService.convertSpuAttribute(spuDetailVO);
        // 查询sku信息、价格、库存
        spuConvertService.fillSkuVoList(spuDetailVO);
        // 处理sku扩展属性，sku扩展属性分组信息
        spuConvertService.handleSkuVOGroupExtAttribute(spuDetailVO.getSkuVOList(), skuExtendPropertyList);
        // 查询spu资质信息
        spuConvertService.fillSpuCertificate(spuDetailVO);
        // 查询修订原因
        spuConvertService.fillSpuAmendReason(spuDetailVO);
        // 获取新版销售属性信息  ZHAOYAN_SALE_ATTR
        spuConvertService.fillSaleAttributes(spuDetailVO);

        // 对sku根据销售属性进行排序
        spuConvertService.sortSkuList(spuDetailVO);

        return spuDetailVO;
    }


    @Override
    public SpuBaseInfoVO getSpuBaseInfoVO(Integer spuType) {
        // 供应商查询接口未实现
        SpuBaseInfoVO spuBaseInfo = new SpuBaseInfoVO();
        try {
            // 查询供应商列表
            String sourceCountryCode = SpuTypeEnum.CROSS_BORDER.getCode() == spuType ? CountryConstant.COUNTRY_ZH : CountryConstant.COUNTRY_VN;
            List<VendorVO> vendorVOList = vendorManageService.listVendorByCountryCode(sourceCountryCode);
            spuBaseInfo.setVendorVOList(vendorVOList);

            if (SpuTypeEnum.CROSS_BORDER.getCode() == spuType) {
                List<AttributeScopeVO> attributeScopeVOList = countryManageService.getCountryCodeList().stream().filter(country -> !CountryConstant.COUNTRY_EN.equals(country))
                        .map(c -> new AttributeScopeVO(c, countryManageService.getCountryNameByCountryCode(c,null))).collect(Collectors.toList());
                spuBaseInfo.setAttributeScopeVOList(attributeScopeVOList);
            }
        } catch (Exception e) {
            log.error("【系统异常】查询供应商列表异常,spuType={}", spuType, e);
        }
        return spuBaseInfo;
    }

    public PageInfo<SpuVO> pageTransform(Page<SpuVO> target) {
        PageInfo<SpuVO> output = new PageInfo<>();
        output.setSize(target.getSize());
        output.setIndex(target.getCurrent());
        if (CollectionUtils.isNotEmpty(target.getRecords())) {
            output.setTotal(target.getTotal());
            output.setRecords(target.getRecords());
        }
        return output;
    }

    @Override
    public String getZbDescription(Long jdSkuId) {
        log.info("SpuReadManageServiceImpl.getZbDetail params[{}]", jdSkuId);
        String cssContent = zbProductDetailRpcService.getPcStyleCssContentByJdSkuId(jdSkuId);
        if (cssContent == null) {
            log.info("商品非装吧装修，商品编码：{}", jdSkuId);
            return "";
        }
        return convertZb2ProductDetailHtml(cssContent);
    }

    @Override
    public Map<Long, List<String>> getZbImageByJdSkuId(Long jdSkuId) {
        log.info("SpuReadManageServiceImpl.getZbDetail params[{}]", jdSkuId);
        String cssContent = zbProductDetailRpcService.getPcStyleCssContentByJdSkuId(jdSkuId);
        if (cssContent == null) {
            log.info("商品非装吧装修，商品编码：{}", jdSkuId);
            return Collections.emptyMap();
        }
        Map<Long, List<String>> jdSkuImageMap = Maps.newHashMap();
        jdSkuImageMap.put(jdSkuId, convertZb2ImageList(cssContent));
        return jdSkuImageMap;
    }

    private static List<String> convertZb2ImageList(String content) {
        // 商品详情模板
        String goodsDescTemplate = "https:%s";

        // 研究原串后，先以尺寸进行分组
        String[] split = content.split("px}");
        List<String> imageList = Lists.newArrayList();
        for (String s : split) {
            if (s.contains("background-image:url")) {    // 过滤掉不含背景图片的数据
                Matcher matcher = patternBgImage.matcher(s);   // 指定匹配器
                while (matcher.find()) { // 进行查找，并判断是否匹配
                    log.info("匹配到的字符串：" + matcher.group());
                    log.info("提取的图片地址：" + matcher.group(1));
                    imageList.add(String.format(goodsDescTemplate, matcher.group(1)));
                }
            }
        }
        return imageList;
    }

    private static String convertZb2ProductDetailHtml(String goodsDesc) {
        // 商品详情模板
        String goodsDescTemplate = "<img src=\"https:%s\" />";

        // 研究原串后，先以尺寸进行分组
        String[] split = goodsDesc.split("px}");
        StringBuilder stringBuilderMatcher = new StringBuilder();
        for (String s : split) {
            if (s.contains("background-image:url")) {    // 过滤掉不含背景图片的数据
                Matcher matcher = patternBgImage.matcher(s);   // 指定匹配器
                while (matcher.find()) { // 进行查找，并判断是否匹配
                    log.info("匹配到的字符串：" + matcher.group());
                    log.info("提取的图片地址：" + matcher.group(1));
                    stringBuilderMatcher.append(String.format(goodsDescTemplate, matcher.group(1)));
                }
            }
        }

        if (stringBuilderMatcher.length() == 0) {
            log.info("css中无装吧数据");
            return null;
        }

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("<div style=\"text-align: center;display: flex;flex-direction: column;justify-content: start\">");
        stringBuilder.append(stringBuilderMatcher);
        stringBuilder.append("</div>");
        log.info("拼接的字符串：" + stringBuilder.toString());
        return stringBuilder.toString();
    }


    /**
     * 查询商品供应商映射
     *
     * @param spuIds 商品ID列表
     * @return 返回商品ID与供应商信息的映射
     */
    private Map<Long, VendorVO> querySpuJdVendorMapBySpuIds(List<Long> spuIds) {
        if (CollectionUtils.isEmpty(spuIds)) {
            return Collections.emptyMap();
        }

        Map<Long, VendorVO> resultMap = Maps.newConcurrentMap();
        Map<Long, List<SkuPO>> spuSkuMap = skuAtomicService.querySkuListBySpuIds(Sets.newHashSet(spuIds));

        List<CompletableFuture<Void>> futureList = Lists.newArrayList();
        for (Map.Entry<Long, List<SkuPO>> entry : spuSkuMap.entrySet()) {
            futureList.add(CompletableFuture.runAsync(() -> this.completeVendor(entry, resultMap)));
        }

        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).get();
        } catch (InterruptedException | ExecutionException e) {
            log.error("查询商品供应商信息出错,入参:spuIds={}", JSON.toJSONString(spuIds), e);
        }
        return resultMap;
    }

    private void completeVendor(Map.Entry<Long, List<SkuPO>> entry, Map<Long, VendorVO> resultMap) {
        Long spuId = entry.getKey();
        List<SkuPO> skuPOList = entry.getValue();
        if (CollectionUtils.isEmpty(skuPOList)) {
            return;
        }
        SkuPO skuPO = skuPOList.get(0);
        String jdVendorCode = skuPO.getJdVendorCode();
        if (StringUtils.isNotBlank(jdVendorCode)) {
            VendorTypeEnum vendorTypeEnum = VendorTypeEnum.fromCountry(skuPO.getSourceCountryCode());
            VendorVO vendorVo = vendorManageService.getVendorVoByVendorCode(jdVendorCode, skuPO.getSourceCountryCode(), Objects.nonNull(vendorTypeEnum) ? vendorTypeEnum.getType() : VendorTypeEnum.DOMESTIC.getType());
            if (Objects.nonNull(vendorVo)) {
                resultMap.put(spuId, vendorVo);
            }
        }
    }

    private Map<String, String> queryVendorVOByVendorCodeSet(Set<String> vendorCodeSet) {
        return vendorManageService.queryVendorMapFromAllSupplierList(vendorCodeSet);
    }

    @Override
    public List<SaleUnitVO> saleUnitList(String lang) {
        LinkedHashMap<Integer, String> saleUnitMap = extendInfoService.listSaleUnitByLang(lang);
        List<SaleUnitVO> resultList = Lists.newArrayList();
        saleUnitMap.forEach((code,unit)-> resultList.add(new SaleUnitVO(code,unit)));
        return resultList;
    }


    @Override
    public List<SpuAmendVO> batchQuerySpuAmendVOListBySpuIds(List<Long> spuIds) {
        // 查询SPU对应要填内容
        List<String> countryCodeList = countryManageService.getCountryCodeList();
        List<Integer> checkTypeList  = new ArrayList<>();
        checkTypeList.add(AttributeCheckTypeEnum.ADD_CUSTOMER_POOL.getCode());
        checkTypeList.add(AttributeCheckTypeEnum.PUBLISH_PRODUCT.getCode());
        checkTypeList.add(AttributeCheckTypeEnum.BEFORE_SHIPMENT.getCode());
        List<SpuAmendVO> spuAmendVOList = this.getSpuAmendVOList(spuIds,countryCodeList,checkTypeList);
        if (CollectionUtils.isEmpty(spuAmendVOList)) {
            return Collections.emptyList();
        }
        // 查询spu下所有sku
        Map<Long, List<SkuPO>> spuSkuListMap = skuAtomicService.querySkuListBySpuIds(Sets.newHashSet(spuIds));
        if (MapUtils.isEmpty(spuSkuListMap)) {
            return Collections.emptyList();
        }
        Map<Long, Long> spuMkuMap = Maps.newHashMap();
        for (Map.Entry<Long, List<SkuPO>> entry : spuSkuListMap.entrySet()) {
            List<Long> skuIds = entry.getValue().stream().map(SkuPO::getSkuId).collect(Collectors.toList());
            // 查询sku关联mkuId
            List<MkuRelationPO> mkuRelationPOList = mkuRelationAtomicService.queryBindListBySkuIds(skuIds);

            MkuRelationPO mkuRelationPO = mkuRelationPOList.stream().min(Comparator.comparing(MkuRelationPO::getCreateTime)).get();
            spuMkuMap.put(entry.getKey(), mkuRelationPO.getMkuId());
        }

        Map<Long, MkuLangPO> mkuLangPOMap = mkuLangAtomicService.listLang(new MkuOperateVO(Sets.newHashSet(spuMkuMap.values()), LangConstant.LANG_ZH));

        for (SpuAmendVO amendVO : spuAmendVOList) {
            amendVO.setMkuId(spuMkuMap.get(amendVO.getSpuId()));
            amendVO.setMkuName(mkuLangPOMap.get(amendVO.getMkuId()).getMkuTitle());
        }
        return spuAmendVOList;
    }

    /**
     * 获取指定SPU ID和目标国家代码列表的SPU修正信息列表
     *
     * @param spuIds SPU ID列表
     * @param targetCountryCodeList 目标国家代码列表
     * @return SPU修正信息列表
     */
    @Override
    @PFTracing
    public List<SpuAmendVO> getSpuAmendVOList(List<Long> spuIds,List<String> targetCountryCodeList,List<Integer> checkTypeList){
        List<SpuPO> spuList = spuAtomicService.querySpuList(spuIds);
        // 获取SPU语言信息
        //Map<String, SpuLangPO> spuIdLangMap = spuLangAtomicService.getMapBySpuId(new HashSet<>(spuIds));
        // 通过国家获取多语言国家名称-集合  去除
//        Map<String,String> langCountryNameMap = getLangCountryNameMap(targetCountryCodeList);
        // 获取SPU证书信息
        Map<Long, List<SpuCertificatePO>> spuCertificateMap = spuCertificateAtomicService.querySpuCertMap(spuIds);
        List<SpuAmendVO> spuAmendVOList = new ArrayList<>();

        List<SkuPO> skuPOList = skuAtomicService.getSkuListBySpuIds(spuIds);
        Map<Long, List<SkuPO>> spuSkuMap = skuAtomicService.querySkuListBySpuIds(Sets.newHashSet(spuIds));
        // 使用Java Stream API将skuPOList转换为id列表
        List<Long> skuIdList = skuPOList.stream().map(SkuPO::getSkuId).collect(Collectors.toList());
        Map<Long, List<SkuCertificatePO>> skuCertificateMap = skuCertificateAtomicService.querySkuCertMap(skuIdList);

        // 使用Java Stream API将skuPOList转换为id列表
        List<ProductGlobalAttributePO> spuAttributePOS = productGlobalAttributeAtomicService.getBySpuId(spuIds);
        Map<Long,List<ProductGlobalAttributePO>> spuGlobalMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(spuAttributePOS)){
            spuGlobalMap.putAll(spuAttributePOS.stream().collect(Collectors.groupingBy(ProductGlobalAttributePO::getKeyId)));
        }

        for(SpuPO spuPO : spuList){
            log.info("SpuReadManageServiceImpl.getSpuAmendVOList spuPO={}",JSON.toJSONString(spuPO));
            // 获取目标国家代码集合
            Set<String> targetCountryCodes = Arrays.stream(spuPO.getAttributeScope().split(Constant.COMMA)).collect(Collectors.toSet());
            Map<String,Boolean> propertyMap = new LinkedHashMap<>();
            // 获取SPU证书ID集合
            Set<Long> spuTableSet = spuCertificateMap.getOrDefault(spuPO.getSpuId(), new ArrayList<>()).stream().map(SpuCertificatePO::getCertificateId).collect(Collectors.toSet());
            List<SkuPO> skuPOS = spuSkuMap.get(spuPO.getSpuId());
            for (Integer checkType : checkTypeList) {
                // 获取全球资质信息 // targetCountryCodes
                checkQualification(propertyMap, spuPO, new HashSet<>(targetCountryCodeList), spuTableSet, checkType);
                // 获取跨境属性信息
                checkGroupProperty(propertyMap, spuPO, new HashSet<>(targetCountryCodeList), checkType, spuGlobalMap.get(spuPO.getSpuId()));
                // 按照sku去检测跨境属性等信息
                checkSkuProperty(propertyMap,spuPO,new HashSet<>(targetCountryCodeList),checkType,skuPOS,skuCertificateMap);
            }
            // 处理多语言信息 去除
            //checkLangTitle(propertyMap, spuPO, spuIdLangMap);
            // 创建SpuAmendVO对象
            SpuAmendVO spuAmendVO = new SpuAmendVO();
            spuAmendVO.setSpuId(spuPO.getSpuId());
            spuAmendVO.setPropertyMap(propertyMap);
            List<SkuAmendVO> skuAmendVOList = convertSkuAmendVOList(skuPOS);
            spuAmendVO.setSkuAmendList(skuAmendVOList);
            spuAmendVOList.add(spuAmendVO);
        }
        log.info("SpuReadManageServiceImpl.getSpuAmendVOList spuAmendVOList={}",JSON.toJSONString(spuAmendVOList));
        return spuAmendVOList;
    }

    /**
     * 将 SkuPO 列表转换为 SkuAmendVO 列表。
     * @param skuPOList SkuPO 对象列表，需要转换的 SKU 信息。
     * @return 转换后的 SkuAmendVO 对象列表。
     */
    @PFTracing
    private List<SkuAmendVO> convertSkuAmendVOList(List<SkuPO> skuPOList){
        List<SkuAmendVO> skuAmendVOList = new ArrayList<>();
        for (SkuPO skuPO : skuPOList){
            SkuAmendVO skuAmendVO = new SkuAmendVO();
            skuAmendVO.setSkuId(skuPO.getSkuId());
            skuAmendVOList.add(skuAmendVO);
        }
        return skuAmendVOList;
    }

    /**
     * 检查 SKU 的属性和资质信息是否满足目标国家的要求。
     * @param propertyMap SKU 属性信息
     * @param spuPO SPu 对象
     * @param targetCountryCodes 目标国家的国别代码集合
     * @param checkType 检查类型
     * @param skuPOList SKU 对象列表
     * @param skuCertificateMap SKU 的资质信息映射表
     */
    @PFTracing
    private void checkSkuProperty(Map<String,Boolean> propertyMap,SpuPO spuPO,Set<String> targetCountryCodes,Integer checkType, List<SkuPO> skuPOList,Map<Long, List<SkuCertificatePO>> skuCertificateMap){
        List<GlobalQualificationVO> skuGlobalQualificationVOList =getSkuQualification(spuPO, targetCountryCodes);
        List<GroupPropertyVO> skuGroupPropertyList = getSkuGroupProperty(spuPO, targetCountryCodes);
        Map<Long,List<ProductGlobalAttributePO>> skuAttributeMap = getSkuAttributeMap(skuPOList);
        for (SkuPO skuPO : skuPOList){
            Set<Long> skuTableSet = skuCertificateMap.getOrDefault(skuPO.getSkuId(), new ArrayList<>()).stream().map(SkuCertificatePO::getCertificateId).collect(Collectors.toSet());
            // 获取全球资质信息
            checkSkuQualification(propertyMap,skuGlobalQualificationVOList,skuTableSet, checkType);
            // 获取跨境属性信息
            checkSkuGroupProperty(propertyMap,skuGroupPropertyList, checkType,skuAttributeMap.get(skuPO.getSkuId()));
        }
    }
    @PFTracing
    private List<GroupPropertyVO> getSkuGroupProperty(SpuPO spuPO,Set<String> targetCountryCodes){
        List<GroupPropertyVO> skuGroupPropertyVOList = globalAttributeManageService.queryGroupPropertyVos(spuPO.getJdCatId(), spuPO.getSourceCountryCode(),targetCountryCodes,AttributeDimensionEnum.SKU.getCode(), null);
        log.info("SpuReadManageServiceImpl.checkGroupProperty skuGroupPropertyVOList={}",JSON.toJSONString(skuGroupPropertyVOList));
        if(CollectionUtils.isEmpty(skuGroupPropertyVOList)){
            return new ArrayList<>();
        }
        return skuGroupPropertyVOList;
    }
    @PFTracing
    private Map<Long,List<ProductGlobalAttributePO>> getSkuAttributeMap(List<SkuPO> skuPOList){
        if(CollectionUtils.isEmpty(skuPOList)){
            return null;
        }
        List<ProductGlobalAttributePO> attributePOS = productGlobalAttributeAtomicService.getBySkuIds(skuPOList.stream().map(SkuPO::getSkuId).collect(Collectors.toList()));
        Map<Long,List<ProductGlobalAttributePO>> result = new HashMap<>();
        if(CollectionUtils.isNotEmpty(attributePOS)){
            result.putAll(attributePOS.stream().collect(Collectors.groupingBy(ProductGlobalAttributePO::getKeyId)));
        }
        return result;
    }

    /**
     * 检查 SKU 组属性是否满足要求。
     * @param propertyMap 属性映射表，用于存储属性名称和是否必填的信息。
     * @param skuGroupPropertyList SKU 组属性列表。
     * @param checkType 检查类型。
     * @param skuAttributePOS SKU 对象。
     */
    @PFTracing
    private void checkSkuGroupProperty(Map<String,Boolean> propertyMap,List<GroupPropertyVO> skuGroupPropertyList,Integer checkType,List<ProductGlobalAttributePO> skuAttributePOS){
        Map<Long, String> crossBorderAttributeMap = parseSpuInterAttribute(skuAttributePOS);
        for(GroupPropertyVO groupPropertyVO : skuGroupPropertyList){
            if(!groupPropertyVO.getRequirement().equals(checkType)){
                continue;
            }
            List<PropertyVO> propertyVOList = groupPropertyVO.getPropertyVOS();
            for (PropertyVO propertyVO : propertyVOList){
                if(propertyVO.getRequired()){
                    if(!crossBorderAttributeMap.containsKey(propertyVO.getAttributeId())){
                        propertyMap.put(propertyVO.getAttributeName(),true);
                    }else{
                        propertyMap.put(propertyVO.getAttributeName(),false);
                    }
                    log.info("SpuReadManageServiceImpl.checkGroupProperty propertyMap={}",JSON.toJSONString(propertyMap));
                }
            }
        }
    }

    /**
     * 检查 SKU 是否满足全球性质条件。
     * @param propertyMap 属性映射表，用于存储检查结果。
     * @param globalQualificationVOList 全球性质条件列表。
     * @param skuTableSet SKU 表集合，用于判断 SKU 是否满足条件。
     * @param checkType 检查类型，用于过滤不需要检查的条件。
     */
    @PFTracing
    private void checkSkuQualification(Map<String,Boolean> propertyMap,List<GlobalQualificationVO> globalQualificationVOList,Set<Long> skuTableSet,Integer checkType){
        for (GlobalQualificationVO globalQualificationVO : globalQualificationVOList){
            // 如果是非必填，跳过
            if(globalQualificationVO.getRequirement().equals(CheckTypeEnum.NO_REQUIRED.getCode().toString())){
                continue;
            }
            // 校验时机
            if(!globalQualificationVO.getCheckType().equals(checkType)){
                continue;
            }
            Map<String, String> langQualificationNameMap = globalQualificationVO.getGlobalQualificationLangVOList().stream().collect(
                Collectors.toMap(GlobalQualificationLangVO::getLang,GlobalQualificationLangVO::getQualificationName));
            if(!skuTableSet.contains(globalQualificationVO.getId())){
                propertyMap.put(langQualificationNameMap.get(LangConstant.LANG_ZH),true);
                log.info("SpuReadManageServiceImpl.checkQualification skuTableSet1={}, propertyMap={}",JSON.toJSONString(skuTableSet),JSON.toJSONString(propertyMap));
            }else {
                propertyMap.put(langQualificationNameMap.get(LangConstant.LANG_ZH),false);
            }
            log.info("SpuReadManageServiceImpl.checkQualification skuTableSet2={}, propertyMap={}",JSON.toJSONString(skuTableSet),JSON.toJSONString(propertyMap));
        }
    }

    /**
     * 根据SPU信息和目标国家代码集合获取SKU的全球资质信息。
     * @param spuPO SPU信息对象
     * @param targetCountryCodes 目标国家代码集合
     * @return SKU的全球资质信息列表
     */
    @PFTracing
    private List<GlobalQualificationVO> getSkuQualification(SpuPO spuPO,Set<String> targetCountryCodes){
        List<GlobalQualificationVO> skuGlobalQualificationVOList = globalQualificationManageService.queryByCountry(spuPO.getJdCatId(), spuPO.getSourceCountryCode(), targetCountryCodes, AttributeDimensionEnum.SKU.getCode(), null);
        log.info("SpuReadManageServiceImpl.checkQualification skuGlobalQualificationVOList={}",JSON.toJSONString(skuGlobalQualificationVOList));
        if(CollectionUtils.isEmpty(skuGlobalQualificationVOList)){
            return new ArrayList<>();
        }
        return skuGlobalQualificationVOList;
    }

    /**
     * 检查商品标题是否为空，并将结果存入propertyMap中。
     * @param propertyMap 存储检查结果的Map
     * @param spuPO 商品基本信息PO
     * @param spuIdLangMap 商品语言信息Map
     */
    private void checkLangTitle(Map<String,Boolean> propertyMap,SpuPO spuPO, Map<String, SpuLangPO> spuIdLangMap){
        String attributeScope = spuPO.getAttributeScope();
        String[] targetCountryCode = attributeScope.split(Constant.COMMA);
        List<String> targetCountryCodeList =  Arrays.asList(targetCountryCode);
        for (String countryCode : targetCountryCodeList){
            String lang = countryManageService.getLangByCountryCode(countryCode);
            String langName = langManageService.getLangNameByLangCode(lang,LangConstant.LANG_ZH);
            SpuLangPO spuLangPO = spuIdLangMap.getOrDefault(spuPO.getSpuId() + lang, new SpuLangPO());
            if(StringUtils.isBlank(spuLangPO.getSpuTitle())){
                propertyMap.put(langName+"商品标题",true);
            }else {
                propertyMap.put(langName+"商品标题",false);
            }
        }
    }

    @PFTracing
    private void checkGroupProperty(Map<String,Boolean> propertyMap,SpuPO spuPO,Set<String> targetCountryCodes,Integer checkType,List<ProductGlobalAttributePO> ProductGlobalAttributePOS){
        List<GroupPropertyVO> spuGroupPropertyVOList = globalAttributeManageService.queryGroupPropertyVos(spuPO.getJdCatId(), spuPO.getSourceCountryCode(),targetCountryCodes,AttributeDimensionEnum.SPU.getCode(), null);
        log.info("SpuReadManageServiceImpl.checkGroupProperty spuGroupPropertyVOList={}",JSON.toJSONString(spuGroupPropertyVOList));
        if(CollectionUtils.isEmpty(spuGroupPropertyVOList)){
            return;
        }
        Map<Long, String> crossBorderAttributeMap = parseSpuInterAttribute(ProductGlobalAttributePOS);
        for(GroupPropertyVO groupPropertyVO : spuGroupPropertyVOList){
            if(!groupPropertyVO.getRequirement().equals(checkType)){
                continue;
            }
            List<PropertyVO> propertyVOList = groupPropertyVO.getPropertyVOS();
            for (PropertyVO propertyVO : propertyVOList){
                if(propertyVO.getRequired()){
                    if(!crossBorderAttributeMap.containsKey(propertyVO.getAttributeId())){
                        propertyMap.put(propertyVO.getAttributeName(),true);
                    }else{
                        propertyMap.put(propertyVO.getAttributeName(),false);
                    }
                    log.info("SpuReadManageServiceImpl.checkGroupProperty propertyMap={}",JSON.toJSONString(propertyMap));
                }
            }
        }
    }

    /**
     * 检查SPU的资格
     * @param propertyMap 存储资格检查结果的映射
     * @param spuPO SPU对象，包含SPU的基本信息
     * @param targetCountryCodes 目标国家代码集合
     * @param spuTableSet SPU表集合，用于检查是否包含特定的资格
     */
    @PFTracing
    private void checkQualification(Map<String,Boolean> propertyMap,SpuPO spuPO,Set<String> targetCountryCodes,Set<Long> spuTableSet,Integer checkType){
        List<GlobalQualificationVO> spuGlobalQualificationVOList = globalQualificationManageService.queryByCountry(spuPO.getJdCatId(), spuPO.getSourceCountryCode(), targetCountryCodes, AttributeDimensionEnum.SPU.getCode(), null);
        log.info("SpuReadManageServiceImpl.checkQualification spuGlobalQualificationVOList={}",JSON.toJSONString(spuGlobalQualificationVOList));
        if(CollectionUtils.isEmpty(spuGlobalQualificationVOList)){
            return;
        }
        for (GlobalQualificationVO globalQualificationVO : spuGlobalQualificationVOList){
            // 如果是非必填，跳过
            if(globalQualificationVO.getRequirement().equals(CheckTypeEnum.NO_REQUIRED.getCode().toString())){
                continue;
            }
            // 校验时机
            if(!globalQualificationVO.getCheckType().equals(checkType)){
                continue;
            }
            Map<String, String> langQualificationNameMap = globalQualificationVO.getGlobalQualificationLangVOList().stream().collect(
                Collectors.toMap(GlobalQualificationLangVO::getLang,GlobalQualificationLangVO::getQualificationName));
            if(!spuTableSet.contains(globalQualificationVO.getId())){
                propertyMap.put(langQualificationNameMap.get(LangConstant.LANG_ZH),true);
                log.info("SpuReadManageServiceImpl.checkQualification spuTableSet1={}, propertyMap={}",JSON.toJSONString(spuTableSet),JSON.toJSONString(propertyMap));
            }else {
                propertyMap.put(langQualificationNameMap.get(LangConstant.LANG_ZH),false);
            }
            log.info("SpuReadManageServiceImpl.checkQualification spuTableSet2={}, propertyMap={}",JSON.toJSONString(spuTableSet),JSON.toJSONString(propertyMap));
        }
    }
    @PFTracing
    private Map<Long, String> parseSpuInterAttribute(List<ProductGlobalAttributePO> attributePOS) {
        Map<Long, String> map = new HashMap<>();
        if(CollectionUtils.isEmpty(attributePOS)){
            return map;
        }

        for(ProductGlobalAttributePO attributePO : attributePOS){
            if(Objects.nonNull(attributePO) && Objects.nonNull(attributePO.getAttributeId()) && Objects.nonNull(attributePO.getAttributeValueId())){
                map.put(attributePO.getAttributeId(),attributePO.getAttributeValueId().toString());
            }
        }
        return map;
    }

    /**
     * 填充商品修改扩展属性。
     * @param spuAmendVO 商品修改信息对象。
     * @param extAttribute 扩展属性字符串。
     */
    public void fillAmendExtAttribute(SpuAmendVO spuAmendVO, String extAttribute){
        if (StringUtils.isBlank(extAttribute)){
            return;
        }
        // 扩展属性映射
        Map<String, List<String>> extendAttributeStrMap = spuConvertService.splitExtendAttributeStr(extAttribute);
        // 扩展属性
        if (MapUtils.isNotEmpty(extendAttributeStrMap)) {
            // 商品功能 106334L
            if (extendAttributeStrMap.containsKey("106334")){
                spuAmendVO.setProductFunction(this.getAttributeValueStr(extendAttributeStrMap.get("106334")));
            }
            // 商品用途 106335L
            if (extendAttributeStrMap.containsKey("106335")){
                spuAmendVO.setProductUse(this.getAttributeValueStr(extendAttributeStrMap.get("106335")));
            }
            // 商品原理 106332L
            if (extendAttributeStrMap.containsKey("106332")){
                spuAmendVO.setProductTheory(this.getAttributeValueStr(extendAttributeStrMap.get("106332")));
            }
            // 商品材质或核心属性 106333L
            if (extendAttributeStrMap.containsKey("106333")){
                spuAmendVO.setProductMaterial(this.getAttributeValueStr(extendAttributeStrMap.get("106333")));
            }
        }
    }

    private String getAttributeValueStr(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        String valueStr = null;
        for (String value : list) {
            if (value.startsWith(LangConstant.LANG_ZH + Constant.FACTORIAL)){
                valueStr = value.replace(LangConstant.LANG_ZH + Constant.FACTORIAL, "");
                break;
            } else if (value.startsWith(LangConstant.LANG_EN + Constant.FACTORIAL)) {
                valueStr = value.replace(LangConstant.LANG_EN + Constant.FACTORIAL,"");
                break;
            } else if (value.startsWith(LangConstant.LANG_VN + Constant.FACTORIAL)) {
                valueStr = value.replace(LangConstant.LANG_VN + Constant.FACTORIAL,"");
                break;
            }else if (value.startsWith(LangConstant.LANG_TH + Constant.FACTORIAL)) {
                valueStr = value.replace(LangConstant.LANG_TH + Constant.FACTORIAL,"");
            }
        }
        return valueStr;
    }



    @Override
    public SpuDetailVO getSpuDetail(SpuDetailReqVO reqVO) {
        //  编辑商品先查草稿
        SaveSpuVO saveSpuVoFromDraft = spuDraftManageService.getSaveSpuVoFromDraftBySpuId(reqVO.getSpuId());

        if (Objects.nonNull(saveSpuVoFromDraft)
                && ((Objects.isNull(saveSpuVoFromDraft.getSpuVO().getAuditStatus()) || !SpuAuditStatusEnum.APPROVED.getCode().equals(saveSpuVoFromDraft.getSpuVO().getAuditStatus()))
                    || (!Objects.isNull(saveSpuVoFromDraft.getSpuVO().getTaxAuditStatus()) && !SpuTaxAuditStatusEnum.APPROVED.getCode().equals(saveSpuVoFromDraft.getSpuVO().getTaxAuditStatus())))
            ){
            if (StringUtils.isBlank(reqVO.getAttributeScope())) {
                reqVO.setAttributeScope(saveSpuVoFromDraft.getSpuVO().getAttributeScope());
            }
            reqVO.setIsExport(saveSpuVoFromDraft.getSpuVO().getIsExport());
            // 此处才是真正查询的地方
            return spuDraftManageService.getSpuDetailVoFromDraftInfo(reqVO);
        }
        // 此处才是真正查询的地方
        return this.getSpuDetailVOFromDb(reqVO);
    }

    @NotNull
    public SpuDetailVO getSpuDetailVOFromDb(SpuDetailReqVO reqVO) {
        SpuDetailVO spuDetailVO = new SpuDetailVO();
        // 查询spu基础信息
        SpuPO spuPo = spuAtomicService.getSpuPoBySpuId(reqVO.getSpuId());
        AssertValidation.isEmpty(spuPo, "", "商品ID不存在，请检查");
        SpuVO spuVO = SpuConvert.INSTANCE.po2vo(spuPo);
        spuVO.setSaleUnitStr(extendInfoService.saleUnit(spuVO.getSaleUnit(),LangConstant.LANG_ZH));
        spuDetailVO.setSpuVO(spuVO);
        if(StringUtils.isNotBlank(reqVO.getAttributeScope())){
            spuVO.setAttributeScope(reqVO.getAttributeScope());
        }
        if(Objects.nonNull(reqVO.getIsExport())){
            spuVO.setIsExport(reqVO.getIsExport());
        }
        // 补全国家信息
        spuConvertService.fillSpuCountry(spuDetailVO);
        // 补全语种信息
        spuConvertService.fillSpuLang(spuDetailVO);
        // 查询商品详描
        spuConvertService.fillSpuDesc(spuDetailVO);
        // 查询供应商
        spuConvertService.fillVendorName(spuDetailVO);
        // 查询类目名
        List<PropertyVO> skuExtendPropertyList = spuConvertService.fillCategoryInfo(spuDetailVO);
        // 销售属性
        //spuDetailVO.setSalePropertyList(skuSaleAttributeManageService.querySpuPropertyList(spuVO.getSpuId()));
        // 查询品牌名
        spuConvertService.fillBrandInfo(spuDetailVO);
        // 处理spu扩展属性
        spuConvertService.convertExtendAttributeAndFillSelectedForView(spuDetailVO);
        // 处理SPU跨境属性
        spuConvertService.convertSpuAttribute(spuDetailVO);
        // 查询sku信息、价格、库存、仓储
        spuConvertService.fillSkuVoList(spuDetailVO);
        // 处理sku扩展属性，sku扩展属性分组信息，注意: SkuVOList是在上一个fillSkuVoList中获取到的
        spuConvertService.handleSkuVOGroupExtAttribute(spuDetailVO.getSkuVOList(), skuExtendPropertyList);
//        skuSaleAttributeManageService.convertAndSetStoreSalePropertyList(spuDetailVO.getSkuVOList());
        // 查询spu资质信息
        spuConvertService.fillSpuCertificate(spuDetailVO);
        // 查询修订原因
        spuConvertService.fillSpuAmendReason(spuDetailVO);
        // 获取新版销售属性信息  ZHAOYAN_SALE_ATTR
        spuConvertService.fillSaleAttributes(spuDetailVO);

        // 对sku根据销售属性进行排序
        spuConvertService.sortSkuList(spuDetailVO);

        return spuDetailVO;
    }


    /**
     * 根据商品准备请求获取审核状态数量。
     * @param spuPrepareReqVO 商品准备请求对象，包含源国家代码等信息。
     * @return 审核状态数量列表。
     */
    @Override
    public List<AuditNumVO> auditStatusNum(SpuPrepareReqVO spuPrepareReqVO) {
        if (CountryConstant.COUNTRY_ZH.equals(spuPrepareReqVO.getSourceCountryCode())) {
            return crossBorderAuditStatusNum(spuPrepareReqVO);
        }
        return otherAuditStatusNum(spuPrepareReqVO);
    }


    /**
     * 获取其他审核状态的数量信息。
     * @param spuPrepareReqVO 商品准备请求对象，包含商品的基本信息和审核状态。
     * @return 其他审核状态的数量信息列表。
     */
    private List<AuditNumVO> otherAuditStatusNum(SpuPrepareReqVO spuPrepareReqVO) {
        List<AuditNumVO> auditNumVOResp = new ArrayList<>();
        int onSaleStatus = SpuStatusEnum.ON_SALE.getStatus();
        Integer approvedStatusCode = SpuAuditStatusEnum.APPROVED.getCode();
        Integer taxApproveStatusCode = null;
        if(CountryConstant.COUNTRY_BR.equals(spuPrepareReqVO.getSourceCountryCode())){
            taxApproveStatusCode = SpuAuditStatusEnum.APPROVED.getCode();
        }
        AuditNumVO auditApproveNumVo = getAuditNumVo(onSaleStatus, approvedStatusCode, taxApproveStatusCode, spuPrepareReqVO);
        auditApproveNumVo.setTypeKey("approved");
        auditNumVOResp.add(auditApproveNumVo);


        Integer rejectStatusCode = SpuAuditStatusEnum.REJECTED.getCode();
        AuditNumVO auditRejectNumVo = getDraftAuditNumVo(null, rejectStatusCode, spuPrepareReqVO);
        auditRejectNumVo.setTypeKey("reject");
        auditNumVOResp.add(auditRejectNumVo);

        for (int i = 0; i < 3; i++) {
            AuditNumVO numVo = getDraftAuditNumVo(i, SpuAuditStatusEnum.WAITING_APPROVED.getCode(), spuPrepareReqVO);
            numVo.setTypeKey("waiting" + i);
            auditNumVOResp.add(numVo);
        }

        AuditNumVO numDraftVo = getDraftAuditNumVo(null, SpuAuditStatusEnum.DRAFT.getCode(), spuPrepareReqVO);
        numDraftVo.setTypeKey("draft");
        auditNumVOResp.add(numDraftVo);

        if (CountryConstant.COUNTRY_VN.equals(spuPrepareReqVO.getSourceCountryCode())
                || CountryConstant.COUNTRY_BR.equals(spuPrepareReqVO.getSourceCountryCode())){
            AuditNumVO numTaxAuditVo = getDraftTaxAuditNumVo(null, SpuAuditStatusEnum.WAITING_APPROVED.getCode(), spuPrepareReqVO);
            numTaxAuditVo.setTypeKey("taxAudit");
            auditNumVOResp.add(numTaxAuditVo);
        }
        return auditNumVOResp;
    }

    private AuditNumVO getDraftAuditNumVo(Integer level, Integer auditStatus, SpuPrepareReqVO spuPrepareReqVO) {
        SpuQueryReqVO reqVo = new SpuQueryReqVO();
        if (auditStatus != null) {
            reqVo.setAuditStatus(auditStatus);
        }
        if (level != null) {
            reqVo.setLevel(level);
        }
        reqVo.setSourceCountryCode(spuPrepareReqVO.getSourceCountryCode());
        if(Objects.nonNull(spuPrepareReqVO.getVendorCode())){
            reqVo.setVendorCode(spuPrepareReqVO.getVendorCode());
        }
        long total = spuDraftManageService.getTotal(reqVo);
        return AuditNumVO.builder()
            .auditStatus(auditStatus)
            .level(level)
            .num(total)
            .build();
    }

    private AuditNumVO getDraftTaxAuditNumVo(Integer level, Integer taxAuditStatus, SpuPrepareReqVO spuPrepareReqVO) {
        SpuQueryReqVO reqVo = new SpuQueryReqVO();
        if (taxAuditStatus != null) {
            reqVo.setTaxAuditStatus(taxAuditStatus);
        }
        if (level != null) {
            reqVo.setLevel(level);
        }
        reqVo.setSourceCountryCode(spuPrepareReqVO.getSourceCountryCode());
        if(Objects.nonNull(spuPrepareReqVO.getVendorCode())){
            reqVo.setVendorCode(spuPrepareReqVO.getVendorCode());
        }
        long total = spuDraftManageService.getTotal(reqVo);
        return AuditNumVO.builder()
                .taxAuditStatus(taxAuditStatus)
                .level(level)
                .num(total)
                .build();
    }

    @NotNull
    private List<AuditNumVO> crossBorderAuditStatusNum(SpuPrepareReqVO spuPrepareReqVO) {
        List<AuditNumVO> auditNumVOResp = new ArrayList<>();
        int onSaleStatus = SpuStatusEnum.ON_SALE.getStatus();
        Integer approvedStatusCode = SpuAuditStatusEnum.APPROVED.getCode();
        AuditNumVO auditNumVo = getAuditNumVo(onSaleStatus, approvedStatusCode,null, spuPrepareReqVO);
        auditNumVo.setTypeKey("approved");
        auditNumVOResp.add(auditNumVo);

        Integer auditRejectStatusCode = SpuAuditStatusEnum.REJECTED.getCode();
        AuditNumVO numRejectVo = getAuditNumVo(null, auditRejectStatusCode,null, spuPrepareReqVO);
        numRejectVo.setTypeKey("reject");
        auditNumVOResp.add(numRejectVo);

        Integer auditWaitingStatusCode = SpuAuditStatusEnum.WAITING_APPROVED.getCode();
        AuditNumVO numWaitingVo = getAuditNumVo(null, auditWaitingStatusCode,null, spuPrepareReqVO);
        numWaitingVo.setTypeKey("waiting");
        auditNumVOResp.add(numWaitingVo);

        Integer auditVendorEditStatusCode = SpuAuditStatusEnum.WAITING_VENDOR_MAINTAIN.getCode();
        AuditNumVO numVendorEditVo = getAuditNumVo(null, auditVendorEditStatusCode,null, spuPrepareReqVO);
        numVendorEditVo.setTypeKey("vendorMaintain");
        auditNumVOResp.add(numVendorEditVo);

        Integer auditVendorSubmitStatusCode = SpuAuditStatusEnum.WAITING_APPROVED_VENDOR_SUBMIT.getCode();
        AuditNumVO numVendorSubmitVo = getAuditNumVo(null, auditVendorSubmitStatusCode,null, spuPrepareReqVO);
        numVendorSubmitVo.setTypeKey("vendorSubmit");
        auditNumVOResp.add(numVendorSubmitVo);

        Integer auditVendorSubmitRejectStatusCode = SpuAuditStatusEnum.REJECTED_VENDOR_SUBMIT.getCode();
        AuditNumVO numVendorSubmitRejectVo = getAuditNumVo(null, auditVendorSubmitRejectStatusCode,null, spuPrepareReqVO);
        numVendorSubmitRejectVo.setTypeKey("vendorSubmitReject");
        auditNumVOResp.add(numVendorSubmitRejectVo);

        return auditNumVOResp;
    }

    private AuditNumVO getAuditNumVo(Integer spuStatus, Integer auditStatus,Integer taxAuditStatus, SpuPrepareReqVO spuPrepareReqVO) {
        SpuQueryReqVO reqVo = new SpuQueryReqVO();
        reqVo.setAuditStatus(auditStatus);
        if (spuStatus != null) {
            reqVo.setSpuStatus(spuStatus);
        }
        if (taxAuditStatus != null) {
            reqVo.setTaxAuditStatus(taxAuditStatus);
        }
        reqVo.setSourceCountryCode(spuPrepareReqVO.getSourceCountryCode());
        if(Objects.nonNull(spuPrepareReqVO.getVendorCode())){
            reqVo.setVendorCode(spuPrepareReqVO.getVendorCode());
        }
        long total = CountryConstant.COUNTRY_ZH.equals(spuPrepareReqVO.getSourceCountryCode())
            ? spuAtomicService.getTotal(reqVo)
            : spuDraftManageService.getTotal(reqVo);
        return AuditNumVO.builder()
            .auditStatus(auditStatus)
            .spuStatus(spuStatus)
            .taxAuditStatus(taxAuditStatus)
            .num(total)
            .build();
    }

    /**
     * 根据给定的 SKU ID 列表生成 StockManageReqDTO 对象。
     * @param skuIdList SKU ID 列表
     * @return StockManageReqDTO 对象
     */
    private StockManageReqDTO getStockManageReqDTO( List<Long> skuIdList){
        StockManageReqDTO stockManageReqDTO = new StockManageReqDTO();
        List<StockItemManageReqDTO> stockItem = new ArrayList<>();
        for (Long id : skuIdList){
            StockItemManageReqDTO stockItemManageReqDTO = new StockItemManageReqDTO();
            stockItemManageReqDTO.setSkuId(id);
            stockItemManageReqDTO.setNum(1L);
            stockItem.add(stockItemManageReqDTO);
        }
        stockManageReqDTO.setStockItem(stockItem);
        return stockManageReqDTO;
    }

    @Override
    public Map<Long, SpuPO> listSpuPo(Set<Long> spuIds) {
        if (CollectionUtils.isEmpty(spuIds)) {
            return Collections.emptyMap();
        }

        List<SpuPO> spuPOList = spuAtomicService.querySpuList(Lists.newArrayList(spuIds));

        return Optional.ofNullable(spuPOList).orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(SpuPO::getSpuId, Function.identity()));
    }

    @Override
    public Map<Long, Map<String, SpuLangPO>> querySpuLangMap(Set<Long> spuIds, Set<String> langSet) {
        if (CollectionUtils.isEmpty(spuIds)) {
            return Collections.emptyMap();
        }
        if (CollectionUtils.isEmpty(langSet)) {
            langSet = Sets.newHashSet(LangConstant.LANG_ZH, LangConstant.LANG_EN);
        }
        // 批量查询spuId的多语言名称
        List<SpuLangPO> spuLangPOList = spuLangAtomicService.listLang(spuIds, Lists.newArrayList(langSet));

        return Optional.ofNullable(spuLangPOList).orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(SpuLangPO::getSpuId,
                        Collectors.toMap(SpuLangPO::getLang,Function.identity() ,(o1, o2) -> o1)));
    }

    @Override
    public DataResponse<Boolean> verifySupplierSpuId(SupplierSpuIdVerifyDTO verifyDTO) {
        // SPU主表是否存在
        boolean spuPoExist = spuAtomicService.existSupplierSpuId(verifyDTO.getSupplierSpuId(), verifyDTO.getSupplierCode(),verifyDTO.getSpuId());
        if (spuPoExist) {
            return DataResponse.success(Boolean.TRUE);
        }
        // 判断草稿中是否存在
        return DataResponse.success(spuDraftManageService.existSupplierSpuId(verifyDTO.getSupplierSpuId(), verifyDTO.getSupplierCode(), verifyDTO.getSpuId()));
    }
}
