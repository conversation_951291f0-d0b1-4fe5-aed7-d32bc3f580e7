package com.jdi.isc.product.soa.service.mapstruct;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.CategoryAttrInputTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.CategoryAttrTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.AttributeCheckTypeEnum;
import com.jdi.isc.product.soa.common.enums.AttributeDimensionEnum;
import com.jdi.isc.product.soa.common.enums.SaleAttributeTypeEnum;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.SystemContextHolder;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeFlatVO;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeValueFlatVO;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeValueLangVO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryComboBoxVO;
import com.jdi.isc.product.soa.domain.category.biz.GlobalQualificationVO;
import com.jdi.isc.product.soa.domain.common.biz.BaseLangVO;
import com.jdi.isc.product.soa.domain.enums.CategoryLevelEnum;
import com.jdi.isc.product.soa.domain.enums.RequiredEnum;
import com.jdi.isc.product.soa.domain.enums.taxRate.TaxTypeRelationEnum;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SpuSaleAttributeVO;
import com.jdi.isc.product.soa.domain.sku.biz.GroupSkuCertificateVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCertificateVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuStockRelationVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.ProductGlobalAttributePO;
import com.jdi.isc.product.soa.domain.spu.po.SpuCertificatePO;
import com.jdi.isc.product.soa.domain.spu.po.SpuDescLangPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuLangPO;
import com.jdi.isc.product.soa.domain.stock.po.StockPO;
import com.jdi.isc.product.soa.domain.stockThreshold.biz.SkuStockThresholdVO;
import com.jdi.isc.product.soa.domain.stockThreshold.po.SkuStockThresholdPO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.BrSkuTaxPO;
import com.jdi.isc.product.soa.domain.vendor.biz.VendorVO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehousePO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehouseSkuPO;
import com.jdi.isc.product.soa.service.atomic.category.GlobalCountryCategoryAttributeRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuFeatureAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.*;
import com.jdi.isc.product.soa.service.atomic.stock.StockAtomicService;
import com.jdi.isc.product.soa.service.atomic.stockThreshold.SkuStockThresholdAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.SupplierAllInfoAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.countryTax.BrSkuTaxAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseSkuAtomicService;
import com.jdi.isc.product.soa.service.manage.attribute.AttributeOutService;
import com.jdi.isc.product.soa.service.manage.brand.BrandOutService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.category.GlobalAttributeManageService;
import com.jdi.isc.product.soa.service.manage.category.GlobalQualificationManageService;
import com.jdi.isc.product.soa.service.manage.certificate.CertificateManageService;
import com.jdi.isc.product.soa.service.manage.country.CountryLangManageService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuWriteManageService;
import com.jdi.isc.product.soa.service.manage.spu.ExtendInfoService;
import com.jdi.isc.product.soa.service.manage.stock.StockManageService;
import com.jdi.isc.product.soa.service.manage.vendor.VendorManageService;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import titan.profiler.shade.com.google.common.collect.Sets;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.Constant.*;


/**
 * <AUTHOR>
 * @date 2023/11/29
 **/
@Slf4j
@Service
public class SpuConvertService {

    @Resource
    private CategoryOutService categoryOutService;

    @Resource
    private AttributeOutService attributeOutService;

    @Resource
    private SkuReadManageService skuReadManageService;

    @Resource
    private SpuDescLangAtomicService spuDescLangAtomicService;

    @Resource
    private SpuLangAtomicService spuLangAtomicService;

    @Resource
    private SpuCertificateAtomicService spuCertificateAtomicService;

    @Resource
    private CertificateManageService certificateManageService;

    @Resource
    @Lazy
    private SkuConvertService skuConvertService;

    @Resource
    private VendorManageService vendorManageService;

    @Resource
    private BrandOutService brandOutService;

    @Resource
    private SupplierAllInfoAtomicService supplierAllInfoAtomicService;

    @Resource
    private SpuAuditRecordAtomicService spuAuditRecordAtomicService;

    @Resource
    private GlobalCountryCategoryAttributeRelationAtomicService countryCategoryAttributeRelationAtomicService;

    @Resource
    private GlobalAttributeManageService globalAttributeManageService;

    @Resource
    private GlobalQualificationManageService globalQualificationManageService;

    @Resource
    private SkuFeatureAtomicService skuFeatureAtomicService;

    @Resource
    private CountryManageService countryManageService;

    @Resource
    private CountryLangManageService countryLangManageService;

    @Resource
    private LangManageService langManageService;

    @Resource
    private ExtendInfoService extendInfoService;

    @Resource
    private WarehouseAtomicService warehouseAtomicService;

    @Resource
    private WarehouseSkuAtomicService warehouseSkuAtomicService;

    @Resource
    private StockManageService stockManageService;

    @Resource
    private StockAtomicService stockAtomicService;

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @Resource
    private ProductAttributeConvertService productAttributeConvertService;

    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;

    @Resource
    private SkuStockThresholdAtomicService skuStockThresholdAtomicService;

    @Resource
    private SaleAttributeManageService saleAttributeManageService;

//    @Deprecated // 废弃, 使用 getGroupExtAttribute() 方法
//    public String getExtAttribute(List<PropertyVO> extendPropertyVoList) {
//        if (CollectionUtils.isEmpty(extendPropertyVoList)) {
//            return "";
//        }
//
//        Map<Long, List<PropertyValueVO>> extendPropertyMap = Maps.newLinkedHashMap();
//        // 属性和属性值映射处理、并过滤掉属性值为空的属性
//        for (PropertyVO propertyVO : extendPropertyVoList) {
//            List<PropertyValueVO> propertyValueVOList =  propertyVO.getPropertyValueVOList()
//                .stream()
//                .filter(Objects::nonNull) // 过滤掉 null 元素
//                .collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(propertyValueVOList)) {
//                extendPropertyMap.put(propertyVO.getAttributeId(), propertyValueVOList);
//            }
//        }
//
//        if (MapUtils.isEmpty(extendPropertyMap)) {
//            return "";
//        }
//
//        StringBuilder extendPropertyStr = new StringBuilder();
//        for (Map.Entry<Long, List<PropertyValueVO>> entry : extendPropertyMap.entrySet()) {
//            extendPropertyStr.append(Constant.HASHTAG);
//            if (CollectionUtils.isEmpty(entry.getValue())) {
//                continue;
//            }
//            // 拼接扩展属性值
//            for (PropertyValueVO valueVO : entry.getValue()) {
//                if (StringUtils.isNotBlank(valueVO.getLang()) && StringUtils.isNotBlank(valueVO.getAttributeValueName())) {
//                    // #属性id3:CN_属性值3ID1,属性id3:EN_属性值3ID2
//                    extendPropertyStr.append(entry.getKey()).append(Constant.COLON).append(valueVO.getLang()).append(FACTORIAL).append(valueVO.getAttributeValueName()).append(COMMA);
//                } else {
//                    // 属性id1：属性值1ID1#属性id2:属性值2id1,属性id2:属性值2id2
//                    extendPropertyStr.append(entry.getKey()).append(Constant.COLON).append(valueVO.getAttributeValueId()).append(COMMA);
//                }`
//            }`
//            extendPropertyStr.deleteCharAt(extendPropertyStr.length() - 1);
//        }
//        return extendPropertyStr.substring(1);
//    }

    /**
     * 将属性列表转换为json格式的字符串，按照comgroupid分组
     * @param extendPropertyVoList 属性值对象列表，包含属性和对应的属性值信息
     * @return 格式化后的属性字符串，json格式
     */
    public String getGroupExtAttribute(List<PropertyVO> extendPropertyVoList) {
        if (CollectionUtils.isEmpty(extendPropertyVoList)) {
            return "";
        }

        // 属性和属性值映射处理、并过滤掉属性值为空的属性
        List<ExtendPropertyGroupVO> group =  ExtendPropertyGroupVO.getExtendPropertyGroupVOS(extendPropertyVoList);
        if (group == null || group.isEmpty()) {
            return "";
        } else{
            // 返回json格式字符串
            return ExtendPropertyGroupVO.serializeExtendPropertyGroups(group);
        }
    }

//    public String getSaleAttribute(List<SkuVO> skuVOList) {
//        Set<String> attributeIdSet = Sets.newLinkedHashSet();
//        List<PropertyValueVO> salePropertyList = skuVOList.get(0).getStoreSalePropertyList();
//        for (PropertyValueVO valueVO : salePropertyList) {
//            attributeIdSet.add(valueVO.getAttributeId().toString());
//        }
//        return String.join(COMMA, attributeIdSet);
//    }

    public void getSpuName(SpuVO spuVO){
        if(CollectionUtils.isEmpty(spuVO.getSpuLangExtendVOList())){
            return;
        }
        for(SpuLangExtendVO spuLangExtendVO : spuVO.getSpuLangExtendVOList()){
            if(StringUtils.equals(LangConstant.LANG_ZH,spuLangExtendVO.getLang())){
                spuVO.setSpuZhName(spuLangExtendVO.getSpuTitle());
            }
            if(StringUtils.equals(LangConstant.LANG_EN,spuLangExtendVO.getLang())){
                spuVO.setSpuEnName(spuLangExtendVO.getSpuTitle());
            }
            if(StringUtils.equals(LangConstant.LANG_TH,spuLangExtendVO.getLang())){
                spuVO.setSpuThName(spuLangExtendVO.getSpuTitle());
            }
            if(StringUtils.equals(LangConstant.LANG_VN,spuLangExtendVO.getLang())){
                spuVO.setSpuVnName(spuLangExtendVO.getSpuTitle());
            }
        }
    }

    /**
     * 将数据库存储的扩展属性转换为页面展示的结构
     *
     * @param detailVo
     */
    public void convertExtendAttributeAndFillSelectedForView(SpuDetailVO detailVo) {
        if (Objects.isNull(detailVo.getSpuVO()) || StringUtils.isBlank(detailVo.getSpuVO().getGroupExtAttribute()) || CollectionUtils.isEmpty(detailVo.getExtendPropertyList())) {
            return;
        }

        detailVo.setStoreExtendPropertyList(Lists.newArrayList());

        // 回填扩展属性选中模式
//        this.fillExtendAttributeSelected(detailVo.getExtendPropertyList(), this.splitExtendAttributeStr(detailVo.getSpuVO().getExtAttribute()));
        // 将spu级别的全量类目下扩展属性extendPropertyList回填选中状态，同时设置storeExtendPropertyList的值
        this.fillGroupExtendAttributeSelected(detailVo.getExtendPropertyList(),detailVo.getStoreExtendPropertyList(), ExtendPropertyGroupVO.obtainExtendPropertyGroupVOList(detailVo.getSpuVO().getGroupExtAttribute()));
    }

    private void fillGroupExtendAttributeSelected(List<PropertyVO> extendPropertyVOList, List<PropertyVO> storeExtendPropertyVOList, List<ExtendPropertyGroupVO> storeGroupExtendPropertyList) {
        if(CollectionUtils.isEmpty(extendPropertyVOList)){
            return;
        }
        for (PropertyVO propertyVO : extendPropertyVOList) {
            Long attributeId = propertyVO.getAttributeId();
            for(ExtendPropertyGroupVO extendPropertyGroupVO: storeGroupExtendPropertyList){
                for(ShotPropertyVO shotPropertyVO:extendPropertyGroupVO.getExtendPropertyList()){
                    if(attributeId != null && attributeId.equals(shotPropertyVO.getAttributeId())){
                        // 处理属性值, 返回选中的属性值列表
                        List<PropertyValueVO> selectedPropertyValueVOList = this.fillGroupExtendAttributeValueSelected(propertyVO.getPropertyValueVOList(), shotPropertyVO.getPropertyValueVOList());
                        // 为storeExtendPropertyList赋值, 这里面的值一定是有编辑了属性值的
                        // 深拷贝propertyVO和selectedPropertyValueVOList
                        // 添加try catch 处理异常，不能影响主流程
                        try {
                            PropertyVO newPropertyVO = JSON.parseObject(JSON.toJSONString(propertyVO), PropertyVO.class);
                            List<PropertyValueVO> newPropertyValueVOList = JSON.parseArray(JSON.toJSONString(selectedPropertyValueVOList), PropertyValueVO.class);
                            newPropertyVO.setPropertyValueVOList(newPropertyValueVOList);
                            storeExtendPropertyVOList.add(newPropertyVO);
                        } catch (Exception e) {
                            log.error("SpuConvertService.fillGroupExtendAttributeSelected 处理扩展属性值异常, propertyVO: {}, selectedPropertyValueVOList: {}", propertyVO, selectedPropertyValueVOList, e);
                        }
                    }
                }
            }
        }
    }

    /**
     * 返回选中的扩展属性值list
     *
     * @param propertyValueVOList
     * @param shotPropertyValueVOList
     * @return
     */
    private List<PropertyValueVO> fillGroupExtendAttributeValueSelected(List<PropertyValueVO> propertyValueVOList,  List<ShotPropertyValueVO> shotPropertyValueVOList) {
        List<PropertyValueVO> selectedPropertyValueVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(shotPropertyValueVOList)) {
            return selectedPropertyValueVOList;
        }
        // 处理文本属性
        for (PropertyValueVO valueVO : propertyValueVOList) {
            List<BaseLangVO> langList = new ArrayList<>();
            for(ShotPropertyValueVO shotPropertyValueVO:shotPropertyValueVOList) {
                // 处理文本属性
                if(StringUtils.isNotBlank(shotPropertyValueVO.getLang())){
                    BaseLangVO  baseLangVO = new BaseLangVO();
                    baseLangVO.setLang(shotPropertyValueVO.getLang());
                    baseLangVO.setLangName(shotPropertyValueVO.getAttributeValueName());
                    langList.add(baseLangVO);
                }
                if(StringUtils.isNotBlank(shotPropertyValueVO.getLang())){
                    // 文本属性
                    if(StringUtils.isNotBlank(valueVO.getLang()) && valueVO.getLang().equals(shotPropertyValueVO.getLang())){
                        valueVO.setSelected(Boolean.TRUE);
                        valueVO.setAttributeValueName(shotPropertyValueVO.getAttributeValueName());
                        // 深拷贝valueVO
                        try {
                            PropertyValueVO newPropertyValueVO = JSON.parseObject(JSON.toJSONString(valueVO), PropertyValueVO.class);
                            selectedPropertyValueVOList.add(newPropertyValueVO);
                        } catch (Exception e) {
                            log.error("SpuConvertService.fillGroupExtendAttributeValueSelected 处理文本属性值异常, valueVO: {}", valueVO, e);
                        }
                    }
                } else {
                    // 单选多选属性
                    if (valueVO.getAttributeValueId().equals(shotPropertyValueVO.getAttributeValueId())) {
                        valueVO.setSelected(Boolean.TRUE);
                        // 深拷贝valueVO
                        try {
                            PropertyValueVO newPropertyValueVO = JSON.parseObject(JSON.toJSONString(valueVO), PropertyValueVO.class);
                            selectedPropertyValueVOList.add(newPropertyValueVO);
                        } catch (Exception e) {
                            log.error("SpuConvertService.fillGroupExtendAttributeValueSelected 处理属性值异常, valueVO: {}", valueVO, e);
                        }
                    }
                }
            }
            // 文本属性值多语言赋值
            valueVO.setLangList(langList);
        }
        return selectedPropertyValueVOList;
    }

//    @Deprecated // 废弃，使用fillGroupExtendAttributeSelected
//    private void fillExtendAttributeSelected(List<PropertyVO> extendPropertyVOList, Map<String, List<String>> storeExtendMap) {
//        for (PropertyVO propertyVO : extendPropertyVOList) {
//            Long attributeId = propertyVO.getAttributeId();
//            if (!storeExtendMap.containsKey(String.valueOf(attributeId))) {
//                continue;
//            }
//            this.fillExtendAttributeValueSelected(propertyVO.getPropertyValueVOList(), storeExtendMap.get(String.valueOf(attributeId)));
//        }
//    }
//
//    /**
//     * 回填扩展属性用于界面展示
//     *
//     * @param propertyValueVOList 扩展属性值列表
//     * @param storeValueList      已存储扩展属性值列表
//     */
//    @Deprecated // 废弃，使用fillGroupExtendAttributeValueSelected
//    private void fillExtendAttributeValueSelected(List<PropertyValueVO> propertyValueVOList, List<String> storeValueList) {
//        for (PropertyValueVO valueVO : propertyValueVOList) {
//            if (CollectionUtils.isEmpty(storeValueList)) {
//                continue;
//            }
//            // 存储的扩展属性，包括当前属性Id或者属性值前缀为语言的，即为选中
//            if (storeValueList.contains(valueVO.getAttributeValueId().toString())) {
//                valueVO.setSelected(Boolean.TRUE);
//            }
//            if (StringUtils.isNotBlank(valueVO.getLang()) && storeValueList.stream().filter(Objects::nonNull).anyMatch(v -> v.startsWith(valueVO.getLang()))) {
//                // 截掉en_ 或 en^ 前缀
//                Optional<String> valueNameOptional = storeValueList.stream().filter(Objects::nonNull).filter(v -> v.startsWith(valueVO.getLang())).findFirst();
//                if (valueNameOptional.isPresent()) {
//                    String valueName = valueNameOptional.get();
//                    String substring = valueName.substring((valueVO.getLang() + FACTORIAL).length());
//                    valueVO.setAttributeValueName(substring);
//                    valueVO.setSelected(Boolean.TRUE);
//                }
//            }
//        }
//    }

    /**
     * 拆分扩展属性字符串
     * key是属性id，value是数值id或者文本类的多语言属性值list，每个属性值前面有语言前缀
     *
     * @param extAttribute 扩展属性字符串
     * @return 拆分后的扩展属性和属性值映射
     */
    public Map<String, List<String>> splitExtendAttributeStr(String extAttribute) {
        if (StringUtils.isBlank(extAttribute)) {
            return Maps.newHashMap();
        }
        Map<String, List<String>> storeExtendMap = Maps.newHashMap();
        String[] extAttributeArray = extAttribute.split(Constant.HASHTAG);
        for (String attribute : extAttributeArray) {
            String[] attributeAndValueArray = attribute.split(COMMA);
            for (String attributeValue : attributeAndValueArray) {
                if (StringUtils.isBlank(attributeValue)) {
                    continue;
                }
                String[] valueArray = attributeValue.split(Constant.COLON);
                String key = valueArray[0];
                if (storeExtendMap.containsKey(key)) {
                    storeExtendMap.get(key).add(valueArray[1]);
                } else {
                    storeExtendMap.put(valueArray[0], Lists.newArrayList(valueArray[1]));
                }
            }
        }
        return storeExtendMap;
    }

    public Map<String, List<String>> splitInterAttributeStr1(String extAttribute) {
        if(StringUtils.isBlank(extAttribute)){
            return new HashMap<>();
        }

        Map<String, List<String>> storeExtendMap = Maps.newHashMap();
        String[] extAttributeArray = extAttribute.split(Constant.HASHTAG);
        for (String attribute : extAttributeArray) {
            String[] attributeAndValueArray = attribute.split(COMMA);
            for (String attributeValue : attributeAndValueArray) {
                String[] valueArray = attributeValue.split(Constant.COLON);
                String key = valueArray[0];
                if (storeExtendMap.containsKey(key)) {
                    storeExtendMap.get(key).add(valueArray[1]);
                } else {
                    storeExtendMap.put(valueArray[0], Lists.newArrayList(valueArray[1]));
                }
            }
        }
        return storeExtendMap;
    }


    public void convertSpuAttribute(SpuDetailVO detailVO){
        Long spuId = detailVO.getSpuVO().getSpuId();
        List<ProductGlobalAttributePO> spuGlobalAttributePOS = productGlobalAttributeAtomicService.getBySpuId(spuId);
        detailVO.setSpuInterPropertyList(productAttributeConvertService.convertAttributeFromDB(spuGlobalAttributePOS,detailVO.getInterPropertyList()));
    }


    public void fillSaleAttributes(SpuDetailVO spuDetailVO) {
        try {
            String lang = StringUtils.isNotBlank(LangContextHolder.get()) ? LangContextHolder.get() : LangConstant.LANG_ZH;
            log.info("SpuConvertService.fillSaleAttributes 开始获取销售属性信息, spuId: {}, lang: {}", spuDetailVO.getSpuVO().getSpuId(), lang);
            SpuSaleAttributeVO spuSaleAttributeVO = saleAttributeManageService.getSpuSaleAttributeDetail(spuDetailVO.getSpuVO().getSpuId(), lang);
            if (spuSaleAttributeVO != null && CollectionUtils.isNotEmpty(spuSaleAttributeVO.getSpuSalePropertyList())) {
                spuDetailVO.setSalePropertyList(spuSaleAttributeVO.getSpuSalePropertyList());
                // 设置SKU销售属性
                if (CollectionUtils.isNotEmpty(spuDetailVO.getSkuVOList())) {
                    spuDetailVO.getSkuVOList().forEach(skuVO -> {
                        if (skuVO.getSkuId() != null && spuSaleAttributeVO.getSkuSalePropertyListMap() != null) {
                            skuVO.setStoreSalePropertyList(spuSaleAttributeVO.getSkuSalePropertyListMap().get(skuVO.getSkuId()));
                        } else if (StringUtils.isNotBlank(skuVO.getSkuKey()) && spuSaleAttributeVO.getDraftSkuSalePropertyListMap() != null) {
                            skuVO.setStoreSalePropertyList(spuSaleAttributeVO.getDraftSkuSalePropertyListMap().get(skuVO.getSkuKey()));
                        }
                    });
                }
                log.info("SpuConvertService.fillSaleAttributes 成功设置销售属性信息, spuId: {}, 销售属性数量: {}",
                        spuDetailVO.getSpuVO().getSpuId(), spuSaleAttributeVO.getSpuSalePropertyList().size());
            } else {
                log.info("SpuConvertService.fillSaleAttributes 未获取到销售属性信息, spuId: {}", spuDetailVO.getSpuVO().getSpuId());
            }
        } catch (Exception e) {
            log.error("SpuConvertService.fillSaleAttributes 获取销售属性信息异常, spuId: {}", spuDetailVO.getSpuVO().getSpuId(), e);
            throw e;
        }
    }

    /**
     * 将数据库存储的扩展属性转换为页面展示的结构
     *
     * @param detailVo
     */
    public void convertInterAttributeAndFillSelectedForView(SpuDetailVO detailVo) {
        if (Objects.isNull(detailVo.getSpuVO()) || CollectionUtils.isEmpty(detailVo.getInterPropertyList())) {
            log.info("SpuConvertService.convertInterAttributeAndFillSelectedForView param is null");
            return;
        }

        detailVo.setSpuInterPropertyList(Lists.newArrayList());

        // 回填跨境属性选中模式
        this.fillInterAttributeSelected(detailVo.getInterPropertyList(), detailVo.getSpuInterPropertyList(), this.splitInterAttributeStr(detailVo.getSpuVO().getSpuInterAttribute()));
    }

    public void fillInterAttributeSelected(List<GroupPropertyVO> interPropertyList, List<GroupPropertyVO> spuInterExtendPropertyList, Map<String, List<String>> storeExtendMap) {
        for(GroupPropertyVO groupPropertyVO : interPropertyList){
            for (PropertyVO propertyVO : groupPropertyVO.getPropertyVOS()) {
                Long attributeId = propertyVO.getAttributeId();
                if (!storeExtendMap.containsKey(String.valueOf(attributeId))) {
                    continue;
                }
                PropertyVO storePropertyVO = SpuConvert.INSTANCE.oldPropertyVo2NewVo(propertyVO);
                storePropertyVO.setPropertyValueVOList(Lists.newArrayList());
                this.fillInterAttributeValueSelected(propertyVO.getPropertyValueVOList(), storePropertyVO.getPropertyValueVOList(), storeExtendMap.get(String.valueOf(attributeId)));
            }
            spuInterExtendPropertyList.add(groupPropertyVO);
        }
    }

    /**
     * 回填扩展属性用于界面展示
     *
     * @param propertyValueVOList 扩展属性值列表
     * @param storeValueList      已存储扩展属性值列表
     */
    private void fillInterAttributeValueSelected(List<PropertyValueVO> propertyValueVOList, List<PropertyValueVO> storePropertyValueVOList, List<String> storeValueList) {
        for (PropertyValueVO valueVO : propertyValueVOList) {
            if (CollectionUtils.isEmpty(storeValueList)) {
                continue;
            }
            // 存储的扩展属性，包括当前属性Id或者属性值前缀为语言的，即为选中
            if (storeValueList.contains(valueVO.getAttributeValueId().toString())) {
                valueVO.setSelected(Boolean.TRUE);
            }
            if (StringUtils.isNotBlank(valueVO.getLang()) && storeValueList.stream().filter(Objects::nonNull).anyMatch(v -> v.startsWith(valueVO.getLang()))) {
                // 截掉en_ 或 en^ 前缀
                Optional<String> valueNameOptional = storeValueList.stream().filter(Objects::nonNull).filter(v -> v.startsWith(valueVO.getLang())).findFirst();
                if (valueNameOptional.isPresent()) {
                    String valueName = valueNameOptional.get();
                    String substring = valueName.substring((valueVO.getLang() + FACTORIAL).length());
                    valueVO.setAttributeValueName(substring);
                    valueVO.setSelected(Boolean.TRUE);
                }
            }
            storePropertyValueVOList.add(valueVO);
        }
    }

    /**
     * 拆分扩展属性字符串
     *
     * @param extAttribute 扩展属性字符串
     * @return 拆分后的扩展属性和属性值映射
     */
    public Map<String, List<String>> splitInterAttributeStr(String extAttribute) {
        if(StringUtils.isBlank(extAttribute)){
            return new HashMap<>();
        }

        Map<String, List<String>> storeExtendMap = Maps.newHashMap();
        String[] extAttributeArray = extAttribute.split(Constant.HASHTAG);
        for (String attribute : extAttributeArray) {
            String[] attributeAndValueArray = attribute.split(COMMA);
            for (String attributeValue : attributeAndValueArray) {
                String[] valueArray = attributeValue.split(Constant.COLON);
                String key = valueArray[0];
                if (storeExtendMap.containsKey(key)) {
                    storeExtendMap.get(key).add(valueArray[1]);
                } else {
                    storeExtendMap.put(valueArray[0], Lists.newArrayList(valueArray[1]));
                }
            }
        }
        return storeExtendMap;
    }

    /**
     * 商品详描转换
     *
     * @param saveSpuVO
     * @return
     */
    public List<SpuDescLangPO> toDescList(SaveSpuVO saveSpuVO) {
        final long spuId = saveSpuVO.getSpuVO().getSpuId();

        Map<String, String> pcDescMap = saveSpuVO.getPcDescriptionMap();
        Map<String, String> appDescMap = saveSpuVO.getAppDescriptionMap();
        List<SpuDescLangPO> spuDescLangPoList = Lists.newArrayList();

        if (MapUtils.isEmpty(pcDescMap) && MapUtils.isEmpty(appDescMap)) {
            return spuDescLangPoList;
        }

        List<SpuDescLangPO> descLangPOList = spuDescLangAtomicService.listDescLang(spuId);
        Map<String, SpuDescLangPO> descLangPoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(descLangPOList)) {
            descLangPoMap = descLangPOList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(SpuDescLangPO::getLang, Function.identity(),BinaryOperator.maxBy(Comparator.comparing(SpuDescLangPO::getCreateTime))));
        }
        List<String> langList = langManageService.getLangCodeList();
        for (String key : langList) {
            SpuDescLangPO descLangPo = new SpuDescLangPO();
            descLangPo.setSpuId((MapUtils.isNotEmpty(descLangPoMap) && Objects.nonNull(descLangPoMap.get(key)) && Objects.nonNull(descLangPoMap.get(key).getSpuId())) ? null : spuId);
            descLangPo.setLang(key);
            if(MapUtils.isNotEmpty(descLangPoMap) && descLangPoMap.get(key) != null){
                descLangPo.setId(descLangPoMap.get(key).getId());
            }
            descLangPo.setPcDescription(MapUtils.isNotEmpty(pcDescMap) ? pcDescMap.getOrDefault(key, "") : "");
            descLangPo.setAppDescription(MapUtils.isNotEmpty(appDescMap) ? appDescMap.getOrDefault(key, "") : "");
            spuDescLangPoList.add(descLangPo);
        }
        log.info("SpuConvertService.toDescList 保存商详 spuDescLangPoList={}", JSON.toJSONString(spuDescLangPoList));
        return spuDescLangPoList;
    }


    /**
     * 商品名多语言
     *
     * @param spuVO
     * @return
     */
    public List<SpuLangPO> convertVoToLangPo(SpuVO spuVO) {
        Map<String, SpuLangPO> langPoMap = spuLangAtomicService.langTitleMap(spuVO.getSpuId());
        Map<String, String> langSpuNameMap = getLangNameMap(spuVO);
        List<SpuLangPO> langPOList = convertLangPo(langSpuNameMap, langPoMap, this.langNameBiFunction(spuVO));

        // 多语言扩展信息
        this.fillSpuLangExtend(spuVO, langPOList);
        return langPOList;
    }

    private Map<String, String> getLangNameMap(SpuVO spuVO) {
        Map<String, String> langSpuNameMap = Maps.newHashMap();

        if (CollectionUtils.isNotEmpty(spuVO.getSpuLangExtendVOList())) {
            List<SpuLangExtendVO> spuLangExtendVOList = spuVO.getSpuLangExtendVOList();
            spuLangExtendVOList.forEach( spuLangExtendVO ->
                    langSpuNameMap.put(spuLangExtendVO.getLang()
                            ,StringUtils.isNotBlank(spuLangExtendVO.getSpuTitle())?spuLangExtendVO.getSpuTitle():""));
        }
        return langSpuNameMap;
    }

    private void fillSpuLangExtend(SpuVO spuVO, List<SpuLangPO> langPoList) {
        if (CollectionUtils.isEmpty(spuVO.getSpuLangExtendVOList()) || CollectionUtils.isEmpty(langPoList)) {
            return;
        }
        Map<String, SpuLangExtendVO> langExtendMap = spuVO.getSpuLangExtendVOList().stream().collect(Collectors.toMap(SpuLangExtendVO::getLang, Function.identity(), (o1, o2) -> o1));
        for (SpuLangPO langPo : langPoList) {
            if (langExtendMap.containsKey(langPo.getLang())) {
                SpuLangExtendVO extendVO = langExtendMap.get(langPo.getLang());
                langPo.setSpuTitle(StringUtils.isNotBlank(extendVO.getSpuTitle())?extendVO.getSpuTitle() : "");
                langPo.setQualifier(extendVO.getQualifier());
                langPo.setSpecification(extendVO.getSpecification());
                langPo.setKeyPhrases(extendVO.getKeyPhrases());
            }
        }
    }

    private BiFunction<Map<String, String>, Map<String, SpuLangPO>, List<SpuLangPO>> langNameBiFunction(SpuVO spuVO) {
        return (langMap, map) -> {
            List<SpuLangPO> poList = Lists.newArrayList();
            for (Map.Entry<String, String> entry : langMap.entrySet()) {
                String lang = entry.getKey();
                SpuLangPO langPO;
                if (MapUtils.isNotEmpty(map) && map.containsKey(lang)) {
                    langPO = map.get(lang);
                    langPO.setSpuTitle(entry.getValue());
                    langPO.setMainImg(spuVO.getMainImg());
                    langPO.setDetailImg(spuVO.getDetailImg());
                    // 更新时置为空，不然报错
                    langPO.setSpuId(null);
                } else {
                    langPO = this.toLangPo(spuVO, lang, langMap.get(lang));
                }
                poList.add(langPO);
            }
            return poList;
        };
    }

    public SpuLangPO toLangPo(SpuVO spuVO, String lang, String title) {
        SpuLangPO spuLangPo = new SpuLangPO();
        spuLangPo.setMainImg(spuVO.getMainImg());
        spuLangPo.setDetailImg(spuVO.getDetailImg());
        spuLangPo.setSpuId(spuVO.getSpuId());
        spuLangPo.setLang(lang);
        spuLangPo.setSpuTitle(title);
        return spuLangPo;
    }

    private List<SpuLangPO> convertLangPo(Map<String, String> langMap, Map<String, SpuLangPO> map, BiFunction<Map<String, String>, Map<String, SpuLangPO>, List<SpuLangPO>> biFunction) {
        return biFunction.apply(langMap, map);
    }

    /**
     * 补全国家信息
     *
     * @param detailVO 商品详情页对象
     */
    @PFTracing
    public void fillSpuCountry(SpuDetailVO detailVO) {
        SpuVO spuVO = detailVO.getSpuVO();
        String sourceCountryCode = spuVO.getSourceCountryCode();
        String attributeScope = spuVO.getAttributeScope();
        List<String> targetCountryCodesP = StringUtils.isNotBlank(attributeScope) ? Arrays.asList(attributeScope.split(COMMA)) : Lists.newArrayList();
        List<String> targetCountryCodes = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(targetCountryCodesP)){
            targetCountryCodes.addAll(targetCountryCodesP);
        }
        targetCountryCodes.add(sourceCountryCode);

        Map<String,String> countryPOMap = countryManageService.getCountryMap(null);
        spuVO.setSourceCountryName(countryPOMap.get(spuVO.getSourceCountryCode()));
        StringBuilder sBuilder = new StringBuilder();
        new HashSet<>(targetCountryCodes).forEach(item->{
            if(countryPOMap.get(item)!= null){
                sBuilder.append(countryPOMap.get(item));
                sBuilder.append(Constant.COMMA);
            }
        });
        String countryNames = sBuilder.toString();
        if (!countryNames.isEmpty()) {
            countryNames = countryNames.substring(0, countryNames.length() - 1);
        }
        spuVO.setAttributeScopeName(countryNames);
    }

    /**
     * 补全语种信息
     *
     * @param detailVO 商品详情页对象
     */
    @PFTracing
    public void fillSpuLang(SpuDetailVO detailVO) {
        List<SpuLangExtendVO> spuLangExtendVOList = detailVO.getSpuVO().getSpuLangExtendVOList();
        SpuVO spuVO = detailVO.getSpuVO();
        if(CollectionUtils.isEmpty(spuLangExtendVOList)){
            log.error("SpuConvertService.fillSpuLang spuLangExtendVOList is null");
            List<SpuLangPO> spuLangPOS = spuLangAtomicService.listLangTitle(spuVO.getSpuId());
            if(CollectionUtils.isEmpty(spuLangPOS)){
                log.error("SpuConvertService.fillSpuLang spuLangPOS is null");
                return;
            }
            // todo 发品 兼容老逻辑
            Map<String, String> langMap = spuLangPOS.stream().collect(Collectors.toMap(SpuLangPO::getLang, SpuLangPO::getSpuTitle, (key1, key2) -> key2));
            spuVO.setSpuZhName(langMap.get(LangConstant.LANG_ZH));
            spuVO.setSpuEnName(langMap.get(LangConstant.LANG_EN));
            spuVO.setSpuVnName(langMap.get(LangConstant.LANG_VN));
            spuVO.setSpuThName(langMap.get(LangConstant.LANG_TH));
            spuLangExtendVOList = SpuConvert.INSTANCE.listLangExtendPo2Vo(spuLangPOS);
        }
        Map<String,String> langPOMap = langManageService.getLangMap(LangContextHolder.get());

        for(SpuLangExtendVO langExtendVO : spuLangExtendVOList){
            if(langPOMap.get(langExtendVO.getLang()) != null){
                langExtendVO.setLangName(langPOMap.get(langExtendVO.getLang()));
            }
        }
        spuVO.setSpuLangExtendVOList(spuLangExtendVOList);
    }

    /**
     * 补全类目信息
     *
     * @param detailVO 商品详情页对象
     */

    @PFTracing
    public List<PropertyVO> fillCategoryInfo(SpuDetailVO detailVO) {

        String lang = LangContextHolder.get();
        // 根据末级查所有父级类目
        setCategoryIdAndName(detailVO, lang);

        // 查询扩展类目信息、查询销售属性信息
        SpuVO spuVO = detailVO.getSpuVO();
        Map<String, List<PropertyVO>> attributeMap = this.getExtendAndSaleAttributeMap(spuVO.getCatId(), spuVO.getAttributeScope(), lang, spuVO.getSourceCountryCode());
        // 设置sku和spu的扩展属性
        List<PropertyVO> skuExtendPropertyList = this.classifyExtendProperties(detailVO, attributeMap.get(CategoryAttrTypeEnum.EXTEND.name()));
        // 设置sku的扩展属性
        detailVO.setSalePropertyList(attributeMap.get(CategoryAttrTypeEnum.SELL.name()));
        Map<String,List<GroupPropertyVO>> interAttributeMap = this.getInterAttributeMap(spuVO.getCatId(), spuVO.getAttributeScope(), lang, spuVO.getSourceCountryCode(), spuVO.getIsExport());
        detailVO.setInterPropertyList(interAttributeMap.get(AttributeDimensionEnum.SPU.getDesc()));
        return skuExtendPropertyList;
    }

    /**
     * 根据属性级别将扩展属性列表分类并设置到SPU详情和SKU列表中
     * @param detailVO SPU详情对象，用于存储分类后的SPU级别扩展属性
     * @param extendPropertyList 待分类的扩展属性列表，包含SPU和SKU级别的属性
     */
    private List<PropertyVO> classifyExtendProperties(SpuDetailVO detailVO, List<PropertyVO> extendPropertyList) {
        if (CollectionUtils.isEmpty(extendPropertyList)) {
            return new ArrayList<>();
        }

        List<PropertyVO> spuExtendPropertyList = new ArrayList<>();
        List<PropertyVO> skuExtendPropertyList = new ArrayList<>();

        for (PropertyVO property : extendPropertyList) {
            if (property.getLevel() != null && property.getLevel() == PropertyVO.ONLY_SPU_LEVEL_EXT_ATTR_VALUE) {
                spuExtendPropertyList.add(property);
            } else {
                skuExtendPropertyList.add(property);
            }
        }
        // 设置spu的扩展属性，sku扩展属性单独处理
        detailVO.setExtendPropertyList(spuExtendPropertyList);
        if(Objects.nonNull(detailVO.getSpuVO())){
            long spuId = detailVO.getSpuVO().getSpuId();
            log.info("SpuConvertService.classifyExtendProperties spuId: {}, spuExtendPropertyList size: {}, skuExtendPropertyList size: {}", spuId, spuExtendPropertyList.size(), skuExtendPropertyList.size());
        }
        return skuExtendPropertyList;
    }


    @PFTracing
    private void setCategoryIdAndName(SpuDetailVO detailVO, String lang) {
        List<CategoryComboBoxVO> comboBoxVo = categoryOutService.queryPath(detailVO.getSpuVO().getCatId(), lang);
        if (CollectionUtils.isEmpty(comboBoxVo)) {
            return;
        }
        // 按级别补充信息
        for (CategoryComboBoxVO vo : comboBoxVo) {
            switch (CategoryLevelEnum.forCode(vo.getLevel())) {
                case ONE:
                    detailVO.setFirstCatId(vo.getId());
                    detailVO.setFirstCatName(vo.getName());
                    break;
                case TWO:
                    detailVO.setSecondCatId(vo.getId());
                    detailVO.setSecondCatName(vo.getName());
                    break;
                case THREE:
                    detailVO.setThirdCatId(vo.getId());
                    detailVO.setThirdCatName(vo.getName());
                    break;
                case FOUR:
                    detailVO.setLastCatId(vo.getId());
                    detailVO.setLastCatName(vo.getName());
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 补全品牌信息
     *
     * @param detailVO 商品详情页对象
     */
    @PFTracing
    public void fillBrandInfo(SpuDetailVO detailVO) {
        String lang = LangContextHolder.get();
        Long brandId = detailVO.getSpuVO().getBrandId();
        if (Objects.isNull(brandId)) {
            return;
        }
        // 品牌查询
        Map<Long, String> brandMap = brandOutService.queryNameByIds(Sets.newHashSet(brandId), lang);
        log.info("SpuConvertService.fillBrandInfo 品牌接口查询完成,brandId={},lang={},brandMap={}", brandId, lang, JSON.toJSONString(brandMap));

        detailVO.getSpuVO().setBrandName(MapUtils.isNotEmpty(brandMap) ? brandMap.get(brandId) : "");
        log.info("SpuConvertService.fillBrandInfo 品牌处理完成,brandId={},lang={}", brandId, lang);
    }

    /**
     * 根据末级类目ID和属性范围查询销售属性和扩展属性
     *
     * @param lastCatId      末级类目ID
     * @param attributeScope 属性范围
     * @return
     */
    @PFTracing
    public Map<String, List<PropertyVO>> getExtendAndSaleAttributeMap(Long lastCatId, String attributeScope, String lang, String sourceCountryCode) {
        Map<String, List<PropertyVO>> resultMap = Maps.newHashMap();
        // 查询扩展属性
        List<AttributeFlatVO> extendAttributeList = attributeOutService.queryExtAttrDetail(lastCatId, lang);

        if (CollectionUtils.isNotEmpty(extendAttributeList)) {
            List<PropertyVO> extendPropertyList =
                extendAttributeList.stream().filter(Objects::nonNull).map(flatVo -> getExtendPropertyVO(flatVo, sourceCountryCode,attributeScope)).sorted(Comparator.comparing(PropertyVO::getSort)).collect(Collectors.toList());
            resultMap.put(CategoryAttrTypeEnum.EXTEND.name(), extendPropertyList);
        }

        // 查询销售属性
        List<PropertyVO> saleAttributeList = attributeOutService.querySellAttrByCatId(lastCatId, lang);  // ZHAOYAN_SALE_ATTR

        if (CollectionUtils.isNotEmpty(saleAttributeList)) {
            resultMap.put(CategoryAttrTypeEnum.SELL.name(), saleAttributeList);
        }
        return resultMap;
    }

    public List<PropertyVO> getExtendAttribute(Long lastCatId, String attributeScope, String lang, String sourceCountryCode) {
        // 查询扩展属性
        List<AttributeFlatVO> extendAttributeList = attributeOutService.queryExtAttrDetail(lastCatId, lang);

        List<PropertyVO> extendPropertyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(extendAttributeList)) {
            extendPropertyList =
                    extendAttributeList.stream().filter(Objects::nonNull).map(flatVo -> getExtendPropertyVO(flatVo, sourceCountryCode,attributeScope)).sorted(Comparator.comparing(PropertyVO::getSort)).collect(Collectors.toList());
        }
        return extendPropertyList;
    }

    public List<PropertyVO> getSaleAttribute(Long lastCatId, String lang) {
        // 查询销售属性
        List<AttributeFlatVO> saleAttributeList = attributeOutService.querySellAttrDetail(lastCatId, lang);

        List<PropertyVO> salePropertyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(saleAttributeList)) {
            salePropertyList = saleAttributeList.stream().filter(Objects::nonNull).map(this::getSalePropertyVO).sorted(Comparator.comparing(PropertyVO::getSort)).collect(Collectors.toList());
        }
        return salePropertyList;
    }


    private PropertyVO getSalePropertyVO(AttributeFlatVO extendFlatVo) {
        PropertyVO propertyVO = SpuConvert.INSTANCE.attributeFlatVo2PropertyVo(extendFlatVo);
        List<AttributeValueFlatVO> attributeValueFlatVOList = extendFlatVo.getAttributeValueList();

        List<PropertyValueVO> propertyValueVOList = Optional.ofNullable(attributeValueFlatVOList).orElseGet(ArrayList::new).stream().map(SpuConvert.INSTANCE::attributeFlatValueVo2PropertyValueVo).sorted(Comparator.comparing(PropertyValueVO::getSort)).collect(Collectors.toList());
        propertyVO.setPropertyValueVOList(propertyValueVOList);
        return propertyVO;
    }

    private PropertyVO getExtendPropertyVO(AttributeFlatVO extendFlatVo, String sourceCountryCode,String attributeScope) {
        PropertyVO propertyVO = SpuConvert.INSTANCE.attributeFlatVo2PropertyVo(extendFlatVo);
        List<AttributeValueFlatVO> attributeValueFlatVOList = extendFlatVo.getAttributeValueList();

        List<PropertyValueVO> propertyValueVOList = Lists.newArrayList();
        // 属性输入类型，需要返回多种语言的提示语
        Integer inputType = extendFlatVo.getAttributeInputType();
        if (CategoryAttrInputTypeEnum.TEXT.getCode().equals(inputType)) {
            for (AttributeValueFlatVO valueFlatVo : attributeValueFlatVOList) {
                propertyValueVOList = sortValueByLang(valueFlatVo, sourceCountryCode,attributeScope, extendFlatVo);
            }
        } else {
            propertyValueVOList = Optional.ofNullable(attributeValueFlatVOList).orElseGet(ArrayList::new).stream().map(SpuConvert.INSTANCE::attributeFlatValueVo2PropertyValueVo).sorted(Comparator.comparing(PropertyValueVO::getSort)).collect(Collectors.toList());
        }
        propertyVO.setPropertyValueVOList(propertyValueVOList);
        return propertyVO;
    }


    private List<PropertyValueVO> sortValueByLang(AttributeValueFlatVO valueFlatVo,String sourceCountryCode,
        String attributeScope, AttributeFlatVO attributeFlatVO) {
        List<PropertyValueVO> propertyValueVOList;
        List<AttributeValueLangVO> langList = valueFlatVo.getLangList();
        List<String> LANG_LIST = new ArrayList<>();
        if(StringUtils.isBlank(attributeScope)){
            LANG_LIST = countryLangManageService.getLangCodeByCountryCode(sourceCountryCode);
        } else {
            // 查询国际扩展属性
            List<String> countryCodes = StringUtils.isNotBlank(attributeScope) ?
                Arrays.asList(attributeScope.split(COMMA)) : Lists.newArrayList();
            Set<String> countryLangLists = new HashSet<>();
            for(String countryCode : countryCodes){
                List<String> langCodes = countryLangManageService.getLangCodeByCountryCode(countryCode);
                if(CollectionUtils.isNotEmpty(langCodes)){
                    countryLangLists.addAll(langCodes);
                }
            }
            if(CollectionUtils.isNotEmpty(countryLangLists)){
                LANG_LIST = new ArrayList<>(countryLangLists);
            }
        }

        if(CollectionUtils.isNotEmpty(langList)){
            List<String> finalLANG_LIST = LANG_LIST;
            langList = langList.stream().filter(item-> finalLANG_LIST.contains(item.getLang())).collect(Collectors.toList());
        }
        Set<String> langSet = langList.stream().filter(Objects::nonNull).map(AttributeValueLangVO::getLang).collect(Collectors.toSet());
        Map<String, Integer> sortLangMap = Maps.newHashMap();
        for (int i = 0; i < LANG_LIST.size(); i++) {
            String lang = LANG_LIST.get(i);
            sortLangMap.put(lang, i);
            // 多语言扩展属性值缺少时，补充缺失语种
            if (!langSet.contains(lang)) {
                AttributeValueLangVO valueLangVO = new AttributeValueLangVO();
                valueLangVO.setLang(lang);
                valueLangVO.setAttributeId(valueLangVO.getAttributeId());
                langList.add(valueLangVO);
            }
        }
        propertyValueVOList = langList.stream().filter(Objects::nonNull).map(langVo -> {
            PropertyValueVO valueVO = SpuConvert.INSTANCE.attributeFlatValueVo2PropertyValueVo(valueFlatVo);
            valueVO.setLang(langVo.getLang());
            valueVO.setLangName(langManageService.getLangNameByLangCode(langVo.getLang(),LangContextHolder.get()));
            // 文本类型扩展属性值默认给提示字段
            valueVO.setAttributeValueName(null);
            valueVO.setPlaceholderValue(langVo.getLangName());
            valueVO.setSort(sortLangMap.get(langVo.getLang()) != null?sortLangMap.get(langVo.getLang()):0);
            // 区分跨境和本土
            if (RequiredEnum.REQUIRED.getCode().equals(attributeFlatVO.getRequired())) {
                valueVO.setRequired(this.requiredByCountryLang(sourceCountryCode, langVo.getLang()));
            }
            return valueVO;
        }).sorted(Comparator.comparing(PropertyValueVO::getSort)).collect(Collectors.toList());
        return propertyValueVOList;
    }


    private boolean requiredByCountryLang(String country, String lang) {
        boolean required = false;
        String countryLang = countryManageService.getLangByCountryCode(country);
        switch (country) {
            case CountryConstant.COUNTRY_ZH:
                required = LangConstant.LANG_ZH.equals(lang);
                break;
            default:
                required = LangConstant.LANG_ZH.equals(lang) || countryLang.equals(lang);
                break;
        }
        return required;
    }

    /**
     * 补全页面所需sku信息
     *
     * @param detailVO 商品详情页对象
     */
    public void fillSkuVoList(SpuDetailVO detailVO) {
        String lang = StringUtils.isNotBlank(LangContextHolder.get()) ? LangContextHolder.get() : LangConstant.LANG_ZH;
        List<SkuVO> skuVOList = skuReadManageService.selectSkusBySpuId(detailVO.getSpuVO(), lang);
        this.fillJdVendorName(skuVOList);
        detailVO.setSkuVOList(skuVOList);
    }

    /**
     * 处理sku扩展属性，sku扩展属性分组信息
     * @param skuVOList
     */
    public void handleSkuVOGroupExtAttribute(List<SkuVO> skuVOList, List<PropertyVO> skuExtendPropertyList ) {
        try {
            if (CollectionUtils.isNotEmpty(skuVOList) && CollectionUtils.isNotEmpty(skuExtendPropertyList)) {
                // 设置sku扩展属性，包括分组信息
                for (SkuVO skuVO : skuVOList) {
                    List<PropertyVO> skuExtendPropertyListCopy = JSON.parseArray(
                            JSON.toJSONString(skuExtendPropertyList),
                            PropertyVO.class
                    );
                    skuVO.setExtendPropertyList(skuExtendPropertyListCopy);
                    skuVO.setStoreExtendPropertyList(Lists.newArrayList());
                    this.fillGroupExtendAttributeSelected(skuVO.getExtendPropertyList(), skuVO.getStoreExtendPropertyList(), ExtendPropertyGroupVO.obtainExtendPropertyGroupVOList(skuVO.getGroupExtAttribute()));
                }
            }
        } catch (Exception e) {
            log.error("SpuConvertService.handleSkuVOGroupExtAttribute error", e);
        } finally {
            log.info("SpuConvertService.handleSkuVOGroupExtAttribute 处理错误，skuVOList={}，skuExtendPropertyList={}", JSON.toJSONString(skuVOList), JSON.toJSONString(skuExtendPropertyList));
        }
    }

    /**
     * 补全页面所需sku信息的跨国属性及跨国资质
     *
     * @param detailVO 商品详情页对象
     */
    public void fillSkuInterVoList(SpuDetailVO detailVO) {
        String lang = StringUtils.isNotBlank(LangContextHolder.get()) ? LangContextHolder.get() : LangConstant.LANG_ZH;
        skuReadManageService.selectSkusInter(detailVO.getSpuVO(),detailVO.getSkuVOList(), lang);
    }

    public void fillSkuCurrencyVoList(SpuDetailVO detailVO){
        String lang = StringUtils.isNotBlank(LangContextHolder.get()) ? LangContextHolder.get() : LangConstant.LANG_ZH;
        skuReadManageService.selectSkusCurrency(detailVO.getSpuVO(),detailVO.getSkuVOList(), lang);
    }

    /**
     * 设置商品详描
     *
     * @param spuDetailVO spu详情对象
     */
    public void fillSpuDesc(SpuDetailVO spuDetailVO) {
        // 查询商品详描
        List<SpuDescLangPO> spuDescLangPOList = spuDescLangAtomicService.listDescLang(spuDetailVO.getSpuVO().getSpuId());
        Map<String, String> pc = Maps.newHashMap();
        Map<String, String> app = Maps.newHashMap();
        spuDescLangPOList.forEach(d -> {
            pc.put(d.getLang(), d.getPcDescription());
            app.put(d.getLang(), d.getAppDescription());
        });
        spuDetailVO.setPcDescriptionMap(pc);
        spuDetailVO.setAppDescriptionMap(app);
    }

    /**
     * 设置商品资质
     *
     * @param spuDetailVO spu详情对象
     */
    public void fillSpuCertificate(SpuDetailVO spuDetailVO) {

        if(StringUtils.isBlank(spuDetailVO.getSpuVO().getAttributeScope())){
            log.info("SpuConvertService.fillSpuCertificate attributeScope is null");
            return;
        }

        // 查询所有资质
        List<GlobalQualificationVO> globalQualificationVOS = globalQualificationManageService.queryByCountry(spuDetailVO.getLastCatId(),spuDetailVO.getSpuVO().getSourceCountryCode()
                ,Arrays.stream(spuDetailVO.getSpuVO().getAttributeScope().split(Constant.COMMA)).collect(Collectors.toSet()),AttributeDimensionEnum.SPU.getCode(),LangContextHolder.get(),spuDetailVO.getSpuVO()
                .getIsExport());
        if (CollectionUtils.isEmpty(globalQualificationVOS)) {
            log.info("SpuConvertService.fillSpuCertificate globalQualificationVOS is null");
            return;
        }

        Map<Long, SpuCertificatePO> certificatePOMap = Maps.newHashMap();
        final Long spuId = spuDetailVO.getSpuVO().getSpuId();
        List<SpuCertificatePO> spuCertificatePoList = spuCertificateAtomicService.listCertificateBySpuId(spuId);
        if (CollectionUtils.isNotEmpty(spuCertificatePoList)) {
            certificatePOMap = spuCertificatePoList.stream().collect(Collectors.toMap(SpuCertificatePO::getCertificateId, Function.identity(),(v1,v2)->v2));
        }

        // 读草稿时，将草稿数据合并进来
        if (CollectionUtils.isNotEmpty(spuDetailVO.getSpuCertificateVOList())) {
            certificatePOMap.putAll(spuDetailVO.getSpuCertificateVOList().stream().filter(Objects::nonNull).collect(Collectors.toMap(SpuCertificateVO::getCertificateId, SpuConvert.INSTANCE::certificateVo2po,(v1,v2)->v2)));
        }

        // 商品资质PO -> 商品资质VO，并补充资质名、资质说明等信息
        List<SpuCertificateVO> spuCertificateVOList = Lists.newArrayList();
        for (GlobalQualificationVO globalQualificationVO : globalQualificationVOS) {
            if (MapUtils.isNotEmpty(certificatePOMap) && certificatePOMap.containsKey(globalQualificationVO.getId())) {
                SpuCertificatePO spuCertificatePO = certificatePOMap.get(globalQualificationVO.getId());
                SpuCertificateVO spuCertificateVO = SpuConvert.INSTANCE.certificatePo2Vo(spuCertificatePO);
                if (globalQualificationVO.getGlobalQualificationLangVOList() != null && !globalQualificationVO.getGlobalQualificationLangVOList().isEmpty()) {
                    spuCertificateVO.setCertificateName(globalQualificationVO.getGlobalQualificationLangVOList().get(0).getQualificationName());
                }
                spuCertificateVO.setCertificateNote(globalQualificationVO.getQualificationRemark());
                spuCertificateVO.setClassification1(globalQualificationVO.getClassification1());
                spuCertificateVO.setClassification2(globalQualificationVO.getClassification2());
                spuCertificateVOList.add(spuCertificateVO);
            } else {
                SpuCertificateVO spuCertificateVO = SpuConvert.INSTANCE.globalCertificateVo2SpuCertificateVo(globalQualificationVO);
                if (globalQualificationVO.getGlobalQualificationLangVOList() != null && !globalQualificationVO.getGlobalQualificationLangVOList().isEmpty()) {
                    spuCertificateVO.setCertificateName(globalQualificationVO.getGlobalQualificationLangVOList().get(0).getQualificationName());
                }
                spuCertificateVOList.add(spuCertificateVO);
            }
        }
        spuDetailVO.setSpuCertificateVOList(spuCertificateVOList);
    }

    /**
     * 补充供应商名
     *
     * @param detailVO 商品详情对象
     */
    public void fillVendorName(SpuDetailVO detailVO) {
        String vendorCode = detailVO.getSpuVO().getVendorCode();
        if (StringUtils.isBlank(vendorCode)) {
            return;
        }
        // 查询供应商
        String countryCode = detailVO.getSpuVO().getSourceCountryCode();
        if (CountryConstant.COUNTRY_ZH.equals(countryCode)) {
            VendorVO vendorVO = vendorManageService.get(vendorCode);
            if (Objects.nonNull(vendorVO)) {
                detailVO.getSpuVO().setVendorName(vendorVO.getVendorName());
            }
        } else {
            Map<String, String> supplierMap = supplierAllInfoAtomicService.querySupplierNameByCodes(Sets.newHashSet(vendorCode), countryCode);
            if (MapUtils.isNotEmpty(supplierMap) && supplierMap.containsKey(vendorCode)) {
                detailVO.getSpuVO().setVendorName(supplierMap.get(vendorCode));
            }
        }
    }

    public void fillJdVendorName(List<SkuVO> skuVOList) {
        if (CollectionUtils.isNotEmpty(skuVOList) && skuVOList.stream().anyMatch(vo -> StringUtils.isNotBlank(vo.getJdVendorCode()))) {
            Set<String> jdVendorCodeSet = skuVOList.stream().filter(vo -> StringUtils.isNotBlank(vo.getJdVendorCode())).map(SkuVO::getJdVendorCode).collect(Collectors.toSet());
            Map<String, String> jdVendorMap = vendorManageService.getVendorMapByVendorCodesFromRpc(jdVendorCodeSet);
            for (SkuVO skuVO : skuVOList) {
                if (StringUtils.isNotBlank(skuVO.getJdVendorCode()) && jdVendorMap.containsKey(skuVO.getJdVendorCode())) {
                    skuVO.setJdVendorName(jdVendorMap.get(skuVO.getJdVendorCode()));
                }
            }
        }
    }

    public String convertAttributeScope(String attributeScope) {
        if (StringUtils.isBlank(attributeScope)) {
            return null;
        }

        // 拆分后按字母顺序排序
        List<String> scopeList = Arrays.stream(attributeScope.split(COMMA)).sorted().collect(Collectors.toList());
        return String.join(COMMA, scopeList);

    }

    @PFTracing
    public void fillCrossBorderSkuStock(List<SkuVO> skuVOList, String attributeScope){
        Stopwatch stopwatch = Stopwatch.createStarted();
        if (CollectionUtils.isEmpty(skuVOList)) {
            return;
        }

        boolean anyMatch = skuVOList.stream().filter(Objects::nonNull).anyMatch(vo -> CountryConstant.COUNTRY_ZH.equals(vo.getSourceCountryCode()));
        if (!anyMatch) {
            return;
        }

        Map<Long,Integer> jdSkuNumMap = skuVOList.stream().filter(Objects::nonNull).filter(vo-> CountryConstant.COUNTRY_ZH.equals(vo.getSourceCountryCode()) && Objects.nonNull(vo.getJdSkuId())).collect(Collectors.toMap(SkuVO::getJdSkuId,o-> 1));

        if (MapUtils.isNotEmpty(jdSkuNumMap)) {
            Map<Long, String> jdSkuStockMap = skuReadManageService.getJdSkuStockBySkuIdsAndNum(jdSkuNumMap);
            skuVOList.forEach(skuVO -> skuVO.setStockNum(jdSkuStockMap.getOrDefault(skuVO.getJdSkuId(), Constant.SOLD_OUT)));
        }


        Set<Long> skuIds = skuVOList.stream().map(SkuVO::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        if (StringUtils.isBlank(attributeScope)) {
            return;
        }
        // 销售国家
        List<String> countryCodeList = Arrays.stream(attributeScope.split(Constant.COMMA)).collect(Collectors.toList());
        // 查询所有仓库
        List<WarehousePO> warehousePOList = warehouseAtomicService.queryWarehouseListByCountryCode(countryCodeList);
        if (CollectionUtils.isEmpty(warehousePOList)) {
            return;
        }
        // 备货仓库集合
        Set<String> warehouIdSet = warehousePOList.stream().filter(Objects::nonNull).map(WarehousePO::getId).map(String::valueOf).collect(Collectors.toSet());

        Map<Long, List<StockPO>> stockMap = stockAtomicService.getStockMap(skuIds);

        // 查询安全库存阈值
        Map<String, SkuStockThresholdPO> stockThresholdMap = this.getStockThresholdMap(stockMap);
        // 查询sku仓库关系
        Map<Long, Map<Long,WarehouseSkuPO>> skuWarehouseRelationMap = warehouseSkuAtomicService.queryBindSkuWarehouseMap(skuIds);

        for (SkuVO skuVO : skuVOList) {
            List<StockPO> stockPOList = stockMap.get(skuVO.getSkuId());
            if (CollectionUtils.isNotEmpty(skuVO.getSkuStockRelationList())){
                if (CollectionUtils.isEmpty(stockPOList)) {
                    List<SkuStockRelationVO> skuStockRelationList = skuVO.getSkuStockRelationList().stream().filter(Objects::nonNull)
                            .filter(relationVo -> FACTORY_DEFAULT_ID_STR.equals(relationVo.getWarehouseId())).collect(Collectors.toList());
                    for (SkuStockRelationVO skuStockRelationVO : skuStockRelationList) {
                        skuStockRelationVO.setStockThreshold(this.getStockThreshold(stockThresholdMap, skuVO.getSkuId(), skuStockRelationVO.getWarehouseId()));
                    }
                    skuVO.setSkuStockRelationList(skuStockRelationList);
                }
            }else {
                if (CollectionUtils.isNotEmpty(stockPOList)) {
                    List<SkuStockRelationVO> skuStockRelationList = Lists.newArrayListWithCapacity(stockPOList.size());
                    Map<Long, WarehouseSkuPO> warehouseSkuPOMap = skuWarehouseRelationMap.get(skuVO.getSkuId());
                    stockPOList.forEach(stockPO -> {
                        if (warehouIdSet.contains(stockPO.getWarehouseId())) {
                            skuStockRelationList.add(new SkuStockRelationVO(skuVO.getSpuId(),stockPO, warehouseSkuPOMap.get(Long.valueOf(stockPO.getWarehouseId()))));
                        }
                    });
                    // 补充跨境品厂直库存
                    skuStockRelationList.add(0,new SkuStockRelationVO(Constant.FACTORY_DEFAULT_ID_STR,skuVO.getSpuId(), skuVO.getSkuId(), skuVO.getStockNum()));
                    for (SkuStockRelationVO skuStockRelationVO : skuStockRelationList) {
                        skuStockRelationVO.setStockThreshold(this.getStockThreshold(stockThresholdMap, skuVO.getSkuId(), skuStockRelationVO.getWarehouseId()));
                    }
                    skuVO.setSkuStockRelationList(skuStockRelationList);
                }
            }

        }
        stopwatch.stop();
        log.info("fillCrossBorderSkuStock 查询补充跨境商品库存信息 耗时：{} ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }



    @PFTracing
    public void fillApprovedSkuStockRelation(List<SkuVO> skuVOList, String attributeScope){
        Stopwatch stopwatch = Stopwatch.createStarted();
        if (CollectionUtils.isEmpty(skuVOList)) {
            return;
        }

/*        boolean anyMatch = skuVOList.stream().filter(Objects::nonNull).anyMatch(vo -> CountryConstant.COUNTRY_ZH.equals(vo.getSourceCountryCode()));
        if (!anyMatch) {
            return;
        }*/

        Map<Long,Integer> jdSkuNumMap = skuVOList.stream().filter(Objects::nonNull).filter(vo-> CountryConstant.COUNTRY_ZH.equals(vo.getSourceCountryCode()) && Objects.nonNull(vo.getJdSkuId())).collect(Collectors.toMap(SkuVO::getJdSkuId,o-> 1));

        if (MapUtils.isNotEmpty(jdSkuNumMap)) {
            Map<Long, String> jdSkuStockMap = skuReadManageService.getJdSkuStockBySkuIdsAndNum(jdSkuNumMap);
            skuVOList.forEach(skuVO -> skuVO.setStockNum(jdSkuStockMap.getOrDefault(skuVO.getJdSkuId(), Constant.SOLD_OUT)));
        }


        Set<Long> skuIds = skuVOList.stream().map(SkuVO::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        if (StringUtils.isBlank(attributeScope)) {
            return;
        }
        // 销售国家
        List<String> countryCodeList = Arrays.stream(attributeScope.split(Constant.COMMA)).collect(Collectors.toList());
        // 查询所有仓库
        List<WarehousePO> warehousePOList = warehouseAtomicService.queryWarehouseListByCountryCode(countryCodeList);
        if (CollectionUtils.isEmpty(warehousePOList)) {
            return;
        }
        // 备货仓库集合
        Set<String> warehouIdSet = warehousePOList.stream().filter(Objects::nonNull).map(WarehousePO::getId).map(String::valueOf).collect(Collectors.toSet());

        Map<Long, List<StockPO>> stockMap = stockAtomicService.getStockMap(skuIds);

        // 查询安全库存阈值
        Map<String, SkuStockThresholdPO> stockThresholdMap = this.getStockThresholdMap(stockMap);
        // 查询sku仓库关系
        Map<Long, Map<Long,WarehouseSkuPO>> skuWarehouseRelationMap = warehouseSkuAtomicService.queryBindSkuWarehouseMap(skuIds);

        for (SkuVO skuVO : skuVOList) {
            List<StockPO> stockPOList = stockMap.get(skuVO.getSkuId());
            if (CollectionUtils.isNotEmpty(skuVO.getSkuStockRelationList())){
                handleExistStockRelationList(skuVO, stockPOList,skuWarehouseRelationMap,warehouIdSet , stockThresholdMap);
            }else {
                handleNoExistStockRelationList(skuVO, stockPOList, skuWarehouseRelationMap, warehouIdSet, stockThresholdMap);
            }

        }
        stopwatch.stop();
        log.info("fillSkuStock 查询补充商品库存信息 耗时：{} ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }

    private void handleNoExistStockRelationList(SkuVO skuVO, List<StockPO> stockPOList, Map<Long, Map<Long, WarehouseSkuPO>> skuWarehouseRelationMap, Set<String> warehouIdSet, Map<String, SkuStockThresholdPO> stockThresholdMap) {
        if (CollectionUtils.isNotEmpty(stockPOList)) {
            List<SkuStockRelationVO> skuStockRelationList = createNewStockRelationList(skuVO, stockPOList, skuWarehouseRelationMap, warehouIdSet);
            // 补充跨境品厂直库存
            fillStockThreshold(skuVO, stockThresholdMap, skuStockRelationList);
            skuVO.setSkuStockRelationList(skuStockRelationList);
        }else if (CountryConstant.COUNTRY_ZH.equals(skuVO.getSourceCountryCode())){
            List<SkuStockRelationVO> skuStockRelationList = Lists.newArrayList();
            skuStockRelationList.add(new SkuStockRelationVO(Constant.FACTORY_DEFAULT_ID_STR, skuVO.getSpuId(), skuVO.getSkuId(), skuVO.getStockNum()));
            fillStockThreshold(skuVO, stockThresholdMap, skuStockRelationList);
            skuVO.setSkuStockRelationList(skuStockRelationList);
        }
    }

    private List<SkuStockRelationVO> createNewStockRelationList(SkuVO skuVO, List<StockPO> stockPOList, Map<Long, Map<Long, WarehouseSkuPO>> skuWarehouseRelationMap, Set<String> warehouIdSet) {
        List<SkuStockRelationVO> skuStockRelationList = Lists.newArrayList();
        Map<Long, WarehouseSkuPO> warehouseSkuPOMap = skuWarehouseRelationMap.get(skuVO.getSkuId());
        stockPOList.forEach(stockPO -> {
            if (warehouIdSet.contains(stockPO.getWarehouseId())) {
                skuStockRelationList.add(new SkuStockRelationVO(skuVO.getSpuId(),stockPO, warehouseSkuPOMap.get(Long.valueOf(stockPO.getWarehouseId()))));
            }else if (!CountryConstant.COUNTRY_ZH.equals(skuVO.getSourceCountryCode()) && StringUtils.isBlank(stockPO.getWarehouseId())){
                skuStockRelationList.add(new SkuStockRelationVO(Constant.FACTORY_DEFAULT_ID_STR, skuVO.getSpuId(), skuVO.getSkuId(), String.valueOf(stockPO.getStock())));
            }
        });
        // 跨境品增加厂直库存
        if (CountryConstant.COUNTRY_ZH.equals(skuVO.getSourceCountryCode())){
            skuStockRelationList.add(0,new SkuStockRelationVO(Constant.FACTORY_DEFAULT_ID_STR, skuVO.getSpuId(), skuVO.getSkuId(), skuVO.getStockNum()));
        }
        return skuStockRelationList;
    }

    private void handleExistStockRelationList(SkuVO skuVO, List<StockPO> stockPOList, Map<Long, Map<Long, WarehouseSkuPO>> skuWarehouseRelationMap, Set<String> warehouIdSet, Map<String, SkuStockThresholdPO> stockThresholdMap) {
        if (CollectionUtils.isEmpty(stockPOList)) {
            List<SkuStockRelationVO> skuStockRelationList = skuVO.getSkuStockRelationList().stream().filter(Objects::nonNull)
                    .filter(relationVo -> FACTORY_DEFAULT_ID_STR.equals(relationVo.getWarehouseId())).collect(Collectors.toList());
            fillStockThreshold(skuVO, stockThresholdMap, skuStockRelationList);
            skuVO.setSkuStockRelationList(skuStockRelationList);
        }else {
            // 库存信息为空时，补充跨境品厂直库存
            List<SkuStockRelationVO> newStockRelationList = createNewStockRelationList(skuVO, stockPOList, skuWarehouseRelationMap, warehouIdSet);
            fillStockThreshold(skuVO,stockThresholdMap,newStockRelationList);
            skuVO.setSkuStockRelationList(newStockRelationList);
        }
    }

    private void fillStockThreshold(SkuVO skuVO, Map<String, SkuStockThresholdPO> stockThresholdMap, List<SkuStockRelationVO> skuStockRelationList) {
        for (SkuStockRelationVO skuStockRelationVO : skuStockRelationList) {
            skuStockRelationVO.setStockThreshold(this.getStockThreshold(stockThresholdMap, skuVO.getSkuId(), skuStockRelationVO.getWarehouseId()));
        }
    }

    /**
     * 根据库存信息获取 SKU 的库存阈值映射。
     * @param stockMap SKU ID 到 StockPO 列表的映射
     * @return SKU ID 到 SkuStockThresholdPO 的映射
     */
    private Map<String, SkuStockThresholdPO> getStockThresholdMap(Map<Long, List<StockPO>> stockMap){
        if (MapUtils.isEmpty(stockMap)) {
            return Collections.emptyMap();
        }
        List<SkuStockThresholdVO> skuStockThresholdVOList = Lists.newArrayList();
        stockMap.forEach((skuId, stockPOList) -> {
            if (CollectionUtils.isNotEmpty(stockPOList)) {
                stockPOList.forEach(stockPO -> skuStockThresholdVOList.add(new SkuStockThresholdVO(StringUtils.isNotBlank(stockPO.getWarehouseId()) ? Long.valueOf(stockPO.getWarehouseId()) : null,skuId)));
            }
        });

        return skuStockThresholdAtomicService.batchQuerySkuStockThresholdMap(skuStockThresholdVOList);
    }

    /**
     * 根据 SKU ID 和仓库 ID 从 Map 中获取库存阈值。
     * @param skuStockThresholdMap SKU 的库存阈值 Map。
     * @param skuId SKU 的 ID。
     * @param warehouseId 仓库的 ID。
     * @return 库存阈值，若未找到则返回 null。
     */
    private Long getStockThreshold(Map<String,SkuStockThresholdPO> skuStockThresholdMap,Long skuId,String warehouseId) {
        warehouseId = StringUtils.isBlank(warehouseId) || FACTORY_DEFAULT_ID_STR.equals(warehouseId) ? null : warehouseId;

        if (StringUtils.isBlank(warehouseId) && skuStockThresholdMap.containsKey(skuId.toString())) {
            return skuStockThresholdMap.get(skuId.toString()).getStockThreshold();
        }else if (StringUtils.isNotBlank(warehouseId) && skuStockThresholdMap.containsKey(skuId.toString() +warehouseId)) {
            return skuStockThresholdMap.get(skuId + warehouseId).getStockThreshold();
        }
        return null;
    }

    /**
     * 填充SPU修订原因
     * @param spuDetailVO SPU详情值对象
     * @throws Exception 异常情况可能抛出
     */
    public void fillSpuAmendReason(SpuDetailVO spuDetailVO){
        SpuVO spuVO = spuDetailVO.getSpuVO();

        if (SpuAuditStatusEnum.amendReason(spuVO.getAuditStatus())){
            String spuAuditReason = spuAuditRecordAtomicService.getSpuAuditReason(spuVO.getSpuId(), spuVO.getAuditStatus());
            spuDetailVO.setAmendReason(spuAuditReason);
        }
    }
    @PFTracing
    public Map<String, List<GroupPropertyVO>> getInterAttributeMap(Long lastCatId, String attributeScope, String lang, String sourceCountryCode,Integer isExport) {
        Map<String, List<GroupPropertyVO>> resultMap = new HashMap<>();
        // 查询国际扩展属性
        List<String> attributeScopeList = StringUtils.isNotBlank(attributeScope) ? Arrays.asList(attributeScope.split(COMMA)) : Lists.newArrayList();
        List<GroupPropertyVO> spuGroupPropertyVOS = globalAttributeManageService.queryGroupPropertyVos(lastCatId, sourceCountryCode, attributeScopeList,AttributeDimensionEnum.SPU.getCode(), lang, isExport);
        this.setPublishProductRequired(spuGroupPropertyVOS,AttributeCheckTypeEnum.PUBLISH_PRODUCT);
        if(CollectionUtils.isNotEmpty(spuGroupPropertyVOS)){
            spuGroupPropertyVOS = spuGroupPropertyVOS.stream()
                    .filter(item->item.getRequirement() != null)
                    .sorted(Comparator.comparing(GroupPropertyVO::getRequirement).reversed())
                    .collect(Collectors.toList());
        }

        resultMap.put(AttributeDimensionEnum.SPU.getDesc(), spuGroupPropertyVOS);

        List<GroupPropertyVO> skuGroupPropertyVOS = globalAttributeManageService.queryGroupPropertyVos(lastCatId, sourceCountryCode, attributeScopeList, AttributeDimensionEnum.SKU.getCode(), lang, isExport);
        this.setPublishProductRequired(skuGroupPropertyVOS,AttributeCheckTypeEnum.PUBLISH_PRODUCT);
        // 初始化越南税值
        if(Constant.SYSTEM_CODE.equals(SystemContextHolder.get())){
            skuConvertService.initVnTaxValue(skuGroupPropertyVOS,sourceCountryCode,lastCatId);
        }
        if(CollectionUtils.isNotEmpty(skuGroupPropertyVOS)){
            skuGroupPropertyVOS = skuGroupPropertyVOS.stream()
                    .filter(item->item.getRequirement() != null)
                    .sorted(Comparator.comparing(GroupPropertyVO::getRequirement).reversed())
                    .collect(Collectors.toList());
        }
        resultMap.put(AttributeDimensionEnum.SKU.getDesc(), skuGroupPropertyVOS);

        return resultMap;
    }

    /**
     * 填充spu资质及sku资质
     */
    public void fillPrepare4Cert(List<GlobalQualificationVO> allCertificateListVOS, SpuPrepareInfoVO prepareInfoVO) {
        if (CollectionUtils.isEmpty(allCertificateListVOS)) {
            log.error("SpuReadManageServiceImpl.fillPrepare4Cert allCertificateListVOS is null");
            return;
        }

        // 填充SPU资质
        fillSpuCertificates(allCertificateListVOS, prepareInfoVO);

        // 填充SKU资质
        fillSkuCertificates(allCertificateListVOS, prepareInfoVO);
    }

    private void fillSpuCertificates(List<GlobalQualificationVO> allCertificateListVOS, SpuPrepareInfoVO prepareInfoVO) {
        List<GlobalQualificationVO> spuGlobalQualificationVOS = allCertificateListVOS.stream()
                .filter(item -> item.getQualificationDimension().equals(AttributeDimensionEnum.SPU.getCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(spuGlobalQualificationVOS)) {
            List<SpuCertificateVO> globalSpuCertificatenVOS = new ArrayList<>();
            for (GlobalQualificationVO qualificationVO : spuGlobalQualificationVOS) {
                SpuCertificateVO spuCertificateVO = createSpuCertificateVO(qualificationVO);
                globalSpuCertificatenVOS.add(spuCertificateVO);
            }
            prepareInfoVO.setSpuCertificateVOList(globalSpuCertificatenVOS);
        }
    }

    private void fillSkuCertificates(List<GlobalQualificationVO> allCertificateListVOS, SpuPrepareInfoVO prepareInfoVO) {
        List<GlobalQualificationVO> skuGlobalQualificationVOS = allCertificateListVOS.stream()
                .filter(item -> item.getQualificationDimension().equals(AttributeDimensionEnum.SKU.getCode()))
                .collect(Collectors.toList());

        Map<Integer, List<GlobalQualificationVO>> groupMap = skuGlobalQualificationVOS.stream()
                .collect(Collectors.groupingBy(GlobalQualificationVO::getCheckType));

        List<GroupSkuCertificateVO> results = new ArrayList<>();
        AttributeCheckTypeEnum[] checkTypeEnums = AttributeCheckTypeEnum.values();
        for(AttributeCheckTypeEnum checkTypeEnum : checkTypeEnums){
            List<GlobalQualificationVO> n = groupMap.get(checkTypeEnum.getCode());
            if(CollectionUtils.isEmpty(n)){
                continue;
            }
            GroupSkuCertificateVO groupSkuCertificateVO = new GroupSkuCertificateVO();
            groupSkuCertificateVO.setRequirement(checkTypeEnum.getCode());
            List<SkuCertificateVO> globalSkuCertificatenVOS = new ArrayList<>();
            for (GlobalQualificationVO qualificationVO : n) {
                SkuCertificateVO skuCertificateVO = createSkuCertificateVO(qualificationVO);
                globalSkuCertificatenVOS.add(skuCertificateVO);
            }
            groupSkuCertificateVO.setSkuCertificateVOS(globalSkuCertificatenVOS);
            results.add(groupSkuCertificateVO);
        }

        prepareInfoVO.setGroupSkuCertificateVOList(results);
    }

    private SpuCertificateVO createSpuCertificateVO(GlobalQualificationVO qualificationVO) {
        SpuCertificateVO spuCertificateVO = new SpuCertificateVO();
        spuCertificateVO.setCertificateId(qualificationVO.getId());
        if (qualificationVO.getGlobalQualificationLangVOList() != null && !qualificationVO.getGlobalQualificationLangVOList().isEmpty()) {
            spuCertificateVO.setCertificateName(qualificationVO.getGlobalQualificationLangVOList().get(0).getQualificationName());
        }
        spuCertificateVO.setRequirement(qualificationVO.getCheckType());
        spuCertificateVO.setCertificateNote(qualificationVO.getQualificationRemark());
        spuCertificateVO.setQualificationSample(qualificationVO.getQualificationSample());
        spuCertificateVO.setClassification1(qualificationVO.getClassification1());
        spuCertificateVO.setClassification2(qualificationVO.getClassification2());
        return spuCertificateVO;
    }

    private SkuCertificateVO createSkuCertificateVO(GlobalQualificationVO qualificationVO) {
        SkuCertificateVO skuCertificateVO = new SkuCertificateVO();
        skuCertificateVO.setCertificateId(qualificationVO.getId());
        if (qualificationVO.getGlobalQualificationLangVOList() != null && !qualificationVO.getGlobalQualificationLangVOList().isEmpty()) {
            skuCertificateVO.setCertificateName(qualificationVO.getGlobalQualificationLangVOList().get(0).getQualificationName());
        }
        skuCertificateVO.setRequirement(qualificationVO.getCheckType());
        skuCertificateVO.setCertificateNote(qualificationVO.getQualificationRemark());
        skuCertificateVO.setQualificationSample(qualificationVO.getQualificationSample());
        skuCertificateVO.setClassification1(qualificationVO.getClassification1());
        skuCertificateVO.setClassification2(qualificationVO.getClassification2());
        return skuCertificateVO;
    }
    @PFTracing
    public List<SpuLangExtendVO> convertSpuLangExtendList(SpuVO spuVO){
        if(CollectionUtils.isNotEmpty(spuVO.getSpuLangExtendVOList())){
            return spuVO.getSpuLangExtendVOList();
        }

        List<SpuLangExtendVO> results = new ArrayList<>();
        if(StringUtils.isNotBlank(spuVO.getSpuZhName())){
            SpuLangExtendVO spuZh = new SpuLangExtendVO(LangConstant.LANG_ZH,null,spuVO.getSpuZhName(),null,null,null);
            results.add(spuZh);
        }
        if(StringUtils.isNotBlank(spuVO.getSpuEnName())){
            SpuLangExtendVO spuEn = new SpuLangExtendVO(LangConstant.LANG_EN,null,spuVO.getSpuEnName(),null,null,null);
            results.add(spuEn);
        }
        if(StringUtils.isNotBlank(spuVO.getSpuThName())){
            SpuLangExtendVO spuTh = new SpuLangExtendVO(LangConstant.LANG_TH,null,spuVO.getSpuThName(),null,null,null);
            results.add(spuTh);
        }
        if(StringUtils.isNotBlank(spuVO.getSpuVnName())){
            SpuLangExtendVO spuVn = new SpuLangExtendVO(LangConstant.LANG_VN,null,spuVO.getSpuVnName(),null,null,null);
            results.add(spuVn);
        }
        log.info("【修复数据】SpuConvertService.convertSpuLangExtendList spuDraft spuId:{}", String.valueOf(spuVO.getSpuId()));
        return results;
    }

    /**
     * 查询 SKU 和 MKU 的关系，并返回包含 SKU 和 MKU 列表的 SPU 映射。
     *
     * @param spuAndSkuMap SPU 和 SKU 列表的映射
     * @return SPU 和包含 SKU 和 MKU 列表的 VO 映射
     */
    public Map<Long, SpuVO> queryMkuAndSkuRelation(Map<Long, List<SkuPO>> spuAndSkuMap) {
        Map<Long, SpuVO> result = new HashMap<>();
        try {
            List<Long> skuIds = spuAndSkuMap.values().stream().flatMap(List::stream).map(SkuPO::getSkuId).collect(Collectors.toList());
            List<List<Long>> partition = Lists.partition(skuIds, 100);
            List<MkuRelationPO> mkuResult = new ArrayList<>();
            for (List<Long> part : partition) {
                List<MkuRelationPO> mkuRelationPO = mkuRelationAtomicService.queryBindListBySkuIds(part);
                if (CollectionUtils.isNotEmpty(mkuRelationPO)) {
                    mkuResult.addAll(mkuRelationPO);
                }
            }
            Map<Long, Long> skuMkuMap = mkuResult.stream().collect(HashMap::new, (m, v) -> m.put(v.getSkuId(), v.getMkuId()), HashMap::putAll);
            spuAndSkuMap.forEach((spu, skus) -> {
                SpuVO spuVO = new SpuVO();
                List<Long> skuList = new ArrayList<>();
                List<Long> mkuList = new ArrayList<>();
                spuVO.setMkuIds(mkuList);
                spuVO.setSkuIds(skuList);
                if (CollectionUtils.isNotEmpty(skus)) {
                    skus.forEach(s -> {
                        Long mkuId = skuMkuMap.get(s.getSkuId());
                        if (Objects.nonNull(mkuId)) {
                            skuList.add(s.getSkuId());
                            mkuList.add(mkuId);
                        }
                    });
                }
                result.put(spu, spuVO);
            });
        } catch (Exception e) {
            log.error("【系统异常】queryMkuAndSkuRelation param:{}, error:", JSON.toJSONString(spuAndSkuMap),e);
        }
        return result;
    }

    /**
     * 设置必填属性
     * @param spuGroupPropertyVOS 属性组列表
     */
    @PFTracing
    public void setPublishProductRequired(List<GroupPropertyVO> spuGroupPropertyVOS,AttributeCheckTypeEnum checkTypeEnum){
        if(CollectionUtils.isEmpty(spuGroupPropertyVOS)){
            log.info("SpuConvertService.setRequired params is null");
            return;
        }
        for(GroupPropertyVO groupPropertyVO: spuGroupPropertyVOS){
            List<PropertyVO> propertyVOS = groupPropertyVO.getPropertyVOS();
            if(CollectionUtils.isEmpty(propertyVOS)){
                continue;
            }
            if(groupPropertyVO.getRequirement() != null
                    && groupPropertyVO.getRequirement().equals(checkTypeEnum.getCode())){
                for (PropertyVO propertyVO : propertyVOS) {
                    if(propertyVO == null){
                        continue;
                    }
                    propertyVO.setRequired(Boolean.TRUE);
                    List<PropertyValueVO> propertyValueVOList = propertyVO.getPropertyValueVOList();
                    if(CollectionUtils.isEmpty(propertyValueVOList)){
                        continue;
                    }
                    for(PropertyValueVO propertyValueVO : propertyValueVOList){
                        propertyValueVO.setRequired(Boolean.TRUE);
                    }
                }
            } else {
                for (PropertyVO propertyVO : propertyVOS) {
                    if(propertyVO == null){
                        continue;
                    }
                    propertyVO.setRequired(Boolean.FALSE);
                    List<PropertyValueVO> propertyValueVOList = propertyVO.getPropertyValueVOList();
                    if(CollectionUtils.isEmpty(propertyValueVOList)){
                        continue;
                    }
                    for(PropertyValueVO propertyValueVO : propertyValueVOList){
                        propertyValueVO.setRequired(Boolean.FALSE);
                    }
                }
            }
        }
    }

    /**
     * 将存储的扩展属性字符串转换为PropertyVO列表
     *
     * @param storeExtAttribute 存储的扩展属性字符串，格式如：属性id1:属性值1ID1,属性id1:属性值1ID2#属性id2:EN^属性值名称
     * @return 转换后的PropertyVO列表
     */
    public List<PropertyVO> convertStoreExtAttributeToPropertyList(String storeExtAttribute) {
        // 边界值判断：空字符串或null
        if (StringUtils.isBlank(storeExtAttribute)) {
            log.info("SpuConvertService.convertStoreExtAttributeToPropertyList storeExtAttribute is blank");
            return Lists.newArrayList();
        }

        try {
            // 使用现有方法解析扩展属性字符串
            Map<String, List<String>> storeExtendAttributeMap = splitExtendAttributeStr(storeExtAttribute);

            // 边界值判断：解析结果为空
            if (MapUtils.isEmpty(storeExtendAttributeMap)) {
                log.info("SpuConvertService.convertStoreExtAttributeToPropertyList parsed storeExtendAttributeMap is empty");
                return Lists.newArrayList();
            }

            List<PropertyVO> propertyVOList = Lists.newArrayList();

            // 遍历解析后的属性映射
            for (Map.Entry<String, List<String>> entry : storeExtendAttributeMap.entrySet()) {
                String attributeIdStr = entry.getKey();
                List<String> valueList = entry.getValue();

                // 边界值判断：属性ID为空或属性值列表为空
                if (StringUtils.isBlank(attributeIdStr) || CollectionUtils.isEmpty(valueList)) {
                    log.warn("SpuConvertService.convertStoreExtAttributeToPropertyList attributeId or valueList is empty, attributeId={}", attributeIdStr);
                    continue;
                }

                Long attributeId;
                try {
                    attributeId = Long.valueOf(attributeIdStr);
                } catch (NumberFormatException e) {
                    log.warn("SpuConvertService.convertStoreExtAttributeToPropertyList invalid attributeId format: {}", attributeIdStr);
                    continue;
                }

                // 创建PropertyVO对象
                PropertyVO propertyVO = new PropertyVO();
                propertyVO.setAttributeId(attributeId);

                List<PropertyValueVO> propertyValueVOList = Lists.newArrayList();

                // 处理属性值列表
                for (String value : valueList) {
                    if (StringUtils.isBlank(value)) {
                        continue;
                    }

                    PropertyValueVO propertyValueVO = new PropertyValueVO();
                    propertyValueVO.setAttributeId(attributeId);
                    propertyValueVO.setSelected(Boolean.TRUE); // 存储的属性值默认为选中状态

                    // 判断是否为文本类型的多语言属性值（包含语言前缀）
                    if (value.contains(FACTORIAL)) {
                        // 文本类型：格式为 "语言^属性值名称"
                        // 使用indexOf找到第一个^的位置，处理可能包含多个^的情况
                        int factorialIndex = value.indexOf(FACTORIAL);
                        if (factorialIndex > 0 && factorialIndex < value.length() - 1) {
                            String lang = value.substring(0, factorialIndex);
                            String attributeValueName = value.substring(factorialIndex + 1);

                            // 边界值判断：语言和属性值名称不能为空
                            if (StringUtils.isNotBlank(lang) && StringUtils.isNotBlank(attributeValueName)) {
                                propertyValueVO.setLang(lang);
                                propertyValueVO.setAttributeValueName(attributeValueName);
                                // 文本类型的属性值ID可以设置为null或者使用特殊值
                                propertyValueVO.setAttributeValueId(null);
                            } else {
                                log.warn("SpuConvertService.convertStoreExtAttributeToPropertyList invalid lang or attributeValueName, value={}", value);
                                continue;
                            }
                        } else {
                            log.warn("SpuConvertService.convertStoreExtAttributeToPropertyList invalid text value format: {}", value);
                            continue;
                        }
                    } else {
                        // 数值类型：直接是属性值ID
                        try {
                            Long attributeValueId = Long.valueOf(value);
                            propertyValueVO.setAttributeValueId(attributeValueId);
                        } catch (NumberFormatException e) {
                            log.warn("SpuConvertService.convertStoreExtAttributeToPropertyList invalid attributeValueId format: {}", value);
                            continue;
                        }
                    }

                    propertyValueVOList.add(propertyValueVO);
                }

                // 边界值判断：属性值列表不能为空
                if (CollectionUtils.isNotEmpty(propertyValueVOList)) {
                    propertyVO.setPropertyValueVOList(propertyValueVOList);
                    propertyVOList.add(propertyVO);
                } else {
                    log.warn("SpuConvertService.convertStoreExtAttributeToPropertyList no valid property values for attributeId={}", attributeId);
                }
            }

            log.info("SpuConvertService.convertStoreExtAttributeToPropertyList successfully converted {} properties", propertyVOList.size());
            return propertyVOList;

        } catch (Exception e) {
            log.error("SpuConvertService.convertStoreExtAttributeToPropertyList error occurred while converting storeExtAttribute: {}", storeExtAttribute, e);
            return Lists.newArrayList();
        }
    }

    public void handleNcmCodeTry(List<SkuVO> skuVOList, SpuVO spuVO) {

        SaveSpuVO saveSpuVO = new SaveSpuVO();
        saveSpuVO.setSkuVOList(skuVOList);
        saveSpuVO.setSpuVO(spuVO);

        try {
            log.info("handleNcmCode before, saveSpuVO={}", JSONObject.toJSONString(saveSpuVO));
            this.handleNcmCode(skuVOList, spuVO);
        } catch (Exception e) {
            log.error("handleNcmCode is error, spuId={}", saveSpuVO.getSpuVO() == null ? null : saveSpuVO.getSpuVO().getSpuId());
            // 增加一个埋点
        } finally {
            log.info("handleNcmCode after, saveSpuVO={}", JSONObject.toJSONString(saveSpuVO));
        }
    }

    public void handleNcmCode(List<SkuVO> skuVOList, SpuVO spuVO) {

        SkuWriteManageService skuWriteManageService = SpringUtil.getBean(SkuWriteManageService.class);
        BrSkuTaxAtomicService brSkuTaxAtomicService = SpringUtil.getBean(BrSkuTaxAtomicService.class);

//        List<SkuVO> skuVOList = saveSpuVO.getSkuVOList();
//        SpuVO spuVO = saveSpuVO.getSpuVO();

        if (spuVO == null || CollectionUtils.isEmpty(skuVOList)) {
            return;
        }

        String sourceCountryCode = spuVO.getSourceCountryCode();

        if (!StringUtils.equals(sourceCountryCode, CountryConstant.COUNTRY_ZH)) {
            return;
        }


        // 属性范围, 判断是否有巴西
        String attributeScope = spuVO.getAttributeScope();

        if (StringUtils.isBlank(attributeScope)) {
            log.warn("属性范围为空, skuId={}", spuVO.getSpuId());
            return;
        }

        List<String> countryCodes = Splitter.on(",").splitToList(attributeScope);

        // 属性范围包含巴西的，才需要更新
        if (!countryCodes.contains(CountryConstant.COUNTRY_BR)) {
            log.warn("属性范围不包含巴西无需处理ncm码, skuId={}", spuVO.getSpuId());
            return;
        }

        List<Long> jdSkus = skuVOList.stream().map(SkuVO::getJdSkuId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, BrSkuTaxPO> skuTaxMap = brSkuTaxAtomicService.mapByJdSkuId(jdSkus);
        if (MapUtils.isEmpty(skuTaxMap)) {
            log.info("skuTaxMap is null, jdSkus={}", JSONObject.toJSONString(jdSkus));
            return;
        }

        skuVOList.forEach(skuVO -> {

            Long jdSkuId = skuVO.getJdSkuId();

            if (jdSkuId == null) {
                log.info("jdSkuId is null, sku={}", skuVO.getSkuId());
                return;
            }

            // 跨境属性分组
            List<GroupPropertyVO> goups = skuVO.getSkuInterPropertyList();

            if (CollectionUtils.isEmpty(goups)) {
                return;
            }

            BrSkuTaxPO skuTax = skuTaxMap.get(jdSkuId);

            if (skuTax == null) {
                log.warn("找不到 skuTax 数据, jdSkuId={}。[跳过处理]", jdSkuId);
                return;
            }

            String ncmCode = skuTax.getNcmCode();

            // 判断gourp 是否含有 requirement，如果没有requirement，查找是否包含71

            long requirementCount = goups.stream().filter(item -> item.getRequirement() != null).count();

            if (requirementCount == 0) {
                // 判断是否包含71
                long ncmCount = goups.stream().map(GroupPropertyVO::getPropertyVOS)
                        .filter(CollectionUtils::isNotEmpty)
                        .flatMap(Collection::stream)
                        .filter(item -> TaxTypeRelationEnum.NCM.getGlobalAttributeId().equals(item.getAttributeId()))
                        .count();

                if (ncmCount == 0) {
                    GroupPropertyVO propertyVO = goups.get(goups.size() - 1);
                    if (propertyVO == null) {
                        propertyVO = new GroupPropertyVO(null, Lists.newArrayList());
                        goups.add(propertyVO);
                    }

                    List<PropertyVO> properties = propertyVO.getPropertyVOS();
                    if (CollectionUtils.isEmpty(properties)) {
                        properties = Lists.newArrayList();
                        propertyVO.setPropertyVOS(properties);
                    }

                    PropertyVO property = skuWriteManageService.initPropertyVO(ncmCode);
                    properties.add(property);
                } else {
                    for (GroupPropertyVO goup : goups) {
                        List<PropertyVO> propertyVOS = goup.getPropertyVOS();
                        if (CollectionUtils.isEmpty(propertyVOS)) {
                            continue;
                        }
                        for (PropertyVO propertyVO : propertyVOS) {
                            if (!TaxTypeRelationEnum.NCM.getGlobalAttributeId().equals(propertyVO.getAttributeId())) {
                                continue;
                            }
                            List<PropertyValueVO> propertyValueVOList = propertyVO.getPropertyValueVOList();
                            if (CollectionUtils.isEmpty(propertyValueVOList)) {
                                continue;
                            }
                            for (PropertyValueVO valueVO : propertyValueVOList) {
                                valueVO.setAttributeValueName(skuTax.getNcmCode());
                            }
                        }
                    }
                }
            }

            for (GroupPropertyVO goup : goups) {
                Integer requirement = goup.getRequirement();

                if (requirement == null || !requirement.equals(AttributeCheckTypeEnum.ADD_CUSTOMER_POOL.getCode())) {
                    continue;
                }

                // 如果是加池必填
                List<PropertyVO> propertyVOS = goup.getPropertyVOS();

                // 数据预处理，预防空指针
                if (propertyVOS == null) {
                    propertyVOS = Lists.newArrayList();
                    goup.setPropertyVOS(propertyVOS);
                }

                // 判断是否包含71属性的数量
                long count = propertyVOS.stream().filter(item -> TaxTypeRelationEnum.NCM.getGlobalAttributeId().equals(item.getAttributeId())).count();

                // 如果不包含71
                if (count == 0) {
                    // 判断是否包含71的属性，如果不包含则，添加71属性
                    PropertyVO property = skuWriteManageService.initPropertyVO(ncmCode);
                    propertyVOS.add(property);
                } else {
                    // 包含71，则更新71的属性值
                    for (PropertyVO property : propertyVOS) {
                        Long attributeId = property.getAttributeId();
                        if (attributeId == null || !attributeId.equals(TaxTypeRelationEnum.NCM.getGlobalAttributeId())) {
                            continue;
                        }
                        List<PropertyValueVO> propertyValueVOList = property.getPropertyValueVOList();

                        if (CollectionUtils.isEmpty(propertyValueVOList)) {
                            List<PropertyValueVO> propertyValues = skuWriteManageService.initPropertyValueVOS(ncmCode);
                            property.setPropertyValueVOList(propertyValues);
                            continue;
                        }
                        for (PropertyValueVO value : propertyValueVOList) {
                            value.setAttributeValueName(ncmCode);
                        }
                    }
                }
            }
        });
    }

    public void sortSkuList(SpuDetailVO spuDetailVO){
        try {
            if(spuDetailVO == null){
                return;
            }
            List<SkuVO> skuVOList = spuDetailVO.getSkuVOList();
            if(CollectionUtils.isEmpty(skuVOList)){
                return;
            }
            // 获取图销和文销
            Map<Long, Integer> imgSortMap = new HashMap<>();
            Map<Long, Integer> textSortMap = new HashMap<>();
            for (PropertyVO propertyVO : spuDetailVO.getSalePropertyList()) {
                if (propertyVO.getAttributeInputType().equals(SaleAttributeTypeEnum.IMAGE.getCode()) && CollectionUtils.isNotEmpty(propertyVO.getPropertyValueVOList())) {
                    for (PropertyValueVO propertyValueVO : propertyVO.getPropertyValueVOList()) {
                        if (propertyValueVO.getAttributeValueId() != null) {
                            imgSortMap.put(propertyValueVO.getAttributeValueId(), propertyValueVO.getSort());
                        }
                    }
                } else if (propertyVO.getAttributeInputType().equals(SaleAttributeTypeEnum.TEXT.getCode()) && CollectionUtils.isNotEmpty(propertyVO.getPropertyValueVOList())) {
                    for (PropertyValueVO propertyValueVO : propertyVO.getPropertyValueVOList()) {
                        if (propertyValueVO.getAttributeValueId() != null) {
                            textSortMap.put(propertyValueVO.getAttributeValueId(), propertyValueVO.getSort());
                        }
                    }
                }
            }
            // 计算并设置sku的sort, 图销（sort+1）*10  +文销*sort=skusort
            for (SkuVO skuVO : skuVOList) {
                if (CollectionUtils.isEmpty(skuVO.getStoreSalePropertyList())) {
                    log.info("SkuVO的销售属性值为空, skuId: {}", skuVO.getSkuId());
                    skuVO.setSort(0);
                    continue;
                }
                int imgSort = 0;
                int textSort = 0;
                for (PropertyValueVO valueVO : skuVO.getStoreSalePropertyList()) {
                    Long attributeValueId = valueVO.getAttributeValueId();
                    if (attributeValueId != null) {
                        if (imgSortMap.containsKey(attributeValueId) && imgSortMap.get(attributeValueId) != null) {
                            imgSort = (imgSortMap.get(attributeValueId) + 1) * 10;
                        } else if (textSortMap.containsKey(attributeValueId) && textSortMap.get(attributeValueId) != null) {
                            textSort = textSortMap.get(attributeValueId);
                        }
                    }
                    skuVO.setSort(imgSort + textSort);
                }
            }
            // 排序
            skuVOList.sort(Comparator.comparing(SkuVO::getSort));
        } catch (Exception e) {
            log.error("SpuConvertService.sortSkuList error, 排序失败", e);
        }
    }
}
