package com.jdi.isc.product.soa.service.atomic.stock;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.enums.StockOperateResEnum;
import com.jdi.isc.product.soa.common.enums.StockOperateTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.StockWriteException;
import com.jdi.isc.product.soa.domain.enums.stock.StockOccupyTypeEnum;
import com.jdi.isc.product.soa.domain.stock.po.StockLogPO;
import com.jdi.isc.product.soa.domain.stock.po.StockPO;
import com.jdi.isc.product.soa.domain.stock.vo.SkuStockReqVO;
import com.jdi.isc.product.soa.domain.stock.vo.SkuStockVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseSkuPageVO;
import com.jdi.isc.product.soa.repository.mapper.product.StockMapper;
import com.jdi.isc.product.soa.service.atomic.stocklog.StockLogAtomicService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * sku库存原子服务
 * <AUTHOR>
 * @date 20231201
 */
@Service
@Slf4j
public class StockAtomicService extends ServiceImpl<StockMapper, StockPO> {

    @Resource
    private StockLogAtomicService stockLogAtomicService;

    @Value("${spring.profiles.active}")
    private String systemProfile;


    /** 库存批量查询*/
    public Map<Long,List<StockPO>> getStockMap (Set<Long> skuId){
        LambdaQueryWrapper<StockPO> wrapper = Wrappers.<StockPO>lambdaQuery()
                .in(StockPO::getSkuId, skuId)
                .eq(StockPO::getYn, YnEnum.YES.getCode())
                ;
        List<StockPO> list = super.list(wrapper);
        if(CollectionUtils.isNotEmpty(list)){
            return list.stream().collect(Collectors.groupingBy(StockPO::getSkuId));
        }
        return new HashMap<>(1);
    }

    /** sku库存预占*/
    public int occupy(List<StockItemManageReqDTO> stockItem) {
        int totalCnt = 0;
        for(StockItemManageReqDTO item : stockItem){
            int occupyCnt;
            StockPO validStock = getValidStock(item.getSkuId(), StringUtils.isNotBlank(item.getWarehouseId()) ? Long.parseLong(item.getWarehouseId()) : null);
            if (Objects.isNull(validStock)) {
                String errMsg = String.format("【%s】%s StockAtomicService.occupy error %s sku预占库存失败,预占的商品不存在: %s",systemProfile,LevelCode.P1.getMessage(),item.getSkuId(),JSON.toJSONString(item));
                Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
                throw new StockWriteException(errMsg);
            }
            if (StringUtils.isBlank(item.getWarehouseId())){
                occupyCnt = this.baseMapper.occupy(item.getSkuId(), item.getNum(), validStock.getVersion());
            }else {
                occupyCnt = this.baseMapper.occupyFromWarehouse(item.getSkuId(), item.getNum(),Long.parseLong(item.getWarehouseId()), validStock.getVersion(),Constant.ZERO);
            }
            log.info("StockAtomicService.occupy req:{} warehouseId={}, num:{} , occupyResult:{}" ,  item.getSkuId(),item.getWarehouseId(), item.getNum(), occupyCnt);
            totalCnt = totalCnt + occupyCnt;
        }
        return totalCnt;
    }

    /** sku库存预占释放(正向流程:预占释放&现货库存扣减)*/
    public int release(StockManageReqDTO req) {
        int totalCnt = 0;
        if(!verifyRelease(req)){
            String errMsg = String.format("【%s】%s StockAtomicService.release error %s sku库存预占扣减失败,库存扣减信息与预占流水信息不一致: %s",systemProfile, LevelCode.P1.getMessage(),req.getBizNo(),JSON.toJSONString(req));
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
            throw new StockWriteException(errMsg);
        }
        for(StockItemManageReqDTO item : req.getStockItem()){
            StockPO validStock = getValidStock(item.getSkuId(), StringUtils.isNotBlank(item.getWarehouseId()) ? Long.parseLong(item.getWarehouseId()) : null);
            if (Objects.isNull(validStock)) {
                String errMsg = String.format("【%s】%s StockAtomicService.occupy error %s sku预占库存失败,预占的商品不存在: %s",systemProfile,LevelCode.P1.getMessage(),item.getSkuId(),JSON.toJSONString(item));
                Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
                throw new StockWriteException(errMsg);
            }
            int releaseCnt;
            if (StringUtils.isBlank(item.getWarehouseId())) {
                releaseCnt = this.baseMapper.release(item.getSkuId(), item.getNum(), validStock.getVersion());
            }else {
                releaseCnt = this.baseMapper.releaseFromWarehouse(item.getSkuId(), item.getNum(),Long.parseLong(item.getWarehouseId()),validStock.getVersion(), Constant.ZERO);
            }
            log.info("StockAtomicService.release req:{} ,warehouseId={}, res:{} , occupyResult:{}" ,  item.getSkuId(),item.getWarehouseId(), item.getNum(), releaseCnt);
            totalCnt = totalCnt + releaseCnt;
        }
        return totalCnt;
    }

    /** 预占现货回退(提单失败)*/
    public int occupyReturn(StockManageReqDTO req) {
        int totalCnt = 0;
        if(!verify(req)){
            String errMsg = String.format("【%s】%s StockAtomicService.occupyReturn error %s sku库存预占回退失败,预占回退信息与预占流水信息不一致: %s",systemProfile,LevelCode.P1.getMessage(),req.getBizNo(),JSON.toJSONString(req));
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
            throw new StockWriteException(errMsg);
        }
        for(StockItemManageReqDTO item : req.getStockItem()){
            StockPO validStock = getValidStock(item.getSkuId(), StringUtils.isNotBlank(item.getWarehouseId()) ? Long.parseLong(item.getWarehouseId()) : null);
            if (Objects.isNull(validStock)) {
                String errMsg = String.format("【%s】%s StockAtomicService.occupy error %s sku库存预占回退失败,回退的商品不存在: %s",systemProfile,LevelCode.P1.getMessage(),item.getSkuId(),JSON.toJSONString(item));
                Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
                throw new StockWriteException(errMsg);
            }
            int occupyReturnCnt;
            if (StringUtils.isBlank(item.getWarehouseId())) {
                occupyReturnCnt = this.baseMapper.occupyReturn(item.getSkuId(), item.getNum(), validStock.getVersion());
            }else {
                occupyReturnCnt = this.baseMapper.occupyReturnWarehouse(item.getSkuId(), item.getNum(),Long.parseLong(item.getWarehouseId()), validStock.getVersion(),Constant.ZERO,Constant.ZERO);
            }
            log.info("StockAtomicService.occupyReturn req:{} ,warehouseId={}, res:{} , occupyResult:{}" ,  item.getSkuId(),item.getWarehouseId(), item.getNum(), occupyReturnCnt);
            totalCnt = totalCnt + occupyReturnCnt;
        }
        return totalCnt;
    }

    /** 现货回退(订单取消或售后)*/
    public int stockReturn(StockManageReqDTO req) {
        int totalCnt = 0;
        if(!returnVerify(req)){
            String errMsg = String.format("【%s】%s StockAtomicService.stockReturn error %s sku现货回退失败,回退信息与库存流水信息不一致或超额回退: %s",systemProfile,LevelCode.P1.getMessage(),req.getBizNo(),JSON.toJSONString(req));
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
            throw new StockWriteException(errMsg);
        }
        for(StockItemManageReqDTO item : req.getStockItem()){
            StockPO validStock = getValidStock(item.getSkuId(), StringUtils.isNotBlank(item.getWarehouseId()) ? Long.parseLong(item.getWarehouseId()) : null);
            if (Objects.isNull(validStock)) {
                String errMsg = String.format("【%s】%s StockAtomicService.stockReturn error %s sku现货回退失败,现货的商品不存在: %s",systemProfile,LevelCode.P1.getMessage(),item.getSkuId(),JSON.toJSONString(item));
                Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
                throw new StockWriteException(errMsg);
            }
            int occupyReturnCnt;
            if (StringUtils.isBlank(item.getWarehouseId())) {
                occupyReturnCnt = this.baseMapper.stockReturn(item.getSkuId(), item.getNum(),validStock.getVersion());
            }else {
                occupyReturnCnt = this.baseMapper.stockReturnWarehouse(item.getSkuId(), item.getNum(),Long.parseLong(item.getWarehouseId()),validStock.getVersion());
            }
            log.info("StockAtomicService.stockReturn req:{} ,warehouseId={}, res:{} , occupyResult:{}" ,  item.getSkuId(),item.getWarehouseId(), item.getNum(), occupyReturnCnt);
            totalCnt = totalCnt + occupyReturnCnt;
        }
        return totalCnt;
    }

    public Boolean exists(Long skuId) {
        LambdaQueryWrapper<StockPO> wrapper = new LambdaQueryWrapper<StockPO>()
                .eq(StockPO::getSkuId, skuId)
                .eq(StockPO::getYn, YnEnum.YES.getCode())
                .isNull(StockPO::getWarehouseId);
        return this.baseMapper.exists(wrapper);
    }

    public int updateStock(StockPO po) {
        return this.baseMapper.updateStock(po.getSkuId(), po.getAvailableStock(),po.getVersion());
    }
    public int appendStock(StockPO po) {
        return this.baseMapper.appendStock(po.getSkuId(), po.getAvailableStock(),po.getVersion());
    }

    public List<StockPO> listStock(StockManageReqDTO req){
        Set<Long> skuSet = req.getStockItem().stream().map(StockItemManageReqDTO::getSkuId).collect(Collectors.toSet());
        LambdaQueryWrapper<StockPO> wrapper = new LambdaQueryWrapper<StockPO>()
                .in(StockPO::getSkuId, skuSet)
                .isNull(StockPO::getWarehouseId)
                .eq(StockPO::getYn, YnEnum.YES.getCode());
        return this.baseMapper.selectList(wrapper);
    }

    public int truncate() {
        LambdaQueryWrapper<StockPO> wrapper = new LambdaQueryWrapper<StockPO>()
                .eq(StockPO::getYn, YnEnum.YES.getCode());
        return this.baseMapper.delete(wrapper);
    }

    /** 校验预占回退或扣减时请求type&bizNo&skuId&num 是否与DB流水记录一致*/
    private boolean verify(StockManageReqDTO req) {
        List<StockLogPO> exists = stockLogAtomicService.list(null,req.getBizNo(), StockOperateTypeEnum.OCCUPY,null);
        return checkStockLogNum(req, exists);
    }

    /** 校验预占回退或扣减时请求type&bizNo&skuId&num 是否与DB流水记录一致;校验扣减数量是否重复操作*/
    private boolean verifyRelease(StockManageReqDTO req) {
        Set<Long> skuIds = req.getStockItem().stream().map(StockItemManageReqDTO::getSkuId).collect(Collectors.toSet());
        List<StockLogPO> exists = stockLogAtomicService.list(null,req.getBizNo(), StockOperateTypeEnum.OCCUPY,skuIds);

        if(CollectionUtils.isEmpty(exists)){
            return false;
        }
        if(exists.size()!= req.getStockItem().size()){
            return false;
        }
        // 现货预占数量和扣减数量一致
        Map<Long, Long> existsDbMap = exists.stream().collect(Collectors.toMap(StockLogPO::getSkuId, StockLogPO::getNum));
        for(StockItemManageReqDTO reqItem : req.getStockItem()){
            if(!existsDbMap.containsKey(reqItem.getSkuId())){
                return false;
            }
            if(!Objects.equals(existsDbMap.get(reqItem.getSkuId()), reqItem.getNum())){
                return false;
            }
        }

        // 现货扣减流水不为空，已经扣减过库存
        List<StockLogPO> releaseExists = stockLogAtomicService.list(null,req.getBizNo(), StockOperateTypeEnum.RELEASE,skuIds);
        if (CollectionUtils.isNotEmpty(releaseExists)) {
            return false;
        }
        return true;
    }

    /** 校验订单取消或售后时 请求的库存回退信息是否与DB流水记录一致*/
    private boolean returnVerify(StockManageReqDTO req) {
        List<StockLogPO> existsOrder = stockLogAtomicService.list(null,req.getBizNo(), StockOperateTypeEnum.RELEASE,null);
        log.info("StockAtomicService.returnVerify req:{},db:{}" , JSON.toJSONString(req), JSON.toJSONString(existsOrder));
        if(CollectionUtils.isNotEmpty(existsOrder)){
            //请求的商品id、数量 应小于 数据库中之前 该订单库存扣减流水中的 id、数量
            Map<Long, Long> existsDbMap = existsOrder.stream().collect(Collectors.toMap(StockLogPO::getSkuId, StockLogPO::getNum));
            for(StockItemManageReqDTO reqItem : req.getStockItem()){
                if(!existsDbMap.containsKey(reqItem.getSkuId())){
                    log.error("StockAtomicService.returnVerify db库存流水中不含此退回请求skuId: existsDbMap:{} , res:{}" , JSON.toJSONString(existsDbMap) , reqItem.getSkuId());
                    return false;
                }
                if(reqItem.getNum() > existsDbMap.get(reqItem.getSkuId())){
                    log.error("StockAtomicService.returnVerify 请求退回库存数量: {} 大于库存流水记录数量:{}" ,JSON.toJSONString(reqItem), JSON.toJSONString(existsDbMap));
                    return false;
                }
                //查询当前sku历史总计已售后数量不能超过sku下单库存数量(子单+同sku的时候，需要严格查询子单+sku是否有历史回退记录，因此需要orderId+bizNo+sku+type联合查询)
                List<StockLogPO> existsReturn = stockLogAtomicService.list(req.getOrderId(),req.getBizNo(), StockOperateTypeEnum.STOCK_RETURN, Sets.newHashSet(reqItem.getSkuId()));
                if(CollectionUtils.isNotEmpty(existsReturn)){
                    Long historyAfterSaleCnt = existsReturn.stream().map(StockLogPO::getNum).reduce(0L, Long::sum);
                    if((historyAfterSaleCnt+reqItem.getNum()) > existsDbMap.get(reqItem.getSkuId())){
                        log.error("StockAtomicService.returnVerify 累计请求库存退回数量{}~{}大于库存流水数量:{}" ,historyAfterSaleCnt,JSON.toJSONString(reqItem), JSON.toJSONString(existsDbMap));
                        return false;
                    }
                }
            }
            return true;
        }
        log.error("StockAtomicService.returnVerify existsOrder {}未查到库存流水信息,无法执行退回", JSON.toJSONString(req));
        return false;
    }

    /** 库存批量查询*/
    public void resetStock (Set<Long> skuIds){
        if(CollectionUtils.isNotEmpty(skuIds)){
            for(Long skuId : skuIds){
                this.baseMapper.resetStock(skuId);
            }
        }
    }

    public PageInfo<SkuStockVO> page(SkuStockReqVO input){
        PageInfo<SkuStockVO> result = new PageInfo<>();
        input.setOffset((input.getIndex() - 1) * input.getSize());
        List<SkuStockVO> list = baseMapper.page(input);
        Long cnt = baseMapper.count(input);
        result.setRecords(list);
        result.setTotal(cnt);
        result.setIndex(input.getIndex());
        result.setSize(input.getSize());
        return result;
    }

    public PageInfo<SkuStockVO> pageQuery(SkuStockReqVO input){
        PageInfo<SkuStockVO> result = new PageInfo<>();
        input.setOffset((input.getIndex() - 1) * input.getSize());
        // 创建一个 Stopwatch 实例来测量时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        //获取分页明细
        List<SkuStockVO> list = baseMapper.pageQuery(input);
        // 记录分页查询耗时
        long pageQueryTime = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("StockAtomicService.pageQuery  time: {} ms", pageQueryTime);


        //获取总数
        Long cnt = getTotal(input);
        //记录总数查询耗时
        long totalCountTime = stopwatch.elapsed(TimeUnit.MILLISECONDS) - pageQueryTime;
        log.info("StockAtomicService.TotalCount  time: {} ms", totalCountTime);


        result.setRecords(list);
        result.setTotal(cnt);
        result.setIndex(input.getIndex());
        result.setSize(input.getSize());
        return result;
    }

    /**
     * 获取SKU库存总数
     * @param input SKU库存查询请求参数对象
     * @return 符合条件的SKU总数
     */
    private Long getTotal(SkuStockReqVO input) {
        //跨分片数据总数查询
        List<Long> skuList = baseMapper.totalCountList(input);
        return (long) skuList.size();
    }

    public int updateStock(SkuStockVO skuStockVO){
        return baseMapper.update(new StockPO(skuStockVO.getStock()),Wrappers.<StockPO>lambdaUpdate().eq(StockPO::getSkuId, skuStockVO.getSkuId()));
    }

    public long getStockCountByWarehouseId(Long warehouseId) {
        return this.baseMapper.sumAvailableStock(String.valueOf(warehouseId));
    }

    /**
     * 检查指定仓库中是否存在指定 SKU 的库存记录
     * @param skuId SKU 的唯一标识
     * @param warehouseId 仓库的唯一标识
     * @return 如果存在返回 true，否则返回 false
     */
    public Boolean existsByWarehouseId(Long skuId,String warehouseId) {
        LambdaQueryWrapper<StockPO> wrapper = new LambdaQueryWrapper<StockPO>()
                .eq(StockPO::getSkuId, skuId)
                .eq(StockPO::getWarehouseId,warehouseId)
                .eq(StockPO::getYn, YnEnum.YES.getCode());
        return this.baseMapper.exists(wrapper);
    }

    /**
     * 根据仓库ID更新库存
     * @param po 库存数据对象，包含 SKU ID、仓库ID 和可用库存信息
     * @return 更新的记录数量
     */
    public int updateStockByWarehouseId(StockPO po) {
        return this.baseMapper.updateStockByWarehouseId(po.getSkuId(),po.getWarehouseId(), po.getAvailableStock(),po.getVersion());
    }
    /**
     * 根据仓库ID追加在途库存
     * @param po 库存信息对象，包含SKU ID、仓库ID和可用库存
     * @return 追加库存的结果状态
     */
    public int appendTransitStockByWarehouseId(StockPO po) {
        return this.baseMapper.appendTransitStockByWarehouseId(po.getSkuId(), po.getWarehouseId(), po.getAvailableStock(),po.getVersion());
    }

    /**
     * 根据仓库ID追加库存
     * @param po 库存信息对象，包含SKU ID、仓库ID和可用库存
     * @return 追加库存的结果状态
     */
    public int appendStockByWarehouseId(StockPO po) {
        return this.baseMapper.appendStockyWarehouseId(po.getSkuId(), po.getWarehouseId(), po.getAvailableStock(),po.getVersion());
    }


    /**
     * 根据仓库ID查询库存信息。
     * @param req StockManageReqDTO 对象，包含仓库和sku的关系信息。
     * @return List<StockPO> 库存信息列表。
     */
    public List<StockPO> listStockByWarehouseId(StockManageReqDTO req){
        // 处理仓库和sku关系
        Map<String, Set<Long>> warehouseSkuMap = req.getStockItem().stream().collect(Collectors.groupingBy(StockItemManageReqDTO::getWarehouseId, Collectors.mapping(StockItemManageReqDTO::getSkuId, Collectors.toSet())));

        List<StockPO> stockPOList = Lists.newArrayList();
        warehouseSkuMap.forEach((warehouseId,skuSet) -> {
            LambdaQueryWrapper<StockPO> wrapper = new LambdaQueryWrapper<StockPO>()
                    .in(StockPO::getSkuId, skuSet)
                    .eq(StockPO::getWarehouseId,warehouseId)
                    .eq(StockPO::getYn, YnEnum.YES.getCode());
            stockPOList.addAll(this.baseMapper.selectList(wrapper));
        });
        return stockPOList;
    }

    /**
     * 批量查询备货仓库存
     * @param stockItem 库存管理请求DTO列表，包含需要查询的仓库ID和SKU ID信息
     * @return 返回符合查询条件的库存持久化对象列表
     */
    public List<StockPO> listStockByWarehouseId(List<StockItemManageReqDTO> stockItem){
        // 处理仓库和sku关系
        Map<String, Set<Long>> warehouseSkuMap = stockItem.stream().collect(Collectors.groupingBy(StockItemManageReqDTO::getWarehouseId, Collectors.mapping(StockItemManageReqDTO::getSkuId, Collectors.toSet())));

        List<StockPO> stockPOList = Lists.newArrayList();
        warehouseSkuMap.forEach((warehouseId,skuSet) -> {
            LambdaQueryWrapper<StockPO> wrapper = new LambdaQueryWrapper<StockPO>()
                    .in(StockPO::getSkuId, skuSet)
                    .eq(StockPO::getWarehouseId,warehouseId)
                    .eq(StockPO::getYn, YnEnum.YES.getCode());
            stockPOList.addAll(this.baseMapper.selectList(wrapper));
        });
        return stockPOList;
    }

    /**
     * 批量查询厂直库存
     * @param skuIds SKU ID集合，用于指定查询哪些SKU的库存信息
     * @return 返回符合条件的库存信息列表，列表中每个元素对应一个SKU的库存信息
     */
    public List<StockPO> listStockBySkuIds(Set<Long> skuIds){
        //
        List<StockPO> stockPOList = Lists.newArrayList();
            LambdaQueryWrapper<StockPO> wrapper = new LambdaQueryWrapper<StockPO>()
                    .in(StockPO::getSkuId, skuIds)
                    .isNull(StockPO::getWarehouseId)
                    .eq(StockPO::getYn, YnEnum.YES.getCode());
            stockPOList.addAll(this.baseMapper.selectList(wrapper));
        return stockPOList;
    }

    /** sku库存在库库存回退(正向流程:在途库存增加)*/
    public int transitStockReturn(StockManageReqDTO req) {
        int totalCnt = 0;
        if(!transitStockReturnVerify(req)){
            String errMsg = String.format("【%s】%s StockAtomicService.transitStockReturn error %s sku在途库存回退失败,回退信息与库存流水信息不一致或超额回退: %s",systemProfile,LevelCode.P1.getMessage(),req.getBizNo(),JSON.toJSONString(req));
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
            throw new StockWriteException(errMsg);
        }
        for(StockItemManageReqDTO item : req.getStockItem()){
            int transitStockReturnCnt = 0;
            if (StringUtils.isNotBlank(item.getWarehouseId())) {
                StockPO stockPO = this.baseMapper.selectOne(Wrappers.lambdaQuery(StockPO.class).eq(StockPO::getSkuId, item.getSkuId()).eq(StockPO::getWarehouseId, item.getWarehouseId()).eq(StockPO::getYn, YnEnum.YES.getCode()));
                if (Objects.isNull(stockPO)) {
                    String errMsg = String.format("【%s】%s StockAtomicService.transitStockReturn error %s sku在途库存回退失败,退回的商品和备货仓未设置过库存: %s",systemProfile,LevelCode.P1.getMessage(),req.getBizNo(),JSON.toJSONString(req));
                    Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
                    throw new StockWriteException(errMsg);
                }
                transitStockReturnCnt = this.baseMapper.transitStockReturnWarehouse(item.getSkuId(), item.getNum(),Long.parseLong(item.getWarehouseId()),stockPO.getVersion());
            }
            log.info("StockAtomicService.transitStockReturn req:{} ,warehouseId={}, res:{} , occupyResult:{}" ,  item.getSkuId(),item.getWarehouseId(), item.getNum(), transitStockReturnCnt);
            totalCnt = totalCnt + transitStockReturnCnt;
        }
        return totalCnt;
    }


    /** 校验备货采购单取消时 请求的在途库存回退信息是否与DB流水记录一致*/
    private boolean transitStockReturnVerify(StockManageReqDTO req) {
        //发货后子单加在途，取消也需要子单查询流水
        List<StockLogPO> existsOrder = stockLogAtomicService.list(null, req.getBizNo(), StockOperateTypeEnum.APPEND_TRANSIT_STOCK, null);
        log.info("StockAtomicService.transitStockReturnVerify req:{},db:{}" , JSON.toJSONString(req), JSON.toJSONString(existsOrder));
        if(CollectionUtils.isNotEmpty(existsOrder)){
            //请求的商品id、数量 应小于 数据库中之前 该备货采购单在途库存扣减流水中的 id、数量
            Map<Long, Long> existsDbMap = existsOrder.stream().collect(Collectors.toMap(StockLogPO::getSkuId, StockLogPO::getNum));
            for(StockItemManageReqDTO reqItem : req.getStockItem()){
                if(!existsDbMap.containsKey(reqItem.getSkuId())){
                    log.error("StockAtomicService.transitStockReturnVerify db库存流水中不含此退回请求skuId: existsDbMap:{} , res:{}" , JSON.toJSONString(existsDbMap) , reqItem.getSkuId());
                    return false;
                }
                if(reqItem.getNum() > existsDbMap.get(reqItem.getSkuId())){
                    log.error("StockAtomicService.transitStockReturnVerify 请求退回库存数量: {} 大于库存流水记录数量:{}" ,JSON.toJSONString(reqItem), JSON.toJSONString(existsDbMap));
                    return false;
                }
                //查询当前sku历史总计已回退总数量不能超过sku已增加在途库存数量
                List<StockLogPO> existsReturn = stockLogAtomicService.list(req.getOrderId(),null, StockOperateTypeEnum.TRANSIT_STOCK_RETURN, Sets.newHashSet(reqItem.getSkuId()));
                // 查询当前sku历史在途转现货库存总数量
                List<StockLogPO> existTransitTransferStock = stockLogAtomicService.list(req.getOrderId(),null, StockOperateTypeEnum.TRANSIT_STOCK_TRANSFER_STOCK, Sets.newHashSet(reqItem.getSkuId()));
                // 历史回退总数
                Long returnNum = Optional.ofNullable(existsReturn).orElseGet(ArrayList::new).stream().map(StockLogPO::getNum).reduce(0L, Long::sum);
                // 在途转现货总数
                Long transitTransferStockNum = Optional.ofNullable(existTransitTransferStock).orElseGet(ArrayList::new).stream().map(StockLogPO::getNum).reduce(0L, Long::sum);
                Long historyAfterSaleCnt = returnNum + transitTransferStockNum;
                if((historyAfterSaleCnt+reqItem.getNum()) > existsDbMap.get(reqItem.getSkuId())){
                    log.error("StockAtomicService.transitStockReturnVerify 累计请求在途库存退回数量{}~{}大于库存流水数量:{}" ,historyAfterSaleCnt,JSON.toJSONString(reqItem), JSON.toJSONString(existsDbMap));
                    return false;
                }
            }
            return true;
        }
        log.error("StockAtomicService.returnVerify transitStockReturnVerify {}未查到库存流水信息,无法执行退回在途库存", JSON.toJSONString(req));
        return false;
    }

    /**在途转现货，在途库存增加后，采购单实际入库时**/
    public int transitStockToStock(StockManageReqDTO req){
        int totalCnt = 0;
        if(!transitStockToStockVerify(req)){
            String errMsg = String.format("【%s】%s StockAtomicService.transitStockToStock error %s sku在途库存转现货失败,在途转现货信息与库存流水信息不一致或超额回退: %s",systemProfile,LevelCode.P1.getMessage(),req.getBizNo(),JSON.toJSONString(req));
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
            throw new StockWriteException(errMsg);
        }
        for(StockItemManageReqDTO item : req.getStockItem()){
            int transitStockReturnCnt = 0;
            if (StringUtils.isNotBlank(item.getWarehouseId())) {
                StockPO stockPO = this.baseMapper.selectOne(Wrappers.lambdaQuery(StockPO.class).eq(StockPO::getSkuId, item.getSkuId()).eq(StockPO::getWarehouseId, item.getWarehouseId()).eq(StockPO::getYn, YnEnum.YES.getCode()));
                if (Objects.isNull(stockPO)) {
                    String errMsg = String.format("【%s】%s StockAtomicService.transitStockToStock error %s sku在途库存转现货失败,转现货的商品和备货仓未设置过库存: %s",systemProfile,LevelCode.P1.getMessage(),req.getBizNo(),JSON.toJSONString(req));
                    Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
                    throw new StockWriteException(errMsg);
                }
                transitStockReturnCnt = this.baseMapper.transitStockToStockWarehouse(item.getSkuId(), item.getNum(),Long.parseLong(item.getWarehouseId()),stockPO.getVersion());
            }
            log.info("StockAtomicService.transitStockToStock req:{} ,warehouseId={}, res:{} , occupyResult:{}" ,  item.getSkuId(),item.getWarehouseId(), item.getNum(), transitStockReturnCnt);
            totalCnt = totalCnt + transitStockReturnCnt;
        }
        return totalCnt;
    }

    /**在途库存转现货，请求转现货库存数量是否与DB流水记录一致**/
    public boolean transitStockToStockVerify(StockManageReqDTO req){
        // 查询当前采购单在途库存增加流水
        List<StockLogPO> existsOrder = stockLogAtomicService.list(null,req.getOrderId(), StockOperateTypeEnum.APPEND_TRANSIT_STOCK,null);
        if (CollectionUtils.isEmpty(existsOrder)) {
            return false;
        }
        //请求的商品id、数量 应小于 数据库中之前 该备货采购单在途库存扣减流水中的 id、数量
        Map<Long, Long> existsDbMap = existsOrder.stream().collect(Collectors.toMap(StockLogPO::getSkuId, StockLogPO::getNum));
        for(StockItemManageReqDTO reqItem : req.getStockItem()){
            if(!existsDbMap.containsKey(reqItem.getSkuId())){
                log.error("StockAtomicService.transitStockToStockVerify db库存流水中不含此增加在途库存请求skuId: existsDbMap:{} , res:{}" , JSON.toJSONString(existsDbMap) , reqItem.getSkuId());
                return false;
            }

            if(reqItem.getNum() > existsDbMap.get(reqItem.getSkuId())){
                log.error("StockAtomicService.transitStockToStockVerify 请求转现货库存数量: {} 大于库存流水记录数量:{}" ,JSON.toJSONString(reqItem), JSON.toJSONString(existsDbMap));
                return false;
            }
            // 增加在途库存数量-回退在途库存数量-已转现货数量 > 在途转现货库存数量

            //查询当前sku历史总计已回退在途库存+在途转现货库存不能大于 增加在途库存总数量
            // 当前采购单已经存在 在途库存转现货的流水
            List<StockLogPO> transitToStock = stockLogAtomicService.list(req.getOrderId(),null, StockOperateTypeEnum.TRANSIT_STOCK_TRANSFER_STOCK, Sets.newHashSet(reqItem.getSkuId()));
            // 当前采购单回退在途库存流水
            List<StockLogPO> transitStockReturn = stockLogAtomicService.list(req.getOrderId(), null, StockOperateTypeEnum.TRANSIT_STOCK_RETURN, null);
            // 历史回退在途库存数量+在途转现货库存数量
            Long transitToStockNum = Optional.ofNullable(transitToStock).orElseGet(ArrayList::new).stream().map(StockLogPO::getNum).reduce(0L, Long::sum);
            Long transitStockReturnNum = Optional.ofNullable(transitStockReturn).orElseGet(ArrayList::new).stream().map(StockLogPO::getNum).reduce(0L, Long::sum);
            Long historyReturnAndTransferCnt = transitToStockNum + transitStockReturnNum;
            if((historyReturnAndTransferCnt+reqItem.getNum()) > existsDbMap.get(reqItem.getSkuId())){
                log.error("StockAtomicService.transitStockToStockVerify 累计请求在途库存退回+在途转现货数量{}~{}大于库存流水数量:{}" ,historyReturnAndTransferCnt,JSON.toJSONString(reqItem), JSON.toJSONString(existsDbMap));
                return false;
            }
        }
        return true;
    }

    /**
     * 根据 SKU ID 和仓库 ID 获取有效的库存信息。
     * @param skuId SKU 的唯一标识符。
     * @param warehouseId 仓库的唯一标识符。
     * @return 符合条件的库存信息对象。
     */
    public StockPO getValidStock(Long skuId,Long warehouseId) {
        LambdaQueryWrapper<StockPO> queryWrapper = Objects.nonNull(warehouseId) ? Wrappers.lambdaQuery(StockPO.class)
                .eq(StockPO::getSkuId, skuId)
                .eq(StockPO::getWarehouseId, warehouseId)
                .eq(StockPO::getYn, YnEnum.YES.getCode()) : Wrappers.lambdaQuery(StockPO.class).eq(StockPO::getSkuId, skuId)
                                                                                                .isNull(StockPO::getWarehouseId)
                                                                                                .eq(StockPO::getYn, YnEnum.YES.getCode());
        StockPO stockPO = this.getOne(queryWrapper);
        return stockPO;
    }

    /**备货仓预占库存 （预占=现货预占+在途预占）addWaitAllocationStock 新增待分配数量**/
    public int occupyEachStockFromWarehouse(StockItemManageReqDTO item, Integer version,Long addWaitAllocationStock) {
        return this.baseMapper.occupyFromWarehouse(item.getSkuId(), item.getNum(),Long.parseLong(item.getWarehouseId()), version,addWaitAllocationStock);
    }


    /** 校验在途预占数量 type&bizNo&skuId&num 是否与DB流水记录一致*/
    public boolean verifyOccupyRelease(StockManageReqDTO req) {
        List<StockLogPO> exists = stockLogAtomicService.list(null,req.getBizNo(), StockOperateTypeEnum.WAITING_ALLOCATION,null);
        boolean verifyOccupyRelease =  checkStockLogNumForRelease(req, exists);

        // 校验是否已转现货记录
        if(verifyOccupyRelease) {
            //请求参数验证通过，继续校验入参sku是否有转过现货的
            List<StockLogPO> transferExists = stockLogAtomicService.list(null, req.getBizNo(), StockOperateTypeEnum.TRANSIT_STOCK_TRANSFER_STOCK_OCCUPY, null);
            if (CollectionUtils.isNotEmpty(transferExists)) {
                Map<Long, Long> existsDbMap = transferExists.stream().collect(Collectors.toMap(StockLogPO::getSkuId, StockLogPO::getNum));
                for (StockItemManageReqDTO reqItem : req.getStockItem()) {
                    if (existsDbMap.containsKey(reqItem.getSkuId())) {
                        log.error("StockAtomicService.verifyOccupyRelease sku已存在在途转现货记录，不能重复转, skuId:{} , dbResult:{}", reqItem.getSkuId(), JSON.toJSONString(existsDbMap));
                        return false;
                    }
                }
            }
        }

        return verifyOccupyRelease;
    }

    /**校验库存流水和传入请求数量是否一致**/
    private boolean checkStockLogNumForRelease(StockManageReqDTO req, List<StockLogPO> exists) {
        if (CollectionUtils.isNotEmpty(exists)) {
            //整单转换需要校验请求数量
//            if (Objects.equals(req.getStockOccupyType(), StockOccupyTypeEnum.BY_ORDER.getCode())) {
//                if (exists.size() != req.getStockItem().size()) {
//                    log.warn("StockAtomicService.verifyOccupyRelease 整单转换sku请求数据和在途预占流水数目不一致，req:{}, dbResult:{}",
//                            JSON.toJSONString(req), JSON.toJSONString(exists));
//                    return false;
//                }
//            }

            //在途预占转现货预占，允许请求的sku数目<=之前在途预占的流水，因此不校验请求sku条数
            Map<Long, Long> existsDbMap = exists.stream().collect(Collectors.toMap(StockLogPO::getSkuId, StockLogPO::getNum));
            for (StockItemManageReqDTO reqItem : req.getStockItem()) {
                if (!existsDbMap.containsKey(reqItem.getSkuId())) {
                    return false;
                }
                if (!Objects.equals(existsDbMap.get(reqItem.getSkuId()), reqItem.getNum())) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }


    /**校验库存流水和传入请求数量是否一致**/
    private boolean checkStockLogNum(StockManageReqDTO req, List<StockLogPO> exists) {
        if(CollectionUtils.isNotEmpty(exists)){
            if(exists.size()!= req.getStockItem().size()){
                return false;
            }
            Map<Long, Long> existsDbMap = exists.stream().collect(Collectors.toMap(StockLogPO::getSkuId, StockLogPO::getNum));
            for(StockItemManageReqDTO reqItem : req.getStockItem()){
                if(!existsDbMap.containsKey(reqItem.getSkuId())){
                    return false;
                }
                if(!Objects.equals(existsDbMap.get(reqItem.getSkuId()), reqItem.getNum())){
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 预占回退 包含现货和在途预占
     * @param req 请求参数
     * @return 回退结果
     */
    public int occupyStockReturnWarehouse(StockManageReqDTO req){
        int totalCnt = 0;
        if(!verifyOccupyReturnWarehouse(req)){
            String errMsg = String.format("【%s】%s StockAtomicService.occupyReturn error %s sku库存预占回退失败,预占回退信息与预占流水信息不一致: %s",systemProfile,LevelCode.P1.getMessage(),req.getBizNo(),JSON.toJSONString(req));
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
            throw new StockWriteException(errMsg);
        }

        Map<Long,List<StockLogPO>> exists = stockLogAtomicService.getStockLogForMultiOperateTpeMap(null,req.getBizNo(), Lists.newArrayList(StockOperateTypeEnum.WAITING_ALLOCATION,StockOperateTypeEnum.OCCUPY,StockOperateTypeEnum.TRANSIT_STOCK_TRANSFER_STOCK_OCCUPY),null);
        for(StockItemManageReqDTO item : req.getStockItem()){
            StockPO validStock = getValidStock(item.getSkuId(), StringUtils.isNotBlank(item.getWarehouseId()) ? Long.parseLong(item.getWarehouseId()) : null);
            if (Objects.isNull(validStock)) {
                String errMsg = String.format("【%s】%s StockAtomicService.occupy error %s sku库存预占回退失败,回退的商品不存在: %s",systemProfile,LevelCode.P1.getMessage(),item.getSkuId(),JSON.toJSONString(item));
                Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
                throw new StockWriteException(errMsg);
            }
            // 当前sku预占流水列表
            List<StockLogPO> stockLogList = exists.getOrDefault(item.getSkuId(), Lists.newArrayList());
            // 待分配库存回退 = 待分配数量-已转现货预占数量
            Long waiteAllocationStock = Optional.ofNullable(stockLogList).orElseGet(ArrayList::new).stream().filter(po -> StockOperateTypeEnum.WAITING_ALLOCATION.getCode().equals(po.getType())).map(StockLogPO::getNum).reduce(Long::sum).orElse(Constant.ZERO);
            // 待分配已转现货数量
            Long transitStockTransferStock = Optional.ofNullable(stockLogList).orElseGet(ArrayList::new).stream().filter(po -> StockOperateTypeEnum.TRANSIT_STOCK_TRANSFER_STOCK_OCCUPY.getCode().equals(po.getType())).map(StockLogPO::getNum).reduce(Long::sum).orElse(Constant.ZERO);
            // 剩余待分配 = 下单时在分配 - 已转现货预占数量
            Long occupyOnWayStock = waiteAllocationStock - transitStockTransferStock;
            occupyOnWayStock = occupyOnWayStock <= Constant.ZERO ? Constant.ZERO : occupyOnWayStock;
            int occupyReturnCnt = this.baseMapper.occupyReturnWarehouse(item.getSkuId(), item.getNum(),Long.parseLong(item.getWarehouseId()), validStock.getVersion(),occupyOnWayStock,transitStockTransferStock);
            log.info("StockAtomicService.occupyStockReturn req:{} ,warehouseId={}, res:{} , occupyResult:{},occupyOnWayStock={}，waiteAllocationStock={},transitStockTransferStock={}" ,  item.getSkuId(),item.getWarehouseId(), item.getNum(), occupyReturnCnt,occupyOnWayStock,waiteAllocationStock,transitStockTransferStock);
            // 按照流水记录回退流水
            buildMultiStockLog(item, stockLogList);
            stockLogAtomicService.saveBatch(stockLogList);
            totalCnt = totalCnt + occupyReturnCnt;
        }
        return totalCnt;
    }

    private void buildMultiStockLog(StockItemManageReqDTO item, List<StockLogPO> stockLogList) {
        List<StockLogPO> removeList= Lists.newArrayList();
        for(StockLogPO stockLogPO : stockLogList){
            if (StockOperateTypeEnum.TRANSIT_STOCK_TRANSFER_STOCK_OCCUPY.getCode().equals(stockLogPO.getType())) {
                removeList.add(stockLogPO);
                continue;
            }
            stockLogPO.setId(null);
            stockLogPO.setCreator(item.getUpdater());
            stockLogPO.setUpdater(item.getUpdater());
            Date date = new Date();
            stockLogPO.setCreateTime(date);
            stockLogPO.setUpdateTime(date);
            if(StockOperateTypeEnum.WAITING_ALLOCATION.getCode().equals(stockLogPO.getType())){
                stockLogPO.setType(StockOperateTypeEnum.TRANSIT_STOCK_OCCUPY_RETURN.getCode());
            } else if (StockOperateTypeEnum.OCCUPY.getCode().equals(stockLogPO.getType())) {
                stockLogPO.setType(StockOperateTypeEnum.OCCUPY_RETURN.getCode());
            }
        }
        // 移除在途预占转现货预占的记录
        stockLogList.removeAll(removeList);
    }

    /**预占回退时，校验预占总流水和请求参数的数量对比是否一致**/
    private boolean verifyOccupyReturnWarehouse(StockManageReqDTO req) {
        // 支持部分预占回退，需要查当前sku预占流水； 由于混合订单（厂直+备货品）下单，厂直下单时会直接扣减现货，需要回退现货；备货品下单不扣减现货，只预占，所以需要校验预占流水
        Set<Long> skuIds = req.getStockItem().stream().map(StockItemManageReqDTO::getSkuId).collect(Collectors.toSet());
        // 查询两种预占流水
        List<StockLogPO> exists = stockLogAtomicService.listForMultiOperateTpe(null,req.getBizNo(), Lists.newArrayList(StockOperateTypeEnum.WAITING_ALLOCATION,StockOperateTypeEnum.OCCUPY),skuIds);
        if(CollectionUtils.isEmpty(exists)){
            return false;
        }
        // 按照sku分组，统计预占数量
        Map<Long, Long> existsDbMap = exists.stream().collect(Collectors.groupingBy(StockLogPO::getSkuId, Collectors.summingLong(StockLogPO::getNum)));
        // 回退SKU数量一致
        if(existsDbMap.size()!= req.getStockItem().size()){
            return false;
        }
        for(StockItemManageReqDTO reqItem : req.getStockItem()){
            if(!existsDbMap.containsKey(reqItem.getSkuId())){
                return false;
            }
            if(!Objects.equals(existsDbMap.get(reqItem.getSkuId()), reqItem.getNum())){
                return false;
            }
        }
        return true;
    }


    /** sku库存预占释放(正向流程:预占释放&现货库存扣减)*/
    public int releaseStockWarehouse(StockManageReqDTO req) {
        int totalCnt = 0;
        if(!verifyReleaseStockWarehouse(req)){
            String errMsg = String.format("【%s】%s StockAtomicService.release error %s sku库存预占扣减失败,库存扣减信息与预占流水信息不一致: %s",systemProfile, LevelCode.P1.getMessage(),req.getBizNo(),JSON.toJSONString(req));
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
            throw new StockWriteException(errMsg);
        }
        for(StockItemManageReqDTO item : req.getStockItem()){
            StockPO validStock = getValidStock(item.getSkuId(), StringUtils.isNotBlank(item.getWarehouseId()) ? Long.parseLong(item.getWarehouseId()) : null);
            if (Objects.isNull(validStock)) {
                String errMsg = String.format("【%s】%s StockAtomicService.occupy error %s sku预占库存失败,预占的商品不存在: %s",systemProfile,LevelCode.P1.getMessage(),item.getSkuId(),JSON.toJSONString(item));
                Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_STOCK_WARNING,errMsg);
                throw new StockWriteException(errMsg);
            }
            int releaseCnt;
            if (StringUtils.isBlank(item.getWarehouseId())) {
                releaseCnt = this.baseMapper.release(item.getSkuId(), item.getNum(), validStock.getVersion());
            }else {
                // 已转现货数量扣减 根据流水来扣
                List<StockLogPO> stockLogPOList = stockLogAtomicService.list(null, req.getBizNo(), StockOperateTypeEnum.TRANSIT_STOCK_TRANSFER_STOCK_OCCUPY, Collections.singleton(item.getSkuId()));
                Long transferedStock = Optional.ofNullable(stockLogPOList).orElseGet(ArrayList::new).stream().map(StockLogPO::getNum).reduce(Long::sum).orElse(Constant.ZERO);
                log.info("StockAtomicService.release skuId:{} ,warehouseId={}, transferedStock:{}", item.getSkuId(), item.getWarehouseId(), transferedStock);
                releaseCnt = this.baseMapper.releaseFromWarehouse(item.getSkuId(), item.getNum(),Long.parseLong(item.getWarehouseId()),validStock.getVersion(),transferedStock);
            }
            log.info("StockAtomicService.release req:{} ,warehouseId={}, res:{} , occupyResult:{}" ,  item.getSkuId(),item.getWarehouseId(), item.getNum(), releaseCnt);
            totalCnt = totalCnt + releaseCnt;
        }
        return totalCnt;
    }

    /**预占释放时，校验预占总流水和请求参数的数量对比是否一致**/
    private boolean verifyReleaseStockWarehouse(StockManageReqDTO req) {
        // 查询两种预占流水
        List<StockLogPO> exists = stockLogAtomicService.listForMultiOperateTpe(null,req.getBizNo(), Lists.newArrayList(StockOperateTypeEnum.TRANSIT_STOCK_TRANSFER_STOCK_OCCUPY,StockOperateTypeEnum.OCCUPY),null);
        if (CollectionUtils.isEmpty(exists)){
            return false;
        }

        Map<Long, Long> existsDbMap = exists.stream().collect(Collectors.groupingBy(StockLogPO::getSkuId, Collectors.summingLong(StockLogPO::getNum)));
        for(StockItemManageReqDTO reqItem : req.getStockItem()){
            if(!existsDbMap.containsKey(reqItem.getSkuId())){
                return false;
            }
            if(!Objects.equals(existsDbMap.get(reqItem.getSkuId()), reqItem.getNum())){
                return false;
            }
        }
        return true;
    }

    /**
     * 在途预占转现货操作，修改已转库存数量
     */
    public int onWayOccupyTransferToStock(StockItemManageReqDTO item, StockPO validStock) {
        return this.baseMapper.onWayOccupyTransferToStock(item.getSkuId(), item.getNum(), Long.valueOf(item.getWarehouseId()), validStock.getVersion());
    }

    public static void main(String[] args) {
        List<StockLogPO> existsReturn = new ArrayList<>();
        StockLogPO po1 = new StockLogPO();
        StockLogPO po2 = new StockLogPO();
        StockLogPO po3 = new StockLogPO();
        StockLogPO po4 = new StockLogPO();
        po1.setNum(1L);
        po2.setNum(2L);
        po3.setNum(3L);
        po4.setNum(4L);
        existsReturn.add(po1);
        existsReturn.add(po2);
        existsReturn.add(po3);
        existsReturn.add(po4);

        Long historyAfterSaleCnt = existsReturn.stream().map(StockLogPO::getNum).reduce(0L, Long::sum);
        System.out.println(historyAfterSaleCnt);
    }


}




