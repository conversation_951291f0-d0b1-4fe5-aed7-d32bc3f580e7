package com.jdi.isc.product.soa.service.manage.sku;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.sku.req.QuerySkuAvailableSaleReqDTO;
import com.jdi.isc.product.soa.api.sku.req.QuerySkuReqDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuAvailableSaleResDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuBaseInfoApiDTO;
import com.jdi.isc.product.soa.api.sku.req.SkuPredictPriceReqDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuFeatureApiDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuPredictPriceResDTO;
import com.jdi.isc.product.soa.domain.enums.SkuScopeEnum;
import com.jdi.isc.product.soa.domain.sku.biz.*;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuVO;
import com.jdi.isc.product.soa.domain.validation.ValidateSpuGroup;
import com.jdi.isc.product.soa.domain.vendor.po.VendorPO;
import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/12/2
 **/
public interface SkuReadManageService {

    Map<Long, SkuDetailOrderReadVO> getDetailBySkuId(Set<Long> skuIds, List<String> localList, boolean checkOnlineStatus);

    /**
     * 根据 SKU ID 集合获取对应的 SKU 基础信息。
     * @param skuId SKU ID 集合
     * @return SKU 基础信息 Map，key 为 SKU ID，value 为 SkuPO 对象
     */
    Map<Long, SkuPO> getSkuBaseInfoBySku(Set<Long> skuId);

    /**
     * 商品及货源国关系
     * @param skuId
     * @return
     */
    List<SkuCountryVO> getSourceCountryBySkuId(Set<Long> skuId);

    /**
     * 根据spuId查询sku列表
     *
     * @param spuVO spuId
     * @param lang  语言
     * @return sku列表
     */
    @Validated(ValidateSpuGroup.querySpu.class)
    List<SkuVO> selectSkusBySpuId(SpuVO spuVO, String lang);

    /**
     * 根据spuId查询sku列表，补全跨境属性及跨境资质
     *
     * @param spuId spuId
     * @param lang  语言
     * @return sku列表
     */
    @Validated(ValidateSpuGroup.querySpu.class)
    void selectSkusInter(SpuVO spuVO, List<SkuVO> skuVOS, String lang);

    /**
     * 根据spuId查询sku列表，补全跨境属性及跨境资质
     *
     * @param spuId spuId
     * @param lang  语言
     * @return sku列表
     */
    @Validated(ValidateSpuGroup.querySpu.class)
    void selectSkusCurrency(SpuVO spuVO, List<SkuVO> skuVOS, String lang);


    /**
     * 查询工业国内sku库存描述信息
     */
    Map<Long, String> getJdSkuStockBySkuIds(List<Long> jdSkuIds);

    /**
     * 批量查询skuIds的详情
     *
     * @param skuIds
     * @param lang
     * @return
     */
    Map<Long, SkuDetailVO> batchQueryDetailBySkuIds(Set<Long> skuIds, Set<SkuScopeEnum> skuScopeEnumSet, String lang);


    /**
     * 单个查询sku对象
     *
     * @param skuId skuId
     * @return skuVo对象
     */
    SkuVO getSkuBySkuId(Long skuId);

    /**
     * 查询工业国内sku信息
     *
     * @param jdSkuId 国内skuId
     * @return sku信息
     */
    DataResponse<SkuVO> getJdSkuById(Long jdSkuId, Long skuId);


    Map<Long, String> getJdSkuStockBySkuIdsAndNum(Map<Long, Integer> jdSkuIdNumMap);

    /**
     * 获取SKU销售价
     *
     * @param reqVO 计算含税价入参
     * @return 返回计算后
     */
    SkuCalculateTaxPriceVO getSkuCalculateSalePrice(SkuCalculatePriceReqVO reqVO);

    /**
     * 查询并返回指定品牌ID集合对应的SKU列表
     */
    List<SkuVO> querySkuListByBrandId(Set<Long> brandIds);

    /**
     * 处理本地SKU价格
     * @param skuVOList SKU值对象列表
     */
    void handleLocalSkuPrice(List<SkuVO> skuVOList,String sourceCountryCode);

    /**
     * 根据 SKU ID 集合获取对应的 SKU 特性信息。
     * @param skuReqDTO SKU ID 集合
     * @return SKU 特性信息映射，键为 SKU ID，值为 SkuFeaturePO 对象
     */
    Map<String, SkuFeatureApiDTO> getSkuFeatureBySku(QuerySkuReqDTO skuReqDTO);

    /**
     * 根据SPU ID获取对应的SKU基本信息。
     * @param spuId SPU的唯一标识符
     * @return 一个Map对象，其中键为SKU的ID，值为对应的SkuPO对象
     */
    Map<Long, SkuPO> getSkuBaseInfoBySku(Long spuId);


    /**
     * 根据供应商 SKU ID 集合获取 SKU 基本信息
     * @param vendorSkuIds 供应商 SKU ID 集合
     * @return SKU 基本信息 Map，key 为 SKU ID，value 为 SkuPO 对象
     */
    Map<String, SkuPO> getSkuBaseInfoByVendorSkuIds(Set<String> vendorSkuIds);


    /**
     * 根据SkuGlobalAttributeVO对象查询跨境属性详细信息。
     * @param param SkuGlobalAttributeVO对象，包含查询条件。
     * @return List<SkuGlobalAttributeDetailVO> 全局属性详细信息列表。
     */
    List<SkuGlobalAttributeDetailVO> selectGlobalAttribute(SkuGlobalAttributeVO param);
    /**
     * 处理本地SKU价格
     * @param skuVOList SKU值对象列表
     */
    void handleLocalSkuPrice(List<SkuVO> skuVOList,String sourceCountryCode,String supplierCode);

    List<SkuPO> getSkuListBySpuIds(Collection<Long> spuIds);


    /**
     * 根据目标国家代码和 SKU IDs 查询对应的中文库存信息。
     * @param skuIds SKU 的 ID 列表
     * @param targetCountryCode 目标国家的代码
     * @return 以 SKU ID 为键，中文库存信息为值的映射表
     */
    Map<Long, String> queryCnStockByTargetCountryCode(List<Long> skuIds, String targetCountryCode);


    /**
     * 根据客户代码查询指定 SKU 的中文名称。
     * @param skuIds SKU ID 列表
     * @param clientCode 客户代码
     * @return SKU ID 到中文名称的映射
     */
    Map<Long, String> queryCnStockByClientCode(List<Long> skuIds, String clientCode);

    /**
     * 根据sku查询国内供应商信息
     * @param SkuIds sku set集合
     * @return map关联结果
     */
    Map<Long, VendorPO> queryVendorBySkuIds(Set<Long> SkuIds);

    /**
     * 根据查询条件获取SKU基础信息
     * @param reqDTO 查询SKU基础信息的请求参数对象
     * @return 包含SKU基础信息映射集合的响应对象，键为SKU标识，值为SKU基础信息DTO
     */
    DataResponse<Map<String, SkuBaseInfoApiDTO>> querySkuBaseInfo(QuerySkuReqDTO reqDTO);

    /**
     * 根据jdSku查询sku信息
     */
    List<SkuPO> getSkuBaseInfoByJdSkuId(Set<Long> jdSkuId);



    /**
     * Calculate br predict country cost price data response.
     *
     * @param query the query
     * @return the data response
     */
    DataResponse<SkuPredictPriceResDTO> calculateBrPredictCountryCostPrice(SkuPredictPriceReqDTO query);

    /**
     * 根据商品ID列表获取对应的商品信息
     * @param productIds 商品ID列表
     * @return 包含商品信息的 ProductIdVO 对象列表
     */
    ProductIdVO getProductInfoByIds(List<Long> productIds);

    /**
     * 根据查询条件获取商品库存可售信息的映射关系。
     * @param reqVO 查询请求对象，包含查询条件等信息。
     * @return 包含商品ID和对应的库存可售结果的映射关系。
     */
    Map<Long, SkuAvailableSaleResultVO> querySkuAvailableMap(QuerySkuAvailableSaleReqVO reqVO);


    /**
     * 根据查询条件获取限购商品信息的映射关系。
     * @param reqVO 查询请求对象，包含查询条件。
     * @return 返回一个 Map 对象，其中键为商品 ID，值为对应的 SkuLimitBuyVO 对象。
     */
    Map <Long,SkuLimitBuyVO> querySkuLimitBuyMap(QuerySkuAvailableSaleReqVO reqVO);
}
