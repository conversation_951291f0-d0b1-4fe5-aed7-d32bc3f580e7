package com.jdi.isc.product.soa.service.manage.price.agreementPrice.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.biz.component.api.enums.JoySkyBizFlowTypeEnum;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.AgreementPriceDataSourceTypeEnums;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.CountryExtendPriceEnum;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.domain.apply.po.ApplyInfoPO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryIdVO;
import com.jdi.isc.product.soa.domain.enums.AuditFormEnum;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.SkuScopeEnum;
import com.jdi.isc.product.soa.domain.mku.po.MkuLangPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.*;
import com.jdi.isc.product.soa.domain.price.agreementPrice.draft.*;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPriceDraftPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.extendPrice.biz.CountryExtendPriceVO;
import com.jdi.isc.product.soa.domain.price.po.PriceLogPO;
import com.jdi.isc.product.soa.domain.sku.biz.ExternalVO;
import com.jdi.isc.product.soa.domain.sku.biz.ProductIdVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuExternalReqVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuLogPO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangVO;
import com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum;
import com.jdi.isc.product.soa.service.adapter.mapstruct.agreementPrice.CountryAgreementPriceConvert;
import com.jdi.isc.product.soa.service.adapter.mapstruct.agreementPrice.CountryAgreementPriceDraftConvert;
import com.jdi.isc.product.soa.service.atomic.apply.ApplyInfoAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.PriceLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuLogAtomicService;
import com.jdi.isc.product.soa.service.manage.brand.BrandOutService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuRelationManageService;
import com.jdi.isc.product.soa.service.manage.price.ProfitCalculateManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceDraftManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.agreement.AgreementPriceApproveService;
import com.jdi.isc.product.soa.service.manage.price.extendPrice.CountryExtendPriceManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuExternalManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.support.ProductIdGenerator;
import com.jdi.isc.product.soa.service.support.audit.AuditService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 国家协议价数据维护服务实现
 * @Author: wangpeng965
 * @Date: 2025/03/03 16:45
 **/

@Slf4j
@Service
public class CountryAgreementPriceDraftManageServiceImpl extends BaseManageSupportService<CountryAgreementPriceDraftVO, CountryAgreementPriceDraftPO> implements
    CountryAgreementPriceDraftManageService {

    @Resource
    private CountryAgreementPriceDraftAtomicService countryAgreementPriceDraftAtomicService;

    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;

    @Resource
    private MkuRelationManageService mkuRelationManageService;

    @Resource
    private CategoryOutService categoryOutService;

    @Resource
    private MkuLangAtomicService mkuLangAtomicService;

    @Resource
    private SkuLogAtomicService skuLogAtomicService;

    @Resource
    private ProductIdGenerator productIdGenerator;

    @Resource
    private AgreementPriceApproveService agreementPriceApproveService;

    @Resource
    private ApplyInfoAtomicService applyInfoAtomicService;

    @Resource
    private BrandOutService brandOutService;

    @Resource
    private SkuExternalManageService skuExternalManageService;

    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;

    @Resource
    private CountryManageService countryManageService;

    @Resource
    private PriceLogAtomicService priceLogAtomicService;

    @Resource
    private ProfitCalculateManageService profitCalculateManageService;

    @Resource
    private CountryAgreementPriceManageService countryAgreementPriceManageService;

    @Resource
    private CountryExtendPriceManageService countryExtendPriceManageService;

    @Resource
    private JimUtils jimUtils;

    @Resource
    private AuditService auditService;
    @Resource
    private SkuReadManageService skuReadManageService;

    @Override
    public PageInfo<CountryAgreementPricePageVO> pageSearch(CountryAgreementPricePageReqVO capVO) {
        CountryAgreementPriceDraftPageReqVO vo = CountryAgreementPriceDraftConvert.INSTANCE.reqVO2DraftReqVO(capVO);
        PageInfo<CountryAgreementPricePageVO> pageInfo = new PageInfo<>();
        pageInfo.setSize(vo.getSize());
        pageInfo.setIndex(vo.getIndex());
        this.buildQuery(vo);
        long total = countryAgreementPriceDraftAtomicService.pageSearchTotal(vo);
        if (total == 0) {
            pageInfo.setTotal(0);
            return pageInfo;
        }
        List<CountryAgreementPriceDraftPageVO> priceList = countryAgreementPriceDraftAtomicService.pageSearch(vo);
        if (CollectionUtils.isEmpty(priceList)){
            return pageInfo;
        }
        this.setMkuTitle(priceList);
        this.setCatIdAndName(priceList);
        this.setSkuId(priceList);
        this.setProfitRateForPage(priceList);
        pageInfo.setTotal(total);
        List<CountryAgreementPricePageVO> countryAgreementPricePageVOList = CountryAgreementPriceDraftConvert.INSTANCE.listDraft2List(priceList);
        pageInfo.setRecords(countryAgreementPricePageVOList);
        return pageInfo;
    }



    private void buildQuery(CountryAgreementPriceDraftPageReqVO vo){
        // 类目id转换为终极类目id
        Set<Long> lastCatIds = this.getLastCatIds(vo);
        vo.setCatIds(lastCatIds);
        if(CollectionUtils.isNotEmpty(vo.getProductIds())){
            ProductIdVO productIdVO = skuReadManageService.getProductInfoByIds(vo.getProductIds());
            if(CollectionUtils.isNotEmpty(productIdVO.getSkuIds())){
                vo.setSkuIds(productIdVO.getSkuIds());
            }
        }
    }


    /**
     * 根据 CountryAgreementPricePageReqVO 对象获取最后一级分类的 ID 集合。
     * @param countryAgreementPriceDraftPageReqVO 包含分类信息的请求对象。
     * @return 最后一级分类的 ID 集合。
     */
    private Set<Long> getLastCatIds(CountryAgreementPriceDraftPageReqVO countryAgreementPriceDraftPageReqVO){
        CategoryIdVO categoryIdVO = new CategoryIdVO();
        categoryIdVO.setFirstCatId(countryAgreementPriceDraftPageReqVO.getFirstCatId());
        categoryIdVO.setSecondCatId(countryAgreementPriceDraftPageReqVO.getSecondCatId());
        categoryIdVO.setThirdCatId(countryAgreementPriceDraftPageReqVO.getThirdCatId());
        categoryIdVO.setLastCatId(countryAgreementPriceDraftPageReqVO
            .getLastCatId());
        Set<Long> catIdSet = categoryOutService.queryLastCatId(categoryIdVO);
        return catIdSet;
    }

    /**
     * 设置MKU标题。
     * @param priceVOList 国际协议价格列表。
     */
    private void setMkuTitle(List<CountryAgreementPriceDraftPageVO> priceVOList){
        if(CollectionUtils.isEmpty(priceVOList)){
            return;
        }
        List<Long> mkuIds = priceVOList.stream().map(CountryAgreementPriceDraftPageVO::getMkuId).filter(Objects::nonNull).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(mkuIds)){
            return;
        }
        Map<Long, MkuLangPO> mkuLangMap = mkuLangAtomicService.getMkuLangNameByMkuIds(mkuIds, LangConstant.LANG_ZH);
        for (CountryAgreementPriceDraftPageVO vo : priceVOList){
            MkuLangPO mkuLangPOzh = mkuLangMap.get(vo.getMkuId());
            if(Objects.nonNull(mkuLangPOzh)){
                vo.setMkuTitle(mkuLangPOzh.getMkuTitle());
            }
        }
    }


    /**
     * 根据国家协议价格页列表设置对应的分类ID和名称。
     * @param pricePageVOList 国家协议价格页列表
     */
    private void setCatIdAndName(List<CountryAgreementPriceDraftPageVO> pricePageVOList){
        if(CollectionUtils.isEmpty(pricePageVOList)){
            return;
        }
        Set<Long> lastCatIds = pricePageVOList.stream().map(CountryAgreementPriceDraftPageVO::getLastCatId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> lastCatMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(lastCatIds)){
            Map<Long, String> tempMap = categoryOutService.queryPathStr(lastCatIds, LangContextHolder.get());
            if (MapUtils.isNotEmpty(tempMap)) {
                lastCatMap.putAll(tempMap);
            }
        }
        pricePageVOList.forEach(item->{
            item.setCatName(lastCatMap.get(item.getLastCatId()));
        });
    }


    /**
     * 根据国别协议价格页面VO列表设置对应的SKU ID列表。
     * @param priceVOList 国别协议价格页面VO列表。
     */
    private void setSkuId(List<CountryAgreementPriceDraftPageVO> priceVOList){
        if(CollectionUtils.isEmpty(priceVOList)){
            return;
        }
        for (CountryAgreementPricePageVO countryAgreementPricePageVO : priceVOList){
            List<Long> skuIds= new ArrayList<>();
            skuIds.add(countryAgreementPricePageVO.getSkuId());
            countryAgreementPricePageVO.setSkuIds(skuIds);
        }
    }

    /**
     * 设置每个页面的利润率。
     * @param priceVOList 包含国家协议价格和成本价格的列表。
     */
    private void setProfitRateForPage(List<CountryAgreementPriceDraftPageVO> priceVOList){
        if(CollectionUtils.isEmpty(priceVOList)){
            return;
        }
        for (CountryAgreementPriceDraftPageVO countryAgreementPriceDraftPageVO : priceVOList){

            if(Objects.isNull(countryAgreementPriceDraftPageVO.getCountryCostPrice()) ||
                Objects.isNull(countryAgreementPriceDraftPageVO.getAgreementPrice()) ||
                Objects.isNull(countryAgreementPriceDraftPageVO.getProfitRate())){
                countryAgreementPriceDraftPageVO.setProfitRate(null);
                continue;
            }
            countryAgreementPriceDraftPageVO.setProfitRate(countryAgreementPriceDraftPageVO.getProfitRate().multiply(Constant.DECIMAL_HUNDRED));
        }
    }

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> save(CountryAgreementPriceVO vo) {
            CountryAgreementPriceDraftVO capVO = CountryAgreementPriceDraftConvert.INSTANCE.vo2DraftVo(vo);
            CountryAgreementPriceReqVO countryAgreementPriceReqVO = createRequestVO(capVO);
            CountryAgreementPriceDraftPO draftPO = countryAgreementPriceDraftAtomicService.getCountryAgreementPriceDraft(countryAgreementPriceReqVO);
            if (Objects.nonNull(draftPO) && draftPO.getAuditStatus().equals(AuditStatusEnum.WAITING_APPROVED.getCode())) {
                log.info("有数据在审批中，无法新增信息");
                return DataResponse.error("有数据在审批中，无法新增信息");
            }

            CountryAgreementPricePO countryAgreementPrice = countryAgreementPriceAtomicService.getCountryAgreementPrice(countryAgreementPriceReqVO);
            if (countryAgreementPrice == null) {
                log.info("国家协议价未查询到信息");
                return DataResponse.error("国家协议价未查询到信息");
            }
            InitAgreementPriceVO initAgreementPriceVO = new InitAgreementPriceVO();
            initAgreementPriceVO.setSkuId(countryAgreementPrice.getSkuId());
            initAgreementPriceVO.setTargetCountryCode(countryAgreementPrice.getTargetCountryCode());
            initAgreementPriceVO.setSourceCountryCode(countryAgreementPrice.getSourceCountryCode());
            CountryExtendPriceVO countryJdPrice = countryExtendPriceManageService.getCountryExtendPrice(initAgreementPriceVO, CountryExtendPriceEnum.JD_PRICE.getCode());
            if (Objects.isNull(countryJdPrice)) {
                log.info("京东价未查询到信息");
                return DataResponse.error("京东价未查询到信息");
            }
            if (vo.getAgreementPrice().compareTo(countryJdPrice.getPrice()) > 0) {
                log.info("国家协议价不能大于京东价");
                return DataResponse.error("国家协议价不能大于京东价");
            }

            CountryAgreementPriceCalculateResVO calculateResVO = agreementPriceApproveService.getCalculateResVO(countryAgreementPrice, vo.getAgreementPrice());
            if (calculateResVO == null) {
                return DataResponse.error("国家协议价利润率为空");
            }

            vo.setCountryAgreementPriceCalculateResVO(calculateResVO);
            Integer auditStatus = this.determineAuditStatus(calculateResVO);

            this.updateCountryAgreementPrice(countryAgreementPrice, vo);
            CountryAgreementPriceDraftPO po = this.prepareDraftPO(countryAgreementPrice, calculateResVO, auditStatus);

            if (!this.saveDraft(po)) {
                log.warn("saveOrUpdate, MarkupRatePO fail. MarkupRatePO={}", JSONObject.toJSONString(po));
                return DataResponse.success(false);
            }

            // 自动通过
            if (this.isApproved(auditStatus)) {
                CountryAgreementPriceVO countryAgreementPriceVO = this.getAgreementPriceVO(countryAgreementPrice, po);
                countryAgreementPriceManageService.saveOrUpdate(countryAgreementPriceVO);
            } else {
                vo.setId(po.getId());
                // 提交joysky审批
                agreementPriceApproveService.submit(vo);
            }
            PriceLogPO priceLogPO = getPriceLogPO(po);
            priceLogAtomicService.save(priceLogPO);
            this.sendLog(po.getBizNo(), capVO, po);
            return DataResponse.success(true);
    }

    private PriceLogPO getPriceLogPO(CountryAgreementPriceDraftPO param){
        PriceLogPO priceLogPO = new PriceLogPO();
        priceLogPO.setBizId(param.getId().toString());
        priceLogPO.setBizType(PriceTypeEnum.COUNTRY_NEGOTIATED_PRICE_DRAFT.getCode());
        priceLogPO.setBizValue(param.getAgreementPrice());
        priceLogPO.setValue1(param.getSourceCountryCode());
        priceLogPO.setValue2(param.getTargetCountryCode());
        priceLogPO.setCreateTime(DateUtil.getCurrentTime());
        priceLogPO.setCreator(param.getCreator());
        priceLogPO.setUpdater(param.getUpdater());
        priceLogPO.setUpdateTime(DateUtil.getCurrentTime());
        return priceLogPO;
    }

    /**
     * 检查并初始化输入参数，若业务编号为空则自动生成。
     * @param capVO 国际协议价格信息对象
     * @return 操作成功的响应结果
     */
    private DataResponse<Boolean> checkAndInitInput(CountryAgreementPriceDraftVO capVO){
        if(Objects.isNull(capVO.getBizNo())){
            capVO.setBizNo(productIdGenerator.getAgreementPriceBizNo());
        }
        return DataResponse.success();
    }

    /**
     * 根据ID获取国家协议价格详细信息
     * @param reqVO 请求对象，包含要查询的国家协议价格的ID
     * @return 国家协议价格详细信息对象，如果ID对应的记录不存在则返回null
     */
    @Override
    public CountryAgreementPriceDraftVO detail(CountryAgreementPriceReqVO reqVO) {
        CountryAgreementPriceDraftReqVO countryAgreementPriceDraftReqVO = new CountryAgreementPriceDraftReqVO();
        countryAgreementPriceDraftReqVO.setSkuId(reqVO.getSkuId());
        countryAgreementPriceDraftReqVO.setSourceCountryCode(reqVO.getSourceCountryCode());
        countryAgreementPriceDraftReqVO.setTargetCountryCode(reqVO.getTargetCountryCode());
        countryAgreementPriceDraftReqVO.setAuditStatus(AuditStatusEnum.WAITING_APPROVED.getCode());
        CountryAgreementPriceDraftPO countryAgreementPriceDraftPO = countryAgreementPriceDraftAtomicService.getCountryAgreementPriceDraft(countryAgreementPriceDraftReqVO);
        CountryAgreementPriceDraftVO countryAgreementPriceDraftVO = CountryAgreementPriceDraftConvert.INSTANCE.po2Vo(countryAgreementPriceDraftPO);
        return countryAgreementPriceDraftVO;
    }



    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public Boolean batchJoySkyApprove(String erp, Integer status, String processInstanceId, String msg) {
        if(status == null || StringUtils.isBlank(processInstanceId)){
            throw new BizException("审批人或流程id为空");
        }

        ApplyInfoPO applyInfoPO = applyInfoAtomicService.getByBizTypeAndProcessInstanceId(JoySkyBizFlowTypeEnum.COUNTRY_AGREEMENT_EFFECTIVE_FLOW,processInstanceId);
        if(applyInfoPO != null && applyInfoPO.getAuditStatus() != null && AuditStatusEnum.WAITING_APPROVED.getCode() != applyInfoPO.getAuditStatus()){
            log.info(String.format("CountryAgreementPriceDraftManageServiceImpl.batchJoySkyApprove applyInfoPO 审批记录为空或审批状态不匹配,processInstanceId:%s",processInstanceId));
            return Boolean.FALSE;
        }

        CountryAgreementPriceDraftPO draftPO = countryAgreementPriceDraftAtomicService.getValidById(Long.valueOf(applyInfoPO.getBizId()));
        if(draftPO == null ||draftPO.getAuditStatus() == null
                || AuditStatusEnum.WAITING_APPROVED.getCode() != draftPO.getAuditStatus()){
            throw new BizException(String.format("CountryAgreementPriceDraftManageServiceImpl.batchJoySkyApprove detailDraftPO 记录为空或审批状态不匹配,applyInfoPO:%s",applyInfoPO.getBizId()));
        }

        CountryAgreementPriceAuditVO param = new CountryAgreementPriceAuditVO();
        param.setProcessInstanceId(processInstanceId);
        param.setAuditErp(erp);
        param.setRejectReason(msg);
        if(status.equals(AuditStatusEnum.APPROVED.getCode())){
            return agreementPriceApproveService.messageApprove(param);
        } else if (status.equals(AuditStatusEnum.REJECTED.getCode())){
            return agreementPriceApproveService.messageReject(param);
        } else if (status.equals(AuditStatusEnum.REVOKE.getCode())){
            return agreementPriceApproveService.messageRevoke(param);
        } else if (status.equals(AuditStatusEnum.APPROVING.getCode())){
            return agreementPriceApproveService.messageApproving(param);
        } else {
            log.error("status is error");
            return null;
        }
    }

    /**
     * 发送日志到数据库。
     * @param mkuId MKU的唯一标识。
     * @param sourceData 日志的源数据。
     * @param targetData 日志的目标数据。
     */
    private void sendLog(String mkuId, CountryAgreementPriceDraftVO sourceData, CountryAgreementPriceDraftPO targetData) {
        try {
            SkuLogPO skuLogPO = new SkuLogPO();
            skuLogPO.setSourceJson(JSONObject.toJSONString(sourceData));
            skuLogPO.setTargetJson(JSONObject.toJSONString(targetData));
            skuLogPO.setKeyType(KeyTypeEnum.AGREEMENT_PRICE_DRAFT.getCode());
            skuLogPO.setKeyId(mkuId);
            skuLogPO.setCreator(targetData.getUpdater());
            skuLogPO.setUpdater(targetData.getUpdater());
            skuLogAtomicService.save(skuLogPO);
            log.info("日志保存成功，MKU: {}", mkuId);
        } catch (Exception e) {
            log.error("存储日志异常，MKU: {},sourceData:{},targetData:{} ,Error: {}", mkuId, JSONObject.toJSONString(sourceData), JSONObject.toJSONString(targetData), e.getMessage(), e);
        }
    }

    private CountryAgreementPriceReqVO createRequestVO(CountryAgreementPriceDraftVO capVO) {
        CountryAgreementPriceReqVO reqVO = new CountryAgreementPriceReqVO();
        reqVO.setSourceCountryCode(capVO.getSourceCountryCode());
        reqVO.setTargetCountryCode(capVO.getTargetCountryCode());
        reqVO.setSkuId(capVO.getSkuId());
        reqVO.setAuditStatus(AuditStatusEnum.WAITING_APPROVED.getCode());
        return reqVO;
    }


    private Integer determineAuditStatus(CountryAgreementPriceCalculateResVO calculateResVO) {
        return (calculateResVO.getProfitRate().compareTo(calculateResVO.getProfitLimitRate()) >= 0)
                ? AuditStatusEnum.APPROVED.getCode()
                : null;
    }

    private void updateCountryAgreementPrice(CountryAgreementPricePO countryAgreementPrice, CountryAgreementPriceVO vo) {
        // 设置原始协议价和成本价，后面用户计算利润率
        vo.setOriginAgreementPrice(countryAgreementPrice.getAgreementPrice());
        vo.setOriginCountryCostPrice(countryAgreementPrice.getCountryCostPrice());

        countryAgreementPrice.setAgreementPrice(vo.getAgreementPrice());
        countryAgreementPrice.setAgreementMark(vo.getAgreementMark());
        countryAgreementPrice.setCreator(vo.getUpdater());
        countryAgreementPrice.setUpdater(vo.getUpdater());
        countryAgreementPrice.setCreateTime(System.currentTimeMillis());
        countryAgreementPrice.setUpdateTime(System.currentTimeMillis());
    }

    private CountryAgreementPriceDraftPO prepareDraftPO(CountryAgreementPricePO countryAgreementPrice, CountryAgreementPriceCalculateResVO calculateResVO, Integer auditStatus) {
        CountryAgreementPriceDraftPO po = CountryAgreementPriceDraftConvert.INSTANCE.po2Po(countryAgreementPrice);
        po.setId(null);
        po.setBizNo(countryAgreementPrice.getBizNo());
        po.setAuditStatus(auditStatus);
        po.setProfitRate(calculateResVO.getProfitRate());
        po.setWarningMsg(calculateResVO.getWarningMsg());
        po.setCreateTime(DateUtil.getCurrentTime());
        po.setCreator(LoginContextHolder.getLoginContextHolder().getPin());
        po.setUpdateTime(DateUtil.getCurrentTime());
        po.setUpdater(LoginContextHolder.getLoginContextHolder().getPin());
        return po;
    }

    private boolean saveDraft(CountryAgreementPriceDraftPO po) {
        return countryAgreementPriceDraftAtomicService.save(po);
    }

    private boolean isApproved(Integer auditStatus) {
        return auditStatus != null && auditStatus.equals(AuditStatusEnum.APPROVED.getCode());
    }

    @Override
    public PageInfo<CountryAgreementPriceAuditResVO> approvePageSearch(CountryAgreementPriceAuditReqVO param) {
        this.buildApproveQuery(param);
        long total = countryAgreementPriceDraftAtomicService.getApproveTotal(param);
        PageInfo<CountryAgreementPriceAuditResVO> result = new PageInfo<>();
        result.setIndex(param.getIndex());
        result.setSize(param.getSize());
        result.setTotal(total);
        if (total == 0) {
            result.setRecords(Collections.emptyList());
            return result;
        }
        // 分页查询草稿
        List<CountryAgreementPriceAuditResVO> dbRecordList = countryAgreementPriceDraftAtomicService.listApproveSearch(param);
        result.setRecords(dbRecordList);
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(result.getRecords())){
            // 设置skuName
            this.setSkuName(result.getRecords());
            // 设置品牌名称
            this.setBrandName(result.getRecords());
            // 设置类目名称
            this.setApproveCatIdAndName(result.getRecords());
            // 设置单号
            this.setApplyCode(result.getRecords());
            // 设置利润率乘以百
            this.setProfitRate(result.getRecords());
            // 设置利润率乘以百
            this.setCountryName(result.getRecords());
        }
        // 设置自定义列
        this.handleProcessFromData(result.getRecords());
        return result;
    }

    /**
     * 处理表单数据
     */
    private void handleProcessFromData(List<CountryAgreementPriceAuditResVO> dbRecordList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(dbRecordList)) {
            return;
        }

        for (CountryAgreementPriceAuditResVO item : dbRecordList) {
            String processFromData = item.getProcessFromData();

            // 获取动态列
            Map<String, String> customColumns = auditService.getCustomColumnMap(processFromData, AuditFormEnum.AGREEMENT_PRICE_AUDIT.getCode());

            item.setCustomColumns(customColumns);
        }
    }

    @Override
    public String batchApprove(CountryAgreementPriceAuditVO input) {
        List<Long> ids = input.getIds();
        List<CountryAgreementPriceDraftPO> draftPOS = countryAgreementPriceDraftAtomicService.getValidByIds(ids);
        if(CollectionUtils.isEmpty(draftPOS)){
            return Constant.SUCCESS;
        }

        List<ApplyInfoPO> applyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.COUNTRY_AGREEMENT_EFFECTIVE_FLOW,input.getIds());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(applyInfoPOS)){
            throw new BizException("无匹配数据,数据已变更，请刷新页面");
        }
        Map<String,ApplyInfoPO> applyInfoPOMap = applyInfoPOS.stream().collect(Collectors.toMap(ApplyInfoPO::getBizId, Function.identity()));

        String message = "";
        for(CountryAgreementPriceDraftPO draftPO : draftPOS){
            ApplyInfoPO applyInfoPO = applyInfoPOMap.get(draftPO.getId().toString());
            try{
                String targetCountryCode = draftPO.getTargetCountryCode();
                if(StringUtils.isBlank(targetCountryCode)){
                    log.error("CountryAgreementPriceDraftManageServiceImpl.batchApprove targetCountryCode is null, draftPO:{}",draftPO);
                    continue;
                }
                input.setIds(Lists.newArrayList(draftPO.getId()));
                agreementPriceApproveService.singleApprove(draftPO,input,applyInfoPO);
            }catch (Exception e){
                log.error("CountryAgreementPriceDraftManageServiceImpl.singleApprove error, draftPO:{}",draftPO,e);
                message = String.format("%s：%s%s", applyInfoPO.getApplyCode(), e.getMessage(),"\n");
            }
        }
        return StringUtils.isNotBlank(message) ? message : Constant.SUCCESS;
    }

    @Override
    public String batchReject(CountryAgreementPriceAuditVO input) {
        List<Long> ids = input.getIds();
        List<CountryAgreementPriceDraftPO> draftPOS = countryAgreementPriceDraftAtomicService.getValidByIds(ids);
        if(CollectionUtils.isEmpty(draftPOS)){
            return Constant.SUCCESS;
        }

        List<ApplyInfoPO> applyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.COUNTRY_AGREEMENT_EFFECTIVE_FLOW,input.getIds());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(applyInfoPOS)){
            throw new BizException("无匹配数据,数据已变更，请刷新页面");
        }
        Map<String,ApplyInfoPO> applyInfoPOMap = applyInfoPOS.stream().collect(Collectors.toMap(ApplyInfoPO::getBizId, Function.identity()));

        String message = "";
        for(CountryAgreementPriceDraftPO draftPO : draftPOS){
            ApplyInfoPO applyInfoPO = applyInfoPOMap.get(draftPO.getId().toString());
            try{
                String targetCountryCode = draftPO.getTargetCountryCode();
                if(StringUtils.isBlank(targetCountryCode)){
                    log.error("CountryAgreementPriceDraftManageServiceImpl.batchReject targetCountryCode is null, draftPO:{}",draftPO);
                    continue;
                }

                input.setIds(Lists.newArrayList(draftPO.getId()));
                agreementPriceApproveService.singleReject(draftPO,input,applyInfoPO);
            }catch (Exception e){
                log.error("CountryAgreementPriceDraftManageServiceImpl.singleReject error, draftPO:{}",draftPO,e);
                message = String.format("%s：%s%s", applyInfoPO.getApplyCode(), e.getMessage(),"\n");
            }
        }
        return StringUtils.isNotBlank(message) ? message : Constant.SUCCESS;
    }

    @Override
    public String batchRevoke(CountryAgreementPriceAuditVO input) {
        List<Long> ids = input.getIds();
        List<CountryAgreementPriceDraftPO> draftPOS = countryAgreementPriceDraftAtomicService.getValidByIds(ids);
        if(CollectionUtils.isEmpty(draftPOS)){
            return Constant.SUCCESS;
        }

        List<ApplyInfoPO> applyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.COUNTRY_AGREEMENT_EFFECTIVE_FLOW,input.getIds());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(applyInfoPOS)){
            throw new BizException("无匹配数据,数据已变更，请刷新页面");
        }
        Map<String,ApplyInfoPO> applyInfoPOMap = applyInfoPOS.stream().collect(Collectors.toMap(ApplyInfoPO::getBizId, Function.identity()));

        for(CountryAgreementPriceDraftPO draftPO : draftPOS){
            String targetCountryCode = draftPO.getTargetCountryCode();
            if(StringUtils.isBlank(targetCountryCode)){
                log.error("CountryAgreementPriceDraftManageServiceImpl.batchRevoke targetCountryCode is null, draftPO:{}",draftPO);
                continue;
            }

            input.setIds(Lists.newArrayList(draftPO.getId()));
            agreementPriceApproveService.singleRevoke(draftPO,input,applyInfoPOMap.get(draftPO.getId().toString()));
        }
        return Constant.SUCCESS;
    }

    private void buildApproveQuery(CountryAgreementPriceAuditReqVO param){
        param.setCurrentAuditor(LoginContextHolder.getLoginContextHolder().getPin());

        Set<Long> skuIdSet = new HashSet<>();
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getSkuIds())){
            skuIdSet.addAll(param.getSkuIds());
        }

        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getMkuIds())){
            List<MkuRelationPO> mkuRelationPOS = mkuRelationAtomicService.queryBindListByMkuIds(param.getMkuIds());
            Set<Long> skuIds = new HashSet<>();
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(mkuRelationPOS)){
                skuIds = mkuRelationPOS.stream().filter(item->item.getSkuId() != null)
                        .map(MkuRelationPO::getSkuId).collect(Collectors.toSet());
            }
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(skuIds)){
                if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(skuIdSet)){
                    skuIdSet.retainAll(skuIds);
                }else {
                    skuIdSet.addAll(skuIds);
                }
            }
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(skuIdSet)){
                skuIdSet.add(-1L);
            }
        }
        param.setSkuIds(new ArrayList<>(skuIdSet));

        // 类目id转换为终极类目id
        Set<Long> lastCatIds = this.getLastCatIds(param);
        param.setCatIds(lastCatIds);

        List<String> bizIds = applyInfoAtomicService.selectWaitAuditBizId(JoySkyBizFlowTypeEnum.COUNTRY_AGREEMENT_EFFECTIVE_FLOW,param.getApplyCode(),param.getCurrentAuditor());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(bizIds)){
            bizIds = new ArrayList<>();
            bizIds.add("-1");
        }

        param.setIds(bizIds);

        if(param.getProfitRateBegin() != null){
            param.setProfitRateBegin(param.getProfitRateBegin().divide(Constant.DECIMAL_HUNDRED,4, RoundingMode.HALF_UP));
        }

        if(param.getProfitRateEnd() != null){
            param.setProfitRateEnd(param.getProfitRateEnd().divide(Constant.DECIMAL_HUNDRED,4, RoundingMode.HALF_UP));
        }
    }

    private void setSkuName(List<CountryAgreementPriceAuditResVO> params){
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(params)){
            return;
        }
        List<Long> skuIds = params.stream().map(CountryAgreementPriceAuditResVO::getSkuId).collect(Collectors.toList());
        //补充sku名称
        SkuExternalReqVO skuQuery = new SkuExternalReqVO();
        skuQuery.setSkuIds(skuIds);
        skuQuery.setScopeEnums(Sets.newHashSet(SkuScopeEnum.BASE,SkuScopeEnum.LANG));
        Map<Long, ExternalVO> skuMap = skuExternalManageService.querySkuInfo(skuQuery);
        for (CountryAgreementPriceAuditResVO target : params) {
            ExternalVO skuLang = skuMap.get(target.getSkuId());
            if(skuLang!=null){
                SpuLangVO zhName = skuLang.getLangVOList().stream().filter(line -> LangConstant.LANG_ZH.equals(line.getLang())).findFirst().orElse(null);
                target.setSkuName(zhName!=null?zhName.getSpuTitle():null);
            }
        }
    }

    /**
     * 根据品牌ID列表查询品牌名称并设置到输入列表中。
     * @param inputs 包含品牌ID的 CountryMkuPageVO.Response 列表
     */
    private void setBrandName(List<CountryAgreementPriceAuditResVO> inputs){
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(inputs)){
            return;
        }
        Set<Long> brandIds = inputs.stream().map(CountryAgreementPriceAuditResVO::getBrandId).collect(Collectors.toSet());
        Map<Long, String> brandNameMap = new HashMap<>();
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(brandIds)){
            brandNameMap.putAll(brandOutService.queryNameByIds(brandIds,LangContextHolder.get()));
        }

        inputs.forEach(item->{
            item.setBrandName(brandNameMap.get(item.getBrandId()));
        });
    }

    /**
     * 设置单号
     * @param inputs 包含供应商代码和国家代码的列表
     */
    private void setApplyCode(List<CountryAgreementPriceAuditResVO> inputs){
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(inputs)){
            return;
        }

        List<Long> ids = inputs.stream().map(CountryAgreementPriceAuditResVO::getId).collect(Collectors.toList());
        List<ApplyInfoPO> applyInfoPOS = applyInfoAtomicService.getByBizTypeAndBizIds(JoySkyBizFlowTypeEnum.COUNTRY_AGREEMENT_EFFECTIVE_FLOW,ids);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(applyInfoPOS)){
            return;
        }

        Map<String, String> applyCodeMap = applyInfoPOS.stream()
                .collect(Collectors.toMap(
                        ApplyInfoPO::getBizId,           // key mapper
                        ApplyInfoPO::getApplyCode,    // value mapper
                        (existing, replacement) -> existing  // 处理重复key的情况
                ));
        inputs.forEach(item->{
            item.setApplyCode(applyCodeMap.get(item.getId().toString()));
        });
    }

    private void setProfitRate(List<CountryAgreementPriceAuditResVO> inputs){
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(inputs)){
            return;
        }

        inputs.forEach(item->{
            item.setProfitRate(item.getProfitRate()!= null?item.getProfitRate().multiply(Constant.DECIMAL_HUNDRED):null);
        });
    }

    /**
     * 根据国家协议价格页列表设置对应的分类ID和名称。
     * @param pricePageVOList 国家协议价格页列表
     */
    private void setApproveCatIdAndName(List<CountryAgreementPriceAuditResVO> pricePageVOList){
        if(CollectionUtils.isEmpty(pricePageVOList)){
            return;
        }
        Set<Long> lastCatIds = pricePageVOList.stream().map(CountryAgreementPriceAuditResVO::getLastCatId).collect(Collectors.toSet());
        Map<Long, String> lastCatMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(lastCatIds)){
            lastCatMap.putAll(categoryOutService.queryPathStr(lastCatIds, LangContextHolder.get()));
        }
        pricePageVOList.forEach(item->{
            item.setCatName(lastCatMap.get(item.getLastCatId()));
        });
    }

    private void setCountryName(List<CountryAgreementPriceAuditResVO> pricePageVOList){
        if(CollectionUtils.isEmpty(pricePageVOList)){
            return;
        }

        Map<String, String> countryMap = countryManageService.getCountryMap(LangConstant.LANG_ZH);
        pricePageVOList.forEach(item->{
            item.setSourceCountryName(countryMap.get(item.getSourceCountryCode()));
            item.setTargetCountryName(countryMap.get(item.getTargetCountryCode()));
        });
    }

    /**
     * 将 CountryAgreementPricePO 和 CountryAgreementPricePO 对象转换为 CountryAgreementPriceVO 对象。
     * @param countryAgreementPrice CountryAgreementPricePO 对象，包含源国家代码、目标国家代码、最后分类 ID 和 SKU ID 等信息。
     * @param po CountryAgreementPriceDraftPO 对象，包含协议价格、更新人和更新时间等信息。
     * @return 转换后的 CountryAgreementPriceVO 对象。
     */
    private CountryAgreementPriceVO getAgreementPriceVO(CountryAgreementPricePO countryAgreementPrice,CountryAgreementPriceDraftPO po){
        CountryAgreementPriceVO countryAgreementPriceVO = CountryAgreementPriceConvert.INSTANCE.po2Vo(countryAgreementPrice);
        countryAgreementPriceVO.setAgreementPrice(po.getAgreementPrice());
        countryAgreementPriceVO.setAgreementMark(po.getAgreementMark());
        countryAgreementPriceVO.setUpdater(po.getUpdater());
        countryAgreementPriceVO.setUpdateTime(po.getUpdateTime());
        countryAgreementPriceVO.setDataSourceTypeEnums(AgreementPriceDataSourceTypeEnums.AGREE);
        countryAgreementPriceVO.setAgreementUpdateTime(Instant.now().toEpochMilli());
        return countryAgreementPriceVO;
    }
}


