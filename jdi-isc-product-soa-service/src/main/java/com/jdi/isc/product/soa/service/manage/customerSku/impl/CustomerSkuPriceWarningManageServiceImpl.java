package com.jdi.isc.product.soa.service.manage.customerSku.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.product.soa.api.approveorder.common.ApproveOrderStatusEnum;
import com.jdi.isc.product.soa.api.approveorder.common.AuditBizTypeEnum;
import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.CustomerSkuDataSourceTypeEnums;
import com.jdi.isc.product.soa.api.common.enums.PriceAvailableSaleStatusEnum;
import com.jdi.isc.product.soa.api.customerSku.req.CustomerSkuPriceWarningDTO;
import com.jdi.isc.product.soa.api.pricewarn.enums.PriceWarnTypeEnum;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveOrderPO;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuPO;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPricePO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceAuditReqVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceWarningPageReqVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceWarningVO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailDraftPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDetailPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceDraftPO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceWarningPO;
import com.jdi.isc.product.soa.domain.enums.SkuScopeEnum;
import com.jdi.isc.product.soa.api.common.enums.CustomerSkuPriceWarningEnums;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.CountryAgreementPriceVO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.biz.ProfitRateVO;
import com.jdi.isc.product.soa.domain.price.po.PriceLogPO;
import com.jdi.isc.product.soa.domain.sku.biz.ExternalVO;
import com.jdi.isc.product.soa.domain.sku.biz.ProductIdVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuExternalReqVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuVO;
import com.jdi.isc.product.soa.service.atomic.approveorder.ApproveOrderAtomicService;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDetailDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerSku.CustomerSkuPriceWarningAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.PriceLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.manage.brand.BrandManageService;
import com.jdi.isc.product.soa.service.manage.category.CategoryManageService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceWarningManageService;
import com.jdi.isc.product.soa.service.manage.price.ProfitCalculateManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceManageService;
import com.jdi.isc.product.soa.service.manage.pricewarn.PriceWarnProducer;
import com.jdi.isc.product.soa.service.manage.sku.SkuExternalManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.specialAttr.SpecialAttrManageService;
import com.jdi.isc.product.soa.service.mapstruct.customerSku.CustomerSkuPriceConvert;
import com.jdi.isc.product.soa.service.support.transactional.ProductTransactionExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: sku客制化价格预警表数据维护服务实现
 * @Author: zhaokun51
 * @Date: 2025/03/17 16:51
 **/

@Slf4j
@Service
public class CustomerSkuPriceWarningManageServiceImpl extends BaseManageSupportService<CustomerSkuPriceWarningVO, CustomerSkuPriceWarningPO> implements CustomerSkuPriceWarningManageService {

    @Resource
    private CustomerSkuPriceWarningAtomicService customerSkuPriceWarningAtomicService;
    @Resource
    private SkuExternalManageService skuExternalManageService;
    @Resource
    private ProfitCalculateManageService profitCalculateManageService;
    @Resource
    private CustomerSkuPriceDetailAtomicService customerSkuPriceDetailAtomicService;
    @Resource
    private CustomerSkuPriceDraftAtomicService customerSkuPriceDraftAtomicService;
    @Resource
    private CustomerSkuPriceDetailDraftAtomicService customerSkuPriceDetailDraftAtomicService;
    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;
    @Resource
    private PriceLogAtomicService priceLogAtomicService;
    @Resource
    @Lazy
    private CountryAgreementPriceManageService countryAgreementPriceManageService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;
    @Resource
    private CustomerMkuPriceAtomicService customerMkuPriceAtomicService;
    @Resource
    private CustomerManageService customerManageService;


    @Value("${spring.profiles.active}")
    protected String systemProfile;

    @Resource
    private SkuReadManageService skuReadManageService;

    @Resource
    private ProductTransactionExecutor productTransactionExecutor;

    @Resource
    private CountryMkuAtomicService countryMkuAtomicService;

    @Resource
    private OperDuccConfig operDuccConfig;

    @Resource
    private CategoryManageService categoryManageService;

    @Resource
    private BrandManageService brandManageService;

    @Resource
    private SpecialAttrManageService specialAttrManageService;

    @Override
    public PageInfo<CustomerSkuPriceWarningVO> pageSearch(CustomerSkuPriceWarningPageReqVO input) {

        PageInfo<CustomerSkuPriceWarningVO> pageInfo = new PageInfo<>();
        pageInfo.setSize(input.getSize());
        pageInfo.setIndex(input.getIndex());
        input.setTestProduct(0);
        List<Long> skuIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(input.getSkuIds())){
            skuIdList.addAll(input.getSkuIds());
        }
        if(CollectionUtils.isNotEmpty(input.getProductIds())){
            ProductIdVO productIdVO = skuReadManageService.getProductInfoByIds(input.getProductIds());
            if(CollectionUtils.isNotEmpty(productIdVO.getSkuIds())){
                skuIdList.addAll(productIdVO.getSkuIds());
            }
        }
        input.setSkuIds(skuIdList);

        // 查询类目信息 换种实现方式
        Set<Long> lastCatIds;

        if (input.queryCategory()) {

            CustomerSkuPriceAuditReqVO query = new CustomerSkuPriceAuditReqVO(input.getFirstCatId(), input.getSecondCatId(), input.getThirdCatId(), input.getLastCatId());

            lastCatIds = SpringUtil.getBean(CustomerSkuPriceDraftManageServiceImpl.class).getLastCatIds(query);

            if (CollectionUtils.isEmpty(lastCatIds)) {
                return pageInfo;
            }
            input.setLastCatIds(lastCatIds);
        }

        long total = customerSkuPriceWarningAtomicService.getTotal(input);
        pageInfo.setTotal(total);
        if (total <= 0) {
            return pageInfo;
        }


        // 分页查询
        List<CustomerSkuPriceWarningVO> pageList = customerSkuPriceWarningAtomicService.listSearch(input);

//        // 查询列表
//        Page<CustomerSkuPriceWarningPO> pageDB = new Page<>(input.getIndex(), input.getSize());
//        // 构建查询条件
//        LambdaQueryWrapper<CustomerSkuPriceWarningPO> wrapper = this.buildWrapper(input);
//
//        Page<CustomerSkuPriceWarningPO> dbRecord = customerSkuPriceWarningAtomicService.page(pageDB, wrapper);
//        pageInfo.setTotal(dbRecord.getTotal());
//        if (CollectionUtils.isEmpty(dbRecord.getRecords())){
//            return pageInfo;
//        }

//        List<CustomerSkuPriceWarningVO> pageList = CustomerSkuPriceConvert.INSTANCE.warningListPo2Vo(pageList);
        if (CollectionUtils.isNotEmpty(pageList)){
            this.setSkuName(pageList);
            this.setBeginAndEndTime(pageList);
            this.setProfitRate(pageList);
            this.setBindStatus(pageList);
            // 设置价格可售状态、不可售阈值
            this.setPriceStatusInfo(pageList);
            // 设置审批状态
            this.setLatestAuditStatus(pageList);
            // 设置类目名称
            categoryManageService.setCatIdAndName(pageList, "jdCatId", "catName");
            // 设置品牌名称
            brandManageService.setBrandName(pageList, "brandId", "brandName");

            pageInfo.setRecords(pageList);
        }

        return pageInfo;
    }

    @Resource
    private ApproveOrderAtomicService approveOrderAtomicService;

    private void setLatestAuditStatus(List<CustomerSkuPriceWarningVO> pageList) {
        if (CollectionUtils.isEmpty(pageList)) {
            return;
        }
        List<String> ids = pageList.stream().map(item -> String.valueOf(item.getId())).collect(Collectors.toList());
        Map<String/*id*/, ApproveOrderPO> auditStatusMap = approveOrderAtomicService.getLatestAuditStatusMap(AuditBizTypeEnum.TYPE_2.getCode(), ids);


        List<Long> skuIds = pageList.stream().map(CustomerSkuPriceWarningVO::getSkuId).collect(Collectors.toList());
        Map<Long, SkuPO> skuMap = skuAtomicService.mapBySkuIdsAndYn(skuIds, null);

        for (CustomerSkuPriceWarningVO item : pageList) {
            ApproveOrderPO approveOrder = auditStatusMap.get(String.valueOf(item.getId()));

            SkuPO skuPO = skuMap.get(item.getSkuId());

            if (skuPO == null) {
                log.info("不存在sku信息，不补充审核信息，data={}", item.getSkuId());
                continue;
            }

            if (approveOrder == null) {
                log.info("找不到审核数据. id={}", item.getId());
                item.setShowAuditSubmitButton(true);
            } else {
                item.setAuditStatus(approveOrder.getAuditStatus());
                item.setApproveStatus(approveOrder.getStatus());
                item.setApproveId(approveOrder.getId());
                item.setApplyCode(approveOrder.getApplyCode());

                if (ApproveOrderStatusEnum.codeOf(approveOrder.getStatus()) == ApproveOrderStatusEnum.PROCESSING) {
                    if (StringUtils.isNotEmpty(approveOrder.getApplyCode())) {
                        item.setShowAuditRevokeButton(true);
                    }
                } else {
                    item.setShowAuditSubmitButton(true);
                }
            }

            // 可售不展示submit按钮
            if (item.getAvailableSaleStatus() != null && item.getAvailableSaleStatus() == PriceAvailableSaleStatusEnum.ENABLE.getCode()) {
                item.setShowAuditSubmitButton(false);
            }
        }
    }

    private void setPriceStatusInfo(List<CustomerSkuPriceWarningVO> pageList) {
        if (CollectionUtils.isEmpty(pageList)) {
            return;
        }

        List<Long> bizIds = pageList.stream().map(CustomerSkuPriceWarningVO::getBizId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        Map<Long, CustomerSkuPriceDetailPO> priceMap = customerSkuPriceDetailAtomicService.getMapByIds(bizIds);

        for (CustomerSkuPriceWarningVO item : pageList) {
            CustomerSkuPriceDetailPO price = priceMap.get(item.getBizId());
            if (price == null) {
                log.warn("找不到客制化价格信息. id={}", item.getId());
                continue;
            }
            item.setUnsellableThreshold(price.getUnsellableThreshold() != null ? price.getUnsellableThreshold().multiply(Constant.DECIMAL_HUNDRED) : null);
            item.setUnsellableThresholdTime(price.getUnsellableThresholdTime());
            item.setAvailableSaleStatus(price.getAvailableSaleStatus());
            item.setAvailableSaleStatusTime(price.getAvailableSaleStatusTime());
        }
    }

    @Resource
    private PriceWarnProducer priceWarnProducer;

    @Override
    public Boolean saveOrUpdateFromCustomerSku(Long detailDraftId, CustomerSkuDataSourceTypeEnums dataSourceTypeEnums) {
        CustomerSkuPriceWarningPO customerSkuPriceWarningPO = null;
        Long skuId = null;
        try{
            CustomerSkuPriceDetailDraftPO customerSkuPriceDetailDraftPO = customerSkuPriceDetailDraftAtomicService.getValidById(detailDraftId);
            CustomerSkuPriceDetailPO customerSkuPriceDetailPO = customerSkuPriceDetailAtomicService.getValidBySourceId(customerSkuPriceDetailDraftPO.getId());

            if (customerSkuPriceDetailPO == null) {
                log.warn("customerSkuPriceDetailPO is null, customerSkuPriceDetailDraftPO={}", JSONObject.toJSONString(customerSkuPriceDetailDraftPO));
                return false;
            }

            // 这里可能会有一个事物会滚的问题
            priceWarnProducer.sendMessage(PriceWarnTypeEnum.CUSTOMER_PRICE, customerSkuPriceDetailPO.getId(), dataSourceTypeEnums.getCode());


//            skuId = customerSkuPriceDetailPO.getSkuId();
//            customerSkuPriceWarningPO = this.getOrCreateSkuPriceWarningPO(customerSkuPriceDetailPO,dataSourceTypeEnums);
//            this.setProfitRateAndStatus(customerSkuPriceWarningPO,customerSkuPriceDetailDraftPO);
//            this.fillData(customerSkuPriceDetailPO, customerSkuPriceWarningPO);
//            customerSkuPriceWarningAtomicService.saveOrUpdate(customerSkuPriceWarningPO);
//            // 添加价格变更记录
//            PriceLogPO priceLogPO = this.getPriceLogPO(customerSkuPriceWarningPO,skuId);
//            priceLogAtomicService.save(priceLogPO);
        }catch (Exception e){
            log.error("CustomerSkuPriceWarningManageServiceImpl.saveOrUpdateFromCustomerSku error, clientCode:{}, skuId:{}", customerSkuPriceWarningPO == null ? null : customerSkuPriceWarningPO.getClientCode(), customerSkuPriceWarningPO.getSkuId(), e);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化价格预警预警,clientCode: %s, skuId:%s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P2.getMessage()
                    , customerSkuPriceWarningPO.getClientCode()
                    , customerSkuPriceWarningPO.getSkuId()
                    , e.getMessage()));
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    @Override
    public Boolean saveOrUpdateFromCostPrice(Long agreementPriceId, CustomerSkuDataSourceTypeEnums dataSourceTypeEnums) {
        try{
            CountryAgreementPricePO countryAgreementPricePO = countryAgreementPriceAtomicService.getValidById(agreementPriceId);
            Long skuId = countryAgreementPricePO.getSkuId();
            String currency = countryAgreementPricePO.getCurrency();
            List<CustomerSkuPriceDetailPO> customerSkuPriceDetailPOS = customerSkuPriceDetailAtomicService.getValidBySkuIdAndCurrency(skuId,currency);
            if(CollectionUtils.isEmpty(customerSkuPriceDetailPOS)){
                log.error("CustomerSkuPriceWarningManageServiceImpl.saveOrUpdateFromCostPrice is not need warning");
                return Boolean.TRUE;
            }

            for (CustomerSkuPriceDetailPO customerSkuPriceDetailPO : customerSkuPriceDetailPOS) {
                priceWarnProducer.sendMessage(PriceWarnTypeEnum.CUSTOMER_PRICE, customerSkuPriceDetailPO.getId(), dataSourceTypeEnums.getCode(), agreementPriceId);
            }

//            List<CustomerSkuPriceWarningPO> customerSkuPriceWarningPOS = new ArrayList<>();
//            for(CustomerSkuPriceDetailPO customerSkuPriceDetailPO : customerSkuPriceDetailPOS){
//                CustomerSkuPriceWarningPO customerSkuPriceWarningPO = this.getOrCreateSkuPriceWarningPO(customerSkuPriceDetailPO,dataSourceTypeEnums);
//                this.setProfitRateAndStatus(customerSkuPriceWarningPO,countryAgreementPricePO,customerSkuPriceDetailPO);
//
//                // 填充数据
//                this.fillData(customerSkuPriceDetailPO, customerSkuPriceWarningPO);
//
//                customerSkuPriceWarningPOS.add(customerSkuPriceWarningPO);
//            }
//            customerSkuPriceWarningAtomicService.saveOrUpdateBatch(customerSkuPriceWarningPOS);
//
//            List<PriceLogPO> priceLogPOS = new ArrayList<>();
//            for(CustomerSkuPriceWarningPO customerSkuPriceWarningPO : customerSkuPriceWarningPOS){
//                // 添加价格变更记录
//                PriceLogPO priceLogPO = this.getPriceLogPO(customerSkuPriceWarningPO,skuId);
//                priceLogPOS.add(priceLogPO);
//            }
//            priceLogAtomicService.saveBatch(priceLogPOS);

        }catch (Exception e){
            log.error("CustomerSkuPriceWarningManageServiceImpl.saveOrUpdateFromCostPrice error, agreementPriceId:{}", agreementPriceId, e);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_SPU_WARNING, String.format("【%s】%s sku客制化价格预警预警,agreementPriceId: %s, 异常信息:%s"
                    , systemProfile
                    , LevelCode.P2.getMessage()
                    , agreementPriceId
                    , e.getMessage()));
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    @Override
    public void fillData(CustomerSkuPriceDetailPO customerSkuPriceDetailPO, CustomerSkuPriceWarningPO customerSkuPriceWarningPO) {
        if (customerSkuPriceDetailPO == null || customerSkuPriceWarningPO == null) {
            log.warn("fillData 客制价为空. customerSkuPriceDetailPO={}, customerSkuPriceWarningPO={}", JSONObject.toJSONString(customerSkuPriceDetailPO), JSONObject.toJSONString(customerSkuPriceWarningPO));
            return;
        }

        Long skuId = customerSkuPriceDetailPO.getSkuId();
        // 查询sku
        SkuPO sku = skuAtomicService.getSkuPoBySkuId(skuId);

        if (sku != null) {
            customerSkuPriceWarningPO.setJdCatId(sku.getJdCatId());
            customerSkuPriceWarningPO.setBrandId(sku.getBrandId());
        }

        ProductIdVO productIdVO = skuReadManageService.getProductInfoByIds(Lists.newArrayList(skuId));

        if (CollectionUtils.isEmpty(productIdVO.getMkuIds())) {
            log.warn("找不到mku信息. skuId={}", skuId);
            return;
        }

        customerSkuPriceWarningPO.setAvailableSaleStatus(customerSkuPriceDetailPO.getAvailableSaleStatus());

        // 设置国家池状态
        CountryMkuPO countryMkuPO = countryMkuAtomicService.getByMkuIdCountryCode(productIdVO.getMkuIds().get(0), customerSkuPriceWarningPO.getTargetCountryCode());
        if (countryMkuPO != null) {
            customerSkuPriceWarningPO.setCountryMkuPoolStatus(countryMkuPO.getPoolStatus());
        } else {
            customerSkuPriceWarningPO.setCountryMkuPoolStatus(CountryMkuPoolStatusEnum.NOT_POOL.getCode());
            log.info("找不到国家池信息. mkuId={}", productIdVO.getMkuIds().get(0));
        }

        // 设置客户池状态
        String bindStatus = customerMkuAtomicService.getBindStatus(productIdVO.getMkuIds().get(0), customerSkuPriceWarningPO.getTargetCountryCode(), customerSkuPriceWarningPO.getClientCode(), operDuccConfig.getPreseletorClientCodeList());
        customerSkuPriceWarningPO.setCustomerMkuPoolStatus(bindStatus);

        if (skuId != null) {
            try {
                Integer testProduct = specialAttrManageService.isTestProduct(skuId);
                customerSkuPriceWarningPO.setTestProduct(testProduct);
            } catch (Exception e) {
                log.warn("客制化价格获取测试产品失败, skuId={}", skuId, e);
            }
        }

        log.info("填充数据. skuId={}, customerSkuPriceWarningPO={}", skuId, customerSkuPriceWarningPO);
    }

    @Override
    public void updateYn(List<Long> skuIds, String updater) {
        log.info("更新SKU客制化价格预警yn=0, skuIds={}, updater={}", skuIds, updater);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(skuIds)) {
            log.info("更新SKU客制化价格预警yn=0, skuIds is empty");
            return;
        }

        List<CustomerSkuPriceWarningPO> list = customerSkuPriceWarningAtomicService.listBySkuIds(skuIds);

        List<Long> ids = list.stream().map(CustomerSkuPriceWarningPO::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            log.info("更新SKU客制化价格预警yn=0, ids is empty");
            return;
        }

        LambdaUpdateWrapper<CustomerSkuPriceWarningPO> wrapper = Wrappers.<CustomerSkuPriceWarningPO>lambdaUpdate()
                .in(CustomerSkuPriceWarningPO::getId, ids);
        CustomerSkuPriceWarningPO update = new CustomerSkuPriceWarningPO();
        update.setYn(YnEnum.NO.getCode());
        update.setUpdateInfo(updater);

        boolean result = customerSkuPriceWarningAtomicService.update(update, wrapper);
        log.info("更新SKU客制化价格预警yn=0, 更新结果={}, ids={}, updater={}", result, ids, updater);
        if (!result) {
            log.error("更新SKU客制化价格预警yn=0, 更新失败. id={}, updater={}", ids, updater);
            throw new BizException("更新SKU客制化价格预警失败");
        }
    }

    @Override
    public List<CustomerSkuPriceWarningDTO> listWarningsByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<CustomerSkuPriceWarningPO> list = customerSkuPriceWarningAtomicService.listWarningsByIds(ids);

        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        return CustomerSkuPriceConvert.INSTANCE.convert(list);
    }


    /**
     * 列表查询条件生成
     * @return 查询条件Wrapper
     */
    private LambdaQueryWrapper<CustomerSkuPriceWarningPO> buildWrapper(CustomerSkuPriceWarningPageReqVO input){
        LambdaQueryWrapper<CustomerSkuPriceWarningPO> wrapper = Wrappers.<CustomerSkuPriceWarningPO>lambdaQuery();
        wrapper.eq(input.getWarningStatus()!= null,CustomerSkuPriceWarningPO::getWarningStatus, input.getWarningStatus());
        wrapper.eq(StringUtils.isNotBlank(input.getTargetCountryCode()),CustomerSkuPriceWarningPO::getTargetCountryCode, input.getTargetCountryCode());
        wrapper.in(CollectionUtils.isNotEmpty(input.getSkuIds()),CustomerSkuPriceWarningPO::getSkuId, input.getSkuIds());
        wrapper.eq(StringUtils.isNotBlank(input.getClientCode()),CustomerSkuPriceWarningPO::getClientCode, input.getClientCode());
        wrapper.eq(CustomerSkuPriceWarningPO::getYn, YnEnum.YES.getCode());
        wrapper.orderByDesc(CustomerSkuPriceWarningPO::getUpdateTime);

        return wrapper;
    }

    /**
     * 根据条件设置开始时间和结束时间为null。
     * @param inputs CustomerSkuPriceWarningVO 对象，包含开始时间和结束时间。
     */
    private void setBeginAndEndTime(List<CustomerSkuPriceWarningVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }

        inputs.forEach(input->{
            if(input.getBeginTime() != null && Constant.ZERO.equals(input.getBeginTime())){
                input.setBeginTime(null);
            }
            if(input.getEndTime() != null && Constant.MAX.equals(input.getEndTime())){
                input.setEndTime(null);
            }
        });
    }

    /**
     * 设置 SKU 名称。
     * @param targets 目标列表，包含 CustomerSkuPriceWarningVO 对象。
     */
    private void setSkuName(List<CustomerSkuPriceWarningVO> targets) {
        if(CollectionUtils.isEmpty(targets)){
            return;
        }
        List<Long> skuIds = targets.stream().map(CustomerSkuPriceWarningVO::getSkuId).collect(Collectors.toList());
        Map<Long, ExternalVO> externalVOMap = querySkuInfo(skuIds);
        for(CustomerSkuPriceWarningVO target :targets){
            ExternalVO skuLang = externalVOMap.get(target.getSkuId());
            if (skuLang != null) {
                SpuLangVO zhName = skuLang.getLangVOList().stream()
                        .filter(line -> LangConstant.LANG_ZH.equals(line.getLang()))
                        .findFirst().orElse(null);
                target.setSkuName(zhName != null ? zhName.getSpuTitle() : null);
                // 设置采销erp
                SpuVO spuVO = skuLang.getSpuVO();

                if (spuVO != null) {
                    target.setBuyerErp(spuVO.getBuyer());
                }
            }
        }

    }

    private Map<Long, ExternalVO> querySkuInfo(List<Long> skuIds) {
        SkuExternalReqVO skuQuery = new SkuExternalReqVO();
        skuQuery.setSkuIds(skuIds);
        skuQuery.setScopeEnums(Sets.newHashSet(SkuScopeEnum.BASE, SkuScopeEnum.LANG));
        return skuExternalManageService.querySkuInfo(skuQuery);
    }

    /**
     * 根据 CustomerSkuPriceDetailPO 对象获取或创建 CustomerSkuPriceWarningPO 对象。
     * @param param CustomerSkuPriceDetailPO 对象，包含客户 SKU 价格详细信息。
     * @return CustomerSkuPriceWarningPO 对象，包含客户 SKU 价格警告信息。
     */
    @Override
    public CustomerSkuPriceWarningPO getOrCreateSkuPriceWarningPO(CustomerSkuPriceDetailPO param,CustomerSkuDataSourceTypeEnums dataSourceTypeEnums) {
        Long customerSkuPriceDetailId = param.getId();
        CustomerSkuPriceWarningPO customerSkuPriceWarningPO = customerSkuPriceWarningAtomicService.getValidByBizId(customerSkuPriceDetailId);
        if (customerSkuPriceWarningPO == null) {
            customerSkuPriceWarningPO = new CustomerSkuPriceWarningPO();
            customerSkuPriceWarningPO.setCreateTime(DateUtil.getCurrentTime());
            customerSkuPriceWarningPO.setCreator(param.getCreator());
            customerSkuPriceWarningPO.setSkuId(param.getSkuId());
        } else {
            customerSkuPriceWarningPO.setSkuId(null);
        }

        customerSkuPriceWarningPO.setBizId(param.getId());
        customerSkuPriceWarningPO.setClientCode(param.getClientCode());
        customerSkuPriceWarningPO.setCurrency(param.getCurrency());
        customerSkuPriceWarningPO.setCustomerTradeType(param.getCustomerTradeType());
        customerSkuPriceWarningPO.setCustomerSalePrice(param.getCustomerSalePrice());
        customerSkuPriceWarningPO.setDataStatusSource(dataSourceTypeEnums.getCode());
        customerSkuPriceWarningPO.setBeginTime(param.getBeginTime());
        customerSkuPriceWarningPO.setEndTime(param.getEndTime());
        customerSkuPriceWarningPO.setUpdater(param.getUpdater());
        customerSkuPriceWarningPO.setUpdateTime(new Date().getTime());
        return customerSkuPriceWarningPO;
    }

    /**
     * 设置利润率和警告状态。
     * @param warningPO 客户SKU价格警告PO对象，包含利润率信息。
     * @param customerSkuPriceDetailDraftPO 客户SKU价格明细草稿PO对象，用于获取对应的客户SKU价格草稿PO对象。
     */
    private void setProfitRateAndStatus(CustomerSkuPriceWarningPO warningPO, CustomerSkuPriceDetailDraftPO customerSkuPriceDetailDraftPO){
        CustomerSkuPriceDraftPO customerSkuPriceDraftPO = customerSkuPriceDraftAtomicService.getValidById(customerSkuPriceDetailDraftPO.getBizId());
        if(customerSkuPriceDraftPO == null){
            log.error("CustomerSkuPriceManageServiceImpl.setProfitRateAndStatus customerSkuPriceDraftPO is null.id:{}" , customerSkuPriceDetailDraftPO.getBizId());
            return;
        }

        SkuPO skuPO = skuAtomicService.getSkuPoBySkuId(customerSkuPriceDetailDraftPO.getSkuId());
        if(skuPO == null){
            log.error("CustomerSkuPriceManageServiceImpl.setProfitRateAndStatus skuPO is null.id:{}" , customerSkuPriceDetailDraftPO.getBizId());
            return;
        }

        warningPO.setSourceCountryCode(skuPO.getSourceCountryCode());

        if(StringUtils.isBlank(customerSkuPriceDraftPO.getTargetCountryCode())){
            CustomerVO customerVO = customerManageService.detail(customerSkuPriceDetailDraftPO.getClientCode());
            if(customerVO != null){
                warningPO.setTargetCountryCode(customerVO.getCountry());
            }
        } else {
            warningPO.setTargetCountryCode(customerSkuPriceDraftPO.getTargetCountryCode());
        }

        BigDecimal rate = BigDecimal.ZERO;
        try{
            if(customerSkuPriceDetailDraftPO.getProfitRate() != null){
                rate = customerSkuPriceDetailDraftPO.getProfitRate();
            } else {
                CountryAgreementPriceVO countryAgreementPriceVO = countryAgreementPriceManageService.getCountryCostPrice(warningPO.getSourceCountryCode(),warningPO.getTargetCountryCode(),warningPO.getSkuId());
                BigDecimal countryCostPrice = countryAgreementPriceVO.getCountryCostPrice();
                rate = profitCalculateManageService.calculateSalePriceProfitRate(customerSkuPriceDetailDraftPO.getCustomerSalePrice(),countryCostPrice);
            }
        }catch (Exception e){
            log.error("CustomerSkuPriceManageServiceImpl.setProfitRateAndStatus error.id:{}", customerSkuPriceDetailDraftPO.getBizId(), e);
        }
        warningPO.setProfitRate(rate);

        BigDecimal profitRate = null;
        BigDecimal lowProfitRate = null;
        ProfitRateVO profitRateVO = profitCalculateManageService.getProfitLimitRate(skuPO.getJdCatId(), customerSkuPriceDraftPO.getSourceCountryCode(), customerSkuPriceDraftPO.getTargetCountryCode());
        if(profitRateVO != null){
            profitRate = profitRateVO.getProfitRate();
            lowProfitRate = profitRateVO.getLowProfitRate();
        }

        if(profitRate == null || lowProfitRate == null){
            log.error("CustomerSkuPriceManageServiceImpl.setProfitRateAndStatus source:{}," +
                    "profitRate:{},lowProfitRate:{} ","sku",profitRate,lowProfitRate);
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.PROFIT_RATE_LOSS.getCode());
            return;
        }

        warningPO.setProfitRateLimit(profitRate);
        warningPO.setProfitRateLowLimit(lowProfitRate);

        BigDecimal grossProfit = operDuccConfig.getGrossProfit(warningPO.getTargetCountryCode());

        log.info("CustomerSkuPriceWarningManageService3.setProfitRateAndStatus, grossProfit={}, rate={}, profitRate={}, lowProfitRate={}, targetCountryCode={}", grossProfit, rate, profitRate, lowProfitRate, warningPO.getTargetCountryCode());

        if(rate.compareTo(profitRate)>=0){
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.GREEN.getCode());
        } else if(rate.compareTo(lowProfitRate) >= 0 && rate.compareTo(profitRate) < 0){
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.YELLOW.getCode());
        } else if (rate.compareTo(grossProfit) > 0 && rate.compareTo(lowProfitRate) < 0){
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.RED.getCode());
        } else {
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.DEEP_RED.getCode());
        }
    }

    /**
     * 设置利润率和状态
     * @param warningPO 客户SKU价格预警对象
     * @param countryAgreementPricePO 国家协议价格对象
     * @param customerSkuPriceDetailPO 客户SKU价格详细对象
     */
    @Override
    public void setProfitRateAndStatus(CustomerSkuPriceWarningPO warningPO, CountryAgreementPricePO countryAgreementPricePO
            ,CustomerSkuPriceDetailPO customerSkuPriceDetailPO){
        CustomerSkuPriceDetailDraftPO customerSkuPriceDetailDraftPO = customerSkuPriceDetailDraftAtomicService.getValidById(customerSkuPriceDetailPO.getSourceId());
        if(customerSkuPriceDetailDraftPO == null){
            log.error("CustomerSkuPriceManageServiceImpl.setProfitRateAndStatus customerSkuPriceDetailDraftPO is null.id:{}" , customerSkuPriceDetailPO.getSourceId());
            return;
        }
        CustomerSkuPriceDraftPO customerSkuPriceDraftPO = customerSkuPriceDraftAtomicService.getValidById(customerSkuPriceDetailDraftPO.getBizId());
        if(customerSkuPriceDraftPO == null){
            log.error("CustomerSkuPriceManageServiceImpl.setProfitRateAndStatus customerSkuPriceDraftPO is null.id:{}" , customerSkuPriceDetailPO.getSourceId());
            return;
        }
        SkuPO skuPO = skuAtomicService.getSkuPoBySkuId(customerSkuPriceDetailDraftPO.getSkuId());
        if(skuPO == null){
            log.error("CustomerSkuPriceManageServiceImpl.setProfitRateAndStatus skuPO is null.id:{}" , customerSkuPriceDetailDraftPO.getBizId());
            return;
        }

        warningPO.setSourceCountryCode(customerSkuPriceDraftPO.getSourceCountryCode());
        warningPO.setTargetCountryCode(customerSkuPriceDraftPO.getTargetCountryCode());

        BigDecimal rate = null ;
        try{
            rate = profitCalculateManageService.calculateSalePriceProfitRate(warningPO.getCustomerSalePrice(),countryAgreementPricePO.getCountryCostPrice());
        }catch (Exception e){
            log.error("CustomerSkuPriceManageServiceImpl.setProfitRateAndStatus error 利润率为空",e);
        }
        warningPO.setProfitRate(rate);

        BigDecimal profitRate = null;
        BigDecimal lowProfitRate = null;
        ProfitRateVO profitRateVO = profitCalculateManageService.getProfitLimitRate(skuPO.getJdCatId(), customerSkuPriceDraftPO.getSourceCountryCode(), customerSkuPriceDraftPO.getTargetCountryCode());
        if(profitRateVO != null){
            profitRate = profitRateVO.getProfitRate();
            lowProfitRate = profitRateVO.getLowProfitRate();
        }

        if(rate == null || profitRate == null || lowProfitRate == null){
            log.error("CustomerSkuPriceManageServiceImpl.setProfitRateAndStatus rate:{} profitRate:{},"
                    + "lowProfitRate:{} ", rate,profitRate,lowProfitRate);
//            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.DEEP_RED.getCode());
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.PROFIT_RATE_LOSS.getCode());
            return;
        }

        warningPO.setProfitRateLimit(profitRate);
        warningPO.setProfitRateLowLimit(lowProfitRate);

        BigDecimal grossProfit = operDuccConfig.getGrossProfit(warningPO.getTargetCountryCode());

        if(rate.compareTo(profitRate)>=0){
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.GREEN.getCode());
        } else if(rate.compareTo(lowProfitRate) >= 0 && rate.compareTo(profitRate) < 0){
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.YELLOW.getCode());
        } else if (rate.compareTo(grossProfit) > 0 && rate.compareTo(lowProfitRate) < 0){
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.RED.getCode());
        } else {
            warningPO.setWarningStatus(CustomerSkuPriceWarningEnums.DEEP_RED.getCode());
        }
    }

    /**
     * 设置利润率的百分比表示。
     * @param inputs 国家的协议价格警告信息列表。
     */
    private void setProfitRate(List<CustomerSkuPriceWarningVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }

        inputs.forEach(item->{
            item.setProfitRate(item.getProfitRate() != null?item.getProfitRate().multiply(Constant.DECIMAL_HUNDRED):null);
            item.setProfitRateLimit(item.getProfitRateLimit() != null?item.getProfitRateLimit().multiply(Constant.DECIMAL_HUNDRED):null);
            item.setProfitRateLowLimit(item.getProfitRateLowLimit()!= null?item.getProfitRateLowLimit().multiply(Constant.DECIMAL_HUNDRED):null);
        });
    }

    /**
     * 设置利润率的百分比表示。
     * @param inputs 国家的协议价格警告信息列表。
     */
    private void setBindStatus(List<CustomerSkuPriceWarningVO> inputs){
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }

        Map<String, Set<Long>> clientSkuMap = inputs.stream()
                .collect(Collectors.groupingBy(
                        CustomerSkuPriceWarningVO::getClientCode,
                        Collectors.mapping(CustomerSkuPriceWarningVO::getSkuId, Collectors.toSet())
                ));
        Map<String, String> clientSkuBindMap = new HashMap<>();
        clientSkuMap.forEach((clientCode, skuIds) -> {
            List<CustomerMkuPricePO> customerMkuPricePOS = customerMkuPriceAtomicService.queryMkuListsBySkuIdClientCode(skuIds,clientCode);
            if(CollectionUtils.isEmpty(customerMkuPricePOS)){
                return;
            }

            Set<Long> mkuIds = customerMkuPricePOS.stream().map(CustomerMkuPricePO::getMkuId).collect(Collectors.toSet());
            List<CustomerMkuPO> customerMkuPOS = customerMkuAtomicService.listCustomerMkuPOByMkuIdsAndClientCode(mkuIds,clientCode);
            if(CollectionUtils.isEmpty(customerMkuPOS)){
                return;
            }

            Map<Long, CustomerMkuBindEnum> mkuBinMap = customerMkuPOS.stream()
                    .collect(Collectors.toMap(
                            CustomerMkuPO::getMkuId,
                            CustomerMkuPO::getBindStatus,
                            (existingValue, newValue) -> existingValue // 如果有重复键，保留现有值
                    ));

            for(CustomerMkuPricePO customerMkuPricePO : customerMkuPricePOS){
                CustomerMkuBindEnum bindEnum = mkuBinMap.get(customerMkuPricePO.getMkuId());
                if(bindEnum != null){
                    clientSkuBindMap.put(StringUtils.joinWith("_",clientCode,customerMkuPricePO.getFixedSkuId()),bindEnum.name());
                }
            }
        });

        inputs.forEach(item->{
            String key = StringUtils.joinWith("_",item.getClientCode(),item.getSkuId());
            String clientSkuBindStatus = clientSkuBindMap.get(key);
            if(StringUtils.isNotBlank(clientSkuBindStatus)){
                item.setBindStatus(clientSkuBindStatus);
                item.setBindStatusName(CustomerMkuBindEnum.valueOf(clientSkuBindStatus).getDesc());
            }
        });
    }

    /**
     * 根据CustomerSkuPriceDetailPO对象创建并返回PriceLogPO对象。
     * @param param CustomerSkuPriceDetailPO对象，包含客户SKU价格详细信息。
     */
    @Override
    public PriceLogPO getPriceLogPO(CustomerSkuPriceWarningPO param, Long skuId){
        PriceLogPO priceLogPO = new PriceLogPO();
        priceLogPO.setBizId(param.getId().toString());
        priceLogPO.setBizType(com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum.VIP_WARNING_DATA_SOURCE.getCode());
        priceLogPO.setBizValue(BigDecimal.valueOf(param.getDataStatusSource()));
        priceLogPO.setValue1(param.getClientCode());
        priceLogPO.setValue2(skuId.toString());
        priceLogPO.setCreateTime(DateUtil.getCurrentTime());
        priceLogPO.setCreator(param.getCreator());
        priceLogPO.setUpdater(param.getUpdater());
        priceLogPO.setUpdateTime(DateUtil.getCurrentTime());
        return priceLogPO;
    }
}
