package com.jdi.isc.product.soa.service.manage.saleAttribute;

import com.jdi.isc.product.soa.api.attribute.common.AttributeDTO;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SpuSaleAttributeVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 销售属性管理服务
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface SaleAttributeManageService {

    /**
     * 读接口1（detail）：通过spuId获取该spu下全部sku销售属性接口，包括草稿阶段和正式阶段
     *
     * @param spuId SPU ID
     * @param lang  语言编码
     * @return SPU销售属性对象，包含SPU下全量销售属性和SKU销售属性值映射
     */
    SpuSaleAttributeVO getSpuSaleAttributeDetail(Long spuId, String lang);

    /**
     * 获取sku销售属性详情
     *
     * @param skuId SKU ID
     * @param lang 语言编码
     * @return
     */
    List<PropertyVO> getSkuSaleAttributeDetail(Long skuId, String lang);

    /**
     * 获取草稿阶段sku销售属性详情，返回包含销售属性名和销售属性lang语音的返回值，用于页面展示（非编辑），包括分页list
     * 
     * @param skuKeySet
     * @param lang
     * @return
     */
    Map<String, List<PropertyValueVO>> getSkuDraftSaleAttributeDetailBySkuKeySet(Set<String> skuKeySet, String lang);

    /**
     * 获取正式阶段sku销售属性详情，返回包含销售属性名和销售属性lang语音的返回值，用于页面展示（非编辑），包括分页list
     *
     * @param skuIdSet
     * @param lang
     * @return
     */
    Map<Long, List<PropertyValueVO>> getSkuSaleAttributeDetailBySkuIdSet(Set<Long> skuIdSet, String lang);

    /**
     * 获取sku销售属性详情，PropertyValue返回多语言
     * @param skuId
     * @param langList
     * @return
     */
    List<PropertyVO> getSkuSaleAttributeDetailWithLangList(Long skuId, List<String> langList);

    /**
     * 获取mku销售属性详情
     *
     * @param mkuId MKU ID
     * @param lang 语言编码
     * @return
     */
    List<PropertyVO> getMkuSaleAttributeDetail(Long mkuId, String lang);


    /**
     * 获取中台sku销售属性值详情，分为图销、文销，用/拼接
     * 
     * @param jdSkuId 中台skuId
     * @return
     */
    List<PropertyValueVO> getJdSkuSaleAttributeDetail(Long jdSkuId, String lang);

    /**
     * 读接口2（prepare）：通过类目ID查询中台该类目的销售属性名称，照排序的顺序使用/进行拼接，并且存入销售属性表和销售属性多语言表（机翻）
     * @param jdCatId 中台类目ID
     * @return 包含属性类型（图销或文销）、属性名称（用/将中台属性名按图销文销拼接）的PropertyVO列表
     * 返回值样例：
     * [
     *   {
     *     "attributeId": 200001,
     *     "attributeName": "颜色",
     *     "attributeType": 3,
     *     "attributeInputType": 0,
     *     "sort": 1,
     *     "required": false,
     *     "propertyValueVOList": []
     *   },
     *   {
     *     "attributeId": 200002,
     *     "attributeName": "内存/存储容量",
     *     "attributeType": 3,
     *     "attributeInputType": 1,
     *     "sort": 2,
     *     "required": false,
     *     "propertyValueVOList": []
     *   }
     * ]
     */
    List<PropertyVO> getCategorySaleAttributesByJdCatId(Long jdCatId, String lang);

    /**
     * 写接口1：用于跨境品发品，excel批量导入，用销售属性是否包含来确定是excel导入还是编辑修改
     * @param skuId ISC SKU ID
     * @param jdSkuId 中台SKU ID
     * @return 操作结果
     */
    Boolean createSkuSaleAttributeForCrossBorder(Long spuId, Long skuId, Long jdSkuId);

    /**
     * 跨境品编辑，重新绑定jdskuid，更新销售属性
     * 单独校验销售属性中文值是否与中台一致，这里直接保存
     * 用于京麦更新和普通更新
     * @param spuId spuId
     * @param updateSkuVOList 更新后的skuVO列表
     * @return
     */
    Boolean updateSkuSaleAttributeForCrossBorder(Long spuId, List<SkuVO> updateSkuVOList);

    /**
     * 用于跨境品批量修改，属于更新，但是没有用户在前端输入销售属性的形式
     * 首先删除，然后新增
     * @param spuId spuId
     * @param skuId skuId
     * @param jdSkuId jdSkuId
     * @return
     */
    Boolean updateSkuSaleAttributeForCrossBorderWithoutUserInput(Long spuId, Long skuId, Long jdSkuId);

    /**
     * 跨境品编辑重新绑定jdskuid，校验销售属性中文值是否与中台一致
     * 
     * @param jdSkuId 中台skuId
     * @param storeSalePropertyList 销售属性值列表
     * @return
     */
    Boolean validateJdSkuSaleAttribute(Long jdSkuId, List<PropertyValueVO> storeSalePropertyList);

    /**
     * 跨境品有销售属性多语言的非中文值编辑，直接存储，这个方法起到数据转换作用，实际调用saveLocalOrEditCrossBorderSaleAttributeValueLangs
     * 如果jdskuId没有变化，调用这个方法，否则调用updateSkuSaleAttributeForCrossBorder，判断依据是销售属性值id是否存在
     * 用于京麦更新和普通更新
     * @param skuVOList 更新后的skuVO列表
     * @return
     */
    Boolean updateSkuSaleAttributeValueLangNameForCrossBorder(List<SkuVO> skuVOList);

    /**
     * 草稿阶段保存本土品SKU销售属性（使用skuKey），对已经创建的商品及其销售属性，不允许修改，只能新增（新spu下新增，或在原有spu下新增）
     * 这个接口会自动过滤掉已经创建的销售属性值的sku（查询销售属性值sku关系表），用spuId+中文销售属性值名称去重
     * 本土品销售属性没有编辑，包括销售属性值的多语言，但是spu本身有编辑，在编辑阶段可以新增sku和销售属性，对于销售属性来说也是新增（create），
     * 用skuKey是否有绑定关系来判断是否是新增，过滤已存在关系的sku，仅对新增的sku创建销售属性
     *
     * skuKey不能改变，已经修改程序，如果改变，直接抛异常！
     *
     * @param spuId spuId
     * @param skuVOList 更新后的skuVO列表
     * @return
     */
    Boolean createSkuSaleAttributeForLocalSkuDraft(Long spuId, List<SkuVO> skuVOList);

    /**
     * 将草稿阶段的销售属性关系转换为正式关系
     * @param skuKeyToSkuIdMap SKU Key到SKU ID的映射
     * @return 转换结果
     */
    Boolean updateDraftSaleAttributeRelationsForLocalSkuApprovel(Map<String, Long> skuKeyToSkuIdMap);

    /**
     * 类目迁移时，将spu销售属性值绑定到目标类目的销售属性ID
     * @param targetCatId
     * @param spuId
     * @return
     */
    Boolean transferSkuSaleAttribute(Long targetCatId, Long spuId);

    /**
     * 将 PropertyVO 列表转换为 AttributeVO 列表
     * @return 转换后的 AttributeVO 列表
     */
    List<AttributeVO> convertPropertyVo2AttributeVos(List<PropertyVO> inputs);


    /**
     * 将 AttributeVO 列表转换为 AttributeDTO 列表。
     * @param inputs 需要转换的 AttributeVO 列表。
     * @return 转换后的 AttributeDTO 列表。
     */
    List<AttributeDTO> convertAttributeVOsToDTOs(List<AttributeVO> inputs);

} 