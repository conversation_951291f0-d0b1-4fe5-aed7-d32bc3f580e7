package com.jdi.isc.product.soa.service.manage.taxRate.countryTax.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.HuSkuTaxPO;
import com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum;
import com.jdi.isc.product.soa.price.api.price.req.HuSkuTaxVO;
import com.jdi.isc.product.soa.service.adapter.mapstruct.countryTax.HuSkuTaxConvert;
import com.jdi.isc.product.soa.service.atomic.taxRate.countryTax.HuSkuTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.HuSkuTaxManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 匈牙利税率管理服务
 * <AUTHOR>
 * @date 2025/3/10
 */
@Slf4j
@Service
public class HuSkuTaxManageServiceImpl extends BaseTaxManageService implements HuSkuTaxManageService {

    @Resource
    private HuSkuTaxAtomicService huSkuTaxAtomicService;

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> saveOrUpdate(HuSkuTaxVO input) {
        boolean flag;
        //将关税除以100保存
        input.setImportTax(input.getImportTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setValueAddedTax(input.getValueAddedTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setAntiSubsidyTax(input.getAntiSubsidyTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setAntiDumpingTax(input.getAntiDumpingTax().divide(factor,5, RoundingMode.HALF_UP));
        JdProductDTO skuPO = getSku(input.getJdSkuId());
        if(skuPO==null){
            return DataResponse.error(String.format("零售skuId:%s 不存在,请检查",input.getJdSkuId()));
        }
        HuSkuTaxPO res = huSkuTaxAtomicService.getOne(input);
        if(res==null){
            HuSkuTaxPO target = HuSkuTaxConvert.INSTANCE.vo2Po(input);
            target.setCreateTime(new Date());
            target.setUpdateTime(target.getCreateTime());
            flag = huSkuTaxAtomicService.save(target);
            recordLog(target.getJdSkuId(), PriceTypeEnum.HU_TAX, target.getImportTax(),target,flag);
        }else {
            input.setId(res.getId());
            flag = huSkuTaxAtomicService.updateTax(input);
            recordLog(input.getJdSkuId(), PriceTypeEnum.HU_TAX, input.getImportTax(),input,flag);
        }
        return DataResponse.success(flag);
    }

}
