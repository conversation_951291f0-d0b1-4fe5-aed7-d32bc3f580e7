package com.jdi.isc.product.soa.service.atomic.sku;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.enums.YesOrNoEnum;
import com.jdi.isc.product.soa.domain.sku.po.SkuFeaturePO;
import com.jdi.isc.product.soa.repository.mapper.sku.SkuFeatureBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * sku属性字原子类
 *
 * <AUTHOR>
 * @date 2024/4/23
 **/
@Service
public class SkuFeatureAtomicService extends ServiceImpl<SkuFeatureBaseMapper, SkuFeaturePO> {

    /**
     * 查询sku特殊属性
     * @param skuId 商品ID
     * @return sku特殊属性
     */
    public SkuFeaturePO getSkuFeatureBySkuId(Long skuId) {
        if (Objects.isNull(skuId)) {
            return null;
        }
        return this.getBaseMapper().selectOne(Wrappers.lambdaQuery(SkuFeaturePO.class)
                .eq(SkuFeaturePO::getSkuId, skuId).eq(SkuFeaturePO::getYn, YnEnum.YES.getCode()));
    }

    /**
     * 批量查询sku特殊属性
     *
     * @param skuIds sku集合
     * @return skuId和属性映射
     */
    public Map<Long, SkuFeaturePO> querySkuFeatureMap(Set<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<SkuFeaturePO> queryWrapper = Wrappers.lambdaQuery(SkuFeaturePO.class).in(SkuFeaturePO::getSkuId, skuIds).eq(SkuFeaturePO::getYn, YnEnum.YES.getCode());
        List<SkuFeaturePO> skuFeaturePoList = this.getBaseMapper().selectList(queryWrapper);
        return Optional.ofNullable(skuFeaturePoList).orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull).collect(Collectors.toMap(SkuFeaturePO::getSkuId, Function.identity()));
    }


//    /**
//     * @function 查询具有备货模式的SKU ID集合
//     * @param spuId 商品的统一标识ID
//     * @returns 返回一个包含SKU ID的集合，这些SKU属于指定的SPU且具有购买模型
//     */
//    public Set<Long> queryPurchaseModelSkus(Long spuId) {
//        LambdaQueryWrapper<SkuFeaturePO> queryWrapper = Wrappers.lambdaQuery(SkuFeaturePO.class).eq(SkuFeaturePO::getSpuId, spuId).eq(SkuFeaturePO::getPurchaseModel, YesOrNoEnum.YES.getCode()).eq(SkuFeaturePO::getYn, YnEnum.YES.getCode());
//        List<SkuFeaturePO> skuFeaturePoList = this.getBaseMapper().selectList(queryWrapper);
//        return Optional.ofNullable(skuFeaturePoList).orElseGet(ArrayList::new).stream().map(SkuFeaturePO::getSkuId).collect(Collectors.toSet());
//    }

    /**
     * 获取spuId和国际编码，增加审核状态
     *
     * @param spuIds spuId数组
     * @return 返回spu信息
     */
    public List<SkuFeaturePO> getSkuInfoByspuIds(List<Long> spuIds) {
        return super.getBaseMapper().selectList(new LambdaQueryWrapper<SkuFeaturePO>().select(SkuFeaturePO::getSpuId,
                SkuFeaturePO::getEnterprise,SkuFeaturePO::getOrgCode,SkuFeaturePO::getContactsName,SkuFeaturePO::getContactsPhone,
                SkuFeaturePO::getOriginCountry,SkuFeaturePO::getMagnetic,SkuFeaturePO::getElectric,SkuFeaturePO::getLiquid,
                SkuFeaturePO::getPowder,SkuFeaturePO::getSkuId).in(SkuFeaturePO::getSpuId, spuIds).eq(SkuFeaturePO::getYn, YnEnum.YES.getCode()));
    }

    /**
     * 按照spuIds查询到List<SkuFeaturePO>数据
     * @param spuIds
     * @return
     */
    public Map<Long,List<SkuFeaturePO>> querySkuFeaturePOMapBySpuIds(List<Long> spuIds){
        List<SkuFeaturePO> skuFeaturePOS = this.getSkuInfoByspuIds(spuIds);
        return Optional.ofNullable(skuFeaturePOS).orElseGet(ArrayList::new)
                .stream().collect(Collectors.groupingBy(SkuFeaturePO::getSpuId));
    }

    /**
     * 根据 SKU ID 集合查询 SKU 特征列表
     * @param skuIds SKU ID 集合
     * @return SKU 特征列表
     */
    public List<SkuFeaturePO> queryListByskuIds(Set<Long> skuIds){
        LambdaQueryWrapper<SkuFeaturePO> lambdaQueryWrapper = Wrappers.lambdaQuery(SkuFeaturePO.class).in(SkuFeaturePO::getSkuId, skuIds).eq(SkuFeaturePO::getYn, YnEnum.YES.getCode());
        List<SkuFeaturePO> skuFeaturePoList = super.list(lambdaQueryWrapper);
        return skuFeaturePoList;
    }
}
