package com.jdi.isc.product.soa.service.protocol.jsf.spu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.api.common.BasicReqDTO;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.spu.IscProductSoaSpuWriteApiService;
import com.jdi.isc.product.soa.api.spu.req.*;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.frame.annotation.ParamForSpu;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.service.manage.spu.ProductToolService;
import com.jdi.isc.product.soa.service.manage.spu.SpuDescriptionManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuTransferCategoryManageService;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuConvert;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuTransferConvert;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import com.jdi.isc.product.soa.service.support.DataResponseMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant.SPU_AUDIT_LOCK_FAILED;

/**
 * SPU写服务实现类
 * <AUTHOR>
 * @date 2024/7/15
 */
@Service
@Slf4j
public class IscProductSoaSpuWriteApiServiceImpl implements IscProductSoaSpuWriteApiService {

    @Resource
    private SpuManageService spuManageService;

    @Resource
    private ProductToolService productToolService;

    @Resource
    private SpuTransferCategoryManageService spuTransferCategoryManageService;

    @Resource
    private DataResponseMessageService dataResponseMessageService;

    @Resource
    private SpuDescriptionManageService spuDescriptionManageService;
    @Resource
    protected JimUtils jimUtils;

    @Override
    @ToolKit(exceptionWrap = true)
//    @ParamForSpu
    public DataResponse<Long> create(SaveSpuApiDTO input) {
        log.info("IscProductSoaSpuWriteApiServiceImpl.create param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        SaveSpuVO param = SpuConvert.INSTANCE.saveSpuDto2Vo(input);
        return spuManageService.create(param);
    }

    @Override
    @ToolKit(exceptionWrap = true)
//    @ParamForSpu
    public DataResponse<Long> update(SaveSpuApiDTO input) {
        log.info("IscProductSoaSpuWriteApiServiceImpl.update param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        SaveSpuVO param = SpuConvert.INSTANCE.saveSpuDto2Vo(input);
        return spuManageService.update(param);
    }

    @Override
//    @ParamForSpu
    @ToolKit(exceptionWrap = true)
    public DataResponse<Long> submit(SaveSpuApiDTO input) {
        log.info("IscProductSoaSpuWriteApiServiceImpl.submit param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        SaveSpuVO param = SpuConvert.INSTANCE.saveSpuDto2Vo(input);
        if(param.getSpuVO().getAuditStatus() == null){
            param.getSpuVO().setAuditStatus(AuditStatusEnum.WAITING_APPROVED.getCode());
        }
        return spuManageService.submit(param);
    }

    @Override
    @ToolKit(exceptionWrap = true)
//    @ParamForSpu
    public DataResponse<Long> saveDraft(SaveSpuApiDTO input) {
        log.info("IscProductSoaSpuWriteApiServiceImpl.saveDraft param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        SaveSpuVO param = SpuConvert.INSTANCE.saveSpuDto2Vo(input);
        return spuManageService.saveDraft(param);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchApprove(SpuAuditRecordUpdateApiDTO spuAuditRecordReqVO) {
        log.info("IscProductSoaSpuWriteApiServiceImpl.batchApprove param:{}", JSONObject.toJSONString(spuAuditRecordReqVO));
        ApiInitUtils.init(spuAuditRecordReqVO);
        SpuAuditRecordUpdateVO param = SpuConvert.INSTANCE.spuAuditDto2Vo(spuAuditRecordReqVO);
        return spuManageService.batchApprove(param);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchReject(SpuAuditRecordUpdateApiDTO spuAuditRecordReqVO) {
        log.info("IscProductSoaSpuWriteApiServiceImpl.batchReject param:{}", JSONObject.toJSONString(spuAuditRecordReqVO));
        ApiInitUtils.init(spuAuditRecordReqVO);
        SpuAuditRecordUpdateVO param = SpuConvert.INSTANCE.spuAuditDto2Vo(spuAuditRecordReqVO);
        return spuManageService.batchReject(param);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchArchive(BatchProductReqDTO reqDTO) {
        log.info("IscProductSoaSpuWriteApiServiceImpl.batchArchive param:{}", JSONObject.toJSONString(reqDTO));
        ApiInitUtils.init(reqDTO);
        SpuBatchReqVO spuBatchReqVO = new SpuBatchReqVO();
        spuBatchReqVO.setSpuIds(reqDTO.getSpuIds());

        BasicReqDTO basicReqDTO = reqDTO.getBasicReqDTO();
        spuBatchReqVO.setSourceCountryCode(basicReqDTO.getSourceCountryCode());
        spuBatchReqVO.setVendorCode(basicReqDTO.getVendorCode());
        spuBatchReqVO.setPin(basicReqDTO.getPin());
        return spuManageService.batchArchive(spuBatchReqVO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchDown(BatchProductReqDTO reqDTO) {
        log.info("IscProductSoaSpuWriteApiServiceImpl.batchDown param:{}", JSONObject.toJSONString(reqDTO));
        ApiInitUtils.init(reqDTO);
        SpuBatchReqVO spuBatchReqVO = new SpuBatchReqVO();
        spuBatchReqVO.setSpuIds(reqDTO.getSpuIds());

        BasicReqDTO basicReqDTO = reqDTO.getBasicReqDTO();
        spuBatchReqVO.setSourceCountryCode(basicReqDTO.getSourceCountryCode());
        spuBatchReqVO.setVendorCode(basicReqDTO.getVendorCode());
        spuBatchReqVO.setPin(basicReqDTO.getPin());
        return spuManageService.batchDown(spuBatchReqVO);
    }

    @Override
    public DataResponse<List<SpuUpdateApiDTO>> updateSpu(List<SpuUpdateApiDTO> spuDtoList) {
        log.info("IscProductSoaSpuWriteApiServiceImpl.updateSpu start param:{}", JSONObject.toJSONString(spuDtoList));
        BaseReqDTO dto = new BaseReqDTO();
        dto.setPin(spuDtoList.get(0).getUpdater());
        dto.setLang(LangConstant.LANG_ZH);
        ApiInitUtils.init(dto);
        List<SpuUpdateReqVO> spuUpdateReqVOList = SpuConvert.INSTANCE.apiList2ReqVO(spuDtoList);
        DataResponse<List<SpuUpdateReqVO>> response = spuManageService.batchUpdate(spuUpdateReqVOList);
        List<SpuUpdateApiDTO> spuUpdateApiDTOS = SpuConvert.INSTANCE.reqVO2ApiList(response.getData());
        log.info("IscProductSoaSpuWriteApiServiceImpl.updateSpu end param:{},res:{}", JSONObject.toJSONString(spuDtoList), JSONObject.toJSONString(spuUpdateApiDTOS));
        return DataResponse.success(spuUpdateApiDTOS);
    }


    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchDelete(BatchProductReqDTO reqDTO){
        ApiInitUtils.init(reqDTO);
        List<String> spuIdMsg = new ArrayList<>();
        for (Long spuId : reqDTO.getSpuIds()){
            String requestId = UUID.randomUUID().toString();
            String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.SPU_AUDIT_OPERATE_LOCK_PRE, String.valueOf(spuId));
            try {
                // 加排他锁，同时只有一个审核操作可以审核当前商品
                AssertValidation.isTrue(!jimUtils.simpleLock(lockKey, requestId, 60), SPU_AUDIT_LOCK_FAILED, "加锁失败");
                Set<Long> spuIdSet = new HashSet<>();
                spuIdSet.add(spuId);
                DataResponse<String> response = productToolService.updateSpuYn(spuIdSet, reqDTO.getYn(), reqDTO.getLang());
                if(!response.getSuccess()){
                    spuIdMsg.add(spuId  + ":" + response.getMessage());
                }
            } catch (BizException bizException) {
                log.error("【系统异常】IscProductSoaSpuWriteApiServiceImpl.batchDelete error spuId={}", JSON.toJSONString(spuId), bizException);
                String msg = spuId  + ":" + bizException.getMsg();
                spuIdMsg.add(msg);
            } catch (Exception e) {
                String errorMessage = dataResponseMessageService.getErrorMessage(DataResponseCodeConstant.SPU_UPDATE_ERROR);
                String msg = spuId  + ":" + errorMessage;
                spuIdMsg.add(msg);
                log.error("【系统异常】IscProductSoaSpuWriteApiServiceImpl.batchDelete error spuId={},message={}", spuId, errorMessage);
            } finally {
                jimUtils.simpleLockRelease(lockKey, requestId);
            }
        }
        if(CollectionUtils.isEmpty(spuIdMsg)){
            return DataResponse.success();
        }
        return DataResponse.error(String.join(",", spuIdMsg));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchAmend(List<SpuAmendReqDTO> inputs) {
        log.info("IscProductSoaSpuWriteApiServiceImpl.batchAmend param:{}", JSONObject.toJSONString(inputs));
        ApiInitUtils.init(inputs.get(0));
        List<SpuAmendReqVO> params = SpuConvert.INSTANCE.listAmendReqDto2Vo(inputs);
        return spuManageService.batchAmend(params);
    }

    @Override
    public DataResponse<Long> updatePrice(SpuUpdatePriceReqDTO dto) {
        // 1。 spuId skuId  货源国code，供应商code，未税采购价，含税采购价，未税销售价，含税销售价格，
        // updateType 1 未税采购价更新含税采购价， 2 含税采购价，更新未税采购价，
        // 3 未税销售价，更新含税销售价格，4 含税销售价格，更新未税销售价
        log.info("IscProductSoaSpuWriteApiServiceImpl.updatePrice param:{}", JSONObject.toJSONString(dto));
        ApiInitUtils.init(dto);
        SpuUpdatePriceReqVO spuUpdatePriceReqVO = SpuConvert.INSTANCE.spuUpdatePriceDTO2VO(dto);
        DataResponse<Long> response = spuManageService.updatePrice(spuUpdatePriceReqVO);
        return response;
    }


    @Override
    public DataResponse<Boolean> batchUpdateStock(StockManageReqDTO dto){
        return spuManageService.updateStockAndDraft(dto);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> transferCategory(TransferCategoryReqDTO reqDTO) {
        ApiInitUtils.init(reqDTO.getUpdater(),LangConstant.LANG_ZH,null);
        TransferCategoryReqVO reqVO = SpuTransferConvert.INSTANCE.dto2vo(reqDTO);
        return spuTransferCategoryManageService.transferCategory(reqVO);
    }

    @Override
    public DataResponse<Boolean> updateSpuSkuMku(SpuUpdateApiDTO dto) {
        log.info("IscProductSoaSpuWriteApiServiceImpl.updateSpuSKuMku start dto:{}", JSONObject.toJSONString(dto));
        ApiInitUtils.init(dto.getUpdater(),LangConstant.LANG_ZH,null);
        SpuUpdateReqVO spuUpdateReqVO = SpuConvert.INSTANCE.api2ReqVO(dto);
        DataResponse<Boolean> response = spuManageService.updateSpuSkuMku(spuUpdateReqVO);
        log.info("IscProductSoaSpuWriteApiServiceImpl.updateSpuSkuMku end param:{},res:{}", JSONObject.toJSONString(dto),response.getSuccess());
        return response;
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> updateDescription(SpuDescriptionUpdateApiDTO dto) {
        log.info("IscProductSoaSpuWriteApiServiceImpl.updateDescription start dto:{}", JSONObject.toJSONString(dto));
        ApiInitUtils.init(dto.getPin(),LangConstant.LANG_ZH,dto.getSystemCode());
        SpuDescriptionUpdateVO param = SpuConvert.INSTANCE.apiDescription2ReqVO(dto);
        return DataResponse.success(spuDescriptionManageService.updateDescription(param.getSkuIds()));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchApproveTax(SpuAuditTaxApproveDTO input) {
        log.info("IscProductSoaSpuWriteApiServiceImpl.batchApproveTax param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        SpuAuditTaxApproveVO param = SpuConvert.INSTANCE.spuTaxAuditDto2Vo(input);
        return spuManageService.batchApproveTax(param);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchRejectTax(SpuAuditTaxApproveDTO input) {
        log.info("IscProductSoaSpuWriteApiServiceImpl.batchRejectTax param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input);
        SpuAuditTaxApproveVO param = SpuConvert.INSTANCE.spuTaxAuditDto2Vo(input);
        return spuManageService.batchRejectTax(param);
    }

    @Override
    public DataResponse<String> updateSpuVendorCode(BatchProductReqDTO reqDTO) {
        ApiInitUtils.init(reqDTO);
        SpuBatchReqVO param = new SpuBatchReqVO();
        param.setSpuIds(reqDTO.getSpuIds());
        if(reqDTO.getBasicReqDTO() == null){
            throw new BizException("来源国和供应商代码不能为空");
        }
        param.setVendorCode(reqDTO.getBasicReqDTO().getVendorCode());
        param.setSourceCountryCode(reqDTO.getBasicReqDTO().getSourceCountryCode());
        return spuManageService.updateSpuVendorCode(param);
    }

    @Override
    public DataResponse<String> refreshSpuExtAttribute(Long skuId) {
        return spuManageService.refreshSpuExtAttribute(skuId);
    }

}
