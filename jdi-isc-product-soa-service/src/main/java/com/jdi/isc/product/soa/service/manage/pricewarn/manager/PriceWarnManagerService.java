package com.jdi.isc.product.soa.service.manage.pricewarn.manager;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.pricewarn.dto.PriceWarnMqDTO;

/**
 * The interface Price warn manager service.
 *
 * <AUTHOR>
 */
public interface PriceWarnManagerService {

    /**
     * Send price warn data response.
     *
     * @param input the input
     * @return the data response
     */
    DataResponse<String> writePriceWarn(PriceWarnMqDTO input);

    /**
     * 发送指定业务ID的告警消息
     * @param bizId 业务ID，唯一标识需要处理的具体业务
     * @param warnType 告警类型，标识告警的类别或级别
     * @param dataSourceType 数据源类型，标识告警数据的来源
     */
    void sendMessage(Long bizId, Integer warnType, Integer dataSourceType);
}
