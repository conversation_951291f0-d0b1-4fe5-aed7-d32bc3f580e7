package com.jdi.isc.product.soa.service.manage.price.agreementPrice;

import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.price.req.PriceAvailableSaleStatusReqDTO;
import com.jdi.isc.product.soa.api.price.req.PriceUnsellableThresholdImportReqDTO;
import com.jdi.isc.product.soa.api.price.res.PriceAvailableSaleStatusResDTO;
import com.jdi.isc.product.soa.api.price.res.PriceUnsellableThresholdImportResDTO;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveOrderPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.biz.*;
import com.jdi.isc.product.soa.domain.price.biz.MkuPriceAvailableVO;
import com.jdi.isc.product.soa.domain.price.extendPrice.biz.CurrencyPriceVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 国家协议价数据维护服务
 * @Author: wangpeng965
 * @Date: 2025/03/03 16:45
 **/

public interface CountryAgreementPriceManageService {

    /**
     * 保存或更新国家协议价格信息。
     * @param input 国家协议价格信息的VO对象，包含必要的字段如国家代码、协议类型、价格等。
     * @return 操作结果，true表示成功，false表示失败。
     */
    DataResponse<Boolean> saveOrUpdate(CountryAgreementPriceVO input);

    /**
     * 根据 CountryAgreementPriceReqVO 对象获取相应的 CountryAgreementPriceVO 对象的详细信息。
     * @param vo CountryAgreementPriceReqVO 对象，包含查询条件。
     * @return CountryAgreementPriceVO 对象，包含查询结果的详细信息。
     */
    CountryAgreementPriceVO detailById(CountryAgreementPriceReqVO vo);

    /**
     * 根据 SKU ID 获取国家协议价格详细信息
     * @param vo 国家协议价格请求对象
     * @return 国家协议价格详细信息对象
     */
    CountryAgreementPriceVO detailBySkuId(CountryAgreementPriceReqVO vo);

    /**
     * 分页查询国家协议价格信息。
     * @param input 查询请求对象，包含分页信息和过滤条件。
     * @return 分页结果，包含总记录数、当前页码、每页记录数和数据列表。
     */
    PageInfo<CountryAgreementPricePageVO> pageSearch(CountryAgreementPricePageReqVO input);

    /**
     * 根据请求获取国家协议价格信息
     * @param vo 国家协议价格请求对象
     * @return 国家协议价格信息对象
     */
    @PFTracing
    CountryAgreementPriceVO getCountryAgreementPrice(CountryAgreementPriceReqVO vo);

    CurrencyPriceVO calculateCountryCostPrice(InitAgreementPriceVO vo);

    CurrencyPriceVO calculateFulfillmentCost(InitAgreementPriceVO vo);

    Boolean initAgreementPrice(InitAgreementPriceVO vo,CurrencyPriceVO currencyPriceVO);

    /**
     * 根据源国家代码、目标国家和 SKU ID 获取国家间的成本价格。
     * @param sourceCountryCode 源国家的 ISO 3166-1 alpha-2 代码。
     * @param targetCountryCode 目标国家的名称。
     * @param skuId SKU 的唯一标识符。
     * @return 国家间的成本价格。
     */
    CountryAgreementPriceVO getCountryCostPrice(String sourceCountryCode, String targetCountryCode, Long skuId);


    /**
     * 更新国家协议价格
     * @param input 国家协议价格请求体
     * @return 更新结果
     */
    DataResponse<Boolean> updateAgreementPriceAndJdPrice(CountryAgreementPriceReqVO input);

    /**
     * 申请更新协议和京东价格。
     * @param input 更新协议和京东价格的请求参数。
     * @return 更新操作的结果。
     */
    DataResponse<Boolean> applyForUpdateAgreementAndJdPrice(CountryAgreementPriceReqVO input);

    /**
     * 批量查询国家协议价和国家成本价
     * @param reqVO 国家协议价查询入参，skuIds和targetCountryCode
     * @return key 为skuId，value：对应的协议价和成本价对象
     */
    Map<Long, CountryAgreementPricePageVO> queryCountryAgreementPriceBySkuIds(CountryAgreementPriceReqVO reqVO);


    /**
     * 根据初始化的协议价格信息计算协议价格和成本价格。
     * @param vo 初始化的协议价格信息
     * @return 包含协议价格和成本价格的 AgreementAndCostPriceVO 对象
     */
    AgreementAndCostPriceVO calculateAgreementAndCostPrice(InitAgreementPriceVO vo);

    /**
     * 更新国家协议价, yn=0.
     *
     * @param skuIds   the sku ids
     * @param finalPin the final pin
     */
    void updateYn(List<Long> skuIds, String finalPin);

    /**
     * 根据国家协议价格ID列表查询国家协议价格信息
     * @param ids 国家协议价格ID列表
     * @return 国家协议价格信息列表
     */
    List<CountryAgreementPriceVO> listByIds(List<Long> ids);

    /**
     * 更新不可售价格阈值
     * @param input 不可售价格阈值导入请求DTO，包含需要更新的阈值信息
     * @return 包含不可售价格阈值导入响应DTO的数据响应对象
     */
    DataResponse<PriceUnsellableThresholdImportResDTO> updateUnsellableThreshold(PriceUnsellableThresholdImportReqDTO input);

    /**
     * 更新商品可售状态
     * @param input 价格可售状态请求DTO列表，包含需要更新的商品状态信息
     * @return 包含价格可售状态响应DTO的数据响应对象
     */
    DataResponse<PriceAvailableSaleStatusResDTO> updateAvailableSaleStatus(List<PriceAvailableSaleStatusReqDTO> input);

    /**
     * 根据审批ID更新不可售阈值
     */
    void updateUnsellableThresholdByApproveId(ApproveOrderPO order);

    /**
     * 批量更新协议对应的池状态
     * @param agreementIds 需要更新状态的协议ID集合，不可为空
     */
    void updatePoolStatus(Set<Long> agreementIds);

    /**
     * 根据国家、协议价格 SKU ID 列表和 MKU 固定 SKU 映射获取 MKU 价格可用状态列表
     * @param targetCountryCode 国家代码
     * @param agreementPriceSkuIds 协议价格 SKU ID 列表
     * @param mkuFixedSkuMap MKU 固定 SKU 映射表
     * @return MKU 价格可用状态列表
     */
    List<MkuPriceAvailableVO> listMkuPriceAvailableStatus(String targetCountryCode, List<Long> agreementPriceSkuIds, Map<Long, Long> mkuFixedSkuMap);
}
