package com.jdi.isc.product.soa.service.manage.approveorder.template.callback.joysky;


import com.alibaba.fastjson.JSONObject;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.biz.component.api.joysky.res.JoySkyDetailInfoDTO;
import com.jdi.isc.product.soa.api.approveorder.common.ApproveOrderStatusEnum;
import com.jdi.isc.product.soa.api.approveorder.common.AuditActionEnum;
import com.jdi.isc.product.soa.api.approveorder.common.AuditStatusEnum;
import com.jdi.isc.product.soa.api.approveorder.mq.JoySkyBizApprovalResultMqDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiResDTO;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.common.util.validation.ValidationUtil;
import com.jdi.isc.product.soa.domain.approveorder.biz.ApproveNodeVO;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveOrderPO;
import com.jdi.isc.product.soa.rpc.suport.AlertHelper;
import com.jdi.isc.product.soa.rpc.utils.ExceptionUtil;
import com.jdi.isc.product.soa.service.atomic.approveorder.ApproveOrderAtomicService;
import com.jdi.isc.product.soa.service.manage.approveorder.context.JoySkyBizCallbackContext;
import com.jdi.isc.product.soa.service.manage.approveorder.manager.JoySkyManager;
import com.jdi.isc.product.soa.service.support.transactional.ProductTransactionExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.Objects;

import static com.jdi.isc.product.soa.domain.enums.AuditStatusEnum.getEnumByCode;

/**
 * joySky审批流回调.
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractJoySkyCallbackMessageExe {

    @Resource
    protected ProductTransactionExecutor productTransactionExecutor;

    @Resource
    protected ApproveOrderAtomicService approveOrderAtomicService;

    @Resource
    protected JimUtils jimUtils;

    @Resource
    protected JoySkyManager joySkyManager;


    /**
     * 获取业务类型
     *
     * @return 业务类型标识符 biz type
     */
    public abstract int getBizType();

    /**
     * 获取当前bean的名称
     *
     * @return 返回当前bean的名称字符串 bean name
     */
    public abstract String getBeanName();

    /**
     * Execute data response.
     *
     * @param input the input
     * @return the data response
     */
    @PFTracing
    public DataResponse<AuditApiResDTO> execute(JoySkyBizApprovalResultMqDTO input, ApproveOrderPO order) {

        Instant start = Instant.now();
        // 校验数据
        Pair<Boolean, String> checkParam = this.checkParam(input);

        if (!checkParam.getKey()) {
            log.info("AbstractJoySkyCallbackMessageExe, 校验参数未通过. param={}, checkParam={}", JSONObject.toJSONString(input), checkParam.getValue());
            return DataResponse.error(checkParam.getValue());
        }

        JoySkyBizCallbackContext context = new JoySkyBizCallbackContext(input);
        context.setOrder(order);

        Pair<Boolean, String> lockPair = this.tryLock(context);

        if (!lockPair.getKey()) {
            return DataResponse.error(lockPair.getValue());
        }

        try {
            // 校验数据
            this.checkData(context);

            if (context.isIdempotent()) {
                log.info("AbstractJoySkyCallbackMessageExe, 幂等操作. ");
                return DataResponse.success();
            }

            com.jdi.isc.product.soa.domain.enums.AuditStatusEnum statusEnum = getEnumByCode(input.getProcessStatus());

            switch (Objects.requireNonNull(statusEnum)) {
                case APPROVED:
                    this.messageApprove(context);
                    break;
                case REJECTED:
                    this.messageReject(context);
                    break;
                case REVOKE:
                    this.messageRevoke(context);
                    break;
                case APPROVING:
                    this.messageApproving(context);
                    break;
                default:
                    log.error("无法匹配流程状态, processStatus:{}", statusEnum);
                    throw new ProductBizException("无法匹配流程状态 %s", statusEnum.getCode());
            }

        } catch (Exception e) {
            log.error("保存审批单失败, param={}", JSONObject.toJSONString(input), e);
            return DataResponse.error(ExceptionUtil.getMessage(e, "保存审批单失败，请稍后重试！"));
        } finally {
            log.error("处理joySky回调消息, param={}, cost={}", JSONObject.toJSONString(input), Duration.between(start, Instant.now()).toMillis());
            // 释放锁
            this.releaseLock(context);
        }

        // 返回结果
        return DataResponse.success();
    }

    /**
     * 处理中间状态审核.
     *
     * @param context the context
     */
    protected void messageApproving(JoySkyBizCallbackContext context) {
        ApproveOrderPO order = context.getOrder();
        JoySkyBizApprovalResultMqDTO input = context.getInput();

        JoySkyDetailInfoDTO joySkyDetailInfo = joySkyManager.getJoySkyDetailInfo(order.getProcessInstanceId(), order.getFlowType());

        if (joySkyDetailInfo == null) {
            throw new ProductBizException("找不到流程信息. %s", order.getProcessInstanceId());
        }

        // 查询最新状态防止消息挤压导致更新错误问题
        if (joySkyDetailInfo.getAuditStatus() == null) {
            log.warn("审批流数据未返回审批状态，无法确定该流程. joySkyDetailInfo={}", JSONObject.toJSONString(joySkyDetailInfo));
            AlertHelper.p1("审批流数据未返回审批状态，无法确定该流程", joySkyDetailInfo.getApplyCode());
            return;
        }

        if (joySkyDetailInfo.getAuditStatus() != com.jdi.isc.product.soa.domain.enums.AuditStatusEnum.APPROVING.getCode()) {
            log.warn("审批流不是审核中，数据可能存在超时未处理等问题. joySkyDetailInfo={}", JSONObject.toJSONString(joySkyDetailInfo));
            return;
        }

        // 更新流程信息
        String reason = input.getRejectReason();

        // 计算审批单状态
        int auditAction = AuditActionEnum.PASS.getCode();

        productTransactionExecutor.execute(() -> {
            String currentNodeErp = null;
            int currentNodeAuditStatus = AuditStatusEnum.STATUS_6.getCode();

            // 如果还有审批人则更新节点审批状态为带审批，开启下一轮的审批工作
            if (CollectionUtils.isNotEmpty(joySkyDetailInfo.getTodoList())) {
                currentNodeAuditStatus = AuditStatusEnum.STATUS_2.getCode();
                currentNodeErp = String.join(",", joySkyDetailInfo.getTodoList());
            }

            // 构建需要更新的节点信息
            ApproveNodeVO updateInfo = this.buildApproveNode(null, auditAction, currentNodeErp, currentNodeAuditStatus, reason);

            approveOrderAtomicService.updateAuditNodeInfo(order, updateInfo);
        });
    }

    /**
     * 处理撤回请求.
     *
     * @param context the context
     */
    protected void messageRevoke(JoySkyBizCallbackContext context) {
        ApproveOrderPO order = context.getOrder();
        JoySkyBizApprovalResultMqDTO input = context.getInput();
        // 更新流程信息
        String reason = input.getRejectReason();

        // 计算审批单状态
        int auditStatus = AuditStatusEnum.STATUS_5.getCode();
        int currentNodeAuditStatus = AuditStatusEnum.STATUS_5.getCode();
        int auditAction = AuditActionEnum.CANCEL.getCode();

        this.buildMessageApproveRevokeParam(context);

        productTransactionExecutor.execute(() -> {
            // 构建需要更新的节点信息
            ApproveNodeVO updateInfo = this.buildApproveNode(auditStatus, auditAction, null, currentNodeAuditStatus, reason);
            approveOrderAtomicService.updateAuditNodeInfo(order, updateInfo);
            this.executeApproveRevokeBiz(context);
        });

        // 审核撤回业务扩展
        this.approvePassBizRevokeExtend(context);
    }

    protected void approvePassBizRevokeExtend(JoySkyBizCallbackContext ignoredContext) {

    }

    /**
     * 处理驳回请求.
     *
     * @param context the context
     */
    protected void messageReject(JoySkyBizCallbackContext context) {
        ApproveOrderPO order = context.getOrder();
        JoySkyBizApprovalResultMqDTO input = context.getInput();
        // 更新流程信息
        String reason = input.getRejectReason();

        // 计算审批单状态
        int auditStatus = AuditStatusEnum.STATUS_4.getCode();
        int currentNodeAuditStatus = AuditStatusEnum.STATUS_4.getCode();
        int auditAction = AuditActionEnum.REJECT.getCode();

        this.buildMessageApproveRejectParam(context);

        productTransactionExecutor.execute(() -> {
            // 构建需要更新的节点信息
            ApproveNodeVO updateInfo = this.buildApproveNode(auditStatus, auditAction, null, currentNodeAuditStatus, reason);

            approveOrderAtomicService.updateAuditNodeInfo(order, updateInfo);
            this.executeApproveRejectBiz(context);
        });

        // 审核通过业务扩展
        this.approvePassBizRejectExtend(context);
    }

    protected void approvePassBizRejectExtend(JoySkyBizCallbackContext ignoredContext) {

    }

    /**
     * 构建消息审批拒绝参数
     *
     * @param ignoredContext 业务回调上下文对象，包含审批流程所需的相关信息
     */
    protected void buildMessageApproveRejectParam(JoySkyBizCallbackContext ignoredContext) {

    }

    /**
     * 执行审批/拒绝业务逻辑
     *
     * @param ignoredContext JoySky业务回调上下文对象，包含审批流程所需数据
     */
    protected void executeApproveRejectBiz(JoySkyBizCallbackContext ignoredContext) {

    }

    /**
     * 构建消息审批通过所需的参数
     *
     * @param ignoredContext JoySky业务回调上下文对象，包含审批流程所需的相关信息
     */
    protected void buildMessageApprovePassParam(JoySkyBizCallbackContext ignoredContext) {

    }

    /**
     * 执行审批通过业务逻辑
     *
     * @param context 业务回调上下文对象，包含审批流程所需的相关数据
     */
    protected abstract void executeApprovePassBiz(JoySkyBizCallbackContext context);

    /**
     * 构建消息审批撤销参数
     *
     * @param ignoredContext JoySky业务回调上下文对象
     */
    protected void buildMessageApproveRevokeParam(JoySkyBizCallbackContext ignoredContext) {

    }

    /**
     * 审批/撤销业务执行方法
     *
     * @param ignoredContext 业务回调上下文对象，包含审批/撤销操作所需的业务数据
     */
    protected void executeApproveRevokeBiz(JoySkyBizCallbackContext ignoredContext) {

    }

    /**
     * Message approve.
     *
     * @param context the context
     */
    protected void messageApprove(JoySkyBizCallbackContext context) {
        ApproveOrderPO order = context.getOrder();
        JoySkyBizApprovalResultMqDTO input = context.getInput();
        // 更新流程信息
        String reason = input.getRejectReason();

        // 计算审批单状态
        int auditStatus = AuditStatusEnum.STATUS_6.getCode();
        int currentNodeAuditStatus = AuditStatusEnum.STATUS_6.getCode();
        int auditAction = AuditActionEnum.PASS.getCode();

        this.buildMessageApprovePassParam(context);

        productTransactionExecutor.execute(() -> {
            // 构建需要更新的节点信息
            ApproveNodeVO updateInfo = this.buildApproveNode(auditStatus, auditAction, null, currentNodeAuditStatus, reason);

            approveOrderAtomicService.updateAuditNodeInfo(order, updateInfo);
            this.executeApprovePassBiz(context);
        });

        // 审核通过业务扩展
        this.approvePassBizExtend(context);

    }

    protected void approvePassBizExtend(JoySkyBizCallbackContext context) {

    }

    /**
     * Check data.
     *
     * @param context the context
     */
    protected void checkData(JoySkyBizCallbackContext context) {
        // 查询进行中的审批单
        ApproveOrderPO approveOrder = approveOrderAtomicService.getByProcessTypeAndProcessInstanceId(context.getInput().getProcessType(), context.getInput().getProcessInstanceId());

        // 如果审批单状态是已完成或者已关闭则不再进行消费
        if (ApproveOrderStatusEnum.codeOf(approveOrder.getStatus()) == ApproveOrderStatusEnum.COMPLETED || ApproveOrderStatusEnum.codeOf(approveOrder.getStatus()) == ApproveOrderStatusEnum.CLOSED) {
            log.info("如果审批单状态是已完成或者已关闭则不再进行消费, applyNo={}", approveOrder.getApplyCode());
            context.setIdempotent(true);
            return;
        }

        context.setOrder(approveOrder);
    }

    /**
     * Try lock pair.
     *
     * @param context the context
     * @return the pair
     */
    protected Pair<Boolean, String> tryLock(JoySkyBizCallbackContext context) {
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.PRODUCT_APPROVE_AUDIT_KEY, String.valueOf(context.getOrder().getId()));

        Boolean lock = jimUtils.simpleLock(lockKey, String.valueOf(Thread.currentThread().getId()), 60);

        if (!lock) {
            log.warn("当前记录正在操作，请勿重复操作！lockKey={}, threadId={}", lockKey, Thread.currentThread().getId());
            return Pair.of(false, "当前记录正在操作，请勿重复操作！");
        }

        return Pair.of(true, "");
    }


    /**
     * Release lock.
     *
     * @param context the context
     */
    protected void releaseLock(JoySkyBizCallbackContext context) {
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.PRODUCT_APPROVE_AUDIT_KEY, String.valueOf(context.getOrder().getId()));

        try {
            jimUtils.simpleLockRelease(lockKey, String.valueOf(Thread.currentThread().getId()));
        } catch (Exception e) {
            log.error("释放所失败. lockKey={}, requestId={}", lockKey, Thread.currentThread().getId(), e);
        }
    }

    private Pair<Boolean, String> checkParam(JoySkyBizApprovalResultMqDTO input) {

        String message = ValidationUtil.validateFindFirstError(input);

        if (StringUtils.isNotEmpty(message)) {
            return Pair.of(false, message);
        }

        com.jdi.isc.product.soa.domain.enums.AuditStatusEnum statusEnum = getEnumByCode(input.getProcessStatus());

        if (statusEnum == null) {
            log.error("AuditStatusEnum is not match, processStatus:{}", input.getProcessStatus());
            return  Pair.of(false, String.format("无法匹配流程状态 %s", input.getProcessStatus()));
        }

        return Pair.of(true, "");
    }

    private ApproveNodeVO buildApproveNode(Integer auditStatus, Integer auditAction, String currentNodeErp, Integer currentNodeAuditStatus, String approveComment) {
        ApproveNodeVO node = new ApproveNodeVO();

        node.setCurrentNodeErp(currentNodeErp);
        node.setCurrentNodeAuditStatus(currentNodeAuditStatus);

        node.setAuditStatus(auditStatus);
        node.setAuditAction(auditAction);
        node.setApproveComment(approveComment);

        return node;
    }
}
