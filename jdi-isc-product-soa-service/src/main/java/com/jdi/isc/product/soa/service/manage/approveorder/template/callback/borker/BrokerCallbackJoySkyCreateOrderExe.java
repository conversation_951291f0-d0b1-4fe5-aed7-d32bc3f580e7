package com.jdi.isc.product.soa.service.manage.approveorder.template.callback.borker;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyCreateReqApiDTO;
import com.jdi.isc.biz.component.api.joysky.req.JoySkyQueryApiDTO;
import com.jdi.isc.biz.component.api.joysky.res.JoySkyCreateRspApiDTO;
import com.jdi.isc.biz.component.api.joysky.res.JoySkyDetailInfoDTO;
import com.jdi.isc.product.soa.api.approveorder.common.ApproveOrderStatusEnum;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderCreateMqDTO;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderMqDTO;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.common.util.StringUtils;
import com.jdi.isc.product.soa.domain.approveorder.biz.ApproveCallbackVO;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveDataPO;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveOrderPO;
import com.jdi.isc.product.soa.rpc.joySky.JoySkyApproveRpcService;
import com.jdi.isc.product.soa.rpc.utils.ExceptionUtil;
import com.jdi.isc.product.soa.service.atomic.approveorder.ApproveDataAtomicService;
import com.jdi.isc.product.soa.service.atomic.approveorder.ApproveOrderAtomicService;
import com.jdi.isc.product.soa.service.manage.approveorder.context.JoySkyOrderCreateCallbackContext;
import com.jdi.isc.product.soa.service.manage.approveorder.manager.ApproveOrderManager;
import com.jdi.isc.product.soa.service.support.transactional.ProductTransactionExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;

import static com.jdi.isc.product.soa.service.manage.approveorder.manager.ApproveOrderManager.MAX_FAIL_COUNT;

@Slf4j
@Component
public class BrokerCallbackJoySkyCreateOrderExe {

    @Resource
    private JimUtils jimUtils;

    @Resource
    private JoySkyApproveRpcService joySkyApproveRpcService;

    @Resource
    private ApproveOrderAtomicService approveOrderAtomicService;

    @Resource
    private ApproveDataAtomicService approveDataAtomicService;

    @Resource
    private ProductTransactionExecutor productTransactionExecutor;

    @Resource
    private ApproveOrderManager approveOrderManager;

    public DataResponse<String> execute(ApproveOrderMqDTO<ApproveOrderCreateMqDTO> input) {
        Instant start = Instant.now();
        // 校验数据
        Pair<Boolean, String> checkParam = this.checkParam(input);

        if (!checkParam.getKey()) {
            log.info("BrokerCallbackJoySkyCreateOrderExe, 校验参数未通过. param={}, checkParam={}", JSONObject.toJSONString(input), checkParam.getValue());
            return DataResponse.error(checkParam.getValue());
        }

        Pair<Boolean, String> lockPair = this.tryLock(input);

        if (!lockPair.getKey()) {
            return DataResponse.error(lockPair.getValue());
        }

        JoySkyOrderCreateCallbackContext context = new JoySkyOrderCreateCallbackContext(input);

        try {
            // 校验数据
            this.checkData(context);

            if (context.isIdempotent()) {
                log.info("BrokerCallbackJoySkyCreateOrderExe, 幂等操作. ");
                return DataResponse.success();
            }

            // 构建
            this.buildJoySkyCreateReqApiDTO(context);

            // 执行事务，创建审批单
            this.createSkyApprovalFlow(context);

        } catch (Exception e) {
            log.error("发起审核失败, param={}", JSONObject.toJSONString(input), e);
            return DataResponse.error(ExceptionUtil.getMessage(e, "发起审核失败，请稍后重试！"));
        } finally {
            log.error("broker审批回调成功, param={}, cost={}", JSONObject.toJSONString(input), Duration.between(start, Instant.now()).toMillis());
            // 释放锁
            this.releaseLock(input);
        }

        // 返回结果
        return DataResponse.success();
    }

    private void buildJoySkyCreateReqApiDTO(JoySkyOrderCreateCallbackContext context) {
        // 审批单id
        Long approveId = context.getInput().getData().getApproveId();

        ApproveDataPO dataPO = approveDataAtomicService.getOneByApproveId(approveId);

        if (dataPO == null) {
            throw new ProductBizException("找不到审批单数据 %s", approveId);
        }

        String approveData = dataPO.getApproveData();

        JoySkyCreateReqApiDTO joySkyData = JSONObject.parseObject(approveData, JoySkyCreateReqApiDTO.class);

        context.setJoySkyCreateReq(joySkyData);
    }

    private void checkData(JoySkyOrderCreateCallbackContext context) {
        ApproveOrderMqDTO<ApproveOrderCreateMqDTO> input = context.getInput();

        // 查询审批单是否存在
        ApproveOrderPO approveOrder = approveOrderAtomicService.getValidById(input.getData().getApproveId());

        if (approveOrder != null && StringUtils.isNotEmpty(approveOrder.getApplyCode())) {
            log.info("已成功创建审批单，请勿重复提交！approveOrder={}", JSONObject.toJSONString(approveOrder));
            context.setIdempotent(true);
            return;
        }

        context.setApproveOrder(approveOrder);
    }

    private void createSkyApprovalFlow(JoySkyOrderCreateCallbackContext context) {
        ApproveOrderPO approveOrder = context.getApproveOrder();
        JoySkyDetailInfoDTO joySkyDetailInfo;
        try {
            JoySkyCreateRspApiDTO joySkyCreateRspApiDTO = joySkyApproveRpcService.createSkyApprovalFlow(context.getJoySkyCreateReq());
            if (joySkyCreateRspApiDTO == null || joySkyCreateRspApiDTO.getProcessInstanceId() == null) {
                log.warn("创建审批流失败. data={}", JSONObject.toJSONString(joySkyCreateRspApiDTO));
                throw new ProductBizException("创建审批流失败");
            }
            joySkyDetailInfo = this.getJoySkyDetailInfo(joySkyCreateRspApiDTO.getProcessInstanceId(),context.getApproveOrder().getFlowType());

            if (joySkyDetailInfo == null) {
                throw new ProductBizException("找不到流程信息. %s", joySkyCreateRspApiDTO.getProcessInstanceId());
            }
        } catch (Exception e) {
            log.error("createSkyApprovalFlow, 调用joySky审批流失败！applyNo={}, param={}, message={}", approveOrder.getApplyCode(), JSONObject.toJSONString(context.getInput()), ExceptionUtil.getMessage(e, "调用joySky审批流失败"), e);
            // 更新审批单状态
            ApproveCallbackVO approveCallbackVO = this.buildApproveCallbackVO(context, e);
            // 是否超过最大重试次数
            approveOrderManager.updateCallbackFailInfo(approveOrder, approveCallbackVO);

            // 查询最新审批单信息
            ApproveOrderPO order = approveOrderAtomicService.getValidById(approveOrder.getId());

            if (ApproveOrderManager.isClosed(order)) {
                log.error("找不到审批单信息！approveId={}", approveOrder.getId());
                return;
            }
            context.setApproveOrder(order);
            throw e;
        }

        productTransactionExecutor.execute(() -> approveOrderAtomicService.updateApplyCode(approveOrder, joySkyDetailInfo));
    }

    private ApproveCallbackVO buildApproveCallbackVO(JoySkyOrderCreateCallbackContext context, Exception e) {
        String callbackFailMessage = ExceptionUtil.getMessage(e, "提交审核失败，请稍后重试！", false);

        Integer status = null;

        if (context.getApproveOrder().getCallbackFailCount() >= MAX_FAIL_COUNT) {
            status = ApproveOrderStatusEnum.CLOSED.getCode();
        }

        return new ApproveCallbackVO(status, callbackFailMessage, context.getApproveOrder().getCallbackFailCount() + 1, context.getInput().getAuditAction(), JSONObject.toJSONString(context.getInput()));
    }

    private Pair<Boolean, String> checkParam(ApproveOrderMqDTO<ApproveOrderCreateMqDTO> input) {
        ApproveOrderCreateMqDTO data = input.getData();

        if (data == null || input.getData().getApproveId() == null) {
            return Pair.of(false, "参数错误 id is null");
        }

//        if (data.getBizId() == null || data.getBizType() == null) {
//            return Pair.of(false, "参数错误 bizId or bizType is null");
//        }

        return Pair.of(true, "");
    }

    protected Pair<Boolean, String> tryLock(ApproveOrderMqDTO<ApproveOrderCreateMqDTO> input) {
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.PRODUCT_APPROVE_AUDIT_KEY, String.valueOf(input.getData().getApproveId()));

        Boolean lock = jimUtils.simpleLock(lockKey, String.valueOf(Thread.currentThread().getId()), 60);

        if (!lock) {
            log.warn("当前记录正在操作，请勿重复操作！lockKey={}, threadId={}", lockKey, Thread.currentThread().getId());
            return Pair.of(false, "当前记录正在操作，请勿重复操作！");
        }

        return Pair.of(true, "");
    }


    protected void releaseLock(ApproveOrderMqDTO<ApproveOrderCreateMqDTO> input) {
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.PRODUCT_APPROVE_AUDIT_KEY, String.valueOf(input.getData().getApproveId()));

        try {
            jimUtils.simpleLockRelease(lockKey, String.valueOf(Thread.currentThread().getId()));
        } catch (Exception e) {
            log.error("释放所失败. lockKey={}, requestId={}", lockKey, Thread.currentThread().getId(), e);
        }
    }

    /**
     * 获取审批流程详细信息。
     *
     * @param processInstanceId 流程实例ID。
     * @return 审批流程详细信息DTO。
     */
    protected JoySkyDetailInfoDTO getJoySkyDetailInfo(String processInstanceId, Integer flowTypeCode){
        // 审批通过获取回填数据
        JoySkyQueryApiDTO joySkyQueryApiDTO = new JoySkyQueryApiDTO();
        joySkyQueryApiDTO.setJoySkyFlowType(flowTypeCode);
        joySkyQueryApiDTO.setProcessInstanceId(processInstanceId);
        joySkyQueryApiDTO.setNeedSubTableData(false);
        joySkyQueryApiDTO.setNeedTodoList(true);
        return joySkyApproveRpcService.queryFlowModel(joySkyQueryApiDTO);
    }
}
