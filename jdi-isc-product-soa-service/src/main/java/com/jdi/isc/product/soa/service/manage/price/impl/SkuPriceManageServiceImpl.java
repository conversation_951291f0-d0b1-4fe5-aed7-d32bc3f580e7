package com.jdi.isc.product.soa.service.manage.price.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.TradeTypeConstant;
import com.jdi.isc.product.soa.api.price.req.PriceUnsellableThresholdImportReqDTO;
import com.jdi.isc.product.soa.api.price.res.PriceUnsellableThresholdImportResDTO;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.exception.SkuPriceException;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.common.util.DateUtil;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryMkuPageVO;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.SkuScopeEnum;
import com.jdi.isc.product.soa.domain.enums.TaskBizTypeEnum;
import com.jdi.isc.product.soa.domain.enums.TradeDirectionEnum;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.price.agreementPrice.po.CountryAgreementPricePO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceReqVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceResVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceVO;
import com.jdi.isc.product.soa.domain.price.po.PriceLogPO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.price.supplierPrice.biz.*;
import com.jdi.isc.product.soa.domain.price.supplierPrice.draft.SkuPriceDraftVO;
import com.jdi.isc.product.soa.domain.sku.biz.ExternalVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuExternalReqVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangVO;
import com.jdi.isc.product.soa.domain.spu.po.ProductGlobalAttributePO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.taxRate.req.PurchaseTaxQueryReqVO;
import com.jdi.isc.product.soa.domain.taxRate.res.PurchaseSkuTaxResVO;
import com.jdi.isc.product.soa.price.api.price.req.FulfillmentRateChangeReqVO;
import com.jdi.isc.product.soa.rpc.mq.ProductCenterMqService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.PriceLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.price.agreementPrice.CountryAgreementPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.ProductGlobalAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.SupplierAllInfoAtomicService;
import com.jdi.isc.product.soa.service.manage.brand.BrandOutService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceDraftManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.price.agreementPrice.CountryAgreementPriceManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuExternalManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.PurchaseTaxManageService;
import com.jdi.isc.product.soa.service.manage.vendor.VendorManageService;
import com.jdi.isc.product.soa.service.mapstruct.ProductAttributeConvertService;
import com.jdi.isc.product.soa.service.mapstruct.price.SkuPriceConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * sku价格业务服务
 * <AUTHOR>
 * @date 20231130
 */
@Slf4j
@Service
public class SkuPriceManageServiceImpl implements SkuPriceManageService {

    @LafValue("jdi.isc.product.maxNegativeProfit")
    private String productMaxNegativeProfit;
    @Resource
    private SkuPriceAtomicService skuPriceAtomicService;
    @Resource
    private ProductCenterMqService productCenterMqService;
    @Value("${topic.jmq4.product.isc.costPriceRefresh}")
    private String costPriceRefreshTopic;
    @Resource
    private PriceLogAtomicService priceLogAtomicService;
    @Resource
    @Lazy
    private SkuPriceDraftManageService skuPriceDraftManageService;
    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;
    @Resource
    private CountryManageService countryManageService;
    @Resource
    private CategoryOutService categoryOutService;
    @Resource
    private SupplierAllInfoAtomicService supplierAllInfoAtomicService;
    @Resource
    private VendorManageService vendorManageService;
    @Resource
    private BrandOutService brandOutService;
    @Resource
    private SkuExternalManageService skuExternalManageService;
    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private PurchaseTaxManageService purchaseTaxManageService;
    @Resource
    private ProductAttributeConvertService productAttributeConvertService;
    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;

    @Resource
    private CountryAgreementPriceAtomicService countryAgreementPriceAtomicService;


    // 巴西税率跨境属性ID集合
    public static final Set<Long> taxBRSet = Sets.newHashSet(73L, 76L, 77L, 79L,74L, 78L, 80L, 106L);

    @Override
    @ToolKit(umpFlag=false)
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class,timeout = 60)
    public Boolean saveOrUpdate(SkuPriceReqVO input) {
        input.getSkuPriceVO().forEach(vo->{
            SkuPricePO skuPrice = skuPriceAtomicService.getOne(vo);
            if(skuPrice!=null){
                if(!vo.getPrice().equals(skuPrice.getPrice())
                        || (vo.getTaxPrice()!= null && !Objects.equals(vo.getTaxPrice(),skuPrice.getTaxPrice()))){
                    skuPriceAtomicService.update(vo);
                    PriceLogPO priceLogPO = this.getPriceLogPO(skuPrice);
                    priceLogAtomicService.save(priceLogPO);
                }
            }else {
                SkuPricePO target = SkuPriceConvert.INSTANCE.vo2po(vo);
                if(CountryConstant.COUNTRY_ZH.equals(vo.getSourceCountryCode())){
                    target.setTradeType(TradeTypeConstant.EXW);
                }else{
                    target.setTradeType(TradeTypeConstant.BD);
                }
                skuPriceAtomicService.save(target);
                PriceLogPO priceLogPO = this.getPriceLogPO(target);
                priceLogAtomicService.save(priceLogPO);
            }
        });
        return true;
    }

    //负毛利检查,负毛利时返回为false
    @Override
    public void negativeProfit(SkuPriceReqVO input) {
        boolean res = false;
        Map<Long, List<SkuPriceVO>> skuSet = input.getSkuPriceVO().stream().collect(Collectors.groupingBy(SkuPriceVO::getSkuId));
        for (Map.Entry<Long, List<SkuPriceVO>> entry : skuSet.entrySet()) {
            if(entry.getValue().size()!=2){
                throw new SkuPriceException("sku价格更新入参缺失");
            }
            SkuPriceVO price1 = entry.getValue().get(0);
            SkuPriceVO price2 = entry.getValue().get(1);
            if(TradeDirectionEnum.CUSTOMER.equals(price1.getTradeDirection())){
                res =  price1.getPrice().subtract(price2.getPrice()).divide(price1.getPrice(),4, RoundingMode.HALF_UP).compareTo(this.getCountryMaxNegativeProfit(price1.getSourceCountryCode()))>=0;
            }else {
                res = price2.getPrice().subtract(price1.getPrice()).divide(price2.getPrice(),4, RoundingMode.HALF_UP).compareTo(this.getCountryMaxNegativeProfit(price2.getSourceCountryCode()))>=0;
            }
        }
       if(!res){
           throw new SkuPriceException("超过阈值的负毛利价格,请检查.");
       }
    }

    /**
     * 查询sku价格列表，对商对客维度的价格会拍平到一条记录,性能待优化
     */
    @Override
    @ToolKit(umpFlag=false)
    public Map<Long, SkuPriceResVO> list(SkuPriceReqVO skuPriceVO) {
        Map<Long,SkuPriceResVO> result = new HashMap<>();
        for(SkuPriceVO priceVO : skuPriceVO.getSkuPriceVO()){
            LambdaQueryWrapper<SkuPricePO> skuPriceQ = Wrappers.<SkuPricePO>lambdaQuery()
                    .eq(SkuPricePO::getSourceCountryCode,priceVO.getSourceCountryCode())
                    .eq(SkuPricePO::getTradeType, CountryConstant.COUNTRY_ZH.equals(priceVO.getSourceCountryCode())? TradeTypeConstant.EXW:TradeTypeConstant.BD)
                    .in(SkuPricePO::getSkuId,priceVO.getSkuId())
                    .eq(SkuPricePO::getCurrency,priceVO.getCurrency());
            List<SkuPricePO> skuPrice = skuPriceAtomicService.list(skuPriceQ);
            if(CollectionUtils.isNotEmpty(skuPrice)){
                SkuPricePO supplyPrice = skuPrice.stream().filter(line -> TradeDirectionEnum.SUPPLIER.equals(line.getTradeDirection())).findFirst().orElse(null);
                SkuPricePO salePrice = skuPrice.stream().filter(line -> TradeDirectionEnum.CUSTOMER.equals(line.getTradeDirection())).findFirst().orElse(null);
                SkuPriceResVO assemblySkuPrice = new SkuPriceResVO();
                assemblySkuPrice.setSkuId(priceVO.getSkuId());
                assemblySkuPrice.setCurrency(priceVO.getCurrency());
                assemblySkuPrice.setSupplierTradeType(supplyPrice!=null?supplyPrice.getTradeType():null);
                assemblySkuPrice.setPurchasePrice(supplyPrice!=null?supplyPrice.getPrice():null);
                assemblySkuPrice.setTaxPurchasePrice(supplyPrice!=null?supplyPrice.getTaxPrice():null);
                assemblySkuPrice.setCustomerTradeType(salePrice!=null?salePrice.getTradeType():null);
                assemblySkuPrice.setSalePrice(salePrice!=null?salePrice.getPrice():null);
                assemblySkuPrice.setTaxSalePrice(salePrice!=null?salePrice.getTaxPrice():null);
                result.put(priceVO.getSkuId(),assemblySkuPrice);
            }
        }
        return result;
    }

    /**
     * 查询sku价格
     */
    @Override
    @PFTracing
    public SkuPricePO getOne(SkuPriceVO skuPriceVO) {
        LambdaQueryWrapper<SkuPricePO> skuPriceQ = Wrappers.<SkuPricePO>lambdaQuery()
                .eq(SkuPricePO::getSkuId,skuPriceVO.getSkuId())
                .eq(SkuPricePO::getTradeDirection,skuPriceVO.getTradeDirection())
                .eq(SkuPricePO::getTradeType,CountryConstant.COUNTRY_ZH.equals(skuPriceVO.getSourceCountryCode())?TradeTypeConstant.EXW:TradeTypeConstant.BD)
                .eq(SkuPricePO::getCurrency,skuPriceVO.getCurrency());
        return skuPriceAtomicService.getOne(skuPriceQ);
    }

    /**
     * 获取指定国家的最大负利润值。
     * @param sourceCountryCode 国家代码。
     * @return 最大负利润值。
     */
    private BigDecimal getCountryMaxNegativeProfit(String sourceCountryCode){
        log.info("SkuPriceManageServiceImpl.getCountryMaxNegativeProfit sourceCountryCode:{}",sourceCountryCode);
        String countryMaxNegativeProfit = ConfigUtils.getStringByKey(productMaxNegativeProfit,sourceCountryCode);
        if(StringUtils.isBlank(countryMaxNegativeProfit)){
            return BigDecimal.ZERO;
        }
        return new BigDecimal(countryMaxNegativeProfit);
    }

    /** 触发对应SKU的国家成本价、入仓成本价重算*/
    @Override
    public Boolean triggerCostPriceRefresh(List<FulfillmentRateChangeReqVO> input) {
        log.info("FulfillmentRateManageServiceImpl.triggerCostPriceRefresh input size:{}" , input.size());
        if(CollectionUtils.isNotEmpty(input)){
            for(FulfillmentRateChangeReqVO vo : input){
                if(vo.getSkuId()!=null && StringUtils.isNotBlank(vo.getSourceCountryCode()) && StringUtils.isNotBlank(vo.getTargetCountryCode())){
                    productCenterMqService.sendProductSoaMessageRetry(String.valueOf(vo.getSkuId()),vo,costPriceRefreshTopic);
                }
            }
        }
        return true;
    }

    @Override
    public PageInfo<SkuPricePageVO> pageSearch(SkuPricePageReqVO vo) {
        PageInfo<SkuPricePageVO> pageInfo = new PageInfo<>();
        pageInfo.setSize(vo.getSize());
        pageInfo.setIndex(vo.getIndex());
        this.buildQuery(vo);
        long total = skuPriceAtomicService.pageSearchTotal(vo);
        if (total == 0) {
            pageInfo.setTotal(0);
            return pageInfo;
        }
        List<SkuPricePageVO> priceList = skuPriceAtomicService.pageSearch(vo);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(priceList)){
            return pageInfo;
        }
        this.setPageSkuName(priceList);
        this.setPageMkuId(priceList);
        this.setPageCountryName(priceList);
        this.setPageSpuInfo(priceList);
        this.setAuditStatus(priceList);
        this.setCostPrice(priceList);
        this.setSkuVatRate(priceList);
        this.setValue1(vo,priceList);
        pageInfo.setTotal(total);
        pageInfo.setRecords(priceList);
        return pageInfo;
    }


    private void setCostPrice(List<SkuPricePageVO> priceList){
        if(CollectionUtils.isEmpty(priceList)){
            return;
        }
        String sourceCountryCode = priceList.get(0).getSourceCountryCode();
        List<Long> skuIdList = priceList.stream().map(SkuPricePageVO::getSkuId).collect(Collectors.toList());
        List<CountryAgreementPricePO> countryAgreementPriceList = countryAgreementPriceAtomicService.getCountryAgreementPriceList(skuIdList, sourceCountryCode);
        Map<Long,CountryAgreementPricePO> countryAgreementPriceMap = countryAgreementPriceList.stream().collect(Collectors.toMap(CountryAgreementPricePO::getSkuId, Function.identity(), (existing, replacement) -> existing));
        for(SkuPricePageVO input : priceList) {
            if(countryAgreementPriceMap.containsKey(input.getSkuId())) {
                CountryAgreementPricePO countryAgreementPricePO = countryAgreementPriceMap.get(input.getSkuId());
                input.setCountryCostPrice(countryAgreementPricePO.getCountryCostPrice());
                input.setAgreementPrice(countryAgreementPricePO.getAgreementPrice());
                input.setCostCurrency(countryAgreementPricePO.getCurrency());
                input.setAgreementMark(countryAgreementPricePO.getAgreementMark());
                input.setCostMark(countryAgreementPricePO.getCostMark());
                if(Objects.nonNull(countryAgreementPricePO.getAgreementPrice()) && Objects.nonNull(countryAgreementPricePO.getCountryCostPrice())) {
                    BigDecimal ratio = countryAgreementPricePO.getCountryCostPrice()
                        .divide(countryAgreementPricePO.getAgreementPrice(), 4, RoundingMode.HALF_UP);
                    BigDecimal profitRate = BigDecimal.ONE.subtract(ratio);
                    input.setProfitRate(profitRate);
                }
            }
        }
    }

    private void buildQuery(SkuPricePageReqVO vo){

    }

    /**
     * 设置 SKU 名称。
     * @param targets 目标列表，包含 CustomerSkuPriceWarningVO 对象。
     */
    private void setPageSkuName(List<SkuPricePageVO> targets) {
        if(CollectionUtils.isEmpty(targets)){
            return;
        }
        List<Long> skuIds = targets.stream().map(SkuPricePageVO::getSkuId).collect(Collectors.toList());
        Map<Long, ExternalVO> externalVOMap = querySkuInfo(skuIds);
        for(SkuPricePageVO target :targets){
            ExternalVO skuLang = externalVOMap.get(target.getSkuId());
            if (skuLang != null) {
                SpuLangVO zhName = skuLang.getLangVOList().stream()
                        .filter(line -> LangConstant.LANG_ZH.equals(line.getLang()))
                        .findFirst().orElse(null);
                target.setSkuName(zhName != null ? zhName.getSpuTitle() : null);
            }
        }
    }

    /**
     * 根据给定的 SKU IDs 查询对应的 ExternalVO 信息。
     * @param skuIds 需要查询的 SKU IDs 列表。
     * @return 包含查询结果的 Map 对象，键为 SKU ID，值为对应的 ExternalVO 对象。
     */
    private Map<Long, ExternalVO> querySkuInfo(List<Long> skuIds) {
        SkuExternalReqVO skuQuery = new SkuExternalReqVO();
        skuQuery.setSkuIds(skuIds);
        skuQuery.setScopeEnums(Sets.newHashSet(SkuScopeEnum.BASE, SkuScopeEnum.LANG));
        return skuExternalManageService.querySkuInfo(skuQuery);
    }

    /**
     * 设置审计状态
     * @param targets 目标列表
     */
    private void setAuditStatus(List<SkuPricePageVO> targets) {
        if(CollectionUtils.isEmpty(targets)){
            return;
        }
        for(SkuPricePageVO target :targets){
            target.setAuditStatus(AuditStatusEnum.APPROVED.getCode());
        }
    }

    /**
     * 设置商品页面的 MKU ID。
     * @param targets 包含 SkuPricePageVO 对象的列表，用于设置 MKU ID。
     */
    private void setPageMkuId(List<SkuPricePageVO> targets) {
        if(CollectionUtils.isEmpty(targets)){
            return;
        }
        Set<Long> skuIds = targets.stream().map(SkuPricePageVO::getSkuId).collect(Collectors.toSet());
        Map<Long, Long> skuMkuMap = mkuRelationAtomicService.queryMkuIdBySkuIds(skuIds);
        for(SkuPricePageVO target :targets){
            target.setMkuId(skuMkuMap.get(target.getSkuId()));
        }
    }

    /**
     * 设置商品价格页面的国家名称。
     * @param targets SkuPricePageVO 列表，包含需要设置国家名称的商品价格页面信息。
     */
    private void setPageCountryName(List<SkuPricePageVO> targets) {
        if(CollectionUtils.isEmpty(targets)){
            return;
        }
        Map<String, String> countryMap = countryManageService.getCountryMap(LangContextHolder.get());
        for(SkuPricePageVO target :targets){
            target.setSourceCountryName(countryMap.get(target.getSourceCountryCode()));
        }
    }

    /**
     * 设置商品价格页面的国家名称。
     * @param inputs SkuPricePageVO 列表，包含需要设置国家名称的商品价格页面信息。
     */
    private void setPageSpuInfo(List<SkuPricePageVO> inputs) {
        if(CollectionUtils.isEmpty(inputs)){
            return;
        }
        Set<Long> skuIds = inputs.stream().map(SkuPricePageVO::getSkuId).collect(Collectors.toSet());
        Map<Long, SkuPO> skuPOMap = skuAtomicService.getSkuMap(skuIds);
        Set<Long> spuIds = inputs.stream().map(SkuPricePageVO::getSpuId).collect(Collectors.toSet());
        Map<Long, SpuPO> spuPOMap = spuAtomicService.getSpuMap(spuIds);
        Set<Long> brandIds = skuPOMap.values().stream().map(SkuPO::getBrandId).collect(Collectors.toSet());
        Map<Long, String> brandNameMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(brandIds)){
            brandNameMap.putAll(brandOutService.queryNameByIds(brandIds,LangContextHolder.get()));
        }

        Set<Long> categoryIds = skuPOMap.values().stream().map(SkuPO::getJdCatId).collect(Collectors.toSet());
        Map<Long, String> catMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(categoryIds)){
            catMap.putAll(categoryOutService.queryPathStr(categoryIds,LangContextHolder.get()));
        }

        Map<String,Set<String>> countrySupplierCodeMap = spuPOMap.values().stream()
                .filter(item -> StringUtils.isNotBlank(item.getVendorCode())
                        && StringUtils.isNotBlank(item.getSourceCountryCode()))
                .collect(Collectors.groupingBy(
                        SpuPO::getSourceCountryCode,
                        Collectors.mapping(SpuPO::getVendorCode, Collectors.toSet())
                ));
        if(MapUtils.isEmpty(countrySupplierCodeMap)){
            return;
        }
        Map<String, String> supplierMap = new HashMap<>();
        countrySupplierCodeMap.forEach((k,v)->{
            if(CountryConstant.COUNTRY_ZH.equals(k)){
                return;
            }
            Map<String, String> resultPart = supplierAllInfoAtomicService.querySupplierNameByCodes(v, k);
            supplierMap.putAll(resultPart);
        });
        countrySupplierCodeMap.forEach((k,v)->{
            if(!CountryConstant.COUNTRY_ZH.equals(k)){
                return;
            }
            Map<String, String> resultPart = vendorManageService.getVendorMapByCodes(v);
            supplierMap.putAll(resultPart);
        });

        inputs.forEach(item->{
            SkuPO skuPO = skuPOMap.get(item.getSkuId());
            if(skuPO != null){
                item.setCatId(skuPO.getJdCatId());
                item.setCatName(catMap.get(skuPO.getJdCatId()));

                item.setBrandId(skuPO.getBrandId());
                item.setBrandName(brandNameMap.get(skuPO.getBrandId()));
            }

            SpuPO spuPO = spuPOMap.get(item.getSpuId());
            if(spuPO != null){
                item.setBuyer(spuPO.getBuyer());
                item.setSpuStatus(spuPO.getSpuStatus());
                item.setVendorName(supplierMap.get(spuPO.getVendorCode()));
            }
        });
    }

    /**
     * 根据CustomerSkuPriceDetailPO对象创建并返回PriceLogPO对象。
     * @param param CustomerSkuPriceDetailPO对象，包含客户SKU价格详细信息。
     */
    private PriceLogPO getPriceLogPO(SkuPricePO param){
        PriceLogPO priceLogPO = new PriceLogPO();
        priceLogPO.setBizId(param.getId().toString());
        priceLogPO.setBizType(com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum.SKU_SUPPLIER_PRICE.getCode());
        priceLogPO.setBizValue(param.getPrice());
        priceLogPO.setValue1(param.getCurrency());
        priceLogPO.setValue2(param.getTaxPrice() == null?"":param.getTaxPrice().toString());
        priceLogPO.setCreateTime(DateUtil.getCurrentTime());
        priceLogPO.setCreator(param.getCreator());
        priceLogPO.setUpdater(param.getUpdater());
        priceLogPO.setUpdateTime(DateUtil.getCurrentTime());
        return priceLogPO;
    }


    @Override
    public SkuPriceVO detailById(SkuPriceQueryReqVO reqVO) {
        reqVO.setTradeDirection(TradeDirectionEnum.SUPPLIER);
        SkuPricePO po = skuPriceAtomicService.getSkuPricePO(reqVO);
        SkuPriceVO vo = SkuPriceConvert.INSTANCE.po2Vo(po);
        return vo;
    }

    @Override
    public SkuPriceVO detailBySkuId(SkuPriceQueryReqVO reqVO) {
        reqVO.setTradeDirection(TradeDirectionEnum.SUPPLIER);
        SkuPriceDraftVO skuPriceDraftVO = skuPriceDraftManageService.detail(reqVO);
        if (Objects.nonNull(skuPriceDraftVO) && skuPriceDraftVO.getAuditStatus().equals(AuditStatusEnum.WAITING_APPROVED.getCode())) {
            log.info("数据在审批中，无法再次申请");
            throw new BizException("数据在审批中，无法再次申请");
        }
        SkuPricePO po = skuPriceAtomicService.getSkuPricePO(reqVO);
        SkuPriceVO vo = SkuPriceConvert.INSTANCE.po2Vo(po);
        return vo;
    }

    /**
     * 根据商品价格列表设置每个商品的增值税率。
     * @param priceList 商品价格列表，包含商品ID和其他相关信息。
     */
    private void setSkuVatRate(List<SkuPricePageVO> priceList) {
        if (CollectionUtils.isEmpty(priceList)) {
            return;
        }
        Set<Long> skuIds = priceList.stream().filter(Objects::nonNull).map(SkuPricePageVO::getSkuId).collect(Collectors.toSet());
        Map<Long, PurchaseSkuTaxResVO> skuTaxResVOMap = purchaseTaxManageService.batchQueryPurchaseTax(new PurchaseTaxQueryReqVO(skuIds));
        if (MapUtils.isNotEmpty(skuTaxResVOMap)){
            priceList.forEach(priceVo -> {
                PurchaseSkuTaxResVO purchaseSkuTaxResVO = skuTaxResVOMap.get(priceVo.getSkuId());
                if (Objects.nonNull(purchaseSkuTaxResVO)) {
                    priceVo.setVatRate(StringUtils.isNotBlank(purchaseSkuTaxResVO.getComplexTaxRate()) ?
                        new BigDecimal(purchaseSkuTaxResVO.getComplexTaxRate()).multiply(new BigDecimal(100)) : null);
                }
            });
        }
    }

    private void setValue1(SkuPricePageReqVO vo,List<SkuPricePageVO> priceList){
        if (vo == null || StringUtils.isBlank(vo.getSourceCountryCode())
                || CollectionUtils.isEmpty(priceList) || !CountryConstant.COUNTRY_BR.equals(vo.getSourceCountryCode())) {
            return;
        }
        Set<Long> skuIds = priceList.stream().map(SkuPricePageVO::getSkuId).collect(Collectors.toSet());
        Map<Long, SkuPO> skuPOMap = skuAtomicService.getSkuMap(skuIds);
        List<ProductGlobalAttributePO> productGlobalAttributePOS = productGlobalAttributeAtomicService.getBySkuIdsAndAttributeIds(skuIds,taxBRSet);
        Map<Long,List<ProductGlobalAttributePO>> skuInterAttributeMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(productGlobalAttributePOS)){
            skuInterAttributeMap.putAll(productGlobalAttributePOS.stream().collect(Collectors.groupingBy(ProductGlobalAttributePO::getKeyId)));
        }

        for(SkuPricePageVO pricePageVO : priceList){
            SkuPO skuPO = skuPOMap.get(pricePageVO.getSkuId());
            if(skuPO == null){
                continue;
            }
            List<ProductGlobalAttributePO> skuAttributePOS = skuInterAttributeMap.get(pricePageVO.getSkuId());
            List<PropertyVO> propertyVOS = productAttributeConvertService.convertSkuAttributeVOSFromDB(skuPO.getSourceCountryCode()
                    , Lists.newArrayList(skuPO.getSourceCountryCode()), skuAttributePOS);
            if(CollectionUtils.isEmpty(propertyVOS)){
                continue;
            }

            pricePageVO.setValue1(JSONObject.toJSONString(propertyVOS));
        }
    }

    @Override
    public List<SkuPurchasePriceVO> getPurchasePrice(SkuPurchasePriceReqVO reqVO) {
        Set<Long> skuIdSet = new HashSet<>();
        if(Objects.nonNull(reqVO.getMkuId()) && CollectionUtils.isNotEmpty(reqVO.getMkuId())){
            Map<Long, List<MkuRelationPO>> dbMkuSkuMap = mkuRelationAtomicService.queryMkuBindSkuListMap(new ArrayList<>(reqVO.getMkuId()));
            if(MapUtils.isNotEmpty(dbMkuSkuMap)){
                //mku&sku一对多取第一个
                Map<Long, Long> mkuSkuMap = dbMkuSkuMap.entrySet().stream().filter(entry -> entry.getValue() != null && !entry.getValue().isEmpty())
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().get(0).getSkuId()));
                skuIdSet.addAll(mkuSkuMap.values());
            }
        }
        if(Objects.nonNull(reqVO.getSkuId()) && CollectionUtils.isNotEmpty(reqVO.getSkuId())){
            skuIdSet.addAll(reqVO.getSkuId());
        }
        List<SkuPricePO> priceList = skuPriceAtomicService.getPriceList(skuIdSet, TradeDirectionEnum.SUPPLIER);
        List<SkuPurchasePriceVO> skuPurchasePriceVOList = new ArrayList<>();
        for (SkuPricePO skuPricePO : priceList){
            SkuPurchasePriceVO skuPurchasePriceVO = new SkuPurchasePriceVO();
            skuPurchasePriceVO.setPurchasePrice(skuPricePO.getPrice());
            skuPurchasePriceVO.setTaxPurchasePrice(skuPricePO.getTaxPrice());
            skuPurchasePriceVO.setSkuId(skuPricePO.getSkuId());
            skuPurchasePriceVO.setCurrency(skuPricePO.getCurrency());
            skuPurchasePriceVOList.add(skuPurchasePriceVO);
        }
        setMkuId(skuPurchasePriceVOList);
        setVendorCode(skuPurchasePriceVOList);
        return skuPurchasePriceVOList;
    }

    @Override
    public DataResponse<PriceUnsellableThresholdImportResDTO> updateUnsellableThreshold(PriceUnsellableThresholdImportReqDTO input) {

        Integer taskBizType = input.getTaskBizType();

        if (TaskBizTypeEnum.forCode(taskBizType) == TaskBizTypeEnum.VIP_PRICE_LINE_AUDIT_IMPORT) {
            return SpringUtil.getBean(CustomerSkuPriceManageService.class).updateUnsellableThreshold(input);
        } else if (TaskBizTypeEnum.forCode(taskBizType) == TaskBizTypeEnum.AGREEMENT_PRICE_LINE_AUDIT_IMPORT) {
            return SpringUtil.getBean(CountryAgreementPriceManageService.class).updateUnsellableThreshold(input);
        } else {
            return DataResponse.error("不支持的业务类型");
        }
    }

    /**
     * 设置商品页面的 MKU ID。
     * @param targets 包含 SkuPricePageVO 对象的列表，用于设置 MKU ID。
     */
    private void setMkuId(List<SkuPurchasePriceVO> targets) {
        if(CollectionUtils.isEmpty(targets)){
            return;
        }
        Set<Long> skuIds = targets.stream().map(SkuPurchasePriceVO::getSkuId).collect(Collectors.toSet());
        Map<Long, Long> skuMkuMap = mkuRelationAtomicService.queryMkuIdBySkuIds(skuIds);
        for(SkuPurchasePriceVO target :targets){
            target.setMkuId(skuMkuMap.get(target.getSkuId()));
        }
    }

    private void setVendorCode(List<SkuPurchasePriceVO> targets){
        if(CollectionUtils.isEmpty(targets)){
            return;
        }
        Set<Long> skuIds = targets.stream().map(SkuPurchasePriceVO::getSkuId).collect(Collectors.toSet());
        List<SkuPO> skuPOList = skuAtomicService.queryBySkuIds(skuIds);
        Map<Long, String> skuIdVendorMap = skuPOList.stream().collect(Collectors.toMap(SkuPO::getSkuId, SkuPO::getVendorCode));
        for(SkuPurchasePriceVO target :targets){
            target.setVendorCode(skuIdVendorMap.get(target.getSkuId()));
        }
    }


}
