package com.jdi.isc.product.soa.service.manage.spu.validate;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.AtrributeValueInputCheckTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.AttributeCheckTypeEnum;
import com.jdi.isc.product.soa.common.enums.AttributeDimensionEnum;
import com.jdi.isc.product.soa.common.enums.AttributeInputTypeEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.util.NumberUtils;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeFlatVO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryVO;
import com.jdi.isc.product.soa.domain.category.biz.GlobalQualificationVO;
import com.jdi.isc.product.soa.domain.common.biz.BaseLangVO;
import com.jdi.isc.product.soa.domain.enums.CompanyTypeEnum;
import com.jdi.isc.product.soa.domain.enums.StatusEnum;
import com.jdi.isc.product.soa.domain.lang.biz.LangVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuLangPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.supplier.po.BusinessLineRelationPO;
import com.jdi.isc.product.soa.domain.taxRate.po.CategoryTaxPO;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.BusinessLineRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.supplier.SupplierAllInfoAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.CategoryTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.attribute.AttributeOutService;
import com.jdi.isc.product.soa.service.manage.brand.BrandOutService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.manage.category.GlobalAttributeManageService;
import com.jdi.isc.product.soa.service.manage.category.GlobalQualificationManageService;
import com.jdi.isc.product.soa.service.manage.country.CountryLangManageService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.manage.spu.ExtendInfoService;
import com.jdi.isc.product.soa.service.manage.spu.SpuCompareService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import com.jdi.isc.product.soa.service.support.DataResponseMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.Constant.COMMA;
import static com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant.*;


/**
 * 保存商品校验服务
 *
 * <AUTHOR>
 * @date 2023/11/29
 **/
@Slf4j
@Service
public class SpuValidateService {

    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;

    @Resource
    private AttributeOutService attributeOutService;

    @Resource
    private CategoryOutService categoryOutService;

    @Resource
    private BrandOutService brandOutService;

    @Resource
    private SpuCompareService spuCompareService;

    @Resource
    private CategoryTaxAtomicService categoryTaxAtomicService;

    @Resource
    private SupplierAllInfoAtomicService supplierAllInfoAtomicService;

    @Resource
    private DataResponseMessageService dataResponseMessageService;

    @Resource
    private GlobalAttributeManageService globalAttributeManageService;

    @Resource
    private GlobalQualificationManageService globalQualificationManageService;

    @Resource
    private CountryManageService countryManageService;

    @Resource
    private SpuConvertService spuConvertService;

    @Resource
    private LangManageService langManageService;

    @Resource
    private ExtendInfoService extendInfoService;

    @Resource
    private BusinessLineRelationAtomicService businessLineRelationAtomicService;

    @Resource
    private SpuLangAtomicService spuLangAtomicService;

    @Resource
    private CountryLangManageService countryLangManageService;

    @LafValue("jdi.isc.product.limit.spuIds")
    private String limitSpuIds;

    public void validateSaveParams(SaveSpuVO saveSpuVO) {
        Long spuId = saveSpuVO.getSpuVO().getSpuId();
        AssertValidation.isTrue(Objects.nonNull(spuId) && spuId < 20000000000L, SPU_NUMBER_WRONG_ERROR, getMessage(SPU_NUMBER_WRONG_ERROR));
        // 校验spu
        this.validateSpu(saveSpuVO);
        // 校验商品详描,校验敏感词

    }


    public void validateSpu(SaveSpuVO saveSpuVO) {
        SpuVO spuVO = saveSpuVO.getSpuVO();
        // 校验品牌
        if (Objects.nonNull(spuVO.getBrandId())) {
            Map<Long, String> brandMap = brandOutService.queryNameByIds(Sets.newHashSet(spuVO.getBrandId()), LangConstant.LANG_ZH);
            log.info("SpuValidateService.validateSaveParams checkBrand branId={},brandMap={}", spuVO.getBrandId(), JSON.toJSONString(brandMap));
            AssertValidation.isTrue(!brandMap.containsKey(spuVO.getBrandId()), SPU_BRAND_NOT_EXIST_ERROR, getMessage(SPU_BRAND_NOT_EXIST_ERROR));
        }

        // 校验类目
        if (Constant.ONE == saveSpuVO.getApproveFlag()) {
            CategoryVO categoryVo = categoryOutService.queryCategoryById(spuVO.getCatId());
            AssertValidation.isTrue(Objects.isNull(categoryVo), String.format("当前类目ID [%S]不存在", spuVO.getCatId()));
            AssertValidation.isTrue(!categoryVo.getLeafNodeFlag(), String.format("当前类目ID [%s] 不是末级，请检查", spuVO.getCatId()));
        } else {
            CategoryVO categoryVo = categoryOutService.queryCategoryById(spuVO.getCatId());
            AssertValidation.isTrue(Objects.isNull(categoryVo) || StatusEnum.FORBIDDEN.getCode().equals(categoryVo.getStatus()), SPU_CATEGORY_NOT_EXIST_ERROR, getMessage(SPU_CATEGORY_NOT_EXIST_ERROR));
            AssertValidation.isTrue(!categoryVo.getLeafNodeFlag(), String.format("当前类目ID [%s] 不是末级，请检查",spuVO.getCatId()));
        }

//        List<CategoryPathVO> categoryPathVOS = categoryOutService.queryPathByStatus(spuVO.getCatId(), null,YesOrNoEnum.YES.getCode());
//        AssertValidation.isTrue(CollectionUtils.isEmpty(categoryPathVOS), SPU_CATEGORY_NOT_EXIST_ERROR, getMessage(SPU_CATEGORY_NOT_EXIST_ERROR));

        // 校验跨国属性
        this.validateInterProperty(spuVO.getCatId(),spuVO.getSourceCountryCode()
                ,StringUtils.isNotBlank(spuVO.getAttributeScope()) ? Arrays.asList(spuVO.getAttributeScope().split(COMMA)) : Lists.newArrayList()
                , saveSpuVO.getSpuInterPropertyList(),spuVO.getIsExport());

        // 校验跨国资质
        this.validateCertificate(spuVO.getCatId(),spuVO.getSourceCountryCode()
                ,StringUtils.isNotBlank(spuVO.getAttributeScope()) ? Arrays.asList(spuVO.getAttributeScope().split(COMMA)) : Lists.newArrayList()
                , saveSpuVO.getSpuCertificateVOList(),spuVO.getIsExport());

        // 校验多语标题名称
        this.validateSpuTitle(spuVO.getSpuLangExtendVOList(),spuVO.getSpuId());

        // 校验关键字
        this.validateKeyPhrases(spuVO.getSpuLangExtendVOList());

        // 校验供应商SPUID
        this.validateVendorSpuId(spuVO);

        // 校验销售单位
        this.validateSaleUnit(spuVO);

        // 验证型号
        this.validateSpecification(spuVO,saveSpuVO.getApproveFlag());

        // 商品主图
        this.validateSpuMainImage(spuVO);

        // 商品幅图
        this.validateSpuDetailImage(spuVO);

        // 校验商品标题是否修改
        this.validateSpuTitleUpdate(spuVO);

        // 校验商品主图是否修改
        this.validateSpuMainImageUpdate(spuVO);

        // 校验扩展属性
        this.validateExtendProperty(saveSpuVO.getStoreExtendPropertyList());

    }

    /**
     * 验证扩展属性合法性
     * @param propertyVOList 扩展属性列表
     */
    public static void validateExtendProperty(List<PropertyVO> propertyVOList) {
        // 暂不启用，因为系统中有其他业务调用发品接口完成，例如税率审核、库存修改、审批通过等，非核心业务数据校验导致这些业务无法通过，不合适，应该通过修改数据进入部分逻辑完成，而不是在发品校验
        if(CollectionUtils.isNotEmpty(propertyVOList)){
            return;
        }
        if (CollectionUtils.isEmpty(propertyVOList)) {
            return;
        }
        // 验证扩展属性合法性
        Set<String> attributeIdSet = new HashSet<>();
        for (PropertyVO propertyVO : propertyVOList) {
            if (Objects.isNull(propertyVO)) {
                continue;
            }
            if (Objects.nonNull(propertyVO.getComGroupId()) && Objects.nonNull(propertyVO.getAttributeId())) {
                String key = propertyVO.getComGroupId() + "" + propertyVO.getAttributeId();
                if (attributeIdSet.contains(key)) {
                    throw new BizException("扩展属性中存在重复的属性ID");
                }
                attributeIdSet.add(key);
            }
        }
    }

    /**
     * 验证SPU标题是否超长。
     * @param spuLangExtendVOList SPU语言扩展信息列表。
     */
    public void validateSpuTitle(List<SpuLangExtendVO> spuLangExtendVOList,Long spuId){
        if(CollectionUtils.isEmpty(spuLangExtendVOList)){
            throw new BizException("商品多语集合不可为空");
        }

        AssertValidation.isTrue(spuLangExtendVOList.stream().noneMatch(extendVo-> LangConstant.LANG_EN.equals(extendVo.getLang())),"商品英文名为空" );

        for(SpuLangExtendVO spuLangExtendVO : spuLangExtendVOList){
            log.info("validateSpuTitle 校验商品名称必填 spuId={},spuLangExtendVO={}",spuId,JSON.toJSONString(spuLangExtendVO));
            if(LangConstant.LANG_ZH.equals(spuLangExtendVO.getLang())){
                AssertValidation.isTrue(StringUtils.isBlank(spuLangExtendVO.getSpuTitle()), "商品中文名为空");
            }
            if (LangConstant.LANG_EN.equals(spuLangExtendVO.getLang())){
                AssertValidation.isTrue(StringUtils.isBlank(spuLangExtendVO.getSpuTitle()), "商品英文名为空");
            }
            if(StringUtils.isNotBlank(spuLangExtendVO.getSpuTitle())){
                AssertValidation.isTrue(spuLangExtendVO.getSpuTitle().length()>200, "商品"+ langManageService.getLangNameByLangCode(spuLangExtendVO.getLang(), LangContextHolder.get())  +"名超长");
            }
        }
    }

    /**
     * 验证关键字是否超长。
     * @param spuLangExtendVOList SPU语言扩展信息列表。
     */
    public void validateKeyPhrases(List<SpuLangExtendVO> spuLangExtendVOList){
        if(CollectionUtils.isEmpty(spuLangExtendVOList)){
            return;
        }
        for(SpuLangExtendVO spuLangExtendVO : spuLangExtendVOList){
            if(StringUtils.isNotBlank(spuLangExtendVO.getKeyPhrases())){
                AssertValidation.isTrue(spuLangExtendVO.getKeyPhrases().length()>200, "商品"+ langManageService.getLangNameByLangCode(spuLangExtendVO.getLang(), LangContextHolder.get())  +"关键字超长");
            }
        }
    }

    /**
     * 验证供应商SpuId长度
     * @param spuVO SPU语言扩展信息列表。
     */
    public void validateVendorSpuId(SpuVO spuVO){
        if(spuVO.getVendorSpuId() == null){
            return;
        }
        AssertValidation.isTrue(spuVO.getVendorSpuId().length()>100, "商品供应商SPUID超长");
    }

    /**
     * 验证销售单位合法性
     * @param spuVO SPU语言扩展信息列表。
     */
    public void validateSaleUnit(SpuVO spuVO){
        if(spuVO.getSaleUnit() == null){
            throw new BizException("商品销售单位不可为空");
        }
        String saleUnitStr = extendInfoService.saleUnit(spuVO.getSaleUnit(),LangConstant.LANG_ZH);

        AssertValidation.isTrue(StringUtils.isBlank(saleUnitStr), "商品销售单位不正确");
    }

    /**
     * 验证型号
     * @param spuVO SPU语言扩展信息列表。
     */
    public void validateSpecification(SpuVO spuVO,Integer approveFlag){
        // 本土商品型号必填
        AssertValidation.isTrue(Constant.ZERO.intValue() ==approveFlag && !CountryConstant.COUNTRY_ZH.equals(spuVO.getSourceCountryCode()) && StringUtils.isBlank(spuVO.getSpecification()), "商品型号必填");
        AssertValidation.isTrue( StringUtils.isNotBlank(spuVO.getSpecification()) && spuVO.getSpecification().length()>100, "商品型号超长");
    }

    /**
     * 商品主图
     * @param spuVO SPU语言扩展信息列表。
     */
    public void validateSpuMainImage(SpuVO spuVO){
        if(StringUtils.isBlank(spuVO.getMainImg())){
            throw new BizException("商品主图不可为空");
        }
    }

    /**
     * 商品副图
     * @param spuVO SPU语言扩展信息列表。
     */
    public void validateSpuDetailImage(SpuVO spuVO){
        if(CollectionUtils.isEmpty(spuVO.getDetailImgList())){
            return;
        }
        AssertValidation.isTrue(spuVO.getDetailImgList().size()>4, "商品副图不允许超过4张");
    }

    /**
     * 校验审核通过参数
     *
     * @param spuAuditRecordUpdateVO
     */
    public void validateApproved(SpuAuditRecordUpdateVO spuAuditRecordUpdateVO) {
        List<Long> spuIds = spuAuditRecordUpdateVO.getSpuIds();
        // 查询spuId是否存在
        validateSpuExisted(spuIds);
    }

    /**
     * 校验驳回参数
     *
     * @param spuAuditRecordUpdateVO
     */
    public void validateReject(SpuAuditRecordUpdateVO spuAuditRecordUpdateVO) {
        List<Long> spuIds = spuAuditRecordUpdateVO.getSpuIds();
        // 查询spuId的草稿存在
        this.validateSpuExisted(spuIds);
    }

    public void validateSpuExisted(List<Long> spuIds) {
        List<SpuDraftPO> spuPoList = spuDraftAtomicService.querySpuList(spuIds);
        AssertValidation.isEmpty(spuPoList, SPU_NOT_EXIST_ERROR, getMessage(SPU_NOT_EXIST_ERROR) + spuPoList);

        List<Long> dbSpuIds = spuPoList.stream().map(SpuDraftPO::getSpuId).collect(Collectors.toList());
        Collection<Long> subtract = CollectionUtils.subtract(spuIds, dbSpuIds);
        AssertValidation.isEmpty(CollectionUtils.isNotEmpty(subtract), SPU_NOT_EXIST_ERROR, getMessage(SPU_NOT_EXIST_ERROR));

        // 校验当前审核状态是否能驳回
        boolean existOnSale = spuPoList.stream().anyMatch(po -> SpuAuditStatusEnum.canNotRejectStatus(po.getAuditStatus()));
        AssertValidation.isTrue(existOnSale, SPU_AUDIT_CAN_NOT_REJECT_ERROR, getMessage(SPU_AUDIT_CAN_NOT_REJECT_ERROR));
    }

    public void validateSpu(List<Long> spuIds) {
        List<SpuDraftPO> spuPoList = spuDraftAtomicService.querySpuList(spuIds);
        AssertValidation.isEmpty(spuPoList, SPU_NOT_EXIST_ERROR, getMessage(SPU_NOT_EXIST_ERROR) + spuPoList);

        List<Long> dbSpuIds = spuPoList.stream().map(SpuDraftPO::getSpuId).collect(Collectors.toList());
        Collection<Long> subtract = CollectionUtils.subtract(spuIds, dbSpuIds);
        AssertValidation.isEmpty(CollectionUtils.isNotEmpty(subtract), SPU_NOT_EXIST_ERROR, getMessage(SPU_NOT_EXIST_ERROR));
    }


    private String getAttributeIdAndName(Map<Long, AttributeFlatVO> attributeFlatVoMap) {
        StringBuilder stringBuilder = new StringBuilder();
        attributeFlatVoMap.forEach((k, v) -> stringBuilder.append(v.getAttributeName()).append("[").append(k).append("]").append(" "));
        return stringBuilder.toString();
    }

    /**
     * 校验已存储商品信息和当前页面商品信息对比
     *
     * @param saveSpuVO
     */
    public void validateDbSaveSpuVoAndViewSaveSpuVo(SaveSpuVO saveSpuVO) {
        String result = spuCompareService.compareSaveSpu(saveSpuVO);
        log.info("SpuValidateService.validateDbSaveSpuVoAndViewSaveSpuVo 比较结果 result={}", result);
        AssertValidation.isBlank(result, SPU_NOT_CHANGE_ERROR, getMessage(SPU_NOT_CHANGE_ERROR));
    }

    public void validateCategoryTaxRate(SpuVO spuVO, List<SkuVO> skuVOList) {
        Set<Long> skuIds = skuVOList.stream().filter(Objects::nonNull).map(SkuVO::getSkuId).collect(Collectors.toSet());
        List<CategoryTaxPO> skuCategoryTaxPoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(skuIds)) {
            LambdaQueryWrapper<CategoryTaxPO> queryWrapper = Wrappers.lambdaQuery(CategoryTaxPO.class)
                    .eq(CategoryTaxPO::getJdCatId, spuVO.getCatId())
                    .eq(CategoryTaxPO::getCountryCode, spuVO.getSourceCountryCode())
                    // 企业性质枚举 1:EPE, 2:FDI
                    .in(CategoryTaxPO::getCompanyType, CompanyTypeEnum.codes())
                    .in(CategoryTaxPO::getSkuId,skuIds);
            skuCategoryTaxPoList = categoryTaxAtomicService.getBaseMapper().selectList(queryWrapper);
        }
        // 如果SKU还未生成或者sku未设置类目税率时校验类目税率
        if (CollectionUtils.isEmpty(skuIds) || CollectionUtils.isEmpty(skuCategoryTaxPoList)){
            LambdaQueryWrapper<CategoryTaxPO> queryWrapper = Wrappers.lambdaQuery(CategoryTaxPO.class)
                    .eq(CategoryTaxPO::getJdCatId, spuVO.getCatId())
                    .eq(CategoryTaxPO::getCountryCode, spuVO.getSourceCountryCode())
                    // 企业性质枚举 1:EPE, 2:FDI
                    .in(CategoryTaxPO::getCompanyType, CompanyTypeEnum.codes())
                    .isNull(CategoryTaxPO::getSkuId);
            List<CategoryTaxPO> categoryTaxPoList = categoryTaxAtomicService.getBaseMapper().selectList(queryWrapper);
            AssertValidation.isEmpty(CollectionUtils.isEmpty(categoryTaxPoList) || categoryTaxPoList.size() != 2, String.format("当前类目 %s 税率设置不全", spuVO.getCatId()));
            Map<Integer, CategoryTaxPO> taxPoMap = categoryTaxPoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(CategoryTaxPO::getCompanyType, Function.identity()));
            // EPE
            CategoryTaxPO epe = taxPoMap.get(CompanyTypeEnum.EPE.getCode());
            AssertValidation.isTrue(Objects.isNull(epe) || Objects.isNull(epe.getDestinationVatRate()), String.format("当前类目 %s 目的国增值税率未设置", spuVO.getCatId()));
            // FDI
            CategoryTaxPO fdi = taxPoMap.get(CompanyTypeEnum.FDI.getCode());
            AssertValidation.isTrue(Objects.isNull(fdi) || Objects.isNull(fdi.getDestinationVatRate()), String.format("当前类目 %s 目的国增值税率未设置", spuVO.getCatId()));
        }
    }

    /**
     * 校验当前供应商简码和国家编码关系
     * @param vendorCode 供应商简码
     * @param countryCode 国家编码
     */
    public void validateVendorCountry(String vendorCode, String countryCode) {
        AssertValidation.isTrue(!supplierAllInfoAtomicService.checkSupplierCodeCountry(vendorCode, countryCode),
            String.format("供应商简码 %s 不属于当前国家 %s", vendorCode,
                countryManageService.getCountryNameByCountryCode(countryCode,null)));
    }

    private String getMessage(String code) {
        return dataResponseMessageService.getErrorMessage(code);
    }

    private void validateInterProperty(Long catId,String sourceCountryCode,List<String> targetCountryCodes,
        List<GroupPropertyVO> spuGroupPropertyVOS,Integer isExport){
        // 校验跨境属性
        List<GroupPropertyVO> groupPropertyVOS = globalAttributeManageService.queryGroupPropertyVos(catId,
            sourceCountryCode, targetCountryCodes, AttributeDimensionEnum.SPU.getCode(), null,isExport);
        if(CollectionUtils.isEmpty(groupPropertyVOS)){
            log.info("SpuValidateService.checkInterProperty groupPropertyVOS is null");
            return;
        }

        groupPropertyVOS = groupPropertyVOS.stream().filter(Objects::nonNull).filter(item->item.getRequirement().equals(AttributeCheckTypeEnum.PUBLISH_PRODUCT.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(groupPropertyVOS)){
            log.info("SpuValidateService.checkInterProperty groupPropertyVOS2 is null");
            return;
        }

        AssertValidation.isTrue(CollectionUtils.isEmpty(spuGroupPropertyVOS),"请填写发品必填国际属性");
        List<PropertyVO> propertyVOS = spuGroupPropertyVOS.stream().filter(Objects::nonNull).filter(item->CollectionUtils.isNotEmpty(item.getPropertyVOS()))
                .flatMap(group -> group.getPropertyVOS().stream())
                .collect(Collectors.toList());
        AssertValidation.isTrue(CollectionUtils.isEmpty(propertyVOS), "请填写发品必填国际属性");
        Map<Long,PropertyVO> interPropertyMap = propertyVOS.stream().collect(Collectors.toMap(PropertyVO::getAttributeId,Function.identity()));

        groupPropertyVOS.get(0).getPropertyVOS().forEach(item->{
            // 不存在属性和当前类目的关系
            AssertValidation.isTrue(!interPropertyMap.containsKey(item.getAttributeId()), SPU_INTER_ATTRIBUTE_NOT_EXIST_ERROR, getMessage(SPU_INTER_ATTRIBUTE_NOT_EXIST_ERROR));
            // 校验属性值文本输入时，校验属性值类型
            this.validateGlobalAttributeValueForText(item, interPropertyMap);
        });
    }

    /**
     * 验证全局属性值是否为文本类型，并检查数字类型的输入内容是否合法。
     * @param item 属性VO对象，包含属性的详细信息。
     * @param interPropertyMap 属性ID到属性VO的映射关系。
     */
    private void validateGlobalAttributeValueForText(PropertyVO item, Map<Long, PropertyVO> interPropertyMap) {
        PropertyVO propertyVO = interPropertyMap.get(item.getAttributeId());
        if (AttributeInputTypeEnum.TEXT.getCode().equals(propertyVO.getAttributeInputType())) {
            List<PropertyValueVO> valueVOS = item.getPropertyValueVOList();
            // 数字类型校验输入内容
            if (Objects.nonNull(propertyVO.getInputCheckType()) && AtrributeValueInputCheckTypeEnum.NUMBER.getCode().equals(propertyVO.getInputCheckType())) {
                if (CollectionUtils.isNotEmpty(valueVOS)) {
                    // 校验不是数字跨境属性值
                    Optional<PropertyValueVO> first = valueVOS.stream().filter(value -> !NumberUtils.isPositiveNumeric(value.getAttributeValueName())).findFirst();
                    if (first.isPresent()) {
                        throw new BizException(String.format("跨境属性值 %s 不是正整数类型", first.get().getAttributeValueName()));
                    }
                }
            }else if(Objects.nonNull(propertyVO.getInputCheckType()) && AtrributeValueInputCheckTypeEnum.DECIMAL.getCode().equals(propertyVO.getInputCheckType())){
                if (CollectionUtils.isNotEmpty(valueVOS)) {
                    // 校验不是正小数数字跨境属性值
                    Optional<PropertyValueVO> first = valueVOS.stream().filter(value -> !NumberUtils.isPositiveDecimal(value.getAttributeValueName())).findFirst();
                    if (first.isPresent()) {
                        throw new BizException(String.format("跨境属性值 %s 不是正小数类型", first.get().getAttributeValueName()));
                    }
                }
            }else if(Objects.nonNull(propertyVO.getInputCheckType()) && AtrributeValueInputCheckTypeEnum.STRING.getCode().equals(propertyVO.getInputCheckType())){
                if (CollectionUtils.isNotEmpty(valueVOS)) {
                    // 校验不是正小数数字跨境属性值
                    Optional<PropertyValueVO> first = valueVOS.stream()
                            .filter(value -> StringUtils.isNotBlank(value.getAttributeValueName()) && value.getAttributeValueName().length()>100).findFirst();
                    if (first.isPresent()) {
                        throw new BizException(String.format("跨境属性值超出100位数", first.get().getAttributeValueName()));
                    }
                }
            }
        }
    }

    private void validateCertificate(Long catId,String sourceCountryCode,List<String> targetCountryCodes,
        List<SpuCertificateVO> spuCertificateVOList, Integer isExport){

        List<GlobalQualificationVO> qualificationVOS = globalQualificationManageService.queryByCountry(catId,
            sourceCountryCode,targetCountryCodes, AttributeDimensionEnum.SPU.getCode(),null,isExport);
        if(CollectionUtils.isEmpty(qualificationVOS)){
            log.info("SpuValidateService.validateCertificate qualificationVOS is null");
            return;
        }
        qualificationVOS = qualificationVOS.stream().filter(Objects::nonNull).filter(item->item.getRequirement().equals(AttributeCheckTypeEnum.PUBLISH_PRODUCT.getCode().toString())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(qualificationVOS)){
            log.info("SpuValidateService.validateCertificate qualificationVOS2 is null");
            return;
        }

        // 校验跨境属性
        AssertValidation.isTrue(CollectionUtils.isEmpty(spuCertificateVOList),"请填写spu发品必填资质");
        spuCertificateVOList = spuCertificateVOList.stream().filter(Objects::nonNull).filter(item->item.getRequirement().equals(AttributeCheckTypeEnum.PUBLISH_PRODUCT.getCode())).collect(Collectors.toList());
        AssertValidation.isTrue(CollectionUtils.isEmpty(spuCertificateVOList), "请填写spu发品必填资质");
        Map<Long, SpuCertificateVO> certificateVOMap = spuCertificateVOList.stream().collect(Collectors.toMap(SpuCertificateVO::getCertificateId, Function.identity()));

        for(GlobalQualificationVO item : qualificationVOS){
            AssertValidation.isTrue(!certificateVOMap.containsKey(item.getId()), SPU_INTER_CERT_NOT_EXIST_ERROR, getMessage(SPU_INTER_CERT_NOT_EXIST_ERROR));
        }
    }

    public String validateBusinessLineRelation(String vendorCode, Long catId,Long brandId) {
        BusinessLineRelationPO businessLineRelationPO = businessLineRelationAtomicService.querySupplierLineByCatIdAndBrandId(vendorCode,catId,brandId);
        if(businessLineRelationPO == null){
            throw new BizException(String.format("供应商无对应产品线，supplierCode:%s,catId:%s,brandId:%s",vendorCode,catId,brandId));
        }
        return businessLineRelationPO.getSupplierCode();
    }

    public void validateBusinessLineRelationAndSetVendorCode(SpuVO spuVO) {
        String vendorCode = spuVO.getVendorCode();
        Long catId = spuVO.getCatId();
        Long brandId = spuVO.getBrandId();
        String supplierCode = this.validateBusinessLineRelation(vendorCode,catId,brandId);
        spuVO.setVendorCode(supplierCode);
    }

    private void validateSpuTitleUpdate(SpuVO spuVO){
        if(spuVO.getSpuId() == null){
            log.info("SpuValidateService.validateSpuTitleUpdate spuId is null");
            return;
        }
        List<SpuLangExtendVO> spuLangExtendVOList = spuVO.getSpuLangExtendVOList();
        if(CollectionUtils.isEmpty(spuLangExtendVOList)){
            log.info("SpuValidateService.validateSpuTitleUpdate spuLangExtendVOList is null");
            return;
        }
        if(!limitSpuIds.contains(spuVO.getSpuId().toString())){
            log.info("SpuValidateService.validateSpuTitleUpdate limitSpuIds not contains");
            return;
        }

        Map<String, SpuLangPO> spuLangPOMap = spuLangAtomicService.langTitleMap(spuVO.getSpuId());
        if(MapUtils.isEmpty(spuLangPOMap)){
            log.info("SpuValidateService.validateSpuTitleUpdate spuLangPOMap is null");
            return;
        }

        Map<String,String> langMap = langManageService.getLangMap(LangContextHolder.get());
        for(SpuLangExtendVO spuLangExtendVO : spuLangExtendVOList){
            if(spuLangExtendVO == null || StringUtils.isBlank(spuLangExtendVO.getLang())){
                continue;
            }
            SpuLangPO spuLangPO = spuLangPOMap.get(spuLangExtendVO.getLang());
            if(spuLangPO == null || StringUtils.isBlank(spuLangPO.getSpuTitle())){
                continue;
            }
            if(!StringUtils.equals(spuLangPO.getSpuTitle(),spuLangExtendVO.getSpuTitle())){
                throw new BizException(String.format("此商品为标题锁定商品，请勿修改标题，如有疑问请联系乔森远。标题:%s,spuId:%s",langMap.get(spuLangExtendVO.getLang()),spuVO.getSpuId()));
            }
        }
    }

    private void validateSpuMainImageUpdate(SpuVO spuVO){
        if(spuVO.getSpuId() == null){
            log.info("SpuValidateService.validateSpuMainImageUpdate spuId is null");
            return;
        }

        String mainImg = spuVO.getMainImg();
        if(StringUtils.isBlank(mainImg)){
            log.info("SpuValidateService.validateSpuMainImageUpdate mainImg is null");
            return;
        }

        if(!limitSpuIds.contains(spuVO.getSpuId().toString())){
            log.info("SpuValidateService.validateSpuMainImageUpdate limitSpuIds not contains");
            return;
        }

        SpuPO spuPO = spuAtomicService.getSpuPoBySpuId(spuVO.getSpuId());
        if(spuPO == null){
            log.info("SpuValidateService.validateSpuMainImageUpdate spuPO is null");
            return;
        }

        if(!StringUtils.equals(mainImg,spuPO.getMainImg())){
            throw new BizException(String.format("此商品为商品主图锁定商品，请勿修改主图，如有疑问请联系乔森远。spuId:%s",spuVO.getSpuId()));
        }
    }

    /**
     * 验证SPU销售属性的合法性。
     * @param saveSpuVO 保存SPU的VO对象，包含了SPU的详细信息和销售属性。
     */
    public void validateSpuSaleProperty(SaveSpuVO saveSpuVO) {
        Set<String> countryCodes = new HashSet<>();
        String attributeScope = saveSpuVO.getSpuVO().getAttributeScope();
        if(StringUtils.isNotBlank(attributeScope)){
            countryCodes = Arrays.stream(attributeScope.split(Constant.COMMA)).collect(Collectors.toSet());
        } else {
            countryCodes.add(saveSpuVO.getSpuVO().getSourceCountryCode());
        }
        List<LangVO> langVOS = countryLangManageService.queryLangListByCountry(countryCodes,null);
        Set<String> langSet = langVOS.stream().map(LangVO::getLangCode).collect(Collectors.toSet());

//        List<AttributeFlatVO> attributeFlatVOS = attributeOutService.querySellAttr(null);
//        List<Long> attributeIds = attributeFlatVOS.stream().map(AttributeFlatVO::getId).collect(Collectors.toList());
//
//        List<PropertyVO> salePropertyList = new ArrayList<>();//saveSpuVO.getSalePropertyList();
//        if(CollectionUtils.isEmpty(salePropertyList)){
//            throw new BizException("销售属性组不能为空");
//        }
//
//        int total = 1;
//        for(PropertyVO propertyVO : salePropertyList){
//            if(propertyVO == null){
//                throw new BizException("销售属性不可为空");
//            }
//            if (propertyVO.getAttributeId() == null) {
//                throw new BizException("销售属性id不可为空");
//            }
//            if(!attributeIds.contains(propertyVO.getAttributeId())){
//                throw new BizException("销售属性id不合法");
//            }
//
//            List<PropertyValueVO> propertyValueVOList = propertyVO.getPropertyValueVOList();
//
//            // 检查销售属性值是否合法
//            this.validateSalePropertyValue(propertyValueVOList,langSet,attributeIds);
//
//            // 单品销售属性不可超出100
//            if(CollectionUtils.isNotEmpty(propertyValueVOList)){
//                int size = propertyValueVOList.size();
//                total = total * size;
//                if(total > 100){
//                    throw new BizException("单品销售属性不可超出100");
//                }
//            }
//
//        }

    }

    /**
     * 检查销售属性值是否合法。
     * @param propertyValueVOList 销售属性值列表
     * @param langSet 允许的语种编码集合
     */
    public void validateSalePropertyValue(List<PropertyValueVO> propertyValueVOList, Set<String> langSet,List<Long> attributeIds) {
        if (CollectionUtils.isEmpty(propertyValueVOList)) {
            throw new BizException("销售属性值不能为空");
        }

        Set<Integer> sortSet = new HashSet<>();

        for (PropertyValueVO propertyValueVO : propertyValueVOList) {
            if (propertyValueVO == null) {
                throw new BizException("销售属性值不可为空");
            }
            if (propertyValueVO.getAttributeId() == null) {
                throw new BizException("销售属性id不可为空");
            }
            if (!attributeIds.contains(propertyValueVO.getAttributeId())) {
                throw new BizException("销售属性id不合法");
            }
            if (propertyValueVO.getSort() == null) {
                throw new BizException("销售属性值排序不可为空");
            }
            if (!sortSet.add(propertyValueVO.getSort())) {
                throw new BizException("销售属性值排序不可重复: " + propertyValueVO.getSort());
            }

            List<BaseLangVO> baseLangVOS = propertyValueVO.getLangList();
            if (CollectionUtils.isEmpty(baseLangVOS)) {
                throw new BizException("销售属性值所属语种集合不可为空");
            }

            Set<String> langCodes = new HashSet<>();
            Map<String, Set<String>> langNameMap = new HashMap<>();

            for (BaseLangVO baseLangVO : baseLangVOS) {
                String langCode = baseLangVO.getLang();
                String langName = baseLangVO.getLangName();

                if (StringUtils.isBlank(langCode)) {
                    throw new BizException("销售属性值所属语种编码不可为空");
                }
                if (!langSet.contains(langCode)) {
                    throw new BizException("销售属性值所属语种编码不在允许的集合中: " + langCode);
                }
                if (!langCodes.add(langCode)) {
                    throw new BizException("销售属性值所属语种编码重复: " + langCode);
                }
                if (StringUtils.isBlank(langName)) {
                    throw new BizException("销售属性值所属语种名称不可为空");
                }
                if (langName.length() > 50) {
                    throw new BizException("销售属性值所属语种名称长度不能超过50字符");
                }
                if (this.containsSpecialCharacters(langName)) {
                    throw new BizException("销售属性值所属语种名称包含特殊字符");
                }

                langNameMap.computeIfAbsent(langCode, k -> new HashSet<>()).add(langName);
            }

            // 检查不同propertyValueVO下，同语种下名称重复问题
            for (Map.Entry<String, Set<String>> entry : langNameMap.entrySet()) {
                if (entry.getValue().size() != baseLangVOS.size()) {
                    throw new BizException("销售属性值名称同语种下名称重复: " + entry.getKey());
                }
            }
        }
    }

    /**
     * 检查输入字符串是否包含特殊字符。
     * @param input 要检查的字符串。
     * @return 如果输入字符串包含特殊字符，则返回 true；否则返回 false。
     */
    private boolean containsSpecialCharacters(String input) {
        // 使用正则表达式检查是否包含特殊字符
        String regex = "^[^,。#！？【】；!\\?\\[\\]@\\$\\^`\\*\\.]+$";
        return Pattern.compile(regex).matcher(input).find();
    }
}
