package com.jdi.isc.product.soa.service.manage.spu.impl.write;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.tp.common.utils.Objects;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.TradeTypeConstant;
import com.jdi.isc.product.soa.api.common.enums.SpuAuditStatusEnum;
import com.jdi.isc.product.soa.domain.enums.FileTypeEnum;
import com.jdi.isc.product.soa.domain.enums.vendor.IdentityEnum;
import com.jdi.isc.product.soa.domain.sku.biz.GroupSkuCertificateVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCertificateVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.service.atomic.price.SkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.stock.StockAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.countryTax.BrSkuTaxAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseAtomicService;
import com.jdi.isc.product.soa.service.manage.attribute.AttributeOutService;
import com.jdi.isc.product.soa.service.manage.common.FileManageService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuWriteManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuAuditRecordManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuDraftManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuWriteManageService;
import com.jdi.isc.product.soa.service.manage.spu.validate.SkuValidateService;
import com.jdi.isc.product.soa.service.manage.spu.validate.SpuValidateService;
import com.jdi.isc.product.soa.service.manage.vendor.VendorManageService;
import com.jdi.isc.product.soa.service.mapstruct.SkuConvertService;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.Constant.COMMA;

/**
 * <AUTHOR>
 * @date 2023/12/11
 **/
@Slf4j
@Service("crossborderSpuWriteManageService")
public class CrossborderSpuWriteManageServiceImpl extends AbstractSpuWriteManageService implements SpuWriteManageService {

    @Resource
    private SpuValidateService spuValidateService;

    @Resource
    private SkuValidateService skuValidateService;

    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private SpuDraftManageService spuDraftManageService;

    @Resource
    private VendorManageService vendorManageService;

    @Resource
    private SpuAuditRecordManageService spuAuditRecordManageService;

    @LafValue("jdi.isc.store.url.prefix")
    private String storeFileUrlPreFix;

    @LafValue("jdi.isc.image.url.prefix")
    private String imageUrlPrefix;

    @Resource
    private FileManageService fileManageService;

    @Resource
    private SkuPriceAtomicService skuPriceAtomicService;

    @Resource
    private SkuConvertService skuConvertService;

    @Resource
    private SpuConvertService spuConvertService;

    @Resource
    private StockAtomicService stockAtomicService;

    @Resource
    private WarehouseAtomicService warehouseAtomicService;

    @Resource
    private AttributeOutService attributeOutService;

    @Resource
    private SkuWriteManageService skuWriteManageService;

    @Resource
    private BrSkuTaxAtomicService brSkuTaxAtomicService;

    @Resource
    private SaleAttributeManageService saleAttributeManageService;

    @Override
    public DataResponse<Long> create(SaveSpuVO saveSpuVO) {
        SpuVO spuVO = saveSpuVO.getSpuVO();

        // 跨境品新建sku销售属性校验 ZHAOYAN_SALE_ATTR
        skuValidateService.validateCreateSkuSaleAttributeForCrossBorder(spuVO, saveSpuVO.getSkuVOList());

        spuVO.setIdentityFlag(IdentityEnum.BUYER.getCode());
        if (Objects.isNull(spuVO.getAuditStatus())){
            spuVO.setAuditStatus(SpuAuditStatusEnum.WAITING_APPROVED.getCode());
        }
        // 创建跨境品（主站sku）时，从中台拉取扩展属性，并添加到spu和sku中（以便后续利用通用保存方法）
        try {
            extractedJDSkuStoreExtendProperty(saveSpuVO);
        } catch (Exception e) {
            log.error("CrossborderSpuWriteManageServiceImpl.create error:{}", e);
        } finally {
            log.info("CrossborderSpuWriteManageServiceImpl.create finally 从中台拉取扩展属性完成:{}", JSONObject.toJSONString(saveSpuVO));
        }
        // 3. 调用通用保存方法
        return super.create(saveSpuVO);
    }

    public void extractedJDSkuStoreExtendProperty(SaveSpuVO saveSpuVO) {
        SpuVO spuVO = saveSpuVO.getSpuVO();
        // 1. 拉取扩展属性（包含中台类目扩展属性和sku的扩展属性），用中台类目扩展属性进行过滤，即不在当前类目的扩展属性要过滤掉
        if(CollectionUtils.isNotEmpty(saveSpuVO.getSkuVOList()) && Objects.nonNull(spuVO.getCatId()) && Objects.nonNull(saveSpuVO.getSkuVOList().get(0))){
            // 跨境品只有一个sku，一个spu对应一个sku, 获取唯一的jdskuId
            Long jdSkuId = saveSpuVO.getSkuVOList().get(0).getJdSkuId();
            List<PropertyVO> propertyVOList = attributeOutService.obtainJDSkuExtAttributeList(spuVO.getCatId(), jdSkuId, LangConstant.LANG_ZH);
            // 根据PropertyVO中的level进行分组，level为0的属性添加到spu中，否则添加到sku中，同时过滤调非selected=true的属性

            List<PropertyVO> spuStoreExtendPropertyList = new ArrayList<>();
            List<PropertyVO> skuStoreExtendPropertyList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(propertyVOList)) {
                for (PropertyVO propertyVO : propertyVOList) {
                    List<PropertyValueVO> propertyValueVOList = propertyVO.getPropertyValueVOList();
                    if (CollectionUtils.isNotEmpty(propertyValueVOList)) {
                        // 获取selected=true的属性值
                        List<PropertyValueVO> selectedPropertyValueVOList = propertyValueVOList.stream().filter(PropertyValueVO::getSelected).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(selectedPropertyValueVOList)) {
                            propertyVO.setPropertyValueVOList(selectedPropertyValueVOList);
                            if (Objects.nonNull(propertyVO.getLevel()) && propertyVO.getLevel() == PropertyVO.ONLY_SPU_LEVEL_EXT_ATTR_VALUE) {
                                spuStoreExtendPropertyList.add(propertyVO);
                            } else {
                                skuStoreExtendPropertyList.add(propertyVO);
                            }
                        }
                    }
                }
            }
            // 2. 添加中台sku扩展属性到spu和sku中
            saveSpuVO.setStoreExtendPropertyList(spuStoreExtendPropertyList);
            saveSpuVO.getSkuVOList().get(0).setStoreExtendPropertyList(skuStoreExtendPropertyList);
            log.info("CrossborderSpuWriteManageServiceImpl.extractedJDSkuStoreExtendProperty CATID: {},JD_SKU_ID: {},spuStoreExtendPropertyList: {},skuStoreExtendPropertyList: {}", spuVO.getCatId(), jdSkuId, spuStoreExtendPropertyList, skuStoreExtendPropertyList);
        }
    }

    @Override
    public DataResponse<Long> saveDraft(SaveSpuVO saveSpuVO) {
        SpuVO spuVO = saveSpuVO.getSpuVO();
        Long spuId = spuVO.getSpuId();
        if (Objects.isNull(spuId)) {
            spuVO.setAuditStatus(SpuAuditStatusEnum.DRAFT.getCode());
            return this.create(saveSpuVO);
        } else {
            spuVO.setAuditStatus(SpuAuditStatusEnum.DRAFT.getCode());
            return super.update(saveSpuVO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Long> submit(SaveSpuVO saveSpuVO) {
        // 校验sku是否存在
        this.checkSkuList(saveSpuVO);
        handleSkuListEXW(saveSpuVO);
        // 补充跨境属性ncm码
        spuConvertService.handleNcmCodeTry(saveSpuVO.getSkuVOList(), saveSpuVO.getSpuVO());
        SpuVO spuVO = saveSpuVO.getSpuVO();
        Long spuId = spuVO.getSpuId();
        if(Objects.isNull(spuId)){
            return this.create(saveSpuVO);
        } else if (SpuAuditStatusEnum.WAITING_APPROVED_VENDOR_SUBMIT.getCode().equals(spuVO.getAuditStatus())) {
            return this.updateForJdm(saveSpuVO);
        }else {
            return this.update(saveSpuVO);
        }
    }

    private void handleSkuListEXW(SaveSpuVO saveSpuVO){
        List<@Valid SkuVO> skuVOList = saveSpuVO.getSkuVOList();
        if (CollectionUtils.isNotEmpty(skuVOList)) {
            skuVOList.forEach(skuVO -> {
                if (StringUtils.isBlank(skuVO.getVendorTradeType())){
                    skuVO.setVendorTradeType(TradeTypeConstant.EXW);
                }
                if (StringUtils.isBlank(skuVO.getCustomerTradeType())) {
                    skuVO.setCustomerTradeType(TradeTypeConstant.EXW);
                }
            });
        }
    }

    /**
     * 京麦更新商品保存方法
     * @param saveSpuVO
     */
    private DataResponse<Long> updateForJdm(SaveSpuVO saveSpuVO){
        SpuVO spuVO = saveSpuVO.getSpuVO();
        // 判断是否修改了jdSkuId
        Boolean isUpdateJdSkuId = this.isUpdateJdSkuId(saveSpuVO);

        // 校验销售属性
        this.validateSaleAttributeUpdate(isUpdateJdSkuId, spuVO,saveSpuVO.getSkuVOList());

        spuVO.setSourceCountryCode(StringUtils.isBlank(spuVO.getSourceCountryCode()) ? CountryConstant.COUNTRY_ZH: spuVO.getSourceCountryCode());
        // 设置采销
        String buyer = categoryBuyerRelationAtomicService.getBuyerByCountryCodeAndCatId(saveSpuVO.getSpuVO().getSourceCountryCode(),saveSpuVO.getSpuVO().getCatId());
        saveSpuVO.getSpuVO().setBuyer(buyer);
        // 校验入参
        spuVO.setAuditStatus(SpuAuditStatusEnum.WAITING_APPROVED_VENDOR_SUBMIT.getCode());
        // 补全sku销售价
        this.fillSkuSalePrice(saveSpuVO.getSkuVOList());
        // 补全库存列表
        spuConvertService.fillCrossBorderSkuStock(saveSpuVO.getSkuVOList(),spuVO.getAttributeScope());
        // 补充国内供应商编码
        skuConvertService.fillJdVendorCode(saveSpuVO.getSkuVOList());
        spuConvertService.convertVoToLangPo(spuVO);
        // 保存草稿
        Long spuId = spuDraftManageService.saveOrUpdateDraft(saveSpuVO);
        // 更新商品状态
        this.updateStatusToWaitApproveForJdm(spuId);
        // 添加审核记录
        // this.addRecord(spuVO, spuId);
        // 异步加载图片和文件
        CompletableFuture.runAsync(()-> this.convertFileUrl(saveSpuVO));
        // 更新跨境品销售属性值多语言信息 ZHAOYAN_SALE_ATTR
        if(Objects.nonNull(isUpdateJdSkuId) && isUpdateJdSkuId){
            // 京麦更新跨境品销售属性，京麦修改不了主站sku，只能修改非中文多语言属性值，暂时注释
//            saleAttributeManageService.updateSkuSaleAttributeForCrossBorder(spuId, saveSpuVO.getSkuVOList());
            log.info("CrossborderSpuWriteManageServiceImpl.updateForJdm 修改了jdSkuId, 仅更新跨境品销售属性 spuId: {}", spuId);
        } else if(Objects.nonNull(isUpdateJdSkuId) && !isUpdateJdSkuId){
            // 京麦更新跨境品销售属性值多语言信息
            saleAttributeManageService.updateSkuSaleAttributeValueLangNameForCrossBorder(saveSpuVO.getSkuVOList());
            log.info("CrossborderSpuWriteManageServiceImpl.updateForJdm 没有修改jdSkuId, 更新跨境品销售属性值多语言信息 spuId: {}", spuId);
        } else {
            log.error("CrossborderSpuWriteManageServiceImpl.updateForJdm isUpdateJdSkuId为空，无法判断是跟新销售属性还是更新销售属性值多语言信息 spuId: {}", spuId);
        }
        return DataResponse.success(spuId);
    }

    @Override
    void validateSaveParams(SaveSpuVO saveSpuVO) {
        spuValidateService.validateSaveParams(saveSpuVO);
    }

    @Override
    void validateCreateSkuVoList(List<SkuVO> skuVOList,SpuVO spuVO) {
        skuValidateService.validateJdSkuNull(skuVOList);
        skuValidateService.validateCreateSkuVoList(skuVOList);
        skuValidateService.validateSkuStockList(skuVOList,spuVO.getSourceCountryCode());
        //  校验国内skuId重复
        skuValidateService.validateJdSkuRelation(skuVOList);
        List<Long> skuIds = skuVOList.stream().map(SkuVO::getJdSkuId).collect(Collectors.toList());

        skuValidateService.validateSkuIdCanPurchase(skuIds);
        skuValidateService.validateSkuIdAreaLimit(skuIds);
        // 校验价格
        skuValidateService.validateSkuPrice(skuVOList, spuVO.getSourceCountryCode());
        // 校验长宽高重
        skuValidateService.validateFourDimension(skuVOList);
        // 校验跨境属性
        skuValidateService.validateInterProperty(spuVO.getCatId()
                ,spuVO.getSourceCountryCode()
                ,StringUtils.isNotBlank(spuVO.getAttributeScope()) ? Arrays.asList(spuVO.getAttributeScope().split(COMMA)) : Lists.newArrayList()
                ,skuVOList,spuVO.getIsExport());
        // 校验跨境资质
        skuValidateService.validateCertificate(spuVO.getCatId()
                ,spuVO.getSourceCountryCode()
                ,StringUtils.isNotBlank(spuVO.getAttributeScope()) ? Arrays.asList(spuVO.getAttributeScope().split(COMMA)) : Lists.newArrayList()
                ,skuVOList,spuVO.getIsExport());
        // 校验出口HsCode
        skuValidateService.validateHsCode(skuVOList);
        // 校验条形码
        skuValidateService.validateUpcCode(skuVOList);
        // todo zhaokun51 验证 SKU 销售属性值是否合法。
//        skuValidateService.validateSkuSaleProperty(spuVO,skuVOList);
    }

    @Override
    void validateUpdateSkuVoList(List<SkuVO> skuVOList, SpuVO spuVO) {
        List<SkuPO> dbSkuPoList = skuAtomicService.selectSkuPosBySpuId(spuVO.getSpuId());
        skuValidateService.validateUpdateSkuVOList(skuVOList, spuVO.getSpuId(), dbSkuPoList);
        List<Long> jdSkuIds = skuVOList.stream().map(SkuVO::getJdSkuId).collect(Collectors.toList());

        skuValidateService.validateSkuIdCanPurchase(jdSkuIds);
        skuValidateService.validateSkuIdAreaLimit(jdSkuIds);
        // 校验价格
        skuValidateService.validateSkuPrice(skuVOList, spuVO.getSourceCountryCode());
        // 校验长宽高重
        skuValidateService.validateFourDimension(skuVOList);
        // 校验跨境属性
        skuValidateService.validateInterProperty(spuVO.getCatId()
                ,spuVO.getSourceCountryCode()
                ,StringUtils.isNotBlank(spuVO.getAttributeScope()) ? Arrays.asList(spuVO.getAttributeScope().split(COMMA)) : Lists.newArrayList()
                ,skuVOList,spuVO.getIsExport());
        // 校验跨境资质
        skuValidateService.validateCertificate(spuVO.getCatId()
                ,spuVO.getSourceCountryCode()
                ,StringUtils.isNotBlank(spuVO.getAttributeScope()) ? Arrays.asList(spuVO.getAttributeScope().split(COMMA)) : Lists.newArrayList()
                ,skuVOList,spuVO.getIsExport());
        // 校验出口HsCode
        skuValidateService.validateHsCode(skuVOList);
        // 校验条形码
        skuValidateService.validateUpcCode(skuVOList);
        // todo zhaokun51 验证 SKU 销售属性值是否合法。
//        skuValidateService.validateSkuSaleProperty(spuVO,skuVOList);
    }

    @Override
    void validateDbSaveSpuVoAndViewSaveSpuVo(SaveSpuVO saveSpuVO) {
        spuValidateService.validateDbSaveSpuVoAndViewSaveSpuVo(saveSpuVO);
    }

    @Override
    void recordVendor(List<SkuVO> skuVOList) {
    }

    @Override
    void updateStatusToWaitApprove(Long spuId) {
        spuAtomicService.updateSpuAuditStatus(spuId, SpuAuditStatusEnum.WAITING_APPROVED);
    }

    private void updateStatusToWaitApproveForJdm(Long spuId) {
        spuAtomicService.updateSpuAuditStatus(spuId, SpuAuditStatusEnum.WAITING_APPROVED_VENDOR_SUBMIT);
    }

    /**
     * 将 SaveSpuVO 对象中的文件 URL 转换为 S3 存储的 URL。
     *
     * @param saveSpuVO 包含要转换的文件 URL 的 SaveSpuVO 对象
     * @throws NullPointerException 如果 saveSpuVO 或其中的某些必要字段为 null
     */
    private void convertFileUrl(SaveSpuVO saveSpuVO) {
        SpuVO spuVO = saveSpuVO.getSpuVO();
        // 主图
        boolean updateFlag = false;
        if (StringUtils.isNotBlank(spuVO.getMainImg()) && spuVO.getMainImg().contains(imageUrlPrefix)) {
            spuVO.setMainImg(fileManageService.downloadAndUploadS3(spuVO.getMainImg(), FileTypeEnum.SPU_IMAGE));
            updateFlag = true;
        }
        // 商品细节图
        if (CollectionUtils.isNotEmpty(spuVO.getDetailImgList())) {
            List<String> detailImgList = spuVO.getDetailImgList();
            for (int i =0, len = detailImgList.size();i < len ;i++) {
                String detailImage = detailImgList.get(i);
                if (detailImage.contains(imageUrlPrefix)){
                    detailImgList.set(i,fileManageService.downloadAndUploadS3(detailImage, FileTypeEnum.SPU_IMAGE));
                    updateFlag = true;
                }
            }
        }

        // sku主图
        if (CollectionUtils.isNotEmpty(saveSpuVO.getSkuVOList())) {
            for (SkuVO skuVO : saveSpuVO.getSkuVOList()) {
                if (StringUtils.isNotBlank(skuVO.getMainImg()) && skuVO.getMainImg().contains(imageUrlPrefix)) {
                    skuVO.setMainImg(fileManageService.downloadAndUploadS3(skuVO.getMainImg(), FileTypeEnum.SKU_IMAGE));
                    updateFlag = true;
                }

                // SKU跨境资质文件路径替换
                if (CollectionUtils.isNotEmpty(skuVO.getSkuCertificateVOList())) {
                    for (GroupSkuCertificateVO groupSkuCertificateVO : skuVO.getSkuCertificateVOList()) {
                        if (CollectionUtils.isEmpty(groupSkuCertificateVO.getSkuCertificateVOS())) {
                            continue;
                        }

                        for (SkuCertificateVO skuCertificateVO : groupSkuCertificateVO.getSkuCertificateVOS()) {
                            if (StringUtils.isNotBlank(skuCertificateVO.getCertificatePath()) && (skuCertificateVO.getCertificatePath().contains(storeFileUrlPreFix) || skuCertificateVO.getCertificatePath().contains(imageUrlPrefix) )) {
                                String path = fileManageService.downloadAndUploadS3(skuCertificateVO.getCertificatePath(), FileTypeEnum.FILE);
                                if (StringUtils.isNotBlank(path)) {
                                    skuCertificateVO.setCertificatePath(path);
                                }
                                updateFlag = true;
                            }
                        }
                    }
                }
            }
        }

        // 资质文件
        if (CollectionUtils.isNotEmpty(saveSpuVO.getSpuCertificateVOList())) {
            for (SpuCertificateVO spuCertificateVO : saveSpuVO.getSpuCertificateVOList()) {
                if (StringUtils.isNotBlank(spuCertificateVO.getCertificatePath()) && (spuCertificateVO.getCertificatePath().contains(storeFileUrlPreFix) || spuCertificateVO.getCertificatePath().contains(imageUrlPrefix))) {
                    String path = fileManageService.downloadAndUploadS3(spuCertificateVO.getCertificatePath(), FileTypeEnum.FILE);
                    if (StringUtils.isNotBlank(path)) {
                        spuCertificateVO.setCertificatePath(path);
                    }
                    updateFlag = true;
                }
            }
        }
        if (updateFlag){
            spuDraftManageService.saveOrUpdateDraft(saveSpuVO);
        }
    }


    /**
     * 填充SKU销售价格
     *
     * @param skuVOList SKU对象列表
     */
    private void fillSkuSalePrice(List<SkuVO> skuVOList) {
        if (CollectionUtils.isNotEmpty(skuVOList)) {
            Set<Long> skuIds = skuVOList.stream().map(SkuVO::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(skuIds)) {
                return;
            }
            Map<Long, BigDecimal> skuSalePriceMap = skuPriceAtomicService.batchQuerySkuSalePrice(skuIds);
            for (SkuVO skuVO : skuVOList) {
                if (StringUtils.isBlank(skuVO.getSalePrice())) {
                    skuVO.setSalePrice(skuSalePriceMap.containsKey(skuVO.getSkuId()) ? skuSalePriceMap.get(skuVO.getSkuId()).toPlainString() : "");
                }
            }
        }
    }
}
