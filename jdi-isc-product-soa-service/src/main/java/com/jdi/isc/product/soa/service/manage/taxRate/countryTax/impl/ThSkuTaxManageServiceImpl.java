package com.jdi.isc.product.soa.service.manage.taxRate.countryTax.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.ThSkuTaxPO;
import com.jdi.isc.product.soa.price.api.enums.PriceTypeEnum;
import com.jdi.isc.product.soa.price.api.price.req.ThSkuTaxVO;
import com.jdi.isc.product.soa.service.adapter.mapstruct.countryTax.ThSkuTaxConvert;
import com.jdi.isc.product.soa.service.atomic.taxRate.countryTax.ThSkuTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.ThSkuTaxManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 泰国税率管理服务
 * <AUTHOR>
 * @date 2025/3/10
 */
@Slf4j
@Service
public class ThSkuTaxManageServiceImpl extends BaseTaxManageService implements ThSkuTaxManageService {

    @Resource
    private ThSkuTaxAtomicService thSkuTaxAtomicService;

    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    public DataResponse<Boolean> saveOrUpdate(ThSkuTaxVO input) {
        boolean flag;
        //将关税除以100保存
        input.setValueAddedTax(input.getValueAddedTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setConsumptionTax(input.getConsumptionTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setFormeTax(input.getFormeTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setMfnTax(input.getMfnTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setAntiDumpingTax(input.getAntiDumpingTax().divide(factor,5, RoundingMode.HALF_UP));
        input.setLocalTax(input.getLocalTax().divide(factor,5, RoundingMode.HALF_UP));

        JdProductDTO skuPO = getSku(input.getJdSkuId());
        if(skuPO==null){
            return DataResponse.error(String.format("零售skuId:%s 不存在,请检查",input.getJdSkuId()));
        }
        ThSkuTaxPO res = thSkuTaxAtomicService.getOne(input);
        if(res==null){
            ThSkuTaxPO target = ThSkuTaxConvert.INSTANCE.vo2Po(input);
            target.setCreateTime(new Date());
            target.setUpdateTime(target.getCreateTime());
            flag = thSkuTaxAtomicService.save(target);
            recordLog(target.getJdSkuId(), PriceTypeEnum.TH_TAX, target.getValueAddedTax(),target,flag);
        }else {
            input.setId(res.getId());
            flag = thSkuTaxAtomicService.updateTax(input);
            recordLog(input.getJdSkuId(), PriceTypeEnum.TH_TAX, input.getValueAddedTax(),input,flag);
        }
        return DataResponse.success(flag);
    }

}
