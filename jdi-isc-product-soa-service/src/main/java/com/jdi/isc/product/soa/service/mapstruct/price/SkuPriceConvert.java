package com.jdi.isc.product.soa.service.mapstruct.price;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.price.supplierPrice.req.*;
import com.jdi.isc.product.soa.api.price.supplierPrice.res.SkuPriceApiDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.res.SkuPriceAuditPageApiDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.res.SkuPricePageApiDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.res.SkuPurchasePriceApiDTO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPurchasePriceAuditVO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.price.supplierPrice.biz.*;
import com.jdi.isc.product.soa.domain.price.supplierPrice.draft.SkuPriceDraftVO;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * sku价格对象转换
 * <AUTHOR>
 * @date 20231130
 **/
@Mapper
public interface SkuPriceConvert {

    SkuPriceConvert INSTANCE = Mappers.getMapper(SkuPriceConvert.class);

    @InheritConfiguration
    SkuPricePO vo2po(SkuPriceVO skuPriceVO);

    @InheritConfiguration
    SkuPriceVO po2Vo(SkuPricePO skuPricePO);

    @InheritConfiguration
    List<SkuPricePO> listVo2Po(List<SkuPriceVO> skuVoList);

    SkuPricePageReqVO reqDto2reqVo(SkuPricePageReqDTO dto);

    PageInfo<SkuPricePageApiDTO> pageVo2PageDto(PageInfo<SkuPricePageVO> vo);


    SkuPriceDraftVO reqDto2Vo(SkuPricePageReqDTO dto);

    SkuPriceDraftVO draftDto2Vo(SkuPriceDraftSaveDTO dto);

    @InheritConfiguration
    SkuPriceApiDTO vo2Dto(SkuPriceVO skuPriceVO);

    @InheritConfiguration
    SkuPurchasePriceAuditVO auditDto2Vo(SkuPriceAuditDTO input);

    SkuPriceAuditPageReqVO reqDto2reqVo(SkuPriceAuditPageReqDTO dto);

    PageInfo<SkuPriceAuditPageApiDTO> auditPageVo2PageDto(PageInfo<SkuPriceAuditPageVO> pageInfo);


    SkuPriceQueryReqVO reqDto2Vo(SkuPriceQueryReqDTO dto);

    SkuPurchasePriceReqVO reqDto2PriceReqVo(SkuPurchasePriceReqDTO reqDto);

    List<SkuPurchasePriceApiDTO> priceReqVo2ReqDto(List<SkuPurchasePriceVO> list);


    List<SkuPriceAuditPageApiDTO> convert(List<SkuPriceAuditPageVO> list);

    @Mapping(source = "item.customColumns", target = "customColumns")
    SkuPriceAuditPageApiDTO convert(SkuPriceAuditPageVO item);

    default PageInfo<SkuPriceAuditPageApiDTO> convert(PageInfo<SkuPriceAuditPageVO> pageInfo) {

        PageInfo<SkuPriceAuditPageApiDTO> result = this.auditPageVo2PageDto(pageInfo);

        if (result == null || CollectionUtils.isEmpty(result.getRecords())) {
            return result;
        }

        List<SkuPriceAuditPageApiDTO> records = this.convert(pageInfo.getRecords());

        result.setRecords(records);

        return result;
    }

    /**
     * Date转换为Long时间戳
     * @param value Date对象
     * @return Long时间戳，如果Date为null则返回null
     */
    default Long map(Date value) {
        return value != null ? value.getTime() : null;
    }

    /**
     * Long时间戳转换为Date
     * @param value Long时间戳
     * @return Date对象，如果Long为null则返回null
     */
    default Date map(Long value) {
        return value != null ? new Date(value) : null;
    }
}
