package com.jdi.isc.product.soa.service.manage.warehouse.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.aggregate.read.api.sku.resp.SkuInfoReadResp;
import com.jdi.isc.order.center.api.biz.req.WarehouseSkuUnBindReq;
import com.jdi.isc.order.center.api.biz.resp.WarehouseSkuUnBindResp;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockResDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.common.util.NumberUtil;
import com.jdi.isc.product.soa.domain.enums.SkuScopeEnum;
import com.jdi.isc.product.soa.domain.enums.mku.MkuRelationBindStatusEnum;
import com.jdi.isc.product.soa.domain.enums.warehouse.WarehouseTypeEnum;
import com.jdi.isc.product.soa.domain.enums.warehouse.WarehouseUnBindType;
import com.jdi.isc.product.soa.domain.sku.biz.ExternalVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuExternalReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangVO;
import com.jdi.isc.product.soa.domain.stock.po.StockPO;
import com.jdi.isc.product.soa.domain.warehouse.biz.*;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehousePO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehouseSkuPO;
import com.jdi.isc.product.soa.domain.warehouse.po.WarehouseSkuUnbindLogPO;
import com.jdi.isc.product.soa.price.api.jdPrice.req.IscSkuDomesticPriceReqDTO;
import com.jdi.isc.product.soa.price.api.jdPrice.res.IscEachSkuPriceResDTO;
import com.jdi.isc.product.soa.price.api.jdPrice.res.IscSkuDomesticPriceResDTO;
import com.jdi.isc.product.soa.rpc.order.OrderCompositeReadRpcService;
import com.jdi.isc.product.soa.rpc.sku.SkuReadRpcService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseSkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.warehouse.WarehouseSkuUnbindLogAtomicService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.manage.price.domestic.DomesticSkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuExternalManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuWriteManageService;
import com.jdi.isc.product.soa.service.manage.stock.SkuStockManageService;
import com.jdi.isc.product.soa.service.manage.stock.StockManageService;
import com.jdi.isc.product.soa.service.manage.warehouse.WarehouseSkuManageService;
import com.jdi.isc.product.soa.service.mapstruct.warehouse.WarehouseConvert;
import com.jdi.isc.product.soa.stock.sku.req.StockItemReqDTO;
import com.jdi.isc.product.soa.stock.sku.req.StockReadReqDTO;
import com.jdi.isc.product.soa.stock.sku.res.StockReadResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @Description: 仓信息对应sku映射服务
 * @Author: wangpeng965
 * @Date: 2024/07/18 11:23
 **/

@Slf4j
@Service
public class WarehouseSkuManageServiceImpl extends BaseManageSupportService<WarehouseSkuVO, WarehouseSkuPO> implements
        WarehouseSkuManageService {


    @Resource
    private WarehouseSkuAtomicService warehouseSkuAtomicService;

    @Resource
    private WarehouseAtomicService warehouseAtomicService;

    @Resource
    private StockManageService stockManageService;

    @Resource
    private DomesticSkuPriceManageService domesticSkuPriceManageService;

    @Resource
    private SkuReadRpcService skuReadRpcService;

    @Resource
    private LangManageService langManageService;

    @Resource
    private SkuStockManageService skuStockManageService;

    @Resource
    private SkuExternalManageService skuExternalManageService;

    @Resource
    private OrderCompositeReadRpcService orderCompositeReadRpcService;

    @Resource
    private WarehouseSkuUnbindLogAtomicService warehouseSkuUnbindLogAtomicService;

    @Resource
    private SkuWriteManageService skuWriteManageService;

    @Override
    public PageInfo<WarehouseSkuResVO> querySkusByCondition(WarehouseSkuPageVO reqVO) {
        Long index = Optional.ofNullable(reqVO.getIndex()).orElse(1L);
        Long size = Optional.ofNullable(reqVO.getSize()).orElse(20L);
        reqVO.setIndex(index);
        reqVO.setSize(size);

        PageInfo<WarehouseSkuResVO> pageInfo = new PageInfo<>();
        pageInfo.setIndex(index);
        pageInfo.setSize(size);
        // 仓库ID不存在
        WarehousePO warehouse = warehouseAtomicService.getWarehouseByWhId(reqVO.getWarehouseId());
        if (Objects.isNull(warehouse)) {
            log.info("WarehouseSkuManageServiceImpl.querySkusByCondition  当前启用状态仓库ID={}不存在", reqVO.getWarehouseId());
            return pageInfo;
        }
        // SkuIdOrName 为全数字，如何时sku id，就清空SkuIdOrName
        if (StringUtils.isNotBlank(reqVO.getSkuIdOrName()) && Long.parseLong(reqVO.getSkuIdOrName()) >= Constant.SKU_BEGIN_ID) {
            reqVO.setSkuId(reqVO.getSkuIdOrName());
            reqVO.setSkuIdOrName(null);
        }

        // 查询总数
        long total = warehouseSkuAtomicService.getTotal(reqVO);
        if (total <= 0) {
            return pageInfo;
        }

        List<WarehouseSkuResVO> warehouseSkuResVOList = warehouseSkuAtomicService.querySkusByCondition(reqVO);
        if (CollectionUtils.isNotEmpty(warehouseSkuResVOList)) {
            List<List<WarehouseSkuResVO>> partition = Lists.partition(warehouseSkuResVOList, Constant.PARTITION_SIZE);
            if (CountryConstant.COUNTRY_ZH.equals(reqVO.getSourceCountryCode())) {
                for (List<WarehouseSkuResVO> part : partition) {
                    if (Objects.nonNull(reqVO.getStockType()) && Constant.ONE == reqVO.getStockType()) {
                        this.processPurchaseModelStockList(part, warehouse.getCountryCode());
                    } else {
                        this.processJdSkuStock(part, warehouse.getCountryCode());
                    }
                    this.setJdPrice(part, reqVO.getTradeEntity());
                }
            } else {
                for (List<WarehouseSkuResVO> part : partition) {
                    if (Objects.nonNull(reqVO.getStockType()) && Constant.ONE == reqVO.getStockType()) {
                        this.processPurchaseModelStockList(part, warehouse.getCountryCode());
                    } else {
                        this.processFactoryStockList(part);
                    }
                    this.setLocalPurchasePrice(part, warehouse.getCountryCode());
                }
            }
        }
        pageInfo.setRecords(warehouseSkuResVOList);
        return pageInfo;
    }

    @Override
    public PageInfo<WarehouseSkuResVO> queryWarehouseTopSkusByCondition(WarehouseSkuPageVO reqVO) {
        reqVO.setIndex(Optional.ofNullable(reqVO.getIndex()).orElse(1L));
        reqVO.setSize(Optional.ofNullable(reqVO.getSize()).orElse(20L));

        PageInfo<WarehouseSkuResVO> pageInfo = new PageInfo<>();
        pageInfo.setIndex(reqVO.getIndex());
        pageInfo.setSize(reqVO.getSize());

        //1、参数校验
        //1.1仓库ID不存在
        Stopwatch stopwatch0 = Stopwatch.createStarted();
        WarehousePO warehouse = warehouseAtomicService.getWarehouseByWhId(reqVO.getWarehouseId());
        if (Objects.isNull(warehouse)) {
            log.info("WarehouseSkuManageServiceImpl.querySkusByCondition  当前启用状态仓库ID={}不存在", reqVO.getWarehouseId());
            return pageInfo;
        }
        //1.2 SkuIdOrName 为全数字，如何时sku id，就清空SkuIdOrName
        if (StringUtils.isNotBlank(reqVO.getSkuIdOrName()) && Long.parseLong(reqVO.getSkuIdOrName()) >= Constant.SKU_BEGIN_ID) {
            reqVO.setSkuId(reqVO.getSkuIdOrName());
            reqVO.setSkuIdOrName(null);
        }

        //2、查询仓商品及库存明细
        Stopwatch stopwatch2 = Stopwatch.createStarted();
        List<WarehouseSkuResVO> warehouseSkuResVOList = warehouseSkuAtomicService.querySkusByConditionV1(reqVO);
        log.info("querySkusByCondition resultSize:{}, QueryingSKUs total took: {} ms", warehouseSkuResVOList.size(), stopwatch2.elapsed(TimeUnit.MILLISECONDS));

        //3、结果信息补充
        if (CollectionUtils.isNotEmpty(warehouseSkuResVOList)) {
            // 补充商品扩展信息
            this.fillSkuExtendInfo(warehouseSkuResVOList);

            // 补充商品库存、价格等信息
            this.fillSkuStockAndPriceInfo(reqVO, warehouseSkuResVOList, warehouse);
        }

        pageInfo.setRecords(warehouseSkuResVOList);
        log.info("querySkusByCondition request:{}, resultSize:{}, total took: {} ms", JSONObject.toJSONString(reqVO), warehouseSkuResVOList.size(), stopwatch0.elapsed(TimeUnit.MILLISECONDS));
        return pageInfo;
    }

    /**
     * 根据商品来源国家分组处理SKU库存和价格信息，分为中国和非中国两部分分别处理
     *
     * @param reqVO                 仓库SKU分页查询请求参数，包含库存类型和交易实体等信息
     * @param warehouseSkuResVOList 需要处理的仓库SKU响应列表
     * @param warehouse             仓库信息对象，包含国家代码等基础信息
     */
    private void fillSkuStockAndPriceInfo(WarehouseSkuPageVO reqVO, List<WarehouseSkuResVO> warehouseSkuResVOList, WarehousePO warehouse) {
        // 将列表分为 CN 和非 CN 两组
        Map<Boolean, List<WarehouseSkuResVO>> groupedSkus = warehouseSkuResVOList.stream()
                .collect(Collectors.partitioningBy(sku -> CountryConstant.COUNTRY_ZH.equals(sku.getSourceCountryCode())));

        // 处理 CN 的部分
        List<WarehouseSkuResVO> cnSkus = groupedSkus.get(true);
        if (CollectionUtils.isNotEmpty(cnSkus)) {
            Stopwatch stopwatch3 = Stopwatch.createStarted();
            List<List<WarehouseSkuResVO>> cnPartitions = Lists.partition(cnSkus, Constant.PARTITION_SIZE);
            for (List<WarehouseSkuResVO> part : cnPartitions) {
                if (Objects.nonNull(reqVO.getStockType()) && Constant.ONE == reqVO.getStockType()) {
                    this.processPurchaseModelStockList(part, warehouse.getCountryCode());
                } else {
                    this.processJdSkuStock(part, warehouse.getCountryCode());
                }
                this.setJdPrice(part, reqVO.getTradeEntity());
            }

            log.info("querySkusByCondition Processing CN SKUs part took: {} ms", stopwatch3.elapsed(TimeUnit.MILLISECONDS));
        }

        // 处理非 CN 的部分
        List<WarehouseSkuResVO> nonCnSkus = groupedSkus.get(false);
        if (CollectionUtils.isNotEmpty(nonCnSkus)) {
            Stopwatch stopwatch4 = Stopwatch.createStarted();
            List<List<WarehouseSkuResVO>> nonCnPartitions = Lists.partition(nonCnSkus, Constant.PARTITION_SIZE);
            for (List<WarehouseSkuResVO> part : nonCnPartitions) {
                if (Objects.nonNull(reqVO.getStockType()) && Constant.ONE == reqVO.getStockType()) {
                    this.processPurchaseModelStockList(part, warehouse.getCountryCode());
                } else {
                    this.processFactoryStockList(part);
                }
                this.setLocalPurchasePrice(part, warehouse.getCountryCode());
            }

            log.info("querySkusByCondition Processing non-CN SKUs part took: {} ms", stopwatch4.elapsed(TimeUnit.MILLISECONDS));
        }
    }

    /**
     * 填充SKU扩展信息到仓库SKU响应VO列表
     *
     * @param warehouseSkuResVOList 仓库SKU响应VO列表，需要填充扩展信息的SKU集合
     */
    private void fillSkuExtendInfo(List<WarehouseSkuResVO> warehouseSkuResVOList) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            List<Long> skuIds = warehouseSkuResVOList.stream().map(WarehouseSkuResVO::getSkuId).collect(Collectors.toList());
            SkuExternalReqVO input = new SkuExternalReqVO(skuIds, Sets.newHashSet(SkuScopeEnum.BASE, SkuScopeEnum.LANG));
            Map<Long, ExternalVO> externalVoMap = skuExternalManageService.querySkuInfo(input);
            log.info("SkuStockManageServiceImpl.page.querySkuInfo req:{} , res:{}", JSONObject.toJSONString(input), JSONObject.toJSONString(externalVoMap));
            for (WarehouseSkuResVO warehouseSkuRes : warehouseSkuResVOList) {
                ExternalVO po = externalVoMap.get(warehouseSkuRes.getSkuId());
                //l.spu_title skuName,s.main_img skuImg,s.jd_sku_id jdSkuId,s.source_country_code sourceCountryCode,s.vendor_code vendorCode, s.jd_vendor_code jdVendorCode
                if (po != null && po.getSkuVO() != null) {
                    //商品名称
                    SpuLangVO lang = Optional.ofNullable(po.getLangVOList()).orElseGet(ArrayList::new).stream().filter(line -> LangConstant.LANG_ZH.equals(line.getLang())).findFirst().orElse(null);
                    warehouseSkuRes.setSkuName(lang != null ? lang.getSpuTitle() : null);

                    //商品京东skuID、商品主图、货源国、供应商编码等设置
                    SkuVO skuVO = po.getSkuVO();
                    warehouseSkuRes.setSkuImg(skuVO.getMainImg());
                    warehouseSkuRes.setJdSkuId(skuVO.getJdSkuId());
                    warehouseSkuRes.setSourceCountryCode(skuVO.getSourceCountryCode());
                    warehouseSkuRes.setVendorCode(skuVO.getVendorCode());
                    warehouseSkuRes.setJdVendorCode(skuVO.getJdVendorCode());
                }
            }
        } finally {
            log.info("querySkusByCondition fillSkuExtendInfo executed in {} ms", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        }
    }

    /**
     * 根据仓库商品列表更新京东商品的库存信息。
     *
     * @param warehouseSkuResVOList 仓库商品列表。
     */
    private void processJdSkuStock(List<WarehouseSkuResVO> warehouseSkuResVOList, String countryCode) {
        StockReadReqDTO readReqDTO = new StockReadReqDTO();
        readReqDTO.setCountryCode(countryCode);
        List<StockItemReqDTO> itemList = warehouseSkuResVOList.stream().filter(Objects::nonNull).filter(vo -> Objects.nonNull(vo.getJdSkuId())).map(vo -> new StockItemReqDTO(vo.getSkuId(), 1)).collect(Collectors.toList());
        readReqDTO.setStockItems(itemList);
        DataResponse<Map<Long, StockReadResDTO>> response = skuStockManageService.queryCnStock(readReqDTO);
        if (Objects.nonNull(response) && response.getSuccess() && MapUtils.isNotEmpty(response.getData())) {
            Map<Long, StockReadResDTO> skuStockMap = response.getData();
            warehouseSkuResVOList.forEach(resVo -> {
                StockReadResDTO stockReadResDTO = skuStockMap.get(resVo.getSkuId());
                //remainNum前端判断逻辑：-1 有货，0 无货，否则取原值
                Long jdStock = stockReadResDTO.getRemainNum();
                if (Objects.equals(stockReadResDTO.getStockStateType(), Constant.OUT_OF_STOCK)) {
                    jdStock = 0L;
                }

                resVo.setJdStock(Objects.nonNull(stockReadResDTO) ? jdStock.toString() : Constant.SOLD_OUT);
            });
        } else {
            warehouseSkuResVOList.forEach(resVo -> resVo.setJdStock(Constant.SOLD_OUT));
        }
    }

    /**
     * 处理工厂库存列表，更新每个 SKU 的可用库存。
     *
     * @param warehouseSkuResVOList 工厂库存列表
     */
    private void processFactoryStockList(List<WarehouseSkuResVO> warehouseSkuResVOList) {
        List<StockItemManageReqDTO> stockItemList = warehouseSkuResVOList.stream().filter(Objects::nonNull)
                .map(resVo -> new StockItemManageReqDTO(resVo.getSkuId(), 1L, Constant.SYSTEM, null)).collect(Collectors.toList());
        StockManageReqDTO reqDTO = new StockManageReqDTO();
        reqDTO.setStockItem(stockItemList);
        reqDTO.setBizNo(UUID.randomUUID().toString());
        Map<Long, StockResDTO> stockResDTOMap = stockManageService.getStock(reqDTO);
        warehouseSkuResVOList.forEach((resVo -> {
            StockResDTO stockResDTO = stockResDTOMap.getOrDefault(resVo.getSkuId(), new StockResDTO());
            //在途库存 计算厂直模式 库存-预占。
            resVo.setAvailableStock(Objects.isNull(stockResDTO) || Objects.isNull(stockResDTO.getStock()) ? Constant.ZERO : stockResDTO.getStock() - (stockResDTO.getOccupy() == null ? Constant.ZERO : stockResDTO.getOccupy()));
        }));
    }


    /**
     * 处理备货库存列表，更新每个 SKU 的可用库存。
     *
     * @param warehouseSkuResVOList 备货库存列表
     */
    private void processPurchaseModelStockList(List<WarehouseSkuResVO> warehouseSkuResVOList, String countryCode) {
        List<StockItemManageReqDTO> stockItemList = warehouseSkuResVOList.stream().filter(Objects::nonNull)
                .map(resVo -> new StockItemManageReqDTO(resVo.getSkuId(), 1L, Constant.SYSTEM, null)).collect(Collectors.toList());
        StockManageReqDTO reqDTO = new StockManageReqDTO();
        reqDTO.setStockItem(stockItemList);
        reqDTO.setBizNo(UUID.randomUUID().toString());
        reqDTO.setCountryCode(countryCode);
        Map<Long, StockResDTO> stockResDTOMap = stockManageService.getStock(reqDTO);
        warehouseSkuResVOList.forEach((resVo -> {
            StockResDTO stockResDTO = stockResDTOMap.getOrDefault(resVo.getSkuId(), new StockResDTO());
            //在途库存 计算厂直模式 库存-预占。
            long availableStock = Objects.isNull(stockResDTO) || Objects.isNull(stockResDTO.getStock()) ? Constant.ZERO : stockResDTO.getStock() - (stockResDTO.getOccupy() == null ? Constant.ZERO : stockResDTO.getOccupy());
            resVo.setAvailableStock(availableStock < Constant.ZERO ? Constant.ZERO : availableStock);
        }));
    }


    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 60)
    public WarehouseSkuRelationResVO updateWarehouseSkus(List<WarehouseSkuVO> warehouseSkuVOList) {
        WarehouseSkuRelationResVO resVO = new WarehouseSkuRelationResVO();
        List<WarehouseSkuPO> warehouseSkuPOList = WarehouseConvert.INSTANCE.listWarehouseSkuVo2Po(warehouseSkuVOList);

        //1、获取备货仓信息
        List<WarehousePO> warehousePOList = warehouseAtomicService.listWarehouseByType(WarehouseTypeEnum.STOCKING);
        Map<Long, WarehousePO> warehouseMap = warehousePOList.stream()
                .collect(Collectors.toMap(WarehousePO::getId, warehousePO -> warehousePO));

        //2、校验sku和仓的绑定关系，记录不合法的绑定请求
        List<WarehouseSkuBindResult> warehouseSkuBindResults = validateWarehouseSkuBindRelation(warehouseSkuPOList, warehouseMap);

        //3、剔除非法的sku和仓的绑定列表
        List<WarehouseSkuPO> validWarehouseSkuPOList = warehouseSkuPOList;
        if (CollectionUtils.isNotEmpty(warehouseSkuBindResults)) {
            Map<String, Boolean> warehouseSkuBindResultMap = warehouseSkuBindResults.stream()
                    .collect(Collectors.toMap(bindResult -> fetchSkuWarehouseRelationKey(bindResult.getSkuId(), bindResult.getWarehouseId()),
                            WarehouseSkuBindResult::getBindResult));

            validWarehouseSkuPOList = warehouseSkuPOList.stream()
                    .filter(getWarehouseSkuPOPredicate(warehouseSkuBindResults, warehouseSkuBindResultMap)).collect(Collectors.toList());
        }

        //4、绑定关系批量更新
        if (CollectionUtils.isNotEmpty(validWarehouseSkuPOList)) {
            boolean saved = warehouseSkuAtomicService.saveOrUpdateBatch(validWarehouseSkuPOList);
            Set<String> resultSet = validWarehouseSkuPOList.stream().map(vo -> vo.getWarehouseId() + String.valueOf(vo.getSkuId())).collect(Collectors.toSet());
            if (saved) {
                resVO.setSuccessSet(resultSet);
            } else {
                //记录失败key
                resVO.setFailedSet(resultSet);

                //记录更新失败的绑定关系列表
                recordFailedSkuWarehouseRelationDetails(validWarehouseSkuPOList, warehouseMap, warehouseSkuBindResults);
            }
        }

        //5、设置sku和仓的绑定结果明细(目前仅失败的才设置)
        resVO.setWarehouseSkuBindResultList(warehouseSkuBindResults);
        return resVO;
    }

    /**
     * 记录商品与仓库绑定失败的详细信息
     *
     * @param validWarehouseSkuPOList 有效的商品仓库关系列表
     * @param warehouseMap            仓库ID与仓库对象的映射关系
     * @param warehouseSkuBindResults 用于存储绑定失败结果的集合
     */
    private void recordFailedSkuWarehouseRelationDetails(List<WarehouseSkuPO> validWarehouseSkuPOList, Map<Long, WarehousePO> warehouseMap, List<WarehouseSkuBindResult> warehouseSkuBindResults) {
        for (WarehouseSkuPO warehouseSkuPO : validWarehouseSkuPOList) {
            WarehousePO warehousePO = warehouseMap.get(warehouseSkuPO.getWarehouseId());
            WarehouseSkuBindResult bindResult = new WarehouseSkuBindResult(warehouseSkuPO.getSkuId(), warehouseSkuPO.getWarehouseId());

            bindResult.failed(String.format("商品 %s 和仓库 %s 绑定失败!", warehouseSkuPO.getSkuId(), warehousePO.getWarehouseNo()));
            warehouseSkuBindResults.add(bindResult);
        }
    }

    /**
     * 根据仓库SKU绑定结果生成判断仓库SKU是否有效的断言
     *
     * @param warehouseSkuBindResults   仓库SKU绑定结果列表
     * @param warehouseSkuBindResultMap 仓库SKU绑定结果映射表(key为SKU与仓库关系键,value为是否绑定)
     * @return 返回判断仓库SKU是否有效的断言对象
     */
    private @NotNull Predicate<WarehouseSkuPO> getWarehouseSkuPOPredicate(List<WarehouseSkuBindResult> warehouseSkuBindResults,
                                                                          Map<String, Boolean> warehouseSkuBindResultMap) {
        return inputWarehouseSkuPO -> {
            if (CollectionUtils.isEmpty(warehouseSkuBindResults)) {
                return true;
            }

            Long skuId = inputWarehouseSkuPO.getSkuId();
            Long warehouseId = inputWarehouseSkuPO.getWarehouseId();
            String skuWarehouseKey = fetchSkuWarehouseRelationKey(skuId, warehouseId);
            if (warehouseSkuBindResultMap.containsKey(skuWarehouseKey)) {
                return warehouseSkuBindResultMap.get(skuWarehouseKey);
            }

            return true;
        };
    }

    /**
     * 验证仓库与SKU绑定关系的合法性
     *
     * @param warehouseSkuPOList 待验证的仓库SKU绑定关系列表
     * @param warehouseMap       仓库ID与仓库PO的映射关系
     * @return 包含验证结果的绑定结果列表，若无绑定关系则返回空列表
     */
    private List<WarehouseSkuBindResult> validateWarehouseSkuBindRelation(List<WarehouseSkuPO> warehouseSkuPOList, Map<Long, WarehousePO> warehouseMap) {
        Set<String> skuIds = warehouseSkuPOList.stream()
                .map(WarehouseSkuPO::getSkuId)
                .map(String::valueOf).collect(Collectors.toSet());
        QuerySkuRelationReqVO skuRelationReqVO = new QuerySkuRelationReqVO(skuIds);
        //获取sku和仓已有的绑定关系
        Map<String, Map<String, WarehouseSkuVO>> skuWarehouseRelationMap = querySkuWarehouseRelationMap(skuRelationReqVO);
        log.info("WarehouseSkuManageServiceImpl.validateWarehouseSkuBindRelation  skuWarehouseRelationMap:{}", JSONObject.toJSONString(skuWarehouseRelationMap));

        List<WarehouseSkuBindResult> warehouseSkuBindResults = Lists.newArrayList();
        if (org.springframework.util.CollectionUtils.isEmpty(skuWarehouseRelationMap)) {
            log.info("WarehouseSkuManageServiceImpl.validateWarehouseSkuBindRelation  商品不存在绑定关系，不进行校验！skuIds:{}", skuIds);
            //还未有仓绑定关系，不进行校验
            return warehouseSkuBindResults;
        }

        //新增绑定数据合法性校验
        for (WarehouseSkuPO inputWarehouseSkuPO : warehouseSkuPOList) {
            WarehouseSkuBindResult bindResult = new WarehouseSkuBindResult(inputWarehouseSkuPO.getWarehouseId(), inputWarehouseSkuPO.getSkuId());
            warehouseSkuBindResults.add(bindResult);

            String skuId = String.valueOf(inputWarehouseSkuPO.getSkuId());
            WarehousePO warehousePO = warehouseMap.get(inputWarehouseSkuPO.getWarehouseId());

            //1、仓重复绑定校验
            if (skuWarehouseRelationMap.containsKey(skuId) &&
                    skuWarehouseRelationMap.get(skuId).containsKey(String.valueOf(warehousePO.getId()))) {
                log.info(String.format("WarehouseSkuManageServiceImpl.validateWarehouseSkuBindRelation 商品 %s 已绑定仓库 %s ，请勿重复绑定!", inputWarehouseSkuPO.getSkuId(), warehousePO.getWarehouseNo()));

                bindResult.failed(String.format("商品 %s 已绑定仓库 %s ，请勿重复绑定!", inputWarehouseSkuPO.getSkuId(), warehousePO.getWarehouseNo()));
                continue;
            }

            //2、一个SKU下一个国家内只能关联一个备货仓校验
            if (skuWarehouseRelationMap.containsKey(skuId)) {
                Map<String, WarehouseSkuVO> skuRelationWarehouseMap = skuWarehouseRelationMap.get(skuId);

                List<WarehousePO> relatedWarehouses = skuRelationWarehouseMap.keySet()
                        .stream().map(Long::valueOf)
                        .map(warehouseMap::get) // 获取对应的 WarehousePO
                        .filter(Objects::nonNull).collect(Collectors.toList());

                log.info("WarehouseSkuManageServiceImpl.validateWarehouseSkuBindRelation  skuRelationWarehouseMap:{}, relatedWarehouses:{},warehousePO:{}",
                        JSONObject.toJSONString(skuWarehouseRelationMap), JSONObject.toJSONString(skuRelationWarehouseMap), JSONObject.toJSONString(warehousePO));

                if (relatedWarehouses.stream().anyMatch(relatedWarehouse ->
                        relatedWarehouse.getCountryCode().equals(warehousePO.getCountryCode()))) {
                    bindResult.failed(String.format("商品 %s 在国家 %s 已经关联了一个备货仓 %s ，一个SKU下一个国家内只能关联一个备货仓!", inputWarehouseSkuPO.getSkuId(),
                            warehousePO.getCountryCode(), warehousePO.getWarehouseNo()));

                    log.info(String.format("WarehouseSkuManageServiceImpl.validateWarehouseSkuBindRelation 商品 %s 在国家 %s 已经关联了一个备货仓 %s ，一个SKU下一个国家内只能关联一个备货仓!",
                            inputWarehouseSkuPO.getSkuId(), warehousePO.getCountryCode(), warehousePO.getWarehouseNo()));
                }
            }
        }
        return warehouseSkuBindResults;
    }

    /**
     * 根据商品ID和仓库ID生成关联键
     *
     * @param skuId       商品ID
     * @param warehouseId 仓库ID
     * @return 返回由商品ID和仓库ID拼接而成的关联键字符串，格式为"skuId_warehouseId"
     */
    private String fetchSkuWarehouseRelationKey(Long skuId, Long warehouseId) {
        return skuId + "_" + warehouseId;
    }

    @Override
    public Map<String, Map<String, WarehouseSkuVO>> querySkuWarehouseRelationMap(QuerySkuRelationReqVO reqVO) {
        Set<Long> skuIds = reqVO.getSkuIds().stream().map(Long::parseLong).collect(Collectors.toSet());
        Map<Long, List<WarehouseSkuPO>> skuWarehouseListMap = warehouseSkuAtomicService.queryBindSkuMap(skuIds);

        if (MapUtils.isEmpty(skuWarehouseListMap)) {
            return Collections.emptyMap();
        }

        Map<String, Map<String, WarehouseSkuVO>> resultMap = Maps.newHashMap();
        skuWarehouseListMap.forEach((skuId, skuWarehouseList) -> {
            Map<String, WarehouseSkuVO> skuResVOMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(skuWarehouseList)) {
                skuWarehouseList.forEach(warehouseSkuPo -> skuResVOMap.put(String.valueOf(warehouseSkuPo.getWarehouseId()), WarehouseConvert.INSTANCE.warehouseSkuPo2VO(warehouseSkuPo)));
            }
            resultMap.put(String.valueOf(skuId), skuResVOMap);
        });
        return resultMap;
    }

    @Override
    public List<WarehouseSkuResVO> batchSkusByCondition(WarehouseBatchSkuVo batchSkuVo) {
        WarehousePO warehouse = warehouseAtomicService.getWarehouseByWhId(batchSkuVo.getWarehouseId());
        if (Objects.isNull(warehouse)) {
            log.error("batchSkusByCondition 无效仓信息, reqWarehouseId:{}", batchSkuVo.getWarehouseId());
            return Collections.emptyList();
        }

        List<WarehouseSkuResVO> warehouseSkuResVOList = warehouseSkuAtomicService.batchSkusByCondition(batchSkuVo);
        //查询库存
        //查询价格
        if (CollectionUtils.isNotEmpty(warehouseSkuResVOList)) {
            if (CountryConstant.COUNTRY_ZH.equals(batchSkuVo.getSourceCountryCode())) {
                this.processJdSkuStock(warehouseSkuResVOList, warehouse.getCountryCode());
                this.setJdPrice(warehouseSkuResVOList, batchSkuVo.getTradeEntity());
            } else {
                this.processFactoryStockList(warehouseSkuResVOList);
                this.setLocalPurchasePrice(warehouseSkuResVOList, warehouse.getCountryCode());

            }
        }
        return warehouseSkuResVOList;
    }

    /**
     * 设置仓库商品的销售价格
     */
    private void setJdPrice(List<WarehouseSkuResVO> warehouseSkuResVOList, String tradeEntity) {
        Assert.notNull(tradeEntity, "贸易主体不能为空");
        Set<Long> skuIds = warehouseSkuResVOList.stream().map(WarehouseSkuResVO::getJdSkuId).collect(Collectors.toSet());
        DataResponse<IscSkuDomesticPriceResDTO> response = domesticSkuPriceManageService.getJdiPrice(new IscSkuDomesticPriceReqDTO(tradeEntity, skuIds));
        if (Objects.isNull(response) || !response.getSuccess() || Objects.isNull(response.getData())) {
            return;
        }
        IscSkuDomesticPriceResDTO priceResDTO = response.getData();

        if (MapUtils.isEmpty(priceResDTO.getSuccessSkuPriceMap())) {
            return;
        }

        Map<Long, IscEachSkuPriceResDTO> resDTOMap = priceResDTO.getSuccessSkuPriceMap();
        if (org.springframework.util.CollectionUtils.isEmpty(resDTOMap)) {
            log.info("setJdPrice resDTOMap is empty! skuIds:{}, tradeEntity:{}", skuIds, tradeEntity);
            resDTOMap = Collections.emptyMap();
        }

        Map<Long, IscEachSkuPriceResDTO> finalResDTOMap = resDTOMap;
        warehouseSkuResVOList.forEach(w -> {
            IscEachSkuPriceResDTO jdiSkuPriceResDTO = finalResDTOMap.get(w.getJdSkuId());
            if (Objects.nonNull(jdiSkuPriceResDTO)) {
                w.setPurchasePrice(jdiSkuPriceResDTO.getJdSkuPurchasePrice());
                w.setPurchaseTaxPrice(w.getPurchasePrice());
            }
        });
    }

    /**
     * 设置本地采购价格和销售价格。
     */
    private void setLocalPurchasePrice(List<WarehouseSkuResVO> warehouseSkuResVOList, String sourceCountryCode) {
        List<Long> skuIds = warehouseSkuResVOList.stream().map(WarehouseSkuResVO::getSkuId).collect(Collectors.toList());

        Map<Long, SkuInfoReadResp> readRespMap = skuReadRpcService.querySkuInfoList(new HashSet<>(skuIds), sourceCountryCode, Collections.singletonList(LangConstant.LANG_ZH), true);
        if (Objects.isNull(readRespMap)) {
            return;
        }
        warehouseSkuResVOList.forEach(w -> {
            SkuInfoReadResp skuInfoReadResp = readRespMap.get(w.getSkuId());
            if (Objects.nonNull(skuInfoReadResp) && Objects.nonNull(skuInfoReadResp.getSkuPriceReadResp())) {
                w.setPurchasePrice(skuInfoReadResp.getSkuPriceReadResp().getPurchaseOrderPrice());
                w.setPurchaseTaxPrice(skuInfoReadResp.getSkuPriceReadResp().getPurchaseOrderPrice());
                //进项目税
                if (skuInfoReadResp.getSkuPriceReadResp().getPurchaseOrderValueAddedTax() != null) {
                    w.setPurchaseTaxPrice(w.getPurchaseTaxPrice().add(skuInfoReadResp.getSkuPriceReadResp().getPurchaseOrderValueAddedTax()));
                }
            }
        });
    }

    @Override
    public Boolean updateOnWaySale(WarehouseSkuVO skuVO) {
        if (Objects.isNull(skuVO.getSkuId()) || Objects.isNull(skuVO.getWarehouseId()) || Objects.isNull(skuVO.getOnWaySale())) {
            log.error("updateOnWaySale : 参数获取异常:{}", JSONObject.toJSONString(skuVO));
            throw new BizException("请求参数异常");
        }
        try {
            log.warn("updateOnWaySale  数据 参数:{}", JSONObject.toJSONString(skuVO));
            UpdateWrapper<WarehouseSkuPO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda()
                    .eq(WarehouseSkuPO::getWarehouseId, skuVO.getWarehouseId())
                    .eq(WarehouseSkuPO::getSkuId, skuVO.getSkuId())
                    .eq(WarehouseSkuPO::getBindStatus, MkuRelationBindStatusEnum.BIND.getCode())
                    .eq(WarehouseSkuPO::getYn, YnEnum.YES.getCode());
            WarehouseSkuPO updatePO = new WarehouseSkuPO();
            updatePO.setOnWaySale(skuVO.getOnWaySale());
            updatePO.setUpdater(skuVO.getUpdater());
            updatePO.setUpdateTime(Instant.now().toEpochMilli());
            return warehouseSkuAtomicService.update(updatePO, updateWrapper);
        } catch (Exception e) {
            log.error("操作数据库异常: 参数: {}, 异常信息: {}", JSONObject.toJSONString(skuVO), e.getMessage(), e);
            throw new BizException("存储数据异常");
        }
    }

    /**
     * 解绑条件：
     * 库存表无库存或者库存记录不存在
     * <p>
     * 备货采购单：
     * 不存在没完成或者没取消的采购单
     * <p>
     * 仓发订单
     * 不存在没完成或者没取消的订单
     * <p>
     * 出入库单：
     * 不存在没完成或者没取消的出入库单
     * <p>
     * 预报备货单
     * ——同采购单
     */
    @Override
    public List<WarehouseSkuUnBindResVO> queryWarehouseSkuUnbindRelation(Set<WarehouseSkuUnBindVO> warehouseSkuUnBindVOSet) {
        List<WarehouseSkuUnBindResVO> warehouseSkuUnBindResVOList = Lists.newArrayList();

        //必填参数校验
        boolean hasInvalidEntry = warehouseSkuUnBindVOSet.stream()
                .anyMatch(vo -> Objects.isNull(vo.getSkuId()) || Objects.isNull(vo.getUnBindType()));
        if (hasInvalidEntry) {
            throw new IllegalArgumentException("参数校验失败：国际skuId和unBindType为必填项!");
        }

        // 根据 warehouseId 是否为空将请求参数分为两组
        Map<Boolean, Set<WarehouseSkuUnBindVO>> partitionedRequests = warehouseSkuUnBindVOSet.stream()
                .collect(Collectors.partitioningBy(
                        req -> Objects.isNull(req.getWarehouseId()),
                        Collectors.toSet() // 使用 Collectors.toSet() 确保分组结果是 Set
                ));

        //查询备货仓信息
        Set<Long> warehouseIds = warehouseSkuUnBindVOSet.stream().map(WarehouseSkuUnBindVO::getWarehouseId).collect(Collectors.toSet());
        Map<Long, String> warehouseNoMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(warehouseIds)) {
            List<WarehousePO> warehousePOList = warehouseAtomicService.listByIds(warehouseIds);
            warehouseNoMap = warehousePOList.stream()
                    .collect(Collectors.toMap(
                            WarehousePO::getId, WarehousePO::getWarehouseNo, (name1, name2) -> name1));
        }

        // 分别处理两组请求
        //1、处理备货品零库存判断
        Set<WarehouseSkuUnBindVO> warehouseStockSkuRequest = partitionedRequests.get(false);
        if (CollectionUtils.isNotEmpty(warehouseStockSkuRequest)) {
            List<WarehouseSkuUnBindResVO> warehouseStockSkuValidateResult = processWarehouseStockValidates(warehouseStockSkuRequest, warehouseNoMap);
            warehouseSkuUnBindResVOList.addAll(warehouseStockSkuValidateResult);
        }

        //2、处理厂直品零库存判断
        Set<WarehouseSkuUnBindVO> factoryStockSkuRequest = partitionedRequests.get(true);
        if (CollectionUtils.isNotEmpty(factoryStockSkuRequest)) {
            List<WarehouseSkuUnBindResVO> factoryStockSkuValidateResult = processFactoryStockValidate(factoryStockSkuRequest);
            warehouseSkuUnBindResVOList.addAll(factoryStockSkuValidateResult);
        }


        //3、过滤掉已经判断过的sku，筛选出零库存商品
        Set<WarehouseSkuUnBindVO> zeroStockWarehouseSkuUnBindReq = warehouseSkuUnBindVOSet.stream()
                .filter(item -> !item.getStockValidFlag())
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(zeroStockWarehouseSkuUnBindReq)) {
            //未查到库存或库存不为零
            return warehouseSkuUnBindResVOList;
        }

        //4、校验是否有绑定单据
        Set<WarehouseSkuUnBindReq> warehouseSkuUnBindReqSet = WarehouseConvert.INSTANCE.setWarehouseSkuUnbindVO2DTO(zeroStockWarehouseSkuUnBindReq);
        List<WarehouseSkuUnBindResp> warehouseSkuUnBindReultList = orderCompositeReadRpcService.queryBillRelationForUnbind(warehouseSkuUnBindReqSet);
        if (CollectionUtils.isEmpty(warehouseSkuUnBindReultList)) {
            setFailedWarehouseSkuUnBindResult(warehouseSkuUnBindVOSet, warehouseSkuUnBindResVOList, warehouseNoMap);
            return warehouseSkuUnBindResVOList;
        }

        //5、合并处理结果
        List<WarehouseSkuUnBindResVO> warehouseSkuUnBindResVOS = WarehouseConvert.INSTANCE.listWarehouseSkuUnbindDTO2VO(warehouseSkuUnBindReultList);
        fillWarehouseExtInfo(warehouseSkuUnBindResVOS, warehouseNoMap);
        warehouseSkuUnBindResVOList.addAll(warehouseSkuUnBindResVOS);

        return warehouseSkuUnBindResVOList;
    }

    /**
     * 为仓库SKU解绑响应VO列表填充仓库编号信息
     *
     * @param warehouseSkuUnBindResVOS 仓库SKU解绑响应VO列表，需要填充仓库编号的对象集合
     * @param warehouseNoMap           仓库ID与仓库编号的映射关系表，key为仓库ID，value为仓库编号
     */
    private void fillWarehouseExtInfo(List<WarehouseSkuUnBindResVO> warehouseSkuUnBindResVOS, Map<Long, String> warehouseNoMap) {
        for (WarehouseSkuUnBindResVO warehouseSkuUnBindResVO : warehouseSkuUnBindResVOS) {
            Long warehouseId = warehouseSkuUnBindResVO.getWarehouseId();
            if (warehouseId != null && warehouseNoMap.containsKey(warehouseId)) {
                warehouseSkuUnBindResVO.setWarehouseNo(warehouseNoMap.get(warehouseId));
            }
        }
    }

    /**
     * 处理仓库SKU解绑请求的库存校验逻辑
     * @param requests 需要解绑的仓库SKU集合，包含SKU ID和仓库ID信息
     * @return 返回校验结果列表，包含每个SKU的校验状态和失败原因（如有）
     */
    private List<WarehouseSkuUnBindResVO> processWarehouseStockValidates(Set<WarehouseSkuUnBindVO> requests,Map<Long, String> warehouseNoMap) {

        List<WarehouseSkuUnBindResVO> warehouseSkuUnBindResVOList = Lists.newArrayList();

        //备货仓库存查询
        List<StockItemManageReqDTO> stockItemManageReqDTOS = getStockItemManageReqDTOS(requests);
        List<StockPO> stockPOList = stockManageService.batchGetWarehouseStock(stockItemManageReqDTOS);
        Map<String, StockPO> stockPOMap = stockPOList.stream()
                .collect(Collectors.toMap(
                        stockPO -> stockPO.getSkuId() + "_" + stockPO.getWarehouseId(), // 键是 skuId 和 warehouseId 的组合
                        Function.identity() // 值是 StockPO 对象本身
                ));

        //校验是否有备货仓库存
        for (WarehouseSkuUnBindVO warehouseSkuUnBindReq : requests) {
            WarehouseSkuUnBindResVO warehouseSkuUnBindRes = new WarehouseSkuUnBindResVO();
            warehouseSkuUnBindRes.setSkuId(warehouseSkuUnBindReq.getSkuId());
            warehouseSkuUnBindRes.setWarehouseId(warehouseSkuUnBindReq.getWarehouseId());
            //设置备货仓编号
            if (warehouseNoMap.containsKey(warehouseSkuUnBindReq.getWarehouseId())) {
                warehouseSkuUnBindRes.setWarehouseNo(warehouseNoMap.get(warehouseSkuUnBindReq.getWarehouseId()));
            }

            //获取库存信息
            StockPO stockPO = stockPOMap.get(warehouseSkuUnBindReq.getSkuId() + "_" + warehouseSkuUnBindReq.getWarehouseId());
            if (stockPO != null) {
                //校验库存数量
                if (NumberUtil.gt0(stockPO.getStock()) || NumberUtil.gt0(stockPO.getOnWayStock()) ||
                        NumberUtil.gt0(stockPO.getOccupy())) {
                    warehouseSkuUnBindRes.setValidResult(false);
                    warehouseSkuUnBindRes.setResultMsg("备货仓库存不为零，不能解绑或归档！");
                    warehouseSkuUnBindReq.setStockValidFlag(true);
                    warehouseSkuUnBindResVOList.add(warehouseSkuUnBindRes);
                } else {
                    warehouseSkuUnBindReq.setStockValidFlag(false);
                }
            } else {
                //未查到库存，可能处于绑仓审批阶段
                warehouseSkuUnBindRes.setValidResult(true);
                warehouseSkuUnBindReq.setStockValidFlag(true);
                warehouseSkuUnBindResVOList.add(warehouseSkuUnBindRes);
            }
        }

        return warehouseSkuUnBindResVOList;
    }


    /**
     * 处理工厂库存验证，检查SKU在供应商仓是否有库存
     * @param requests 包含需要验证的仓库SKU解绑请求集合
     * @return 返回验证结果列表，包含每个SKU的验证状态和消息
     */
    private List<WarehouseSkuUnBindResVO> processFactoryStockValidate(Set<WarehouseSkuUnBindVO> requests) {

        List<WarehouseSkuUnBindResVO> factorySkuUnBindResVOList = Lists.newArrayList();

        //厂直库存查询
        Set<Long> skuIds = requests.stream().map(WarehouseSkuUnBindVO::getSkuId).collect(Collectors.toSet());
        List<StockPO> stockPOList = stockManageService.batchGetFactoryStock(skuIds);
        Map<Long, StockPO> stockPOMap = stockPOList.stream()
                .collect(Collectors.toMap(stockPO -> stockPO.getSkuId(),
                        Function.identity() // 值是 StockPO 对象本身
                ));

        //校验是否有备货仓库存
        for (WarehouseSkuUnBindVO warehouseSkuUnBindReq : requests) {
            WarehouseSkuUnBindResVO warehouseSkuUnBindRes = new WarehouseSkuUnBindResVO();
            warehouseSkuUnBindRes.setSkuId(warehouseSkuUnBindReq.getSkuId());
            warehouseSkuUnBindRes.setWarehouseId(warehouseSkuUnBindReq.getWarehouseId());

            //获取库存信息
            StockPO stockPO = stockPOMap.get(warehouseSkuUnBindReq.getSkuId());
            if (stockPO != null) {
                //校验库存数量
                if (NumberUtil.gt0(stockPO.getStock()) || NumberUtil.gt0(stockPO.getOccupy())) {
                    warehouseSkuUnBindRes.setValidResult(false);
                    warehouseSkuUnBindRes.setResultMsg("供应商库存不为零，不能归档！");
                    warehouseSkuUnBindReq.setStockValidFlag(true);
                    factorySkuUnBindResVOList.add(warehouseSkuUnBindRes);
                } else {
                    warehouseSkuUnBindReq.setStockValidFlag(false);
                }
            } else {
                //未查到库存，可能处于审批阶段
                warehouseSkuUnBindRes.setValidResult(true);
                warehouseSkuUnBindReq.setStockValidFlag(true);
                factorySkuUnBindResVOList.add(warehouseSkuUnBindRes);
            }
        }

        return factorySkuUnBindResVOList;
    }

    /**
     * 设置解绑失败的仓库SKU结果信息
     * @param warehouseSkuUnBindVOSet 待处理的仓库SKU解绑请求集合
     * @param warehouseSkuUnBindResVOList 用于存储解绑失败结果的列表
     */
    private void setFailedWarehouseSkuUnBindResult(Set<WarehouseSkuUnBindVO> warehouseSkuUnBindVOSet, List<WarehouseSkuUnBindResVO> warehouseSkuUnBindResVOList, Map<Long, String> warehouseNoMap) {
        //查询单据关联绑定关系失败，默认返回不能解绑
        for (WarehouseSkuUnBindVO warehouseSkuUnBindReq : warehouseSkuUnBindVOSet) {
            WarehouseSkuUnBindResVO warehouseSkuUnBindRes = new WarehouseSkuUnBindResVO();
            warehouseSkuUnBindRes.setSkuId(warehouseSkuUnBindReq.getSkuId());
            warehouseSkuUnBindRes.setWarehouseId(warehouseSkuUnBindReq.getWarehouseId());
            warehouseSkuUnBindRes.setValidResult(false);
            warehouseSkuUnBindRes.setResultMsg("单据绑定关系查询异常，无法判断解绑！");

            //设置备货仓编号
            if (warehouseSkuUnBindReq.getWarehouseId() != null && warehouseNoMap.containsKey(warehouseSkuUnBindReq.getWarehouseId())) {
                warehouseSkuUnBindRes.setWarehouseNo(warehouseNoMap.get(warehouseSkuUnBindReq.getWarehouseId()));
            }

            warehouseSkuUnBindResVOList.add(warehouseSkuUnBindRes);
        }
    }

    /**
     * 将WarehouseSkuUnBindVO集合转换为StockItemManageReqDTO列表
     * @param warehouseSkuUnBindVOSet 需要转换的仓库SKU解绑数据集合，包含skuId和warehouseId信息
     * @return 转换后的StockItemManageReqDTO列表，包含skuId和对应的warehouseId
     */
    @NotNull
    private  List<StockItemManageReqDTO> getStockItemManageReqDTOS(Set<WarehouseSkuUnBindVO> warehouseSkuUnBindVOSet) {
        List<StockItemManageReqDTO> stockItemManageReqDTOS = warehouseSkuUnBindVOSet.stream().map(item->{
            StockItemManageReqDTO stockItemManageReqDTO = new StockItemManageReqDTO();
            stockItemManageReqDTO.setSkuId(item.getSkuId());
            stockItemManageReqDTO.setWarehouseId(String.valueOf(item.getWarehouseId()));
            return  stockItemManageReqDTO;
        }).collect(Collectors.toList());
        return stockItemManageReqDTOS;
    }

    /**
     * 解绑步骤：
     * 清理库存表记录
     * 清理仓-商品表记录
     * 清理商品草稿表当前仓-sku记录(json串)
     * 记录解绑流水
     * @param warehouseSkuUnBindVOSet 需要解绑的仓库SKU关系集合，包含待解绑的仓库和SKU信息
     * @return
     */
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    @Override
    public Boolean unbindSkuWarehouseBindRelation(Set<WarehouseSkuUnBindVO> warehouseSkuUnBindVOSet, String operator, String approver) {

        log.info("unbindSkuWarehouseBindRelation operator:{}, request:{}", operator, JSON.toJSONString(warehouseSkuUnBindVOSet));

        //必填参数校验
        boolean hasInvalidEntry = warehouseSkuUnBindVOSet.stream()
                .anyMatch(vo -> Objects.isNull(vo.getSkuId()) || !Objects.equals(vo.getUnBindType(), WarehouseUnBindType.UNBIND.getCode()));
        if (hasInvalidEntry) {
            throw new IllegalArgumentException("参数校验失败：SKU和UnBindType必填参数缺失或者UnBindType不为0-解绑！");
        }

        //解绑申请人校验
        if (StringUtils.isBlank(operator) || StringUtils.isBlank(approver)) {
            throw new IllegalArgumentException("参数校验失败：解绑操作人或者审批人不能为空！");
        }

        //备货仓解绑参数合法性校验
        boolean hasInvalidRequest = warehouseSkuUnBindVOSet.stream()
                .anyMatch(req -> Objects.equals(WarehouseUnBindType.UNBIND.getCode(),req.getUnBindType())
                        && Objects.isNull(req.getWarehouseId()));
        if (hasInvalidRequest) {
            throw new IllegalArgumentException("解绑清理验证，备货仓ID不能传值为空!");
        }

        //查询库存、仓-商品等记录Id
        Set<Long> stockPoIds = Sets.newHashSet();
        Set<Long> warehouseSkuPoIds = Sets.newHashSet();
        List<WarehouseSkuUnbindLogPO> warehouseSkuUnbindLogPOList = Lists.newArrayList();
        for (WarehouseSkuUnBindVO warehouseSkuUnBindVO : warehouseSkuUnBindVOSet) {
            Long skuId = warehouseSkuUnBindVO.getSkuId();
            Long warehouseId = warehouseSkuUnBindVO.getWarehouseId();
            StockPO stockPO = stockManageService.getWarehouseStock(skuId, warehouseId);
            if (stockPO != null) {
                stockPoIds.add(stockPO.getId());
            }

            WarehouseSkuPO warehouseSkuPO = warehouseSkuAtomicService.queryWarehouseSku(skuId, warehouseId);
            if (warehouseSkuPO != null) {
                warehouseSkuPoIds.add(warehouseSkuPO.getId());
            }

            WarehouseSkuUnbindLogPO warehouseSkuUnbindLogPO = getWarehouseSkuUnbindLogPO(operator, approver, warehouseSkuUnBindVO,
                    warehouseId, skuId, stockPO, warehouseSkuPO);
            warehouseSkuUnbindLogPOList.add(warehouseSkuUnbindLogPO);


            boolean removeResult = skuWriteManageService.removeNeedUnbindSkuRelation(Sets.newHashSet(skuId), String.valueOf(warehouseId));
            Assert.isTrue(removeResult, "库存绑定关系草稿清理失败！");
        }


        //数据清理
        if (CollectionUtils.isNotEmpty(stockPoIds)) {
            boolean stockUpdate = stockManageService.batchRemoveStockByIds(stockPoIds);
            Assert.isTrue(stockUpdate, "库存清理失败");
        }
        if (CollectionUtils.isNotEmpty(warehouseSkuPoIds)) {
            boolean warehouseSkuUpdate = warehouseSkuAtomicService.removeBatchByIds(warehouseSkuPoIds);
            Assert.isTrue(warehouseSkuUpdate, "仓-商品关系清理失败");
        }
        if (CollectionUtils.isNotEmpty(warehouseSkuUnbindLogPOList)) {
            boolean saveResult = warehouseSkuUnbindLogAtomicService.saveBatch(warehouseSkuUnbindLogPOList);
            Assert.isTrue(saveResult, "解绑记录表保存失败！");
        }

        return Boolean.TRUE;
    }

    /**
     * 创建并返回仓库SKU解绑日志记录对象
     * @param operator 操作人
     * @param approver 审批人
     * @param warehouseSkuUnBindVO 仓库SKU解绑信息值对象
     * @param warehouseId 仓库ID
     * @param skuId SKU ID
     * @param stockPO 库存持久化对象
     * @param warehouseSkuPO 仓库SKU持久化对象
     * @return 填充好属性的仓库SKU解绑日志持久化对象
     */
    @NotNull
    private static WarehouseSkuUnbindLogPO getWarehouseSkuUnbindLogPO(String operator, String approver, WarehouseSkuUnBindVO warehouseSkuUnBindVO, Long warehouseId, Long skuId, StockPO stockPO, WarehouseSkuPO warehouseSkuPO) {
        WarehouseSkuUnbindLogPO warehouseSkuUnbindLogPO = new WarehouseSkuUnbindLogPO();
        warehouseSkuUnbindLogPO.setWarehouseId(warehouseId);
        warehouseSkuUnbindLogPO.setSkuId(skuId);
        warehouseSkuUnbindLogPO.setUnbindType(warehouseSkuUnBindVO.getUnBindType()!=null? warehouseSkuUnBindVO.getUnBindType():0);
        warehouseSkuUnbindLogPO.setStockInfo(JSON.toJSONString(stockPO));
        warehouseSkuUnbindLogPO.setWarehouseSkuInfo(JSON.toJSONString(warehouseSkuPO));
        warehouseSkuUnbindLogPO.setOperator(operator);
        warehouseSkuUnbindLogPO.setApprover(approver);

        warehouseSkuUnbindLogPO.setVersion(1);
        warehouseSkuUnbindLogPO.setRemark("");
        warehouseSkuUnbindLogPO.setCreator("system");
        warehouseSkuUnbindLogPO.setUpdater("system");
        warehouseSkuUnbindLogPO.setCreateTime(System.currentTimeMillis());
        warehouseSkuUnbindLogPO.setUpdateTime(System.currentTimeMillis());
        return warehouseSkuUnbindLogPO;
    }

}
