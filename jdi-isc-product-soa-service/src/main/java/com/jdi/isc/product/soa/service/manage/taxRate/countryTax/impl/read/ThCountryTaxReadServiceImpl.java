package com.jdi.isc.product.soa.service.manage.taxRate.countryTax.impl.read;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.CurrencyConstant;
import com.jdi.isc.product.soa.domain.price.extendPrice.biz.CurrencyPriceVO;
import com.jdi.isc.product.soa.domain.price.po.FulfillmentRatePO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.CrossBorderImportTaxResVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.IscSkuImportTaxReqVO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.MySkuTaxPO;
import com.jdi.isc.product.soa.domain.taxRate.po.countryTax.ThSkuTaxPO;
import com.jdi.isc.product.soa.price.api.price.req.CrossBorderImportTaxReqDTO;
import com.jdi.isc.product.soa.price.api.price.res.CrossBorderImportTaxResDTO;
import com.jdi.isc.product.soa.service.atomic.taxRate.countryTax.ThSkuTaxAtomicService;
import com.jdi.isc.product.soa.service.manage.taxRate.countryTax.BaseTaxReadService;
import com.jdi.isc.product.soa.service.support.TaxCalculateExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 泰国税率读服务
 * <AUTHOR>
 * @date 2025/3/10
 */
@Slf4j
@Service
@TaxCalculateExecutor(countryCode = CountryConstant.COUNTRY_TH)
public class ThCountryTaxReadServiceImpl extends BaseTaxSupportService implements BaseTaxReadService {
    @Resource
    private ThSkuTaxAtomicService thSkuTaxAtomicService;

    /** 查询各国进口关税<只能查跨境品,币种全为rmb>*/
    @Override
    public Map<Long, CrossBorderImportTaxResDTO> queryCrossBorderSkuImportTax(CrossBorderImportTaxReqVO input) {
        Map<Long, CrossBorderImportTaxResDTO> result = Maps.newHashMapWithExpectedSize(input.getCnSkuIds().size());
        //获取跨境采购价
        Map<Long, SkuPricePO> priceMap = skuPriceAtomicService.batchQuerySkuTaxPrice(input.getCnSkuIds(), CountryConstant.COUNTRY_ZH);
        //获取泰国关税率
        Map<Long, ThSkuTaxPO> skuTaxMap = getSkuTaxMap(input.getCnSkuIds());
        for(Long skuId : input.getCnSkuIds()){
            CrossBorderImportTaxResDTO target = new CrossBorderImportTaxResDTO(skuId,input.getCountryCode(),CurrencyConstant.CURRENCY_ZH);
            StringBuilder msg = new StringBuilder();
            if(priceMap.get(skuId)==null){
                msg.append("skuId:").append(skuId).append(" 采购价为空,无法计算关税;");
                target.setDesc(msg.toString());
                result.put(skuId,target);
                continue;
            }
            if(skuTaxMap.get(skuId)==null){
                msg.append("skuId:").append(skuId).append(" 关税配置为空,无法计算关税;");
                target.setDesc(msg.toString());
                result.put(skuId,target);
                continue;
            }
            ThSkuTaxPO taxInfo = skuTaxMap.get(skuId);
            //履约费用
            CurrencyPriceVO fulfillmentPrice;
            if(!input.getFulfillmentFee().isEmpty() && input.getFulfillmentFee().get(skuId)!=null){
                fulfillmentPrice = input.getFulfillmentFee().get(skuId);
            }else {
                fulfillmentPrice = getFulfillmentPrice(input.getCountryCode(), skuId);
            }
            if(fulfillmentPrice==null || fulfillmentPrice.getPrice()==null){
                target.setDesc(fulfillmentPrice!=null&& StringUtils.isNotBlank(fulfillmentPrice.getMsg())?fulfillmentPrice.getMsg():"履约费用为空,无法计算关税;");
                result.put(skuId,target);
                continue;
            }
            //CIF价格=采购价（含税）+ 履约费用
            BigDecimal cif = priceMap.get(skuId).getPrice().add(fulfillmentPrice.getPrice());
            //进口关税金= CIF价格 x min（FormE关税，最惠国关税MFN%）；
            BigDecimal importDuties = cif.multiply(taxInfo.getFormeTax().min(taxInfo.getMfnTax()));
            //反倾销税金= CIF价格 x 反倾销税率(%)
            BigDecimal antiDumping = cif.multiply(taxInfo.getAntiDumpingTax());
            //目的国消费税金 = CIF价格  x 消费税率（%）
            BigDecimal consumption = cif.multiply(taxInfo.getConsumptionTax());
            //Local Tax=目的国消费税金 x Local Tax率（%）
            BigDecimal localTax = consumption.multiply(taxInfo.getLocalTax());
            //增值税金= （ CIF价格 + 进口关税金 +  反倾销税金 +消费税金 +Local Tax）x 增值税率（%）
            BigDecimal valueAdd = (cif.add(importDuties).add(antiDumping).add(consumption).add(localTax)).multiply(taxInfo.getValueAddedTax());
            //进口税费=进口关税金+反倾销税金+目的国消费税金 +Local Tax + 增值税金  (中间值不舍位，最终结果四舍五入)
            BigDecimal importTax = (importDuties.add(antiDumping).add(consumption).add(localTax).add(valueAdd)).setScale(2, RoundingMode.HALF_UP);

            //优化小数位展示
            String cifStr = cif.stripTrailingZeros().toPlainString();
            String importDutiesStr = importDuties.stripTrailingZeros().toPlainString();
            String antiDumpingStr = antiDumping.stripTrailingZeros().toPlainString();
            String consumptionStr = consumption.stripTrailingZeros().toPlainString();
            String localTaxStr = localTax.stripTrailingZeros().toPlainString();
            String valueAddStr = valueAdd.stripTrailingZeros().toPlainString();
            String importTaxStr = importTax.stripTrailingZeros().toPlainString();

            //计算过程消息
            String totalMsg = String.format("进口税费%s = 进口关税金%s+反倾销税金%s+目的国消费税金%s +Local Tax%s + 增值税金%s",importTaxStr,importDutiesStr,antiDumpingStr,consumptionStr,localTaxStr,valueAddStr);
            String cifMsg = String.format("CIF价格%s=采购价（含税）%s+ 履约费用%s",cif.stripTrailingZeros().toPlainString(),priceMap.get(skuId).getPrice(),fulfillmentPrice.getPrice());
            String importDutiesMsg = String.format("进口关税金 %s = CIF价格%s x min（FormE关税%s，最惠国关税MFN%s）",importDutiesStr,cifStr,taxInfo.getFormeTax().stripTrailingZeros().toPlainString(),taxInfo.getMfnTax().stripTrailingZeros().toPlainString());
            String antiDumpingMsg = String.format("反倾销税金 %s = CIF价格%s x 反倾销税率%s",antiDumpingStr,cifStr,taxInfo.getAntiDumpingTax().stripTrailingZeros().toPlainString());
            String consumptionMsg = String.format("目的国消费税金 %s = CIF价格%s  x 消费税率%s",consumptionStr,cifStr,taxInfo.getConsumptionTax().stripTrailingZeros().toPlainString());
            String localTaxMsg = String.format("Local Tax %s = 目的国消费税金%s x Local Tax率%s",localTaxStr,consumptionStr,taxInfo.getLocalTax().stripTrailingZeros().toPlainString());
            String valueAddMsg = String.format("增值税金 %s =（ CIF价格%s + 进口关税金%s +  反倾销税金%s + 消费税金%s + Local Tax%s）x 增值税率%s",valueAddStr,cifStr,importDutiesStr,antiDumpingStr,consumptionStr,localTaxStr,taxInfo.getValueAddedTax().stripTrailingZeros().toPlainString());
            target.setDesc(msg.append(totalMsg).append("\n").
                    append(cifMsg).append("\n").
                    append(importDutiesMsg).append("\n").
                    append(antiDumpingMsg).append("\n").
                    append(consumptionMsg).append("\n").
                    append(localTaxMsg).append("\n").
                    append(valueAddMsg).toString());
            target.setImportTax(importTax);
            target.setDeductionAmount(valueAdd.setScale(2, RoundingMode.HALF_UP));
            log.info("ThCountryTaxManageServiceImpl.queryCrossBorderSkuImportTax skuId:{} , res:{}" , skuId , JSON.toJSONString(target));
            result.put(skuId,target);
        }
        return result;
    }

    private Map<Long, ThSkuTaxPO> getSkuTaxMap(Set<Long> cnSkuIds) {
        Map<Long, ThSkuTaxPO> result = Maps.newHashMapWithExpectedSize(cnSkuIds.size());
        //国际sku批量转jdSku
        Map<Long, Long> res = skuAtomicService.querySkuMapByIscSkuIds(cnSkuIds);
        if(res.isEmpty()){
            log.error("BrCountryTaxReadServiceImpl.getSkuTaxMap invoke error, 给定的国际sku未能查询到正确的sku与jdSku映射关系 {}", cnSkuIds);
            return new HashMap<>();
        }
        //根据jdSku查关税率
        Map<Long, ThSkuTaxPO> skuTaxMap = thSkuTaxAtomicService.listSkuTax(new HashSet<>(res.values()));
        if(skuTaxMap.isEmpty()){
            log.error("BrCountryTaxReadServiceImpl.getSkuTaxMap invoke error, 给定的jdSku未能查询到关税配置 res {}", JSON.toJSONString(res));
            return new HashMap<>();
        }
        //构建国际sku&关税实体关系
        for (Map.Entry<Long, Long> entry : res.entrySet()) {
            result.put(entry.getKey(), skuTaxMap.get(entry.getValue()));
        }
        return result;
    }

    @Override
    public Map<Long, CrossBorderImportTaxResVO> queryImportTaxByIscSkuIds(IscSkuImportTaxReqVO input) {
        Map<Long, ThSkuTaxPO> skuTaxMap = this.getSkuTaxMap(input.getSkuIds());
        // 构建返回结果
        Map<Long, CrossBorderImportTaxResVO> resultMap = Maps.newHashMapWithExpectedSize(skuTaxMap.size());
        skuTaxMap.forEach((skuId,taxPo)-> resultMap.put(skuId, new CrossBorderImportTaxResVO(skuId,taxPo.getJdSkuId(), taxPo.getHsCode())));
        return resultMap;
    }

    public static void main(String[] args) {
        StringBuilder msg = new StringBuilder();
        msg.append("totalMsg").append("\\n").
                append("importDutiesMsg").append("\n").
                append("antiDumpingMsg").append("\n").
                append("consumptionMsg").append("\n").toString();
        System.out.println(msg);
    }

}
