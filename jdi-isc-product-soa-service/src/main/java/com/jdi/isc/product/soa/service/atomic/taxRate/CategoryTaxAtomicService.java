package com.jdi.isc.product.soa.service.atomic.taxRate;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.taxRate.po.CategoryTaxPO;
import com.jdi.isc.product.soa.repository.mapper.taxRate.CategoryTaxBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @Description: 类目税率信息原子服务
 * @Author: taxuezheng1
 * @Date: 2024/03/26 17:51
 **/
@Slf4j
@Service
public class CategoryTaxAtomicService extends ServiceImpl<CategoryTaxBaseMapper, CategoryTaxPO> {

    /**
     * 未删除的对象
     *
     * @param id id
     * @return 对象
     */
    public CategoryTaxPO getValidById(Long id) {
        LambdaQueryWrapper<CategoryTaxPO> wrapper = Wrappers.<CategoryTaxPO>lambdaQuery()
                .eq(CategoryTaxPO::getId, id)
                .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    //批量获取类目税率
    @PFTracing
    public Map<Long, CategoryTaxPO> batchSkuCatTax(CustomerVO customer, Collection<SkuPO> skuPo) {
        if (skuPo == null) {
            return Maps.newHashMap();
        }
        Map<Long, CategoryTaxPO> result = Maps.newHashMapWithExpectedSize(skuPo.size());
        //越南销售增值税率获取
        if (CountryConstant.COUNTRY_VN.equals(customer.getCountry())) {
            for (SkuPO po : skuPo) {
                //先按类目+sku查询
                LambdaQueryWrapper<CategoryTaxPO> skuWrapper = Wrappers.<CategoryTaxPO>lambdaQuery()
                        .eq(CategoryTaxPO::getCountryCode, customer.getCountry())
                        .eq(CategoryTaxPO::getCompanyType, Integer.valueOf(customer.getCompanyType()))
                        .isNull(CategoryTaxPO::getClientCode)
                        .eq(CategoryTaxPO::getJdCatId, po.getJdCatId())
                        .eq(CategoryTaxPO::getSkuId, po.getSkuId())
                        .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode());
                CategoryTaxPO itemBySku = super.getOne(skuWrapper);
                if (itemBySku != null) {
                    result.put(po.getSkuId(), itemBySku);
                    continue;
                }
                //再按类目查询
                LambdaQueryWrapper<CategoryTaxPO> cateWrapper = Wrappers.<CategoryTaxPO>lambdaQuery()
                        .eq(CategoryTaxPO::getCountryCode, customer.getCountry())
                        .eq(CategoryTaxPO::getCompanyType, Integer.valueOf(customer.getCompanyType()))
                        .isNull(CategoryTaxPO::getClientCode)
                        .isNull(CategoryTaxPO::getSkuId)
                        .eq(CategoryTaxPO::getJdCatId, po.getJdCatId())
                        .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode());
                CategoryTaxPO itemByCate = super.getOne(cateWrapper);
                if (itemByCate != null) {
                    result.put(po.getSkuId(), itemByCate);
                }
            }
            //泰国销售增值税率获取
        } else if (CountryConstant.COUNTRY_TH.equals(customer.getCountry())) {
            for (SkuPO po : skuPo) {
                //按客户+类目维度查询
                LambdaQueryWrapper<CategoryTaxPO> wrapper = Wrappers.<CategoryTaxPO>lambdaQuery()
                        .eq(CategoryTaxPO::getCountryCode, customer.getCountry())
                        .eq(CategoryTaxPO::getClientCode, customer.getClientCode())
                        .eq(CategoryTaxPO::getJdCatId, po.getJdCatId())
                        .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode());
                CategoryTaxPO item = super.getOne(wrapper);
                if (item != null) {
                    result.put(po.getSkuId(), item);
                }
            }
        }else if (CountryConstant.COUNTRY_HU.equals(customer.getCountry()) || CountryConstant.COUNTRY_ID.equals(customer.getCountry())){
            for (SkuPO po : skuPo) {
                //按国家+类目维度查询
                LambdaQueryWrapper<CategoryTaxPO> wrapper = Wrappers.<CategoryTaxPO>lambdaQuery()
                        .eq(CategoryTaxPO::getCountryCode, customer.getCountry())
                        .eq(CategoryTaxPO::getJdCatId, po.getJdCatId())
                        .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode());
                CategoryTaxPO item = super.getOne(wrapper);
                if (item != null) {
                    result.put(po.getSkuId(), item);
                }
            }
        }
        log.info("CategoryTaxAtomicService.batchSkuCatTax 客户:{} , 增值税率明细:{}", JSON.toJSONString(customer), JSON.toJSONString(result));
        return result;
    }

    @PFTracing
    public Map<Long, CategoryTaxPO> batchSkuCatTaxByCatId(CustomerVO customer, Long catId){
        if (catId == null) {
            return Maps.newHashMap();
        }
        Map<Long, CategoryTaxPO> result = Maps.newHashMapWithExpectedSize(1);
        //越南销售增值税率获取
        if (CountryConstant.COUNTRY_VN.equals(customer.getCountry())) {
                //再按类目查询
                LambdaQueryWrapper<CategoryTaxPO> cateWrapper = Wrappers.<CategoryTaxPO>lambdaQuery()
                    .eq(CategoryTaxPO::getCountryCode, customer.getCountry())
                    .eq(CategoryTaxPO::getCompanyType, Integer.valueOf(customer.getCompanyType()))
                    .isNull(CategoryTaxPO::getClientCode)
                    .isNull(CategoryTaxPO::getSkuId)
                    .eq(CategoryTaxPO::getJdCatId, catId)
                    .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode());
                CategoryTaxPO itemByCate = super.getOne(cateWrapper);
                if (itemByCate != null) {
                    result.put(catId, itemByCate);
                }
            //泰国销售增值税率获取
        } else if (CountryConstant.COUNTRY_TH.equals(customer.getCountry())) {
                //按客户+类目维度查询
                LambdaQueryWrapper<CategoryTaxPO> wrapper = Wrappers.<CategoryTaxPO>lambdaQuery()
                    .eq(CategoryTaxPO::getCountryCode, customer.getCountry())
                    .eq(CategoryTaxPO::getClientCode, customer.getClientCode())
                    .eq(CategoryTaxPO::getJdCatId, catId)
                    .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode());
                CategoryTaxPO item = super.getOne(wrapper);
                if (item != null) {
                    result.put(catId, item);
                }
        }else if (CountryConstant.COUNTRY_HU.equals(customer.getCountry()) || CountryConstant.COUNTRY_ID.equals(customer.getCountry())){
                //按国家+类目维度查询
                LambdaQueryWrapper<CategoryTaxPO> wrapper = Wrappers.<CategoryTaxPO>lambdaQuery()
                    .eq(CategoryTaxPO::getCountryCode, customer.getCountry())
                    .eq(CategoryTaxPO::getJdCatId, catId)
                    .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode());
                CategoryTaxPO item = super.getOne(wrapper);
                if (item != null) {
                    result.put(catId, item);
                }
        }
        log.info("CategoryTaxAtomicService.batchSkuCatTaxByCatId 客户:{} , 增值税率明细:{}", JSON.toJSONString(customer), JSON.toJSONString(result));
        return result;

    }
    /**
     * 根据 SKU ID 集合和原始分类 ID 查询关联的分类税收信息。
     *
     * @param skuIds           SKU ID 集合
     * @param originCategoryId 原始分类 ID
     * @return 符合条件的分类税收信息列表
     */
    public List<CategoryTaxPO> listBySkuIdsAndCategoryId(Set<Long> skuIds, Long originCategoryId) {
        LambdaQueryWrapper<CategoryTaxPO> wrapper = Wrappers.lambdaQuery(CategoryTaxPO.class)
                .in(CategoryTaxPO::getSkuId, skuIds)
                .eq(CategoryTaxPO::getJdCatId, originCategoryId)
                .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    /**
     * 根据 SKU ID 查询关联的分类税收信息。
     *
     * @param skuIds           SKU ID 集合
     * @return 符合条件的分类税收信息列表
     */
    public List<CategoryTaxPO> listBySkuIds(Collection<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CategoryTaxPO> wrapper = Wrappers.lambdaQuery(CategoryTaxPO.class)
                .in(CategoryTaxPO::getSkuId, skuIds)
                .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    /**
     * 根据国家编码查询对应的商品类目ID集合（仅查有效记录）。
     * @param countryCode 国家编码
     * @return 符合条件的商品类目ID集合
     */
    public Set<Long> getCatIdsByCountryCode(String countryCode) {
        if (StringUtils.isBlank(countryCode)) {
            return Collections.emptySet();
        }
        LambdaQueryWrapper<CategoryTaxPO> wrapper = Wrappers.lambdaQuery(CategoryTaxPO.class)
                .select(CategoryTaxPO::getJdCatId)
                .eq(CategoryTaxPO::getCountryCode, countryCode)
                .eq(CategoryTaxPO::getYn, YnEnum.YES.getCode());

        // 查询只返回jdCatId字段
        List<CategoryTaxPO> poList = super.list(wrapper);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptySet();
        }
        // 提取jdCatId并去重
        return poList.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getJdCatId()))
                .map(CategoryTaxPO::getJdCatId)
                .collect(Collectors.toSet());
    }
}
