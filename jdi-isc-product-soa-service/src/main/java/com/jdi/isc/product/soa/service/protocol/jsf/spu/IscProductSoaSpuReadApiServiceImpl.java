package com.jdi.isc.product.soa.service.protocol.jsf.spu;

import com.alibaba.fastjson.JSONObject;
import com.jd.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.spu.IscProductSoaSpuReadApiService;
import com.jdi.isc.product.soa.api.spu.req.*;
import com.jdi.isc.product.soa.api.spu.res.*;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.SystemContextHolder;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.service.manage.spu.SpuDraftManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.impl.tax.VnSpuTaxManageService;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * SPU写服务实现类
 * <AUTHOR>
 * @date 2024/7/15
 */
@Service
@Slf4j
public class IscProductSoaSpuReadApiServiceImpl implements IscProductSoaSpuReadApiService {


    @Resource
    private SpuReadManageService spuReadManageService;

    @Resource
    private VnSpuTaxManageService vnSpuTaxManageService;

    @Resource
    private SpuDraftManageService spuDraftManageService;


    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<SpuApiDTO>> spuPage(SpuQueryReqApiDTO spuQueryReqApiDTO) {
        log.info("IscProductSoaSpuReadApiServiceImpl.spuPage param:{}",JSONObject.toJSONString(spuQueryReqApiDTO));
        LangContextHolder.init(StringUtils.isNotBlank(spuQueryReqApiDTO.getLang())?spuQueryReqApiDTO.getLang(): LangConstant.LANG_ZH);
        SpuQueryReqVO param = SpuConvert.INSTANCE.spuQueryApiDto2ReqVO(spuQueryReqApiDTO);
        PageInfo<SpuVO> pageInfo = spuReadManageService.spuPage(param);
        return DataResponse.success(SpuConvert.INSTANCE.spuPageVo2ApiDto(pageInfo));
    }

    @Override
    @ToolKit(exceptionWrap = true,logFlag = false)
    public DataResponse<SpuDetailApiDTO> getDetailBySpuId(SpuDetailReqApiDTO input) {
        log.info("IscProductSoaSpuReadApiServiceImpl.getDetailBySpuId param:{}",JSONObject.toJSONString(input));
        LangContextHolder.init(StringUtils.isNotBlank(input.getLang())?input.getLang(): LangConstant.LANG_ZH);
//        SystemContextHolder.init(input.getSystemCode());
        SpuDetailVO vo = spuReadManageService.getDetailBySpuId(input.getSpuId());
        return DataResponse.success(SpuConvert.INSTANCE.detailVo2DTO(vo));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<SpuPrepareInfoApiDTO> getPrepare(SpuPrepareReqApiDTO input) {
        log.info("IscProductSoaSpuReadApiServiceImpl.getPrepareVo input:{}", JSONObject.toJSONString(input));
        LangContextHolder.init(StringUtils.isNotBlank(input.getLang())?input.getLang(): LangConstant.LANG_ZH);
//        SystemContextHolder.init(input.getSystemCode());
        SpuPrepareInfoVO vo = spuReadManageService.getPrepareVo(input.getLastCatId(),input.getAttributeScope(),input.getSourceCountryCode(),input.getIsExport(),input.getVendorCode());
        return DataResponse.success(SpuConvert.INSTANCE.spuPrepareInfoVo2Dto(vo));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<SaleUnitApiDTO>> saleUnitList(String lang) {
        log.info("IscProductSoaSpuReadApiServiceImpl.saleUnitList lang:{}",lang);
        List<SaleUnitVO> vos = spuReadManageService.saleUnitList(lang);
        return DataResponse.success(SpuConvert.INSTANCE.listSaleUnitVO2Dto(vos));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<SpuDetailApiDTO> getDetailInfo(SpuDetailReqApiDTO reqApiDTO) {
        log.info("IscProductSoaSpuReadApiServiceImpl.getDetailInfo reqApiDTO:{}",JSONObject.toJSONString(reqApiDTO));
        SpuDetailReqVO spuDetailReqVO = SpuConvert.INSTANCE.reqApiDTO2ReqVO(reqApiDTO);
        LangContextHolder.init(StringUtils.isNotBlank(reqApiDTO.getLang())?reqApiDTO.getLang(): LangConstant.LANG_ZH);
        SpuDetailVO spuDetail = spuReadManageService.getSpuDetail(spuDetailReqVO);
        return DataResponse.success(SpuConvert.INSTANCE.detailVo2DTO(spuDetail));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<SpuAmendApiDTO>> querySpuAmendVOListBySpuIds(List<Long> spuIds) {
        log.info("IscProductSoaSpuReadApiServiceImpl.querySpuAmendVOListBySpuIds spuIds:{}", JSON.toJSONString(spuIds));
        List<SpuAmendVO> spuAmendVOList = spuReadManageService.batchQuerySpuAmendVOListBySpuIds(spuIds);
        return DataResponse.success(SpuConvert.INSTANCE.listSpuAmendVO2ListSpuAmendApiDTO(spuAmendVOList));
    }

    /**
     * 判断消息是否完整。
     * @param saveSpuApiDTO 保存商品API DTO，包含商品信息。
     * @return true 如果消息完整，false 否则。
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> isMsgComplete(SaveSpuApiDTO saveSpuApiDTO) {
        log.info("IscProductSoaSpuReadApiServiceImpl.isMsgComplete param:{}", JSON.toJSONString(saveSpuApiDTO));
        ApiInitUtils.init(saveSpuApiDTO);
        SaveSpuVO param = SpuConvert.INSTANCE.saveSpuDto2Vo(saveSpuApiDTO);
        return vnSpuTaxManageService.isMsgComplete(param);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<SpuApiDTO>> spuDraftPage(SpuQueryReqApiDTO spuQueryReqApiDTO) {
        log.info("IscProductSoaSpuReadApiServiceImpl.spuDraftPage param:{}",JSONObject.toJSONString(spuQueryReqApiDTO));
        LangContextHolder.init(StringUtils.isNotBlank(spuQueryReqApiDTO.getLang()) ? spuQueryReqApiDTO.getLang(): LangConstant.LANG_ZH);
        SpuQueryReqVO param = SpuConvert.INSTANCE.spuQueryApiDto2ReqVO(spuQueryReqApiDTO);
        PageInfo<SpuVO> pageInfo = spuDraftManageService.page(param);
        return DataResponse.success(SpuConvert.INSTANCE.spuPageVo2ApiDto(pageInfo));
    }


    @Override
    public DataResponse<List<AuditNumDTO>> auditStatusNum(SpuPrepareReqApiDTO apiDTO) {
        log.info("IscProductSoaSpuReadApiServiceImpl.auditStatusNum param:{}",JSONObject.toJSONString(apiDTO));
        String lang = StringUtils.isNotBlank(apiDTO.getLang()) ? apiDTO.getLang(): LangConstant.LANG_ZH;
        LangContextHolder.init(lang);
        SpuPrepareReqVO spuPrepareReqVO = SpuConvert.INSTANCE.spuPrepareReqApiDTO2VO(apiDTO);
        spuPrepareReqVO.setLang(lang);
        List<AuditNumVO> auditNumVOList = spuReadManageService.auditStatusNum(spuPrepareReqVO);
        List<AuditNumDTO> spuApiDTOPageInfo = SpuConvert.INSTANCE.auditNumVoList2DtoList(auditNumVOList);
        log.info("IscProductSoaSpuReadApiServiceImpl.auditStatusNum param:{}, res:{}",JSONObject.toJSONString(apiDTO),JSONObject.toJSONString(spuApiDTOPageInfo));
        return DataResponse.success(spuApiDTOPageInfo);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<SpuApiDTO>> spuDraftPageForVc(SpuQueryReqApiDTO spuQueryReqApiDTO) {
        log.error("废弃接口");
        return null;
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> verifySupplierSpuId(SupplierSpuIdVerifyDTO verifyDTO) {
        return spuReadManageService.verifySupplierSpuId(verifyDTO);
    }
}
