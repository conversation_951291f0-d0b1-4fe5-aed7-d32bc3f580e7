package com.jdi.isc.product.soa.service.manage.spu;

import com.alibaba.fastjson.JSON;
import com.jdi.isc.product.soa.common.util.CompareDiffUtil;
import com.jdi.isc.product.soa.domain.common.biz.CompareResult;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.spu.biz.SaveSpuVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuDetailVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuVO;
import com.jdi.isc.product.soa.domain.spu.po.SpuCertificatePO;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * spu对象比较服务
 *
 * <AUTHOR>
 * @date 2023/12/7
 **/
@Slf4j
@Service
public class SpuCompareService {

    @Resource
    private SpuDraftManageService spuDraftManageService;

    @Resource
    private SpuReadManageService spuReadManageService;

    @Resource
    private LangManageService langManageService;


    /**
     * 比较商品资质对象
     *
     * @param view
     * @param db
     * @return
     */
    public boolean compareSpuCertificate(SpuCertificatePO view, SpuCertificatePO db) {
        if (view == db) {
            return true;
        }
        return view.getId().equals(db.getId()) && view.getSpuId().equals(db.getSpuId()) && view.getCertificateId().equals(db.getCertificateId()) && view.getCertificatePath().equals(db.getCertificatePath()) && view.getCertificateFileName().equals(db.getCertificateFileName());
    }


    /**
     * 1、对比spu信息、名字、详描、扩展属性、物流信息
     * 2、对比sku个数、销售属性维度、属性值、价格、物流信息
     * 3、资质列表
     * 4、商品详描
     *
     * @param view 保存商品请求对象
     */
    public String compareSaveSpu(SaveSpuVO view) {
        final Long spuId = view.getSpuVO().getSpuId();
        // 对比结果
        List<CompareResult> compareResultList = Lists.newArrayList();
        // 查询草稿
        SaveSpuVO draftSaveSpuVo = spuDraftManageService.getSaveSpuVoFromDraftBySpuId(spuId);
        if (Objects.nonNull(draftSaveSpuVo)) {
            log.info("SpuCompareService.compareSaveSpu draftSaveSpuVo={},view={}", JSON.toJSONString(draftSaveSpuVo), JSON.toJSONString(view));
            compareStepByStep(view, draftSaveSpuVo, compareResultList);
            log.info("SpuCompareService.compareSaveSpu 页面和草稿比对结果 compareResultList={}", JSON.toJSONString(compareResultList));
            return JSON.toJSONString(compareResultList);
        }
        //  DB已存储的商品内容
        SpuDetailVO detailVO = spuReadManageService.getSpuDetailVoFromDb(spuId);
        log.info("SpuCompareService.compareSaveSpu detailVO={},view={}", JSON.toJSONString(detailVO), JSON.toJSONString(view));
        SaveSpuVO dbSaveSpuVo = SpuConvert.INSTANCE.detailVo2SaveVo(detailVO);
        clearDbExist(dbSaveSpuVo);
        compareStepByStep(view, dbSaveSpuVo, compareResultList);
        log.info("SpuCompareService.compareSaveSpu 页面和DB比较结果 compareResultList={}", JSON.toJSONString(compareResultList));
        return JSON.toJSONString(compareResultList);
    }

    /**
     * 比较两个SpuDetailVO对象的详细信息，返回比较结果。
     * @param db 数据库中的SpuDetailVO对象
     * @param view 视图中的SpuDetailVO对象
     * @return 比较结果的JSON字符串
     */
    public String compareSpuDetail(SpuDetailVO db, SpuDetailVO view) {
        clearDbExist(db);
        // 对比结果
        List<CompareResult> compareResultList = Lists.newArrayList();
        compareStepByStep(view, db, compareResultList);
        return JSON.toJSONString(compareResultList);
    }

    private static void clearDbExist(SaveSpuVO dbSaveSpuVo) {
        dbSaveSpuVo.getSpuVO().setBrandName(null);
        dbSaveSpuVo.getSpuVO().setBuyer(null);
        dbSaveSpuVo.getSpuVO().setSpuStatus(null);
        dbSaveSpuVo.getSpuVO().setCreator(null);
        dbSaveSpuVo.getSpuVO().setUpdater(null);
        dbSaveSpuVo.getSpuVO().setYn(null);
        dbSaveSpuVo.getSpuVO().setGroupExtAttribute(null);
        dbSaveSpuVo.getSpuVO().setSaleAttribute(null);
    }

    private void compareStepByStep(SaveSpuVO view, SaveSpuVO draftSaveSpuVo, List<CompareResult> compareResultList) {
        // 比较spu
        CompareDiffUtil.diff(draftSaveSpuVo, view, compareResultList, SpuVO.class);
        // 比较sku列表
        CompareDiffUtil.diff(draftSaveSpuVo, view, compareResultList, SkuVO.class);
        // 比较商详
        compareDescription(draftSaveSpuVo, view, compareResultList);
        // 比较资质
        CompareDiffUtil.diff(draftSaveSpuVo, view, compareResultList, SpuCertificatePO.class);
    }

    private void compareDescription(SaveSpuVO draft, SaveSpuVO view, List<CompareResult> compareResultList) {
        compareMap("pcDescriptionMap", draft.getPcDescriptionMap(), view.getPcDescriptionMap(), compareResultList);
        compareMap("appDescriptionMap", draft.getAppDescriptionMap(), view.getAppDescriptionMap(), compareResultList);
    }

    private void compareMap(String descriptionType, Map<String, String> sourceMap, Map<String, String> targetMap, List<CompareResult> compareResultList) {
        if (MapUtils.isEmpty(sourceMap) && MapUtils.isEmpty(targetMap)) {
            return;
        }

        // 商详只要有不同就返回差异对象
        if ((MapUtils.isEmpty(sourceMap) && MapUtils.isNotEmpty(targetMap))
                || (MapUtils.isNotEmpty(sourceMap) && MapUtils.isEmpty(targetMap))) {
            CompareResult compareResult = new CompareResult();
            compareResult.setFieldName(descriptionType);
            compareResult.setSourceContent(MapUtils.isNotEmpty(sourceMap) ? JSON.toJSONString(sourceMap) : "");
            compareResult.setTargetContent(MapUtils.isNotEmpty(targetMap) ? JSON.toJSONString(targetMap) : "");
            compareResultList.add(compareResult);
            return;
        }

        for (String lang : langManageService.getLangCodeList()) {
            String sourceDesc = sourceMap.get(lang);
            String targetDesc = targetMap.get(lang);
            if (this.isDiff(sourceDesc, targetDesc)) {
                CompareResult compareResult = new CompareResult();
                compareResult.setFieldName(descriptionType);
                compareResult.setSourceContent(sourceMap.get(lang));
                compareResult.setTargetContent(targetMap.get(lang));
                compareResultList.add(compareResult);
                return;
            }
        }
    }

    private boolean isDiff(String sourceDesc, String targetDesc) {
        return (StringUtils.isNotBlank(sourceDesc) && StringUtils.isNotBlank(targetDesc) && !StringUtils.equals(sourceDesc, targetDesc))
                || (StringUtils.isBlank(sourceDesc) && StringUtils.isNotBlank(targetDesc))
                || (StringUtils.isNotBlank(sourceDesc) && StringUtils.isBlank(targetDesc));
    }
}
