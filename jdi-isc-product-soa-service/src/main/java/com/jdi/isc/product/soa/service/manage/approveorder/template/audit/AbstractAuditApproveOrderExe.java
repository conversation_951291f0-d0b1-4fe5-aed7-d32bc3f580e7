package com.jdi.isc.product.soa.service.manage.approveorder.template.audit;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.approveorder.common.ApproveOrderStatusEnum;
import com.jdi.isc.product.soa.api.approveorder.common.AuditActionEnum;
import com.jdi.isc.product.soa.api.approveorder.common.AuditStatusEnum;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderAuditMqDTO;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderMqDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiReqDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiResDTO;
import com.jdi.isc.product.soa.common.constants.CacheKeyConstant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.common.util.JimUtils;
import com.jdi.isc.product.soa.common.util.validation.ValidationUtil;
import com.jdi.isc.product.soa.domain.approveorder.po.ApproveOrderPO;
import com.jdi.isc.product.soa.rpc.mq.ProductCenterMqService;
import com.jdi.isc.product.soa.rpc.utils.ExceptionUtil;
import com.jdi.isc.product.soa.service.atomic.approveorder.ApproveOrderAtomicService;
import com.jdi.isc.product.soa.service.manage.approveorder.context.ApproveOrderAuditContext;
import com.jdi.isc.product.soa.service.support.transactional.ProductTransactionExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.List;

/**
 * The type Abstract create approve order exe.
 */
@Slf4j
public abstract class AbstractAuditApproveOrderExe {

    @Resource
    protected ProductTransactionExecutor transactionExecutor;

    @Resource
    protected ApproveOrderAtomicService approveOrderAtomicService;

    @Resource
    protected JimUtils jimUtils;

    @Resource
    protected OperDuccConfig operDuccConfig;


    /**
     * 获取业务类型
     *
     * @return 业务类型标识符 biz type
     */
    public abstract int getBizType();

    /**
     * 获取当前bean的名称
     *
     * @return 返回当前bean的名称字符串 bean name
     */
    public abstract String getBeanName();

    @Resource
    private ProductCenterMqService productCenterMqService;


    public DataResponse<AuditApiResDTO> execute(AuditApiReqDTO input) {
        Pair<Boolean, String> checkParam = this.checkParam(input);

        if (!checkParam.getKey()) {
            log.info("校验参数未通过. param={}, checkParam={}", JSONObject.toJSONString(input), checkParam.getValue());
            return DataResponse.error(checkParam.getValue());
        }

        AuditApiResDTO result = new AuditApiResDTO();
        result.setAction(input.getAction());

        List<AuditApiResDTO.AuditIdResDTO> auditIdList = Lists.newArrayList();

        for(Long id : input.getIds()) {
            AuditApiReqDTO singleRequest = BeanUtil.copyProperties(input, AuditApiReqDTO.class);
            singleRequest.setIds(Lists.newArrayList(id));
            DataResponse<AuditApiResDTO> response = this.singleExecute(singleRequest);

            if (!response.getSuccess()) {
                auditIdList.add(new AuditApiResDTO.AuditIdResDTO(id, false, response.getMessage()));
            } else {
                auditIdList.add(new AuditApiResDTO.AuditIdResDTO(id, true, null));
            }
        }

        result.setAuditIdList(auditIdList);

        return DataResponse.success(result);
    }

    /**
     * Execute data response.
     *
     * @param input the input
     * @return the data response
     */
    public DataResponse<AuditApiResDTO> singleExecute(AuditApiReqDTO input) {

        Instant start = Instant.now();
        // 校验数据
        Pair<Boolean, String> checkParam = this.checkParam(input);

        if (!checkParam.getKey()) {
            log.info("singleExecute, 校验参数未通过. param={}, checkParam={}", JSONObject.toJSONString(input), checkParam.getValue());
            return DataResponse.error(checkParam.getValue());
        }

        ApproveOrderAuditContext context = new ApproveOrderAuditContext(input);

        Pair<Boolean, String> lockPair = this.tryLock(context);

        if (!lockPair.getKey()) {
            return DataResponse.error(lockPair.getValue());
        }

        try {
            // 校验数据
            this.checkData(context);

            if (context.isIdempotent()) {
                log.info("AbstractAuditApproveOrderExe, 幂等操作. ");
                return DataResponse.success();
            }

            // 执行事务，更新审批状态
            this.updateApproveStatus(context);

            // 执行事务，创建审批单
            ApproveOrderMqDTO<?> mqData = this.buildMqData(context);

            // 发送消息
            this.sendMessageRetry(mqData);
        } catch (Exception e) {
            log.error("审核失败, param={}", JSONObject.toJSONString(input), e);
            return DataResponse.error(ExceptionUtil.getMessage(e, "审核失败，请稍后重试！"));
        } finally {
            log.error("审批完成, param={}, cost={}", JSONObject.toJSONString(input), Duration.between(start, Instant.now()).toMillis());
            // 释放锁
            this.releaseLock(context);
        }

        // 返回结果
        return DataResponse.success();
    }

    protected void checkData(ApproveOrderAuditContext context) {

        // 审核单据id
        Long approveId = context.getInput().getIds().get(0);
        // 审核人erp
        String approveErp = context.getInput().getPin();

        // 审核动作
        Integer action = context.getInput().getAction();

        // 查询进行中的审批单
        ApproveOrderPO order = approveOrderAtomicService.getValidById(approveId);

        if (StringUtils.isEmpty(order.getCurrentNodeErp())) {
            throw new ProductBizException("未查询到审批人信息，请稍后重试！%s", order.getApplyCode());
        }

        if (AuditActionEnum.codeOf(action) != AuditActionEnum.CANCEL && !Splitter.on(',').splitToList(order.getCurrentNodeErp()).contains(approveErp)) {
            throw new ProductBizException("当前用户不是当前审批人，请勿越权操作！");
        }

        // 如果是撤回单号未回写则不允许操作
        if (AuditActionEnum.codeOf(action) == AuditActionEnum.CANCEL && StringUtils.isEmpty(order.getApplyCode())) {
            throw new ProductBizException("正在提交审批审批，请稍后重试！");
        }

        if (ApproveOrderStatusEnum.codeOf(order.getStatus()) == ApproveOrderStatusEnum.COMPLETED) {
            throw new ProductBizException("当前审批单已完成，请勿重复提交！");
        }

        // 审核状态
        AuditStatusEnum currentNodeAuditStatus = AuditStatusEnum.codeOf(order.getCurrentNodeAuditStatus());
        AuditStatusEnum auditStatus = AuditStatusEnum.codeOf(order.getAuditStatus());

        if (currentNodeAuditStatus == AuditStatusEnum.STATUS_3) {
            throw new ProductBizException("当前单据正在审核中, 请稍后进行查询审批结果 %s", order.getApplyCode());
        }

        if (currentNodeAuditStatus != AuditStatusEnum.STATUS_2) {
            throw new ProductBizException("当前单据状态为%s, 不能操作%s", auditStatus.getDesc(), AuditActionEnum.getNameByCode(action));
        }

        context.setOrder(order);
    }

    protected Pair<Boolean, String> tryLock(ApproveOrderAuditContext context) {
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.PRODUCT_APPROVE_AUDIT_KEY, String.valueOf(context.getInput().getIds().get(0)));

        Boolean lock = jimUtils.simpleLock(lockKey, String.valueOf(Thread.currentThread().getId()), 60);

        if (!lock) {
            log.warn("当前记录正在操作，请勿重复操作！lockKey={}, threadId={}", lockKey, Thread.currentThread().getId());
            return Pair.of(false, "当前记录正在操作，请勿重复操作！");
        }

        return Pair.of(true, "");
    }


    protected void releaseLock(ApproveOrderAuditContext context) {
        String lockKey = CacheKeyConstant.getKey(CacheKeyConstant.PRODUCT_APPROVE_AUDIT_KEY, String.valueOf(context.getInput().getIds().get(0)));

        try {
            jimUtils.simpleLockRelease(lockKey, String.valueOf(Thread.currentThread().getId()));
        } catch (Exception e) {
            log.error("释放所失败. lockKey={}, requestId={}", lockKey, Thread.currentThread().getId(), e);
        }
    }

    private Pair<Boolean, String> checkParam(AuditApiReqDTO input) {

        String message = ValidationUtil.validateFindFirstError(input);

        if (StringUtils.isNotEmpty(message)) {
            return Pair.of(false, message);
        }

        return Pair.of(true, "");
    }

    private void updateApproveStatus(ApproveOrderAuditContext context) {

        AuditApiReqDTO input = context.getInput();

        Integer action = input.getAction();
        ApproveOrderPO order = context.getOrder();
        String approveComment = input.getApproveComment();

        transactionExecutor.execute(() -> {
            // 获取进行中的状态
            int auditStatus = approveOrderAtomicService.getIngAuditStatusByAction(action);
            // 创建审批单
            approveOrderAtomicService.updateAuditStatus(order, auditStatus, AuditStatusEnum.STATUS_3.getCode(), action, approveComment);
        });
    }

    /**
     * 构建mq对象
     */
    private ApproveOrderMqDTO<?> buildMqData(ApproveOrderAuditContext context) {

        AuditApiReqDTO input = context.getInput();
        ApproveOrderPO order = context.getOrder();

        Integer auditAction = AuditActionEnum.codeOf(context.getInput().getAction()).getCode();

        // mq key
        String businessId = String.format("approve_id_%d_%d", auditAction, order.getId());



        // 审核数据
        ApproveOrderAuditMqDTO data = new ApproveOrderAuditMqDTO(order.getId(), input.getApproveComment());
        data.setBizId(order.getBizId());
        data.setBizType(order.getBizType());
        data.setApproveComment(input.getApproveComment());

        return new ApproveOrderMqDTO<>(input.getPin(), auditAction, businessId, data);
    }

    /**
     * 发送消息
     *
     * @param mqData the mq data
     */
    protected void sendMessageRetry(ApproveOrderMqDTO<?> mqData) {
        productCenterMqService.sendProductSoaMessageRetry(mqData.getBusinessId(), mqData, operDuccConfig.getApproveOrderTopic());
    }
}
