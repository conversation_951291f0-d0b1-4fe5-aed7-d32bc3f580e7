package com.jdi.isc.product.soa.service.manage.category;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.domain.category.biz.GlobalQualificationPageVO;
import com.jdi.isc.product.soa.domain.category.biz.GlobalQualificationVO;
import com.jdi.isc.product.soa.domain.category.biz.GroupCertificateVO;
import com.jdi.isc.product.soa.domain.common.biz.KvVO;

import java.util.Collection;
import java.util.List;

/**
 * @Description: 国际资质主数据数据维护服务
 * @Author: taxuezheng1
 * @Date: 2024/07/12 11:33
 **/

public interface GlobalQualificationManageService {

    /**
     * 保存、更新
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<Boolean> saveOrUpdate(GlobalQualificationVO input);

    /**
     * 详情
     * @param id 对象ID
     * @return VO对象
     */
    GlobalQualificationVO detail(Long id);

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    PageInfo<GlobalQualificationPageVO.Response> pageSearch(GlobalQualificationPageVO.Request input);

    /**
     * 详情
     * @param catId 末级分类id
     * @param sourceCountryCode 来源国家
     * @param targetCountryCodes 销售国家
     * @return VO对象
     */
    List<GlobalQualificationVO> queryByCountry(Long catId, String sourceCountryCode,
        Collection<String> targetCountryCodes,Integer dimension,String lang,Integer isExport);

    /**
     * 详情
     * @param catId 末级分类id
     * @param sourceCountryCode 来源国家
     * @param targetCountryCodes 销售国家
     * @return VO对象
     */
    List<GlobalQualificationVO> queryByCountry(Long catId, String sourceCountryCode,
        Collection<String> targetCountryCodes,Integer dimension,String lang);

    /**
     * 根据国家代码和维度查询组证书信息。
     * @param catId 组证书类别ID。
     * @param sourceCountryCode 源国家代码。
     * @param targetCountryCodes 目标国家代码集合。
     * @param dimension 维度。
     * @param lang 语言。
     * @param isExport 是否导出。
     * @return 查询结果的List。
     */
    List<GroupCertificateVO> queryGroupByCountry(Long catId, String sourceCountryCode,
                                            Collection<String> targetCountryCodes, Integer dimension, String lang, Integer isExport);


    /**
     * 校验多语言名称是否有重复
     */
    boolean checkLangName(GlobalQualificationVO reqVo);

    /**
     * 根据id查询属性名称
     */
    List<KvVO> queryQualificationName(List<Long> ids, String lang);
}
