package com.jdi.isc.product.soa.service.mapstruct.category;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.KvDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import com.jdi.isc.product.soa.api.spu.req.SpuLangApiDTO;
import com.jdi.isc.product.soa.api.spu.req.UpdateProductGlobalAttributeReqDTO;
import com.jdi.isc.product.soa.api.spu.res.GroupPropertyDTO;
import com.jdi.isc.product.soa.api.spu.res.UpdateGlobalAttributeResDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.*;
import com.jdi.isc.product.soa.api.wimp.category.res.GlobalAttributeApiResDTO;
import com.jdi.isc.product.soa.domain.category.biz.*;
import com.jdi.isc.product.soa.domain.category.po.GlobalAttributePO;
import com.jdi.isc.product.soa.domain.common.biz.KvVO;
import com.jdi.isc.product.soa.domain.spu.biz.GroupPropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangExtendVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 国际跨境属性对象转换
 * @Author: taxuezheng1
 * @Date: 2024/07/12 13:17
 **/
@Mapper
public interface GlobalAttributeConvert {

    GlobalAttributeConvert INSTANCE = Mappers.getMapper(GlobalAttributeConvert.class);

    GlobalAttributePO vo2Po(GlobalAttributeVO vo);

    GlobalAttributeVO po2Vo(GlobalAttributePO po);

    List<GlobalAttributeVO> listPo2Vo(List<GlobalAttributePO> poList);

    List<GlobalAttributePO> listVo2Po(List<GlobalAttributeVO> voList);

    List<GlobalAttributePageVO.Response> pageListPo2Vo(List<GlobalAttributePO> pos);

    GlobalAttributePageVO.Request pageApiReq2PageVoReq(GlobalAttributePageApiDTO.Request pageApiReq);

    PageInfo<GlobalAttributePageApiDTO.Response> pageVoResponse2PageApiRes(PageInfo<GlobalAttributePageVO.Response> pageVoResponse);

    GlobalAttributeApiDTO vo2ApiDTO(GlobalAttributeVO detail);

    GlobalAttributeVO apiDto2Vo(GlobalAttributeApiDTO input);

    List<GlobalAttributePageApiDTO.Response> pageRes2ApiPageRes(List<GlobalAttributePageVO.Response> list);

    List<KvDTO> listKvDto2Vo(List<KvVO> list);

    List<GlobalReqVO> listReqDto2Vo(List<GlobalReqApiDTO> list);

    List<GlobalResApiDTO> listResVo2Dto(List<GlobalResVO> list);

    GlobalAttributeQueryReqVO queryReqDTO2ReqVO(GlobalAttributeQueryReqDTO queryReqDTO);

    List<GlobalAttributeApiResDTO> restVO2ApiResDTO(List<GlobalAttributeResVO> resVOList);

    UpdateProductGlobalAttributeReqVO updateGlobalDto2Vo(UpdateProductGlobalAttributeReqDTO reqDTO);

    SpuLangExtendVO langExtendDto2Vo(SpuLangApiDTO spuLangApiDTO);

    UpdateGlobalAttributeResDTO updateGlobalVo2DTO(UpdateGlobalAttributeResVO resVO);

    List<PropertyVO> listVo2Vo(List<PropertyVO> list);

    PropertyApiDTO propertyVo2ApiVo(PropertyVO propertyVO);

    Map<String, PropertyApiDTO> map2Api(Map<String, PropertyVO> voMap);

    Map<String, Map<String, PropertyApiDTO>> propertyMap2ApiMap(Map<String, Map<String, PropertyVO>> map);

    PropertyVO propertyApiDTO2VO(PropertyApiDTO apiDTO);

    GroupPropertyDTO groupPropertyVo2Dto(GroupPropertyVO groupPropertyVO);

    List<GroupPropertyDTO> listGroupPropertyVo2Dto(List<GroupPropertyVO> groupPropertyVOList);

    /**
     * Date转换为Long时间戳
     * @param value Date对象
     * @return Long时间戳，如果Date为null则返回null
     */
    default Long map(Date value) {
        return value != null ? value.getTime() : null;
    }

    /**
     * Long时间戳转换为Date
     * @param value Long时间戳
     * @return Date对象，如果Long为null则返回null
     */
    default Date map(Long value) {
        return value != null ? new Date(value) : null;
    }
}
