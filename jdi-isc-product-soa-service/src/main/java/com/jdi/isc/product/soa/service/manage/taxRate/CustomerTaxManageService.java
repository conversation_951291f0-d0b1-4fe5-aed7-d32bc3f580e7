package com.jdi.isc.product.soa.service.manage.taxRate;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.taxRate.res.CustomerSaleTaxRateApiDTO;
import com.jdi.isc.product.soa.domain.taxRate.req.CustomerTaxQueryReqVO;
import com.jdi.isc.product.soa.domain.taxRate.res.CustomerSkuTaxResVO;
import com.jdi.isc.product.soa.domain.taxRate.res.CustomerTaxResVO;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description：客户侧税管理服务
 * @Date 2024-12-16
 */
public interface CustomerTaxManageService {

    /**
     * 批量查询客户商品税务信息。
     * @param reqDTO 客户商品税务查询请求参数。
     * @return 包含客户商品税务信息的响应对象。
     */
    DataResponse<CustomerTaxResVO> batchQueryCustomerTax(CustomerTaxQueryReqVO reqDTO);


    /**
     * 批量查询指定 SKU ID 的巴西客户税率信息。
     * @param skuIds SKU ID 集合
     * @return 包含客户 SKU 税率信息的 DataResponse 对象
     */
    DataResponse<Map<Long,CustomerSkuTaxResVO>> batchQueryBrCustomerSkuTax(Set<Long> skuIds);

    /**
     * 根据客户编码和商品ID获取客户初始销售税率信息
     * @param clientCode 客户编码
     * @param skuId 商品ID
     * @return 客户销售税率信息数据传输对象
     */
    CustomerSaleTaxRateApiDTO getBrCustomerInitSaleTaxRate(String clientCode, Long skuId);
}
