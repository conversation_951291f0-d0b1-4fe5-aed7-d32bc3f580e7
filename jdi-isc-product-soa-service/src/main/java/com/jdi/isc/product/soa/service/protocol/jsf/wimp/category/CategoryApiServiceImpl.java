package com.jdi.isc.product.soa.service.protocol.jsf.wimp.category;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.category.biz.CategoryQueryPageApiDTO;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.supplier.res.CategoryTreeResDTO;
import com.jdi.isc.product.soa.api.wimp.category.CategoryApiService;
import com.jdi.isc.product.soa.api.wimp.category.req.CategoryBatchReqApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.CategoryReqApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.CategoryTreeReq;
import com.jdi.isc.product.soa.api.wimp.category.res.CategoryPathNameDTO;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.category.biz.CategoryQueryPageVO;
import com.jdi.isc.product.soa.domain.category.biz.CategoryVO;
import com.jdi.isc.product.soa.service.manage.category.CategoryManageService;
import com.jdi.isc.product.soa.service.manage.category.CategoryOutService;
import com.jdi.isc.product.soa.service.mapstruct.category.CategoryConvert;
import com.jdi.isc.product.soa.service.protocol.jsf.supplier.BusinessLineReadApiServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/09/09 11:05 上午
 */

@Slf4j
@Service
public class CategoryApiServiceImpl implements CategoryApiService {

    @Resource
    private CategoryOutService categoryOutService;
    @Resource
    private BusinessLineReadApiServiceImpl businessLineReadApiServiceImpl;
    @Resource
    private CategoryManageService categoryManageService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<CategoryTreeResDTO>> queryList(CategoryReqApiDTO input) {
        Map<Long, List<CategoryVO>> categoryVOMap = categoryOutService.queryCategory(input.getCatId(),input.getLangSet(),input.getOnlyFormalCat());
        List<CategoryTreeResDTO> categoryTreeResDTOList = businessLineReadApiServiceImpl.buildCategoryTree(categoryVOMap);
        return DataResponse.success(categoryTreeResDTOList);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<CategoryTreeResDTO>> queryListBySupplierCode(CategoryReqApiDTO param) {
        log.info("CategoryApiServiceImpl.queryListBySupplierCode param:{}", JSONObject.toJSONString(param));
        ApiInitUtils.init(param.getPin(),param.getLang(),param.getSystemCode());
        return DataResponse.success(CategoryConvert.INSTANCE.listTreeVo2TreeDto(categoryOutService.queryListBySupplierCode(param.getSupplierCode())));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, CategoryPathNameDTO>> queryByCategoryIds(CategoryBatchReqApiDTO param) {
        log.info("CategoryApiServiceImpl.queryByCategoryIds param:{}", JSONObject.toJSONString(param));
        ApiInitUtils.init(param.getPin(),param.getLang(),param.getSystemCode());
        return DataResponse.success(CategoryConvert.INSTANCE.mapVo2Dto(categoryOutService.queryCategoryByIds(param.getCatIds(),null)));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<String>> queryAllCategoryTile(CategoryTreeReq input) {
        log.info("CategoryApiServiceImpl.queryAllCategoryTile param:{}", JSONObject.toJSONString(input));
        List<String> categoryTiles = categoryOutService.queryAllCategoryTile(input.getIndex(),input.getSize(),input.getLangCodes());
        return DataResponse.success(categoryTiles);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<String>> queryCategoryBuyerAllCategoryTile(CategoryQueryPageApiDTO.Request input) {
        log.info("CategoryApiServiceImpl.queryCategoryBuyerAllCategoryTile param:{}", JSONObject.toJSONString(input));
        List<String> categoryTiles = categoryOutService.queryCategoryBuyerAllCategoryTile(input.getIndex(),input.getSize(),input.getCountryCode(),input.getLangCodes());
        return DataResponse.success(categoryTiles);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<CategoryQueryPageApiDTO.Response>> pageSearch(CategoryQueryPageApiDTO.Request input){
        log.info("CategoryApiServiceImpl.pageSearch param:{}", JSONObject.toJSONString(input));
        ApiInitUtils.init(input.getPin(), StringUtils.isNotBlank(input.getLang())?input.getLang(): LangConstant.LANG_ZH,input.getSystemCode());
        CategoryQueryPageVO.Request reqVo = CategoryConvert.INSTANCE.pageApiReq2PageVoReq(input);

        PageInfo<CategoryQueryPageVO.Response> pageInfoRes = categoryManageService.pageSearch(reqVo);
        PageInfo<CategoryQueryPageApiDTO.Response> pageInfoApiRes = CategoryConvert.INSTANCE.pageVoResponse2PageApiRes(pageInfoRes);

        return DataResponse.success(pageInfoApiRes);
    }
}
