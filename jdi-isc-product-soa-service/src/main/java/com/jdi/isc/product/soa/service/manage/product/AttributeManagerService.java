package com.jdi.isc.product.soa.service.manage.product;


import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/5 18:25
 */
public interface AttributeManagerService {

//    /**
//     * 获取销售属性
//     * @param catId 类目Id
//     * @param attribute 属性串
//     * @param lang 语种
//     * @return 属性
//     */
//    public List<PropertyVO> getSaleAttrByCatId(long catId, String attribute, String lang);

    /**
     * 获取扩展属性
     * @param catId 类目Id
     * @param groupExtAttribute 属性串, 新版本为带属性组的json格式字符串
     * @param lang 语种
     * @return 属性
     */
    public List<PropertyVO> getExtendAttrByCatId(long catId, String groupExtAttribute, String lang);

}
