package com.jdi.isc.product.soa.service.manage.attribute.impl;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.gms.greatdane.category.domain.CategoryGroupAtt;
import com.jd.gms.greatdane.category.domain.CategoryGroupAttValue;
import com.jd.gms.greatdane.category.domain.NewAttribute;
import com.jd.gms.greatdane.category.domain.NewAttributeValue;
import com.jd.gms.greatdane.category.request.GetNewAttributeParam;
import com.jd.gms.greatdane.category.request.GetNewAttributeValueParam;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.translate.req.BatchMultiTranslateReqDTO;
import com.jdi.isc.product.soa.api.translate.req.MultiTranslateReqDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.domain.attribute.biz.ExtAttributeLangTransformVO;
import com.jdi.isc.product.soa.domain.attribute.po.ExtAttributeLangPO;
import com.jdi.isc.product.soa.domain.attribute.po.ExtAttributeTempPO;
import com.jdi.isc.product.soa.domain.category.po.CategoryPO;
import com.jdi.isc.product.soa.rpc.gms.CategoryRpcService;
import com.jdi.isc.product.soa.rpc.gms.GmsAttributeRpcService;
import com.jdi.isc.product.soa.service.atomic.attribute.ExtAttributeLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.category.CategoryAtomicService;
import com.jdi.isc.product.soa.service.manage.attribute.ExtAttributeLangService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.manage.translate.TextTranslateManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 新扩展属性管理服务
 * <AUTHOR>
 * @date 20240219
 **/
@Slf4j
@Service
public class ExtAttributeLangServiceImpl implements ExtAttributeLangService {

    @Resource
    private CategoryAtomicService categoryAtomicService;
    @Resource
    private CategoryRpcService categoryRpcService;
    @Resource
    private TextTranslateManageService textTranslateManageService;
    @Resource
    private LangManageService langManageService;
    @Resource
    private ExtAttributeLangAtomicService extAttributeLangAtomicService;
    @Resource
    private GmsAttributeRpcService gmsAttributeRpcService;

    final ExecutorService pool = new ThreadPoolExecutor(2, 4, 30L, TimeUnit.MICROSECONDS,
            new ArrayBlockingQueue<>(10000), new ThreadFactoryBuilder().setNamePrefix("ext-attr").build(), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 根据语种、扩展属性中文名查询多语言扩展属性值，需要增加对属性组的处理
     *    扩属中文      语种   扩属值
     *       ↓          ↓      ↓
     * Map<String,Map<String,String> >
     */
    @Override
    @ToolKit(logFlag = false)
    @PFTracing
    public Map<String, Map<String,String>> getExtAttrLangMapByCnName(Set<String> keyword,Set<String> langList){
        Assert.isTrue(CollectionUtils.isNotEmpty(keyword) && CollectionUtils.isNotEmpty(langList),"ExtAttributeLangServiceImpl.getExtAttrLangMapByCnName illegal params : null keyword or langList");
        Map<String, Map<String, String>> result = new HashMap<>();
        CompletableFuture<Map<String, Map<String, String>>> future = CompletableFuture.supplyAsync(() -> getExtAttrLangMap(keyword, langList),pool);
        try {
            result = future.get(5, TimeUnit.SECONDS);
        }catch (Exception e){
            log.error("ExtAttributeLangServiceImpl.getExtAttrLangMapByCnName invoke error 扩展属性多语言获取超时,降级返回为空", e);
        }
        return result;
    }
    @PFTracing
    Map<String, Map<String,String>> getExtAttrLangMap(Set<String> keyword,Set<String> langList){
        ExtAttributeLangTransformVO dbRes = extAttributeLangAtomicService.search(keyword, langList);

        //判断返回值是否有多语言缺失,有则实时翻译补充 key为中文关键字,value为待翻译语种
        if(keyword.size()!=dbRes.getMatchStr().size()){
            final Set<String> mStr = dbRes.getMatchStr();
            //异步触发扩展属性翻译及多语言落库
            CompletableFuture.runAsync(() -> doInitTranslateAndSave(CollectionUtils.subtract(keyword, mStr)),pool);
        }

        if(CollectionUtils.isEmpty(dbRes.getTargetList())){
            return new HashMap<>();
        }

        return buildTranslateResult(dbRes.getTargetList());
    }

    /** 多语言扩展属性数据库实体转Map实体*/
    @PFTracing
    private Map<String, Map<String,String>> buildTranslateResult(List<ExtAttributeLangPO> dbRes){
        Map<String, Map<String,String>> result = new HashMap<>();
        //根据groupId分组
        Map<String, List<ExtAttributeLangPO>> groupedAttr = dbRes.stream().collect(Collectors.groupingBy(ExtAttributeLangPO::getGroupId));
        //每次循环处理一个扩展属性多语言集合
        for (Map.Entry<String, List<ExtAttributeLangPO>> entry : groupedAttr.entrySet()) {
            String zhKeyword = null;
            Map<String,String> langKeyword = Maps.newHashMapWithExpectedSize(16);
            for(ExtAttributeLangPO po : entry.getValue()){
                if(LangConstant.LANG_ZH.equals(po.getLang())){
                    zhKeyword = po.getExtAttributeName();
                }
                langKeyword.put(po.getLang(),po.getExtAttributeName());
            }
            if(StringUtils.isNotBlank(zhKeyword)){
                result.put(zhKeyword,langKeyword);
            }
        }
        return result;
    }

    /**
     * 根据语种、扩展属性Id查询多语言扩展属性名称
     *    扩属Id      语种   扩属值
     *      ↓          ↓      ↓
     * Map<Long,Map<String,String> >
     */
    @Override
    public Map<Integer, Map<String,String>> getExtAttrLangMapById(Set<Integer> attributeIds,Set<String> langList){
        Map<Integer, Map<String,String>> result = new HashMap<>();
        //去中台根据id换扩属中文名
        Map<Integer, String> gmsAttrChName = getAttributeById(attributeIds);
        if(MapUtils.isEmpty(gmsAttrChName)){
            return result;
        }
        return ch2Multilingual(gmsAttrChName,langList);
    }

    /**
     * 根据语种、扩展属性值Id查询多语言扩展属性值名称
     *    扩属值Id      语种   扩属值
     *      ↓          ↓      ↓
     * Map<Long,Map<String,String> >
     */
    public Map<Integer, Map<String,String>> getExtAttrValueLangMapById(Set<Integer> attributeValueIds,Set<String> langList){
        Map<Integer, Map<String,String>> result = new HashMap<>();
        //去中台根据id换扩属中文名
        Map<Integer, String> gmsAttrValueChName = getAttributeValueById(attributeValueIds);
        if(MapUtils.isEmpty(gmsAttrValueChName)){
            return result;
        }
        return ch2Multilingual(gmsAttrValueChName,langList);
    }

    private Map<Integer, Map<String,String>> ch2Multilingual(Map<Integer, String> gmsChName,Set<String> langList){
        Map<Integer, Map<String,String>> result = new HashMap<>();
        //去国际库根据中文名获取多语言名
        Map<String, Map<String, String>> localLangName = getExtAttrLangMapByCnName(new HashSet<>(gmsChName.values()), langList);
        if(MapUtils.isEmpty(localLangName)){
            return result;
        }
        for (Map.Entry<Integer, String> entry : gmsChName.entrySet()) {
            result.put(entry.getKey(),localLangName.get(entry.getValue()));
        }
        return result;
    }

    @Override
    public Map<Integer, String> getAttributeById(Set<Integer> attributeId) {
        GetNewAttributeParam in = new GetNewAttributeParam();
        in.setComAttIds(attributeId);
        Map<Integer, NewAttribute> res = gmsAttributeRpcService.getNewAttribute(in);
        if(MapUtils.isNotEmpty(res)) {
            return res.entrySet().stream().collect(
                Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().getName()
                ));
        }
        return new HashMap<>();
    }

    @Override
    public Map<Integer, String> getAttributeValueById(Set<Integer> attributeValueId) {
        GetNewAttributeValueParam in = new GetNewAttributeValueParam();
        in.setComValueIds(Sets.newHashSet(attributeValueId));
        Map<Integer, NewAttributeValue> res = gmsAttributeRpcService.getNewAttributeValue(in);
        if(MapUtils.isNotEmpty(res)) {
            return res.entrySet().stream().collect(
                Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().getName()
                ));
        }
        return new HashMap<>();
    }

    @Override
    public void init(Set<Long> cateSet) {
        //1. 遍历所有国际末级类目,并获取所有京东零售类目
        Set<String> targetStr = new HashSet<>(5000);
        StringBuilder errMsg = new StringBuilder();
        List<CategoryPO> list = categoryAtomicService.queryAllRetailCategory(cateSet);
        for(CategoryPO po : list){
            try {
                //2 遍历所有京东零售类目,根据类目获取类目下扩展属性,并取出中文名称
                Map<Integer, CategoryGroupAtt> categoryGroupAttByCatId = categoryRpcService.getCategoryGroupAttByCatId(po.getJdCatId().intValue());
                if(MapUtils.isEmpty(categoryGroupAttByCatId)){
                    errMsg.append(String.format("类目%s未能获取到中台扩展属性信息: %s \n",po.getJdCatId(), JSON.toJSONString(categoryGroupAttByCatId)));
                }
                //2.1 将当前类目下的扩展属性名称追加到待翻译列表中
                Set<String> res1 = categoryGroupAttByCatId.values().stream().map(CategoryGroupAtt::getName).collect(Collectors.toSet());
                log.info("ExtAttributeLangServiceImpl.init attr  cateId :{} , res:{}" , po.getJdCatId() ,res1 );
                addIfNotExist(targetStr,res1);
                //2.2 将当前类目下所有扩展属性对应的扩展属性值都查出置入待翻译列表中
                Set<Long> attrIds = categoryGroupAttByCatId.values().stream().filter(Objects::nonNull).map(categoryGroupAtt -> Long.valueOf(categoryGroupAtt.getId().toString())).collect(Collectors.toSet());
                //3 遍历2下所有扩展属性的扩展属性值,并取出中文名称
                Map<Long, List<CategoryGroupAttValue>> attrVoMap = categoryRpcService.queryCategoryGroupAttValueByAttMapId(attrIds);
                if(MapUtils.isEmpty(attrVoMap)){
                    errMsg.append(String.format("扩展属性Id%s未能获取到中台扩展属性值信息: %s \n",attrIds, JSON.toJSONString(attrVoMap)));
                }
                Set<String> res2 = attrVoMap.values().stream().flatMap(List::stream).map(CategoryGroupAttValue::getName).collect(Collectors.toSet());
                log.info("ExtAttributeLangServiceImpl.init attrVal  cateId :{} , res:{}" , po.getJdCatId() ,res2 );
                addIfNotExist(targetStr,res2);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        log.info("===============ExtAttributeLangMapServiceImpl.init 中文名称获取完毕,开始执行翻译{}",targetStr.size());
        //4 将所有中文名称机翻成多国语言并落库
        List<List<String>> toTranslateList = Lists.partition(new ArrayList<>(targetStr), 30);
        int n = 0;
        for(List<String> cnName : toTranslateList){
            log.info("ExtAttributeLangMapServiceImpl.init 当前处理第{}波 ,共{}波" ,  n++, toTranslateList.size());
            //每批次翻译落库30个扩展属性
            doInitTranslateAndSave(cnName);
        }
        log.info("ExtAttributeLangMapServiceImpl.init err {}" ,  errMsg);
    }

//    /** 词条多语言初始化并落库*/
//    private void doInitTranslateAndSave(Collection<String> input){
//        log.info("ExtAttributeLangServiceImpl.doInitTranslateAndSave start size: {} req:{} " ,  input.size(), JSON.toJSONString(input));
//        List<String> langList = langManageService.getLangCodeList();
//        //中文关键字本身不需要翻译,从待翻译语种列表中移除
//        langList.remove(LangConstant.LANG_ZH);
//        if(CollectionUtils.isEmpty(input)){
//            return;
//        }
//
//        List<ExtAttributeLangPO> toSave = new ArrayList<>();
//
//        //todo 外部方法负责过滤拼参,内部方法负责事务(不存在才落库)
//        for(String keyword : input){
//            //不包含中文的翻译请求直接构造原输入结果
//            if(com.jdi.isc.product.soa.common.util.StringUtils.isNotContainChineseStr(keyword)){
//                Map<String, String> noTranslateRes = Maps.newHashMapWithExpectedSize(12);
//                for(String lang : langList){
//                    noTranslateRes.put(lang,keyword);
//                }
//                toSave.addAll(buildPo(noTranslateRes));
//                continue;
//            }
//
//
//            if(extAttributeLangAtomicService.exist(new ExtAttributeLangPO(LangConstant.LANG_ZH,keyword))){
//                log.info("ExtAttributeLangServiceImpl.doInitTranslateAndSave 词条{}已存在,忽略翻译" , keyword);
//                continue;
//            }
//
//
//        }
//
//
//
//
//        try {
//            Map<String, Map<String, String>> result = new HashMap<>();
//
//
//
//
//            BatchMultiTranslateReqDTO in = new BatchMultiTranslateReqDTO();
//            Set<MultiTranslateReqDTO> item = new HashSet<>();
//            //不需要翻译的字符串
//            Map<String, Map<String, String>> noTranslateMap = new HashMap<>();
//            for(String target : input){
//                //库中已存在的词条不重复翻译
//                if(extAttributeLangAtomicService.exist(new ExtAttributeLangPO(LangConstant.LANG_ZH,target))){
//                    log.info("ExtAttributeLangServiceImpl.doInitTranslateAndSave 词条{}已存在,忽略翻译" , target);
//                    continue;
//                }
//                //不包含中文的翻译请求直接构造原输入结果
//                if(com.jdi.isc.product.soa.common.util.StringUtils.isNotContainChineseStr(target)){
//                    Map<String, String> noTranslateRes = Maps.newHashMapWithExpectedSize(12);
//                    for(String lang : langList){
//                        noTranslateRes.put(lang,target);
//                    }
//                    noTranslateMap.put(target,noTranslateRes);
//                }else {
//                    MultiTranslateReqDTO multiTranslateReqDTO = new MultiTranslateReqDTO();
//                    multiTranslateReqDTO.setText(target);
//                    multiTranslateReqDTO.setFrom(LangConstant.LANG_ZH);
//                    multiTranslateReqDTO.setToLangList(langList);
//                    item.add(multiTranslateReqDTO);
//                }
//            }
//            if(CollectionUtils.isNotEmpty(item)){
//                //以每10个批次处理
//                List<List<MultiTranslateReqDTO>> subList = Lists.partition(new ArrayList<>(item), 10);
//                for(List<MultiTranslateReqDTO> req : subList){
//                    in.setMultiTranslateReqDTOSet(new HashSet<>(req));
//                    DataResponse<Map<String, Map<String, String>>> res = textTranslateManageService.batchTranslateMultiLangText(in);
//                    if(res.getSuccess() && !MapUtils.isEmpty(res.getData())){
//                        result.putAll(res.getData());
//                    }
//                }
//            }
//            result.putAll(noTranslateMap);
//            //补回中文名称键值
//            for (Map.Entry<String, Map<String, String>> entry : result.entrySet()) {
//                entry.getValue().put(LangConstant.LANG_ZH,entry.getKey());
//            }
//            //将翻译结果转化为扩展属性映射表
//            List<List<ExtAttributeLangPO>> pos = buildPo(result);
//            for(List<ExtAttributeLangPO> po : pos){
//                boolean saveRes = extAttributeLangAtomicService.saveBatch(po);
//                log.info("ExtAttributeLangServiceImpl.doInitTranslateAndSave use:{}  req:{} , res:{}" , (System.currentTimeMillis()-start), JSON.toJSONString(po), saveRes);
//            }
//        }catch (Exception e){
//            log.error("ExtAttributeLangServiceImpl.doInitTranslateAndSave req:{} " ,  input, e);
//            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_COMMON_WARNING,"ExtAttributeLangMapServiceImpl.init invoke error");
//        }
//    }

    @PFTracing
    private void doInitTranslateAndSave(Collection<String> input){
        log.info("ExtAttributeLangServiceImpl.doInitTranslateAndSave start size: {} req:{} " ,  input.size(), JSON.toJSONString(input));
        long start = System.currentTimeMillis();
        try {
            Map<String, Map<String, String>> result = new HashMap<>();
            if(CollectionUtils.isEmpty(input)){
                return;
            }
            List<String> langList = langManageService.getLangCodeList();
            //中文本身不需要翻译
            langList.remove(LangConstant.LANG_ZH);
            BatchMultiTranslateReqDTO in = new BatchMultiTranslateReqDTO();
            Set<MultiTranslateReqDTO> item = new HashSet<>();
            //不需要翻译的字符串
            Map<String, Map<String, String>> noTranslateMap = new HashMap<>();
            for(String target : input){
                //库中已存在的词条不重复翻译
                if(extAttributeLangAtomicService.exist(new ExtAttributeLangPO(LangConstant.LANG_ZH,target))){
                    log.info("ExtAttributeLangServiceImpl.doInitTranslateAndSave 词条{}已存在,忽略翻译" , target);
                    continue;
                }
                //不包含中文的翻译请求直接构造原输入结果
                if(com.jdi.isc.product.soa.common.util.StringUtils.isNotContainChineseStr(target)){
                    Map<String, String> noTranslateRes = Maps.newHashMapWithExpectedSize(12);
                    for(String lang : langList){
                        noTranslateRes.put(lang,target);
                    }
                    noTranslateMap.put(target,noTranslateRes);
                }else {
                    MultiTranslateReqDTO multiTranslateReqDTO = new MultiTranslateReqDTO();
                    multiTranslateReqDTO.setText(target);
                    multiTranslateReqDTO.setFrom(LangConstant.LANG_ZH);
                    multiTranslateReqDTO.setToLangList(langList);
                    item.add(multiTranslateReqDTO);
                }
            }
            if(CollectionUtils.isNotEmpty(item)){
                //以每10个批次处理
                List<List<MultiTranslateReqDTO>> subList = Lists.partition(new ArrayList<>(item), 10);
                for(List<MultiTranslateReqDTO> req : subList){
                    in.setMultiTranslateReqDTOSet(new HashSet<>(req));
                    DataResponse<Map<String, Map<String, String>>> res = textTranslateManageService.batchTranslateMultiLangText(in);
                    if(res.getSuccess() && !MapUtils.isEmpty(res.getData())){
                        result.putAll(res.getData());
                    }
                }
            }
            result.putAll(noTranslateMap);
            //补回中文名称键值
            for (Map.Entry<String, Map<String, String>> entry : result.entrySet()) {
                entry.getValue().put(LangConstant.LANG_ZH,entry.getKey());
            }
            //将翻译结果转化为扩展属性映射表
            List<List<ExtAttributeLangPO>> pos = buildPo(result);
            for(List<ExtAttributeLangPO> po : pos){
                boolean saveRes = extAttributeLangAtomicService.saveBatch(po);
                log.info("ExtAttributeLangServiceImpl.doInitTranslateAndSave use:{}  req:{} , res:{}" , (System.currentTimeMillis()-start), JSON.toJSONString(po), saveRes);
            }
        }catch (Exception e){
            log.error("ExtAttributeLangServiceImpl.doInitTranslateAndSave req:{} " ,  input, e);
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_COMMON_WARNING,"ExtAttributeLangMapServiceImpl.init invoke error");
        }
    }

    /** 将多语言翻译实体转为PO实体*/
    @PFTracing
    private List<List<ExtAttributeLangPO>> buildPo(Map<String, Map<String, String>> multiTranslateRes) {
        List<ExtAttributeLangPO> target = new ArrayList<>();
        log.info("ExtAttributeLangMapServiceImpl.buildPo resSize:{}" ,  multiTranslateRes.values().size());
        //外层循环每次执行一个扩展属性关键字
        for (Map.Entry<String, Map<String, String>> entry : multiTranslateRes.entrySet()) {
            Date now = new Date();
            //内层循环每次执行一个关键字的一个语种
            String groupId = UUID.randomUUID().toString();
            for (Map.Entry<String, String> langEntry : entry.getValue().entrySet()) {
                ExtAttributeLangPO item = new ExtAttributeLangPO();
                item.setGroupId(groupId);
                item.setLang(langEntry.getKey());
                item.setExtAttributeName(langEntry.getValue());
                item.setCreateTime(now);
                item.setUpdateTime(now);
                item.setCreator(Constant.SYSTEM);
                item.setUpdater(Constant.SYSTEM);
                target.add(item);
            }
        }
        log.info("ExtAttributeLangServiceImpl.buildPo req:{} , res:{}" , JSON.toJSONString(multiTranslateRes) ,JSON.toJSONString(target));
        return Lists.partition(target,100);
    }

//    private List<ExtAttributeLangPO> buildPo(Map<String, String> multiTranslateRes) {
//        List<ExtAttributeLangPO> target = new ArrayList<>();
//        Date now = new Date();
//        String groupId = UUID.randomUUID().toString();
//        for (Map.Entry<String, String> langEntry : multiTranslateRes.entrySet()) {
//            ExtAttributeLangPO item = new ExtAttributeLangPO();
//            item.setGroupId(groupId);
//            item.setLang(langEntry.getKey());
//            item.setExtAttributeName(langEntry.getValue());
//            item.setCreateTime(now);
//            item.setUpdateTime(now);
//            item.setCreator(Constant.SYSTEM);
//            item.setUpdater(Constant.SYSTEM);
//            target.add(item);
//        }
//        log.info("ExtAttributeLangServiceImpl.buildPo req:{} , res:{}" , JSON.toJSONString(multiTranslateRes) ,JSON.toJSONString(target));
//        return target;
//    }

    /** 已经存在的关键词，不重复处理*/
    private void addIfNotExist(Set<String> result,Set<String> toCheck){
        //查看哪些扩展属性关键字在多语言库里已存在
        List<ExtAttributeLangPO> existPo = extAttributeLangAtomicService.batchExist(LangConstant.LANG_ZH, toCheck);
        if(CollectionUtils.isEmpty(existPo)){
            result.addAll(toCheck);
            return;
        }
        Set<String> existKey = existPo.stream().map(ExtAttributeLangPO::getExtAttributeName).collect(Collectors.toSet());
        Collection<String> notExist = CollectionUtils.subtract(toCheck, existKey);
        log.info("ExtAttributeLangMapServiceImpl.addIfNotExist 已存在关键字不重复翻译:{} , 不存在的关键字:{}" ,  existKey, notExist);
        if(CollectionUtils.isNotEmpty(notExist)){
            result.addAll(notExist);
        }
    }

    @Override
    public void removeDirtyKeyword() {
        List<ExtAttributeTempPO> lackList = extAttributeLangAtomicService.listLackItem();
        log.info("ExtAttributeLangServiceImpl.initExtNewLang req:{}" , lackList.size());
        for(ExtAttributeTempPO item : lackList){
            extAttributeLangAtomicService.deleteByGroupId(item.getGroupId());
            log.info("ExtAttributeLangServiceImpl.removeDirtyKeyword groupId:{} 数据已移除 " ,  item.getGroupId() );
        }
    }

    @Override
    public void removeDul(Boolean delFlag) {
        //重复项
        List<ExtAttributeTempPO> item = extAttributeLangAtomicService.getDulItem();
        log.info("ExtAttributeLangServiceImpl.removeDul 总计:{}个词条" , item.size() );
        Integer dulCnt = 0;
        for(ExtAttributeTempPO po : item){
            List<ExtAttributeLangPO> dulItem = extAttributeLangAtomicService.list(new ExtAttributeLangPO(LangConstant.LANG_ZH,po.getExtAttributeName()));
            if(CollectionUtils.isNotEmpty(dulItem) && dulItem.size()>1){
                dulCnt++;
                //保留第一个不删除
                dulItem.remove(0);
                for(ExtAttributeLangPO removeTarget : dulItem){
                    if(delFlag){
                        boolean res = extAttributeLangAtomicService.deleteByGroupId(removeTarget.getGroupId());
                        log.info("ExtAttributeLangServiceImpl.removeDul groupId:{} , res:{}, 已处理:{}" ,  removeTarget.getGroupId(), res,dulCnt);
                    }else {
                        log.info("模拟移除ExtAttributeLangServiceImpl.removeDul groupId:{} " ,  removeTarget.getGroupId() );
                    }
                }
            }
        }
        log.info("ExtAttributeLangServiceImpl.removeDul 总计:{}个词条 ,其中重复词条:{}个" , item.size() , dulCnt);
    }

    public static void main(String[] args) {
        List<ExtAttributeLangPO> dulItem = new ArrayList<>();
        ExtAttributeLangPO a1 = new ExtAttributeLangPO();
        ExtAttributeLangPO a2 = new ExtAttributeLangPO();
        a1.setExtAttributeName("ces1");
        a2.setExtAttributeName("ces2");
        dulItem.add(a1);
        dulItem.add(a2);

        System.out.println(JSON.toJSONString(dulItem));
        dulItem.remove(0);
        System.out.println(JSON.toJSONString(dulItem));
    }

    public static boolean isNotContainChineseStr(String str) {
        if (str == null || str.isEmpty()) return true;
        String regex = "[\\u4E00-\\u9FFF]";
        return !Pattern.compile(regex).matcher(str).find();
    }

}
