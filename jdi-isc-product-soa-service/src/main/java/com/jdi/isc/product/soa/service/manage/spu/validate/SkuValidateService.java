package com.jdi.isc.product.soa.service.manage.spu.validate;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.AtrributeValueInputCheckTypeEnum;
import com.jdi.isc.product.soa.api.common.enums.PurchaseModelTypeEnum;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.constants.msgCode.StockMessageEnum;
import com.jdi.isc.product.soa.common.enums.AttributeCheckTypeEnum;
import com.jdi.isc.product.soa.common.enums.AttributeDimensionEnum;
import com.jdi.isc.product.soa.common.enums.AttributeInputTypeEnum;
import com.jdi.isc.product.soa.common.enums.SaleAttributeTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.BizException;
import com.jdi.isc.product.soa.common.frame.SaleAttributeUNhandleContextHolder;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.common.util.NumberUtils;
import com.jdi.isc.product.soa.common.util.StockNumUtils;
import com.jdi.isc.product.soa.domain.category.biz.GlobalQualificationVO;
import com.jdi.isc.product.soa.domain.common.biz.BaseLangVO;
import com.jdi.isc.product.soa.domain.enums.sku.SkuWeightSourceEnum;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.gms.req.JdProductQueryDTO;
import com.jdi.isc.product.soa.domain.lang.biz.LangVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeValueLangVO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributePO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SkuSaleAttributeValueRelationPO;
import com.jdi.isc.product.soa.domain.sku.biz.GroupSkuCertificateVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuCertificateVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuStockRelationVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.GroupPropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuVO;
import com.jdi.isc.product.soa.domain.stock.vo.SkuStockReqVO;
import com.jdi.isc.product.soa.domain.stock.vo.SkuStockVO;
import com.jdi.isc.product.soa.domain.warehouse.biz.WarehouseResVO;
import com.jdi.isc.product.soa.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SaleAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SkuSaleAttributeValueRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuCertificateAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuUniqueAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.manage.attribute.AttributeOutService;
import com.jdi.isc.product.soa.service.manage.category.GlobalAttributeManageService;
import com.jdi.isc.product.soa.service.manage.category.GlobalQualificationManageService;
import com.jdi.isc.product.soa.service.manage.country.CountryLangManageService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.price.SkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeLangManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuCrossBorderSaleStatusReadManageService;
import com.jdi.isc.product.soa.service.manage.stock.SkuStockManageService;
import com.jdi.isc.product.soa.service.manage.warehouse.WarehouseManageService;
import com.jdi.isc.product.soa.service.mapstruct.SkuConvertService;
import com.jdi.isc.product.soa.service.support.AssertValidation;
import com.jdi.isc.product.soa.service.support.DataResponseMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.Constant.MAX_PRICE;
import static com.jdi.isc.product.soa.common.constants.DataResponseCodeConstant.*;


/**
 * <AUTHOR>
 * @date 2023/12/16
 **/
@Slf4j
@Service
public class SkuValidateService {

    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private SkuConvertService skuConvertService;

    @Resource
    private DataResponseMessageService dataResponseMessageService;

    @Resource
    private SkuPriceManageService skuPriceManageService;

    @Resource
    private SkuUniqueAtomicService skuUniqueAtomicService;

    @Resource
    private GlobalAttributeManageService globalAttributeManageService;

    @Resource
    private GlobalQualificationManageService globalQualificationManageService;

    @Resource
    private SkuCertificateAtomicService skuCertificateAtomicService;

    @Autowired
    private SkuCrossBorderSaleStatusReadManageService skuCrossBorderSaleStatusReadManageService;

    @Resource
    private SpuValidateService spuValidateService;

    @Resource
    private CountryLangManageService countryLangManageService;

    @Resource
    private AttributeOutService attributeOutService;

    @Resource
    private WarehouseManageService warehouseManageService;

    @Resource
    private SkuStockManageService skuStockManageService;

    @Resource
    private SkuDraftAtomicService skuDraftAtomicService;

    @Autowired
    private SaleAttributeAtomicService saleAttributeAtomicService;

    @Resource
    private SaleAttributeLangManageService saleAttributeLangManageService;

    @Resource
    private SkuInfoRpcService skuInfoRpcService;

    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private SkuSaleAttributeValueRelationAtomicService skuSaleAttributeValueRelationAtomicService;

    @LafValue("jdi.isc.product.maxNegativeProfit")
    private String productMaxNegativeProfit;

    @LafValue("jdi.isc.jdSku.single")
    private Boolean jdSkuSingle;

    @LafValue("jdi.isc.jdSku.maxSkuAmount")
    private Integer maxSkuAmount;

    @LafValue("jdi.isc.jdSku.checkJdSkuIdCatId")
    private Boolean checkJdSkuIdCatId;

    private static final Pattern DECIMAL_PATTERN = Pattern.compile("^\\d{1,10}(\\.\\d{1,2})?$");

    private static final String JD_SKU_ID_LIKE_STR = "jdSkuId\":";

    public void validateCreateSkuVoList(List<SkuVO> skuVOList) {
        // 国内sku是否重复
        this.validateRepeatJdSku(skuVOList);
        // 跨境品校验国内SKU ID是否存在
        validateJdSkuIdExistedBySkuVoList(skuVOList);

        // 销售属性校验
//        Set<String> saleAttributeSet = Sets.newHashSet();
        for (SkuVO vo : skuVOList) {
            AssertValidation.isTrue(Objects.nonNull(vo.getSkuId()) && vo.getSkuId() < 80000000000L, SPU_NUMBER_WRONG_ERROR, getMessage(SPU_NUMBER_WRONG_ERROR));
            // 销售属性组合
//            String saleAttributeStr = skuConvertService.saleAttributeFromSkuVo(vo);
//            AssertValidation.isTrue(saleAttributeSet.contains(saleAttributeStr), SKU_REPEAT_SALE_ATTRIBUTE_ERROR, getMessage(SKU_REPEAT_SALE_ATTRIBUTE_ERROR));
//            saleAttributeSet.add(saleAttributeStr);
        }
    }

    private void validateJdSkuIdExistedBySkuVoList(List<SkuVO> skuVOList) {
        // 跨境品校验国内SKU ID是否存在
        SkuVO skuVO = skuVOList.get(0);
        if (CountryConstant.COUNTRY_ZH.equals(skuVO.getSourceCountryCode())){
            Set<Long> jdSkuIds = skuVOList.stream().filter(Objects::nonNull).map(SkuVO::getJdSkuId).collect(Collectors.toSet());
            validateJdSkuExisted(jdSkuIds);
        }
    }

    public void validateUpdateSkuVOList(List<SkuVO> skuVOList, Long spuId, List<SkuPO> dbSkuPoList) {

        /**
         * 1、校验sku销售属性维度是否一致
         * 2、校验是否已经存在相同组合的sku，有报错
         * 3、已经删除的sku组合是否和保存值相同，有就恢复之前的skuId
         * 4、校验sku组合个数是否超长
         *
         * */
        // 国内sku是否重复
        this.validateRepeatJdSku(skuVOList);
        // 跨境品校验国内SKU ID是否存在
        validateJdSkuIdExistedBySkuVoList(skuVOList);
        // 查询已存储sku列表
        //List<SkuPO> dbSkuPoList = skuAtomicService.selectSkuPosBySpuId(spuId);

//        String saleAttribute = dbSkuPoList.get(0).getSaleAttribute();
//        // 销售组合维度
//        int length = saleAttribute.split(Constant.HASHTAG).length;
//
//        // 先校验sku是否存在、根据销售属性组合
//        Map<String, Long> dbSkuSaleAttributeMap = dbSkuPoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(SkuPO::getSaleAttribute, SkuPO::getSkuId));
//
        if(CollectionUtils.isEmpty(dbSkuPoList)){
            return;
        }

        Map<Long, SkuPO> skuMap = dbSkuPoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(SkuPO::getSkuId, Function.identity()));
//
//        // 页面的sku列表销售属性组合拼接后集合
//        Set<String> viewSaleAttributeSet = Sets.newHashSet();
//        List<Long> viewSkuIds = Lists.newArrayList();
        for (SkuVO skuVO : skuVOList) {
//            // 维度数对比，不能比已经存在的商品维度少
//            AssertValidation.isTrue(CollectionUtils.isEmpty(skuVO.getStoreSalePropertyList()) || skuVO.getStoreSalePropertyList().size() < length, SKU_SALE_ATTRIBUTE_DOWN_DIMENSION_ERROR, getMessage(SKU_SALE_ATTRIBUTE_DOWN_DIMENSION_ERROR));
//
//            String saleAttributeStr = skuConvertService.saleAttributeFromSkuVo(skuVO);
//            // 新增的sku有重复组合时校验
//            AssertValidation.isTrue(Objects.isNull(skuVO.getSkuId()) && viewSaleAttributeSet.contains(saleAttributeStr), SKU_REPEAT_SALE_ATTRIBUTE_ERROR, getMessage(SKU_REPEAT_SALE_ATTRIBUTE_ERROR));
//            viewSaleAttributeSet.add(saleAttributeStr);
//            skuVO.setSaleAttribute(saleAttributeStr);
//            // db中存在当前销售属性组合，恢复之前的skuId
//            if (Objects.isNull(skuVO.getSkuId()) && dbSkuSaleAttributeMap.containsKey(saleAttributeStr)) {
//                skuVO.setSkuId(dbSkuSaleAttributeMap.get(saleAttributeStr));
//                skuVO.setYn(YnEnum.YES.getCode());
//            }
//
//            if (Objects.nonNull(skuVO.getSkuId())) {
//                viewSkuIds.add(skuVO.getSkuId());
//            }

            // 通过 SKU ID 获取对应的 SkuPO 对象，并更新重量来源
            this.updateWeightSource(skuVO, skuMap);
        }
//        // 想要的删除的skuId列表
//        List<Long> deleteSkuIds = (List<Long>) CollectionUtils.subtract(dbSkuSaleAttributeMap.values(), viewSkuIds);
//        log.info("SkuValidateService.validateUpdateSkuVOList deleteSkuIds={}" + JSON.toJSONString(deleteSkuIds));
//        if (CollectionUtils.isNotEmpty(deleteSkuIds)) {
//            deleteSkuIds.forEach(id -> {
//                SkuVO skuVO = new SkuVO();
//                skuVO.setSkuId(id);
//                skuVO.setYn(YnEnum.NO.getCode());
//                skuVOList.add(skuVO);
//            });
//        }

    }

    /**
     * 更新 SKU 的重量来源
     * @param skuVO SKU 信息对象
     * @param skuMap SKU ID 到 SKU PO 的映射
     */
    private void updateWeightSource(SkuVO skuVO, Map<Long, SkuPO> skuMap) {
        Optional.ofNullable(skuVO.getSkuId())
                .map(skuMap::get)
                .filter(Objects::nonNull)
                .ifPresent(skuPO -> {
                    boolean isDifferent = !isEqual(skuPO.getLength(), skuVO.getLength()) ||
                            !isEqual(skuPO.getWidth(), skuVO.getWidth()) ||
                            !isEqual(skuPO.getHeight(), skuVO.getHeight()) ||
                            !isEqual(skuPO.getWeight(), skuVO.getWeight());
                    if (isDifferent) {
                        skuVO.setWeightSource(SkuWeightSourceEnum.SUPPLIER_INPUT.getCode());
                    }
                });
    }

    private boolean isEqual(BigDecimal bd, String str) {
        if (str == null) {
            return bd == null;
        }
        try {
            BigDecimal strBd = new BigDecimal(str);
            return bd != null && bd.compareTo(strBd) == 0;
        } catch (NumberFormatException e) {
            // 如果字符串不能转换为 BigDecimal，认为不相等
            return false;
        }
    }

    /**
     * 校验价格
     *
     * @param skuVoList         sku列表
     * @param sourceCountryCode 发货国
     */
    public void validateSkuPrice(List<SkuVO> skuVoList, String sourceCountryCode) {
        BigDecimal maxPrice = new BigDecimal(MAX_PRICE);
        for (SkuVO skuVO : skuVoList) {
            String purchasePriceStr = skuVO.getPurchasePrice();
            AssertValidation.isTrue(StringUtils.isBlank(purchasePriceStr), SKU_CREATE_ERROR, "采购价不可为空");

            BigDecimal purchasePrice = new BigDecimal(skuVO.getPurchasePrice());
            AssertValidation.isTrue(purchasePrice.compareTo(maxPrice) > 0, SKU_SALE_PRICE_MAX_ERROR, getMessage(SKU_SALE_PRICE_MAX_ERROR));

            if(StringUtils.isNotBlank(skuVO.getTaxPurchasePrice())){
                BigDecimal taxPurchasePrice = new BigDecimal(skuVO.getTaxPurchasePrice());
                AssertValidation.isTrue(taxPurchasePrice.compareTo(purchasePrice) < 0, "含税价不许小于未税价");
            }


//            if(skuVO.getSalePrice() == null){
//                log.error("SkuValidateService.validateSkuPrice salePrice is null");
//                continue;
//            }

//            BigDecimal salePrice = new BigDecimal(skuVO.getSalePrice());
//            AssertValidation.isTrue(salePrice.compareTo(maxPrice) > 0, SKU_PURCHASE_PRICE_MAX_ERROR, getMessage(SKU_PURCHASE_PRICE_MAX_ERROR));

//            boolean res = salePrice.subtract(purchasePrice).divide(salePrice, 4, RoundingMode.HALF_UP).compareTo(this.getCountryMaxNegativeProfit(sourceCountryCode)) >= 0;
//            if (!res) {
//                throw new BizException("超过阈值的负毛利价格,请检查.");
//            }
        }
    }

    /**
     * 本土品创建通用校验： 需要首先调用
     * 1、sku最多100个
     * 2、每个商品销售属性值个数不能大于2、不能少于1
     * 3、销售属性id去重后个数不大于2
     * 4、销售属性id必须不为空、且在数据库中必须存在、且一个图销一个文销
     * 5、销售属性与当前的类目id必须匹配
     *
     * @param spuVO spuVO
     * @param skuVOList skuVOList
     */
    private void firstCommonValidateSkuSaleAttributeForLocal(SpuVO spuVO, List<SkuVO> skuVOList) {
        // spu类目不能为空
        if (Objects.isNull(spuVO.getCatId())) {
            throw new BizException("spu类目不能为空，请检查");
        }
        if (CollectionUtils.isEmpty(skuVOList)) {
            throw new BizException("sku列表为空");
        }
        if (skuVOList.size() > maxSkuAmount) {
            throw new BizException("SKU数量超过" + maxSkuAmount + "，禁止新增，请拆分成多个SPU进行发布！");
        }
        Set<Long> attributeIdSet = Sets.newHashSet();
        for (SkuVO skuVO : skuVOList) {
            if (CollectionUtils.isEmpty(skuVO.getStoreSalePropertyList())) {
                throw new BizException("销售属性为空，请填写sku发品必填销售属性");
            }
            // 每个商品销售属性值个数不能大于2
            if (skuVO.getStoreSalePropertyList().size() > Constant.SALE_ATTIRBUTE_MAX_AMOUNT) {
                throw new BizException("商品销售属性值个数不能大于" + Constant.SALE_ATTIRBUTE_MAX_AMOUNT + "，请检查");
            }
            for (PropertyValueVO propertyValueVO : skuVO.getStoreSalePropertyList()) {
                if (Objects.isNull(propertyValueVO.getAttributeId())) {
                    throw new BizException("销售属性id不能为空，请检查");
                }
                attributeIdSet.add(propertyValueVO.getAttributeId());
            }
        }
        // 销售属性id去重后个数不大于2
        if (attributeIdSet.size() > Constant.SALE_ATTIRBUTE_MAX_AMOUNT) {
            throw new BizException("销售属性个数不能大于" + Constant.SALE_ATTIRBUTE_MAX_AMOUNT + "，请检查");
        }
        // 销售属性id在数据库中必须存在
        List<SaleAttributePO> attributePOList = saleAttributeAtomicService.getValidByIdList(new ArrayList<>(attributeIdSet));
        if (CollectionUtils.isEmpty(attributePOList)) {
            throw new BizException("销售属性id在数据库中不存在，请检查");
        }
        // 计算销售属性文销个数
        long textAttributeCount = attributePOList.stream().filter(attributePO -> attributePO.getSaleAttributeType() == SaleAttributeTypeEnum.TEXT.getCode()).count();
        // 销售属性文销个数不能大于1
        if (textAttributeCount > 1) {
            throw new BizException("销售属性文销个数不能大于1，请检查");
        }
        // 计算销售属性图销个数
        long imageAttributeCount = attributePOList.stream().filter(attributePO -> attributePO.getSaleAttributeType() == SaleAttributeTypeEnum.IMAGE.getCode()).count();
        // 销售属性图销个数不能大于1
        if (imageAttributeCount > 1) {
            throw new BizException("销售属性图销个数不能大于1，请检查");
        }
        // 销售属性所属类目id与当前的类目id必须匹配
        attributePOList.forEach(attributePO -> {
            if (!Objects.equals(attributePO.getJdCatId(), spuVO.getCatId())) {
                throw new BizException("销售属性与当前的类目id不匹配，请检查");
            }
        });
    }

    /**
     * 本土品创建通用校验：更新时候也有新建，所以对新建sku也用这个校验
     * 1、每个商品的多语言值必须包含中文
     * 2、每个商品的多语言值个数必须相同
     * 3、所有商品的销售属性value id必须为空
     *
     * @param skuVOList
     */
    private void secondCommonValidateSkuSaleAttributeForLocalCreate(List<SkuVO> skuVOList) {
        Set<Integer> langSizeSet = Sets.newHashSet();
        for (SkuVO skuVO : skuVOList) {
            skuVO.getStoreSalePropertyList().forEach(propertyValueVO -> {
                if (CollectionUtils.isEmpty(propertyValueVO.getLangList())) {
                    throw new BizException("销售属性多语言值为空，请填写sku发品必填销售属性多语言值");
                }
                // 每个商品的多语言值必须包含中文
                propertyValueVO.getLangList().stream().filter(baseLangVO -> StringUtils.isNotBlank(baseLangVO.getLangName())
                                && StringUtils.isNotBlank(baseLangVO.getLang()) && baseLangVO.getLang().equals(Constant.DEFAULT_LANG))
                        .findFirst().orElseThrow(() -> new BizException("sku发品销售属性中文值未填写"));
                // 每个商品的多语言值必须包含英文
                propertyValueVO.getLangList().stream().filter(baseLangVO -> StringUtils.isNotBlank(baseLangVO.getLangName())
                                && StringUtils.isNotBlank(baseLangVO.getLang()) && baseLangVO.getLang().equals(LangConstant.LANG_EN))
                        .findFirst().orElseThrow(() -> new BizException("sku发品销售属性英文值未填写"));
                // 所有商品的销售属性value id必须为空
                if (Objects.nonNull(propertyValueVO.getAttributeValueId())) {
                    throw new BizException("销售属性value id必须为空，请检查");
                }
                langSizeSet.add(propertyValueVO.getLangList().size());
            });
        }
        // 每个商品的多语言值个数必须相同，因为都是处理的新增部分数据，所以可以进行校验
        if (langSizeSet.size() > 1) {
            throw new BizException("请填写完整商品的多语言值，商品的多语言值个数必须相同，请检查");
        }
        // 每种个sku销售属性多语言，语言类型有且仅有一个，语言set个数与langlist个数相同，且每个sku的langlist语言类型必须相同，例如sku1是中英泰、sku2也必须是中英泰，所以langSet的总个数一定是三个
        for (SkuVO skuVO : skuVOList) {
            Set<String> allLangSet = Sets.newHashSet();
            Set<Integer> langSetSize = Sets.newHashSet();
            skuVO.getStoreSalePropertyList().forEach(propertyValueVO -> {
                Set<String> langSet = Sets.newHashSet();
                propertyValueVO.getLangList().forEach(baseLangVO -> {
                    if (StringUtils.isNotBlank(baseLangVO.getLang())) {
                        langSet.add(baseLangVO.getLang());
                        allLangSet.add(baseLangVO.getLang());
                    }
                });
                langSetSize.add(propertyValueVO.getLangList().size());
                if (langSet.size() != propertyValueVO.getLangList().size()) {
                    throw new BizException("销售属性多语言类型重复，请检查");
                }
            });
           if(langSetSize.size() !=1 || allLangSet.size() != langSetSize.iterator().next()){
               throw new BizException("销售属性多语言类型不一致，请检查");
           }
        }
    }

    private void secondCommonValidateSkuSaleAttributeForLocalCreateInUpdate(List<SkuVO> skuVOList) {
        Set<Integer> langSizeSet = Sets.newHashSet();
        for (SkuVO skuVO : skuVOList) {
            skuVO.getStoreSalePropertyList().forEach(propertyValueVO -> {
                if (propertyValueVO.getAttributeValueId() == null && CollectionUtils.isEmpty(propertyValueVO.getLangList())) {
                    throw new BizException("销售属性多语言值为空，请填写sku发品必填销售属性多语言值");
                }
                // 每个商品的多语言值必须包含中文
                propertyValueVO.getLangList().stream().filter(baseLangVO -> StringUtils.isNotBlank(baseLangVO.getLangName())
                                && StringUtils.isNotBlank(baseLangVO.getLang()) && baseLangVO.getLang().equals(Constant.DEFAULT_LANG))
                        .findFirst().orElseThrow(() -> new BizException("sku发品销售属性中文值未填写"));
                langSizeSet.add(propertyValueVO.getLangList().size());
            });
        }
        // 每个商品的多语言值个数必须相同，对于刷的数据，这个配置会导致修改品无法通过校验，所以需要暂时去掉
//        if (langSizeSet.size() > 1) {
//            throw new BizException("请填写完整商品的多语言值，商品的多语言值个数必须相同，请检查");
//        }
        // 每种个sku销售属性多语言，语言类型有且仅有一个，语言set个数与langlist个数相同
        for (SkuVO skuVO : skuVOList) {
            skuVO.getStoreSalePropertyList().forEach(propertyValueVO -> {
                Set<String> langSet = Sets.newHashSet();
                propertyValueVO.getLangList().forEach(baseLangVO -> {
                    if (StringUtils.isNotBlank(baseLangVO.getLang())) {
                        langSet.add(baseLangVO.getLang());
                    }
                });
                if (langSet.size() != propertyValueVO.getLangList().size()) {
                    throw new BizException("销售属性多语言类型重复，请检查");
                }
            });
        }
    }

    /**
     * 本土品新建sku销售属性校验
     *
     * @param spuVO spuVO
     * @param skuVOList skuVOList
     */
    public void validateCreateSkuSaleAttributeForLocal(SpuVO spuVO, List<SkuVO> skuVOList) {
        // 不处理销售属性
        if(SaleAttributeUNhandleContextHolder.get() !=null && SaleAttributeUNhandleContextHolder.get()){
            return;
        }
        // 规范sku的storeSalePropertyList, attributeValueId不为空的将langList置空，attributeValueId=默认id的将attributeValueId置空，并赋值默认langlist
        this.normlizeSkuSalePropertyList(skuVOList);
        this.firstCommonValidateSkuSaleAttributeForLocal(spuVO, skuVOList);
        this.secondCommonValidateSkuSaleAttributeForLocalCreate(skuVOList);
        // 校验销售属性值重复，如果不同sku有相同销售属性值，抛出异常
        this.saleValueDumplicateCheck(skuVOList);
    }

    /**
     * 1、规范sku的storeSalePropertyList, attributeValueId不为空的将langList置空
     * 2、兼容vc、wiop，设置属性值，attributeValueId=默认id的将attributeValueId置空，并赋值默认langlist
     *
     * @param skuVOList
     */
    private void normlizeSkuSalePropertyList(List<SkuVO> skuVOList) {
        if (CollectionUtils.isEmpty(skuVOList)) {
            return;
        }
        // 规范sku的storeSalePropertyList, attributeValueId不为空的将langList置空
        for (SkuVO skuVO : skuVOList) {
            skuVO.getStoreSalePropertyList().forEach(propertyValueVO -> {
                if (Objects.nonNull(propertyValueVO.getAttributeValueId())) {
                    propertyValueVO.setLangList(new ArrayList<>());
                }
            });
        }
        // 兼容vc、wiop，设置属性值，attributeValueId=默认id的将attributeValueId置空，并赋值默认langlist
        for (SkuVO skuVO : skuVOList) {
            skuVO.getStoreSalePropertyList().forEach(propertyValueVO -> {
                if (Objects.nonNull(propertyValueVO.getAttributeValueId()) && (Objects.equals(propertyValueVO.getAttributeValueId(), Constant.DEFAULT_IMAGE_SALE_ATTRIBUTE_VALUE_ID)
                        || Objects.equals(propertyValueVO.getAttributeValueId(), Constant.DEFAULT_TEXT_SALE_ATTRIBUTE_VALUE_ID))) {
                    propertyValueVO.setAttributeValueId(null);
                    propertyValueVO.setLangList(new ArrayList<>());
                    Constant.DEFAULT_SALE_ATTRIBUTE_VALUE_NAME_LANG_MAP.forEach((lang, langName) -> {
                        BaseLangVO baseLangVO = new BaseLangVO();
                        baseLangVO.setLang(lang);
                        baseLangVO.setLangName(langName);
                        propertyValueVO.getLangList().add(baseLangVO);
                    });
                }
            });
        }
    }

    /**
     * 本土品更新sku销售属性校验
     *
     * @param spuVO spuVO
     * @param skuVOList skuVOList
     */
    public void validateUpdateSkuSaleAttributeForLocal(SpuVO spuVO, List<SkuVO> skuVOList) {
        // 不处理销售属性
        if(SaleAttributeUNhandleContextHolder.get() !=null && SaleAttributeUNhandleContextHolder.get()){
            return;
        }

        // 规范sku的storeSalePropertyList, attributeValueId不为空的将langList置空，attributeValueId=默认id的将attributeValueId置空，并赋值默认langlist
        this.normlizeSkuSalePropertyList(skuVOList);

        this.firstCommonValidateSkuSaleAttributeForLocal(spuVO, skuVOList);

        if(spuVO.getSpuId() == null){
            throw new BizException("spuId为空，请检查");
        }

        // 深copy skuVOList
        List<SkuVO> skuVOListCopy = JSON.parseArray(JSON.toJSONString(skuVOList), SkuVO.class);

        // 查询当前spu下已有的sku和sku草稿，如果在skuVOList中任意的sku或sku草稿不存在，抛异常，提示词“spu下的sku列表不完成，请检查或尝试刷新页面后操作”
        List<SkuDraftPO> skuDraftPOListFromDB = skuDraftAtomicService.getBySpuId(spuVO.getSpuId());
        if (CollectionUtils.isNotEmpty(skuDraftPOListFromDB)) {
            skuDraftPOListFromDB.forEach(skuDraftPOFromDB -> {
                // 如果skuId不为空校验skuId
                if (Objects.nonNull(skuDraftPOFromDB.getSkuId())) {
                    if (!skuVOListCopy.stream().anyMatch(skuVO -> Objects.equals(skuVO.getSkuId(), skuDraftPOFromDB.getSkuId()))) {
                        log.error("SkuValidateService.validateUpdateSkuSaleAttributeForLocal check skuId skuVOListCopy:{}", JSON.toJSONString(skuVOListCopy));
                        throw new BizException("spu下的sku列表数据不完整，skuId在更新信息中不存在，请检查或尝试刷新页面后操作");
                    }
                }
            });
        }

        // 将createSkuVOList分成两组，一组是更新销售属性关系的sku（所有销售属性值都有id（值id的个数与销售属性值的个数相等）），一组是新增sku（如果sku的销售属性值中存在没有值id的销售属性值，则认为是新增）
        List<SkuVO> updateSkuVOList = new ArrayList<>();
        List<SkuVO> createSkuVOList = new ArrayList<>();
        for (SkuVO skuVO : skuVOListCopy) {
            int valudIdCount = 0;
            if (CollectionUtils.isNotEmpty(skuVO.getStoreSalePropertyList())) {
                for (PropertyValueVO propertyValueVO : skuVO.getStoreSalePropertyList()) {
                    if (propertyValueVO.getAttributeValueId() != null) {
                        valudIdCount++;
                    }
                }
                if (valudIdCount == skuVO.getStoreSalePropertyList().size()) {
                    // 如果sku的所有销售属性值都有id（值id的个数与销售属性值的个数相等）, 则认为需要更新销售属性关系的skuKey
                    updateSkuVOList.add(skuVO);
                } else {
                    // 如果sku的销售属性值中存在没有值id的销售属性值，则认为是新增
                    createSkuVOList.add(skuVO);
                }
            } else {
                throw new BizException("销售属性值为空，请检查");
            }
        }

        // 对updateSkuVOList进行更新校验，同时对销售属性多语言进行赋值
        // 更新品：传递过来的销售属性值id必须存在，首先收集所有销售属性值id
        Set<Long> attributeValueIdSet = Sets.newHashSet();
        for (SkuVO skuVO : updateSkuVOList) {
            skuVO.getStoreSalePropertyList().forEach(propertyValueVO -> {
                if (Objects.nonNull(propertyValueVO.getAttributeValueId())) {
                    attributeValueIdSet.add(propertyValueVO.getAttributeValueId());
                }
            });
        }
        // 根据id查询销售属性值
        Map<Long, List<SaleAttributeValueLangVO>> saleAttributeValueLangMap = saleAttributeLangManageService.getSaleAttributeValueNameLangsByIds(new ArrayList<>(attributeValueIdSet));
        // 根据销售属性值id获取销售属性值多语言，赋值给langlist
        Map<Long, List<BaseLangVO>> langListMap = Maps.newHashMap();
        for (SkuVO skuVO : updateSkuVOList) {
            skuVO.getStoreSalePropertyList().forEach(propertyValueVO -> {
                if (Objects.nonNull(propertyValueVO.getAttributeValueId())) {
                    List<SaleAttributeValueLangVO> saleAttributeValueLangList = saleAttributeValueLangMap.get(propertyValueVO.getAttributeValueId());
                    if (CollectionUtils.isNotEmpty(saleAttributeValueLangList)) {
                        // 将saleAttributeValueLangList转换为BaseLangVO
                        List<BaseLangVO> baseLangVOList = saleAttributeValueLangList.stream().map(saleAttributeValueLangVO -> {
                            BaseLangVO baseLangVO = new BaseLangVO();
                            baseLangVO.setLang(saleAttributeValueLangVO.getLang());
                            baseLangVO.setLangName(saleAttributeValueLangVO.getLangName());
                            return baseLangVO;
                        }).collect(Collectors.toList());
                        propertyValueVO.setLangList(baseLangVOList);
                        langListMap.put(propertyValueVO.getAttributeValueId(), baseLangVOList);
                    } else {
                        throw new BizException("销售属性值id不存在，请检查");
                    }
                }
            });
        }

        // 对createSkuVOList存在属性值id的langlist进行赋值，以便进行销售属性重复性校验，已有的langlist从langListMap中获取
        // 校验销售属性值重复，如果不同sku有相同销售属性值，抛出异常，此处需要对所有sku的销售属性进行赋值，然后进行校验updateSkuVOList中销售属性值id相同的获取，如果获取不到抛异常，证明这不是一个原有的销售属性值id
        // 获取新增品的完全新增销售属性langList
        List<BaseLangVO> newSaleAttributeValueLangList = new ArrayList<>();
        for (SkuVO skuVO : createSkuVOList) {
            for (PropertyValueVO propertyValueVO : skuVO.getStoreSalePropertyList()) {
                if (Objects.nonNull(propertyValueVO.getAttributeValueId())) {
                    List<BaseLangVO> baseLangVOList = langListMap.get(propertyValueVO.getAttributeValueId());
                    if (CollectionUtils.isNotEmpty(baseLangVOList)) {
                        propertyValueVO.setLangList(baseLangVOList);
                    } else {
                        throw new BizException("销售属性值id不存在，请检查");
                    }
                } else {
                    if (CollectionUtils.isNotEmpty(propertyValueVO.getLangList())) {
                        newSaleAttributeValueLangList = propertyValueVO.getLangList();
                    }
                }
            }
        }

        // 对createSkuVOList进行校验，也就是更新中的新建品，因为都是新建品，所以可以进行create校验
        this.secondCommonValidateSkuSaleAttributeForLocalCreateInUpdate(createSkuVOList);

        final List<BaseLangVO> newSaleAttributeValueLangList1 = newSaleAttributeValueLangList;
        // 获取createSkuVOList中的第一个langList的语言类型，并根据这些语言对updateSkuVOList进行赋值
        // 删除多余的langList值，值保留与newSaleAttributeValueLangList相同的lang值
        for (SkuVO skuVO : skuVOListCopy) {
            for (PropertyValueVO propertyValueVO : skuVO.getStoreSalePropertyList()) {
                if (CollectionUtils.isNotEmpty(propertyValueVO.getLangList())) {
                    propertyValueVO.setLangList(propertyValueVO.getLangList().stream().filter(baseLangVO -> newSaleAttributeValueLangList1.stream().filter(e -> e != null && StringUtils.isNotBlank(e.getLang())).anyMatch(baseLangVO1 -> baseLangVO1.getLang().equals(baseLangVO.getLang())))
                            .collect(Collectors.toList()));
                }
            }
        }
        log.info("validateUpdateSkuSaleAttributeForLocal skuVOListCopy:{}", JSON.toJSONString(skuVOListCopy));
        this.saleValueDumplicateCheck(skuVOListCopy);
    }

    /**
     * 校验销售属性值重复
     *
     * @param skuVOList
     */
    private void saleValueDumplicateCheck(List<SkuVO> skuVOList) {
        Set<String> langSet = Sets.newHashSet();
        Map<String, List<List<String>>> langNameListMap = Maps.newHashMap(); // ZH语言: 所有sku对应语言的所有销售属性值名称list的list
        // 所有sku的图销+文销任意语言的名字相加不能重复，且不能为空，逐个语言处理，判断标准是集合大小与skuVOList.size()相同，名字相加前需要用相同规则排序
        for (SkuVO skuVO : skuVOList) {
            List<PropertyValueVO> storeSalePropertyList = skuVO.getStoreSalePropertyList();
            Map<String, List<String>> langNameMap = Maps.newHashMap();  // ZH语言: 当前sku对应语言的所有销售属性值名称list
            for (PropertyValueVO propertyValueVO : storeSalePropertyList) {
                List<BaseLangVO> langList = propertyValueVO.getLangList();
                for (BaseLangVO baseLangVO : langList) {
                    if (StringUtils.isNotBlank(baseLangVO.getLangName()) && StringUtils.isNotBlank(baseLangVO.getLang())) {
                        if(langNameMap.get(baseLangVO.getLang().trim()) == null){
                            langNameMap.put(baseLangVO.getLang().trim(), new ArrayList<>());
                        }
                        langNameMap.get(baseLangVO.getLang().trim()).add(baseLangVO.getLangName().trim());
                        langSet.add(baseLangVO.getLang().trim());
                    }
                }
            }
            for (String lang : langSet) {
                if(langNameListMap.get(lang) == null){
                    langNameListMap.put(lang, new ArrayList<>());
                }
                langNameListMap.get(lang).add(langNameMap.get(lang));
            }
        }
        Map<String, Set<String>> langNameSetMap = Maps.newHashMap();
        for (String lang : langSet) {
            langNameSetMap.put(lang, new HashSet<>());
        }

        log.info("saleValueDumplicateCheck langNameListMap:{}", JSON.toJSONString(langNameListMap));
        langNameListMap.forEach((lang, langNameList) -> {
            langNameList.forEach(langNameList1 -> {
                if (CollectionUtils.isNotEmpty(langNameList1)) {
                    // 用相同规则排序
                    langNameList1.sort(Comparator.naturalOrder());
                    String langName = String.join("/", langNameList1);
                    langNameSetMap.get(lang).add(langName);
                } else {
                    langNameSetMap.get(lang).add("");
                }
            });
        });
        log.info("saleValueDumplicateCheck langNameSetMap:{}", JSON.toJSONString(langNameSetMap));

        langNameSetMap.forEach((lang, langNameSet) -> {
            if(langNameSet.size() != skuVOList.size()){
                throw new BizException("不同sku有相同销售属性值，请检查");
            }
        });
    }

    /**
     * 跨境品销售属性通用校验
     * 1、jdskuid所在类目id与当前类目必须一致
     * 2、sku数量必须为1
     *
     * @param skuVOList
     */
    private void commonValidateSkuSaleAttributeForCrossBorder(SpuVO spuVO, List<SkuVO> skuVOList, boolean check) {

        if (CollectionUtils.isEmpty(skuVOList)) {
            throw new BizException("跨境品sku不能为空");
        }

        if (Objects.isNull(spuVO.getCatId())) {
            throw new BizException("spu类目不能为空");
        }

        // 1、sku数量必须为1
        if (skuVOList.size() > Constant.MAX_CROSS_BOARDER_SKU_NUM_PER_SPU) {
            throw new BizException("跨境品sku数量不能大于" + Constant.MAX_CROSS_BOARDER_SKU_NUM_PER_SPU);
        }

        // 2、jdSkuId所在类目id与当前类目必须一致
        for (SkuVO skuVO : skuVOList) {
            if (Objects.nonNull(skuVO.getJdSkuId())) {
                Long jdSkuId = skuVO.getJdSkuId();
                if (Objects.isNull(jdSkuId)) {
                    throw new BizException("jdSkuId不能为空");
                }
                if(!check){
                    log.info("commonValidateSkuSaleAttributeForCrossBorder 不校验类目, spuId: {}, jdSkuId: {}", spuVO.getSpuId(), jdSkuId);
                    continue;
                }
                JdProductDTO jdProductDTO = skuInfoRpcService.getSkuById(jdSkuId);
                if (Objects.isNull(jdProductDTO)) {
                    throw new BizException("jdSkuId不存在，请检查");
                }
                Long jdCatId = jdProductDTO.getUnLimitCid() != null ? Long.valueOf(jdProductDTO.getUnLimitCid()) : null;
                if (!Objects.equals(jdCatId, spuVO.getCatId())) {
                    throw new BizException("jdSkuId所在类目id与当前spu类目不一致，请检查");
                }
            }
        }

    }
    /**
     * 跨境品新建sku销售属性校验
     *
     * @param spuVO spuVO
     * @param skuVOList skuVOList
     */
    public void validateCreateSkuSaleAttributeForCrossBorder(SpuVO spuVO, List<SkuVO> skuVOList) {
        // 不处理销售属性
        if(SaleAttributeUNhandleContextHolder.get() !=null && SaleAttributeUNhandleContextHolder.get()){
            return;
        }
       this.commonValidateSkuSaleAttributeForCrossBorder(spuVO, skuVOList, true);
    }

    /**
     * 跨境品更新多语言 sku销售属性校验
     *
     * @param spuVO spuVO
     * @param skuVOList skuVOList
     */
    public void validateUpdateSkuSaleAttributeValueLangNameForCrossBorder(SpuVO spuVO, List<SkuVO> skuVOList) {
        // 不处理销售属性
        if(SaleAttributeUNhandleContextHolder.get() !=null && SaleAttributeUNhandleContextHolder.get()){
            return;
        }
        if (Objects.isNull(spuVO.getSpuId())) {
            throw new BizException("spuId不能为空");
        }
        for (SkuVO skuVO : skuVOList) {
            if (Objects.isNull(skuVO.getSkuId())) {
                throw new BizException("skuId不能为空");
            }
        }

        this.commonValidateSkuSaleAttributeForCrossBorder(spuVO, skuVOList, checkJdSkuIdCatId);

        // 更新多语言，当前的属性值id必须是当前sku的，且不能多也不能少
        for (SkuVO skuVO : skuVOList) {
            List<SkuSaleAttributeValueRelationPO> skuRelations = skuSaleAttributeValueRelationAtomicService.getValidBySkuId(skuVO.getSkuId());
            if (CollectionUtils.isEmpty(skuRelations)) {
                throw new BizException("sku原销售属性值关系不存在，请检查");
            }
            Set<Long> attributeValueIdSet = skuRelations.stream().map(SkuSaleAttributeValueRelationPO::getSaleAttributeValueId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(attributeValueIdSet)) {
                throw new BizException("sku原销售属性值不存在，请检查");
            }
            List<PropertyValueVO> storeSalePropertyList = skuVO.getStoreSalePropertyList();
            for (PropertyValueVO propertyValueVO : storeSalePropertyList) {
                if (Objects.nonNull(propertyValueVO.getAttributeValueId()) && !attributeValueIdSet.contains(propertyValueVO.getAttributeValueId())) {
                    throw new BizException("传入的销售属性值id不是当前sku的，不匹配的id为：" + propertyValueVO.getAttributeValueId());
                }
            }
            // 界面未必显示所有语言，所以这个校验是不对的
//            if (attributeValueIdSet.size() != storeSalePropertyList.size()) {
//                throw new BizException("传入的销售属性值数量与当前sku的属性值数量不一致");
//            }
        }
    }

    /**
     * 跨境品更新jdSkuId  sku销售属性校验
     *
     * @param spuVO spuVO
     * @param skuVOList skuVOList
     */
    public void validateUpdateSkuSaleAttributeJdSkuIdForCrossBorder(SpuVO spuVO, List<SkuVO> skuVOList) {
        // 不处理销售属性
        if(SaleAttributeUNhandleContextHolder.get() !=null && SaleAttributeUNhandleContextHolder.get()){
            return;
        }
        if (Objects.isNull(spuVO.getSpuId())) {
            throw new BizException("spuId不能为空");
        }
        for (SkuVO skuVO : skuVOList) {
            if (Objects.isNull(skuVO.getSkuId())) {
                throw new BizException("skuId不能为空");
            }
        }
        this.commonValidateSkuSaleAttributeForCrossBorder(spuVO, skuVOList, checkJdSkuIdCatId);
    }

    /**
     * 校验长宽高重
     * @param skuVoList         sku列表
     */
    public void validateFourDimension(List<SkuVO> skuVoList) {
        for (SkuVO skuVO : skuVoList) {
            if (StringUtils.isBlank(skuVO.getLength())) {
                throw new BizException("SKU长度为空");
            }
            if (StringUtils.isBlank(skuVO.getWidth())) {
                throw new BizException("SKU宽度为空");
            }
            if (StringUtils.isBlank(skuVO.getHeight())) {
                throw new BizException("SKU高度为空");
            }
            if (StringUtils.isBlank(skuVO.getWeight())) {
                throw new BizException("SKU重量为空");
            }
            if (!isValidDecimal(skuVO.getLength())) {
                throw new BizException("SKU长度、超长或位数不正确: " + skuVO.getLength());
            }
            if (!isValidDecimal(skuVO.getWidth())) {
                throw new BizException("SKU宽度、超长或位数不正确: " + skuVO.getWidth());
            }
            if (!isValidDecimal(skuVO.getHeight())) {
                throw new BizException("SKU高度、超长或位数不正确: " + skuVO.getHeight());
            }
            if (!isValidDecimal(skuVO.getWeight())) {
                throw new BizException("SKU重量、超长或位数不正确: " + skuVO.getWeight());
            }
        }
    }

    private static boolean isValidDecimal(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        return DECIMAL_PATTERN.matcher(value.trim()).matches();
    }


    public void validateJdSkuExist(Long jdSkuId, Long skuId) {
        Boolean result = validateJdSkuIdExist(jdSkuId, skuId);
        AssertValidation.isTrue(result, SKU_JD_SKU_BIND_ERROR, getMessage(SKU_JD_SKU_BIND_ERROR));
    }

    public Boolean validateJdSkuIdExist(Long jdSkuId, Long skuId){
        List<Long> skuIds = validateJdSkuExistSku(jdSkuId, skuId);
        return CollectionUtils.isNotEmpty(skuIds);
    }

    /**
     * 校验逻辑
     * 1、新增的商品只校验jdSkuId存在绑定关系
     * 2、更新的商品，校验jdSkuId和当前skuId绑定外，是否还有其他绑定关系
     *
     * @param skuVOList sku列表
     */
    public void validateJdSkuRelation(List<SkuVO> skuVOList) {
        if (!jdSkuSingle){
            return;
        }
        List<Long> firstJdSkuIds = Lists.newArrayList();
        Map<Long, Long> updateJdSkuMap = Maps.newHashMap();
        for (SkuVO skuVO : skuVOList) {
            if (Objects.nonNull(skuVO.getSkuId())) {
                updateJdSkuMap.put(skuVO.getJdSkuId(), skuVO.getSkuId());
            } else {
                firstJdSkuIds.add(skuVO.getJdSkuId());
            }
        }
        if (CollectionUtils.isNotEmpty(firstJdSkuIds)){
            // 新增商品绑定关系校验
            Map<Long, Long> jdSkuRelationMap = skuAtomicService.querySkuRelationByJdSkuIds(firstJdSkuIds);
            for (Long jdSkuId : firstJdSkuIds) {
                //  校验同一个国内skuId只能关联一次
                AssertValidation.isTrue(jdSkuRelationMap.containsKey(jdSkuId), String.format("jdSku id: %s 已经存在关联的工业国际商品", jdSkuId));
            }
        }
        // 更新商品绑定关系校验
        if (MapUtils.isNotEmpty(updateJdSkuMap)) {
            for (Map.Entry<Long, Long> entry : updateJdSkuMap.entrySet()) {
                LambdaQueryWrapper<SkuPO> queryWrapper = Wrappers.lambdaQuery(SkuPO.class).eq(SkuPO::getJdSkuId, entry.getKey()).ne(SkuPO::getSkuId, entry.getValue()).eq(SkuPO::getYn, YnEnum.YES.getCode());
                List<SkuPO> skuPoList = skuAtomicService.list(queryWrapper);
                //  校验同一个国内skuId只能关联一次
                AssertValidation.isTrue(CollectionUtils.isNotEmpty(skuPoList), String.format("jdSku id: %s 已经存在关联的工业国际商品", entry.getKey()));
            }
        }
    }

    public void validateJdSkuNull(List<SkuVO> skuVOList) {
        for (SkuVO skuVO : skuVOList) {
            Long jdSkuId = skuVO.getJdSkuId();
            AssertValidation.isTrue(Objects.isNull(jdSkuId), "jdSku id: 不可为空");
        }
    }

    public void validateRepeatJdSku(List<SkuVO> skuVOList) {
        if (!jdSkuSingle){
            return;
        }
        Set<Long> jdSkuIds = Sets.newHashSet();
        for (SkuVO skuVO : skuVOList) {
            Long jdSkuId = skuVO.getJdSkuId();
            AssertValidation.isTrue(Objects.nonNull(jdSkuId) && jdSkuIds.contains(jdSkuId), String.format("jdSku id: %s 不能同时绑定多个SKU", jdSkuId));
            jdSkuIds.add(jdSkuId);
        }
    }

    public void validateInterProperty(Long catId,String sourceCountryCode,List<String> targetCountryCodes,List<SkuVO> skuVOList,Integer isExport) {
        // 校验跨境属性
        List<GroupPropertyVO> groupPropertyVOS = globalAttributeManageService.queryGroupPropertyVos(catId,sourceCountryCode, targetCountryCodes, AttributeDimensionEnum.SKU.getCode(), null,isExport);
        if(CollectionUtils.isEmpty(groupPropertyVOS)){
            log.info("SkuValidateService.checkInterProperty groupPropertyVOS is null");
            return;
        }

        groupPropertyVOS = groupPropertyVOS.stream().filter(Objects::nonNull).filter(item->item.getRequirement().equals(AttributeCheckTypeEnum.PUBLISH_PRODUCT.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(groupPropertyVOS)){
            log.info("SkuValidateService.checkInterProperty groupPropertyVOS2 is null");
            return;
        }

        List<PropertyVO> propertyVOS = groupPropertyVOS.get(0).getPropertyVOS();
        if(CollectionUtils.isEmpty(propertyVOS)){
            log.info("SkuValidateService.checkInterProperty propertyVOS is null");
            return;
        }
        Map<Long, PropertyVO> interPropertyMap = propertyVOS.stream().collect(Collectors.toMap(PropertyVO::getAttributeId, Function.identity()));

        List<Long> publishes = propertyVOS.stream().map(PropertyVO::getAttributeId).collect(Collectors.toList());

        for(SkuVO skuVO : skuVOList){
            List<GroupPropertyVO> skuGroupPropertyList = skuVO.getSkuInterPropertyList();
            AssertValidation.isTrue(CollectionUtils.isEmpty(skuGroupPropertyList),"请填写sku发品必填国际属性");
            List<Long> publishCount = new ArrayList<>();
            for(GroupPropertyVO groupPropertyVO : skuGroupPropertyList){
                List<PropertyVO> propertyVOS1 = groupPropertyVO.getPropertyVOS();
                if(CollectionUtils.isEmpty(propertyVOS1)){
                    continue;
                }
                for(PropertyVO propertyVO : propertyVOS1){
                    if(propertyVO == null){
                        continue;
                    }

                    if(publishes.contains(propertyVO.getAttributeId())){
                        publishCount.add(propertyVO.getAttributeId());
                        this.validateGlobalAttributeValueForText(propertyVO, interPropertyMap);
                    }
                }
            }

            if(publishCount.size() != publishes.size()){
                AssertValidation.isTrue(CollectionUtils.isEmpty(skuGroupPropertyList),"请填写sku发品必填国际属性");
            }
        }
    }

    public void validateCertificate(Long catId,String sourceCountryCode,List<String> targetCountryCodes,List<SkuVO> skuVOList,Integer isExport) {
        if(CollectionUtils.isEmpty(skuVOList)){
            log.info("SkuValidateService.validateCertificate skuVOList is null");
            return;
        }

        List<GlobalQualificationVO> qualificationVOS = globalQualificationManageService.queryByCountry(catId,sourceCountryCode,targetCountryCodes, AttributeDimensionEnum.SKU.getCode(),null, isExport);
        if(CollectionUtils.isEmpty(qualificationVOS)){
            log.info("SkuValidateService.validateCertificate qualificationVOS is null");
            return;
        }
        qualificationVOS = qualificationVOS.stream().filter(Objects::nonNull).filter(item->item.getRequirement().equals(AttributeCheckTypeEnum.PUBLISH_PRODUCT.getCode().toString())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(qualificationVOS)){
            log.info("SkuValidateService.validateCertificate qualificationVOS2 is null");
            return;
        }

        for(SkuVO skuVO : skuVOList){
            List<GroupSkuCertificateVO> skuGroupCertificateVOS = skuVO.getSkuCertificateVOList();
            AssertValidation.isTrue(CollectionUtils.isEmpty(skuGroupCertificateVOS),"请填写sku发品必填资质");
            skuGroupCertificateVOS = skuGroupCertificateVOS.stream().filter(Objects::nonNull).filter(item->item.getRequirement().equals(AttributeCheckTypeEnum.PUBLISH_PRODUCT.getCode())).collect(Collectors.toList());
            AssertValidation.isTrue(CollectionUtils.isEmpty(skuGroupCertificateVOS), "请填写sku发品必填资质");
            Map<Long, SkuCertificateVO> certificateVOMap = skuGroupCertificateVOS.get(0).getSkuCertificateVOS().stream().collect(Collectors.toMap(SkuCertificateVO::getCertificateId, Function.identity()));
            qualificationVOS.forEach(item->{
                AssertValidation.isTrue(!certificateVOMap.containsKey(item.getId()), SKU_INTER_CERT_NOT_EXIST_ERROR, getMessage(SKU_INTER_CERT_NOT_EXIST_ERROR));
            });
        }
    }

    public void validateProductionCycle(List<SkuVO> skuVOList){
        for (SkuVO skuVO : skuVOList) {
            BigDecimal productionCycle = skuVO.getProductionCycle();
            if(productionCycle == null){
                throw new BizException("发货时效不可为空");
            }
            // 检查是否为整数
            if (productionCycle.stripTrailingZeros().scale()  > 0) {
                throw new BizException("发货时效必须为整数");
            }
            // 检查是否为正数
            if (productionCycle.compareTo(BigDecimal.ZERO) <= 0) {
                throw new BizException("发货时效必须为正数");
            }
            // 检查是否超过10位
            if (productionCycle.compareTo(Constant.W10_BIGDECIMAL) > 0) {
                throw new BizException("发货时效不能超过10位数");
            }
        }
    }

    public void validateHsCode(List<SkuVO> skuVOList){
        if(CollectionUtils.isEmpty(skuVOList)){
            return;
        }
        for (SkuVO skuVO : skuVOList) {
            String hsCode = skuVO.getHsCode();
            if(StringUtils.isBlank(hsCode)){
                continue;
            }
            if(hsCode.length()>20){
                throw new BizException("出口HSCode长度不能超过20");
            }
        }
    }

    /**
     * 校验skulist扩展属性合法性
     * @param skuVOList 商品列表
     */
    public void validateSkuExtendProperty(List<SkuVO> skuVOList){
        // 暂不启用，因为系统中有其他业务调用发品接口完成，例如税率审核、库存修改、审批通过等，非核心业务数据校验导致这些业务无法通过，不合适，应该通过修改数据进入部分逻辑完成，而不是在发品校验
        if(CollectionUtils.isNotEmpty(skuVOList)){
            return;
        }
        if(CollectionUtils.isEmpty(skuVOList)){
            return;
        }
        for(SkuVO skuVO : skuVOList){
            SpuValidateService.validateExtendProperty(skuVO.getStoreExtendPropertyList());
        }
    }
    /**
     * 验证商品的UPC码是否合法。
     * @param skuVOList 商品列表
     */
    public void validateUpcCode(List<SkuVO> skuVOList){
        if(CollectionUtils.isEmpty(skuVOList)){
            return;
        }
        for (SkuVO skuVO : skuVOList) {
            String upcCode = skuVO.getUpcCode();
            if(StringUtils.isBlank(upcCode)){
                continue;
            }
            if(upcCode.matches(".*[，；]+.*")) {
                throw new BizException("条形码不能包含中文逗号或中文分号");
            }
            if(upcCode.length()>50){
                throw new BizException("条形码总长度不能超过50");
            }
        }
    }

    /**
     * 验证京东商品是否可购买。
     * @param jdSkuIds 京东商品的 SKU ID 列表。
     */
    public void validateSkuIdCanPurchase(List<Long> jdSkuIds){
        List<Long> canPurchaseJdSkuIds = skuCrossBorderSaleStatusReadManageService.queryStandardSkuAvailableSaleAllAreaByJdSkuIds(jdSkuIds);
        for(Long jdSkuId : jdSkuIds){
            AssertValidation.isTrue(canPurchaseJdSkuIds == null || !canPurchaseJdSkuIds.contains(jdSkuId), String.format("该主站商品%s在主站处于不可售状态，请检查", jdSkuId));
        }
    }

    public void validateSkuIdAreaLimit(List<Long> jdSkuIds){
        List<Long> areaLimitJdSkuIds = skuCrossBorderSaleStatusReadManageService.queryStandardSkuAreaLimitAllAreaByJdSkuIds(jdSkuIds);
        for(Long jdSkuId : jdSkuIds){
            AssertValidation.isTrue(CollectionUtils.isNotEmpty(areaLimitJdSkuIds) && areaLimitJdSkuIds.contains(jdSkuId), String.format("该主站商品%s在主站处于区域限售状态，请检查", jdSkuId));
        }
    }

    @Resource
    private CustomerManageService customerManageService;

    /**
     * 验证商品是否可购买。
     * @param skuIds 商品的 SKU ID 列表。
     * @param countryCode 国家代码。
     */
    @PFTracing
    public List<Long> validateSkuIdCanPurchase(List<Long> skuIds, String countryCode, Map<Long,String> failMsgMap, Boolean isCustomerPool){
        List<Long> canPurchaseSkuIds =
            skuCrossBorderSaleStatusReadManageService.queryStandardSkuAvailableSaleForCountryPool(skuIds,countryCode,failMsgMap,isCustomerPool);
        return canPurchaseSkuIds;
    }

    /**
     * 验证商品ID是否在区域限售状态。 返回来的是区域限售的京东商品 jdSkuID。
     * @param skuIds 商品ID列表。
     * @param countryCode 国家代码。
     */
    @PFTracing
    public List<Long> validateSkuIdAreaLimit(List<Long> skuIds, String countryCode, Boolean isCustomerPool){
        List<Long> areaLimitSkuIds = skuCrossBorderSaleStatusReadManageService.queryStandardSkuAreaLimitForCountryPool(skuIds,countryCode,isCustomerPool);
        return areaLimitSkuIds;
    }

    private String getMessage(String code) {
        return dataResponseMessageService.getErrorMessage(code);
    }

    /**
     * 验证全局属性值是否为文本类型，并检查数字类型的输入内容是否合法。
     * @param item 属性VO对象，包含属性的详细信息。
     * @param interPropertyMap 属性ID到属性VO的映射关系。
     */
    private void validateGlobalAttributeValueForText(PropertyVO item, Map<Long, PropertyVO> interPropertyMap) {
        PropertyVO propertyVO = interPropertyMap.get(item.getAttributeId());
        if (AttributeInputTypeEnum.TEXT.getCode().equals(propertyVO.getAttributeInputType())) {
            List<PropertyValueVO> valueVOS = item.getPropertyValueVOList();

            if (CollectionUtils.isEmpty(valueVOS)){
                return;
            }

            // 数字类型校验输入内容
            if (Objects.nonNull(propertyVO.getInputCheckType()) && AtrributeValueInputCheckTypeEnum.NUMBER.getCode().equals(propertyVO.getInputCheckType())) {
                // 校验不是正整数数字跨境属性值
                Optional<PropertyValueVO> first = valueVOS.stream().filter(value-> StringUtils.isNotBlank(value.getAttributeValueName())).filter(value -> !NumberUtils.isPositiveNumeric(value.getAttributeValueName())).findFirst();
                if (first.isPresent()) {
                    throw new BizException(String.format("跨境属性 [%s] 的属性值 %s 不是正整数类型",propertyVO.getAttributeName(), first.get().getAttributeValueName()));
                }
            }else if(Objects.nonNull(propertyVO.getInputCheckType()) && AtrributeValueInputCheckTypeEnum.DECIMAL.getCode().equals(propertyVO.getInputCheckType())){
                // 校验不是正小数数字跨境属性值
                Optional<PropertyValueVO> first = valueVOS.stream().filter(value-> StringUtils.isNotBlank(value.getAttributeValueName())).filter(value -> !NumberUtils.isPositiveDecimal(value.getAttributeValueName())).findFirst();
                if (first.isPresent()) {
                    throw new BizException(String.format("跨境属性 [%s] 的属性值 %s 不是正小数类型",propertyVO.getAttributeName(), first.get().getAttributeValueName()));
                }
            }else if(Objects.nonNull(propertyVO.getInputCheckType()) && AtrributeValueInputCheckTypeEnum.STRING.getCode().equals(propertyVO.getInputCheckType())){
                // 校验不是正小数数字跨境属性值
                Optional<PropertyValueVO> first = valueVOS.stream()
                        .filter(value -> StringUtils.isNotBlank(value.getAttributeValueName()) && value.getAttributeValueName().length()>100).findFirst();
                if (first.isPresent()) {
                    throw new BizException(String.format("跨境属性 [%s] 的属性值 %s 超出100位数",propertyVO.getAttributeName(), first.get().getAttributeValueName()));
                }
            }
        }
    }

    /**
     * 获取指定国家的最大负利润值。
     * @param sourceCountryCode 国家代码。
     * @return 最大负利润值。
     */
    private BigDecimal getCountryMaxNegativeProfit(String sourceCountryCode){
        log.info("SkuValidateService.getCountryMaxNegativeProfit sourceCountryCode:{}",sourceCountryCode);
        String countryMaxNegativeProfit = ConfigUtils.getStringByKey(productMaxNegativeProfit,sourceCountryCode);
        if(StringUtils.isBlank(countryMaxNegativeProfit)){
            return BigDecimal.ZERO;
        }
        return new BigDecimal(countryMaxNegativeProfit);
    }

    /**
     * 验证 SKU 库存列表中每个 SKU 的库存数量是否为整数。
     * @param skuVOList SKU VO 列表
     */
    public void validateSkuStockList(List<SkuVO> skuVOList, String sourceCountryCode) {
        // 查询sku库存列表
        Set<Long> skuIds = skuVOList.stream().filter(vo -> Objects.nonNull(vo.getSkuId())).map(SkuVO::getSkuId).collect(Collectors.toSet());
        // 批量查询sku库存信息
        Map<Long, Map<String, SkuStockVO>> skuWarehouseMap = skuStockManageService.listSkuWarehouseStock(new SkuStockReqVO(skuIds));
        for (SkuVO skuVO : skuVOList) {
            // 本土校验库存是否为整数
            AssertValidation.isTrue(!CountryConstant.COUNTRY_ZH.equals(sourceCountryCode) && !StringUtils.isNumeric(skuVO.getStockNum()), SPU_STOCK_NUMBER_ERROR, getMessage(SPU_STOCK_NUMBER_ERROR));

            // 获取当前SKU的库存关系
            Map<String, SkuStockVO> warehouseStockMap = skuWarehouseMap.getOrDefault(skuVO.getSkuId(), Collections.emptyMap());
            // 校验一个SKU不能绑定同国家多个备货仓
            List<SkuStockRelationVO> skuStockRelationList = skuVO.getSkuStockRelationList();
            if (CollectionUtils.isNotEmpty(skuStockRelationList)) {
                // 备货仓ID集合
                Set<Long> warehouseIds = skuStockRelationList.stream().filter(Objects::nonNull)
                        .filter(relation -> !Objects.equals(Constant.FACTORY_DEFAULT_ID_STR, relation.getWarehouseId()))
                        .map(r -> Long.valueOf(r.getWarehouseId())).collect(Collectors.toSet());

                if (CollectionUtils.isEmpty(warehouseIds)) {
                    continue;
                }

                // 巴西不支持寄售模式
                validateBrConsignment(sourceCountryCode, skuStockRelationList);

                for (SkuStockRelationVO relationVO : skuStockRelationList) {
                    if (Objects.equals(Constant.FACTORY_DEFAULT_ID_STR, relationVO.getWarehouseId())) {
                        continue;
                    }
                    // 库存信息
                    SkuStockVO skuStockVO = warehouseStockMap.get(relationVO.getWarehouseId());
                    log.info("validateSkuStockList 校验商品库存备货模式切换 skuId={}, skuStockVO={} relationVO={}",relationVO.getSkuId(),JSON.toJSONString(skuStockVO),JSON.toJSONString(relationVO));
                    // 有库存数量时，备货模式不能切换
                    String msg = String.format(StockMessageEnum.STK30001.getMsg(), skuVO.getSkuId());
                    AssertValidation.isTrue(BooleanUtils.toBoolean(StockNumUtils.existStock(skuStockVO)) && Objects.nonNull(relationVO.getPurchaseModel())
                                    && !Objects.equals(relationVO.getPurchaseModel(),skuStockVO.getPurchaseModel())
                            , StockMessageEnum.STK30001.getCode(), StockMessageEnum.STK30001.buildMsg(skuVO.getSkuId()));
                }

                List<WarehouseResVO> warehouseResVOS = warehouseManageService.queryByWarehouseIds(warehouseIds);
                // 按国家分组仓库列表
                Map<String, List<WarehouseResVO>> countryWarehouseMap = Optional.ofNullable(warehouseResVOS).orElseGet(ArrayList::new)
                        .stream().filter(Objects::nonNull)
                        .collect(Collectors.groupingBy(WarehouseResVO::getCountryCode));

                countryWarehouseMap.forEach((countryCode, list)-> {
                    if (CollectionUtils.isNotEmpty(list) && list.size() > 1){
                        log.warn("validateSkuStockList 校验商品同一个国家只能绑定一个备货仓逻辑 skuId={} countryCode={} 备货仓list={}",skuVO.getSkuId(),countryCode,JSON.toJSONString(list));
                        throw new BizException("一个SKU下一个国家内只能关联一个备货仓");
                    }
                });
            }
        }
    }

    /**
     * 巴西本土品不支持寄售模式校验
     * @param sourceCountryCode 货源国
     * @param skuStockRelationList 库存和商品关系列表
     */
    private void validateBrConsignment(String sourceCountryCode, List<SkuStockRelationVO> skuStockRelationList) {
        boolean isConsignment = skuStockRelationList.stream().filter(relation -> !Objects.equals(Constant.FACTORY_DEFAULT_ID_STR, relation.getWarehouseId())).anyMatch(r -> Objects.equals(r.getPurchaseModel(), PurchaseModelTypeEnum.CONSIGNMENT.getCode()));
        boolean isBrCountryCode = Objects.equals(CountryConstant.COUNTRY_BR, sourceCountryCode);
        if (isBrCountryCode){
            log.info("validateBrConsignment 校验巴西商品不支持寄售模式 sourceCountryCode={}, isConsignment={} skuStockRelationList={}",sourceCountryCode,isConsignment,JSON.toJSONString(skuStockRelationList));
        }
        AssertValidation.isTrue(isBrCountryCode && isConsignment,"巴西本土品不支持寄售模式");
    }

    /**
     * 验证 SKU 销售属性值是否合法。
     * @param spuVO 保存 SPu 的 VO 对象，包含 SPu 的属性范围信息。
     * @param skuVOList SKU 的 VO 对象列表，包含每个 SKU 的销售属性值信息。
     */
    @Deprecated
    public void validateSkuSaleProperty(SpuVO spuVO, List<SkuVO> skuVOList) {
        if(CollectionUtils.isEmpty(skuVOList)){
            throw new BizException("sku信息不能为空");
        }

        Set<String> countryCodes = new HashSet<>();
        String attributeScope = spuVO.getAttributeScope();
        if(StringUtils.isNotBlank(attributeScope)){
            countryCodes = Arrays.stream(attributeScope.split(Constant.COMMA)).collect(Collectors.toSet());
        } else {
            countryCodes.add(spuVO.getSourceCountryCode());
        }
        List<LangVO> langVOS = countryLangManageService.queryLangListByCountry(countryCodes,null);
        Set<String> langSet = langVOS.stream().map(LangVO::getLangCode).collect(Collectors.toSet());

//        List<AttributeFlatVO> attributeFlatVOS = attributeOutService.querySellAttr(null);
//        List<Long> attributeIds = attributeFlatVOS.stream().map(AttributeFlatVO::getId).collect(Collectors.toList());
//
//
//        for(SkuVO skuVO : skuVOList){
//            List<PropertyValueVO> propertyValueVOList = skuVO.getStoreSalePropertyList();
//            // 检查销售属性值是否合法
//            spuValidateService.validateSalePropertyValue(propertyValueVOList,langSet,attributeIds);
//        }
    }

    /**
     * 校验国内SKU已经绑定了国际SKU
     * @param jdSkuId 国内SKU ID
     * @param skuId 当前国际SKU ID
     * @return 国际SKU ID
     */
    public List<Long> validateJdSkuExistSku(Long jdSkuId, Long skuId) {
        if (!jdSkuSingle){
            return Collections.emptyList();
        }
        // 查询当前jdSkuId，并且不等于skuId的数据，存在就报错
        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>().select(SkuPO::getSkuId, SkuPO::getJdSkuId).eq(SkuPO::getJdSkuId, jdSkuId)
                .ne(Objects.nonNull(skuId), SkuPO::getSkuId, skuId)// 不等于skuId的数据
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> skuPoList = skuAtomicService.getBaseMapper().selectList(queryWrapper);
        if(CollectionUtils.isNotEmpty(skuPoList)){
            return skuPoList.stream().map(SkuPO::getSkuId).collect(Collectors.toList());
        }

        Stopwatch skuDraftJdSkuWatch = Stopwatch.createStarted();
        LambdaQueryWrapper<SkuDraftPO> like = Wrappers.lambdaQuery(SkuDraftPO.class)
                .eq(SkuDraftPO::getJdSkuId, jdSkuId)
                .ne(SkuDraftPO::getSkuId, skuId)
                .eq(SkuDraftPO::getYn, YnEnum.YES.getCode());
                //.like(SkuDraftPO::getSkuJsonInfo, JD_SKU_ID_LIKE_STR + jdSkuId);
        List<SkuDraftPO>  skuDraftPOList = skuDraftAtomicService.list(like);
        Stopwatch stop = skuDraftJdSkuWatch.stop();
        log.info("SkuValidateService.validateJdSkuExistSku skuId={} SKU草稿是否包含JD SKU ID={}, 消耗时间={}ms",skuId, jdSkuId, stop.elapsed(TimeUnit.MILLISECONDS));
        return Optional.ofNullable(skuDraftPOList)
                .orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull)
                .map(SkuDraftPO::getSkuId).collect(Collectors.toList());
    }

    /**
     * 校验国内商品是否真实有效存在
     * @param jdSkuIds 国内SKU ID集合
     */
    private void validateJdSkuExisted(Set<Long> jdSkuIds){
        if (CollectionUtils.isEmpty(jdSkuIds)){
            return;
        }

        Map<Long, JdProductDTO> jdSkuMap = skuInfoRpcService.querySkuMap(new JdProductQueryDTO(Lists.newArrayList(jdSkuIds)));

        Set<Long> notExistedJdSkuIds = new HashSet<>();
        for (Long jdSkuId : jdSkuIds) {
            if (!jdSkuMap.containsKey(jdSkuId)) {
                notExistedJdSkuIds.add(jdSkuId);
            }
        }

        if (CollectionUtils.isNotEmpty(notExistedJdSkuIds)){
            throw new BizException(String.format("国内SKU ID [%s] 不存在",StringUtils.joinWith(Constant.COMMA, notExistedJdSkuIds)));
        }
    }
}
