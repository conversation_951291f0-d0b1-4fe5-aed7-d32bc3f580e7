package com.jdi.isc.product.soa.service.manage.taxRate.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jd.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.GlobalAttributeVnEnum;
import com.jdi.isc.product.soa.api.common.enums.ViTaxEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.BaseManageSupportService;
import com.jdi.isc.product.soa.domain.sku.biz.SkuGlobalAttributeDetailVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuGlobalAttributeVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuLangPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.domain.taxRate.biz.HscodeTaxRateRelationPageVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.HscodeTaxRateRelationVO;
import com.jdi.isc.product.soa.domain.taxRate.biz.TaxRateVO;
import com.jdi.isc.product.soa.domain.taxRate.po.ExportTaxRatePO;
import com.jdi.isc.product.soa.domain.taxRate.po.HscodeTaxRateRelationPO;
import com.jdi.isc.product.soa.domain.taxRate.res.HscodeTaxRateRelationResVO;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.ExportTaxRateAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.HscodeTaxRateRelationAtomicService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.HscodeTaxRateRelationManageService;
import com.jdi.isc.product.soa.service.manage.taxRate.TaxRateManageService;
import com.jdi.isc.product.soa.service.mapstruct.taxRate.HscodeTaxRateRelationConvert;
import com.jdi.isc.product.soa.service.taxRate.req.HscodeTaxRateRelationReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 跨境税率关系数据维护服务实现
 * @Author: chengliwei
 * @Date: 2024/12/11 10:47
 **/

@Slf4j
@Service
public class HscodeTaxRateRelationManageServiceImpl extends BaseManageSupportService<HscodeTaxRateRelationVO, HscodeTaxRateRelationPO> implements HscodeTaxRateRelationManageService {

    @Resource
    private HscodeTaxRateRelationAtomicService hscodeTaxRateRelationAtomicService;
    @Resource
    private ExportTaxRateAtomicService exportTaxRateAtomicService;
    @Resource(name = "taxRateManageServiceImpl")
    private TaxRateManageService taxRateManageService;
    @Resource
    private SpuLangAtomicService spuLangAtomicService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private SkuReadManageService skuReadManageService;

    @Override
    public DataResponse<HscodeTaxRateRelationResVO> saveOrUpdate(HscodeTaxRateRelationReqVO input) {
        log.info("saveOrUpdate, input={}", JSONObject.toJSONString(input));

        // 参数业务校验
        DataResponse<Boolean> checkResult = checkAndInitInput(input);
        if (!checkResult.getSuccess()) {
            log.info("saveOrUpdate, check input fail, input={}, checkResult={}", JSONObject.toJSONString(input), JSONObject.toJSONString(checkResult));
            return DataResponse.error(checkResult.getMessage());
        }

        Long skuId = input.getSkuId();
        String countryCode = input.getCountryCode();
        HscodeTaxRateRelationPO exists = hscodeTaxRateRelationAtomicService.getValidBySkuIdAndCountryCode(skuId, countryCode);
        if (exists != null) {
            String otherHsCode = exists.getOtherHsCode();
            if (CountryConstant.COUNTRY_VN.equals(countryCode)
                    && otherHsCode != null
                    && !otherHsCode.equals(input.getOtherHsCode())) {
                return DataResponse.error("越南HScode与原有信息不一致，请人工检查，其他行正常导入");
            }
            if (update(exists)) {
                return DataResponse.success();
            } else {
                return DataResponse.error("更新失败，请联系管理员");
            }
        }
        // 保存对象
        HscodeTaxRateRelationPO po = HscodeTaxRateRelationConvert.INSTANCE.hscodeTaxRateRelationReqVo2Po(input);
        SkuPO skuPO = skuAtomicService.getSkuPoBySkuId(skuId);
        po.setSpuId(skuPO.getSpuId());
        boolean saveRes = hscodeTaxRateRelationAtomicService.save(po);
        if (!saveRes) {
            log.warn("saveOrUpdate, HscodeTaxRateRelationPO fail. HscodeTaxRateRelationPO={}", JSONObject.toJSONString(po));
            return DataResponse.error("保存失败，请联系管理员");
        }

        return DataResponse.success();
    }

    private boolean update(HscodeTaxRateRelationPO po) {
        po.setCreator(null);
        po.setSpuId(null);

        return hscodeTaxRateRelationAtomicService.updateById(po);
    }

    @Override
    public HscodeTaxRateRelationResVO detail(Long id) {
        HscodeTaxRateRelationPO po = hscodeTaxRateRelationAtomicService.getValidById(id);
        if (null == po) {
            log.info("detail, HscodeTaxRateRelationPO null. id={}", id);
            return null;
        }

        HscodeTaxRateRelationResVO vo = HscodeTaxRateRelationConvert.INSTANCE.hscodeTaxRateRelationPo2ResVo(po);
        return vo;
    }

    @Override
    public PageInfo<HscodeTaxRateRelationPageVO.Response> pageSearch(HscodeTaxRateRelationPageVO.Request input) {
        PageInfo<HscodeTaxRateRelationPageVO.Response> pageInfo = new PageInfo<>();
        pageInfo.setSize(input.getSize());
        pageInfo.setIndex(input.getIndex());

        // 总条数
        long total = hscodeTaxRateRelationAtomicService.pageSearchTotal(input);
        pageInfo.setTotal(total);
        if (0 == total) {
            log.info("page, total is zero. input={}", JSONObject.toJSONString(input));
            return pageInfo;
        }

        // 分页查询
        List<HscodeTaxRateRelationPageVO.Response> pageList = hscodeTaxRateRelationAtomicService.pageSearch(input);

        List<Long> spuIdSet = pageList.stream().map(HscodeTaxRateRelationPageVO.Response::getSpuId).collect(Collectors.toList());
        Map<Long, SpuLangPO> spuMap = spuLangAtomicService.getSpuLangNameBySpuIds(spuIdSet, LangConstant.LANG_ZH);

        pageList.forEach(item -> {
            SpuLangPO spuLangPO = spuMap.get(item.getSpuId());
            if (spuLangPO != null) {
                item.setSpuName(spuLangPO.getSpuTitle());
            }
            // 中国海关相关信息
            ExportTaxRatePO exportTaxRateByHsCode = exportTaxRateAtomicService.getExportTaxRateByHsCode(item.getCnHsCode(), CountryConstant.COUNTRY_ZH);
            log.info("中国海关相关信息：{}", JSON.toJSONString(exportTaxRateByHsCode));
            if (exportTaxRateByHsCode != null) {
                item.setExportRebateRate(exportTaxRateByHsCode.getExportRebateRate());
                item.setCustomsCondition(exportTaxRateByHsCode.getCustomsCondition());
                item.setQuarantineCat(exportTaxRateByHsCode.getQuarantineCat());
            }
            // 越南海关相关信息
            TaxRateVO taxRateVO = new TaxRateVO();
            taxRateVO.setCountryCode(CountryConstant.COUNTRY_VN);
            taxRateVO.setKeyId(String.valueOf(item.getSkuId()));
            taxRateVO.setHsCode(item.getCnHsCode());
            taxRateVO.setKeyType(1);
            List<TaxRateVO> taxRateVOS = taxRateManageService.listByCountryCodeAndSkuId(taxRateVO);
            log.info("越南海关相关信息：{}", JSON.toJSONString(taxRateVOS));
            Map<String, BigDecimal> codeRateMap = taxRateVOS.stream().collect(Collectors.toMap(TaxRateVO::getTaxCode, TaxRateVO::getTaxRate));
            item.setViMFNTaxRate(codeRateMap.get(ViTaxEnum.MFN.name()));
            item.setViOriginTaxRate(codeRateMap.get(ViTaxEnum.ORIGIN.name()));
            item.setViConsumptionTaxRate(codeRateMap.get(ViTaxEnum.CONSUMPTION.name()));
            item.setViAntiDumpingTaxRate(codeRateMap.get(ViTaxEnum.ANTI_DUMPING.name()));
            item.setVatIncreaseTaxRate(codeRateMap.get(ViTaxEnum.INCREASE.name()));
            // 跨境属性
            SkuGlobalAttributeVO param = new SkuGlobalAttributeVO();
            param.setTargetCountryCode(CountryConstant.COUNTRY_VN);
            SkuGlobalAttributeDetailVO skuGlobalAttributeDetailVO = new SkuGlobalAttributeDetailVO();
            skuGlobalAttributeDetailVO.setSkuId(item.getSkuId());
            Map<String, Object> updateData = new HashMap<>();
            updateData.put(GlobalAttributeVnEnum.VN_IMPORT_LIMIT.getCode(), "");
            updateData.put(GlobalAttributeVnEnum.VN_IN_BONDED_WAREHOUSE.getCode(), "");
            updateData.put(GlobalAttributeVnEnum.VN_IMPORT_REMARK.getCode(), "");
            updateData.put(GlobalAttributeVnEnum.VN_HSCODE.getCode(), item.getViHsCode());
            skuGlobalAttributeDetailVO.setUpdateData(updateData);
            param.setDetailData(Collections.singletonList(skuGlobalAttributeDetailVO));
            List<SkuGlobalAttributeDetailVO> globalAttributes = skuReadManageService.selectGlobalAttribute(param);
            if (globalAttributes != null) {
                SkuGlobalAttributeDetailVO detailVO = globalAttributes.get(0);
                Map<String, Object> globalAttributeData = detailVO.getUpdateData();
                String vnImportLimit = String.valueOf(globalAttributeData.get(GlobalAttributeVnEnum.VN_IMPORT_LIMIT.getCode()));
                item.setViImportRestrict("1".equals(vnImportLimit) ? "是" : "否");
                String vnInBondedWarehouse = String.valueOf(globalAttributeData.get(GlobalAttributeVnEnum.VN_IN_BONDED_WAREHOUSE.getCode()));
                item.setViInBondWarehouse("1".equals(vnInBondedWarehouse) ? "是" : "否");
                if(Objects.nonNull(globalAttributeData.get(GlobalAttributeVnEnum.VN_IMPORT_REMARK.getCode()))){
                    item.setViImportRemark(String.valueOf(globalAttributeData.get(GlobalAttributeVnEnum.VN_IMPORT_REMARK.getCode())));
                }
            }
        });
        pageInfo.setRecords(pageList);

//        // 查询列表
//        Page<HscodeTaxRateRelationPO> pageDB = new Page<>(input.getIndex(), input.getSize());
//        // 构建查询条件
//        LambdaQueryWrapper<HscodeTaxRateRelationPO> wrapper = this.buildWrapper(input);
//
//        Page<HscodeTaxRateRelationPO> dbRecord = hscodeTaxRateRelationAtomicService.page(pageDB, wrapper);
//        pageInfo.setTotal(dbRecord.getTotal());
//        if (CollectionUtils.isEmpty(dbRecord.getRecords())){
//            return pageInfo;
//        }
//
//        List<HscodeTaxRateRelationPageVO.Response> pageList = HscodeTaxRateRelationConvert.INSTANCE.pageListPo2Vo(dbRecord.getRecords());
//        if (CollectionUtils.isNotEmpty(pageList)){
//            pageInfo.setRecords(pageList);
//        }

        return pageInfo;
    }


    /**
     * 参数校验
     *
     * @param input 提交参数
     * @return 检查结果
     */
    private DataResponse<Boolean> checkAndInitInput(HscodeTaxRateRelationReqVO input) {
        Long skuId = input.getSkuId();
        SkuVO sku = skuReadManageService.getSkuBySkuId(skuId);
        if (sku == null) {
            return DataResponse.error("商品不存在");
        }
        if (!CountryConstant.COUNTRY_ZH.equals(sku.getSourceCountryCode())) {
            return DataResponse.error("货源国不是中国");
        }
        return DataResponse.success();
    }

    /**
     * 列表查询条件生成
     *
     * @param input 查询参数
     * @return 查询条件Wrapper
     */
    private LambdaQueryWrapper<HscodeTaxRateRelationPO> buildWrapper(HscodeTaxRateRelationPageVO.Request input) {
        LambdaQueryWrapper<HscodeTaxRateRelationPO> wrapper = Wrappers.<HscodeTaxRateRelationPO>lambdaQuery();
        wrapper.eq(HscodeTaxRateRelationPO::getYn, YnEnum.YES.getCode());
        wrapper.orderByDesc(HscodeTaxRateRelationPO::getUpdateTime);

        return wrapper;
    }


}