package com.jdi.isc.product.soa.service.manage.customerMku.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.fastjson.JSON;
import com.jd.k2.gd.boost.dto.ReflectDataDto;
import com.jd.ka.stock.enums.ReflectTypeEnum;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.ducc.OperDuccConfig;
import com.jdi.isc.product.soa.common.enums.KeyTypeEnum;
import com.jdi.isc.product.soa.common.exception.CustomerMkuBindException;
import com.jdi.isc.product.soa.domain.customer.biz.CustomerVO;
import com.jdi.isc.product.soa.domain.customerMku.biz.CustomerMkuEachReqVO;
import com.jdi.isc.product.soa.domain.customerMku.biz.CustomerMkuPriceVO;
import com.jdi.isc.product.soa.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.product.soa.domain.ducc.PreselectionVO;
import com.jdi.isc.product.soa.domain.enums.mku.MkuSpuRelationFlagEnum;
import com.jdi.isc.product.soa.domain.mku.biz.MkuOperateVO;
import com.jdi.isc.product.soa.domain.mku.po.MkuLangPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuPO;
import com.jdi.isc.product.soa.domain.mku.po.MkuRelationPO;
import com.jdi.isc.product.soa.domain.sku.biz.QuerySkuAvailableSaleReqVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuAvailableSaleResultVO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuLogSaveVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.specialAttr.po.SpecialAttrRelationPO;
import com.jdi.isc.product.soa.domain.taxRate.po.CustomerSkuTaxRatePO;
import com.jdi.isc.product.soa.rpc.ept.EptWareRpcService;
import com.jdi.isc.product.soa.rpc.gms.CategoryRpcService;
import com.jdi.isc.product.soa.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.product.soa.rpc.vcs.VendorPriceRpcService;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.customerMku.CustomerMkuPriceAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.mku.MkuRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuLogAtomicService;
import com.jdi.isc.product.soa.service.atomic.specialAttr.SpecialAttrRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.taxRate.CustomerSkuTaxRateAtomicService;
import com.jdi.isc.product.soa.service.manage.country.CountryManageService;
import com.jdi.isc.product.soa.service.manage.customer.CustomerManageService;
import com.jdi.isc.product.soa.service.manage.customerMku.CustomerMkuBindManageService;
import com.jdi.isc.product.soa.service.manage.customerMku.CustomerMkuPriceManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuCrossBorderSaleStatusReadManageService;
import com.jdi.isc.product.soa.service.manage.sku.SkuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuReadManageService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/7/25
 **/
@Slf4j
@Service
public class CustomerMkuBindManageServiceImpl implements CustomerMkuBindManageService {
    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;
    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private MkuRelationAtomicService mkuRelationAtomicService;
    @Resource
    private CustomerMkuPriceManageService customerMkuPriceManageService;
    @Resource
    private MkuAtomicService mkuAtomicService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private EptWareRpcService eptWareRpcService;
    @Resource
    private CustomerSkuTaxRateAtomicService customerSkuTaxRateAtomicService;
    @Resource
    private CustomerMkuPriceAtomicService customerMkuPriceAtomicService;
    @Resource
    private SkuInfoRpcService skuInfoRpcService;
    @Resource
    private SkuReadManageService skuReadManageService;
    @Resource
    private VendorPriceRpcService vendorPriceRpcService;
    @Resource
    private SpuReadManageService spuReadManageService;
    @Resource
    @Lazy
    private SpuManageService spuManageService;
    @Resource
    private CountryMkuAtomicService countryMkuAtomicService;
    @Resource
    private CountryManageService countryManageService;


    @Resource
    private SpecialAttrRelationAtomicService specialAttrRelationAtomicService;

    @Resource
    private CategoryRpcService categoryRpcService;


    @LafValue("jdi.isc.bind.check")
    private Boolean bindCheckFlag;

    // 入池校验商品无英文名，比亚迪
    @LafValue("jdi.isc.bind.check.mkuName.client")
    private String clientCodes;

    @Resource
    private MkuLangAtomicService mkuLangAtomicService;

    @Autowired
    private SkuCrossBorderSaleStatusReadManageService skuCrossBorderSaleStatusReadManageService;

    // 入池校验商品无英文名，比亚迪
    @LafValue("jdi.isc.bind.check.customerPriceFlg")
    private Boolean customerPriceFlg;


    @LafValue("jdi.isc.check.price.customer")
    private Set<String> checkCustomerPrice;

    @LafValue("jdi.isc.open.byd.clients")
    private Set<String> bydClientCodes;

    @Resource
    private OperDuccConfig operDuccConfig;

    private static Long PRE_SELECTED = 9L;

    @Resource
    private SkuLogAtomicService skuLogAtomicService;

    /**
    * @function 绑定客户与mku的关系，并校验相关信息
    * @param input 客户mku每个请求的值对象，包含客户代码和mku ID集合
    * @returns Boolean 返回绑定操作是否成功
    */
    @Override
    @Transactional(transactionManager = "productTransactionManager", rollbackFor = Exception.class, timeout = 60)
    @PFTracing
    @ToolKit()
    public Boolean bind(CustomerMkuEachReqVO input) {
        CustomerVO customer = customerManageService.detail(input.getClientCode());
        if(customer == null){
            throw new CustomerMkuBindException(String.format("客户mku绑定失败:客户 %s 不存在",input.getClientCode()));
        }
        if (CollectionUtils.isEmpty(customer.getStations())) {
            throw new CustomerMkuBindException(String.format("客户mku绑定失败:客户 %s 未配置可采购的货源站点",input.getClientCode()));
        }
        Long mkuId = input.getMkuId();
        MkuPO mkuPo = mkuAtomicService.getPOById(mkuId);
        if(Objects.isNull(mkuPo)){
            throw new CustomerMkuBindException(String.format("客户mku绑定失败:mku %s 不存在",mkuId));
        }
        //绑定规则读取配置
        List<String> allowBindCountries = customerManageService.getInPoolRule(input.getClientCode());;
        if ( !allowBindCountries.contains(mkuPo.getSourceCountryCode())){
            throw new CustomerMkuBindException(String.format("客户mku绑定失败:%s 客户只能绑定货源国为 %s 的mku",customer.getClientCode(), JSONObject.toJSONString(allowBindCountries)));
        }
        // 比亚迪客户校验开阳系统类目映射
        checkRelationCategory(customer, mkuId);
        // 校验客制化价格
        checkCustomerPrice(mkuPo,customer);
        Map<Long, MkuLangPO> mkuLangPOMap = mkuLangAtomicService.listLang(new MkuOperateVO(Sets.newHashSet(mkuId), LangConstant.LANG_EN));
        if (MapUtils.isEmpty(mkuLangPOMap) || Objects.isNull(mkuLangPOMap.get(mkuId)) || StringUtils.isBlank(mkuLangPOMap.get(mkuId).getMkuTitle())) {
            throw new CustomerMkuBindException(String.format("客户mku绑定失败:客户 %s 商品 %s 无英文名称", input.getClientCode(), mkuId));
        }
        List<MkuRelationPO> listSku = mkuRelationAtomicService.queryBindListByMkuId(mkuId);
        if(CollectionUtils.isEmpty(listSku)){
            throw new CustomerMkuBindException(String.format("客户mku绑定失败:mku %s 不存在任何有绑定关系的sku",mkuId));
        }
        //mku下辖固定sku
        MkuRelationPO fixedSku = listSku.stream().filter(line -> mkuPo.getSourceCountryCode().equals(line.getSkuSourceCountryCode()))
                .min(Comparator.comparing(MkuRelationPO::getCreateTime)).orElse(null);
        if(fixedSku==null){
            throw new CustomerMkuBindException(String.format("客户mku绑定失败:mku %s 未能找到同货源国的固定sku",mkuId));
        }
        List<PreselectionVO> preselectionVOList = operDuccConfig.preselectionList();
        for (PreselectionVO preselectionVO : preselectionVOList){
            if(!preselectionVO.getClientCodeList().contains(customer.getClientCode())){
                checkSelection(fixedSku);
            }
        }
        //如果客户配置了需要校验税率信息,需判断固定sku对应的增值税&关税不为空
        if(customer.getTaxCheckFlag()){
            CustomerSkuTaxRatePO SkuTaxRatePo = new CustomerSkuTaxRatePO(customer.getCountry(),customer.getClientCode(),fixedSku.getSkuId());
            CustomerSkuTaxRatePO tax = customerSkuTaxRateAtomicService.getOneBySku(SkuTaxRatePo);
            if(tax==null || tax.getValueAdded()==null || tax.getTariff()==null){
                throw new CustomerMkuBindException(String.format("客户mku绑定失败:固定sku %s 增值税或关税数据不完整,请检查",fixedSku.getSkuId()));
            }
        }
        SkuPO skuPo = skuAtomicService.getSkuPoBySkuId(fixedSku.getSkuId());
        //校验固定sku绑定的jdSkuId是否有对应的eptSkuId
        if(!checkSkuInPool(skuPo, customer)){
            throw new CustomerMkuBindException(String.format("客户mku绑定失败:固定sku %s 内贸段不可售,请检查",fixedSku.getSkuId()));
        }
        //更新客户&mku关系
        buildCustomerMku(customer,mkuId,skuPo,input.getGreenPass());
        //更新客&mku&固定sku关系
        CustomerMkuPriceVO customerMkuPriceVO = new CustomerMkuPriceVO(mkuPo.getMkuId(), customer.getClientCode(), customer.getClientName(), mkuPo.getSourceCountryCode(), fixedSku.getSkuId(), fixedSku.getVendorCode(),customer.getCountry());
        customerMkuPriceManageService.saveOrUpdate(customerMkuPriceVO);
        log.info("CustomerMkuManageServiceImpl.bind 客户{}及mku{}及固定sku{}绑定完毕." ,  customer.getClientCode(), mkuId,fixedSku.getSkuId());
        return true;
    }



    @PFTracing
    private void checkCustomerPrice(MkuPO mkuPo,CustomerVO customer){
        boolean check = true;
        /*if(CountryConstant.COUNTRY_BR.equals(customer.getCountry()) || CountryConstant.COUNTRY_ZH.equals(customer.getCountry())){
            check = true;
        }*/
        /*else if(CountryConstant.COUNTRY_ZH.equals(mkuPo.getSourceCountryCode())){
            // 校验客制化价格
            check = true;
        }*/
        /*else if(CountryConstant.COUNTRY_BR.equals(mkuPo.getSourceCountryCode()) || CountryConstant.COUNTRY_MY.equals(mkuPo.getSourceCountryCode())){
            // 校验客制化价格
            check = true;
        }*/
        /*else if(checkCustomerPrice.contains(customer.getClientCode())){
            // 校验客制化价格
            check = true;
        }*/
        List<PreselectionVO> preselectionVOList = operDuccConfig.preselectionList();
        for (PreselectionVO preselectionVO : preselectionVOList){
            if(preselectionVO.getPreseletorClientCodeList().contains(customer.getClientCode())){
                check = false;
            }
        }
        if(check) {
            // 查询客制化价格
            Map<Long, CustomerMkuPriceVO> customerMkuPriceVoMap = this.batchQueryCustomerMkuPriceMap(Lists.newArrayList(mkuPo.getMkuId()), customer.getClientCode());
            if(customerMkuPriceVoMap.containsKey(mkuPo.getMkuId())){
                CustomerMkuPriceVO customerMkuPriceVO = customerMkuPriceVoMap.get(mkuPo.getMkuId());
                if(Objects.isNull(customerMkuPriceVO)){
                    throw new CustomerMkuBindException(String.format("客户mku绑定失败:客户 %s 商品 mku %s 未设置客制化价格和国家协议价", customer.getClientName(),mkuPo.getMkuId()));
                }
            }else {
                throw new CustomerMkuBindException(String.format("客户mku绑定失败:客户 %s 商品 mku %s 未设置客制化价格和国家协议价",customer.getClientName(),mkuPo.getMkuId()));
            }
        }
    }


    /**
     * 检查是否为巴西预选品，如果是则抛出异常。
     * @param fixedSku 固定的 SKU 对象
     */
    @PFTracing
    private void checkSelection(MkuRelationPO fixedSku){
        Set<Long> skuIdSet = new HashSet<>();
        skuIdSet.add(fixedSku.getSkuId());
        List<SpecialAttrRelationPO> specialAttrRelationPOList = specialAttrRelationAtomicService.queryBySkus(skuIdSet);
        for (SpecialAttrRelationPO specialAttrRelationPO : specialAttrRelationPOList){
            if (PRE_SELECTED.equals(specialAttrRelationPO.getAttributeValueId())) {
                throw new CustomerMkuBindException(String.format("客户mku绑定失败:固定sku %s 是预选品无法入池,请检查mku %s",fixedSku.getSkuId(),fixedSku.getMkuId()));
            }
        }
    }



    //校验固定sku绑定的jdSkuId是否有对应的eptSkuId
    @SneakyThrows
    private boolean eptSkuCheck(SkuPO skuPo) {
        //非跨境品不检查
        if(!CountryConstant.COUNTRY_ZH.equals(skuPo.getSourceCountryCode())){
            return true;
        }
        if(bindCheckFlag!=null && bindCheckFlag){
            Map<Long, Long> eptSku = eptWareRpcService.queryEptSkuIdByJdSkuId(Sets.newHashSet(skuPo.getJdSkuId()));
            return eptSku != null && eptSku.get(skuPo.getJdSkuId()) != null;
        }
        return true;
    }

    @SneakyThrows
    @PFTracing
    private boolean checkSkuInPool(SkuPO skuPo, CustomerVO customer){
        //非跨境品不检查
        if(!CountryConstant.COUNTRY_ZH.equals(skuPo.getSourceCountryCode())){
            return true;
        }
        if(bindCheckFlag!=null && bindCheckFlag){
            QuerySkuAvailableSaleReqVO reqVO = new QuerySkuAvailableSaleReqVO();
            reqVO.setSkuIds(Sets.newHashSet(skuPo.getSkuId()));
            reqVO.setClientCode(customer.getClientCode());
            Map<Long, SkuAvailableSaleResultVO> resultMap = skuCrossBorderSaleStatusReadManageService.querySkuAvailableCanPurchaseMap(reqVO);
            return resultMap != null && resultMap.get(skuPo.getSkuId()) != null && resultMap.get(skuPo.getSkuId()).getIsAvailableSale();
        }

        return true;
    }

    //更新 客户&mku关系
    @PFTracing
    private void buildCustomerMku(CustomerVO customer, Long mkuId, SkuPO skuPo,String remark) {
        CustomerMkuPO target = new CustomerMkuPO(customer.getClientCode(), mkuId, customer.getClientName());
        target.setCatId(skuPo.getCatId());
        target.setJdCatId(skuPo.getJdCatId());
        // 判断是否已存在【客户&spu&绑定】的数据,存在则当前绑定记录的spuFlag置为0,即当前mku不会在前台跨境商列中展示
        target.setCountryCode(skuPo.getSourceCountryCode());
        target.setSpuId(skuPo.getSpuId());
        target.setRemark(remark);
        target.setTargetCountryCode(customer.getCountry());
        if(customerMkuAtomicService.leaderMkuExists(target.getClientCode(), skuPo.getSpuId(), skuPo.getSourceCountryCode())){
            target.setSpuFlag(MkuSpuRelationFlagEnum.NO.getCode());
        }else {
            target.setSpuFlag(MkuSpuRelationFlagEnum.YES.getCode());
        }
        //更新客&mkuId
        CustomerMkuPO existRef = customerMkuAtomicService.getOne(mkuId,customer.getClientCode(),null);
        if(existRef==null){
            customerMkuAtomicService.save(target);
        }else if (CustomerMkuBindEnum.INVALID.equals(existRef.getBindStatus())){
            customerMkuAtomicService.update(new CustomerMkuPO(target), Wrappers.<CustomerMkuPO>lambdaQuery()
                    .eq(CustomerMkuPO::getMkuId, existRef.getMkuId()).eq(CustomerMkuPO::getClientCode, existRef.getClientCode()));
        }

        SkuLogSaveVO saveVO = new SkuLogSaveVO(KeyTypeEnum.CUSTOMER_POOL.getCode(), target.getMkuId()+target.getClientCode()
            , JSON.toJSONString(existRef), JSON.toJSONString(target), target.getUpdater());
        skuLogAtomicService.saveLog(saveVO);
    }
    @PFTracing
    private Map<Long,CustomerMkuPriceVO> batchQueryCustomerMkuPriceMap(List<Long> mkuIds,String clientCode){

        Map<Long, List<MkuRelationPO>> mkuBindSkuListMap = mkuRelationAtomicService.queryMkuBindSkuListMap(mkuIds);
        Map<Long,MkuRelationPO> mkuSkuMap = Maps.newHashMap();
        mkuBindSkuListMap.forEach((mkuId,skuList)-> {
            // mku和sku关系简历最早为固定sku
            MkuRelationPO fixSkuPo = skuList.stream().min(Comparator.comparing(MkuRelationPO::getCreateTime)).orElse(null);
            if (null == fixSkuPo) {
                throw new CustomerMkuBindException(String.format("客户mku绑定失败:mku %s 未能找到同货源国的固定sku",mkuId));
            }
            mkuSkuMap.put(mkuId, fixSkuPo);
        });

        if (MapUtils.isEmpty(mkuSkuMap)) {
            return Collections.emptyMap();
        }

        return customerMkuPriceManageService.batchQueryCustomerSkuPriceMap(Lists.newArrayList(mkuSkuMap.values()), clientCode);
    }

    /**
     * 检查客户的MKU在开阳系统类目关系是否正确。
     * @param customer 客户信息对象
     * @param mkuId MKU的ID
     */
    @PFTracing
    private void checkRelationCategory(CustomerVO customer, Long mkuId){
        // 只有比亚迪校验类目关系
        if (!operDuccConfig.getKaiYangCategoryContract().contains(customer.getContractCode())) {
            return;
        }

        String contractCode = customer.getContractCode();
        ReflectDataDto req = new ReflectDataDto();
        req.setQueryKey(String.valueOf(mkuId));
        req.setContractNo(contractCode);
        req.setReflectType(ReflectTypeEnum.SKU_CATE.getType());

        ReflectDataDto reflectDataDto = categoryRpcService.queryReflectData(req);

        if (reflectDataDto == null) {
            throw new CustomerMkuBindException(String.format("客户mku绑定失败:mku %s 未在开阳系统绑定比亚迪类目",mkuId));
        }else if (StringUtils.isBlank(reflectDataDto.getResultValue1()) || StringUtils.isBlank(reflectDataDto.getResultValue3())
                || StringUtils.isBlank(reflectDataDto.getResultValue5()) || StringUtils.isBlank(reflectDataDto.getResultValue7())){
            throw new CustomerMkuBindException(String.format("客户mku绑定失败:mku %s 在开阳系统绑定的类目信息不全。一级类目ID: %s，二级类目ID：%s，三级类目ID：%s,四级类目ID: %s "
                    ,mkuId,reflectDataDto.getResultValue1(),reflectDataDto.getResultValue3(),reflectDataDto.getResultValue5(),reflectDataDto.getResultValue7()));
        }
    }


    @Override
    public Boolean bindCustomerToMku(CustomerMkuEachReqVO input) {
        validateParams(input);
        if(input.getBindStatus().equals(CustomerMkuBindEnum.BIND)){
            return bindMku(input);
        }else {
            return remove(input);
        }
    }
    public Boolean bindMku(CustomerMkuEachReqVO input) {
        String clientCode = input.getClientCode();
        Long mkuId = input.getMkuId();
        CustomerVO customer = getAndValidateCustomer(clientCode);
        MkuPO mkuPo = getAndValidateMku(mkuId);
        MkuRelationPO fixedSku = getAndValidateFixedSku(mkuPo);
        SkuPO skuPo = skuAtomicService.getSkuPoBySkuId(fixedSku.getSkuId());
        buildCustomerMku(customer, mkuId, skuPo, "");
        //更新客&mku&固定sku关系
        CustomerMkuPriceVO customerMkuPriceVO =
            new CustomerMkuPriceVO(mkuPo.getMkuId(), customer.getClientCode(), customer.getClientName(), mkuPo.getSourceCountryCode(), fixedSku.getSkuId(), fixedSku.getVendorCode(), customer.getCountry());
        customerMkuPriceManageService.saveOrUpdate(customerMkuPriceVO);
        log.info("CustomerMkuManageServiceImpl.publicCustomerBind 客户{}及mku{}及固定sku{}绑定完毕.", customer.getClientCode(), mkuId,
            fixedSku.getSkuId());
        return true;
    }
    public Boolean remove(CustomerMkuEachReqVO input) {
        CustomerMkuPO existRef = customerMkuAtomicService.getOne(input.getMkuId(),input.getClientCode(),CustomerMkuBindEnum.BIND);
        if(Objects.isNull(existRef)){
            return false;
        }
        //逻辑解绑客户mku关系
        customerMkuAtomicService.update(new CustomerMkuPO(CustomerMkuBindEnum.INVALID,MkuSpuRelationFlagEnum.NO),
            Wrappers.<CustomerMkuPO>lambdaUpdate().eq(CustomerMkuPO::getMkuId, input.getMkuId()).eq(CustomerMkuPO::getClientCode, input.getClientCode())
                .set(CustomerMkuPO::getSpuId,null));
        log.info("CustomerMkuManageServiceImpl.remove 客户{}移除mku:{}" , input.getClientCode() ,input.getMkuId());
        //如果当前关系的spuFlag=1,则需要选举新的mku作为主展示mku
        if(MkuSpuRelationFlagEnum.YES.getCode().equals(existRef.getSpuFlag())){
            customerMkuAtomicService.setNewLeaderMku(input.getClientCode(),existRef.getSpuId(),input.getMkuId(),existRef.getCountryCode());
        }
        //移除客户mku固定sku
        customerMkuPriceManageService.delete(new CustomerMkuPO(input.getClientCode(),input.getMkuId(),""));
        return true;
    }

    private void validateParams(CustomerMkuEachReqVO vo) {
        if(Objects.isNull(vo)){
            throw new CustomerMkuBindException(String.format("Customer MKU binding failed: request does not exist"));
        }
        if(Objects.isNull(vo.getMkuId())){
            throw new CustomerMkuBindException(String.format("Customer MKU binding failed: mkuId %s does not exist", vo));
        }
        if(StringUtils.isBlank(vo.getTargetCountryCode())){
            throw new CustomerMkuBindException(String.format("Customer MKU binding failed: targetCountryCode %s does not exist", vo));
        }
        if(Objects.isNull(vo.getBindStatus())){
            throw new CustomerMkuBindException(String.format("Customer MKU binding failed: bindStatus %s does not exist", vo));
        }
        Map<String, String> clientCodeByCountry = operDuccConfig.getClientCodeByCountry();
        if(clientCodeByCountry.containsKey(vo.getTargetCountryCode())){
            String clientCode = clientCodeByCountry.get(vo.getTargetCountryCode());
            vo.setClientCode(clientCode);
        }else {
            throw new CustomerMkuBindException(String.format("Customer MKU binding failed: clientCodeByCountry %s does not exist", clientCodeByCountry));
        }

    }


    /**
     * 根据客户代码获取并验证客户信息
     * @param clientCode 客户代码
     * @return 验证通过的客户信息
     * @throws CustomerMkuBindException 如果客户信息不存在
     */
    private CustomerVO getAndValidateCustomer(String clientCode) {
        CustomerVO customer = customerManageService.detail(clientCode);
        if (customer == null) {
            throw new CustomerMkuBindException(String.format("Customer MKU binding failed: Customer %s does not exist", clientCode));
        }
        return customer;
    }
    /**
     * 根据给定的 MKU ID 获取并验证 MKU 对象。
     * @param mkuId MKU 的唯一标识符
     * @return 对应的 MKU 对象
     * @throws CustomerMkuBindException 如果 MKU 不存在
     */
    private MkuPO getAndValidateMku(Long mkuId) {
        MkuPO mku = mkuAtomicService.getPOById(mkuId);
        if (mku == null) {
            throw new CustomerMkuBindException(String.format("Customer MKU binding failed: MKU %s does not exist", mkuId));
        }
        return mku;
    }

    /**
     * 获取并验证指定MKU的固定SKU。
     * @param mku MKU对象
     * @return 对应的MkuRelationPO对象
     */
    private MkuRelationPO getAndValidateFixedSku(MkuPO mku) {
        List<MkuRelationPO> skuRelations = mkuRelationAtomicService.queryBindListByMkuId(mku.getMkuId());
        if (CollectionUtils.isEmpty(skuRelations)) {
            throw new CustomerMkuBindException(String.format("Customer MKU binding failed: MKU %s has no bound SKUs", mku.getMkuId()));
        }
        return skuRelations.stream()
            .filter(relation -> mku.getSourceCountryCode().equals(relation.getSkuSourceCountryCode()))
            .min(Comparator.comparing(MkuRelationPO::getCreateTime))
            .orElseThrow(() -> new CustomerMkuBindException(String.format("Customer MKU binding failed: No fixed SKU found for MKU %s with matching source country", mku.getMkuId())));
    }
}
