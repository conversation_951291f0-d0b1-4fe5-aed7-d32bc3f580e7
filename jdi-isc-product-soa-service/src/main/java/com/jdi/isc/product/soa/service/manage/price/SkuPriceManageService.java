package com.jdi.isc.product.soa.service.manage.price;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.price.req.PriceUnsellableThresholdImportReqDTO;
import com.jdi.isc.product.soa.api.price.res.PriceUnsellableThresholdImportResDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.res.SkuPricePageApiDTO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceReqVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceResVO;
import com.jdi.isc.product.soa.domain.price.biz.SkuPriceVO;
import com.jdi.isc.product.soa.domain.price.po.SkuPricePO;
import com.jdi.isc.product.soa.domain.price.supplierPrice.biz.*;
import com.jdi.isc.product.soa.domain.validation.ValidatorGroup;
import com.jdi.isc.product.soa.price.api.price.req.FulfillmentRateChangeReqVO;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

/**
 * SKU价格读写接口
 * <AUTHOR>
 * @date 20231130
 **/
public interface SkuPriceManageService {

    @Validated(ValidatorGroup.saveSkuPrice.class)
    Boolean saveOrUpdate(SkuPriceReqVO input);

    @Validated(ValidatorGroup.listSkuPrice.class)
    Map<Long, SkuPriceResVO> list(SkuPriceReqVO skuPriceVO);

    SkuPricePO getOne(SkuPriceVO skuPriceVO);

    void negativeProfit(SkuPriceReqVO input);

    /** 触发对应SKU的国家成本价、入仓成本价重算*/
    Boolean triggerCostPriceRefresh(List<FulfillmentRateChangeReqVO> input);

    PageInfo<SkuPricePageVO> pageSearch(SkuPricePageReqVO input);


    SkuPriceVO detailById(SkuPriceQueryReqVO reqVO);

    SkuPriceVO detailBySkuId(SkuPriceQueryReqVO reqVO);


    List<SkuPurchasePriceVO> getPurchasePrice(SkuPurchasePriceReqVO reqVO);

    /**
     * 更新不可售价格阈值
     * @param input 不可售价格阈值导入请求参数，包含需要更新的阈值信息
     * @return 包含不可售价格阈值导入响应结果的数据响应对象
     */
    DataResponse<PriceUnsellableThresholdImportResDTO> updateUnsellableThreshold(PriceUnsellableThresholdImportReqDTO input);
}
