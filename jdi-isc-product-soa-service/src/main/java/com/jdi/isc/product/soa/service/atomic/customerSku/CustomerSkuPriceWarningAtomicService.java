package com.jdi.isc.product.soa.service.atomic.customerSku;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.exception.ProductBizException;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceWarningPageReqVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceWarningVO;
import com.jdi.isc.product.soa.domain.customerSku.po.CustomerSkuPriceWarningPO;
import com.jdi.isc.product.soa.repository.mapper.customerSku.CustomerSkuPriceWarningBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: sku客制化价格预警表原子服务
 * @Author: zhaokun51
 * @Date: 2025/03/17 16:51
 **/
@Slf4j
@Service
public class CustomerSkuPriceWarningAtomicService extends ServiceImpl<CustomerSkuPriceWarningBaseMapper, CustomerSkuPriceWarningPO> {


    /**
     * 未删除的对象
     * @param id id
     * @return 对象
     */
    public CustomerSkuPriceWarningPO getValidById(Long id){
        if(id == null){
            return null;
        }
        LambdaQueryWrapper<CustomerSkuPriceWarningPO> wrapper = Wrappers.<CustomerSkuPriceWarningPO>lambdaQuery()
                .eq(CustomerSkuPriceWarningPO::getId, id)
                .eq(CustomerSkuPriceWarningPO::getYn, YnEnum.YES.getCode());
        return super.getOne(wrapper);
    }

    public List<CustomerSkuPriceWarningPO> listByBizIds(List<Long> bizIds){
        if(CollectionUtils.isEmpty(bizIds)){
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<CustomerSkuPriceWarningPO> wrapper = Wrappers.<CustomerSkuPriceWarningPO>lambdaQuery()
                .in(CustomerSkuPriceWarningPO::getBizId, bizIds)
                .eq(CustomerSkuPriceWarningPO::getYn, YnEnum.YES.getCode())
                .orderByDesc(CustomerSkuPriceWarningPO::getUpdateTime);
        List<CustomerSkuPriceWarningPO> warningPOS = this.list(wrapper);
        if(CollectionUtils.isEmpty(warningPOS)){
            return Lists.newArrayList();
        }

        Map<Long, CustomerSkuPriceWarningPO> map = warningPOS.stream().collect(Collectors.toMap(CustomerSkuPriceWarningPO::getBizId, Function.identity(), (k1, k2) -> k1));

        return CollectionUtil.sort(map.values(), Comparator.comparing(CustomerSkuPriceWarningPO::getId, Comparator.nullsLast(Long::compareTo)).reversed());
    }

    /**
     * 未删除的对象
     * @param bizId id
     * @return 对象
     */
    public CustomerSkuPriceWarningPO getValidByBizId(Long bizId){
        if(bizId == null){
            return null;
        }
        LambdaQueryWrapper<CustomerSkuPriceWarningPO> wrapper = Wrappers.<CustomerSkuPriceWarningPO>lambdaQuery()
                .eq(CustomerSkuPriceWarningPO::getBizId, bizId)
                .eq(CustomerSkuPriceWarningPO::getYn, YnEnum.YES.getCode());
        List<CustomerSkuPriceWarningPO> warningPOS = this.list(wrapper);
        if(CollectionUtils.isEmpty(warningPOS)){
            return null;
        }
        return warningPOS.stream().sorted(Comparator.comparing(CustomerSkuPriceWarningPO::getUpdateTime).reversed()).collect(Collectors.toList()).get(0);
    }

    public List<CustomerSkuPriceWarningPO> listBySkuIds(List<Long> skuIds) {
        if(CollectionUtils.isEmpty(skuIds)){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CustomerSkuPriceWarningPO> wrapper = Wrappers.<CustomerSkuPriceWarningPO>lambdaQuery()
                .in(CustomerSkuPriceWarningPO::getSkuId, skuIds)
                .eq(CustomerSkuPriceWarningPO::getYn, YnEnum.YES.getCode());

        return this.list(wrapper);
    }

    public List<CustomerSkuPriceWarningPO> listWarningsByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CustomerSkuPriceWarningPO> wrapper = Wrappers.<CustomerSkuPriceWarningPO>lambdaQuery()
                .in(CustomerSkuPriceWarningPO::getId, ids)
                .eq(CustomerSkuPriceWarningPO::getYn, YnEnum.YES.getCode());
        return super.list(wrapper);
    }

    public long getTotal(CustomerSkuPriceWarningPageReqVO input) {
        return super.getBaseMapper().getTotal(input);
    }

    public List<CustomerSkuPriceWarningVO> listSearch(CustomerSkuPriceWarningPageReqVO input) {
        return super.getBaseMapper().listSearch(input);
    }

    public void updateAvailableSaleStatusList(List<CustomerSkuPriceWarningPO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }

        // 排序
        CollectionUtil.sort(updateList, Comparator.comparing(CustomerSkuPriceWarningPO::getId, Comparator.nullsLast(Long::compareTo)));

        for (CustomerSkuPriceWarningPO item : updateList) {
            this.updateAvailableSaleStatus(item);
        }
    }

    public void updateAvailableSaleStatus(CustomerSkuPriceWarningPO update) {
        Preconditions.checkArgument(update.getId() != null, "id不能为空");

        LambdaUpdateWrapper<CustomerSkuPriceWarningPO> updateWrapper = Wrappers.lambdaUpdate(CustomerSkuPriceWarningPO.class)
                .set(CustomerSkuPriceWarningPO::getAvailableSaleStatus, update.getAvailableSaleStatus())
                .set(CustomerSkuPriceWarningPO::getCountryMkuPoolStatus, update.getCountryMkuPoolStatus())
                .set(CustomerSkuPriceWarningPO::getCustomerMkuPoolStatus, update.getCustomerMkuPoolStatus())

//                .set(CustomerSkuPriceWarningPO::getUpdateTime, new Date().getTime())
                .set(CustomerSkuPriceWarningPO::getUpdater, update.getUpdater())

                .eq(CustomerSkuPriceWarningPO::getId, update.getId())
                .eq(CustomerSkuPriceWarningPO::getYn, YnEnum.YES.getCode());

        boolean flag = super.update(updateWrapper);

        log.info("更新vip价预警可售状态. result=[{}], update={}", flag, JSONObject.toJSONString(update));

        if (!flag) {
            log.info("更新vip价预警可售状态失败, update={}", JSONObject.toJSONString(update));
            throw new ProductBizException("客制化价格预警正在进行其他业务操作，请稍后重试 %s", update.getSkuId());
        }
    }
}
