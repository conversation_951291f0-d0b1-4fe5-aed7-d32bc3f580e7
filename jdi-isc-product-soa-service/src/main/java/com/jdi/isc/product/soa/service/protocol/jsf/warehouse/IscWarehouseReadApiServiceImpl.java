package com.jdi.isc.product.soa.service.protocol.jsf.warehouse;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.jdi.yz.core.utils.StringUtils;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.enums.PurchaseModelTypeEnum;
import com.jdi.isc.product.soa.api.warehouse.IscWarehouseReadApiService;
import com.jdi.isc.product.soa.api.warehouse.base.WarehouseDTO;
import com.jdi.isc.product.soa.api.warehouse.base.WarehouseSkuDTO;
import com.jdi.isc.product.soa.api.warehouse.req.*;
import com.jdi.isc.product.soa.api.warehouse.req.WarehouseBatchSkuReqDTO;
import com.jdi.isc.product.soa.api.warehouse.res.*;
import com.jdi.isc.product.soa.common.util.NumberUtils;
import com.jdi.isc.product.soa.domain.enums.warehouse.WarehouseUnBindType;
import com.jdi.isc.product.soa.domain.supplier.po.SupplierBaseInfoPO;
import com.jdi.isc.product.soa.domain.warehouse.biz.*;
import com.jdi.isc.product.soa.rpc.tde.TdeClientRpcService;
import com.jdi.isc.product.soa.rpc.vc.SuppilerInfoRpcService;
import com.jdi.isc.product.soa.service.atomic.supplier.SupplierBaseInfoAtomicService;
import com.jdi.isc.product.soa.service.manage.warehouse.WarehouseManageService;
import com.jdi.isc.product.soa.service.manage.warehouse.WarehouseSkuManageService;
import com.jdi.isc.product.soa.service.mapstruct.warehouse.WarehouseConvert;
import com.jdi.isc.vc.soa.api.supplier.res.SupplierWarehouseRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.common.constants.Constant.PAGE_SIZE;
import static com.jdi.isc.product.soa.common.constants.Constant.WMS_SKU_MAX_PAGE_SIZE;

/**
 * 仓库查询服务
 *
 * <AUTHOR>
 * @date 2024/7/23
 **/
@Slf4j
@Service
public class IscWarehouseReadApiServiceImpl implements IscWarehouseReadApiService {

    @Resource
    private WarehouseManageService warehouseManageService;

    @Resource
    private TdeClientRpcService tdeClientRpcService;

    @Resource
    private WarehouseSkuManageService warehouseSkuManageService;

    @Resource
    private SupplierBaseInfoAtomicService supplierBaseInfoAtomicService;

    @Resource
    private SuppilerInfoRpcService suppilerInfoRpcService;

    @LafValue("jdi.isc.product.soa.querySku.switch")
    private Boolean querySkuSwitch;


    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<WarehouseResDTO>> searchWarehouse(WarehousePageReqDTO pageReqDTO) {
        WarehousePageReqVO warehousePageReqVO = WarehouseConvert.INSTANCE.searchDto2Vo(pageReqDTO);
        PageInfo<WarehouseResVO> pageInfo = warehouseManageService.search(warehousePageReqVO);
        PageInfo<WarehouseResDTO> resultPageInfo = WarehouseConvert.INSTANCE.pageResVo2Dto(pageInfo);
        List<WarehouseResDTO> records = resultPageInfo.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            for (WarehouseResDTO record : records) {
                record.setConsignee(tdeClientRpcService.decrypt(record.getConsignee()));
                record.setConsigneeAddress(tdeClientRpcService.decrypt(record.getConsigneeAddress()));
                record.setConsigneeEmail(tdeClientRpcService.decrypt(record.getConsigneeEmail()));
                record.setConsigneePhone(tdeClientRpcService.decrypt(record.getConsigneePhone()));
                record.setConsigneeMobile(tdeClientRpcService.decrypt(record.getConsigneeMobile()));
            }
        }
        return DataResponse.success(resultPageInfo);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<WarehouseDetailResDTO> getWarehouseDetail(WarehouseGetReqDTO reqDTO) {

        WarehouseDetailVO warehouseDetailVO = warehouseManageService.detail(reqDTO.getWarehouseId());
        WarehouseDetailResDTO warehouseDetailResDTO = WarehouseConvert.INSTANCE.detailVo2Dto(warehouseDetailVO);
        if (Objects.nonNull(warehouseDetailVO) && Objects.nonNull(warehouseDetailVO.getWarehouseVO())) {
            WarehouseVO warehouseVO = warehouseDetailVO.getWarehouseVO();
            WarehouseDTO warehouseDTO = warehouseDetailResDTO.getWarehouseDTO();
            warehouseDTO.setConsignee(this.decrypt(warehouseVO.getConsigneeEncrypt()));
            warehouseDTO.setConsigneeAddress(this.decrypt(warehouseVO.getConsigneeAddressEncrypt()));
            warehouseDTO.setConsigneePhone(this.decrypt(warehouseVO.getConsigneePhoneEncrypt()));
            warehouseDTO.setConsigneeMobile(this.decrypt(warehouseVO.getConsigneeMobileEncrypt()));
            warehouseDTO.setConsigneeEmail(this.decrypt(warehouseVO.getConsigneeEmailEncrypt()));
        }
        return DataResponse.success(warehouseDetailResDTO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<WarehouseSkuResDTO>> querySkusByCondition(WarehouseSkuPageReqDTO reqDTO) {
        if (reqDTO.getSize() > WMS_SKU_MAX_PAGE_SIZE) {
            return DataResponse.error("分页参数超过仓-商品接口最大查询限制数:" + WMS_SKU_MAX_PAGE_SIZE);
        }

        if (StringUtils.isNotBlank(reqDTO.getSkuIdOrName()) && !NumberUtils.isPositiveNumeric(reqDTO.getSkuIdOrName())) {
            return DataResponse.error("输入的skuId非数字类型!");
        }

        WarehouseSkuPageVO getReqVO = WarehouseConvert.INSTANCE.getReqDTO2Vo(reqDTO);
        getReqVO.setPurchaseModel(PurchaseModelTypeEnum.STOCK_UP.getCode());

        PageInfo<WarehouseSkuResVO> pageInfo;
        if (querySkuSwitch) {
            pageInfo = warehouseSkuManageService.queryWarehouseTopSkusByCondition(getReqVO);
        } else {
            pageInfo = warehouseSkuManageService.querySkusByCondition(getReqVO);
        }

        return DataResponse.success(WarehouseConvert.INSTANCE.pageSkuResVo2DTO(pageInfo));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<WarehouseResDTO>> queryWarehouseByCondition(WarehouseBatchGetReqDTO reqDTO) {
        WarehouseBatchGetReqVO batchGetReqVO = WarehouseConvert.INSTANCE.batchGetDTO2VO(reqDTO);
        List<WarehouseResVO> warehouseResVOList = warehouseManageService.queryWarehouseByCondition(batchGetReqVO);
        return DataResponse.success(WarehouseConvert.INSTANCE.listResVo2Dto(warehouseResVOList));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<String, Map<String, WarehouseSkuDTO>>> querySkuWarehouseRelationMap(QuerySkuRelationReqDTO reqDTO) {
        Map<String, Map<String, WarehouseSkuVO>> skuWarehouseRelationMap = warehouseSkuManageService.querySkuWarehouseRelationMap(new QuerySkuRelationReqVO(reqDTO.getSkuIds()));
        if (MapUtils.isEmpty(skuWarehouseRelationMap)) {
            return DataResponse.success(Collections.emptyMap());
        }
        Map<String, Map<String, WarehouseSkuDTO>> resultMap = Maps.newHashMap();
        skuWarehouseRelationMap.forEach((skuId, skuRelationVoMap) -> {
            Map<String, WarehouseSkuDTO> skuResDTOMap = Maps.newHashMap();
            if (MapUtils.isNotEmpty(skuRelationVoMap)) {
                skuRelationVoMap.forEach((warehouseId, skuVO) -> {
                    skuResDTOMap.put(warehouseId, WarehouseConvert.INSTANCE.warehouseSkuVo2Dto(skuVO));
                });
            }
            resultMap.put(skuId, skuResDTOMap);
        });
        return DataResponse.success(resultMap);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<WarehouseSkuResDTO>> batchSkusByCondition(WarehouseBatchSkuReqDTO batchSkuReq) {
        WarehouseBatchSkuVo getReqVO = WarehouseConvert.INSTANCE.batchSkuReq2Vo(batchSkuReq);
        List<WarehouseSkuResVO> skuList = warehouseSkuManageService.batchSkusByCondition(getReqVO);

        //补充仓-商品扩展信息
        if (CollectionUtils.isNotEmpty(skuList) && Objects.equals(batchSkuReq.getPurchaseModel(), PurchaseModelTypeEnum.CONSIGNMENT.getCode())) {
            Set<String> vendorCodes = skuList.stream().map(WarehouseSkuResVO::getVendorCode).collect(Collectors.toSet());

            //补充寄售商品供应商名称、货主编码等信息
            String warehouseId = String.valueOf(batchSkuReq.getWarehouseId());
            boolean isCrossSku = CountryConstant.COUNTRY_ZH.equals(batchSkuReq.getSourceCountryCode());
            try {
                this.fillConsignSkuExtraInfo(vendorCodes, skuList, isCrossSku, warehouseId);
            } catch (Exception e) {
                log.error("batchSkusByCondition failed to fillConsignSkuExtraInfo, request:{}", JSON.toJSONString(batchSkuReq), e);
            }
        }

        return DataResponse.success(WarehouseConvert.INSTANCE.listSkuResVO2DTO(skuList));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<WarehouseConsignSkuResDTO>> queryConsignmentSkusByCondition(WarehouseSkuPageReqDTO reqDTO) {
        WarehouseSkuPageVO getReqVO = WarehouseConvert.INSTANCE.getReqDTO2Vo(reqDTO);
        getReqVO.setPurchaseModel(PurchaseModelTypeEnum.CONSIGNMENT.getCode());

        if (reqDTO.getSize() > WMS_SKU_MAX_PAGE_SIZE) {
            return DataResponse.error("分页参数超过仓-商品接口最大查询限制数:" + WMS_SKU_MAX_PAGE_SIZE);
        }

        if (StringUtils.isNotBlank(reqDTO.getSkuIdOrName()) && !NumberUtils.isPositiveNumeric(reqDTO.getSkuIdOrName())) {
            return DataResponse.error("输入的skuId非数字类型!");
        }

        PageInfo<WarehouseSkuResVO> pageInfo;
        if (querySkuSwitch) {
            pageInfo = warehouseSkuManageService.queryWarehouseTopSkusByCondition(getReqVO);
        } else {
            pageInfo = warehouseSkuManageService.querySkusByCondition(getReqVO);
        }

        if (CollectionUtils.isNotEmpty(pageInfo.getRecords())) {
            List<WarehouseSkuResVO> warehouseSkuResVOList = pageInfo.getRecords();
            boolean isCrossSku = CountryConstant.COUNTRY_ZH.equals(reqDTO.getSourceCountryCode());
            Set<String> vendorCodes = warehouseSkuResVOList.stream().map(WarehouseSkuResVO::getVendorCode).collect(Collectors.toSet());

            //补充寄售商品供应商名称、货主编码等信息
            String warehouseId = String.valueOf(reqDTO.getWarehouseId());
            try {
                this.fillConsignSkuExtraInfo(vendorCodes, warehouseSkuResVOList, isCrossSku, warehouseId);
            } catch (Exception e) {
                log.error("queryConsignmentSkusByCondition failed to fillConsignSkuExtraInfo, request:{}", JSON.toJSONString(reqDTO), e);
            }
        }

        return DataResponse.success(WarehouseConvert.INSTANCE.pageSkuResVo2ConsignDTO(pageInfo));
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<WarehouseSkuUnBindResDTO>> validateWarehouseSkuUnbindRelation(Set<WarehouseSkuUnBindItemReqDTO> warehouseSkuUnBindReqs) {
        //批量校验数目限制
        if (warehouseSkuUnBindReqs.size() > WMS_SKU_MAX_PAGE_SIZE) {
            return DataResponse.error("分页参数超过校验最大查询限制数:" + WMS_SKU_MAX_PAGE_SIZE);
        }

        //备货仓解绑参数合法性校验
        boolean hasInvalidRequest = warehouseSkuUnBindReqs.stream()
                .anyMatch(req -> Objects.equals(WarehouseUnBindType.UNBIND.getCode(),req.getUnBindType())
                        && Objects.isNull(req.getWarehouseId()));
        if (hasInvalidRequest) {
            return DataResponse.error("解绑查询验证，备货仓ID不能传值为空!");
        }

        //执行解绑校验
        Set<WarehouseSkuUnBindVO>  warehouseSkuUnBindVOSet = WarehouseConvert.INSTANCE.warehouseSkuUnBindReqDTOToVO(warehouseSkuUnBindReqs);
        List<WarehouseSkuUnBindResVO> warehouseSkuUnBindResDTOList = warehouseSkuManageService.queryWarehouseSkuUnbindRelation(warehouseSkuUnBindVOSet);

        return DataResponse.success(WarehouseConvert.INSTANCE.warehouseSkuUnBindResVOToDTO(warehouseSkuUnBindResDTOList));
    }

    /**
     * 填充寄售商品额外信息，包括供应商名称等
     *
     * @param vendorCodes           供应商编码集合
     * @param warehouseSkuResVOList 仓库商品响应VO列表
     * @param isCrossSku            是否跨商品标识
     */
    private void fillConsignSkuExtraInfo(Set<String> vendorCodes, List<WarehouseSkuResVO> warehouseSkuResVOList, boolean isCrossSku, String warehouseId) {
        //获取供应商名称
        Map<String, SupplierBaseInfoPO> supplierBaseMap = getPagedSupplierBaseInfo(vendorCodes);

        //获取货主信息
        List<SupplierWarehouseRes> supplierWarehouseRes = suppilerInfoRpcService.getSupplierWarehouseList(vendorCodes);
        Map<String, SupplierWarehouseRes> supplierWarehouseResMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(supplierWarehouseRes)) {
            //同一个仓+同一个供应商只对应一个货主编码，后续如果对应多个，此处需要迭代
            supplierWarehouseResMap = supplierWarehouseRes
                    .stream()
                    .filter(supplierWarehouse -> Objects.equals(supplierWarehouse.getWarehouseId(), warehouseId))
                    .collect(Collectors.toMap(
                            SupplierWarehouseRes::getSupplierCode,
                            Function.identity(),
                            (existing, replacement) -> existing // 保留第一个值
                    ));
        }

        for (WarehouseSkuResVO warehouseSkuResVO : warehouseSkuResVOList) {
            String vendorCode = warehouseSkuResVO.getVendorCode();

            //设置本本供应商名称
            if (!org.springframework.util.CollectionUtils.isEmpty(supplierBaseMap) && !isCrossSku) {
                String supplierName = supplierBaseMap.getOrDefault(vendorCode, new SupplierBaseInfoPO())
                        .getBusinessLicenseName();
                warehouseSkuResVO.setSupplierName(supplierName);
            }

            //设置货主编码
            if (!org.springframework.util.CollectionUtils.isEmpty(supplierWarehouseResMap)) {
                Optional.ofNullable(supplierWarehouseResMap.get(vendorCode))
                        .ifPresent(supplierWarehouseInfo ->
                                warehouseSkuResVO.setConsignorCode(supplierWarehouseInfo.getCustomerDeptCode())
                        );
            }
        }
    }

    private String decrypt(String value) {
        return tdeClientRpcService.decrypt(value);
    }


    /**
     * 根据供应商编码集合分页查询供应商基础信息
     *
     * @param vendorCodes 供应商编码集合，用于查询对应的供应商基础信息
     * @return 包含供应商编码与对应供应商基础信息的映射表
     */
    private Map<String, SupplierBaseInfoPO> getPagedSupplierBaseInfo(Set<String> vendorCodes) {
        Map<String, SupplierBaseInfoPO> supplierBaseMap = new HashMap<>();

        // 将 vendorCodes 转换为 List
        List<String> vendorCodeList = new ArrayList<>(vendorCodes);

        // 使用 Guava 的 Lists.partition 方法进行分页
        List<List<String>> partitions = Lists.partition(vendorCodeList, PAGE_SIZE);

        for (List<String> batch : partitions) {
            // 执行分页查询
            List<SupplierBaseInfoPO> supplierBaseInfoPOS = supplierBaseInfoAtomicService.listBySupplierCode(new HashSet<>(batch));

            if (CollectionUtils.isNotEmpty(supplierBaseInfoPOS)) {
                Map<String, SupplierBaseInfoPO> batchResult = supplierBaseInfoPOS
                        .stream()
                        .collect(Collectors.toMap(SupplierBaseInfoPO::getSupplierCode, Function.identity()));
                supplierBaseMap.putAll(batchResult);
            }

            log.info("getPagedSupplierBaseInfo batch vendorCodes:{}, supplierBaseMap:{}", batch, JSON.toJSONString(supplierBaseInfoPOS));
        }

        return supplierBaseMap;
    }

}
