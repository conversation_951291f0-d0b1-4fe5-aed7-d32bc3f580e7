package com.jdi.isc.product.soa.service.manage.countryMku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuCheckReqDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientInPoolApiDTO;
import com.jdi.isc.product.soa.domain.countryMku.biz.*;
import com.jdi.isc.product.soa.domain.mku.biz.MkuClientInPoolVO;
import com.jdi.isc.product.soa.domain.mku.biz.MkuClientUpSaleStatusVO;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangExtendVO;

import java.util.List;
import java.util.Map;

/**
 * @Description: 商品国家池表数据维护服务
 * @Author: wangpeng965
 * @Date: 2024/12/02 21:49
 **/

public interface CountryMkuManageService {

    /**
     * 保存或更新国家MKU信息。
     * @param input 要保存或更新的国家MKU信息对象。
     * @return 操作是否成功。
     */
    DataResponse<String> saveOrUpdate(CountryMkuVO input);
    /**
     * 详情
     * @param id 对象ID
     * @return VO对象
     */
    CountryMkuVO detail(Long id);

    /**
     * 删除-物理
     * @param id 对象ID
     * @return VO对象
     */
    Boolean delete(Long id);



    /**
     * 批量删除指定范围内的数据。
     * @param beginId 开始ID
     * @param endId 结束ID
     * @return 删除是否成功
     */
    Boolean batchDelete(Long beginId, Long endId);

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    PageInfo<CountryMkuPageVO.Response> pageSearch(CountryMkuPageVO.Request input);

    /**
     * 获取审核状态数量。
     * @return 审核状态数量列表。
     */
    List<CountryMkuPageVO.Request> auditStatusNum(CountryMkuPageVO.Request input);

    /**
     * 批量将指定国家的商品添加到黑名单中。
     * @param input 包含要操作的国家信息和商品信息的对象。
     * @return 操作结果的描述信息。
     */
    DataResponse<String> batchBlack(CountryMkuApproveVO input);

    /**
     * 批量将指定国家的商品从黑名单中移除
     * @param input 包含要移除的国家信息和商品信息的对象
     * @return 执行结果的描述信息
     */
    DataResponse<String> batchOutBlack(CountryMkuApproveVO input);

    /**
     * 批量处理国家MKU审批请求。
     * @param input 包含待处理的国家MKU审批信息。
     * @return 处理结果。
     */
    DataResponse<String> batchPool(CountryMkuApproveVO input);

    /**
     * 功能：国家池指定国家的MKU池。
     * @param reqVO 包含国家信息的请求对象。
     * @return 一个DataResponse对象，包含了操作结果的布尔值。
     */
    DataResponse<Boolean> mkuPoolJoinCountryPool(CountryMkuReqVO reqVO);

    /**
     * 客户池加入国家池。
     * @param reqVO 国家MKU请求对象，包含客户ID和国家池ID等信息。
     * @return 操作结果，true表示成功，false表示失败。
     */
    DataResponse<Boolean> customerPoolJoinCountryPool(CountryMkuReqVO reqVO);

    /**
     * 加入国家MKU池
     * @param reqVO 国家MKU请求对象
     * @return 操作结果
     */
    DataResponse<Boolean> mkuMsgJoinCountryPool(CountryMkuReqVO reqVO);

    /**
     * 将指定的 京东SKU ID 和国家信息关联到消息池中。
     * @param reqVO 包含 SKU ID 和国家信息的请求对象。
     * @return 操作结果，true 表示成功，false 表示失败。
     */
    DataResponse<Boolean> jdSkuIdMsgJoinCountryPool(CountryMkuReqVO reqVO);


    /**
     * 检查黑名单数据。
     * @param reqVO 请求参数对象，包含需要检查的数据。
     * @return 包含检查结果的响应对象。
     */
    DataResponse<CountryMkuCheckVO> checkBlackData(CountryMkuCheckReqVO reqVO);

    /**
     * 检查黑名单数据。
     * @param reqVO 国别MKU检查请求对象。
     * @return 包含国别MKU检查结果的DataResponse对象。
     */
    DataResponse<CountryMkuCheckVO> checkOutBlackData(CountryMkuCheckReqVO reqVO);

    /**
     * 在 MKU 客户池中检查指定国家的客户是否存在。
     * @param input 包含要检查的国家信息的请求对象。
     * @return 如果客户在池中，返回包含客户信息的 DataResponse 对象；否则返回空的 DataResponse 对象。
     */
    DataResponse<List<MkuClientInPoolVO>> checkInPool(CountryMkuCheckReqVO input);

    /**
     * 检查指定国家的MKU客户上行销售状态。
     * @param input 包含要检查的国家信息的请求对象。
     * @return 上行销售状态列表。
     */
    DataResponse<List<MkuClientUpSaleStatusVO>> checkUpSaleStatus(CountryMkuCheckReqVO input);

    DataResponse<Boolean> anewMkuJoinCountryPool(CountryMkuReqVO reqVO);

    PageInfo<CountryMkuExportVO> queryCountryMku(CountryMkuPageVO.Request input);

    DataResponse<String> ruleCheckCountryMku(String countryCode, Integer warnStatus);

    /**
     * 更新mku多语言商品名称和翻译确认
     * @param mkuId 
     * @param countryCode 目标国家池code
     * @param langNameMap key:语言code, value:该语言的商品名称
     * @return
     */
    DataResponse<Boolean> updateMkuSpuTitleLangAndApprovalTranslation(Long mkuId, String countryCode, Map<String, String> langNameMap);

    /**
     * 获取目标国际池mku待翻译确认标题
     * @param mkuId 
     * @param targetCountryCode 目标国家池code
     * @return
     */
    DataResponse<List<SpuLangExtendVO>> getMkuSpuTitleLang(Long mkuId, String targetCountryCode);
}
