<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.international.soa.dao.cart.CartWaresMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jd.international.soa.domain.common.cart.CartWares">
        <id column="id" property="id" />
        <result column="cart_id" property="cartId" />
        <result column="sku" property="sku" />
        <result column="sku_num" property="skuNum" />
        <result column="checked" property="checked" />
        <result column="add_cart_price" property="addCartPrice" />
        <result column="add_cart_time" property="addCartTime" />
        <result column="pin" property="pin" />
        <result column="del" property="del"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cart_id, sku, sku_num, checked, add_cart_price, add_cart_time, pin,del
    </sql>
    <insert id="batchInsert">
        insert into international_cart_wares (cart_id, sku, sku_num, checked, add_cart_price, add_cart_time, pin,del)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.cartId}, #{entity.sku},#{entity.skuNum}, #{entity.checked}, #{entity.addCartPrice}, #{entity.addCartTime},#{entity.pin}, #{entity.del})
        </foreach>
    </insert>
    <update id="delCartWares">
        update international_cart_wares set del = 1
        where sku in
        <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        and cart_id = #{cartId}
    </update>
    <update id="updateCartWaresNum">
        update international_cart_wares set sku_num = #{num}
        where cart_Id = #{cartId} and sku = #{sku}
    </update>
    <update id="updateCartWaresChecked">
        update international_cart_wares set checked = #{checked}
        where sku in
        <foreach collection="skus" item="sku" open="(" close=")" separator=",">
            #{sku}
        </foreach>
        and cart_id = #{cartId}
    </update>
    <update id="batchUpdate">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update international_cart_wares
            <set>
                `sku_num`= #{item.skuNum},
                `checked` = #{item.checked},
                `add_cart_time` = #{item.addCartTime}
            </set>
            where `id` = #{item.id}
        </foreach>
    </update>
    <update id="delCartAllWares">
        update international_cart_wares set del = 1 where cart_id = #{cartId}
    </update>
    <delete id="delete">
        delete from international_cart_wares
    </delete>
    <select id="getCartWaresByCartId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from international_cart_wares where cart_id = #{cartId} and del = 0
        <if test="checked != null">
            and checked = #{checked}
        </if>
        order by add_cart_time desc
    </select>
    <select id="getCartWaresBySku" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from international_cart_wares where cart_id = #{cartId}
        and sku in
        <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>and del = 0
    </select>

</mapper>
