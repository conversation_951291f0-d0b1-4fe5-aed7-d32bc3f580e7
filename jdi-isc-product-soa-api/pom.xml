<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jdi.isc.product.soa</groupId>
    <artifactId>jdi-isc-product-soa-api</artifactId>
    <version>1.1.36${lib.version}</version>

    <name>jdi-isc-product-soa-api</name>
    <packaging>jar</packaging>

    <properties>
        <java.version>1.8</java.version>
        <encoding>UTF-8</encoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencies>
        <!-- 校验 -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>6.0.18.Final</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83-jdsec.rc1</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <artifactId>common-domain</artifactId>
            <groupId>com.jdi.common</groupId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>5.3.25</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.jd.masterdata</groupId>
            <artifactId>jd-masterdata-base</artifactId>
            <version>1.2.5</version>
        </dependency>


    </dependencies>

    <distributionManagement>
        <repository>
            <id>libs-releases-local</id>
            <name>Release Repository</name>
            <url>http://artifactory.jd.com/libs-releases-local</url>
        </repository>
        <snapshotRepository>
            <id>libs-snapshots-local</id>
            <name>Snapshot Repository</name>
            <url>http://artifactory.jd.com/libs-snapshots-local</url>
        </snapshotRepository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>test</id>
            <properties>
                <lib.version>-test-SNAPSHOT</lib.version>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <lib.version>-test-SNAPSHOT</lib.version>
            </properties>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <lib.version>-SNAPSHOT</lib.version>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <lib.version></lib.version><!-- 生产环境版本 -->
            </properties>
        </profile>
    </profiles>

</project>
