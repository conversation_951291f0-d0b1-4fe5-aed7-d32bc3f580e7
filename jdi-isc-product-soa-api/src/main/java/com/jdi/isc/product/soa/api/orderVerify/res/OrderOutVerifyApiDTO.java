package com.jdi.isc.product.soa.api.orderVerify.res;

import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OrderOutVerifyApiDTO  extends BasicDTO {
    /**
     * 订单id
     */

    private Long orderId;

    /**
     * 客户编码
     */

    private String clientCode;

    /**
     * 客户名称
     */
    private String clientName;
    /**
     * 下单时间
     */

    private Long orderCreateTime;

    /**
     * SKUID
     */

    private Long skuId;

    /**
     * sku的商品名称
     */
    private String skuName;

    /**
     * JDSKUID
     */
    private Long jdSkuId;

    /**
     * 目的国
     */

    private String countryCode;

    /**
     * 目的国名称
     */
    private String countryName;

    /**
     * 缺失的跨境属性
     */
    private String loseProperty;

    /**
     * 缺失的跨境资质
     */
    private String loseCertificate;
    /**
     * spuId
     */
    private Long spuId;
    /**
     * 原始父订单号
     */
    private Long parentOrderId;
    /**
     * 被拆分的订单号
     */
    private Long splitOrderId;
}
