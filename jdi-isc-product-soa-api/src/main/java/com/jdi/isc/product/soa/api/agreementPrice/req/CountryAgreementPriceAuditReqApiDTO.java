package com.jdi.isc.product.soa.api.agreementPrice.req;


import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;


/**
 * 客户MKU关系实体
 * <AUTHOR>
 * @date 2025/03/10 13:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CountryAgreementPriceAuditReqApiDTO extends BasePageReqDTO {

    /** skuIds */
    private List<Long> skuIds;

    /** mkuIds */
    private List<Long> mkuIds;

    /** 品牌 */
    private Long brandId;

    /** 一级类目 */
    private Long firstCatId;

    /** 二级类目 */
    private Long secondCatId;

    /** 三级类目 */
    private Long thirdCatId;

    /** 四级类目 */
    private Long lastCatId;

    /** 申请单号 */
    private String applyCode;

    /** 利润率 */
    private BigDecimal profitRateBegin;

    /** 利润率 */
    private BigDecimal profitRateEnd;

    /** 预警情况 */
    private String warningMsg;

    /** 发起人erp */
    private String updater;

    /** 采销 */
    private String buyer;
}
