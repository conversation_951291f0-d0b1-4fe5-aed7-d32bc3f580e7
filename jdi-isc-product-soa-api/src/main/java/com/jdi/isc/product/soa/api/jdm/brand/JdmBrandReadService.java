package com.jdi.isc.product.soa.api.jdm.brand;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.jdm.brand.req.JdmBrandPageReqDTO;
import com.jdi.isc.product.soa.api.jdm.brand.res.JdmBrandResDTO;

/**
 * 对接京麦品牌查询接口
 * <AUTHOR>
 * @date 2024/7/3
 **/
public interface JdmBrandReadService {

    /**
     * 分页查询品牌信息
     * @param pageReqDTO 分页入参
     * @return 品牌列表
     */
    DataResponse<PageInfo<JdmBrandResDTO>> page(JdmBrandPageReqDTO pageReqDTO);
}
