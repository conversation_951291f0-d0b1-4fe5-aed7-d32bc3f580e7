package com.jdi.isc.product.soa.api.mkuTag;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.mkuTag.res.MkuTagApiDTO;
import com.jdi.isc.product.soa.api.mkuTag.res.MkuTagPageApiDTO;

/**
 * @Description: 国际商品标签表api服务
 * @Author: wangpeng965
 * @Date: 2024/12/17 13:47
 **/

public interface IscMkuTagWriteApiService {

    /**
     * 重置MKU标签绑定状态
     * @param dto MKU标签请求DTO，包含需要重置绑定状态的MKU标签信息
     * @return 重置绑定状态的结果，true表示成功，false表示失败
     */
    DataResponse<Boolean> resetBinding(MkuTagApiDTO dto);
}
