package com.jdi.isc.product.soa.api.attribute;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.attribute.common.AttributeDTO;
import com.jdi.isc.product.soa.api.attribute.req.AttributeQueryReqDTO;
import com.jdi.isc.product.soa.api.attribute.req.AttributeVcQueryReqDTO;
import com.jdi.isc.product.soa.api.attribute.res.AttributeFlatDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyValueApiDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 查询属性列表
 * <AUTHOR>
 * @date 2024/9/1
 **/
public interface IscAttributeReadApiService {

    /**
     * 查询属性列表
     * @param reqDTO 属性查询请求对象
     * @return 属性DTO列表的数据响应
     */
    DataResponse<List<AttributeDTO>> queryAttributeListByCatId(AttributeQueryReqDTO reqDTO);

    /**
     * 查询类目销售属性
     * @param categoryId 类目ID
     * @param lang 语种
     * @return 类目属性集合
     */
    DataResponse<List<AttributeFlatDTO>> querySellAttrDetail(Long categoryId, String lang);

    /**
     * 查询类目扩展属性
     * @param categoryId 类目ID
     * @param lang 语种
     * @return 类目属性集合
     */
    DataResponse<List<AttributeFlatDTO>> queryExtAttrDetail(Long categoryId, String lang);

    /**
     * 根据AttributeVcQueryReqDTO查询扩展属性详细信息。
     * @param input AttributeVcQueryReqDTO对象，包含查询条件。
     * @return DataResponse对象，包含List<AttributeDTO>类型的结果集。
     */
    DataResponse<List<PropertyApiDTO>> queryExtAttrDetailForVc(AttributeVcQueryReqDTO input);

    /**
     * 根据输入的AttributeVcQueryReqDTO对象，查询销售属性详细信息。
     * @param input AttributeVcQueryReqDTO对象，包含查询条件。
     * @return DataResponse对象，包含查询结果的List<AttributeDTO>。
     */
    DataResponse<List<PropertyApiDTO>> querySellAttrDetailForVc(AttributeVcQueryReqDTO input);


    /** 新扩展属性多语言初始化*/
    DataResponse<Void> extAttributeLangMapInit(Set<Long> cateSet);

    /** 扩展属性查询*/
    DataResponse<String> getAttributeById(Integer attributeId);
    /** 扩展属性值查询*/
    DataResponse<String> getAttributeValueById(Integer attributeId);

    /** 根据扩展属性名称查询多语言名称*/
    DataResponse<Map<String, Map<String, String>>> getExtAttrLangMapByCnName(Set<String> keyword, Set<String> langList);

    /** 根据扩展属性id获取多语言信息*/
    DataResponse<Map<Integer, Map<String,String>>> getExtAttrLangMapById(Set<Integer> attributeIds,Set<String> langList);

    /** 根据扩展属性值id获取多语言信息*/
    DataResponse<Map<Integer, Map<String,String>>> getExtAttrValueLangMapById(Set<Integer> attributeValueIds,Set<String> langList);

    /** 初始化扩展属性指定语种语言*/
    DataResponse<Void> removeDirtyData();

    /**
     * 根据类目id和京东skuid查询扩展属性，并对中台已经添加的扩展属性对类目的对应扩展属性赋值（selected）
     *
     * @param catId 类目id
     * @param lang 语种
     * @return
     */
    DataResponse<List<PropertyApiDTO>> getCategorySaleAttributesByJdCatId(Long catId, String lang);

    /**
     * 根据类目id和京东skuid查询扩展属性，并对中台已经添加的扩展属性对类目的对应扩展属性赋值（selected）
     *
     * @param catId 类目id
     * @param jdSkuId 京东skuid
     * @param lang 语种
     * @return
     */
    DataResponse<List<PropertyApiDTO>> obtainJDSkuExtAttributeList(Long catId, Long jdSkuId, String lang);

    /**
     * 获取中台SKU销售属性值详情，通过jdSkuId查询中台获取类目id和销售属性值，通过中台类目id查询数据库获取销售属性，按图销、文销拼接数据返回
     *
     * @param jdSkuId 中台skuId
     * @return 销售属性值列表的数据响应
     */
    DataResponse<List<PropertyValueApiDTO>> getJdSkuSaleAttributeDetail(Long jdSkuId, String lang);

    /**
     * 获取sku销售属性值详情
     *
     * @param skuId skuId
     * @param lang 语种
     * @return 销售属性值列表的数据响应
     */
    DataResponse<List<PropertyApiDTO>> obtainSkuSaleAttributeList(Long skuId, String lang);

    /**
     * 获取mku销售属性值详情
     *
     * @param mkuId mkuId
     * @param lang 语种
     * @return 销售属性值列表的数据响应
     */
    DataResponse<List<PropertyApiDTO>> obtainMkuSaleAttributeList(Long mkuId, String lang);


    /**
     * 根据分类ID和语言查询没有默认值的销售属性。
     * @param categoryId 分类ID
     * @param lang 语言
     * @return 包含PropertyApiDTO对象的列表的DataResponse对象
     */
    DataResponse<List<PropertyApiDTO>> querySellAttrByCatId(Long categoryId, String lang);
}
