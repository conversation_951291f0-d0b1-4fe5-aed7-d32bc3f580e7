package com.jdi.isc.product.soa.api.customerMku.req;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import com.jdi.isc.product.soa.api.material.req.ClientIdDTO;
import com.jdi.isc.product.soa.api.mkuTag.res.MkuTagIdDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CustomerMkuQueryDTO  extends BasePageReqDTO {
    /** mkuId*/
    private Long mkuId;

    /** 客户编码*/
    private String clientCode;

    /** 客户简码*/
    private String briefCode;

    /** 客户名称*/
    private Integer clientName;

    /** 绑定状态*/
    private CustomerMkuBindEnum bindStatus;

    /** mku名称*/
    private String mkuName;

    /** 创建者*/
    protected String creator;

    /** 修改人*/
    protected String updater;

    /** 修改时间*/
    protected Date updateTime;

    /**
     * 客户ID信息的集合
     */
    private ClientIdDTO clientIdVo;

    /**
     * 标签键值对映射，用于存储和管理标签信息。
     */
    private List<MkuTagIdDTO> tagIdList;

    /**
     * skuId、spuId、jdSkuId、mkuId
     */
    private List<Long> productIds;

}
