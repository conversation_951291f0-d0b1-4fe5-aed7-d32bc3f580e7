package com.jdi.isc.product.soa.api.price.req;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * @Description: 履约费率表分页查询对象
 * @Author: zhaokun51
 * @Date: 2024/10/29 21:47
 **/

@Data
public class FulfillmentRateReqApiDTO extends BasePageReqDTO {


    /**
     * 国家码，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 国家码，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * 一级类目id
     */
    private Long firstCatId;

    /**
     * 二级类目id
     */
    private Long secondCatId;

    /**
     * 三级类目id
     */
    private Long thirdCatId;

    /**
     * 末级类目id
     */
    private Long lastCatId;

    /**
     * 是否备货：1备货(入仓) 0直送
     */
    private Integer isWarehouseProduct;

    /**
     * skuId list
     */
    private Set<Long> skuIds;

}
