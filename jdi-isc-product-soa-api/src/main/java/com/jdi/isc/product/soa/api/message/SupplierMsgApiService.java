package com.jdi.isc.product.soa.api.message;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.message.req.SupplierMsgReadReq;
import com.jdi.isc.product.soa.api.message.req.SupplierMsgWriteReq;
import com.jdi.isc.product.soa.api.message.req.ValidatorGroup;
import com.jdi.isc.product.soa.api.message.res.SupplierMsgReadResp;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 供应商消息读写服务
 * <AUTHOR>
 * @date 20250424
 */
public interface SupplierMsgApiService {

    /**
     * 消息查询
     */
    @Validated(ValidatorGroup.msgConsume.class)
    DataResponse<List<SupplierMsgReadResp>> consume(SupplierMsgReadReq req);
    /**
     * 消息移除
     */
    @Validated(ValidatorGroup.msgRemove.class)
    DataResponse<Boolean> remove(SupplierMsgReadReq req);

    /**
     * 消息新增
     */
    DataResponse<Boolean> add(SupplierMsgWriteReq req);


}
