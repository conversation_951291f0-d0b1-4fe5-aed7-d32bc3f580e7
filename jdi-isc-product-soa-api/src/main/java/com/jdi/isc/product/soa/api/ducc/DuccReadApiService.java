package com.jdi.isc.product.soa.api.ducc;

import com.jdi.common.domain.rpc.bean.DataResponse;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: ducc配置接口
 * @Author: zhaojianguo21
 * @Date: 2025/03/25
 **/
public interface DuccReadApiService {
    /**
     * 获取运单定制化功能客户
     * @return
     */
    DataResponse<Set<String>> queryOrderWayCustomizeCustomers();

    /**
     * 是否为运单定制化功能客户
     * @return
     */
    DataResponse<Boolean> isOrderWayCustomizeCustomers(String clientCode);


    /**
     * 根据指定的键列表获取枚举键值映射
     * @param keys 需要查询的键列表，不可为空
     * @return 包含枚举键值映射的数据响应对象，结构为两级映射(分组键->枚举键->枚举值)
     */
    DataResponse<Map<String, Map<String, String>>> getEnumKvMap(List<String> keys);
}
