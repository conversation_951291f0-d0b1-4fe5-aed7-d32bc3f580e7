package com.jdi.isc.product.soa.api.taxRate.req;

import com.jdi.isc.product.soa.api.common.BasicDTO;
import com.jdi.isc.product.soa.api.validation.ExportTaxRateValidateGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * @Description: 中国出口税率信息 DTO实体类
 * @Author: zhaojianguo21
 * @Date: 2024/11/25 20:58
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ExportTaxRateReqDTO extends BasicDTO {
    /**
     * 国家码
     */
    @NotNull(message = "国家码不能为空" ,groups = ExportTaxRateValidateGroup.save.class)
    private String countryCode;

    /**
     * 海关编码
     */
    @NotNull(message = "海关编码不能为空",groups = ExportTaxRateValidateGroup.save.class)
    private String hsCode;

    /**
     * 出口征税率
     */
    @NotNull(message = "出口征税率不能为空",groups = ExportTaxRateValidateGroup.save.class)
    @DecimalMin(value = "0", message = "出口征税率不能小于0",groups = ExportTaxRateValidateGroup.save.class)
    @DecimalMax(value = "100", message = "出口征税率不能大于100",groups = ExportTaxRateValidateGroup.save.class)
    @Digits(integer = 3, fraction = 2, message = "出口征税率格式不正确，请输入正整数或两位小数",groups = ExportTaxRateValidateGroup.save.class)
    private BigDecimal exportTaxRate;

    /**
     * 出口退税率
     */
    @NotNull(message = "出口退税率不能为空")
    @DecimalMin(value = "0", message = "出口退税率不能小于0",groups = ExportTaxRateValidateGroup.save.class)
    @DecimalMax(value = "100", message = "出口退税率不能大于100",groups = ExportTaxRateValidateGroup.save.class)
    @Digits(integer = 3, fraction = 2, message = "出口退税率格式不正确，请输入正整数或两位小数",groups = ExportTaxRateValidateGroup.save.class)
    private BigDecimal exportRebateRate;

    /**
     * 海关控制条件
     */
    @NotNull(message = "海关控制条件不能为空",groups = ExportTaxRateValidateGroup.save.class)
    @Length(max = 100, message = "海关控制条件长度不能超过100",groups = ExportTaxRateValidateGroup.save.class)
    private String customsCondition;

    /**
     * 检验检疫类别
     */
    @NotNull(message = "检验检疫类别不能为空")
    @Length(max = 100, message = "检验检疫类别长度不能超过100",groups = ExportTaxRateValidateGroup.save.class)
    private String quarantineCat;

    /** 修改人*/
    @NotNull(message = "更新人不能为空",groups = ExportTaxRateValidateGroup.save.class)
    private String updater;

}
