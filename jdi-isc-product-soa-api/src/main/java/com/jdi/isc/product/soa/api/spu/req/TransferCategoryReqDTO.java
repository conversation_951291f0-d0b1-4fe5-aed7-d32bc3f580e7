package com.jdi.isc.product.soa.api.spu.req;


import com.jdi.isc.product.soa.api.validation.StockValidGroup;
import com.jdi.isc.product.soa.api.validation.TransferCategoryValidateGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description：类目迁移入参
 * @Date 2024-10-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransferCategoryReqDTO implements Serializable {
    /** SPU ID*/
    @NotNull(message = "spuId不能为空", groups = {TransferCategoryValidateGroup.transfer.class})
    private Long spuId;
    /** 目标类目ID*/
    @NotNull(message = "目标类目ID不能为空",groups = {TransferCategoryValidateGroup.transfer.class})
    private Long targetCatId;
    /** 更新人*/
    @NotNull(message = "操作人不能为空",groups = {TransferCategoryValidateGroup.transfer.class})
    private String updater;
}
