package com.jdi.isc.product.soa.api.outUrl.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 工业国际外链映射表
 * <AUTHOR>
 * @date 2025/4/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OutUrlMapDTO {

    /**
     * 批次Id
     */
    @NotNull(message = "批次号不能为空")
    private Long batchId;
    /**
     * url来源 UrlSourceEnum
     */
    private Integer source;
    /**
     * 外部url,是否必填
     */
    private Map<String,Boolean> outUrl;

}
