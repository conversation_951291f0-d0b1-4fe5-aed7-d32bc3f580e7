package com.jdi.isc.product.soa.api.countryMku.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class CountryMkuReqDTO {

    /**
     * mkuID
     */
    private Long mkuId;

    /**
     * 国家或地区的CODE。
     */
    private String countryCode;

    /**
     * 客户编码
     * */
    private String clientCode;

    /**
     * 开始时间戳。
     */
    private Long startTime;

    /**
     * 结束时间戳。
     */
    private Long endTime;

    /**
     * jdSkuId
     */
    private Long jdSkuId;

    /**
     * 开始ID，用于分页查询。
     */
    private Long beginId;

    /**
     * 结束ID，用于分页查询。
     */
    private Long endId;

    /**
     * skuId。
     */
    private Long skuId;

    /**
     * spuId。
     */
    private Long spuId;


    /**
     * 品牌ID列表，用于筛选特定品牌的数据。
     */
    private List<Long> brandIdList;

    /**
     * MKU ID列表，用于筛选特定MKU的数据。
     */
    private List<Long> mkuIdList;
}
