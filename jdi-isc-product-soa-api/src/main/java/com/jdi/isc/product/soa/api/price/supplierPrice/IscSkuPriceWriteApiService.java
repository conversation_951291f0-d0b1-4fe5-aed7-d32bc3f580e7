package com.jdi.isc.product.soa.api.price.supplierPrice;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.price.req.PriceUnsellableThresholdImportReqDTO;
import com.jdi.isc.product.soa.api.price.res.PriceUnsellableThresholdImportResDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.req.SkuPriceAuditDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.req.SkuPriceDraftSaveDTO;

public interface IscSkuPriceWriteApiService {
    /**
     * 保存、更新
     * @param dto 提交参数
     * @return 结果
     */
    DataResponse<Boolean> saveDraft(SkuPriceDraftSaveDTO dto);

    /**
     * 通过-批量
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<String> batchApprove(SkuPriceAuditDTO input);

    /**
     * 驳回-批量
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<String> batchReject(SkuPriceAuditDTO input);

    /**
     * 撤回-批量
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<String> batchRevoke(SkuPriceAuditDTO input);

    /**
     * 更新国家协议价格和京东价格。
     * @param dto 国家协议价格和京东价格请求体对象。
     * @return 更新操作的结果。
     */
    DataResponse<Boolean> batchSaveDraft(SkuPriceDraftSaveDTO dto);

    /**
     * 更新不可售价格阈值
     * @param input 不可售价格阈值导入请求DTO，包含需要更新的阈值信息
     * @return 包含不可售价格阈值导入响应结果的DataResponse对象
     */
    DataResponse<PriceUnsellableThresholdImportResDTO> updateUnsellableThreshold(PriceUnsellableThresholdImportReqDTO input);

}
