package com.jdi.isc.product.soa.api.category;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.category.biz.CategoryReadDTO;
import com.jdi.isc.product.soa.api.category.biz.GmsCategoryReqDTO;
import com.jdi.isc.product.soa.api.category.biz.JdCategorySyncReqDTO;
import com.jdi.isc.product.soa.api.wisp.category.biz.CategoryDTO;

import java.util.List;
import java.util.Map;

/**
 * 类目API接口，用于主站类目查询和同步
 * <AUTHOR>
 * @description：类目API接口，包含主站一级类目同步、查询主站类目信息、修改类目信息
 * @Date 2025-05-22
 */
public interface IscProductSoaCategoryApiService {


    /**
     * 根据一级类目ID同步京东类目信息
     * @param reqDTO 同步请求对象，包含需要同步的一级类目ID
     * @return 同步操作结果，true表示成功，false表示失败
     */
    DataResponse<Boolean> syncJdCategoryByFirstCatId(JdCategorySyncReqDTO reqDTO);


    /**
     * 根据JdCategorySyncReqDTO获取对应的CategoryDTO列表
     * @param reqDTO JdCategorySyncReqDTO对象，包含同步所需的参数信息
     * @return DataResponse对象，包含CategoryDTO列表和相关的状态信息
     */
    DataResponse<CategoryDTO> getJdCategoryDTO(JdCategorySyncReqDTO reqDTO);


    /**
     * 修改商品类别
     * @param reqDTO 请求参数，包含要修改的商品类别信息
     * @return 操作结果，true表示成功，false表示失败
     */
    DataResponse<Boolean> changeCategory(GmsCategoryReqDTO reqDTO);

    /**
     * 根据类目id查询类目信息
     * @param req 请求参数
     * @return 操作结果
     */
    DataResponse<Map<Long, Map<String, String>>> getCategoryById(CategoryReadDTO req);

    /**
     * 根据条件查询分类信息,不区分类目禁用状态
     * @param req 查询条件
     * @return 分类信息列表
     */
    DataResponse<Map<Long,CategoryDTO>> queryCategoryByIds(CategoryReadDTO req);

}
