package com.jdi.isc.product.soa.api.stock.res;

import lombok.Data;

import java.util.Date;


/**
 * 库存响应实体
 * <AUTHOR>
 * @date 2024/6/15
 */
@Data
public class StockResDTO {

    /** 自增ID*/
    protected Long id;

    /** 仓id*/
    private  String warehouseId;

    /** spuId*/
    private Long spuId;

    /** skuId*/
    private Long skuId;

    /** 预占库存数量*/
    @Deprecated
    private Long preemptionStock;

    /** 现货库存*/
    private Long stock;

    /** 预占库存数量*/
    private Long occupy;

    /** 可用库存*/
    private Long availableStock;

    /** 备注*/
    protected String remark;

    /** 创建者*/
    protected String creator;

    /** 修改人*/
    protected String updater;

    /** 创建时间*/
    protected Date createTime;

    /** 最后修改时间*/
    protected Date updateTime;

    /** 删除状态*/
    protected Integer yn;

    /** 库存状态 : 33有货、34无货*/
    private Integer stockStateType;

    /** 库存状态描述 : 有货、无货*/
    private String stockStateDesc;

    /** 现货剩余库存数量,即可用库存 = 现货+在途-预占 */
    private Long remainNum;
    /**在途库存**/
    private Long onWayStock;
    /**
     * 在途销售状态，1表示在途销售中，0表示不在途销售中
     */
    private Integer onWaySale;

}
