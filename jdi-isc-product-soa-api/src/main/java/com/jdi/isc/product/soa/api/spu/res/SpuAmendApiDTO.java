package com.jdi.isc.product.soa.api.spu.res;

import com.jdi.isc.product.soa.api.sku.res.SkuAmendApiDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SpuAmendApiDTO {
    /**
     * SPUID
     */
    private Long spuId;
    /**
     * MKUID
     */
    private Long mkuId;
    /**
     * 原产地（原产国）
     */
    private String originCountry;
    /**
     * 商品功能
     */
    private String productFunction;
    /**
     * 商品用途
     */
    private String productUse;
    /**
     * 商品原理
     */
    private String productTheory;
    /**
     * 商品材质
     */
    private String productMaterial;
    /**
     * 型号
     */
    private String specification;
    /**
     * sku hscode
     */
    private String skuHSCode;
    /**
     * 申报要素
     */
    private String declarationElements;
    /**
     * 申报品名
     */
    private String declarationZhName;
    /**
     * 申报品英文名
     */
    private String declarationEnName;
    /**
     * 含电 0:不带电，1:带电
     */
    private Integer electric;
    /**
     * 含磁 0:不带磁，1:带磁
     */
    private Integer magnetic;
    /**
     * 含液 0:不含液，1:含液
     */
    private Integer liquid;
    /**
     * 含粉 0:不含粉，1:含粉
     */
    private Integer powder;
    /**
     * 生产企业
     */
    private String supplierName;
    /**
     * 组织机构代码
     */
    private String organizationCode;
    /**
     * 联系人姓名
     */
    private String name;
    /**
     * 联系人电话
     */
    private String phone;

    /**
     * SKID
     */
    private Long skuId;

    /**
     * MKU名称
     */
    private String mkuName;

    /**
     * Sku修改信息列表
     */
    private List<SkuAmendApiDTO> skuAmendList;
    /**
     * 类别 0:需维护 1:驳回
     */
    private Integer type;

    /**
     * 是否需要校验
     */
    private Boolean verify = false;
    /**
     * 缺失的字段map
     */
    private Map<String,Boolean> propertyMap;
}
