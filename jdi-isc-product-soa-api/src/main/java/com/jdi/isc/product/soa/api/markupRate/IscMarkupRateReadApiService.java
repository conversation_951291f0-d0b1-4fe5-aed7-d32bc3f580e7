package com.jdi.isc.product.soa.api.markupRate;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.markupRate.req.MarkupRateReqDTO;
import com.jdi.isc.product.soa.api.markupRate.res.MarkupRateDTO;
import com.jdi.isc.product.soa.api.markupRate.res.MarkupRatePageApiDTO;
import com.jdi.isc.product.soa.api.markupRate.req.MarkupRatePageReqDTO;

/**
 * @Description: 国家协议加价率api服务
 * @Author: wangpeng965
 * @Date: 2025/02/28 10:31
 **/

public interface IscMarkupRateReadApiService {

    /**
     * 分页查询
     * @param dto 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<MarkupRatePageApiDTO>> pageSearch(MarkupRatePageReqDTO dto);

    DataResponse<MarkupRateDTO> detail(MarkupRateReqDTO dto);
}
