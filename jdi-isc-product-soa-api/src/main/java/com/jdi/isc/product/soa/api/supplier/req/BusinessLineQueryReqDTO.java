package com.jdi.isc.product.soa.api.supplier.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/9/1
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessLineQueryReqDTO {
    /**
     * 供应商代码，用于查询业务线信息。
     */
    @NotBlank(message = "supplierCode can not be null")
    private String supplierCode;
    /**
     * 产品线类目ID
     */
    private Long categoryId;
    /**
     * 语言集合，用于指定查询业务线信息时所需的语言。
     */
    private Set<String> langSet;
}
