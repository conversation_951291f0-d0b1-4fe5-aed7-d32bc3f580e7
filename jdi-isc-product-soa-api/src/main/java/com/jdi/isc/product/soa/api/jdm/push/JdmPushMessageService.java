package com.jdi.isc.product.soa.api.jdm.push;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.jdm.push.req.JdmSupplierMessageReqDTO;

/**
 * 京麦推送消息接口
 *
 * <AUTHOR>
 * @date 2024/7/9
 **/
public interface JdmPushMessageService {

    /**
     * 批量推送消息接口
     * @param reqDTO  供应商消息入参
     * @return 推送结果
     */
    DataResponse<String> pushMessageToJm(JdmSupplierMessageReqDTO reqDTO);
}
