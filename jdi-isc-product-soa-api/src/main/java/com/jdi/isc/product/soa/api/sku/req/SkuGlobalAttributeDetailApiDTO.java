package com.jdi.isc.product.soa.api.sku.req;

import com.jdi.isc.product.soa.api.common.enums.GlobalAttributeVnEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SkuGlobalAttributeDetailApiDTO {
    /**
     * SKUID 国际SKUID
     */
    private Long skuId;

    /**
     * 需要更新的跨境属性
     * @see GlobalAttributeVnEnum 越南-key集合
     * 传参示例：{"vnHsCode": "1233213213321","vnImportLimit": "1"}
     */
    private Map<String,Object> updateData;
}
