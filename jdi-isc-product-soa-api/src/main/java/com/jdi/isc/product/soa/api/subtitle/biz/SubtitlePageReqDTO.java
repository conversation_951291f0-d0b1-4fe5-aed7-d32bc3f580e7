package com.jdi.isc.product.soa.api.subtitle.biz;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;
@Data
@NoArgsConstructor
@EqualsAndHashCode
@AllArgsConstructor
public class SubtitlePageReqDTO extends BasePageReqDTO {
    /**
     * 采购员
     */
    private String buyer;

    /**
     * 一级类目id
     */
    private Long firstCatId;

    /**
     * 二级类目id
     */
    private Long secondCatId;

    /**
     * 三级类目id
     */
    private Long thirdCatId;

    /**
     * 末级类目id
     */
    private Long lastCatId;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 目标国、国家站、国家码，ISO 3166-1 两字母代码
     */
    @NotNull(message = "目标国不能为空")
    private String targetCountryCode;

    /**
     * 分类ids
     * */
    private Set<Long> catIds;

    /**
     * mkuIds
     * */
    private List<Long> mkuIds;

    /**
     * spuIds
     * */
    private List<Long> spuIds;

    /**
     * skuIds
     * */
    private List<Long> skuIds;

    /**
     * 页签key
     * */
    private String typeKey;

    /**
     * 数量
     */
    private Long num;
    /**
     * 标题语言
     */
    @NotNull(message = "标题语言不能为空")
    private String titleLang;

    /** 修改人*/
    private String updater;
}
