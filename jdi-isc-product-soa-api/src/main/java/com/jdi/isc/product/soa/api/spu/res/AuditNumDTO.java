package com.jdi.isc.product.soa.api.spu.res;

import lombok.Builder;
import lombok.Data;

/**
 * @Description:
 * @Author: wangpeng965
 * @Date: 2024/10/16 10:42
 */
@Data
@Builder
public class AuditNumDTO {
    /**
     * typeKey
     */
    private String typeKey;
    /**
     * spu状态
     */
    private Integer spuStatus;
    /**
     * 审核状态
     */
    private Integer auditStatus;
    /**
     * 审核状态
     */
    private Integer taxAuditStatus;
    /**
     * 数量
     */
    private Long num;
    /**
     * 审核等级
     */
    private Integer level;
}
