package com.jdi.isc.product.soa.api.translate;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.translate.req.BatchMultiTranslateReqDTO;
import com.jdi.isc.product.soa.api.translate.req.MultiTranslateReqDTO;
import com.jdi.isc.product.soa.api.translate.req.TranslateRemainingCountReqDTO;
import com.jdi.isc.product.soa.api.translate.req.TranslateReqDTO;

import java.util.Map;
import java.util.Set;

/**
 * 翻译接口
 * <AUTHOR>
 * @date 2024/8/26
 **/
public interface IscProductSoaTranslateApiService {

    /**
     * 翻译单个文本
     * @param reqDTO 翻译入参
     * @return 返回翻译结果
     */
    DataResponse<String> translateSingleLangText(TranslateReqDTO reqDTO);

    /**
     * 翻译多语言文本
     * @param reqDTO 包含批量翻译请求数据的对象
     * @return 包含翻译结果的 DataResponse 对象，其中键为语言代码，值为对应翻译文本
     */
    DataResponse<Map<String, String>> translateMultiLangText(MultiTranslateReqDTO reqDTO);

    /**
     * 批量翻译多语言文本
     * @param batchReqDTOSet 包含多个批量翻译请求的集合
     * @return 包含翻译结果的DataResponse对象，内部数据类型为BatchTranslateResDTO列表
     */
    DataResponse<Map<String,Map<String,String>>> batchTranslateMultiLangText(BatchMultiTranslateReqDTO batchReqDTOSet);

    /**
     * 根据翻译请求对象翻译单语种文本
     * @param reqDTO 翻译请求数据传输对象，包含待翻译文本及目标语言等信息
     * @return 包含翻译结果的响应对象，封装翻译状态及翻译后文本
     */
    DataResponse<String> translateSingleLangTextByBaidu(TranslateReqDTO reqDTO);

    /**
     * 查询翻译剩余次数
     * @param reqDTO 翻译剩余次数请求DTO
     * @return 包含翻译剩余次数的DataResponse对象
     */
    DataResponse<String> queryRemainingCount(TranslateRemainingCountReqDTO reqDTO);
}
