package com.jdi.isc.product.soa.api.orderVerify.req;

import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class MkuVerifyReqDTO  extends BasicDTO {

    /**
     * mkuId列表
     */
    private List<Long> mkuIdList;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 客户编码
     */
    private String clientCode;
    /**
     * 父订单号
     */
    private Long parentOrderId;
    /**
     * 被拆分的订单号
     */
    private Long splitOrderId;
    /**
     * 状态 1创建 2更新 3删除
     */
    private Integer updateStatus;
    /**
     * 订单所属国
     */
    private String countryCode;
    /**
     * 订单创建时间
     */
    private Long orderCreateTime;
}
