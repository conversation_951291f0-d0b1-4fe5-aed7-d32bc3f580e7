package com.jdi.isc.product.soa.api.subtitle.biz;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 巴西短标题.
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class IscSubtitleExportApiDTO extends BaseExportApiDTO {
    /**
     * skuId
     */
    private String skuId;

    /**
     * MKUID
     */
    private Long mkuId;

    /**
     * spuId
     */
    private String spuId;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 管理类目
     */
    private String catName;

    /**
     * 采购ERP
     */
    private String buyer;

    /**
     * 商品中文名称。
     */
    private String zhMkuTitle;

    /**
     * 葡萄牙语（巴西）MKU标题。
     */
    private String ptBrMkuTitle;

    /**
     * 商品巴葡短标题。
     */
    private String mkuSubtitle;

    /**
     * 字符数。
     */
    private Integer subtitleLength;

    private String updater;

    private String updateTimeStr;


    /* 最后修改时间*/
    private Date updateTime;

    /**
     * spuIds
     * */
    private List<Long> spuIds;

    /**
     * skuIds
     * */
    private List<Long> skuIds;

    /**
     * 报错原因
     */
    private String remark;

}
