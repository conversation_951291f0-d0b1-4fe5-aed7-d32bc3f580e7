package com.jdi.isc.product.soa.api.message.req;

/**
 * 供应商开放平台消息枚举
 * 1xx 为商品类型消息
 * 2xx 为价格类型消息
 * 3xx 为库存类型消息
 * 以此类推..
 */
public enum SupplierMsgTypeEnum {

//    /**
//     * 商品发布状态消息。
//     */
//    SUCCESS(100, "商品发布状态消息"),
    /**
     * 商品审核结果消息。
     */
    AUDIT_MSG(101, "商品审核结果消息"),

    ;

    private final Integer type;
    private final String name;

    private SupplierMsgTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }


    public Integer getType() {
        return this.type;
    }

    public String getName() {
        return this.name;
    }
}