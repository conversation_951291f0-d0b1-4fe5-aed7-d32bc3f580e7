package com.jdi.isc.product.soa.api.extendPrice.res;

import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 国家扩展（参考）价分页查询对象
 * @Author: wangpeng965
 * @Date: 2025/03/04 16:28
 **/

@Data
public class CountryExtendPricePageApiDTO extends BasicDTO implements Serializable{
        /**
         * 业务编号
         */
        private String bizNo;

        /**
         * MKUID
         */
        private Long mkuId;

        /**
         * SKUID
         */
        private Long skuId;

        /**
         * 货源国、国家站、国家码，ISO 3166-1 两字母代码
         */
        private String sourceCountryCode;

        /**
         * 目标国、国家站、国家码，ISO 3166-1 两字母代码
         */
        private String targetCountryCode;

        /**
         * 末级类目id
         */
        private Long lastCatId;

        /**
         * 品牌
         */
        private Long brandId;

        /**
         * 京东价
         */
        private BigDecimal jdPrice;

        /**
         * 入仓成本价
         */
        private BigDecimal warehousingPrice;

        /**
         * 预估跨境退税金额
         */
        private BigDecimal taxRefundPrice;

        /**
         * 商检及认证费
         */
        private BigDecimal certifiedPrice;

        /**
         * 货币类型。
         */
        private String currency;
        /**
         * 业务类型，用于区分不同类型的业务操作。
         */
        private Integer businessType;

        /**
         * 扩展价格实体的单价。
         */
        private BigDecimal price;

        /**
         * 扩展价格实体的单价标记。
         */
        private String priceMark;
}
