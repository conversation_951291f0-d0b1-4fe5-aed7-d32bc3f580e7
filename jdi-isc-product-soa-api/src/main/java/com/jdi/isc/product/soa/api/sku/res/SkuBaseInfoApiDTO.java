package com.jdi.isc.product.soa.api.sku.res;

import lombok.Data;

import java.util.Map;

/**
 * SKU基础信息
 * <AUTHOR>
 * @date 2024/8/26
 **/
@Data
public class SkuBaseInfoApiDTO {
    /**
     * SKUID
     */
    private Long skuId;

    /**
     * SPUID
     */
    private Long spuId;

    /**
     * 工业国内SKUID
     */
    private Long jdSkuId;

    /**
     * EPT SKUID
     */
    private Long eptSkuId;

    /**
     * 供应商简码
     */
    private String vendorCode;

    /**
     * 供应商SKUID
     */
    @Deprecated
    private Long vendorSkuId;
    /**
     * 国内供应商简码
     */
    private String jdVendorCode;

    /**
     * upc编码
     */
    private String upcCode;

    /**
     * 货源国、国家站
     */
    private String sourceCountryCode;

    /**
     * 末级后台类目ID
     */
    private Long catId;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 毛重(g)
     */
    private String weight;

    /**
     * 长(mm)
     */
    private String length;

    /**
     * 宽(mm)
     */
    private String width;

    /**
     * 高(mm)
     */
    private String height;

    /**
     * 销售属性及值 aa:bb,cc:dd
     */
    private String saleAttribute;

    /**
     * sku主图
     */
    private String mainImg;
    /**
     * 海关编码
     */
    private String hsCode;
    /**
     * 商品状态
     */
    private Integer skuStatus;
    /**
     * 商品审核状态
     */
    private Integer auditStatus;
    /**
     * 重量来源 0:供应商填写 1：集运中心回传
     */
    private Integer weightSource;
    /**
     * 商品名称多语言映射
     */
    private Map<String,String> skuNameMap;
    /**
     * 品牌名称
     */
    private Map<String,String> brandNameMap;
    /**
     * 供应商简码
     */
    private String supplierCode;
    /**
     * 一级类目ID
     */
    private Long firstCatId;
    /**
     * 二级类目ID
     */
    private Long secondCatId;
    /**
     * 三级类目ID
     */
    private Long thirdCatId;
    /**
     * 四级类目ID
     */
    private Long fourthCatId;
    /**
     * 销售单位
     */
    private String saleUnit;
    /**
     * SKU特殊信息
     */
    private SkuFeatureApiDTO skuFeatureApiDTO;
    /**
     * 型号
     */
    private String specification;
    /**
     * 是否在客户池
     */
    private Boolean inCustomerPool;
    /**
     * 是否在国家池
     */
    private Boolean inCountryPool;

    /**
     * 采购员
     */
    private String buyer;

    /**
     * 采购员
     */
    private String skuThirdRefId;

    /**
     * 数据来源系统
     */
    private String systemCode;

    /**
     * 多语言销售单位
     */
    private Map<String, String> saleUnitMap;

    /**
     * 国际供应商信息
     */
    private VendorInfoApiDTO vendorInfo;

    /**
     * 京东零售供应商信息(仅跨境品存在)
     */
    private VendorInfoApiDTO jdVendorInfo;

    /**
     * 供应商SKUID
     */
    private String vendorSkuIdNew;
    /**
     * 销售单位编码
     */
    private Integer saleUnitCode;
}
