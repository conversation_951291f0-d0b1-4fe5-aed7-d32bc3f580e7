package com.jdi.isc.product.soa.api.specialAttr;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.specialAttr.biz.SpecialAttrRelationPageApiDTO;
import com.jdi.isc.product.soa.api.specialAttr.biz.SpecialAttrTagReq;
import com.jdi.isc.product.soa.api.specialAttr.res.SpecialAttrRelationDTO;
import com.jdi.isc.product.soa.api.specialAttr.res.SpecialAttrTagResp;

import java.util.List;

/**
 * @Description: 商品特殊属性api服务
 * @Author: zhangjin176
 * @Date: 2024/12/11 09:58
 **/

public interface SpecialAttrRelationApiService {

    /**
     * 分页查询
     *
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<SpecialAttrRelationDTO>> pageSearch(SpecialAttrRelationPageApiDTO.Request input);



    /**
     * 根据 SpecialAttrTagReq 请求获取特殊属性标签。
     *
     * @param req SpecialAttrTagReq 请求对象，包含查询条件。
     * @return DataResponse<Map < Long, List < SpecialAttrTagDTO>>> 返回一个 DataResponse 对象，其中包含了特殊属性标签的 Map 结果。
     */
    DataResponse<List<SpecialAttrTagResp>> getSpecialAttrTag(SpecialAttrTagReq req);

}