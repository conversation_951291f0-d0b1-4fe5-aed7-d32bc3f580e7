package com.jdi.isc.product.soa.api.wimp.category;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.wimp.category.req.GlobalCountryCategoryAttributeRelationApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.GlobalCountryCategoryAttributeRelationPageApiDTO;

/**
 * @Description: 国际国家类目属性关联api服务
 * @Author: taxuezheng1
 * @Date: 2024/07/12 11:33
 **/

public interface GlobalCountryCategoryAttributeRelationApiService {


    /**
     * 保存、更新
     */
    DataResponse<Boolean> saveOrUpdate(GlobalCountryCategoryAttributeRelationApiDTO input);

    /**
     * 详情
     */
    DataResponse<GlobalCountryCategoryAttributeRelationApiDTO> detail(GlobalCountryCategoryAttributeRelationApiDTO input);


    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<GlobalCountryCategoryAttributeRelationPageApiDTO.Response>> pageSearch(GlobalCountryCategoryAttributeRelationPageApiDTO.Request input);
}
