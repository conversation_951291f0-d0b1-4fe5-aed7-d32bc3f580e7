package com.jdi.isc.product.soa.api.category;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.category.biz.CategoryCountryPageReqDTO;
import com.jdi.isc.product.soa.api.category.biz.CategoryTreeDTO;
import com.jdi.isc.product.soa.api.category.biz.CategoryCountryReqDTO;
import com.jdi.isc.product.soa.api.wisp.category.biz.CategoryDTO;

import java.util.List;

/**
 * @Description: 禁止国家类目表api服务
 * @Author: wangpeng965
 * @Date: 2024/12/02 21:55
 **/

public interface IscCategoryCountryApiService {

    /**
     * 获取分类树列表
     * @param reqDTO 请求参数对象，包含国家信息
     * @return 分类树列表
     */
    DataResponse<List<CategoryTreeDTO>> listTree(CategoryCountryReqDTO reqDTO);

    /**
     * 保存或更新类别国家信息。
     * @param categoryReqVO 类别国家请求对象
     * @return 操作结果
     */
    DataResponse<Boolean> saveOrUpdate(CategoryCountryReqDTO categoryReqVO);

    /**
     * 分页获取商品分类列表
     * @param pageReqDTO 分页请求参数，包括当前页码、每页条数、国家或地区代码等
     * @return 分页结果，包含当前页码、总页数、总记录数以及分类列表
     */
    DataResponse<PageInfo<CategoryDTO>> page(CategoryCountryPageReqDTO pageReqDTO);


}
