package com.jdi.isc.product.soa.api.mku.res;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 国际MKU批量回参
 * <AUTHOR>
 * @date 2024/12/4
 */
@Data
@NoArgsConstructor
public class IscMkuResDTO {
    /** mkuId*/
    private Long mkuId;
    /** mku名称<默认中文>*/
    private String mkuName;
    /** mku型号*/
    private String model;
    /** 品牌id*/
    private Long brandId;
    /** 品牌名称<默认中文>*/
    private String brandName;
    /** 类目id*/
    private Long catId;
    /** 单位<默认中文>*/
    private String unit;
    /** moq*/
    private Integer moq;

    /** 价格信息*/
    private IscMkuPriceResDTO price;
    /** mku主图*/
    private String mainImgUrl;
    /**客户目标国主语种商品名称**/
    private String targetLangMkuName;

    /** 规格*/
    private String specification;
    /** 供应商简码*/
    private String supplierCode;
    /** 供应商名称*/
    private String supplierName;
    /** 货源国*/
    private String sourceCountryCode;
    /** mku细节图*/
    private List<String> detailImgList;
    /** 0: 不在国家池 1：在国家池*/
    private Integer inCountryPool;
    /** 多语言mku名称*/
    private Map<String, String> mkuNameMap;
    /** 多语言销售单位*/
    private Map<String, String> saleUnitMap;
    /** 多语言品牌名称*/
    private Map<String, String> brandNameMap;

    public IscMkuResDTO(Long mkuId,String mkuName,Long catId,Long brandId,String brandName,String mainImgUrl,String targetLangMkuName,String sourceCountryCode){
        this.mkuId = mkuId;
        this.mkuName = mkuName;
        this.catId = catId;
        this.brandId = brandId;
        this.brandName = brandName;
        this.mainImgUrl = mainImgUrl;
        this.targetLangMkuName = targetLangMkuName;
        this.sourceCountryCode = sourceCountryCode;
    }

}
