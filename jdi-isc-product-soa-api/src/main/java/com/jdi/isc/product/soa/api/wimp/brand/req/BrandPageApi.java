package com.jdi.isc.product.soa.api.wimp.brand.req;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 品牌类目关系分页查询对象
 * @Author: taxuezheng1
 * @Date: 2024/07/25 16:32
 **/

@Data
public class BrandPageApi implements Serializable {


    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Request extends BasePageReqDTO implements Serializable {

        /**
         * 品牌多语言
         */
        private List<BrandLangApiDTO> brandLangList;

        /**
         * 状态
         */
        private Integer status;

        /** 国家码 */
        private String countryCode;

        /**
         * 品牌分层类型
         */
        private String levelType;

        /**
         * 跨境品牌出口授权方式
         */
        private String authType;

        /**
         * 末级类目id
         */
        private Long categoryId;

        /**
         * 品牌id
         */
        private Long brandId;
    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BasicApiDTO implements Serializable{

        private BrandCountryApiDTO brandCountryVO;


        /** 品牌logo图片路径*/
        private String brandLogoPath;

        /** 排序*/
        private Integer sort;

        /** 状态 */
        private Integer status;

        /** 品牌名称多语言*/
        private List<BrandLangApiDTO> brandLangList;

        /**
         * 品牌分层类型
         */
        private String levelType;

        /**
         * 国内品牌id
         */
        private Long jdBrandId;

        /**
         * 全类目生效
         */
        private boolean allCategoryScope;

        /**
         * 类目全类名列表
         */
        private List<String> categoryFullNameList;

        /**
         * 创建时间
         */
        protected Date createTime;

        /**
         * 最后修改时间
         */
        protected Date updateTime;
    }

}
