package com.jdi.isc.product.soa.api.category.biz;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 禁止国家类目表 VO实体类
 * @Author: wangpeng965
 * @Date: 2024/12/02 21:55
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class CategoryCountryPageReqDTO extends BasePageReqDTO {

    /**
     * 禁止国家 CN,VN,TH
     */
    @NotNull(message = "禁止国家 CN,VN,TH不能为空")
    private String countryCode;

    /**
     * 被禁止的类目列表
     */
    private List<Long> banCatList;

    /**
     * 版本信息
     */
    private String version;
}
