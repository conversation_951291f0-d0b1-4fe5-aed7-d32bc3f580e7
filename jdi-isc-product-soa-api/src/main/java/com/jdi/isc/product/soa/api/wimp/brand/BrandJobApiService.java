package com.jdi.isc.product.soa.api.wimp.brand;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.wimp.brand.req.BrandCountryApiDTO;

import java.util.List;

/**
 * 品牌job任务服务
 */
public interface BrandJobApiService {


    /**
     * 处理品牌过期半个月的任务
     */
    DataResponse<Boolean> brandExpireHalfMonthHandleJob();

    /**
     * 处理品牌过期月份的任务
     */
    DataResponse<Boolean> brandExpireMonthHandleJob();

    DataResponse<List<BrandCountryApiDTO>> queryBrandUpdateTime(BrandCountryApiDTO dto);

}
