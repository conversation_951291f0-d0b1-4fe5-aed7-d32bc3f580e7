package com.jdi.isc.product.soa.api.jdm.category;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.jdm.category.req.JdmCategoryReqDTO;
import com.jdi.isc.product.soa.api.jdm.category.res.JdmCategoryResDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/3
 **/
public interface JdmCategoryReadService {

    /**
     * 根据父级类目ID查询自己类目列表
     * @param reqDTO 父级类目信息
     * @return 自己类目列表
     */
    DataResponse<List<JdmCategoryResDTO>> queryChildCats(JdmCategoryReqDTO reqDTO);
}
