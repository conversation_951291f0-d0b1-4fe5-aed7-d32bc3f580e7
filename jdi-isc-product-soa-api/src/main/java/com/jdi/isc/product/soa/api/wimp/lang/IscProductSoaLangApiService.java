package com.jdi.isc.product.soa.api.wimp.lang;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.wimp.lang.res.LangLibraryVersionApiDTO;
import com.jdi.isc.product.soa.api.wimp.lang.res.LangResDTO;

import java.util.List;
import java.util.Map;

/**
 * 国家主数据写服务
 * <AUTHOR>
 * @date 2024/07/10
 */
public interface IscProductSoaLangApiService {


    /**
     * 查询列表
     */
    @Deprecated
    DataResponse<List<LangResDTO>> queryList();

    /**
     * 查询列表
     */
    DataResponse<List<LangResDTO>> queryListByLang(String lang);

    /**
     * 重新加载缓存中配置
     * @return 返回重新加载是否成功
     */
    DataResponse<Boolean> reloadCache();

    /**
     * 获取语言列表
     * @return 按降序排序的语言列表，如果缓存数据为空则返回 null
     */
    DataResponse<List<String>> getLangCodeList();

    /**
     * 获取语言映射表
     * @return 包含语言映射关系的Map，如果缓存数据为空则返回null
     * Map<langCode,langName(中文名)></>
     */
    @Deprecated
    DataResponse<Map<String,String>> getLangMap();

    /**
     * 获取语言映射表
     * @param lang 语言代码
     * @return 包含语言映射表的 DataResponse 对象
     */
    DataResponse<Map<String,String>> getLangMapByLang(String lang);

    /**
     * 根据语言代码获取语言名称
     * @param langCode 语言代码
     * @return 对应的语言名称，如果未找到则返回 null
     */
    @Deprecated
    DataResponse<String> getLangNameByLangCode(String langCode);

    /**
     * 根据语言代码获取语言名称
     * @param langCode 语言代码
     * @param lang 语言
     * @return 包含语言名称的 DataResponse 对象
     */
    DataResponse<String> getLangNameByLangCodeAndLang(String langCode,String lang);



    /**
     * 根据语言获取语言库信息
     * @param lang 语言代码
     * @return 包含语言库信息的 DataResponse 对象
     */
    DataResponse<JSONObject> getLangLibraryByLang(String lang);


    /**
     * 根据给定的标签获取语言库版本列表。
     * @param tag 标签，用于筛选语言库版本。
     * @return 语言库版本列表。
     */
    DataResponse<List<LangLibraryVersionApiDTO>> getLangLibraryVersion(String tag);



    /**
     * 对 JimTmp 进行操作
     * @param param 操作参数
     * @return 操作结果
     */
    DataResponse<JSONObject> operateJimTmp(JSONObject param);

}
