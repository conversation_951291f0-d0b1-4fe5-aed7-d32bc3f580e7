package com.jdi.isc.product.soa.api.certificate.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * 跨境属性查询入参
 * <AUTHOR>
 * @date 2024/9/9
 **/
@Data
public class GlobalCertificateQueryReqDTO {
    /**
     * 多语言名称查询
     */
    private String lang;

    /**
     * 属性维度,1=spu,2=sku
     **/
    @NotNull(message = "attributeDimension can not be null")
    private Integer attributeDimension;
    /**
     * 属性输入类型
     */
    private Set<Integer> inputTypeList;
    /**
     * 四级类目ID
     */
    @NotNull(message = "categoryId can not be null")
    private Long categoryId;
    /**
     * 可出口
     */
    private Integer export;
    /**
     * 货源国
     */
    @NotBlank(message = "sourceCountryCode can not be null")
    private String sourceCountryCode;
    /**
     * 销售国集合
     */
    @NotEmpty(message = "targetCountryCodes can not be null")
    private Set<String> targetCountryCodes;
}
