package com.jdi.isc.product.soa.api.price.supplierPrice.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SkuPriceQueryReqDTO extends BaseReqDTO implements Serializable {
    /**
     * 供应商代码
     */
    private String vendorCode;

    /**
     * SKU的唯一标识符
     */
    private Long skuId;

    /** 货源国 */
    private String sourceCountryCode;

}
