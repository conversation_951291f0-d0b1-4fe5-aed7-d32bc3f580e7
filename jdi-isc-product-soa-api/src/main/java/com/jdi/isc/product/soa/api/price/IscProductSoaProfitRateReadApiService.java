package com.jdi.isc.product.soa.api.price;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.price.req.ProfitRateReqApiDTO;
import com.jdi.isc.product.soa.api.price.res.ProfitRateApiDTO;

/**
 * @Description: 利润率阈值表api服务
 * @Author: zhaokun51
 * @Date: 2024/10/29 21:46
 **/

public interface IscProductSoaProfitRateReadApiService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<ProfitRateApiDTO>> pageSearch(ProfitRateReqApiDTO input);

    /**
     * 详情查询
     * @param id 主键id
     * @return 分页查询结果
     */
    DataResponse<ProfitRateApiDTO> detail(Long id);

}
