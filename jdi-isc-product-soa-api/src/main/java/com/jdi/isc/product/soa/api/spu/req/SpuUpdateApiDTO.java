package com.jdi.isc.product.soa.api.spu.req;

import com.jdi.isc.product.soa.api.common.BaseExcelDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SpuUpdateApiDTO extends BaseExcelDTO {
    /**
     * SKUID 国际SKUID
     */
    private Long skuId;
    /**
     * 供应商SPUID
     */
    private String vendorSpuId;
    /**
     * 品牌ID
     */
    private Long brandId;
    /**
     * 原产地
     */
    private String originCountry;

    /**
     * 销售单位
     */
    private Integer saleUnit;
    /**
     * 毛重(g)
     */
    private BigDecimal weight;
    /**
     * 长(mm)
     */
    private BigDecimal length;

    /**
     * 宽(mm)
     */
    private BigDecimal width;

    /**
     * 高(mm)
     */
    private BigDecimal height;

    /**
     * 海关编码
     */
    private String hsCode;
    /**
     * sku主图 ---商品主图链接  （同样打包上传，后续同）
     */
    private String mainImg;
    /**
     * 细节图列表（1-4 #号拼接）
     */
    private String detailImg;

    /**
     * spu多语言
     */
    private List<SpuLangApiDTO> spuLangList;
    // 以下是sku信息
    /**
     * JDSKUID 京东SKUID
     */
    private Long jdSkuId;
    /**
     * 规格型号
     *
     * @date 2024-1-6
     */
    private String specification;

    /**
     * 生产周期--生产/备货周期（天）
     */
    private BigDecimal productionCycle;

    /**
     * 币种
     */
    private String currency;
    /**
     * 条形码
     */
    private String upcCode;
    /**
     * 采购价---未税采购价格
     */
    @Min(value = 0, message = "采购价不能小于0")
    private String purchasePrice;
    /**
     * 销售价---未税销售价格
     */
    @Min(value = 0, message = "销售价不能小于0")
    private String salePrice;
    /**
     * 库存数量
     */
    private String stockNum;

    /**
     * 最小下订数量
     */
    private Integer moq;
    /**
     * 图片列表
     */
    private List<String> detailImgList;
    /**
     * 供应商的SKU ID
     */
    private String  vendorSkuId;

    /**
     * 货源国国家代码
     */
    private String sourceCountryCode;
}
