package com.jdi.isc.product.soa.api.jdm.sku.base;

import com.jdi.isc.product.soa.api.common.BaseLangDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 属性值信息
 * <AUTHOR>
 * @date 2024/7/8
 **/
@Data
public class JdmPropertyValueDTO implements Serializable {
    /**
     * 属性ID
     */
    private Long attributeId;
    /**
     * 属性值ID
     */
    @NotNull(message = "属性值ID不能为空")
    private Long attributeValueId;
    /**
     * 属性值名
     */
    @Size(max = 100, message = "超长")
//    @Pattern(regexp = "^[^,。#！？【】；!\\?\\[\\]@\\$\\^`\\*\\.]+$", message = "属性值不能包含特殊字符")
    private String attributeValueName;
    /**
     * 排序字段
     */
    private Integer sort;
    /**
     * 属性值语种 ZH、VN、TH
     */
    private String lang;
    /**
     * 当前是否选择状态,默认未选中
     */
    private Boolean selected;
    /**
     * 是否必填
     */
    private Boolean required;
    /**
     * 提示语
     */
    private String placeholderValue;
    /**
     * 属性值语种 ZH、VN、TH
     */
    private String langName;
    
    /**
     * 当前组内的多语属性值
     */
    private List<BaseLangDTO> langList; // ZHAOYAN_SALE_ATTR
}
