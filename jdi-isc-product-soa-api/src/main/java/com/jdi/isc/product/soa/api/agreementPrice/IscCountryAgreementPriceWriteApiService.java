package com.jdi.isc.product.soa.api.agreementPrice;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.agreementPrice.req.*;
import com.jdi.isc.product.soa.api.agreementPrice.res.CountryAgreementPriceSyncResDTO;
import com.jdi.isc.product.soa.api.price.req.PriceAvailableSaleStatusReqDTO;
import com.jdi.isc.product.soa.api.price.res.PriceAvailableSaleStatusResDTO;
import com.jdi.isc.product.soa.api.pricewarn.dto.PriceWarnMqDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuPoolFlagDTO;

import java.util.List;
import java.util.Set;

/**
 * @Description: 国家协议价api服务
 * @Author: wangpeng965
 * @Date: 2025/03/03 16:45
 **/

public interface IscCountryAgreementPriceWriteApiService {

    /**
     * 保存、更新
     * @param dto 提交参数
     * @return 结果
     */
    DataResponse<Boolean> updateDraft(CountryAgreementPriceReqDTO dto);

    /**
     * 通过-批量
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<String> batchApprove(CountryAgreementPriceAuditDTO input);

    /**
     * 驳回-批量
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<String> batchReject(CountryAgreementPriceAuditDTO input);

    /**
     * 撤回-批量
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<String> batchRevoke(CountryAgreementPriceAuditDTO input);

    /**
     * 更新国家协议价格和京东价格。
     * @param dto 国家协议价格和京东价格请求体对象。
     * @return 更新操作的结果。
     */
    DataResponse<Boolean> updateAgreementAndJdPrice(CountryAgreementPriceReqDTO dto);

    /**
     * 更新国家协议价格和京东价格。
     * @param dto 国家协议价格和京东价格请求体对象。
     * @return 更新操作的结果。
     */
    DataResponse<Boolean> applyForUpdateAgreementAndJdPrice(CountryAgreementPriceReqDTO dto);

    /**
     * 初始化国家协议警告价格。
     * @param ids 需要初始化的国家协议ID集合。
     * @return 初始化结果。
     */
    @Deprecated
    DataResponse<String> initCountryAgreementWarningPrice(Set<Long> ids);

    /**
     * 初始化国家协议预警价格数据V2版本
     * @param ids 需要初始化预警价格的国家协议ID集合
     * @param dataStatusSource 数据状态来源标识
     * @return 包含处理结果的响应对象，响应数据类型为String
     */
    DataResponse<String> initCountryAgreementWarningPriceV2(Set<Long> ids, Integer dataStatusSource);


    /**
     * 初始化国家成本价格。
     * @param dto 包含刷新价格所需的数据的DTO对象。
     * @return 操作结果，true表示成功，false表示失败。
     */
    DataResponse<Boolean> initCountryCostPrice(PriceRefreshDTO dto);

    /**
     * 批量同步国家建议协议价到国家协议价。
     * @return 同步结果。
     */
    DataResponse<CountryAgreementPriceSyncResDTO> batchSyncSuggestToAgreementPrice(CountryAgreementPriceSyncReqDTO dto);


    /**
     * 更新商品的可售状态
     * @param input 商品可售状态请求DTO列表，包含需要更新的商品状态信息
     * @return 包含更新后的商品可售状态响应DTO的数据响应对象
     */
    DataResponse<PriceAvailableSaleStatusResDTO> updateAvailableSaleStatus(List<PriceAvailableSaleStatusReqDTO> input);


    /**
     * 刷新国家协议警告价格
     * @param input Mku池标志DTO列表，包含需要刷新警告价格的国家协议相关信息
     * @return 包含操作结果的DataResponse对象，其中Boolean类型表示操作是否成功
     */
    DataResponse<Boolean> refreshCountryAgreementWarningPrice(List<MkuPoolFlagDTO> input);


    /**
     * 根据最小ID获取指定数量的协议价格ID列表
     * @param minId 查询的最小ID，作为查询的起始条件
     * @return 包含协议价格ID列表的数据响应对象
     */
    DataResponse<List<Long>> getAgreementPriceIds(Long minId);


    /**
     * 初始化国家协议价格和客制化价格
     * @param agreementIds 需要初始化价格的协议ID集合
     */
    DataResponse<String> initPricePoolStatus(Set<Long> agreementIds);

    /**
     * 发送国家协议预警价格初始化消息
     * @param agreementIds 协议ID集合，不能为空
     * @return 包含操作结果的数据响应对象，响应内容为字符串类型
     */
    DataResponse<InitCountryAgreementMqDTO> sendInitCountryAgreementWarningPriceMessage(Set<Long> agreementIds);

    /**
     * 写入价格预警.
     *
     * @param priceWarnMqDTO 价格预警信息
     * @return 写入结果
     */
    DataResponse<String> writePriceWarn(PriceWarnMqDTO priceWarnMqDTO);

}
