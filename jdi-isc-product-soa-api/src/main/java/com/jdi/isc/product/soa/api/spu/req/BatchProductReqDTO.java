package com.jdi.isc.product.soa.api.spu.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.api.common.BasicReqDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/3/19
 **/
@Data
public class BatchProductReqDTO extends BaseReqDTO {
    /**
     * 商品ID集合
     */
    private Set<Long> spuIds;
    /**
     * 站点基本信息
     */
    private BasicReqDTO basicReqDTO;

    /**
     * 注意：只支持传0，不支持传其他的值
     */
    private Integer yn;

}
