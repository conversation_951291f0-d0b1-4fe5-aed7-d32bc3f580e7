package com.jdi.isc.product.soa.api.taxRate.res;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 类目税率
 * @Date 2024/4/1 2:00 下午
 */

@Data
public class CategoryTaxRateRes {

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 企业性质 1:EPE, 2:FDI
     */
    private Integer companyType;

    /**
     * 末级类目id
     */
    private Long categoryId;

    /**
     * 客户简码
     */
    private String clientCode;

    /**
     * 海关编码
     */
    private String hsCode;

    /**
     * 增值税率
     */
    private BigDecimal vatRate;
}
