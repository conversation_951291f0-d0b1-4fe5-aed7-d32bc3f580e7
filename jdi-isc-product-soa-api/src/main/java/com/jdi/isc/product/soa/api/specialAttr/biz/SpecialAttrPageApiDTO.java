package com.jdi.isc.product.soa.api.specialAttr.biz;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import com.jdi.isc.product.soa.api.specialAttr.res.SpecialAttrDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 商品特殊属性分页查询对象
 * @Author: zhangjin176
 * @Date: 2024/12/11 09:58
 **/

@Data
public class SpecialAttrPageApiDTO implements Serializable {


    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Request extends BasePageReqDTO implements Serializable {
        /**
         * 属性值名称，同个属性下不可重复
         */
        private String attributeKeyName;

        /**
         * 属性值名称，必填，同个属性下不可重复
         */
        private String countryScope;

        /**
         * 状态 0-无效 1-有效
         */
        private Integer status;

        /**
         * 组织ID，用于标识所属组织。
         */
        private String orgId;

    }

    @Data
    public static class Response implements Serializable{

        /**
         * 特殊属性列表。
         */
        List<SpecialAttrDTO> list;
    }

}