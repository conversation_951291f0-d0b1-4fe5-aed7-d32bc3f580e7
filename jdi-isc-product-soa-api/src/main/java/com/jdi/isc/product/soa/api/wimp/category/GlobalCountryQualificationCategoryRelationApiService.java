package com.jdi.isc.product.soa.api.wimp.category;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.wimp.category.req.GlobalCountryQualificationCategoryRelationApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.GlobalCountryQualificationCategoryRelationPageApiDTO;

/**
 * @Description: 国家资质类目关联api服务
 * @Author: taxuezheng1
 * @Date: 2024/07/12 11:33
 **/

public interface GlobalCountryQualificationCategoryRelationApiService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<GlobalCountryQualificationCategoryRelationPageApiDTO.Response>> pageSearch(GlobalCountryQualificationCategoryRelationPageApiDTO.Request input);

    /**
     * 保存、更新
     */
    DataResponse<Boolean> saveOrUpdate(GlobalCountryQualificationCategoryRelationApiDTO input);

    /**
     * 详情
     */
    DataResponse<GlobalCountryQualificationCategoryRelationApiDTO> detail(GlobalCountryQualificationCategoryRelationPageApiDTO.Request input);
}
