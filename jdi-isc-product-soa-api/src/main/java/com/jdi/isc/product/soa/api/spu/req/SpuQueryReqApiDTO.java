package com.jdi.isc.product.soa.api.spu.req;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SpuQueryReqApiDTO extends BasePageReqDTO {

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * SPUID
     */
    private Long spuId;

    /**
     * 供应商的spuId
     */
    private String vendorSpuId;

    /**
     * 采购员
     */
    private String buyer;

    /**
     * 供应商简码
     */
    private String vendorCode;
    /**
     * 国内供应商简码
     */
    private String jdVendorCode;
    /**
     * 商品名称
     */
    private String spuName;
    /**
     * 一级类目ID
     */
    private Integer catFirstId;
    /**
     * 二级类目ID
     */
    private Integer catSecondId;
    /**
     * 三级类目ID
     */
    private Integer catThirdId;
    /**
     * 四级类目ID
     */
    private Integer catFourId;
    /**
     * 属性范围
     */
    private String attributeScope;
    /**
     * 属性范围多个
     */
    private List<String> attributeScopes;
    /**
     * 状态
     */
    private Integer spuStatus;
    /**
     * 发货国 CN VN TH US
     */
    private String sourceCountryCode;
    /**
     * 国内工业skuId
     */
    private Long jdSkuId;
    /**
     * 审核状态
     */
    private Integer auditStatus;
    /**
     * 审核等级
     */
    private Integer level;

    /**
     * 审核等级
     */
    private Long mkuId;

    /**
     * skuIds
     */
    private List<Long> skuIds;

    /**
     * 客户编码
     * */
    private String clientCode;

    /**
     * 关键字
     * */
    private String key;

    /**
     * 末级类目ID
     */
    private Long catId;

    /**
     * 税务审核状态
     */
    private Integer taxAuditStatus;

    /**
     * spuIds
     */
    private List<Long> spuIds;

    /**
     * 品牌id
     */
    private Integer brandId;

    /**
     * 国内工业skuId
     */
    private List<Long> jdSkuIds;

    /**
     * mkuId 集合
     */
    private List<Long> mkuIds;

    /**
     * 标属性值id
     */
    private Long attributeValueId;
    /**
     * sku上下架状态
     */
    private Integer skuStatus;
}
