package com.jdi.isc.product.soa.api.price.supplierPrice.req;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SkuPricePageReqDTO extends BasePageReqDTO implements Serializable {

    private String vendorCode;

    private Long skuId;

    /** 货源国 */
    private String sourceCountryCode;

    /**
     * 审核状态，1:审核通过，2:驳回 3:待审核,0:撤销
     */
    private Integer auditStatus;

    /**
     * 类型关键字，用于区分不同类型的请求。
     */
    private String typeKey;

    /**
     * SKU对应的SPU ID列表
     */
    private List<Long> spuIds;

    /**
     * SKU对应的ID列表
     */
    private List<Long> skuIds;

    /**
     * MKU对应的ID列表
     */
    private List<Long> mkuIds;
}