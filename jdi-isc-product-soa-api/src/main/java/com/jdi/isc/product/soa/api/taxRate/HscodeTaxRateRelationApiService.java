package com.jdi.isc.product.soa.api.taxRate;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.taxRate.biz.HscodeTaxRateRelationPageApiDTO;
import com.jdi.isc.product.soa.api.taxRate.req.HscodeTaxRateRelationDetailReqApiDTO;
import com.jdi.isc.product.soa.api.taxRate.req.HscodeTaxRateRelationSaveUpdateReqApiDTO;
import com.jdi.isc.product.soa.api.taxRate.res.HscodeTaxRateRelationResApiDTO;

/**
 * @Description: 跨境税率关系api服务
 * @Author: chengliwei
 * @Date: 2024/12/11 10:47
 **/

public interface HscodeTaxRateRelationApiService {

    /**
     * 详情查询
     * @param input 查询条件
     * @return 查询结果
     */
    DataResponse<HscodeTaxRateRelationResApiDTO> detail(HscodeTaxRateRelationDetailReqApiDTO input);
    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<HscodeTaxRateRelationPageApiDTO.Response>> pageSearch(HscodeTaxRateRelationPageApiDTO.Request input);
}