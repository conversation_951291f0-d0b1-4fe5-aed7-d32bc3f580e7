package com.jdi.isc.product.soa.api.sku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.BaseResDTO;
import com.jdi.isc.product.soa.api.sku.req.*;
import com.jdi.isc.product.soa.api.validation.SkuWriteValidateGroup;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

/**
 * spu写服务
 * <AUTHOR>
 * @date 2024/07/15
 */
public interface IscProductSoaSkuWriteApiService {

    /**
     * @deprecated 该方法已不推荐使用，后续会删除。
     */
    @Deprecated
    DataResponse<List<SkuUpdateApiDTO>> updateSku(List<SkuUpdateApiDTO> skuAmendApiDTOList);

    /**
     * 更新跨境属性
     * @param input SkuGlobalAttributeApiDTO 对象，包含需要更新的全局属性信息
     * @return DataResponse<String> 对象，表示更新操作的结果
     */
    DataResponse<String> updateGlobalAttribute(SkuGlobalAttributeApiDTO input);

    /**
     * 每次只能更新一个sku, 这里传入多个是因为有可能有脏数据, 原则上还是一个sku.
     *
     * @param input the input
     * @return the data response
     */
    DataResponse<Void> saveProductGlobalNcmAttribute(SkuSyncNcmDTO input);

    /**
     * 批量更新自定义信息。
     * @param reqDTO 包含批量更新请求的DTO对象。
     * @return 更新结果，Map的key为自定义信息的ID，value为对应的更新结果DTO。
     */
    @Validated
    DataResponse<Map<String, BaseResDTO>> batchUpdateCustoms(BatchUpdateCustomsApiReqDTO reqDTO);

    /**
     * 批量修复商品NCM编码
     * @param jdSkuIds 需要修复的京东SKU ID列表
     * @param update 是否执行更新操作
     * @param updater 操作人姓名
     * @return 包含处理结果数量的响应对象
     */
    DataResponse<Integer> batchFixBrNcmCode(List<Long> jdSkuIds, boolean update, String updater);

    /**
     * 更新商品SKU状态
     * @param reqDTO 更新SKU状态请求参数
     * @return 更新结果
     */
    @Validated(SkuWriteValidateGroup.skuUpOrDown.class)
    DataResponse<Boolean> skuUpOrDown(UpdateSkuStatusReqDTO reqDTO);

    /**
     * 更新 SKU 同步主站状态切换
     * @param reqDTO 更新 SKU 状态请求对象
     * @return 操作结果
     */
    @Validated(SkuWriteValidateGroup.updateSkuMainSyncStatus.class)
    DataResponse<Boolean> updateSkuMainSyncStatus(UpdateSkuStatusReqDTO reqDTO);


    /**
     * 根据供应商代码更新商品状态。
     * @param reqVO UpdateSkuStatusBySupplierCodeReqVO 对象，包含需要更新的商品信息和新的状态。
     * @return DataResponse(Boolean) 对象，表示更新操作的结果。
     */
    DataResponse<Boolean> skuUpOrDownBySupplierCode(UpdateSkuStatusBySupplierCodeReqDTO reqVO);
}
