package com.jdi.isc.product.soa.api.subtitle.biz;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * The type Mku subtitle import excel dto.
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
public class MkuSubtitleImportReqDTO extends BasicApiDTO {

    private List<MkuSubtitleImportReqDTO.Item> items;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Item extends BaseReqDTO implements Serializable {
        @NotEmpty(message = "MKUID为空")
        private String mkuId;

        /**
         * 商品巴葡短标题。
         */
        @NotEmpty(message = "短标题为空")
        private String mkuSubtitle;

        /**
         * 数据是否有效,有效才触发db写入
         */
        protected Boolean valid = true;

        protected List<String> errorMsgs = new ArrayList<>();

        protected String result;

        public void success() {
            this.result = "success";
        }

        public void failed(String result) {
            this.valid = false;
            this.result = result;
        }

        public void addErrorMsg(String errorMsg) {
            if (!StringUtils.hasText(errorMsg)) {
                return;
            }
            List<String> data = new ArrayList<>();
            data.add(errorMsg);
            this.addErrorMsg(data);
        }

        public void addErrorMsg(List<String> data) {

            if (CollectionUtils.isEmpty(data)) {
                return;
            }

            this.valid = false;
            if (CollectionUtils.isEmpty(this.errorMsgs)) {
                errorMsgs = new ArrayList<>();
            }
            this.errorMsgs.addAll(data);
        }
    }

}