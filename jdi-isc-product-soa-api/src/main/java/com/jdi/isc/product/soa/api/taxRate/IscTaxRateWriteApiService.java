package com.jdi.isc.product.soa.api.taxRate;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.taxRate.req.BrImportTaxReqDTO;
import com.jdi.isc.product.soa.api.taxRate.req.TaxRateReqDTO;
import com.jdi.isc.product.soa.api.taxRate.res.TaxRateApiDTO;

/**
 * 税率写服务
 * <AUTHOR>
 * @date 2024/11/08
 */
public interface IscTaxRateWriteApiService {

    /**
     * 新增
     */
    DataResponse<TaxRateApiDTO> saveOrUpdate(TaxRateReqDTO dto);

    /**
     * 删除
     */
    DataResponse<Boolean> delete(TaxRateReqDTO dto);

    /**
     * 更新进口税信息。
     * @param input BrImportTaxReqDTO 对象，包含需要更新的进口税信息。
     * @return DataResponse(Boolean) 对象，表示更新操作的结果。
     */
    DataResponse<Boolean> updateBrIpiTax(BrImportTaxReqDTO input);
}
