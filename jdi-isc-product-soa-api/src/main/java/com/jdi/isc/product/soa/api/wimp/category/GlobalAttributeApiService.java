package com.jdi.isc.product.soa.api.wimp.category;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.KvDTO;
import com.jdi.isc.product.soa.api.spu.res.GroupPropertyDTO;
import com.jdi.isc.product.soa.api.validation.GlobalAttributeValidGroup;
import com.jdi.isc.product.soa.api.validation.WarehouseValidGroup;
import com.jdi.isc.product.soa.api.wimp.category.req.*;
import com.jdi.isc.product.soa.api.wimp.category.res.GlobalAttributeApiResDTO;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * @Description: 国际跨境属性api服务
 * @Author: taxuezheng1
 * @Date: 2024/07/12 13:17
 **/

public interface GlobalAttributeApiService {

    /**
     * 保存更新接口
     */
    DataResponse<Boolean> saveOrUpdate(GlobalAttributeApiDTO input);

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<GlobalAttributePageApiDTO.Response>> pageSearch(GlobalAttributePageApiDTO.Request input);


    /**
     * 详情
     * @return VO对象
     */
    DataResponse<GlobalAttributeApiDTO> detail(GlobalAttributeApiDTO input);


    /**
     * 校验多语言名称重复
     */
    @Validated(GlobalAttributeValidGroup.save.class)
    DataResponse<Boolean> checkLangName(GlobalAttributeApiDTO input);


    /**
     * 根据维度查询属性列表
     */
    DataResponse<List<GlobalAttributePageApiDTO.Response>> queryAttributeList(GlobalAttributePageApiDTO.Request  input);

    /**
     * 根据属性查询属性值列表
     */
    DataResponse<List<GlobalAttributeValueApiDTO>> queryAttributeValueList(GlobalAttributePageApiDTO.Request  input);

    /**
     * 根据属性查询属性值列表
     */
    DataResponse<List<KvDTO>> queryAttributeName(List<Long> ids, String lang);

    /**
     * 根据属性查询属性值列表
     */
    DataResponse<List<GlobalResApiDTO>> queryRequireAttribute(List<GlobalReqApiDTO> inputs);

    /**
     * 根据类目ID查询属性列表
     */
    DataResponse<List<GlobalAttributeApiResDTO>> queryAttributeListByCatId(GlobalAttributeQueryReqDTO input);

    /**
     * 根据类目ID查询属性列表
     */
    DataResponse<List<GroupPropertyDTO>> queryGroupGlobalAttributeByCatId(GlobalAttributeQueryReqDTO input);
}
