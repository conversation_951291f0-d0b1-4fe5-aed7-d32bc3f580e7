package com.jdi.isc.product.soa.api.orderVerify.req;

import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OrderOutVerifyReqDTO extends BasicDTO {

    /**
     * 订单id
     */

    private Long orderId;

    /**
     * 客户编码
     */

    private String clientCode;

    /**
     * 下单时间
     */

    private Long orderCreateTime;

    /**
     * SKUID
     */

    private Long skuId;

    /**
     * JDSKUID
     */
    private Long jdSkuId;

    /**
     * 目的国
     */
    @NotNull(message = "目的国不能为空")
    private String countryCode;

    /**
     * 缺失的跨境属性
     */
    private String loseProperty;

    /**
     * 缺失的跨境资质
     */
    private String loseCertificate;
}
