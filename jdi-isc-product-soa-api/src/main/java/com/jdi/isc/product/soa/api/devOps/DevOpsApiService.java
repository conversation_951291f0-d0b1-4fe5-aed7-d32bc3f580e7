package com.jdi.isc.product.soa.api.devOps;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.devOps.req.DevCustomerMkuStatusQueryDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuFeatureApiDTO;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.taxRate.req.TaxRateReqDTO;
import com.jdi.isc.product.soa.api.wimp.brand.req.BrandApiDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

/**
 * 研发使用接口
 * <AUTHOR>
 * @date 2024/7/29
 **/
public interface DevOpsApiService {

    /**
     * @function 根据提供的仓库ID集合删除对应的仓库
     * @param ids 要删除的仓库的ID集合
     * @param env 标识
     * @returns 删除操作的结果，包括成功或错误信息的字符串
     */
    DataResponse<String> removeWarehouse(Set<Long> ids,String env);


    /**
     * @function 添加品牌信息到Xbp系统
     * @param brandApiDTO - 包含品牌信息的数据传输对象
     * @returns DataResponse<String> - 包含操作结果信息的数据响应对象
     */
    DataResponse<String> addBrandXbp(BrandApiDTO brandApiDTO);

    /**
     * @function 更新指定仓库的库存信息
     * @param reqDTO - 包含仓库ID、商品ID以及需要更新的库存数量等信息的数据传输对象
     * @returns 返回更新操作的响应数据，包含操作结果的字符串信息
     */
    DataResponse<String> updateStockForWarehouse(StockItemManageReqDTO reqDTO);

    /**
     * @function 修改品牌信息到Xbp系统
     * @param brandApiDTO - 包含品牌信息的数据传输对象
     * @returns DataResponse<String> - 包含操作结果信息的数据响应对象
     */
    DataResponse<String> modifyBrandXbp(BrandApiDTO brandApiDTO);

    /**
     * 删除问题库存数据
     * @param spuIds
     * @return
     */
    DataResponse<String> removeWrongStockInfo(Set<Long> spuIds, String env);

    /**
     * 从指定环境中删除错误的股票数据。
     * @param ids 需要删除的股票ID集合。
     * @param type 股票类型。1:SKU和-1仓关联 2：SKU仓为-1库存 3：SKU仓-1库存日志
     * @param env 环境标识。
     * @return 删除操作的结果。
     */
    DataResponse<String> removeWrongStock(Set<Long> ids,Integer type,String env);

    /**
     * 将多语言SPU的英文翻译转换为对应的MKU的英文翻译。
     * @param mkuId MKU的ID列表
     * @return 包含转换后的MKU的ID的DataResponse对象
     */
    DataResponse<List<Long>> spuLang2mkuLang(List<Long> mkuId,String lang);

    /**
     * 更新MKU状态
     * @param mkuId MKU的ID列表
     * @param status 新的状态值
     * @return 包含更新成功的MKU ID的列表
     */
    DataResponse<List<Long>> updateMkuStatus(List<Long> mkuId, Integer status);


    /**
     * 在指定环境中批量更新商品的特性信息。
     * @param skuIds 需要更新特性的商品ID集合。
     * @param env 更新特性的环境，例如：dev、test、prod。
     * @return 更新操作的结果。
     */
    DataResponse<String> updateSkuFeature(Set<Long> skuIds,String env);


    DataResponse<String> createSkuDraftBySkuId(Set<Long> skuIds);

    DataResponse<String> updateSpuExtAttribute(Long spuId,String attribute);

    /**
     * 更新商品库存状态。
     * @param skuAndId 包含商品ID和SKU的集合。
     * @param env 环境标识。
     * @return 更新结果。
     */
    DataResponse<String> updateSkuStockYn(Set<String> skuAndId, String env,Integer yn);
    /**
     * 更新SPU草稿的JSON数据。
     * @param spuId SPU的ID。
     * @param spuDraft SPU草稿的JSON数据。
     * @return 更新结果。
     */
    DataResponse<String> updateSpuDraftJson(Long spuId,String spuDraft);

    /**
     * 更新指定 SKU 的价格和税费价格。
     * @param id SKU 的唯一标识符。
     * @param price SKU 的新价格。
     * @return 包含更新结果的 DataResponse 对象。
     */
    DataResponse<String> updateSkuPrice(Long id, BigDecimal price);


    DataResponse<String> revokeJoySky(String erp, String processInstance,Integer joySkyType);


    DataResponse<String> updateSpuAttributeContainSymbol(Set<Long> spuIds);

    DataResponse<Boolean> delete(TaxRateReqDTO dto);
    /**
     * 更新指定 SKU 的价格和税费价格。
     * @param id SKU 的唯一标识符。
     * @param taxPrice SKU 的新价格。
     * @return 包含更新结果的 DataResponse 对象。
     */
    DataResponse<String> updateSkuTaxPrice(Long id, BigDecimal taxPrice);

    /**
     * 更新SPU记录的yn字段
     * @param id recordId
     * @return 更新结果
     */
    DataResponse<String> updateSpuRecordYn(Long id);

    /**
     * 根据客户信息查询 MKU 状态分布情况。
     * @param queryDTO 包含查询条件的对象。
     * @return 客户 MKU 状态分布情况的 Map，key 为客户 ID，value 为 MKU 状态到数量的 Map。
     */
    Map<String, Map<String, Long>> queryClientMkuStatusMap(DevCustomerMkuStatusQueryDTO queryDTO);

    /**
     * 初始化SPU SKU JSON数据。
     * @param spuIds 需要初始化的SPU ID集合。
     * @return 包含初始化结果的DataResponse对象。
     */
    DataResponse<String> initSpuInter(Set<Long> spuIds);

    /**
     * 批量删除SPU关联数据。
     * @param ids 要删除的SPU关联数据ID集合。
     * @return 删除操作的结果。
     */
    DataResponse<String> deleteSpuInter(Set<Long> ids);

    /**
     * 初始化SPU SKU的JSON数据。
     * @return 包含SPU SKU信息的JSON字符串。
     */
    DataResponse<String> deleteAllInter();

    /**
     * 更新国家 MKU 状态。
     * @param country 国家名称。
     * @param sourceStatus 原始状态。
     * @param targetStatus 目标状态。
     * @return 更新结果。
     */
    DataResponse<String> updateCountryMkuStatus(String  country,Integer sourceStatus,Integer targetStatus);
    DataResponse<String> initSpuSkuJSON(List<Long> spuIds);

    /**
     * 添加 SKU 特性包规格。
     * @param apiDTOList SKU 特性包规格的 API DTO 列表。
     * @return 添加操作的结果。
     */
    DataResponse<String> addSkuFeaturePackageSpecification(List<SkuFeatureApiDTO> apiDTOList);

    /**
     * 在指定环境中初始化股票阈值。
     * @param env 环境名称，如"dev"、"test"或"prod"。
     * @return 初始化结果的详细信息。
     */
    DataResponse<String> initStockThreshold(String env);

    /**
     * 在指定环境中截断库存阈值。
     * @param env 环境名称，用于指定操作的目标环境。
     * @return 操作结果的详细信息。
     */
    DataResponse<String> truncateStockThreshold(String env);


    /**
     * 初始化客户 SKU 详细信息草稿。
     * @param draftIds 草稿 ID 集合。
     * @return 操作结果。
     */
    DataResponse<String> initCustomerSkuDetailDraft(Set<Long> draftIds);

    /**
     * 初始化客户 SKU 详细信息草稿。
     * @param detailDraftIds 草稿 ID 集合。
     * @return 操作结果。
     */
    DataResponse<String> deleteCustomerSkuDetailDraft(Set<Long> detailDraftIds);

    /**
     * 根据产品ID查询产品信息
     * @param productId 产品ID
     * @return 包含产品信息的DataResponse对象
     */
    DataResponse<JSONObject> queryProductInfo(String productId);

    DataResponse<String> getFulfillmentRate(Long skuId,String sourceCountryCode,String targetCountryCode);

    /**
     * 更新指定 SKU 的属性可见范围。
     * @param skuIds SKU ID 列表。
     * @param attributeScope 属性可见范围。
     * @return 更新结果。
     */
    DataResponse<String> updateSpuAttributeScope(List<Long> skuIds, String attributeScope);

    /** 移除重复的扩展属性多语言信息*/
    DataResponse<Void> removeDulExtAttr(Boolean delFlag);

    /**
     * 修复巴西跨境商品的Ncm码.
     *
     * @param jdSkuIds the jd sku ids
     * @return the data response
     */
//    DataResponse<Integer> batchFixBrNcmCode(List<Long> jdSkuIds, boolean update);

    /**
     * 更新转移库存数量
     * @param skuId SKU的ID
     * @param warehouseId 仓库的ID
     * @param transferStockNum 转移库存的数量
     * @return 更新后的库存信息
     */
    DataResponse<String> updateTransferStock(Long skuId, Long warehouseId, Long transferStockNum);


    /**
     * 更新指定 SKU 的销售属性。
     * @param skuIds 需要更新销售属性的 SKU ID 列表。
     * @param saleAttribute 新的销售属性值。
     * @return 更新操作的结果。
     */
    DataResponse<String> updateSaleAttribute(List<Long> skuIds, String saleAttribute);

    /**
     * 商品报关数据初始化
     * @param startSpuId
     * @param batchSize
     * @return
     */
    DataResponse<String> productCustomsInit(Long startSpuId, int batchSize);

    /**
     * 批量处理所有SPU扩展属性数据，将旧格式的extAttribute转换为新格式的groupExtAttribute
     * 同时更新SPU和SKU的groupExtAttribute字段
     * 
     * @return 处理结果
     */
    DataResponse<String> processAllSpuExtAttributes(String updateTime);

    /**
     * 批量修复商品NCM编码
     * @param jdSkuIds 需要修复的京东SKU ID列表
     * @param update 是否执行更新操作
     * @param updater 操作人姓名
     * @return 包含处理结果数量的响应对象
     */
    DataResponse<Integer> batchFixBrNcmCode(List<Long> jdSkuIds, boolean update, String updater);

    /**
     * 批量处理所有MKU扩展属性数据，将旧格式的extAttribute转换为新格式的groupExtAttribute
     * 
     * @return 处理结果
     */
    DataResponse<String> processAllMkuExtAttributes(String updateTime);

    /**
     * 批量处理所有SpuDraft扩展属性数据，将extProperty字段转换为新格式的groupExtAttribute
     * 
     * @return 处理结果
     */
    DataResponse<String> processAllSpuDraftExtProperties(String updateTime);

    /**
     * 处理单条SPU扩展属性数据
     * @param spuId
     * @return
     */
    DataResponse<String> processSpuExtAttributesBySpuId(Long spuId);

    /**
     * 处理单条SpuDraft扩展属性数据
     * @param spuId
     * @return
     */
    DataResponse<String> processSpuDraftExtAttributesBySpuId(Long spuId);

    /**
     * 处理单条MKU扩展属性数据
     * @param mkuId
     * @return
     */
    DataResponse<String> processMkuExtAttributesByMkuId(Long mkuId);

    /**
     * 刷新国家协议警告价格
     */
    void refreshCountryAgreementWarningPrice();

    /**
     * 根据京东SKU ID获取对应的增值税率
     * @param jdSkuId 京东商品SKU ID，用于标识唯一商品
     * @return 返回对应商品的增值税率，以BigDecimal类型表示
     */
    BigDecimal getJdVatRate(Long jdSkuId);

    /**
     * 根据京东商品SKU ID获取对应增值税率
     * @param jdSkuId 京东商品SKU ID，不能为null
     * @return 返回商品对应的增值税率，使用BigDecimal保证精度
     */
    BigDecimal getJdSkuVatRate(Long jdSkuId);

    /**
     * 根据京东SKU ID获取对应类目的增值税率
     * @param jdSkuId 京东商品SKU ID，用于标识唯一商品
     * @return 返回对应商品类目的增值税率，以BigDecimal类型表示
     */
    BigDecimal getJdCategoryVatRate(Long jdSkuId);

    /**
     * 向指定客户详情ID集合发送初始客户预警价格消息
     * @param customerDetailIds 需要发送预警消息的客户详情ID集合
     */
    void sendInitCustomerWarningPriceMessage(Set<Long> customerDetailIds);
}
