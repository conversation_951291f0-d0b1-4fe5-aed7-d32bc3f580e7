package com.jdi.isc.product.soa.api.countryMku.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class CountryMkuCheckReqDTO {

   /**
    * MKU ID 列表，用于查询对应的国家信息。
    */
   private List<Long> mkuIdList;

   /**
    * 目标国家的国别代码。
    */
   private String targetCountryCode;
}
