package com.jdi.isc.product.soa.api.sku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.sku.req.*;
import com.jdi.isc.product.soa.api.sku.res.*;
import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import com.jdi.isc.product.soa.api.validation.SkuReadValidateGroup;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * sku读服务
 * <AUTHOR>
 * @date 2024/08/06
 */
public interface IscProductSoaSkuReadApiService {

    DataResponse<SkuCalculateTaxPriceDTO> getSkuCalculateSalePrice(SkuCalculatePriceReqDTO reqVO);


    /**
     * 查询SKU信息
     * @param reqDTO 查询SKU信息的请求参数
     * @return 包含SKU基本信息的响应数据
     */
    @Validated(SkuReadValidateGroup.querySkuBaseInfo.class)
    DataResponse<Map<String, SkuBaseInfoApiDTO>> querySkuBaseInfo(QuerySkuReqDTO reqDTO);


    /**
     * 查询SKU 属性
     * @param reqDTO 查询SKU信息的请求参数
     * @return 包含SKU基本信息的响应数据
     */
    @Validated(SkuReadValidateGroup.querySkuBaseInfo.class)
    DataResponse<Map<String, SkuFeatureApiDTO>> querySkuFeature(QuerySkuReqDTO reqDTO);

    /**
     * 查询SKU信息
     * @param reqDTO 查询SKU信息的请求参数
     * @return 包含SKU基本信息的响应数据
     */
    DataResponse<Map<String, SkuBaseInfoApiDTO>> querySkuBaseInfoBySpuId(QuerySkuReqDTO reqDTO);


    /**
     * 根据供应商 SKU IDs 查询 SKU 基础信息。
     * @param reqDTO 包含供应商 SKU IDs 的请求对象。
     * @return 包含 SKU 基础信息的响应对象。
     */
    DataResponse<Map<String, SkuBaseInfoApiDTO>> querySkuBaseInfoByVendorSkuIds(QuerySkuReqDTO reqDTO);

    /**
     * 根据SkuGlobalAttributeVO对象查询跨境属性详细信息。
     * @param param SkuGlobalAttributeVO对象，包含查询条件。
     * @return List<SkuGlobalAttributeDetailVO> 全局属性详细信息列表。
     */
    @Deprecated
    DataResponse<List<SkuGlobalAttributeDetailApiDTO>> selectGlobalAttribute(SkuGlobalAttributeApiDTO param);

    /**
     * 根据请求参数查询产品的跨境属性映射。
     * @param reqDTO 请求参数对象，包含查询条件等信息。
     * @return 一个DataResponse对象，内部包含一个Map<String, Map<String, PropertyApiDTO>>，表示产品的全局属性映射。
     * Key：商品ID value：Map<String,PropertyApiDTO>(key: 跨境属性ID value：跨境属性信息)
     */
    DataResponse<Map<String, Map<String, PropertyApiDTO>>> queryProductGlobalAttributeMap(GetProductGlobalAttributeReqDTO reqDTO);


    /**
     * 根据请求参数查询 SKU 的限售区域信息。
     * @param reqDTO 查询请求对象，包含 SKU 的相关信息。
     * @return 包含 SKU ID 和对应的限售区域信息的映射。
     */
    @Validated(SkuReadValidateGroup.queryCrossBorderSkuLimitAreaMap.class)
    DataResponse<Map<Long, SkuAvailableSaleResDTO>> queryCrossBorderSkuLimitAreaMap(QuerySkuAvailableSaleReqDTO reqDTO);

    /**
     * 查询跨境商品销售状态的方法。
     * @param reqDTO 查询请求对象，包含查询条件。
     * @return 返回一个DataResponse对象，其中包含了Map类型的数据，键为商品ID，值为SkuAvailableSaleResDTO对象，表示该商品的销售状态和消息。
     */
    @Validated(SkuReadValidateGroup.queryCrossBorderSkuSaleMap.class)
    DataResponse<Map<Long,SkuAvailableSaleResDTO>> queryCrossBorderSkuCanPurchaseMap(QuerySkuAvailableSaleReqDTO reqDTO);

    /**
     * 查询所有地区的跨境商品可售区域限制信息。
     * @param jdSkuIds 需要查询的跨境商品ID集合。
     * @return 包含所有地区的跨境商品可售区域限制信息的列表。
     */
    DataResponse<List<SkuAvailableSaleResDTO>> queryCrossBorderSkuLimitAreaForAllAreaList(Set<Long> jdSkuIds);
    /**
     * 查询所有地区的跨境商品可售信息。
     * @param jdSkuIds 跨境商品的京东 SKU ID 集合。
     * @return 包含所有地区的跨境商品可售信息的 DataResponse 对象。
     */
    DataResponse<List<SkuAvailableSaleResDTO>> queryCrossBorderSkuAvailableSaleForAllAreaList(Set<Long> jdSkuIds);

    /**
     * 查询国内商品对应供应商信息
     * @param jdSkuIds 国内sku集合
     * @return sku与供应商信息map
     */
    DataResponse<Map<Long, VendorDTO>>  queryJdVendor(Set<Long> jdSkuIds);

    /**
     * 通过jdSkuId查询SKU信息
     * @param reqDTO 查询SKU信息的请求参数
     * @return 包含SKU基本信息的响应数据
     */
    @Validated(SkuReadValidateGroup.querySkuInfoByJdSkuId.class)
    DataResponse<List<SkuBaseInfoApiDTO>> querySkuInfoByJdSkuId(QuerySkuReqDTO reqDTO);



    /**
     * 计算巴西预估价格.
     *
     * @param query the query
     * @return the sku predict price res dto
     */
    DataResponse<SkuPredictPriceResDTO> calculateBrPredictCountryCostPrice(SkuPredictPriceReqDTO query);

    DataResponse<ProductIdDTO> getProductInfoByIds(List<Long> productIds);


    /**
     * 根据请求参数查询商品可售映射关系。
     * @param reqDTO 查询请求对象，包含查询条件等信息。
     * @return 包含商品ID和对应可售信息的映射关系。
     */
    DataResponse<Map<Long,SkuAvailableSaleResDTO>> querySkuAvailableMap(QuerySkuAvailableSaleReqDTO reqDTO);

}
