package com.jdi.isc.product.soa.api.supplier;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.supplier.req.BusinessLineQueryReqDTO;
import com.jdi.isc.product.soa.api.supplier.res.BrandResDTO;
import com.jdi.isc.product.soa.api.supplier.res.CategoryTreeResDTO;
import com.jdi.isc.product.soa.api.validation.BusinessLineValidateGroup;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 产品线读服务
 * <AUTHOR>
 * @date 2024/9/1
 **/
public interface BusinessLineReadApiService {

    /**
     * 查询业务线下的所有类目树
     * @param queryReqDTO 业务线查询请求对象，包含业务线ID等信息
     * @return 类目树列表，按照层级结构返回
     */
    DataResponse<List<CategoryTreeResDTO>> queryCategoryTreeBySupplierCode(BusinessLineQueryReqDTO queryReqDTO);

    /**
     * 根据供应商代码查询品牌列表
     * @param queryReqDTO 业务线查询请求对象，包含供应商代码等信息
     * @return 品牌列表的数据响应对象
     */
    DataResponse<List<BrandResDTO>> queryBrandListBySupplierCode(BusinessLineQueryReqDTO queryReqDTO);
}
