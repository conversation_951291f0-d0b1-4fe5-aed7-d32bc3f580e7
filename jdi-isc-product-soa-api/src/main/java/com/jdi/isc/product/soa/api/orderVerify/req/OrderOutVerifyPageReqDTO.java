package com.jdi.isc.product.soa.api.orderVerify.req;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OrderOutVerifyPageReqDTO extends BasePageReqDTO implements Serializable {


    private Long orderId;


    private String clientCode;

    /**
     * 下单时间开始
     */

    private Long orderCreateTimeStart;

    /**
     * 下单时间结束
     */
    private Long orderCreateTimeEnd;

    /**
     * SKUID
     */

    private Long skuId;

    /**
     * JDSKUID
     */
    private Long jdSkuId;

    /**
     * 目的国
     */

    private String countryCode;

    /**
     * 缺失的跨境属性
     */
    private String loseProperty;

    /**
     * 缺失的跨境资质
     */
    private String loseCertificate;
}
