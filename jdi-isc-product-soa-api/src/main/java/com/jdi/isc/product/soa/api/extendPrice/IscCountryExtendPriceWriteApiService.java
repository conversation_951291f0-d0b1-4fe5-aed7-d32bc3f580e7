package com.jdi.isc.product.soa.api.extendPrice;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.extendPrice.res.CountryExtendPriceDTO;

import java.util.List;

public interface IscCountryExtendPriceWriteApiService {
    /**
     * 保存、更新
     * @param dto 提交参数
     * @return 结果
     */
    DataResponse<Boolean> saveOrUpdate(CountryExtendPriceDTO dto);

    /**
     * 批量保存或更新国家协议价格信息。
     * @param dtoList 国家协议价格DTO列表。
     * @return 批量保存或更新操作的结果。
     */
    DataResponse<List<CountryExtendPriceDTO>> batchSaveOrUpdate(List<CountryExtendPriceDTO> dtoList);
}

