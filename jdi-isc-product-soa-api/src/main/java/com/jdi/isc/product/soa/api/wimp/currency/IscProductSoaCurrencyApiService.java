package com.jdi.isc.product.soa.api.wimp.currency;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.wimp.currency.res.CurrencyResDTO;

import java.util.List;
import java.util.Map;

/**
 * @Description: 货币主数据api服务
 * @Author: zhaokun51
 * @Date: 2024/08/16 14:37
 **/

public interface IscProductSoaCurrencyApiService {

    /**
     * 查询列表
     */
    @Deprecated
    DataResponse<List<CurrencyResDTO>> queryList();

    /**
     * 查询列表
     */
    DataResponse<List<CurrencyResDTO>> queryListByLang(String lang);

    /**
     * 重新加载缓存中配置
     * @return 返回重新加载是否成功
     */
    DataResponse<Boolean> reloadCache();

    /**
     * 获取货币列表
     * @return 货币列表，如果缓存数据为空则返回 null
     */
    DataResponse<List<String>> getCurrencyCodeList();

    /**
     * 获取货币映射
     * @return 包含货币代码和对应名称的映射数据响应
     */
    @Deprecated
    DataResponse<Map<String,String>> getCurrencyMap();

    /**
     * 获取货币映射表
     * @param lang 语言代码
     * @return 包含货币映射表的 DataResponse 对象
     */
    DataResponse<Map<String,String>> getCurrencyMapByLang(String lang);

    /**
     * 根据货币代码获取货币名称
     * @return 包含货币名称的 DataResponse 对象
     */
    @Deprecated
    DataResponse<String> getCurrencyNameByCurrencyCode(String currencyCode);

    /**
     * 根据货币代码和语言获取货币名称
     * @param countryCode 国家代码
     * @param lang 语言
     * @return 包含货币名称的 DataResponse 对象
     */
    DataResponse<String> getCurrencyNameByCurrencyCodeAndLang(String countryCode,String lang);
}
