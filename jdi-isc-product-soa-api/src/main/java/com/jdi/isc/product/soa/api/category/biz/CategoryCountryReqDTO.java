package com.jdi.isc.product.soa.api.category.biz;

import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/03 3:42 下午
 */
@Data
@NoArgsConstructor
public class CategoryCountryReqDTO extends BasicDTO {

    /**
     * 国家或地区的CODE。
     */
    private String countryCode;

    /**
     * 被禁止的类目列表
     */
    private List<Long> banCatList;

    /**
     * 版本号，用于控制和追踪数据的变更。
     */
    private String version;
}
