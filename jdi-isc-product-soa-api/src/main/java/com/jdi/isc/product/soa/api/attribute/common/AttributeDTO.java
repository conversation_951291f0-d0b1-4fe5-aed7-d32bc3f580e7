package com.jdi.isc.product.soa.api.attribute.common;

import com.jdi.isc.product.soa.api.common.BaseLangDTO;
import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import lombok.Data;

import java.util.List;

import java.util.Map;

/**
 * 属性对象
 * <AUTHOR>
 * @date 2024/9/1
 **/
@Data
public class AttributeDTO extends BasicApiDTO {

    /**
     * Integer shield（隐藏属性 shield字段1为隐藏）
     */
    private Integer shield;
    /**
     * String isQuJianZhi（为 1 表明为区间属性）
     */
    private String isQuJianZhi;

    /** 属性组id */
    private Integer comGroupId;

    /** 语言与属性组名称翻译map */
    private Map<String, String> langComGroupNameMap;

    /** 属性级别  0:product  1:sku  2:product和sku都能用，默认0 */
    private Integer level;

    /** 属性类型  */
    private Integer attributeType;

    /** 属性值类型  */
    private Integer attributeInputType;

    /** 排序*/
    private Integer sort;

    /** 启用状态 */
    private Integer status;

    /** 键值对，扩展字段*/
    private String features;
    /**属性多语言*/
    private List<AttributeLangDTO> langList;
    /**属性国家*/
    private List<String> countryCodeList;
    /**属性值列表*/
    private List<AttributeValueDTO> attributeValueList;
    /** 是否必填 */
    private Integer required;
}
