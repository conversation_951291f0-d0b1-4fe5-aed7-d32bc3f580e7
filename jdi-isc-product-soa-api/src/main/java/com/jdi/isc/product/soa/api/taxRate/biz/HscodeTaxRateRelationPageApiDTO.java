package com.jdi.isc.product.soa.api.taxRate.biz;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 跨境税率关系分页查询对象
 * @Author: chengliwei
 * @Date: 2024/12/11 10:47
 **/

@Data
public class HscodeTaxRateRelationPageApiDTO implements Serializable {


    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Request extends BasePageReqDTO implements Serializable {
        /**
         * SKUID
         */
        private List<String> skuIdList;

        /**
         * 中国海关编码
         */
        private List<String> cnHsCodeList;

        /**
         * 他国海关编码
         */
        private List<String> otherHsCodeList;

        /**
         * 国家码
         */
        private String countryCode;
    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BaseReqDTO implements Serializable{
        /**
         * SKUID
         */
        private Long skuId;

        /**
         * 中国海关编码
         */
        private String cnHsCode;

        /**
         * 他国海关编码
         */
        private String otherHsCode;

        /**
         * 国家码
         */
        private String countryCode;

        /**
         * spu名称
         */
        private String spuName;
        /**
         * 出口征税率
         */
        private BigDecimal exportTaxRate;
        /**
         * 出口退税率
         */
        private BigDecimal exportRebateRate;
        /**
         * 海关控制条件
         */
        private String customsCondition;
        /**
         * 检验检疫类别
         */
        private String quarantineCat;
        /**
         * 越南海关编码
         */
        private String viHsCode;
        /**
         * 越南最惠国关税
         */
        private BigDecimal viMFNTaxRate;
        /**
         * 越南原产地优惠关税
         */
        private BigDecimal viOriginTaxRate;
        /**
         * 越南消费税
         */
        private BigDecimal viConsumptionTaxRate;
        /**
         * 越南增值税
         */
        private BigDecimal vatIncreaseTaxRate;
        /**
         * 越南反倾销税
         */
        private BigDecimal viAntiDumpingTaxRate;
        /**
         * 是否涉及越南进口限制
         */
        private String viImportRestrict;
        /**
         * 是否进入越南保税仓
         */
        private String viInBondWarehouse;
        /**
         * 越南进口备注
         */
        private String viImportRemark;
        /**
         * 更新人
         */
        private String updater;
        /**
         * 更新时间
         */
        private Long updateTime;

    }

}