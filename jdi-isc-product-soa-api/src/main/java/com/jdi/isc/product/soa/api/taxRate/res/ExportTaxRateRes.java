package com.jdi.isc.product.soa.api.taxRate.res;

import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 中国出口税率信息 DTO实体类
 * @Author: zhaojianguo21
 * @Date: 2024/11/25 20:58
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ExportTaxRateRes extends BasicDTO {

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 海关编码
     */
    private String hsCode;

    /**
     * 出口征税率
     */
    private BigDecimal exportTaxRate;

    /**
     * 出口退税率
     */
    private BigDecimal exportRebateRate;

    /**
     * 海关控制条件
     */
    private String customsCondition;

    /**
     * 检验检疫类别
     */
    private String quarantineCat;
    /**
     * 国家名称
     */
    private String countryName;

}
