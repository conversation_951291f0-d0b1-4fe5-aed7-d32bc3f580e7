
package com.jdi.isc.product.soa.api.wimp.brand.req;

import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import com.jdi.isc.product.soa.api.validation.BrandValidateGroup;
import com.jdi.isc.product.soa.api.wimp.category.req.CategoryTreeApiDTO;
import com.jdi.isc.product.soa.api.wimp.xbp.XbpFlowApiDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 品牌视图对象
 * <AUTHOR>
 * @date 20231202
 */

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class BrandApiDTO extends BasicApiDTO {

    /** 品牌logo图片路径*/
    private String brandLogoPath;

    /** 排序*/
    private Integer sort;

    /** 状态 */
    @NotNull(message = "状态不能为空",groups = BrandValidateGroup.save.class)
    private Integer status;

    /** 键值对，扩展字段*/
    private String features;

    /** 品牌名称多语言*/
    @Valid
    @NotEmpty(message = "品牌名称不能为空",groups = BrandValidateGroup.save.class)
    private List<BrandLangApiDTO> langList;

    /**
     * 品牌分层类型
     */
    private String levelType;

    /**
     * 国内品牌id
     */
    private Long jdBrandId;

    /**
     * 类目集合
     */
    private List<CategoryTreeApiDTO> categoryTreeList;

    /**
     * 品牌国家
     */
    private BrandCountryApiDTO brandCountry;

    /**
     * 全类目生效
     */
    private boolean allCategoryScope;

    /**
     * 选择的类目节点
     */
    private List<CategoryTreeApiDTO> selectCategoryNodes;
    /**
     * 语言id
     */
    private String  lang;
    /** xbp审核详情 */
    private List<XbpFlowApiDTO> xbpFlows;
}