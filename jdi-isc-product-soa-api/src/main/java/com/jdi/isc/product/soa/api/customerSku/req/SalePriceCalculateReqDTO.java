package com.jdi.isc.product.soa.api.customerSku.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Map;

@Data
public class SalePriceCalculateReqDTO extends BaseReqDTO {

    /**
     * 客户编码
     */
    @NotNull(message = "客户编码必填")
    private String clientCode;

    /**
     * 末级类目ID
     */
    @NotNull(message = "skuID必填")
    private Long skuId;

    /**
     * 未税销售价
     */
    private BigDecimal salePrice;

    /**
     * 未税销售价
     */
    private BigDecimal grossRate;

    /**
     * 是否从数据库中取值
     */
    private Boolean dbFlag = Boolean.FALSE;

    /**
     * 各种率
     * */
    private Map<String,Object> salePriceTaxReq;
    /**
     * true客户销售价查询 false或空：非客户价格查询
     */
    private Boolean customerDirection;
}
