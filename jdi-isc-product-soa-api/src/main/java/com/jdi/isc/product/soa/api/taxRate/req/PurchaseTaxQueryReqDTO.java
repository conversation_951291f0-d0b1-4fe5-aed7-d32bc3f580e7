package com.jdi.isc.product.soa.api.taxRate.req;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @description：进项税查询入参
 * @Date 2024-12-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseTaxQueryReqDTO implements Serializable {

    private static final long serialVersionUID = -1175490090863956973L;
    /**SKU 数组**/
    @Size(max = 100,message = "SKU 个数不能超过100个")
    @NotEmpty(message = "SKU ID 不能为空")
    private Set<Long> skuIds;
}
