package com.jdi.isc.product.soa.api.certificate.res;

import com.jdi.isc.product.soa.api.common.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 跨境资质
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CertificateDTO extends BaseDTO {

    /**
     * 必填时机
     */
    private Integer requirement;

    /**
     * 资质id
     */
    private Long certificateId;

    /**
     * 资质文件路径
     */
    private String certificatePath;

    // 资质名称
    private String certificateName;

    // 资质说明
    private String certificateNote;

    // 资质样例
    private String qualificationSample;

    /**
     * 资质文件名
     */
    private String certificateFileName;

    /**
     * 是否长期
     */
    private Integer isLong;

    /**
     * 结束时间
     */
    private Long certEndTime;

    /**
     * 类型
     * */
    private String typeKey;

    /**
     * 资质类型名称集合
     */
    private List<String> classification1;

    /**
     * 资质类型名称集合
     */
    private List<String> classification2;
}