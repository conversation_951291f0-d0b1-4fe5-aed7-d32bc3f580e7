package com.jdi.isc.product.soa.api.wimp.product;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.validation.GlobalAttributeValidGroup;
import com.jdi.isc.product.soa.api.wimp.product.req.UpdateProductGlobalAttributeReqDTO;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @description：商品跨境属性写服务
 * @Date 2025-02-10
 */
public interface IscProductSoaProductGlobalAttributeWriteApiService {


    /**
     * 更新产品的全局属性草稿。
     * @param updateReqDTO 更新请求的数据传输对象，包含了要更新的全局属性信息。
     * @return 更新操作是否成功。
     */
    @Validated(GlobalAttributeValidGroup.updateProductGlobalAttributeDraft.class)
    DataResponse<Boolean> updateProductGlobalAttributeDraft(UpdateProductGlobalAttributeReqDTO updateReqDTO);
}
