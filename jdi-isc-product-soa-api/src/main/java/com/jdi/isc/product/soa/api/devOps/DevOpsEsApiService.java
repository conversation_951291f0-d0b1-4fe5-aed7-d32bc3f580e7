package com.jdi.isc.product.soa.api.devOps;

import com.jdi.common.domain.rpc.bean.DataResponse;

import java.util.Set;

/**
 * 研发使用接口
 * <AUTHOR>
 * @date 2025/06/17
 **/
public interface DevOpsEsApiService {

    /**
     * 初始化es中因国家池出现的标志
     * @param targetCountryCode 目标国家的国别代码
     * @return 操作结果
     */
    DataResponse<String> initPoolFlag(String env, String targetCountryCode, Set<Long> mkuIds);

    /**
     * 初始化es中因国家池出现的标志
     * @param targetCountryCode 目标国家的国别代码
     * @return 操作结果
     */
    DataResponse<String> initEsData(String env, String targetCountryCode,String clientCode, Set<Long> mkuIds);

    /**
     * 在指定环境和索引中删除文档。
     * @param env 环境名称
     * @param index 索引名称
     * @param id 文档ID
     * @return 删除操作是否成功
     */
    DataResponse<Boolean> deleteDocument(String env, String index, String id);


}
