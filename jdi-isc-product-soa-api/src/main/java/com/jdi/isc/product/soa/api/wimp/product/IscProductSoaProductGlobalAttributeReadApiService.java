package com.jdi.isc.product.soa.api.wimp.product;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.validation.GlobalAttributeValidGroup;
import com.jdi.isc.product.soa.api.wimp.product.req.GetProductGlobalAttributeReqDTO;
import com.jdi.isc.product.soa.api.wimp.product.res.ProductGlobalAttributeResDTO;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * <AUTHOR>
 * @description：商品跨境属性读服务
 * @Date 2025-02-10
 */
public interface IscProductSoaProductGlobalAttributeReadApiService {


    /**
     * 批量查询 SKU 草稿的全局属性映射关系。
     * @param reqDTO 请求参数，包含需要查询的 SKU 草稿列表。
     * @return 返回一个 Map，key 为 SKU 草稿 ID，value 为该 SKU 草稿的跨境属性列表。
     */
    @Validated(GlobalAttributeValidGroup.batchQuerySkuDraftGlobalAttributeMap.class)
    DataResponse<List<ProductGlobalAttributeResDTO>> batchQuerySkuDraftGlobalAttributeMap(GetProductGlobalAttributeReqDTO reqDTO);
}
