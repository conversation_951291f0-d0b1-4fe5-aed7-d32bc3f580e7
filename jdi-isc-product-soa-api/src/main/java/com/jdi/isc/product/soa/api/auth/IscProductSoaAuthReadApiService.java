package com.jdi.isc.product.soa.api.auth;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.auth.req.RoleListRequestDTO;
import com.jdi.isc.product.soa.api.auth.res.UserResDTO;

import java.util.List;

/**
 * 权限读服务
 * <AUTHOR>
 * @date 2025/06/21
 **/
public interface IscProductSoaAuthReadApiService {

    DataResponse<List<UserResDTO>> getUserByRoleList(RoleListRequestDTO reqDTO);
}
