package com.jdi.isc.product.soa.api.devOps;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.price.res.ProfitRateApiDTO;

import javax.xml.crypto.Data;
import java.util.List;
import java.util.Set;

/**
 * 工具类，处理类目数据
 * <AUTHOR>
 * @description：DevOpsCateApiService 处理切换类目ID方法
 * @Date 2025-06-09
 */
public interface DevOpsCateApiService {

    /**
     * 更新类目的京东类目ID
     * @param env 环境参数
     * @param catIds 类目ID集合
     * @return 更新结果
     */
    DataResponse<String> updateJdCategoryForCategory(String env, Set<Long> catIds);

    /**
     * 更新类目多语言京东类目信息
     * @param env 环境参数
     * @param catIds 类目ID集合
     * @return 更新结果
     */
    DataResponse<String> updateCatLangJdCategory(String env, Set<Long> catIds);

    /**
     * 更新SKU的京东类目信息
     * @param env 环境参数
     * @param skuIds SKU ID集合
     * @return 更新结果
     */
    DataResponse<String> updateSkuJdCategory(String env, Set<Long> skuIds);

    /**
     * 更新MKU的京东类目ID
     * @param env 环境参数
     * @param mkuIds MKU ID集合
     * @return 更新结果
     */
    DataResponse<String> updateMkuJdCategory(String env, Set<Long> mkuIds);

    /**
     * 更新SPU草稿的京东类目信息
     * @param env 环境参数
     * @param spuIds SPU ID集合
     * @return 更新结果
     */
    DataResponse<String> updateSpuDraftJdCategory(String env, Set<Long> spuIds);

    /**
     * 更新少量数据表四级类目的京东类目ID
     * @param env 环境参数
     * @param categoryIds 类目ID集合
     * @return 更新结果
     */
    DataResponse<String> updateJdCategoryForLowData(String env, Set<Long> categoryIds);

    /**
     * 更新国家池的京东类目ID
     * @param env 环境参数
     * @param mkuIds MKU ID集合
     * @return 更新结果
     */
    DataResponse<String> updateCountryMkuJdCategory(String env, Set<Long> mkuIds);

    /**
     * 更新客户池的京东类目ID
     * @param env 环境参数
     * @param mkuIds MKU ID集合
     * @return 更新结果
     */
    DataResponse<String> updateCustomerMkuJdCategory(String env, Set<Long> mkuIds);

    /**
     * 更新产品线的京东类目ID
     * @param env 环境参数
     * @param businessLineIds 业务线ID集合
     * @return 更新结果
     */
    DataResponse<String> updateBusinessLineJdCategory(String env, Set<Long> businessLineIds);

    /**
     * 更新国家协议加价率的京东类目ID
     * @param env 环境参数
     * @param catIds 类目ID集合
     * @return 更新结果
     */
    DataResponse<String> updateMakeUpJdCategory(String env, Set<Long> catIds);

    /**
     * 更新利润率阈值的京东类目ID
     * @param env 环境参数
     * @param catIds 类目ID集合
     * @return 更新结果
     */
    DataResponse<String> updateProfitRatePOJdCategory(String env, Set<Long> catIds);

    /**
     * 更新国家协议价草稿的京东类目ID
     * @param env 环境参数
     * @param lastCatIds 最后类目ID集合
     * @return 更新结果
     */
    DataResponse<String> updateCountryAgreementPriceDraftJdCategory(String env, Set<Long> lastCatIds);

    /**
     * 更新国家协议价的京东类目ID
     * @param env 环境参数
     * @param lastCatIds 最后类目ID集合
     * @return 更新结果
     */
    DataResponse<String> updateCountryAgreementPriceJdCategory(String env, Set<Long> lastCatIds);

    /**
     * 更新国家协议价预警的京东类目ID
     * @param env 环境参数
     * @param lastCatIds 最后类目ID集合
     * @return 更新结果
     */
    DataResponse<String> updateCountryAgreementPriceWarningPOJdCategory(String env, Set<Long> lastCatIds);

    /**
     * 更新国家扩展价的京东类目ID
     * @param env 环境参数
     * @param lastCatIds 最后类目ID集合
     * @return 更新结果
     */
    DataResponse<String> updateCountryExtendPriceJdCategory(String env, Set<Long> lastCatIds);


    /**
     * 根据指定的环境和级别更新京东分类信息。
     * @param env 环境标识，例如“dev”、“test”或“prod”。
     * @param level 分类级别，例如1、2或3。
     * @param catIds 需要更新的分类ID集合。
     * @return 更新操作的结果信息。
     */
    DataResponse<String> updateJdCategoryForCategoryByLevel(String env,Integer level, Set<Long> catIds);

    /**
     * 在指定环境中更新四级分类对应的京东三级分类。
     * @param env 环境标识，例如"dev"、"test"或"prod"。
     * @param catIds 需要更新的四级分类ID集合。
     * @return 更新操作的结果。
     */
    DataResponse<String> updateThirdCategoryJdCategoryForFourCategory(String env,Set<Long> catIds);

    /**
     * 在指定环境中更新多个 MKU 的 ES JD 类别
     * @param env 环境标识
     * @param mkuIds 需要更新的 MKU ID 集合
     * @return 更新结果
     */
    DataResponse<String>  updateMkuEsJdCategory(String  env,Set<Long> mkuIds);

    /**
     * 根据 ID 列表更新利润率。
     * @param list ProfitRateApiDTO 对象列表，包含要更新的利润率信息。
     * @return 更新操作的结果。
     */
    DataResponse<String> updateProfitRateById(List<ProfitRateApiDTO> list);


    /**
     * 更新指定环境下多个类目属性的POJO对象。
     * @param env 环境标识，用于区分不同的部署环境。
     * @param categoryIds 需要更新的类目ID集合。
     * @return 包含更新结果的DataResponse对象。
     */
    DataResponse<String> updateCategoryAttributePOJdCategory(String env,Set<Long> categoryIds);

    /**
     * 更新客户 SKU 价格草稿的京东分类信息。
     * @param env 环境标识，用于区分不同的运行环境。
     * @param categoryIds 需要更新的京东分类 ID 集合。
     * @return 更新操作的结果，包含是否成功和相应的错误信息。
     */
    DataResponse<String> updateCustomerSkuPriceDraftPOJdCategory(String env,Set<Long> categoryIds);

    /**
     * 根据指定的分类ID集合更新叶子节点的环境信息。
     * @param env 环境信息
     * @param leaf 叶子节点标识
     * @param catIds 分类ID集合
     * @return 更新结果
     */
    DataResponse<String> updateCategoryLeafByCatIds(String env,Integer leaf,Set<Long> catIds);

    /**
     * 根据提供的环境和yn值，更新指定的商品类别的yn属性。
     * @param env 环境信息
     * @param yn yn属性的新值
     * @param catIds 需要更新的商品类别的ID集合
     * @return 更新操作的结果
     */
    DataResponse<String> updateCategoryYnByCatIds(String env,Integer yn,Set<Long> catIds);

    /**
     * 根据给定的环境、起始位置和类目ID集合，更新类目定义的JD类目ID。
     * @param env 环境信息，用于区分不同的部署环境。
     * @param start 起始位置，用于分页或批量处理。
     * @param catIds 类目ID集合，需要更新的类目ID列表。
     * @return 更新操作的结果，包含成功或失败的状态信息。
     */
    DataResponse<String> updateCategoryDefinitionJdCatIdByCatIds(String env,Long start,Set<Long> catIds);

    /**
     * 根据提供的类目ID集合，更新这些类目的父类目ID。
     * @param env 环境信息，用于区分不同的部署环境。
     * @param catIds 要更新的类目ID集合。
     * @return 更新操作的结果，包含成功或失败的状态信息以及可能的错误消息。
     */
    DataResponse<String> updateCategoryDefinitionJdParentCatIdByCatIds(String env,Set<Long> catIds);


    /**
     * 在指定环境中更新默认销售属性的类别。
     * @param env 环境标识
     * @param catIds 需要更新的类别ID集合
     * @return 更新操作的结果
     */
    DataResponse<String> updateCategoryDefaultSaleAttribute(String env,Set<Long> catIds);

    /**
     * 从指定环境中删除重复的业务线。
     * @param env 环境名称，用于标识操作的目标环境。
     * @param businessLineIds 需要删除的业务线ID集合。
     * @return 删除操作的结果，包含成功或失败的信息。
     */
    DataResponse<String> removeRepeatBusinessLine(String env,Set<Long> businessLineIds);

    /**
     * 从GMS更新指定环境下的商品分类名称。
     * @param env 环境标识
     * @param catIds 需要更新的商品分类ID集合
     * @return 更新结果
     */
    DataResponse<String> updateCategoryNameFromGms(String env, Set<Long> catIds);


    /**
     * 从GMS更新子类别信息。
     * @param env 环境标识。
     * @param catIds 需要更新的子类别ID集合。
     * @return 更新结果。
     */
    DataResponse<String> updateSonCategoryFromGms(String env,Set<Long> catIds);

    /**
     * 在指定环境中执行Jade测试并返回结果。
     * @param env 测试执行的环境名称。
     * @return 测试执行的结果。
     */
    DataResponse<String> jadeTestExecutor(String env);

    /**
     * 更新京东SKU草稿ID
     * @param env 环境标识
     * @param skuIds SKU ID集合
     * @return 包含字符串的响应对象
     */
    DataResponse<String> updateSkuDraftJdSkuId(String env,Set<Long> skuIds);
}
