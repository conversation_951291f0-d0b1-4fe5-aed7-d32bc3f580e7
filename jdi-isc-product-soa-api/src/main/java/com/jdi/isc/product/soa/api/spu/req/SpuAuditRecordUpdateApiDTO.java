package com.jdi.isc.product.soa.api.spu.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/6
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SpuAuditRecordUpdateApiDTO extends BaseReqDTO {
    /**
     * SPUID
     */
    private List<Long> spuIds;
    /**
     * 审核人
     */
    private String auditErp;

    /**
     * 审核状态 1通过，2驳回
     *
     */
    private Integer status;
    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 国家类型，用于区分商品所属国家或地区。
     */
    private Integer countryType;


    /**
     * 税务状态，表示商品的税务处理情况。
     */
    private Integer taxStatus;

}
