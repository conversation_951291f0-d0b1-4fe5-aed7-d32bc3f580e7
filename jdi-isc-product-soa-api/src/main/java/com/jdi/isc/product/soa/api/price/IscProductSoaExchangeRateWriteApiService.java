package com.jdi.isc.product.soa.api.price;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.price.req.ExchangeRateChangeDTO;
import com.jdi.isc.product.soa.api.price.res.ExchangeRateApiDTO;

import java.util.List;

/**
 * @Description: 汇率表api服务
 * @Author: zhaokun51
 * @Date: 2025/03/04 15:30
 **/

public interface IscProductSoaExchangeRateWriteApiService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<Boolean> saveOrUpdate(ExchangeRateApiDTO input);

    /**
     * 根据汇率变化更新价格信息。
     * @param inputs 汇率变化的详细信息。
     * @return 更新结果，true表示成功，false表示失败。
     */
    DataResponse<Boolean> rateChangeToPriceRefresh(List<ExchangeRateChangeDTO> inputs);

    /**
     * 重新加载缓存。
     * @return 重新加载缓存的结果，true 表示成功，false 表示失败。
     */
    DataResponse<Boolean> reloadCache();
}
