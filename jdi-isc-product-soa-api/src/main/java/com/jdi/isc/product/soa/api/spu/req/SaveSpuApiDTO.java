package com.jdi.isc.product.soa.api.spu.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.api.sku.req.SaveSkuApiDTO;
import com.jdi.isc.product.soa.api.spu.res.GroupPropertyDTO;
import com.jdi.isc.product.soa.api.spu.res.SpuApiDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SaveSpuApiDTO extends BaseReqDTO {
    /**
     * spu对象
     */
    private SpuApiDTO spuVO;
    /**
     * 扩展属性信息，key->属性ID value->属性值列表
     */
    //@NotNull(message = "商品扩展属性不能为空")
    private List<PropertyApiDTO> storeExtendPropertyList;
    /**
     * spu资质列表
     */
    private List<SpuCertificateApiDTO> spuCertificateVOList;
    /**
     * sku列表
     */
    private List<SaveSkuApiDTO> skuVOList;
    /**
     * PC商品详描 key->语言 value->详描信息
     */
    //@NotNull(message = "商品详情不能为空")
    private Map<String, String> pcDescriptionMap;
    /**
     * APP商品详描 key->语言 value->详描信息
     */
    private Map<String, String> appDescriptionMap;
    /**
     * 国际扩展属性信息，key->属性ID value->属性值列表
     */
    private List<GroupPropertyDTO> spuInterPropertyList;

    /**
     * 审核状态标识，true表示不参与审核，false表示参与审核。
     */
    private boolean notAudit;

    /**
     * 组织ID，用于标识商品所属的组织。
     */
    private String orgId;

    /**
     * 标签关键字ID，用于标识商品的特定属性或分类。
     */
    private Long tagKeyId;
}
