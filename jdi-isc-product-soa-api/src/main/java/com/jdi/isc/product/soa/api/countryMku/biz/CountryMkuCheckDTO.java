package com.jdi.isc.product.soa.api.countryMku.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class CountryMkuCheckDTO {

    /**
     * 包含在客户池中的MKU列表。
     */
    List<Long> inPoolIdList;

    /**
     * 不在客户池中的MKU列表。
     */
    List<Long> notPoolIdList;

    /**
     * 用于存储检查结果的提示信息。
     */
    private String notice;
}
