package com.jdi.isc.product.soa.api.subtitle.biz;

import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode
@AllArgsConstructor
public class SubtitlePageResDTO extends BasicApiDTO {
    /**
     * MKUID
     */
    private Long mkuId;

    /**
     * 采购员
     */
    private String buyer;

    /**
     * 一级类目id
     */
    private Long firstCatId;

    /**
     * 二级类目id
     */
    private Long secondCatId;

    /**
     * 三级类目id
     */
    private Long thirdCatId;

    /**
     * 末级类目id
     */
    private Long lastCatId;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 目标国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * 类目名称 path
     */
    private String catName;

    /**
     * mkuIds
     * */
    private List<Long> mkuIds;

    /**
     * spuIds
     * */
    private List<Long> spuIds;

    /**
     * skuIds
     * */
    private List<Long> skuIds;

    /**
     * 页签key
     * */
    private String typeKey;

    /**
     * 中文MKU标题。
     */
    private String zhMkuTitle;

    /**
     * 葡萄牙语（巴西）MKU标题。
     */
    private String ptBrMkuTitle;

    /**
     * MKU的简短标题。
     */
    private String mkuSubtitle;

    /**
     * MKU的简短标题的长度。
     */
    private Integer subtitleLength;

    /**
     * 标题语言
     */
    private String titleLang;

    /**
     * MKU短标题的来源类型
     * 枚举值：1：系统生成（大模型调用)  2：人工校验修改
     */
    private Integer subtitleType;
}
