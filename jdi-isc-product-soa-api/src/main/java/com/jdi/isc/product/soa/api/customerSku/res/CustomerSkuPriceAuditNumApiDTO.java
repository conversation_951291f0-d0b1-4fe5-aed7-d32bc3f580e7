package com.jdi.isc.product.soa.api.customerSku.res;

import lombok.Builder;
import lombok.Data;

import java.util.Set;

/**
 * @Description:
 * @Author: zhaokun51
 * @Date: 2024/10/21 10:42
 */
@Data
@Builder
public class CustomerSkuPriceAuditNumApiDTO {

    /**
     * 页面唯一键
     */
    private String typeKey;
    /**
     * 来源国家
     */
    private String sourceCountryCode;
    /**
     * 目标国家
     */
    private Set<String> targetCountryCodes;
    /**
     * 审核状态
     */
    private Integer auditStatus;
    /**
     * 层级
     */
    private Integer level;
    /**
     * 数量
     */
    private Long num;
}
