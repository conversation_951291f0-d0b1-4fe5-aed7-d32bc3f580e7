package com.jdi.isc.product.soa.api.sku.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SkuGlobalAttributeApiDTO extends BaseReqDTO {
    /**
     * 国家
     */
    private String targetCountryCode;

    /**
     * SKU全局属性详细信息列表
     */
    private List<SkuGlobalAttributeDetailApiDTO> detailData;
}
