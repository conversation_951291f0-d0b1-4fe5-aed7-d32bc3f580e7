package com.jdi.isc.product.soa.api.wiop.bonded;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.wiop.bonded.req.WiopBondedAuditSaveReqDTO;

/**
 * 保税审核商品服务
 * <AUTHOR>
 * @date 2024/7/16
 **/
public interface WiopBondedAuditApiService {


    /**
     * 添加保税审核记录
     * @param reqDTO WiopBondedAuditSaveReqDTO对象，表示要保存的保税审核记录信息
     * @return DataResponse<Long> 返回保存的保税审核记录的ID
     */
    DataResponse<Long> saveBondedAuditRecord(WiopBondedAuditSaveReqDTO reqDTO);
}
