package com.jdi.isc.product.soa.api.mku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.mku.req.*;
import com.jdi.isc.product.soa.api.mku.res.IscMkuAvailableSaleResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuLangsResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuResDTO;
import com.jdi.isc.product.soa.api.spu.res.SpuApiDTO;
import com.jdi.isc.product.soa.api.validation.MkuQueryValidateGroup;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientPageReqApiDTO;
import org.springframework.validation.annotation.Validated;

import java.util.Map;
import java.util.Set;

/**
 * 国家主数据写服务
 * <AUTHOR>
 * @date 2024/07/10
 */
public interface IscProductSoaMkuReadApiService {


    /**
     * 根据 SKU ID 查询商品信息。
     * @param input 查询请求对象，包含 SKU ID 等信息。
     * @return 包含查询结果的 DataResponse 对象，结果类型为 Long。
     */
    DataResponse<Long> queryBySkuId(QueryMkuReqDTO input);

    /**
     * 商品列表 颗粒度到spu,mku->sku->spu
     * @param input 列表参数f
     * @return 商品列表、包含全部信息es中全部信息
     */
    DataResponse<PageInfo<SpuApiDTO>> queryPageForSpu(MkuClientPageReqApiDTO input);

    /**
     * 国际MKU批量查询
     */
    @Validated(MkuQueryValidateGroup.getIscMku.class)
    DataResponse<Map<Long,IscMkuResDTO>> getIscMku(BatchQueryMkuReqDTO req);

    /**
     * 根据jdSku查询MkuId
     */
    @Validated(MkuQueryValidateGroup.getIscMkuIdByJdSkuId.class)
    DataResponse<Map<Long, Set<Long>>> getIscMkuIdByJdSkuId(BatchQueryMkuReqDTO req);

    /**
     * 根据 MKU IDs 查询特殊属性映射。
     * @param input SpecialAttrSkuReq 对象，包含要查询的 MKU IDs。
     * @return DataResponse 对象，包含一个 Map，键为 MKU ID，值为特殊属性KEY和属性值的映射。
     */
    DataResponse<Map<Long,Map<String,String>>> querySpecialAttrMapByMkuIds(SpecialAttrMkuReqDTO input);

    /**
     * 根据客户和 MKU IDs 查询特殊属性映射。
     * @param input SpecialAttrMkuClientReqDTO 对象，包含要查询的 MKU IDs和客户编码。
     * @return DataResponse 对象，包含一个 Map，键为 MKU ID，值为特殊属性KEY和属性值的映射。
     */
    DataResponse<Map<Long,Map<String,String>>> querySpecialAttrMapByClientCode(SpecialAttrMkuClientReqDTO input);

    /**
     * 根据sku集合查询mkuId
     * @param req skuId集合
     * @return sku和mku的关系
     */
    DataResponse<Map<Long, Long>> getIscMkuIdBySkuId(BatchQueryMkuReqDTO req);

    /**
     * 查询MKU可购买信息
     * @param req IscMkuAvailableSaleReq对象，包含查询条件
     * @return DataResponse对象，封装了查询结果的Map，key为MKU ID，value为IscMkuAvailableSaleResDTO对象，表示该MKU的可购买信息
     */
    DataResponse<Map<Long, IscMkuAvailableSaleResDTO>> queryMkuAvailable(IscMkuAvailableSaleReq req);

    /**
     * 批量查询mku的多语言信息
     * @Description: 涉及多语言的信息，都以多语言的形式返回
     * @param req
     * @return
     */
    DataResponse<Map<Long, IscMkuLangsResDTO>> getIscMkuLangs(BatchQueryMkuLangsReqDTO req);

    /**
     * 根据mkuId查找skuId,如果传入clientId则查找clientId&MKUID下的固定skuId,如果未传入clientId则查找MKU绑定的SKU(出现一对多直接报错)
     * @param req clientCode&mkuId
     * @return skuId
     */
    DataResponse<Map<Long, Long>> getIscSkuIdByMkuId(BatchQueryMkuReqDTO req);

    /**
     * 根据MKU ID批量查询对应的JD SKU ID。
     * @param req 批量查询MKU ID的请求对象。
     * @return 包含MKU ID和对应JD SKU ID的映射关系。
     */
    @Validated(MkuQueryValidateGroup.getIscJdSkuIdByMkuId.class)
    DataResponse<Map<Long, Long>> getIscJdSkuIdByMkuId(BatchQueryMkuReqDTO req);

    /**
     * 根据MkuId批量查询iscSpuSku关联关系。
     * @param req 包含MkuId的请求对象。
     * @return 返回一个包含MkuId和对应iscSpuSku关联关系的Map。
     * true 是一对多关系，false 是一对一关系
     */
    @Validated(MkuQueryValidateGroup.getIscSpuSkuRelationByMkuId.class)
    DataResponse<Map<Long, Boolean>> getIscSpuSkuRelationByMkuId(BatchQueryMkuReqDTO req);

}
