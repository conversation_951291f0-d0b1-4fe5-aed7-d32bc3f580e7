package com.jdi.isc.product.soa.api.customerSku.res;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.api.common.PriceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description: sku客制化价格-草稿表 VO实体类
 * @Author: zhaokun51
 * @Date: 2024/10/21 13:30
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSkuPriceDraftExportApiDTO extends BaseReqDTO {

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 来源国家
     * */
    private String sourceCountryCode;

    /**
     * 客户简码
     */
    private String clientCode;

    /**
     * 客户名称
     */
    private String clientName;

    /**
     * 目标销售国家（客户所在国家）
     */
    private String targetCountryCode;

    /**
     * 供应商简码
     */
    private String vendorCode;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 对客交货模式 EXW,DDP
     */
    private String customerTradeType;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 客制销售价未税
     */
    private BigDecimal customerSalePrice;

    /**
     * 客制销售价含税
     */
    private BigDecimal taxCustomerSalePrice;

    /**
     * 审核状态，1:审核通过，2:驳回 3:待审核
     */
    private Integer auditStatus;

    /** 商品名称*/
    private String skuName;

    /**
     * 开始时间
     */
    private Long beginTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /** 自增ID*/
    protected Long id;

    /** 备注*/
    private String remark;

    /** 创建者*/
    protected String creator;

    /** 修改人*/
    protected String updater;

    /** 创建时间*/
    protected Long createTime;

    /** 最后修改时间*/
    protected Long updateTime;

    /** 0失效1有效2过期3未生效 */
    private Integer enableStatus;

    /**
     * 国家成本价
     */
    private BigDecimal countryCostPrice;

    /**
     * 国家协议价
     */
    private BigDecimal agreementPrice;

    /**
     * 京东价
     */
    private BigDecimal jdPrice;

    /**
     * 预估跨境退税额
     */
    private BigDecimal refundTaxPrice;

    /**
     * 国家跨境入仓价
     */
    private BigDecimal countryWarehousingPrice;

    /**
     * 利润率
     * */
    private BigDecimal profitRate;

    /**
     * 相关税率税额
     */
    private Map<String,Object> salePriceTaxRes;


}
