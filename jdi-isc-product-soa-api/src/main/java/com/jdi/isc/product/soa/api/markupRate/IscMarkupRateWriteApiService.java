package com.jdi.isc.product.soa.api.markupRate;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.markupRate.req.MarkupRateReqDTO;
import com.jdi.isc.product.soa.api.markupRate.res.MarkupRateDTO;

import java.util.List;

/**
 * @Description: 国家协议加价率api服务
 * @Author: wangpeng965
 * @Date: 2025/02/28 10:31
 **/

public interface IscMarkupRateWriteApiService {

    /**
     * 保存、更新
     * @param dto 提交参数
     * @return 结果
     */
    DataResponse<Boolean> saveOrUpdate(MarkupRateReqDTO dto);

    /**
     * 批量保存或更新标记率信息。
     * @param markupRateDTOList 标记率信息列表。
     * @return 批量保存或更新的结果。
     */
    DataResponse<List<MarkupRateDTO>> batchSaveOrUpdate(List<MarkupRateDTO> markupRateDTOList);
}
