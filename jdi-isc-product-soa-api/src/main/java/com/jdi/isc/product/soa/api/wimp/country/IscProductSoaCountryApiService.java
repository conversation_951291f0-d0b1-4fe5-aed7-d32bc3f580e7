package com.jdi.isc.product.soa.api.wimp.country;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.wimp.country.res.CountryResDTO;

import java.util.List;
import java.util.Map;

/**
 * 国家主数据写服务
 * <AUTHOR>
 * @date 2024/07/10
 */
public interface IscProductSoaCountryApiService {

    /**
     * 查询列表
     */
    @Deprecated
    DataResponse<List<CountryResDTO>> queryList();

    /**
     * 查询列表
     */
    DataResponse<List<CountryResDTO>> queryListByLang(String lang);

    /**
     * 重新加载缓存中配置
     * @return 返回重新加载是否成功
     */
    DataResponse<Boolean> reloadCache();

    /**
     * 获取国家列表
     * @return 按降序排列的国家列表，如果缓存数据为空则返回 null
     */
    DataResponse<List<String>> getCountryCodeList();

    /**
     * 根据国家代码获取国家名称
     * @param countryCode 国家代码
     * @return 国家名称，如果找不到返回 null
     */
    @Deprecated
    DataResponse<String> getCountryNameByCountryCode(String countryCode);

    /**
     * 根据国家代码和语言获取国家名称
     * @param countryCode 国家代码
     * @param lang 语言代码
     * @return 包含国家名称的DataResponse对象
     */
    DataResponse<String> getCountryNameByCountryCodeAndLang(String countryCode,String lang);

    /**
     * 根据国家代码获取语言
     * @param countryCode 国家代码
     * @return 语言代码，如果找不到对应的语言代码则返回 null
     */
    DataResponse<String> getLangByCountryCode(String countryCode);

    /**
     * 根据国家代码获取货币代码
     * @param countryCode 国家代码
     * @return 货币代码，如果未找到则返回 null
     */
    DataResponse<String> getCurrencyByCountryCode(String countryCode);

    /**
     * 获取国家货币映射表
     * @return 包含国家货币映射信息的 DataResponse 对象
     */
    DataResponse<Map<String,String>> getCountryCurrencyMap();

    /**
     * 根据国家获取贸易类型
     * @param countryCode 国家代码
     * @return 相应国家的贸易类型，如果国家不在列表中或没有匹配的贸易类型，返回空字符串
     */
    DataResponse<String> getTradeTypeByCountry(String countryCode);

    /**
     * 获取国家映射数据
     * @return 包含国家信息的DataResponse对象，其中键为国家代码，值为国家名称
     */
    @Deprecated
    DataResponse<Map<String,String>> getCountryMap();

    /**
     * 获取国家映射信息
     * @param lang 语言代码，用于指定返回的国家名称语言
     * @return 包含国家映射信息的DataResponse对象，其中键为国家代码，值为国家名称
     */
    DataResponse<Map<String,String>> getCountryMapByLang(String lang);
}
