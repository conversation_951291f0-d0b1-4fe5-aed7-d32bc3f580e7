package com.jdi.isc.product.soa.api.message.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * 供应商开放消息请求实体
 * <AUTHOR>
 * @date 20231221
 */
@Data
public class SupplierMsgReadReq {

    /** 消息类型 {@link SupplierMsgTypeEnum} */
    @Size(max = 10, message = "illegal message type quantity",groups = {ValidatorGroup.msgConsume.class})
    private Set<Integer> type;

    /** 供应商简码*/
    @NotNull(message = "supplierCode can not be null",groups = {ValidatorGroup.msgConsume.class,ValidatorGroup.msgRemove.class})
    private String supplierCode;

    /** 消息id*/
    @NotEmpty(message = "id can not be null",groups = {ValidatorGroup.msgRemove.class})
    @Size(min=1, max = 500, message = "illegal id quantity",groups = {ValidatorGroup.msgRemove.class})
    private Set<Long> id;


}
