package com.jdi.isc.product.soa.api.taxRate.req;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description：查询中国税率信息入参
 * @Date 2024-11-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetExportTaxRateDTO {
    /** 主键 */
    private Long id;
    /** 国家编码 */
    private String countryCode;
    /** 海关编码*/
    private String hsCode;
}
