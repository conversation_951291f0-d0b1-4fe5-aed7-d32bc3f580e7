package com.jdi.isc.product.soa.api.stock;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockResDTO;
import com.jdi.isc.product.soa.api.validation.StockValidGroup;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * 库存读微服务
 * <AUTHOR>
 * @date 2024/6/15
 */
public interface IscProductSoaStockReadApiService {

    /** 库存查询*/
    @Validated(StockValidGroup.query.class)
    DataResponse<Map<Long, StockResDTO>> getStock(StockManageReqDTO req);

}
