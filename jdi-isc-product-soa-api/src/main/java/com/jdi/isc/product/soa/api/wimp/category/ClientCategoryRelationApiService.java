package com.jdi.isc.product.soa.api.wimp.category;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.wimp.category.req.ClientCategoryRelationDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.ClientCategoryRelationGetReqDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.ClientCategoryRelationQueryReqDTO;

import java.util.Map;

/**
 * 客户类目关系API服务
 * <AUTHOR>
 * @date 2024/10/21
 **/
public interface ClientCategoryRelationApiService {

    /**
     * 保存或更新客户类别关联信息。
     * @param relationDTO 客户类别关联信息DTO对象。
     * @return 保存或更新操作结果，true表示成功，false表示失败。
     */
    DataResponse<Boolean> saveOrUpdate(ClientCategoryRelationDTO relationDTO);


    /**
     * 根据查询条件分页获取客户类别关联信息。
     * @param reqDTO 查询条件，包含页码、每页记录数等信息。
     * @return 分页结果，包含当前页数据和分页信息。
     */
    DataResponse<PageInfo<ClientCategoryRelationDTO>> page(ClientCategoryRelationQueryReqDTO reqDTO);


    /**
     * 获取客户类别关联关系的Map
     * @param getReqDTO 客户类别关联关系查询请求DTO
     * @return 包含客户类别关联关系的Map，键为类别ID，值为关联的国际类目ID
     */
    DataResponse<Map<String, Long>> getClientCategoryRelationMap(ClientCategoryRelationGetReqDTO getReqDTO);
}
