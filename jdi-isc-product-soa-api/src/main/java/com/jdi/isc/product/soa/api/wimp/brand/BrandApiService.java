package com.jdi.isc.product.soa.api.wimp.brand;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.supplier.res.BrandResDTO;
import com.jdi.isc.product.soa.api.supplier.res.BrandSimpleResDTO;
import com.jdi.isc.product.soa.api.validation.BrandValidateGroup;
import com.jdi.isc.product.soa.api.wimp.brand.req.BrandApiDTO;
import com.jdi.isc.product.soa.api.wimp.brand.req.BrandForProductReqApiDTO;
import com.jdi.isc.product.soa.api.wimp.brand.req.BrandPageApi;
import com.jdi.isc.product.soa.api.wimp.brand.req.BrandReqApiDTO;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 品牌类目服务
 */
public interface BrandApiService {

    /**
     * 保存或更新品牌信息
     */
    @Validated(BrandValidateGroup.save.class)
    DataResponse<Boolean> saveOrUpdateBrandInfo(BrandApiDTO brandApiDTO);

    /**
     * @function 获取品牌详情信息
     * @param brandApiDTO 品牌信息DTO对象
     * @returns DataResponse<BrandApiDTO> 包含品牌详情信息的响应对象
     */
    DataResponse<BrandApiDTO> detail(BrandApiDTO brandApiDTO);

    /**
     * 分页查询
     */
    DataResponse<PageInfo<BrandPageApi.Response>> pageSearch(BrandPageApi.Request input);

    /**
     * 校验语言名重复
     */
    DataResponse<Boolean> checkLangName(BrandPageApi.Request input);


    /**
     * 列出品牌列表
     * @param input 品牌请求参数
     * @return 包含 BrandResDTO 列表的 DataResponse 对象
     */
    @Validated(BrandValidateGroup.get.class)
    DataResponse<List<BrandResDTO>> queryList(BrandReqApiDTO input);

    /**
     * 根据供应商代码获取品牌信息
     * @param input BrandForProductReqApiDTO 对象，包含查询条件
     * @return DataResponse<PageInfo<BrandResDTO>> 响应数据，包含品牌信息列表
     */
    DataResponse<PageInfo<BrandSimpleResDTO>> getBrandInfoForSupplierCode(BrandForProductReqApiDTO input);
}
