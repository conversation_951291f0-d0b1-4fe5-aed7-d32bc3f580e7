package com.jdi.isc.product.soa.api.attribute.common;

import com.jdi.isc.product.soa.api.common.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 属性多语言
 * <AUTHOR>
 * @date 2024/9/1
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AttributeLangDTO extends BaseDTO {
        /** 属性id*/
    private Long attributeId;

    /** 属性类型*/
    private Integer attributeType;
        /** 语种 */
    private String lang;
    /** 名称*/
    private String langName;
}
