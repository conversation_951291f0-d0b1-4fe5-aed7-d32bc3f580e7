package com.jdi.isc.product.soa.api.customerSku.res;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 客户sku
 * <AUTHOR>
 * @date 2024/10/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CustomerSkuEmailDTO extends BasePageReqDTO {

    /** id */
    private String erp;

    /** 名字 */
    private String erpName;

    /** 邮箱 */
    private String email;

    /** 国家名称 */
    private String countryName;

}


