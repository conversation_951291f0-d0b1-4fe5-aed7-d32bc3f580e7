package com.jdi.isc.product.soa.api.mku.req;


import com.jdi.isc.product.soa.api.common.enums.MkuSpecialAttrEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * <AUTHOR>
 * @description：MKU特殊属性请求参数
 * @Date 2024-12-25
 */
@Data
public class SpecialAttrMkuReqDTO {
    @NotEmpty(message = "mkuIds不能为空")
    @Size(max = 20,message = "mkuIds最大长度为20")
    private Set<Long> mkuIds;
    /**国家编码**/
    private String countryCode;
    /**
     * 特殊属性枚举
     */
    private Set<MkuSpecialAttrEnum> specialAttrEnums;
}
