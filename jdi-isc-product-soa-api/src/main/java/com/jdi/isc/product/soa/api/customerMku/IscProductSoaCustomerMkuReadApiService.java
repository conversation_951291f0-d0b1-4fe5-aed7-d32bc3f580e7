package com.jdi.isc.product.soa.api.customerMku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.customerMku.req.CustomerMkuPricePageReqDTO;
import com.jdi.isc.product.soa.api.customerMku.req.CustomerMkuQueryDTO;
import com.jdi.isc.product.soa.api.customerMku.req.CustomerMkuReqDTO;
import com.jdi.isc.product.soa.api.customerMku.res.CustomerMkuDTO;
import com.jdi.isc.product.soa.api.customerMku.res.CustomerMkuPriceResDTO;
import com.jdi.isc.product.soa.api.spu.res.SpuAmendApiDTO;

import java.util.List;

/**
 * spu写服务
 * <AUTHOR>
 * @date 2024/08/05
 */
public interface IscProductSoaCustomerMkuReadApiService {


    DataResponse<PageInfo<CustomerMkuPriceResDTO>> pricePage(CustomerMkuPricePageReqDTO input);


    DataResponse<List<SpuAmendApiDTO>> getDetailListByMkuIds(CustomerMkuReqDTO customerMkuReqDTO);


    DataResponse<PageInfo<CustomerMkuDTO>> customerMkuPage(CustomerMkuQueryDTO input);

    DataResponse<List<CustomerMkuDTO>> listCustomerMku(CustomerMkuReqDTO dto);

}
