package com.jdi.isc.product.soa.api.mku.req;

import com.jd.tp.common.masterdata.UniformBizInfo;
import com.jdi.isc.product.soa.api.common.enums.MkuQueryEnum;
import com.jdi.isc.product.soa.api.validation.MkuQueryValidateGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * 国际MKU批量入参查询
 * <AUTHOR>
 * @date 2024/12/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchQueryMkuReqDTO   {

    /**
     * 国际化租户信息
     */
    @NotNull(message = "uniformBizInfo不能为空",groups = {MkuQueryValidateGroup.getIscMku.class})
    private UniformBizInfo uniformBizInfo;

    /**
     * 客户编码信息
     */
    @NotNull(message = "clientCode不能为空",groups = {MkuQueryValidateGroup.getIscMku.class
            ,MkuQueryValidateGroup.getIscJdSkuIdByMkuId.class,MkuQueryValidateGroup.getIscSpuSkuRelationByMkuId.class})
    private String clientCode;

    /**
     * 商品ID集合
     */
    @NotNull(message = "mkuId不能为空",groups = {MkuQueryValidateGroup.getIscMku.class,MkuQueryValidateGroup.getIscSkuIdByMkuId.class
            ,MkuQueryValidateGroup.getIscJdSkuIdByMkuId.class,MkuQueryValidateGroup.getIscSpuSkuRelationByMkuId.class})
    private Set<Long> mkuId;

    /**
     * mku查询入参枚举
     */
    @NotNull(message = "queryEnum不能为空",groups = {MkuQueryValidateGroup.getIscMku.class})
    private Set<MkuQueryEnum> queryEnum;

    /**
     * 国际JD_SKU_ID集合
     */
    @NotNull(message = "jdSkuId不能为空",groups = {MkuQueryValidateGroup.getIscMkuIdByJdSkuId.class})
    private Set<Long> jdSkuId;

    /**
     * 国际SKU_ID集合
     */
    @NotNull(message = "skuId不能为空",groups = {MkuQueryValidateGroup.getIscMkuIdBySkuId.class})
    private Set<Long> skuIds;

    /**
     * 语言集合，用于指定查询业务线信息时所需的语言。
     */
    @NotNull(message = "langSet不能为空",groups = {MkuQueryValidateGroup.getIscMku.class})
    private Set<String> langSet;

}
