package com.jdi.isc.product.soa.api.sku.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.api.common.enums.CalculatePriceEnums;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 计算含税采购价等信息入参
 *
 * <AUTHOR>
 * @date 2024/5/7
 **/
@Data
public class SkuCalculatePriceReqDTO extends BaseReqDTO {

    /**
     * 末级类目ID
     */
    private Long catId;
    /**
     * 末级类目ID
     */
    private Long skuId;
    /**
     * 货源国编码
     */
    private String sourceCountryCode;
    /**
     * 未税采购价
     */
    private BigDecimal purchasePrice;
    /**
     * 含税税采购价
     */
    private BigDecimal taxPurchasePrice;
    /**
     * 未税销售价
     */
    private BigDecimal salePrice;
    /**
     * 含税销售价
     */
    private BigDecimal taxSalePrice;
    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     * 类目税率
     * */
    private CategoryTaxDTO categoryTaxVO;
    /**
     * 是否获取sku价格、主要应用在是否需要获取sku级别价格，true是需要，false是不需要
     */
    private Boolean skuPriceFlag = Boolean.TRUE;
    /**
     * 跨境属性ID和税率映射
     */
    private Map<Long,String> taxRateMap;
    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * 计算价格的枚举类型，用于指定计算哪些价格。
     */
    private CalculatePriceEnums calculatePriceEnums;
}
