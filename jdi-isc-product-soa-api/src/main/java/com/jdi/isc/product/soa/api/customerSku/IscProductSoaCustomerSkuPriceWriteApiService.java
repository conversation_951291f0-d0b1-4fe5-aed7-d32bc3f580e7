package com.jdi.isc.product.soa.api.customerSku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.customerSku.req.CustomerSkuAuditApiDTO;
import com.jdi.isc.product.soa.api.customerSku.req.CustomerSkuPriceDraftIdApiDTO;
import com.jdi.isc.product.soa.api.customerSku.res.CustomerSkuPriceDraftApiDTO;
import com.jdi.isc.product.soa.api.price.req.PriceAvailableSaleStatusReqDTO;
import com.jdi.isc.product.soa.api.price.res.PriceAvailableSaleStatusResDTO;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Set;

/**
 * sku定制化价格
 * <AUTHOR>
 * @date 2024/11/01
 */
public interface IscProductSoaCustomerSkuPriceWriteApiService {


    /**
     * 保存、更新
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<String> saveOrUpdate(@Validated CustomerSkuPriceDraftApiDTO input);

    /**
     * 通过-批量
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<String> batchApprove(CustomerSkuAuditApiDTO input);

    /**
     * 驳回-批量
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<String> batchReject(CustomerSkuAuditApiDTO input);

    /**
     * 无效
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<String> updateEnableStatusById(CustomerSkuPriceDraftIdApiDTO input);

    /**
     * 删除
     * @param id 提交参数
     * @return 结果
     */
    DataResponse<Boolean> deleteById(Long id);

    /**
     * 撤回-批量
     * @param input 提交参数
     * @return 结果
     */
    DataResponse<String> batchRevoke(CustomerSkuAuditApiDTO input);

    /**
     * 批量启用审批的商品信息。
     * @param input 审核API的输入参数，包含待审批的商品信息。
     * @return 批量启用审批的结果。
     */
    DataResponse<String> batchEnableApprove(CustomerSkuAuditApiDTO input);

    /**
     * 批量启用或拒绝客户 SKU 审核请求。
     * @param input 客户 SKU 审核 API DTO 对象，包含要操作的 SKU 列表和操作类型（启用或拒绝）。
     * @return DataResponse 对象，包含操作结果的状态码和消息。
     */
    DataResponse<String> batchEnableReject(CustomerSkuAuditApiDTO input);

    /**
     * 批量启用或撤销客户 SKU 审核。
     * @param input 客户 SKU 审核 API DTO 对象，包含批量操作的必要信息。
     * @return DataResponse 对象，包含操作结果和相关数据。
     */
    DataResponse<String> batchEnableRevoke(CustomerSkuAuditApiDTO input);

    /**
     * 初始化客户端 SKU 警告价格。
     * @param ids 需要初始化警告价格的 SKU ID 集合。
     * @return 初始化操作的结果。
     */
    DataResponse<String> initCustomerSkuWarningPrice(Set<Long> ids);

    /**
     * 更新商品的可售状态
     * @param input 商品可售状态请求DTO列表，包含需要更新的商品状态信息
     * @return 包含更新后的商品可售状态响应DTO的数据响应对象
     */
    DataResponse<PriceAvailableSaleStatusResDTO> updateAvailableSaleStatus(List<PriceAvailableSaleStatusReqDTO> input);
}
