package com.jdi.isc.product.soa.api.mku.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * mku多语言表业务对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class MkuLangReqDTO extends BasicApiDTO {

    /**
     * MKUID
     */
    private Long mkuId;

    /**
     * mku标题
     */
    private String mkuTitle;

    /**
     * MKU的短标题
     */
    private String mkuSubtitle;

    /**
     * MKU短标题的长度
     */
    private Integer subtitleLength;

    /**
     * MKU短标题的来源类型
     */
    private Integer subtitleType;

}