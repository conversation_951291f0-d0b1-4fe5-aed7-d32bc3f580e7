package com.jdi.isc.product.soa.api.price.supplierPrice;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.AuditColumnConfigDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.req.SkuPriceAuditPageReqDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.req.SkuPricePageReqDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.req.SkuPriceQueryReqDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.req.SkuPurchasePriceReqDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.res.SkuPriceApiDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.res.SkuPriceAuditPageApiDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.res.SkuPricePageApiDTO;
import com.jdi.isc.product.soa.api.price.supplierPrice.res.SkuPurchasePriceApiDTO;

import java.util.List;

import java.util.List;

public interface IscSkuPriceReadApiService {

    /**
     * 分页查询 SKU 价格信息。
     * @param dto SkuPricePageReqDTO 对象，包含分页查询的参数。
     * @return DataResponse<PageInfo<SkuPricePageApiDTO>> 对象，包含分页后的 SKU 价格信息。
     */
    DataResponse<PageInfo<SkuPricePageApiDTO>> pageSearch(SkuPricePageReqDTO dto);

    /**
     * 获取国家协议价格详情
     * @param dto 国家协议价格请求参数
     * @return 国家协议价格响应数据
     */
    DataResponse<SkuPriceApiDTO> detail(SkuPriceQueryReqDTO dto);



    /**
     * 批量审批商品价格变更请求的分页搜索结果。
     * @param dto SkuPricePageReqDTO对象，包含分页搜索的条件和参数。
     * @return DataResponse对象，包含批量审批商品价格变更请求的分页搜索结果和分页信息。
     */
    DataResponse<PageInfo<SkuPriceAuditPageApiDTO>> approvePageSearch(SkuPriceAuditPageReqDTO dto);




    /**
     * 获取商品采购价格信息。
     * @param reqDTO 采购价格请求参数，包括商品ID、供应商ID等。
     * @return 包含商品采购价格的列表。
     */
    DataResponse<List<SkuPurchasePriceApiDTO>> getPurchasePrice(SkuPurchasePriceReqDTO reqDTO);




    /**
     * 获取动态列.
     *
     * @return the custom columns
     */
    DataResponse<List<AuditColumnConfigDTO>> getCustomColumns(String key);
}

