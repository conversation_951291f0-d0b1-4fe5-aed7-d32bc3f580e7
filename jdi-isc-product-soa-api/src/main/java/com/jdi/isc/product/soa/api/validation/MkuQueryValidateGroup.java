package com.jdi.isc.product.soa.api.validation;

/**
 * <AUTHOR>
 * @date 2024/9/1
 **/
public interface MkuQueryValidateGroup {


    /**
     *  getIscMku
     */
    public static interface getIscMku {}

    /**
     * getIscMkuIdByJdSkuId
     */
    public static interface getIscMkuIdByJdSkuId {}

    /**
     * getIscMkuIdBySkuId
     */
    public static interface getIscMkuIdBySkuId {}

    /**
     * getIscSkuIdByMkuId
     */
    public static interface getIscSkuIdByMkuId {}

    /**
     * getIscJdSkuIdByMkuId
     */
    public static interface getIscJdSkuIdByMkuId {}

    /**
     * getIscJdSkuIdByMkuId
     */
    public static interface getIscSpuSkuRelationByMkuId {}
}
