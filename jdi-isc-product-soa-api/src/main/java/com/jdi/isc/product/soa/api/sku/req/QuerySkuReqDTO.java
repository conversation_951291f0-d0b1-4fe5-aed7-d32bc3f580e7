package com.jdi.isc.product.soa.api.sku.req;

import com.jdi.isc.product.soa.api.common.enums.SkuQueryEnum;
import com.jdi.isc.product.soa.api.validation.SkuReadValidateGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * sku查询入参
 * <AUTHOR>
 * @date 2024/8/26
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuerySkuReqDTO {
    /**
     * SKU ID 集合
     */
    @NotEmpty(message = "skuIds can not empty" ,groups = {SkuReadValidateGroup.querySkuBaseInfo.class})
    @NotNull(message = "skuIds can not empty",groups = {SkuReadValidateGroup.querySkuBaseInfo.class})
    @Size(max = 100,message = "skuIds can not larger than 100")
    private Set<String> skuIds;

    /**
     * SPU ID
     */
    private String spuId;

    /**
     * 供应商 SKU ID 集合
     */
    private Set<String> vendorSkuIds;
    /**
     * 商品销售国家，用于查备货模式，不传默认备货返回null
     */
    private String countryCode;
    /**
     * 多语言列表、不传默认查中文名称和英文名称
     */
    private Set<String> langSet;
    /**
     * 查询枚举值
     */
    private Set<SkuQueryEnum> skuQueryEnums;

    /**
     * jd SKU ID 集合
     */
    @NotEmpty(message = "jdSkuIds can not empty" ,groups = {SkuReadValidateGroup.querySkuInfoByJdSkuId.class})
    @Size(max = 100,message = "jdSkuIds can not larger than 100",groups = {SkuReadValidateGroup.querySkuInfoByJdSkuId.class})
    private Set<Long> jdSkuIds;

    public QuerySkuReqDTO(Set<String> skuIds){
        this.skuIds = skuIds;
    }

    public QuerySkuReqDTO(String spuId){
        this.spuId = spuId;
    }

    /**
     * 查询商品属性信息用
     * @param skuIds skuId集合
     * @param countryCode 国家编码
     */
    public QuerySkuReqDTO(Set<String> skuIds, String countryCode) {
        this.skuIds = skuIds;
         this.countryCode = countryCode;
    }
}
