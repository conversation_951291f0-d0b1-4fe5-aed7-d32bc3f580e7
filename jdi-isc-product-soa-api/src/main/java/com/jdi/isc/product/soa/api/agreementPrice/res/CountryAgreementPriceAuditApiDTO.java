package com.jdi.isc.product.soa.api.agreementPrice.res;

import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @Description: sku客制化价格-草稿表 VO实体类
 * @Author: zhaokun51
 * @Date: 2025/03/10 13:30
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class CountryAgreementPriceAuditApiDTO extends BasicDTO {

    /** 货源国 */
    private String sourceCountryCode;

    /** 货源国 */
    private String sourceCountryName;

    /** 目标国 */
    private String targetCountryCode;

    /** 目标国 */
    private String targetCountryName;

    /** 申请单号 */
    private String applyCode;

    /** skuId */
    private Long skuId;

    /** skuName */
    private String skuName;

    /** mkuId */
    private Long mkuId;

    /** 类目id */
    private Long lastCatId;

    /** 类目名称 */
    private String catName;

    /** 品牌id */
    private Long brandId;

    /** 品牌名称 */
    private String brandName;

    /** 供应商编码 */
    private String vendorCode;

    /** 供应商名称 */
    private String vendorName;

    /** 采销 */
    private String buyer;

    /** 利润率 */
    private BigDecimal profitRate;

    /** 预警情况 */
    private String warningMsg;

    /** 发起人ERP */
    private String updater;

    /**
     * 国家协议价计算流程。
     */
    private String agreementMark;


    /**
     * 国家成本价计算流程。
     */
    private String costMark;

    /**
     * 审批流表单
     */
    private String processFromData;

    /**
     * 自定义列
     */
    private Map<String, String> customColumns;
}
