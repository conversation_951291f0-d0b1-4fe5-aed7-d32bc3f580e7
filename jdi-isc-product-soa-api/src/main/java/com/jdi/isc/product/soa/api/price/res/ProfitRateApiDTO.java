package com.jdi.isc.product.soa.api.price.res;

import com.jdi.common.domain.rpc.bean.BaseReqDTO;
import com.jdi.isc.product.soa.api.common.BaseDTO;
import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import com.jdi.isc.product.soa.api.common.BasicDTO;
import com.jdi.isc.product.soa.api.common.BasicReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 利润率阈值表 VO实体类
 * @Author: zhaokun51
 * @Date: 2024/10/29 21:46
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ProfitRateApiDTO extends BasicApiDTO {

    /**
     * 国家码，ISO 3166-1 两字母代码
     */
    @NotNull(message = "商品来源国不可为空")
    private String sourceCountryCode;

    /**
     * 国家码，ISO 3166-1 两字母代码
     */
    @NotNull(message = "商品销售国不可为空")
    private String targetCountryCode;

    /**
     * 一级类目id
     */
    private Long firstCatId;

    /**
     * 二级类目id
     */
    private Long secondCatId;

    /**
     * 三级类目id
     */
    private Long thirdCatId;

    /**
     * 末级类目id
     */
    private Long lastCatId;

    /**
     * 利润率阈值
     */
    @NotNull(message = "利润率阈值不能为空")
    private BigDecimal profitRate;

    /**
     * 超低利润率阈值
     */
    @NotNull(message = "超低利润率阈值不能为空")
    private BigDecimal lowProfitRate;


    // ----------------------------------- 展示用

    private String sourceCountryName;

    private String targetCountryName;

    private String firstCatName;

    private String secondCatName;

    private String thirdCatName;

    private String lastCatName;

}
