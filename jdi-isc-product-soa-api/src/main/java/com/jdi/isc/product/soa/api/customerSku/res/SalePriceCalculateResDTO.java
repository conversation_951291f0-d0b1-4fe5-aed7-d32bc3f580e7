package com.jdi.isc.product.soa.api.customerSku.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

@Data
public class SalePriceCalculateResDTO {

    /**
     * 客户编码
     */
    private String clientCode;

    /**
     * 客户国家编码
     */
    private String customerCountryCode;

    /**
     * 末级类目ID
     */
    private Long skuId;

    /**
     * 未税销售价
     */
    private BigDecimal salePrice;

    /**
     * 含税采购价
     */
    private BigDecimal purchasePrice;

    /**
     * 较低协议价
     */
    private BigDecimal minProtocolPrice;

    /**
     * 利润率
     */
    private BigDecimal profitRate;

    /**
     * 利润率阈值
     */
    private BigDecimal profitLimitRate;

    /**
     * 超低利润率阈值
     */
    private BigDecimal lowProfitLimitRate;

    /**
     * 进口税
     */
    private BigDecimal importRate;

    /**
     * 履约费率
     */
    private BigDecimal fulfillmentRate;

    /**
     * 毛利率
     */
    private BigDecimal grossRate;

    /**
     * 预警信息
     */
    private String warningMsg;

    /**
     * 综合税率
     */
    private BigDecimal valueAddTax;

    /**
     * 含税销售价
     */
    private BigDecimal taxSalePrice;

    /**
     * 是否从数据库中取值
     */
    private Boolean dbFlag = Boolean.FALSE;

    /**
     * 相关税率税额
     */
    private Map<String,Object> salePriceTaxRes;

    /**
     * 国家成本价
     */
    private BigDecimal countryCostPrice;

    /**
     * 国家协议价
     */
    private BigDecimal agreementPrice;

    /**
     * 京东价
     */
    private BigDecimal jdPrice;

    /**
     * 预估跨境退税额
     */
    private BigDecimal refundTaxPrice;

    /**
     * 国家跨境入仓价
     */
    private BigDecimal countryWarehousingPrice;

}
