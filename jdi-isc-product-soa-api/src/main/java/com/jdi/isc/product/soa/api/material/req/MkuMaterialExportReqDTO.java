package com.jdi.isc.product.soa.api.material.req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * excel导出批量查询
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class MkuMaterialExportReqDTO implements Serializable {
    /**
     * 物料编码
     */
    private String materialId;

    /**
     * 物料名字
     */
    private String materialName;

    /**
     * MKUID
     */
    private Long mkuId;

    /**
     * MKU名称
     */
    private String mkuName;

    /**
     * 客户编码
     */
    private String clientCode;

    /**
     * 绑定状态
     */
    private String bindStatus;
}
