package com.jdi.isc.product.soa.api.price;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.price.req.ExchangeRatePageReqApiDTO;
import com.jdi.isc.product.soa.api.price.res.ExchangeRateApiDTO;

import java.math.BigDecimal;

/**
 * @Description: 汇率表api服务
 * @Author: zhaokun51
 * @Date: 2025/03/04 15:30
 **/

public interface IscProductSoaExchangeRateReadApiService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<ExchangeRateApiDTO>> pageSearch(ExchangeRatePageReqApiDTO input);

    /**
     * 根据源货币和目标货币获取汇率。
     * @param sourceCurrency 源货币代码。
     * @param targetCurrency 目标货币代码。
     * @return 包含汇率信息的 DataResponse 对象。
     */
    DataResponse<BigDecimal> getExchangeRateByCurrency(String sourceCurrency, String targetCurrency);
}
