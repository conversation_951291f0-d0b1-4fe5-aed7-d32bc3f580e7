package com.jdi.isc.product.soa.api.material.req;


import com.jdi.isc.product.soa.api.common.BaseExcelDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MkuMaterialExcelReqDTO extends BaseExcelDTO {

    /**
     * 客户编码
     */
    @NotNull(message = "客户编号必填")
    private String clientCode;

    /**
     * 物料编码
     */
    @NotNull(message = "物料编码必填")
    private String materialId;

    /**
     * 物料名称
     */
    @NotNull(message = "物料名称必填")
    private String materialName;

    /**
     * MKU ID
     */
    @NotNull(message = "MKU id必填")
    @Pattern(regexp = "^[0-9]*$", message = "MKU id不合法")
    private String mkuId;

    /**
     * 用户类型，0=JD运营，1=客户
     */
    private Integer userType;
}
