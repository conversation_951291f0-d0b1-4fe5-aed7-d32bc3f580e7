package com.jdi.isc.product.soa.api.customerSku.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class CustomerSkuAuditApiDTO extends BaseReqDTO {
    /**
     * SPUID
     */
    @Size(max = 100, message = "id个数不能超长")
    @NotEmpty(message = "id数组不能为空")
    private List<Long> ids;
    /**
     * 审核人
     */
    private String auditErp;

    /**
     * 审核状态 1通过，2驳回
     *
     */
//    @NotNull(message = "审核状态不能为空")
    private Integer status;
    /**
     * 驳回原因
     */
    @Size(max = 200, message = "驳回原因超长")
    private String rejectReason;
}
