package com.jdi.isc.product.soa.api.markupRate.res;

import com.jdi.isc.product.soa.api.common.BaseLangDTO;
import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MarkupRateDTO extends BaseLangDTO implements Serializable {

    /**
     * 业务编号
     */
    private String bizNo;

    /**
     * MKUID
     */
    private Long mkuId;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 目标国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * 末级类目id
     */
    private Long lastCatId;

    /**
     * 品牌
     */
    private Long brandId;

    /**
     * 协议价加价率，单位：%
     */
    private BigDecimal agreementMarkupRate;

    /**
     * 京东价加价率，单位：%
     */
    private BigDecimal jdMarkupRate;
    /**
     * MKUID的标题信息
     */
    private String mkuTitle;

    /**
     * 最后一级类目的名称
     */
    private String catName;

    /**
     * SKU ID列表，用于批量查询和操作。
     */
    private List<Long> SkuIds;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 标识该VO实体是否有效
     */
    private Boolean valid = false;

    /**
     * 一级类目id
     */
    private Long firstCatId;

    /**
     * 二级类目id
     */
    private Long secondCatId;

    /**
     * 三级类目id
     */
    private Long thirdCatId;

}
