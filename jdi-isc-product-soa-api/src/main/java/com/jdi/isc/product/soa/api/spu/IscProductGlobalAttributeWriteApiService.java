package com.jdi.isc.product.soa.api.spu;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.spu.req.UpdateProductGlobalAttributeReqDTO;
import com.jdi.isc.product.soa.api.spu.res.UpdateGlobalAttributeResDTO;
import com.jdi.isc.product.soa.api.validation.GlobalAttributeValidGroup;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 跨境属性写接口
 *
 * <AUTHOR>
 * @date 2024/8/20
 **/
public interface IscProductGlobalAttributeWriteApiService {


    /**
     * 批量更新全局属性
     * @param reqDTO 包含更新请求的详细信息
     * @return 包含更新结果的 DataResponse 对象，内部封装了一个 UpdateGlobalAttributeResDTO 列表
     */
    @Validated(GlobalAttributeValidGroup.batchUpdateGlobalAttribute.class)
    DataResponse<UpdateGlobalAttributeResDTO> batchUpdateGlobalAttributes(UpdateProductGlobalAttributeReqDTO reqDTO);
}
