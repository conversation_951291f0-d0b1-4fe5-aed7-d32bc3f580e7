package com.jdi.isc.product.soa.api.spu.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/09/06
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SpuPrepareReqApiDTO extends BaseReqDTO {

    private Long lastCatId;

    private String attributeScope;

    private String sourceCountryCode;

    private Integer isExport;

    private String vendorCode;
}
