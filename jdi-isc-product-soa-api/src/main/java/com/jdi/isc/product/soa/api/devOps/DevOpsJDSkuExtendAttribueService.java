package com.jdi.isc.product.soa.api.devOps;

import com.jdi.common.domain.rpc.bean.DataResponse;

import java.util.List;

/**
 * 京东SKU扩展属性处理服务接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface DevOpsJDSkuExtendAttribueService {

    /**
     * 处理全量京东SKU扩展属性，将京东属性合并到ISC扩展属性中
     * @param updateTime 更新时间过滤条件，格式：yyyy-MM-dd HH:mm:ss
     * @param writeToDB 是否写入数据库，true-更新数据库，false-仅打印数据用于人工检验
     * @param pageIndex 起始页码，默认为1
     * @return 处理结果
     */
    DataResponse<String> processAllJDSkuExtAttributes(String updateTime, boolean writeToDB, Long pageIndex);

    /**
     * 处理全量京东SKU扩展属性，将京东属性合并到ISC扩展属性中
     * @param updateTime 更新时间过滤条件，格式：yyyy-MM-dd HH:mm:ss
     * @param writeToDB 是否写入数据库，true-更新数据库，false-仅打印数据用于人工检验
     * @return 处理结果
     */
    default DataResponse<String> processAllJDSkuExtAttributes(String updateTime, boolean writeToDB) {
        return processAllJDSkuExtAttributes(updateTime, writeToDB, 1L);
    }

    /**
     * 处理指定SKU列表的京东扩展属性，将京东属性合并到ISC扩展属性中
     * @param skuIdList 要处理的SKU ID列表
     * @param writeToDB 是否写入数据库，true-更新数据库，false-仅打印数据用于人工检验
     * @return 处理结果
     */
    DataResponse<String> processJDSkuExtAttributesBySkuIds(List<Long> skuIdList, boolean writeToDB);

    /**
     * 处理单个京东SKU ID的扩展属性，将京东属性合并到ISC扩展属性中
     * @param jdSkuId 京东SKU ID
     * @param writeToDB 是否写入数据库，true-更新数据库，false-仅打印数据用于人工检验
     * @return 处理结果
     */
    DataResponse<String> processJDSkuExtAttributesByJdSkuId(Long jdSkuId, boolean writeToDB);

    /**
     * 统计跨境品类目匹配数量
     *
     */
    DataResponse<Long> calculateJdSkuCatMatchAmount();
} 