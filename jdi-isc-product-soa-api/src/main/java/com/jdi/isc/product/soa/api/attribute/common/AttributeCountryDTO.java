package com.jdi.isc.product.soa.api.attribute.common;

import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 类目属性适用范围
 * <AUTHOR>
 * @date 20231124
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class AttributeCountryDTO extends BasicApiDTO {

    /** 属性id*/
    private Long attributeId;

    /** 国家码  */
    @NotNull(message = "国家不能为空")
    private String countryCode;
}
