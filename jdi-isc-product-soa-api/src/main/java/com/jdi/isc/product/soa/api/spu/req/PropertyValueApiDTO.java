package com.jdi.isc.product.soa.api.spu.req;

import com.jdi.isc.product.soa.api.common.BaseLangDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 属性值信息
 *
 * <AUTHOR>
 * @date 2023/11/27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class PropertyValueApiDTO {
    /**
     * 属性ID
     */
    private Long attributeId;

    /**
     * 属性名
     */
    private String attributeName;

    /**
     * 属性值ID
     */
    private Long attributeValueId;
    /**
     * 属性值名
     */
    private String attributeValueName;
    /**
     * 排序字段
     */
    private Integer sort;
    /**
     * 属性值语种 ZH、VN、TH
     */
    private String lang;
    /**
     * 属性值语种 ZH、VN、TH
     */
    private String langName;
    // 当前是否选择状态,默认未选中
    private Boolean selected;
    /**
     * 是否必填
     */
    private Boolean required;
    /**
     * 提示语
     */
    private String placeholderValue;

    /**
     * 当前组内的多语属性值
     * */
    private List<BaseLangDTO> langList;

    public PropertyValueApiDTO(Long attributeId, String lang, String attributeValueName) {
        this.attributeId = attributeId;
        this.lang = lang;
        this.attributeValueName = attributeValueName;
    }
}
