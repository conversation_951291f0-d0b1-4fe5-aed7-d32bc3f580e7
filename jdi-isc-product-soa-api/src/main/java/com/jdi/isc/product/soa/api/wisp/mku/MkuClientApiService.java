package com.jdi.isc.product.soa.api.wisp.mku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.wisp.mku.biz.*;

import java.util.List;

/**
 * @Description: 销端服务接口
 * @Author: zhaokun51
 * @Date: 2024/06/24 14:38
 **/
public interface MkuClientApiService {


    /**
     * 商品列表
     * @param input 列表参数f
     * @return 商品列表、包含全部信息es中全部信息
     */
    DataResponse<PageInfo<MkuClientApiDTO>> page(MkuClientPageReqApiDTO input);

    /**
     * 商品列表
     * @param input 列表参数
     * @return 仅返回mkuIds
     */
    default DataResponse<PageInfo<MkuClientListInfoResApiDTO>> pageSimple(MkuClientPageReqApiDTO input){
        return null;
    }

    /**
     * 商品列表
     * @param input mku编码s
     * @return skuInfo
     */
    default DataResponse<List<MkuClientListInfoResApiDTO>> mergeInfo(MkuClientListInfoReqApiDTO input){
        return null;
    }

    /**
     * 商品上下品-批量(初始化，或补偿商品用)-手动接口
     * @param input 列表参数f
     * @return 商品列表、包含全部信息es中全部信息
     */
    DataResponse<Boolean> upsertToEsBatchPage(String clientCode,Integer startPageNum,Integer endPageNum);

    /**
     * 商品上下品-批量(初始化，或补偿商品用)-手动接口
     * @param input 列表参数f
     * @return 商品列表、包含全部信息es中全部信息
     */
    DataResponse<Boolean> upsertToEsBatch(MkuClientListInfoReqApiDTO input);

    /**
     * 上下品
     * @param input 列表参数f
     * @return 商品列表、包含全部信息es中全部信息
     */
    DataResponse<Boolean> upsertToEs(MkuClientDetailReqApiDTO input);

    /**
     * 检查Mku是否存在
     * @param input 包含分页请求参数的Mku客户端请求DTO
     * @return 包含是否存在的响应数据
     */
    DataResponse<Long> existsMku(MkuClientPageReqApiDTO input);

    /**
     * 尝试获取 MKU 客户页面数据。
     * @param input MKU 客户页面请求 API DTO，包含必要的查询条件和分页信息。
     * @return 尝试结果，成功则返回 List<String> 类型的数据，否则返回错误信息。
     */
    DataResponse<List<String>> attempt(MkuClientPageReqApiDTO input);


    /**
     * 检查客户端是否在池中且不在黑名单中
     * @param dtoList 客户端详细信息请求DTO列表
     * @return 包含客户端在池中且不在黑名单中的信息的DataResponse对象
     */
    DataResponse<List<MkuClientInPoolApiDTO>> checkInPoolNoBlack(List<MkuClientDetailReqApiDTO> dtoList);

    /**
     * 最新入池商品
     * @param input
     * @return
     */
    DataResponse<List<MkuClientApiDTO>> latestWares(MkuClientLatestWareReqApiDTO input);

    /**
     * 商品列表信息
     * @param input
     * @return
     */
    DataResponse<List<MkuClientApiDTO>> queryWaresInfo(MkuListInfoReqApiDTO input);


    /**
     * 更新客户身份标识
     */
    DataResponse<Boolean> updateMkuCustomerFeature(MkuFeatureTagDTO mkuFeatureTagDTO);

    /**
     * 更新MKU国家池标志。
     * @param input MKU池标志DTO对象，包含要更新的标志信息。
     * @return 更新操作的结果，true表示更新成功，false表示更新失败。
     */
    DataResponse<Boolean> updateMkuPoolFlag(MkuPoolFlagDTO input);



    /**
     * 根据查询条件检索ES中的MKU客户信息
     * @param input MKU客户详情查询请求参数对象，包含查询条件信息
     * @return 包含ES查询结果的响应对象，封装MKU客户ES数据传输对象
     */
    DataResponse<MkuEsClientDTO> queryEsMkuByCondition(MkuEsDetailReqApiDTO input);

    /**
     * 更新MKU ES库存标签
     * @param mkuEsStockTagReqDTO MKU ES库存标签请求参数DTO
     * @return 包含操作结果的响应对象，其中Boolean类型表示操作是否成功
     */
    DataResponse<Boolean> updateMkuEsStockTag(MkuEsStockTagReqDTO mkuEsStockTagReqDTO);

    /**
     * 更新聚合标志
     * @param input 合并键DTO对象，包含合并的相关信息
     * @return 更新操作结果，true表示成功，false表示失败
     */
    DataResponse<Boolean> updateAggKey(MkuClientDetailReqApiDTO input);

    /**
     * 更新MKU履约时效标签
     * @param mkuPromiseTagDTO 包含MKU承诺特性标签信息的传输对象
     * @return 包含操作结果的响应对象，其中Boolean类型表示更新是否成功
     */
    DataResponse<Boolean> updateMkuPromiseFeature(MkuPromiseTagDTO mkuPromiseTagDTO);

}
