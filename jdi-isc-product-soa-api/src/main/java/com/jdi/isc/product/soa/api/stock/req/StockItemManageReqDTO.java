package com.jdi.isc.product.soa.api.stock.req;

import com.jdi.isc.product.soa.api.validation.StockValidGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 库存管理操作实体
 * <AUTHOR>
 * @date 2024/6/3
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockItemManageReqDTO {

    /** 国际skuId*/
    @NotNull(message = "skuId不能为空",groups = {StockValidGroup.order.class,StockValidGroup.saveOrUpdate.class,StockValidGroup.query.class})
    private Long skuId;

    /** 请求数量*/
    @NotNull(message = "num不能为空",groups = {StockValidGroup.order.class,StockValidGroup.saveOrUpdate.class})
    private Long num;

    /** 更新人*/
    @NotNull(message = "updater不能为空",groups = {StockValidGroup.order.class,StockValidGroup.saveOrUpdate.class})
    private String updater;
    /** 仓库ID */
    private String warehouseId;
}
