package com.jdi.isc.product.soa.api.material;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.material.req.MkuMaterialPageReqDTO;
import com.jdi.isc.product.soa.api.material.res.MkuMaterialApiDTO;

import java.util.List;

public interface IscMkuMaterialReadApiService {
    DataResponse<MkuMaterialApiDTO> queryMkuMaterialDTOByMkuId(Long mkuId);

    DataResponse<MkuMaterialApiDTO> queryMkuMaterialDTOById(Long id,String lang);

    DataResponse<List<MkuMaterialApiDTO>> queryMkuMaterialDTOsByMkuIds(List<Long> mkuIds);

    DataResponse<List<MkuMaterialApiDTO>> queryMkuMaterialDTOsByMkuIdClient(List<Long> mkuIds,String clientCode);

    DataResponse<List<MkuMaterialApiDTO>> queryMkuMaterialVOsByMaterialIds(List<String> materials, String clientCode);
    DataResponse<PageInfo<MkuMaterialApiDTO>> pqgeMkuMaterialDTOByReq(MkuMaterialPageReqDTO dto);

    DataResponse<PageInfo<MkuMaterialApiDTO>> pqgeAPIMkuMaterialDTO(MkuMaterialPageReqDTO dto);

    DataResponse<List<MkuMaterialApiDTO>> exportMkuMaterial(MkuMaterialPageReqDTO dto);
    /**
     * 检查指定mkuId和clientCode的组合是否存在。
     *
     * @return 如果存在，返回true；否则返回false。
     */
    DataResponse<MkuMaterialApiDTO> isExist(MkuMaterialApiDTO mkuMaterialApiDTO);
}
