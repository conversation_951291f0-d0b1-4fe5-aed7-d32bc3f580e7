package com.jdi.isc.product.soa.api.price.supplierPrice.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SkuPriceReqDTO extends BaseReqDTO implements Serializable {

    private Long spuId;

    /** SKU */
    private Long skuId;

    /** 货源国 */
    private String sourceCountryCode;

    /** 贸易方向 {@link TradeDirectionEnum} */
    //private TradeDirectionEnum tradeDirection;

    /** 贸易模式 */
    private String tradeType;

    /** 币种 */
    private String currency;

    /** 价格,4位小数,direction=SUPPLIER为采购价,否则为销售价*/
    private BigDecimal price;

    /** 含税价格,4位小数,direction=SUPPLIER为采购价,否则为销售价*/
    private BigDecimal taxPrice;

    /**
     * 审核状态，1:审核通过，2:驳回 3:待审核,0:撤销
     */
    private Integer auditStatus;

    /**
     * 审批人
     */
    private String auditor;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 利润率
     */
    private BigDecimal profitRate;

    /**
     * 预警情况
     */
    private String warningMsg;

    /**
     * 采销
     * */
    private String buyer;

    /**
     * 审批等级。
     */
    private Integer level;

    /**
     * 类型关键字，用于区分不同类型的请求。
     */
    private String typeKey;
}
