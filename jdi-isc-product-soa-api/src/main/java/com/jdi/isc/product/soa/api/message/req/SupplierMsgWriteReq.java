package com.jdi.isc.product.soa.api.message.req;

import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 供应商消息基础表
 * <AUTHOR>
 * @date 20240103
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SupplierMsgWriteReq extends BasicApiDTO {

    /**
     * 供应商简码
     */
    @NotNull(message = "supplierCode can not be null")
    private String supplierCode;

    /**
     * 消息类型
     */
    @NotNull(message = "type can not be null")
    private Integer type;

    /**
     * 消息正文
     */
    @NotNull(message = "content can not be null")
    private String content;

    /**
     * 消息落库时间
     */
    private Date msgTime;

}
