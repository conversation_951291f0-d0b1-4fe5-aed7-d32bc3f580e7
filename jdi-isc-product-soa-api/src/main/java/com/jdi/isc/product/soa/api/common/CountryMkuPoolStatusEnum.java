package com.jdi.isc.product.soa.api.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 入池状态
 * <AUTHOR>
 * @date 2024/12/03
 **/
@Getter
@AllArgsConstructor
public enum CountryMkuPoolStatusEnum implements KvEnumInterface {


    DRAFT(0, "待确认"),
    POOL(1, "已入池"),
    BLACK(2, "已拉黑"),
    NOT_POOL(3, "未入国家池"),
    TRANSLATION_NOT_APPROVAL(4, "翻译确认"),
    ;

    /**
     * The Map.
     */
    private static final Map<Integer, String> codeNameMap = new LinkedHashMap<Integer, String>() {{
        for (CountryMkuPoolStatusEnum e : CountryMkuPoolStatusEnum.values()) {
            put(e.getCode(), e.getDesc());
        }
    }};


    private final Integer code;
    private final String desc;

    public static CountryMkuPoolStatusEnum countryEnumByCode(Integer code) {
        if(code == null){
            return null;
        }

        CountryMkuPoolStatusEnum[] values = CountryMkuPoolStatusEnum.values();
        for (CountryMkuPoolStatusEnum v : values) {
            if (v.code.equals(code)) {
                return v;
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        if (code != null) {
            return codeNameMap.get(code);
        }

        return null;
    }

    @Override
    public Map<String, String> getKvMap() {
        return convert(codeNameMap);
    }
}
