package com.jdi.isc.product.soa.api.approveorder.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品审核分页查询对象
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class ApproveOrderSaveReqDTO extends BaseReqDTO implements Serializable {

    /**
     * 审核主表
     */
    private @Valid ApproveOrderDTO order;

    /**
     * 审核明细
     */
    private @Valid List<ApproveFormDTO> orderFormList;

    /**
     * 商品货源国
     */
    private String sourceCountryCode;

    /**
     * 客户国家
     */
    private String targetCountryCode;

    /**
     * 是否自动审批通过
     */
    private boolean isAutoAuditPass;

    /**
     * 获取表单数据
     */
    public Map<String, String> getFieldNameMap() {
        if (CollectionUtils.isEmpty(orderFormList)) {
            return new HashMap<>(16);
        }

        return orderFormList.stream()
                .filter(item -> StringUtils.hasText(item.getFieldName()) &&  StringUtils.hasText(item.getFieldValue()))
                .collect(Collectors.toMap(ApproveFormDTO::getFieldName, ApproveFormDTO::getFieldValue));
    }

    /**
     * 获取表单数据
     */
    public Map<String, String> getFieldCommentMap() {
        if (CollectionUtils.isEmpty(orderFormList)) {
            return new HashMap<>(16);
        }

        return orderFormList.stream()
                .filter(item -> StringUtils.hasText(item.getFieldComment()) &&  StringUtils.hasText(item.getFieldValue()))
                .collect(Collectors.toMap(ApproveFormDTO::getFieldComment, ApproveFormDTO::getFieldValue));
    }

    public void addFiled(String filedName, String fieldComment, String fieldValue) {
        if (orderFormList == null) {
            orderFormList = new ArrayList<>(16);
        }
        orderFormList.add(new ApproveFormDTO(filedName, fieldComment, fieldValue));
    }
}