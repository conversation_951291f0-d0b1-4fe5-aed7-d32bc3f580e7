package com.jdi.isc.product.soa.api.sku.req;

import com.jdi.isc.product.soa.api.common.CurrencyConstant;
import com.jdi.isc.product.soa.api.sku.res.GroupSkuCertificateDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuStockRelationDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyValueApiDTO;
import com.jdi.isc.product.soa.api.spu.res.GroupPropertyDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * SKU业务对象
 *
 * <AUTHOR>
 * @date 2023/11/25
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SaveSkuApiDTO {

    /**
     * 全部扩展属性，包含是否选中等
     */
    List<PropertyApiDTO> extendPropertyList;

    /**
     * 添加的扩展属性列表，包含属性组信息（与extendPropertyList的区别是，extendPropertyList是全部属性，storeExtendPropertyList是建立商品时添加的属性）
     * 列入类目下全部sku属性有10个，但是建立商品只添加了5个，那么extendPropertyList有10个，storeExtendPropertyList有5个
     */
    private List<PropertyApiDTO> storeExtendPropertyList;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * skuKey
     */
    private String skuKey;

    /**
     * SPUID
     */
    private Long spuId;

    /**
     * 工业国内SKUID
     */
    private Long jdSkuId;

    /**
     * EPT SKUID
     */
    private Long eptSkuId;

    /**
     * 供应商简码
     */
    private String vendorCode;

    /**
     * 供应商SKUID
     */
    private Long vendorSkuId;

    /**
     * 国内供应商简码
     */
    private String jdVendorCode;
    /**
     * 国内供应商名称
     */
    private String jdVendorName;

    /**
     * upc编码
     */
    private String upcCode;

    /**
     * 货源国、国家站
     */
    private String sourceCountryCode;

    /**
     * 末级后台类目ID
     */
    private Long catId;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 毛重(g)
     */
    private String weight;

    /**
     * 长(mm)
     */
    private String length;

    /**
     * 宽(mm)
     */
    private String width;

    /**
     * 高(mm)
     */
    private String height;

    /**
     * 销售属性及值 aa:bb,cc:dd
     */
    private String saleAttribute;

    /**
     * sku主图
     */
    //@NotNull(message = "sku主图不能为空")
    private String mainImg;

    /**
     * sku细节图
     */
    private String detailImg;
    /**
     * 备注
     */
    private String remark;
    /**
     * 带磁 默认0=不带磁 1=带磁
     *
     * @date 2024-1-6
     */
    private Integer magnetic;
    /**
     * 带电 默认0=不带电 1=带电
     *
     * @date 2024-1-6
     */
    private Integer electric;

    /**
     * 海关编码
     */
    private String hsCode;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后修改时间
     */
    private Date updateTime;

    /**
     * 逻辑删除 0=删除,1有效
     */
    private Integer yn;
    /**
     * 对商模式
     */
    private String vendorTradeType;
    /**
     * 对客模式
     */
    private String customerTradeType;
    /**
     * 币种 {@link CurrencyConstant}
     */
    private String currency;
    /**
     * 采购价
     */
    private String purchasePrice;
    /**
     * 销售价
     */
    private String salePrice;
    /**
     * 库存数量
     */
    private String stockNum;
    /**
     * sku上架时间
     */
    private Date skuOnlineTime;
    /**
     * 销售属性列表
     */
    private List<PropertyValueApiDTO> storeSalePropertyList;
    /**
     * 细节图列表
     */
    private List<String> detailImgList;
    /**
     * 商品状态
     */
    private Integer skuStatus;

    /**
     * 最小下订数量
     */
    private Integer moq;
    /**
     * 含液体
     */
    private Integer liquid;
    /**
     * 含粉尘
     */
    private Integer powder;
    /**
     * 发货时效
     */
    private BigDecimal productionCycle;
    /**
     * 供应商sku唯一编码
     */
    private String uniqueCode;
    /**
     * 售后方式 0:其他 1:只换不修 2:寄修 3:本地上门服务
     */
    private Integer returnType;

    /**
     * 适配器插头类型
     */
    private String adapterPlus;

    /**
     * 适用电压
     */
    private String voltage;

    /**
     * 原产地
     */
    private String originCountry;
    /**
     * 售后方式文本值
     */
    private String returnTypeValue;
    /**
     * 含税采购价
     */
    private String taxPurchasePrice;
    /**
     * 含税销售价
     */
    private String taxSalePrice;
    /**
     * 不退税毛利率
     */
    private BigDecimal noTaxGrossRate;
    /**
     * 退税毛利率
     */
    private BigDecimal taxGrossRate;
    /**
     *  采购模式 跨境品 null为直供，本土品无标  1、备货模式
     */
    private Integer purchaseModel;

    /**
     * 跨国属性信息，key->属性ID value->属性值列表
     */
    private List<GroupPropertyDTO> skuInterPropertyList;

    /**
     *  sku资质集合
     */
    private List<GroupSkuCertificateDTO> skuCertificateVOList;

    /**
     *  规格型号
     */
    private String specification;
    /**
     * sku库存列表、支持多仓
     */
    private List<SkuStockRelationDTO> skuStockRelationList;

    /** 采购价体系优化 2025-04-14 sunlei61 增加国家协议价、国家成本价、协议价利润率**/
    /**
     * 协议价
     */
    private BigDecimal agreementPrice;

    /**
     * 国家成本价
     */
    private BigDecimal countryCostPrice;
    /**
     * 协议价利润率
     */
    private BigDecimal agreementProfitRate;
    /**
     * 国家成本价币种
     */
    private String costCurrency;

    /**
     * 重量来源
     */
    private Integer weightSource;

    /**
     * 外部关联skuId(与国际sku一对一)
     */
    private String skuThirdRefId;
    /**
     * 供应商SKUID String类型 新字段
     */
    @Length(max = 100,message = "supplier skuId length cannot exceed 100")
    private String vendorSkuIdNew;
}
