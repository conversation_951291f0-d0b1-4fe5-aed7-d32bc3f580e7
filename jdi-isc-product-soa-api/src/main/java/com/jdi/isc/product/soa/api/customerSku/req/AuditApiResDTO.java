package com.jdi.isc.product.soa.api.customerSku.req;

import com.jdi.isc.product.soa.api.approveorder.common.AuditActionEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 客制化价格审核请求参数.
 *
 * <AUTHOR>
 */
@Data
public class AuditApiResDTO implements Serializable {

    /**
     * 审核动作，0-审核创建，1-审核通过，2=审核驳回，3-审核撤回
     *
     * {@link AuditActionEnum}
     */
    private Integer action;

    /**
     * 审批结果
     */
    private List<AuditIdResDTO> auditIdList;

    /**
     * 操作成功的ids
     */
    private List<Long> successIds;

    /**
     * 操作成功的ids
     */
    private List<Long> failIds;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AuditIdResDTO implements Serializable {
        /**
         * id
         */
        private Long id;

        /**
         * 是否操作成功
         */
        private boolean success;
        /**
         * 提示信息
         */
        private String tipMessage;
    }

    /**
     * 获取所有审核成功的ID列表
     * @return 返回成功审核的ID列表，若没有成功项则返回空列表
     */
    public List<Long> getSuccessIds() {
        if (CollectionUtils.isEmpty(auditIdList)) {
            return new ArrayList<>();
        }

        return auditIdList.stream()
                .filter(AuditIdResDTO::isSuccess)
                .map(AuditIdResDTO::getId)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取所有失败的审计ID列表
     * @return 包含所有失败审计ID的列表，若无失败项或输入为空则返回空列表
     */
    public List<Long> getFailIds() {
        if (CollectionUtils.isEmpty(auditIdList)) {
            return new ArrayList<>();
        }

        return auditIdList.stream()
                .filter(item -> !item.isSuccess())
                .map(AuditIdResDTO::getId)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }
}
