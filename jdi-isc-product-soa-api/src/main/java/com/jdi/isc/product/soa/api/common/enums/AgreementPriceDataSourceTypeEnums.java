package com.jdi.isc.product.soa.api.common.enums;

import com.jdi.isc.product.soa.api.common.KvEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum AgreementPriceDataSourceTypeEnums implements KvEnumInterface {
    AGREE(1,"协议价变更"),
    COST(2,"成本价变更"),

    /**
     * 履约税率变更.
     */
    FULFILLMENT_RATE(3, "履约税率变更"),
    /**
     * 采购价变更.
     */
    PURCHASE_PRICE(4, "采购价变更"),
    /**
     * 中国出口税率变更.œ
     */
    EXPORT_TAX_RATE(6, "中国出口税率变更"),
    /**
     * 退税成功率变更.
     */
    TAX_REFUND_RATE(7, "退税成功率变更"),
    /**
     * 汇率变更.
     */
    EXCHANGE_RATE(8, "汇率变更"),
    /**
     * 进口税变更.
     */
    IMPORT_TAX_RATE(9, "进口税变更"),
    /**
     * 手动修改.
     */
    MANUAL_MODIFICATION(10, "手动修改"),

    /**
     * MKU绑定
     */
    MKU_RELATION(11, "MKU绑定"),

    /**
     * sku体积比那更.
     */
    SKU_VOLUME(12, "sku体积变更"),

    /**
     * skuHS码变更.
     */
    SKU_HS_CODE(13, "skuHS码变更"),

    ;
    private int code;


    private String desc;


    public static AgreementPriceDataSourceTypeEnums countryEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        AgreementPriceDataSourceTypeEnums[] values = AgreementPriceDataSourceTypeEnums.values();
        for (AgreementPriceDataSourceTypeEnums v : values) {
            if (v.code == code) {
                return v;
            }
        }
        return null;
    }

    /**
     * The Map.
     */
    private static final Map<Integer, String> codeNameMap = new HashMap<Integer, String>() {{
        for (AgreementPriceDataSourceTypeEnums e : AgreementPriceDataSourceTypeEnums.values()) {
            put(e.getCode(), e.getDesc());
        }
    }};

    @Override
    public Map<String, String> getKvMap() {
        return convert(codeNameMap);
    }
}
