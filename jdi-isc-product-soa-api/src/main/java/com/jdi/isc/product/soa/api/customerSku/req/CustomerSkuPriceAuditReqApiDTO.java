package com.jdi.isc.product.soa.api.customerSku.req;


import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;


/**
 * 客户MKU关系实体
 * <AUTHOR>
 * @date 2024-10-21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CustomerSkuPriceAuditReqApiDTO extends BasePageReqDTO {

    /** 供应商简码 */
    private String vendorCode;

    /** 客户编码 */
    private String clientCode;

    /** skuIds */
    private List<Long> skuIds;

    /** mkuIds */
    private List<Long> mkuIds;

    /** 品牌 */
    private Long brandId;

    /** 一级类目 */
    private Long firstCatId;

    /** 二级类目 */
    private Long secondCatId;

    /** 三级类目 */
    private Long thirdCatId;

    /** 四级类目 */
    private Long lastCatId;

    /** 申请单号 */
    private String applyCode;

    /** 利润率 */
    private BigDecimal profitRateBegin;

    /** 利润率 */
    private BigDecimal profitRateEnd;

    /** 预警情况 */
    private String warningMsg;

    /** 发起人erp */
    private String updater;

    /** 采销 */
    private String buyer;
}
