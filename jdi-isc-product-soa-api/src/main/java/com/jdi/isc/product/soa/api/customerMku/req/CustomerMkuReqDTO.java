package com.jdi.isc.product.soa.api.customerMku.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class CustomerMkuReqDTO {
    /**
     * mkuId集合
     */
    private List<Long> mkuIdList;

    /**
     * mkuId
     */
    private Long mkuId;

    /** 客户编码*/
    private String clientCode;

    /**
     * 客户国家编码
     */
    private String clientCountryCode;

    /**
     * skuId、spuId、jdSkuId、mkuId
     */
    private List<Long> productIds;
}
