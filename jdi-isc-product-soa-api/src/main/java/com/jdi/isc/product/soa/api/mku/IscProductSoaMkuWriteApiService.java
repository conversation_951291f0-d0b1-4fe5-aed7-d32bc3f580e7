package com.jdi.isc.product.soa.api.mku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuDTO;
import com.jdi.isc.product.soa.api.mku.req.MkuLangReqDTO;
import com.jdi.isc.product.soa.api.subtitle.biz.MkuSubtitleImportReqDTO;
import com.jdi.isc.product.soa.api.subtitle.biz.MkuSubtitleImportResDTO;

public interface IscProductSoaMkuWriteApiService {


    /**
     * 更新多语言配置。
     * @param dto 多语言配置请求对象。
     * @return 更新结果。
     */
    DataResponse<String> updateSubtitle(MkuLangReqDTO dto);

    /**
     * 保存MKU语言信息。
     * @param dto MKU语言请求对象，包含要保存的语言信息。
     * @return 保存结果的响应对象，包含保存后的结果信息。
     */
    DataResponse<String> saveMkuLang(MkuLangReqDTO dto);

    /**
     * 导入mku短标题
     *
     * @param req the req
     * @return data response
     */
    DataResponse<MkuSubtitleImportResDTO> importMkuSubtitle(MkuSubtitleImportReqDTO req);
}
