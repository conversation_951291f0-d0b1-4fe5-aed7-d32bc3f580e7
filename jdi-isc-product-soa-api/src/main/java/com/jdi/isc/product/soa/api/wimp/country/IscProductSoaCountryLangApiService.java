package com.jdi.isc.product.soa.api.wimp.country;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.wimp.country.req.CountryLangApiDTO;
import com.jdi.isc.product.soa.api.wimp.country.res.CountryLangResDTO;
import com.jdi.isc.product.soa.api.wimp.country.res.CountryResDTO;
import com.jdi.isc.product.soa.api.wimp.lang.res.LangResDTO;

import java.util.Collection;
import java.util.List;

/**
 * 国家主数据写服务
 * <AUTHOR>
 * @date 2024/07/10
 */
public interface IscProductSoaCountryLangApiService {


    /**
     * 新增/保存
     */
    DataResponse<Boolean> upsert(CountryLangApiDTO input);

    /**
     * 查询列表
     */
    @Deprecated
    DataResponse<List<CountryLangResDTO>> queryList();

    /**
     * 查询列表
     */
    DataResponse<List<CountryLangResDTO>> queryListByLang(String lang);

    /**
     * 查询配置表中所有国家
     */
    @Deprecated
    DataResponse<List<CountryResDTO>> queryCountryAllList();

    /**
     * 查询配置表中所有国家
     */
    DataResponse<List<CountryResDTO>> queryCountryAllListByLang(String lang);

    /**
     * 查询配置表中所有语言
     */
    @Deprecated
    DataResponse<List<LangResDTO>> queryLangAllList();

    /**
     * 查询配置表中所有语言
     */
    DataResponse<List<LangResDTO>> queryLangAllListByLang(String lang);

    /**
     * 查询相关国家配置的语言
     */
    @Deprecated
    DataResponse<List<LangResDTO>> queryLangListByCountry(Collection<String> countryCodes);

    /**
     * 查询相关国家配置的语言
     */
    DataResponse<List<LangResDTO>> queryLangListByCountryByLang(Collection<String> countryCodes,String lang);

    /**
     * 重新加载缓存中配置
     * @return 返回重新加载是否成功
     */
    DataResponse<Boolean> reloadCache();

    /**
     * 根据国家代码获取语言代码列表
     * @param countryCode 国家代码
     * @return 包含语言代码列表的 DataResponse 对象
     */
    DataResponse<List<String>> getLangCodeByCountryCode(String countryCode);
}
