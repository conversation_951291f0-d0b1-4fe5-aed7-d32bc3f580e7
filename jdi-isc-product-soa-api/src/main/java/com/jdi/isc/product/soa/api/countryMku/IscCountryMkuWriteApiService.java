package com.jdi.isc.product.soa.api.countryMku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuApproveDTO;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuDTO;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuReqDTO;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuUpdateStatusDTO;

public interface IscCountryMkuWriteApiService {


    /**
     * 加入国家MKU池
     * @param dto 国家MKU请求DTO
     * @return 是否成功加入池
     */
    DataResponse<Boolean> mkuPoolJoinCountryPool(CountryMkuReqDTO dto);

    /**
     * 客户加入国家池。
     * @param dto 国家MKU请求DTO，包含客户ID和国家信息。
     * @return 加入结果，true表示成功，false表示失败。
     */
    DataResponse<Boolean> customerPoolJoinCountryPool(CountryMkuReqDTO dto);


    /**
     * 将mku通过消息加入指定国家的MKU池。
     * @param dto 包含用户ID和目标国家的请求对象。
     * @return 操作结果，true表示成功，false表示失败。
     */
    DataResponse<Boolean> mkuMsgJoinCountryPool(CountryMkuReqDTO dto);

    /**
     * 将jdSkuId通过消息加入指定国家的MKU池。
     * @param dto 包含用户ID和目标国家的请求对象。
     * @return 操作结果，true表示成功，false表示失败。
     */
    DataResponse<Boolean> jdSkuIdMsgJoinCountryPool(CountryMkuReqDTO dto);

    /**
     * 批量将指定国家的商品添加到黑名单中。
     * @param input 包含要操作的国家信息和商品信息的对象。
     * @return 操作结果的描述信息。
     */
    DataResponse<String> batchBlack(CountryMkuApproveDTO input);

    /**
     * 批量将指定国家的商品从黑名单中移除
     * @param input 包含要移除的国家信息和商品信息的对象
     * @return 执行结果的描述信息
     */
    DataResponse<String> batchOutBlack(CountryMkuApproveDTO input);

    /**
     * 批量处理国家MKU审批请求。
     * @param input 包含待处理的国家MKU审批信息。
     * @return 处理结果。
     */
    DataResponse<String> batchPool(CountryMkuApproveDTO input);


    /**
     * 根据指定的ID删除数据。
     * @param id 要删除的数据的ID。
     * @return 删除操作的结果。
     */
    DataResponse<String> delete(Long id);


    /**
     * 批量删除数据。
     * @param beginId 起始ID
     * @param endId 结束ID
     * @return 批量删除操作的结果
     */
    DataResponse<String> batchDelete(Long beginId,Long endId);

    /**
     * 保存或更新国家MKU信息。
     * @param countryMkuDTO 国家MKU数据传输对象
     * @return 保存或更新后的结果信息
     */
    DataResponse<String> saveOrUpdate(CountryMkuDTO countryMkuDTO);


    DataResponse<Boolean> anewMkuJoinCountryPool(CountryMkuReqDTO dto);

    /**
     * 检查指定国家的 MKU 规则是否符合要求，并返回结果。
     * @param countryCode 国家代码
     * @param warnStatus 警告状态码
     * @return 包含检查结果的 DataResponse 对象
     */
    DataResponse<String>  ruleCheckCountryMku(String countryCode, Integer warnStatus);

    /**
     * 更新MKU状态
     * @param dto 包含国家和MKU信息的DTO对象
     * @return 更新结果的字符串描述
     */
    DataResponse<String> updateMkuStatus(CountryMkuUpdateStatusDTO dto);
}
