package com.jdi.isc.product.soa.api.taxRate.req;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TaxRateReqDTO extends BasePageReqDTO implements Serializable {

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 客户简码
     */
    private String clientCode;

    /**
     * 海关编码
     */
    private String hsCode;

    /**
     * key类型。1 sku。2 类目
     */
    private Integer keyType;

    /**
     * 可能是skuId。也可能是类目 id
     */
    private String keyId;

    /**
     * 税的code码
     */
    private String taxCode;

    /**
     * 税的名称
     */
    private String taxName;

    /**
     * 增值税率
     */
    private BigDecimal taxRate;

    /**
     * 客户类型 0=供货商，1=客户
     */
    private Integer clientType;

    /**
     * 备注
     */
    private String remark;

    private String creator;

    private String updater;
}
