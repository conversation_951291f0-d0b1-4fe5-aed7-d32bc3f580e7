package com.jdi.isc.product.soa.api.supplier.res;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/8/30 9:16 上午
 */
@Data
public class SupplierSettlementAccountRes {


    /**
     * 供应商Code
     */
    private String supplierCode;

    /**
     * 收款单位名称
     */
    private String payeeUnitName;

    /**
     * 开户公司名称
     */
    private String companyNameOpenAccount;

    /**
     * 开户行名称
     */
    private String bankAccountNameOpen;

    /**
     * 开户银行账号
     */
    private String bankAccountNumberOpen;

    /**
     * swift code
     */
    private String swiftCode;

    /**
     * 国家代码，swift code的第五位和第六位
     */
    private String swiftCountryCode;

    /**
     * 中转行名称
     */
    private String transBankNameOpen;

    /**
     * 中转行swift code
     */
    private String transSwiftCode;

    /**
     * 收款账户性质
     */
    private String payeeAccountNature;


    /**
     * 银行账户code
     */
    private String bankFinanceAccountCode;


    /**
     * 银行联行号
     */
    private String bankKeyNo;

    /**
     * 银行所属国家
     */
    private String bankCountryCode;

}
