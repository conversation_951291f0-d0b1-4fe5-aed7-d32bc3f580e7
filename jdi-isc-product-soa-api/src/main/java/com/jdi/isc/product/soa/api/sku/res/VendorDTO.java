package com.jdi.isc.product.soa.api.sku.res;

import lombok.Data;

import java.util.Date;


@Data
public class VendorDTO {

    /** 财务侧客商主数据编码*/
    private String mCode;
    /** 商家编码*/
    private String vendorCode;
    /** 商家名称*/
    private String vendorName;
    /** 税号*/
    private String taxCode;
    /** 商家类型:1国内,2国外*/
    private Integer vendorType;
    /** 供应商所属国家站:VN越南CN中国*/
    private String sourceCountryCode;
    /** 商家状态*/
    private Integer status;
    /** 生效起始时间*/
    private Date effectiveTime;
    /** 生效结束时间*/
    private Date expireTime;
    /** 排序*/
    private Integer sort;
    /** 键值对，扩展字段*/
    private String features;
}
