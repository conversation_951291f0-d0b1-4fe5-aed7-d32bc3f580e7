package com.jdi.isc.product.soa.api.outUrl;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.outUrl.req.OutUrlMapDTO;
import com.jdi.isc.product.soa.api.outUrl.req.OutUrlTransformReq;
import com.jdi.isc.product.soa.api.outUrl.res.OutUrlTransformRes;

import java.util.List;

/**
 * 外链读写服务
 * <AUTHOR>
 * @date 20250401
 */
public interface IscOutUrlApiService {

    /**
     * 执行外链转内链服务
     */
    DataResponse<Boolean> doUrlTransform(OutUrlMapDTO req);

    /**
     * 批量外链转换
     */
    DataResponse<List<OutUrlTransformRes>> batchTransform(OutUrlTransformReq req);

}