package com.jdi.isc.product.soa.api.material.req;

import com.jdi.isc.product.soa.api.common.BaseExcelDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MkuMaterialDeleteExcelReqDTO extends BaseExcelDTO {

    /**
     * 客户编码
     */
    @NotNull(message = "客户编号必填")
    private String clientCode;

    /**
     * 物料编码
     */
    @NotNull(message = "物料编码必填")
    private String materialId;
}
