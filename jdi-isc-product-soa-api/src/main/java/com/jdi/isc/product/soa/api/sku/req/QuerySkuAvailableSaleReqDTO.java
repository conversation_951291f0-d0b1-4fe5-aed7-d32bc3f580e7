package com.jdi.isc.product.soa.api.sku.req;


import com.jdi.isc.product.soa.api.validation.SkuReadValidateGroup;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description：商品可售性请求对象
 * @Date 2025-02-25
 */
@Data
@NoArgsConstructor
public class QuerySkuAvailableSaleReqDTO {
    /**
     * 商品ID集合
     */
    @NotNull(message = "skuIds can not be null",groups = {SkuReadValidateGroup.queryCrossBorderSkuLimitAreaMap.class, SkuReadValidateGroup.queryCrossBorderSkuSaleMap.class})
    @Size(max = 100, message = "skuIds size must be less than 100",groups = {SkuReadValidateGroup.queryCrossBorderSkuLimitAreaMap.class, SkuReadValidateGroup.queryCrossBorderSkuSaleMap.class})
    private Set<Long> skuIds;
    /**
     * 国家编码
     */
    @NotNull(message = "countryCode can not be null",groups = {SkuReadValidateGroup.querySkuAvailableMap.class})
    private String countryCode;
    /**
     * 客户编码
     */
    private String clientCode;
    /**
     * sku信息集合
     */
    @NotEmpty(message = "skuSaleReqList can not be null",groups = {SkuReadValidateGroup.querySkuAvailableMap.class})
    @Size(max = 100, message = "skuIds size must be less than 100",groups = {SkuReadValidateGroup.querySkuAvailableMap.class})
    private List<SkuSaleReqDTO> skuSaleReqList;


    public QuerySkuAvailableSaleReqDTO(Set<Long> skuIds, String countryCode) {
        this.skuIds = skuIds;
         this.countryCode = countryCode;
    }

    public QuerySkuAvailableSaleReqDTO(Set<Long> skuIds, String countryCode, String clientCode) {
        this.skuIds = skuIds;
         this.countryCode = countryCode;
         this.clientCode = clientCode;
    }

    public QuerySkuAvailableSaleReqDTO(String countryCode, List<SkuSaleReqDTO> skuSaleReqList) {
        this.countryCode = countryCode;
         this.skuSaleReqList = skuSaleReqList;
    }
}
