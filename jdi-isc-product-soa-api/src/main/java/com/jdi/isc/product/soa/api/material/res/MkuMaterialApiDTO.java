package com.jdi.isc.product.soa.api.material.res;

import com.jdi.isc.product.soa.api.common.BaseExcelDTO;
import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MkuMaterialApiDTO extends BaseExcelDTO {
    /**
     * 物料编码
     */
    private String materialId;

    /**
     * 物料名字
     */
    private String materialName;

    /**
     * MKUID
     */
    private Long mkuId;

    /**
     * MKU名称
     */
    private String mkuName;

    /**
     * 客户编码
     */
    private String clientCode;

    /**
     * 绑定状态
     */
    private String bindStatus;
    /**
     * 用户类型，0=JD运营，1=客户
     */
    private Integer userType;
    /**
     * 客户名称
     */
    private String clientName;

    /** 京东销售价,未税销价*/
    private BigDecimal salePrice;
    /**
     * 保存状态
     */
    private String saveStatus;

    /**
     * 存储关联的MKU ID列表。
     */
    private List<Long> mkuIds;

}
