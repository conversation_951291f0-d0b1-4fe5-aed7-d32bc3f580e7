package com.jdi.isc.product.soa.api.customerSku.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.api.common.PriceTypeEnum;
import com.jdi.isc.product.soa.api.customerSku.res.TaxRateDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: sku客制化价格-草稿表 VO实体类
 * @Author: zhaokun51
 * @Date: 2024/10/21 13:30
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSkuPriceDraftIdApiDTO extends BaseReqDTO {

    @NotNull(message = "明细草稿表id不能为空")
    private Long detailDraftId;

}
