package com.jdi.isc.product.soa.api.supplier.res;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description 供应商账号
 * @Date 2024/3/18 3:34 下午
 */
@Data
public class SupplierAccountRes {

    /**
     * 供应商账号id
     */
    private Long id;
    /**
     * 商家code
     */
    private String supplierCode;
    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 账户状态 1：未生效，2：已生效
     */
    private Integer accountStatus;
    /**
     * 账户类型
     */
    private Integer accountType;

    /**
     * 账号邮箱
     */
    private String accountEmail;

}
