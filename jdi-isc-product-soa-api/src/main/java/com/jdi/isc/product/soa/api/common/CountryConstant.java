package com.jdi.isc.product.soa.api.common;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 国家常量
 * <a href="https://zh.wikipedia.org/wiki/ISO_3166-1">src</a>
 */
public class CountryConstant {

    public static List<String> COUNTRY_LIST;
    /**
     * 所有的国家编码
     */
    public static List<String> ALL_COUNTRY_CODE_LIST = null;
    /** 国家列表*/
    public final static String COUNTRY_ZH = "CN";
    public final static String COUNTRY_EN = "US";
    public final static String COUNTRY_VN = "VN";
    public final static String COUNTRY_TH = "TH";
    public final static String COUNTRY_MY = "MY";
    public final static String COUNTRY_BR = "BR";
    public final static String COUNTRY_HU = "HU";
    public final static String COUNTRY_UZ = "UZ";
    public final static String GLOBAL = "GLOBAL";
    public final static String COUNTRY_MIX = "ALL";
    public final static String COUNTRY_HK = "HK";
    public final static String COUNTRY_ID = "ID";
    /** 默认*/
    public final static String COUNTRY_DEFAULT = "DEFAULT";

    static {
        COUNTRY_LIST = new ArrayList<>();
        // 香港
        COUNTRY_LIST.add("HK");
        // 越南
        COUNTRY_LIST.add("VN");
        // 泰国
        COUNTRY_LIST.add("TH");
        // 匈牙利
        COUNTRY_LIST.add("HU");
        // 巴西
        COUNTRY_LIST.add("BR");
        // 马来西亚
        COUNTRY_LIST.add("MY");
        // 印度尼西亚
        COUNTRY_LIST.add("ID");
        ALL_COUNTRY_CODE_LIST = new ArrayList<>();
        ALL_COUNTRY_CODE_LIST.addAll(COUNTRY_LIST);
        ALL_COUNTRY_CODE_LIST.add(COUNTRY_ZH);
    }

    /**
     * 是否海外国家
     */
    public static boolean isOutCountry(String countryCode){
        return COUNTRY_LIST.contains(countryCode);
    }

    /**
     * 是否国家
     */
    public static boolean isALlCountry(String countryCode){
        return ALL_COUNTRY_CODE_LIST.contains(countryCode);
    }

    /**
     * 是否中国
     */
    public static boolean isChina(String countryCode){
        return Objects.equals(COUNTRY_ZH,countryCode);
    }
}