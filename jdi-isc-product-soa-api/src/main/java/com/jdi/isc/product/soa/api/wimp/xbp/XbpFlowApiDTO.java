package com.jdi.isc.product.soa.api.wimp.xbp;

import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2024/5/8 14:17
 **/
@Data
public class XbpFlowApiDTO {
    /**
     * 节点名称
     */
    private String flowName;

    /**
     * 审批节点序号
     */
    private Integer stage;

    private Integer status;

    private String statusText;

    private Integer allApprove;

    private List<XbpFlowApproveApiDTO> flowApproves;

}
