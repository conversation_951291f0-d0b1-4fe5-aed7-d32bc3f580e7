package com.jdi.isc.product.soa.api.common;

import lombok.Data;

import java.io.Serializable;

/**
 * 请求实体基础类
 * <AUTHOR>
 * @date 20240624
 */
@Data
public class BaseReqDTO implements Serializable {

    /** 自增ID*/
    private Long id;

    /** 用户pin*/
    private String pin;

    /** 客户简码*/
    private String clientCode;

    /** 语种*/
    private String lang;

    /** 站点类型 */
    private Integer stationType;

    /** 系统编码 */
    private String systemCode;
}
