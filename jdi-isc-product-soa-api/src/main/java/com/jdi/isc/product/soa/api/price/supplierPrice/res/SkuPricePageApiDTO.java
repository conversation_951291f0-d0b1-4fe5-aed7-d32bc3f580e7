package com.jdi.isc.product.soa.api.price.supplierPrice.res;

import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 国家协议价分页查询对象
 * @Author: wangpeng965
 * @Date: 2025/03/03 16:45
 **/

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SkuPricePageApiDTO extends BasicApiDTO implements Serializable {
    /** SPU */
    private Long spuId;

    /** SKU */
    private Long skuId;

    /** SKU */
    private Long mkuId;

    /** SKU */
    private String skuName;

    /** 货源国 */
    private String sourceCountryCode;

    /** 货源国 */
    private String sourceCountryName;

    /** 品牌 */
    private Long brandId;

    /** 品牌 */
    private String brandName;

    /** 币种 */
    private String currency;

    /** 商品类目 */
    private Long catId;

    /** 商品类目 */
    private String catName;

    /*** 采销 **/
    private String buyer;

    /** 价格,4位小数,direction=SUPPLIER为采购价,否则为销售价*/
    private BigDecimal price;

    /** 含税价格,4位小数,direction=SUPPLIER为采购价,否则为销售价*/
    private BigDecimal taxPrice;

    /*** 供应商简称 */
    private String vendorName;

    /*** 商品状态 */
    private Integer spuStatus;

    /*** 审核状态 */
    private Integer auditStatus;

    /**
     * 类型关键字，用于区分不同类型的请求。
     */
    private String typeKey;

    /**
     * 供应商代码
     */
    private String vendorCode;

    /**
     * 扩展字段1
     */
    private String value1;


    //------------------------------ 草稿列表 start
    /** 审核人 */
    private String auditor;

    /** 拒绝原因 */
    private String rejectReason;

    /** 预警情况 */
    private String warningMsg;

    /**
     * 国家成本价格，用于记录商品在特定国家的成本信息。
     */
    private BigDecimal countryCostPrice;

    /**
     * 与供应商达成的协议价格。
     */
    private BigDecimal agreementPrice;

    /**
     * 成本标记，用于记录与供应商协商的成本信息。
     */
    private String costMark;

    /**
     * 协议标记，用于标识与供应商达成的协议情况。
     */
    private String agreementMark;

    /** 利润率 */
    private BigDecimal profitRate;

    /**
     * 成本价和协议价的货币类型 币种:VND越南,THB泰国,CNY人民币,USD美元。
     */
    private String costCurrency;
    /**
     * 增值税率
     */
    private BigDecimal vatRate;
    //------------------------------ 草稿列表 end
}
