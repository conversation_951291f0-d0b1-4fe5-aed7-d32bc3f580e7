package com.jdi.isc.product.soa.api.mkuTag.res;

import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 国际商品标签表分页查询对象
 * @Author: wangpeng965
 * @Date: 2024/12/17 13:47
 **/

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MkuTagPageApiDTO extends BasicDTO implements Serializable{
        /**
         * SPUID
         */
        private Long spuId;

        /**
         * SKUID
         */
        private Long skuId;

        /**
         * MKUID
         */
        private Long mkuId;

        /**
         * 属性key ID (jdi_isc_product_special_attr)
         */
        private Long attributeKeyId;

        /**
         * 属性value ID (jdi_isc_product_special_attr_value)
         */
        private Long attributeValueId;

        /**
         * 商品所在国家代码
         */
        private String sourceCountryCode;

        /**
         * 商品销售目标国家代码
         */
        private String targetCountryCode;

}
