package com.jdi.isc.product.soa.api.common.enums;

import com.jdi.isc.product.soa.api.common.KvEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * The enum Customer sku data source type enums.
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CustomerSkuDataSourceTypeEnums implements KvEnumInterface {
    /**
     * sku客制化价格变更.
     */
    CUSTOMER(1,"sku客制化价格变更"),
    /**
     * 成本价变更.
     */
    COST(2,"成本价变更"),

    /**
     * 履约税率变更.
     */
    FULFILLMENT_RATE(3, "履约税率变更"),
    /**
     * 采购价变更.
     */
    PURCHASE_PRICE(4, "采购价变更"),
    /**
     * 中国出口税率变更.
     */
    EXPORT_TAX_RATE(6, "中国出口税率变更"),
    /**
     * 退税成功率变更.
     */
    TAX_REFUND_RATE(7, "退税成功率变更"),
    /**
     * 汇率变更.
     */
    EXCHANGE_RATE(8, "汇率变更"),
    /**
     * 进口税变更.
     */
    IMPORT_TAX_RATE(9, "进口税变更"),
    /**
     * 手动修改.
     */
    MANUAL_MODIFICATION(10, "手动修改"),

    /**
     * MKU绑定
     */
    MKU_RELATION(11, "MKU绑定"),

    /**
     * sku体积比那更.
     */
    SKU_VOLUME(12, "sku体积变更"),

    /**
     * skuHS码变更.
     */
    SKU_HS_CODE(13, "skuHS码变更"),
    /**
     * sku类目的变更
     */
    SKU_CAT(14,"sku类目变更"),
    /**
     * 协议价率
     */
    MARKUP_RATE(15,"协议价率"),
    ;
    private int code;


    private String desc;


    /**
     * Country enum by code customer sku data source type enums.
     *
     * @param code the code
     * @return the customer sku data source type enums
     */
    public static CustomerSkuDataSourceTypeEnums countryEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        CustomerSkuDataSourceTypeEnums[] values = CustomerSkuDataSourceTypeEnums.values();
        for (CustomerSkuDataSourceTypeEnums v : values) {
            if (v.code == code) {
                return v;
            }
        }
        return null;
    }

    /**
     * The Map.
     */
    private static final Map<Integer, String> codeNameMap = new HashMap<Integer, String>() {{
        for (CustomerSkuDataSourceTypeEnums e : CustomerSkuDataSourceTypeEnums.values()) {
            put(e.getCode(), e.getDesc());
        }
    }};

    private static final Map<Integer, CustomerSkuDataSourceTypeEnums> enumMap = new HashMap<Integer, CustomerSkuDataSourceTypeEnums>() {{
        for (CustomerSkuDataSourceTypeEnums e : CustomerSkuDataSourceTypeEnums.values()) {
            put(e.getCode(), e);
        }
    }};

    public static CustomerSkuDataSourceTypeEnums codeOf(Integer code) {
        return enumMap.get(code);
    }

    @Override
    public Map<String, String> getKvMap() {
        return convert(codeNameMap);
    }
}
