package com.jdi.isc.product.soa.api.taxRate.res;


import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @description：客户侧商品税率信息
 * @Date 2024-12-16
 */
@Data
public class CustomerMkuTaxResDTO {
    /**MKU ID*/
    private Long mkuId;
    /**销侧海关编码*/
    private String hsCode;
    /**巴西特殊NCM海关编码**/
    private String ncm;
    /**综合税率**/
    private String complexTaxRate;
    /**税率种类和税率值映射 TaxTypeEnum**/
    private Map<String,String> taxRateMap;
    /**巴西CST注册号 **/
    private String cst;
}
