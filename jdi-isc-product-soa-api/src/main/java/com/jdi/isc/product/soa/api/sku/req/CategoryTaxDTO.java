package com.jdi.isc.product.soa.api.sku.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.api.taxRate.req.CategoryFullNodeDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/3/26 9:45 下午
 */

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CategoryTaxDTO extends BaseReqDTO {

    /**
     * 国家码
     */
    @NotNull(message = "国家码不能为空")
    private String countryCode;

    /**
     * 企业性质 1:EPE, 2:FDI
     */
    private Integer companyType;

    /**
     * 末级类目id
     */
    private Long categoryId;

    /**
     * 客户简码
     */
    private String clientCode;

    /**
     * 海关编码
     */
    private String hsCode;

    /**
     * 增值税率
     */
    private BigDecimal vatRate;

    /**
     * 类目全级类名
     */
    private String fullCategoryName;

    /**
     * 类目节点平铺
     */
    private CategoryFullNodeDTO fullNodeVO;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 目的国增值税率
     */
    private BigDecimal destinationVatRate;


    /**
     * 客户名称
     */
    private String clientName;


    /**
     * 匹配到已存在数据后更新
     */
    private Boolean matchAndUpdate;

    private List<Long> skuIds;

    public CategoryTaxDTO(Long categoryId, Integer companyType, String countryCode){
        this.categoryId = categoryId;
        this.companyType = companyType;
        this.countryCode = countryCode;
    }
}
