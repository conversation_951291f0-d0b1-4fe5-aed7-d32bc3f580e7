package com.jdi.isc.product.soa.api.customerSku.res;

import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class TaxRateDTO extends BasicDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 客户简码
     */
    private String clientCode;

    /**
     * key类型。1 sku。2 类目
     */
    private Integer keyType;

    /**
     * 可能是skuId。也可能是类目 id
     */
    private String keyId;

    /**
     * 税的code码
     */
    private String taxCode;

    /**
     * 税的名称
     */
    private String taxName;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 客户类型 0=供货商，1=客户
     */
    private Integer clientType;

    /**
     * 备注
     */
    private String remark;

}
