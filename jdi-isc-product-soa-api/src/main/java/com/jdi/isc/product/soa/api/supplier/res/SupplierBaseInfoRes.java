package com.jdi.isc.product.soa.api.supplier.res;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/8/30 9:18 上午
 */
@Data
public class SupplierBaseInfoRes {

    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * 供应商状态
     */
    private Integer supplierStatus;

    /**
     * M Code
     */
    private String merchantCode;

    /**
     * 营业执照名称
     */
    private String businessLicenseName;

    /**
     * 营业执照编号
     */
    private String businessLicenseNumber;

    /**
     * 营业执照文件
     */
    private String businessLicenseFile;

    /**
     * 营业执照注册时间
     */
    private Long businessLicenseRegistTime;

    /**
     * 营业执照所在国家
     */
    private String businessLicenseCountry;

    /**
     * 企业性质
     */
    private Integer nature;

    /**
     * 企业类型
     */
    private Integer enterpriseType;

    /**
     * 税号
     */
    private String taxId;

    /**
     * 组织机构代码
     */
    private String organizationCode;

    /**
     * 注册资金
     */
    private BigDecimal registerCapital;

    /**
     * 注册资金币种
     */
    private String registerCurrency;

    /**
     * 开票类型
     */
    private String invoiceTypeRemark;

    /**
     * 批次号
     */
    private String batchNum;

    /**
     * 客商来源码
     */
    private String merchantSourceCode;

    /**
     * 创建人
     */
    protected String creator;

    /**
     * 来源是否为开放式供应商
     */
    private Boolean IsOpenSupplier;


}
