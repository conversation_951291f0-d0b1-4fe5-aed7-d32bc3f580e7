package com.jdi.isc.product.soa.api.agreementPrice.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/8
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class CountryAgreementPriceAuditDTO extends BaseReqDTO {
    /**
     * SPUID
     */
    @Size(max = 20, message = "id个数不能超长")
    @NotEmpty(message = "id不能为空")
    private List<Long> ids;
    /**
     * 审核人
     */
    private String auditErp;

    /**
     * 审核状态 1通过，2驳回
     *
     */
    private Integer status;
    /**
     * 驳回原因
     */
    @Size(max = 200, message = "驳回原因超长")
    private String rejectReason;

    /**
     * 流程实例ID，用于关联审批流程。
     */
    private String processInstanceId;
}
