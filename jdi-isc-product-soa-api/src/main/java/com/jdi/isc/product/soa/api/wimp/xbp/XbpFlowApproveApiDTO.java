package com.jdi.isc.product.soa.api.wimp.xbp;

import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2024/5/8 14:17
 **/
@Data
public class XbpFlowApproveApiDTO {

    /**
     * 配置的审批人
     */
    private List<XbpFlowApproverApiDTO> approvers;

    /**
     * 实际审批操作人
     */
    private XbpFlowApproveOperatorApiDTO approveOperator;

    private Integer status;
    private String statusText;

    /**
     * 审批意见
     */
    private String opinion;

    /**
     * 例：2024-04-23 16:14:17.068
     */
    private String approveTime;

}
