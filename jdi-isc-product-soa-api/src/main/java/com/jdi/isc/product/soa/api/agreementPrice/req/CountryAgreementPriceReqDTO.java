package com.jdi.isc.product.soa.api.agreementPrice.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CountryAgreementPriceReqDTO extends BaseReqDTO implements Serializable {

    /**
     * 业务编号
     */
    private String bizNo;

    /**
     * MKUID
     */
    private Long mkuId;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 目标国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * 末级类目id
     */
    private Long lastCatId;

    /**
     * 品牌
     */
    private Long brandId;

    /**
     * 协议价
     */
    private BigDecimal agreementPrice;

    /**
     * MKUID的标题信息
     */
    private String mkuTitle;

    /**
     * 最后一级类目的名称
     */
    private String catName;

    /**
     * SKU ID列表，用于批量查询和操作。
     */
    private List<Long> skuIds;

    /**
     * 国家成本价
     */
    private BigDecimal countryCostPrice;

    /**
     * 货币类型。
     */
    private String currency;

    /**
     * 国家协议价计算流程。
     */
    private String agreementMark;


    /**
     * 国家成本价计算流程。
     */
    private String costMark;

    /**
     * 京东价
     */
    private BigDecimal jdPrice;

    /**
     * 调价原因
     */
    private String adjustmentPriceReason;

    /**
     * 附件
     */
    private List<String> attachmentUrls;
}
