package com.jdi.isc.product.soa.api.spu.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpuUpdatePriceReqDTO extends BaseReqDTO {
    /**
     * 商品SPU的唯一标识符。
     */
    private Long spuId;

    /**
     * SKU的唯一标识符。
     */
    private Long skuId;


    /**
     * SKU的当前价格
     */
    private BigDecimal price;

    /**
     * SKU的供货价格
     */
    private BigDecimal purchasePrice;

    /**
     * SKU的含税供货价格。
     */
    private BigDecimal taxPurchasePrice;
    /**
     * 未税销售价
     */
    private BigDecimal salePrice;
    /**
     * 含税销售价
     */
    private BigDecimal taxSalePrice;

    /**
     * 供应商SKU的唯一标识符。
     */
    private String vendorSkuId;

    // updateType 1 未税采购价更新含税采购价， 2 含税采购价，更新未税采购价，
    // 3 未税销售价，更新含税销售价格，4 含税销售价格，更新未税销售价
    private Integer updateType;
}
