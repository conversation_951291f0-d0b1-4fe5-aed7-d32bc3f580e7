package com.jdi.isc.product.soa.api.mkuTag.res;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MkuTagIdDTO {
        /**
         * 属性key ID (jdi_isc_product_special_attr)
         */
        private Long attributeKeyId;

        /**
         * 属性value ID (jdi_isc_product_special_attr_value)
         */
        private Long attributeValueId;
}
