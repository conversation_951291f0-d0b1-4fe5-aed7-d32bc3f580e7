package com.jdi.isc.product.soa.api.stock;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockSplitReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockOccupyResDTO;
import com.jdi.isc.product.soa.api.validation.StockValidGroup;
import org.springframework.validation.annotation.Validated;

import java.util.Set;


/**
 * 库存写微服务
 *
 * <AUTHOR>
 * @date 2024/6/3
 */
public interface IscProductSoaStockWriteApiService {

    /**
     * 库存预占(提单前)
     */
    @Validated(StockValidGroup.order.class)
    DataResponse<Boolean> occupy(StockManageReqDTO req);

    /**
     * 预占扣减(提单支付后的正向流程)
     */
    @Validated(StockValidGroup.order.class)
    DataResponse<Boolean> release(StockManageReqDTO req);

    /**
     * 预占回退(提单失败)
     */
    @Validated(StockValidGroup.order.class)
    DataResponse<Boolean> occupyReturn(StockManageReqDTO req);

    /**
     * 现货回退(订单取消或售后)
     */
    @Validated(StockValidGroup.order.class)
    DataResponse<Boolean> stockReturn(StockManageReqDTO req);

    /**
     * 库存更新(运营或供应商维护库存)
     */
    @Validated(StockValidGroup.saveOrUpdate.class)
    DataResponse<Boolean> saveOrUpdate(StockManageReqDTO req);

    /**
     * 库存初始化
     */
    DataResponse<Boolean> resetStock(Set<Long> skuId);

    /**
     * 库存预占转现货
     * 接口返参示例：
     * {
     *     "code": "0",
     *     "data": {
     *         "orderId": "250701220000001",
     *         "stockItemOccupy": [
     *             {
     *                 "occupyOnWayStock": 0,
     *                 "occupyStock": 5,
     *                 "onWayStockFlag": false,
     *                 "skuId": 80000207224,
     *                 "stockFlag": true
     *             },
     *             {
     *                 "occupyOnWayStock": 8,
     *                 "occupyStock": 0,
     *                 "onWayStockFlag": false,
     *                 "skuId": 80000206283,
     *                 "stockFlag": false
     *             }
     *         ]
     *     },
     *     "message": "success",
     *     "success": true
     * }
     */
    @Validated(StockValidGroup.order.class)
    DataResponse<StockOccupyResDTO> occupyRelease(StockManageReqDTO req);

    /**
     * 在途库存退回并返回操作结果。
     *
     * @param req 库存回退请求对象，包含调拨的商品信息，仓库信息。
     * @return 调拨操作的结果，true表示成功，false表示失败。
     */
    @Validated(StockValidGroup.order.class)
    DataResponse<Boolean> transitStockReturn(StockManageReqDTO req);

    /**
     * 在途库存转现货库存
     *
     * @param req 在途库存转现货对象，包含转现货的库存数量和仓信息
     * @return 转现货操作的结果，true表示成功，false表示失败。
     */
    @Validated(StockValidGroup.order.class)
    DataResponse<Boolean> transitStockToStock(StockManageReqDTO req);

    /**
     * 新库存预占
     */
    @Validated(StockValidGroup.order.class)
    DataResponse<StockOccupyResDTO> occupyStock(StockManageReqDTO req);

    /**
     * 预占扣减(提单支付后的正向流程)
     */
    @Validated(StockValidGroup.order.class)
    DataResponse<Boolean> releaseStock(StockManageReqDTO req);

    /**
     * 新预占回退
     */
    @Validated(StockValidGroup.order.class)
    DataResponse<Boolean> occupyStockReturn(StockManageReqDTO req);

    /**
     * 新现货回退
     */
    @Validated(StockValidGroup.order.class)
    DataResponse<Boolean> stockNewReturn(StockManageReqDTO req);

    /**
     * 1、订单拆单时，将父单预占流水拆分到不同子单，
     * 2、校验子单是否已有预占流水，无记录子单预占流水，有返回已成功拆分
     **/
    @Validated(StockValidGroup.order.class)
    DataResponse<Boolean> splitStock(StockSplitReqDTO req);

    /**
     * 将拆采购单或者预报备货单的父单流水拆分到子单上，后续子采购单取消回退逻辑使用
     **/
    @Validated(StockValidGroup.order.class)
    DataResponse<Boolean> splitPurchaseOrderStock(StockSplitReqDTO req);

}
