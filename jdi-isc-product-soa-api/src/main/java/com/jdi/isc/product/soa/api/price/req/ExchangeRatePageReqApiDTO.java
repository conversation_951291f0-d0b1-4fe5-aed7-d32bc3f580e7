package com.jdi.isc.product.soa.api.price.req;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import lombok.Data;

/**
 * @Description: 汇率表对象
 * @Author: zhaokun51
 * @Date: 2025/03/04 15:30
 **/

@Data
public class ExchangeRatePageReqApiDTO extends BasePageReqDTO {

    /**
     * 货币代码 ISO_4217 三字母代码
     */
    private String sourceCurrencyCode;

    /**
     * 货币代码，ISO_4217 三字母代码
     */
    private String targetCurrencyCode;
}
