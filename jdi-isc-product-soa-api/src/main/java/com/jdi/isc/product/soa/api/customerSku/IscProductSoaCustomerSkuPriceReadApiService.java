package com.jdi.isc.product.soa.api.customerSku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.agreementPrice.req.CountryAgreementPriceWarningPageReqDTO;
import com.jdi.isc.product.soa.api.agreementPrice.res.CountryAgreementPriceWarningDTO;
import com.jdi.isc.product.soa.api.customerSku.req.*;
import com.jdi.isc.product.soa.api.customerSku.res.*;

import java.util.List;

/**
 * sku定制化价格
 * <AUTHOR>
 * @date 2024/11/01
 */
public interface IscProductSoaCustomerSkuPriceReadApiService {


    /**
     * 分页查询客户 SKU 价格草稿。
     * @param input 查询条件，包括分页信息和筛选条件。
     * @return 分页结果，包含当前页数据和分页信息。
     */
    DataResponse<PageInfo<CustomerSkuPriceDraftApiDTO>> page(CustomerSkuPriceDraftReqApiDTO input);


    /**
     * 获取审核状态数量。
     * @return 审核状态数量列表。
     */
    DataResponse<List<CustomerSkuPriceAuditNumApiDTO>> auditStatusNum(CustomerSkuPriceDraftReqApiDTO input);

    /**
     * 获取待审批需要发的邮箱。
     * @return 审核状态数量列表。
     */
    DataResponse<List<CustomerSkuEmailDTO>> waitingAudit();

    /**
     * 获取利润率计算结果。
     * @param input 利润率计算请求参数，包括必要的计算数据。
     * @return 包含利润率计算结果的 DataResponse 对象。
     */
    DataResponse<SalePriceCalculateResDTO> getSalePrice(SalePriceCalculateReqDTO input);

    /**
     * joySky待审批列表。
     * @return joySky。
     */
    DataResponse<PageInfo<CustomerSkuPriceAuditApiDTO>> approvePage(CustomerSkuPriceAuditReqApiDTO input);

    /**
     * 价格预警列表。
     * @return 列表。
     */
    DataResponse<PageInfo<CustomerSkuPriceWarningDTO>> warningPage(CustomerSkuPriceWarningPageReqDTO input);


    /**
     * 查询所有客户SKU价格。
     * @return 列表。
     */
    DataResponse<List<CustomerSkuPriceDraftExportApiDTO>> queryCustomerSkuDraft(CustomerSkuPriceDraftReqApiDTO input);

    /**
     * 根据客户ID和SKU ID列表查询价格预警信息
     * @param input 包含客户ID和SKU ID列表的分页查询请求对象
     * @return 包含价格预警信息列表的数据响应对象
     */
    DataResponse<List<CustomerSkuPriceWarningDTO>> listWarningsByApproveIds(CustomerSkuPriceWarningPageReqDTO input);
}
