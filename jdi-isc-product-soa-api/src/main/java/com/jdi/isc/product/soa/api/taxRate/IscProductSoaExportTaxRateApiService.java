package com.jdi.isc.product.soa.api.taxRate;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.taxRate.req.ExportTaxRatePageApiDTO;
import com.jdi.isc.product.soa.api.taxRate.req.ExportTaxRateReqDTO;
import com.jdi.isc.product.soa.api.taxRate.req.GetExportTaxRateDTO;
import com.jdi.isc.product.soa.api.taxRate.res.ExportTaxRateRes;
import com.jdi.isc.product.soa.api.validation.ExportTaxRateValidateGroup;
import org.springframework.validation.annotation.Validated;

/**
 * @Description: 中国出口税率信息api服务
 * @Author: zhaojianguo21
 * @Date: 2024/11/25 20:58
 **/

public interface IscProductSoaExportTaxRateApiService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<ExportTaxRatePageApiDTO.Response>> pageSearch(ExportTaxRatePageApiDTO.Request input);


    /**
     * 获取出口税率信息
     * @param input GetExportTaxRateDTO 对象，包含获取出口税率所需的参数
     * @return DataResponse<ExportTaxRateRes> 对象，包含出口税率信息的响应数据
     */
    DataResponse<ExportTaxRateRes> getExportTaxRate(GetExportTaxRateDTO input);

    /**
     * 保存、更新
     * @param input 提交入参
     * @return 结果
     */
    @Validated(ExportTaxRateValidateGroup.save.class)
    DataResponse<Boolean> saveOrUpdate(ExportTaxRateReqDTO input);
}
