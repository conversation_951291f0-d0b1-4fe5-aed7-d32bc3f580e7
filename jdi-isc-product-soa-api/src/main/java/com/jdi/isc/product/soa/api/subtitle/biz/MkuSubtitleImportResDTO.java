package com.jdi.isc.product.soa.api.subtitle.biz;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * The type Mku subtitle import excel dto.
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MkuSubtitleImportResDTO extends BaseReqDTO {

    private List<Item> items;

    public MkuSubtitleImportResDTO() {
        this.items = new ArrayList<>();
    }

    public MkuSubtitleImportResDTO(List<Item> items) {
        this.items = items;
    }

    @Data
    public static class Item implements Serializable {
        @NotEmpty(message = "MKUID为空")
        private String mkuId;

        /**
         * 商品巴葡短标题。
         */
        @NotEmpty(message = "短标题为空")
        private String mkuSubtitle;

        /**
         * 表示是否操作成功。
         */
        private boolean valid ;

        protected List<String> errorMsgs = new ArrayList<>();

        /**
         * 存储操作结果的字符串。
         */
        private String result;

        /**
         * Success.
         */
        public void success() {
            this.valid = true;
            this.result = "success";
        }

        /**
         * Failed.
         *
         * @param result the result
         */
        public void failed(String result) {
            this.valid = false;
            this.result = result;
        }

        public String getResult() {
            if (this.valid) {
                return "success";
            }
            if (CollectionUtils.isEmpty(this.errorMsgs)) {
                return "success";
            }

            return String.join(",", this.errorMsgs);
        }
    }


}