package com.jdi.isc.product.soa.api.sku.req;


import com.jdi.isc.product.soa.api.sku.base.CustomsApiDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description：UpdateCustomsApiDTO
 * @Date 2025-04-28
 */
@Data
public class UpdateCustomsApiDTO extends CustomsApiDTO {
    /**
     * SKU ID
     */
    @NotNull(message = "skuId 不能为空")
    private String skuId;
}
