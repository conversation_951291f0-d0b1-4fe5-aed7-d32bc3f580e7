package com.jdi.isc.product.soa.api.customerMku.req;

import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class MkuPoolVerificationReqDTO extends BasicApiDTO {
    /**
     * 成功验证的MKU ID列表。
     */
    @Size(max = 50, message = "单次查询操作不能输入超过50个国际mkuId")
    @NotEmpty(message = "国际mkuId不能为空")
    private List<Long> successMkuIdList;

    /**
     * 验证失败的MKU ID列表。
     */
    private List<Long> failMkuIdList;

    /**
     * 验证类型，用于区分不同的验证场景。
     */
    private Integer checkType;

    /**
     * 客户代码。
     */
    private String clientCode;

    /**
     * 国家码
     */
    private String countryCode;
}
