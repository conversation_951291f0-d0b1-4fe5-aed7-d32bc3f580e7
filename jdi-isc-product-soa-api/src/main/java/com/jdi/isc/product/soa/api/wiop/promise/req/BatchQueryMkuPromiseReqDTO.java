package com.jdi.isc.product.soa.api.wiop.promise.req;


import com.jd.tp.common.masterdata.UniformBizInfo;
import com.jdi.isc.product.soa.api.validation.PromiseQueryValidateGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description：BatchQueryMkuPromiseReqDTO
 * @Date 2025-01-08
 */
@Data
public class BatchQueryMkuPromiseReqDTO {
    /**
     * 国际化租户信息
     */
    @NotNull(message = "uniformBizInfo不能为空",groups = {PromiseQueryValidateGroup.queryPromise.class})
    private UniformBizInfo uniformBizInfo;

    /**
     * 客户编码信息
     */
    @NotNull(message = "clientCode不能为空",groups = {PromiseQueryValidateGroup.queryPromise.class})
    private String clientCode;
    /**
     * MKU ID信息
     */
    @NotNull(message = "mkuId不能为空",groups = {PromiseQueryValidateGroup.queryPromise.class})
    private List<MkuPromiseReqDTO> mkuPromiseReqDTOList;
}
