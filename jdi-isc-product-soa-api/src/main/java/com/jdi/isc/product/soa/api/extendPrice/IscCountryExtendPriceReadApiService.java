package com.jdi.isc.product.soa.api.extendPrice;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.extendPrice.req.CountryExtendPricePageReqDTO;
import com.jdi.isc.product.soa.api.extendPrice.req.CountryExtendPriceReqDTO;
import com.jdi.isc.product.soa.api.extendPrice.res.CountryExtendPriceDTO;
import com.jdi.isc.product.soa.api.extendPrice.res.CountryExtendPricePageApiDTO;

/**
 * @Description: 国家扩展（参考）价api服务
 * @Author: wangpeng965
 * @Date: 2025/03/04 16:28
 **/

public interface IscCountryExtendPriceReadApiService {

    /**
     * 分页查询
     * @param dto 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<CountryExtendPricePageApiDTO>> pageSearch(CountryExtendPricePageReqDTO dto);


    /**
     * 获取国家协议价格详情
     * @param dto 国家协议价格请求参数
     * @return 国家协议价格响应数据
     */
    DataResponse<CountryExtendPriceDTO> detail(CountryExtendPriceReqDTO dto);
}
