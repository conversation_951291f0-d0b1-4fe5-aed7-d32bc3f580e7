package com.jdi.isc.product.soa.api.stock.req;

import com.jdi.common.domain.rpc.bean.Client;
import com.jdi.isc.product.soa.api.validation.StockValidGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 库存管理操作实体
 * <AUTHOR>
 * @date 2024/6/3
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StockManageReqDTO extends Client {

    /** sku信息*/
    @NotNull(message = "stockItem库存请求信息不能为空",groups = {StockValidGroup.order.class,StockValidGroup.saveOrUpdate.class,StockValidGroup.query.class})
    @Valid
    private List<StockItemManageReqDTO> stockItem;

    /** 订单Id*/
    @NotNull(message = "订单Id不能为空",groups = {StockValidGroup.order.class})
    private String orderId;

    /** 单据号,订单库存预占、预占释放、现货回退 操作时bizNo=orderId */
    @NotNull(message = "单据号不能为空",groups = {StockValidGroup.order.class,StockValidGroup.saveOrUpdate.class})
    private String bizNo;

    /** 库存更新模式：0或不传 覆写模式<运营、商家维护库存>  1追加模式<备货库存> */
    private Integer stockWriteType;

    /** 客户国家编码 为空或当前国家无绑定备货仓查厂直库存；有备货仓查备货库存*/
    // @NotNull(message = "countryCode不能为空",groups = {StockValidGroup.order.class})
    private String countryCode;
    /**
     * 现货优先  1：现货优先 0：非现货优先
     */
    private Integer stockPriority = 0;
}
