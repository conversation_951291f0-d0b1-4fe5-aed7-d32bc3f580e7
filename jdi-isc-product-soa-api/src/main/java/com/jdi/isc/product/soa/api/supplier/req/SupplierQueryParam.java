package com.jdi.isc.product.soa.api.supplier.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import com.jdi.isc.product.soa.api.common.BasicDTO;
import com.jdi.isc.product.soa.api.common.BasicReqDTO;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/8/30 8:45 上午
 */

@Data
public class SupplierQueryParam extends BasicReqDTO {

    /**
     * 供应商代码
     */
    private String supplierCode;

    /**
     * 供应商代码集合，用于存储多个供应商的代码
     */
    private Set<String> supplierCodes;
}
