package com.jdi.isc.product.soa.api.taxRate.res;


import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @description：供侧税率信息
 * @Date 2024-12-17
 */
@Data
public class PurchaseSkuTaxResDTO implements Serializable {
    private static final long serialVersionUID = 4650233595896357016L;
    /**
     * SKU ID
     */
    private Long skuId;
    /**供侧海关编码*/
    private String hsCode;
    /**巴西特殊NCM海关编码**/
    private String ncm;
    /**综合税率**/
    private String complexTaxRate;
    /**税率种类和税率值映射 TaxTypeEnum**/
    private Map<String,String> taxRateMap;
    /**巴西CST注册号 **/
    private String cst;
}
