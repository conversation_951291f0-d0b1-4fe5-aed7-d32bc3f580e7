package com.jdi.isc.product.soa.api.supplier;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.supplier.req.SupplierQueryParam;
import com.jdi.isc.product.soa.api.supplier.res.*;

import java.util.List;

/**
 * 供应商读服务
 */
public interface SupplierReadApiService {


    /**
     * 查询供应商银行信息。
     * @param queryParam 供应商查询参数。
     * @return 包含供应商银行信息的 DataResponse 对象。
     */
    DataResponse<SupplierInfoRes> querySupplierBankInfo(SupplierQueryParam queryParam);


    /**
     * 根据供应商来源国家代码查询供应商基本信息。
     * @param queryParam 供应商查询参数，包括来源国家代码等。
     * @return 供应商基本信息的数据响应对象。
     */
    DataResponse<List<SupplierBaseInfoRes>> querySupplierBySourceCountryCode(SupplierQueryParam queryParam);


    /**
     * 根据供应商代码查询供应商账户信息。
     * @param queryParam 查询参数对象，包含供应商代码等信息。
     * @return 包含查询结果的 DataResponse 对象。
     */
    DataResponse<List<SupplierAccountRes>> queryAccountBySupplierCode(SupplierQueryParam queryParam);


    /**
     * 根据供应商查询条件查询合同信息。
     * @param queryParam 供应商查询参数对象。
     * @return 包含供应商合同信息的 DataResponse 对象。
     */
    DataResponse<List<SupplierContractRes>> queryContractInfo(SupplierQueryParam queryParam);


    /**
     * 根据供应商来源国家代码查询供应商基本信息。
     * @param queryParam 供应商查询参数，包括来源国家代码等。
     * @return 供应商基本信息的数据响应对象。
     */
    DataResponse<List<SupplierBaseInfoRes>> querySupplierBaseInfo(SupplierQueryParam queryParam);

    /**
     * 根据供应商来源国家代码查询供应商基本信息。
     * @param queryParam 供应商查询参数，包括来源国家代码等。
     * @return 供应商基本信息的数据响应对象。
     */
    DataResponse<List<JdSupplierBaseInfoRes>> queryJdSupplierInfo(SupplierQueryParam queryParam);


}
