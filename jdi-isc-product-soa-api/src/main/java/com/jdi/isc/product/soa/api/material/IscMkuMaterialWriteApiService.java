package com.jdi.isc.product.soa.api.material;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.BasicDTO;
import com.jdi.isc.product.soa.api.material.req.MkuMaterialDeleteExcelReqDTO;
import com.jdi.isc.product.soa.api.material.req.MkuMaterialExcelReqDTO;
import com.jdi.isc.product.soa.api.material.res.MkuMaterialApiDTO;

import java.util.List;
import java.util.Map;

public interface IscMkuMaterialWriteApiService {

    /**
     * 增加Mku物料
     * */
    DataResponse<Map<String,String>> create(MkuMaterialApiDTO dto);

    /**
     * 更新Mku物料
     * */
    DataResponse<Map<String,String>> update(MkuMaterialApiDTO dto);

    /**
     * 更新Mku物料
     * */
    DataResponse<Boolean> delete(MkuMaterialApiDTO dto);

    /**
     * 批量删除Mku物料集合
     * */
    DataResponse<Boolean> batchDelete(List<Long> ids,MkuMaterialApiDTO dto);

    /**
     * 批量增加Mku物料集合
     * */
    DataResponse<Boolean> batchCreate(List<MkuMaterialApiDTO> dtoList);

    /**
     *  批量修改Mku物料集合
     * */
    DataResponse<Long> batchUpdate(List<MkuMaterialApiDTO> dtoList);

    /**
     * @function 导入MkuMaterial Excel数据
     * @param repDtoList 包含MkuMaterialExcelReqDTO对象的列表，用于导入的数据
     * @returns DataResponse对象，包含导入操作的结果和可能的错误信息
     */
    DataResponse<List<MkuMaterialApiDTO> > importMkuMaterialExcel(List<MkuMaterialExcelReqDTO> repDtoList);

    /**
     * @function 导入MkuMaterialDeleteExcel的方法
     * @param reqDtoList 包含MkuMaterialDeleteExcelReqDTO对象的列表
     * @returns 包含MkuMaterialDeleteExcelReqDTO对象的列表
     */
    DataResponse<List<MkuMaterialApiDTO>> importMkuMaterialDeleteExcel(List<MkuMaterialDeleteExcelReqDTO> reqDtoList);


    DataResponse<Boolean> dropDelete(Long id);
}
