package com.jdi.isc.product.soa.api.taxRate.req;

import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @Description: 跨境税率关系 实体类
 * @Author: cheng<PERSON><PERSON>
 * @Date: 2024/12/11 10:47
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class HscodeTaxRateRelationDetailReqApiDTO extends BasicDTO {

    /**
     * SKUID
     */
    @NotNull(message = "SKUID不能为空")
    private Long skuId;

    /**
     * 中国海关编码
     */
    private String cnHsCode;

    /**
     * 他国海关编码
     */
    private String otherHsCode;

    /**
     * 国家码
     */
    @NotNull(message = "国家码不能为空")
    private String countryCode;

}