package com.jdi.isc.product.soa.api.warehouse;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.validation.WarehouseValidGroup;
import com.jdi.isc.product.soa.api.warehouse.base.WarehouseSkuDTO;
import com.jdi.isc.product.soa.api.warehouse.req.*;
import com.jdi.isc.product.soa.api.warehouse.res.*;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 仓库查询服务
 * <AUTHOR>
 * @date 2024/7/22
 **/
public interface IscWarehouseReadApiService {

    /**
     * @function 分页查询仓库信息
     * @param pageReqDTO - 分页查询的请求参数，包含分页参数及可能的筛选条件
     * @returns 返回包含分页信息的仓库数据传输对象列表的响应结构
     */
    DataResponse<PageInfo<WarehouseResDTO>> searchWarehouse(WarehousePageReqDTO pageReqDTO);

    /**
     * @function 根据请求参数获取仓库详细信息
     * @param reqDTO - 包含获取仓库详情所需请求参数的数据传输对象
     * @returns 返回包含仓库详细信息的数据响应对象
     */
    @Validated(WarehouseValidGroup.detail.class)
    DataResponse<WarehouseDetailResDTO> getWarehouseDetail(WarehouseGetReqDTO reqDTO);

    /**
     * @function 根据仓库ID查询仓库中的SKU信息(仅备货品)
     * @param reqDTO - 包含仓库ID的请求数据传输对象
     * @returns 返回包含仓库内SKU列表信息的数据响应对象
     */
    @Validated(WarehouseValidGroup.querySku.class)
    DataResponse<PageInfo<WarehouseSkuResDTO>> querySkusByCondition(WarehouseSkuPageReqDTO reqDTO);


    /**
     * @function 根据请求批量获取仓库信息
     * @param reqDTO - 批量获取仓库信息的请求参数对象
     * @returns 返回包含仓库信息列表的数据响应对象
     */
    DataResponse<List<WarehouseResDTO>> queryWarehouseByCondition(WarehouseBatchGetReqDTO reqDTO);

    /**
     * 查询 SKU 与仓库关系的映射
     * @param reqDTO 包含查询条件的请求数据传输对象
     * @return SKU 与仓库关系的嵌套映射，其中键为 SKU，值为另一个映射，该映射的键为仓库标识，值为仓库 SKU 资源数据传输对象
     */
    DataResponse<Map<String, Map<String, WarehouseSkuDTO>>> querySkuWarehouseRelationMap(QuerySkuRelationReqDTO reqDTO);


    /**
     * 根据条件批量获取仓库商品信息。
     */
    DataResponse<List<WarehouseSkuResDTO>> batchSkusByCondition(WarehouseBatchSkuReqDTO batchSkuVo);

    /**
     * @function 根据仓库ID查询仓库中的寄售SKU的信息
     * @param reqDTO - 包含仓库ID的请求数据传输对象
     * @returns 返回包含仓库内SKU列表信息的数据响应对象
     */
    @Validated(WarehouseValidGroup.querySku.class)
    DataResponse<PageInfo<WarehouseConsignSkuResDTO>> queryConsignmentSkusByCondition(WarehouseSkuPageReqDTO reqDTO);


    /**
     * 验证仓库SKU解绑是否满足条件
     * @param warehouseSkuUnBindReqs 需要验证的仓库SKU解绑请求集合
     * @return 包含验证结果的响应对象，其中包含解绑响应DTO列表
     */
    @Validated(WarehouseValidGroup.validateUnbind.class)
    DataResponse<List<WarehouseSkuUnBindResDTO>> validateWarehouseSkuUnbindRelation(Set<WarehouseSkuUnBindItemReqDTO> warehouseSkuUnBindReqs);

    /**
     * 根据条件查询仓库 SKU 仓报价信息。
     * @param reqDTO 查询请求对象，包含查询条件。
     * @return DataResponse 对象，包含查询结果的 List<WarehouseSkuPriceResDTO>。
     */
    DataResponse<List<WarehouseSkuPriceResDTO>> queryWarehouseSkuPricesByCondition(WarehouseSkuPriceBatchReqDTO reqDTO);

}
