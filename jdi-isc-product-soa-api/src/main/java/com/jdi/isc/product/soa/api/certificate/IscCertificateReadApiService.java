package com.jdi.isc.product.soa.api.certificate;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.certificate.req.GlobalCertificateQueryReqDTO;
import com.jdi.isc.product.soa.api.certificate.res.GroupCertificateDTO;

import java.util.List;

/**
 * 查询属性列表
 * <AUTHOR>
 * @date 2025/04/23
 **/
public interface IscCertificateReadApiService {

    /**
     * 根据类目ID查询资质列表
     */
    DataResponse<List<GroupCertificateDTO>> queryGroupGlobalCertificateByCatId(GlobalCertificateQueryReqDTO input);

}
