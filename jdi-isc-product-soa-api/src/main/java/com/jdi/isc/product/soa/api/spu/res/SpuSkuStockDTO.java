package com.jdi.isc.product.soa.api.spu.res;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @description sku的库存、mkuId、未税销售价、含税销售价
 * @date 2024/10/16
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SpuSkuStockDTO {
    /**
     * SKU的唯一标识符
     */
    private Long SkuId;

    /**
     * MKU的唯一标识符
     */
    private Long mkuId;

    /**
     * SKU的当前库存数量
     */
    private String stock;

    /**
     * SKU的供货价格
     */
    private BigDecimal purchasePrice;

    /**
     * SKU的含税供货价格。
     */
    private BigDecimal taxPurchasePrice;
    /**
     * 未税销售价
     */
    private BigDecimal salePrice;
    /**
     * 含税销售价
     */
    private BigDecimal taxSalePrice;

    /**
     * 销售属性的键值对映射，用于存储SKU的销售属性信息。
     */
    private Map<String ,String> saleAttributeMap;

    /**
     * 供应商SKU的唯一标识符。
     */
    private String vendorSkuId;

    /**
     * SPU的唯一标识符。
     */
    private Long spuId;
    /**SKU草稿KEY**/
    private String skuKey;
    /** 采购价体系优化 2025-04-14 sunlei61 增加国家协议价、国家成本价、协议价利润率**/
    /**
     * 协议价
     */
    private BigDecimal agreementPrice;

    /**
     * 国家成本价
     */
    private BigDecimal countryCostPrice;
    /**
     * 协议价利润率
     */
    private BigDecimal agreementProfitRate;
    /**
     * 国家成本价币种
     */
    private String costCurrency;

    /**
     * upc编码
     */
    private String upcCode;
    /**
     * 毛重(g)
     */
    private String weight;

    /**
     * 长(mm)
     */
    private String length;

    /**
     * 宽(mm)
     */
    private String width;

    /**
     * 高(mm)
     */
    private String height;

    /**
     * 发货时效
     */
    private String productionCycle;

    /**
     * 最小下订数量
     */
    private Integer moq;

    /**
     * 海关编码
     */
    private String hsCode;
    /**
     * SKU上下架状态
     */
    private Integer skuStatus;
    /**
     * 主站状态联动启用状态  0：未启用 1：启用
     */
    private Integer mainSiteSynSwitch;
    /**
     * 下架原因
     */
    private String downReason;
}
