package com.jdi.isc.product.soa.api.price.biz;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * @Description: 价格监控比较查询对象
 * @Author: zhaojianguo21
 * @Date: 2024/12/11 17:13
 **/

@Data
public class SkuPurchasePriceCompareApiDTO implements Serializable {
    
    @Data
    @NoArgsConstructor
    public static class Request implements Serializable {
        private Set<Long> spuIds;
    }

    @Data
    @NoArgsConstructor
    public static class Response implements Serializable{

    }

}