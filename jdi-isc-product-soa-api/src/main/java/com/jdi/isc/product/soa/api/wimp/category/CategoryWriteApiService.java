package com.jdi.isc.product.soa.api.wimp.category;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.wimp.category.biz.CategoryLangBatchSaveUpdateDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.CategoryBuyerRelationSyncReqApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.res.CategoryBuyerRelationSyncResApiDTO;

import java.util.List;
import java.util.Map;

/**
 * 类目写服务
 * <AUTHOR>
 * @date 2024-11-26
 **/

public interface CategoryWriteApiService {

    /**
     * 批量保存更新类目名称多语言
     * @param input
     * @return
     */
    DataResponse<CategoryLangBatchSaveUpdateDTO.Response> saveUpdateCategoryLangBatch(CategoryLangBatchSaveUpdateDTO.Request input);


    /**
     * 同步采购Erp
     */
    DataResponse<CategoryBuyerRelationSyncResApiDTO> syncBuyer(CategoryBuyerRelationSyncReqApiDTO input);

}
