package com.jdi.isc.product.soa.api.spu.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/09/27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SpuAuditTaxApproveDTO extends BaseReqDTO {

    /**
     * spuIds
     */
    @NotEmpty(message = "spuIds不能为空")
    @Size(max = 100,message = "SPU 个数不能超过100个")
    private List<Long> spuIds;

    /**
     * 审核人
     */
    private String auditErp;

    /**
     * 原因
     */
    private String reason;

    /**
     * countryType
     */
    @NotNull(message = "国家类型不能为空")
    private Integer countryType;

}
