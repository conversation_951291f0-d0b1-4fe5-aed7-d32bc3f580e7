package com.jdi.isc.product.soa.api.price.biz;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 价格监控分页查询对象
 * @Author: zhaojianguo21
 * @Date: 2024/12/11 17:13
 **/

@Data
public class SkuPriceMonitorSpuPageApiDTO implements Serializable {


    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Request extends BasePageReqDTO implements Serializable {

    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BasicDTO implements Serializable{
        /**
         * SPUID
         */
        private Long spuId;

        /**
         * 状态，1-上柜，2待审核
         */
        private Integer spuStatus;

        /**
         * 审核状态，1:审核通过，2:驳回 3:待审核
         */
        private Integer auditStatus;

        /**
         * 数据来源系统 VC
         */
        private String systemCode;

        /**
         * 发品人 1:供应商 2:采销
         */
        private Integer identityFlag;

        /**
         * 货源国、国家站
         */
        private String sourceCountryCode;

        /**
         * 末级类目ID
         */
        private Long catId;

    }

}