package com.jdi.isc.product.soa.api.price.req;

import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 汇率表对象
 * @Author: zhaokun51
 * @Date: 2025/03/04 15:30
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExchangeRateChangeDTO {

    /**
     * 货币代码 ISO_4217 三字母代码
     */
    private String sourceCurrencyCode;

    /**
     * 货币代码，ISO_4217 三字母代码
     */
    private String targetCurrencyCode;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 变更来源
     */
    private Integer dataStatusSource;
}
