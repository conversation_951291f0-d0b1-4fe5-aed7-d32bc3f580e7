package com.jdi.isc.product.soa.api.price;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.price.res.ProfitRateApiDTO;

/**
 * @Description: 利润率阈值表api服务
 * @Author: zhaokun51
 * @Date: 2024/10/29 21:46
 **/

public interface IscProductSoaProfitRateWriteApiService {

    /**
     * 保存或更新利率信息。
     * @param input ProfitRateApiDTO 对象，包含需要保存或更新的利率信息。
     * @return DataResponse<Boolean> 对象，表示操作是否成功。
     */
    DataResponse<Boolean> saveOrUpdate(ProfitRateApiDTO input);

    /**
     * 根据ID删除数据
     * @param id 要删除的数据的ID
     * @return 删除操作的结果
     */
    DataResponse<Boolean> deleteById(Long id);
}
