package com.jdi.isc.product.soa.api.category.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/3/20 3:42 下午
 */
@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class CategoryTreeDTO {

    /**
     * 类目ID
     */
    private Long catId;

    /**
     * 类目名称
     */
    private String catName;


    /**
     * 类目级数
     */
    private Integer level;


    /**
     * 子类目
     */
    private List<CategoryTreeDTO> child;


    /**
     * 子类目ids
     */
    private Set<Long> childIds;

    /**
     * 是否选中状态
     */
    private boolean selected;

    /**
     * 状态 0:未选中，1:半选中，3:全选中
     */
    private Integer status;

    /**
     * 父类目ID
     */
    private Long parentCatId;

    /**
     * 版本信息
     */
    private String version;

    /**
     * 用于标识类目树节点的唯一键
     */
    private String key;

    /**
     * 标题信息
     */
    private String title;
    /**
     * 是否为叶子节点
     */
    private Boolean isLeaf;

    /**
     * 子类目
     */
    private List<CategoryTreeDTO> children;
}
