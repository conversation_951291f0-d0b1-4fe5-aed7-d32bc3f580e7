package com.jdi.isc.product.soa.api.supplier.res;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/8/30 9:19 上午
 */
@Data
public class SupplierContractRes {

    /**
     * 供应商Code
     */
    private String supplierCode;

    /**
     * 合同编号
     */
    private String contractNumber;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 京东侧签约主体ID
     */
    private Long orgId;

    /**
     * 京东侧签约主体名称
     */
    private String orgName;

    /**
     * 合同类型
     */
    private String type;

    /**
     * 合同生效日期
     */
    private Long effectiveDate;

    /**
     * 合同失效日期
     */
    private Long expirationDate;

    /**
     * 合同状态
     */
    private String status;

    /**
     * 供应商币种
     */
    private String currency;

    /**
     * 结算方式
     */
    private Integer settlementMethod;


}
