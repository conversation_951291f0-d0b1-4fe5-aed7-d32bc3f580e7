package com.jdi.isc.product.soa.api.wiop.promise;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.validation.PromiseQueryValidateGroup;
import com.jdi.isc.product.soa.api.wiop.promise.req.BatchQueryMkuPromiseReqDTO;
import com.jdi.isc.product.soa.api.wiop.promise.res.MkuPromiseResDTO;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * <AUTHOR>
 * @description：发货时效查询接口
 * @Date 2025-01-08
 */
public interface IscPromiseReadApiService {

    /**
     * 查询多个MKU的承诺信息。
     * @param reqDTO 批量查询MKU承诺请求对象。
     * @return 承诺信息的映射，key为MKU的ID，value为对应的承诺信息。
     */
    @Validated(PromiseQueryValidateGroup.queryPromise.class)
    DataResponse<Map<Long, MkuPromiseResDTO>> queryMkuPromise(BatchQueryMkuPromiseReqDTO reqDTO);
}
