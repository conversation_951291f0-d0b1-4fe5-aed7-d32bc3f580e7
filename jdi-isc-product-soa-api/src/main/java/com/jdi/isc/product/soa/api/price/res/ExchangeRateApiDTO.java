package com.jdi.isc.product.soa.api.price.res;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 汇率表对象
 * @Author: zhaokun51
 * @Date: 2025/03/04 15:30
 **/

@Data
public class ExchangeRateApiDTO extends BasePageReqDTO {

    /**
     * 货币代码 ISO_4217 三字母代码
     */
    private String sourceCurrencyCode;

    /**
     * 货币代码，ISO_4217 三字母代码
     */
    private String targetCurrencyCode;

    /**
     * 货币代码，ISO_4217 三字母代码
     */
    private BigDecimal exchangeRate;

    /** 源币种名称 */
    private String sourceCurrencyName;

    /** 目标币种名称 */
    private String targetCurrencyName;

    /** 更新人 */
    private String updater;

    /** 更新时间 */
    private Long updateTime;
}
