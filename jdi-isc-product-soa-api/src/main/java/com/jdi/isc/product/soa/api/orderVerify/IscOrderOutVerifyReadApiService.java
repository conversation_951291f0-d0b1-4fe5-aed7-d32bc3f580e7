package com.jdi.isc.product.soa.api.orderVerify;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.orderVerify.req.OrderOutVerifyPageReqDTO;
import com.jdi.isc.product.soa.api.orderVerify.res.OrderOutVerifyApiDTO;

public interface IscOrderOutVerifyReadApiService {

    DataResponse<PageInfo<OrderOutVerifyApiDTO>> pageOrderOutVerifyInfo(OrderOutVerifyPageReqDTO dto);
}
