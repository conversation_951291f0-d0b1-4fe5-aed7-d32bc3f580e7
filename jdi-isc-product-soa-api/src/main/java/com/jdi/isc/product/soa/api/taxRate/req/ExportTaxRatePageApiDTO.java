package com.jdi.isc.product.soa.api.taxRate.req;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 中国出口税率信息分页查询对象
 * @Author: zhaojianguo21
 * @Date: 2024/11/25 20:58
 **/

@Data
public class ExportTaxRatePageApiDTO implements Serializable {


    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Request extends BasePageReqDTO implements Serializable {
        /**
         * 国家码
         */
        private String countryCode;
        /**
         * 海关编码
         */
        private String hsCode;
    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BasicDTO implements Serializable{
        /**
         * 国家码
         */
        private String countryCode;

        /**
         * 海关编码
         */
        private String hsCode;

        /**
         * 出口征税率
         */
        private BigDecimal exportTaxRate;

        /**
         * 出口退税率
         */
        private BigDecimal exportRebateRate;

        /**
         * 海关控制条件
         */
        private String customsCondition;

        /**
         * 检验检疫类别
         */
        private String quarantineCat;
        /**
         * 国家名称
         */
        private String countryName;
    }

}
