package com.jdi.isc.product.soa.api.specialAttr;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.specialAttr.biz.*;
import com.jdi.isc.product.soa.api.specialAttr.res.SpecialAttrAttributeDTO;
import com.jdi.isc.product.soa.api.specialAttr.res.SpecialAttrDTO;

import java.util.List;
import java.util.Map;

/**
 * @Description: 商品特殊属性api服务
 * @Author: zhangjin176
 * @Date: 2024/12/11 09:58
 **/
public interface SpecialAttrApiService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<SpecialAttrDTO>> pageSearch(SpecialAttrPageApiDTO.Request input);

    /**
     * 根据输入的 SpecialAttrReq 对象搜索并返回匹配的 SpecialAttrReq 列表。
     * @param input 搜索条件的 SpecialAttrReq 对象。
     * @return 包含搜索结果的 DataResponse 对象，其中泛型参数为 List<SpecialAttrReq>。
     */
    DataResponse<List<SpecialAttrDTO>> search(SpecialAttrReq input);


    /**
     * 保存或更新特殊属性Excel明细请求。
     * @param input SpecialAttrExcelDetailReq 对象，包含要保存或更新的特殊属性Excel明细信息。
     * @return DataResponse对象，包含保存或更新后的特殊属性列表。
     */
    DataResponse<String> saveOrUpdate(SpecialAttrExcelReq input);

    /**
     * 根据特殊属性键请求获取特殊属性值列表
     * @param input SpecialAttrKeyReq对象，包含特殊属性键等信息
     * @return DataResponse对象，包含特殊属性值列表
     */
    DataResponse<List<SpecialAttrAttributeDTO>> specialAttrKey(SpecialAttrKeyReq input);
    /**
     * 根据SpecialAttrValueReq获取特定属性的值列表
     * @param input SpecialAttrValueReq对象，包含查询条件
     * @return DataResponse对象，包含List<SpecialAttrAttributeDTO>类型的结果集
     */
    DataResponse<List<SpecialAttrAttributeDTO>> specialAttrValue(SpecialAttrValueReq input);

    /**
     * 根据 SKU 获取特殊属性信息。
     * @param input SpecialAttrSkuReq 对象，包含要查询的 SKU 信息。
     * @return DataResponse<Map<Long,String>> 对象，包含特殊属性信息的键值对。
     */
    DataResponse<Map<Long,List<String>>> getSpecialAttrBySku(SpecialAttrSkuReq input);


    /**
     * 根据 SKU IDs 查询特殊属性映射。
     * @param input SpecialAttrSkuReq 对象，包含要查询的 SKU IDs。
     * @return DataResponse 对象，包含一个 Map，键为 SKU ID，值为特殊属性KEY和属性值的映射。
     */
    DataResponse<Map<Long,Map<String,String>>> querySpecialAttrMapBySkuIds(SpecialAttrSkuReq input);

    /**
     * 给SKU打特殊属性标
     * @param input SKU特殊属性合并请求数据传输对象
     * @return 包含操作结果的响应对象，其中布尔值表示合并是否成功
     */
    DataResponse<Boolean> mergeSkuSpecialAttr(SkuSpecialAttrMergeReqDTO input);
}