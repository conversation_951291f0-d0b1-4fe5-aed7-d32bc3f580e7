package com.jdi.isc.product.soa.api.specialAttr.biz;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import com.jdi.isc.product.soa.api.specialAttr.res.SpecialAttrDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 商品特殊属性分页查询对象
 * @Author: zhangjin176
 * @Date: 2024/12/11 09:58
 **/

@Data
public class SpecialAttrRelationPageApiDTO implements Serializable {


    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Request extends BasePageReqDTO implements Serializable {
        /**
         * SKU ID列表，用于筛选请求结果。
         */
        private String skuIds;
        /**
         * SPU ID列表，用于筛选请求结果。
         */
        private String spuIds;
        /**
         * MKU ID列表，用于筛选请求结果。
         */
        private String mkuIds;
        /**
         * 国家或地区范围，用于筛选请求结果。
         */
        private String countryScope;
        /**
         * 属性关键字ID，用于筛选请求结果。
         */
        private Integer attributeKeyId;
        /**
         * 属性值ID，用于筛选请求结果。
         */
        private String attributeValueId;

    }

}