package com.jdi.isc.product.soa.api.countryMku.biz;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * @Description: 商品国家池表审批 VO实体类
 * @Author: zhaokun51
 * @Date: 2024/12/05 21:49
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class CountryMkuApproveDTO extends BaseReqDTO {

    /**
     * ids
     */
    private Set<Long> ids;

    /**
     * status
     */
    private Integer approveStatus;

}
