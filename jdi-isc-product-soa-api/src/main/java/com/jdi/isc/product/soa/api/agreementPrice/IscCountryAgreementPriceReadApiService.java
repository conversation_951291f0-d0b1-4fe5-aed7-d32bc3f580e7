package com.jdi.isc.product.soa.api.agreementPrice;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.agreementPrice.req.*;
import com.jdi.isc.product.soa.api.agreementPrice.res.*;

import java.util.List;

/**
 * @Description: 国家协议价api服务
 * @Author: wangpeng965
 * @Date: 2025/03/03 16:45
 **/

public interface IscCountryAgreementPriceReadApiService {


    /**
     * 根据条件分页查询国家协议价格信息。
     * @param dto 查询条件对象，包含国家、协议类型、价格区间等信息。
     * @return 分页后的国家协议价格信息，包括当前页数据和分页信息。
     */
    DataResponse<PageInfo<CountryAgreementPricePageApiDTO>> pageSearch(CountryAgreementPricePageReqDTO dto);

    /**
     * 获取国家协议价格详情
     * @param dto 国家协议价格请求参数
     * @return 国家协议价格响应数据
     */
    DataResponse<CountryAgreementPriceDTO> detail(CountryAgreementPriceReqDTO dto);

    /**
     * 批准国家协议价格页面搜索
     * @param dto 国家协议价格页面请求DTO
     * @return 包含PageInfo和CountryAgreementPricePageApiDTO的DataResponse对象
     */
    DataResponse<PageInfo<CountryAgreementPriceAuditApiDTO>> approvePageSearch(CountryAgreementPriceAuditReqApiDTO dto);

    /**
     * 根据条件分页查询国家协议价格预警信息。
     * @param input 查询条件，包括国家、协议、价格等信息。
     * @return 分页查询结果，包含当前页数据和分页信息。
     */
    DataResponse<PageInfo<CountryAgreementPriceWarningDTO>> warningPageSearch(CountryAgreementPriceWarningPageReqDTO input);

    /**
     * 根据ID列表查询国家协议价格预警信息
     * @param input 包含查询条件的请求参数对象
     * @return 包含国家协议价格预警信息列表的响应对象
     */
    DataResponse<List<CountryAgreementPriceWarningDTO>> listWarningsByApproveIds(CountryAgreementPriceWarningPageReqDTO input);
}
