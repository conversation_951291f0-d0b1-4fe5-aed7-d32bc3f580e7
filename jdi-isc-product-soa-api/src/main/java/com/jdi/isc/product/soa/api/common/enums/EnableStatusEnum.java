package com.jdi.isc.product.soa.api.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/4
 **/
@Getter
@AllArgsConstructor
public enum EnableStatusEnum {
    FAILED_EFFECTIVE(0,"失效"),
    EFFECTIVE(1, "生效"),
    EXPIRED(2, "过期"),
    NOT_EFFECTIVE(3,"未生效"),
    ;


    private Integer code;
    private String desc;

    public static EnableStatusEnum forCode(Integer code) {
        if (code == null) {
            return null;
        }

        EnableStatusEnum[] values = values();
        for (EnableStatusEnum enumObj : values) {
            if (code.equals(enumObj.getCode())) {
                return enumObj;
            }
        }
        return null;
    }
}
