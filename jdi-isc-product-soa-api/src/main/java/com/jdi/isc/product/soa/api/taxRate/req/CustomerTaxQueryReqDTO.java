package com.jdi.isc.product.soa.api.taxRate.req;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 客户商品税率查询入参
 * @Date 2024-12-16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerTaxQueryReqDTO implements Serializable {
    private static final long serialVersionUID = -8045787433374577322L;
    /** 客户编码 */
    @NotBlank(message = "客户编码不能为空")
    private String clientCode;
    /**MKU 数组**/
    @Size(max = 100,message = "MKU 个数不能超过100个")
    @NotEmpty(message = "MKU ID 不能为空")
    private Set<Long> mkuIds;
    /** 国家编码 */
    private String countryCode;
}
