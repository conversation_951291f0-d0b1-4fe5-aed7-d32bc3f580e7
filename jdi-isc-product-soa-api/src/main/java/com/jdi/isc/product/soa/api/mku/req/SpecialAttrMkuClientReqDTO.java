package com.jdi.isc.product.soa.api.mku.req;


import com.jdi.isc.product.soa.api.common.enums.MkuClientSpecialAttrEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * <AUTHOR>
 * @description：客户MKU特殊属性入参
 * @Date 2025-01-07
 */
@Data
public class SpecialAttrMkuClientReqDTO {

    @NotEmpty(message = "mkuIds不能为空")
    @Size(max = 20,message = "mkuIds最大长度为20")
    private Set<Long> mkuIds;
    /**客户编码**/
    @NotBlank(message = "clientCode不能为空")
    private String clientCode;
    /**
     * 特殊属性枚举
     */
    private Set<MkuClientSpecialAttrEnum> specialAttrEnums;
}
