package com.jdi.isc.product.soa.api.price;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.price.res.FulfillmentRateApiDTO;

/**
 * @Description: 履约费率表api服务
 * @Author: zhaokun51
 * @Date: 2024/10/29 21:47
 **/

public interface IscProductSoaFulfillmentRateWriteApiService {

    /**
     * 保存或更新FulfillmentRateApiDTO对象
     * @param input FulfillmentRateApiDTO对象，包含要保存或更新的数据
     * @return 保存或更新操作的结果，true表示成功，false表示失败
     */
    DataResponse<Boolean> saveOrUpdate(FulfillmentRateApiDTO input);


    /**
     * 根据ID删除数据。
     * @param id 要删除的数据的ID。
     * @return 删除操作是否成功。
     */
    DataResponse<Boolean> deleteById(Long id);

}
