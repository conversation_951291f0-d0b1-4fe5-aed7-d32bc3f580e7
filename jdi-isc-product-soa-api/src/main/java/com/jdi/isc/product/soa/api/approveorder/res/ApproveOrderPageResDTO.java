package com.jdi.isc.product.soa.api.approveorder.res;

import com.jdi.isc.product.soa.api.approveorder.common.ApproveOrderStatusEnum;
import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Map;

/**
 * 审核列表查询出参.
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApproveOrderPageResDTO extends BasicDTO implements Serializable {

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 流程类型
     */
    private Integer flowType;

    /**
     * 业务唯一键
     */
    private String bizId;

    /**
     * 流程单号
     */
    private String applyCode;

    /**
     * 审批状态，审核状态，0待审核，10审核中，20审核通过，30审核驳回，40审核撤回
     *
     * {@link com.jdi.isc.product.soa.api.approveorder.common.AuditStatusEnum}
     */
    private Integer auditStatus;

    /**
     * 当前节点审批人erp账号
     */
    private String currentNodeErp;

    /**
     * 1：审批通过 2：驳回
     */
    private Integer preApproveFlag;

    /**
     * 审批意见
     */
    private String preApproveComment;


    /**
     * 自定义列
     */
    private Map<String, String> customColumns;

    /**
     * 申请人
     */
    private String applyUserErp;

    /**
     * 申请原因
     */
    private String applyReason;

    /**
     * 审核失败原因
     */
    private String callbackFailMessage;

    /**
     * 失败次数
     */
    private Integer callbackFailCount;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 状态，10进行中， 20 已完成
     *
     * {@link ApproveOrderStatusEnum}
     */
    private Integer status;

    /**
     * 当前节点审批人
     */
    private String currentNodeAuditor;

    /**
     * 当前审批节点名称
     */
    private String currentNodeName;

    /**
     * 当前审批节点审核状态
     *
     * {@link com.jdi.isc.product.soa.api.approveorder.common.AuditStatusEnum}
     */
    private Integer currentNodeAuditStatus;

    /**
     * 回调失败动作
     */
    private Integer callbackFailAction;

    /**
     * 回调失败参数
     */
    private String callbackFailParam;
}
