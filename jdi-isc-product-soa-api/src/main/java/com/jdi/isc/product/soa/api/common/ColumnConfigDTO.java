package com.jdi.isc.product.soa.api.common;

import lombok.Data;

import java.io.Serializable;

/**
 * The type Audit column config dto.
 *
 * <AUTHOR>
 */
@Data
public class ColumnConfigDTO implements Serializable {
    /**
     * 列名称
     */
    private String title;

    /**
     * 拼接符号（%、￥等）
     */
    private String appendSymbol;

    private Integer width;

    private String dataIndex;

    private String key;

    private boolean omitNil = Boolean.TRUE;

    private boolean hideInSearch = Boolean.TRUE;

    private String align = "center";
}
