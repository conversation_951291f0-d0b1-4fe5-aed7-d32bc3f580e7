package com.jdi.isc.product.soa.api.wimp.category;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.KvDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.GlobalReqApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.GlobalResApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.GlobalQualificationApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.GlobalQualificationPageApiDTO;

import java.util.List;

/**
 * @Description: 国际资质主数据api服务
 * @Author: taxuezheng1
 * @Date: 2024/07/12 11:33
 **/

public interface GlobalQualificationApiService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<GlobalQualificationPageApiDTO.Response>> pageSearch(GlobalQualificationPageApiDTO.Request input);


    /**
     * 保存、更新资质信息
     */
    DataResponse<Boolean> saveOrUpdate(GlobalQualificationApiDTO input);


    /**
     * 跨境属性
     */
    DataResponse<GlobalQualificationApiDTO> detail(GlobalQualificationApiDTO input);


    /**
     * 校验多语言名称重复
     */
    DataResponse<Boolean> checkLangName(GlobalQualificationApiDTO input);

    /**
     * 根据id查询资质名称
     */
    DataResponse<List<KvDTO>> queryQualificationName(List<Long> ids, String lang);

    /**
     * 根据目的国查询缺失资质
     */
    DataResponse<List<GlobalResApiDTO>> queryRequireQualification(List<GlobalReqApiDTO> inputs);
}
