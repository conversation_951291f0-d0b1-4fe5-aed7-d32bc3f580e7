package com.jdi.isc.product.soa.api.customerSku.res;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.api.common.PriceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: sku客制化价格-草稿表 VO实体类
 * @Author: zhaokun51
 * @Date: 2024/10/21 13:30
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSkuPriceDraftApiDTO extends BaseReqDTO {

    /**
     * SKUID
     */
    @NotNull(message = "SKU id不能为空")
    private Long skuId;

    /**
     * 来源国家
     * */
    private String sourceCountryCode;

    /**
     * 客户简码
     */
    @NotNull(message = "客户编码不能为空")
    private String clientCode;

    /**
     * 客户名称
     */
    private String clientName;

    /**
     * 目标销售国家（客户所在国家）
     */
    private String targetCountryCode;

    /**
     * 供应商简码
     */
    private String vendorCode;

    /**
     * 审批层级
     * */
    private Integer level;

    /**
     * 对客交货模式 EXW,DDP
     */
    private String customerTradeType;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 客制销售价
     */
    @NotNull(message = "客制化销售价不能为空")
    private BigDecimal customerSalePrice;

    /**
     * 客制采购价,默认从base采购价取
     */
    private BigDecimal customerPurchasePrice;

    /**
     * 审核状态，1:审核通过，2:驳回 3:待审核
     */
    private Integer auditStatus;

    /** 商品名称*/
    private String skuName;


    /** 价格类型*/
    private PriceTypeEnum priceType;

    /**
     * 未税价
     */
    private BigDecimal salePrice;

    /**
     * 含税价
     */
    private BigDecimal includeTaxPrice;

    /**
     * 毛利率
     */
    private BigDecimal grossRate;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 开始时间
     */
    private Long beginTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /** 自增ID*/
    protected Long id;

    /** 备注*/
    private String remark;

    /** 创建者*/
    protected String creator;

    /** 修改人*/
    protected String updater;

    /** 创建时间*/
    protected Long createTime;

    /** 最后修改时间*/
    protected Long updateTime;

    private Integer yn;

    /** 0失效1有效2过期3未生效 */
    private Integer enableStatus;

    /**
     * 税率信息列表
     */
    private List<TaxRateDTO> taxRateList;

    /**
     * 调价原因
     */
    private String adjustmentPriceReason;

    /**
     * 附件
     */
    private List<String> attachmentUrls;

    /**
     * 国家池入池状态，1-已入池，3-未入池
     *
     * {@link CountryMkuPoolStatusEnum}
     */
    private Integer countryMkuPoolStatus;

    /**
     * 是否入客户池 1-绑定，0-未绑定
     *
     * {@link CustomerMkuBindEnum}
     */
    private String customerMkuPoolStatus;

    /**
     * 不可售阈值
     */
    private BigDecimal unsellableThreshold;

    /**
     * 不可售阈值更新时间
     */
    private Long unsellableThresholdTime;

    /**
     * 可售状态
     */
    private Integer availableSaleStatus;

    /**
     * 价格可售状态更新时间
     */
    private Long availableSaleStatusTime;

}
