package com.jdi.isc.product.soa.api.price;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.price.req.FulfillmentRateReqApiDTO;
import com.jdi.isc.product.soa.api.price.res.FulfillmentRateApiDTO;

/**
 * @Description: 履约费率表api服务
 * @Author: zhaokun51
 * @Date: 2024/10/29 21:47
 **/

public interface IscProductSoaFulfillmentRateReadApiService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<FulfillmentRateApiDTO>> pageSearch(FulfillmentRateReqApiDTO input);

    /**
     * 详情查询
     * @param id 主键id
     * @return 分页查询结果
     */
    DataResponse<FulfillmentRateApiDTO> detail(Long id);
}
