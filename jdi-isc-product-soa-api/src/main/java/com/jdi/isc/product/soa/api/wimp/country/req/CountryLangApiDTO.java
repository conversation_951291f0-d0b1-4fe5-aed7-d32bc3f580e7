package com.jdi.isc.product.soa.api.wimp.country.req;

import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import com.jdi.isc.product.soa.api.wimp.lang.res.LangResDTO;
import lombok.Data;

import java.util.List;

/**
 * @Description: 客户端VO
 * @Author: zhaokun51
 * @Date: 2024/07/10 09:42
 **/
@Data
public class CountryLangApiDTO extends BasicApiDTO {

    /** 国别码 */
    private String countryCode;

    /** 语言集合 */
    private List<LangResDTO> langList;

}
