package com.jdi.isc.product.soa.api.wimp.country.res;

import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import com.jdi.isc.product.soa.api.wimp.lang.res.LangResDTO;
import lombok.Data;

import java.util.List;

/**
 * @Description: 客户端VO
 * @Author: zhaokun51
 * @Date: 2024/07/10 09:42
 **/
@Data
public class CountryLangResDTO extends BasicApiDTO {

    /** 国家名 */
    private String countryName;

    /** 国别码 */
    private String countryCode;

    /** 国旗字符串 */
    private String countryFlag;

    /** 本土币种 */
    private String currency;

    /** 语言集合 */
    private List<LangResDTO> langList;
}
