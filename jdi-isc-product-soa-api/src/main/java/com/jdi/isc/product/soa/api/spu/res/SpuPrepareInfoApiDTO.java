package com.jdi.isc.product.soa.api.spu.res;

import com.jdi.isc.product.soa.api.sku.res.GroupSkuCertificateDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import com.jdi.isc.product.soa.api.spu.req.SpuCertificateApiDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SpuPrepareInfoApiDTO {

    /**扩展属性*/
    List<PropertyApiDTO> extendPropertyList;
    /**销售属性*/
    List<PropertyApiDTO> salePropertyList;
    /**spu资质列表*/
    List<SpuCertificateApiDTO> spuCertificateVOList;
    /**sku资质列表*/
    List<GroupSkuCertificateDTO> groupSkuCertificateVOList;
    /**spu国际属性列表*/
    List<GroupPropertyDTO> spuInterPropertyList;
    /**sku国际属性列表*/
    List<GroupPropertyDTO> skuInterPropertyList;
    /**币种*/
    private String currency;
}
