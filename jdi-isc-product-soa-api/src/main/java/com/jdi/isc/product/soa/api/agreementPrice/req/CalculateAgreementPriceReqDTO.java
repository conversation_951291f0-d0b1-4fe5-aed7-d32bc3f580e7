package com.jdi.isc.product.soa.api.agreementPrice.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class CalculateAgreementPriceReqDTO {

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    @NotNull(message = "货源国、国家站、国家码，ISO 3166-1 两字母代码不能为空")
    private String sourceCountryCode;

    /**
     * 目标国、国家站、国家码，ISO 3166-1 两字母代码
     */
    @NotNull(message = "目标国、国家站、国家码，ISO 3166-1 两字母代码不能为空")
    private String targetCountryCode;

    /**
     * SKU ID
     */
    @NotNull(message = "skuId不能为空")
    private Long skuId;

    /**
     * 是否为仓库产品，1表示是，0表示否。
     */
    private Integer isWareHouseProduct;

    /**
     * 不含税采购价格。
     */
    private BigDecimal price;

    /**
     * 含税采购价格。
     */
    private BigDecimal taxPrice;

    /**
     * 货币类型。
     */
    @NotNull(message = "货币类型不能为空")
    private String currency;
    /**
     * 1:未税算含税，及成本价，2:含税算未税，及成本价
     */
    private Integer calculateType;
}
