package com.jdi.isc.product.soa.api.common;

import lombok.Data;

import java.io.Serializable;

/**
 * 分页请求基础对象
 */
@Data
public class BasePageReqDTO extends BaseReqDTO implements Serializable {

    /** 起始页*/
    private Long index;

    /** 页大小*/
    private Long size;

    /** 偏移量*/
    private Long offset;

    /** 总记录数*/
    private Long total;

    /** 是否导出操作*/
    private boolean exportOperate;

    public Long getOffset(){
        if(this.offset == null && this.index != null && this.size != null){
            return (this.index - 1) * this.size;
        }
        return this.offset;
    }
}
