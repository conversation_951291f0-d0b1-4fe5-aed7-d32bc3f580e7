package com.jdi.isc.product.soa.api.taxRate;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.taxRate.req.CustomerTaxQueryReqDTO;
import com.jdi.isc.product.soa.api.taxRate.req.PurchaseTaxQueryReqDTO;
import com.jdi.isc.product.soa.api.taxRate.req.TaxRateReqDTO;
import com.jdi.isc.product.soa.api.taxRate.res.CustomerSaleTaxRateApiDTO;
import com.jdi.isc.product.soa.api.taxRate.res.CustomerTaxResDTO;
import com.jdi.isc.product.soa.api.taxRate.res.PurchaseSkuTaxResDTO;
import com.jdi.isc.product.soa.api.taxRate.res.TaxRateApiDTO;

import java.util.Map;

/**
 * 税率读服务
 * <AUTHOR>
 * @date 2024/11/08
 */

public interface IscTaxRateReadApiService {
    /**
     * 根据主键 id 查询
     */
    DataResponse<TaxRateApiDTO> getDetailById(TaxRateReqDTO dto);

    /**
     * 分页查询
     */
    DataResponse<PageInfo<TaxRateApiDTO>> pageTaxRate(TaxRateReqDTO dto);

    /**
     * 是否已存在
     * @param dto
     * @return
     */
    DataResponse<Boolean> exists(TaxRateReqDTO dto);


    /**
     * 批量查询客户商品税信息。
     * @param reqDTO 客户商品税信息查询请求对象。
     * @return 包含客户商品税信息的响应对象。
     */
    DataResponse<CustomerTaxResDTO> batchQueryCustomerTax(CustomerTaxQueryReqDTO reqDTO);

    /**
     * 批量查询采购税信息。
     * @param reqDTO 查询请求对象，包含查询条件。
     * @return 包含查询结果的 DataResponse 对象，结果为 Map 结构，key 为商品 SKU ID，value 为对应的 PurchaseSkuTaxResDTO 对象。
     */
    DataResponse<Map<Long, PurchaseSkuTaxResDTO>> batchQueryPurchaseTax(PurchaseTaxQueryReqDTO reqDTO);

    /**
     * 根据客户编码和SKU ID获取客户销售税率信息
     * @param clientCode 客户编码
     * @param skuId SKU ID
     * @return 包含客户销售税率信息的响应对象
     */
    DataResponse<CustomerSaleTaxRateApiDTO> getBrCustomerInitSaleTaxRate(String clientCode, Long skuId);
}
