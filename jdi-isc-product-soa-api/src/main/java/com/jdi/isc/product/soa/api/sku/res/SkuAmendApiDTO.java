package com.jdi.isc.product.soa.api.sku.res;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SkuAmendApiDTO {
    /**
     * 含电 0:不带电，1:带电
     */
    private Integer electric;
    /**
     * 含磁 0:不带磁，1:带磁
     */
    private Integer magnetic;
    /**
     * 含液 0:不含液，1:含液
     */
    private Integer liquid;
    /**
     * 含粉 0:不含粉，1:含粉
     */
    private Integer powder;
    /**
     * 生产企业
     */
    private String supplierName;
    /**
     * 组织机构代码
     */
    private String organizationCode;
    /**
     * 联系人姓名
     */
    private String name;
    /**
     * 联系人电话
     */
    private String phone;

    /**
     * SKUID
     */
    private Long skuId;
}
