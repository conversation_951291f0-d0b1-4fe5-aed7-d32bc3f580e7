package com.jdi.isc.product.soa.api.material.req;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 物料和MKU绑定分页查询对象
 * @Author: wangpeng965
 * @Date: 2024/08/05 16:45
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MkuMaterialPageReqDTO extends BasePageReqDTO implements Serializable {
    /**
     * 物料编码
     */
    private String materialId;

    /**
     * 物料名字
     */
    private String materialName;

    /**
     * MKUID
     */
    private Long mkuId;

    /**
     * MKU名称
     */
    private String mkuName;

    /**
     * 客户编码
     */
    private String clientCode;

    /**
     * 绑定状态
     */
    private String bindStatus;

    /**
     * 客户ID信息的集合
     */
    private ClientIdDTO clientIdVo;
}
