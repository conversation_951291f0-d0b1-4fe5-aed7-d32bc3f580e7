package com.jdi.isc.product.soa.api.mku.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.api.common.BasicReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/9/18
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryMkuReqDTO extends BaseReqDTO {

    /**
     * 商品ID集合
     */
    private String skuId;

}
