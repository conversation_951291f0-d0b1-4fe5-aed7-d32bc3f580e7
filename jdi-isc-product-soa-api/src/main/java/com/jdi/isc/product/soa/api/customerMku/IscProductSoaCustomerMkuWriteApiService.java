package com.jdi.isc.product.soa.api.customerMku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.customerMku.req.CustomerMkuBatchReqDTO;
import com.jdi.isc.product.soa.api.customerMku.req.CustomerMkuReqDTO;
import com.jdi.isc.product.soa.api.customerMku.req.MkuCheckBindReqDTO;
import com.jdi.isc.product.soa.api.customerMku.req.MkuPoolVerificationReqDTO;
import com.jdi.isc.product.soa.api.customerMku.res.MkuCheckBindResDTO;
import com.jdi.isc.product.soa.api.customerMku.res.MkuPoolVerificationResDTO;

import java.util.Set;

/**
 * spu写服务
 * <AUTHOR>
 * @date 2024/08/05
 */
public interface IscProductSoaCustomerMkuWriteApiService {

    DataResponse<Boolean> bind(CustomerMkuBatchReqDTO input);

    DataResponse<Set<Long>> batchBind(CustomerMkuBatchReqDTO input);

    DataResponse<MkuPoolVerificationResDTO> poolVerification(MkuPoolVerificationReqDTO poolVerificationReqDTO);


    DataResponse<Boolean> bindCustomerToMku(CustomerMkuBatchReqDTO reqVO);

    DataResponse<MkuCheckBindResDTO> checkAndBind(MkuCheckBindReqDTO reqVO);
}
