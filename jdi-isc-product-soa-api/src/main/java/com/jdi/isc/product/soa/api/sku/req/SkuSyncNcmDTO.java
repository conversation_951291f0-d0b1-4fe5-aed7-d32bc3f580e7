package com.jdi.isc.product.soa.api.sku.req;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * The type Sku sync ncm dto.
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class SkuSyncNcmDTO extends BaseReqDTO {
    /**
     * 国内SKU ID
     */
    @NotNull(message = "国内SKU ID不能为空")
    private Long jdSkuId;
    /**
     * 海关编码
     */
    @NotBlank(message = "海关编码不能为空")
    private String ncmCode;

    /**
     * Instantiates a new Sku sync ncm dto.
     *
     * @param jdSkuId the jd sku id
     * @param ncmCode the ncm code
     * @param pin     the pin
     */
    public SkuSyncNcmDTO(Long jdSkuId, String ncmCode, String pin) {
        this.jdSkuId = jdSkuId;
        this.ncmCode = ncmCode;
        super.setPin(pin);
    }
}
