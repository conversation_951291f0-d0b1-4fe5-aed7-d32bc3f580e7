package com.jdi.isc.product.soa.api.spu;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.spu.req.*;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.validation.TransferCategoryValidateGroup;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * spu写服务
 * <AUTHOR>
 * @date 2024/07/15
 */
public interface IscProductSoaSpuWriteApiService {

    /**
     * 创建SPU
     * */
    DataResponse<Long> create(SaveSpuApiDTO input);

    /**
     * 更新SPU
     * */
    DataResponse<Long> update(SaveSpuApiDTO input);

    /**
     * 保存草稿
     * */
    DataResponse<Long> saveDraft(SaveSpuApiDTO input);

    /**
     * 提交
     * */
    DataResponse<Long> submit(SaveSpuApiDTO input);

    /**
     * 审批通过-批量
     * */
    DataResponse<String> batchApprove(SpuAuditRecordUpdateApiDTO spuAuditRecordReqVO);

    /**
     * 审批驳回-批量
     * */
    DataResponse<String> batchReject(SpuAuditRecordUpdateApiDTO spuAuditRecordReqVO);

    /**
     * 批量归档数据-手动
     */
    DataResponse<String> batchArchive(BatchProductReqDTO reqDTO);

    /**
     * 批量下架
     */
    DataResponse<String> batchDown(BatchProductReqDTO reqDTO);

    /**
     * 更新商品信息
     * @param spuExcelApiDTOs 商品更新信息的列表
     * @return 包含更新结果的 DataResponse 对象，内含更新后的商品信息列表
     */
    DataResponse<List<SpuUpdateApiDTO>> updateSpu(List<SpuUpdateApiDTO> spuExcelApiDTOs);

    /**
     * 批量归档-功能
     */
    DataResponse<String> batchDelete(BatchProductReqDTO reqDTO);

    /**
     * 批量修改商品信息
     */
    DataResponse<String> batchAmend(List<SpuAmendReqDTO> inputs);


    DataResponse<Long> updatePrice(SpuUpdatePriceReqDTO dto);


    DataResponse<Boolean> batchUpdateStock(StockManageReqDTO dto);

    /**
     * 将商品从一个类目转移到另一个类目。
     * @param reqDTO 转移类目请求对象，包含SPU ID和目标类目ID。
     * @return 转移操作是否成功。
     */
    @Validated(TransferCategoryValidateGroup.transfer.class)
    DataResponse<Boolean> transferCategory(TransferCategoryReqDTO reqDTO);

    DataResponse<Boolean> updateSpuSkuMku(SpuUpdateApiDTO dto);

    /**
     * 更新商品详情信息
     * @param dto 更新商品描述的DTO对象
     */
    DataResponse<String> updateDescription(SpuDescriptionUpdateApiDTO dto);

    /**
     * 税务-审批通过-批量
     * */
    DataResponse<String> batchApproveTax(SpuAuditTaxApproveDTO spuAuditTaxApproveDTO);

    /**
     * 税务-审批驳回-批量
     * */
    DataResponse<String> batchRejectTax(SpuAuditTaxApproveDTO spuAuditTaxApproveDTO);

    /**
     * 更新批量商品的供应商代码
     * @param batchProductReqDTO 批量商品请求对象，包含需要更新的商品信息和新的供应商代码
     * @return 更新结果，返回成功或失败的消息
     */
    DataResponse<String> updateSpuVendorCode(BatchProductReqDTO batchProductReqDTO);

    /**
     * 根据中台商品扩展属性变动消息重新从中台拉去刷新商品扩展属性
     *
     * @param skuId
     * @return
     */
    DataResponse<String> refreshSpuExtAttribute(Long skuId);

}
