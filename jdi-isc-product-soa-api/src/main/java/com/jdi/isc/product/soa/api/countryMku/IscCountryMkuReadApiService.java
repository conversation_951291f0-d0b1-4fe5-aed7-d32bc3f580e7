package com.jdi.isc.product.soa.api.countryMku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuCheckDTO;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuCheckReqDTO;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuExportDTO;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuPageApiDTO;
import com.jdi.isc.product.soa.api.spu.req.SpuLangApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientInPoolApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientUpSaleApiDTO;

import java.util.List;

public interface IscCountryMkuReadApiService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<CountryMkuPageApiDTO.Response>> pageSearch(CountryMkuPageApiDTO.Request input);


    /**
     * 获取审核状态数量。
     * @return 审核状态数量列表。
     */
    DataResponse<List<CountryMkuPageApiDTO.Request>> auditStatusNum(CountryMkuPageApiDTO.Request input);


    /**
     * 获取审核状态数量。
     * @return 审核状态数量列表。
     */
    DataResponse<CountryMkuCheckDTO> checkBlackData(CountryMkuCheckReqDTO input);


    DataResponse<CountryMkuCheckDTO> checkOutBlackData(CountryMkuCheckReqDTO input);



    /**
     * 在指定国家的 MKU 客户池中检查是否存在匹配的客户。
     * @param input 包含国家信息和其他必要参数的请求对象。
     * @return 如果在客户池中找到了匹配的客户，则返回一个包含客户信息的列表；否则，返回一个空列表。
     */
    DataResponse<List<MkuClientInPoolApiDTO>> checkInPool(CountryMkuCheckReqDTO input);

    /**
     * 根据条件查询国家MKU列表
     * @param input 查询条件
     * @return 国家MKU列表的数据响应
     */
    DataResponse<List<CountryMkuExportDTO>> queryCountryMku(CountryMkuPageApiDTO.Request input);

    /**
     * 检查指定国家的MKU客户上行销售状态。
     * @param input 包含要检查的国家信息的请求对象。
     * @return 上行销售状态列表。
     */
    DataResponse<List<MkuClientUpSaleApiDTO>> checkUpSaleStatus(CountryMkuCheckReqDTO input);

    /**
     * 获取MKU和SPU的多语言标题
     * @param mkuId MKU ID
     * @param targetCountryCode 目标国家代码
     * @return MKU/SPU多语言标题信息
     */
    DataResponse<List<SpuLangApiDTO>> getMkuSpuTitleLang(Long mkuId, String targetCountryCode);
}
