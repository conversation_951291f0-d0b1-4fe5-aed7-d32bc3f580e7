package com.jdi.isc.product.soa.api.price;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.price.biz.SkuPriceMonitorSpuPageApiDTO;
import com.jdi.isc.product.soa.api.price.biz.SkuPurchasePriceCompareApiDTO;
import com.jdi.isc.product.soa.api.pricewarn.dto.PriceWarnMqDTO;

/**
 * @Description: 价格监控api服务
 * @Author: zhaojianguo21
 * @Date: 2024/12/11 17:13
 **/

public interface SkuPriceMonitorApiService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<SkuPriceMonitorSpuPageApiDTO.Response>> pageSearchSpu(SkuPriceMonitorSpuPageApiDTO.Request input);

    /**
     * 比较采购价
     * @param input
     * @return
     */
    DataResponse<SkuPurchasePriceCompareApiDTO.Response> comparePurchasePrice(SkuPurchasePriceCompareApiDTO.Request input);

}