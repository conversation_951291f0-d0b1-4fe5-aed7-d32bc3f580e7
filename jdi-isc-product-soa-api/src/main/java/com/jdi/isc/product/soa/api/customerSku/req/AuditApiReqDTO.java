package com.jdi.isc.product.soa.api.customerSku.req;

import com.jdi.isc.product.soa.api.approveorder.common.AuditBizTypeEnum;
import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.api.approveorder.common.AuditActionEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * 客制化价格审核请求参数.
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AuditApiReqDTO extends BaseReqDTO implements Serializable {

    /**
     * 审核动作，0-审核创建，1-审核通过，2=审核驳回，3-审核撤回
     *
     * {@link AuditActionEnum}
     */
    @NotNull(message = "审核动作不能为空")
    @Min(value = 0, message = "审核动作不正确")
    @Max(value = 3, message = "审核动作不正确")
    private Integer action;

    /**
     * 业务类型
     * <p>
     * {@link AuditBizTypeEnum}
     */
    @NotNull(message = "业务类型不能为空")
    private Integer bizType;

    /**
     * 是否强制，发起审批时使用，true-强制创建，则会将进行中的流程取消，重新创建一个新的流程
     * <p>
     * 只能用于同申请人的强制创建，不同申请人不支持，会返回错误
     */
    private boolean force;

    /**
     * 审核表id信息
     */
    @Size(min = 1, message = "审核条目不能为空")
    @Size(max = 100, message = "审核条目不能超过100条")
    private List<Long> ids;

    /**
     * 审批意见
     */
    @NotEmpty(message = "审批意见不能为空")
    private String approveComment;
}
