package com.jdi.isc.product.soa.api.attribute.common;

import com.jdi.isc.product.soa.api.common.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import java.util.List;

/**
 * 属性值
 * <AUTHOR>
 * @date 2024/9/1
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AttributeValueDTO extends BaseDTO {
        /** 属性ID*/
    private Long attributeId;

    /** 排序字段*/
    private Integer sort;

    /** 启用状态 */
    private Integer status;

    /** 键值对，扩展字段*/
    private String features;

    /** 多语言信息*/
    private List<AttributeValueLangDTO> langList;
}
