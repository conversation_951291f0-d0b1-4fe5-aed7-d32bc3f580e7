package com.jdi.isc.product.soa.api.category.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Set;

/**
 * 类目读实体
 * <AUTHOR>
 * @Description 类目读实体
 * @Date 20250619
 */
@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class CategoryReadDTO {

    /**
     * 类目ID
     */
    @NotEmpty(message = "catId can not be empty")
    private Set<Long> catId;

    /**
     * 语种
     */
    @NotEmpty(message = "lang can not be empty")
    private Set<String> lang;

}
