package com.jdi.isc.product.soa.api.warehouse;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.validation.WarehouseValidGroup;
import com.jdi.isc.product.soa.api.warehouse.req.*;
import com.jdi.isc.product.soa.api.warehouse.res.WarehouseSkuRelationResDTO;
import org.springframework.validation.annotation.Validated;

import java.util.Set;

/**
 * 仓库
 * <AUTHOR>
 * @date 2024/7/23
 **/
public interface IscWarehouseWriteApiService{


     /**
     * @function 保存或更新仓库信息，并返回操作结果
     * @param reqDTO - 包含仓库更新所需信息的数据传输对象
     * @returns DataResponse<Long> - 包含操作结果的响应数据，其中包括新建或更新的仓库ID
     */
    @Validated(WarehouseValidGroup.save.class)
    DataResponse<Long> saveOrUpdate(WarehouseUpdateReqDTO reqDTO);


    /**
     * @function 更新仓库状态
     * @param reqDTO - 包含更新状态所需信息的数据传输对象
     * @returns 返回包含操作成功与否的布尔值的数据响应对象
     */
    DataResponse<Boolean> updateStatus(WarehouseUpdateStatusReqDTO reqDTO);


    /**
     * @function 更新仓库中的SKU关系信息
     * @param skuRelationUpdateReqDTO - 仓库SKU关系更新请求数据传输对象，包含需要更新的SKU关系信息
     * @returns DataResponse<WarehouseSkuRelationResDTO> - 包含操作结果状态和更新后的SKU关系信息的数据响应对象
     */
    DataResponse<WarehouseSkuRelationResDTO> updateWarehouseSkus(WarehouseSkuRelationUpdateReqDTO skuRelationUpdateReqDTO);

    /**
     * 更新在途销售状态。
     * @param reqDTO 包含更新在途销售状态所需的信息。
     * @return 更新操作的结果。
     */
    DataResponse<Boolean> updateOnWaySale(WarehouseUpdateOnWaySaleReqDTO reqDTO);

    /**
     * 解绑SKU与仓库的绑定关系
     * @param warehouseSkuUnBindReq 需要解绑的SKU与仓库关系集合，包含待解绑的SKU和仓库信息
     * @return 包含解绑操作结果的响应对象，其中布尔值表示操作是否成功
     */
    @Validated(WarehouseValidGroup.execUnbind.class)
    DataResponse<Boolean> unbindSkuWarehouseBindRelation(WarehouseSkuUnBindReqDTO warehouseSkuUnBindReq);

}
