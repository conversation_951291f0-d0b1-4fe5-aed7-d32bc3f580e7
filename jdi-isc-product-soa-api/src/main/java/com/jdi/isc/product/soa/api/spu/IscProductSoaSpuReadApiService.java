package com.jdi.isc.product.soa.api.spu;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.spu.req.*;
import com.jdi.isc.product.soa.api.spu.res.*;
import com.jdi.isc.product.soa.api.spu.res.SpuApiDTO;

import java.util.List;

/**
 * 国家主数据写服务
 * <AUTHOR>
 * @date 2024/07/10
 */
public interface IscProductSoaSpuReadApiService {


    DataResponse<PageInfo<SpuApiDTO>> spuPage(SpuQueryReqApiDTO spuQueryReqApiDTO);

    DataResponse<SpuDetailApiDTO> getDetailBySpuId(SpuDetailReqApiDTO reqApiDTO);

    DataResponse<SpuPrepareInfoApiDTO> getPrepare(SpuPrepareReqApiDTO spuPrepareReqApiDTO);

    DataResponse<List<SaleUnitApiDTO>> saleUnitList(String lang);

    /**
     * 获取商品详细信息
     * @param reqApiDTO 请求参数对象，包含商品ID等信息
     * @return 商品详细信息的DataResponse对象，包括SpuDetailApiDTO实体
     */
    DataResponse<SpuDetailApiDTO> getDetailInfo(SpuDetailReqApiDTO reqApiDTO);


    DataResponse<List<SpuAmendApiDTO>> querySpuAmendVOListBySpuIds(List<Long> spuIds);


    DataResponse<Boolean> isMsgComplete(SaveSpuApiDTO saveSpuApiDTO);

    DataResponse<PageInfo<SpuApiDTO>> spuDraftPage(SpuQueryReqApiDTO spuQueryReqApiDTO);

    /**
     * 获取指定条件下的审核状态数量。
     * @param apiDTO SPU准备请求对象，包含查询条件
     * @return 审核状态数量的列表。
     */
    DataResponse<List<AuditNumDTO>> auditStatusNum(SpuPrepareReqApiDTO apiDTO);

    /**
     * 获取SPU草稿分页列表
     * @param spuQueryReqApiDTO SPU查询请求对象
     * @return 分页的SPU草稿列表
     */
    DataResponse<PageInfo<SpuApiDTO>> spuDraftPageForVc(SpuQueryReqApiDTO spuQueryReqApiDTO);


    /**
     * 验证供应商SPU ID是否合法。
     * @param verifyDTO 包含待验证的供应商SPU ID信息的DTO对象。
     * @return 如果SPU ID合法，则返回true；否则返回false。
     */
    DataResponse<Boolean> verifySupplierSpuId(SupplierSpuIdVerifyDTO verifyDTO);

}
