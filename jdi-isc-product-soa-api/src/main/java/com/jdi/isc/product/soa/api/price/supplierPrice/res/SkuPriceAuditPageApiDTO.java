package com.jdi.isc.product.soa.api.price.supplierPrice.res;

import com.jdi.isc.product.soa.api.common.CustomColumnDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class SkuPriceAuditPageApiDTO extends SkuPricePageApiDTO implements Serializable {

    /**
     * 申请单号
     */
    private String applyCode;

    /**
     * 利润率
     */
    private BigDecimal profitRate;

    /**
     * 国家成本价
     */
    private BigDecimal countryCostPrice;

    /**
     * 国家协议价
     */
    private BigDecimal agreementPrice;

    /**
     * 利润率阈值高
     */
    private BigDecimal profitRateLimit;

    /**
     * 利润率阈值低
     */
    private BigDecimal profitRateLowLimit;

    /**
     * 自定义列
     */
    private Map<String, String> customColumns;

    /**
     * 附件
     */
    private String attachmentUrls;

    /**
     * 调价原因
     */
    private String adjustmentPriceReason;
}
