package com.jdi.isc.product.soa.api.sku.req;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * <AUTHOR>
 * @description：BatchUpdateCustomsApiDTO
 * @Date 2025-04-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchUpdateCustomsApiReqDTO {
    /**
     * 批量更新海关信息
     */
    @NotEmpty(message = "报关信息不能为空")
    @Size(max = 20,message = "报关信息不能超过20个")
    private Set<UpdateCustomsApiDTO> updateCustomsApiDTOSet;
    /**
     * 更新人
     */
    @NotNull(message = "更新人不能为空")
    private String updater;
}
