package com.jdi.isc.product.soa.api.price.res;

import com.jdi.isc.product.soa.api.common.BasicApiDTO;
import com.jdi.isc.product.soa.api.validation.ExportTaxRateValidateGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 履约费率表 VO实体类
 * @Author: zhaokun51
 * @Date: 2024/10/29 21:47
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class FulfillmentRateApiDTO extends BasicApiDTO {

    /**
     * 国家码，ISO 3166-1 两字母代码
     */
    @NotNull(message = "商品来源国不可为空")
    private String sourceCountryCode;

    /**
     * 国家码，ISO 3166-1 两字母代码
     */
    @NotNull(message = "商品销售国不可为空")
    private String targetCountryCode;

    /**
     * 一级类目id
     */
    private Long firstCatId;

    /**
     * 二级类目id
     */
    private Long secondCatId;

    /**
     * 三级类目id
     */
    private Long thirdCatId;

    /**
     * 末级类目id
     */
    private Long lastCatId;
    /**
     * skuId
     */
    private Long skuId;

    private String sourceCountryName;

    private String targetCountryName;

    private String firstCatName;

    private String secondCatName;

    private String thirdCatName;

    private String lastCatName;

    /**
     * 是否备货：1备货(入仓) 2直送
     */
    @NotNull(message = "是否备货不能为空:1备货,0直送")
    private Integer isWarehouseProduct;

    /**
     * 货值系数-直发
     */
    @DecimalMin(value = "0", message = "货值系数不能小于0")
    private BigDecimal coefficientValue;

    /**
     * 体积金额-直发(CBM)
     */
    @DecimalMin(value = "0", message = "体积金额(RMB)不能小于0")
    private BigDecimal volumeValue;

    /**
     * 固定履约费用RMB(针对sku粒度维护专用)
     */
    @DecimalMin(value = "0", message = "固定履约费用(RMB)不能小于0")
    private BigDecimal fixedFulfillmentCost;

}
