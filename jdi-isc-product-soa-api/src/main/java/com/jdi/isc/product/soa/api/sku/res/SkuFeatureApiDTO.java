package com.jdi.isc.product.soa.api.sku.res;

import com.jdi.isc.product.soa.api.sku.base.CustomsApiDTO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * sku特性信息
 * <AUTHOR>
 * @date 2024/8/27
 **/
@Data
public class SkuFeatureApiDTO {
    /**
     * SPUID
     */
    private Long spuId;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 带磁 默认0=不带磁 1=带磁
     */
    private Integer magnetic;

    /**
     * 带电 默认0=不带电 1=带电
     */
    private Integer electric;

    /**
     * 含液体 默认0=不带 1=带液体
     */
    private Integer liquid;

    /**
     * 带粉末 默认0=不带 1=带粉末
     */
    private Integer powder;

    /**
     * 售后方式 0:其他 1:只换不修 2:寄修 3:本地上门服务
     */
    private Integer returnType;

    /**
     * 适配器插头类型
     */
    private String adapterPlus;

    /**
     * 适用电压
     */
    private String voltage;

    /**
     * 发货时效
     */
    private BigDecimal productionCycle;

    /**
     * 原产地
     */
    private String originCountry;
    /**
     * 采购模式 跨境品 null为直供，本土品无标  1、备货模式
     */
    private Integer purchaseModel;
    /**
     *  生产企业
     */
    private String enterprise;

    /**
     *  组织机构代码
     */
    private String orgCode;

    /**
     *  联系人姓名
     */
    private String contactsName;

    /**
     *  联系人电话
     */
    private String contactsPhone;

    /**
     *  规格型号
     */
    private String specification;
    /**
     *  包装规格
     */
    private Long packageSpecification;
    /**
     *  包装规格销售单位枚举，同SPU销售单位
     */
    private Integer packageSpecificationUnit;
    /**
     * 报关信息
     */
    private CustomsApiDTO customsApiDTO;
    /**
     * 主站状态联动启用状态  0：未启用 1：启用
     */
    private Integer mainSiteSynSwitch;
    /**
     * 最小起订量
     */
    private Integer moq;
}
