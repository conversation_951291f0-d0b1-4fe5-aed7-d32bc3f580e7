package com.jdi.isc.product.soa.api.jdm.sku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.isc.product.soa.api.jdm.sku.base.JdmSaveSpuDTO;
import com.jdi.isc.product.soa.api.jdm.sku.base.JdmSkuAuditRecordDTO;
import com.jdi.isc.product.soa.api.jdm.sku.req.JdmGetCountryReqDTO;
import com.jdi.isc.product.soa.api.jdm.sku.req.JdmSearchSkuAuditRecordReqDTO;
import com.jdi.isc.product.soa.api.jdm.sku.req.JdmSearchSkuReqDTO;
import com.jdi.isc.product.soa.api.jdm.sku.req.JdmGetSkuReqDTO;
import com.jdi.isc.product.soa.api.jdm.sku.res.JdmSaleUnitResDTO;
import com.jdi.isc.product.soa.api.jdm.sku.res.JdmSearchSkuResDTO;
import com.jdi.isc.product.soa.api.wimp.lang.res.LangResDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/4
 **/
public interface JdmSkuReadService {

    /**
     * 国内sku搜索
     * @param reqDTO 入参
     * @return 商品信息列表
     */
    DataResponse<PageInfo<JdmSearchSkuResDTO>> searchSku(JdmSearchSkuReqDTO reqDTO);

    /**
     * 查询商品详情
     * @param reqDTO 入参
     * @return 商品详情
     */
    DataResponse<JdmSaveSpuDTO> getSku(JdmGetSkuReqDTO reqDTO);

    /**
     * 销售单位查询
     * @return 销售单位列表
     */
    DataResponse<List<JdmSaleUnitResDTO>> getSaleUnits();

    /**
     * 根据给定的搜索请求DTO分页查询SKU审核记录。
     *
     * @param reqDTO 包含搜索条件和分页信息的请求DTO
     * @return 包含分页信息和SKU审核记录的响应对象
     */
    DataResponse<PageInfo<JdmSkuAuditRecordDTO>> auditRecordPage(JdmSearchSkuAuditRecordReqDTO reqDTO);


    /**
     * 根据属性范围查询语言资源列表
     * @param reqDTO 查询请求对象，包含国家信息等
     * @return 语言资源列表的DataResponse对象
     */
    DataResponse<List<LangResDTO>> queryLangListByAttributeScope(JdmGetCountryReqDTO reqDTO);
}
