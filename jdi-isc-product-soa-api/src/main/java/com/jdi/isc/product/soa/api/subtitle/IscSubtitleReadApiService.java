package com.jdi.isc.product.soa.api.subtitle;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.subtitle.biz.SubtitlePageReqDTO;
import com.jdi.isc.product.soa.api.subtitle.biz.SubtitlePageResDTO;

import java.util.List;

public interface IscSubtitleReadApiService {


    /**
     * 根据条件分页查询字幕信息。
     * @param dto 分页查询请求对象，包含查询条件和分页信息。
     * @return 分页查询结果，包括总页数、当前页码、每页记录数和字幕信息列表。
     */
    DataResponse<PageInfo<SubtitlePageResDTO>> pageSearch(SubtitlePageReqDTO dto);



    /**
     * 获取待审核字幕页数量
     * @param dto SubtitlePageReqDTO对象，包含查询条件
     * @return DataResponse对象，包含List<SubtitlePageReqDTO>类型的数据和状态码
     */
    DataResponse<List<SubtitlePageReqDTO>> auditStatusNum(SubtitlePageReqDTO dto);


}
