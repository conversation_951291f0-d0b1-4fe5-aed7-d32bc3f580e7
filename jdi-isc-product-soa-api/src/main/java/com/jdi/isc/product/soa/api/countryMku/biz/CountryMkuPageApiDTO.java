package com.jdi.isc.product.soa.api.countryMku.biz;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import com.jdi.isc.product.soa.api.common.BasicDTO;
import com.jdi.isc.product.soa.api.mkuTag.res.MkuTagIdDTO;
import com.jdi.isc.product.soa.api.specialAttr.res.SpecialAttrTagDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 商品国家池表分页查询对象
 * @Author: wangpeng965
 * @Date: 2024/12/02 21:49
 **/

@Data
public class CountryMkuPageApiDTO implements Serializable {


    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Request extends BasePageReqDTO implements Serializable {
        /**
         * MKUID
         */
        private Long mkuId;

        /**
         * mku标题
         */
        private String mkuTitle;

        /**
         * 品牌
         */
        private Long brandId;

        /**
         * 一级类目id
         */
        private Long firstCatId;

        /**
         * 二级类目id
         */
        private Long secondCatId;

        /**
         * 三级类目id
         */
        private Long thirdCatId;

        /**
         * 末级类目id
         */
        private Long lastCatId;

        /**
         * 货源国、国家站、国家码，ISO 3166-1 两字母代码
         */
        private String sourceCountryCode;

        /**
         * 目标国、国家站、国家码，ISO 3166-1 两字母代码
         */
        private String targetCountryCode;

        /**
         * 采购员
         */
        private String buyer;

        /**
         * 发货时效
         */
        private BigDecimal productionCycle;

        /**
         * 入池状态，0：待入池，1:已入池，2:已拉黑
         */
        private Integer poolStatus;

        /**
         * 预警状态，0：预警，1:正常
         */
        private Integer warnStatus;

        /**
         * 待确认原因，0:商品类目禁售，1:跨境运费占比超过阈值(0，1)
         */
        private String undeterminedReason;

        /**
         * 页签key
         * */
        private String typeKey;

        /**
         * 分类ids
         * */
        private Set<Long> catIds;

        /**
         * mkuIds
         * */
        private List<Long> mkuIds;

        /**
         * 黑名单原因
         * */
        private Set<Integer> blackReasons;

        /**
         * 预警原因
         * */
        private Set<Integer> warnReasons;

        /**
         * 待确认原因
         * */
        private Set<Integer> undeterminedReasons;

        /**
         * 数量
         */
        private Long num;

        /**
         * 标签键值对映射，用于存储和管理标签信息。
         */
        private List<MkuTagIdDTO> tagIdList;

        /**
         * skuIds
         * */
        private List<Long> skuIds;
        /**
         * skuId、spuId、jdSkuId、mkuId
         */
        private List<Long> productIds;

        /**
         * 国家商品池上下架状态 0:下架，1:上架
         */
        private Integer countryMkuStatus;

        /**
         * 下架原因
         */
        private String downReason;
    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BasicDTO implements Serializable{
        /**
         * MKUID
         */
        private Long mkuId;

        /**
         * mku中文名
         */
        private String mkuTitle;

        /**
         * 品牌
         */
        private Long brandId;

        /**
         * 品牌名称
         */
        private String brandName;

        /**
         * 货源国、国家站、国家码，ISO 3166-1 两字母代码
         */
        private String sourceCountryCode;

        /**
         * 货源国
         */
        private String sourceCountryName;

        /**
         * 目标国、国家站、国家码，ISO 3166-1 两字母代码
         */
        private String targetCountryCode;

        /**
         * 目标国
         */
        private String targetCountryName;

        /**
         * 类目id
         */
        private Long lastCatId;

        /**
         * 类目名称 path
         */
        private String catName;

        /**
         * 库存
         */
        private String stockNum;

        /**
         * 采购员
         */
        private String buyer;

        /**
         * 发货时效
         */
        private BigDecimal productionCycle;

        /**
         * 审核状态，0:草稿，1:审核通过，2:驳回，3:待审核，4:撤销
         */
        private Integer auditStatus;

        /**
         * 审核状态，0:无申请，1:待入池，2:待拉黑
         */
        private Integer applyStatus;

        /**
         * 入池状态，0：待入池，1:已入池，2:已拉黑
         */
        private Integer poolStatus;

        /**
         * 预警状态，0：预警，1:正常
         */
        private Integer warnStatus;

        /**
         * 预警原因，0:- ,1：进口许可不符合，2：出口许可不符合，3:强制商品认证不符合，4：跨境运输不符合，5:品牌授权不符合，6:跨境商品未入国内商品池
         */
        private String warnReason;

        /**
         * 拉黑原因，0:固定规则不满足，1:业务规则不满足，2:手动拉黑(0，1，2逗号分隔)
         */
        private String blackReason;

        /**
         * 待确认原因，0:商品类目禁售，1:跨境运费占比超过阈值(0，1)
         */
        private String undeterminedReason;

        /**
         * 标签名称列表
         */
        private List<String> tagNameList;

        /**
         * 特殊属性标签列表
         */
        private List<SpecialAttrTagDTO> specialAttrTagVOList;
        
        /**
         * 一级类目名称
         */
        private String firstCatName;

        /**
         * 二级类目名称
         */
        private String secondCatName;

        /**
         * 三级类目名称
         */
        private String thirdCatName;

        /**
         * 末级类目名称
         */
        private String lastCatName;

        /**
         * 入池失败原因
         */
        private String poolFailReason;

        /**
         * 不可售原因
         */
        private String unSaleReason;
        /**
         * SKU的Id 取值是mku和sku绑定关系，按照时间取最后一个
         */
        private Long skuId;

        /**
         * 国家成本价格
         */
        private BigDecimal countryCostPrice;

        /**
         * 国家仓储价格
         */
        private BigDecimal countryWarehousingPrice;

        /**
         * 退税价格
         */
        private BigDecimal refundTaxPrice;

        /**
         * 国家协议价格
         */
        private BigDecimal countryAgreementPrice;

        /**
         * 国家VIP价格
         */
        private BigDecimal countryVipPrice;

        /**
         * 京东平台的商品价格。
         */
        private BigDecimal jdPrice;
        /**
         * 货币类型。
         */
        private String currency;

        /**
         * 成本价格的标记。
         */
        private String costPriceMark;

        /**
         * 协议价格的标记。
         */
        private String agreementPriceMark;

        /**
         * 仓储价格的标记。
         */
        private String warehousingPriceMark;

        /**
         * 退税价格的标记。
         */
        private String refundTaxPriceMark;

        /**
         * 京东平台的商品价格标记。
         */
        private String jdPriceMark;
        /**
         * SPUID
         */
        private Long spuId;

        /**
         * 是否备货
         */
        private String purchase;


        /**
         * 国家商品池上下架状态 0:下架，1:上架
         */
        private Integer countryMkuStatus;

        /**
         * 下架原因
         */
        private String downReason;
        /**
         * 是否备货（0：否，1：是）
         */
        private Integer purchaseCode;
    }

}
