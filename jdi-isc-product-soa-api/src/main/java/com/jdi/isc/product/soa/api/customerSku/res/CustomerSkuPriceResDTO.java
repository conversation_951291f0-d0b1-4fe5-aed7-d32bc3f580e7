package com.jdi.isc.product.soa.api.customerSku.res;

import com.jdi.isc.product.soa.api.common.BaseDTO;
import com.jdi.isc.product.soa.api.common.PriceTypeEnum;
import com.jdi.isc.product.soa.api.sku.res.SkuPriceResDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 客户MKU关系实体
 * <AUTHOR>
 * @date 20231128
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CustomerSkuPriceResDTO extends BaseDTO {

    /** skuId*/
    private Long skuId;

    /** 客户编码*/
    private String clientCode;

    /** 客户名称*/
    private String clientName;

    /** 供应商简码*/
    private String vendorCode;

    /** 币种*/
    private String currency;

    /** 客制采购价*/
    private BigDecimal customerPurchasePrice;

    /** 客制销售价*/
    private BigDecimal customerSalePrice;

    /** 商品名称*/
    private String skuName;

    /** 交货方式  */
    private String customerTradeType;

    /** 价格类型*/
    private PriceTypeEnum priceType;

    /**
     * 未税价
     */
    private BigDecimal salePrice;


    /**
     * 含税价
     */
    private BigDecimal includeTaxPrice;
    /**
     * 毛利率
     */
    private BigDecimal grossRate;


    public CustomerSkuPriceResDTO(SkuPriceResDTO input){
        this.skuId = input.getSkuId();
        this.currency = input.getCurrency();
        this.customerSalePrice = input.getSalePrice();
        this.customerPurchasePrice = input.getPurchasePrice();
        this.customerTradeType = input.getCustomerTradeType();
    }

    public CustomerSkuPriceResDTO(Long skuId , String clientCode){
        this.skuId = skuId;
        this.clientCode = clientCode;
    }

}
