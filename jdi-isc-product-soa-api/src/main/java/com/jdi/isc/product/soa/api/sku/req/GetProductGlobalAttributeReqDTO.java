package com.jdi.isc.product.soa.api.sku.req;


import com.jdi.isc.product.soa.api.common.enums.IscAttributeDimensionEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @description：查询商品跨境属性
 * @Date 2025-02-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetProductGlobalAttributeReqDTO {
    /**
     * 商品ID集合
     */
    private Set<Long> skuIds;
    /**
     * 跨境属性ID集合
     */
    private Set<Long> attributeIds;
    /**
     * 属性维度 1:SPU 2:SKU
     * @see IscAttributeDimensionEnum
     */
    private Integer dimension;
    /**
     * 多语言 默认为zh
     */
    private String lang;
}
