package com.jdi.isc.product.soa.api.wimp.category;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.category.biz.CategoryQueryPageApiDTO;
import com.jdi.isc.product.soa.api.supplier.res.CategoryTreeResDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.CategoryBatchReqApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.CategoryReqApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.CategoryTreeReq;
import com.jdi.isc.product.soa.api.wimp.category.res.CategoryPathNameDTO;

import java.util.List;
import java.util.Map;

/**
 * 标准商品关系服务
 * <AUTHOR>
 * @date 20240909
 */
public interface CategoryApiService {

    /**
     * 所有类目树
     */
    DataResponse<List<CategoryTreeResDTO>> queryList(CategoryReqApiDTO input);

    /**
     * 根据供应商代码查询分类树列表
     * @param categoryReqApiDTO 分类请求参数对象，包含供应商代码等信息
     * @return 分类树列表的DataResponse对象
     */
    DataResponse<List<CategoryTreeResDTO>> queryListBySupplierCode(CategoryReqApiDTO categoryReqApiDTO);

    /**
     * 根据末级类目id，查询4321级别的类目名称级状态
     * @param input 类目批量请求对象，包含要查询的类目ID列表
     * @return 类目ID到对应的类目路径和名称的映射关系
     */
    DataResponse<Map<Long, CategoryPathNameDTO>> queryByCategoryIds(CategoryBatchReqApiDTO input);

    /**
     * 分页获取指定分类树的所有叶子节点
     * @param input 分类树请求参数，包括要查询的分类树ID、当前页码和每页条数等信息
     * @return 分页后的叶子节点列表，若无叶子节点则返回空列表
     */
    DataResponse<List<String>> queryAllCategoryTile(CategoryTreeReq input);

    /**
     * 查询所有类别下买家的标题信息
     * @param input 类别查询请求参数
     * @return 包含所有类别下买家标题信息的 DataResponse 对象
     */
    DataResponse<List<String>> queryCategoryBuyerAllCategoryTile(CategoryQueryPageApiDTO.Request input);

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    DataResponse<PageInfo<CategoryQueryPageApiDTO.Response>> pageSearch(CategoryQueryPageApiDTO.Request input);
}
