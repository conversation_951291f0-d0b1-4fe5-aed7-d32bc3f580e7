package com.jdi.isc.product.soa.api.markupRate.req;

import com.jdi.isc.product.soa.api.common.BasePageReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MarkupRatePageReqDTO extends BasePageReqDTO implements Serializable {
    /**
     * 业务编号
     */
    private String bizNo;

    /**
     * 一级类目id
     */
    private Long firstCatId;

    /**
     * 二级类目id
     */
    private Long secondCatId;

    /**
     * 三级类目id
     */
    private Long thirdCatId;

    /**
     * 末级类目id
     */
    private Long lastCatId;
    /**
     * 分类ids
     * */
    private Set<Long> catIds;

    /**
     * mkuIds
     * */
    private List<Long> mkuIds;

    /**
     * skuIds
     * */
    private List<Long> skuIds;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 目标国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * 品牌
     */
    private Long brandId;

    /**
     * 协议价加价率，单位：%
     */
    private BigDecimal agreementMarkupRate;

    /**
     * 京东价加价率，单位：%
     */
    private BigDecimal jdMarkupRate;

}
