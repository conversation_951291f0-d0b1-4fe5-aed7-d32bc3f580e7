package com.jdi.isc.product.soa.api.spu.res;

import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import com.jdi.isc.product.soa.api.spu.req.SaveSpuApiDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SpuDetailApiDTO extends SaveSpuApiDTO {
    /**
     * 一级类目ID
     */
    private Long firstCatId;
    /**
     * 二级类目ID
     */
    private Long secondCatId;
    /**
     * 三级类目ID
     */
    private Long thirdCatId;
    /**
     * 末级类目ID
     */
    private Long lastCatId;
    /**
     * 一级类目名
     */
    private String firstCatName;
    /**
     * 二级类目名
     */
    private String secondCatName;
    /**
     * 三级类目名
     */
    private String thirdCatName;
    /**
     * 末级类目名
     */
    private String lastCatName;
    /**
     * 扩展属性
     */
    List<PropertyApiDTO> extendPropertyList;

    /**
     * 销售属性
     */
    List<PropertyApiDTO> salePropertyList;

    /**
     * 跨境属性
     */
    List<PropertyApiDTO> interPropertyList;
}
