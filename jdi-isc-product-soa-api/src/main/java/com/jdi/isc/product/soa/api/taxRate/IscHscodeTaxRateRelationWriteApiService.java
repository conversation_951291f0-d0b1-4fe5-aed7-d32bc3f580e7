package com.jdi.isc.product.soa.api.taxRate;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.taxRate.biz.HscodeTaxRateRelationPageApiDTO;
import com.jdi.isc.product.soa.api.taxRate.req.HscodeTaxRateRelationDetailReqApiDTO;
import com.jdi.isc.product.soa.api.taxRate.req.HscodeTaxRateRelationSaveUpdateReqApiDTO;
import com.jdi.isc.product.soa.api.taxRate.res.HscodeTaxRateRelationResApiDTO;

/**
 * @Description: 跨境税率关系api服务
 * @Author: chengliwei
 * @Date: 2024/12/11 10:47
 **/

public interface IscHscodeTaxRateRelationWriteApiService {

    /**
     * 保存更新
     * @param input 保存请求参数
     * @return 保存结果
     */
    DataResponse<HscodeTaxRateRelationResApiDTO> saveOrUpdate(HscodeTaxRateRelationSaveUpdateReqApiDTO input);
}