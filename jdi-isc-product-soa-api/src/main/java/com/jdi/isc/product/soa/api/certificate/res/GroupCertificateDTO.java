package com.jdi.isc.product.soa.api.certificate.res;

import com.jdi.isc.product.soa.api.common.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 跨境资质
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GroupCertificateDTO extends BaseDTO {

    /**
     * 必填时机
     */
    private Integer requirement;

    /**
     * sku资质
     * */
    private List<CertificateDTO> certificateDTOS;
}