package com.jdi.isc.open.soa.service.protocol.sdk.stock;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.aggregate.read.wiop.api.mku.MkuReadApiService;
import com.jdi.isc.aggregate.read.wiop.api.mku.req.MkuClientReadReq;
import com.jdi.isc.aggregate.read.wiop.api.mku.resp.CustomerMkuReadResp;
import com.jdi.isc.aggregate.read.wiop.api.stock.req.GetStockByIdGoodsReadReq;
import com.jdi.isc.aggregate.read.wiop.api.stock.req.SkuNumBaseGoodsReadReq;
import com.jdi.isc.aggregate.read.wiop.api.stock.res.GetStockByIdGoodsReadResp;
import com.jdi.isc.open.soa.rpc.stock.StockRpcService;
import com.jdi.isc.open.soa.sdk.stock.IscStockReadApiService;
import com.jdi.isc.open.soa.sdk.stock.req.IscSkuNumInfoReqDTO;
import com.jdi.isc.open.soa.sdk.stock.req.IscSkuStockReqDTO;
import com.jdi.isc.open.soa.sdk.stock.res.IscSkuStockResDTO;
import com.jdi.isc.open.soa.service.adapter.mapstruct.stock.SkuStockConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author：xubing82
 * @date：2025/8/12 21:24
 * @description：工业国际库存查询
 */
@Service
@Slf4j
public class IscStockReadApiServiceImpl implements IscStockReadApiService {


    @Resource
    private MkuReadApiService mkuReadApiService;


    @Resource
    private StockRpcService stockRpcService;


    @ToolKit(exceptionWrap = true)
    @Override
    public DataResponse<List<IscSkuStockResDTO>> getIscStock(IscSkuStockReqDTO req) {
        //mku数据权限过滤
        MkuClientReadReq mkuReq = new MkuClientReadReq();
        mkuReq.setMkuIdList(req.getIscSkuNumInfoReqDTO().stream().map(IscSkuNumInfoReqDTO::getMkuId).collect(Collectors.toSet()));
        mkuReq.setClientCode(req.getClientCode());
        DataResponse<List<CustomerMkuReadResp>> effectiveMkuRpc = mkuReadApiService.list(mkuReq);
        if (!effectiveMkuRpc.getSuccess() || CollectionUtils.isEmpty(effectiveMkuRpc.getData())) {
            return DataResponse.error(DataResponseCode.NO_PERMISSION.getCode(), String.format("客户 %s 未拥有对应mku的查询权限", req.getClientCode()));
        }


        //过滤出有效的mku数据，重新设置库存请求信息
        Set<Long> effectiveMku = effectiveMkuRpc.getData().stream().map(CustomerMkuReadResp::getMkuId).collect(Collectors.toSet());
        List<IscSkuNumInfoReqDTO> items = req.getIscSkuNumInfoReqDTO()
                .stream().filter(item -> effectiveMku.contains(item.getMkuId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(items)) {
            return DataResponse.error(DataResponseCode.NO_PERMISSION.getCode(), String.format("客户 %s 未拥有对应mku的查询权限", req.getClientCode()));
        }
        req.setIscSkuNumInfoReqDTO(items);


        //获取库存信息
        GetStockByIdGoodsReadReq input = buildGetStockByIdGoodsReadReq(req);
        List<GetStockByIdGoodsReadResp> getStockByIdGoodsReadRes = stockRpcService.getStockBySkuId(input);
        List<IscSkuStockResDTO> skuStockResDTOS = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(getStockByIdGoodsReadRes)) {
            skuStockResDTOS = SkuStockConvert.INSTANCE.read2StockRes(getStockByIdGoodsReadRes);
        }

        return DataResponse.success(skuStockResDTOS);
    }

    /**
     * 构建获取商品库存信息请求对象
     *
     * @param req IscSkuStockReqDTO类型的请求对象，包含客户代码、合同号和商品信息
     * @return GetStockByIdGoodsReadReq类型的请求对象，用于获取指定商品的库存信息
     */
    @NotNull
    private GetStockByIdGoodsReadReq buildGetStockByIdGoodsReadReq(IscSkuStockReqDTO req) {
        GetStockByIdGoodsReadReq input = new GetStockByIdGoodsReadReq();
        input.setClientId(req.getClientCode());
        input.setContractNumber(req.getContractNum());
        List<SkuNumBaseGoodsReadReq> skuNumInfoLis = req.getIscSkuNumInfoReqDTO().stream().map(item -> {
            SkuNumBaseGoodsReadReq baseGoodsReadReq = new SkuNumBaseGoodsReadReq();
            baseGoodsReadReq.setSkuId(item.getMkuId());
            baseGoodsReadReq.setSkuNumber(item.getNum());
            return baseGoodsReadReq;
        }).collect(Collectors.toList());

        input.setSkuNumInfoList(skuNumInfoLis);
        return input;
    }


}
