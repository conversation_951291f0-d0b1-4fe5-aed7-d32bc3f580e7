package com.jdi.isc.open.soa.service.protocol.sdk.price;

import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.aggregate.read.wiop.api.customer.res.CustomerReadResp;
import com.jdi.isc.open.soa.rpc.customer.RpcCustomerService;
import com.jdi.isc.open.soa.rpc.mku.IscProductSoaMkuReadRpcService;
import com.jdi.isc.open.soa.rpc.price.CustomerSkuPriceWriteRpcService;
import com.jdi.isc.open.soa.sdk.price.IscSalePriceWriteApiService;
import com.jdi.isc.open.soa.sdk.price.req.IscMkuSalePriceWriteSdkReq;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.customerSku.res.CustomerSkuPriceDraftApiDTO;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;


/**
 * 工业国际MKU销价写服务接口
 * @description 提供国际SKU客制化价格写服务
 * <AUTHOR>
 * @date 2025/6/4
 */
@Service
@Slf4j
public class IscSalePriceWriteApiServiceImpl implements IscSalePriceWriteApiService {

    @Resource
    private CustomerSkuPriceWriteRpcService customerSkuPriceWriteRpcService;
    @Resource
    private RpcCustomerService rpcCustomerService;
    @Resource
    private IscProductSoaMkuReadRpcService iscProductSoaMkuReadRpcService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> setIscSalePrice(IscMkuSalePriceWriteSdkReq req) {
        //获取客户简码
        Map<String, CustomerReadResp> clientMap = rpcCustomerService.getCustomerByContractCode(Sets.newHashSet(req.getContractNum()));
        if(MapUtils.isEmpty(clientMap) || clientMap.get(req.getContractNum())==null){
            return DataResponse.error(String.format("客户%s不存在,请检查",req.getContractNum()));
        }
        CustomerReadResp client = clientMap.get(req.getContractNum());
        if(CountryConstant.COUNTRY_BR.equals(client.getCountry())){
            return DataResponse.error(String.format("vip设价不支持巴西客户,请检查 %s",req.getContractNum()));
        }
        //mku转sku
        BatchQueryMkuReqDTO refReq = new BatchQueryMkuReqDTO();
        refReq.setMkuId(Sets.newHashSet(req.getMkuId()));
        Map<Long, Long> sku = iscProductSoaMkuReadRpcService.getIscSkuIdByMkuId(refReq);
        if(MapUtils.isEmpty(sku)){
            return DataResponse.error(String.format("MKU%s 下不存在有效的对应sku关系,请检查",req.getMkuId()));
        }
        //保存客制化价格
        CustomerSkuPriceDraftApiDTO input = new CustomerSkuPriceDraftApiDTO();
        input.setClientCode(client.getClientCode());
        input.setSkuId(sku.get(req.getMkuId()));
        input.setCustomerSalePrice(req.getCustomerSalePrice());
        input.setBeginTime(req.getBeginTime());
        input.setEndTime(req.getEndTime());
        input.setPin(req.getOperator());
        return customerSkuPriceWriteRpcService.saveOrUpdate(input);
    }

}
