package com.jdi.isc.open.soa.service.manage.mku.impl;


import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.wiop.api.stock.req.GetStockByIdGoodsReadReq;
import com.jdi.isc.aggregate.read.wiop.api.stock.req.SkuNumBaseGoodsReadReq;
import com.jdi.isc.aggregate.read.wiop.api.stock.res.GetStockByIdGoodsReadResp;
import com.jdi.isc.open.soa.common.constants.Constant;
import com.jdi.isc.open.soa.rpc.customer.RpcCustomerService;
import com.jdi.isc.open.soa.rpc.mku.IscProductSoaMkuReadRpcService;
import com.jdi.isc.open.soa.rpc.stock.StockRpcService;
import com.jdi.isc.open.soa.sdk.common.LangConstant;
import com.jdi.isc.open.soa.sdk.mku.req.MkuAvailableSaleReqDTO;
import com.jdi.isc.open.soa.sdk.mku.req.MkuInfoReq;
import com.jdi.isc.open.soa.sdk.mku.res.IscMkuAvailableSaleRes;
import com.jdi.isc.open.soa.service.adapter.mapstruct.mku.MkuConvert;
import com.jdi.isc.open.soa.service.manage.mku.MkuReadManageService;
import com.jdi.isc.product.soa.api.common.enums.MkuQueryEnum;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuReqDTO;
import com.jdi.isc.product.soa.api.mku.req.IscMkuAvailableSaleReq;
import com.jdi.isc.product.soa.api.mku.res.IscMkuAvailableSaleResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description：MkuReadManageServiceImpl
 * @Date 2025-08-18
 */
@Slf4j
@Service
public class MkuReadManageServiceImpl implements MkuReadManageService {

    @Resource
    private StockRpcService stockRpcService;
    @Resource
    private RpcCustomerService rpcCustomerService;
    @Resource
    private IscProductSoaMkuReadRpcService iscProductSoaMkuReadRpcService;


    @Override
    public DataResponse<Map<Long, IscMkuAvailableSaleRes>> queryMkuAvailable(MkuAvailableSaleReqDTO req) {
        if(StringUtils.isNotBlank(req.getContractNumber())){
            String clientCode = rpcCustomerService.getClientCodeByContractNum(req.getContractNumber());
            req.setClientCode(clientCode);
        }
        if(StringUtils.isBlank(req.getClientCode()) && StringUtils.isBlank(req.getContractNumber())){
            return DataResponse.error("clientCode或contractNumber不能为空");
        }

        // 处理UniformBizInfo信息 暂不实现

        // 商品ID集合
        Set<Long> mkuIds = Sets.newHashSetWithExpectedSize(req.getMkuSet().size());
        // 初始化默认商品数量
        req.getMkuSet().forEach(mkuInfoReq -> {
            mkuIds.add(mkuInfoReq.getMkuId());
            if (Objects.isNull(mkuInfoReq.getNum())){
                mkuInfoReq.setNum(Constant.ONE);
            }
        });
        Map<Long, IscMkuAvailableSaleRes> resMap = Maps.newHashMapWithExpectedSize(mkuIds.size());
        mkuIds.forEach(mkuId -> resMap.put(mkuId,new IscMkuAvailableSaleRes(mkuId,Boolean.TRUE)));

        // 查询商品最小起订量是否满足
        BatchQueryMkuReqDTO queryMkuReqDTO = new BatchQueryMkuReqDTO();
        queryMkuReqDTO.setUniformBizInfo(req.getUniformBizInfo());
        queryMkuReqDTO.setClientCode(req.getClientCode());
        queryMkuReqDTO.setQueryEnum(Sets.newHashSet(MkuQueryEnum.BASE,MkuQueryEnum.EXTEND));
        queryMkuReqDTO.setMkuId(mkuIds);
        queryMkuReqDTO.setLangSet(Sets.newHashSet(StringUtils.isNotBlank(req.getLang()) ? req.getLang() : LangConstant.LANG_ZH));
        Map<Long, IscMkuResDTO> mkuResDTOMap = iscProductSoaMkuReadRpcService.getIscMku(queryMkuReqDTO);

        if (MapUtils.isEmpty(mkuResDTOMap)){
            return DataResponse.error(String.format("MKU数据归档或失效,请检查 %s",mkuIds));
        }
        // 判断最小起订量是否满足
        compareMoq(req, mkuResDTOMap, resMap);
        // 处理Mku可售状态
        handleMkuAvailable(req, mkuIds, resMap);
        // 查询库存是否有货
        List<GetStockByIdGoodsReadResp> stockRespList = stockRpcService.getStockBySkuId(this.buildGetStockByIdGoodsReadReq(req));
        if (CollectionUtils.isNotEmpty(stockRespList)){
            Map<Long, GetStockByIdGoodsReadResp> stockResMap = stockRespList.stream().collect(Collectors.toMap(GetStockByIdGoodsReadResp::getSkuId, Function.identity()));
            resMap.forEach((mkuId, saleRes)-> {
                if (stockResMap.containsKey(mkuId) && Objects.nonNull(stockResMap.get(mkuId)) && stockResMap.get(mkuId).getStockStateType() == 34) {
                    saleRes.failed(mkuId, "库存不足");
                }
            });
        }
        return DataResponse.success(resMap);
    }

    private void handleMkuAvailable(MkuAvailableSaleReqDTO req, Set<Long> mkuIds, Map<Long, IscMkuAvailableSaleRes> resMap) {
        IscMkuAvailableSaleReq iscMkuAvailableSaleReq = MkuConvert.INSTANCE.dto2Req(req);
        iscMkuAvailableSaleReq.setMkuIds(mkuIds);
        // 语言为空，默认中文
        if (StringUtils.isBlank(iscMkuAvailableSaleReq.getLang())){
            iscMkuAvailableSaleReq.setLang(LangConstant.LANG_ZH);
        }
        Map<Long, IscMkuAvailableSaleResDTO> map = iscProductSoaMkuReadRpcService.queryMkuAvailable(iscMkuAvailableSaleReq);
        if (MapUtils.isNotEmpty(map)){
            resMap.forEach((mkuId, saleRes)-> {
                if (map.containsKey(mkuId) && Objects.nonNull(map.get(mkuId)) && !map.get(mkuId).getIsAvailableSale()) {
                    saleRes.failed(mkuId,map.get(mkuId).getUnAvailableSaleMessage());
                }
            });
        }
    }

    private void compareMoq(MkuAvailableSaleReqDTO req, Map<Long, IscMkuResDTO> mkuResDTOMap, Map<Long, IscMkuAvailableSaleRes> resMap) {
        for (MkuInfoReq mkuInfoReq : req.getMkuSet()) {
            IscMkuResDTO iscMkuResDTO = mkuResDTOMap.getOrDefault(mkuInfoReq.getMkuId(), new IscMkuResDTO());
            // 最小起订量大于查询数量
            if (Objects.nonNull(iscMkuResDTO.getMoq()) && iscMkuResDTO.getMoq().compareTo(mkuInfoReq.getNum()) > 0){
                resMap.get(mkuInfoReq.getMkuId()).failed(mkuInfoReq.getMkuId(),String.format("不满足最小起订量 %s",iscMkuResDTO.getMoq()));
            }
        }
    }

    /**
     * 构建获取商品库存信息请求对象
     *
     * @param req IscSkuStockReqDTO类型的请求对象，包含客户代码、合同号和商品信息
     * @return GetStockByIdGoodsReadReq类型的请求对象，用于获取指定商品的库存信息
     */
    @NotNull
    private GetStockByIdGoodsReadReq buildGetStockByIdGoodsReadReq(MkuAvailableSaleReqDTO req) {
        GetStockByIdGoodsReadReq input = new GetStockByIdGoodsReadReq();
        input.setClientId(req.getClientCode());
        List<SkuNumBaseGoodsReadReq> skuNumInfoLis = req.getMkuSet().stream().map(item -> {
            SkuNumBaseGoodsReadReq baseGoodsReadReq = new SkuNumBaseGoodsReadReq();
            baseGoodsReadReq.setSkuId(item.getMkuId());
            baseGoodsReadReq.setSkuNumber(item.getNum());
            return baseGoodsReadReq;
        }).collect(Collectors.toList());

        input.setSkuNumInfoList(skuNumInfoLis);
        return input;
    }
}
