package com.jdi.isc.open.soa.service.protocol.sdk.pool;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.open.soa.common.utils.CommonUtils;
import com.jdi.isc.open.soa.rpc.customer.RpcCustomerService;
import com.jdi.isc.open.soa.rpc.customerMku.IscProductSoaCustomerMkuWriteRpcService;
import com.jdi.isc.open.soa.sdk.common.LangConstant;
import com.jdi.isc.open.soa.sdk.pool.IscPoolWriteApiService;
import com.jdi.isc.open.soa.sdk.pool.req.PoolMkuReqDTO;
import com.jdi.isc.open.soa.sdk.pool.res.PoolMkuResDTO;
import com.jdi.isc.product.soa.api.customerMku.req.MkuCheckBindReqDTO;
import com.jdi.isc.product.soa.api.customerMku.res.MkuCheckBindResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 国际商品池出入池写服务
 * @description 提供国际商品池出入池写服务
 * <AUTHOR>
 * @date 2025/6/04
 */
@Slf4j
@Service
public class IscPoolWriteApiServiceImpl implements IscPoolWriteApiService {

    @Resource
    private IscProductSoaCustomerMkuWriteRpcService iscProductSoaCustomerMkuWriteRpcService;
    @Resource
    private RpcCustomerService rpcCustomerService;

    /**
     * 商品入池
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PoolMkuResDTO> addMkuToPool(PoolMkuReqDTO req) {
        //获取请求语种(决定异常报文语种)
        String lang = CommonUtils.getLangByUniformBizInfo(req.getUniformBizInfo());
        //获取客户简码
        String clientCode = rpcCustomerService.getClientCodeByContractNum(req.getContractNum());
        if(StringUtils.isBlank(clientCode)){
            return DataResponse.error(String.format("客户%s不存在,请检查",req.getContractNum()));
        }
        //调用SOA入客户池服务
        MkuCheckBindReqDTO input = new MkuCheckBindReqDTO();
        input.setClientCode(clientCode);
        input.setMkuIdList(req.getMkuIdList());
        input.setPin(req.getOperator());
        input.setLang(StringUtils.isNotBlank(lang)?lang: LangConstant.LANG_ZH);
        DataResponse<MkuCheckBindResDTO> res = iscProductSoaCustomerMkuWriteRpcService.checkAndBind(input);
        if(res == null){
            return DataResponse.error("系统异常,请联系管理员");
        }
        if(!res.getSuccess()){
            return DataResponse.error(res.getMessage());
        }
        return DataResponse.success(new PoolMkuResDTO(res.getData().getSuccessMkuMsgMap(),res.getData().getFailMkuMsgMap()));
    }

}
