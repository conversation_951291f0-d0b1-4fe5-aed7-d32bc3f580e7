package com.jdi.isc.open.soa.service.manage.mku;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.open.soa.sdk.mku.req.MkuAvailableSaleReqDTO;
import com.jdi.isc.open.soa.sdk.mku.res.IscMkuAvailableSaleRes;

import java.util.Map;

/**
 * MKU查询服务
 * <AUTHOR>
 * @description：MkuReadManageService
 * @Date 2025-08-18
 */
public interface MkuReadManageService {

    /**
     * 查询MKU可用销售信息。
     * @param req MKU可用销售请求参数。
     * @return MKU可用销售响应数据。
     */
    DataResponse<Map<Long, IscMkuAvailableSaleRes>> queryMkuAvailable(MkuAvailableSaleReqDTO req);
}
