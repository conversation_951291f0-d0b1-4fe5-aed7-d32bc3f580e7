package com.jdi.isc.vc.api.common.ducc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.laf.config.spring.annotation.LafValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * ducc 配置类信息
 *
 * @author: z<PERSON><PERSON>176
 * @date: 2025/6/4 15:54
 * @version:
 **/
@Slf4j
@Component
public class VcDuccConfig {

    @LafValue("vc.tab.config")
    private String tabConfig;

    public JSONObject buildPurchaseTabDTO(String tabType) {
        JSONObject json = JSON.parseObject(tabConfig);
        return json.getJSONObject(tabType);
    }

}
