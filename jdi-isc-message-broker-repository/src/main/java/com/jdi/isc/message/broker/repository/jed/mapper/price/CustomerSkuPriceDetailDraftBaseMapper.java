package com.jdi.isc.message.broker.repository.jed.mapper.price;

import com.jdi.isc.message.broker.domain.price.po.CustomerSkuPriceDetailDraftPO;
import com.jdi.isc.message.broker.repository.jed.mapper.common.BasicMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * sku客制化价格草稿明细dao
 * <AUTHOR>
 * @date 20231130
 */
@Mapper
public interface CustomerSkuPriceDetailDraftBaseMapper extends BasicMapper<CustomerSkuPriceDetailDraftPO> {

}




