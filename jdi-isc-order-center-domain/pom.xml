<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.jdi.isc.order.center</groupId>
        <artifactId>jdi-isc-order-center</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>jdi-isc-order-center-domain</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>jdi-isc-order-center-domain</name>
    <packaging>jar</packaging>


    <dependencies>
        <dependency>
            <groupId>com.jdi.isc.order.center</groupId>
            <artifactId>jdi-isc-order-center-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <artifactId>log4j-api</artifactId>
            <groupId>org.apache.logging.log4j</groupId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.binlake</groupId>
            <artifactId>binlake-wave.client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <artifactId>guava</artifactId>
            <groupId>com.google.guava</groupId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.aggregate.read</groupId>
            <artifactId>jdi-isc-aggregate-read-wiop-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.aggregate.read</groupId>
            <artifactId>jdi-isc-aggregate-read-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.product.soa</groupId>
            <artifactId>jdi-isc-product-soa-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.laf.config</groupId>
            <artifactId>laf-config-client-jd-springboot-starter</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.jd.fspinvoice</groupId>
            <artifactId>fspinvoice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.wisfin</groupId>
            <artifactId>jsf-wisfin-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.library</groupId>
            <artifactId>jdi-isc-library-common</artifactId>
        </dependency>
    </dependencies>

</project>
