package com.jdi.isc.order.center.domain.purchaseOrder.po;

import lombok.Data;

import java.util.List;

/**
 * 部分入库对象
 */
@Data
public class UpdatePurchaseOrderPartEnterWhBizPO {

    /**
     * 需要修改的采购单po
     */
    private PurchaseOrderPO purchaseOrderPO;

    /**
     * 需要修改的采购单商品po
     */
    private List<PurchaseOrderWarePO> purchaseOrderWarePOList;

    /**
     * 入仓po
     */
    private PurchaseOrderEnterWarehousePO purchaseOrderEnterWarehousePO;

    /**
     * 入仓商品po
     */
    private List<PurchaseOrderEnterWarehouseWarePO> purchaseOrderEnterWarehouseWarePOList;
}
