package com.jdi.isc.order.center.domain.mapstruct.purchaseOrder;

import com.jdi.isc.order.center.api.common.SystemInfoOrderApiReq;
import com.jdi.isc.order.center.api.purchaseOrder.biz.req.UpdatePurchaseOrderStatusApiReq;
import com.jdi.isc.order.center.api.purchaseOrder.biz.req.UpdatePurchaseOrderStatusManageApiReq;
import com.jdi.isc.order.center.api.purchaseOrder.biz.req.UpdatePurchaseOrdersStatusApiReq;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.UpdatePurchaseOrderStatusReqVO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.UpdatePurchaseOrdersStatusReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PurchaseOrderUpdateConvert {


    PurchaseOrderUpdateConvert INSTANCE = Mappers.getMapper(PurchaseOrderUpdateConvert.class);

    UpdatePurchaseOrderStatusReqVO apiReq2voReq(UpdatePurchaseOrderStatusApiReq updatePurchaseOrderStatusApiReq);

    UpdatePurchaseOrderStatusReqVO manageApiReq2voReq(UpdatePurchaseOrderStatusManageApiReq updatePurchaseOrderStatusManageApiReq);

    SystemInfoOrderApiReq req2ApiReq(UpdatePurchaseOrderStatusApiReq updatePurchaseOrderStatusApiReq);

    SystemInfoOrderApiReq manageReq2ApiReq(UpdatePurchaseOrderStatusManageApiReq updatePurchaseOrderStatusManageApiReq);

    UpdatePurchaseOrdersStatusReqVO apiReq2voReq(UpdatePurchaseOrdersStatusApiReq updatePurchaseOrdersStatusApiReq);

    SystemInfoOrderApiReq req2ApiReq(UpdatePurchaseOrdersStatusApiReq updatePurchaseOrdersStatusApiReq);

}
