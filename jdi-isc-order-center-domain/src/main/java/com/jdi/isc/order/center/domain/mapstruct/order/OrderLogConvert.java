package com.jdi.isc.order.center.domain.mapstruct.order;

import com.jdi.isc.order.center.api.biz.resp.OrderLogResp;
import com.jdi.isc.order.center.domain.order.po.OrderLogPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 订单对象转换
 * <AUTHOR>
 * @date 20231225
 */
@Mapper
public interface OrderLogConvert {

    OrderLogConvert INSTANCE = Mappers.getMapper(OrderLogConvert.class);

    List<OrderLogResp> listPo2Resp(List<OrderLogPO> orderLogList);

}
