package com.jdi.isc.order.center.domain.aftersales.biz;

import lombok.Data;

import java.util.List;

@Data
public class AfterSalesSubOperateStatusVo {


    /**
     * 表示售后子操作的状态值
     */
    private Integer status;


    /**
     * 表示售后子操作的具体操作类型或操作名称
     */
    private String operate;


    /**
     * 表示售后子操作的状态描述或说明信息
     */
    private String operateDesc;



    /**
     * 表示售后子操作在进行当前操作前的状态值列表
     */
    private List<Integer> beforeStatus;






}
