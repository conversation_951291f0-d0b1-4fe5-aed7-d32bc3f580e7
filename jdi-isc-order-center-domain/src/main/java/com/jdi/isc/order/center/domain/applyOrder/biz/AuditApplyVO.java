package com.jdi.isc.order.center.domain.applyOrder.biz;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 申请单表
 */
@Data
public class AuditApplyVO {

    /** 申请单Id*/
    @NotNull(message = "申请单Id不能为空.")
    private Long applyId;

    /** 申请单审批层级*/
    @NotNull(message = "申请单层级不能为空.")
    private Integer level;

    /** 审批操作人*/
    @NotNull(message = "审批操作人不能为空.")
    private String erp;
}
