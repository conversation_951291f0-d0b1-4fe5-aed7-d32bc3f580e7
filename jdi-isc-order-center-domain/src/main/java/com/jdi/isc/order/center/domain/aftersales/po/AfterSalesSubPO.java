package com.jdi.isc.order.center.domain.aftersales.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 售后单db实体
 * <AUTHOR>
 * @date 20240815
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "jdi_isc_after_sales_sub_sharding")
@NoArgsConstructor
public class AfterSalesSubPO extends BasicPO {

    /**
     * 售后子单单号
     */
    private String bizId;

    /**
     * AfterSalesSubTypeEnum
     * 单据类型100.承担费用200.取件维修300.换货补货400.退款
     */
    private Integer bizType;

    /**
     * 录入退款金额,4位小数
     */
    private BigDecimal recordRefundPrice;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 售后单号
     */
    private String afterSalesOrderId;

    /**
     * 京东国际订单号
     */
    private Long orderId;

    /**
     * AfterSalesSubStatusEnum
     * 售后单状态
     * 120 承担费用待审批
     * 200 待取件/维修、220取件/维修中、
     * 300 待换货/补货、320换货/补货中、
     * 400待退款、420退款中、
     * 99已完成、 2已取消（售后单关闭）
     */
    private Integer status;

    /**
     * 原因
     */
    private String reason;

    /**
     * 关注人
     */
    private String follower;

    /**
     * 完成时间
     */
    private Long completeTime;

    /**
     * 取消时间
     */
    private Long cancelTime;

    /**
     * 版本号
     */
    private Integer version;


}
