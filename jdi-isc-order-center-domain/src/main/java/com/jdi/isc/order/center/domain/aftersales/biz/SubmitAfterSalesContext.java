package com.jdi.isc.order.center.domain.aftersales.biz;

import com.jdi.isc.aggregate.read.api.customer.res.CustomerReadResp;
import com.jdi.isc.order.center.api.aftersales.req.AfterSalesSubmitReqDTO;
import com.jdi.isc.order.center.domain.aftersales.po.AfterSalesOperatePO;
import com.jdi.isc.order.center.domain.aftersales.po.AfterSalesPO;
import com.jdi.isc.order.center.domain.aftersales.po.AfterSalesWarePO;
import com.jdi.isc.order.center.domain.enums.customer.CustomerVO;
import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 提交售后单上下文
 * @date 2025/6/18 16:45
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SubmitAfterSalesContext {

    /**
     * 售后请求
     */
    private AfterSalesSubmitReqDTO afterSalesSubmitReqDTO;

    /**
     * 订单
     */
    private OrderPO orderPO;

    /**
     * 订单商品
     */
    private List<OrderWarePO> orderWarePOList;

    /**
     * 售后单
     */
    private AfterSalesPO afterSalesPO;

    /**
     * 售后商品
     */
    private List<AfterSalesWarePO> afterSalesWarePOList;

    /**
     * 售后操作
     */
    private AfterSalesOperatePO afterSalesOperatePO;

    /**
     * 客户信息
     */
    private CustomerReadResp customerReadResp;
}
