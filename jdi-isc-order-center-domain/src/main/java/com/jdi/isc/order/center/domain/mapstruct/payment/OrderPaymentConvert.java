package com.jdi.isc.order.center.domain.mapstruct.payment;


import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.domain.payment.biz.OrderPaymentMsgVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OrderPaymentConvert {

    OrderPaymentConvert INSTANCE = Mappers.getMapper(OrderPaymentConvert.class);

    @Mappings ({
            @Mapping(target = "createTime", expression = "java(orderPO.getCreateTime().getTime())"),
            @Mapping(target = "updateTime", expression = "java(orderPO.getUpdateTime().getTime())")
    })
    OrderPaymentMsgVO orderPo2OrderPaymentConvert(OrderPO orderPO);

}
