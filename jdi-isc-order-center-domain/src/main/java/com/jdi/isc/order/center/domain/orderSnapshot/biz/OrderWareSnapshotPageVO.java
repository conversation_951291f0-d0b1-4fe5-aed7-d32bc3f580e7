package com.jdi.isc.order.center.domain.orderSnapshot.biz;

import com.jdi.isc.library.common.domain.common.biz.BasicPageVO;
import com.jdi.isc.library.common.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 订单商品快照分页查询对象
 * @Author: zhaojianguo21
 * @Date: 2025/05/26 20:48
 **/

@Data
public class OrderWareSnapshotPageVO implements Serializable {


    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Request extends BasicPageVO implements Serializable {

    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BasicVO implements Serializable{
        /**
         * 订单号
         */
        private Long orderId;

        /**
         * mku编码
         */
        private Long mkuId;

        /**
         * 履约sku编码
         */
        private Long skuId;

        /**
         * 商品快照url
         */
        private String snapshotUrl;

        /**
         * 创建快照开始时间
         */
        private Long snapshotStartTime;

        /**
         * 创建快照结束时间
         */
        private Long snapshotEndTime;

        /**
         * 快照状态,0=未创建,1=创建成功,2=创建失败,3=部分信息创建失败
         */
        private Integer snapshotStatus;

    }

}