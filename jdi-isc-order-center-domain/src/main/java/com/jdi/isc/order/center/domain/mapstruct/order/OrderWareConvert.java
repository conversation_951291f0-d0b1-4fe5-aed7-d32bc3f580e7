package com.jdi.isc.order.center.domain.mapstruct.order;

import com.jdi.isc.order.center.api.biz.resp.BROrderWareTaxDTO;
import com.jdi.isc.order.center.api.orderRead.biz.manage.OrderReadManageWareApiDTO;
import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface OrderWareConvert {

    OrderWareConvert INSTANCE = Mappers.getMapper(OrderWareConvert.class);
    BROrderWareTaxDTO po2vo(OrderWarePO orderWarePO);

    List<BROrderWareTaxDTO> listPo2vo(List<OrderWarePO> orderWarePOList);

    OrderWarePO copyPo(OrderWarePO orderWarePO);

    List<OrderReadManageWareApiDTO> orderWarePo2ManageWareList(List<OrderWarePO> orderWarePOList);

}
