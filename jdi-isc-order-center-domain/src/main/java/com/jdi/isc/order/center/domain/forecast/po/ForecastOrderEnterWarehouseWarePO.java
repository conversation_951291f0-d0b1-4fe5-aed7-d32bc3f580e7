package com.jdi.isc.order.center.domain.forecast.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;


/**
 * 预报备货单入仓信息商品表
 * @TableName jdi_isc_forecast_order_sharding
 */


@TableName(value ="jdi_isc_forecast_order_enter_warehouse_ware_sharding")
@Data
public class ForecastOrderEnterWarehouseWarePO extends BasicPO {



    /**
     * 预报备货单号
     */
    private String forecastOrderId;


    /**
     * 第三方来源标识
     */
    private String dataSource;

    /**
     * 第三方唯一标识
     */
    private String uniqueFlag;

    /**
     * 第三方入库单号
     */
    private String thirdCode;

    /**
     * 入仓商品ID
     */
    private Long skuId;

    /**
     * 入仓商品数量
     */
    private Integer num;





}
