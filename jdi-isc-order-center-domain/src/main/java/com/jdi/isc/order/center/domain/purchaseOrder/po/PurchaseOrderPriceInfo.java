package com.jdi.isc.order.center.domain.purchaseOrder.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 采购单中各种金额的base信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PurchaseOrderPriceInfo {

    /**
     * 原始币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currencySource;

    /**
     * 采购币种对人民币的汇率
     */
    private BigDecimal exchangeRateRmb;

}
