package com.jdi.isc.order.center.domain.orderSnapshot.req;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2025/5/20 21:36
 **/
@Data
public class OrderWareSnapshotCreateReqVO implements Serializable {

    /**
     * 指定各mku的各模块重新生成快照策略
     */
    private Map<Long/*mkuId*/, Map<Integer/*业务模块*/,Boolean/*重新生成模块快照信息*/>> mkuIdStrategyMap;

}
