package com.jdi.isc.order.center.domain.orderArchive.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 订单三方归档 实体类
 * @Author: zhaojianguo21
 * @Date: 2024/12/27 22:18
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("jdi_isc_order_third_archive_sharding")
public class OrderThirdArchivePO extends BasicPO {

    /**
     * 订单
     */
    private Long orderId;

    /**
     * 父订单号
     */
    private Long pOrderId;

    /**
     * 合同号
     */
    private String contractNum;

    /**
     * 客户号
     */
    private String clientCode;

    /**
     * bpm流程单号
     */
    private String bpmId;

    /**
     * 归档时间
     */
    private Long archiveTime;

    /**
     * 归档批次，自动生成
     */
    private String archiveBatchNum;

    /**
     * 订单含税金额
     */
    private BigDecimal orderPrice;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 工厂名称（即发票抬头）
     */
    private String companyName;

    /**
     * 成本中心
     */
    private String costCenter;

    /**
     * 费用科目
     */
    private String loanPro;

    /**
     * 申请人工号
     */
    private String empNo;

    /**
     * 需求人工号
     */
    private String demandNo;

    /**
     * 工厂代码（与工厂名称相对应）
     */
    private String companyCode;

    /**
     * 开票状态：0=待开票，1=开票中，2=开票成功，3=开票失败
     */
    private Integer makeInvoiceStatus;

    /**
     * 开票批次号
     */
    private String invoiceBatchNum;
}