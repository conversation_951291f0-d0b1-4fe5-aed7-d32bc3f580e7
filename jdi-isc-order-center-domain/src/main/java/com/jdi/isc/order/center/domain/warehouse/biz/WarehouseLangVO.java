package com.jdi.isc.order.center.domain.warehouse.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: 仓信息多语言表 VO实体类
 * @Author: wangpeng965
 * @Date: 2024/07/18 11:30
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class WarehouseLangVO extends BasicVO {
    /**
     * 仓库id
     */
    private String warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库编码
     */
    private String warehouseNo;

    /**
     * 仓的名字语言 0:中文 1:英文 2:泰文 3:越南文
     */
    private Integer lang;
}
