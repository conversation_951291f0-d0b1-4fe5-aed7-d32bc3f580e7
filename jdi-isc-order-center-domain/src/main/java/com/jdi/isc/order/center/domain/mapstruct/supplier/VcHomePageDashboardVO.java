package com.jdi.isc.order.center.domain.mapstruct.supplier;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VcHomePageDashboardVO implements Serializable {



    /**
     * 供应商简码
     */
    private String supplierCode;
    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 开始时间
     */
    private long startTime;

    /**
     * 结束时间
     */
    private long endTime;

    /**
     * 对比开始时间
     */
    private long compareStartTime;

    /**
     * 对比结束时间
     */
    private long compareEndTime;


    /**
     * 语言设置
     */
    private String lang;
}
