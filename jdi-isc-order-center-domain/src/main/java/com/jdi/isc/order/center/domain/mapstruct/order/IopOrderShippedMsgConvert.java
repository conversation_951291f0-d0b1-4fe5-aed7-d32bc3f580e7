package com.jdi.isc.order.center.domain.mapstruct.order;

import com.jdi.isc.order.center.domain.order.biz.*;
import com.jdi.isc.order.center.domain.order.biz.shipped.GptSoaB2bOutStockOrderInfoMsg;
import com.jdi.isc.order.center.domain.order.po.IopOrderShippedMsgPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Description: 订单发货信息表对象转换
 * @Author: liudong21
 * @Date: 2024/05/06 20:50
 **/
@Mapper
public interface IopOrderShippedMsgConvert {

    IopOrderShippedMsgConvert INSTANCE = Mappers.getMapper(IopOrderShippedMsgConvert.class);

    IopOrderShippedMsgPO vo2Po(IopOrderShippedMsgVO vo);

    @Mapping(target="orderId", source="jdOrderId")
    IopOrderShippedMsgPO gptSoaB2bOutStockMsg2PO(GptSoaB2bOutStockOrderInfoMsg orderInfo);
}
