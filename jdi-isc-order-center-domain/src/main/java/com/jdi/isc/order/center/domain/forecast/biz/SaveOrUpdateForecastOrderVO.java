package com.jdi.isc.order.center.domain.forecast.biz;

import com.jdi.isc.order.center.domain.forecast.po.ForecastOrderConsigneePO;
import com.jdi.isc.order.center.domain.forecast.po.ForecastOrderPO;
import com.jdi.isc.order.center.domain.forecast.po.ForecastOrderWarePO;
import com.jdi.isc.order.center.domain.order.po.OrderBizInfoPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderConsigneePO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderWarePO;
import lombok.Data;

import java.util.List;

/**
 * 修改或更新采购单信息
 */
@Data
public class SaveOrUpdateForecastOrderVO {

    /**
     * 采购单
     */
    private ForecastOrderPO forecastOrderPO;

//    /**
//     * 跨境biz信息
//     */
//    private OrderBizInfoPO orderBizInfoPO;

    /**
     * 商品信息
     */
    private List<ForecastOrderWarePO> forecastOrderWarePOList;

    /**
     * 采购单收货地址
     */
    private List<ForecastOrderConsigneePO> forecastOrderConsigneePOList;
}
