package com.jdi.isc.order.center.domain.purchaseOrder.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 采购单结算表
 */
@TableName(value = "jdi_isc_purchase_order_settlement_sharding")
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderSettlementPO extends BasicPO {

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * 结算状态
     */
    private Integer settlementStatus;

    /**
     * 订单版本号
     */
    private Integer version;


    /**
     * 结算资料
     */
    private String settlementInfo;

    /**
     * 修改采购单结算的客户端信息
     */
    private String updateClientInfo;

}