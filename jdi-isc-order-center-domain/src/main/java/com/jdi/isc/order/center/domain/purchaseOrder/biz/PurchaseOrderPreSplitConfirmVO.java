package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import lombok.Data;

import java.util.List;

@Data
public class PurchaseOrderPreSplitConfirmVO {

    private PurchaseOrderPO updateParentOrderPO;

    private List<OrderPO> successOrderPOList;

    private List<OrderPO> failOrderPOList;

    private List<PurchaseOrderPO> purchaseOrderPOList;
}
