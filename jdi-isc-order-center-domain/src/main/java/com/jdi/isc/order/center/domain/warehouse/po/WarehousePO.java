package com.jdi.isc.order.center.domain.warehouse.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @Description: 仓信息表 实体类
 * @Author: wangpeng965
 * @Date: 2024/07/18 11:23
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class WarehousePO extends BasicPO {
    /**
     * 仓库编码
     */
    private String warehouseNo;

    /**
     * 仓库类型 0:备货仓 1:集货仓
     */
    private Integer type;

    /**
     * 国家/地区编码、CN、TH
     */
    private String countryCode;

    /**
     * 国家/地区id
     */
    private String countryId;

    /**
     * 国家/地区名称
     */
    private String countryName;

    /**
     * 所属洲/省编码
     */
    private Long provinceId;

    /**
     * 所属洲/省编码
     */
    private String provinceName;

    /**
     * 所属城市编码
     */
    private Long cityId;

    /**
     * 所属城市名称
     */
    private String cityName;

    /**
     * 所属县/区编码
     */
    private Long countyId;

    /**
     * 所属县/区名称
     */
    private String countyName;

    /**
     * 所属乡/镇编码
     */
    private Long townId;

    /**
     * 所属乡/镇名称
     */
    private String townName;

    /**
     * 邮政编码
     */
    private String zip;

    /**
     * 收货人详细地址
     */
    private String consigneeAddressEncrypt;

    /**
     * 收货人详细地址加密
     */
    private String consigneeAddressIndex;

    /**
     * 联系人id
     */
    private Long consigneeId;

    /**
     * 联系人名称加密
     */
    private String consigneeEncrypt;

    /**
     * 联系人名称
     */
    private String consigneeNameIndex;

    /**
     * 收货人电话加密
     */
    private String consigneePhoneEncrypt;

    /**
     * 收货人电话
     */
    private String consigneePhoneIndex;

    /**
     * 收货人手机号加密
     */
    private String consigneeMobileEncrypt;

    /**
     * 收货人手机号
     */
    private String consigneeMobileIndex;

    /**
     * 收货人邮箱加密
     */
    private String consigneeEmailEncrypt;

    /**
     * 收货人邮箱
     */
    private String consigneeEmailIndex;

    /**
     * 理由
     */
    private String reason;

    /**
     * 多语言名称
     */
    private Map<String, String> warehouseNameMap;

    /**
     *  备货仓名称
     */
    private String warehouseName;
}
