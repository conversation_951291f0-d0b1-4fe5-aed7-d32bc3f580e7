package com.jdi.isc.order.center.domain.aftersales.biz;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 售后单查询请求实体
 * <AUTHOR>
 * @date 20240815
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CreateAfterSalesSubVo  {


    /**
     * 单据类型100.承担费用200.取件维修300.换货补货400.退款
     */
    @NotNull(message = "bizType is not null")
    private Integer bizType;


    /**
     * 售后单号
     */
    @NotNull(message = "afterSalesOrderId is not null")
    private String afterSalesOrderId;







}
