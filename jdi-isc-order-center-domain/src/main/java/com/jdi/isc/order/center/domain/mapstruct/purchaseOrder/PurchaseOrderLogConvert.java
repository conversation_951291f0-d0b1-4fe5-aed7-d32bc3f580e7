package com.jdi.isc.order.center.domain.mapstruct.purchaseOrder;

import com.jdi.isc.order.center.api.biz.resp.PurchaseOrderLogResp;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderLogPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PurchaseOrderLogConvert {


    PurchaseOrderLogConvert INSTANCE = Mappers.getMapper(PurchaseOrderLogConvert.class);

    List<PurchaseOrderLogResp> listPo2Resp(List<PurchaseOrderLogPO> purchaseOrderLogList);
}
