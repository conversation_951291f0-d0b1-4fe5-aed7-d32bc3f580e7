package com.jdi.isc.order.center.domain.declaration.biz;

import com.jdi.isc.order.center.common.biz.BasePageVO;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 报关信息表
 * <AUTHOR>
 * @date 20231109
 */

@Data
public class CustomsDeclarationMainReq extends BasePageVO {


    /**
     * 境内发货人关联ID
     * */
    private Long domesticShipperId;

    /**
     * 境外收货人关联ID
     * */
    private Long overseasConsigneeId;

    /**
     * 生产销售单位关联ID
     * */
    private Long productionId;

    /**
     * 申报口岸
     * */
    private String portDeclare;

    /**
     * 始发口岸
     * */
    private String portOrigin;

    /**
     * 目的口岸
     * */
    private String portDestination;

    /**
     * 监管方式
     * */
    private String superviseWay;

    /**
     * 成交方式（FOB/CIF）
     * */
    private String dealWay;

    /**
     * 运输方式1海运2空运3铁运4陆运
     * */
    private Integer transportMode;

    /**
     * 报关单状态1新建3确认5已放行
     * */
    private Integer status;

    /**
     * 航班号
     * */
    private String flightNumber;

    /**
     * 预计离港时间
     * */
    private Long etd;

    /**
     * 预计到达时间
     * */
    private Long eta;

    /**
     * 保费
     * */
    private BigDecimal insPrm;

    /**
     * 杂费
     * */
    private BigDecimal miscExp;

    /**
     *  运费
     *  */
    private BigDecimal freightFee;

    /**
     * 报关单号
     * */
    private String customsId;

    /**
     *  报关服务商名称
     *  */
    private String customsAgentName;

    /**
     * 报关服务商Id
     * */
    private String customsAgentId;

    /**
     * 报关单金额/销售金额
     * */
    private BigDecimal customsAmount;

    /**
     * 申报日期
     * */
    private Long declareTime;

    /**
     * 放行日期
     * */
    private Long releaseTime;

    /**
     * 提单号
     * */
    private String billNo;



}
