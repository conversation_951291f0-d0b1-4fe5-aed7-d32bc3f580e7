package com.jdi.isc.order.center.domain.forecast.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;


/**
 * 预报备货单入仓信息表
 * @TableName jdi_isc_forecast_order_sharding
 */


@TableName(value ="jdi_isc_forecast_order_enter_warehouse_sharding")
@Data
public class ForecastOrderEnterWarehousePO extends BasicPO {



    /**
     * 预报备货单号
     */
    private String forecastOrderId;


    /**
     * 入仓时间（时间戳）
     */
    private Long enterWarehouseDate;

    /**
     * 入仓资料
     */
    private String enterMaterial;

    /**
     * 第三方来源标识
     */
    private String dataSource;

    /**
     * 第三方唯一标识
     */
    private String uniqueFlag;

    /**
     * 第三方入库单号
     */
    private String thirdCode;








}
