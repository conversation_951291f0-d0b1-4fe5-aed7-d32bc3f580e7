package com.jdi.isc.order.center.domain.forecast.po;

import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderEnterWarehousePO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderEnterWarehouseWarePO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderWarePO;
import lombok.Data;

import java.util.List;

/**
 * 部分入库对象
 */
@Data
public class UpdateForecastOrderPartEnterWhBizPO {

    /**
     * 需要修改的采购单po
     */
    private ForecastOrderPO forecastOrderPO;

    /**
     * 需要修改的采购单商品po
     */
    private List<ForecastOrderWarePO> forecastOrderWarePOList;

    /**
     * 入仓po
     */
    private ForecastOrderEnterWarehousePO forecastOrderEnterWarehousePO;

    /**
     * 入仓商品po
     */
    private List<ForecastOrderEnterWarehouseWarePO> forecastOrderEnterWarehouseWarePOList;
}
