package com.jdi.isc.order.center.domain.promiseTime.res;

import com.jdi.isc.order.center.domain.enums.promiseTime.UpdateExtInfoPromiseTimeResultEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2025/6/12 18:34
 **/
@Data
@AllArgsConstructor
@Builder
public class UpdateBizOrderExtPromiseTimeRes {

    private String bizId;

    /**
     * 订单扩展信息PO是否发生变化
     */
    private Boolean orderExtInfoPoChanged;


    /**
     * 承诺发货时间 订单确认时间 + 发货时效(d)
     */
    private Long promisedShipmentTime;

    /**
     * 承诺送达时间(订单确认时间+发货时效+max配送时效)
     */
    private Long promisedDeliveryTime;

    /**
     * 更新发货时间结果
     */
    private UpdateExtInfoPromiseTimeResultEnum promisedShipmentTimeResult;

    /**
     * 更新送达时间结果
     */
    private UpdateExtInfoPromiseTimeResultEnum promisedDeliveryTimeResult;

}
