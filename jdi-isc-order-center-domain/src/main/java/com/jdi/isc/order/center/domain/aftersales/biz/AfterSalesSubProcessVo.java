package com.jdi.isc.order.center.domain.aftersales.biz;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 售后单查询请求实体
 * <AUTHOR>
 * @date 20240815
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AfterSalesSubProcessVo  {


    /**
     * 单据类型100.承担费用200.取件维修300.换货补货400.退款
     */
    @NotNull(message = "bizType is not null")
    private Integer bizType;


    /**
     * 售后单号
     */
    @NotNull(message = "afterSalesOrderId is not null")
    private String afterSalesOrderId;

    /**
     * AfterSalesSubStatusEnum
     * 售后单状态
     * 120 承担费用待审批
     * 200 待取件/维修、220取件/维修中、
     * 300 待换货/补货、320换货/补货中、
     * 400待退款、420退款中、
     * 99已完成、 2已取消（售后单关闭）
     */
    private Integer status;



    /**
     * 操作类型或操作标识，用于记录对售后单执行的具体操作
     * DUCC查看afterSales.subOperateStatus的值
     */
    private String operateCode;


    /**
     * 原因
     */
    private String reason;



    /**
     * 录入退款金额,4位小数
     */
    private BigDecimal recordRefundPrice;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;







}
