package com.jdi.isc.order.center.domain.forecast.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;


/**
 * 预报备货单商品表
 * @TableName jdi_isc_forecast_order_sharding
 */


@TableName(value ="jdi_isc_forecast_order_ware_sharding")
@Data
public class ForecastOrderWarePO extends BasicPO {



    /**
     * 预报备货单号
     */
    private String forecastOrderId;

    /**
     * 履约SKU编码
     */
    private Long skuId;

    /**
     * SKU数量
     */
    private Integer skuNum;

    /**
     * SKU类型
     * 0：商品
     * 1：附件
     * 2：赠品
     */
    private Integer skuType ;

    /**
     * 父类SKU（主要赠品附件用）
     */
    private Long parentSkuId ;

    /**
     * 国家码
     */
    private String skuCountryCode;

    /**
     * SKU信息快照（不含商详）
     */
    private String skuJsonInfo;

    /**
     * 商品入库数量（商品表的加合）
     */
    private Integer inboundWarehouseWareNum;



    /**
     * 版本号
     */
    private Integer version; // 版本号


    /**
     * 用于拆分的key
     */
    @TableField(exist = false)
    private String splitKey;



}
