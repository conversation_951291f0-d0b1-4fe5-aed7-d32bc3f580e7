package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @Date
 */
@Data
public class InvoiceCountVO {

    /**
     * 发票审核状态
     */
    private Integer invoiceApprovalStatus;

    /**
     * 国家或地区的代码。
     */
    private String countryCode;

    /**
     * 供应商代码
     */
    private String supplierCode;


    /**
     * 订单类型
     */
    private Integer purchaseOrderType;

    /**
     * 采购单状态的集合。
     */
    private Set<Integer> purchaseOrderStatusSet;

}
