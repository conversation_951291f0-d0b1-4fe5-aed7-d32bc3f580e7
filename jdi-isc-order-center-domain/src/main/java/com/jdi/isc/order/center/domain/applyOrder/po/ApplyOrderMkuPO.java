package com.jdi.isc.order.center.domain.applyOrder.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 申请单商品表
 */
@TableName(value = "jdi_isc_apply_order_mku_sharding")
@Data
public class ApplyOrderMkuPO  {

    /** '申请单号' */
    private Long applyId;
    /** 'mkuId' */
    private Long mkuId;
    /** '数量' */
    private Long num;
    /** '币种:VND越南,THB泰国,CNY人民币,USD美元' */
    private String currency;
    /** '含税销售价' */
    private BigDecimal salePrice;
    /** '销售单位' */
    private String saleUnit;

    /** 自增ID*/
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 最后修改时间
     */
    private Long updateTime;

    /**
     * 逻辑删除 0=删除,1有效
     */
    private Integer yn;

}