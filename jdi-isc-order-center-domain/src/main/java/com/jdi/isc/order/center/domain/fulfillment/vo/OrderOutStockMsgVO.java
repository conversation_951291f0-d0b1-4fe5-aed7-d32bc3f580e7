package com.jdi.isc.order.center.domain.fulfillment.vo;

import lombok.Data;

import java.util.List;

@Data
public class OrderOutStockMsgVO {

    /**
     * 来源
     */
    private String dataSource;

    /**
     * 变更事件
     */
    private String event;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 商品明细
     */
    private List<OrderOutStockMsgDetailVO> detailList;

    /**
     * 三方单号
     */
    private String thirdCode;
}
