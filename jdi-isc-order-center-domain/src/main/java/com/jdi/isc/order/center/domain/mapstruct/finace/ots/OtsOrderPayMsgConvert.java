package com.jdi.isc.order.center.domain.mapstruct.finace.ots;

import com.jdi.isc.order.center.domain.finace.biz.OrderPayMsgVO;
import com.jdi.isc.order.center.domain.finace.po.OrderPayMsgPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 订单待支付消息对象转换
 * @Author: zhaojianguo21
 * @Date: 2024/05/22
 **/
@Mapper
public interface OtsOrderPayMsgConvert {

    OtsOrderPayMsgConvert INSTANCE = Mappers.getMapper(OtsOrderPayMsgConvert.class);

    OrderPayMsgPO vo2Po(OrderPayMsgVO vo);

    OrderPayMsgVO po2Vo(OrderPayMsgPO po);

    List<OrderPayMsgVO> listPo2Vo(List<OrderPayMsgPO> poList);

    List<OrderPayMsgPO> listVo2Po(List<OrderPayMsgVO> voList);

}
