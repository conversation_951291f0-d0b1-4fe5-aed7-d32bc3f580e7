package com.jdi.isc.order.center.domain.forecast.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.PurchaseOrderEnterWarehouseWareVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * <AUTHOR>
 * @Description 采购单入仓信息
 * @Date 2024/5/27 9:53 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ForecastOrderEnterWarehouseVO extends BasicVO {

    /**
     * 预报备货单的唯一标识号
     */
    private String forecastOrderId; // 预报备货单号

    /**
     * 入仓时间
     */
    private long enterWarehouseDate;

    /**
     * 入仓资料
     */
    private String enterMaterial;
    /**
     * 入仓视频资料
     */
    private String enterMaterialVideo;

    /**
     * 第三方来源标识
     */
    private String dataSource;

    /**
     *  唯一标识
     */
    private String uniqueFlag;

    /**
     * 入库单号
     */
    private String thirdCode;

    /**
     * 入仓商品vo
     */
    private List<ForecastOrderEnterWarehouseWareVO> forecastOrderEnterWarehouseWareVOList;
}
