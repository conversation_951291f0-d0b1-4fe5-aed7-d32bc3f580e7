package com.jdi.isc.order.center.domain.warehouse.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: 仓信息对应sku映射 实体类
 * @Author: wangpeng965
 * @Date: 2024/07/18 11:33
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("jdi_isc_warehouse_sku_sharding")
public class WarehouseSkuPO extends BasicPO {

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String skuName;
}
