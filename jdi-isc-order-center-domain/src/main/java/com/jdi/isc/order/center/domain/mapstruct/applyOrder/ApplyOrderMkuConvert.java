package com.jdi.isc.order.center.domain.mapstruct.applyOrder;

import com.jdi.isc.order.center.domain.applyOrder.biz.ApplyOrderMkuVO;
import com.jdi.isc.order.center.domain.applyOrder.po.ApplyOrderMkuPO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 对象转换
 * <AUTHOR>
 * @date 20240501
 **/
@Mapper
public interface ApplyOrderMkuConvert {

    ApplyOrderMkuConvert INSTANCE = Mappers.getMapper(ApplyOrderMkuConvert.class);


    @InheritConfiguration
    ApplyOrderMkuPO vo2po(ApplyOrderMkuVO applyOrderMkuVO);

    @InheritConfiguration
    ApplyOrderMkuVO po2vo(ApplyOrderMkuPO applyOrderMkuPO);

    @InheritConfiguration
    List<ApplyOrderMkuPO> listVo2Po(List<ApplyOrderMkuVO> applyOrderMkuVO);

    @InheritConfiguration
    List<ApplyOrderMkuVO> listPo2Vo(List<ApplyOrderMkuPO> applyOrderMkuPO);
}
