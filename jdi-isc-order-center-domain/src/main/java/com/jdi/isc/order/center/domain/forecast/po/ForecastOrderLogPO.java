package com.jdi.isc.order.center.domain.forecast.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;


/**
 * 预报备货日志表
 * @TableName jdi_isc_forecast_order_sharding
 */


@TableName(value ="jdi_isc_forecast_order_log_sharding")
@Data
public class ForecastOrderLogPO extends BasicPO {



    /**
     * 预报备货单号
     */
    private String forecastOrderId;


    /**
     * 目的国国家码
     */
    private String countryCode;

    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * 订单状态
     */
    private Integer forecastOrderStatus;

    /**
     * 预报备货单信息
     */
    private String forecastOrderInfo;

    /**
     * 预报备货单商品信息
     */
    private String forecastOrderWareInfo;


    /**
     * 版本号
     */
    private Integer version; // 版本号






}
