package com.jdi.isc.order.center.domain.orderArchive.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 订单三方归档商品 实体类
 * @Author: zhaojianguo21
 * @Date: 2024/12/27 22:02
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("jdi_isc_order_third_archive_ware_sharding")
public class OrderThirdArchiveWarePO extends BasicPO {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * mku编码
     */
    private Long mkuId;

    /**
     * 履约sku编码
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 商品税率
     */
    private BigDecimal tax;

    /**
     * 商品未税单价,4位小数
     */
    private BigDecimal nakedPrice;

    /**
     * 商品含税单价,4位小数
     */
    private BigDecimal price;

    /**
     * 归档批次，自动生成
     */
    private String archiveBatchNum;
    
}
