package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 类描述：
 * 采购单修改价格的实体
 *
 * <AUTHOR>
 * @date 2024/10/10
 */

@Data
public class PurchaseOrderPriceToolDTO {

    /**
     * 采购单号
     */
    @NotNull(message = "purchaseOrderId 不能为空")
    private String purchaseOrderId;


    /**
     * 采购单中商品的单价
     */
    private BigDecimal skuPrice;


    /**
     * 税额信息
     */
    private BigDecimal tax;


    /**
     * 申请人erp
     */
    @NotNull(message = "applyErp 不能为空")
    private String applyErp;


    /**
     * 订单ID，用于关联具体的订单信息。
     */
    private Long orderId;


}
      