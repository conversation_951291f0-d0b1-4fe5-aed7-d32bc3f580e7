package com.jdi.isc.order.center.domain.declaration.po;


import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 报关信息配置
 * <AUTHOR>
 * @date 20241109
 */


@Data
@EqualsAndHashCode(callSuper = true)
@TableName("jdi_isc_customs_config")
@AllArgsConstructor
@NoArgsConstructor
public class CustomsConfigPo extends BasicPO {


    /** 业务名 */
    private String bizName;

    /** 业务编码 */
    private String bizCode;

    /**
     * 业务类型
     * 1:境内发货人
     * 2境外收件人
     * 3生成销售单位
     * */
    private Integer type;

    /**
     * 传真号码
     */
    private String fax;

    /**
     * 地址
     */
    private String address;

    /**
     * 英文地址
     */
    private String addressEn;

    /**
     * 中文名称
     */
    private String name;

    /**
     * 英文名称
     */
    private String nameEn;

    /**
     * 电话
     */
    private String phone;


}
