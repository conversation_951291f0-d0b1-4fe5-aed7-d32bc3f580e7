package com.jdi.isc.order.center.domain.waybill.biz;

import com.jdi.isc.order.center.api.common.BasicDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 订单签收单
 * @Author: zhaojianguo21
 * @Date: 2024/12/23 22:47
 **/
@Data
public class QueryOrderSignatureFormReqRes implements Serializable {

    @Data
    @NoArgsConstructor
    public static class Request implements Serializable {
        /**
         * 用户pin
         */
        protected String pin;

        /**
         * 客户编码
         */
        protected String clientCode;

        /**
         * 合同号
         */
        protected String contractNum;

        /**
         * 请求国家语种信息
         */
        private String lang;

        /**
         * 签收单查询条件
         */
        @NotEmpty(message = "订单条件不能为空")
        @Size(min=1, max = 100, message = "单次查询的订单数应为1至100")
        private List<QueryOrderSignatureFormConditionReq> queryOrders;
    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BasicDTO implements Serializable{
        /**
         * 查询结果
         */
        private Boolean success;

        /**
         * 失败信息
         */
        private String failMessage;

        /**
         * 订单号
         */
        private Long orderId;

        /**
         * 三方订单号/唯一ID
         */
        private String thirdOrderId;

        /**
         * 签收单
         */
        private List<String> signatureFormUrls;
    }

    @Data
    @NoArgsConstructor
    public static class AssembleReq implements Serializable {
        /**
         * 订单号
         */
        private Set<Long> orderIds;

        private Map<String,Long> validReqAndOrderIdMap;
    }

}
