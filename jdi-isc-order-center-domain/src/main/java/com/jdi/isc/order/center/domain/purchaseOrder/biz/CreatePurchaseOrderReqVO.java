package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.order.po.OrderBizInfoPO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import lombok.Data;

import java.util.List;

/**
 * 创建采购单上下文
 */
@Data
public class CreatePurchaseOrderReqVO {

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 订单
     */
    private OrderPO orderPO;

    /**
     * 订单商品
     */
    private List<OrderWarePO> orderWarePOList;

    /**
     * 用户ip
     */
    private String userIp;

    /**
     * 老的采购单
     */
    private PurchaseOrderPO oldPurchaseOrderPO;

}
