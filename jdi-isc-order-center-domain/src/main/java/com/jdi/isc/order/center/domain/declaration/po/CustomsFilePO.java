package com.jdi.isc.order.center.domain.declaration.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 报关附件信息
 * <AUTHOR>
 * @date 20241109
 */


@Data
@EqualsAndHashCode(callSuper = true)
@TableName("jdi_isc_customs_file")
@AllArgsConstructor
@NoArgsConstructor
public class CustomsFilePO extends BasicPO {


    /**
     * 关联报关单信息id表
     */
    @TableField("customs_main_id")
    private Long customsMainId;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /*
     * 文件类型
     * 依据枚举类
     * DeclarationFileTypeEnum
     * com.jdi.isc.product.center.domain.enums
     * */
    @TableField("file_type")
    private Integer fileType;

    /**
     * 文件链接
     */
    @TableField("file_url")
    private String fileUrl;

    /**
     * 文件名称类型【区分不同的业务文件】
     */
    @TableField("file_name_type")
    private String fileNameType;



}
