package com.jdi.isc.order.center.domain.purchaseOrder.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.common.constants.Constant;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> 采购单商品表
 */
@TableName(value = "jdi_isc_purchase_order_ware_sharding")
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderWarePO extends BasicPO {

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * mku编码
     */
    private Long mkuId;

    /**
     * 履约sku编码
     */
    private Long skuId;

    /**
     * sku末级类目id
     */
    private Long catId;

    /**
     * sku的品牌id
     */
    private Long brandId;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 商品采购价,4位小数
     */
    private BigDecimal purchasePrice;

    /**
     * 商品小计,4位小数
     */
    private BigDecimal skuAllPrice;

    /**
     * sku数量
     */
    private Integer skuNum;

    /**
     * sku类型 0：商品 1：附件 2：赠品
     */
    private Integer skuType;

    /**
     * 父类sku 主要赠品附件用
     */
    private Long parentSkuId;

    /**
     * 国家码
     */
    private String skuCountryCode;

    /**
     * 商品扩展信息
     */
    private String skuJsonInfo;

    /**
     * mku类型 0：商品 1：附件 2：赠品
     */
    private Integer mkuType;

    /**
     * 父类mku 主要赠品附件用
     */
    private Long parentMkuId;

    /**
     * 国家码
     */
    private String mkuCountryCode;

    /**
     * mku信息快照，不含商详
     */
    private String mkuJsonInfo;

    /**
     * 含税价
     */
    private BigDecimal includeTaxPrice;

    /**
     * 增值税
     */
    private BigDecimal valueAddedTax;

    /**
     * 增值税率
     */
    private BigDecimal valueAddedTaxRate;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 实际入库商品数量
     */
    private Integer inboundWarehouseWareNum;

    @TableField(exist = false)
    private String uuid;

    public String getUuid(){
        return this.purchaseOrderId + Constant.UNDER_LINE + this.skuId;
    }


    /**
     * 增值税信息
     */
    private String valueAddedTaxesInfo;

    /**
     * 用于拆分的key
     */
    @TableField(exist = false)
    private String splitKey;

    /**
     * 商品采购价
     */
    private BigDecimal purchasePriceRmb;

    /**
     * 商品小计
     */
    private BigDecimal skuAllPriceRmb;

    /**
     * 含税价
     */
    private BigDecimal includeTaxPriceRmb;

    /**
     * 增值税
     */
    private BigDecimal valueAddedTaxRmb;

}