package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.parcel.biz.PurchaseOrderParcelVO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.supplier.biz.SupplierBaseInfoVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 采购单mq消息包装类；
 * @Author: zhaojianguo21
 * @Date: 2024/09/6
 **/

@Data
public class PurchaseOrderWrapperMqMsgVO extends PurchaseOrderPO {

    /**
     * 采购单扩展信息
     */
    private PurchaseOrderExtendResVO extendInfo;
    /**
     * 商品信息
     */
    private List<PurchaseOrderWareVO> wareList;
    /**
     * 包裹
     * */
    private List<PurchaseOrderParcelVO> parcelList;
    /**
     * 收货信息
     */
    private PurchaseOrderWareHouseVO consigneeInfo;
    /**
     * 运单信息
     */
    private PurchaseOrderWaybillVO waybillInfo;
    /**
     * 供应商基本信息
     */
    private SupplierBaseInfoVO supplierBaseInfo;

    /**
     * 前置采购单状态
     */
    private Integer beforePurchaseOrderStatus;

}
