package com.jdi.isc.order.center.domain.waybill.biz;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class PurchaseOrderWaybillOperateResp implements Serializable {

    private static final long serialVersionUID = -5294808183917474895L;

    /**
     * 操作
     */
    private String operate;

    /**
     * 操作文案描述
     */
    private Map<String, String> operateDescMap;

    /**
     * 前置状态
     */
    private List<Integer> beforeStatus;

    /**
     * 当前状态
     */
    private Integer status;
}
