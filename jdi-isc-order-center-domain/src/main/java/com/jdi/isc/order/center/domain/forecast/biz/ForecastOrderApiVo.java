package com.jdi.isc.order.center.domain.forecast.biz;

import com.jdi.isc.order.center.api.common.BasicApiReq;
import com.jdi.isc.order.center.domain.common.biz.DataIsolationQueryVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

@EqualsAndHashCode(callSuper = false)
@Data
public class ForecastOrderApiVo extends BasicApiReq implements Serializable {

    private static final long serialVersionUID = -8241668961479266410L;

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码错误")
    private Long index;

    @NotNull(message = "页大小不能为空")
    @Range(min=1, max = 200, message = "分页大小错误")
    private Long size;

    /**
     * 预报备货单的唯一标识号
     */
    private String forecastOrderId; // 预报备货单号

    /**
     * 供应商简码，标识订单关联的供应商信息
     */
    private String supplierCode; // 供应商简码



    /**
     * 原始父预报备货单号
     */
    private String parentForecastOrderId; // 原始父预报备货单号

    /**
     * 父预报备货单号
     */
    private String splitForecastOrderId; // 父预报备货单号


    /**
     * 预报备货单后台状态
     */
    private Integer forecastOrderStatus; // 预报备货单后台状态



    /**
     * 备货仓ID
     */
    private String enterpriseWarehouseId; // 备货仓ID



    /**
     * 入库单号
     */
    private String enterWarehouseNo; // 入库单号



    /**
     * 入仓时间
     */
    private Long enterWarehouseTime; // 入仓时间




    /**
     * 发货时间
     */
    Long startShippedTime;


    /**
     * 发货时间
     */
    Long endShippedTime;


    /**
     * 入仓时间
     */
    Long startEnterWarehouseTime;


    /**
     * 入仓时间
     */
    Long endEnterWarehouseTime;



    /**
     * 表示预报备货单创建时间的起始时间范围
     */
    Long startTime;



    /**
     * 表示预报备货单创建时间的结束时间范围
     */
    Long endTime;


    /**
     * 预报备货单下单时间，子单下单时间为父单下单时间，早于createTime
     */
    private Long startTimeForecastCreateTime;




    /**
     * 预报备货单下单时间，子单下单时间为父单下单时间，早于createTime
     */
    private Long endTimeForecastCreateTime;




    /**
     * 用于指定预报备货单查询结果的排序方式
     */
    String sortType;


    /**
     * 用于指定预报备货单查询结果的排序字段
     * 1正-1倒
     */
    Integer sort;


    /** 运单号 */
    private String waybillNum;


    /**
     * 预报备货单的唯一标识号
     */
    private Set<String> forecastOrderIdSet; // 预报备货单号

    /**
     * 操作人erp
     */
    private String operator;

    /**
     * 数据隔离查询条件
     */
    private DataIsolationQueryVO dataIsolationQueryVO;

}
