package com.jdi.isc.order.center.domain.purchaseOrder.po;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PurchaseOrderWareTaxInfoPO {



    /**
     * 商品价格的总上涨指数
     */
    private BigDecimal grossUpPriceIndex;

    /**
     * utilization
     */
    private String utilization;

    /**
     * 单位价格
     */
    private BigDecimal perUnitPrice;

    /**
     * icms税率
     */
    private BigDecimal icmsRate;

    /**
     * IPI税率
     */
    private BigDecimal ipiRate;

    /**
     * pis税率
     */
    private BigDecimal pisRate;

    /**
     * cofins税率
     */
    private BigDecimal cofinsRate;

    /**
     * icms税值
     */
    private BigDecimal icmsTaxValue;

    /**
     * IPI税值
     */
    private BigDecimal ipiTaxValue;

    /**
     * pis税值
     */
    private BigDecimal pisTaxValue;

    /**
     *  cofins税值
     */
    private BigDecimal cofinsTaxValue;






}

