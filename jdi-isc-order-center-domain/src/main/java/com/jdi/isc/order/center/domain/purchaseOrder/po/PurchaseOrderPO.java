package com.jdi.isc.order.center.domain.purchaseOrder.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import com.jdi.isc.order.center.domain.enums.purchase.PurchaseModelEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.poi.ss.formula.functions.T;

import java.math.BigDecimal;

/**
 * <AUTHOR> 采购单表
 */
@TableName(value = "jdi_isc_purchase_order_sharding")
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderPO extends BasicPO {

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 订单pin
     */
    private String pin;

    /**
     * 订单合同号
     */
    private String contractNum;

    /**
     * 原始父采购单号
     */
    private String parentPurchaseOrderId;

    /**
     * 当前订单的上一级父采购单号
     */
    private String splitPurchaseOrderId;

    /**
     * 订单
     */
    private Long orderId;

    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * sku总数量(所有skuNum的加合)
     */
    private Integer skuNum;

    /**
     * 采购单商品种类
     */
    private Integer skuKindNum;

    /**
     * 采购单后台状态(0:待确认，1:已确认，2:已取消，3：已发货，4：已入库)
     */
    private Integer purchaseOrderStatus;

    /**
     * 采购单类型【1.本土；2跨境】
     */
    private Integer purchaseOrderType;

    /**
     * 采购模式
     * @See com.jdi.isc.order.center.domain.enums.purchase.PurchaseModelEnum
     */
    private Integer purchaseModel;

    /**
     * 预拆单状态
     * @See com.jdi.isc.order.center.domain.enums.purchase.PreSplitStatusEnum
     */
    private Integer preSplitStatus;

    /**
     * 报关
     */
    private Integer customsClearance;

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 总采购价,4位小数(warePurchaseTotalPrice +  purchaseOrderTaxes +  serviceFee + orderFreightPrice)
     */
    private BigDecimal purchaseTotalPrice;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 订单总运费
     */
    private BigDecimal orderFreightPrice;

    /**
     * 商品采购总价,4位小数(ware上的采购价)
     */
    private BigDecimal waresPurchaseTotalPrice;

    /**
     * 采购单税金(ware上的tax总和)
     */
    private BigDecimal purchaseOrderTaxes;


    /**
     * 海外企配仓编号/备货仓编号
     */
    private String enterpriseWarehouseCode;

    /**
     * 订单版本号
     */
    private Integer version;

    /**
     * 扩展标，下游透传
     */
    private String purchaseOrderExtInfo;

    /**
     * 修改采购单的客户端信息
     */
    private String updateClientInfo;

    /**
     * 采购单的有效状态
     */
    private Integer validState;

    /**
     * 采购单下单时间，子单下单时间为父单下单时间，早于createTime
     */
    private Long purchaseCreateTime;

    /**
     * 接单时间
     */
    private Long receiveTime;

    /**
     * 发货时间
     */
    private Long shippedTime;

    /**
     * 入仓时间
     */
    private Long enterWarehouseTime;

    /**
     * 完成时间
     */
    private Long completeTime;

    /**
     * 取消时间
     */
    private Long cancelTime;

    /**
     * 商品含税价
     */
    @TableField(exist = false)
    private BigDecimal warePurchaseTotalIncludeTaxPrice;

    /**
     * 第三方订单号
     */
    private String thirdOrderId;

    /**
     * 入集货仓时间（国内）
     */
    private Long enterStorehouseTime;
    /**
     * 入备货仓单号(第三方编号)
     */
    private String enterWarehouseNo;

    /**
     * 实际入库商品数量
     */
    private Integer inboundWarehouseWareNum;

    /**
     * 确认后的时间
     */
    private Long confirmTime;

    /**
     * 出集货仓时间
     */
    private Long outStorehouseTime;

    /**
     * 发票上传状态
     */
    private Integer invoiceUploadStatus;

    /**
     * 发票审批状态
     */
    private Integer invoiceApprovalStatus;

    /**
     * 集运仓编码
     */
    private String storeHouseId;

    /**
     * 商品采购总价人民币
     */
    private BigDecimal waresPurchaseTotalPriceRmb;

    /**
     * 服务费人民币
     */
    private BigDecimal serviceFeeRmb;

    /**
     * 采购单总运费人民币
     */
    private BigDecimal orderFreightPriceRmb;

    /**
     * 采购单总税金人民币
     */
    private BigDecimal purchaseOrderTaxesRmb;

    /**
     * 采购单总金额人民币
     */
    private BigDecimal purchaseTotalPriceRmb;

    /**
     * 原始币种价格信息
     */
    private String sourcePriceInfo;


    public BigDecimal getWarePurchaseTotalIncludeTaxPrice(){
        if(this.waresPurchaseTotalPrice == null){
            return null;
        }
        this.warePurchaseTotalIncludeTaxPrice = this.waresPurchaseTotalPrice;
        if(this.getPurchaseOrderTaxes() != null){
            this.warePurchaseTotalIncludeTaxPrice = this.waresPurchaseTotalPrice.add(this.getPurchaseOrderTaxes());
        }
        return warePurchaseTotalIncludeTaxPrice;
    }

    public BigDecimal getPurchaseTotalPrice() {
        if(this.waresPurchaseTotalPrice == null){
            return null;
        }
        this.purchaseTotalPrice = this.waresPurchaseTotalPrice;
        if(this.getServiceFee() != null){
            this.purchaseTotalPrice = this.purchaseTotalPrice.add(this.getServiceFee());
        }
        if(this.getPurchaseOrderTaxes() != null){
            this.purchaseTotalPrice = this.purchaseTotalPrice.add(this.getPurchaseOrderTaxes());
        }
        if(this.getOrderFreightPrice() != null){
            this.purchaseTotalPrice = this.purchaseTotalPrice.add(this.getOrderFreightPrice());
        }
        return purchaseTotalPrice;
    }

    public BigDecimal getPurchaseTotalPriceRmb() {
        if(this.waresPurchaseTotalPriceRmb == null){
            return null;
        }
        this.purchaseTotalPriceRmb = this.waresPurchaseTotalPriceRmb;
        if(this.getServiceFeeRmb() != null){
            this.purchaseTotalPriceRmb = this.purchaseTotalPriceRmb.add(this.getServiceFeeRmb());
        }
        if(this.getPurchaseOrderTaxesRmb() != null){
            this.purchaseTotalPriceRmb = this.purchaseTotalPriceRmb.add(this.getPurchaseOrderTaxesRmb());
        }
        if(this.getOrderFreightPriceRmb() != null){
            this.purchaseTotalPriceRmb = this.purchaseTotalPriceRmb.add(this.getOrderFreightPriceRmb());
        }
        return purchaseTotalPriceRmb;
    }
}



