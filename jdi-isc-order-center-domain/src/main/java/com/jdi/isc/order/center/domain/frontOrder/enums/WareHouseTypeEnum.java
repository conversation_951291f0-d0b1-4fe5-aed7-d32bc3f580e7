package com.jdi.isc.order.center.domain.frontOrder.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @description 集运中心仓类型
 * <AUTHOR>
 * @date 2024/09/11
 **/
public enum WareHouseTypeEnum {
    INNER(0,"国内仓"),
    OVERSEA(1,"海外仓"),
    ;

    private Integer code;
    private String desc;

    public static Map<Integer, WareHouseTypeEnum> enumMap = new HashMap();
    public static Map<Integer, String> codeDescMap = new HashMap();

    static {
        for (WareHouseTypeEnum val : values()) {
            enumMap.put(val.getCode(), val);
            codeDescMap.put(val.getCode(), val.getDesc());
        }
    }

    WareHouseTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }

    public static WareHouseTypeEnum forCode(Integer code) {
        return enumMap.get(code);
    }
}