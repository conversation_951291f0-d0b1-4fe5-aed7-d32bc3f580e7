package com.jdi.isc.order.center.domain.mapstruct.order;

import com.jdi.isc.aggregate.read.wiop.api.orderCenter.resp.OrderMkuDetailReadResp;
import com.jdi.isc.order.center.api.biz.req.SubmitOrderApiReq;
import com.jdi.isc.order.center.api.biz.resp.*;
import com.jdi.isc.order.center.api.biz.resp.dto.OrderApiDTO;
import com.jdi.isc.order.center.api.biz.resp.dto.OrderWareApiDTO;
import com.jdi.isc.order.center.api.common.SystemInfoOrderApiReq;
import com.jdi.isc.order.center.api.settlement.biz.SettlementOrderApiReq;
import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.order.biz.OrderVO;
import com.jdi.isc.order.center.domain.order.biz.SubmitOrderStockItemVO;
import com.jdi.isc.order.center.domain.order.po.OrderExtInfoPO;
import com.jdi.isc.order.center.domain.order.po.OrderMsgPO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.domain.order.po.OrderWareMkuJsonInfoPO;
import com.jdi.isc.product.soa.api.stock.res.StockItemOccupyResDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 订单对象转换
 * <AUTHOR>
 * @date 20231225
 */
@Mapper
public interface OrderConvert {

    OrderConvert INSTANCE = Mappers.getMapper(OrderConvert.class);

    OrderPO dto2po(OrderVO orderVO);

    @Mappings({
            @Mapping(target = "orderStatusTime", source = "updateTime"),
            @Mapping(target = "thirdExtInfo", ignore = true)
    })
    OrderInfoOrderApiResp po2orderInfoDTO(OrderPO orderPO);

    ConsigneeInfoOrderApiResp po2orderConsigneeInfoDTO(OrderPO orderPO);

    PriceOrderApiResp po2orderPriceDTO(OrderPO orderPO);

    PromiseInfoOrderApiResp po2orderPromiseInfoOpenDTO(OrderPO orderPO);

    @Mappings({
            @Mapping(target = "creator", source = "reqDTO.pin"),
            @Mapping(target = "updater", source = "reqDTO.pin"),
            @Mapping(target = "orderExtInfo", ignore = true),
            @Mapping(target = "thirdExtInfo", ignore = true),
            @Mapping(target = "countryCode", source = "targetCountryCode"),

            @Mapping(target = "consigneeId", ignore = true),
    })
    OrderPO reqDTO2po(SubmitOrderApiReq reqDTO);

    SystemInfoOrderApiReq orderReq2systemReq(SubmitOrderApiReq reqDTO);


    OrderMsgPO po2msgPo(OrderPO orderPO);


    @Mappings({
            @Mapping(target = "mkuPriceReadResp", source = "orderMkuPriceReadResp")
    })
    OrderWareMkuJsonInfoPO mkuJsonInfoRead2Po(OrderMkuDetailReadResp orderMkuDetailReadResp);

    OrderPO copyPO(OrderPO orderPO);

    SystemInfoOrderApiReq settlementReq2SystemReq(SettlementOrderApiReq req);

    OrderExtInfoPO orderReq2OrderExtInfoPO(SubmitOrderApiReq reqDTO);


    List<OrderInfoOrderApiResp> listPo2Res(List<OrderPO> orderList);

    SkuInfoApiResp orderWarePo2Res(OrderWarePO orderWarePO);

    List<SubmitOrderStockItemVO> submitOrderStockItemDto2Vo(List<StockItemOccupyResDTO> stockItemOccupyResDTOList);

    List<OrderWareApiDTO> listOrderWarePo2OrderWareApiDto(List<OrderWarePO> orderWarePOList);

    List<OrderApiDTO> listOrderPo2OrderApiDto(List<OrderPO> orderPOList);
}
