package com.jdi.isc.order.center.domain.waybill.biz;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jdi.isc.order.center.api.common.SystemInfoOrderApiReq;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = false)
@Data
public class PurchaseOrderWaybillOperateReq extends SystemInfoOrderApiReq implements Serializable {

    private static final long serialVersionUID = 5999192712932858570L;

}
