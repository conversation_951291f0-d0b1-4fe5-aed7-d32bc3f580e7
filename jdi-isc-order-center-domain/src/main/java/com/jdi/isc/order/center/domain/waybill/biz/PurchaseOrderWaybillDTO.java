package com.jdi.isc.order.center.domain.waybill.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 一段运单 VO实体类
 * @Author: zhaojianguo21
 * @Date: 2024/06/13 15:17
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
//@AllArgsConstructor
public class PurchaseOrderWaybillDTO extends BasicVO {

    /** 运单号 */
    private String waybillNum;

    /** 运单状态 1:已发运 2:已入仓 */
    private Integer status;

    /** 供应商简码 */
    private String supplierCode;

    /** 关联采购单号 */
    private List<String> purchaseOrderIds;

    /**
     * 业务类型
     * {@link com.jdi.isc.fulfillment.soa.api.enums.common.BizTypeEnum}
     */
    private Integer bizType;
}
