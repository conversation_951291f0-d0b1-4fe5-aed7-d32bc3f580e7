package com.jdi.isc.order.center.domain.snapshot.price;

import com.jdi.isc.order.center.domain.snapshot.common.SnapshotBasicVO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 商品价格快照对象
 * @Author: zhaojianguo21
 * @Date: 2025/5/21 14:51
 **/
@Data
public class SnapshotPriceVO extends SnapshotBasicVO {

    /**
     * 币种
     */
    private String currency;
    /**
     * 币种符号
     */
    private String symbol;

    /**
     * 未税销售单价
     */
    private BigDecimal salePrice;

    /**
     * 单个商品的增值税金额
     */
    private BigDecimal valueAddedTax;

    /**
     * 税率
     */
    private BigDecimal valueAddedTaxRate;

    /**
     * 含税销售单价
     */
    private BigDecimal includeTaxPrice;

}
