package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import lombok.Data;

import java.util.List;

/**
 * 创建采购单上下文
 */
@Data
public class ChangePurchaseOrderReqVO {

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 用户ip
     */
    private String userIp;

    /**
     * 是否更新
     */
    private Boolean update;

    /**
     * 操作人
     */
    private String operator;
}
