package com.jdi.isc.order.center.domain.mapstruct.purchaseOrder;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.common.SystemInfoOrderApiReq;
import com.jdi.isc.order.center.api.purchaseOrder.biz.req.*;
import com.jdi.isc.order.center.api.settlement.biz.SettlementPurchaseOrderApplyApiReq;
import com.jdi.isc.order.center.api.settlement.biz.SettlementPurchaseOrderApproveApiReq;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.*;
import com.jdi.isc.order.center.domain.purchaseOrder.po.*;
import com.jdi.isc.order.center.domain.waybill.po.PurchaseOrderWaybillEnterPO;
import com.jdi.isc.order.center.domain.waybill.po.PurchaseOrderWaybillPO;
import com.jdi.isc.order.center.domain.waybill.po.PurchaseOrderWaybillRelationPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PurchaseOrderConvert {

    PurchaseOrderConvert INSTANCE = Mappers.getMapper(PurchaseOrderConvert.class);

    SystemInfoOrderApiReq updateReq2SystemReq(UpdatePurchaseOrderStatusReqVO reqVO);

    SystemInfoOrderApiReq updateReq2SystemReq(UpdatePurchaseOrdersStatusReqVO reqVO);

    PurchaseOrderSettlementPO purchaseOrderPo2SettlementPO(PurchaseOrderPO purchaseOrderPO);

    PurchaseOrderVO purchaseOrderPo2Vo(PurchaseOrderPO purchaseOrderPO);

    SystemInfoOrderApiReq settlementApply2SystemReq(SettlementPurchaseOrderApplyApiReq req);

    SystemInfoOrderApiReq settlementApprove2SystemReq(SettlementPurchaseOrderApproveApiReq req);

    PurchaseOrderSettlementLogPO settlementPo2LogPo(PurchaseOrderSettlementPO purchaseOrderSettlementPO);

    PurchaseOrderPO po2po(PurchaseOrderPO purchaseOrderPO);

    List<PurchaseOrderPO> purchaseOrderVo2Po(List<PurchaseOrderVO> purchaseOrderVO);

    @Mappings({
            @Mapping(target = "createTime", expression = "java(orderPO.getCreateTime().getTime())"),
            @Mapping(target = "updateTime", expression = "java(orderPO.getUpdateTime().getTime())")
    })
    PurchaseOrderPO orderPo2Po(OrderPO orderPO);

    LocalSplitPurchaseOrderReqVO splitPurchaseOrderLocalReq2Vo(SplitPurchaseOrderLocalReq req);


    PurchaseOrderPageVO.Request req2PageReq(PurchaseOrderApiReq req);

    PageInfo<PurchaseOrderApiDTO> page2ApiDTO(PageInfo<PurchaseOrderPageVO.Response> responsePageInfo);

    List<PurchaseOrderPageVO.Response> po2PageRes(List<PurchaseOrderPO> records);

    PurchaseOrderApiDTO vo2ApiDTO(PurchaseOrderVO purchaseOrderVO);

    PurchaseOrderTransportPO transportVo2Po(PurchaseOrderTransportVO transportVO);

    CrossBorderStockUpReqVO stockUpApiReq2ReqVO(CrossBorderStockUpApiReq apiReq);

    CrossBorderStockUpAuditReqVO stockUpAuditApiReq2ReqVO(CrossBorderStockUpAuditApiReq apiReq);

    PurchaseOrderEnterWarehousePO enterWarehouseVo2Po(PurchaseOrderEnterWarehouseVO purchaseOrderEnterWarehouse);

    PurchaseOrderWaybillPO waybillVo2Po(PurchaseOrderWaybillVO waybillDTO);

    PurchaseOrderWaybillRelationPO waybillPo2RelationPo(PurchaseOrderWaybillPO waybillPO);

    PurchaseOrderWaybillEnterPO waybillEnterVo2Po(PurchaseOrderWaybillEnterVO purchaseOrderWaybillEnterVO);

    PurchaseOrderApiDTO po2ApiDTO(PurchaseOrderPO purchaseOrderPO);

    PurchaseOrderWaybillVO waybillPo2Vo(PurchaseOrderWaybillPO input);
}
