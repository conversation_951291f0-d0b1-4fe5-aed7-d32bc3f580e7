package com.jdi.isc.order.center.domain.declaration.biz;

import lombok.Data;

import java.math.BigDecimal;


/**
 * 订单商品聚合信息查询出参
 * <a href="https://joyspace.jd.com/pages/H90mEyBebmXMPJtw0drN">实体解释</a>
 */
@Data
public class OrderSkusVo {

    /** sku数据库id 存疑@刘雪雁*/
    private Long id;
    /** 关联的订单号*/
    private Long orderId;
    /** 京东的SKU*/
    private Long skuId;
    /** 商品的标题或名称*/
    private String skuName;
    /**  商品的主图URL，即商品的主要展示图片*/
    private String mainImage;
    /** 商品所属的三级类目ID*/
    private Long categoryId;
    /** 商品所属的二级类目ID*/
    private Long categoryId2;
    /** 商品所属的一级类目ID*/
    private Long categoryId1;
    /** 外部系统中的SKU ID*/
    private String openSkuId;
    /** 外部SKU的单价*/
    private BigDecimal openSkuPrice;
    /** 商品数量*/
    private Integer num;
    /** 商品的跨境单价*/
    private BigDecimal skuPrice;
    /** 商品在主站的红字价格，通常指特殊优惠价格*/
    private BigDecimal redPrice;
    /** 商品的加价比例*/
    private Integer rate;

    /** 商品标识编码*/
    private String productIdentificationCode;



}
