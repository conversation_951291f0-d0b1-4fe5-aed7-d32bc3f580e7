package com.jdi.isc.order.center.domain.purchaseOrder.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR> 采购单日志表
 */
@TableName(value = "jdi_isc_purchase_order_log_sharding")
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderLogPO extends BasicPO {


    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * 订单状态
     */
    private Integer purchaseOrderStatus;

    /**
     * 采购单信息
     */
    private String purchaseOrderInfo;

    /**
     * 采购单商品信息
     */
    private String purchaseOrderWareInfo;

    /**
     * 订单版本号
     */
    private Integer version;

    /**
     * 修改采购单结算的客户端信息
     */
    private String updateClientInfo;

}