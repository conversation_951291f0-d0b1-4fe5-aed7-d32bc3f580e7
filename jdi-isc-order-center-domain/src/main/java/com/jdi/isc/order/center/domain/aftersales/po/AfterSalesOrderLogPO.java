package com.jdi.isc.order.center.domain.aftersales.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 售后申请单log表
 *
 * <AUTHOR>
 * @description
 * @date 2025/6/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "jdi_isc_after_sales_order_log_sharding")
@NoArgsConstructor
public class AfterSalesOrderLogPO extends BasicPO {

    /**
     * 售后单号
     */
    private String afterSalesOrderId;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 操作pin
     */
    private String pin;

    /**
     * 合同号
     */
    private String contractNum;

    /**
     * 客户号
     */
    private String clientCode;

    /**
     * 售后单状态:10待处理33处理中50已完成60取消
     */
    private Integer status;

    /**
     * 售后单快照信息
     */
    private String afterSalesOrderInfo;

    /**
     * 售后单商品快照信息
     */
    private String afterSalesOrderWareInfo;

    /**
     * 版本号
     */
    private Integer version;

}