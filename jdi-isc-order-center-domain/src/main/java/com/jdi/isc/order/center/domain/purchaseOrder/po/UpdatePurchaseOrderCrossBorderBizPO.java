package com.jdi.isc.order.center.domain.purchaseOrder.po;

import com.jdi.isc.order.center.domain.order.po.OrderBizInfoPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderWarePO;
import lombok.Data;

import java.util.List;

@Data
public class UpdatePurchaseOrderCrossBorderBizPO {

    private PurchaseOrderPO purchaseOrderPO;

    private List<PurchaseOrderWarePO> purchaseOrderWarePOList;

    private OrderBizInfoPO orderBizInfoPO;
}
