package com.jdi.isc.order.center.domain.snapshot.order;

import com.jdi.isc.order.center.domain.snapshot.common.SnapshotBasicVO;
import lombok.Data;

/**
 * @Description: 订单快照基本信息对象
 * @Author: zhaojianguo21
 * @Date: 2025/5/21 15:03
 **/
@Data
public class OrderSnapshotBaseInfoVO extends SnapshotBasicVO {

    /**
     * 国家/地区码
     */
    private String countryAreaCode;

    /**
     * 订单所属于国家地区的语言
     */
    private String countryAreaCodeLang;
}
