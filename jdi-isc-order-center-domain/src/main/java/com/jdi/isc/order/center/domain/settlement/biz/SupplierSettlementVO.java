package com.jdi.isc.order.center.domain.settlement.biz;

import lombok.Data;

@Data
public class SupplierSettlementVO {

    /**
     * 采购订单ID
     */
    private String purchaseOrderId;

    /**
     * 发票审批状态
     */
    private Integer invoiceApprovalStatus;

    /**
     * 发票上传状态
     */
    private Integer invoiceUploadStatus;

    /**
     * 发货时间
     */
    private Long shippedTime;

    /**
     * 入仓时间
     */
    private Long enterWarehouseTime;

    /**
     * 完成时间
     */
    private Long completeTime;

    /**
     * 采购单状态
     */
    private Long purchaseOrderStatus;



    /**
     * 供应商确认状态
     */
    private Integer supplierConfirmStatus;

    /**
     * 支付状态
     */
    private Integer payStatus;

    /**
     * 支付结束时间
     */
    private Long payTime;

    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * 原始父采购单号
     */
    private String parentPurchaseOrderId;

    /**
     * 多个结算单号拼接
     */
    private String requestIds;

    /**
     * 多个结算单号拼接
     */
    private String requestId;

    /**
     * 操作状态
     */
    private Integer opStatus;
}
