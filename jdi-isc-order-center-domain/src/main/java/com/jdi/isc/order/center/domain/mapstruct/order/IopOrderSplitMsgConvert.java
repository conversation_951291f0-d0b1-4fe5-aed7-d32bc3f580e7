package com.jdi.isc.order.center.domain.mapstruct.order;

import com.jdi.isc.order.center.domain.order.biz.IopOrderSplitContext;
import com.jdi.isc.order.center.domain.order.biz.IopOrderSplitMsgPageVO;
import com.jdi.isc.order.center.domain.order.biz.IopOrderSplitMsgVO;
import com.jdi.isc.order.center.domain.order.po.IopOrderSplitMsgPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description: 订单拆单信息表对象转换
 * @Author: taxuezheng1
 * @Date: 2024/05/06 20:50
 **/
@Mapper
public interface IopOrderSplitMsgConvert {

    IopOrderSplitMsgConvert INSTANCE = Mappers.getMapper(IopOrderSplitMsgConvert.class);

    IopOrderSplitMsgPO vo2Po(IopOrderSplitMsgVO vo);

    IopOrderSplitMsgVO po2Vo(IopOrderSplitMsgPO po);

    List<IopOrderSplitMsgVO> listPo2Vo(List<IopOrderSplitMsgPO> poList);

    List<IopOrderSplitMsgPO> listVo2Po(List<IopOrderSplitMsgVO> voList);

    List<IopOrderSplitMsgPageVO.Response> pageListPo2Vo(List<IopOrderSplitMsgPO> pos);

    @Mapping(target="orderId", source="jdOrderId")
    IopOrderSplitMsgVO msg2Vo(IopOrderSplitContext orderInfo);
}
