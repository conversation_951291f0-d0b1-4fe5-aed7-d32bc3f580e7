package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.common.po.BasicPO;
import com.jdi.isc.order.center.domain.order.biz.OrderBizInfoVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderVO extends BasicPO {

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 订单pin
     */
    private String pin;

    /**
     * 订单合同号
     */
    private String contractNum;

    /**
     * 原始父采购单号
     */
    private String parentPurchaseOrderId;

    /**
     * 当前订单的上一级父采购单号
     */
    private String splitPurchaseOrderId;

    /**
     * 订单
     */
    private Long orderId;

    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * sku总数量(所有skuNum的加合)
     */
    private Integer skuNum;

    /**
     * 采购单商品种类
     */
    private Integer skuKindNum;

    /**
     * 采购单后台状态(0:待确认，1:已确认，2:已取消，3：已发货，4：已入库)
     */
    private Integer purchaseOrderStatus;

    /**
     * 采购单类型【1.本土；2跨境】
     */
    private Integer purchaseOrderType;

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 总采购价,4位小数(warePurchaseTotalPrice +  purchaseOrderTaxes +  serviceFee + orderFreightPrice)
     */
    private BigDecimal purchaseTotalPrice;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 订单总运费
     */
    private BigDecimal orderFreightPrice;

    /**
     * 商品采购总价,4位小数(ware上的采购价)
     */
    private BigDecimal waresPurchaseTotalPrice;

    /**
     * 采购单税金(ware上的tax总和)
     */
    private BigDecimal purchaseOrderTaxes;


    /**
     * 企配仓编码
     */
    private String enterpriseWarehouseCode;

    /**
     * 订单版本号
     */
    private Integer version;

    /**
     * 扩展标，下游透传
     */
    private String purchaseOrderExtInfo;

    /**
     * 修改采购单的客户端信息
     */
    private String updateClientInfo;

    /**
     * 采购单的有效状态
     */
    private Integer validState;
    /**
     * 采购单商品
     */
    private List<PurchaseOrderWareVO> purchaseOrderWareVOList;

    /**
     * orderBizInfo (基于ept的采购单)
     */
    private OrderBizInfoVO orderBizInfoVO;

    /**
     * 子采购单
     */
    private List<PurchaseOrderVO> childPurchaseOrderList;

    /**
     * 采购单下单时间，子单下单时间为父单下单时间，早于createTime
     */
    private Long purchaseCreateTime;

    /**
     * 采购单子单的orderId对应采购单split的orderId
     */
    private Map<Long, Long> splitOrderIdMap;

    /**
     * 采购单收货信息
     */
    private PurchaseOrderConsigneeVO purchaseOrderConsigneeVO;


    /**
     * 接单时间
     */
    private Long receiveTime;

    /**
     * 发货时间
     */
    private Long shippedTime;

    /**
     * 入仓时间
     */
    private Long enterWarehouseTime;

    /**
     * 完成时间
     */
    private Long completeTime;

    /**
     * 取消时间
     */
    private Long cancelTime;

    /**
     * 入集货仓时间（国内）
     */
    private Long enterStorehouseTime;
    /**
     * 入备货仓单号
     */
    private String enterWarehouseNo;
    /**
     * 出集货仓时间
     */
    private Long outStorehouseTime;
    /**
     * 预拆单状态
     * @See com.jdi.isc.order.center.domain.enums.purchase.PreSplitStatusEnum
     */
    private Integer preSplitStatus;


    /**
     * 采购模式
     * @See com.jdi.isc.order.center.domain.enums.purchase.PurchaseModelEnum
     */
    private Integer purchaseModel;

    /**
     * 确认后的时间
     */
    private Long confirmTime;

    /**
     * 备货模式描述
     */
    private String purchaseModelDesc;

}
