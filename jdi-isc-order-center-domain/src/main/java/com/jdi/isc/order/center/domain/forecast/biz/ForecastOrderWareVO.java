package com.jdi.isc.order.center.domain.forecast.biz;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;


/**
 * 预报备货单商品表
 * @TableName jdi_isc_forecast_order_sharding
 */


@Data
public class ForecastOrderWareVO extends BasicVO {



    /**
     * 预报备货单号
     */
    private String forecastOrderId;

    /**
     * 履约SKU编码
     */
    private Long skuId;

    /**
     * SKU数量
     */
    private Integer skuNum;

    /**
     * SKU类型
     * 0：商品
     * 1：附件
     * 2：赠品
     */
    private Integer skuType ;

    /**
     * 父类SKU（主要赠品附件用）
     */
    private Long parentSkuId ;

    /**
     * 国家码
     */
    private String skuCountryCode;

    /**
     * SKU信息快照（不含商详）
     */
    private String skuJsonInfo;

    /**
     * 商品入库数量（商品表的加合）
     */
    private Integer inboundWarehouseWareNum;



    /**
     * 版本号
     */
    private Integer version; // 版本号


    /**
     * 用于拆分的key
     */
    @TableField(exist = false)
    private String splitKey;



}
