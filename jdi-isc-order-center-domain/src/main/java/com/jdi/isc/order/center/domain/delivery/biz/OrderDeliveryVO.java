package com.jdi.isc.order.center.domain.delivery.biz;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.jdi.isc.order.center.domain.common.biz.BaseVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 订单配送信息表
 * @TableName jdi_isc_order_delivery_sharding
 */
@TableName(value ="jdi_isc_order_delivery_sharding")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class OrderDeliveryVO extends BaseVO {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 语言编码
     */
    private String lang;

    /**
     * 配送阶段，0:境内，1:境外
     */
    private Integer deliveryStage;

    /**
     * 配送信息
     */
    private String deliveryText;

}