package com.jdi.isc.order.center.domain.declaration.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@TableName("jdi_isc_customs_out_bound_file")
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CustomsOutBoundFilePo extends BasePO {

    private Long   customsId;//关联报关单Id
    private String   fileName;//文件名称
    private String   fileType;//文件类型
    private String   fileUrl;//文件链接
    private String   uploadErp;//上传人erp
    private Date     uploadDate;//上传时间 yyyy-MM-dd HH:mm:ss
    private String   fileNameType;//文件名称类型【区分不同的业务文件】


}
