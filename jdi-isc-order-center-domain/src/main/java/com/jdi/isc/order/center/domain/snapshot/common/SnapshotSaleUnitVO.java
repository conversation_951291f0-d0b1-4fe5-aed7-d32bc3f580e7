package com.jdi.isc.order.center.domain.snapshot.common;

import com.jdi.isc.order.center.domain.snapshot.common.SnapshotBasicVO;
import lombok.Data;

import java.util.Map;

/**
 * @Description: 销售单位快照对象
 * @Author: zhaojianguo21
 * @Date: 2025/5/21 14:51
 **/
@Data
public class SnapshotSaleUnitVO extends SnapshotBasicVO {

    /** 销售单位编码 */
    private Integer code;

    /** 销售单位多语言名称 */
    private Map<String/*语种*/,String/*名称*/> nameLangMap;

}
