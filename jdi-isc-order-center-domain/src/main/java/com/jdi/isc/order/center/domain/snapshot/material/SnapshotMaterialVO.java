package com.jdi.isc.order.center.domain.snapshot.material;

import com.jdi.isc.order.center.domain.snapshot.common.SnapshotBasicVO;
import lombok.Data;

/**
 * @Description: 物料快照对象
 * @Author: zhaojianguo21
 * @Date: 2025/5/21 14:51
 **/
@Data
public class SnapshotMaterialVO extends SnapshotBasicVO {

    /** 物料编码 */
    private String materialCodeShow;

    /** 物料名称 */
    private String materialNameShow;

    /** wimp系统中维护的物料编码 */
    private String materialCodeInner;

    /** wimp系统中维护的物料名称 */
    private String materialNameInner;

    /** 提单时请求的物料编码 */
    private String materialCodeSubmitOrder;

    /** 提单时请求物料名称 */
    private String materialNameSubmitOrder;
}
