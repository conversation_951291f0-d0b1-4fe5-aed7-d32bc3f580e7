package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.common.biz.BasePageVO;
import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 采购单修改信息申请表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PurchaseOrderUpdateApplyPageVO extends BasePageVO {

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 修改类型，1:修改采购单金额
     */
    private Integer updateType;

    /**
     * 申请业务ID
     */
    private String applyId;

    /**
     * 申请业务编码
     */
    private String applyCode;

    /**
     * 操作状态 0:待处理 1:成功 -1:取消申请
     */
    private Integer applyStatus;

}