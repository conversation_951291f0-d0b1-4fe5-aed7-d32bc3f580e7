package com.jdi.isc.order.center.domain.applyOrder.biz;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 申请单表
 */
@Data
public class ApplyOrderVO {

    /** '申请单来源 1:邮件 2:RPA' */
    @NotNull(message = "申请单来源不能为空.")
    private Integer source;
    /** '申请单创建人(邮件来源对应发件人)' */
    @NotNull(message = "申请单创建人不能为空.")
    private String applicant;
    /** '下单pin' */
    private String pin;
    /** '申请单附件url' */
    @NotNull(message = "申请单附件url不能为空.")
    private String attachmentUrl;
    /** '三方订单号' */
    @NotNull(message = "三方订单号不能为空.")
    private String thirdOrderId;
    /** '三方扩展信息，下游透传' */
    private String thirdExtInfo;
    /** 'sap单号' */
    private String sapOrderId;
    /** '国际订单号' */
    private Long orderId;
    /** '公司抬头' */
    private String header;
    /** '收货人姓名' */
    @NotNull(message = "收货人姓名不能为空.")
    private String consignee;
    /** '收货国家' */
    @NotNull(message = "收货国家不能为空.")
    private String consigneeCountry;
    /** '收货地址省ID' */
    @NotNull(message = "收货地址省ID不能为空.")
    private Long consigneeProvinceId;
    /** '收货地址城市ID' */
    @NotNull(message = "收货地址城市ID不能为空.")
    private Long consigneeCityId;
    /** '收货地址区县ID' */
    @NotNull(message = "收货地址区县ID不能为空.")
    private Long consigneeCountyId;
    /** '收货地址乡镇ID 四级地址编码：收货人乡镇地址编码(如果没有四级地址则传0)' */
    private Long consigneeTownId;
    /** '收货详细地址' */
    @NotNull(message = "收货详细地址不能为空.")
    private String consigneeAddress;
    /** '收货人电话' */
    private String consigneePhone;
    /** '收货人手机号' */
    private String consigneeMobile;
    /** '收货人邮箱' */
    private String consigneeEmail;
    /** '币种:VND越南,THB泰国,CNY人民币,USD美元' */
    private String currency;
    /** '订单总价' */
    private String orderTotalPrice;
    /** '订单确认状态 1:已确认 0:待确认' */
    private Integer confirmStatus;
    /** '申请单审核状态 1:已确认 0:待确认' */
    private Integer auditStatus;
    /** '当前审核驳回等级 1:一级 2:二级' */
    private Integer level;
    /** '国际订单状态' */
    private Integer orderStatus;

    /** '申请单商品信息' */
    @Valid
    @NotNull(message = "申请单商品信息不能为空.")
    private List<ApplyOrderMkuVO> mkuInfo;

    /** '合同号' */
    private String contractNum;
    /** '客户id' */
    private String clientCode;

    /** 申请单类型,【1.本土；2跨境】 */
    private Integer orderType;
}