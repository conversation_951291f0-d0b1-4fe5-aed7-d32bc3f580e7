package com.jdi.isc.order.center.domain.frontOrder;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/8/23 3:06 下午
 */

@Data
public class JdiIOBoundFinishedMsg {

    /**
     * 操作类型
     * 0-入库完成、1-出库完成
     */
    private Integer operateType;

    /**
     * 国际单号
     */
//    private String nationalOrderNo;

    /**
     * 国内单号
     */
    private String iopOrderNo;

    /**
     * 操作人ID
     */
    private String operateId;

    /**
     * 操作人名称
     */
    private String operateName;

    /**
     * 订单状态（1 待发货 2 部分发货 3 已发货 4 已取消）
     */
//    private Integer orderStatus;

    /**
     * 操作时间
     */
    private Long operateTime;

}
