package com.jdi.isc.order.center.domain.declaration.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

//报关单相关销售、采购、出库信息
@Data
@TableName("jdi_isc_customs_relation")
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CustomsRelationPo  extends BasePO {


    private Long   customsId;//关联报关单Id
    private String   saleOrderId;//销售单Id
    private String   purchaseOrderId;//采购单Id
    private String   outboundDeliveryOrderId;//出库单Id
    private String   orderType;//销售单类型  自营 0| 厂直  18  https://cf.jd.com/pages/viewpage.action?pageId=523238981
    private String   eptSaleOrderId;//EPT销售单Id

}
