package com.jdi.isc.order.center.domain.declaration.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 报关信息表
 * <AUTHOR>
 * @date 20231109
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("jdi_isc_customs_info")
@AllArgsConstructor
@NoArgsConstructor
public class CustomsInfoPo extends BasePO {




    private String   customsId;//关联报关单Id
    private String   sourceSystem;// 来源系统【传输所在系统的域名】
    private String   customerCode;//客户编码
    private String   customerName;//客户名称
    private Long     customsAgentId;//报关服务商Id
    private String   customsAgentName;// 报关服务商名称
    private String   superviseWay;//监管方式（0110/9610）
    private String   dealWay;//成交方式（FOB/CIF）

    @TableField(value = "freight_fee")
    private String   freightFee;//运费
    @TableField(value = "ins_prm")
    private String   insPRM;//保费
    @TableField(value = "misc_exp")
    private String   miscEXP;//杂费

    private BigDecimal customsAmount;//报关单金额/销售金额
    private Date       applyDate;//制单时间 yyyy-MM-dd HH:mm:ss
    private String   applyUserErp;//制单人erp
    private String   applyUserName;//制单人姓名

    private Integer  status;//订单状态0草稿1已提交




}
