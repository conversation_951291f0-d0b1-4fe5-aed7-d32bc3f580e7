package com.jdi.isc.order.center.domain.applyOrder.biz;

import com.jdi.isc.order.center.domain.common.biz.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 申请单商品表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApplyOrderMkuVO extends BaseVO {

    /** '申请单号' */
    private Long applyId;
    /** 'mkuId' */
    @NotNull(message = "mkuId不能为空.")
    private Long mkuId;
    /** '数量' */
    @NotNull(message = "mku数量不能为空.")
    private Integer num;
    /** '币种:VND越南,THB泰国,CNY人民币,USD美元' */
    private String currency;
    /** '含税销售价' */
    private BigDecimal salePrice;
    /** '销售单位' */
    private String saleUnit;

}