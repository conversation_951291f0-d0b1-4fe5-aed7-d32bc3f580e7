package com.jdi.isc.order.center.domain.purchaseOrder.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Description 采购单运送信息
 * @Date 2024/5/27 9:53 下午
 */
@TableName(value = "jdi_isc_purchase_order_transport_sharding")
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderTransportPO extends BasicPO {

    /**
     * 采购单
     */
    private String purchaseOrderId;
    /**
     * 运输方式
     */
    private Integer transportMethod;
    /**
     * 运输单号
     */
    private String transportNum;
    /**
     * 承运商
     */
    private String carrier;
    /**
     * 发货时间
     */
    private Long deliveryDate;
    /**
     * 预计送达时间
     */
    private Long expectedDeliveryDate;
    /**
     * 发货资料
     */
    private String deliveryMaterial;
    /**
     * 发货视频资料
     */
    private String deliveryMaterialVideo;
    /**
     * 配送人
     */
    private String deliveryPerson;
    /**
     * 车牌号
     */
    private String licensePlate;
    /**
     * 配送人电话
     */
    private String deliveryMobileIndex;
    /**
     * 配送人电话
     */
    private String deliveryMobileEncrypt;

}
