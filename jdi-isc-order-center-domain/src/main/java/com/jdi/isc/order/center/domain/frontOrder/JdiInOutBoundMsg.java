package com.jdi.isc.order.center.domain.frontOrder;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/8/23 3:06 下午
 */

@Data
public class JdiInOutBoundMsg {

    /**
     * 仓编码
     */
    private String wareHouseNo;

    /**
     * 仓类型
     * 0-国内仓，1-国外仓
     * {@link com.jdi.isc.order.center.domain.frontOrder.enums.WareHouseTypeEnum}
     */
    private Integer wareHouseType;

    /**
     * 消息类型
     * 0-入库完成、1-出库完成
     * {@link com.jdi.isc.order.center.domain.frontOrder.enums.OperateTypeEnum}
     */
    private Integer operateType;

    /**
     * 出入库唯一标识
     * 入库：入库执行单号，CG开头
     * 出库：出库计划单号，CK开头，代表多个订单一个批次进行出库
     */
    private String uniqueWMSOrderNo;

    /**
     * 操作人ID
     */
    private String operateId;

    /**
     * 操作人名称
     */
    private String operateName;

    /**
     * 操作时间
     */
    private Long operateTime;

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 订单信息
     */
    private List<JdiInOutOrderInfoMsg> orderInfo;
}
