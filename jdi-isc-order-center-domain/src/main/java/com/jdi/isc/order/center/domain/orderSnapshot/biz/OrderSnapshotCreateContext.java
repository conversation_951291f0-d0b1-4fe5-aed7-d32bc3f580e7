package com.jdi.isc.order.center.domain.orderSnapshot.biz;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.api.category.res.CategoryReadResDTO;
import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.domain.order.po.OrderSkuDetailPO;
import com.jdi.isc.order.center.domain.order.po.OrderWareMkuJsonInfoPO;
import com.jdi.isc.order.center.domain.snapshot.order.OrderSnapshotBaseInfoVO;
import com.jdi.isc.order.center.domain.tax.OrderWareTaxInfoVO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuLangsResDTO;
import com.jdi.isc.product.soa.api.supplier.res.BrandResDTO;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2025/5/21 18:02
 **/
@Data
public class OrderSnapshotCreateContext {

    /**
     * 商品所属订单基本信息
     */
    private OrderSnapshotBaseInfoVO orderSnapshotBaseInfo;

    /**
     * 快照基本信息
     */
    private OrderWareSnapshotCommonInfoVO wareSnapshotCommonInfoVO;

    /**
     * 订单信息
     */
    private OrderPO orderPO;

    /**
     * 所有mkuId集合
     */
    private Set<Long> mkuIds;

    /**
     * 订单商品快照记录
     */
    private List<OrderWareSnapshotVO> orderWareSnapshotVOList;

    /**
     * 订单商品快照记录
     */
    private Map<Long/*mkuId*/,OrderWareSnapshotVO> orderWareSnapshotVoMap;

    /**
     * 订单商品表数据
     */
    private List<OrderWarePO> orderWarePOList;

    /**
     * 订单商品表数据map
     */
    private Map<Long/*mkuId*/,OrderWarePO> orderWarePoMap;

    /**
     * 订单商品表 mku_json_info 字段解析后的数据
     */
    private Map<Long/*mkuId*/, OrderWareMkuJsonInfoPO> orderWareMkuJsonInfoMap;

    /**
     * 订单商品表 mku_ext_info 字段解析后的数据
     */
    private Map<Long/*mkuId*/, JSONObject> orderWareMkuExtJsonInfoMap;

    /**
     * 订单商品表 skuJsonInfo 字段解析后的数据
     */
    private Map<Long/*mkuId*/, OrderSkuDetailPO> orderWareSkuJsonInfoMap;

    /**
     * 订单商品表 value_added_taxes_info 字段解析后的数据
     */
    private Map<Long/*mkuId*/, OrderWareTaxInfoVO> orderWareValueAddedTaxesInfoMap;

    /**
     * 所有商品的类目id集合
     */
    private Set<Long> categoryIds;

    /**
     * 所有商品的品牌id集合
     */
    private Set<Long> brandIds;

    /**
     * 销售单位多语言map
     */
    private LinkedHashMap<Integer, Map<String, String>> allSaleUnitMap;

    /**
     * 品牌多语言
     */
    private Map<Long, BrandResDTO> brandMap;

    /**
     * 多语言，中/英/当前国语言
     */
    private Set<String> langSet;

    /**
     * 类目信息
     */
    private Map<Long, CategoryReadResDTO> categoryReadMap;

    /**
     * mku 属性map
     */
    private Map<Long, IscMkuLangsResDTO> mkuBaseInfoMap;

    /**
     * 商品履约时效文案
     */
    Map<Long/*mkuId*/, Map<String, String>> mkuPromiseTimeLangTextMap;
}
