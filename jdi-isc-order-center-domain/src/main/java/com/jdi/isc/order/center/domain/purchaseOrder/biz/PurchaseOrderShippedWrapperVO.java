package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.waybill.po.PurchaseOrderWaybillPO;
import com.jdi.isc.order.center.domain.waybill.po.PurchaseOrderWaybillRelationPO;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2024/9/19 14:20
 **/
@Data
public class PurchaseOrderShippedWrapperVO {

    private PurchaseOrderWaybillPO orderWaybillPO;

    private List<PurchaseOrderWaybillRelationPO> relationPOS;

}
