package com.jdi.isc.order.center.domain.supplier.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/5/30
 **/
@Data
public class SupplierBaseInfoVO extends BasicVO {
    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * 供应商状态
     */
    private Integer supplierStatus;

    /**
     * M Code
     */
    private String merchantCode;

    /**
     * 营业执照名称
     */
    private String businessLicenseName;

    /**
     * 营业执照编号
     */
    private String businessLicenseNumber;

    /**
     * 营业执照文件
     */
    private String businessLicenseFile;

    /**
     * 营业执照注册时间
     */
    private Long businessLicenseRegistTime;

    /**
     * 营业执照所在国家
     */
    private String businessLicenseCountry;

    /**
     * 企业性质
     */
    private Integer nature;

    /**
     * 企业类型
     */
    private Integer enterpriseType;

    /**
     * 税号
     */
    private String taxId;

    /**
     * 组织机构代码
     */
    private String organizationCode;

    /**
     * 注册资金
     */
    private BigDecimal registerCapital;

    /**
     * 注册资金币种
     */
    private String registerCurrency;

    /**
     * 开票类型
     */
    private String invoiceTypeRemark;

    /**
     * 批次号
     */
    private String batchNum;
}
