package com.jdi.isc.order.center.domain.orderArchive.req;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 订单三方归档商品 实体类
 * @Author: zhaojianguo21
 * @Date: 2024/12/27 22:12
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class OrderThirdArchiveWareReqVO extends BasicVO {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * mku编码
     */
    @NotNull(message = "商品ID不能为空")
    private Long mkuId;

    /**
     * 履约sku编码
     */
    private Long skuId;

    /**
     * 商品名称
     */
    @NotNull(message = "商品名称不能为空")
    private String name;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    private Integer num;

    /**
     * 商品税率
     */
    @NotNull(message = "商品税率不能为空")
    private BigDecimal tax;

    /**
     * 商品未税单价,4位小数
     */
    @NotNull(message = "商品未税单价不能为空")
    private BigDecimal nakedPrice;

    /**
     * 商品含税单价,4位小数
     */
    @NotNull(message = "商品含税单价不能为空")
    private BigDecimal price;

    /**
     * 归档批次，自动生成
     */
    private String archiveBatchNum;
}