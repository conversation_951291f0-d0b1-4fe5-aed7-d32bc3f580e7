package com.jdi.isc.order.center.domain.waybill.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @Description: 二段运单妥投 VO实体类
 * @Author: zhaojianguo21
 * @Date: 2024/06/13 18:12
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class OrderWaybillDeliveryCustomerVO extends BasicVO {
    /** 运单号 */
    @NotNull(message = "运单号不能为空")
    private String waybillNum;

    /** 妥投时间 */
    @NotNull(message = "妥投时间不能为空")
    private Long deliveryCustomerDate;

    /** 妥投资料 */
    private String material;
}
