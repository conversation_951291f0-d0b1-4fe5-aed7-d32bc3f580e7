package com.jdi.isc.order.center.domain.waybill.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

/**
 * @Description: 运单状态更新 VO实体类
 * @Author: zhaojianguo21
 * @Date: 2024/09/9
 **/

public class OrderWaybillStatusUpdateVO {

    @EqualsAndHashCode(callSuper = true)
    @Data
    @NoArgsConstructor
    public static class Request extends BasicVO implements Serializable {
        /** 运单号 */
        @NotEmpty(message = "运单号不能为空！")
        private Set<String> waybillNums;

        /** 运单类型 */
        @NotNull(message = "运单类型不能为空！")
        private Integer waybillType;

    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BasicVO implements Serializable{

    }

}
