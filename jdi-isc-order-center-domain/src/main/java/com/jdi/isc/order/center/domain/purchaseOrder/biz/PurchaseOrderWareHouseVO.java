package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderConsigneePO;
import lombok.Data;

import java.util.List;

@Data
public class PurchaseOrderWareHouseVO {

    private String enterpriseWarehouseCode;

    private String enterpriseWarehouseName;

    /**
     * 详细地址
     */
    private String consigneeAddressEncrypt;

    /**
     * 详细地址
     */
    private String consigneeAddress;

    private List<PurchaseOrderConsigneeVO> consigneeInfoList;
}
