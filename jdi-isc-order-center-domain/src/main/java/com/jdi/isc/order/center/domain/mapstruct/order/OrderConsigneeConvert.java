package com.jdi.isc.order.center.domain.mapstruct.order;

import com.jdi.isc.order.center.api.biz.resp.ConsigneeInfoOrderApiResp;
import com.jdi.isc.order.center.api.biz.resp.OrderConsigneeApiDTO;
import com.jdi.isc.order.center.domain.order.biz.OrderConsigneeVO;
import com.jdi.isc.order.center.domain.order.po.OrderConsigneePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OrderConsigneeConvert {

    OrderConsigneeConvert INSTANCE = Mappers.getMapper(OrderConsigneeConvert.class);

    OrderConsigneeVO po2vo(OrderConsigneePO orderConsigneePO);

    ConsigneeInfoOrderApiResp po2apiResp(OrderConsigneePO orderConsigneePO);

    OrderConsigneePO copyPO(OrderConsigneePO parentOrderConsign);

    OrderConsigneeApiDTO po2dto(OrderConsigneePO c);
}
