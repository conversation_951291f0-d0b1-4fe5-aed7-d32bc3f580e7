package com.jdi.isc.order.center.domain.settlement.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SupplierSettlementsDetailExpenseVo extends BasicVO {

    // 采购单号
    private String purchaseOrderId;

    // sku编码
    private Long skuId;

    // 商品数量
    private Integer skuNum;


    // 结算单号（内部结算单）
    private String requestId;

    // 供应商简码
    private String supplierId;

    // 结算金额SupplierSettlementsDetailPo
    private BigDecimal amount;

    // 币种
    private String accountCurrency;



    /**
     * 费项编码与资金方向映射说明：
     * - 160537: 采购入库单，资金方向为京东付款
     * - 160540: 税差单收，资金方向为京东收款
     * - 160539: 税差单付，资金方向为京东付款
     * - ********: 质保金，资金方向为京东收款
     * - 160973: 采购价差调增，资金方向为京东付款
     * - 160974: 采购价差调减，资金方向为京东收款
     * 这些费项编码用于在财务系统中标识不同类型的交易和资金流动。
     *  com.jdi.isc.order.center.domain.enums.settlement.ExpenseItemEnum
     */
    private Integer expenseType;
    


    // 结算侧唯一编码
    private String bizCode;



    /**
     * 费项编码与资金方向映射说明：
     *  com.jdi.isc.order.center.domain.enums.settlement.ExpenseItemEnum
     */
    private String expenseName;


}
