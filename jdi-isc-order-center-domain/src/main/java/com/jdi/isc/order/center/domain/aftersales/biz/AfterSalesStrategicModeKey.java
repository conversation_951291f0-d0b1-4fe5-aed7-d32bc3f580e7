package com.jdi.isc.order.center.domain.aftersales.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AfterSalesStrategicModeKey {


    /**
     * 表示售后策略处理类型的成员变量
     * 必填审核结果
     */
    @FieldWeight(1)
    private Integer processingType;

    /**
     * 表示售后问题责任类型的成员变量，
     * 用于标识问题的定性
     * 必填审核结果
     */
    @FieldWeight(2)
    private Integer afterSalesReasonLiabilityType;//问题定性


//    /**
//     * 表示国家代码的成员变量，用于标识售后策略适用的国家或地区
//     */
//    @FieldWeight(3)
//    private String countryCode;


    /**
     * 表示合同编号的成员变量，用于标识与售后策略相关的合同
     */
    @FieldWeight(5)
    private String contractNum;//合同号


//    /**
//     * 表示订单编号的成员变量，用于唯一标识与售后策略相关联的订单
//     */
//    @FieldWeight(15)
//    private Long orderId;//订单号


    /**
     * 表示售后订单编号的成员变量，用于唯一标识关联的售后订单
     */
    @FieldWeight(20)
    private String afterSalesOrderId;






}
