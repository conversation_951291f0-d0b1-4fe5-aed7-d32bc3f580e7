package com.jdi.isc.order.center.domain.mapstruct.ofc;

import com.jdi.isc.order.center.api.ofc.biz.rsp.SkuInfoResDTO;
import com.jdi.isc.order.center.domain.ofc.fulfillmentOrder.biz.MultiWarehouseSkuVO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.SkuInfoReadDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuFeatureApiDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Date 2025/2/13
 * @Description
 */
@Mapper
public interface FulfillmentPolicyConvert {

    /**
     * 获取FulfillmentPolicyConvert接口的实例。
     */
    FulfillmentPolicyConvert INSTANCE = Mappers.getMapper(FulfillmentPolicyConvert.class);

    /**
     * 将SkuFeatureApiDTO对象转换为MultiWarehouseSkuVO对象
     * @param skuInfoReadDTO SkuFeatureApiDTO对象，包含SKU的详细信息
     * @return 转换后的MultiWarehouseSkuVO对象
     */
    MultiWarehouseSkuVO skuFeature2MultiWarehouseSkuVO(SkuFeatureApiDTO skuInfoReadDTO);

    /**
     * 将 MultiWarehouseSkuVO 对象转换为 SkuInfoResDTO 对象。
     * @param multiWarehouseSkuVO 多仓库 SKU 信息对象。
     * @return 转换后的 SkuInfoResDTO 对象。
     */
    SkuInfoResDTO sku2SkuInfoResDto(MultiWarehouseSkuVO multiWarehouseSkuVO);

    /**
     * 将 SkuInfoReadDTO 转换为 MultiWarehouseSkuVO 对象。
     * @param skuInfoReadDTO SkuInfoReadDTO 对象，包含 SKU 信息。
     * @return 转换后的 MultiWarehouseSkuVO 对象。
     */
    MultiWarehouseSkuVO skuInfo2MultiWarehouseSkuVO(SkuInfoReadDTO skuInfoReadDTO);

    /**
     * 将 MultiWarehouseSkuVO 转换为 SkuInfoReadDTO。
     * @param multiWarehouseSkuVO 多仓库 SKU 的详细信息
     * @return 转换后的 SkuInfoReadDTO 对象
     */
    SkuInfoReadDTO sku2SkuInfoReadDTO(MultiWarehouseSkuVO multiWarehouseSkuVO);

}
