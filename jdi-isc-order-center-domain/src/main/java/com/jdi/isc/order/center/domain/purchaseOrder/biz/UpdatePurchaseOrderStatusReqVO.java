package com.jdi.isc.order.center.domain.purchaseOrder.biz;


import com.jdi.isc.order.center.api.common.SystemInfoOrderApiReq;
import lombok.Data;

@Data
public class UpdatePurchaseOrderStatusReqVO {

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 采购单修改人
     */
    private String operateAccount;

    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * 操作  com.jdi.isc.order.center.api.constants.PurchaseOrderStatusConstant
     */
    private String operate;

    /**
     * 客户信息UpdatePurchaseOrderStatusReqVO
     */
    private SystemInfoOrderApiReq clientReqExt;
    /**
     * 采购单发货对象
     */
    private PurchaseOrderTransportVO purchaseOrderTransport;

    /**
     * 采购单入仓信息
     */
    private PurchaseOrderEnterWarehouseVO purchaseOrderEnterWarehouse;

    /**
     * 操作时间（非必填，不填时，默认用当前时间）
     */
    private Long operateTime;
}
