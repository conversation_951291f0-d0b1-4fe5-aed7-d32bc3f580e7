package com.jdi.isc.order.center.domain.purchaseOrder.biz;


import lombok.Data;

@Data
public class DuccConfigIopInvoiceInfoVo {
    /**
     * 主体
     */
    private Integer tradeRoute;

    /**
     * iopPin
     */
    private String iopPin;
    /**
     * 客户端
     */
    private String clientId;

    /**
     * true：跨境直供模式， false:跨境本土（各个国家站）
     */
    private boolean crossBorderModel;

    /**
     * 报关相关信息
     */
    private DuccConfigCustomsInfoVO customsInfoVO;

    /**
     * 发票信息
     */
    private InvoiceInfo invoiceInfo;
}
