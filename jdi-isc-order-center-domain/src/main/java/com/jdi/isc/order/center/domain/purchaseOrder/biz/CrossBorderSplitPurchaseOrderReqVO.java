package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.order.biz.IopOrderSplitContext;
import com.jdi.isc.order.center.domain.order.po.OrderBizInfoPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderWarePO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 跨境采购单拆单请求
 */
@Data
public class CrossBorderSplitPurchaseOrderReqVO extends SplitPurchaseOrderReqVO{

    @NotNull(message = "iopPin is not null")
    private String iopPin;

    @NotNull(message = "iopOrderId is not null")
    private Long iopOrderId;

    private List<PurchaseOrderPO> existParentAndChildPurchaseOrderPOList;

    private Map<String, List<PurchaseOrderWarePO>> existPurchaseOrderWarePOMap;

    private List<OrderBizInfoPO> parentAndChildOrderBizInfoPOList;

    /**
     * 原始父单id
     */
    private Long iopParentOrderId;

    /**
     * 子单列表
     */
    private List<IopOrderSplitContext> childrenOrderList;

}
