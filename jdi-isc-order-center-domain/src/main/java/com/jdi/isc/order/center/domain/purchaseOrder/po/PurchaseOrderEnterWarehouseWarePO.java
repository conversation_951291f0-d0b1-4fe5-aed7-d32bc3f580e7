package com.jdi.isc.order.center.domain.purchaseOrder.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @Description 采购单入仓商品表
 * @Date 2024/8/6 23:53 下午
 */
@TableName(value = "jdi_isc_purchase_order_enter_warehouse_ware_sharding")
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderEnterWarehouseWarePO extends BasicPO {

    /**
     * 采购单
     */
    private String purchaseOrderId;

    /**
     * 第三方来源标识
     */
    private String dataSource;

    /**
     *  唯一标识
     */
    private String uniqueFlag;

    /**
     * 入仓商品id
     */
    private Long skuId;

    /**
     * 入仓商品数量
     */
    private Integer num;

}
