package com.jdi.isc.order.center.domain.settlement.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value ="jdi_isc_purchase_order_settlements_sharding")
public class PurchaseOrderSettlementsPo extends BasicPO {

    // purchase_order_id
    private String purchaseOrderId;

    // 多个结算单号拼接
    private String requestIds;

    // 付款状态：1待付款 2部分付款 3已付款
    private Integer payStatus;

    // 付款完成时间
    private Long payTime;

    // 供应商确认状态：0待确认 1部分确认 2已确认
    private Integer supplierConfirmStatus;

    // 供应商确认时间
    private Long supplierConfirmTime;


}
