
package com.jdi.isc.order.center.domain.frontOrder.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @description 集运中心出入库类型
 * <AUTHOR>
 * @date 2024/09/11
 **/
public enum OperateTypeEnum {
    IN_BOUND(0,"入库完成"),
    OUT_BOUND(1,"出库完成"),
    ;

    private Integer code;
    private String desc;

    public static Map<Integer, OperateTypeEnum> enumMap = new HashMap();
    public static Map<Integer, String> codeDescMap = new HashMap();

    static {
        for (OperateTypeEnum val : values()) {
            enumMap.put(val.getCode(), val);
            codeDescMap.put(val.getCode(), val.getDesc());
        }
    }

    OperateTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }

    public static OperateTypeEnum forCode(Integer code) {
        return enumMap.get(code);
    }
}