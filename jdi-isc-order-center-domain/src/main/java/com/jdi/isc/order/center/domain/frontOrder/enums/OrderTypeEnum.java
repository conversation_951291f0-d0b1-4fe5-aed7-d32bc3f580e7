package com.jdi.isc.order.center.domain.frontOrder.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @description 集运中心订单类型
 * <AUTHOR>
 * @date 2024/09/11
 **/
public enum OrderTypeEnum {
    /**
     * 国际集运仓订单类型。
     */
    I18N_CONCENTRATED_TRANSPORT(1,"国际集运仓订单"),
    /**
     * 表示国内集运仓订单类型。
     */
    // 待集运中心确认5的含义
    CODE_5(5,"国内集运仓订单"),
    /**
     * 表示国际本对本订单类型。
     */
    LOCAL(301,"国际本对本订单"),
    /**
     * 表示国际跨境履约订单类型。
     */
    CROSS_BORDER(302,"国际跨境履约订单"),
    /**
     * 表示国际备货订单类型。（不区分直发、跨境）
     */
    PURCHASE(303, "国际备货订单")
    ;

    private Integer code;
    private String desc;

    public static Map<Integer, OrderTypeEnum> enumMap = new HashMap();
    public static Map<Integer, String> codeDescMap = new HashMap();

    static {
        for (OrderTypeEnum val : values()) {
            enumMap.put(val.getCode(), val);
            codeDescMap.put(val.getCode(), val.getDesc());
        }
    }

    OrderTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }

    public static OrderTypeEnum forCode(Integer code) {
        return enumMap.get(code);
    }
}