package com.jdi.isc.order.center.domain.snapshot.price;

import com.jdi.isc.order.center.domain.snapshot.common.SnapshotBasicVO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 巴西税价快照对象
 * @Author: zhaojianguo21
 * @Date: 2025/5/21 14:51
 **/

@Data
public class SnapshotBrazilTaxFeeDetailVO extends SnapshotBasicVO {

    /**
     * 商品价格的总上涨指数
     */
    private BigDecimal grossUpPriceIndex;

    /**
     * 单位价格
     */
    private BigDecimal perUnitPrice;

    /**
     * icms税率
     */
    private BigDecimal icmsRate;

    /**
     * icms税值
     */
    private BigDecimal icmsTaxValue;

    /**
     * IPI税率
     */
    private BigDecimal ipiRate;

    /**
     * IPI税值
     */
    private BigDecimal ipiTaxValue;

    /**
     * pis税率
     */
    private BigDecimal pisRate;

    /**
     * pis税值
     */
    private BigDecimal pisTaxValue;

    /**
     * cofins税率
     */
    private BigDecimal cofinsRate;

    /**
     *  cofins税值
     */
    private BigDecimal cofinsTaxValue;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税金
     */
    private BigDecimal taxes;

    /**
     * utilization
     */
    private String utilization;

}
