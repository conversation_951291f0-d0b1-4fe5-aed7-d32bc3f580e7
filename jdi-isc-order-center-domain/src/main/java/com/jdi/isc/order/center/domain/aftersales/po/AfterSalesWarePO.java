package com.jdi.isc.order.center.domain.aftersales.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasePO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 售后单商品db实体
 * <AUTHOR>
 * @date 20240815
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "jdi_isc_after_sales_order_ware_sharding")
public class AfterSalesWarePO extends BasePO {
    /**
     * 售后单号
     */
    private String afterSalesOrderId;
    /**
     * 三方售后单号
     */
    private String thirdAfterSalesOrderId;
    /**
     * 京东国际订单号
     */
    private Long orderId;
    /**
     * mku商品号
     */
    private Long mkuId;
    /**
     * sku商品号
     */
    private Long skuId;
    /**
     * 商品数量
     */
    private Integer mkuNum;

    /**
     * 版本号
     */
    private Integer version;

}
