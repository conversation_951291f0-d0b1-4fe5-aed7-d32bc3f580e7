package com.jdi.isc.order.center.domain.aftersales.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasePO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 售后单db实体
 * <AUTHOR>
 * @date 20240815
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "jdi_isc_after_sales_order_sharding")
@NoArgsConstructor
public class AfterSalesPO extends BasePO {
    /**
     * 售后单号
     */
    private String afterSalesOrderId;
    /**
     * 三方售后单号
     */
    private String thirdAfterSalesOrderId;
    /**
     * 京东国际订单号
     */
    private Long orderId;
    /**
     * iop订单号
     */
    private Long iopOrderId;
    /**
     * 售后单状态:10待处理33处理中50已完成60取消
     */
    private Integer status;
    /**
     * 售后类型：退货(10)、换货(20)、维修(30)
     */
    private Integer customerExpectType;
    /**
     * 售后类型：退货(10)、换货(20)、维修(30)
     */
    private Integer processingType;
    /**
     * 产品问题描述，最多1000字符
     */
    private String customerQuestionDesc;
    /**
     * 是否需要检测报告
     */
    private Boolean isNeedDetectionReport;
    /**
     * 问题描述图片.最多2000字符支持多张图片，用逗号分隔（英文逗号）
     */
    private String questionPic;
    /**
     * 是否有包装
     */
    private Boolean isHasPackage;
    /**
     * 包装描述：0 无包装 10 包装完整 20 包装破损
     */
    private Integer packageDesc;
    /**
     * 包装描述：0 无包装 10 包装完整 20 包装破损
     */
    private String pin;
    /**
     * 合同号
     */
    private String contractNum;
    /**
     * 客户号
     */
    private String clientCode;
    /**
     * 客户名称
     */
    private String clientName;
    /**
     * 来源: WIOP, WISP
     */
    private String sourceCode;
    /**
     * 联系人
     */
    private String customerContactNameEncrypt;
    /**
     * 联系电话
     */
    private String customerTelEncrypt;
    /**
     * 手机号
     */
    private String customerMobilePhoneEncrypt;
    /**
     * Email
     */
    private String customerEmailEncrypt;
    /**
     * 邮编
     */
    private String customerPostcodeEncrypt;
    /**
     * 联系人
     */
    private String customerContactNameIndex;
    /**
     * 联系电话
     */
    private String customerTelIndex;
    /**
     * 手机号
     */
    private String customerMobilePhoneIndex;
    /**
     * Email
     */
    private String customerEmailIndex;
    /**
     * 邮编
     */
    private String customerPostcodeIndex;
    /**
     * 预计完成时间
     */
    private Long expectedCompleteTime;
    /**
     * 实际完成时间
     */
    private Long actualCompleteTime;
    /**
     * 售后原因
     */
    private Integer afterSalesReasonCode;

    /**
     * 问题定性
     */
    private Integer afterSalesReasonLiabilityType;

    /**
     * 审核依据：描述售后情况，不超500字符
     */
    private String afterSalesApproveRemark;

    /**
     * 申请来源：{@link com.jdi.isc.order.center.api.constants.afterSales.AfterSalesApplicationSourceConstant}
     */
    private Integer applicationSource;

    /**
     * 关注人
     */
    private String follower;

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 售后单商品信息
     */
    @TableField(exist = false)
    private List<AfterSalesWarePO> afterSalesWare;

    /**
     * 原因
     */
    private String reason;

}
