package com.jdi.isc.order.center.domain.aftersales.biz;

import com.jdi.isc.order.center.common.biz.BasePageVO;
import com.jdi.isc.order.center.domain.common.biz.DataIsolationQueryVO;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/24 17:25
 */
@Data
public class AfterSalesPageReqVO extends BasePageVO {

    /**
     * 售后联系人
     */
    private String contactUser;
    /**
     * 审核结果 10：退货退款，20：取旧换新，30：维修，40：仅退款，50：关闭服务单，90：补发
     */
    private Integer processingType;
    /**
     * 售后单状态 10:待处理, 120:承担费用, 200:待取件/维修, 300: 待换货/补货, 400: 退款中, 50:已完成, 60:取消
     */
    private Integer status;
    /**
     * 售后提交开始时间
     */
    private Date submitTimeStart;
    /**
     * 售后提交结束时间
     */
    private Date submitTimeEnd;

    /**
     * 客户简码
     */
    private String clientCode;
    /**
     * 合同号
     */
    private String contractNum;
    /**
     * IOP订单号列表
     */
    private Set<Long> iopOrderIds;

    /**
     * 京东国际订单号列表
     */
    private Set<Long> orderIds;
    /**
     * 售后订单号列表
     */
    private Set<String> afterSalesOrderIds;

    /**
     * 售后原因: 310:商品质量问题、320:商品破损/包装问题、330:少件/发错货/未收到货、340:商品与页面描述不符、350:买多/买错/不想要、999:其他
     */
    private Integer afterSalesReasonCode;


    /**
     * 问题定性 410：商家责、420：物流责、430：客户高期、440：京责、450:需求变更
     */
    private Integer afterSalesReasonLiabilityType;

    /**
     * 用户期望售后类型：退货(10)、换货(20)、维修(30)、补货(90)
     */
    private Integer customerExpectType;

    /**
     * 排序字段
     */
    private String sort;

    /**
     * 排序类型
     */
    private String sortType;

    /**
     * 操作人erp
     */
    private String operate;

    /**
     * 角色身份 @link AfterSalesManageRoleConstant
     */
    private String role;

    /**
     * 数据隔离
     */
    private DataIsolationQueryVO dataIsolationQueryVO;

    /**
     * 国家
     */
    private String countryCode;
}
