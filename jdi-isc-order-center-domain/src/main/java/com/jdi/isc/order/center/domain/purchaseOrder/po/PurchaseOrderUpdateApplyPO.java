package com.jdi.isc.order.center.domain.purchaseOrder.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 采购单修改信息申请表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "jdi_isc_purchase_order_update_apply_sharding")
public class PurchaseOrderUpdateApplyPO extends BasicPO {

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 修改类型，1:修改采购单金额
     */
    private Integer updateType;

    /**
     * 修改前信息
     */
    private String beforePurchaseOrderInfo;

    /**
     * 修改后信息
     */
    private String afterPurchaseOrderInfo;

    /**
     * 申请业务ID
     */
    private String applyId;

    /**
     * 申请业务编码
     */
    private String applyCode;

    /**
     * 唯一性校验字段
     */
    private String requestId;

    /**
     * 操作状态 0:待处理 1:成功 -1:取消申请
     */
    private Integer applyStatus;

    /**
     * 版本号
     */
    private Integer version;
}