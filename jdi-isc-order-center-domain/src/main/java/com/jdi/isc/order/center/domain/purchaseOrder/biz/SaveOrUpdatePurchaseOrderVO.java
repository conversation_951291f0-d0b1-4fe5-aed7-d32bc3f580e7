package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.order.po.OrderBizInfoPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderConsigneePO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderWarePO;
import lombok.Data;

import java.util.List;

/**
 * 修改或更新采购单信息
 */
@Data
public class SaveOrUpdatePurchaseOrderVO {

    /**
     * 采购单
     */
    private PurchaseOrderPO purchaseOrderPO;

    /**
     * 跨境biz信息
     */
    private OrderBizInfoPO orderBizInfoPO;

    /**
     * 商品信息
     */
    private List<PurchaseOrderWarePO> purchaseOrderWarePOList;

    /**
     * 采购单收货地址
     */
    private List<PurchaseOrderConsigneePO> purchaseOrderConsigneePOList;
}
