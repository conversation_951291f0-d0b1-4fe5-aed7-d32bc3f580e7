package com.jdi.isc.order.center.domain.snapshot.mku;

import com.jdi.isc.order.center.domain.snapshot.common.SnapshotSaleUnitVO;
import com.jdi.isc.order.center.domain.snapshot.common.SnapshotBasicVO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Description: MKU基本信息快照对象
 * @Author: zhaojianguo21
 * @Date: 2025/5/21 15:03
 **/
@Data
public class SnapshotMkuVO extends SnapshotBasicVO {
    /**
     * mkuId
     */
    private Long mkuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * jdSkuId
     */
    private Long jdSkuId;

    /**
     * 商品多语言名称
     */
    private Map<String/*语种*/,String/*名称*/> nameLangMap;

    /**
     * 销售单位
     */
    private SnapshotSaleUnitVO saleUnit;

    /**
     * mku主图
     */
    private String mainImg;

    /**
     * mku细节图
     */
//    private List<String> detailImg;
    private String detailImg;

    /** 所属国家*/
    private String sourceCountryCode;

    /**
     * mku.是否报关
     */
    private Integer customsClearance;

    /**
     * ncmCode
     */
    private String ncmCode;

    /**
     * 采购模式
     */
    private Integer purchaseModel;

    /**
     * 是否备货
     */
    private Integer stockUp;
}
