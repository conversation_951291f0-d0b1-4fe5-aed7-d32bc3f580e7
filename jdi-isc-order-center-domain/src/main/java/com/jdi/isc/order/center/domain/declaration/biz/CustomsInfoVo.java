package com.jdi.isc.order.center.domain.declaration.biz;

import com.jdi.isc.order.center.domain.common.biz.BaseVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;



/**
 * 报关信息
 * <AUTHOR>
 * @date 20241109
 */

@Data
public class CustomsInfoVo extends BaseVO {



    /**
     * 关联报关单的唯一标识符。
     * //关联报关单Id
     */
    private String   customsId;

    /**
     * // 来源系统【传输所在系统的域名】
     */
    private String   sourceSystem;

    /**
     * //客户编码
     */
    private String   customerCode;

    /**
     * //客户名称
     */
    private String   customerName;

    /**
     * //报关服务商Id
     */
    private Long     customsAgentId;

    /**
     * // 报关服务商名称
     */
    private String   customsAgentName;

    /**
     * //监管方式（0110/9610）
     */
    private String   superviseWay;


    /**
     * //成交方式（FOB/CIF）
     */
    private String   dealWay;


    /**
     * //运费
     */
    private String   freightFee;


    /**
     * //保费
     */
    private String   insPRM;


    /**
     * //杂费
     */
    private String   miscEXP;


    /**
     * //报关单金额/销售金额
     */
    private BigDecimal customsAmount;



    /**
     * //制单时间 yyyy-MM-dd HH:mm:ss
     */
    private Date  applyDate;



    /**
     * 制单人ERP账号。
     */
    private String  applyUserErp;//制单人erp



    /**
     * 制单人姓名
     */
    private String  applyUserName;



    /**
     * 订单状态0草稿1已提交
     */
    private Integer  status;




}
