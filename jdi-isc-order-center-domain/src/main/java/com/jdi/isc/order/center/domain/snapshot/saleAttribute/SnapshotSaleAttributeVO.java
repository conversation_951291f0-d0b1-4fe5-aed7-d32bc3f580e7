package com.jdi.isc.order.center.domain.snapshot.saleAttribute;

import com.jdi.isc.order.center.domain.snapshot.common.SnapshotBasicVO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Description: 销售属性快照对象
 * @Author: zhaojianguo21
 * @Date: 2025/5/21 14:51
 **/
@Data
public class SnapshotSaleAttributeVO extends SnapshotBasicVO {

    /** 销售属性多语言名称 */
    private Map<String/*语种*/,String/*名称*/> nameLangMap;

    /**
     * 销售属性值列表
     */
    private List<SnapshotSaleAttributeValueVO> attributeValues;
}
