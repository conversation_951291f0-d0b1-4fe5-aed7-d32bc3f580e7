package com.jdi.isc.order.center.domain.mapstruct.applyOrder;

import com.jdi.isc.order.center.domain.applyOrder.biz.ApplyOrderVO;
import com.jdi.isc.order.center.domain.applyOrder.po.ApplyOrderPO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 对象转换
 * <AUTHOR>
 * @date 20240501
 **/
@Mapper
public interface ApplyOrderConvert {

    ApplyOrderConvert INSTANCE = Mappers.getMapper(ApplyOrderConvert.class);


    @InheritConfiguration
    ApplyOrderPO vo2po(ApplyOrderVO applyOrderVO);
    @InheritConfiguration
    ApplyOrderVO po2vo(ApplyOrderPO applyOrderPO);

    @InheritConfiguration
    List<ApplyOrderPO> listVo2Po(List<ApplyOrderVO> applyOrderVO);
}
