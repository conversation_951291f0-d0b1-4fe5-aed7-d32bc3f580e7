package com.jdi.isc.order.center.domain.mapstruct.freight;

import com.alibaba.fastjson.JSONObject;
import com.jdi.isc.aggregate.read.wiop.api.freight.req.CalculateFreightMkuReadReq;
import com.jdi.isc.aggregate.read.wiop.api.freight.res.CalculateFreightItemReadResp;
import com.jdi.isc.aggregate.read.wiop.api.orderCenter.resp.OrderMkuDetailReadResp;
import com.jdi.isc.aggregate.read.wiop.api.orderCost.req.OrderCostMkuInfoReadReq;
import com.jdi.isc.order.center.api.biz.resp.FreightInfoOrderApiResp;
import com.jdi.isc.order.center.api.orderRead.biz.manage.OrderReadFreightApiDTO;
import com.jdi.isc.order.center.domain.freight.po.OrderFreightPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/28 18:02
 */
@Mapper
public interface OrderFreightConvert {

    OrderFreightConvert INSTANCE = Mappers.getMapper(OrderFreightConvert.class);

    @Mappings({
            @Mapping(target = "isMagnetic", source = "magnetic"),
            @Mapping(target = "isElectric", source =  "electric")
    })
    OrderCostMkuInfoReadReq orderMkuDetailReadDTO2readReq(OrderMkuDetailReadResp mkuDetailReadDTO);

    @Mappings({
            @Mapping(target = "freightPrice", expression = "java(orderFreightPO.getFreightPrice().toPlainString())")
    })
    FreightInfoOrderApiResp po2apiReq(OrderFreightPO orderFreightPO);

    @Mappings({
            @Mapping(target = "freightPrice", source = "freight")
    })
    OrderFreightPO readResp2orderFreightPO(CalculateFreightItemReadResp calculateFreightItemReadResp);

    OrderFreightPO copyPO(OrderFreightPO orderFreightPO);

    @Mappings({
            @Mapping(target = "createTime", expression = "java(orderFreightPO.getCreateTime().getTime())"),
            @Mapping(target = "updateTime", expression = "java(orderFreightPO.getUpdateTime().getTime())")
    })
    OrderReadFreightApiDTO po2apiDTO(OrderFreightPO orderFreightPO);

    List<OrderReadFreightApiDTO> listPo2apiDTO(List<OrderFreightPO> orderFreightPOList);
}
