package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 商品最新采购时间查询 VO实体类
 * @Author: zhaojianguo21
 * @Date: 2025/03/11
 **/

@Data
public class WareLastPurchaseTimeVO {

    @EqualsAndHashCode(callSuper = true)
    @Data
    @NoArgsConstructor
    public static class Request extends BasicVO implements Serializable {
        /**
         * 国家码
         */
        private String countryCode;

        /** 商品ID */
        private List<Long> skuIds;
    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BasicVO implements Serializable{
        /** skuId */
        private Long skuId;

        /**
         * 下单时间
         * 之前将创建时间作为最近一次采购时间使用。后续应该删除掉此字段。
         * */
        private Long purchaseCreateTime;

        /** 最近一次采购时间 */
        private Long lastPurchaseTime;
    }

}
