package com.jdi.isc.order.center.domain.mapstruct.supplier;

import com.jdi.isc.order.center.api.supplier.req.VcHomePageDashboardReq;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface StatisticsReadConvert {

    StatisticsReadConvert INSTANCE = Mappers.getMapper(StatisticsReadConvert.class);
    PurchaseOrderVO transportDTO2VO(VcHomePageDashboardReq dto);
}
