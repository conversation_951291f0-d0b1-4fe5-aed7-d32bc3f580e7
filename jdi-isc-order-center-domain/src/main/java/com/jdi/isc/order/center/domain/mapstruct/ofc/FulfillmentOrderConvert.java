package com.jdi.isc.order.center.domain.mapstruct.ofc;

import com.jdi.isc.order.center.api.ofc.biz.req.ManualSplitOrderApiReq;
import com.jdi.isc.order.center.api.ofc.biz.req.ManualSplitOrderWareDTO;
import com.jdi.isc.order.center.api.ofc.biz.req.UpdateStockInOutOrderStatusManageApiReq;
import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.ofc.fulfillmentOrder.biz.FulfillmentOrderContextVO;
import com.jdi.isc.order.center.domain.ofc.fulfillmentOrder.biz.FulfillmentOrderPageVO;
import com.jdi.isc.order.center.domain.ofc.fulfillmentOrder.po.FulfillmentOrderConsigneePO;
import com.jdi.isc.order.center.domain.ofc.fulfillmentOrder.vo.FulfillmentSplitOrderDetailVO;
import com.jdi.isc.order.center.domain.ofc.fulfillmentOrder.vo.FulfillmentSplitOrderVO;
import com.jdi.isc.order.center.domain.ofc.stockIoOrder.biz.UpdateStockInOutOrderStatusReqVO;
import com.jdi.isc.order.center.domain.order.po.OrderConsigneePO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/3/7
 * @Description
 */
@Mapper
public interface FulfillmentOrderConvert {

    /**
     * 获取FulfillmentOrderConvert接口的实例。
     */
    FulfillmentOrderConvert INSTANCE = Mappers.getMapper(FulfillmentOrderConvert.class);

    /**
     * 将OrderConsigneePO对象转换为FulfillmentOrderConsignee对象。
     * @param orderConsigneePO OrderConsigneePO对象，表示订单收货人信息。
     * @return 转换后的FulfillmentOrderConsignee对象。
     */
    @Mappings({
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateTime", ignore = true)
    })
    FulfillmentOrderConsigneePO orderConsigneePO2FulfillmentOrderConsignee(OrderConsigneePO orderConsigneePO);


    /**
     * 将FulfillmentOrderPageVO.Response对象转换为FulfillmentOrderContextVO对象。
     * @param pageResult FulfillmentOrderPageVO.Response对象，包含订单页面的结果信息。
     * @return 转换后的FulfillmentOrderContextVO对象。
     */
    FulfillmentOrderContextVO orderPageResponse2FulfillmentOrderContextVO(FulfillmentOrderPageVO.Response pageResult);


    /**
     * 将手动拆单API请求对象转换为手动拆单VO对象
     * @param manualSplitOrderApiReq 手动拆单API请求对象，包含拆单相关信息
     * @return 转换后的手动拆单VO对象，包含拆单详情列表
     */
    @Mapping(source = "orderId", target = "bizOrderId")
    @Mapping(source = "manualSplitOrderWareDTOList", target = "fulfillmentSplitOrderDetailVOList")
    FulfillmentSplitOrderVO manualSplitApiReq2voReq(ManualSplitOrderApiReq manualSplitOrderApiReq);
    // 当自动检测不到时，可以使用 @Mapping 的 qualifiedByName 或 expression 等属性
    default List<FulfillmentSplitOrderDetailVO> convertDetailList(List<ManualSplitOrderWareDTO> list) {
        if (list == null) {
            return null;
        }
        return list.stream()
                .map(this::convertDetail)
                .collect(Collectors.toList());
    }

    @Mapping(source = "availableStock", target = "stockNum")
    FulfillmentSplitOrderDetailVO convertDetail(ManualSplitOrderWareDTO dto);


    /**
     * 复制履约拆分子订单详情VO对象
     * @param fulfillmentSplitOrderDetailVO 待复制的配送拆分订单详情VO对象
     * @return 复制后的新配送拆分订单详情VO对象
     */
    FulfillmentSplitOrderDetailVO copyPo(FulfillmentSplitOrderDetailVO fulfillmentSplitOrderDetailVO);

}
