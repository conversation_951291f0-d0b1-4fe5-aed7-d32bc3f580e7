package com.jdi.isc.order.center.domain.forecast.req;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 商品最新采购时间请求实体类
 * @Author: zhaojianguo21
 * @Date: 2025/06/18
 **/

@Data
@NoArgsConstructor
public class ForecastOrderWareLastPurchaseTimeReqVO {

    /**
     * 国家码
     */
    @NotNull(message = "国家码不能为空")
    private String countryCode;

    /** 商品ID */
    @NotEmpty(message = "商品ID不能为空")
    private List<Long> skuIds;

}
