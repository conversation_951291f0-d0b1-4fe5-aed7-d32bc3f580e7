package com.jdi.isc.order.center.domain.aftersales.biz;

import com.jdi.isc.order.center.domain.common.biz.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 售后单商品db实体
 * <AUTHOR>
 * @date 20240815
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class AfterSalesWareVO extends BaseVO {
    /**
     * 售后单号
     */
    private Set<String> afsIds;
    /**
     * 售后单号
     */
    private String afsId;

    /**
     * 订单号
     */
    private Set<Long> orderIds;

    /**
     * 订单号
     */
    private Long orderId;

    public AfterSalesWareVO(Set<String> afsIds){
        this.afsIds = afsIds;
    }

}
