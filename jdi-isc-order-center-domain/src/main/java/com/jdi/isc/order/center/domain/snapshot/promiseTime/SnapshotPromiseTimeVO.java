package com.jdi.isc.order.center.domain.snapshot.promiseTime;

import com.jdi.isc.order.center.domain.snapshot.common.SnapshotBasicVO;
import lombok.Data;

import java.util.Map;

/**
 * @Description: 履约时效快照对象
 * @Author: zhaojianguo21
 * @Date: 2025/5/21 14:51
 **/
@Data
public class SnapshotPromiseTimeVO extends SnapshotBasicVO {


    /** 履约时效文案多语言 */
    private Map<String/*语种*/,String/*名称*/> nameLangMap;

}
