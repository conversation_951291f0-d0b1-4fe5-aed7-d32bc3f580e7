package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 跨境采购单拆单请求
 */
@Data
public class LocalSplitPurchaseOrderReqVO extends  SplitPurchaseOrderReqVO{


    /**
     * 拆单状态
     */
    private Integer splitOrderStatus;

    /**
     * 拆单原因
     */
    @NotNull(message = "splitRemark is not null")
    private String splitRemark;


    /**
     * 被拆单 采购单id
     */
    @NotNull(message = "splitPurchaseOrderId is not null")
    private String splitPurchaseOrderId;
    /**
     * 拆单信息集合
     */
    @NotEmpty(message = "splitOrderList is not empty")
    private List<PurchaseOrderVO> splitOrderList;

    @NotNull(message = "updater is not null")
    protected String updater;

}
