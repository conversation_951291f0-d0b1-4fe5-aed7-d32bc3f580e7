package com.jdi.isc.order.center.domain.waybill.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: 一段运单 VO实体类
 * @Author: zhaojianguo21
 * @Date: 2024/06/13 15:17
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
//@AllArgsConstructor
public class PurchaseOrderWaybillEnterVO extends BasicVO {

    /** 运单号 */
    private String waybillNum;

    /**
     * 入仓资料
     */
    private String enterMaterial;

    /** 运单状态 1:已发运 2:已入仓
     *  {@link com.jdi.isc.order.center.domain.enums.PurchaseOrderWaybillStatusEnum } */
    private Integer status;

    /** 运单状态 1:已发运 2:已入仓
     *  {@link com.jdi.isc.order.center.domain.enums.PurchaseOrderWaybillStatusEnum } */
    private Integer noEqStatus;
}
