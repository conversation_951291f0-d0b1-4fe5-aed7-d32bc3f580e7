package com.jdi.isc.order.center.domain.mapstruct.ofc;

import com.jdi.isc.order.center.api.common.SystemInfoOrderApiReq;
import com.jdi.isc.order.center.api.ofc.biz.req.UpdateStockInOutOrderStatusManageApiReq;
import com.jdi.isc.order.center.api.ofc.biz.rsp.StockInOutOrderDetailResDTO;
import com.jdi.isc.order.center.api.ofc.biz.rsp.StockInOutOrderWareResDTO;
import com.jdi.isc.order.center.domain.ofc.stockIoOrder.biz.UpdateStockInOutOrderStatusReqVO;
import com.jdi.isc.order.center.domain.ofc.stockIoOrder.po.StockInOutOrderMsgPO;
import com.jdi.isc.order.center.domain.ofc.stockIoOrder.po.StockInOutOrderPO;
import com.jdi.isc.order.center.domain.ofc.stockIoOrder.po.StockInOutOrderWarePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author：xubing82
 * @Date：2025/3/31 18:26
 * @Description：StockInOutOrderConvert
 */
@Mapper
public interface StockInOutOrderConvert {

    StockInOutOrderConvert INSTANCE = Mappers.getMapper(StockInOutOrderConvert.class);

    StockInOutOrderDetailResDTO stockInOutOrderPO2ResDTO(StockInOutOrderPO stockInOutOrderPO);

    List<StockInOutOrderWareResDTO> stockInOutOrderWarePO2ResDTOList(List<StockInOutOrderWarePO> stockInOutOrderWarePOList);

    StockInOutOrderMsgPO po2msgPo(StockInOutOrderPO stockInOutOrderPO);

    UpdateStockInOutOrderStatusReqVO manageApiReq2voReq(UpdateStockInOutOrderStatusManageApiReq updateStockInOutOrderStatusManageApiReq);

    SystemInfoOrderApiReq req2ApiReq(UpdateStockInOutOrderStatusManageApiReq updateStockInOutOrderStatusManageApiReq);


}

