package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/2 6:13 下午
 */
@Data
public class InvoicePageVO {

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 发票审核状态
     */
    private Integer invoiceApprovalStatus;

    /**
     * 发货时间开始
     */
    private Long beginShippedTime;
    /**
     * 发货时间结束
     */
    private Long endShippedTime;
    /**
     * 完成时间开始
     */
    private Long beginFinishTime;
    /**
     * 完成时间结束
     */
    private Long endFinishTime;

    /**
     * 国家或地区的代码。
     */
    private String countryCode;

    /**
     * 供应商代码
     */
    private String supplierCode;


    /**
     * 订单类型
     */
    private Integer purchaseOrderType;

    /**
     * 采购单状态的集合。
     */
    private Set<Integer> purchaseOrderStatusSet;
    /**
     * 起始页
     */
    private Long index;

    /**
     * 页大小
     */
    private Long size;

    private Long offset;



    /**
     * 审核结果，0表示人工通过，1表示发票问题，2表示采购单问题，5表示审核自动通过。
     */
    private Integer taxReviewResult;


    /**
     * 审核状态，0表示待审核，1表示审核完成，2表示自主撤回。
     */
    private Set<Integer> taxReviewStatus;


    /**
     * 审核人
     */
    private String taxReviewer;

    /**
     * 发票上传状态，0表示未上传，1表示已上传。
     */
    private Integer invoiceUploadStatus;
}
