package com.jdi.isc.order.center.domain.orderSnapshot.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;

/**
 * @Description: 订单商品快照 实体类
 * @Author: zhaojianguo21
 * @Date: 2025/05/26 20:48
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class OrderWareSnapshotDetailReqVO extends BasicVO {

    /**
     * 订单号
     */
    @NotNull(message = "订单号不能为空")
    private Long orderId;

    /**
     * mku编码
     */
    @NotNull(message = "mku编码不能为空")
    private Long mkuId;

    /**
     * 履约sku编码
     */
    @NotNull(message = "履约sku编码不能为空")
    private Long skuId;

    /**
     * 商品快照url
     */
    private String snapshotUrl;

    /**
     * 创建快照开始时间
     */
    private Long snapshotStartTime;

    /**
     * 创建快照结束时间
     */
    private Long snapshotEndTime;

    /**
     * 快照状态,0=未创建,1=创建成功,2=创建失败,3=部分信息创建失败
     */
    @NotNull(message = "快照状态,0=未创建,1=创建成功,2=创建失败,3=部分信息创建失败不能为空")
    private Integer snapshotStatus;

}