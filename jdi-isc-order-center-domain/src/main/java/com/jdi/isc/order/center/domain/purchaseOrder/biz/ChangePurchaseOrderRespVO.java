package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderWarePO;
import lombok.Data;

import java.util.List;

/**
 * 创建采购单上下文
 */
@Data
public class ChangePurchaseOrderRespVO {

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 采购单信息
     */
    private PurchaseOrderPO oldPurchaseOrderPO;

    /**
     * 采购单商品信息
     */
    private List<PurchaseOrderWarePO> oldPurchaseOrderWarePOList;

    /**
     * 采购单信息
     */
    private PurchaseOrderPO newPurchaseOrderPO;

    /**
     * 采购单商品信息
     */
    private List<PurchaseOrderWarePO> newPurchaseOrderWarePOList;

}
