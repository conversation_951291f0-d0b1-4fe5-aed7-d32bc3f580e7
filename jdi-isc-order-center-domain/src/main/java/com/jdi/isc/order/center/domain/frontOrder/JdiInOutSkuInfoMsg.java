package com.jdi.isc.order.center.domain.frontOrder;

import lombok.Data;

/**
 * 类描述：
 * 出入库订单消息sku信息
 *
 * <AUTHOR>
 * @date 2024/10/12
 */

@Data
public class JdiInOutSkuInfoMsg {

    /**
     * 国内SKU
     */
    private Long skuId;

    /**
     * 国际SKU ID
     */
    private Long intlSkuId;

    /**
     * SKU名称
     */
    private String name;

    /**
     * 测量重量
     */
    private String measureWeight;

    /**
     * 测量体积
     */
    private String measureVolume;
}
      