package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import com.jdi.isc.order.center.domain.validate.group.PurchaseShipGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/5/28
 **/
@Data
public class PurchaseOrderTransportVO extends BasicVO {
    /**
     * 采购单
     */
    @NotNull(message = "purchaseOrderId can not be empty")
    private String purchaseOrderId;
    /**
     * 运输方式
     */
    @NotNull(message = "transportMethod can not be empty")
    private Integer transportMethod;
    /**
     * 运输单号
     */
    @NotNull(message = "transportNum can not be empty", groups = PurchaseShipGroup.ExpressGroup.class)
    private String transportNum;
    /**
     * 承运商
     */
    @NotNull(message = "carrier can not be empty", groups = PurchaseShipGroup.ExpressGroup.class)
    private String carrier;
    /**
     * 发货时间
     */
    @NotNull(message = "deliveryDate can not be empty")
    private Long deliveryDate;
    /**
     * 预计送达时间
     */
    @NotNull(message = "expectedDeliveryDate can not be empty")
    private Long expectedDeliveryDate;
    /**
     * 发货资料
     */
    @NotNull(message = "deliveryMaterial can not be empty")
    private String deliveryMaterial;
    /**
     * 发货视频资料
     */
    @NotNull(message = "deliveryMaterialVideo can not be empty")
    private String deliveryMaterialVideo;
    /**
     * 配送人
     */
    @NotNull(message = "deliveryPerson can not be empty", groups = {PurchaseShipGroup.Vehicle.class})
    private String deliveryPerson;
    /**
     * 车牌号
     */
    @NotNull(message = "licensePlate can not be empty", groups = {PurchaseShipGroup.Vehicle.class})
    private String licensePlate;
    /**
     * 配送人电话
     */
    @NotNull(message = "deliveryMobile can not be empty", groups = {PurchaseShipGroup.Vehicle.class})
    private String deliveryMobile;
    /**
     * 配送人电话
     */
    private String deliveryMobileIndex;
    /**
     * 配送人电话
     */
    private String deliveryMobileEncrypt;
    /**
     * 入集货仓时间（国内）
     */
    private Long enterStorehouseTime;
    /**
     * 入备货仓单号
     */
    private String enterWarehouseNo;
}
