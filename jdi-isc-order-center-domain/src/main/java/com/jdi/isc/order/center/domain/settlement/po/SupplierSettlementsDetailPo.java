package com.jdi.isc.order.center.domain.settlement.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value ="jdi_isc_supplier_settlements_detail_sharding")
public class SupplierSettlementsDetailPo extends BasicPO {

    // 采购单号
    private String purchaseOrderId;

    // sku编码
    private Long skuId;

    // 商品数量
    private Integer skuNum;


    // 结算单号（内部结算单）
    private String requestId;

    // 供应商简码
    private String supplierId;

    // 结算金额SupplierSettlementsDetailPo
    private BigDecimal amount;

    // 币种
    private String accountCurrency;


}
