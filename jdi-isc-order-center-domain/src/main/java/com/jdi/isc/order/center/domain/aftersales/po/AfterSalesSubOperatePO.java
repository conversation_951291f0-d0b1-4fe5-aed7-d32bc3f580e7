package com.jdi.isc.order.center.domain.aftersales.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 售后单db实体
 * <AUTHOR>
 * @date 20240815
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "jdi_isc_after_sales_sub_operate_sharding")
@NoArgsConstructor
public class AfterSalesSubOperatePO extends BasicPO {

    /**
     * 操作业务单号
     */
    private String bizId;

    /**
     * 单据类型100.承担费用200.取件维修300.换货补货400.退款
     */
    private Integer bizType;

    /**
     * 售后单号
     */
    private String afterSalesOrderId;

    /**
     * 京东国际订单号
     */
    private Long orderId;

    /**
     * 单据快照信息
     */
    private String bizInfo;

    /**
     * 售后单操作状态
     * AfterSalesSubStatusEnum
     */
    private Integer status;

    /**
     * 版本号
     */
    private Integer version;




}
