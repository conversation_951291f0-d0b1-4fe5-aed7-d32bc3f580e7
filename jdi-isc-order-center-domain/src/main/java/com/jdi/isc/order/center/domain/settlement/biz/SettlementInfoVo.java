package com.jdi.isc.order.center.domain.settlement.biz;

import java.math.BigDecimal;
import java.util.Map;


public class SettlementInfoVo {

    // 租户
    private String tenantCode;

    // 业务时区
    private String timeZone;

    // 无需解析
    private Integer rfSystem;

    // 结算业务
    private Integer settlementBizId;

    // 结算分组
    private Integer settlementGroupId;

    // 唯一值
    private String uuid;

    // 结算单号（内部结算单）
    private String requestId;

    // 操作状态
    private Integer opStatus;

    // 供应商简码
    private String supplierId;

    // 供应商名称
    private String supplierName;

    // 商家简码
    private String shopId;

    // 商家名称
    private String shopName;

    // 付款完成时间
    private String payTime;

    // 业务结算单号（外部结算单号）
    private String outSettleCode;

    // ou
    private String ouId;


    // 操作时间
    private String operatorTime;

    // 操作类型
    private Integer operatorType;

    // 结算金额
    private BigDecimal amount;

    // 付款方式
    private String payType;

    // 付款方向: -1京东付款，1京东收款
    private Integer direction;

    // 核销状态：0无需核销，1未核销，9已核销（收单完成）
    private Integer compareStatus;

    // 结算单类型
    private Integer settleType;

    // 流程实例ID
    private String checkInstanceId;

    // 结算模式
    private Integer settleModel;

    // 审核意见
    private String suggestion;

    // 扩展信息
    private Map<String, Object> extraInfo;


    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public Integer getRfSystem() {
        return rfSystem;
    }

    public void setRfSystem(Integer rfSystem) {
        this.rfSystem = rfSystem;
    }

    public Integer getSettlementBizId() {
        return settlementBizId;
    }

    public void setSettlementBizId(Integer settlementBizId) {
        this.settlementBizId = settlementBizId;
    }

    public Integer getSettlementGroupId() {
        return settlementGroupId;
    }

    public void setSettlementGroupId(Integer settlementGroupId) {
        this.settlementGroupId = settlementGroupId;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Integer getOpStatus() {
        return opStatus;
    }

    public void setOpStatus(Integer opStatus) {
        this.opStatus = opStatus;
    }

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    public String getOutSettleCode() {
        return outSettleCode;
    }

    public void setOutSettleCode(String outSettleCode) {
        this.outSettleCode = outSettleCode;
    }

    public String getOperatorTime() {
        return operatorTime;
    }

    public void setOperatorTime(String operatorTime) {
        this.operatorTime = operatorTime;
    }

    public Integer getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(Integer operatorType) {
        this.operatorType = operatorType;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public Integer getDirection() {
        return direction;
    }

    public void setDirection(Integer direction) {
        this.direction = direction;
    }

    public Integer getCompareStatus() {
        return compareStatus;
    }

    public void setCompareStatus(Integer compareStatus) {
        this.compareStatus = compareStatus;
    }

    public Integer getSettleType() {
        return settleType;
    }

    public void setSettleType(Integer settleType) {
        this.settleType = settleType;
    }

    public String getCheckInstanceId() {
        return checkInstanceId;
    }

    public void setCheckInstanceId(String checkInstanceId) {
        this.checkInstanceId = checkInstanceId;
    }

    public Integer getSettleModel() {
        return settleModel;
    }

    public void setSettleModel(Integer settleModel) {
        this.settleModel = settleModel;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public Map<String, Object> getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(Map<String, Object> extraInfo) {
        this.extraInfo = extraInfo;
    }


    public String getOuId() {
        return ouId;
    }

    public void setOuId(String ouId) {
        this.ouId = ouId;
    }
}
