package com.jdi.isc.order.center.domain.settlement.biz;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Set;

@Data
public class SupplierSettlementPageVO {

    /**
     * 采购订单ID
     */
    private String purchaseOrderId;

    /**
     * 采购单集合
     */
    private Set<String> purchaseOrderIds;

    /**
     * 发票审批状态
     */
    private Integer invoiceApprovalStatus;

    /**
     * 发货开始时间
     */
    private Long beginShippedTime;

    /**
     * 发货结束时间
     */
    private Long endShippedTime;

    /**
     * 是否根据发货时间排序
     */
    private Integer shippedTimeSort;

    /**
     * 采购单完成开始时间
     */
    private Long beginFinishTime;

    /**
     * 采购单完成结束时间
     */
    private Long endFinishTime;

    /**
     * 是否根据完成时间排序
     */
    private Integer finishTimeSort;

    /**
     * 结算单号
     */
    private String settlementId;


    /**
     * 供应商确认状态
     */
    private Integer supplierConfirmStatus;

    /**
     * 支付状态
     */
    private Integer payStatus;

    /**
     * 未支付的状态
     */
    private boolean noPayStatus;

    /**
     * 支付结束时间
     */
    private Long payTimeStart;

    /**
     * 支付开始时间
     */
    private Long payTimeEnd;

    /**
     * 升序 1，降序 -1
     */
    private Integer sort;

    /**
     * 排序类型
     */
    private String sortType;

    /**
     * 国家
     */
    private String countryCode;

    /** 起始页*/
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码错误")
    private Long index;

    /** 页大小*/
    @NotNull(message = "页大小不能为空")
    @Range(min=1, max = 200, message = "分页大小错误")
    private Long size;

    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * 采购订单类型
     */
    private Integer purchaseOrderType;

    /**
     * 采购订单状态集合
     */
    private Set<Integer> purchaseOrderStatusSet;

    /**
     * 待上传的发票
     */
    private boolean queryCanUploadInvoice;
    public long getOffset(){
        return (index-1)*size;
    }
}
