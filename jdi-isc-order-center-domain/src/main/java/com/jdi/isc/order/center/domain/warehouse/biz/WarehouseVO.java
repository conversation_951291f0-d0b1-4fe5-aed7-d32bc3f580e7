package com.jdi.isc.order.center.domain.warehouse.biz;

import com.jdi.isc.order.center.api.common.BaseApiReq;
import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: 仓信息表 VO实体类
 * @Author: wangpeng965
 * @Date: 2024/07/18 11:23
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class WarehouseVO extends BasicPO {
    /**
     * 仓库编码
     */
    private String warehouseNo;

    /**
     * 仓库类型 0:备货仓 1:集货仓
     */
    private Integer type;

    /**
     * 国家/地区编码、CN、TH
     */
    private String countryCode;

    /**
     * 国家/地区id
     */
    private String countryId;

    /**
     * 国家/地区名称
     */
    private String countryName;

    /**
     * 所属洲/省编码
     */
    private Long provinceId;

    /**
     * 所属洲/省编码
     */
    private String provinceName;

    /**
     * 所属城市编码
     */
    private Long cityId;

    /**
     * 所属城市名称
     */
    private String cityName;

    /**
     * 所属县/区编码
     */
    private Long countyId;

    /**
     * 所属县/区名称
     */
    private String countyName;

    /**
     * 所属乡/镇编码
     */
    private Long townId;

    /**
     * 所属乡/镇名称
     */
    private String townName;

    /**
     * 邮政编码
     */
    private String zip;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 联系人id
     */
    private Long contactId;

    /**
     * 联系人名称加密
     */
    private String contactEncrypt;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 收货人电话加密
     */
    private String contactPhoneEncrypt;

    /**
     * 收货人电话
     */
    private String contactPhone;

    /**
     * 收货人手机号加密
     */
    private String contactMobileEncrypt;

    /**
     * 收货人手机号
     */
    private String contactMobile;

    /**
     * 收货人邮箱加密
     */
    private String contactEmailEncrypt;

    /**
     * 收货人邮箱
     */
    private String contactEmail;

    /**
     * 理由
     */
    private String reason;

}
