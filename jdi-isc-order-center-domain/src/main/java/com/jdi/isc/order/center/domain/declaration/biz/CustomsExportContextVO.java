package com.jdi.isc.order.center.domain.declaration.biz;

import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class CustomsExportContextVO {

    /**
     * 订单ids
     */
    private Set<Long> orderIds;

    /**
     * 采购单ids
     */
    private Set<String> poIds;

    /**
     * 目的国
     */
    private String targetCountryCode;

    /**
     * 国家名称
     */
    private Map<String, String> countryNameMap;

    /**
     * 报关订单关系list
     */
    private List<CustomsOrderVo> orderCustomsOrderVoList;

    /**
     * 报关采购单关系list
     */
    private List<CustomsOrderVo> poCustomsOrderVoList;

    /**
     * 装托
     */
    private Map<String, String> pallentNoMap;
}
