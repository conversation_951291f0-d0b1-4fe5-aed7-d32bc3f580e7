package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import lombok.Data;

/**
 * @Description: ept接口参数信息
 * @Author: zhaojianguo21
 * @Date: 2024/5/29 09:10
 **/
@Data
public class CrossBorderStockUpEptDuccVO {

    /**
     * 是否调用ept接口
     */
    private Boolean invokeEptSwitch;

    /**
     * 机构Id
     */
    private  Long orgId;

    /**
     * erp
     */
    private  String sellerErp;

    /**
     * 部门id
     */
    private  Integer collect4WhoId;

    /**
     *
     */
    private  Integer deliveryContryId;

    /**
     * ept的采购人pin
     */
    private  String purchasePin;

    /**
     * 审核ERP
     */
    private String auditErp;

    /**
     * 审核ERP姓名
     */
    private String auditErpName;

}
