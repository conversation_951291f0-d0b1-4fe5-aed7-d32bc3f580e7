package com.jdi.isc.order.center.domain.purchaseOrder.biz;


import com.jdi.isc.order.center.api.common.SystemInfoOrderApiReq;
import lombok.Data;

import java.util.List;

@Data
public class UpdatePurchaseOrdersStatusReqVO {

    /**
     * 采购单号
     */
    private List<String> purchaseOrderIds;

    /**
     * 采购单修改人
     */
    private String operateAccount;

    /**
     * 操作  com.jdi.isc.order.center.api.constants.PurchaseOrderStatusConstant
     */
    private String operate;

    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * 客户信息
     */
    private SystemInfoOrderApiReq clientReqExt;
    /**
     * 采购单发货对象
     */
    private PurchaseOrderWaybillVO purchaseOrderWaybill;

    /**
     * AP 发货订单
     */
    private String apNumber;
}
