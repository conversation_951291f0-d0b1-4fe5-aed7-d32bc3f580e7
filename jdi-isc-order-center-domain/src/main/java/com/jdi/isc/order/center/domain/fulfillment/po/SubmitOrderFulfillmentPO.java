package com.jdi.isc.order.center.domain.fulfillment.po;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

@Data
public class SubmitOrderFulfillmentPO {

    /**
     * 最短送达天数(履约时间)
     */
    private BigDecimal minDeliveryDays;

    /**
     * 最长送达天数(履约时间)
     */
    private BigDecimal maxDeliveryDays;

    /**
     * 时效规则版本(履约时间)
     */
    private String timeRuleVersion;

    /**
     * 混合
     */
    private Map<String, SubmitOrderFulfillmentPO> fulfillmentMap;
}
