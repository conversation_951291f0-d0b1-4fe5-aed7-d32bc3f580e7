package com.jdi.isc.order.center.domain.frontOrder;

import lombok.Data;

import java.util.List;

@Data
public class JdiInOutOrderInfoMsg {
     /**
     * 国际采购单号
     */
    private String intlPurchaseOrderNo;

    /**
     * 国际订单号
     */
    private String intlOrderNo;

    /**
     * 内贸单号
     */
    private String iopOrderNo;

    /**
     * wareHouseType=0-国内仓
     * |-订单类型：1-国际集运仓订单
     * wareHouseType=1-国外仓
     * |-订单类型： 301-本对本订单，302-跨境履约订单
     * {@link com.jdi.isc.order.center.domain.frontOrder.enums.OrderTypeEnum}
     */
    private Integer orderType;

    /**
     * 收货：RECEIVED(20, "收货完成");
     * 发货：COMPLETE(3, "已发货"),
     */
    private Integer orderStatus;

    /**
     * 商品信息
     */
    private List<JdiInOutSkuInfoMsg> skuInfo;
}