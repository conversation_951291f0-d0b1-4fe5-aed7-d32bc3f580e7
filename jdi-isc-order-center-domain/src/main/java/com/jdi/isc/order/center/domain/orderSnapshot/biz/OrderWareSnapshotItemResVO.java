package com.jdi.isc.order.center.domain.orderSnapshot.biz;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 订单商品快照各业务信息快照结果对象
 * @Author: zhaojianguo21
 * @Date: 2025/5/20 21:36
 **/
@Data
public class OrderWareSnapshotItemResVO implements Serializable {

    /**
     * 业务模块。使用枚举的code码。方便JSON中Map转换，使用String类型。
     * {@link com.jdi.isc.order.center.domain.enums.orderSnapshot.OrderWareSnapshotBizItemEnum}
     */
    private String bizCode;

    /**
     * 业务模块。与bizCode对应。
     * {@link com.jdi.isc.order.center.domain.enums.orderSnapshot.OrderWareSnapshotBizItemEnum}
     */
    private String bizName;

    /**
     * 当前业务快照是否成功
     */
    private Boolean success;
    /**
     * 业务快照失败原因
     */
    private String message;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 最后更新时间
     */
    private Long updateTime;

    /**
     * 创建时间
     */
    private String createTimeFormat;

    /**
     * 最后更新时间
     */
    private String updateTimeFormat;
}
