package com.jdi.isc.order.center.domain.declaration.biz;

import com.jdi.isc.order.center.common.biz.BasePageVO;
import lombok.Data;

import java.util.Set;

/**
 * @auther liudong21
 * time 2019/11/21 12:14
 */
@Data
public class CustomsOrderReqVo extends BasePageVO {



    /**
     * 业务单号
     */
    private String bizId;

    /**
     * 业务类型 1:跨境采购单，2:跨境备货订单
     */
    private Integer type;

    /**
     * 订单类型/采购单类型
     */
    private Integer orderType;

    /**
     * 后台状态
     */
    private Integer orderStatus;

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 下单时间
     */
    private Long createTimeStart;
    private Long createTimeEnd;


    /**
     * ids
     */
    private Set<String> bizIds;

    /**
     * IOP订单号
     */
    private String iopOrderId;

    /**
     * 入集货仓时间条件
     */
    private Long enterStorehouseTimeStart;
    private Long enterStorehouseTimeEnd;

    /**
     * 订单状态集合
     */
    private Set<Integer> orderStatusSet;

}
