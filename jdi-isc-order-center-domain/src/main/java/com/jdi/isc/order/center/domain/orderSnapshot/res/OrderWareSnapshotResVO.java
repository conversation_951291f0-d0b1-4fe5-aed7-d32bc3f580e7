package com.jdi.isc.order.center.domain.orderSnapshot.res;

import com.jdi.isc.order.center.domain.orderSnapshot.biz.OrderWareSnapshotBaseInfoVO;
import com.jdi.isc.order.center.domain.orderSnapshot.biz.OrderWareSnapshotItemResVO;
import com.jdi.isc.order.center.domain.snapshot.brand.SnapshotBrandVO;
import com.jdi.isc.order.center.domain.snapshot.category.SnapshotCategoryVO;
import com.jdi.isc.order.center.domain.snapshot.common.SnapshotSaleUnitVO;
import com.jdi.isc.order.center.domain.snapshot.extendAttribute.SnapshotExtendAttributeVO;
import com.jdi.isc.order.center.domain.snapshot.material.SnapshotMaterialVO;
import com.jdi.isc.order.center.domain.snapshot.mku.SnapshotMkuDescVO;
import com.jdi.isc.order.center.domain.snapshot.mku.SnapshotMkuVO;
import com.jdi.isc.order.center.domain.snapshot.order.OrderSnapshotBaseInfoVO;
import com.jdi.isc.order.center.domain.snapshot.price.SnapshotBrazilTaxFeeDetailVO;
import com.jdi.isc.order.center.domain.snapshot.price.SnapshotPriceVO;
import com.jdi.isc.order.center.domain.snapshot.promiseTime.SnapshotPromiseTimeVO;
import com.jdi.isc.order.center.domain.snapshot.saleAttribute.SnapshotSaleAttributeVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description: 订单商品快照对象
 * @Author: zhaojianguo21
 * @Date: 2025/5/20 21:36
 **/
@Data
public class OrderWareSnapshotResVO implements Serializable {

    /**
     * 各业务模块快照结果明细
     */
    private Map<String/*业务code码，用整数表示*/,OrderWareSnapshotItemResVO> bizItemsResMap;

    /**
     * 商品所属订单基本信息
     */
    private OrderSnapshotBaseInfoVO orderSnapshotBaseInfo;

    /**
     * 订单商品快照基本信息
     */
    private OrderWareSnapshotBaseInfoVO wareSnapshotBaseInfo;

    /**
     * 类目
     */
    private List<SnapshotCategoryVO> category;

    /**
     * 品牌
     */
    private SnapshotBrandVO brand;

    /**
     * 商品基本信息
     */
    private SnapshotMkuVO mkuBaseInfo;

    /**
     * 销售属性 集合
     */
    private List<SnapshotSaleAttributeVO> saleAttribute;

    /**
     * 扩展属性
     */
    private List<SnapshotExtendAttributeVO> extendAttribute;

    /**
     * 价格
     */
    private SnapshotPriceVO price;

    /**
     * 巴西税费信息
     */
    private SnapshotBrazilTaxFeeDetailVO brazilTaxFeeDetail;

    /**
     * 物料
     */
    private SnapshotMaterialVO material;

    /**
     * 履约时效
     */
    private SnapshotPromiseTimeVO promiseTime;

    /**
     * 商品详描
     */
    private SnapshotMkuDescVO mkuDesc;

}
