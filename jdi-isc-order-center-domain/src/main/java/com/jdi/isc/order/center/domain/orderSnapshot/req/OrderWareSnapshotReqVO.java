package com.jdi.isc.order.center.domain.orderSnapshot.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2025/5/20 21:36
 **/
@Data
public class OrderWareSnapshotReqVO implements Serializable {

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空")
    private Long orderId;

    /**
     * mkuId
     */
    @NotNull(message = "mkuId不能为空")
    private Long mkuId;

}
