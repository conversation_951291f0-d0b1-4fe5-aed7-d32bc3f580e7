package com.jdi.isc.order.center.domain.declaration.po;


import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 报关订单信息关系表
 * <AUTHOR>
 * @date 20241109
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("jdi_isc_customs_order")
@AllArgsConstructor
@NoArgsConstructor
public class CustomsOrderPo extends BasicPO {


    /**
     *  关联报关单信息id表
     *  */
    private Long customsMainId;


    /**
     * 业务id
     * */
    private String bizId;

    /**
     * 单据类型
     * 1:跨境订单
     * 2备货采购单
     * */
    private Integer type;


}
