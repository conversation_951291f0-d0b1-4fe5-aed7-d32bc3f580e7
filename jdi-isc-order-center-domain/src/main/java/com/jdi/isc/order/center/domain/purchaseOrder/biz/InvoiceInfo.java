package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import lombok.Data;

/**
 * 发票信息
 */
@Data
public class InvoiceInfo {
    /**
     * 开票方式(2为集中开票，3 不开发票, 4 订单完成后开票)
     */
    private Integer invoicePutType;
    /**
     * 发票类型（1普票，2增值税专用发票；3 电子票） 当发票类型为2时，开票方式只支持2集中开票
     */
    private Integer invoiceType;
    /**
     * 发票抬头类型：4：个人，5：单位
     */
    private Integer invoiceSelectedTitle;
    /**
     * 发票抬头
     */
    private String invoiceCompanyName;
    /**
     * 1:明细，100：大类 备注:若增值税专用发票则只能选1 明细
     */
    private Integer invoiceContentType;
    /**
     * 收票人姓名
     */
    private String invoiceName;
    /**
     * 收票人手机号
     */
    private String invoicePhone;
    /**
     * 专票资质纳税人识别号 当invoiceType =2时必填。 如果真的没有，
     * 接口方会自动查询资质信息，填充，前提是，必须要在京东侧添加增票资质。
     */
    private String invoiceRegCode;
    /**
     * 收票人地址明细
     */
    private String invoiceAddress;
    /**
     * 专票资质收票人明细
     */
    private String invoiceRegAddress;
    /**
     * 专票资质公司名称 当invoiceType =2时必填。
     * 如果真的没有，接口方会自动查询资质信息，填充，前提是，必须要在京东侧添加增票资质。
     */
    private String invoiceRegCompanyName;
    /**
     * 收票人地址省ID
     */
    private Integer invoiceProvinceId;
    /**
     *收票人地址城市ID
     */
    private Long invoiceCityId;
    /**
     * 收票人地址区县ID
     */
    private Integer invoiceCountyId;
    /**
     * 收票人地址乡镇ID，没有传0
     */
    private Integer invoiceTownId;
    /**
     * 专票资质注册银行 当invoiceType =2时必填。
     * 如果真的没有，接口方会自动查询资质信息，填充，前提是，必须要在京东侧添加增票资质。
     */
    private String invoiceRegBank;
    /**
     * 专票资质银行账号 当invoiceType =2时必填。
     * 如果真的没有，接口方会自动查询资质信息，填充，前提是，必须要在京东侧添加增票资质。
     */
    private String invoiceRegBankAccount;
    /**
     * 专票资质注册电话 当invoiceType =2时必填。
     * 如果真的没有，接口方会自动查询资质信息，填充，前提是，必须要在京东侧添加增票资质。
     */
    private String invoiceRegPhone;
}
