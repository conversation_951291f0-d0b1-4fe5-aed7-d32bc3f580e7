package com.jdi.isc.order.center.domain.fulfillment.vo;

import lombok.Data;

import java.util.List;

@Data
public class FulfillmentInStockMsgVO {

    /**
     * 来源
     */
    private String dataSource;

    /**
     * 变更事件
     */
    private String event;

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 采购单明细
     */
    private List<FulfillmentInStockMsgDetailVO> detailList;

    /**
     * 三方单号
     */
    private String thirdCode;

    /**
     * 收货仓库编码
     */
    private String warehouseCode;

    /**
     *  唯一标识
     */
    private String uniqueFlag;


    /**
     * 入库单号（可能的值：采购单号、非订单场景入库单号,预报备货单号）
     */
    private String customerBillCode;


}
