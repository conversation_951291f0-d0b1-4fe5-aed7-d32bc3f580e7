package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import lombok.Data;

/**
 * @Description: 一段运单 VO实体类
 * @Author: zhaokun51
 * @Date: 2024/06/13 15:17
 **/

@Data
public class PurchaseOrderWaybillVO {

    /** 运单号 */
    private String waybillNum;

    /** 运单状态 1:已发运 2:已入仓 */
    private Integer status;

    /** 供应商简码 */
    private String supplierCode;

    /**
     * 运输方式
     */
    private Integer transportMethod;
    /**
     * 运输单号
     */
    private String thirdTransportNum;
    /**
     * 承运商
     */
    private String carrier;
    /**
     * 预计送达时间
     */
    private Long expectedDeliveryDate;
    /**
     * 发货资料
     */
    private String deliveryMaterial;
    /**
     * 配送人
     */
    private String deliveryPerson;
    /**
     * 车牌号
     */
    private String licensePlate;
    /**
     * 配送人电话
     */
    private String deliveryMobileIndex;
    /**
     * 配送人电话
     */
    private String deliveryMobileEncrypt;
}
