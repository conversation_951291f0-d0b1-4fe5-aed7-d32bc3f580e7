package com.jdi.isc.order.center.domain.mapstruct.supplier;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VcHomePageReminderVO implements Serializable {



    /**
     * 国家或地区的代码。发票必传 reminder
     */
    private String countryCode;

    /**
     * 供应商代码 发票必传 reminder
     */
    private String supplierCode;


    /**
     * 结算单查询条件 发票必传 reminder
     */
    private String partnerCode;

    /**
     * 国家或地区的名称。 发票必传 reminder
     */
    private String country;
}
