package com.jdi.isc.order.center.domain.declaration.biz;

import lombok.Data;

import java.util.List;

@Data
public class CustomsDetailsVo {

    private Long id;
    private String   customsId;//关联报关单Id
    private String   customsNumber;//报关单项号
    private String   customsSkuId;//商品编号
    private String   customsSkuInfo;//报关单商品信息(名称、规格、型号)
    private String   customsSkuQuantity;//sku 数量
    private String   jdOrderId;//订单ID
    private String   jdSkuId;//京东SkuId

    private List<CustomsOrderVo> customsOrderVoList;


}
