package com.jdi.isc.order.center.domain.snapshot.extendAttribute;

import com.jdi.isc.order.center.domain.snapshot.common.SnapshotBasicVO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Description: 扩展属性快照对象
 * @Author: zhaojianguo21
 * @Date: 2025/5/21 14:51
 **/
@Data
public class SnapshotExtendAttributeVO extends SnapshotBasicVO {

    /** 扩展属性多语言名称 */
    private Map<String/*语种*/,String/*名称*/> nameLangMap;

    /**
     * 扩展属性值列表
     */
    private List<SnapshotExtendAttributeValueVO> attributeValues;
}
