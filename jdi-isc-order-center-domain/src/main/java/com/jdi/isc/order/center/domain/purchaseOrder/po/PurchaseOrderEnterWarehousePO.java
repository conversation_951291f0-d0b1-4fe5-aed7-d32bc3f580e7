package com.jdi.isc.order.center.domain.purchaseOrder.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @Description 采购单入仓信息
 * @Date 2024/5/27 9:53 下午
 */
@TableName(value = "jdi_isc_purchase_order_enter_warehouse_sharding")
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderEnterWarehousePO extends BasicPO {

    /**
     * 采购单
     */
    private String purchaseOrderId;

    /**
     * 入仓时间
     */
    private long enterWarehouseDate;

    /**
     * 入仓资料
     */
    private String enterMaterial;

    /**
     * 入仓视频资料
     */
    private String enterMaterialVideo;

    /**
     * 第三方来源标识
     */
    private String dataSource;

    /**
     *  唯一标识
     */
    private String uniqueFlag;

    /**
     * 入库单号
     */
    private String thirdCode;

}
