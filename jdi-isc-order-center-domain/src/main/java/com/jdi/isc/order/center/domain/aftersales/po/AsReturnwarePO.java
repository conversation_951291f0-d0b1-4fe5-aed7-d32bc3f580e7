package com.jdi.isc.order.center.domain.aftersales.po;

import lombok.Data;

/**
 * 返件信息实体
 * <AUTHOR>
 * @date 2024/8/16
 */
@Data
public class AsReturnwarePO {
    /** 返件方式：自营配送(10),第三方配送(20)*/
    private Integer returnwareType;
    /** 返件省*/
    private Integer returnwareProvince;
    /** 返件市*/
    private Integer returnwareCity;
    /** 返件县*/
    private Integer returnwareCounty;
    /** 返件乡镇*/
    private Integer returnwareVillage;
    /** 返件街道地址，最多500字符*/
    private String returnwareAddress;
}
