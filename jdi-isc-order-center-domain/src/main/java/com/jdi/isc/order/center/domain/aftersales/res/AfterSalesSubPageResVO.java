package com.jdi.isc.order.center.domain.aftersales.res;

import com.jdi.isc.library.common.domain.common.biz.BasicVO;
import com.jdi.isc.order.center.api.common.OperateDTO;
import com.jdi.isc.order.center.api.constants.PaymentChannelConstant;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 售后子流程分页查询结果对象
 * @Author: zhaojianguo21
 * @Date: 2025/6/17
 **/

@Data
public class AfterSalesSubPageResVO extends BasicVO implements Serializable {

    /**
     * 售后子单单号
     */
    private String bizId;

    /**
     * 单据类型100.承担费用200.取件维修300.换货补货400.退款
     * {@link com.jdi.isc.order.center.domain.enums.aftersales.AfterSalesSubTypeEnum }
     */
    private Integer bizType;

    /**
     * 录入退款金额,4位小数
     */
    private BigDecimal recordRefundPrice;

    /**
     * 允许的最大的退款金额,4位小数
     */
    private BigDecimal allowMaxRefundPrice;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 售后单号
     */
    private String afterSalesOrderId;

    /**
     * 京东国际订单号
     */
    private Long orderId;

    /**
     * AfterSalesSubStatusEnum
     * {@link com.jdi.isc.order.center.domain.enums.aftersales.AfterSalesSubStatusEnum}
     */
    private Integer status;

    /**
     * 原因
     */
    private String reason;

    /**
     * 关注人
     */
    private String follower;

    /**
     * 完成时间
     */
    private Long completeTime;

    /**
     * 取消时间
     */
    private Long cancelTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * iop订单号
     */
    private Long iopOrderId;

    /**
     * 操作列表
     */
    private List<OperateDTO> operateList;

    /**
     * 客户code
     */
    private String clientCode;

    /**
     * 支付渠道（支付方式） {@link PaymentChannelConstant}
     */
    private String paymentChannelCode;

    /**
     * 支付渠道（支付方式） {@link PaymentChannelConstant}
     */
    private String paymentChannelName;

    /**
     * 退款发起时间
     */
    private Long refundCreateTime;

    /**
     * 退款完成时间
     */
    private Long refundFinishTime;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 币种: VND越南, THB泰国, CNY人民币, USD美元
     */
    private String foreignCurrency; // 币种: VND越南, THB泰国, CNY人民币, USD美元
    /**
     * 退款金额, 4位小数
     */
    private BigDecimal foreignAmount; // 退款金额, 4位小数
}