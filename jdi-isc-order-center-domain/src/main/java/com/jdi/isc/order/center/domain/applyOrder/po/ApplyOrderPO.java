package com.jdi.isc.order.center.domain.applyOrder.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 申请单表
 */
@TableName(value = "jdi_isc_apply_order_sharding")
@Data
public class ApplyOrderPO  {

    /** '申请单来源 1:邮件 2:RPA' */
    private Integer source;
    /** '申请单创建人(邮件来源对应发件人)' */
    private String applicant;
    /** '下单pin' */
    private String pin;
    /** '合同号' */
    private String contractNum;
    /** '客户id' */
    private String clientCode;
    /** '申请单附件url' */
    private String attachmentUrl;
    /** '三方订单号' */
    private String thirdOrderId;
    /** '三方扩展信息，下游透传' */
    private String thirdExtInfo;
    /** 'sap单号' */
    private String sapOrderId;
    /** '国际订单号' */
    private Long orderId;
    /** '公司抬头' */
    private String header;
    /** '收货国家' */
    private String consigneeCountry;
    /** '收货地址省ID' */
    private Long consigneeProvinceId;
    /** '收货地址城市ID' */
    private Long consigneeCityId;
    /** '收货地址区县ID' */
    private Long consigneeCountyId;
    /** '收货地址乡镇ID 四级地址编码：收货人乡镇地址编码(如果没有四级地址则传0)' */
    private Long consigneeTownId;
    /** '币种:VND越南,THB泰国,CNY人民币,USD美元' */
    private String currency;
    /** '订单总价' */
    private String orderTotalPrice;
    /** '一级审批人' */
    private String firstAuditor;
    /** '二级审批人' */
    private String secondAuditor;
    /** '当前审核驳回等级 1:一级 2:二级' */
    private Integer level;
    /** '收货人姓名' */
    private String consignee;
    /** '收货详细地址' */
    private String consigneeAddress;
    /** '收货人电话' */
    private String consigneePhone;
    /** '收货人手机号' */
    private String consigneeMobile;
    /** '收货人邮箱' */
    private String consigneeEmail;
    /** '收货人姓名-用于检索' */
    private String consigneeIndex;
    /** '收货详细地址-用于检索' */
    private String consigneeAddressIndex;
    /** '收货人电话-用于检索' */
    private String consigneePhoneIndex;
    /** '收货人手机号-用于检索' */
    private String consigneeMobileIndex;
    /** '收货人邮箱-用于检索' */
    private String consigneeEmailIndex;
    /** 自增ID*/
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 备注 */
    private String remark;
    /** 创建者 */
    private String creator;
    /** 修改人 */
    private String updater;
    /** 创建时间 */
    private Long createTime;
    /**  最后修改时间 */
    private Long updateTime;
    /** 逻辑删除 0=删除,1有效 */
    private Integer yn;

    /** 申请单类型,【1.本土；2跨境】 */
    private Integer orderType;

}