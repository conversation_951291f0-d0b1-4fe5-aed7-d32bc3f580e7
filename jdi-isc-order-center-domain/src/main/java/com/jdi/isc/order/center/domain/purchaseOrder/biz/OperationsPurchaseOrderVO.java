package com.jdi.isc.order.center.domain.purchaseOrder.biz;


import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Set;

/**
 * 采购单运维对象
 */
@Data
public class OperationsPurchaseOrderVO {

    /**
     * 采购单号集合
     */
    @NotEmpty
    private Set<String> purchaseOrderIds;


    /**
     * 采购单的当前状态
     */
    private Integer purchaseOrderStatus;

    /**
     * 表示采购单的有效状态，0表示无效，1表示有效
     */
    private Integer validStatus;
}
