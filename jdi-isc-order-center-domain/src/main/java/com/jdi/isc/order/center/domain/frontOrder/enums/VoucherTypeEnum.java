package com.jdi.isc.order.center.domain.frontOrder.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/12/5
 * @Description
 */
@Getter
public enum VoucherTypeEnum {
    /**
     * 发货凭证类型，代码值为1，描述信息为“发货凭证”。
     */
    SHIPMENT_VOUCHER(1, "发货凭证"),

    /**
     * 收货凭证类型，代码值为2，描述信息为“收货凭证”。
     */
    RECEIVE_VOUCHER(2, "收货凭证"),
    ;

    /**
     * 凭证类型的代码值
     */
    private Integer code;

    /**
     * 凭证类型的描述信息
     */
    private String desc;

    VoucherTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
