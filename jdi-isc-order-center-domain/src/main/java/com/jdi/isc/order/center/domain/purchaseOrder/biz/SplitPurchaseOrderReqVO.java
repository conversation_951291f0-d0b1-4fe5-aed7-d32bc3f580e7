package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.order.biz.IopOrderSplitContext;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/5/23 11:06 上午
 */
@Data
public class SplitPurchaseOrderReqVO {

    /**
     * 订单类型 com.jdi.isc.order.center.api.constants.OrderTypeConstant
     */
    @NotNull(message = "orderType is not null")
    private Integer orderType;
}
