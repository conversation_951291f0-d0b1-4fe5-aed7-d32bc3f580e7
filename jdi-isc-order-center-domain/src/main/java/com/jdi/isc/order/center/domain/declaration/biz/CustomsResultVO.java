package com.jdi.isc.order.center.domain.declaration.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


/**
 *  报关结果回填对象
 * <AUTHOR>
 * @date 2024-10-21 14:32 2024-10-21 14:44
 */
@Data
public class CustomsResultVO extends BasicVO {


    /**
     * 保费
     * */
    @NotNull(message = "保费不能为空")
    private BigDecimal insPrm;

    /**
     * 杂费
     * */
    @NotNull(message = "杂费不能为空")
    private BigDecimal miscExp;

    /**
     *  运费
     *  */
    @NotNull(message = "运费不能为空")
    private BigDecimal freightFee;

    /**
     * 报关单号
     * */
    @NotNull(message = "报关单号不能为空")
    private String customsId;

    /**
     *  报关服务商名称
     *  */
    @NotNull(message = "报关服务商不能为空")
    private String customsAgentName;

    /**
     * 报关单金额/销售金额
     * */
    @NotNull(message = "报关单金额不能为空")
    private BigDecimal customsAmount;

    /**
     * 申报日期
     * */
    @NotNull(message = "申报日期不能为空")
    private Long declareTime;

    /**
     * 放行日期
     * */
    @NotNull(message = "放行日期不能为空")
    private Long releaseTime;


    /** 备注*/
    @Length(max = 250, message = "备注超长")
    protected String remark;

}
