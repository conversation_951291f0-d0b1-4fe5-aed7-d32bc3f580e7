package com.jdi.isc.order.center.domain.declaration.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("jdi_isc_customs_details")
@AllArgsConstructor
@NoArgsConstructor
public class CustomsDetailsPo extends BasePO {

    private Long   customsId;//关联报关单Id
    private Long   customsNumber;//报关单项号
    private String   customsSkuId;//商品编号
    private String   customsSkuInfo;//报关单商品信息(名称、规格、型号)
    private Integer   customsSkuQuantity;//sku 数量
    private Long   jdOrderId;//订单ID
    private Long   jdSkuId;//京东SkuId

}
