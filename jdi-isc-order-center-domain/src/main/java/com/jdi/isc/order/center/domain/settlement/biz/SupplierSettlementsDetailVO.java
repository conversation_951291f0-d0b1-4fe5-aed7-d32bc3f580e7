package com.jdi.isc.order.center.domain.settlement.biz;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SupplierSettlementsDetailVO extends BasicVO {

    // 采购单号
    private String purchaseOrderId;

    // sku编码
    private Long skuId;

    // 商品数量
    private Integer skuNum;


    // 结算单号（内部结算单）
    private String requestId;

    // 供应商简码
    private String supplierId;

    // 结算金额SupplierSettlementsDetailPo
    private BigDecimal amount;

    // 币种
    private String accountCurrency;


}
