package com.jdi.isc.order.center.domain.purchaseOrder.po;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 自动关闭采购单任务表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "jdi_isc_close_purchase_order_task_sharding")
public class ClosePurchaseOrderTaskPO extends BasicPO {

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * 所属国
     */
    private String countryCode;

    /**
     * 类型1本土订单2跨境订单
     */
    private Integer purchaseOrderType;

    /**
     * 采购模式0:非备货，1:备货
     */
    private Integer purchaseModel;

    /**
     * 采购单已入库状态0:未入库,1:已入库
     */
    private Integer purchaseOrderInputWarehouseStatus;

    /**
     * 采购单已入库时间
     */
    private Long purchaseOrderInputWarehouseTime;

    /**
     * 重试次数
     */
    private Integer retryTimes;

    /**
     * 消息状态 0:待处理 1:成功 -1:异常
     */
    private Integer taskStatus;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 订单版本号
     */
    private Integer version;

}