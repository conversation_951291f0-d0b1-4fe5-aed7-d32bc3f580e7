package com.jdi.isc.order.center.domain.settlement.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value ="jdi_isc_supplier_settlements_sharding")
public class SupplierSettlementsPo extends BasicPO {

    // 结算业务
    private Integer settlementBizId;

    // 结算分组
    private Integer settlementGroupId;

    // 唯一值
    private String uuid;

    // 结算单号（内部结算单）
    private String requestId;

    // 操作状态
    private Integer opStatus;

    // 操作时间
    private Date operatorTime;

    // 付款完成时间
    private Date payTime;

    // 供应商简码
    private String supplierId;

    // 业务结算单号（如，外部结算单号）
    private String outSettleCode;

    // 结算金额
    private BigDecimal amount;

    // 扩展信息
    private String extraInfo;

    // 供应商确认状态0待确认 1部分确认 2已确认
    private Integer supplierConfirmStatus;

    // 供应商确认时间
    private Long supplierConfirmTime;

    // 国家码
    private String countryCode;


    /**
     * 主OU编码
     */
    private String ou;

    /**
     * 主OU名称
     */
    private String ouName;


}
