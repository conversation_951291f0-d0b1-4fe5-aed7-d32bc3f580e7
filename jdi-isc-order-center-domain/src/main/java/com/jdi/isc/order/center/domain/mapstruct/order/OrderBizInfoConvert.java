package com.jdi.isc.order.center.domain.mapstruct.order;

import com.jdi.isc.order.center.api.orderRead.biz.manage.OrderReadBizInfoApiDTO;
import com.jdi.isc.order.center.domain.ept.req.EptSubmitOrderReqVO;
import com.jdi.isc.order.center.domain.ept.resp.EptSubmitOrderRespVO;
import com.jdi.isc.order.center.domain.order.biz.OrderBizInfoReqVO;
import com.jdi.isc.order.center.domain.order.biz.OrderBizInfoVO;
import com.jdi.isc.order.center.domain.order.biz.OrderTradeRouteVO;
import com.jdi.isc.order.center.domain.order.biz.UpdateOrderStatusReqVO;
import com.jdi.isc.order.center.domain.order.po.OrderBizInfoPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OrderBizInfoConvert {

    OrderBizInfoConvert INSTANCE = Mappers.getMapper(OrderBizInfoConvert.class);

    @Mappings({
            @Mapping(target = "creator", source = "eptSubmitOrderReqVO.operateErp"),
            @Mapping(target = "updater", source = "eptSubmitOrderReqVO.operateErp"),
            @Mapping(target = "orderId", source = "eptSubmitOrderReqVO.orderId")
    })
    OrderBizInfoPO etpReq2po(EptSubmitOrderReqVO eptSubmitOrderReqVO, EptSubmitOrderRespVO eptSubmitOrderRespVO);


    @Mappings({
            @Mapping(target = "updater", source = "updateOrderStatusReqVO.operateErp"),
            @Mapping(target = "orderId", source = "updateOrderStatusReqVO.orderId")
    })
    OrderBizInfoPO updateOrderStatusReq2po(UpdateOrderStatusReqVO updateOrderStatusReqVO, OrderBizInfoPO orderBizInfoPO);


    EptSubmitOrderReqVO orderTradeRouteVO2Ept(OrderTradeRouteVO orderTradeRouteVO);

    OrderBizInfoVO orderBizInfoPo2Vo(OrderBizInfoPO orderBizInfoPO);


    OrderBizInfoPO po2po(OrderBizInfoPO orderBizInfoPO);

    @Mappings({
            @Mapping(target = "createTime", expression = "java(orderBizInfoPO.getCreateTime().getTime())"),
            @Mapping(target = "updateTime", expression = "java(orderBizInfoPO.getUpdateTime().getTime())")
    })
    OrderReadBizInfoApiDTO po2ApiDTO(OrderBizInfoPO orderBizInfoPO);
}

