package com.jdi.isc.order.center.domain.frontOrder;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/10/17
 * @Description 集运中心签收凭证消息体
 */
@Data
public class JdiReceiptVoucherMsg {
    /**
     * 操作人ID
     */
    private String operateId;

    /**
     * 操作人名称
     */
    private String operateName;

    /**
     * 上传时间
     */
    private Long operateTime;

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 审核图片地址
     */
    private String imgUrl;

    /**
     * 凭证类型
     * 1-发货、2-收货
     */
    private Integer voucherType;

    /**
     * 国际采购单号(收货凭证使用)
     */
    private String intlPurchaseOrderNo;

    /**
     * 妥投时间
     */
    private Long deliveryCustomerDate;
}
