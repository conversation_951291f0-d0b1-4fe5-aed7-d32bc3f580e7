package com.jdi.isc.order.center.domain.forecast.biz;

import com.jdi.isc.order.center.domain.forecast.po.ForecastOrderPO;
import com.jdi.isc.order.center.domain.order.po.OrderPO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderPO;
import lombok.Data;

import java.util.List;

@Data
public class ForecastOrderPreSplitConfirmVO {

    private ForecastOrderPO updateParentOrderPO;

//    private List<OrderPO> successOrderPOList;
//
//    private List<OrderPO> failOrderPOList;

    private List<ForecastOrderPO> forecastOrderPOList;
}
