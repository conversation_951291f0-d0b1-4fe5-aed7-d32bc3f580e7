package com.jdi.isc.order.center.domain.aftersales.po;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 售后单变更消息
 * @date 2025/6/18 18:35
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AfterSalesMsgPO extends AfterSalesPO{

    /**
     * 前置状态
     */
    private Integer beforeStatus;

    /**
     * 售后子单状态
     */
//    private String afterSalesSubStatus;
}
