package com.jdi.isc.order.center.domain.declaration.biz;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CustomsDeclarationBaseVo extends BasicPO {



    /**
     * 订单Id
     */
    private String saleOrdId;

    /**
     * 日期
     */
    private Date dt;

    /**
     * 主站的父订单号，指最顶层的父订单编号
     */
    private String parentSaleOrdId;

    /**
     * 优惠后金额
     */
    @TableField("after_prefr_amount_1")
    private String afterPrefrAmount1;

    /**
     * 订单运费
     */
    private String skuFreightAmount;

    /**
     * 订单下单时间
     */
    private String saleOrdTm;

    /**
     * 支付时间
     */
    private String ordDealTm;

    /**
     * 大仓出库时间
     */
    private String outWhTm;

    /**
     * 订单完成时间
     */
    private String ordCompleteTm;

    /**
     * 订单取消时间
     */
    private String ordCancelTm;

    /**
     * 主站订单状态
     */
    private String ordStatusName;

    /**
     * 主站下单支付类型
     */
    private String payCateCd;

    /**
     * 客户的唯一标识ID
     */
    private String userLogAcct;

    /**
     * 客户的编码
     */
    private String corpId;

    /**
     * 与订单相关的合同编号
     */
    private String contractNum;

    /**
     * 跨境订单状态
     */
    private String intlOrderStatus;

    /**
     * 订单对应的运单信息
     */
    private String carryBillId;

    /**
     * 订单下单金额
     */
    private String openOrderAmount;

    /**
     * 出口成本默认0
     */
    private String exportCost;

    /**
     * 是否产生逆向取消费用，0表示否，1表示是默认0
     */
    private String isCancelFee;

    /**
     * 买方信息相同
     */
    private String corpName;

    /**
     * 主站skuId
     */
    private String itemSkuId;

    /**
     * 商品的标题或名称
     */
    private String skuName;

    /**
     * 商品所属的三级类目ID
     */
    private String itemThirdCateCd;

    /**
     * 商品所属的二级类目ID
     */
    private String itemSecondCateCd;

    /**
     * 商品所属的一级类目ID
     */
    private String itemFirstCateCd;

    /**
     * 商品数量
     */
    private String saleQtty;

    /**
     * 商品在主站的红字价格，通常指特殊优惠价格
     */
    private String jdRedPrice;

    /**
     * 跨境销售单价
     */
    private String openSkuPrice;

    /**
     * 涉及到备案计算
     */
    private String intlSkuPrice;

    /**
     * 区分免税和退税
     */
    private String productIdentificationCode;

    /**
     * 商品的产品ID，与skuId可能相同，用于区分不同的产品实例
     */
    private String itemId;

    /**
     * 主SKU ID，用于关联商品的主要SKU，特别是在商品有多个变体（如颜色、尺寸）时
     */
    private String mainSkuId;

    /**
     * 商品的主图URL，即商品的主要展示图片
     */
    private String icPMain;

    /**
     * 商品的产地信息
     */
    private String originAddr;

    /**
     * 商品品牌的唯一标识符
     */
    private String brandCode;

    /**
     * 商品品牌的中文名称
     */
    private String brandNameCn;

    /**
     * 商品品牌的英文名称
     */
    private String brandNameEn;

    /**
     * 商品的特殊属性描述
     */
    private String specAttr;

    /**
     * 商品的长度
     */
    private String length;

    /**
     * 商品的宽度
     */
    private String width;

    /**
     * 商品的高度
     */
    private String height;

    /**
     * 商品的重量
     */
    private String weight;

    /**
     * 主站301商品状态，用于标识商品的上下架状态等
     */
    private String skuStatusCd;

    /**
     * 主站301商品YN标识，通常用于标识是否有效或启用
     */
    private String skuValidFlag;

    /**
     * 商品的型号
     */
    private String pattern;

    /**
     * 商品的销售单位（如件、盒）
     */
    private String mainunitCode;

    /**
     * 主供应商编码
     */
    private String majorSuppCd;

    /**
     * 主供应商名称
     */
    private String majorSuppName;

    /**
     * 卖方合同信息
     */
    private String sellerContractNum;









}
