package com.jdi.isc.order.center.domain.declaration.biz;


import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 报关订单信息关系表
 * <AUTHOR>
 * @date 20241109
 */
@Data
public class CustomsOrderVo extends BasicVO {


    /**  关联报关单信息id表 */
    private Long customsMainId;


    /**  业务id */
    private String bizId;

    /**
     * 单据类型
     * 1:跨境订单
     * 2备货采购单
     * */
    private Integer type;

    /**
     * 业务父单号
     */
    private String parentBizId;

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 订单类型【1.本土；2跨境】
     */
    private Integer orderType;

    /**
     * 币种
     */
    private String currency;

    /**
     * iop订单号
     */
    private String iopOrderId;

    /**
     * iopPin
     */
    private String iopPin;

    /**
     * 订单总价
     */
    private BigDecimal orderTotalPrice;

    /**
     * 下单时间
     */
    private String orderCreateTimeStr;

    /**
     * mku总数量(所有mkuNum的加合)
     */
    private Integer mkuNum;

    /**
     * 订单后台状态(0:已提交、1:待运营修改信息确认、2:用户已确认、3:用户已审批、4:运营已确认、100:已完成、99:已取消)
     */
    private Integer orderStatus;

    /**
     * 入集货仓时间
     */
    private Long enterStorehouseTime;

    /**
     * 合同号
     */
    private String contractNum;

}
