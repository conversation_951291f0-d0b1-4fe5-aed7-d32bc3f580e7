package com.jdi.isc.order.center.domain.mapstruct.tax;

import com.jdi.isc.aggregate.read.wiop.api.orderTax.resp.OrderTaxItemMkuReadResp;
import com.jdi.isc.order.center.domain.tax.OrderWareTaxInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/12/28 18:02
 */
@Mapper
public interface OrderTaxConvert {

    OrderTaxConvert INSTANCE = Mappers.getMapper(OrderTaxConvert.class);

    OrderWareTaxInfoVO orderWareTaxInfoReadReq2VO(OrderTaxItemMkuReadResp orderTaxItemMkuReadResp);

}
