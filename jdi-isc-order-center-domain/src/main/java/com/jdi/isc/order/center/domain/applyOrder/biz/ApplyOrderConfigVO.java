package com.jdi.isc.order.center.domain.applyOrder.biz;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * 申请单表
 */
@Data
public class ApplyOrderConfigVO {

    /** 申请人(发件人)*/
    private String applicant;
    /** 下单pin */
    private String pin;
    /** 客户id */
    private String clientCode;
    /** 合同号 */
    private String contractNum;
    /** 一级审批erp */
    private Set<String> firstAuditErp;
    /** 二级审批erp */
    private Set<String> secondAuditErp;
    /** 附件解析方式 */
    private Integer parseType;
}
