package com.jdi.isc.order.center.domain.declaration.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import com.jdi.isc.order.center.domain.validate.group.ValidateCustomsGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 报关附件VO 对象
 * <AUTHOR>
 * @date 2024-10-21 14:32 2024-10-21 16:32
 */

@Data
public class CustomsFileVO extends BasicVO {


    /**
     * 关联报关单信息id表
     */
    @NotNull(message = "报关单id不能为空",groups = {ValidateCustomsGroup.createFile.class,ValidateCustomsGroup.delFile.class,ValidateCustomsGroup.litFile.class})
    private Long customsMainId;

    /**
     * 文件名称
     */
    @NotNull(message = "文件名称",groups = {ValidateCustomsGroup.createFile.class})
    private String fileName;

    /**
     * 文件类型
     * 依据枚举类
     * DeclarationFileTypeEnum
     * com.jdi.isc.product.center.domain.enums
     * */
    @NotNull(message = "文件类型",groups = {ValidateCustomsGroup.createFile.class})
    private Integer fileType;

    /**
     * 文件链接
     */
    @NotNull(message = "文件链接",groups = {ValidateCustomsGroup.createFile.class})
    private String fileUrl;

    /**
     * 文件名称类型【区分不同的业务文件】
     */
    private String fileNameType;
}
