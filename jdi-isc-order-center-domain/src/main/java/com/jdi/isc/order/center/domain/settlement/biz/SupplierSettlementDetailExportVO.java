package com.jdi.isc.order.center.domain.settlement.biz;

import cn.hutool.db.DaoTemplate;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/3 17:57
 */
@Data
public class SupplierSettlementDetailExportVO {

    /**
     * 结算单号
     */
    @ExcelProperty("结算单号")
    private String requestId;

    /**
     * 结算单金额
     */
    @ExcelProperty("结算单金额")
    private BigDecimal amount;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    @ExcelProperty("结算币种")
    private String currency;

    /**
     * 结算单状态
     */
    @ExcelProperty("结算单状态")
    private String supplierConfirmStatusStr;

    /**
     * 平台付款状态
     */
    @ExcelProperty("平台付款状态")
    private String payStatusStr;

    /**
     * 付款时间
     */
    @ExcelProperty("付款时间")
    private Date payTime;

    /**
     * 结算机构名称
     */
    @ExcelProperty("结算机构名称")
    private String ouName;

    /**
     * 采购订单ID
     */
    @ExcelProperty("采购单号")
    private String purchaseOrderId;

    /**
     * 父采购订单ID
     */
    @ExcelProperty("父采购单")
    private String parentPurchaseOrderId;


    /**
     * 采购单含税金额
     */
    @ExcelProperty("采购单含税金额")
    private BigDecimal purchaseTotalPrice;

    /**
     * 采购单未税金额
     */
    @ExcelProperty("采购单未税金额")
    private BigDecimal waresPurchaseTotalPrice;

    /**
     * 商品SKUID
     */
    @ExcelProperty("商品SKUID")
    private Long skuId;

    /**
     * sku数量
     */
    @ExcelProperty("数量")
    private Integer skuNum;

    /**
     * 销售单位
     */
    @ExcelProperty("单位")
    private String saleUnit;

    /**
     * 含税价
     */
    @ExcelProperty("含税采购价")
    private BigDecimal includeTaxPrice;

    /**
     * 增值税
     */
    @ExcelIgnore
    private BigDecimal valueAddedTax;

    /**
     * 增值税率
     */
    @ExcelProperty("增值税率")
    private BigDecimal valueAddedTaxRate;

    /**
     * 商品采购价,4位小数
     */
    @ExcelProperty("未税采购价")
    private BigDecimal purchasePrice;

    /**
     * 商品小计,4位小数
     */
    @ExcelIgnore
    private BigDecimal skuAllPrice;

    /**
     * 商品结算总额
     */
    @ExcelProperty("商品结算总额")
    private BigDecimal skuSettlementAmount;
}
