package com.jdi.isc.order.center.domain.delivery.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 订单物流轨迹信息表
 */
@TableName(value = "jdi_isc_order_delivery_track_sharding")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class OrderDeliveryTrackPO extends BasePO {

    /**
     * 订单配送信息表id
     */
    private Long orderDeliveryId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 配送阶段，0:境内，1:境外
     */
    private Integer deliveryStage;

    /**
     * 跟踪属性，多语言，(您的订单已经进入京东北京仓库准备出库)
     */
    private String trackContentInfo;

    /**
     * 操作人
     */
    private String trackOperator;

    /**
     * 物流轨迹时间
     */
    private Date trackMsgTime;
    /**
     * 物流轨迹类别
     */
    private Integer trackType;
}
