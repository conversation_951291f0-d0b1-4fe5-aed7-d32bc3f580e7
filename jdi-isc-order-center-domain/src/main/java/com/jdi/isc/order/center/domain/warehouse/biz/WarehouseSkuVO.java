package com.jdi.isc.order.center.domain.warehouse.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: 仓信息对应sku映射 VO实体类
 * @Author: wangpeng965
 * @Date: 2024/07/18 11:33
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class WarehouseSkuVO extends BasicVO {
    private static final long serialVersionUID = 1L;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String skuName;


}
