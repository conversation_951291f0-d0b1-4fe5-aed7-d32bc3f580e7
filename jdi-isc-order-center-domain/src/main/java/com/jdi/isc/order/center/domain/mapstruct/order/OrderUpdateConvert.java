package com.jdi.isc.order.center.domain.mapstruct.order;

import com.jdi.isc.order.center.api.biz.req.*;
import com.jdi.isc.order.center.domain.ept.req.EptSubmitOrderReqVO;
import com.jdi.isc.order.center.domain.order.biz.BatchSubmitOrderParamVO;
import com.jdi.isc.order.center.domain.order.biz.BatchSubmitOrderWareVO;
import com.jdi.isc.order.center.domain.order.biz.UpdateOrderStatusReqVO;
import com.jdi.isc.order.center.domain.order.biz.UpdateOrderThirdIdVO;
import com.jdi.isc.order.center.domain.order.po.OrderBizInfoPO;
import com.jdi.isc.order.center.domain.order.po.OrderMsgPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OrderUpdateConvert {

    OrderUpdateConvert INSTANCE = Mappers.getMapper(OrderUpdateConvert.class);

    UpdateOrderStatusReqVO apiReq2voReq(UpdateOrderStatusApiReq updateOrderStatusApiReq);

    EptSubmitOrderReqVO manageApiReq2voReq(UpdateOrderStatusManageApiReq updateOrderStatusManageApiReq);

    default EptSubmitOrderReqVO submitConfig2voReq(EptSubmitOrderReqVO eptSubmitOrderReqVO, EptSubmitOrderReqVO config){
        if(eptSubmitOrderReqVO == null || config == null){
            return eptSubmitOrderReqVO;
        }
        eptSubmitOrderReqVO.setIopPin(config.getIopPin());
        eptSubmitOrderReqVO.setTradeRoute(config.getTradeRoute());
        eptSubmitOrderReqVO.setSellerErp(config.getSellerErp());
        eptSubmitOrderReqVO.setPeriodType(config.getPeriodType());
        eptSubmitOrderReqVO.setPrepaymentRatio(config.getPrepaymentRatio());
        eptSubmitOrderReqVO.setFinalPaymentPeriod(config.getFinalPaymentPeriod());
        eptSubmitOrderReqVO.setExchangeRate(config.getExchangeRate());
        eptSubmitOrderReqVO.setExchangeRatePrice(config.getExchangeRatePrice());
        return eptSubmitOrderReqVO;
    }

    default EptSubmitOrderReqVO orderMsgPo2voReq(OrderMsgPO orderMsgPO, String operateType){
        if(orderMsgPO == null){
            return null;
        }
        EptSubmitOrderReqVO eptSubmitOrderReqVO = new EptSubmitOrderReqVO();
        eptSubmitOrderReqVO.setOrderId(orderMsgPO.getOrderId());
        eptSubmitOrderReqVO.setThirdOrderId(orderMsgPO.getThirdOrderId());
        eptSubmitOrderReqVO.setPin(orderMsgPO.getPin());
        eptSubmitOrderReqVO.setOperateType(operateType);
        eptSubmitOrderReqVO.setSourceCode(orderMsgPO.getSourceCode());
        eptSubmitOrderReqVO.setOperateErp(orderMsgPO.getPin());
        return eptSubmitOrderReqVO;
    }

    EptSubmitOrderReqVO orderBizInfo2submitEptVo(OrderBizInfoPO orderBizInfoPO);

    UpdateOrderThirdIdVO thirdIdReqToVoReq(UpdateOrderThirdIdReq req);

    BatchSubmitOrderParamVO batchSubmitOrder2Vo(BatchSubmitOrderParamDTO paramDTO);

    BatchSubmitOrderWareDTO batchSubmitOrderWare2Dto(BatchSubmitOrderWareVO wareVO);
}
