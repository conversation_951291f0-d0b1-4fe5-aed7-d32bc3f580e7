package com.jdi.isc.order.center.domain.waybill.biz;

import com.jdi.isc.order.center.api.common.BasicDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 订单签收单
 * @Author: zhaojianguo21
 * @Date: 2024/12/23 22:47
 **/
@Data
public class QueryOrderMaterialBatchReqRes implements Serializable {

    @Data
    @NoArgsConstructor
    public static class Request implements Serializable {
        /**
         * 订单号
         */
        private Set<Long> orderIds;

        private Map<String,Long> validReqAndOrderIdMap;
    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BasicDTO implements Serializable{
        private Long orderId;
        private String waybillNum;
        private String material;
    }

}
