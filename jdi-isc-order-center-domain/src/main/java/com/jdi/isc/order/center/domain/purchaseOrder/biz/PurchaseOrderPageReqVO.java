package com.jdi.isc.order.center.domain.purchaseOrder.biz;


import com.jdi.isc.order.center.api.common.validation.group.PurchaseOrderValidationGroup;
import com.jdi.isc.order.center.common.biz.BasePageVO;
import com.jdi.isc.order.center.domain.common.biz.DataIsolationQueryVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * 采购单分页查询入参
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderPageReqVO extends BasePageVO  {

    /**
     * 支持语言种类
     */
    protected List<String> localeList;

    /**
     * 应用code
     */
    protected String systemCode;

    /**
     * 国家码
     */
    @NotNull(message = "countryCode not null", groups = {PurchaseOrderValidationGroup.supplierPage.class})
    private String countryCode;

    /**
     * 采购单类型【1.本土；2跨境】
     */
    @NotNull(message = "purchaseOrderType not null", groups = {PurchaseOrderValidationGroup.stockUpPage.class,PurchaseOrderValidationGroup.directSupplyPage.class,PurchaseOrderValidationGroup.consignmentPage.class})
    private Integer purchaseOrderType;

    /**
     * 采购模式集合
     * @See com.jdi.isc.order.center.domain.enums.purchase.PurchaseModelEnum
     */
    private List<Integer> purchaseModels;

    /**
     * 采购模式
     * @See com.jdi.isc.order.center.domain.enums.purchase.PurchaseModelEnum
     */
    @NotNull(message = "purchaseOrderType not null", groups = {PurchaseOrderValidationGroup.stockUpPage.class,PurchaseOrderValidationGroup.directSupplyPage.class,PurchaseOrderValidationGroup.consignmentPage.class})
    private Integer purchaseModel;



    /**
     * 供应商简码
     */
    @NotNull(message = "supplierCode not null", groups = {PurchaseOrderValidationGroup.supplierPage.class})
    private String supplierCode;

    /**
     * 采购单号
     */
    @Length(max = 50,message = "采购单号超长")
    private String purchaseOrderId;

    /**
     * 采购单ids
     */
    private Set<String> purchaseOrderIds;


    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单id集合
     */
    private Set<Long> orderIds;

    /**
     * 运单号
     */
    private String waybillNum;

    /**
     * 采购单后台状态(0:待确认，1:已确认，2:已取消，3：已发货，4：已入库)
     */
    private Integer purchaseOrderStatus;

    /**
     * 采购单状态集合类
     */
    private List<Integer> purchaseOrderStatusSet;


    /**
     * 父采购单号
     */
    @Length(max = 50,message = "父采购单号超长")
    private String splitPurchaseOrderId;

    /**
     * 原始父采购单号
     */
    private String parentPurchaseOrderId;

    /**
     * IOP订单号
     */
    @Range(min = 0, message = "IOP订单号不正确")
    private Long iopOrderId;



    /**
     * 入库单号
     */
    @Length(max = 50,message = "入库单号超长")
    private String enterWarehouseNo;

    /**
     * 备货仓Id
     */
    private String warehouseId;

    /**
     * 下单时间
     */
    private Long purchaseCreateTimeStart;
    private Long purchaseCreateTimeEnd;

    /**
     * 接单时间条件
     */
    private Long receiveTimeStart;
    private Long receiveTimeEnd;

    /**
     * 发货时间条件
     */
    private Long shippedTimeStart;
    private Long shippedTimeEnd;

    /**
     * 入企配仓时间条件
     */
    private Long enterWarehouseTimeStart;
    private Long enterWarehouseTimeEnd;

    /**
     * 完成时间条件
     */
    private Long completeTimeStart;
    private Long completeTimeEnd;

    /**
     * 取消时间条件
     */
    private Long cancelTimeStart;
    private Long cancelTimeEnd;

    /**
     * 入集货仓时间条件
     */
    private Long enterStorehouseTimeStart;
    private Long enterStorehouseTimeEnd;

    /**
     * 确认后的时间
     */
    private Long confirmTimeStart;
    private Long confirmTimeEnd;

    /**
     * 拆单时间条件
     */
    private Long createTimeStart;
    private Long createTimeEnd;

    /**
     * 发货时间
     */
    private Integer shippedTimeSort;
    /**
     * 拆单时间
     */
    private Integer createTimeSort;
    /**
     * 下单时间
     */
    private Integer purchaseCreateTimeSort;
    /**
     * 接单时间
     */
    private Integer receiveTimeSort;

    /**
     * 权限隔离
     */
    private DataIsolationQueryVO dataIsolationQueryVO;

}
