package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR> 采购单商品表
 */
@Data
public class PurchaseOrderWareVO extends BasicPO {

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * mku编码
     */
    private Long mkuId;

    /**
     * 履约sku编码
     */
    private Long skuId;

    /**
     * sku末级类目id
     */
    private Long catId;

    /**
     * sku的品牌id
     */
    private Long brandId;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 商品采购价,4位小数
     */
    private BigDecimal purchasePrice;

    /**
     * 商品小计,4位小数
     */
    private BigDecimal skuAllPrice;

    /**
     * mku数量
     */
    private Integer skuNum;

    /**
     * sku类型 0：商品 1：附件 2：赠品
     */
    private Integer skuType;

    /**
     * 父类sku 主要赠品附件用
     */
    private Long parentSkuId;

    /**
     * 国家码
     */
    private String skuCountryCode;

    /**
     * 商品扩展信息
     */
    private String skuJsonInfo;

    /**
     * mku类型 0：商品 1：附件 2：赠品
     */
    private Integer mkuType;

    /**
     * 父类mku 主要赠品附件用
     */
    private Long parentMkuId;

    /**
     * 国家码
     */
    private String mkuCountryCode;

    /**
     * mku信息快照，不含商详
     */
    private String mkuJsonInfo;

    /**
     * 含税价
     */
    private BigDecimal includeTaxPrice;

    /**
     * 增值税
     */
    private BigDecimal valueAddedTax;

    /**
     * 增值税率
     */
    private BigDecimal valueAddedTaxRate;

    /**
     * 版本号
     */
    private Integer version;


    /**
     * 商品图片
     */
    private String skuImage;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品多语言名称
     */
    private Map<String, String> skuNameMap;

    /**
     * 销售单位
     */
    private String saleUnit;

    /**
     * 未税总额
     */
    private BigDecimal purchaseTotalPrice;


    /**
     * 采购单发票中使用的商品名称
     */
    private String invoiceSkuName;
   
}