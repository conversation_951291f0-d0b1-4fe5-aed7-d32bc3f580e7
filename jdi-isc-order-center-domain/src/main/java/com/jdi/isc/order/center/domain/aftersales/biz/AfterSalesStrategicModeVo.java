package com.jdi.isc.order.center.domain.aftersales.biz;

import lombok.Data;

import java.util.List;

@Data
public class AfterSalesStrategicModeVo {


    /**
     * 表示售后策略模式中的步骤序号
     */
    private Integer step;


    /**
     * 表示售后策略模式中的业务类型
     */
    private Integer bizType;


    /**
     * 表示售后策略模式中的描述信息
     */
    private String describe;



    /**
     * 表示售后策略模式中的下一个步骤节点，用于构建策略模式的链式结构
     */
    private List<AfterSalesStrategicModeVo> nextAfterSalesStrategicModeVo;






}
