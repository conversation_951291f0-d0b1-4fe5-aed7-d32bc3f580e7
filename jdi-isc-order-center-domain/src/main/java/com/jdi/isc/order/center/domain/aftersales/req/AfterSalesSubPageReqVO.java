package com.jdi.isc.order.center.domain.aftersales.req;

import com.jdi.isc.order.center.api.common.BasePageAPIVO;
import com.jdi.isc.order.center.common.biz.BasePageVO;
import com.jdi.isc.order.center.domain.common.biz.DataIsolationQueryVO;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 售后子流程分页查询条件
 * @Author: zhaojianguo21
 * @Date: 2025/6/17
 **/

@Data
public class AfterSalesSubPageReqVO extends BasePageAPIVO implements Serializable {

    /**
     * 售后子单单号
     */
    private String bizId;

    /**
     * 单据类型100.承担费用200.取件维修300.换货补货400.退款
     */
    private Integer bizType;

    /**
     * 售后单号
     */
    private String afterSalesOrderId;

    /**
     * 京东国际订单号
     */
    private Long orderId;

    /**
     * iop订单号
     */
    private Long iopOrderId;

    /**
     * AfterSalesSubStatusEnum
     * {@link com.jdi.isc.order.center.domain.enums.aftersales.AfterSalesSubStatusEnum}
     */
    private Integer status;

    /**
     * 原因
     */
    private String reason;

    /**
     * 关注人
     */
    private String follower;

    /**
     * 角色
     */
    private String role;

    /**
     * 客户号
     */
    private String clientCode;

    /**
     * 是否可以查看所有人员的数据
     */
    private Boolean viewAllData;

    /**
     * 数据隔离查询条件
     */
    private DataIsolationQueryVO dataIsolationQueryVO;

}