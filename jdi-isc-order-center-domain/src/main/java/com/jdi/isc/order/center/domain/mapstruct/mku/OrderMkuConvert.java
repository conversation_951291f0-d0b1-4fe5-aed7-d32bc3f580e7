package com.jdi.isc.order.center.domain.mapstruct.mku;

import com.jdi.isc.aggregate.read.api.sku.resp.SkuInfoReadResp;
import com.jdi.isc.aggregate.read.wiop.api.orderCenter.resp.OrderMkuDetailReadResp;
import com.jdi.isc.order.center.api.biz.resp.MkuInfoOrderApiResp;
import com.jdi.isc.order.center.api.ofc.biz.rsp.SkuInfoResDTO;
import com.jdi.isc.order.center.domain.mku.biz.OrderMkuDetailReadDTO;
import com.jdi.isc.order.center.domain.mku.po.OrderMkuExtInfoPO;
import com.jdi.isc.order.center.domain.mku.po.OrderSkuExtInfoPO;
import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.order.po.OrderSkuDetailPO;
import com.jdi.isc.order.center.domain.order.po.OrderWareMkuJsonInfoPO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.SkuInfoReadDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/28 18:02
 */
@Mapper
public interface OrderMkuConvert {

    OrderMkuConvert INSTANCE = Mappers.getMapper(OrderMkuConvert.class);

    MkuInfoOrderApiResp orderMkuDetailReadResp2orderMkuInfoDTO(OrderMkuDetailReadResp orderMkuDetailReadResp);

    OrderWarePO readResp2orderWarePO(OrderMkuDetailReadResp orderMkuDetailReadResp);

    @Mappings({
            @Mapping(target = "fixedSkuDetail", ignore = true)
    })
    OrderMkuDetailReadDTO readResp2readDTO(OrderMkuDetailReadResp orderMkuDetailReadResp);

    Map<Long, OrderMkuDetailReadDTO> readRespMap2readDTOMap(Map<Long, OrderMkuDetailReadResp> map);

    @Mappings({
            @Mapping(target = "mkuPrice", source = "salePrice"),
            @Mapping(target = "mkuTotalTaxPrice", source = "valueAddedTaxes")
    })
    MkuInfoOrderApiResp po2mkuInfoOrderApiResp(OrderWarePO orderWarePO);

    @Mappings({
            @Mapping(target = "createTime", expression = "java(orderWarePO.getCreateTime().getTime())"),
            @Mapping(target = "updateTime", expression = "java(orderWarePO.getUpdateTime().getTime())"),
            @Mapping(target = "mkuId", source = "orderWarePO.mkuId"),
            @Mapping(target = "catId", source = "orderWarePO.catId"),
            @Mapping(target = "customsClearance", source = "orderWareMkuJsonInfoPO.customsClearance")
    })
    OrderMkuExtInfoPO po2mkuExtInfoPo(OrderWarePO orderWarePO, OrderWareMkuJsonInfoPO orderWareMkuJsonInfoPO);

    @Mappings({
            @Mapping(target = "createTime", expression = "java(orderWarePO.getCreateTime().getTime())"),
            @Mapping(target = "updateTime", expression = "java(orderWarePO.getUpdateTime().getTime())"),
            @Mapping(target = "mkuId", source = "orderWarePO.mkuId"),
            @Mapping(target = "catId", source = "orderWarePO.catId"),
            @Mapping(target = "skuId", source = "orderWarePO.skuId"),
            @Mapping(target = "currency", source = "orderWarePO.currency"),
            @Mapping(target = "hsCode", source = "orderWarePO.hsCode"),
            @Mapping(target = "vendorCode", source = "orderWarePO.vendorCode")
    })
    OrderSkuExtInfoPO po2skuExtInfoPo(OrderWarePO orderWarePO, OrderSkuDetailPO orderSkuDetailPO);


    OrderSkuDetailPO sku2SkuInfoReadResp(SkuInfoReadResp skuInfoReadResp);

    OrderSkuDetailPO sku2SkuInfoReadDTO(SkuInfoReadDTO skuInfoReadDTO);

    SkuInfoReadDTO sku2SkuInfoReadDto(SkuInfoReadResp skuInfoReadResp);

    SkuInfoResDTO sku2SkuInfoResDto(SkuInfoReadDTO skuInfoReadDTO);

    OrderWarePO copyPO(OrderWarePO orderWarePO);

    List<MkuInfoOrderApiResp> listPo2mkuInfoOrderApiResp(List<OrderWarePO> orderWarePOList);

}
