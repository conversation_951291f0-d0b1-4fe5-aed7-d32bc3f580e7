package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.common.biz.BasePageVO;
import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import com.jdi.isc.order.center.domain.common.biz.DataIsolationQueryVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/5/24 9:25 上午
 */
@Data
public class PurchaseOrderPageVO implements Serializable{

    @EqualsAndHashCode(callSuper = true)
    @Data
    @NoArgsConstructor
    public static class Request extends BasePageVO implements Serializable {

        /**
         * 采购单状态
         */
        private Integer purchaseOrderStatus;

        /**
         * 采购单类型【1.本土；2跨境】
         */
        private Integer purchaseOrderType;

        /**
         * 国家码
         */
        private String countryCode;

        /**
         * 供应商简码
         */
        private String supplierCode;

        /**
         * 采购单号
         */
        private String purchaseOrderId;

        /**
         * 订单
         */
        private Long orderId;

        /**
         * 接单时间条件
         */
        private Long receiveTimeStart;
        private Long receiveTimeEnd;

        /**
         * 发货时间条件
         */
        private Long shippedTimeStart;
        private Long shippedTimeEnd;

        /**
         * 入仓时间条件
         */
        private Long enterWarehouseTimeStart;
        private Long enterWarehouseTimeEnd;

        /**
         * 完成时间条件
         */
        private Long completeTimeStart;
        private Long completeTimeEnd;

        /**
         * 取消时间条件
         */
        private Long cancelTimeStart;
        private Long cancelTimeEnd;

        /**
         * 父采购单号
         */
        private String splitPurchaseOrderId;


        /**
         * 备货仓名称
         */
        private String warehouseNo;

        /**
         * IOP订单号
         */
        private Long iopOrderId;

        /**
         * 入库单号
         */
        private String enterWarehouseNo;

        /**
         * 入集货仓时间条件
         */
        private Long enterStorehouseTimeStart;
        private Long enterStorehouseTimeEnd;

        /**
         * 下单时间条件
         */
        private Long purchaseCreateTimeStart;
        private Long purchaseCreateTimeEnd;
        /**
         * 备货采购单id
         */
        private Long id;

        /**
         * 备货仓id
         */
        private String warehouseId;


        /**
         * 原始父采购单号
         */
        private String parentPurchaseOrderId;

        /**
         * 数据隔离条件
         */
        private DataIsolationQueryVO dataIsolationQueryVO;

    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BasicVO implements Serializable{

        /**
         * 采购单号
         */
        private String purchaseOrderId;

        /**
         * 订单pin
         */
        private String pin;

        /**
         * 原始父采购单号
         */
        private String parentPurchaseOrderId;

        /**
         * 当前订单的上一级父采购单号
         */
        private String splitPurchaseOrderId;

        /**
         * 订单
         */
        private Long orderId;

        /**
         * 供应商简码
         */
        private String supplierCode;

        /**
         * sku总数量(所有skuNum的加合)
         */
        private Integer skuNum;

        /**
         * 采购单商品种类
         */
        private Integer skuKindNum;

        /**
         * 采购单后台状态(0:待确认，1:已确认，2:已取消，3：已发货，4：已入库)
         */
        private Integer purchaseOrderStatus;

        /**
         * 采购单类型【1.本土；2跨境】
         */
        private Integer purchaseOrderType;

        /**
         * 国家码
         */
        private String countryCode;

        /**
         * 币种:VND越南,THB泰国,CNY人民币,USD美元
         */
        private String currency;

        /**
         * 总采购价,4位小数(warePurchaseTotalPrice +  purchaseOrderTaxes +  serviceFee + orderFreightPrice)
         */
        private BigDecimal purchaseTotalPrice;

        /**
         * 服务费
         */
        private BigDecimal serviceFee;

        /**
         * 订单总运费
         */
        private BigDecimal orderFreightPrice;

        /**
         * 商品采购总价,4位小数(ware上的采购价)
         */
        private BigDecimal waresPurchaseTotalPrice;

        /**
         * 采购单税金(ware上的tax总和)
         */
        private BigDecimal purchaseOrderTaxes;


        /**
         * 企配仓编码
         */
        private String enterpriseWarehouseCode;

        /**
         * 订单版本号
         */
        private Integer version;

        /**
         * 扩展标，下游透传
         */
        private String purchaseOrderExtInfo;

        /**
         * 修改采购单的客户端信息
         */
        private String updateClientInfo;

        /**
         * 采购单的有效状态
         */
        private Integer validState;

        /**
         * 采购单下单时间，子单下单时间为父单下单时间，早于createTime
         */
        private Long purchaseCreateTime;

        /**
         * 接单时间
         */
        private Long receiveTime;

        /**
         * 发货时间
         */
        private Long shippedTime;

        /**
         * 入仓时间
         */
        private Long enterWarehouseTime;

        /**
         * 完成时间
         */
        private Long completeTime;

        /**
         * 取消时间
         */
        private Long cancelTime;

        /**
         * 入集货仓时间（国内）
         */
        private Long enterStorehouseTime;
        /**
         * 入备货仓单号
         */
        private String enterWarehouseNo;
        /**
         * IOP订单号
         */
        private String iopOrderId;

        /**
         * 实际入库商品数量
         */
        private Integer inboundWarehouseWareNum;

        /**
         * 确认后的时间
         */
        private Long confirmTime;

    }
}
