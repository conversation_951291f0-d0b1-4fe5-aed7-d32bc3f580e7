package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @Description 采购单入仓信息
 * @Date 2024/5/27 9:53 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderEnterWarehouseWareVO extends BasicVO {

    /**
     * 采购单
     */
    private String purchaseOrderId;

    /**
     * 第三方来源标识
     */
    private String dataSource;

    /**
     *  唯一标识
     */
    private String uniqueFlag;

    /**
     * 入仓商品id
     */
    private Long skuId;

    /**
     * 入仓商品数量
     */
    private Integer num;

    /**
     * 入仓编号
     */
    private String warehouseId;

}
