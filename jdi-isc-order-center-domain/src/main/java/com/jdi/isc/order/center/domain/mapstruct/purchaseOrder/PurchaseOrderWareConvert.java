package com.jdi.isc.order.center.domain.mapstruct.purchaseOrder;

import com.jdi.isc.order.center.api.purchaseOrder.biz.resp.PurchaseOrderWareApiDTO;
import com.jdi.isc.order.center.domain.mku.po.OrderWarePO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.PurchaseOrderWareVO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderWarePO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PurchaseOrderWareConvert {

    PurchaseOrderWareConvert INSTANCE = Mappers.getMapper(PurchaseOrderWareConvert.class);

    PurchaseOrderWareVO purchaseOrderWarePo2Vo(PurchaseOrderWarePO purchaseOrderWarePO);

    List<PurchaseOrderWareVO> listPurchaseOrderWarePo2Vo(List<PurchaseOrderWarePO> purchaseOrderWarePOList);

    PurchaseOrderWarePO po2po(PurchaseOrderWarePO purchaseOrderWarePO);

    List<PurchaseOrderWarePO> listVo2Po(List<PurchaseOrderWareVO> purchaseOrderWareVOList);

    @Mappings({
            @Mapping(target = "createTime", expression = "java(orderWarePO.getCreateTime().getTime())"),
            @Mapping(target = "updateTime", expression = "java(orderWarePO.getUpdateTime().getTime())")
    })
    PurchaseOrderWarePO orderWarePo2Po(OrderWarePO orderWarePO);


    List<PurchaseOrderWareApiDTO> poList2DTOList(List<PurchaseOrderWarePO> purchaseOrderWarePOList);

    List<PurchaseOrderWarePO> listPo2Po(List<PurchaseOrderWarePO> wl);
}
