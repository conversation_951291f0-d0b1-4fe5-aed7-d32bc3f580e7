package com.jdi.isc.order.center.domain.purchaseOrder.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 采购单表
 */
@TableName(value = "jdi_isc_purchase_order_stream_mark_sharding")
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderStreamMarkPO extends BasicPO {


    /**
     * 采购单的唯一标识符
     */
    private String purchaseOrderId;



    /**
     * 供应商代码
     */
    private String supplierCode;



    /**
     * 国家代码，标识采购单相关的国家信息
     */
    private String countryCode;



    /**
     * 标识采购单的标记信息
     * 标记 1线下结算 2线上结算
     */
    private Integer mark;




}

