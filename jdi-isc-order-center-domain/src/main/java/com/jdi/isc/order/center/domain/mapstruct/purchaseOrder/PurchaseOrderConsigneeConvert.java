package com.jdi.isc.order.center.domain.mapstruct.purchaseOrder;


import com.jdi.isc.order.center.api.purchaseOrder.biz.resp.PurchaseOrderConsigneeApiDTO;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.res.PurchaseOrderConsigneeReadApiDTO;
import com.jdi.isc.order.center.domain.purchaseOrder.biz.PurchaseOrderConsigneeVO;
import com.jdi.isc.order.center.domain.purchaseOrder.po.PurchaseOrderConsigneePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 采购单收货信息
 */

@Mapper
public interface PurchaseOrderConsigneeConvert {

    PurchaseOrderConsigneeConvert INSTANCE = Mappers.getMapper(PurchaseOrderConsigneeConvert.class);

    PurchaseOrderConsigneePO vo2Po(PurchaseOrderConsigneeVO vo);

    PurchaseOrderConsigneePO copyPo(PurchaseOrderConsigneePO purchaseOrderConsigneePO);

    PurchaseOrderConsigneeVO po2Vo(PurchaseOrderConsigneePO consigneePO);

    PurchaseOrderConsigneeApiDTO po2DTO(PurchaseOrderConsigneePO consigneePO);

    List<PurchaseOrderConsigneePO> listPo2Po(List<PurchaseOrderConsigneePO> consigneeList);

    List<PurchaseOrderConsigneeReadApiDTO> listPo2ReadApi(List<PurchaseOrderConsigneePO> purchaseOrderConsigneePOS);

    PurchaseOrderConsigneeReadApiDTO po2ApiDto(PurchaseOrderConsigneePO purchaseOrderConsigneePO);
}
