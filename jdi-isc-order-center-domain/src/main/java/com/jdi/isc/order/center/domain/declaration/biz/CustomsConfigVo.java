package com.jdi.isc.order.center.domain.declaration.biz;


import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;

/**
 * 报关信息配置
 * <AUTHOR>
 * @date 20241109
 */
@Data
public class CustomsConfigVo extends BasicVO {


    /** 业务名 */
    private String bizName;

    /** 业务编码 */
    private String bizCode;

    /**
     * 业务类型
     * 1:境内发货人
     * 2境外收件人
     * 3生成销售单位
     * */
    private Integer type;


}
