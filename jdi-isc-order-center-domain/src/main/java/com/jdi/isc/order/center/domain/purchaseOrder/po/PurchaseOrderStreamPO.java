package com.jdi.isc.order.center.domain.purchaseOrder.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR> 采购单表
 */
@TableName(value = "jdi_isc_purchase_order_stream")
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderStreamPO extends BasicPO {


    /**
     * 防重编号
     */
    private String outCode;

    /**
     * 上游系统计费汇总后的结算编码
     */
    private String outSettleCode;

    /**
     * 租户
     */
    private String tenantCode;

    /**
     * 来源系统编号
     */
    private String authSys;

    /**
     * 账套
     */
    private String bizType;

    /**
     * 结算分组
     */
    private String settleGroup;

    /**
     * 费用类型
     */
    private String expenseType;

    /**
     * 渠道编码，默认-1
     */
    private String channelCode;

    /**
     * 主OU编码
     */
    private String ou;

    /**
     * 主OU名称
     */
    private String ouName;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 主客商编码
     */
    private String partnerCode;

    /**
     * 主客商名称
     */
    private String partnerName;

    /**
     * 往来户类型默认1
     */
    private String partnerType;

    /**
     * 采购单单价
     */
    private BigDecimal ledgerPrice;

    /**
     * 采购单数量
     */
    private Integer ledgerQuantity;

    /**
     * 采购单总金额
     */
    private BigDecimal ledgerAmount;

    /**
     * 结算币种
     */
    private String settleCurrency;

    /**
     * 采购单币种
     */
    private String accountCurrency;

    /**
     * 采购单币种
     */
    private String paymentCurrency;

    /**
     * 采购单完成时间
     */
    private Long bizTime;

    /**
     * 采购单号
     */
    private String purchaseNumber;

    /**
     * 关联的客户订单号
     */
    private Long orderNumber;

    /**
     * 业务单号
     */
    private String bizCode;

    /**
     * 批次号（长度<64）
     */
    private String batchNo;

    /**
     * 商品编码
     */
    private Long sku;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 责任人erp账号
     */
    private String responsiblePerson;

    /**
     * 责任人员（采销员）名称
     */
    private String responsiblePersonName;

    /**
     * 部门编码
     */
    private String departmentCode;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 开票方式：1 无需开票 2 京东开发票 3 供应商开发票  5 京东开收据 6 京东开预借发票
     */
    private Integer invoiceMode;

    /**
     * 收款方式 1 票扣/2 账扣/ 3 单独收款(电汇)
     */
    private Integer receiptMode;

    /**
     * 结算单生成方式：1实时、2离线
     */
    private Integer settleTriggerMode;

    /**
     * 采购单下单时候记录时间（月结传0）
     */
    private Integer checkinDays;

    /**
     * 扩展字段
     */
    private String settleLedgerExtMap;

    /**
     * 流水推送状态1待推送2推送成功3推送失败
     */
    private Integer pushStatus;

}

