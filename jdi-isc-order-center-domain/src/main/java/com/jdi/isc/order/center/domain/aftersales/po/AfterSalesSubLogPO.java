package com.jdi.isc.order.center.domain.aftersales.po;

/**
 * 售后子单日志实体类
 *
 * <AUTHOR>
 * @description
 * @date 2025/6/25
 */

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 售后子单日志实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "jdi_isc_after_sales_sub_log_sharding")
@NoArgsConstructor
public class AfterSalesSubLogPO extends BasicPO {

    /**
     * 操作业务单号
     */
    private String bizId;

    /**
     * 单据类型10.承担费用20.取件维修30.换货补货40.退款
     */
    private Integer bizType;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 售后单号
     */
    private String afterSalesOrderId;

    /**
     * 单据快照信息
     */
    private String bizInfo;

    /**
     * 售后单操作状态
     */
    private Integer status;

    /**
     * 版本号
     */
    private Integer version;



}