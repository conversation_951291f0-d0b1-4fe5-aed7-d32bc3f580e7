package com.jdi.isc.order.center.domain.forecast.biz;

import com.jdi.isc.order.center.domain.forecast.po.ForecastOrderPO;
import com.jdi.isc.order.center.domain.forecast.po.ForecastOrderWarePO;
import lombok.Data;

import java.util.List;

@Data
public class ForecastOrderMsgVo extends ForecastOrderPO {



    /**
     * 预报备货单后台前置状态
     */
    private Integer beforeForecastOrderStatus; // 预报备货单后台状态



    /**
     * 预报备货单的分割类型标识 1预拆单2客户主动拆单
     */
    private Integer splitType;


    /**
     * 预报备货单商品商品
     */
    private List<ForecastOrderWarePO> forecastOrderWarePOList;


    /**
     * 子单
     */
    private List<ForecastOrderMsgVo> childForecastOrderList;







}
