package com.jdi.isc.order.center.domain.delivery.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.jdi.isc.order.center.domain.common.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 订单配送信息表
 * @TableName jdi_isc_order_delivery_sharding
 */
@TableName(value ="jdi_isc_order_delivery_sharding")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class OrderDeliveryPO extends BasePO {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 配送阶段，0:境内，1:境外
     */
    private Integer deliveryStage;

    /**
     * 父订单号
     */
    private Long parentOrderId;

    /**
     * 配送单号
     */
    private String deliveryOrderId;

    /**
     * 承运人信息，多语言
     */
    private String deliveryCarrierInfo;
}