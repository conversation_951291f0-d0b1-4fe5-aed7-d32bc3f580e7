package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/5/23 4:45 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderConsigneeVO extends BasicVO {

    /**
     * 采购单id
     */
    private String purchaseOrderId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 企配仓编码
     */
    private String enterpriseWarehouseCode;

    /**
     * 收货人id
     */
    private Long consigneeId;

    /**
     * 收货人名称
     */
    private String consigneeEncrypt;

    /**
     * 收货人名称
     */
    private String consigneeIndex;

    /**
     * 收货详细地址
     */
    private String consigneeAddressEncrypt;

    /**
     * 收货详细地址
     */
    private String consigneeAddressIndex;

    /**
     * 收货人电话
     */
    private String consigneePhoneEncrypt;

    /**
     * 收货人电话
     */
    private String consigneePhoneIndex;

    /**
     * 收货人手机号
     */
    private String consigneeMobileEncrypt;

    /**
     * 收货人手机号
     */
    private String consigneeMobileIndex;

    /**
     * 收货人邮箱
     */
    private String consigneeEmailEncrypt;

    /**
     * 收货人邮箱
     */
    private String consigneeEmailIndex;

    /**
     * 收货国家
     */
    private String consigneeCountry;

    /**
     * 收货地址省ID
     */
    private Long consigneeProvinceId;

    /**
     * 收货地址城市ID
     */
    private Long consigneeCityId;

    /**
     * 收货地址区县ID
     */
    private Long consigneeCountyId;

    /**
     * 收货地址乡镇ID
     */
    private Long consigneeTownId;

    /**
     * 邮政编码
     */
    private String consigneeZip;

    /**
     * 收货人名称
     */
    private String consignee;

    /**
     * 收货详细地址
     */
    private String consigneeAddress;

    /**
     * 收货人电话
     */
    private String consigneePhone;

    /**
     * 收货人手机号
     */
    private String consigneeMobile;

    /**
     * 收货人邮箱
     */
    private String consigneeEmail;

    /**
     * 主收货人
     */
    private Integer master;

}
