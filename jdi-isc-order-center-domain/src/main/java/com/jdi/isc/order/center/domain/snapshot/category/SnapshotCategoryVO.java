package com.jdi.isc.order.center.domain.snapshot.category;

import com.jdi.isc.order.center.domain.snapshot.common.SnapshotBasicVO;
import lombok.Data;

import java.util.Map;

/**
 * @Description: 类目快照对象
 * @Author: zhaojianguo21
 * @Date: 2025/5/21 14:51
 **/
@Data
public class SnapshotCategoryVO extends SnapshotBasicVO {

    /** 后台类目级别 */
    private Integer level;

    /** 后台类目多语言名称 */
    private Map<String/*语种*/,String/*名称*/> nameLangMap;

}
