package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.aggregate.read.api.sku.resp.SkuInfoReadResp;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/2/7
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SkuInfoReadDTO extends SkuInfoReadResp {

    /**
     * 集运仓编码
     */
    private String storeHouseId;

    /**
     * 海外企配仓编码
     */
    private String enterpriseWarehouseId;

}
