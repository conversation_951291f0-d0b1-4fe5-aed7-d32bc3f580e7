package com.jdi.isc.order.center.domain.orderSnapshot.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.order.center.domain.common.po.BasicPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 订单商品快照 实体类
 * @Author: zhaojianguo21
 * @Date: 2025/05/26 20:48
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("jdi_isc_order_ware_snapshot_sharding")
public class OrderWareSnapshotPO extends BasicPO {

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * mku编码
     */
    private Long mkuId;

    /**
     * 履约sku编码
     */
    private Long skuId;

    /**
     * 商品快照url
     */
    private String snapshotUrl;

    /**
     * 创建快照开始时间
     */
    private Long snapshotStartTime;

    /**
     * 创建快照结束时间
     */
    private Long snapshotEndTime;

    /**
     * 快照状态,0=未创建,1=创建成功,2=创建失败,3=部分信息创建失败
     */
    private Integer snapshotStatus;

}