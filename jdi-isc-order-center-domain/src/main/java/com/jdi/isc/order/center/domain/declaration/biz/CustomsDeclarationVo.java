package com.jdi.isc.order.center.domain.declaration.biz;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class CustomsDeclarationVo {


      /**
       * 唯一标识符，用于区分不同的报关单信息。
       */
      private String id;

      // 报关单Id
      @NotNull(message = "customsId不能为空")
      private String customsId;

      // 订单状态 0草稿 1已提交
      private Integer status;

      // 来源系统【传输所在系统的域名】
      private String sourceSystem = "JDI";

      // 客户编码
      private String customerCode;

      // 客户名称
      private String customerName;

      // 报关服务商Id
      private Long customsAgentId;

      // 报关服务商名称
      private String customsAgentName;

      // 监管方式（0110/9610）
      private String superviseWay;

      // 成交方式（FOB/CIF）
      private String dealWay;

      // 运费
      private String freightFee;

      // 保费
      private String insPRM;

      // 杂费
      private String miscEXP;

      // 子单数目
      private Integer orderNumber;

      // 报关单金额/销售金额
      private BigDecimal customsAmount;
      @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")

      //制单时间 yyyy-MM-dd HH:mm:ss
      private Date       applyDate;

      //制单人erp
      private String   applyUserErp;

      //制单人姓名
      private String   applyUserName;

      //备注
      private String   remark;

      //报关单相关销售、采购、出库信息
      private List<CustomsRelationVo> customsRelation;

      //报关单明细
      private List<CustomsDetailsVo>   customsDetails;

      //单证文件列表
      private List<CustomsOutBoundFileVo>   outBoundFileList=new ArrayList<>();

}
