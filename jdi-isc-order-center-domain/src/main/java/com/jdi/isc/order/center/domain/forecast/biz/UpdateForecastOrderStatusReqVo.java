package com.jdi.isc.order.center.domain.forecast.biz;

import com.jdi.isc.order.center.api.common.SystemInfoOrderApiReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 修改订单状态请求
 * <AUTHOR>
 * @date 2023/12/26 15:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UpdateForecastOrderStatusReqVo implements Serializable {

    private static final long serialVersionUID = -2061730884906849924L;


    /**
     * 预报备货单的唯一标识号
     */
    @NotNull(message = "forecastOrderId is not null")
    private String forecastOrderId; // 预报备货单号

    /**
     * 修改人
     */
    @NotNull(message = "operateAccount is not null")
    private String operateAccount;

    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * 操作  com.jdi.isc.order.center.api.constants.PurchaseOrderStatusConstant
     */
    @NotNull(message = "operate is not null")
    private String operate;



//    /**
//     * 预报备货单的运输信息DTO对象
//     */
//    private ForecastOrderTransportDTO forecastOrderTransportDTO;


    /**
     * 客户信息UpdatePurchaseOrderStatusReqVO
     */
    private SystemInfoOrderApiReq clientReqExt;


    /**
     * 采购单入仓信息
     */
    private ForecastOrderEnterWarehouseVO forecastOrderEnterWarehouseVO;

    /**
     * 操作时间（非必填，不填时，默认用当前时间）
     */
    private Long operateTime;


}
