package com.jdi.isc.order.center.domain.purchaseOrder.po;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购单扩展信息
 */
@Data
public class PurchaseOrderExtInfoPO {

    /**
     * 贸易条款：DDP
     */
    private String tradeType;

    /**
     * 拆单原因
     */
    private String splitOrderRemark;

    /**
     * 预计发货天数(商品)
     */
    private String deliverGoodsDays;

    /**
     * 最短送达天数(履约时间)
     */
    private BigDecimal minDeliveryDays;

    /**
     * 最长送达天数(履约时间)
     */
    private BigDecimal maxDeliveryDays;

    /**
     * 时效规则版本(履约时间)
     */
    private String timeRuleVersion;

    /**
     * 供应商结算方式
     */
    private Integer supplierSettlementMethod;

    /**
     * ap单号
     */
    private String apNumber;

    /**
     * 承诺发货时间 订单确认时间 + 发货时效(d)
     */
    private Long promisedShipmentTime;

    /**
     * 承诺送达时间(订单确认时间+发货时效+max配送时效)
     */
    private Long promisedDeliveryTime;
}
