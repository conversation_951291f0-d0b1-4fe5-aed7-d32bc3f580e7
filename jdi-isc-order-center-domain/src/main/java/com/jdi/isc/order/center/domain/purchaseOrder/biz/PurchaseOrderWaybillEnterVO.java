package com.jdi.isc.order.center.domain.purchaseOrder.biz;

import com.jdi.isc.order.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @Description 采购单入仓信息
 * @Date 2024/5/27 9:53 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderWaybillEnterVO extends BasicVO {

    /**
     * 入仓时间
     */
    private long enterWarehouseDate;

    /**
     * 入仓资料
     */
    private String enterMaterial;
}
