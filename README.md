## 各module释义
### 1. jdi-isc-xxx-api  
>对外提供能力sdk, pom最小依赖原则
### 2. jdi-isc-xxx-common
>通用工具
### 3. jdi-isc-xxx-domain
>工程实体
### 4. jdi-isc-xxx-repository
>工程dao层,注意不同持久化中间件用不同package区分,如jed\es\jimdb
### 5. jdi-isc-xxx-rpc
>依赖外部接口集,如中台服务\库存服务\价格服务等
### 6. jdi-isc-xxx-service
>adapter 实体、枚举、自定义转换层
>atomic 原子服务层
>manage 垂直领域服务层
>protocol 协议服务层,如jdi-isc-xxx-api中的sdk实现类可以放在这里
>support 其他服务,如支撑服务
### 7. jdi-isc-xxx-web
>conf 服务实例配置
>controller 服务入口



## 编码约定:
1. 开发规范参考: https://joyspace.jd.com/h/personal/pages/Qjizgova6mdb15sdazvq
2. 详细设计规范参考: https://joyspace.jd.com/h/personal/pages/R2UQWy0TEPl6Zmcyi1Ol
3. 依赖外部的服务需加特定关键字RPC，如xxxRpcService,封装逻辑位于<jdi-isc-xxx-rpc>下 
4. 对外提供服务需要增加api关键字如xxxApiService,封装逻辑位于<jdi-isc-xxx-api>下 