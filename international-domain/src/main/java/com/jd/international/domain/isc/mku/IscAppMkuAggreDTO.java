package com.jd.international.domain.isc.mku;

import com.jd.international.domain.material.res.MkuMaterialResVO;
import com.jd.international.domain.product.ProductBaseInfoVo;
import com.jd.international.domain.product.ProductCategoryVo;
import com.jd.international.domain.product.ProductPropertyDTO;
import lombok.Data;
import org.apache.commons.lang3.tuple.Pair;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description: 客户端VO
 * @Author: zhaojianguo21
 * @Date: 2023/12/15 09:42
 **/
@Data
public class IscAppMkuAggreDTO implements Serializable {

    private Map<String, String> bigField;
    private ProductBaseInfoVo baseInfo;
    private List<Pair<String, String>> productAttr;
    private ProductCategoryVo productCategory;
    private List<ProductPropertyDTO> saleAttributes;
    private List<IscAppMkuClientPropertyValueRelationDTO> mkuSaleAttributes;

    /**
     * 物料信息
     */
    private MkuMaterialResVO mkuMaterial;

}
