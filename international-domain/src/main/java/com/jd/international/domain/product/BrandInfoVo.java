package com.jd.international.domain.product;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/11/1 10:16
 */
@Data
public class BrandInfoVo {

    /**
     * 商品id
     */
    private Integer brandId;
    /**
     * 商品名称，后续仅使用这个字段，用来代表当前语言的品牌名称
     */
    private String name;
    /**
     * 英文名称
     */
    private String enName;
    /**
     * 中文名称
     */
    private String cnName;
    private String code;
    private String flagshipUrl;
    private String logoUrl;
    private Integer yn;
    private Date created;
    private Date modified;
    private String brandGroupName;
    private Integer mainBrandId;
    private String tradeMarkHolder;
    private String tradeMarkPaper;
    private Integer applySource;
    private Integer isMainBrand;
    private Integer statu;
    private boolean isPopCreate;

}
