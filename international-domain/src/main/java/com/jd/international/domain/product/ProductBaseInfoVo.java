package com.jd.international.domain.product;

import com.jd.international.soa.base.skuSale.SkuSaleStateResp;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商品基本信息vo类
 *
 * <AUTHOR>
 * @since 2022/11/1 10:07
 */
@Data
public class ProductBaseInfoVo {

    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品单价
     */
    private BigDecimal price;

    /**
     * 币种
     * */
    private String currency;

    /**
     * 多币种价格
     */
    private Map<String,ProductPricesVO> currenciesPrices;

    /**
     * 默认币种价格
     */
    private ProductPricesVO showCurrency;

    /**
     * 商品图片列表
     */
    private List<SkuImageVo> imageDTOList;

    /**
     * 品牌信息
     */
    private BrandInfoVo brandInfoVo;


    /**
     * 当前商品最低起售数量
     */
    private Integer lowestBuy;

    /**
     * 商品主图
     */
    private String imagePath;

    /**
     * 赠品附件
     */
    private Map<String, Map<String,String>> gifts;
    /**
     * 可售状态
     */
    private SkuSaleStateResp skuSaleState;

    /**
     * 商品结构
     */
    private ProductPriceVo productPriceVo;

    /**
     * 最小起订量
     */
    private Integer moq;

    /**
     * 最小起订量文案
     */
    private String moqText;

    /**
     * 剩余库存
     */
    private Integer remainNum;
    /**
     * 货期
     */
    private String deliveryDate;
    /**
     * 所属国家
     */
    private String sourceCountryCode;

    /**
     * 库存详细状态
     */
    private Integer stockFlag;

    /** 库存状态 : 33有货、34无货 39 在途有货*/
    private Integer stockStateType;


    /**
     * 标识商品是否可售的状态
     */
    private Boolean isAvailableSale;

    /**
     * 型号，没有多语言，只展示默认信息
     */
    private String model;

    /**
     * 销售单位，多语言类型，展示当前语言
     */
    private String unit;
}
