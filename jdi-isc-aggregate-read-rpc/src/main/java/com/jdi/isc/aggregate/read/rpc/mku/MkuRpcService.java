package com.jdi.isc.aggregate.read.rpc.mku;


import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuReqDTO;
import com.jdi.isc.product.soa.api.mku.req.IscMkuAvailableSaleReq;
import com.jdi.isc.product.soa.api.mku.res.IscMkuAvailableSaleResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuResDTO;

import java.util.Map;

/**
 * <AUTHOR>
 * @description：MKU服务
 * @Date 2025-05-14
 */
public interface MkuRpcService {

    /**
     * 根据请求查询多个MKU的可用销售信息。
     * @param req IscMkuAvailableSaleReq对象，包含查询条件。
     * @return Map对象，key为MKU的ID，value为对应的IscMkuAvailableSaleResDTO对象，表示MKU的可用销售信息。
     */
    Map<Long, IscMkuAvailableSaleResDTO> queryMkuAvailable(IscMkuAvailableSaleReq req);


    /**
     * 根据批量查询请求获取ISC MKU资源映射信息。
     * @param req 批量查询MKU资源映射信息的请求对象。
     * @return ISC MKU资源映射信息的Map集合，键为MKU ID，值为对应的IscMkuResDTO对象。
     */
    Map<Long, IscMkuResDTO> getIscMku(BatchQueryMkuReqDTO req);
}
