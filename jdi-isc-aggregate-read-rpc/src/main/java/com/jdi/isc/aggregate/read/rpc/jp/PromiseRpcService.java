package com.jdi.isc.aggregate.read.rpc.jp;

import com.alibaba.fastjson.JSON;
import com.jd.jp.strategy.sdk.dto.SkuDetailedPageInfoReq;
import com.jd.jp.strategy.sdk.dto.SkuDetailedPageInfoResp;
import com.jd.jp.strategy.sdk.service.PromiseSdkService;
import com.jd.jp.strategy.sdk.vo.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 金鹏时效类远程服务
 * <AUTHOR>
 * @date 20240508
 */
@Service
@Slf4j
public class PromiseRpcService {

    @Resource
    private PromiseSdkService promiseSdkService;

//    https://cf.jd.com/pages/viewpage.action?pageId=766926862

    //获取商详页货期信息(商详页话术)
    public SkuDetailedPageInfoResp getSkuDetailedPageInfo(SkuDetailedPageInfoReq input){
        RpcResult<SkuDetailedPageInfoResp> res = null;
        try {
            res = promiseSdkService.getSkuDetailedPageInfo(input);
            if(res.isSuccess()){
                return res.getData();
            }
        }finally {
            log.info("PromiseRpcService.getSkuDetailedPageInfo req:{} , res:{}" , JSON.toJSONString(input), JSON.toJSONString(res));
        }
        return null;
    }

//    //获取商详页货期信息(商详页话术)
//    public Map<Long,SkuPromsieResp> batchGetSkuPromise(SkuPromiseReq req){
//
//        RpcResult<List<SkuPromsieResp>> res = null;
//        try {
//            res = promiseSdkService.batchGetSkuPromise(req);
//            if(res.isSuccess()){
//                return res.getData().stream().collect(Collectors.toMap(SkuPromsieResp::getSkuId, Function.identity()));
//            }
//        }finally {
//            log.info("PromiseRpcService.batchGetSkuPromise req:{} , res:{}" , JSON.toJSONString(req), JSON.toJSONString(res));
//        }
//        return null;
//    }

}
