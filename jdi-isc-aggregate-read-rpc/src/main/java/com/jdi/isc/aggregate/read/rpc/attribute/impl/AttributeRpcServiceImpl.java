package com.jdi.isc.aggregate.read.rpc.attribute.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.jd.fastjson.JSONObject;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.aggregate.read.common.costants.UmpKeyConstant;
import com.jdi.isc.aggregate.read.rpc.attribute.AttributeRpcService;
import com.jdi.isc.product.soa.api.attribute.IscAttributeReadApiService;
import com.jdi.isc.product.soa.api.attribute.common.AttributeDTO;
import com.jdi.isc.product.soa.api.attribute.res.AttributeFlatDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class AttributeRpcServiceImpl implements AttributeRpcService {

//    @Resource
//    private IscAttributeReadApiService iscAttributeReadApiService;
//
//    @Value("${spring.profiles.active}")
//    private String systemProfile;
//
//    @Override
//    public List<AttributeFlatDTO> querySellAttrDetail(Long categoryId,String lang){
//        List<AttributeFlatDTO> result = Lists.newArrayList();
//        try {
//            log.info("AttributeRpcServiceImpl.querySellAttrDetail categoryId:{},lang:{}",categoryId,lang);
//            DataResponse<List<AttributeFlatDTO>> resp = iscAttributeReadApiService.querySellAttrDetail(categoryId,lang);
//            if(resp.getSuccess()){
//                return resp.getData();
//            }
//        }catch (Exception e){
//            log.error("【系统异常】AttributeRpcServiceImpl.querySellAttrDetail error:{}", e.getMessage(), e);
//            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_OPEN_WARNING, String.format("【%s】 %s 获取销售属性及属性值信息报错, 异常信息:%s"
//                    , systemProfile
//                    , LevelCode.P1.getMessage()
//                    , e.getMessage()));
//        } finally {
//            log.info("AttributeRpcServiceImpl.querySellAttrDetail res:{}" , JSON.toJSONString(result));
//        }
//        return result;
//    }
//
//    @Override
//    public List<AttributeFlatDTO> queryExtAttrDetail(Long categoryId,String lang){
//        List<AttributeFlatDTO> result = Lists.newArrayList();
//        try {
//            log.info("AttributeRpcServiceImpl.queryExtAttrDetail categoryId:{},lang:{}",categoryId,lang);
//            DataResponse<List<AttributeFlatDTO>> resp = iscAttributeReadApiService.queryExtAttrDetail(categoryId,lang);
//            if(resp.getSuccess()){
//                return resp.getData();
//            }
//        }catch (Exception e){
//            log.error("【系统异常】AttributeRpcServiceImpl.queryExtAttrDetail error:{}", e.getMessage(), e);
//            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_OPEN_WARNING, String.format("【%s】 %s 获取扩展属性及属性值信息报错, 异常信息:%s"
//                    , systemProfile
//                    , LevelCode.P3.getMessage()
//                    , e.getMessage()));
//        } finally {
//            log.info("AttributeRpcServiceImpl.queryExtAttrDetail res:{}" , JSON.toJSONString(result));
//        }
//        return result;
//    }
//
//    @Override
//    public Map<Integer, Map<String, String>> getExtAttrLangMapById(Set<Integer> attributeIds, Set<String> langList) {
//        Map<Integer, Map<String, String>> result = Maps.newHashMap();
//        try {
//            log.info("AttributeRpcServiceImpl.getExtAttrLangMapById categoryId:{},lang:{}", JSONObject.toJSONString(attributeIds),JSONObject.toJSONString(langList));
//            DataResponse<Map<Integer, Map<String, String>>> resp = iscAttributeReadApiService.getExtAttrLangMapById(attributeIds,langList);
//            if(resp.getSuccess()){
//                return resp.getData();
//            }
//        }catch (Exception e){
//            log.error("【系统异常】AttributeRpcServiceImpl.getExtAttrLangMapById error:{}", e.getMessage(), e);
//            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_OPEN_WARNING, String.format("【%s】 %s 获取扩展属性多语报错, 异常信息:%s"
//                    , systemProfile
//                    , LevelCode.P3.getMessage()
//                    , e.getMessage()));
//        } finally {
//            log.info("AttributeRpcServiceImpl.getExtAttrLangMapById res:{}" , JSON.toJSONString(result));
//        }
//        return result;
//    }
//
//    @Override
//    public Map<Integer, Map<String, String>> getExtAttrValueLangMapById(Set<Integer> attributeValueIds, Set<String> langList) {
//        Map<Integer, Map<String, String>> result = Maps.newHashMap();
//        try {
//            log.info("AttributeRpcServiceImpl.getExtAttrValueLangMapById categoryId:{},lang:{}", JSONObject.toJSONString(attributeValueIds),JSONObject.toJSONString(langList));
//            DataResponse<Map<Integer, Map<String, String>>> resp = iscAttributeReadApiService.getExtAttrValueLangMapById(attributeValueIds,langList);
//            if(resp.getSuccess()){
//                return resp.getData();
//            }
//        }catch (Exception e){
//            log.error("【系统异常】AttributeRpcServiceImpl.getExtAttrValueLangMapById error:{}", e.getMessage(), e);
//            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_OPEN_WARNING, String.format("【%s】 %s 获取扩展属性值多语报错, 异常信息:%s"
//                    , systemProfile
//                    , LevelCode.P3.getMessage()
//                    , e.getMessage()));
//        } finally {
//            log.info("AttributeRpcServiceImpl.getExtAttrValueLangMapById res:{}" , JSON.toJSONString(result));
//        }
//        return result;
//    }

}
