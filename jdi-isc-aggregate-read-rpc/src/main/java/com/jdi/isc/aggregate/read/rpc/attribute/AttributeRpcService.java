package com.jdi.isc.aggregate.read.rpc.attribute;

import com.jdi.isc.product.soa.api.attribute.res.AttributeFlatDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AttributeRpcService {

//
//    /**
//     * 根据类别ID和语言查询销售属性详细信息。
//     * @param categoryId 类别ID
//     * @param lang 语言代码
//     * @return 包含AttributeFlatDTO列表的DataResponse对象
//     */
//    List<AttributeFlatDTO> querySellAttrDetail(Long categoryId, String lang);
//
//    /**
//     * 根据类目ID和语言查询扩展属性详细信息。
//     * @param categoryId 类目ID
//     * @param lang 语言
//     * @return 扩展属性详细信息列表
//     */
//    List<AttributeFlatDTO> queryExtAttrDetail(Long categoryId,String lang);
//
//    /**
//     * 根据指定的属性ID集合和语言代码集合获取扩展属性的语言映射。
//     * @param attributeIds 属性ID集合
//     * @param langList 语言代码集合
//     * @return 属性ID到语言代码到扩展属性值的映射
//     */
//    Map<Integer, Map<String,String>> getExtAttrLangMapById(Set<Integer> attributeIds,Set<String> langList);
//
//    /**
//     * 根据指定的属性值ID集合和语言列表，获取扩展属性值的语言映射。
//     * @param attributeValueIds 属性值ID集合
//     * @param langList 语言列表
//     * @return 扩展属性值的语言映射，Map<Integer, Map<String,String>>，其中key为属性值ID，value为该属性值在不同语言下的名称映射
//     */
//    Map<Integer, Map<String,String>> getExtAttrValueLangMapById(Set<Integer> attributeValueIds,Set<String> langList);
}
