package com.jdi.isc.aggregate.read.rpc.mku;


import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.mku.IscProductSoaMkuReadApiService;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuReqDTO;
import com.jdi.isc.product.soa.api.mku.req.IscMkuAvailableSaleReq;
import com.jdi.isc.product.soa.api.mku.res.IscMkuAvailableSaleResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description：MkuRpcServiceImpl
 * @Date 2025-05-14
 */
@Slf4j
@Service
public class MkuRpcServiceImpl implements MkuRpcService{

    @Resource
    private IscProductSoaMkuReadApiService iscProductSoaMkuReadApiService;

    @Override
    public Map<Long, IscMkuAvailableSaleResDTO> queryMkuAvailable(IscMkuAvailableSaleReq req) {

        DataResponse<Map<Long, IscMkuAvailableSaleResDTO>> response = null;
        try{
            response = iscProductSoaMkuReadApiService.queryMkuAvailable(req);
            if (Objects.nonNull(response) && response.getSuccess()) {
                return response.getData();
            }
        } finally {
            log.info("MkuRpcServiceImpl.queryMkuAvailable 查询mku可采信息 入参:[{}],出参:[{}]", JSON.toJSONString(req),JSON.toJSONString(response));
        }
        return Collections.emptyMap();
    }

    @Override
    public Map<Long, IscMkuResDTO> getIscMku(BatchQueryMkuReqDTO req) {

        DataResponse<Map<Long, IscMkuResDTO>> response = null;
        try{
            response = iscProductSoaMkuReadApiService.getIscMku(req);
            if (Objects.nonNull(response) && response.getSuccess()) {
                return response.getData();
            }
        } finally {
            log.info("MkuRpcServiceImpl.getIscMku 查询mku可采信息 入参:[{}],出参:[{}]", JSON.toJSONString(req),JSON.toJSONString(response));
        }
        return Collections.emptyMap();
    }
}
