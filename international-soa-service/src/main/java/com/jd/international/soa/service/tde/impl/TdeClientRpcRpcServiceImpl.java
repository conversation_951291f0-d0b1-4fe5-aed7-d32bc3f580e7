package com.jd.international.soa.service.tde.impl;

import com.jd.international.soa.service.tde.TdeClientRpcService;
import com.jd.security.tdeclient.TDEClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Slf4j
@Service("tdeClientRpcService")
public class TdeClientRpcRpcServiceImpl implements TdeClientRpcService {

    @Qualifier("tdeClient")
    @Autowired
    private TDEClient tdeClient;

    @Value("${rpc.tde.indexSalt}")
    private String indexSalt;

    /**
     * 加密
     * @param str
     * @return
     */
    @Override
    public String encrypt(String str) {
        String res = null;
        try {
            if(StringUtils.isBlank(str)){
                return null;
            }
            res = this.tdeClient.encryptString(str);
        } catch (Exception exp) {
            log.error("encrypt error: ", exp);
        }
        return res;
    }

    /**
     * 解密.
     * @param str 需要解密的字符串
     * @return 解密后的字符串
     */
    @Override
    public String decrypt(String str) {
        String res = null;
        try {
            if(StringUtils.isBlank(str)){
                return null;
            }
            if (this.tdeClient.isDecryptable(str) == TDEClient.CipherStatus.Decryptable) {
                res = this.tdeClient.decryptString(str);
            } else {
                log.error("decrypt failed");
            }
        } catch (Exception exp) {
            log.error("decrypt error: ", exp);
        }
        return res;
    }

    /**
     * 索引字符串，可用于存储至索引列。
     * @param str
     * @return
     */
    @Override
    public String calculateStringIndex(String str) {
        String res = null;
        try {
            if(StringUtils.isBlank(str)){
                return null;
            }
            res = this.tdeClient.calculateStringIndex(str.getBytes(StandardCharsets.UTF_8), indexSalt.getBytes(StandardCharsets.UTF_8));
        } catch (Exception exp) {
            log.error("encrypt error: ", exp);
        }
        return res;
    }

    @Override
    public boolean isDecryptable(String str) {
        if(StringUtils.isBlank(str)){
            return false;
        }
        TDEClient.CipherStatus decryptable = tdeClient.isDecryptable(str);
        return decryptable == TDEClient.CipherStatus.Decryptable;
    }
}
