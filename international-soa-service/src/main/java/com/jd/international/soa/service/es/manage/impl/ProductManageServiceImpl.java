package com.jd.international.soa.service.es.manage.impl;

import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.InnerHitsResult;
import co.elastic.clients.json.JsonData;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.international.soa.common.ducc.DuccConfig;
import com.jd.international.soa.common.ducc.biz.ChatGptProperties;
import com.jd.international.soa.common.utils.ConfigUtils;
import com.jd.international.soa.common.utils.S3Utils;
import com.jd.international.soa.domain.enums.YnEnum;
import com.jd.international.soa.domain.es.EsConstants;
import com.jd.international.soa.domain.es.EsSearchRequest;
import com.jd.international.soa.domain.es.ProductEsPO;
import com.jd.international.soa.domain.promise.DeliveryTemplate;
import com.jd.international.soa.domain.promise.JdSkuDetailPromiseReqVO;
import com.jd.international.soa.domain.promise.PromiseInfo;
import com.jd.international.soa.domain.wares.product.MkuContextDTO;
import com.jd.international.soa.rpc.attribute.AttributeRpcService;
import com.jd.international.soa.rpc.category.CategoryRpcService;
import com.jd.international.soa.rpc.currency.CurrencyRpcService;
import com.jd.international.soa.rpc.customer.RpcCustomerService;
import com.jd.international.soa.rpc.isc.mku.RpcMkuService;
import com.jd.international.soa.rpc.lang.LangRpcService;
import com.jd.international.soa.rpc.price.PriceRpcReadService;
import com.jd.international.soa.rpc.promise.PromiseApiRpcService;
import com.jd.international.soa.rpc.stock.StockRpcService;
import com.jd.international.soa.sdk.common.common.ProductPricesDTO;
import com.jd.international.soa.sdk.common.customer.res.CustomerDTO;
import com.jd.international.soa.sdk.common.customer.res.IopAddressVO;
import com.jd.international.soa.sdk.common.enums.tag.MkuFeatureTagEnum;
import com.jd.international.soa.sdk.common.isc.brand.domain.biz.IscBrandClientDTO;
import com.jd.international.soa.sdk.common.isc.category.domain.biz.IscCategoryTreeNodeDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.*;
import com.jd.international.soa.service.adapter.customer.CustomerConvert;
import com.jd.international.soa.service.adapter.delivery.DeliveryConvert;
import com.jd.international.soa.service.chatGpt.ChatGptService;
import com.jd.international.soa.service.common.cache.JimCacheService;
import com.jd.international.soa.service.es.atomic.ProductEsAtomicService;
import com.jd.international.soa.service.es.manage.ProductManageService;
import com.jd.jp.strategy.sdk.dto.Address;
import com.jd.jp.strategy.sdk.dto.SkuDetailedPageInfoResp;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.tp.common.masterdata.UniformBizInfoBuilder;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.aggregate.read.wisp.api.delivery.resp.DeliveryAgingResp;
import com.jdi.isc.library.common.exception.BizI18nException;
import com.jdi.isc.library.common.response.ResponseCode;
import com.jdi.isc.product.soa.api.attribute.common.AttributeDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeLangDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeValueDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeValueLangDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.MkuQueryEnum;
import com.jdi.isc.product.soa.api.common.enums.MkuQueryLangsEnum;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuLangsReqDTO;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuReqDTO;
import com.jdi.isc.product.soa.api.mku.req.IscMkuAvailableSaleReq;
import com.jdi.isc.product.soa.api.mku.res.IscMkuAvailableSaleResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuLangsResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuResDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.CategoryBatchReqApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.res.CategoryPathNameDTO;
import com.jdi.isc.product.soa.price.api.clientPrice.req.MkuClientPriceReqApiDTO;
import com.jdi.isc.product.soa.price.api.clientPrice.res.MkuClientPriceResApiDTO;
import com.jdi.isc.product.soa.stock.mku.res.IscMkuStockResDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 客户端ES相关服务接口
 * @Author: zhaokun51
 * @Date: 2025/06/25 15:29
 **/
@Slf4j
@Service
public class ProductManageServiceImpl implements ProductManageService {

    @Resource
    private ProductEsAtomicService productEsAtomicService;
    @Resource
    private DuccConfig duccConfig;
    @Resource
    private ChatGptService chatGptService;
    @Resource
    private JimCacheService jimCacheService;
    @Resource
    private RpcMkuService rpcMkuService;
    @Resource
    private PriceRpcReadService priceRpcReadService;
    @Resource
    private StockRpcService stockRpcService;
    @Resource
    private CurrencyRpcService currencyRpcService;
    @Resource
    private LangRpcService langRpcService;
    @Resource
    private RpcCustomerService rpcCustomerService;
    @Resource
    private PromiseApiRpcService promiseApiRpcService;
    @Resource
    private CategoryRpcService categoryRpcService;
    @Resource
    private AttributeRpcService attributeRpcService;
    @Resource
    private S3Utils s3Utils;


    @LafValue("jdi.isc.agg.delivery.text")
    private String deliveryText;
    @LafValue("jdi.isc.wisp.detail.count")
    private Integer count;
    /**
     * 默认广州仓地址，能配送到当前仓的品才可以销售到泰国
     */
    @LafValue("jdi.isc.iopPin.store.addressMapping")
    private String defaultAddr;
    @Value("${spring.profiles.active}")
    private String active;
    @Value("${config.mkuContext}")
    private String mkuContext;


    private final static String JINMEN_CLIENT_CODE = "xsb9Oiq1Tx7jWveKg2MI";

    @Override
    public List<IscMkuClientDTO> listSimpleInfo(IscMkuClientListReqDTO input) {
        log.info("listSimpleInfo params: {}", input);
        Set<Long> mkuIds = input.getMkuIds();
        String clientCode = input.getClientCode();
        String lang = input.getLang();

        if (StringUtils.isBlank(clientCode) || CollectionUtils.isEmpty(mkuIds)) {
            log.warn("listSimpleInfo param error. input={}", input);
            return Collections.emptyList();
        }

        // 查询客户
        CustomerDTO customerDTO = CustomerConvert.INSTANCE.readReqToDTO(
                rpcCustomerService.getCustomerByClientCode(clientCode));
        input.setCustomerDTO(customerDTO);

        // 批量依赖数据查询
        BatchQueryMkuReqDTO mkuInput = buildMkuQueryInput(mkuIds, clientCode, lang,Sets.newHashSet(MkuQueryEnum.BASE, MkuQueryEnum.EXTEND));
        Map<Long, IscMkuResDTO> iscMkuResDTOMap = rpcMkuService.getIscMku(mkuInput);
        if (MapUtils.isEmpty(iscMkuResDTOMap)) {
            log.warn("ProductManageService.listSimpleInfo, iscMkuResDTOMap is empty. country={}, input={}",
                    customerDTO.getCountry(), mkuInput);
            return Collections.emptyList();
        }

        IscMkuAvailableSaleReq saleReq = this.buildMkuAvailable(mkuIds,clientCode);
        Map<Long, IscMkuAvailableSaleResDTO> mkuSaleStatusMap = rpcMkuService.getMkuAvailable(saleReq);

        Map<Long, Long> mkuSkuMap = rpcMkuService.getIscSkuIdByMkuId(mkuInput);

        BatchQueryMkuLangsReqDTO langInput = buildMkuLangsQueryInput(
                mkuIds, clientCode, lang, Sets.newHashSet(MkuQueryLangsEnum.BASE, MkuQueryLangsEnum.SELL_ATTRIBUTE));
        Map<Long, IscMkuLangsResDTO> iscMkuLangsResDTOMap = rpcMkuService.getIscMkuLangs(langInput);

        Map<Long, Integer> mkuMoqMap = iscMkuResDTOMap.entrySet().stream()
                .filter(e -> e.getValue() != null)
                .collect(Collectors.toMap(Map.Entry::getKey,
                        e -> e.getValue().getMoq() != null ? e.getValue().getMoq() : YnEnum.YES.getCode()
                ));

        Map<Long, IscMkuStockResDTO> stockMap = stockRpcService.buildStockMap(clientCode, mkuIds, mkuMoqMap, mkuSkuMap);

        MkuClientPriceReqApiDTO priceInput = buildPriceQueryInput(clientCode, mkuIds);
        Map<Long, MkuClientPriceResApiDTO> priceMap = priceRpcReadService.getPriceMap(priceInput);

        Map<String, String> currencySymbolMap = currencyRpcService.getCurrencySymbol();

        Map<Long, Boolean> spuSkuRelationMap = rpcMkuService.getIscSpuSkuRelationByMkuId(mkuInput);

        List<IscMkuClientDTO> results = new ArrayList<>();
        for (Long mkuId : mkuIds) {
            IscMkuResDTO mkuResDTO = iscMkuResDTOMap.get(mkuId);
            if (mkuResDTO == null) {
                log.error("ProductManageService.listSimpleInfo iscMkuResDTO is null, mkuId:{}", mkuId);
                continue;
            }

            IscMkuClientDTO iscMkuClientDTO = this.buildSingleProductApiDTO(
                    mkuId, null, mkuResDTO, mkuSkuMap, priceMap, stockMap,
                    iscMkuLangsResDTOMap, currencySymbolMap, lang);

            IscMkuAvailableSaleResDTO saleResDTO = mkuSaleStatusMap.get(mkuId);
            boolean isOnSale = saleResDTO.getIsAvailableSale();
            iscMkuClientDTO.setSaleStatus(isOnSale ? YnEnum.YES.getCode() : YnEnum.NO.getCode());
            iscMkuClientDTO.setIsAvailableSale(isOnSale);

            // 如果是本土品且spu->sku 1->1,意为系统聚合逻辑展示的品，则不展示销售属性
            if (!CountryConstant.COUNTRY_ZH.equals(iscMkuClientDTO.getSourceCountryCode())
                    && Boolean.FALSE.equals(spuSkuRelationMap.get(mkuId))) {
                results.add(iscMkuClientDTO);
                continue;
            }

            IscMkuLangsResDTO iscMkuLangsResDTO = iscMkuLangsResDTOMap.get(mkuId);
            if (iscMkuLangsResDTO != null && !CollectionUtils.isEmpty(iscMkuLangsResDTO.getSellAttributeList())) {
                String saleAttrValues = buildSaleAttributeValues(iscMkuLangsResDTO.getSellAttributeList(), lang);
                iscMkuClientDTO.setSaleAttributeValues(saleAttrValues);
            }
            results.add(iscMkuClientDTO);
        }
        return results;
    }

    /**
     * 组装销售属性值字符串
     */
    private String buildSaleAttributeValues(List<AttributeDTO> sellAttributeList, String lang) {
        StringBuilder attrValue = new StringBuilder();
        for (AttributeDTO attributeDTO : sellAttributeList) {
            if (attributeDTO == null || CollectionUtils.isEmpty(attributeDTO.getAttributeValueList())) {
                continue;
            }
            for (AttributeValueDTO attrValueDTO : attributeDTO.getAttributeValueList()) {
                String attrValueName = getAttrValueNameByLang(attrValueDTO.getLangList(), lang);
                if (StringUtils.isNotBlank(attrValueName)) {
                    attrValue.append(attrValueName).append(" ");
                }
            }
        }
        return attrValue.toString().trim();
    }


    @Override
    public PageInfo<IscMkuClientDTO> search(IscMkuClientPageReqDTO input) {
        // todo zhangjin176 查询客户
        CustomerDTO customerDTO = CustomerConvert.INSTANCE.readReqToDTO(rpcCustomerService.getCustomerByClientCode(input.getClientCode()));
        input.setCustomerDTO(customerDTO);
        // 校验传参
        this.checkPageParam(input);

        customerDTO = input.getCustomerDTO();

        Set<Integer> featureTags = transformFeatureTags(input);
        String indexName = isJinMen(customerDTO.getClientCode())
                ? EsConstants.getMkuEsName(CountryConstant.COUNTRY_HK)
                : EsConstants.getMkuEsName(customerDTO.getCountry());

        EsSearchRequest request = EsSearchRequest.builder()
                .keywords(this.wispGenerateWords(input.getKey(), input.getLang()))
                .catIds(input.getCatIds())
                .clientCode(customerDTO.getClientCode())
                .indexName(indexName)
                .index(input.getIndex().intValue())
                .size(input.getSize().intValue())
                .featureTags(featureTags)
                .stockFlag(input.getStockFlag())
                .build();

        SearchResponse<ProductEsPO> response = productEsAtomicService.search(request);

        PageInfo<IscMkuClientDTO> result = new PageInfo<>();
        result.setIndex(input.getIndex());
        result.setSize(input.getSize());
        if(response.hits() == null || response.hits().total() == null || response.hits().total().value() < 1){
            log.info("ProductManageService.page response is null");
            result.setTotal(0L);
            result.setRecords(Collections.emptyList());
            return result;
        }

        result.setTotal(response.hits().total().value());
        result.setRecords(this.buildSearchShowInfo(response
                ,customerDTO, input.getLang()));

        return result;
    }

    @Override
    public IscMkuClientDTO baseInfo(IscMkuClientDetailReqDTO input) {
        // 1. 参数校验
        if (input == null) {
            log.warn("ProductManageService.baseInfo, input or customerDTO is null. input={}", JSONObject.toJSONString(input));
            return null;
        }

        // todo zhangjin176 查询客户
        CustomerDTO customerDTO = CustomerConvert.INSTANCE.readReqToDTO(rpcCustomerService.getCustomerByClientCode(input.getClientCode()));
        String clientCode = customerDTO.getClientCode();

        if (StringUtils.isBlank(customerDTO.getCountry())) {
            log.warn("ProductManageService.baseInfo, customerVO error. clientCode={}, customerVO={}", clientCode, JSONObject.toJSONString(customerDTO));
            return null;
        }

        Long mkuId = input.getMkuId();
        if (mkuId == null) {
            log.warn("ProductManageService.baseInfo, mkuId is null. input={}", JSONObject.toJSONString(input));
            return null;
        }

        // 2. 语言选择（优先用传入的lang，否则用默认中文）
        String lang = langRpcService.getLangCodeList().contains(input.getLang()) ? input.getLang() : LangConstant.LANG_ZH;
        String country = customerDTO.getCountry();

        // 3. ES索引名选择（特殊客户走金门逻辑）
        String indexName = isJinMen(clientCode)
                ? EsConstants.getMkuEsName(CountryConstant.COUNTRY_HK)
                : EsConstants.getMkuEsName(country);

        try {
            // 4. 查询商品基本信息
            String esId = String.join("_", clientCode, mkuId.toString());
            ProductEsPO productEsPO = productEsAtomicService.queryById(indexName, esId);
            if (productEsPO == null) {
                log.warn("ProductManageService.baseInfo, productEsPO is null. mkuId={}", mkuId);
                return null;
            }

            // 5. 构建基础信息列表
            List<IscMkuClientDTO> iscMkuClientDTOS = this.buildBaseInfo(Collections.singletonList(productEsPO), customerDTO, lang);
            this.buildDetailInfo(iscMkuClientDTOS, customerDTO, lang);

            if (CollectionUtils.isEmpty(iscMkuClientDTOS)) {
                log.warn("ProductManageService.baseInfo, iscMkuClientDTOS is null. mkuId={}", mkuId);
                return null;
            }
            IscMkuClientDTO result = iscMkuClientDTOS.get(0);
            List<IscMkuClientPropertyDTO> propertyDTOS = new ArrayList<>();
            if(CountryConstant.COUNTRY_ZH.equals(result.getSourceCountryCode())){
                propertyDTOS = this.getCnSaleName(productEsPO,indexName,result.getCatId(),lang);
            } else {
                propertyDTOS = this.getIntSaleName(productEsPO,indexName,result.getCatId(),lang);
            }

            result.setSaleAttributes(propertyDTOS);

            List<IscMkuClientMkuPropertyValueDTO> iscMkuClientMkuPropertyValueDTOS = this.mergeMkuSaleAttrs(propertyDTOS);
            result.setMkuSaleAttributes(iscMkuClientMkuPropertyValueDTOS);

            result.setSaleAttributeValues(this.getAttributeValueName(mkuId,iscMkuClientMkuPropertyValueDTOS,propertyDTOS));

            result.setMoqText(getMoqText(result.getMoq(), input.getLang()));
            return result;
        } catch (Exception e) {
            log.error("ProductManageService.baseInfo, input={}", JSONObject.toJSONString(input), e);
            String msg = active + LevelCode.P0.getMessage() +
                    "WISP查询商品详情异常, input=" + JSONObject.toJSONString(input);
            Profiler.businessAlarm(
                    "com.jd.international.soa.service.es.manage.ProductManageService.baseInfo." + active, msg);
            return null;
        }
    }

    /**
     * 根据产品信息和其他参数，获取对应的销售属性DTO列表。
     */
    private List<IscMkuClientPropertyDTO> getIntSaleName(ProductEsPO productEsPO, String indexName, Long catId, String lang) {
        // 1. 参数校验和主mkuId有效性校验
        if (productEsPO == null) {
            log.warn("getIntSaleName: productEsPO is null.");
            return Collections.emptyList();
        }
        String clientCode = productEsPO.getClientCode();
        Long mkuId = productEsPO.getMkuId();
        String aggKey = productEsPO.getAggKey();

        // 2. 主mku是否是SPU
        BatchQueryMkuReqDTO mainMkuInput = buildMkuQueryInput(Sets.newHashSet(mkuId), clientCode, lang, Sets.newHashSet(MkuQueryEnum.BASE));
        Map<Long, Boolean> mkuSpuSkuRelationMap = rpcMkuService.getIscSpuSkuRelationByMkuId(mainMkuInput);
        if (MapUtils.isNotEmpty(mkuSpuSkuRelationMap) && Boolean.TRUE.equals(mkuSpuSkuRelationMap.get(mkuId))) {
            return getCnSaleName(productEsPO, indexName, catId, lang);
        }

        // 3. 查询聚合组商品
        List<ProductEsPO> productEsList = productEsAtomicService.queryByClientCodeAndAggKey(indexName, clientCode, aggKey);
        if (CollectionUtils.isEmpty(productEsList)) {
            log.warn("getIntSaleName: productEsList is empty. mkuId={}", mkuId);
            return Collections.emptyList();
        }

        // 限制最大count个
        if (productEsList.size() > count) {
            productEsList = productEsList.subList(0, count);
        }

        // 4. 获取所有相关mkuId
        Set<Long> mkuIds = productEsList.stream()
                .map(ProductEsPO::getMkuId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(mkuIds)) {
            log.warn("getIntSaleName: mkuIds is empty. mkuId={}", mkuId);
            return Collections.emptyList();
        }

        // 5. 查询并构建销售属性DTO映射（图销/文销）
        Map<Integer, IscMkuClientPropertyDTO> propertyDTOMap = buildPropertyDTOMap(catId, lang);

        // 6. 查询mku信息
        BatchQueryMkuReqDTO mkuInput = buildMkuQueryInput(mkuIds, clientCode, lang,Sets.newHashSet(MkuQueryEnum.BASE));
        Map<Long, IscMkuResDTO> iscMkuResDTOMap = rpcMkuService.getIscMku(mkuInput);
        if (MapUtils.isEmpty(iscMkuResDTOMap)) {
            log.warn("getIntSaleName: iscMkuResDTOMap is empty. clientCode={}, input={}", clientCode, JSONObject.toJSONString(mkuInput));
            return Collections.emptyList();
        }

        // 7. 汇总属性值及其mkuId集合
        List<IscMkuResDTO> mkuResDTOS = new ArrayList<>(iscMkuResDTOMap.values());
        GroupResult groupResult = groupByPic(mkuResDTOS);

        Map<String, IscMkuClientPropertyValueDTO> picSaleAttrMap = buildAttrValueMap(groupResult.getPicAttr2mkuIds(), groupResult.getPicAttr2Image());
        Map<String, IscMkuClientPropertyValueDTO> textSaleAttrMap = buildAttrValueMap(groupResult.getTextAttr2mkuIds(), null);

        // 8. 排序及组装属性DTO
        List<IscMkuClientPropertyDTO> result = new ArrayList<>(2);
        IscMkuClientPropertyDTO picPropertyDTO = propertyDTOMap.get(YnEnum.NO.getCode());
        if (picPropertyDTO != null) {
            picPropertyDTO.setPropertyValueDTOS(sortPropertyValueList(picSaleAttrMap.values()));
            result.add(picPropertyDTO);
        }
        IscMkuClientPropertyDTO textPropertyDTO = propertyDTOMap.get(YnEnum.YES.getCode());
        if (textPropertyDTO != null) {
            textPropertyDTO.setPropertyValueDTOS(sortPropertyValueList(textSaleAttrMap.values()));
            result.add(textPropertyDTO);
        }

        return result;
    }

    /**
     * 根据mkuId和属性值列表获取对应的属性值名称。
     * @param mkuId MKU的唯一标识。
     * @param mkuClientMkuPropertyValueDTOS MKU的属性值列表。
     * @param propertyDTOS 属性值的详细信息列表。
     * @return 对应的属性值名称，格式为"图片名称:文字名称"，如果没有匹配的属性值则返回空字符串。
     */
    private String getAttributeValueName(Long mkuId,
                                         List<IscMkuClientMkuPropertyValueDTO> mkuClientMkuPropertyValueDTOS,
                                         List<IscMkuClientPropertyDTO> propertyDTOS) {
        if (mkuId == null || mkuClientMkuPropertyValueDTOS == null || mkuClientMkuPropertyValueDTOS.isEmpty()
                || propertyDTOS == null || propertyDTOS.isEmpty()) {
            return "";
        }

        // 构建属性值ID到DTO的映射
        Map<String, IscMkuClientPropertyValueDTO> propertyValueDTOMap = propertyDTOS.stream()
                .filter(dto -> dto.getPropertyValueDTOS() != null)
                .flatMap(dto -> dto.getPropertyValueDTOS().stream())
                .collect(Collectors.toMap(IscMkuClientPropertyValueDTO::getAttributeValueId, Function.identity(), (a, b) -> a)); // 假设getId()是唯一标识

        // 查找匹配的mkuValue并拼接属性值
        return mkuClientMkuPropertyValueDTOS.stream()
                .filter(mkuValue -> mkuId.equals(mkuValue.getMkuId()))
                .findFirst()
                .map(mkuValue -> {
                    IscMkuClientPropertyValueDTO picPropertyValueDTO = propertyValueDTOMap.get(mkuValue.getPicAttrValueId());
                    IscMkuClientPropertyValueDTO textPropertyValueDTO = propertyValueDTOMap.get(mkuValue.getTextAttrValueId());
                    String picName = picPropertyValueDTO != null ? picPropertyValueDTO.getAttributeValueName() : "";
                    String textName = textPropertyValueDTO != null ? textPropertyValueDTO.getAttributeValueName() : "";
                    if(StringUtils.isBlank(picName) && StringUtils.isBlank(textName)) {
                        return "";
                    } else if(StringUtils.isBlank(picName)) {
                        return textName;
                    } else if(StringUtils.isBlank(textName)) {
                        return picName;
                    }
                    return picName + ":" + textName;
                })
                .orElse("");
    }


    /**
     * 合并图销和文销属性值，生成对应的MkuPropertyValueDTO列表。
     * @param propertyDTOS 图销和文销属性值列表
     * @return 合并后的MkuPropertyValueDTO列表
     */
    private List<IscMkuClientMkuPropertyValueDTO> mergeMkuSaleAttrs(List<IscMkuClientPropertyDTO> propertyDTOS) {
        List<IscMkuClientPropertyValueDTO> picValueDTOList = null;
        List<IscMkuClientPropertyValueDTO> textValueDTOList = null;

        // 1. 从propertyDTOS中找到图销、文销属性值列表
        for (IscMkuClientPropertyDTO propertyDTO : propertyDTOS) {
            if (YnEnum.NO.getCode().equals(propertyDTO.getAttributeInputType())) {
                picValueDTOList = propertyDTO.getPropertyValueDTOS();
            } else if (YnEnum.YES.getCode().equals(propertyDTO.getAttributeInputType())) {
                textValueDTOList = propertyDTO.getPropertyValueDTOS();
            }
        }

        // 防止NPE
        if (picValueDTOList == null) picValueDTOList = Collections.emptyList();
        if (textValueDTOList == null) textValueDTOList = Collections.emptyList();

        // 2. mkuId -> 图销ID
        Map<Long, String> mkuId2PicAttrId = new HashMap<>();
        for (IscMkuClientPropertyValueDTO dto : picValueDTOList) {
            for (Long mkuId : dto.getMkuIds()) {
                mkuId2PicAttrId.put(mkuId, dto.getAttributeValueId());
            }
        }
        // 3. mkuId -> 文销ID
        Map<Long, String> mkuId2TextAttrId = new HashMap<>();
        for (IscMkuClientPropertyValueDTO dto : textValueDTOList) {
            for (Long mkuId : dto.getMkuIds()) {
                mkuId2TextAttrId.put(mkuId, dto.getAttributeValueId());
            }
        }
        // 4. 合并所有mkuId
        Set<Long> allMkuIds = new HashSet<>();
        allMkuIds.addAll(mkuId2PicAttrId.keySet());
        allMkuIds.addAll(mkuId2TextAttrId.keySet());

        // 5. 输出合并结果
        List<IscMkuClientMkuPropertyValueDTO> result = new ArrayList<>();
        for (Long mkuId : allMkuIds) {
            IscMkuClientMkuPropertyValueDTO vo = new IscMkuClientMkuPropertyValueDTO();
            vo.setMkuId(mkuId);
            vo.setPicAttrValueId(mkuId2PicAttrId.get(mkuId));
            vo.setTextAttrValueId(mkuId2TextAttrId.get(mkuId));
            result.add(vo);
        }
        return result;
    }

    /**
     * 辅助方法：将属性值集合映射为DTO map
     */
    private Map<String, IscMkuClientPropertyValueDTO> buildAttrValueMap(Map<String, List<Long>> attr2mkuIds,Map<String, String> attr2ImageMap) {
        Map<String, IscMkuClientPropertyValueDTO> result = new HashMap<>();
        if(attr2mkuIds == null){
            return result;
        }
        attr2mkuIds.forEach((k, v) -> {
            IscMkuClientPropertyValueDTO dto = new IscMkuClientPropertyValueDTO();
            dto.setAttributeValueId(UUID.randomUUID().toString());
            dto.setAttributeValueName(k);
            dto.setMkuIds(v == null ? Collections.emptySet() : new HashSet<>(v));
            if(MapUtils.isNotEmpty(attr2ImageMap) && StringUtils.isNotBlank(attr2ImageMap.get(k))){
                dto.setImagePath(s3Utils.replaceHost(attr2ImageMap.get(k)));
            }
            result.put(k, dto);
        });
        return result;
    }


    /**
     * 按照图片分组，并生成相应的图销和文销属性。
     * @param mkuList 待分组的商品列表
     * @return 分组结果，包含图销属性到商品ID的映射和文销属性到商品ID的映射
     */
    private GroupResult groupByPic(List<IscMkuResDTO> mkuList) {
        // 按图片分组
        Map<String, List<IscMkuResDTO>> groupMap = new LinkedHashMap<>();
        for (IscMkuResDTO dto : mkuList) {
            String key = this.getS3KeyWithoutSuffix(dto.getMainImgUrl());
            String eTag = s3Utils.getETag(key);
            if(StringUtils.isBlank(eTag)){
                eTag = dto.getMainImgUrl();
            }
            groupMap.computeIfAbsent(eTag, k -> new ArrayList<>()).add(dto);
        }

        GroupResult result = new GroupResult();

        for (List<IscMkuResDTO> group : groupMap.values()) {
            if (group.isEmpty()) {
                continue;
            }
            List<String> names = new ArrayList<>();
            for (IscMkuResDTO dto : group) {
                names.add(dto.getMkuName());
            }

            // 最长公共前缀
            int prefix = 0;
            outer: while (true) {
                char ch = 0;
                for (String s : names) {
                    if (prefix >= s.length()) {
                        break outer;
                    }
                    if (ch == 0) {
                        ch = s.charAt(prefix);
                    } else if (ch != s.charAt(prefix)) {
                        break outer;
                    }
                }
                prefix++;
            }
            // 最长公共后缀
            int suffix = 0;
            outer: while (true) {
                char ch = 0;
                for (String s : names) {
                    if (s.length() - 1 - suffix < prefix) {
                        break outer;
                    }
                    char c = s.charAt(s.length() - 1 - suffix);
                    if (ch == 0) {
                        ch = c;
                    } else if (ch != c) {
                        break outer;
                    }
                }
                suffix++;
            }

            // 图销属性
            String picAttr = "";
            if (prefix > 0) {
                picAttr += names.get(0).substring(0, prefix);
            }
            if (suffix > 0) {
                picAttr += names.get(0).substring(names.get(0).length() - suffix);
            }

            // 文销属性
            Map<String, List<Long>> text2ids = new LinkedHashMap<>();
            Map<String, Integer> textCount = new HashMap<>();
            for (int i = 0; i < group.size(); i++) {
                String s = names.get(i);
                String wenAttr = "";
                if (prefix + suffix < s.length()) {
                    wenAttr = s.substring(prefix, s.length() - suffix);
                }
                if (wenAttr.isEmpty()) {
                    wenAttr = picAttr;
                }
                // 同名编号
                int count = textCount.getOrDefault(wenAttr, 0) + 1;
                textCount.put(wenAttr, count);
                String finalWenAttr = wenAttr;
                if (count > 1) {
                    finalWenAttr = wenAttr + count;
                }
                text2ids.computeIfAbsent(finalWenAttr, k -> new ArrayList<>()).add(group.get(i).getMkuId());
            }
            // 合并图销属性
            result.picAttr2mkuIds.put(picAttr, new ArrayList<>());
            for (List<Long> ids : text2ids.values()) {
                result.picAttr2mkuIds.get(picAttr).addAll(ids);
            }
            // 图销属性与图片一对一
            result.picAttr2Image.put(picAttr, group.get(0).getMainImgUrl());
            // 合并文销属性
            for (Map.Entry<String, List<Long>> entry : text2ids.entrySet()) {
                result.textAttr2mkuIds.put(entry.getKey(), entry.getValue());
            }
        }
        return result;
    }

    /**
     * 从给定的 URL 中提取 .com/ 后到 ? 之间的字符串
     * 例如: https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/ps1/TASK21705744812579797.png?
     * 返回: ps1/TASK21705744812579797.png
     * @param url S3 对象的 URL
     * @return .com/ 后到 ? 之间的字符串
     */
    private String getS3KeyWithoutSuffix(String url) {
        if (url == null) {
            return null;
        }
        // 找到 .com/ 的位置
        int comIdx = url.indexOf(".com/");
        if (comIdx == -1) {
            return null;
        }
        int start = comIdx + ".com/".length();

        // 找到 ? 的位置
        int end = url.indexOf('?', start);
        // 如果没有问号，取到字符串结尾
        if (end == -1) {
            end = url.length();
        }

        // 截取 .com/ 后到 ? 之间的内容
        return url.substring(start, end);
    }

    @Data
    class GroupResult {
        /**
         * 图销属性与mkuIds的映射关系。
         */
        private Map<String, List<Long>> picAttr2mkuIds = new LinkedHashMap<>();
        /**
         * 文销属性与mkuIds的映射关系。
         */
        private Map<String, List<Long>> textAttr2mkuIds = new LinkedHashMap<>();
        /**
         * 图销属性与图片URL的映射关系（一对一）。
         */
        private Map<String, String> picAttr2Image = new LinkedHashMap<>();
    }

    /**
     * 获取并设置销售属性名（支持多语言）。
     * 主要流程：
     * 1. 查询聚合组商品，获取所有相关mkuId。
     * 2. 查询销售属性多语言信息。
     * 3. 构建销售属性DTO映射（区分图销/文销）。
     * 4. 汇总各商品销售属性值及其mkuId集合。
     * 5. 对属性值列表排序：先按mkuIds数量降序，再按属性值首字母升序。
     * 6. 组装并返回结果。
     *
     * @param productEsPO 商品ES对象
     * @param indexName ES索引名
     * @param catId 类目ID
     * @param lang 语言
     * @return 含有图销/文销属性及属性值的DTO列表
     */
    private List<IscMkuClientPropertyDTO> getCnSaleName(ProductEsPO productEsPO, String indexName, Long catId,
                                                        String lang) {
        // 参数校验
        if (productEsPO == null) {
            log.warn("ProductManageService.getCnSaleName, productEsPO is null.");
            return Collections.emptyList();
        }

        // 1. 查询聚合组商品
        List<ProductEsPO> productEsList = productEsAtomicService.queryByClientCodeAndAggKey(
                indexName, productEsPO.getClientCode(), productEsPO.getAggKey());
        if (CollectionUtils.isEmpty(productEsList)) {
            log.warn("ProductManageService.getCnSaleName, productEsList is null. mkuId={}", productEsPO.getMkuId());
            return Collections.emptyList();
        }

        // 2. 获取所有相关mkuId
        Set<Long> mkuIds = productEsList.stream().filter(Objects::nonNull).map(ProductEsPO::getMkuId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());;
        if (mkuIds.isEmpty()) {
            return Collections.emptyList();
        }

        BatchQueryMkuReqDTO queryInput = buildMkuQueryInput(mkuIds, productEsPO.getClientCode(), lang, Sets.newHashSet(MkuQueryEnum.BASE));
        Map<Long, IscMkuResDTO> mkuMap = rpcMkuService.getIscMku(queryInput);
        if (mkuMap.isEmpty()) {
            return Collections.emptyList();
        }

        // 3. 查询销售属性多语言信息
        BatchQueryMkuLangsReqDTO langInput = buildMkuLangsQueryInput(
                mkuIds, productEsPO.getClientCode(), lang, Sets.newHashSet(MkuQueryLangsEnum.BASE, MkuQueryLangsEnum.SELL_ATTRIBUTE));
        Map<Long, IscMkuLangsResDTO> mkuLangsMap = rpcMkuService.getIscMkuLangs(langInput);
        if (mkuLangsMap.isEmpty()) {
            return Collections.emptyList();
        }

        // 4. 构建基础数据映射
        Map<Long, String> mkuImageMap = mkuMap.values().stream().filter(Objects::nonNull)
                .filter(dto -> dto.getMkuId() != null).collect(Collectors.toMap(IscMkuResDTO::getMkuId, IscMkuResDTO::getMainImgUrl, (v1, v2) -> v1));
        Map<Integer, IscMkuClientPropertyDTO> propertyDTOMap = this.buildPropertyDTOMap(catId, lang);

        // 5. 处理销售属性
        PropertyAttributeMaps attributeMaps = this.processSaleAttributes(mkuLangsMap, lang);

        // 6. 补充图片信息
        this.supplementImageInfo(attributeMaps.getPicSaleAttrMap(), mkuImageMap);

        // 7. 组装返回结果
        return this.buildResultList(propertyDTOMap, attributeMaps);
    }

    /**
     * 销售属性映射容器
     */
    private static class PropertyAttributeMaps {
        private final Map<String, IscMkuClientPropertyValueDTO> picSaleAttrMap = new HashMap<>();
        private final Map<String, IscMkuClientPropertyValueDTO> textSaleAttrMap = new HashMap<>();

        public Map<String, IscMkuClientPropertyValueDTO> getPicSaleAttrMap() { return picSaleAttrMap; }
        public Map<String, IscMkuClientPropertyValueDTO> getTextSaleAttrMap() { return textSaleAttrMap; }
    }

    /**
     * 处理销售属性
     */
    private PropertyAttributeMaps processSaleAttributes(Map<Long, IscMkuLangsResDTO> mkuLangsMap, String lang) {
        PropertyAttributeMaps attributeMaps = new PropertyAttributeMaps();

        for (Map.Entry<Long, IscMkuLangsResDTO> entry : mkuLangsMap.entrySet()) {
            Long mkuId = entry.getKey();
            IscMkuLangsResDTO langsResDTO = entry.getValue();

            String name = getBestName(langsResDTO.getMkuNames(), lang);
            List<AttributeDTO> attrList = langsResDTO.getSellAttributeList();

            if (CollectionUtils.isEmpty(attrList)) {
                // 属性为空，直接用商品名
                addAttrValue(name, mkuId, attributeMaps.getPicSaleAttrMap());
                addAttrValue(name, mkuId, attributeMaps.getTextSaleAttrMap());
                continue;
            }

            processAttributeList(attrList, name, mkuId, lang, attributeMaps);
        }

        return attributeMaps;
    }

    /**
     * 处理属性列表
     */
    private void processAttributeList(List<AttributeDTO> attrList, String name, Long mkuId, String lang,
                                      PropertyAttributeMaps attributeMaps) {
        Map<Integer, AttributeDTO> saleAttrMap = attrList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        AttributeDTO::getAttributeInputType,
                        Function.identity(),
                        (existing, replacement) -> existing));

        // 获取图销和文销属性
        AttributeDTO picAttr = saleAttrMap.get(YnEnum.NO.getCode());
        AttributeDTO textAttr = saleAttrMap.get(YnEnum.YES.getCode());

        // 处理图销属性（优先使用图销，否则使用文销）
        AttributeDTO picAttrToUse = picAttr != null ? picAttr : textAttr;
        if (picAttrToUse != null) {
            buildAttrValueMap(picAttrToUse, name, mkuId, lang, attributeMaps.getPicSaleAttrMap());
        }

        // 处理文销属性（优先使用文销，否则使用图销）
        AttributeDTO textAttrToUse = textAttr != null ? textAttr : picAttr;
        if (textAttrToUse != null) {
            buildAttrValueMap(textAttrToUse, name, mkuId, lang, attributeMaps.getTextSaleAttrMap());
        }
    }

    /**
     * 补充图片信息
     */
    private void supplementImageInfo(Map<String, IscMkuClientPropertyValueDTO> picSaleAttrMap,
                                     Map<Long, String> mkuImageMap) {
        if (mkuImageMap.isEmpty()) {
            return;
        }

        picSaleAttrMap.values().stream()
                .filter(dto -> StringUtils.isBlank(dto.getImagePath()))
                .forEach(dto -> {
                    dto.getMkuIds().stream()
                            .map(mkuImageMap::get)
                            .filter(StringUtils::isNotBlank)
                            .findFirst()
                            .ifPresent(imageUrl -> dto.setImagePath(s3Utils.replaceHost(imageUrl)));
                });
    }

    /**
     * 构建最终结果列表
     */
    private List<IscMkuClientPropertyDTO> buildResultList(Map<Integer, IscMkuClientPropertyDTO> propertyDTOMap,
                                                          PropertyAttributeMaps attributeMaps) {
        List<IscMkuClientPropertyDTO> resultList = new ArrayList<>();

        // 添加图销属性
        IscMkuClientPropertyDTO picPropertyDTO = propertyDTOMap.get(YnEnum.NO.getCode());
        if (picPropertyDTO != null) {
            picPropertyDTO.setPropertyValueDTOS(sortPropertyValueList(attributeMaps.getPicSaleAttrMap().values()));
            resultList.add(picPropertyDTO);
        }

        // 添加文销属性
        IscMkuClientPropertyDTO textPropertyDTO = propertyDTOMap.get(YnEnum.YES.getCode());
        if (textPropertyDTO != null) {
            textPropertyDTO.setPropertyValueDTOS(sortPropertyValueList(attributeMaps.getTextSaleAttrMap().values()));
            resultList.add(textPropertyDTO);
        }

        return resultList;
    }

    // 优先返回指定语言、英文、中文
    private String getBestName(Map<String, String> nameMap, String lang) {
        if (MapUtils.isEmpty(nameMap)) {
            return "";
        }
        String name = nameMap.get(lang);
        if (StringUtils.isBlank(name)) {
            name = nameMap.get(LangConstant.LANG_EN);
        }
        if (StringUtils.isBlank(name)) {
            name = nameMap.get(LangConstant.LANG_ZH);
        }
        return name != null ? name : "";
    }

    // 简化属性值添加
    private void addAttrValue(String valueName, Long mkuId, Map<String, IscMkuClientPropertyValueDTO> map) {
        if (StringUtils.isBlank(valueName)) {
            return;
        }
        map.computeIfAbsent(valueName, k -> {
            IscMkuClientPropertyValueDTO dto = new IscMkuClientPropertyValueDTO();
            dto.setAttributeValueName(valueName);
            dto.setMkuIds(new HashSet<>());
            return dto;
        }).getMkuIds().add(mkuId);
    }

    /**
     * 构建属性类型到属性DTO的映射（区分图销、文销）
     * @param catId 类目ID
     * @param lang  语言
     * @return Map<Integer, IscMkuClientPropertyDTO>
     */
    private Map<Integer, IscMkuClientPropertyDTO> buildPropertyDTOMap(Long catId, String lang) {
        List<PropertyApiDTO> propertyApiDTOS = attributeRpcService.getCategorySaleAttributesByJdCatId(catId, lang);
        if (CollectionUtils.isEmpty(propertyApiDTOS)) {
            log.error("ProductManageService.buildPropertyDTOMap propertyApiDTOS is null");
            return Collections.emptyMap();
        }

        // 构建属性类型映射
        Map<Integer, PropertyApiDTO> propertyApiDTOMap = propertyApiDTOS.stream()
                .filter(Objects::nonNull)
                .filter(dto -> dto.getAttributeInputType() != null)
                .collect(Collectors.toMap(
                        PropertyApiDTO::getAttributeInputType,
                        dto -> dto,
                        (a, b) -> a
                ));

        Map<Integer, IscMkuClientPropertyDTO> propertyDTOMap = new HashMap<>();

        boolean hasPic = propertyApiDTOMap.containsKey(YnEnum.NO.getCode());
        boolean hasText = propertyApiDTOMap.containsKey(YnEnum.YES.getCode());

        if (hasPic && hasText) {
            // 图销和文销都存在
            PropertyApiDTO picApiDTO = propertyApiDTOMap.get(YnEnum.NO.getCode());
            propertyDTOMap.put(
                    YnEnum.NO.getCode(),
                    createClientPropertyDTO(picApiDTO.getAttributeName(), YnEnum.NO.getCode())
            );
            PropertyApiDTO textApiDTO = propertyApiDTOMap.get(YnEnum.YES.getCode());
            propertyDTOMap.put(
                    YnEnum.YES.getCode(),
                    createClientPropertyDTO(textApiDTO.getAttributeName(), YnEnum.YES.getCode())
            );
        } else if (hasPic) {
            // 只有图销，文销用图销属性名
            PropertyApiDTO picApiDTO = propertyApiDTOMap.get(YnEnum.NO.getCode());
            propertyDTOMap.put(
                    YnEnum.NO.getCode(),
                    createClientPropertyDTO(picApiDTO.getAttributeName(), YnEnum.NO.getCode())
            );
            propertyDTOMap.put(
                    YnEnum.YES.getCode(),
                    createClientPropertyDTO(picApiDTO.getAttributeName(), YnEnum.YES.getCode())
            );
        } else if (hasText) {
            // 只有文销，图销用文销属性名
            PropertyApiDTO textApiDTO = propertyApiDTOMap.get(YnEnum.YES.getCode());
            propertyDTOMap.put(
                    YnEnum.NO.getCode(),
                    createClientPropertyDTO(textApiDTO.getAttributeName(), YnEnum.NO.getCode())
            );
            propertyDTOMap.put(
                    YnEnum.YES.getCode(),
                    createClientPropertyDTO(textApiDTO.getAttributeName(), YnEnum.YES.getCode())
            );
        }

        return propertyDTOMap;
    }

    /**
     * 构建单个属性DTO
     */
    private IscMkuClientPropertyDTO createClientPropertyDTO(String name, Integer inputType) {
        IscMkuClientPropertyDTO dto = new IscMkuClientPropertyDTO();
        dto.setName(name);
        dto.setAttributeInputType(inputType);
        dto.setPropertyValueDTOS(new ArrayList<>());
        return dto;
    }

    /**
     * 处理属性值列表，将其添加到对应的属性值Map中
     *
     * @param attr 属性DTO
     * @param name 兜底名称
     * @param currMkuId 当前mkuId
     * @param lang 语言
     * @param targetMap 目标属性值Map
     */
    private void buildAttrValueMap(AttributeDTO attr, String name, Long currMkuId, String lang,
                                   Map<String, IscMkuClientPropertyValueDTO> targetMap) {
        if (attr == null) {
            log.error("ProductManageService.buildAttrValueMap attr is null");
            return;
        }
        for (AttributeValueDTO attrValueDTO : attr.getAttributeValueList()) {
            String attrValueName = getAttrValueNameByLang(attrValueDTO.getLangList(), lang);
            if (StringUtils.isBlank(attrValueName)) {
                attrValueName = name;
            }
            if (StringUtils.isBlank(attrValueName)) {
                continue;
            }
            targetMap.computeIfAbsent(attrValueName, k -> {
                IscMkuClientPropertyValueDTO dto = new IscMkuClientPropertyValueDTO();
                dto.setAttributeValueId(UUID.randomUUID().toString());
                dto.setAttributeValueName(k);
                dto.setMkuIds(new HashSet<>());
                return dto;
            }).getMkuIds().add(currMkuId);
        }
    }

    /**
     * 属性值列表排序（按mkuIds数量降序，再按属性值首字母升序）
     *
     * @param values 属性值集合
     * @return 排序后的列表
     */
    private List<IscMkuClientPropertyValueDTO> sortPropertyValueList(Collection<IscMkuClientPropertyValueDTO> values) {
        return values.stream()
                .sorted(Comparator.comparingInt((IscMkuClientPropertyValueDTO item) ->
                                item.getMkuIds() == null ? 0 : item.getMkuIds().size())
                        .reversed()
                        .thenComparing(item -> {
                            String name = item.getAttributeValueName();
                            return (name == null || name.isEmpty()) ? "" : name.substring(0, 1).toUpperCase();
                        })
                )
                .collect(Collectors.toList());
    }

    /**
     * 获取属性值名称（多语言优先级：指定语言 > 英文 > 中文）
     *
     * @param langDTOList 属性值多语言列表
     * @param lang 当前语言
     * @return 最优先的属性值名称
     */
    private String getAttrValueNameByLang(List<AttributeValueLangDTO> langDTOList, String lang) {
        Map<String, String> attributeNameMap = Optional.ofNullable(langDTOList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .filter(dto -> dto.getLang() != null && dto.getLangName() != null)
                .collect(Collectors.toMap(
                        AttributeValueLangDTO::getLang,
                        AttributeValueLangDTO::getLangName,
                        (oldValue, newValue) -> newValue
                ));

        if (MapUtils.isEmpty(attributeNameMap)) {
            return "";
        }
        String name = attributeNameMap.get(lang);
        if (StringUtils.isBlank(name)) {
            name = attributeNameMap.get(LangConstant.LANG_EN);
        }
        if (StringUtils.isBlank(name)) {
            name = attributeNameMap.get(LangConstant.LANG_ZH);
        }
        return name != null ? name : "";
    }

    /**
     * 填充商品详情的附加属性
     */
    private void buildDetailInfo(List<IscMkuClientDTO> dtoList, CustomerDTO customerDTO, String lang) {
        if (CollectionUtils.isEmpty(dtoList) || customerDTO == null) {
            return;
        }
        String clientCode = customerDTO.getClientCode();

        // 收集有效的mkuId和catId
        Set<Long> mkuIds = dtoList.stream()
                .filter(Objects::nonNull)
                .map(IscMkuClientDTO::getMkuId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<Long> catIds = dtoList.stream()
                .filter(Objects::nonNull)
                .map(IscMkuClientDTO::getCatId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (mkuIds.isEmpty()) {
            return;
        }

        // 批量查询依赖数据
        IscMkuAvailableSaleReq saleReq = this.buildMkuAvailable(mkuIds,clientCode);
        Map<Long, IscMkuAvailableSaleResDTO> mkuSaleStatusMap = rpcMkuService.getMkuAvailable(saleReq);
        BatchQueryMkuReqDTO mkuInput = this.buildMkuQueryInput(mkuIds, clientCode, lang,Sets.newHashSet(MkuQueryEnum.BASE, MkuQueryEnum.EXTEND));
        Map<Long, Long> mkuJdSkuIdMap = rpcMkuService.getIscJdSkuIdByMkuId(mkuInput);
        Map<Long, DeliveryAgingResp> deliveryAgingRespMap = rpcMkuService.getDeliveryAging(new ArrayList<>(mkuIds), clientCode);
        PromiseInfo promiseInfo = this.buildPromiseInfo(customerDTO, mkuJdSkuIdMap);

        BatchQueryMkuLangsReqDTO langInput = this.buildMkuLangsQueryInput(
                mkuIds, clientCode, lang,
                Sets.newHashSet(MkuQueryLangsEnum.BASE, MkuQueryLangsEnum.DESCRIPTION, MkuQueryLangsEnum.EXTEND_ATTRIBUTE));
        Map<Long, IscMkuLangsResDTO> mkuLangsResDTOMap = rpcMkuService.getIscMkuLangs(langInput);

        Map<Long, CategoryPathNameDTO> categoryPathNameDTOMap = Collections.emptyMap();
        if (!catIds.isEmpty()) {
            CategoryBatchReqApiDTO categoryBatchReqApiDTO = new CategoryBatchReqApiDTO();
            categoryBatchReqApiDTO.setCatIds(catIds);
            categoryBatchReqApiDTO.setLang(lang);
            categoryPathNameDTOMap = categoryRpcService.queryByCategoryIds(categoryBatchReqApiDTO);
        }

        for (IscMkuClientDTO dto : dtoList) {
            if (dto == null || dto.getMkuId() == null) {
                continue;
            }
            Long mkuId = dto.getMkuId();

            // 1. 上下架状态
            IscMkuAvailableSaleResDTO saleResDTO = mkuSaleStatusMap.get(mkuId);
            boolean isOnSale = saleResDTO.getIsAvailableSale();
            dto.setSaleStatus(isOnSale ? YnEnum.YES.getCode() : YnEnum.NO.getCode());
            dto.setIsAvailableSale(saleResDTO.getIsAvailableSale());

            if (isOnSale && promiseInfo != null && MapUtils.isNotEmpty(promiseInfo.getDeliveryDateMap())) {
                Long jdSkuId = mkuJdSkuIdMap.get(mkuId);
                Integer deliveryDays = promiseInfo.getDeliveryDateMap().get(jdSkuId);
                if (deliveryDays != null) {
                    DeliveryTemplate template = promiseInfo.getDeliveryTemplate();
                    String templateStr = deliveryDays >= template.getThreshold() ?
                            DeliveryTemplate.getByLang(template.getFutureDelivery(), lang) :
                            DeliveryTemplate.getByLang(template.getNoFutureDelivery(), lang);
                    dto.setDeliveryDate(String.format(templateStr, deliveryDays));
                }
                // 交期
                DeliveryAgingResp deliveryAgingResp = deliveryAgingRespMap.get(mkuId);
                dto.setSkuDeliveryAging(DeliveryConvert.INSTANCE.resp2Dto(deliveryAgingResp));
            }

            // 多语言描述和扩展属性
            IscMkuLangsResDTO mkuLangs = mkuLangsResDTOMap.get(mkuId);
            if (mkuLangs != null) {
                // 描述
                if (CollectionUtils.isNotEmpty(mkuLangs.getDesc())) {
                    mkuLangs.getDesc().stream()
                            .filter(d -> lang.equals(d.getLang()))
                            .findFirst()
                            .ifPresent(d -> dto.setDescription(d.getPcDescription()));
                }
                // 扩展属性
                if (CollectionUtils.isNotEmpty(mkuLangs.getExtentAttributeList())) {
                    dto.setExtentAttributes(this.convertAttribute(mkuLangs.getExtentAttributeList(), lang));
                }
            }

            // 分类路径
            CategoryPathNameDTO categoryPathNameDTO = categoryPathNameDTOMap.get(dto.getCatId());
            if (categoryPathNameDTO != null) {
                dto.setCatePath(this.convertCategoryNode(categoryPathNameDTO));
            }
        }
    }

    /**
     * 将AttributeDTO列表转换为IscMkuClientPropertyDTO列表，根据指定语言筛选属性值。
     * @param attributeDTOS 属性DTO列表
     * @param lang 语言代码
     * @return 转换后的IscMkuClientPropertyDTO列表
     */
    private List<IscMkuClientPropertyDTO> convertAttribute(List<AttributeDTO> attributeDTOS, String lang) {
        if (CollectionUtils.isEmpty(attributeDTOS)) {
            return Collections.emptyList();
        }

        List<IscMkuClientPropertyDTO> resList = new ArrayList<>(attributeDTOS.size());
        for (AttributeDTO attributeDTO : attributeDTOS) {
            if (attributeDTO == null || CollectionUtils.isEmpty(attributeDTO.getAttributeValueList())) {
                continue;
            }

            // 拼接属性值
            String values = attributeDTO.getAttributeValueList().stream()
                    .filter(Objects::nonNull)
                    .flatMap(valueDTO -> valueDTO.getLangList().stream())
                    .filter(langDTO -> lang.equals(langDTO.getLang()))
                    .map(AttributeValueLangDTO::getLangName)
                    .collect(Collectors.joining(" "));

            // 获取属性名
            String propertyName = attributeDTO.getLangList().stream()
                    .filter(langDTO -> lang.equals(langDTO.getLang()))
                    .map(AttributeLangDTO::getLangName)
                    .findFirst()
                    .orElse(null);

            IscMkuClientPropertyDTO clientPropertyVO = new IscMkuClientPropertyDTO();
            clientPropertyVO.setName(propertyName);
            clientPropertyVO.setValues(values);
            clientPropertyVO.setShield(attributeDTO.getShield());
            clientPropertyVO.setIsQuJianZhi(attributeDTO.getIsQuJianZhi());
            clientPropertyVO.setComGroupId(attributeDTO.getComGroupId());
            Map<String, String> langComGroupNameMap = attributeDTO.getLangComGroupNameMap();
            if (langComGroupNameMap != null) {
                clientPropertyVO.setComGroupName(langComGroupNameMap.get(lang));
            }
            resList.add(clientPropertyVO);
        }
        return resList;
    }

    /**
     * 将CategoryPathNameDTO对象转换为IscCategoryTreeNodeDTO对象列表。
     * @param categoryPathNameDTO 需要转换的CategoryPathNameDTO对象。
     * @return 转换后的IscCategoryTreeNodeDTO对象列表。
     */
    private List<IscCategoryTreeNodeDTO> convertCategoryNode(CategoryPathNameDTO categoryPathNameDTO){
        List<IscCategoryTreeNodeDTO> results = new ArrayList<>();

        IscCategoryTreeNodeDTO categoryTreeNodeDTO1 = new IscCategoryTreeNodeDTO();
        categoryTreeNodeDTO1.setId(categoryPathNameDTO.getId1());
        categoryTreeNodeDTO1.setName(categoryPathNameDTO.getName1());
        results.add(categoryTreeNodeDTO1);

        IscCategoryTreeNodeDTO categoryTreeNodeDTO2 = new IscCategoryTreeNodeDTO();
        categoryTreeNodeDTO2.setId(categoryPathNameDTO.getId2());
        categoryTreeNodeDTO2.setName(categoryPathNameDTO.getName2());
        results.add(categoryTreeNodeDTO2);

        IscCategoryTreeNodeDTO categoryTreeNodeDTO3 = new IscCategoryTreeNodeDTO();
        categoryTreeNodeDTO3.setId(categoryPathNameDTO.getId3());
        categoryTreeNodeDTO3.setName(categoryPathNameDTO.getName3());
        results.add(categoryTreeNodeDTO3);

        if(categoryPathNameDTO.getId4() != null){
            IscCategoryTreeNodeDTO categoryTreeNodeDTO4 = new IscCategoryTreeNodeDTO();
            categoryTreeNodeDTO4.setId(categoryPathNameDTO.getId4());
            categoryTreeNodeDTO4.setName(categoryPathNameDTO.getName4());
            results.add(categoryTreeNodeDTO4);
        }
        return results;
    }

    /**
     * 构建基本产品信息列表。
     * @param productEsPOS 产品信息列表
     * @param customerDTO 客户信息
     * @param lang 语言代码
     * @return 基本产品信息列表
     */
    private List<IscMkuClientDTO> buildBaseInfo(List<ProductEsPO> productEsPOS, CustomerDTO customerDTO, String lang) {
        if (CollectionUtils.isEmpty(productEsPOS)) {
            log.warn("ProductManageService.buildBaseInfo productEsPOS is null or empty");
            return Collections.emptyList();
        }

        // 收集所有mkuId
        Set<Long> allMkuIds = productEsPOS.stream()
                .map(ProductEsPO::getMkuId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (allMkuIds.isEmpty()) {
            log.info("ProductManageService.buildBaseInfo no mkuIds found");
            return Collections.emptyList();
        }

        // 批量依赖数据查询
        String clientCode = customerDTO.getClientCode();
        BatchQueryMkuReqDTO mkuInput = buildMkuQueryInput(allMkuIds, clientCode, lang, Sets.newHashSet(MkuQueryEnum.BASE, MkuQueryEnum.EXTEND));

        Map<Long, IscMkuResDTO> iscMkuResDTOMap = rpcMkuService.getIscMku(mkuInput);
        if (MapUtils.isEmpty(iscMkuResDTOMap)) {
            log.warn("ProductManageService.buildBaseInfo, iscMkuResDTOMap is empty. country={}, input={}", customerDTO.getCountry(), JSONObject.toJSONString(mkuInput));
            return Collections.emptyList();
        }

        Map<Long, Long> mkuSkuMap = rpcMkuService.getIscSkuIdByMkuId(mkuInput);

        BatchQueryMkuLangsReqDTO langInput = buildMkuLangsQueryInput(
                allMkuIds, clientCode, lang, Sets.newHashSet(MkuQueryLangsEnum.BASE));
        Map<Long, IscMkuLangsResDTO> iscMkuLangsResDTOMap = rpcMkuService.getIscMkuLangs(langInput);

        Map<Long, Integer> mkuMoqMap = iscMkuResDTOMap.entrySet()
                .stream().filter(e -> e.getValue() != null).collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> { return e.getValue().getMoq() != null ? e.getValue().getMoq() : YnEnum.YES.getCode();}
                ));

        Map<Long, IscMkuStockResDTO> stockMap = stockRpcService.buildStockMap(clientCode, allMkuIds, mkuMoqMap, mkuSkuMap);

        MkuClientPriceReqApiDTO priceInput = buildPriceQueryInput(clientCode, allMkuIds);
        Map<Long, MkuClientPriceResApiDTO> priceMap = priceRpcReadService.getPriceMap(priceInput);

        Map<String, String> currencySymbolMap = currencyRpcService.getCurrencySymbol();

        // 构建主商品
        return productEsPOS.stream()
                .map(esPO -> {
                    Long mkuId = esPO.getMkuId();
                    IscMkuResDTO mainRes = iscMkuResDTOMap.get(mkuId);
                    if (mainRes == null) {
                        log.error("ProductManageService.buildBaseInfo iscMkuResDTO is null, mkuId:{}", mkuId);
                        return null;
                    }
                    return this.buildSingleProductApiDTO(
                            mkuId, esPO, mainRes, mkuSkuMap, priceMap, stockMap, iscMkuLangsResDTOMap, currencySymbolMap, lang);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 构建搜索结果的显示信息。
     * @param response 搜索响应对象，包含了搜索结果的列表。
     * @param customerDTO 客户信息对象，用于获取客户的相关信息。
     * @param lang 语言标识，用于确定显示的语言。
     * @return 构建好的搜索结果显示信息列表。
     */
    private List<IscMkuClientDTO> buildSearchShowInfo(SearchResponse<ProductEsPO> response, CustomerDTO customerDTO, String lang) {
        if (response == null || response.hits() == null || CollectionUtils.isEmpty(response.hits().hits())) {
            log.warn("ProductManageService.buildSearchShowInfo response or hits is null/empty");
            return Collections.emptyList();
        }

        // 主/子商品ID、主PO收集
        Map<Long, List<Long>> mainSubMap = new HashMap<>();
        List<ProductEsPO> allEsPOs = new ArrayList<>();
        List<Long> mainMkuIds = new ArrayList<>();
        this.collectMkuIdsAndAggMap(response.hits().hits(), mainMkuIds, mainSubMap, allEsPOs);

        if (allEsPOs.isEmpty()) {
            log.info("ProductManageService.buildSearchShowInfo no allEsPOs found");
            return Collections.emptyList();
        }

        // 构建所有商品
        List<IscMkuClientDTO> allResults = this.buildBaseInfo(allEsPOs, customerDTO, lang);
        Map<Long, IscMkuClientDTO> mkuIdToDtoMap = allResults.stream()
                .collect(Collectors.toMap(IscMkuClientDTO::getMkuId, Function.identity()));

        // 按mainMkuIds顺序重组商品，设置子商品
        List<IscMkuClientDTO> resultList = mainMkuIds.stream()
                .map(mainId -> {
                    IscMkuClientDTO mainDto = mkuIdToDtoMap.get(mainId);
                    if (mainDto == null) {
                        return null;
                    }

                    // 取出子商品ID列表
                    List<Long> subIds = Optional.ofNullable(mainSubMap.get(mainId))
                            .orElse(Collections.emptyList());

                    List<IscMkuClientDTO> subList = new ArrayList<>();
                    for(Long subMkuId : subIds){
                        IscMkuClientDTO iscMkuClientDTO = mkuIdToDtoMap.get(subMkuId);
                        if(iscMkuClientDTO == null){
                           continue;
                        }
                        subList.add(JSONObject.parseObject(JSONObject.toJSONString(iscMkuClientDTO),IscMkuClientDTO.class));
                    }

                    mainDto.setSubList(subList);

                    return mainDto;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return resultList;
    }

    /**
     * 收集主/子mkuId、aggKeyMap、主PO
     */
    private void collectMkuIdsAndAggMap(List<Hit<ProductEsPO>> hits, List<Long> mainMkuIds
            , Map<Long, List<Long>> mainSunMap, List<ProductEsPO> allEsPOS) {
        for (Hit<ProductEsPO> hit : hits) {
            ProductEsPO mainEsPO = hit.source();
            if (mainEsPO == null) {
                continue;
            }
            Long mainMkuId = mainEsPO.getMkuId();
            mainMkuIds.add(mainMkuId);

            List<Long> subMkuIds = new ArrayList<>();
            Map<String, InnerHitsResult> innerHitsMap = hit.innerHits();
            if (innerHitsMap != null && innerHitsMap.containsKey("agg_key_hits")) {
                InnerHitsResult innerHitsResult = innerHitsMap.get("agg_key_hits");
                for (Hit<JsonData> innerHit : innerHitsResult.hits().hits()) {
                    ProductEsPO subEsPO = innerHit.source().to(ProductEsPO.class);
                    if (subEsPO != null && subEsPO.getMkuId() != null) {
                        subMkuIds.add(subEsPO.getMkuId());
                        allEsPOS.add(subEsPO);
                    }
                }
            }
            mainSunMap.put(mainMkuId, subMkuIds);
        }
    }

    /**
     * 构建MKU可用销售请求对象。
     * @param mkuIds MKU的ID集合。
     * @param clientCode 客户代码。
     * @return IscMkuAvailableSaleReq对象。
     */
    private IscMkuAvailableSaleReq buildMkuAvailable(Set<Long> mkuIds,String clientCode){
        IscMkuAvailableSaleReq saleReq = new IscMkuAvailableSaleReq();
        saleReq.setClientCode(clientCode);
        saleReq.setMkuIds(mkuIds);
        return saleReq;
    }

    /**
     * 构造mku基础查询参数
     */
    private BatchQueryMkuReqDTO buildMkuQueryInput(Set<Long> mkuIds, String clientCode, String lang,Set<MkuQueryEnum> enums) {
        BatchQueryMkuReqDTO input = new BatchQueryMkuReqDTO();
        input.setMkuId(mkuIds);
        input.setClientCode(clientCode);
        input.setQueryEnum(enums);
        input.setUniformBizInfo(new UniformBizInfoBuilder().build());
        input.setLangSet(StringUtils.isBlank(lang) ? Sets.newHashSet(LangConstant.LANG_ZH) : Sets.newHashSet(lang));
        return input;
    }

    /**
     * 构造价格查询参数
     */
    private MkuClientPriceReqApiDTO buildPriceQueryInput(String clientCode, Set<Long> mkuIds) {
        MkuClientPriceReqApiDTO input = new MkuClientPriceReqApiDTO();
        input.setClientCode(clientCode);
        input.setMkuIdList(mkuIds);
        return input;
    }

    /**
     * 构造多语言查询参数
     */
    private BatchQueryMkuLangsReqDTO buildMkuLangsQueryInput(Set<Long> mkuIds, String clientCode, String lang,Set<MkuQueryLangsEnum> enums) {
        BatchQueryMkuLangsReqDTO input = new BatchQueryMkuLangsReqDTO();
        input.setMkuId(mkuIds);
        input.setClientCode(clientCode);
        input.setQueryEnum(enums);
        input.setUniformBizInfo(new UniformBizInfoBuilder().build());
        input.setLangs(StringUtils.isBlank(lang) ?
                Sets.newHashSet(LangConstant.LANG_ZH) :
                Sets.newHashSet(LangConstant.LANG_ZH, LangConstant.LANG_EN, lang));
        return input;
    }

    /**
     * 构建单个产品的 API DTO 对象。
     * @param mkuId 产品的 MKU ID。
     * @param esPO 产品的 ES PO 对象。
     * @param iscMkuResDTO 产品的 ISC MKU 资源 DTO 对象。
     * @param mkuSkuMap 产品的 MKU-SKU 映射表。
     * @param priceMap 产品的价格信息映射表。
     * @param stockMap 产品的库存信息映射表。
     * @param iscMkuLangsResDTOMap 产品的多语言名称信息映射表。
     * @param lang 当前请求的语言。
     * @return 构建好的 ProductApiDTO 对象。
     */
    private IscMkuClientDTO buildSingleProductApiDTO(Long mkuId, ProductEsPO esPO, IscMkuResDTO iscMkuResDTO,
                                                   Map<Long, Long> mkuSkuMap,
                                                   Map<Long, MkuClientPriceResApiDTO> priceMap,
                                                   Map<Long, IscMkuStockResDTO> stockMap,
                                                   Map<Long, IscMkuLangsResDTO> iscMkuLangsResDTOMap,
                                                   Map<String,String> currencySymbolMap,
                                                   String lang) {
        IscMkuClientDTO dto = new IscMkuClientDTO();

        // 基础属性
        dto.setMkuId(mkuId);
        dto.setMkuImage(iscMkuResDTO.getMainImgUrl());
        dto.setCatId(iscMkuResDTO.getCatId());
        dto.setSkuId(mkuSkuMap.get(mkuId));
        dto.setMoq(iscMkuResDTO.getMoq());
        dto.setSourceCountryCode(iscMkuResDTO.getSourceCountryCode());

        // 价格相关
        MkuClientPriceResApiDTO priceResApiDTO = priceMap.get(mkuId);
        if (priceResApiDTO != null) {
            dto.setCurrency(priceResApiDTO.getCurrency());
            dto.setSalePrice(priceResApiDTO.getSalePrice());

            ProductPricesDTO productPricesDTO = this.buildProductPricesDTO(priceResApiDTO,currencySymbolMap);

            if(productPricesDTO != null && StringUtils.isNotBlank(productPricesDTO.getCurrency())){
                dto.setCurrenciesPrices(Collections.singletonMap(productPricesDTO.getCurrency(), productPricesDTO));
            }
            dto.setShowCurrency(JSONObject.parseObject(JSONObject.toJSONString(productPricesDTO),ProductPricesDTO.class));
        }

        // 库存相关
        IscMkuStockResDTO stockResDTO = stockMap.get(mkuId);
        if (stockResDTO != null) {
            dto.setStock(this.buildProductStockResApiDTO(stockResDTO));
        }

        // 多语言名称
        String mkuName = null;
        IscMkuLangsResDTO langsResDTO = iscMkuLangsResDTOMap.get(mkuId);
        if (langsResDTO != null && langsResDTO.getMkuNames() != null) {
            mkuName = langsResDTO.getMkuNames().get(lang);
            if (StringUtils.isBlank(mkuName)) {
                mkuName = langsResDTO.getMkuNames().get(LangConstant.LANG_EN);
            }
        }

        dto.setMkuName(mkuName);

        // 品牌
        IscBrandClientDTO brandApiDTO = new IscBrandClientDTO();
        brandApiDTO.setId(iscMkuResDTO.getBrandId());
        // 设置为当前语音的品牌名称
        if (MapUtils.isNotEmpty(iscMkuResDTO.getBrandNameMap())) {
            String langName = iscMkuResDTO.getBrandNameMap().get(lang);
            brandApiDTO.setName(StringUtils.isNotBlank(langName) ? langName : iscMkuResDTO.getBrandName());
        } else {
            brandApiDTO.setName(iscMkuResDTO.getBrandName());
        }
        dto.setBrand(brandApiDTO);

        dto.setSaleUnit(iscMkuResDTO.getUnit());

        dto.setModel(iscMkuResDTO.getModel());

        // 是否在国家池
        dto.setCountryInPoolFlag(YnEnum.YES.getCode().equals(iscMkuResDTO.getInCountryPool()));

        // 打标
        this.setFeatureTags(esPO, dto);

        return dto;
    }

    /**
     * 构建 ProductPricesDTO 对象。
     * @param priceResApiDTO MkuClientPriceResApiDTO 对象，包含产品价格信息。
     * @return ProductPricesDTO 对象，包含转换后的产品价格信息。
     */
    private ProductPricesDTO buildProductPricesDTO(MkuClientPriceResApiDTO priceResApiDTO,Map<String,String> currencySymbolMap) {
        ProductPricesDTO dto = new ProductPricesDTO();
        dto.setCurrency(priceResApiDTO.getCurrency());
        dto.setCurrencySource(priceResApiDTO.getOriginCurrency());
        dto.setExchangeRate(priceResApiDTO.getExchangeRate());
        dto.setSalePrice(priceResApiDTO.getIncludeTaxPrice());
        dto.setPrice(priceResApiDTO.getSalePrice());
        dto.setShowPrice(priceResApiDTO.getIncludeTaxPrice());
        dto.setIncludeTaxPrice(priceResApiDTO.getIncludeTaxPrice());
        dto.setValueAddedTax(priceResApiDTO.getTaxPrice());
        dto.setValueAddedTaxRate(priceResApiDTO.getValueAddedTax());
        dto.setSymbol(currencySymbolMap.get(priceResApiDTO.getCurrency()));
        return dto;
    }

    /**
     * 构建产品库存响应DTO。
     * @param stockResDTO IscMkuStockResDTO对象，包含库存相关信息。
     * @return ProductStockResApiDTO对象，包含产品库存的详细信息。
     */
    private IscMkuClientStockDTO buildProductStockResApiDTO(IscMkuStockResDTO stockResDTO) {
        IscMkuClientStockDTO dto = new IscMkuClientStockDTO();
        dto.setMkuId(stockResDTO.getMkuId());
        dto.setNum(stockResDTO.getNum() != null ? stockResDTO.getNum().intValue() : -1);
        dto.setTransitStock(stockResDTO.getTransitStock());
        dto.setOccupy(stockResDTO.getOccupy());
        dto.setAvailableStock(stockResDTO.getAvailableStock());
        dto.setOnWaySale(stockResDTO.getOnWaySale());
        dto.setStockStateType(stockResDTO.getStockStateType());
        return dto;
    }

    /**
     * 转换商品标识
     */
    private void setFeatureTags(ProductEsPO esPO, IscMkuClientDTO productApiDTO) {
        if(esPO == null){
            log.info("setFeatureTags.esPO is null");
            return;
        }
        Set<Integer> featureTags = esPO.getFeatureTags();
        if (CollectionUtils.isNotEmpty(featureTags)) {
            Map<String, String> featureMap = new HashMap<>();
            featureTags.forEach(f -> {
                MkuFeatureTagEnum tagEnum = MkuFeatureTagEnum.queryEsCode(f);
                if (Objects.nonNull(tagEnum)) {
                    featureMap.put(tagEnum.getCode(), tagEnum.getDesc());
                }
            });
            productApiDTO.setFeatureTagMap(featureMap);
        }
    }

    /**
     * 同步构建货期时效信息
     */
    private PromiseInfo buildPromiseInfo(CustomerDTO customerDTO, Map<Long, Long> mkuJdSkuIdMap) {
        PromiseInfo promiseInfoResult = new PromiseInfo();
        Set<Long> jdSkuIds = null;
        if(MapUtils.isNotEmpty(mkuJdSkuIdMap) && CollectionUtils.isNotEmpty(mkuJdSkuIdMap.values())){
            jdSkuIds= new HashSet<>(mkuJdSkuIdMap.values());
        }
        if (CollectionUtils.isEmpty(jdSkuIds)) {
            log.info("ProductManageService.buildPromiseInfo, jdSkuIds empty. skip.");
            return promiseInfoResult;
        }
        log.info("ProductManageService.buildPromiseInfo, clientCode={}, jdSkuIds={}", customerDTO.getClientCode(), JSONObject.toJSONString(jdSkuIds));

        Map<Long, Integer> deliveryDateMap = Maps.newHashMapWithExpectedSize(jdSkuIds.size());
        for(Long jdSkuId : jdSkuIds){
            JdSkuDetailPromiseReqVO reqVO = new JdSkuDetailPromiseReqVO(jdSkuId, 1, customerDTO.getClientCode());
            SkuDetailedPageInfoResp promiseInfo = this.getPromiseInfoByJdSkuId(reqVO,customerDTO);
            if (Objects.nonNull(promiseInfo) && Objects.nonNull(promiseInfo.getDeliveryDays())){
                deliveryDateMap.put(jdSkuId, promiseInfo.getDeliveryDays());
            }
        }

        DeliveryTemplate deliveryTemplate = JSONObject.parseObject(deliveryText, DeliveryTemplate.class);
        promiseInfoResult.setDeliveryTemplate(deliveryTemplate);
        promiseInfoResult.setDeliveryDateMap(deliveryDateMap);
        log.info("ProductManageService.buildPromiseInfo, promiseInfoResult={}", JSONObject.toJSONString(promiseInfoResult));
        return promiseInfoResult;
    }

    /**
     * 根据京东商品ID获取承诺信息。
     * @param promiseReqVO 包含京东商品ID和数量的请求对象。
     * @param customerDTO 客户信息对象。
     * @return 承诺信息响应对象。
     */
    private SkuDetailedPageInfoResp getPromiseInfoByJdSkuId(JdSkuDetailPromiseReqVO promiseReqVO,CustomerDTO customerDTO) {
        IopAddressVO addr = this.getCustomerIopAddressVO(customerDTO);
        Address address = this.iopAddressVo2Address(addr);
        SkuDetailedPageInfoResp resp = promiseApiRpcService.processPromiseInfo(promiseReqVO.getJdSkuId(),promiseReqVO.getNum(), address, addr.getIopPin());
        if (resp != null && resp.getDeliveryDays() != null) {
            resp.setDeliveryDays(resp.getDeliveryDays());
        }
        return resp;
    }

    /**
     * 将IopAddressVO对象转换为Address对象。
     * @param iopAddressVO IopAddressVO对象，包含省、市、县、乡镇和详细地址信息。
     * @return 转换后的Address对象。
     */
    private Address iopAddressVo2Address(IopAddressVO iopAddressVO){
        Address address = new Address();
        address.setProvinceId(iopAddressVO.getProvinceId().intValue());
        address.setCityId(iopAddressVO.getCityId().intValue());
        address.setCountyId(iopAddressVO.getCountyId().intValue());
        address.setTownId(iopAddressVO.getTownId().intValue());
        address.setAddress(address.getAddress());
        return address;
    }

    /**
     * 校验搜索时参数
     * */
    private void checkPageParam(IscMkuClientPageReqDTO input){
        if(StringUtils.isBlank(input.getLang())){
            log.error("ProductManageService.checkPageParam lang is null");
            throw new BizI18nException(ResponseCode.COMMON_PARAM_ERROR.getCode());
        }
        if(StringUtils.isBlank(input.getClientCode())){
            log.error("ProductManageService.checkPageParam clientCode is null");
            throw new BizI18nException(ResponseCode.COMMON_PARAM_ERROR.getCode());
        }
        if(Objects.isNull(input.getCustomerDTO())){
            log.error("ProductManageService.checkPageParam customerDTO is null");
            throw new BizI18nException(ResponseCode.COMMON_PARAM_ERROR.getCode());
        }
        if(StringUtils.isBlank(input.getCustomerDTO().getCountry())){
            log.error("ProductManageService.checkPageParam customerDTO.country is null");
            throw new BizI18nException(ResponseCode.COMMON_PARAM_ERROR.getCode());
        }
    }

    /**
     * 转换商品标识
     */
    private  Set<Integer> transformFeatureTags(IscMkuClientPageReqDTO input) {
        Set<String> featureTagStr = input.getFeatureTags();
        Set<Integer> featureTags = new HashSet<>();
        if (CollectionUtils.isNotEmpty(featureTagStr)) {

            featureTagStr.forEach(f -> {
                MkuFeatureTagEnum tagEnum = MkuFeatureTagEnum.queryCode(f);
                if (Objects.nonNull(tagEnum)) {
                    featureTags.add(tagEnum.getEsCode());
                }
            });
        }
        return featureTags;
    }

    /**
     * 判断客户端代码是否为金门。
     * @param clientCode 客户编码。
     * @return true 如果是金门客户端，false 否则。
     */
    private boolean isJinMen(String clientCode) {
        if(StringUtils.equals(JINMEN_CLIENT_CODE,clientCode)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 根据关键词和语言生成相关的单词列表。
     * @param keyword 关键词，用于生成相关单词。
     * @param lang 语言代码，指定生成单词的语言。
     * @return 生成的单词列表。
     */
    private List<String> wispGenerateWords(String keyword, String lang) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        final String cacheKey = buildWispCacheKey(keyword, lang);

        try {
            // 1. Try to fetch from distributed cache first
            String cachedResult = jimCacheService.getStr(cacheKey);
            if (StringUtils.isNotBlank(cachedResult)) {
                log.info("ProductManageService.wispGenerateWords cache hit for key: {} value :{}", cacheKey,cachedResult);
                try {
                    return JSON.parseObject(cachedResult, new TypeReference<List<String>>() {});
                } catch (Exception e) {
                    log.error("ProductManageService.Failed to parse cached wispGenerateWords result. Key: {}, Value: {}", cacheKey, cachedResult, e);
                    // Fallback to calling the service if parsing fails
                }
            }

            // 2. If cache miss, call the AI service
            List<String> generatedWords = callGptForWisp(keyword, lang);
            log.info("ProductManageService.wispGenerateWords cache miss for key: {}. Calling GPT service.generatedWords: {}", cacheKey, JSONObject.toJSONString(generatedWords));
            // 3. Store the result in cache with a 10-minute TTL
            if (CollectionUtils.isNotEmpty(generatedWords)) {
                jimCacheService.setEx(cacheKey, JSON.toJSONString(generatedWords), 1, TimeUnit.HOURS);
            }

            return generatedWords;

        } finally {
            log.info("ProductManageService.wispGenerateWords keyword: {}, lang: {}, total took: {}ms", keyword, lang, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        }
    }

    /**
     * 调用GPT模型获取与关键词相关的词语列表。
     * @param keyword 关键词，不能为空字符串。
     * @param lang 语言类型，用于指定GPT模型。
     * @return 包含原始关键词和由GPT模型生成的相关词语的列表。
     */
    private List<String> callGptForWisp(String keyword, String lang) {
        if (StringUtils.isBlank(keyword)) {
            return new ArrayList<>();
        }
        try {
            String promptTemplate = duccConfig.getWispPrompts();
            String paramContent = String.format(promptTemplate, keyword, lang);
            ChatGptProperties chatGptProperties = duccConfig.preseChatGpt();
            log.info("ProductManageService.callGptForWisp request to GPT. model: {}, prompt: {}", chatGptProperties.getChatGptModel().get(ChatGptProperties.SUBTITLE), paramContent);
            String result = chatGptService.postGpt(paramContent, chatGptProperties.getChatGptModel().get(ChatGptProperties.SUBTITLE));
            log.info("ProductManageService.callGptForWisp response from GPT. keyword: [{}], lang: [{}], result: [{}]", keyword, lang, result);

            if (StringUtils.isNotBlank(result)) {
                List<String> finalWords = new ArrayList<>();
                // 1. 原词，直接放到返回的list的第一位
                finalWords.add(keyword);
                // 2. ai处理完的词可以拆分，追加到后面
                finalWords.addAll(this.processKeyword(result));
                // 3. 去重，保持原始词在第一位
                return new ArrayList<>(new LinkedHashSet<>(finalWords));
            }
        } catch (Exception e) {
            log.error("ProductManageService.callGptForWisp failed, falling back to original keyword. keyword: [{}], lang: [{}].", keyword, lang, e);
        }
        // Fallback: 异常或无结果时，只返回原始关键词
        return Collections.singletonList(keyword);
    }

    /**
     * 构建Wisp缓存键
     * @param keyword 关键字
     * @param lang 语言代码
     * @return Wisp缓存键字符串
     */
    private String buildWispCacheKey(String keyword, String lang) {
        return "jdi:isc:wisp:kw:" + keyword + ":" + lang;
    }

    /**
     * 处理关键字字符串，将其拆分成多个单词，并转换为小写
     *
     * @param keyword 要处理的关键字字符串
     * @return 处理后的关键字列表
     */
    private List<String> processKeyword(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return null;
        }

        return Arrays.stream(keyword.trim().split("\\s+"))
                .map(String::toLowerCase)
                .collect(Collectors.toList());
    }

    /**
     * 获取客户的iop提单地址信息。
     * @param customerDTO 客户信息DTO，包含iopPin和storeId等字段。
     * @return iop提单地址信息VO，若获取失败则返回null。
     */
    private IopAddressVO getCustomerIopAddressVO(CustomerDTO customerDTO) {
        try {
            if (Objects.isNull(customerDTO)) {
                return null;
            }

            IopAddressVO iopAddressVO =customerDTO.getIopAddressVO();
            if(iopAddressVO != null){
                return iopAddressVO;
            }

            JSONObject storeMap = ConfigUtils.getValueByKey(defaultAddr, customerDTO.getIopPin(), JSONObject.class);
            String addressList = storeMap.getString(String.valueOf(customerDTO.getStoreId()));
            if (StringUtils.isBlank(addressList)) {
                return null;
            }

            List<IopAddressVO> addressInfos = JSONArray.parseArray(addressList, IopAddressVO.class);
            iopAddressVO = addressInfos.get(0);
            iopAddressVO.setIopPin(customerDTO.getIopPin());
            return iopAddressVO;
        } catch (Exception e) {
            log.error("buildIopAddress 构建iop提单地址异常，pin={},storeId={} e:", customerDTO.getIopPin(), customerDTO.getStoreId(), e);
            throw new RuntimeException(e);
        }
    }

    private String getMoqText(Integer moq, String lang) {
        if (moq == null) {
            return null;
        }
        JSONObject mkuContextObject = JSONObject.parseObject(mkuContext);
        MkuContextDTO mkuContextDTO = mkuContextObject.getObject(lang, MkuContextDTO.class);
        if (mkuContextDTO == null) {
            mkuContextDTO = mkuContextObject.getObject(LangConstant.LANG_ZH, MkuContextDTO.class);
        }
        return String.format(mkuContextDTO.getMoqText(), moq);
    }

}
