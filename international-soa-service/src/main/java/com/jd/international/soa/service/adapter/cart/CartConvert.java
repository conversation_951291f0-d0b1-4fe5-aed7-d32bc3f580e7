package com.jd.international.soa.service.adapter.cart;

import com.jd.international.soa.sdk.common.cart.req.*;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientListReqDTO;
import com.jdi.isc.aggregate.read.wisp.api.common.BaseReqDTO;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.MkuClientListInfoReqApiDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 购物车对象转换
 *
 * <AUTHOR>
 * @date 2023/11/26
 **/
@Mapper
public interface CartConvert {

    CartConvert INSTANCE = Mappers.getMapper(CartConvert.class);

    BaseReqDTO addCartReq2BaseReqDTO(AddCartReq addCartReq);

    BaseReqDTO cartReq2BaseReqDTO(CartReq cartReq);

    BaseReqDTO updateCartReq2BaseReqDTO(UpdateCartReq updateCartReq);

    BaseReqDTO delCartReq2BaseReqDTO(DelCartReq delCartReq);

    MkuClientListInfoReqApiDTO baseReqDTO2MkuClientListInfoReqApiDTO(BaseReqDTO baseReqDTO);

    IscMkuClientListReqDTO baseReqDTO2MkuReqApiDTO(BaseReqDTO input);

    BaseReqDTO batchCartReq2BaseReqDTO(BatchCartReq cartReq);
}
