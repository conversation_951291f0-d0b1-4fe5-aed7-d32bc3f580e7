package com.jd.international.soa.service.common.cache;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2013-09-05
 */

public interface JimCacheService {

    /* 公用方法，不区分String和Object */

    /**
     * return the type of the value stored at key in form of a string. The type
     * can be one of "none", "string", "list", "set". "none" is returned if the
     * key does not exist.
     * @param key
     * @return 数据的类型
     */
    String type(String key);

    /**
     * 通过key删除数据
     * @param key
     * @return 删除的数目
     */
    Long delete(String key);

    /**
     * 通过key,查询数据是否存在
     * @param key
     * @return 是否存在
     */
    Boolean exists(String key);

    /**
     * 修改redis中缓存的生效时间
     * @param key
     * @param expireTime
     * @param timeUnit
     * @return
     */
    boolean expire(String key,long expireTime,TimeUnit timeUnit);

    /******* String类型的get,set处理 *******/

    /**
     * 设置key的值
     * @param key String类型的key
     * @param value String类型的值
     */
    void setStr(String key, String value);

    /**
     * 设置key的值,寿命为 exp秒
     * @param key String类型的key
     * @param exp 过期时间 ,单位秒
     * @param value String类型的值
     */
    void setStr(String key, int exp, String value);

    /**
     * 通过key获取String对象
     * @param key String类型的key
     * @return String类型的值
     */
    String getStr(String key);

    /**
     * 对key的值增1，如果该值不存在或者不为数字，则默认设置为0，然后再执行增1
     * @param key String类型的key
     * @return
     */
    Long incr(String key);

    /**
     * 对key的值减1，如果该值不存在或者不为数字，则默认设置为0，然后再执行减1
     * @param key String类型的key
     */
    void decr(String key);

    /******* String泛型的 map集合处理 *******/

    /**
     * 设置map的属性值，map的key和value均为String
     * @param mapName map的名称
     * @param field 属性名称 String类型
     * @param value 属性的值 String类型
     */
    void setFieldStr(String mapName, String field, String value);

    /**
     * 获取map中属性的值，map的key和value均为String
     * @param mapName map的名称
     * @param field 属性的名称
     * @return String类型的属性值
     */
    String getFieldStr(String mapName, String field);

    /**
     * 删除 Map中的指定属性，map的key和value均为String
     * @param mapName map的名称
     * @param field 属性的名称
     * @return 1 is success ,0 is no field deleted
     */
    Long delFieldStr(String mapName, String field);

    /**
     * 存在整个map对象，无过期时间，MAP 的key和value都是String
     * @param mapName map的名称
     * @param map map对象，key和value都是String
     */
    void setMapStr(String mapName, Map<String, String> map);

    /**
     * 存在整个map对象，过期时间为exp秒，MAP 的key和value都是String
     * @param mapName map的名称
     * @param exp 过期时间 单位秒
     * @param map map对象，key和value都是String
     */
    void setMapStr(String mapName, int exp, Map<String, String> map);

    /**
     * 获取多个map下参数
     * @param mKey
     * @param key
     * @return
     */
    List<String> hMGet(String mKey, String... key);

    /**
     * 通过mapName获取map对象， 获取key和value都是String的map
     * @param mapName map的名称
     * @return map对象，key和value均为String
     */
    Map<String, String> getMapStr(String mapName);

    /**
     * 查询属性在Map中是否存在，map的key和value均为String
     * @param mapName map名称
     * @param field 属性名称
     * @return 是否存在
     */
    Boolean exsistsFieldStr(String mapName, String field);

    /************** java对象的存储读取 ******************/

    /**
     * 设置key的值为Object对象，寿命为永久
     * @param key key值
     * @param o 存入的对象
     */
    void setObject(String key, Object o);

    /**
     * 设置key的值为Object对象，寿命为exp 秒
     * @param key key值
     * @param exp 过期时间，单位秒
     * @param o 存入的对象
     */
    void setObject(String key, int exp, Object o);

    /**
     * 通过key获取Object对象
     * @param key key值
     * @return 转换为原来的java对象
     */
    Object getObject(String key);

    /**************** java Object类型的Map存储 **********************/

    /**
     * 存入map对象,无过期时间 ， 其中map的key和value均为Object对象
     * @param mapName map名称
     * @param map 存储的map,其中map的key和value均为Object对象
     */
    void setMapObject(String mapName, Map<String, Object> map);

    /**
     * 存入map对象,包含过期时间 ， 其中map的key和value均为Object对象
     * @param mapName map名称
     * @param exp 过期时间 单位秒
     * @param map 存储的map,其中map的key和value均为Object对象
     */
    void setMapObject(String mapName, int exp, Map<String, Object> map);

    /**
     * 获取整个map对象， 其中map的key和value均为Object对象
     * @param mapName map的名称
     * @return map对象，数据已转为存入前的类型
     */
    Map<String, Object> getMapObject(String mapName);

    /**
     * 设置map的属性值， 其中map的key和value均为Object对象
     * @param mapName map名称
     * @param field 属性名称
     * @param object 存入的Object对象
     */
    void setFieldObject(String mapName, String field, Object object);

    /**
     * 获取Map中的属性值， 其中map的key和value均为Object对象
     * @param mapName map的名称
     * @param field 属性名称
     * @return map中的属性值
     */
    Object getFieldObject(String mapName, String field);

    /**
     * 删除 Map中的属性值， 其中map的key和value均为Object对象
     * @param mapName map的名称
     * @param field 属性名称
     * @return 1 is success ,0 is no field deleted
     */
    Long delFieldObject(String mapName, String field);

    /**
     * 查询map中是否存在该属性值
     * @param mapName map名称
     * @param field 属性名称
     * @return 是否存在
     */
    boolean existsFieldObject(String mapName, String field);

    byte[] getBytesInfo(String key);

    /**
     * 若不存在赋值
     * @param key
     * @param value
     * @return
     */
    boolean setNx(String key,String value);

    /**
     * 修改redis中缓存的生效时间 单位：秒
     * @param key
     * @param seconds
     * @return
     */
    boolean expire(String key,long seconds);

    Set<String> getKeys(String key);

    Boolean simpleLock(String key, String requestId,long timeout);

    boolean simpleLockRelease(String key, String requestId);

    Boolean setEx(String key, String value, long timeout, TimeUnit unit);
    }
