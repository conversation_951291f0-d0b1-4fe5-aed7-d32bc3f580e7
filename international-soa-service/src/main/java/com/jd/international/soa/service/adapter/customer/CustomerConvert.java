package com.jd.international.soa.service.adapter.customer;

import com.jd.international.soa.sdk.common.customer.res.CustomerDTO;
import com.jd.international.soa.sdk.common.customer.res.IopAddressVO;
import com.jdi.isc.aggregate.read.wisp.api.address.IopAddressReadResp;
import com.jdi.isc.aggregate.read.wisp.api.customer.res.CustomerReadResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 客户对象转换
 *
 * <AUTHOR>
 * @date 2024/03/14
 **/
@Mapper
public interface CustomerConvert {

    CustomerConvert INSTANCE = Mappers.getMapper(CustomerConvert.class);

    IopAddressVO addressReadReqToDTO(IopAddressReadResp input);

    @Mappings({
            @Mapping(target = "iopAddressVO", source = "iopAddressReadResp"),
    })
    CustomerDTO readReqToDTO(CustomerReadResp input);

}
