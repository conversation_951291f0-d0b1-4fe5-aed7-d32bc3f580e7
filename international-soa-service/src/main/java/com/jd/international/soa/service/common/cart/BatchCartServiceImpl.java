package com.jd.international.soa.service.common.cart;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.jd.international.soa.base.isc.common.IscBaseReqDTO;
import com.jd.international.soa.common.utils.DataTranslationUtils;
import com.jd.international.soa.domain.common.cart.Cart;
import com.jd.international.soa.domain.common.cart.CartWares;
import com.jd.international.soa.domain.stock.ProductStockInfoRes;
import com.jd.international.soa.domain.enums.YnEnum;
import com.jd.international.soa.rpc.isc.mku.RpcMkuService;
import com.jd.international.soa.rpc.product.RpcIscMkuMaterialService;
import com.jd.international.soa.sdk.common.cart.req.AddCartWaresReq;
import com.jd.international.soa.sdk.common.cart.req.BatchCartReq;
import com.jd.international.soa.sdk.common.cart.resp.CartResp;
import com.jd.international.soa.sdk.common.cart.resp.CartWaresResp;
import com.jd.international.soa.sdk.common.common.MultiCurrencyPriceDTO;
import com.jd.international.soa.sdk.common.common.ProductPricesDTO;
import com.jd.international.soa.sdk.common.enums.stock.StockFlagEnum;
import com.jd.international.soa.sdk.common.enums.stock.StockStateTypeEnum;
import com.jd.international.soa.sdk.common.isc.mku.IscMkuClientApiService;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientListReqDTO;
import com.jd.international.soa.sdk.common.material.res.MkuMaterialResDTO;
import com.jd.international.soa.service.adapter.cart.CartConvert;
import com.jd.international.soa.service.adapter.material.MkuMaterialConvert;
import com.jd.international.soa.service.authority.AuthorityService;
import com.jd.international.soa.service.common.currency.CurrencySymbolService;
import com.jd.international.soa.service.es.manage.ProductManageService;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.aggregate.read.wisp.api.common.BaseReqDTO;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.*;
import com.jdi.isc.product.soa.api.material.res.MkuMaterialApiDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Description:
 * @Author: chengliwei7
 * @Date: 2024/9/11 10:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BatchCartServiceImpl implements BatchCartService {
    private final RpcIscMkuMaterialService rpcIscMkuMaterialService;
    private final AuthorityService authorityService;
    private final RpcMkuService rpcMkuService;
    private final CurrencySymbolService currencySymbolService;
    private final IscMkuClientApiService iscMkuClientApiService;
    private final ProductManageService productManageService;

    @Value("${spring.profiles.active}")
    private String active;
    private static final int STOCK_THRESHOLD_VALUE = 200;
    @Override
    public CartResp getBatchCartList(BatchCartReq cartReq) {
        log.info("BatchCartServiceImpl.getBatchCartList cartReq={}", JSON.toJSONString(cartReq));
        BaseReqDTO baseReqDTO = CartConvert.INSTANCE.batchCartReq2BaseReqDTO(cartReq);
        // Cart cart = getCartItem(cartReq.getCode(), cartReq.getPin(), cartReq.getContractNum());
        if (StringUtils.isBlank(baseReqDTO.getClientCode())) {
            log.error("CartServiceImpl.getCartList error, target:{} ", JSON.toJSONString(cartReq));
        }
        CartResp res = packageCartFromAggService(cartReq.getSkus(), baseReqDTO);
        if (res != null) {
            // 填充物料信息
            fillMaterialInfo(baseReqDTO.getClientCode(), res.getCartWares());
        }
        log.info("BatchCartServiceImpl.getBatchCartList normal res = {}", JSON.toJSONString(res));
        return res;
    }

    private CartResp packageCartFromAggService(List<AddCartWaresReq> skus, BaseReqDTO baseReqDTO) {
        log.info("CartServiceImpl.packageCart cart={}, baseReq={}", JSON.toJSONString(skus), JSON.toJSONString(baseReqDTO));
        try {
            int totalCount = 0, totalCheckCount = 0, totalKindCount = 0, totalCheckKindCount = 0;
            BigDecimal totalPrice = BigDecimal.ZERO;

            List<CartWaresResp> cartWaresRespList = new ArrayList<>();
            List<CartWaresResp> notSale = new ArrayList<>();
            List<CartWaresResp> notSaleChecked = new ArrayList<>();
            MultiCurrencyPriceDTO totalCurrencies = new MultiCurrencyPriceDTO();

            if (CollectionUtils.isNotEmpty(skus)) {
                IscMkuClientListReqDTO iscMkuClientListReqDTO = CartConvert.INSTANCE.baseReqDTO2MkuReqApiDTO(baseReqDTO);
                iscMkuClientListReqDTO.setMkuIds(skus.stream().map(sku -> Long.valueOf(sku.getSku())).collect(Collectors.toSet()));
                List<IscMkuClientDTO> iscMkuClientDTOS = productManageService.listSimpleInfo(iscMkuClientListReqDTO);
                Map<Long, IscMkuClientDTO> simpleApiDTOMap = null;
                if (CollectionUtils.isNotEmpty(iscMkuClientDTOS)) {
                    simpleApiDTOMap = iscMkuClientDTOS.stream().filter(Objects::nonNull).collect(Collectors.toMap(IscMkuClientDTO::getMkuId, o -> o));
                }
                List<CartWares> cartWares = skus.stream().map(sku -> {
                    CartWares wares = new CartWares();
                    wares.setSku(sku.getSku());
                    wares.setSkuNum(sku.getNum());
                    return wares;
                }).collect(Collectors.toList());

                //构建可用库存
                buildRemainNum(baseReqDTO.getClientCode(), baseReqDTO.getStationType(), cartWares, simpleApiDTOMap);

                for (int i = 0, size = cartWares != null ? cartWares.size() : 0; i < size; i++) {
                    CartWares wares = cartWares.get(i);
                    CartWaresResp cartWaresResp = packageCartWaresAgg(wares, simpleApiDTOMap, baseReqDTO.getLang());
                    log.info("packageCartFromAggService  cartWaresResp ={}", JSON.toJSONString(cartWaresResp));
                    int saleState = 1;
                    Integer skuNum = cartWaresResp.getSkuNum();
                    if (skuNum == null) {
                        skuNum = 1;
                    }
                    totalCount += skuNum;
                    totalKindCount += 1;
                    // 0:不可售 1：可售
                    if (cartWaresResp.getChecked() && saleState == 1) {
                        totalCheckCount += skuNum;
                        totalCheckKindCount += 1;
                        totalPrice = totalPrice.add(null != cartWaresResp.getNotes() ? cartWaresResp.getNotes() : BigDecimal.ZERO);
                    }
                    if (saleState == 0) {
                        notSale.add(cartWaresResp);
                        if (cartWaresResp.getChecked()) {
                            notSaleChecked.add(cartWaresResp);
                        }
                    } else {
                        cartWaresRespList.add(cartWaresResp);
                    }
                }
                // 总金额多币种设置
                this.convertTotalCurrencies(cartWaresRespList, totalCurrencies);
            }
            if (CollectionUtils.isNotEmpty(notSale)) {
//                if (CollectionUtils.isNotEmpty(notSaleChecked)) {
//                    List<String> notSaleSkuIds = notSaleChecked.stream().filter(not -> (not.getChecked() != null && not.getChecked())
//                                    && (not.getSaleState() != null && not.getSaleState().getSaleState() == 0))
//                            .map(CartWaresResp::getSku).collect(Collectors.toList());
//                    if (CollectionUtils.isNotEmpty(notSaleSkuIds)) {
//                        cartWaresMapper.updateCartWaresChecked(cart.getId(), notSaleSkuIds, false);
//                    }
//                }
                cartWaresRespList.addAll(notSale);
            }
            Cart cart = new Cart();
            cart.setTotalCount(totalCount);
            cart.setTotalCheckCount(totalCheckCount);
            cart.setTotalKindCount(totalKindCount);
            cart.setTotalCheckKindCount(totalCheckKindCount);
            //商品排序
            cartWaresRespList.sort((o1, o2) -> {
                if (o1.getInPool() && !o2.getInPool()) {
                    return -1;
                }
                if (!o1.getInPool() && o2.getInPool()) {
                    return 1;
                }
//                return o2.getAddCartTime().compareTo(o1.getAddCartTime());
                return 0;
            });
            CartResp cartResp = packageCart(cart, cartWaresRespList, totalPrice, totalCurrencies);
            log.info("packageCartFromAggService cartResp={}", JSON.toJSONString(cartResp));
            return cartResp;
        } catch (Exception e) {
            log.error("load cart exception.", e);
        }
        return null;
    }

    /**
     * 包装购物车参数
     */
    private CartResp packageCart(Cart cart, List<CartWaresResp> cartWaresRespList, BigDecimal totalPrice, MultiCurrencyPriceDTO totalCurrencies) {
        CartResp cartResp = new CartResp();
//        cartResp.setId(cart.getId());
//        cartResp.setCode(cart.getCode());
        cartResp.setTotalCount(cart.getTotalCount());
        cartResp.setTotalCheckCount(cart.getTotalCheckCount());
        cartResp.setTotalKindCount(cart.getTotalKindCount());
        cartResp.setTotalCheckKindCount(cart.getTotalCheckKindCount());
        cartResp.setPin(cart.getPin());
//        cartResp.setContractNum(cart.getContractNum());
        cartResp.setTotalPrice(totalPrice);
        cartResp.setCreated(cart.getCreated());
        cartResp.setCartWares(cartWaresRespList);
        cartResp.setTotalPriceCurrencies(totalCurrencies);
        return cartResp;
    }

    private void fillMaterialInfo(String clientCode, List<CartWaresResp> cartWaresRespList) {
        IscBaseReqDTO baseInfo = new IscBaseReqDTO();
        baseInfo.setClientCode(clientCode);

        if (!authorityService.haseMaterialAuthority(baseInfo)) {
            log.info("fillMaterialInfo, no MaterialAuthority. clientCode={}", clientCode);
            return;
        }

        if (CollectionUtils.isEmpty(cartWaresRespList)) {
            log.info("fillMaterialInfo, cartWaresRespList empty. clientCode={}", clientCode);
            return;
        }

        try {
            Set<Long> mkuIds = new HashSet<>();
            cartWaresRespList.forEach(o -> {
                mkuIds.add(Long.parseLong(o.getSku()));
            });

            if (CollectionUtils.isEmpty(mkuIds)) {
                log.info("fillMaterialInfo, mkuIds empty. pin={}", baseInfo.getPin());
                return;
            }

            Map<Long, MkuMaterialApiDTO> mkuMaterialApiDTOMap = rpcIscMkuMaterialService.queryMkuMaterial(new ArrayList<>(mkuIds), clientCode);

            cartWaresRespList.forEach(o -> {
                if (StringUtils.isNotBlank(o.getSku())) {
                    Long skuId = Long.parseLong(o.getSku());
                    if (null != mkuMaterialApiDTOMap.get(skuId)) {
                        MkuMaterialResDTO orderMkuMaterialResDTO = MkuMaterialConvert.INSTANCE.mkuMaterialApiDTO2SoaCommonResDto(mkuMaterialApiDTOMap.get(skuId));
                        o.setMkuMaterial(orderMkuMaterialResDTO);
                    }
                }
            });

        } catch (Exception e) {
            log.error("", e);
        }
    }

    //拼接商品现货库存信息
    private void buildRemainNum(String clientCode, Integer stationType, List<CartWares> cartWares, Map<Long, IscMkuClientDTO> mkuMap) {
        try {
            List<MkuClientStockApiDTO> stockItem = new ArrayList<>();
            for (CartWares ware : cartWares) {
                Long key = Long.valueOf(ware.getSku());

                if (mkuMap.get(key) != null && mkuMap.get(key).getSaleStatus() != null && mkuMap.get(key).getSaleStatus().equals(YnEnum.YES.getCode())) {
                    MkuClientStockApiDTO stockReq = new MkuClientStockApiDTO();
                    stockReq.setNum(ware.getSkuNum());
                    stockReq.setMkuId(key);
                    stockItem.add(stockReq);
                }
            }
            MkuClientStockReqApiDTO req = new MkuClientStockReqApiDTO();
            req.setClientCode(clientCode);
            req.setStationType(stationType);
            req.setStockItem(stockItem);
            List<MkuClientStockApiDTO> res = rpcMkuService.getStock(req);
            if(CollectionUtils.isNotEmpty(res)){
                Map<Long, MkuClientStockApiDTO> map = res.stream().collect(Collectors.toMap(MkuClientStockApiDTO::getMkuId, Function.identity()));
                cartWares.forEach(ware-> {
                    MkuClientStockApiDTO mkuClientStockApiDTO = map.get(Long.valueOf(ware.getSku()));
                    if (mkuClientStockApiDTO != null) {
                        ProductStockInfoRes productStockInfoRes = calculateStock(clientCode, ware, mkuClientStockApiDTO);
                        ware.setRemainNum(productStockInfoRes.getRemainNum());
                        ware.setStockFlag(productStockInfoRes.getStockFlag());
                        ware.setStockStateType(productStockInfoRes.getStockStateType());
                    }
                });
            }
        } catch (Exception e) {
            log.error("CartServiceImpl.buildRemainNum error, target:{} ", JSON.toJSONString(cartWares), e);
//            Profiler.businessAlarm("CartServiceImpl.buildRemainNum", LevelCode.P0.getMessage() + "库存处理异常,请关注: " + JSON.toJSONString(skus));
        }

    }

    private ProductStockInfoRes calculateStock(String clientCode, CartWares cartWares, MkuClientStockApiDTO stock){
        ProductStockInfoRes res = new ProductStockInfoRes();

        if (null==stock || null==stock.getStockStateType() ||
                null== StockStateTypeEnum.forCode(stock.getStockStateType()) ){
            // 数据错误。默认设置为无货
            log.warn("calculateStock, StockState error. clientCode={}, cartWares={}, stock={}"
                    , clientCode, JSONObject.toJSONString(cartWares), JSONObject.toJSONString(stock));
            res.setRemainNum(0);
            res.setStockFlag(StockFlagEnum.OUT_OF_STOCK.getCode());
            res.setStockStateType(StockStateTypeEnum.OUT_OF_STOCK.getCode());

            // 增加预警
            StringBuilder msg = new StringBuilder();
            msg.append(active).append(LevelCode.P1.getMessage()).append("WISP计算库存值异常，未知的库存状态，请关注！")
                    .append(" clientCode=").append(clientCode)
                    .append(" , stock=").append(JSONObject.toJSONString(stock))
                    .append(" , cartWares=").append(JSONObject.toJSONString(cartWares));
            Profiler.businessAlarm("com.jd.international.soa.service.common.cart.CartServiceImpl."+ active, msg.toString());
            return res;
        }

        if (StockStateTypeEnum.OUT_OF_STOCK.getCode().equals(stock.getStockStateType())){
            // 无货
            res.setRemainNum(0);
            res.setStockFlag(StockFlagEnum.OUT_OF_STOCK.getCode());
            res.setStockStateType(stock.getStockStateType());
            return res;
        }

        // 以下为有货逻辑
        if (StockStateTypeEnum.PRE_ORDER.getCode().equals(stock.getStockStateType())){
            log.info("calculateStock, StockStateTypeEnum is PRE_ORDER.clientCode={}, cartWares={}, stock={}"
                    , clientCode, JSONObject.toJSONString(cartWares), JSONObject.toJSONString(stock));
            // 预定状态的直接返回结果，库存值未知。
            res.setRemainNum(-1);
            res.setStockFlag(StockFlagEnum.ENOUGH.getCode());
            res.setStockStateType(stock.getStockStateType());
            return res;
        }

        if (null==stock.getNum() || null==stock.getOccupy()){
            log.warn("calculateStock, stock val null. clientCode={}, cartWares={}, stock={}"
                    , clientCode, JSONObject.toJSONString(cartWares), JSONObject.toJSONString(stock));
            //有货，但库存值未知
            res.setRemainNum(-1);
            res.setStockFlag(StockFlagEnum.ENOUGH.getCode());
            res.setStockStateType(stock.getStockStateType());
            return res;
        }

        if (-1==stock.getNum()){
            // 有货但无具体值。
            res.setRemainNum(-1);
            res.setStockFlag(StockFlagEnum.ENOUGH.getCode());
            res.setStockStateType(stock.getStockStateType());
            return res;
        }

        // 可售库存(纯现货库存)，优先展示纯现货可售库存
        long stockSpotGoods = stock.getNum()-stock.getOccupy().intValue();;
        if (stockSpotGoods >= STOCK_THRESHOLD_VALUE){
            // 超阀值后，不返回具体库存值
            res.setRemainNum(-1);
            res.setStockFlag(StockFlagEnum.ENOUGH.getCode());
            res.setStockStateType(stock.getStockStateType());
            return res;
        }else if (stockSpotGoods>0){
            res.setRemainNum(Long.valueOf(stockSpotGoods).intValue());
            res.setStockFlag(StockFlagEnum.LOW.getCode());
            res.setStockStateType(stock.getStockStateType());
            return res;
        }

        // 可售库存(含在途库存)。纯现货可售库存不足，再展示采购中
        Long availableStock = stock.getAvailableStock();
        boolean stockTrans = stock.getOnWaySale()!=null && stock.getOnWaySale() == 1
                && null!= availableStock && availableStock>0;
        if (stockTrans){
            // 采购中，不返回具体库存值
            res.setRemainNum(-1);
            res.setStockFlag(StockFlagEnum.PURCHASING.getCode());
            res.setStockStateType(stock.getStockStateType());
            return res;
        }

        // 有货情况，不应该出现以下情况
        log.warn("calculateStock, stock not normal. clientCode={}, cartWares={}, stock={}"
                , clientCode, JSONObject.toJSONString(cartWares), JSONObject.toJSONString(stock));
        StringBuilder msg = new StringBuilder();
        msg.append(active).append(LevelCode.P1.getMessage()).append("WISP计算库存值异常，库存值及状态不一致，请关注！")
                .append(" clientCode=").append(clientCode)
                .append(" , stock=").append(JSONObject.toJSONString(stock))
                .append(" , cartWares=").append(JSONObject.toJSONString(cartWares));
        Profiler.businessAlarm("com.jd.international.soa.service.common.cart.CartServiceImpl."+ active, msg.toString());

        res.setRemainNum(-1);
        res.setStockFlag(StockFlagEnum.UNKNOWN.getCode());
        res.setStockStateType(stock.getStockStateType());
        return res;
    }

    private CartWaresResp packageCartWaresAgg(CartWares wares, Map<Long, IscMkuClientDTO> simpleApiDTOMap, String lang) {
        CartWaresResp cartWaresResp = new CartWaresResp();
//        cartWaresResp.setId(wares.getId());
        cartWaresResp.setSku(wares.getSku());
        Integer skuNum = wares.getSkuNum();
        cartWaresResp.setSkuNum(skuNum);
//        cartWaresResp.setAddCartPrice(wares.getAddCartPrice());
//        cartWaresResp.setAddCartTime(wares.getAddCartTime());
        cartWaresResp.setPin(wares.getPin());
        cartWaresResp.setRemainNum(wares.getRemainNum());

        //默认不选中，不在池
        cartWaresResp.setChecked(false);
        cartWaresResp.setInPool(false);
        int lowestBuy = 1;
        if (MapUtils.isNotEmpty(simpleApiDTOMap) && simpleApiDTOMap.get(Long.valueOf(wares.getSku())) != null) {
            IscMkuClientDTO waresMap = simpleApiDTOMap.get(Long.valueOf(wares.getSku()));
            cartWaresResp.setSkuName(waresMap.getMkuName());
            cartWaresResp.setSourceCountryCode(waresMap.getSourceCountryCode());
            cartWaresResp.setSkuImg(waresMap.getMkuImage());
            cartWaresResp.setSaleAttributes(waresMap.getSaleAttributeValues());
            //设置在池
            Boolean saleStatus = waresMap.getSaleStatus() != null && waresMap.getSaleStatus().equals(YnEnum.YES.getCode())?Boolean.TRUE:Boolean.FALSE;
            cartWaresResp.setInPool(saleStatus);
            //在池计算价格
            if (saleStatus && null != waresMap.getShowCurrency() && null != waresMap.getShowCurrency().getIncludeTaxPrice()) {
                //TODO 含税价为空时，做兜底处理，避免空指针异常
                cartWaresResp.setPrice(waresMap.getShowCurrency().getIncludeTaxPrice());
                if (skuNum == null) {
                    skuNum = 1;
                }
                cartWaresResp.setNotes(waresMap.getShowCurrency().getIncludeTaxPrice().multiply(new BigDecimal(skuNum)));
                // 价格
                cartWaresResp.setShowCurrency(this.convertPriceDto(waresMap.getShowCurrency(), currencySymbolService.symbol(waresMap.getCurrency())));
                cartWaresResp.setCurrenciesPrices(this.convertPriceDtoMap(waresMap.getCurrenciesPrices()));
                this.convertWareNotes(cartWaresResp);
                if (wares.getChecked() != null) {
                    cartWaresResp.setChecked(wares.getChecked());
                }
            }
            if (cartWaresResp.getChecked()) {
                //现货库存为0时取消选中
                if (wares.getRemainNum() != null && wares.getRemainNum() == 0) {
                    cartWaresResp.setChecked(false);
                }
            }
            lowestBuy = waresMap.getMoq() != null ? waresMap.getMoq() : lowestBuy;
            cartWaresResp.setMoq(waresMap.getMoq());
            cartWaresResp.setMoqText(iscMkuClientApiService.getMoqText(waresMap.getMoq(), lang));
            cartWaresResp.setUnit(waresMap.getSaleUnit());
        }
        cartWaresResp.setLowestBuy(lowestBuy);
        cartWaresResp.setSaleState(DataTranslationUtils.buildSkuSaleStateResp(null, null));
        cartWaresResp.setMoq(lowestBuy);
        cartWaresResp.setRemainNum(wares.getRemainNum());
        return cartWaresResp;
    }

    private ProductPricesDTO convertPriceDto(ProductPricesDTO pricesDTO, String symbol) {
        ProductPricesDTO productPricesDTO = new ProductPricesDTO();
        productPricesDTO.setSymbol(symbol);
        productPricesDTO.setCurrencySource(pricesDTO.getCurrencySource());
        productPricesDTO.setCurrency(pricesDTO.getCurrency());
        productPricesDTO.setExchangeRate(pricesDTO.getExchangeRate());
        productPricesDTO.setValueAddedTax(pricesDTO.getValueAddedTax());
        productPricesDTO.setValueAddedTaxRate(pricesDTO.getValueAddedTaxRate());
        productPricesDTO.setIncludeTaxPrice(pricesDTO.getIncludeTaxPrice());
        productPricesDTO.setSalePrice(pricesDTO.getIncludeTaxPrice());
        productPricesDTO.setShowPrice(pricesDTO.getIncludeTaxPrice());
        productPricesDTO.setPrice(pricesDTO.getPrice());
        return productPricesDTO;
    }

    private Map<String, ProductPricesDTO> convertPriceDtoMap(Map<String, ProductPricesDTO> map) {
        if (MapUtils.isEmpty(map)) {
            return Collections.emptyMap();
        }

        Map<String, String> symbols = currencySymbolService.symbols(map.keySet());
        Map<String, ProductPricesDTO> resultMap = Maps.newHashMap();
        map.forEach((k, v) -> resultMap.put(k, this.convertPriceDto(v, symbols.get(k))));
        return resultMap;
    }

    private void convertWareNotes(CartWaresResp cartWaresResp) {
        MultiCurrencyPriceDTO multiCurrencyPriceDTO = new MultiCurrencyPriceDTO();
        ProductPricesDTO notesShow = new ProductPricesDTO();
        BeanUtils.copyProperties(cartWaresResp.getShowCurrency(), notesShow);
        Integer skuNums = cartWaresResp.getSkuNum();
        if (skuNums == null) {
            skuNums = 1;
        }
        BigDecimal skuNum = new BigDecimal(skuNums);
        notesShow.setShowPrice(cartWaresResp.getShowCurrency().getShowPrice().multiply(skuNum));
        notesShow.setPrice(cartWaresResp.getShowCurrency().getPrice().multiply(skuNum));
        notesShow.setSalePrice(cartWaresResp.getShowCurrency().getSalePrice().multiply(skuNum));
        notesShow.setValueAddedTax(Objects.nonNull(cartWaresResp.getShowCurrency().getValueAddedTax()) ? cartWaresResp.getShowCurrency().getValueAddedTax().multiply(skuNum) : BigDecimal.ZERO);
        notesShow.setIncludeTaxPrice(cartWaresResp.getShowCurrency().getIncludeTaxPrice().multiply(skuNum));
        multiCurrencyPriceDTO.setShowCurrency(notesShow);

        // 多币种
        Map<String, ProductPricesDTO> currenciesPrices = cartWaresResp.getCurrenciesPrices();
        Map<String, ProductPricesDTO> notesMap = Maps.newHashMap();
        currenciesPrices.forEach((k, v) -> {
            ProductPricesDTO notePriceDTO = new ProductPricesDTO();
            BeanUtils.copyProperties(v, notePriceDTO);
            notePriceDTO.setSalePrice(v.getSalePrice().multiply(skuNum));
            notePriceDTO.setShowPrice(v.getShowPrice().multiply(skuNum));
            notePriceDTO.setPrice(v.getPrice().multiply(skuNum));
            notePriceDTO.setValueAddedTax(Objects.nonNull(v.getValueAddedTax()) ? v.getValueAddedTax().multiply(skuNum) : BigDecimal.ZERO);
            notePriceDTO.setIncludeTaxPrice(v.getIncludeTaxPrice().multiply(skuNum));
            notesMap.put(k, notePriceDTO);
        });
        multiCurrencyPriceDTO.setCurrenciesPrices(notesMap);
        cartWaresResp.setNotesCurrencies(multiCurrencyPriceDTO);
    }

    private void convertTotalCurrencies(List<CartWaresResp> cartWaresRespList, MultiCurrencyPriceDTO totalCurrencies) {
        try {
            // 过滤出被选中的商品
            List<CartWaresResp> filterCartWareList = cartWaresRespList.stream().filter(CartWaresResp::getChecked).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(filterCartWareList)) {
                return;
            }

            ProductPricesDTO totalShowCurrency = new ProductPricesDTO();
            BeanUtils.copyProperties(filterCartWareList.get(0).getShowCurrency(), totalShowCurrency);
            totalShowCurrency.setPrice(BigDecimal.ZERO);
            totalShowCurrency.setShowPrice(BigDecimal.ZERO);
            totalShowCurrency.setSalePrice(BigDecimal.ZERO);
            totalShowCurrency.setValueAddedTax(BigDecimal.ZERO);
            totalShowCurrency.setIncludeTaxPrice(BigDecimal.ZERO);
            Map<String, ProductPricesDTO> totalCurrenciesPrices = new HashMap<>();

            filterCartWareList.forEach(cartWaresResp -> {
                MultiCurrencyPriceDTO notesCurrencies = cartWaresResp.getNotesCurrencies();
                // 当前币种求和
                totalShowCurrency.setPrice(totalShowCurrency.getPrice().add(notesCurrencies.getShowCurrency().getPrice()));
                totalShowCurrency.setShowPrice(totalShowCurrency.getShowPrice().add(notesCurrencies.getShowCurrency().getShowPrice()));
                totalShowCurrency.setSalePrice(totalShowCurrency.getSalePrice().add(notesCurrencies.getShowCurrency().getSalePrice()));
                totalShowCurrency.setValueAddedTax(totalShowCurrency.getValueAddedTax().add(notesCurrencies.getShowCurrency().getValueAddedTax()));
                totalShowCurrency.setIncludeTaxPrice(totalShowCurrency.getIncludeTaxPrice().add(notesCurrencies.getShowCurrency().getIncludeTaxPrice()));

                // 多币种求和
                notesCurrencies.getCurrenciesPrices().forEach((k, v) -> {
                    ProductPricesDTO productPricesDTO = new ProductPricesDTO();
                    if (totalCurrenciesPrices.containsKey(k)) {
                        productPricesDTO = totalCurrenciesPrices.get(k);
                        productPricesDTO.setSalePrice(productPricesDTO.getSalePrice().add(v.getSalePrice()));
                        productPricesDTO.setShowPrice(productPricesDTO.getShowPrice().add(v.getShowPrice()));
                        productPricesDTO.setPrice(productPricesDTO.getPrice().add(v.getPrice()));
                        productPricesDTO.setValueAddedTax(productPricesDTO.getValueAddedTax().add(v.getValueAddedTax()));
                        productPricesDTO.setIncludeTaxPrice(productPricesDTO.getIncludeTaxPrice().add(v.getIncludeTaxPrice()));
                    } else {
                        productPricesDTO = v;
                    }
                    totalCurrenciesPrices.put(k, productPricesDTO);
                });

            });
            totalCurrencies.setShowCurrency(totalShowCurrency);
            totalCurrencies.setCurrenciesPrices(totalCurrenciesPrices);
        } catch (Exception e) {
            log.error("convertTotalCurrencies error,param:{},e:", JSON.toJSONString(cartWaresRespList), e);
            throw new RuntimeException(e);
        }
    }
}
