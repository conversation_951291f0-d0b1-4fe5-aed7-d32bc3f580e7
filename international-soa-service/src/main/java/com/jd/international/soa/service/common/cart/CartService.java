package com.jd.international.soa.service.common.cart;

import com.jd.international.soa.sdk.common.cart.req.AddCartReq;
import com.jd.international.soa.sdk.common.cart.req.CartReq;
import com.jd.international.soa.sdk.common.cart.req.DelCartReq;
import com.jd.international.soa.sdk.common.cart.req.UpdateCartReq;
import com.jd.international.soa.sdk.common.cart.resp.CartResp;

import java.util.List;
import java.util.Set;


public interface CartService {
    /**
     * 获取购物车数量
     * @param cartReq
     * @return
     */
    int getCartNum(CartReq cartReq);

    /**
     * 获取购物车列表
     * @param cartReq
     * @return
     */
    CartResp getCartList(CartReq cartReq);

    /**
     * 添加商品
     * @param addCartReq
     */
    CartResp addProducts(AddCartReq addCartReq);

    /**
     * 修改商品数量
     * @param updateCartReq
     */
    CartResp updateProductNum(UpdateCartReq updateCartReq);

    /**
     * 选中或反选购物车商品
     * @param cartReq
     * @return
     */
    CartResp checkProduct(CartReq cartReq);

    /**
     * 删除商品
     * @param delCartReq
     * @return
     */
    CartResp delProducts(DelCartReq delCartReq);

    /**
     * 替换购物车
     * @param addCartReq
     * @return
     */
    CartResp replaceCart(AddCartReq addCartReq);

    int deleteAll();

//    /**
//     *
//     * @return
//     */
//    Set<Long> queryInPoolMku(AddCartReq cartReq);

    /**
     * 获取可用的MKU ID集合
     * @param cartReq 添加购物车请求对象，包含商品信息和数量等
     * @return 可用的MKU ID集合
     */
    Set<Long> getMkuAvailable(AddCartReq cartReq);
}
