package com.jd.international.soa.service.es.atomic;

import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import jakarta.json.stream.JsonGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;

/**
 * ES序列化反序列化基础实现类
 * <AUTHOR>
 * @date 20240624
 */
@Slf4j
@Service
public class EsAtomicSupportService<T> {

    @Value("${es.maxResultWindow}")
    private String maxResultWindow;

    /**
     * searchRequest 获取请求体
     * */
    protected String getRequestBody(SearchRequest searchRequest){
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        JacksonJsonpMapper mapper = new JacksonJsonpMapper();

        // 使用 JacksonJsonpMapper 进行序列化
        try (JsonGenerator generator = mapper.jsonProvider().createGenerator(outputStream)) {
            mapper.serialize(searchRequest, generator);
        }

        return outputStream.toString();
    }

    /**
     * 深度分页时、最后一页 一直不变
     * */
    protected void setPage(SearchRequest.Builder requestBuilder,Integer index, Integer size){
        int from = 0;
        Integer maxWindow = Integer.parseInt(maxResultWindow);
        if(index * size > maxWindow){
            int finalPage = maxWindow / size;
            int finalSize = maxWindow - (finalPage * size);
            if(finalSize == 0){
                from = (finalPage-1)*size;
            } else {
                from = finalPage * size;
                size = finalSize;
            }
        } else {
            from = (index-1)*size;
        }

        requestBuilder.from(from);
        requestBuilder.size(size);
    }
}
