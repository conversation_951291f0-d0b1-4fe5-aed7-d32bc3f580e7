package com.jd.international.soa.service.es.manage;

import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientDetailReqDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientListReqDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientPageReqDTO;
import com.jdi.common.domain.rpc.bean.PageInfo;

import java.util.List;

/**
 * @Description: 客户端ES相关服务接口
 * @Author: zhaokun51
 * @Date: 2025/06/25 15:29
 **/
public interface ProductManageService {


    /**
     * 根据输入的搜索条件，返回符合条件的产品列表。
     * @param input 搜索条件，包括关键字、分类ID、客户代码、索引、页面大小等。
     * @return 符合条件的产品列表的分页信息。
     */
    List<IscMkuClientDTO> listSimpleInfo(IscMkuClientListReqDTO input);

    /**
     * 根据输入的搜索条件，返回符合条件的产品列表。
     * @param input 搜索条件，包括关键字、分类ID、客户代码、索引、页面大小等。
     * @return 符合条件的产品列表的分页信息。
     */
    PageInfo<IscMkuClientDTO> search(IscMkuClientPageReqDTO input);

    /**
     * 获取ISC MKU客户基本信息
     * @param input ISC MKU客户详细请求DTO
     * @return ISC MKU客户基本信息DTO
     */
    IscMkuClientDTO baseInfo(IscMkuClientDetailReqDTO input);
}
