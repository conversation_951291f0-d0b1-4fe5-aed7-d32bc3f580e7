package com.jd.international.soa.service.order.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.international.soa.base.bean.RPCResult;
import com.jd.international.soa.common.config.UccConfig;
import com.jd.international.soa.common.constants.CommonConstant;
import com.jd.international.soa.common.exception.BizException;
import com.jd.international.soa.common.utils.IPUtils;
import com.jd.international.soa.rpc.config.RpcOrderConfigService;
import com.jd.international.soa.rpc.config.RpcSettleConfigService;
import com.jd.international.soa.rpc.customer.RpcCustomerService;
import com.jd.international.soa.rpc.isc.order.RpcOrderService;
import com.jd.international.soa.sdk.common.cart.req.DelCartReq;
import com.jd.international.soa.sdk.order.order.req.SubmitOrderReq;
import com.jd.international.soa.sdk.order.order.req.SubmitOrderWaresReq;
import com.jd.international.soa.service.adapter.order.OrderConvert;
import com.jd.international.soa.service.common.cart.CartService;
import com.jd.international.soa.service.tde.TdeClientRpcService;
import com.jd.ka.mro.workflow.soa.sdk.vo.req.ConfigurationFunctionReq;
import com.jd.ka.mro.workflow.soa.sdk.vo.req.OrderConfigInfoReq;
import com.jd.ka.mro.workflow.soa.sdk.vo.res.ConfigurationFunctionVO;
import com.jdi.isc.order.center.api.biz.req.SubmitOrderApiReq;
import com.jdi.isc.order.center.api.biz.resp.OrderInfoOrderApiResp;
import com.jdi.isc.order.center.api.constants.OrderSourceCodeConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.jd.international.soa.common.exception.ResponseErrorCode.ORDER_CONFIG_ERROR;

/**
 * <AUTHOR>
 * @date 2024/2/4
 **/
@Slf4j
@Service("tradeService")
@RequiredArgsConstructor
public class TradeServiceAdaptorImpl implements TradeService {

    private final TradeServiceImpl tradeOriginService;

    private final RpcOrderService rpcOrderService;

    private final UccConfig uccConfig;

    private final CartService cartService;

    private final TdeClientRpcService tdeClientRpcService;

    private final RpcOrderConfigService rpcOrderConfigService;

    private final RpcSettleConfigService rpcSettleConfigService;
    private final RpcCustomerService rpcCustomerService;

    @Value("${orderCenter.systemCode}")
    private String systemCode;

    private static final String PUNCH_OUT_SOURCE_TYPE = "punchout";

    @Override
    public RPCResult<String> submit(SubmitOrderReq submitOrderReq) {
        // 校验配置入参
        this.checkConfig(submitOrderReq);
        SubmitOrderApiReq submitOrderApiReq = OrderConvert.INSTANCE.ispOrder2Center(submitOrderReq);
        submitOrderApiReq.setConsignee(tdeClientRpcService.encrypt(submitOrderReq.getConsignee()));
        submitOrderApiReq.setConsigneeAddress(tdeClientRpcService.encrypt(submitOrderReq.getDeliveryAddress()));
        submitOrderApiReq.setConsigneeMobile(tdeClientRpcService.encrypt(submitOrderReq.getDeliveryPhone()));
        submitOrderApiReq.setOrderTime(submitOrderReq.getOrderTime().getTime());
        //处理物料编码
        submitOrderApiReq.setMkuInfoList(OrderConvert.INSTANCE.listIspWare2Center(submitOrderReq.getWaresReqs()));
        submitOrderApiReq.setOrderType(submitOrderReq.getOrderType());
        submitOrderApiReq.setRequestId(UUID.randomUUID().toString());
        submitOrderApiReq.setThirdOrderId(submitOrderReq.getThirdOrderId());

        String sourceType = submitOrderReq.getSourceType();
        String clientStaffId = submitOrderReq.getClientStaffId();
        if (StringUtils.isNotBlank(clientStaffId)) {
            submitOrderApiReq.setSourceCode(sourceType);
            submitOrderApiReq.setSubAccount(clientStaffId);
        } else {
            submitOrderApiReq.setSourceCode(OrderSourceCodeConstant.WISP);
        }
        String targetCountryCode = rpcCustomerService.getCountryCodeByClientCode(submitOrderReq.getClientCode());
        submitOrderApiReq.setTargetCountryCode(targetCountryCode);// ducc取
        if (StringUtils.isBlank(submitOrderApiReq.getConsigneeCountry())) {
            submitOrderApiReq.setConsigneeCountry(targetCountryCode);
        }
        submitOrderApiReq.setLocaleList(Lists.newArrayList(submitOrderReq.getEnv()));
        submitOrderApiReq.setSystemCode(systemCode);
        submitOrderApiReq.setServiceIp(IPUtils.getLocalIp());
        submitOrderApiReq.setUserIp(submitOrderReq.getUserIp());
        // 下单
        RPCResult<OrderInfoOrderApiResp> orderApiRespResult = rpcOrderService.submitOrder(submitOrderApiReq);
        if (!orderApiRespResult.isSuccess()) {
            return RPCResult.error(orderApiRespResult.getCode(), orderApiRespResult.getMessage());
        }
        // 清空购物车
        this.clearCart(submitOrderReq);
        // 记录订单配置信息
        OrderInfoOrderApiResp orderApiResp = orderApiRespResult.getResult();
        this.insertOrderConfig(submitOrderReq, orderApiResp);
        log.info("TradeServiceAdaptorImpl.submit req:{} , res:{}", JSON.toJSONString(submitOrderReq), orderApiResp.getOrderId());
        return RPCResult.success(orderApiResp.getOrderId().toString());
    }

    @Override
    public RPCResult<Void> submitEptOrder(SubmitOrderReq order) {
        return tradeOriginService.submitEptOrder(order);
    }

    @Override
    public RPCResult<Boolean> confirmIopOrder(SubmitOrderReq order) {
        return tradeOriginService.confirmIopOrder(order);
    }

    @Override
    public RPCResult<Void> withdrawEptOrder(SubmitOrderReq order) {
        return tradeOriginService.withdrawEptOrder(order);
    }

    @Override
    public RPCResult<Integer> recoverOrder(SubmitOrderReq orderReq) {
        return tradeOriginService.recoverOrder(orderReq);
    }

    /**
     * 清空购物车
     *
     * @param submitOrderReq
     */
    private void clearCart(SubmitOrderReq submitOrderReq) {
        List<SubmitOrderWaresReq> waresReqs = submitOrderReq.getWaresReqs();
        List<String> skuIds = waresReqs.stream().map(SubmitOrderWaresReq::getSku).collect(Collectors.toList());
        DelCartReq delCartReq = new DelCartReq();
        delCartReq.setPin(submitOrderReq.getPin());
        delCartReq.setCode(submitOrderReq.getCartCode());
        delCartReq.setSkuIds(skuIds);
        cartService.delProducts(delCartReq);
    }

    private void insertOrderConfig(SubmitOrderReq submitOrderReq, OrderInfoOrderApiResp orderApiResp) {
        if (MapUtils.isEmpty(submitOrderReq.getOrderExtInfo()) || !submitOrderReq.getOrderExtInfo().containsKey("settleInfo")) {
            return;
        }

        String settleInfo = submitOrderReq.getOrderExtInfo().get("settleInfo");
        JSONArray array = JSON.parseArray(settleInfo);

        if (Objects.isNull(array) || array.isEmpty()) {
            return;
        }

        List<OrderConfigInfoReq> orderConfigInfoReqList = Lists.newArrayList();
        for (Object object : array) {
            orderConfigInfoReqList.add(this.getOrderConfigInfoReq(submitOrderReq, orderApiResp, (JSONObject) object));
        }

        if (CollectionUtils.isNotEmpty(orderConfigInfoReqList)) {
            rpcOrderConfigService.batchCreateOrderConfigInfo(orderConfigInfoReqList);
        }
    }

    private OrderConfigInfoReq getOrderConfigInfoReq(SubmitOrderReq submitOrderReq, OrderInfoOrderApiResp orderApiResp, JSONObject o) {
        OrderConfigInfoReq orderConfigInfoReq = new OrderConfigInfoReq();
        orderConfigInfoReq.setOrderId(orderApiResp.getOrderId());
        orderConfigInfoReq.setPin(submitOrderReq.getPin());
        orderConfigInfoReq.setContractNum(submitOrderReq.getContractNum());
        orderConfigInfoReq.setFieldName(o.getString("category"));
        orderConfigInfoReq.setFieldValue(o.getString("value"));
        orderConfigInfoReq.setCreateDate(new Date());
        return orderConfigInfoReq;
    }

    private void checkConfig(SubmitOrderReq submitOrderReq) {
        if (MapUtils.isEmpty(submitOrderReq.getOrderExtInfo()) || !submitOrderReq.getOrderExtInfo().containsKey("settleInfo")) {
            return;
        }

        ConfigurationFunctionReq functionReq = new ConfigurationFunctionReq();
        functionReq.setExt1("1");
        functionReq.setContractNum(submitOrderReq.getContractNum());
        functionReq.setModule(CommonConstant.MODULE);
        List<ConfigurationFunctionVO> configurationFunctionVOList = rpcSettleConfigService.queryFunctionListByCondition(functionReq);
        if (CollectionUtils.isEmpty(configurationFunctionVOList)) {
            return;
        }

        String settleInfo = submitOrderReq.getOrderExtInfo().get("settleInfo");
        JSONArray array = JSON.parseArray(settleInfo);

        if (Objects.isNull(array) || array.isEmpty()) {
            throw new BizException(ORDER_CONFIG_ERROR);
        }

        Set<Long> functionIdSet = Sets.newHashSet();
        for (Object o : array) {
            JSONObject config = (JSONObject) o;
            functionIdSet.add(config.getLong("id"));
        }

        for (ConfigurationFunctionVO functionVO : configurationFunctionVOList) {
            if (!functionIdSet.contains(functionVO.getId())) {
                throw new BizException(ORDER_CONFIG_ERROR);
            }
        }
    }
}
