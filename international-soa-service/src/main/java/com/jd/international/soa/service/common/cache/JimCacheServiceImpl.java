package com.jd.international.soa.service.common.cache;

import com.jd.international.soa.common.utils.SerializeUtil;
import com.jd.jim.cli.Cluster;
import com.jd.jim.cli.protocol.DataType;
import com.jd.jim.cli.protocol.ScriptOutputType;
import com.jd.jim.cli.util.SafeEncoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class JimCacheServiceImpl implements JimCacheService {
    public static final String ENCODE = "UTF-8";

    private final Cluster redisClient;

    private static final Logger LOGGER = LoggerFactory.getLogger(JimCacheServiceImpl.class);

    private static final Long RELEASE_SUCCESS = 1L;

    /* 公用方法，不区分String和Object     */

    /**
     * return the type of the value stored at key in form of a string. The type
     * can be one of  "none", "string", "list", "set". "none" is returned if the
     * key does not exist.
     *
     * @param key
     * @return 数据的类型
     */
    @Override
    public String type(String key) {
        DataType type = redisClient.type(key);
        return type.code();
    }

    /**
     * 通过key删除数据
     *
     * @param key
     * @return 删除的数目
     */
    @Override
    public Long delete(String key) {
        return redisClient.del(key);
    }


    /**
     * 通过key,查询数据是否存在
     *
     * @param key
     * @return 是否存在
     */
    @Override
    public Boolean exists(String key) {
        return redisClient.exists(key);
    }

    @Override
    public boolean expire(String key, long expireTime, TimeUnit timeUnit) {
        return redisClient.expire(key, expireTime, timeUnit);
    }

    /******* String类型的get,set处理    *******/


    /**
     * 设置key的值
     *
     * @param key   String类型的key
     * @param value String类型的值
     */
    @Override
    public void setStr(String key, String value) {
        redisClient.set(key, value);
    }


    /**
     * 设置key的值,寿命为 exp秒
     *
     * @param key   String类型的key
     * @param exp   过期时间 ,单位秒
     * @param value String类型的值
     */
    @Override
    public void setStr(String key, int exp, String value) {
        redisClient.setEx(key, value, exp, TimeUnit.SECONDS);
    }


    /**
     * 通过key获取String对象
     *
     * @param key String类型的key
     * @return String类型的值
     */
    @Override
    public String getStr(String key) {
        return redisClient.get(key);
    }


    /**
     * 对key的值增1，如果该值不存在或者不为数字，则默认设置为0，然后再执行增1
     *
     * @param key String类型的key
     * @return
     */
    @Override
    public Long incr(String key) {
        return redisClient.incr(key);
    }


    /**
     * 对key的值减1，如果该值不存在或者不为数字，则默认设置为0，然后再执行减1
     *
     * @param key String类型的key
     */
    @Override
    public void decr(String key) {
        redisClient.decr(key);
    }


    /******* String泛型的 map集合处理    *******/


    /**
     * 设置map的属性值，map的key和value均为String
     *
     * @param mapName map的名称
     * @param field   属性名称 String类型
     * @param value   属性的值 String类型
     */
    @Override
    public void setFieldStr(String mapName, String field, String value) {
        redisClient.hSet(mapName, field, value);
    }


    /**
     * 获取map中属性的值，map的key和value均为String
     *
     * @param mapName map的名称
     * @param field   属性的名称
     * @return String类型的属性值
     */
    @Override
    public String getFieldStr(String mapName, String field) {
        return redisClient.hGet(mapName, field);
    }


    /**
     * 删除 Map中的指定属性，map的key和value均为String
     *
     * @param mapName map的名称
     * @param field   属性的名称
     * @return 1 is success ,0 is no field deleted
     */
    @Override
    public Long delFieldStr(String mapName, String field) {
        return redisClient.hDel(mapName, field);
    }


    /**
     * 存在整个map对象，无过期时间，MAP 的key和value都是String
     *
     * @param mapName map的名称
     * @param map     map对象，key和value都是String
     */
    @Override
    public void setMapStr(String mapName, Map<String, String> map) {
        redisClient.hMSet(mapName, map);
    }


    /**
     * 存在整个map对象，过期时间为exp秒，MAP 的key和value都是String
     *
     * @param mapName map的名称
     * @param exp     过期时间 单位秒
     * @param map     map对象，key和value都是String
     */
    @Override
    public void setMapStr(String mapName, int exp, Map<String, String> map) {
        redisClient.hMSet(mapName, map);
        if (exp > 0) {
            redisClient.expire(mapName, exp, TimeUnit.SECONDS);
        }
    }

    @Override
    public List<String> hMGet(String mKey, String... key) {
        return redisClient.hMGet(mKey,key);
    }

    /**
     * 通过mapName获取map对象， 获取key和value都是String的map
     *
     * @param mapName map的名称
     * @return map对象，key和value均为String
     */
    @Override
    public Map<String, String> getMapStr(String mapName) {
        return redisClient.hGetAll(mapName);
    }


    /**
     * 查询属性在Map中是否存在，map的key和value均为String
     *
     * @param mapName map名称
     * @param field   属性名称
     * @return 是否存在
     */
    @Override
    public Boolean exsistsFieldStr(String mapName, String field) {
        return redisClient.hExists(mapName, field);
    }


    /**************  java对象的存储读取   ******************/

    /**
     * 设置key的值为Object对象，寿命为永久
     *
     * @param key key值
     * @param o   存入的对象
     */
    @Override
    public void setObject(String key, Object o) {
        redisClient.set(this.getBytes(key), SerializeUtil.serialize(o));
    }


    /**
     * 设置key的值为Object对象，寿命为exp 秒
     *
     * @param key key值
     * @param exp 过期时间，单位秒
     * @param o   存入的对象
     */
    @Override
    public void setObject(String key, int exp, Object o) {
        byte[] keyBytes = this.getBytes(key);
        redisClient.setEx(keyBytes, SerializeUtil.serialize(o), exp, TimeUnit.SECONDS);
        redisClient.setEx(keyBytes, SerializeUtil.serialize(o), exp, TimeUnit.SECONDS);
    }


    /**
     * 通过key获取Object对象
     *
     * @param key key值
     * @return 转换为原来的java对象
     */
    @Override
    public Object getObject(String key) {
        byte[] bytes = redisClient.get(this.getBytes(key));
        LOGGER.info("key"+key);
        if (bytes != null && bytes.length != 0) {
            return SerializeUtil.unSerialize(bytes);
        }
        return null;
    }


    /****************   java Object类型的Map存储**********************/


    /**
     * 存入map对象,无过期时间 ，    其中map的key和value均为Object对象
     *
     * @param mapName map名称
     * @param map     存储的map,其中map的key和value均为Object对象
     */
    @Override
    public void setMapObject(String mapName, Map<String, Object> map) {
        this.setMapObject(mapName, 0, map);
    }


    /**
     * 存入map对象,包含过期时间 ，   其中map的key和value均为Object对象
     *
     * @param mapName map名称
     * @param exp     过期时间 单位秒
     * @param map     存储的map,其中map的key和value均为Object对象
     */
    @Override
    public void setMapObject(String mapName, int exp,
                             Map<String, Object> map) {
        if (map == null) {
            return;
        }
        Map<byte[], byte[]> byteMap = new HashMap<byte[], byte[]>();
        Set<Entry<String, Object>> entrySet = map.entrySet();
        for (Entry<String, Object> entry : entrySet) {
            byteMap.put(getBytes(entry.getKey()), SerializeUtil.serialize(entry.getValue()));
        }
        byte[] mapNameBytes = getBytes(mapName);
        redisClient.hMSet(mapNameBytes, byteMap);
        if (exp > 0) {
            redisClient.expire(mapNameBytes, exp, TimeUnit.SECONDS);
        }

    }

    /**
     * 获取整个map对象，     其中map的key和value均为Object对象
     *
     * @param mapName map的名称
     * @return map对象，数据已转为存入前的类型
     */
    @Override
    public Map<String, Object> getMapObject(String mapName) {
        Map<byte[], byte[]> byteMap = redisClient.hGetAll(getBytes(mapName));
        if (byteMap == null) {
            return null;
        }
        Map<String, Object> objectMap = new HashMap<String, Object>();

        Set<Entry<byte[], byte[]>> entrySet = byteMap.entrySet();
        for (Entry<byte[], byte[]> entry : entrySet) {
            try {
                objectMap.put(new String(entry.getKey(), ENCODE), SerializeUtil.unSerialize(entry.getValue()));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }

        return objectMap;
    }


    /**
     * 设置map的属性值， 其中map的key和value均为Object对象
     *
     * @param mapName map名称
     * @param field   属性名称
     * @param object  存入的Object对象
     */
    @Override
    public void setFieldObject(String mapName, String field, Object object) {
        byte[] bytes = redisClient.hGet(this.getBytes(mapName), this.getBytes(field));
        if (bytes != null) {
            redisClient.hSet(this.getBytes(mapName), this.getBytes(field), SerializeUtil.serialize(object));
        }
    }

    /**
     * 获取Map中的属性值，    其中map的key和value均为Object对象
     *
     * @param mapName map的名称
     * @param field   属性名称
     * @return map中的属性值
     */
    @Override
    public Object getFieldObject(String mapName, String field) {
        byte[] bytes = redisClient.hGet(this.getBytes(mapName), this.getBytes(field));
        if (bytes != null) {
            return SerializeUtil.unSerialize(bytes);
        }
        return null;
    }


    /**
     * 删除 Map中的属性值，   其中map的key和value均为Object对象
     *
     * @param mapName map的名称
     * @param field   属性名称
     * @return 1 is success ,0 is no field deleted
     */
    @Override
    public Long delFieldObject(String mapName, String field) {
        return redisClient.hDel(this.getBytes(mapName), this.getBytes(field));
    }


    /**
     * 查询map中是否存在该属性值
     *
     * @param mapName map名称
     * @param field   属性名称
     * @return 是否存在
     */
    @Override
    public boolean existsFieldObject(String mapName, String field) {
        return redisClient.hExists(getBytes(mapName), getBytes(field));
    }


    /**
     * 获取String的byte[]数组
     *
     * @param string
     * @return
     */
    private byte[] getBytes(String string) {
        try {
            if (string != null) {
                return string.getBytes(ENCODE);
            }
        } catch (UnsupportedEncodingException e) {
        }
        return null;
    }

    @Override
    public byte[] getBytesInfo(String key) {
        try {
            return redisClient.get(SafeEncoder.encode(key));
        } catch (Exception e) {
            return null;
        }
    }
    @Override
    public boolean setNx(String key, String value) {
        return redisClient.setNX(key, value);
    }

    @Override
    public boolean expire(String key, long seconds) {
        return redisClient.expire(key, seconds, TimeUnit.SECONDS);
    }

    @Override
    public Set<String> getKeys(String key) {
        return redisClient.hKeys(key);
    }


    /**
     * 简易锁,和simpleLockRelease配套使用
     */
    public Boolean simpleLock(String key, String requestId,long timeout) {
        try {
            return redisClient.set(key, requestId, timeout, TimeUnit.SECONDS, false);
        }catch (Exception e){
            throw new RuntimeException("RedisCacheManager.simpleLock error" ,e);
        }
    }

    /**
     * 简易锁释放
     */
    public boolean simpleLockRelease(String key, String requestId) {
        try {
            String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
            String load = redisClient.scriptLoad(script);
            Object result = redisClient.evalsha(load, Collections.singletonList(key), Collections.singletonList(requestId), false, ScriptOutputType.BOOLEAN);
            return RELEASE_SUCCESS.equals(result);
        }catch (Exception e){
            throw new RuntimeException("RedisCacheManager.simpleLockRelease error" ,e);
        }
    }

    public Boolean setEx(String key, String value, long timeout, TimeUnit unit) {
        try {
            redisClient.setEx(key, value, timeout, unit);
        }catch (Exception e){
            log.error("RedisCacheManager.set error, key={}, value={}", key, value, e);
            return false;
        }
        return true;
    }
}
