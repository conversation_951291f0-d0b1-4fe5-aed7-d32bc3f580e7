package com.jd.international.soa.service.common.cart;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.jd.ept.warecenter.api.enums.LanguageEnum;
import com.jd.ept.warecenter.api.enums.WareBaseFieldEnum;
import com.jd.international.soa.base.isc.common.IscBaseReqDTO;
import com.jd.international.soa.common.config.UccConfig;
import com.jd.international.soa.common.exception.BizException;
import com.jd.international.soa.common.exception.ResponseErrorCode;
import com.jd.international.soa.common.utils.DataTranslationUtils;
import com.jd.international.soa.dao.cart.CartMapper;
import com.jd.international.soa.dao.cart.CartWaresMapper;
import com.jd.international.soa.dao.product.JdiIntlProductBaseLangShardingMapper;
import com.jd.international.soa.dao.product.JdiIntlProductPushTaskShardingMapper;
import com.jd.international.soa.domain.common.cart.Cart;
import com.jd.international.soa.domain.common.cart.CartWares;
import com.jd.international.soa.domain.enums.YnEnum;
import com.jd.international.soa.domain.stock.ProductStockInfoRes;
import com.jd.international.soa.rpc.isc.mku.RpcMkuService;
import com.jd.international.soa.rpc.product.RpcEptProductService;
import com.jd.international.soa.rpc.product.RpcIscMkuMaterialService;
import com.jd.international.soa.rpc.product.RpcProductService;
import com.jd.international.soa.rpc.trans.RpcTranslateService;
import com.jd.international.soa.sdk.common.cart.req.*;
import com.jd.international.soa.sdk.common.cart.resp.CartResp;
import com.jd.international.soa.sdk.common.cart.resp.CartWaresGroupResp;
import com.jd.international.soa.sdk.common.cart.resp.CartWaresResp;
import com.jd.international.soa.sdk.common.common.MultiCurrencyPriceDTO;
import com.jd.international.soa.sdk.common.common.ProductPricesDTO;
import com.jd.international.soa.sdk.common.customer.CustomerApiService;
import com.jd.international.soa.sdk.common.customer.res.CustomerDTO;
import com.jd.international.soa.sdk.common.enums.stock.StockFlagEnum;
import com.jd.international.soa.sdk.common.enums.stock.StockStateTypeEnum;
import com.jd.international.soa.sdk.common.isc.mku.IscMkuClientApiService;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientDeliveryAgingDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientListReqDTO;
import com.jd.international.soa.sdk.common.material.res.MkuMaterialResDTO;
import com.jd.international.soa.sdk.common.price.req.PriceSkuInfoReq;
import com.jd.international.soa.sdk.common.price.resp.PriceResp;
import com.jd.international.soa.sdk.common.sale.SkuSaleAttrDTO;
import com.jd.international.soa.service.adapter.cart.CartConvert;
import com.jd.international.soa.service.adapter.material.MkuMaterialConvert;
import com.jd.international.soa.service.authority.AuthorityService;
import com.jd.international.soa.service.common.cache.JimCacheService;
import com.jd.international.soa.service.common.currency.CurrencySymbolService;
import com.jd.international.soa.service.common.price.PriceService;
import com.jd.international.soa.service.es.manage.ProductManageService;
import com.jd.international.soa.service.order.currency.CurrencyService;
import com.jd.international.soa.service.wares.gift.GiftService;
import com.jd.international.soa.service.wares.product.ProductQueryService;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.aggregate.read.api.sku.SkuReadApiService;
import com.jdi.isc.aggregate.read.api.sku.resp.SkuFeatureResp;
import com.jdi.isc.aggregate.read.wisp.api.common.BaseReqDTO;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.*;
import com.jdi.isc.library.common.exception.BizI18nException;
import com.jdi.isc.library.common.response.ResponseCode;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.material.res.MkuMaterialApiDTO;
import com.jdi.isc.product.soa.api.mku.req.IscMkuAvailableSaleReq;
import com.jdi.isc.product.soa.api.mku.res.IscMkuAvailableSaleResDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CartServiceImpl implements CartService {
    private final UccConfig uccConfig;
    private final CartMapper cartMapper;
    private final CartWaresMapper cartWaresMapper;
    private final RpcProductService rpcProductService;
    private final PriceService priceService;
    private final CurrencyService currencyService;
    private final ProductQueryService productQueryService;
    private final JimCacheService jimCacheService;
    private final GiftService giftService;
    private final RpcEptProductService rpcEptProductService;
    private final JdiIntlProductPushTaskShardingMapper jdiIntlProductPushTaskShardingMapper;
    private final JdiIntlProductBaseLangShardingMapper jdiIntlProductBaseLangShardingMapper;
    private final RpcTranslateService rpcTranslateService;
    private final RpcMkuService rpcMkuService;
    private final CurrencySymbolService currencySymbolService;
    private final IscMkuClientApiService iscMkuClientApiService;
    private final AuthorityService authorityService;
    private final RpcIscMkuMaterialService rpcIscMkuMaterialService;
    private final SkuReadApiService skuReadApiService;
    private final CustomerApiService customerApiService;
    private final ProductManageService productManageService;
    final ExecutorService pool = new ThreadPoolExecutor(8, 16, 90L, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(200), new ThreadFactoryBuilder().setNamePrefix("sku-translate").build());

    @Value("${spring.profiles.active}")
    private String active;

    /**
     * 库存阀值
     */
    private static final int STOCK_THRESHOLD_VALUE = 200;

    @Override
    public int getCartNum(CartReq cartReq) {
        Cart cart = getCartItem(cartReq.getCode(), cartReq.getPin(), cartReq.getContractNum());
        return cart.getTotalKindCount();
    }

    @Override
    public CartResp getCartList(CartReq cartReq) {
        BaseReqDTO baseReqDTO = CartConvert.INSTANCE.cartReq2BaseReqDTO(cartReq);
        Cart cart = getCartItem(cartReq.getCode(), cartReq.getPin(), cartReq.getContractNum());
        if(StringUtils.isBlank(baseReqDTO.getClientCode())){
            log.error("CartServiceImpl.getCartList invole error, cartReq={} ", JSON.toJSONString(cartReq));
        }
        String requestSource = cartReq.getRequestSource();
        CartResp res = packageCartFromAggService(baseReqDTO, cart, false, cartReq.getChecked(), requestSource);
        refreshChecked(cartReq,cart,res);

        // 填充物料信息
        if (CollectionUtils.isNotEmpty(res.getCartWares())){
            fillMaterialInfo(baseReqDTO.getClientCode(), res.getCartWares());
        }else {
            log.error("CartServiceImpl.getCartList error, CartWares empty. cartReq={} ", JSON.toJSONString(cartReq));
        }

        return res;
    }

    //刷新数据库中的选中状态
    private void refreshChecked(CartReq cartReq,Cart cart,CartResp res) {
        if(res!=null && CollectionUtils.isNotEmpty(res.getCartWares())){
            List<String> checkedSku = new ArrayList<>();
            List<String> unCheckedSku = new ArrayList<>();
            //本土 todo 本土库存打通前购物车商品可售不判断库存状态
            if(cartReq.getStationType()==0){
                res.getCartWares().forEach(ware->{
                    if(ware.getChecked() && ware.getInPool()){
                        checkedSku.add(ware.getSku());
                    }else {
                        unCheckedSku.add(ware.getSku());
                    }
                });
            //跨境
            }else if(cartReq.getStationType()==1){
                res.getCartWares().forEach(ware->{
                    if(ware!=null){
                        if(ware.getChecked()!=null && ware.getChecked() && ware.getInPool()!=null && ware.getInPool() && ware.getRemainNum()!=null
                                && ware.getRemainNum()!=0){
                            checkedSku.add(ware.getSku());
                        }else {
                            unCheckedSku.add(ware.getSku());
                        }
                    }
                });
            }
            if(CollectionUtils.isNotEmpty(checkedSku)){
                cartWaresMapper.updateCartWaresChecked(cart.getId(), checkedSku, true);
            }
            if(CollectionUtils.isNotEmpty(unCheckedSku)){
                cartWaresMapper.updateCartWaresChecked(cart.getId(), unCheckedSku, false);
            }
        }
    }

    @Override
    @Transactional
    public CartResp addProducts(AddCartReq addCartReq) {
        BaseReqDTO baseReqDTO = CartConvert.INSTANCE.addCartReq2BaseReqDTO(addCartReq);

        Cart cart = getCartItem(addCartReq.getCode(), addCartReq.getPin(), addCartReq.getContractNum());
        List<AddCartWaresReq> wares = addCartReq.getWares();//sku加购数量
        List<String> skuList = wares.stream().map(AddCartWaresReq::getSku).collect(Collectors.toList());//加购sku
        List<CartWares> cartWaresBySku = cartWaresMapper.getCartWaresBySku(cart.getId(), skuList);//已经在购物车里的sku数量
        cartWaresBySku = Optional.ofNullable(cartWaresBySku).orElseGet(Collections::emptyList);
        List<CartWares> insert = new ArrayList<>();
        List<CartWares> update = new ArrayList<>();
        List<String> repSku = cartWaresBySku.stream().map(CartWares::getSku).collect(Collectors.toList());//已经在购物车里的sku
        Set<String> lowestSku = skuList.stream().filter(sku -> !repSku.contains(sku)).collect(Collectors.toSet());//取出购物车里不包含的本次新加购的sku
        if ((cart.getTotalKindCount() + lowestSku.size()) > uccConfig.getCartMaxNum()) {
            throw new BizException(ResponseErrorCode.CART_NUM_ERROR, String.valueOf(uccConfig.getCartMaxNum()));
        }
        //获取购物车商品可用库存
        MkuClientStockReqApiDTO req = new MkuClientStockReqApiDTO();
        req.setClientCode(addCartReq.getClientCode());
        req.setStationType(addCartReq.getStationType());
        List<MkuClientStockApiDTO> stockItemReq = new ArrayList<>();
        wares.forEach(ware-> stockItemReq.add(new MkuClientStockApiDTO(Long.valueOf(ware.getSku()), ware.getNum())));
        req.setStockItem(stockItemReq);
        Map<Long, MkuClientStockApiDTO> stockMap = rpcMkuService.getStockMap(req);
        Map<String, Integer> lowestBuy = CollectionUtils.isNotEmpty(lowestSku) ? getLowestBuy(lowestSku) : new HashMap<>();//取新加购物车商品的最小起订量(目前都按1)
        for (int i = 0, size = wares.size(); i < size; i++) {
            AddCartWaresReq addCartWaresReq = wares.get(i);//新加购sku
            Optional<CartWares> first = cartWaresBySku.stream().filter(cartWares -> cartWares.getSku().equals(addCartWaresReq.getSku())).findFirst();
            if (first.isPresent()) {//如果过往购物车存在这个sku
                CartWares cartWares = first.get();
                MkuClientStockApiDTO stock = stockMap.getOrDefault(Long.valueOf(cartWares.getSku()), new MkuClientStockApiDTO());
                int targetNum = cartWares.getSkuNum() + addCartWaresReq.getNum();
                if(stock.getNum()!=null && stock.getNum()!=-1 ){
                    cartWares.setSkuNum(targetNum<stock.getNum()?targetNum:stock.getNum());
                }else {
                    cartWares.setSkuNum(targetNum);
                }
                cartWares.setChecked(true);
                cartWares.setAddCartTime(new Date());
                update.add(cartWares);
            } else {
                insert.add(buildCartWares(addCartReq, cart, addCartWaresReq, lowestBuy));
            }
        }
        if (CollectionUtils.isNotEmpty(update)) {
            cartWaresMapper.batchUpdate(update);
        }
        if (CollectionUtils.isNotEmpty(insert)) {
            cartWaresMapper.batchInsert(insert);
        }

        if(StringUtils.isBlank(baseReqDTO.getClientCode())){
            log.error("CartServiceImpl.addProducts invole error, target:{} ", JSON.toJSONString(baseReqDTO.getClientCode()));
        }
        packageCartFromAggService(baseReqDTO, cart, true, null, CartReq.REQUEST_SOURCE_CART);
//        packageCart(cart, true, null, null);
        cart.setTotalKindCount(cart.getTotalKindCount());
        return packageCart(cart, null, null,null);
    }

    @Override
    @Transactional
    public CartResp updateProductNum(UpdateCartReq updateCartReq) {
        BaseReqDTO baseReqDTO = CartConvert.INSTANCE.updateCartReq2BaseReqDTO(updateCartReq);

        Cart cart = getCartItem(updateCartReq.getCode(), updateCartReq.getPin(), updateCartReq.getContractNum());
        String sku = updateCartReq.getSku();
        Integer num = updateCartReq.getNum();
        if (num < 1) {
            throw new BizException(ResponseErrorCode.TRADE_LOWEST_ERROR, sku);
        }
//        Map<String, Integer> lowestBuy = getLowestBuy(Collections.singleton(sku));
//        if(MapUtils.isNotEmpty(lowestBuy) && lowestBuy.containsKey(sku) && lowestBuy.get(sku).intValue() > num.intValue()){
//            throw new BizException(ResponseErrorCode.TRADE_LOWEST_ERROR,sku);
//        }

        cartWaresMapper.updateCartWaresNum(cart.getId(), sku, num);
        if(StringUtils.isBlank(baseReqDTO.getClientCode())){
            log.error("CartServiceImpl.updateProductNum invole error, target:{}",JSON.toJSONString(baseReqDTO.getClientCode()));
        }
        CartResp res = packageCartFromAggService(baseReqDTO, cart, true, null, CartReq.REQUEST_SOURCE_CART);

        // 填充物料信息
        fillMaterialInfo(baseReqDTO.getClientCode(), res.getCartWares());
//        return packageCart(cart,true,null, updateCartReq.getEnv());
        return res;
    }

    @Override
    @Transactional
    public CartResp checkProduct(CartReq cartReq) {
        BaseReqDTO baseReqDTO = CartConvert.INSTANCE.cartReq2BaseReqDTO(cartReq);
        Cart cart = getCartItem(cartReq.getCode(), cartReq.getPin(), cartReq.getContractNum());
        cartWaresMapper.updateCartWaresChecked(cart.getId(), cartReq.getSkus(), cartReq.getChecked());
        if(StringUtils.isBlank(baseReqDTO.getClientCode())){
            log.error("CartServiceImpl.checkProduct invole error, target:{} ", JSON.toJSONString(baseReqDTO.getClientCode()));
        }
        return packageCartFromAggService(baseReqDTO, cart, true, null, CartReq.REQUEST_SOURCE_CART);
//        CartResp res = packageCartFromAggService(baseReqDTO, cart, true, null);
//        cartWaresMapper.updateCartWaresChecked(cart.getId(), cartReq.getSkus(), cartReq.getChecked());
//        return null;
    }

    @Override
    @Transactional
    public CartResp delProducts(DelCartReq delCartReq) {
        BaseReqDTO baseReqDTO = CartConvert.INSTANCE.delCartReq2BaseReqDTO(delCartReq);

        Cart cart = getCartItem(delCartReq.getCode(), delCartReq.getPin(), delCartReq.getContractNum());
        cartWaresMapper.delCartWares(cart.getId(), delCartReq.getSkuIds());
        if(StringUtils.isBlank(baseReqDTO.getClientCode())){
            log.error("CartServiceImpl.delProducts invole error, target:{} ", JSON.toJSONString(baseReqDTO.getClientCode()));
        }
        return packageCartFromAggService(baseReqDTO, cart, true, null, CartReq.REQUEST_SOURCE_CART);
//        return packageCart(cart,true,null, null);
    }

    @Override
    @Transactional
    public CartResp replaceCart(AddCartReq addCartReq) {
        BaseReqDTO baseReqDTO = CartConvert.INSTANCE.addCartReq2BaseReqDTO(addCartReq);

        Cart cart = getCartItem(addCartReq.getCode(), addCartReq.getPin(), addCartReq.getContractNum());
        // 删除购物车现有的商品
        cartWaresMapper.delCartAllWares(cart.getId());
        Set<String> lowestSku = addCartReq.getWares().stream().map(AddCartWaresReq::getSku).collect(Collectors.toSet());
        Map<String, Integer> lowestBuy = CollectionUtils.isNotEmpty(lowestSku) ? getLowestBuy(lowestSku) : new HashMap<>();
        List<CartWares> collect = addCartReq.getWares().stream().map(ware ->
                buildCartWares(addCartReq, cart, ware, lowestBuy)).collect(Collectors.toList());
        cartWaresMapper.batchInsert(collect);
        if(StringUtils.isBlank(baseReqDTO.getClientCode())){
            log.error("CartServiceImpl.replaceCart invole error, target:{}", JSON.toJSONString(baseReqDTO.getClientCode()));
        }
        return packageCartFromAggService(baseReqDTO, cart, true, null, CartReq.REQUEST_SOURCE_CART);
//        return packageCart(cart,true,null, null);
    }

    @Override
    public int deleteAll() {
        cartMapper.delete();
        cartWaresMapper.delete();
        return 0;
    }

//    @Override
//    public Set<Long> queryInPoolMku(AddCartReq cartReq) {
//        MkuClientListInfoReqApiDTO input = new MkuClientListInfoReqApiDTO();
//        input.setClientCode(cartReq.getClientCode());
//        input.setMkuIds(cartReq.getWares().stream().map(c -> Long.valueOf(c.getSku())).collect(Collectors.toSet()));
//        input.setStationType(cartReq.getStationType());
//        return rpcMkuService.queryPoolMkuIds(input);
//    }

    @Override
    public Set<Long> getMkuAvailable(AddCartReq cartReq) {
        Set<Long> results = new HashSet<>();
        IscMkuAvailableSaleReq saleReq = this.buildMkuAvailable(cartReq.getWares().stream().map(c -> Long.valueOf(c.getSku())).collect(Collectors.toSet()),cartReq.getClientCode());
        Map<Long, IscMkuAvailableSaleResDTO> mkuSaleStatusMap = rpcMkuService.getMkuAvailable(saleReq);
        if(MapUtils.isEmpty(mkuSaleStatusMap)){
            return results;
        }
        mkuSaleStatusMap.forEach((k,v)->{
            if(v.getIsAvailableSale()){
                results.add(v.getMkuId());
            }
        });

        return results;
    }

    private CartWares buildCartWares(AddCartReq addCartReq, Cart cart, AddCartWaresReq ware, Map<String, Integer> lowestBuy) {
        CartWares cartWares = new CartWares();
        cartWares.setPin(addCartReq.getPin());
        cartWares.setCartId(cart.getId());
        cartWares.setSku(ware.getSku());
        cartWares.setSkuNum(lowestBuy.containsKey(ware.getSku()) ? (lowestBuy.get(ware.getSku())
                > ware.getNum() ? lowestBuy.get(ware.getSku()) : ware.getNum()) : ware.getNum());
        cartWares.setAddCartPrice(ware.getPrice());
        cartWares.setChecked(true);
        cartWares.setAddCartTime(new Date());
        cartWares.setDel(false);
        return cartWares;
    }

    /**
     * @param cart    购物车
     * @param renew   是否更新数据
     * @param checked 是否获取勾选或全部商品
     * @param env     当前环境
     * @return
     */
    @PFTracing
    @Deprecated
    private CartResp packageCart(Cart cart, boolean renew, Boolean checked, String env) {
        try {
            List<CartWares> cartWares = cartWaresMapper.getCartWaresByCartId(cart.getId(), checked);
            cartWares = Optional.ofNullable(cartWares).orElseGet(Collections::emptyList);
            int totalCount = 0, totalCheckCount = 0, totalKindCount = 0, totalCheckKindCount = 0;
            BigDecimal totalPrice = BigDecimal.ZERO;
            List<CartWaresResp> cartWaresRespList = new ArrayList<>();
            List<CartWaresResp> notSale = new ArrayList<>();
            List<CartWaresResp> notSaleChecked = new ArrayList<>();
            // 单位映射表
            String unitMapString = uccConfig.getUnit();
            log.info("uccConfig {}", JSON.toJSONString(uccConfig));
            HashMap<String, String> unitMap = JSON.parseObject(unitMapString, HashMap.class);
            if (CollectionUtils.isNotEmpty(cartWares)) {
                List<PriceSkuInfoReq> skuInfos = new ArrayList<>();
                Set<String> skuIds = new HashSet<>();
                cartWares.stream().forEach(ware -> {
                    PriceSkuInfoReq priceSkuInfoReq = new PriceSkuInfoReq();
                    priceSkuInfoReq.setSku(ware.getSku());
                    priceSkuInfoReq.setNum(ware.getSkuNum());
                    skuInfos.add(priceSkuInfoReq);
                    skuIds.add(ware.getSku());
                });

                CompletableFuture<Map<String, PriceResp>> waresPriceFuture = CompletableFuture.supplyAsync(() -> getWaresPrice(skuInfos, cart.getPin()), pool);
                CompletableFuture<Map<String, Map<String, String>>> skwWaresFuture = CompletableFuture.supplyAsync(() -> getWares(skuIds, env), pool);
                CompletableFuture<Map<String, Integer>> lowestBuyFuture = CompletableFuture.supplyAsync(() -> getLowestBuy(skuIds), pool);
//                CompletableFuture<Map<String, Map<String, String>>> saleStateFuture = CompletableFuture.supplyAsync(() -> productQueryService.querySkuSaleState(cart.getPin(), new ArrayList(skuIds)), pool);
//                CompletableFuture<Map<String, Map<String, Map<String, String>>>> giftFuture = CompletableFuture.supplyAsync(() -> giftService.getSkuGiftPromotion(skuIds), pool);
                Map<String, Map<String, String>> skuWares = skwWaresFuture.get();
                Map<String, Integer> lowestBuy = lowestBuyFuture.get();
//                Map<String, Map<String, String>> saleStateMap = saleStateFuture.get();
//                Map<String, Map<String, Map<String, String>>> giftMap = giftFuture.get();
                Map<String, PriceResp> waresPrice = waresPriceFuture.get();
                // 如果是英文环境 替换skuWaresMap部分字段
                if ("en".equals(env)) {
                    List<Long> skuIdsLong = skuIds.stream()
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
                    // 获取SkuId-EptId映射
                    HashMap<Long, Long> eptSkuIdMap = getEptSkuIdMap(skuIdsLong);
                    // ept信息map
                    Map<Long, Map<String, String>> result = new HashMap<>();
                    HashSet<Long> eptIds = new HashSet<>();
                    for (Map.Entry<Long, Long> entry : eptSkuIdMap.entrySet()) {
                        eptIds.add(entry.getValue());
                    }

                    // 查询ept商品信息
                    result = rpcEptProductService.queryBaseInfo(eptIds, LanguageEnum.ENGLISH);
                    for (Map.Entry<String, Map<String, String>> entry : skuWares.entrySet()) {
                        Map<String, String> skuWare = entry.getValue();
                        String skuId = entry.getKey();
                        if (eptSkuIdMap.containsKey(Long.valueOf(skuId))) {
                            Long eptSkuId = eptSkuIdMap.get(Long.valueOf(skuId));
                            Map<String, String> eptSkuInfo = result.get(eptSkuId);
                            if (MapUtils.isNotEmpty(eptSkuInfo)) {
                                // 获取ept商品标题
                                skuWare.put("sku_name", eptSkuInfo.get(WareBaseFieldEnum.TITLE.getField()));
                                // 查询商品离线库获取型号信息
                                String modelEn = jdiIntlProductBaseLangShardingMapper.selectModelEnBySkuId(Long.valueOf(skuId));
                                if (StringUtils.isNotBlank(modelEn)) {
                                    skuWare.put("model", modelEn);
                                }
                            }
                        }
                        // 单位映射
                        if (MapUtils.isNotEmpty(unitMap)) {
                            String unitEn = unitMap.get(skuWare.get("unit"));
                            skuWare.put("unit", StringUtils.isNotBlank(unitEn) ? unitEn : "piece");
                        }
//                        //赠品实时翻译
//                        if(MapUtils.isNotEmpty(giftMap)) {
//                            Map<String, Map<String, String>> gitMap = giftMap.get(skuId);
//                            if (MapUtils.isNotEmpty(gitMap)) {
//                                for (Map.Entry<String, Map<String, String>> giftEntry : gitMap.entrySet()) {
//                                    Map<String, String> entryValue = giftEntry.getValue();
//                                    entryValue.put("product_name", rpcTranslateService.transCn2EnStr(entryValue.get("product_name")));
//                                }
//                            }
//                        }
                    }
                    log.info("购物车英文商品信息内容skuWare{}", JSON.toJSONString(skuWares));
                }
                for (int i = 0, size = cartWares != null ? cartWares.size() : 0; i < size; i++) {
                    CartWares wares = cartWares.get(i);
                    CartWaresResp cartWaresResp = packageCartWares(wares, skuWares, waresPrice, lowestBuy, null, null);
                    int saleState = 1;
                    totalCount += cartWaresResp.getSkuNum();
                    totalKindCount += 1;
                    // 0:不可售 1：可售
                    if (cartWaresResp.getChecked() && saleState == 1) {
                        totalCheckCount += cartWaresResp.getSkuNum();
                        totalCheckKindCount += 1;
                        totalPrice = totalPrice.add(cartWaresResp.getNotes());
                    }
                    if (saleState == 0) {
                        notSale.add(cartWaresResp);
                        if (cartWaresResp.getChecked()) {
                            notSaleChecked.add(cartWaresResp);
                        }
                    } else {
                        cartWaresRespList.add(cartWaresResp);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(notSale)) {
                if (CollectionUtils.isNotEmpty(notSaleChecked)) {
                    List<String> notSaleSkuIds = notSaleChecked.stream().filter(not -> (not.getChecked() != null && not.getChecked())
                                    && (not.getSaleState() != null && not.getSaleState().getSaleState() == 0))
                            .map(CartWaresResp::getSku).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(notSaleSkuIds)) {
                        cartWaresMapper.updateCartWaresChecked(cart.getId(), notSaleSkuIds, false);
                    }
                }
                cartWaresRespList.addAll(notSale);
            }
            cart.setTotalCount(totalCount);
            cart.setTotalCheckCount(totalCheckCount);
            cart.setTotalKindCount(totalKindCount);
            cart.setTotalCheckKindCount(totalCheckKindCount);
            if (renew) {
                cartMapper.update(cart);
            }
            CartResp cartResp = packageCart(cart, cartWaresRespList, totalPrice,null);
            return cartResp;
        } catch (Exception e) {
            log.error("-----------加载购物车失败", e);
        }
        return null;
    }

    private CartResp packageCartFromAggService(BaseReqDTO baseReqDTO, Cart cart, boolean renew, Boolean checked, String requestSource) {
        try {
            /**
             * todo
             * 增加购物车商品信息校验：1、需要返回的商品数量与实际查到的商品数量不一致，则需要返回错误信息
             */
            List<CartWares> cartWares = cartWaresMapper.getCartWaresByCartId(cart.getId(), checked);
            log.info("packageCartFromAggService,baseReqDTO={} cartWares={}",JSON.toJSONString(baseReqDTO), JSONObject.toJSONString(cartWares));
            cartWares = Optional.ofNullable(cartWares).orElseGet(Collections::emptyList);
            int totalCount = 0, totalCheckCount = 0, totalKindCount = 0, totalCheckKindCount = 0;
            BigDecimal totalPrice = BigDecimal.ZERO;
            List<CartWaresResp> cartWaresRespList = new ArrayList<>();
            List<CartWaresResp> notSale = new ArrayList<>();
            List<CartWaresResp> notSaleChecked = new ArrayList<>();
            MultiCurrencyPriceDTO totalCurrencies = new MultiCurrencyPriceDTO();
            if (CollectionUtils.isNotEmpty(cartWares)) {
                IscMkuClientListReqDTO iscMkuClientListReqDTO = CartConvert.INSTANCE.baseReqDTO2MkuReqApiDTO(baseReqDTO);
                iscMkuClientListReqDTO.setMkuIds(cartWares.stream().map(line->Long.valueOf(line.getSku())).collect(Collectors.toSet()));
                List<IscMkuClientDTO> iscMkuClientDTOS = productManageService.listSimpleInfo(iscMkuClientListReqDTO);
                Map<Long, IscMkuClientDTO> simpleApiDTOMap = null;
                if (CollectionUtils.isNotEmpty(iscMkuClientDTOS)) {
                    simpleApiDTOMap = iscMkuClientDTOS.stream().filter(Objects::nonNull).collect(Collectors.toMap(IscMkuClientDTO::getMkuId, o -> o));
                }
                // 为空时会导致下面出现空指针异常
                if (simpleApiDTOMap == null) {
                    return new CartResp();
                }
                //构建可用库存
                buildRemainNum(baseReqDTO.getClientCode(),baseReqDTO.getStationType(),cartWares,simpleApiDTOMap);

                Set<Long> mkuIdSet = cartWares.stream().map(CartWares::getSku).map(Long::valueOf).collect(Collectors.toSet());

                Map<Long, SkuFeatureResp> skuFeatureMap = Collections.emptyMap();;
                Map<Long, IscMkuClientDeliveryAgingDTO> deliveryAgingMap = Collections.emptyMap();;

                if (CartReq.REQUEST_SOURCE_SETTLEMENT.equals(requestSource)) {
                    DataResponse<CustomerDTO> customerDTOResponse = customerApiService.queryCustomerByClientCode(baseReqDTO.getClientCode());
                    CustomerDTO customerDTO = customerDTOResponse.getData();
                    String countryCode = customerDTO == null ? null : customerDTO.getCountry();
                    try {
                        DataResponse<Map<Long, SkuFeatureResp>> skuFeatureResp = skuReadApiService.querySkuFeature(mkuIdSet, countryCode, baseReqDTO.getClientCode());
                        log.info("packageCartFromAggService, skuFeatureResp={}", JSONObject.toJSONString(skuFeatureResp));
                        skuFeatureMap = skuFeatureResp.getData();

                        List<Long> mkuIds = simpleApiDTOMap.values().stream().map(IscMkuClientDTO::getMkuId).collect(Collectors.toList());
                        DataResponse<Map<Long, IscMkuClientDeliveryAgingDTO>> deliveryAging = iscMkuClientApiService.getDeliveryAging(mkuIds, baseReqDTO.getClientCode());
                        log.info("packageCartFromAggService, deliveryAging={}", JSONObject.toJSONString(deliveryAging));
                        if (deliveryAging == null || !deliveryAging.getSuccess()) {
                            deliveryAgingMap = Collections.emptyMap();
                        } else {
                            deliveryAgingMap = deliveryAging.getData();
                        }
                    } catch (Exception e) {
                        log.error("商品属性或时效信息获取失败。baseReqDTO={}, mkuIdSet={}"
                                , JSONObject.toJSONString(baseReqDTO), JSONObject.toJSONString(mkuIdSet), e);

                        Profiler.businessAlarm("cartServiceImpl.settlement.page.getCartList.feature.error."+active
                                , active+LevelCode.P1.getMessage() + "获取购物车商品属性或时效信息失败。info="+JSONObject.toJSONString(baseReqDTO));
                    }
                }

                for (int i = 0, size = cartWares != null ? cartWares.size() : 0; i < size; i++) {
                    CartWares wares = null;
                    try {
                        wares = cartWares.get(i);
                        CartWaresResp cartWaresResp = packageCartWaresAgg(wares, simpleApiDTOMap, baseReqDTO.getLang(), skuFeatureMap.get(Long.valueOf(wares.getSku())), deliveryAgingMap);
                        log.info("packageCartFromAggService  cartWaresResp ={}", JSON.toJSONString(cartWaresResp));
                        int saleState = 1;
                        totalCount += cartWaresResp.getSkuNum();
                        totalKindCount += 1;
                        // 0:不可售 1：可售
                        if (cartWaresResp.getChecked() && saleState == 1) {
                            totalCheckCount += cartWaresResp.getSkuNum();
                            totalCheckKindCount += 1;
                            totalPrice = totalPrice.add(null != cartWaresResp.getNotes() ? cartWaresResp.getNotes() : BigDecimal.ZERO);
                        }
                        if (saleState == 0) {
                            notSale.add(cartWaresResp);
                            if (cartWaresResp.getChecked()) {
                                notSaleChecked.add(cartWaresResp);
                            }
                        } else {
                            cartWaresRespList.add(cartWaresResp);
                        }
                    } catch (Exception e) {
                        log.error("填充购物车商品信息异常, baseReqDTO={}, wares={}", JSONObject.toJSONString(baseReqDTO), JSONObject.toJSONString(wares), e);
                        if (CartReq.REQUEST_SOURCE_SETTLEMENT.equals(requestSource)) {
                            throw new BizI18nException(ResponseCode.COMMON_RESPONSE_FAIL.getCode());
                        }
                    }
                }
                // 总金额多币种设置
                this.convertTotalCurrencies(cartWaresRespList, totalCurrencies);
            }
            if (CollectionUtils.isNotEmpty(notSale)) {
                if (CollectionUtils.isNotEmpty(notSaleChecked)) {
                    List<String> notSaleSkuIds = notSaleChecked.stream().filter(not -> (not.getChecked() != null && not.getChecked())
                                    && (not.getSaleState() != null && not.getSaleState().getSaleState() == 0))
                            .map(CartWaresResp::getSku).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(notSaleSkuIds)) {
                        cartWaresMapper.updateCartWaresChecked(cart.getId(), notSaleSkuIds, false);
                    }
                }
                cartWaresRespList.addAll(notSale);
            }
            cart.setTotalCount(totalCount);
            cart.setTotalCheckCount(totalCheckCount);
            cart.setTotalKindCount(totalKindCount);
            cart.setTotalCheckKindCount(totalCheckKindCount);
            if (renew) {
                cartMapper.update(cart);
            }
            //商品排序
            cartWaresRespList.sort((o1,o2)->{
                if (o1.getInPool() && !o2.getInPool()) {
                    return -1;
                }
                if (!o1.getInPool() && o2.getInPool()) {
                    return 1;
                }
                return o2.getAddCartTime().compareTo(o1.getAddCartTime());
            });
            CartResp cartResp = packageCart(cart, cartWaresRespList, totalPrice,totalCurrencies);
            log.info("packageCartFromAggService cartResp={}",JSON.toJSONString(cartResp));
            // 商品分堆
            if (CartReq.REQUEST_SOURCE_SETTLEMENT.equals(requestSource)) {
                groupCartWares(cartResp);
            }
            return cartResp;
        } catch (Exception e) {
            log.error("load cart exception. baseReqDTO={}", JSONObject.toJSONString(baseReqDTO), e);

            Profiler.businessAlarm("cartServiceImpl.settlement.page.getCartList.error."+active
                    , active+LevelCode.P0.getMessage() + "WISP获取购物车商品异常。info="+JSONObject.toJSONString(baseReqDTO));

            if (CartReq.REQUEST_SOURCE_SETTLEMENT.equals(requestSource)) {
                throw new BizI18nException(ResponseCode.COMMON_RESPONSE_FAIL.getCode());
            }
        }
        return null;
    }

    private void groupCartWares(CartResp cartResp) {
        List<CartWaresResp> cartWareList = cartResp.getCartWares();
        Map<Integer, List<CartWaresResp>> fulfillmentModelWaresMap = new HashMap<>();
        for (CartWaresResp cartWare : cartWareList) {
            fulfillmentModelWaresMap.putIfAbsent(cartWare.getFulfillmentModel(), new ArrayList<>());
            fulfillmentModelWaresMap.computeIfPresent(cartWare.getFulfillmentModel(), (k, v) -> {
                v.add(cartWare);
                return v;
            });
        }
        List<CartWaresGroupResp> cartWaresGroup = new ArrayList<>();
        for (Map.Entry<Integer, List<CartWaresResp>> entry : fulfillmentModelWaresMap.entrySet()) {
            CartWaresGroupResp cartWaresGroupResp = new CartWaresGroupResp();
            cartWaresGroupResp.setFulfillmentModel(entry.getKey());
            List<CartWaresResp> cartWares = entry.getValue();
            cartWaresGroupResp.setCartWares(cartWares);
            int maxFulfillmentDays = cartWares.stream().mapToInt(
                    cartWaresResp -> {
                        Integer fulfillmentDays = cartWaresResp.getFulfillmentDays();
                        return fulfillmentDays == null ? 0 : fulfillmentDays;
                    }
            ).max().getAsInt();
            cartWaresGroupResp.setMaxFulfillmentDays(maxFulfillmentDays);
            long minDeliveryTime = LocalDateTime.now().plusDays(maxFulfillmentDays).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
            cartWaresGroupResp.setMinDeliveryTime(minDeliveryTime);
            cartWaresGroup.add(cartWaresGroupResp);
        }
        cartResp.setCartWaresGroup(cartWaresGroup);
    }

    //拼接商品现货库存信息
    private void buildRemainNum(String clientCode ,Integer stationType, List<CartWares> cartWares,Map<Long, IscMkuClientDTO> mkuMap) {
        try {
            List<MkuClientStockApiDTO> stockItem = new ArrayList<>();
            for (CartWares wa : cartWares){
                Long key = Long.valueOf(wa.getSku());
                if(mkuMap.get(key)!=null && mkuMap.get(key).getSaleStatus() != null && mkuMap.get(key).getSaleStatus().equals(YnEnum.YES.getCode())){
                    MkuClientStockApiDTO stockReq = new MkuClientStockApiDTO();
                    stockReq.setNum(wa.getSkuNum());
                    stockReq.setMkuId(key);
                    stockItem.add(stockReq);
                }
            }
            MkuClientStockReqApiDTO req = new MkuClientStockReqApiDTO();
            req.setClientCode(clientCode);
            req.setStationType(stationType);
            req.setStockItem(stockItem);
            List<MkuClientStockApiDTO> res = rpcMkuService.getStock(req);
            if(CollectionUtils.isNotEmpty(res)){
                Map<Long, MkuClientStockApiDTO> map = res.stream().collect(Collectors.toMap(MkuClientStockApiDTO::getMkuId, Function.identity()));
                cartWares.forEach(ware-> {
                    MkuClientStockApiDTO mkuClientStockApiDTO = map.get(Long.valueOf(ware.getSku()));
                    if (mkuClientStockApiDTO != null) {
                        ProductStockInfoRes productStockInfoRes = calculateStock(clientCode, ware, mkuClientStockApiDTO);
                        ware.setRemainNum(productStockInfoRes.getRemainNum());
                        ware.setStockFlag(productStockInfoRes.getStockFlag());
                        ware.setStockStateType(productStockInfoRes.getStockStateType());
                    }
                });
            }
        }catch (Exception e){
            log.error("CartServiceImpl.buildRemainNum error, target:{} ", com.alibaba.fastjson.JSON.toJSONString(cartWares),e);
            Profiler.businessAlarm("CartServiceImpl.buildRemainNum."+active, active+LevelCode.P0.getMessage() + "库存处理异常,请关注: "+com.alibaba.fastjson.JSON.toJSONString(cartWares));
        }

    }

    /**
     * 计算库存
     * @param stock
     * @return
     */
    private ProductStockInfoRes calculateStock(String clientCode, CartWares cartWares, MkuClientStockApiDTO stock){
        ProductStockInfoRes res = new ProductStockInfoRes();

        if (null==stock || null==stock.getStockStateType() ||
                null==StockStateTypeEnum.forCode(stock.getStockStateType()) ){
            // 数据错误。默认设置为无货
            log.warn("calculateStock, StockState error. clientCode={}, cartWares={}, stock={}"
                    , clientCode, JSONObject.toJSONString(cartWares), JSONObject.toJSONString(stock));
            res.setRemainNum(0);
            res.setStockFlag(StockFlagEnum.OUT_OF_STOCK.getCode());
            res.setStockStateType(StockStateTypeEnum.OUT_OF_STOCK.getCode());

            // 增加预警
            StringBuilder msg = new StringBuilder();
            msg.append(active).append(LevelCode.P1.getMessage()).append("WISP计算库存值异常，未知的库存状态，请关注！")
                    .append(" clientCode=").append(clientCode)
                    .append(" , stock=").append(JSONObject.toJSONString(stock))
                    .append(" , cartWares=").append(JSONObject.toJSONString(cartWares));
            Profiler.businessAlarm("com.jd.international.soa.service.common.cart.CartServiceImpl."+ active, msg.toString());
            return res;
        }

        if (StockStateTypeEnum.OUT_OF_STOCK.getCode().equals(stock.getStockStateType())){
            // 无货
            res.setRemainNum(0);
            res.setStockFlag(StockFlagEnum.OUT_OF_STOCK.getCode());
            res.setStockStateType(stock.getStockStateType());
            return res;
        }

        // 以下为有货逻辑
        if (StockStateTypeEnum.PRE_ORDER.getCode().equals(stock.getStockStateType())){
            log.info("calculateStock, StockStateTypeEnum is PRE_ORDER.clientCode={}, cartWares={}, stock={}"
                    , clientCode, JSONObject.toJSONString(cartWares), JSONObject.toJSONString(stock));
            // 预定状态的直接返回结果，库存值未知。
            res.setRemainNum(-1);
            res.setStockFlag(StockFlagEnum.ENOUGH.getCode());
            res.setStockStateType(stock.getStockStateType());
            return res;
        }

        if (null==stock.getNum() || null==stock.getOccupy()){
            log.warn("calculateStock, stock val null. clientCode={}, cartWares={}, stock={}"
                    , clientCode, JSONObject.toJSONString(cartWares), JSONObject.toJSONString(stock));
            //有货，但库存值未知
            res.setRemainNum(-1);
            res.setStockFlag(StockFlagEnum.ENOUGH.getCode());
            res.setStockStateType(stock.getStockStateType());
            return res;
        }

        if (-1==stock.getNum()){
            // 有货但无具体值。
            res.setRemainNum(-1);
            res.setStockFlag(StockFlagEnum.ENOUGH.getCode());
            res.setStockStateType(stock.getStockStateType());
            return res;
        }

        // 可售库存(纯现货库存)，优先展示纯现货可售库存
        long stockSpotGoods = stock.getNum()-stock.getOccupy().intValue();;
        if (stockSpotGoods >= STOCK_THRESHOLD_VALUE){
            // 超阀值后，不返回具体库存值
            res.setRemainNum(-1);
            res.setStockFlag(StockFlagEnum.ENOUGH.getCode());
            res.setStockStateType(stock.getStockStateType());
            return res;
        }else if (stockSpotGoods>0){
            res.setRemainNum(Long.valueOf(stockSpotGoods).intValue());
            res.setStockFlag(StockFlagEnum.LOW.getCode());
            res.setStockStateType(stock.getStockStateType());
            return res;
        }

        // 可售库存(含在途库存)。纯现货可售库存不足，再展示采购中
        Long availableStock = stock.getAvailableStock();
        boolean stockTrans = stock.getOnWaySale()!=null && stock.getOnWaySale() == 1
                && null!= availableStock && availableStock>0;
        if (stockTrans){
            // 采购中，不返回具体库存值
            res.setRemainNum(-1);
            res.setStockFlag(StockFlagEnum.PURCHASING.getCode());
            res.setStockStateType(stock.getStockStateType());
            return res;
        }

        // 有货情况，不应该出现以下情况
        log.warn("calculateStock, stock not normal. clientCode={}, cartWares={}, stock={}"
                , clientCode, JSONObject.toJSONString(cartWares), JSONObject.toJSONString(stock));
        StringBuilder msg = new StringBuilder();
        msg.append(active).append(LevelCode.P1.getMessage()).append("WISP计算库存值异常，库存值及状态不一致，请关注！")
                .append(" clientCode=").append(clientCode)
                .append(" , stock=").append(JSONObject.toJSONString(stock))
                .append(" , cartWares=").append(JSONObject.toJSONString(cartWares));
        Profiler.businessAlarm("com.jd.international.soa.service.common.cart.CartServiceImpl."+ active, msg.toString());

        res.setRemainNum(-1);
        res.setStockFlag(StockFlagEnum.UNKNOWN.getCode());
        res.setStockStateType(stock.getStockStateType());
        return res;
    }

    private void convertTotalCurrencies(List<CartWaresResp> cartWaresRespList, MultiCurrencyPriceDTO totalCurrencies) {
        try {
            // 过滤出被选中的商品
            List<CartWaresResp> filterCartWareList = cartWaresRespList.stream().filter(CartWaresResp::getChecked).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(filterCartWareList)) {
                return;
            }

            ProductPricesDTO totalShowCurrency = new ProductPricesDTO();
            BeanUtils.copyProperties(filterCartWareList.get(0).getShowCurrency(), totalShowCurrency);
            totalShowCurrency.setPrice(BigDecimal.ZERO);
            totalShowCurrency.setShowPrice(BigDecimal.ZERO);
            totalShowCurrency.setSalePrice(BigDecimal.ZERO);
            totalShowCurrency.setValueAddedTax(BigDecimal.ZERO);
            totalShowCurrency.setIncludeTaxPrice(BigDecimal.ZERO);
            Map<String, ProductPricesDTO> totalCurrenciesPrices = new HashMap<>();

            filterCartWareList.forEach(cartWaresResp -> {
                MultiCurrencyPriceDTO notesCurrencies = cartWaresResp.getNotesCurrencies();
                // 当前币种求和
                totalShowCurrency.setPrice(totalShowCurrency.getPrice().add(notesCurrencies.getShowCurrency().getPrice()));
                totalShowCurrency.setShowPrice(totalShowCurrency.getShowPrice().add(notesCurrencies.getShowCurrency().getShowPrice()));
                totalShowCurrency.setSalePrice(totalShowCurrency.getSalePrice().add(notesCurrencies.getShowCurrency().getSalePrice()));
                totalShowCurrency.setValueAddedTax(totalShowCurrency.getValueAddedTax().add(notesCurrencies.getShowCurrency().getValueAddedTax()));
                totalShowCurrency.setIncludeTaxPrice(totalShowCurrency.getIncludeTaxPrice().add(notesCurrencies.getShowCurrency().getIncludeTaxPrice()));

                // 多币种求和
                notesCurrencies.getCurrenciesPrices().forEach((k, v) -> {
                    ProductPricesDTO productPricesDTO = new ProductPricesDTO();
                    if (totalCurrenciesPrices.containsKey(k)) {
                        productPricesDTO = totalCurrenciesPrices.get(k);
                        productPricesDTO.setSalePrice(productPricesDTO.getSalePrice().add(v.getSalePrice()));
                        productPricesDTO.setShowPrice(productPricesDTO.getShowPrice().add(v.getShowPrice()));
                        productPricesDTO.setPrice(productPricesDTO.getPrice().add(v.getPrice()));
                        productPricesDTO.setValueAddedTax(productPricesDTO.getValueAddedTax().add(v.getValueAddedTax()));
                        productPricesDTO.setIncludeTaxPrice(productPricesDTO.getIncludeTaxPrice().add(v.getIncludeTaxPrice()));
                    } else {
                        productPricesDTO = v;
                    }
                    totalCurrenciesPrices.put(k, productPricesDTO);
                });

            });
            totalCurrencies.setShowCurrency(totalShowCurrency);
            totalCurrencies.setCurrenciesPrices(totalCurrenciesPrices);
        } catch (Exception e) {
            log.error("convertTotalCurrencies error,param:{},e:",JSON.toJSONString(cartWaresRespList),e);
            throw new RuntimeException(e);
        }
    }

    private void convertTotalCurrencies(List<CartWaresResp> cartWaresRespList, BigDecimal totalPrice, MultiCurrencyPriceDTO totalCurrencies) {
        // 过滤出被选中的商品
        List<CartWaresResp> filterCartWareList = cartWaresRespList.stream().filter(CartWaresResp::getChecked).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(filterCartWareList)) {
            CartWaresResp firstWareResp = filterCartWareList.get(0);
            ProductPricesDTO productPricesDTO = new ProductPricesDTO();
            BeanUtils.copyProperties(firstWareResp.getShowCurrency(),productPricesDTO);
            productPricesDTO.setSalePrice(totalPrice);
            totalCurrencies.setShowCurrency(productPricesDTO);

            // 多币种信息
            Map<String,ProductPricesDTO> totalPricesMap = Maps.newHashMap();
            firstWareResp.getNotesCurrencies().getCurrenciesPrices().forEach((k,v)-> {
                ProductPricesDTO totalCurrency = new ProductPricesDTO();
                BeanUtils.copyProperties(v, totalCurrency);
                totalPricesMap.put(k,totalCurrency);
                });

            // 将不同币种金额做加和
            for (int i = 1, size = filterCartWareList.size(); i< size ; i++ ){
                CartWaresResp cartWaresResp = filterCartWareList.get(i);
                MultiCurrencyPriceDTO notesCurrencies = cartWaresResp.getNotesCurrencies();
                if (Objects.nonNull(notesCurrencies) && MapUtils.isNotEmpty(notesCurrencies.getCurrenciesPrices())) {
                    notesCurrencies.getCurrenciesPrices().forEach((k,v)->{
                        ProductPricesDTO tempPrices = totalPricesMap.get(k);
                        tempPrices.setSalePrice(tempPrices.getSalePrice().add(Objects.nonNull(v.getSalePrice()) ? v.getSalePrice(): BigDecimal.ZERO));
                    });
                }
            }
            totalCurrencies.setCurrenciesPrices(totalPricesMap);
        }
    }

    /**
     * 包装购物车商品参数
     *
     * @param wares
     * @param simpleApiDTOMap
     * @return
     */
    private CartWaresResp packageCartWaresAgg(CartWares wares, Map<Long, IscMkuClientDTO> simpleApiDTOMap, String lang, SkuFeatureResp skuFeature, Map<Long, IscMkuClientDeliveryAgingDTO> deliveryAgingMap) {
        CartWaresResp cartWaresResp = new CartWaresResp();
        cartWaresResp.setId(wares.getId());
        cartWaresResp.setSku(wares.getSku());
        cartWaresResp.setSkuNum(wares.getSkuNum());
        cartWaresResp.setAddCartPrice(wares.getAddCartPrice());
        cartWaresResp.setAddCartTime(wares.getAddCartTime());
        cartWaresResp.setPin(wares.getPin());

        //默认不选中，不在池
        cartWaresResp.setChecked(false);
        cartWaresResp.setInPool(false);
        int lowestBuy = 1;
        Long mkuId = Long.valueOf(wares.getSku());
        IscMkuClientDTO waresMap = simpleApiDTOMap.get(mkuId);
        IscMkuClientDeliveryAgingDTO iscMkuClientDeliveryAgingDTO = deliveryAgingMap.get(mkuId);
        if (MapUtils.isNotEmpty(simpleApiDTOMap) && simpleApiDTOMap.get(Long.valueOf(wares.getSku())) != null) {
            cartWaresResp.setSkuName(waresMap.getMkuName());
            cartWaresResp.setSourceCountryCode(waresMap.getSourceCountryCode());
            cartWaresResp.setSkuImg(waresMap.getMkuImage());
            cartWaresResp.setSaleAttributes(waresMap.getSaleAttributeValues());
            //设置在池
            Boolean saleStatus = waresMap.getSaleStatus() != null && waresMap.getSaleStatus().equals(YnEnum.YES.getCode())?Boolean.TRUE:Boolean.FALSE;
            cartWaresResp.setInPool(saleStatus);
            //在池计算价格
            if (saleStatus && null != waresMap.getShowCurrency() && null != waresMap.getShowCurrency().getIncludeTaxPrice()) {
                //TODO 含税价为空时，做兜底处理，避免空指针异常
                cartWaresResp.setPrice(waresMap.getShowCurrency().getIncludeTaxPrice());
                cartWaresResp.setNotes(waresMap.getShowCurrency().getIncludeTaxPrice().multiply(new BigDecimal(wares.getSkuNum())));
                // 价格
                cartWaresResp.setShowCurrency(this.convertPriceDto(waresMap.getShowCurrency(), currencySymbolService.symbol(waresMap.getCurrency())));
                cartWaresResp.setCurrenciesPrices(this.convertPriceDtoMap(waresMap.getCurrenciesPrices()));
                this.convertWareNotes(cartWaresResp);
                cartWaresResp.setChecked(wares.getChecked());
            }
            if(cartWaresResp.getChecked()){
                //现货库存为0时取消选中
                if(wares.getRemainNum()!=null && wares.getRemainNum()==0){
                    cartWaresResp.setChecked(false);
                }
            }
            lowestBuy = waresMap.getMoq() != null ? waresMap.getMoq() : lowestBuy;
            cartWaresResp.setMoq(waresMap.getMoq());
            cartWaresResp.setMoqText(iscMkuClientApiService.getMoqText(waresMap.getMoq(), lang));
            cartWaresResp.setUnit(waresMap.getSaleUnit());
        }
        cartWaresResp.setLowestBuy(lowestBuy);
        cartWaresResp.setSaleState(DataTranslationUtils.buildSkuSaleStateResp(null, null));
        cartWaresResp.setMoq(lowestBuy);
        cartWaresResp.setRemainNum(wares.getRemainNum());
        cartWaresResp.setStockFlag(wares.getStockFlag());
        cartWaresResp.setStockStateType(wares.getStockStateType());
        if (skuFeature != null) {
            cartWaresResp.setFulfillmentModel(getFulfillmentModel(skuFeature.getPurchaseModel(), waresMap.getSourceCountryCode()));
        }
        if (iscMkuClientDeliveryAgingDTO != null) {
            BigDecimal maxDeliveryDays = iscMkuClientDeliveryAgingDTO.getMaxDeliveryDays();
            BigDecimal deliverGoodsDays = iscMkuClientDeliveryAgingDTO.getDeliverGoodsDays();
            if (maxDeliveryDays != null && deliverGoodsDays != null) {
                BigDecimal add = maxDeliveryDays.add(deliverGoodsDays);
                cartWaresResp.setFulfillmentDays(add.intValue());
            } else {
                cartWaresResp.setFulfillmentDays(0);
            }
        }
        return cartWaresResp;
    }

    private Integer getFulfillmentModel(Integer purchaseModel, String sourceCountryCode) {
        if (purchaseModel == 1) {
            // 仓发
            return 3;
        }
        if (CountryConstant.COUNTRY_ZH.equalsIgnoreCase(sourceCountryCode)) {
            // 跨境
            return 1;
        }
        // 本本
        return 2;
    }

    private List<SkuSaleAttrDTO> convertSkuSaleAttrResp(List<AttributeKeyAndValueDTO> saleAttributes) {
        if (saleAttributes == null || saleAttributes.isEmpty()) {
            return Collections.emptyList();
        }

        return saleAttributes.stream()
                .map(attribute -> {
                    SkuSaleAttrDTO resp = new SkuSaleAttrDTO();
                    // 使用 BeanUtils 将属性值复制过去
                    BeanUtils.copyProperties(attribute, resp);
                    return resp;
                })
                .collect(Collectors.toList());
    }


    private void convertWareNotes(CartWaresResp cartWaresResp) {
        MultiCurrencyPriceDTO multiCurrencyPriceDTO = new MultiCurrencyPriceDTO();
        ProductPricesDTO notesShow = new ProductPricesDTO();
        BeanUtils.copyProperties(cartWaresResp.getShowCurrency(), notesShow);
        BigDecimal skuNum = new BigDecimal(cartWaresResp.getSkuNum());
        notesShow.setShowPrice(cartWaresResp.getShowCurrency().getShowPrice().multiply(skuNum));
        notesShow.setPrice(cartWaresResp.getShowCurrency().getPrice().multiply(skuNum));
        notesShow.setSalePrice(cartWaresResp.getShowCurrency().getSalePrice().multiply(skuNum));
        notesShow.setValueAddedTax(Objects.nonNull(cartWaresResp.getShowCurrency().getValueAddedTax()) ? cartWaresResp.getShowCurrency().getValueAddedTax().multiply(skuNum) : BigDecimal.ZERO);
        notesShow.setIncludeTaxPrice(cartWaresResp.getShowCurrency().getIncludeTaxPrice().multiply(skuNum));
        multiCurrencyPriceDTO.setShowCurrency(notesShow);

        // 多币种
        Map<String, ProductPricesDTO> currenciesPrices = cartWaresResp.getCurrenciesPrices();
        Map<String, ProductPricesDTO> notesMap = Maps.newHashMap();
        currenciesPrices.forEach((k, v) -> {
            ProductPricesDTO notePriceDTO = new ProductPricesDTO();
            BeanUtils.copyProperties(v, notePriceDTO);
            notePriceDTO.setSalePrice(v.getSalePrice().multiply(skuNum));
            notePriceDTO.setShowPrice(v.getShowPrice().multiply(skuNum));
            notePriceDTO.setPrice(v.getPrice().multiply(skuNum));
            notePriceDTO.setValueAddedTax(Objects.nonNull(v.getValueAddedTax()) ? v.getValueAddedTax().multiply(skuNum) : BigDecimal.ZERO);
            notePriceDTO.setIncludeTaxPrice(v.getIncludeTaxPrice().multiply(skuNum));
            notesMap.put(k, notePriceDTO);
        });
        multiCurrencyPriceDTO.setCurrenciesPrices(notesMap);
        cartWaresResp.setNotesCurrencies(multiCurrencyPriceDTO);
    }

    private Map<String, ProductPricesDTO> convertPriceDtoMap(Map<String, ProductPricesDTO> map) {
        if (MapUtils.isEmpty(map)) {
            return Collections.emptyMap();
        }

        Map<String, String> symbols = currencySymbolService.symbols(map.keySet());
        Map<String, ProductPricesDTO> resultMap = Maps.newHashMap();
        map.forEach((k, v) -> resultMap.put(k, this.convertPriceDto(v, symbols.get(k))));
        return resultMap;
    }


    private ProductPricesDTO convertPriceDto(ProductPricesDTO pricesDTO, String symbol) {
        ProductPricesDTO productPricesDTO = new ProductPricesDTO();
        productPricesDTO.setSymbol(symbol);
        productPricesDTO.setCurrencySource(pricesDTO.getCurrencySource());
        productPricesDTO.setCurrency(pricesDTO.getCurrency());
//        productPricesDTO.setSalePrice(pricesDTO.getSalePrice());
        productPricesDTO.setExchangeRate(pricesDTO.getExchangeRate());
        productPricesDTO.setValueAddedTax(pricesDTO.getValueAddedTax());
        productPricesDTO.setValueAddedTaxRate(pricesDTO.getValueAddedTaxRate());
        productPricesDTO.setIncludeTaxPrice(pricesDTO.getIncludeTaxPrice());
        productPricesDTO.setSalePrice(pricesDTO.getIncludeTaxPrice());
        productPricesDTO.setShowPrice(pricesDTO.getIncludeTaxPrice());
        productPricesDTO.setPrice(pricesDTO.getPrice());
        return productPricesDTO;
    }

    public Map<String, Map<String, String>> getSkuSaleState(String pin, List<String> skuIds) {
        Map<String, Map<String, String>> res = new HashMap<>();
        String key = "cart:sku:saleState";
        List<String> skuSaleCaches = jimCacheService.hMGet(key, skuIds.stream().toArray(String[]::new));
        List<String> noCacheSkuIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(skuSaleCaches)) {
            for (int i = 0, size = skuSaleCaches.size(); i < size; i++) {
                String skuSaleCache = skuSaleCaches.get(i);
                if (StringUtils.isNotBlank(skuSaleCache)) {
                    res.put(skuIds.get(i), JSON.parseObject(skuSaleCache, Map.class));
                } else {
                    noCacheSkuIds.add(skuIds.get(i));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(noCacheSkuIds)) {
            Map<String, Map<String, String>> noCacheSkuSaleState = productQueryService.querySkuSaleState(pin, noCacheSkuIds);
            res.putAll(noCacheSkuSaleState);
            Map<String, String> cache = new HashMap<>();
            noCacheSkuSaleState.forEach((k, v) -> cache.put(k, JSON.toJSONString(v)));
            jimCacheService.setMapStr(key, cache);
            if (noCacheSkuIds.size() == skuIds.size()) {
                jimCacheService.expire(key, 2, TimeUnit.HOURS);
            }
        }
        return res;
    }

    /**
     * 获取商品价格
     *
     * @param skuInfos
     * @param pin
     * @return
     */
    private Map<String, PriceResp> getWaresPrice(List<PriceSkuInfoReq> skuInfos, String pin) {
//        NewestCurrencyRes usd = currencyService.queryNewestCurrency(CurrencyEnums.USD.getName());
//        BigDecimal exchangeRate = (usd != null && usd.getExchangeRate() != null) ? usd.getExchangeRate() : BigDecimal.ONE;
//        BigDecimal premium = (usd != null && usd.getPremium() != null) ? usd.getPremium() : BigDecimal.ONE;
//        return priceService.getPricesBySku(skuInfos, pin, exchangeRate, premium);
        return null;
    }

    /**
     * 获取商品信息
     *
     * @param skuIds
     * @param env
     * @return
     */
    private Map<String, Map<String, String>> getWares(Set<String> skuIds, String env) {
//        if(EN.equals(env)){
//
//        }else{
//            return rpcProductService.querySku(skuIds);
//        }
        // todo 查询ept
        return rpcProductService.querySku(skuIds);
    }

    /**
     * 获取最低起购量
     *
     * @param skuIds
     * @return
     */
    private Map<String, Integer> getLowestBuy(Set<String> skuIds) {
        Map<String, Integer> map = new HashMap<>();
        skuIds.forEach(o -> {
            map.put(o, 1);
        });
        return map;
//        return productQueryService.getLowestBuy(new ArrayList<>(skuIds));
    }

    /**
     * 包装购物车参数
     *
     * @param cart
     * @param cartWaresRespList
     * @param totalPrice
     * @return
     */
    private CartResp packageCart(Cart cart, List<CartWaresResp> cartWaresRespList, BigDecimal totalPrice,MultiCurrencyPriceDTO totalCurrencies) {
        CartResp cartResp = new CartResp();
        cartResp.setId(cart.getId());
        cartResp.setCode(cart.getCode());
        cartResp.setTotalCount(cart.getTotalCount());
        cartResp.setTotalCheckCount(cart.getTotalCheckCount());
        cartResp.setTotalKindCount(cart.getTotalKindCount());
        cartResp.setTotalCheckKindCount(cart.getTotalCheckKindCount());
        cartResp.setPin(cart.getPin());
        cartResp.setContractNum(cart.getContractNum());
        cartResp.setTotalPrice(totalPrice);
        cartResp.setCreated(cart.getCreated());
        cartResp.setCartWares(cartWaresRespList);
        cartResp.setTotalPriceCurrencies(totalCurrencies);
        return cartResp;
    }

    /**
     * 包装购物车商品参数
     *
     * @param wares
     * @param skuWares
     * @param prices
     * @return
     */
    private CartWaresResp packageCartWares(CartWares wares, Map<String, Map<String, String>> skuWares, Map<String, PriceResp> prices, Map<String, Integer> lowestBuy, Map<String, Map<String, String>> saleStateMap, Map<String, Map<String, Map<String, String>>> giftMap) {
        CartWaresResp cartWaresResp = new CartWaresResp();
        cartWaresResp.setId(wares.getId());
        cartWaresResp.setSku(wares.getSku());
        cartWaresResp.setSkuNum(wares.getSkuNum());
        cartWaresResp.setChecked(wares.getChecked());
        cartWaresResp.setAddCartPrice(wares.getAddCartPrice());
        cartWaresResp.setAddCartTime(wares.getAddCartTime());
        cartWaresResp.setPin(wares.getPin());
        if (MapUtils.isNotEmpty(skuWares)) {
            Map<String, String> waresMap = skuWares.get(wares.getSku());
            if (MapUtils.isNotEmpty(waresMap)) {
                cartWaresResp.setSkuName(waresMap.get("sku_name"));
                cartWaresResp.setSkuImg(waresMap.get("product_image"));
                cartWaresResp.setModel(waresMap.get("model"));
                cartWaresResp.setUnit(waresMap.get("unit"));
            }
        }
        if (MapUtils.isNotEmpty(lowestBuy)) {
            cartWaresResp.setLowestBuy(lowestBuy.get(wares.getSku()) != null ? lowestBuy.get(wares.getSku()) : 1);
        }
//        if(MapUtils.isNotEmpty(saleStateMap)){
        cartWaresResp.setSaleState(DataTranslationUtils.buildSkuSaleStateResp(null, null));
//        }
//        if(MapUtils.isNotEmpty(giftMap)){
//            cartWaresResp.setGifts(giftMap.get(wares.getSku()));
//        }
        cartWaresResp.setOriginalPrice(MapUtils.isNotEmpty(prices) && prices.containsKey(wares.getSku()) ? prices.get(wares.getSku()).getPrice() : BigDecimal.ZERO);
        cartWaresResp.setPrice(MapUtils.isNotEmpty(prices) && prices.containsKey(wares.getSku()) ? prices.get(wares.getSku()).getSellingPrice() : BigDecimal.ZERO);
        cartWaresResp.setNotes(cartWaresResp.getPrice().multiply(new BigDecimal(wares.getSkuNum())));
        cartWaresResp.setExchangeRate(MapUtils.isNotEmpty(prices) && prices.containsKey(wares.getSku()) ? prices.get(wares.getSku()).getExchangeRate() : BigDecimal.ZERO);
        return cartWaresResp;
    }

    /**
     * 获取购物车
     *
     * @param code
     * @param pin
     * @param contractNum
     * @return
     */
    private Cart getCartItem(String code, String pin, String contractNum) {
        Cart cart = cartMapper.getCart(code);
        if (cart == null) {
            synchronized (this) {
                if (cart == null) {
                    cart = new Cart();
                    cart.setPin(pin);
                    cart.setCode(code);
                    cart.setContractNum(contractNum);
                    cart.setCreated(new Date());
                    cartMapper.insert(cart);
                }
            }
        }
        return cart;
    }

    /**
     * <AUTHOR>
     * @Description //key为skuid value为{"ept_sku_id:xxxxxx,"sku_id":xxxxxx}
     * @Date 2023/8/10 14:22
     **/
    @PFTracing
    private HashMap<Long, Long> getEptSkuIdMap(List<Long> skuIds) {
        HashMap<Long, Long> skuIdEptIdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(skuIds)) {
            HashMap<Long, Map<String, Long>> skuIdObjMap = jdiIntlProductPushTaskShardingMapper.selectEptIdByJdSkuId(skuIds);
            for (Map.Entry<Long, Map<String, Long>> entry : skuIdObjMap.entrySet()) {
                skuIdEptIdMap.put(entry.getKey(), entry.getValue().get("ept_sku_id"));
            }
        }
        return skuIdEptIdMap;
    }

    /**
     * 填充物料信息
     * @param clientCode
     * @param cartWaresRespList
     */
    private void fillMaterialInfo(String clientCode, List<CartWaresResp> cartWaresRespList){
        IscBaseReqDTO baseInfo = new IscBaseReqDTO();
        baseInfo.setClientCode(clientCode);

        if (!authorityService.haseMaterialAuthority(baseInfo)){
            log.info("fillMaterialInfo, no MaterialAuthority. clientCode={}", clientCode);
            return;
        }

        if (CollectionUtils.isEmpty(cartWaresRespList)){
            log.info("fillMaterialInfo, cartWaresRespList empty. clientCode={}", clientCode);
            return;
        }

        try {
            Set<Long> mkuIds = new HashSet<>();
            cartWaresRespList.forEach(o->{
                mkuIds.add(Long.parseLong(o.getSku()));
            });

            if (CollectionUtils.isEmpty(mkuIds)){
                log.info("fillMaterialInfo, mkuIds empty. pin={}", baseInfo.getPin());
                return;
            }

            Map<Long, MkuMaterialApiDTO> mkuMaterialApiDTOMap = rpcIscMkuMaterialService.queryMkuMaterial(new ArrayList<>(mkuIds), clientCode);

            cartWaresRespList.forEach(o->{
                if (StringUtils.isNotBlank(o.getSku())){
                    Long skuId = Long.parseLong(o.getSku());
                    if (null!=mkuMaterialApiDTOMap.get(skuId)){
                        MkuMaterialResDTO orderMkuMaterialResDTO = MkuMaterialConvert.INSTANCE.mkuMaterialApiDTO2SoaCommonResDto(mkuMaterialApiDTOMap.get(skuId));
                        o.setMkuMaterial(orderMkuMaterialResDTO);
                    }
                }
            });

        } catch (Exception e) {
            log.error("",e);
        }
    }

    /**
     * 构建MKU可用销售请求对象。
     * @param mkuIds MKU的ID集合。
     * @param clientCode 客户代码。
     * @return IscMkuAvailableSaleReq对象。
     */
    private IscMkuAvailableSaleReq buildMkuAvailable(Set<Long> mkuIds,String clientCode){
        IscMkuAvailableSaleReq saleReq = new IscMkuAvailableSaleReq();
        saleReq.setClientCode(clientCode);
        saleReq.setMkuIds(mkuIds);
        return saleReq;
    }
}
