package com.jd.international.soa.service.adapter.delivery;

import com.jd.international.soa.sdk.common.isc.delivery.DeliveryAgingDTO;
import com.jdi.isc.aggregate.read.wisp.api.delivery.resp.DeliveryAgingResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 地址对象转换
 *
 * <AUTHOR>
 * @date 2024/03/06
 **/
@Mapper
public interface DeliveryConvert {

    DeliveryConvert INSTANCE = Mappers.getMapper(DeliveryConvert.class);


    DeliveryAgingDTO resp2Dto(DeliveryAgingResp input);



}
