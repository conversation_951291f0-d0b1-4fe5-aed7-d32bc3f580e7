package com.jd.international.soa.service.approval.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.international.soa.common.enums.SendMailTemplateEnum;
import com.jd.international.soa.common.enums.SendMsgStatusEnum;
import com.jd.international.soa.common.enums.YnEnum;
import com.jd.international.soa.dao.base.order.order.OrderApproveMsgMapper;
import com.jd.international.soa.domain.msg.order.IscOrderApproveInfo;
import com.jd.international.soa.domain.msg.order.IscOrderApproveMsg;
import com.jd.international.soa.domain.order.order.OrderApproveMsg;
import com.jd.international.soa.rpc.order.IscOrderReadRpcService;
import com.jd.international.soa.sdk.common.account.res.AccountInfoRes;
import com.jd.international.soa.sdk.order.orderList.res.OrderRes;
import com.jd.international.soa.service.adapter.order.OrderApproveMsgConvert;
import com.jd.international.soa.service.adapter.order.OrderConvert;
import com.jd.international.soa.service.approval.OrderApprovalService;
import com.jd.international.soa.service.approval.OrderApproveMsgService;
import com.jd.international.soa.service.common.account.AccountService;
import com.jd.international.soa.service.common.mail.MailService;
import com.jd.ka.mro.workflow.soa.sdk.enums.ApprovalStatusEnum;
import com.jd.ka.mro.workflow.soa.sdk.utils.ApproveStatus;
import com.jd.ka.mro.workflow.soa.sdk.vo.res.OrderApprovalNodeVO;
import com.jd.ka.mro.workflow.soa.sdk.vo.res.OrderApprovalProgressVO;
import com.jd.ump.annotation.JProfiler;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.aggregate.read.wisp.api.order.req.QueryOrderListReadReq;
import com.jdi.isc.aggregate.read.wisp.api.order.resp.OrderInfoReadResp;
import com.jdi.isc.order.center.api.constants.OrderStatusConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderApproveMsgServiceImpl implements OrderApproveMsgService {

    @Resource
    private OrderApproveMsgMapper orderApproveMsgMapper;

    @Autowired
    private MailService mailService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private IscOrderReadRpcService iscOrderReadRpcService;

    @Value("${order.msg.approveProgressDay}")
    private String approveProgressDayTime = "********";


    @Autowired
    private OrderApprovalService orderApprovalService;

    @Value("${spring.profiles.active}")
    private String active;


    @Override
    public boolean saveIscOrderApproveMsg(IscOrderApproveMsg iscOrderApproveMsg){
        List<IscOrderApproveInfo> iscOrderApproveInfoList = iscOrderApproveMsg.getIscOrderApproveInfoList();
        if(CollectionUtils.isEmpty(iscOrderApproveInfoList)){
            return true;
        }

        Date now = new Date();
        List<OrderApproveMsg> orderApproveMsgList = iscOrderApproveMsg.getIscOrderApproveInfoList().stream().map(orderApproveInfo -> {
            OrderApproveMsg orderApproveMsg = OrderApproveMsgConvert.INSTANCE.isc2orderApproveMsg(iscOrderApproveMsg);
            orderApproveMsg.setSendMailType(SendMailTemplateEnum.ORDER_APPROVE_PROGRESS.getType());
            orderApproveMsg.setOrderId(orderApproveInfo.getOrderId());
            orderApproveMsg.setContractNumber(orderApproveInfo.getContractNumber());
            orderApproveMsg.setRoleId(orderApproveInfo.getRoleId());
            orderApproveMsg.setSendEmailBeginTime(orderApproveMsg.getOperateTime() + Long.parseLong(approveProgressDayTime));
            orderApproveMsg.setSendEmailStatus(SendMsgStatusEnum.NOT_STARTED.getStatus());
            orderApproveMsg.setCreateTime(now);
            orderApproveMsg.setUpdateTime(now);
            orderApproveMsg.setYn(YnEnum.Y.getYn());
            return orderApproveMsg;
        }).collect(Collectors.toList());

        // 落库，用于T+1
        try{
            // TODO 查询
            int count = orderApproveMsgMapper.insertBatch(orderApproveMsgList);
            if(count <= 0){
                return false;
            }
        }catch (DuplicateKeyException e){
            log.error("发送邮件重复.OrderApproveMsgServiceImpl.saveIscOrderApproveMsg, iscOrderApproveMsg = {}, message = {}", JSON.toJSONString(iscOrderApproveMsg), e.getMessage());
        }catch (Exception e){
            log.error("发送邮件异常.OrderApproveMsgServiceImpl.saveIscOrderApproveMsg, iscOrderApproveMsg = {}", JSON.toJSONString(iscOrderApproveMsg), e);
            return false;
        }

        // 第一次发送邮件
        SendMsgStatusEnum sendMsgStatusEnum = this.sendEmail(iscOrderApproveMsg.getApprovePin(), orderApproveMsgList, false);
        if(SendMsgStatusEnum.FAIL.getStatus() == sendMsgStatusEnum.getStatus()){
            log.error("发送邮件异常.OrderApproveMsgServiceImpl.saveIscOrderApproveMsg, sendMsgStatusEnum = {}, iscOrderApproveMsg = {}", sendMsgStatusEnum, JSON.toJSONString(iscOrderApproveMsg));
            return false;
        }
        return true;

    }

    @JProfiler(jKey = "com.jd.international.soa.service.approval.impl.OrderApproveMsgServiceImpl.sendEmailJob")
    @Override
    public void sendEmailJob(){
        log.error("OrderApproveMsgServiceImpl.sendEmailJob, begin");
        OrderApproveMsg orderApproveMsg = new OrderApproveMsg();
        orderApproveMsg.setQuerySendEmailStatusSet(Sets.newHashSet(SendMsgStatusEnum.NOT_STARTED.getStatus(), SendMsgStatusEnum.FAIL.getStatus()));
        Date now = new Date();
        orderApproveMsg.setMaxSendEmailBeginTime(now.getTime());
        Integer count = orderApproveMsgMapper.queryOrderApproveMsgCount(orderApproveMsg);
        if(count == null || count == 0){
            log.info("OrderApproveMsgServiceImpl.sendEmailJob, count = {}", count);
            return ;
        }

        int pageSize = 20;
        int current = 0;
        int pageNo = 1;
        do{
            orderApproveMsg.setPageNo(pageNo);
            orderApproveMsg.setPageSize(pageSize);
            try{
                List<OrderApproveMsg> orderApproveMsgList = orderApproveMsgMapper.queryOrderApproveMsgListPage(orderApproveMsg);
                Map<String, List<OrderApproveMsg>> approvePinList = orderApproveMsgList.stream().collect(Collectors.groupingBy(OrderApproveMsg::getApprovePin));
                approvePinList.entrySet().forEach(entry -> {
                    processSendEmail(entry.getKey(), entry.getValue());
                });

            }catch (Exception e){
                Profiler.businessAlarm("OrderApproveMsgServiceImpl.sendEmailJob.exception."+active, active+LevelCode.P1.getMessage() + "WISP发送待审核邮件提醒异常");
                log.error("OrderApproveMsgServiceImpl.sendEmailJob, req = {}", orderApproveMsg, e);
            }

            current += pageSize;
            pageNo++;
            log.error("OrderApproveMsgServiceImpl.sendEmailJob, current = {}", current);
        }while (current < count);
        log.error("OrderApproveMsgServiceImpl.sendEmailJob, end, count = {}", count);
    }



    private void processSendEmail(String approvePin, List<OrderApproveMsg> orderApproveMsgList){
        try {
            // id, redis加锁 TODO
            // 发送邮件
            SendMsgStatusEnum sendMsgStatusEnum = sendEmail(approvePin, orderApproveMsgList, true);
            Date now = new Date();
            if(sendMsgStatusEnum.getStatus() == SendMsgStatusEnum.FAIL.getStatus()){
                // 发送失败，更新数据库状态为失败, retry次数+1，更新修改时间。
                orderApproveMsgList.forEach(orderApproveMsg -> {
                    orderApproveMsg.setSendEmailStatus(sendMsgStatusEnum.getStatus());
                    orderApproveMsg.setSendEmailRetryCount(orderApproveMsg.getSendEmailRetryCount() + 1);
                    orderApproveMsg.setUpdater("system");
                    orderApproveMsg.setUpdateTime(now);
                });
            }else{
                // 发送成功，或不需要修改
                orderApproveMsgList.forEach(orderApproveMsg -> {
                    orderApproveMsg.setSendEmailStatus(sendMsgStatusEnum.getStatus());
                    orderApproveMsg.setUpdater("system");
                    orderApproveMsg.setUpdateTime(now);
                });
            }
            orderApproveMsgMapper.batchUpdateOrderApproveMsgById(orderApproveMsgList);
        }catch (Exception e){
            Profiler.businessAlarm("OrderApproveMsgServiceImpl.sendEmailJob.exception."+active, active+LevelCode.P1.getMessage() + String.format("WISP发送待审核邮件提醒异常approvePin:%s", approvePin));
            log.error("发送待审核邮件提醒异常-OrderApproveMsgServiceImpl.sendEmail, approvePin = {}, orderIds = {}", approvePin, JSON.toJSONString(orderApproveMsgList), e);
        }finally {
            // 解锁 TODO
        }
    }

    private SendMsgStatusEnum sendEmail(String approvePin, List<OrderApproveMsg> orderApproveMsgList, boolean checkApproveStatus){
        log.info("sendMail, approvePin = {}, orderApproveMsgList = {}, checkApproveStatus={}"
                , approvePin, JSON.toJSONString(orderApproveMsgList), checkApproveStatus);
        Map<Long, OrderApproveMsg> orderApproveMsgMap = orderApproveMsgList.stream().collect(Collectors.toMap(OrderApproveMsg::getOrderId, Function.identity()));
        Set<Long> orderIds = orderApproveMsgMap.keySet();

        Set<String> approvePins = new HashSet<>(Arrays.asList(approvePin.split(",")));
        List<AccountInfoRes> accountInfoResList = new ArrayList<>();
        for(String pin:approvePins){
            AccountInfoRes accountInfoDTO = accountService.queryAccountInfoByPin(pin);
            if(accountInfoDTO == null || StringUtils.isBlank(accountInfoDTO.getEmail())){
                log.warn("发送待审核邮箱提醒异常-OrderApproveMsgServiceImpl.sendEmail, account null or not bind email. pin = {}", pin);
                continue;
            }
            accountInfoResList.add(accountInfoDTO);
        }
        if (CollectionUtils.isEmpty(accountInfoResList)){
            log.warn("发送待审核邮箱提醒异常-OrderApproveMsgServiceImpl.sendEmail, account null or not bind email. pin = {}", approvePin);
            Profiler.businessAlarm("OrderApproveMsgServiceImpl.sendEmailJob.exception."+active
                    , active+LevelCode.P1.getMessage() + String.format("WISP发送订单邮件提醒异常。orderIds=%s", JSONObject.toJSONString(orderIds)));
            // 没有可发送的邮件，视为成功
            return SendMsgStatusEnum.SUCCESS;
        }

        QueryOrderListReadReq orderListReadReq = new QueryOrderListReadReq();
        orderListReadReq.setOrderIds(Lists.newArrayList(orderApproveMsgMap.keySet()));
        orderListReadReq.setIndex(1L);
        orderListReadReq.setSize(100L);
        List<OrderInfoReadResp> orderInfoReadResps = iscOrderReadRpcService.queryOrderList(orderListReadReq);
        List<OrderRes> orderList = orderInfoReadResps.stream()
                .map(orderInfoReadResp -> {
                    OrderRes orderRes = OrderConvert.INSTANCE.iscOrderReq2OrderRes(orderInfoReadResp);
                    orderRes.setOrderNo(String.valueOf(orderInfoReadResp.getOrderId()));
                    orderRes.setOrderTime(new Date(orderInfoReadResp.getOrderCreateTime()));
                    if(orderInfoReadResp.getOrderPrice() != null){
                        orderRes.setTotalPrice(new BigDecimal(orderInfoReadResp.getOrderPrice().getOrderTotalPrice()));
                        orderRes.setCurrency(orderInfoReadResp.getOrderPrice().getCurrency());
                    }
                    return orderRes;
                }).collect(Collectors.toList());
        if(checkApproveStatus){
            // 校验
            orderList = orderList.stream().filter(order -> order.getOrderStatus().intValue() == OrderStatusConstant.USER_CONFIRM.getStatus().intValue())
                    .filter(order ->{
                        // 根据订单ids查询仍在审批中的审批流的roleIds
                        OrderApprovalProgressVO progressVO = orderApprovalService.getProgressVO(order.getContractNum(), Long.valueOf(order.getOrderNo()), LangConstant.LANG_ZH);
                        if(progressVO == null){
                            String msg = active + LevelCode.P1.getMessage() + String.format("发送邮件，未查询到审批流信息 contractNum = %s, orderId = %s", order.getContractNum(), Long.valueOf(order.getOrderNo()));
                            log.error(msg);
                            Profiler.businessAlarm("OrderApproveMsgServiceImpl.sendEmail_progressVO_is_null."+active, msg);
                            return false;
                        }
                        if(ApprovalStatusEnum.PROCESSING.getStatus() != progressVO.getApprovalStatus()){
                            log.info("发送邮件，未在审批中");
                            return false;
                        }
                        Optional<OrderApprovalNodeVO> reduce = progressVO.getNodeVOList().stream().filter(node -> node.getNodeStatus() == 0).reduce((a, b) -> b);
                        OrderApproveMsg orderApproveMsg = orderApproveMsgMap.get(Long.valueOf(order.getOrderNo()));
                        log.info("发送邮件，审批节点信息,nodeList = {}", JSON.toJSONString(JSON.toJSONString(progressVO.getNodeVOList())));
                        if (reduce.isPresent() && reduce.get().getRoleId().longValue() == Long.valueOf(orderApproveMsg.getRoleId()).longValue()) {
                            return true;
                        }
                        return false;
                    })
                    .collect(Collectors.toList());
        }

        if(CollectionUtils.isEmpty(orderList)){
            log.error("OrderApproveMsgServiceImpl.sendEmail, no need send email, approvePin = {}, orderIds = {}, ", approvePin, orderApproveMsgMap.keySet());
            return SendMsgStatusEnum.NO_NEED;
        }

        boolean result = mailService.buildOrderApproveProgressBatch(accountInfoResList, orderList);
        return result ? SendMsgStatusEnum.SUCCESS : SendMsgStatusEnum.FAIL;
    }


    public boolean sendPassOrRejectEmail(IscOrderApproveMsg iscOrderApproveMsg){
        List<Long> orderIds = new ArrayList<>();
        try {
            AccountInfoRes accountInfoDTO = accountService.queryAccountInfoByPin(iscOrderApproveMsg.getOrderPin());
            if(accountInfoDTO == null || StringUtils.isBlank(accountInfoDTO.getEmail())){
                log.error("发送审核结果邮箱提醒异常-OrderApproveMsgServiceImpl.sendPassOrRejectEmail, approvePin = {}, 未绑定邮箱", JSON.toJSONString(iscOrderApproveMsg));
                return true;
            }
            orderIds = iscOrderApproveMsg.getIscOrderApproveInfoList().stream().map(IscOrderApproveInfo::getOrderId).collect(Collectors.toList());
            QueryOrderListReadReq orderListReadReq = new QueryOrderListReadReq();
            orderListReadReq.setOrderIds(orderIds);
            orderListReadReq.setIndex(1L);
            orderListReadReq.setSize(100L);
            List<OrderInfoReadResp> orderInfoReadResps = iscOrderReadRpcService.queryOrderList(orderListReadReq);
            if(CollectionUtils.isEmpty(orderInfoReadResps)){
                log.error("发送审核结果邮箱提醒-OrderApproveMsgServiceImpl.sendPassOrRejectEmail, orderInfoReadResps is empty, msg = {}", JSON.toJSONString(iscOrderApproveMsg));
                return true;
            }
            List<OrderRes> orderResList = OrderConvert.INSTANCE.iscOrderReq2OrderResList(orderInfoReadResps);
            if(iscOrderApproveMsg.getApproveStatus() == ApproveStatus.APPROVED.getType()){
                // 通过
                return mailService.buildOrderApprovePass(accountInfoDTO.getEmail(), iscOrderApproveMsg.getOrderPin(), iscOrderApproveMsg.getApprovePin(), orderResList);
            }else{
                return mailService.buildOrderApproveReject(accountInfoDTO.getEmail(), iscOrderApproveMsg.getOrderPin(), iscOrderApproveMsg.getApprovePin(), orderResList);
            }
        }catch (Exception e){
            Profiler.businessAlarm("OrderApproveMsgServiceImpl.sendEmailJob.exception."+active, active+LevelCode.P1.getMessage() + String.format("WISP发送待审核邮件提醒异常orderIds:%s, orderPin:%s", orderIds, iscOrderApproveMsg.getOrderPin()));
            log.error("发送审核结果邮箱提醒异常-OrderApproveMsgServiceImpl.sendEmail, iscOrderApproveMsg = {}", iscOrderApproveMsg, e);
        }
        return false;
    }

}
