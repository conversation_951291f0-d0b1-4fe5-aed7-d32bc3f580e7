package com.jd.international.soa.service.chatGpt.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.international.soa.common.constants.UmpKeyConstant;
import com.jd.international.soa.common.utils.HttpUtils;
import com.jd.international.soa.domain.chatGpt.ChatGptMsgReqDto;
import com.jd.international.soa.domain.chatGpt.ChatGptReqDto;
import com.jd.international.soa.service.chatGpt.ChatGptService;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * ChatGpt服务
 *
 * <AUTHOR>
 * @date 20240625
 * @see <a href="https://joyspace.jd.com/page/q9AY6VwZTxholVXl0E7D">api</a>
 */
@Service
@Slf4j
public class ChatGptServiceImpl implements ChatGptService {

    /**
     * 配置文件 chatGpt翻译url
     */

    @Value("${chatgpt.url}")
    private String url;

    /**
     * ducc chatGpt翻译key集合
     */
    @LafValue("${chatgpt.chatGptKeys}")
    private String chatGptKeys;

    /**
     * ducc chatGpt翻译key集合
     */
    @LafValue("${chatgpt.model}")
    private String chatGptModel;

    /**
     * ducc chatGpt翻译失败内容超长限制字符长度
     */
//    @LafValue("chatGptContentLength")
//    private Integer chatGptContentLength;

    @Value("${spring.profiles.active}")
    protected String systemProfile;


    @Override
    public String postGpt(String content) {
        return postGpt(content,null);
    }

    @Override
    public String postGpt(String content, String modelName) {
        ChatGptReqDto reqDto = new ChatGptReqDto();
        List<ChatGptMsgReqDto> msgReqDtos = new ArrayList<>();
        ChatGptMsgReqDto msgReqDto = new ChatGptMsgReqDto();
        msgReqDto.setRole("system");
        msgReqDto.setContent(content);
        msgReqDtos.add(msgReqDto);
        if(StringUtils.isBlank(modelName)) {
            reqDto.setModel(chatGptModel);
        }else {
            reqDto.setModel(modelName);
        }
        reqDto.setMessages(msgReqDtos);
        return this.postGpt(reqDto);
    }

    @Override
    public String postGpt(ChatGptReqDto input) {
//        CallerInfo callerInfo = Profiler.registerInfo(systemProfile + "-"+UmpKeyConstant.CHATGPT_TRANSLATE_WARNING);
        CallerInfo callerInfo = Profiler.registerInfo(systemProfile + "-ChatGptServiceImpl.postGpt");
        String resultStr = null;
        try {
            List<String> keyList = getKey(chatGptKeys);
            if (CollectionUtils.isEmpty(keyList)) {
                log.error("ChatGptServiceImpl.postGpt keyList is null");
                return null;
            }

            for(String keyParam : keyList){
                if (StringUtils.isBlank(keyParam)) {
                    return null;
                }

                resultStr = this.postGpt(input,keyParam);

                Map<String,String> parseObj = this.parseResult(resultStr);

                if(MapUtil.isNotEmpty(parseObj) && parseObj.get("isRepeat").equals("0")){
                    return parseObj.get("text");
                }
            }

        } catch (Exception e) {
            log.error("【系统异常】ChatGptServiceImpl.postGpt error",e);
            Profiler.businessAlarm(UmpKeyConstant.CHATGPT_TRANSLATE_WARNING, String.format("【%s】 %s 大模型翻译异常, 异常信息:%s"
                , systemProfile
                , LevelCode.P1.getMessage()
                , e.getMessage()));
            Profiler.functionError(callerInfo);
        }finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return null;
    }

    /**
     * 请求GPT
     * */
    private String postGpt(ChatGptReqDto input, String key) {
        String result = null;
        try {
            Map<String,String> headers = new HashMap<>();
            headers.put("Authorization",key);
            headers.put("Content-type", "application/json;charset=utf-8");
            log.info("Http Request url:{},headers:{},body:{}",url,JSONObject.toJSONString(headers),JSONObject.toJSONString(input));
            result = HttpUtils.builder().postJSON(url, headers,null,input);
            log.info("Http Response result:{}",result);
        } catch (Exception e) {
            log.error("【系统异常】ChatGptServiceImpl.postGpt postGpt result is error",e);
        }
        return result;
    }

    /**
     * 解析结果
     * */
    private Map<String,String> parseResult(String result){
        Map<String,String> resultCurrent = new HashMap<>();
        resultCurrent.put("isRepeat","0");
        if(StringUtils.isBlank(result)){
            log.error("ChatGptServiceImpl.postGpt postGpt result is null");
            return resultCurrent;
        }
        JSONObject jResult = JSONObject.parseObject(result);
        if(jResult == null){
            log.error("ChatGptServiceImpl.postGpt postGpt jResult is null");
            return resultCurrent;
        }

        if(jResult.containsKey("error")){
            log.error("ChatGptServiceImpl.postGpt postGpt error");
            resultCurrent.put("isRepeat","1");
            return resultCurrent;
        }

        JSONArray choices = jResult.getJSONArray("choices");
        JSONObject choice = choices.getJSONObject(0);
        String message = choice.getString("message");
        if(StringUtils.isBlank(message)){
            log.error("ChatGptServiceImpl.postGpt postGpt message is null");
            return resultCurrent;
        }

        String content = JSONObject.parseObject(message).getString("content");
        if(StringUtils.isBlank(content)){
            log.error("ChatGptServiceImpl.postGpt content is null");
            return resultCurrent;
        }

        resultCurrent.put("text",content);
        return resultCurrent;
    }

    /**
     * 获取key
     *
     * @param keys key集合
     * @return
     */
    private List<String> getKey(String keys) {
        List<String> keyList = new ArrayList<>();
        if (StringUtils.isNotBlank(keys)) {
            keyList = Arrays.asList(keys.split(","));
        }
        return keyList;
    }

}

