<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jd.international.soa</groupId>
        <artifactId>international-soa</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>international-soa-service</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-soa-rpc</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jdi-isc-aggregate-read-wisp-api</artifactId>
                    <groupId>com.jdi.isc.aggregate.read</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-soa-dao</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-order-sdk</artifactId>
            <version>${international.order.sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-wares-sdk</artifactId>
            <version>${international.wares.sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-approval-sdk</artifactId>
            <version>${international.approval.sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.aggregate.read</groupId>
            <artifactId>jdi-isc-aggregate-read-wisp-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jmq</groupId>
            <artifactId>jmq2-client-springboot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.security</groupId>
            <artifactId>tdeclient</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.security</groupId>
            <artifactId>aces-springclient</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.jd.security</groupId>
                    <artifactId>tdeclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.security</groupId>
                    <artifactId>tdecommon</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.library</groupId>
            <artifactId>jdi-isc-library-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-common-sdk</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.library</groupId>
            <artifactId>jdi-isc-library-i18n-datasource</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-elasticsearch</artifactId>
        </dependency>
    </dependencies>
</project>