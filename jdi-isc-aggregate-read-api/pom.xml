<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jdi.isc.aggregate.read</groupId>
    <artifactId>jdi-isc-aggregate-read-api</artifactId>
<!--    <version>0.0.25-SNAPSHOT</version>-->
    <version>1.1.4${lib.version}</version>

    <name>jdi-isc-aggregate-read-api</name>
    <packaging>jar</packaging>

    <properties>
        <java.version>1.8</java.version>
        <encoding>UTF-8</encoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencies>
        <!-- 校验 -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>6.0.18.Final</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83-jdsec.rc1</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <artifactId>common-domain</artifactId>
            <groupId>com.jdi.common</groupId>
            <version>2.0.0</version>
        </dependency>

    </dependencies>


    <distributionManagement>
        <repository>
            <id>libs-releases-local</id>
            <name>Release Repository</name>
            <url>http://artifactory.jd.com/libs-releases-local</url>
        </repository>
        <snapshotRepository>
            <id>libs-snapshots-local</id>
            <name>Snapshot Repository</name>
            <url>http://artifactory.jd.com/libs-snapshots-local</url>
        </snapshotRepository>
    </distributionManagement>

<profiles>
    <profile>
        <id>test</id>
        <properties>
            <lib.version>-test-SNAPSHOT</lib.version>
            <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
            <old.order.version>0</old.order.version>
        </properties>
    </profile>
    <profile>
        <id>dev</id>
        <properties>
            <lib.version>-test-SNAPSHOT</lib.version>
            <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
            <old.order.version>0</old.order.version>
        </properties>
        <activation>
            <activeByDefault>true</activeByDefault>
        </activation>
    </profile>
    <profile>
        <id>uat</id>
        <properties>
            <lib.version>-SNAPSHOT</lib.version>
            <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
            <old.order.version>0</old.order.version>
        </properties>
    </profile>
    <profile>
        <id>prod</id>
        <properties>
            <!-- 生产环境版本 -->
            <lib.version></lib.version>
            <!-- 订单中心0522前的版本管理方式,切release后应保持和lib.version类似方式 -->
            <old.order.version>1</old.order.version>
        </properties>
    </profile>
</profiles>

</project>
