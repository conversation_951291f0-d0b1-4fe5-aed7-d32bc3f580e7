package com.jdi.isc.product.soa.web.controller.devpos;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.jd.gms.greatdane.category.request.GetCategorySaleAttParam;
import com.jd.gms.greatdane.category.service.read.CategorySaleAttReadService;
import com.jd.gms.greatdane.category.request.GetCategorySaleAttByCategoryIdsParam;
import com.jd.gms.greatdane.category.domain.CategorySaleAtt;
import com.jd.gms.greatdane.response.GreatDaneResult;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.devOps.DevOpsApiService;
import com.jdi.isc.product.soa.api.devOps.DevOpsJDSkuExtendAttribueService;
import com.jdi.isc.product.soa.api.devOps.DevOpsSaleApiService;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.spu.biz.ExtendPropertyGroupVO;
import com.jdi.isc.product.soa.domain.spu.biz.ShotPropertyVO;
import com.jdi.isc.product.soa.repository.mapper.sku.SkuDraftBaseMapper;
import com.jdi.isc.product.soa.repository.mapper.spu.SpuDraftBaseMapper;
import com.jdi.isc.product.soa.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.product.soa.rpc.gms.BaseGmsClientService;
import com.jdi.isc.product.soa.service.manage.attribute.AttributeOutService;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeFlatVO;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDraftAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuDraftAtomicService;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.domain.sku.po.SkuDraftPO;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuTransferCategoryManageService;
import com.jdi.isc.product.soa.domain.spu.biz.TransferCategoryReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/devops")
public class DevOpsController {

    @Autowired
    private DevOpsApiService devOpsApiService;

    @Autowired
    private DevOpsJDSkuExtendAttribueService devOpsJDSkuExtendAttribueService;
    
    @Autowired
    private DevOpsSaleApiService devOpsSaleApiService;
    
    @Resource
    private CategorySaleAttReadService categorySaleAttReadService;
    
    @Resource
    private SkuInfoRpcService skuInfoRpcService;
    
    @Autowired
    private BaseGmsClientService baseGmsClientService;
    
    @Resource
    private AttributeOutService attributeOutService;
    
    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;
    
    @Resource
    private SkuDraftAtomicService skuDraftAtomicService;

    @Resource
    private SaleAttributeManageService saleAttributeManageService;

    @Resource
    private SpuTransferCategoryManageService spuTransferCategoryManageService;
    
    private final String token = "dDFSADF111";

    @GetMapping("attr/spu/refresh")
    public String spuAttrRefresh(String updateTime, String token) {
        if (token.equals(this.token)) {
            devOpsApiService.processAllSpuExtAttributes(updateTime);
            return "success";
        } else {
            return "token error";
        }
    }

    @GetMapping("attr/mku/refresh")
    public String mkuAttrRefresh(String updateTime, String token) {
        if (token.equals(this.token)) {
            devOpsApiService.processAllMkuExtAttributes(updateTime);
            return "success";
        } else {
            return "token error";
        }
    }

    @GetMapping("attr/spu/draft/refresh")
    public String spuDraftAttrRefresh(String updateTime, String token) {
        if (token.equals(this.token)) {
            devOpsApiService.processAllSpuDraftExtProperties(updateTime);
            return "success";
        } else {
            return "token error";
        }
    }

    @GetMapping("attr/spu/refresh/{spuId}")
    public String spuAttrRefresh(@PathVariable Long spuId, String updateTime, String token) {
        if (token.equals(this.token)) {
            devOpsApiService.processSpuExtAttributesBySpuId(spuId);
            return "success";
        } else {
            return "token error";
        }
    }

    @GetMapping("attr/spu/draft/refresh/{spuId}")
    public String spuDraftAttrRefresh(@PathVariable Long spuId, String updateTime, String token) {
        if (token.equals(this.token)) {
            devOpsApiService.processSpuDraftExtAttributesBySpuId(spuId);
            return "success";
        } else {
            return "token error";
        }
    }

    @GetMapping("attr/mku/refresh/{mkuId}")
    public String mkuAttrRefresh(@PathVariable Long mkuId, String updateTime, String token) {
        if (token.equals(this.token)) {
            devOpsApiService.processMkuExtAttributesByMkuId(mkuId);
            return "success";
        } else {
            return "token error";
        }
    }

    /**
     * 测试接口：调用categorySaleAttReadService.getCategorySaleAttListByCategoryIds
     * curl 'http://localhost:8080/product/devops/test/category/saleAttr/1?token=dDFSADF111'
     * @param categoryId 类目ID
     * @param token 验证token
     * @return JSON格式的类目销售属性数据
     */
    @GetMapping("test/category/saleAttr/{categoryId}")
    public String testGetCategorySaleAttributes(@PathVariable Integer categoryId, String token) {
        if (!token.equals(this.token)) {
            return "token error";
        }
        
        log.info("DevOpsController.testGetCategorySaleAttributes 调用测试，categoryId: {}", categoryId);
        
        try {
            GetCategorySaleAttByCategoryIdsParam param = new GetCategorySaleAttByCategoryIdsParam();
            param.setClientInfo(baseGmsClientService.buildClientInfo());
            param.setCategoryIds(Sets.newHashSet(categoryId));
            
            GreatDaneResult<Map<Integer, List<CategorySaleAtt>>> result = categorySaleAttReadService.getCategorySaleAttListByCategoryIds(param);
            
            log.info("DevOpsController.testGetCategorySaleAttributes 调用结果，categoryId: {}, result: {}", categoryId, JSON.toJSONString(result));
            
            return JSON.toJSONString(result);
        } catch (Exception e) {
            log.error("DevOpsController.testGetCategorySaleAttributes 调用失败，categoryId: {}", categoryId, e);
            return "error: " + e.getMessage();
        }
    }

    /**
     * 测试接口：调用skuInfoRpcService.getSkuById
     * curl 'http://localhost:8080/product/devops/test/sku/80000000044?token=dDFSADF111'
     * @param skuId SKU ID
     * @param token 验证token
     * @return JSON格式的SKU数据
     */
    @GetMapping("test/sku/{skuId}")
    public String testGetSkuById(@PathVariable Long skuId, String token) {
        if (!token.equals(this.token)) {
            return "token error";
        }
        
        log.info("DevOpsController.testGetSkuById 调用测试，skuId: {}", skuId);
        
        try {
            JdProductDTO jdProductDTO = skuInfoRpcService.getSkuById(skuId);
            
            log.info("DevOpsController.testGetSkuById 调用结果，skuId: {}, result: {}", skuId, JSON.toJSONString(jdProductDTO));
            
            return JSON.toJSONString(jdProductDTO);
        } catch (Exception e) {
            log.error("DevOpsController.testGetSkuById 调用失败，skuId: {}", skuId, e);
            return "error: " + e.getMessage();
        }
    }

    /**
     * 测试接口：调用categorySaleAttReadService.getCategorySaleAtt
     * curl 'http://localhost:8080/product/devops/test/category/saleAtt/single/1?token=dDFSADF111'
     * @param categoryId 类目ID
     * @param token 验证token
     * @return JSON格式的类目销售属性数据
     */
    @GetMapping("test/category/saleAtt/single/{categoryId}/{venderCode}")
    public String testGetCategorySaleAtt(@PathVariable Integer categoryId, @PathVariable String venderCode, String token) {
        if (!token.equals(this.token)) {
            return "token error";
        }
        
        log.info("DevOpsController.testGetCategorySaleAtt 调用测试，categoryId: {}", categoryId);
        
        try {
            // 构建参数对象，参考现有的GetCategorySaleAttByCategoryIdsParam结构
            GetCategorySaleAttParam param = new GetCategorySaleAttParam();
            param.setClientInfo(baseGmsClientService.buildClientInfo());
            param.setCategoryId(categoryId);
            param.setVenderCode(venderCode);
            
            // 调用getCategorySaleAtt方法
            Object result = categorySaleAttReadService.getCategorySaleAtt(param);
            
            log.info("DevOpsController.testGetCategorySaleAtt 调用结果，categoryId: {}, result: {}", categoryId, JSON.toJSONString(result));
            
            return JSON.toJSONString(result);
        } catch (Exception e) {
            log.error("DevOpsController.testGetCategorySaleAtt 调用失败，categoryId: {}", categoryId, e);
            return "error: " + e.getMessage();
        }
    }

    /**
     * 处理全量京东SKU扩展属性（支持指定起始页码）
     * curl 'http://localhost:8080/product/devops/attr/jd-sku/refresh/page?writeToDB=false&pageIndex=5&useCache=true&token=dDFSADF111'
     */
    @GetMapping("attr/jd-sku/refresh/page")
    public String jdSkuAttrRefreshWithPageIndex(@RequestParam(defaultValue = "false") Boolean writeToDB,
                                                @RequestParam(defaultValue = "1") Long pageIndex,
                                                @RequestParam(defaultValue = "true") Boolean useCache,
                                                @RequestParam String token) {
        if(token.equals(this.token)) {
            try {
                devOpsJDSkuExtendAttribueService.processAllJDSkuExtAttributes(writeToDB, pageIndex,useCache);
                log.info("全量处理京东SKU扩展属性完成, writeToDB: {}, pageIndex: {}", writeToDB, pageIndex);
                return "success";
            } catch (Exception e) {
                log.error("全量处理京东SKU扩展属性异常", e);
                return "error: " + e.getMessage();
            }
        } else {
            return "token error";
        }
    }

    /**
     * 处理单个京东SKU ID的扩展属性
     * curl 'http://localhost:8080/product/devops/attr/jd-sku/refresh/100004205573?writeToDB=false&useCache=true&token=dDFSADF111'
     */
    @GetMapping("attr/jd-sku/refresh/{jdSkuId}")
    public String jdSkuAttrRefresh(@PathVariable Long jdSkuId,
                                   @RequestParam(defaultValue = "false") Boolean forceCover,
                                   @RequestParam(defaultValue = "false") Boolean writeToDB,
                                   @RequestParam(defaultValue = "true") Boolean useCache,
                                   @RequestParam String token) {
        if(token.equals(this.token)) {
            try {
                devOpsJDSkuExtendAttribueService.processJDSkuExtAttributesByJdSkuId(jdSkuId, forceCover, writeToDB,useCache);
                log.info("单个京东SKU {} 扩展属性处理完成, writeToDB: {}", jdSkuId, writeToDB);
                return "success";
            } catch (Exception e) {
                log.error("单个京东SKU {} 扩展属性处理异常", jdSkuId, e);
                return "error: " + e.getMessage();
            }
        } else {
            return "token error";
        }
    }

    /**
     * 根据京东SKU ID查询扩展属性
     * URL: /devops/attr/jd-sku/query/{jdSkuId}?token=dDFSADF111
     */
    @GetMapping("attr/jd-sku/query/{jdSkuId}")
    public DataResponse<String> queryJdSkuExtAttributes(@PathVariable Long jdSkuId,
                                                        @RequestParam String token) {
        if(!token.equals(this.token)) {
            return DataResponse.error("token error");
        }

        try {
            if (jdSkuId == null) {
                return DataResponse.error("京东SKU ID不能为空");
            }

            // 获取京东SKU的末级类目
            com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO jdProductDTO = skuInfoRpcService.getSkuById(jdSkuId);
            if (jdProductDTO == null || jdProductDTO.getUnLimitCid() == null) {
                return DataResponse.error("京东SKU " + jdSkuId + " 获取类目信息失败");
            }

            Long unLimitCid = Long.valueOf(jdProductDTO.getUnLimitCid());

            // 获取京东该SKU的扩展属性
            List<com.jdi.isc.product.soa.domain.spu.biz.PropertyVO> propertyVOList =
                    attributeOutService.obtainJDSkuStoreExtAttributeList(unLimitCid, jdSkuId);

            if (org.apache.commons.collections4.CollectionUtils.isEmpty(propertyVOList)) {
                return DataResponse.success("京东SKU " + jdSkuId + " 没有扩展属性");
            }

            // 返回JSON格式的扩展属性信息
            String resultJson = JSON.toJSONString(propertyVOList);

            log.info("查询京东SKU {} 扩展属性完成, 类目ID: {}, 属性: {}", jdSkuId, unLimitCid, resultJson);

            return DataResponse.success(resultJson);

        } catch (Exception e) {
            log.error("查询京东SKU {} 扩展属性异常", jdSkuId, e);
            return DataResponse.error("查询京东SKU扩展属性失败: " + e.getMessage());
        }
    }

    /**
     * 测试接口：调用categorySaleAttReadService.getSaleAttributeByCategoryIds
     * curl 'http://localhost:8080/product/devops/test/category/saleAttr/byCategoryIds?categoryIds=1,2,3&token=dDFSADF111'
     * @param categoryIds 类目ID列表（逗号分隔）
     * @param token 验证token
     * @return JSON格式的销售属性数据
     */
    @GetMapping("test/category/saleAttr/byCategoryIds")
    public String testGetSaleAttributeByCategoryIds(@RequestParam String categoryIds, String token) {
        if (!token.equals(this.token)) {
            return "token error";
        }
        
        log.info("DevOpsController.testGetSaleAttributeByCategoryIds 调用测试，categoryIds: {}", categoryIds);
        
        try {
            // 解析类目ID列表
            String[] categoryIdArray = categoryIds.split(",");
            Set<Integer> categoryIdSet = new HashSet<>();
            for (String categoryIdStr : categoryIdArray) {
                try {
                    categoryIdSet.add(Integer.parseInt(categoryIdStr.trim()));
                } catch (NumberFormatException e) {
                    log.warn("DevOpsController.testGetSaleAttributeByCategoryIds 无效的类目ID: {}", categoryIdStr);
                }
            }
            
            if (categoryIdSet.isEmpty()) {
                return "error: 无有效的类目ID";
            }
            
            // 构建参数对象
            GetCategorySaleAttByCategoryIdsParam param = new GetCategorySaleAttByCategoryIdsParam();
            param.setClientInfo(baseGmsClientService.buildClientInfo());
            param.setCategoryIds(categoryIdSet);
            
            // 调用getSaleAttributeByCategoryIds方法
            Object result = categorySaleAttReadService.getSaleAttributeByCategoryIds(param);
            
            log.info("DevOpsController.testGetSaleAttributeByCategoryIds 调用结果，categoryIds: {}, result: {}", categoryIds, JSON.toJSONString(result));
            
            return JSON.toJSONString(result);
        } catch (Exception e) {
            log.error("DevOpsController.testGetSaleAttributeByCategoryIds 调用失败，categoryIds: {}", categoryIds, e);
            return "error: " + e.getMessage();
        }
    }

    /**
     * 测试接口：调用attributeOutService.queryExtAttrDetail
     * curl 'http://localhost:8080/product/devops/test/attribute/extAttr/1?lang=zh&token=dDFSADF111'
     * @param categoryId 类目ID
     * @param lang 语言
     * @param token 验证token
     * @return JSON格式的扩展属性数据
     */
    @GetMapping("test/attribute/extAttr/{categoryId}")
    public String testQueryExtAttrDetail(@PathVariable Long categoryId, @RequestParam(required = false, defaultValue = "zh") String lang, String token) {
        if (!token.equals(this.token)) {
            return "token error";
        }
        
        log.info("DevOpsController.testQueryExtAttrDetail 调用测试，categoryId: {}, lang: {}", categoryId, lang);
        
        try {
            List<AttributeFlatVO> result = attributeOutService.queryExtAttrDetail(categoryId, lang);
            
            log.info("DevOpsController.testQueryExtAttrDetail 调用结果，categoryId: {}, lang: {}, result: {}", categoryId, lang, JSON.toJSONString(result));
            
            return JSON.toJSONString(result);
        } catch (Exception e) {
            log.error("DevOpsController.testQueryExtAttrDetail 调用失败，categoryId: {}, lang: {}", categoryId, lang, e);
            return "error: " + e.getMessage();
        }
    }

    /**
     * 检查草稿表中group_ext_attribute字段是否有重复属性ID
     * curl 'http://localhost:8080/product/devops/check/draft/repeat/attribute?token=dDFSADF111'
     * @param token 验证token
     * @param pageSize 分页大小，默认100
     * @return 检查结果
     */
    @GetMapping("check/draft/repeat/attribute")
    public String checkDraftRepeatAttribute(String token, @RequestParam(defaultValue = "100") Integer pageSize) {
        if (!token.equals(this.token)) {
            return "token error";
        }
        
        log.info("DevOpsController.checkDraftRepeatAttribute 开始检查草稿表重复属性，pageSize: {}", pageSize);
        
        try {
            // 校验分页大小
            if (pageSize <= 0 || pageSize > 1000) {
                return "pageSize must be between 1 and 1000";
            }
            
            int totalRepeatCount = 0;
            
            // 检查 SPU 草稿表
            log.info("开始检查 jdi_isc_spu_draft_sharding 表");
            int spuRepeatCount = checkSpuDraftRepeatAttribute(pageSize);
            totalRepeatCount += spuRepeatCount;
            
            // 检查 SKU 草稿表
            log.info("开始检查 jdi_isc_sku_draft_sharding 表");
            int skuRepeatCount = checkSkuDraftRepeatAttribute(pageSize);
            totalRepeatCount += skuRepeatCount;
            
            String result = String.format("检查完成！SPU草稿表重复数据: %d 条，SKU草稿表重复数据: %d 条，总重复数据: %d 条", 
                    spuRepeatCount, skuRepeatCount, totalRepeatCount);
            log.info("DevOpsController.checkDraftRepeatAttribute 检查结果: {}", result);
            
            return result;
        } catch (Exception e) {
            log.error("DevOpsController.checkDraftRepeatAttribute 检查失败", e);
            return "error: " + e.getMessage();
        }
    }
    
    /**
     * 检查 SPU 草稿表中的重复属性
     */
    private int checkSpuDraftRepeatAttribute(Integer pageSize) {
        int repeatCount = 0;
        int offset = 0;
        int processedCount = 0;
        
        while (true) {
            // 使用严格按照SQL样例的分页查询
            List<SpuDraftPO> spuDraftList = spuDraftAtomicService.listSpuDraftByGroupExtAttributePage((long) offset, pageSize);
            
            if (CollectionUtils.isEmpty(spuDraftList)) {
                break;
            }
            
            // 检查每条数据
            for (SpuDraftPO spuDraft : spuDraftList) {
                if (spuDraft.getGroupExtAttribute() != null) {
                    boolean hasRepeat = isContainsRepeatAttributeIdInSameGroup(spuDraft.getGroupExtAttribute());
                    if (hasRepeat) {
                        repeatCount++;
                        log.warn("SPU草稿表发现重复属性 - ID: {}, SPU_ID: {}, group_ext_attribute: {}", 
                                spuDraft.getId(), spuDraft.getSpuId(), spuDraft.getGroupExtAttribute());
                    }
                }
                processedCount++;
            }
            
            offset += pageSize;
            
            // 每处理1000条记录打印一次进度
            if (processedCount % 1000 == 0) {
                log.info("SPU草稿表检查进度 - 已处理: {} 条，发现重复: {} 条", processedCount, repeatCount);
            }
        }
        
        log.info("SPU草稿表检查完成 - 总处理: {} 条，重复数据: {} 条", processedCount, repeatCount);
        return repeatCount;
    }
    
    /**
     * 检查 SKU 草稿表中的重复属性
     */
    private int checkSkuDraftRepeatAttribute(Integer pageSize) {
        int repeatCount = 0;
        int offset = 0;
        int processedCount = 0;
        
        while (true) {
            // 使用严格按照SQL样例的分页查询
            List<SkuDraftPO> skuDraftList = skuDraftAtomicService.listSkuDraftByGroupExtAttributePage((long) offset, pageSize);
            
            if (CollectionUtils.isEmpty(skuDraftList)) {
                break;
            }
            
            // 检查每条数据
            for (SkuDraftPO skuDraft : skuDraftList) {
                if (skuDraft.getGroupExtAttribute() != null) {
                    boolean hasRepeat = isContainsRepeatAttributeIdInSameGroup(skuDraft.getGroupExtAttribute());
                    if (hasRepeat) {
                        repeatCount++;
                        log.warn("SKU草稿表发现重复属性 - ID: {}, SPU_ID: {}, SKU_ID: {}, SKU_KEY: {}, JD_SKU_ID: {}, group_ext_attribute: {}", 
                                skuDraft.getId(), skuDraft.getSpuId(), skuDraft.getSkuId(), 
                                skuDraft.getSkuKey(), skuDraft.getJdSkuId(), skuDraft.getGroupExtAttribute());
                    }
                }
                processedCount++;
            }
            
            offset += pageSize;
            
            // 每处理1000条记录打印一次进度
            if (processedCount % 1000 == 0) {
                log.info("SKU草稿表检查进度 - 已处理: {} 条，发现重复: {} 条", processedCount, repeatCount);
            }
        }
        
        log.info("SKU草稿表检查完成 - 总处理: {} 条，重复数据: {} 条", processedCount, repeatCount);
        return repeatCount;
    }

    /**
     * 判断同一个扩展属性属性组内是否有重复的属性值id
     * @return
     */
    public static boolean isContainsRepeatAttributeIdInSameGroup(String groupExtAttribute){
        List<ExtendPropertyGroupVO> extendPropertyGroupVOS = ExtendPropertyGroupVO.obtainExtendPropertyGroupVOList(groupExtAttribute);
        if (CollectionUtils.isEmpty(extendPropertyGroupVOS)) {
            return false;
        }
        // 对extendPropertyGroupVOS中extendPropertyList中的ShotPropertyVO根据attributeId去重
        for (ExtendPropertyGroupVO extendPropertyGroupVO : extendPropertyGroupVOS) {
            if (extendPropertyGroupVO.getExtendPropertyList() != null) {
                Map<Long, ShotPropertyVO> map = new HashMap<>();
                for (ShotPropertyVO shotPropertyVO : extendPropertyGroupVO.getExtendPropertyList()) {
                    if (shotPropertyVO != null && shotPropertyVO.getAttributeId() != null) {
                        if (map.containsKey(shotPropertyVO.getAttributeId())) {
                            return true;
                        }
                        map.put(shotPropertyVO.getAttributeId(), shotPropertyVO);
                    }
                }
                extendPropertyGroupVO.setExtendPropertyList(new ArrayList<>(map.values()));
            }
        }

        return false;
    }

    /**
     * 刷新跨境品销售属性
     * curl 'http://localhost:8080/product/devops/sale-attr/cross-border/refresh?startPageNum=1&endPageNum=10&pageSize=100&writeToDB=true&token=dDFSADF111'
     */
    @GetMapping("sale-attr/cross-border/refresh")
    public String refreshSkuSaleAttributeForCrossBorder(@RequestParam(defaultValue = "1") int startPageNum,
                                                       @RequestParam(defaultValue = "999999") int endPageNum,
                                                       @RequestParam(defaultValue = "100") int pageSize,
                                                       @RequestParam(defaultValue = "false") boolean writeToDB,
                                                       String token) {
        if (token.equals(this.token)) {
            try {
                devOpsSaleApiService.refreshSkuSaleAttributeForCrossBorder(startPageNum, endPageNum, pageSize, writeToDB);
                return "success";
            } catch (Exception e) {
                return "error: " + e.getMessage();
            }
        } else {
            return "token error";
        }
    }

    /**
     * 刷新本土品草稿和审批通过的正式品销售属性
     *   curl 'http://localhost:8080/product/devops/sale-attr/local-draft-approvel/refresh?startPageNum=1&endPageNum=10&pageSize=100&writeToDB=true&token=dDFSADF111'
     */
    @GetMapping("sale-attr/local-draft-approvel/refresh")
    public String refreshSkuSaleAttributeForLocalDraftAndApprovel(@RequestParam(defaultValue = "1") int startPageNum,
                                                                  @RequestParam(defaultValue = "999999") int endPageNum,
                                                                  @RequestParam(defaultValue = "100") int pageSize,
                                                                  @RequestParam(defaultValue = "false") boolean writeToDB,
                                                                  String token) {
        if (token.equals(this.token)) {
            try {
                devOpsSaleApiService.refreshSkuSaleAttributeForLocalDraftAndApprovel(startPageNum, endPageNum, pageSize, writeToDB);
                return "success";
            } catch (Exception e) {
                return "error: " + e.getMessage();
            }
        } else {
            return "token error";
        }
    }

    /**
     * 刷新SKU的京东主SkuId和SpuId
     * 	curl 'http://localhost:8080/product/devops/sku/jd-main-sku-spu-id/refresh?startPageNum=1&endPageNum=10&pageSize=100&writeToDB=true&token=dDFSADF111'
     */
    @GetMapping("sku/jd-main-sku-spu-id/refresh")
    public String refreshSkuJdMainSkuIdAndSpuId(@RequestParam(defaultValue = "1") int startPageNum,
                                               @RequestParam(defaultValue = "999999") int endPageNum,
                                               @RequestParam(defaultValue = "100") int pageSize,
                                               @RequestParam(defaultValue = "false") boolean writeToDB,
                                               String token) {
        if (token.equals(this.token)) {
            try {
                devOpsSaleApiService.refreshSkuJdMainSkuIdAndSpuId(startPageNum, endPageNum, pageSize, writeToDB);
                return "success";
            } catch (Exception e) {
                return "error: " + e.getMessage();
            }
        } else {
            return "token error";
        }
    }

    /**
     * 回填跨境品SKU销售属性关系的skuKey
     * curl 'http://localhost:8080/product/devops/sale-attr/cross-border/sku-key/refresh?startPageNum=1&pageSize=100&writeToDB=true&token=dDFSADF111'
     */
    @GetMapping("sale-attr/cross-border/sku-key/refresh")
    public String refreshSkuSaleAttributeSkuKeySkuIdForCrossBoarderAndLocal(@RequestParam(defaultValue = "1") int startPageNum,
                                                              @RequestParam(defaultValue = "100") int pageSize,
                                                              @RequestParam(defaultValue = "false") boolean writeToDB,
                                                              String token) {
        if (token.equals(this.token)) {
            try {
                devOpsSaleApiService.refreshSkuSaleAttributeSkuKeySkuIdForCrossBoarderAndLocal(startPageNum, pageSize, writeToDB);
                return "success";
            } catch (Exception e) {
                return "error: " + e.getMessage();
            }
        } else {
            return "token error";
        }
    }

    /**
     * 校验销售属性数据正确性
     * curl 'http://localhost:8080/product/devops/sale-attr/validate?startPageNum=1&pageSize=100&token=dDFSADF111'
     */
    @GetMapping("sale-attr/validate")
    public String validateSkuSaleAttribute(@RequestParam(defaultValue = "1") int startPageNum,
                                          @RequestParam(defaultValue = "100") int pageSize,
                                          String token) {
        if (token.equals(this.token)) {
            try {
                devOpsSaleApiService.validateSkuSaleAttribute(startPageNum, pageSize);
                return "success";
            } catch (Exception e) {
                return "error: " + e.getMessage();
            }
        } else {
            return "token error";
        }
    }

    /**
     * 刷新单个跨境品SPU销售属性
     * curl 'http://localhost:8080/product/devops/sale-attr/cross-border/single/refresh?spuId=123456&token=dDFSADF111'
     */
    @GetMapping("sale-attr/cross-border/single/refresh")
    public String refreshSingleCrossBoarderSpuSaleAttribute(@RequestParam Long spuId,
                                                           String token) {
        if (token.equals(this.token)) {
            try {
                devOpsSaleApiService.refreshSingleCrossBoarderSpuSaleAttribute(spuId);
                return "success";
            } catch (Exception e) {
                return "error: " + e.getMessage();
            }
        } else {
            return "token error";
        }
    }

    /**
     * 删除所有跨境品SPU销售属性
     * curl 'http://localhost:8080/product/devops/sale-attr/cross-border/delete-all?skuId=123456&writeDB=true&token=dDFSADF111'
     */
    @GetMapping("sale-attr/cross-border/delete-all")
    public String deleteAllCrossBoarderSpuSaleAttribute(@RequestParam(required = false) Long skuId,
                                                       @RequestParam(defaultValue = "false") boolean writeDB,
                                                       String token) {
        if (token.equals(this.token)) {
            try {
                devOpsSaleApiService.deleteAllCrossBoarderSpuSaleAttribute(skuId, writeDB);
                return "success";
            } catch (Exception e) {
                return "error: " + e.getMessage();
            }
        } else {
            return "token error";
        }
    }


    /**
     * 获取跨境品SPU销售属性
     * curl 'http://localhost:8080/product/devops/sale-attr/cross-border/get-spu-sale-attribute-detail?spuId=123456&lang=en&token=dDFSADF111'
     */
    @GetMapping("sale-attr/cross-border/get-spu-sale-attribute-detail")
    public String getSpuSaleAttributeDetail(@RequestParam Long spuId, @RequestParam String lang,
                                           String token) {
        if (token.equals(this.token)) {
            // 同时验证spu销售属性是否合规
            boolean isValid = devOpsSaleApiService.validateSingleSpuSaleAttribute(spuId);
            return isValid + JSON.toJSONString(saleAttributeManageService.getSpuSaleAttributeDetail(spuId, lang));
        }
        return "token error";
    }

    /**
     * 获取销售属性验证未通过的spuId集合
     * curl 'http://localhost:8080/product/devops/sale-attr/not-ok-spu-id-set?token=dDFSADF111'
     */
    @GetMapping("sale-attr/not-ok-spu-id-set")
    public String obtainNotOKSaleAttributeSpuIdSet(String token){
        if (token.equals(this.token)) {
            Set<String> strings = devOpsSaleApiService.obtainNotOKSaleAttributeSpuIdSet();
            log.info("obtainNotOKSaleAttributeSpuIdSet 获取到未通过销售属性验证的spuId集合: {}", JSONObject.toJSONString(strings));
            return JSON.toJSONString(strings);
        }
        return "token error";
    }

    /**
     * 统计跨境品类目匹配数量
     * curl 'http://localhost:8080/product/devops/attr/jd-sku/calculate-cat-match?token=dDFSADF111'
     */
    @GetMapping("attr/jd-sku/calculate-cat-match")
    public DataResponse<Long> calculateJdSkuCatMatchAmount(String token) {
        if (!token.equals(this.token)) {
            return DataResponse.error("token error");
        }
        
        try {
            return devOpsJDSkuExtendAttribueService.calculateJdSkuCatMatchAmount();
        } catch (Exception e) {
            log.error("统计跨境品类目匹配数量异常", e);
            return DataResponse.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取全部跨境品AttributeSettingResult信息
     * curl 'http://localhost:8080/product/devops/attr/jd-sku/read-all-attribute-setting?token=dDFSADF111'
     */
    @GetMapping("attr/jd-sku/read-all-attribute-setting")
    public DataResponse<String> readAllJdSkuAttributeSettingResult(String token) {
        if (!token.equals(this.token)) {
            return DataResponse.error("token error");
        }
        
        try {
            return devOpsJDSkuExtendAttribueService.readAllJdSkuAttributeSettingResult();
        } catch (Exception e) {
            log.error("获取全部跨境品AttributeSettingResult信息异常", e);
            return DataResponse.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 为jdi_isc_sale_attribute_value_lang_sharding表中没有中文（zh）的sale_attribute_value_id数据添加中文数据
     * curl 'http://localhost:8080/product/devops/sale-attr/add-default-zh-lang?fixAmount=1000&writeDB=false&token=dDFSADF111'
     * 
     * @param fixAmount 没有中文翻译sale_attribute_value_id的条数，为空则处理全部
     * @param writeDB 是否写入数据库
     * @param token 验证token
     * @return 处理结果
     */
    @GetMapping("sale-attr/add-default-zh-lang")
    public String addDefaultZHLangForSaleAttribute(@RequestParam(required = false) Long fixAmount,
                                                  @RequestParam(defaultValue = "false") Boolean writeDB,
                                                  String token) {
        if (!token.equals(this.token)) {
            return "token error";
        }
        
        try {
            log.info("DevOpsController.addDefaultZHLangForSaleAttribute 开始执行，fixAmount: {}, writeDB: {}", fixAmount, writeDB);
            
            devOpsSaleApiService.addDefautZHLangForSaleAttribute(fixAmount, writeDB);
            
            log.info("DevOpsController.addDefaultZHLangForSaleAttribute 执行完成，fixAmount: {}, writeDB: {}", fixAmount, writeDB);
            return "success";
        } catch (Exception e) {
            log.error("DevOpsController.addDefaultZHLangForSaleAttribute 执行失败，fixAmount: {}, writeDB: {}", fixAmount, writeDB, e);
            return "error: " + e.getMessage();
        }
    }

    /**
     * 打印所有销售属性验证未通过spuId的创建人
     * curl 'http://localhost:8080/product/devops/sale-attr/print-spu-creator?token=dDFSADF111'
     */
    @GetMapping("sale-attr/print-spu-creator")
    public String printSpuCreator(String token){
        if (!token.equals(this.token)) {
            return "token error";
        }
        devOpsSaleApiService.printSpuCreator();
        return "success";
    }

    /**
     * 获取京东SKU扩展属性信息，包括类目扩展属性组信息、sku属性信息
     * curl 'http://localhost:8080/product/devops/attr/jd-sku/ext-attribute-info?jdSkuId=100004205573&token=dDFSADF111'
     */
    @GetMapping("attr/jd-sku/ext-attribute-info")
    public String obtainJdSkuExtAttributeInfo(String token, Long jdSkuId){
        if (!token.equals(this.token)) {
            return "token error";
        }
        return devOpsJDSkuExtendAttribueService.obtainJdSkuExtAttributeInfo(jdSkuId).getData();
    }

    /**
     * 测试跨境品创建时扩展属性能否正常拉取
     * curl 'http://localhost:8080/product/devops/test/crossboarder-sku-ext-attribute-create/100004205573?token=dDFSADF111'
     */
    @GetMapping("test/crossboarder-sku-ext-attribute-create/{jdSkuId}")
    public DataResponse<String> testCrossboarderSkuExtAttributeCreate(@PathVariable Long jdSkuId, String token) {
        if (!token.equals(this.token)) {
            return DataResponse.error("token error");
        }
        
        try {
            log.info("DevOpsController.testCrossboarderSkuExtAttributeCreate 调用测试，jdSkuId: {}", jdSkuId);
            return devOpsJDSkuExtendAttribueService.testCrossboarderSkuExtAttributeCreate(jdSkuId);
        } catch (Exception e) {
            log.error("DevOpsController.testCrossboarderSkuExtAttributeCreate 调用失败，jdSkuId: {}", jdSkuId, e);
            return DataResponse.error("测试失败: " + e.getMessage());
        }
    }



    /**
     * 处理跨境品销售属性顺序，确保属性名与属性值的对应关系正确
     * curl 'http://localhost:8080/product/devops/sale-attribute/handle-order-crossboarder?startPageNum=1&endPageNum=2&pageSize=100&writeDB=false&useCache=true&token=dDFSADF111'
     */
    @GetMapping("sale-attribute/handle-order-crossboarder")
    public DataResponse<String> handleSaleAtrributeOrderForCrossBoarder(String token,
                                                                       @RequestParam(defaultValue = "1") int startPageNum,
                                                                       @RequestParam(defaultValue = "1") int endPageNum,
                                                                       @RequestParam(defaultValue = "100") int pageSize,
                                                                       @RequestParam(defaultValue = "false") boolean writeDB,
                                                                       @RequestParam(defaultValue = "true") boolean useCache) {
        if (!token.equals(this.token)) {
            return DataResponse.error("token error");
        }

        try {
            log.info("DevOpsController.handleSaleAtrributeOrderForCrossBoarder 处理跨境品销售属性顺序, startPageNum: {}, endPageNum: {}, pageSize: {}, writeDB: {}, useCache: {}",
                    startPageNum, endPageNum, pageSize, writeDB, useCache);
            return devOpsSaleApiService.handleSaleAtrributeOrderForCrossBoarder(startPageNum, endPageNum, pageSize, writeDB, useCache);
        } catch (Exception e) {
            log.error("DevOpsController.handleSaleAtrributeOrderForCrossBoarder 处理失败, startPageNum: {}, endPageNum: {}, pageSize: {}",
                    startPageNum, endPageNum, pageSize, e);
            return DataResponse.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 校验跨境品销售属性顺序处理结果
     * curl 'http://localhost:8080/product/devops/sale-attribute/validate-order-crossboarder?startPageNum=1&endPageNum=2&pageSize=100&token=dDFSADF111'
     */
    @GetMapping("sale-attribute/validate-order-crossboarder")
    public DataResponse<String> validataHandleSaleAtrributeOrderForCrossBoarder(String token,
                                                                               @RequestParam(defaultValue = "1") int startPageNum,
                                                                               @RequestParam(defaultValue = "1") int endPageNum,
                                                                               @RequestParam(defaultValue = "100") int pageSize) {
        if (!token.equals(this.token)) {
            return DataResponse.error("token error");
        }

        try {
            log.info("DevOpsController.validataHandleSaleAtrributeOrderForCrossBoarder 校验跨境品销售属性顺序, startPageNum: {}, endPageNum: {}, pageSize: {}",
                    startPageNum, endPageNum, pageSize);
            return devOpsSaleApiService.validataHandleSaleAtrributeOrderForCrossBoarder(startPageNum, endPageNum, pageSize);
        } catch (Exception e) {
            log.error("DevOpsController.validataHandleSaleAtrributeOrderForCrossBoarder 校验失败, startPageNum: {}, endPageNum: {}, pageSize: {}",
                    startPageNum, endPageNum, pageSize, e);
            return DataResponse.error("校验失败: " + e.getMessage());
        }
    }

    /**
     * 处理单个跨境品销售属性顺序
     * curl 'http://localhost:8080/product/devops/sale-attribute/process-order-one?skuId=100004205573&writeDB=false&useCache=true&token=dDFSADF111'
     */
    @GetMapping("sale-attribute/process-order-one")
    public DataResponse<Boolean> processSaleAttributeOrderOne(String token, Long skuId, @RequestParam(defaultValue = "false") boolean writeDB, @RequestParam(defaultValue = "true") boolean useCache) {
        if (!token.equals(this.token)) {
            return DataResponse.error("token error");
        }
        try {
            log.info("DevOpsController.processSaleAttributeOrderOne 处理单个跨境品销售属性顺序, skuId: {}, writeDB: {}, useCache: {}",
                    skuId, writeDB, useCache);
            // 处理单个跨境品销售属性顺序
            boolean result = devOpsSaleApiService.processSaleAttributeOrderOne(skuId, writeDB, useCache);
            return DataResponse.success(result);
        } catch (Exception e) {
            log.error("DevOpsController.processSaleAttributeOrderOne 处理失败, skuId: {}, writeDB: {}, useCache: {}",
                    skuId, writeDB, useCache, e);
            return DataResponse.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 刷新单个SPU的本土品草稿和审批通过的销售属性
     * curl 'http://localhost:8080/product/devops/sale-attr/local-draft-approvel/single/refresh?spuId=123456&token=dDFSADF111'
     */
    @GetMapping("sale-attr/local-draft-approvel/single/refresh")
    public String refreshSingleSpuSaleAttributeForLocalDraftAndApprovel(@RequestParam String spuIds,
                                                                        String token) {
        if (token.equals(this.token)) {
            try {
                log.info("DevOpsController.refreshSingleSpuSaleAttributeForLocalDraftAndApprovel 开始刷新单个SPU销售属性, spuIds: {}", spuIds);
                // spuids用,分割
                String[] spuIdArray = spuIds.split(",");
                for (String spuIdStr : spuIdArray) {
                    Long spuId = Long.parseLong(spuIdStr.trim());
                    devOpsSaleApiService.refreshSingleSpuSaleAttributeForLocalDraftAndApprovel(spuId);
                }
                log.info("DevOpsController.refreshSingleSpuSaleAttributeForLocalDraftAndApprovel 刷新完成, spuIds: {}", spuIds);
                return "success";
            } catch (Exception e) {
                log.error("DevOpsController.refreshSingleSpuSaleAttributeForLocalDraftAndApprovel 刷新失败, spuIds: {}", spuIds, e);
                return "error: " + e.getMessage();
            }
        } else {
            return "token error";
        }
    }

    /**
     * SPU类目迁移接口
     * curl 'http://localhost:8080/product/devops/spu/transfer-category?spuId=123456&targetCatId=789&token=dDFSADF111'
     * @param spuId SPU ID
     * @param targetCatId 目标类目ID
     * @param token 验证token
     * @return 迁移结果
     */
    @GetMapping("spu/transfer-category")
    public String transferCategory(@RequestParam Long spuId,
                                  @RequestParam Long targetCatId,
                                  String token) {
        if (!token.equals(this.token)) {
            return "token error";
        }
        
        log.info("DevOpsController.transferCategory 开始执行SPU类目迁移，spuId: {}, targetCatId: {}", spuId, targetCatId);
        
        try {

            // 构建请求参数
            TransferCategoryReqVO reqVO = new TransferCategoryReqVO();
            reqVO.setSpuId(spuId);
            reqVO.setTargetCatId(targetCatId);
            reqVO.setUpdater(Constant.SYSTEM);
            ApiInitUtils.init(Constant.SYSTEM,LangConstant.LANG_ZH,null);
            // 调用迁移服务
            DataResponse<Boolean> result = spuTransferCategoryManageService.transferCategory(reqVO);
            
            log.info("DevOpsController.transferCategory 执行结果，spuId: {}, targetCatId: {}, result: {}", 
                    spuId, targetCatId, JSON.toJSONString(result));
            
            if (result.getSuccess()) {
                return "success";
            } else {
                return "error: " + result.getMessage();
            }
        } catch (Exception e) {
            log.error("DevOpsController.transferCategory 执行失败，spuId: {}, targetCatId: {}", spuId, targetCatId, e);
            return "error: " + e.getMessage();
        }
    }
}
