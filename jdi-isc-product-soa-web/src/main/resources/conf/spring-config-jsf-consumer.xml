<?xml version="1.0" encoding="utf-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-autowire="byName">

    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="${jd.jsf.registry.index}"/>

    <!-- iop标品服务 -->
    <jsf:consumer id="standardQueryGoodsOpenProvider" interface="com.jd.ka.gpt.soa.client.StandardQueryGoodsOpenProvider"
                  alias="${jd.jsf.consumer.iopProduct.alias}" protocol="jsf" timeout="10000" serialization="hessian"/>

    <!-- 商品中台：商品基本信息、特殊属性及区域限制查询接口 -->
    <jsf:consumer id="productRpc" interface="com.jd.gms.crs.rpc.ProductRpc"
                  protocol="jsf" alias="${jd.jsf.consumer.gmsProduct.alias}" timeout="${jd.jsf.common.timeout}" serialization="hessian">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.gmsProduct.token}" hide="true"/>
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="(J-dos)imp-operate-admin" hide="true"/>
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.gmsProduct.alias}" hide="true"/>
    </jsf:consumer>

    <!-- 藏经阁价格查询接口  接口文档：https://joyspace.jd.com/page/xnW5m3T0FFQ7GYthzXEC-->
    <jsf:consumer id="ppsPriceQueryService" interface="com.jd.pps.pub.api.PpsPriceQueryService"
                  protocol="jsf" alias="${jd.jsf.consumer.ppsPrice.alias}" timeout="${jd.jsf.common.timeout}">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.ppsPrice.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdi-isc-product-center" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.ppsPrice.alias}" hide="true" />
    </jsf:consumer>

    <!--供应链渠道仓报价 https://cf.jd.com/pages/viewpage.action?pageId=910997336-->
    <jsf:consumer id="jsfCbjStatsService"
                  interface="com.jd.fms.plough.JsfService.JsfCbjStatsService"
                  protocol="jsf" alias="${jd.jsf.consumer.fms.alias}" timeout="10000"  serialization="hessian">
        <jsf:parameter key="token" value="${jd.jsf.consumer.fms.token}" hide="true"/>
    </jsf:consumer>

    <!--金鹏商详页话术接口 https://cf.jd.com/pages/viewpage.action?pageId=766926862 -->
    <jsf:consumer id="promiseSdkService"
                  interface="com.jd.jp.strategy.sdk.service.PromiseSdkService"
                  protocol="jsf" alias="${jd.jsf.consumer.strategy.alias}" timeout="10000"  serialization="hessian">
    </jsf:consumer>

    <!--综合可售 https://cf.jd.com/pages/viewpage.action?pageId=1018965961 -->
    <jsf:consumer id="availableSaleService" interface="com.available.sale.api.service.AvailableSaleService"
                  protocol="jsf" alias="${jd.jsf.consumer.available.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="3859cd71-dbb0-4a69-821d-85e7cb31a6cb" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-gptsoa" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.available.alias}" hide="true" />
    </jsf:consumer>

    <!-- 消息推送 call anycall send msg-->
    <jsf:consumer id="pinMsgPushService" interface="com.jd.anycall.client.service.PinMsgPushService"
                  protocol="jsf"
                  alias="${jd.jsf.consumer.anycall.alias}"
                  timeout="5000" retries="0">
    </jsf:consumer>

    <!-- 查询国内供应商PIN接口-->
    <jsf:consumer id="userQueryExportFacade" interface="com.jd.vc.auth.facade.UserQueryExportFacade"
                  protocol="jsf"
                  alias="${jd.jsf.consumer.vc.auth.alias}"
                  timeout="5000" retries="0">
    </jsf:consumer>

    <!--国内供应商信息查询接口-->
    <jsf:consumer id="vendorBaseInfoService" interface="com.jd.vms.i18n.external.api.VendorBaseInfoService"
                  protocol="jsf" alias="${jd.jsf.consumer.vmsExternal.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.vmsExternal.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdi-isc-product-center" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.vmsExternal.alias}" hide="true" />
    </jsf:consumer>

    <!-- mkuId查询skuId关系服务 https://joyspace.jd.com/pages/aSDDKVWQJGbZWvWSEBMg-->
    <jsf:consumer id="relationQueryRpc" interface="com.jd.aggregation.api.service.RelationQueryRpc"
                  protocol="jsf" alias="${jd.jsf.consumer.mapping.alias}" timeout="${jd.jsf.common.timeout}" >
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.mapping.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdi-isc-product-center" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.mapping.alias}" hide="true" />
    </jsf:consumer>

    <!-- 装吧：商品装吧装饰数据获取服务 -->
    <jsf:consumer id="zbWareDetailService" interface="com.jd.sdd.mkt.sdk.component.ZbWareDetailService"
                  protocol="jsf" alias="${jd.jsf.consumer.market.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="831b118d-957b-4fa1-bee5-ecd5cccc683e" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-imp-operate-admin" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.market.alias}" hide="true" />
    </jsf:consumer>

    <!-- 藏经阁商品大字段查询接口  -->
    <jsf:consumer id="assemblyRpc" interface="com.jd.gms.crs.rpc.AssemblyRpc"
                  protocol="jsf" alias="${jd.jsf.consumer.gmsAssembly.alias}" timeout="${jd.jsf.common.timeout}">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.gmsAssembly.token}" hide="true"/>
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdi-isc-product-center" hide="true"/>
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.gmsAssembly.alias}" hide="true"/>
    </jsf:consumer>

    <!--发邮件-->
    <jsf:consumer id="customMailService" interface="com.jd.leo.mail.product.produce.rpc.service.CustomMailService"
                  alias="${jd.jsf.consumer.customMailService.alias}" timeout="5000">
        <jsf:parameter key="token" value="${jd.jsf.consumer.customMailService.token}" hide="true"/>
    </jsf:consumer>

    <!--EBS合同服务-->
    <jsf:consumer id="contractService" interface="com.jd.ebs.esi.core.contract.ContractService"
                  protocol="jsf" alias="${jd.jsf.consumer.esi.contractService.alias}" timeout="${jd.jsf.consumer.esi.contractService.timeout}" retries="2">
    </jsf:consumer>

    <!--生成m码-->
    <jsf:consumer id="eaMdmDataJsfService" interface="com.jd.ea.mdm.jsf.service.EaMdmDataJsfService"
                  protocol="jsf" alias="${jd.jsf.consumer.eamdmdata.alias}" timeout="${jd.jsf.consumer.eamdmdata.timeout}" retries="3">
    </jsf:consumer>

    <!--  xbp申请单相关  -->
    <jsf:consumer
            id="ticketService" interface="com.jd.xbp.jsf.api.TicketService"
            protocol="jsf" alias="${jd.jsf.consumer.xbp.common.alias}" timeout="${jd.jsf.consumer.xbp.common.timeout}"
            retries="0" serialization="hessian">
    </jsf:consumer>

    <!-- 商品中台:类目服务 -->
    <jsf:consumer id="categoryReadService" interface="com.jd.gms.greatdane.category.service.read.CategoryReadService"
                  protocol="jsf" alias="${jd.jsf.consumer.gmsCate.alias}" timeout="${jd.jsf.common.timeout}">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.gmsCate.token}" hide="true"/>
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${jd.jsf.app.productSoa}" hide="true"/>
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.gmsCate.alias}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="categoryGroupAttReadService" interface="com.jd.gms.greatdane.category.service.read.CategoryGroupAttReadService" alias="${jd.jsf.consumer.property.alias}">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.property.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${jd.jsf.consumer.property.app}" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.property.alias}" hide="true" />
    </jsf:consumer>

    <!--中台属性值读服务-->
    <jsf:consumer id="categoryGroupAttValueReadService" interface="com.jd.gms.greatdane.category.service.read.CategoryGroupAttValueReadService"
                  protocol="jsf" alias="${jd.jsf.consumer.propertyvalue.alias}">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.propertyvalue.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${jd.jsf.consumer.propertyvalue.app}" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.propertyvalue.alias}" hide="true" />
    </jsf:consumer>

    <!-- 中台属性组读服务, 根据属性组id查询属性组名称 -->
    <jsf:consumer id="newAttributeGroupReadService"
                  interface="com.jd.gms.greatdane.category.service.read.NewAttributeGroupReadService"
                  protocol="jsf" alias="${jd.jsf.consumer.attributeGroup.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.attributeGroup.token}" hide="true"/>
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${jd.jsf.app.productSoa}" hide="true"/>
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.attributeGroup.alias}" hide="true"/>
    </jsf:consumer>

    <!--中台图片、属性等资源读服务-->
    <jsf:consumer id="materialRpc" interface="com.jd.gms.crs.rpc.MaterialRpc" protocol="jsf" alias="${jd.jsf.consumer.material.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.material.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${jd.jsf.consumer.material.app}" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.material.alias}" hide="true" />
    </jsf:consumer>

    <!-- ept商品查询服务  -->
    <jsf:consumer id="wareRpcService" interface="com.jd.ept.warecenter.api.ware.WareRpcService"
                  alias="${jd.jsf.consumer.eptWareCenter.alias}" protocol="jsf" timeout="${jd.jsf.common.timeout}" serialization="hessian"/>

    <!-- 工鼎商品查询服务 -->
    <jsf:consumer id="iJdiK2GdProductService" interface="com.jd.k2.gd.boost.sdk.soa.IJdiK2GdProductService"
                  protocol="jsf" alias="${jd.jsf.consumer.gdProduct.alias}" timeout="${jd.jsf.common.timeout}" />

    <!-- 工鼎中台商品查询服务 -->
    <jsf:consumer id="iJdiGdGmsProductService" interface="com.jd.k2.gd.boost.sdk.soa.IJdiGdGmsProductService"
                  protocol="jsf" alias="${jd.jsf.consumer.gdGmsProduct.alias}" timeout="${jd.jsf.common.timeout}"
                  warmupWeightStrategy="productSoaWarmup" />

    <!-- 价格B中台 -->
    <jsf:consumer id="b2bPriceCoreService" interface="com.jd.b2b.price.core.sdk.service.B2bPriceCoreService"
                  protocol="jsf" alias="${jd.jsf.consumer.b2bPriceCore.alias}" timeout="${jd.jsf.consumer.b2bPriceCore.timeout}"
                  warmupWeightStrategy="productSoaWarmup">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.b2bPriceCore.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${jd.jsf.consumer.b2bPriceCore.app}" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.b2bPriceCore.alias}" hide="true" />
    </jsf:consumer>

    <!-- 零售属性读服务 -->
    <jsf:consumer id="newAttributeReadService" interface="com.jd.gms.greatdane.category.service.read.NewAttributeReadService"
                  protocol="jsf" alias="${jd.jsf.consumer.newAttribute.alias}" >
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.newAttribute.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdi-isc-product-center" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.newAttribute.alias}" hide="true" />
    </jsf:consumer>

    <!-- 零售属性值读服务 -->
    <jsf:consumer id="newAttributeValueReadService" interface="com.jd.gms.greatdane.category.service.read.NewAttributeValueReadService"
                  protocol="jsf" alias="${jd.jsf.consumer.newAttributeValue.alias}" >
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.newAttributeValue.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdi-isc-product-center" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.newAttributeValue.alias}" hide="true" />
    </jsf:consumer>

    <!-- joysky审批流业务组件 -->
    <jsf:consumer id="joySkyAppFlowApiService"
                  interface="com.jdi.isc.biz.component.api.joysky.JoySkyAppFlowApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.writeTimeout}"
                  serialization="hessian"
                  warmupWeightStrategy="productSoaWarmup">
        <!--     超过线程数会进行等待，等待到接口超时   -->
        <jsf:method name="createSkyApprovalFlow" concurrents="1"/>
        <jsf:method name="createSkyApprovalFlowRetry" concurrents="1"/>
    </jsf:consumer>
    <!-- 查询本土商品采购价 -->
    <jsf:consumer id="skuReadApiService" interface="com.jdi.isc.aggregate.read.api.sku.SkuReadApiService" alias="${jd.jsf.consumer.aggregate.alias}"
                  timeout="${jd.jsf.consumer.aggregate.timeout}" check="false">
    </jsf:consumer>

    <!-- 查询本土商品采购价 -->
    <jsf:consumer id="customerReadService" interface="com.jdi.isc.aggregate.read.api.customer.CustomerReadService" alias="${jd.jsf.consumer.aggregate.alias}"
                  timeout="${jd.jsf.consumer.aggregate.timeout}" check="false">
    </jsf:consumer>

    <!-- 工鼎中台类目查询服务 -->
    <jsf:consumer id="iJdiExtendDataService" interface="com.jd.k2.gd.boost.sdk.soa.IJdiExtendDataService"
                  protocol="jsf" alias="${jd.jsf.consumer.extendData.alias}" timeout="${jd.jsf.common.timeout}"
                  serialization="hessian"
                  warmupWeightStrategy="productSoaWarmup"/>

    <!-- 工鼎中台新增服务 -->
    <jsf:consumer id="jdiSynCustomerPoolLingxiService"
                  interface="com.jd.k2.customer.pool.core.man.JdiSynCustomerPoolLingxiService"
                  alias="${jd.jsf.consumer.gd.pool.alias}"
                  timeout="${jd.jsf.consumer.gd.pool.timeout}"
                  check="false"/>
    <!-- 集运中心仓策略服务-->
    <jsf:consumer id="fulfillmentPolicyApiService"
                  interface="com.jdi.isc.order.center.api.ofc.FulfillmentPolicyApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"/>

    <!-- 订单配置查询服务-->
    <jsf:consumer id="orderConfigApiService"
                  interface="com.jdi.isc.order.center.api.config.OrderConfigApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"/>
    <!--中台区域库存接口 https://cf.jd.com/pages/viewpage.action?pageId=168188544 -->
    <jsf:consumer id="areaStockStateExport" interface="com.jd.stock.state.export.AreaStockStateExport"
                  protocol="jsf" alias="${jd.jsf.consumer.gmsAreaStock.alias}" timeout="${jd.jsf.common.timeout}" serialization="hessian">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.gmsAreaStock.token}" hide="true"/>
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdi-isc-aggregate-read" hide="true"/>
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.gmsAreaStock.alias}" hide="true"/>
    </jsf:consumer>

    <!-- 京ME发送消息接口 -->
    <jsf:consumer id="jingMeMessageApiService"
                  interface="com.jdi.isc.biz.component.api.jme.JingMeMessageApiService"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"/>

    <!-- 商品中台:类目搜索服务 -->
    <jsf:consumer id="categorySearchService" interface="com.jd.gms.greatdane.category.service.read.CategorySearchService"
                  protocol="jsf" alias="${jd.jsf.consumer.gmsCateSearch.alias}" timeout="${jd.jsf.common.timeout}">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.gmsCateSearch.token}" hide="true"/>
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${jd.jsf.app.productSoa}" hide="true"/>
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.gmsCateSearch.alias}" hide="true"/>
    </jsf:consumer>


    <!-- 商品中台:类目Tree查询服务 -->
    <jsf:consumer id="categoryTreeReadService" interface="com.jd.gms.greatdane.category.service.read.CategoryTreeReadService"
                  protocol="jsf" alias="${jd.jsf.consumer.gmsCateTree.alias}" timeout="${jd.jsf.common.timeout}">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.gmsCateTree.token}" hide="true"/>
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${jd.jsf.app.productSoa}" hide="true"/>
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.gmsCateTree.alias}" hide="true"/>
    </jsf:consumer>


    <!-- VC获取供应商仓库列表 -->
    <jsf:consumer id="supplierWarehouseReadApiService"
                  interface="com.jdi.isc.vc.soa.api.supplier.SupplierWarehouseReadApiService"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"/>


    <!--其它consumer可复用，预热使用-->
    <jsf:warmupWeightStrategy id="productSoaWarmup" enabled="true" warmupDuration="15000" warmupWeight="40"/>

    <jsf:consumer id="bWareReadService"
                  interface="com.jd.b2b.ware.biz.v2.sdk.read.BWareReadService" protocol="jsf"
                  alias="${jd.jsf.consumer.b2b.ware.alias}" timeout="${jd.jsf.consumer.b2b.ware.timeout:3000}" retries="1"/>

    <!-- auth权限系统菜单服务 -->
    <jsf:consumer id="menuFacade"
                  interface="com.jd.auth.facade.jsf.MenuFacade"
                  protocol="jsf" alias="${jd.jsf.consumer.auth.alias}"
                  timeout="${jd.jsf.consumer.auth.timeout}" >
    </jsf:consumer>

    <!-- auth权限系统资源服务 -->
    <jsf:consumer id="resourceFacade"
                  interface="com.jd.auth.facade.jsf.ResourceFacade"
                  protocol="jsf" alias="${jd.jsf.consumer.auth.alias}"
                  timeout="${jd.jsf.consumer.auth.timeout}" >
    </jsf:consumer>

    <!-- auth权限系统过滤资源服务 -->
    <jsf:consumer id="filterResourceFacade"
                  interface="com.jd.auth.facade.jsf.FilterResourceFacade"
                  protocol="jsf" alias="${jd.jsf.consumer.auth.alias}"
                  timeout="${jd.jsf.consumer.auth.timeout}" >
    </jsf:consumer>

    <!-- auth权限系统角色服务 -->
    <jsf:consumer id="roleFacade"
                  interface="com.jd.auth.facade.jsf.RoleFacade"
                  protocol="jsf" alias="${jd.jsf.consumer.auth.alias}"
                  timeout="${jd.jsf.consumer.auth.timeout}" >
    </jsf:consumer>

    <!-- auth权限系统纬度相关服务 -->
    <jsf:consumer id="dimPermissionFacade"
                  interface="com.jd.auth.facade.jsf.DimPermissionFacade"
                  protocol="jsf" alias="${jd.jsf.consumer.auth.alias}"
                  timeout="${jd.jsf.consumer.auth.timeout}" >
    </jsf:consumer>

    <!-- auth权限系统用户相关服务 -->
    <jsf:consumer id="userFacade"
                  interface="com.jd.auth.facade.jsf.UserFacade"
                  protocol="jsf" alias="${jd.jsf.consumer.auth.alias}"
                  timeout="${jd.jsf.consumer.auth.timeout}" >
    </jsf:consumer>

    <!-- 通过项目id查询合同信息-->
    <jsf:consumer id="contractManageService" interface="com.jd.b2b.user.soa.sdk.service.ContractManageService"
                  protocol="jsf" alias="${jd.jsf.consumer.user.soa.alias}" timeout="${jd.jsf.consumer.user.soa.timeout}"
                  retries="0" check="false">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.user.soa.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdi-isc-product-center" hide="true" />
    </jsf:consumer>

    <!-- erp查询项目信息 -->
    <jsf:consumer id="bussinessOpportunityQueryService"
                  interface="com.jd.ka.buser.sdk.service.BussinessOpportunityQueryService"
                  protocol="jsf"
                  alias="${jd.jsf.consumer.buser.sdk.alias}"
                  timeout="${jd.jsf.consumer.buser.sdk.timeout}" >
    </jsf:consumer>

    <!-- 中台销售属性组读服务, 根据类目ID查询类目销售属性 -->
    <jsf:consumer id="categorySaleAttReadServiceh"
                  interface="com.jd.gms.greatdane.category.service.read.CategorySaleAttReadService"
                  protocol="jsf" alias="${jd.jsf.consumer.categorySaleAtt.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.categorySaleAtt.token}" hide="true"/>
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${jd.jsf.app.productSoa}" hide="true"/>
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.categorySaleAtt.alias}" hide="true"/>
    </jsf:consumer>


    <!-- 履约中心查询时效信息 -->
    <jsf:consumer id="promiseTimeReadApiService"
                  interface="com.jdi.isc.fulfillment.soa.api.promiseTime.PromiseTimeReadApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"/>

    <!-- 查询供应商信息 -->
    <jsf:consumer id="supplierBaseInfoReadApiService"
                  interface="com.jdi.isc.vc.soa.api.supplier.SupplierBaseInfoReadApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"/>

    <!-- 订单聚合查询服务-->
    <jsf:consumer id="orderCompositeReadApiService"
                  interface="com.jdi.isc.order.center.api.config.OrderCompositeReadApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"/>

    <jsf:consumer id="jsfCategoryService"
                  interface="jd.gms.category.dubbo.service.CategoryService"
                  protocol="jsf"
                  alias="${jd.jsf.consumer.gms.category.alias}"
                  timeout="${jd.jsf.consumer.gms.category.timeout}"
    >
    </jsf:consumer>

    <!-- 限购中台最小起购量查询服务-->
    <jsf:consumer id="strategyMetadataService" interface="com.jd.limitbuy.service.StrategyMetadataService"
                  protocol="jsf" alias="${jd.jsf.consumer.limitbuy.alias}" timeout="${jd.jsf.consumer.limitbuy.timeout}">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jd.jsf.consumer.limitbuy.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdi-isc-product-soa" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${jd.jsf.consumer.limitbuy.alias}" hide="true" />
    </jsf:consumer>
</beans>