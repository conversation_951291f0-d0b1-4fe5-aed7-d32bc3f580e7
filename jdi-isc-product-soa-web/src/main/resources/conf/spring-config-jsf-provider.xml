<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd">

	<jsf:server id="jsf-write" protocol="jsf" port="22020"/>
	<jsf:server id="jsf-read" protocol="jsf" port="22030"/>

	<!-- 库存写服务 -->
	<jsf:provider id="iscProductSoaStockApiService"
				  interface="com.jdi.isc.product.soa.api.stock.IscProductSoaStockWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.writeTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaStockWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 库存读服务 -->
	<jsf:provider id="iscProductSoaStockReadApiService"
				  interface="com.jdi.isc.product.soa.api.stock.IscProductSoaStockReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.writeTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaStockReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- mku相关服务 -->
	<jsf:provider id="iscProductSoaMkuApiService"
				  interface="com.jdi.isc.product.soa.api.wisp.mku.MkuClientApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.writeTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="mkuClientApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 京麦品牌查询服务 -->
	<jsf:provider id="jdmBrandReadService"
				  interface="com.jdi.isc.product.soa.api.jdm.brand.JdmBrandReadService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="jdmBrandReadServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 京麦类目查询服务 -->
	<jsf:provider id="jdmCategoryReadService"
				  interface="com.jdi.isc.product.soa.api.jdm.category.JdmCategoryReadService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="jdmCategoryReadServiceImpl" server="jsf-write" >
	</jsf:provider>


	<!-- 京麦商品搜索服务 -->
	<jsf:provider id="jdmSkuReadService"
				  interface="com.jdi.isc.product.soa.api.jdm.sku.JdmSkuReadService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="jdmSkuReadServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 京麦商品写入服务 -->
	<jsf:provider id="jdmSkuWriteService"
				  interface="com.jdi.isc.product.soa.api.jdm.sku.JdmSkuWriteService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="jdmSkuWriteServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 京麦商品写入服务 -->
	<jsf:provider id="jdmPushMessageService"
				  interface="com.jdi.isc.product.soa.api.jdm.push.JdmPushMessageService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="jdmPushMessageServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 国家语言相关服务 -->
	<jsf:provider id="iscProductSoaCountryLangApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.country.IscProductSoaCountryLangApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.writeTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaCountryLangApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 语言主数据相关服务 -->
	<jsf:provider id="iscProductSoaLangApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.lang.IscProductSoaLangApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.longTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaLangApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 国家主数据相关服务 -->
	<jsf:provider id="iscProductSoaCountryApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.country.IscProductSoaCountryApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaCountryApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 类目属性服务 -->
	<jsf:provider id="globalAttributeApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.category.GlobalAttributeApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="globalAttributeApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- SPU写服务 -->
	<jsf:provider id="iscProductSoaSpuWriteApiService"
				  interface="com.jdi.isc.product.soa.api.spu.IscProductSoaSpuWriteApiService"
				  alias="${jd.jsf.provider.spuWrite.alias}"
				  timeout="${jd.jsf.provider.spuWrite.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaSpuWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- SPU读服务 -->
	<jsf:provider id="iscProductSoaSpuReadApiService"
				  interface="com.jdi.isc.product.soa.api.spu.IscProductSoaSpuReadApiService"
				  alias="${jd.jsf.provider.spuRead.alias}"
				  timeout="${jd.jsf.provider.spuRead.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaSpuReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

<!--	&lt;!&ndash; 保税商品审核记录服务 &ndash;&gt;-->
<!--	<jsf:provider id="wiopBondedAuditApiService"-->
<!--				  interface="com.jdi.isc.product.soa.api.wiop.bonded.WiopBondedAuditApiService"-->
<!--				  alias="${jd.jsf.common.alias}"-->
<!--				  timeout="${jd.jsf.common.timeout}"-->
<!--				  serialization="hessian"-->
<!--				  delay="5000"-->
<!--				  ref="wiopBondedAuditApiServiceImpl" server="jsf-write" >-->
<!--	</jsf:provider>-->

	<!-- 国家属性类目关系服务 -->
	<jsf:provider id="globalCountryCategoryAttributeRelationApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.category.GlobalCountryCategoryAttributeRelationApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="globalCountryCategoryAttributeRelationApiServiceImpl" server="jsf-read" >
	</jsf:provider>


	<!-- 跨境资质服务 -->
	<jsf:provider id="globalQualificationApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.category.GlobalQualificationApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="globalQualificationApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 跨境资质类目关系服务 -->
	<jsf:provider id="globalCountryQualificationCategoryRelationApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.category.GlobalCountryQualificationCategoryRelationApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="globalCountryQualificationCategoryRelationApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 仓库读服务 -->
	<jsf:provider id="iscWarehouseReadApiService"
				  interface="com.jdi.isc.product.soa.api.warehouse.IscWarehouseReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscWarehouseReadApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 仓库写服务 -->
	<jsf:provider id="iscWarehouseWriteApiService"
				  interface="com.jdi.isc.product.soa.api.warehouse.IscWarehouseWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscWarehouseWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>
    <!-- 类目品牌关系服务 -->
    <jsf:provider id="brandCategoryApiService"
                  interface="com.jdi.isc.product.soa.api.wimp.brand.BrandCategoryApiService"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian"
                  delay="5000"
                  ref="brandCategoryApiServiceImpl" server="jsf-read" >
    </jsf:provider>

	<!-- 研发自测接口服务 -->
    <jsf:provider id="devOpsApiService"
                  interface="com.jdi.isc.product.soa.api.devOps.DevOpsApiService"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian"
                  delay="5000"
                  ref="devOpsApiServiceImpl" server="jsf-read" >
    </jsf:provider>

	<!-- 品牌服务 -->
	<jsf:provider id="brandApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.brand.BrandApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="brandApiServiceImpl" server="jsf-write" >
	</jsf:provider>
	<!-- 保税商品审核记录服务 -->
	<jsf:provider id="wiopBondedAuditApiService"
				  interface="com.jdi.isc.product.soa.api.wiop.bonded.WiopBondedAuditApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="wiopBondedAuditApiServiceImpl" server="jsf-write" >
	</jsf:provider>


	<!-- 保税商品审核记录服务 -->
	<jsf:provider id="brandJobApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.brand.BrandJobApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="brandJobApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 客户MKU写服务 -->
	<jsf:provider id="iscProductSoaCustomerMkuWriteApiService"
				  interface="com.jdi.isc.product.soa.api.customerMku.IscProductSoaCustomerMkuWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.writeTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaCustomerMkuWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- sku读服务 -->
	<jsf:provider id="iscProductSoaSkuReadApiService"
				  interface="com.jdi.isc.product.soa.api.sku.IscProductSoaSkuReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaSkuReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- mku读服务 -->
	<jsf:provider id="iscProductSoaCustomerMkuReadApiService"
				  interface="com.jdi.isc.product.soa.api.customerMku.IscProductSoaCustomerMkuReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaCustomerMkuReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>


	<!-- MKU物料管理写服务 -->
	<jsf:provider id="iscMkuMaterialWriteApiService"
				  interface="com.jdi.isc.product.soa.api.material.IscMkuMaterialWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscMkuMaterialWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>


	<!-- MKU物料管理读服务 -->
	<jsf:provider id="iscMkuMaterialReadApiService"
				  interface="com.jdi.isc.product.soa.api.material.IscMkuMaterialReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscMkuMaterialReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>


	<!-- 货币主数据相关服务 -->
	<jsf:provider id="iscProductSoaCurrencyApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.currency.IscProductSoaCurrencyApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaCurrencyApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- sku写服务 -->
	<jsf:provider id="iscProductSoaSkuWriteApiService"
				  interface="com.jdi.isc.product.soa.api.sku.IscProductSoaSkuWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaSkuWriteApiServiceImpl" server="jsf-write" >
		<jsf:method name="batchFixBrNcmCode" timeout="60000"/>
	</jsf:provider>


	<!-- 翻译服务 -->
	<jsf:provider id="iscProductSoaTranslateApiService"
				  interface="com.jdi.isc.product.soa.api.translate.IscProductSoaTranslateApiService"
				  alias="${jd.jsf.provider.translate.alias}"
				  timeout="${jd.jsf.provider.translate.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaTranslateApiServiceImpl" server="jsf-read" >
	</jsf:provider>
	<!-- 查询供应商服务 -->
	<jsf:provider id="supplierReadApiService"
				  interface="com.jdi.isc.product.soa.api.supplier.SupplierReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="supplierReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 查询供应商产品线服务 -->
	<jsf:provider id="businessLineReadApiService"
				  interface="com.jdi.isc.product.soa.api.supplier.BusinessLineReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="businessLineReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 查询属性服务 -->
	<jsf:provider id="iscAttributeReadApiService"
				  interface="com.jdi.isc.product.soa.api.attribute.IscAttributeReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.writeTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscAttributeReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 类目 -->
	<jsf:provider id="jdiIscSoaCategoryApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.category.CategoryApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="300000"
				  serialization="hessian"
				  delay="5000"
				  ref="categoryApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 类目 -->
	<jsf:provider id="iscProductSoaCategoryWriteApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.category.CategoryWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.longTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="categoryWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 跨境属性写服务 -->
	<jsf:provider id="iscProductGlobalAttributeWriteApiService"
				  interface="com.jdi.isc.product.soa.api.spu.IscProductGlobalAttributeWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductGlobalAttributeWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 订单履约信息缺失读服务 -->
	<jsf:provider id="iscOrderOutVerifyReadApiService"
				  interface="com.jdi.isc.product.soa.api.orderVerify.IscOrderOutVerifyReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscOrderOutVerifyReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>
	<!-- 订单履约信息缺失写服务 -->
	<jsf:provider id="iscOrderOutVerifyWriteApiService"
				  interface="com.jdi.isc.product.soa.api.orderVerify.IscOrderOutVerifyWriteApiService"
				  alias="${jd.jsf.provider.orderVerify.alias}"
				  timeout="${jd.jsf.provider.orderVerify.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscOrderOutVerifyWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>
	<!-- MKU读服务 -->
	<jsf:provider id="iscProductSoaMkuReadApiService"
				  interface="com.jdi.isc.product.soa.api.mku.IscProductSoaMkuReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaMkuReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 客户类目关系管理 -->
	<jsf:provider id="clientCategoryRelationApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.category.ClientCategoryRelationApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="clientCategoryRelationApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 利润率阈值写服务 -->
	<jsf:provider id="iscProductSoaProfitRateWriteApiService"
				  interface="com.jdi.isc.product.soa.api.price.IscProductSoaProfitRateWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.writeTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaProfitRateWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 利润率阈值读服务 -->
	<jsf:provider id="iscProductSoaProfitRateReadApiService"
				  interface="com.jdi.isc.product.soa.api.price.IscProductSoaProfitRateReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaProfitRateReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 履约费率写服务 -->
	<jsf:provider id="iscProductSoaFulfillmentRateWriteApiService"
				  interface="com.jdi.isc.product.soa.api.price.IscProductSoaFulfillmentRateWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.writeTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaFulfillmentRateWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 履约费率读服务 -->
	<jsf:provider id="iscProductSoaFulfillmentRateReadApiService"
				  interface="com.jdi.isc.product.soa.api.price.IscProductSoaFulfillmentRateReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaFulfillmentRateReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- sku客制化价格写服务 -->
	<jsf:provider id="iscProductSoaCustomerSkuPriceWriteApiService"
				  interface="com.jdi.isc.product.soa.api.customerSku.IscProductSoaCustomerSkuPriceWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.writeTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaCustomerSkuPriceWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 履约费率读服务 -->
	<jsf:provider id="iscProductSoaCustomerSkuPriceReadApiService"
				  interface="com.jdi.isc.product.soa.api.customerSku.IscProductSoaCustomerSkuPriceReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaCustomerSkuPriceReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 中国税率服务 -->
	<jsf:provider id="iscProductSoaExportTaxRateApiService"
				  interface="com.jdi.isc.product.soa.api.taxRate.IscProductSoaExportTaxRateApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaExportTaxRateApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 税率写服务 -->
	<jsf:provider id="iscTaxRateWriteApiService"
				  interface="com.jdi.isc.product.soa.api.taxRate.IscTaxRateWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscTaxRateWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 税率读服务 -->
	<jsf:provider id="iscTaxRateReadApiService"
				  interface="com.jdi.isc.product.soa.api.taxRate.IscTaxRateReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscTaxRateReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 类目禁止国家服务 -->
	<jsf:provider id="iscCategoryCountryApiService"
				  interface="com.jdi.isc.product.soa.api.category.IscCategoryCountryApiService"
				  alias="${jd.jsf.provider.category.alias}"
				  timeout="${jd.jsf.provider.category.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscCategoryCountryApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 国家池读服务 -->
	<jsf:provider id="iscCountryMkuReadApiService"
				  interface="com.jdi.isc.product.soa.api.countryMku.IscCountryMkuReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscCountryMkuReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 国家池写服务 -->
	<jsf:provider id="iscCountryMkuWriteApiService"
				  interface="com.jdi.isc.product.soa.api.countryMku.IscCountryMkuWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.writeTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscCountryMkuWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 价格监控服务 -->
	<jsf:provider id="skuPriceMonitorApiService"
				  interface="com.jdi.isc.product.soa.api.price.SkuPriceMonitorApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.longTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="skuPriceMonitorApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 特殊属性服务 -->
	<jsf:provider id="specialAttrApiService"
				  interface="com.jdi.isc.product.soa.api.specialAttr.SpecialAttrApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="specialAttrApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 特殊属性服务 -->
	<jsf:provider id="specialAttrRelationApiService"
				  interface="com.jdi.isc.product.soa.api.specialAttr.SpecialAttrRelationApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="specialAttrRelationApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 跨境税率关系 -->
	<jsf:provider id="hscodeTaxRateRelationApiService"
				  interface="com.jdi.isc.product.soa.api.taxRate.HscodeTaxRateRelationApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="hscodeTaxRateRelationApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 跨境税率关系写 -->
	<jsf:provider id="iscHscodeTaxRateRelationWriteApiService"
				  interface="com.jdi.isc.product.soa.api.taxRate.IscHscodeTaxRateRelationWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscHscodeTaxRateRelationWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 国际商品标签写服务 -->
	<jsf:provider id="iscMkuTagWriteApiService"
				  interface="com.jdi.isc.product.soa.api.mkuTag.IscMkuTagWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscMkuTagWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 国际商品发货时效查询服务 -->
	<jsf:provider id="iscPromiseReadApiService"
				  interface="com.jdi.isc.product.soa.api.wiop.promise.IscPromiseReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscPromiseReadApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 国际商品库存阈值服务 -->
	<jsf:provider id="skuStockThresholdApiService"
				  interface="com.jdi.isc.product.soa.stock.threshold.SkuStockThresholdApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="skuStockThresholdApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 国际异常库存统计服务 -->
	<jsf:provider id="abnormalStockStatisticsApiService"
				  interface="com.jdi.isc.product.soa.stock.threshold.AbnormalStockStatisticsApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="abnormalStockStatisticsApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 国际商品跨境属性读服务 -->
	<jsf:provider id="iscProductSoaProductGlobalAttributeReadApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.product.IscProductSoaProductGlobalAttributeReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaProductGlobalAttributeReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 国际商品跨境属性写服务 -->
	<jsf:provider id="iscProductSoaProductGlobalAttributeWriteApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.product.IscProductSoaProductGlobalAttributeWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaProductGlobalAttributeWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>


	<!-- 短标题读服务 -->
	<jsf:provider id="iscSubtitleReadApiService"
				  interface="com.jdi.isc.product.soa.api.subtitle.IscSubtitleReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscSubtitleReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- mku写服务读服务 -->
	<jsf:provider id="iscProductSoaMkuWriteApiService"
				  interface="com.jdi.isc.product.soa.api.mku.IscProductSoaMkuWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaMkuWriteApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- sku库存读服务 -->
	<jsf:provider id="iscSkuStockReadApiService"
				  interface="com.jdi.isc.product.soa.stock.sku.IscSkuStockReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscSkuStockReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- mku库存读服务 -->
	<jsf:provider id="iscMkuStockReadApiService"
				  interface="com.jdi.isc.product.soa.stock.mku.IscMkuStockReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscMkuStockReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>
	<!-- 加价率读服务 -->
	<jsf:provider id="iscMarkupRateReadApiService"
				  interface="com.jdi.isc.product.soa.api.markupRate.IscMarkupRateReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscMarkupRateReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 加价率写服务 -->
	<jsf:provider id="iscMarkupRateWriteApiService"
				  interface="com.jdi.isc.product.soa.api.markupRate.IscMarkupRateWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscMarkupRateWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 协议价读服务 -->
	<jsf:provider id="iscCountryAgreementPriceReadApiService"
				  interface="com.jdi.isc.product.soa.api.agreementPrice.IscCountryAgreementPriceReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscCountryAgreementPriceReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 协议价写服务 -->
	<jsf:provider id="iscCountryAgreementPriceWriteApiService"
				  interface="com.jdi.isc.product.soa.api.agreementPrice.IscCountryAgreementPriceWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.writeTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscCountryAgreementPriceWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 汇率写服务 -->
	<jsf:provider id="iscProductSoaExchangeRateWriteApiService"
				  interface="com.jdi.isc.product.soa.api.price.IscProductSoaExchangeRateWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.writeTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaExchangeRateWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 汇率读服务 -->
	<jsf:provider id="iscProductSoaExchangeRateReadApiService"
				  interface="com.jdi.isc.product.soa.api.price.IscProductSoaExchangeRateReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaExchangeRateReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 价格日志服务 -->
	<jsf:provider id="iscProductSoaPriceLogApiService"
				  interface="com.jdi.isc.product.soa.price.api.common.IscProductSoaPriceLogApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaPriceLogApiServiceImpl" server="jsf-read" >
	</jsf:provider>


	<!--  扩展价（参考价）读服务 -->
	<jsf:provider id="iscCountryExtendPriceReadApiService"
				  interface="com.jdi.isc.product.soa.api.extendPrice.IscCountryExtendPriceReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscCountryExtendPriceReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 扩展价（参考价）写服务 -->
	<jsf:provider id="iscCountryExtendPriceWriteApiService"
				  interface="com.jdi.isc.product.soa.api.extendPrice.IscCountryExtendPriceWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscCountryExtendPriceWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 履约费率读写服务 -->
	<jsf:provider id="iscProductSoaFulfillmentRateApiService"
				  interface="com.jdi.isc.product.soa.price.api.price.IscProductSoaFulfillmentRateApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaFulfillmentRateApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 国际SKU价格写服务 -->
	<jsf:provider id="iscProductSoaPriceWriteApiService"
				  interface="com.jdi.isc.product.soa.price.api.price.IscProductSoaPriceWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaPriceWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 国际SKU税率读服务 -->
	<jsf:provider id="iscProductSoaTaxReadApiService"
				  interface="com.jdi.isc.product.soa.price.api.price.IscProductSoaTaxReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaTaxReadApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 国际SKU税率写服务 -->
	<jsf:provider id="iscProductSoaTaxWriteApiService"
				  interface="com.jdi.isc.product.soa.price.api.price.IscProductSoaTaxWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaTaxWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 销售价格读服务 -->
	<jsf:provider id="iscProductSoaSalePriceReadApiService"
				  interface="com.jdi.isc.product.soa.price.api.salePrice.IscProductSoaSalePriceReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaSalePriceReadApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 销售价格写服务 -->
	<jsf:provider id="iscProductSoaSalePriceWriteApiService"
				  interface="com.jdi.isc.product.soa.price.api.salePrice.IscProductSoaSalePriceWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaSalePriceWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- jd价格读服务 -->
	<jsf:provider id="iscProductSoaJdPriceReadApiService"
				  interface="com.jdi.isc.product.soa.price.api.jdPrice.IscProductSoaJdPriceReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaJdPriceReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 销端价格读服务 -->
	<jsf:provider id="clientPriceReadApiService"
				  interface="com.jdi.isc.product.soa.price.api.clientPrice.ClientPriceReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="clientPriceReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- ducc配置读服务 -->
	<jsf:provider id="duccReadApiService"
				  interface="com.jdi.isc.product.soa.api.ducc.DuccReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="duccReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- wiop消息读写服务 -->
	<jsf:provider id="wiopMsgApiService"
				  interface="com.jdi.isc.product.soa.api.message.WiopMsgApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="wiopMsgApiServiceImpl" server="jsf-write" >
	</jsf:provider>




	<!-- sku采购价读服务 -->
	<jsf:provider id="iscSkuPriceReadApiService"
				  interface="com.jdi.isc.product.soa.api.price.supplierPrice.IscSkuPriceReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscSkuPriceReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- Sku采购价写服务 -->
	<jsf:provider id="iscSkuPriceWriteApiService"
				  interface="com.jdi.isc.product.soa.api.price.supplierPrice.IscSkuPriceWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.writeTimeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscSkuPriceWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 外链读写服务 -->
	<jsf:provider id="iscOutUrlApiService"
				  interface="com.jdi.isc.product.soa.api.outUrl.IscOutUrlApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscOutUrlApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 跨境资质读服务 -->
	<jsf:provider id="iscCertificateReadApiService"
				  interface="com.jdi.isc.product.soa.api.certificate.IscCertificateReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscCertificateReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 供应商开放消息读写服务 -->
	<jsf:provider id="supplierMsgApiService"
				  interface="com.jdi.isc.product.soa.api.message.SupplierMsgApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="supplierMsgApiServiceImpl" server="jsf-write" >
	</jsf:provider>


	<!-- 历史实际退税成功率api写服务 -->
	<jsf:provider id="iscTaxRefundRateWriteApiService"
				  interface="com.jdi.isc.product.soa.api.price.taxRefundRate.IscTaxRefundRateWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscTaxRefundRateWriteApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 历史实际退税成功率api读服务 -->
	<jsf:provider id="iscTaxRefundRateReadApiService"
				  interface="com.jdi.isc.product.soa.api.price.taxRefundRate.IscTaxRefundRateReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscTaxRefundRateReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 类目相关服务 -->
	<jsf:provider id="iscProductSoaCategoryApiService"
				  interface="com.jdi.isc.product.soa.api.category.IscProductSoaCategoryApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaCategoryApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 研发自测类目接口服务 -->
	<jsf:provider id="devOpsCateApiService"
				  interface="com.jdi.isc.product.soa.api.devOps.DevOpsCateApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="devOpsCateApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 类目采销关系 -->
	<jsf:provider id="categoryBuyerRelationApiService"
				  interface="com.jdi.isc.product.soa.api.wimp.category.CategoryBuyerRelationApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="categoryBuyerRelationApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 研发自测类目接口服务 -->
	<jsf:provider id="devOpsEsApiService"
				  interface="com.jdi.isc.product.soa.api.devOps.DevOpsEsApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="devOpsEsApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 类目统计 -->
	<jsf:provider id="iscProductSoaCategoryConfigStatisticsApiService"
				  interface="com.jdi.isc.product.soa.api.category.IscProductSoaCategoryConfigStatisticsApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaCategoryConfigStatisticsApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 权限查询 -->
	<jsf:provider id="iscProductSoaAuthReadApiService"
				  interface="com.jdi.isc.product.soa.api.auth.IscProductSoaAuthReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaAuthReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- sku库存管理服务 -->
	<jsf:provider id="iscSkuStockManageApiService"
				  interface="com.jdi.isc.product.soa.stock.sku.IscSkuStockManageApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscSkuStockManageApiServiceImpl" server="jsf-read" >
	</jsf:provider>
	<!-- 商品服务-审核-写服务 -->
	<jsf:provider id="approveOrderWriteApiService"
				  interface="com.jdi.isc.product.soa.api.approveorder.ApproveOrderWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="approveOrderApiServiceImpl" server="jsf-write" >
	</jsf:provider>

	<!-- 商品服务-审核-读服务 -->
	<jsf:provider id="approveOrderReadApiService"
				  interface="com.jdi.isc.product.soa.api.approveorder.ApproveOrderReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="approveOrderApiServiceImpl" server="jsf-write" >
	</jsf:provider>




	<!-- 国内商品价格查询服务 -->
	<jsf:provider id="iscSkuDomesticPriceReadApiService"
				  interface="com.jdi.isc.product.soa.price.api.jdPrice.IscSkuDomesticPriceReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscSkuDomesticPriceReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 销售单位查询服务 -->
	<jsf:provider id="iscSaleUnitReadApiService"
				  interface="com.jdi.isc.product.soa.api.saleUnit.IscSaleUnitReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscSaleUnitReadApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 品牌查询服务 -->
	<jsf:provider id="iscProductSoaBrandApiService"
				  interface="com.jdi.isc.product.soa.api.brand.IscProductSoaBrandApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscProductSoaBrandApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 类目税率写服务 -->
	<jsf:provider id="iscCategoryTaxRateWriteApiService"
				  interface="com.jdi.isc.product.soa.api.taxRate.IscCategoryTaxRateWriteApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscCategoryTaxRateWriteApiServiceImpl" server="jsf-read" >
	</jsf:provider>

	<!-- 类目税率读服务 -->
	<jsf:provider id="iscCategoryTaxRateReadApiService"
				  interface="com.jdi.isc.product.soa.api.taxRate.IscCategoryTaxRateReadApiService"
				  alias="${jd.jsf.common.alias}"
				  timeout="${jd.jsf.common.timeout}"
				  serialization="hessian"
				  delay="5000"
				  ref="iscCategoryTaxRateReadApiServiceImpl" server="jsf-write" >
	</jsf:provider>

</beans>
