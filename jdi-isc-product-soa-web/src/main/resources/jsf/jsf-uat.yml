jd:
  jsf:
    registry:
      index: i.jsf.jd.local
    common:
      alias: uat:1.0
      timeout: 5000
      writeTimeout: 10000
      longTimeout: 30000
    app:
      productSoa: J-dos-jdi-isc-product-soa
    consumer:
      attributeGroup:
        alias: category_yfb_cn
        token: 6a99291a-8cd4-42e7-bc8a-2dd277df0dfc
      mapping:
        alias: ztcs
        token: pre:1.0
      anycall:
        alias: anycall-center-test
      vc:
        auth:
          alias: VCAUTH_YF
      gd:
        pool:
          alias: jdi_customer_pool_man_core:pro
          timeout: 5000
      gmsAreaStock:
        alias: ss_lf_public
        token: 6de34936-674d-4b44-8168-70676e285340
      jme:
        alias: uat:1.0
      gmsCate:
        alias: category_yfb_cn
        token: 5e3823e4-c92f-434c-98a8-da896b889fdd
      gmsCateSearch:
        alias: category_yfb_cn
        token: 2f44a302-58d5-48fe-9073-bb09c79d1326
      gmsCateTree:
        alias: category_yfb_cn
        token: 17c72f79-6689-48d4-a675-b454627626f1
      categorySaleAtt:
        alias: category_yfb_cn
        token: 8aa70fc5-24af-464f-8352-f968bb932f7d
      auth:
        alias: pre
        timeout: 5000
      user:
        soa:
          alias: ka-user-soa_yufa:0.0.1
          timeout: 5000
          token: a29d3746-9bdc-44ce-b368-c18094bff886
      buser:
        sdk:
          alias: buser-pre:0.0.2
          timeout: 5000
      limitbuy:
        alias: WebFlat-LF-Test
        token: 8195db42-df02-4785-88b4-13a7b1d9f2f0
        timeout: 5000
    provider:
      spuRead:
        alias: uat:1.0
        timeout: 15000
      xxx:
        alias: pre:1.0
        timeout: 5000
      orderVerify:
        alias: uat:1.0
        timeout: 5000
      translate:
        alias: uat:1.0
        timeout: 20000
      category:
        alias: uat:1.0
        timeout: 30000
      extendData:
        alias: k2_gd_boost:yufa_xl
        timeout: 5000
