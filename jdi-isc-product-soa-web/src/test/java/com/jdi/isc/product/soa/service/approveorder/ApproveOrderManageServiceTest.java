package com.jdi.isc.product.soa.service.approveorder;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.approveorder.common.AuditActionEnum;
import com.jdi.isc.product.soa.api.approveorder.common.AuditBizTypeEnum;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderAuditMqDTO;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderCreateMqDTO;
import com.jdi.isc.product.soa.api.approveorder.mq.ApproveOrderMqDTO;
import com.jdi.isc.product.soa.api.approveorder.mq.JoySkyBizApprovalResultMqDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderFormQueryDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderPageQueryDTO;
import com.jdi.isc.product.soa.api.approveorder.req.ApproveOrderSaveReqDTO;
import com.jdi.isc.product.soa.api.approveorder.res.ApproveOrderPageResDTO;
import com.jdi.isc.product.soa.api.approveorder.res.ApproveOrderSaveResDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiReqDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiResDTO;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.approveorder.res.ApproveOrderResVO;
import com.jdi.isc.product.soa.domain.enums.AuditStatusEnum;
import com.jdi.isc.product.soa.joySky.biz.ApprovalResultMsg;
import com.jdi.isc.product.soa.service.manage.approveorder.ApproveOrderManageService;
import com.jdi.isc.product.soa.web.ServiceApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@SpringBootTest(classes = ServiceApplication.class)
@ActiveProfiles("test")
public class ApproveOrderManageServiceTest {

    @Resource
    private ApproveOrderManageService approveOrderManageService;

    @Test
    public void testListWithPage() {
        ApproveOrderPageQueryDTO qry = new ApproveOrderPageQueryDTO();
//        qry.setApplyCode("001");
        qry.setIndex(1L);
        qry.setSize(20L);
        qry.setProcessType(100001);
        qry.setProcessType(AuditBizTypeEnum.TYPE_1.getCode());
        qry.setPin("liuzhaoming.10");

        List<ApproveOrderFormQueryDTO> list = Lists.newArrayList();

//        list.add(new ApproveOrderFormQueryDTO("sku_id", "8001", "eq"));
//        list.add(new ApproveOrderFormQueryDTO("sku_name", "sku测试名称1", "eq"));
//        qry.setFormQueryList(list);

        ApiInitUtils.init(qry);
        PageInfo<ApproveOrderPageResDTO> page = approveOrderManageService.listWithPage(qry);

        log.info("page={}", page);
    }


    /**
     * 详情
     */
    public void detail() {
        Long id = 1L;
        ApproveOrderResVO detail = approveOrderManageService.detail(id);

        System.out.println(detail);
    }

    /**
     * 创建审计API记录
     */
    @Test
    public void create1() {
        ApproveOrderSaveReqDTO input = new ApproveOrderSaveReqDTO();
        ApproveOrderDTO order = new ApproveOrderDTO();
        order.setBizId(String.valueOf(26904));
        order.setBizType(AuditBizTypeEnum.TYPE_1.getCode());
        input.setOrder(order);

        order.setApplyReason("这是一个测试demo");
        order.setApplyUserErp("liuzhaoming.10");

        input.setPin("liuzhaoming.10");

        ApiInitUtils.init(input);
        DataResponse<ApproveOrderSaveResDTO> response = approveOrderManageService.create(input);

        log.info("create response={}", response);
    }

    @Test
    public void create2() {
        ApproveOrderSaveReqDTO input = new ApproveOrderSaveReqDTO();
        ApproveOrderDTO order = new ApproveOrderDTO();
        order.setBizId(String.valueOf(14200));
        order.setBizType(AuditBizTypeEnum.TYPE_2.getCode());
        input.setOrder(order);

        order.setApplyReason("这是一个测试demo");
        order.setApplyUserErp("liuzhaoming.10");

        input.setPin("liuzhaoming.10");

        ApiInitUtils.init(input);
        DataResponse<ApproveOrderSaveResDTO> response = approveOrderManageService.create(input);

        log.info("create2 response={}", response);
    }

    /**
     * 审核API请求并返回审核结果
     */
    @Test
    public void auditPass() {
        AuditApiReqDTO input = new AuditApiReqDTO();
        input.setAction(AuditActionEnum.PASS.getCode());
        input.setBizType(AuditBizTypeEnum.TYPE_2.getCode());
        input.setForce(false);
        input.setIds(Lists.newArrayList(31L));
        input.setApproveComment("测试审核通过");

        input.setPin("liuzhaoming.10");
        ApiInitUtils.init(input);
        DataResponse<AuditApiResDTO> response = approveOrderManageService.audit(input);

        log.info("auditPass response={}", response);
    }

    @Test
    public void auditReject() {
        AuditApiReqDTO input = new AuditApiReqDTO();
        input.setAction(AuditActionEnum.REJECT.getCode());
        input.setBizType(AuditBizTypeEnum.TYPE_1.getCode());
        input.setForce(false);
        input.setIds(Lists.newArrayList(12L));
        input.setApproveComment("测试审核驳回");

        input.setPin("liuzhaoming.10");
        ApiInitUtils.init(input);
        DataResponse<AuditApiResDTO> response = approveOrderManageService.audit(input);

        log.info("auditReject response={}", response);
    }

    /**
     * 创建审核表后mq回调.
     */
    @Test
    public void handleCreateMessage() {
        ApproveOrderMqDTO<ApproveOrderCreateMqDTO> input = new ApproveOrderMqDTO<>();
        input.setAuditAction(AuditActionEnum.CREATE.getCode());
        input.setBusinessId(null);

        ApproveOrderCreateMqDTO createMqDTO = new ApproveOrderCreateMqDTO(14L);

        input.setData(createMqDTO);
        input.setPin("liuzhaoming.10");



        ApiInitUtils.init(input);
        approveOrderManageService.handleCreateMessage(input);

    }

    /**
     * 审核数据后mq回调.
     */
    @Test
    public void handleAuditMessage() {
        ApproveOrderMqDTO<ApproveOrderAuditMqDTO> input = new ApproveOrderMqDTO<>();
        input.setAuditAction(AuditActionEnum.PASS.getCode());

        ApproveOrderAuditMqDTO auditMqDTO = new ApproveOrderAuditMqDTO(34L, "测试回调消息");
//        ApproveOrderAuditMqDTO auditMqDTO = JSONObject.parseObject("{\"auditAction\":1,\"businessId\":\"approve_id_1_34\",\"data\":{\"approveId\":34}}", ApproveOrderAuditMqDTO.class);

        input.setData(auditMqDTO);


        input.setPin("liuzhaoming.10");
        ApiInitUtils.init(input);
        approveOrderManageService.handleAuditMessage(input);
    }

    /**
     * 批量处理JoySky业务审批结果
     */
    @Test
    public void batchJoySkyApprove() {

        String json = "{\"operator\":\"liuzhaoming.10\",\"processInstanceId\":\"e44b57e4-5b41-11f0-93b2-fa163e82aea0\",\"processKey\":\"process1751881094228\",\"processStatus\":5,\"processType\":107,\"rejectReason\":\"\"}";

        ApprovalResultMsg approve = JSONObject.parseObject(json, ApprovalResultMsg.class);

        approve.setProcessInstanceId("ebb4af3a-5d71-11f0-bf63-fa163ee5673e");
        approve.setProcessStatus(AuditStatusEnum.APPROVED.getCode());
        approve.setRejectReason("同意");
        approve.setProcessKey("process1751888743221");
        approve.setProcessType(106);
        approve.setOperator("yuxinyue10");


        approveOrderManageService.batchJoySkyApprove(BeanUtil.toBean(approve, JoySkyBizApprovalResultMqDTO.class));


    }
}
