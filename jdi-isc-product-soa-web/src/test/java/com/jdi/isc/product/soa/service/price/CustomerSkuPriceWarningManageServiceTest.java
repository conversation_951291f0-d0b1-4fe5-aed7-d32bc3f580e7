package com.jdi.isc.product.soa.service.price;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import com.jdi.isc.product.soa.api.customerSku.req.CustomerSkuPriceWarningDTO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceWarningPageReqVO;
import com.jdi.isc.product.soa.domain.customerSku.biz.CustomerSkuPriceWarningVO;
import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.service.manage.customerSku.CustomerSkuPriceWarningManageService;
import com.jdi.isc.product.soa.service.mapstruct.customerSku.CustomerSkuPriceConvert;
import com.jdi.isc.product.soa.web.ServiceApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@AutoConfigureMockMvc
@SpringBootTest(classes = ServiceApplication.class)
public class CustomerSkuPriceWarningManageServiceTest {

    @Resource
    private CustomerSkuPriceWarningManageService customerSkuPriceWarningManageService;

    @Test
    public void test() {
        CustomerSkuPriceWarningPageReqVO query = new CustomerSkuPriceWarningPageReqVO();
        query.setWarningStatus(4);
        query.setTargetCountryCode("BR");
        query.setSkuIds(Lists.newArrayList(80000000047L, 80000000062L));
        query.setSourceCountryCode("CN");

        query.setDataStatusSource(2);
        query.setLastCatId(17563L);
        query.setBrandId(8000L);
        query.setBeginWarningTime(0L);
        query.setEndWarningTime(new Date().getTime());
        query.setWarningStatusList(Lists.newArrayList(4));

        query.setAvailableSaleStatus(20);
        query.setPreseletorClientCodeList(Lists.newArrayList("123", "2345"));


        query.setCountryMkuPoolStatus(CountryMkuPoolStatusEnum.POOL.getCode());
        query.setCustomerMkuPoolStatus(CustomerMkuBindEnum.BIND.name());
        query.setAuditStatus(10);

        query.setIndex(1L);
        query.setSize(10L);

        query = JSONObject.parseObject("{\"index\":1,\"size\":20,\"countryCode\":\"\",\"productIds\":[],\"availableSaleStatus\":\"20\",\"lastLevelName\":\"fourLevel\"}", CustomerSkuPriceWarningPageReqVO.class);
//        query.setWarningStatusList(Lists.newArrayList(1,2,3,4));

        PageInfo<CustomerSkuPriceWarningVO> page = customerSkuPriceWarningManageService.pageSearch(query);

        PageInfo<CustomerSkuPriceWarningDTO> pageInfo = CustomerSkuPriceConvert.INSTANCE.warningPageVo2Dto(page);

        log.info("pageInfo={}", pageInfo);
    }
}
