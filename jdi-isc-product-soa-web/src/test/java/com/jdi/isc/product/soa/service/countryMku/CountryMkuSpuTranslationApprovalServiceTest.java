package com.jdi.isc.product.soa.service.countryMku;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.countryMku.po.CountryMkuSpuTranslationApprovalPO;
import com.jdi.isc.product.soa.service.atomic.countryMku.CountryMkuSpuTranslationApprovalAtomicService;
import com.jdi.isc.product.soa.web.ServiceApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @Description: 国际池商品名称翻译审核表原子服务单元测试
 * @Author: zhaoyan316
 * @Date: 2025-08-26
 **/
@Slf4j
@AutoConfigureMockMvc
@SpringBootTest(classes = ServiceApplication.class)
@Transactional
@Rollback
public class CountryMkuSpuTranslationApprovalServiceTest {

    @Resource
    private CountryMkuSpuTranslationApprovalAtomicService countryMkuSpuTranslationApprovalAtomicService;

    private final String TEST_COUNTRY_CODE = "TH";
    private final Long BASE_SPU_ID = 10001L;

    @BeforeEach
    public void setUp() {
        // 初始化登录上下文
        BaseReqDTO input = new BaseReqDTO();
        input.setPin("testUser");
        ApiInitUtils.init(input);
        
        // 清理测试数据
        cleanTestData();
    }

    @AfterEach
    public void tearDown() {
        // 清理测试数据
        cleanTestData();
    }

    /**
     * 清理测试数据
     */
    private void cleanTestData() {
        try {
            // 删除测试数据（通过设置yn=0实现逻辑删除）
            List<CountryMkuSpuTranslationApprovalPO> testDataList = countryMkuSpuTranslationApprovalAtomicService.getByCountryCodeAndApprovalStatus(TEST_COUNTRY_CODE, false);
            if (testDataList != null && !testDataList.isEmpty()) {
                for (CountryMkuSpuTranslationApprovalPO po : testDataList) {
                    if (po.getSpuId() >= BASE_SPU_ID && po.getSpuId() <= BASE_SPU_ID + 100) {
                        po.setYn(YnEnum.NO.getCode());
                        countryMkuSpuTranslationApprovalAtomicService.updateById(po);
                    }
                }
            }
            
            testDataList = countryMkuSpuTranslationApprovalAtomicService.getByCountryCodeAndApprovalStatus(TEST_COUNTRY_CODE, true);
            if (testDataList != null && !testDataList.isEmpty()) {
                for (CountryMkuSpuTranslationApprovalPO po : testDataList) {
                    if (po.getSpuId() >= BASE_SPU_ID && po.getSpuId() <= BASE_SPU_ID + 100) {
                        po.setYn(YnEnum.NO.getCode());
                        countryMkuSpuTranslationApprovalAtomicService.updateById(po);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Clean test data failed", e);
        }
    }

    /**
     * 测试 getBySpuIdAndCountryCode 方法 - 正常情况
     */
    @Test
    public void testGetBySpuIdAndCountryCode_Success() {
        // 准备测试数据
        Long spuId = BASE_SPU_ID + 1;
        
        // 先添加一条测试数据
        boolean addResult = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(spuId, TEST_COUNTRY_CODE);
        assertTrue(addResult);
        
        // 执行测试
        CountryMkuSpuTranslationApprovalPO result = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdAndCountryCode(spuId, TEST_COUNTRY_CODE);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(spuId, result.getSpuId());
        assertEquals(TEST_COUNTRY_CODE, result.getTargetCountryCode());
        assertFalse(result.getApprovalStatus());
        assertEquals(YnEnum.YES.getCode(), result.getYn());
    }

    /**
     * 测试 getBySpuIdAndCountryCode 方法 - 参数为空
     */
    @Test
    public void testGetBySpuIdAndCountryCode_WithNullParams() {
        // 测试 spuId 为空
        CountryMkuSpuTranslationApprovalPO result1 = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdAndCountryCode(null, TEST_COUNTRY_CODE);
        assertNull(result1);
        
        // 测试 targetCountryCode 为空
        CountryMkuSpuTranslationApprovalPO result2 = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdAndCountryCode(BASE_SPU_ID + 2, null);
        assertNull(result2);
        
        // 测试 targetCountryCode 为空字符串
        CountryMkuSpuTranslationApprovalPO result3 = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdAndCountryCode(BASE_SPU_ID + 2, "");
        assertNull(result3);
    }

    /**
     * 测试 getBySpuIdAndCountryCode 方法 - 查询结果为空
     */
    @Test
    public void testGetBySpuIdAndCountryCode_NotFound() {
        // 执行测试 - 查询一个不存在的spuId
        Long nonExistentSpuId = BASE_SPU_ID + 999;
        CountryMkuSpuTranslationApprovalPO result = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdAndCountryCode(nonExistentSpuId, TEST_COUNTRY_CODE);
        
        // 验证结果
        assertNull(result);
    }

    /**
     * 测试 getBySpuIdListAndCountryCode 方法 - 正常情况
     */
    @Test
    public void testGetBySpuIdListAndCountryCode_Success() {
        // 准备测试数据
        Long spuId1 = BASE_SPU_ID + 3;
        Long spuId2 = BASE_SPU_ID + 4;
        Long spuId3 = BASE_SPU_ID + 5;
        List<Long> spuIds = Arrays.asList(spuId1, spuId2, spuId3);
        
        // 先添加两条测试数据
        boolean addResult1 = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(spuId1, TEST_COUNTRY_CODE);
        assertTrue(addResult1);
        
        boolean addResult2 = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(spuId2, TEST_COUNTRY_CODE);
        assertTrue(addResult2);
        
        // 审核通过第二条记录
        boolean approvalResult = countryMkuSpuTranslationApprovalAtomicService.approvalSpu(spuId2, TEST_COUNTRY_CODE);
        assertTrue(approvalResult);
        
        // 执行测试
        List<CountryMkuSpuTranslationApprovalPO> result = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdListAndCountryCode(spuIds, TEST_COUNTRY_CODE);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size()); // 只有spuId1和spuId2有记录，spuId3没有
        
        // 验证返回的数据
        boolean foundSpuId1 = false, foundSpuId2 = false;
        for (CountryMkuSpuTranslationApprovalPO po : result) {
            if (po.getSpuId().equals(spuId1)) {
                foundSpuId1 = true;
                assertFalse(po.getApprovalStatus());
            } else if (po.getSpuId().equals(spuId2)) {
                foundSpuId2 = true;
                assertTrue(po.getApprovalStatus());
            }
        }
        assertTrue(foundSpuId1);
        assertTrue(foundSpuId2);
    }

    /**
     * 测试 getBySpuIdListAndCountryCode 方法 - 参数为空
     */
    @Test
    public void testGetBySpuIdListAndCountryCode_WithEmptyParams() {
        // 测试 spuIds 为空
        List<CountryMkuSpuTranslationApprovalPO> result1 = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdListAndCountryCode(null, TEST_COUNTRY_CODE);
        assertNotNull(result1);
        assertTrue(result1.isEmpty());
        
        // 测试 spuIds 为空列表
        List<CountryMkuSpuTranslationApprovalPO> result2 = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdListAndCountryCode(Collections.emptyList(), TEST_COUNTRY_CODE);
        assertNotNull(result2);
        assertTrue(result2.isEmpty());
        
        // 测试 targetCountryCode 为空
        List<CountryMkuSpuTranslationApprovalPO> result3 = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdListAndCountryCode(Arrays.asList(BASE_SPU_ID + 6), null);
        assertNotNull(result3);
        assertTrue(result3.isEmpty());
    }

    /**
     * 测试 addNeedApprovalSpu 方法 - 正常情况
     */
    @Test
    public void testAddNeedApprovalSpu_Success() {
        // 准备测试数据
        Long spuId = BASE_SPU_ID + 10;
        
        // 执行测试
        boolean result = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(spuId, TEST_COUNTRY_CODE);
        
        // 验证结果
        assertTrue(result);
        
        // 验证数据库中确实有了这条记录
        CountryMkuSpuTranslationApprovalPO savedPO = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdAndCountryCode(spuId, TEST_COUNTRY_CODE);
        assertNotNull(savedPO);
        assertEquals(spuId, savedPO.getSpuId());
        assertEquals(TEST_COUNTRY_CODE, savedPO.getTargetCountryCode());
        assertFalse(savedPO.getApprovalStatus());
        assertEquals(YnEnum.YES.getCode(), savedPO.getYn());
    }

    /**
     * 测试 addNeedApprovalSpu 方法 - 记录已存在
     */
    @Test
    public void testAddNeedApprovalSpu_RecordExists() {
        // 准备测试数据
        Long spuId = BASE_SPU_ID + 11;
        
        // 先添加一条记录
        boolean firstAddResult = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(spuId, TEST_COUNTRY_CODE);
        assertTrue(firstAddResult);
        
        // 再次尝试添加相同的记录
        boolean secondAddResult = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(spuId, TEST_COUNTRY_CODE);
        
        // 验证结果 - 第二次添加应该失败
        assertFalse(secondAddResult);
        
        // 验证数据库中只有一条记录
        CountryMkuSpuTranslationApprovalPO savedPO = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdAndCountryCode(spuId, TEST_COUNTRY_CODE);
        assertNotNull(savedPO);
    }

    /**
     * 测试 addNeedApprovalSpu 方法 - 参数为空
     */
    @Test
    public void testAddNeedApprovalSpu_WithNullParams() {
        // 测试 spuId 为空
        boolean result1 = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(null, TEST_COUNTRY_CODE);
        assertFalse(result1);
        
        // 测试 targetCountryCode 为空
        boolean result2 = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(BASE_SPU_ID + 12, null);
        assertFalse(result2);
        
        // 测试 targetCountryCode 为空字符串
        boolean result3 = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(BASE_SPU_ID + 12, "");
        assertFalse(result3);
    }

    /**
     * 测试 addNeedApprovalSpuBatch 方法 - 正常情况
     */
    @Test
    public void testAddNeedApprovalSpuBatch_Success() {
        // 准备测试数据
        Long spuId1 = BASE_SPU_ID + 20;
        Long spuId2 = BASE_SPU_ID + 21;
        Long spuId3 = BASE_SPU_ID + 22;
        List<Long> spuIds = new ArrayList<>(Arrays.asList(spuId1, spuId2, spuId3));
        
        // 先添加一条记录，模拟部分已存在的情况
        boolean preAddResult = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(spuId1, TEST_COUNTRY_CODE);
        assertTrue(preAddResult);
        
        // 执行批量添加
        boolean result = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpuBatch(spuIds, TEST_COUNTRY_CODE);
        
        // 验证结果
        assertTrue(result);
        spuIds = new ArrayList<>(Arrays.asList(spuId1, spuId2, spuId3));
        // 验证数据库中的记录 - 应该有3条记录
        List<CountryMkuSpuTranslationApprovalPO> savedList = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdListAndCountryCode(spuIds, TEST_COUNTRY_CODE);
        assertNotNull(savedList);
        assertEquals(3, savedList.size());
        
        // 验证所有记录都是未审核状态
        for (CountryMkuSpuTranslationApprovalPO po : savedList) {
            assertFalse(po.getApprovalStatus());
            assertEquals(TEST_COUNTRY_CODE, po.getTargetCountryCode());
            assertEquals(YnEnum.YES.getCode(), po.getYn());
        }
    }

    /**
     * 测试 addNeedApprovalSpuBatch 方法 - 参数为空
     */
    @Test
    public void testAddNeedApprovalSpuBatch_WithEmptyParams() {
        // 测试 spuIds 为空
        boolean result1 = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpuBatch(null, TEST_COUNTRY_CODE);
        assertFalse(result1);
        
        // 测试 spuIds 为空列表
        boolean result2 = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpuBatch(Collections.emptyList(), TEST_COUNTRY_CODE);
        assertFalse(result2);
        
        // 测试 targetCountryCode 为空
        boolean result3 = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpuBatch(Arrays.asList(BASE_SPU_ID + 23), null);
        assertFalse(result3);
        
        // 测试 targetCountryCode 为空字符串
        boolean result4 = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpuBatch(Arrays.asList(BASE_SPU_ID + 23), "");
        assertFalse(result4);
    }

    /**
     * 测试 approvalSpu 方法 - 正常情况
     */
    @Test
    public void testApprovalSpu_Success() {
        // 准备测试数据
        Long spuId = BASE_SPU_ID + 30;
        
        // 先添加一条待审核记录
        boolean addResult = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(spuId, TEST_COUNTRY_CODE);
        assertTrue(addResult);
        
        // 确认记录存在且未审核
        CountryMkuSpuTranslationApprovalPO beforeApproval = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdAndCountryCode(spuId, TEST_COUNTRY_CODE);
        assertNotNull(beforeApproval);
        assertFalse(beforeApproval.getApprovalStatus());
        
        // 执行审核
        boolean result = countryMkuSpuTranslationApprovalAtomicService.approvalSpu(spuId, TEST_COUNTRY_CODE);
        
        // 验证结果
        assertTrue(result);
        
        // 验证数据库中的记录状态已更新
        CountryMkuSpuTranslationApprovalPO afterApproval = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdAndCountryCode(spuId, TEST_COUNTRY_CODE);
        assertNotNull(afterApproval);
        assertTrue(afterApproval.getApprovalStatus());
        assertEquals(spuId, afterApproval.getSpuId());
        assertEquals(TEST_COUNTRY_CODE, afterApproval.getTargetCountryCode());
    }

    /**
     * 测试 approvalSpu 方法 - 记录不存在
     */
    @Test
    public void testApprovalSpu_RecordNotExists() {
        // 准备测试数据 - 使用一个不存在的spuId
        Long nonExistentSpuId = BASE_SPU_ID + 31;
        
        // 执行测试
        boolean result = countryMkuSpuTranslationApprovalAtomicService.approvalSpu(nonExistentSpuId, TEST_COUNTRY_CODE);
        
        // 验证结果
        assertFalse(result);
    }

    /**
     * 测试 approvalSpu 方法 - 记录已审核
     */
    @Test
    public void testApprovalSpu_AlreadyApproved() {
        // 准备测试数据
        Long spuId = BASE_SPU_ID + 32;
        
        // 先添加一条记录
        boolean addResult = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(spuId, TEST_COUNTRY_CODE);
        assertTrue(addResult);
        
        // 先审核一次
        boolean firstApprovalResult = countryMkuSpuTranslationApprovalAtomicService.approvalSpu(spuId, TEST_COUNTRY_CODE);
        assertTrue(firstApprovalResult);
        
        // 确认记录已被审核
        CountryMkuSpuTranslationApprovalPO approvedPO = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdAndCountryCode(spuId, TEST_COUNTRY_CODE);
        assertNotNull(approvedPO);
        assertTrue(approvedPO.getApprovalStatus());
        
        // 再次尝试审核
        boolean secondApprovalResult = countryMkuSpuTranslationApprovalAtomicService.approvalSpu(spuId, TEST_COUNTRY_CODE);
        
        // 验证结果 - 第二次审核应该失败
        assertFalse(secondApprovalResult);
    }

    /**
     * 测试 approvalSpuBatch 方法 - 正常情况
     */
    @Test
    public void testApprovalSpuBatch_Success() {
        // 准备测试数据
        Long spuId1 = BASE_SPU_ID + 40;
        Long spuId2 = BASE_SPU_ID + 41;
        Long spuId3 = BASE_SPU_ID + 42;
        List<Long> spuIds = new ArrayList<>(Arrays.asList(spuId1, spuId2, spuId3));
        
        // 先添加三条记录
        boolean addResult1 = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(spuId1, TEST_COUNTRY_CODE);
        assertTrue(addResult1);
        
        boolean addResult2 = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(spuId2, TEST_COUNTRY_CODE);
        assertTrue(addResult2);
        
        boolean addResult3 = countryMkuSpuTranslationApprovalAtomicService.addNeedApprovalSpu(spuId3, TEST_COUNTRY_CODE);
        assertTrue(addResult3);
        
        // 先审核第二条记录（模拟部分已审核的情况）
        boolean preApprovalResult = countryMkuSpuTranslationApprovalAtomicService.approvalSpu(spuId2, TEST_COUNTRY_CODE);
        assertTrue(preApprovalResult);
        
        // 执行批量审核
        boolean result = countryMkuSpuTranslationApprovalAtomicService.approvalSpuBatch(spuIds, TEST_COUNTRY_CODE);
        
        // 验证结果
        assertTrue(result);
        spuIds = new ArrayList<>(Arrays.asList(spuId1, spuId2, spuId3));
        // 验证数据库中的记录状态
        List<CountryMkuSpuTranslationApprovalPO> approvedList = countryMkuSpuTranslationApprovalAtomicService.getBySpuIdListAndCountryCode(spuIds, TEST_COUNTRY_CODE);
        assertNotNull(approvedList);
        assertEquals(3, approvedList.size());
        
        // 验证所有记录都已审核通过
        for (CountryMkuSpuTranslationApprovalPO po : approvedList) {
            assertTrue(po.getApprovalStatus());
            assertEquals(TEST_COUNTRY_CODE, po.getTargetCountryCode());
            assertEquals(YnEnum.YES.getCode(), po.getYn());
        }
    }

    /**
     * 测试 approvalSpuBatch 方法 - 参数为空
     */
    @Test
    public void testApprovalSpuBatch_WithEmptyParams() {
        // 测试 spuIds 为空
        boolean result1 = countryMkuSpuTranslationApprovalAtomicService.approvalSpuBatch(null, TEST_COUNTRY_CODE);
        assertFalse(result1);
        
        // 测试 spuIds 为空列表
        boolean result2 = countryMkuSpuTranslationApprovalAtomicService.approvalSpuBatch(Collections.emptyList(), TEST_COUNTRY_CODE);
        assertFalse(result2);
        
        // 测试 targetCountryCode 为空
        boolean result3 = countryMkuSpuTranslationApprovalAtomicService.approvalSpuBatch(Arrays.asList(BASE_SPU_ID + 50), null);
        assertFalse(result3);
        
        // 测试 targetCountryCode 为空字符串
        boolean result4 = countryMkuSpuTranslationApprovalAtomicService.approvalSpuBatch(Arrays.asList(BASE_SPU_ID + 50), "");
        assertFalse(result4);
    }
}
