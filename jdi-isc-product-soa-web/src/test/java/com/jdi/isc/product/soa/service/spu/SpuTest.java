package com.jdi.isc.product.soa.service.spu;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.gms.crs.model.AttributeSettingResult;
import com.jd.gms.crs.model.PropAttribute;
import com.jd.gms.crs.model.PropGroup;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.jdm.sku.base.JdmSaveSpuDTO;
import com.jdi.isc.product.soa.api.jdm.sku.req.JdmUpdateSkuReqDTO;
import com.jdi.isc.product.soa.api.sku.req.SaveSkuApiDTO;
import com.jdi.isc.product.soa.api.spu.IscProductSoaSpuReadApiService;
import com.jdi.isc.product.soa.api.spu.IscProductSoaSpuWriteApiService;
import com.jdi.isc.product.soa.api.spu.req.PropertyValueApiDTO;
import com.jdi.isc.product.soa.api.spu.req.SaveSpuApiDTO;
import com.jdi.isc.product.soa.api.spu.req.SpuDetailReqApiDTO;
import com.jdi.isc.product.soa.api.spu.req.SpuQueryReqApiDTO;
import com.jdi.isc.product.soa.api.spu.res.SpuApiDTO;
import com.jdi.isc.product.soa.api.spu.res.SpuDetailApiDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.frame.SystemContextHolder;
import com.jdi.isc.product.soa.common.util.ApiInitUtils;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.domain.spu.po.SpuPO;
import com.jdi.isc.product.soa.repository.mapper.spu.SpuDraftBaseMapper;
import com.jdi.isc.product.soa.rpc.gms.MaterialRpcService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDraftAtomicService;
import com.jdi.isc.product.soa.service.manage.spu.SpuManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.impl.SpuDraftManageServiceImpl;
import com.jdi.isc.product.soa.service.mapstruct.sku.SkuConvert;
import com.jdi.isc.product.soa.service.mapstruct.spu.SpuConvert;
import com.jdi.isc.product.soa.web.ServiceApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@SpringBootTest(classes = ServiceApplication.class)
@AutoConfigureMockMvc
public class SpuTest {

    @Resource
    private SpuReadManageService spuReadManageService;

    @Resource
    private SpuDraftBaseMapper spuDraftBaseMapper;

    @Autowired
    private IscProductSoaSpuWriteApiService iscProductSoaSpuWriteApiService;

    @Autowired
    private IscProductSoaSpuReadApiService iscProductSoaSpuReadApiService;

    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;

    @Autowired
    private SpuDraftManageServiceImpl spuDraftManageServiceImpl;


    @Test
    public void recoverSkuKeyForSkuListBySaleAttributeTest(){
        SpuDetailVO spuDetailVO = JSON.parseObject(SpuVOGenerator.saleSpuVO, SpuDetailVO.class);
        spuDraftManageServiceImpl.recoverSkuKeyForSkuListBySaleAttribute(spuDetailVO.isCrossBorder(), spuDetailVO.getSkuVOList());
        System.out.println(JSON.toJSONString(spuDetailVO));
    }
    
    @Test
    public void test() {
        long spuId = 20000000022L;
        String code = "VN";
        List<Long> spuIds = new ArrayList<>();
        spuIds.add(spuId);
        List<String> targetCountryCodeList = new ArrayList<>();
        targetCountryCodeList.add(code);
        //spuReadManageService.getSpuAmendVOList( spuIds,targetCountryCodeList,2);
    }

    @Test
    public void draftPage() {
        SpuQueryReqVO reqVO = new SpuQueryReqVO();
        reqVO.setIndex(1L);
        reqVO.setSize(10L);
        reqVO.setBuyer("test");
        long total = spuDraftBaseMapper.getTotal(reqVO);
        System.out.println(total);
        List<SpuDraftPO> spuDraftPOS = spuDraftBaseMapper.listSearch(reqVO);
        System.out.println(JSON.toJSON(spuDraftPOS));
    }

    @Resource
    private SpuManageService spuManageService;

    @Test
    public void testSpuDraftPO() {
        SpuUpdateReqVO spuUpdateReqVO = new SpuUpdateReqVO();
        spuUpdateReqVO.setJdSkuId(1234567890L);
        BigDecimal bigDecimal = new BigDecimal(500);
        spuUpdateReqVO.setWeight(bigDecimal);
        spuUpdateReqVO.setLength(bigDecimal);
        spuUpdateReqVO.setHeight(bigDecimal);
        spuUpdateReqVO.setWidth(bigDecimal);
        DataResponse<Boolean> response = spuManageService.updateSpuSkuMku(spuUpdateReqVO);
    }

    @Test
    public void testSymbol() {
        SpuDetailVO spuDetailVO = JSON.parseObject("{\"notAudit\":false,\"pcDescriptionMap\":{},\"pin\":\"guohonghe\",\"skuVOList\":[{\"brandId\":8022,\"catId\":1185,\"createTime\":1732677005394,\"creator\":\"guohonghe\",\"customerTradeType\":\"BD\",\"height\":\"123\",\"jdVendorCode\":\"\",\"length\":\"12\",\"productionCycle\":4,\"purchasePrice\":\"1\",\"salePrice\":\"1.20\",\"skuCertificateVOList\":[],\"skuInterPropertyList\":[{\"propertyVOS\":[{\"attributeId\":69,\"propertyValueVOList\":[{\"attributeId\":69,\"attributeValueName\":\"2\",\"lang\":\"zh\"}]},{\"attributeId\":70,\"propertyValueVOList\":[{\"attributeId\":70,\"attributeValueId\":100077,\"selected\":true}]},{\"attributeId\":71,\"propertyValueVOList\":[{\"attributeId\":71,\"attributeValueName\":\"12345\",\"lang\":\"zh\"}]},{\"attributeId\":72,\"propertyValueVOList\":[{\"attributeId\":72,\"attributeValueName\":\"2\",\"lang\":\"zh\"}]},{\"attributeId\":73,\"propertyValueVOList\":[{\"attributeId\":73,\"attributeValueName\":\"2\",\"lang\":\"zh\"}]},{\"attributeId\":74,\"propertyValueVOList\":[{\"attributeId\":74,\"attributeValueName\":\"0.02\",\"lang\":\"zh\"}]},{\"attributeId\":106,\"propertyValueVOList\":[{\"attributeId\":106,\"attributeValueName\":\"0.02\",\"lang\":\"zh\"}]},{\"attributeId\":75,\"propertyValueVOList\":[{\"attributeId\":75,\"attributeValueId\":100083,\"selected\":true}]},{\"attributeId\":76,\"propertyValueVOList\":[{\"attributeId\":76,\"attributeValueName\":\"2\",\"lang\":\"zh\"}]},{\"attributeId\":77,\"propertyValueVOList\":[{\"attributeId\":77,\"attributeValueName\":\"2\",\"lang\":\"zh\"}]},{\"attributeId\":78,\"propertyValueVOList\":[{\"attributeId\":78,\"attributeValueName\":\"0.02\",\"lang\":\"zh\"}]},{\"attributeId\":79,\"propertyValueVOList\":[{\"attributeId\":79,\"attributeValueName\":\"2\",\"lang\":\"zh\"}]},{\"attributeId\":80,\"propertyValueVOList\":[{\"attributeId\":80,\"attributeValueName\":\"0.02\",\"lang\":\"zh\"}]}]}],\"skuOnlineTime\":1732677005369,\"skuStatus\":1,\"sourceCountryCode\":\"BR\",\"stockNum\":\"1\",\"storeSalePropertyList\":[{\"attributeId\":100100,\"attributeValueId\":103901}],\"taxPurchasePrice\":\"1\",\"taxSalePrice\":\"1.20\",\"updateTime\":1732677005394,\"updater\":\"guohonghe\",\"vendorCode\":\"YFBRPRO2024082102\",\"vendorTradeType\":\"BD\",\"weight\":\"123\",\"width\":\"23\",\"yn\":1}],\"spuVO\":{\"attributeScope\":\"BR\",\"auditStatus\":3,\"brandId\":8022,\"buyer\":\"guohonghe\",\"catId\":1185,\"createTime\":1732677005394,\"creator\":\"guohonghe\",\"detailImgList\":[],\"identityFlag\":2,\"mainImg\":\"https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/ps1/41732677005161.jpeg\",\"saleUnit\":1,\"sourceCountryCode\":\"BR\",\"spuInterAttribute\":\"\",\"spuLangExtendVOList\":[{\"lang\":\"zh\",\"qualifier\":\"\",\"spuTitle\":\"A02 工业防护口罩（测试）\"},{\"lang\":\"pt_BR\",\"qualifier\":\"\",\"spuTitle\":\"A02 Máscara protetora industrial (teste)\"},{\"lang\":\"en\",\"qualifier\":\"\",\"spuTitle\":\"A02 Industrial protective mask (test)\"}],\"spuStatus\":1,\"systemCode\":\"WIMP\",\"updateTime\":1732677005394,\"updater\":\"guohonghe\",\"vendorCode\":\"YFBRPRO2024082102\",\"yn\":1}}", SpuDetailVO.class);
        SpuDetailApiDTO spuDetailApiDTO = SpuConvert.INSTANCE.detailVo2DTO(spuDetailVO);
        DataResponse<Long> submit = iscProductSoaSpuWriteApiService.submit(spuDetailApiDTO);
        System.out.println(JSON.toJSONString(submit));
    }

    @Test
    public void testDraftPage() {
        SpuQueryReqApiDTO spuQueryReqApiDTO = JSON.parseObject("{\"index\":1,\"lang\":\"zh\",\"offset\":0,\"size\":20,\"sourceCountryCode\":\"BR\",\"taxAuditStatus\":3}", SpuQueryReqApiDTO.class);
        DataResponse<PageInfo<SpuApiDTO>> submit = iscProductSoaSpuReadApiService.spuDraftPage(spuQueryReqApiDTO);
        System.out.println(JSON.toJSONString(submit));
    }


    @Test
    public void testDraftSave() {
        SaveSpuApiDTO spuQueryReqApiDTO = JSON.parseObject("{\"lang\":\"zh\",\"notAudit\":false,\"pcDescriptionMap\":{\"th\":\"<p></p>\",\"en\":\"<p></p>\",\"zh\":\"<p></p>\"},\"pin\":\"aa\",\"skuVOList\":[{\"currency\":\"THB\",\"customerTradeType\":\"BD\",\"height\":\"11\",\"length\":\"111\",\"noTaxGrossRate\":-7,\"productionCycle\":1,\"purchasePrice\":\"11\",\"salePrice\":\"11\",\"skuInterPropertyList\":[{\"propertyVOS\":[{\"attributeId\":102,\"attributeInputType\":1,\"attributeName\":\"泰国强制商品认证\",\"propertyValueVOList\":[],\"required\":false,\"showSupplier\":0,\"sort\":300}],\"requirement\":1}],\"skuStockRelationList\":[{\"stock\":\"11\",\"warehouseId\":\"-1\"},{\"onWaySale\":1,\"stock\":\"0\",\"warehouseId\":\"1001\"}],\"stockNum\":\"11\",\"storeSalePropertyList\":[{\"attributeId\":100101,\"attributeValueId\":102800,\"attributeValueName\":\"其他-测试\",\"selected\":true,\"sort\":1}],\"taxGrossRate\":0,\"taxPurchasePrice\":\"11.77\",\"taxSalePrice\":\"11.77\",\"vendorCode\":\"TestTh25022301\",\"vendorTradeType\":\"BD\",\"weight\":\"111\",\"width\":\"111\"}],\"spuVO\":{\"attributeScope\":\"TH\",\"brandId\":8000,\"catId\":52,\"detailImgList\":[],\"isExport\":0,\"mainImg\":\"https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/ks1/5437929047222758.jpg\",\"saleUnit\":44,\"sourceCountryCode\":\"TH\",\"spuId\":20000001073,\"spuLangExtendVOList\":[{\"lang\":\"zh\",\"spuTitle\":\"231231\"},{\"lang\":\"th\"},{\"lang\":\"en\"}],\"systemCode\":\"WIMP\",\"vendorCode\":\"TestTh25022301\",\"vendorName\":\"TestTh25022301商家\"},\"storeExtendPropertyList\":[]}", SaveSpuApiDTO.class);
         iscProductSoaSpuWriteApiService.submit(spuQueryReqApiDTO);
    }

    @Test
    public void spuPageTest() {
        SpuQueryReqVO query = new SpuQueryReqVO();

        query.setSkuIds(Lists.newArrayList());
        query.setSpuIds(Lists.newArrayList(20000001102L));
        query.setMkuIds(Lists.newArrayList());
        query.setJdSkuIds(Lists.newArrayList(100004205573L, 12312312332L));

        query.setIndex(1L);
        query.setSize(100L);

        long total = spuAtomicService.getTotal(query);

        // 分页查询spu
        List<SpuPO> page = spuAtomicService.listSearch(query);
        System.out.println("page1::::" + total + "   " + JSON.toJSONString(page));


        total = spuDraftAtomicService.getTotal(query);

        // 分页查询草稿
        List<SpuDraftPO> page2 = spuDraftAtomicService.listSearch(query);
        System.out.println("page2::::" + total + "   " + JSON.toJSONString(page2));
    }

    @Test
    public void testNewSpu() {
        SpuDetailReqApiDTO spuDetailReqApiDTO = new SpuDetailReqApiDTO();
        spuDetailReqApiDTO.setSpuId(20000001252L);
        spuDetailReqApiDTO.setLang("zh");
        SpuDetailApiDTO spuDetailApiDTO = iscProductSoaSpuReadApiService.getDetailInfo(spuDetailReqApiDTO).getData();
        System.out.println("------ext:"+JSON.toJSONString(spuDetailApiDTO.getExtendPropertyList()));
        System.out.println("---"+JSON.toJSONString(spuDetailApiDTO.getSkuVOList().get(0).getExtendPropertyList()));
//        iscProductSoaSpuWriteApiService.submit(spuDetailApiDTO);
    }


//    @Resource
//    private IscAttributeReadApiService iscAttributeReadApiService;
    @Resource
    private MaterialRpcService materialRpcService;
//100104950158
    @Test
    public void testSave() {
//        iscAttributeReadApiService.obtainJDSkuExtAttributeList();
        AttributeSettingResult attributeSettingResult = materialRpcService.getCateAttributeBySkuId(100104950158L);
        for (PropGroup expandGroup : attributeSettingResult.getExpandGroups()) {
            for (PropAttribute att : expandGroup.getAtts()) {
                att.getAttId();
            }
        }

        System.out.println(JSON.toJSONString(attributeSettingResult));
    }

    @Test
    public void getSpuDetail() {
        SpuDetailReqVO reqVO = new SpuDetailReqVO();
        reqVO.setSpuId(20000001259L);
        reqVO.setLang("zh");
        SpuDetailVO spuDetail = spuReadManageService.getSpuDetail(reqVO);
        System.out.println();
        System.out.println(JSON.toJSONString(spuDetail));
    }

    @Test
    public void testSaveSpu() {
        SpuPO spuPO = new SpuPO();
        String spuJson = "{\n" +
                "        \"attributeScope\": \"CN,HK,ID,BR,MY,HU,TH,VN\",\n" +
                "        \"auditStatus\": 3,\n" +
                "        \"brandId\": 8000,\n" +
                "        \"buyer\": \"zhaoyan316\",\n" +
                "        \"catId\": 17563,\n" +
                "        \"createTime\": 1749470303997,\n" +
                "        \"creator\": \"zhaoyan316\",\n" +
                "        \"detailImg\": \"\",\n" +
                "        \"detailImgList\": [],\n" +
                "        \"identityFlag\": 2,\n" +
                "        \"mainImg\": \"https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/ps1/TASK19714063871687580.jpeg\",\n" +
                "        \"saleUnit\": 14,\n" +
                "        \"sourceCountryCode\": \"CN\",\n" +
                "        \"specification\": \"CEM10N3X8D-G\",\n" +
                "        \"spuId\": 20000001264,\n" +
                "        \"spuInterAttribute\": \"\",\n" +
                "        \"spuLangExtendVOList\": [\n" +
                "            {\n" +
                "                \"lang\": \"zh_Hant\",\n" +
                "                \"qualifier\": \"一把\",\n" +
                "                \"spuTitle\": \"數顯扭力扳手2-10N（含第三方偵測報告） CEM10N3X8D-G 一把\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"zh\",\n" +
                "                \"qualifier\": \"一把\",\n" +
                "                \"spuTitle\": \"数显扭力扳手2-10N（含第三方检测报告） CEM10N3X8D-G 一把\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"vi\",\n" +
                "                \"qualifier\": \"Một nắm tay\",\n" +
                "                \"spuTitle\": \"Cờ lê lực kỹ thuật số 2-10N (bao gồm báo cáo thử nghiệm của bên thứ ba) CEM10N3X8D-G Một nắm tay\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"th\",\n" +
                "                \"qualifier\": \"กำมือหนึ่ง\",\n" +
                "                \"spuTitle\": \"ประแจแรงบิดดิจิตอล 2-10N (รวมรายงานการทดสอบของบุคคลที่สาม) CEM10N3X8D-G กำมือหนึ่ง\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"pt_BR\",\n" +
                "                \"qualifier\": \"Um punhado\",\n" +
                "                \"spuTitle\": \"Chave de torque digital 2-10N (incluindo relatório de teste de terceiros) CEM10N3X8D-G Um punhado\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"ms\",\n" +
                "                \"qualifier\": \"segelintir\",\n" +
                "                \"spuTitle\": \"Sepana tork digital 2-10N (termasuk laporan ujian pihak ketiga) CEM10N3X8D-G segelintir\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"id\",\n" +
                "                \"qualifier\": \"Segenggam\",\n" +
                "                \"spuTitle\": \"Kunci torsi digital 2-10N (termasuk laporan pengujian pihak ketiga) CEM10N3X8D-G Segenggam\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"hu\",\n" +
                "                \"qualifier\": \"Egy marék\",\n" +
                "                \"spuTitle\": \"Digitális nyomatékkulcs 2-10N (harmadik féltől származó tesztjelentéssel) CEM10N3X8D-G Egy marék\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"en\",\n" +
                "                \"qualifier\": \"A handful\",\n" +
                "                \"spuTitle\": \"Digital torque wrench 2-10N (including third-party test report) CEM10N3X8D-G A handful\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"spuStatus\": 1,\n" +
                "        \"systemCode\": \"WIMP\",\n" +
                "        \"updateTime\": 1749470303997,\n" +
                "        \"updater\": \"zhaoyan316\",\n" +
                "        \"vendorCode\": \"cnsjgy\",\n" +
                "        \"vendorSpuId\": \"\",\n" +
                "        \"yn\": 1\n" +
                "    }";
        spuPO = JSON.parseObject(spuJson, SpuPO.class);
        spuPO.setVendorSpuId(null);
        spuAtomicService.save(spuPO);
        System.out.println();
        System.out.println(spuAtomicService.getSpuPoBySpuId(20000001264L));
    }

    public static void main(String[] args) {
        SpuPO spuPO = new SpuPO();
        String spuJson = "{\n" +
                "        \"attributeScope\": \"CN,HK,ID,BR,MY,HU,TH,VN\",\n" +
                "        \"auditStatus\": 3,\n" +
                "        \"brandId\": 8000,\n" +
                "        \"buyer\": \"zhaoyan316\",\n" +
                "        \"catId\": 17563,\n" +
                "        \"createTime\": 1749470303997,\n" +
                "        \"creator\": \"zhaoyan316\",\n" +
                "        \"detailImg\": \"\",\n" +
                "        \"detailImgList\": [],\n" +
                "        \"identityFlag\": 2,\n" +
                "        \"mainImg\": \"https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/ps1/TASK19714063871687580.jpeg\",\n" +
                "        \"saleUnit\": 14,\n" +
                "        \"sourceCountryCode\": \"CN\",\n" +
                "        \"specification\": \"CEM10N3X8D-G\",\n" +
                "        \"spuId\": 20000001264,\n" +
                "        \"spuInterAttribute\": \"\",\n" +
                "        \"spuLangExtendVOList\": [\n" +
                "            {\n" +
                "                \"lang\": \"zh_Hant\",\n" +
                "                \"qualifier\": \"一把\",\n" +
                "                \"spuTitle\": \"數顯扭力扳手2-10N（含第三方偵測報告） CEM10N3X8D-G 一把\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"zh\",\n" +
                "                \"qualifier\": \"一把\",\n" +
                "                \"spuTitle\": \"数显扭力扳手2-10N（含第三方检测报告） CEM10N3X8D-G 一把\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"vi\",\n" +
                "                \"qualifier\": \"Một nắm tay\",\n" +
                "                \"spuTitle\": \"Cờ lê lực kỹ thuật số 2-10N (bao gồm báo cáo thử nghiệm của bên thứ ba) CEM10N3X8D-G Một nắm tay\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"th\",\n" +
                "                \"qualifier\": \"กำมือหนึ่ง\",\n" +
                "                \"spuTitle\": \"ประแจแรงบิดดิจิตอล 2-10N (รวมรายงานการทดสอบของบุคคลที่สาม) CEM10N3X8D-G กำมือหนึ่ง\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"pt_BR\",\n" +
                "                \"qualifier\": \"Um punhado\",\n" +
                "                \"spuTitle\": \"Chave de torque digital 2-10N (incluindo relatório de teste de terceiros) CEM10N3X8D-G Um punhado\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"ms\",\n" +
                "                \"qualifier\": \"segelintir\",\n" +
                "                \"spuTitle\": \"Sepana tork digital 2-10N (termasuk laporan ujian pihak ketiga) CEM10N3X8D-G segelintir\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"id\",\n" +
                "                \"qualifier\": \"Segenggam\",\n" +
                "                \"spuTitle\": \"Kunci torsi digital 2-10N (termasuk laporan pengujian pihak ketiga) CEM10N3X8D-G Segenggam\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"hu\",\n" +
                "                \"qualifier\": \"Egy marék\",\n" +
                "                \"spuTitle\": \"Digitális nyomatékkulcs 2-10N (harmadik féltől származó tesztjelentéssel) CEM10N3X8D-G Egy marék\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"lang\": \"en\",\n" +
                "                \"qualifier\": \"A handful\",\n" +
                "                \"spuTitle\": \"Digital torque wrench 2-10N (including third-party test report) CEM10N3X8D-G A handful\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"spuStatus\": 1,\n" +
                "        \"systemCode\": \"WIMP\",\n" +
                "        \"updateTime\": 1749470303997,\n" +
                "        \"updater\": \"zhaoyan316\",\n" +
                "        \"vendorCode\": \"cnsjgy\",\n" +
                "        \"vendorSpuId\": \"\",\n" +
                "        \"yn\": 1\n" +
                "    }";
        spuPO = JSON.parseObject(spuJson, SpuPO.class);
        // System.out.println(JSON.toJSONString(spuPO));


        String jdmUpdateSkuJson = "{\n" +
                "  \"traceId\": \"TRACE_" + System.currentTimeMillis() + "\",\n" +
                "  \"firstCatId\": \"1316\",\n" +
                "  \"secondCatId\": \"1381\",\n" +
                "  \"thirdCatId\": \"1385\",\n" +
                "  \"lastCatId\": \"1185\",\n" +
                "  \"firstCatName\": \"家用电器\",\n" +
                "  \"secondCatName\": \"生活电器\",\n" +
                "  \"thirdCatName\": \"健康电器\",\n" +
                "  \"lastCatName\": \"按摩器\",\n" +
                "  \"amendReason\": \"测试更新商品信息\",\n" +
                "  \"jdmSpuDTO\": {\n" +
                "    \"spuId\": 20000001000,\n" +
                "    \"brandId\": 8000,\n" +
                "    \"brandName\": \"测试品牌\",\n" +
                "    \"vendorCode\": \"TEST_VENDOR_001\",\n" +
                "    \"vendorName\": \"测试供应商\",\n" +
                "    \"sourceCountryCode\": \"CN\",\n" +
                "    \"attributeScope\": \"CN,TH,VN,MY,ID,BR\",\n" +
                "    \"saleUnit\": 1,\n" +
                "    \"mainImg\": \"https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/test/main.jpg\",\n" +
                "    \"spuStatus\": 1,\n" +
                "    \"auditStatus\": 3\n" +
                "  },\n" +
                "  \"skuVOList\": [\n" +
                "    {\n" +
                "      \"skuId\": 100000001000,\n" +
                "      \"jdSkuId\": ************,\n" +
                "      \"vendorCode\": \"TEST_VENDOR_001\",\n" +
                "      \"customerTradeType\": \"BD\",\n" +
                "      \"sourceCountryCode\": \"CN\",\n" +
                "      \"currency\": \"CNY\",\n" +
                "      \"purchasePrice\": \"100.00\",\n" +
                "      \"salePrice\": \"120.00\",\n" +
                "      \"stockNum\": \"1000\",\n" +
                "      \"weight\": \"500\",\n" +
                "      \"length\": \"10\",\n" +
                "      \"width\": \"8\",\n" +
                "      \"height\": \"5\",\n" +
                "      \"skuStatus\": 1,\n" +
                "      \"storeExtendPropertyList\": [\n" +
                "        {\n" +
                "          \"attributeId\": 401,\n" +
                "          \"attributeName\": \"SKU材质\",\n" +
                "          \"attributeValue\": \"高强度塑料\",\n" +
                "          \"propertyValueVOList\": [\n" +
                "            {\n" +
                "              \"attributeId\": 401,\n" +
                "              \"attributeValueName\": \"高强度塑料\",\n" +
                "              \"lang\": \"zh\"\n" +
                "            },\n" +
                "            {\n" +
                "              \"attributeId\": 401,\n" +
                "              \"attributeValueName\": \"High-strength plastic\",\n" +
                "              \"lang\": \"en\"\n" +
                "            }\n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"attributeId\": 402,\n" +
                "          \"attributeName\": \"SKU颜色\",\n" +
                "          \"attributeValue\": \"珍珠白\",\n" +
                "          \"propertyValueVOList\": [\n" +
                "            {\n" +
                "              \"attributeId\": 402,\n" +
                "              \"attributeValueName\": \"珍珠白\",\n" +
                "              \"lang\": \"zh\"\n" +
                "            },\n" +
                "            {\n" +
                "              \"attributeId\": 402,\n" +
                "              \"attributeValueName\": \"Pearl White\",\n" +
                "              \"lang\": \"en\"\n" +
                "            }\n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"attributeId\": 403,\n" +
                "          \"attributeName\": \"SKU规格\",\n" +
                "          \"attributeValue\": \"标准版\",\n" +
                "          \"propertyValueVOList\": [\n" +
                "            {\n" +
                "              \"attributeId\": 403,\n" +
                "              \"attributeValueName\": \"标准版\",\n" +
                "              \"lang\": \"zh\"\n" +
                "            },\n" +
                "            {\n" +
                "              \"attributeId\": 403,\n" +
                "              \"attributeValueName\": \"Standard Edition\",\n" +
                "              \"lang\": \"en\"\n" +
                "            }\n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"attributeId\": 404,\n" +
                "          \"attributeName\": \"包装方式\",\n" +
                "          \"attributeValue\": \"独立包装\",\n" +
                "          \"propertyValueVOList\": [\n" +
                "            {\n" +
                "              \"attributeId\": 404,\n" +
                "              \"attributeValueName\": \"独立包装\",\n" +
                "              \"lang\": \"zh\"\n" +
                "            },\n" +
                "            {\n" +
                "              \"attributeId\": 404,\n" +
                "              \"attributeValueName\": \"Individual packaging\",\n" +
                "              \"lang\": \"en\"\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ],\n" +
                "  \"extendPropertyList\": [\n" +
                "    {\n" +
                "      \"attributeId\": 101,\n" +
                "      \"attributeName\": \"材质\",\n" +
                "      \"attributeValue\": \"塑料\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"attributeId\": 102,\n" +
                "      \"attributeName\": \"颜色\",\n" +
                "      \"attributeValue\": \"白色\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"attributeId\": 103,\n" +
                "      \"attributeName\": \"尺寸\",\n" +
                "      \"attributeValue\": \"10*8*5cm\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"storeExtendPropertyList\": [\n" +
                "    {\n" +
                "      \"attributeId\": 201,\n" +
                "      \"attributeName\": \"保修期\",\n" +
                "      \"attributeValue\": \"1年\",\n" +
                "      \"propertyValueVOList\": [\n" +
                "        {\n" +
                "          \"attributeId\": 201,\n" +
                "          \"attributeValueName\": \"1年\",\n" +
                "          \"lang\": \"zh\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"attributeId\": 201,\n" +
                "          \"attributeValueName\": \"1 Year\",\n" +
                "          \"lang\": \"en\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"attributeId\": 202,\n" +
                "      \"attributeName\": \"产地\",\n" +
                "      \"attributeValue\": \"中国\",\n" +
                "      \"propertyValueVOList\": [\n" +
                "        {\n" +
                "          \"attributeId\": 202,\n" +
                "          \"attributeValueName\": \"中国\",\n" +
                "          \"lang\": \"zh\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"attributeId\": 202,\n" +
                "          \"attributeValueName\": \"China\",\n" +
                "          \"lang\": \"en\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ],\n" +
                "  \"pcDescriptionMap\": {\n" +
                "    \"zh\": \"<p>这是中文商品详细描述，包含商品的主要特性和使用说明。</p>\",\n" +
                "    \"en\": \"<p>This is English product description with main features and usage instructions.</p>\",\n" +
                "    \"th\": \"<p>คำอธิบายผลิตภัณฑ์ภาษาไทยพร้อมคุณสมบัติหลักและคำแนะนำการใช้งาน</p>\"\n" +
                "  },\n" +
                "  \"spuCertificateVOList\": [\n" +
                "    {\n" +
                "      \"certificateId\": 1001,\n" +
                "      \"certificateName\": \"3C认证\",\n" +
                "      \"certificateNumber\": \"2023010101001234\",\n" +
                "      \"certificateUrl\": \"https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/cert/3c_cert.pdf\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"spuInterPropertyList\": [\n" +
                "    {\n" +
                "      \"attributeId\": 301,\n" +
                "      \"attributeName\": \"国际认证\",\n" +
                "      \"requirement\": 1,\n" +
                "      \"propertyVOS\": [\n" +
                "        {\n" +
                "          \"attributeId\": 301,\n" +
                "          \"attributeValue\": \"CE认证\",\n" +
                "          \"lang\": \"zh\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        // 使用JSON反序列化
        JdmUpdateSkuReqDTO jdmUpdateSkuReqDTO = JSON.parseObject(jdmUpdateSkuJson, JdmUpdateSkuReqDTO.class);
        SaveSpuVO saveSpuVO = SkuConvert.INSTANCE.jdmUpdate2SaveSpuVo(jdmUpdateSkuReqDTO);
        System.out.println(JSON.toJSONString(saveSpuVO));

        SpuDetailVO spuDetailVO = JSON.parseObject(jdmUpdateSkuJson, SpuDetailVO.class);
        JdmSaveSpuDTO jdmSaveSpuDTO = SkuConvert.INSTANCE.saveDetailVo2Jdm(spuDetailVO);
        System.out.println(JSON.toJSONString(jdmSaveSpuDTO));
    }

    @Test
    @Transactional
    public void testSaleSpu() {
        SpuDetailVO spuDetailVO = JSON.parseObject(SpuVOGenerator.createSpuVO, SpuDetailVO.class);
        SpuDetailApiDTO spuDetailApiDTO = SpuConvert.INSTANCE.detailVo2DTO(spuDetailVO);
        spuDetailApiDTO.setPin("zhaoyan316");
        spuDetailApiDTO.setLang("zh");
        spuDetailApiDTO.setSystemCode("WIMP");
        DataResponse<Long> submit = iscProductSoaSpuWriteApiService.submit(spuDetailApiDTO);
        System.out.println(JSON.toJSONString(submit));


        SpuDetailReqApiDTO spuDetailReqApiDTO = new SpuDetailReqApiDTO();
        spuDetailReqApiDTO.setSpuId(submit.getData());
        spuDetailReqApiDTO.setLang("zh");
        spuDetailApiDTO = iscProductSoaSpuReadApiService.getDetailInfo(spuDetailReqApiDTO).getData();
        System.out.println("---" + JSON.toJSONString(spuDetailApiDTO));
        for (SaveSkuApiDTO saveSkuApiDTO : spuDetailApiDTO.getSkuVOList()) {
            for (PropertyValueApiDTO propertyValueApiDTO : saveSkuApiDTO.getStoreSalePropertyList()) {
                System.out.println(saveSkuApiDTO.getSkuKey()+":"+propertyValueApiDTO.getAttributeValueName());
            }
        }

        spuDetailApiDTO.setPin("zhaoyan316");
        spuDetailApiDTO.setLang("zh");
        spuDetailApiDTO.setSystemCode("WIMP");
        submit = iscProductSoaSpuWriteApiService.submit(spuDetailApiDTO);
        System.out.println(JSON.toJSONString(submit));

        spuDetailReqApiDTO = new SpuDetailReqApiDTO();
        spuDetailReqApiDTO.setSpuId(submit.getData());
        spuDetailReqApiDTO.setLang("zh");
        spuDetailApiDTO = iscProductSoaSpuReadApiService.getDetailInfo(spuDetailReqApiDTO).getData();
        System.out.println("---" + JSON.toJSONString(spuDetailApiDTO));
        for (SaveSkuApiDTO saveSkuApiDTO : spuDetailApiDTO.getSkuVOList()) {
            for (PropertyValueApiDTO propertyValueApiDTO : saveSkuApiDTO.getStoreSalePropertyList()) {
                System.out.println(saveSkuApiDTO.getSkuKey()+":"+propertyValueApiDTO.getAttributeValueName());
            }
        }

        if(spuDetailVO!=null){
            throw new RuntimeException();
        }
    }
}
