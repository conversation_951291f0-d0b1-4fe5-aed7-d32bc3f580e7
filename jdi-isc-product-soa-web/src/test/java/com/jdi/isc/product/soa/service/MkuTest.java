package com.jdi.isc.product.soa.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.common.enums.MkuFeatureTagEnum;
import com.jdi.isc.product.soa.api.common.enums.MkuSpecialAttrEnum;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuPoolFlagDTO;
import com.jdi.isc.product.soa.common.exception.CustomerMkuBindException;
import com.jdi.isc.product.soa.domain.mku.biz.*;
import com.jdi.isc.product.soa.domain.mku.po.es.MkuEsPO;
import com.jdi.isc.product.soa.service.atomic.mku.MkuEsAtomicService;
import com.jdi.isc.product.soa.service.manage.mku.MkuEsManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuManageService;
import com.jdi.isc.product.soa.web.ServiceApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@SpringBootTest(classes = ServiceApplication.class)
@AutoConfigureMockMvc
public class MkuTest {

    @Resource
    private MkuManageService mkuManageService;
    @Resource
    private MkuEsAtomicService mkuEsAtomicService;
    @Resource
    private MkuEsManageService mkuEsManageService;

    @LafValue("jdi.isc.test.customer")
    private Set<String> testClientSet;

    @LafValue("jdi.isc.test.mkuIds")
    private Set<Long> testMkuIds;

    @Test
    public void upsertToEs() throws Exception {

        MkuClientDetailReqVO input =  new MkuClientDetailReqVO();

        Long mkuId = 50000000010L;
        input.setMkuId(mkuId);
        input.setClientCode("OSDH134KJ35");

        Boolean res = mkuEsManageService.upsert2Es(input);
        System.out.println(" >>>>>>>>>>>>>> " + res);
    }

    @Test
    public void page() throws Exception {
        MkuClientPageReqVO input = new MkuClientPageReqVO();
        input.setClientCode("CLIENT123");
        input.setLang("en");
//        List<Long> catIds = new ArrayList<>();
//        catIds.add(49L);
//        input.setCatIds(catIds);
       // input.setKeyword("更新");
        input.setIndex(1L);
        input.setSize(5L);
        input.setFeatureTags(Collections.singleton(MkuFeatureTagEnum.SHIPPING_48_HOUR.getCode()));
//        PageInfo<MkuClientVO> result = mkuEsManageService.page(input);
//        System.out.println(" >>>>>>>>>>>>>> " + JSON.toJSONString(result));
    }

    @Test
    public void attempt() throws Exception {

        MkuClientPageReqVO input =  new MkuClientPageReqVO();

        input.setKeyword("测试品 防爆车");
        input.setIndex(1L);
        input.setSize(10L);
        input.setLang("zh");
        input.setClientCode("OSDH134KJ35");

        List<String> res = mkuEsManageService.attempt(input);
        System.out.println(" >>>>>>>>>>>>>> " + res);
    }

    @Test
    public void delete() throws Exception {

        MkuClientDetailReqVO input =  new MkuClientDetailReqVO();

        Long mkuId = 50000000010L;
        input.setMkuId(mkuId);
        input.setClientCode("OSDH134KJ35");

        Boolean res = mkuEsAtomicService.deleteDocument("jdi_isc_mku_vn","OSDH134KJ35_50000000010");
        System.out.println(" >>>>>>>>>>>>>> " + res);
    }

    @Test
    public void testDuccConfig() {
                        // 是否为测试客户 如果是再校验商品是否为测试商品
        try {
            boolean testClient = testClientSet.contains("hRvDgUX263Y2FuWbVzB8");

            // 入池校验非测试品和测试客户
            if (testClient && !testMkuIds.contains(50000027518L)) {
                throw new CustomerMkuBindException(String.format("测试客户编码 %s 不能绑定非测试品。", "hRvDgUX263Y2FuWbVzB8"));
            }
        } catch (Exception e) {
            log.error("testDuccConfig error ",e);
        }

    }

    @Test
    public void testQueryMkuSpecialAttr() {
        SpecialAttrMkuReqVO input = new SpecialAttrMkuReqVO();
        input.setMkuIds(Sets.newHashSet(50000000018L));
        input.setSpecialAttrEnums(Arrays.stream(MkuSpecialAttrEnum.values()).collect(Collectors.toSet()));
        DataResponse<Map<Long, Map<String, String>>> mapDataResponse = mkuManageService.querySpecialAttrMapByMkuIds(input);
        System.out.println(JSON.toJSONString(mapDataResponse));
    }


    @Test
    public void upsertTag() throws Exception {
        MkuFeatureTagVO tagVO = new MkuFeatureTagVO();
        tagVO.setMkuId(67891L);
        tagVO.setTagType("stockUp");
        tagVO.setCountryCode("TH");
        tagVO.setOperator("remove");
        Boolean res = mkuEsManageService.updateMkuCustomerFeatureTag(tagVO);

        System.out.println(" >>>>>>>>>>>>>> " + res);
    }

    @Test
    public void upsertPoolFlag() throws Exception {
        MkuPoolFlagDTO input = new MkuPoolFlagDTO();
        input.setMkuId(50000000093L);
//        input.setPoolStatus(2);
        input.setTargetCountryCode("TH");
        Boolean res = mkuEsManageService.updateMkuPoolFlag(input);

        System.out.println(" >>>>>>>>>>>>>> " + res);
    }

    @Test
    public void testBuildMkuEsStockFlag(){
        MkuEsPO mkuEsPO = new MkuEsPO();
        mkuEsPO.setMkuId(50000000114L);
        mkuEsPO.setClientCode("uDFnyS3iUOBc3D1EP1XP");

        String countryCode = "MR";
        //Integer stockFlag = mkuManageService.buildMkuEsStockFlag(mkuEsPO, countryCode);
        //System.out.println("result is:"+stockFlag);
    }

    @Test
    public void testGetPromiseSevenDayDeliveryTag(){
        MkuEsPO mkuEsPO = new MkuEsPO();
        mkuEsPO.setMkuId(50000000114L);
        mkuEsPO.setClientCode("uDFnyS3iUOBc3D1EP1XP");

        String countryCode = "MR";
//        Integer stockFlag = mkuManageService.getPromiseSevenDayDeliveryTag(mkuEsPO, countryCode);
//        System.out.println("result is:"+stockFlag);
    }
}
