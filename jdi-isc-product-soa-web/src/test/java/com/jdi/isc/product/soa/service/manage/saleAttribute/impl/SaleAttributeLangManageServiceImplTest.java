package com.jdi.isc.product.soa.service.manage.saleAttribute.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.translate.req.BatchMultiTranslateReqDTO;
import com.jdi.isc.product.soa.api.translate.req.MultiTranslateReqDTO;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeValueLangVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeValueVO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeLangPO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeValueLangPO;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SaleAttributeLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SaleAttributeValueLangAtomicService;
import com.jdi.isc.product.soa.service.manage.lang.LangManageService;
import com.jdi.isc.product.soa.service.manage.translate.TextTranslateManageService;
import com.jdi.isc.product.soa.service.mapstruct.saleAttribute.SaleAttributeConvert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SaleAttributeLangManageService单元测试
 * <AUTHOR>
 * @date 2024-12-19
 */
@ExtendWith(MockitoExtension.class)
class SaleAttributeLangManageServiceImplTest {

    @InjectMocks
    private SaleAttributeLangManageServiceImpl saleAttributeLangManageService;

    @Mock
    private SaleAttributeLangAtomicService saleAttributeLangAtomicService;

    @Mock
    private SaleAttributeValueLangAtomicService saleAttributeValueLangAtomicService;

    @Mock
    private TextTranslateManageService textTranslateManageService;

    @Mock
    private LangManageService langManageService;

    // 测试数据
    private static final String TEST_LANG_EN = LangConstant.LANG_EN;
    private static final String TEST_LANG_ZH = LangConstant.LANG_ZH;
    private static final String TEST_SALE_ATTR_CODE_1 = "color";
    private static final String TEST_SALE_ATTR_CODE_2 = "size";
    private static final Long TEST_SALE_ATTR_VALUE_ID_1 = 1001L;
    private static final Long TEST_SALE_ATTR_VALUE_ID_2 = 1002L;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    /**
     * 测试getSaleAttributeNameLangsByCode方法 - 正常情况
     */
    @Test
    void testGetSaleAttributeNameLangsByCode_Success() {
        // 准备测试数据
        List<String> saleAttributeCodeList = Arrays.asList(TEST_SALE_ATTR_CODE_1, TEST_SALE_ATTR_CODE_2);
        List<SaleAttributeLangPO> mockLangPOList = createMockSaleAttributeLangPOList();

        // Mock依赖调用
        when(saleAttributeLangAtomicService.getValidBySaleAttributeCodeList(saleAttributeCodeList))
                .thenReturn(mockLangPOList);

        // 执行测试
        Map<String, String> result = saleAttributeLangManageService.getSaleAttributeNameLangsByCode(
                saleAttributeCodeList, TEST_LANG_EN);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Color", result.get(TEST_SALE_ATTR_CODE_1));
        assertEquals("Size", result.get(TEST_SALE_ATTR_CODE_2));

        // 验证Mock调用
        verify(saleAttributeLangAtomicService).getValidBySaleAttributeCodeList(saleAttributeCodeList);
    }

    /**
     * 测试getSaleAttributeNameLangsByCode方法 - 没有找到对应语言，使用英文兜底
     */
    @Test
    void testGetSaleAttributeNameLangsByCode_FallbackToEnglish() {
        // 准备测试数据 - 只有英文翻译，没有德语
        List<String> saleAttributeCodeList = Arrays.asList(TEST_SALE_ATTR_CODE_1);
        List<SaleAttributeLangPO> mockLangPOList = createMockSaleAttributeLangPOListWithEnglishOnly();

        // Mock依赖调用
        when(saleAttributeLangAtomicService.getValidBySaleAttributeCodeList(saleAttributeCodeList))
                .thenReturn(mockLangPOList);

        // 执行测试 - 请求德语，但只有英语可用
        Map<String, String> result = saleAttributeLangManageService.getSaleAttributeNameLangsByCode(
                saleAttributeCodeList, "de");

        // 验证结果 - 应该返回英文兜底
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Color", result.get(TEST_SALE_ATTR_CODE_1));
    }

    /**
     * 测试getSaleAttributeNameLangsByCode方法 - 没有任何翻译，使用code兜底
     */
    @Test
    void testGetSaleAttributeNameLangsByCode_FallbackToCode() {
        // 准备测试数据
        List<String> saleAttributeCodeList = Arrays.asList(TEST_SALE_ATTR_CODE_1);

        // Mock依赖调用 - 返回空列表
        when(saleAttributeLangAtomicService.getValidBySaleAttributeCodeList(saleAttributeCodeList))
                .thenReturn(Collections.emptyList());

        // 执行测试
        Map<String, String> result = saleAttributeLangManageService.getSaleAttributeNameLangsByCode(
                saleAttributeCodeList, TEST_LANG_EN);

        // 验证结果 - 应该返回code作为兜底
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(TEST_SALE_ATTR_CODE_1, result.get(TEST_SALE_ATTR_CODE_1));
    }

    /**
     * 测试getSaleAttributeNameLangsByCode方法 - 参数为空
     */
    @Test
    void testGetSaleAttributeNameLangsByCode_EmptyParams() {
        // 测试空列表
        Map<String, String> result1 = saleAttributeLangManageService.getSaleAttributeNameLangsByCode(
                Collections.emptyList(), TEST_LANG_EN);
        assertTrue(result1.isEmpty());

        // 测试null列表
        Map<String, String> result2 = saleAttributeLangManageService.getSaleAttributeNameLangsByCode(
                null, TEST_LANG_EN);
        assertTrue(result2.isEmpty());

        // 测试空语言
        List<String> codeList = Arrays.asList(TEST_SALE_ATTR_CODE_1);
        Map<String, String> result3 = saleAttributeLangManageService.getSaleAttributeNameLangsByCode(
                codeList, null);
        assertTrue(result3.isEmpty());

        Map<String, String> result4 = saleAttributeLangManageService.getSaleAttributeNameLangsByCode(
                codeList, "");
        assertTrue(result4.isEmpty());

        // 验证没有调用原子服务
        verify(saleAttributeLangAtomicService, never()).getValidBySaleAttributeCodeList(anyList());
    }

    /**
     * 测试getSaleAttributeValueNameLangsByIds方法 - 正常情况
     */
    @Test
    void testGetSaleAttributeValueNameLangsByIds_Success() {
        // 准备测试数据
        List<Long> valueIdList = Arrays.asList(TEST_SALE_ATTR_VALUE_ID_1, TEST_SALE_ATTR_VALUE_ID_2);
        List<SaleAttributeValueLangPO> mockLangPOList = createMockSaleAttributeValueLangPOList();
        List<SaleAttributeValueLangVO> mockLangVOList = createMockSaleAttributeValueLangVOList();

        // Mock依赖调用
        when(saleAttributeValueLangAtomicService.getValidBySaleAttributeValueIds(valueIdList))
                .thenReturn(mockLangPOList);

        // Mock静态方法
        try (MockedStatic<SaleAttributeConvert> mockedConvert = mockStatic(SaleAttributeConvert.class)) {
            mockedConvert.when(() -> SaleAttributeConvert.INSTANCE.toValueLangVOList(anyList()))
                    .thenReturn(mockLangVOList);

            // 执行测试
            Map<Long, List<SaleAttributeValueLangVO>> result = saleAttributeLangManageService
                    .getSaleAttributeValueNameLangsByIds(valueIdList);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size());
            assertTrue(result.containsKey(TEST_SALE_ATTR_VALUE_ID_1));
            assertTrue(result.containsKey(TEST_SALE_ATTR_VALUE_ID_2));
            assertNotNull(result.get(TEST_SALE_ATTR_VALUE_ID_1));
            assertNotNull(result.get(TEST_SALE_ATTR_VALUE_ID_2));
        }

        // 验证Mock调用
        verify(saleAttributeValueLangAtomicService).getValidBySaleAttributeValueIds(valueIdList);
    }

    /**
     * 测试getSaleAttributeValueNameLangsByIds方法 - 空参数
     */
    @Test
    void testGetSaleAttributeValueNameLangsByIds_EmptyParams() {
        // 测试空列表
        Map<Long, List<SaleAttributeValueLangVO>> result1 = saleAttributeLangManageService
                .getSaleAttributeValueNameLangsByIds(Collections.emptyList());
        assertTrue(result1.isEmpty());

        // 测试null
        Map<Long, List<SaleAttributeValueLangVO>> result2 = saleAttributeLangManageService
                .getSaleAttributeValueNameLangsByIds(null);
        assertTrue(result2.isEmpty());

        // 验证没有调用原子服务
        verify(saleAttributeValueLangAtomicService, never()).getValidBySaleAttributeValueIds(anyList());
    }

    /**
     * 测试getLocalTranslateSaleAttributeNamesByLang方法 - 正常情况
     */
    @Test
    void testGetLocalTranslateSaleAttributeNamesByLang_Success() {
        // 准备测试数据
        Set<String> saleAttributeNameSet = Sets.newHashSet("颜色", "尺寸");
        Map<String, Map<String, String>> mockTranslateResult = createMockTranslateResult();
        
        DataResponse<Map<String, Map<String, String>>> mockTranslateResponse = DataResponse.success(mockTranslateResult);

        // Mock依赖调用
        when(textTranslateManageService.batchTranslateMultiLangText(any(BatchMultiTranslateReqDTO.class)))
                .thenReturn(mockTranslateResponse);

        // 执行测试
        Map<String, String> result = saleAttributeLangManageService.getLocalTranslateSaleAttributeNamesByLang(
                saleAttributeNameSet, TEST_LANG_EN);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.containsKey("颜色"));
        assertTrue(result.containsKey("尺寸"));
        assertEquals("Color", result.get("颜色"));
        assertEquals("Size", result.get("尺寸"));

        // 验证Mock调用
        verify(textTranslateManageService).batchTranslateMultiLangText(any(BatchMultiTranslateReqDTO.class));
    }

    /**
     * 测试getLocalTranslateSaleAttributeNamesByLang方法 - 空参数
     */
    @Test
    void testGetLocalTranslateSaleAttributeNamesByLang_EmptyParams() {
        // 测试空Set
        Map<String, String> result1 = saleAttributeLangManageService.getLocalTranslateSaleAttributeNamesByLang(
                Sets.newHashSet(), TEST_LANG_EN);
        assertTrue(result1.isEmpty());

        // 测试null Set
        Map<String, String> result2 = saleAttributeLangManageService.getLocalTranslateSaleAttributeNamesByLang(
                null, TEST_LANG_EN);
        assertTrue(result2.isEmpty());

        // 测试空语言
        Set<String> nameSet = Sets.newHashSet("颜色");
        Map<String, String> result3 = saleAttributeLangManageService.getLocalTranslateSaleAttributeNamesByLang(
                nameSet, null);
        assertTrue(result3.isEmpty());

        // 验证没有调用其他服务
        verify(langManageService, never()).getLangCodeList();
    }

    /**
     * 测试createCrossBorderSaleAttributeValueLangs方法 - 正常情况
     */
    @Test
    void testCreateCrossBorderSaleAttributeValueLangs_Success() {
        // 准备测试数据
        List<SaleAttributeValueVO> saleAttributeValueVOList = createMockSaleAttributeValueVOList();
        List<String> mockLangList = Arrays.asList(LangConstant.LANG_EN, LangConstant.LANG_VN);
        
        DataResponse<Map<String, Map<String, String>>> mockTranslateResponse = DataResponse.success(createMockTranslateResponseMap());

        // Mock依赖调用
        when(langManageService.getLangCodeList()).thenReturn(mockLangList);
        when(textTranslateManageService.batchTranslateMultiLangText(any(BatchMultiTranslateReqDTO.class)))
                .thenReturn(mockTranslateResponse);

        // 执行测试
        assertDoesNotThrow(() -> {
            saleAttributeLangManageService.createCrossBorderSaleAttributeValueLangs(saleAttributeValueVOList);
        });

        // 验证Mock调用
        verify(langManageService).getLangCodeList();
        verify(textTranslateManageService).batchTranslateMultiLangText(any(BatchMultiTranslateReqDTO.class));
        verify(saleAttributeValueLangAtomicService).saveBatch(anyList());
    }

    /**
     * 测试createCrossBorderSaleAttributeValueLangs方法 - 空参数
     */
    @Test
    void testCreateCrossBorderSaleAttributeValueLangs_EmptyParams() {
        // 测试空列表
        assertDoesNotThrow(() -> {
            saleAttributeLangManageService.createCrossBorderSaleAttributeValueLangs(Collections.emptyList());
        });

        // 测试null
        assertDoesNotThrow(() -> {
            saleAttributeLangManageService.createCrossBorderSaleAttributeValueLangs(null);
        });

        // 验证没有调用其他服务
        verify(langManageService, never()).getLangCodeList();
        verify(textTranslateManageService, never()).batchTranslateMultiLangText(any(BatchMultiTranslateReqDTO.class));
        verify(saleAttributeValueLangAtomicService, never()).saveBatch(anyList());
    }

    /**
     * 测试saveLocalOrEditCrossBorderSaleAttributeValueLangs方法 - 正常情况
     */
    @Test
    void testSaveLocalOrEditCrossBorderSaleAttributeValueLangs_Success() {
        // 准备测试数据
        List<SaleAttributeValueLangVO> saleAttributeValueLangVOList = createMockSaleAttributeValueLangVOList();
        List<SaleAttributeValueLangPO> mockExistingPOList = createMockExistingSaleAttributeValueLangPOList();

        // Mock依赖调用
        when(saleAttributeValueLangAtomicService.getValidBySaleAttributeValueIds(anyList()))
                .thenReturn(mockExistingPOList);

        // 执行测试
        assertDoesNotThrow(() -> {
            saleAttributeLangManageService.updateCrossBorderSaleAttributeValueLangs(saleAttributeValueLangVOList);
        });

        // 验证Mock调用
        verify(saleAttributeValueLangAtomicService).getValidBySaleAttributeValueIds(anyList());
        verify(saleAttributeValueLangAtomicService).saveOrUpdateBatch(anyList());
    }

    /**
     * 测试saveLocalOrEditCrossBorderSaleAttributeValueLangs方法 - 空参数
     */
    @Test
    void testSaveLocalOrEditCrossBorderSaleAttributeValueLangs_EmptyParams() {
        // 测试空列表
        assertDoesNotThrow(() -> {
            saleAttributeLangManageService.updateCrossBorderSaleAttributeValueLangs(Collections.emptyList());
        });

        // 测试null
        assertDoesNotThrow(() -> {
            saleAttributeLangManageService.updateCrossBorderSaleAttributeValueLangs(null);
        });

        // 验证没有调用其他服务
        verify(saleAttributeValueLangAtomicService, never()).getValidBySaleAttributeValueIds(anyList());
        verify(saleAttributeValueLangAtomicService, never()).saveOrUpdateBatch(anyList());
    }

    // ===== 辅助方法：创建Mock测试数据 =====

    private List<SaleAttributeLangPO> createMockSaleAttributeLangPOList() {
        SaleAttributeLangPO lang1 = new SaleAttributeLangPO();
        lang1.setSaleAttributeCode(TEST_SALE_ATTR_CODE_1);
        lang1.setLang(TEST_LANG_EN);
        lang1.setLangName("Color");
        lang1.setYn(YnEnum.YES.getCode());

        SaleAttributeLangPO lang2 = new SaleAttributeLangPO();
        lang2.setSaleAttributeCode(TEST_SALE_ATTR_CODE_2);
        lang2.setLang(TEST_LANG_EN);
        lang2.setLangName("Size");
        lang2.setYn(YnEnum.YES.getCode());

        return Arrays.asList(lang1, lang2);
    }

    private List<SaleAttributeLangPO> createMockSaleAttributeLangPOListWithEnglishOnly() {
        SaleAttributeLangPO lang1 = new SaleAttributeLangPO();
        lang1.setSaleAttributeCode(TEST_SALE_ATTR_CODE_1);
        lang1.setLang(TEST_LANG_EN);
        lang1.setLangName("Color");
        lang1.setYn(YnEnum.YES.getCode());

        return Arrays.asList(lang1);
    }

    private List<SaleAttributeValueLangPO> createMockSaleAttributeValueLangPOList() {
        SaleAttributeValueLangPO lang1 = new SaleAttributeValueLangPO();
        lang1.setSaleAttributeValueId(TEST_SALE_ATTR_VALUE_ID_1);
        lang1.setLang(TEST_LANG_EN);
        lang1.setLangName("Red");
        lang1.setYn(YnEnum.YES.getCode());

        SaleAttributeValueLangPO lang2 = new SaleAttributeValueLangPO();
        lang2.setSaleAttributeValueId(TEST_SALE_ATTR_VALUE_ID_2);
        lang2.setLang(TEST_LANG_EN);
        lang2.setLangName("Blue");
        lang2.setYn(YnEnum.YES.getCode());

        return Arrays.asList(lang1, lang2);
    }

    private List<SaleAttributeValueLangVO> createMockSaleAttributeValueLangVOList() {
        SaleAttributeValueLangVO vo1 = new SaleAttributeValueLangVO();
        vo1.setSaleAttributeValueId(TEST_SALE_ATTR_VALUE_ID_1);
        vo1.setLang(TEST_LANG_EN);
        vo1.setLangName("Red");

        SaleAttributeValueLangVO vo2 = new SaleAttributeValueLangVO();
        vo2.setSaleAttributeValueId(TEST_SALE_ATTR_VALUE_ID_2);
        vo2.setLang(TEST_LANG_EN);
        vo2.setLangName("Blue");

        return Arrays.asList(vo1, vo2);
    }

    private Map<String, Map<String, String>> createMockTranslateResult() {
        Map<String, Map<String, String>> result = new HashMap<>();
        
        Map<String, String> colorTranslates = new HashMap<>();
        colorTranslates.put(TEST_LANG_EN, "Color");
        colorTranslates.put(LangConstant.LANG_VN, "Color");
        result.put("颜色", colorTranslates);
        
        Map<String, String> sizeTranslates = new HashMap<>();
        sizeTranslates.put(TEST_LANG_EN, "Size");
        sizeTranslates.put(LangConstant.LANG_VN, "Tamaño");
        result.put("尺寸", sizeTranslates);
        
        return result;
    }

    private List<SaleAttributeValueVO> createMockSaleAttributeValueVOList() {
        SaleAttributeValueVO vo1 = new SaleAttributeValueVO();
        vo1.setId(TEST_SALE_ATTR_VALUE_ID_1);
        vo1.setSaleAttributeValueName("红色");
        vo1.setSaleAttributeId(1L);

        SaleAttributeValueVO vo2 = new SaleAttributeValueVO();
        vo2.setId(TEST_SALE_ATTR_VALUE_ID_2);
        vo2.setSaleAttributeValueName("蓝色");
        vo2.setSaleAttributeId(1L);

        return Arrays.asList(vo1, vo2);
    }

    private Map<String, Map<String, String>> createMockTranslateResponseMap() {
        Map<String, Map<String, String>> result = new HashMap<>();
        
        // 红色的翻译结果
        Map<String, String> redTranslates = new HashMap<>();
        redTranslates.put("en", "Red");
        redTranslates.put("es", "Rojo");
        redTranslates.put("vn", "Đỏ");
        result.put("红色", redTranslates);
        
        // 蓝色的翻译结果
        Map<String, String> blueTranslates = new HashMap<>();
        blueTranslates.put("en", "Blue");
        blueTranslates.put("es", "Azul");
        blueTranslates.put("vn", "Xanh");
        result.put("蓝色", blueTranslates);
        
        return result;
    }

    private List<SaleAttributeValueLangPO> createMockExistingSaleAttributeValueLangPOList() {
        SaleAttributeValueLangPO existing1 = new SaleAttributeValueLangPO();
        existing1.setId(100L);
        existing1.setSaleAttributeValueId(TEST_SALE_ATTR_VALUE_ID_1);
        existing1.setLang(TEST_LANG_EN);
        existing1.setLangName("OldRed");

        return Arrays.asList(existing1);
    }
} 