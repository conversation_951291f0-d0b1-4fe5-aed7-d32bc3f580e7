package com.jdi.isc.product.soa.service;

import com.google.common.collect.Lists;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class SplitIdList {
    public static void main(String[] args) throws Exception {
        // 用类加载器读取resources下文件（确保文件在resources目录下）
        BufferedReader reader = new BufferedReader(new InputStreamReader(
                Objects.requireNonNull(SplitIdList.class.getClassLoader().getResourceAsStream("id.text"))));
        List<String> ids = new ArrayList<>();
        String line;
        while ((line = reader.readLine()) != null) {
            String trimmed = line.trim();
            if (!trimmed.isEmpty()) {
                ids.add(trimmed);
            }
        }
        reader.close();

        for (List<String> list : Lists.partition(ids, 500)) {

            String tempIds = String.join(",", list);

            String sql = "update jdi_isc_country_agreement_price_warning_sharding set yn = 0, update_time = 1753680865000 where id in (%s);";

            System.out.println(String.format(sql, tempIds));
        }
    }
}