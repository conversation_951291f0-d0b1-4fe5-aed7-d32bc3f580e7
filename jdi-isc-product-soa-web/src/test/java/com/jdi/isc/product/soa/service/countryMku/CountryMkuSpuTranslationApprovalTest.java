package com.jdi.isc.product.soa.service.countryMku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.domain.spu.biz.SpuLangExtendVO;
import com.jdi.isc.product.soa.service.manage.countryMku.CountryMkuManageService;
import com.jdi.isc.product.soa.service.manage.mku.MkuLangManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuLangManageService;
import com.jdi.isc.product.soa.web.ServiceApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @Description: 国际池商品名称翻译相关服务单元测试
 * @Author: zhaoyan316
 * @Date: 2024-12-20
 **/
@Slf4j
@AutoConfigureMockMvc
@SpringBootTest(classes = ServiceApplication.class)
@Transactional
@Rollback
public class CountryMkuSpuTranslationApprovalTest {

    @Resource
    private MkuLangManageService mkuLangManageService;
    
    @Resource
    private SpuLangManageService spuLangManageService;
    
    @Resource
    private CountryMkuManageService countryMkuManageService;

    // 测试用的常量
    private static final Long TEST_MKU_ID = 99999999L;
    private static final Long TEST_SPU_ID = 88888888L;
    private static final String TEST_COUNTRY_CODE = "TH";
    
    /**
     * 测试 MkuLangManageService.updateMkuTitleLang 方法
     */
    @Test
    public void testUpdateMkuTitleLang() {
        log.info("开始测试 MkuLangManageService.updateMkuTitleLang 方法");
        
        // 准备测试数据
        Map<String, String> langNameMap = new HashMap<>();
        langNameMap.put(LangConstant.LANG_ZH, "测试MKU中文名称");
        langNameMap.put(LangConstant.LANG_EN, "Test MKU English Name");
        langNameMap.put(LangConstant.LANG_TH, "ชื่อผลิตภัณฑ์ทดสอบ MKU");
        
        // 执行测试
        DataResponse<String> response = mkuLangManageService.updateMkuTitleLang(TEST_MKU_ID, langNameMap);
        
        // 验证结果
        assertNotNull(response);
        assertTrue(response.getSuccess() || response.getMessage().contains("没有需要更新的内容"));
        log.info("MkuLangManageService.updateMkuTitleLang 测试完成，结果: {}", response.getMessage());
    }
    
    /**
     * 测试 MkuLangManageService.updateMkuTitleLang 方法 - 参数为空
     */
    @Test
    public void testUpdateMkuTitleLang_WithNullParams() {
        log.info("开始测试 MkuLangManageService.updateMkuTitleLang 方法 - 参数为空");
        
        // 测试 mkuId 为空
        DataResponse<String> response1 = mkuLangManageService.updateMkuTitleLang(null, new HashMap<>());
        assertNotNull(response1);
        assertFalse(response1.getSuccess());
        assertTrue(response1.getMessage().contains("mkuId不能为空"));
        
        // 测试 langNameMap 为空
        DataResponse<String> response2 = mkuLangManageService.updateMkuTitleLang(TEST_MKU_ID, null);
        assertNotNull(response2);
        assertFalse(response2.getSuccess());
        assertTrue(response2.getMessage().contains("多语言名称映射不能为空"));
        
        // 测试 langNameMap 为空Map
        DataResponse<String> response3 = mkuLangManageService.updateMkuTitleLang(TEST_MKU_ID, new HashMap<>());
        assertNotNull(response3);
        assertFalse(response3.getSuccess());
        assertTrue(response3.getMessage().contains("多语言名称映射不能为空"));
        
        log.info("MkuLangManageService.updateMkuTitleLang 参数校验测试完成");
    }
    
    /**
     * 测试 SpuLangManageService.updateSpuTitleLang 方法
     */
    @Test
    public void testUpdateSpuTitleLang() {
        log.info("开始测试 SpuLangManageService.updateSpuTitleLang 方法");
        
        // 准备测试数据
        Map<String, String> langNameMap = new HashMap<>();
        langNameMap.put(LangConstant.LANG_ZH, "测试SPU中文名称");
        langNameMap.put(LangConstant.LANG_EN, "Test SPU English Name");
        langNameMap.put(LangConstant.LANG_TH, "ชื่อผลิตภัณฑ์ทดสอบ SPU");
        
        // 执行测试
        DataResponse<String> result = spuLangManageService.updateSpuTitleLang(TEST_SPU_ID, langNameMap);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess() || result.getMessage().contains("没有需要更新的内容"));
        log.info("SpuLangManageService.updateSpuTitleLang 测试完成，结果: {}", result.getMessage());
    }
    
    /**
     * 测试 SpuLangManageService.updateSpuTitleLang 方法 - 参数异常
     */
    @Test
    public void testUpdateSpuTitleLang_WithException() {
        log.info("开始测试 SpuLangManageService.updateSpuTitleLang 方法 - 参数异常");
        
        // 测试 spuId 为空
        try {
            spuLangManageService.updateSpuTitleLang(null, new HashMap<>());
            fail("应该抛出异常");
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("spuId不能为空"));
        }
        
        // 测试 langNameMap 为空
        try {
            spuLangManageService.updateSpuTitleLang(TEST_SPU_ID, null);
            fail("应该抛出异常");
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("多语言名称映射不能为空"));
        }
        
        log.info("SpuLangManageService.updateSpuTitleLang 异常测试完成");
    }
    
    /**
     * 测试 CountryMkuManageService.updateMkuSpuTitleLangAndApprovalTranslation 方法
     */
    @Test
    public void testUpdateMkuSpuTitleLangAndApprovalTranslation() {
        log.info("开始测试 CountryMkuManageService.updateMkuSpuTitleLangAndApprovalTranslation 方法");
        
        // 准备测试数据
        Map<String, String> langNameMap = new HashMap<>();
        langNameMap.put(LangConstant.LANG_ZH, "测试商品中文名称");
        langNameMap.put(LangConstant.LANG_EN, "Test Product English Name");
        langNameMap.put(LangConstant.LANG_TH, "ชื่อผลิตภัณฑ์ทดสอบ");
        
        // 执行测试
        DataResponse<Boolean> response = countryMkuManageService.updateMkuSpuTitleLangAndApprovalTranslation(
            TEST_MKU_ID, TEST_COUNTRY_CODE, langNameMap);
        
        // 验证结果
        assertNotNull(response);
        // 由于测试数据可能不存在，允许返回错误，但不能为空
        log.info("CountryMkuManageService.updateMkuSpuTitleLangAndApprovalTranslation 测试完成，结果: {}", 
                response.getSuccess() ? "成功" : response.getMessage());
    }
    
    /**
     * 测试 CountryMkuManageService.updateMkuSpuTitleLangAndApprovalTranslation 方法 - 参数为空
     */
    @Test
    public void testUpdateMkuSpuTitleLangAndApprovalTranslation_WithNullParams() {
        log.info("开始测试 CountryMkuManageService.updateMkuSpuTitleLangAndApprovalTranslation 方法 - 参数为空");
        
        Map<String, String> langNameMap = new HashMap<>();
        langNameMap.put(LangConstant.LANG_ZH, "测试商品名称");
        
        // 测试 mkuId 为空
        DataResponse<Boolean> response1 = countryMkuManageService.updateMkuSpuTitleLangAndApprovalTranslation(
            null, TEST_COUNTRY_CODE, langNameMap);
        assertNotNull(response1);
        assertFalse(response1.getSuccess());
        assertTrue(response1.getMessage().contains("mkuId不能为空"));
        
        // 测试 countryCode 为空
        DataResponse<Boolean> response2 = countryMkuManageService.updateMkuSpuTitleLangAndApprovalTranslation(
            TEST_MKU_ID, null, langNameMap);
        assertNotNull(response2);
        assertFalse(response2.getSuccess());
        assertTrue(response2.getMessage().contains("目标国家代码不能为空"));
        
        // 测试 langNameMap 为空
        DataResponse<Boolean> response3 = countryMkuManageService.updateMkuSpuTitleLangAndApprovalTranslation(
            TEST_MKU_ID, TEST_COUNTRY_CODE, null);
        assertNotNull(response3);
        assertFalse(response3.getSuccess());
        assertTrue(response3.getMessage().contains("多语言名称映射不能为空"));
        
        log.info("CountryMkuManageService.updateMkuSpuTitleLangAndApprovalTranslation 参数校验测试完成");
    }
    
    /**
     * 测试 CountryMkuManageService.getMkuSpuTitleLang 方法
     */
    @Test
    public void testGetMkuSpuTitleLang() {
        log.info("开始测试 CountryMkuManageService.getMkuSpuTitleLang 方法");
        
        // 执行测试
        DataResponse<List<SpuLangExtendVO>> response = countryMkuManageService.getMkuSpuTitleLang(
            TEST_MKU_ID, TEST_COUNTRY_CODE);
        
        // 验证结果
        assertNotNull(response);
        // 由于测试数据可能不存在，允许返回错误，但结构应该正确
        if (response.getSuccess()) {
            List<SpuLangExtendVO> data = response.getData();
            assertNotNull(data);
            // 验证返回的语言类型应该包含中文、英文和目标国家语言
            log.info("获取到的语言信息数量: {}", data.size());
            for (SpuLangExtendVO langExtendVO : data) {
                assertNotNull(langExtendVO.getLang());
                assertNotNull(langExtendVO.getLangName());
                log.info("语言信息: lang={}, langName={}, spuTitle={}", 
                        langExtendVO.getLang(), langExtendVO.getLangName(), langExtendVO.getSpuTitle());
            }
        } else {
            log.info("getMkuSpuTitleLang 返回错误: {}", response.getMessage());
        }
        
        log.info("CountryMkuManageService.getMkuSpuTitleLang 测试完成");
    }
    
    /**
     * 测试 CountryMkuManageService.getMkuSpuTitleLang 方法 - 参数为空
     */
    @Test
    public void testGetMkuSpuTitleLang_WithNullParams() {
        log.info("开始测试 CountryMkuManageService.getMkuSpuTitleLang 方法 - 参数为空");
        
        // 测试 mkuId 为空
        DataResponse<List<SpuLangExtendVO>> response1 = countryMkuManageService.getMkuSpuTitleLang(
            null, TEST_COUNTRY_CODE);
        assertNotNull(response1);
        assertFalse(response1.getSuccess());
        assertTrue(response1.getMessage().contains("mkuId不能为空"));
        
        // 测试 targetCountryCode 为空
        DataResponse<List<SpuLangExtendVO>> response2 = countryMkuManageService.getMkuSpuTitleLang(
            TEST_MKU_ID, null);
        assertNotNull(response2);
        assertFalse(response2.getSuccess());
        assertTrue(response2.getMessage().contains("目标国家代码不能为空"));
        
        // 测试 targetCountryCode 为空字符串
        DataResponse<List<SpuLangExtendVO>> response3 = countryMkuManageService.getMkuSpuTitleLang(
            TEST_MKU_ID, "");
        assertNotNull(response3);
        assertFalse(response3.getSuccess());
        assertTrue(response3.getMessage().contains("目标国家代码不能为空"));
        
        log.info("CountryMkuManageService.getMkuSpuTitleLang 参数校验测试完成");
    }
    
    /**
     * 测试语言名称映射功能
     */
    @Test
    public void testLanguageNameMapping() {
        log.info("开始测试语言名称映射功能");
        
        // 测试不同国家代码的语言名称映射
        String[] countryCodes = {"TH", "VN", "BR", "MY", "ID", "PH", "SG", "XX"};
        
        for (int i = 0; i < countryCodes.length; i++) {
            DataResponse<List<SpuLangExtendVO>> response = countryMkuManageService.getMkuSpuTitleLang(
                TEST_MKU_ID, countryCodes[i]);
            
            assertNotNull(response);
            // 即使数据不存在，也应该能正确处理语言名称映射
            log.info("国家代码 {} 对应的响应: {}", countryCodes[i], response.getMessage());
        }
        
        log.info("语言名称映射功能测试完成");
    }
    
    /**
     * 测试并发更新场景
     */
    @Test
    public void testConcurrentUpdate() {
        log.info("开始测试并发更新场景");
        
        Map<String, String> langNameMap1 = new HashMap<>();
        langNameMap1.put(LangConstant.LANG_ZH, "并发测试商品名称1");
        
        Map<String, String> langNameMap2 = new HashMap<>();
        langNameMap2.put(LangConstant.LANG_EN, "Concurrent Test Product Name 2");
        
        // 模拟并发更新
        DataResponse<String> response1 = mkuLangManageService.updateMkuTitleLang(TEST_MKU_ID, langNameMap1);
        DataResponse<String> response2 = mkuLangManageService.updateMkuTitleLang(TEST_MKU_ID, langNameMap2);
        
        // 验证两次更新都能正常处理
        assertNotNull(response1);
        assertNotNull(response2);
        
        log.info("并发更新测试完成，response1: {}, response2: {}", 
                response1.getMessage(), response2.getMessage());
    }
    
    /**
     * 测试边界值和特殊字符
     */
    @Test
    public void testBoundaryAndSpecialCharacters() {
        log.info("开始测试边界值和特殊字符");
        
        Map<String, String> langNameMap = new HashMap<>();
        langNameMap.put(LangConstant.LANG_ZH, "测试商品名称！@#$%^&*()_+-={}[]|\\:;\"'<>?,./ 中文特殊字符");
        langNameMap.put(LangConstant.LANG_EN, "Test Product Name With Special Characters !@#$%^&*()_+-={}[]|\\:;\"'<>?,./");
        langNameMap.put(LangConstant.LANG_TH, "ชื่อผลิตภัณฑ์ทดสอบพร้อมอักขระพิเศษ");
        
        // 测试特殊字符
        DataResponse<String> response = mkuLangManageService.updateMkuTitleLang(TEST_MKU_ID, langNameMap);
        assertNotNull(response);
        
        // 测试空字符串
        langNameMap.clear();
        langNameMap.put(LangConstant.LANG_ZH, "");
        langNameMap.put(LangConstant.LANG_EN, "   ");  // 空白字符
        
        DataResponse<String> response2 = mkuLangManageService.updateMkuTitleLang(TEST_MKU_ID, langNameMap);
        assertNotNull(response2);
        
        log.info("边界值和特殊字符测试完成");
    }
}
