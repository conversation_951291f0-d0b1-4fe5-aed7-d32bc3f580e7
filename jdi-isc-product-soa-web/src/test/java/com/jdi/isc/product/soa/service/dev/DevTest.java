package com.jdi.isc.product.soa.service.dev;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.devOps.DevOpsApiService;
import com.jdi.isc.product.soa.api.sku.res.SkuFeatureApiDTO;
import com.jdi.isc.product.soa.web.ServiceApplication;
import com.jdi.isc.product.soa.web.controller.devpos.DevOpsController;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * <AUTHOR>
 * @description：DevTest
 * @Date 2025-01-02
 */
@Slf4j
@SpringBootTest(classes = ServiceApplication.class)
@AutoConfigureMockMvc
@ActiveProfiles("test")
public class DevTest {

    @Autowired
    private DevOpsApiService devOpsApiService;

    @Autowired
    DevOpsController devOpsController;

    @Test
    public void testGetSkuById() {
        devOpsController.checkDraftRepeatAttribute("dDFSADF111",100);
    }

    @Test
    public void testSkuFeature() {
        List<SkuFeatureApiDTO> apiDTOList = Lists.newArrayList();
        SkuFeatureApiDTO apiDTO = new SkuFeatureApiDTO();
        apiDTO.setSkuId(80000000015L);
        apiDTO.setPackageSpecification(1000L);
        apiDTO.setPackageSpecificationUnit(1);
        apiDTOList.add(apiDTO);
        DataResponse<String> dataResponse = devOpsApiService.addSkuFeaturePackageSpecification(apiDTOList);
        System.out.println(JSON.toJSONString(dataResponse));
    }

    public static final String UPDATE_TIME = "2000-06-10 10:37:39";

    /**
     * 扩展属性mku刷数据
     */
    @Test
    public void testProcessAllMkuExtAttributes(){
        devOpsApiService.processAllMkuExtAttributes(null);
    }

    /**
     * 扩展属性spu草稿刷数据，包括根据level将数据刷到sku草稿上
     */
    @Test
    public void testProcessAllSpuDraftExtProperties(){
        devOpsApiService.processAllSpuDraftExtProperties(null);
    }

    /**
     * 扩展属性spu刷数据，包括根据level将数据刷到sku上
     */
    @Test
    public void testProcessAllSpuExtAttributes(){
        devOpsApiService.processAllSpuExtAttributes(null);
    }

    /**
     * 处理单条SPU扩展属性数据
     */
    @Test
    public void testProcessSpuExtAttributesById(){
        Long spuId = 20000001247L;
        long spudraftID = 20000001245L;
        long mkuId = 50000000130L;
        devOpsApiService.processSpuExtAttributesBySpuId(spuId);
        devOpsApiService.processSpuDraftExtAttributesBySpuId(spudraftID);
        devOpsApiService.processMkuExtAttributesByMkuId(mkuId);
        System.out.println();
    }

}
