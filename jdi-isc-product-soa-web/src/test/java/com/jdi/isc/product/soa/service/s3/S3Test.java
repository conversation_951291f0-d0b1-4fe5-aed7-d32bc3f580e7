package com.jdi.isc.product.soa.service.s3;

//import com.jdi.isc.library.common.enmus.file.FileTypeEnum;
import com.jdi.isc.product.soa.common.util.S3Utils;
import com.jdi.isc.product.soa.web.ServiceApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@SpringBootTest(classes = ServiceApplication.class)
public class S3Test {

    @Resource
    private S3Utils s3Utils;

    @Test
    public void testUpload() {
        String basePath = "/Users/<USER>/Desktop/";
        List<String> nameList = new ArrayList<>(Arrays.asList("id_categoryRateTemplate.xlsx"));

        for(String name:nameList){
//            try {
//                String fixUrl = s3Utils.upload(new FileInputStream(basePath+name), FileTypeEnum.BATCH_FILE_TEMPLATE.getCode(), name);
//                System.out.println(fixUrl);
//            } catch (FileNotFoundException e) {
//                throw new RuntimeException(e);
//            }
        }
    }


}
