package com.jdi.isc.product.soa.service.manage.saleAttribute.impl;

import lombok.Data;

import java.util.List;

/**
 * 商品属性信息数据传输对象
 * <AUTHOR>
 * @date 2025/5/24
 */
@Data
public class VcPropertyDTO {

    /**
     * Integer shield（隐藏属性 shield字段1为隐藏）
     */
    private Integer shield;
    /**
     * String isQuJianZhi（为 1 表明为区间属性）
     */
    private String isQuJianZhi;

    /**
     * 属性组id
     */
    private Integer comGroupId;

    /**
     * 属性组名称
     */
    private String comGroupName;

    /**
     * SKU属性下沉  属性级别  0:product  1:sku  2:product和sku都能用
     * attrLevel:0   product
     * attrLevel:1   sku
     * attrLevel:2   都可以
     * 默认 = attrLevel:0
     */
    private Integer level;

    /**
     * 属性名
     */
    private String attributeName;
    /**
     * 属性ID
     */
    private Long attributeId;
    /**
     * 属性类型
     */
    private Integer attributeType;
    /**
     * 属性值录入类型
     */
    private Integer attributeInputType;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 是否必填
     */
    private Boolean required;
    /**
     * VC是否展示
     */
    private Integer showSupplier;
    /**
     * 属性值列表
     */
    private List<VcPropertyValueDTO> propertyValues;
    /**
     * 输入为文本时校验填入内容：1：字符串 2:数字
     */
    private Integer inputCheckType;
}
