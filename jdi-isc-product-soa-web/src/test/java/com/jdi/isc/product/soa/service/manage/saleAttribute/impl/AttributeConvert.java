package com.jdi.isc.product.soa.service.manage.saleAttribute.impl;

import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import com.jdi.isc.product.soa.api.spu.res.GroupPropertyDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 属性对象转换
 * <AUTHOR>
 * @date 20231124
 */
@Mapper
public interface AttributeConvert {

    AttributeConvert INSTANCE = Mappers.getMapper(AttributeConvert.class);


    @Mappings({
            @Mapping(target = "propertyValues", source = "propertyValueVOList")
    })
    VcPropertyDTO attributeDto2Dto(PropertyApiDTO input);

    List<VcPropertyDTO> listDto2Dto(List<PropertyApiDTO> inputs);
}
