package com.jdi.isc.product.soa.service.manage.saleAttribute.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Sets;
import com.jd.tp.common.masterdata.UniformBizInfo;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.attribute.IscAttributeReadApiService;
import com.jdi.isc.product.soa.api.attribute.req.AttributeVcQueryReqDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.MkuQueryEnum;
import com.jdi.isc.product.soa.api.common.enums.MkuQueryLangsEnum;
import com.jdi.isc.product.soa.api.devOps.DevOpsSaleApiService;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuLangsReqDTO;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuReqDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuLangsResDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuResDTO;
import com.jdi.isc.product.soa.api.spu.req.PropertyApiDTO;
import com.jdi.isc.product.soa.api.spu.req.SaveSpuApiDTO;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.enums.SaleAttributeTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.util.S3Utils;
import com.jdi.isc.product.soa.domain.attribute.biz.AttributeFlatVO;
import com.jdi.isc.product.soa.domain.attribute.po.ExtAttributeLangPO;
import com.jdi.isc.product.soa.domain.common.biz.BaseLangVO;
import com.jdi.isc.product.soa.domain.enums.FileTypeEnum;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SpuSaleAttributeVO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributePO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeValuePO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SkuSaleAttributeValueRelationPO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.*;
import com.jdi.isc.product.soa.domain.spu.po.SpuDraftPO;
import com.jdi.isc.product.soa.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SaleAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SaleAttributeValueAtomicService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SkuSaleAttributeValueRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.atomic.spu.SpuDraftAtomicService;
import com.jdi.isc.product.soa.service.manage.attribute.AttributeOutService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeLangManageService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuDraftManageService;
import com.jdi.isc.product.soa.service.manage.spu.SpuReadManageService;
import com.jdi.isc.product.soa.service.manage.spu.impl.SpuDraftManageServiceImpl;
import com.jdi.isc.product.soa.service.mapstruct.SpuConvertService;
import com.jdi.isc.product.soa.service.protocol.jsf.mku.IscProductSoaMkuReadApiServiceImpl;
import com.jdi.isc.product.soa.service.protocol.jsf.spu.IscProductSoaSpuWriteApiServiceImpl;
import com.jdi.isc.product.soa.service.spu.SpuVOGenerator;
import com.jdi.isc.product.soa.web.ServiceApplication;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceApplication.class)
@ActiveProfiles("test")
@Slf4j
public class SaleAttributeTest {

    @Autowired
    private SaleAttributeManageService saleAttributeManageService;

    @Autowired
    private SaleAttributeLangManageService saleAttributeLangManageService;

    private static final long spuId = 1111111111111111111L;

    private static final long jdCatId = 33622L;

    @Autowired
    private SpuDraftManageService spuDraftManageService;

    @Autowired
    private SpuReadManageService spuReadManageService;

    @Autowired
    private DevOpsSaleApiService devOpsSaleApiService;


    @Autowired
    private SkuInfoRpcService skuInfoRpcService;

    @Autowired
    private SaleAttributeAtomicService saleAttributeAtomicService;

    @Autowired
    private SaleAttributeValueAtomicService saleAttributeValueAtomicService;

    @Autowired
    private SaleAttributeManageServiceImpl saleAttributeManageServiceImpl;

    @Resource
    private SpuConvertService spuConvertService;

    @Resource
    private AttributeOutService attributeOutService;

    @Autowired
    private IscProductSoaSpuWriteApiServiceImpl iscProductSoaSpuWriteApiServiceImpl;
    @Resource
    private SpuDraftAtomicService spuDraftAtomicService;
    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private IscProductSoaMkuReadApiServiceImpl iscProductSoaMkuReadApiServiceImpl;

    @Autowired
    private S3Utils S3Utils;

    @Test
    public void testUpload() {
        Set<String> keyword = new HashSet<>();
        keyword.add("测试");
        keyword.add("测试2");
        List<ExtAttributeLangPO> existZhList = null;
//        System.out.println(JSON.toJSONString(initDate(keyword, existZhList)));
        existZhList = new ArrayList<>();
        ExtAttributeLangPO a1 = new ExtAttributeLangPO();
        a1.setExtAttributeName("测试");
        existZhList.add(a1);
//        System.out.println(JSON.toJSONString(initDate(keyword, existZhList)));
        existZhList = new ArrayList<>();
        ExtAttributeLangPO a2 = new ExtAttributeLangPO();
        a2.setExtAttributeName("测试2");
        existZhList.add(a2);
//        System.out.println(JSON.toJSONString(initDate(keyword, existZhList)));
    }

    @Test
    public void testGetCategorySaleAttributesByJdCatId() {
        String fileName = "jdSkuCatMatchAmount_" + System.currentTimeMillis() + ".txt";
        String filePath = "C:/tmp/" + fileName;
        File file = new File(filePath);
        if(!file.exists()){
            try {
                file.createNewFile();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        try (FileWriter fileWriter = new FileWriter(file)) {
            for (int i = 0; i < 1000; i++) {
                fileWriter.write(i +","+i+","+i+","+i+","+i+ "\n");
            }
            fileWriter.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
        String url = S3Utils.upload(filePath, FileTypeEnum.FILE.getCode());
        System.out.println(url);
    }
    @Test
    public void testMkuLangs(){
        BatchQueryMkuReqDTO req = new BatchQueryMkuReqDTO();
        Long mkuId=50000001388L;
        req.setMkuId(Sets.newHashSet(mkuId));
        req.setClientCode("yPpvH0qjoPodSR8M4LCb");
        req.setQueryEnum(Sets.newHashSet(MkuQueryEnum.BASE, MkuQueryEnum.EXTEND));
        req.setUniformBizInfo(new UniformBizInfo());
        req.setLangSet(Sets.newHashSet(LangConstant.LANG_ZH, LangConstant.LANG_EN));
        DataResponse<Map<Long, IscMkuResDTO>> iscMku = iscProductSoaMkuReadApiServiceImpl.getIscMku(req);
        System.out.println(JSON.toJSONString(iscMku));
        BatchQueryMkuLangsReqDTO req2 = new BatchQueryMkuLangsReqDTO();
        req2.setMkuId(Sets.newHashSet(mkuId));
        req2.setClientCode("yPpvH0qjoPodSR8M4LCb");
        req2.setUniformBizInfo(new UniformBizInfo());
        req2.setLangs(Sets.newHashSet(LangConstant.LANG_ZH, LangConstant.LANG_EN, LangConstant.LANG_VN));
        req2.setQueryEnum(Sets.newHashSet(MkuQueryLangsEnum.EXTEND_ATTRIBUTE, MkuQueryLangsEnum.SELL_ATTRIBUTE));
        DataResponse<Map<Long, IscMkuLangsResDTO>> iscMkuLangs = iscProductSoaMkuReadApiServiceImpl.getIscMkuLangs(req2);
        System.out.println(JSON.toJSONString(iscMkuLangs));
        try {
            Thread.sleep(1000*20);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        iscMkuLangs = iscProductSoaMkuReadApiServiceImpl.getIscMkuLangs(req2);
        System.out.println(JSON.toJSONString(iscMkuLangs));
    }

    @Test
    public void testCreateSkuSaleAttributeForCrossBorder(){
        long spuId= 20000002533L;
        // 通过spuId查询spuDraft数据
        SpuDraftPO spuDraftPO = spuDraftAtomicService.getOne(Wrappers.lambdaQuery(SpuDraftPO.class)
                .eq(SpuDraftPO::getSpuId, spuId)
                .eq(SpuDraftPO::getYn, YnEnum.YES.getCode()));
        // 判断是否是跨境品
        if (!CountryConstant.COUNTRY_ZH.equals(spuDraftPO.getSourceCountryCode())) {
            log.info("DevOpsSaleApiServiceImpl.refreshSingleCrossBoarderSpuSaleAttribute 不是跨境品，无需刷新, spuId: {}", spuId);
            return;
        }
        // 通过spuid到sku主表查询sku
        List<SkuPO> skuPOS = skuAtomicService.getSkuPoBySpuId(spuId);
        // 物理删除spu的销售属性
//        saleAttributeManageServiceImpl.physicalDeleteSpuSaleAttribute(spuId);
        log.info("DevOpsSaleApiServiceImpl.refreshSingleCrossBoarderSpuSaleAttribute 物理删除spu的销售属性完成, spuId: {}, skuId: {}, jdSkuId: {}", spuId, skuPOS.get(0).getSkuId(), skuPOS.get(0).getJdSkuId());
        // 刷新销售属性
        saleAttributeManageService.createSkuSaleAttributeForCrossBorder(spuId, skuPOS.get(0).getSkuId(), skuPOS.get(0).getJdSkuId());
        log.info("DevOpsSaleApiServiceImpl.refreshSingleCrossBoarderSpuSaleAttribute 刷新跨境品销售属性完成, spuId: {}", spuId);
    }
    @Test
    public void testUpdateSkuSaleAttributeForCrossBorder(){
        List<SkuVO> skuVOList = JSON.parseArray(SpuVOGenerator.skuVOList, SkuVO.class);
        saleAttributeManageService.updateSkuSaleAttributeForCrossBorder(20000002523L, skuVOList);
    }

    @Test
    public void testUpdateSkuSaleAttributeValueLangNameForCrossBorder(){
        List<SkuVO> skuVOList = JSON.parseArray(SpuVOGenerator.skuVOList, SkuVO.class);
        saleAttributeManageService.updateSkuSaleAttributeValueLangNameForCrossBorder(skuVOList);
    }

    @Test
    @Transactional
    public void sumbitCrossBoarder(){
        SaveSpuApiDTO input = JSON.parseObject(SpuVOGenerator.saveSpuVo, SaveSpuApiDTO.class);
        input.getSpuVO().setMainImg("https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/ks1/JD_111243700425187025.jpeg");
        iscProductSoaSpuWriteApiServiceImpl.submit(input);
        if(input != null){
            throw new RuntimeException();
        }
    }

    @Test
    public void validateSkuSaleAttributeTest(){
        devOpsSaleApiService.validateSkuSaleAttribute(1, 10);
    }


    @Test
    public void sellAttrTest(){
//        astCatId=40426426&isExport=1&attributeScope=VN&sourceCountryCode=VN&supplierCode=TESTVN25022101
        AttributeVcQueryReqDTO result = new AttributeVcQueryReqDTO();
        result.setCategoryId(40426426L);
        result.setLang(LangConstant.LANG_EN);
        result.setSourceCountryCode("VN");
        result.setAttributeScope("VN");
        List<PropertyVO> propertyVOS = spuConvertService.getSaleAttribute(result.getCategoryId(), result.getLang());
        List<PropertyApiDTO> res = com.jdi.isc.product.soa.service.mapstruct.attribute.AttributeConvert.INSTANCE.listPropertyVo2Dto(propertyVOS);
        List<PropertyVO> propertyVOS1 = attributeOutService.querySellAttrByCatId(result.getCategoryId(), result.getLang());
        System.out.println(JSON.toJSONString(AttributeConvert.INSTANCE.listDto2Dto(res)));
        System.out.println(JSON.toJSONString(res));
        System.out.println(JSON.toJSONString(propertyVOS1));

        // 接口返回值
        // [{"attributeId":100100,"attributeInputType":3,"attributeName":"其他-测试","attributeType":1,"propertyValueVOList":[{"attributeId":100100,"attributeValueId":102801,"attributeValueName":"其它","langList":[],"sort":9999}],"required":false,"sort":1}]
        // [{"attributeId":100100,"attributeInputType":3,"attributeName":"其他-测试","attributeType":1,"propertyValues":[{"attributeId":100100,"attributeValueId":102801,"attributeValueName":"其它","sort":9999}],"required":false,"sort":1}]
    }

    @Test
    public void refreshSkuSaleAttributeSkuKeyForCrossBoarderTest() {
        devOpsSaleApiService.refreshSkuSaleAttributeSkuKeySkuIdForCrossBoarderAndLocal(1, 3, true);
        devOpsSaleApiService.refreshSkuSaleAttributeForLocalDraftAndApprovel(1, 3,5, false);
        devOpsSaleApiService.refreshSkuSaleAttributeForCrossBorder(1, 3,5, false);
        devOpsSaleApiService.refreshSkuJdMainSkuIdAndSpuId(1, 3,5, false);
        System.out.println("ok");
    }

    @Test
    public void test(){
        String jsonArrayString = "[\n" +
                "    {\n" +
                "        \"brandId\": 8000,\n" +
                "        \"businessResult\": [],\n" +
                "        \"businessRule\": true,\n" +
                "        \"catId\": 30444,\n" +
                "        \"currency\": \"CNY\",\n" +
                "        \"customerTradeType\": \"BD\",\n" +
                "        \"detailImgList\": [],\n" +
                "        \"fixedResult\": [],\n" +
                "        \"fixedRule\": true,\n" +
                "        \"height\": \"2\",\n" +
                "        \"length\": \"1\",\n" +
                "        \"mainImg\": \"https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/ks1/8076759011635989.jpg\",\n" +
                "        \"normal\": true,\n" +
                "        \"productionCycle\": 2,\n" +
                "        \"purchasePrice\": \"1.92\",\n" +
                "        \"skuCertificateVOList\": [],\n" +
                "        \"skuKey\": \"***********\",\n" +
                "        \"sourceCountryCode\": \"VN\",\n" +
                "        \"spuId\": ***********,\n" +
                "        \"stockNum\": \"1\",\n" +
                "        \"storeExtendPropertyList\": [],\n" +
                "        \"storeSalePropertyList\": [\n" +
                "            {\n" +
                "                \"attributeId\": 17001,\n" +
                "                \"langList\": [\n" +
                "                    {\n" +
                "                        \"lang\": \"zh\",\n" +
                "                        \"langName\": \"3\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"lang\": \"vi\",\n" +
                "                        \"langName\": \"3\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"lang\": \"en\",\n" +
                "                        \"langName\": \"4\"\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"selected\": true,\n" +
                "                \"sort\": 3\n" +
                "            },\n" +
                "            {\n" +
                "                \"attributeId\": 17002,\n" +
                "                \"attributeValueId\": 18701,\n" +
                "                \"lang\": \"zh\",\n" +
                "                \"langList\": [\n" +
                "                    {\n" +
                "                        \"id\": 27503,\n" +
                "                        \"lang\": \"zh\",\n" +
                "                        \"langName\": \"尺码8\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 27504,\n" +
                "                        \"lang\": \"vi\",\n" +
                "                        \"langName\": \"尺码8\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 27505,\n" +
                "                        \"lang\": \"en\",\n" +
                "                        \"langName\": \"尺码8\"\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"langName\": \"尺码8\",\n" +
                "                \"selected\": true,\n" +
                "                \"sort\": 1\n" +
                "            }\n" +
                "        ],\n" +
                "        \"taxPurchasePrice\": \"2\",\n" +
                "        \"valid\": true,\n" +
                "        \"vendorCode\": \"TestVn25022301\",\n" +
                "        \"vendorSkuId\": 2,\n" +
                "        \"vendorTradeType\": \"BD\",\n" +
                "        \"weight\": \"1\",\n" +
                "        \"weightSource\": 0,\n" +
                "        \"width\": \"2\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"brandId\": 8000,\n" +
                "        \"businessResult\": [],\n" +
                "        \"businessRule\": true,\n" +
                "        \"catId\": 30444,\n" +
                "        \"currency\": \"CNY\",\n" +
                "        \"customerTradeType\": \"BD\",\n" +
                "        \"detailImgList\": [],\n" +
                "        \"fixedResult\": [],\n" +
                "        \"fixedRule\": true,\n" +
                "        \"height\": \"2\",\n" +
                "        \"length\": \"1\",\n" +
                "        \"mainImg\": \"https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/ks1/8076759011635989.jpg\",\n" +
                "        \"moq\": 2,\n" +
                "        \"normal\": true,\n" +
                "        \"productionCycle\": 2,\n" +
                "        \"purchasePrice\": \"1.92\",\n" +
                "        \"skuCertificateVOList\": [],\n" +
                "        \"skuKey\": \"***********\",\n" +
                "        \"sourceCountryCode\": \"VN\",\n" +
                "        \"spuId\": ***********,\n" +
                "        \"stockNum\": \"2\",\n" +
                "        \"storeExtendPropertyList\": [],\n" +
                "        \"storeSalePropertyList\": [\n" +
                "            {\n" +
                "                \"attributeId\": 17001,\n" +
                "                \"langList\": [\n" +
                "                    {\n" +
                "                        \"lang\": \"zh\",\n" +
                "                        \"langName\": \"3\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"lang\": \"vi\",\n" +
                "                        \"langName\": \"3\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"lang\": \"en\",\n" +
                "                        \"langName\": \"4\"\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"selected\": true,\n" +
                "                \"sort\": 3\n" +
                "            },\n" +
                "            {\n" +
                "                \"attributeId\": 17002,\n" +
                "                \"attributeValueId\": 18702,\n" +
                "                \"lang\": \"zh\",\n" +
                "                \"langList\": [\n" +
                "                    {\n" +
                "                        \"id\": 27506,\n" +
                "                        \"lang\": \"zh\",\n" +
                "                        \"langName\": \"5\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 27507,\n" +
                "                        \"lang\": \"vi\",\n" +
                "                        \"langName\": \"6\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 27508,\n" +
                "                        \"lang\": \"en\",\n" +
                "                        \"langName\": \"7\"\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"langName\": \"5\",\n" +
                "                \"selected\": true,\n" +
                "                \"sort\": 2\n" +
                "            }\n" +
                "        ],\n" +
                "        \"taxPurchasePrice\": \"2\",\n" +
                "        \"valid\": true,\n" +
                "        \"vendorCode\": \"TestVn25022301\",\n" +
                "        \"vendorTradeType\": \"BD\",\n" +
                "        \"weight\": \"1\",\n" +
                "        \"weightSource\": 0,\n" +
                "        \"width\": \"2\"\n" +
                "    }\n" +
                "]";
        List<SkuVO> skuVOList = JSON.parseArray(jsonArrayString, SkuVO.class);
        saleAttributeManageServiceImpl.createSkuDraftSaleAttributes(***********L, skuVOList);
    }

    @Test
    public void updateDraftSaleAttributeRelationsForLocalSkuApprovelTest() {
        List<SaleAttributeValuePO> saleAttributeValuePOList = new ArrayList<>();
        SaleAttributeValuePO saleAttributeValuePO = new SaleAttributeValuePO();
        saleAttributeValuePO.setSaleAttributeId(100086082808L);
        saleAttributeValuePO.setSaleAttributeValueName("红色");
        // 设置创建者和更新者
        if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
            saleAttributeValuePO.setCreator(Constant.PIN_SYSTEM);
            saleAttributeValuePO.setUpdater(Constant.PIN_SYSTEM);
        }
        saleAttributeValuePO.setSort(1);
        saleAttributeValuePOList.add(saleAttributeValuePO);
        saleAttributeValuePO = new SaleAttributeValuePO();
        saleAttributeValuePO.setSaleAttributeId(1000860828081L);
        saleAttributeValuePO.setSaleAttributeValueName("红色1");
        // 设置创建者和更新者
        if (LoginContextHolder.getLoginContextHolder() == null || StringUtils.isBlank(LoginContextHolder.getLoginContextHolder().getPin())) {
            saleAttributeValuePO.setCreator(Constant.PIN_SYSTEM);
            saleAttributeValuePO.setUpdater(Constant.PIN_SYSTEM);
        }
        saleAttributeValuePO.setSort(1);
        saleAttributeValuePOList.add(saleAttributeValuePO);
        saleAttributeValueAtomicService.saveWithSortAndReturnWithId(spuId, saleAttributeValuePOList);
    }

    @Test
    public void updateSkuSaleAttributeForCrossBorderWithoutUserInputTest() {
        saleAttributeManageService.updateSkuSaleAttributeForCrossBorderWithoutUserInput(20000001281L, 80000000173L, 100086082808L);
        saleAttributeManageService.updateSkuSaleAttributeForCrossBorderWithoutUserInput(20000001224L, 80000000141L, 23422312L);
        saleAttributeManageService.updateSkuSaleAttributeForCrossBorderWithoutUserInput(20000001265L, 80000000166L, 100233882870L);

    }

    @Test
    public void getSaleAttributeTest() {
        SaleAttributePO saleAttributePO = saleAttributeAtomicService.saveOrUpdateSaleAttribute(100086082808L, 1, "红色", 1);
        System.out.println(JSON.toJSONString(saleAttributePO));
    }



    @Test
    public void getSkuByIdTest() {
        JdProductDTO jdProductDTO = skuInfoRpcService.getSkuById(100086082808L);
        System.out.println(JSON.toJSONString(jdProductDTO));
    }

    @Test
    public void refreshSkuSaleAttributeForLocalDraftAndApprovelTest() {
        devOpsSaleApiService.refreshSkuSaleAttributeForLocalDraftAndApprovel(1, 10000,100, false);
    }

    @Test
    public void refreshSkuSaleAttributeForCrossBorderTest() {
        devOpsSaleApiService.refreshSkuSaleAttributeForCrossBorder(1, 10000,100, false);
    }

    @Test
    public void refreshSkuJdMainSkuIdAndSpuIdTest() {
        devOpsSaleApiService.refreshSkuJdMainSkuIdAndSpuId(1, 10000,100, false);
    }

    @Test
    public void getSpuDetailTest() {
        SpuDetailReqVO spuDetailReqVO = new SpuDetailReqVO();
        spuDetailReqVO.setSpuId(20000002503L);
        spuDetailReqVO.setLang(LangConstant.LANG_ZH);
        SpuDetailVO spuDetailVO = spuReadManageService.getSpuDetail(spuDetailReqVO);
        System.out.println(JSON.toJSONString(spuDetailVO));
    }

    /**
     * 分页商品列表测试
     */
    @Test
    public void pageSpuDraftBySpuIdTest() {
        SpuQueryReqVO spuQueryReqVO = new SpuQueryReqVO();
        spuQueryReqVO.setSize(20L);
        spuQueryReqVO.setIndex(1L);
        spuQueryReqVO.setSourceCountryCode("VN");
        LangContextHolder.init(LangConstant.LANG_ZH);
        PageInfo<SpuVO>  page = spuDraftManageService.page(spuQueryReqVO);
        System.out.println(JSON.toJSONString(page));
    }

    @Test
    public void localPublishTest() {
        this.createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft();
        this.getSpuSaleAttributeDetail();
        this.updateDraftSaleAttributeRelationsForLocalSkuApprovel();
        this.getSpuSaleAttributeDetail();
    }

    @Test
    public void crossBorderPublishTest() {
        long skuId=80000003050L;
        List<PropertyVO> propertyVOList = saleAttributeManageService.getSkuSaleAttributeDetail(skuId, LangConstant.LANG_ZH);
        System.out.println(JSON.toJSONString(propertyVOList));
        List<String> langList = new ArrayList<>();  
        langList.add(LangConstant.LANG_VN);
        langList.add(LangConstant.LANG_TH);
        langList.add(LangConstant.LANG_ZH);
        propertyVOList = saleAttributeManageService.getSkuSaleAttributeDetailWithLangList(skuId, langList);
        System.out.println(JSON.toJSONString(propertyVOList));
    }

    @Test
    public void transferSkuSaleAttribute() {
        this.getSpuSaleAttributeDetail();
        saleAttributeManageService.transferSkuSaleAttribute(17563L, spuId);
        this.getSpuSaleAttributeDetail();
    }

    @Test
    public void getJdSkuSaleAttributeDetail() {
        long jdskuId = 100171215111L;
        List<PropertyValueVO> jdSkuSaleAttributeDetail = saleAttributeManageService.getJdSkuSaleAttributeDetail(jdskuId, "zh");
        System.out.println(JSON.toJSONString(jdSkuSaleAttributeDetail));
    }

    /**
     * 测试prepare接口
     */
    @Test
    public void prepare() {
        List<Long> jdCatIdList = new ArrayList<>();
        jdCatIdList.add(17563L);
        jdCatIdList.add(30435L);
        jdCatIdList.add(11303L);
        jdCatIdList.add(21398L);
        jdCatIdList.add(30435L);
        jdCatIdList.add(40426220L);
        for (Long jdCatId : jdCatIdList) {
            List<PropertyVO> propertyVOList = saleAttributeManageService.getCategorySaleAttributesByJdCatId(jdCatId, LangConstant.LANG_ZH);
            System.out.println(JSON.toJSONString(propertyVOList));
            for (PropertyVO propertyVO : propertyVOList) {
                System.out.println(propertyVO.getAttributeId() + " " + propertyVO.getAttributeName() + " " + propertyVO.getAttributeInputType());
            }
        }
    }


    /**
     * 测试通过SPU ID获取SPU销售属性详情
     */
    @Test
    public void getSpuSaleAttributeDetail() {

        // 2. 设置语言
        String lang = LangConstant.LANG_ZH; // 中文

        // 3. 调用测试方法
        SpuSaleAttributeVO spuSaleAttributeVO = saleAttributeManageService.getSpuSaleAttributeDetail(spuId, lang);
        System.out.println(JSON.toJSONString(spuSaleAttributeVO));
        // 4. 打印结果
        if (spuSaleAttributeVO != null) {
            System.out.println("===== SPU销售属性详情 =====");

            // 4.1 打印SPU销售属性列表
            List<PropertyVO> spuSalePropertyList = spuSaleAttributeVO.getSpuSalePropertyList();
            if (spuSalePropertyList != null && !spuSalePropertyList.isEmpty()) {
                System.out.println("SPU销售属性数量: " + spuSalePropertyList.size());
                for (PropertyVO propertyVO : spuSalePropertyList) {
                    System.out.println("销售属性ID: " + propertyVO.getAttributeId() +
                            ", 销售属性名称: " + propertyVO.getAttributeName() +
                            ", 销售属性类型: " + propertyVO.getAttributeInputType());

                    // 打印销售属性值列表
                    List<PropertyValueVO> propertyValueVOList = propertyVO.getPropertyValueVOList();
                    if (propertyValueVOList != null && !propertyValueVOList.isEmpty()) {
                        System.out.println("  销售属性值数量: " + propertyValueVOList.size());
                        for (PropertyValueVO propertyValueVO : propertyValueVOList) {
                            System.out.println("  销售属性值ID: " + propertyValueVO.getAttributeValueId() +
                                    ", 销售属性值名称: " + propertyValueVO.getAttributeValueName());
                        }
                    } else {
                        System.out.println("  无销售属性值");
                    }
                }
            } else {
                System.out.println("无SPU销售属性");
            }

            // 4.2 打印SKU销售属性值映射
            Map<Long, List<PropertyValueVO>> skuSalePropertyListMap = spuSaleAttributeVO.getSkuSalePropertyListMap();
            if (skuSalePropertyListMap != null && !skuSalePropertyListMap.isEmpty()) {
                System.out.println("SKU数量: " + skuSalePropertyListMap.size());
                for (Map.Entry<Long, List<PropertyValueVO>> entry : skuSalePropertyListMap.entrySet()) {
                    Long skuId = entry.getKey();
                    List<PropertyValueVO> skuSalePropertyList = entry.getValue();

                    System.out.println("SKU ID: " + skuId);
                    if (skuSalePropertyList != null && !skuSalePropertyList.isEmpty()) {
                        System.out.println("  SKU销售属性值数量: " + skuSalePropertyList.size());
                        for (PropertyValueVO propertyValueVO : skuSalePropertyList) {
                            System.out.println("  销售属性ID: " + propertyValueVO.getAttributeId() +
                                    ", 销售属性值ID: " + propertyValueVO.getAttributeValueId() +
                                    ", 销售属性值名称: " + propertyValueVO.getAttributeValueName());
                        }
                    } else {
                        System.out.println("  无SKU销售属性值");
                    }
                }
            } else {
                System.out.println("无SKU销售属性值映射");
            }

            // 4.3 打印草稿SKU销售属性值映射
            Map<String, List<PropertyValueVO>> draftSkuSalePropertyListMap = spuSaleAttributeVO.getDraftSkuSalePropertyListMap();
            if (draftSkuSalePropertyListMap != null && !draftSkuSalePropertyListMap.isEmpty()) {
                System.out.println("草稿SKU数量: " + draftSkuSalePropertyListMap.size());
                for (Map.Entry<String, List<PropertyValueVO>> entry : draftSkuSalePropertyListMap.entrySet()) {
                    String skuKey = entry.getKey();
                    List<PropertyValueVO> draftSkuSalePropertyList = entry.getValue();

                    System.out.println("SKU Key: " + skuKey);
                    if (draftSkuSalePropertyList != null && !draftSkuSalePropertyList.isEmpty()) {
                        System.out.println("  草稿SKU销售属性值数量: " + draftSkuSalePropertyList.size());
                        for (PropertyValueVO propertyValueVO : draftSkuSalePropertyList) {
                            System.out.println("  销售属性ID: " + propertyValueVO.getAttributeId() +
                                    ", 销售属性值ID: " + propertyValueVO.getAttributeValueId() +
                                    ", 销售属性值名称: " + propertyValueVO.getAttributeValueName());
                        }
                    } else {
                        System.out.println("  无草稿SKU销售属性值");
                    }
                }
            } else {
                System.out.println("无草稿SKU销售属性值映射");
            }
        } else {
            System.out.println("未获取到SPU销售属性详情");
        }
    }

    @Test
    public void createSkuSaleAttributeAndUpdateSkuKeySaleAttributeRelationForLocalSkuDraft() {
        // 2. 创建SKU列表
        List<SkuVO> skuVOList = createTestSkuVOList();

        // 3. 调用测试方法
        saleAttributeManageService.createSkuSaleAttributeForLocalSkuDraft(spuId, skuVOList);
    }

    /**
     * 创建测试用的SKU列表
     * @return SKU列表
     */
    private List<SkuVO> createTestSkuVOList() {
        List<SkuVO> skuVOList = new ArrayList<>();

        // 创建第一个SKU
        SkuVO sku1 = new SkuVO();
        sku1.setSkuKey("TEST_SKU_KEY_001");
        sku1.setStoreSalePropertyList(createSalePropertyList(13001L, 13002L));
        skuVOList.add(sku1);

        // 创建第二个SKU
        SkuVO sku2 = new SkuVO();
        sku2.setSkuKey("TEST_SKU_KEY_002");
        sku2.setStoreSalePropertyList(createSalePropertyList(13001L, 13002L));
        skuVOList.add(sku2);

        return skuVOList;
    }

    /**
     * 创建销售属性列表
     * @param imageAttributeId 图销属性ID
     * @param textAttributeId 文销属性ID
     * @return 销售属性列表
     */
    private List<PropertyValueVO> createSalePropertyList(Long imageAttributeId, Long textAttributeId) {
        List<PropertyValueVO> propertyValueVOList = new ArrayList<>();

        // 创建图销属性值
        PropertyValueVO imagePropertyValue = new PropertyValueVO();
        imagePropertyValue.setAttributeId(imageAttributeId);
        imagePropertyValue.setAttributeValueName("红色");
        imagePropertyValue.setSort(SaleAttributeTypeEnum.IMAGE.getSort());
        imagePropertyValue.setLangList(createMultiLanguageList("红色", "Đỏ", "สีแดง"));
        propertyValueVOList.add(imagePropertyValue);

        // 创建文销属性值
        PropertyValueVO textPropertyValue = new PropertyValueVO();
        textPropertyValue.setAttributeId(textAttributeId);
        textPropertyValue.setAttributeValueName("128GB");
        textPropertyValue.setSort(SaleAttributeTypeEnum.TEXT.getSort());
        textPropertyValue.setLangList(createMultiLanguageList("128GB", "128GB", "128GB"));
        propertyValueVOList.add(textPropertyValue);

        return propertyValueVOList;
    }

    /**
     * 创建多语言列表
     * @param zhName 中文名称
     * @param vnName 越南语名称
     * @param thName 泰语名称
     * @return 多语言列表
     */
    private List<BaseLangVO> createMultiLanguageList(String zhName, String vnName, String thName) {
        List<BaseLangVO> langList = new ArrayList<>();

        // 中文
        BaseLangVO zhLang = new BaseLangVO();
        zhLang.setLang(LangConstant.LANG_ZH);
        zhLang.setLangName(zhName);
        langList.add(zhLang);

        // 越南语
        BaseLangVO vnLang = new BaseLangVO();
        vnLang.setLang(LangConstant.LANG_VN);
        vnLang.setLangName(vnName);
        langList.add(vnLang);

        // 泰语
        BaseLangVO thLang = new BaseLangVO();
        thLang.setLang(LangConstant.LANG_TH);
        thLang.setLangName(thName);
        langList.add(thLang);

        return langList;
    }

    /**
     * 测试将草稿阶段的销售属性关系转换为正式关系
     */
    @Test
    public void updateDraftSaleAttributeRelationsForLocalSkuApprovel() {
        // 1. 创建SKU Key到SKU ID的映射
        Map<String, Long> skuKeyToSkuIdMap = new HashMap<>();
        
        // 添加测试数据
        skuKeyToSkuIdMap.put("TEST_SKU_KEY_001", 3333333333333333333L);
        skuKeyToSkuIdMap.put("TEST_SKU_KEY_002", 4444444444444444444L);
        skuKeyToSkuIdMap.put("TEST_SKU_KEY_003", 5555555555555555555L);
        
        // 2. 打印测试数据
        System.out.println("===== 测试数据 =====");
        for (Map.Entry<String, Long> entry : skuKeyToSkuIdMap.entrySet()) {
            System.out.println("SKU Key: " + entry.getKey() + ", SKU ID: " + entry.getValue());
        }
        
        // 3. 调用测试方法
        Boolean result = saleAttributeManageService.updateDraftSaleAttributeRelationsForLocalSkuApprovel(skuKeyToSkuIdMap);
        
        // 4. 打印结果
        System.out.println("===== 测试结果 =====");
        System.out.println("转换结果: " + (result ? "成功" : "失败"));
    }

    /**
     * 测试通过中台类目ID获取销售属性
     */
    @Test
    public void getCategorySaleAttributesByJdCatId() {

        // 2. 设置语言
        String lang = LangConstant.LANG_ZH; // 中文

        // 3. 调用测试方法
//        List<PropertyVO> propertyVOList = saleAttributeManageService.getCategorySaleAttributesByJdCatId(jdCatId, lang);
        List<PropertyVO> propertyVOList = saleAttributeManageService.getCategorySaleAttributesByJdCatId(17564L, lang);
        // 4. 打印结果
        if (propertyVOList != null && !propertyVOList.isEmpty()) {
            System.out.println("获取到销售属性数量: " + propertyVOList.size());
            for (PropertyVO propertyVO : propertyVOList) {
                System.out.println("销售属性ID: " + propertyVO.getAttributeId() +
                                  ", 销售属性名称: " + propertyVO.getAttributeName() +
                                  ", 销售属性类型: " + propertyVO.getAttributeInputType());
            }
        } else {
            System.out.println("未获取到销售属性");
        }
    }
static {
    Unirest.setTimeouts(0, 0);
}
    @Test
    public void testUnirest() {
        for (int i = 0; i < 10000; i++) {
            try {

                HttpResponse<String> res = Unirest.get("http://wimp-test.jdtest.net").asObject(String.class);
                System.out.println(res.getBody());
                if(i%100==0){
                    System.out.println("i="+i);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    @Test
    public void testGetSkuSaleAttributeDetail() {
        Map<String, Set<String>> needTranslateMap = new HashMap<>();
        needTranslateMap.put("红色", new HashSet<>(Arrays.asList(LangConstant.LANG_VN, LangConstant.LANG_TH)));
        for (int i = 0; i < 100; i++) {
            Map<String, Map<String, String>> propertyVOList = saleAttributeLangManageService.getTranslateResult(needTranslateMap);
            System.out.println(JSON.toJSONString(propertyVOList));
            if(i%20==0){
                System.out.println("i="+i);
            }
        }

    }
}
