package com.jdi.isc.product.soa.service.url;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.outUrl.IscOutUrlApiService;
import com.jdi.isc.product.soa.api.outUrl.req.OutUrlTransformReq;
import com.jdi.isc.product.soa.api.outUrl.res.OutUrlTransformRes;
import com.jdi.isc.product.soa.web.ServiceApplication;
import io.vitess.shaded.com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@Slf4j
@SpringBootTest(classes = ServiceApplication.class)
@AutoConfigureMockMvc
public class IscOutUrlApiServiceTest {

    @Autowired
    private IscOutUrlApiService iscOutUrlApiService;


    @Test
    public void baiduTranslate() {
        OutUrlTransformReq req = new OutUrlTransformReq();
        req.setOutUrl(Sets.newLinkedHashSet(
                Lists.newArrayList("https://gmidistribuidora.agilecdn.com.br/008636-1_1.jpg?v=287-450815903",
                        "https://gmidistribuidora.agilecdn.com.br/12833-1_1.jpg?v=287-1141934846",
                        "https://www.eio.com/cdn/shop/files/pic_ps_3300219_1_b93bb4a5-a0d8-41fc-a16f-5bf5e1ae5271.jpg?v=1734791489",
                        "https://dimensionalstoreb2b.vtexassets.com/arquivos/ids/233258-800-auto?v=638131047642330000&width=800&height=auto&aspect=true")
        ));
        DataResponse<List<OutUrlTransformRes>> res = iscOutUrlApiService.batchTransform(req);
        System.out.println(JSON.toJSONString(res));
    }

}
