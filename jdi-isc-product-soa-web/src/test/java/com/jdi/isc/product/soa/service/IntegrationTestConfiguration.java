package com.jdi.isc.product.soa.service;

import com.jd.jmq.client.springboot.configuration.JmqAutoConfiguration;
import com.jd.laf.config.spring.boot.LafConfigConfiguration;
import com.jd.laf.config.spring.boot.log.LogAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;


@SpringBootApplication(exclude = {
        HibernateJpaAutoConfiguration.class,
        JpaRepositoriesAutoConfiguration.class,
        KafkaAutoConfiguration.class,
//        LafConfigConfiguration.class,
        LogAutoConfiguration.class,
        JmqAutoConfiguration.class,
})
@ComponentScan({"com.jdi.isc.product"})
@EnableConfigurationProperties
public class IntegrationTestConfiguration {


}
