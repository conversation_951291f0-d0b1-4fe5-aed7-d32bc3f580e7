package com.jdi.isc.product.soa.service.manage.saleAttribute.impl;

import lombok.Data;

/**
 * 属性值信息数据传输对象
 * <AUTHOR>
 * @date 2025/5/24
 */
@Data
public class VcPropertyValueDTO {
    /**
     * 属性ID
     */
    private Long attributeId;
    /**
     * 属性值ID
     */
    private Long attributeValueId;
    /**
     * 属性值名
     */
    private String attributeValueName;
    /**
     * 排序字段
     */
    private Integer sort;
    /**
     * 属性值语种 ZH、VN、TH
     */
    private String lang;
    /**
     * 属性值语种 ZH、VN、TH
     */
    private String langName;
    /**
     * 是否选中该属性值。
     */
    private Boolean selected;
    /**
     * 是否必填
     */
    private Boolean required;
    /**
     * 提示语
     */
    private String placeholderValue;
}
