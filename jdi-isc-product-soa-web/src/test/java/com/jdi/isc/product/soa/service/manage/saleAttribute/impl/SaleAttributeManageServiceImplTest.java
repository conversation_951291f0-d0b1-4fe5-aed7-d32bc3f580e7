package com.jdi.isc.product.soa.service.manage.saleAttribute.impl;

import com.jd.gms.greatdane.category.domain.CategorySaleAtt;
import com.jd.gms.greatdane.category.domain.SaleAttribute;
import com.jd.gms.greatdane.category.request.GetCategorySaleAttByCategoryIdsParam;
import com.jd.gms.greatdane.category.service.read.CategorySaleAttReadService;
import com.jd.gms.greatdane.response.GreatDaneResult;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.common.enums.SaleAttributeTypeEnum;
import com.jdi.isc.product.soa.common.enums.YnEnum;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SaleAttributeValueVO;
import com.jdi.isc.product.soa.domain.saleAttribute.biz.SpuSaleAttributeVO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributePO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeValueLangPO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SaleAttributeValuePO;
import com.jdi.isc.product.soa.domain.saleAttribute.po.SkuSaleAttributeValueRelationPO;
import com.jdi.isc.product.soa.domain.sku.biz.SkuVO;
import com.jdi.isc.product.soa.domain.sku.po.SkuPO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyVO;
import com.jdi.isc.product.soa.domain.spu.biz.PropertyValueVO;
import com.jdi.isc.product.soa.domain.common.biz.BaseLangVO;
import com.jdi.isc.product.soa.rpc.gms.BaseGmsClientService;
import com.jdi.isc.product.soa.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SaleAttributeAtomicService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SaleAttributeLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SaleAttributeValueAtomicService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SaleAttributeValueLangAtomicService;
import com.jdi.isc.product.soa.service.atomic.saleAttribute.SkuSaleAttributeValueRelationAtomicService;
import com.jdi.isc.product.soa.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.product.soa.service.manage.saleAttribute.SaleAttributeLangManageService;
import com.jdi.isc.product.soa.service.mapstruct.saleAttribute.SaleAttributeConvertService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SaleAttributeManageService单元测试
 * <AUTHOR>
 * @date 2024-12-19
 */
@ExtendWith(MockitoExtension.class)
class SaleAttributeManageServiceImplTest {

    @InjectMocks
    private SaleAttributeManageServiceImpl saleAttributeManageService;

    @Mock
    private SaleAttributeAtomicService saleAttributeAtomicService;

    @Mock
    private SaleAttributeValueAtomicService saleAttributeValueAtomicService;

    @Mock
    private SkuSaleAttributeValueRelationAtomicService skuSaleAttributeValueRelationAtomicService;

    @Mock
    private SaleAttributeLangAtomicService saleAttributeLangAtomicService;

    @Mock
    private SaleAttributeValueLangAtomicService saleAttributeValueLangAtomicService;

    @Mock
    private SkuAtomicService skuAtomicService;

    @Mock
    private SkuInfoRpcService skuInfoRpcService;

    @Mock
    private SaleAttributeConvertService saleAttributeConvertService;

    @Mock
    private CategorySaleAttReadService categorySaleAttReadService;

    @Mock
    private BaseGmsClientService baseGmsClientService;

    @Mock
    private SaleAttributeLangManageService saleAttributeLangManageService;

    // 测试数据
    private static final Long TEST_SPU_ID = 12345L;
    private static final Long TEST_SKU_ID = 67890L;
    private static final Long TEST_JD_SKU_ID = 98765L;
    private static final Long TEST_JD_CAT_ID = 11111L;
    private static final String TEST_LANG = LangConstant.LANG_EN;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    /**
     * 测试getSpuSaleAttributeDetail方法 - 正常情况
     */
    @Test
    void testGetSpuSaleAttributeDetail_Success() {
        // 准备测试数据
        List<SkuPO> mockSkuList = createMockSkuList();
        List<SkuSaleAttributeValueRelationPO> mockRelations = createMockSkuSaleAttributeRelations();
        List<SaleAttributeValuePO> mockSaleAttributeValues = createMockSaleAttributeValues();
        List<SaleAttributePO> mockSaleAttributes = createMockSaleAttributes();
        Map<String, String> mockAttributeNameLangs = createMockAttributeNameLangs();

        // Mock依赖调用
        when(skuAtomicService.selectSkuPosBySpuId(TEST_SPU_ID)).thenReturn(mockSkuList);
        when(skuSaleAttributeValueRelationAtomicService.getValidBySkuIds(anyList())).thenReturn(mockRelations);
        when(saleAttributeValueAtomicService.getValidByIds(anyList())).thenReturn(mockSaleAttributeValues);
        when(saleAttributeAtomicService.getValidByIdList(anyList())).thenReturn(mockSaleAttributes);
        when(saleAttributeLangManageService.getSaleAttributeNameLangsByCode(anyList(), eq(TEST_LANG)))
                .thenReturn(mockAttributeNameLangs);
        when(saleAttributeValueLangAtomicService.getValidBySaleAttributeValueIds(anyList()))
                .thenReturn(createMockSaleAttributeValueLangPOList());

        // 执行测试
        SpuSaleAttributeVO result = saleAttributeManageService.getSpuSaleAttributeDetail(TEST_SPU_ID, TEST_LANG);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getSpuSalePropertyList());
        assertNotNull(result.getSkuSalePropertyListMap());
        assertTrue(result.getSpuSalePropertyList().size() > 0);

        // 验证Mock调用
        verify(skuAtomicService).selectSkuPosBySpuId(TEST_SPU_ID);
        verify(skuSaleAttributeValueRelationAtomicService).getValidBySkuIds(anyList());
        verify(saleAttributeValueAtomicService).getValidByIds(anyList());
        verify(saleAttributeAtomicService).getValidByIdList(anyList());
    }

    /**
     * 测试getSpuSaleAttributeDetail方法 - SPU下没有SKU
     */
    @Test
    void testGetSpuSaleAttributeDetail_NoSkus() {
        // Mock返回空SKU列表
        when(skuAtomicService.selectSkuPosBySpuId(TEST_SPU_ID)).thenReturn(Collections.emptyList());

        // 执行测试
        SpuSaleAttributeVO result = saleAttributeManageService.getSpuSaleAttributeDetail(TEST_SPU_ID, TEST_LANG);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSpuSalePropertyList().isEmpty());
        assertTrue(result.getSkuSalePropertyListMap().isEmpty());

        // 验证只调用了查询SKU的方法
        verify(skuAtomicService).selectSkuPosBySpuId(TEST_SPU_ID);
        verify(skuSaleAttributeValueRelationAtomicService, never()).getValidBySkuIds(anyList());
    }

    /**
     * 测试getSpuSaleAttributeDetail方法 - 参数为空
     */
    @Test
    void testGetSpuSaleAttributeDetail_NullParams() {
        // 测试spuId为null
        SpuSaleAttributeVO result1 = saleAttributeManageService.getSpuSaleAttributeDetail(null, TEST_LANG);
        assertNull(result1);

        // 测试lang为空
        SpuSaleAttributeVO result2 = saleAttributeManageService.getSpuSaleAttributeDetail(TEST_SPU_ID, null);
        assertNull(result2);

        // 测试lang为空字符串
        SpuSaleAttributeVO result3 = saleAttributeManageService.getSpuSaleAttributeDetail(TEST_SPU_ID, "");
        assertNull(result3);

        // 验证没有调用其他方法
        verify(skuAtomicService, never()).selectSkuPosBySpuId(anyLong());
    }

    /**
     * 测试getCategorySaleAttributesByJdCatId方法 - 正常情况
     */
    @Test
    void testGetCategorySaleAttributesByJdCatId_Success() {
        // 准备测试数据
        Map<Integer, List<CategorySaleAtt>> mockCategorySaleAttMap = new HashMap<>();
        List<CategorySaleAtt> mockCategorySaleAttList = Arrays.asList(createMockCategorySaleAtt());
        mockCategorySaleAttMap.put(TEST_JD_CAT_ID.intValue(), mockCategorySaleAttList);
        
        GreatDaneResult<Map<Integer, List<CategorySaleAtt>>> mockResult = mock(GreatDaneResult.class);
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockCategorySaleAttMap);

        Map<String, String> mockTranslateResult = new HashMap<>();
        mockTranslateResult.put("图销属性1", "Image Sale Attr 1");
        mockTranslateResult.put("文销属性1", "Text Sale Attr 1");

        List<SaleAttributePO> mockExistingAttrs = createMockExistingSaleAttributes();

        // Mock依赖调用
        when(categorySaleAttReadService.getCategorySaleAttListByCategoryIds(any(GetCategorySaleAttByCategoryIdsParam.class)))
                .thenReturn(mockResult);
        when(saleAttributeLangManageService.getLocalTranslateSaleAttributeNamesByLang(anySet(), eq(TEST_LANG)))
                .thenReturn(mockTranslateResult);
        when(saleAttributeAtomicService.getValidByJdCatId(TEST_JD_CAT_ID)).thenReturn(mockExistingAttrs);

        // 执行测试
        List<PropertyVO> result = saleAttributeManageService.getCategorySaleAttributesByJdCatId(TEST_JD_CAT_ID, TEST_LANG);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        PropertyVO imageSaleAttr = result.get(0);
        assertEquals(SaleAttributeTypeEnum.IMAGE.getCode(), imageSaleAttr.getAttributeType());
        assertTrue(imageSaleAttr.getAttributeName().contains("图销属性1"));

        PropertyVO textSaleAttr = result.get(1);
        assertEquals(SaleAttributeTypeEnum.TEXT.getCode(), textSaleAttr.getAttributeType());
        assertTrue(textSaleAttr.getAttributeName().contains("文销属性1"));

        // 验证Mock调用
        verify(categorySaleAttReadService).getCategorySaleAttListByCategoryIds(any());
        verify(saleAttributeLangManageService).getLocalTranslateSaleAttributeNamesByLang(anySet(), eq(TEST_LANG));
        verify(saleAttributeAtomicService).getValidByJdCatId(TEST_JD_CAT_ID);
    }

    /**
     * 测试getCategorySaleAttributesByJdCatId方法 - 中台返回失败
     */
    @Test
    void testGetCategorySaleAttributesByJdCatId_GreatDaneFailure() {
        // Mock中台返回失败
        GreatDaneResult<Map<Integer, List<CategorySaleAtt>>> mockResult = mock(GreatDaneResult.class);
        when(mockResult.isSuccess()).thenReturn(false);

        when(categorySaleAttReadService.getCategorySaleAttListByCategoryIds(any()))
                .thenReturn(mockResult);

        // 执行测试
        List<PropertyVO> result = saleAttributeManageService.getCategorySaleAttributesByJdCatId(TEST_JD_CAT_ID, TEST_LANG);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证只调用了中台接口
        verify(categorySaleAttReadService).getCategorySaleAttListByCategoryIds(any());
        verify(saleAttributeLangManageService, never()).getLocalTranslateSaleAttributeNamesByLang(anySet(), anyString());
    }

    /**
     * 测试saveSkuSaleAttributeForCrossBorder方法 - 正常情况
     */
    @Test
    void testSaveSkuSaleAttributeForCrossBorder_Success() {
        // 准备测试数据
        JdProductDTO mockJdProduct = createMockJdProductDTO();
        List<SaleAttributeValueVO> mockSaleAttributeValues = createMockSaleAttributeValueVOs();

        // Mock依赖调用
        when(skuInfoRpcService.getSkuById(TEST_JD_SKU_ID)).thenReturn(mockJdProduct);
        doNothing().when(saleAttributeLangManageService).createCrossBorderSaleAttributeValueLangs(anyList());

        // 执行测试
        Boolean result = saleAttributeManageService.createSkuSaleAttributeForCrossBorder(TEST_SPU_ID, TEST_SKU_ID, TEST_JD_SKU_ID);

        // 验证结果
        assertTrue(result);

        // 验证Mock调用
        verify(skuInfoRpcService).getSkuById(TEST_JD_SKU_ID);
        verify(saleAttributeLangManageService).createCrossBorderSaleAttributeValueLangs(anyList());
    }

    /**
     * 测试saveSkuSaleAttributeForCrossBorder方法 - 中台SKU不存在
     */
    @Test
    void testCreateSkuSaleAttributeForCrossBorder_JdSkuNotFound() {
        // Mock中台SKU不存在
        when(skuInfoRpcService.getSkuById(TEST_JD_SKU_ID)).thenReturn(null);

        // 执行测试
        Boolean result = saleAttributeManageService.createSkuSaleAttributeForCrossBorder(TEST_SPU_ID, TEST_SKU_ID, TEST_JD_SKU_ID);

        // 验证结果
        assertFalse(result);

        // 验证调用了查询方法但没有调用保存方法
        verify(skuInfoRpcService).getSkuById(TEST_JD_SKU_ID);
        verify(saleAttributeLangManageService, never()).createCrossBorderSaleAttributeValueLangs(anyList());
    }

    /**
     * 测试saveSkuSaleAttributeForCrossBorder方法 - 参数为空
     */
    @Test
    void testSaveSkuSaleAttributeForCrossBorder_NullParams() {
        // 测试skuId为null
        Boolean result1 = saleAttributeManageService.createSkuSaleAttributeForCrossBorder(null, TEST_SKU_ID, TEST_JD_SKU_ID);
        assertFalse(result1);

        // 测试jdSkuId为null
        Boolean result2 = saleAttributeManageService.createSkuSaleAttributeForCrossBorder(TEST_SPU_ID, TEST_SKU_ID, null);
        assertFalse(result2);

        // 验证没有调用其他方法
        verify(skuInfoRpcService, never()).getSkuById(anyLong());
    }

    // ===== 辅助方法：创建Mock测试数据 =====

    private List<SkuPO> createMockSkuList() {
        SkuPO sku1 = new SkuPO();
        sku1.setSkuId(1001L);
        sku1.setSpuId(TEST_SPU_ID);

        SkuPO sku2 = new SkuPO();
        sku2.setSkuId(1002L);
        sku2.setSpuId(TEST_SPU_ID);

        return Arrays.asList(sku1, sku2);
    }

    private List<SkuSaleAttributeValueRelationPO> createMockSkuSaleAttributeRelations() {
        SkuSaleAttributeValueRelationPO relation1 = new SkuSaleAttributeValueRelationPO();
        relation1.setSkuId(1001L);
        relation1.setSaleAttributeValueId(2001L);

        SkuSaleAttributeValueRelationPO relation2 = new SkuSaleAttributeValueRelationPO();
        relation2.setSkuId(1002L);
        relation2.setSaleAttributeValueId(2002L);

        return Arrays.asList(relation1, relation2);
    }

    private List<SaleAttributeValuePO> createMockSaleAttributeValues() {
        SaleAttributeValuePO value1 = new SaleAttributeValuePO();
        value1.setId(2001L);
        value1.setSaleAttributeId(3001L);
        value1.setSaleAttributeValueName("红色");
        value1.setYn(YnEnum.YES.getCode());

        SaleAttributeValuePO value2 = new SaleAttributeValuePO();
        value2.setId(2002L);
        value2.setSaleAttributeId(3001L);
        value2.setSaleAttributeValueName("蓝色");
        value2.setYn(YnEnum.YES.getCode());

        return Arrays.asList(value1, value2);
    }

    private List<SaleAttributePO> createMockSaleAttributes() {
        SaleAttributePO attr1 = new SaleAttributePO();
        attr1.setId(3001L);
        attr1.setSaleAttributeName("颜色");
        attr1.setSaleAttributeType(SaleAttributeTypeEnum.IMAGE.getCode());
        attr1.setYn(YnEnum.YES.getCode());

        return Arrays.asList(attr1);
    }

    private Map<String, String> createMockAttributeNameLangs() {
        Map<String, String> result = new HashMap<>();
        result.put("color", "Color");
        return result;
    }

    private List<SaleAttributeValueLangPO> createMockSaleAttributeValueLangPOList() {
        SaleAttributeValueLangPO lang1 = new SaleAttributeValueLangPO();
        lang1.setSaleAttributeValueId(2001L);
        lang1.setLang(TEST_LANG);
        lang1.setLangName("Red");

        SaleAttributeValueLangPO lang2 = new SaleAttributeValueLangPO();
        lang2.setSaleAttributeValueId(2002L);
        lang2.setLang(TEST_LANG);
        lang2.setLangName("Blue");

        return Arrays.asList(lang1, lang2);
    }

    private CategorySaleAtt createMockCategorySaleAtt() {
        CategorySaleAtt categorySaleAtt = new CategorySaleAtt();
        categorySaleAtt.setCategoryId(TEST_JD_CAT_ID.intValue());

        // 创建图销属性
        SaleAttribute imageSaleAttr1 = new SaleAttribute();
        imageSaleAttr1.setSaleAttName("图销属性1");
        imageSaleAttr1.setColor(true); // 图销

        SaleAttribute imageSaleAttr2 = new SaleAttribute();
        imageSaleAttr2.setSaleAttName("图销属性2");
        imageSaleAttr2.setColor(true); // 图销

        // 创建文销属性
        SaleAttribute textSaleAttr1 = new SaleAttribute();
        textSaleAttr1.setSaleAttName("文销属性1");
        textSaleAttr1.setColor(false); // 文销

        SaleAttribute textSaleAttr2 = new SaleAttribute();
        textSaleAttr2.setSaleAttName("文销属性2");
        textSaleAttr2.setColor(false); // 文销

        categorySaleAtt.setSaleAttribute(imageSaleAttr1);
        return categorySaleAtt;
    }

    private List<SaleAttributePO> createMockExistingSaleAttributes() {
        return new ArrayList<>(); // 返回空列表，表示没有已存在的属性
    }

    private JdProductDTO createMockJdProductDTO() {
        JdProductDTO jdProduct = new JdProductDTO();
        jdProduct.setSkuId(TEST_JD_SKU_ID);
        jdProduct.setSpuId("11111"); // String类型的spuId
        jdProduct.setJdSpuId(11111L); // Long类型的jdSpuId
        jdProduct.setSkuName("测试商品");
        jdProduct.setUnLimitCid(12345); // 设置类目ID

        // 模拟销售属性数据 - 修正为String类型
        jdProduct.setSaleAttributes("[{\"dim\":1,\"saleName\":\"颜色\",\"saleValue\":\"红色\",\"sequenceNo\":1}]");

        return jdProduct;
    }

    private List<SaleAttributeValueVO> createMockSaleAttributeValueVOs() {
        SaleAttributeValueVO valueVO = new SaleAttributeValueVO();
        valueVO.setId(2001L);
        valueVO.setSaleAttributeValueName("红色");
        valueVO.setSaleAttributeId(3001L);
        return Arrays.asList(valueVO);
    }

    // ===== saveSkuSaleAttributeForLocal 测试方法 =====

    /**
     * 测试saveSkuSaleAttributeForLocal方法 - 正常情况
     */
    @Test
    void testSaveSkuSaleAttributeForLocal_Success() {
        // 准备测试数据
        List<SkuVO> mockSkuVOList = createMockSkuVOList();
        List<SaleAttributePO> mockExistingSaleAttributes = createMockExistingSaleAttributesForLocal();
        List<SaleAttributeValuePO> mockSavedSaleAttributeValues = createMockSavedSaleAttributeValues();

        // Mock依赖调用
        when(saleAttributeAtomicService.getValidByIdList(anyList())).thenReturn(mockExistingSaleAttributes);
        when(saleAttributeValueAtomicService.saveWithSortAndReturnWithId(anyLong(), anyList()))
                .thenReturn(mockSavedSaleAttributeValues);
        when(saleAttributeValueLangAtomicService.saveBatch(anyList())).thenReturn(true);
        when(skuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuKey(anyMap()))
                .thenReturn(true);

        // 执行测试
        Boolean result = saleAttributeManageService.createSkuSaleAttributeForLocalSkuDraft(TEST_SPU_ID, mockSkuVOList);

        // 验证结果
        assertTrue(result);

        // 验证Mock调用
        verify(saleAttributeAtomicService).getValidByIdList(anyList());
        verify(saleAttributeValueAtomicService, atLeastOnce()).saveWithSortAndReturnWithId(anyLong(), anyList());
        verify(saleAttributeValueLangAtomicService).saveBatch(anyList());
        verify(skuSaleAttributeValueRelationAtomicService).batchSaveSkuAttributeValueRelationListForSkuKey(anyMap());
    }

    /**
     * 测试saveSkuSaleAttributeForLocal方法 - 参数为空
     */
    @Test
    void testSaveSkuSaleAttributeForLocal_NullParams() {
        // 测试null参数
        Boolean result1 = saleAttributeManageService.createSkuSaleAttributeForLocalSkuDraft(null, null);
        assertFalse(result1);

        // 测试空列表
        Boolean result2 = saleAttributeManageService.createSkuSaleAttributeForLocalSkuDraft(TEST_SPU_ID, Collections.emptyList());
        assertFalse(result2);

        // 验证没有调用其他方法
        verify(saleAttributeAtomicService, never()).getValidByIdList(anyList());
        verify(saleAttributeValueAtomicService, never()).saveWithSortAndReturnWithId(anyLong(), anyList());
    }

    /**
     * 测试saveSkuSaleAttributeForLocal方法 - SKU没有销售属性
     */
    @Test
    void testSaveSkuSaleAttributeForLocal_NoSaleProperties() {
        // 准备测试数据 - SKU没有销售属性
        List<SkuVO> mockSkuVOList = createMockSkuVOListWithoutSaleProperties();

        // 执行测试
        Boolean result = saleAttributeManageService.createSkuSaleAttributeForLocalSkuDraft(TEST_SPU_ID, mockSkuVOList);

        // 验证结果
        assertFalse(result);

        // 验证没有调用保存相关方法
        verify(saleAttributeAtomicService, never()).getValidByIdList(anyList());
        verify(saleAttributeValueAtomicService, never()).saveWithSortAndReturnWithId(anyLong(), anyList());
    }

    /**
     * 测试saveSkuSaleAttributeForLocal方法 - 销售属性不存在
     */
    @Test
    void testSaveSkuSaleAttributeForLocal_SaleAttributeNotExist() {
        // 准备测试数据
        List<SkuVO> mockSkuVOList = createMockSkuVOList();

        // Mock销售属性不存在
        when(saleAttributeAtomicService.getValidByIdList(anyList())).thenReturn(Collections.emptyList());

        // 执行测试
        Boolean result = saleAttributeManageService.createSkuSaleAttributeForLocalSkuDraft(TEST_SPU_ID, mockSkuVOList);

        // 验证结果
        assertFalse(result);

        // 验证调用了查询方法但没有调用保存方法
        verify(saleAttributeAtomicService).getValidByIdList(anyList());
        verify(saleAttributeValueAtomicService, never()).saveWithSortAndReturnWithId(anyLong(), anyList());
    }

    /**
     * 测试saveSkuSaleAttributeForLocal方法 - 销售属性值保存失败
     */
    @Test
    void testSaveSkuSaleAttributeForLocal_SaveAttributeValuesFailed() {
        // 准备测试数据
        List<SkuVO> mockSkuVOList = createMockSkuVOList();
        List<SaleAttributePO> mockExistingSaleAttributes = createMockExistingSaleAttributesForLocal();

        // Mock销售属性存在但保存属性值失败
        when(saleAttributeAtomicService.getValidByIdList(anyList())).thenReturn(mockExistingSaleAttributes);
        when(saleAttributeValueAtomicService.saveWithSortAndReturnWithId(anyLong(), anyList()))
                .thenReturn(Collections.emptyList()); // 返回空列表表示保存失败

        // 执行测试
        Boolean result = saleAttributeManageService.createSkuSaleAttributeForLocalSkuDraft(TEST_SPU_ID, mockSkuVOList);

        // 验证结果
        assertFalse(result);

        // 验证调用了相关方法
        verify(saleAttributeAtomicService).getValidByIdList(anyList());
        verify(saleAttributeValueAtomicService, atLeastOnce()).saveWithSortAndReturnWithId(anyLong(), anyList());
        verify(skuSaleAttributeValueRelationAtomicService, never()).batchSaveSkuAttributeValueRelationListForSkuKey(anyMap());
    }

    /**
     * 测试saveSkuSaleAttributeForLocal方法 - 多语言保存失败但不影响主流程
     */
    @Test
    void testSaveSkuSaleAttributeForLocal_LangSaveFailedButContinue() {
        // 准备测试数据
        List<SkuVO> mockSkuVOList = createMockSkuVOList();
        List<SaleAttributePO> mockExistingSaleAttributes = createMockExistingSaleAttributesForLocal();
        List<SaleAttributeValuePO> mockSavedSaleAttributeValues = createMockSavedSaleAttributeValues();

        // Mock销售属性值保存成功，但多语言保存失败
        when(saleAttributeAtomicService.getValidByIdList(anyList())).thenReturn(mockExistingSaleAttributes);
        when(saleAttributeValueAtomicService.saveWithSortAndReturnWithId(anyLong(), anyList()))
                .thenReturn(mockSavedSaleAttributeValues);
        when(saleAttributeValueLangAtomicService.saveBatch(anyList()))
                .thenThrow(new RuntimeException("多语言保存失败"));
        when(skuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuKey(anyMap()))
                .thenReturn(true);

        // 执行测试
        Boolean result = saleAttributeManageService.createSkuSaleAttributeForLocalSkuDraft(TEST_SPU_ID, mockSkuVOList);

        // 验证结果 - 多语言失败不应影响主流程
        assertTrue(result);

        // 验证调用了相关方法
        verify(saleAttributeAtomicService).getValidByIdList(anyList());
        verify(saleAttributeValueAtomicService, atLeastOnce()).saveWithSortAndReturnWithId(anyLong(), anyList());
        verify(saleAttributeValueLangAtomicService).saveBatch(anyList());
        verify(skuSaleAttributeValueRelationAtomicService).batchSaveSkuAttributeValueRelationListForSkuKey(anyMap());
    }

    /**
     * 测试saveSkuSaleAttributeForLocal方法 - 批量关联关系保存失败
     */
    @Test
    void testSaveSkuSaleAttributeForLocal_BatchRelationSaveFailed() {
        // 准备测试数据
        List<SkuVO> mockSkuVOList = createMockSkuVOList();
        List<SaleAttributePO> mockExistingSaleAttributes = createMockExistingSaleAttributesForLocal();
        List<SaleAttributeValuePO> mockSavedSaleAttributeValues = createMockSavedSaleAttributeValues();

        // Mock前面都成功，但批量关联关系保存失败
        when(saleAttributeAtomicService.getValidByIdList(anyList())).thenReturn(mockExistingSaleAttributes);
        when(saleAttributeValueAtomicService.saveWithSortAndReturnWithId(anyLong(), anyList()))
                .thenReturn(mockSavedSaleAttributeValues);
        when(saleAttributeValueLangAtomicService.saveBatch(anyList())).thenReturn(true);
        when(skuSaleAttributeValueRelationAtomicService.batchSaveSkuAttributeValueRelationListForSkuKey(anyMap()))
                .thenReturn(false);

        // 执行测试
        Boolean result = saleAttributeManageService.createSkuSaleAttributeForLocalSkuDraft(TEST_SPU_ID, mockSkuVOList);

        // 验证结果
        assertFalse(result);

        // 验证调用了相关方法
        verify(saleAttributeAtomicService).getValidByIdList(anyList());
        verify(saleAttributeValueAtomicService, atLeastOnce()).saveWithSortAndReturnWithId(anyLong(), anyList());
        verify(saleAttributeValueLangAtomicService).saveBatch(anyList());
        verify(skuSaleAttributeValueRelationAtomicService).batchSaveSkuAttributeValueRelationListForSkuKey(anyMap());
    }

    /**
     * 测试saveSkuSaleAttributeForLocal方法 - 没有中文多语言信息
     */
    @Test
    void testSaveSkuSaleAttributeForLocal_NoChineseLangInfo() {
        // 准备测试数据 - 没有中文多语言信息
        List<SkuVO> mockSkuVOList = createMockSkuVOListWithoutChineseLang();

        // 执行测试
        Boolean result = saleAttributeManageService.createSkuSaleAttributeForLocalSkuDraft(TEST_SPU_ID, mockSkuVOList);

        // 验证结果
        assertFalse(result);

        // 验证没有调用保存相关方法
        verify(saleAttributeAtomicService, never()).getValidByIdList(anyList());
        verify(saleAttributeValueAtomicService, never()).saveWithSortAndReturnWithId(anyLong(), anyList());
    }

    // ===== 辅助方法：创建saveSkuSaleAttributeForLocal测试数据 =====

    /**
     * 创建包含销售属性的SKU测试数据
     */
    private List<SkuVO> createMockSkuVOList() {
        SkuVO skuVO1 = new SkuVO();
        skuVO1.setSkuId(1001L);
        skuVO1.setStoreSalePropertyList(createMockPropertyValueVOListWithLangs(1001L));

        SkuVO skuVO2 = new SkuVO();
        skuVO2.setSkuId(1002L);
        skuVO2.setStoreSalePropertyList(createMockPropertyValueVOListWithLangs(1002L));

        return Arrays.asList(skuVO1, skuVO2);
    }

    /**
     * 创建没有销售属性的SKU测试数据
     */
    private List<SkuVO> createMockSkuVOListWithoutSaleProperties() {
        SkuVO skuVO1 = new SkuVO();
        skuVO1.setSkuId(1001L);
        skuVO1.setStoreSalePropertyList(Collections.emptyList());

        SkuVO skuVO2 = new SkuVO();
        skuVO2.setSkuId(1002L);
        skuVO2.setStoreSalePropertyList(null);

        SkuVO skuVO3 = new SkuVO();
        skuVO3.setSkuId(null); // SKU ID为空

        return Arrays.asList(skuVO1, skuVO2, skuVO3);
    }

    /**
     * 创建没有中文多语言信息的SKU测试数据
     */
    private List<SkuVO> createMockSkuVOListWithoutChineseLang() {
        SkuVO skuVO1 = new SkuVO();
        skuVO1.setSkuId(1001L);
        skuVO1.setStoreSalePropertyList(createMockPropertyValueVOListWithoutChineseLang());

        return Arrays.asList(skuVO1);
    }

    /**
     * 创建包含多语言信息的PropertyValueVO列表
     */
    private List<PropertyValueVO> createMockPropertyValueVOListWithLangs(Long skuId) {
        PropertyValueVO propertyValueVO1 = new PropertyValueVO();
        propertyValueVO1.setAttributeId(3001L);
        propertyValueVO1.setLangList(createMockBaseLangVOListWithChinese("红色"));

        PropertyValueVO propertyValueVO2 = new PropertyValueVO();
        propertyValueVO2.setAttributeId(3002L);
        propertyValueVO2.setLangList(createMockBaseLangVOListWithChinese("大"));

        return Arrays.asList(propertyValueVO1, propertyValueVO2);
    }

    /**
     * 创建没有中文多语言信息的PropertyValueVO列表
     */
    private List<PropertyValueVO> createMockPropertyValueVOListWithoutChineseLang() {
        PropertyValueVO propertyValueVO1 = new PropertyValueVO();
        propertyValueVO1.setAttributeId(3001L);
        propertyValueVO1.setAttributeValueId(2001L);
        propertyValueVO1.setLangList(createMockBaseLangVOListWithoutChinese());

        return Arrays.asList(propertyValueVO1);
    }

    /**
     * 创建包含中文的多语言列表
     */
    private List<BaseLangVO> createMockBaseLangVOListWithChinese(String chineseName) {
        BaseLangVO chineseLang = new BaseLangVO();
        chineseLang.setLang(LangConstant.LANG_ZH);
        chineseLang.setLangName(chineseName);

        BaseLangVO englishLang = new BaseLangVO();
        englishLang.setLang(LangConstant.LANG_EN);
        englishLang.setLangName("Red".equals(chineseName) ? "Red" : "大".equals(chineseName) ? "Large" : "Unknown");

        BaseLangVO thaiLang = new BaseLangVO();
        thaiLang.setLang("TH");
        thaiLang.setLangName("Red".equals(chineseName) ? "แดง" : "大".equals(chineseName) ? "ใหญ่" : "ไม่ทราบ");

        return Arrays.asList(chineseLang, englishLang, thaiLang);
    }

    /**
     * 创建不包含中文的多语言列表
     */
    private List<BaseLangVO> createMockBaseLangVOListWithoutChinese() {
        BaseLangVO englishLang = new BaseLangVO();
        englishLang.setLang(LangConstant.LANG_EN);
        englishLang.setLangName("Red");

        BaseLangVO thaiLang = new BaseLangVO();
        thaiLang.setLang("TH");
        thaiLang.setLangName("แดง");

        return Arrays.asList(englishLang, thaiLang);
    }

    /**
     * 创建测试用的已存在销售属性列表
     */
    private List<SaleAttributePO> createMockExistingSaleAttributesForLocal() {
        SaleAttributePO attr1 = new SaleAttributePO();
        attr1.setId(3001L);
        attr1.setSaleAttributeName("颜色");
        attr1.setSaleAttributeType(SaleAttributeTypeEnum.IMAGE.getCode());
        attr1.setYn(YnEnum.YES.getCode());

        SaleAttributePO attr2 = new SaleAttributePO();
        attr2.setId(3002L);
        attr2.setSaleAttributeName("尺寸");
        attr2.setSaleAttributeType(SaleAttributeTypeEnum.TEXT.getCode());
        attr2.setYn(YnEnum.YES.getCode());

        return Arrays.asList(attr1, attr2);
    }

    /**
     * 创建测试用的已保存销售属性值列表
     */
    private List<SaleAttributeValuePO> createMockSavedSaleAttributeValues() {
        SaleAttributeValuePO value1 = new SaleAttributeValuePO();
        value1.setId(2001L);
        value1.setSaleAttributeId(3001L);
        value1.setSaleAttributeValueName("红色");
        value1.setYn(YnEnum.YES.getCode());

        SaleAttributeValuePO value2 = new SaleAttributeValuePO();
        value2.setId(2002L);
        value2.setSaleAttributeId(3002L);
        value2.setSaleAttributeValueName("大");
        value2.setYn(YnEnum.YES.getCode());

        return Arrays.asList(value1, value2);
    }
} 