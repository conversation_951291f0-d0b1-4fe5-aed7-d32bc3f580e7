package com.jdi.isc.product.soa.service.stock;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.devOps.DevOpsApiService;
import com.jdi.isc.product.soa.api.stock.IscProductSoaStockReadApiService;
import com.jdi.isc.product.soa.api.stock.IscProductSoaStockWriteApiService;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockSplitReqDTO;
import com.jdi.isc.product.soa.api.stock.res.StockOccupyResDTO;
import com.jdi.isc.product.soa.api.stock.res.StockResDTO;
import com.jdi.isc.product.soa.service.atomic.stock.StockAtomicService;
import com.jdi.isc.product.soa.service.atomic.stocklog.StockLogAtomicService;
import com.jdi.isc.product.soa.service.manage.stock.StockManageService;
import com.jdi.isc.product.soa.web.ServiceApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@SpringBootTest(classes = ServiceApplication.class)
@AutoConfigureMockMvc
public class StockTest {

    @Resource
    private StockManageService stockManageService;
    @Autowired
    private IscProductSoaStockWriteApiService iscProductSoaStockWriteApiService;
    @Autowired
    private IscProductSoaStockReadApiService iscProductSoaStockReadApiService;
    @Resource
    private StockLogAtomicService stockLogAtomicService;
    @Resource
    private StockAtomicService stockAtomicService;
    @Autowired
    private DevOpsApiService devOpsApiService;

    @Test
    public void saveOrUpdate() throws Exception {
        List<StockItemManageReqDTO> stockItem = new ArrayList<>();
        StockItemManageReqDTO item = new StockItemManageReqDTO();

//        item.setSkuId(80000000039L);
//        item.setNum(10L);
////        item.setWarehouseId("1000");
//        item.setUpdater("yuyang63");

        StockItemManageReqDTO item2 = new StockItemManageReqDTO();
        item2.setSkuId(80000000042L);
        item2.setNum(10L);
        item2.setWarehouseId("1090");
        item2.setUpdater("yuyang63");


//        stockItem.add(item);
        stockItem.add(item2);

        String bizNo = UUID.randomUUID().toString();
        StockManageReqDTO req = new StockManageReqDTO();
        req.setBizNo(bizNo);
//        req.setOrderId(bizNo);
        req.setStockItem(stockItem);
        DataResponse<Boolean> res = iscProductSoaStockWriteApiService.saveOrUpdate(req);
        System.out.println(" >>>>>>>>>>>>>> " + res);
    }
    @Test
    public void append() throws Exception {
        List<StockItemManageReqDTO> stockItem = new ArrayList<>();
        StockItemManageReqDTO item = new StockItemManageReqDTO();

        item.setSkuId(80000059565L);
        item.setNum(8L);
        item.setUpdater("yuyang63");


        stockItem.add(item);

        String bizNo = UUID.randomUUID().toString();
        StockManageReqDTO req = new StockManageReqDTO();
        req.setBizNo(bizNo);
        req.setStockItem(stockItem);
        req.setStockWriteType(1);
        DataResponse<Boolean> res = iscProductSoaStockWriteApiService.saveOrUpdate(req);
        System.out.println(" >>>>>>>>>>>>>> " + res);
    }

    @Test
    public void list() throws Exception {
        List<StockItemManageReqDTO> stockItem = new ArrayList<>();
        StockItemManageReqDTO item = new StockItemManageReqDTO();
        item.setSkuId(80000059566L);
        item.setNum(16L);
        item.setUpdater("yuyang63");
        StockItemManageReqDTO item2 = new StockItemManageReqDTO();
        item2.setSkuId(80000059565L);
//        item.setNum(16L);
        item2.setUpdater("yuyang63");
        stockItem.add(item);
        stockItem.add(item2);


        StockManageReqDTO req = new StockManageReqDTO();
        req.setStockItem(stockItem);


        DataResponse<Map<Long, StockResDTO>> res = iscProductSoaStockReadApiService.getStock(req);
        System.out.println(" >>>>>>>>>>>>>> " + JSON.toJSONString(res));
    }


    @Test
    public void occupy() throws Exception {
        List<StockItemManageReqDTO> stockItem = new ArrayList<>();
        StockItemManageReqDTO item = new StockItemManageReqDTO();
        item.setSkuId(80000059566L);
        item.setNum(4L);
        item.setUpdater("yuyang63");

        StockItemManageReqDTO item2 = new StockItemManageReqDTO();
        item2.setSkuId(80000059565L);
        item2.setNum(4L);
        item2.setWarehouseId("1001");
        item2.setUpdater("yuyang63");

        stockItem.add(item);
        stockItem.add(item2);

        String bizNo = UUID.randomUUID().toString();
        StockManageReqDTO req = new StockManageReqDTO();
        req.setBizNo(bizNo);
        req.setOrderId(bizNo);
        req.setStockItem(stockItem);
        DataResponse<Boolean> res = iscProductSoaStockWriteApiService.occupy(req);

        System.out.println(" >>>>>>>>>>>>>> " + res);
    }

    @Test
    public void release()  {
        List<StockItemManageReqDTO> stockItem = new ArrayList<>();
        StockItemManageReqDTO item = new StockItemManageReqDTO();
        item.setSkuId(80000059566L);
        item.setNum(4L);
        item.setUpdater("yuyang63");

        StockItemManageReqDTO item2 = new StockItemManageReqDTO();
        item2.setSkuId(80000059565L);
        item2.setNum(4L);
        item2.setUpdater("yuyang63");

        stockItem.add(item);
        stockItem.add(item2);

        String bizNo = "575c3f41-5caa-4e57-92a7-5298bf1198bb";

        StockManageReqDTO req = new StockManageReqDTO();
        req.setBizNo(bizNo);
        req.setOrderId(bizNo);
        req.setStockItem(stockItem);
        DataResponse<Boolean> res = iscProductSoaStockWriteApiService.release(req);

        System.out.println(" 预占释放结果>>>>>>>>>>>>>> " + JSON.toJSONString(res));
    }


    @Test
    public void occupyReturn() {
        List<StockItemManageReqDTO> stockItem = new ArrayList<>();
        StockItemManageReqDTO item = new StockItemManageReqDTO();
        item.setSkuId(80000059566L);
        item.setNum(4L);
        item.setUpdater("yuyang63");

        StockItemManageReqDTO item2 = new StockItemManageReqDTO();
        item2.setSkuId(80000059565L);
        item2.setNum(4L);
        item2.setUpdater("yuyang63");

        stockItem.add(item);
        stockItem.add(item2);

        String bizNo = "e1e626cb-b157-480f-916d-d6af2998457d";

        StockManageReqDTO req = new StockManageReqDTO();
        req.setBizNo(bizNo);
        req.setOrderId(bizNo);
        req.setStockItem(stockItem);
        DataResponse<Boolean> res = iscProductSoaStockWriteApiService.occupyReturn(req);

        System.out.println(" 预占退回结果>>>>>>>>>>>>>> " + JSON.toJSONString(res));
    }

    @Test
    public void stockReturn() {
        List<StockItemManageReqDTO> stockItem = new ArrayList<>();
        StockItemManageReqDTO item = new StockItemManageReqDTO();
        item.setSkuId(80000059566L);
        item.setNum(4L);
        item.setUpdater("yuyang63");

        StockItemManageReqDTO item2 = new StockItemManageReqDTO();
        item2.setSkuId(80000059565L);
        item2.setNum(4L);
        item2.setUpdater("yuyang63");

        stockItem.add(item);
//        stockItem.add(item2);

        StockManageReqDTO req = new StockManageReqDTO();
        req.setOrderId("8f24ade6-c6bf-4b5b-8a3e-b90710b7714d");
        req.setBizNo("8f24ade6-c6bf-4b5b-8a3e-b90710b7714e");
        req.setStockItem(stockItem);
        DataResponse<Boolean> res = iscProductSoaStockWriteApiService.stockReturn(req);

        System.out.println(" 现货回退(订单取消或售后)>>>>>>>>>>>>>> " + JSON.toJSONString(res));
    }

    @Test
    public void truncate() {
        DataResponse<Integer> res = stockManageService.truncate();
        int res2 = stockLogAtomicService.truncate();
        System.out.println(" 清空结果>>>>>>>>>>>>>> " + JSON.toJSONString(res)+"-"+res2);
    }


    @Test
    public void sumStock(){
        long stockSum = stockAtomicService.getStockCountByWarehouseId(1L);
        System.out.println(stockSum);
    }

    @Test
    public void saveSkuStock() {
        StockManageReqDTO reqDTO = new StockManageReqDTO();
        reqDTO.setBizNo(UUID.randomUUID().toString());

        List<StockItemManageReqDTO> stockItemManageReqDTOList = Lists.newArrayList();
        stockItemManageReqDTOList.add(new StockItemManageReqDTO(80000000045L,10L,"sunlei61",null));
        reqDTO.setStockItem(stockItemManageReqDTOList);
        DataResponse<Boolean> dataResponse = stockManageService.saveOrUpdate(reqDTO);
        System.out.println(JSON.toJSONString(dataResponse));
    }

    @Test
    public void initStock() {
        //DataResponse<String> dataResponse = devOpsApiService.initStockData("spu");
        //System.out.println(JSON.toJSONString(dataResponse));
    }

    @Test
    public void getStock() {
        StockManageReqDTO reqDTO = new StockManageReqDTO();
        //reqDTO.setCountryCode(CountryConstant.COUNTRY_ZH);
        List<StockItemManageReqDTO> stockItem = Lists.newArrayList();
        StockItemManageReqDTO itemManageReqDTO = new StockItemManageReqDTO();
        itemManageReqDTO.setSkuId(80000000025L);
        itemManageReqDTO.setNum(1L);
        stockItem.add(itemManageReqDTO);
        reqDTO.setStockItem(stockItem);
        Map<Long, StockResDTO> stockResDTOMap = stockManageService.getStock(reqDTO);
        System.out.println(JSON.toJSONString(stockResDTOMap));



    }

    /**增加在途库存**/
    @Test
    public void appendTransitStock() {
        StockManageReqDTO reqDTO = new StockManageReqDTO();
        List<StockItemManageReqDTO> stockItem = Lists.newArrayList();
        StockItemManageReqDTO itemManageReqDTO = new StockItemManageReqDTO();
        itemManageReqDTO.setSkuId(80000000138L);
        itemManageReqDTO.setNum(5L);
        itemManageReqDTO.setWarehouseId("1015");
        itemManageReqDTO.setUpdater("xubing82");
        stockItem.add(itemManageReqDTO);
        reqDTO.setStockItem(stockItem);
        reqDTO.setStockWriteType(2);
        reqDTO.setOperator("xubing82");
        reqDTO.setOrderId("S250************");
        reqDTO.setBizNo("S250************");
        DataResponse<Boolean> dataResponse = stockManageService.saveOrUpdate(reqDTO);
        System.out.println(dataResponse);
    }

    /**在途库存回退**/
    @Test
    public void returnTransitStock() {
        StockManageReqDTO reqDTO = new StockManageReqDTO();
        List<StockItemManageReqDTO> stockItem = Lists.newArrayList();
        StockItemManageReqDTO itemManageReqDTO = new StockItemManageReqDTO();
        itemManageReqDTO.setSkuId(80000000025L);
        itemManageReqDTO.setNum(100L);
        itemManageReqDTO.setWarehouseId("1001");
        itemManageReqDTO.setUpdater("sunlei61");
        stockItem.add(itemManageReqDTO);
        reqDTO.setStockItem(stockItem);
        reqDTO.setBizNo("S20250310000001");
        DataResponse<Boolean> dataResponse = stockManageService.transitStockReturn(reqDTO);
        System.out.println(dataResponse);
    }

    /**在途库存转现货**/
    @Test
    public void transitStockTransferStock() {
        StockManageReqDTO reqDTO = new StockManageReqDTO();
        List<StockItemManageReqDTO> stockItem = Lists.newArrayList();
        StockItemManageReqDTO itemManageReqDTO = new StockItemManageReqDTO();
        itemManageReqDTO.setSkuId(80000000025L);
        itemManageReqDTO.setNum(2L);
        itemManageReqDTO.setWarehouseId("1001");
        itemManageReqDTO.setUpdater("sunlei61");
        stockItem.add(itemManageReqDTO);
        reqDTO.setStockItem(stockItem);
        reqDTO.setBizNo("S20250310000001");
        DataResponse<Boolean> dataResponse = stockManageService.transitStockToStock(reqDTO);
        System.out.println(dataResponse);
    }

    private StockManageReqDTO getWarehouseStockManageReqDTO() {
        StockManageReqDTO reqDTO = new StockManageReqDTO();
        List<StockItemManageReqDTO> stockItem = Lists.newArrayList();
        StockItemManageReqDTO itemManageReqDTO = new StockItemManageReqDTO();
        itemManageReqDTO.setSkuId(80000022153L);
        itemManageReqDTO.setNum(1L);
        itemManageReqDTO.setWarehouseId("1014");
        itemManageReqDTO.setUpdater("BR-BYD采购账号");

        stockItem.add(itemManageReqDTO);
        reqDTO.setStockItem(stockItem);
        reqDTO.setBizNo("250121211000007");
        reqDTO.setOrderId("0");
        return reqDTO;
    }

    /**新预占库存-备货仓**/
    @Test
    public void occupyStockWarehouse() {
        StockManageReqDTO reqDTO = getWarehouseStockManageReqDTO();
        reqDTO.setStockPriority(1);
        DataResponse<StockOccupyResDTO> response = stockManageService.occupyStock(reqDTO);
        System.out.println(response);
    }


    /**新释放库存-备货仓**/
    @Test
    public void releaseStockWarehouse() {
        StockManageReqDTO reqDTO = getWarehouseStockManageReqDTO();
        DataResponse<Boolean> dataResponse = stockManageService.releaseStock(reqDTO);
        System.out.println(JSON.toJSONString(dataResponse));
    }

    /**备货库存预占回退*/
    @Test
    public void occupyStockReturnWarehouse(){
        StockManageReqDTO reqDTO = getWarehouseStockManageReqDTO();
        reqDTO.setStockPriority(1);
        DataResponse<Boolean> response = stockManageService.occupyStockReturn(reqDTO);
        System.out.println(JSON.toJSONString(response));
    }

    /**新现货回退接口-备货仓**/
    @Test
    public void stockNewReturnWarehouse() {
        StockManageReqDTO reqDTO = getWarehouseStockManageReqDTO();
        DataResponse<Boolean> dataResponse = stockManageService.stockNewReturn(reqDTO);
        System.out.println(JSON.toJSONString(dataResponse));
    }

    /**在途预占转现货-备货仓**/
    @Test
    public void occupyRelease() {
        StockManageReqDTO reqDTO = getWarehouseStockManageReqDTO();
        reqDTO.setStockPriority(1);
        DataResponse<StockOccupyResDTO> dataResponse = stockManageService.occupyRelease(reqDTO);
        System.out.println(JSON.toJSONString(dataResponse));
    }

    @Test
    public void splitPurchaseOrder() {
        StockSplitReqDTO reqDTO = new StockSplitReqDTO();
        List<StockManageReqDTO> stockManageReqDTOList = Lists.newArrayList();
        StockManageReqDTO order1 = new StockManageReqDTO();
        order1.setOrderId("S250321235000001");
        order1.setBizNo("S250321235000002");
        List<StockItemManageReqDTO> stockItem1 = Lists.newArrayList();
        stockItem1.add(new StockItemManageReqDTO(80000096870L,2L,"zhaowei67",null));
        order1.setStockItem(stockItem1);
        stockManageReqDTOList.add(order1);


        StockManageReqDTO order2 = new StockManageReqDTO();
        order2.setOrderId("S250321235000001");
        order2.setBizNo("S250321235000002");
//        order2.setBizNo("S250325112900001");
        List<StockItemManageReqDTO> stockItem2 = Lists.newArrayList();
        stockItem2.add(new StockItemManageReqDTO(80000157246L,2L,"zhaowei67",null));
        order2.setStockItem(stockItem2);
        stockManageReqDTOList.add(order2);

/*        StockManageReqDTO order3 = new StockManageReqDTO();
        order3.setOrderId("S250307181500003");
        order3.setBizNo("S250325112900002");
        List<StockItemManageReqDTO> stockItem3 = Lists.newArrayList();
        stockItem3.add(new StockItemManageReqDTO(80000000045L,2L,"sunlei61",null));
        order3.setStockItem(stockItem3);
        stockManageReqDTOList.add(order3);

        StockManageReqDTO order4 = new StockManageReqDTO();
        order4.setOrderId("S250307181500001");
        order4.setBizNo("S250307181500004");
        List<StockItemManageReqDTO> stockItem4 = Lists.newArrayList();
        stockItem4.add(new StockItemManageReqDTO(80000000045L,2L,"sunlei61",null));
        order4.setStockItem(stockItem4);
        stockManageReqDTOList.add(order4);*/

        reqDTO.setStockManageReqDTOList(stockManageReqDTOList);
        System.out.println(JSON.toJSONString(reqDTO));
//        DataResponse<Boolean> dataResponse = stockManageService.splitPurchaseOrderStock(reqDTO);
//        System.out.println(JSON.toJSONString(dataResponse));
    }

    private StockManageReqDTO getStockManageReqDTO() {
        StockManageReqDTO reqDTO = new StockManageReqDTO();
        List<StockItemManageReqDTO> stockItem = Lists.newArrayList();
        StockItemManageReqDTO itemManageReqDTO = new StockItemManageReqDTO();
        itemManageReqDTO.setSkuId(80000000045L);
        itemManageReqDTO.setNum(10L);
        itemManageReqDTO.setUpdater("sunlei61");
        stockItem.add(itemManageReqDTO);
        reqDTO.setStockItem(stockItem);
        reqDTO.setBizNo("S250307181500001");
        reqDTO.setOrderId("S250307181500001");
        return reqDTO;
    }

    /**厂直库存预占*/
    @Test
    public void occupyStock(){
        StockManageReqDTO reqDTO = getStockManageReqDTO();
        DataResponse<StockOccupyResDTO> response = stockManageService.occupyStock(reqDTO);
        System.out.println(JSON.toJSONString(response));
    }

    /**厂直库存预占回退*/
    @Test
    public void occupyStockReturn(){
        StockManageReqDTO reqDTO = getStockManageReqDTO();
        DataResponse<Boolean> response = stockManageService.occupyStockReturn(reqDTO);
        System.out.println(JSON.toJSONString(response));
    }

    /**厂直库存扣减*/
    @Test
    public void releaseStock(){
        StockManageReqDTO reqDTO = getStockManageReqDTO();
        DataResponse<Boolean> response = stockManageService.releaseStock(reqDTO);
        System.out.println(JSON.toJSONString(response));
    }

    /**厂直库存现货回退*/
    @Test
    public void stockNewReturn(){
        StockManageReqDTO reqDTO = getStockManageReqDTO();
        DataResponse<Boolean> response = stockManageService.stockNewReturn(reqDTO);
        System.out.println(JSON.toJSONString(response));
    }

    /**
     * 前置条件：线上通过ai导数据到测试库
     * 1）prompt语句：假设你是一个数据库专家，下面我会给出一张表jdi_isc_stock_log_sharding的一些数据，第一行为字段名，后续为字段内容，请生成对应的数据insert语句序号
     * 2）粘贴线上库查处的数据；
     * 3）生成insert语句，进行校验，注意remark需要设置为null
     * INSERT INTO jdi_isc_stock_log_sharding
     * (id, order_id, biz_no, sku_id, warehouse_id, num, type, remark, creator, updater, create_time, update_time, yn) VALUES
     * (882146, '250705154400001', '250705154400001', '80000161221', '1006', 1, 13, null,'Bydauto_th_local', 'Bydauto_th_local', '2025-07-05 15:44:13', '2025-07-05 15:44:13', 1),
     * (882144, '250705154400001', '250705154400001', '80000161219', '1006', 1, 13, null,'Bydauto_th_local', 'Bydauto_th_local', '2025-07-05 15:44:12', '2025-07-05 15:44:12', 1),
     * (920867, '0', '250705154400001', '80000161219', '1006', 1, 12, null,'Bydauto_th_local', 'Bydauto_th_local', '2025-07-16 18:55:06', '2025-07-16 18:55:06', 1),
     * (882145, '250705154400001', '250705154400001', '80000161220', '1006', 1, 13, null,'Bydauto_th_local', 'Bydauto_th_local', '2025-07-05 15:44:13', '2025-07-05 15:44:13', 1),
     * (882147, '250705154400001', '250705154400001', '80000161222', '1006', 1, 13, null,'Bydauto_th_local', 'Bydauto_th_local', '2025-07-05 15:44:13', '2025-07-05 15:44:13', 1);
     */
    @Test
    public void splitStockTest() {
        String splitReqDtoStr = "[\n"
                + "    {\n"
                + "        \"bizNo\": \"250805150100001\",\n"
                + "        \"countryCode\": \"TH\",\n"
                + "        \"orderId\": \"250705154400001\",\n"
                + "        \"stockItem\": [\n"
                + "            {\n"
                + "                \"num\": 1,\n"
                + "                \"skuId\": 80000161220,\n"
                + "                \"updater\": \"wangyuan37-orderSplit\",\n"
                + "                \"warehouseId\": \"1006\"\n"
                + "            },\n"
                + "            {\n"
                + "                \"num\": 1,\n"
                + "                \"skuId\": 80000161222,\n"
                + "                \"updater\": \"wangyuan37-orderSplit\",\n"
                + "                \"warehouseId\": \"1006\"\n"
                + "            },\n"
                + "            {\n"
                + "                \"num\": 1,\n"
                + "                \"skuId\": 80000161221,\n"
                + "                \"updater\": \"wangyuan37-orderSplit\",\n"
                + "                \"warehouseId\": \"1006\"\n"
                + "            }\n"
                + "        ],\n"
                + "        \"stockOccupyType\": 0,\n"
                + "        \"stockPriority\": 0\n"
                + "    },\n"
                + "    {\n"
                + "        \"bizNo\": \"250805150100002\",\n"
                + "        \"countryCode\": \"TH\",\n"
                + "        \"orderId\": \"250705154400001\",\n"
                + "        \"stockItem\": [\n"
                + "            {\n"
                + "                \"num\": 1,\n"
                + "                \"skuId\": 80000161219,\n"
                + "                \"updater\": \"wangyuan37-orderSplit\",\n"
                + "                \"warehouseId\": \"1006\"\n"
                + "            }\n"
                + "        ],\n"
                + "        \"stockOccupyType\": 0,\n"
                + "        \"stockPriority\": 0\n"
                + "    }\n"
                + "]";

        // 首先将JSON解析为Map<String, JSONObject>
        List<StockManageReqDTO> stockManageReqDTOList = JSON.parseObject(splitReqDtoStr, new TypeReference<List<StockManageReqDTO>>() {
        });

//        // 然后转换为Map<Long, AreaInfoReadResp>
//        Map<Long, AreaInfoReadResp> areaInfoReadRespMap = new HashMap<>();
//        for (Map.Entry<String, JSONObject> entry : tempMap.entrySet()) {
//            Long key = Long.parseLong(entry.getKey());
//            AreaInfoReadResp value = JSON.toJavaObject(entry.getValue(), AreaInfoReadResp.class);
//            areaInfoReadRespMap.put(key, value);
//        }


        StockSplitReqDTO reqDTO = new StockSplitReqDTO();
        reqDTO.setStockManageReqDTOList(stockManageReqDTOList);
        DataResponse<Boolean> dataResponse = stockManageService.splitStock(reqDTO);

        System.out.println("split result is:" + JSON.toJSONString(dataResponse));
    }


    public static void main(String[] args) {
        String splitReqDtoStr = "[{\"bizNo\":\"250805150100001\",\"countryCode\":\"TH\",\"orderId\":\"250705154400001\",\"stockItem\":[{\"num\":1,\"skuId\":80000161220,\"updater\":\"wangyuan37-orderSplit\",\"warehouseId\":\"1006\"},{\"num\":1,\"skuId\":80000161222,\"updater\":\"wangyuan37-orderSplit\",\"warehouseId\":\"1006\"},{\"num\":1,\"skuId\":80000161221,\"updater\":\"wangyuan37-orderSplit\",\"warehouseId\":\"1006\"}],\"stockOccupyType\":0,\"stockPriority\":0},{\"bizNo\":\"250805150100002\",\"countryCode\":\"TH\",\"orderId\":\"250705154400001\",\"stockItem\":[{\"num\":1,\"skuId\":80000161219,\"updater\":\"wangyuan37-orderSplit\",\"warehouseId\":\"1006\"}],\"stockOccupyType\":0,\"stockPriority\":0}]";

        // 首先将JSON解析为Map<String, JSONObject>
        List<StockManageReqDTO> stockManageReqDTOList = JSON.parseObject(splitReqDtoStr, new TypeReference<List<StockManageReqDTO>>() {
        });

        System.out.println("result is:"+ stockManageReqDTOList);
    }
}
