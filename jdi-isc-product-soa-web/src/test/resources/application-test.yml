jdi:
  common:
    frame:
      ump-prefix:
      log-level: INFO
  isc:
    station:
      country:
        relation: "{\"relations\":{\"0\":[\"VN\",\"TH\"],\"1\":[\"TH\",\"CN\",\"VN\",\"UAE\"]}}"
    categoryAttribute: '{"saleAttrIdOther":100100}'
    zip:
      path: /Users/<USER>/Downloads/zipFolder/
    task:
      env: dev

oper-ducc:
  hostPort: test.ducc.jd.local
  config: isc-oper
  profile: pre
  namespace: international
  application: jdos_eone-jdi-isc-product-soa
open-ducc:
  hostPort: test.ducc.jd.local
  config: isc-open
  profile: pre
  namespace: international
  application: jdos_eone-jdi-isc-open-soa

jimdb:
  serviceEndpoint: http://test.cfs.jim.jd.local
  ioThreadPoolSize: 5
  computationThreadPoolSize: 5
  requestQueueSize: 100000
# https://docs.jdcloud.com/cn/object-storage-service/oss-endpont-list
s3:
  region: cn-north-1
  protocol: https://
  external:
    endpoint: s3.cn-north-1.jdcloud-oss.com
  internal:
    endpoint: s3-internal.cn-north-1.jdcloud-oss.com
  default:
    bucket: jdi-intl
es:
  maxResultWindow: 10000

chatgpt:
  url: http://gpt-proxy.jd.com/v1/chat/completions
  chatGptKeys: 755be5cb-1a60-4ab0-8cbb-fe8496478539
  model: gpt-35-turbo-1106
jd:
  product:
    system:
      key: qiye_jdi-igc-product
  ipc:
    system:
      key: xuni_imp-operate-admin
  gms:
    product:
      system:
        key: gongye_jdi-isc-product-center
sku:
  detail:
    purchaseTypeName: '{"*":{"CN":"工业采购","VN":"国际自采"}}'
    customerTradeTypeName: '{"*":{"EXW":"EXW","DD":"本土交货"},"*":{"EXW":"EXW(EN)","DD":"本土交货(EN)"}}'

image:
  translate:
    url: www.jiatongyitu.com/imageTransl
    getResultUrl: www.jiatongyitu.com/imageTransl/getResult
    apiKey: WV5NeUrCNmpVwHEc
  domain: http://img11.360buyimg.com/n1/

topic:
  jmq4:
    consumer:
      special-attr: jdi_isc_product_special_attr_msg_pre
      isc:
        sku:
          publish: jdi_isc_sku_publish_msg
        supplier:
          audit: jdi_isc_supplier_audit_msg_uat
        order:
          submit: test
      xbp:
        event: XBP_EVENT_PRE
      approve:
        event: jdi_isc_joysky_result_msg_pre
    product:
      special-attr: jdi_isc_product_special_attr_msg_pre
      isc:
        order:
          delivery: jdi_isc_order_delivery_msg
        supplier:
          audit: jdi_isc_supplier_audit_msg_uat
        costPriceRefresh: jdi_isc_cost_price_trigger_msg
        customerPriceRefresh: jdi_isc_customer_price_refresh_msg
        skuChangeMsg: jdi_isc_sku_change_msg_test
        approveOrder: jdi_isc_approve_order_msg_test
        initCountryAgreementTopic: jdi_isc_init_country_agreement_msg_test
        priceWarnTopic: jdi_isc_price_warn_msg_test
push:
  msg:
    appId: jdi-isc-product-soa
    type:

mail:
  mailType: 161106
  titleConfig: '{1:{"pass":"京东工采国际平台提醒您：账号审核通过/ Jingdong Engineering and Procurement International Platform reminds you: your account has been reviewed"},2:{"default":"京东工采国际平台提醒您：品牌即将过期/JDi reminds you: brand is about to expire"},5:{"VN":"JDI WVC系统发品驳回通知 XIN THÔNG BÁO TỪ CHỐI PHÁT HÀNH SẢN PHẨM"},6:{"TH":"JDI WVC系统发品驳回通知 การแจ้งปฏิเสธการเผยแพร่ผลิตภัณฑ์ในระบบ"}}'
  brand:
    customMailCopyTo: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'
    specialMail: '<EMAIL>'


logging:
  level:
    com.jd.laf.config.spring.config.Observer: OFF


enabled:
  ted: false
  ump: false
  oss: false
  jimdb: true

#测试地址
auth:
  appKey: f575be01336147d18c854c43cadd103a
  appToken: ad489b49a09e4e3780622574bace1088
  alias: test
  applyFor: http://test.auth.jd.com/permission/apply?systemCode=6hAA2bxHKoqJ3ltj