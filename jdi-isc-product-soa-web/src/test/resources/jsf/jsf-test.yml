jd:
  jsf:
    registry:
      index: test.i.jsf.jd.local
    common:
      alias: test:1.0
      testAlias: easymock_isc:test:1.0
      timeout: 5000
      writeTimeout: 300000
      longTimeout: 120000
    app:
      productSoa: J-dos-jdi-isc-product-soa
    consumer:
      gms.category:
        alias: easymock_gms_category
        timeout: 5000
      jme:
        alias: uat:1.0
      attributeGroup:
        alias: jdtest
        token: 6a99291a-8cd4-42e7-bc8a-2dd277df0dfc
      categorySaleAtt:
        alias: jdtest
        token: 8aa70fc5-24af-464f-8352-f968bb932f7d
      b2b.ware:
        alias: easymock_b2b
        timeout: 5000
      mapping:
        alias: ztcs
        token: prod:1.0
      iopProduct:
        alias: jdtest
        timeout: 5000
      gmsProduct:
        alias: easymock_isc:test:1.0
        token:
      ppsPrice:
        alias: pps_jc_tp
        token: 31a78292-3967-4488-999d-f21d354f3b45
      fms:
        alias: kingdo-price
        token: ploughcbj_trunk
      strategy:
        alias: production
      available:
        alias: easymock_isc:test:1.0
      anycall:
        alias: anycall-center
      vc:
        auth:
          alias: VCAUTH
      vmsExternal:
        alias: vms-i18n-service-alias
        token: b4d923fc-fbc7-41e6-8bed-5eb58a4f9e09
      market:
        alias: WARE-DETAIL
        token: 831b118d-957b-4fa1-bee5-ecd5cccc683e
      gmsAssembly:
        alias: LF_PUBLIC_DB_0
        token: 99be1344-bc83-4800-ba43-338b321141d1
      customMailService:
        token: VBbXzW7xlaD3YiqcVrVehA
        alias: custom_mailService_online
      esi:
        contractService:
          alias: easymock_isc:test:1.0
          timeout: 10000
      eamdmdata:
        alias: ea-mdm-0.0.1
        timeout: 30000
      xbp:
        common:
          alias: TEST
          timeout: 10000
      gmsCate:
        alias: jdtest
        token:
      property:
        app: J-dos-jdi-igc-product
        alias: jdtest
        token: a9f71bea-ab5e-4286-b582-3908c435c95c
      propertyvalue:
        app: J-dos-jdi-igc-product
        alias: jdtest
        token:
      material:
        app: J-dos-jdi-isc-product-center
        alias: jdtest
        token:
      eptWareCenter:
        alias: production:0.0.1
      gdProduct:
        alias: easymock_isc:test:1.0
        timeout: 5000
      gdGmsProduct:
        alias: easymock_isc:test:1.0
        timeout: 5000
      b2bPriceCore:
        app: J-dos-jdi-isc-product-center
        token: d0aebd4a-90f8-44e5-83ad-a266ef2c4864
        alias: easymock_test:1.0
        timeout: 5000
      aggregate:
        alias: test:1.0
        timeout: 5000
      extendData:
        alias: jdtest
        timeout: 5000
      gd:
        pool:
          alias: jdtest
          timeout: 5000
      newAttribute:
        alias: jdtest
        token:
      newAttributeValue:
        alias: jdtest
        token:
      gmsAreaStock:
        alias: easymock_isc:test:1.0
        token:
      gmsCateSearch:
        alias: jdtest
        token:
      gmsCateTree:
        alias: jdtest
        token:
      auth:
        alias: jdtest
        timeout: 5000
      user:
        soa:
          alias: ka-user-soa_yufa:0.0.1
          timeout: 5000
          token: a29d3746-9bdc-44ce-b368-c18094bff886
      buser:
        sdk:
          alias: buser-pre:0.0.3
          timeout: 5000
      limitbuy:
        alias: WebFlat-LF-Test
        token: 8195db42-df02-4785-88b4-13a7b1d9f2f0
        timeout: 5000
    provider:
      spuRead:
        alias: test:1.0
        timeout: 15000
      spuWrite:
        alias: test:1.0
        timeout: 50000
      xxx:
        alias: test:1.0
        timeout: 5000
      orderVerify:
        alias: test:1.0
        timeout: 5000
      translate:
        alias: test:1.0
        timeout: 20000
      category:
        alias: test:1.0
        timeout: 30000

