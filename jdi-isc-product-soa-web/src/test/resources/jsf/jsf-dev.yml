jd:
  jsf:
    registry:
      index: test.i.jsf.jd.local
    common:
      alias: test:1.0
      timeout: 5000
      writeTimeout: 10000
      longTimeout: 30000
    app:
      productSoa: J-dos-jdi-isc-product-soa
    consumer:
      gms.category:
        alias: easymock_gms_category
        timeout: 5000
      attributeGroup:
        alias: jdtest
        token: 6a99291a-8cd4-42e7-bc8a-2dd277df0dfc
      categorySaleAtt:
#        alias: easymock_isc:test1.0
        alias: jdtest
        token: 8aa70fc5-24af-464f-8352-f968bb932f7d
      b2b.ware:
        alias: easymock_b2b
        timeout: 5000
      aggregate:
        timeout: 5000
        alias: test:1.0
      mapping:
        alias: ztcs
        token: test
      iopProduct:
        alias: online
        timeout: 5000
      gmsProduct:
        alias: category_pro_cn_LF
        token: 7212f1b4-1c2a-4133-8ef6-8c3eea0683c9
      jme:
        alias: uat:1.0
      ppsPrice:
        alias: pps_jc_tp
        token: 31a78292-3967-4488-999d-f21d354f3b45
      fms:
        alias: gylzt_yfb
        token: ploughcbj_trunk
      strategy:
        alias: yf
      available:
        alias: ht
      anycall:
        alias: anycall-center-test
      vc:
        auth:
          alias: VCAUTH_YF
      vmsExternal:
        alias: poptest
        token:
      market:
        alias: WARE-DETAIL
        token: 831b118d-957b-4fa1-bee5-ecd5cccc683e
      gmsAssembly:
        alias: LF_PUBLIC_DB_0
        token: 4090804c-62fe-4771-aa4d-3f79c4864d5f
      customMailService:
        token: VBbXzW7xlaD3YiqcVrVehA
        alias: custom_mailService_online
      esi:
        contractService:
          alias: esi.createContractService-pre
          timeout: 10000
      eamdmdata:
        alias: ea-mdm-0.0.1
        timeout: 5000
      xbp:
        common:
          alias: TEST
          timeout: 10000
      gmsCate:
        alias: jdtest
        token: e57f65de-d0f4-440a-8ac7-167793bf7da0
      property:
        app: J-dos-jdi-igc-product
        alias: jdtest
        token: a9f71bea-ab5e-4286-b582-3908c435c95c
      propertyvalue:
        app: J-dos-jdi-igc-product
        alias: jdtest
        token:
      material:
        app: J-dos-jdi-isc-product-center
        alias: ztcs
        token:
      eptWareCenter:
        alias: production:0.0.1
      gdProduct:
        alias: k2_gd_boost:pro
        timeout: 5000
      gdGmsProduct:
        alias: k2_gd_boost:pro
        timeout: 5000
      b2bPriceCore:
        app: J-dos-jdi-isc-product-center
        token: d0aebd4a-90f8-44e5-83ad-a266ef2c4864
        alias: b2b-price:paas
        timeout: 5000
      extendData:
        alias: k2_gd_boost:pro
        timeout: 5000
      newAttribute:
        alias: jdtest
        token: 464cb362-484c-460d-9f1f-b98b14f63acf
      newAttributeValue:
        alias: jdtest
        token: ca87d881-0d17-4502-b6f0-9d05c3532f1b
      gd:
        pool:
          alias: test
          timeout: 5000
      gmsAreaStock:
        alias: ss_lf_public
        token: 6de34936-674d-4b44-8168-70676e285340
      gmsCateSearch:
        alias: jdtest
        token: e57f65de-d0f4-440a-8ac7-167793bf7da0
      gmsCateTree:
        alias: jdtest
        token: e57f65de-d0f4-440a-8ac7-167793bf7da0
      auth:
        alias: jdtest
        timeout: 5000
      user:
        soa:
          alias: ka-user-soa_yufa:0.0.1
          timeout: 5000
          token: a29d3746-9bdc-44ce-b368-c18094bff886
      buser:
        sdk:
          alias: buser-pre:0.0.3
          timeout: 5000
      limitbuy:
        alias: WebFlat-LF-Test
        token: 8195db42-df02-4785-88b4-13a7b1d9f2f0
        timeout: 5000
    provider:
      spuRead:
        alias: prod:1.0
        timeout: 15000
      spuWrite:
        alias: prod:1.0
        timeout: 50000
      xxx:
        alias: dev:1.0
        timeout: 5000
      orderVerify:
        alias: dev:1.0
        timeout: 5000
      translate:
        alias: dev:1.0
        timeout: 20000
      category:
        alias: dev:1.0
        timeout: 30000

jdi:
  isc:
    customerOrderWaybill: xxx

mail:
  mailType: 161106
  titleConfig: '{"pass":"京东工采国际平台提醒您：账号审核通过/ Jingdong Engineering and Procurement International Platform reminds you: your account has been reviewed"}'
