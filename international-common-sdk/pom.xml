<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <properties>
        <java.version>1.8</java.version>
        <encoding>UTF-8</encoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <groupId>com.jd.international.soa</groupId>
    <artifactId>international-common-sdk</artifactId>
    <version>1.1.13${lib.env}</version>
    <packaging>jar</packaging>
    <name>international-common-sdk</name>

    <dependencies>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-soa-base</artifactId>
            <version>1.1.0${lib.env}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <artifactId>common-domain</artifactId>
            <groupId>com.jdi.common</groupId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83-jdsec.rc1</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <artifactId>common-domain</artifactId>
            <groupId>com.jdi.common</groupId>
            <version>2.0.0</version>
        </dependency>

    </dependencies>

    <distributionManagement>
        <repository>
            <id>libs-releases-local</id>
            <name>Release Repository</name>
            <url>http://artifactory.jd.com/libs-releases-local</url>
        </repository>
        <snapshotRepository>
            <id>libs-snapshots-local</id>
            <name>Snapshot Repository</name>
            <url>http://artifactory.jd.com/libs-snapshots-local</url>
        </snapshotRepository>
    </distributionManagement>


    <profiles>

        <profile>
            <id>dev</id>
            <properties>
                <lib.env>-test-SNAPSHOT</lib.env>
            </properties>
        </profile>

        <profile>
            <id>test</id>
            <properties>
                <lib.env>-test-SNAPSHOT</lib.env>
            </properties>
        </profile>

        <profile>
            <id>pre</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <lib.env>-SNAPSHOT</lib.env>
            </properties>
        </profile>

        <profile>
            <id>pro</id>
            <properties>
                <lib.env></lib.env>
            </properties>
        </profile>
    </profiles>
</project>