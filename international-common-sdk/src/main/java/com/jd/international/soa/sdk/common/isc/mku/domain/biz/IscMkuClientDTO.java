package com.jd.international.soa.sdk.common.isc.mku.domain.biz;

import com.jd.international.soa.sdk.common.common.ProductPricesDTO;
import com.jd.international.soa.sdk.common.isc.brand.domain.biz.IscBrandClientDTO;
import com.jd.international.soa.sdk.common.isc.category.domain.biz.IscCategoryTreeNodeDTO;
import com.jd.international.soa.sdk.common.isc.delivery.DeliveryAgingDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description: 客户端VO
 * @Author: zhaojianguo21
 * @Date: 2023/12/15 09:42
 **/
@Data
public class IscMkuClientDTO implements Serializable {

    /** MKUID */
    private Long mkuId;

    /** mku主图 */
    private String mkuImage;

    /** mku细节图 */
    private String detailImg;

    /** mku名称 */
    private String mkuName;

    /** 币种 */
    private String currency;

    /** 销售单位 */
    private String saleUnit;

    /** 销售价,4位小数 */
    private BigDecimal salePrice;

    /** 多个销售属性的值 */
    private String saleAttributeValues;

    /** 扩展属性*/
    private List<IscMkuClientPropertyDTO> extentAttributes;

    /** 商品详描 */
    private String description;


    /** 类目ID */
    private Long catId;
    /** 类目四级路径详情*/
    private List<IscCategoryTreeNodeDTO> catePath;
    /** 类目四级路径 */
    private String catePathStr;

    /** 品牌信息 */
    private IscBrandClientDTO brand;

    /** 销售状态 */
    private Integer saleStatus;


    /**
     * 多币种价格
     */
    private Map<String, ProductPricesDTO> currenciesPrices;

    /**
     * 默认币种价格
     */
    private ProductPricesDTO showCurrency;

    /**
     * 最小起订量
     */
    private Integer moq;

    /**
     * 最小起订量文案
     */
    private String moqText;

    /**
     * 剩余库存
     */
    private Integer remainNum;
    /**
     * 货期
     */
    private String deliveryDate;
    /**
     * 所属国家
     */
    private String sourceCountryCode;

    /**
     * 履约时效数据
     */
    private DeliveryAgingDTO skuDeliveryAging;

    /**
     * 库存
     */
    private IscMkuClientStockDTO stock;

    /** skuId */
    private Long skuId;

    /**
     * 是否在国池中标志
     */
    private Boolean countryInPoolFlag;

    /**
     * 商品打标集合
     */
    private Map<String,String> featureTagMap;

    /**
     * 子列表，用于存储关联的IscMkuClientDTO对象。
     */
    private List<IscMkuClientDTO> subList;

    /**
     * 存储关联的销售属性的值的列表。
     */
    private List<IscMkuClientPropertyDTO> saleAttributes;

    /**
     * 存储MKU关联的销售属性的值的列表。
     */
    private List<IscMkuClientMkuPropertyValueDTO> mkuSaleAttributes;

    /**
     * 是否可销售的标志
     */
    private Boolean isAvailableSale;

    /**
     * 型号，没有多语言，只展示默认信息
     */
    private String model;
}
