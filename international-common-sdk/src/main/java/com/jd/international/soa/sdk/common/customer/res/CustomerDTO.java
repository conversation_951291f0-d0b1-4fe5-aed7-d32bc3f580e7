package com.jd.international.soa.sdk.common.customer.res;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;


/**
 * 客户基础实体
 * <AUTHOR>
 * @date 20231109
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDTO {

    /** 客户名称*/
    private String clientName;

    /** 客户编码*/
    private String clientCode;

    /** 合同编码*/
    private String contractCode;

    /** 客户pin*/
    private String pin;

    /** 客户所属站点 CN、US、VN、TH*/
    private String country;

    /** 拥有的站点 */
    private List<Integer> stations;

    /**
     * 售卖币种
     */
    private String saleCurrency;

    /**
     * wisp页面可见的订单列表状态
     */
    private List<Integer> wispOrderListShowStatus;

    /**
     * 订单配置信息
     */
    private Map<String, String> orderMap;

    /**
     * 开放访问所属地区
     */
    private String openArea;
    /**
     * 企业类型：1-EPE(跨境贸易),2-FDI(本土贸易)
     */
    private String companyType;
    /**
     * 客户语种信息
     */
    private Map<String, String> language;
    /**
     * 客户多语言国家站信息
     */
    private Map<String, String> countryNameMap;

    /**
     * 内贸段pin
     */
    private String iopPin;

    /**
     * EPT仓编码
     */
    private Long storeId;

    /**
     * iop地址信息
     */
    private IopAddressVO iopAddressVO;
}
