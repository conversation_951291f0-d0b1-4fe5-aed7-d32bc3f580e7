package com.jd.international.soa.sdk.common.isc.mku.domain.biz;

import com.jd.international.soa.base.isc.common.IscBasePageReqDTO;
import com.jd.international.soa.sdk.common.customer.res.CustomerDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * @Description: 客户端列表请求
 * @Author: zhaojianguo21
 * @Date: 2023/12/15 09:57
 **/
@Data
public class IscMkuClientPageReqDTO extends IscBasePageReqDTO implements Serializable {

    /** 类目ID*/
    private Long catId;

    /** 商品名称*/
    private String name;

    /** 类目ID*/
    private List<Long> catIds;

    /** 关键字*/
    private String key;

    /** 商品标识 */
    private Set<String> featureTags;

    /*** 库存标识，2-仅看有货(现货)商品，null忽略此标签 */
    private Integer stockFlag;

    /*** 客户 */
    private CustomerDTO customerDTO;
}
