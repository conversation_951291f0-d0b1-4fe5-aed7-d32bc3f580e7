package com.jd.international.soa.sdk.common.isc.mku.domain.biz;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 属性
 * @Author: zhaojianguo21
 * @Date: 2023/12/15 13:46
 **/
@Data
public class IscMkuClientPropertyDTO implements Serializable {
    /**
     * Integer shield（隐藏属性 shield字段1为隐藏）
     */
    private Integer shield;
    /**
     * String isQuJianZhi（为 1 表明为区间属性）
     */
    private String isQuJianZhi;
    /**
     * 属性组id
     */
    private Integer comGroupId;
    /**
     * 属性组名称
     */
    private String comGroupName;
    /** 属性名称*/
    private String name;
    /** 属性值，多个值之间固定符号分隔*/
    private String values;
    /** 销售属性0图销1文销 */
    private Integer attributeInputType;
    /** 销售属性值*/
    private List<IscMkuClientPropertyValueDTO> propertyValueDTOS;
}
