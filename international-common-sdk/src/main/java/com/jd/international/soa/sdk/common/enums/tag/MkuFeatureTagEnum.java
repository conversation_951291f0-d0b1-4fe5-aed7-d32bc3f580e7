package com.jd.international.soa.sdk.common.enums.tag;


import lombok.Getter;

import java.util.Arrays;

@Getter
public enum MkuFeatureTagEnum {

    SHIPPING_48_HOUR(48,"48-hour-shipping","48h Delivery","stockUp"),

    ;

    MkuFeatureTagEnum(Integer esCode, String code, String desc, String relatCode) {
        this.esCode = esCode;
        this.code = code;
        this.desc = desc;
        this.relatCode = relatCode;
    }


    public static MkuFeatureTagEnum queryByRelatCode(String relatCode){
        return Arrays.stream(values()).filter(c -> relatCode.equals(c.getRelatCode())).findFirst().orElse(null);
    }

    public static MkuFeatureTagEnum queryCode(String code){
        return Arrays.stream(values()).filter(c -> code.equals(c.getCode())).findFirst().orElse(null);
    }

    public static MkuFeatureTagEnum queryEsCode(Integer esCode){
        return Arrays.stream(values()).filter(c -> esCode.equals(c.getEsCode())).findFirst().orElse(null);
    }

    /**
     * es 中的值
     */
    private Integer esCode;

    /**
     * code 编码，可以明显看出业务含义
     */
    private String code;
    /**
     * 说明
     */
    private String desc;

    /**
     * 关联的业务code
     */
    private String relatCode;
}
