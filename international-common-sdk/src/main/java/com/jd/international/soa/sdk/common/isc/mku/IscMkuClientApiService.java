package com.jd.international.soa.sdk.common.isc.mku;

import com.jd.international.soa.sdk.common.isc.mku.domain.biz.*;
import com.jd.international.soa.sdk.common.isc.mku.req.MkuClientStockSoaDTO;
import com.jd.international.soa.sdk.common.isc.mku.req.MkuClientStockSoaReqDTO;
import com.jd.international.soa.sdk.common.isc.mku.req.MkuReplenishmentSoaDTO;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * @Description: 销端服务接口
 * @Author: zhaojianguo21
 * @Date: 2023/12/15 14:38
 **/
public interface IscMkuClientApiService {

    /**
     * 商品列表
     * @param input 列表参数
     * @return 商品列表
     */
    DataResponse<PageInfo<IscMkuClientDTO>> page(IscMkuClientPageReqDTO input);

    /**
     * 获取商品配置文案信息
     * @param moq
     * @param lang
     * @return
     */
    String getMoqText(Integer moq, String lang);

//    /**
//     * 是否存在mku
//     * @param input 请求参数
//     * @return true/false
//     */
//    DataResponse<Boolean> existsMku(IscMkuClientDetailReqDTO input);

    /**
     * 是否存在有效mku
     * @param input 请求参数
     * @return true/false
     */
    DataResponse<Boolean> existsValidMku(IscMkuClientDetailReqDTO input);

//    /**
//     * mku信息
//     * @param input 请求参数
//     * @return mku描述
//     */
//    DataResponse<IscMkuClientDTO> baseInfo(IscMkuClientDetailReqDTO input);
//
//    /**
//     * 查同mku的同组聚堆信息
//     * @param input 请求参数
//     * @return 同组商品聚堆信息
//     */
//    DataResponse<IscMkuClientGroupDTO> groupInfo(IscMkuClientDetailReqDTO input);

//
//    /**
//     * 商品列表页单个商品卡片信息
//     * @param input 请求参数
//     * @return 商品卡片信息
//     */
//    DataResponse<IscMkuClientCardInfoResDTO> cardInfo(IscMkuClientCardInfoReqDTO input);

    /**
     * 库存查询
     * @return 商品卡片信息
     */
    DataResponse<List<MkuClientStockSoaDTO>> getStock(MkuClientStockSoaReqDTO req);


    /**
     * 申请补货
     */
    DataResponse<Boolean> replenishment(MkuReplenishmentSoaDTO req);

    /**
     * 商品配送时效信息
     */
    DataResponse<Map<Long, IscMkuClientDeliveryAgingDTO>> getDeliveryAging(List<Long> mkuIds, String clientCode);
}
