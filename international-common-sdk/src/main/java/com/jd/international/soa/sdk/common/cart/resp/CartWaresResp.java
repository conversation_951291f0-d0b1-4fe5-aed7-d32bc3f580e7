package com.jd.international.soa.sdk.common.cart.resp;

import com.jd.international.soa.base.skuSale.SkuSaleStateResp;
import com.jd.international.soa.sdk.common.common.MultiCurrencyPriceDTO;
import com.jd.international.soa.sdk.common.common.ProductPricesDTO;
import com.jd.international.soa.sdk.common.material.res.MkuMaterialResDTO;
import com.jd.international.soa.sdk.common.sale.SkuSaleAttrDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
@Data
public class CartWaresResp {
    private Long id;
    /**
     * 商品编码
     */
    private String sku;

    /**
     * 商品数量
     */
    private Integer skuNum;

    /**
     * 勾选状态
     */
    private Boolean checked;
    /**
     * 加车时价格
     */
    private String addCartPrice;

    /**
     * 人民币价格
     */
    private BigDecimal originalPrice;

    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 小记
     */
    private BigDecimal notes;
    /**
     * 汇率
     */
    private BigDecimal exchangeRate;

    /**
     * 加车时间
     */
    private Date addCartTime;

    /**
     * pin
     */
    private String pin;
    /**
     * 商品名称
     */
    private String skuName;
    /**
     * 商品图片
     */
    private String skuImg;
    /**
     * 商品型号
     */
    private String model;

    /**
     * 最低起购量
     */
    private Integer lowestBuy;
    /**
     * 商品单位
     */
    private String unit;
    /**
     * 可售状态
     */
    private SkuSaleStateResp saleState;
    /**
     * 赠品数据
     */
    private Map<String,Map<String, String>> gifts;

    /**
     * 多币种价格
     */
    private Map<String, ProductPricesDTO> currenciesPrices;

    /**
     * 默认币种价格
     */
    private ProductPricesDTO showCurrency;

    /**
     * 商品小计多币种数据
     */
    private MultiCurrencyPriceDTO notesCurrencies;

    /**
     * 商品是否在池
     */
    private Boolean inPool;

    /**
     * 最小起订量
     */
    private Integer moq;

    /**
     * 最小起订量文案
     */
    private String moqText;

    /**
     * 现货库存
     */
    private Integer remainNum;

    /**
     * 物料信息
     */
    private MkuMaterialResDTO mkuMaterial;

    /**
     * 所属国家
     */
    private String sourceCountryCode;

    /**
     * 销售属性
     */
    private String saleAttributes;

    /**
     * 履约模式，1跨境、2本本、3仓发
     */
    private Integer fulfillmentModel;

    /**
     * 履约时效：商品履约 + 发货时效
     */
    private Integer fulfillmentDays;

    /**
     * 库存状态
     */
    private Integer stockFlag;

    /** 库存状态 : 33有货、34无货*/
    private Integer stockStateType;

    /**
     * 是否可售
     */
    private Boolean isAvailableSale;


}
