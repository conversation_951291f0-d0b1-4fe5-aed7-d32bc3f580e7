package com.jd.international.soa.sdk.common.isc.mku.domain.biz;

import com.jd.international.soa.base.isc.common.IscBaseReqDTO;
import com.jd.international.soa.sdk.common.customer.res.CustomerDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 商详页面请求
 * @Author: zhaojianguo21
 * @Date: 2023/12/15 14:06
 **/
@Data
public class IscMkuClientDetailReqDTO extends IscBaseReqDTO implements Serializable {

    /** MKUID */
    private Long mkuId;

    /** 类目ID*/
    private Long catId;

    /** 客户 */
    private CustomerDTO customerDTO;
}
