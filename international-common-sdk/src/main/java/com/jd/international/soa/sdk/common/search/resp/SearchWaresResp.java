package com.jd.international.soa.sdk.common.search.resp;

import com.jd.international.soa.base.skuSale.SkuSaleStateResp;
import com.jd.international.soa.sdk.common.common.ProductPricesDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientPropertyDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class SearchWaresResp implements Serializable {
    private static final long serialVersionUID = -4253867432104828576L;
    /**
     * 商品名称
     */
    private String wareName;
    /**
     * mkuid
     */
    private String sku;

    /**
     * 币种
     */
    private String currency;
    /**
     * 市场价
     */
    private BigDecimal price;

    private String color;
    /**
     * 图片
     */
    private String img;

    private SkuSaleStateResp saleState;

    private List<SearchWaresResp> slaveWares;

    private String brand;

    private String mode;

    /**
     * 多币种价格
     */
    private Map<String,ProductPricesDTO> currenciesPrices;

    /**
     * 默认币种价格
     */
    private ProductPricesDTO showCurrency;


    /**
     * 最小起订量
     */
    private Integer moq;

    /**
     * 最小起订量文案
     */
    private String moqText;

    /**
     * 剩余库存
     */
    private Integer remainNum;
    /**
     * 货期
     */
    private String deliveryDate;
    /**
     * 所属国家
     */
    private String sourceCountryCode;

    /**
     * 库存详细状态
     */
    private Integer stockFlag;

    /** 库存状态 : 33有货、34无货*/
    private Integer stockStateType;

    /**
     * 商品打标集合
     */
    private Map<String,String> featureTagMap;

    /**
     * 是否可售
     */
    private Boolean isAvailableSale;

    /**
     * 存储关联的销售属性的值的列表。
     */
    private List<IscMkuClientPropertyDTO> saleAttributes;

    /**
     * 子列表，用于存储关联的其他商品信息。
     */
    private List<SearchWaresResp> subList;

    public String getWareName() {
        return wareName;
    }

    public void setWareName(String wareName) {
        this.wareName = wareName;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public SkuSaleStateResp getSaleState() {
        return saleState;
    }

    public void setSaleState(SkuSaleStateResp saleState) {
        this.saleState = saleState;
    }

    public List<SearchWaresResp> getSlaveWares() {
        return slaveWares;
    }

    public void setSlaveWares(List<SearchWaresResp> slaveWares) {
        this.slaveWares = slaveWares;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public Map<String, ProductPricesDTO> getCurrenciesPrices() {
        return currenciesPrices;
    }

    public void setCurrenciesPrices(Map<String, ProductPricesDTO> currenciesPrices) {
        this.currenciesPrices = currenciesPrices;
    }

    public ProductPricesDTO getShowCurrency() {
        return showCurrency;
    }

    public void setShowCurrency(ProductPricesDTO showCurrency) {
        this.showCurrency = showCurrency;
    }

    public Integer getMoq() {
        return moq;
    }

    public void setMoq(Integer moq) {
        this.moq = moq;
    }

    public String getMoqText() {
        return moqText;
    }

    public void setMoqText(String moqText) {
        this.moqText = moqText;
    }

    public Integer getRemainNum() {
        return remainNum;
    }

    public void setRemainNum(Integer remainNum) {
        this.remainNum = remainNum;
    }

    public String getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(String deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getSourceCountryCode() {
        return sourceCountryCode;
    }

    public void setSourceCountryCode(String sourceCountryCode) {
        this.sourceCountryCode = sourceCountryCode;
    }

    public Integer getStockFlag() {
        return stockFlag;
    }

    public void setStockFlag(Integer stockFlag) {
        this.stockFlag = stockFlag;
    }

    public Integer getStockStateType() {
        return stockStateType;
    }

    public void setStockStateType(Integer stockStateType) {
        this.stockStateType = stockStateType;
    }

    public Map<String, String> getFeatureTagMap() {
        return featureTagMap;
    }

    public void setFeatureTagMap(Map<String, String> featureTagMap) {
        this.featureTagMap = featureTagMap;
    }

    public Boolean getIsAvailableSale() {
        return isAvailableSale;
    }

    public void setIsAvailableSale(Boolean isAvailableSale) {
        this.isAvailableSale = isAvailableSale;
    }

    public List<SearchWaresResp> getSubList() {
        return subList;
    }

    public void setSubList(List<SearchWaresResp> subList) {
        this.subList = subList;
    }

    public List<IscMkuClientPropertyDTO> getSaleAttributes() {
        return saleAttributes;
    }

    public void setSaleAttributes(List<IscMkuClientPropertyDTO> saleAttributes) {
        this.saleAttributes = saleAttributes;
    }
}
