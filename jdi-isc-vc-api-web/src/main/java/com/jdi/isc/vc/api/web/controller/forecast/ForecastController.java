package com.jdi.isc.vc.api.web.controller.forecast;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.fulfillment.soa.api.parcel.ParcelFileReadApiService;
import com.jdi.isc.fulfillment.soa.api.parcel.ParcelWriteApiService;
import com.jdi.isc.fulfillment.soa.api.parcel.req.ParcelCreateReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.parcel.req.ParcelFileReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.parcel.res.ParcelCreateResApiDTO;
import com.jdi.isc.fulfillment.soa.api.parcel.res.ParcelFileResApiDTO;
import com.jdi.isc.fulfillment.soa.api.purchaseWaybill.PurchaseOrderWaybillReadApiService;
import com.jdi.isc.fulfillment.soa.api.purchaseWaybill.PurchaseOrderWaybillWriteApiService;
import com.jdi.isc.fulfillment.soa.api.purchaseWaybill.req.PurchaseOrderWaybillFileReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.purchaseWaybill.req.PurchaseOrderWaybillShipReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.purchaseWaybill.res.PurchaseOrderWaybillFileResApiDTO;
import com.jdi.isc.fulfillment.soa.api.purchaseWaybill.res.PurchaseOrderWaybillShipResApiDTO;
import com.jdi.isc.library.i18n.datasource.cache.I18nCacheManager;
import com.jdi.isc.order.center.api.forecast.ForecastOrderApiService;
import com.jdi.isc.order.center.api.forecast.biz.req.*;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderApiResp;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderCountVO;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderDetailResp;
import com.jdi.isc.task.center.api.forecast.ForecastPurchaseOrderPrintApiService;
import com.jdi.isc.task.center.api.forecast.impl.ForecastPurchaseOrderPrintApiDTO;
import com.jdi.isc.vc.api.common.costants.i18n.I18nKeyConstant;
import com.jdi.isc.vc.api.common.frame.UserContextHolder;
import com.jdi.isc.vc.api.common.utils.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Slf4j
@RestController
@RequestMapping("/api/forecast")
public class ForecastController {

    @Resource
    private ForecastOrderApiService forecastOrderApiService;

    @Resource
    private PurchaseOrderWaybillReadApiService purchaseOrderWaybillReadApiService;

    @Resource
    private ParcelWriteApiService parcelWriteApiService;

    @Resource
    private ForecastPurchaseOrderPrintApiService forecastPurchaseOrderPrintApiService;

    @Resource
    private PurchaseOrderWaybillWriteApiService purchaseOrderWaybillWriteApiService;
    @Resource
    private ParcelFileReadApiService parcelFileReadApiService;
    @Resource
    private I18nCacheManager i18nCacheManager;

    @PostMapping("/list")
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<PageInfo<ForecastOrderApiResp>> list(@RequestBody ForecastOrderApiReq req) {
        String lang = UserContextHolder.get().getLang();
        DataResponse<Boolean> response = null;
        SystemUtil.fillOrderExtSystemInfo(req);
        try {
            req.setSupplierCode(UserContextHolder.get().getSupplierCode());
            DataResponse<PageInfo<ForecastOrderApiResp>> pageInfoDataResponse = forecastOrderApiService.pageForecastOrderToVc(req);
            pageInfoDataResponse.getData().getRecords().forEach(oper->{
                if (!CollectionUtils.isEmpty(oper.getOperateVOList())) {
                    oper.getOperateVOList().forEach(vo -> {
                        vo.setOperateName(i18nCacheManager.getValueOrDefault(lang, vo.getOperateName()));
                    });
                }
            });
            return pageInfoDataResponse;
        } catch (Exception e) {
            log.error("采购单-PurchaseOrderController.updateStatus error, req = {}, res = {}", JSON.toJSONString(req), JSON.toJSONString(response));
            return DataResponse.error(I18nKeyConstant.COMMON_SYSTEM_ERROR);
        }
    }


    @PostMapping("/detail")
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<ForecastOrderDetailResp> detail(@RequestBody ForecastDetailOrderApiReq req) {
        DataResponse<Boolean> response = null;
        String lang = UserContextHolder.get().getLang();
        SystemUtil.fillOrderExtSystemInfo(req);
        try {
            req.setSupplierCode(UserContextHolder.get().getSupplierCode());
            DataResponse<ForecastOrderDetailResp> forecastOrderDetailRespDataResponse = forecastOrderApiService.detailForecastOrderToVc(req);
            forecastOrderDetailRespDataResponse.getData().getOperateVOList().forEach(oper->{
                oper.setOperateName(i18nCacheManager.getValueOrDefault(lang, oper.getOperateName()));
            });
            forecastOrderDetailRespDataResponse.getData().getForecastProgressVO().forEach(progress->{
                progress.setNodeName(i18nCacheManager.getValueOrDefault(lang, progress.getNodeName()));
            });
            return forecastOrderDetailRespDataResponse;
        } catch (Exception e) {
            log.error("采购单-PurchaseOrderController.updateStatus error, req = {}, res = {}", JSON.toJSONString(req), JSON.toJSONString(response));
            return DataResponse.error(I18nKeyConstant.COMMON_SYSTEM_ERROR);
        }
    }
    //接单
    @PostMapping("/updateForecastOrderStatus")
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<Boolean> updateForecastOrderStatus(@RequestBody ForecastOrderStatusReq req) {
        DataResponse<Boolean> response = null;
        try {
            UpdateForecastOrderStatusApiReq apiReq = new UpdateForecastOrderStatusApiReq();
            apiReq.setSupplierCode(UserContextHolder.get().getSupplierCode());
            apiReq.setOperateAccount(UserContextHolder.get().getUserName());
            apiReq.setOperate(req.getOperate());
            apiReq.setForecastOrderId(req.getForecastOrderId());
            SystemUtil.fillOrderExtSystemInfo(apiReq);
            DataResponse<Boolean> booleanDataResponse = forecastOrderApiService.updateForecastOrderStatus(apiReq);
            return booleanDataResponse;
        } catch (Exception e) {
            log.error("采购单-PurchaseOrderController.updateStatus error, req = {}, res = {}", JSON.toJSONString(req), JSON.toJSONString(response));
            return DataResponse.error(I18nKeyConstant.COMMON_SYSTEM_ERROR);
        }
    }

    //拆单
    @PostMapping("/splitForecastOrderLocal")
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<Boolean> splitForecastOrderLocal(@RequestBody SplitForecastReq req) {
        DataResponse<Boolean> response = null;
        try {
            SplitForecastOrderLocalReq localReq = new SplitForecastOrderLocalReq();
            localReq.setSplitRemark(req.getSplitRemark());
            localReq.setUpdater(UserContextHolder.get().getUserName());
            localReq.setSplitForecastOrderId(req.getSplitForecastOrderId());
            localReq.setSplitOrderList(req.getSplitOrderList());
            localReq.setSupplierCode(UserContextHolder.get().getSupplierCode());
            SystemUtil.fillOrderExtSystemInfo(localReq);
            DataResponse<Boolean> booleanDataResponse = forecastOrderApiService.splitForecastOrderLocal(localReq);
            return booleanDataResponse;
        } catch (Exception e) {
            log.error("采购单-PurchaseOrderController.updateStatus error, req = {}, res = {}", JSON.toJSONString(req), JSON.toJSONString(response));
            return DataResponse.error(I18nKeyConstant.COMMON_SYSTEM_ERROR);
        }
    }


    //下载交接单
    @PostMapping("/purchaseOrderExport")
    @ToolKit(exceptionWrap = true)
    public DataResponse<PurchaseOrderWaybillFileResApiDTO> purchaseOrderExport(@RequestBody PurchaseOrderWaybillFileReqApiDTO req) {
        SystemUtil.fillFulfillmentSystemInfo(req);
        req.setSupplierCode(UserContextHolder.get().getSupplierCode());
        DataResponse<PurchaseOrderWaybillFileResApiDTO> export = purchaseOrderWaybillReadApiService.export(req);
        return export;
    }

    //创建箱麦
    @PostMapping("/createParcel")
    @ToolKit(exceptionWrap = true)
    public DataResponse<ParcelCreateResApiDTO> createParcel(@RequestBody ParcelCreateReq req) {
        ParcelCreateReqApiDTO reqApiDTO = new ParcelCreateReqApiDTO();
        reqApiDTO.setBizType(4);
        reqApiDTO.setParcelList(req.getParcelList());
        reqApiDTO.setSupplierCode(UserContextHolder.get().getSupplierCode());
        reqApiDTO.setBizId(req.getBizId());
        reqApiDTO.setCreator(UserContextHolder.get().getUserName());
        reqApiDTO.setUpdater(UserContextHolder.get().getUserName());
        DataResponse<ParcelCreateResApiDTO> parcel = parcelWriteApiService.createParcel(reqApiDTO);
        return parcel;
    }

    //打印备货入库单
    @PostMapping("/printEntryStock")
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<String> printEntryStock(@RequestBody ForecastPurchaseOrderPrintApiDTO req) {
        req.setSupplierCode(UserContextHolder.get().getSupplierCode());
        return forecastPurchaseOrderPrintApiService.printEntryStock(req);
    }


    //下载箱唛
    @PostMapping("/parcelExport")
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<ParcelFileResApiDTO> parcelExport(@RequestBody ParcelFileReqApiDTO req) {
        SystemUtil.fillFulfillmentSystemInfo(req);
        req.setBizType(4);
        req.setSupplierCode(UserContextHolder.get().getSupplierCode());
        DataResponse<ParcelFileResApiDTO> export = parcelFileReadApiService.export(req);
        return export;
    }



    //发货/批量发货
    @PostMapping("/purchaseWaybillCreate")
    @ToolKit(exceptionWrap = true,validFlag = false)
    public DataResponse<PurchaseOrderWaybillShipResApiDTO> purchaseWaybillCreate(@RequestBody PurchaseOrderWaybillShipReqApiDTO req) {
        req.setBizType(4);
        req.setCreator(UserContextHolder.get().getUserName());
        req.setUpdater(UserContextHolder.get().getUserName());
        req.setSupplierCode(UserContextHolder.get().getSupplierCode());
        DataResponse<PurchaseOrderWaybillShipResApiDTO> purchaseOrderWaybillShipResApiDTODataResponse = purchaseOrderWaybillWriteApiService.purchaseWaybillCreate(req);
        //todo
        //错误码：100001   ，请分开发货[xxx][xxxx]
        //错误码：100002，当前状态不允许发货:xxx,xxx
        return purchaseOrderWaybillShipResApiDTODataResponse;
    }
    //数量
    @PostMapping("/count")
    @ToolKit(exceptionWrap = true)
    public DataResponse count(@RequestBody OrderCountReq req) {
        ForecastOrderCountReq countReq = new ForecastOrderCountReq();
        countReq.setForecastOrderStatusList(req.getStatusList());
        countReq.setSupplierCode(UserContextHolder.get().getSupplierCode());
        SystemUtil.fillOrderExtSystemInfo(countReq);
        DataResponse<ForecastOrderCountVO> count = forecastOrderApiService.count(countReq);
        return DataResponse.success(count.getData().getRes());
    }
}
