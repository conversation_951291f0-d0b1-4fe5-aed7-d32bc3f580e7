package com.jdi.isc.vc.api.web.controller.config;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.library.i18n.datasource.cache.I18nCacheManager;
import com.jdi.isc.vc.api.common.ducc.VcDuccConfig;
import com.jdi.isc.vc.api.common.frame.UserContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/config")
public class ConfigController {
    @Resource
    private VcDuccConfig vcDuccConfig;

    @Resource
    private I18nCacheManager i18nCacheManager;

    /**
     * 获取表单tab
     */
    @GetMapping("/tab")
    @ToolKit(exceptionWrap = true)
    public DataResponse tabConfig(String tabType) {
        String lang = UserContextHolder.get().getLang();
        JSONObject json = vcDuccConfig.buildPurchaseTabDTO(tabType);
        if (json == null || json.isEmpty()) {
            return DataResponse.error("No data found for the specified tab type.");
        }
        JSONArray array = json.getJSONArray("tab");
        for (int i = 0; i < array.size(); i++) {
            JSONObject tabItem = array.getJSONObject(i);
            String label = tabItem.getString("label");
            String localizedLabel = i18nCacheManager.getValueOrDefault(lang, label);
            tabItem.put("label", localizedLabel);
        }
        return DataResponse.success(json);
    }
}

