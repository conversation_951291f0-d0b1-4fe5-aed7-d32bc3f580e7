package com.jdi.isc.vc.api.web.controller.product;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.ducc.DuccReadApiService;
import com.jdi.isc.vc.api.common.ducc.DuccConfig;
import com.jdi.isc.vc.api.domain.access.PageConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/access")
public class AccessController {

    @Resource
    private DuccReadApiService duccReadApiService;

    @Resource
    private DuccConfig duccConfig;

    /**
     * 获取页面配置
     */
    @GetMapping("/getPageConfig")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<String, List<PageConfigVO>>> getPageConfig( ) {
        Map<String, List<PageConfigVO>> pageConfig = duccConfig.getPageConfig();
        return DataResponse.success(pageConfig);
    }

    /**
     * 获取全局统一枚举
     */
    @PostMapping("/getEnumMap")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<String, Map<String, String>>> getEnumMap(@RequestBody List<String> enumNames) {
        try {
            return duccReadApiService.getEnumKvMap(enumNames);
        } catch (Exception e) {
            log.error("获取枚举失败", e);
            return DataResponse.error("获取枚举失败");
        }
    }

}
