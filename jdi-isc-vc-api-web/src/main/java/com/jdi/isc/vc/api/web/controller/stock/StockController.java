package com.jdi.isc.vc.api.web.controller.stock;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.stock.sku.IscSkuStockManageApiService;
import com.jdi.isc.product.soa.stock.sku.req.SkuStockPageReqDTO;
import com.jdi.isc.product.soa.stock.sku.res.SkuStockPageResDTO;
import com.jdi.isc.vc.api.common.frame.UserContextHolder;
import com.jdi.isc.vc.api.domain.user.UserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 库存信息
 *
 * @author: zhangjin176
 * @date: 2025/5/27 10:35
 * @version:
 **/
@Slf4j
@RestController
@RequestMapping("/api/stock")
public class StockController {

    @Resource
    private IscSkuStockManageApiService iscSkuStockManageApiService;

    @PostMapping("/list")
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<SkuStockPageResDTO>> list(@RequestBody SkuStockPageReqDTO req) {
        UserDTO userDTO = UserContextHolder.get();
        req.setVendorCode(userDTO.getSupplierCode());
        DataResponse<PageInfo<SkuStockPageResDTO>> response = iscSkuStockManageApiService.pageSearchForSupplier(req);
        return DataResponse.success(response.getData());
    }
}
