<?xml version="1.0" encoding="utf-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-autowire="byName">

    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="${jd.jsf.registry.index}"/>

    <jsf:consumer id="purchaseOrderApiService" interface="com.jdi.isc.vc.soa.api.purchaseOrder.PurchaseOrderApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.purchaseOrder.alias}" timeout="${jd.jsf.consumer.purchaseOrder.timeout}" />

    <jsf:consumer id="purchaseOrderCenterApiService" interface="com.jdi.isc.order.center.api.purchaseOrder.PurchaseOrderApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.orderCenter.alias}" timeout="${jd.jsf.consumer.orderCenter.timeout}" />

    <!--供应商品牌查询接口-->
    <jsf:consumer id="vendorBrandReadApiService" interface="com.jdi.isc.vc.soa.api.brand.VendorBrandReadApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.brand.alias}" timeout="${jd.jsf.common.timeout}" />

    <!--供应商类目查询接口-->
    <jsf:consumer id="vendorCategoryReadApiService" interface="com.jdi.isc.vc.soa.api.category.VendorCategoryReadApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.category.alias}" timeout="${jd.jsf.common.timeout}" />
    <!--供应商商品查询接口-->
    <jsf:consumer id="vendorProductReadApiService" interface="com.jdi.isc.vc.soa.api.product.VendorProductReadApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.read.product.alias}" timeout="${jd.jsf.common.timeout}" />

    <!--供应商信息查询接口-->
    <jsf:consumer id="supplierAccountApiService" interface="com.jdi.isc.vc.soa.api.supplier.SupplierAccountApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.supplier.alias}" timeout="${jd.jsf.common.timeout}" />

    <!--翻译查询接口-->
    <jsf:consumer id="textTranslateApiService" interface="com.jdi.isc.vc.soa.api.translate.TextTranslateApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.translate.alias}" timeout="${jd.jsf.consumer.translate.timeout}" />

    <!--SKU查询接口-->
    <jsf:consumer id="vendorSkuReadApiService" interface="com.jdi.isc.vc.soa.api.product.VendorSkuReadApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.sku.alias}" timeout="${jd.jsf.common.timeout}" />

    <!-- 任务查询接口-->
    <jsf:consumer id="vendorTaskApiService" interface="com.jdi.isc.vc.soa.api.task.VendorTaskApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.task.alias}" timeout="${jd.jsf.common.timeout}" />

    <!-- 模板数据查询接口-->
    <jsf:consumer id="templateDataApiService" interface="com.jdi.isc.vc.soa.api.excel.TemplateDataApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.template.alias}" timeout="${jd.jsf.common.timeout}" />

    <!-- 一段运单数据查询接口-->
    <jsf:consumer id="purchaseOrderWaybillApiService" interface="com.jdi.isc.vc.soa.api.waybill.PurchaseOrderWaybillApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.waybill.alias}" timeout="${jd.jsf.common.longTimeout}" />

    <!-- 包裹-->
    <jsf:consumer id="purchaseOrderParcelReadApiService" interface="com.jdi.isc.vc.soa.api.parcel.PurchaseOrderParcelReadApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.parcel.read.alias}" timeout="${jd.jsf.common.timeout}" />

    <!-- 包裹写服务-->
    <jsf:consumer id="purchaseOrderParcelWriteApiService" interface="com.jdi.isc.order.center.api.parcel.PurchaseOrderParcelWriteApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.parcel.write.alias}" timeout="${jd.jsf.common.timeout}" />

    <!--采购单-->
    <jsf:consumer id="settlementPurchaseApiService" interface="com.jdi.isc.order.center.api.purchaseOrder.SettlementPurchaseApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.orderCenter.alias}"
                  timeout="${jd.jsf.consumer.orderCenter.timeout}" />

    <!-- 唛头文件服务 -->
    <jsf:consumer id="purchaseOrderParcelFileApiService" interface="com.jdi.isc.vc.soa.api.parcel.PurchaseOrderParcelFileApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.parcel.file.alias}" timeout="20000" />

    <!-- SPU写服务 -->
    <jsf:consumer id="iscProductSoaSpuWriteApiService" interface="com.jdi.isc.product.soa.api.spu.IscProductSoaSpuWriteApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.writeTimeout}" />

    <!-- SPU读服务 -->
    <jsf:consumer id="iscProductSoaSpuReadApiService" interface="com.jdi.isc.product.soa.api.spu.IscProductSoaSpuReadApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.longTimeout}" />

    <!-- 国家语言 -->
    <jsf:consumer id="iscProductSoaCountryLangApiService" interface="com.jdi.isc.product.soa.api.wimp.country.IscProductSoaCountryLangApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}" />

    <!-- 国家 -->
    <jsf:consumer id="iscProductSoaCountryApiService" interface="com.jdi.isc.product.soa.api.wimp.country.IscProductSoaCountryApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}" />

    <!-- 语言 -->
    <jsf:consumer id="iscProductSoaLangApiService" interface="com.jdi.isc.product.soa.api.wimp.lang.IscProductSoaLangApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}" />

    <!-- 货币 -->
    <jsf:consumer id="iscProductSoaCurrencyApiService" interface="com.jdi.isc.product.soa.api.wimp.currency.IscProductSoaCurrencyApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}" />

    <!-- 翻译 -->
    <jsf:consumer id="iscProductSoaTranslateApiService" interface="com.jdi.isc.product.soa.api.translate.IscProductSoaTranslateApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}" />

    <!-- sku读服务 -->
    <jsf:consumer id="iscProductSoaSkuReadApiService" interface="com.jdi.isc.product.soa.api.sku.IscProductSoaSkuReadApiService"
                  alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}" retries="2" protocol="jsf">
    </jsf:consumer>

    <!-- 动态生成模板服务 -->
    <jsf:consumer id="iscTaskTemplateApiService" interface="com.jdi.isc.task.center.api.template.IscTaskTemplateApiService"
                  alias="${jd.jsf.consumer.template.alias}" timeout="${jd.jsf.consumer.template.timeout}" retries="0" protocol="jsf">
    </jsf:consumer>

    <!-- 发票读服务 -->
    <jsf:consumer id="purchaseInvoiceReadApiService" interface="com.jdi.isc.order.center.api.finance.purchaseInvoice.PurchaseInvoiceReadApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.invoice.alias}"
                  timeout="${jd.jsf.consumer.invoice.timeout}" >
    </jsf:consumer>

    <!-- 发票写服务 -->
    <jsf:consumer id="purchaseInvoiceApiService" interface="com.jdi.isc.order.center.api.finance.purchaseInvoice.PurchaseInvoiceApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.invoice.alias}"
                  timeout="${jd.jsf.consumer.invoice.timeout}" >
    </jsf:consumer>

    <!-- isc库存写服务-->
    <jsf:consumer id="iscProductSoaStockWriteApiService" interface="com.jdi.isc.product.soa.api.stock.IscProductSoaStockWriteApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.stock.alias}" timeout="${jd.jsf.consumer.stock.timeout}" />

    <!-- vc首页供应商帮助中心 -->
    <jsf:consumer id="supplierHelpCenterApiService" interface="com.jdi.isc.vc.soa.api.help.SupplierHelpCenterApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.consumer.invoice.timeout}" >
    </jsf:consumer>

    <!--vc首页供应商公告 -->
    <jsf:consumer id="supplierNoticeApiService" interface="com.jdi.isc.vc.soa.api.notice.SupplierNoticeApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}" >
    </jsf:consumer>

    <jsf:consumer id="vcHomePageReadApiService"
                  interface="com.jdi.isc.order.center.api.supplier.VcHomePageReadApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}" >
    </jsf:consumer>


    <jsf:consumer id="supplierConfigApiService" interface="com.jdi.isc.vc.soa.api.supplier.SupplierConfigApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.supplier.alias}" timeout="${jd.jsf.common.timeout}" >
    </jsf:consumer>

    <!--  pdf打印  -->
    <jsf:consumer id="printPdfService" interface="com.jdi.isc.task.center.api.print.PrintPdfService"
                  alias="${jd.jsf.consumer.cloudPrint.alias}" timeout="${jd.jsf.consumer.cloudPrint.timeout}"/>


    <!--  结算单列表  -->
    <jsf:consumer id="supplierSettlementReadApiService" interface="com.jdi.isc.order.center.api.settlement.SupplierSettlementReadApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}" >
    </jsf:consumer>


    <!-- sku采购价格写服务 -->
    <jsf:consumer id="iscSkuPriceWriteApiService"
                  interface="com.jdi.isc.product.soa.api.price.supplierPrice.IscSkuPriceWriteApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- sku采购价格读服务 -->
    <jsf:consumer id="iscSkuPriceReadApiService"
                  interface="com.jdi.isc.product.soa.api.price.supplierPrice.IscSkuPriceReadApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.longTimeout}">
    </jsf:consumer>

    <!-- 类目 -->
    <jsf:consumer id="jdiIscSoaCategoryApiService"
                  interface="com.jdi.isc.product.soa.api.wimp.category.CategoryApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 品牌服务 -->
    <jsf:consumer id="brandApiService" interface="com.jdi.isc.product.soa.api.wimp.brand.BrandApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}"
                  serialization="hessian" />
    <!-- 寄售服务 -->
    <jsf:consumer id="forecastOrderApiService"
                  interface="com.jdi.isc.order.center.api.forecast.ForecastOrderApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 库存服务 -->
    <jsf:consumer id="iscSkuStockManageApiService"
                  interface="com.jdi.isc.product.soa.stock.sku.IscSkuStockManageApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>


    <!-- 库存服务 -->
    <jsf:consumer id="purchaseOrderReadApiService"
                  interface="com.jdi.isc.order.center.api.purchaseOrderRead.PurchaseOrderReadApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 导出/下载交接单 -->
    <jsf:consumer id="purchaseOrderWaybillReadApiService"
                  interface="com.jdi.isc.fulfillment.soa.api.purchaseWaybill.PurchaseOrderWaybillReadApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 导出/下载交接单 -->
    <jsf:consumer id="parcelWriteApiService"
                  interface="com.jdi.isc.fulfillment.soa.api.parcel.ParcelWriteApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 仓信息 -->
    <jsf:consumer id="supplierWarehouseReadApiService"
                  interface="com.jdi.isc.vc.soa.api.supplier.SupplierWarehouseReadApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>


    <!-- 仓信息 -->
    <jsf:consumer id="iscWarehouseReadApiService"
                  interface="com.jdi.isc.product.soa.api.warehouse.IscWarehouseReadApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 仓信息 -->
    <jsf:consumer id="forecastPurchaseOrderPrintApiService"
                  interface="com.jdi.isc.task.center.api.forecast.ForecastPurchaseOrderPrintApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 仓信息 -->
    <jsf:consumer id="purchaseOrderWaybillWriteApiService"
                  interface="com.jdi.isc.fulfillment.soa.api.purchaseWaybill.PurchaseOrderWaybillWriteApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <jsf:consumer id="parcelFileReadApiService"
                  interface="com.jdi.isc.fulfillment.soa.api.parcel.ParcelFileReadApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>
</beans>