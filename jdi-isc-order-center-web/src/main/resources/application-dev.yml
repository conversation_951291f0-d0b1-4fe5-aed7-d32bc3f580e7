infrastructure:
  jsfInvokeLog: true
  jsfInvokeLogLevel: INFO
  jsfValidate: true
  adapterInvokeLog: true
  adapterInvokeLogLevel: DEBUG
  adapterUmpPrefix: depend-
  invokeUmpPrefix: dev-

ducc:
  token: 58dd05be285e403fbc1c6b7d4bdbf403
  hostPort: test.ducc.jd.local
  config: isc-order
  profile: test
  namespace: international
  application: jdos_eone-jdi-isc-order-center

jsf:
  registry:
    index: test.i.jsf.jd.local
  consumer:
    kaOrderExport:
      timeout: 10000
      alias: std-jc-dev
    jdihubWms:
      timeout: 10000
      alias: test:1.0
    wiopApi:
      timeout: 10000
      alias: test:1.0
    cloudPrint:
      alias: yf
      timeout: 10000
    jdihub:
      timeout: 1222
      alias: xxx
    common:
      alias: test:1.0
      timeout: 5000
      longTimeOut: 5000
      veryLongTimeOut: 15000
    price:
      alias: test:1.0
      timeout: 10000
    ware:
      ept:
        alias: test:1.0
        timeout: 2000
    order:
      KaLogisticsBillQuery:
        alias: yf
        timeout: 2000
      wiop:
        alias: test:1.0
        timeout: 2000
      ept:
        alias: test:1.0
        timeout: 2000
      iop:
        alias: test:1.0
        timeout: 2000
      iopQuery:
        alias: test:1.0
        timeout: 2000
    ept:
      stock:
        alias: EPT_STOCK_LF_TEST
        timeout: 10000
        token: ept_stock_token
    freight:
      wiop:
        alias: test:1.0
        timeout: 2000
    orderCost:
      alias: test:1.0
      timeout: 2000
    account:
      alias: test:1.0
      timeout: 2000
    aggregate:
      alias: test:1.0
      timeout: 2000
    productSoa:
      alias: test:1.0
      timeout: 2000
    xbp:
      common:
        alias: test:1.0
        timeout: 2000
    ots:
      orderResource:
        alias: otsOrderBank-test
        timeout: 30000
    collectlost:
      collectOrderService:
        alias: cla_jsf_test:0.0.1
        timeout: 30000
    oderMiddle:
      alias: test:1.0
      timeout: 2000
    settleDataOpen:
      group: clearing_cn_test
      clientName: J-dos-jdi-isc-product-soa
      authToken: c399b6ca-47ec-439b-ac14-6fef86918857
      alias: clearing_cn_test
    fsp:
      alias: fspcube
      token: 123456
    fmscg:
      alias: fmscg_test_zy
      token: fmscg_test_zy
    overseas:
      invoice:
        alias: jdi-vn-overseas-invoice:0.0.1:uat
        timeout: 5000
        token: JDI-VN-invoice:0.0.1:uat
    fulfillment:
      alias: 1
      timeout: 5000
    fspinvoice:
      alias: fspinv
      timeout: 5000
      token: ZNNWAW52DG9RZW4
    vc:
      common:
        alias: test:1.0
        timeout: 2000
    jrPaySplit:
      alias: paySplit_test
      timeout: 15000
    exchangeRate:
      alias: test:1.0
      timeout: 5000
    operatePayResource:
      alias: otsOrderBank-test
      timeout: 5000
    fulfillmentTime:
      alias: test:1.0
      timeout: 5000
    orderoutserver:
      alias: cornucopia_test
      timeout: 5000
      token: 1d45refund954as
    frontStore:
      timeout: 10000
      alias: test
    quotaManageExport:
      alias: std-jc-test-1
      timeout: 5000
      token: 1d45refund954as
    productSoaRead:
      longTimeOut: 10000

  provider:
    common:
      alias: test:1.0
      timeout: 5000
      longTimeOut: 15000
    order:
      read:
        alias: test:1.0
        timeout: 2000
      wiop:
        alias: test:1.0
        timeout: 2000
      waybill:
        alias: test:1.0
        timeout: 2000
      print:
        alias: test:1.0
        timeout: 2000
    purchaseOrder:
      order:
        timeout: 2000
        alias: test:1.0
      parcel:
        alias: test:1.0
        timeout: 2000
    invoice:
      alias: test:1.0
      timeout: 10000
    idGenerator:
      alias: test:1.0
      timeout: 3000
    customerInvoice:
      alias: test:1.0
      timeout: 2000

jimdb:
  serviceEndpoint: http://test.cfs.jim.jd.local
  ioThreadPoolSize: 5
  computationThreadPoolSize: 5
  requestQueueSize: 100000

jmq:
  producer:
    order:
      app: jdiiscordercenter
      pwd: ca28064c67f64ecf969b03c7512eadea
  consumer:
    order:
      app: orderLocalTestGroup
      pwd: d7b23506877d48dcaddd28d2877d876b
    payIscSynThird:
      app: jdiIscPayCenterPre
      pwd: c9c8f990511f49d480d91235380c9f90
    customs:
      app: jdiiscCustomsCenter
      pwd: 638149683adc4f2da503e8cb2da1485c
    orderSnapshot:
      app: jdiIscOrderSnapshot
      pwd: xxx
    purchaseOrder:
      app: xxx
      pwd: xxx
    afterSalesSub:
      updateStatus: xxxxx
jmq2:
  address: jmq-testcluster.jd.local:50088
  product:
    order:
      app: thjposwork
      pwd: A2CB4F5F

topic:
  producer:
    order:
      submit: jdi_isc_order_center_submit_msg_test
      updateStatus: jdi_isc_order_center_status_update_msg_test
      split: jdi_isc_order_split_msg_test
      preSplit: jdi_isc_order_pre_split_msg_test
      paymentFinish: jdi_isc_order_payment_finish_msg_test
      waitPayment: jdi_isc_order_payment_wait_msg_test
      waybill: jdi_isc_order_waybill_status_msg_test
      invoiceApplyCreate: jdi_isc_customer_invoice_create_msg_test
      updateExtendInfo: jdi_isc_order_extend_update_test
      customerInvoice: xxx
      updateInfo: jdi_isc_order_update_info_msg_test
      preSplitTree: xxx
      refund: jdi_isc_order_refund_msg_test
    orderWaybill:
      created: jdi_isc_order_waybill_created_msg_test
    purchaseOrder:
      create: jdi_isc_purchase_order_status_create_msg_test
      updateStatus: jdi_isc_purchase_order_status_update_msg_test
      split: jdi_isc_purchase_order_split_msg_test
      preSplit: jdi_isc_purchase_order_preSplit_msg_test
      waybill: jdi_isc_purchase_waybill_status_msg_test
      invoiceUpdate: jdi_isc_purchase_invoice_update_msg_test
      invoiceStatusUpdate: jdi_isc_purchase_invoice_status_update_msg_test
      invoiceCreate: jdi_isc_purchase_invoice_create_msg_test
      updateInfo: test
    applyOrder:
      create: jdi_isc_apply_order_create_msg_test
      audit: jdi_isc_apply_order_audit_msg_test
    orderStock:
      occupyReturn: jdi_isc_order_stock_occupyReturn_msg_test
      stockReturn: jdi_isc_order_stock_stockReturn_msg_test
    skuStock:
      update: jdi_isc_order_skuStock_update_msg_test
    task:
      mail: jdi_isc_mail_send_topic_test
    stockInOutOrder:
      updateStatus: jdi_isc_stockInOut_order_status_update_msg_test
      skuStock:
        update: jdi_isc_stockInOut_order_skuStock_update_msg_test
    customs:
      customsProductUpdate: jdi_isc_customs_product_update_msg_test
    afterSales:
      updateStatus: jdi_isc_after_sales_update_msg_test
    forecastOrder:
      updateStatus: jdi_isc_forecast_order_status_update_msg_test
      split: jdi_isc_forecast_order_split_msg_test


  consumer:
    order:
      OTSDuiZhang: OTS_DuiZhang_Success_test
      submit: jdi_isc_order_center_submit_msg_test
      log: jdi_isc_order_log_msg_test
      iopOrderSplit: B2B_Split_stop??????????
      iopOutStock: gpt_soa_B2B_OutStock_stop??????????
      popOutStorage: POP_OUT_STORAGE_stop??????????
      dropShipWayBillNotify: dropship_waybill_notify_stop??????????
      preSplit: jdi_isc_order_test_split_msg_stop_test
      updateStatus: jdi_isc_order_center_status_update_msg_test?????
      paymentFinish: jdi_isc_order_payment_finish_msg_test
      waitPayment: jdi_isc_order_payment_wait_msg_test
      invoiceApproval: OverseasInvoiceStatus
      invoiceApplyCreate: jdi_isc_customer_invoice_create_msg_test
      waybill: jdi_isc_order_waybill_status_msg_test
      waybillCreated: jdi_isc_order_waybill_created_msg_test
      updateExtendInfo: jdi_isc_order_extend_update_test
      jdiOrderLogisticsStatus: jdi_order_logistics_status_dev
      updateInfo: jdi_isc_order_update_info_msg_test
      split: jdi_isc_order_split_msg_test
    orderStock:
      occupyReturn: jdi_isc_order_stock_occupyReturn_msg_test
      stockReturn: jdi_isc_order_stock_stockReturn_msg_test
    purchaseOrder:
      updateStatus: jdi_isc_purchase_order_status_update_msg_test
      log: jdi_isc_purchase_order_log_msg_stop_test
      split: jdi_isc_purchase_order_split_msg_test
      preSplit: jdi_isc_purchase_order_preSplit_msg_test
      create: jdi_isc_purchase_order_status_create_msg_test
      waybill: jdi_isc_purchase_waybill_status_msg_test
      invoiceApproval: fsp_invoice_approval_status_notify_test111111
      invoiceUpdate: jdi_isc_purchase_invoice_update_msg_test
      invoiceCreate: jdi_isc_purchase_invoice_create_msg_test
      updateInfo: jdi_isc_purchase_order_update_info_msg_test
    orderWare:
      log: jdi_isc_order_ware_log_msg_stop_test
    applyOrder:
      create: jdi_isc_apply_order_create_msg_test222323
      audit: jdi_isc_apply_order_audit_msg_test
    orderPay:
      orderStatusUpdate: jdi_isc_order_center_status_update_msg_test
      waitPayment: jdi_isc_order_payment_wait_msg_test
      invoiceCreate: jdi_isc_purchase_invoice_create_msg_test
      orderSplit: jdi_isc_order_split_msg_test
      claimSettleBill: cla_settleBillAuto
      refund: jdi_isc_order_refund_msg_test
    fulfillment:
      stockUpOutStock: jdi_isc_fulfillment_order_out_stock_event_test
      stockUpInStock: jdi_isc_fulfillment_stock_up_in_stock_event_test
    skuStock:
      update: jdi_isc_order_skuStock_update_msg_test
    frontOrder:
      upInStock: jdi_isc_front_inbound_outbound_finish_test
      receiptVoucher: jdi_isc_front_receipt_voucher_audit_test
      orderWaybillPlan: xxx
      orderWayBillShip: xxx
    settlementOrder:
      create: requestStatusG11n
    forecastOrder:
      updateStatus: jdi_isc_forecast_order_status_update_msg_test
      split: jdi_isc_forecast_order_split_msg_test
      log: jdi_isc_forecast_order_log_msg_test
    customs:
      customsProductUpdate: jdi_isc_customs_product_update_msg_test
      submitOrder: jdi_isc_order_center_submit_msg_test
      skuChangeMsg: jdi_isc_sku_change_msg_test
    ofc:
      orderSplit: jdi_isc_order_split_msg_test

    afterSalesSub:
      updateStatus: xxxxx
      log: jdi_isc_after_sales_sub_log_msg_test
    afterSales:
      updateStatus: jdi_isc_after_sales_update_msg_test
      log: jdi_isc_after_sales_order_log_msg_test






order:
  country:
    config: '{"TH":{"invoiceType":14,"sellerTariff":"0135567002946","ouId":"63591","ouName":"JINGDONG INDUSTRIALS (THAILAND) CO.,LTD.","checkIncludeTaxTotalPrice":true,"checkExcludingTaxTotalPrice":true,"useCustomerExcludingTaxTotalPrice":true,"useCustomerIncludeTaxTotalPrice":true,"autoPushSettlementOpen":true,"autoPushSettlementDate":"2024-10-24 00:00:00","customerInvoiceUseDb":false,"customerInvoiceCountryVO":{"srcSystem":"JDI-VN","applyUser":"thuhuong.hoang1","invType":2,"dataType":2,"sellerName":"JINGDONG INDUSTRIALS (THAILAND) CO.,LTD.","receiverCompanyProperty":"Head Office","currencyCode":"THB","iban":"002252922981","buyerCountryCode":"TH"}},"VN":{"invoiceType":14,"sellerTariff":"0110733264","ouId":"65511","ouName":"JINGDONG INDUSTRIALS SUPPLY CHAIN (VIETNAM) COMPANY LIMITED","checkIncludeTaxTotalPrice":false,"useCustomerIncludeTaxTotalPrice":true,"autoPushSettlementOpen":true,"autoPushSettlementDate":"2024-10-20 00:00:00","customerInvoiceUseDb":false}}'
  ledger:
    merchantId: xxxx
  tradeEnterprise: ''
  goodsCollectionWarehouse: ''
  ept:
    crossBorderStockUp: 1
  local:
    wareHouseInfo: 11
  splitPins: '1'
  preSplitOrder:
    pin: 11
    createOrderId: 122
  update:
    applicantErp: 1212
  change:
    info: '{"240715132100001":{"50000027518":80000070288}}'
  auth:
    system: '{"open-soa":"submitOrder,updateOrder,updateOrderStatus","product-center":"approvePurchaseInvoiceUpdatePoApply,updateOrderStatus,approveSettlement,submitStockInOutOrder","vc-soa":"updatePurchaseOrderStatus,queryPurchaseOrderOperateList,updatePurchaseOrdersStatus"}'
    orderThirdExtKey: 'sapOrderNo,sapOrderNo2'
    mkuThirdExtKey: 'lineNum'
    mkuThirdExtKeyJson: '{"ISPG-**************":[{"key":"lineNum","operate":"submitOrder","required":true}],"ISPG-**************":[{"key":"lineNum","operate":"submitOrder","required":true}]}'
    orderThirdExtKeyJson: '{"ISPG-**************":[{"key":"sapOrderNo","operate":"submitOrder","required":true}],"ISPG-**************":[{"key":"sapOrderNo","operate":"confirmOrder","required":true},{"key":"thrPurchaserAccount","operate":"submitOrder","required":false}]}'
  submitEpt:
    reqInfo: '{"ISPG-**************":"{\"iopPin\":\"广州晶东-A1\",\"tradeRoute\":10,\"sellerErp\":\"wangyanbin10\",\"periodType\":1,\"prepaymentRatio\":0,\"finalPaymentPeriod\":90}"}'
    thirdAccountCode: '1111'
  address:
    province: '{"*":{"*********":"罗勇"}}'
    city: '{"*":{"*********":"尼空帕塔纳"}}'
    county: '{"*":{"*********":"帕那尼空"}}'
    town: '{"*":{"0":""}}'
  clientCode:
    submitConfig: '{"ISPG-**************":{"processHandleListGroup":[["submitOrderTaxHandle","submitOrderInfoHandle"],["submitOrderFreightHandle"]],"checkOrderHandleListGroup":[["submitOrderAreaLimitHandle","submitOrderWareHandle","submitOrderFreightHandle","submitOrderThirdInfoHandle"]],"afterProcessHandleListGroup":[["submitOrderWareHandle","submitOrderConsigneeHandle","submitOrderThirdInfoHandle"],["submitOrderFreightHandle","submitOrderTaxHandle"],["submitOrderInfoHandle"]]},"testISPG-**************":{"processHandleListGroup":[["submitOrderFreightHandle"]],"checkOrderHandleListGroup":[["submitOrderAreaLimitHandle","submitOrderWareHandle","submitOrderFreightHandle"]],"afterProcessHandleListGroup":[["submitOrderWareHandle","submitOrderConsigneeHandle","submitOrderThirdInfoHandle"],["submitOrderFreightHandle"],["submitOrderInfoHandle"]]}}'
  rpc:
    cost: '{"mock":true,"mockData":"{\"freightReadResp\":{\"currency\":\"THB\",\"freight\":100.0001,\"itemList\":[{\"currency\":\"THB\",\"freight\":0.0001,\"quoteItemId\":2,\"quoteNames\":{\"EN\":\"运费名称-EN\",\"TH\":\"运费名称-TH\"},\"unitType\":1},{\"currency\":\"THB\",\"freight\":100.0000,\"quoteItemId\":1,\"quoteNames\":{\"EN\":\"运费名称-EN\",\"TH\":\"运费名称-TH\"},\"unitType\":1}],\"quoteTemplateId\":7123}}"}'
  orderOperateStatus: '{"ISPG-20221115112609":{"submitOrder":{"status":1,"beforeStatus":null},"cancelOrder":{"status":2,"beforeStatus":[1,11,12]},"confirmOrder":{"status":3,"beforeStatus":[1,11,12]},"confirmReceive":{"status":99,"beforeStatus":[90]},"splitOrder":{"status":301,"beforeStatus":[3]},"submitEptOrder":{"status":31,"beforeStatus":[301,32]},"cancelInnerOrder":{"status":32,"beforeStatus":[31]},"confirmInnerOrder":{"status":33,"beforeStatus":[31]},"deliverySubmitted":{"status":90,"beforeStatus":[33]}},"ISPG-**************":{"submitOrder":{"status":1,"beforeStatus":null},"cancelOrder":{"status":2,"beforeStatus":[1,301]},"confirmOrder":{"status":3,"beforeStatus":[1]},"confirmReceive":{"status":99,"beforeStatus":[90]},"splitOrder":{"status":301,"beforeStatus":[3]},"submitEptOrder":{"status":31,"beforeStatus":[301,32]},"cancelInnerOrder":{"status":32,"beforeStatus":[31]},"confirmInnerOrder":{"status":33,"beforeStatus":[31]},"deliverySubmitted":{"status":90,"beforeStatus":[33]}},"ISPG-**************":{"submitOrder":{"status":1,"beforeStatus":null},"cancelOrder":{"status":2,"beforeStatus":[1,11,12]},"confirmOrder":{"status":3,"beforeStatus":[1,11,12]},"confirmReceive":{"status":99,"beforeStatus":[90]},"splitOrder":{"status":301,"beforeStatus":[3]},"submitEptOrder":{"status":31,"beforeStatus":[301,32]},"cancelInnerOrder":{"status":32,"beforeStatus":[31]},"confirmInnerOrder":{"status":33,"beforeStatus":[31]},"deliverySubmitted":{"status":90,"beforeStatus":[33]}},"ISPG-**************":{"submitOrder":{"status":1,"beforeStatus":null},"cancelOrder":{"status":2,"beforeStatus":[1]},"confirmOrder":{"status":3,"beforeStatus":[1]},"confirmReceive":{"status":99,"beforeStatus":[33,31]},"splitOrder":{"status":301,"beforeStatus":[3]},"submitEptOrder":{"status":31,"beforeStatus":[301,32]},"cancelInnerOrder":{"status":32,"beforeStatus":[31]},"confirmInnerOrder":{"status":33,"beforeStatus":[31]},"deliverySubmitted":{"status":90,"beforeStatus":[3,31,33]}},"ISPG-**************":{"submitOrder":{"status":1,"beforeStatus":null},"cancelOrder":{"status":2,"beforeStatus":[1]},"confirmOrder":{"status":3,"beforeStatus":[1]},"confirmReceive":{"status":99,"beforeStatus":[33,31]},"splitOrder":{"status":301,"beforeStatus":[3]},"submitEptOrder":{"status":31,"beforeStatus":[301,32]},"cancelInnerOrder":{"status":32,"beforeStatus":[31]},"confirmInnerOrder":{"status":33,"beforeStatus":[31]},"deliverySubmitted":{"status":90,"beforeStatus":[3,31,33]}},"ISPG-**************":{"submitOrder":{"status":1,"beforeStatus":null},"priceConfirm":{"status":11,"beforeStatus":[1]},"userConfirm":{"status":12,"beforeStatus":[11]},"cancelOrder":{"status":2,"beforeStatus":[1,11,12]},"confirmOrder":{"status":3,"beforeStatus":[11,12]},"confirmReceive":{"status":99,"beforeStatus":[90]},"splitOrder":{"status":301,"beforeStatus":[3]},"submitEptOrder":{"status":31,"beforeStatus":[301,32]},"cancelInnerOrder":{"status":32,"beforeStatus":[31]},"confirmInnerOrder":{"status":33,"beforeStatus":[31]},"deliverySubmitted":{"status":90,"beforeStatus":[33]}},"ISPG-**************":{"submitOrder":{"status":1,"beforeStatus":null},"priceConfirm":{"status":11,"beforeStatus":[1]},"userConfirm":{"status":12,"beforeStatus":[11]},"cancelOrder":{"status":2,"beforeStatus":[1,11,12]},"confirmOrder":{"status":3,"beforeStatus":[11,12]},"confirmReceive":{"status":99,"beforeStatus":[90]},"splitOrder":{"status":301,"beforeStatus":[3]},"submitEptOrder":{"status":31,"beforeStatus":[301,32]},"cancelInnerOrder":{"status":32,"beforeStatus":[31]},"confirmInnerOrder":{"status":33,"beforeStatus":[31]},"deliverySubmitted":{"status":90,"beforeStatus":[33]}},"ISPG-**************":{"submitOrder":{"status":1,"beforeStatus":null},"cancelOrder":{"status":2,"beforeStatus":[1]},"confirmOrder":{"status":3,"beforeStatus":[1]},"confirmReceive":{"status":99,"beforeStatus":[33,90]},"splitOrder":{"status":301,"beforeStatus":[3]},"submitEptOrder":{"status":31,"beforeStatus":[301,32]},"cancelInnerOrder":{"status":32,"beforeStatus":[31]},"confirmInnerOrder":{"status":33,"beforeStatus":[31]},"deliverySubmitted":{"status":90,"beforeStatus":[33]}},"ISPG-**************":{"submitOrder":{"status":1,"beforeStatus":null},"priceConfirm":{"status":11,"beforeStatus":[1]},"userConfirm":{"status":12,"beforeStatus":[11]},"cancelOrder":{"status":2,"beforeStatus":[1,11,12,3]},"confirmOrder":{"status":3,"beforeStatus":[1,11,12]},"confirmReceive":{"status":99,"beforeStatus":[90,31]},"splitOrder":{"status":301,"beforeStatus":[3]},"submitEptOrder":{"status":31,"beforeStatus":[301,32,1]},"cancelInnerOrder":{"status":32,"beforeStatus":[31]},"confirmInnerOrder":{"status":33,"beforeStatus":[31]},"deliverySubmitted":{"status":90,"beforeStatus":[33]}},"ISPG-**************":{"submitOrder":{"status":1,"beforeStatus":null},"cancelOrder":{"status":2,"beforeStatus":[1]},"confirmOrder":{"status":3,"beforeStatus":[1]},"confirmReceive":{"status":99,"beforeStatus":[33,90]},"splitOrder":{"status":301,"beforeStatus":[3]},"submitEptOrder":{"status":31,"beforeStatus":[301,32]},"cancelInnerOrder":{"status":32,"beforeStatus":[31]},"confirmInnerOrder":{"status":33,"beforeStatus":[31]},"deliverySubmitted":{"status":90,"beforeStatus":[31,33]}},"test-WIOP_ISPG-**************":{"submitOrder":{"status":1,"beforeStatus":null},"cancelOrder":{"status":2,"beforeStatus":[1]},"confirmOrder":{"status":3,"beforeStatus":[1]},"confirmReceive":{"status":99,"beforeStatus":[33,90]},"splitOrder":{"status":301,"beforeStatus":[3]},"submitEptOrder":{"status":31,"beforeStatus":[301,32]},"cancelInnerOrder":{"status":32,"beforeStatus":[31]},"confirmInnerOrder":{"status":33,"beforeStatus":[31]},"deliverySubmitted":{"status":90,"beforeStatus":[31,33]}}}'
  tradeRouteRelation: '{"10":{"iopPin":"广州晶东-A1"},"626":{"iopPin":"JD香港内采1"},"2295":{"iopPin":"JDi_Thailand1"},"2249":{"iopPin":"广州晶东-A1"},"ISPG-**************":{"tradeRoute":10,"storeId":24682489,"iopPin":"广州晶东-A1","sellerErp":"wangyanbin10","periodType":1,"prepaymentRatio":0,"finalPaymentPeriod":90,"dcId":24682489},"ISPG-**************":{"iopPin":"JDi_Thailand1","tradeRoute":2295,"sellerErp":"wangyanbin10","periodType":1,"prepaymentRatio":0,"finalPaymentPeriod":90,"storeId":24688209,"dcId":24688209},"ISPG-**************":{"iopPin":"JDi_Thailand1","tradeRoute":2295,"sellerErp":"wangyanbin10","periodType":1,"prepaymentRatio":0,"finalPaymentPeriod":90,"storeId":24688209,"dcId":24688209},"ISPG-**************":{"iopPin":"广州晶东-A1","tradeRoute":10,"sellerErp":"wangyanbin10","periodType":1,"prepaymentRatio":0,"finalPaymentPeriod":90,"storeId":24682489,"dcId":24682489},"ISPG-**************":{"iopPin":"广州晶东-A1","tradeRoute":10,"sellerErp":"wangyanbin10","periodType":1,"prepaymentRatio":0,"finalPaymentPeriod":90,"storeId":24682489,"dcId":24682489},"ISPG-**************":{"iopPin":"广州晶东-A1","tradeRoute":2249,"sellerErp":"wangyanbin10","periodType":1,"prepaymentRatio":0,"finalPaymentPeriod":90,"storeId":24688213,"dcId":24688213},"test-ISPG-**************":{"iopPin":"广州晶东-A1","tradeRoute":10,"sellerErp":"wangyanbin10","periodType":1,"prepaymentRatio":0,"finalPaymentPeriod":90,"storeId":24682489,"dcId":24682489},"ISPG-**************":{"iopPin":"广州晶东-A1","tradeRoute":10,"sellerErp":"wangyanbin10","periodType":1,"prepaymentRatio":0,"finalPaymentPeriod":90,"storeId":24682489,"dcId":24682489},"ISPG-**************":{"iopPin":"广州晶东-A1","tradeRoute":2249,"sellerErp":"wangyanbin10","periodType":1,"prepaymentRatio":0,"finalPaymentPeriod":90,"storeId":24688213,"dcId":24688213},"ISPG-**************":{"remark":"比亚迪香港邮件下单","iopPin":"广州晶东-A1","tradeRoute":10,"sellerErp":"wangyanbin10","periodType":1,"prepaymentRatio":0,"finalPaymentPeriod":90,"storeId":24682489,"dcId":24682489},"广州晶东-A1":{"iopPin":"广州晶东-A1","tradeRoute":10,"sellerErp":"wangyanbin10","periodType":1,"prepaymentRatio":0,"finalPaymentPeriod":90,"storeId":24682489,"dcId":24682489},"香港京东":{"iopPin":"JD香港内采1","tradeRoute":626,"sellerErp":"wangyanbin10","periodType":1,"prepaymentRatio":0,"finalPaymentPeriod":90,"storeId":2468994,"dcId":2468994},"泰国工业":{"iopPin":"JDi_Thailand1","tradeRoute":2295,"sellerErp":"wangyanbin10","periodType":1,"prepaymentRatio":0,"finalPaymentPeriod":90,"storeId":24688209,"dcId":24688209},"深圳工业":{"iopPin":"广州晶东-A1","tradeRoute":2249,"sellerErp":"wangyanbin10","periodType":1,"prepaymentRatio":0,"finalPaymentPeriod":90,"storeId":24688213,"dcId":24688213}}'
  testPins: ',caochuan-2,'
  iopStoreAddressMapping:
  internalTradeAutoConfirm: ''
  splitIop:
    retry:
      threshold: 7
  orderConfirm:
    mock:
  iopStoreAddressMap: 11
  xbp:
    userName: jdi_wimp
    sign: 2919277adc
    innerConfirmConfig: 12121
purchaseOrder:
  stock:
    iopInfo: '{ "TH": { "tradeEnterprise": "JDI-TH", "goodsCollectionWarehouse": "GDFG" }, "VN": { "tradeEnterprise": "JDI-VN", "goodsCollectionWarehouse": "GDFG" }, "BR": { "tradeEnterprise": "JDI-BR", "goodsCollectionWarehouse": "101" }, "MY": { "tradeEnterprise": "JDI-MY", "goodsCollectionWarehouse": "101" }, "HU": { "tradeEnterprise": "JDI-HU", "goodsCollectionWarehouse": "GDFG" }, "ID": { "tradeEnterprise": "JDI-ID", "goodsCollectionWarehouse": "GDFG" }, "ISPG-**************": { "remark": "金门", "tradeEnterprise": "JDI-SZ", "goodsCollectionWarehouse": "GDFG" }, "CN": { "remark": "全球氟和其他", "tradeEnterprise": "JDI-SZ", "goodsCollectionWarehouse": "GDFG" }, "ISPG-20250105125151": { "remark": "印尼格林美", "tradeEnterprise": "JDI-SZ", "goodsCollectionWarehouse": "IDGLM" }, "ISPG-20250105123826": { "remark": "印尼格林美", "tradeEnterprise": "JDI-SZ", "goodsCollectionWarehouse": "IDGLM" }, "ISPG-20250105123221": { "remark": "印尼格林美", "tradeEnterprise": "JDI-SZ", "goodsCollectionWarehouse": "IDGLM" }, "ISPG-20250103120257": { "remark": "印尼格林美", "tradeEnterprise": "JDI-SZ", "goodsCollectionWarehouse": "IDGLM" }, "ISPG-20250214165124": { "remark": "印尼格林美", "tradeEnterprise": "JDI-SZ", "goodsCollectionWarehouse": "IDGLM" }, "ISPG-**************": { "remark": "caochuan-V2", "tradeEnterprise": "GZJD", "goodsCollectionWarehouse": "101" }, "ISPG-20240818124409": { "remark": "BR-BYD采购账号", "tradeEnterprise": "JDI-BR", "goodsCollectionWarehouse": "101" }, "ISPG-20250305213354": { "remark": "BYD_UZ_Buyer_01", "tradeEnterprise": "JDI-SZ", "goodsCollectionWarehouse": "XABQ" } }'
  orderOperateStatus: '{"vc-soa":{"receive":{"status":5,"beforeStatus":[3],"operateDescMap":{"zh":"接单","en":"receive","vi":"Đơn hang đa nhận","th":"รับออเดอร์แล้ว"}},"shipped":{"status":80,"beforeStatus":[5],"operateDescMap":{"zh":"发货","en":"shipped","vi":"đã giao","th":"ส่งออก"}},"shippedBatch":{"status":80,"beforeStatus":[5],"operateDescMap":{"zh":"发货","en":"shipped","vi":"đã giao","th":"ส่งออก"}}},"product-center":{"confirm":{"status":3,"beforeStatus":[1],"operateDescMap":{"zh":"确认","en":"confirm","vi":"xác nhận","th":"ยืนยัน"}},"cancel":{"status":2,"beforeStatus":[0,1,12],"operateDescMap":{"zh":"取消","en":"cancel","vi":"hủy","th":"ยกเลิก"}},"receive":{"status":5,"beforeStatus":[3],"operateDescMap":{"zh":"已接单","en":"receive","vi":"Đơn hang đa nhận","th":"รับออเดอร์แล้ว"}},"cancelInner":{"status":12,"beforeStatus":[1],"operateDescMap":{"zh":"取消内贸段","en":"cancel inner order","vi":"hủy","th":"ยกเลิก"}},"createInner":{"status":1,"beforeStatus":[0,12],"operateDescMap":{"zh":"创建内贸段","en":"create inner order","vi":"create inner order","th":"create inner order"}},"crossBorderConfirm":{"status":3,"beforeStatus":[1,3],"operateDescMap":{"zh":"确认内贸段","en":"confirm inner order","vi":"confirm inner order","th":"confirm inner order"}},"shipped":{"status":80,"beforeStatus":[3,5],"operateDescMap":{"zh":"已发货","en":"shipped","vi":"đã giao","th":"ส่งออก"}},"shippedBatch":{"status":80,"beforeStatus":[3,5],"operateDescMap":{"zh":"发货","en":"shipped","vi":"đã giao","th":"ส่งออก"}},"inputConsolidationWh":{"status":81,"beforeStatus":[80],"operateDescMap":{"zh":"集货仓收货","en":"inputConsolidationWarehouse","vi":"Nhập kho tập trung","th":"โกดังรวมข้อมูลนำเข้า"}},"inputWarehouse":{"status":90,"beforeStatus":[80,81],"operateDescMap":{"zh":"入仓","en":"inputWarehouse","vi":"đưa vào kho","th":"ขยายการเก็บ"}},"finish":{"status":99,"beforeStatus":[80,90],"operateDescMap":{"zh":"完成","en":"finish","vi":"finish","th":"finish"}}},"product-center-stockUp":{"confirm":{"status":3,"beforeStatus":[1],"operateDescMap":{"zh":"确认内贸段","en":"confirm inner order","vi":"confirm inner order","th":"confirm inner order"}},"cancel":{"status":2,"beforeStatus":[0,12],"operateDescMap":{"zh":"取消","en":"cancel","vi":"hủy","th":"ยกเลิก"}},"receive":{"status":5,"beforeStatus":[3],"operateDescMap":{"zh":"已接单","en":"receive","vi":"Đơn hang đa nhận","th":"รับออเดอร์แล้ว"}},"cancelInner":{"status":12,"beforeStatus":[1],"operateDescMap":{"zh":"取消内贸段","en":"cancel inner order","vi":"hủy","th":"ยกเลิก"}},"createInner":{"status":1,"beforeStatus":[0,12],"operateDescMap":{"zh":"创建内贸段","en":"create inner order","vi":"create inner order","th":"create inner order"}},"crossBorderConfirm":{"status":3,"beforeStatus":[1,3],"operateDescMap":{"zh":"确认内贸段","en":"confirm inner order","vi":"confirm inner order","th":"confirm inner order"}},"shipped":{"status":80,"beforeStatus":[3,5],"operateDescMap":{"zh":"已发货","en":"shipped","vi":"đã giao","th":"ส่งออก"}},"inputConsolidationWh":{"status":81,"beforeStatus":[80],"operateDescMap":{"zh":"集货仓收货","en":"inputConsolidationWarehouse","vi":"Nhập kho tập trung","th":"โกดังรวมข้อมูลนำเข้า"}},"inputWarehousePart":{"status":82,"beforeStatus":[81],"operateDescMap":{"zh":"部分入库","en":"inputWarehouse","vi":"đưa vào kho","th":"ขยายการเก็บ"}},"inputWarehouse":{"status":90,"beforeStatus":[81,82],"operateDescMap":{"zh":"全部入库","en":"inputWarehouse","vi":"đưa vào kho","th":"ขยายการเก็บ"}},"finish":{"status":99,"beforeStatus":[90],"operateDescMap":{"zh":"完成","en":"finish","vi":"finish","th":"finish"}}},"order-center":{"preSplitConfirm":{"status":3,"beforeStatus":[1],"operateDescMap":{"zh":"预拆单生效","en":"confirm","vi":"xác nhận","th":"ยืนยัน"}},"confirm":{"status":3,"beforeStatus":[303,1],"operateDescMap":{"zh":"确认","en":"confirm","vi":"xác nhận","th":"ยืนยัน"}},"cancel":{"status":2,"beforeStatus":[0,1,12,3,80],"operateDescMap":{"zh":"取消","en":"cancel","vi":"hủy","th":"ยกเลิก"}},"cancelManage":{"status":2,"beforeStatus":[0,1,3,12,80,5],"operateDescMap":{"zh":"运营取消","en":"cancel","vi":"hủy","th":"ยกเลิก"}},"receive":{"status":5,"beforeStatus":[3],"operateDescMap":{"zh":"已接单","en":"receive","vi":"Đơn hang đa nhận","th":"รับออเดอร์แล้ว"}},"cancelInner":{"status":302,"beforeStatus":[303,1],"operateDescMap":{"zh":"取消内贸段","en":"cancel inner order","vi":"hủy","th":"ยกเลิก"}},"createInner":{"status":303,"beforeStatus":[302],"operateDescMap":{"zh":"创建内贸段","en":"create inner order","vi":"create inner order","th":"create inner order"}},"shipped":{"status":80,"beforeStatus":[3,5],"operateDescMap":{"zh":"发货","en":"shipped","vi":"đã giao","th":"ส่งออก"}},"shippedBatch":{"status":80,"beforeStatus":[3,5],"operateDescMap":{"zh":"发货","en":"shipped","vi":"đã giao","th":"ส่งออก"}},"inputConsolidationWh":{"status":81,"beforeStatus":[80],"operateDescMap":{"zh":"已入集货仓","en":"inputWarehouse","vi":"đưa vào kho","th":"ขยายการเก็บ"}},"inputWarehousePart":{"status":82,"beforeStatus":[81,82],"operateDescMap":{"zh":"部分入库","en":"inputWarehouse","vi":"đưa vào kho","th":"ขยายการเก็บ"}},"inputWarehouse":{"status":90,"beforeStatus":[80,81,82],"operateDescMap":{"zh":"入库","en":"inputWarehouse","vi":"đưa vào kho","th":"ขยายการเก็บ"}},"finish":{"status":99,"beforeStatus":[80,90],"operateDescMap":{"zh":"完成","en":"finish","vi":"finish","th":"finish"}}}}'
  errorMsg: '{}'
  stockUp:
    warehouseId: '1014,1012'
  crossBroder:
    stockUp:
      finishTimes: 300000
    Direct:
      finishTimes: 1800000
  autoClose:
    pin: xubing82
forecastOrder:
  orderOperateStatus: '{"vc-soa":{"receive":{"status":5,"beforeStatus":[3],"operateDescMap":{"zh":"接单","en":"receive","vi":"Đơn hang đa nhận","th":"รับออเดอร์แล้ว"}},"shipped":{"status":80,"beforeStatus":[5],"operateDescMap":{"zh":"发货","en":"shipped","vi":"đã giao","th":"ส่งออก"}},"shippedBatch":{"status":80,"beforeStatus":[5],"operateDescMap":{"zh":"发货","en":"shipped","vi":"đã giao","th":"ส่งออก"}}},"product-center":{"confirm":{"status":3,"beforeStatus":[1],"operateDescMap":{"zh":"确认","en":"confirm","vi":"xác nhận","th":"ยืนยัน"}},"cancel":{"status":2,"beforeStatus":[0,1,12],"operateDescMap":{"zh":"取消","en":"cancel","vi":"hủy","th":"ยกเลิก"}},"receive":{"status":5,"beforeStatus":[3],"operateDescMap":{"zh":"已接单","en":"receive","vi":"Đơn hang đa nhận","th":"รับออเดอร์แล้ว"}},"cancelInner":{"status":12,"beforeStatus":[1],"operateDescMap":{"zh":"取消内贸段","en":"cancel inner order","vi":"hủy","th":"ยกเลิก"}},"createInner":{"status":1,"beforeStatus":[0,12],"operateDescMap":{"zh":"创建内贸段","en":"create inner order","vi":"create inner order","th":"create inner order"}},"crossBorderConfirm":{"status":3,"beforeStatus":[1,3],"operateDescMap":{"zh":"确认内贸段","en":"confirm inner order","vi":"confirm inner order","th":"confirm inner order"}},"shipped":{"status":80,"beforeStatus":[3,5],"operateDescMap":{"zh":"已发货","en":"shipped","vi":"đã giao","th":"ส่งออก"}},"shippedBatch":{"status":80,"beforeStatus":[3,5],"operateDescMap":{"zh":"发货","en":"shipped","vi":"đã giao","th":"ส่งออก"}},"inputConsolidationWh":{"status":81,"beforeStatus":[80],"operateDescMap":{"zh":"集货仓收货","en":"inputConsolidationWarehouse","vi":"Nhập kho tập trung","th":"โกดังรวมข้อมูลนำเข้า"}},"inputWarehouse":{"status":90,"beforeStatus":[80,81],"operateDescMap":{"zh":"入仓","en":"inputWarehouse","vi":"đưa vào kho","th":"ขยายการเก็บ"}},"finish":{"status":99,"beforeStatus":[80,90],"operateDescMap":{"zh":"完成","en":"finish","vi":"finish","th":"finish"}}},"product-center-stockUp":{"confirm":{"status":3,"beforeStatus":[1],"operateDescMap":{"zh":"确认内贸段","en":"confirm inner order","vi":"confirm inner order","th":"confirm inner order"}},"cancel":{"status":2,"beforeStatus":[0,12],"operateDescMap":{"zh":"取消","en":"cancel","vi":"hủy","th":"ยกเลิก"}},"receive":{"status":5,"beforeStatus":[3],"operateDescMap":{"zh":"已接单","en":"receive","vi":"Đơn hang đa nhận","th":"รับออเดอร์แล้ว"}},"cancelInner":{"status":12,"beforeStatus":[1],"operateDescMap":{"zh":"取消内贸段","en":"cancel inner order","vi":"hủy","th":"ยกเลิก"}},"createInner":{"status":1,"beforeStatus":[0,12],"operateDescMap":{"zh":"创建内贸段","en":"create inner order","vi":"create inner order","th":"create inner order"}},"crossBorderConfirm":{"status":3,"beforeStatus":[1,3],"operateDescMap":{"zh":"确认内贸段","en":"confirm inner order","vi":"confirm inner order","th":"confirm inner order"}},"shipped":{"status":80,"beforeStatus":[3,5],"operateDescMap":{"zh":"已发货","en":"shipped","vi":"đã giao","th":"ส่งออก"}},"inputConsolidationWh":{"status":81,"beforeStatus":[80],"operateDescMap":{"zh":"集货仓收货","en":"inputConsolidationWarehouse","vi":"Nhập kho tập trung","th":"โกดังรวมข้อมูลนำเข้า"}},"inputWarehousePart":{"status":82,"beforeStatus":[81],"operateDescMap":{"zh":"部分入库","en":"inputWarehouse","vi":"đưa vào kho","th":"ขยายการเก็บ"}},"inputWarehouse":{"status":90,"beforeStatus":[81,82],"operateDescMap":{"zh":"全部入库","en":"inputWarehouse","vi":"đưa vào kho","th":"ขยายการเก็บ"}},"finish":{"status":99,"beforeStatus":[90],"operateDescMap":{"zh":"完成","en":"finish","vi":"finish","th":"finish"}}},"order-center":{"preSplitConfirm":{"status":3,"beforeStatus":[1],"operateDescMap":{"zh":"预拆单生效","en":"confirm","vi":"xác nhận","th":"ยืนยัน"}},"confirm":{"status":3,"beforeStatus":[303,1],"operateDescMap":{"zh":"确认","en":"confirm","vi":"xác nhận","th":"ยืนยัน"}},"cancel":{"status":2,"beforeStatus":[0,1,12,3,80],"operateDescMap":{"zh":"取消","en":"cancel","vi":"hủy","th":"ยกเลิก"}},"cancelManage":{"status":2,"beforeStatus":[0,1,3,12,80,5],"operateDescMap":{"zh":"运营取消","en":"cancel","vi":"hủy","th":"ยกเลิก"}},"receive":{"status":5,"beforeStatus":[3],"operateDescMap":{"zh":"已接单","en":"receive","vi":"Đơn hang đa nhận","th":"รับออเดอร์แล้ว"}},"cancelInner":{"status":302,"beforeStatus":[303,1],"operateDescMap":{"zh":"取消内贸段","en":"cancel inner order","vi":"hủy","th":"ยกเลิก"}},"createInner":{"status":303,"beforeStatus":[302],"operateDescMap":{"zh":"创建内贸段","en":"create inner order","vi":"create inner order","th":"create inner order"}},"shipped":{"status":80,"beforeStatus":[3,5],"operateDescMap":{"zh":"发货","en":"shipped","vi":"đã giao","th":"ส่งออก"}},"shippedBatch":{"status":80,"beforeStatus":[3,5],"operateDescMap":{"zh":"发货","en":"shipped","vi":"đã giao","th":"ส่งออก"}},"inputConsolidationWh":{"status":81,"beforeStatus":[80],"operateDescMap":{"zh":"已入集货仓","en":"inputWarehouse","vi":"đưa vào kho","th":"ขยายการเก็บ"}},"inputWarehousePart":{"status":82,"beforeStatus":[81,82],"operateDescMap":{"zh":"部分入库","en":"inputWarehouse","vi":"đưa vào kho","th":"ขยายการเก็บ"}},"inputWarehouse":{"status":90,"beforeStatus":[80,81,82],"operateDescMap":{"zh":"入库","en":"inputWarehouse","vi":"đưa vào kho","th":"ขยายการเก็บ"}},"finish":{"status":99,"beforeStatus":[80,90],"operateDescMap":{"zh":"完成","en":"finish","vi":"finish","th":"finish"}}}}'


stockInOutOrder:
  orderOperateStatus: '{ "order-center": { "issueStockWarehouse": { "status": 1, "beforeStatus": [ 0 ], "operateDescMap": { "zh": "下发中", "en": "Issuing", "vi": "Đang phát hành", "th": "กำลังออก" } }, "inBoundCallBack": { "status": 3, "beforeStatus": [ 1 ], "operateDescMap": { "zh": "待入库", "en": "Awaiting Inbound", "vi": "Đợi nhập kho", "th": "รอเข้าคลัง" } }, "outBoundCallBack": { "status": 2, "beforeStatus": [ 1 ], "operateDescMap": { "zh": "待出库", "en": "Awaiting Outbound", "vi": "Đợi xuất kho", "th": "รอออกจากคลัง" } }, "inputWarehousePart": { "status": 4, "beforeStatus": [ 3 ], "operateDescMap": { "zh": "部分入库", "en": "Partially Inbound", "vi": "Nhập kho một phần", "th": "บางส่วนเข้าคลัง" } }, "inputWarehouse": { "status": 6, "beforeStatus": [ 3, 4 ], "operateDescMap": { "zh": "全部入库", "en": "Fully Inbound", "vi": "Nhập kho đầy đủ", "th": "เข้าคลังทั้งหมด" } }, "outBoundShipFinished": { "status": 6, "beforeStatus": [ 2 ], "operateDescMap": { "zh": "出库完成", "en": "Outbound Completed", "vi": "Xuất kho hoàn thành", "th": "ออกจากคลังแล้ว" } }, "cancelOrder": { "status": 5, "beforeStatus": [ 1, 2, 3 ], "operateDescMap": { "zh": "已取消", "en": "Cancelled", "vi": "Đã hủy", "th": "ยกเลิกแล้ว" } } } }'

settlement:
  orderCanSettlementStatus: '[1,2,3,31,32,33,90,99]'
  purchaseOrderCanSettlementStatus: '[1,2,3,4,5]'

purchaseOrderWaybill:
  operateStatus: '{"product-center":{"inputWarehouse":{"status":2,"beforeStatus":[1],"operateDescMap":{"zh":"入库","en":"inputWarehouse","vi":"đưa vào kho","th":"ขยายการเก็บ"}}}}'

error:
  msg: '{"1001":"提单-请求频繁","1002":"提单-第三方订单id不唯一","1003":"提单-运费错误","1004":"提单-运费金额错误","1005":"提单-商品金额错误","1201":"提单-提交ept订单失败","2001":"修改订单状态-当前状态不支持该操作","2002":"取消内贸段订单，取消ept订单异常","2003":"确认内贸段订单，确认ept订单异常","2004":"确认内贸段订单，确认iop订单异常"}'
  message: '{"1":{"zh":"参数错误","th":"param error","vi":"param error","en":"param error"},"3":{"zh":"数据不存在","th":"data is null","vi":"data is null","en":"data is null"},"1001":{"zh":"请求频繁","th":"คำขอบอย่างรวดเร็ว","vi":"Yêu cầu thường xuyên","en":"Frequent requests"},"1002":{"zh":"第三方订单ID不唯一","th":"รหัสใบสั่งซื้อของ bên thứ ba không duy nhất","vi":"Mã đơn hàng bên thứ ba không duy nhất","en":"Third-party order ID not unique"},"1003":{"zh":"运费错误","th":"ค่าขนส่งผิดพลาด","vi":"Phí vận chuyển sai","en":"Shipping error"},"1004":{"zh":"运费金额错误","th":"จำนวนเงินที่ต้องชำระค่าขนส่งผิดพลาด","vi":"Số tiền phải trả cho phí vận chuyển sai","en":"Shipping amount error"},"1005":{"zh":"商品金额错误","th":"จำนวนเงินสินค้าผิดพลาด","vi":"Số tiền sản phẩm sai","en":"Product amount error"},"1006":{"zh":"商品金额为空","th":"ราคาสินค้าว่างเปล่า","vi":"Số tiền sản phẩm rỗng","en":"Product amount empty"},"1007":{"zh":"商品不可售","th":"สินค้าไม่สามารถขาย","vi":"Sản phẩm không được bán","en":"Product unavailable"},"1008":{"zh":"运费接口异常","th":"อินเตอร์เซียสของค่าจัดส่งผิดปกติ","vi":"Cổng thông tin vận chuyển sai","en":"Shipping interface abnormal"},"1009":{"zh":"总税金错误","th":"ภาษีรวมผิดพลาด","vi":"Tổng thuế sai","en":"Total tax error"},"1010":{"zh":"总金额错误","th":"จำนวนเงินรวมผิดพลาด","vi":"Số tiền tổng cộng sai","en":"Total amount error"},"1201":{"zh":"提交ept订单失败","th":"ส่งข้อมูลใบสั่งซื้อ ept ล้มเหลว","vi":"Gửi thông tin đơn hàng ept thất bại","en":"Submit ept order failed"},"1202":{"zh":"提交ept订单参数错误","th":"ข้อมูลส่งข้อมูลใบสั่งซื้อ ept ผิดปกติ","vi":"Thông tin gửi thông tin đơn hàng ept sai","en":"Submit ept order parameter error"},"2001":{"zh":"当前状态不支持该操作","th":"สถานะปัจจุบันไม่รองรับการดำเนินการนี้","vi":"Trạng thái hiện tại không hỗ trợ hành động này","en":"Current status does not support this operation"},"2002":{"zh":"取消内贸段订单，取消ept订单异常","th":"Confirm domestic segment order, confirm ept order abnormal","vi":"Confirm domestic segment order, confirm ept order abnormal","en":"Cancel domestic segment order, cancel ept order abnormal"},"2003":{"zh":"确认内贸段订单，确认ept订单异常","th":"Confirm domestic segment order, confirm ept order abnormal","vi":"Confirm domestic segment order, confirm ept order abnormal","en":"Confirm domestic segment order, confirm ept order abnormal"},"2004":{"zh":"确认内贸段订单，确认iop订单异常","th":"Confirm domestic segment order, confirm iop order abnormal","vi":"Confirm domestic segment order, confirm iop order abnormal","en":"Confirm domestic segment order, confirm iop order abnormal"},"5001":{"zh":"创建采购单时，商品已下架","th":"Create purchase order when product is out of stock","vi":"Create purchase order when product is out of stock","en":"Create purchase order when product is out of stock"},"5002":{"zh":"采购单数据已存在","th":"ข้อมูลใบสั่งซื้อสินค้ามีอยู่","vi":"Thông tin đơn hàng mua đã tồn tại","en":"Purchase order data already exists"},"6002":{"zh":"结算数据已存在","th":"ข้อมูลการตรวจสอบได้มีอยู่แล้ว","vi":"Thông tin thanh toán đã tồn tại","en":"Settlement data already exists"}}'

account:
  eptMapping: '[{"jdiContractCode":"ISPG-**************","eptContractCode":"EKA20240002653","eptPin":"ept_hiCRNcZchERu","eptClientName":"比亚迪汽车（泰国）有限公司","accountMapping":{"bydauto":{"eptContractCode":"EKA20240002653","eptPin":"ept_hiCRNcZchERu","eptClientName":"比亚迪汽车（泰国）有限公司"},"bydcomponent":{"eptContractCode":"EKA20240002607","eptPin":"ept_PiMjqQAFRiZg","eptClientName":"比亚迪汽车零部件（泰国）有限公司"}}},{"jdiContractCode":"ISPG-**************","eptContractCode":"EKA20230019089","eptPin":"ept_cKvJqiwEUEyJ","eptClientName":"LEE_INNOVATION_COMPANY_LIMITED","accountMapping":{"caochuanV2":{"eptContractCode":"EKA20230019089","eptPin":"ept_cKvJqiwEUEyJ","eptClientName":"LEE_INNOVATION_COMPANY_LIMITED1"},"caochuanTest":{"eptContractCode":"EKA20230019089","eptPin":"ept_cKvJqiwEUEyJ","eptClientName":"LEE_INNOVATION_COMPANY_LIMITED2"}}},{"jdiContractCode":"ISPG-**************","eptContractCode":"EKA20230024506","eptPin":"ept_auajyhalVzqK","eptClientName":"正泰国际采购项目"},{"jdiContractCode":"ISPG-**************","eptContractCode":"EKA20240003510","eptPin":"ept_JIhXMIPQHgzt","eptClientName":"刚果布索瑞米项目"},{"jdiContractCode":"ISPG-**************","eptContractCode":"EKA20240005193","eptPin":"ept_NmoHstruEfbM","eptClientName":"金门项目正式"},{"jdiContractCode":"ISPG-**************","eptContractCode":"EKA20240005791","eptPin":"ept_juzlfLUlIoqg","eptClientName":"全球氟化工工厂有限公司+MRO"},{"jdiContractCode":"ISPG-**************","eptContractCode":"EKA20230019089","eptPin":"ept_cKvJqiwEUEyJ","eptClientName":"LEE_INNOVATION_COMPANY_LIMITED_001","accountMapping":{"bydtest":{"eptContractCode":"EKA20230019089","eptPin":"ept_cKvJqiwEUEyJ","eptClientName":"LEE_INNOVATION_COMPANY_LIMITED_001"}}},{"jdiContractCode":"ISPG-**************","eptContractCode":"EKA20240004612","eptPin":"ept_bifyxTZPVFpR","eptClientName":"BYD AUTO (THAILAND) CO.,LTD.","accountMapping":{"bydautoTh":{"eptContractCode":"EKA20240004612","eptPin":"ept_bifyxTZPVFpR","eptClientName":"BYD AUTO (THAILAND) CO.,LTD."},"bydcomponentTh":{"eptContractCode":"EKA20240004612","eptPin":"ept_mEDuPJhbqpUB","eptClientName":"BYD AUTO COMPONENTS (THAILAND) CO.,LTD"}}},{"jdiContractCode":"ISPG-**************","eptContractCode":"EKA20240007772","eptPin":"ept_jwHoasqILwat","eptClientName":"比亚迪香港项目"}]'

ept:
  mockAddress:
iop:
  order:
    split:
      pins: 1,2,caochuan-V2
    splitMsg:
      pins: 1,2,北京果儿vop
mock:
  split:
    order: true

fulfilment:
  policy:
    config: '{ "BR": { "provinceCodes": "SP,MG,RJ,ES,PR,SC,RS", "skuCNPJValue": "100077", "enterpriseWarehouseCodes": [ "BRHUB-BA", "BRHUB-SP" ], "storehouseCodes": [], "targetStrategyKey": "" }, "HU": { "provinceCodes": "", "skuCNPJValue": "", "enterpriseWarehouseCodes": [], "storehouseCodes": [ "GDFG", "GDFG" ], "targetStrategyKey": "" }, "MY": { "provinceCodes": "", "skuCNPJValue": "", "enterpriseWarehouseCodes": [ "MYHUB" ], "storehouseCodes": [], "targetStrategyKey": "" }, "TEST": { "provinceCodes": "HNX,HPI,QNU,BGC,TNH,BNC,HYN,HDI,VPN,PTH,HNB,NNA,NBI,LSN,CBA,BKC,TQU,YBN,LCO,SLN,LCA,IBN,HBA,THH,NAG,HTX", "skuCNPJValue": "100077", "enterpriseWarehouseCodes": [ "2", "1" ], "storehouseCodes": [ "101", "102" ], "targetStrategyKey": "VN" }, "VN": { "provinceCodes": "HNX,HPI,QNU,BGC,TNH,BNC,HYN,HDI,VPN,PTH,HNB,NNA,NBI,LSN,CBA,BKC,TQU,YBN,LCO,SLN,LCA,IBN,HBA,THH,NAG,HTX", "skuCNPJValue": "", "enterpriseWarehouseCodes": [ "VNHUB", "VNHUB-TDN" ], "storehouseCodes": [], "targetStrategyKey": "" } }'

jdi:
  isc:
    jmi:
      uat:
        contactNos: 'ISPG-**************,ISPG-20221115112609,ISPG-**************,ISPG-20240701183952,ISPG-20240702114421,ISPG-20240731155652,ISPG-20240816182832,ISPG-20240818124409'

# https://docs.jdcloud.com/cn/object-storage-service/oss-endpont-list
s3:
  region: cn-north-1
  protocol: https://
  external:
    endpoint: s3.cn-north-1.jdcloud-oss.com
  internal:
    endpoint: s3-internal.cn-north-1.jdcloud-oss.com
  default:
    bucket: jdi-intl


fill:
  iopOrderIdJob:
    startTime:

stock:
  mockFalseResult:
invoice:
  country:
    statusShow: '{\"TH\":[99],\"VN\":[80,81,82,90,99]}'

customer:
  invoice:
    change: xxx

jc:
  export:
    sysId: gygj
    token: E36F2039EDFBCA3E2E537B28F077CA0D