package com.jd.international.web.web.aop;

import com.alibaba.fastjson2.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Collectors;


@Aspect
@Component
@Slf4j
public class InternationalAspect {
    private static final String production = "";
    @Value("${ump.key}")
    private String umpKey;
    @Pointcut("execution(public * com.jd.international.web..*Controller.*(..))")
    public void pointcut(){}
    @Around("pointcut()")
    public Object providerPointAround(ProceedingJoinPoint point) throws Throwable {
        String[] packageNames = point.getTarget().getClass().getName().split("\\.");
        String packageName = packageNames[packageNames.length-1];
        String clsName = point.getTarget().getClass().getSimpleName();
        String methodName = point.getSignature().getName();
        Object result = null;
        StringBuilder posStr = new StringBuilder(clsName).append("#").append(methodName).append(":");
        CallerInfo callerInfo = null;
        if(production.equals(umpKey)) {
            callerInfo = Profiler.registerInfo("international-api." + packageName + "." + clsName + "." + methodName,
                    false, true);
        }
        try {
            result = point.proceed();
        } catch (Throwable e) {
            Object[] args = point.getArgs();
            if(production.equals(umpKey)) {
                Profiler.functionError(callerInfo);
            }
            try {
                log.error("调用" + packageName + "服务" + posStr + "异常：" + (Optional.ofNullable(args).isPresent() ? JSON.toJSONString(Arrays.stream(point.getArgs()).filter(arg ->
                        !(arg instanceof MultipartFile)).collect(Collectors.toList())) : ""), e);
            }catch (Exception ew){
                log.error("调用" + packageName + "服务" + posStr + "异常：",e);
            }
            throw e;
        } finally{
            if(production.equals(umpKey)) {
                Profiler.registerInfoEnd(callerInfo);
            }
        }
        return result;
    }
}
