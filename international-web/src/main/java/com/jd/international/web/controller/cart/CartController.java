package com.jd.international.web.controller.cart;

import com.alibaba.fastjson.JSON;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.bean.SimpleResult;
import com.jd.international.domain.cart.CartCheckReq;
import com.jd.international.domain.cart.batch.BatchCartParseReq;
import com.jd.international.domain.cart.batch.BatchCartParseResp;
import com.jd.international.domain.cart.batch.BatchSmartMatchReq;
import com.jd.international.service.cart.BatchCartService;
import com.jd.international.service.cart.CartService;
import com.jd.international.soa.sdk.common.cart.req.AddCartWaresReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/jdig/cart")
@RequiredArgsConstructor
public class CartController {
    private final CartService cartService;
    private final BatchCartService batchCartService;

    @GetMapping("/getCartNum")
    public SimpleResult getCartNum(BaseInfo baseInfo) {
        return SimpleResult.ok(cartService.getCartNum(baseInfo));
    }

    @GetMapping("/getCartList")
    public SimpleResult getCartList(BaseInfo baseInfo) {
        return SimpleResult.ok(cartService.getCartList(baseInfo, null));
    }

    @PostMapping("/addProducts")
    public SimpleResult addProducts(@RequestBody List<AddCartWaresReq> addCartWaresReqList, BaseInfo baseInfo) {
        log.info("CartController.updateProductNum addCartWaresReqList:{} , baseInfo:{}", JSON.toJSONString(addCartWaresReqList), JSON.toJSONString(baseInfo));
        return SimpleResult.ok(cartService.addProducts(baseInfo, addCartWaresReqList));
    }

    @GetMapping("/updateProductNum")
    public SimpleResult updateProductNum(String sku, Integer num, BaseInfo baseInfo) {
        log.info("CartController.updateProductNum sku:{} , num:{}, baseInfo:{}", sku, num, JSON.toJSONString(baseInfo));
        return SimpleResult.ok(cartService.updateProductNum(baseInfo, sku, num));
    }

    @PostMapping("/checkProduct")
    public SimpleResult checkProduct(@RequestBody CartCheckReq cartCheckReq, BaseInfo baseInfo) {
        log.info("CartController.checkProduct cartCheckReq:{} , baseInfo:{}", JSON.toJSONString(cartCheckReq), JSON.toJSONString(baseInfo));
        return SimpleResult.ok(cartService.checkProduct(baseInfo, cartCheckReq.getSkus(), cartCheckReq.getChecked()));
    }

    @PostMapping("/delProducts")
    public SimpleResult delProducts(@RequestBody List<String> skuIds, BaseInfo baseInfo) {
        log.info("CartController.delProducts skuIds:{} , baseInfo:{}", skuIds, JSON.toJSONString(baseInfo));
        return SimpleResult.ok(cartService.delProducts(baseInfo, skuIds));
    }

    @GetMapping("/batch/template")
    public SimpleResult batchTemplate(BaseInfo baseInfo) {
        log.info(CartController.class.getName() + ".batchTemplate baseInfo:{}", JSON.toJSONString(baseInfo));
        String env = baseInfo.getEnv();
        return SimpleResult.ok(batchCartService.template(env));
    }

    @PostMapping("/batch/parse")
    public SimpleResult batchParse(@RequestPart("file") MultipartFile file, @RequestParam("param") String param, BaseInfo baseInfo) {
        BatchCartParseReq batchCartParseReq = JSON.parseObject(param, BatchCartParseReq.class);
        log.info(CartController.class.getName() + ".batchParse batchCartParseReq:{}, baseInfo:{}", JSON.toJSONString(batchCartParseReq), JSON.toJSONString(baseInfo));
        return batchCartService.parseExcel(file, batchCartParseReq, baseInfo);
    }

    @PostMapping("/batch/smartMatch")
    public SimpleResult batchSmartMatch(@RequestBody BatchSmartMatchReq req, BaseInfo baseInfo) {
        log.info(CartController.class.getName() + ".batchSmartMatch batchSmartMatchReq:{}, baseInfo:{}", JSON.toJSONString(req), JSON.toJSONString(baseInfo));
        return batchCartService.smartMatch(req, baseInfo);
    }

    @PostMapping("/batch/checkedItemsPrice")
    public SimpleResult checkedItemsPrice(@RequestBody List<AddCartWaresReq> items, BaseInfo baseInfo) {
        log.info(CartController.class.getName() + ".checkedItemsPrice items:{}, baseInfo:{}", JSON.toJSONString(items), JSON.toJSONString(baseInfo));
        return batchCartService.checkedItemsPrice(items);
    }
}
