package com.jd.international.web.controller.search;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.bean.SimpleResult;
import com.jd.international.common.config.CommonDUCCConfig;
import com.jd.international.service.isc.mku.IscMkuClientService;
import com.jd.international.service.search.SearchService;
import com.jd.international.soa.sdk.common.search.req.SearchReq;
import com.jd.international.soa.sdk.common.search.resp.BusinessCardResp;
import com.jd.international.soa.sdk.common.search.resp.SearchResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@RestController
@RequestMapping("/jdig/search")
@RequiredArgsConstructor
public class SearchController {
    private final SearchService searchService;
    @Resource
    private IscMkuClientService iscMkuClientService;
    @Resource
    private CommonDUCCConfig commonDUCCConfig;

    @GetMapping("/searchHistory")
    public SimpleResult searchHistory(BaseInfo baseInfo){
        return SimpleResult.ok(searchService.searchHistory(baseInfo.getPin()));
    }
    @GetMapping("/delHistory")
    public SimpleResult delHistory(String word,Integer delAll,BaseInfo baseInfo){
        searchService.delHistory(word, delAll,baseInfo.getPin());
        return SimpleResult.ok();
    }
    @GetMapping("/addSearchHistory")
    public SimpleResult addSearchHistory(String word,BaseInfo baseInfo){
        searchService.addSearchHistory(word,baseInfo.getPin());
        return SimpleResult.ok();
    }
    @GetMapping("/hotAndDark")
    public SimpleResult hotAndDark(BaseInfo baseInfo){
       return SimpleResult.ok(searchService.hotAndDark(baseInfo.getPin()));
    }
    @GetMapping("/attempt")
    public SimpleResult attempt(String key, BaseInfo baseInfo){
        return SimpleResult.ok(searchService.attempt(key, baseInfo));
    }
    @PostMapping("/search")
    public SimpleResult search(@RequestBody SearchReq searchReq, BaseInfo baseInfo){
        return SimpleResult.ok();
//        return SimpleResult.ok(searchService.search(searchReq,baseInfo));
    }

    @PostMapping("/searchWares")
    public SimpleResult searchWares(HttpServletRequest request, HttpServletResponse response, @RequestBody SearchReq searchReq, BaseInfo baseInfo){
        String stationType = baseInfo.getStationType().toString();
        log.info("searchWares, searchReq={}, baseInfo={}, stationType={}", JSONObject.toJSONString(searchReq), JSONObject.toJSONString(baseInfo), stationType);
        SearchResp result = iscMkuClientService.pageEs(searchReq, baseInfo);
        if (null!=commonDUCCConfig && Boolean.TRUE.equals(commonDUCCConfig.getLogDebugSwitch())){
            log.info("searchWares, result={}, searchReq={}", JSONObject.toJSONString(result), JSONObject.toJSONString(searchReq));
        }
        if(result==null || CollectionUtils.isEmpty(result.getWares())){
            log.info("SearchController.searchWares 无货搜索记录 关键字:{},用户信息:{}" ,  searchReq.getKey(), JSON.toJSONString(baseInfo));
        }
        return SimpleResult.ok(result);
    }

    @PostMapping("/searchCombined")
    public SimpleResult searchCombined(@RequestBody SearchReq searchReq, BaseInfo baseInfo){
        return SimpleResult.ok(searchService.searchCombined(searchReq,baseInfo));
    }
//    @GetMapping("/getBusinessCardInfo")
//    public SimpleResult getBusinessCardInfo(HttpServletRequest request, HttpServletResponse response, @RequestParam String skuId,@RequestParam String brandId, BaseInfo baseInfo){
//        log.info("getBusinessCardInfo, skuId={}, brandId={}, baseInfo={}", skuId, brandId, JSONObject.toJSONString(baseInfo));
//        BusinessCardResp result = iscMkuClientService.cardInfo(brandId, baseInfo);
//        return SimpleResult.ok(result);
//    }

    @GetMapping("/isSkuSearch")
    public SimpleResult isSkuSearch(@RequestParam String sku, BaseInfo baseInfo){
        return SimpleResult.ok(searchService.isSkuSearch(baseInfo, sku));
    }

    /**
     * 最新入池商品
     * @param request
     * @param response
     * @param baseInfo
     * @return
     */
    @PostMapping("/latestWares")
    public SimpleResult latestWares(HttpServletRequest request, HttpServletResponse response, BaseInfo baseInfo){
        log.info("latestWares, baseInfo={}", JSONObject.toJSONString(baseInfo));
        SearchResp result = iscMkuClientService.latestWares(baseInfo);
        if (null!=commonDUCCConfig && Boolean.TRUE.equals(commonDUCCConfig.getLogDebugSwitch())){
            log.info("latestWares, result={}", JSONObject.toJSONString(result));
        }
        if(result==null || CollectionUtils.isEmpty(result.getWares())){
            log.info("SearchController.latestWares 无入池商品 baseInfo={}" ,  JSON.toJSONString(baseInfo));
        }
        return SimpleResult.ok(result);
    }
}
