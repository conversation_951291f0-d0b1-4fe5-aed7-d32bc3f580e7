package com.jd.international.web.web.filter.config;

import com.jd.international.web.web.filter.*;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: 所有过滤器配置
 * @Author: zhaojianguo21
 * @Date: 2024/8/29
 **/

@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean<CorsFilter> corsFilterRegistrationBean(CorsFilter corsFilter) {
        FilterRegistrationBean<CorsFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(corsFilter);
        registrationBean.addUrlPatterns("/jdig/*");
        // 此过滤器需要配置为第1个。
        registrationBean.setOrder(1);
        return registrationBean;
    }



    @Bean
    public FilterRegistrationBean<LoginModelServletFilter> loginModelServletFilterRegistrationBean(LoginModelServletFilter loginModelServletFilter) {
        FilterRegistrationBean<LoginModelServletFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(loginModelServletFilter);
        registrationBean.addUrlPatterns("/jdig/*");
        registrationBean.setOrder(2);
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean<LoginCheckServletFilter> loginCheckServletFilterRegistrationBean(LoginCheckServletFilter loginCheckServletFilter) {
        // 此filter依赖 LoginPassportServletFilter，要放在 LoginPassportServletFilter 之后
        FilterRegistrationBean<LoginCheckServletFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(loginCheckServletFilter);
        registrationBean.addUrlPatterns("/jdig/*");
        registrationBean.setOrder(3);
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean<SignServletFilter> signFilterRegistrationBean(SignServletFilter signServletFilter) {
        FilterRegistrationBean<SignServletFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(signServletFilter);
        registrationBean.addUrlPatterns("/jdig/*");
        registrationBean.setOrder(4);
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean<ResponseWrapperFilter> responseWrapperFilterRegistrationBean(ResponseWrapperFilter responseWrapperFilter) {
        FilterRegistrationBean<ResponseWrapperFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(responseWrapperFilter);
        registrationBean.addUrlPatterns("/jdig/*");
        registrationBean.setOrder(800);
        return registrationBean;
    }
}
