package com.jd.international.web.controller.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.bean.SimpleResult;
import com.jd.international.common.config.CommonDUCCConfig;
import com.jd.international.common.config.InternationalConfig;
import com.jd.international.common.config.OrderDUCCConfig;
import com.jd.international.common.constants.CommonConstant;
import com.jd.international.common.constants.OrderConstant;
import com.jd.international.common.enums.OrderStateEnum;
import com.jd.international.common.utils.DateUtils;
import com.jd.international.domain.delivery.OrderDeliveryRequest;
import com.jd.international.domain.delivery.OrderDeliveryVO;
import com.jd.international.domain.delivery.TrackInfoVO;
import com.jd.international.domain.order.OrderListReq;
import com.jd.international.domain.order.OrderPoDTO;
import com.jd.international.domain.order.PunchoutOrderRequestVO;
import com.jd.international.domain.order.PunchoutOrderResultVO;
import com.jd.international.domain.order.export.OrderExportConfigSaveReq;
import com.jd.international.rpc.approval.RpcOrderQueryProvider;
import com.jd.international.rpc.material.MkuMaterialRpcService;
import com.jd.international.service.authority.AuthorityService;
import com.jd.international.service.download.DownloadService;
import com.jd.international.service.order.OrderService;
import com.jd.international.service.orderList.OrderListService;
import com.jd.international.service.punchout.PunchoutOrderService;
import com.jd.international.soa.base.bean.RPCResult;
import com.jd.international.soa.sdk.order.download.req.IntlOrderExport;
import com.jd.international.soa.sdk.order.download.req.IntlOrderPrint;
import com.jd.international.soa.sdk.order.download.res.Pagination;
import com.jd.international.soa.sdk.order.orderList.OrderQueryProvider;
import com.jd.international.soa.sdk.order.orderList.req.OrderExportProviderReq;
import com.jd.international.soa.sdk.order.orderList.req.OrderReq;
import com.jd.international.soa.sdk.order.orderList.res.OrderExportConfig;
import com.jd.international.soa.sdk.order.orderList.res.OrderGroupExpDeliverDate;
import com.jd.international.soa.sdk.order.orderList.res.OrderRes;
import com.jd.international.soa.sdk.order.orderList.res.OrderStatusRes;
import com.jdi.isc.library.common.constants.i18n.I18nKeyConstant;
import com.jdi.isc.library.common.exception.BizException;
import com.jdi.isc.library.common.exception.BizI18nException;
import com.jdi.isc.library.common.response.ResponseCode;
import com.jdi.isc.library.i18n.datasource.cache.I18nCacheManager;
import com.jdi.isc.product.soa.api.common.LangConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.jdi.isc.library.common.constants.i18n.I18nKeyConstant.COMMON_OPERATE_FAIL;

@RestController
@RequestMapping("/jdig/order")
@Slf4j
public class OrderListController {

    @Resource
    private OrderService orderService;
    @Resource
    private OrderListService orderListService;
    @Resource
    private DownloadService downloadService;
    @Resource
    private RpcOrderQueryProvider rpcOrderQueryProvider;
    @Resource
    private MkuMaterialRpcService mkuMaterialRpcService;
    @Resource
    private AuthorityService authorityService;
    @Resource
    private OrderDUCCConfig orderDUCCConfig;

    @Resource
    private CommonDUCCConfig commonDUCCConfig;
    @Resource
    private I18nCacheManager i18nCacheManager;
    @Resource
    private Map<String, PunchoutOrderService> punchoutOrderServiceMap;


    @PostMapping("/updateOrderStatus")
    public SimpleResult<String> updateOrderStatus(@RequestBody OrderReq orderReq, BaseInfo baseInfo) {
        String processKey = null;
        if (orderReq.getOrderStatus().equals(OrderStateEnum.CANCELED.getCode())) {
            OrderRes orderRes = orderListService.queryByOrderId(orderReq.getOrderNo(), baseInfo);
            Integer orderStatus = orderRes.getOrderStatus();
            if (baseInfo.isPunchout()) {
                if (orderStatus > 3) {
                    return SimpleResult.error(ResponseCode.ORDER_CANNOT_CANCEL.getCode(), ResponseCode.ORDER_CANNOT_CANCEL.getMessage());
                }
            } else if (!Arrays.asList(OrderStateEnum.TO_BE_CONFIRMED.getCode(),OrderStateEnum.SUBMITTED.getCode(),OrderStateEnum.WAIT_APPROVEL.getCode()).contains(orderStatus)) {
                return SimpleResult.error(ResponseCode.ORDER_CANNOT_CANCEL.getCode(), ResponseCode.ORDER_CANNOT_CANCEL.getMessage());
            }
            String orderExtInfo = orderRes.getOrderExtInfo();
            if (StringUtils.isNotBlank(orderExtInfo)) {
                JSONObject object = JSON.parseObject(orderExtInfo);
                processKey = object.getString("processKey");
            }
        }
        Boolean aBoolean = orderListService.updateOrderStatus(orderReq, baseInfo);
        if (aBoolean) {
            return SimpleResult.ok(ResponseCode.COMMON_MODIFY_SUCCESS.getCode(), ResponseCode.COMMON_MODIFY_SUCCESS.getMessage());
        }
        return SimpleResult.error(ResponseCode.COMMON_MODIFY_FAILED.getCode(), ResponseCode.COMMON_MODIFY_FAILED.getMessage());
    }


    @PostMapping("/confirmReceipt")
    public SimpleResult<String> confirmReceipt(@RequestBody OrderReq orderReq, BaseInfo baseInfo) {
        OrderRes orderRes = orderListService.queryByOrderId(orderReq.getOrderNo(), baseInfo);
        Integer orderStatus = orderRes.getOrderStatus();
        List<Integer> check = Arrays.asList(OrderStateEnum.SUBMITTED.getCode(),
                OrderStateEnum.TO_BE_CONFIRMED.getCode(),
                OrderStateEnum.WAIT_APPROVEL.getCode(),
                OrderStateEnum.SHIPPING.getCode()
        );
        if (check.contains(orderStatus)) {
            return SimpleResult.error(ResponseCode.ORDER_CANNOT_CONFIRM_RECEIPT.getCode(), ResponseCode.ORDER_CANNOT_CONFIRM_RECEIPT.getMessage());
        }
        Boolean b = orderListService.confirmReceipt(orderReq, baseInfo);
        if (b) {
            return SimpleResult.ok(ResponseCode.COMMON_MODIFY_SUCCESS.getCode(), ResponseCode.COMMON_MODIFY_SUCCESS.getMessage());
        }
        return SimpleResult.error(ResponseCode.COMMON_MODIFY_FAILED.getCode(), ResponseCode.COMMON_MODIFY_FAILED.getMessage());
    }

    @PostMapping("/batchUpdateOrderStatus")
    public SimpleResult<String> batchUpdateOrderStatus(@RequestBody List<OrderReq> orderReq, BaseInfo baseInfo) {
        log.info("获取的订单列表req" + JSON.toJSONString(orderReq));
        List<String> collect = orderReq.stream().map(OrderReq::getOrderNo).collect(Collectors.toList());
        List<OrderRes> orderResList = orderListService.queryStatusByOrderNo(collect);
        log.info("获取的订单列表" + JSON.toJSONString(orderResList));
        Map<String, Integer> orderStatusMap = new HashMap<>();
        for (OrderRes orderRes : orderResList) {
            orderStatusMap.put(orderRes.getOrderNo(), orderRes.getOrderStatus());
        }
        log.info("获取的订单列表orderStatusMap" + JSON.toJSONString(orderStatusMap));
        for (OrderReq order : orderReq) {
            if (Objects.equals(OrderStateEnum.CANCELED.getCode(), order.getOrderStatus())) {
                if (!(orderStatusMap.get(order.getOrderNo()).equals(OrderStateEnum.TO_BE_CONFIRMED.getCode()) || orderStatusMap.get(order.getOrderNo()).equals(OrderStateEnum.SUBMITTED.getCode()))) {
                    String i18nVal = i18nCacheManager.getValueOrDefault(baseInfo.getEnv(), I18nKeyConstant.NOT_ALLOWED_TO_CANCEL);
                    return SimpleResult.error(order.getOrderNo() + i18nVal);
                }
            } else if (Objects.equals(OrderStateEnum.COMPLETED.getCode(), order.getOrderStatus())) {
                if (!(orderStatusMap.get(order.getOrderNo()).equals(OrderStateEnum.SHIPPING.getCode()))) {
                    String i18nVal = i18nCacheManager.getValueOrDefault(baseInfo.getEnv(), I18nKeyConstant.NOT_ALLOWED_TO_CONFIRM);
                    return SimpleResult.error(order.getOrderNo() + i18nVal);
                }
            }
        }
        Boolean aBoolean = orderListService.batchUpdateOrderStatus(baseInfo, orderReq);
        if (aBoolean) {
            return SimpleResult.ok(ResponseCode.COMMON_MODIFY_SUCCESS.getCode(), ResponseCode.COMMON_MODIFY_SUCCESS.getMessage());
        }
        return SimpleResult.error(ResponseCode.ORDER_PARTIAL_MODIFICATION_FAILED.getCode(), ResponseCode.ORDER_PARTIAL_MODIFICATION_FAILED.getMessage());
    }

    @PostMapping("/orderList")
    public SimpleResult<Pagination<OrderRes>> getOrderList(@RequestBody OrderListReq orderListReq, BaseInfo baseInfo) throws ParseException {
        String env = baseInfo.getEnv();
        log.info("OrderListController.getOrderList orderListReq:{} ,baseInfo:{}", JSON.toJSONString(orderListReq), env);
        OrderReq orderReq = new OrderReq();
        BeanUtils.copyProperties(orderListReq, orderReq);
        orderReq.setPin(baseInfo.getPin());
        orderReq.setContractNum(baseInfo.getContractNum());
        orderReq.setLang(env);
        String subAccount = baseInfo.getSubAccount();
        if (subAccount != null) {
            orderReq.setClientStaffId(subAccount);
            orderReq.setSourceType(baseInfo.getLoginSource());
        }
        if (null != orderListReq.getPageNo()) {
            orderReq.setPageNo(orderListReq.getPageNo());
        }
        if (null != orderListReq.getPageSize()) {
            orderReq.setPageSize(orderListReq.getPageSize());
        }
        if (null != orderListReq.getStartTime()) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            Date date = formatter.parse(orderListReq.getStartTime());
            orderReq.setStartTime(date);
        }
        if (null != orderListReq.getEndTime()) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            Date date = formatter.parse(orderListReq.getEndTime());
            orderReq.setEndTime(date);
        }
        orderReq.setValidStatus(1);

//        if (authorityService.haseMaterialAuthority(baseInfo) && StringUtils.isNotBlank(orderListReq.getMaterialId())) {
//            Map<String, MkuMaterialApiDTO> mkuMaterialApiDTOMap = mkuMaterialRpcService.queryMkuMaterialVOsByMaterialIds(Sets.newHashSet(orderListReq.getMaterialId()), baseInfo.getClientId());
//            if (null != mkuMaterialApiDTOMap && null != mkuMaterialApiDTOMap.get(orderListReq.getMaterialId())
//                    && null != mkuMaterialApiDTOMap.get(orderListReq.getMaterialId()).getMkuId()) {
//                orderReq.setSku(mkuMaterialApiDTOMap.get(orderListReq.getMaterialId()).getMkuId().toString());
//            } else {
//                log.info("OrderListController.getOrderList, no match material. orderListReq:{} ,baseInfo:{}", JSON.toJSONString(orderListReq), env);
//                return SimpleResult.ok(new Pagination());
//            }
//        }
        if(StringUtils.isNotBlank(orderListReq.getMaterialId())){
            orderReq.setMaterialId(orderListReq.getMaterialId());
        }
        Pagination<OrderRes> pagination = orderListService.queryOrderList(orderReq, baseInfo);
        if (null != pagination) {
            return SimpleResult.ok(pagination);
        }
        return SimpleResult.error(ResponseCode.COMMON_QUERY_FAILED.getCode(), ResponseCode.COMMON_QUERY_FAILED.getMessage());
    }

    @PostMapping("/sortByDate")
    public SimpleResult<List<OrderGroupExpDeliverDate>> sortByDate(@RequestBody OrderListReq orderListReq, BaseInfo baseInfo) throws ParseException {
        String env = baseInfo.getEnv();
        log.info("OrderListController.sortByDate orderListReq:{} ,baseInfo:{}", JSON.toJSONString(orderListReq), env);
        if (!orderDUCCConfig.showGroupByExpDeliverDate(baseInfo.getClientId())) {
            log.info("OrderListController.sortByDate, authority error. orderListReq:{} ,baseInfo:{}", JSON.toJSONString(orderListReq), env);
            return SimpleResult.error(ResponseCode.COMMON_NO_AUTHORIZED.getCode(), ResponseCode.COMMON_NO_AUTHORIZED.getMessage());
        }
        OrderReq orderReq = new OrderReq();
        BeanUtils.copyProperties(orderListReq, orderReq);
        orderReq.setPin(baseInfo.getPin());
        orderReq.setContractNum(baseInfo.getContractNum());
        orderReq.setLang(env);
        if (null != orderListReq.getPageNo()) {
            orderReq.setPageNo(orderListReq.getPageNo());
        }
        if (null != orderListReq.getPageSize()) {
            orderReq.setPageSize(orderListReq.getPageSize());
        }
        if (null != orderListReq.getStartTime()) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            Date date = formatter.parse(orderListReq.getStartTime());
            orderReq.setStartTime(date);
        }
        if (null != orderListReq.getEndTime()) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            Date date = formatter.parse(orderListReq.getEndTime());
            orderReq.setEndTime(date);
        }
        orderReq.setValidStatus(1);
        orderReq.setGroupByExpectedDeliveryDate(Boolean.TRUE);
        Pagination<OrderRes> pagination = orderListService.queryOrderList(orderReq, baseInfo);
        if (null != pagination && !CollectionUtils.isEmpty(pagination.getList())) {
            return SimpleResult.ok(groupByDate(pagination.getList()));
        } else {
            log.info("OrderListController.sortByDate, pagination empty. orderListReq:{} ,baseInfo:{}", JSON.toJSONString(orderListReq), JSON.toJSONString(baseInfo));
            return SimpleResult.ok(new ArrayList<OrderGroupExpDeliverDate>());
        }

    }

    private List<OrderGroupExpDeliverDate> groupByDate(List<OrderRes> allOrder) {
        List<OrderGroupExpDeliverDate> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(allOrder)) {
            return result;
        }

        long todayStartTime = DateUtils.getStartTimeOfDay();
        // 按照预计到货时间聚堆
        Map<Long, List<OrderRes>> allOrderMap = new HashMap<>();
        for (OrderRes orderRes : allOrder) {
            Long expectedDeliveryDate = orderRes.getExpectedDeliveryDate();
            long keyTime = null != expectedDeliveryDate ?
                    (expectedDeliveryDate >= todayStartTime ? expectedDeliveryDate : OrderConstant.OUT_EXPECTED_DELIVERY_DATE) : OrderConstant.NO_EXPECTED_DELIVERY_DATE;
            List<OrderRes> orderResListValue = allOrderMap.get(keyTime);
            if (null == orderResListValue) {
                orderResListValue = new ArrayList<>();
                allOrderMap.put(keyTime, orderResListValue);
            }

            orderResListValue.add(orderRes);
        }

        // 按照到货时间排序放到list集合
        Iterator<Map.Entry<Long, List<OrderRes>>> iterator = allOrderMap.entrySet().iterator();
        OrderGroupExpDeliverDate noExpDeliveryDate = null;
        while (iterator.hasNext()) {
            Map.Entry<Long, List<OrderRes>> entry = iterator.next();
            List<OrderRes> orderListDay = entry.getValue();
            // 同一天内到达的订单按照下单时间排序
            orderListDay.sort(Comparator.comparing(OrderRes::getOrderTime));

            if (OrderConstant.NO_EXPECTED_DELIVERY_DATE == entry.getKey()) {
                noExpDeliveryDate = new OrderGroupExpDeliverDate();
                noExpDeliveryDate.setExpectedDeliveryDate(entry.getKey());
                noExpDeliveryDate.setOrderResList(orderListDay);
                continue;
            }

            OrderGroupExpDeliverDate orderGroupExpDeliverDate = new OrderGroupExpDeliverDate();
            orderGroupExpDeliverDate.setExpectedDeliveryDate(entry.getKey());
            orderGroupExpDeliverDate.setOrderResList(orderListDay);

            result.add(orderGroupExpDeliverDate);
        }
        result.sort(Comparator.comparing(OrderGroupExpDeliverDate::getExpectedDeliveryDate));
        if (null != noExpDeliveryDate) {
            result.add(noExpDeliveryDate);
        }

        return result;
    }

    @GetMapping("/getOrder")
    public SimpleResult<Map<String, Object>> getOrder(String orderId, Boolean mask, BaseInfo baseInfo) {
        SimpleResult<Map<String, Object>> stringObjectMap = orderListService.queryAllByOrderId(orderId, mask, baseInfo);
        return stringObjectMap;
    }

    @GetMapping("/getOrderStatus")
    public SimpleResult<OrderRes> getOrderStatus(BaseInfo baseInfo) {
        OrderReq orderReq = new OrderReq();
        orderReq.setPin(baseInfo.getPin());
        orderReq.setContractNum(baseInfo.getContractNum());
        String subAccount = baseInfo.getSubAccount();
        if (subAccount != null) {
            orderReq.setClientStaffId(subAccount);
            orderReq.setSourceType(baseInfo.getLoginSource());
        }
        return SimpleResult.ok(orderListService.queryOrderStatus(orderReq));
    }

    @GetMapping("/getOrderStatusList")
    public SimpleResult<OrderStatusRes> getOrderStatusList(BaseInfo baseInfo) {
        OrderReq orderReq = new OrderReq();
        orderReq.setPin(baseInfo.getPin());
        orderReq.setContractNum(baseInfo.getContractNum());
        String env = baseInfo.getEnv();
        orderReq.setLang(env);
        String subAccount = baseInfo.getSubAccount();
        if (subAccount != null) {
            orderReq.setClientStaffId(subAccount);
            orderReq.setSourceType(baseInfo.getLoginSource());
        }
        List<OrderStatusRes> orderStatusRes = orderListService.queryOrderStatusVOList(orderReq);
        return SimpleResult.ok(orderStatusRes);
    }

    @RequestMapping("/OrderPrint")
    public SimpleResult<OrderRes> OrderPrint(String orderIds, Integer type, BaseInfo baseInfo) {
        IntlOrderPrint intlOrderPrint = new IntlOrderPrint();
        intlOrderPrint.setOrderId(orderIds);
        intlOrderPrint.setTaskType(type);
        intlOrderPrint.setStatus(1);
        intlOrderPrint.setPin(baseInfo.getPin());
        intlOrderPrint.setContractNum(baseInfo.getContractNum());
        downloadService.insertIntlOrderPrint(intlOrderPrint);
        return SimpleResult.ok(ResponseCode.ORDER_PRINT_TASK_CREATED_SUCCESS.getCode(), ResponseCode.ORDER_PRINT_TASK_CREATED_SUCCESS.getMessage());
    }

    @RequestMapping("/OrderExport")
    public SimpleResult<OrderRes> OrderExport(String taskType, BaseInfo baseInfo) {
        String pin = baseInfo.getPin();
        IntlOrderExport intlOrderExport = new IntlOrderExport();
        intlOrderExport.setPin(pin);
        intlOrderExport.setStatus(1);
        intlOrderExport.setContractNum(baseInfo.getContractNum());
        if (null != taskType) {
            intlOrderExport.setTaskType(Integer.parseInt(taskType));
        }
        log.info("订单导出入参" + JSON.toJSONString(intlOrderExport));
        downloadService.insertIntlOrderExport(intlOrderExport);
        return SimpleResult.ok(ResponseCode.ORDER_EXPORT_TASK_CREATED_SUCCESS.getCode(), ResponseCode.ORDER_EXPORT_TASK_CREATED_SUCCESS.getMessage());
    }

    @PostMapping("/getOrderPrintList")
    public SimpleResult<Pagination<IntlOrderPrint>> getOrderPrintList(@RequestBody IntlOrderPrint intlOrderPrint, BaseInfo baseInfo) {
        if (null == intlOrderPrint) {
            intlOrderPrint = new IntlOrderPrint();
        }
        intlOrderPrint.setPin(baseInfo.getPin());
        intlOrderPrint.setContractNum(baseInfo.getContractNum());
        Pagination<IntlOrderPrint> intlOrderPrintPagination = downloadService.queryIntlOrderPrintByPage(intlOrderPrint);
        return SimpleResult.ok(intlOrderPrintPagination);
    }

    @PostMapping("/getOrderExportList")
    public SimpleResult<Pagination<IntlOrderExport>> getOrderExportList(@RequestBody IntlOrderExport intlOrderExport, BaseInfo baseInfo) {
        if (null == intlOrderExport) {
            intlOrderExport = new IntlOrderExport();
        }
        String pin = baseInfo.getPin();
        intlOrderExport.setPin(pin);
        intlOrderExport.setContractNum(baseInfo.getContractNum());
        Pagination<IntlOrderExport> intlOrderExportPagination = downloadService.queryIntlOrderExportByPage(intlOrderExport);
        return SimpleResult.ok(intlOrderExportPagination);
    }

    @GetMapping("/deleteOrderExportById")
    public SimpleResult<Boolean> deleteOrderExportById(Long id) {
        boolean b = downloadService.deleteIntlOrderExportById(id);
        if (b) {
            return SimpleResult.ok(ResponseCode.COMMON_DELETE_SUCCESS.getCode(), ResponseCode.COMMON_DELETE_SUCCESS.getMessage());
        }
        return SimpleResult.error(ResponseCode.COMMON_DELETE_FAILED.getCode(), ResponseCode.COMMON_DELETE_FAILED.getMessage());
    }

    @GetMapping("/deleteOrderPrintById")
    public SimpleResult<OrderRes> deleteOrderPrintById(Long id) {
        boolean b = downloadService.deleteIntlOrderPrintById(id);
        if (b) {
            return SimpleResult.ok(ResponseCode.COMMON_DELETE_SUCCESS.getCode(), ResponseCode.COMMON_DELETE_SUCCESS.getMessage());
        }
        return SimpleResult.error(ResponseCode.COMMON_DELETE_FAILED.getCode(), ResponseCode.COMMON_DELETE_FAILED.getMessage());
    }


    /**
     * 获取地线部件的相位枚举
     **/
    @GetMapping(value = "/configuration")
    @ResponseBody
    public SimpleResult<OrderExportConfig> getOrderConfig(BaseInfo baseInfo) {
        OrderExportProviderReq req = new OrderExportProviderReq();
        req.setContractNum(baseInfo.getContractNum());
        req.setClientCode(baseInfo.getClientId());
        req.setPin(baseInfo.getPin());
        req.setStationType(baseInfo.getStationType());
        req.setLang(baseInfo.getEnv());
        //todo 带优化
        List<OrderExportConfig> orderExportConfigs = rpcOrderQueryProvider.listOrderExportItemByLang(req);
           JSONObject result = new JSONObject() {{
                put("result", orderExportConfigs);
                put("code", "200");
                put("success", true);
                put("message", "成功");
            }};
        return SimpleResult.ok(result);
    }

    @PostMapping(value = "/configuration/save")
    @ResponseBody
    public SimpleResult saveUpdateExportConfig(@RequestBody OrderExportConfigSaveReq configSaveReq, BaseInfo baseInfo) {
        log.info("saveUpdateExportConfig, configSaveReq={}", JSONObject.toJSONString(configSaveReq));
        RPCResult res = rpcOrderQueryProvider.saveOrderExportConfig(baseInfo, configSaveReq.getExportConfigs());
        return SimpleResult.ok(res.getResult());
    }

    @Resource
    private OrderQueryProvider orderQueryProvider;

    @GetMapping(value = "/processKey")
    public String testProcessKey(Long orderId) {
        OrderReq orderReq = new OrderReq();
        orderReq.setOrderNo(String.valueOf(orderId));
        orderReq.setLang(LangConstant.LANG_EN);
        RPCResult<OrderRes> resRPCResult = orderQueryProvider.queryByOrderReq(orderReq);

        OrderRes orderRes = resRPCResult.getResult();
        Map<String, Object> orderExtInfoMap = JSON.parseObject(orderRes.getOrderExtInfo(), new TypeReference<Map<String, Object>>() {
        });
        String processKey = (String) orderExtInfoMap.get(CommonConstant.PROCESS_KEY);
        return processKey;
    }

    /**
     * 查询订单物流轨迹信息
     *
     * @param request  订单信息
     * @param baseInfo 基础信息
     * @return 物流轨迹信息
     */
    @PostMapping("queryDelivery")
    public SimpleResult<OrderDeliveryVO> queryDelivery(@RequestBody OrderDeliveryRequest request, BaseInfo baseInfo) {
        request.setBaseInfo(baseInfo);
        Integer trackScopeFlag = commonDUCCConfig.getTrackScopeFlag();
        if (Objects.nonNull(trackScopeFlag) && !trackScopeFlag.equals(request.getLogisticScope())) {
            request.setLogisticScope(trackScopeFlag);
        }
        OrderDeliveryVO orderDeliveryVO = orderListService.queryDeliveryInfo(request);
        //todo i18n
        List<TrackInfoVO> trackInfoList = orderDeliveryVO.getTrackInfoList();
        if (CollectionUtils.isNotEmpty(trackInfoList)) {
            trackInfoList.forEach(trackInfoVO -> {
                trackInfoVO.setTrackContent(i18nCacheManager.getValueOrDefault(baseInfo.getEnv(), trackInfoVO.getTrackContent()));
            });
        }
        return SimpleResult.ok(orderDeliveryVO);
    }

    @PostMapping("punchoutOrder")
    public SimpleResult<PunchoutOrderResultVO> punchoutOrder(@RequestBody PunchoutOrderRequestVO requestVO, BaseInfo baseInfo) {
        log.info("punchoutOrder, requestVO={}, baseInfo={}", JSONObject.toJSONString(requestVO), JSONObject.toJSONString(baseInfo));
        String orderId = requestVO.getOrderId();
        String env = baseInfo.getEnv();
        baseInfo.setEnv(LangConstant.LANG_EN);
        OrderRes orderRes = orderListService.queryByOrderId(orderId, baseInfo);
        baseInfo.setEnv(env);
        String punchoutImpl = orderDUCCConfig.getPunchoutImplByContractNum(baseInfo.getContractNum());
        PunchoutOrderResultVO resultVO = punchoutOrderServiceMap.get(punchoutImpl).orderData(orderRes, baseInfo);
        Map<String, Object> extMaps = baseInfo.getExtMaps();
        String hookUrl = String.valueOf(extMaps.get("hookUrl"));
        resultVO.setHookUrl(hookUrl);
        log.info("OCI返回第三方登陆跳转信息 :{}",JSONObject.toJSONString(resultVO));
        return SimpleResult.<PunchoutOrderResultVO>ok(resultVO);
    }

    @GetMapping("orderCallback/encode")
    public SimpleResult orderCallbackEncode(String orderId, BaseInfo baseInfo) {
        return SimpleResult.ok(orderService.orderCallback(orderId, baseInfo));
    }

    @GetMapping("orderCallback/decode")
    public SimpleResult orderCallbackDecode(String content, BaseInfo baseInfo) {
        String result = orderService.orderCallbackDecode(content, baseInfo);
        log.info("content: {}, decode result: {}", content, result);
        return SimpleResult.ok(result);
    }

    /**
     * v2
     * 提交订单信息补充PO单号 订单未发货前
     * @param orderPoDTO 订单信息DTO
     * @param baseInfo 基础信息
     * @return 提交结果
     */
    @PostMapping("/addPo")
    public SimpleResult addPo(@RequestBody OrderPoDTO orderPoDTO, BaseInfo baseInfo){
        try {
            orderService.addPo(orderPoDTO, baseInfo.getPin());
            return SimpleResult.ok();
        } catch (BizException e) {
            log.error("提单异常:", e);
            return SimpleResult.error(e.getCode(), e.getMessage());
        } catch (BizI18nException e) {
            log.error("提单异常:", e);
            return SimpleResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("提单异常:", e);
            return SimpleResult.error(COMMON_OPERATE_FAIL);
        }
    }
}
