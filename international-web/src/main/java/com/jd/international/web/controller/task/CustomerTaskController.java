package com.jd.international.web.controller.task;

import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.bean.SimpleResult;
import com.jd.international.common.config.InternationalConfig;
import com.jd.international.common.config.InternationalDuccConfig;
import com.jd.international.domain.task.biz.CustomerTaskPageVO;
import com.jd.international.service.task.CustomerTaskService;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.library.common.constants.i18n.I18nKeyConstant;
import com.jdi.isc.task.center.api.common.enums.TaskCreateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * @Description: 销端异步任务接口
 * @Author: zhaojianguo21
 * @Date: 2024/08/16 13:33
 **/

@Slf4j
@RestController
@RequestMapping("/jdig/task/center")
public class CustomerTaskController {

    @Resource
    private CustomerTaskService customerTaskService;

    @Resource
    private InternationalConfig internationalConfig;

    @PostMapping("/import")
    public SimpleResult<String> importData(@RequestParam MultipartFile file, @RequestParam Integer importType, BaseInfo baseInfo) {
        try {
            DataResponse<String> result = customerTaskService.importData(baseInfo, file, importType, TaskCreateTypeEnum.IMPORT);
            if (result.getSuccess()) {
                return SimpleResult.ok(result.getData());
            } else {
                return SimpleResult.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("importData, Exception.", e);
            return SimpleResult.error(internationalConfig.queryLanValOrDefault(baseInfo.getEnv(), I18nKeyConstant.COMMON_SYSTEM_ERROR));
        }
    }

    /**
     * 保存、更新
     */
    /*@PostMapping("/saveOrUpdate")
    @ResponseBody
    public SimpleResult<Boolean> saveOrUpdate(@RequestBody CustomerTaskReqVO input, BaseInfo baseInfo) {
        CustomerTaskSaveUpdateReqVO reqParam = new CustomerTaskSaveUpdateReqVO();
        reqParam.setBizData(input);
        reqParam.setBaseInfo(baseInfo);

        ResponseData<CustomerTaskResVO> invokeResult = customerTaskService.saveOrUpdate(reqParam);
        if (invokeResult.getSuccess()){
            return SimpleResult.ok();
        }
        return SimpleResult.error(invokeResult.getMessage());
    }*/

    /**
     * 分页查询
     */
    @PostMapping("/page")
    public SimpleResult<PageInfo<CustomerTaskPageVO.Response>> page(@RequestBody CustomerTaskPageVO.Request input, BaseInfo baseInfo) {
        input.setBaseInfo(baseInfo);
        PageInfo<CustomerTaskPageVO.Response> invokeResult = customerTaskService.pageSearch(input);
        return SimpleResult.ok(invokeResult);
    }

}
