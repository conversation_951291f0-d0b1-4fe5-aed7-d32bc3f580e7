package com.jd.international.web.controller.homepage;

import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.bean.SimpleResult;
import com.jd.international.domain.banner.BannerResp;
import com.jd.international.service.banner.BannerService;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.library.common.constants.i18n.I18nKeyConstant;
import com.jdi.isc.library.i18n.datasource.cache.I18nCacheManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 广告banner接口
 *
 * <AUTHOR>
 * @date 2024/12/30 10:20
 */
@Slf4j
@RestController
@RequestMapping("/jdig/banner")
@RequiredArgsConstructor
public class BannerController {

    /**
     * Banner 服务对象，用于获取 Banner 数据。
     */
    @Resource
    private BannerService bannerService;
    /**
     * 国际化缓存管理器，用于获取不同环境下的国际化消息。
     */
    @Resource
    private I18nCacheManager i18nCacheManager;

    /**
     * 获取 Banner 数据接口
     *
     * @return DataResponse 封装的 BannerResp 数据
     */
    @GetMapping("/get")
    @ToolKit
    public SimpleResult<BannerResp> getBanner(BaseInfo baseInfo) {
        try {
            return SimpleResult.ok(bannerService.getBanner(baseInfo.getCountry(), baseInfo.getEnv(), baseInfo.getClientId()));
        } catch (Exception e) {
            log.error("获取banner异常", e);
            return SimpleResult.error(i18nCacheManager.getValueOrDefault(baseInfo.getEnv(), I18nKeyConstant.COMMON_SERVICE_EXCEPTION));
        }
    }

}
