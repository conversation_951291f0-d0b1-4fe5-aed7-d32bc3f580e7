package com.jd.international.web.web.filter;

import com.alibaba.fastjson2.JSONObject;
import com.jd.international.common.config.LoginFreeDuccConfig;
import com.jd.international.web.utils.CookieUtils;
import com.jd.international.common.utils.LoginTokenUtils;
import com.jd.international.domain.login.CustomerLogin;
import com.jd.international.service.user.UserService;
import com.jd.international.web.web.filter.assistant.CustomHttpServletRequestWrapper;
import com.jd.international.web.web.filter.assistant.ReturnUtil;
import com.jd.passport.login.filter.context.LoginInfo;
import com.jd.passport.login.filter.context.PassportContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

import static com.jd.international.common.constants.LoginConstant.USER_AUTH;
import static com.jd.international.common.constants.LoginSource.OPEN;
import static com.jd.international.common.exception.ResponseErrorCode.DECRYPTION_ERROR;
import static com.jd.international.common.exception.ResponseErrorCode.NON_INTERNATIONAL_USER;
import static com.jd.international.web.utils.CookieUtils.COMMON.*;
import static com.jd.international.web.utils.CookieUtils.openArea;


/**
 * @Description: 国际业务登录过滤器
 * @Author: zhaojianguo21
 * @Date: 2024/8/29
 **/

@Slf4j
@Component
public class LoginCheckServletFilter implements Filter {

    /**
     * 登录过滤器中使用的登录自由Ducc配置对象，用于获取不需要过滤的URL列表。
     */
    @Autowired
    private LoginFreeDuccConfig freeConfig;
    /**
     * 用户服务对象，用于检查用户是否为国际用户。
     */
    @Autowired
    private UserService userService;
    /**
     * 登录URL的配置值，用于在非国际用户尝试访问受保护资源时重定向到该URL。
     */
    @Value("${passport.loginUrl}")
    private String loginUrl;

    @Override
    public void init(FilterConfig filterConfig) {
        log.info("[LoginCheckServletFilter] Filter initialized.");
    }

    /**
     * 过滤器方法，用于检查用户是否为国际用户。
     *
     * @param request  ServletRequest 对象，代表客户端的请求。
     * @param response ServletResponse 对象，代表服务器的响应。
     * @param chain    FilterChain 对象，用于在过滤器链中传递请求和响应。
     * @throws IOException      如果在处理请求或响应时发生 I/O 错误。
     * @throws ServletException 如果在处理请求或响应时发生 Servlet 错误。
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        String referer = httpRequest.getHeader(HttpHeaders.REFERER);
        String uri = httpRequest.getRequestURI();
        if (freeConfig.isNoFilterUrls(uri)) {
            log.info("LoginCheckServletFilter.doFilter uri:{} 无需处理",uri);
            chain.doFilter(httpRequest, httpResponse);
            return;
        }
        log.info("[LoginCheckServletFilter] Processing request. URI: {}, Referer: {}", uri, referer);
        LoginInfo loginInfo = PassportContext.getLoginInfo();
        //open开放域 pin是没有值的。
        if (StringUtils.isBlank(openArea(referer))) {
            log.info("[LoginCheckServletFilter] URI requires filtering. Checking international user for PIN: {}", loginInfo.getPin());
            if (!userService.checkIsInternationUser(loginInfo.getPin())) {
                log.warn("[LoginCheckServletFilter] Non-international user detected. PIN: {}", loginInfo.getPin());
                ReturnUtil.writeResponseInfo(httpResponse, HttpStatus.FORBIDDEN.value(), NON_INTERNATIONAL_USER.getCode(), NON_INTERNATIONAL_USER.getMessage());
                return;
            }
        }
        try {
            CustomHttpServletRequestWrapper wrapper = dealLoginParams(httpRequest);
            log.info("[LoginCheckServletFilter] Login parameters processed successfully.");
            chain.doFilter(wrapper, httpResponse);
        } catch (Exception e) {
            log.error("[LoginCheckServletFilter] Error processing login parameters. Exception: {}", e.getMessage(), e);
            ReturnUtil.writeResponseInfo(httpResponse, HttpStatus.FORBIDDEN.value(), DECRYPTION_ERROR.getCode(), DECRYPTION_ERROR.getMessage());
        }
    }

    private CustomHttpServletRequestWrapper dealLoginParams(HttpServletRequest request) throws Exception {
        Map<String, String[]> cookieMaps = CookieUtils.loadMapFromCookies(request);
        String referer = request.getHeader(HttpHeaders.REFERER);
        String openArea = openArea(referer);
        log.info("[LoginCheckServletFilter] Processing login parameters. Referer: {}, OpenArea: {}", referer, openArea);
        if (StringUtils.isNotBlank(openArea)) {
            cookieMaps.put(LOGIN_SOURCE, new String[]{URLDecoder.decode(OPEN, StandardCharsets.UTF_8.name())});
            cookieMaps.put(OPEN_AREA, new String[]{URLDecoder.decode(openArea, StandardCharsets.UTF_8.name())});
            log.info("[LoginCheckServletFilter] Open area and login source added to cookie maps.");
        } else {
            String userInfo = LoginTokenUtils.decrypt(cookieMaps.get(USER_AUTH)[0]);
            CustomerLogin customerLogin = JSONObject.parseObject(userInfo, CustomerLogin.class);
            cookieMaps.put(LOGIN_SOURCE, new String[]{URLDecoder.decode(customerLogin.getSourceType(), StandardCharsets.UTF_8.name())});
            cookieMaps.put(EXT_MAP, new String[]{URLDecoder.decode(userInfo, StandardCharsets.UTF_8.name())});
            if (StringUtils.isNotBlank(customerLogin.getSubAccount())) {
                cookieMaps.put(SUB_ACCOUNT, new String[]{URLDecoder.decode(customerLogin.getSubAccount(), StandardCharsets.UTF_8.name())});
            }
            log.info("[LoginCheckServletFilter] User authentication data added to cookie maps.:{}", JSONObject.toJSONString(customerLogin));
        }
        CustomHttpServletRequestWrapper customRequest = new CustomHttpServletRequestWrapper(request);
        if (MapUtils.isNotEmpty(cookieMaps)) {
            customRequest.getParameterMap().putAll(cookieMaps);
            log.info("[LoginCheckServletFilter] Cookie maps added to request parameters.");
        }
        return customRequest;
    }

    @Override
    public void destroy() {
        log.info("[LoginCheckServletFilter] Filter destroyed.");
    }


}