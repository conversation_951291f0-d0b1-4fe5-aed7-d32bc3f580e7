package com.jd.international.web.web.filter;

import com.alibaba.fastjson.JSONObject;
import com.jd.international.common.config.LoginFreeDuccConfig;
import com.jd.international.common.constants.CommonConstant;
import com.jd.international.web.utils.CookieUtils;
import com.jd.international.domain.customer.CustomerVO;
import com.jd.international.service.customer.CustomerService;
import com.jd.international.web.web.filter.assistant.ReturnUtil;
import com.jd.passport.login.filter.LoginFilterConfig;
import com.jd.passport.login.filter.agent.LoginVerificationAgent;
import com.jd.passport.login.filter.agent.utils.Assert;
import com.jd.passport.login.filter.api.LoginVerification;
import com.jd.passport.login.filter.api.PassportVerification;
import com.jd.passport.login.filter.context.LoginInfo;
import com.jd.passport.login.filter.context.PassportContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.jd.international.common.constants.CommonConstant.PIN;
import static com.jd.international.common.constants.CommonConstant.USER_AGENT;
import static com.jd.international.common.constants.LoginConstant.USER_AUTH;
import static com.jd.international.common.exception.ResponseErrorCode.*;

/**
 * @Description: 集团passport过滤器 主要为了设置  PassportContext.setLoginInfo(loginInfo);
 * 没有loginInfo就是非法用户登陆
 * @Author: zhaojianguo21
 * @Date: 2024/8/29
 **/

@Slf4j
@Component
public class LoginModelServletFilter implements Filter {


    @Value("${passport.loginUrl}")
    private String loginUrl;

    @Value("${passport.idc.name}")
    private String idcName;

    @Value("${passport.env}")
    private String env;
    /**
     * 自动注入的LoginFreeDuccConfig对象，用于获取免密登录的相关配置信息。
     */
    @Autowired
    private LoginFreeDuccConfig freeConfig;
    /**
     * 自动注入的CustomerService对象，用于获取游客信息。
     */
    @Autowired
    private CustomerService customerService;
    /**
     * 自动注入的LoginVerification对象，用于验证用户登录信息。
     */
    private LoginVerification loginVerification;
    /**
     * 存储登录过滤器的配置信息
     */
    private LoginFilterConfig loginFilterConfig;

    @Override
    public void init(FilterConfig filterConfig) {
        log.info("[PLF] Filter initialization started.");
        log.info("[PLF] Configuration - loginUrl: {}, idcName: {}, env: {}",
                loginUrl, idcName, env);
        this.loginFilterConfig = new LoginFilterConfig();

        if (StringUtils.isNotBlank(loginUrl)) {
            loginFilterConfig.setLoginUrl(loginUrl.trim());
        }

        if (CollectionUtils.isNotEmpty(CommonConstant.WHITE_LIST_URL)) {
            loginFilterConfig.setUnLoginPaths(CommonConstant.WHITE_LIST_URL);
        }
        log.info("[PLF] White list URLs: {}", JSONObject.toJSONString(CommonConstant.WHITE_LIST_URL));
        Assert.notBlankString(idcName, "idcName");
        loginFilterConfig.setIdcName(idcName.trim());

        Assert.notBlankString(env, "environment");
        loginFilterConfig.setEnvironment(env.trim());
        log.info("[PLF] LoginFilterConfig initialized: {}", JSONObject.toJSONString(loginFilterConfig));
        loginVerification = LoginVerificationAgent.getAgent(loginFilterConfig);
        log.info("[PLF] LoginVerification agent initialized successfully.");
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;
        String clientIP = httpRequest.getRemoteAddr();
        String uri = httpRequest.getRequestURI();
        String userAgent = httpRequest.getHeader(USER_AGENT);
        log.info("[PLF] Received request: URI={}, ClientIP={}, UserAgent={}", uri, clientIP, userAgent);
        //放行 不需要经过filter uri
        if (freeConfig.isNoFilterUrls(uri)) {
            log.info("[PLF] Skipping filter for URI: {}", uri);
            filterChain.doFilter(httpRequest, httpResponse);
            return;
        }
        //处理登陆模式，按照referer
        LoginInfo loginInfo = processLogin(httpRequest, httpResponse, CookieUtils.isOpenArea(httpRequest, freeConfig.openAreaList()));
        if (null == loginInfo) {
            log.warn("[PLF] LoginInfo is null, terminating request processing. URI: {}", uri);
            return;
        }
        PassportContext.setLoginInfo(loginInfo);
        log.info("[PLF] LoginInfo set successfully for URI: {}", uri);
        filterChain.doFilter(servletRequest, servletResponse);
    }

    /**
     * 处理登录请求，根据是否开启客人登录选择不同的处理逻辑。
     *
     * @param httpRequest  登录请求对象
     * @param httpResponse 登录响应对象
     * @param isOpen       是否开启客人登录
     * @return 登录信息对象
     * @throws IOException 如果处理登录请求时发生I/O错误
     */
    private LoginInfo processLogin(HttpServletRequest httpRequest, HttpServletResponse httpResponse, boolean isOpen) throws IOException {
        return isOpen ? handleOpenLogin(httpRequest, httpResponse) : handleLogin(httpRequest, httpResponse);
    }


    /**
     * 处理登录请求，验证用户信息并返回登录结果。
     *
     * @param httpRequest  HTTP请求对象，用于获取客户端IP、User-Agent和请求URI等信息。
     * @param httpResponse HTTP响应对象，用于重定向到错误页面或登录页面。
     * @return 登录信息对象，包含用户的登录状态和相关信息。
     * @throws IOException 如果在处理登录过程中发生I/O错误。
     */
    private LoginInfo handleLogin(HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws IOException {
        LoginInfo loginInfo;
        String clientIP = httpRequest.getRemoteAddr();
        String userAgent = httpRequest.getHeader(USER_AGENT);
        String uri = httpRequest.getRequestURI();
        String pin = CookieUtils.getCookieValue(httpRequest, PIN);
        log.info("[PLF] Handling login request. URI={}, ClientIP={}, UserAgent={},pin:{}", uri, clientIP, userAgent, pin);
        if (!CookieUtils.isCookieExpired(httpRequest, USER_AUTH)) {
            log.warn("[PLF] Cookie expired user_auth  for URI: {} pin:{}", uri, pin);
            ReturnUtil.redirect(httpResponse, HttpStatus.UNAUTHORIZED.value(), USER_INFO_EXPIRED.getCode(), USER_INFO_EXPIRED.getMessage(), uri, loginUrl);
            return null;
        }
        try {
            loginInfo = loginVerification.verify(httpRequest, httpResponse, null);
            if (null == loginInfo) {
                log.error("[PLF] 登录验证 返回值为空, uri={}, clientIP={}, userAgent={} pin:{}", uri, clientIP, userAgent, pin);
                ReturnUtil.redirect(httpResponse, HttpStatus.UNAUTHORIZED.value(), USER_INFO_VALIDATION.getCode(), USER_INFO_VALIDATION.getMessage(), uri, loginUrl);
                return null;
            }
            Map<String, Object> param = new HashMap<>();
            param.put(PassportVerification.LOGIN_INFO_KEY, loginInfo);
            if (loginInfo.getResultCode() <= LoginInfo.FAILED || StringUtils.isBlank(loginInfo.getPin())) {
                log.error("[PLF] 登录验证失败或pin为空, uri={}, clientIP={}, userAgent={} resultCode:{} pin :{}", uri, clientIP, userAgent, loginInfo.getResultCode(), pin);
                loginVerification.verifyFailed(httpRequest, httpResponse, param);
                ReturnUtil.redirect(httpResponse, HttpStatus.UNAUTHORIZED.value(), USER_LOGIN_FAIL.getCode(), USER_LOGIN_FAIL.getMessage(), uri, loginUrl);
                return null;
            } else if (loginInfo.getResultCode() == LoginInfo.SUCCESSFUL) {
                loginVerification.verifySuccessful(httpRequest, httpResponse, param);
            }
        } catch (Exception e) {
            log.error("[PLF] Login verification failed or PIN is blank. URI={}, ClientIP={}", uri, clientIP);
            ReturnUtil.redirect(httpResponse, HttpStatus.UNAUTHORIZED.value(), USER_INFO_VALIDATION_ERROR.getCode(), USER_INFO_VALIDATION_ERROR.getMessage(), uri, loginUrl);
            return null;
        }
        log.info("[PLF] Login verification succeeded. ResultCode={}, PIN={}", loginInfo.getResultCode(), loginInfo.getPin());
        return loginInfo;
    }


    /**
     * 处理开放式登录请求。
     *
     * @param httpRequest  HTTP请求对象
     * @param httpResponse HTTP响应对象
     * @return 登录信息对象，包含PIN码和结果代码；如果登录失败，则返回null。
     * @throws IOException 如果在处理请求或响应时发生I/O错误。
     */
    private LoginInfo handleOpenLogin(HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws IOException {
        String uri = httpRequest.getRequestURI();
        String referer = httpRequest.getHeader(HttpHeaders.REFERER);
        String clientIP = httpRequest.getRemoteAddr();
        String userAgent = httpRequest.getHeader(USER_AGENT);
        if (!freeConfig.isOpenUrl(uri)) {
            log.warn("[PLF] Access to URI denied. URI: {}, Reason: Not an open URL.", uri);
            ReturnUtil.writeResponseInfo(httpResponse, HttpStatus.FORBIDDEN.value(), FORBIDDEN_ACCESS.getCode(), FORBIDDEN_ACCESS.getMessage());
            return null;
        }
        String openRegion = CookieUtils.openArea(referer);
        log.info("[PLF] Open login request received. URI: {}, openRegion: {} clientIP:{} userAgent:{}", uri, openRegion, clientIP, userAgent);
        CustomerVO customerVO;
        try {
            customerVO = customerService.getGuestByOpenArea(openRegion);
            log.info("[PLF] Retrieved customer info for OpenRegion: {}. CustomerVO: {}", openRegion, customerVO);
        } catch (Exception e) {
            log.error("[PLF] Error retrieving customer info for OpenRegion: {}. Error: {}", openRegion, e.getMessage(), e);
            ReturnUtil.writeResponseInfo(httpResponse, HttpStatus.FORBIDDEN.value(), QUERY_OPEN_DOMAIN_USER_SERVICE_ERROR.getCode(), QUERY_OPEN_DOMAIN_USER_SERVICE_ERROR.getMessage());
            return null;
        }
        if (customerVO == null) {
            log.warn("[PLF] No customer info found for OpenRegion: {}.", openRegion);
            ReturnUtil.writeResponseInfo(httpResponse, HttpStatus.FORBIDDEN.value(), QUERY_OPEN_DOMAIN_USER_ERROR.getCode(), QUERY_OPEN_DOMAIN_USER_ERROR.getMessage());
            return null;
        }
        LoginInfo loginInfo = new LoginInfo();
        loginInfo.setPin(customerVO.getPin());
        loginInfo.setResultCode(LoginInfo.SUCCESSFUL);
        log.info("[PLF] Open login successful. LoginInfo: {}", loginInfo);
        return loginInfo;
    }


    @Override
    public void destroy() {

    }
}
