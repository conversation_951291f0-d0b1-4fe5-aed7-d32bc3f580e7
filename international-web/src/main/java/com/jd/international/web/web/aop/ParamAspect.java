package com.jd.international.web.web.aop;

import com.alibaba.fastjson.JSONObject;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.config.CommonDUCCConfig;
import com.jd.international.common.constants.LoginSource;
import com.jd.international.domain.customer.CustomerVO;
import com.jd.international.service.customer.CustomerService;
import com.jd.international.service.user.UserService;
import com.jd.international.soa.sdk.common.user.res.EnterpriseUserAllRes;
import com.jd.international.web.utils.CookieUtils;
import com.jd.passport.login.filter.context.LoginInfo;
import com.jd.passport.login.filter.context.PassportContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

import static com.jd.international.web.utils.CookieUtils.COMMON.*;

@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class ParamAspect {


    /**
     * 用户服务接口，用于获取企业用户信息。
     */
    private final UserService userService;

    /**
     * 客户服务接口，用于获取客户信息。
     */
    private final CustomerService customerService;
    /**
     * 公共的DUCC配置对象，用于获取日志调试开关等配置信息。
     */
    private final CommonDUCCConfig commonDUCCConfig;

    @Pointcut("execution(public * com.jd.international.web..*Controller.*(..))")
    public void pointcut() {
    }

    /**
     * 在方法执行前后记录请求信息，并根据请求参数设置BaseInfo对象的属性。
     *
     * @param point ProceedingJoinPoint对象，用于获取方法参数和执行方法。
     * @return 方法执行结果。
     */
    @Around("pointcut()")
    public Object providerPointAround(ProceedingJoinPoint point) throws Throwable {
        Object[] args = point.getArgs();
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Map<String, String> cookies = CookieUtils.loadMapFromCookie(request.getParameterMap());
        String lang = request.getHeader("Jdi-language");
        log.info("获取语言为:{}", lang);
        String uri = request.getRequestURI();
        try {
            for (int i = 0; i < args.length; i++) {
                if (args[i] instanceof BaseInfo) {
                    BaseInfo baseInfo = new BaseInfo();
                    baseInfo.setEnv(lang);
                    String loginSource = cookies.get(LOGIN_SOURCE);
                    if (StringUtils.isBlank(loginSource)) {
                        loginSource = "default";
                        log.warn("LOGIN_SOURCE cookie is null, defaulting to '{}' uri :{}", loginSource, uri);
                    }
                    switch (loginSource) {
                        case LoginSource.OPEN:
                            baseInfo.setEnv(lang);
                            baseInfo.setOpenArea(cookies.get(OPEN_AREA));
                            baseInfo.setLoginSource(cookies.get(LOGIN_SOURCE));
                            setupGuestMode(baseInfo);
                            break;
                        case LoginSource.WISP:
                        case LoginSource.WIEP_OCI:
                        case LoginSource.WIEP_CXML:
                        case LoginSource.WIEP_JD:
                        case LoginSource.WIEP_CATL:
                            LoginInfo login = PassportContext.getLoginInfo();
                            baseInfo.setEnv(lang);
                            baseInfo.setPin(login.getPin());
                            baseInfo.setExtMaps(null != cookies.get(EXT_MAP) ? JSONObject.parseObject(cookies.get(EXT_MAP)) : null);
                            baseInfo.setSubAccount(cookies.getOrDefault(SUB_ACCOUNT,null));
                            baseInfo.setLoginSource(cookies.get(LOGIN_SOURCE));
                            setupLoginMode(baseInfo);
                            break;
                        default:
                            log.error("登陆source 为空 查询原因 :{} uri:{}", JSONObject.toJSONString(cookies),uri);
                            break;
                    }
                    logRequestInfo(baseInfo, request.getRequestURI());
                    args[i] = baseInfo;
                    break;
                }
            }
        } catch (Exception e) {
            log.error("Error occurred while processing request URI: {}. Error: {}", request.getRequestURI(), e.getMessage(), e);
            throw e;
        }
        return point.proceed(args);
    }


    /**
     * 设置访客模式的基本信息。
     *
     * @param baseInfo 包含开放区域信息的 BaseInfo 对象。
     */
    private void setupGuestMode(BaseInfo baseInfo) {
        try {
            CustomerVO customerVO = customerService.getGuestByOpenArea(baseInfo.getOpenArea());
            if (customerVO != null) {
                baseInfo.setClientId(customerVO.getClientCode());
                baseInfo.setContractNum(customerVO.getContractCode());
                baseInfo.setCountry(customerVO.getCountry());
                baseInfo.setStationType(getFirstStation(customerVO));
            } else {
                log.error("setupGuestMode failed: customerVO is null for openArea: {}", baseInfo.getOpenArea());
            }
        } catch (Exception e) {
            log.error("Error setting up guest mode for openArea: {}, pin: {}. Error: {}", baseInfo.getOpenArea(), baseInfo.getPin(), e.getMessage(), e);
        }
    }

    /**
     * 设置登录模式，根据提供的 Pin 查询企业用户信息并更新 BaseInfo 对象。
     *
     * @param baseInfo 基础信息对象，用于存储登录模式相关的信息。
     */
    private void setupLoginMode(BaseInfo baseInfo) {
        try {
            EnterpriseUserAllRes enterpriseUser = userService.queryInternationEnterpriseUserByPin(baseInfo.getPin());
            if (enterpriseUser != null) {
                CustomerVO customerVO = customerService.getCustomerByClientCode(enterpriseUser.getContractInfoRes().getClientId());
                baseInfo.setClientId(enterpriseUser.getContractInfoRes().getClientId());
                baseInfo.setContractNum(enterpriseUser.getContractInfoRes().getContractNumber());
                baseInfo.setUserType(enterpriseUser.getEnterpriseUserRes().getFlag());
                baseInfo.setCountry(customerVO.getCountry());
                baseInfo.setStationType(getFirstStation(customerVO));
            } else {
                log.warn("setupLoginMode: No enterprise user found for pin: {}", baseInfo.getPin());
            }
        } catch (Exception e) {
            log.error("Error setting up login mode for pin: {}, env: {}. Error: {}", baseInfo.getPin(), baseInfo.getEnv(), e.getMessage(), e);
        }
    }

    /**
     * 获取客户的第一个站点。
     * @param customerVO 客户信息对象。
     * @return 客户的第一个站点，若客户没有站点则返回 null。
     */
    private Integer getFirstStation(CustomerVO customerVO) {
        if (customerVO.getStations() != null && !customerVO.getStations().isEmpty()) {
            return customerVO.getStations().get(0);
        }
        return null;
    }


    /**
     * 记录请求信息到日志中。
     *
     * @param parameter 请求参数对象。
     * @param uri       请求的URI路径。
     */
    private void logRequestInfo(Object parameter, String uri) {
        if (Boolean.TRUE.equals(commonDUCCConfig.getLogDebugSwitch())) {
            log.info("Request URI: {}, Parameters: {}", uri, JSONObject.toJSONString(parameter));
        }
    }
}
