package com.jd.international.web.controller.common;

import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Stopwatch;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.bean.SimpleResult;
import com.jd.international.common.config.CommonDUCCConfig;
import com.jd.international.common.config.LoginFreeDuccConfig;
import com.jd.international.common.constants.CommonConstant;
import com.jd.international.web.utils.CookieUtils;
import com.jd.international.rpc.mail.RpcMailService;
import com.jd.international.service.file.FileManageService;
import com.jd.international.soa.sdk.common.mail.req.SendMailReq;
import com.jd.international.soa.sdk.order.orderList.res.OrderRes;
import com.jdi.common.domain.rpc.bean.DataResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.jd.international.web.utils.CookieUtils.GUEST.GUEST_ENV;
import static com.jd.international.web.utils.CookieUtils.GUEST.GUEST_STATION_TYPE;
import static com.jd.international.web.utils.CookieUtils.LOGIN.LOGIN_ENV;
import static com.jd.international.web.utils.CookieUtils.LOGIN.LOGIN_STATION_TYPE;
import static com.jd.international.web.utils.CookieUtils.ONE_MONTH_COOKIE_AGE;

@RestController
@RequestMapping("/jdig/public")
@RequiredArgsConstructor
@Slf4j
public class PublicController {
    /**
     * 公共控制器的配置对象，用于获取和设置公共配置信息。
     */
    private final CommonDUCCConfig DUCCConfig;

    /**
     * RPC邮件服务，用于发送邮件。
     */
    @Autowired
    private RpcMailService rpcMailService;
    /**
     * 文件管理服务，用于处理文件上传和管理等功能。
     */
    @Resource
    private FileManageService fileManageService;
    /**
     * 免登录配置类，用于处理免登录相关的配置信息。
     */
    @Resource
    private LoginFreeDuccConfig loginFreeDUCCConfig;
    /**
     * 文件上传接口
     *
     * @param file 文件
     * @param type 文件类型，用来确定存储空间目录
     * @return 返回文件url
     */
    @PostMapping("/upload")
    public SimpleResult<String> fileUpload(MultipartFile file, @RequestParam Integer type, BaseInfo baseInfo) {
        try {
            String lang = baseInfo.getEnv();
//            String remoteIp = HostUtil.getIpAddr(request);
            DataResponse<String> fileUrlResponse = fileManageService.doUploadFile(file, type, lang);
            if (fileUrlResponse.getSuccess()) {
                return SimpleResult.ok(fileUrlResponse.getData());
            } else {
                return SimpleResult.error("upload failed");
            }
        } catch (Exception e) {
            log.error("upload error", e);
            return SimpleResult.error("upload failed");
        }
    }

    /**
     * 切换站点
     *
     * @return
     */
    @GetMapping("/change/station")
    public SimpleResult changeStation(HttpServletRequest request, HttpServletResponse response, @RequestParam("stationType") String stationType) {
        if (CookieUtils.isOpenArea(request,loginFreeDUCCConfig.openAreaList())) {
            // 免登录访问
            CookieUtils.setCookie(request, response, GUEST_STATION_TYPE, stationType, -1);
        } else {
            // 登录访问 + 第三方访问
            CookieUtils.setCookie(request, response, LOGIN_STATION_TYPE, stationType, -1);
        }
        return SimpleResult.ok();
    }

    /**
     * 切换语言
     *
     * @return
     */
    @GetMapping("/changeEnv")
    public SimpleResult changeEnv(HttpServletRequest request, HttpServletResponse response, @RequestParam("env") String env
            , @RequestParam(name = "reqStartTime", required = false) Long reqStartTime, BaseInfo baseInfo) {
        log.info("changeEnv, param env={}, reqStartTime={} . baseInfo={}", env, reqStartTime, JSONObject.toJSONString(baseInfo));
        Stopwatch stopwatch = Stopwatch.createStarted();
        if (CookieUtils.isOpenArea(request,loginFreeDUCCConfig.openAreaList())) {
            // 免登录访问
            CookieUtils.setCookie(request, response, GUEST_ENV, env, ONE_MONTH_COOKIE_AGE);
        } else {
            // 登录访问 + 第三方访问
            CookieUtils.setCookie(request, response, LOGIN_ENV, env, ONE_MONTH_COOKIE_AGE);
        }

        long time = System.currentTimeMillis();
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("sendResStartTime", time + "");
        log.info("changeEnv, elapsedTime={}, sendResStartTime={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), time);
        return SimpleResult.ok(dataMap);
    }

    /**
     * 获取logo
     *
     * @param baseInfo
     * @return
     */
    @GetMapping("/logo")
    public SimpleResult logoConfig(BaseInfo baseInfo) {
        String logoConfig = DUCCConfig.getLogoConfig();
        if (StringUtils.isNotBlank(logoConfig)) {
            JSONObject jsonObject = JSONObject.parseObject(logoConfig);
            return SimpleResult.ok(jsonObject.getString(baseInfo.getContractNum()));
        }
        return SimpleResult.ok(null);
    }

    @GetMapping("/sendMail")
    public SimpleResult sendMail() {
        String emailInfo = DUCCConfig.getTestEmailInfo();
        log.error("sendMail,emailInfo = {}", emailInfo);
        JSONObject emailInfoJson = JSONObject.parseObject(emailInfo);
        SendMailReq customMail = new SendMailReq();
        customMail.setEmailAddress(emailInfoJson.getString("email"));
        customMail.setPin(emailInfoJson.getString("pin")); // TODO ????
        customMail.setType(161106);
        Map<Object, Object> keyValues = new HashMap<Object, Object>(CommonConstant.HASHMAP_INITIAL_CAPACITY);
        //keyValues.put("title", "京东工采国际平台提醒您：有订单取消。");
        keyValues.put("title", emailInfoJson.getString("title"));
        keyValues.put("pin", emailInfoJson.getString("keyPin")); // TODO ????
        Map<String, Object> model = new HashMap<String, Object>(CommonConstant.HASHMAP_INITIAL_CAPACITY);
        model.put("pin", emailInfoJson.getString("modelPin"));
        model.put("orderPin", emailInfoJson.getString("orderPin"));

        List<OrderRes> orderList = new ArrayList<>();
        OrderRes order1 = new OrderRes();
        order1.setOrderNo("12313131231313123");
        order1.setOrderTime(new Date());
        order1.setTotalPrice(new BigDecimal("100.12313"));
        order1.setCurrency("CNY");
        OrderRes order2 = new OrderRes();
        order2 = new OrderRes();
        order2.setOrderNo("12313131231314444");
        order2.setOrderTime(new Date());
        order2.setTotalPrice(new BigDecimal("100.12314"));
        order2.setCurrency("CNY");
        OrderRes order3 = new OrderRes();
        order3 = new OrderRes();
        order3.setOrderNo("12313131231315555");
        order3.setOrderTime(new Date());
        order3.setTotalPrice(new BigDecimal("100.12315"));
        order3.setCurrency("CNY");
        orderList.add(order1);
        orderList.add(order2);
        orderList.add(order3);
        model.put("orderList", orderList);
        model.put("passOrReject", emailInfoJson.getBoolean("passOrReject"));
        model.put("pass", emailInfoJson.getBooleanValue("pass"));
        TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig("template", TemplateConfig.ResourceMode.CLASSPATH));
        Template template = engine.getTemplate("orderApprovalListMail.ftl");
        String htmlContent = template.render(model);
        keyValues.put("content", htmlContent);
        customMail.setKeyValues(keyValues);
        rpcMailService.sendMail(customMail);
        return SimpleResult.ok();
    }
}
