package com.jd.international.web.controller.wares;

import com.alibaba.fastjson.JSONObject;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.bean.SimpleResult;
import com.jd.international.common.config.CommonDUCCConfig;
import com.jd.international.domain.isc.mku.IscAppMkuAggreDTO;
import com.jd.international.domain.isc.mku.IscAppMkuClientDetailReqDTO;
import com.jd.international.domain.isc.mku.IscAppMkuClientGroupDTO;
import com.jd.international.domain.isc.mku.IscAppMkuClientSpecialAttrReqDTO;
import com.jd.international.service.isc.mku.IscMkuClientService;
import com.jd.international.soa.sdk.common.isc.mku.req.MkuReplenishmentSoaDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Description: 商品服务
 * @Author: zhaojianguo21
 * @Date: 2023/12/13 18:56
 **/
@Slf4j
@RestController
@RequestMapping("/jdig/pro")
public class IscMkuController {

    @Resource
    private IscMkuClientService iscMkuClientService;
    @Resource
    private CommonDUCCConfig commonDUCCConfig;

    /**
     * mku商品详情
     */
    @PostMapping("/info")
    public SimpleResult<IscAppMkuAggreDTO> baseInfo(@RequestBody IscAppMkuClientDetailReqDTO param, BaseInfo baseInfo) {
        IscAppMkuAggreDTO result = iscMkuClientService.baseInfo(param, baseInfo);

        if (null!=commonDUCCConfig && Boolean.TRUE.equals(commonDUCCConfig.getLogDebugSwitch())){
            log.info("baseInfo, result={}, input={}", JSONObject.toJSONString(result), JSONObject.toJSONString(param));
        }
        return SimpleResult.ok(result);
    }

//    /**
//     * mku商品聚堆信息
//     */
//    @PostMapping("/group")
//    public SimpleResult<IscAppMkuClientGroupDTO> groupInfo(@RequestBody IscAppMkuClientDetailReqDTO param, BaseInfo baseInfo) {
//        IscAppMkuClientGroupDTO result = iscMkuClientService.groupInfo(param, baseInfo);
//
//        if (null!=commonDUCCConfig && Boolean.TRUE.equals(commonDUCCConfig.getLogDebugSwitch())){
//            log.info("groupInfo, result={}, input={}", JSONObject.toJSONString(result), JSONObject.toJSONString(param));
//        }
//        return SimpleResult.ok(result);
//    }

    /**
     * 商品申请补货
     */
    @PostMapping("/replenishment")
    public SimpleResult<Boolean> replenishment(@RequestBody MkuReplenishmentSoaDTO param, BaseInfo baseInfo) {
        BeanUtils.copyProperties(baseInfo,param);
        return SimpleResult.ok(iscMkuClientService.replenishment(param));
    }

    @PostMapping("querySpecialAttr")
    public SimpleResult<Map<Long, Map<String,String>>> querySpecialAttr(@RequestBody IscAppMkuClientSpecialAttrReqDTO param, BaseInfo baseInfo) {
        param.setClientCode(baseInfo.getClientId());
        return SimpleResult.ok(iscMkuClientService.queryMkuSpecialAttr(param));
    }
}
