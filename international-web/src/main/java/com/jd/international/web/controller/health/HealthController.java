package com.jd.international.web.controller.health;

import com.jd.international.common.bean.SimpleResult;
import com.jd.international.common.utils.AESUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 健康检查接口
 */
@Slf4j
@RestController
@RequestMapping("/jdig/check")
public class HealthController {
    /**
     * 检查服务是否可用。
     *
     * @return 服务状态，"ok" 表示服务正常运行。
     */
    @GetMapping("/health")
    public SimpleResult health() {
        return SimpleResult.ok();
    }


    @GetMapping("/info")
    public SimpleResult str(String str) {
        String decryptedData = AESUtils.decrypt(str);
        return SimpleResult.ok(decryptedData);
    }
}
