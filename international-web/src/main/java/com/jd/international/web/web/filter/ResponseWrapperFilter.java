package com.jd.international.web.web.filter;

import com.alibaba.fastjson.JSONObject;
import com.jd.international.common.bean.InternationalResult;
import com.jd.international.common.bean.SimpleResult;
import com.jd.international.common.config.InternationalConfig;
import com.jd.international.common.exception.ResponseErrorCode;
import com.jd.international.common.utils.AESUtils;
import com.jd.international.web.web.filter.assistant.CustomHttpServletResponseWrapper;
import com.jd.international.web.web.filter.assistant.ReturnUtil;
import com.jdi.isc.library.common.constants.i18n.I18nKeyConstant;
import com.jdi.isc.library.common.response.ResponseCode;
import com.jdi.isc.library.i18n.datasource.cache.I18nCacheManager;
import com.jdi.isc.product.soa.api.common.LangConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Description: 响应结果封装过滤器
 * @Author: zhaojianguo21
 * @Date: 2024/8/29
 **/

@Slf4j
@Component
public class ResponseWrapperFilter implements Filter {

    @Autowired
    private I18nCacheManager i18nCacheManager;

    private static final String ENV = "env";
    private static final String successDefMsgZh = "成功";

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 初始化操作
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;

        String env = LangConstant.LANG_ZH;
        try {
            // 包装响应对象
            CustomHttpServletResponseWrapper responseWrapper = new CustomHttpServletResponseWrapper(httpServletResponse);
            responseWrapper.setStatus(HttpStatus.OK.value());

            // 继续执行过滤器链
            chain.doFilter(request, responseWrapper);

            // 处理响应结果
            String responseBody = responseWrapper.getTextContent();
            env = httpServletRequest.getParameter(ENV) != null ? httpServletRequest.getParameter(ENV) : LangConstant.LANG_ZH;
            String encrypt = httpServletRequest.getHeader("encrypt");
            responseBody = handleResult(responseBody, env, encrypt);

            // 写入处理后的响应结果
            httpServletResponse.getOutputStream().write(responseBody.getBytes());
        } catch (Exception e) {
            log.error("ResponseWrapperFilter Exception.", e);
            String internationalParamsByLangType = i18nCacheManager.getValueOrDefault(env, ResponseCode.COMMON_RESPONSE_FAIL.getMessage());
            ReturnUtil.writeErrorMsg(httpServletResponse, ResponseCode.COMMON_RESPONSE_FAIL.getCode(),
                    StringUtils.isNotBlank(internationalParamsByLangType) ? internationalParamsByLangType : ResponseCode.COMMON_RESPONSE_FAIL.getMessage());
        }
    }

    @Override
    public void destroy() {
        // 销毁操作
    }

    private String handleResult(String responseBody, String env, String encrypt) {
        if (StringUtils.isBlank(responseBody)) {
            log.warn("handleResult, responseBody is null. set default responseBody.");
            responseBody = "{}";
        }
        SimpleResult simpleResult = JSONObject.parseObject(responseBody, SimpleResult.class);

        InternationalResult result = new InternationalResult();
        result.setCode(simpleResult.getCode());
        result.setSuccess(simpleResult.isSuccess());
        result.setMsg(simpleResult.getMsg());

        String i18nMsg = handleI18nMsg(simpleResult, env);
        if (StringUtils.isNotBlank(i18nMsg)) {
            result.setMsg(i18nMsg);
        }

        if (simpleResult.getValue() != null && !"0".equals(encrypt)) {
            result.setValue(AESUtils.encrypt(JSONObject.toJSONString(simpleResult.getValue())));
        } else {
            result.setValue(simpleResult.getValue());
        }
        return JSONObject.toJSONString(result);
    }

    private String handleI18nMsg(SimpleResult simpleResult, String lang) {
        // 响应成功的词条
        if (simpleResult.isSuccess()) {
            String i18nMsg = null;
            if (successDefMsgZh.equals(simpleResult.getMsg()) && !LangConstant.LANG_ZH.equals(lang)) {
                i18nMsg = i18nCacheManager.getValueOrDefault(lang, I18nKeyConstant.COMMON_SUCCESS);
                log.info("handI18nMsg, success. i18nMsg={}, lang={}", i18nMsg, lang);
            }
            return i18nMsg;
        }

        // 响应失败的词条
        // 获取定制的国际化词条
        String i18nEntry = simpleResult.getMsgI18nEntry();
        if (StringUtils.isBlank(i18nEntry) && StringUtils.isNotBlank(simpleResult.getCode())) {
            // 若没有指定国际化词条，则再根据code获取国际化词条
            i18nEntry = ResponseErrorCode.queryI18nEntryByCode(simpleResult.getCode());
        }

        if (StringUtils.isBlank(i18nEntry)) {
            log.warn("handI18nMsg, not exists i18nEntry. code={}", simpleResult.getCode());
            return null;
        }
        // 获取国际化信息
        String i18nMsg = i18nCacheManager.getValueOrDefault(lang, i18nEntry);
        // 替换国际化词条中的占位符
        if (null != simpleResult.getErrorParam() && simpleResult.getErrorParam().length > 0) {
            i18nMsg = String.format(i18nMsg, simpleResult.getErrorParam());
            log.info("handI18nMsg, format i18nMsg. i18nMsg={} i18nEntry={}, errorParam={}, lang={}, code={}"
                    , i18nMsg, i18nEntry, JSONObject.toJSONString(simpleResult.getErrorParam()), lang, simpleResult.getCode());
        }

        log.info("handI18nMsg, end. i18nMsg={} i18nEntry={}, errorParam={}, lang={}, code={}"
                , i18nMsg, i18nEntry, JSONObject.toJSONString(simpleResult.getErrorParam()), lang, simpleResult.getCode());
        return StringUtils.isNotBlank(i18nMsg) ? i18nMsg : null;
    }
}
