package com.jd.international.web.controller.order;

import com.alibaba.fastjson.JSONObject;
import com.jd.international.common.bean.BaseInfo;
import com.jd.international.common.bean.SimpleResult;
import com.jd.international.common.utils.IpUtils;
import com.jd.international.domain.approval.multi.MultiApprovalSelectDTO;
import com.jd.international.domain.order.*;
import com.jd.international.service.order.OrderService;
import com.jdi.isc.library.common.exception.BizException;
import com.jdi.isc.library.common.exception.BizI18nException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

import static com.jdi.isc.library.common.constants.i18n.I18nKeyConstant.COMMON_OPERATE_FAIL;

@Slf4j
@RestController
@RequestMapping("/jdig/trade")
@RequiredArgsConstructor
public class OrderController {
    private final OrderService orderService;

    /**
     * 立即购买
     * @param orderBuyNowRequest
     * @param baseInfo
     * @return
     */
    @PostMapping("/ispBuyNow")
    public SimpleResult ispBuyNow(@RequestBody OrderBuyNowRequest orderBuyNowRequest, BaseInfo baseInfo){
        log.info("ispBuyNow, orderBuyNowRequest={}, baseInfo={}", JSONObject.toJSONString(orderBuyNowRequest), JSONObject.toJSONString(baseInfo));
        orderService.ispBuyNow(orderBuyNowRequest,baseInfo);
        return SimpleResult.ok();
    }

    /**
     * 立即购买
     * @param orderBuyNowRequest
     * @param baseInfo
     * @return
     */
    @PostMapping("/batch/ispBuyNow")
    public SimpleResult ispBatchBuyNow(@RequestBody List<OrderBuyNowRequest> orderBuyNowRequest, BaseInfo baseInfo){
        log.info("ispBatchBuyNow, orderBuyNowRequest={}, baseInfo={}", JSONObject.toJSONString(orderBuyNowRequest), JSONObject.toJSONString(baseInfo));
        orderService.ispBatchBuyNow(orderBuyNowRequest, baseInfo);
        return SimpleResult.ok();
    }

    /**
     * 生成结算页
     * @param orderSettlementRequest
     * @param baseInfo
     * @return
     */
    @PostMapping("/settlementPage")
    public SimpleResult settlementPage(@RequestBody OrderSettlementRequest orderSettlementRequest, BaseInfo baseInfo){
        log.info("settlementPage, orderSettlementRequest={}, baseInfo={}", JSONObject.toJSONString(orderSettlementRequest), JSONObject.toJSONString(baseInfo));
        return SimpleResult.ok(orderService.settlementPage(orderSettlementRequest, baseInfo));
    }

    /**
     * 结算页审批流下拉框
     * @param orderApprovalListReq
     * @param baseInfo
     * @return
     */
    @PostMapping("/approval/list")
    public SimpleResult<List<MultiApprovalSelectDTO>> approvalList(@RequestBody OrderApprovalListReq orderApprovalListReq, BaseInfo baseInfo){
        log.info("approvalList, orderApprovalListReq={}, baseInfo={}", JSONObject.toJSONString(orderApprovalListReq), JSONObject.toJSONString(baseInfo));
        return SimpleResult.ok(orderService.approvalList(orderApprovalListReq, baseInfo));
    }

    /**
     * 提交订单
     * @param submitOrderInfo
     * @param baseInfo
     * @return
     */
    @PostMapping("/submit")
    public SimpleResult submit(@RequestBody SubmitOrderInfo submitOrderInfo, BaseInfo baseInfo, HttpServletRequest request){
        baseInfo.setUserIp(IpUtils.getRealIp(request));
        try {
            return SimpleResult.ok(orderService.submit(submitOrderInfo, baseInfo));
        } catch (BizException e) {
            log.error("提单异常:", e);
            return SimpleResult.error(e.getCode(), e.getMessage());
        } catch (BizI18nException e) {
            log.error("提单异常:", e);
            return SimpleResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("提单异常:", e);
            return SimpleResult.error(COMMON_OPERATE_FAIL);
        }
    }
}
