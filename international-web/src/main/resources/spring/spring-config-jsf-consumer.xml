<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd">

    <!-- 注册中心   *************** i.jsf.jd.com  #测试index服务地址 -->
    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="i.jsf.jd.com"/>

    <jsf:consumer id="addressManagerProvider" interface="com.jd.international.soa.sdk.common.address.AddressManagerProvider"
                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">
    </jsf:consumer>

    <jsf:consumer id="addressManagerAuthorityProvider" interface="com.jd.international.soa.sdk.common.address.AddressManagerAuthorityProvider"
                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">
    </jsf:consumer>

    <jsf:consumer id="categoryProvider" interface="com.jd.international.soa.sdk.common.category.CategoryProvider"
                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">
    </jsf:consumer>
    <jsf:consumer id="cartProvider" interface="com.jd.international.soa.sdk.common.cart.CartProvider"
                  alias="${jsf.international.soa.alias}" timeout="30000" check="false" serialization="hessian">
    </jsf:consumer>
    <jsf:consumer id="accountProvider" interface="com.jd.international.soa.sdk.common.account.AccountProvider"
                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">
    </jsf:consumer>
    <jsf:consumer id="mailProvider" interface="com.jd.international.soa.sdk.common.mail.MailProvider"
                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">
    </jsf:consumer>
<!--    <jsf:consumer id="productQueryProvider" interface="com.jd.international.soa.sdk.wares.product.ProductQueryProvider"-->
<!--                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">-->
<!--    </jsf:consumer>-->

    <jsf:consumer id="multiApproveService" interface="com.jd.ka.mro.workflow.soa.sdk.service.MultiApproveService"
                  alias="${jsf.workflow.international.soa.alias}" timeout="5000" check="false">
    </jsf:consumer>
    <jsf:consumer id="roleInfoService" interface="com.jd.ka.mro.workflow.soa.sdk.service.RoleInfoService"
                  alias="${jsf.workflow.international.soa.alias}" timeout="5000" check="false">
    </jsf:consumer>
    <jsf:consumer id="processRoleRelService" interface="com.jd.ka.mro.workflow.soa.sdk.service.ProcessRoleRelService"
                  alias="${jsf.workflow.international.soa.alias}" timeout="5000" check="false">
    </jsf:consumer>
    <!--多条件审批-->
    <jsf:consumer id="multiConditionProcessService" interface="com.jd.ka.mro.workflow.soa.sdk.service.MultiConditionProcessService"
                  alias="${jsf.workflow.multiConditionProcess.alias}" protocol="jsf" timeout="5000">
    </jsf:consumer>
    <!--多条件审批针对pin的查询服务-->
    <jsf:consumer id="multiConditionRoleService" interface="com.jd.ka.mro.workflow.soa.sdk.service.MultiConditionRoleService"
                  alias="${jsf.workflow.multiConditionRole.alias}" protocol="jsf" timeout="5000">
    </jsf:consumer>

    <jsf:consumer id="getAccountService" interface="com.jd.ka.user.soa.sdk.service.GetAccountService"
                  alias="${jsf.kaUser.international.soa.alias}" timeout="5000" check="false">
    </jsf:consumer>
<!--    <jsf:consumer id="iJdiStandardProductQueryService" interface="com.jd.jdi.product.sku.boost.sdk.es.IJdiStandardProductQueryService"-->
<!--                  alias="${jsf.iJdiStandardProductQueryService.alias}" timeout="5000" check="false">-->
<!--    </jsf:consumer>-->
    <jsf:consumer id="multiOrderService" interface="com.jd.ka.mro.workflow.soa.sdk.service.MultiOrderService"
                  alias="${jsf.workflow.international.soa.alias}" timeout="5000" check="false">
    </jsf:consumer>
    <jsf:consumer id="tradeProvider" interface="com.jd.international.soa.sdk.order.order.TradeProvider"
                  alias="${jsf.international.soa.alias}" timeout="10000" check="false" serialization="hessian">
    </jsf:consumer>
    <jsf:consumer id="multiApproveHistoryService" interface="com.jd.ka.mro.workflow.soa.sdk.service.MultiApproveHistoryService"
                  alias="${jsf.workflow.international.soa.alias}" timeout="5000" check="false">
    </jsf:consumer>
    <jsf:consumer id="approvalFileProvider" interface="com.jd.international.soa.approval.sdk.ApprovalFileProvider"
                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">
    </jsf:consumer>
    <jsf:consumer id="multiApprovalExecuteService" interface="com.jd.ka.mro.workflow.soa.sdk.service.approval.MultiApprovalExecuteService"
                  alias="${jsf.workflow.international.soa.alias}" timeout="5000" check="false">
    </jsf:consumer>
    <jsf:consumer id="userProvider" interface="com.jd.international.soa.sdk.common.user.UserProvider"
                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">
    </jsf:consumer>
    <jsf:consumer id="multiApprovalQueryService" interface="com.jd.ka.mro.workflow.soa.sdk.service.approval.MultiApprovalQueryService"
                  alias="${jsf.workflow.international.soa.alias}" timeout="5000" check="false">
    </jsf:consumer>
    <jsf:consumer id="searchProvider" interface="com.jd.international.soa.sdk.common.search.SearchProvider"
                  alias="${jsf.international.soa.alias}" timeout="10000" check="false" serialization="hessian">
    </jsf:consumer>
    <jsf:consumer id="intlDownloadProvider" interface="com.jd.international.soa.sdk.order.download.IntlDownloadProvider"
                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">
    </jsf:consumer>
    <jsf:consumer id="orderQueryProvider" interface="com.jd.international.soa.sdk.order.orderList.OrderQueryProvider"
                  alias="${jsf.international.soa.alias}" timeout="${jsf.consumer.isc.order.timeout}" check="false" serialization="hessian">
    </jsf:consumer>
<!--    <jsf:consumer id="priceProvider" interface="com.jd.international.soa.sdk.common.price.PriceProvider"-->
<!--                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">-->
<!--    </jsf:consumer>-->
    <jsf:consumer id="brandProvider" interface="com.jd.international.soa.sdk.wares.product.BrandProvider"
                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">
    </jsf:consumer>
    <jsf:consumer id="currencyManagerProvider" interface="com.jd.international.soa.sdk.order.currency.CurrencyManagerProvider"
                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">
    </jsf:consumer>
    <jsf:consumer id="marketProvider" interface="com.jd.international.soa.sdk.common.market.MarketProvider"
                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">
    </jsf:consumer>
    <jsf:consumer id="iscCategoryApiService" interface="com.jd.international.soa.sdk.common.isc.category.IscCategoryApiService"
                  alias="${jsf.consumer.isc.category.alias}" timeout="5000" check="false" serialization="hessian">
    </jsf:consumer>
    <jsf:consumer id="iscMkuClientApiService" interface="com.jd.international.soa.sdk.common.isc.mku.IscMkuClientApiService"
                  alias="${jsf.consumer.isc.mkuClient.alias}" timeout="15000" check="false" serialization="hessian">
    </jsf:consumer>
    <jsf:consumer id="iscCalculateFreightApiService" interface="com.jd.international.soa.sdk.common.isc.freight.IscCalculateFreightApiService"
                  alias="${jsf.consumer.isc.calculateFreight.alias}" timeout="10000" check="false" serialization="hessian">
    </jsf:consumer>
    <!--  工采国际sao服务 地址服务-->
    <jsf:consumer id="iscAreaApiService"
                  interface="com.jd.international.soa.sdk.common.isc.area.IscAreaApiService"
                  protocol="jsf" alias="${jsf.consumer.isc.area.alias}"  serialization="hessian"/>
    <!--  工采国际sao服务 地址服务-->
    <jsf:consumer id="customerApiService"
                  interface="com.jd.international.soa.sdk.common.customer.CustomerApiService"
                  protocol="jsf" alias="${jsf.consumer.customer.alias}"  serialization="hessian"/>

    <!--配置功能-->
    <jsf:consumer id="configFunctionProvider" interface="com.jd.international.soa.sdk.approval.config.ConfigFunctionProvider"
                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">
    </jsf:consumer>
    <!--订单配置-->
    <jsf:consumer id="orderConfigInfoProvider" interface="com.jd.international.soa.sdk.approval.config.OrderConfigInfoProvider"
                  alias="${jsf.international.soa.alias}" timeout="5000" check="false" serialization="hessian">
    </jsf:consumer>
    <!--履约时效-->
    <jsf:consumer id="promiseSoaApiService" interface="com.jd.international.soa.sdk.wares.promise.PromiseSoaApiService"
                  alias="${jsf.consumer.customer.alias}" timeout="10000" check="false" serialization="hessian">
    </jsf:consumer>

    <!-- mku相关服务 -->
    <jsf:consumer id="iscProductSoaMkuApiService" interface="com.jdi.isc.product.soa.api.wisp.mku.MkuClientApiService"
                  alias="${jsf.consumer.isc.product.soa.alias}" timeout="10000" check="false" serialization="hessian">
    </jsf:consumer>

    <!-- mku物料读服务 -->
    <jsf:consumer id="iscMkuMaterialReadApiService"
                  interface="com.jdi.isc.product.soa.api.material.IscMkuMaterialReadApiService"
                  alias="${jsf.consumer.isc.product.soa.alias}" timeout="10000" retries="2"
                  check="false" serialization="hessian"
                  protocol="jsf">
    </jsf:consumer>
    <!-- mku物料写服务 -->
    <jsf:consumer id="iscMkuMaterialWriteApiService"
                  interface="com.jdi.isc.product.soa.api.material.IscMkuMaterialWriteApiService"
                  alias="${jsf.consumer.isc.product.soa.alias}" timeout="10000" retries="2"
                  check="false" serialization="hessian"
                  protocol="jsf">
    </jsf:consumer>

    <!-- 任务中心，销端任务 -->
    <jsf:consumer id="customerTaskApiService"
                  interface="com.jdi.isc.task.center.api.task.customer.CustomerTaskApiService"
                  alias="${jsf.consumer.isc.task.center.alias}"
                  timeout="${jsf.consumer.isc.task.center.timeout}"
                  retries="1" check="false" serialization="hessian"
                  protocol="jsf">
    </jsf:consumer>

    <!-- 语言 -->
    <jsf:consumer id="iscProductSoaLangApiService"
                  interface="com.jdi.isc.product.soa.api.wimp.lang.IscProductSoaLangApiService"
                  protocol="jsf"
                  alias="${jsf.consumer.isc.product.soa.alias}"
                  timeout="10000" >
    </jsf:consumer>

    <!-- 动态生成模板服务 -->
    <jsf:consumer id="iscTaskTemplateApiService"
                  interface="com.jdi.isc.task.center.api.template.IscTaskTemplateApiService"
                  alias="${jsf.consumer.isc.task.center.alias}"
                  timeout="10000"
                  retries="0"
                  protocol="jsf">
    </jsf:consumer>

    <jsf:consumer id="afterSalesProvider"
                  interface="com.jd.international.soa.sdk.order.afterSales.AfterSalesProvider"
                  protocol="jsf"
                  alias="${jsf.consumer.isc.international.soa.alias}"
                  timeout="${jsf.consumer.isc.international.soa.timeout}" >
    </jsf:consumer>
    
    <jsf:consumer id="iJdiExtendDataService"
                  interface="com.jd.k2.gd.boost.sdk.soa.IJdiExtendDataService"
                  alias="${jsf.consumer.k2.gd.alias}"
                  timeout="${jsf.consumer.k2.gd.timeout}" >
    </jsf:consumer>

    <!-- MKU特殊属性查询 -->
    <jsf:consumer id="iscProductSoaMkuReadApiService"
                  interface="com.jdi.isc.product.soa.api.mku.IscProductSoaMkuReadApiService"
                  protocol="jsf"
                  alias="${jsf.consumer.isc.product.soa.alias}"
                  timeout="10000" >
    </jsf:consumer>

    <jsf:consumer id="customerReadService"
                  interface="com.jdi.isc.aggregate.read.api.customer.CustomerReadService"
                  protocol="jsf" alias="${jsf.consumer.customer.read.alias}" timeout="10000"
                  serialization="hessian">
    </jsf:consumer>

    <jsf:consumer id="userPassportExportService" interface="com.jd.user.sdk.export.UserPassportExportService"
                  protocol="jsf" alias="${jd.jsf.consumer.passport.alias}" timeout="10000" retries="0" check="false">
        <jsf:parameter  key="source" value="international-api" hide="true" />
    </jsf:consumer>

    <jsf:consumer id="purchaseOrderReadApiService"
                  interface="com.jdi.isc.order.center.api.purchaseOrderRead.PurchaseOrderReadApiService"
                  protocol="jsf"
                  alias="${jsf.consumer.common.alias}" timeout="${jsf.consumer.common.timeout}"
                  serialization="hessian">
    </jsf:consumer>

    <!-- 查询Mku库存服务 -->
    <jsf:consumer id="iscMkuStockReadApiService"
                  interface="com.jdi.isc.product.soa.stock.mku.IscMkuStockReadApiService"
                  protocol="jsf"
                  alias="${jsf.consumer.common.alias}"
                  timeout="${jsf.consumer.common.longTimeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 工采国际商品服务-->
    <jsf:consumer id="aggMkuClientApiService" interface="com.jdi.isc.aggregate.read.wisp.api.mku.MkuClientApiService"
                  alias="${jsf.consumer.common.alias}"
                  timeout="${jsf.consumer.common.longTimeout}"
                  protocol="jsf" serialization="hessian"/>

    <jsf:consumer id="mkuRelationApiService" interface="com.jdi.isc.aggregate.read.api.mku.MkuRelationApiService"
                  alias="${jsf.consumer.common.alias}"
                  timeout="${jsf.consumer.common.longTimeout}"
                  check="false" serialization="hessian" protocol="jsf">
    </jsf:consumer>

    <!--客户池服务-->
    <jsf:consumer id="iscProductSoaCustomerMkuReadApiService" interface="com.jdi.isc.product.soa.api.customerMku.IscProductSoaCustomerMkuReadApiService"
                  protocol="jsf"
                  alias="${jsf.consumer.common.alias}"
                  timeout="${jsf.consumer.common.timeout}" />

    <jsf:consumer id="areaApiService" interface="com.jdi.isc.aggregate.read.wisp.api.area.AreaApiService"
                  protocol="jsf"
                  alias="${jsf.consumer.common.alias}"
                  timeout="${jsf.consumer.common.timeout}" />
    <!--订单修改po单号-->
    <jsf:consumer id="orderWriteManageApiService" interface="com.jdi.isc.order.center.api.OrderWriteManageApiService"
                  protocol="jsf"
                  alias="${jsf.consumer.common.alias}"
                  timeout="${jsf.consumer.common.timeout}" />

    <jsf:consumer id="promiseTimeReadApiService" interface="com.jdi.isc.fulfillment.soa.api.promiseTime.PromiseTimeReadApiService"
                  protocol="jsf"
                  alias="${jsf.consumer.common.alias}"
                  timeout="${jsf.consumer.common.timeout}" />

    <jsf:consumer id="jsfDataService"
                  interface="com.jd.dd.open.gw.api.DataService"
                  alias="dogdata" >
        <jsf:parameter key="token" value="35ebe4d9de7e42938e28fedc6f1f97d8" hide="true" />
    </jsf:consumer>


    <jsf:consumer id="afterSalesWriteApiService" interface="com.jdi.isc.order.center.api.aftersales.AfterSalesWriteApiService"
                  protocol="jsf"
                  alias="${jsf.consumer.common.alias}"
                  timeout="${jsf.consumer.common.timeout}" />

    <jsf:consumer id="afterSalesReadApiService" interface="com.jdi.isc.order.center.api.aftersales.AfterSalesReadApiService"
                  protocol="jsf"
                  alias="${jsf.consumer.common.alias}"
                  timeout="${jsf.consumer.common.timeout}" />

    <!-- 以后WISP查询商品全都从这走 -->
    <jsf:consumer id="productReadApiService" interface="com.jd.international.soa.sdk.common.isc.mku.ProductReadApiService"
                  protocol="jsf"
                  alias="${jsf.consumer.common.alias}"
                  timeout="10000" />
</beans>