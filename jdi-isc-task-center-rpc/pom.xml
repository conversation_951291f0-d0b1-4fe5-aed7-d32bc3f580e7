<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>jdi-isc-task-center</artifactId>
        <groupId>com.jdi.isc.task.center</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>jdi-isc-task-center-rpc</artifactId>
    <name>jdi-isc-task-center-rpc</name>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.jdi.isc.task.center</groupId>
            <artifactId>jdi-isc-task-center-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.task.center</groupId>
            <artifactId>jdi-isc-task-center-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.aggregate.read</groupId>
            <artifactId>jdi-isc-aggregate-read-wisp-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.common</groupId>
            <artifactId>jdi-common-frame-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.product.soa</groupId>
            <artifactId>jdi-isc-product-soa-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.international.soa</groupId>
            <artifactId>international-common-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>gms.jd.component.crs.proxy</groupId>
            <artifactId>component-crs-proxy-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.aggregation</groupId>
            <artifactId>relation.aggregation.client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.aggregate.read</groupId>
            <artifactId>jdi-isc-aggregate-read-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.wanfang</groupId>
            <artifactId>wanfang-support-soa-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.order.center</groupId>
            <artifactId>jdi-isc-order-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.ka.gpt.soa</groupId>
            <artifactId>gpt-soa-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.wanfang</groupId>
                    <artifactId>wanfang-support-soa-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jsf-limiter-sdk</artifactId>
                    <groupId>com.jd.jsf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsf-lite</artifactId>
                    <groupId>com.jd</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.leo.mail</groupId>
            <artifactId>product_produce-rpc-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.library</groupId>
            <artifactId>jdi-isc-library-i18n-datasource</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jp</groupId>
            <artifactId>print-templet-center-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator-annotation-processor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.product.soa</groupId>
            <artifactId>jdi-isc-product-soa-stock-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.bizcomponent.center</groupId>
            <artifactId>jdi-isc-bizcomponent-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.product.soa</groupId>
            <artifactId>jdi-isc-product-soa-price-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.kunlun</groupId>
            <artifactId>kunlun-algo-cls-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.ump</groupId>
            <artifactId>profiler</artifactId>
        </dependency>
    </dependencies>
</project>
