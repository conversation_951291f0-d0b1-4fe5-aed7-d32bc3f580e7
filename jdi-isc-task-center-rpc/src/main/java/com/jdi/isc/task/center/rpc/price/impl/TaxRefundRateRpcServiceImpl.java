package com.jdi.isc.task.center.rpc.price.impl;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.markupRate.req.MarkupRateReqDTO;
import com.jdi.isc.product.soa.api.price.taxRefundRate.IscTaxRefundRateWriteApiService;
import com.jdi.isc.product.soa.api.price.taxRefundRate.res.TaxRefundRateDTO;
import com.jdi.isc.task.center.rpc.price.TaxRefundRateRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class TaxRefundRateRpcServiceImpl implements TaxRefundRateRpcService {

    @Resource
    private IscTaxRefundRateWriteApiService iscTaxRefundRateWriteApiService;

    @Override
    public StatusTaxRefundRateDTO saveOrUpdate(StatusTaxRefundRateDTO dto) {
        log.info("TaxRefundRateRpcServiceImpl.saveOrUpdate req:{}", JSON.toJSONString(dto));
        DataResponse<Boolean> response = null;
        try {
            dto.setLang(LangConstant.LANG_ZH);
            TaxRefundRateDTO taxRefundRateDTO = new TaxRefundRateDTO();
            taxRefundRateDTO.setUpdater(dto.getUpdater());
            taxRefundRateDTO.setCreator(dto.getUpdater());
            taxRefundRateDTO.setLang(LangConstant.LANG_ZH);
            taxRefundRateDTO.setTaxRefundRate(dto.getTaxRefundRate());
            taxRefundRateDTO.setLastCatId(dto.getLastCatId());
            taxRefundRateDTO.setSourceCountryCode(dto.getSourceCountryCode());
            taxRefundRateDTO.setTargetCountryCode(dto.getTargetCountryCode());
            response = iscTaxRefundRateWriteApiService.saveOrUpdate(taxRefundRateDTO);
            if(response.getSuccess()) {
                dto.success();
            }else {
                dto.failed(response.getMessage());
            }
            return dto;
        }catch (Exception e){
            log.error("TaxRefundRateRpcServiceImpl.saveOrUpdate req:{},res:{}", JSON.toJSONString(dto), JSON.toJSONString(e));
            dto.failed("系统异常，请联系管理员");
        }finally {
            log.info("TaxRefundRateRpcServiceImpl.saveOrUpdate req:{},res:{}", JSON.toJSONString(dto), JSON.toJSONString(response));
        }
        return dto;
    }
}
