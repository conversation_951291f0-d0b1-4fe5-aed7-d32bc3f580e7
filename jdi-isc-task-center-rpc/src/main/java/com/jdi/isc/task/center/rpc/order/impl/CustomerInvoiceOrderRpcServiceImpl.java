package com.jdi.isc.task.center.rpc.order.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.finance.customerInvoice.CustomerInvoiceOrderReadApiService;
import com.jdi.isc.order.center.api.finance.customerInvoice.req.CustomerInvoiceOrderPageReqDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceOrderResDTO;
import com.jdi.isc.task.center.rpc.order.CustomerInvoiceOrderRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 注释
 *
 * <AUTHOR>
 * @since 2025/8/18 13:54
 */
@Slf4j
@Service
public class CustomerInvoiceOrderRpcServiceImpl implements CustomerInvoiceOrderRpcService {

    @Autowired
    private CustomerInvoiceOrderReadApiService customerInvoiceOrderReadApiService;
    /**
     * 获取自助提报-订单分页信息
     *
     * @param reqDTO 分页请求参数
     * @return 分页后的订单列表
     */
    @Override
    public DataResponse<PageInfo<CustomerInvoiceOrderResDTO>> invoiceOrderPage(CustomerInvoiceOrderPageReqDTO reqDTO) {
        return customerInvoiceOrderReadApiService.invoiceOrderPage(reqDTO);
    }
}
