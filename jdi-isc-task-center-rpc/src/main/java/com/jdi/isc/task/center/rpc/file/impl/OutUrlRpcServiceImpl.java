package com.jdi.isc.task.center.rpc.file.impl;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.outUrl.IscOutUrlApiService;
import com.jdi.isc.product.soa.api.outUrl.req.OutUrlTransformReq;
import com.jdi.isc.product.soa.api.outUrl.res.OutUrlTransformRes;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.rpc.file.OutUrlRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 外链转内链的Rpc服务实现类
 * <AUTHOR>
 * @description：OutUrlRpcServiceImpl
 * @Date 2025-08-15
 */
@Slf4j
@Service
public class OutUrlRpcServiceImpl implements OutUrlRpcService {

    @Resource
    private IscOutUrlApiService iscOutUrlApiService;


    @Override
    public List<OutUrlTransformRes> batchTransform(Set<String> outUrlSet) {
        if (CollectionUtils.isEmpty(outUrlSet)) {
            return Collections.emptyList();
        }

        if (outUrlSet.size() <= Constant.PARTITION_SIZE) {
            log.error("OutUrlRpcServiceImpl.batchTransform outUrlSet size > 20");
            return batchTransformSub(new OutUrlTransformReq(Sets.newLinkedHashSet(outUrlSet)));
        }else {
            // 分批处理
            List<List<String>> partition = Lists.partition(Lists.newArrayList(outUrlSet), Constant.PARTITION_SIZE);
            // 结果汇总列表
            List<OutUrlTransformRes> result = Lists.newArrayListWithExpectedSize(outUrlSet.size());
            for (List<String> subList : partition) {
                result.addAll(this.batchTransformSub(new OutUrlTransformReq(Sets.newLinkedHashSet(subList))));
            }
            return result;
        }
    }

    @Override
    public Map<String, OutUrlTransformRes> batchTransformOutUrlMap(Set<String> outUrlSet) {
        List<OutUrlTransformRes> outUrlTransformResList = this.batchTransform(outUrlSet);

        return Optional.ofNullable(outUrlTransformResList)
                .orElseGet(ArrayList::new)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(OutUrlTransformRes::getOutUrl, Function.identity()));
    }

    private List<OutUrlTransformRes> batchTransformSub(OutUrlTransformReq req) {
        DataResponse<List<OutUrlTransformRes>> response = null;
        try {
            response = iscOutUrlApiService.batchTransform(req);
            if (Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getData())) {
                return response.getData();
            }
        } catch (Exception e) {
            log.error("OutUrlRpcServiceImpl.batchTransform error 入参:{}",JSON.toJSONString(req), e);
        }finally {
            log.info("OutUrlRpcServiceImpl.batchTransform req={},response={}", JSON.toJSONString(req), JSON.toJSONString(response));
        }
        return Collections.emptyList();
    }
}
