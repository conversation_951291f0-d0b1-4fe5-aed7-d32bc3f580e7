package com.jdi.isc.task.center.rpc.forecast;

import com.jdi.isc.order.center.api.forecast.biz.req.ForecastDetailOrderApiReq;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderDetailResp;

/**
 * 预报备采购单rpc服务
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/30
 */
public interface ForecastPurchaseOrderRpcService {

    /**
     * 查询预报备采购单详情
     */
    ForecastOrderDetailResp queryForecastOrderDetail(ForecastDetailOrderApiReq orderApiReq);

}