package com.jdi.isc.task.center.rpc.order;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.finance.customerInvoice.req.CustomerInvoiceOrderPageReqDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceOrderResDTO;

/**
 * 注释
 *
 * <AUTHOR>
 * @since 2025/8/16 19:08
 */
public interface CustomerInvoiceOrderRpcService {

    /**
     * 获取自助提报-订单分页信息
     *
     * @param reqDTO 分页请求参数
     * @return 分页后的订单列表
     */
    DataResponse<PageInfo<CustomerInvoiceOrderResDTO>> invoiceOrderPage(CustomerInvoiceOrderPageReqDTO reqDTO);
}
