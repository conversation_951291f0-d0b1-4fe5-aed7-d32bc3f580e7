package com.jdi.isc.task.center.rpc.order.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.finance.customerInvoice.CustomerInvoiceQualificationApiService;
import com.jdi.isc.order.center.api.finance.customerInvoice.req.CustomerInvoiceQualificationPageReqDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceQualificationRes;
import com.jdi.isc.task.center.rpc.order.CustomerInvoiceQualificationRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 注释
 *
 * <AUTHOR>
 * @since 2025/8/20 10:01
 */
@Slf4j
@Service
public class CustomerInvoiceQualificationRpcServiceImpl implements CustomerInvoiceQualificationRpcService {

    @Autowired
    private CustomerInvoiceQualificationApiService customerInvoiceQualificationApiService;

    /**
     * 获取资质分页列表
     *
     * @param req 请求参数
     * @return 分页列表
     */
    @Override
    public DataResponse<PageInfo<CustomerInvoiceQualificationRes>> getQualificationPage(CustomerInvoiceQualificationPageReqDTO req) {
        return customerInvoiceQualificationApiService.getQualificationPage(req);
    }
}
