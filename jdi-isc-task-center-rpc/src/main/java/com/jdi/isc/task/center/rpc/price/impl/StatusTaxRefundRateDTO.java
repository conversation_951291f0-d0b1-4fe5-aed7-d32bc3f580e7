package com.jdi.isc.task.center.rpc.price.impl;

import com.jdi.isc.product.soa.api.price.taxRefundRate.res.TaxRefundRateDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class StatusTaxRefundRateDTO extends TaxRefundRateDTO {

    /**
     * 表示当前SKU价格草稿是否有效。
     */
    private Boolean valid ;
    /**
     * 存储操作结果的字符串。
     */
    private String result;

    public void success() {
        this.valid = true;
        this.result = "success";
    }

    public void failed(String result) {
        this.valid = false;
        this.result = result;
    }
}
