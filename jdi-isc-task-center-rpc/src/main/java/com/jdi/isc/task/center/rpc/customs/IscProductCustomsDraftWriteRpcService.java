package com.jdi.isc.task.center.rpc.customs;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.customs.req.ProductCustomsDraftApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.biz.CategoryBuyerRelationDTO;
import com.jdi.isc.product.soa.api.wimp.category.biz.CategoryLangBatchSaveUpdateDTO;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2025/07/18
 **/
public interface IscProductCustomsDraftWriteRpcService {

    /**
     * 保存和更新数据
     * @param input
     * @return
     */
    DataResponse<Boolean> saveOrUpdate(ProductCustomsDraftApiDTO input);
}
