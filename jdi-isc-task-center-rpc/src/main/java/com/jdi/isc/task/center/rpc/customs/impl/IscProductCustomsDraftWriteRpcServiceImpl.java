package com.jdi.isc.task.center.rpc.customs.impl;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.api.category.IscProductSoaCategoryConfigStatisticsApiService;
import com.jdi.isc.product.soa.api.customs.IscProductCustomsDraftWriteApiService;
import com.jdi.isc.product.soa.api.customs.req.ProductCustomsDraftApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.CategoryBuyerRelationApiService;
import com.jdi.isc.product.soa.api.wimp.category.CategoryWriteApiService;
import com.jdi.isc.product.soa.api.wimp.category.biz.CategoryBuyerRelationDTO;
import com.jdi.isc.product.soa.api.wimp.category.biz.CategoryLangBatchSaveUpdateDTO;
import com.jdi.isc.task.center.rpc.category.CategoryWriteRpcService;
import com.jdi.isc.task.center.rpc.customs.IscProductCustomsDraftWriteRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2024/11/27 20:03
 **/
@Slf4j
@Service
public class IscProductCustomsDraftWriteRpcServiceImpl implements IscProductCustomsDraftWriteRpcService {

    @Resource
    private IscProductCustomsDraftWriteApiService iscProductCustomsDraftWriteApiService;

    @ToolKit
    @Override
    public DataResponse saveOrUpdate(ProductCustomsDraftApiDTO input) {
        DataResponse rpcRes = null;
        try {
            rpcRes = iscProductCustomsDraftWriteApiService.saveOrUpdate(input);
            if (null!=rpcRes && Boolean.TRUE.equals(rpcRes.getSuccess())){
                return DataResponse.success();
            }

            return null!=rpcRes?DataResponse.error(rpcRes.getCode(),rpcRes.getMessage()):DataResponse.error("未知结果");
        } catch (Exception e) {
            log.error("saveOrUpdate, input={}", JSONObject.toJSONString(input), e);
            return DataResponse.error("出错了。");
        }finally {
            log.info("saveOrUpdate, param:{},rpcRes={}", JSONObject.toJSONString(input),JSONObject.toJSONString(rpcRes));
        }
    }

}
