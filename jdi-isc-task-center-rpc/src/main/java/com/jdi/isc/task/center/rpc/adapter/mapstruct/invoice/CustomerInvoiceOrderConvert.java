package com.jdi.isc.task.center.rpc.adapter.mapstruct.invoice;

import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceOrderResDTO;
import com.jdi.isc.task.center.domain.task.excel.CustomerInvoiceOrderExcelDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 注释
 *
 * <AUTHOR>
 * @since 2025/8/18 16:09
 */
@Mapper
public interface CustomerInvoiceOrderConvert {
    /**
     * The constant INSTANCE.
     */
    CustomerInvoiceOrderConvert INSTANCE = Mappers.getMapper(CustomerInvoiceOrderConvert.class);

    /**
     * Convert export api dto list.
     *
     * @param records the records
     * @return the list
     */
    List<CustomerInvoiceOrderExcelDTO> convertExportApiDTO(List<CustomerInvoiceOrderResDTO> records);
}
