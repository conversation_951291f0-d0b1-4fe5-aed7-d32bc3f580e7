package com.jdi.isc.task.center.rpc.forecast.imp;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.order.center.api.forecast.ForecastOrderApiService;
import com.jdi.isc.order.center.api.forecast.biz.req.ForecastDetailOrderApiReq;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderDetailResp;
import com.jdi.isc.task.center.common.utils.HostUtil;
import com.jdi.isc.task.center.rpc.forecast.ForecastPurchaseOrderRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/30
 */

@Slf4j
@Service
public class ForecastPurchaseOrderRpcServiceImpl implements ForecastPurchaseOrderRpcService {

    @Value("${order.systemCode}")
    private String systemCode;

    @Resource
    private ForecastOrderApiService forecastOrderApiService;

    @ToolKit
    public ForecastOrderDetailResp queryForecastOrderDetail(ForecastDetailOrderApiReq orderApiReq) {
        orderApiReq.setSystemCode(systemCode);
        orderApiReq.setUserIp(HostUtil.getLocalIP());
        orderApiReq.setServiceIp(HostUtil.getLocalIP());
        DataResponse<ForecastOrderDetailResp> dataResponse = forecastOrderApiService.detailForecastOrderToVc(orderApiReq);
        if (Objects.nonNull(dataResponse) && Boolean.TRUE.equals(dataResponse.getSuccess())) {
            return dataResponse.getData();
        }
        log.error("ForecastPurchaseOrderRpcServiceImpl.queryForecastOrderDetail param:{},response:{}", JSONObject.toJSONString(orderApiReq),JSONObject.toJSONString(dataResponse));
        throw new RuntimeException("查询预报备采购单详情失败");
    }
}