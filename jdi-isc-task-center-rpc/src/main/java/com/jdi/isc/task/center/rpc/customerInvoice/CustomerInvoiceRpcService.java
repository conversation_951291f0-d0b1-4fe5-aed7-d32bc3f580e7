package com.jdi.isc.task.center.rpc.customerInvoice;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.finance.customerInvoice.req.CustomerInvoicePageReqDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.req.CustomerInvoiceSaveReqDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceApplyDTO;

public interface CustomerInvoiceRpcService {


    /**
     * 保存发票信息
     * @param reqDTO 发票保存请求对象
     * @return 保存结果，true表示保存成功，false表示保存失败
     */
    DataResponse<Boolean> saveInvoicePro(CustomerInvoiceSaveReqDTO reqDTO);


    /**
     * 获取开票分页信息
     * @param req 开票分页请求参数
     * @return 分页后的开票列表
     */
    DataResponse<PageInfo<CustomerInvoiceApplyDTO>> invoicePage(CustomerInvoicePageReqDTO req);

}
