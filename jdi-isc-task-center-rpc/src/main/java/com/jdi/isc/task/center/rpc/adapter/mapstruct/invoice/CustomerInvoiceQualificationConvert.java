package com.jdi.isc.task.center.rpc.adapter.mapstruct.invoice;

import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceQualificationRes;
import com.jdi.isc.task.center.domain.task.excel.CustomerInvoiceQualificationExcelDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 注释
 *
 * <AUTHOR>
 * @since 2025/8/20 10:17
 */
@Mapper
public interface CustomerInvoiceQualificationConvert {
    /**
     * The constant INSTANCE.
     */
    CustomerInvoiceQualificationConvert INSTANCE = Mappers.getMapper(CustomerInvoiceQualificationConvert.class);

    /**
     * Convert export api dto list.
     *
     * @param records the records
     * @return the list
     */
    List<CustomerInvoiceQualificationExcelDTO> convertExportApiDTO(List<CustomerInvoiceQualificationRes> records);
}
