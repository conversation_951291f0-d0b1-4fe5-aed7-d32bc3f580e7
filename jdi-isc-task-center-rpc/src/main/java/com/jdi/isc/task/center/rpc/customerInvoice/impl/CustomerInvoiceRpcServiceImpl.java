package com.jdi.isc.task.center.rpc.customerInvoice.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.finance.customerInvoice.CustomerInvoiceApiService;
import com.jdi.isc.order.center.api.finance.customerInvoice.CustomerInvoiceReadApiService;
import com.jdi.isc.order.center.api.finance.customerInvoice.req.CustomerInvoicePageReqDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.req.CustomerInvoiceSaveReqDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceApplyDTO;
import com.jdi.isc.task.center.rpc.customerInvoice.CustomerInvoiceRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CustomerInvoiceRpcServiceImpl implements CustomerInvoiceRpcService {


    @Autowired
    private CustomerInvoiceApiService customerInvoiceApiService;
    @Autowired
    private CustomerInvoiceReadApiService customerInvoiceReadApiService;





    /**
     * 保存发票信息。
     * @param reqDTO 发票保存请求对象，包含发票的详细信息。
     * @return 保存结果。
     */
    @Override
    public DataResponse<Boolean> saveInvoicePro(CustomerInvoiceSaveReqDTO reqDTO) {
        return customerInvoiceApiService.saveInvoicePro(reqDTO);
    }


    /**
     * 获取开票分页信息
     *
     * @param req 开票分页请求参数
     * @return 分页后的开票列表
     */
    @Override
    public DataResponse<PageInfo<CustomerInvoiceApplyDTO>> invoicePage(CustomerInvoicePageReqDTO req) {
        return customerInvoiceReadApiService.invoicePage(req);
    }




}
