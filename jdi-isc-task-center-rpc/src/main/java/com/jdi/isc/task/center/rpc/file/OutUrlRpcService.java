package com.jdi.isc.task.center.rpc.file;


import com.jdi.isc.product.soa.api.outUrl.res.OutUrlTransformRes;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 图片外链转内链的Rpc服务接口
 * <AUTHOR>
 * @description：OutUrlRpcService
 * @Date 2025-08-15
 */
public interface OutUrlRpcService {

    /**
     * 批量外链转内链 URL
     * @param outUrlSet 转换请求对象，包含需要转换的外链 URL 信息
     * @return 外链转内链的结果列表
     */
    List<OutUrlTransformRes> batchTransform(Set<String> outUrlSet);

    /**
     * 批量转换外部URL的方法
     * @param outUrlSet 需要转换的外部URL集合
     * @return 转换后的外部URL映射关系，键为原始URL，值为转换后的URL信息
     */
    Map<String,OutUrlTransformRes> batchTransformOutUrlMap(Set<String> outUrlSet);
}
