package com.jdi.isc.task.center.rpc.mq.impl;

import com.alibaba.fastjson.JSONObject;
import com.jd.jmq.client.producer.Producer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.task.center.rpc.mq.TaskCenterMqService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/11/4
 * @Description
 */
@Slf4j
@Service
public class TaskCenterMqServiceImpl implements TaskCenterMqService {

    @JmqProducer(name = "jdiIscTaskCenterProducer")
    private Producer producer;

    @Override
    public void sendMessageRetry(String businessId, Object messageObj,String topic){
        try {
            sendMessage(businessId, messageObj, topic);
            log.info("[{} mq product ] [生产消息] msg : {}", topic, JSONObject.toJSONString(messageObj));
        }catch (Exception e){
            log.error("[{} mq product ] [生产消息] send mq error ", topic, e);
            try {
                sendMessage(businessId, messageObj, topic);
            } catch (Exception e1) {
                log.error("[{} mq product ] [生产消息] send mq error ", JSONObject.toJSONString(messageObj), topic, e1);
                Profiler.businessAlarm("jdi.isc.order.center.OrderMqService.sendMessage.error", String.format("%s MQ发送失败, topic=%s,businessId=%s", LevelCode.P0.getMessage(), topic, businessId));
            }
        }
    }

    /**
     * 发送消息到指定主题。
     * @param businessId 业务ID，用于标识消息来源。
     * @param messageObj 消息对象，会被序列化为JSON字符串。
     * @param topic 消息主题，指定消息要发送到的队列。
     * @throws JMQException 如果发送消息失败，会抛出此异常。
     */
    private void sendMessage(String businessId,Object messageObj,String topic) throws JMQException {
        String msgBody = JSONObject.toJSONString(messageObj);
        Message message = new Message(topic,msgBody,businessId);
        log.info("[{} mq product ] [生产消息] msg : {}", topic, JSONObject.toJSONString(message) );
        producer.send(message);
    }
}
