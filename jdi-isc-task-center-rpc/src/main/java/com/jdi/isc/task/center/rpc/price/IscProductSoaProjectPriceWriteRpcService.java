package com.jdi.isc.task.center.rpc.price;

import com.jdi.isc.product.soa.api.price.projectPrice.IscProductSoaProjectPriceWriteApiService;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceUpdateImportStatusApiDTO;
import com.jdi.isc.task.center.rpc.apporder.AbstractRpcService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * The type Isc product soa project price write rpc service.
 *
 * <AUTHOR>
 */
@Component
public class IscProductSoaProjectPriceWriteRpcService extends AbstractRpcService {

    @Resource
    private IscProductSoaProjectPriceWriteApiService iscProductSoaProjectPriceWriteApiService;

    /**
     * 上传Excel文件进行项目价格申请审核
     * @param input 项目价格申请审核的输入数据
     * @return 上传结果，true表示成功，false表示失败
     */
    public Integer updateExcelStatus(ProjectPriceUpdateImportStatusApiDTO input) {
        return unWriteWrapper(() -> iscProductSoaProjectPriceWriteApiService.updateExcelStatus(input), input, "更新项目单excel状态！");
    }


}
