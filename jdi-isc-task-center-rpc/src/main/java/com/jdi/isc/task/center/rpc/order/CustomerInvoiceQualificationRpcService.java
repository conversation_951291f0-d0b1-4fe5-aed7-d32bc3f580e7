package com.jdi.isc.task.center.rpc.order;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.order.center.api.finance.customerInvoice.req.CustomerInvoiceQualificationPageReqDTO;
import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerInvoiceQualificationRes;

/**
 * 注释
 *
 * <AUTHOR>
 * @since 2025/8/20 10:00
 */
public interface CustomerInvoiceQualificationRpcService {

    /**
     * 获取资质分页列表
     *
     * @param req 请求参数
     * @return 分页列表
     */
    DataResponse<PageInfo<CustomerInvoiceQualificationRes>> getQualificationPage(CustomerInvoiceQualificationPageReqDTO req);
}
