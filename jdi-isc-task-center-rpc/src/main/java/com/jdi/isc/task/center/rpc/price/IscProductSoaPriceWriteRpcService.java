package com.jdi.isc.task.center.rpc.price;


import com.jdi.isc.product.soa.price.api.price.req.PriceRefreshVO;
import com.jdi.isc.product.soa.price.api.salePrice.res.CustomerSkuPriceDetailDTO;

import java.util.List;

/**
 * 国际价格写服务
 * <AUTHOR>
 * @date 2025/3/4
 */
public interface IscProductSoaPriceWriteRpcService {

    /** 触发跨境国家成本价重算*/
    Boolean refreshCnCostPrice(List<PriceRefreshVO> input);

    /** 触发跨境入仓成本价重算*/
    Boolean refreshCnWarehouseCostPrice(List<PriceRefreshVO> input);

}
