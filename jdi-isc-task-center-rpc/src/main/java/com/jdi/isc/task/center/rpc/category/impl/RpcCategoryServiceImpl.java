package com.jdi.isc.task.center.rpc.category.impl;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.aggregate.read.wisp.api.category.CategoryApiService;
import com.jdi.isc.aggregate.read.wisp.api.category.biz.CategoryListReq;
import com.jdi.isc.aggregate.read.wisp.api.category.res.CategoryRes;
import com.jdi.isc.product.soa.api.category.IscProductSoaCategoryApiService;
import com.jdi.isc.product.soa.api.category.biz.CategoryQueryPageApiDTO;
import com.jdi.isc.product.soa.api.category.biz.CategoryReadDTO;
import com.jdi.isc.product.soa.api.supplier.res.CategoryTreeResDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.CategoryReqApiDTO;
import com.jdi.isc.product.soa.api.wimp.category.req.CategoryTreeReq;
import com.jdi.isc.task.center.rpc.category.RpcCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 类目Rpc接口
 * <AUTHOR>
 * @date 2024/6/14
 **/
@Slf4j
@Service
public class RpcCategoryServiceImpl implements RpcCategoryService {

    @Resource
    private CategoryApiService categoryApiService;
    @Resource
    private IscProductSoaCategoryApiService iscProductSoaCategoryApiService;

    @Resource
    private com.jdi.isc.product.soa.api.wimp.category.CategoryApiService jdiIscSoaCategoryApiService;

    @Override
    @ToolKit(exceptionWrap = true)
    public List<CategoryRes> page(CategoryListReq req) {
        try {
            DataResponse<List<CategoryRes>> dataResponse = categoryApiService.page(req);
            if (null != dataResponse && dataResponse.getSuccess()) {
                return dataResponse.getData();
            }
        } catch (Exception e) {
            log.error("RpcCategoryServiceImpl.page error req={}", JSON.toJSONString(req), e);
        }
        return null;
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public List<CategoryTreeResDTO> queryCategoryTree(CategoryReqApiDTO input){
        try {
            DataResponse<List<CategoryTreeResDTO>> dataResponse = jdiIscSoaCategoryApiService.queryList(input);
            log.error("RpcCategoryServiceImpl.queryCategoryTree 入参:[{}]", JSON.toJSONString(input));
            if (null != dataResponse && dataResponse.getSuccess()) {
                return dataResponse.getData();
            }
        } catch (Exception e) {
            log.error("RpcCategoryServiceImpl.queryCategoryTree 入参:[{}]", JSON.toJSONString(input),e);
        }
        return Collections.emptyList();
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public List<String> queryAllCategoryTile(CategoryTreeReq input){
        log.error("RpcCategoryServiceImpl.queryAllCategoryTile 入参:[{}]", JSON.toJSONString(input));
        try {
            DataResponse<List<String>> dataResponse = jdiIscSoaCategoryApiService.queryAllCategoryTile(input);
            log.error("RpcCategoryServiceImpl.queryAllCategoryTile 反参:[{}]", JSON.toJSONString(dataResponse));
            if (null != dataResponse && dataResponse.getSuccess()) {
                return dataResponse.getData();
            }
        } catch (Exception e) {
            log.error("RpcCategoryServiceImpl.queryAllCategoryTile 入参:[{}]", JSON.toJSONString(input),e);
        }
        return Collections.emptyList();
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public List<String> queryCategoryBuyerAllCategoryTile(CategoryQueryPageApiDTO.Request input){
        log.error("RpcCategoryServiceImpl.queryCategoryBuyerAllCategoryTile 入参:[{}]", JSON.toJSONString(input));
        try {
            DataResponse<List<String>> dataResponse = jdiIscSoaCategoryApiService.queryCategoryBuyerAllCategoryTile(input);
            log.error("RpcCategoryServiceImpl.queryCategoryBuyerAllCategoryTile 反参:[{}]", JSON.toJSONString(dataResponse));
            if (null != dataResponse && dataResponse.getSuccess()) {
                return dataResponse.getData();
            }
        } catch (Exception e) {
            log.error("RpcCategoryServiceImpl.queryCategoryBuyerAllCategoryTile 入参:[{}]", JSON.toJSONString(input),e);
        }
        return Collections.emptyList();
    }

    @Override
    public Map<Long, Map<String, String>> getCategoryById(CategoryReadDTO req){
        DataResponse<Map<Long, Map<String, String>>> res = null;
        try {
            res = iscProductSoaCategoryApiService.getCategoryById(req);
            return res.getData();
        }finally {
            log.info("RpcCategoryServiceImpl.get req:{} , res:{}" ,  JSON.toJSONString(req), JSON.toJSONString(res));
        }
    }

}
