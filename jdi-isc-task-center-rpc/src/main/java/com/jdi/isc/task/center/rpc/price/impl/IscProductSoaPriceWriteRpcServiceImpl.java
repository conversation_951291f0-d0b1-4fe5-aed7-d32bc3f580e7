package com.jdi.isc.task.center.rpc.price.impl;

import com.alibaba.fastjson.JSON;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.price.api.price.IscProductSoaPriceWriteApiService;
import com.jdi.isc.product.soa.price.api.price.req.PriceRefreshVO;
import com.jdi.isc.product.soa.price.api.salePrice.IscProductSoaSalePriceWriteApiService;
import com.jdi.isc.task.center.rpc.price.IscProductSoaPriceWriteRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 价格写服务rpc服务
 * <AUTHOR>
 * @date 2025/3/5
 */
@Service
@Slf4j
public class IscProductSoaPriceWriteRpcServiceImpl implements IscProductSoaPriceWriteRpcService {

    @Resource
    private IscProductSoaPriceWriteApiService iscProductSoaPriceWriteApiService;
    @Resource
    private IscProductSoaSalePriceWriteApiService iscProductSoaSalePriceWriteApiService;

    /** 触发跨境国家成本价重算*/
    @ToolKit
    @Override
    public Boolean refreshCnCostPrice(List<PriceRefreshVO> input) {
        DataResponse<Boolean> res = null;
        try {
            res = iscProductSoaPriceWriteApiService.refreshCnCostPrice(input);
            if(res.getSuccess() && res.getData()!=null){
                return res.getData();
            }
        }finally {
            log.info("IscProductSoaPriceWriteRpcServiceImpl.refreshCnCostPrice req:{} , res:{}" , JSON.toJSONString(input), JSON.toJSONString(res));
        }
        throw new RuntimeException("触发跨境国家成本价重算异常");
    }

    /** 触发跨境入仓成本价重算*/
    @ToolKit
    @Override
    public Boolean refreshCnWarehouseCostPrice(List<PriceRefreshVO> input) {
        DataResponse<Boolean> res = null;
        try {
            res = iscProductSoaPriceWriteApiService.refreshCnWarehouseCostPrice(input);
            if(res.getSuccess() && res.getData()!=null){
                return res.getData();
            }
        }finally {
            log.info("IscProductSoaPriceWriteRpcServiceImpl.refreshCnWarehouseCostPrice req:{} , res:{}" , JSON.toJSONString(input), JSON.toJSONString(res));
        }
        throw new RuntimeException("触发跨境入仓成本价重算异常");
    }

}
