package com.jdi.isc.aggregate.read.service.manage.mku;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.aggregate.read.domain.delivery.biz.SkuDeliveryAgingVO;
import com.jdi.isc.aggregate.read.domain.mku.biz.*;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.MkuClientListInfoReqApiDTO;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.MkuClientListInfoResApiDTO;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.OrderMkuInfoDetailResApiDTO;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.OrderMkuInfoReqApiDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 客户端服务接口
 * @Author: zhaojianguo21
 * @Date: 2023/12/15 09:29
 **/
public interface MkuClientManageService {

    /**
     * 查询订单业务需要的商品信息
     * @param input 参数
     * @return mku信息
     */
    List<OrderMkuInfoDetailResApiDTO> queryMkuInfoByIds(OrderMkuInfoReqApiDTO input);

//    /**
//     * 根据mkuIds查商品基本信息
//     * @param input 列表参数
//     * @return 商品信息
//     */
//    MkuClientListInfoResApiDTO listSimpleInfo(MkuClientListInfoReqApiDTO input);

//    /**
//     * 商品列表
//     * @param input 列表参数
//     * @return 商品列表
//     */
//    PageInfo<MkuClientVO> page(MkuClientPageReqVO input);
//
    /**
     * 是否存在mku
     * @param input 请求参数
     * @return true/false
     */
    Boolean existsMku(MkuClientDetailReqVO input);

    /**
     * 是否存在有效mku
     * @param input 请求参数
     * @return true/false
     */
    Boolean existsValidMku(MkuClientDetailReqVO input);

//    /**
//     * mku信息
//     * @param input 请求参数
//     * @return mku描述
//     */
//    MkuClientVO baseInfo(MkuClientDetailReqVO input);
//
//    /**
//     * mku归堆信息
//     * @param input 请求参数
//     * @return 同组聚堆mku
//     */
//    MkuClientGroupVO groupInfo(MkuClientDetailReqVO input);
//
//    /**
//     * 商品列表页单个商品卡片信息
//     * @param input 请求参数
//     * @return 商品卡片信息
//     */
//    MkuClientCardInfoResVO cardInfo(MkuClientCardInfoReqVO input);
//
//    /**
//     * 获取在池mku
//     */
//    Set<Long> queryPoolMkuIds(MkuClientListInfoReqApiDTO input);


    /**
     * mku转jdSku
     */
    Map<Long,MkuIdRefVO> getMkuRef(String clientCode, Set<Long> mkuIds);


//    /**
//     * mku转jdSku
//     */
//    Map<Long,Long> mku2JdSku(String clientCode, Set<Long> mkuIds);

    /**
     * 根据指定的MKU IDs、客户代码和库存状态类型获取相应的交付信息。
     * @param mkuIds MKU的ID列表
     * @param clientCode 客户代码
     * @param stockStateTypeMap 库存状态类型
     * @return 一个Map对象，其中键为MKU的ID，值为对应的SkuDeliveryAgingVO对象
     */
    Map<Long, SkuDeliveryAgingVO> getDeliveryInfo(List<Long> mkuIds,  String clientCode, Map<Long/*mkuId*/, Integer/*stockStateType*/> stockStateTypeMap);
}
