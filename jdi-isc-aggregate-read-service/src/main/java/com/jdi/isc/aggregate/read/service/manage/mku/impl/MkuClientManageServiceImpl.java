package com.jdi.isc.aggregate.read.service.manage.mku.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Sets;
import com.jd.jp.strategy.sdk.dto.SkuDetailedPageInfoResp;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.tp.common.masterdata.UniformBizInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.aggregate.read.common.config.CommonDUCCConfig;
import com.jdi.isc.aggregate.read.domain.attribute.biz.AttributeKeyAndValueVO;
import com.jdi.isc.aggregate.read.domain.brand.biz.BrandClientVO;
import com.jdi.isc.aggregate.read.domain.brand.po.BrandPO;
import com.jdi.isc.aggregate.read.domain.common.biz.DeliveryTemplate;
import com.jdi.isc.aggregate.read.domain.common.biz.PromiseInfo;
import com.jdi.isc.aggregate.read.domain.customer.biz.CustomerVO;
import com.jdi.isc.aggregate.read.domain.customerMku.CustomerMkuPricePO;
import com.jdi.isc.aggregate.read.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.aggregate.read.domain.delivery.biz.SkuDeliveryAgingVO;
import com.jdi.isc.aggregate.read.domain.enums.FulfillmentTypeEnum;
import com.jdi.isc.aggregate.read.domain.enums.PurchaseModelEnum;
import com.jdi.isc.aggregate.read.domain.enums.ShippingTypeEnum;
import com.jdi.isc.aggregate.read.domain.enums.YnEnum;
import com.jdi.isc.aggregate.read.domain.mku.biz.MkuClientCardInfoReqVO;
import com.jdi.isc.aggregate.read.domain.mku.biz.MkuClientCardInfoResVO;
import com.jdi.isc.aggregate.read.domain.mku.biz.MkuClientDetailReqVO;
import com.jdi.isc.aggregate.read.domain.mku.biz.MkuIdRefVO;
import com.jdi.isc.aggregate.read.domain.enums.*;
import com.jdi.isc.aggregate.read.domain.mku.biz.*;
import com.jdi.isc.aggregate.read.domain.mku.po.MkuDescLangPO;
import com.jdi.isc.aggregate.read.domain.mku.po.MkuLangPO;
import com.jdi.isc.aggregate.read.domain.mku.po.MkuPO;
import com.jdi.isc.aggregate.read.domain.price.biz.MkuClientPriceReqVO;
import com.jdi.isc.aggregate.read.domain.price.biz.MkuPriceReqVO;
import com.jdi.isc.aggregate.read.domain.price.biz.MkuPriceResVO;
import com.jdi.isc.aggregate.read.domain.promise.biz.JdSkuDetailPromiseReqVO;
import com.jdi.isc.aggregate.read.domain.promise.biz.PromiseInfoAsyncReq;
import com.jdi.isc.aggregate.read.domain.promise.biz.PromiseInfoAsyncRes;
import com.jdi.isc.aggregate.read.domain.sku.biz.SkuFeatureVO;
import com.jdi.isc.aggregate.read.domain.sku.po.SkuPO;
import com.jdi.isc.aggregate.read.domain.spu.po.SpuPO;
import com.jdi.isc.aggregate.read.repository.jed.mapper.customerMku.CustomerMkuBaseMapper;
import com.jdi.isc.aggregate.read.rpc.delivery.DeliveryTimeRpcService;
import com.jdi.isc.aggregate.read.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.aggregate.read.rpc.lang.LangRpcService;
import com.jdi.isc.aggregate.read.rpc.mku.MkuRpcService;
import com.jdi.isc.aggregate.read.service.ExtendInfoService;
import com.jdi.isc.aggregate.read.service.adapter.mapstruct.brand.BrandConvert;
import com.jdi.isc.aggregate.read.service.adapter.mapstruct.mku.MkuConvert;
import com.jdi.isc.aggregate.read.service.atomic.attribute.AttributeAtomicService;
import com.jdi.isc.aggregate.read.service.atomic.brand.BrandAtomicService;
import com.jdi.isc.aggregate.read.service.atomic.customer.CustomerAtomicService;
import com.jdi.isc.aggregate.read.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.aggregate.read.service.atomic.mku.MkuAtomicService;
import com.jdi.isc.aggregate.read.service.atomic.mku.MkuDescLangAtomicService;
import com.jdi.isc.aggregate.read.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.aggregate.read.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.aggregate.read.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.aggregate.read.service.manage.brand.BrandOutService;
import com.jdi.isc.aggregate.read.service.manage.category.CategoryCustomService;
import com.jdi.isc.aggregate.read.service.manage.category.CategoryOutService;
import com.jdi.isc.aggregate.read.service.manage.common.CommonService;
import com.jdi.isc.aggregate.read.service.manage.customer.CustomerManageService;
import com.jdi.isc.aggregate.read.service.manage.customerMku.CustomerMkuPriceManageService;
import com.jdi.isc.aggregate.read.service.manage.mku.MkuClientManageService;
import com.jdi.isc.aggregate.read.service.manage.price.ClientPriceManageService;
import com.jdi.isc.aggregate.read.service.manage.price.MkuPriceManageService;
import com.jdi.isc.aggregate.read.service.manage.product.AttributeManagerService;
import com.jdi.isc.aggregate.read.service.manage.promise.PromiseManageService;
import com.jdi.isc.aggregate.read.service.manage.sku.SkuFeatureManageService;
import com.jdi.isc.aggregate.read.service.manage.stock.MkuStockService;
import com.jdi.isc.aggregate.read.wisp.api.common.CustomerMkuBindEnum;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.MkuAttributeValueDTO;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.MkuClientListInfoReqApiDTO;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.OrderMkuInfoDetailResApiDTO;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.OrderMkuInfoReqApiDTO;
import com.jdi.isc.fulfillment.soa.api.time.biz.GoodsFulfillmentTimeApiDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.MkuQueryEnum;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuReqDTO;
import com.jdi.isc.product.soa.api.mku.res.IscMkuResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * @Description: 客户端服务类
 * @Author: zhaojianguo21
 * @Date: 2023/12/15 09:30
 **/
@Slf4j
@Service
public class MkuClientManageServiceImpl implements MkuClientManageService {

    @Resource
    private MkuAtomicService mkuAtomicService;
    @Resource
    private MkuLangAtomicService mkuLangAtomicService;
    @Resource
    private MkuDescLangAtomicService mkuDescLangAtomicService;
    @Resource
    private CategoryOutService categoryOutService;
    @Resource
    private CategoryCustomService categoryCustomService;
    @Resource
    private BrandOutService brandOutService;
    @Resource
    private BrandAtomicService brandAtomicService;
    @Resource
    private AttributeManagerService attributeManagerService;
    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;
    @Resource
    private CustomerMkuPriceManageService customerMkuPriceManageService;
    @Resource
    private CustomerMkuBaseMapper customerMkuBaseMapper;

    @Resource
    private CommonDUCCConfig commonDUCCConfig;

    @Resource
    private CustomerAtomicService customerAtomicService;
    @Resource
    private CommonService commonService;

    @Autowired
    private MkuPriceManageService mkuPriceManageService;
    @Resource
    private ClientPriceManageService clientPriceManageService;

    @Autowired
    private SkuAtomicService skuAtomicService;
    @Resource
    private SkuInfoRpcService skuInfoRpcService;
    @Resource
    private CustomerManageService customerManageService;
//    @Resource
//    private PromiseRpcService promiseRpcService;
    @Resource
    private MkuStockService mkuStockService;
    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    private ExtendInfoService extendInfoService;
    @Resource
    private LangRpcService langRpcService;
    @Resource
    private AttributeAtomicService attributeAtomicService;
    @Resource
    private DeliveryTimeRpcService deliveryTimeRpcService;
    @Resource
    private SkuFeatureManageService skuFeatureManageService;
    @Resource
    private PromiseManageService promiseManageService;

    @Autowired
    private ThreadPoolExecutor promiseDeliveryThreadPool;
    @Resource
    private MkuRpcService mkuRpcService;


//    private final static Integer DEFAULT_REQ_SOURCE = 17;
    @LafValue("jdi.isc.agg.delivery.text")
    private String deliveryText;

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public List<OrderMkuInfoDetailResApiDTO> queryMkuInfoByIds(OrderMkuInfoReqApiDTO input) {
        log.info("queryMkuInfoByIds, input={}", JSONObject.toJSONString(input));
        if (CollectionUtils.isEmpty(input.getMkuIds())) {
            log.info("queryMkuInfoByIds, param error. input={}", JSONObject.toJSONString(input));
            return null;
        }
        Set<Long> mkuIds = input.getMkuIds();
        String clientCode = input.getClientCode();

        CustomerVO customerVO = customerAtomicService.get(clientCode);
        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
            log.warn("queryMkuInfoByIds, customerVO error. clientCode={}, customerVO={}", clientCode, JSONObject.toJSONString(customerVO));
            return null;
        }

        if (commonService.stationForbidden(customerVO.getCountry(), input.getStationType())) {
            log.warn("queryMkuInfoByIds, stationForbidden. country={}, input={}", customerVO.getCountry(), JSONObject.toJSONString(input));
            return null;
        }

        // 查询客户绑定商品关系
        List<Long> mkuIdList = this.getCustomerMkuIdList(Sets.newHashSet(mkuIds), clientCode);
        if (CollectionUtils.isEmpty(mkuIdList)) {
            log.info("queryMkuInfoByIds, mkuIdList empty.");
            return null;
        }

        LambdaQueryWrapper<MkuPO> queryWrapper = Wrappers.lambdaQuery(MkuPO.class)
                .in(MkuPO::getMkuId, input.getMkuIds())
                .eq(MkuPO::getYn, YnEnum.YES.getCode());
        List<MkuPO> mkuPoList = mkuAtomicService.getBaseMapper().selectList(queryWrapper);

        List<OrderMkuInfoDetailResApiDTO> mkuInfoDetailResApiDTOList = MkuConvert.INSTANCE.wispListMkuPo2MkuInfoReadResp(mkuPoList);

        // 查询价格
        MkuClientPriceReqVO priceReqVO = new MkuClientPriceReqVO();
        priceReqVO.setClientCode(input.getClientCode());
        priceReqVO.setMkuIdList(mkuIds);
        DataResponse<List<MkuPriceResVO>> priceResponse = clientPriceManageService.listMkuPriceNotLimitSize(priceReqVO);
        Map<Long, MkuPriceResVO> priceMap = null;
        if (priceResponse.getSuccess() && CollectionUtils.isNotEmpty(priceResponse.getData())) {
            priceMap = priceResponse.getData().stream()
                    .filter(Objects::nonNull).collect(Collectors.toMap(MkuPriceResVO::getMkuId, o -> o));
        }

        //查询销售单位
        Map<Long, String> saleUnitMap = queryMkuSaleUnit(mkuIds, input.getLang());

        for (OrderMkuInfoDetailResApiDTO detailResApiDTO : mkuInfoDetailResApiDTOList) {
            // 设置价格
            if (null != priceMap && null != priceMap.get(detailResApiDTO.getMkuId())) {
                MkuPriceResVO mkuPriceReadResp = priceMap.get(detailResApiDTO.getMkuId());
                detailResApiDTO.setSalePrice(mkuPriceReadResp.getSalePrice());
                detailResApiDTO.setCurrency(mkuPriceReadResp.getCurrency());
                detailResApiDTO.setFixedSkuId(mkuPriceReadResp.getSkuId());
                detailResApiDTO.setIncludeTaxPrice(mkuPriceReadResp.getIncludeTaxPrice());
                detailResApiDTO.setValueAddedTax(mkuPriceReadResp.getTaxPrice());
                detailResApiDTO.setValueAddedTaxRate(mkuPriceReadResp.getValueAddedTax());
            }
        }

        if (null != commonDUCCConfig && Objects.equals(Boolean.TRUE, commonDUCCConfig.getLogDebugSwitch())) {
            log.info("queryMkuInfoByIds, input={}, mkuPoList={}, mkuInfoDetailResApiDTOList={}"
                    , JSONObject.toJSONString(input), JSONObject.toJSONString(mkuPoList), JSONObject.toJSONString(mkuInfoDetailResApiDTOList));
        }
        return mkuInfoDetailResApiDTOList;
    }

    private Map<Long, String> queryMkuSaleUnit(Set<Long> mkuIds, String lang) {
        Map<Long, String> saleUnitMap = new HashMap<>();
        try {
            LambdaQueryWrapper<CustomerMkuPO> queryWrapper = Wrappers.lambdaQuery(CustomerMkuPO.class).in(CustomerMkuPO::getMkuId, mkuIds).eq(CustomerMkuPO::getYn, YnEnum.YES.getCode());
            List<CustomerMkuPO> spuList = customerMkuAtomicService.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(spuList)) {
                Map<Long, Long> spuMkuMap = spuList.stream().collect(HashMap::new, (m, v) -> m.put(v.getSpuId(), v.getMkuId()), HashMap::putAll);
                Map<Long, SpuPO> spuMap = spuAtomicService.querySpuMapBySpuIds(spuMkuMap.keySet());
                spuMkuMap.forEach((k, v) -> {
                    SpuPO spuPO = spuMap.get(k);
                    if (null != spuPO && Objects.nonNull(spuPO.getSaleUnit())) {
                        String saleUnit = extendInfoService.saleUnit(spuPO.getSaleUnit(), lang);
                        saleUnitMap.put(v, saleUnit);
                    }
                });
            }
        } catch (Exception e) {
            log.error("queryMkuSaleUnit, param={},lang={},e:", JSONObject.toJSONString(mkuIds), lang, e);
        }
        return saleUnitMap;
    }

//    @Override
//    public MkuClientListInfoResApiDTO listSimpleInfo(MkuClientListInfoReqApiDTO input) {
//        log.info("listSimpleInfo params :{}",JSONObject.toJSONString(input));
//        Set<Long> mkuIds = input.getMkuIds();
//        if (null == input.getClientCode() || CollectionUtils.isEmpty(mkuIds)) {
//            log.warn("listSimpleInfo, param error. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//        String clientCode = input.getClientCode();
//
//        CustomerVO customerVO = customerAtomicService.get(clientCode);
//        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
//            log.warn("listSimpleInfo, customerVO error. clientCode={}, customerVO={}", clientCode, JSONObject.toJSONString(customerVO));
//            return null;
//        }
//
//        if (commonService.stationForbidden(customerVO.getCountry(), input.getStationType())) {
//            log.warn("listSimpleInfo, stationForbidden. country={}, input={}", customerVO.getCountry(), JSONObject.toJSONString(input));
//            return null;
//        }
//        List<MkuPO> mkuPOList = mkuAtomicService.listByMkuIds(Lists.newArrayList(mkuIds));
//        List<MkuClientSimpleApiDTO> simpleApiDTOList = MkuConvert.INSTANCE.mkuPoList2mkuClientSimpleList(mkuPOList);
//        //查询库里所有的 销售属性 对应字典集合
//        try {
//            List<AttributeKeyAndValueVO> allAttributeKeyAndValue = extractSaleAttributes(mkuPOList);
//            if(null !=allAttributeKeyAndValue&& !allAttributeKeyAndValue.isEmpty()){
//                List<AttributeKeyAndValueDTO> attributeKeyAndValueDTOS = MkuConvert.INSTANCE.attributeKeyAndValue(allAttributeKeyAndValue);
//                assembleAttributeKeyAndValue(attributeKeyAndValueDTOS,simpleApiDTOList);
//                log.warn("attributeKeyAndValueDTOS={}",  JSONObject.toJSONString(simpleApiDTOList));
//            }
//        }catch (Exception e){
//            log.error("写入销售属性异常:", e);
//        }
//
//        // 查名称多语言
//        MkuOperateVO mkuLang = new MkuOperateVO(mkuIds, input.getLang());
//        Map<Long, MkuLangPO> mkuLangMap = mkuLangAtomicService.listLang(mkuLang);
//
//        // 查询价格
//        MkuClientPriceReqVO priceReqVO = new MkuClientPriceReqVO();
//        priceReqVO.setClientCode(input.getClientCode());
//        priceReqVO.setMkuIdList(mkuIds);
//        DataResponse<List<MkuPriceResVO>> priceResponse = clientPriceManageService.listMkuPriceNotLimitSize(priceReqVO);
//        Map<Long, MkuPriceResVO> priceMap = new HashMap<>();
//        if (priceResponse.getSuccess() && CollectionUtils.isNotEmpty(priceResponse.getData())) {
//            priceResponse.getData().stream()
//                    .filter(Objects::nonNull).forEach(mkuPriceReadResp -> {
//                        priceMap.put(mkuPriceReadResp.getMkuId(), mkuPriceReadResp);
//                    });
//        }
//        // 固定sku信息
//        Map<Long, MkuIdRefVO> mkuMap = getMkuRef(clientCode, mkuIds);
//        Map<Long, Integer> mkuMoq = buildMoqMap(mkuMap);
//        //查询销售单位
//        Map<Long, String> saleUnitMap = queryMkuSaleUnit(mkuIds, input.getLang());
//        for (MkuClientSimpleApiDTO simpleApiDTO : simpleApiDTOList) {
//            // 是否在池
//            boolean inPool = false;
//            // 设置价格
//            if (MapUtils.isNotEmpty(priceMap) && null != priceMap.get(simpleApiDTO.getMkuId())) {
//                inPool = true;
//                MkuPriceResVO mkuPriceReadResp = priceMap.get(simpleApiDTO.getMkuId());
//                simpleApiDTO.setSalePrice(mkuPriceReadResp.getSalePrice());
//                simpleApiDTO.setCurrency(mkuPriceReadResp.getCurrency());
//                // 显示币种和价格
//                ProductPricesDTO productPricesDTO = new ProductPricesDTO();
//                productPricesDTO.setCurrency(mkuPriceReadResp.getCurrency());
//                productPricesDTO.setCurrencySource(mkuPriceReadResp.getOriginCurrency());
//                productPricesDTO.setExchangeRate(mkuPriceReadResp.getExchangeRate());
//                productPricesDTO.setSalePrice(mkuPriceReadResp.getSalePrice());
//                productPricesDTO.setIncludeTaxPrice(mkuPriceReadResp.getIncludeTaxPrice());
//                productPricesDTO.setValueAddedTax(mkuPriceReadResp.getTaxPrice());
//                productPricesDTO.setValueAddedTaxRate(mkuPriceReadResp.getValueAddedTax());
//                simpleApiDTO.setShowCurrency(productPricesDTO);
//
//                // 多币种价格
//                Map<String, ProductPricesDTO> pricesDTOMap = Maps.newHashMap();
//                pricesDTOMap.put(mkuPriceReadResp.getCurrency(), productPricesDTO);
//                simpleApiDTO.setCurrenciesPrices(pricesDTOMap);
//                simpleApiDTO.setMoq(mkuMoq.get(simpleApiDTO.getMkuId()));
//            }
//
//            simpleApiDTO.setInPool(inPool);
//            // 设置商品名称
//            MkuLangPO langPO = mkuLangMap.get(simpleApiDTO.getMkuId());
//            simpleApiDTO.setName(null != langPO ? langPO.getMkuTitle() : "");
//            // 设置销售单位
//            if (saleUnitMap.containsKey(simpleApiDTO.getMkuId())) {
//                String saleUnit = saleUnitMap.get(simpleApiDTO.getMkuId());
//                simpleApiDTO.setSaleUnit(saleUnit);
//            }
//        }
//
//        MkuClientListInfoResApiDTO listInfoResApiDTO = new MkuClientListInfoResApiDTO();
//        listInfoResApiDTO.setMkuInfoList(simpleApiDTOList);
//        if (null != commonDUCCConfig && Objects.equals(Boolean.TRUE, commonDUCCConfig.getLogDebugSwitch())) {
//            log.info("listSimpleInfo. result={}, input={}", JSONObject.toJSONString(listInfoResApiDTO), JSONObject.toJSONString(input));
//        }
//        return listInfoResApiDTO;
//    }
//
//    private void assembleAttributeKeyAndValue(List<AttributeKeyAndValueDTO> attributeKeyAndValueDTOS, List<MkuClientSimpleApiDTO> simpleApiDTOList) {
//        log.info("attributeKeyAndValueDTOS:{},simpleApiDTOList:{}", JSONObject.toJSONString(attributeKeyAndValueDTOS), JSONObject.toJSONString(simpleApiDTOList));
//        if (attributeKeyAndValueDTOS != null && !attributeKeyAndValueDTOS.isEmpty()) {
//            simpleApiDTOList.forEach(simple -> {
//                // 获取当前 DTO 的 saleAttribute 字段（格式化如 "100017:100414#100018:100417"）
//                String saleAttribute = simple.getSaleAttribute();
//                if (StringUtils.isNotBlank(saleAttribute)) {
//                    // 根据 saleAttribute 字段拆分出对应的 attributeId 和 attributeValueId
//                    List<AttributeKeyAndValueDTO> filtration = Arrays.stream(saleAttribute.split("#")) // 按 # 分割
//                            .map(pair -> pair.split(":")) // 按 : 分割
//                            .filter(values -> values.length == 2) // 确保是有效的键值对
//                            .flatMap(values -> {
//                                try {
//                                    Long attributeId = Long.valueOf(values[0]);
//                                    Long attributeValueId = Long.valueOf(values[1]);
//                                    // 在 attributeKeyAndValueDTOS 中找到所有匹配的值
//                                    return attributeKeyAndValueDTOS.stream()
//                                            .filter(dto -> dto.getAttrAttributeId().equals(attributeId) && dto.getValAttributeValueId().equals(attributeValueId));
//                                } catch (NumberFormatException e) {
//                                    log.error("Invalid attribute pair: " + Arrays.toString(values), e);
//                                    return Stream.empty();
//                                }
//                            })
//                            .collect(Collectors.toList());
//
//                    // 将找到的匹配属性设置到 simple 对象的 saleAttributes 中
//                    simple.setSaleAttributes(filtration);
//                }
//            });
//        }
//    }
//
//

//    @Override
//    public PageInfo<MkuClientVO> page(MkuClientPageReqVO input) {
//        PageInfo<MkuClientVO> pageInfo = new PageInfo<>();
//        pageInfo.setSize(input.getSize());
//        pageInfo.setIndex(input.getIndex());
//        pageInfo.setTotal(0);
//        if (null == input.getClientCode() || input.getSize() > Constant.CLIENT_MAX_PAGE_SIZE) {
//            log.warn("page, param error. input={}", JSONObject.toJSONString(input));
//            return pageInfo;
//        }
//        String clientCode = input.getClientCode();
//        CustomerVO customerVO = customerAtomicService.get(clientCode);
//        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
//            log.warn("page, customerVO error. clientCode={}, customerVO={}", clientCode, JSONObject.toJSONString(customerVO));
//            return pageInfo;
//        }
//        if (commonService.stationForbidden(customerVO.getCountry(), input.getStationType())) {
//            log.warn("page, stationForbidden. country={}, input={}", customerVO.getCountry(), JSONObject.toJSONString(input));
//            return pageInfo;
//        }
//        if (null != input.getCatId()) {
//            // 查询某个类目节点下的所有叶子节点ID
//            Set<Long> catIds = categoryCustomService.queryLeafId(input.getClientCode(), input.getStationType(), input.getCatId());
//            if (CollectionUtils.isEmpty(catIds)) {
//                log.info("page, catIds of level four empty. input={}", JSONObject.toJSONString(input));
//                return pageInfo;
//            }
//            if (null != commonDUCCConfig && Objects.equals(Boolean.TRUE, commonDUCCConfig.getLogDebugSwitch())) {
//                log.info("page, catIds={}", JSONObject.toJSONString(catIds));
//            }
//            input.setCatIds(new ArrayList<>(catIds));
//        }
//        if (null != commonDUCCConfig && Objects.equals(Boolean.TRUE, commonDUCCConfig.getLogDebugSwitch())) {
//            log.info("pageInfo. final input={}", JSONObject.toJSONString(input));
//        }
//        long total = customerMkuAtomicService.pageMkuTotal(input);
//        pageInfo.setTotal(total);
//        if (0 == total) {
//            log.info("page, total is zero. input={}", JSONObject.toJSONString(input));
//            return pageInfo;
//        }
//        //mku信息查询
//        List<CustomerMkuPO> pageList = customerMkuAtomicService.pageMku(input);
//        if (CollectionUtils.isEmpty(pageList)) {
//            log.info("page, List<CustomerMkuPO> empty. input={}", JSONObject.toJSONString(input));
//            return pageInfo;
//        }
//        Set<Long> mkuIds = pageList.stream().filter(o -> null != o && Objects.nonNull(o.getMkuId())).map(CustomerMkuPO::getMkuId).collect(Collectors.toSet());
//        List<MkuPO> mkuPOList = mkuAtomicService.listByMkuIds(Lists.newArrayList(mkuIds));
//        List<MkuClientVO> clientVOList = MkuConvert.INSTANCE.mkuPoList2mkuClientVoList(mkuPOList);
//        // 重新排序，与pageMku方法中的排序规则保持一致
//        clientVOList.sort(Comparator.comparing(MkuClientVO::getMkuId).reversed());
//        //mku&sku关系
//        Map<Long, MkuIdRefVO> mkuMap = getMkuRef(input.getClientCode(), mkuIds);
//        //名称多语言
//        Map<Long, MkuLangPO> mkuLangMap = mkuLangAtomicService.listLang(new MkuOperateVO(mkuIds, input.getLang()));
//        //价格
//        Map<Long, MkuPriceResVO> priceMap = buildPriceMap(input, mkuIds);
//        //最小起订量
//        Map<Long, Integer> mkuMoqMap = buildMoqMap(mkuMap);
//        //库存
//        Map<Long, StockResVO> stockMap = buildStockMap(input.getStationType(), input.getClientCode(), mkuIds, mkuMap, mkuMoqMap);
//        //货期 列表页面的货期页面上没展示，先屏蔽掉。若需要展示，buildPromiseInfo接口应该改造为批量获取，避免循环调用
////        PromiseInfo promiseInfo = buildPromiseInfo(input.getClientCode(), mkuMap);
//        //信息拼装
//        for (MkuClientVO clientVO : clientVOList) {
//            buildSkuId(clientVO, mkuMap);
//
//            // 设置价格
//            if (MapUtils.isNotEmpty(priceMap) && null != priceMap.get(clientVO.getMkuId())) {
//                MkuPriceResVO mkuPriceReadResp = priceMap.get(clientVO.getMkuId());
//                clientVO.setSalePrice(mkuPriceReadResp.getSalePrice());
//                clientVO.setCurrency(mkuPriceReadResp.getCurrency());
//
//                // 显示币种和价格
//                ProductPricesDTO productPricesDTO = new ProductPricesDTO();
//                productPricesDTO.setCurrency(mkuPriceReadResp.getCurrency());
//                productPricesDTO.setCurrencySource(mkuPriceReadResp.getOriginCurrency());
//                productPricesDTO.setExchangeRate(mkuPriceReadResp.getExchangeRate());
//                productPricesDTO.setSalePrice(mkuPriceReadResp.getSalePrice());
//                productPricesDTO.setIncludeTaxPrice(mkuPriceReadResp.getIncludeTaxPrice());
//                productPricesDTO.setValueAddedTax(mkuPriceReadResp.getTaxPrice());
//                productPricesDTO.setValueAddedTaxRate(mkuPriceReadResp.getValueAddedTax());
//                clientVO.setShowCurrency(productPricesDTO);
//
//                // 多币种价格
//                Map<String, ProductPricesDTO> pricesDTOMap = Maps.newHashMap();
//                pricesDTOMap.put(mkuPriceReadResp.getCurrency(), productPricesDTO);
//                clientVO.setCurrenciesPrices(pricesDTOMap);
//                //moq
//                clientVO.setMoq(mkuMoqMap.get(clientVO.getMkuId()));
//                if (MapUtils.isNotEmpty(stockMap)){
//                    // 库存
//                    buildStock(clientVO, stockMap.get(clientVO.getMkuId()) );
//                }
//
//                //货期
//                /*if (MapUtils.isNotEmpty(promiseInfo.getDeliveryDateMap())) {
//                    Integer deliveryDays = promiseInfo.getDeliveryDateMap().get(mkuMap.get(clientVO.getMkuId()).getPo().getJdSkuId());
//                    if (deliveryDays != null) {
//                        String langTemp;
//                        if (deliveryDays >= promiseInfo.getDeliveryTemplate().getThreshold()) {
//                            langTemp = DeliveryTemplate.getByLang(promiseInfo.getDeliveryTemplate().getFutureDelivery(), input.getLang());
//                            clientVO.setDeliveryDate(String.format(langTemp, deliveryDays));
//                        }
//                    }
//                }*/
//            }
//            // 设置商品名称
//            MkuLangPO langPO = mkuLangMap.get(clientVO.getMkuId());
//            clientVO.setMkuName(null != langPO ? langPO.getMkuTitle() : "");
//
//            // 设置品牌信息
//            BrandClientVO brandClientVO = new BrandClientVO();
//            brandClientVO.setId(clientVO.getBrandId());
//            clientVO.setBrand(brandClientVO);
//        }
//
//        pageInfo.setRecords(clientVOList);
//        if (null != commonDUCCConfig && Objects.equals(Boolean.TRUE, commonDUCCConfig.getLogDebugSwitch())) {
//            log.info("pageInfo. result={}, input={}", JSONObject.toJSONString(pageInfo), JSONObject.toJSONString(input));
//        }
//        return pageInfo;
//    }
//
//    /**
//     * 同步构建货期时效信息
//     */
//    private PromiseInfo buildPromiseInfo(String clientCode, Map<Long, MkuIdRefVO> mkuMap) {
//        PromiseInfo promiseInfoResult = new PromiseInfo();
//        Set<Long> jdSkuIds = mkuMap.values().stream().filter(line -> CountryConstant.COUNTRY_ZH.equals(line.getPo().getSourceCountryCode()))
//                .map(line -> line.getPo().getJdSkuId()).collect(Collectors.toSet());
//        if (CollectionUtils.isEmpty(jdSkuIds)) {
//            log.info("buildPromiseInfo, jdSkuIds empty. skip.");
//            return promiseInfoResult;
//        }
//        log.info("buildPromiseInfo, clientCode={}, jdSkuIds={}", clientCode, JSONObject.toJSONString(jdSkuIds));
//
//        Map<Long, Integer> deliveryDateMap = Maps.newHashMapWithExpectedSize(jdSkuIds.size());
//        for(Long jdSkuId : jdSkuIds){
//            JdSkuDetailPromiseReqVO reqVO = new JdSkuDetailPromiseReqVO(jdSkuId, 1, clientCode);
//            SkuDetailedPageInfoResp promiseInfo = promiseManageService.getPromiseInfoByJdSkuId(reqVO);
//            if (Objects.nonNull(promiseInfo) && Objects.nonNull(promiseInfo.getDeliveryDays())){
//                deliveryDateMap.put(jdSkuId, promiseInfo.getDeliveryDays());
//            }
//        }
//
//        DeliveryTemplate deliveryTemplate = JSONObject.parseObject(deliveryText, DeliveryTemplate.class);
//        promiseInfoResult.setDeliveryTemplate(deliveryTemplate);
//        promiseInfoResult.setDeliveryDateMap(deliveryDateMap);
//        log.info("buildPromiseInfo, promiseInfoResult={}", JSONObject.toJSONString(promiseInfoResult));
//        return promiseInfoResult;
//    }

    /**
     * 构建货期时效信息
     */
    private PromiseInfo buildPromiseInfoByFuture(String clientCode, Map<Long, MkuIdRefVO> mkuMap) {
        PromiseInfo promiseInfoResult = new PromiseInfo();
        Set<Long> jdSkuIds = mkuMap.values().stream().filter(line -> CountryConstant.COUNTRY_ZH.equals(line.getPo().getSourceCountryCode()))
                .filter(o->null!=o.getPo().getJdSkuId())
                .map(line -> line.getPo().getJdSkuId()).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(jdSkuIds)) {
            log.info("buildPromiseInfoByFuture, jdSkuIds empty. skip.");
            return promiseInfoResult;
        }
        log.info("buildPromiseInfoByFuture, clientCode={}, jdSkuIds={}", clientCode, JSONObject.toJSONString(jdSkuIds));

        // 组装并行future
        List<PromiseInfoAsyncReq> promiseInfoAsyncFutureList = jdSkuIds.stream().map(jdSkuId -> {
            JdSkuDetailPromiseReqVO reqVO = new JdSkuDetailPromiseReqVO(jdSkuId, 1, clientCode);
            return reqVO;
        }).filter(Objects::nonNull).map(reqVO -> {
            PromiseInfoAsyncReq promiseInfoAsync = new PromiseInfoAsyncReq();
            promiseInfoAsync.setJdSkuId(reqVO.getJdSkuId());
            promiseInfoAsync.setFuture( promiseDeliveryThreadPool.submit(() -> promiseManageService.getPromiseInfoByJdSkuId(reqVO)) );
            return promiseInfoAsync;
        })
        .collect(Collectors.toList());

        // 从future中获取结果
        Map<Long, Integer> deliveryDateMap = promiseInfoAsyncFutureList.stream().map(promiseInfoAsyncFutureReq -> {
            try {
                SkuDetailedPageInfoResp skuDetailedPageInfoResp = (SkuDetailedPageInfoResp)promiseInfoAsyncFutureReq.getFuture().get();

                PromiseInfoAsyncRes promiseInfoAsyncRes = new PromiseInfoAsyncRes();
                promiseInfoAsyncRes.setJdSkuId(promiseInfoAsyncFutureReq.getJdSkuId());
                promiseInfoAsyncRes.setFutureResult(skuDetailedPageInfoResp);
                log.info("buildPromiseInfoByFuture, getFuture result! jdSkuId={}, deliveryDays={}"
                        , promiseInfoAsyncRes.getJdSkuId(), null!=skuDetailedPageInfoResp?skuDetailedPageInfoResp.getDeliveryDays():"-");
                return promiseInfoAsyncRes;
            } catch (Exception e) {
                log.error("buildPromiseInfoByFuture, getFuture exception! jdSkuId={}", promiseInfoAsyncFutureReq.getJdSkuId(), e);
                return null;
            }
        }).filter(resp -> resp != null && resp.getFutureResult() != null && null!=((SkuDetailedPageInfoResp)resp.getFutureResult()).getDeliveryDays() )
                .collect(Collectors.toMap(o->o.getJdSkuId(), o->((SkuDetailedPageInfoResp)o.getFutureResult()).getDeliveryDays()));

        DeliveryTemplate deliveryTemplate = JSONObject.parseObject(deliveryText, DeliveryTemplate.class);
        promiseInfoResult.setDeliveryTemplate(deliveryTemplate);
        promiseInfoResult.setDeliveryDateMap(deliveryDateMap);

        log.info("buildPromiseInfoByFuture, promiseInfoResult={}", JSONObject.toJSONString(promiseInfoResult));
        return promiseInfoResult;
    }

//    /**
//     * 构建库存map
//     */
//    private Map<Long, StockResVO> buildStockMap(Integer stationType, String clientCode, Set<Long> mkuIds, Map<Long, MkuIdRefVO> mkuMap, Map<Long, Integer> mkuMoqMap) {
//        StockReqVO stockReq = new StockReqVO();
//        stockReq.setStationType(stationType);
//        stockReq.setClientCode(clientCode);
//        stockReq.setStockItem(mkuIds.stream().map(mku -> new StockItemReqVO(mku, mkuMoqMap.getOrDefault(mku, 1))).collect(Collectors.toList()));
//        return mkuStockService.getStock(stockReq, mkuMap);
//    }
//
//    /**
//     * 构建价格map
//     */
//    private Map<Long, MkuPriceResVO> buildPriceMap(MkuClientPageReqVO input, Set<Long> mkuIds) {
//        Map<Long, MkuPriceResVO> priceMap = new HashMap<>();
//        MkuClientPriceReqVO priceReqVO = new MkuClientPriceReqVO();
//        priceReqVO.setClientCode(input.getClientCode());
//        priceReqVO.setMkuIdList(mkuIds);
//        DataResponse<List<MkuPriceResVO>> priceResponse = clientPriceManageService.listMkuPriceNotLimitSize(priceReqVO);
//        if (priceResponse.getSuccess() && CollectionUtils.isNotEmpty(priceResponse.getData())) {
//            priceResponse.getData().stream()
//                    .filter(Objects::nonNull).forEach(mkuPriceReadResp -> priceMap.put(mkuPriceReadResp.getMkuId(), mkuPriceReadResp));
//        }
//        return priceMap;
//    }
//
//    /**
//     * 根据给定的 SKU IDs 和国家代码查询本地产品的交货天数。
//     * @param skuIds SKU IDs 集合
//     * @param country 国家代码
//     * @return SKU ID 到交货天数的映射关系
//     */
//    private Map<Long, BigDecimal> queryLocalProductDeliveryGoodsDays(Set<Long> skuIds, String country) {
//        Map<Long, BigDecimal> result = Maps.newHashMapWithExpectedSize(skuIds.size());
//        if (CollectionUtils.isEmpty(skuIds)) {
//            return result;
//        }
//
//        Map<Long, SkuFeatureVO> res = skuFeatureManageService.querySkuFeatureMap(skuIds, country);
//        if (MapUtils.isNotEmpty(res)) {
//            for (Map.Entry<Long, SkuFeatureVO> entry : res.entrySet()) {
//                result.put(entry.getKey(), entry.getValue().getProductionCycle());
//            }
//        }
//        return result;
//    }
//
//    private Map<Long, Integer> buildMoqMap(Map<Long, MkuIdRefVO> req) {
//        Map<Long, Integer> result = new HashMap<>();
//        try {
//            //跨境sku  <jdSkuId,mkuId关系>
//            Map<Long, Long> crossMap = new HashMap<>();
//            for (Map.Entry<Long, MkuIdRefVO> entry : req.entrySet()) {
//                // 备货模式默认返回1
//                if (Objects.nonNull(entry.getValue().getFeaturePO()) && Objects.nonNull(entry.getValue().getFeaturePO().getPurchaseModel())
//                        && Constant.ONE ==  entry.getValue().getFeaturePO().getPurchaseModel()){
//                    result.put(entry.getKey(), 1);
//                }else {
//                    if (CountryConstant.COUNTRY_ZH.equals(entry.getValue().getPo().getSourceCountryCode())) {
//                        crossMap.put(entry.getValue().getPo().getJdSkuId(), entry.getKey());
//                    } else {
//                        //处理本土moq
//                        result.put(entry.getKey(), entry.getValue().getPo().getMoq() != null ? entry.getValue().getPo().getMoq() : 1);
//                    }
//                }
//            }
//            //处理跨境moq
//            if (MapUtils.isNotEmpty(crossMap)) {
//                Map<Long, JdProductDTO> gmsSkuMap = skuInfoRpcService.querySkuMap(new JdProductQueryDTO(new ArrayList<>(crossMap.keySet())));
//                for (Map.Entry<Long, JdProductDTO> entry : gmsSkuMap.entrySet()) {
//                    Long mkuId = crossMap.get(entry.getKey());
//                    String lb = entry.getValue().getLowestBuy();
//                    if (StringUtils.isBlank(lb)) {
//                        result.put(mkuId, 1);
//                    } else {
//                        result.put(mkuId, Integer.valueOf(lb));
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("MkuClientManageServiceImpl.buildMoq error, target:{} ,errMsg:{}", JSON.toJSONString(req), e);
//            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_COMMON_WARNING, String.format("%s moq构建异常", LevelCode.P1.getMessage()));
//        }
//        log.info("MkuClientManageServiceImpl.buildMoq req:{} , res:{}", JSON.toJSONString(req), JSON.toJSONString(result));
//        return result;
//    }
//
    @Override
    @ToolKit
    public Boolean existsMku(MkuClientDetailReqVO input) {
        String clientCode = input.getClientCode();
        CustomerVO customerVO = customerAtomicService.get(clientCode);
        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
            log.info("existsMku, not exists. customerVO error. clientCode={}, customerVO={}", clientCode, JSONObject.toJSONString(customerVO));
            return Boolean.FALSE;
        }

        if (commonService.stationForbidden(customerVO.getCountry(), input.getStationType())) {
            log.info("existsMku, not exists. stationForbidden. input={}", JSONObject.toJSONString(input));
            return Boolean.FALSE;
        }

        Long mkuId = input.getMkuId();
        CustomerMkuPO customerMkuPO = customerMkuAtomicService.getOneValid(mkuId, input.getClientCode());
        if (null == customerMkuPO) {
            log.info("existsMku, not exists. no match CustomerMkuPO. input={}", JSONObject.toJSONString(input));
            return Boolean.FALSE;
        }
        log.info("existsMku, exists. input={}", JSONObject.toJSONString(input));
        return Boolean.TRUE;
    }

    @Override
    @ToolKit
    public Boolean existsValidMku(MkuClientDetailReqVO input) {
        String clientCode = input.getClientCode();
        CustomerVO customerVO = customerAtomicService.get(clientCode);
        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
            log.info("existsMku, not exists. customerVO error. clientCode={}, customerVO={}", clientCode, JSONObject.toJSONString(customerVO));
            return Boolean.FALSE;
        }

        if (commonService.stationForbidden(customerVO.getCountry(), input.getStationType())) {
            log.info("existsMku, not exists. stationForbidden. input={}", JSONObject.toJSONString(input));
            return Boolean.FALSE;
        }

        Long mkuId = input.getMkuId();
        CustomerMkuPO customerMkuPO = customerMkuAtomicService.getOneValid(mkuId, input.getClientCode());
        if (null == customerMkuPO || customerMkuPO.getBindStatus() == CustomerMkuBindEnum.INVALID) {
            log.info("existsMku, not exists. no match CustomerMkuPO. input={}", JSONObject.toJSONString(input));
            return Boolean.FALSE;
        }
        log.info("existsMku, exists. input={}", JSONObject.toJSONString(input));
        return Boolean.TRUE;
    }

//    @Override
//    public MkuClientVO baseInfo(MkuClientDetailReqVO input) {
//        try {
//            String clientCode = input.getClientCode();
//            CustomerVO customerVO = customerAtomicService.get(clientCode);
//            if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
//                log.warn("baseInfo, customerVO error. clientCode={}, customerVO={}", clientCode, JSONObject.toJSONString(customerVO));
//                return null;
//            }
//
//            if (commonService.stationForbidden(customerVO.getCountry(), input.getStationType())) {
//                log.warn("baseInfo, stationForbidden. input={}", JSONObject.toJSONString(input));
//                return null;
//            }
//
//            Long mkuId = input.getMkuId();
//            String lang = langRpcService.getLangCodeList().contains(input.getLang()) ? input.getLang() : LangConstant.LANG_ZH;
//            CustomerMkuPO customerMkuPO = customerMkuAtomicService.getOneValid(mkuId, input.getClientCode());
//            if (null == customerMkuPO) {
//                log.warn("baseInfo, no match CustomerMkuPO. input={}", JSONObject.toJSONString(input));
//                return null;
//            }
//
//            MkuPO mkuPO = mkuAtomicService.getPOById(mkuId);
//            if (null == mkuPO) {
//                log.warn("baseInfo, mkuPO is null. mkuId={}", mkuId);
//                return null;
//            }
//            MkuClientVO mkuVO = MkuConvert.INSTANCE.mkuPo2mkuClientVo(mkuPO);
//            // 名称多语言
//            MkuOperateVO mkuLang = new MkuOperateVO(Sets.newHashSet(mkuId), lang);
//            Map<Long, MkuLangPO> mkuLangMap = mkuLangAtomicService.listLang(mkuLang);
//            mkuVO.setMkuName(null != mkuLangMap && null != mkuLangMap.get(mkuId) ? mkuLangMap.get(mkuId).getMkuTitle() : null);
//            mkuVO.setSaleStatus(MkuSaleStatusEnum.OFF.getCode());
//            if (CustomerMkuBindEnum.BIND.equals(customerMkuPO.getBindStatus())) {
//                mkuVO.setSaleStatus(MkuSaleStatusEnum.ON_SALE.getCode());
//                //价格
//                MkuClientPriceReqVO priceReqVO = new MkuClientPriceReqVO();
//                priceReqVO.setClientCode(input.getClientCode());
//                priceReqVO.setMkuIdList(Sets.newHashSet(mkuId));
//                DataResponse<List<MkuPriceResVO>> priceResponse = clientPriceManageService.listMkuPriceNotLimitSize(priceReqVO);
//                //mku&sku关系
//                Map<Long, MkuIdRefVO> mkuMap = getMkuRef(input.getClientCode(), Sets.newHashSet(mkuId));
//                buildSkuId(mkuVO, mkuMap);
//
//                //最小起订量
//                Map<Long, Integer> moqMap = buildMoqMap(mkuMap);
//                //库存
//                Map<Long, StockResVO> stockMap = buildStockMap(input.getStationType(), input.getClientCode(), Sets.newHashSet(mkuId), mkuMap, moqMap);
//                //货期
//                PromiseInfo promiseInfo = buildPromiseInfo(input.getClientCode(), mkuMap);
//                if (priceResponse.getSuccess() && CollectionUtils.isNotEmpty(priceResponse.getData())) {
//                    Optional<MkuPriceResVO> priceOptional = priceResponse.getData().stream().filter(o -> mkuId.equals(o.getMkuId())).findFirst();
//                    mkuVO.setSalePrice(priceOptional.map(MkuPriceResVO::getSalePrice).orElse(null));
//                    mkuVO.setCurrency(priceOptional.map(MkuPriceResVO::getCurrency).orElse(null));
//                    // 显示币种和价格
//                    ProductPricesDTO productPricesDTO = new ProductPricesDTO();
//                    productPricesDTO.setCurrency(priceOptional.map(MkuPriceResVO::getCurrency).orElse(null));
//                    productPricesDTO.setCurrencySource(priceOptional.map(MkuPriceResVO::getOriginCurrency).orElse(null));
//                    productPricesDTO.setExchangeRate(priceOptional.map(MkuPriceResVO::getExchangeRate).orElse(null));
//                    productPricesDTO.setSalePrice(priceOptional.map(MkuPriceResVO::getSalePrice).orElse(null));
//                    productPricesDTO.setIncludeTaxPrice(priceOptional.map(MkuPriceResVO::getIncludeTaxPrice).orElse(null));
//                    productPricesDTO.setValueAddedTax(priceOptional.map(MkuPriceResVO::getTaxPrice).orElse(null));
//                    productPricesDTO.setValueAddedTaxRate(priceOptional.map(MkuPriceResVO::getValueAddedTax).orElse(null));
//                    mkuVO.setShowCurrency(productPricesDTO);
//                    // 多币种价格
//                    Map<String, ProductPricesDTO> pricesDTOMap = Maps.newHashMap();
//                    pricesDTOMap.put(productPricesDTO.getCurrency(), productPricesDTO);
//                    mkuVO.setCurrenciesPrices(pricesDTOMap);
//                    //moq
//                    mkuVO.setMoq(moqMap.get(mkuVO.getMkuId()));
//                    if (MapUtils.isNotEmpty(stockMap)){
//                        // 库存
//                        buildStock(mkuVO, stockMap.get(mkuVO.getMkuId()) );
//                    }
//
//                    //货期
//                    Integer deliveryDays = 0;
//                    if (MapUtils.isNotEmpty(promiseInfo.getDeliveryDateMap())) {
//                        deliveryDays = promiseInfo.getDeliveryDateMap().get(mkuMap.get(mkuVO.getMkuId()).getPo().getJdSkuId());
//                        if (deliveryDays != null) {
//                            String langTemp;
//                            //期货
//                            if (deliveryDays >= promiseInfo.getDeliveryTemplate().getThreshold()) {
//                                langTemp = DeliveryTemplate.getByLang(promiseInfo.getDeliveryTemplate().getFutureDelivery(), input.getLang());
//                                //非期货
//                            } else {
//                                langTemp = DeliveryTemplate.getByLang(promiseInfo.getDeliveryTemplate().getNoFutureDelivery(), input.getLang());
//                            }
//                            mkuVO.setDeliveryDate(String.format(langTemp, deliveryDays));
//
//                        }
//                    }
//
//                    Set<Long> skuIds = mkuMap.entrySet().stream().filter(o->null!=o.getValue() && null!=o.getValue().getPo())
//                            .map(o->o.getValue().getPo().getSkuId()).filter(o->null!=o).collect(Collectors.toSet());
//                    Map<Long, SkuFeatureVO> skuFeatureVOMap = skuFeatureManageService.querySkuFeatureMap(skuIds, customerVO.getCountry());
//
//                    //查询时效数据
//                    Map<Long/*mkuId*/, Integer/*stockStateType*/> stockStateTypeMap = new HashMap<>();
//                    stockStateTypeMap.put(mkuPO.getMkuId(), mkuVO.getStock().getStockStateType());
//                    SkuDeliveryAgingVO skuDeliveryAgingVO = buildProductDeliveryAgingData(mkuPO, customerVO, mkuMap, deliveryDays, skuFeatureVOMap, stockStateTypeMap);
//                    mkuVO.setSkuDeliveryAging(skuDeliveryAgingVO);
//                } else {
//                    log.warn("baseInfo, List<MkuPriceReadResp> empty. priceReqVO={}", JSONObject.toJSONString(priceReqVO));
//                }
//            }
//
//            // 销售属性
//            List<PropertyVO> saleProList = attributeManagerService.getSaleAttrByCatId(mkuVO.getCatId(), mkuVO.getSaleAttribute(), lang);
//            mkuVO.setSaleAttributeValues(joinSaleAttributeValue(saleProList));
//
//            // 扩展属性
//            List<PropertyVO> extProList = attributeManagerService.getExtendAttrByCatId(mkuVO.getCatId(), mkuVO.getGroupExtAttribute(), lang);
//            List<MkuClientPropertyVO> clientExtProList = convertExtentAttribute(extProList);
//            mkuVO.setExtentAttributes(clientExtProList);
//
//            // 商品详情描述多语言
//            MkuDescLangPO mkuDescLangPO = mkuDescLangAtomicService.descLang(mkuId, lang);
//            mkuVO.setDescription(null != mkuDescLangPO ? mkuDescLangPO.getPcDescription() : null);
//
//            // 品牌
//            BrandPO brandPo = brandAtomicService.getById(mkuPO.getBrandId());
//            if (null != brandPo) {
//                BrandClientVO brandVO = BrandConvert.INSTANCE.brandPo2ClientBrandVO(brandPo);
//                Map<Long, String> brandMap = brandOutService.queryNameByIds(Sets.newHashSet(mkuPO.getBrandId()), lang);
//                brandVO.setName(null != brandMap ? brandMap.get(mkuPO.getBrandId()) : null);
//                mkuVO.setBrand(brandVO);
//            } else {
//                log.info("baseInfo, brandPo null. mkuId={}, branId={}", mkuPO.getMkuId(), mkuPO.getBrandId());
//            }
//
//            // 类目
//            List<CategoryComboBoxVO> categoryPath = categoryOutService.queryPath(mkuPO.getJdCatId(), lang);
//            List<CategoryTreeNodeDTO> pathNodeList = CategoryConvert.INSTANCE.comboBoxList2TreeNodeDTOList(categoryPath);
//            mkuVO.setCatePath(pathNodeList);
//
//            if (null != commonDUCCConfig && Objects.equals(Boolean.TRUE, commonDUCCConfig.getLogDebugSwitch())) {
//                log.info("baseInfo. mkuVO={}, input={}", JSONObject.toJSONString(mkuVO), JSONObject.toJSONString(input));
//            }
//
//            return mkuVO;
//        } catch (Exception e) {
//            log.error("baseInfo, input = {}", JSON.toJSON(input), e);
//
//            StringBuilder msg = new StringBuilder();
//            msg.append(active).append(LevelCode.P0.getMessage()).append("WISP查询商品详情异常")
//                    .append("  , input=").append(JSONObject.toJSONString(input));
//            Profiler.businessAlarm("com.jdi.isc.aggregate.read.service.manage.mku.impl.MkuClientManageServiceImpl.baseInfo."+ active, msg.toString());
//        }
//        return null;
//    }
//
//    /**
//     * 设置skuId
//     * @param mkuVO
//     * @param mkuMap
//     */
//    private void buildSkuId(MkuClientVO mkuVO, Map<Long, MkuIdRefVO> mkuMap){
//        if (MapUtils.isEmpty(mkuMap)) {
//            return;
//        }
//
//        MkuIdRefVO mkuIdRefVO = mkuMap.get(mkuVO.getMkuId());
//        if (null==mkuIdRefVO || null==mkuIdRefVO.getPo()){
//            return;
//        }
//        // 设置skuId
//        mkuVO.setSkuId(mkuIdRefVO.getPo().getSkuId());
//    }

    /**
     * 构建产品交付老化数据。
     * @param mkuPO MkuPO对象，包含PO信息。
     * @param customerVO CustomerVO对象，包含客户信息。
     * @param mkuMap MkuIdRefVO的Map，用于获取SkuId。
     * @param deliveryDays 交付天数。
     * @return SkuDeliveryAgingVO对象，包含交付老化数据。
     */
    private SkuDeliveryAgingVO buildProductDeliveryAgingData(MkuPO mkuPO, CustomerVO customerVO, Map<Long, MkuIdRefVO> mkuMap
            , Integer deliveryDays, Map<Long, SkuFeatureVO> skuFeatureVOMap, Map<Long/*mkuId*/, Integer/*stockStateType*/> stockStateTypeMap) {
        int initDeliveryDays = null!=deliveryDays?deliveryDays:0;

        String productCountry = mkuPO.getSourceCountryCode();
        String customerCountry = customerVO.getCountry();
        SkuDeliveryAgingVO skuDeliveryAgingVO = new SkuDeliveryAgingVO();
        skuDeliveryAgingVO.setDeliverGoodsDays(BigDecimal.valueOf(initDeliveryDays));
        Long skuId = null;

        try {
            //查询备货标记
            MkuIdRefVO mkuIdRefVO = mkuMap.get(mkuPO.getMkuId());
            if (null==mkuIdRefVO || null==mkuIdRefVO.getPo()){
                log.info("buildProductDeliveryAgingData, mkuIdRef info is null. mkuId={}", mkuPO.getMkuId());
                return skuDeliveryAgingVO;
            }
            skuId = mkuIdRefVO.getPo().getSkuId();
            SkuFeatureVO skuFeatureVO = skuFeatureVOMap.get(skuId);
            Integer purchaseModel = skuFeatureVO.getPurchaseModel();
            BigDecimal deliveryGoodsDay = skuFeatureVO.getProductionCycle();
            //设置商品发货时效
            if (initDeliveryDays == 0) {
                log.info("buildProductDeliveryAgingData set local info={}", skuFeatureVO);
                skuDeliveryAgingVO.setDeliverGoodsDays(deliveryGoodsDay);
            }
            //在途有货 备货品 转为 本本直发/跨境直发 ，补丁-因为现在没有本本备货/跨境备货履约模式
            //需要取获取实效信息
            Integer stockStateType = MapUtils.isNotEmpty(stockStateTypeMap) ? stockStateTypeMap.get(mkuPO.getMkuId()) : null;
            if (stockStateType != null && stockStateType == 39 &&
                    (Objects.equals(purchaseModel, PurchaseModelEnum.STOCK_UP.getCode()) || Objects.equals(purchaseModel, PurchaseModelEnum.CONSIGNMENT.getCode()))) {
                purchaseModel=0;
                GoodsFulfillmentTimeApiDTO.Response result = deliveryTimeRpcService.queryDeliveryTimeInfo(productCountry, customerCountry, purchaseModel);
                if (result != null && !org.springframework.util.CollectionUtils.isEmpty(result.getLimitDetails())) {
                    Map<Integer, GoodsFulfillmentTimeApiDTO.Response.LimitDetailsDTO> limitDetails = result.getLimitDetails();
                    Integer needShippingType = ShippingTypeEnum.SEA_WAY.getCode();
                    if (limitDetails.containsKey(needShippingType)) {
                        GoodsFulfillmentTimeApiDTO.Response.LimitDetailsDTO limitDetailsDTO = limitDetails.get(needShippingType);
                        skuDeliveryAgingVO.setMinDeliveryDays(limitDetailsDTO.getTimeLimitMin());
                        skuDeliveryAgingVO.setMaxDeliveryDays(limitDetailsDTO.getTimeLimitMax());
                        //获取到了实效信息以后，还需要将模式替换为原有的履约模式，为了wisp展示文案使用
                        skuDeliveryAgingVO.setFulfillmentMode(FulfillmentTypeEnum.STOCK_WAREHOUSE.getId());
                        return skuDeliveryAgingVO;
                    }
                }
            }else {
                GoodsFulfillmentTimeApiDTO.Response result = deliveryTimeRpcService.queryDeliveryTimeInfo(productCountry, customerCountry, purchaseModel);
                if (result != null && !org.springframework.util.CollectionUtils.isEmpty(result.getLimitDetails())) {
                    Map<Integer, GoodsFulfillmentTimeApiDTO.Response.LimitDetailsDTO> limitDetails = result.getLimitDetails();
                    Integer needShippingType = ShippingTypeEnum.SEA_WAY.getCode();
                    if (limitDetails.containsKey(needShippingType)) {
                        GoodsFulfillmentTimeApiDTO.Response.LimitDetailsDTO limitDetailsDTO = limitDetails.get(needShippingType);
                        skuDeliveryAgingVO.setMinDeliveryDays(limitDetailsDTO.getTimeLimitMin());
                        skuDeliveryAgingVO.setMaxDeliveryDays(limitDetailsDTO.getTimeLimitMax());
                        skuDeliveryAgingVO.setFulfillmentMode(limitDetailsDTO.getFfMode());
                        return skuDeliveryAgingVO;
                    }
                }
            }
        } catch (Exception e) {
            log.error("buildProductDeliveryAgingData error", e);
            StringBuilder msg = new StringBuilder();
            msg.append(active).append(LevelCode.P1.getMessage()).append("组装发货履约时效信息异常")
                    .append("  , mkuPO=").append(JSONObject.toJSONString(mkuPO));
            Profiler.businessAlarm("com.jdi.isc.aggregate.read.service.manage.mku.impl.MkuClientManageServiceImpl.buildProductDeliveryAgingData."+ active, msg.toString());
        } finally {
            log.info("buildProductDeliveryAgingData skuId: {},result:{}", skuId, JSONObject.toJSONString(skuDeliveryAgingVO));
        }
        return skuDeliveryAgingVO;
    }

//    @Override
//    @ToolKit
//    public MkuClientGroupVO groupInfo(MkuClientDetailReqVO input) {
//        CustomerVO customerVO = customerAtomicService.get(input.getClientCode());
//        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
//            log.warn("groupInfo, customerVO error. clientCode={}, customerVO={}", input.getClientCode(), JSONObject.toJSONString(customerVO));
//            return null;
//        }
//
//        if (commonService.stationForbidden(customerVO.getCountry(), input.getStationType())) {
//            log.warn("groupInfo, stationForbidden. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//
//        Long mkuId = input.getMkuId();
//        String lang = langRpcService.getLangCodeList().contains(input.getLang()) ? input.getLang() : LangConstant.LANG_ZH;
//
//        // 根据入参查基本信息
//        CustomerMkuPO customerMkuPO = customerMkuAtomicService.getOneValid(mkuId,
//                input.getClientCode(), CustomerMkuBindEnum.BIND);
//        if (null == customerMkuPO) {
//            log.warn("groupInfo, CustomerMkuPO is null. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//
//        // 查同一分组下的所有mku id
//        CustomerMkuPO condition = new CustomerMkuPO();
//        condition.setClientCode(input.getClientCode());
//        condition.setCatId(customerMkuPO.getCatId());
//        condition.setSpuId(customerMkuPO.getSpuId());
//        condition.setJdCatId(customerMkuPO.getJdCatId());
//        List<CustomerMkuPO> customerMkuPOList = customerMkuAtomicService.groupInfoBySpu(condition);
//        Set<Long> mkuIds = customerMkuPOList.stream().filter(o -> {
//                    return null != o && Objects.nonNull(o.getMkuId());
//                })
//                .map(CustomerMkuPO::getMkuId).collect(Collectors.toSet());
//        List<MkuPO> mkuPOList = mkuAtomicService.listByMkuIds(Lists.newArrayList(mkuIds));
//
//        // 查同组中每个mku的详细信息
//        List<MkuClientSimpleVO> groupMembers = new ArrayList<>(mkuPOList.size());
//        for (MkuPO mkuPO : mkuPOList) {
//            if (null == mkuPO) {
//                continue;
//            }
//            // 查销售属性信息
//            List<PropertyVO> saleProList = attributeManagerService.getSaleAttrByCatId(customerMkuPO.getJdCatId(), mkuPO.getSaleAttribute(), lang);
//            log.info("groupInfo, mkuId={}, saleProList={}", mkuPO.getMkuId(), JSONObject.toJSONString(saleProList));
//
//            MkuClientSimpleVO.MkuClientSimpleVOBuilder simpleVoBuilder = MkuClientSimpleVO.builder();
//            simpleVoBuilder.mkuId(mkuPO.getMkuId())
//                    .name(joinSaleAttributeValue(saleProList))
//                    .mainImg(mkuPO.getMainImg())
//                    .saleProList(saleProList);
//            groupMembers.add(simpleVoBuilder.build());
//        }
//        // 重新排序
//        //groupMembers = groupMembers.stream().sorted(Comparator.comparing(MkuClientSimpleVO::getMkuId)).collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(groupMembers)) {
//            Collections.sort(groupMembers, new MkuClientSimpleVOComparator());
//        }
//        MkuClientGroupVO groupVO = new MkuClientGroupVO();
//        groupVO.setMembers(groupMembers);
//        return groupVO;
//    }
//
//    @Override
//    @ToolKit
//    public MkuClientCardInfoResVO cardInfo(MkuClientCardInfoReqVO input) {
//        if (commonService.stationForbidden(input)) {
//            log.warn("cardInfo, stationForbidden. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//
//        // 品牌
//        BrandPO brandPo = brandAtomicService.getById(input.getBrandId());
//        if (null == brandPo) {
//            log.info("cardInfo, brandPo null. input={}", input);
//            return null;
//        }
//        BrandClientVO brandVO = BrandConvert.INSTANCE.brandPo2ClientBrandVO(brandPo);
//
//        Map<Long, String> brandMap = brandOutService.queryNameByIds(Sets.newHashSet(brandPo.getId()), input.getLang());
//        brandVO.setName(null != brandMap ? brandMap.get(brandPo.getId()) : "");
//
//        MkuClientCardInfoResVO resVO = new MkuClientCardInfoResVO();
//        resVO.setBrandInfo(brandVO);
//        return resVO;
//    }
//
//    /**
//     * 拼接销售属性值
//     *
//     * @param saleProList 销售属性
//     * @return 多个销售属性值字符串
//     */
//    private String joinSaleAttributeValue(List<PropertyVO> saleProList) {
//        if (CollectionUtils.isEmpty(saleProList)) {
//            return "";
//        }
//
//        StringJoiner stringJoiner = new StringJoiner(" ");
//        for (PropertyVO vo : saleProList) {
//            if (null == vo || CollectionUtils.isEmpty(vo.getPropertyValueVOList())) {
//                log.warn("joinSaleAttributeValue, PropertyVO invalid. PropertyVO={}", JSONObject.toJSONString(vo));
//                continue;
//            }
//
//            List<PropertyValueVO> valueVOList = vo.getPropertyValueVOList();
//            if (valueVOList.size() > 1) {
//                log.warn("valueVOList size gt 1. vo={}", JSONObject.toJSONString(vo));
//                continue;
//            }
//            valueVOList.forEach(o -> stringJoiner.add(o.getAttributeValueName()));
//        }
//
//        return stringJoiner.toString();
//    }
//
//    /**
//     * 转换扩展属性
//     *
//     * @param extProList 扩展属性
//     * @return 客户端展示的扩展属性集合
//     */
//    private List<MkuClientPropertyVO> convertExtentAttribute(List<PropertyVO> extProList) {
//        if (CollectionUtils.isEmpty(extProList)) {
//            return null;
//        }
//
//        List<MkuClientPropertyVO> resList = new ArrayList<>();
//        for (PropertyVO vo : extProList) {
//            if (null == vo || CollectionUtils.isEmpty(vo.getPropertyValueVOList())) {
//                continue;
//            }
//
//            StringJoiner stringJoiner = new StringJoiner(" ");
//            List<PropertyValueVO> valueVOList = vo.getPropertyValueVOList();
//            valueVOList.forEach(o -> stringJoiner.add(o.getAttributeValueName()));
//
//            MkuClientPropertyVO clientPropertyVO = new MkuClientPropertyVO();
//            clientPropertyVO.setName(vo.getAttributeName());
//            clientPropertyVO.setValues(stringJoiner.toString());
//            clientPropertyVO.setShield(vo.getShield());
//            clientPropertyVO.setIsQuJianZhi(vo.getIsQuJianZhi());
//            clientPropertyVO.setComGroupId(vo.getComGroupId());
//            clientPropertyVO.setComGroupName(vo.getComGroupName());
//            resList.add(clientPropertyVO);
//        }
//
//        return resList;
//    }

    /**
     * mku查找jdSku映射关系
     *
     * @param mkuIds mkuIds
     * @return mkuIds mku->MkuIdRefVO
     */
    @Override
    @ToolKit
    public Map<Long, MkuIdRefVO> getMkuRef(String clientCode, Set<Long> mkuIds) {
        Map<Long, MkuIdRefVO> result = new HashMap<>();
        MkuPriceReqVO priceReqVO = new MkuPriceReqVO();
        priceReqVO.setClientCode(clientCode);
        priceReqVO.setMkuIdList(mkuIds);
        List<CustomerMkuPricePO> fixedSkuList = customerMkuPriceManageService.getCustomerMkuFixedSkuListByMkuClient(priceReqVO);
        //sku&mku
        Map<Long, Long> mkuMap = fixedSkuList.stream().collect(Collectors.toMap(CustomerMkuPricePO::getFixedSkuId, CustomerMkuPricePO::getMkuId));
        //sku&skuPo
        Set<Long> skuIds = fixedSkuList.stream().map(CustomerMkuPricePO::getFixedSkuId).collect(Collectors.toSet());
        Map<Long, SkuPO> skuPOMap = skuAtomicService.batchQuerySkuPO(skuIds);
        CustomerVO customerVO = customerManageService.detail(clientCode);
        // sku&skuFeaturePo
        Map<Long, SkuFeatureVO> skuFeaturePOMap = skuFeatureManageService.querySkuFeatureMap(skuIds,customerVO.getCountry());
        for (Map.Entry<Long, SkuPO> entry : skuPOMap.entrySet()) {
            MkuIdRefVO res = new MkuIdRefVO();
            res.setPo(entry.getValue());
            res.setFeaturePO(skuFeaturePOMap.get(entry.getKey()));
            result.put(mkuMap.get(entry.getKey()), res);
        }
        return result;
    }

//    @Override
//    @ToolKit
//    public Map<Long, Long> mku2JdSku(String clientCode, Set<Long> mkuIds) {
//        Map<Long, Long> result = new HashMap<>();
//        MkuPriceReqVO priceReqVO = new MkuPriceReqVO();
//        priceReqVO.setClientCode(clientCode);
//        priceReqVO.setMkuIdList(mkuIds);
//        List<CustomerMkuPricePO> fixedSkuList = customerMkuPriceManageService.getCustomerMkuFixedSkuListByMkuClient(priceReqVO);
//        //sku&mku
//        Map<Long, Long> mkuMap = fixedSkuList.stream().collect(Collectors.toMap(CustomerMkuPricePO::getFixedSkuId, CustomerMkuPricePO::getMkuId));
//        //sku&skuPo
//        Map<Long, SkuPO> skuPOMap = skuAtomicService.batchQuerySkuPO(fixedSkuList.stream().map(CustomerMkuPricePO::getFixedSkuId).collect(Collectors.toSet()));
//        for (Map.Entry<Long, SkuPO> entry : skuPOMap.entrySet()) {
//            result.put(mkuMap.get(entry.getKey()), entry.getValue().getJdSkuId());
//        }
//        return result;
//    }

    @Override
    public Map<Long, SkuDeliveryAgingVO> getDeliveryInfo(List<Long> mkuIds, String clientCode, Map<Long/*mkuId*/, Integer/*stockStateType*/> stockStateTypeMap) {
        CustomerVO customerVO = customerAtomicService.get(clientCode);
        if (null == customerVO || StringUtils.isBlank(customerVO.getCountry())) {
            log.warn("baseInfo, customerVO error. clientCode={}, customerVO={}", clientCode, JSONObject.toJSONString(customerVO));
            return null;
        }
        List<MkuPO> mkuPOS = mkuAtomicService.listByMkuIds(mkuIds);
        Map<Long, SkuDeliveryAgingVO> res = new HashMap<>();
        Map<Long, MkuIdRefVO> mkuMap = getMkuRef(clientCode, Sets.newHashSet(mkuIds));
        PromiseInfo promiseInfo = buildPromiseInfoByFuture(clientCode, mkuMap);

        Set<Long> skuIds = mkuMap.entrySet().stream().filter(o->null!=o.getValue() && null!=o.getValue().getPo())
                .map(o->o.getValue().getPo().getSkuId()).filter(o->null!=o).collect(Collectors.toSet());
        Map<Long, SkuFeatureVO> skuFeatureVOMap = skuFeatureManageService.querySkuFeatureMap(skuIds, customerVO.getCountry());

        for (MkuPO mkuPO : mkuPOS) {
            Long mkuId = mkuPO.getMkuId();
            Integer deliveryDays = 0;
            if (MapUtils.isNotEmpty(promiseInfo.getDeliveryDateMap())) {
                deliveryDays = promiseInfo.getDeliveryDateMap().get(mkuMap.get(mkuId).getPo().getJdSkuId());
            }
            SkuDeliveryAgingVO skuDeliveryAgingVO = buildProductDeliveryAgingData(mkuPO, customerVO, mkuMap, deliveryDays, skuFeatureVOMap,stockStateTypeMap);
            res.put(mkuId, skuDeliveryAgingVO);
        }
        return res;
    }

    private List<Long> getCustomerMkuIdList(Set<Long> mkuIdList, String clientCode) {
        List<CustomerMkuPO> customerMkuPoList = this.queryCustomerMkuPoList(mkuIdList, clientCode);
        if (CollectionUtils.isEmpty(customerMkuPoList)) {
            return Collections.emptyList();
        }
        return customerMkuPoList.stream().filter(Objects::nonNull).map(CustomerMkuPO::getMkuId).collect(Collectors.toList());
    }

    private List<CustomerMkuPO> queryCustomerMkuPoList(Set<Long> mkuIdList, String clientCode) {
        LambdaQueryWrapper<CustomerMkuPO> wrapper = Wrappers.<CustomerMkuPO>lambdaQuery()
                .in(CustomerMkuPO::getMkuId, mkuIdList)
                .eq(CustomerMkuPO::getClientCode, clientCode)
                .eq(CustomerMkuPO::getBindStatus, CustomerMkuBindEnum.BIND)
                .eq(CustomerMkuPO::getYn, YnEnum.YES.getCode());
        return customerMkuBaseMapper.selectList(wrapper);
    }
//
//
//    @Override
//    @ToolKit
//    public Set<Long> queryPoolMkuIds(MkuClientListInfoReqApiDTO input) {
//        CustomerVO customerVO = customerAtomicService.get(input.getClientCode());
//        MkuPriceReqVO priceReqVO = new MkuPriceReqVO();
//        priceReqVO.setClientCode(input.getClientCode());
//        priceReqVO.setMkuIdList(input.getMkuIds());
//
//        List<CustomerMkuPricePO> customerMkuPricePOS = this.queryPoolMkuList(priceReqVO);
//        return customerMkuPricePOS.stream().map(CustomerMkuPricePO::getMkuId).collect(Collectors.toSet());
//    }
//
//    /**
//     * 获取在池的sku
//     */
//    private List<CustomerMkuPricePO> queryPoolMkuList(MkuPriceReqVO req) {
//        // 获取当前客户站点下固定sku信息
//        List<CustomerMkuPricePO> mkuList = customerMkuPriceManageService.getCustomerMkuFixedSkuListByMkuClient(req);
//        if (null == mkuList) {
//            return Collections.emptyList();
//        }
//        return mkuList;
//    }
//
//    public class MkuClientSimpleVOComparator implements Comparator<MkuClientSimpleVO> {
//
//        @Override
//        public int compare(MkuClientSimpleVO o1, MkuClientSimpleVO o2) {
//            if (o1 == null && o2 == null) {
//                return 0;
//            } else if (o1 == null && o2 != null) {
//                return 1;
//            } else if (o1 != null && o2 == null) {
//                return -1;
//            }
//
//            if (CollectionUtils.isEmpty(o1.getSaleProList()) && CollectionUtils.isEmpty(o2.getSaleProList())) {
//                return 0;
//            } else if (CollectionUtils.isEmpty(o1.getSaleProList()) && CollectionUtils.isNotEmpty(o2.getSaleProList())) {
//                return 1;
//            } else if (CollectionUtils.isNotEmpty(o1.getSaleProList()) && CollectionUtils.isEmpty(o2.getSaleProList())) {
//                return -1;
//            }
//
//            // 获取两个对象的saleProList的最小长度，以避免索引越界
//            int minSize = Math.min(o1.getSaleProList().size(), o2.getSaleProList().size());
//
//            for (int i = 0; i < minSize; i++) {
//                List<PropertyValueVO> propValueList1 = o1.getSaleProList().get(i).getPropertyValueVOList();
//                List<PropertyValueVO> propValueList2 = o2.getSaleProList().get(i).getPropertyValueVOList();
//
//                // 获取propertyValueVOList的最小长度，以避免索引越界
//                int minValueListSize = Math.min(propValueList1.size(), propValueList2.size());
//
//                for (int j = 0; j < minValueListSize; j++) {
//                    int sort1 = propValueList1.get(j).getSort();
//                    int sort2 = propValueList2.get(j).getSort();
//
//                    if (sort1 != sort2) {
//                        return Integer.compare(sort1, sort2);
//                    }
//                }
//
//                // 如果当前索引的PropertyValueVO的sort值全部相同，但一个列表比另一个列表长，较长的列表应该排在后面
//                if (propValueList1.size() != propValueList2.size()) {
//                    return Integer.compare(propValueList1.size(), propValueList2.size());
//                }
//            }
//
//            // 如果所有对应的PropertyValueVO的sort值都相同，或者所有PropertyVO的propertyValueVOList长度都相等，则认为两个MkuClientSimpleVO相等
//            return 0;
//        }
//    }
//
//
//    public List<AttributeKeyAndValueVO> extractSaleAttributes(List<MkuPO> simpleApiDTOList) {
//        if (simpleApiDTOList == null || simpleApiDTOList.isEmpty()) {
//            return Collections.emptyList();
//        }
//        List<MkuAttributeValueDTO> resultList = simpleApiDTOList.stream()
//                .map(MkuPO::getSaleAttribute) // 提取 saleAttribute 字段
//                .filter(Objects::nonNull) // 过滤掉 null 值
//                .flatMap(saleAttribute -> Arrays.stream(saleAttribute.split("#"))) // 按 "#" 分割每个 saleAttribute
//                .map(pair -> pair.split(":")) // 按 ":" 分割成键值对
//                .filter(values -> values.length == 2) // 确保键值对格式正确
//                .map(values -> {
//                    try {
//                        return MkuAttributeValueDTO.builder()
//                                .attributeId(Long.valueOf(values[0]))
//                                .attributeValueId(Long.valueOf(values[1]))
//                                .build();
//                    } catch (NumberFormatException e) {
//                        log.error("Invalid attribute pair: " + Arrays.toString(values));
//                        return null;
//                    }
//                })
//                .filter(Objects::nonNull) // 排除解析失败的记录
//                .collect(Collectors.toList());
//
//        // 如果结果为空，直接返回空集合
//        if (resultList.isEmpty()) {
//            return Collections.emptyList();
//        }
//
//        // 查询并返回最终结果
//        return attributeAtomicService.queryAttributesByDTOList(resultList);
//    }
//
//    private void buildStock(MkuClientVO mkuVO, StockResVO stockResVO){
//        // 库存对象
//        MkuClientStockVO mkuClientStockVO = MkuClientStockConvert.INSTANCE.stockResVO2ApiDTO(stockResVO);
//        if (null==mkuClientStockVO){
//            mkuClientStockVO = new MkuClientStockVO();
//        }
//
//        mkuVO.setStock(mkuClientStockVO);
//    }
}
