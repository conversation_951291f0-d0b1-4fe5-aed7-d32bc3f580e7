package com.jdi.isc.aggregate.read.service.protocol.jsf.wiop.mku;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.jp.strategy.sdk.dto.SkuDetailedPageInfoResp;
import com.jd.k2.gd.boost.dto.ReflectDataDto;
import com.jd.ka.stock.enums.ReflectTypeEnum;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.biz.exception.ServiceException;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.constant.DataResponseCode;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.aggregate.read.common.config.OperDuccConfig;
import com.jdi.isc.aggregate.read.common.costants.Constant;
import com.jdi.isc.aggregate.read.common.costants.UmpKeyConstant;
import com.jdi.isc.aggregate.read.common.utils.ConfigUtils;
import com.jdi.isc.aggregate.read.domain.attribute.biz.AttributeLangVO;
import com.jdi.isc.aggregate.read.domain.attribute.biz.AttributeVO;
import com.jdi.isc.aggregate.read.domain.attribute.biz.AttributeValueLangVO;
import com.jdi.isc.aggregate.read.domain.attribute.biz.AttributeValueVO;
import com.jdi.isc.aggregate.read.domain.category.biz.CategoryComboBoxVO;
import com.jdi.isc.aggregate.read.domain.customer.biz.CustomerVO;
import com.jdi.isc.aggregate.read.domain.customerMku.CustomerMkuPricePO;
import com.jdi.isc.aggregate.read.domain.customerMku.po.CustomerMkuPO;
import com.jdi.isc.aggregate.read.domain.enums.MkuStatusEnum;
import com.jdi.isc.aggregate.read.domain.enums.YnEnum;
import com.jdi.isc.aggregate.read.domain.gms.resp.JdProductDTO;
import com.jdi.isc.aggregate.read.domain.mku.biz.MkuOperateVO;
import com.jdi.isc.aggregate.read.domain.mku.po.MkuDescLangPO;
import com.jdi.isc.aggregate.read.domain.mku.po.MkuLangPO;
import com.jdi.isc.aggregate.read.domain.mku.po.MkuPO;
import com.jdi.isc.aggregate.read.domain.price.biz.MkuPriceReqVO;
import com.jdi.isc.aggregate.read.domain.price.biz.MkuPriceResVO;
import com.jdi.isc.aggregate.read.domain.promise.biz.JdSkuDetailPromiseReqVO;
import com.jdi.isc.aggregate.read.domain.sku.biz.SkuFeatureVO;
import com.jdi.isc.aggregate.read.domain.sku.po.SkuPO;
import com.jdi.isc.aggregate.read.domain.spu.biz.ExtendPropertyGroupVO;
import com.jdi.isc.aggregate.read.domain.spu.biz.ShotPropertyVO;
import com.jdi.isc.aggregate.read.domain.spu.biz.ShotPropertyValueVO;
import com.jdi.isc.aggregate.read.domain.spu.po.SpuPO;
import com.jdi.isc.aggregate.read.domain.taxRate.po.CustomerSkuTaxRatePO;
import com.jdi.isc.aggregate.read.repository.jed.mapper.customerMku.CustomerMkuBaseMapper;
import com.jdi.isc.aggregate.read.repository.jed.mapper.customerMku.CustomerMkuPriceBaseMapper;
import com.jdi.isc.aggregate.read.repository.jed.mapper.sku.SkuBaseMapper;
import com.jdi.isc.aggregate.read.rpc.attribute.AttributeRpcService;
import com.jdi.isc.aggregate.read.rpc.category.IscClientCategoryRelationRpcService;
import com.jdi.isc.aggregate.read.rpc.countryMku.CountryMkuRpcService;
import com.jdi.isc.aggregate.read.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.aggregate.read.rpc.iop.SkuAreaLimitRpcService;
import com.jdi.isc.aggregate.read.rpc.mku.MkuRpcService;
import com.jdi.isc.aggregate.read.rpc.translate.TranslateRcpService;
import com.jdi.isc.aggregate.read.service.ExtendInfoService;
import com.jdi.isc.aggregate.read.service.adapter.SkuConvertService;
import com.jdi.isc.aggregate.read.service.adapter.SpuConvertService;
import com.jdi.isc.aggregate.read.service.adapter.mapstruct.customerMku.CustomerMkuConvert;
import com.jdi.isc.aggregate.read.service.adapter.mapstruct.customerMku.CustomerMkuPriceConvert;
import com.jdi.isc.aggregate.read.service.adapter.mapstruct.mku.MkuConvert;
import com.jdi.isc.aggregate.read.service.atomic.customerMku.CustomerMkuAtomicService;
import com.jdi.isc.aggregate.read.service.atomic.mku.MkuAtomicService;
import com.jdi.isc.aggregate.read.service.atomic.mku.MkuDescLangAtomicService;
import com.jdi.isc.aggregate.read.service.atomic.mku.MkuLangAtomicService;
import com.jdi.isc.aggregate.read.service.atomic.sku.SkuAtomicService;
import com.jdi.isc.aggregate.read.service.atomic.spu.SpuAtomicService;
import com.jdi.isc.aggregate.read.service.atomic.taxRate.CustomerSkuTaxRateAtomicService;
import com.jdi.isc.aggregate.read.service.manage.attribute.AttributeOutService;
import com.jdi.isc.aggregate.read.service.manage.brand.BrandOutService;
import com.jdi.isc.aggregate.read.service.manage.category.CategoryOutService;
import com.jdi.isc.aggregate.read.service.manage.customer.CustomerManageService;
import com.jdi.isc.aggregate.read.service.manage.customerMku.CustomerMkuPriceManageService;
import com.jdi.isc.aggregate.read.service.manage.mku.MkuRelationService;
import com.jdi.isc.aggregate.read.service.manage.price.MkuPriceManageService;
import com.jdi.isc.aggregate.read.service.manage.promise.PromiseManageService;
import com.jdi.isc.aggregate.read.service.manage.sku.SkuFeatureManageService;
import com.jdi.isc.aggregate.read.service.support.AssertValidation;
import com.jdi.isc.aggregate.read.wiop.api.common.DescriptionConstant;
import com.jdi.isc.aggregate.read.wiop.api.mku.MkuReadApiService;
import com.jdi.isc.aggregate.read.wiop.api.mku.req.*;
import com.jdi.isc.aggregate.read.wiop.api.mku.resp.*;
import com.jdi.isc.aggregate.read.wisp.api.common.CustomerMkuBindEnum;
import com.jdi.isc.product.soa.api.attribute.IscAttributeReadApiService;
import com.jdi.isc.product.soa.api.attribute.common.AttributeDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeLangDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeValueDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeValueLangDTO;
import com.jdi.isc.product.soa.api.attribute.req.AttributeQueryReqDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import com.jdi.isc.product.soa.api.common.enums.AttributeTypeEnum;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuCheckReqDTO;
import com.jdi.isc.product.soa.api.common.enums.MkuQueryLangsEnum;
import com.jdi.isc.product.soa.api.mku.IscProductSoaMkuReadApiService;
import com.jdi.isc.product.soa.api.mku.req.BatchQueryMkuLangsReqDTO;
import com.jdi.isc.product.soa.api.mku.req.IscMkuAvailableSaleReq;
import com.jdi.isc.product.soa.api.mku.res.IscMkuAvailableSaleResDTO;
import com.jdi.isc.product.soa.api.common.enums.AttributeTypeEnum;
import com.jdi.isc.product.soa.api.mku.res.IscMkuLangsResDTO;
import com.jdi.isc.product.soa.api.sku.req.QuerySkuAvailableSaleReqDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuAvailableSaleResDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientUpSaleApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdi.isc.aggregate.read.common.costants.Constant.*;

/**
 * <AUTHOR>
 * @date 2023/12/13 18:46
 */
@Slf4j
@Service
public class MkuReadApiServiceImpl implements MkuReadApiService {

    @Resource
    private MkuAtomicService mkuAtomicService;

    @Resource
    private MkuLangAtomicService mkuLangAtomicService;

    @Resource
    private MkuDescLangAtomicService mkuDescLangAtomicService;

    @Resource
    private AttributeOutService attributeOutService;

    @Resource
    private BrandOutService brandOutService;

    @Resource
    private CategoryOutService categoryOutService;

    @Resource
    private SpuConvertService spuConvertService;

    @Resource
    private SkuConvertService skuConvertService;
    @Resource
    private CustomerMkuPriceBaseMapper customerMkuPriceBaseMapper;
    @Resource
    private CustomerMkuPriceManageService customerMkuPriceManageService;
    @Resource
    private CustomerMkuBaseMapper customerMkuBaseMapper;

    @Resource
    private CustomerMkuAtomicService customerMkuAtomicService;

    @Resource
    private SkuAreaLimitRpcService skuAreaLimitRpcService;

    @Resource
    private SpuAtomicService spuAtomicService;

    @Resource
    private SkuBaseMapper skuBaseMapper;

    @Resource
    private SkuInfoRpcService skuInfoRpcService;

    @Resource
    private AsyncTaskExecutor coreTaskExecutor;

    @Resource
    private MkuPriceManageService mkuPriceManageService;

    private static final int SIZE = 100;

    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private TranslateRcpService translateRcpService;
    @LafValue("jdi.isc.aggregate.canSale.translate.message")
    private String canSaleTranslateMessage;
    @Resource
    private MkuRelationService mkuRelationService;
    @Resource
    private CustomerSkuTaxRateAtomicService customerSkuTaxRateAtomicService;
    @Resource
    private SkuAtomicService skuAtomicService;

    @Resource
    private SkuFeatureManageService skuFeatureManageService;
    @Resource
    private PromiseManageService promiseManageService;
    @Resource
    private ExtendInfoService extendInfoService;
    @Resource
    private IscClientCategoryRelationRpcService iscClientCategoryRelationRpcService;
    @LafValue("jdi.isc.open.byd.clients")
    private Set<String> bydClients;

    @Resource
    private IscAttributeReadApiService iscAttributeReadApiService;

    @Resource
    private OperDuccConfig operDuccConfig;

    @Resource
    private AttributeRpcService attributeRpcService;
    @Resource
    private MkuRpcService mkuRpcService;
    @Resource
    private CountryMkuRpcService countryMkuRpcService;

    @Resource
    private IscProductSoaMkuReadApiService iscProductSoaMkuReadApiService;

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<MkuImageReadResp>> getMkuImageList(MkuImageReadReq reqDTO) {
        AssertValidation.isEmpty(reqDTO, DataResponseCode.PARAM_ERROR.getMessage());
        // 查询客户绑定商品关系
        List<Long> mkuIdList = this.getCustomerMkuIdList(Sets.newHashSet(reqDTO.getMkuIdList()), reqDTO.getClientCode());
        if (CollectionUtils.isEmpty(mkuIdList)) {
            return DataResponse.success();
        }
        List<MkuPO> mkuPoList = this.getMkuPoList(mkuIdList);


        Map<Long, List<MkuImageItemReadResp>> mkuImageMap = Maps.newHashMap();
        for (MkuPO mkuPO : mkuPoList) {
            List<MkuImageItemReadResp> itemList = Lists.newArrayList();
            MkuImageItemReadResp mkuImageItemReadResp = MkuConvert.INSTANCE.mkuPO2mkuImageItemRespDTO(mkuPO);
            itemList.add(mkuImageItemReadResp);
            completeDetailImage(mkuPO, itemList);
            mkuImageMap.put(mkuPO.getMkuId(), itemList);
        }

        List<MkuImageReadResp> mkuImageReadRespList = Lists.newArrayList();
        mkuImageMap.forEach((mkuId, imageList) -> mkuImageReadRespList.addAll(imageList.stream().map(item -> new MkuImageReadResp(mkuId, this.handleImageLang(reqDTO.getLangList(), item))).collect(Collectors.toList())));
        return DataResponse.success(mkuImageReadRespList);
    }

    private void completeDetailImage(MkuPO mkuPO, List<MkuImageItemReadResp> itemList) {
        if (StringUtils.isNotBlank(mkuPO.getDetailImg())) {
            String[] detailImageArr = mkuPO.getDetailImg().split(Constant.HASHTAG);
            final int len = detailImageArr.length;
            for (int i = 0; i < len; i++) {
                MkuImageItemReadResp temp = MkuConvert.INSTANCE.mkuPO2mkuImageItemRespDTO(mkuPO);
                temp.setMkuImage(detailImageArr[i]);
                temp.setIsPrimary(0);
                temp.setOrderSort(i + 2);
                itemList.add(temp);
            }
        }
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<MkuStateReadResp>> getMkuStateList(MkuStateReadReq reqDTO) {
        AssertValidation.isEmpty(reqDTO, DataResponseCode.PARAM_ERROR.getMessage());
        // 查询客户绑定商品关系
        List<Long> mkuIdList = this.getCustomerMkuIdList(Sets.newHashSet(reqDTO.getMkuIdList()), reqDTO.getClientCode());
        if (CollectionUtils.isEmpty(mkuIdList)) {
            // 没查到客制化绑定关系时，都返回状态为2
            List<MkuStateReadResp> readRespList = getWaitStatusList(reqDTO.getMkuIdList());
            return DataResponse.success(readRespList);
        }
        List<MkuPO> mkuPoList = this.getMkuPoList(mkuIdList);
        List<MkuStateReadResp> mkuStateReadRespList = MkuConvert.INSTANCE.mkuPO2mkuStateRespDTO(mkuPoList);
        mkuStateReadRespList.forEach(mkuStateReadResp -> mkuStateReadResp.setMkuStatus(MkuStatusEnum.ON_SALE.getStatus()));
        // 未查询到客户化信息的商品重新填补数据
        List<Long> subtractMkuIdList = (List<Long>) CollectionUtils.subtract(reqDTO.getMkuIdList(), mkuIdList);
        if (CollectionUtils.isNotEmpty(subtractMkuIdList)) {
            mkuStateReadRespList.addAll(this.getWaitStatusList(subtractMkuIdList));
        }
        return DataResponse.success(mkuStateReadRespList);
    }

    /**
     * 未查询到客制化信息时，将状态都设置为2，不可售
     *
     * @param reqDTOMkuIdList mkuId数组
     * @return mku状态数据
     */
    private List<MkuStateReadResp> getWaitStatusList(List<Long> reqDTOMkuIdList) {
        List<MkuPO> mkuPoList = this.getMkuPoList(reqDTOMkuIdList);
        mkuPoList.forEach(mkuPo -> mkuPo.setMkuStatus(MkuStatusEnum.WAIT_APPROVE.getStatus()));
        return MkuConvert.INSTANCE.mkuPO2mkuStateRespDTO(mkuPoList);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<CheckMkuSaleReadResp>> checkMkuSaleStateList(CheckMkuSaleReadReq reqDTO) {
        AssertValidation.isEmpty(reqDTO, DataResponseCode.PARAM_ERROR.getMessage());
        // 查询客户绑定商品关系
        List<Long> mkuIdList = this.getCustomerMkuIdList(Sets.newHashSet(reqDTO.getMkuIdList()), reqDTO.getClientCode());
        if (CollectionUtils.isEmpty(mkuIdList)) {
            // 没查到客制化绑定关系时，都返回状态为2
            return DataResponse.success(this.getCheckMkuSaleReadRespList(reqDTO.getMkuIdList(), reqDTO.getLangList()));
        }

        // 有客制化绑定关系的数据
        List<MkuPO> mkuPoList = this.getMkuPoList(mkuIdList);
        MkuOperateVO mkuOperateVO = new MkuOperateVO();
        mkuOperateVO.setMkuIds(Sets.newHashSet(mkuIdList));
        mkuOperateVO.setLang(Optional.of(reqDTO.getLangList().get(0)).orElse(LangConstant.LANG_ZH));

        Map<Long, MkuLangPO> mkuLangPoMap = mkuLangAtomicService.listLang(mkuOperateVO);
        List<CheckMkuSaleReadResp> checkMkuSaleReadRespList = MkuConvert.INSTANCE.mkuPOList2checkMkuSaleRespDTO(mkuPoList, mkuLangPoMap);

        // 查询MKU国家池状态
        CustomerVO customerVO = customerManageService.detail(reqDTO.getClientCode());
        List<MkuClientUpSaleApiDTO> mkuSaleStatList = countryMkuRpcService.checkUpSaleStatus(new CountryMkuCheckReqDTO(mkuIdList, customerVO.getCountry()));
        if (CollectionUtils.isNotEmpty(mkuSaleStatList)){
            Map<Long, MkuClientUpSaleApiDTO> mkuSaleStatusMap = mkuSaleStatList.stream().filter(Objects::nonNull).collect(Collectors.toMap(MkuClientUpSaleApiDTO::getMkuId, Function.identity()));
            MkuClientUpSaleApiDTO mkuClientUpSaleApiDTO = new MkuClientUpSaleApiDTO();
            mkuClientUpSaleApiDTO.setCountryMkuStatus(MkuStatusEnum.WAIT_APPROVE.getStatus());
            checkMkuSaleReadRespList.forEach(each -> each.setMkuStatus(mkuSaleStatusMap.getOrDefault(each.getMkuId(),mkuClientUpSaleApiDTO).getCountryMkuStatus()));
        }

        // 存在未绑定客制化绑定关系的mkuId
        List<Long> subtractMkuIdList = (List<Long>) CollectionUtils.subtract(reqDTO.getMkuIdList(), mkuIdList);
        if (CollectionUtils.isNotEmpty(subtractMkuIdList)) {
            checkMkuSaleReadRespList.addAll(this.getCheckMkuSaleReadRespList(subtractMkuIdList, reqDTO.getLangList()));
        }
        return DataResponse.success(checkMkuSaleReadRespList);
    }

    private List<CheckMkuSaleReadResp> getCheckMkuSaleReadRespList(List<Long> mkuIdList, List<String> langList) {
        // 查询mku信息
        List<MkuPO> mkuPoList = this.getMkuPoList(mkuIdList);
        // 查询mku多语言信息
        MkuOperateVO mkuOperateVO = new MkuOperateVO();
        mkuOperateVO.setMkuIds(Sets.newHashSet(mkuIdList));
        mkuOperateVO.setLang(Optional.of(langList.get(0)).orElse(LangConstant.LANG_ZH));
        Map<Long, MkuLangPO> mkuLangPoMap = mkuLangAtomicService.listLang(mkuOperateVO);
        // 转换为mku销售信息
        List<CheckMkuSaleReadResp> checkMkuSaleReadRespList = MkuConvert.INSTANCE.mkuPOList2checkMkuSaleRespDTO(mkuPoList, mkuLangPoMap);
        checkMkuSaleReadRespList.forEach(each -> each.setMkuStatus(MkuStatusEnum.WAIT_APPROVE.getStatus()));
        return checkMkuSaleReadRespList;
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<MkuDetailReadResp> getMkuDetail(MkuDetailReadReq reqDTO) {
        AssertValidation.isEmpty(reqDTO, DataResponseCode.PARAM_ERROR.getMessage());
        AssertValidation.isEmpty(reqDTO.getMkuId(), DataResponseCode.PARAM_ERROR.getMessage());
        final Long mkuId = reqDTO.getMkuId();

        // 查询客户绑定商品关系
        CustomerMkuPO customerMkuPO = customerMkuAtomicService.getOneValid(mkuId, reqDTO.getClientCode(), CustomerMkuBindEnum.BIND);
        if (customerMkuPO == null) {
            return DataResponse.success();
        }
        // 查询mku信息、商品名、品牌、类目属性、销售属性
        MkuPO mkuPo = mkuAtomicService.getPOById(mkuId);
        AssertValidation.isEmpty(mkuPo, DataResponseCode.DATA_NULL.getMessage());
        // 补充商品货源国编码
        reqDTO.setSourceCountryCode(mkuPo.getSourceCountryCode());
        List<String> langList = Optional.ofNullable(reqDTO.getLangList()).orElse(Lists.newArrayList(LangConstant.LANG_ZH));
        MkuDetailReadResp mkuDetailReadResp = new MkuDetailReadResp();
        mkuDetailReadResp.setMkuId(mkuId);
        mkuDetailReadResp.setImagePath(langList.stream().map(l -> new LangItemReadResp(l, mkuPo.getMainImg())).collect(Collectors.toList()));
        mkuDetailReadResp.setMkuStatus(MkuStatusEnum.ON_SALE.getStatus());
        mkuDetailReadResp.setLogisticsType(0);
        mkuDetailReadResp.setWeight(Objects.nonNull(mkuPo.getWeight()) ? mkuPo.getWeight().toString() : "");
        mkuDetailReadResp.setLength(Objects.nonNull(mkuPo.getLength()) ? mkuPo.getLength().toString() : "");
        mkuDetailReadResp.setHeight(Objects.nonNull(mkuPo.getHeight()) ? mkuPo.getHeight().toString() : "");
        mkuDetailReadResp.setWidth(Objects.nonNull(mkuPo.getWidth()) ? mkuPo.getWidth().toString() : "");

        CustomerVO customerVO = customerManageService.detail(customerMkuPO.getClientCode());

        try {
            // 海关编码，取跨境固定sku的海关编码
            CompletableFuture<Void> hsCodeFuture = CompletableFuture.runAsync(() -> this.handleSkuInfo(mkuDetailReadResp, customerMkuPO, langList, customerVO), coreTaskExecutor);
            // mku名称
            CompletableFuture<Void> mkuNameFuture = CompletableFuture.runAsync(() -> this.handleMkuName(mkuDetailReadResp, langList, mkuId, customerVO), coreTaskExecutor);
            // mku类目ID，使用逗号连接
            CompletableFuture<Void> categoryFuture = CompletableFuture.runAsync(() -> this.handleCategory(mkuDetailReadResp, mkuPo.getJdCatId()), coreTaskExecutor);
            // 品牌名
            CompletableFuture<Void> brandFuture = CompletableFuture.runAsync(() -> this.handleBrand(mkuDetailReadResp, langList, mkuPo.getBrandId()), coreTaskExecutor);
            // 扩展属性
//            CompletableFuture<Void> extendAttrFuture = CompletableFuture.runAsync(() -> this.handleExtendAttribute(mkuDetailReadResp, langList, mkuPo.getExtAttribute()), coreTaskExecutor);
            CompletableFuture<Void> extendAttrFuture = CompletableFuture.runAsync(() -> this.handleGroupExtendAttribute(mkuDetailReadResp, customerMkuPO, langList, mkuPo.getGroupExtAttribute()), coreTaskExecutor);
            // 销售属性

            CompletableFuture<Void> saleAttrFuture = CompletableFuture.runAsync(() -> this.handleSaleAttribute(mkuDetailReadResp, langList, mkuId, reqDTO.getClientCode()), coreTaskExecutor);
            // 根据入参查询pc详描、app详描
            CompletableFuture<Void> descFuture = CompletableFuture.runAsync(() -> this.handleDescription(reqDTO, mkuDetailReadResp), coreTaskExecutor);
            // 设置最小起售量
            CompletableFuture<Void> lowestBuyFuture = CompletableFuture.runAsync(() -> this.handleLowestBuy(reqDTO, mkuDetailReadResp), coreTaskExecutor);
            // 取回所有信息
            CompletableFuture.allOf(hsCodeFuture, mkuNameFuture, categoryFuture, brandFuture, extendAttrFuture, saleAttrFuture, descFuture, lowestBuyFuture).join();
        } catch (Exception e) {
            log.error("MkuReadApiServiceImpl.getMkuDetail 获取结果异常 reqDTO={}", JSON.toJSONString(reqDTO), e);
        }
        return DataResponse.success(mkuDetailReadResp);
    }

    /**
     * 处理扩展属性，将扩展属性按照规则拆分成属性id和属性值的关系。
     *
     * @param mkuDetailReadResp MkuDetailRespDTO对象，包含扩展属性列表。
     * @param langList          语言参数，用于查询属性的语言。
     * @param groupExtAttribute 扩展属性字符串。
     */
    private void handleGroupExtendAttribute(MkuDetailReadResp mkuDetailReadResp, CustomerMkuPO customerMkuPO, List<String> langList, String groupExtAttribute) {
        if (StringUtils.isBlank(groupExtAttribute)) {
            log.info("MkuReadApiServiceImpl.handleExtendAttribute 无扩展属性");
            return;
        }
        try {
            // 获取结构化扩展属性，属性组->属性list->属性值list
            List<ExtendPropertyGroupVO> extendPropertyGroupVOS = ExtendPropertyGroupVO.obtainExtendPropertyGroupVOList(groupExtAttribute);

            AttributeQueryReqDTO reqDTO = new AttributeQueryReqDTO();
            reqDTO.setCategoryId(customerMkuPO.getJdCatId());
            reqDTO.setAttributeTypeEnum(AttributeTypeEnum.EXTEND);
            reqDTO.setLangSet(new HashSet<>(langList));

            List<AttributeDTO> data = new ArrayList<>();
            try {
                // 获取类目下全部对应语音翻译的所有属性信息，包括属性组
                DataResponse<List<AttributeDTO>> listDataResponse = iscAttributeReadApiService.queryAttributeListByCatId(reqDTO);
                if (listDataResponse.getSuccess()) {
                    data = listDataResponse.getData();
                }
            } catch (Exception e) {
                log.error("【系统异常】IscAttributeReadApiService.queryAttributeListByCatId error:{}", e.getMessage(), e);
            } finally {
                log.info("IiscAttributeReadApiService.queryAttributeListByCatId res:{}", JSON.toJSONString(data));
            }
            // data转换为属性组id+属性id：属性实体map
            Map<String, AttributeDTO> attributeDTOMap = data.stream().collect(Collectors.toMap(e -> getKey(e.getId(), e.getComGroupId()), e -> e, (existing, replacement) -> existing));
            List<AttributeReadResp> extendAttributeReadRespList = Lists.newArrayList();

            // for循环遍历mku属性组，再次for循环遍历属性组下的属性值，依次for循环语音添加对应翻译
            for (ExtendPropertyGroupVO extendPropertyGroupVO : extendPropertyGroupVOS) {
                if (extendPropertyGroupVO.getExtendPropertyList() != null) {
                    for (ShotPropertyVO shotPropertyVO : extendPropertyGroupVO.getExtendPropertyList()) {
                        AttributeDTO attributeDTO = attributeDTOMap.get(getKey(shotPropertyVO.getAttributeId(), extendPropertyGroupVO.getComGroupId()));
                        // 获取到属性后，对属性名、属性值、属性组名进行多语言赋值
                        if (attributeDTO != null) {
                            for (String paramLang : langList) {
                                AttributeReadResp respDTO = new AttributeReadResp();
                                respDTO.setAttributeId(shotPropertyVO.getAttributeId());
                                respDTO.setLang(paramLang);
                                respDTO.setSort(attributeDTO.getSort());
                                // 设置属性多语言名
                                if(attributeDTO.getLangList() !=null){
                                    respDTO.setAttributeName(attributeDTO.getLangList().stream().filter(e -> paramLang.equals(e.getLang())).map(e -> e.getLangName()).findFirst().orElse(""));
                                }
                                respDTO.setShield(attributeDTO.getShield());
                                respDTO.setIsQuJianZhi(attributeDTO.getIsQuJianZhi());
                                respDTO.setComGroupId(attributeDTO.getComGroupId());
                                respDTO.setLevel(attributeDTO.getLevel());
                                respDTO.setRemark(attributeDTO.getRemark());

                                // 设置属性组多语言名
                                if(attributeDTO.getLangComGroupNameMap() !=null ){
                                    respDTO.setComGroupName(attributeDTO.getLangComGroupNameMap().get(paramLang));
                                }
                                // 设置属性值多语言名
                                List<AttributeValueDTO> attributeValueList = attributeDTO.getAttributeValueList();
                                if(attributeValueList !=null){
                                    Map<Long, Map<String, String>> attributeValueLangMap = new HashMap<>();  // 属性值id-》lang：langname
                                    // 组装属性值id与属性值对象的map，因为原对象把属性值id放到了多语言list中了，反而属性组对象没有valueId！
                                    for (AttributeValueDTO attributeValueDTO : attributeValueList) {
                                        List<AttributeValueLangDTO> attributeValueDTOLangList = attributeValueDTO.getLangList();
                                        if(attributeValueDTOLangList!=null && !attributeValueDTOLangList.isEmpty()){
                                            Map<String, String> langMap = attributeValueDTOLangList.stream().collect(Collectors.toMap(e -> e.getLang(), e -> e.getLangName(), (existing, replacement) -> existing));
                                            attributeValueLangMap.put(attributeValueDTOLangList.get(0).getAttributeValueId(), langMap);
                                        }
                                    }
                                    if (shotPropertyVO.getPropertyValueVOList() != null) {
                                        for (ShotPropertyValueVO shotPropertyValueVO : shotPropertyVO.getPropertyValueVOList()) {
                                            // 为每一个属性值赋值多语言值
                                            if (StringUtils.isNotBlank(shotPropertyValueVO.getLang())) { // 代表这是文本属性
                                                String lang = shotPropertyValueVO.getLang();
                                                String value = shotPropertyValueVO.getAttributeValueName();
                                                if(StringUtils.isNotBlank(value) && paramLang.equals(lang)){
                                                    List<String> attributeValueLangList = new ArrayList<>();
                                                    attributeValueLangList.add(value);
                                                    respDTO.setAttributeValueList(attributeValueLangList);
                                                }
                                            } else if (shotPropertyValueVO.getAttributeValueId() != null) { // 代表单选或多选
                                                Long valueId = shotPropertyValueVO.getAttributeValueId();
                                                Map<String, String> langMap = attributeValueLangMap.get(valueId);
                                                List<String> attributeValueLangList = new ArrayList<>();
                                                if(langMap!=null){
                                                    String value = langMap.get(paramLang);
                                                    if(StringUtils.isNotBlank(value)){
                                                        attributeValueLangList.add(value);
                                                    }
                                                }
                                                respDTO.setAttributeValueList(attributeValueLangList);
                                            }
                                        }
                                    }
                                }
                                extendAttributeReadRespList.add(respDTO);
                            }
                        }
                    }
                }
            }
            mkuDetailReadResp.setExtendAttrList(extendAttributeReadRespList);
        } catch (Exception e) {
            log.error("MkuReadApiServiceImpl.handleExtendAttribute 处理扩展属性异常", e);
        }
    }

    private static String getKey(Long id, Integer groupId) {
        return id + COLON + groupId;
    }

    private void handleLowestBuy(MkuDetailReadReq reqDTO, MkuDetailReadResp mkuDetailReadResp) {
        // 查询mkuId和jdSkuId关系
        final Long mkuId = reqDTO.getMkuId();
        CustomerVO customerVO = customerManageService.detail(reqDTO.getClientCode());
        MkuRefReadReq readReq = new MkuRefReadReq();
        readReq.setMkuIdList(Sets.newHashSet(reqDTO.getMkuId()));
        readReq.setClientCode(reqDTO.getClientCode());
        Map<Long, CustomerMkuPricePO> customerMkuPriceMap = this.queryMkuCustomerMkuPriceMap(readReq);

        CustomerMkuPricePO customerMkuPricePO = customerMkuPriceMap.get(reqDTO.getMkuId());
        // 查询商品销售信息
        SkuFeatureVO feature = skuFeatureManageService.getSkuFeatureBySkuId(customerMkuPricePO.getFixedSkuId(), customerVO.getCountry());

        if (!CountryConstant.COUNTRY_ZH.equals(reqDTO.getSourceCountryCode())) {
            if (MapUtils.isNotEmpty(customerMkuPriceMap)) {
/*                    SkuPO skuPo = skuAtomicService.getSkuPo(customerMkuPricePO.getFixedSkuId());
            if (Objects.nonNull(skuPo) && Objects.nonNull(skuPo.getMoq())) {
                    // 默认为1
                    mkuDetailReadResp.setLowestBuy(skuPo.getMoq());
                } else {
                    // 默认为1
                    mkuDetailReadResp.setLowestBuy(1);
                }*/

                // 非备货品返回发货周期
                if (Objects.nonNull(feature) && Objects.nonNull(feature.getProductionCycle()) && (Objects.isNull(feature.getPurchaseModel()) || ZERO == feature.getPurchaseModel())) {
                    mkuDetailReadResp.setDeliveryTime(feature.getProductionCycle().longValue());
                }

/*                // 备货品默认最小起订量1
                if (null != feature &&  Objects.nonNull(feature.getPurchaseModel()) && ZERO != feature.getPurchaseModel()) {
                    mkuDetailReadResp.setLowestBuy(1);
                }*/
            }
            // 危险品：跨境去国内的，本土默认否
            mkuDetailReadResp.setDangerousGoods(YnEnum.NO.getCode());
        } else {
            MkuRefReadReq req = new MkuRefReadReq();
            req.setSourceCountryCode(reqDTO.getSourceCountryCode());
            req.setClientCode(reqDTO.getClientCode());
            req.setMkuIdList(Sets.newHashSet(mkuId));
            DataResponse<Map<Long, Long>> jdSkuAndMkuRelationResponse = this.getJdSkuByMkuIds(req);

            if (Boolean.TRUE.equals(jdSkuAndMkuRelationResponse.getSuccess()) && MapUtils.isNotEmpty(customerMkuPriceMap)
                    && jdSkuAndMkuRelationResponse.getData().containsKey(mkuId)) {
                Map<Long, Long> relationMap = jdSkuAndMkuRelationResponse.getData();
                Long jdSkuId = relationMap.get(mkuId);
                // 查询国内工业商品信息
                JdProductDTO jdSku = skuInfoRpcService.getSkuById(jdSkuId);

                if (Objects.nonNull(jdSku)) {
                    //mkuDetailReadResp.setLowestBuy(StringUtils.isBlank(jdSku.getLowestBuy()) || "0".equals(jdSku.getLowestBuy()) ? 1 : Integer.parseInt(jdSku.getLowestBuy()));
                    mkuDetailReadResp.setDangerousGoods(Objects.nonNull(jdSku.getDangerous()) ? BooleanUtils.toInteger(jdSku.getDangerous()) : YnEnum.NO.getCode());

                    // 查询发货时效
                    // 非备货品返回发货周期
                    if (Objects.isNull(feature) || Objects.isNull(feature.getPurchaseModel()) || ZERO == feature.getPurchaseModel()) {
                        // 查询国内商品履约时间
                        SkuDetailedPageInfoResp promiseInfo = promiseManageService.getPromiseInfoByJdSkuId(new JdSkuDetailPromiseReqVO(jdSkuId, 1, reqDTO.getClientCode()));
                        // 国内发货时效
                        mkuDetailReadResp.setDeliveryTime(Objects.nonNull(promiseInfo) && Objects.nonNull(promiseInfo.getDeliveryDays()) ? promiseInfo.getDeliveryDays().longValue() : null);
                    }

                    // 备货品默认最小起订量1
/*                    if (null != feature &&  Objects.nonNull(feature.getPurchaseModel()) && ZERO != feature.getPurchaseModel()) {
                        mkuDetailReadResp.setLowestBuy(1);
                    }*/
                } else {
                    // 默认为1
                    //mkuDetailReadResp.setLowestBuy(1);
                    // 危险品：跨境去国内的，本土默认否
                    mkuDetailReadResp.setDangerousGoods(YnEnum.NO.getCode());
                }
            } else {
                // 默认为1
                //mkuDetailReadResp.setLowestBuy(1);
                // 危险品：跨境去国内的，本土默认否
                mkuDetailReadResp.setDangerousGoods(YnEnum.NO.getCode());
            }
        }
        Integer lowestBuy = Objects.nonNull(feature) && Objects.nonNull(feature.getMoq()) ? feature.getMoq() : ONE;
        mkuDetailReadResp.setLowestBuy(lowestBuy);
    }

    /**
     * 设置海关编码
     *
     * @param mkuDetailReadResp
     * @param customerMkuPO
     */
    private void handleSkuInfo(MkuDetailReadResp mkuDetailReadResp, CustomerMkuPO customerMkuPO, List<String> langList, CustomerVO customerVO) {
        MkuPriceReqVO mkuPriceReqVO = new MkuPriceReqVO();
        mkuPriceReqVO.setMkuIdList(Sets.newHashSet(customerMkuPO.getMkuId()));
        mkuPriceReqVO.setClientCode(customerMkuPO.getClientCode());
        Map<Long, Long> mkuSkuMap = mkuRelationService.queryFixSkuIdByIscMkuId(mkuPriceReqVO);
        if (MapUtils.isNotEmpty(mkuSkuMap)) {
            Long skuId = mkuSkuMap.get(customerMkuPO.getMkuId());
            SkuPO skuPo = skuAtomicService.getSkuPo(skuId);
            // 查询客制化进口海关编码和税率
            CustomerSkuTaxRatePO skuTaxRatePO = customerSkuTaxRateAtomicService.getPoBySkuId(skuId, customerVO.getCountry(), customerMkuPO.getClientCode());
            if (Objects.nonNull(skuTaxRatePO) && StringUtils.isNotBlank(skuTaxRatePO.getHsCode())) {
                mkuDetailReadResp.setHsCode(skuTaxRatePO.getHsCode());
            } else {
                // 查询供应商海关编码 不能取出口海关编码
                //mkuDetailReadResp.setHsCode(Objects.nonNull(skuPo) ? skuPo.getHsCode() : "");
            }
            // SKU 跨境属性
            if (StringUtils.isNotBlank(skuPo.getSkuInterAttribute())) {
                Map<String, List<String>> interAttributeMap = spuConvertService.splitInterAttributeStr(skuPo.getSkuInterAttribute());
                // 原产地
                if (interAttributeMap.containsKey(SKU_ORIGIN_COUNTRY) && CollectionUtils.isNotEmpty(interAttributeMap.get(SKU_ORIGIN_COUNTRY))) {
                    String functionStr = interAttributeMap.get(SKU_ORIGIN_COUNTRY).get(0);
                    mkuDetailReadResp.setOriginCountry(StringUtils.isNotBlank(functionStr) ? functionStr.substring((LangConstant.LANG_ZH + FACTORIAL).length()) : "");
                }
            }
            if (StringUtils.isNotBlank(skuPo.getSourceCountryCode())) {
                mkuDetailReadResp.setProductSourceCode(skuPo.getSourceCountryCode());
            }

//            SkuFeaturePO skuFeaturePO = skuFeatureAtomicService.getSkuFeatureBySkuId(skuId);
//            mkuDetailReadResp.setSkuModel("No Model");
//            if (Objects.nonNull(skuFeaturePO)) {
            // 原产国取sku上的原产地
            //mkuDetailReadResp.setOriginCountry(skuFeaturePO.getOriginCountry());
            // 产品型号是取sku上面的型号,没有默认 No Model
//                mkuDetailReadResp.setSkuModel(StringUtils.isNotBlank(skuFeaturePO.getSpecification()) ? skuFeaturePO.getSpecification() : "No Model");
//            }
            // 净重就取现在的weight
            mkuDetailReadResp.setNetWeight(skuPo.getWeight().toPlainString());
            // 非跨境品没有跨境属性
            // SPU
            SpuPO spuPo = spuAtomicService.getSpuPoBySpuId(skuPo.getSpuId());
            mkuDetailReadResp.setSkuModel(StringUtils.isNotBlank(spuPo.getSpecification()) ? spuPo.getSpecification() : "No Model");

            // 销售单位
            this.handleSaleUnit(mkuDetailReadResp, langList, spuPo, customerVO.getClientCode());

            if (!CountryConstant.COUNTRY_ZH.equals(skuPo.getSourceCountryCode())) {
                return;
            }

            Map<String, List<String>> interAttributeMap = spuConvertService.splitInterAttributeStr(spuPo.getSpuInterAttribute());
            if (MapUtils.isEmpty(interAttributeMap)) {
                mkuDetailReadResp.setClassificationElements(null);
            } else {
                StringBuilder element = new StringBuilder();
                // 功能
                if (interAttributeMap.containsKey(PRODUCT_FUNCTION) && CollectionUtils.isNotEmpty(interAttributeMap.get(PRODUCT_FUNCTION))) {
                    String functionStr = interAttributeMap.get(PRODUCT_FUNCTION).get(0);
                    element.append(StringUtils.isNotBlank(functionStr) ? functionStr.substring((LangConstant.LANG_ZH + FACTORIAL).length()) : "").append(" ");
                }
                // 用途
                if (interAttributeMap.containsKey(PRODUCT_USE) && CollectionUtils.isNotEmpty(interAttributeMap.get(PRODUCT_USE))) {
                    String functionStr = interAttributeMap.get(PRODUCT_USE).get(0);
                    element.append(StringUtils.isNotBlank(functionStr) ? functionStr.substring((LangConstant.LANG_ZH + FACTORIAL).length()) : "").append(" ");
                }
                // 原理
                if (interAttributeMap.containsKey(PRODUCT_MATERIAL) && CollectionUtils.isNotEmpty(interAttributeMap.get(PRODUCT_MATERIAL))) {
                    String functionStr = interAttributeMap.get(PRODUCT_MATERIAL).get(0);
                    element.append(StringUtils.isNotBlank(functionStr) ? functionStr.substring((LangConstant.LANG_ZH + FACTORIAL).length()) : "").append(" ");
                }
                // 材质
                if (interAttributeMap.containsKey(PRODUCT_PRINCIPLE) && CollectionUtils.isNotEmpty(interAttributeMap.get(PRODUCT_PRINCIPLE))) {
                    String functionStr = interAttributeMap.get(PRODUCT_PRINCIPLE).get(0);
                    element.append(StringUtils.isNotBlank(functionStr) ? functionStr.substring((LangConstant.LANG_ZH + FACTORIAL).length()) : "");
                }
                String elementClassfication = element.toString();
                mkuDetailReadResp.setClassificationElements(StringUtils.isNotBlank(elementClassfication) ? elementClassfication : null);
            }
        }
    }

    /**
     * 处理销售单位的多语言信息。
     *
     * @param mkuDetailReadResp MKU详细信息读取响应对象。
     * @param langList          语言列表。
     * @param spuPo             SPU采购订单对象。
     */
    private void handleSaleUnit(MkuDetailReadResp mkuDetailReadResp, List<String> langList, SpuPO spuPo, String clientCode) {
        if (Objects.isNull(spuPo.getSaleUnit())) {
            return;
        }
        // 国际销售单位编码
        Integer saleUnit = spuPo.getSaleUnit();

        CustomerVO customerVO = customerManageService.detail(clientCode);

        // 1、先查询MKU+合同号绑定的类目和销售单位是否存在，有则返回
        ReflectDataDto req = new ReflectDataDto();
        req.setQueryKey(String.valueOf(mkuDetailReadResp.getMkuId()));
        req.setReflectType(ReflectTypeEnum.SKU_CATE.getType());
        ReflectDataDto reflectDataDto = getReflectDataDto(req, customerVO);
        if (Objects.nonNull(reflectDataDto) && StringUtils.isNotBlank(reflectDataDto.getResultExt())) {
            JSONObject jsonObject = JSON.parseObject(reflectDataDto.getResultExt());
            String saleUnitStr = jsonObject.getString(CUSTOMER_SALE_UNIT);
            if (StringUtils.isNotBlank(saleUnitStr)) {
                List<LangItemReadResp> saleUnitList = getLangItemReadRespsBySaleUnitStr(langList, saleUnitStr);
                mkuDetailReadResp.setSaleUnit(saleUnitList);
                return;
            }
        }

        // 2、根据国际销售单位编码+合同编号查询对应客户销售单位映射
        req.setQueryKey(String.valueOf(saleUnit));
        req.setReflectType(ReflectTypeEnum.OTHER.getType());
        // 销售单位
        req.setOtherType(KAI_YANG_UNIT_TYPE);
        reflectDataDto = getReflectDataDto(req, customerVO);

        if (Objects.nonNull(reflectDataDto) && StringUtils.isNotBlank(reflectDataDto.getResultExt())) {
            JSONObject jsonObject = JSON.parseObject(reflectDataDto.getResultExt());
            String saleUnitStr = jsonObject.getString(CUSTOMER_OTHER_SALE_UNIT);
            if (StringUtils.isNotBlank(saleUnitStr)) {
                List<LangItemReadResp> saleUnitList = getLangItemReadRespsBySaleUnitStr(langList, saleUnitStr);
                mkuDetailReadResp.setSaleUnit(saleUnitList);
                return;
            }
        }

        // 3、开阳系统没有销售单位映射，则查询客户销售单位配置
        if (CollectionUtils.isNotEmpty(bydClients) && bydClients.contains(clientCode)) {
            Map<String, String> bydSaleUnitMap = extendInfoService.queryBydSaleUnitByCodeAndLangList(saleUnit, langList);
            if (MapUtils.isNotEmpty(bydSaleUnitMap)) {
                List<LangItemReadResp> saleUnitReadRespList = getLangItemReadResps(bydSaleUnitMap);
                mkuDetailReadResp.setSaleUnit(saleUnitReadRespList);
            } else {
                // 不在映射中，兜底个
                Map<String, String> pieceUnitMap = extendInfoService.queryBydSaleUnitByCodeAndLangList(ONE, langList);
                List<LangItemReadResp> saleUnitReadRespList = getLangItemReadResps(pieceUnitMap);
                mkuDetailReadResp.setSaleUnit(saleUnitReadRespList);
            }
        } else {
            //  销售单位转多语言列表
            Map<String, String> saleUnitMap = extendInfoService.querySaleUnitByCodeAndLangList(saleUnit, langList);
            if (MapUtils.isNotEmpty(saleUnitMap)) {
                List<LangItemReadResp> saleUnitReadRespList = getLangItemReadResps(saleUnitMap);
                mkuDetailReadResp.setSaleUnit(saleUnitReadRespList);
            }
        }
    }

    private ReflectDataDto getReflectDataDto(ReflectDataDto req, CustomerVO customerVO) {
        req.setContractNo(customerVO.getContractCode());
        return iscClientCategoryRelationRpcService.queryReflectData(req);
    }

    private List<LangItemReadResp> getLangItemReadRespsBySaleUnitStr(List<String> langList, String saleUnitStr) {
        List<LangItemReadResp> saleUnitList = Lists.newArrayList();
        langList.forEach(lang -> saleUnitList.add(new LangItemReadResp(lang, saleUnitStr)));
        return saleUnitList;
    }


    private List<LangItemReadResp> getLangItemReadResps(Map<String, String> pieceUnitMap) {
        List<LangItemReadResp> saleUnitReadRespList = Lists.newArrayList();
        pieceUnitMap.forEach((k, v) -> saleUnitReadRespList.add(new LangItemReadResp(k, v)));
        return saleUnitReadRespList;
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<CheckMkuAreaLimitReadResp>> checkMkuAreaLimitList(CheckMkuAreaLimitReadReq reqDTO) {
        AssertValidation.isEmpty(reqDTO, DataResponseCode.PARAM_ERROR.getMessage());
        AssertValidation.isEmpty(reqDTO.getMkuAreaBaseInfoReadReq(), DataResponseCode.PARAM_ERROR.getMessage());
        // 查询客户绑定商品关系
        List<Long> mkuIdList = this.getCustomerMkuIdList(Sets.newHashSet(reqDTO.getMkuIdList()), reqDTO.getClientCode());
        if (CollectionUtils.isEmpty(mkuIdList)) {
            return DataResponse.error(DataResponseCode.DATA_NULL.getCode(), String.format("未查询到客户 %s 绑定的商品信息", reqDTO.getClientCode()));
        }

        // 查询客制化价格
        MkuRefReadReq mkuRefReadReq = new MkuRefReadReq();
        mkuRefReadReq.setMkuIdList(Sets.newHashSet(reqDTO.getMkuIdList()));
        mkuRefReadReq.setClientCode(reqDTO.getClientCode());
        mkuRefReadReq.setSourceCountryCode(reqDTO.getSourceCountryCode());
        List<CustomerMkuPricePO> customerMkuPricePOList = this.getCustomerMkuPricePOList(mkuRefReadReq);

        if (CollectionUtils.isEmpty(customerMkuPricePOList)) {
            return DataResponse.error(DataResponseCode.DATA_NULL.getCode(), String.format("未查询到客户 %s 的商品信息", reqDTO.getClientCode()));
        }

        // 没有跨境品时，直接返回不限制
        boolean crossBorderMku = customerMkuPricePOList.stream().anyMatch(po -> CountryConstant.COUNTRY_ZH.equals(po.getSourceCountryCode()));
        if (!crossBorderMku) {
            List<CheckMkuAreaLimitReadResp> checkMkuAreaLimitReadRespList = Lists.newArrayList();
            reqDTO.getMkuIdList().forEach(mkuId -> {
                CheckMkuAreaLimitReadResp limitReadResp = new CheckMkuAreaLimitReadResp();
                limitReadResp.setMkuId(mkuId);
                limitReadResp.setAreaRestrict(Boolean.FALSE);
                checkMkuAreaLimitReadRespList.add(limitReadResp);
            });
            return DataResponse.success(checkMkuAreaLimitReadRespList);
        }


        Set<Long> localMkuIdList = Sets.newHashSet();
        Map<Long, Long> mkuFixedSkuMap = Maps.newHashMap();
        customerMkuPricePOList.forEach(pricePo -> {
            if (CountryConstant.COUNTRY_ZH.equals(pricePo.getSourceCountryCode())) {
                mkuFixedSkuMap.put(pricePo.getMkuId(), pricePo.getFixedSkuId());
            } else {
                localMkuIdList.add(pricePo.getMkuId());
            }
        });

        // 本土品直接返回不限制
        List<CheckMkuAreaLimitReadResp> checkMkuAreaLimitReadRespList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(localMkuIdList)) {
            localMkuIdList.forEach(mkuId -> {
                CheckMkuAreaLimitReadResp limitReadResp = new CheckMkuAreaLimitReadResp();
                limitReadResp.setMkuId(mkuId);
                limitReadResp.setAreaRestrict(Boolean.FALSE);
                checkMkuAreaLimitReadRespList.add(limitReadResp);
            });
        }

        // 查询客户信息
        CustomerVO customerVO = customerManageService.detail(reqDTO.getClientCode());
        // 查询商品属性，备货模式
        Map<Long, SkuFeatureVO> skuFeatureMap = skuFeatureManageService.querySkuFeatureMap(Sets.newHashSet(mkuFixedSkuMap.values()), customerVO.getCountry());
        // 如果有备货 直接返回，不校验区域限售
        if (MapUtils.isNotEmpty(skuFeatureMap)) {
            // 固定sku和mku映射
            Map<Long, Long> fixedSkuMkuMap = MapUtils.invertMap(mkuFixedSkuMap);
            // 查询sku限制区域
            skuFeatureMap.forEach((skuId, feature) -> {
                // 备货品不受区域限制
                if (Objects.nonNull(feature) &&  ZERO != feature.getPurchaseModel()) {
                    CheckMkuAreaLimitReadResp readResp = new CheckMkuAreaLimitReadResp();
                    Long mkuId = fixedSkuMkuMap.get(skuId);
                    readResp.setMkuId(mkuId);
                    readResp.setAreaRestrict(Boolean.FALSE);
                    checkMkuAreaLimitReadRespList.add(readResp);
                    // 移除备货模式的商品
                    mkuFixedSkuMap.remove(mkuId);
                }
            });
        }

        // 商品都是备货模式时，直接返回
        if (MapUtils.isEmpty(mkuFixedSkuMap)) {
            return DataResponse.success(checkMkuAreaLimitReadRespList);
        }

        if (MapUtils.isEmpty(mkuFixedSkuMap)) {
            return DataResponse.error(DataResponseCode.DATA_NULL.getCode(), String.format("未查询到客户 %s 的国内商品信息", reqDTO.getClientCode()));
        }

        QuerySkuAvailableSaleReqDTO saleReqDTO = new QuerySkuAvailableSaleReqDTO();
        saleReqDTO.setSkuIds(Sets.newHashSet(mkuFixedSkuMap.values()));
        saleReqDTO.setClientCode(reqDTO.getClientCode());
        Map<Long, SkuAvailableSaleResDTO> saleResDTOMap = skuAreaLimitRpcService.queryStandardSkuAreaLimit(saleReqDTO);
        if (MapUtils.isEmpty(saleResDTOMap)) {
            return DataResponse.error(DataResponseCode.WARNING.getCode(), "商品不满足区域售卖条件");
        }

        Map<Long, Boolean> skuLimitMap = Maps.newHashMap();
        saleResDTOMap.forEach((skuId, saleResult) -> {
            // WIOP接口限售true，可售false  AvailableSaleService.isSkuAvailableSale接口 true：不限售 false：限售
            skuLimitMap.put(saleResult.getSkuId(), !saleResult.getIsAvailableSale());
        });

        mkuFixedSkuMap.forEach((mkuId, skuId) -> {
            CheckMkuAreaLimitReadResp readResp = new CheckMkuAreaLimitReadResp();
            readResp.setMkuId(mkuId);
            readResp.setAreaRestrict(skuLimitMap.get(skuId));
            checkMkuAreaLimitReadRespList.add(readResp);
        });
        return DataResponse.success(checkMkuAreaLimitReadRespList);
    }


    /**
     * 处理图片多语言的方法。
     *
     * @param langList 语言列表，可以为null
     * @param item     图片项
     * @return 处理后的图片项列表
     */
    private List<MkuImageItemReadResp> handleImageLang(List<String> langList, MkuImageItemReadResp item) {
        return Optional.ofNullable(langList).orElseGet(ArrayList::new).stream().map(lang -> {
            MkuImageItemReadResp bean = BeanUtil.toBean(item, MkuImageItemReadResp.class);
            bean.setLang(lang);
            return bean;
        }).collect(Collectors.toList());
    }


    /**
     * 处理类目的方法
     *
     * @param mkuDetailReadResp MkuDetailRespDTO对象，包含了处理后的类目信息
     * @param catId             类别ID，用于查询类目路径
     */

    private void handleCategory(MkuDetailReadResp mkuDetailReadResp, Long catId) {
        List<CategoryComboBoxVO> categoryComboBoxVOList = categoryOutService.queryPath(catId, LangConstant.LANG_ZH);
        List<String> categoryNameList = Optional.ofNullable(categoryComboBoxVOList).orElseGet(ArrayList::new).stream().sorted(Comparator.comparing(CategoryComboBoxVO::getLevel)).map(boxVo -> String.valueOf(boxVo.getId())).collect(Collectors.toList());
        mkuDetailReadResp.setCategory(CollectionUtils.isNotEmpty(categoryNameList) ? String.join(Constant.COMMA, categoryNameList) : "");
    }

    /**
     * 处理Mku名称的方法
     *
     * @param mkuDetailReadResp Mku详情响应DTO对象
     * @param langList          语言参数
     * @param mkuId             Mku的ID
     */
    private void handleMkuName(MkuDetailReadResp mkuDetailReadResp, List<String> langList, Long mkuId, CustomerVO customerVO) {
        Map<String, MkuLangPO> mkuLangMap = mkuLangAtomicService.listLangByMkuId(mkuId);
        if (CollectionUtils.isNotEmpty(langList) && langList.contains(LangConstant.LANG_EN)
                && (!mkuLangMap.containsKey(LangConstant.LANG_EN) || StringUtils.isBlank(mkuLangMap.get(LangConstant.LANG_EN).getMkuTitle()))) {
            log.error("MkuReadApiServiceImpl.handleMkuName 商品ID:{},英文名为空。langList={}", mkuId, JSON.toJSONString(langList));
            Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_MKU_EN_TITLE_WARNING, String.format("%s mkuId=%s 英文名称为空", LevelCode.P0.getMessage(), mkuId));
        }
        List<LangItemReadResp> langMkuTitleList = new ArrayList<>();
        List<LangItemReadResp> langSubtitleList = new ArrayList<>();

        //如果是巴西的客户,标题优先从开阳平台获取,获取不到降级取原有标题
        String brEnSubTitle = null;
        if (CountryConstant.COUNTRY_BR.equals(customerVO.getCountry())) {
            JSONObject kaiYangData = getKaiYangData(mkuDetailReadResp.getMkuId(), ReflectTypeEnum.SKU_CATE.getType(), customerVO);
            brEnSubTitle = kaiYangData.getString(CUSTOMER_EN_SUB_TITLE);
        }

        for (String lang : langList) {
            if (mkuLangMap.containsKey(lang)) {
                MkuLangPO mkuLangPO = mkuLangMap.getOrDefault(lang, new MkuLangPO());
                LangItemReadResp langMkuTitle = new LangItemReadResp(lang, mkuLangPO.getMkuTitle());
                //巴西英文长标题优先从开阳获取
                if (CountryConstant.COUNTRY_BR.equals(customerVO.getCountry()) && LangConstant.LANG_EN.equals(lang)) {
                    langMkuTitleList.add(StringUtils.isBlank(brEnSubTitle) ? langMkuTitle : new LangItemReadResp(lang, brEnSubTitle));
                } else {
                    langMkuTitleList.add(langMkuTitle);
                }
                if (LangConstant.LANG_BR.equals(lang)) {
                    if (StringUtils.isNotBlank(mkuLangPO.getMkuSubtitle()) && mkuLangPO.getMkuSubtitle().length() <= 40) {
                        langSubtitleList.add(new LangItemReadResp(lang, mkuLangPO.getMkuSubtitle()));
                    }

                } else {
                    langSubtitleList.add(new LangItemReadResp(lang, mkuLangPO.getMkuSubtitle()));
                }
            }
        }
        mkuDetailReadResp.setMkuName(langMkuTitleList);
        mkuDetailReadResp.setMkuSubtitle(langSubtitleList);
    }

    /**
     * 从开阳映射获取数据
     */
    private JSONObject getKaiYangData(Long mkuId, String type, CustomerVO customerVO) {
        ReflectDataDto req = new ReflectDataDto();
        req.setQueryKey(String.valueOf(mkuId));
        req.setReflectType(type);
        ReflectDataDto reflectDataDto = getReflectDataDto(req, customerVO);
        if (Objects.nonNull(reflectDataDto) && StringUtils.isNotBlank(reflectDataDto.getResultExt())) {
            return JSON.parseObject(reflectDataDto.getResultExt());
        }
        return new JSONObject();
    }

    /**
     * 处理描述信息的方法
     *
     * @param reqDTO            MkuDetailReqDTO对象，包含请求参数
     * @param mkuDetailReadResp MkuDetailRespDTO对象，包含响应参数
     */
    private void handleDescription(MkuDetailReadReq reqDTO, MkuDetailReadResp mkuDetailReadResp) {
        List<String> langList = reqDTO.getLangList();
        if (CollectionUtils.isNotEmpty(reqDTO.getExtSet())) {
            Map<String, MkuDescLangPO> langDescMap = mkuDescLangAtomicService.listDescLangByIdAndLang(reqDTO.getMkuId(), langList);
            if (MapUtils.isEmpty(langDescMap)) {
                return;
            }

            if (reqDTO.getExtSet().contains(DescriptionConstant.PC)) {
                mkuDetailReadResp.setPcDescription(langList.stream().filter(Objects::nonNull).filter(langDescMap::containsKey).filter(l -> StringUtils.isNotBlank(langDescMap.get(l).getPcDescription())).map(l -> new LangItemReadResp(l, langDescMap.getOrDefault(l, new MkuDescLangPO()).getPcDescription())).collect(Collectors.toList()));
            }
            if (reqDTO.getExtSet().contains(DescriptionConstant.APP)) {
                mkuDetailReadResp.setAppDescription(langList.stream().filter(Objects::nonNull).filter(lang -> StringUtils.isNotBlank(langDescMap.get(lang).getAppDescription())).map(l -> new LangItemReadResp(l, langDescMap.getOrDefault(l, new MkuDescLangPO()).getAppDescription())).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 处理品牌信息
     *
     * @param mkuDetailReadResp MkuDetailRespDTO对象，包含品牌信息
     * @param langList          语言信息
     * @param brandId           品牌ID
     */
    private void handleBrand(MkuDetailReadResp mkuDetailReadResp, List<String> langList, Long brandId) {
        Map<String, String> brandMap = brandOutService.queryBrandNameByIdAndLangs(brandId, langList);
        List<LangItemReadResp> langItemReadRespList = Lists.newArrayList();
        brandMap.forEach((k, v) -> langItemReadRespList.add(new LangItemReadResp(k, v)));
        mkuDetailReadResp.setBrandName(langItemReadRespList);
    }

    /**
     * 获取MkuPO列表的方法。
     *
     * @param mkuIdList MkuId列表，用于获取MkuPO列表。
     * @return MkuPO列表。
     * @throws ServiceException 如果reqVo为空或mkuIdList为空，抛出ServiceException。
     */
    private List<MkuPO> getMkuPoList(List<Long> mkuIdList) {
        AssertValidation.isEmpty(mkuIdList, DataResponseCode.DATA_NULL.getMessage());
        AssertValidation.isTrue(mkuIdList.size() > SIZE, DataResponseCode.PARAM_ERROR.getMessage());
        List<MkuPO> mkuPoList = mkuAtomicService.listByMkuIds(mkuIdList);
        AssertValidation.isEmpty(mkuPoList, DataResponseCode.DATA_NULL.getMessage());
        return mkuPoList;
    }

    /**
     * 处理销售属性
     *
     * @param mkuDetailReadResp MkuDetailRespDTO对象，包含销售属性信息
     * @param langList          语言参数，用于查询属性信息的语言设置
     */
    private void handleSaleAttribute(MkuDetailReadResp mkuDetailReadResp, List<String> langList, Long mkuId, String clientCode) {
        if (mkuId==null || StringUtils.isBlank(clientCode) ) {
            log.info("MkuReadApiServiceImpl.handleSaleAttribute mkuId or clientCode is null");
            return;
        }
        BatchQueryMkuLangsReqDTO dto = new BatchQueryMkuLangsReqDTO();
        dto.setMkuId(Sets.newHashSet(mkuId));
        dto.setQueryEnum(Sets.newHashSet(MkuQueryLangsEnum.SELL_ATTRIBUTE));
        dto.setClientCode(clientCode);
        dto.setLangs(new HashSet<>(langList));
        DataResponse<Map<Long, IscMkuLangsResDTO>> iscMkuLangs = iscProductSoaMkuReadApiService.getIscMkuLangs(dto);
        if (iscMkuLangs == null || iscMkuLangs.getData() == null || iscMkuLangs.getData().isEmpty()) {
            log.info("MkuReadApiServiceImpl.handleSaleAttribute iscMkuLangs is null");
            return;
        }
        IscMkuLangsResDTO mkuLangsResDTO = iscMkuLangs.getData().get(mkuId);
        if (mkuLangsResDTO == null) {
            log.info("MkuReadApiServiceImpl.handleSaleAttribute mkuLangsResDTO is null");
            return;
        }
        List<AttributeDTO> sellAttributeList = mkuLangsResDTO.getSellAttributeList();
        if (CollectionUtils.isEmpty(sellAttributeList)) {
            log.info("MkuReadApiServiceImpl.handleSaleAttribute sellAttributeList is empty");
            return;
        }
        log.info("MkuReadApiServiceImpl.handleSaleAttribute  mkuId={},sellAttributeList={}", mkuId, JSON.toJSONString(sellAttributeList));
        List<AttributeReadResp> saleAttributeReadRespList = Lists.newArrayList();
        for (AttributeDTO vo : sellAttributeList) {
            // 属性名称映射
            Map<String, String> attributeLangMap = Optional.ofNullable(vo.getLangList()).orElseGet(ArrayList::new).stream().filter(Objects::nonNull).filter(l -> StringUtils.isNotBlank(l.getLangName())).collect(Collectors.toMap(AttributeLangDTO::getLang, AttributeLangDTO::getLangName));

            // 属性值多语言名称映射
            Map<Long, Map<String, String>> valueLangMap = Optional.ofNullable(vo.getAttributeValueList()).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(AttributeValueDTO::getId, valueVo -> valueVo.getLangList().stream().filter(Objects::nonNull).collect(Collectors.toMap(AttributeValueLangDTO::getLang, AttributeValueLangDTO::getLangName))));

            log.info("MkuReadApiServiceImpl.handleSaleAttribute  valueLangMap={}", JSON.toJSONString(valueLangMap));
            for (String lang : langList) {
                if (!attributeLangMap.containsKey(lang)) {
                    continue;
                }
                AttributeReadResp respDTO = new AttributeReadResp();
                respDTO.setAttributeId(vo.getId());
                respDTO.setLang(lang);
                respDTO.setSort(vo.getSort());
                respDTO.setAttributeName(attributeLangMap.get(lang));
                // 销售属性值
                List<String> valueList = Lists.newArrayList();
                for (Long valueId : valueLangMap.keySet()) {
                    if (MapUtils.isNotEmpty(valueLangMap) && valueLangMap.containsKey(Long.valueOf(valueId)) && StringUtils.isNotBlank(valueLangMap.get(Long.valueOf(valueId)).get(lang))) {
                        valueList.add(valueLangMap.get(Long.valueOf(valueId)).get(lang));
                    }
                }
                respDTO.setAttributeValueList(valueList);

                saleAttributeReadRespList.add(respDTO);
            }
        }
        mkuDetailReadResp.setSaleAttrList(saleAttributeReadRespList);
    }

    /**
     * 根据mkuId获取固定sku信息
     */
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, Long>> getFixedSkuByMkuId(MkuRefReadReq mkuIds) {
        Map<Long, Long> map = getMkuIdFixedSkuMap(mkuIds);
        return DataResponse.success(map);
    }

    private Map<Long, Long> getMkuIdFixedSkuMap(MkuRefReadReq mkuIds) {
        Map<Long, Long> map = Maps.newHashMapWithExpectedSize(mkuIds.getMkuIdList().size());
        List<CustomerMkuPricePO> res = this.getCustomerMkuPricePOList(mkuIds);
        if (CollectionUtils.isNotEmpty(res)) {
            map = res.stream().collect(Collectors.toMap(CustomerMkuPricePO::getMkuId, CustomerMkuPricePO::getFixedSkuId));
        }
        return map;
    }

    private List<CustomerMkuPricePO> getCustomerMkuPricePOList(MkuRefReadReq mkuIds) {
        MkuPriceReqVO req = CustomerMkuPriceConvert.INSTANCE.mkuRefReadReq2ReqVo(mkuIds);
        return customerMkuPriceManageService.getCustomerMkuFixedSkuListByMkuClient(req);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<String, List<CustomerMkuSkuReadResp>>> groupBySourceCountry(CustomerMkuSkuReadReq input) {
        MkuPriceReqVO req = CustomerMkuPriceConvert.INSTANCE.mkuRefReadReq2ReqVo(input);
        DataResponse<Map<String, List<CustomerMkuPricePO>>> countryMkuMapRes = customerMkuPriceManageService.groupBySourceCountry(req);
        if (!countryMkuMapRes.getSuccess()) {
            log.info("listMkuPrice, countryMkuMapRes fail. res={}", JSONObject.toJSONString(countryMkuMapRes));
            return DataResponse.error(countryMkuMapRes.getMessage());
        }

        Map<String, List<CustomerMkuSkuReadResp>> resultMap = CustomerMkuPriceConvert.INSTANCE.mapCustomerMkuPricePO2ReadResp(countryMkuMapRes.getData());
        return DataResponse.success(resultMap);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<CustomerMkuReadResp>> list(MkuClientReadReq mkuClientReadReq) {
        List<CustomerMkuPO> res = queryCustomerMkuPoList(mkuClientReadReq.getMkuIdList(), mkuClientReadReq.getClientCode());
        if (CollectionUtils.isNotEmpty(res)) {
            return DataResponse.success(CustomerMkuConvert.INSTANCE.listPo2resp(res));
        }
        return DataResponse.success();
    }

    private List<Long> getCustomerMkuIdList(Set<Long> mkuIdList, String clientCode) {
        List<CustomerMkuPO> customerMkuPoList = this.queryCustomerMkuPoList(mkuIdList, clientCode);
        if (CollectionUtils.isEmpty(customerMkuPoList)) {
            return Collections.emptyList();
        }
        return customerMkuPoList.stream().filter(Objects::nonNull).map(CustomerMkuPO::getMkuId).collect(Collectors.toList());
    }

    private List<CustomerMkuPO> queryCustomerMkuPoList(Set<Long> mkuIdList, String clientCode) {
        LambdaQueryWrapper<CustomerMkuPO> wrapper = Wrappers.<CustomerMkuPO>lambdaQuery().in(CustomerMkuPO::getMkuId, mkuIdList).eq(CustomerMkuPO::getClientCode, clientCode).eq(CustomerMkuPO::getBindStatus, CustomerMkuBindEnum.BIND).eq(CustomerMkuPO::getYn, YnEnum.YES.getCode());
        return customerMkuBaseMapper.selectList(wrapper);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, Long>> getJdSkuByMkuIds(MkuRefReadReq req) {
        Map<Long, Long> mkuIdFixedSkuMap = this.getMkuIdFixedSkuMap(req);
        if (MapUtils.isEmpty(mkuIdFixedSkuMap)) {
            return DataResponse.success();
        }
        return DataResponse.success(this.getJdSkuBySkuIds(mkuIdFixedSkuMap));
    }


    private Map<Long, Long> getJdSkuBySkuIds(Map<Long, Long> mkuIdFixedSkuMap) {
        List<Long> fixedSkuIdList = mkuIdFixedSkuMap.values().stream().filter(Objects::nonNull).collect(Collectors.toList());
        // 查询国际sku信息
        LambdaQueryWrapper<SkuPO> queryWrapper = new LambdaQueryWrapper<SkuPO>().select(SkuPO::getSkuId, SkuPO::getJdSkuId).in(SkuPO::getSkuId, fixedSkuIdList).eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> skuPoList = skuBaseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(skuPoList)) {
            return Collections.emptyMap();
        }
        Map<Long, Long> resultMap = Maps.newHashMap();
        // 国际skuId和京东skuId的映射
        Map<Long, Long> jdSkuMap = skuPoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(SkuPO::getSkuId, SkuPO::getJdSkuId));
        mkuIdFixedSkuMap.forEach((k, v) -> {
            if (jdSkuMap.containsKey(v) && Objects.nonNull(jdSkuMap.get(v))) {
                resultMap.put(k, jdSkuMap.get(v));
            }
        });
        return resultMap;
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<MkuCanPurchaseReadResp>> checkMkuCanPurchase(CheckMkuCanPurchaseReadReq req) {
        AssertValidation.isEmpty(req, DataResponseCode.PARAM_ERROR.getMessage());
        AssertValidation.isEmpty(req.getMkuAreaBaseInfoReadReq(), DataResponseCode.PARAM_ERROR.getMessage());
        // 查询客户绑定商品关系
        List<CustomerMkuPO> customerMkuPOList = this.queryCustomerMkuPoList(Sets.newHashSet(req.getMkuIdList()), req.getClientCode());
        if (CollectionUtils.isEmpty(customerMkuPOList)) {
            return DataResponse.success(this.getNoCustomerMkuCanPurchase(req.getMkuIdList(), req.getClientCode(), req.getLangList()));
        }

        Set<Long> mkuIdList = customerMkuPOList.stream().filter(Objects::nonNull).map(CustomerMkuPO::getMkuId).collect(Collectors.toSet());
        MkuRefReadReq mkuRefReadReq = new MkuRefReadReq();
        mkuRefReadReq.setMkuIdList(mkuIdList);
        mkuRefReadReq.setClientCode(req.getClientCode());
        mkuRefReadReq.setSourceCountryCode(req.getSourceCountryCode());

        List<CustomerMkuPricePO> customerMkuPricePOList = this.getCustomerMkuPricePOList(mkuRefReadReq);
        // 没查到客制化价格时，报错
        if (CollectionUtils.isEmpty(customerMkuPricePOList)) {
            return DataResponse.success(this.getNoCustomerMkuCanPurchase(req.getMkuIdList(), req.getClientCode(), req.getLangList()));
        }
        // 查询MKU的可采接口
        IscMkuAvailableSaleReq saleReq = new IscMkuAvailableSaleReq();
        saleReq.setMkuIds(mkuIdList);
        saleReq.setClientCode(req.getClientCode());
        Map<Long, IscMkuAvailableSaleResDTO> mkuAvailableMap = mkuRpcService.queryMkuAvailable(saleReq);

        List<MkuCanPurchaseReadResp> mkuCanPurchaseReadRespList = Lists.newArrayList();
        saleReq.getMkuIds().forEach((mkuId) -> {
            MkuCanPurchaseReadResp canPurchaseReadResp = new MkuCanPurchaseReadResp();
            canPurchaseReadResp.setMkuId(mkuId);
            IscMkuAvailableSaleResDTO saleResult = mkuAvailableMap.get(mkuId);
            canPurchaseReadResp.setCanPurchase(saleResult.getIsAvailableSale());
            this.translateUnAvailableMessage(canPurchaseReadResp, saleResult.getUnAvailableSaleMessage(), "", req.getLangList());
            mkuCanPurchaseReadRespList.add(canPurchaseReadResp);
        });

        // 未绑定客制化信息的商品设置为不可售
        List<Long> subtractMkuIdList = (List<Long>) CollectionUtils.subtract(req.getMkuIdList(), mkuIdList);
        if (CollectionUtils.isNotEmpty(subtractMkuIdList)) {
            mkuCanPurchaseReadRespList.addAll(this.getNoCustomerMkuCanPurchase(subtractMkuIdList, req.getClientCode(), req.getLangList()));
        }
        return DataResponse.success(mkuCanPurchaseReadRespList);
    }


    private List<MkuCanPurchaseReadResp> getNoCustomerMkuCanPurchase(List<Long> mkuIdList, String clientCode, List<String> langList) {
        return mkuIdList.stream().map(mkuId -> {
            MkuCanPurchaseReadResp readResp = new MkuCanPurchaseReadResp();
            readResp.setMkuId(mkuId);
            readResp.setCanPurchase(Boolean.FALSE);
            this.translateUnAvailableMessage(readResp, String.format("未查询到客户 %s 绑定的商品信息", clientCode), clientCode, langList);
            return readResp;
        }).collect(Collectors.toList());
    }


    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<MkuInfoReadResp>> queryMkuInfoByIds(MkuRefReadReq req) {
        if (CollectionUtils.isEmpty(req.getMkuIdList())) {
            return DataResponse.error(DataResponseCode.PARAM_ERROR.getCode(), DataResponseCode.PARAM_ERROR.getMessage());
        }

        // 查询客户绑定商品关系
        List<Long> mkuIdList = this.getCustomerMkuIdList(Sets.newHashSet(req.getMkuIdList()), req.getClientCode());
        if (CollectionUtils.isEmpty(mkuIdList)) {
            return DataResponse.success();
        }

        LambdaQueryWrapper<MkuPO> queryWrapper = Wrappers.lambdaQuery(MkuPO.class).in(MkuPO::getMkuId, req.getMkuIdList()).eq(MkuPO::getYn, YnEnum.YES.getCode());
        List<MkuPO> mkuPoList = mkuAtomicService.getBaseMapper().selectList(queryWrapper);
        List<MkuInfoReadResp> mkuInfoReadRespList = MkuConvert.INSTANCE.listMkuPo2MkuInfoReadResp(mkuPoList);


        // 查询mku客制化价格
        Map<Long, MkuPriceResVO> mkuPriceResVOMap = this.getMkuPriceResVOMap(req);

        for (MkuInfoReadResp readResp : mkuInfoReadRespList) {
            if (MapUtils.isNotEmpty(mkuPriceResVOMap) && mkuPriceResVOMap.containsKey(readResp.getMkuId())
                    && Objects.nonNull(mkuPriceResVOMap.get(readResp.getMkuId()))) {
                MkuPriceResVO mkuPriceResVO = mkuPriceResVOMap.get(readResp.getMkuId());
                readResp.setSalePrice(mkuPriceResVO.getSalePrice());
                readResp.setIncludeTaxPrice(mkuPriceResVO.getIncludeTaxPrice());
                readResp.setValueAddedTaxRate(mkuPriceResVO.getValueAddedTax());
                readResp.setSkuId(mkuPriceResVO.getSkuId());
                readResp.setCurrency(mkuPriceResVO.getCurrency());
            }
        }
        return DataResponse.success(mkuInfoReadRespList);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<MkuInfoReadResp>> queryMkuInfoWithFixedSkuByMkuIds(MkuRefReadReq req) {
        if (CollectionUtils.isEmpty(req.getMkuIdList())) {
            return DataResponse.error(DataResponseCode.PARAM_ERROR.getCode(), DataResponseCode.PARAM_ERROR.getMessage());
        }
        Map<Long, Long> mkuIdFixedSkuMap = getMkuIdFixedSkuMap(req);
        if (MapUtils.isEmpty(mkuIdFixedSkuMap)) {
            return DataResponse.success();
        }

        LambdaQueryWrapper<MkuPO> queryWrapper = Wrappers.lambdaQuery(MkuPO.class).in(MkuPO::getMkuId, req.getMkuIdList()).eq(MkuPO::getYn, YnEnum.YES.getCode());
        List<MkuPO> mkuPoList = mkuAtomicService.getBaseMapper().selectList(queryWrapper);
        List<MkuInfoReadResp> mkuInfoReadRespList = MkuConvert.INSTANCE.listMkuPo2MkuInfoReadResp(mkuPoList);


        // 查询mku客制化价格
        Map<Long, MkuPriceResVO> mkuPriceResVOMap = this.getMkuPriceResVOMap(req);

        CustomerVO customerVO = customerManageService.detail(req.getClientCode());
        for (MkuInfoReadResp readResp : mkuInfoReadRespList) {
            if (MapUtils.isNotEmpty(mkuPriceResVOMap) && mkuPriceResVOMap.containsKey(readResp.getMkuId())
                    && Objects.nonNull(mkuPriceResVOMap.get(readResp.getMkuId()))) {
                MkuPriceResVO mkuPriceResVO = mkuPriceResVOMap.get(readResp.getMkuId());
                readResp.setSalePrice(mkuPriceResVO.getSalePrice());
                readResp.setIncludeTaxPrice(mkuPriceResVO.getIncludeTaxPrice());
                readResp.setValueAddedTaxRate(mkuPriceResVO.getValueAddedTax());
                Long fixedSkuId = mkuIdFixedSkuMap.get(readResp.getMkuId());
                readResp.setSkuId(fixedSkuId);
                readResp.setCurrency(mkuPriceResVO.getCurrency());
                SkuFeatureVO skuFeatureBySkuId = skuFeatureManageService.getSkuFeatureBySkuId(fixedSkuId, customerVO.getCountry());
                readResp.setPurchaseModel(skuFeatureBySkuId.getPurchaseModel());
            }
        }
        return DataResponse.success(mkuInfoReadRespList);
    }


    private Map<Long, MkuPriceResVO> getMkuPriceResVOMap(MkuRefReadReq req) {
        MkuPriceReqVO mkuPriceReqVO = new MkuPriceReqVO();
        mkuPriceReqVO.setMkuIdList(req.getMkuIdList());
        mkuPriceReqVO.setClientCode(req.getClientCode());
        mkuPriceReqVO.setSourceCountryCode(req.getSourceCountryCode());
        DataResponse<List<MkuPriceResVO>> dataResponse = mkuPriceManageService.listMkuPrice(mkuPriceReqVO);
        if (dataResponse != null && CollectionUtils.isNotEmpty(dataResponse.getData())) {
            return dataResponse.getData().stream().collect(Collectors.toMap(MkuPriceResVO::getMkuId, Function.identity()));
        }
        return null;
    }

    private void translateUnAvailableMessage(MkuCanPurchaseReadResp canPurchaseReadResp, String message, String clientCode, List<String> langList) {
        if (StringUtils.isBlank(message)) {
            return;
        }

        JSONObject jsonObject = this.getCanSaleTranslateMessageJsonObject(message);
        if (Objects.nonNull(jsonObject)) {
            List<LangItemReadResp> langMessageList = Lists.newArrayList();
            langMessageList.add(new LangItemReadResp(LangConstant.LANG_ZH, message.contains("%s") ? String.format(message, clientCode) : message));
            langList.forEach(lang -> langMessageList.add(new LangItemReadResp(lang, message.contains("%s") ? String.format(this.getCanSaleTranslateMessage(jsonObject, lang), clientCode) : this.getCanSaleTranslateMessage(jsonObject, lang))));
            canPurchaseReadResp.setLangMessageList(langMessageList);
        } else {
            // 异步调用翻译接口
            Map<String, CompletableFuture<String>> translateMessageMap = langList.stream().filter(lang -> !LangConstant.LANG_ZH.equals(lang))
                    .collect(Collectors.toMap(Function.identity(), l -> CompletableFuture.supplyAsync(() -> translateRcpService.translate(message, LangConstant.LANG_ZH, l))));

            List<LangItemReadResp> langMessageList = Lists.newArrayList();
            langMessageList.add(new LangItemReadResp(LangConstant.LANG_ZH, message));
            if (MapUtils.isNotEmpty(translateMessageMap)) {
                translateMessageMap.forEach((k, v) -> {
                    String msg = "";
                    try {
                        msg = v.get(60, TimeUnit.SECONDS);
                    } catch (InterruptedException | ExecutionException | TimeoutException e) {
                        log.error("MkuReadApiServiceImpl.translateUnAvailableMessage 不可采时，调用翻译接口异常,message={}", message, e);
                        // throw new RuntimeException(e);
                        // 如果调用翻译接口失败，默认返回空
                    }
                    langMessageList.add(new LangItemReadResp(k, msg));
                });
            }
            canPurchaseReadResp.setLangMessageList(langMessageList);
        }
    }

    private JSONObject getCanSaleTranslateMessageJsonObject(String key) {
        JSONObject translateMessageJson = ConfigUtils.getJsonByKey(canSaleTranslateMessage, key);
        if (null == translateMessageJson) {
            return null;
        }
        return translateMessageJson;
    }

    private String getCanSaleTranslateMessage(JSONObject jsonObject, String lang) {
        if (null == jsonObject) {
            return "";
        }
        return ConfigUtils.getStringByLang(jsonObject, lang);
    }

    private Map<Long, CustomerMkuPricePO> queryMkuCustomerMkuPriceMap(MkuRefReadReq mkuIds) {
        List<CustomerMkuPricePO> customerMkuPricePOList = this.getCustomerMkuPricePOList(mkuIds);
        if (CollectionUtils.isEmpty(customerMkuPricePOList)) {
            return Collections.emptyMap();
        }

        return customerMkuPricePOList.stream().filter(Objects::nonNull).collect(Collectors.toMap(CustomerMkuPricePO::getMkuId, Function.identity()));
    }

}
