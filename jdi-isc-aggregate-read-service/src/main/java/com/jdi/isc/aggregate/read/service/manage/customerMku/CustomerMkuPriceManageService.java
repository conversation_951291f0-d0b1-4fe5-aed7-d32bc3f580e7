package com.jdi.isc.aggregate.read.service.manage.customerMku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.domain.customerMku.CustomerMkuPricePO;
import com.jdi.isc.aggregate.read.domain.price.biz.MkuPriceReqVO;

import java.util.List;
import java.util.Map;

public interface CustomerMkuPriceManageService {

//    /** 获取客户mku站点下固定sku列表。
//     *  不需要SourceCountryCode参数了。
//     * */
//    @Deprecated
//    List<CustomerMkuPricePO> getCustomerMkuFixedSkuList(MkuPriceReqVO req);

    /**
     * 获取客户mku站点下固定sku列表
     * @param req
     * @return
     */
    List<CustomerMkuPricePO> getCustomerMkuFixedSkuListByMkuClient(MkuPriceReqVO req);

    /**
     * 将mku按照固定sku的货源国分组
     * @param req
     * @return
     */
    DataResponse<Map<String, List<CustomerMkuPricePO>>> groupBySourceCountry(MkuPriceReqVO req);
}
