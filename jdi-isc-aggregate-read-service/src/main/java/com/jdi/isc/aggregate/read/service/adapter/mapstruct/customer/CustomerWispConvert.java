package com.jdi.isc.aggregate.read.service.adapter.mapstruct.customer;

import com.jdi.isc.aggregate.read.domain.customer.biz.CustomerVO;
import com.jdi.isc.aggregate.read.wisp.api.customer.res.CustomerReadResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2024/3/14
 **/
@Mapper
public interface CustomerWispConvert {

    CustomerWispConvert INSTANCE = Mappers.getMapper(CustomerWispConvert.class);


    @Mapping(source = "iopAddressVO",target = "iopAddressReadResp")
    CustomerReadResp vo2ReadResp(CustomerVO customerVO);

}
