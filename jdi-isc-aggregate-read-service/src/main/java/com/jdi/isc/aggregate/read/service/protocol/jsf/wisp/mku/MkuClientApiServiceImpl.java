package com.jdi.isc.aggregate.read.service.protocol.jsf.wisp.mku;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.aggregate.read.domain.delivery.biz.SkuDeliveryAgingVO;
import com.jdi.isc.aggregate.read.domain.mku.biz.*;
import com.jdi.isc.aggregate.read.domain.stock.biz.StockItemReqVO;
import com.jdi.isc.aggregate.read.domain.stock.biz.StockReqVO;
import com.jdi.isc.aggregate.read.domain.stock.biz.StockResVO;
import com.jdi.isc.aggregate.read.service.adapter.mapstruct.mku.MkuConvert;
import com.jdi.isc.aggregate.read.service.manage.mku.MkuClientManageService;
import com.jdi.isc.aggregate.read.service.manage.stock.MkuStockService;
import com.jdi.isc.aggregate.read.wisp.api.delivery.resp.DeliveryAgingResp;
import com.jdi.isc.aggregate.read.wisp.api.mku.MkuClientApiService;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2023/12/15 14:46
 **/
@Slf4j
@Service
public class MkuClientApiServiceImpl implements MkuClientApiService {

    @Resource
    private MkuClientManageService mkuClientManageService;
    @Resource
    private MkuStockService mkuStockService;

    @Override
    public DataResponse<List<OrderMkuInfoDetailResApiDTO>> queryMkuInfoByIds(OrderMkuInfoReqApiDTO input){
        log.info("queryMkuInfoByIds, input={}", JSONObject.toJSONString(input));
        List<OrderMkuInfoDetailResApiDTO> result = mkuClientManageService.queryMkuInfoByIds(input);
        return DataResponse.success(result);
    }

    @Override
    public DataResponse<MkuClientListInfoResApiDTO> listSimpleInfo(MkuClientListInfoReqApiDTO input) {
        log.info("listSimpleInfo, param={}", JSONObject.toJSONString(input));
//        MkuClientListInfoResApiDTO result = mkuClientManageService.listSimpleInfo(input);
        return DataResponse.success();
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<MkuClientApiDTO>> page(MkuClientPageReqApiDTO input) {
        log.info("page, param={}", JSONObject.toJSONString(input));
//        MkuClientPageReqVO pageReqVO = MkuConvert.INSTANCE.mkuClientPageReqDTO2VO(input);
//        PageInfo<MkuClientVO> pageInfo = mkuClientManageService.page(pageReqVO);
//        if (null==pageInfo){
//            log.warn("PageInfo<MkuClientVO> null. input={}", JSONObject.toJSONString(input));
//            return DataResponse.error("page record empty.");
//        }
//        PageInfo<MkuClientApiDTO> pageInfoDTO = MkuConvert.INSTANCE.mkuClientPageVO2DTO(pageInfo);
        return  DataResponse.success();
    }

    @Override
    public DataResponse<Boolean> existsMku(MkuClientDetailReqApiDTO input) {
        log.info("existsMku, param={}", JSONObject.toJSONString(input));
        MkuClientDetailReqVO reqVO = MkuConvert.INSTANCE.mkuClientReqApiDTO2ReqVo(input);
        Boolean existsMku = mkuClientManageService.existsMku(reqVO);
        return DataResponse.success(existsMku);
    }

    @Override
    public DataResponse<Boolean> existsValidMku(MkuClientDetailReqApiDTO input) {
        log.info("existsValidMku, param={}", JSONObject.toJSONString(input));
        MkuClientDetailReqVO reqVO = MkuConvert.INSTANCE.mkuClientReqApiDTO2ReqVo(input);
        Boolean existsMku = mkuClientManageService.existsValidMku(reqVO);
        return DataResponse.success(existsMku);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<MkuClientApiDTO> baseInfo(MkuClientDetailReqApiDTO input) {
        log.info("baseInfo, param={}", JSONObject.toJSONString(input));
        return DataResponse.success();
//        MkuClientDetailReqVO reqVO = MkuConvert.INSTANCE.mkuClientReqApiDTO2ReqVo(input);
//        MkuClientVO clientVO = mkuClientManageService.baseInfo(reqVO);
//        if (null==clientVO){
//            log.warn("MkuClientVO not exists. input={}", JSONObject.toJSONString(input));
//            return DataResponse.error("not exists.");
//        }
//        MkuClientApiDTO mkuClientApiVO = MkuConvert.INSTANCE.mkuClientVo2ApiDTO(clientVO);
//        return DataResponse.success(mkuClientApiVO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<MkuClientGroupApiDTO> groupInfo(MkuClientDetailReqApiDTO input) {
        log.info("groupInfo, param={}", JSONObject.toJSONString(input));
        return DataResponse.success();
//        MkuClientDetailReqVO reqVO = MkuConvert.INSTANCE.mkuClientReqApiDTO2ReqVo(input);
//        MkuClientGroupVO mkuClientGroupVO = mkuClientManageService.groupInfo(reqVO);
//        if (null==mkuClientGroupVO){
//            log.warn("MkuClientGroupVO not exists. input={}", JSONObject.toJSONString(input));
//            return DataResponse.error("not exists.");
//        }
//        MkuClientGroupApiDTO mkuClientGroupApiVO = MkuConvert.INSTANCE.mkuClientGroupVo2ApiDTO(mkuClientGroupVO);
//        return DataResponse.success(mkuClientGroupApiVO);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<MkuClientCardInfoResApiDTO> cardInfo(MkuClientCardInfoReqApiDTO input) {
        log.info("cardInfo, param={}", JSONObject.toJSONString(input));
//        if (null==input || null==input.getBrandId()){
//            return DataResponse.error("param error.");
//        }
//        MkuClientCardInfoReqVO reqVO = MkuConvert.INSTANCE.mkuClientCardInfoReqDTO2VO(input);
//        MkuClientCardInfoResVO resVO = mkuClientManageService.cardInfo(reqVO);
//        MkuClientCardInfoResApiDTO resApiDTO = MkuConvert.INSTANCE.mkuClientCardInfoResVO2DTO(resVO);
        return DataResponse.success();
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Set<Long>> queryPoolMkuIds(MkuClientListInfoReqApiDTO input) {
//        if (input == null || Strings.isBlank(input.getClientCode()) || CollectionUtils.isEmpty(input.getMkuIds())) {
//            return DataResponse.error("param error.");
//        }
//        Set<Long> result = mkuClientManageService.queryPoolMkuIds(input);
        return DataResponse.success();
    }

    /** 查询库存数量*/
    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<MkuClientStockApiDTO>> getStock(MkuClientStockReqApiDTO input) {
        StockReqVO req = new StockReqVO();
        req.setClientCode(input.getClientCode());
        req.setStationType(input.getStationType());
        req.setContractNum(input.getContractNum());
        List<StockItemReqVO> stockItem = new ArrayList<>(input.getStockItem().size());
        input.getStockItem().forEach(line-> stockItem.add(new StockItemReqVO(line.getMkuId(), line.getNum())));
        req.setStockItem(stockItem);
        Map<Long, StockResVO> stockMap = mkuStockService.getStock(req,null);
        if(MapUtils.isNotEmpty(stockMap)){
            List<MkuClientStockApiDTO> target = new ArrayList<>();
            stockMap.values().forEach(line->{
                MkuClientStockApiDTO item = new MkuClientStockApiDTO();
                BeanUtils.copyProperties(line,item);
                target.add(item);
            });
            return DataResponse.success(target);
        }
        return DataResponse.success(new ArrayList<>());
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, DeliveryAgingResp>> getDeliveryInfo(List<Long> mkuIds, String clientCode) {
        Map<Long, SkuDeliveryAgingVO> deliveryInfo = mkuClientManageService.getDeliveryInfo(mkuIds, clientCode, null);
        Map<Long, DeliveryAgingResp> deliveryAgingRespMap = MkuConvert.INSTANCE.skuDeliveryAgingVO2Resp(deliveryInfo);
        return DataResponse.success(deliveryAgingRespMap);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, DeliveryAgingResp>> getDeliveryInfoByStockStateType(List<Long> mkuIds, String clientCode, Integer stockStateType) {
        Map<Long/*mkuId*/, Integer/*stockStateType*/> stockStateTypeMap = new HashMap<>();
        if (null!=stockStateType){
            mkuIds.forEach(mkuId-> {
                stockStateTypeMap.put(mkuId, stockStateType);
            });
        }
        Map<Long, SkuDeliveryAgingVO> deliveryInfo = mkuClientManageService.getDeliveryInfo(mkuIds, clientCode, stockStateTypeMap);
        Map<Long, DeliveryAgingResp> deliveryAgingRespMap = MkuConvert.INSTANCE.skuDeliveryAgingVO2Resp(deliveryInfo);
        return DataResponse.success(deliveryAgingRespMap);
    }

    @Override
    @ToolKit(exceptionWrap = true)
    public DataResponse<Map<Long, DeliveryAgingResp>> getDeliveryInfoByStockStateTypeMap(List<Long> mkuIds, String clientCode, Map<Long/*mkuId*/, Integer/*stockStateType*/> stockStateTypeMap) {
        Map<Long, SkuDeliveryAgingVO> deliveryInfo = mkuClientManageService.getDeliveryInfo(mkuIds, clientCode, stockStateTypeMap);
        Map<Long, DeliveryAgingResp> deliveryAgingRespMap = MkuConvert.INSTANCE.skuDeliveryAgingVO2Resp(deliveryInfo);
        return DataResponse.success(deliveryAgingRespMap);
    }
}
