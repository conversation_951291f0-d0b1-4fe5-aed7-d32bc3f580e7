package com.jdi.isc.aggregate.read.service.adapter.mapstruct.mku;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.aggregate.read.domain.attribute.biz.AttributeKeyAndValueVO;
import com.jdi.isc.aggregate.read.domain.delivery.biz.SkuDeliveryAgingVO;
import com.jdi.isc.aggregate.read.domain.mku.biz.*;
import com.jdi.isc.aggregate.read.domain.mku.po.MkuLangPO;
import com.jdi.isc.aggregate.read.domain.mku.po.MkuPO;
import com.jdi.isc.aggregate.read.domain.rpc.SkuAreaLimitRpcVO;
import com.jdi.isc.aggregate.read.domain.sku.biz.SkuVO;
import com.jdi.isc.aggregate.read.wiop.api.mku.resp.*;
import com.jdi.isc.aggregate.read.wisp.api.delivery.resp.DeliveryAgingResp;
import com.jdi.isc.aggregate.read.wisp.api.mku.biz.*;
import org.apache.commons.compress.utils.Lists;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * mku对象转换
 *
 * <AUTHOR>
 * @date 2023/11/26
 **/
@Mapper
public interface MkuConvert {

    MkuConvert INSTANCE = Mappers.getMapper(MkuConvert.class);

    @Mappings({@Mapping(source = "mainImg", target = "mkuImage"), @Mapping(constant = "1", target = "isPrimary"), @Mapping(constant = "1", target = "orderSort")})
    MkuImageItemReadResp mkuPO2mkuImageItemRespDTO(MkuPO mkuPO);

    List<MkuImageItemReadResp> mkuPO2mkuImageItemRespDTO(List<MkuPO> mkuPO);


    List<MkuStateReadResp> mkuPO2mkuStateRespDTO(List<MkuPO> mkuPOList);

    default List<CheckMkuSaleReadResp> mkuPOList2checkMkuSaleRespDTO(List<MkuPO> mkuPOList, Map<Long, MkuLangPO> mkuLangPOMap) {
        if (mkuPOList == null) {
            return null;
        }

        List<CheckMkuSaleReadResp> checkMkuSaleReadRespList = Lists.newArrayList();
        for (MkuPO mkuPO : mkuPOList) {
            checkMkuSaleReadRespList.add(this.mkuPO2CheckMkuSaleRespDTO(mkuPO, mkuLangPOMap.getOrDefault(mkuPO.getMkuId(), null)));
        }
        return checkMkuSaleReadRespList;
    }

    default CheckMkuSaleReadResp mkuPO2CheckMkuSaleRespDTO(MkuPO mkuPO, MkuLangPO mkuLangPO) {
        if (Objects.isNull(mkuPO)) {
            return null;
        }

        CheckMkuSaleReadResp checkMkuSaleReadResp = new CheckMkuSaleReadResp();
        checkMkuSaleReadResp.setMkuId(mkuPO.getMkuId());
        checkMkuSaleReadResp.setMkuName(Optional.of(mkuLangPO).orElse(new MkuLangPO()).getMkuTitle());
        checkMkuSaleReadResp.setMkuStatus(mkuPO.getMkuStatus());
        checkMkuSaleReadResp.setIsCanVat(0);
        checkMkuSaleReadResp.setNoReasonToReturn(0);
        checkMkuSaleReadResp.setReturnRuleType(0);
        checkMkuSaleReadResp.setReturnRuleStr("");
        checkMkuSaleReadResp.setBanCause("");
        checkMkuSaleReadResp.setSelfType(0);
        checkMkuSaleReadResp.setLogisticsType(2);
        checkMkuSaleReadResp.setOutputVat(null);
        return checkMkuSaleReadResp;
    }

    @Mappings({@Mapping(ignore = true, target = "createTime")})
    MkuPO skuVo2mkuPO(SkuVO skuVO);

//    @Mappings({@Mapping(ignore = true, target = "createTime"), @Mapping(source = "extAttribute", target = "extAttribute")})
//    MkuPO skuVo2mkuPO(SkuVO skuVO, String extAttribute);

    @Mappings({@Mapping(source = "mainImg", target = "mkuImage"),@Mapping(source = "jdCatId",target = "catId")})
    MkuVO mkuPo2mkuVo(MkuPO mkuPO);

    List<MkuVO> mkuPoList2mkuVoList(List<MkuPO> mkuPO);


    MkuPO mkuUpdateVo2Po(MkuUpdateVO mkuUpdateVO);

    MkuUpdateVO po2MkuUpdateVO(MkuPO mkuPO);

    MkuClientSimpleApiDTO mkuPo2mkuClientSimple(MkuPO mkuPO);

    List<MkuClientSimpleApiDTO> mkuPoList2mkuClientSimpleList(List<MkuPO> mkuPOs);

    @Mappings({
            @Mapping(source = "mainImg", target = "mkuImage"),@Mapping(source = "jdCatId",target = "catId")
    })
    MkuClientVO mkuPo2mkuClientVo(MkuPO mkuPO);

    List<MkuClientVO> mkuPoList2mkuClientVoList(List<MkuPO> mkuPOs);

    MkuClientDetailReqVO mkuClientReqApiDTO2ReqVo(MkuClientDetailReqApiDTO reqApiVO);

    MkuClientApiDTO mkuClientVo2ApiDTO(MkuClientVO clientVO);

    /**
     * 库存对象转换
     * @param input
     * @return
     */
    MkuClientStockApiDTO stockVO2ApiDTO(MkuClientStockVO input);

    /**
     * 商品聚堆对象转换
     *
     * @param clientGroupVO 入参对象
     * @return 出参
     */
    MkuClientGroupApiDTO mkuClientGroupVo2ApiDTO(MkuClientGroupVO clientGroupVO);

    /**
     * 商品列表页面请求参数对象转换
     *
     * @param pageReqApiDTO 列表请求条件对象
     * @return 列表请求条件对象
     */
    MkuClientPageReqVO mkuClientPageReqDTO2VO(MkuClientPageReqApiDTO pageReqApiDTO);

    /**
     * 商品列表页面对象转换
     *
     * @param pageInfo 列表页面对象
     * @return 列表页面对象
     */
    PageInfo<MkuClientApiDTO> mkuClientPageVO2DTO(PageInfo<MkuClientVO> pageInfo);

    /**
     * 商品卡片请求对象转换
     *
     * @param cardInfoReqApiDTO 源对象
     * @return 目标对象
     */
    MkuClientCardInfoReqVO mkuClientCardInfoReqDTO2VO(MkuClientCardInfoReqApiDTO cardInfoReqApiDTO);

    /**
     * 商品卡片结果转换
     *
     * @param cardInfoResVO 源对象
     * @return 目标对象
     */
    MkuClientCardInfoResApiDTO mkuClientCardInfoResVO2DTO(MkuClientCardInfoResVO cardInfoResVO);


    default MkuPO mkuUpdateVo2Po(MkuUpdateVO mkuUpdateVO, MkuPO mkuPO) {
        MkuPO newMkuPO = mkuUpdateVo2Po(mkuUpdateVO);
        newMkuPO.setId(mkuPO.getId());
        newMkuPO.setMkuId(mkuPO.getMkuId());
        newMkuPO.setMainImg(mkuPO.getMainImg());
        newMkuPO.setDetailImg(mkuPO.getDetailImg());
        newMkuPO.setCatId(mkuPO.getCatId());
        newMkuPO.setJdCatId(mkuPO.getJdCatId());
//        newMkuPO.setSaleAttribute(mkuPO.getSaleAttribute());
        newMkuPO.setGroupExtAttribute(mkuPO.getGroupExtAttribute());
        newMkuPO.setUpcCode(mkuPO.getUpcCode());
        newMkuPO.setMkuStatus(mkuPO.getMkuStatus());
        newMkuPO.setRemark(mkuPO.getRemark());
        newMkuPO.setCreator(mkuPO.getCreator());
        newMkuPO.setUpdater(mkuPO.getUpdater());
        newMkuPO.setCreateTime(mkuPO.getCreateTime());
        newMkuPO.setUpdateTime(mkuPO.getUpdateTime());
        newMkuPO.setYn(mkuPO.getYn());
        return newMkuPO;
    }

    default CheckMkuAreaLimitReadResp areaLimitRpc2MkuAreaLimitReadResp(SkuAreaLimitRpcVO skuAreaLimitRpcVO) {
        if (skuAreaLimitRpcVO == null) {
            return null;
        }

        CheckMkuAreaLimitReadResp areaLimitReadResp = new CheckMkuAreaLimitReadResp();
        areaLimitReadResp.setMkuId(skuAreaLimitRpcVO.getSkuId());
        areaLimitReadResp.setAreaRestrict(skuAreaLimitRpcVO.isAreaRestrict());
        return areaLimitReadResp;
    }

    List<CheckMkuAreaLimitReadResp> listAreaLimitRpc2MkuAreaLimitReadResp(List<SkuAreaLimitRpcVO> skuAreaLimitRpcVOList);


    MkuInfoReadResp mkuPo2MkuInfoReadResp(MkuPO mkuPO);

    List<MkuInfoReadResp> listMkuPo2MkuInfoReadResp(List<MkuPO> mkuPOList);

    List<OrderMkuInfoDetailResApiDTO> wispListMkuPo2MkuInfoReadResp(List<MkuPO> mkuPOList);

    List<AttributeKeyAndValueDTO> attributeKeyAndValue(List<AttributeKeyAndValueVO> attributeKeyAndValueVOS);

    DeliveryAgingResp skuDeliveryAgingVO2Resp(SkuDeliveryAgingVO skuDeliveryAgingVO);
    Map<Long,DeliveryAgingResp> skuDeliveryAgingVO2Resp(Map<Long,SkuDeliveryAgingVO> mkuIdDeliveryAgingVOMap);
}
