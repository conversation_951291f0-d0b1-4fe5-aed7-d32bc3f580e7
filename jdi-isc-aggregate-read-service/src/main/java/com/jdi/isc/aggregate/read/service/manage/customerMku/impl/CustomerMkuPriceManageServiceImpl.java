package com.jdi.isc.aggregate.read.service.manage.customerMku.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.domain.customerMku.CustomerMkuPricePO;
import com.jdi.isc.aggregate.read.domain.enums.YnEnum;
import com.jdi.isc.aggregate.read.domain.price.biz.MkuPriceReqVO;
import com.jdi.isc.aggregate.read.domain.sku.biz.SkuCountryVO;
import com.jdi.isc.aggregate.read.service.atomic.customerMku.CustomerMkuPriceAtomicService;
import com.jdi.isc.aggregate.read.service.manage.customerMku.CustomerMkuPriceManageService;
import com.jdi.isc.aggregate.read.service.manage.sku.SkuReadManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * mku固定sku读服务
 * <AUTHOR>
 * @date 20231221
 */
@Service
@Slf4j
public class CustomerMkuPriceManageServiceImpl implements CustomerMkuPriceManageService {

    @Resource
    private CustomerMkuPriceAtomicService customerMkuPriceAtomicService;

    @Resource
    private SkuReadManageService skuReadManageService;

//    /** 获取客户mku站点下固定sku列表。
//     *  不需要SourceCountryCode参数了。
//     * */
//    @Deprecated
//    public List<CustomerMkuPricePO> getCustomerMkuFixedSkuList(MkuPriceReqVO req){
//        //获取当前客户站点下固定sku信息
//        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
//                .in(CustomerMkuPricePO::getMkuId,req.getMkuIdList())
//                .eq(CustomerMkuPricePO::getClientCode, req.getClientCode())
//                .eq(CustomerMkuPricePO::getSourceCountryCode, req.getSourceCountryCode())
//                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode())
//                ;
//        return customerMkuPriceAtomicService.list(wrapper);
//    }

    /**
     * 获取客户mku站点下固定sku列表
     * @param req
     * @return
     */
    public List<CustomerMkuPricePO> getCustomerMkuFixedSkuListByMkuClient(MkuPriceReqVO req){
        //获取当前客户站点下固定sku信息
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .in(CustomerMkuPricePO::getMkuId,req.getMkuIdList())
                .eq(CustomerMkuPricePO::getClientCode, req.getClientCode())
                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode())
                ;
        return customerMkuPriceAtomicService.list(wrapper);
    }

    public DataResponse<Map<String, List<CustomerMkuPricePO>>> groupBySourceCountry(MkuPriceReqVO req){
        Map<String,List<CustomerMkuPricePO>> countryMkuMap = new HashMap<>();
        // 获取当前客户站点下固定sku信息
        List<CustomerMkuPricePO> allMkuFixedSkuList = getCustomerMkuFixedSkuListByMkuClient(req);
        if (CollectionUtils.isEmpty(allMkuFixedSkuList)) {
            log.info("groupBySourceCountry, allMkuFixedSkuList empty. req={}", JSONObject.toJSONString(req));
            return DataResponse.success(countryMkuMap);
        }

        // 获取sku货源国信息
        Set<Long> fixedSkuIds = allMkuFixedSkuList.stream().map(CustomerMkuPricePO::getFixedSkuId).collect(Collectors.toSet());
        List<SkuCountryVO> fixSkuCountryVoList = skuReadManageService.getSourceCountryBySkuId(fixedSkuIds);
        if (CollectionUtils.isEmpty(fixSkuCountryVoList)){
            log.info("groupBySourceCountry, skuCountryVoList empty. req={}, fixedSkuIds={}", JSONObject.toJSONString(req), JSONObject.toJSONString(fixedSkuIds));
            return DataResponse.error("获取商品价格失败。");
        }
        Map<Long,String> fixSkuCountryMap = fixSkuCountryVoList.stream().collect(Collectors.toMap(SkuCountryVO::getSkuId,SkuCountryVO::getSourceCountryCode));

        // 将mku按照货源国分组
        for(CustomerMkuPricePO mkuPricePO:allMkuFixedSkuList){
            String country = fixSkuCountryMap.get(mkuPricePO.getFixedSkuId());
            List<CustomerMkuPricePO> mkuPricePOList = countryMkuMap.get(country);
            if (null==mkuPricePOList){
                mkuPricePOList = new ArrayList<>();
                countryMkuMap.put(country, mkuPricePOList);
            }

            mkuPricePOList.add(mkuPricePO);
        }
        log.info("groupBySourceCountry, countryMkuMap={}", JSONObject.toJSONString(countryMkuMap));

        return DataResponse.success(countryMkuMap);
    }
}
