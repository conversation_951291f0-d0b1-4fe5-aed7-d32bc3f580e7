package com.jdi.isc.aggregate.read.service.manage.product.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jdi.isc.aggregate.read.common.config.CommonDUCCConfig;
import com.jdi.isc.aggregate.read.common.costants.Constant;
import com.jdi.isc.aggregate.read.domain.attribute.biz.AttributeFlatVO;
import com.jdi.isc.aggregate.read.domain.attribute.biz.AttributeValueFlatVO;
import com.jdi.isc.aggregate.read.domain.enums.CategoryAttrInputTypeEnum;
import com.jdi.isc.aggregate.read.domain.enums.CategoryAttrTypeEnum;
import com.jdi.isc.aggregate.read.domain.spu.biz.*;
import com.jdi.isc.aggregate.read.rpc.attribute.AttributeRpcService;
import com.jdi.isc.aggregate.read.service.adapter.mapstruct.attribute.AttributeConvert;
import com.jdi.isc.aggregate.read.service.manage.attribute.AttributeOutService;
import com.jdi.isc.aggregate.read.service.manage.product.AttributeManagerService;
import com.jdi.isc.product.soa.api.attribute.res.AttributeFlatDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/5 18:26
 */
@Slf4j
@Service
public class AttributeManagerServiceImpl implements AttributeManagerService {
//
//    @Resource
//    private AttributeOutService attributeOutService;
//
//    @Resource
//    private CommonDUCCConfig commonDUCCConfig;
//
//    @Resource
//    private AttributeRpcService attributeRpcService;

//    @Override
//    public List<PropertyVO> getSaleAttrByCatId(long catId, String attribute, String lang){
////        List<AttributeFlatVO> attributeVOList = attributeOutService.queryAttrDetail(catId, lang, CategoryAttrTypeEnum.SELL);
//        List<AttributeFlatDTO> attributeFlatDTOS = attributeRpcService.querySellAttrDetail(catId,lang);
//        List<AttributeFlatVO> attributeVOList =  AttributeConvert.INSTANCE.flatDto2VoList(attributeFlatDTOS);
//        if (CollectionUtils.isEmpty(attributeVOList)) {
//            log.info("getSaleAttrByCatId, attributeVOList empty. catId={}, attribute={}, lang={}",catId, attribute, lang);
//            return null;
//        }
//
//        Map<Long, AttributeFlatVO> attrVOMap = attributeVOList.stream().collect(Collectors.toMap(AttributeFlatVO::getId, vo -> vo, (o1, o2) -> o1));
//
//        List<PropertyVO> saleProList = getPropertyVOList(attribute, attrVOMap, lang);
//
//        List<PropertyVO> sortedSaleProList = this.sortSaleProList(saleProList);
//        if (null!=commonDUCCConfig && Objects.equals(Boolean.TRUE,commonDUCCConfig.getLogDebugSwitch()) ){
//            log.info("getSaleAttrByCatId, sort sale property front sorted saleProList={}, after sorted sortedSaleProList={}, lang={}", JSON.toJSONString(saleProList), JSONObject.toJSONString(sortedSaleProList), lang);
//        }
//        return sortedSaleProList;
//    }
//
//    @Override
//    public List<PropertyVO> getExtendAttrByCatId(long catId, String attribute, String lang){
////        List<AttributeFlatVO> attributeVOList = attributeOutService.queryAttrDetail(catId, lang, CategoryAttrTypeEnum.EXTEND);
//        if(StringUtils.isBlank(attribute)){
//            return null;
//        }
//        List<AttributeFlatDTO> attributeFlatDTOS = attributeRpcService.queryExtAttrDetail(catId,lang);
//        List<AttributeFlatVO> attributeVOList =  AttributeConvert.INSTANCE.flatDto2VoList(attributeFlatDTOS);
//        if (CollectionUtils.isEmpty(attributeVOList)) {
//            return null;
//        }
//
//        Map<Long, AttributeFlatVO> attrVOMap = attributeVOList.stream().collect(Collectors.toMap(AttributeFlatVO::getId, vo -> vo, (o1, o2) -> o1));
//
//        return getGroupExtendPropertyVOList(attribute, attrVOMap, lang);
//    }
//
//    /**
//     * 根据扩展属性组字符串、属性映射和语言信息获取扩展属性VO列表
//     * @param groupExtAttribute 扩展属性组字符串，包含扩展属性信息
//     * @param attrVOMap 属性ID到属性VO的映射，用于查找属性信息
//     * @param lang 语言标识，用于筛选特定语言的属性值
//     * @return 返回扩展属性VO列表，包含属性名称、ID、类型、输入类型、排序值及对应的属性值列表
//     */
//    private List<PropertyVO> getGroupExtendPropertyVOList(String groupExtAttribute, Map<Long, AttributeFlatVO> attrVOMap, String lang){
//        if (null!=commonDUCCConfig && Objects.equals(Boolean.TRUE,commonDUCCConfig.getLogDebugSwitch()) ){
//            log.info("getGroupExtendPropertyVOList, parma attribute={}, attrVOMap={}, lang={}", groupExtAttribute, JSONObject.toJSONString(attrVOMap), lang);
//        }
//        List<PropertyVO> propertyVOList = Lists.newArrayList();
//        if(StringUtils.isBlank(groupExtAttribute)){
//            return propertyVOList;
//        }
//        List<ExtendPropertyGroupVO> extendPropertyGroupVOS = ExtendPropertyGroupVO.obtainExtendPropertyGroupVOList(groupExtAttribute);
//        if(CollectionUtils.isNotEmpty(extendPropertyGroupVOS)){
//            for(ExtendPropertyGroupVO extendPropertyGroupVO : extendPropertyGroupVOS) {
//                List<ShotPropertyVO> extendPropertyList = extendPropertyGroupVO.getExtendPropertyList();
//                if(CollectionUtils.isNotEmpty(extendPropertyList)){
//                    for(ShotPropertyVO shotPropertyVO : extendPropertyList) {
//                        Long attrId = shotPropertyVO.getAttributeId();
//                        PropertyVO attrVO = new PropertyVO();
//                        AttributeFlatVO attributeVO = attrVOMap.get(attrId);
//                        if(attributeVO == null){
//                            continue;
//                        }
//                        attrVO.setAttributeName(attributeVO.getAttributeName());
//                        attrVO.setAttributeId(attrId);
//                        attrVO.setAttributeType(attributeVO.getAttributeType());
//                        attrVO.setAttributeInputType(attributeVO.getAttributeInputType());
//                        attrVO.setSort(attributeVO.getSort());
//                        List<PropertyValueVO> propertyValueVOList = new ArrayList<>();
//
//                        Map<Long, AttributeValueFlatVO> attributeValueLangVOMap = attributeVO.getAttributeValueList().stream().collect(Collectors.toMap(AttributeValueFlatVO::getId, vo -> vo, (o1, o2) -> o1));
//                        for(ShotPropertyValueVO valueVO : shotPropertyVO.getPropertyValueVOList()){
//                            PropertyValueVO propertyValueVO = new PropertyValueVO();
//                            if(CategoryAttrTypeEnum.EXTEND.getCode().equals(attributeVO.getAttributeType()) && attrVO.getAttributeInputType().intValue() == CategoryAttrInputTypeEnum.TEXT.getCode().intValue()){
//                                String attvalueLang = valueVO.getLang();
//                                if(lang.equals(attvalueLang)){
//                                    propertyValueVO.setAttributeValueName(valueVO.getAttributeValueName());
//                                    propertyValueVOList.add(propertyValueVO);
//                                }
//                            }else{
//                                Long valueId = valueVO.getAttributeValueId();
//                                propertyValueVO.setAttributeValueId(valueId);
//                                AttributeValueFlatVO attributeValueVO = attributeValueLangVOMap.get(valueId);
//                                if(attributeValueVO == null){
//                                    continue;
//                                }
//                                propertyValueVO.setAttributeValueName(attributeValueVO.getLangName());
//                                propertyValueVO.setSort(attributeValueVO.getSort());
//                                propertyValueVOList.add(propertyValueVO);
//                            }
//                        }
//                        attrVO.setPropertyValueVOList(propertyValueVOList);
//                        propertyVOList.add(attrVO);
//                    }
//                }
//            }
//        }
//        return propertyVOList;
//    }
//
//    private List<PropertyVO> getPropertyVOList(String attribute, Map<Long, AttributeFlatVO> attrVOMap, String lang){
//        if (null!=commonDUCCConfig && Objects.equals(Boolean.TRUE,commonDUCCConfig.getLogDebugSwitch()) ){
//            log.info("getPropertyVOList, parma attribute={}, attrVOMap={}, lang={}", attribute, JSONObject.toJSONString(attrVOMap), lang);
//        }
//        List<PropertyVO> propertyVOList = Lists.newArrayList();
//        if(StringUtils.isBlank(attribute)){
//            return propertyVOList;
//        }
//        String[] attrArray = attribute.split(Constant.HASHTAG);
//        for(String attr : attrArray){
//            String[] currAttrArray = attr.split(Constant.COMMA);
//            Long attrId = Long.parseLong(currAttrArray[0].split(Constant.COLON)[0]);
//            PropertyVO attrVO = new PropertyVO();
//            AttributeFlatVO attributeVO = attrVOMap.get(attrId);
//            if(attributeVO == null){
//                continue;
//            }
//            attrVO.setAttributeName(attributeVO.getAttributeName());
//            attrVO.setAttributeId(attrId);
//            attrVO.setAttributeType(attributeVO.getAttributeType());
//            attrVO.setAttributeInputType(attributeVO.getAttributeInputType());
//            attrVO.setSort(attributeVO.getSort());
//            List<PropertyValueVO> propertyValueVOList = new ArrayList<>();
//
//            Map<Long, AttributeValueFlatVO> attributeValueLangVOMap = attributeVO.getAttributeValueList().stream().collect(Collectors.toMap(AttributeValueFlatVO::getId, vo -> vo, (o1, o2) -> o1));
//            for(String currAttr : currAttrArray){
//                PropertyValueVO propertyValueVO = new PropertyValueVO();
//                if(CategoryAttrTypeEnum.EXTEND.getCode().equals(attributeVO.getAttributeType()) && attrVO.getAttributeInputType().intValue() == CategoryAttrInputTypeEnum.TEXT.getCode().intValue()){
//                    String textLangValue = currAttr.split(Constant.COLON)[1];
//                    if(lang.equals(textLangValue.split(Constant.FACTORIAL_REGEX)[0])){
//                        propertyValueVO.setAttributeValueName(textLangValue.split(Constant.FACTORIAL_REGEX)[1]);
//                        propertyValueVOList.add(propertyValueVO);
//                    }
//                }else{
//                    Long valueId = Long.parseLong(currAttr.split(Constant.COLON)[1]);
//                    propertyValueVO.setAttributeValueId(valueId);
//                    AttributeValueFlatVO attributeValueVO = attributeValueLangVOMap.get(valueId);
//                    if(attributeValueVO == null){
//                        continue;
//                    }
//                    propertyValueVO.setAttributeValueName(attributeValueVO.getLangName());
//                    propertyValueVO.setSort(attributeValueVO.getSort());
//                    propertyValueVOList.add(propertyValueVO);
//                }
//            }
//            attrVO.setPropertyValueVOList(propertyValueVOList);
//            propertyVOList.add(attrVO);
//        }
//        return propertyVOList;
//    }
//
//    /**
//     * 销售属性排序
//     * 1、先按销售属性排序
//     * 2、销售属性值排序
//     * @param saleProList
//     * @return
//     */
//    private List<PropertyVO> sortSaleProList(List<PropertyVO> saleProList) {
//        if (CollectionUtils.isEmpty(saleProList)) {
//            return Collections.emptyList();
//        }
//
//        return saleProList.stream().filter(Objects::nonNull)
//                .sorted(Comparator.nullsLast(Comparator.comparing(PropertyVO::getSort)))
//                .map(propertyVO -> {
//                    if (null != propertyVO && CollectionUtils.isNotEmpty(propertyVO.getPropertyValueVOList())) {
//                        propertyVO.setPropertyValueVOList(propertyVO.getPropertyValueVOList().stream().filter(Objects::nonNull)
//                                .sorted(Comparator.nullsLast(Comparator.comparing(PropertyValueVO::getSort)))
//                                .collect(Collectors.toList()));
//                        return propertyVO;
//                    }
//                    return propertyVO;
//                }).collect(Collectors.toList());
//    }
}
