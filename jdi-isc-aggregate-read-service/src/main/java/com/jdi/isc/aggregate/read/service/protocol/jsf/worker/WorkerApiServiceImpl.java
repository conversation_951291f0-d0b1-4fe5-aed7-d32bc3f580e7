package com.jdi.isc.aggregate.read.service.protocol.jsf.worker;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.service.worker.job.category.LoadToCacheJob;
import com.jdi.isc.aggregate.read.wisp.api.worker.WorkerApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2023/12/18 20:17
 **/
@Slf4j
@Service
public class WorkerApiServiceImpl implements WorkerApiService {

    @Resource
    private LoadToCacheJob loadToCacheJob;
    @Resource
    private AsyncTaskExecutor categoryTaskExecutor;

    @Override
    public DataResponse<String> loadAllCategoryName(String xxlJobParam) {
        log.info("loadAllCategoryName, xxlJobParam={}", xxlJobParam);
        // 使用线程池执行任务，无需等待，直接返回
        categoryTaskExecutor.execute(() -> {
            try {
                log.info("loadAllCategoryName, categoryTaskExecutor xxlJobParam={}", xxlJobParam);
                loadToCacheJob.loadAllCategoryName(xxlJobParam);
            } catch (Exception e) {
                log.error("loadAllCategoryName categoryTaskExecutor error", e);
            }
        } );

        return DataResponse.success();
    }

    @Override
    public DataResponse<String> loadCategoryForAllCustomers(String xxlJobParam) {
        log.info("loadCategoryForAllCustomers, xxlJobParam={}", xxlJobParam);
        // 使用线程池执行任务，无需等待，直接返回
        categoryTaskExecutor.execute(() -> {
            try {
                log.info("loadCategoryForAllCustomers, categoryTaskExecutor xxlJobParam={}", xxlJobParam);
                loadToCacheJob.loadCategoryForAllCustomers(xxlJobParam);
            } catch (Exception e) {
                log.error("loadCategoryForAllCustomers categoryTaskExecutor error", e);
            }
        } );
        return DataResponse.success();
    }
}
