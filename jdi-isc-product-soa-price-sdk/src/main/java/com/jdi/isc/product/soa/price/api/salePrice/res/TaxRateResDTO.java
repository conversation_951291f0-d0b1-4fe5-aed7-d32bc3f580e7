package com.jdi.isc.product.soa.price.api.salePrice.res;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class TaxRateResDTO {

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 客户简码
     */
    private String clientCode;

    /**
     * key类型。1 sku。2 类目
     */
    private Integer keyType;

    /**
     * 可能是skuId。也可能是类目 id
     */
    private String keyId;

    /**
     * 税的code码
     */
    private String taxCode;

    /**
     * 税的名称
     */
    private String taxName;

    /**
     * 增值税率
     */
    private BigDecimal taxRate;


    /**
     * 客户类型 0=供货商，1=客户
     */
    private Integer clientType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 税率的值
     */
    private BigDecimal taxValue;

    /**
     * 利用率类型
     */
    private String rateType;

    /** 自增ID*/
    private Long id;
    /** 创建者*/
    private String creator;
    /** 修改人*/
    private String updater;
    /** 创建时间*/
    private Long createTime;
    /** 最后修改时间*/
    private Long updateTime;
}
