package com.jdi.isc.product.soa.price.api.salePrice.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * sku查询销售价
 * <AUTHOR>
 * @date 2025/3/11
 */
@Data
public class SalePriceResDTO {

    /** skuId*/
    private Long skuId;
    /** clientCode */
    private String clientCode;
    /** 来源币种 */
    private String sourceCurrencyCode;

    /** 来源币种 */
    private String sourceSalePrice;

    /** 目标币种 */
    private String targetCurrencyCode;
    /** 未税销售价 */
    private BigDecimal salePrice;
    /** 含税销售价 */
    private BigDecimal taxSalePrice;
    /** 总税金 */
    private BigDecimal taxPrice;
    /** 总税率 */
    private BigDecimal valueAddTax;

    /** 相关税率税额 */
    private Map<String,Object> salePriceTaxRes;

    /**
     * 可售状态
     */
    private Integer availableSaleStatus;
}
