package com.jdi.isc.product.soa.price.api.price.req;

import com.jdi.isc.product.soa.price.api.common.BasicApiDateTimeDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description: 履约费率表 VO实体类
 * @Author: zhaokun51
 * @Date: 2024/10/29 21:47
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class FulfillmentRateDTO extends BasicApiDateTimeDTO {

    /**
     * 国家码，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 国家码，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * 一级类目id
     */
    private Long firstCatId;

    /**
     * 二级类目id
     */
    private Long secondCatId;

    /**
     * 三级类目id
     */
    private Long thirdCatId;

    /**
     * 末级类目id
     */
    private Long lastCatId;

    private String sourceCountryName;

    private String targetCountryName;

    private String firstCatName;

    private String secondCatName;

    private String thirdCatName;

    private String lastCatName;

    /**
     * 是否备货：1备货(入仓) 0直送
     */
    private Integer isWarehouseProduct;

    /**
     * 货值系数-直发
     */
    private BigDecimal directSendCoefficientValue;

    /**
     * 体积金额-直发(CBM)
     */
    private BigDecimal directSendVolumeValue;

    /**
     * 货值系数-仓发
     */
    private BigDecimal warehouseSendCoefficientValue;

    /**
     * 体积金额-仓发(CBM)
     */
    private BigDecimal warehouseSendVolumeValue;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 当前sku生效的货值系数，根据isWarehouseProduct自动判断,如果为直发等同于directSendCoefficientValue，否则为warehouseSendCoefficientValue
     */
    private BigDecimal effectiveCoefficientValue;

    /**
     * 当前sku生效的体积金额，根据isWarehouseProduct自动判断,如果为直发等同于directSendVolumeValue，否则为warehouseSendVolumeValue
     */
    private BigDecimal effectiveVolumeValue;

    /**
     * 固定履约费用RMB(针对sku粒度维护专用)
     */
    private BigDecimal fixedFulfillmentCost;

    public FulfillmentRateDTO(String sourceCountryCode, String targetCountryCode, Long skuId){
        this.sourceCountryCode = sourceCountryCode;
        this.targetCountryCode = targetCountryCode;
        this.skuId = skuId;
    }
}
