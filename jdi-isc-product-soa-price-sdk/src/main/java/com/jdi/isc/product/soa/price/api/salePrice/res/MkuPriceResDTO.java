package com.jdi.isc.product.soa.price.api.salePrice.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/27 21:02
 */
@Data
public class MkuPriceResDTO {

    /** 京东销售价,未税销价*/
    private BigDecimal salePrice;
    /** 税额*/
    private BigDecimal taxPrice;
    /** mkuId*/
    private Long mkuId;
    /** skuId*/
    private Long skuId;
    /** 币种 */
    private String currency;
    /**
     * 原币种
     */
    private String originCurrency;
    /**
     * 汇率
     */
    private BigDecimal exchangeRate;
    /**
     * 初始币种价格(未税) salePrice的原始价
     */
    private BigDecimal originPrice;

    /** 增值税 */
    private BigDecimal valueAddedTax;

    /** 含税价*/
    private BigDecimal includeTaxPrice;
    /**
     * 税率信息列表
     */
    private List<TaxRateResDTO> taxRateList;
}
