package com.jdi.isc.product.soa.price.api.clientPrice.res;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/03/25
 */
@Data
public class MkuClientPriceResApiDTO {

    /**
     * mkuId
     */
    private Long mkuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 展示币种
     */
    private String currency;

    /**
     * 原币种
     */
    private String originCurrency;

    /**
     * 汇率
     */
    private BigDecimal exchangeRate;

    /**
     * 初始币种价格(未税) salePrice的原始价
     */
    private BigDecimal originPrice;

    /**
     * 未税价
     */
    private BigDecimal salePrice;

    /**
     * 税额
     */
    private BigDecimal taxPrice;

    /**
     * 增值税
     */
    private BigDecimal valueAddedTax;

    /**
     * 含税价
     */
    private BigDecimal includeTaxPrice;
    /**
     * 税率信息列表
     */
//    private List<TaxRateResp> taxRateList;
}
