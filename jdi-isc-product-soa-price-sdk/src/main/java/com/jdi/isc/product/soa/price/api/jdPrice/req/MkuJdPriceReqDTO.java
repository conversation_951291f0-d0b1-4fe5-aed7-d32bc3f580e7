package com.jdi.isc.product.soa.price.api.jdPrice.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * mku jdPrice
 * <AUTHOR>
 * @date 2025/3/24
 */
@Data
public class MkuJdPriceReqDTO {

    /** mkuIdList 商品编号，最高支持100个商品*/
    @NotEmpty(message = "mkuIdList can not be empty")
    @Size(max = 100 , message = "mkuIdList size max is 100")
    private Set<Long> mkuIdList;

    /** 客户编码*/
    @NotNull(message = "clientCode can not be null")
    private String clientCode;


}
