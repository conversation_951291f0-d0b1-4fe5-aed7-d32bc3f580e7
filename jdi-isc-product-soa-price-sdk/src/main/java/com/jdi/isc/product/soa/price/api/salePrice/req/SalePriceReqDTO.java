package com.jdi.isc.product.soa.price.api.salePrice.req;

import com.jdi.isc.product.soa.price.api.common.CustomerDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * sku查询销售价
 * <AUTHOR>
 * @date 2025/3/11
 */
@Data
public class SalePriceReqDTO {

    /** SkuId */
    @NotNull(message = "skuId不能为空")
    private Long skuId;
    /** 客户编码 */
    @NotBlank(message = "客户编码不能为空")
    private String clientCode;
    /** 币种编码 */
    @NotBlank(message = "币种不能为空")
    private String currencyCode;
    /** 支持客户传参、以减少查询次数 */
    private CustomerDTO customerDTO;


}
