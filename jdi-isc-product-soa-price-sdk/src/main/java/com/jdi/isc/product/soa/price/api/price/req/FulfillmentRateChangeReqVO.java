package com.jdi.isc.product.soa.price.api.price.req;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 履约费率变更消息实体
 * <AUTHOR>
 * @Date 2025/3/5
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FulfillmentRateChangeReqVO {

    /**
     * 货源国，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 目的国，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * 一级类目id
     */
    private Long firstCatId;

    /**
     * 二级类目id
     */
    private Long secondCatId;

    /**
     * 三级类目id
     */
    private Long thirdCatId;

    /**
     * 末级类目id
     */
    private Long lastCatId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 是否为仓库产品，1表示是，0表示否。
     */
    private Integer isWareHouseProduct;

    /**
     * 是否及联查询品类信息，默认true
     */
    private boolean defaultQueryCategory = Boolean.TRUE;

    /**
     * 历史实际退税成功率
     */
    private TaxRefundRateBinLackVO taxRefundRate;

    /**
     * 海关编码
     */
    private String hsCode;

    /**
     * 变更来源
     */
    private Integer dataStatusSource;

    public FulfillmentRateChangeReqVO(String sourceCountryCode,String targetCountryCode,Long skuId){
        this.sourceCountryCode = sourceCountryCode;
        this.targetCountryCode = targetCountryCode;
        this.skuId = skuId;
    }

    public FulfillmentRateChangeReqVO(String sourceCountryCode,String targetCountryCode,Long skuId,Integer isWareHouseProduct){
        this.sourceCountryCode = sourceCountryCode;
        this.targetCountryCode = targetCountryCode;
        this.skuId = skuId;
        this.isWareHouseProduct = isWareHouseProduct;
    }

    /**
     * 历史实际退税成功率
     */
    @Data
    @NoArgsConstructor
    public static class TaxRefundRateBinLackVO implements Serializable {
        /**
         * 末级类目id
         */
        private Long lastCatId;

        public TaxRefundRateBinLackVO(Long lastCatId) {
            this.lastCatId = lastCatId;
        }
    }

}
