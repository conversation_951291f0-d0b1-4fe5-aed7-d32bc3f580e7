package com.jdi.isc.product.soa.price.api.jdPrice.res;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/03/24 21:02
 */
@Data
public class MkuJdPriceResDTO {

    /** 京东销售价,未税销价*/
    private BigDecimal jdPrice;
    /** 税额*/
    private BigDecimal taxPrice;
    /** mkuId*/
    private Long mkuId;
    /** skuId*/
    private Long skuId;
    /** 币种 */
    private String currency;
    /** 增值税 */
    private BigDecimal valueAddedTax;
    /** 含税价*/
    private BigDecimal includeTaxPrice;
}
