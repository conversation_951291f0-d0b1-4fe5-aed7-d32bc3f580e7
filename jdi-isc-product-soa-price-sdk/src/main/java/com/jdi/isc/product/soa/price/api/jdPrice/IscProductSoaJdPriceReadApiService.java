package com.jdi.isc.product.soa.price.api.jdPrice;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.price.api.jdPrice.req.JdPriceReqDTO;
import com.jdi.isc.product.soa.price.api.jdPrice.req.MkuJdPriceReqDTO;
import com.jdi.isc.product.soa.price.api.jdPrice.res.JdPriceResDTO;
import com.jdi.isc.product.soa.price.api.jdPrice.res.MkuJdPriceResDTO;
import com.jdi.isc.product.soa.price.api.salePrice.req.MkuPriceReqDTO;
import com.jdi.isc.product.soa.price.api.salePrice.res.MkuPriceResDTO;

import java.util.List;


/**
 * 国家协议价中的京东价价读服务
 * <AUTHOR>
 * @date 2025/3/21
 */
public interface IscProductSoaJdPriceReadApiService {

    /** 查询商品京东价*/
    DataResponse<JdPriceResDTO> getJdPrice(JdPriceReqDTO jdPriceReqDTO);

    /** 查询mku商品京东价*/
    DataResponse<List<MkuJdPriceResDTO>> listMkuJdPrice(MkuJdPriceReqDTO req);
}
