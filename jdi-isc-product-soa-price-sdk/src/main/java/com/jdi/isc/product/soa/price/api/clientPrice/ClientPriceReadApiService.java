package com.jdi.isc.product.soa.price.api.clientPrice;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.price.api.clientPrice.req.MkuClientPriceReqApiDTO;
import com.jdi.isc.product.soa.price.api.clientPrice.res.MkuClientPriceResApiDTO;

import java.util.List;

/**
 * 销端面客价管理
 * <AUTHOR>
 * @date 2025/3/25
 */
public interface ClientPriceReadApiService {

    /**
     * 面客价格
     * @param req
     * @return
     */
    DataResponse<List<MkuClientPriceResApiDTO>> listMkuPrice(MkuClientPriceReqApiDTO req);

}
