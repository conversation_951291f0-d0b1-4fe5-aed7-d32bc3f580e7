package com.jdi.isc.product.soa.price.api.salePrice.res;

import com.jdi.isc.product.soa.price.api.common.BasicApiLongTimeDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description: sku客制化价格草稿明细表 实体类
 * @Author: zhaokun51
 * @Date: 2025/02/27 14:53
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class CustomerSkuPriceDetailDTO extends BasicApiLongTimeDTO {

    /**
     * 关联客制化价格表id
     */
    private Long bizId;

    /**
     * 草稿明细来源id
     * */
    private Long sourceId;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 客户简码
     */
    private String clientCode;

    /**
     * 对客交货模式 EXW,DDP
     */
    private String customerTradeType;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 客制销售价
     */
    private BigDecimal customerSalePrice;

    /**
     * 开始时间
     */
    private Long beginTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 是否启用
     */
    private Integer enableStatus;

    /**
     * 有效期结束当天的00:00:00时间
     */
    private Long endDay;

    /**
     * 生效状态:0当前记录还未生效;1当前记录生效中
     */
    private Integer effectiveStatus;

}
