package com.jdi.isc.product.soa.price.api.price.req;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 触发SKU价格刷新实体
 * <AUTHOR>
 * @Date 2025/3/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PriceRefreshVO {

    /**
     * 货源国，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 目的国，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 是否为仓库产品，1表示是，0表示否。
     */
    private Integer isWareHouseProduct;

    /**
     * SKU ID列表，用于批量刷新价格。
     */
    private List<Long> skuIdList;

    public PriceRefreshVO(String sourceCountryCode, String targetCountryCode, Long skuId, Integer isWareHouseProduct, List<Long> skuIdList) {
        this.sourceCountryCode = sourceCountryCode;
        this.targetCountryCode = targetCountryCode;
        this.skuId = skuId;
        this.isWareHouseProduct = isWareHouseProduct;
        this.skuIdList = skuIdList;
    }

    /**
     * 变更来源
     */
    private Integer dataStatusSource;

}
