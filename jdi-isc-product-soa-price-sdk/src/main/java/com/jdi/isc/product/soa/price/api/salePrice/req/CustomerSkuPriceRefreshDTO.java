package com.jdi.isc.product.soa.price.api.salePrice.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客制价刷新通知实体
 * <AUTHOR>
 * @date 2025/3/13
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSkuPriceRefreshDTO {

    /**
     * 主键Id
     */
    private Long id;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 客户简码
     */
    private String clientCode;


}
