package com.jdi.isc.product.soa.price.api.salePrice;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.price.api.salePrice.req.MkuCalculateTaxSalePriceReqDTO;
import com.jdi.isc.product.soa.price.api.salePrice.req.MkuPriceReqDTO;
import com.jdi.isc.product.soa.price.api.salePrice.req.SalePriceReqDTO;
import com.jdi.isc.product.soa.price.api.salePrice.res.CustomerSkuPriceDetailDTO;
import com.jdi.isc.product.soa.price.api.salePrice.res.MkuCalculateTaxSalePriceResDTO;
import com.jdi.isc.product.soa.price.api.salePrice.res.MkuPriceResDTO;
import com.jdi.isc.product.soa.price.api.salePrice.res.SalePriceResDTO;

import java.util.List;


/**
 * 销售价读服务
 * <AUTHOR>
 * @date 2025/3/11
 */
public interface IscProductSoaSalePriceReadApiService {

    /** 查询商品销售价*/
    DataResponse<SalePriceResDTO> getSalePrice(SalePriceReqDTO salePriceReqDTO);

    /** 查询mku商品销售价*/
    DataResponse<List<MkuPriceResDTO>> listMkuPrice(MkuPriceReqDTO req);

    /** 查询商品vip裸价*/
    DataResponse<SalePriceResDTO> getNakedSalePrice(SalePriceReqDTO salePriceReqDTO);

    /** 根据id查询客制化价格详情*/
    DataResponse<CustomerSkuPriceDetailDTO> getCustomerSkuPriceDetail(Long id);

    DataResponse<MkuCalculateTaxSalePriceResDTO> salePriceCalculateTaxSalePrice(MkuCalculateTaxSalePriceReqDTO req);


}
