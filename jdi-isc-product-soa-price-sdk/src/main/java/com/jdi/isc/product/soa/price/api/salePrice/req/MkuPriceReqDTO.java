package com.jdi.isc.product.soa.price.api.salePrice.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * sku查询销售价
 * <AUTHOR>
 * @date 2025/3/11
 */
@Data
public class MkuPriceReqDTO {

    /** mkuIdList 商品编号，最高支持100个商品*/
    @NotEmpty(message = "mkuIdList can not be empty")
    @Size(max = 100 , message = "mkuIdList size max is 100")
    private Set<Long> mkuIdList;

    /** 客户编码*/
    @NotNull(message = "clientCode can not be null")
    private String clientCode;
    /**
     * 目标币种
     */
    private String targetCurrencyCode;

    /**
     * 是否未入客户池
     */
    private Boolean notCustomerPool = false;

}
