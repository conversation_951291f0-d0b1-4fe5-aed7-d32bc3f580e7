package com.jdi.isc.product.soa.price.api.price;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.price.api.price.req.FulfillmentRateChangeReqVO;
import com.jdi.isc.product.soa.price.api.price.req.FulfillmentRateDTO;

import java.util.List;

/**
 * 履约费率读写服务
 * <AUTHOR>
 * @date 2025/3/4
 */
public interface IscProductSoaFulfillmentRateApiService {

    /** 履约费率变更触发价格(国家成本价、入仓成本价)刷新*/
    DataResponse<Boolean> rateChangeToPriceRefresh(List<FulfillmentRateChangeReqVO> input);


    DataResponse<FulfillmentRateDTO> getFulfillmentRateBySkuId(FulfillmentRateDTO dto);

}
