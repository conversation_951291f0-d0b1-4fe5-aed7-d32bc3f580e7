package com.jdi.isc.product.soa.price.api.price;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.price.api.price.req.FulfillmentRateChangeReqVO;
import com.jdi.isc.product.soa.price.api.price.req.PriceRefreshVO;

import java.util.List;
import java.util.Map;

/**
 * 国际价格写服务
 * <AUTHOR>
 * @date 2025/3/4
 */
public interface IscProductSoaPriceWriteApiService {

    /** 触发跨境国家成本价重算*/
    DataResponse<Boolean> refreshCnCostPrice(List<PriceRefreshVO> input);

    /** 触发跨境入仓成本价重算*/
    DataResponse<Boolean> refreshCnWarehouseCostPrice(List<PriceRefreshVO> input);

    /**
     * 更新商品成本价。
     * @param input 包含要更新的商品成本价信息的 PriceRefreshVO 对象。
     * @return 更新操作是否成功。
     */
    DataResponse<Boolean> updateCostPrice(PriceRefreshVO input);

    /** 触发成本价刷新(国家成本价、入仓成本价)*/
    DataResponse<Boolean> triggerCostPriceRefresh(List<FulfillmentRateChangeReqVO> input);

    /**
     * 初始化京东价格。
     * @param input 一个 PriceRefreshVO 对象，包含价格刷新的相关信息。
     * @return DataResponse 对象，包含一个布尔值，表示初始化操作是否成功。
     */
    DataResponse<Boolean> initJdPrice(PriceRefreshVO input);

    /**
     * 更新成本价格列表。
     * @param input 价格刷新VO对象，包含需要更新的成本价格信息。
     * @return 更新操作的结果，true表示更新成功，false表示更新失败。
     */
    DataResponse<Map<Long,Boolean>> updateCostPriceList(PriceRefreshVO input);

}
