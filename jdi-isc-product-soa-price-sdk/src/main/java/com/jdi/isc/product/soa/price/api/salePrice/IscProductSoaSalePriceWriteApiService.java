package com.jdi.isc.product.soa.price.api.salePrice;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.price.api.salePrice.res.CustomerSkuPriceDetailDTO;

/**
 * 国际销价写服务
 * <AUTHOR>
 * @date 2025/3/13
 */
public interface IscProductSoaSalePriceWriteApiService {

    /**
     * 触发即将过期的客制价状态更新
     */
    DataResponse<Integer> triggerExpiredPriceRefresh();

    /**
     * 触发SKU客制化价格生效状态更新
     */
    DataResponse<Boolean> triggerCustomerSkuPriceUpdate(CustomerSkuPriceDetailDTO dto) ;

}
