package com.jdi.isc.product.soa.price.api.price;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.price.api.price.req.CrossBorderImportTaxReqDTO;
import com.jdi.isc.product.soa.price.api.price.res.CrossBorderImportTaxResDTO;

import java.util.Map;

/**
 * 国际税读服务
 * <AUTHOR>
 * @date 2025/3/4
 */
public interface IscProductSoaTaxReadApiService {

    /** 查询指定国家的进口税费信息*/
    DataResponse<Map<Long, CrossBorderImportTaxResDTO>> queryCrossBorderSkuImportTax(CrossBorderImportTaxReqDTO input);

}
