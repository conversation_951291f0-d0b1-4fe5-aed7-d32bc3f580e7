package com.jdi.isc.product.soa.price.api.price;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.price.api.price.req.*;

/**
 * 国际税写服务
 * <AUTHOR>
 * @date 2025/3/12
 */
public interface IscProductSoaTaxWriteApiService {

    /** 保存更新越南关税信息*/
    DataResponse<Boolean> saveOrUpdateVnTax(VnSkuTaxVO input);

    /** 保存更泰国南关税信息*/
    DataResponse<Boolean> saveOrUpdateThTax(ThSkuTaxVO input);

    /** 保存更新巴西关税信息*/
    DataResponse<Boolean> saveOrUpdateBrTax(BrSkuTaxVO input);

    /** 保存更新马来关税信息*/
    DataResponse<Boolean> saveOrUpdateMyTax(MySkuTaxVO input);

    /** 保存更新匈牙利关税信息*/
    DataResponse<Boolean> saveOrUpdateHuTax(HuSkuTaxVO input);

    /** 保存更新印尼关税信息*/
    DataResponse<Boolean> saveOrUpdateIdTax(IdSkuTaxVO input);


}
