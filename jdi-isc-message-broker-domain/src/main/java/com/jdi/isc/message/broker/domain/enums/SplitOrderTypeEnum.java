package com.jdi.isc.message.broker.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SplitOrderTypeEnum {

    PRE_SPLIT_ORDER(1, "预拆单"),

    SPLIT_ORDER(2, "拆单")
    ;

    private int type;

    private String desc;

    public static SplitOrderTypeEnum queryByType(int type) {
        for (SplitOrderTypeEnum typeEnum : SplitOrderTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }

}
