package com.jdi.isc.message.broker.domain.msg;

import lombok.Builder;
import lombok.Data;


/**
 * mku创建消息
 * <AUTHOR>
 * @date 20250610
 */
@Data
@Builder
public class MkuCreateMsg {

    /**
     * mkuId
     */
    private Long mkuId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 发品来源
     */
    private String source;

    /**
     * 外部关联商品id(可能为空)
     */
    private String skuThirdRefId;

    /**
     * 消息类型
     */
    private String changeType;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 备注
     */
    private String remark;


}
