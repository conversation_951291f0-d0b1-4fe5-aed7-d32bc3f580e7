package com.jdi.isc.message.broker.domain.msg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author：xubing82
 * @Date：2025/5/23 14:43
 * @Description：ForecastOrderSplitOpenMsg预报备单拆单消息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ForecastOrderSplitOpenMsg {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 最后修改时间
     */
    private Long updateTime;

    /**
     * 逻辑删除
     */
    private Integer yn;

    /**
     * 预报备货单的唯一标识号
     */
    private String forecastOrderId; // 预报备货单号

    /**
     * 供应商简码，标识订单关联的供应商信息
     */
    private String supplierCode; // 供应商简码

    /**
     * SKU总数量(所有SKU数量的加合)
     */
    private Integer skuNum; // sku总数量(所有skuNum的加合)

    /**
     * 预报备货单商品种类
     */
    private Integer skuKindNum; // 预报备货单商品种类

    /**
     * 预报备货单后台状态
     */
    private Integer forecastOrderStatus; // 预报备货单后台状态

    /**
     * 采购单类型【1.本土；2跨境；3混合】
     */
    private Integer forecastOrderType; // 采购单类型【1.本土；2跨境；3混合】

    /**
     * 目的国国家码
     */
    private String countryCode; // 目的国国家码

    /**
     * 备货仓编码
     */
    private String enterpriseWarehouseId; // 备货仓ID

    /**
     * 扩展标，下游透传
     */
    private String forecastOrderExtInfo; // 扩展标，下游透传

    /**
     * 版本号
     */
    private Integer version; // 版本号

    /**
     * 原始父预报备货单号
     */
    private String parentForecastOrderId; // 原始父预报备货单号

    /**
     * 父预报备货单号
     */
    private String splitForecastOrderId; // 父预报备货单号

    /**
     * 预报备货单下单时间，子单下单时间为父单下单时间，早于createTime
     */
    private Long forecastCreateTime; // 预报备货单下单时间，子单下单时间为父单下单时间，早于createTime

    /**
     * 确认时间
     */
    private Long confirmTime; // 确认时间

    /**
     * 预报备货单的有效状态
     */
    private Integer validState; // 预报备货单的有效状态

    /**
     * 接单时间
     */
    private Long receiveTime; // 接单时间

    /**
     * 发货时间
     */
    private Long shippedTime; // 发货时间

    /**
     * 入仓时间
     */
    private Long enterWarehouseTime; // 入仓时间

    /**
     * 全部入库时间
     */
    private Long allStoredTime; // 全部入库时间

    /**
     * 取消时间
     */
    private Long cancelTime; // 取消时间

    /**
     * 是否报关状态 1报关，0不报关
     */
    private Integer customsClearance; // 是否报关状态 1报关，0不报关

    /**
     * 三方订单号/唯一ID
     */
    private String thirdOrderId; // 三方订单号/唯一ID

    /**
     * 入集货仓时间
     */
    private Long enterStorehouseTime; // 入集货仓时间

    /**
     * 出集货仓时间
     */
    private Long outStorehouseTime; // 出集货仓时间

    /**
     * 集运中心编号
     */
    private String storehouseId; // 集运中心编号

    /**
     * 入库单号
     */
    private String enterWarehouseNo; // 入库单号

    /**
     * 商品入库数量(商品表的加合)
     */
    private Integer inboundWarehouseWareNum; // 商品入库数量(商品表的加合)

    /**
     * 预拆单状态 0待预拆单，1已预拆单
     */
    private Integer preSplitStatus; // 预拆单状态 0待预拆单，1已预拆单

    /**
     * 预报备货单后台前置状态
     */
    private Integer beforeForecastOrderStatus; // 预报备货单后台状态

    /**
     * 预报备货单的分割类型标识 1预拆单2客户主动拆单
     */
    private Integer splitType;

    /**
     * 预报备货单商品信息
     */
    private List<ForecastOrderWareOpenMsg> forecastOrderWarePOList;

    /**
     * 拆单后的子单
     */
    private List<ForecastOrderSplitOpenMsg> childForecastOrderList;

}
