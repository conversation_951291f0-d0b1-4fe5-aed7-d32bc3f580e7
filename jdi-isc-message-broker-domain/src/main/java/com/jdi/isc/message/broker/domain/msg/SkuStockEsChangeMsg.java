package com.jdi.isc.message.broker.domain.msg;

import lombok.Data;

/**
 * @author：xubing82
 * @date：2025/6/11 16:20
 * @description：SkuStockEsChangeMsg
 */
@Data
public class SkuStockEsChangeMsg {
    /**
     * 客户代码，用于标识该库存信息所属的客户
     */
    private String clientCode;

    /**
     * 客户所属国家码
     */
    private String countryCode;


    /**
     * 平台商品编号，用于标识平台上的商品
     */
    private Long mkuId;

    /**
     * 京东商品编号，用于标识京东平台上的商品
     */
    private Long jdSkuId;

    /**
     * 国际商品编号，用于标识系统中的商品
     */
    private Long skuId;

    /**
     * 库存类型，用于区分不同库存来源或分类
     *
     */
    private Integer stockType;

    /**
     * 库存标记，用于标识商品是否有库存
     * 0-无库存，1-有在途库存(现货+在途), 2-有现货库存(纯现货)
     */
    private Integer hasStockTag;

    /**
     * 可用库存数量，表示商品当前可售的实际库存量
     */
    private Long availableStock;

}
