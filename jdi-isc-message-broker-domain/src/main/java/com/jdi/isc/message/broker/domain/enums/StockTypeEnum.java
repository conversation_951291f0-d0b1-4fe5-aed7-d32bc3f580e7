package com.jdi.isc.message.broker.domain.enums;

import java.util.Arrays;

public enum StockTypeEnum {
    CN_STOCK(0, "国内库存(跨境直发品)"),
    SUPPLY_STOCK(1, "供应商库存"),
    WAREHOUSE_STOCK(2, "备货仓库存");

    /**
     * code
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;

    StockTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static StockTypeEnum getByCode(Integer code) {
        return Arrays.stream(values()).filter(c -> c.getCode().equals(code)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
