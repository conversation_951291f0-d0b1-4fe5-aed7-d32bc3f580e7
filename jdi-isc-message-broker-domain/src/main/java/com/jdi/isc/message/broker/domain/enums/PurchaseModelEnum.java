package com.jdi.isc.message.broker.domain.enums;

import com.google.common.collect.Sets;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @author：xubing82
 * @date：2025/6/4 13:38
 * @description：PurchaseModelEnum
 */
public enum PurchaseModelEnum {

    DIRECT_SUPPLY(0, "直供模式"),

    STOCK_UP(1, "备货模式"),

    CONSIGNMENT(2, "寄售模式"),
    ;

    private Integer code;
    private String desc;

    public static Map<Integer, PurchaseModelEnum> enumMap = new HashMap();
    public static Map<Integer, String> codeDescMap = new HashMap();

    public static final Set<Integer> CAN_SEND_WMS_MODEL = Sets.newHashSet(STOCK_UP.getCode(), CONSIGNMENT.getCode());

    public static final Set<Integer> WIMP_STOCK_MODEL = Sets.newHashSet(STOCK_UP.getCode(), CONSIGNMENT.getCode());


    static {
        for (PurchaseModelEnum val : values()) {
            enumMap.put(val.getCode(), val);
            codeDescMap.put(val.getCode(), val.getDesc());
        }
    }

    PurchaseModelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PurchaseModelEnum forCode(Integer code) {
        return enumMap.get(code);
    }

}
