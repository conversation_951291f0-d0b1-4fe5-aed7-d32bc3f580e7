package com.jdi.isc.message.broker.domain.msg;

import lombok.Data;

import java.util.Set;

/**
 * @author：xubing82
 * @date：2025/6/11 18:56
 * @description：MkuEsVOWithStock
 */
@Data
public class MkuClientEsVO {

    /**
     * 唯一标识符，用于标识MkuEsClientVO对象的实例
     */
    private String id;

    /**
     * mku编码
     */
    private Long mkuId;

    /**
     * 客户简码
     */
    private String clientCode;

    /**
     * 库存标记，用于标识商品是否有库存
     * 0-无库存，1-有在途库存(现货+在途), 2-有现货库存(纯现货)
     */
    private Integer hasStock;

    /**
     * 数据存活状态
     */
    private Integer yn;

    /**
     * 履约时效数据
     */
    private Set<Integer> featureTags;

}
