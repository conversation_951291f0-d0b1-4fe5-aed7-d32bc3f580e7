package com.jdi.isc.message.broker.domain.msg;

import lombok.Data;

/**
 * @author：xubing82
 * @date：2025/6/11 16:20
 * @description：mku中ES业务属性字段变更消息对象
 */
@Data
public class MkuEsAttributeChangeMsg {
    /**
     * 客户代码，用于标识该库存信息所属的客户
     */
    private String clientCode;

    /**
     * 客户所属国家码
     */
    private String countryCode;

    /**
     * 平台商品编号，用于标识平台上的商品
     */
    private Long mkuId;

    /**
     * 国际商品编号，附属字段
     */
    private Long skuId;

    //------------------业务属性字段----------------------
    /**
     * 属性变更类型
     */
    private Integer changeType;


    //------------------业务属性字段----------------------

    /**
     * 库存标记，用于标识商品是否有库存
     * 0-无库存，1-有在途库存(现货+在途), 2-有现货库存(纯现货)
     */
    private Integer hasStockTag;

    /**
     * 商品时效标记,1-48小时、2-72小时、3-精准达、4-xxx
     **/
    private Integer promiseValue;

}
