package com.jdi.isc.message.broker.domain.msg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author：xubing82
 * @Date：2025/5/23 14:53
 * @Description：ForecastOrderWareSplitOpenMsg
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ForecastOrderWareOpenMsg {

    /** 自增ID*/
    private Long id;

    /** 备注*/
    private String remark;

    /** 创建者*/
    private String creator;

    /** 修改人*/
    private String updater;

    /** 创建时间*/
    private Long createTime;

    /** 最后修改时间*/
    private Long updateTime;

    /** 逻辑删除  */
    private Integer yn;

    /**
     * 预报备货单号
     */
    private String forecastOrderId;

    /**
     * 履约SKU编码
     */
    private Long skuId;

    /**
     * SKU数量
     */
    private Integer skuNum;

    /**
     * SKU类型
     * 0：商品
     * 1：附件
     * 2：赠品
     */
    private Integer skuType ;

    /**
     * 父类SKU（主要赠品附件用）
     */
    private Long parentSkuId ;

    /**
     * 国家码
     */
    private String skuCountryCode;

    /**
     * SKU信息快照（不含商详）
     */
    private String skuJsonInfo;

    /**
     * 商品入库数量（商品表的加合）
     */
    private Integer inboundWarehouseWareNum;

    /**
     * 版本号
     */
    private Integer version; // 版本号

}
