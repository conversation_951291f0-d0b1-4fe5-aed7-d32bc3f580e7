package com.jdi.isc.aggregate.read.service.mku;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.aggregate.read.common.costants.Constant;
import com.jdi.isc.aggregate.read.service.BaseTest;
import com.jdi.isc.aggregate.read.wiop.api.mku.MkuReadApiService;
import com.jdi.isc.aggregate.read.wiop.api.mku.req.*;
import com.jdi.isc.aggregate.read.wiop.api.mku.resp.*;
import com.jdi.isc.product.soa.api.attribute.common.AttributeDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeLangDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeValueDTO;
import com.jdi.isc.product.soa.api.attribute.common.AttributeValueLangDTO;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.internal.util.collections.Sets;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


public class MkuReadTest extends BaseTest {
    @Resource
    private MkuReadApiService mkuReadApiService;

    @LafValue("jdi.isc.stock.default.addr")
    private String defaultAddr;

    @Test
    public void saleAttributeTest() {
        List<AttributeDTO> sellAttributeList = JSON.parseArray("[\n" +
                "                {\n" +
                "                    \"attributeInputType\": 0,\n" +
                "                    \"attributeType\": 1,\n" +
                "                    \"attributeValueList\": [\n" +
                "                        {\n" +
                "                            \"id\": 18112,\n" +
                "                            \"langList\": [\n" +
                "                                {\n" +
                "                                    \"lang\": \"vi\",\n" +
                "                                    \"langName\": \"mặc định\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"lang\": \"ru\",\n" +
                "                                    \"langName\": \"по умолчанию\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"lang\": \"th\",\n" +
                "                                    \"langName\": \"ค่าเริ่มต้น\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"lang\": \"ms\",\n" +
                "                                    \"langName\": \"lalai\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"lang\": \"en\",\n" +
                "                                    \"langName\": \"default\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"lang\": \"pt_BR\",\n" +
                "                                    \"langName\": \"padrão\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"lang\": \"id\",\n" +
                "                                    \"langName\": \"bawaan\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"lang\": \"zh_Hant\",\n" +
                "                                    \"langName\": \"預設\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"lang\": \"hu\",\n" +
                "                                    \"langName\": \"alapértelmezett\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"lang\": \"zh\",\n" +
                "                                    \"langName\": \"默认\"\n" +
                "                                }\n" +
                "                            ],\n" +
                "                            \"sort\": 1\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"id\": 22100,\n" +
                "                    \"langList\": [\n" +
                "                        {\n" +
                "                            \"lang\": \"en\",\n" +
                "                            \"langName\": \"color\"\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"lang\": \"zh\",\n" +
                "                            \"langName\": \"颜色\"\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"sort\": 1\n" +
                "                }\n" +
                "            ]", AttributeDTO.class);
        Set<String> langList = com.google.common.collect.Sets.newHashSet("en", "zh");
        List<AttributeReadResp> saleAttributeReadRespList = Lists.newArrayList();
        for (AttributeDTO vo : sellAttributeList) {
            // 属性名称映射
            Map<String, String> attributeLangMap = Optional.ofNullable(vo.getLangList()).orElseGet(ArrayList::new).stream().filter(Objects::nonNull).filter(l -> StringUtils.isNotBlank(l.getLangName())).collect(Collectors.toMap(AttributeLangDTO::getLang, AttributeLangDTO::getLangName));

            // 属性值多语言名称映射
            Map<Long, Map<String, String>> valueLangMap = Optional.ofNullable(vo.getAttributeValueList()).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(AttributeValueDTO::getId, valueVo -> valueVo.getLangList().stream().filter(Objects::nonNull).collect(Collectors.toMap(AttributeValueLangDTO::getLang, AttributeValueLangDTO::getLangName))));

            for (String lang : langList) {
                if (!attributeLangMap.containsKey(lang)) {
                    continue;
                }
                AttributeReadResp respDTO = new AttributeReadResp();
                respDTO.setAttributeId(vo.getId());
                respDTO.setLang(lang);
                respDTO.setSort(vo.getSort());
                respDTO.setAttributeName(attributeLangMap.get(lang));
                // 销售属性值
                List<String> valueList = Lists.newArrayList();
                for (Long valueId : valueLangMap.keySet()) {
                    if (MapUtils.isNotEmpty(valueLangMap) && valueLangMap.containsKey(Long.valueOf(valueId)) && StringUtils.isNotBlank(valueLangMap.get(Long.valueOf(valueId)).get(lang))) {
                        valueList.add(valueLangMap.get(Long.valueOf(valueId)).get(lang));
                    }
                }
                respDTO.setAttributeValueList(valueList);

                saleAttributeReadRespList.add(respDTO);
            }
        }
        System.out.println(JSON.toJSONString(saleAttributeReadRespList));
    }

    @Test
    public void getMkuImageList() {
        MkuImageReadReq mkuImageReadReq = new MkuImageReadReq();
        mkuImageReadReq.setMkuIdList(Lists.newArrayList(50000000010L));
//        mkuImageReadReq.setLangList(LangConstant.LANG_LIST);
        mkuImageReadReq.setClientCode("OSDH134KJ35");
        System.out.println(JSON.toJSONString(mkuImageReadReq));
        DataResponse<List<MkuImageReadResp>> mkuImageList = mkuReadApiService.getMkuImageList(mkuImageReadReq);
        System.out.println(JSON.toJSONString(mkuImageList));
    }

    @Test
    public void getMkuStateList() {
        MkuStateReadReq mkuStateReadReq = new MkuStateReadReq();
        mkuStateReadReq.setMkuIdList(Lists.newArrayList(50000000010L));
        mkuStateReadReq.setClientCode("OSDH134KJ35");
        DataResponse<List<MkuStateReadResp>> mkuStateList = mkuReadApiService.getMkuStateList(mkuStateReadReq);
        Assertions.assertNotNull(mkuStateList, "mku state data is null.");
        System.out.println(JSON.toJSONString(mkuStateList));
    }

    @Test
    public void getMkuDetail() {
        MkuDetailReadReq mkuDetailReadReq = new MkuDetailReadReq();
        mkuDetailReadReq.setMkuId(50000000012L);
        mkuDetailReadReq.setExtSet(Sets.newSet(1, 2));
//        mkuDetailReadReq.setLangList(Lists.newArrayList(LangConstant.LANG_ZH));
        mkuDetailReadReq.setClientCode("OSDH134KJ35");
        System.out.println(JSON.toJSONString(mkuDetailReadReq));
        DataResponse<MkuDetailReadResp> mkuDetail = mkuReadApiService.getMkuDetail(mkuDetailReadReq);
        Assertions.assertNotNull(mkuDetail, "mku detail data is null.");
        System.out.println(JSON.toJSONString(mkuDetail));
    }

    @Test
    public void checkMkuSale() {
        CheckMkuSaleReadReq checkMkuSaleReadReq = new CheckMkuSaleReadReq();
        checkMkuSaleReadReq.setMkuIdList(Lists.newArrayList(50000000010L));
//        checkMkuSaleReadReq.setLangList(Lists.newArrayList(LangConstant.LANG_ZH));
        checkMkuSaleReadReq.setClientCode("OSDH134KJ35");
        DataResponse<List<CheckMkuSaleReadResp>> listDataResponse = mkuReadApiService.checkMkuSaleStateList(checkMkuSaleReadReq);
        Assertions.assertNotNull(listDataResponse, "mku sale data is null.");
        System.out.println(JSON.toJSONString(listDataResponse));
    }

    @Test
    public void checkMkuLimit() {
        CheckMkuAreaLimitReadReq checkMkuAreaLimitReadReq = new CheckMkuAreaLimitReadReq();
//        checkMkuAreaLimitReadReq.setLangList(Lists.newArrayList(LangConstant.LANG_ZH));
        checkMkuAreaLimitReadReq.setMkuIdList(Lists.newArrayList(50000000012L));
        // 19,1601,50283,129163
        MkuAreaBaseInfoReadReq mkuAreaBaseInfoReadReq = new MkuAreaBaseInfoReadReq();
        mkuAreaBaseInfoReadReq.setCountyId(1L);
        mkuAreaBaseInfoReadReq.setProvinceId(2L);
        mkuAreaBaseInfoReadReq.setCityId(3L);
        mkuAreaBaseInfoReadReq.setTownId(4L);
        checkMkuAreaLimitReadReq.setMkuAreaBaseInfoReadReq(mkuAreaBaseInfoReadReq);
        checkMkuAreaLimitReadReq.setClientCode("OSDH134KJ35");
        DataResponse<List<CheckMkuAreaLimitReadResp>> listDataResponse = mkuReadApiService.checkMkuAreaLimitList(checkMkuAreaLimitReadReq);
        Assertions.assertNotNull(listDataResponse, "mku check limit area data is null.");
        System.out.println(JSON.toJSONString(listDataResponse));
    }

    @Test
    public void getFixedSku() {
        MkuRefReadReq input = new MkuRefReadReq();
        input.setClientCode("OSDH134KJ35");
        input.setMkuIdList(com.google.common.collect.Sets.newHashSet(50000000010L, 50000000012L));
        DataResponse<Map<Long, Long>> res = mkuReadApiService.getFixedSkuByMkuId(input);
        System.out.println(JSON.toJSONString(res));
    }

    @Test
    public void listMkuClient() {
        MkuClientReadReq input = new MkuClientReadReq();
        input.setClientCode("OSDH134KJ35");
        input.setMkuIdList(com.google.common.collect.Sets.newHashSet(50000000010L, 50000000012L, 50000000014L));

        DataResponse<List<CustomerMkuReadResp>> res = mkuReadApiService.list(input);
        System.out.println(JSON.toJSONString(res));
    }

    @Test
    public void getJdSku() {
        MkuRefReadReq req = new MkuRefReadReq();
        req.setClientCode("OSDH134KJ35");
        req.setMkuIdList(com.google.common.collect.Sets.newHashSet(50000000010L, 50000000012L));
        req.setSourceCountryCode("VN");
        DataResponse<Map<Long, Long>> jdSkuResponse = mkuReadApiService.getJdSkuByMkuIds(req);
        System.out.println(JSON.toJSONString(jdSkuResponse));
    }

    @Test
    public void checkCanPurchase() {
        CheckMkuCanPurchaseReadReq readReq = new CheckMkuCanPurchaseReadReq();
//        readReq.setLangList(Lists.newArrayList(LangConstant.LANG_ZH, LangConstant.LANG_EN, LangConstant.LANG_VN));
        readReq.setMkuIdList(Lists.newArrayList(50000000012L));
        // 19,1601,50283,129163
        MkuAreaBaseInfoReadReq mkuAreaBaseInfoReadReq = new MkuAreaBaseInfoReadReq();
        mkuAreaBaseInfoReadReq.setCountyId(1L);
        mkuAreaBaseInfoReadReq.setProvinceId(2L);
        mkuAreaBaseInfoReadReq.setCityId(3L);
        mkuAreaBaseInfoReadReq.setTownId(4L);
        readReq.setMkuAreaBaseInfoReadReq(mkuAreaBaseInfoReadReq);
        readReq.setClientCode("OSDH134KJ35");
        DataResponse<List<MkuCanPurchaseReadResp>> listDataResponse = mkuReadApiService.checkMkuCanPurchase(readReq);
        Assertions.assertNotNull(listDataResponse, "数据不能为空");
        System.out.println(JSON.toJSONString(listDataResponse));
    }


    @Test
    public void getMkuInfo() {
        MkuRefReadReq mkuRefReadReq = new MkuRefReadReq();
        mkuRefReadReq.setMkuIdList(Sets.newSet(50000000012L));
        mkuRefReadReq.setClientCode("OSDH134KJ35");
        DataResponse<List<MkuInfoReadResp>> listDataResponse = mkuReadApiService.queryMkuInfoByIds(mkuRefReadReq);
        System.out.println(JSON.toJSONString(listDataResponse));
    }

    @Test
    public void defaultAddress() {
        System.out.println(this.buildMkuAreaBaseInfoReadReq());
    }

    private MkuAreaBaseInfoReadReq buildMkuAreaBaseInfoReadReq() {
        MkuAreaBaseInfoReadReq area = new MkuAreaBaseInfoReadReq();
        String[] addrArr = defaultAddr.split(Constant.COMMA);
        area.setProvinceId(Long.valueOf(addrArr[0]));
        area.setCityId(Long.valueOf(addrArr[1]));
        area.setCountyId(Long.valueOf(addrArr[2]));
        area.setTownId(Long.valueOf(addrArr[3]));
        return area;
    }
}
