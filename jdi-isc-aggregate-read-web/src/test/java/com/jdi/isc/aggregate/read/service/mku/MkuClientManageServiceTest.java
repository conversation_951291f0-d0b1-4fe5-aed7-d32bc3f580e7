package com.jdi.isc.aggregate.read.service.mku;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.aggregate.read.domain.mku.biz.MkuClientDetailReqVO;
import com.jdi.isc.aggregate.read.domain.mku.biz.MkuClientGroupVO;
import com.jdi.isc.aggregate.read.domain.mku.biz.MkuClientPageReqVO;
import com.jdi.isc.aggregate.read.domain.mku.biz.MkuClientVO;
import com.jdi.isc.aggregate.read.domain.mku.po.MkuPO;
import com.jdi.isc.aggregate.read.service.BaseTest;
import com.jdi.isc.aggregate.read.service.adapter.mapstruct.mku.MkuConvert;
import com.jdi.isc.aggregate.read.service.manage.mku.MkuClientManageService;
import com.jdi.isc.product.soa.api.common.LangConstant;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2023/12/13 09:28
 **/
@Slf4j
public class MkuClientManageServiceTest extends BaseTest {

    @Resource
    private MkuClientManageService mkuClientManageService;

    @Test
    public void page(){
        List<MkuPO> mkuPOList = new ArrayList<>();
        MkuPO mkuPO = new MkuPO();
        mkuPO.setMkuId(123L);
        mkuPO.setMainImg("http://sssss");
        mkuPO.setBrandId(888L);
        mkuPOList.add(mkuPO);
        List<MkuClientVO> clientVOList = MkuConvert.INSTANCE.mkuPoList2mkuClientVoList(mkuPOList);

        MkuClientPageReqVO input = new MkuClientPageReqVO();
        input.setIndex(1L);
        input.setSize(10L);
        input.setLang(LangConstant.LANG_ZH);
        input.setClientCode("OSDH134KJ35");
        input.setCatId(1L);
//        PageInfo<MkuClientVO> pageInfo = mkuClientManageService.page(input);
//        log.info("pageInfo={}", JSONObject.toJSONString(pageInfo));
    }

    @Test
    public void baseInfo(){
        MkuClientDetailReqVO input = new MkuClientDetailReqVO();
        input.setMkuId(50000000010L);
        input.setLang(LangConstant.LANG_ZH);
        input.setClientCode("OSDH134KJ35");
//        MkuClientVO mkuClientVO = mkuClientManageService.baseInfo(input);
//        log.info("mkuClientVO={}", JSONObject.toJSONString(mkuClientVO));
    }

    @Test
    public void groupInfo(){
        MkuClientDetailReqVO input = new MkuClientDetailReqVO();
        input.setMkuId(50000000010L);
        input.setClientCode("OSDH134KJ35");
        input.setLang(LangConstant.LANG_ZH);

//        MkuClientGroupVO mkuClientGroupVO = mkuClientManageService.groupInfo(input);
//        log.info("mkuClientGroupVO={}", JSONObject.toJSONString(mkuClientGroupVO));
    }
}
