package com.jdi.isc.product.soa.common.util;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import com.jdi.isc.product.soa.common.frame.LangContextHolder;
import com.jdi.isc.product.soa.common.frame.LoginContextHolder;
import com.jdi.isc.product.soa.common.frame.SystemContextHolder;
import lombok.extern.slf4j.Slf4j;

/**
 * zip文件工具类
 *
 * <AUTHOR>
 * @date 2024/08/06
 **/
@Slf4j
public class ApiInitUtils {

    public static void init(BaseReqDTO dto){
        LoginContextHolder loginContextHolder = new LoginContextHolder();
        loginContextHolder.setPin(dto.getPin());
        LoginContextHolder.setLoginContextHolder(loginContextHolder);

        LangContextHolder.init(dto.getLang());

        SystemContextHolder.init(dto.getSystemCode());
    }


    public static void init(String pin,String lang,String systemCode) {
        LoginContextHolder loginContextHolder = new LoginContextHolder();
        loginContextHolder.setPin(pin);
        LoginContextHolder.setLoginContextHolder(loginContextHolder);

        LangContextHolder.init(lang);
        SystemContextHolder.init(systemCode);
    }

    /**
     * 初始化登录上下文并设置用户PIN码
     * @param pin 用户唯一标识PIN码
     */
    public static void init(String pin) {
        LoginContextHolder loginContextHolder = new LoginContextHolder();
        loginContextHolder.setPin(pin);
        LoginContextHolder.setLoginContextHolder(loginContextHolder);
    }

}
