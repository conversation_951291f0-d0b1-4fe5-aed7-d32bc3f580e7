package com.jdi.isc.product.soa.common.constants;


import com.google.common.collect.Sets;
import com.jdi.isc.product.soa.api.common.LangConstant;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 通用常量信息
 * <AUTHOR>
 * @date 20230815
 **/
public class Constant {


    /** 分隔符-逗号*/
    public final static String COMMA = ",";
    /** 分隔符-顿号*/
    public final static String SYMBOL_COMMA = "、";
    /** 分隔符-点*/
    public final static String DOT = ".";
    /**
     * 分隔符减号
     */
    public final static String MINUS = "-";
    /** 分隔符-下划线*/
    public final static String UNDER_LINE = "_";
    /** 分隔符-井号 */
    public final static String HASHTAG = "#";
    /** 分隔符-冒号 */
    public final static String COLON = ":";
    public final static String STAR = "*";
    /** 分割符-阶乘*/
    public final static String FACTORIAL = "^";
    /**
     * 分隔符-阶乘
     */
    public final static String FACTORIAL_REGEX = "\\^";
    /**
     * 分隔符斜杠
     */
    public final static String SLASH = "/";

    public final static String DOUBLE_SLASH = "//";
    /**
     * 左中括号
     */
    public final static String LEFT_BRACKET = "[";
    /**
     * 右中括号
     */
    public final static String RIGHT_BRACKET = "]";

    public final static String LEFT_PARENTHESES ="(";
    public final static String RIGHT_PARENTHESES =")";

    /** 分隔符-类目路径 */
    public final static String CATEGORY_PATH_SEPARATOR = ">";
    /**
     * 分隔符-百分号
     */
    public final static String PERCENT_SIGN = "%";
    /**
     * 空格
     */
    public final static String WHITE_SPACE = " ";

    public final static String CHINESE_COMMA = "，";
    public final static String CHINESE_COLON = "：";
    // 全角井号
    public final static String CHINESE_HASHTAG = "＃";

    /** 类目根目录ID，根目录下挂载的是一级目录 */
    public final static long CATEGORY_ROOT_ID = 0L;
    /** 类目层级深度*/
    public final static int CATEGORY_LEVEL_DEPTH = 4;
    /** 类目绑定销售属性时，可绑定的属性数量上限*/
    public final static long CATEGORY_ATTRIBUTE_SELL_BIND_LIMIT = 6L;
    /** 系统操作 */
    public final static String SYSTEM = "system";

    /** 批量最多20个*/
    public final static int PARTITION_SIZE =20;

    /** 批量处理时，单次处理数据大小*/
    public final static long BATCH_SIZE =1000;

    /** 销端商品列表页面 每页展示商品数量上限*/
    public final static int CLIENT_MAX_PAGE_SIZE =50;


    /** 1分钟的秒数*/
    public final static long SECONDS_ONE_MINUTE = 60;

    /** 5分钟的秒数*/
    public final static long SECONDS_5_MINUTES = 60*5;

    /** 10分钟的秒数*/
    public final static long SECONDS_10_MINUTES = 60*10;

    /** 半小时的秒数*/
    public final static long SECONDS_HALF_HOUR = 60*30;

    /** 1小时的秒数*/
    public final static long SECONDS_ONE_HOUR = 60*60;

    /** 24小时的秒数*/
    public final static long SECONDS_24_HOURS = 60*60*24;

    /** 48小时的秒数*/
    public final static long SECONDS_48_HOURS = 60*60*24*2;

    /** 7天的秒数*/
    public final static long SECONDS_7_DAYS = 60*60*24*7;

    /** 商品金额的精度 */
    public final static int PRODUCT_SCALE_MONEY = 2;

    /** 计算运费保留的默认精度 */
    public final static int FREIGHT_SCALE = 12;

    /** 计算运费体积相关精度。立方毫米转立方米后有9位，长(mm)、宽(mm)、高(mm)本身允许有两位小数。加起来至少要11位 */
    public final static int FREIGHT_SCALE_VOLUME = 12;

    /** 计算运费时重量相关的精度。克转千克后有3位，重量(g)本身允许有两位小数。加起来至少要5位 */
    public final static int FREIGHT_SCALE_WEIGHT = 6;

    /** 计算运费时金额的精度 */
    public final static int FREIGHT_SCALE_MONEY = 2;

    /** 计算运费，托盘公式计算结果精度*/
    public final static int FREIGHT_SCALE_PLATE_FORMULA = 0;

    /** 计算运费，重量、体积上浮比例*/
    public final static double FREIGHT_WEIGHT_VOLUME_UP_RATE = 1.1;

    /** 税金相关精度：金额分摊的cif运费公式中，销售价金额占比精度 */
    public final static int ORDER_TAX_SCALE_PRICE_PROPORTION = 2;
    /** 税金相关精度：税金精度 */
    public final static int ORDER_TAX_SCALE_MONEY = 2;

    /**
     * 区域根ID
     */
    public final static long AREA_ROOT_ID = -1L;

    /**
     * 属性值“其它”
     */
    public final static String ATTRIBUTE_VAL_OTHER_ZH = "其它";

    /**
     * 属性值“其它”
     */
    public final static String ATTRIBUTE_VAL_OTHER_VN = "khác";

    /**
     * 属性值为“其它”的排序值
     */
    public final static int ATTRIBUTE_VAL_OTHER_SORT = 9999;
    /**
     * kg换算g
     */
    public final static int THOUSAND = 1000;

    public static final int MAX_FILE_SIZE = 3 * 1024 * 1024;
    /**
     * 供应商文件大小
     */
    public static final int MAX_FILE_SIZE_SUPPLIER = 10 * 1024 * 1024;

    public static final int MAX_UPLOAD_FILE_SIZE = 10 * 1024 * 1024;

    public static final int MAX_MEMORY_SIZE = 1024 * 1024 * 1024;
    /**
     * 图床上传图片最大支持1m
     */
    public static final int ALBUM_IMAGE_SIZE = 3 * 1024 * 1024;

    public static final int WAREHOUSE_IMAGE_SIZE = 10 * 1024 * 1024;

    /**
     * 图片类型集合
     */
    public static final Set<String> IMAGE_TYPE_SET = Sets.newHashSet("jpg", "jpeg", "png", "JPG", "JPEG", "PNG");
    /**
     * 文件格式
     */
    public static final Set<String> FIlE_TYPE_SET = Sets.newHashSet("xlsx", "xls", "pdf", "doc", "docx");

    public static final String MAX_PRICE = "999999999999.99";

    public static final BigDecimal W10_BIGDECIMAL = new BigDecimal("9999999999");

    public static final String XLSX = "xlsx";
    public static final String XLS = "xls";
    public static final String ZIP = "zip";


    public static final String PURCHASE_RATE = "0.85";

    public static final String HTTPS = "https";
    public static final String HTTP = "http";

    /**
     * 监控采购价格，单次处理数据大小
     */
    public final static long PURCHASE_PRICE_BATCH_SIZE = 100L;

    /**
     * pin值
     */
    public static final String PIN_SYSTEM = "system";

    /**
     * Excel临时文件前缀~$
     */
    public static final String EXCEL_TEMP_PREFIX = "~$";

    /**
     * 税率精度
     */
    public final static int ORDER_TAX_SCALE_RATE = 4;

    /**
     * 所有客户
     */
    public final static String CUSTOMER_ALL = "all";
    /**
     * 通用客户名称
     */
    public final static String CUSTOMER_ALL_NAME = "通用";

    public static final String SETTLE_INFO = "settleInfo";

    public static final String AES_ENCRYPT_KEY = "eb64rJZA0Hjw7PY7x7Us+A@=eb64rJZA";

    public static final int MAX_UPLOAD_IMAGE_NUM = 50;

    public static final String SYSTEM_CODE = "WIMP";

    public static final String VC_SYSTEM_CODE = "VC";


    public static final String CERTIFICATE = "CERTIFICATE";

    /** 显示地址使用的默认语言 */
    public static final String SHOW_AREA_DEFAULT_LANG = LangConstant.LANG_EN;

    /** 泰国ID */
    public static final long AREA_ID_TH = 764L;
    /** 泰国ID最小值 */
    public static final long AREA_ID_TH_MIN = 184549376L;
    /** 泰国ID最大值 */
    public static final long AREA_ID_TH_MAX = 1460469763L;

    /** 中国ID最小值 */
    public static final long AREA_ID_CN_MIN = 0L;
    /** 中国ID最大值 */
    public static final long AREA_ID_CN_MAX = 999999L;

    /** 弃用ID（阿联酋）最小值 */
    public static final long AREA_ID_ABANDON_MIN = 10000000L;
    /** 弃用ID（阿联酋）最大值 */
    public static final long AREA_ID_ABANDON_MAX = 10000003L;

    public static final String SOLD_OUT = "无货";



    public static final int PAGE_SIZE = 100;

    /**
     * 1小时 (毫秒)
     */
    public static final int EXTEND_TIME = 60 * 60 * 1000;

    /**
     * 资质和属性绑定类目场景下，表示选中全部类目
     */
    public static final Long DEFAULT_ALL_CATEGORY_SELECT = -100L;
    public static final String DEFAULT_ALL_CATEGORY_SELECT_NAME = "全部类目";

    public static final Long SKU_BEGIN_ID = 80000000000L;

    public static final Long ZERO = 0L;

    public static final Long MAX = 9999999999999L;

    /**
     * 英文分号
     */
    public static final String SEMICOLON = ";";

    public static final String FRONT_LINE = "<br/>";

    /** 主键 */
    public final static String ID = "id";
    /** 值 */
    public final static String VALUE = "value";

    /** 100 */
    public final static BigDecimal DECIMAL_HUNDRED = new BigDecimal(100);
    /**
     * 厂直默认仓编码
     */
    public static final Long FACTORY_DEFAULT_ID = -1L;
    public static final String FACTORY_DEFAULT_ID_STR = "-1";

    /**
     * 一次发品sku最大数量
     */
    public static final int MAX_SKU_AMOUNT=100;

    /**
     * 每个sku销售属性最大数量
     */
    public static final int SALE_ATTIRBUTE_MAX_AMOUNT=2;

    /**
     * 语言库的URL地址
     */
    public static final String LANG_LIBRARY_URL = "https://jdios.jdindustry.com/langLibrary/";

    /**
     * 系统自动撤销
     */
    public static final String REVOKE_REASON = "system revoke";

    public static final int ONE = 1;

    /**
     * 定义小数点后保留的位数，用于计算和显示价格等数值。
     */
    public static final int SCALE_TWO = 2;
    public static final String UP = "上架";
    public static final String DOWN = "下架";

    public static final String CNSJGY = "cnsjgy";
    /**匈牙利IOP地址ID编码**/
    public static final String HU_IOP_ADDRESS_ID = "7";

    public static final String SUCCESS = "success";
    public static final String FAILED = "failed";

    /**
     * 线下合同
     */
    public static final Integer OFFLINE_CONTRACT = 2;

    /**
     * 默认属性组id
     */
    public final static Integer DEFAULT_ATTR_GROUP_ID = 0;

    public static final String DEFAULT_LANG = "zh";
    /**
     * 默认销售属性id “其它”
     */
    public static final Long DEFAULT_SALE_ATTRIBUTE_ID = 100100L;


    /**
     * 仓-商品列表查询，最大查询限制
     */
    public final static int WMS_SKU_MAX_PAGE_SIZE = 20;

    /**
     * 跨境品每个spu最多sku数量
     */
    public final static int MAX_CROSS_BOARDER_SKU_NUM_PER_SPU = 1;

    public static final Set<String> TEST_DEV_ENV = Sets.newHashSet("dev","test");

    /**
     * 默认销售属性值名称
     */
    public static final String DEFAULT_SALE_ATTRIBUTE_VALUE_NAME = "默认";

    /**
     * 任务执行状态-成功
     */
    public final static String TASK_STATUS_SUCCESS = "SUCCESS";
    /**
     * 任务执行状态-失败
     */
    public final static String TASK_STATUS_FAILURE = "FAILURE";

    /**
     * 每个sku最多销售属性值个数，最多一个图销一个文销，也就是两个
     */
    public final static int MAX_SALE_ATTRIBUTE_NUM_PER_SKU = 2;

    /**
     * 默认销售属性值id，用于兼容，后续供应商和wiop都修改适配后删除
     */
    public final static long DEFAULT_IMAGE_SALE_ATTRIBUTE_VALUE_ID = Long.MAX_VALUE;
    public final static long DEFAULT_TEXT_SALE_ATTRIBUTE_VALUE_ID = Long.MAX_VALUE-1;

    /**
     * 默认销售属性值名称及其多语言，用于兼容，后续供应商和wiop都修改适配后删除
     */
    public final static Map<String,String> DEFAULT_SALE_ATTRIBUTE_VALUE_NAME_LANG_MAP = new HashMap<String,String>() {{
        put("vi","mặc định");
        put("ru","по умолчанию");
        put("th","ค่าเริ่มต้น");
        put("ms","lalai");
        put("en","default");
        put("pt_BR","padrão");
        put("id","bawaan");
        put("zh_Hant","預設");
        put("hu","alapértelmezett");
        put(DEFAULT_LANG,"默认");
    }};

}
