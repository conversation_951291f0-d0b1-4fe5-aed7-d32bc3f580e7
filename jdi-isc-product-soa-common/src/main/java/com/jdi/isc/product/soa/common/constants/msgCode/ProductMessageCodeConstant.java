package com.jdi.isc.product.soa.common.constants.msgCode;


/**
 * 商品相关提示错误码
 * <AUTHOR>
 * @description：
 * @Date 2025-06-21
 */

public class ProductMessageCodeConstant {

    /**
     * sku 11xx
     * spu 12xx
     * mku 13xx
     */
    public static final String SKU_REPEAT_SALE_ATTRIBUTE_ERROR = "1101";
    public static final String SKU_SALE_ATTRIBUTE_DOWN_DIMENSION_ERROR = "1102";
    public static final String SKU_CREATE_ERROR = "1103";
    public static final String SKU_JD_SKU_BIND_ERROR = "1104";
    public static final String SKU_PURCHASE_PRICE_MAX_ERROR = "1105";
    public static final String SKU_SALE_PRICE_MAX_ERROR = "1106";
    public static final String SKU_INTER_ATTRIBUTE_NOT_EXIST_ERROR = "1107";
    public static final String SKU_INTER_CERT_NOT_EXIST_ERROR = "1108";
    /**SKU特殊属性不存在*/
    public static final String SKU_SPECIAL_ATTR_NOT_EXIST_ERROR = "1109";
    public static final String SKU_SPECIAL_ATTR_VALUE_NOT_EXIST_ERROR = "1110";
    public static final String SKU_NOT_EXIST_ERROR = "1111";
    public static final String SKU_SPECIAL_ATTR_COUNTRY_NOT_EXIST_ERROR = "1112";
    public static final String SKU_SPECIAL_ATTR_ERP_NOT_EXIST_ERROR = "1113";

    public static final String SPU_CREATE_ERROR = "1201";
    public static final String SPU_UPDATE_ERROR = "1202";
    public static final String SPU_REJECT_ERROR = "1203";
    public static final String SPU_APPROVED_ERROR = "1204";
    public static final String SPU_AUDIT_LOCK_FAILED = "1205";
    public static final String SPU_NUMBER_WRONG_ERROR = "1206";
    public static final String SPU_BRAND_NOT_EXIST_ERROR = "1207";
    public static final String SPU_EXTEND_ATTRIBUTE_NOT_EXIST_ERROR = "1208";
    public static final String SPU_MUST_EXTEND_ATTRIBUTE_EMPTY_ERROR = "1209";
    public static final String SPU_NOT_EXIST_ERROR = "1210";
    public static final String SPU_EXIST_ONE_SALE_ERROR = "1211";
    public static final String SPU_NOT_CHANGE_ERROR = "1212";
    public static final String SPU_CATEGORY_NOT_EXIST_ERROR = "1213";
    public static final String SPU_WEIGHT_EMPTY_ERROR = "1214";
    public static final String SPU_LENGTH_EMPTY_ERROR = "1215";
    public static final String SPU_WIDTH_EMPTY_ERROR = "1216";
    public static final String SPU_HEIGHT_EMPTY_ERROR = "1217";
    public static final String SPU_STOCK_NUMBER_ERROR = "1218";

    public static final String SPU_AUDIT_CAN_NOT_REJECT_ERROR = "1219";
    public static final String SPU_INTER_ATTRIBUTE_NOT_EXIST_ERROR = "1220";
    public static final String SPU_INTER_CERT_NOT_EXIST_ERROR = "1221";



    // mku审批拒绝备注校验失败
    public static final String MKU_AUDITION_REJECT_REMARK_ERROR = "1301";
    public static final String MKU_RELATION_LOCK_FAIL = "1311";
    public static final String MKU_RELATION_UNBIND_FIXED_SKU = "1312";
    public static final String MKU_RELATION_UNBIND_EXIST = "1313";
    public static final String MKU_RELATION_UNBIND_LAST_ONE = "1314";
    public static final String MKU_RELATION_BIND_SKU_MAX = "1315";
    public static final String MKU_RELATION_BIND_ATTR_CAT_ID = "1316";
    public static final String MKU_RELATION_BIND_ATTR_BRAND_ID = "1317";
    public static final String MKU_RELATION_BIND_ATTR_SALE = "1318";
    public static final String MKU_RELATION_BIND_ATTR_EXT = "1319";

    public static final String MKU_RELATION_BIND_ATTR_COUNTRY_CODE = "1320";
    public static final String MKU_RELATION_BIND_EXIST = "1321";

    public static final String MKU_RELATION_BIND_EXIST_OTHER = "1322";

    public static final String MKU_AUDIT_LOCK_FAIL = "1341";
    public static final String MKU_INFO_UPDATE_NOTHING = "1342";

    public static final String ORDER_DELIVERY_ORDER_ID_EXIST = "2401";

    public static final String ORDER_DELIVERY_ORDER_STATUS_ERROR = "2402";
    public static final String ORDER_DELIVERY_NUMBER_NOT_EXIST_ERROR = "2403";
    public static final String ORDER_DELIVERY_NODE_EXISTED_ERROR = "2404";

    /**MKU国家池状态错误码*/
    private final String MKU_COUNTRY_POOL_PREFIX = "MCP";
    // 不在国家池
    public static final String MKU_COUNTRY_POOL_OUT_POOL = "MCP4001";
    /** MKU客户状态错误码*/
    private final String MKU_CUSTOMER_PREFIX = "MCT";
    // 不在客户池
    public static final String MKU_CUSTOMER_AVAILABLE_OUT_POOL = "MCT5001";



}
