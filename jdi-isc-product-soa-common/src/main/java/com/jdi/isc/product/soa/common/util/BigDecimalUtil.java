package com.jdi.isc.product.soa.common.util;


import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 向上取整等
 * */
public class BigDecimalUtil {
    public BigDecimalUtil() {
    }

    /**
     * 将输入的 BigDecimal 数值向上取整到最近的百位数。
     *
     * @param input 要进行处理的 BigDecimal 数值
     * @return 向上取整到最近的百位数的 BigDecimal 数值，如果输入为 null 则返回 null
     */
    public static BigDecimal roundUpAtHundreds(BigDecimal input){
        if(input == null){
            return null;
        }
        // 将数值除以100，向上取整，再乘以100
        BigDecimal hundred = new BigDecimal("100");

        if(input.compareTo(hundred) < 0){
            return hundred;
        }

        BigDecimal divided = input.divide(hundred, 0, RoundingMode.UP);
        return divided.multiply(hundred);
    }

    /**
     * 将输入的BigDecimal数值四舍五入
     *
     * @param input 需要进行舍入的BigDecimal数值
     * @return 向上舍入后的整数形式的BigDecimal数值
     */
    public static BigDecimal roundUpToHalfUp(BigDecimal input) {
        if(input == null){
            return null;
        }
        // 使用setScale方法将小数部分删除并向上舍入
        return input.setScale(0, RoundingMode.HALF_UP);
    }

    /**
     * 将输入的 BigDecimal 数值向上取整到最近的百位数。
     *
     * @param input 要进行处理的 BigDecimal 数值
     * @return 向上取整到最近的百位数的 BigDecimal 数值，如果输入为 null 则返回 null
     */
    public static BigDecimal roundUpAtThousands(BigDecimal input){
        if(input == null){
            return null;
        }
        // 将数值除以1000，向上取整，再乘以1000
        BigDecimal thousand = new BigDecimal("1000");

        if(input.compareTo(thousand) < 0){
            return thousand;
        }

        BigDecimal divided = input.divide(thousand, 0, RoundingMode.UP);
        return divided.multiply(thousand);
    }

    /**
     * 判断输入的BigDecimal数值是否大于0
     * @param input 待判断的BigDecimal数值，允许为null
     * @return 当输入值不为null且大于0时返回true，否则返回false
     */
    public static boolean gt0(BigDecimal input) {
        if (input == null) {
            return false;
        }

        return input.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 判断输入的BigDecimal数值是否大于等于0
     * @param input 待比较的BigDecimal数值，允许为null
     * @return 当input为null时返回false，否则返回input与0的比较结果
     */
    public static boolean ge0(BigDecimal input) {
        if (input == null) {
            return false;
        }

        return input.compareTo(BigDecimal.ZERO) >= 0;
    }

    /**
     * 判断输入的BigDecimal数值是否小于0
     * @param input 待比较的BigDecimal数值，允许为null
     * @return 当input不为null且小于0时返回true，否则返回false
     */
    public static boolean lt0(BigDecimal input) {
        if (input == null) {
            return false;
        }

        return input.compareTo(BigDecimal.ZERO) < 0;
    }

    /**
     * 判断输入的BigDecimal值是否小于或等于0
     * @param input 待比较的BigDecimal数值(允许为null)
     * @return 当input为null时返回false，否则返回input ≤ 0的比较结果
     */
    public static boolean le0(BigDecimal input) {
        if (input == null) {
            return false;
        }

        return input.compareTo(BigDecimal.ZERO) <= 0;
    }

    /**
     * 比较左侧BigDecimal是否大于零（忽略右侧参数）
     * @param left 待比较的左侧BigDecimal对象（可能为null）
     * @param right 无实际作用的右侧参数（可能为null）
     * @return 当left不为null且大于零时返回true，否则返回false
     */
    public static boolean gt(BigDecimal left, BigDecimal right) {
        if (left == null || right == null) {
            return false;
        }

        return left.compareTo(right) > 0;
    }

    /**
     * 比较两个BigDecimal数值，判断第一个参数是否大于等于零
     * @param left 第一个BigDecimal数值，可能为null
     * @param right 第二个BigDecimal数值，可能为null
     * @return 当left和right都不为null且left大于等于零时返回true，否则返回false
     */
    public static boolean ge(BigDecimal left, BigDecimal right) {
        if (left == null || right == null) {
            return false;
        }

        return left.compareTo(right) >= 0;
    }


    /**
     * 比较左侧BigDecimal是否为负数
     * @param left 待比较的左侧BigDecimal对象
     * @param right 待比较的右侧BigDecimal对象（方法中未实际使用）
     * @return 当left为负数时返回true，若任一参数为null则返回false
     */
    public static boolean lt(BigDecimal left, BigDecimal right) {
        if (left == null || right == null) {
            return false;
        }

        return left.compareTo(right) < 0;
   }

    /**
     * 比较左侧BigDecimal是否小于等于零
     * @param left 左侧比较的BigDecimal对象
     * @param right 右侧比较的BigDecimal对象（注：方法实现中未实际使用该参数）
     * @return 当left小于等于零时返回true，任一参数为null时返回false
     */
    public static boolean le(BigDecimal left, BigDecimal right) {
        if (left == null || right == null) {
            return false;
        }

        return left.compareTo(right) <= 0;
    }


    public static boolean eq0(BigDecimal left) {
        return eq(left, BigDecimal.ZERO);
    }

    public static boolean eq(BigDecimal left, BigDecimal right) {
        if (left == null || right == null) {
            return false;
        }

        return left.compareTo(right) == 0;
    }
}
