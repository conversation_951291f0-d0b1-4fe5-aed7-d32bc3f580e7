package com.jdi.isc.product.soa.common.enums;

/**
 * 角色枚举
 */
public enum RoleEnum {
    STORE_MANAGER("store_manager", "国家店长"),
    PRODUCT_OPERATOR("product_operator", "商品经理"),
    TAX_BR("tax_bp", "税务"),
    ;

    private String code;
    private String desc;
    private RoleEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
