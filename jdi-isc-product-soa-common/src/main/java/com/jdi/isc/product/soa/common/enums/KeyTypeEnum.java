package com.jdi.isc.product.soa.common.enums;

public enum KeyTypeEnum {
    SKU(1, "sku"),
    SPU(2, "spu"),
    SPU_DRAFT(3, "spuDraft"),
    MKU(4, "mku"),
    SPECIAL_ATTR(5, "specialAttr"),
    COUNTRY_POOL(6, "countryPool"),
    SKU_PRICE(7,"skuPrice"),
    MKU_LANG(8,"mkuLang"),
    MARKUP_RATE(9,"bizNo"),
    AGREEMENT_PRICE(10,"bizNo"),
    AGREEMENT_PRICE_DRAFT(11,"bizNo"),
    SKU_PRICE_DRAFT(12,"skuPriceDraft"),
    SKU_PRICE_FORMAL(13,"skuPriceFormal"),
    TAX_REFUND_RATE(14,"bizNo"),
    SKU_MAIN_SYNC_SWITCH(15,"skuMainSyncSwitch"),
    CUSTOMER_POOL(16,"customerMku"),
    SPU_LANG(17,"spuLang"),
    SPU_DRFAT_LANG(18,"spuDraftLang"),
    ;

    private Integer code;
    private String desc;

    private KeyTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
