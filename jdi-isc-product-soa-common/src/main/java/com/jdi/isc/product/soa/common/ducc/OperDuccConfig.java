package com.jdi.isc.product.soa.common.ducc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.util.ConfigUtils;
import com.jdi.isc.product.soa.domain.attribute.biz.CrossAttributeChangeConfig;
import com.jdi.isc.product.soa.domain.common.biz.DataIsolationQueryVO;
import com.jdi.isc.product.soa.domain.countryMku.biz.CountryPoolSwitchVO;
import com.jdi.isc.product.soa.domain.countryMku.biz.RuleEngineVO;
import com.jdi.isc.product.soa.domain.ducc.ChatGptProperties;
import com.jdi.isc.product.soa.domain.ducc.PreselectionVO;
import com.jdi.isc.product.soa.domain.ducc.PriceConfig;
import com.jdi.isc.product.soa.domain.enums.xbp.XbpBizModleEnum;
import com.jdi.isc.product.soa.domain.price.biz.SkuPurchasePriceMonitorConfig;
import com.jdi.isc.product.soa.api.common.AuditColumnConfigDTO;
import com.jdi.isc.product.soa.domain.supplier.biz.SupplierFlowApprovers;
import com.jdi.isc.product.soa.domain.taxRate.biz.BrIcmsRateConfig;
import com.jdi.isc.product.soa.domain.xbp.biz.XbpDuccBizConfigVO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2024/4/24 21:21
 **/
@Slf4j
@Component
public class OperDuccConfig {

    @LafValue("jdi.isc.xbp.bizConfig")
    private String xbpBizConfig;

    @LafValue("jdi.isc.xbp.biz.supplier.flow.approvers")
    private String supplierFlowApprovers;

    /**
     * xbp单个富文本字段最大字节数，单位KB。
     */
    @LafValue("jdi.isc.xbp.RichText.MaxSize")
    private Long xbpRichTexMaxSize;

    @LafValue("jdi.isc.jinmen.customer")
    private String jinmenClientCodes;

    @LafValue("jdi.isc.rule.engine")
    private String ruleEngine;


    @LafValue("jdi.isc.country.pool.switch")
    private String countryPoolSwitch;

    @LafValue("jdi.isc.erp.clientCode.preselection")
    private String preselectionConfig;

    @LafValue("chatGpt.config")
    private String chatGptConfig;

    @LafValue("jdi.isc.offlineContract")
    private String offlineContract;

    @LafValue("${jdi.isc.customerOrderWaybill}")
    private String customerOrderWaybill;

    @LafValue("jdi.isc.price.config")
    private String priceConfig;

    @LafValue("jdi.isc.country.client.relation")
    private String countryClientRelation;

    @LafValue("jdi.isc.chatgpt")
    private String chatgpt;


    @LafValue("jdi.isc.attribute.cross.change")
    private String changeCrossAttribute;

    @LafValue("jdi.isc.open.platform.vc.relation")
    private String openSupplier;

    @LafValue("jdi.isc.open.soa.test.cat.erp")
    private String filterErp;

    @LafValue("jdi.isc.open.soa.price.jdTaxRefundPrice")
    private String jdTaxRefundPriceConfig;

    @LafValue("jdi.isc.open.soa.price.jdTaxRefundPrice.sku")
    private String jdTaxRefundPriceSkuConfig;

    @LafValue("jdi.isc.gray.jdCatIds")
    private String grayJdCatIds;
    @LafValue("jdi.isc.product.soa.agreementPrice.switch")
    private String agreementPriceSwitch;

    @Value("${topic.jmq4.product.isc.approveOrder}")
    private String approveOrderTopic;

    @LafValue("jdi.isc.wisp.search.prompts")
    private String wispSearchPrompts;

    @LafValue("jdi.isc.test.supplier")
    private String supplier;

    @Value("${jdi.isc.dataIsolation.config}")
    private String dataIsolation;

    @Value("${jdi.isc.dataIsolation.adminErp}")
    private String authAdminErp;

    @Value("${jdi.isc.queryMkuAvailable.useStatus}")
    private Integer queryMkuAvailableUseStatus;

    @LafValue("jdi.isc.category.replace.contractList")
    private String contractList;

    @Value("${topic.jmq4.product.isc.initCountryAgreementTopic}")
    private String initCountryAgreementTopic;

    @Value("${topic.jmq4.product.isc.priceWarnTopic}")
    @Getter
    private String priceWarnTopic;

    @Value("${jdi.isc.price.rate.br.icms}")
    private String priceRateBrIcms;

    public List<String> getSupplierList(){
        try{
            return Arrays.asList(supplier.split(Constant.COMMA));
        }catch (Exception e){
            log.info("OperDuccConfig.getSupplierList supplier:{}",JSON.toJSONString(supplier));
            return new ArrayList<>();
        }
    }


    /**
     * 解析agreementPriceSwitch配置，返回国家Code码和开关类型。
     * @return 价格转换映射，键为国家Code码（VN、TH、HU等），值为开关类型（1:协议价为空，自动设置国家协议价。2:不自动设置国家协议价，3:自动设置国家协议价，4:vip价同步到国家协议价）。
     * 配置开关：1、首次（国家协议价为空）自动设置国家协议价；2、不自动联动设置国家协议价；3、自动联动设置国家协议价；4、按客户vip价更新国家协议价（支持配置多个客户，适用巴西场景）配置开关按国家设置
     * 配置开关1：当前现状，不做特殊处理
     * 配置开关2：没有手动设置的情况下国家协议价不做任何变动
     * 配置开关3：所有的国家成本价的变动自动联动国家协议价修改，当国家成本价为空的时候国家协议价也为空
     * 配置开关4（仅限巴西场景）：在1的基础上（为空的时候设置，不为空不设置），不论国家协议价当前状态当有指定客户范围的vip价审核通过，按vip价写入国家协议价
     */
    public Map<String, Integer> agreementPriceSwitch(){
        try{
            return JSON.parseObject(agreementPriceSwitch, new TypeReference<Map<String, Integer>>(){});
        }catch (Exception e){
            log.error("OperDuccConfig.getJdTaxRefundPriceSkuConfig agreementPriceSwitch:{}",JSON.toJSONString(agreementPriceSwitch),e);
            return new HashMap<>();
        }
    }


    public List<String> getFilterErp(){
        try{
            return Arrays.asList(filterErp.split(Constant.COMMA));
        }catch (Exception e){
            log.info("OperDuccConfig.getFilterErp filterErp:{}",JSON.toJSONString(filterErp));
            return new ArrayList<>();
        }
    }

    /**
     * 审核自定义列配置
     */
    @LafValue("jdi.isc.price.audit.formData")
    private String processFromData;

    public PriceConfig priceConfig(){
        try {
            PriceConfig config = JSON.parseObject(priceConfig, PriceConfig.class);
            log.info("OperDuccConfig.priceConfig chatGpt:{}",JSON.toJSONString(priceConfig));
            return config;
        }catch (Exception e){
            PriceConfig config = new PriceConfig();
            log.error("OperDuccConfig.priceConfig 解析priceConfig配置异常，config:{},e:{}",JSON.toJSONString(config),e.getMessage(),e);
            return config;
        }
    }
    public ChatGptProperties preseChatGpt(){
        try {
            ChatGptProperties chatGptProperties = JSON.parseObject(chatGptConfig, ChatGptProperties.class);
            log.info("OperDuccConfig.preseChatGpt chatGpt:{}",JSON.toJSONString(chatGptProperties));
            return chatGptProperties;
        }catch (Exception e){
            ChatGptProperties chatGptProperties = new ChatGptProperties();
            log.error("OperDuccConfig.preseChatGpt 解析chatGptConfig配置异常，e:{}",e.getMessage(),e);
            return chatGptProperties;
        }
    }

    public ChatGptProperties getChatgptByKey(String key){
        try {
            String chatgpts = ConfigUtils.getStringByKey(chatgpt,key);
            ChatGptProperties chatGptProperties = JSON.parseObject(chatgpts, ChatGptProperties.class);
            log.info("OperDuccConfig.getChatgptByKey chatGpt:{}",JSON.toJSONString(chatGptProperties));
            return chatGptProperties;
        }catch (Exception e){
            ChatGptProperties chatGptProperties = new ChatGptProperties();
            log.error("OperDuccConfig.getChatgptByKey 解析chatGptConfig配置异常，e:{}",e.getMessage(),e);
            return chatGptProperties;
        }
    }

    /**
     * 获取预选配置列表
     * @return 预选配置列表
     */
    @PFTracing
    public List<PreselectionVO> preselectionList(){
        List<PreselectionVO> configList = JSONArray.parseArray(preselectionConfig, PreselectionVO.class);
        if (null==configList){
            return new ArrayList<>();
        }
        return configList;
    }

    /**
     * 价格监控配置
     */
    @LafValue("jdi.isc.sku.price.monitor.config")
    private String skuPriceMonitorConfig;

    /**
     * 获取国家池开关配置信息。
     * @return 国家池开关配置信息对象，若解析配置异常则返回 null。
     */
    public CountryPoolSwitchVO getCountryPool() {
        try {
            CountryPoolSwitchVO countryPoolSwitchVO = JSON.parseObject(countryPoolSwitch, CountryPoolSwitchVO.class);
            return countryPoolSwitchVO;
        }catch (Exception e){
            CountryPoolSwitchVO countryPoolSwitchVO = new CountryPoolSwitchVO();
            countryPoolSwitchVO.setPoolSwitch(false);
            log.error("OperDuccConfig.getCountryPool 解析countryPoolSwitch配置异常，e:{}",e.getMessage(),e);
            return countryPoolSwitchVO;
        }
    }

    /**
     * 从ruleEngine中解析出所有的规则引擎配置信息。
     * @return 解析后的规则引擎配置信息列表。
     */
    public List<RuleEngineVO> ruleEngine(){
        List<RuleEngineVO> ruleEngineList = null;
        try {
             ruleEngineList = JSONArray.parseArray(ruleEngine, RuleEngineVO.class);
            if (null == ruleEngineList){
                return new ArrayList<>();
            }
        }finally {
            log.info("OperDuccConfig.ruleEngine ruleEngineList:{}",JSON.toJSONString(ruleEngineList));
        }
        return ruleEngineList;
    }

    /**
     * xbp业务流程配置
     * @return
     */
    public List<XbpDuccBizConfigVO> xbpBizConfigList(){
        List<XbpDuccBizConfigVO> configList = JSONArray.parseArray(xbpBizConfig, XbpDuccBizConfigVO.class);
        if (null==configList){
            return new ArrayList<>();
        }
        return configList;
    }


    /**
     * xbp流程id集合
     * @return
     */
    public Set<Integer> xbpBizProcessorIds(){
        Set<Integer> processorIds = xbpBizConfigList().stream().filter(Objects::nonNull).filter(o->null!=o.getProcessId())
                .map(o->o.getProcessId()).collect(Collectors.toSet());
        if (null==processorIds){
            return new HashSet<>();
        }
        return processorIds;
    }

    /**
     * 获取创建供应商审批流程id
     * @return
     */
    public Set<Integer> getSupplierCreateProcessorIds(){
        Set<Integer> ids = xbpBizConfigList().stream().filter(o-> XbpBizModleEnum.SUPPLIER_CREATE.getCode().equalsIgnoreCase(o.getBizModel()))
                .map(o->o.getProcessId()).collect(Collectors.toSet());
        return ids;
    }

    /**
     * 获取修改供应商审批流程id
     * @return
     */
    public Set<Integer> getSupplierModifyProcessorIds(){
        Set<Integer> ids = xbpBizConfigList().stream().filter(o-> XbpBizModleEnum.SUPPLIER_MODIFY.getCode().equalsIgnoreCase(o.getBizModel()))
                .map(o->o.getProcessId()).collect(Collectors.toSet());
        return ids;
    }

    /**
     * 获取创建供应商审批流程id
     * @return
     */
    public Integer getSupplierCreateProcessorId(String country){
        XbpDuccBizConfigVO vo = xbpBizConfigList().stream()
                .filter(o-> XbpBizModleEnum.SUPPLIER_CREATE.getCode().equalsIgnoreCase(o.getBizModel()) && country.equalsIgnoreCase(o.getCountry()))
                .findFirst().orElse(null);
        return null!=vo?vo.getProcessId():null;
    }

    /**
     * 获取修改供应商审批流程id
     * @return
     */
    public Integer getSupplierModifyProcessorId(String country){
        XbpDuccBizConfigVO vo = xbpBizConfigList().stream()
                .filter(o-> XbpBizModleEnum.SUPPLIER_MODIFY.getCode().equalsIgnoreCase(o.getBizModel()) && country.equalsIgnoreCase(o.getCountry()))
                .findFirst().orElse(null);
        return null!=vo?vo.getProcessId():null;
    }

    /**
     * 供应商xbp审批人员配置
     * @return
     */
    public List<SupplierFlowApprovers> supplierFlowApproversList(){
        List<SupplierFlowApprovers> configList = JSONArray.parseArray(supplierFlowApprovers, SupplierFlowApprovers.class);
        if (null==configList){
            return new ArrayList<>();
        }
        return configList;
    }

    /**
     * 某个国家的供应商xbp审批人员配置
     * @param country
     * @return
     */
    public Map<String, List<String>> getSupplierFlowApprovers(String country){
        SupplierFlowApprovers vo = supplierFlowApproversList().stream().filter(o-> country.equalsIgnoreCase(o.getCountry()))
                .findFirst().orElse(null);
        return null!=vo?vo.getApprovers():null;
    }

    /**
     * xbp单个富文本字段最大为64KB，预留4KB作为其它用途，业务最大使用60KB。
     * @return 单位：字节
     */
    public long getXbpRichTexMaxSizeOfByte(){
        if (null==xbpRichTexMaxSize || xbpRichTexMaxSize<1 || xbpRichTexMaxSize>=60){
            return 1024*60L;
        }
        return 1024*xbpRichTexMaxSize;
    }


    /**
     * 获取创建品牌审批流程id
     * @return
     */
    public Integer getBrandCreateProcessorId(String country){
        XbpDuccBizConfigVO vo = xbpBizConfigList().stream()
                .filter(o-> XbpBizModleEnum.BRAND_CREATE.getCode().equalsIgnoreCase(o.getBizModel()) && country.equalsIgnoreCase(o.getCountry()))
                .findFirst().orElse(null);
        return null!=vo?vo.getProcessId():null;
    }


    /**
     * 获取修改品牌审批流程id
     * @return
     */
    public Integer getBrandModifyProcessorId(String country){
        XbpDuccBizConfigVO vo = xbpBizConfigList().stream()
                .filter(o-> XbpBizModleEnum.BRAND_MODIFY.getCode().equalsIgnoreCase(o.getBizModel()) && country.equalsIgnoreCase(o.getCountry()))
                .findFirst().orElse(null);
        return null!=vo?vo.getProcessId():null;
    }

    /**
     * 获取修改税率审批流程id
     * @return
     */
    public Integer getTaxModifyProcessorId(String country){
        XbpDuccBizConfigVO vo = xbpBizConfigList().stream()
                .filter(o-> XbpBizModleEnum.TAX_MODIFY.getCode().equalsIgnoreCase(o.getBizModel()) && country.equalsIgnoreCase(o.getCountry()))
                .findFirst().orElse(null);
        return null!=vo?vo.getProcessId():null;
    }

    /**
     * 获取创建供应商审批流程id
     * @return
     */
    public Set<Integer> getBrandCreateProcessorIds(){
        Set<Integer> ids = xbpBizConfigList().stream().filter(o-> XbpBizModleEnum.BRAND_CREATE.getCode().equalsIgnoreCase(o.getBizModel()))
                .map(o->o.getProcessId()).collect(Collectors.toSet());
        return ids;
    }

    /**
     * 获取修改供应商审批流程id
     * @return
     */
    public Set<Integer> getBrandModifyProcessorIds(){
        Set<Integer> ids = xbpBizConfigList().stream().filter(o-> XbpBizModleEnum.BRAND_MODIFY.getCode().equalsIgnoreCase(o.getBizModel()))
                .map(o->o.getProcessId()).collect(Collectors.toSet());
        return ids;
    }

    public Set<String> getJinMenCustomers() {
        if(StringUtils.isBlank(jinmenClientCodes)) {
            return Collections.singleton("xsb9Oiq1Tx7jWveKg2MI");
        } else {
            return Arrays.stream(jinmenClientCodes.split(",")).collect(Collectors.toSet());
        }
    }

    /**
     * 采购价监控配置
     * @return
     */
    public SkuPurchasePriceMonitorConfig skuPriceMonitorConfig(){
        if (org.apache.commons.lang3.StringUtils.isBlank(skuPriceMonitorConfig)){
            log.warn("skuPriceMonitorConfig ducc value is empty.");
            return null;
        }
        SkuPurchasePriceMonitorConfig config = JSONObject.parseObject(skuPriceMonitorConfig, SkuPurchasePriceMonitorConfig.class);
        return config;
    }

    public boolean updatePriceByApprove(){
        SkuPurchasePriceMonitorConfig config = skuPriceMonitorConfig();
        if (null==config){
            return Boolean.FALSE;
        }
        // 两配置项都为true，则返回false
        boolean result = (Boolean.TRUE.equals(config.getUpdatePriceByApprove()) && Boolean.TRUE.equals(config.getUpdatePriceDirect()))
                ? Boolean.FALSE : Boolean.TRUE.equals(config.getUpdatePriceByApprove());
        return result;
    }

    public boolean updatePriceDirect(){
        SkuPurchasePriceMonitorConfig config = skuPriceMonitorConfig();
        if (null==config){
            return Boolean.FALSE;
        }
        // 两配置项都为true，则返回false
        boolean result = (Boolean.TRUE.equals(config.getUpdatePriceByApprove()) && Boolean.TRUE.equals(config.getUpdatePriceDirect()))
                ? Boolean.FALSE : Boolean.TRUE.equals(config.getUpdatePriceDirect());
        return result;
    }

    public Set<String> getContractList(){
        if (StringUtils.isNotEmpty(offlineContract)) {
            String[] split = offlineContract.split(",");
            return new HashSet<>(Arrays.asList(split));
        }
        return Collections.emptySet();
    }

    public Set<String> getCustomerOrderWaybill() {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(customerOrderWaybill)) {
            String[] split = customerOrderWaybill.split(",");
            // 将数组转换为Set
            return new HashSet<>(Arrays.asList(split));
        }
        return Collections.emptySet();
    }

    public Map<String,String> getClientCodeByCountry(){
        try {
            Map<String,String> countryClientCodeMap  = JSON.parseObject(countryClientRelation, new TypeReference<Map<String, String>>() {});
            return countryClientCodeMap;
        }catch (Exception e){
            Map<String,String> countryClientCodeMap = new HashMap<>();
            log.error("OperDuccConfig.getClientCodeByCountry error，e:{}",e.getMessage(),e);
            return countryClientCodeMap;
        }
    }

    public List<CrossAttributeChangeConfig> getCrossAttributeChangeConfig() {
        if (StringUtils.isEmpty(changeCrossAttribute)) {
            return Lists.newArrayList();
        }
        try {
            return JSON.parseArray(changeCrossAttribute, CrossAttributeChangeConfig.class);
        } catch (Exception e){
            log.error("OperDuccConfig.getCrossAttributeChangeConfig error，e:{}", e.getMessage(), e);
            return Lists.newArrayList();
        }
    }

    /**
     * 判断指定供应商是否为开放供应商。
     * @param supplierCode 供应商代码。
     * @return true 如果指定的供应商是开放供应商，否则返回 false。
     */
    public boolean isOpenSupplier(String supplierCode) {
        if (StringUtils.isBlank(openSupplier)) {
            return false;
        }
        log.info("openSupplier:{} supplierCode:{}", openSupplier, supplierCode);
        JSONObject rootNode = JSON.parseObject(openSupplier);
        List<String> supplierCodes = rootNode.entrySet().stream()
                .map(entry -> (JSONObject) entry.getValue())
                .map(supplierNode -> supplierNode.getString("supplierCode"))
                .collect(Collectors.toList());
        return supplierCodes.contains(supplierCode);
    }

    /**
     * 审核表单配置，为了映射审核列表展示
     */
    public Map<String/*业务唯一标识*/, List<AuditColumnConfigDTO>> getAuditColumnConfigMap() {
        if (StringUtils.isEmpty(processFromData)) {
            return Maps.newHashMap();
        }

        try {
            return JSON.parseObject(processFromData, new TypeReference<Map<String, List<AuditColumnConfigDTO>>>() {
            });

        } catch (Exception e){
            log.error("OperDuccConfig.getAuditColumnConfigMap error，e:{}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 是否计算退税款价格
     */
    public boolean isComputeJdTaxRefundPrice(String targetCountryCode) {
        if  (StringUtils.isBlank(jdTaxRefundPriceConfig) || StringUtils.isBlank(targetCountryCode)) {
            return false;
        }

        List<String> list = Splitter.on(',').splitToList(jdTaxRefundPriceConfig);

        return list.contains("all") || list.contains(targetCountryCode);
    }

    public boolean isComputeJdTaxRefundPriceSku(Long skuId) {
        if  (StringUtils.isBlank(jdTaxRefundPriceSkuConfig) || skuId == null) {
            return false;
        }

        List<String> list = Splitter.on(',').splitToList(jdTaxRefundPriceSkuConfig);

        return list.contains("all") || list.contains(skuId.toString());
    }

    public boolean isGrayJdCatIds(Long jdCatId) {
        if  (StringUtils.isBlank(grayJdCatIds) || jdCatId == null) {
            return false;
        }
        List<String> list = Splitter.on(',').splitToList(grayJdCatIds);

        return list.contains("all") || list.contains(jdCatId.toString());
    }

    public String getApproveOrderTopic() {
        return approveOrderTopic;
    }

    public String getWispPrompts() {
        if  (StringUtils.isBlank(wispSearchPrompts)) {
            return  "Your role is an AI query analysis engine for a B2B industrial goods marketplace. Your task is to refine the user's raw search query into a set of core technical keywords suitable for a high-precision search engine.\n" +
                    "IMPORTANT: The backend search engine splits keywords by spaces and requires that all keywords must co-exist in the product data (AND logic). Therefore, you must not add speculative or overly broad terms, as this will cause the search to fail.\n" +
                    "Process the following search term: (\"%s\"), in language: (%s).\n" +
                    "Processing Requirements:\n" +
                    "Identify Core Product: Accurately identify the core product or component in the user's query.\n" +
                    "Extract Key Specifications: Extract explicit technical specifications, such as model numbers, dimensions, materials, or standards (e.g., \"M5\", \"304 stainless steel\", \"10mm\", \"IP67\").\n" +
                    "Remove Extraneous Words: Delete all non-technical, conversational, or descriptive words (e.g., \"I want\", \"looking for\", \"heavy-duty\").\n" +
                    "Standardize Terminology: Convert common or ambiguous terms into standard industrial vocabulary (e.g., \"computer internet cable\" -> \"ethernet patch cable\").\n" +
                    "Output Format:\n" +
                    "The final result must be a single line of text.\n" +
                    "Include only the refined core technical keywords, separated by a single space.\n" +
                    "Strictly prohibit any introductions, explanations, or bullet points.";
        }
        return wispSearchPrompts;
    }

    /**
     * 获取需要排除的客户
     */
    public List<String> getPreseletorClientCodeList() {
        List<PreselectionVO> list = this.preselectionList();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        return list.stream().map(PreselectionVO::getPreseletorClientCodeList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 超级管理员
     * @return
     */
    public Set<String> getDataIsolationAuthAdminErp(){
        return JSON.parseObject(authAdminErp, new TypeReference<Set<String>>() {});
    }

    /**
     * 数据隔离配置
     * @return
     */
    public DataIsolationQueryVO getDataIsolationQueryVO(String erp){
        Set<String> authAdminErpSet = this.getDataIsolationAuthAdminErp();
        if(CollectionUtils.isNotEmpty(authAdminErpSet) && StringUtils.isNotBlank(erp) && authAdminErpSet.contains(erp)){
            // 管理员权限，不隔离数据
            log.info("{}管理员权限，不隔离数据", erp);
            return new DataIsolationQueryVO();
        }
        return JSON.parseObject(dataIsolation, DataIsolationQueryVO.class);
    }

    /**
     * <a href="https://joyspace.jd.com/pages/ITHp3II4dEVbR1IKrwYY?source=joyday">毛利配置先写死</a>
     */
    Map<String, BigDecimal> grossProfitConfig = new HashMap<String, BigDecimal>(){{
        put("BR", BigDecimal.valueOf(0.05));
        put("HU", BigDecimal.valueOf(0.03));
        put("TH", BigDecimal.valueOf(0.063));
        put("VN", BigDecimal.valueOf(0.032));
        put("MY", BigDecimal.valueOf(0.041));
        put("ID", BigDecimal.valueOf(0.068));
    }};

    /**
     * 根据国家代码获取对应的边际负毛阈值
     * @param countryCode 国家代码，用于查找对应的毛利率配置
     * @return 返回指定国家代码对应的毛利率配置值，若未找到则返回BigDecimal.ZERO
     */
    public BigDecimal getGrossProfit(String countryCode) {
        BigDecimal profit = grossProfitConfig.getOrDefault(countryCode, BigDecimal.ZERO);
        log.info("获取边际利润率，countryCode=【{}】，边际利润率=【{}】", countryCode, profit);
        return profit;
    }

    /**
     * 是否使用价格可售状态过滤数据
     * @return 当queryMkuAvailableUseStatus不为null且大于0时返回true，否则返回false
     */
    public boolean isQueryMkuAvailableUseStatus() {
        return queryMkuAvailableUseStatus != null && queryMkuAvailableUseStatus > 0;
    }
    /**
     * 获取开阳配置类目的客户合同集合。
     * @return 开阳类别合同集合。
     */
    public Set<String> getKaiYangCategoryContract() {
        if (org.apache.commons.lang3.StringUtils.isBlank(contractList)) {
            return Sets.newHashSet();
        }
        return  Arrays.stream(contractList.split(Constant.COMMA)).collect(Collectors.toSet());
    }

    public String getInitCountryAgreementTopic() {
        if (initCountryAgreementTopic == null) {
            throw new RuntimeException("initCountryAgreementTopic config error ");
        }
        return this.initCountryAgreementTopic;
    }

    public List<BrIcmsRateConfig> getBrIcmsRateConfigList() {
        if (StringUtils.isBlank(priceRateBrIcms)) {
            throw new RuntimeException("priceRateBrIcms config error ");
        }
        return JSON.parseArray(priceRateBrIcms, BrIcmsRateConfig.class);
    }
}
