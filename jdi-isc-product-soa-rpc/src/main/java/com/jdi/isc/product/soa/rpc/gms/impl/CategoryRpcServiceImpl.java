package com.jdi.isc.product.soa.rpc.gms.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.jd.gms.greatdane.category.domain.*;
import com.jd.gms.greatdane.category.request.*;
import com.jd.gms.greatdane.category.service.read.*;
import com.jd.gms.greatdane.domain.ClientInfo;
import com.jd.gms.greatdane.domain.SearchPage;
import com.jd.gms.greatdane.response.GreatDaneResult;
import com.jd.k2.gd.boost.dto.ReflectDataDto;
import com.jd.k2.gd.boost.sdk.soa.IJdiExtendDataService;
import com.jd.ka.stock.util.SdkResult;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.common.util.HostUtil;
import com.jdi.isc.product.soa.common.util.StringUtils;
import com.jdi.isc.product.soa.domain.gms.resp.JdProductDTO;
import com.jdi.isc.product.soa.rpc.b2b.ware.BWareReadRpcService;
import com.jdi.isc.product.soa.rpc.gms.CategoryRpcService;
import com.jdi.isc.product.soa.rpc.gms.SkuInfoRpcService;
import com.jdi.isc.product.soa.rpc.suport.AlertHelper;
import jd.gms.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.jdi.isc.product.soa.rpc.b2b.ware.BWareReadRpcService.JD_VAT_RATE_KEY;

/**
 * 中台类目查询服务
 */
@Service
@Slf4j
public class CategoryRpcServiceImpl implements CategoryRpcService {

    public static final String APPLICATION_ID = "494707";
    public static final String APPLICATION_NAME = "jdos_jdi-isc-product-center";
    // 默认属性组id为null时，属性组id=0
    public static final Integer DEFAULT_NO_COMGROUP_ID=0;
    // 默认scope为null时，scope=1，自营
    public static final Integer DEFAULT_SCOPE=1;
    // 默认level为null时，level=0，spu
    public static final Integer DEFAULT_LEVEL=0;

    @Resource
    private CategoryGroupAttReadService categoryGroupAttReadService;

    @Resource
    private NewAttributeGroupReadService newAttributeGroupReadService;

    @Resource
    private CategoryGroupAttValueReadService categoryGroupAttValueReadService;

    @Resource
    private IJdiExtendDataService iJdiExtendDataService;

    @Resource
    private CategoryTreeReadService categoryTreeReadService;

    @Resource
    private CategorySearchService categorySearchService;

    @Resource
    private CategoryReadService categoryReadService;

    @Resource
    private jd.gms.category.dubbo.service.CategoryService categoryService;

    @Resource
    private SkuInfoRpcService skuInfoRpcService;

    private static final String APPLICATION_NAME2 = "J-dos-jdi-isc-product-soa";

    private ClientInfo buildClientInfo() {
        return new ClientInfo(HostUtil.getLocalIP(), APPLICATION_ID, APPLICATION_NAME, APPLICATION_NAME, APPLICATION_NAME);
    }

    private ClientInfo buildClientInfo2() {
        return new ClientInfo(HostUtil.getLocalIP(), APPLICATION_ID, APPLICATION_NAME2, APPLICATION_NAME2, APPLICATION_NAME2);
    }

    @Override
    @PFTracing
    public Map<Integer, CategoryGroupAtt> getCategoryGroupAttByCatId(Integer categoryId) {
        // 根据属性id ，返回属性
        GetCategoryGroupAttByCatIdParam categoryGroupAttParam = new GetCategoryGroupAttByCatIdParam();
//        categoryGroupAttParam.setClientInfo(new ClientInfo("127.0.0.1", "50289","(J-one)ipc-csb", "jdi","cn_retail_igoods"));//国内KA配置
        categoryGroupAttParam.setClientInfo(buildClientInfo());
        categoryGroupAttParam.setCatId(categoryId);
        categoryGroupAttParam.setContainParent(true);
        GreatDaneResult<Map<Integer, CategoryGroupAtt>> categoryGroupAttResult = null;
        try{
            categoryGroupAttResult = categoryGroupAttReadService.getCategoryGroupAttByCatId(categoryGroupAttParam);
            if (categoryGroupAttResult != null || categoryGroupAttResult.isSuccess()) {
                // 所有id！=0的属性组id，用来查询属性名称
                Set<Integer> groupIdSet = new HashSet<>();
                // 设置没有属性组id的默认属性组id为0，通常没有属性组的属性属性组id就是0，同时属性组名称为null
                if(categoryGroupAttResult.getResult() !=null){
                    for (CategoryGroupAtt value : categoryGroupAttResult.getResult().values()) {
                        if(value != null && value.getComGroupId()==null){
                            value.setComGroupId(DEFAULT_NO_COMGROUP_ID);
                        } else if (value.getComGroupId() !=DEFAULT_NO_COMGROUP_ID){
                            groupIdSet.add(value.getComGroupId());
                        }
                        // 设置没有scope的默认scope为1，自营
                        if(value != null && value.getScope()==null){
                            value.setScope(DEFAULT_SCOPE);
                        }
                        // 设置没有level的默认level为0，spu属性
                        if(value != null && value.getLevel()==null){
                            value.setLevel(DEFAULT_LEVEL);
                        }
                    }
                }

                if(!groupIdSet.isEmpty()) {
                    // 根据属性组id，查询属性组名称
                    GetNewAttributeGroupParam newAttributeGroupParam = new GetNewAttributeGroupParam();
                    newAttributeGroupParam.setClientInfo(buildClientInfo2());
                    newAttributeGroupParam.setComGroupIds(groupIdSet);
                    GreatDaneResult<Map<Integer, NewAttributeGroup>> newAttributeGroup = newAttributeGroupReadService.getNewAttributeGroup(newAttributeGroupParam);
                    if (newAttributeGroup != null && newAttributeGroup.isSuccess()) {
                        // 获取属性组id-》属性组名的map
                        Map<Integer, String> groupIdToNameMap = new HashMap<>();
                        if (newAttributeGroup.getResult() != null) {
                            for (NewAttributeGroup entry : newAttributeGroup.getResult().values()) {
                                if (entry.getComGroupId() != null && entry.getName() != null) {
                                    groupIdToNameMap.put(entry.getComGroupId(), entry.getName());
                                }
                            }
                        }

                        // 将属性组名称赋值到CategoryGroupAtt中
                        if (categoryGroupAttResult.getResult() != null) {
                            for (CategoryGroupAtt categoryGroupAtt : categoryGroupAttResult.getResult().values()) {
                                if (categoryGroupAtt != null && categoryGroupAtt.getComGroupId() != null) {
                                    String groupName = groupIdToNameMap.get(categoryGroupAtt.getComGroupId());
                                    if (groupName != null) {
                                        categoryGroupAtt.setComGroupName(groupName);
                                    }
                                }
                            }
                        }
                        log.info("CategoryRpcServiceImpl.getCategoryGroupAttByCatId:newAttributeGroup:{}", JSON.toJSONString(newAttributeGroup));
                    }
                }
                return categoryGroupAttResult.getResult();
            }
        }catch (Exception e){
            log.error("CategoryRpcServiceImpl.getCategoryGroupAttByCatId:has an error, params[{}], errorMessage[{}]", categoryGroupAttResult, Objects.nonNull(categoryGroupAttResult) ? categoryGroupAttResult.getErrorMessage():null, e);
        } finally {
            log.info("CategoryRpcServiceImpl.getCategoryGroupAttByCatId params[{}], Result[{}]", JSON.toJSONString(categoryGroupAttParam), JSON.toJSONString(categoryGroupAttResult));
        }
        return Collections.emptyMap();
    }

    @Override
    @PFTracing
    public Map<Long, List<CategoryGroupAttValue>> queryCategoryGroupAttValueByAttMapId(Set<Long> attMapIds) {
        QueryCategoryGroupAttValueByAttMapIdParam param = new QueryCategoryGroupAttValueByAttMapIdParam();
//        param.setClientInfo(new ClientInfo("127.0.0.1", "50289","(J-one)ipc-csb", "jdi","cn_retail_igoods"));//国内KA配置
        param.setClientInfo(buildClientInfo());
        param.setAttMapIds(attMapIds);
        GreatDaneResult<Map<Long, List<CategoryGroupAttValue>>> categoryGroupAttResult = null;
        try {
            categoryGroupAttResult = categoryGroupAttValueReadService.queryCategoryGroupAttValueByAttMapId(param);
            if (categoryGroupAttResult != null || categoryGroupAttResult.isSuccess()) {
                // 设置没有属性组id的默认属性组id为0，通常没有属性组的属性属性组id就是0，同时属性组名称为null
                if(categoryGroupAttResult.getResult() !=null){
                    for (List<CategoryGroupAttValue> value : categoryGroupAttResult.getResult().values()) {
                        if(value!=null&&!value.isEmpty()){
                            for (CategoryGroupAttValue categoryGroupAttValue : value) {
                                if(categoryGroupAttValue != null && categoryGroupAttValue.getComGroupId()==null){
                                    categoryGroupAttValue.setComGroupId(DEFAULT_NO_COMGROUP_ID);
                                }
                            }
                        }
                    }
                }
                return categoryGroupAttResult.getResult();
            }
        }catch (Exception e){
            log.error("CategoryRpcServiceImpl.queryCategoryGroupAttValueByAttMapId:has an error, params[{}], errorMessage[{}]", param, e.getMessage());
        } finally {
//            log.info("CategoryRpcServiceImpl.queryCategoryGroupAttValueByAttMapId params[{}], Result[{}]", JSON.toJSONString(param), JSON.toJSONString(categoryGroupAttResult));
            log.info("CategoryRpcServiceImpl.queryCategoryGroupAttValueByAttMapId params[{}], is over", JSON.toJSONString(param));
        }
        return Collections.emptyMap();
    }

    @Override
    @ToolKit(exceptionWrap = true)
    @PFTracing
    public ReflectDataDto queryReflectData(ReflectDataDto req){
        SdkResult<ReflectDataDto> result = null;
        try {
            result = iJdiExtendDataService.queryReflectData(req);
            if(Objects.nonNull(result) && result.isSuccess()){
                return result.getData();
            }
        }catch (Exception e){
            log.error("CategoryRpcServiceImpl queryReflectData req:{},res:{} ,err:{}", JSON.toJSONString(req),JSON.toJSONString(result),e.getMessage(),e);
        }
        return null;
    }

    @Override
    public CategoryTree getCategoryTree(GetCategoryTreeParam param) {
        param.setClientInfo(buildClientInfo2());
        GreatDaneResult<CategoryTree> result = null;
        try {
            result = categoryTreeReadService.getCategoryTree(param);
            if (result != null && result.isSuccess()) {
                return result.getResult();
            }
        } catch (Exception e) {
            log.error("CategoryRpcServiceImpl.getCategoryTree:has an error, params[{}], errorMessage[{}]", param, e.getMessage());
        } finally {
            log.info("CategoryRpcServiceImpl.getCategoryTree params[{}], Result[{}]", JSON.toJSONString(param), JSON.toJSONString(result));
        }
        return null;
    }

    @Override
    public List<Category> getCategoryByClass(GetCategoryByClassParam param) {
        param.setClientInfo(buildClientInfo2());
        GreatDaneResult<Set<Category>> result = null;
        try {
            result = categoryTreeReadService.getCategoryByClass(param);
            if (result != null && result.isSuccess()) {
                return result.getResult().stream().filter(Objects::nonNull).sorted(Comparator.comparing(Category::getCategoryId)).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("CategoryRpcServiceImpl.getCategoryByClass:has an error, params[{}], errorMessage[{}]", param, e.getMessage());
        } finally {
            log.info("CategoryRpcServiceImpl.getCategoryByClass params[{}], Result[{}]", JSON.toJSONString(param), JSON.toJSONString(result));
        }
        return Collections.emptyList();
    }

    @Override
    public PageInfo<Category> searchCategory(SearchCategoryParam param) {
        SearchPage searchPage = param.getSearchPage();
        if (Objects.isNull(searchPage)) {
            searchPage = new SearchPage();
        }
        searchPage.setCurrentPage(Objects.nonNull(searchPage.getCurrentPage()) ? searchPage.getCurrentPage() : Constant.ONE);
        searchPage.setPageSize(Objects.nonNull(searchPage.getPageSize()) ? searchPage.getPageSize() : Constant.PARTITION_SIZE);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setIndex(searchPage.getCurrentPage());
        pageInfo.setSize(searchPage.getPageSize());
        param.setClientInfo(buildClientInfo2());
        GreatDaneResult<List<Category>> result = null;
        try {
            result = categorySearchService.searchCategory(param);
            if (result != null && result.isSuccess()) {
                pageInfo.setRecords(result.getResult());
            }
        } catch (Exception e) {
            log.error("CategoryRpcServiceImpl.searchCategory:has an error, params[{}], errorMessage[{}]", param, e.getMessage());
        } finally {
            log.info("CategoryRpcServiceImpl.searchCategory params[{}], Result[{}]", JSON.toJSONString(param), JSON.toJSONString(result));
        }
        return pageInfo;
    }

    @Override
    public Map<Integer, Category> getCategoryByIds(GetCategoryByIdsParam param) {
        param.setClientInfo(buildClientInfo2());
        GreatDaneResult<Map<Integer, Category>> result = null;
        try {
            result = categoryReadService.getCategoryByIds(param);
            if (result != null && result.isSuccess()) {
                return result.getResult();
            }
        } catch (Exception e) {
            log.error("CategoryRpcServiceImpl.getCategoryByIds:has an error, params[{}], errorMessage[{}]", param, e.getMessage());
        } finally {
            log.info("CategoryRpcServiceImpl.getCategoryByIds params[{}], Result[{}]", JSON.toJSONString(param), JSON.toJSONString(result));
        }
        return Collections.emptyMap();
    }

    @Override
    public List<Category> getOriginById(GetOrginByIdParam param) {
        param.setClientInfo(buildClientInfo2());
        GreatDaneResult<List<Category>> result = null;
        try {
            result = categoryReadService.getOrginById(param);
            if (result != null && result.isSuccess()) {
                return result.getResult();
            }
        } catch (Exception e) {
            log.error("CategoryRpcServiceImpl.getOrginById:has an error, params[{}], errorMessage[{}]", param, e.getMessage());
        } finally {
            log.info("CategoryRpcServiceImpl.getOrginById params[{}], Result[{}]", JSON.toJSONString(param), JSON.toJSONString(result));
        }
        return Collections.emptyList();
    }

    @Override
    public List<Category> getRelation(GetRelationParam param) {
        param.setClientInfo(buildClientInfo2());
        GreatDaneResult<Set<Category>> result = null;
        try {
            result = categoryReadService.getRelation(param);
            if (result != null && result.isSuccess()) {
                return result.getResult().stream().filter(Objects::nonNull).sorted(Comparator.comparing(Category::getCategoryId)).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("CategoryRpcServiceImpl.getRelation:has an error, params[{}], errorMessage[{}]", param, e.getMessage());
        } finally {
            log.info("CategoryRpcServiceImpl.getRelation params[{}], Result[{}]", JSON.toJSONString(param), JSON.toJSONString(result));
        }
        return Collections.emptyList();
    }

    @Override
    public Map<Integer, Set<Category>> getBatchRelation(GetBatchRelationParam param) {
        param.setClientInfo(buildClientInfo2());
        GreatDaneResult<Map<Integer, Set<Category>>> result = null;
        try {
            result = categoryReadService.getBatchRelation(param);
            if (result != null && result.isSuccess()) {
                return result.getResult();
            }
        } catch (Exception e) {
            log.error("CategoryRpcServiceImpl.getBatchRelation:has an error, params[{}], errorMessage[{}]", param, e.getMessage());
        } finally {
            log.info("CategoryRpcServiceImpl.getBatchRelation params[{}], Result[{}]", JSON.toJSONString(param), JSON.toJSONString(result));
        }
        return Collections.emptyMap();
    }

    @Override
    public jd.gms.category.dubbo.domain.Category getCategory(Integer thirdCategoryId) {
        if (thirdCategoryId == null) {
            log.info("thirdCategoryId is null");
            return null;
        }

        long l = System.currentTimeMillis();

        Result<jd.gms.category.dubbo.domain.Category> result = null;

        try {
            result = categoryService.get(thirdCategoryId);
            if (result != null && result.isSuccess()) {
                return result.getObj();
            }
        } catch (Exception e) {
            log.error("CategoryRpcServiceImpl.get:has an error, params[{}], errorMessage[{}]", thirdCategoryId, e.getMessage(), e);
            AlertHelper.p0("jd.gms.category.dubbo.service.CategoryService.get error", String.valueOf(thirdCategoryId));
        } finally {
            log.info("CategoryRpcServiceImpl.get params[{}], Result[{}], cost={}", thirdCategoryId, JSON.toJSONString(result), System.currentTimeMillis() - l);
        }

        return null;
    }

    @Override
    public jd.gms.category.dubbo.domain.Category getCategory(Long jdSkuId) {
        if (jdSkuId == null) {
            log.info("jdSkuId is null");
            return null;
        }
        JdProductDTO product = skuInfoRpcService.getSkuById(jdSkuId);

        if (product == null) {
            log.warn("找不到商品信息. jdSkuId={}", jdSkuId);
            return null;
        }

        Integer thirdCategoryId = product.getThirdCategoryId();

        if (thirdCategoryId == null) {
            log.warn("JdProductDTO thirdCategoryId is null. jdSkuId={}", jdSkuId);
            return null;
        }

        return this.getCategory(thirdCategoryId);
    }

    /**
     * 根据京东SKU ID获取对应的增值税率
     * @param jdSkuId 京东商品SKU ID
     * @return 返回对应商品的增值税率，若未找到商品信息则返回null
     */
    @Override
    public BigDecimal getJdCategoryVatRate(Long jdSkuId) {
        jd.gms.category.dubbo.domain.Category category = this.getCategory(jdSkuId);

        if (category != null && StringUtils.isNotEmpty(category.getFeature())) {
            Map<String, BigDecimal> vatInfo = SpringUtil.getBean(BWareReadRpcService.class).getVatInfo(jdSkuId, category.getFeature());
            return vatInfo.get(JD_VAT_RATE_KEY);
        }

        log.info("CategoryRpcService.getJdVatRate, 未获取到增值税. jdSkuId={}", jdSkuId);

        return null;
    }
}
