package com.jdi.isc.product.soa.rpc.gms;

import com.jd.gms.greatdane.category.domain.Category;
import com.jd.gms.greatdane.category.domain.CategoryGroupAtt;
import com.jd.gms.greatdane.category.domain.CategoryGroupAttValue;
import com.jd.gms.greatdane.category.domain.CategoryTree;
import com.jd.gms.greatdane.category.request.*;
import com.jd.k2.gd.boost.dto.ReflectDataDto;
import com.jdi.common.domain.rpc.bean.PageInfo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 中台类目查询服务
 * <AUTHOR>
 * @date 20231208
 */
public interface CategoryRpcService {

    Map<Integer, CategoryGroupAtt> getCategoryGroupAttByCatId(Integer categoryId);

    Map<Long, List<CategoryGroupAttValue>> queryCategoryGroupAttValueByAttMapId(Set<Long> attMapIds);

    /**
     * 根据请求对象查询反射数据。
     * @param req 反射数据请求对象。
     * @return 查询结果反射数据对象。
     */
    ReflectDataDto queryReflectData(ReflectDataDto req);

    // 新增方法

    /**
     * 获取类目树
     * @param param 获取类目树参数
     * @return 类目树
     */
    CategoryTree getCategoryTree(GetCategoryTreeParam param);

    /**
     * 根据类目层级获取类目列表
     * @param param 获取类目层级参数
     * @return 类目集合
     */
    List<Category> getCategoryByClass(GetCategoryByClassParam param);

    /**
     * 搜索类目（分页搜过，不支持cursor）
     * @param param 搜索类目参数
     * @return 类目分页列表
     */
    PageInfo<Category> searchCategory(SearchCategoryParam param);

    /**
     * 根据ID获取类目
     * @param param 获取类目参数
     * @return 类目Map，key为类目ID，value为类目对象
     */
    Map<Integer, Category> getCategoryByIds(GetCategoryByIdsParam param);

    /**
     * 根据ID获取原始类目
     * @param param 获取原始类目参数
     * @return 原始类目列表
     */
    List<Category> getOriginById(GetOrginByIdParam param);

    /**
     * 获取类目关系
     * @param param 获取类目关系参数
     * @return 类目集合
     */
    List<Category> getRelation(GetRelationParam param);

    /**
     * 批量获取类目关系
     * @param param 批量获取类目关系参数
     * @return 类目关系Map，key为类目ID，value为相关类目集合
     */
    Map<Integer, Set<Category>> getBatchRelation(GetBatchRelationParam param);


    /**
     * 根据三级类目ID获取类目信息
     * @param thirdCategoryId 三级类目ID
     * @return 对应的类目对象
     */
    jd.gms.category.dubbo.domain.Category getCategory(Integer thirdCategoryId);

    /**
     * 根据京东商品ID获取商品所属的分类信息
     * @param jdSkuId 京东商品ID
     * @return 返回商品对应的分类信息对象
     */
    jd.gms.category.dubbo.domain.Category getCategory(Long jdSkuId);

    /**
     * 根据京东SKU ID获取对应的增值税率
     * @param jdSkuId 京东商品SKU ID
     * @return 返回对应商品的增值税率，若未找到商品信息则返回null
     */
    BigDecimal getJdCategoryVatRate(Long jdSkuId);
}
