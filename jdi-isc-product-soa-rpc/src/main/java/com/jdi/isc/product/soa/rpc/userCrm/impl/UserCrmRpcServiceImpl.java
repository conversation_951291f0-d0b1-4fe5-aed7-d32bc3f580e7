package com.jdi.isc.product.soa.rpc.userCrm.impl;

import com.alibaba.fastjson.JSON;
import com.jd.b2b.user.soa.sdk.dto.common.CommonQuery;
import com.jd.b2b.user.soa.sdk.dto.common.PaginatedResult;
import com.jd.b2b.user.soa.sdk.dto.contract.ContractDto;
import com.jd.b2b.user.soa.sdk.service.ContractManageService;
import com.jd.ka.buser.sdk.service.BussinessOpportunityQueryService;
import com.jd.ka.buser.sdk.vo.BuserPageQuery;
import com.jd.ka.buser.sdk.vo.ProjectBussinessVo;
import com.jdi.isc.product.soa.common.constants.Constant;
import com.jdi.isc.product.soa.rpc.userCrm.UserCrmRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserCrmRpcServiceImpl implements UserCrmRpcService {

    @Resource
    private ContractManageService contractManageService;

    @Resource
    private BussinessOpportunityQueryService bussinessOpportunityQueryService;

    private final static Long DEFAULT_INDEX = 1L;
    private final static Integer DEFAULT_SIZE = 50;

    private final static Integer MAX_ITEM = 10000;

    @Override
    public List<String> queryContractPage(String erp){
        List<Long> projectIdList = queryProjectIdList(erp);
        List<String> contractNumberList = new ArrayList<>();

        for (Long projectId : projectIdList){
            List<String> contractNumbers = queryContractNumber(projectId);
            contractNumberList.addAll(contractNumbers);
        }
        return contractNumberList;
    }

    /**
     * 根据 ERP 编号查询项目 ID 列表。
     * @param erp ERP 编号。
     * @return 项目 ID 列表。
     */
    private List<Long> queryProjectIdList(String erp){
        List<Long> projectIdList = new ArrayList<>();
        try {
            log.info("UserCrmRpcServiceImpl queryProjectIdList erp:{}",erp);
            List<ProjectBussinessVo> dataList = new ArrayList<>();
            int done = 0;
            int index = 1;
            BuserPageQuery pageQuery = new BuserPageQuery();
            pageQuery.setPageSize(DEFAULT_SIZE);
            pageQuery.setPageNo(index);
            List<ProjectBussinessVo> batchRes = bussinessOpportunityQueryService.queryProjectInfoByErp(pageQuery, erp, Constant.SYSTEM_CODE).getDataList();
            log.info("UserCrmRpcServiceImpl queryProjectIdList pageQuery:{}, batchRes:{} ", JSON.toJSONString(pageQuery),JSON.toJSONString(batchRes));
            while (CollectionUtils.isNotEmpty(batchRes) && done < MAX_ITEM){
                dataList.addAll(batchRes);
                done = done + batchRes.size();
                index = index + 1;
                pageQuery.setPageNo(index);
                batchRes = bussinessOpportunityQueryService.queryProjectInfoByErp(pageQuery, erp, Constant.SYSTEM_CODE).getDataList();
                log.info("UserCrmRpcServiceImpl queryProjectIdList req:{}, data:{} ", JSON.toJSONString(pageQuery),JSON.toJSONString(batchRes));
            }
            if(CollectionUtils.isEmpty(dataList)){
                return projectIdList;
            }
            projectIdList = dataList.stream().map(ProjectBussinessVo::getId).collect(Collectors.toList());
            log.info("UserCrmRpcServiceImpl queryProjectIdList erp:{},result:{}",erp,JSON.toJSONString(projectIdList));
            return projectIdList;
        }catch (Exception e){
            log.error("【系统异常】UserCrmRpcServiceImpl queryProjectIdList erp:{},error:{}",erp,e.getMessage(),e);
        }
        return projectIdList;
    }

    /**
     * 查询合同信息
     * @return 合同信息列表
     */
    private List<String> queryContractNumber(Long projectId){

        List<String> contractNumberList = new ArrayList<>();
        try {
            CommonQuery commonQuery = new CommonQuery();
            commonQuery.setCutPage(1);
            commonQuery.setPageSize(DEFAULT_SIZE);
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("projectNumber",projectId);
            commonQuery.setParamMap(paramMap);
            log.info("UserCrmRpcServiceImpl queryContractNumber param:{}", JSON.toJSONString(commonQuery));
            PaginatedResult<ContractDto> result = contractManageService.queryContractPage(commonQuery);
            if(Objects.isNull(result)){
                return contractNumberList;
            }
            List<ContractDto> contractDtoList = result.getList();
            if(CollectionUtils.isEmpty(contractDtoList)){
                return contractNumberList;
            }
            contractNumberList  = contractDtoList.stream().map(ContractDto::getContractNumber).collect(Collectors.toList());
            log.info("UserCrmRpcServiceImpl queryContractNumber projectId:{},result:{}",projectId,JSON.toJSONString(result));
            return contractNumberList;
        }catch (Exception e){
            log.error("UserCrmRpcServiceImpl queryContractNumber projectId:{},error:{}",projectId,e.getMessage(),e);
        }
        return contractNumberList;
    }
}
