package com.jdi.isc.product.soa.rpc.iop.impl;

import com.alibaba.fastjson.JSON;
import com.jd.ka.gpt.soa.client.StandardQueryGoodsOpenProvider;
import com.jd.ka.gpt.soa.client.goods.request.GetStockByIdGoodsReq;
import com.jd.ka.gpt.soa.client.goods.request.SkuNumBaseGoodsReq;
import com.jd.ka.gpt.soa.client.goods.resp.GetStockByIdGoodsResp;
import com.jd.ka.gpt.soa.client.resp.BaseRpcResponse;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.constant.LevelCode;
import com.jdi.isc.product.soa.common.constants.UmpKeyConstant;
import com.jdi.isc.product.soa.rpc.iop.SkuStockRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * iop商品库存服务
 * <AUTHOR>
 * @date 20231218
 */
@Service
@Slf4j
public class SkuStockRpcServiceImpl implements SkuStockRpcService {

    @Resource
    private StandardQueryGoodsOpenProvider standardQueryGoodsOpenProvider;

    @Value("${spring.profiles.active}")
    private String systemProfile;
    private final static int OUT_OF_STOCK = 34;

    // iop获取库存接口,原始文档 https://jos.jd.com/apilistnewdetail?apiGroupId=1007&apiId=18525&apiName=null
    @Override
    public List<GetStockByIdGoodsResp> getCnStockBySkuId(GetStockByIdGoodsReq req) {
        CallerInfo callerInfo = Profiler.registerInfo(systemProfile + "-SkuStockRpcServiceImpl.getCnStockBySkuId");
        List<GetStockByIdGoodsResp> result = new ArrayList<>();
        BaseRpcResponse<List<GetStockByIdGoodsResp>> stockRes = null;
        try {
            stockRes = standardQueryGoodsOpenProvider.getNewStockById(req, req.getClientId(), req.getPin(), 4, "127.0.0.1");
            if (stockRes.isSuccess()) {
                if (CollectionUtils.isNotEmpty(stockRes.getData())) {
                    result = stockRes.getData();
                }
            } else {
                Profiler.businessAlarm(UmpKeyConstant.BUSINESS_KEY_COMMON_WARNING, String.format("【%s】%s 库存查询异常 %s", systemProfile, LevelCode.P1.getMessage(), stockRes.getMessage()));
                for (SkuNumBaseGoodsReq sku : req.getSkuNumInfoList()) {
                    GetStockByIdGoodsResp mockRes = new GetStockByIdGoodsResp();
                    mockRes.setSkuId(sku.getSkuId());
                    mockRes.setStockStateType(OUT_OF_STOCK);
                    mockRes.setRemainNum(0);
                    result.add(mockRes);
                }
                return result;
            }
        } catch (Exception e) {
            log.error("SkuStockRpcServiceImpl.getStockBySkuId req:{} , res:{}", JSON.toJSONString(req), JSON.toJSONString(stockRes),e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("SkuStockRpcServiceImpl.getStockBySkuId req:{} , res:{}", JSON.toJSONString(req), JSON.toJSONString(stockRes));
            Profiler.registerInfoEnd(callerInfo);
        }
        return result;
    }

    @Override
    public Map<Long, GetStockByIdGoodsResp> getCnStockBySkuIdMap(GetStockByIdGoodsReq req) {
        List<GetStockByIdGoodsResp> goodsRespList = this.getCnStockBySkuId(req);

        return Optional.ofNullable(goodsRespList)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(GetStockByIdGoodsResp::getSkuId, Function.identity()));
    }

}
