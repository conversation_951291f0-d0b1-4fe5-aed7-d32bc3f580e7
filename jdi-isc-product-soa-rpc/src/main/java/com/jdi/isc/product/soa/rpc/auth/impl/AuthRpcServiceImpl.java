package com.jdi.isc.product.soa.rpc.auth.impl;

import com.jd.auth.facade.jsf.*;
import com.jd.auth.facade.request.BaseRequest;
import com.jd.auth.facade.request.dim.DimResCodeRequest;
import com.jd.auth.facade.request.role.RoleListRequest;
import com.jd.auth.facade.request.user.ErpRequest;
import com.jd.auth.facade.request.user.ErpResRequest;
import com.jd.auth.facade.request.user.UserRequest;
import com.jd.auth.facade.request.user.UserResRequest;
import com.jd.auth.facade.response.Response;
import com.jd.auth.facade.response.menu.FuncDto;
import com.jd.auth.facade.response.menu.MenuDto;
import com.jd.auth.facade.response.res.DimResourceDto;
import com.jd.auth.facade.response.role.RoleDto;
import com.jd.auth.facade.response.user.UserDto;
import com.jd.fastjson.JSON;
import com.jd.ump.annotation.JProEnum;
import com.jd.ump.annotation.JProfiler;
import com.jdi.isc.product.soa.rpc.auth.AuthRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * @Description: 权限服务
 * @Author: wangpeng965
 * @Date: 2024/09/20 20:33
 **/
@Slf4j
@Service
public class AuthRpcServiceImpl implements AuthRpcService {

    /**
     * 应用程序的密钥，用于身份验证和授权。
     */
    @Value("${auth.appKey}")
    private String appKey;
    
    /**
     * 应用程序的令牌，用于身份验证和授权。
     */
    @Value("${auth.appToken}")
    private String appToken;
    
    /**
     * 菜单服务接口的实例，用于处理与菜单相关的业务逻辑。
     */
    @Resource
    private MenuFacade menuFacade;

    /**
     * 菜单服务接口的实例，用于处理与菜单相关的业务逻辑。
     */
    @Resource
    private ResourceFacade resourceFacade;


    /**
     * 数据权限服务接口的实例，用于处理与数据资源相关的业务逻辑。
     */
    @Resource
    private DimPermissionFacade dimPermissionFacade;

    /**
     * 角色服务接口的实例，用于处理与角色相关的业务逻辑。
     */
    @Resource
    private RoleFacade roleFacade;

    /**
     * 过滤资源的服务接口实例。
     */
    @Resource
    private FilterResourceFacade filterResourceFacade;

    @Resource
    private UserFacade userFacade;

    /**
     * 检查资源是否可用。
     * @param request ErpResRequest 对象，包含应用程序密钥、令牌和租户代码。
     * @return Boolean 响应，表示资源是否可用。
     */
    @Override
    public Response<Boolean> checkResource(ErpResRequest request){
        request.setAppKey(appKey);
        request.setAppToken(appToken);
        request.setTenantCode(BaseRequest.TENANT_CODE_CN);
        Response<Boolean> booleanResponse = resourceFacade.checkResource(request);
        return booleanResponse;
    }

    /**
     * 根据ERP信息过滤资源
     * @param request ERP资源请求对象，包含appKey、appToken和tenantCode等信息
     * @return 过滤后的资源列表
     */
    @Override
    public Response<List<String>> filterResourcesByErp(ErpResRequest request){
        request.setAppKey(appKey);
        request.setAppToken(appToken);
        request.setTenantCode(BaseRequest.TENANT_CODE_CN);
        Response<List<String>> listResponse = filterResourceFacade.filterResourcesByErp(request);
        return listResponse;
    }

    /**
     * 查询系统所有菜单。
     * @param request 基础请求对象，包含租户代码、应用密钥和应用令牌。
     * @return 系统所有菜单的列表。
     */
    @Override
    public Response<List<MenuDto>> querySystemAllMenu(BaseRequest request){
        request.setTenantCode(BaseRequest.TENANT_CODE_CN);
        request.setAppKey(appKey);
        request.setAppToken(appToken);
        Response<List<MenuDto>> listResponse = menuFacade.querySystemAllMenu(request);
        return listResponse;
    }

    /**
     * 获取根菜单信息。
     * @param request 用户请求对象，包含租户代码、应用密钥和应用令牌。
     * @return 根菜单信息列表。
     */
    @Override
    public Response<List<MenuDto>> getRootMenu(UserRequest request){
        request.setTenantCode(BaseRequest.TENANT_CODE_CN);
        request.setAppKey(appKey);
        request.setAppToken(appToken);
        Response<List<MenuDto>> rootMenu = menuFacade.getRootMenu(request);
        return rootMenu;
    }

    /**
     * 获取菜单树结构。
     * @param request 用户请求对象，包含租户代码、应用密钥和应用令牌。
     * @return 菜单树结构的响应对象。
     */
    @Override
    public Response<List<MenuDto>> getMenuTree(UserRequest request){
        request.setTenantCode(BaseRequest.TENANT_CODE_CN);
        request.setAppKey(appKey);
        request.setAppToken(appToken);
        Response<List<MenuDto>> menuTree = menuFacade.getMenuTree(request);
        return menuTree;
    }

    /**
     * 获取菜单功能列表
     * @param request 用户请求信息，包含租户代码、应用密钥和应用令牌
     * @return 菜单功能列表
     */
    @Override
    public Response<List<FuncDto>> getMenuFun(UserResRequest request){
        request.setTenantCode(BaseRequest.TENANT_CODE_CN);
        request.setAppKey(appKey);
        request.setAppToken(appToken);
        Response<List<FuncDto>> menuFun = menuFacade.getMenuFun(request);
        return menuFun;
    }

    /**
     * 获取所有菜单功能。
     * @param request 用户请求对象，包含租户代码、应用程序密钥和令牌。
     * @return 包含所有菜单功能的响应对象。
     */
    @Override
    public Response<List<MenuDto>> getAllMenuFunc(UserRequest request){
        request.setTenantCode(BaseRequest.TENANT_CODE_CN);
        request.setAppKey(appKey);
        request.setAppToken(appToken);
        Response<List<MenuDto>> response = menuFacade.getAllMenuFunc(request);
        return response;
    }



    /**
     * 根据资源代码获取角色列表。
     * @param request 包含资源代码的请求对象。
     * @return 角色列表的响应对象。
     */
    @Override
    public Response<List<RoleDto>> getRoleByResCode(ErpResRequest request){
        request.setAppKey(appKey);
        request.setAppToken(appToken);
        request.setTenantCode(BaseRequest.TENANT_CODE_CN);
        Response<List<RoleDto>> roleByResCode = roleFacade.getRoleByResCode(request);
        return roleByResCode;
    }

    /**
     * 根据给定的条件查询维度资源值列表。
     * @param request 包含查询条件的请求对象。
     * @return 维度资源值列表的响应对象。
     */
    @Override
    public Response<List<DimResourceDto>> queryDimResValueList(DimResCodeRequest request){
        request.setAppKey(appKey);
        request.setAppToken(appToken);
        request.setTenantCode(BaseRequest.TENANT_CODE_CN);
        Response<List<DimResourceDto>> listResponse = dimPermissionFacade.queryDimResValueList(request);
        return listResponse;
    }

    @Override
    @JProfiler(mState = JProEnum.TP, jKey = "AuthRpcServiceImpl.getUserByRoleList")
    public Response<List<UserDto>> getUserByRoleList(RoleListRequest request) {
        request.setAppKey(appKey);
        request.setAppToken(appToken);
        request.setTenantCode(BaseRequest.TENANT_CODE_CN);
        log.info("AuthRpcServiceImpl.getUserByRoleList 根据角色资源码查询用户列表 request:{}", JSON.toJSONString(request));
        Response<List<UserDto>> userByRoleList = userFacade.getUserByRoleList(request);
        log.info("AuthRpcServiceImpl.getUserByRoleList 根据角色资源码查询用户列表 request:{} ,response={}", JSON.toJSONString(request),JSON.toJSONString(userByRoleList));
        return userByRoleList;
    }

    /**
     * 查询erp所属角色
     */
    @Override
    public Response<List<RoleDto>> queryRoleByErp(ErpRequest erpRequest){
        erpRequest.setAppKey(appKey);
        erpRequest.setAppToken(appToken);
        erpRequest.setTenantCode(BaseRequest.TENANT_CODE_CN);
        return roleFacade.getRole(erpRequest);
    }

}
