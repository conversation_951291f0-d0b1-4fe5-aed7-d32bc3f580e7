package com.jdi.isc.product.soa.rpc.adapter.mapstruct.auth;

import com.jd.auth.facade.request.datares.DataResRequest;
import com.jd.auth.facade.request.dim.DimResCodeRequest;
import com.jd.auth.facade.request.user.ErpResRequest;
import com.jd.auth.facade.request.user.UserRequest;
import com.jd.auth.facade.request.user.UserResRequest;
import com.jdi.isc.product.soa.domain.auth.AuthReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AuthConvert {

    AuthConvert INSTANCE = Mappers.getMapper(AuthConvert.class);

    /**
     * 将 AuthReqVO 对象转换为 ErpResRequest 对象。
     * @param vo AuthReqVO 对象，包含请求信息。
     * @return 转换后的 ErpResRequest 对象。
     */
    ErpResRequest vo2ErpResRequest(AuthReqVO vo);

    /**
     * 将 AuthReqVO 对象转换为 UserRequest 对象
     * @param vo AuthReqVO 对象，包含用户请求的详细信息
     * @return 转换后的 UserRequest 对象
     */
    UserRequest vo2UserRequest(AuthReqVO vo);

    /**
     * 将 AuthReqVO 对象转换为 UserResRequest 对象。
     * @param vo AuthReqVO 对象，包含用户认证请求的信息。
     * @return 转换后的 UserResRequest 对象。
     */
    UserResRequest vo2UserResRequest(AuthReqVO vo);

    /**
     * 将 AuthReqVO 对象转换为 DimResCodeRequest 对象。
     * @param vo AuthReqVO 对象，包含认证请求的必要信息。
     * @return 转换后的 DimResCodeRequest 对象。
     */
    DimResCodeRequest vo2DimResCodeRequest(AuthReqVO vo);

    /**
     * 将AuthReqVO对象转换为DataResRequest对象。
     * @param vo AuthReqVO对象，包含身份验证所需的信息。
     * @return 转换后的DataResRequest对象。
     */
    DataResRequest vo2DataResRequest(AuthReqVO vo);
}
