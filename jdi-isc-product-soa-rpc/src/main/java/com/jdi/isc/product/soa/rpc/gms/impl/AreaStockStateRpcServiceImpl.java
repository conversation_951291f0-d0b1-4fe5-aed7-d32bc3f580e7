package com.jdi.isc.product.soa.rpc.gms.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.stock.base.CallerParam;
import com.jd.stock.state.export.AreaStockStateExport;
import com.jd.stock.state.export.vo.param.AreaStockStateGlobalParam;
import com.jd.stock.state.export.vo.param.SkuNumParam;
import com.jdi.isc.aggregate.read.api.stock.req.MkuStockInfo;
import com.jdi.isc.product.soa.common.util.HostUtil;
import com.jdi.isc.product.soa.domain.stock.vo.MkuStockInfoVO;
import com.jdi.isc.product.soa.rpc.gms.AreaStockStateRpcService;
import com.jdi.isc.product.soa.stock.mku.res.IscMkuStockResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 中台区域库存查询服务
 * <AUTHOR>
 * @date 20240513
 */
@Slf4j
@Service
public class AreaStockStateRpcServiceImpl implements AreaStockStateRpcService {

    private final static String DEFAULT_TIME_ZONE = "GMT+8";
    private final static String DEFAULT_LANG = "zh";
    private final static String SYSTEM_NAME = "jdi-isc-aggregate-read";
    private final static int DEFAULT_BU_ID = 328;
    private final static int DEFAULT_TENANT_ID = 1024;
    private final static int DEFAULT_CHANNEL_ID = 11;
    private final static String DEFAULT_NATION_ID = "CN";
    private final static Integer MAX_INPUT = 20;
    private final static String OUT_OF_STOCK = "34";
    private static final String SOLD_OUT = "无货";

    @Resource
    private AreaStockStateExport areaStockStateExport;

    //https://cf.jd.com/pages/viewpage.action?pageId=168188544
    @Override
    public Map<Long, MkuStockInfoVO> queryAreaStockStateGlobal(AreaStockStateGlobalParam areaReq) {
        Map<Long, MkuStockInfoVO> result = new HashMap<>();
        CallerParam callerParam = buildCallerParam();
        Map<String, Map<String, String>> res = new HashMap<>();
        areaReq.setChannelId(DEFAULT_CHANNEL_ID);
        List<SkuNumParam> sku = areaReq.getSkuNumList();
        if(sku.size()<=MAX_INPUT){
            res = queryAreaStock(callerParam, areaReq);
        }else {
            List<List<SkuNumParam>> partSku = Lists.partition(sku, MAX_INPUT);
            for(List<SkuNumParam> partInput : partSku){
                areaReq.setSkuNumList(partInput);
                Map<String, Map<String, String>> partRes = queryAreaStock(callerParam, areaReq);
                if(MapUtils.isNotEmpty(partRes)){
                    res.putAll(partRes);
                }
            }
        }
        if(MapUtils.isNotEmpty(res)){
            Map<Long, SkuNumParam> skuNumParamMap = sku.stream().collect(Collectors.toMap(SkuNumParam::getSkuId, Function.identity()));
            for (Map.Entry<String, Map<String, String>> entry : res.entrySet()) {
                Long skuId = Long.valueOf(entry.getKey());
                MkuStockInfoVO mkuStock = new MkuStockInfoVO();
                mkuStock.setMkuId(skuId);
                // 库存实际数量
                Integer resNum = Integer.valueOf(entry.getValue().get("c"));
                mkuStock.setNum(resNum);
                mkuStock.setStockStateType(entry.getValue().get("a"));
                mkuStock.setStockStateDesc(entry.getValue().get("b"));
                if (skuNumParamMap.containsKey(skuId) && Objects.nonNull(skuNumParamMap.get(skuId))) {
                    SkuNumParam skuNumParam = skuNumParamMap.get(skuId);
                    // 查询库存数量大于已有库存数量，返回库存不足 库存无货状态时无货
                    // skuNumParam.getNum() > resNum ||
                    if (mkuStock.getStockStateType().equals(OUT_OF_STOCK) || (resNum > 0 && skuNumParam.getNum() > resNum)) {
                        mkuStock.setStockStateType(OUT_OF_STOCK);
                        mkuStock.setStockStateDesc(SOLD_OUT);
                    }
                }
                result.put(skuId,mkuStock);
            }
        }
        return result;
    }

    public Map<String, Map<String, String>> queryAreaStock(CallerParam callerParam,AreaStockStateGlobalParam areaReq) {
        Map<String, Map<String, String>> res = new HashMap<>();
        try {
            res = areaStockStateExport.queryAreaStockStateGlobal(callerParam, areaReq);
        }finally {
            log.info("AreaStockStateRpcServiceImpl.queryAreaStockStateGlobal callerParam:{} , req: {} res:{}",
                    JSON.toJSONString(callerParam),
                    JSON.toJSONString(areaReq),
                    JSON.toJSONString(res));
        }
        return res;
    }

//    https://cf.jd.com/pages/viewpage.action?pageId=165577572
    private CallerParam buildCallerParam() {
        CallerParam callerParam = new CallerParam();
        callerParam.setBuId(DEFAULT_BU_ID);
        callerParam.setSystemName(SYSTEM_NAME);
        callerParam.setNationId(DEFAULT_NATION_ID);
        callerParam.setTimezone(DEFAULT_TIME_ZONE);
        callerParam.setLanguage(DEFAULT_LANG);
        callerParam.setSysIp(HostUtil.getJsfIp());
        callerParam.setTenantId(DEFAULT_TENANT_ID);
        return callerParam;
    }

}
