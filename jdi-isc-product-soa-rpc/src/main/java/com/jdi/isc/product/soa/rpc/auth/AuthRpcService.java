package com.jdi.isc.product.soa.rpc.auth;

import com.jd.auth.facade.request.BaseRequest;
import com.jd.auth.facade.request.dim.DimResCodeRequest;
import com.jd.auth.facade.request.role.RoleListRequest;
import com.jd.auth.facade.request.user.ErpRequest;
import com.jd.auth.facade.request.user.ErpResRequest;
import com.jd.auth.facade.request.user.UserRequest;
import com.jd.auth.facade.request.user.UserResRequest;
import com.jd.auth.facade.response.Response;
import com.jd.auth.facade.response.menu.FuncDto;
import com.jd.auth.facade.response.menu.MenuDto;
import com.jd.auth.facade.response.res.DimResourceDto;
import com.jd.auth.facade.response.role.RoleDto;
import com.jd.auth.facade.response.user.UserDto;

import java.util.List;

public interface AuthRpcService {
    /**
     * 检查ERP资源是否可用。
     * @param request ERP资源请求对象，包含资源的相关信息。
     * @return 如果资源可用则返回true，否则返回false。
     */
    Response<Boolean> checkResource(ErpResRequest request);

    /**
     * 根据ERP资源请求过滤资源
     * @param request ERP资源请求对象，包含过滤条件
     * @return 满足条件的资源列表
     */
    Response<List<String>> filterResourcesByErp(ErpResRequest request);

    /**
     * 查询系统所有菜单
     * @param request 基础请求对象
     * @return 包含所有菜单信息的响应对象
     */
    Response<List<MenuDto>> querySystemAllMenu(BaseRequest request);

    /**
     * 获取用户的根菜单列表。
     * @param request 用户请求对象，包含用户信息等。
     * @return 根菜单列表的响应结果。
     */
    Response<List<MenuDto>> getRootMenu(UserRequest request);

    /**
     * 获取菜单树结构。
     * @param request 用户请求对象，包含用户信息和其他必要的上下文信息。
     * @return 包含菜单树结构的响应对象。
     */
    Response<List<MenuDto>> getMenuTree(UserRequest request);

    /**
     * 获取用户菜单和功能列表
     * @param request 用户请求对象，包含用户信息等
     * @return 包含菜单和功能的列表
     */
    Response<List<FuncDto>> getMenuFun(UserResRequest request);

    /**
     * 获取所有菜单信息。
     * @param request 用户请求对象，包含用户信息和其他必要的参数。
     * @return 包含所有菜单信息的响应对象。
     */
    Response<List<MenuDto>> getAllMenuFunc(UserRequest request);

    /**
     * 根据资源代码获取角色列表
     * @param request 包含资源代码的请求对象
     * @return 角色列表的响应结果
     */
    Response<List<RoleDto>> getRoleByResCode(ErpResRequest request);

    /**
     * 根据维度资源代码请求，查询维度资源值列表。
     * @param request 维度资源代码请求对象，包含查询条件。
     * @return 维度资源值列表的响应对象。
     */
    Response<List<DimResourceDto>> queryDimResValueList(DimResCodeRequest request);

    /**
     * 根据角色资源码列表获取用户信息
     * @param request 角色列表请求对象
     * @return 用户信息列表
     */
    Response<List<UserDto>> getUserByRoleList(RoleListRequest request);


    /**
     * 查询erp所属角色
     */
    Response<List<RoleDto>> queryRoleByErp(ErpRequest erpRequest);

}
