package com.jd.international.soa.common.ducc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.international.soa.common.domain.authority.biz.FunctionAuthorityConfigVO;
import com.jd.international.soa.common.ducc.biz.ChatGptProperties;
import com.jd.laf.config.spring.annotation.LafValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2024/8/17
 **/
@Slf4j
@Component
public class DuccConfig {

    @LafValue("function.authority.config")
    private String functionAuthorityConfig;
    @LafValue("chatGpt.config")
    private String chatGptConfig;
    @LafValue("jdi.isc.wisp.search.prompts")
    private String wispSearchPrompts;

    /**
     * 功能权限配置
     * @return
     */
    public FunctionAuthorityConfigVO queryFunctionAuthorityConfig(){
        if (StringUtils.isBlank(functionAuthorityConfig)){
            log.info("ducc functionAuthorityConfig null.");
            return new FunctionAuthorityConfigVO();
        }

        return JSONObject.parseObject(functionAuthorityConfig, FunctionAuthorityConfigVO.class);
    }

    public ChatGptProperties preseChatGpt(){
        try {
            ChatGptProperties chatGptProperties = JSON.parseObject(chatGptConfig, ChatGptProperties.class);
            log.info("DuccConfig.preseChatGpt chatGpt:{}",JSON.toJSONString(chatGptProperties));
            return chatGptProperties;
        }catch (Exception e){
            ChatGptProperties chatGptProperties = new ChatGptProperties();
            log.error("DuccConfig.preseChatGpt 解析chatGptConfig配置异常，e:{}",e.getMessage(),e);
            return chatGptProperties;
        }
    }

    public String getWispPrompts() {
        if  (StringUtils.isBlank(wispSearchPrompts)) {
            return  "Your role is an AI query analysis engine for a B2B industrial goods marketplace. Your task is to refine the user's raw search query into a set of core technical keywords suitable for a high-precision search engine.\n" +
                    "IMPORTANT: The backend search engine splits keywords by spaces and requires that all keywords must co-exist in the product data (AND logic). Therefore, you must not add speculative or overly broad terms, as this will cause the search to fail.\n" +
                    "Process the following search term: (\"%s\"), in language: (%s).\n" +
                    "Processing Requirements:\n" +
                    "Identify Core Product: Accurately identify the core product or component in the user's query.\n" +
                    "Extract Key Specifications: Extract explicit technical specifications, such as model numbers, dimensions, materials, or standards (e.g., \"M5\", \"304 stainless steel\", \"10mm\", \"IP67\").\n" +
                    "Remove Extraneous Words: Delete all non-technical, conversational, or descriptive words (e.g., \"I want\", \"looking for\", \"heavy-duty\").\n" +
                    "Standardize Terminology: Convert common or ambiguous terms into standard industrial vocabulary (e.g., \"computer internet cable\" -> \"ethernet patch cable\").\n" +
                    "Output Format:\n" +
                    "The final result must be a single line of text.\n" +
                    "Include only the refined core technical keywords, separated by a single space.\n" +
                    "Strictly prohibit any introductions, explanations, or bullet points.";
        }
        return wispSearchPrompts;
    }

}
