package com.jd.international.soa.common.ducc.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChatGptProperties {
    /**
     * ChatGPT API的URL地址。
     */
    private String url;
    /**
     * ChatGPT API的密钥。
     */
    private String chatGptKeys;

    /**
     * 存储ChatGPT支持的模型及其对应的参数配置。 type-model， 短标题-40
     */
    private Map<String,String> chatGptModel = new HashMap<>();
    /**
     * 存储用户提供的初始提示信息，用于启动与ChatGPT的对话。 国家-提示 BR-XXXX
     */
    private Map<String,String> prompt = new HashMap<>();

    /**
     * 存储不同国家或地区的初始提示信息对应的最大内容长度。 国家-长度，BR-40
     */
    private Map<String,Integer> contentLength = new HashMap<>();

    /**
     * 存储需要过滤的关键词列表，用于在与ChatGPT交互时过滤掉不合适的内容。
     */
    private List<String> filterList = new ArrayList<>();

    /**
     * 与ChatGPT交互的循环次数。
     */
    private Integer cycleNumber;
    /**
     * 短标题key
     */
    public static final String SUBTITLE = "subtitle";
    /**
     * 长标题key
     */
    public static final String LONGTITLE = "longtitle";
    /**
     * 翻译key
     */
    public static final String TRANSLATORS = "translate";
    /**
     * 搜索key
     */
    public static final String SEARCH = "search";
    /**
     * 默认
     */
    public static final String DEFAULT = "default";

}
