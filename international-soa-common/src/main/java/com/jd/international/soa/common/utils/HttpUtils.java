package com.jd.international.soa.common.utils;


import com.alibaba.fastjson.JSON;
import com.jd.jsf.gd.util.JsonUtils;
import okhttp3.*;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.zip.CRC32;
import java.util.zip.CheckedInputStream;

public class HttpUtils {
    private static volatile OkHttpClient okHttpClient = null;
    private static volatile Semaphore semaphore = null;
    private Map<String, String> headerMap;
    private Map<String, String> paramMap;
    private String url;
    private Request.Builder request;
    private static final Log log = LogFactory.getLog(HttpUtils.class);
    private static final String CONTENT_TYPE = "Content-Type";

    /**
     * 初始化okHttpClient，并且允许https访问
     */
    private HttpUtils() {
        if (okHttpClient == null) {
            synchronized (HttpUtils.class) {
                if (okHttpClient == null) {
                    TrustManager[] trustManagers = buildTrustManagers();
                    okHttpClient = new OkHttpClient.Builder()
                            .connectTimeout(15, TimeUnit.SECONDS)
                            .writeTimeout(20, TimeUnit.SECONDS)
                            .readTimeout(20, TimeUnit.SECONDS)
                            .sslSocketFactory(createSSLSocketFactory(trustManagers), (X509TrustManager) trustManagers[0])
                            .hostnameVerifier((hostName, session) -> true)
                            .retryOnConnectionFailure(true)
                            .build();
                }
            }
        }
    }
    /*
     * 上传图片
     * */
    public static String fileUpload(byte[] var0, String var1) {
        String var2 = null;
        if(var0 != null && var0.length > 0) {
            BufferedInputStream var3 = new BufferedInputStream(new ByteArrayInputStream(var0));

            HttpPost var5 = new HttpPost("http://upload.erp.360buyimg.com/imageUpload.action");
            HttpParams var6 = new BasicHttpParams();
            HttpConnectionParams.setConnectionTimeout(var6,20000);
            HttpConnectionParams.setSoTimeout(var6,20000);
            HttpClient var4 = new DefaultHttpClient(var6);

            try {
                var5.addHeader("aucode", var1);
                var5.addHeader("type", "2");
                var5.addHeader("keycode", getFileCRCCode(var3) + var0.length);
                var5.setEntity(new ByteArrayEntity(var0));

                HttpResponse httpResponse = var4.execute(var5);

                int statusCode = httpResponse.getStatusLine().getStatusCode();
                if (statusCode != 200) {
                    var5.abort();
                    throw new RuntimeException("HttpClient,error status code :" + statusCode);
                }

                HttpEntity entity = httpResponse.getEntity();
                if (entity != null){
                    var2 = EntityUtils.toString(entity);
                }

            } catch (Exception var11) {
                log.error(var1 + "_" + var11.getMessage(), var11);
            } finally {
                var5.releaseConnection();
                var4.getConnectionManager().closeIdleConnections(30, TimeUnit.SECONDS);
            }
        }
        return var2;
    }
    public static String getFileCRCCode(InputStream var0) {
        CRC32 var1 = new CRC32();

        try {
            BufferedInputStream var2 = new BufferedInputStream(var0);
            CheckedInputStream var3 = new CheckedInputStream(var2, var1);

            while(var3.read() != -1) {
                ;
            }
        } catch (Exception var4) {
            var4.printStackTrace();
        }

        return Long.toHexString(var1.getValue());
    }
    /**
     * 用于异步请求时，控制访问线程数，返回结果
     *
     * @return
     */
    private static Semaphore getSemaphoreInstance() {
        //只能1个线程同时访问
        synchronized (HttpUtils.class) {
            if (semaphore == null) {
                semaphore = new Semaphore(0);
            }
        }
        return semaphore;
    }

    /**
     * 创建OkHttpUtils
     *
     * @return
     */
    public static HttpUtils builder() {
        return new HttpUtils();
    }

    /**
     * 添加url
     *
     * @param url
     * @return
     */
    public HttpUtils url(String url) {
        this.url = url;
        return this;
    }

    /**
     * 添加参数
     *
     * @param key   参数名
     * @param value 参数值
     * @return
     */
    public HttpUtils addParam(String key, String value) {
        if (paramMap == null) {
            paramMap = new LinkedHashMap<>(16);
        }
        paramMap.put(key, value);
        return this;
    }
    /**
     * 添加参数
     *
     * @param param   参数
     * @return
     */
    public HttpUtils addParam(Object param) {
        if (paramMap == null) {
            paramMap = new LinkedHashMap<>(16);
        }
        if (param == null) {
            return this;
        }
        Class clazz = param.getClass();
        Field[] fields = clazz.getDeclaredFields();
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(param);
                if(Optional.ofNullable(value).isPresent()){
                    paramMap.put(field.getName(), value.toString());
                }
            }
        } catch (Exception e) {
            log.error("http请求添加参数异常", e);
        }
        return this;
    }
    /**
     * 添加请求头
     *
     * @param key   参数名
     * @param value 参数值
     * @return
     */
    public HttpUtils addHeader(String key, String value) {
        if (headerMap == null) {
            headerMap = new LinkedHashMap<>(16);
        }
        headerMap.put(key, value);
        return this;
    }

    /**
     * 初始化get方法
     *
     * @return
     */
    public HttpUtils get() {
        request = new Request.Builder().get();
        StringBuilder urlBuilder = new StringBuilder(url);
        try {
            if (paramMap != null) {
                if (url.indexOf("?") != -1) {
                    urlBuilder.append("&");
                } else {
                    urlBuilder.append("?");
                }
                for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                    urlBuilder.append(URLEncoder.encode(entry.getKey(), "utf-8")).
                            append("=").
                            append(URLEncoder.encode(entry.getValue(), "utf-8")).
                            append("&");
                }
                urlBuilder.deleteCharAt(urlBuilder.length() - 1);
            }
            request.url(urlBuilder.toString());
        }catch (Exception e) {
            log.error("拼接查询url出错，msg: "+ e.getMessage()+", 入参[paramMap: "+paramMap +"]" , e);
        }finally {
            return this;
        }
    }

    /**
     * 初始化post方法
     *
     * @param isJsonPost true等于json的方式提交数据，类似postman里post方法的raw
     *                   false等于普通的表单提交
     * @return
     */
    public HttpUtils post(boolean isJsonPost) {
        RequestBody requestBody;
        if (isJsonPost) {
            String json = "";
            if (paramMap != null) {
                json = JsonUtils.toJSONString(paramMap);
            }
            requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);
        } else {
            FormBody.Builder formBody = new FormBody.Builder();
            if (paramMap != null) {
                paramMap.forEach(formBody::add);
            }
            requestBody = formBody.build();
        }
        request = new Request.Builder().post(requestBody).url(url);
        return this;
    }

    /**
     * 同步请求
     *
     * @return
     */
    public String sync() {
        setHeader(request);
        try {
            Response response = okHttpClient.newCall(request.build()).execute();
            return response.body().string();
        } catch (IOException e) {
            log.error("HttpUtils.sync() http接口调用失败:url=" + url + "；param=" + paramMap, e);
            return null;
        }
    }

    /**
     * 异步请求，有返回值
     */
    public String async() {
        StringBuilder buffer = new StringBuilder("");
        setHeader(request);
        okHttpClient.newCall(request.build()).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                buffer.append("请求出错：").append(e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                assert response.body() != null;
                buffer.append(response.body().string());
                getSemaphoreInstance().release();
            }
        });
        try {
            getSemaphoreInstance().acquire();
        } catch (InterruptedException e) {
            log.error("HttpUtils.async() http接口调用失败:url=" + url + "；param=" + paramMap, e);
        } finally {
            return buffer.toString();
        }
    }

    /**
     * 异步请求，带有接口回调
     *
     * @param callBack
     */
    public void async(ICallBack callBack) {
        setHeader(request);
        okHttpClient.newCall(request.build()).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                callBack.onFailure(call, e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                assert response.body() != null;
                callBack.onSuccessful(call, response.body().string());
            }
        });
    }

    /**
     * 为request添加请求头
     *
     * @param request
     */
    private void setHeader(Request.Builder request) {
        if (headerMap != null) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                request.addHeader(entry.getKey(), entry.getValue());
            }
        }
    }


    /**
     * 生成安全套接字工厂，用于https请求的证书跳过
     *
     * @return
     */
    private static SSLSocketFactory createSSLSocketFactory(TrustManager[] trustAllCerts) {
        SSLSocketFactory ssfFactory = null;
        try {
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            ssfFactory = sc.getSocketFactory();
        } catch (Exception e) {
            log.error("生成安全套接字工厂失败", e);
        }
        return ssfFactory;
    }

    private static TrustManager[] buildTrustManagers() {
        return new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[]{};
                    }
                }
        };
    }

    /**
     * 自定义一个接口回调
     */
    public interface ICallBack {

        void onSuccessful(Call call, String data);

        void onFailure(Call call, String errorMsg);

    }

    /**
     *
     * @param url
     * @param headers
     * @param params
     * @param json
     * @return
     * @throws IOException
     */
    public String postJSON(String url,
                           Map<String, String> headers,
                           Map<String, String> params,
                           Object json) throws IOException {
        if(MapUtils.isEmpty(headers)){
            headers = initHeaders();
        }
        headers.put(CONTENT_TYPE, ContentType.JSON.toString());
        return post(url,headers,params,getJSONBody(json));
    }

    private Map<String, String> initHeaders(){
        Map<String,String> result = new HashMap<>();
        result.put("accept", "*/*");
        result.put("connection", "Keep-Alive");
        result.put("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1)");
        return result;
    }

    /**
     * Content-Type: application/json
     *
     * @param json
     * @return
     */
    private RequestBody getJSONBody(Object json){
        return getRawBody(JSON.toJSONString(json),MediaType.parse(ContentType.JSON.toString()));
    }

    /**
     * @param content
     * @param type
     * @return
     */
    private RequestBody getRawBody(String content, MediaType type){
        return RequestBody.create(type,content);
    }

    /**
     * post form表单 请求，同步方式，提交数据，是在主线程中执行的，需要新起线程，将其放到子线程中执行
     *
     * @param url
     * @param headers
     * @param params
     * @param requestBody
     * @return
     * @throws IOException
     */
    private String post(String url,
                        Map<String, String> headers,
                        Map<String, String> params,
                        RequestBody requestBody) throws IOException {

        Request request = new Request.Builder()
                .url(getHttpUrl(url,params))
                .headers(getHeaders(headers))
                .post(requestBody)
                .build();

        Response response = okHttpClient.newCall(request).execute();
        return response.body().string();
    }

    /**
     * Set URI
     * @param url
     * @param params
     */
    private HttpUrl getHttpUrl(String url,
                               Map<String, String> params) {
        HttpUrl.Builder newBuilder = HttpUrl.parse(url).newBuilder();
        if (MapUtils.isNotEmpty(params)) {
            // Set params
            for (Map.Entry<String, String> stringStringEntry : params.entrySet()) {
                newBuilder.addQueryParameter(stringStringEntry.getKey(), stringStringEntry.getValue());
            }
        }
        return newBuilder.build();
    }
    /**
     * Set Header
     * @param headers 请求头数据
     */
    private Headers getHeaders(Map<String, String> headers){
        if(MapUtils.isNotEmpty(headers)){
            return Headers.of(headers);
        }
        return new Headers.Builder().build();
    }
}


