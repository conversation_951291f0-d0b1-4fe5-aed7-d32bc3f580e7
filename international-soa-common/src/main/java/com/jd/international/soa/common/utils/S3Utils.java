package com.jd.international.soa.common.utils;

import cn.hutool.core.io.FileUtil;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.services.s3.model.S3Object;
import com.jd.international.soa.common.enums.FileTypeEnum;
import com.jd.laf.config.spring.annotation.LafValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.nio.file.Paths;

/**
 * s3工具类
 *
 * <AUTHOR>
 * @date 20231117
 */
@Service
@Slf4j
public class S3Utils {

    @Value("${s3.default.bucket}")
    private String bucket;
    @Value("${s3.protocol}")
    private String protocol;
    @Value("${s3.external.endpoint}")
    private String externalEndpoint;
    @LafValue("s3.host.replace")
    private Boolean enableHostReplace;
    private final static String DOT = ".";
    private final static String DEFAULT_FOLDER = "d1";
    private final static String DEFAULT_CONTENT_TYPE = "multipart/form-data";
    private static final String REPLACE_HOST = "jdios.jdindustry.com";

    @Resource
    private AmazonS3 s3InternalClient;

    /**
     * 文件上传
     *
     * @param inputStream 文件流
     * @param key         bucket+路径+文件名,如jdi-intl/d1/demo.jpg
     * @return 外链地址
     */
    public String upload(InputStream inputStream, int type, String key) {
        PutObjectResult res = null;
        try {
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType(DEFAULT_CONTENT_TYPE);
            objectMetadata.setContentLength(inputStream.available());
            String path = FileTypeEnum.path(type);
            res = s3InternalClient.putObject(bucket, path + File.separator + key, inputStream, objectMetadata);
            return genUrl(path, key);
        } catch (Exception e) {
            log.error("S3Utils.upload error, target:{} ", key, e);
        } finally {
            log.info("S3Utils.upload req:{} , res:{}", key, res != null ? res.getContentMd5() : null);
        }
        return null;
    }

    /**
     * 文件上传
     * @return 外链地址
     */
    public String uploadFile(String url, int type) {
        PutObjectResult res = null;
        String key = FileUtil.getName(url);
        try {
            String path = FileTypeEnum.path(type);
            res = s3InternalClient.putObject(bucket, path + File.separator + key, new File(url));
            return genUrl(path, key);
        } catch (Exception e) {
            log.error("S3Utils.upload error, target:{} ", key, e);
        } finally {
            log.info("S3Utils.upload req:{} , res:{}", key, res != null ? res.getContentMd5() : null);
        }
        return null;
    }

    public String upload(String path) {
        String key = Paths.get(path).getFileName().toString();
        try {
            PutObjectResult res = s3InternalClient.putObject(bucket, key, new File(path));
            log.info("S3Utils.upload req:{} , res:{}", path, res.getContentMd5());
            return res.getContentMd5();
        } catch (Exception e) {
            log.error("S3Utils.upload error, target:{} ", path, e);
        }
        return null;
    }

    public S3Object download(String key) {
        try {
            return s3InternalClient.getObject(bucket, key);
        } catch (Exception e) {
            log.error("S3Utils.download error, target:{} ", key, e);
        }
        return null;
    }

    public String delete(String key) {
        try {
            s3InternalClient.deleteObject(bucket, key);
        } catch (Exception e) {
            log.error("S3Utils.delete error,target:{}", key, e);
            return "failed";
        }
        return "sucess";
    }

//    public void download(String key,String path){
//        try {
//            S3Object o = s3InternalClient.getObject(bucket, key);
//            S3ObjectInputStream s3is = o.getObjectContent();
//            FileUtils.writeByteArrayToFile(new File(path), IOUtils.toByteArray(s3is));
//        } catch (Exception e) {
//            log.error("S3Utils.download error, target:{} ", key,e);
//        }
//    }

    public String genUrl(String path, String key) {
        return genUrl(externalEndpoint, path, key);
    }

    public String genUrl(String endpoint, String path, String key) {
        return protocol + bucket + DOT + endpoint + File.separator + path + File.separator + key;
    }

    public String getETag(String key) {
        String eTag = null;
        try {
            log.info("S3Utils.download  param:{} ", key);
            ObjectMetadata metadata = s3InternalClient.getObjectMetadata(bucket, key);
            eTag = metadata.getETag();
            return eTag;
        } catch (Exception e) {
            log.error("S3Utils.download error, target:{} ", key, e);
        } finally {
            log.error("S3Utils.download success, key:{},eTag:{} ", key, eTag);
        }
        return null;
    }

    public String getHost() {
        return bucket + DOT + externalEndpoint;
    }

    public String replaceHost(String filePath) {
        if (!enableHostReplace) {
            return filePath;
        }
        if (StringUtils.isBlank(filePath)) {
            return filePath;
        }
        return filePath.replace(getHost(), REPLACE_HOST);
    }

}
