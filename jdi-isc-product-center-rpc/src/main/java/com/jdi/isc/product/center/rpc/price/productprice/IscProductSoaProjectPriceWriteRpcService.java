package com.jdi.isc.product.center.rpc.price.productprice;

import com.jdi.isc.product.center.rpc.apporder.AbstractRpcService;
import com.jdi.isc.product.soa.api.price.projectPrice.IscProductSoaProjectPriceWriteApiService;
import com.jdi.isc.product.soa.api.price.projectPrice.req.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * The type Isc product soa project price write rpc service.
 *
 * <AUTHOR>
 */
@Component
public class IscProductSoaProjectPriceWriteRpcService extends AbstractRpcService {

    @Resource
    private IscProductSoaProjectPriceWriteApiService iscProductSoaProjectPriceWriteApiService;

    /**
     * 上传Excel文件进行项目价格申请审核
     * @param input 项目价格申请审核的输入数据
     * @return 上传结果，true表示成功，false表示失败
     */
    public Long uploadExcel(ProjectPriceApplyImportApiDTO input) {
        return unWriteWrapper(() -> iscProductSoaProjectPriceWriteApiService.uploadExcel(input), input, "解析excel文件失败！");
    }

    /**
     * 保存或更新项目价格申请产品的上报信息。
     * @param input 项目价格申请产品上报信息的DTO对象。
     * @return 保存或更新操作的结果，true表示成功，false表示失败。
     */
    public Boolean saveOrUpdate(ProjectPriceApplyForProductUpsertApiDTO input) {
        return unWriteWrapper(() -> iscProductSoaProjectPriceWriteApiService.saveOrUpdate(input), input, "保存提交失败！");
    }

    /**
     * 撤回项目价格申请。
     * @param input 项目价格申请撤回的输入参数。
     * @return 撤回操作是否成功。
     */
    public Boolean productRevoke(ProjectPriceApplyAuditApiDTO input) {
        return unWriteWrapper(() -> iscProductSoaProjectPriceWriteApiService.productRevoke(input), input, "商品经理撤回失败！");
    }

    /**
     * 保存或更新并提交项目价格申请。
     * @param input 项目价格申请的更新或新增数据。
     * @return 提交结果。
     */
    public Boolean saveOrUpdateAndSubmit(ProjectPriceApplyForBuyerUpsertApiDTO input) {
        return unWriteWrapper(() -> iscProductSoaProjectPriceWriteApiService.saveOrUpdateAndSubmit(input), input, "采销经理提交失败！");
    }

    /**
     * 采销驳回操作。
     * @param input 项目价格申请审核DTO对象，包含驳回所需的信息。
     * @return 操作结果，true表示成功，false表示失败。
     */
    public Boolean buyerReject(ProjectPriceApplyAuditApiDTO input) {
        return unWriteWrapper(() -> iscProductSoaProjectPriceWriteApiService.buyerReject(input), input, "采销驳回失败！");
    }

    /**
     * 上传Excel文件进行项目价格申请审核
     * @param input 项目价格申请审核的输入数据
     * @return 上传结果，true表示成功，false表示失败
     */
    public Integer updateExcelStatus(ProjectPriceUpdateImportStatusApiDTO input) {
        return unWriteWrapper(() -> iscProductSoaProjectPriceWriteApiService.updateExcelStatus(input), input, "更新项目单excel状态！");
    }


}
