package com.jdi.isc.product.center.rpc.customs;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.customs.req.ProductCustomsApiDTO;
import com.jdi.isc.product.soa.api.customs.req.ProductCustomsReqApiDTO;

import java.util.List;

public interface IscProductCustomsReadRpcService {

    DataResponse<List<ProductCustomsApiDTO>> queryList(ProductCustomsReqApiDTO reqApiDTO);
}
