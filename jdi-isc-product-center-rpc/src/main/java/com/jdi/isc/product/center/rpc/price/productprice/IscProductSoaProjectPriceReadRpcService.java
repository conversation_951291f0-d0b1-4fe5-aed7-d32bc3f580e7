package com.jdi.isc.product.center.rpc.price.productprice;

import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.rpc.apporder.AbstractRpcService;
import com.jdi.isc.product.soa.api.common.BaseDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.IscProductSoaProjectPriceReadApiService;
import com.jdi.isc.product.soa.api.price.projectPrice.IscProductSoaProjectPriceWriteApiService;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceApplyAuditApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceApplyForBuyerUpsertApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceApplyForProductUpsertApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.res.ProjectPriceApplyApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.res.ProjectPriceApplyPageApiDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * The type Isc product soa project price read api service.
 *
 * <AUTHOR>
 */
@Component
public class IscProductSoaProjectPriceReadRpcService extends AbstractRpcService {

    @Resource
    private IscProductSoaProjectPriceReadApiService iscProductSoaProjectPriceReadApiService;

    public PageInfo<ProjectPriceApplyPageApiDTO.Response> pageSearch(ProjectPriceApplyPageApiDTO.Request input) {
        return unReadWrapper(() -> iscProductSoaProjectPriceReadApiService.pageSearch(input), input, "查询失败！");
    }

    public ProjectPriceApplyApiDTO detail(BaseDTO input) {
        return unReadWrapper(() -> iscProductSoaProjectPriceReadApiService.detail(input), input, "查询失败！");
    }

}
