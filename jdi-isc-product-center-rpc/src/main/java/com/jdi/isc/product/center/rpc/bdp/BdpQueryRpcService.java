package com.jdi.isc.product.center.rpc.bdp;

import com.alibaba.fastjson.JSON;
import com.google.common.base.CaseFormat;
import com.jd.fds.lib.dto.server.ApiQueryRequest;
import com.jd.fds.lib.dto.server.FdsPage;
import com.jd.fds.lib.dto.server.FdsServerResult;
import com.jd.fds.lib.facade.QueryFacade;
import com.jdi.isc.product.center.api.devOps.req.ForeverLogDTO;
import com.jdi.isc.product.center.domain.bdp.ForeverLogVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

/**
 * easyData 企业部类目溢价率查询服务
 * <AUTHOR>
 * @date 20240328
 */
@Service
@Slf4j
public class BdpQueryRpcService {
    @Resource
    private QueryFacade queryFacade;

    private final static String LOG_API_GROUP_NAME = "isc_log";
    private final static String LOG_API_NAME = "iscOfflineLogQuery";
    private final static String LOG_CNT_API_NAME = "iscOfflineLogCnt";
    private final static String API_TOKEN = "6d09dcb86acb0233edbe7b118cf8f656";
    private final static String COUNT = "count";

    /**
     * 查询类目溢价率
     */
    public List<ForeverLogVO> queryOfflineLog(ForeverLogDTO foreverLogReq){
        long start = System.currentTimeMillis();
        FdsPage res = null;
        ApiQueryRequest req = new ApiQueryRequest();
        req.setApiGroupName(LOG_API_GROUP_NAME);
        req.setApiName(LOG_API_NAME);
        req.setAppToken(API_TOKEN);
        req.setPageNumber(foreverLogReq.getPageNum());
        req.setPageSize(foreverLogReq.getPageSize());
        try {
            Map<String, Object> input = new HashMap<>();
            input.put("dt","0-"+foreverLogReq.getDt());
            input.put("appName",foreverLogReq.getAppName());
            input.put("msg","%"+foreverLogReq.getKeyword()+"%");
            req.setRequestId(UUID.randomUUID().toString());
            req.setParams(input);
            res = queryFacade.queryPageByRouter(req);
            if(res==null || res.getStatus()!=200){
                throw new RuntimeException(JSON.toJSONString(req)+"日志查询异常:"+JSON.toJSONString(res));
            }
            if(CollectionUtils.isNotEmpty(res.getContent())){
                return buildResult(res.getContent());
            }
        }finally {
            log.info("BdpQueryRpcService.queryOfflineLog last:{} req:{} , res:{}" , (System.currentTimeMillis()-start), JSON.toJSONString(req) , JSON.toJSONString(res));
        }
        return null;
    }

    public Long queryOfflineLogCnt(ForeverLogDTO foreverLogReq){
        Long resultCnt = 0L;
        long start = System.currentTimeMillis();
        FdsServerResult res;
        ApiQueryRequest req = new ApiQueryRequest();
        req.setApiGroupName(LOG_API_GROUP_NAME);
        req.setApiName(LOG_CNT_API_NAME);
        req.setAppToken(API_TOKEN);
        try {
            Map<String, Object> input = new HashMap<>();
            input.put("dt","0-"+foreverLogReq.getDt());
            input.put("appName",foreverLogReq.getAppName());
            input.put("msg","%"+foreverLogReq.getKeyword()+"%");
            req.setRequestId(UUID.randomUUID().toString());
            req.setParams(input);
            res = queryFacade.queryByRouter(req);
            if(res==null || res.getStatus()!=200){
                throw new RuntimeException(JSON.toJSONString(req)+"日志查询异常:"+JSON.toJSONString(res));
            }
            if(CollectionUtils.isEmpty(res.getResult())){
                return resultCnt;
            }
            List<Map<String, Object>> mapRes = res.getResult();
            Object count = mapRes.get(0).get(COUNT);
            if(count instanceof Long){
                resultCnt = (Long) count;
            }
        }catch (Exception e){
            log.error("BdpQueryRpcService.queryOfflineLogCnt invoke error, req:{} ", JSON.toJSONString(req),e);
        }finally {
            log.info("BdpQueryRpcService.queryOfflineLogCnt last:{} req:{} , res:{}" , (System.currentTimeMillis()-start), JSON.toJSONString(req) , resultCnt);
        }
        return resultCnt;
    }

    @SneakyThrows
    private List<ForeverLogVO> buildResult(List<Map<String, Object>> result) {
        List<ForeverLogVO> res = new ArrayList<>(result.size());
        Class<?> clazz = ForeverLogVO.class;
        Field[] fields = clazz.getDeclaredFields();
        //下划线式属性名
        List<String> fieldNameList = new ArrayList<>(16);
        for (Field field : fields) {fieldNameList.add(field.getName());}
        for(Map<String, Object> item : result){
            ForeverLogVO vo = new ForeverLogVO();
            for(String fieldN : fieldNameList){
                Field field = clazz.getDeclaredField(fieldN);
                field.setAccessible(true);
                Object value = item.get(CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, fieldN));
                field.set(vo, value);
            }
            res.add(vo);
        }
        return res;
    }


    public static void main(String[] args) {
        List<String> fieldNameList = new ArrayList<>(16);
        Class<?> clazz = ForeverLogVO.class;
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {fieldNameList.add(CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, field.getName()));}
    }

}
