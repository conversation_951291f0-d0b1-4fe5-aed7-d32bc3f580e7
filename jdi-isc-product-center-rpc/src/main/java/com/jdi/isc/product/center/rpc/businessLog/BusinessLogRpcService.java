package com.jdi.isc.product.center.rpc.businessLog;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.biz.component.api.businessLog.IscBusinessLogReadApiService;
import com.jdi.isc.biz.component.api.businessLog.biz.BusinessLogDTO;
import com.jdi.isc.biz.component.api.businessLog.req.BusinessLogPageReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class BusinessLogRpcService {

    @Resource
    private IscBusinessLogReadApiService iscBusinessLogReadApiService;

    public PageInfo<BusinessLogDTO> queryPage(BusinessLogPageReq param) {
        PageInfo<BusinessLogDTO> result = null;
        try {
            DataResponse<PageInfo<BusinessLogDTO>> res = iscBusinessLogReadApiService.queryPage(param);
            result = res.getData();
        } catch (Exception e) {
            log.error("【系统异常】BusinessLogRpcService.queryPage", e);
        } finally {
            log.info("BusinessLogRpcService.queryPage req:{},res:{}", JSONObject.toJSONString(param), JSON.toJSONString(result));
        }
        return result;

    }
}
