package com.jdi.isc.product.center.rpc.countryMku.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.common.web.LoginContext;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.common.costants.Constant;
import com.jdi.isc.product.center.common.frame.LangContextHolder;
import com.jdi.isc.product.center.domain.countryMku.*;
import com.jdi.isc.product.center.rpc.adapter.mapstruct.countryMku.CountryMkuRpcConvert;
import com.jdi.isc.product.center.rpc.countryMku.CountryMkuRpcService;
import com.jdi.isc.product.soa.api.countryMku.IscCountryMkuReadApiService;
import com.jdi.isc.product.soa.api.countryMku.IscCountryMkuWriteApiService;
import com.jdi.isc.product.soa.api.countryMku.biz.*;
import com.jdi.isc.product.soa.api.spu.req.SpuLangApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 商品国家池表 rpc接口实现
 * @Author: wangpeng965
 * @Date: 2024/12/02 21:49
 **/

@Slf4j
@Service
public class CountryMkuRpcServiceImpl implements CountryMkuRpcService {

    @Resource
    private IscCountryMkuReadApiService iscCountryMkuReadApiService;

    @Resource
    private IscCountryMkuWriteApiService iscCountryMkuWriteApiService;

    @Override
    public PageInfo<CountryMkuPageVO.Response> pageSearch(CountryMkuPageVO.Request input){
        log.info("CountryMkuRpcServiceImpl.pageSearch, input={}", JSONObject.toJSONString(input));

        DataResponse<PageInfo<CountryMkuPageApiDTO.Response>> rpcResponse = null;
        try {
            CountryMkuPageApiDTO.Request param = CountryMkuRpcConvert.INSTANCE.pageVoReq2PageApiReq(input);
            param.setPin(LoginContext.getLoginContext().getPin());
            param.setLang(LangContextHolder.get());
            param.setSystemCode(Constant.SYSTEM_CODE);
            rpcResponse = iscCountryMkuReadApiService.pageSearch(param);
            if (!rpcResponse.getSuccess()){
                log.warn("pageSearch, rpcResponse fail. input={}", JSONObject.toJSONString(input));
                return null;
            }

            PageInfo<CountryMkuPageApiDTO.Response> rpcData = rpcResponse.getData();
            PageInfo<CountryMkuPageVO.Response> pageInfoRes = CountryMkuRpcConvert.INSTANCE.pageApiResponse2PageVoRes(rpcData);
            return pageInfoRes;
        } catch (Exception e) {
            log.error("CountryMkuRpcServiceImpl.pageSearch rpc exception. input={}", JSONObject.toJSONString(input), e);
            return null;
        }finally {
            log.info("CountryMkuRpcServiceImpl.pageSearch rpc end. input={}, rpcResponse={}", JSONObject.toJSONString(input), JSONObject.toJSONString(rpcResponse));
        }
    }

    @Override
    public List<CountryMkuPageVO.Request> auditStatusNum(CountryMkuPageVO.Request input){
        log.info("CountryMkuRpcServiceImpl.auditStatusNum, input={}", JSONObject.toJSONString(input));

        DataResponse<List<CountryMkuPageApiDTO.Request>> rpcResponse = null;
        try {
            CountryMkuPageApiDTO.Request param = CountryMkuRpcConvert.INSTANCE.pageVoReq2PageApiReq(input);
            param.setPin(LoginContext.getLoginContext().getPin());
            param.setLang(LangContextHolder.get());
            param.setSystemCode(Constant.SYSTEM_CODE);
            rpcResponse = iscCountryMkuReadApiService.auditStatusNum(param);
            if (!rpcResponse.getSuccess()){
                log.warn("auditStatusNum, rpcResponse fail. input={}", JSONObject.toJSONString(input));
                return null;
            }

            List<CountryMkuPageApiDTO.Request> rpcData = rpcResponse.getData();
            List<CountryMkuPageVO.Request> results = CountryMkuRpcConvert.INSTANCE.listDto2Vo(rpcData);
            return results;
        } catch (Exception e) {
            log.error("CountryMkuRpcServiceImpl.auditStatusNum rpc exception. input={}", JSONObject.toJSONString(input), e);
            return null;
        }finally {
            log.info("CountryMkuRpcServiceImpl.auditStatusNum rpc end. input={}, rpcResponse={}", JSONObject.toJSONString(input), JSONObject.toJSONString(rpcResponse));
        }
    }

    @Override
    public DataResponse<String> batchBlack(CountryMkuApproveVO input){
        log.info("CountryMkuRpcServiceImpl.batchBlack, input={}", JSONObject.toJSONString(input));
        try {
            CountryMkuApproveDTO param = CountryMkuRpcConvert.INSTANCE.approveVo2Dto(input);
            param.setPin(LoginContext.getLoginContext().getPin());
            param.setLang(LangContextHolder.get());
            param.setSystemCode(Constant.SYSTEM_CODE);
            return iscCountryMkuWriteApiService.batchBlack(param);
        } catch (Exception e) {
            log.error("CountryMkuRpcServiceImpl.batchBlack rpc exception. input={}", JSONObject.toJSONString(input), e);
        }
        return null;
    }

    @Override
    public DataResponse<String> batchOutBlack(CountryMkuApproveVO input){
        log.info("CountryMkuRpcServiceImpl.batchOutBlack,input={}", JSONObject.toJSONString(input));
        try {
            CountryMkuApproveDTO param = CountryMkuRpcConvert.INSTANCE.approveVo2Dto(input);
            param.setPin(LoginContext.getLoginContext().getPin());
            param.setLang(LangContextHolder.get());
            param.setSystemCode(Constant.SYSTEM_CODE);
            return iscCountryMkuWriteApiService.batchOutBlack(param);
        } catch (Exception e) {
            log.error("CountryMkuRpcServiceImpl.batchOutBlack rpc exception. input={}", JSONObject.toJSONString(input), e);
        }
        return null;
    }

    @Override
    public DataResponse<String> batchPool(CountryMkuApproveVO input){
        log.info("CountryMkuRpcServiceImpl.batchPool, input={}", JSONObject.toJSONString(input));
        try {
            CountryMkuApproveDTO param = CountryMkuRpcConvert.INSTANCE.approveVo2Dto(input);
            param.setPin(LoginContext.getLoginContext().getPin());
            param.setLang(LangContextHolder.get());
            param.setSystemCode(Constant.SYSTEM_CODE);
            return iscCountryMkuWriteApiService.batchPool(param);
        } catch (Exception e) {
            log.error("CountryMkuRpcServiceImpl.batchPool rpc exception. input={}", JSONObject.toJSONString(input), e);
        }
        return null;
    }

    /**
     * 检查黑名单数据。
     * @param input 请求参数，包含需要检查的国家和MKU信息。
     * @return 返回检查结果，包括是否在黑名单中。
     */
    @Override
    public DataResponse<CountryMkuCheckVO> checkBlackData(CountryMkuCheckReqVO input) {
        log.info("CountryMkuRpcServiceImpl.checkBlackData, input={}", JSONObject.toJSONString(input));
        DataResponse<CountryMkuCheckDTO> response = null;
        try {
            CountryMkuCheckReqDTO countryMkuCheckReqDTO = CountryMkuRpcConvert.INSTANCE.reqVo2Dto(input);
            response = iscCountryMkuReadApiService.checkBlackData(countryMkuCheckReqDTO);
            CountryMkuCheckDTO dto = response.getData();
            CountryMkuCheckVO countryMkuCheckVO = CountryMkuRpcConvert.INSTANCE.dto2Vo(dto);
            return DataResponse.success(countryMkuCheckVO);
        } catch (Exception e) {
            log.error("CountryMkuRpcServiceImpl.checkBlackData rpc exception. input={}", JSONObject.toJSONString(input), e);
        }finally {
            log.info("CountryMkuRpcServiceImpl.checkOutBlackData req:{},res:{},", JSON.toJSONString(input), JSON.toJSONString(response));
        }
        return DataResponse.success();
    }

    /**
     * 检查出黑数据。
     * @param input 国际化商品黑名单检查请求对象。
     * @return 国际化商品黑名单检查结果。
     */
    @Override
    public DataResponse<CountryMkuCheckVO> checkOutBlackData(CountryMkuCheckReqVO input) {
        log.info("CountryMkuRpcServiceImpl.checkOutBlackData, input={}", JSONObject.toJSONString(input));
        DataResponse<CountryMkuCheckDTO> response = null;
        try {
            CountryMkuCheckReqDTO countryMkuCheckReqDTO = CountryMkuRpcConvert.INSTANCE.reqVo2Dto(input);
            response = iscCountryMkuReadApiService.checkOutBlackData(countryMkuCheckReqDTO);
            CountryMkuCheckDTO dto = response.getData();
            CountryMkuCheckVO countryMkuCheckVO = CountryMkuRpcConvert.INSTANCE.dto2Vo(dto);
            return DataResponse.success(countryMkuCheckVO);
        } catch (Exception e) {
            log.error("CountryMkuRpcServiceImpl.checkOutBlackData rpc exception. input={}", JSONObject.toJSONString(input), e);
        }finally {
            log.info("CountryMkuRpcServiceImpl.checkOutBlackData req:{},res:{},", JSON.toJSONString(input), JSON.toJSONString(response));
        }
        return DataResponse.success();
    }

    /**
     * 刷新国家MKU池
     * @param vo 包含要刷新的MKU ID列表和目标国家代码的对象
     * @return DataResponse对象，包含了操作结果
     */
    @Override
    public DataResponse<Boolean> refreshCountryMku(CountryMkuRefreshVO vo) {
        DataResponse<Boolean> dataResponse = null;
        try {
            CountryMkuReqDTO dto = new CountryMkuReqDTO();
            dto.setMkuIdList(vo.getMkuIdList());
            dto.setCountryCode(vo.getTargetCountryCode());
            dataResponse = iscCountryMkuWriteApiService.mkuPoolJoinCountryPool(dto);
            return dataResponse;
        } catch (Exception e) {
            log.error("CountryAgreementPriceRpcServiceImpl.refreshCountryMku 异常 reqDTO={}", JSONObject.toJSONString(vo),e);
        }
        return dataResponse;
    }

    /**
     * 更新国家MKU状态。
     * @param vo 包含要更新的MKU信息的VO对象。
     * @return 更新结果。
     */
    public DataResponse<String> updateMkuStatus(CountryMkuUpdateStatusVO vo){
        DataResponse<String> response = null;
        try {
            CountryMkuUpdateStatusDTO dto = new CountryMkuUpdateStatusDTO();
            dto.setIdList(vo.getIdList());
            dto.setCountryMkuStatus(vo.getCountryMkuStatus());
            dto.setDownReason(vo.getDownReason());
            dto.setPin(LoginContext.getLoginContext().getPin());
            dto.setLang(LangContextHolder.get());
            dto.setSystemCode(Constant.SYSTEM_CODE);
            response = iscCountryMkuWriteApiService.updateMkuStatus(dto);
            return response;
        } catch (Exception e) {
            log.error("CountryAgreementPriceRpcServiceImpl.updateMkuStatus 异常 reqDTO={}", JSONObject.toJSONString(vo),e);
        }finally {
            log.info("CountryMkuRpcServiceImpl.updateMkuStatus req:{},res:{},", JSON.toJSONString(vo), JSON.toJSONString(response));
        }
        return response;
    }

    @Override
    public DataResponse<List<SpuLangApiDTO>> getMkuSpuTitleLang(Long mkuId, String targetCountryCode) {
        log.info("CountryMkuRpcServiceImpl.getMkuSpuTitleLang mkuId:{}, targetCountryCode:{}", mkuId, targetCountryCode);
        
        DataResponse<List<SpuLangApiDTO>> response = null;
        try {
            response = iscCountryMkuReadApiService.getMkuSpuTitleLang(mkuId, targetCountryCode);
            
            if (!response.getSuccess() || response.getData() == null) {
                log.warn("CountryMkuRpcServiceImpl.getMkuSpuTitleLang failed, response:{}", JSONObject.toJSONString(response));
                return DataResponse.error("获取MKU/SPU多语言标题失败");
            }
            
            List<SpuLangApiDTO> spuLangList = response.getData();

            log.info("CountryMkuRpcServiceImpl.getMkuSpuTitleLang success, mkuId:{}, targetCountryCode:{}, result:{}", mkuId, targetCountryCode, JSONObject.toJSONString(spuLangList));
            return DataResponse.success(spuLangList);
            
        } catch (Exception e) {
            log.error("CountryMkuRpcServiceImpl.getMkuSpuTitleLang exception, mkuId:{}, targetCountryCode:{}", mkuId, targetCountryCode, e);
            return DataResponse.error("获取MKU/SPU多语言标题异常: " + e.getMessage());
        } finally {
            log.info("CountryMkuRpcServiceImpl.getMkuSpuTitleLang req: mkuId:{}, targetCountryCode:{}, res:{}", mkuId, targetCountryCode, JSONObject.toJSONString(response));
        }
    }

    @Override
    public DataResponse<Boolean> updateMkuSpuTitleLangAndApprovalTranslation(CountryMkuTranslationVO.ImportRequest request) {
        log.info("CountryMkuRpcServiceImpl.updateMkuSpuTitleLangAndApprovalTranslation request:{}", JSONObject.toJSONString(request));
        
        DataResponse<Boolean> response = null;
        try {
            response = iscCountryMkuWriteApiService.updateMkuSpuTitleLangAndApprovalTranslation(
                request.getMkuId(), 
                request.getCountryCode(), 
                request.getLangNameMap()
            );
            
            log.info("CountryMkuRpcServiceImpl.updateMkuSpuTitleLangAndApprovalTranslation success, request:{}, result:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(response));
            return response;
            
        } catch (Exception e) {
            log.error("CountryMkuRpcServiceImpl.updateMkuSpuTitleLangAndApprovalTranslation exception, request:{}", JSONObject.toJSONString(request), e);
            return DataResponse.error("更新MKU/SPU多语言标题异常: " + e.getMessage());
        } finally {
            log.info("CountryMkuRpcServiceImpl.updateMkuSpuTitleLangAndApprovalTranslation req:{}, res:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(response));
        }
    }
}
