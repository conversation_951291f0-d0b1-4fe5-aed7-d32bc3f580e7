package com.jdi.isc.product.center.rpc.customs.impl;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.center.rpc.customs.IscProductCustomsReadRpcService;
import com.jdi.isc.product.soa.api.customs.IscProductCustomsReadApiService;
import com.jdi.isc.product.soa.api.customs.req.ProductCustomsApiDTO;
import com.jdi.isc.product.soa.api.customs.req.ProductCustomsReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class IscProductCustomsReadRpcServiceImpl implements IscProductCustomsReadRpcService {

    @Resource
    private IscProductCustomsReadApiService iscProductCustomsReadApiService;

    @ToolKit
    @Override
    public DataResponse<List<ProductCustomsApiDTO>> queryList(ProductCustomsReqApiDTO reqApiDTO) {
        return iscProductCustomsReadApiService.queryList(reqApiDTO);
    }
}
