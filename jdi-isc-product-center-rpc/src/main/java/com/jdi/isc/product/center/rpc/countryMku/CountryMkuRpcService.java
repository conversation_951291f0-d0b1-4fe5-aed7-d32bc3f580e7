package com.jdi.isc.product.center.rpc.countryMku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.domain.agreementPrice.biz.PriceRefreshVO;
import com.jdi.isc.product.center.domain.countryMku.*;
import com.jdi.isc.product.soa.api.spu.req.SpuLangApiDTO;

import java.util.List;

/**
 * @Description: 商品国家池表 rpc接口
 * @Author: wangpeng965
 * @Date: 2024/12/02 21:49
 **/

public interface CountryMkuRpcService {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    PageInfo<CountryMkuPageVO.Response> pageSearch(CountryMkuPageVO.Request input);

    /**
     * 获取审核状态数量。
     * @return 审核状态数量列表。
     */
    List<CountryMkuPageVO.Request> auditStatusNum(CountryMkuPageVO.Request input);

    /**
     * 批量将指定国家的商品添加到黑名单中。
     * @param input 包含要操作的国家信息和商品信息的对象。
     * @return 操作结果的描述信息。
     */
    DataResponse<String> batchBlack(CountryMkuApproveVO input);

    /**
     * 批量将指定国家的商品从黑名单中移除
     * @param input 包含要移除的国家信息和商品信息的对象
     * @return 执行结果的描述信息
     */
    DataResponse<String> batchOutBlack(CountryMkuApproveVO input);

    /**
     * 批量处理国家MKU审批请求。
     * @param input 包含待处理的国家MKU审批信息。
     * @return 处理结果。
     */
    DataResponse<String> batchPool(CountryMkuApproveVO input);

    /**
     * 检查黑名单数据
     * @param input 国别MKU检查请求信息
     * @return 包含国别MKU检查结果的响应数据
     */
    DataResponse<CountryMkuCheckVO> checkBlackData(CountryMkuCheckReqVO input);

    /**
     * 检查黑名单数据
     * @param input 国别、地区、产品等信息
     * @return 包含黑名单检查结果的 CountryMkuCheckVO 对象
     */
    DataResponse<CountryMkuCheckVO> checkOutBlackData(CountryMkuCheckReqVO input);

    /**
     * 刷新国家MKU信息。
     * @param vo 包含需要刷新的国家MKU信息的对象。
     * @return 刷新操作是否成功。
     */
    DataResponse<Boolean> refreshCountryMku(CountryMkuRefreshVO vo);


    /**
     * 更新MKU状态。
     * @param vo 包含国家和MKU状态的更新信息。
     * @return 更新操作的结果。
     */
    DataResponse<String> updateMkuStatus(CountryMkuUpdateStatusVO vo);

    /**
     * 获取MKU和SPU的多语言标题
     * @param mkuId MKU ID
     * @param targetCountryCode 目标国家代码
     * @return MKU/SPU多语言标题信息
     */
    DataResponse<List<SpuLangApiDTO>> getMkuSpuTitleLang(Long mkuId, String targetCountryCode);

    /**
     * 更新MKU和SPU的多语言标题并确认翻译
     * @param request 更新请求
     * @return 更新结果
     */
    DataResponse<Boolean> updateMkuSpuTitleLangAndApprovalTranslation(CountryMkuTranslationVO.ImportRequest request);
}
