package com.jdi.isc.task.center.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/12/29 10:53
 */
@Slf4j
public class DateUtil {
    
    public static final String DEFAULT_DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final String DEFAULT_DAY_PATTERN = "yyyy-MM-dd";

    public static final String YY_MM_DD_DAY_PATTERN = "yyyyMMdd";

    public static final String DEFAULT_DAY_PATTERN_THAI = "MM/dd/yyyy";

    public static final String DEFAULT_CHINESE_DATE_PATTERN = "yyyy年MM月dd日 HH:mm";

    public static final String DEFAULT_CHINESE_DATE_PATTERN1 = "yyyy年MM月dd日";

    public static final String DEFAULT_DAY_PATTERN_VN = "dd-MM-yyyy";

    private DateUtil() {
        throw new UnsupportedOperationException();
    }

    public static String formatDate(Date date) {
        return formatDate(date, DEFAULT_DATE_PATTERN);
    }

    public static String formatDay(Date date) {
        return formatDate(date, DEFAULT_DAY_PATTERN);
    }

    public static String formatDate(Date date, String pattern) {
        if (date == null) {
            throw new IllegalArgumentException("date is null");
        }

        if (pattern == null) {
            throw new IllegalArgumentException("pattern is null");
        }

        SimpleDateFormat formatter = new SimpleDateFormat(pattern, Locale.CHINA);
        return formatter.format(date);
    }
    public static String getBeforeDay() {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, -1);
        Date start = c.getTime();
        String beforeDay = formatDay(start);//前一天
        return beforeDay;
    }

    /**
     * 获得账期(年份和月份 例如 201510)
     * @return
     */
    public static String getAccountPeriod(){
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH, -1);
        String accountPeriod = formatDate(c.getTime(), "yyyyMM");
        return accountPeriod;
    }
    /**
     * 将当前时间转换为账期格式(年份和月份 例如 )
     * @return
     */
    public static String getCurrentAccountPeriod(){
        Calendar c = Calendar.getInstance();
        String accountPeriod = formatDate(c.getTime(), "yyyyMM");
        return accountPeriod;
    }
    

    /**
     * 将时间格式的字符串 比如 2015年4月8日16:17:07或 2014-04-06 16:17:07转换为 日期格式的时间
     * @param strDate
     * @return
     */
    public static Date stringToDate(String strDate) {
        Date date=null;
        SimpleDateFormat formatter=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            date=formatter.parse(strDate);
        } catch (ParseException e) {
            log.error("【系统异常】字符串转换为时间出现问题:err:{}",e.getMessage(),e);
        }
        return date;
    }

    /**
     * 字符串转化为时间
     * @param dateStr
     * @param pattern
     * @return
     */
    public static Date stringToDate(String dateStr, String pattern){
        Date date = null;
        try {
            SimpleDateFormat format = new SimpleDateFormat(pattern);
            date = format.parse(dateStr);
        } catch (ParseException e) {
            log.error("【系统异常】时间转化异常! dateStr = " + dateStr + ", pattern = " + pattern);
        }
        return date;
    }


    /**
     * 获取当前日期与时间
     */
    public static String getCurrentDatetime() {
        // 2015-02-12 14:00  zhouen  修改原SimpleDateFormat作为静态常量方式实现getCurrentDatetime()的方式。
        //                           此修改主要目的在于规避多线程安全性模式（即可能使得SimpleDateFormat作为静态常量方式被滥用）风险。
        //                           可以使用FastDateFormat或者joda来做更好。
        return formatDate(new Date());
    }

    /**
     * 获得当前时间戳
     * @return
     */
    public static Long getCurrentTime(){
        Date date = new Date();
        return date.getTime();
    }

    /**
     * 比较时间 d1 > d2 ?
     * @param d1
     * @param d2
     * @return
     */
    public static boolean compareDate(Date d1, Date d2) {
        return (d1 != null && d2 != null && d1.getTime() > d2.getTime());
    }

    /**
     * 比较时间 d1 >= d2 ?
     * @param d1
     * @param d2
     * @return
     */
    public static boolean compareDateEq(Date d1, Date d2) {
        return (d1 != null && d2 != null && d1.getTime() >= d2.getTime());
    }

    public static long getDayBetween(Date beginDate,Date endDate, boolean startZero){
        return offDay(beginDate, endDate, 1, true) + (startZero ? 0 : 1);
    }

    /**
     * beginDate距endDate多少天
     *       2015-01-01 12:12:12 距离 2015-01-10 12:12:12   9天(1 true|false)  9天(2 false)  9天(2 true)   9天(3 fasle)  10天(3 true)
     *       2015-01-01 12:12:12 距离 2015-01-10 12:12:13   9天(1 true|false)  9天(2 false)  10天(2 true)  9天(3 fasle)  10天(3 true)
     *       2015-01-01 12:12:12 距离 2015-01-10 12:12:11   9天(1 true|false)  8天(2 false)  9天(2 true)   9天(3 fasle)  10天(3 true)
     * @param beginDate 开始时间
     * @param endDate   截止日期
     * @param calType   计算格式类型
     *      1. 只计算天（yyyy-MM-dd）的差值（默认）
     *      2. 时间按照（yyyy-MM-dd hh:mm:ss）计算时间
     *      3. 起始时间按照（yyyy-MM-dd）截止日期按照（yyyy-MM-dd hh:mm:ss）计算时间
     * @param isOneDay 不足一天是否设置成一天
     *     false:不足一天舍弃 true: 不足一天设置成一天
     * @return
     */
    public static long offDay(Date beginDate,Date endDate,int calType,boolean isOneDay){
        //这里对时间进行了比对，使用时应注意
        if (endDate == null || beginDate == null ) {//|| beginDate.getTime() >= endDate.getTime()
            return 0;
        }
        Calendar c1 = Calendar.getInstance();
        c1.setTime(beginDate);
        Calendar c2 = Calendar.getInstance();
        c2.setTime(endDate);
        switch(calType){
            case 3:
                c1.set(c1.get(Calendar.YEAR), c1.get(Calendar.MONTH), c1.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
                c2.set(c2.get(Calendar.YEAR), c2.get(Calendar.MONTH), c2.get(Calendar.DATE), c2.get(Calendar.HOUR_OF_DAY), c2.get(Calendar.MINUTE), c2.get(Calendar.SECOND));
                break;
            case 2:
                c1.set(c1.get(Calendar.YEAR), c1.get(Calendar.MONTH), c1.get(Calendar.DATE), c1.get(Calendar.HOUR_OF_DAY), c1.get(Calendar.MINUTE), c1.get(Calendar.SECOND));
                c2.set(c2.get(Calendar.YEAR), c2.get(Calendar.MONTH), c2.get(Calendar.DATE), c2.get(Calendar.HOUR_OF_DAY), c2.get(Calendar.MINUTE), c2.get(Calendar.SECOND));
                break;
            case 1: //统一按照yyyy-MM-dd 格式进行时间差计算
            default:
                c1.set(c1.get(Calendar.YEAR), c1.get(Calendar.MONTH), c1.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
                c2.set(c2.get(Calendar.YEAR), c2.get(Calendar.MONTH), c2.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
                break;
        }
        long offTime = c2.getTime().getTime()/1000 - c1.getTime().getTime()/1000;
        return (offTime / (60 * 60 * 24)) + (isOneDay?((offTime % (60 * 60 * 24)>0)?1:0):0);
    }

    /**
     *  获取两日期相差月份
     * 规则：不到一个月的按照一个月处理
     * 2015-01-01 ~ 2015-05-02  = 5 个月(true)  4个月（false）
     * 2015-01-02 ~ 2015-05-02  = 5 个月(true)  4个月（false）
     * 2015-01-02 ~ 2015-05-01  = 4 个月(true)  4个月（false）
     * @param beginDate  起始日期
     * @param endDate    截止日期
     * @param isOneMonth 不足一个月设置为一个月 （true：是 false：否）
     * @return
     * @throws ParseException
     */
    public static long getMonthSpace(Date beginDate, Date endDate,boolean isOneMonth){
        if (beginDate == null || endDate == null
                || beginDate.getTime() >= endDate.getTime()) {
            log.warn("比较时间失败，参数不合法");
            return 0;
        }
        long result = 0;
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(beginDate);
        c2.setTime(endDate);

        result = (c2.get(Calendar.YEAR) - c1.get(Calendar.YEAR)) * 12
                + (c2.get(Calendar.MONTH) - c1.get(Calendar.MONTH))
                + (isOneMonth?(c2.get(Calendar.DATE) > c1.get(Calendar.DATE) ? 1 : 0):0);

        log.warn("比较时间信息，beginDate:{}，endDate:{}，monthSpace:{}",new Object[]{beginDate,endDate,result});
        return result == 0 ? 1 : Math.abs(result);
    }

    /**
     *  获取两日期相差年份
     *     规则:年粒度大 不足一年 舍去 通过月份获取
     * 2015-01-01 ~ 2016-01-02  = 1年
     * 2015-01-02 ~ 2016-05-02  = 1年
     * 2015-01-02 ~ 2016-01-01  = 1年
     * 2015-01-02 ~ 2015-07-01  = 0年
     * @param beginDate  起始日期
     * @param endDate    截止日期
     * @return
     * @throws ParseException
     */
    public static long getYearSpace(Date beginDate, Date endDate){
        if (beginDate == null || endDate == null
                || beginDate.getTime() >= endDate.getTime()) {
            log.warn("比较时间失败，参数不合法");
            return 0;
        }
        long result = 0;
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(beginDate);
        c2.setTime(endDate);

        result =  Math.abs(c2.get(Calendar.YEAR) - c1.get(Calendar.YEAR));

        log.warn("比较时间信息，beginDate:{}，endDate:{}，yearSpace:{}",new Object[]{beginDate,endDate,result});
        return result;
    }


    /**
     * d1的零点 00:00:00
     * @param d1
     * @return
     */
    public static Date getBeginTimeByDay(Date d1){
        Calendar c = Calendar.getInstance();
        c.setTime(stringToDate(formatDay(d1) + " 00:00:00", "yyyy-MM-dd HH:mm:ss"));
        return c.getTime();
    }

    /**
     * 获取某个时间对应的unix时间戳.
     * @param date
     * @return
     */
    public static long getTimeUnixFormat(Date date) {

        return date.getTime()/1000;
    }

    /**
     * d1的23:59:59
     * @param d1
     * @return
     */
    public static Date getEndTimeByDay(Date d1){
        Calendar c = Calendar.getInstance();
        c.setTime(stringToDate(formatDay(d1) + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));
        return c.getTime();
    }

    /**
     * 前xx月，含当前月
     *        beforeCount = 12  now 2015-3
     *        [2015-3, 2015-2, 2015-1, 2014-12, 2014-11, 2014-10, 2014-9, 2014-8, 2014-7, 2014-6, 2014-5, 2014-4]
     * @param beforeCount
     * @return
     */
    public static List<String> getYearMonthListBefore(int beforeCount){
        List<String> list = new ArrayList<>(beforeCount);
        Calendar c1 = Calendar.getInstance();
        c1.setTime(new Date());
        int tempYear = c1.get(Calendar.YEAR);
        int tempMonth = c1.get(Calendar.MONTH) + 1;
        for(int i = 0; i < beforeCount; i++){
            if(tempMonth < 10){
                list.add(tempYear + "-0" + tempMonth);
            }else{
                list.add(tempYear + "-" + tempMonth);
            }
            tempMonth--;
            if(tempMonth == 0){
                tempYear--;
                tempMonth = 12;
            }
        }
        return list;
    }

    /**
     * 获得 yyyy-MM 的月初 时间
     *         yearMonth 2015-3 -->  2015-03-01 00:00:00
     * @param yearMonth
     * @return
     */
    public static Date getBeginTimeOfMonth(String yearMonth){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return format.parse(yearMonth + "-01 00:00:00");
        } catch (ParseException e) {
            log.error("获得 yyyy-MM 的月初 时间异常！yearMonth = " + yearMonth, e);
        }

        return new Date();
    }

    /**
     * 获得 yyyy-MM 的月末 时间
     *         yearMonth 2015-3 -->  2015-03-31 23:59:59
     * @param yearMonth
     * @return
     */
    public static Date getEndTimeOfMonth(String yearMonth){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(format.parse(yearMonth + "-01 23:59:59"));
            calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
            calendar.set(Calendar.DAY_OF_MONTH, 0);
        } catch (ParseException e) {
            log.error("获得 yyyy-MM 的月末 时间异常！yearMonth = " + yearMonth, e);
        }

        return calendar.getTime();
    }

    /**
     * 在原日期的基础上增加
     *
     * @param date
     * @param filed
     * @param count
     * @return Date
     */
    public static Date addDatefield(Date date, int filed, int count) {
        if (filed != Calendar.YEAR && filed != Calendar.MONTH
                && filed != Calendar.DATE && filed != Calendar.HOUR
                && filed != Calendar.MINUTE && filed != Calendar.SECOND) {
            throw new IllegalStateException("不能处理的时间类型");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(filed, count);
        return c.getTime();
    }

    /**
     * 在原日期的增加天
     *
     * @param date
     * @param count
     * @return Date
     */
    public static Date addDay(Date date, int count) {
        return addDatefield(date, Calendar.DAY_OF_MONTH, count);
    }

    /**
     * 在原日期的增加月
     *
     * @param date
     * @param count
     * @return Date
     */
    public static Date addMonth(Date date, int count) {
        return addDatefield(date, Calendar.MONTH, count);
    }

    /**
     * 在原日期的增加年
     *
     * @param date
     * @param count
     * @return Date
     */
    public static Date addYear(Date date, int count) {
        return addDatefield(date, Calendar.YEAR, count);
    }

    /**
     * 得到某一年周的总数
     *
     * @param year
     * @return
     */
    public static int getMaxWeekNumOfYear(int year) {
        Calendar c = new GregorianCalendar();
        c.set(year, Calendar.DECEMBER, 31, 23, 59, 59);

        return getWeekOfYear(c.getTime());
    }
    /**
     * 取得当前日期是多少周
     *
     * @param date
     * @return
     */
    public static int getWeekOfYear(Date date) {
        Calendar c = new GregorianCalendar();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.setMinimalDaysInFirstWeek(7);
        c.setTime (date);

        return c.get(Calendar.WEEK_OF_YEAR);
    }

    /**
     * 得到某年某周的第一天
     *
     * @param year
     * @param week
     * @return
     */
    public static Date getFirstDayOfWeek(int year, int week) {
        Calendar c = new GregorianCalendar();
        c.set(Calendar.YEAR, year);
        c.set (Calendar.MONTH, Calendar.JANUARY);
        c.set(Calendar.DATE, 1);

        Calendar cal = (GregorianCalendar) c.clone();
        cal.add(Calendar.DATE, week * 7);

        return getFirstDayOfWeek(cal.getTime());
    }


    /**
     * 取得指定日期所在周的第一天
     *
     * @param date
     * @return
     */
    public static Date getFirstDayOfWeek(Date date) {
        Calendar c = new GregorianCalendar();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.setTime(date);
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek()); // Monday
        return c.getTime ();
    }
    /**
     * 取得指定日期所在周的最后一天
     *
     * @param date
     * @return
     */
    public static Date getLastDayOfWeek(Date date) {
        Calendar c = new GregorianCalendar();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.setTime(date);
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek() + 6); // Sunday
        return c.getTime();
    }

    /**
     * 取得指定日期所在周的第一天
     *
     * @param date
     * @return
     */
    public static Date getFirstDayOfMonth(Date date) {
        Calendar c = new GregorianCalendar();
        c.setTime(date);
        c.set(c.get(Calendar.YEAR), c.get(Calendar.MONTH), 1, 0, 0, 0);
        return c.getTime();
    }

    /**
     * 根据传入的日期，向前推 或向后退多少天
     * @param date
     * @param days
     * @return
     */
    public static Date addDayOrDeleteDay(Date date, Integer days){
        Date dNow = date;   //当前时间
        Date dBefore = new Date();

        Calendar calendar = Calendar.getInstance(); //得到日历
        calendar.setTime(dNow);//把当前时间赋给日历
        calendar.add(Calendar.DAY_OF_MONTH, days);  //设置为前一天
        dBefore = calendar.getTime();   //得到前一天的时间
        return dBefore;
    }

    // 得到几天前的时间
    public static Date getDateBefore(Date d,int day){
        Calendar now = Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.DATE, now.get(Calendar.DATE) - day);
        return now.getTime();
    }

    // 得到几天后的时间
    public static Date getDateAfter(Date d,int day){
        Calendar now = Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.DATE, now.get(Calendar.DATE) + day);
        return now.getTime();
    }

    /**
     * 获得某天的零点 2014-08-14 00:00:00
     * @return
     */
    public static Date getDayStart(Date date){
        Calendar now = Calendar.getInstance();
        now.setTime(date);
        now.set(now.get(Calendar.YEAR), now.get(Calendar.MONTH), now.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        now.set(Calendar.MILLISECOND, 0);
        return now.getTime();
    }

    /**
     * 获得某天的 2014-08-14 23:59:59
     * @param date
     * @return
     */
    public static Date getDayEnd(Date date){
        Calendar now = Calendar.getInstance();
        now.setTime(date);
        now.set(now.get(Calendar.YEAR), now.get(Calendar.MONTH), now.get(Calendar.DAY_OF_MONTH), 23, 59, 59);
        return now.getTime();
    }

    /**
     * 获取上一个月Date
     *
     * @return 上一个月Date
     */
    public static Date getLastMonth() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, -1);
        return cal.getTime();
    }

    /**
     * 获取当月的天数
     *
     * @param date
     * @return 当月的天数
     */
    public static int getLastDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取两个时间相差的分钟数
     *
     * @param smallDate
     * @param bigDate
     * @return
     */
    public static long getDayBetweenMinute(Date smallDate, Date bigDate) {
        if (smallDate == null || bigDate == null) {
            return -1;
        }
        long diff = ((bigDate.getTime() - smallDate.getTime())/1000)/60;
        return diff;
    }

    /**
     * 获取两个时间相差的秒数
     *
     * @param smallDate
     * @param bigDate
     * @return
     */
    public static long getDayBetweenSecond(Date smallDate, Date bigDate) {
        if (smallDate == null || bigDate == null) {
            return -1;
        }
        long diff = (bigDate.getTime() - smallDate.getTime())/1000;
        return diff;
    }

    /**
     * 秒转date
     * @param second
     * @return
     */
    public static Date second2Date(long second){
        return new Date(second * 1000);
    }

    public static Date long2date(Long time){
        return time != null ? new Date(time) : null;
    }

    /**
     * 是否式相同的一周
     * @param date1
     * @param date2
     * @return
     */
    public static boolean isSameWeek(Date date1, Date date2){
        return DateUtil.getBeginTimeByDay(DateUtil.getFirstDayOfWeek(date1)).getTime() == DateUtil.getBeginTimeByDay(DateUtil.getFirstDayOfWeek(date2)).getTime();
    }


    public static String parseTime(Long time,String format){
        if (time == null || format == null || format.isEmpty()) {
            throw new IllegalArgumentException("Time and format must not be null or empty");
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date date = new Date(time);
        return sdf.format(date);
    }


    public static String getTimeStr(){
        // 定义日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        // 获取当前时间的格式化字符串
        String formattedDate = dateFormat.format(new Date());
        return formattedDate;
    }

    /**
     * 根据指定格式获取当前日期的字符串表示
     * @param pattern 日期格式模式字符串，例如"yyyy-MM-dd"
     * @return 格式化后的当前日期字符串
     */
    public static String getDateStrByPattern(String pattern){
        // 定义日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        // 获取当前时间的格式化字符串
        String formattedDate = dateFormat.format(new Date());
        return formattedDate;
    }

    /**
     * 获取前一天的日期字符串
     * @return 返回格式化为"YY-MM-DD"的前一天日期字符串
     */
    public  static String getYesterdayDateStr() {
        DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(YY_MM_DD_DAY_PATTERN);
        return LocalDate.now().minusDays(1).format(DATE_FORMATTER);
    }


    /**
     * 获取指定时间前12小时的时间
     * @param date 指定时间
     * @return 前12小时的时间
     */
    public static Date getBeforeHours(Date date,int beforeHour) {
        if (date == null) {
            throw new IllegalArgumentException("date is null");
        }
        return getBeforeHoursMinutes(date,beforeHour,0);
    }

    /**
     * 获取指定时间前xx小时xx分钟的时间
     * @param date 指定时间
     * @return 前xx小时xx分钟的时间
     */
    public static Date getBeforeHoursMinutes(Date date, int beforeHour, int beforeMin) {
        if (date == null) {
            throw new IllegalArgumentException("date is null");
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, beforeHour);
        calendar.add(Calendar.MINUTE, beforeMin);
        return calendar.getTime();
    }

    public static void main(String[] args) throws InterruptedException {
        /*Date date1 = new Date();
        long time = getTimeUnixFormat(date1);
    	*//*Thread.currentThread().sleep(10000);
    	Date date2 = new Date();*//*
        System.out.println(time);

        Date date2 = DateUtil.stringToDate("2100-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        System.out.println(date2.getTime());
        System.out.println(date2.getTime() - date1.getTime());
        // 2533 7073 6000 000 15位置  9999
        // 2517 6010 8075 945
        // 4102 4160 0000 0  13位置

        System.out.println("------date1------" + DateUtil.getFirstDayOfWeek(date1));
        System.out.println("------date2------" + DateUtil.getFirstDayOfWeek(DateUtil.stringToDate("2021-01-18 18:25:48")));

        System.out.println(getDayBetween(DateUtil.stringToDate("2021-01-25 18:25:48"), new Date(), true));
        System.out.println(getDayBetween(DateUtil.stringToDate("2021-01-25 18:25:48"), new Date(), false));
        System.out.println(getDayBetween(DateUtil.stringToDate("2021-01-24 18:25:48"), new Date(), true));
        System.out.println(getDayBetween(DateUtil.stringToDate("2021-01-24 18:25:48"), new Date(), false));


        System.out.println(DateUtil.formatDate(DateUtil.addDatefield(DateUtil.stringToDate("2021-02-22 11:21:48"), Calendar.SECOND, 86400)));
   */
        String timeStr = DateUtil.getTimeStr();
        System.out.println(timeStr);

        String timeStr1 = DateUtil.getDateStrByPattern(YY_MM_DD_DAY_PATTERN);
        System.out.println("result is:"+ timeStr1);
    }
}
