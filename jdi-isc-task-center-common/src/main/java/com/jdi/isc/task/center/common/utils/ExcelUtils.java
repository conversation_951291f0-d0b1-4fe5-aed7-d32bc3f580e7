package com.jdi.isc.task.center.common.utils;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.jdi.isc.task.center.common.costants.Constant;
import com.jdi.isc.task.center.domain.excel.ExcelPropertyVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Excel工具类
 *
 * <AUTHOR>
 * @date 2024/1/9
 **/
@Slf4j
public class ExcelUtils {

    public final static String FONT_NAME = "微软雅黑";

    public static void validateSuffix() {
    }


    /**
     * 单元格样式
     *
     * @return
     */
    public static WriteCellStyle cellStyle() {
        // 设置样式，这里使用默认样式，但你可以根据需求自定义样式
        WriteCellStyle cellStyle = new WriteCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        cellStyle.setWriteFont(writeFont());
        return cellStyle;
    }

    /**
     * 单元格字体
     *
     * @return
     */
    private static WriteFont writeFont() {
        WriteFont writeFont = new WriteFont();
        writeFont.setFontName(FONT_NAME);
        writeFont.setBold(Boolean.TRUE);
        writeFont.setColor(IndexedColors.BLACK.getIndex());
        return writeFont;
    }


    /**
     * @param wb          HSSFWorkbook对象
     * @param sheet       HSSFSheet 需要验证的界面
     * @param targetSheet targetSheet 储存下拉项的页面名（是名字，字符串）
     * @param validData   下拉项数组
     * @param index       单元格的位置
     */
    public static void setHSSFDataValid(HSSFWorkbook wb, HSSFSheet sheet, String targetSheet, String[] validData, int index, int lastRowIndex) {

        // 创建临时页
        HSSFSheet sheetTemp = wb.createSheet(targetSheet);
        // 设置下拉页页面隐藏
        int sheetIndex = wb.getSheetIndex(targetSheet);
        log.info("ExcelUtils.setHSSFDataValid  targetSheet={} sheetIndex={}",targetSheet,sheetIndex);
        if (sheetIndex < 0) {
            return;
        }
        wb.setSheetHidden(sheetIndex, true);
        // 创建单元格对象
        HSSFCell cell = null;
        // 遍历我们上面的数组，将数据取出来放到新sheet的单元格中
        for (int i = 0, length = validData.length; i < length; i++) {
            // 取出数组中的每个元素
            String dataTemp = validData[i];
            // 根据i创建相应的行对象（说明我们将会把每个元素单独放一行）
            HSSFRow row = sheetTemp.createRow(i);
            // 创建每一行中的第一个单元格
            cell = row.createCell(0);
            // 然后将数组中的元素赋值给这个单元格
            cell.setCellValue(dataTemp);
        }

        // 创建名称，可被其他单元格引用
        Name namedCell = wb.createName();
        namedCell.setNameName(targetSheet);
        // 设置名称引用的公式
        namedCell.setRefersToFormula(targetSheet + "!$A$1:$A$" + validData.length);
        // 加载数据,将名称为hidden的sheet中的数据转换为List形式
        // 设置验证数据内容
        DVConstraint constraint = DVConstraint.createFormulaListConstraint(targetSheet);
        // 设置需要验证的单元格范围（范围：1-60000行的第几列到第几列设置）
        CellRangeAddressList rangeAddressList = new CellRangeAddressList(1, lastRowIndex, index, index);
        // 创建数据验证规则对象
        DataValidation dataValidation = new HSSFDataValidation(rangeAddressList, constraint);
        // 设置验证对象错误提示内容
        dataValidation.createErrorBox("error", "请选择正确的数据");
        // 将工作表添加验证对象
        sheet.addValidationData(dataValidation);
    }


    /**
     * @param wb          XSSFWorkbook
     * @param sheet       XSSFSheet 需要验证的界面
     * @param targetSheet targetSheet 储存下拉项的页面名（是名字，字符串）
     * @param validData   下拉项数组
     * @param index       单元格的位置
     */
    public static void setXSSFDataValid(XSSFWorkbook wb, XSSFSheet sheet, String targetSheet, String[] validData, int index, int lastRowIndex) {

        // 创建临时页
        XSSFSheet sheetTemp = wb.createSheet(targetSheet);
        // 设置下拉页页面隐藏
        int sheetIndex = wb.getSheetIndex(targetSheet);
        log.info("ExcelUtils.setXSSFDataValid  targetSheet={} sheetIndex={}",targetSheet,sheetIndex);
        if (sheetIndex < 0) {
            return;
        }
        wb.setSheetHidden(sheetIndex, true);
        // 创建单元格对象
        XSSFCell cell = null;
        // 遍历我们上面的数组，将数据取出来放到新sheet的单元格中
        for (int i = 0, length = validData.length; i < length; i++) {
            // 取出数组中的每个元素
            String dataTemp = validData[i];
            // 根据i创建相应的行对象（说明我们将会把每个元素单独放一行）
            XSSFRow row = sheetTemp.createRow(i);
            // 创建每一行中的第一个单元格
            cell = row.createCell(0);
            // 然后将数组中的元素赋值给这个单元格
            cell.setCellValue(dataTemp);
        }

        // 设置需要验证的单元格范围（范围：1～lastRowIndex行的第几列到第几列设置）
        CellRangeAddressList rangeAddressList = new CellRangeAddressList(1, lastRowIndex, index, index);
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint formulaListConstraint = helper.createFormulaListConstraint(targetSheet + "!$A$1:$A$" + validData.length);
        DataValidation dataValidation = helper.createValidation(formulaListConstraint, rangeAddressList);
        // 设置验证对象错误提示内容
        dataValidation.setShowErrorBox(Boolean.TRUE);
        dataValidation.createErrorBox("error", "请选择正确的数据");
        // 将工作表添加验证对象
        sheet.addValidationData(dataValidation);


        // 创建名称，可被其他单元格引用
       /* Name namedCell = wb.createName();
        namedCell.setNameName(targetSheet);
        // 设置名称引用的公式
        namedCell.setRefersToFormula(targetSheet + "!$A$1:$A$" + validData.length);
        // 加载数据,将名称为hidden的sheet中的数据转换为List形式
        // 设置验证数据内容
        DVConstraint constraint = DVConstraint.createFormulaListConstraint(targetSheet);
        // 设置需要验证的单元格范围（范围：1-60000行的第几列到第几列设置）
        CellRangeAddressList rangeAddressList = new CellRangeAddressList(1, lastRowIndex, index, index);
        // 创建数据验证规则对象
        DataValidation dataValidation = new HSSFDataValidation(rangeAddressList, constraint);
        // 设置验证对象错误提示内容
        dataValidation.createErrorBox("error", "请选择正确的数据");
        // 将工作表添加验证对象
        sheet.addValidationData(dataValidation);*/
        // --
        //XSSFDataValidationConstraint xssfDataValidationConstraint = new XSSFDataValidationConstraint(DataValidationConstraint.OperatorType.BETWEEN,1,"100");
        //CTDataValidation cTDataValidation = CTDataValidation.Factory.newInstance();
        //XSSFDataValidation xssfDataValidation = new XSSFDataValidation(xssfDataValidationConstraint,rangeAddressList,cTDataValidation);
        //sheet.addValidationData(xssfDataValidation);
    }


    /**
     * @param wb           SXSSFWorkbook对象
     * @param sheet        SXSSFSheet 需要验证的界面
     * @param targetSheet  targetSheet 储存下拉项的页面名（是名字，字符串）
     * @param validData    下拉项数组
     * @param index        单元格的位置
     * @param lastRowIndex 最后一行位置
     */
    public static void setSXSSFDataValid(SXSSFWorkbook wb, SXSSFSheet sheet, String targetSheet, String[] validData, int index, int lastRowIndex) {
        // 创建临时页
        SXSSFSheet sheetTemp = wb.createSheet(targetSheet);
        // 设置下拉页页面隐藏
        int sheetIndex = wb.getSheetIndex(targetSheet);
        log.info("ExcelUtils.setSXSSFDataValid  targetSheet={} sheetIndex={}",targetSheet,sheetIndex);
        if (sheetIndex < 0) {
            return;
        }
        wb.setSheetHidden(sheetIndex, true);
        // 创建单元格对象
        SXSSFCell cell = null;
        // 遍历我们上面的数组，将数据取出来放到新sheet的单元格中
        for (int i = 0, length = validData.length; i < length; i++) {
            // 取出数组中的每个元素
            String dataTemp = validData[i];
            // 根据i创建相应的行对象（说明我们将会把每个元素单独放一行）
            SXSSFRow row = sheetTemp.createRow(i);
            // 创建每一行中的第一个单元格
            cell = row.createCell(0);
            // 然后将数组中的元素赋值给这个单元格
            cell.setCellValue(dataTemp);
        }

        // 设置需要验证的单元格范围（范围：1～lastRowIndex行的第几列到第几列设置）
        CellRangeAddressList rangeAddressList = new CellRangeAddressList(1, lastRowIndex, index, index);
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint formulaListConstraint = helper.createFormulaListConstraint(targetSheet + "!$A$1:$A$" + validData.length);
        DataValidation dataValidation = helper.createValidation(formulaListConstraint, rangeAddressList);
        // 设置验证对象错误提示内容
        dataValidation.setShowErrorBox(Boolean.TRUE);
        dataValidation.createErrorBox("error", "请选择正确的数据");
        // 将工作表添加验证对象
        sheet.addValidationData(dataValidation);
    }

    /**
     * 获取单元格内容
     *
     * @param row
     * @param i
     * @return
     */
    public static String getCellValue(Row row, Integer i) {
        String cellStr = getCellValueStr(row, i);
        if (cellStr == null) {
            return null;
        }
        // 如果[]之间是数字，并且最后一位是]的条件，则认为是取ID，直接截取返回
        int beginIndex = cellStr.lastIndexOf(Constant.LEFT_BRACKET);
        boolean canParse = beginIndex > -1 && cellStr.endsWith(Constant.RIGHT_BRACKET);
        if (canParse) {
            String splitCell = getCellId(cellStr);
            if (StringUtils.isNotBlank(splitCell)) {
                return splitCell;
            }
        }
        return cellStr;
    }

    /**
     * 获取单元格内容，不包含“[”和"]"
     *
     * @param row
     * @param i
     * @return
     */
    public static String getCellValueStr(Row row, Integer i) {
        return getCellValueStr(row, i, null);
    }


    /**
     * 获取单元格内容，不包含“[”和"]"
     *
     * @param row
     * @param i
     * @return
     */
    public static String getCellValueStr(Row row, Integer i, FormulaEvaluator evaluator) {
        if (Objects.isNull(row) || Objects.isNull(i)) {
            return null;
        }
        Cell cell = row.getCell(i);
        if (Objects.isNull(cell)) {
            return null;
        }
        String cellStr = null;
        switch (cell.getCellType()) {
            case STRING:
                cellStr = cell.getStringCellValue();
                break;
            case NUMERIC:
                NumberFormat nf = NumberFormat.getInstance();
                nf.setGroupingUsed(false);
                double acno = cell.getNumericCellValue();
                cellStr = nf.format(acno);
                break;
            case FORMULA:
                if (evaluator != null){
                   cellStr =  getCellValueStrFromFormula(cell, evaluator);
                }else {
                    // 公式单元格区分数字和字符串类型，适配数字单元格未字符串格式
                    CellType cachedFormulaResultType = cell.getCachedFormulaResultType();
                    switch (cachedFormulaResultType) {
                        case STRING:
                            cellStr = cell.getStringCellValue();
                            break;
                        case NUMERIC:
                            cell.setCellType(CellType.STRING);
                            cellStr = cell.getStringCellValue();
                            break;
                        default:
                            cellStr = "";
                            break;
                    }
                }
                break;
            case BLANK:
                cellStr = "";
            case BOOLEAN:
                break;
            default:
                cellStr = "";
                break;
        }
        return cellStr;
    }

    /**
     * 解析带公式的单元格数据
     *
     * @param cell
     * @param evaluator
     * @return
     */
    public static String getCellValueStrFromFormula(Cell cell, FormulaEvaluator evaluator){
        if (Objects.isNull(cell) || Objects.isNull(evaluator)) {
            return null;
        }
        // 评估单元格公式类型
        CellValue cellValue = evaluator.evaluate(cell);
        String cellStr = null;
        switch (cellValue.getCellType()) {
            case STRING:
                cellStr = cellValue.getStringValue();
                break;
            case NUMERIC:
                NumberFormat nf = NumberFormat.getInstance();
                nf.setGroupingUsed(false);
                double acno = cellValue.getNumberValue();
                cellStr = nf.format(acno);
                break;
            case BLANK:
                cellStr = "";
            case BOOLEAN:
                boolean booleanValue = cellValue.getBooleanValue();
                cellStr = String.valueOf(booleanValue);
                break;
            default:
                cellStr = "";
                break;
        }
        return cellStr;
    }

    /**
     * 截取中括号中的数字
     *
     * @param cellStr
     * @return
     */
    public static String getCellId(String cellStr) {
        if (StringUtils.isBlank(cellStr)) {
            return null;
        }
        // 如果[]之间是数字，并且最后一位是]的条件，则认为是取ID，直接截取返回
        int beginIndex = cellStr.lastIndexOf(Constant.LEFT_BRACKET);
        boolean canParse = beginIndex > -1 && cellStr.endsWith(Constant.RIGHT_BRACKET);
        if (canParse) {
            int endIndex = cellStr.lastIndexOf(Constant.RIGHT_BRACKET);
            String idStr = cellStr.substring(beginIndex + 1, endIndex);
            if (NumberUtils.isDigits(idStr)) {
                return idStr;
            }
        }
        return null;
    }


    /**
     * 将列索引转换为列字母（例如，0 转换为 'A'，1 转换为 'B'，27 转换为 'AB'）。
     *
     * @param columnIndex 列索引，从 0 开始。
     * @return 对应的列字母。
     */
    public static String convertIndexToColumnLetter(int columnIndex) {
        StringBuilder columnLetter = new StringBuilder();
        while (columnIndex >= 0) {
            int remainder = columnIndex % 26;
            columnLetter.insert(0, (char) (remainder + 'A'));
            columnIndex = (columnIndex / 26) - 1;
        }
        return columnLetter.toString();
    }

    public static void main(String[] args) {
        String name = "nihao[32343]";

        System.out.println(name.substring(name.lastIndexOf(Constant.LEFT_BRACKET) + 1, name.lastIndexOf(Constant.RIGHT_BRACKET)));

        System.out.println(30500 % 6);

        String image = "https://jdi-intl.s3.cn-north-1.jdcloud-oss.com/mubla1/1712112103979.png";

        String imagePrefix = "<p></p><div class=\"media-wrap image-wrap\">";
        String imageSrc = "<img src=\"%s\"/>";
        String imageSuffix = "</div><p></p>";
        String[] imageArray = image.split(Constant.HTTP);
        StringBuilder desc = new StringBuilder(imagePrefix);
        for (String url : imageArray) {
            if (StringUtils.isNotBlank(url)) {
                desc.append(String.format(imageSrc, Constant.HTTP + url));
            }
        }
        desc.append(imageSuffix);
        System.out.println(desc);

        System.out.println(new BigDecimal("1.450").multiply(new BigDecimal(1000)).setScale(2, RoundingMode.HALF_UP));
        System.out.println("94865102135497140_SPU跨境属性中文zh00".length());
    }

    /**
     * 通过字段名获取index
     */
    public static Integer getIndexByFieldName(Class<?> clazz, String fieldName) {
        try {
            Field field = clazz.getDeclaredField(fieldName);
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            return annotation != null ? annotation.index() : null;
        } catch (NoSuchFieldException e) {
            return null;
        }
    }

    /**
     * 获取所有字段的index映射
     */
    public static Map<Integer,ExcelPropertyVO> getAllFieldIndexMapping(Class<?> clazz) {
        Map<Integer,ExcelPropertyVO > mapping = new LinkedHashMap<>();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (annotation != null) {
                ExcelPropertyVO excelPropertyVO = new ExcelPropertyVO();
                excelPropertyVO.setFieldName(field.getName());
                excelPropertyVO.setIndex(annotation.index());
                excelPropertyVO.setValue(annotation.value().length > 0 ? annotation.value()[0] : field.getName());
                mapping.put(annotation.index(),excelPropertyVO);
            }
        }
        return mapping;
    }
}
