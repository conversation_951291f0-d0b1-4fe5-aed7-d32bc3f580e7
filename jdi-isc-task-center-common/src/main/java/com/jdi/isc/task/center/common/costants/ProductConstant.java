package com.jdi.isc.task.center.common.costants;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/8/31
 **/
public final class ProductConstant {

        public static final String SPU_NAME_PREFIX  = "spuName_%s";

        /**主站skuId*/
        public static final String JD_SKU_ID_PREFIX  = "jdSkuId";

        /**型号*/
        public static final String SPU_SPECIFICATION_PREFIX  = "spuSpecification";

        /** 规格/颜色/尺码 修饰词*/
        public static final String SPU_QUALIFIER_PREFIX  = "spuQualifier_%s";
        /** 关键词*/
        public static final String SPU_KEY_WORD_PREFIX  = "spuKeyWord_%s";
        /**
         * 商详key+语种
         */
        public static final String SPU_DESCRIPTION_PREFIX  = "description_%s";
        /**
         * 跨境属性 文本类型拼接方式：key+属性ID+语言 单选方式： key+属性ID 多选: key+属性ID
         */
        public static final String SPU_GLOBAL_ATTRIBUTE_PREFIX  = "spuGlobal_%s_%s";
        public static final String SKU_GLOBAL_ATTRIBUTE_PREFIX  = "skuGlobal_%s_%s";
        public static final String SPU_EXTEND_ATTRIBUTE_PREFIX  = "spuExtend_%s_%s";


        /**
         * 供应商简码
         */
        public static final String FIX_CATEGORY_ID = "fixCategoryId";
         /**
         * 供应商简码
         */
        public static final String FIX_SUPPLIER_CODE = "fixSupplierCode";

        /**
         * 供应商SPUID
         */
        public static final String FIX_SUPPLIER_SPUID = "fixSupplierSpuId";

        /**
         * jd供应商简码
         */
        public static final String FIX_JD_SUPPLIER_CODE = "fixJdSupplierCode";

        /**
         * 品牌ID
         */
        public static final String FIX_BRAND_ID = "fixBrandId";

        /**
         * 原产地
         */
        public static final String FIX_ORIGIN = "fixOriginCountry";

        /**
         * 发货实效（天）
         */
        public static final String FIX_PRODUCTION_CYCLE = "fixProductionCycle";

        /**
         * 销售单位
         */
        public static final String FIX_SALES_UNIT = "fixSaleUnit";

        /**
         * 毛重（g/unit ）
         */
        public static final String FIX_GROSS_WEIGHT = "fixWeight";

        /**
         * 长度（mm）
         */
        public static final String FIX_LENGTH = "fixLength";

        /**
         * 宽度（mm）
         */
        public static final String FIX_WIDTH = "fixWidth";

        /**
         * 高度（mm）
         */
        public static final String FIX_HEIGHT = "fixHeight";

        /**
         * 条形码
         */
        public static final String FIX_BARCODE = "fixUpcCode";

        /**
         * HSCODE
         */
        public static final String FIX_HSCODE = "fixHsCode";

        /**
         * 币种
         */
        public static final String FIX_CURRENCY = "fixCurrency";

        /**
         * 未税采购价格
         */
        public static final String FIX_UNTAXED_PURCHASE_PRICE = "fixPurchasePrice";
        /**
         * 含税采购价格
         */
        public static final String FIX_TAXED_PURCHASE_PRICE = "fixTaxPurchasePrice";

        /**
         * 未税销售价格
         */
        public static final String FIX_UNTAXED_SALES_PRICE = "fixSalePrice";

        /**
         * 库存
         */
        public static final String FIX_STOCK = "fixStock";

        /**
         * MOQ
         */
        public static final String FIX_MOQ = "fixMoq";

        /**
         * 商品主图链接
         */
        public static final String FIX_MAIN_IMAGE_URL = "fixSpuMainImg";

        /**
         * 商品副图链接 1
         */
        public static final String FIX_SPU_IMAGE_URL_1 = "fixSpuDetailImg1";

        /**
         * 商品副图链接 2
         */
        public static final String FIX_SPU_IMAGE_URL_2 = "fixSpuDetailImg2";

        /**
         * 商品副图链接 3
         */
        public static final String FIX_SPU_IMAGE_URL_3 = "fixSpuDetailImg3";

        /**
         * 商品副图链接 4
         */
        public static final String FIX_SPU_IMAGE_URL_4 = "fixSpuDetailImg4";
        public static final String FIX_SPU_IMAGE_URL_PREFIX = "fixSpuDetailImg";

        /**
         * sku图片
         */
        public static final String FIX_SKU_IMAGE = "fixSkuMainImg";
        /**销售属性 key+销售属性ID */
        public static final String SALE_ATTRIBUTE = "saleAttribute_%s";

                /**销售属性 key+销售属性ID */
        public static final String SALE_ATTRIBUTE_I18N = "saleAttribute";
        public static final String SPU_EXTEND_I18N = "spuExtend";
        public static final String SPU_GLOBAL_I18N = "spuGlobal";
        public static final String SKU_GLOBAL_I18N = "skuGlobal";

        public static final String SPU_NAME_PREFIX_I18N  = "spuName";

        /** 规格/颜色/尺码 修饰词*/
        public static final String SPU_QUALIFIER_PREFIX_I18N  = "spuQualifier";
        /** 关键词*/
        public static final String SPU_KEY_WORD_PREFIX_I18N  = "spuKeyWord";
        /**
         * 商详key+语种
         */
        public static final String SPU_DESCRIPTION_PREFIX_I18N  = "description";
        public static final String RADIO_PREFIX_I18N  = "radio";
        public static final String CHECKBOX_PREFIX_I18N  = "checkbox";
        public static final String TEX_PREFIX_I18N  = "text";


        /**隐藏sheet名称*/
        public static final String HIDDEN_SHEET_NAME = "hiddenSheet";


        // 翻译key ===================
        public static final String VC_SHEET_NAME_FIRST = "sheetFirst";
        public static final String VC_SHEET_NAME_SECOND = "sheetSecond";
        public static final String VC_SHEET_NAME_THIRD = "sheetThird";
        public static final String VC_SHEET_NAME_FORTH = "sheetForth";
        public static final String VC_SHEET_NAME_FIRTH = "sheetFirth";
        public static final String VC_EXCEL_NAME = "excelName";

        public static final String TEMPLATE_FIRST_ROW_COUNTRY_I18N = "firstRowCountry";
        public static final String TEMPLATE_FIRST_ROW_SUPPLIER_I18N = "firstRowSupplier";
        public static final String TEMPLATE_FIRST_ROW_GROUP_TIP_I18N = "firstRowGroupTip";
        public static final String TEMPLATE_ERROR_IMAGE_SAME_I18N = "imageNameEqual";
        public static final String TEMPLATE_ERROR_IMAGE_EXCEPTION_I18N = "imageException";
        public static final String TEMPLATE_ERROR_DATA_NULL_I18N = "dataNull";
        public static final String TEMPLATE_ERROR_NOT_NULL_I18N = "notNull";
        public static final String TEMPLATE_ERROR_SUPPLIER_NO_BRAND_I18N = "supplierNoBrand";
        public static final String TEMPLATE_ERROR_CN_NO_BRAND_I18N = "supplierCnNoBrand";
        public static final String TEMPLATE_ERROR_SUPPLIER_NO_CATEGORY_I18N = "supplierNoCategory";
        public static final String TEMPLATE_ERROR_CN_NO_CATEGORY_I18N = "supplierCnNoCategory";
        public static final String TEMPLATE_ERROR_NOT_NUMBER_I18N = "notNumber";
        public static final String TEMPLATE_ERROR_NOT_ZERO_I18N = "notZero";
        public static final String TEMPLATE_ERROR_SPU_GLOBAL_CHECKBOX_I18N = "spuGlobalCheckbox";


        public static final String TEMPLATE_FIRST_ROW = "所在国家：%s\n" +
            "销售国家：%s\n" +
            "%s";

        public static final String TEMPLATE_FIRST_ROW_SUPPLIER = "供应商简码：%s\n";

        public static  final String BATCH_I18N_MODEL = "batchProduct";
        public static  final String BATCH_I18N_REMARK = "remark";

        public static final String BD =  "BD" ;
        /**
         * 数字key集合
         */
        public static final Set<String> fixColumnNumberSet = Sets.newHashSet(FIX_PRODUCTION_CYCLE,FIX_HSCODE
                ,FIX_STOCK,FIX_MOQ);

        /**
         * 数字不能小于等于0，且是正整数的key集合
         */
        public static final Set<String> fixColumnNumberNotZeroSet = Sets.newHashSet(FIX_GROSS_WEIGHT,FIX_LENGTH,FIX_WIDTH,FIX_HEIGHT);
        /** 其它销售属性 */
        public static final Long OTHER_SALE_ATTRIBUTE_ID = 100100L;
        /**其它销售属性值*/
        public static final Long OTHER_SALE_ATTRIBUTE_VALUE_ID = 103901L;
        /**全部品类*/
        public static final Long CATEGORY_ALL = -100L;
        /**
         * 越南跨境属性增值税率
         */
        public static final Long VN_GLOBAL_VAT_RATE = 68L;
}
