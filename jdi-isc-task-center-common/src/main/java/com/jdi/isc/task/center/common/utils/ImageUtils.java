package com.jdi.isc.task.center.common.utils;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdi.isc.task.center.common.costants.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description：ImageUtils
 * @Date 2025-08-15
 */
@Slf4j
public class ImageUtils {

    /**
     * 装吧 定义提取图片URL和height值的正则表达式，提取的字段用group的()语法
     */
    private static final Pattern patternBbImage = Pattern.compile("background-image:url\\(.*(//.*)\\)");

    private static final String IMG_SYMBOL = "<img";

    /**
     * 解析主站装吧图片链接
     * @param content
     * @return 图片链接列表
     */
    public static List<String> convertZb2ImageList(String content) {
        // 商品详情模板
        String goodsDescTemplate = "https:%s";
        // 研究原串后，先以尺寸进行分组
        String[] split = content.split("px}");
        List<String> imageList = Lists.newArrayList();
        for (String s : split) {
            if (s.contains("background-image:url")) {    // 过滤掉不含背景图片的数据
                Matcher matcher = patternBbImage.matcher(s);   // 指定匹配器
                while (matcher.find()) { // 进行查找，并判断是否匹配
                    log.info("匹配到的字符串：" + matcher.group());
                    log.info("提取的图片地址：" + matcher.group(1));
                    imageList.add(String.format(goodsDescTemplate, matcher.group(1)));
                }
            }
        }
        return imageList;
    }

    /**
     * 解析主站商品非装吧商详内容中图片
     *
     * @param content 自定义商详
     * @return 解析后商详图片列表
     */
    public static List<String> convertJdDesc2ImageList(String content) {
        if(StringUtils.isBlank(content)){
            return new ArrayList<>();
        }
        // 国内商品详情，两种图片url格式，首先解析http:// ，如果不存在，解析//img开头的图片
        List<String> imageList = matchImage(content, "src=\"(http://.*?)\"","jfs");
        if (CollectionUtils.isEmpty(imageList)) {
            imageList = matchImage(content, "src=\"(//img.*?)\"","jfs");
            imageList = imageList.stream().filter(Objects::nonNull).map(url -> Constant.HTTPS + Constant.COLON + url).collect(Collectors.toList());
        }
        return imageList;
    }

    public static List<String> matchImage(String content, String patternStr, String backgroundSymbol) {
        Pattern pattern = Pattern.compile(patternStr);
        String[] split = content.split(IMG_SYMBOL);
        List<String> imageList = Lists.newArrayList();
        for (String sub : split) {
            if (StringUtils.isBlank(backgroundSymbol)){
                matchUrl(sub, pattern, imageList);
            }else if (sub.contains(backgroundSymbol)){
                matchUrl(sub, pattern, imageList);
            }
        }
        return imageList;
    }

    private static void matchUrl(String sub, Pattern pattern, List<String> imageList) {
        Matcher matcher = pattern.matcher(sub);   // 指定匹配器
        while (matcher.find()) { // 进行查找，并判断是否匹配
            log.info("匹配到的字符串：{}" , matcher.group());
            log.info("提取的图片地址：{}" , matcher.group(1));
            imageList.add(matcher.group(1));
        }
    }

    /**
     * 将图片链接列表拼接成html标签代码
     * @param imageList
     * @return 拼接后的html代码
     */
    public static String convertImageList2ProductDescriptionHtml(List<String> imageList) {
        // 商品详情模板
        String goodsDescTemplate = "<img src=\"%s\" />";

        StringBuilder stringBuilder = new StringBuilder("<div style=\"text-align: center;display: flex;flex-direction: column;justify-content: start\">");
        for (String image : imageList) {
            stringBuilder.append(String.format(goodsDescTemplate, image));
        }
        stringBuilder.append("</div>");
        log.info("拼接的字符串：" + stringBuilder);
        return stringBuilder.toString();
    }


    /**
     * 解析非主站商品商详内容中图片
     *
     * @param content 自定义商详
     * @return 解析后商详图片列表
     */
    public static List<String> convertDesc2ImageList(String content) {
        if(StringUtils.isBlank(content)){
            return new ArrayList<>();
        }
        // 商品详情，两种图片url格式，首先解析http:// ，如果不存在，解析https://开头的图片
        List<String> imageList = matchImage(content, "src=\"(http://.*?)\"",null);
        if (CollectionUtils.isEmpty(imageList)) {
            imageList = matchImage(content, "src=\"(https://.*?)\"",null);
        }
        return imageList;
    }


    public static void main(String[] args) {
        String description = "<p></p><p></p><p> </p><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=95b31c9cc01c76112341043928b3adbf\"/></div><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=3afecf687d178454841be858940ae170\"/></div><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=b72406e6ca49201f8f9b31e1e0286633\"/></div><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=e6a4466bb4a9ef4d16493413ea8d648e\"/></div><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=187167913af21e87ae670e9510f7fbf9\"/></div><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=fada2cd30df1015a5057b5fe4569e055\"/></div><p src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=aaf9b68cc3f5fce03f2c5dfe401029a6\"></p><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=0ddb7a0e2fc2e716ba5cc83c475d2d0d\"/></div><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=97a1b5c0b53021f9ff53fbd90f4cd229\"/></div><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=d30abe621e57eaf013fab7f27ba18a95\"/></div><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=ed468c3d84d7e22b6c29d585fc6c4690\"/></div><p src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=0af49ed0908151afbd84752aac80f18a\"></p><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=2384559767920815c9e9c41259f63758\"/></div><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=413e3bd1825eca8f39470facd6f99acc\"/></div><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=7327399fbed7fed274070c1417ea7c6c\"/></div><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=7a0a184a384b4e643387cabf572dab25\"/></div><div class=\"media-wrap image-wrap\"><img class=\"media-wrap image-wrap\" src=\"https://extranet-emea.bosch-pt.com/cgi-bin/WebObjects.dll/V5Prod_CatalogWeb.woa/41/wa/CatalogRequestHandler/getCachedImage?id=20831455abcbb15ac13b8a2464ccc236\"/></div><p></p>";

        List<String> imageList = convertDesc2ImageList(description);

        System.out.println(JSON.toJSONString(imageList));
    }
}
