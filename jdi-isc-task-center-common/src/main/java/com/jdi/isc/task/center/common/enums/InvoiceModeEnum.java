package com.jdi.isc.task.center.common.enums;

/**
 * <AUTHOR>
 * @description
 * @projectName jdi-isc-order-center
 * @create 2025-08-13 22:01
 */
public enum InvoiceModeEnum {

    BLUE_INVOICE(1, "","多单一票"),
    RED_INVOICE(2,"", "一单一票");

    /**
     *  工业定义
     *  * 开票模式
     *  * 多单一票1
     *  * 一单一票2
     */
    private Integer code;

    /**
     * 用于区分发票类型。
     */
    private String redFlag;

    private String description;

    InvoiceModeEnum(Integer code,String redFlag, String description) {
            this.code = code;
            this.redFlag = redFlag;
            this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getRedFlag() {
        return redFlag;
    }

    public String getDescription() {
        return description;
    }





    /**
     * 根据指定的代码获取对应的发票模式枚举值。
     *
     * @return 对应的发票模式枚举值的代码，若未找到则返回 null。
     */
    public static Integer getCodeByRedFlag(String redFlag) {
        for (InvoiceModeEnum type : values()) {
            if (type.getRedFlag().equals(redFlag)) {
                return type.getCode();
            }
        }
        return null;
    }


}

