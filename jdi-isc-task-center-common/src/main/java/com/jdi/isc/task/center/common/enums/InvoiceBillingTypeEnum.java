package com.jdi.isc.task.center.common.enums;

/**
 * <AUTHOR>
 * @description
 * @projectName jdi-isc-order-center
 * @create 2025-08-13 22:01
 */
public enum InvoiceBillingTypeEnum {

    CI_INVOICE(1,"CI", "CI形式发票"),
    VAT_INVOICE(2,"VAT", "VAT增值税发票");

    /**
     *  工业定义1,CI形式发票; 2,VAT增值税发票
     */
    private Integer code;

    /**
     * 用于区分发票类型。
     */
    private String redFlag;
    private String description;

    InvoiceBillingTypeEnum(Integer code, String redFlag, String description) {
            this.code = code;
            this.redFlag = redFlag;
            this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getRedFlag() {
        return redFlag;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据 redFlag 获取对应的 code
     * @param redFlag 红字标志
     * @return 对应的 code，未找到时返回 null
     */
    public static Integer getCodeByRedFlag(String redFlag) {
        for (InvoiceBillingTypeEnum type : values()) {
            if (type.getRedFlag().equals(redFlag)) {
                return type.getCode();
            }
        }
        return null;
    }

}

