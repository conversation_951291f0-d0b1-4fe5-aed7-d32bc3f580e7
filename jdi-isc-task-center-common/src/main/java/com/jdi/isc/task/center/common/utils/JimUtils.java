package com.jdi.isc.task.center.common.utils;

import com.alibaba.fastjson.JSON;
import com.jd.jim.cli.Cluster;
import com.jd.jim.cli.JimFuture;
import com.jd.jim.cli.PipelineClient;
import com.jd.jim.cli.protocol.ScriptOutputType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * jimdb工具类
 * <AUTHOR>
 * @date 20231115
 */
@Service
@Slf4j
public class JimUtils {

    @Resource(name = "jimClient")
    private Cluster jimClient;

    private static final Long RELEASE_SUCCESS = 1L;

    public Long incr(String key){
        try {
            return jimClient.incr(key);
        }catch (Exception e){
            log.error("JimUtils.incry error, target:{} ",key,e);
        }
        return null;
    }

    public Long incrBy(String key,Long size){
        try {
            return jimClient.incrBy(key,size);
        }catch (Exception e){
            log.error("JimUtils.incrBy error, target:{} ,size:{} ,errMsg:{}", key,size,e);
        }
        return null;
    }

    public String get(String key){
        try {
            return jimClient.get(key);
        }catch (Exception e){
            log.error("JimUtils.get error, target:{} ",key,e);
        }
        return null;
    }

    public Boolean pipeSet(Map<String,String> target) {
        try {
            if(target!=null){
                log.info("start pipeSet : {}", JSON.toJSONString(target));
                PipelineClient pipelineClient = jimClient.pipelineClient();
                for (Map.Entry<String,String> entry : target.entrySet()) {
                    pipelineClient.set(entry.getKey(),entry.getValue());
                }
                pipelineClient.flush();
            }
        }catch (Exception e){
            log.error("RedisCacheManager.pipeSet error, target:{} ,errMsg:{}", JSON.toJSONString(target),e);
        }
        return true;
    }

    public Boolean pipeRemove(Set<String> target) {
        try {
            if(target!=null){
                log.info("start pipeRemove : {}",JSON.toJSONString(target));
                PipelineClient pipelineClient = jimClient.pipelineClient();
                for(String key: target){
                    pipelineClient.del(key);
                }
                pipelineClient.flush();
            }
        }catch (Exception e){
            log.error("RedisCacheManager.pipeRemove error, target:{} ,errMsg:{}", JSON.toJSONString(target),e);
        }
        return true;
    }

    public Boolean pipeHDel(Map<String,String> target) {
        try {
            if(target!=null){
                log.info("start pipeHDel : {}",JSON.toJSONString(target));
                PipelineClient pipelineClient = jimClient.pipelineClient();
                for (Map.Entry<String,String> entry : target.entrySet()) {
                    pipelineClient.hDel(entry.getKey(),entry.getValue());
                }
                pipelineClient.flush();
            }
        }catch (Exception e){
            log.error("RedisCacheManager.pipeHDel error, target:{} ,errMsg:{}", JSON.toJSONString(target),e);
        }
        return true;
    }

    public Boolean pipeHmSet(Map<String,Map<String, String>> target, long timeout) {
        try {
            if(target!=null){
                log.info("start pipeHmSet : {}",JSON.toJSONString(target));
                PipelineClient pipelineClient = jimClient.pipelineClient();
                for (Map.Entry<String,Map<String, String>> entry : target.entrySet()) {
                    pipelineClient.hMSet(entry.getKey(),entry.getValue());
                    pipelineClient.expire(entry.getKey(), timeout, TimeUnit.SECONDS); // 设置过期时间
                }
                pipelineClient.flush();
            }
        }catch (Exception e){
            throw new RuntimeException("RedisCacheManager.pipeHmSet error" ,e);
        }
        return true;
    }

    public <T> List<T> pipeHGetAll(String redisPrefix, List<Long> ids, Class<T> clazz) {
        try {
            List<T> result = new ArrayList<>();
            PipelineClient pipelineClient = jimClient.pipelineClient();
            List<JimFuture<Map<String, String>>> futures = new ArrayList<>();
            for (Long id:ids){
                String key = redisPrefix + id;
                futures.add(pipelineClient.hGetAll(key));
            }
            pipelineClient.flush();
            for (JimFuture<Map<String, String>> future : futures) {
                Map<String, String> map = future.get();
                if(!map.isEmpty()){
                    T bean = JSON.parseObject(JSON.toJSONString(map), clazz);
                    result.add(bean);
                }
            }
            return result;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public String hGet(String mainKey,String childKey) {
        try {
            if(StringUtils.isNotBlank(mainKey) && StringUtils.isNotBlank(childKey)){
                return jimClient.hGet(mainKey,childKey);
            }
        }catch (Exception e){
            throw new RuntimeException("RedisCacheManager.hGet error" ,e);
        }
        return null;
    }

    /**
     * 简易锁,和simpleLockRelease配套使用
     */
    public Boolean simpleLock(String key,long deadLine,long timeout) {
        try {
            if(jimClient.setNX(key,String.valueOf(deadLine))){
                return jimClient.expire(key,timeout, TimeUnit.SECONDS);
            }else {
                //防止setNX后因异常未执行expire造成的死锁 ttl=-1代表key存在但没设置超时时间
                if(-1 == jimClient.ttl(key)){
                    jimClient.expire(key,timeout,TimeUnit.SECONDS);
                    return false;
                }
            }
        }catch (Exception e){
            throw new RuntimeException("RedisCacheManager.simpleLock error" ,e);
        }
        return false;
    }

    /**
     * 简易锁,和simpleLockRelease配套使用
     */
    public Boolean simpleLock(String key, String requestId, long timeout) {
        try {
            return jimClient.set(key, requestId, timeout, TimeUnit.SECONDS, false);
        } catch (Exception e) {
            throw new RuntimeException("RedisCacheManager.simpleLock error", e);
        }
    }

    /**
     * 简易锁释放
     */
    public void simpleLockRelease(String key,long deadLine) {
        try {
            if(StringUtils.isNotBlank(jimClient.get(key)) && Long.parseLong(jimClient.get(key)) == deadLine){
                jimClient.del(key);
            }
        }catch (Exception e){
            throw new RuntimeException("RedisCacheManager.simpleLockRelease error" ,e);
        }
    }

    /**
     * 简易锁释放
     */
    public boolean simpleLockRelease(String key, String requestId) {
        try {
            String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
            String load = jimClient.scriptLoad(script);
            Object result = jimClient.evalsha(load, Collections.singletonList(key), Collections.singletonList(requestId), false, ScriptOutputType.BOOLEAN);
            return RELEASE_SUCCESS.equals(result);
            /*if(StringUtils.isNotBlank(jimClient.get(key)) && Long.parseLong(jimClient.get(key)) == deadLine){
                jimClient.del(key);
            }*/
        } catch (Exception e) {
            throw new RuntimeException("RedisCacheManager.simpleLockRelease error", e);
        }
    }

    /**
     * 检查key是否存在
     * @param key key
     * @return 判断结果
     */
    public boolean exist(String key) {
        try {
            return jimClient.exists(key);
        } catch (Exception e) {
            throw new RuntimeException("RedisCacheManager.exist error" ,e);
        }
    }

}
