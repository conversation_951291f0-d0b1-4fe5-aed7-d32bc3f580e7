package com.jdi.isc.task.center.common.enums;

public enum CalculationModeEnum {

//    旧版-新版：含税价一致-未税价一致；
    CALCULATIONMODE_1(1,"NET", "未税价一致"),
    CALCULATIONMODE_2(2,"GROSS", "含税价一致");

    private final int value;

    private String redFlag;

    private final String description;

    CalculationModeEnum(int value,String redFlag, String description) {
        this.value = value;
        this.redFlag = redFlag;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public String getRedFlag() {
        return redFlag;
    }

    public static CalculationModeEnum fromValue(int value) {
        for (CalculationModeEnum status : CalculationModeEnum.values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unexpected value: " + value);
    }




    /**
     * 根据 redFlag 获取对应的 code
     * @param redFlag 红字标志
     * @return 对应的 code，未找到时返回 null
     */
    public static Integer getCodeByRedFlag(String redFlag) {
        for (CalculationModeEnum type : values()) {
            if (type.getRedFlag().equals(redFlag)) {
                return type.getValue();
            }
        }
        return null;
    }
}
