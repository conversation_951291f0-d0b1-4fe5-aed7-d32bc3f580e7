package com.jdi.isc.task.center.common.utils;


import cn.hutool.core.util.ReUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description：StrUtils
 * @Date 2025-08-27
 */
public class StrUtils {

    /**
     * 去除字符串中的所有空格、制表符等符号。
     * @param str 待处理的字符串。
     * @return 处理后的字符串。
     */
    public static String trimAll(String str){
        if (StringUtils.isBlank(str)) {
            return str;
        }
        // 替换掉所有空格、制表符等符号
        str = ReUtil.replaceAll(str, "\\p{Z}+", "");
        return str;
    }
}
