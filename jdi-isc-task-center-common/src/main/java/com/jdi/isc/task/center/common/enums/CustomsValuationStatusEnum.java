package com.jdi.isc.task.center.common.enums;

/**
 * @description 商品待评估的状态
 * <AUTHOR>
 * @date 2025/07/03
 **/
public enum CustomsValuationStatusEnum {

    /**
     * 待评估
     */
    PENDING_VALUATION(0, "待评估"),
    /**
     * 已存在
     */
    EXISTS(1, "已存在"),
    /**
     * 已完成
     */
    COMPLETE(2, "已完成");


    private Integer code;
    private String desc;

    private CustomsValuationStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CustomsValuationStatusEnum forCode(Integer code) {
        if (code == null) {
            return null;
        }

        CustomsValuationStatusEnum[] values = values();
        for (CustomsValuationStatusEnum enumObj : values) {
            if (code.equals(enumObj.getCode())) {
                return enumObj;
            }
        }
        return null;
    }
}
