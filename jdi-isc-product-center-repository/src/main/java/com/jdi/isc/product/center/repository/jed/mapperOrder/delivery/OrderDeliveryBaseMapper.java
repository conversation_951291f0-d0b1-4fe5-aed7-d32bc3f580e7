package com.jdi.isc.product.center.repository.jed.mapperOrder.delivery;

import com.jdi.isc.product.center.domain.delivery.po.OrderDeliveryPO;
import com.jdi.isc.product.center.domain.mku.biz.MkuPageReqVO;
import com.jdi.isc.product.center.domain.mku.po.MkuPO;
import com.jdi.isc.product.center.repository.jed.mapper.common.BasicMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【jdi_isc_order_delivery_sharding】的数据库操作Mapper
 * @createDate 2024-01-14 17:24:53
 * @Entity com.jdi.isc.product.center.domain.delivery.po.OrderDeliveryPO
 */
public interface OrderDeliveryBaseMapper extends BasicMapper<OrderDeliveryPO> {

}




