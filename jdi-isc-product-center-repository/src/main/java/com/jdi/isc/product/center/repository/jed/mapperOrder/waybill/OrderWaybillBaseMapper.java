package com.jdi.isc.product.center.repository.jed.mapperOrder.waybill;

import com.jdi.isc.product.center.domain.order.po.OrderPO;
import com.jdi.isc.product.center.domain.waybill.biz.OrderWaybillPageVO;
import com.jdi.isc.product.center.domain.waybill.po.OrderWaybillPO;
import com.jdi.isc.product.center.repository.jed.mapper.common.BasicMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 二段运单
 * @Author: zhaojianguo21
 * @Date: 2024/06/13 17:19
 **/
@Mapper
public interface OrderWaybillBaseMapper extends BasicMapper<OrderWaybillPO> {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    @Select({"<script> " +
            " select ow.id, ow.waybill_num, ow.status, ow.client_code, ow.transport_method, ow.third_transport_num, ow.carrier " +
            "   , ow.delivery_person, ow.license_plate, ow.delivery_mobile_encrypt as delivery_mobile " +
            "   , ow.delivery_date, ow.expected_delivery_date, ow.delivery_material, ow.remark, ow.creator, ow.updater, ow.create_time, ow.update_time " +
            "   , ow.warehouse_id, ow.data_source, ow.waybill_type, ow.third_waybill_num " +
            "   , owdc.delivery_customer_date , owdc.sign_status " +
            " from jdi_isc_order_waybill_sharding ow " +
            "   left join jdi_isc_order_waybill_delivery_customer_sharding owdc on ow.waybill_num = owdc.waybill_num " +
            " <where>" +
            " <if test='@org.apache.commons.lang3.StringUtils@isNotBlank(waybillNum)'> " +
            "   and ow.waybill_num = #{waybillNum}  " +
            " </if> " +
            " <if test='warehouseId != null'> " +
            "   and ow.warehouse_id = #{warehouseId}  " +
            " </if> " +
            " <if test='status != null'> " +
            "   and ow.status = #{status}  " +
            " </if> " +
            " <if test='clientCode != null'> " +
            "   and ow.client_code = #{clientCode}  " +
            " </if> " +
            " <if test='transportMethod != null'> " +
            "   and ow.transport_method = #{transportMethod}  " +
            " </if> " +
            " <if test='@org.apache.commons.lang3.StringUtils@isNotBlank(thirdTransportNum)'> " +
            "   and ow.third_transport_num = #{thirdTransportNum}  " +
            " </if> " +
            " <if test='@org.apache.commons.lang3.StringUtils@isNotBlank(carrier)'> " +
            "   and ow.carrier like concat('%', #{carrier}, '%') " +
            " </if> " +
            " <if test='@org.apache.commons.lang3.StringUtils@isNotBlank(deliveryPerson)'> " +
            "   and ow.delivery_person = #{deliveryPerson}  " +
            " </if> " +
            " <if test='licensePlate != null'> " +
            "   and ow.license_plate = #{licensePlate}  " +
            " </if> " +
            " <if test='deliveryDateStart != null'> " +
            "   and ow.delivery_date <![CDATA[ >= ]]> #{deliveryDateStart}  " +
            " </if> " +
            " <if test='deliveryDateEnd != null'> " +
            "   and ow.delivery_date <![CDATA[ <= ]]> #{deliveryDateEnd}  " +
            " </if> " +
            " <if test='deliveryCustomerDateStart != null'> " +
            "   and owdc.delivery_customer_date <![CDATA[ >= ]]> #{deliveryCustomerDateStart}  " +
            " </if> " +
            " <if test='deliveryCustomerDateEnd != null'> " +
            "   and owdc.delivery_customer_date <![CDATA[ <= ]]> #{deliveryCustomerDateEnd}  " +
            " </if> " +
            " <if test='createTimeStart != null'> " +
            "   and ow.create_time <![CDATA[ >= ]]> #{createTimeStart}  " +
            " </if> " +
            " <if test='createTimeEnd != null'> " +
            "   and ow.create_time <![CDATA[ <= ]]> #{createTimeEnd}  " +
            " </if> " +
            /*" <if test='dataSource != null'> " +
            "   and ow.data_source = #{dataSource}  " +
            " </if> " +
            " <if test='dataSource == null'> " +
            "   and ow.data_source is null " +
            " </if> " +*/
            " <if test='waybillType != null'> " +
            "   and ow.waybill_type = #{waybillType} " +
            " </if> " +
            " <if test='thirdWaybillNum != null'> " +
            "   and ow.third_waybill_num = #{thirdWaybillNum} " +
            " </if> " +
            " <if test='dataIsolationQueryVO != null and dataIsolationQueryVO.blackClientCodes != null'> " +
            "  and ow.client_code not in " +
            " <foreach collection=\"dataIsolationQueryVO.blackClientCodes\" item=\"clientCode\" open=\"(\" close=\")\" separator=\",\"> " +
            "   #{clientCode} " +
            " </foreach> " +
            " </if> " +
            " <if test='dataIsolationQueryVO != null and dataIsolationQueryVO.uat == true'> " +
            "  and ow.client_code in " +
            " <foreach collection=\"dataIsolationQueryVO.uatClientCodes\" item=\"clientCode\" open=\"(\" close=\")\" separator=\",\"> " +
            "   #{clientCode} " +
            " </foreach> " +
            " </if> " +
            " and ow.yn = 1 " +
            " </where>" +
            " order by ow.update_time desc " +
            " limit #{offset},#{size}" +
            "</script>"})
    List<OrderWaybillPageVO.Response> pageSearch(OrderWaybillPageVO.Request input);

    /**
     * 分页查询的列表总数
     * @param input 查询条件
     * @return 总条数
     */
    @Select({"<script> " +
            " select count(0) " +
            " from jdi_isc_order_waybill_sharding ow " +
            "   left join jdi_isc_order_waybill_delivery_customer_sharding owdc on ow.waybill_num = owdc.waybill_num " +
            " <where>" +
            " <if test='@org.apache.commons.lang3.StringUtils@isNotBlank(waybillNum)'> " +
            "   and ow.waybill_num = #{waybillNum}  " +
            " </if> " +
            " <if test='warehouseId != null'> " +
            "   and ow.warehouse_id = #{warehouseId}  " +
            " </if> " +
            " <if test='status != null'> " +
            "   and ow.status = #{status}  " +
            " </if> " +
            " <if test='clientCode != null'> " +
            "   and ow.client_code = #{clientCode}  " +
            " </if> " +
            " <if test='transportMethod != null'> " +
            "   and ow.transport_method = #{transportMethod}  " +
            " </if> " +
            " <if test='@org.apache.commons.lang3.StringUtils@isNotBlank(thirdTransportNum)'> " +
            "   and ow.third_transport_num = #{thirdTransportNum}  " +
            " </if> " +
            " <if test='@org.apache.commons.lang3.StringUtils@isNotBlank(carrier)'> " +
            "   and ow.carrier like concat('%', #{carrier}, '%') " +
            " </if> " +
            " <if test='@org.apache.commons.lang3.StringUtils@isNotBlank(deliveryPerson)'> " +
            "   and ow.delivery_person = #{deliveryPerson}  " +
            " </if> " +
            " <if test='licensePlate != null'> " +
            "   and ow.license_plate = #{licensePlate}  " +
            " </if> " +
            " <if test='deliveryDateStart != null'> " +
            "   and ow.delivery_date <![CDATA[ >= ]]> #{deliveryDateStart}  " +
            " </if> " +
            " <if test='deliveryDateEnd != null'> " +
            "   and ow.delivery_date <![CDATA[ <= ]]> #{deliveryDateEnd}  " +
            " </if> " +
            " <if test='deliveryCustomerDateStart != null'> " +
            "   and owdc.delivery_customer_date <![CDATA[ >= ]]> #{deliveryCustomerDateStart}  " +
            " </if> " +
            " <if test='deliveryCustomerDateEnd != null'> " +
            "   and owdc.delivery_customer_date <![CDATA[ <= ]]> #{deliveryCustomerDateEnd}  " +
            " </if> " +
            " <if test='createTimeStart != null'> " +
            "   and ow.create_time <![CDATA[ >= ]]> #{createTimeStart}  " +
            " </if> " +
            " <if test='createTimeEnd != null'> " +
            "   and ow.create_time <![CDATA[ <= ]]> #{createTimeEnd}  " +
            " </if> " +
            /*" <if test='dataSource != null'> " +
            "   and ow.data_source = #{dataSource}  " +
            " </if> " +
            " <if test='dataSource == null'> " +
            "   and ow.data_source is null " +
            " </if> " +*/
            " <if test='waybillType != null'> " +
            "   and ow.waybill_type = #{waybillType} " +
            " </if> " +
            " <if test='thirdWaybillNum != null'> " +
            "   and ow.third_waybill_num = #{thirdWaybillNum} " +
            " </if> " +
            " <if test='dataIsolationQueryVO != null and dataIsolationQueryVO.blackClientCodes != null'> " +
            "  and ow.client_code not in " +
            " <foreach collection=\"dataIsolationQueryVO.blackClientCodes\" item=\"clientCode\" open=\"(\" close=\")\" separator=\",\"> " +
            "   #{clientCode} " +
            " </foreach> " +
            " </if> " +
            " <if test='dataIsolationQueryVO != null and dataIsolationQueryVO.uat == true'> " +
            "  and ow.client_code in " +
            " <foreach collection=\"dataIsolationQueryVO.uatClientCodes\" item=\"clientCode\" open=\"(\" close=\")\" separator=\",\"> " +
            "   #{clientCode} " +
            " </foreach> " +
            " </if> " +
            " and ow.yn = 1 " +
            " </where>" +
            "</script>"})
    long pageSearchTotal(OrderWaybillPageVO.Request input);

}
