package com.jdi.isc.product.center.repository.jed.mapperOrder.declaration;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdi.isc.product.center.domain.declaration.po.CustomsInfoPo;
import com.jdi.isc.product.center.domain.declaration.po.CustomsRelationPo;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 报关单dao层
* <AUTHOR>
*/
public interface CustomsRelationMapper extends BaseMapper<CustomsRelationPo> {


    /**
     * 执行数据修复操作
     * @param ql 数据修复的 SQL 查询语句
     */
    @Select({"<script> " +
            " ${ql}" +
            "</script>"})
    void dataRepair(String ql);



    @Select({"<script> " +
            "SELECT" +
            "  a.*," +
            "  b.*" +
            "FROM" +
            "  (" +
            "    SELECT" +
            "      TABLE_NAME," +
            "      column_name," +
            "      CONCAT(" +
            "        data_type," +
            "        '('," +
            "        IFNULL(character_maximum_length, numeric_precision)," +
            "        ')'" +
            "      ) AS data_type," +
            "      column_comment" +
            "    FROM" +
            "      information_schema.columns" +
            "    WHERE" +
            "      table_name in (" +
            "        SELECT" +
            "          TABLE_NAME" +
            "        FROM" +
            "          information_schema.tables" +
            "        WHERE" +
            "          TABLE_TYPE = 'BASE TABLE'" +
            "          and TABLE_COMMENT != ''" +
            "          and TABLE_COMMENT != 'vitess_sequence'" +
            "      )" +
            "  ) a" +
            "  left join (" +
            "    SELECT" +
            "      TABLE_NAME," +
            "      TABLE_COMMENT" +
            "    FROM" +
            "      information_schema.tables" +
            "    WHERE" +
            "      TABLE_TYPE = 'BASE TABLE'" +
            "      and TABLE_COMMENT != ''" +
            "      and TABLE_COMMENT != 'vitess_sequence'" +
            "  ) b on a.TABLE_NAME = b.TABLE_NAME; " +
            "</script>"})
    List<Map<String, String>> getTable();
}




