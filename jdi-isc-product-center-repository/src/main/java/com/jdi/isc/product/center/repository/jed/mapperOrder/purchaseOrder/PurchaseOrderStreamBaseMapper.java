package com.jdi.isc.product.center.repository.jed.mapperOrder.purchaseOrder;

import com.jdi.isc.product.center.domain.purchaseOrder.biz.PurchaseOrderStreamPageVO;
import com.jdi.isc.product.center.domain.purchaseOrder.po.PurchaseOrderStreamPO;
import com.jdi.isc.product.center.repository.jed.mapper.common.BasicMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 采购单流水
 * @Author: liujun553
 * @Date: 2024/07/23 20:52
 **/
@Mapper
public interface PurchaseOrderStreamBaseMapper extends BasicMapper<PurchaseOrderStreamPO> {

    /**
     * 分页查询
     * @param input 查询条件
     * @return 分页查询结果
     */
    @Select({"<script> " +
            " select updater, update_time " +
            " from  " +
            " <where>" +
            " </where>" +
            " order by update_time desc " +
            " limit #{offset},#{size}" +
            "</script>"})
    List<PurchaseOrderStreamPageVO.Response> pageSearch(PurchaseOrderStreamPageVO.Request input);

    /**
     * 分页查询的列表总数
     * @param input 查询条件
     * @return 总条数
     */
    @Select({"<script> " +
            " select count(0) " +
            " from  " +
            " <where>" +
            " </where>" +
            "</script>"})
    long pageSearchTotal(PurchaseOrderStreamPageVO.Request input);

}
