package com.jdi.isc.product.center.repository.jed.mapperOrder.purchaseOrder;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdi.isc.product.center.domain.order.biz.OrderPageReqVO;
import com.jdi.isc.product.center.domain.order.po.OrderPO;
import com.jdi.isc.product.center.domain.purchaseOrder.biz.QueryPurchaseOrderListVO;
import com.jdi.isc.product.center.domain.purchaseOrder.po.PurchaseOrderPO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * PurchaseOrderMapper继承基类
 */
public interface PurchaseOrderMapper extends BaseMapper<PurchaseOrderPO> {

    @Select({"<script> select po.id,po.purchase_order_id,po.order_id,po.supplier_code,po.sku_num,po.sku_kind_num,po.purchase_order_status,po.purchase_order_type,po.country_code,po.currency,po.purchase_total_price,po.purchase_order_ext_info,po.version,po.remark,po.creator,po.updater,po.create_time,po.update_time,po.yn" +
            " from jdi_isc_purchase_order_sharding  po " +
            "<where> po.yn = 1" +
            " <if test=\"purchaseOrderId != null\"> " +
            "and po.purchase_order_id = #{purchaseOrderId}" +
            " </if> " +
            " <if test=\"orderId != null\"> " +
            "and po.order_id = #{orderId}" +
            " </if> " +
            " <if test=\"purchaseOrderStatus != null\"> " +
            "and po.purchase_order_status = #{purchaseOrderStatus}" +
            " </if> " +
            " <if test=\"beginCreateTime != null\"> " +
            "and po.create_time <![CDATA[ >= ]]> #{beginCreateTime}" +
            " </if> " +
            " <if test=\"endCreateTime != null\"> " +
            "and po.create_time <![CDATA[ <= ]]> #{endCreateTime}" +
            " </if> " +
            " <if test=\"(skuName != null and skuName != '') or skuId != null or mkuId != null\"> " +
            "and exists (select 1 from jdi_isc_purchase_order_ware_sharding ware where po.purchase_order_id = ware.purchase_order_id and ware.yn = 1" +
            " <if test=\"mkuId != null and mkuId != '' \"> " +
            "and ware.mku_id = #{mkuId}" +
            " </if> " +
            " <if test=\"skuId != null and skuId != '' \"> " +
            "and ware.sku_id = #{skuId}" +
            " </if> " +
            " <if test=\"skuName != null and skuName != '' \"> " +
            "and ware.sku_json_info like concat('%', #{skuName}, '%')" +
            " </if> " +
            ")" +
            " </if> " +

            " <if test=\"settlementStatusSet != null\"> " +
                "and exists (select 1 from jdi_isc_purchase_order_settlement_sharding settlement where po.purchase_order_id = settlement.purchase_order_id and settlement.yn = 1 and settlement.settlement_status in" +
                " <foreach collection='settlementStatusSet' item='settlementStatus' separator=',' open='(' close=')'> #{settlementStatus} </foreach> " +
            " </if> " +
            " <if test=\"settlementStatus != null\"> " +
            "and exists (select 1 from jdi_isc_purchase_order_settlement_sharding settlement where po.purchase_order_id = settlement.purchase_order_id and settlement.yn = 1 and settlement.settlement_status = #{settlementStatus}" +
            " </if> " +

            " </where>" +
            " order by po.update_time desc " +
            " limit #{offset},#{size}" +
            "</script>"
    })
    List<PurchaseOrderPO> pageCustomer(QueryPurchaseOrderListVO queryPurchaseOrderListVO);

    @Select({"<script>select count(1) from jdi_isc_purchase_order_sharding  po " +
            "<where> po.yn = 1" +
            " <if test=\"purchaseOrderId != null\"> " +
            "and po.purchase_order_id = #{purchaseOrderId}" +
            " </if> " +
            " <if test=\"orderId != null\"> " +
            "and po.order_id = #{orderId}" +
            " </if> " +
            " <if test=\"purchaseOrderStatus != null\"> " +
            "and po.purchase_order_status = #{purchaseOrderStatus}" +
            " </if> " +
            " <if test=\"beginCreateTime != null\"> " +
            "and po.create_time <![CDATA[ >= ]]> #{beginCreateTime}" +
            " </if> " +
            " <if test=\"endCreateTime != null\"> " +
            "and po.create_time <![CDATA[ <= ]]> #{endCreateTime}" +
            " </if> " +
            " <if test=\"(skuName != null and skuName != '') or skuId != null or mkuId != null\"> " +
            "and exists (select 1 from jdi_isc_purchase_order_ware_sharding ware where po.purchase_order_id = ware.purchase_order_id and ware.yn = 1" +
            " <if test=\"mkuId != null and mkuId != '' \"> " +
            "and ware.mku_id = #{mkuId}" +
            " </if> " +
            " <if test=\"skuId != null and skuId != '' \"> " +
            "and ware.sku_id = #{skuId}" +
            " </if> " +
            " <if test=\"skuName != null and skuName != '' \"> " +
            "and ware.sku_json_info like concat('%', #{skuName}, '%')" +
            " </if> " +
            ")" +
            " </if> " +

            " <if test=\"settlementStatusSet != null\"> " +
            "and exists (select 1 from jdi_isc_purchase_order_settlement_sharding settlement where po.purchase_order_id = settlement.purchase_order_id and settlement.yn = 1 and settlement.settlement_status in" +
            " <foreach collection='settlementStatusSet' item='settlementStatus' separator=',' open='(' close=')'> #{settlementStatus} </foreach> " +
            " </if> " +
            " <if test=\"settlementStatus != null\"> " +
            "and exists (select 1 from jdi_isc_purchase_order_settlement_sharding settlement where po.purchase_order_id = settlement.purchase_order_id and settlement.yn = 1 and settlement.settlement_status = #{settlementStatus}" +
            " </if> " +

            " </where>" +
            "</script>"
    })
    long pageCountCustomer(QueryPurchaseOrderListVO queryPurchaseOrderListVO);


    @Select({"<script>select id, purchase_order_id, pin, contract_num, parent_purchase_order_id, split_purchase_order_id, wares_purchase_total_price, " +
            "service_fee, order_freight_price, purchase_order_taxes, valid_state, purchase_create_time, receive_time, shipped_time, enter_warehouse_time, " +
            "complete_time, confirm_time, cancel_time, purchase_model, customs_clearance, order_id, supplier_code, sku_num, sku_kind_num, purchase_order_status," +
            " purchase_order_type, country_code, currency, purchase_total_price, enterprise_warehouse_code, purchase_order_ext_info, update_client_info, " +
            "inbound_warehouse_ware_num, version, remark, creator, updater, create_time, update_time, yn, enter_storehouse_time, out_storehouse_time, enter_warehouse_no " +
            " from jdi_isc_purchase_order_sharding orders" +
            "<where> orders.yn = 1 and orders.valid_state = 1  and orders.purchase_model = 1" +
            " <if test=\"purchaseOrderId != null and purchaseOrderId != ''\"> " +
            " and orders.purchase_order_id = #{purchaseOrderId}" +
            " </if> " +
            " <if test=\"purchaseOrderStatus != null\"> " +
            " and orders.purchase_order_status = #{purchaseOrderStatus}" +
            " </if> " +
            " <if test=\"purchaseOrderType != null\"> " +
            " and orders.purchase_order_type = #{purchaseOrderType}" +
            " </if> " +
            " <if test=\"countryCode != null and countryCode != '' \"> " +
            " and orders.country_code = #{countryCode}" +
            " </if> " +
            " <if test=\"enterStorehouseTimeStart != null and enterStorehouseTimeEnd != null\"> " +
            " and orders.enter_storehouse_time between #{enterStorehouseTimeStart} and #{enterStorehouseTimeEnd}" +
            " </if> " +
            " <if test=\"purchaseCreateTimeStart != null and purchaseCreateTimeEnd != null\"> " +
            " and orders.purchase_create_time between #{purchaseCreateTimeStart} and #{purchaseCreateTimeEnd}" +
            " </if> " +
            " <if test=\"purchaseOrderIds != null and purchaseOrderIds.size() > 0\"> " +
            " and orders.purchase_order_id in " +
            " <foreach collection='purchaseOrderIds' item='purchaseOrderId' open='(' separator=',' close=')'>" +
            "  #{purchaseOrderId}" +
            " </foreach>" +
            " </if> " +
            " <if test=\"purchaseOrderStatusSet != null and purchaseOrderStatusSet.size() > 0\"> " +
            " and orders.purchase_order_status in " +
            " <foreach collection='purchaseOrderStatusSet' item='purchaseOrderStatus' open='(' separator=',' close=')'>" +
            " #{purchaseOrderStatus}" +
            " </foreach>" +
            " </if> " +
            "<if test=\"notPurchaseOrderIds != null and notPurchaseOrderIds.size() > 0\"> " +
            " and orders.purchase_order_id not in " +
            " <foreach collection='notPurchaseOrderIds' item='purchaseOrderId' open='(' separator=',' close=')'>" +
            " #{purchaseOrderId}" +
            " </foreach>" +
            "</if> " +
            "</where>" +
            " order by update_time desc " +
            " limit #{offset},#{size}" +
            "</script>"
    })
    List<PurchaseOrderPO> page4Customs(QueryPurchaseOrderListVO orderPageReqVO);


    @Select({"<script>select count(1) " +
            " from jdi_isc_purchase_order_sharding orders" +
            "<where> orders.yn = 1 and orders.valid_state = 1 and orders.purchase_model = 1" +
            " <if test=\"purchaseOrderId != null and purchaseOrderId != ''\"> " +
            " and orders.purchase_order_id = #{purchaseOrderId}" +
            " </if> " +
            " <if test=\"purchaseOrderStatus != null\"> " +
            " and orders.purchase_order_status = #{purchaseOrderStatus}" +
            " </if> " +
            " <if test=\"purchaseOrderType != null\"> " +
            " and orders.purchase_order_type = #{purchaseOrderType}" +
            " </if> " +
            " <if test=\"countryCode != null and countryCode != '' \"> " +
            " and orders.country_code = #{countryCode}" +
            " </if> " +
            " <if test=\"enterStorehouseTimeStart != null and enterStorehouseTimeEnd != null\"> " +
            " and orders.enter_storehouse_time between #{enterStorehouseTimeStart} and #{enterStorehouseTimeEnd}" +
            " </if> " +
            " <if test=\"purchaseCreateTimeStart != null and purchaseCreateTimeEnd != null\"> " +
            " and orders.purchase_create_time between #{purchaseCreateTimeStart} and #{purchaseCreateTimeEnd}" +
            " </if> " +
            " <if test=\"purchaseOrderIds != null and purchaseOrderIds.size() > 0\"> " +
            "and orders.purchase_order_id in " +
            "<foreach collection='purchaseOrderIds' item='purchase_order_id' open='(' separator=',' close=')'>" +
            "#{purchaseOrderId}" +
            "</foreach>" +
            " </if> " +
            " <if test=\"purchaseOrderStatusSet != null and purchaseOrderStatusSet.size() > 0\"> " +
            "and orders.purchase_order_status in " +
            "<foreach collection='purchaseOrderStatusSet' item='purchaseOrderStatus' open='(' separator=',' close=')'>" +
            "#{purchaseOrderStatus}" +
            "</foreach>" +
            " </if> " +
            " <if test=\"notPurchaseOrderIds != null and notPurchaseOrderIds.size() > 0\"> " +
            " and orders.purchase_order_id not in " +
            "<foreach collection='notPurchaseOrderIds' item='purchaseOrderId' open='(' separator=',' close=')'>" +
            "#{purchaseOrderId}" +
            "</foreach>" +
            " </if> " +
            "</where>" +
            "</script>"
    })
    long count4Customs(QueryPurchaseOrderListVO orderPageReqVO);
}