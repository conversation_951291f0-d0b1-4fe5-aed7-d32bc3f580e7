package com.jdi.isc.product.center.common.enums.response;

import com.jdi.isc.library.i18n.enums.http.ResponseEnums;

public enum ProductErrorEnums implements ResponseEnums {

    /**
     * 表示商品列表为空的错误码。
     */
    PRODUCT_LIST_CANNOT_BE_EMPTY ("3000030017", "商品列表不能为空"),
    ;
    private final String code;
    private final String message;

    ProductErrorEnums(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
