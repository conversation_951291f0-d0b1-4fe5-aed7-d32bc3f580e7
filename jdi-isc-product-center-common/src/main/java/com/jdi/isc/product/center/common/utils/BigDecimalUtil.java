package com.jdi.isc.product.center.common.utils;


import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * The type Big decimal util.
 *
 * <AUTHOR>
 */
public class BigDecimalUtil {
    private BigDecimalUtil() {
    }

    /**
     * Is big decimal boolean.
     *
     * @param str the str
     * @return the boolean
     */
    public static boolean isBigDecimal(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        try {
            new BigDecimal(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Ne null and zero boolean.
     *
     * @param input the input
     * @return the boolean
     */
    public static boolean neNullAndZero(BigDecimal input) {
        return input != null && input.compareTo(BigDecimal.ZERO) != 0;
    }

    /**
     * Gets profit rate format.
     *
     * @param profitRate the profit rate
     * @return the profit rate format
     */
    public static String getProfitRateFormat(BigDecimal profitRate) {
        if (profitRate != null) {
            return profitRate.multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString() + "%";
        }
        return null;
    }

}
