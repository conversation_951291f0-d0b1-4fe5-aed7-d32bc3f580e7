package com.jdi.isc.product.center.common.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.common.web.LoginContext;
import com.jdi.isc.fulfillment.soa.api.common.SystemInfoFulfillmentReqApiDTO;
import com.jdi.isc.order.center.api.common.SystemInfoOrderApiReq;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;

/**
 * 填充订单域系统参数工具类
 *
 * @author: zhangjin176
 * @date: 2025/5/23 14:24
 * @version:
 **/
@Slf4j
public class SystemUtil {

    /**
     * 填充dto的systemInfo字段。
     *
     * @param target 需要填充的dto对象。
     */
    public static <T> void fillOrderSystemInfo(T target) {
        if (target == null) {
            return;
        }
        SystemInfoOrderApiReq systemInfo = new SystemInfoOrderApiReq();
        systemInfo.setSystemCode("product-center");
        systemInfo.setLocaleList(Lists.newArrayList(LoginContext.getLoginContext().getLang()));
        systemInfo.setServiceIp(HostUtil.getLocalIP());
        systemInfo.setUserIp(LoginContext.getLoginContext().getIp());
        try {
            java.lang.reflect.Field field = findField(target.getClass(), "systemInfo");
            if (field != null) {
                field.setAccessible(true);
                field.set(target, systemInfo);
            } else {
                log.warn("未找到 systemInfo 字段，无法填充: {}", JSON.toJSONString(target));
            }
        } catch (IllegalAccessException e) {
            log.error("填充系统参数时发生异常: {}", JSON.toJSONString(target), e);
        }

        try {
            Field operatorField = target.getClass().getDeclaredField("operator");
            if (operatorField.getType().equals(String.class)) {
                operatorField.setAccessible(true);
                operatorField.set(target, LoginContext.getLoginContext().getPin());
            }
        } catch (NoSuchFieldException e) {
            // 没有 operator 字段，忽略
        } catch (IllegalAccessException e) {
            log.error("存在 operator 设置异常");
        }
    }

    /**
     * 递归查找字段，包括父类字段
     */
    private static java.lang.reflect.Field findField(Class<?> clazz, String fieldName) {
        while (clazz != null) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        return null;
    }
    public static <T extends SystemInfoOrderApiReq> void fillOrderExtSystemInfo(T target) {
        if (target == null) {
            return;
        }
        target.setLocaleList(Lists.newArrayList(LoginContext.getLoginContext().getLang()));
        target.setSystemCode("product-center");
        target.setServiceIp(HostUtil.getLocalIP());
        target.setUserIp(LoginContext.getLoginContext().getIp());
        try {
            Field operatorField = target.getClass().getDeclaredField("operator");
            if (operatorField.getType().equals(String.class)) {
                operatorField.setAccessible(true);
                operatorField.set(target, LoginContext.getLoginContext().getPin());
            }
        } catch (NoSuchFieldException e) {
            // 没有 operator 字段，忽略
        } catch (IllegalAccessException e) {
            log.error("存在 operator 设置异常");
        }
    }


    /**
     * 填充dto的systemInfo字段。
     *
     * @param dto 需要填充的dto对象。
     */
    public static <T> void fillFulfillmentSystemInfo(T dto) {
        if (dto == null) {
            return;
        }
        SystemInfoFulfillmentReqApiDTO systemInfoReq = new SystemInfoFulfillmentReqApiDTO();
        systemInfoReq.setLang(LoginContext.getLoginContext().getLang());
        systemInfoReq.setServiceIp(HostUtil.getLocalIP());
        systemInfoReq.setUserIp(LoginContext.getLoginContext().getIp());
        systemInfoReq.setSystemCode("product-center");
        try {
            java.lang.reflect.Field field = findField(dto.getClass(), "systemInfoReq");
            if (field != null) {
                field.setAccessible(true);
                field.set(dto, systemInfoReq);
            } else {
                log.warn("未找到 systemInfoReq 字段，无法填充: {}", JSON.toJSONString(dto));
            }
        } catch (IllegalAccessException e) {
            log.error("填充系统参数时发生异常: {}", JSON.toJSONString(dto), e);
        }
    }
}
