package com.jdi.isc.product.center.common.exception;

/**
 * 任务操作异常
 */
public class TaskBizException extends RuntimeException{

	private static final long serialVersionUID = 1L;

	private final String code;
    private final String msg;

    public TaskBizException(String code, String msg){
        this(code, msg, null);
    }

    public TaskBizException(String msg, Throwable cause){
        super(msg, cause);
        this.msg = msg;
        this.code = null;
    }

    public TaskBizException(String msg){
        super(msg);
        this.msg = msg;
        this.code = null;
    }

    public TaskBizException(String code, String msg, Throwable cause) {

        super(msg, cause);
        this.msg = msg;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
