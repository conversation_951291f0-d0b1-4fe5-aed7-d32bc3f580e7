package com.jdi.isc.product.center.common.ducc;

import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/6 17:43
 */
@Data
public class OperDuccDataIsolationConfig {

    private Set<String> blackErps;

    private Set<String> blackPins;

    private Set<String> blackClientCodes;

    private Set<String> blackSupplierCodes;

    private Map<String, List<DataIsolationRules>> rules;

    @Data
    public static class DataIsolationRules {

        /**
         * 目标语句
         */
        private String targetMappedStatementId;

        /**
         * 目标表别名
         */
        private String targetTableAlias;

        /**
         * 隔离的字段
         */
        private String isolationColumn;

        /**
         * 隔离数据类型
         */
        private String isolationType;
    }
}
