package com.jdi.isc.product.center.common.frame;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.google.common.base.Joiner;
import com.jd.common.web.LoginContext;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.isc.product.center.common.costants.Constant;
import com.jdi.isc.product.center.common.ducc.OperDuccConfig;
import com.jdi.isc.product.center.common.ducc.OperDuccDataIsolationConfig;
import com.jdi.isc.product.center.common.enums.response.JdiIscQueryDataTypeEnum;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 数据权限隔离-废弃
 * @date 2025/6/5 14:41
 */
@Slf4j
//@Service
public class JdiIscDataIsolationPermissionHandler implements DataPermissionHandler {

    @Autowired
    private OperDuccConfig operDuccConfig;

    private String getSqlSegment(OperDuccDataIsolationConfig config, List<OperDuccDataIsolationConfig.DataIsolationRules> rules) {
        List<String> sqlSegmentList = rules.stream().map(rule -> getSqlSegment(config, rule)).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        return Joiner.on(" AND ").join(sqlSegmentList);
    }

    private String getSqlSegment(OperDuccDataIsolationConfig config, OperDuccDataIsolationConfig.DataIsolationRules rule) {

        StringBuilder sqlSegment = new StringBuilder();

        // Handle table alias
        if (StringUtils.isNotBlank(rule.getTargetTableAlias())) {
            sqlSegment.append(rule.getTargetTableAlias()).append(Constant.DOT);
        }

        // Validate column name
        String column = Objects.requireNonNull(rule.getIsolationColumn(), "Column name cannot be null");
        sqlSegment.append(column);

        // Handle values
        Set<String> values = new HashSet<>();
        if(JdiIscQueryDataTypeEnum.TEST_ERP.getCode().equals(rule.getIsolationType())){
            values = config.getBlackErps();
        }else if(JdiIscQueryDataTypeEnum.TEST_PIN.getCode().equals(rule.getIsolationType())){
            values = config.getBlackPins();
        }else if(JdiIscQueryDataTypeEnum.TEST_CLIENT_CODE.getCode().equals(rule.getIsolationType())){
            values = config.getBlackClientCodes();
        }else if(JdiIscQueryDataTypeEnum.TEST_SUPPLIER_CODE.getCode().equals(rule.getIsolationType())){
            values = config.getBlackSupplierCodes();
        }

        if(CollectionUtils.isEmpty(values)){
            log.info("dataIsolation, values is null, type = {}", rule.getIsolationType());
            return null;
        }
        // If you must concatenate:
         sqlSegment.append(" not in ('").append(Joiner.on("','").join(values)).append("')");

        return sqlSegment.toString();
    }

    @Override
    public Expression getSqlSegment(Expression where, String mappedStatementId) {
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.product.center.common.frame.JdiIscDataPermissionHandler.getSqlSegment");
        String whereStr = null;
        try {

            // 数据为空不处理
            if(where == null || StringUtils.isBlank(mappedStatementId)){
                return where;
            }
            whereStr = where.toString();

            // 只有管理员才能看所有数据，erp为空不是管理员
            String erp = LoginContext.getLoginContext().getPin();
            if(StringUtils.isNotBlank(erp) && operDuccConfig.getAuthAdminErp().contains(erp)){
                log.info("erp = {}, 是管理员账号，可以查看所有数据", erp);
                return where;
            }

            // 只处理隔离规则中的数据
            OperDuccDataIsolationConfig dataIsolationConfig = operDuccConfig.getDataIsolationConfig();
            Map<String, List<OperDuccDataIsolationConfig.DataIsolationRules>> ruleMap = dataIsolationConfig.getRules();

            if(MapUtils.isEmpty(ruleMap)){
                log.info("dataIsolation, rules is {}", JSON.toJSON(ruleMap));
                return where;
            }

            List<OperDuccDataIsolationConfig.DataIsolationRules> rules = ruleMap.get(mappedStatementId);
            if(CollectionUtils.isEmpty(rules)){
                log.info("dataIsolation, rule is null");
                return where;
            }

            String sqlSegment = getSqlSegment(dataIsolationConfig, rules);

            if(StringUtils.isBlank(sqlSegment)){
                log.info("dataIsolation, sqlSegment is null, rule = {}", JSON.toJSON(rules));
                return where;
            }

            Expression sqlSegmentExpression = CCJSqlParserUtil.parseCondExpression(sqlSegment);
            return new AndExpression(where, sqlSegmentExpression);
        } catch (Exception e){
            log.error("JdiIscDataPermissionHandler.getSqlSegment, req = {}, result = {}", whereStr, mappedStatementId, e);
            Profiler.functionError(callerInfo);
        } finally {
            log.error("JdiIscDataPermissionHandler.getSqlSegment, req = {}, result = {}", whereStr, mappedStatementId);
            Profiler.registerInfoEnd(callerInfo);
        }
        return where;
    }
}
