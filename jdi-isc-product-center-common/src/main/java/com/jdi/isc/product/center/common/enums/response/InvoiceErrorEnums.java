package com.jdi.isc.product.center.common.enums.response;

import com.jdi.isc.library.i18n.enums.http.ResponseEnums;

public enum InvoiceErrorEnums implements ResponseEnums {
    /**
     * 批量开票 erp 查询绑定 客户所属国家
     */
    NO_COUNTRY("2000050001", "未找到该账号可用的国家权限"),

    /**
     * 批量开票 erp 查询绑定 客户所属国家
     */
    MULTI_COUNTRY("2000050002", "存在多个国家权限，请先缩小权限范围"),
    /**
     * 红冲发票返回异常
     */
    STATUS_ERROR("**********", "发票状态不正确"),

    /**
     * 待审批申请已存在，不可重复创建
     */
    WAITING_APPROVAL_EXISTS("**********", "待审批申请已存在，不可重复创建"),
    /**
     * 订单状态或者订单所属国家错误
     */
    ORDER_STATUS_OR_COUNTRY_ERROR("**********", "订单状态或者订单所属国家错误"),
    /**
     * 开票订单只能是相同下单账号
     */
    SAME_ACCOUNT_REQUIRED("**********", "开票订单只能是相同下单账号"),
    /**
     * 特殊渠道订单，不支持通用开票功能
     */
    SPECIAL_CHANNEL_UNSUPPORTED("**********", "特殊渠道订单，不支持通用开票功能"),
    /**
     * %s,CST未填写，请补充后提交开票
     */
    CST_REQUIRED_WITH_PREFIX("**********", "%s,CST未填写，请补充后提交开票"),
    /**
     * %s,物料编码未填写，请补充后提交开票
     */
    MATERIAL_CODE_REQUIRED_WITH_PREFIX("**********", "%s,物料编码未填写，请补充后提交开票"),
    /**
     * %s,葡语名称未填写，请补充后提交开票
     */
    PORTUGUESE_NAME_REQUIRED_WITH_PREFIX("**********", "%s,葡语名称未填写，请补充后提交开票"),

    /**
     * 发票资质校验失败，客户与PIN关系不匹配
     */
    INVOICE_QUALIFICATION_VALIDATION_FAILED("**********", "该发票抬头客户不存在请先在CRM客户关系池中维护"),

    /**
     * 发票资质校验失败，客户与PIN关系不匹配
     */
    INVOICE_QUALIFICATION_VALIDATION_JSF_FAILED("2000050012", "获取CRM发票抬头与客户校验接口异常"),

    ;

    private final String code;
    private final String message;

    InvoiceErrorEnums(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
