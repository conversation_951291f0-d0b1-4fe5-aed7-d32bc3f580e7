package com.jdi.isc.product.center.common.enums.response;

import com.jdi.isc.library.i18n.enums.http.ResponseEnums;
import java.util.HashMap;
import java.util.Map;

public enum OrderErrorEnums implements ResponseEnums {

    /**
     * 没有可结算的商品错误码
     */
    SETTLEMENT_WARES_ERROR("2000040001","没有可结算的商品"),
    /**
     * 收货地址为空的错误码
     */
    SHIP_ADDRESS_NULL("2000040002","收货地址不能为空"),
    /**
     * 收货地址异常的错误码，表示收货地址存在问题，需要检查并修正。
     */
    ABNORMAL_RECEIVING_ADDRESS("2000040003","收货地址异常，请检查!"),
    /**
     * 表示重复提交订单的错误码。
     */
    REPEAT_ORDER("2000040004","请不要重复提交订单"),
    /**
     * 表示查询的订单不存在的错误码。
     */
    DATA_NOT_EXIST("2000040004","您所查询的订单不存在，请输入正确的订单号"),

    /**
     * 父单号查询无效的错误码，提示用户使用子单号进行查询。
     */
    PARENT_ORDER_INVALID("2000040005","父单号查询无效，请用子单号查询"),
    /**
     * WIOP售后请自助操作，客服暂不支持的错误码。
     */
    WIOP_AFTERSALES_SELF_SERVICE_ONLY("2000040006","WIOP售后请自助操作,客服暂不支持～"),
    /**
     * 有货先发拆单失败的错误码。
     */
    ORDER_SPLITTING_FAILED("2000040007","有货先发拆单失败"),
    /**
     * 采购单创建失败的错误码。
     */
    PURCHASE_ORDER_CREATION_FAILED ("2000040008","采购单创建失败"),

    /**
     * 操作频繁，请稍后再试的错误码。
     */
    REPEAT_ERROR("13003", "操作频繁，请稍后再试"),

    /**
     * 履约单拆分处理中，不能进行二次拆分的错误码。
     */
    SPLIT_IN_PREGRESS("13004", "履约单拆分处理中，不能进行二次拆分"),
    ;
    private final String code;
    private final String message;

    OrderErrorEnums(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 订单相关错误码映射表
     */
    private static final Map<String, OrderErrorEnums> ORDER_ERROR_MAP = new HashMap<>();

    static {
        ORDER_ERROR_MAP.put("2000040001", SETTLEMENT_WARES_ERROR);
        ORDER_ERROR_MAP.put("2000040002", SHIP_ADDRESS_NULL);
        ORDER_ERROR_MAP.put("2000040003", ABNORMAL_RECEIVING_ADDRESS);
        ORDER_ERROR_MAP.put("2000040004", REPEAT_ORDER);
        ORDER_ERROR_MAP.put("2000040005", PARENT_ORDER_INVALID);
        ORDER_ERROR_MAP.put("2000040006", WIOP_AFTERSALES_SELF_SERVICE_ONLY);
        ORDER_ERROR_MAP.put("13003", REPEAT_ERROR);
        ORDER_ERROR_MAP.put("13004", SPLIT_IN_PREGRESS);
    }

    /**
     * 根据错误码获取对应的订单错误枚举
     * @param code 错误码
     * @return 对应的订单错误枚举，如果未找到则返回null
     */
    public static OrderErrorEnums getByCode(String code) {
        return ORDER_ERROR_MAP.get(code);
    }

    /**
     * 检查错误码是否为订单相关错误码
     * @param code 错误码
     * @return 如果是订单相关错误码返回true，否则返回false
     */
    public static boolean isOrderError(String code) {
        return ORDER_ERROR_MAP.containsKey(code);
    }

    /**
     * 获取所有订单错误码
     * @return 订单错误码集合
     */
    public static Map<String, OrderErrorEnums> getOrderErrorMap() {
        return new HashMap<>(ORDER_ERROR_MAP);
    }
}
