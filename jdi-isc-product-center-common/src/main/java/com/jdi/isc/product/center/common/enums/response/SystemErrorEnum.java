package com.jdi.isc.product.center.common.enums.response;

/**
 * 系统级/JSF异常的统一错误码与文案模板
 */
public enum SystemErrorEnum {
	CALL_ERROR("70000", "%s调用异常，请稍后重试"),
	CONNECTION_ERROR("70001", "%s服务连接失败，请检查网络或联系管理员"),
	TIMEOUT_ERROR("70002", "%s服务暂时不可用，请稍后重试"),
	SERVICE_UNAVAILABLE("70003", "%s服务暂时不可用，请稍后重试"),
	NO_PROVIDER("70004", "%s服务暂时不可用，请稍后重试"),
	SYSTEM_ERROR("70005", "%s服务繁忙，请稍后重试"),
	UNKNOWN_ERROR("70999", "%s处理异常，请稍后重试");

	private final String code;
	private final String message;

	SystemErrorEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}

}
