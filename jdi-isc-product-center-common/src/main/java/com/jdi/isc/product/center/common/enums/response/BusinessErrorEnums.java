package com.jdi.isc.product.center.common.enums.response;

import com.jdi.isc.library.i18n.enums.http.ResponseEnums;
import java.util.HashMap;
import java.util.Map;

/**
 * 通用业务错误枚举
 * 包含系统级别的通用错误码和错误信息
 * <AUTHOR>
 */
public enum BusinessErrorEnums implements ResponseEnums {
    /**
     * 系统繁忙的错误码，提示用户稍后重试。
     */
    SYSTEM_BUSY("***********", "业务繁忙，请稍后重试"),
    /**
     * 操作失败的错误码。
     */
    COMMON_OPERATE_FAIL ("***********","操作失败"),

    /**
     * 表示参数不能为空的错误码。
     */
    COMMON_PARAM_NOT_ALLOW_EMPTY("***********", "参数不能为空"),
    /**
     * 表示参数不能为空的错误码，允许在错误消息中插入参数。
     */
    PARAM_NOT_ALLOW_EMPTY("***********", "参数不能为空，%s"),

    /**
     * 表示调用下游服务出现异常
     */
    DOWNSTREAM_SERVICE_ERROR("1000020010", "下游服务异常，请稍后再试"),

    /**
     * 表示加密操作失败的错误码。
     */
    ENCRYPTION_FAILED("1000020011", "加密失败"),

    /**
     * 表示解密操作失败的错误码。
     */
    DECRYPTION_FAILED("1000020012", "解密失败"),
    /**
     * 表示无数据变更的错误码，提示用户不需要进行操作。
     */
    NO_DATA_CHANGED("1000020013", "无数据变更，请勿操作"),

    /**
     * 表示数据不存在的错误码，提示用户确认数据是否正确。
     */
    DATA_NOT_EXIST("1000020014", "数据不存在，请检查后重试"),

    /**
     * 没有权限的错误码。
     */
    NO_PERMISSION("10000200041", "没有权限执行此操作"),

    /**
     * 元素长度超限，提示用户输入的元素长度超过限制。
     */
    ELEMENT_LENGTH_EXCEED("1000020015", "元素长度超过限制"),

    /**
     * 表示调用下游服务出现异常并且需要传入参数的错误码。
     */
    DOWNSTREAM_SERVICE_ERROR_PARAMS("1000020016", "%s服务繁忙，请稍后再试"),

    /**
     * 表示调用下游服务时返回空数据的错误码。
     */
    DOWNSTREAM_SERVICE_DATA_NULL("1000020017", "下游服务异常，返回信息为空"),

    /**
     * 表示调用下游服务时返回空数据的错误码。
     */
    DOWNSTREAM_SERVICE_CALLBACK_NULL("1000020018", "下游服务正常，返回信息为空"),

    ;

    private final String code;
    private final String message;

    BusinessErrorEnums(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 通用错误码映射表
     * 包含系统级别的通用错误码
     */
    private static final Map<String, BusinessErrorEnums> COMMON_ERROR_MAP = new HashMap<>();

    static {
        COMMON_ERROR_MAP.put("-1", COMMON_OPERATE_FAIL);
        COMMON_ERROR_MAP.put("1", COMMON_PARAM_NOT_ALLOW_EMPTY);
        COMMON_ERROR_MAP.put("2", NO_PERMISSION);
        COMMON_ERROR_MAP.put("3", DATA_NOT_EXIST);
        COMMON_ERROR_MAP.put("4", DOWNSTREAM_SERVICE_ERROR);
    }

    /**
     * 根据错误码获取对应的业务错误枚举
     * @param code 错误码
     * @return 对应的业务错误枚举，如果未找到则返回系统繁忙错误
     */
    public static BusinessErrorEnums getByCode(String code) {
        return COMMON_ERROR_MAP.getOrDefault(code, SYSTEM_BUSY);
    }

    /**
     * 检查错误码是否为通用错误码
     * @param code 错误码
     * @return 如果是通用错误码返回true，否则返回false
     */
    public static boolean isCommonError(String code) {
        return COMMON_ERROR_MAP.containsKey(code);
    }

    /**
     * 获取所有通用错误码
     * @return 通用错误码集合
     */
    public static Map<String, BusinessErrorEnums> getCommonErrorMap() {
        return new HashMap<>(COMMON_ERROR_MAP);
    }

    /**
     * 兼容旧版本的swap方法
     * @param code 错误码
     * @return 对应的业务错误枚举
     * @deprecated 建议使用 getByCode 方法替代
     */
    @Deprecated
    public static BusinessErrorEnums swap(String code) {
        return getByCode(code);
    }
}
