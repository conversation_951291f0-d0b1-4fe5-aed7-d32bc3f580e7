package com.jdi.isc.product.center.common.enums.response;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 权限字段所对应的测试数据组类型
 * @date 2025/6/5 15:58
 */
@AllArgsConstructor
@Getter
public enum JdiIscQueryDataTypeEnum {

    TEST_ERP("testErp"),

    TEST_PIN("testPin"),

    TEST_CLIENT_CODE("testClientCode"),

    TEST_SUPPLIER_CODE("testSupplierCode"),

    ;

    private String code;

}
