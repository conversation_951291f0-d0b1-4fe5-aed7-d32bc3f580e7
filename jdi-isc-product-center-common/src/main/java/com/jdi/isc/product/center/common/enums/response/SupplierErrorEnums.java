package com.jdi.isc.product.center.common.enums.response;

import com.jdi.isc.library.i18n.enums.http.ResponseEnums;

public enum SupplierErrorEnums implements ResponseEnums {


    /**
     * 账号异常，禁止操作
     */
    ACCOUNT_EXCEPTION("***********", "账号未审核通过，禁止操作"),
    /**
     * 供应商不存在
     */
    SUPPLIER_NOT_FOUND("***********", "供应商不存在");

    ;

    private final String code;
    private final String message;

    SupplierErrorEnums(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
