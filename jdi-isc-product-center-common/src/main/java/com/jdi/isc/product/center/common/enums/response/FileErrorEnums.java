package com.jdi.isc.product.center.common.enums.response;

import com.jdi.isc.library.i18n.enums.http.ResponseEnums;
import lombok.Getter;

@Getter
public enum FileErrorEnums implements ResponseEnums {

    /**
     * 表示模板不存在的错误码。
     */
    TEMPLATE_NOT_FOUND("4000010010", "该模板不存在"),
    /**
     * 表示模板下载失败的错误码。
     */
    FILE_TEMPLATE_DOWNLOAD_FAIL("4000050011", "模板下载失败"),
    /**
     * 表示上传的文件格式不正确的错误码，正确的文件格式为 *.xlsx。
     */
    FILE_INVALID_FILE("4000050012", "无效文件,正确的文件格式为 *.xlsx"),
    /**
     * 表示上传的文件大小超过限制的错误码。
     */
    FILE_TOO_LARGE("4000050013", "文件过大"),
    /**
     * 表示上传的文件大小超过限制的错误码，提示用户限制文件大小。
     */
    FILE_TOO_LARGE_LIMIT("4000050014", "文件过大,请限制到%s以内"),
    /**
     * 表示文件解析失败的错误码。
     */
    FILE_PARSING_FAIL("4000050015", "文件解析异常"),
    /**
     * 数据量限制错误码，提示用户控制数据量在指定范围内。
     */
    FILE_DATA_LIMIT("4000050016", "数据量请控制在%s条"),
    ;
    private final String code;
    private final String message;

    FileErrorEnums(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
