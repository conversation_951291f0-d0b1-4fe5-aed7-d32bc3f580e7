package com.jdi.isc.product.center.common.costants;

/**
    * @description: i18n提示词
    * @author: zhangjin176
    * @date: 2025/5/6 15:03
    * @version:
    **/
public class I18nConstant {
    // 类目级别不能为空的提示信息
    public static final String CATEGORY_LEVEL = "类目级别";
    /**
     * 表示失效状态的常量。
     */
    public static final String FAILURE_KEY = "(失效)";
    /**
     * 供应商简码的常量。
     */
    public static final String SUPPLIER_CODE = "供应商简码";

    /**
     * 定义商品唯一标识符的国际化常量。
     */
    public static final String SKU_ID = "skuId";
    /**
     * 京东商品唯一标识符的国际化常量。
     */
    public static final String JD_SKU_ID = "JdSkuId";
}
