package com.jdi.isc.product.center.common.enums.response;

import com.jdi.isc.library.i18n.enums.http.ResponseEnums;

public enum LoginErrorEnums implements ResponseEnums {


    /**
     * 账号被锁
     */
    ACCOUNT_LOCKED( "***********", "请求频繁,账号已禁用,请稍后重试！"),

    /**
     * 登录密码为空
     */
    ACCOUNT_PASSWORD_ERROR( "***********", "用户名或密码错误，请重新输入。"),


    /**
     * 登陆权限不足的错误码。
     */
    INSUFFICIENT_LOGIN_PERMISSION("**********", "登陆权限不足"),

    ;

    private final String code;
    private final String message;

    LoginErrorEnums(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
