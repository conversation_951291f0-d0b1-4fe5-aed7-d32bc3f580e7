package com.jdi.isc.message.broker.service.msg.wimp;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.binlog.client.WaveEntry;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.rpc.price.IscProductSoaFulfillmentRateRpcService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.common.enums.CustomerSkuDataSourceTypeEnums;
import com.jdi.isc.product.soa.price.api.price.req.FulfillmentRateChangeReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 履约费率变更消息
 * <AUTHOR>
 * @Date 2025/3/5
 */
@Slf4j
@Component
public class FulfillmentRateBinLakeListener extends AbsMsgTool<FulfillmentRateChangeReqVO> {

    @Resource
    private IscProductSoaFulfillmentRateRpcService iscProductSoaFulfillmentRateRpcService;

    @JmqListener(id = "iscProductSoaJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.fulfillmentRate}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<ChangeTableEventMsg> eventMsgList = parse(messages);
        try {
            log.info("FulfillmentRateBinLakeListener.onMessage req:{}" , JSON.toJSONString(eventMsgList));
            List<FulfillmentRateChangeReqVO> target = filterAndTransform(eventMsgList);
            iscProductSoaFulfillmentRateRpcService.rateChangeToPriceRefresh(target);
        }catch (Exception e){
            throw new BinlakeConsumeException("FulfillmentRateBinLakeListener.onMessage error",e);
        }
    }

    private List<FulfillmentRateChangeReqVO> filterAndTransform(List<ChangeTableEventMsg> eventMsgList) {
        List<FulfillmentRateChangeReqVO> fulfillmentRateChangeList = Lists.newArrayListWithExpectedSize(eventMsgList.size());
        for (ChangeTableEventMsg eventMsg : eventMsgList) {
            if (WaveEntry.EventType.DELETE.equals(eventMsg.getChangeType())) {
                continue;
            }
            // 修改字段不包含履约费率字段，则跳过
            if (!eventMsg.getChangeFields().containsKey("coefficient_value") && !eventMsg.getChangeFields().containsKey("volume_value")
                    && !eventMsg.getChangeFields().containsKey("fixed_fulfillment_cost") && !eventMsg.getChangeFields().containsKey("yn")) {
                continue;
            }
            String sourceCountryCode = eventMsg.getRowAfter().get("source_country_code");
            String targetCountryCode = eventMsg.getRowAfter().get("target_country_code");

            String firstCatId = eventMsg.getRowAfter().get("first_jd_cat_id");
            String secondCatId = eventMsg.getRowAfter().get("second_jd_cat_id");
            String thirdCatId = eventMsg.getRowAfter().get("third_jd_cat_id");
            String lastCatId = eventMsg.getRowAfter().get("last_jd_cat_id");

//            String firstCatId = eventMsg.getRowAfter().get("first_cat_id");
//            String secondCatId = eventMsg.getRowAfter().get("second_cat_id");
//            String thirdCatId = eventMsg.getRowAfter().get("third_cat_id");
//            String lastCatId = eventMsg.getRowAfter().get("last_cat_id");

            String skuId = eventMsg.getRowAfter().get("sku_id");
            String isWarehouseProduct = eventMsg.getRowAfter().get("is_warehouse_product");
            String updater = eventMsg.getRowAfter().get("updater");
            String updateTime = eventMsg.getRowAfter().get("update_time");
            FulfillmentRateChangeReqVO res = FulfillmentRateChangeReqVO.builder()
                    .sourceCountryCode(sourceCountryCode)
                    .targetCountryCode(targetCountryCode)
                    .firstCatId(StringUtils.isNotBlank(firstCatId)?Long.valueOf(firstCatId):null)
                    .secondCatId(StringUtils.isNotBlank(secondCatId)?Long.valueOf(secondCatId):null)
                    .thirdCatId(StringUtils.isNotBlank(thirdCatId)?Long.valueOf(thirdCatId):null)
                    .lastCatId(StringUtils.isNotBlank(lastCatId)?Long.valueOf(lastCatId):null)
                    .skuId(StringUtils.isNotBlank(skuId)?Long.valueOf(skuId):null)
                    .isWareHouseProduct(Integer.valueOf(isWarehouseProduct))
                    .updater(updater)
                    .updateTime(updateTime)
                    .dataStatusSource(CustomerSkuDataSourceTypeEnums.FULFILLMENT_RATE.getCode())
                    .build();
            fulfillmentRateChangeList.add(res);
        }
        return fulfillmentRateChangeList;
    }
}
