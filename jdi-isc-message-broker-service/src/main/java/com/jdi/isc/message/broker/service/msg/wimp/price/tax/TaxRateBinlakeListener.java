package com.jdi.isc.message.broker.service.msg.wimp.price.tax;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.jd.binlog.client.WaveEntry;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.domain.customer.CustomerVO;
import com.jdi.isc.message.broker.domain.msg.MkuPriceChangeMsg;
import com.jdi.isc.message.broker.domain.msg.po.OpenMsgPO;
import com.jdi.isc.message.broker.rpc.curreny.IscProductSoaCountryRpcApiService;
import com.jdi.isc.message.broker.rpc.price.IscProductSoaSalePriceReadRpcService;
import com.jdi.isc.message.broker.rpc.suport.AlertHelper;
import com.jdi.isc.message.broker.service.manage.customer.CustomerManageService;
import com.jdi.isc.message.broker.service.manage.msg.OpenMsgManageService;
import com.jdi.isc.message.broker.service.manage.price.PricePushManagerService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.price.api.salePrice.req.SalePriceReqDTO;
import com.jdi.isc.product.soa.price.api.salePrice.res.SalePriceResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * jdi_isc_tax_rate_sharding
 * 监听vip价中的税率变更
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TaxRateBinlakeListener extends AbsMsgTool<MkuPriceChangeMsg> {

    @Resource
    private OpenMsgManageService openMsgManageService;
    @Resource
    private PricePushManagerService pricePushManagerService;
    @Resource
    private IscProductSoaSalePriceReadRpcService iscProductSoaSalePriceReadRpcService;
    @Resource
    private CustomerManageService customerManageService;

    @Resource
    private IscProductSoaCountryRpcApiService iscProductSoaCountryRpcApiService;
    /**
     * On message.
     *
     * @param messages the messages
     * @throws Exception the exception
     */
    @JmqListener(id = "iscIntlJmq4Consumer", topics = {"${topic.jmq4.consumer.isc.taxRateMsg}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<ChangeTableEventMsg> eventMsgList = parse(messages);
        try {
            log.info("TaxRateBinlakeListener.onMessage req:{}", JSON.toJSONString(eventMsgList));
            List<OpenMsgPO> target = filterAndTransform(eventMsgList);
            if (CollectionUtils.isNotEmpty(target)) {
                openMsgManageService.batchAdd(target);
            }
        } catch (Exception e) {
            throw new BinlakeConsumeException("TaxRateBinlakeListener.onMessage error", e);
        } finally {
            log.info("TaxRateBinlakeListener.onMessage end");
        }

    }

    private static final Set<String> dbFields = Sets.newHashSet(/*"country_code", "client_code",*/ "hs_code", "key_type", "key_id", "tax_code", "tax_rate", "client_type");

    /**
     * 消息过滤及转化
     */
    private List<OpenMsgPO> filterAndTransform(List<ChangeTableEventMsg> eventMsgList) {
        List<OpenMsgPO> result = new ArrayList<>();
        Set<String> skuIds = Sets.newHashSet();
        for (ChangeTableEventMsg msg : eventMsgList) {
            if (WaveEntry.EventType.DELETE.equals(msg.getChangeType())) {
                continue;
            }

            Integer keyType = msg.getAfterValue("key_type", Integer.class);
            String skuId = msg.getRowAfter().get("key_id");

            if (keyType == null || keyType != 1) {
                log.info("CustomerSkuPriceBinlakeListener.filterAndTransform 客户编码或keyType为空, 跳过推送价格数据, skuId:{}, keyType:{}", skuId, keyType);
                continue;
            }

            if (skuIds.contains(skuId)) {
                continue;
            }

            String clientCode = msg.getRowAfter().get("client_code");

            CustomerVO client = customerManageService.detail(clientCode);

            if (client == null) {
                log.info("PricePushManagerService.filterAndTransform 客户{} 不存在, 跳过推送价格数据", clientCode);
                return null;
            }

            String currencyCode = iscProductSoaCountryRpcApiService.getCurrencyNameByCurrencyCode(client.getCountry());

            if (currencyCode == null) {
                log.warn("未找到币种. clientCode:{}", clientCode);
                AlertHelper.p0("未找到币种", clientCode, client.getCountry());
                continue;
            }

            SalePriceReqDTO req = new SalePriceReqDTO();
            req.setSkuId(Long.valueOf(skuId));
            req.setClientCode(clientCode);
            req.setCurrencyCode(currencyCode);
            DataResponse<SalePriceResDTO> res = iscProductSoaSalePriceReadRpcService.getNakedSalePrice(req);
            if (res == null || !res.getSuccess()) {
                log.error("PricePushManagerService.filterAndTransform 获取价格失败, req={}", req);
                throw new BinlakeConsumeException("PricePushManagerService.filterAndTransform 获取价格失败, skuId:" + skuId);
            }

            if (res.getData() == null) {
                log.info("无销价无需推送, skuId:{}， country={}", skuId, client.getCountry());
                continue;
            }

            OpenMsgPO openMsgPO = pricePushManagerService.buildPushData(msg, dbFields, skuId);

            if (openMsgPO != null) {
                skuIds.add(skuId);
                result.add(openMsgPO);
            }

//            long updateDate = Long.parseLong(msg.getRowAfter().get("update_time"));
//            CustomerVO client = customerManageService.detail(clientCode);
//            Map<String, String> changeFields = msg.getChangeFields();
//
//            if (MapUtils.isEmpty(changeFields)) {
//                log.info("no changeFields, 跳过推送价格数据");
//                continue;
//            }
//
//            long count = dbFields.stream().filter(changeFields::containsKey).count();
//
//            if (count < 1) {
//                log.info("CustomerSkuPriceBinlakeListener.filterAndTransform 价格变更字段为空, 跳过推送价格数据");
//                continue;
//            }
//
//            if (client != null && client.getOpenSubscribeFlag()) {
//                //查看sku对应mku客户是否绑定过,绑了才推送
//                CustomerMkuPricePO customerMkuPrice = customerMkuPriceService.getOne(Long.valueOf(skuId), clientCode);
//                if (customerMkuPrice == null) {
//                    log.info("CustomerSkuPriceBinlakeListener.filterAndTransform 客户{} sku{} 不存在mku绑定关系,忽略客制化价格消息", clientCode, skuId);
//                } else {
//                    result.add(new OpenMsgPO(clientCode, MsgTypeEnum.PRICE.getCode(), JSON.toJSONString(new MkuPriceChangeMsg(customerMkuPrice.getMkuId())), updater, new Date(updateDate)));
//                }
//            }
        }
        return result;
    }

}
