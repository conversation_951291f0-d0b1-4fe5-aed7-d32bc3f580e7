package com.jdi.isc.message.broker.service.msg.wimp.price.salestatus;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.binlog.client.WaveEntry;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.domain.msg.MkuPriceChangeMsg;
import com.jdi.isc.message.broker.rpc.price.IscCountryAgreementPriceRpcService;
import com.jdi.isc.message.broker.rpc.utils.ExceptionUtil;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.price.req.PriceAvailableSaleStatusReqDTO;
import com.jdi.isc.product.soa.api.price.res.PriceAvailableSaleStatusResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;


/**
 * 协议价-可售状态更新.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AgreementPriceSaleStatusBinlakeListener extends AbsMsgTool<MkuPriceChangeMsg> {

    @Resource
    private IscCountryAgreementPriceRpcService iscCountryAgreementPriceRpcService;


    /**
     * 监听事件类型
     */
    private final List<WaveEntry.EventType> eventTypes = Lists.newArrayList(WaveEntry.EventType.INSERT, WaveEntry.EventType.UPDATE);

    /**
     * On message.
     *
     * @param messages the messages
     * @throws Exception the exception
     */
    @JmqListener(id = "iscIntlJmq4Consumer", topics = {"${topic.jmq4.consumer.isc.agreementPriceMsg}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<ChangeTableEventMsg> eventMsgList = parse(messages);
        try {
            log.info("AgreementPriceSaleStatusBinlakeListener.onMessage req:{}", JSON.toJSONString(eventMsgList));
            List<PriceAvailableSaleStatusReqDTO> target = filterAndTransform(eventMsgList);
            if (CollectionUtils.isNotEmpty(target)) {
                PriceAvailableSaleStatusResDTO res = iscCountryAgreementPriceRpcService.updateAvailableSaleStatus(target);
                log.info("AgreementPriceSaleStatusBinlakeListener.onMessage req:{}, res={}", JSON.toJSONString(eventMsgList), res);
            } else {
                log.info("AgreementPriceSaleStatusBinlakeListener, target is empty");
            }
        } catch (Exception e) {
            log.error("AgreementPriceSaleStatusBinlakeListener.onMessage eventMsgList:{}, message={}", ExceptionUtil.getMessage(e, "更新VIP价可售状态失败"), JSON.toJSONString(eventMsgList), e);
            throw new BinlakeConsumeException("更新协议价可售状态失败");
        }
    }

    private List<PriceAvailableSaleStatusReqDTO> filterAndTransform(List<ChangeTableEventMsg> eventMsgList) {

        List<PriceAvailableSaleStatusReqDTO> result = Lists.newArrayList();

        for (ChangeTableEventMsg msg : eventMsgList) {
            Set<String> changeKey = msg.getChangeFields().keySet();
            //忽略非更新消息
            if (!eventTypes.contains(msg.getChangeType()) || CollectionUtils.isEmpty(changeKey)) {
                continue;
            }

            Long yn = msg.getAfterValue("yn", Long.class);

            if (yn == 0) {
                continue;
            }

            // id
            Long id = msg.getAfterValue("id", Long.class);
            // 操作人
            String updater = msg.getAfterValue("updater", String.class);

            if (!changeKey.contains("unsellable_threshold") && !changeKey.contains("country_cost_price") && !changeKey.contains("agreement_price")) {
                log.info("协议价，不可售阈值、成本价、协议价均未发生变更，不进行重算可售状态，id={}", id);
                continue;
            }

            PriceAvailableSaleStatusReqDTO item = new PriceAvailableSaleStatusReqDTO();
            item.setId(id);
            item.setPin(updater);
            item.setUpdater(updater);
            if (changeKey.contains("country_cost_price")) {
                item.setUpdateCustomerAvailableSaleStatus(true);
            }
            result.add(item);
        }
        return result;
    }

    public static void main(String[] args) {
//        String json = "[{\"changeFields\":{\"update_time\":\"1752214081921\",\"unsellable_threshold\":\"0.2000\",\"unsellable_threshold_time\":\"1752214081924\",\"updater\":\"liuzhaoming.10\"},\"changeType\":\"UPDATE\",\"dataBase\":\"share11_split2\",\"rowAfter\":{\"id\":\"11801\",\"biz_no\":\"PRICE250420195100001\",\"mku_id\":\"50000000059\",\"sku_id\":\"80000000079\",\"source_country_code\":\"BR\",\"target_country_code\":\"BR\",\"last_cat_id\":\"81\",\"jd_cat_id\":\"17563\",\"brand_id\":\"8000\",\"currency\":\"BRL\",\"agreement_price\":\"200.0\",\"agreement_mark\":\"精确方式(两位小数，进位),精确后115.24=精确前115.23333333;\\n国家协议价115.23333333=国家成本价103.71/[(1-协议价率0.1)=0.9];\\n精确方式(两位小数，进位),精确后103.71=精确前103.71;\\n国家成本价103.71=(本本未税采购价123+采购价IPI税值1.55)*汇率0.8326;\\n\",\"country_cost_price\":\"100.0\",\"cost_mark\":\"精确方式(两位小数，进位),精确后103.71=精确前103.71;\\n国家成本价103.71=(本本未税采购价123+采购价IPI税值1.55)*汇率0.8326;\\n\",\"unsellable_threshold\":\"0.2000\",\"unsellable_threshold_time\":\"1752214081924\",\"country_mku_pool_status\":\"0\",\"customer_mku_pool_status\":\"INVALID\",\"creator\":\"System\",\"updater\":\"liuzhaoming.10\",\"create_time\":\"1745149897652\",\"update_time\":\"1752214081921\",\"yn\":\"1\"},\"rowBefore\":{\"id\":\"11801\",\"biz_no\":\"PRICE250420195100001\",\"mku_id\":\"50000000059\",\"sku_id\":\"80000000079\",\"source_country_code\":\"BR\",\"target_country_code\":\"BR\",\"last_cat_id\":\"81\",\"jd_cat_id\":\"17563\",\"brand_id\":\"8000\",\"currency\":\"BRL\",\"agreement_price\":\"200.0\",\"agreement_mark\":\"精确方式(两位小数，进位),精确后115.24=精确前115.23333333;\\n国家协议价115.23333333=国家成本价103.71/[(1-协议价率0.1)=0.9];\\n精确方式(两位小数，进位),精确后103.71=精确前103.71;\\n国家成本价103.71=(本本未税采购价123+采购价IPI税值1.55)*汇率0.8326;\\n\",\"country_cost_price\":\"100.0\",\"cost_mark\":\"精确方式(两位小数，进位),精确后103.71=精确前103.71;\\n国家成本价103.71=(本本未税采购价123+采购价IPI税值1.55)*汇率0.8326;\\n\",\"unsellable_threshold\":\"0.1000\",\"unsellable_threshold_time\":\"1752151923493\",\"country_mku_pool_status\":\"0\",\"customer_mku_pool_status\":\"INVALID\",\"creator\":\"System\",\"updater\":\"wuzhonghai1\",\"create_time\":\"1745149897652\",\"update_time\":\"1752151923493\",\"yn\":\"1\"},\"tableName\":\"jdi_isc_country_agreement_price_sharding\"},{\"changeFields\":{\"update_time\":\"1752214081951\",\"unsellable_threshold\":\"0.5000\",\"unsellable_threshold_time\":\"1752214081951\",\"updater\":\"liuzhaoming.10\"},\"changeType\":\"UPDATE\",\"dataBase\":\"share11_split2\",\"rowAfter\":{\"id\":\"13003\",\"biz_no\":\"PRICE250512215400003\",\"mku_id\":\"50000000118\",\"sku_id\":\"80000000126\",\"source_country_code\":\"BR\",\"target_country_code\":\"BR\",\"last_cat_id\":\"17563\",\"jd_cat_id\":\"17563\",\"brand_id\":\"8000\",\"currency\":\"BRL\",\"agreement_price\":\"100.0\",\"agreement_mark\":\"zhaowei67更新uDFnyS3iUOBc3D1EP1XP客制化价格，同步修改巴西国家协议价\",\"country_cost_price\":\"100.0\",\"cost_mark\":\"精确方式(两位小数，进位),精确后607.89=精确前607.89;\\n国家成本价607.89=(本本未税采购价625.8+采购价IPI税值104.3)*汇率0.8326;\\n\",\"unsellable_threshold\":\"0.5000\",\"unsellable_threshold_time\":\"1752214081951\",\"country_mku_pool_status\":\"0\",\"customer_mku_pool_status\":\"INVALID\",\"agreement_update_time\":\"1752070409907\",\"creator\":\"System\",\"updater\":\"liuzhaoming.10\",\"create_time\":\"1747058096847\",\"update_time\":\"1752214081951\",\"yn\":\"1\"},\"rowBefore\":{\"id\":\"13003\",\"biz_no\":\"PRICE250512215400003\",\"mku_id\":\"50000000118\",\"sku_id\":\"80000000126\",\"source_country_code\":\"BR\",\"target_country_code\":\"BR\",\"last_cat_id\":\"17563\",\"jd_cat_id\":\"17563\",\"brand_id\":\"8000\",\"currency\":\"BRL\",\"agreement_price\":\"100.0\",\"agreement_mark\":\"zhaowei67更新uDFnyS3iUOBc3D1EP1XP客制化价格，同步修改巴西国家协议价\",\"country_cost_price\":\"100.0\",\"cost_mark\":\"精确方式(两位小数，进位),精确后607.89=精确前607.89;\\n国家成本价607.89=(本本未税采购价625.8+采购价IPI税值104.3)*汇率0.8326;\\n\",\"unsellable_threshold\":\"0.0\",\"unsellable_threshold_time\":\"1752143720106\",\"country_mku_pool_status\":\"0\",\"customer_mku_pool_status\":\"INVALID\",\"agreement_update_time\":\"1752070409907\",\"creator\":\"System\",\"updater\":\"yuxinyue10\",\"create_time\":\"1747058096847\",\"update_time\":\"1752143720103\",\"yn\":\"1\"},\"tableName\":\"jdi_isc_country_agreement_price_sharding\"},{\"changeFields\":{\"update_time\":\"1752214081968\",\"unsellable_threshold\":\"0.8000\",\"unsellable_threshold_time\":\"1752214081968\",\"updater\":\"liuzhaoming.10\"},\"changeType\":\"UPDATE\",\"dataBase\":\"share11_split2\",\"rowAfter\":{\"id\":\"32000\",\"biz_no\":\"BR80000000166\",\"mku_id\":\"50000000146\",\"sku_id\":\"80000000166\",\"source_country_code\":\"CN\",\"target_country_code\":\"BR\",\"last_cat_id\":\"21398\",\"jd_cat_id\":\"21398\",\"brand_id\":\"8005\",\"currency\":\"BRL\",\"agreement_price\":\"200.0\",\"agreement_mark\":\"国家成本价为空，无法计算出来国家协议价\",\"country_cost_price\":\"100.0\",\"cost_mark\":\"跨境品履约费率为空\",\"unsellable_threshold\":\"0.8000\",\"unsellable_threshold_time\":\"1752214081968\",\"country_mku_pool_status\":\"0\",\"customer_mku_pool_status\":\"INVALID\",\"suggest_agreement_mark\":\"国家成本价为空，无法计算出来建议国家协议价\",\"agreement_update_time\":\"1751372769680\",\"creator\":\"System\",\"updater\":\"liuzhaoming.10\",\"create_time\":\"1751362918239\",\"update_time\":\"1752214081968\",\"yn\":\"1\"},\"rowBefore\":{\"id\":\"32000\",\"biz_no\":\"BR80000000166\",\"mku_id\":\"50000000146\",\"sku_id\":\"80000000166\",\"source_country_code\":\"CN\",\"target_country_code\":\"BR\",\"last_cat_id\":\"21398\",\"jd_cat_id\":\"21398\",\"brand_id\":\"8005\",\"currency\":\"BRL\",\"agreement_price\":\"200.0\",\"agreement_mark\":\"国家成本价为空，无法计算出来国家协议价\",\"country_cost_price\":\"100.0\",\"cost_mark\":\"跨境品履约费率为空\",\"country_mku_pool_status\":\"0\",\"customer_mku_pool_status\":\"INVALID\",\"suggest_agreement_mark\":\"国家成本价为空，无法计算出来建议国家协议价\",\"agreement_update_time\":\"1751372769680\",\"creator\":\"System\",\"updater\":\"System\",\"create_time\":\"1751362918239\",\"update_time\":\"1751372769680\",\"yn\":\"1\"},\"tableName\":\"jdi_isc_country_agreement_price_sharding\"}]";
//
//        List<ChangeTableEventMsg> changeTableEventMsgs = JSONObject.parseArray(json, ChangeTableEventMsg.class);
//
//        List<PriceAvailableSaleStatusReqDTO> target = new AgreementPriceSaleStatusBinlakeListener().filterAndTransform(changeTableEventMsgs);
    }

}
