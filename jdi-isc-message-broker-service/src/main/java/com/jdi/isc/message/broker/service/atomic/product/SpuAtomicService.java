package com.jdi.isc.message.broker.service.atomic.product;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.jdi.isc.message.broker.domain.enums.YnEnum;
import com.jdi.isc.message.broker.domain.spu.SpuPO;
import com.jdi.isc.message.broker.repository.jed.mapper.spu.SpuBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【jdi_isc_spu_sharding(spu基础表)】的数据库操作Service实现
 * @createDate 2023-11-25 15:39:30
 */
@Service
public class SpuAtomicService extends ServiceImpl<SpuBaseMapper, SpuPO> {

    public Map<Long,SpuPO> batchQuerySpuPoMap(List<Long> spuIdList){
        if (CollectionUtils.isEmpty(spuIdList)) {
            return Maps.newHashMap();
        }

        LambdaQueryWrapper<SpuPO> queryWrapper = Wrappers.lambdaQuery(SpuPO.class)
                .select(SpuPO::getSpuId, SpuPO::getBuyer, SpuPO::getVendorCode, SpuPO::getSystemCode)
                .in(SpuPO::getSpuId, spuIdList)
                .eq(SpuPO::getYn, YnEnum.YES.getCode());
        List<SpuPO> spuPOList = super.getBaseMapper().selectList(queryWrapper);

        return Optional.ofNullable(spuPOList)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(SpuPO::getSpuId, Function.identity()));
    }


    public SpuPO getSpuPo(Long spuId) {
        LambdaQueryWrapper<SpuPO> queryWrapper = Wrappers.lambdaQuery(SpuPO.class)
                .select(SpuPO::getSpuId, SpuPO::getBuyer, SpuPO::getVendorCode, SpuPO::getBrandId, SpuPO::getSpecification, SpuPO::getSystemCode)
                .eq(SpuPO::getSpuId, spuId)
                .eq(SpuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectOne(queryWrapper);
    }

    /**
     * 判断指定spuId的商品是否存在
     * @param spuId 商品SPU ID
     * @return true if exists, false otherwise
     */
    public boolean exist(Long spuId) {
        LambdaQueryWrapper<SpuPO> queryWrapper = Wrappers.lambdaQuery(SpuPO.class)
                .eq(SpuPO::getSpuId, spuId)
                .eq(SpuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().exists(queryWrapper);
    }
}




