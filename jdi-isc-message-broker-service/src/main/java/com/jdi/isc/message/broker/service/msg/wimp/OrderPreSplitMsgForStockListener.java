package com.jdi.isc.message.broker.service.msg.wimp;


import com.alibaba.fastjson.JSON;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.message.broker.common.constants.Constant;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.domain.msg.OrderSplitOpenMsg;
import com.jdi.isc.message.broker.domain.msg.order.OrderInfoVO;
import com.jdi.isc.message.broker.domain.msg.order.OrderWareVO;
import com.jdi.isc.message.broker.rpc.stock.IscStockRpcService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockSplitReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 国际订单预拆单消息监听
 * @Date 2025-03-13
 */
@Slf4j
@Component
public class OrderPreSplitMsgForStockListener extends AbsMsgTool<OrderSplitOpenMsg> {

    @Resource
    private IscStockRpcService iscStockRpcService;

    @LafValue("isc.message.broker.preOrder.orders")
    private Set<Long> orderIds;

    @JmqListener(id = "iscProductSoaJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.preOrderSplit}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<OrderSplitOpenMsg> msg = transform(messages);
        log.info("WImpPreOrderSplitMsgListener.onMessage req:{}" , JSON.toJSONString(msg));
        if (CollectionUtils.isEmpty(msg)) {
            log.error("WImpPreOrderSplitMsgListener 消息为空，自动过滤");
            return;
        }

        for(OrderSplitOpenMsg target : msg){
            try {
                // 只有一个子单跳过，未拆单，不处理
                if (Objects.isNull(target.getOrderInfo()) || CollectionUtils.isEmpty(target.getOrderInfo().getChildrenList())
                        || (CollectionUtils.isNotEmpty(target.getOrderInfo().getChildrenList()) && target.getOrderInfo().getChildrenList().size() == 1)){
                    log.warn("WImpPreOrderSplitMsgListener.onMessage target.getOrderInfo() is empty or only one, reqDTO={}", JSON.toJSONString(target));
                    continue;
                }

                OrderInfoVO orderInfo = target.getOrderInfo();
                if (orderIds.contains(orderInfo.getOrderId())) {
                    log.error("WImpPreOrderSplitMsgListener.onMessage 跳过有问题的订单ID[{}]，不处理",orderInfo.getOrderId());
                    continue;
                }

                // 多个子单，但是子单号相同时跳过
                List<OrderInfoVO> childrenList = target.getOrderInfo().getChildrenList();
                Set<Long> orderIdSet = childrenList.stream().map(OrderInfoVO::getOrderId).collect(Collectors.toSet());
                if (childrenList.size() > orderIdSet.size()) {
                    log.warn("WImpPreOrderSplitMsgListener.onMessage childrenList size is not equal orderIdSet size, reqDTO={}", JSON.toJSONString(target));
                    continue;
                }

                // 多个子单，但是子单会有商品为空的情况
                for (OrderInfoVO orderInfoVO : childrenList) {
                    if (CollectionUtils.isEmpty(orderInfoVO.getOrderWareList())) {
                        log.warn("WImpPreOrderSplitMsgListener.onMessage orderInfoVO.getOrderWareList() is empty, reqDTO={}", JSON.toJSONString(target));
                        throw new RuntimeException("WImpPreOrderSplitMsgListener 子单："+ orderInfoVO.getOrderId() + "没有商品信息 处理预拆单消息失败");
                    }
                }

                StockSplitReqDTO reqDTO = this.fillStockSplitReqDTO(target);
                if (CollectionUtils.isEmpty(reqDTO.getStockManageReqDTOList())) {
                    log.warn("WImpPreOrderSplitMsgListener.onMessage stockManageReqDTOList is empty, reqDTO={}", JSON.toJSONString(target));
                    continue;
                }
                boolean splitStock = iscStockRpcService.splitStock(reqDTO);
                if (!splitStock) {
                    log.error("WImpPreOrderSplitMsgListener.onMessage splitStock error, reqDTO={}", JSON.toJSONString(reqDTO));
                    throw new RuntimeException("WImpPreOrderSplitMsgListener 处理预拆单消息失败");
                }
            }catch (Exception e){
                throw new BinlakeConsumeException("WImpPreOrderSplitMsgListener.onMessage error",e);
            }
        }
    }

    private StockSplitReqDTO fillStockSplitReqDTO(OrderSplitOpenMsg msg){
        StockSplitReqDTO reqDTO = new StockSplitReqDTO();

        List<StockManageReqDTO> stockManageReqDTOList = Lists.newArrayList();
        if (Objects.nonNull(msg.getOrderInfo()) && CollectionUtils.isNotEmpty(msg.getOrderInfo().getChildrenList())){
            List<OrderInfoVO> childrenList = msg.getOrderInfo().getChildrenList();
            for (OrderInfoVO infoVO : childrenList) {
                if (CollectionUtils.isNotEmpty(infoVO.getOrderWareList())) {
                    StockManageReqDTO manageReqDTO = new StockManageReqDTO();
                    manageReqDTO.setCountryCode(msg.getCountryCode());
                    manageReqDTO.setOrderId(String.valueOf(infoVO.getParentOrderId()));
                    manageReqDTO.setBizNo(String.valueOf(infoVO.getOrderId()));
                    // 商品列表
                    List<StockItemManageReqDTO> itemList = this.fillStockSkuItemList(infoVO.getOrderWareList());
                    if (CollectionUtils.isEmpty(itemList)) {
                        continue;
                    }
                    manageReqDTO.setStockItem(itemList);
                    stockManageReqDTOList.add(manageReqDTO);
                }
            }
        }
        reqDTO.setStockManageReqDTOList(stockManageReqDTOList);
        return reqDTO;
    }

    private List<StockItemManageReqDTO> fillStockSkuItemList(List<OrderWareVO> orderWareList) {
        List<StockItemManageReqDTO> stockItemManageReqDTOList = Lists.newArrayList();
        for (OrderWareVO orderWareVO : orderWareList) {
            // 跨境品跳过，不处理
            if (CountryConstant.COUNTRY_ZH.equals(orderWareVO.getMkuCountryCode()) && StringUtils.isBlank(orderWareVO.getWarehouseId())) {
                continue;
            }

            StockItemManageReqDTO stockItemManageReqDTO = new StockItemManageReqDTO();
            stockItemManageReqDTO.setSkuId(orderWareVO.getSkuId());
            stockItemManageReqDTO.setNum(orderWareVO.getMkuNum());
            stockItemManageReqDTO.setWarehouseId(orderWareVO.getWarehouseId());
            stockItemManageReqDTO.setUpdater(Constant.SYSTEM +"-preOrderSplit");
            stockItemManageReqDTOList.add(stockItemManageReqDTO);
        }
        return stockItemManageReqDTOList;
    }
}
