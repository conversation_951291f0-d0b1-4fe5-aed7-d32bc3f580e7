package com.jdi.isc.message.broker.service.msg.es;

import com.alibaba.fastjson.JSON;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.isc.message.broker.domain.msg.MkuClientEsVO;
import com.jdi.isc.message.broker.domain.msg.MkuEsAttributeChangeMsg;
import com.jdi.isc.message.broker.service.manage.es.EsAttributeChangeManageService;
import com.jdi.isc.message.broker.service.manage.mku.MkuManageService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.common.enums.EsAttributeChangeTypeEnum;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuEsDetailReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.jdi.isc.message.broker.common.constants.UmpKeyConstant.BUSINESS_KEY_FRAME_WARNING;

/**
 * mkuES属性变更监听消息处理类
 * 可能的变更事件，例如：
 * ——1、库存有无标签变更处理
 * ——2、时效标签变更处理
 *
 * <AUTHOR>
 * @date 20250611
 */
@Slf4j
@Component
public class MkuEsAttributesChangeMsgListener extends AbsMsgTool<MkuEsAttributeChangeMsg> {


    @Resource
    private EsAttributeChangeManageService esAttributeChangeManageService;

    @Resource
    private MkuManageService mkuManageService;

    @Value("${spring.profiles.active}")
    private String envCode;

    @JmqListener(id = "iscIntlJmq4Consumer", topics = {"${topic.jmq4.consumer.isc.mkuEsChange}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<MkuEsAttributeChangeMsg> mkuEsAttributeChangeMsgList = transform(messages);
        try {
            log.info("MkuEsAttributesChangeMsgListener.onMessage req:{}", JSON.toJSONString(mkuEsAttributeChangeMsgList));
            if (CollectionUtils.isEmpty(mkuEsAttributeChangeMsgList)) {
                log.error("MkuEsAttributesChangeMsgListener.onMessage 消息为空！");
                return;
            }


            for (MkuEsAttributeChangeMsg mkuEsAttributeChangeMsg : mkuEsAttributeChangeMsgList) {
                if (!validateRequiredParam(mkuEsAttributeChangeMsg)) {
                    log.error("MkuEsAttributesChangeMsgListener.onMessage 必填参数为空！changMsg:{}", JSON.toJSONString(mkuEsAttributeChangeMsg));
                    continue;
                }

                //获取es数据
                MkuEsDetailReqApiDTO mkuClientDetailReqApiDTO = buildMkuClientDetailReqApiDTO(mkuEsAttributeChangeMsg);
                MkuClientEsVO mkuClientEsVO = mkuManageService.queryMkuEsByParam(mkuClientDetailReqApiDTO);
                if (mkuClientEsVO == null) {
                    log.warn("MkuEsAttributesChangeMsgListener queryMkuEsByParam empty, 商品在es中不存在！request:{}", JSON.toJSONString(mkuEsAttributeChangeMsg));
                    return;
                }

                //更新属性标签
                EsAttributeChangeTypeEnum esAttributeChangeType = EsAttributeChangeTypeEnum.forCode(mkuEsAttributeChangeMsg.getChangeType());
                switch (esAttributeChangeType) {
                    case STOCK_TAG_CHANGE:
                        esAttributeChangeManageService.handleEsStockMarkingProcess(mkuEsAttributeChangeMsg, mkuClientEsVO);
                        break;
                    case STOCK_PROMISE_TAG_CHANGE:
                        esAttributeChangeManageService.handleEsPromiseTimeMarkingProcess(mkuEsAttributeChangeMsg, mkuClientEsVO);
                        break;
                    default:
                        log.info("MkuEsAttributesChangeMsgListener.onMessage 非法类型:{}，暂无处理流程！", esAttributeChangeType.getCode());
                        //其他流程处理
                        break;
                }
            }

        } catch (Exception e) {
            log.error("StockChangeMsgListener.onMessage failed, messages:{}", JSON.toJSONString(messages));
            throw new RuntimeException("StockChangeMsgListener.onMessage error", e);
        }
    }

    /**
     * 验证必填参数是否为空
     *
     * @param mkuEsAttributeChangeMsg 包含需要验证的参数对象
     * @return 当所有必填参数都不为空时返回true，否则返回false
     */
    private boolean validateRequiredParam(MkuEsAttributeChangeMsg mkuEsAttributeChangeMsg) {
        EsAttributeChangeTypeEnum esAttributeChangeTypeEnum = EsAttributeChangeTypeEnum.forCode(mkuEsAttributeChangeMsg.getChangeType());
        if (Objects.isNull(esAttributeChangeTypeEnum)) {
            return false;
        }

        if (StringUtils.isNotEmpty(mkuEsAttributeChangeMsg.getClientCode())
                && mkuEsAttributeChangeMsg.getMkuId() != null &&
                StringUtils.isNotEmpty(mkuEsAttributeChangeMsg.getCountryCode())) {
            return true;
        }

        return false;
    }

    @NotNull
    private MkuEsDetailReqApiDTO buildMkuClientDetailReqApiDTO(MkuEsAttributeChangeMsg skuStockEsChangeMsg) {
        MkuEsDetailReqApiDTO mkuClientDetailReqApiDTO = new MkuEsDetailReqApiDTO();
        mkuClientDetailReqApiDTO.setClientCode(skuStockEsChangeMsg.getClientCode());
        mkuClientDetailReqApiDTO.setMkuId(skuStockEsChangeMsg.getMkuId());
        mkuClientDetailReqApiDTO.setCountry(skuStockEsChangeMsg.getCountryCode());
        return mkuClientDetailReqApiDTO;
    }

}
