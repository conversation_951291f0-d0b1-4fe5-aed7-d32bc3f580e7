package com.jdi.isc.message.broker.service.manage.mku.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.binlog.client.WaveEntry;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.domain.customer.CustomerVO;
import com.jdi.isc.message.broker.domain.mku.CustomerMkuPO;
import com.jdi.isc.message.broker.domain.mku.CustomerMkuPricePO;
import com.jdi.isc.message.broker.domain.msg.*;
import com.jdi.isc.message.broker.domain.sku.SkuPO;
import com.jdi.isc.message.broker.rpc.mku.RpcIscProductSoaMkuService;
import com.jdi.isc.message.broker.rpc.sku.IscProductSoaSkuWriteRpcService;
import com.jdi.isc.message.broker.rpc.suport.AlertHelper;
import com.jdi.isc.message.broker.service.adapter.MkuConvert;
import com.jdi.isc.message.broker.service.atomic.product.SkuAtomicService;
import com.jdi.isc.message.broker.service.atomic.product.SpuAtomicService;
import com.jdi.isc.message.broker.service.manage.customer.CustomerManageService;
import com.jdi.isc.message.broker.service.manage.mku.CustomerMkuManageService;
import com.jdi.isc.message.broker.service.manage.mku.CustomerMkuPriceService;
import com.jdi.isc.message.broker.service.manage.mku.MkuManageService;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.countryMku.biz.CountryMkuReqDTO;
import com.jdi.isc.product.soa.api.orderVerify.req.MkuVerifyReqDTO;
import com.jdi.isc.product.soa.api.spu.req.SpuUpdateApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MkuManageServiceImpl implements MkuManageService {

    @Resource
    private RpcIscProductSoaMkuService rpcIscProductSoaMkuService;
    @Resource
    private IscProductSoaSkuWriteRpcService iscProductSoaSkuWriteRpcService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private CustomerMkuPriceService customerMkuPriceService;
    @Resource
    private SpuAtomicService spuAtomicService;
    @Resource
    private CustomerMkuManageService customerMkuManageService;
    @Resource
    private CustomerManageService customerManageService;

    @Override
    public DataResponse<Boolean> createOrderOutVerifyInfo(List<OrderOutVerifyMsg> msgList) {
        List<MkuVerifyReqDTO> mkuVerifyReqDTOList = MkuConvert.INSTANCE.listMsg2ListDTO(msgList);
        DataResponse<Boolean> orderOutVerifyInfo = rpcIscProductSoaMkuService.createOrderOutVerifyInfo(mkuVerifyReqDTOList);
        return orderOutVerifyInfo;
    }

    @Override
    public DataResponse<Boolean> updateSpuSkuMku(SkuInfoMsg skuInfoMsg) {
        SpuUpdateApiDTO spuUpdateApiDTO =  MkuConvert.INSTANCE.msg2ApiDto(skuInfoMsg);
        DataResponse<Boolean> response = rpcIscProductSoaMkuService.updateSpuSkuMku(spuUpdateApiDTO);
        return response;
    }

    @Override
    public DataResponse<Boolean> mkuJoinCountryPool(List<MkuChangeMsg> mkuChangeMsgs) {
        if(CollectionUtils.isEmpty(mkuChangeMsgs)){
            log.error("MkuManageServiceImpl.mkuJoinCountryPool param is null.");
            return DataResponse.error("MkuManageServiceImpl.mkuJoinCountryPool param is null");
        }
        for(MkuChangeMsg mkuChangeMsg : mkuChangeMsgs){
            try{
                CountryMkuReqDTO countryMkuReqDTO = new CountryMkuReqDTO();
                countryMkuReqDTO.setMkuId(mkuChangeMsg.getSkuId());
                countryMkuReqDTO.setCountryCode(mkuChangeMsg.getTargetCountryCode());
                DataResponse<Boolean> booleanDataResponse = rpcIscProductSoaMkuService.mkuJoinCountryPool(countryMkuReqDTO);
            }catch (Exception e){
                log.error("MkuManageServiceImpl.mkuJoinCountryPool error",e);
            }
        }
        return DataResponse.success();
    }

    @Override
    public DataResponse<Boolean> jdSkuIdJoinCountryPool(List<SkuInfoMsg> skuInfoMsgs) {
        if(CollectionUtils.isEmpty(skuInfoMsgs)){
            log.error("MkuManageServiceImpl.jdSkuIdJoinCountryPool param is null");
            return DataResponse.error("MkuManageServiceImpl.jdSkuIdJoinCountryPool param is null");
        }
        for(SkuInfoMsg skuInfoMsg : skuInfoMsgs){
            try{
                CountryMkuReqDTO countryMkuReqDTO = new CountryMkuReqDTO();
                countryMkuReqDTO.setJdSkuId(skuInfoMsg.getJdSkuId());
                DataResponse<Boolean> booleanDataResponse = rpcIscProductSoaMkuService.jdSkuIdJoinCountryPool(countryMkuReqDTO);
            }catch (Exception e){
                log.error("MkuManageServiceImpl.mkuJoinCountryPool error",e);
            }
        }
        return DataResponse.success();
    }

    @Override
    public Boolean updateMkuFeatureTag(MkuFeatureTagVO reqVo){
        try {
            MkuFeatureTagDTO dto = MkuConvert.INSTANCE.convertMkuFeatureTagVO2Dto(reqVo);
            DataResponse<Boolean> response = rpcIscProductSoaMkuService.updateMkuFeatureTag(dto);
            log.error("MkuManageServiceImpl.updateMkuFeatureTag param:{},res:{}",JSONObject.toJSONString(reqVo), JSON.toJSONString(response));
            return response.getData();
        } catch (Exception e) {
            log.error("MkuManageServiceImpl.updateMkuFeatureTag param:{},e:",JSONObject.toJSONString(reqVo), e);
        }
        return false;
    }

    @Override
    public MkuClientEsVO queryMkuEsByParam(MkuEsDetailReqApiDTO reqVo) {
        MkuClientEsVO mkuClientEsVO = null;
        try {
            DataResponse<MkuEsClientDTO> response = rpcIscProductSoaMkuService.queryEsMkuByCondition(reqVo);
            if (response.getSuccess() && response.getData() != null) {
                log.info("MkuManageServiceImpl.queryMkuEsByParam param:{},res:{}", JSONObject.toJSONString(reqVo), JSON.toJSONString(response));
                MkuEsClientDTO mkuEsClientDTO = response.getData();
                mkuClientEsVO = MkuConvert.INSTANCE.convertMkuClientDto2EsVo(mkuEsClientDTO);
            }

            return mkuClientEsVO;
        } catch (Exception e) {
            log.error("MkuManageServiceImpl.queryMkuEsByParam param:{},e:", JSONObject.toJSONString(reqVo), e);
        }
        return mkuClientEsVO;
    }

    @Override
    public Boolean updateMkuEsStockTag(MkuEsStockTagReqDTO reqVo) {
        DataResponse<Boolean> response = null;
        try {
            response = rpcIscProductSoaMkuService.updateMkuEsStockTag(reqVo);
            return response.getData();
        } catch (Exception e) {
            log.error("MkuManageServiceImpl.updateMkuEsStockTag param:{},e:", JSONObject.toJSONString(reqVo), e);
        } finally {
            log.info("MkuManageServiceImpl.updateMkuEsStockTag param:{},res:{}", JSONObject.toJSONString(reqVo), JSON.toJSONString(response));
        }
        return false;
    }

    @Override
    public void updateSkuNcmCode(List<ChangeTableEventMsg> eventMsgList) {
        if (null == eventMsgList || eventMsgList.isEmpty()) {
            return;
        }

        try {
            for (ChangeTableEventMsg eventMsg : eventMsgList) {
                if (WaveEntry.EventType.INSERT.equals(eventMsg.getChangeType()) || WaveEntry.EventType.UPDATE.equals(eventMsg.getChangeType())) {
                    if ("0".equals(eventMsg.getRowAfter().get("yn"))) {
                        continue;
                    }

                    // 获取京东skuId
                    Long jdSkuId = eventMsg.getAfterValue("jd_sku_id", Long.class);
                    String sourceCountryCode = eventMsg.getAfterValueStr("source_country_code");
                    String updater = eventMsg.getAfterValueStr("updater");

                    log.info("插入sku表，jdSkuId:{}, sourceCountryCode:{}, updater:{}", jdSkuId, sourceCountryCode, updater);

                    if (!StringUtils.equals(CountryConstant.COUNTRY_ZH, sourceCountryCode)) {
                        continue;
                    }

                    if (jdSkuId == null) {
                        log.info("updateSkuNcmCode jdSkuId is null");
                        continue;
                    }

                    // 更新ncmCode
                    iscProductSoaSkuWriteRpcService.batchFixBrNcmCode(jdSkuId, updater);
                }
            }
        } catch (Exception e) {
            log.error("batchFixBrNcmCode, 出现异常.", e);
            AlertHelper.p0("监听binlake同步ncmCode失败", e.getMessage());
        }
    }

    @Override
    public Boolean updateMkuEsCommonPromiseTag(MkuPromiseTagDTO reqVo) {
        DataResponse<Boolean> response = null;
        try {
            response = rpcIscProductSoaMkuService.updateMkuEsPromiseTimeTag(reqVo);
            return response.getData();
        } catch (Exception e) {
            log.error("MkuManageServiceImpl.updateMkuEsCommonPromiseTag param:{},e:", JSONObject.toJSONString(reqVo), e);
        } finally {
            log.info("MkuManageServiceImpl.updateMkuEsCommonPromiseTag param:{},res:{}", JSONObject.toJSONString(reqVo), JSON.toJSONString(response));
        }
        return false;
    }

    @Override
    public List<MkuClientDetailReqApiDTO> fissionForCustomerMku(String clientCode, Long mkuId) {
        List<MkuClientDetailReqApiDTO> results = new ArrayList<>();
        CustomerMkuPO customerMkuPO = customerMkuManageService.getOne(clientCode, mkuId);
        if (customerMkuPO != null) {
            this.processCustomerMkuPO(customerMkuPO.getClientCode(),customerMkuPO.getMkuId(), results, "fissionForCustomerMku");
        } else {
            log.info("MkuManageServiceImpl.fissionForCustomer customerMkuPO is null");
            results.add(this.getMkuClientDetail(clientCode, mkuId));
        }
        return results;
    }

    @Override
    public List<MkuClientDetailReqApiDTO> fissionForSku(Long skuId) {
        List<MkuClientDetailReqApiDTO> results = new ArrayList<>();
        List<CustomerMkuPricePO> customerMkuPricePOS = customerMkuPriceService.listBySkuId(skuId);
        if (CollectionUtils.isEmpty(customerMkuPricePOS)) {
            return results;
        }
        for (CustomerMkuPricePO customerMkuPricePO : customerMkuPricePOS) {
            this.processCustomerMkuPO(customerMkuPricePO.getClientCode(),customerMkuPricePO.getMkuId(), results, "fissionForSku");
        }

        return results;
    }

    @Override
    public List<MkuClientDetailReqApiDTO> fissionForMku(Long mkuId) {
        List<MkuClientDetailReqApiDTO> results = new ArrayList<>();
        List<CustomerMkuPO> customerMkuList = customerMkuManageService.list(mkuId);
        if (CollectionUtils.isEmpty(customerMkuList)) {
            return results;
        }
        for (CustomerMkuPO customerMkuPO : customerMkuList) {
            this.processCustomerMkuPO(customerMkuPO.getClientCode(),customerMkuPO.getMkuId(), results, "fissionForMku");
        }
        return results;
    }

    /**
     * 公共处理逻辑抽取
     */
    private void processCustomerMkuPO(String clientCode,Long mkuId, List<MkuClientDetailReqApiDTO> results, String logPrefix) {
        if (StringUtils.isBlank(clientCode) || mkuId == null) {
            return;
        }
        CustomerVO client = customerManageService.detail(clientCode);
        if (client == null) {
            log.info("MkuManageServiceImpl.{} client is null, clientCode:{}", logPrefix, clientCode);
            return;
        }

        // 添加主明细
        results.add(this.getMkuClientDetail(clientCode, mkuId));

        CustomerMkuPricePO customerMkuPricePO = customerMkuPriceService.getOneByMkuId(mkuId, clientCode);
        if (customerMkuPricePO == null) {
            log.info("MkuManageServiceImpl.{} customerMkuPricePO is null, clientCode:{}, mkuId:{}", logPrefix, clientCode, mkuId);
            return;
        }

        SkuPO skuPO = skuAtomicService.getSkuPoBySkuId(customerMkuPricePO.getFixedSkuId());
        if (skuPO == null) {
            log.info("MkuManageServiceImpl.{} skuPO is null, clientCode:{}, mkuId:{}, skuId:{}", logPrefix, clientCode, mkuId, customerMkuPricePO.getFixedSkuId());
            return;
        }

        List<SkuPO> skuPOList;
        if (CountryConstant.COUNTRY_ZH.equals(skuPO.getSourceCountryCode())) {
            // 跨境品
            skuPOList = skuAtomicService.getSkuPoByJdMainSkuIdOrJdSpuId(
                    skuPO.getJdMainSkuId() != null ? skuPO.getJdMainSkuId() : null,
                    skuPO.getJdSpuId()
            );
        } else {
            // 本土品
            skuPOList = skuAtomicService.getSkuPoBySpuId(skuPO.getSpuId());
            if (CollectionUtils.isEmpty(skuPOList) || skuPOList.size() <= 1) {
                log.info("MkuManageServiceImpl.{} skuPOList is null or only one, clientCode:{}, mkuId:{}, skuId:{}", logPrefix, clientCode, mkuId, customerMkuPricePO.getFixedSkuId());
                return;
            }
        }

        if (CollectionUtils.isEmpty(skuPOList)) {
            log.info("MkuManageServiceImpl.{} allSkuPOList is null, clientCode:{}, mkuId:{}, skuId:{}", logPrefix, clientCode, mkuId, customerMkuPricePO.getFixedSkuId());
            return;
        }

        Set<Long> skuIds = skuPOList.stream().map(SkuPO::getSkuId).collect(Collectors.toSet());
        List<CustomerMkuPricePO> mkuPricePOS = customerMkuPriceService.listByClientCodeAndSkuIds(skuIds, client.getClientCode());
        if (CollectionUtils.isEmpty(mkuPricePOS)) {
            log.info("MkuManageServiceImpl.{} mkuPricePOS is null, clientCode:{}, mkuId:{}, skuId:{}", logPrefix, clientCode, mkuId, customerMkuPricePO.getFixedSkuId());
            return;
        }
        for (CustomerMkuPricePO mkuPricePO : mkuPricePOS) {
            results.add(this.getMkuClientDetail(mkuPricePO.getClientCode(), mkuPricePO.getMkuId()));
        }
    }

    /**
     * 获取MKU客户端详细信息请求DTO。
     * @param clientCode 客户端代码。
     * @param mkuId MKU ID。
     * @return MKU客户端详细信息请求DTO。
     */
    private MkuClientDetailReqApiDTO getMkuClientDetail(String clientCode,Long mkuId){
        MkuClientDetailReqApiDTO result = new MkuClientDetailReqApiDTO();
        result.setClientCode(clientCode);
        result.setMkuId(mkuId);
        return result;
    }

    /**
     * 实际发送
     * */
    @Override
    public Boolean upsertToEs(List<MkuClientDetailReqApiDTO> apiDTOS){
        if(CollectionUtils.isEmpty(apiDTOS)){
            log.error("CustomerManageService.iscProductSoaUpsertToEs params is null");
            return Boolean.FALSE;
        }

        List<Boolean> results = new ArrayList<>();
        for(MkuClientDetailReqApiDTO reqApiDTO : apiDTOS){
            DataResponse<Boolean> rpcResponse = rpcIscProductSoaMkuService.upsertToEs(reqApiDTO);
            if (null==rpcResponse){
                log.warn("iscProductSoaUpsertToEs.upsertToEs, rpcResponse fail, rpcResponse null. input={}, rpcResponse={}", JSONObject.toJSONString(reqApiDTO), JSONObject.toJSONString(rpcResponse));
                continue;
            }
            if (!rpcResponse.getSuccess()){
                log.warn("iscProductSoaUpsertToEs.upsertToEs, rpcResponse fail. input={}, rpcResponse={}", JSONObject.toJSONString(reqApiDTO), JSONObject.toJSONString(rpcResponse));
                continue;
            }
            results.add(rpcResponse.getData());
        }

        if(CollectionUtils.isEmpty(results)){
            log.error("CustomerManageService.iscProductSoaUpsertToEs results is null");
            return Boolean.FALSE;
        }

        results = results.stream().filter(item->item.equals(Boolean.TRUE)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(results)){
            log.error("CustomerManageService.iscProductSoaUpsertToEs filter results is null");
            return Boolean.FALSE;
        }


        if(results.size() == apiDTOS.size()){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean updateMkuEsAggKey(MkuClientDetailReqApiDTO input) {
        DataResponse<Boolean> response = null;
        try {
            response = rpcIscProductSoaMkuService.updateMkuEsAggKey(input);
            return response.getData();
        } catch (Exception e) {
            log.error("MkuManageServiceImpl.updateMkuEsAggKey param:{},e:", JSONObject.toJSONString(input), e);
        } finally {
            log.info("MkuManageServiceImpl.updateMkuEsAggKey param:{},res:{}", JSONObject.toJSONString(input), JSON.toJSONString(response));
        }
        return false;
    }
}
