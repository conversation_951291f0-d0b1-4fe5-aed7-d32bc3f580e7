package com.jdi.isc.message.broker.service.msg.wimp.productAttribute;

import com.alibaba.fastjson.JSON;
import com.jd.binlog.client.WaveEntry;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.domain.customer.CustomerVO;
import com.jdi.isc.message.broker.domain.enums.MsgTypeEnum;
import com.jdi.isc.message.broker.domain.mku.CustomerMkuPricePO;
import com.jdi.isc.message.broker.domain.msg.MkuPriceChangeMsg;
import com.jdi.isc.message.broker.domain.msg.po.OpenMsgPO;
import com.jdi.isc.message.broker.domain.sku.SkuPO;
import com.jdi.isc.message.broker.domain.spu.ProductGlobalAttributePO;
import com.jdi.isc.message.broker.service.atomic.product.ProductGlobalAttributeAtomicService;
import com.jdi.isc.message.broker.service.atomic.product.SkuAtomicService;
import com.jdi.isc.message.broker.service.ducc.OperDuccConfig;
import com.jdi.isc.message.broker.service.manage.customer.CustomerManageService;
import com.jdi.isc.message.broker.service.manage.mku.CustomerMkuPriceService;
import com.jdi.isc.message.broker.service.manage.msg.OpenMsgManageService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.jdi.isc.message.broker.service.atomic.product.ProductGlobalAttributeAtomicService.TYPE_KEY_SKU_ID;

/**
 * jdi_isc_product_global_attribute_binlake
 * sku跨境属性变更
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ProductGlobalForSaleAttributeBinlakeListener extends AbsMsgTool<OpenMsgPO> {

    @Resource
    private ProductGlobalAttributeAtomicService productGlobalAttributeAtomicService;
    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private OpenMsgManageService openMsgManageService;
    @Resource
    private CustomerMkuPriceService customerMkuPriceService;
    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private OperDuccConfig operDuccConfig;

    /**
     * 处理从JMQ接收到的消息并保存到数据库中。
     * @param messages 接收到的消息列表。
     * @throws BinlakeConsumeException 如果处理消息时发生错误。
     */
    @JmqListener(id = "iscCommonJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.productGlobalAttribute}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<ChangeTableEventMsg> eventMsgList = parse(messages);

        CallerInfo callerInfo = Profiler.registerInfo("prod" + "ProductGlobalForSaleAttributeBinlakeListener.onMessage");

        try {
            log.info("ProductGlobalForSaleAttributeBinlakeListener.onMessage req:{}", JSON.toJSONString(eventMsgList));
            List<OpenMsgPO> target = filterAndTransform(eventMsgList);
            if (CollectionUtils.isNotEmpty(target)) {
                openMsgManageService.batchAdd(target);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            Profiler.functionError(callerInfo);
            throw new BinlakeConsumeException("ProductGlobalForSaleAttributeBinlakeListener.onMessage error", e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /** 消息过滤及转化 */
    private List<OpenMsgPO> filterAndTransform(List<ChangeTableEventMsg> eventMsgList) {
        List<OpenMsgPO> result = new ArrayList<>();

        if (null == eventMsgList || eventMsgList.isEmpty()) {
            log.error("ProductGlobalForSaleAttributeBinlakeListener.filterAndTransform eventMsgList is null");
            return result;
        }

        List<Long> skuIdList = new ArrayList<>();
        for (ChangeTableEventMsg eventMsg : eventMsgList) {
            if (TYPE_KEY_SKU_ID.equals(eventMsg.getRowAfter().get("key_type"))) {
                skuIdList.add(Long.parseLong(eventMsg.getRowAfter().get("key_id")));
            }
        }
        if(CollectionUtils.isEmpty(skuIdList)){
            log.error("ProductGlobalForSaleAttributeBinlakeListener.filterAndTransform skuIdList is null");
            return result;
        }

//        List<Long> testSkuIds = operDuccConfig.getTestSkuIds();
//        if(CollectionUtils.isEmpty(testSkuIds)){
//            log.error("ProductGlobalForSaleAttributeBinlakeListener.filterAndTransform testSkuIds is null");
//            return result;
//        }

        Map<Long, SkuPO> skuPoMap = skuAtomicService.batchQuerySkuPoMap(skuIdList);

        for (ChangeTableEventMsg eventMsg : eventMsgList) {
            if (WaveEntry.EventType.DELETE.equals(eventMsg.getChangeType())) {
                continue;
            }

            String skuIdStr = eventMsg.getRowAfter().get("key_id");
            if (StringUtils.isBlank(skuIdStr)) {
                log.error("ProductGlobalForSaleAttributeBinlakeListener.filterAndTransform skuId is null");
                continue;
            }
            String updater = eventMsg.getRowAfter().get("updater");
            long updateDate = Long.parseLong(eventMsg.getRowAfter().get("update_time"));

            Long skuId = null;
            try {
                skuId = Long.parseLong(skuIdStr);
            } catch (NumberFormatException e) {
                log.error("ProductGlobalForSaleAttributeBinlakeListener.filterAndTransform skuIdStr 不是有效的数字，skuIdStr:{}", skuIdStr);
                continue;
            }
            SkuPO skuPO = skuPoMap.get(skuId);
            if (skuPO == null) {
                log.error("ProductGlobalForSaleAttributeBinlakeListener.filterAndTransform skuPO is null,sku_id:{}", skuId);
                continue;
            }
            if (!StringUtils.equals(CountryConstant.COUNTRY_ZH, skuPO.getSourceCountryCode())) {
                log.error("ProductGlobalForSaleAttributeBinlakeListener.filterAndTransform sku is not zh,sku_id:{}", skuId);
                continue;
            }

            List<ProductGlobalAttributePO> productGlobalAttributePOS = productGlobalAttributeAtomicService.getBySkuIdAndAttributeIdAndLimit(skuId, 71L, 2);
            if (CollectionUtils.isEmpty(productGlobalAttributePOS)) {
                log.error("ProductGlobalForSaleAttributeBinlakeListener.filterAndTransform productGlobalAttributePOS is null,sku_id:{}", skuId);
                continue;
            }

            Boolean allSame = Boolean.TRUE;
            if (productGlobalAttributePOS.size() == 1) {
                log.error("ProductGlobalForSaleAttributeBinlakeListener.filterAndTransform allSame is true,productGlobalAttributePOS.size is ONE,skuId:{}", skuId);
                allSame = Boolean.FALSE;
            } else {
                String value1 = productGlobalAttributePOS.get(0).getAttributeValue();
                String value2 = productGlobalAttributePOS.get(1).getAttributeValue();
                if(!StringUtils.equals(value1,value2)){
                    allSame = Boolean.FALSE;
                }
            }

            if (allSame) {
                log.error("ProductGlobalForSaleAttributeBinlakeListener.filterAndTransform allSame is true,skuId:{}",skuId);
                continue;
            }

//            if(!testSkuIds.contains(skuId)){
//                log.error("ProductGlobalForSaleAttributeBinlakeListener.filterAndTransform testSkuIds not contains skuId,skuId:{}",skuId);
//                continue;
//            }

            List<CustomerMkuPricePO> customerMkuPricePOS = customerMkuPriceService.listBySkuId(skuId);
            if (CollectionUtils.isEmpty(customerMkuPricePOS)) {
                log.error("ProductGlobalForSaleAttributeBinlakeListener.filterAndTransform customerMkuPricePOS is true,skuId:{}",skuId);
                continue;
            }
            for(CustomerMkuPricePO customerMkuPricePO : customerMkuPricePOS){
                String clientCode = customerMkuPricePO.getClientCode();
                CustomerVO client = customerManageService.detail(clientCode);
                if (client == null) {
                    log.error("ProductGlobalForSaleAttributeBinlakeListener.filterAndTransform client is true,clientCode:{}",clientCode);
                    continue;
                }
                if (!client.getOpenSubscribeFlag()) {
                    log.info("client 没有开启订阅, 跳过推送价格数据, clientCode:{}, clientName={}", clientCode, client.getClientName());
                    return null;
                }
                if (!StringUtils.equals(CountryConstant.COUNTRY_BR,client.getCountry())) {
                    log.error("ProductGlobalForSaleAttributeBinlakeListener.filterAndTransform CountryCode is not BR,clientCode:{}",clientCode);
                    continue;
                }

                result.add(new OpenMsgPO(clientCode, MsgTypeEnum.PRICE.getCode(),JSON.toJSONString(new MkuPriceChangeMsg(customerMkuPricePO.getMkuId())),updater,new Date(updateDate)));
            }
        }
        return result;

    }
}
