package com.jdi.isc.message.broker.service.atomic.product;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.message.broker.domain.enums.YnEnum;
import com.jdi.isc.message.broker.domain.sku.SkuPO;
import com.jdi.isc.message.broker.repository.jed.mapper.sku.SkuBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SkuAtomicService extends ServiceImpl<SkuBaseMapper, SkuPO> {


    /**
     * 批量查询 SKU PO 映射表。
     * @param skuIdList SKU ID 列表
     * @return SKU PO 映射表，key 为 SKU ID，value 为对应的 SkuPO 对象；如果输入的 SKU ID 列表为空，则返回 null。
     */
    public Map<Long, SkuPO> batchQuerySkuPoMap(List<Long> skuIdList) {
        if (CollectionUtils.isEmpty(skuIdList)) {
            return null;
        }
        LambdaQueryWrapper<SkuPO> queryWrapper = Wrappers.lambdaQuery(SkuPO.class).select(SkuPO::getSkuId, SkuPO::getSpuId, SkuPO::getSourceCountryCode, SkuPO::getVendorCode)
                .in(SkuPO::getSkuId, skuIdList)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> skuPOList = super.getBaseMapper().selectList(queryWrapper);

        return Optional.ofNullable(skuPOList)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(SkuPO::getSkuId, Function.identity()));
    }

    public List<SkuPO> getSkuPoByJdSkuId(Long jdSkuId) {
        return super.getBaseMapper().selectList(new LambdaQueryWrapper<SkuPO>().eq(SkuPO::getJdSkuId, jdSkuId).eq(SkuPO::getYn, YnEnum.YES.getCode()));
    }

    public SkuPO getSkuPoBySkuId(Long skuId) {
        if(skuId == null){
            return null;
        }
        return super.getBaseMapper().selectOne(new LambdaQueryWrapper<SkuPO>().eq(SkuPO::getSkuId, skuId).eq(SkuPO::getYn, YnEnum.YES.getCode()));
    }

    public List<SkuPO> getSkuPoByJdMainSkuIdOrJdSpuId(Long jdMainSkuId,Long jdSpuId) {
        if(jdMainSkuId == null && jdSpuId == null){
            return null;
        }
        LambdaQueryWrapper<SkuPO> queryWrapper = Wrappers.lambdaQuery(SkuPO.class);
        queryWrapper.eq(jdMainSkuId != null,SkuPO::getJdMainSkuId, jdMainSkuId);
        queryWrapper.eq(jdSpuId != null,SkuPO::getJdSpuId, jdSpuId);
        queryWrapper.eq(SkuPO::getYn, YnEnum.YES.getCode());
        return this.list(queryWrapper);
    }

    public List<SkuPO> getSkuPoBySpuId(Long spuId) {
        if(spuId == null){
            return null;
        }
        return super.getBaseMapper().selectList(new LambdaQueryWrapper<SkuPO>()
                .eq(SkuPO::getSpuId, spuId)
                .eq(SkuPO::getYn, YnEnum.YES.getCode()));
    }


    /**
     * 根据京东商品ID集合查询对应的SKU信息，并以京东商品ID为键，SKU列表为值返回Map。
     * @param jdSkuIdSet 京东商品ID集合
     * @return 以京东商品ID为键，SKU列表为值的Map
     */
    public Map<Long,List<SkuPO>> querySkuMapByJdSkuIds(Set<Long> jdSkuIdSet) {
        LambdaQueryWrapper<SkuPO> queryWrapper = Wrappers.lambdaQuery(SkuPO.class)
                .in(SkuPO::getJdSkuId, jdSkuIdSet)
                .eq(SkuPO::getYn, YnEnum.YES.getCode());
        List<SkuPO> skuPOList = super.getBaseMapper().selectList(queryWrapper);

        return Optional.ofNullable(skuPOList)
                .orElseGet(ArrayList::new)
                .stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(SkuPO::getJdSkuId));
    }
}




