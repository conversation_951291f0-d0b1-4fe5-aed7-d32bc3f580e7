package com.jdi.isc.message.broker.service.manage.es;

import com.jdi.isc.message.broker.domain.msg.MkuClientEsVO;
import com.jdi.isc.message.broker.domain.msg.MkuEsAttributeChangeMsg;

/**
 * @author：xubing82
 * @date：2025/6/12 18:52
 * @description：ES业务属性变更处理接口类
 */
public interface EsAttributeChangeManageService {

    /**
     * 处理ES库存标记流程
     * @param mkuEsAttributeChangeMsg ES属性变更消息对象
     * @param mkuClientEsVO 客户端ES对象
     */
    void handleEsStockMarkingProcess(MkuEsAttributeChangeMsg mkuEsAttributeChangeMsg, MkuClientEsVO mkuClientEsVO);

    void handleEsStockMarkingProcess(MkuEsAttributeChangeMsg mkuEsAttributeChangeMsg);

    /**
     * 处理ES承诺时间标记流程
     * @param mkuEsAttributeChangeMsg ES属性变更消息对象，包含需要处理的ES属性变更信息
     * @param mkuClientEsVO 客户ES值对象，包含客户相关的ES信息
     */
    void handleEsPromiseTimeMarkingProcess(MkuEsAttributeChangeMsg mkuEsAttributeChangeMsg, MkuClientEsVO mkuClientEsVO);
}
