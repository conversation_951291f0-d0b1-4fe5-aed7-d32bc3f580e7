package com.jdi.isc.message.broker.service.msg.wimp.sku;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.binlog.client.WaveEntry;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.common.exception.MqConsumeException;
import com.jdi.isc.message.broker.domain.sku.BWareSkuChangeMsgVO;
import com.jdi.isc.message.broker.domain.sku.UpdateSkuStatusVO;
import com.jdi.isc.message.broker.service.manage.sku.SkuManageService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 国内SKU状态变更ßß
 * <AUTHOR>
 * @description：JdSkuStatusSyncListener
 * @Date 2025-07-14
 */
@Slf4j
@Service
public class JdSkuStatusSyncListener extends AbsMsgTool<BWareSkuChangeMsgVO> {

    @Value("${spring.profiles.active}")
    private String systemEnv;

    private static final String SKU_STATUS = "skuState";

    @Resource
    private SkuManageService skuManageService;

    /**
     * 处理从JMQ接收到的消息并保存到数据库中。
     * https://cf.jd.com/pages/viewpage.action?pageId=213019662
     * bware_sku_data_change
     * @param messages 接收到的消息列表。
     * @throws BinlakeConsumeException 如果处理消息时发生错误。
     */
    @JmqListener(id = "iscJmq2InternationalCenter", topics = {"${topic.jmq4.consumer.isc.bware}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<BWareSkuChangeMsgVO> eventMsgList = transform(messages);

        CallerInfo callerInfo = Profiler.registerInfo(systemEnv + "-JdSkuStatusSyncListener.onMessage");

        try {
            List<UpdateSkuStatusVO> updateSkuStatusVOList = Lists.newArrayList();

            for (BWareSkuChangeMsgVO eventMsg : eventMsgList) {
                // 过滤费插入、更新操作
                if (WaveEntry.EventType.UPDATE.name().equals(eventMsg.getChangeType())
                        || WaveEntry.EventType.INSERT.name().equals(eventMsg.getChangeType())) {

                    if (CollectionUtils.isEmpty(eventMsg.getChangeFields())) {
                        continue;
                    }

                    //log.info("JdSkuStatusSyncListener.onMessage jdSkuId={},changeFields={}", eventMsg.getAfterData().getJdSkuId(), eventMsg.getChangeFields());
                    // 判断是否是sku状态变更
                    if (!eventMsg.getChangeFields().contains(SKU_STATUS)) {
                        continue;
                    }

                    Integer afterSkuStatus = eventMsg.getAfterData().getSkuState();
                    Long jdSkuId = eventMsg.getAfterData().getJdSkuId();
                    updateSkuStatusVOList.add(new UpdateSkuStatusVO(jdSkuId, afterSkuStatus));
                }
                // 同步国内SKU状态
                if (CollectionUtils.isNotEmpty(updateSkuStatusVOList)){
                    boolean result = skuManageService.updateSkuStatusFromJdSku(updateSkuStatusVOList);
                    log.info("JdSkuStatusSyncListener.onMessage req:{},res:{}", JSON.toJSONString(updateSkuStatusVOList),result);
                    if (!result) {
                        throw new MqConsumeException(String.format("JdSkuStatusSyncListener.onMessage 国内商品信息[%s]联动国际sku状态失败",JSON.toJSONString(updateSkuStatusVOList)));
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            Profiler.functionError(callerInfo);
            throw new MqConsumeException("JdSkuStatusSyncListener.onMessage error", e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }
}
