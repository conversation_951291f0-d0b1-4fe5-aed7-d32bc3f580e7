package com.jdi.isc.message.broker.service.msg.es;

import com.alibaba.fastjson.JSON;
import com.jd.binlog.client.WaveEntry;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.domain.msg.po.OpenMsgPO;
import com.jdi.isc.message.broker.rpc.mku.RpcIscProductSoaMkuService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuPoolFlagDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class CountryMkuEsBinLakeListener extends AbsMsgTool<OpenMsgPO> {

    @Resource
    private RpcIscProductSoaMkuService rpcIscProductSoaMkuService;

    @JmqListener(id = "iscMessageesJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.countryMku}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<ChangeTableEventMsg> eventMsgList = parse(messages);
        log.info("CountryMkuEsBinLakeListener.onMessage req:{}" , JSON.toJSONString(eventMsgList));
        try {
            List<MkuPoolFlagDTO> targets = filterAndTransform(eventMsgList);
            if(CollectionUtils.isNotEmpty(targets)){
                for (MkuPoolFlagDTO mkuPoolFlagDTO : targets){
                    rpcIscProductSoaMkuService.updateMkuPoolFlag(mkuPoolFlagDTO);
                }
            }
        }catch (Exception e){
            log.error("CountryMkuEsBinLakeListener.onMessage msg:{}",JSON.toJSONString(messages),e);
        }
    }

    private List<MkuPoolFlagDTO> filterAndTransform(List<ChangeTableEventMsg> eventMsgList) {
        List<MkuPoolFlagDTO> result = new ArrayList<>();
        for(ChangeTableEventMsg msg : eventMsgList){
            if(WaveEntry.EventType.DELETE.equals(msg.getChangeType())){
                continue;
            }
            String mkuId = msg.getRowAfter().get("mku_id");
            String targetCountryCode = msg.getRowAfter().get("target_country_code");

            String beforePoolStatus = msg.getRowBefore().get("pool_status");
            String afterPoolStatus = msg.getRowAfter().get("pool_status");

            if (StringUtils.isBlank(beforePoolStatus)
                    || StringUtils.isBlank(afterPoolStatus)
                    || StringUtils.equals(beforePoolStatus,afterPoolStatus)) {
                log.info("CountryMkuEsBinLakeListener.filterAndTransform data={}", JSON.toJSONString(msg));
                continue;
            }

            MkuPoolFlagDTO mkuPoolFlagDTO = new MkuPoolFlagDTO();
            mkuPoolFlagDTO.setMkuId(Long.valueOf(mkuId));
            mkuPoolFlagDTO.setTargetCountryCode(targetCountryCode);
            result.add(mkuPoolFlagDTO);
        }
        return result;
    }
}
