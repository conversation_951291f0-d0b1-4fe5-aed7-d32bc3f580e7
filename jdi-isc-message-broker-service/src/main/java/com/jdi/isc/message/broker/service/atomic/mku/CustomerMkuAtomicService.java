package com.jdi.isc.message.broker.service.atomic.mku;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.message.broker.domain.enums.YnEnum;
import com.jdi.isc.message.broker.domain.mku.CustomerMkuPO;
import com.jdi.isc.message.broker.repository.jed.mapper.mku.CustomerMkuBaseMapper;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @author：xubing82
 * @date：2025/6/12 20:35
 * @description：CustomerMkuAtomicService
 */
@Service
public class CustomerMkuAtomicService extends ServiceImpl<CustomerMkuBaseMapper, CustomerMkuPO> {


    public List<CustomerMkuPO> list(Long mkuId){
        LambdaQueryWrapper<CustomerMkuPO> wrapper = Wrappers.<CustomerMkuPO>lambdaQuery()
                .eq(CustomerMkuPO::getMkuId, mkuId)
                .eq(CustomerMkuPO::getBindStatus, "BIND")
                .eq(CustomerMkuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(wrapper);
    }


    public List<CustomerMkuPO> listCustomerMkuByMkuIdsCountry(Collection<Long> mkuIds, String targetCountryCode){
        if (CollectionUtils.isEmpty(mkuIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CustomerMkuPO> queryWrapper = Wrappers.lambdaQuery(CustomerMkuPO.class)
                .in(CustomerMkuPO::getMkuId, mkuIds)
                .eq(Objects.nonNull(targetCountryCode), CustomerMkuPO::getTargetCountryCode, targetCountryCode)
                .eq(CustomerMkuPO::getBindStatus, CustomerMkuBindEnum.BIND)
                .eq(CustomerMkuPO::getYn, YnEnum.YES.getCode());
        return this.getBaseMapper().selectList(queryWrapper);
    }
}
