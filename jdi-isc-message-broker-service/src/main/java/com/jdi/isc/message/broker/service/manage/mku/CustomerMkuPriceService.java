package com.jdi.isc.message.broker.service.manage.mku;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.message.broker.domain.enums.YnEnum;
import com.jdi.isc.message.broker.domain.mku.CustomerMkuPricePO;
import com.jdi.isc.message.broker.repository.jed.mapper.mku.CustomerMkuPriceBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;


/**
 * 客户MKU价格服务
 * <AUTHOR>
 * @date 20231128
 **/
@Service
@Slf4j
public class CustomerMkuPriceService extends ServiceImpl<CustomerMkuPriceBaseMapper, CustomerMkuPricePO> {

    public List<CustomerMkuPricePO> listByFixedSku(Long skuId){
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .eq(CustomerMkuPricePO::getFixedSkuId, skuId)
                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode());
        return list(wrapper);
    }

    public CustomerMkuPricePO getOne(Long skuId,String clientCode){
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .eq(CustomerMkuPricePO::getClientCode, clientCode)
                .eq(CustomerMkuPricePO::getFixedSkuId, skuId)
                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode());
        return getOne(wrapper);
    }

    public CustomerMkuPricePO getOneByMkuId(Long mkuId,String clientCode){
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .eq(CustomerMkuPricePO::getClientCode, clientCode)
                .eq(CustomerMkuPricePO::getMkuId, mkuId)
                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode());
        return getOne(wrapper);
    }

    public List<CustomerMkuPricePO> listByClientCodeAndSkuIds(Collection<Long> skuIds, String clientCode){
        if(CollectionUtils.isEmpty(skuIds) || StringUtils.isBlank(clientCode)){
            return null;
        }
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .select(CustomerMkuPricePO::getClientCode,CustomerMkuPricePO::getFixedSkuId,CustomerMkuPricePO::getMkuId)
                .eq(CustomerMkuPricePO::getClientCode, clientCode)
                .in(CustomerMkuPricePO::getFixedSkuId, skuIds)
                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode());
        return list(wrapper);
    }

    public List<CustomerMkuPricePO> listBySkuId(Long skuId){
        if(skuId == null){
            return null;
        }
        LambdaQueryWrapper<CustomerMkuPricePO> wrapper = Wrappers.<CustomerMkuPricePO>lambdaQuery()
                .select(CustomerMkuPricePO::getClientCode,CustomerMkuPricePO::getFixedSkuId,CustomerMkuPricePO::getMkuId)
                .in(CustomerMkuPricePO::getFixedSkuId, skuId)
                .eq(CustomerMkuPricePO::getYn, YnEnum.YES.getCode());
        return list(wrapper);
    }
}
