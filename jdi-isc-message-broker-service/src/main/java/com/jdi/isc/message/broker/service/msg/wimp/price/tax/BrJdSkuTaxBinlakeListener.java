package com.jdi.isc.message.broker.service.msg.wimp.price.tax;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.binlog.client.EntryMessage;
import com.jd.binlog.client.MessageDeserialize;
import com.jd.binlog.client.WaveEntry;
import com.jd.binlog.client.impl.JMQMessageDeserialize;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.domain.customer.CustomerVO;
import com.jdi.isc.message.broker.domain.msg.MkuPriceChangeMsg;
import com.jdi.isc.message.broker.domain.msg.po.OpenMsgPO;
import com.jdi.isc.message.broker.domain.sku.SkuPO;
import com.jdi.isc.message.broker.rpc.curreny.IscProductSoaCountryRpcApiService;
import com.jdi.isc.message.broker.rpc.price.IscProductSoaSalePriceReadRpcService;
import com.jdi.isc.message.broker.rpc.suport.AlertHelper;
import com.jdi.isc.message.broker.rpc.taxRate.IscTaxRateRpcService;
import com.jdi.isc.message.broker.service.atomic.product.SkuAtomicService;
import com.jdi.isc.message.broker.service.ducc.OperDuccConfig;
import com.jdi.isc.message.broker.service.manage.customer.CustomerManageService;
import com.jdi.isc.message.broker.service.manage.msg.OpenMsgManageService;
import com.jdi.isc.message.broker.service.manage.price.PricePushManagerService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.sku.req.SkuSyncNcmDTO;
import com.jdi.isc.product.soa.api.taxRate.req.BrImportTaxReqDTO;
import com.jdi.isc.product.soa.price.api.salePrice.req.SalePriceReqDTO;
import com.jdi.isc.product.soa.price.api.salePrice.res.SalePriceResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

/**
 * jdi_isc_br_jd_sku_tax_sharding
 * 巴西进口税率
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BrJdSkuTaxBinlakeListener extends AbsMsgTool<BrImportTaxReqDTO> {

    @Resource
    private SkuAtomicService skuAtomicService;
    @Resource
    private IscTaxRateRpcService iscTaxRateRpcService;
    @Resource
    private OperDuccConfig operDuccConfig;

    private static final Set<String> dbFields = Sets.newHashSet(/*"country_code", "client_code",*/ "industry_product_tax");
    /**
     * On message.
     *
     * @param messages the messages
     * @throws Exception the exception
     */
    @JmqListener(id = "iscCommonJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.brSkuTaxMsg}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<ChangeTableEventMsg> eventMsgList = parse(messages);
        try {
            log.info("BrJdSkuTaxBinlakeListener.onMessage req:{}" , JSON.toJSONString(eventMsgList));
            List<BrImportTaxReqDTO> targets = filterAndTransform(eventMsgList);

            log.info("BrJdSkuTaxBinlakeListener.filterAndTransform, data={}", JSON.toJSONString(targets));
            if (CollectionUtils.isNotEmpty(targets)) {
                for(BrImportTaxReqDTO brImportTaxReqDTO : targets){
                    iscTaxRateRpcService.updateBrIpiTax(brImportTaxReqDTO);
                }
            }
        }catch (Exception e){
            throw new BinlakeConsumeException("BrJdSkuTaxBinlakeListener.onMessage error",e);
        }
    }

    private List<BrImportTaxReqDTO> filterAndTransform(List<ChangeTableEventMsg> eventMsgList) {
        List<BrImportTaxReqDTO> result = Lists.newArrayListWithExpectedSize(eventMsgList.size());
//        List<Long> testSkuIds = operDuccConfig.getTestSkuIds();
//        if(CollectionUtils.isEmpty(testSkuIds)){
//            return result;
//        }
        for (ChangeTableEventMsg eventMsg : eventMsgList) {
            if(WaveEntry.EventType.DELETE.equals(eventMsg.getChangeType())){
                continue;
            }

            long count = dbFields.stream().filter(eventMsg.getChangeFields()::containsKey).count();

            if (count < 1) {
                log.info("BrJdSkuTaxBinlakeListener.filterAndTransform 价格变更字段为空, 跳过推送价格数据");
                return null;
            }

            String jdSkuIdAfterStr = eventMsg.getRowAfter().get("jd_sku_id");
            if(StringUtils.isBlank(jdSkuIdAfterStr)){
                log.error("BrJdSkuTaxBinlakeListener.filterAndTransform jdSkuIdAfterStr is null");
                continue;
            }
            String ipiRate = eventMsg.getRowAfter().get("industry_product_tax");
            String updater = eventMsg.getRowAfter().get("updater");

            Long jdSkuId = Long.valueOf(jdSkuIdAfterStr);

            List<SkuPO> skuPOList = skuAtomicService.getSkuPoByJdSkuId(jdSkuId);
            if(CollectionUtils.isEmpty(skuPOList)){
                log.error("BrJdSkuTaxBinlakeListener.filterAndTransform skuPOList is null");
                continue;
            }

            for(SkuPO skuPO : skuPOList){
//                if(!testSkuIds.contains(skuPO.getSkuId())){
//                    continue;
//                }
                BrImportTaxReqDTO brImportTaxReqDTO = new BrImportTaxReqDTO();
                brImportTaxReqDTO.setSkuId(skuPO.getSkuId());
                brImportTaxReqDTO.setPin(updater);
                brImportTaxReqDTO.setIpiRate(new BigDecimal(ipiRate));
                result.add(brImportTaxReqDTO);
            }
        }
        return result;
    }
}
