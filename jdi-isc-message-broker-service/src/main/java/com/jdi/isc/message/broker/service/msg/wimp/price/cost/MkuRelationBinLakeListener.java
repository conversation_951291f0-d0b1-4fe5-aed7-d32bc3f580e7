package com.jdi.isc.message.broker.service.msg.wimp.price.cost;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.binlog.client.WaveEntry;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.PfinderContext;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.message.broker.common.exception.ProductBizException;
import com.jdi.isc.message.broker.common.util.NumberUtil;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.domain.enums.MkuRelationBindStatusEnum;
import com.jdi.isc.message.broker.domain.msg.po.OpenMsgPO;
import com.jdi.isc.message.broker.service.ducc.OperDuccConfig;
import com.jdi.isc.message.broker.service.manage.price.PriceManagerService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.message.broker.rpc.suport.AlertHelper;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.enums.AgreementPriceDataSourceTypeEnums;
import com.jdi.isc.product.soa.price.api.price.req.PriceRefreshVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * mku绑定关系 binLake 监听器.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MkuRelationBinLakeListener extends AbsMsgTool<OpenMsgPO> {

    @Resource
    private PriceManagerService priceManagerService;

    @Resource
    private OperDuccConfig operDuccConfig;

    /**
     * 监听事件类型
     */
    private final List<WaveEntry.EventType> eventTypes = Lists.newArrayList(WaveEntry.EventType.INSERT, WaveEntry.EventType.UPDATE);

    /**
     * On rate change message.
     *
     * @param messages the messages
     * @throws Exception the exception
     */
    @JmqListener(id = "iscProductSoaJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.mkuRelation}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<ChangeTableEventMsg> eventMsgList = parse(messages);

//        PfinderContext.iterateConsume(messages, message -> {
            // 体积或者hs_code变动，出发成本价重算
            List<PriceRefreshVO> target = filterAndTransform(eventMsgList);

            if (CollectionUtils.isEmpty(target)) {
                log.info("mku绑定关系表binLake，刷新协议价. 没有需要处理的数据");
                return;
            }

            for (List<PriceRefreshVO> list : Lists.partition(target, 100)) {
                try {
                    log.info("mku绑定关系表binLake. 刷新协议价. 消息:{}", JSON.toJSONString(list));
                    priceManagerService.costPriceRefresh(target);
                } catch (Exception e) {
                    log.error("mku绑定关系表binLake，刷新价格信息, 出现异常. 消息:{}", JSON.toJSONString(target), e);
                    AlertHelper.p0(this.getClass().getSimpleName(), Thread.currentThread().getStackTrace()[1].getMethodName(), "中国出口税率信息表binlake，刷新价格信息", e.getMessage());
                    throw new ProductBizException("mku绑定关系表binLake，同步失败");
                }
            }
//        });
    }

    /**
     * 消息过滤及转化
     */
    private List<PriceRefreshVO> filterAndTransform(List<ChangeTableEventMsg> eventMsgList) {

        if (null == eventMsgList || eventMsgList.isEmpty()) {
            return Lists.newArrayList();
        }

        List<PriceRefreshVO> result = Lists.newArrayList();

        for (ChangeTableEventMsg eventMsg : eventMsgList) {
            Set<String> changeKey = eventMsg.getChangeFields().keySet();
            //忽略非更新消息
            if (!eventTypes.contains(eventMsg.getChangeType()) || CollectionUtils.isEmpty(changeKey)) {
                continue;
            }

            Long skuId = eventMsg.getAfterValue("sku_id", Long.class);
            Long yn = eventMsg.getAfterValue("yn", Long.class);

            // 绑定状态
            Integer beforeBindStatus = eventMsg.getBeforeValue("bind_status", Integer.class);
            Integer afterBindStatus = eventMsg.getAfterValue("bind_status", Integer.class);
            String skuSourceCountryCode = eventMsg.getAfterValueStr("sku_source_country_code");

            if (yn == 0 || skuId == null || StringUtils.isEmpty(skuSourceCountryCode)) {
                continue;
            }

            if (beforeBindStatus == null && afterBindStatus == null) {
                continue;
            }

            if (beforeBindStatus != null && NumberUtil.equals(afterBindStatus, beforeBindStatus)) {
                log.warn("mku绑定关系表binLake，刷新价格信息. 状态未发生变化. data={}", JSON.toJSONString(eventMsg));
                continue;
            }

            // 如果不是绑定操作
            if (!NumberUtil.eq(MkuRelationBindStatusEnum.BIND.getCode(), afterBindStatus)) {
                log.warn("mku绑定关系表binLake，刷新价格信息. 非绑定操作. data={}", JSON.toJSONString(eventMsg));
                continue;
            }

            // 刷新数据
            List<String> targetCountryCodes = StringUtils.equals(skuSourceCountryCode, CountryConstant.COUNTRY_ZH) ? CountryConstant.ALL_COUNTRY_CODE_LIST : Lists.newArrayList(skuSourceCountryCode);

            for (String targetCountryCode : targetCountryCodes) {

                if (!operDuccConfig.isComputeJdTaxRefundPrice(targetCountryCode)) {
                    log.info("targetCountryCode is not support compute jd tax refund price, targetCountryCode={}", targetCountryCode);
                    continue;
                }

                if (!operDuccConfig.isComputeJdTaxRefundPriceSku(skuId)) {
                    log.info("skuId is not support compute jd tax refund price, skuId={}", skuId);
                    continue;
                }

                PriceRefreshVO item = new PriceRefreshVO();
                item.setSourceCountryCode(skuSourceCountryCode);
                item.setTargetCountryCode(targetCountryCode);
                item.setSkuId(skuId);
                // 是否为仓库产品，0-直送，1-仓发，null-全部
                item.setIsWareHouseProduct(0);
                item.setDataStatusSource(AgreementPriceDataSourceTypeEnums.MKU_RELATION.getCode());
                result.add(item);
            }
        }

        return result;
    }

}
