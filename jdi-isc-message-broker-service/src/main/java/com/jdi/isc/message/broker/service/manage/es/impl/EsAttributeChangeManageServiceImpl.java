package com.jdi.isc.message.broker.service.manage.es.impl;

import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.isc.message.broker.domain.msg.MkuClientEsVO;
import com.jdi.isc.message.broker.domain.msg.MkuEsAttributeChangeMsg;
import com.jdi.isc.message.broker.service.manage.es.EsAttributeChangeManageService;
import com.jdi.isc.message.broker.service.manage.mku.MkuManageService;
import com.jdi.isc.product.soa.api.common.enums.MkuFeatureTagEnum;
import com.jdi.isc.product.soa.api.common.enums.PromiseTimeTagEnum;
import com.jdi.isc.product.soa.api.common.enums.SkuSpecialAttrEnum;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuEsDetailReqApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuEsStockTagReqDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuPromiseTagDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Set;

import static com.jdi.isc.message.broker.common.constants.UmpKeyConstant.BUSINESS_KEY_FRAME_WARNING;

/**
 * @author：xubing82
 * @date：2025/6/12 18:53
 * @description：ES业务属性变更处理接口实现类
 */
@Slf4j
@Service
public class EsAttributeChangeManageServiceImpl implements EsAttributeChangeManageService {


    @Resource
    private MkuManageService mkuManageService;

    @Value("${spring.profiles.active}")
    private String envCode;


    @Override
    public void handleEsStockMarkingProcess(MkuEsAttributeChangeMsg mkuEsAttributeChangeMsg, MkuClientEsVO mkuClientEsVO) {
        try {
            //库存标签处理
            Integer oldStockFlag = mkuClientEsVO.getHasStock();
            Integer newStockFlag = mkuEsAttributeChangeMsg.getHasStockTag();
            if (!Objects.equals(oldStockFlag, newStockFlag)) {

                //库存产生变更
                MkuEsStockTagReqDTO mkuEsStockTagReqDTO = buildMkuEsStockTagReqDTO(mkuEsAttributeChangeMsg, newStockFlag);
                boolean result = mkuManageService.updateMkuEsStockTag(mkuEsStockTagReqDTO);
                if (!result) {
                    log.error("EsAttributeChangeManageServiceImpl.handleEsStockMarkingProcess updateMkuEsStockTag failed! changeMsg:{}", JSON.toJSONString(mkuEsAttributeChangeMsg));

                    //ump监控告警
                    String umpErrMsg = String.format("[%s]更新商品库存有无标签失败,请联系管理员，clientCode=%s, mkuId=%s, oldStockFlag=%s, newStockFlag=%s", envCode,
                            mkuEsAttributeChangeMsg.getClientCode(), mkuEsAttributeChangeMsg.getMkuId(), oldStockFlag, newStockFlag);
                    Profiler.businessAlarm(BUSINESS_KEY_FRAME_WARNING, umpErrMsg);
                }
            }
        } catch (Exception e) {
            log.error("EsAttributeChangeManageServiceImpl.handleEsStockMarkingProcess failed, request:{}", JSON.toJSONString(mkuEsAttributeChangeMsg));

            //ump监控告警
            String umpErrMsg = String.format("[%s]更新商品库存有无标签异常,请联系管理员，clientCode=%s, mkuId=%s", envCode,
                    mkuEsAttributeChangeMsg.getClientCode(), mkuEsAttributeChangeMsg.getMkuId());
            Profiler.businessAlarm(BUSINESS_KEY_FRAME_WARNING, umpErrMsg);
        }
    }

    @Override
    public void handleEsStockMarkingProcess(MkuEsAttributeChangeMsg mkuEsAttributeChangeMsg){
        //获取es数据
        MkuEsDetailReqApiDTO mkuClientDetailReqApiDTO = buildMkuClientDetailReqApiDTO(mkuEsAttributeChangeMsg);
        MkuClientEsVO mkuClientEsVO = mkuManageService.queryMkuEsByParam(mkuClientDetailReqApiDTO);
        if (mkuClientEsVO == null) {
            log.warn("MkuEsAttributesChangeMsgListener queryMkuEsByParam empty, 商品在es中不存在！request:{}", JSON.toJSONString(mkuEsAttributeChangeMsg));
            return;
        }

        handleEsStockMarkingProcess(mkuEsAttributeChangeMsg, mkuClientEsVO);
    }


    @Override
    public void handleEsPromiseTimeMarkingProcess(MkuEsAttributeChangeMsg mkuEsAttributeChangeMsg, MkuClientEsVO mkuClientEsVO){
        try {
            //时效标签处理
            Set<Integer> oldPromiseFlagSet = mkuClientEsVO.getFeatureTags();
            Integer newPromiseFlag = mkuEsAttributeChangeMsg.getPromiseValue();

            //本次履约时效需求非7日达的数据先不处理、如果是null则放过
            if (!Objects.equals(PromiseTimeTagEnum.forCode(newPromiseFlag), PromiseTimeTagEnum.DEFAULT_EMPTY_TAG)
                    && !Objects.equals(PromiseTimeTagEnum.forCode(newPromiseFlag), PromiseTimeTagEnum.DELIVERY_7_DAYS_TAG)) {
                return;
            }

            boolean updateFlag = whetherUpdatePromiseFlag(oldPromiseFlagSet, newPromiseFlag);
            if (updateFlag) {

                //时效产生变更
                MkuPromiseTagDTO mkuPromiseTagDTO = buildMkuEsPromiseTimeTagReqDTO(mkuEsAttributeChangeMsg, newPromiseFlag, oldPromiseFlagSet);
                boolean result = mkuManageService.updateMkuEsCommonPromiseTag(mkuPromiseTagDTO);
                if (!result) {
                    log.error("EsAttributeChangeManageServiceImpl.handleEsPromiseTimeMarkingProcess failed! changeMsg:{}", JSON.toJSONString(mkuEsAttributeChangeMsg));

                    //ump监控告警
                    String umpErrMsg = String.format("[%s]更新商品时效标签失败,请联系管理员，clientCode=%s, mkuId=%s, oldFlag=%s, newFlag=%s", envCode,
                            mkuEsAttributeChangeMsg.getClientCode(), mkuEsAttributeChangeMsg.getMkuId(), oldPromiseFlagSet, newPromiseFlag);
                    Profiler.businessAlarm(BUSINESS_KEY_FRAME_WARNING, umpErrMsg);
                }
            }
            log.info("EsAttributeChangeManageServiceImpl whetherUpdatePromiseFlag,oldPromiseFlagSet:{}, newPromiseFlag:{}, result:{}",
                    oldPromiseFlagSet, newPromiseFlag, updateFlag);

        } catch (Exception e) {
            log.error("EsAttributeChangeManageServiceImpl.handleEsPromiseTimeMarkingProcess failed, request:{}", JSON.toJSONString(mkuEsAttributeChangeMsg));

            //ump监控告警
            String umpErrMsg = String.format("[%s]更新商品时效标签失败,请联系管理员，clientCode=%s, mkuId=%s", envCode,
                    mkuEsAttributeChangeMsg.getClientCode(), mkuEsAttributeChangeMsg.getMkuId());
            Profiler.businessAlarm(BUSINESS_KEY_FRAME_WARNING, umpErrMsg);
        }
    }

    /**
     * 比对逻辑：
     * 1、当前标为null，则放过
     * ——后续处理：获取出时效标记，都进行移除；
     * 2、es中包括了当前标记，则不更新；
     * ——es没标记、新值也是null，则不处理；
     * 3、不包括则处理(底层追加)处理；
     *
     * @param skuFeatures
     * @param newPromiseFlag
     * @return
     */
    private boolean whetherUpdatePromiseFlag(Set<Integer> skuFeatures, Integer newPromiseFlag) {
        if (emptyPromiseFlag(newPromiseFlag) && emptySkuFeatureSet(skuFeatures)) {
            //没有标记需要更新
            return false;
        }

        if (emptyPromiseFlag(newPromiseFlag)) {
            //es存在标记，需要移除时效标记
            return true;
        }

        MkuFeatureTagEnum mkuFeatureTagEnum = MkuFeatureTagEnum.queryByFulfilPromise(SkuSpecialAttrEnum.FULFIL_PROMISE, newPromiseFlag);
        if (mkuFeatureTagEnum != null) {
            // es中包括了当前标记，不包括则更新
            return !skuFeatures.contains(mkuFeatureTagEnum.getEsCode());
        }

        return true;
    }

    /**
     * 判断承诺标志是否为空或默认空值
     * @param newPromiseFlag 待检查的承诺标志值
     * @return 当入参为null或等于默认空标志值时返回true，否则返回false
     */
    private boolean emptyPromiseFlag(Integer newPromiseFlag) {
        return newPromiseFlag == null ||
                Objects.equals(newPromiseFlag, PromiseTimeTagEnum.DEFAULT_EMPTY_TAG.getCode());
    }

    /**
     * 判断SKU特征集合是否为空集合或仅包含默认空标记
     * @param skuFeaturesFlag 待检查的SKU特征标志集合
     * @return 当集合为空或仅包含默认空标记时返回true，否则返回false
     */
    private boolean emptySkuFeatureSet(Set<Integer> skuFeaturesFlag) {
        if (CollectionUtils.isEmpty(skuFeaturesFlag)) {
            return true;
        }

        if (skuFeaturesFlag.size() == 1 && skuFeaturesFlag.contains(PromiseTimeTagEnum.DEFAULT_EMPTY_TAG.getCode())) {
            return true;
        }

        return false;
    }

    /**
     * 构建MkuEsStockTagReqDTO对象
     *
     * @param skuStockEsChangeMsg SKU库存ES变更消息，包含客户端代码、国家代码和MKU ID等信息
     * @param newStockFlag        新库存标记，表示是否有库存
     * @return 构建好的MkuEsStockTagReqDTO对象，包含客户端代码、国家代码、MKU ID和库存标记
     */
    @NotNull
    private MkuEsStockTagReqDTO buildMkuEsStockTagReqDTO(MkuEsAttributeChangeMsg skuStockEsChangeMsg, Integer newStockFlag) {
        MkuEsStockTagReqDTO mkuEsStockTagReqDTO = new MkuEsStockTagReqDTO();
        mkuEsStockTagReqDTO.setClientCode(skuStockEsChangeMsg.getClientCode());
        mkuEsStockTagReqDTO.setCountry(skuStockEsChangeMsg.getCountryCode());
        mkuEsStockTagReqDTO.setMkuId(skuStockEsChangeMsg.getMkuId());
        mkuEsStockTagReqDTO.setHasStock(newStockFlag);
        return mkuEsStockTagReqDTO;
    }

    /**
     * 构建MKU ES承诺时间标签请求DTO对象
     * @param mkuEsAttributeChangeMsg MKU ES属性变更消息对象，包含客户端代码、国家代码和MKU ID等信息
     * @param newValue 新的承诺时间数值
     * @return 包含客户端代码、国家代码、MKU ID和承诺时间数值的MKU承诺标签DTO对象
     */
    private MkuPromiseTagDTO buildMkuEsPromiseTimeTagReqDTO(MkuEsAttributeChangeMsg mkuEsAttributeChangeMsg, Integer newValue, Set<Integer> oldPromiseFlag) {
        MkuPromiseTagDTO mkuPromiseTagDTO = new MkuPromiseTagDTO();
        mkuPromiseTagDTO.setClientCode(mkuEsAttributeChangeMsg.getClientCode());
        mkuPromiseTagDTO.setCountryCode(mkuEsAttributeChangeMsg.getCountryCode());
        mkuPromiseTagDTO.setMkuId(mkuEsAttributeChangeMsg.getMkuId());
        mkuPromiseTagDTO.setPromiseValue(newValue);
        mkuPromiseTagDTO.setFeatureTags(oldPromiseFlag);
        return mkuPromiseTagDTO;
    }

    /**
     * 根据SKU库存变更消息构建MKU客户端详情请求DTO对象
     *
     * @param skuStockEsChangeMsg SKU库存变更消息，包含客户端代码和MKU ID信息
     * @return 构建完成的MKU客户端详情请求数据传输对象
     */
    @NotNull
    private MkuEsDetailReqApiDTO buildMkuClientDetailReqApiDTO(MkuEsAttributeChangeMsg skuStockEsChangeMsg) {
        MkuEsDetailReqApiDTO mkuClientDetailReqApiDTO = new MkuEsDetailReqApiDTO();
        mkuClientDetailReqApiDTO.setClientCode(skuStockEsChangeMsg.getClientCode());
        mkuClientDetailReqApiDTO.setMkuId(skuStockEsChangeMsg.getMkuId());
        mkuClientDetailReqApiDTO.setCountry(skuStockEsChangeMsg.getCountryCode());
        return mkuClientDetailReqApiDTO;
    }

}
