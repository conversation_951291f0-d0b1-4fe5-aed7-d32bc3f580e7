package com.jdi.isc.message.broker.service.msg.wiop;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.jd.binlog.client.WaveEntry;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.domain.customer.CustomerVO;
import com.jdi.isc.message.broker.domain.enums.MsgTypeEnum;
import com.jdi.isc.message.broker.domain.mku.CustomerMkuPricePO;
import com.jdi.isc.message.broker.domain.msg.MkuPriceChangeMsg;
import com.jdi.isc.message.broker.domain.msg.po.OpenMsgPO;
import com.jdi.isc.message.broker.rpc.price.IscCountryAgreementPriceRpcService;
import com.jdi.isc.message.broker.service.ducc.OperDuccConfig;
import com.jdi.isc.message.broker.service.manage.customer.CustomerManageService;
import com.jdi.isc.message.broker.service.manage.mku.CustomerMkuPriceService;
import com.jdi.isc.message.broker.service.manage.msg.OpenMsgManageService;
import com.jdi.isc.message.broker.service.manage.price.PricePushManagerService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.agreementPrice.req.CountryAgreementPriceReqDTO;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.CurrencyConstant;
import com.jdi.isc.product.soa.api.common.LangConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 客户sku价格binlake消息监听V2
 * <AUTHOR>
 * @date 20250313
 */
@Slf4j
@Component
public class CustomerSkuPriceBinlakeListenerV2 extends AbsMsgTool<MkuPriceChangeMsg> {

    @Resource
    private OpenMsgManageService openMsgManageService;
    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private CustomerMkuPriceService customerMkuPriceService;

    @Value("${spring.profiles.active}")
    private String systemCode;

    @Resource
    private IscCountryAgreementPriceRpcService iscCountryAgreementPriceRpcService;

    @Resource
    private PricePushManagerService pricePushManagerService;

    @Resource
    private OperDuccConfig operDuccConfig;

    @JmqListener(id = "iscProductSoaJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.customerSkuPriceDetailMsg}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<ChangeTableEventMsg> eventMsgList = parse(messages);
        try {
            log.info("CustomerSkuPriceBinlakeListenerV2.onMessage req:{}" , JSON.toJSONString(eventMsgList));
            List<CountryAgreementPriceReqDTO> target = filterAndTransformBrData(eventMsgList);
            if(CollectionUtils.isNotEmpty(target)){
                for (CountryAgreementPriceReqDTO dto : target) {
                    iscCountryAgreementPriceRpcService.updateAgreementAndJdPrice(dto);
                }
            }
        }catch (Exception e){
            log.error("CustomerSkuPriceBinlakeListenerV2.onMessage",e);
        }

        try {
            log.info("CustomerSkuPriceBinlakeListenerV2.onMessage req:{}" , JSON.toJSONString(eventMsgList));
            List<OpenMsgPO> target = filterAndTransform(eventMsgList);
            if(CollectionUtils.isNotEmpty(target)){
                openMsgManageService.batchAdd(target);
            }
        }catch (Exception e){
            throw new BinlakeConsumeException("CustomerSkuPriceBinlakeListenerV2.onMessage error",e);
        } finally {
            log.info("CustomerSkuPriceBinlakeListenerV2.onMessage end");
        }

    }

    /**
     * 根据 ChangeTableEventMsg 列表过滤和转换为 OpenMsgPO 列表。
     * @param eventMsgList ChangeTableEventMsg 列表，包含要处理的事件消息。
     * @return 过滤和转换后的 OpenMsgPO 列表。
     */
    private List<CountryAgreementPriceReqDTO> filterAndTransformBrData(List<ChangeTableEventMsg> eventMsgList) throws ParseException {
        List<CountryAgreementPriceReqDTO> result = new ArrayList<>();
        for(ChangeTableEventMsg msg : eventMsgList){
            if(WaveEntry.EventType.DELETE.equals(msg.getChangeType()) || WaveEntry.EventType.UPDATE.equals(msg.getChangeType())){
                continue;
            }
            // 币种
            String currency = msg.getRowAfter().get("currency");
            // 是否有效
            String yn = msg.getRowAfter().get("yn");
            if(!CurrencyConstant.CURRENCY_BR.equals(currency) || "0".equals(yn) ){
                continue;
            }
            Map<String, Integer> countryCodeSwitchTypeMap = operDuccConfig.agreementPriceSwitch();
            Integer type = countryCodeSwitchTypeMap.get(CountryConstant.COUNTRY_BR);
            if(type == null || type != 4){
                continue;
            }
            String skuId = msg.getRowAfter().get("sku_id");
            // 更新者
            String updater = msg.getRowAfter().get("updater");
            // 客户code
            String clientCode = msg.getRowAfter().get("client_code");
            // 未税销售价格
            String customerSalePrice = msg.getRowAfter().get("customer_sale_price");

            CountryAgreementPriceReqDTO countryAgreementPrice = new CountryAgreementPriceReqDTO();
            countryAgreementPrice.setSkuId(Long.valueOf(skuId));
            countryAgreementPrice.setTargetCountryCode(CountryConstant.COUNTRY_BR);
            countryAgreementPrice.setAgreementPrice(new BigDecimal(customerSalePrice));
            String mark = updater + "更新"+ clientCode +"客制化价格，同步修改巴西国家协议价";
            countryAgreementPrice.setAgreementMark(mark);
            countryAgreementPrice.setLang(LangConstant.LANG_ZH);
            countryAgreementPrice.setSystemCode(systemCode);
            countryAgreementPrice.setPin(updater);
            result.add(countryAgreementPrice);
        }
        return result;
    }

    private static final Set<String> dbFields = Sets.newHashSet("biz_id", "sku_id", "source_id", "client_code", "currency", "customer_trade_type", "customer_sale_price", "begin_time", "end_time", "enable_status", "end_day", "effective_status");

    /** 消息过滤及转化 */
    private List<OpenMsgPO> filterAndTransform(List<ChangeTableEventMsg> eventMsgList) throws ParseException {
        List<OpenMsgPO> result = new ArrayList<>();
        for(ChangeTableEventMsg msg : eventMsgList){
            if(WaveEntry.EventType.DELETE.equals(msg.getChangeType())){
                continue;
            }
            //初始化一个未生效的vip价不触发消息
            if(WaveEntry.EventType.INSERT.equals(msg.getChangeType()) && "0".equals(msg.getRowAfter().get("effective_status"))){
                continue;
            }
            String skuId = msg.getRowAfter().get("sku_id");
            OpenMsgPO openMsgPO = pricePushManagerService.buildPushData(msg, dbFields, skuId);

            if (openMsgPO != null) {
                result.add(openMsgPO);
            }

//            String updater = msg.getRowAfter().get("updater");
//            String clientCode = msg.getRowAfter().get("client_code");
//            long updateDate = Long.parseLong(msg.getRowAfter().get("update_time"));
//            CustomerVO client = customerManageService.detail(clientCode);
//            Map<String, String> changeFields = msg.getChangeFields();
//
//            if (MapUtils.isEmpty(changeFields)) {
//                log.info("no changeFields, 跳过推送价格数据");
//                continue;
//            }
//
//            long count = dbFields.stream().filter(changeFields::containsKey).count();
//
//            if (count < 1) {
//                log.info("CustomerSkuPriceBinlakeListener.filterAndTransform 价格变更字段为空, 跳过推送价格数据");
//                continue;
//            }
//
//            if(client!=null && client.getOpenSubscribeFlag()){
//                //查看sku对应mku客户是否绑定过,绑了才推送
//                CustomerMkuPricePO customerMkuPrice = customerMkuPriceService.getOne(Long.valueOf(skuId), clientCode);
//                if(customerMkuPrice==null){
//                    log.info("CustomerSkuPriceBinlakeListener.filterAndTransform 客户{} sku{} 不存在mku绑定关系,忽略客制化价格消息" , clientCode , skuId);
//                }else {
//                    result.add(new OpenMsgPO(clientCode, MsgTypeEnum.PRICE.getCode(),JSON.toJSONString(new MkuPriceChangeMsg(customerMkuPrice.getMkuId())),updater,new Date(updateDate)));
//                }
//            }
        }
        return result;
    }

}
