package com.jdi.isc.message.broker.service.msg.wimp;


import com.alibaba.fastjson.JSON;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.message.broker.common.constants.Constant;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.domain.msg.OrderSplitOpenMsg;
import com.jdi.isc.message.broker.domain.msg.order.OrderInfoVO;
import com.jdi.isc.message.broker.domain.msg.order.OrderWareVO;
import com.jdi.isc.message.broker.rpc.stock.IscStockRpcService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockSplitReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 国际订单拆单消息监听
 * @Date 2025-03-13
 */
@Slf4j
@Component
public class OrderSplitMsgForStockListener extends AbsMsgTool<OrderSplitOpenMsg> {

    @Resource
    private IscStockRpcService iscStockRpcService;

    private static final String OPERATOR = Constant.SYSTEM +"-orderSplit";

    @JmqListener(id = "iscProductSoaJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.orderSplit}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<OrderSplitOpenMsg> msg = transform(messages);
        log.info("WImpOrderSplitMsgListener.onMessage req:{}" , JSON.toJSONString(msg));
        if (CollectionUtils.isEmpty(msg)) {
            log.error("WImpOrderSplitMsgListener 消息为空，自动过滤");
            return;
        }

        for(OrderSplitOpenMsg target : msg){
            try {
                // 只有一个子单跳过，未拆单，不处理
                if (Objects.isNull(target.getOrderInfo()) || CollectionUtils.isEmpty(target.getOrderInfo().getChildrenList())
                        || (CollectionUtils.isNotEmpty(target.getOrderInfo().getChildrenList()) && target.getOrderInfo().getChildrenList().size() == 1)){
                    log.warn("WImpOrderSplitMsgListener.onMessage target.getOrderInfo() is empty or only one, reqDTO={}", JSON.toJSONString(target));
                    continue;
                }

                // 多个子单，但是子单号相同时跳过
                List<OrderInfoVO> childrenList = target.getOrderInfo().getChildrenList();
                Set<Long> orderIdSet = childrenList.stream().map(OrderInfoVO::getOrderId).collect(Collectors.toSet());
                if (childrenList.size() > orderIdSet.size()) {
                    log.warn("WImpOrderSplitMsgListener.onMessage childrenList size is not equal orderIdSet size, reqDTO={}", JSON.toJSONString(target));
                    continue;
                }

                // 多个子单，但是子单会有商品为空的情况
                for (OrderInfoVO orderInfoVO : childrenList) {
                    if (CollectionUtils.isEmpty(orderInfoVO.getOrderWareList())) {
                        log.warn("WImpOrderSplitMsgListener.onMessage orderInfoVO.getOrderWareList() is empty, reqDTO={}", JSON.toJSONString(target));
                        throw new RuntimeException("WImpOrderSplitMsgListener 子单："+ orderInfoVO.getOrderId() + "没有商品信息 处理拆单消息失败");
                    }
                }

                StockSplitReqDTO reqDTO = this.fillStockSplitReqDTO(target);
                if (CollectionUtils.isEmpty(reqDTO.getStockManageReqDTOList())) {
                    log.warn("WImpOrderSplitMsgListener.onMessage stockManageReqDTOList is empty, reqDTO={}", JSON.toJSONString(target));
                    continue;
                }
                boolean splitStock = iscStockRpcService.splitStock(reqDTO);
                if (!splitStock) {
                    log.error("WImpOrderSplitMsgListener.onMessage splitStock error, reqDTO={}", JSON.toJSONString(reqDTO));
                    throw new RuntimeException("WImpOrderSplitMsgListener 处理拆单消息失败");
                }
            }catch (Exception e){
                throw new BinlakeConsumeException("WImpOrderSplitMsgListener.onMessage error",e);
            }
        }
    }

    private StockSplitReqDTO fillStockSplitReqDTO(OrderSplitOpenMsg msg){
        StockSplitReqDTO reqDTO = new StockSplitReqDTO();

        List<StockManageReqDTO> stockManageReqDTOList = Lists.newArrayList();

        OrderInfoVO splitOrder = msg.getSplitOrder();
        // 当前被拆单订单ID
        Long currentSplitOrderId = splitOrder.getOrderId();
        List<OrderInfoVO> childrenList = getChildrenList(msg.getOrderInfo(), currentSplitOrderId);
        if (CollectionUtils.isEmpty(childrenList)) {
            log.error("WImpOrderSplitMsgListener.fillStockSplitReqDTO  父单:[{}] 没有子单信息",currentSplitOrderId);
            throw new RuntimeException(String.format("WImpOrderSplitMsgListener 处理拆单消息失败, 父单:[%s] 没有子单信息",currentSplitOrderId));
        }

        for (OrderInfoVO infoVO : childrenList) {
            StockManageReqDTO manageReqDTO = new StockManageReqDTO();
            manageReqDTO.setCountryCode(msg.getCountryCode());
            manageReqDTO.setOrderId(String.valueOf(currentSplitOrderId));
            manageReqDTO.setBizNo(String.valueOf(infoVO.getOrderId()));
            List<StockItemManageReqDTO> itemList = this.fillStockSkuItemList(infoVO.getOrderWareList());
            if (CollectionUtils.isEmpty(itemList)) {
                continue;
            }
            manageReqDTO.setStockItem(itemList);
            stockManageReqDTOList.add(manageReqDTO);
        }
        reqDTO.setStockManageReqDTOList(stockManageReqDTOList);
        return reqDTO;
    }

    /**
     * 递归获取子单信息
     * @param orderInfo
     * @param currentSplitOrderId
     * @return
     */
    private static List<OrderInfoVO> recursionChildrenList(OrderInfoVO orderInfo, Long currentSplitOrderId) {
        if (CollectionUtils.isEmpty(orderInfo.getChildrenList())) {
            return Collections.emptyList();
        }

        if (currentSplitOrderId.equals(orderInfo.getSplitOrderId())) {
            return orderInfo.getChildrenList();
        }
        for (OrderInfoVO orderInfoVO : orderInfo.getChildrenList()) {
            List<OrderInfoVO> list = getChildrenList(orderInfoVO, currentSplitOrderId);
            if (CollectionUtils.isNotEmpty(list)) {
                return list;
            }
        }
        return Collections.emptyList();
    }

    /**
     * 根据拆单订单号过滤出其子单信息
     * @param orderInfoVO 订单信息
     * @param currentSplitOrderId 子单
     * @return
     */
    private static List<OrderInfoVO> getChildrenList(OrderInfoVO orderInfoVO, Long currentSplitOrderId) {
        List<OrderInfoVO> childrenList = orderInfoVO.getChildrenList();
        if (CollectionUtils.isEmpty(childrenList)) {
            return Collections.emptyList();
        }

        return childrenList.stream().filter(vo -> vo.getSplitOrderId().equals(currentSplitOrderId)).collect(Collectors.toList());
    }

    private List<StockItemManageReqDTO> fillStockSkuItemList(List<OrderWareVO> orderWareList) {
        if (CollectionUtils.isEmpty(orderWareList)) {
            return Collections.emptyList();
        }
        List<StockItemManageReqDTO> stockItemManageReqDTOList = new ArrayList<>(orderWareList.size());
        for (OrderWareVO orderWareVO : orderWareList) {
            if (CountryConstant.COUNTRY_ZH.equals(orderWareVO.getMkuCountryCode()) && StringUtils.isBlank(orderWareVO.getWarehouseId())) {
                continue;
            }
            StockItemManageReqDTO stockItemManageReqDTO = new StockItemManageReqDTO();
            stockItemManageReqDTO.setSkuId(orderWareVO.getSkuId());
            stockItemManageReqDTO.setNum(orderWareVO.getMkuNum());
            stockItemManageReqDTO.setWarehouseId(orderWareVO.getWarehouseId());
            stockItemManageReqDTO.setUpdater(OPERATOR);
            stockItemManageReqDTOList.add(stockItemManageReqDTO);
        }
        return stockItemManageReqDTOList;
    }

    public static void main(String[] args) {
        String msg = "{\"clientCode\":\"EJigfasu6Pml0mLtWjFi\",\"contractNum\":\"ISPG-20241210145844\",\"createTime\":1742003400437,\"creator\":\"Cuiyong-SUNWODA\",\"orderInfo\":{\"childrenList\":[{\"orderId\":250314155300005,\"orderTotalPrice\":{\"amount\":107771.5400,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":21597.5800,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":107771.5400,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300001},{\"orderId\":250314160000010,\"orderTotalPrice\":{\"amount\":9439.9200,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":1891.7600,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":9439.9200,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250314160000008,\"orderTotalPrice\":{\"amount\":1737.6000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":348.2000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":1737.6000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250314160000007,\"orderTotalPrice\":{\"amount\":512.6700,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":102.7300,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":512.6700,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250314160000005,\"orderTotalPrice\":{\"amount\":7997.7500,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":1602.7100,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":7997.7500,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250314160000003,\"orderTotalPrice\":{\"amount\":1917.5000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":384.2500,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":1917.5000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250314155300007,\"orderTotalPrice\":{\"amount\":4140.0000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":829.6000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":4140.0000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300001},{\"orderId\":250314155300010,\"orderTotalPrice\":{\"amount\":4041.0000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":809.8000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":4041.0000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300001},{\"orderId\":250314164100004,\"orderTotalPrice\":{\"amount\":353.2800,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":70.8000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":353.2800,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300002},{\"orderId\":250314164100003,\"orderTotalPrice\":{\"amount\":2668.0000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":534.7000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":2668.0000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300002},{\"orderId\":250314160000002,\"orderTotalPrice\":{\"amount\":670.7000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":134.4000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":670.7000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250314164100001,\"orderTotalPrice\":{\"amount\":2173.2000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":435.5000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":2173.2000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300002},{\"orderId\":250314164100002,\"orderTotalPrice\":{\"amount\":867.3300,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":173.8200,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":867.3300,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300002},{\"orderId\":250314160000017,\"orderTotalPrice\":{\"amount\":3221.6000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":645.6000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":3221.6000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250314160000015,\"orderTotalPrice\":{\"amount\":670.7000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":134.4000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":670.7000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250314160000012,\"orderTotalPrice\":{\"amount\":3081.3600,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":617.4800,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":3081.3600,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250314160000013,\"orderTotalPrice\":{\"amount\":1015.5000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":203.5000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":1015.5000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250314160000011,\"orderTotalPrice\":{\"amount\":7740.8800,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":1551.2000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":7740.8800,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250315095000001,\"orderTotalPrice\":{\"amount\":7086.0000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":1420.0000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":7086.0000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314160000009},{\"orderId\":250314155300004,\"orderTotalPrice\":{\"amount\":76715.1000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":15373.7000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":76715.1000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300001},{\"orderId\":250314155300006,\"orderTotalPrice\":{\"amount\":4872.8000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":976.4800,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":4872.8000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300001},{\"orderId\":250314155300008,\"orderTotalPrice\":{\"amount\":2109.6000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":422.8000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":2109.6000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300001},{\"orderId\":250314155300009,\"orderTotalPrice\":{\"amount\":2300.0000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":460.9000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":2300.0000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300001},{\"orderId\":250314160000016,\"orderTotalPrice\":{\"amount\":10090.8000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":2022.0000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":10090.8000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250314160000014,\"orderTotalPrice\":{\"amount\":1167.7000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":234.0000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":1167.7000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250314160000006,\"orderTotalPrice\":{\"amount\":888.2200,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":178.0000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":888.2200,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250314160000004,\"orderTotalPrice\":{\"amount\":78456.5200,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":15722.6400,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":78456.5200,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250314160000001,\"orderTotalPrice\":{\"amount\":670.7000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":134.4000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":670.7000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003},{\"orderId\":250315095000002,\"orderTotalPrice\":{\"amount\":13199.0000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":2645.0000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":13199.0000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314160000009}],\"orderId\":250314155300001,\"orderTotalPrice\":{\"amount\":357576.9700,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":71657.9500,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":234752.9248,\"currency\":\"THB\"},\"parentOrderId\":0,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":0},\"pOrder\":\"250314155300001\",\"pin\":\"Cuiyong-SUNWODA\",\"splitOrder\":{\"orderId\":250314160000009,\"orderTotalPrice\":{\"amount\":20285.0000,\"currency\":\"THB\"},\"orderTotalPriceRmb\":{\"amount\":4065.0000,\"currency\":\"CNY\"},\"orderTotalPriceSource\":{\"amount\":20285.0000,\"currency\":\"THB\"},\"parentOrderId\":250314155300001,\"pin\":\"Cuiyong-SUNWODA\",\"splitOrderId\":250314155300003,\"splitTime\":1742003400437}}";
        OrderSplitOpenMsg openMsg = JSON.parseObject(msg, OrderSplitOpenMsg.class);

        OrderInfoVO splitOrder = openMsg.getSplitOrder();
        // 当前被拆单订单ID
        Long currentSplitOrderId = splitOrder.getOrderId();
        List<OrderInfoVO> childrenList = getChildrenList(openMsg.getOrderInfo(), currentSplitOrderId);
        System.out.println(JSON.toJSONString(childrenList));
    }
}
