package com.jdi.isc.message.broker.service.atomic.stock;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.message.broker.domain.enums.YnEnum;
import com.jdi.isc.message.broker.domain.stock.WarehousePO;
import com.jdi.isc.message.broker.repository.jed.mapper.stock.WarehouseBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author：xubing82
 * @date：2025/6/12 20:51
 * @description：WarehouseAtomicService
 */
@Slf4j
@Service
public class WarehouseAtomicService extends ServiceImpl<WarehouseBaseMapper, WarehousePO> {

    /**
     * 根据仓库ID获取启用状态的仓库信息。
     */
    public WarehousePO getWarehouseByWhId(Long warehouseId) {
        LambdaQueryWrapper<WarehousePO> wrapper = Wrappers.lambdaQuery(WarehousePO.class)
                .eq(WarehousePO::getId, warehouseId)
                .eq(WarehousePO::getStatus, 1)
                .eq(WarehousePO::getYn, YnEnum.YES.getCode());
        List<WarehousePO> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

}
