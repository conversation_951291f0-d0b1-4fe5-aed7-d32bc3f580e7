package com.jdi.isc.message.broker.service.msg.es;

import com.alibaba.fastjson.JSON;
import com.jd.binlog.client.WaveEntry;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.domain.msg.po.OpenMsgPO;
import com.jdi.isc.message.broker.service.manage.mku.MkuManageService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientDetailReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * mku基本信息binlake消息监听
 * <AUTHOR>
 * @date 20250627
 */
@Slf4j
@Component("mkuEsBinlakeListener")
public class MkuEsBinlakeListener extends AbsMsgTool<OpenMsgPO> {

    @Resource
    private MkuManageService mkuManageService;

    @JmqListener(id = "iscMessageesJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.mku}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<ChangeTableEventMsg> eventMsgList = parse(messages);
        try {
            log.info("MkuEsBinlakeListener.onMessage req:{}" , JSON.toJSONString(eventMsgList));
            List<MkuClientDetailReqApiDTO> target = filterAndTransform(eventMsgList);
            if(CollectionUtils.isNotEmpty(target)){
                mkuManageService.upsertToEs(target);
            }
        }catch (Exception e){
            throw new BinlakeConsumeException("MkuEsBinlakeListener.onMessage error",e);
        }
    }

    /** 消息过滤及转化 */
    private List<MkuClientDetailReqApiDTO> filterAndTransform(List<ChangeTableEventMsg> eventMsgList) {
        List<MkuClientDetailReqApiDTO> result = new ArrayList<>();
        for(ChangeTableEventMsg msg : eventMsgList){
            if(WaveEntry.EventType.DELETE.equals(msg.getChangeType())){
                continue;
            }
            if(!(msg.getChangeFields().containsKey("mku_status")
                    || msg.getChangeFields().containsKey("cat_id")
                    || msg.getChangeFields().containsKey("jd_cat_id") )){
                continue;
            }
            String mkuId = msg.getRowAfter().get("mku_id");
            List<MkuClientDetailReqApiDTO> res = mkuManageService.fissionForMku(Long.valueOf(mkuId));
            if(CollectionUtils.isNotEmpty(res)){
                result.addAll(res);
            }
        }
        return result;
    }

//            {
//                "changeFields":
//                {
//                    "update_time": "2024-01-08 14:30:29",
//                        "mku_status": "1",
//                        "brand_id": "8014"
//                },
//                "changeType": "UPDATE",
//                    "dataBase": "jdi_intl_product",
//                    "rowAfter":
//                {
//                    "mku_id": "50000002294",
//                        "source_country_code": "CN",
//                        "cat_id": "1127",
//                        "brand_id": "8014",
//                        "main_img": "http://jdi-intl.s3.cn-north-1.jdcloud-oss.com/ks1/1704684858441.png?x-oss-process=img/cc/100/100",
//                        "ext_attribute": "100078:102800#100057:102314#100060:102320,100060:102321,100060:102322,100060:102323,100060:102324#100080:102900,100080:102901#100055:102310#100079:102802,100079:102803,100079:102804",
//                        "sale_attribute": "100075:102700#100076:102704#100077:102709",
//                        "weight": "243.0",
//                        "length": "20.0",
//                        "width": "30.0",
//                        "height": "50.0",
//                        "buyer": "fujiebin1",
//                        "mku_status": "1",
//                        "creator": "system",
//                        "updater": "yuyang63",
//                        "create_time": "2024-01-08 11:35:34",
//                        "update_time": "2024-01-08 14:30:29",
//                        "yn": "1"
//                },
//                "rowBefore":
//                {
//                    "mku_id": "50000002294",
//                        "source_country_code": "CN",
//                        "cat_id": "1127",
//                        "brand_id": "8017",
//                        "main_img": "http://jdi-intl.s3.cn-north-1.jdcloud-oss.com/ks1/1704684858441.png?x-oss-process=img/cc/100/100",
//                        "ext_attribute": "100078:102800#100057:102314#100060:102320,100060:102321,100060:102322,100060:102323,100060:102324#100080:102900,100080:102901#100055:102310#100079:102802,100079:102803,100079:102804",
//                        "sale_attribute": "100075:102700#100076:102704#100077:102709",
//                        "weight": "243.0",
//                        "length": "20.0",
//                        "width": "30.0",
//                        "height": "50.0",
//                        "buyer": "fujiebin1",
//                        "mku_status": "2",
//                        "creator": "system",
//                        "updater": "yuyang63",
//                        "create_time": "2024-01-08 11:35:34",
//                        "update_time": "2024-01-08 14:30:18",
//                        "yn": "1"
//                },
//                "tableName": "jdi_isc_mku_sharding"
//            }
}
