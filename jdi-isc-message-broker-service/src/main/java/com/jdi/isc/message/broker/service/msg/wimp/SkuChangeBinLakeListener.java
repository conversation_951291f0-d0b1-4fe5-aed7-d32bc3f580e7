package com.jdi.isc.message.broker.service.msg.wimp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.jd.binlog.client.WaveEntry;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.domain.changeRecord.ChangeRecordPO;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.domain.customer.CustomerVO;
import com.jdi.isc.message.broker.domain.enums.MsgTypeEnum;
import com.jdi.isc.message.broker.domain.mku.CustomerMkuPricePO;
import com.jdi.isc.message.broker.domain.msg.MkuChangeMsg;
import com.jdi.isc.message.broker.domain.msg.po.OpenMsgPO;
import com.jdi.isc.message.broker.rpc.price.IscProductSoaPriceWriteRpcService;
import com.jdi.isc.message.broker.rpc.sku.IscProductSoaSkuReadRpcService;
import com.jdi.isc.message.broker.service.manage.changeRecord.ChangeRecordManageService;
import com.jdi.isc.message.broker.service.manage.customer.CustomerManageService;
import com.jdi.isc.message.broker.service.manage.mku.CustomerMkuPriceService;
import com.jdi.isc.message.broker.service.manage.mku.MkuManageService;
import com.jdi.isc.message.broker.service.manage.msg.OpenMsgManageService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.common.CountryConstant;
import com.jdi.isc.product.soa.api.common.enums.AgreementPriceDataSourceTypeEnums;
import com.jdi.isc.product.soa.api.sku.req.QuerySkuReqDTO;
import com.jdi.isc.product.soa.api.sku.res.SkuBaseInfoApiDTO;
import com.jdi.isc.product.soa.price.api.price.req.PriceRefreshVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.jd.jmq.common.message.Message;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @description：监听 sku 基础表变更消息，并将变更记录保存到数据库
 * @Date 20250226
 */
@Slf4j
@Component
public class SkuChangeBinLakeListener extends AbsMsgTool<OpenMsgPO> {

    // 定义忽略字段集合
    private final Set<String> IGNORE_FIELDS = Sets.newHashSet(
            "spu_id", "sku_id", "id", "remark", "creator",
            "updater", "create_time", "update_time");

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Resource
    private ChangeRecordManageService changeRecordManageService;
    @Resource
    private IscProductSoaPriceWriteRpcService iscProductSoaPriceWriteRpcService;
    @Resource
    private CustomerMkuPriceService customerMkuPriceService;
    @Resource
    private OpenMsgManageService openMsgManageService;
    @Resource
    private IscProductSoaSkuReadRpcService iscProductSoaSkuReadRpcService;
    @Resource
    private CustomerManageService customerManageService;

    @Resource
    private MkuManageService mkuManageService;

    /**
     * 处理从JMQ接收到的消息并保存到数据库中。
     * @param messages 接收到的消息列表。
     * @throws BinlakeConsumeException 如果处理消息时发生错误。
     */
    @JmqListener(id = "iscIntlJmq4Consumer", topics = {"${topic.jmq4.consumer.isc.sku}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<ChangeTableEventMsg> eventMsgList = parse(messages);
        try {
            log.info("SkuChangeBinLakeListener.onMessage req:{}", JSON.toJSONString(eventMsgList));


            // 回填ncmCode
            mkuManageService.updateSkuNcmCode(eventMsgList);

            List<ChangeRecordPO> target = filterAndTransform(eventMsgList);
            changeRecordManageService.saveChangeRecord(target);
        } catch (Exception e) {
            log.error("SkuChangeBinLakeListener.onMessage invoke error, target:{} ", JSON.toJSONString(eventMsgList),e);
        }
    }


    /** 监听sku体积变更,因履约费率需重算要间距触发成本价重算*/
    @JmqListener(id = "iscProductSoaJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.baseSku}"})
    @PFTracing
    public void onVolumeChangeMessage(List<Message> messages) throws Exception {
        List<ChangeTableEventMsg> eventMsgList = parse(messages);
        try {
            // 体积或者hs_code变动，触发成本价重算
            List<PriceRefreshVO> target = filterVolumeChangeAndTransform(eventMsgList);
            if(CollectionUtils.isNotEmpty(target)){
                for(PriceRefreshVO vo : target){
                    Boolean res = iscProductSoaPriceWriteRpcService.updateCostPrice(vo);
                    log.info("SkuChangeBinLakeListener.onVolumeChangeMessage req:{} , res:{}" , JSON.toJSONString(vo) , res);
                }
            }
        }catch (Exception e){
            throw new RuntimeException("SkuChangeBinLakeListener.onVolumeChangeMessage error" + JSON.toJSONString(eventMsgList));
        }
    }

    //过滤体积变更消息
    private List<PriceRefreshVO> filterVolumeChangeAndTransform(List<ChangeTableEventMsg> eventMsgList) {
        List<PriceRefreshVO> result = new ArrayList<>();
        if (null == eventMsgList || eventMsgList.isEmpty()) {
            return result;
        }
        for (ChangeTableEventMsg eventMsg : eventMsgList) {
            Set<String> changeKey = eventMsg.getChangeFields().keySet();
            //忽略非更新消息
            if (!WaveEntry.EventType.UPDATE.equals(eventMsg.getChangeType())) {
                continue;
            }
            //忽略归档品
            if ("0".equals(eventMsg.getRowAfter().get("yn"))) {
                continue;
            }
            //忽略非跨境品消息
            if (!"CN".equals(eventMsg.getRowAfter().get("source_country_code"))) {
                continue;
            }
            //忽略非长宽高变更的消息
            if(!changeKey.contains("length") && !changeKey.contains("width") && !changeKey.contains("height") && !changeKey.contains("hs_code") && !changeKey.contains("cat_id") && !changeKey.contains("jd_cat_id")){
                continue;
            }
            PriceRefreshVO priceRefreshVO = new PriceRefreshVO();

            if(changeKey.contains("length") || changeKey.contains("width") || changeKey.contains("height") ){
                priceRefreshVO.setDataStatusSource(AgreementPriceDataSourceTypeEnums.SKU_VOLUME.getCode());
            }


            if(changeKey.contains("hs_code") ){
                priceRefreshVO.setDataStatusSource(AgreementPriceDataSourceTypeEnums.SKU_HS_CODE.getCode());
            }

            if(changeKey.contains("cat_id") || changeKey.contains("jd_cat_id") ){
                priceRefreshVO.setDataStatusSource(AgreementPriceDataSourceTypeEnums.SKU_CAT.getCode());
            }


            priceRefreshVO.setSkuId(Long.valueOf(eventMsg.getRowAfter().get("sku_id")));
            priceRefreshVO.setSourceCountryCode(CountryConstant.COUNTRY_ZH);
            result.add(priceRefreshVO);
        }
        return result;
    }

    /** 消息过滤及转化 */
    private List<ChangeRecordPO> filterAndTransform(List<ChangeTableEventMsg> eventMsgList) {
        List<ChangeRecordPO> res = new ArrayList<>();

        if (null == eventMsgList || eventMsgList.isEmpty()) {
            return res;
        }

        for (ChangeTableEventMsg eventMsg : eventMsgList) {
            if (WaveEntry.EventType.DELETE.equals(eventMsg.getChangeType()) || WaveEntry.EventType.INSERT.equals(eventMsg.getChangeType())) {
                continue;
            }

            if ("0".equals(eventMsg.getRowAfter().get("yn"))) {
                continue;
            }

            Long spuId = Long.valueOf(eventMsg.getRowAfter().get("spu_id"));
            Long skuId = Long.valueOf(eventMsg.getRowAfter().get("sku_id"));

            for (Map.Entry<String, String> entry : eventMsg.getChangeFields().entrySet()) {
                String field = entry.getKey();
                if (IGNORE_FIELDS.contains(field)) {
                    continue;
                }

                String newValue = entry.getValue();
                String oldValue = eventMsg.getRowBefore().get(field);

                ChangeRecordPO changeRecord = new ChangeRecordPO(
                        spuId,
                        skuId,
                        eventMsg.getTableName(),
                        field,
                        oldValue,
                        newValue,
                        eventMsg.getRowAfter().get("remark"),
                        eventMsg.getRowAfter().get("creator"),
                        eventMsg.getRowAfter().get("updater"),
                        parseDateTime(eventMsg.getRowAfter().get("create_time")),
                        parseDateTime(eventMsg.getRowAfter().get("update_time")),
                        Integer.valueOf(eventMsg.getRowAfter().get("yn"))
                );
                res.add(changeRecord);
            }
        }
        return res;
    }

    /**
     * 解析日期时间字符串为毫秒数。
     * @param dateTimeStr 日期时间字符串，格式必须符合DATE_FORMAT定义。
     * @return 解析后的日期时间的毫秒数。
     */
    private static Long parseDateTime(String dateTimeStr) {
        try {
            return DATE_FORMAT.parse(dateTimeStr).getTime();
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /** 监听本土品sku Moq变更,触发下游wiop变更消息下发*/
    @JmqListener(id = "iscMessageesJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.baseSku}"})
    @PFTracing
    public void onSkuChangeMessage(List<Message> messages) throws Exception {
        List<ChangeTableEventMsg> eventMsgList = parse(messages);
        try {
            List<OpenMsgPO> target = filterSkuChangeAndTransform(eventMsgList);
            if(CollectionUtils.isNotEmpty(target)){
                openMsgManageService.batchAdd(target);
            }
        }catch (Exception e){
            log.error("SkuChangeBinLakeListener.onSkuChangeMessage invoke error", e);
            throw new RuntimeException("SkuChangeBinLakeListener.onSkuChangeMessage error" + JSON.toJSONString(eventMsgList));
        }
    }

    /** 监听跨境品sku Moq变更,触发下游wiop变更消息下发*/
    @JmqListener(id = "iscProductSoaJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.gdSpuPropertyChange}"})
    @PFTracing
    public void onGdSkuChangeMessage(List<Message> messages) {
        try {
            List<OpenMsgPO> target = filterJdSkuChangeAndTransform(messages);
            if(CollectionUtils.isNotEmpty(target)){
                openMsgManageService.batchAdd(target);
                log.info("SkuChangeBinLakeListener.onGdSkuChangeMessage req {}" ,  JSON.toJSONString(target));
            }
        }catch (Exception e){
            log.error("SkuChangeBinLakeListener.onGdSkuChangeMessage invoke error", e);
            throw new RuntimeException("SkuChangeBinLakeListener.onGdSkuChangeMessage error");
        }
    }

    //跨境品moq变更处理
    private List<OpenMsgPO> filterJdSkuChangeAndTransform(List<Message> messages) {
        Date now = new Date();
        List<OpenMsgPO> result = new ArrayList<>();
        String propertyValues = "propertyValues";
        String lowestBuy = "LowestBuy";
        Set<Long> jdSkuId = new HashSet<>();
        for (Message message : messages) {
            String json = message.getText();
            JSONObject msgJson = JSON.parseObject(json);
            if(!"UPDATE".equals(msgJson.getString("changeType"))){
                continue;
            }
            JSONArray changeFieldsArray = msgJson.getJSONArray("changeFields");
            if(changeFieldsArray==null || changeFieldsArray.size()<1 || !changeFieldsArray.contains(propertyValues)){
                continue;
            }
            JSONObject beforeData = msgJson.getObject("beforeData", JSONObject.class);
            JSONObject afterData = msgJson.getObject("afterData", JSONObject.class);
            JSONObject beforeProperties = beforeData!=null?JSONObject.parseObject(beforeData.getString(propertyValues)):null;
            JSONObject afterProperties = afterData!=null?JSONObject.parseObject(afterData.getString(propertyValues)):null;
            String beforeLowestBuy = beforeProperties!=null && StringUtils.isNotBlank(beforeProperties.getString(lowestBuy)) ? beforeProperties.getString(lowestBuy) :"";
            String afterLowestBuy = afterProperties!=null && StringUtils.isNotBlank(afterProperties.getString(lowestBuy)) ? afterProperties.getString(lowestBuy) :"";
            if (beforeLowestBuy.equals(afterLowestBuy)) {
                continue;
            }
            jdSkuId.add(msgJson.getLong("skuId"));
        }
        if(CollectionUtils.isEmpty(jdSkuId)){
            return null;
        }
        //jdSku查国际sku
        QuerySkuReqDTO req =  new QuerySkuReqDTO();
        req.setJdSkuIds(jdSkuId);
        List<SkuBaseInfoApiDTO> iscSku = iscProductSoaSkuReadRpcService.querySkuInfoByJdSkuId(req);
        if(CollectionUtils.isEmpty(iscSku)){
            return null;
        }
        for(SkuBaseInfoApiDTO sku : iscSku){
            //国际sku查有入客户池的mku(会命中多个,因为sku对应的mku会被多个客户绑定)
            List<CustomerMkuPricePO> customerMku = customerMkuPriceService.listByFixedSku(sku.getSkuId());
            if (CollectionUtils.isEmpty(customerMku)) {
                log.info("SkuChangeBinLakeListener.filterJdSkuChangeAndTransform sku{}非固定sku,忽略sku moq更新" , sku.getSkuId());
                continue;
            }
            //消息组装(因为mku可能被多个WIOP客户绑定,都需要触发消息下发)
            for(CustomerMkuPricePO mku : customerMku){
                CustomerVO client = customerManageService.detail(mku.getClientCode());
                if(client!=null && client.getOpenSubscribeFlag()){
                    result.add(new OpenMsgPO(mku.getClientCode(), MsgTypeEnum.MKU_INFO.getCode(),JSON.toJSONString(new MkuChangeMsg(mku.getMkuId(), WaveEntry.EventType.UPDATE)),mku.getUpdater(),now));
                }
            }
        }
        return result;
    }

    //本土品moq变更处理
    private List<OpenMsgPO> filterSkuChangeAndTransform(List<ChangeTableEventMsg> eventMsgList) throws ParseException {
        List<OpenMsgPO> result = new ArrayList<>();
        if (null == eventMsgList || eventMsgList.isEmpty()) {
            return result;
        }
        for (ChangeTableEventMsg eventMsg : eventMsgList) {
            Set<String> changeKey = eventMsg.getChangeFields().keySet();
            //忽略非更新消息
            if (!WaveEntry.EventType.UPDATE.equals(eventMsg.getChangeType())) {
                continue;
            }
            //忽略跨境品消息(moq变更只关注本土品,如果后续升级监听其他字段变更注意注释此逻辑)
            if ("CN".equals(eventMsg.getRowAfter().get("source_country_code"))) {
                continue;
            }
            //只关注moq变更动作
            if(!changeKey.contains("moq")){
                continue;
            }
            Long skuId = Long.valueOf(eventMsg.getRowAfter().get("sku_id"));
            String updater = eventMsg.getRowAfter().get("updater");
            String updateDate = eventMsg.getRowAfter().get("update_time");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //国际sku查有入客户池的mku(会命中多个,因为sku对应的mku会被多个客户绑定)
            List<CustomerMkuPricePO> customerMku = customerMkuPriceService.listByFixedSku(skuId);
            if (CollectionUtils.isEmpty(customerMku)) {
                log.info("SkuChangeBinLakeListener.filterSkuChangeAndTransform sku{}非固定sku,忽略sku moq更新" , skuId);
                continue;
            }
            //消息组装(因为mku可能被多个WIOP客户绑定,都需要触发消息下发)
            for(CustomerMkuPricePO mku : customerMku){
                CustomerVO client = customerManageService.detail(mku.getClientCode());
                if(client!=null && client.getOpenSubscribeFlag()){
                    result.add(new OpenMsgPO(mku.getClientCode(), MsgTypeEnum.MKU_INFO.getCode(),
                            JSON.toJSONString(new MkuChangeMsg(mku.getMkuId(), WaveEntry.EventType.UPDATE)),updater,sdf.parse(updateDate)));
                }
            }
        }
        return result;
    }

}
