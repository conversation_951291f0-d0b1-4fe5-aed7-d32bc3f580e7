package com.jdi.isc.message.broker.service.msg.wimp;

import com.alibaba.fastjson.JSON;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.message.broker.common.constants.CacheKeyConstant;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.common.util.JimUtils;
import com.jdi.isc.message.broker.domain.enums.SplitOrderTypeEnum;
import com.jdi.isc.message.broker.domain.msg.ForecastOrderSplitOpenMsg;
import com.jdi.isc.message.broker.domain.msg.ForecastOrderWareOpenMsg;
import com.jdi.isc.message.broker.rpc.stock.IscStockRpcService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.stock.req.StockItemManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockManageReqDTO;
import com.jdi.isc.product.soa.api.stock.req.StockSplitReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.jdi.isc.message.broker.common.constants.CacheKeyConstant.FORECAST_ORDER_SPLIT_MSG;

/**
 * @Author：xubing82
 * @Date：2025/5/21 11:18
 * @Description：ForecastOrderSplitMqListener预报备单拆单消息监听处理
 */
@Slf4j
@Component
public class ForecastOrderSplitMqListener extends AbsMsgTool<ForecastOrderSplitOpenMsg> {

    @Resource
    private IscStockRpcService iscStockRpcService;

    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private JimUtils jimUtils;

    private static final String LOG_PREFIX = "[ForecastOrderSplit]";

    @JmqListener(id = "iscProductSoaJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.forecastOrderSplit}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        try {
            List<ForecastOrderSplitOpenMsg> msgs = transform(messages);

            for (ForecastOrderSplitOpenMsg msg : msgs) {
                log.info("{} 开始处理消息，预报备单ID: {}", LOG_PREFIX, msg.getForecastOrderId());

                String key = CacheKeyConstant.getKey(FORECAST_ORDER_SPLIT_MSG, msg.getForecastOrderId());

                processWithLock(key, () -> handleBusinessLogic(msg, key));
            }
        } catch (Exception e) {
            log.error("WimpForecastOrderSplitMsgListener.onMessage error", e);
            throw new BinlakeConsumeException("WimpForecastOrderSplitMsgListener.onMessage error", e);
        }
    }


    /**
     * 处理预报备单拆单业务逻辑，包括库存流水拆分请求封装和执行拆分操作
     *
     * @param msg 预报备单拆单消息对象，包含拆单所需数据
     * @param key 消息队列键值，用于日志追踪
     */
    private void handleBusinessLogic(ForecastOrderSplitOpenMsg msg, String key) {

        //1、库存流水拆分请求流水封装
        StockSplitReqDTO req = fillOrderStockSplitReqDTO(msg);

        //2、执行库存流水拆分
        if (req != null && CollectionUtils.isNotEmpty(req.getStockManageReqDTOList())) {
            Boolean splitResult = iscStockRpcService.splitForecastOrderStock(req);
            if (!splitResult) {
                log.warn("{} 库存流水拆分失败，预报备单ID: {}", LOG_PREFIX, msg.getForecastOrderId());
                throw new BinlakeConsumeException("预报备单拆单库存流水拆分失败！");
            }
        }

        log.info("{} 库存流水拆分处理结束，key: {}", LOG_PREFIX, key);
    }

    /**
     * 根据预报备订单拆单消息填充库存拆单请求DTO
     *
     * @param openMsg 预报备订单拆单消息，包含拆单类型、子单据列表等信息
     * @return 库存拆单请求DTO，包含需要拆分的库存管理请求列表；若消息无效则返回null
     */
    private StockSplitReqDTO fillOrderStockSplitReqDTO(ForecastOrderSplitOpenMsg openMsg) {
        List<ForecastOrderSplitOpenMsg> childForecastOrderList = openMsg.getChildForecastOrderList();
        if (CollectionUtils.isEmpty(childForecastOrderList)) {
            log.error("该消息信息，获取子单据异常:{}", JSON.toJSONString(openMsg));
            return null;
        }

        if (SplitOrderTypeEnum.SPLIT_ORDER.getType() != openMsg.getSplitType()) {
            log.error("预报备单拆单消息，非拆单消息不处理，预报备货单号:{},拆单类型:[{}]", openMsg.getForecastOrderId(), openMsg.getSplitType());
            return null;
        }

        StockSplitReqDTO reqDTO = new StockSplitReqDTO();
        List<StockManageReqDTO> stockLists = new ArrayList<>();
        for (ForecastOrderSplitOpenMsg msg : childForecastOrderList) {
            //bizNo 子单
            List<ForecastOrderWareOpenMsg> forecastOrderWareVOList = msg.getForecastOrderWarePOList();
            if (CollectionUtils.isEmpty(forecastOrderWareVOList)) {
                log.error("该消息信息，获取商品信息数据异常:{}", JSON.toJSONString(msg));
                continue;
            }

            stockLists.add(getStockManageReqDTO(msg, forecastOrderWareVOList));
        }

        if (CollectionUtils.isNotEmpty(stockLists)) {
            reqDTO.setStockManageReqDTOList(stockLists);
        }
        return reqDTO;
    }

    /**
     * 根据预测订单拆分消息和商品列表生成库存管理请求DTO
     *
     * @param msg             预测订单拆分消息，包含预测订单ID、拆分订单ID和企业仓库ID等信息
     * @param orderWareVOList 预测订单商品列表，包含SKU信息、数量和更新人等数据
     * @return 组装完成的库存管理请求DTO对象，包含业务单号、订单号和库存明细项列表
     */
    private StockManageReqDTO getStockManageReqDTO(ForecastOrderSplitOpenMsg msg, List<ForecastOrderWareOpenMsg> orderWareVOList) {
        List<StockItemManageReqDTO> manages = new ArrayList<>();
        StockManageReqDTO stockManageReqDTO = new StockManageReqDTO();
        stockManageReqDTO.setBizNo(msg.getForecastOrderId());
        stockManageReqDTO.setOrderId(msg.getSplitForecastOrderId());
        for (ForecastOrderWareOpenMsg wareOpenMsg : orderWareVOList) {

            StockItemManageReqDTO manageReqDTO = new StockItemManageReqDTO();
            manageReqDTO.setNum(Long.valueOf(wareOpenMsg.getSkuNum()));
            manageReqDTO.setUpdater(wareOpenMsg.getUpdater());
            manageReqDTO.setSkuId(wareOpenMsg.getSkuId());
            manageReqDTO.setWarehouseId(msg.getEnterpriseWarehouseId());
            manages.add(manageReqDTO);
        }
        stockManageReqDTO.setStockItem(manages);
        return stockManageReqDTO;
    }

    /**
     * 使用分布式锁执行业务逻辑
     *
     * @param key     分布式锁的键
     * @param process 需要加锁执行的业务逻辑
     */
    private void processWithLock(String key, Runnable process) {
        String uuid = generateLockId();
        try {
            if (!jimUtils.simpleLock(key, uuid, 20)) {
                log.warn("{} 获取锁失败，key: {}", LOG_PREFIX, key);
                throw new BinlakeConsumeException("幂等/系统繁忙，请重试" + key);
            }
            process.run();
        } finally {
            jimUtils.simpleLockRelease(key, uuid);
        }
    }

    /**
     * 生成一个唯一的锁标识符
     *
     * @return 返回一个基于UUID生成的唯一字符串作为锁ID
     */
    private String generateLockId() {
        return UUID.randomUUID().toString();
    }


}
