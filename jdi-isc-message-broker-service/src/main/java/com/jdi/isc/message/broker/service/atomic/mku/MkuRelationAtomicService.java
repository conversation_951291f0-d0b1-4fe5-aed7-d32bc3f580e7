package com.jdi.isc.message.broker.service.atomic.mku;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.message.broker.domain.enums.MkuRelationBindStatusEnum;
import com.jdi.isc.message.broker.domain.enums.YnEnum;
import com.jdi.isc.message.broker.domain.mku.MkuRelationPO;
import com.jdi.isc.message.broker.repository.jed.mapper.mku.MkuRelationBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author：xubing82
 * @date：2025/6/12 20:20
 * @description：MkuRelationAtomicService
 */
@Service
public class MkuRelationAtomicService  extends ServiceImpl<MkuRelationBaseMapper, MkuRelationPO> {

    public Map<Long,Long> queryMkuIdBySkuIds(Set<Long> skuIdSet){
        if (CollectionUtils.isEmpty(skuIdSet)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<MkuRelationPO> wrapper = Wrappers.<MkuRelationPO>lambdaQuery()
                .select(MkuRelationPO::getSkuId,MkuRelationPO::getMkuId)
                .in(MkuRelationPO::getSkuId, skuIdSet)
                .eq(MkuRelationPO::getBindStatus, MkuRelationBindStatusEnum.BIND.getCode())
                .eq(MkuRelationPO::getYn, YnEnum.YES.getCode());

        List<MkuRelationPO> mkuRelationPOList = super.getBaseMapper().selectList(wrapper);

        return Optional.ofNullable(mkuRelationPOList).orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(MkuRelationPO::getSkuId, MkuRelationPO::getMkuId));

    }
}
