package com.jdi.isc.message.broker.service.msg.wimp.supplier;


import com.alibaba.fastjson.JSON;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.common.exception.MqConsumeException;
import com.jdi.isc.message.broker.domain.supplier.SupplierContractStatusMsg;
import com.jdi.isc.message.broker.service.manage.sku.SkuManageService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 供应商合同状态变更
 * <AUTHOR>
 * @description：SupplierContractValidListener
 * @Date 2025-07-14
 */
@Slf4j
@Service
public class SupplierContractValidListener extends AbsMsgTool<SupplierContractStatusMsg> {

    @Value("${spring.profiles.active}")
    private String systemEnv;

    @Resource
    private SkuManageService skuManageService;

    /**
     * 处理从JMQ接收到的消息并保存到数据库中。
     * @param messages 接收到的消息列表。
     * @throws BinlakeConsumeException 如果处理消息时发生错误。
     */
    @JmqListener(id = "iscProductSoaJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.supplierStatus}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<SupplierContractStatusMsg> eventMsgList = transform(messages);

        CallerInfo callerInfo = Profiler.registerInfo(systemEnv + "-SupplierContractValidListener.onMessage");

        try {
            for (SupplierContractStatusMsg eventMsg : eventMsgList) {
                log.info("SupplierContractValidListener.onMessage 供应商状态消息体 req:{}", JSON.toJSONString(eventMsg));;
                if (Objects.nonNull(eventMsg) && !eventMsg.getValid()){
                    skuManageService.updateSkuStatusBySupplierCode(eventMsg.getSupplierCode());
                }
            }
            log.info("SupplierContractValidListener.onMessage req:{}", JSON.toJSONString(eventMsgList));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            Profiler.functionError(callerInfo);
            throw new MqConsumeException("SupplierContractValidListener.onMessage error", e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }
}
