package com.jdi.isc.message.broker.service.manage.mku;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.message.broker.domain.enums.YnEnum;
import com.jdi.isc.message.broker.domain.mku.CustomerMkuPO;
import com.jdi.isc.message.broker.repository.jed.mapper.mku.CustomerMkuBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 消息读写服务
 * <AUTHOR>
 * @date 20240108
 */
@Service
@Slf4j
public class CustomerMkuManageService extends ServiceImpl<CustomerMkuBaseMapper, CustomerMkuPO> {

    public List<CustomerMkuPO> list(Long mkuId){
        LambdaQueryWrapper<CustomerMkuPO> wrapper = Wrappers.<CustomerMkuPO>lambdaQuery()
                .eq(CustomerMkuPO::getMkuId, mkuId)
                .eq(CustomerMkuPO::getBindStatus, "BIND")
                .eq(CustomerMkuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(wrapper);
    }

    public List<CustomerMkuPO> list(Set<Long> mkuId){
        LambdaQueryWrapper<CustomerMkuPO> wrapper = Wrappers.<CustomerMkuPO>lambdaQuery()
                .in(CustomerMkuPO::getMkuId, mkuId)
                .eq(CustomerMkuPO::getBindStatus, "BIND")
                .eq(CustomerMkuPO::getYn, YnEnum.YES.getCode());
        return super.getBaseMapper().selectList(wrapper);
    }

    public CustomerMkuPO getOne(String clientCode,Long mkuId){
        LambdaQueryWrapper<CustomerMkuPO> wrapper = Wrappers.<CustomerMkuPO>lambdaQuery()
                .eq(CustomerMkuPO::getClientCode, clientCode)
                .eq(CustomerMkuPO::getMkuId, mkuId)
                .eq(CustomerMkuPO::getBindStatus, "BIND")
                .eq(CustomerMkuPO::getYn, YnEnum.YES.getCode());
        return getOne(wrapper);
    }

}
