package com.jdi.isc.message.broker.service.manage.mku;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.domain.msg.*;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientDetailReqApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuEsDetailReqApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuEsStockTagReqDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuPromiseTagDTO;

import java.util.List;

public interface MkuManageService {

    DataResponse<Boolean> createOrderOutVerifyInfo(List<OrderOutVerifyMsg> msg);

    DataResponse<Boolean> updateSpuSkuMku(SkuInfoMsg skuInfoMsg);

    DataResponse<Boolean> mkuJoinCountryPool(List<MkuChangeMsg> mkuChangeMsgs);

    DataResponse<Boolean> jdSkuIdJoinCountryPool(List<SkuInfoMsg> skuInfoMsgs);

    Boolean upsertToEs(List<MkuClientDetailReqApiDTO> apiDTOS);

    Boolean updateMkuFeatureTag(MkuFeatureTagVO t);

    /**
     * 根据查询参数获取MkuClientEsVO信息
     * @param reqVo 包含查询条件的请求参数对象
     * @return 符合查询条件的MkuClientEsVO结果
     */
    MkuClientEsVO queryMkuEsByParam(MkuEsDetailReqApiDTO reqVo);

    /**
     * 更新MkuEs库存标签
     * @param reqVo MkuEs库存标签请求数据传输对象
     * @return 操作是否成功
     */
    Boolean updateMkuEsStockTag(MkuEsStockTagReqDTO reqVo);

    /**
     * 更新MKU ES聚合键。
     * @param input MKU客户端详细请求DTO，包含更新的信息。
     * @return 更新操作是否成功。
     */
    Boolean updateMkuEsAggKey(MkuClientDetailReqApiDTO input);

    /**
     * 更新SKU的NCM代码
     * @param eventMsgList 包含变更表事件消息的列表，用于批量处理SKU的NCM代码更新
     */
    void updateSkuNcmCode(List<ChangeTableEventMsg> eventMsgList);

    /**
     * 根据mkuId拆分出多个MkuClientDetailReqApiDTO对象。
     * @param mkuId Mku的唯一标识符。
     * @return 拆分后的MkuClientDetailReqApiDTO对象列表。
     */
    List<MkuClientDetailReqApiDTO> fissionForMku(Long mkuId);

    /**
     * 为指定客户的 MKU 进行分裂操作。
     * @param clientCode 客户代码
     * @param mkuId MKU ID
     * @return 分裂后的 MKU 客户信息列表
     */
    List<MkuClientDetailReqApiDTO> fissionForCustomerMku(String clientCode,Long mkuId);

    /**
     * 根据 SKU ID 进行商品拆分，返回拆分后的商品详情列表。
     * @param skuId SKU ID
     * @return 拆分后的商品详情列表
     */
    List<MkuClientDetailReqApiDTO> fissionForSku(Long skuId);

    /**
     * 更新MKU ES通用承诺标签
     * @param reqVo MKU承诺标签数据传输对象，包含需要更新的标签信息
     * @return 返回更新操作是否成功，true表示成功，false表示失败
     */
    Boolean updateMkuEsCommonPromiseTag(MkuPromiseTagDTO reqVo);
}
