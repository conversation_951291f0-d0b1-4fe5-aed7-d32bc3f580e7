package com.jdi.isc.message.broker.service.msg.wimp.price.salestatus;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.binlog.client.WaveEntry;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.domain.msg.MkuPriceChangeMsg;
import com.jdi.isc.message.broker.rpc.price.IscProductSoaCustomerSkuPriceWriteRpcService;
import com.jdi.isc.message.broker.rpc.utils.ExceptionUtil;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.price.req.PriceAvailableSaleStatusReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 客制化价格-更新可售状态.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CustomerSkuPriceSaleStatusBinlakeListener extends AbsMsgTool<MkuPriceChangeMsg> {

    @Resource
    private IscProductSoaCustomerSkuPriceWriteRpcService iscProductSoaCustomerSkuPriceWriteRpcService;

    /**
     * 监听事件类型
     */
    private final List<WaveEntry.EventType> eventTypes = Lists.newArrayList(WaveEntry.EventType.INSERT, WaveEntry.EventType.UPDATE);

    /**
     * On message.
     *
     * @param messages the messages
     * @throws Exception the exception
     */
    @JmqListener(id = "iscIntlJmq4Consumer", topics = {"${topic.jmq4.consumer.isc.customerSkuPriceDetailMsg}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        long l = System.currentTimeMillis();
        List<ChangeTableEventMsg> eventMsgList = parse(messages);
        log.info("CustomerSkuPriceSaleStatusBinlakeListener. parse, cost={}", System.currentTimeMillis() - l);

        log.info("CustomerSkuPriceSaleStatusBinlakeListener.onMessage req:{}" , JSON.toJSONString(eventMsgList));
        List<PriceAvailableSaleStatusReqDTO> target = filterAndTransformBrData(eventMsgList);

        try {
            if(CollectionUtils.isNotEmpty(target)){
                iscProductSoaCustomerSkuPriceWriteRpcService.updateAvailableSaleStatus(target);
            } else {
                log.info("CustomerSkuPriceSaleStatusBinlakeListener, target is empty");
            }
        }catch (Exception e){
            log.error("CustomerSkuPriceSaleStatusBinlakeListener.onMessage error, message={}", ExceptionUtil.getMessage(e, "更新协议价可售状态失败"), e);
            throw new BinlakeConsumeException("更新协议价可售状态失败");
        } finally {
            log.info("CustomerSkuPriceSaleStatusBinlakeListener, finish, cost={}", System.currentTimeMillis() - l);
        }

    }

    /**
     * 根据 ChangeTableEventMsg 列表过滤和转换为 OpenMsgPO 列表。
     * @param eventMsgList ChangeTableEventMsg 列表，包含要处理的事件消息。
     * @return 过滤和转换后的 OpenMsgPO 列表。
     */
    private List<PriceAvailableSaleStatusReqDTO> filterAndTransformBrData(List<ChangeTableEventMsg> eventMsgList) {
        List<PriceAvailableSaleStatusReqDTO> result = new ArrayList<>();
        for(ChangeTableEventMsg msg : eventMsgList){
            Set<String> changeKey = msg.getChangeFields().keySet();
            //忽略非更新消息
            if (!eventTypes.contains(msg.getChangeType()) || CollectionUtils.isEmpty(changeKey)) {
                continue;
            }

            // 是否有效
            Long yn = msg.getAfterValue("yn", Long.class);

            if (yn == 0) {
                continue;
            }

            // id
            Long id = msg.getAfterValue("id", Long.class);

            // 更新者
            String updater = msg.getRowAfter().get("updater");

            if (!changeKey.contains("unsellable_threshold") && !changeKey.contains("customer_sale_price")) {
                log.info("客制价，不可售阈值或者销价未发生变更，不进行重算可售状态，id={}", id);
                continue;
            }

            PriceAvailableSaleStatusReqDTO item = new PriceAvailableSaleStatusReqDTO();
            item.setId(id);
            item.setPin(updater);
            item.setUpdater(updater);
            result.add(item);
        }
        return result;
    }

}
