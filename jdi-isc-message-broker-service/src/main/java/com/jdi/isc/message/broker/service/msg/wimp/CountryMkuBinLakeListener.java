package com.jdi.isc.message.broker.service.msg.wimp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.jd.binlog.client.WaveEntry;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.domain.msg.po.OpenMsgPO;
import com.jdi.isc.message.broker.rpc.mku.RpcIscProductSoaMkuService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import com.jdi.isc.product.soa.api.customerMku.req.CustomerMkuBatchReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CountryMkuBinLakeListener extends AbsMsgTool<OpenMsgPO> {

    @Resource
    private RpcIscProductSoaMkuService rpcIscProductSoaMkuService;
    @LafValue("jdi.isc.country.client.relation")
    private String countryClientRelation;
    @JmqListener(id = "iscProductSoaJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.countryMku}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<ChangeTableEventMsg> eventMsgList = parse(messages);
        log.info("CountryMkuBinLakeListener.onMessage req:{}" , JSON.toJSONString(eventMsgList));
        try {
            List<CustomerMkuBatchReqDTO> target = filterAndTransform(eventMsgList);
            if(CollectionUtils.isNotEmpty(target)){
                for (CustomerMkuBatchReqDTO customerMkuBatchReqDTO : target){
                    rpcIscProductSoaMkuService.bindCustomerToMku(customerMkuBatchReqDTO);
                }
            }
        }catch (Exception e){
            log.error("CountryMkuBinLakeListener.onMessage msg:{}",JSON.toJSONString(messages),e);
        }
    }

    private List<CustomerMkuBatchReqDTO> filterAndTransform(List<ChangeTableEventMsg> eventMsgList) {
        List<CustomerMkuBatchReqDTO> result = new ArrayList<>();
        for(ChangeTableEventMsg msg : eventMsgList){
            if(WaveEntry.EventType.DELETE.equals(msg.getChangeType())){
                continue;
            }
            String mkuId = msg.getRowAfter().get("mku_id");
            String targetCountryCode = msg.getRowAfter().get("target_country_code");
            String poolStatus = msg.getRowAfter().get("pool_status");
            Map<String, String> clientCodeByCountry = getClientCodeByCountry();
            if(!clientCodeByCountry.containsKey(targetCountryCode)){
                continue;
            }
            CustomerMkuBatchReqDTO customerMkuBatchReqDTO = new CustomerMkuBatchReqDTO();
            customerMkuBatchReqDTO.setMkuId(Long.valueOf(mkuId));
            customerMkuBatchReqDTO.setTargetCountryCode(targetCountryCode);
            if(poolStatus.equals(CountryMkuPoolStatusEnum.POOL.getCode())){
                customerMkuBatchReqDTO.setBindStatus(CustomerMkuBindEnum.BIND);
            }else {
                customerMkuBatchReqDTO.setBindStatus(CustomerMkuBindEnum.INVALID);
            }
            result.add(customerMkuBatchReqDTO);
        }
        return result;
    }
    public Map<String,String> getClientCodeByCountry(){
        try {
            Map<String,String> countryClientCodeMap  = JSON.parseObject(countryClientRelation, new TypeReference<Map<String, String>>() {});
            return countryClientCodeMap;
        }catch (Exception e){
            Map<String,String> countryClientCodeMap = new HashMap<>();
            log.error("OperDuccConfig.getClientCodeByCountry error，e:{}",e.getMessage(),e);
            return countryClientCodeMap;
        }
    }
    //            {
    //                "changeFields":
    //                {
    //                    "update_time": "2024-01-08 14:32:02",
    //                        "price": "123.0",
    //                        "updater": "yuyang63"
    //                },
    //                "changeType": "UPDATE",
    //                    "dataBase": "jdi_intl_product",
    //                    "rowAfter":
    //                {
    //                    "id": "11901",
    //                        "spu_id": "20000000026",
    //                        "sku_id": "80000000087",
    //                        "source_country_code": "VN",
    //                        "trade_direction": "CUSTOMER",
    //                        "trade_type": "BD",
    //                        "currency": "VND",
    //                        "price": "123.0",
    //                        "creator": "sunlei61",
    //                        "updater": "yuyang63",
    //                        "create_time": "2023-12-15 20:18:13",
    //                        "update_time": "2024-01-08 14:32:02",
    //                        "yn": "1"
    //                },
    //                "rowBefore":
    //                {
    //                    "id": "11901",
    //                        "spu_id": "20000000026",
    //                        "sku_id": "80000000087",
    //                        "source_country_code": "VN",
    //                        "trade_direction": "CUSTOMER",
    //                        "trade_type": "BD",
    //                        "currency": "VND",
    //                        "price": "122.0",
    //                        "creator": "sunlei61",
    //                        "updater": "sunlei61",
    //                        "create_time": "2023-12-15 20:18:13",
    //                        "update_time": "2023-12-15 20:18:13",
    //                        "yn": "1"
    //                },
    //                "tableName": "jdi_isc_sku_price_sharding"
    //            }
}
