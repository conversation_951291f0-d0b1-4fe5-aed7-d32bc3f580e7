package com.jdi.isc.message.broker.service.msg.es;

import com.alibaba.fastjson.JSON;
import com.jd.binlog.client.WaveEntry;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.pfinder.profiler.sdk.trace.PFTracing;
import com.jdi.isc.message.broker.common.exception.BinlakeConsumeException;
import com.jdi.isc.message.broker.domain.common.biz.ChangeTableEventMsg;
import com.jdi.isc.message.broker.domain.customer.CustomerVO;
import com.jdi.isc.message.broker.domain.msg.MkuPoolChangeMsg;
import com.jdi.isc.message.broker.service.manage.customer.CustomerManageService;
import com.jdi.isc.message.broker.service.manage.mku.MkuManageService;
import com.jdi.isc.message.broker.service.msg.common.AbsMsgTool;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientDetailReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 客户mku关系binlake消息监听
 * <AUTHOR>
 * @date 20240108
 */
@Slf4j
@Component
public class CustomerMkuEsBinlakeListener extends AbsMsgTool<MkuPoolChangeMsg> {

    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private MkuManageService mkuManageService;

    @JmqListener(id = "iscMessageesJmq4MessageConsumer", topics = {"${topic.jmq4.consumer.isc.customerMku}"})
    @PFTracing
    public void onMessage(List<Message> messages) throws Exception {
        List<ChangeTableEventMsg> eventMsgList = parse(messages);
        try {
            log.info("CustomerMkuEsBinlakeListener.onMessage req:{}" , JSON.toJSONString(eventMsgList));
            List<MkuClientDetailReqApiDTO> target = filterAndTransform(eventMsgList);
            if(CollectionUtils.isNotEmpty(target)){
                mkuManageService.upsertToEs(target);
            }
        }catch (Exception e){
            throw new BinlakeConsumeException("CustomerMkuEsBinlakeListener.onMessage error",e);
        }
    }

    /** 消息过滤及转化 */
    private List<MkuClientDetailReqApiDTO> filterAndTransform(List<ChangeTableEventMsg> eventMsgList) {
        List<MkuClientDetailReqApiDTO> result = new ArrayList<>();
        for(ChangeTableEventMsg msg : eventMsgList){
            if(WaveEntry.EventType.DELETE.equals(msg.getChangeType())){
                continue;
            }
            if(!(msg.getChangeFields().containsKey("bind_status")
                    || msg.getChangeFields().containsKey("yn")) ){
                continue;
            }
            String mkuId = msg.getRowAfter().get("mku_id");
            String clientCode = msg.getRowAfter().get("client_code");
            CustomerVO client = customerManageService.detail(clientCode);
            if(client!=null){
                List<MkuClientDetailReqApiDTO> res = mkuManageService.fissionForCustomerMku(clientCode,Long.valueOf(mkuId));
                if(CollectionUtils.isNotEmpty(res)){
                    result.addAll(res);
                }
            }
        }
        return result;
    }

//            {
//                "changeFields":
//                {
//                    "id": "13701",
//                        "mku_id": "50000002280",
//                        "client_code": "hRvDgUX263Y2FuWbVzB8",
//                        "client_name": "越南客户-工采国际测试1",
//                        "bind_status": "BIND",
//                        "cat_id": "1167",
//                        "cn_spu_id": "20000000180",
//                        "cn_spu_flag": "1",
//                        "vn_spu_flag": "0",
//                        "creator": "yuyang63",
//                        "updater": "yuyang63",
//                        "create_time": "2024-01-08 14:29:06",
//                        "update_time": "2024-01-08 14:29:06",
//                        "yn": "1"
//                },
//                "changeType": "INSERT",
//                    "dataBase": "jdi_intl_product",
//                    "rowAfter":
//                {
//                    "$ref": "$.changeFields"
//                },
//                "tableName": "jdi_isc_customer_mku_sharding"
//            }


    public static void main(String[] args) {
        String str = "2024-01-10 14:19:25";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            System.out.println(sdf.parse(str));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

}
