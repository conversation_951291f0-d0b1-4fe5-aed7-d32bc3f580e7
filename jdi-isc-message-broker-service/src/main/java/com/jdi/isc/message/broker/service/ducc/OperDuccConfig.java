package com.jdi.isc.message.broker.service.ducc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.jd.jsf.gd.util.StringUtils;
import com.jd.laf.config.spring.annotation.LafValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * The type Oper ducc config.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OperDuccConfig {

    @LafValue("jdi.isc.open.soa.price.jdTaxRefundPrice")
    private String jdTaxRefundPriceConfig;

    @LafValue("jdi.isc.open.soa.price.jdTaxRefundPrice.sku")
    private String jdTaxRefundPriceSkuConfig;

    @LafValue("jdi.isc.product.soa.agreementPrice.switch")
    private String agreementPriceSwitch;

    @LafValue("jdi.isc.product.soa.test.skuIds")
    private String testSkuIds;

    /**
     * 解析agreementPriceSwitch配置，返回国家Code码和开关类型。
     * @return 价格转换映射，键为国家Code码（VN、TH、HU等），值为开关类型（1:协议价为空，自动设置国家协议价。2:不自动设置国家协议价，3:自动设置国家协议价，4:vip价同步到国家协议价）。
     * 配置开关：1、首次（国家协议价为空）自动设置国家协议价；2、不自动联动设置国家协议价；3、自动联动设置国家协议价；4、按客户vip价更新国家协议价（支持配置多个客户，适用巴西场景）配置开关按国家设置
     * 配置开关1：当前现状，不做特殊处理
     * 配置开关2：没有手动设置的情况下国家协议价不做任何变动
     * 配置开关3：所有的国家成本价的变动自动联动国家协议价修改，当国家成本价为空的时候国家协议价也为空
     * 配置开关4（仅限巴西场景）：在1的基础上（为空的时候设置，不为空不设置），不论国家协议价当前状态当有指定客户范围的vip价审核通过，按vip价写入国家协议价
     */
    public Map<String, Integer> agreementPriceSwitch(){
        try{
            return JSON.parseObject(agreementPriceSwitch, new TypeReference<Map<String, Integer>>(){});
        }catch (Exception e){
            log.error("OperDuccConfig.getJdTaxRefundPriceSkuConfig agreementPriceSwitch:{}",JSON.toJSONString(agreementPriceSwitch),e);
            return new HashMap<>();
        }
    }


    /**
     * 是否计算退税款价格-按目标国灰度
     *
     * @param targetCountryCode the target country code
     * @return the boolean
     */
    public boolean isComputeJdTaxRefundPrice(String targetCountryCode) {
        if  (StringUtils.isBlank(jdTaxRefundPriceConfig) || StringUtils.isBlank(targetCountryCode)) {
            return false;
        }

        List<String> list = Splitter.on(',').splitToList(jdTaxRefundPriceConfig);

        return list.contains("all") || list.contains(targetCountryCode);
    }

    /**
     * 是否计算退税款价格-按sku灰度
     *
     * @param skuId the sku id
     * @return the boolean
     */
    public boolean isComputeJdTaxRefundPriceSku(Long skuId) {
        if  (StringUtils.isBlank(jdTaxRefundPriceSkuConfig) || skuId == null) {
            return false;
        }

        List<String> list = Splitter.on(',').splitToList(jdTaxRefundPriceSkuConfig);

        return list.contains("all") || list.contains(skuId.toString());
    }

    /**
     * 获取测试 SKU 的 ID 列表。
     * @return SKU ID 列表，如果 testSkuIds 为空或解析失败则返回 null。
     */
    public List<Long> getTestSkuIds() {
        try{
            if(StringUtils.isBlank(testSkuIds)){
                return null;
            }
            return Splitter.on(',')
                    .trimResults()
                    .omitEmptyStrings()
                    .splitToList(testSkuIds)
                    .stream()
                    .map(Long::parseLong).collect(Collectors.toList());
        }catch (Exception e){
            log.error("OperDuccConfig.getJdTaxRefundPriceSkuConfig agreementPriceSwitch:{}",JSON.toJSONString(testSkuIds),e);
            return Lists.newArrayList();
        }
    }
}
