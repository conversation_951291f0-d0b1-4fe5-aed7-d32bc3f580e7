package com.jdi.isc.message.broker.service.atomic.price;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jdi.isc.message.broker.domain.price.po.CustomerSkuPriceDetailDraftPO;
import com.jdi.isc.message.broker.repository.jed.mapper.price.CustomerSkuPriceDetailDraftBaseMapper;
import groovy.util.logging.Slf4j;
import org.springframework.stereotype.Service;

/**
 * sku客制化价格草稿明细原子服务
 * <AUTHOR>
 * @date 20250609
 */
@Service
@Slf4j
public class CustomerSkuPriceDetailDraftAtomicService extends ServiceImpl<CustomerSkuPriceDetailDraftBaseMapper, CustomerSkuPriceDetailDraftPO> {


}




