<?xml version="1.0" encoding="utf-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-autowire="byName">

    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="${jd.jsf.registry.index}"/>

    <!-- mku相关服务 -->
    <jsf:consumer id="iscProductSoaMkuApiService" interface="com.jdi.isc.product.soa.api.wisp.mku.MkuClientApiService"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.writeTimeout}"
                  retries="2" check="false">
    </jsf:consumer>

    <!-- 订单履约信息缺失写服务 -->
    <jsf:consumer id="iscOrderOutVerifyWriteApiService"
                  interface="com.jdi.isc.product.soa.api.orderVerify.IscOrderOutVerifyWriteApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.product.alias}" timeout="${jd.jsf.consumer.product.timeout}" />

    <!-- joysky审批流业务组件 -->
    <jsf:consumer id="joySkyAppFlowApiService"
                  interface="com.jdi.isc.biz.component.api.joysky.JoySkyAppFlowApiService"
                  protocol="jsf" alias="${jd.jsf.consumer.bizComponent.alias}"
                  timeout="${jd.jsf.consumer.bizComponent.timeout}" />

    <!-- sku和spu相关服务 -->
    <jsf:consumer id="iscProductSoaSpuWriteApiService"
                  interface="com.jdi.isc.product.soa.api.spu.IscProductSoaSpuWriteApiService"
                  protocol="jsf"
                  alias="${jd.jsf.consumer.product.alias}"
                  timeout="${jd.jsf.consumer.product.timeout}" >
    </jsf:consumer>

    <!-- 国家池写服务 -->
    <jsf:consumer id="iscCountryMkuWriteApiService"
                  interface="com.jdi.isc.product.soa.api.countryMku.IscCountryMkuWriteApiService"
                  protocol="jsf"
                  alias="${jd.jsf.consumer.product.alias}"
                  timeout="${jd.jsf.consumer.product.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <jsf:consumer id="customerReadService" interface="com.jdi.isc.aggregate.read.api.customer.CustomerReadService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian" >
    </jsf:consumer>

    <!-- 邮件发送服务 -->
    <jsf:consumer id="iscMailServiceApi" interface="com.jdi.isc.task.center.api.mail.IscMailServiceApi"
                  alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}" retries="0" protocol="jsf">
    </jsf:consumer>

    <!-- 京ME发送消息接口 -->
    <jsf:consumer id="jingMeMessageApiService"
                  interface="com.jdi.isc.biz.component.api.jme.JingMeMessageApiService"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"/>

    <!-- 库存写相关服务 -->
    <jsf:consumer id="iscProductSoaStockWriteApiService"
                  interface="com.jdi.isc.product.soa.api.stock.IscProductSoaStockWriteApiService"
                  protocol="jsf"
                  alias="${jd.jsf.consumer.stock.alias}"
                  timeout="${jd.jsf.consumer.stock.timeout}" >
    </jsf:consumer>
    <!-- 履约费率读写服务 -->
    <jsf:consumer id="iscProductSoaFulfillmentRateApiService"
                  interface="com.jdi.isc.product.soa.price.api.price.IscProductSoaFulfillmentRateApiService"
                  alias="${jd.jsf.consumer.fulfillmentRate.alias}"
                  timeout="${jd.jsf.common.timeout}"/>

    <!-- 国际SKU价格写服务 -->
    <jsf:consumer id="iscProductSoaPriceWriteApiService"
                  interface="com.jdi.isc.product.soa.price.api.price.IscProductSoaPriceWriteApiService"
                  alias="${jd.jsf.consumer.costPrice.alias}"
                  timeout="${jd.jsf.consumer.costPrice.writeTimeout}"/>

    <!-- 国际SKU销价读服务 -->
    <jsf:consumer id="iscProductSoaSalePriceReadApiService"
                  interface="com.jdi.isc.product.soa.price.api.salePrice.IscProductSoaSalePriceReadApiService"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"/>

    <!-- 销售价格写服务 -->
    <jsf:consumer id="iscProductSoaSalePriceWriteApiService"
                  interface="com.jdi.isc.product.soa.price.api.salePrice.IscProductSoaSalePriceWriteApiService"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"/>

    <!-- 国家 -->
    <jsf:consumer id="iscProductSoaCountryApiService"
                  interface="com.jdi.isc.product.soa.api.wimp.country.IscProductSoaCountryApiService"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"/>

    <!-- 汇率写服务 -->
    <jsf:consumer id="iscProductSoaExchangeRateWriteApiService"
                  interface="com.jdi.isc.product.soa.api.price.IscProductSoaExchangeRateWriteApiService"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"/>
    <!-- 客户mku -->
    <jsf:consumer id="iscProductSoaCustomerMkuWriteApiService" interface="com.jdi.isc.product.soa.api.customerMku.IscProductSoaCustomerMkuWriteApiService"
                  alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.writeTimeout}" retries="2" protocol="jsf">
    </jsf:consumer>

    <!-- 查询备货仓信息 -->
    <jsf:consumer id="iscWarehouseReadApiService" interface="com.jdi.isc.product.soa.api.warehouse.IscWarehouseReadApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}" />

    <!--  mku绑定关系  -->
    <jsf:consumer id="mkuRelationApiService" interface="com.jdi.isc.aggregate.read.api.mku.MkuRelationApiService"
                  protocol="jsf" alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}"  serialization="hessian" >
    </jsf:consumer>

    <!--其它consumer可复用，预热使用-->
    <jsf:warmupWeightStrategy id="taskWarmup" enabled="true" warmupDuration="15000" warmupWeight="40"/>

    <!--Sku服务-->
    <jsf:consumer id="iscProductSoaSkuWriteApiService"
                  interface="com.jdi.isc.product.soa.api.sku.IscProductSoaSkuWriteApiService"
                  alias="${jd.jsf.consumer.product.alias}"
                  timeout="${jd.jsf.consumer.product.timeout}"
                  retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!--供应商消息服务-->
    <jsf:consumer id="supplierMsgApiService"
                  interface="com.jdi.isc.product.soa.api.message.SupplierMsgApiService"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>
    <!--供应商信息查询服务-->
    <jsf:consumer id="supplierReadApiService"
                  interface="com.jdi.isc.product.soa.api.supplier.SupplierReadApiService"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  retries="0" warmupWeightStrategy="taskWarmup">
    </jsf:consumer>

    <!-- sku读服务 -->
    <jsf:consumer id="iscProductSoaSkuReadApiService" interface="com.jdi.isc.product.soa.api.sku.IscProductSoaSkuReadApiService"
                  alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}" retries="2" protocol="jsf">
    </jsf:consumer>

    <!-- 国家协议价写服务 -->
    <jsf:consumer id="iscCountryAgreementPriceWriteApiService"
                  interface="com.jdi.isc.product.soa.api.agreementPrice.IscCountryAgreementPriceWriteApiService"
                  protocol="jsf"
                  alias="${jd.jsf.consumer.product.alias}"
                  timeout="${jd.jsf.consumer.product.timeout}"
                  serialization="hessian">
    </jsf:consumer>

    <!-- 类目写接口 -->
    <jsf:consumer id="categoryWriteApiService"
                  interface="com.jdi.isc.product.soa.api.wimp.category.CategoryWriteApiService"
                  protocol="jsf"
                  alias="${jd.jsf.consumer.product.alias}"
                  timeout="${jd.jsf.common.writeTimeout}"
                  serialization="hessian">
    </jsf:consumer>


    <!-- 国际同步国内类目服务服务 -->
    <jsf:consumer id="iscProductSoaCategoryApiService"
                  interface="com.jdi.isc.product.soa.api.category.IscProductSoaCategoryApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.consumer.category.timeout}"
                  serialization="hessian">
    </jsf:consumer>

    <!-- isc商品读服务 -->
    <jsf:consumer id="iscProductSoaMkuReadApiService" interface="com.jdi.isc.product.soa.api.mku.IscProductSoaMkuReadApiService"
                  alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}"
                  retries="2" serialization="hessian"  />

    <!-- 国家池读接口 -->
    <jsf:consumer id="iscCountryMkuReadApiService" interface="com.jdi.isc.product.soa.api.countryMku.IscCountryMkuReadApiService"
                  alias="${jd.jsf.common.alias}" timeout="${jd.jsf.common.timeout}"
                  serialization="hessian"/>

    <!-- 商品服务 审核写能力 -->
    <jsf:consumer id="approveOrderWriteApiService"
                  interface="com.jdi.isc.product.soa.api.approveorder.ApproveOrderWriteApiService"
                  protocol="jsf"
                  alias="${jd.jsf.common.alias}"
                  timeout="${jd.jsf.common.timeout}"
                  serialization="hessian">
    </jsf:consumer>

    <!-- 客制化价写服务 -->
    <jsf:consumer id="iscProductSoaCustomerSkuPriceWriteApiService"
                  interface="com.jdi.isc.product.soa.api.customerSku.IscProductSoaCustomerSkuPriceWriteApiService"
                  protocol="jsf"
                  alias="${jd.jsf.consumer.product.alias}"
                  timeout="${jd.jsf.consumer.product.timeout}"
                  serialization="hessian">
    </jsf:consumer>

    <jsf:consumer id="iscTaxRateWriteApiService"
                  interface="com.jdi.isc.product.soa.api.taxRate.IscTaxRateWriteApiService"
                  alias="${jd.jsf.consumer.product.alias}"
                  timeout="${jd.jsf.consumer.product.timeout}"
                  serialization="hessian"/>


</beans>