jdi:
  common:
    frame:
      ump-prefix: pre-
      log-level: INFO
ducc:
  hostPort: ducc.jd.local
  config: isc-oper
  profile: pre
  namespace: international
  application: jdos_jdi-isc-product-center

jimdb:
  serviceEndpoint: http://cfs.jim.jd.local
  ioThreadPoolSize: 5
  computationThreadPoolSize: 5
  requestQueueSize: 100000

topic:
  jmq4:
    consumer:
      isc:
        taxRateMsg: jdi_isc_tax_rate_binlake_msg_pre
        priceWarnTopic: jdi_isc_price_warn_msg_pre
        initCountryAgreementTopic: jdi_isc_init_country_agreement_msg_pre
        approveOrder: jdi_isc_approve_order_msg_pre
        forecastOrderSplit: xxx
        customerSkuPrice: jdi_isc_customer_sku_price_binlake_msg
        basePrice: jdi_isc_sku_price_binlake_msg
        customerMku: jdi_isc_customer_mku_binlake_msg
        mku: jdi_isc_mku_binlake_msg
        mkubak: jdi_isc_mku_binlake_msg
        mkuLang: jdi_isc_mku_lang_binlake_msg
        mkuDescLang: jdi_isc_mku_desc_lang_binlake_msg
        orderStatus: jdi_isc_order_center_status_update_msg_pre
        deliveryOrderStatus: jdi_isc_order_delivery_msg
        orderSplit: jdi_isc_order_split_msg_pre
        orderSubmit: jdi_isc_order_center_submit_msg_pre
        joySkyResult: base_process_operate_pre
        skuProperty: jdi_isc_front_product_measure_change_yf
        gdSkuPool: JDI_CUSTOMER_POOL_SKU_INSERT_pb
        customerInvoice: jdi_isc_customer_invoice_save_msg_pre
        stock: jdi_isc_sku_stock_msg
        skuFeature: jdi_isc_sku_feature_msg
        fulfillmentRate: jdi_isc_fulfillment_rate_binlale_msg
        preOrderSplit: jdi_isc_order_pre_split_msg_tree_pre
        exchangeRate: jdi_isc_exchange_rate_binlale_msg
        costPrice: jdi_isc_sku_price_binlake_msg
        countryMku: jdi_isc_country_mku_binlake_msg
        purchaseOrderSplit: jdi_isc_purchase_order_split_msg_pre
        baseSku: jdi_isc_sku_binlake_msg
        kaiyangMsg: JDI_CUSTOMER_REFLECT_DATA_BINLAKE
        warehouseSku:
          binLake: jdi_isc_warehouse_sku_binlake_msg_pre
        gdSpuPropertyChange: jdi_gd_spu_property_data_change_by_sku
        spuDraftChangeMsg: jdi_isc_spu_draft_binlake_msg
        mkuEsChange: jdi_isc_mku_es_change_msg_pre
        bWareSkuProperty: bware_sku_property_data_change
        bWareSpuProperty: bware_spu_property_data_change
        gmsProductCategory: gms_product_cat_new_test
        applyBinlakeMsg: jdi_isc_apply_info_binlake_msg
        bware: bware_sku_data_change
        supplierStatus: jdi_isc_supplier_status_msg_pre
        markupRateBinLakeMsg: jdi_isc_markup_rate_binlake_msg_pre
    provider:
      customerPriceAuditTopic: jdi_isc_customer_price_audit_msg_pre
      mkuChangeTopic: jdi_isc_mku_change_msg_pre
jd:
  product:
    system:
      key: qiye_jdi-igc-product