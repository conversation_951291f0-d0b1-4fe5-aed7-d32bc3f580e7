package com.jdi.isc.message.broker.service.wimp;


import com.alibaba.fastjson.JSON;
import com.jd.binlog.client.WaveEntry;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.message.broker.common.constants.Constant;
import com.jdi.isc.message.broker.common.util.ConfigUtils;
import com.jdi.isc.message.broker.domain.msg.SkuStockChangeMsg;
import com.jdi.isc.message.broker.domain.stock.SkuStockThresholdPO;
import com.jdi.isc.message.broker.service.ServiceApplication;
import com.jdi.isc.message.broker.service.atomic.stock.SkuStockThresholdAtomicService;
import com.jdi.isc.message.broker.service.manage.sku.SkuStockManageService;
import com.jdi.isc.product.soa.api.common.LangConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description：StockThresholdTest
 * @Date 2025-01-17
 */

@Slf4j
@SpringBootTest(classes = ServiceApplication.class)
public class StockTest {

    @LafValue("isc.message.broker.sku.stock.dongdong.config")
    private String dongDongConfig;

    @Resource
    private SkuStockThresholdAtomicService skuStockThresholdAtomicService;

    @Resource
    private SkuStockManageService skuStockManageService;


    @Test
    public void testStockThreshold() {
        String zhMessage = ConfigUtils.getStringFromJsonString(dongDongConfig, Constant.ONE_STR, LangConstant.LANG_ZH);
        System.out.println(zhMessage);
        String enMessage = ConfigUtils.getStringFromJsonString(dongDongConfig, Constant.ONE_STR, LangConstant.LANG_EN);
        System.out.println(enMessage);
    }

    @Test
    public void querySkuThreshold() {
        SkuStockThresholdPO skuStockThreshold = skuStockThresholdAtomicService.getSkuStockThreshold(80000059565L, null);
        System.out.println(JSON.toJSONString(skuStockThreshold));
    }

    @Test
    public void insertSkuThreshold() {
        SkuStockThresholdPO skuStockThresholdPO = new SkuStockThresholdPO();
        skuStockThresholdPO.setSkuId(80000059565L);
        skuStockThresholdPO.setWarehouseId(1006L);
        skuStockThresholdPO.setStockThreshold(10L);
        skuStockThresholdPO.setYn(1);
        skuStockThresholdPO.setCreator(Constant.SYSTEM);
        skuStockThresholdPO.setUpdater(Constant.SYSTEM);
        skuStockThresholdPO.setCreateTime(System.currentTimeMillis());
        skuStockThresholdPO.setUpdateTime(System.currentTimeMillis());
        skuStockThresholdAtomicService.save(skuStockThresholdPO);
    }

    @Test
    public void testCheckAndNotifyStockAvailability() {

        List<SkuStockChangeMsg> skuStockChangeMsgList = Lists.newArrayList();

        //供应商库存变更
        SkuStockChangeMsg skuStockChangeMsg = new SkuStockChangeMsg();
        skuStockChangeMsg.setSkuId(80000000042L);
        skuStockChangeMsg.setStock(100L);
        skuStockChangeMsg.setOnWayStock(0L);
        skuStockChangeMsg.setEventType(WaveEntry.EventType.UPDATE);

        //供应商库存变更
        SkuStockChangeMsg skuStockChangeMsg1 = new SkuStockChangeMsg();
        skuStockChangeMsg1.setSkuId(80000000138L);
        skuStockChangeMsg1.setStock(100L);
        skuStockChangeMsg1.setOnWayStock(0L);
        skuStockChangeMsg1.setWarehouseId(1015L);
        skuStockChangeMsg1.setEventType(WaveEntry.EventType.UPDATE);

        skuStockChangeMsgList.add(skuStockChangeMsg);
        skuStockChangeMsgList.add(skuStockChangeMsg1);

        DataResponse<Boolean> response = skuStockManageService.checkAndNotifyStockAvailability(skuStockChangeMsgList);
        System.out.println("result is: " + response);
    }


}
