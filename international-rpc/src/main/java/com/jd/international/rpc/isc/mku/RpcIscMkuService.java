package com.jd.international.rpc.isc.mku;


import com.jd.international.soa.sdk.common.isc.mku.domain.biz.*;
import com.jd.international.soa.sdk.common.isc.mku.req.MkuReplenishmentSoaDTO;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.soa.api.mku.req.SpecialAttrMkuClientReqDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientLatestWareReqApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientPageReqApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuListInfoReqApiDTO;

import java.util.List;
import java.util.Map;

/**
 * @Description: 商品服务接口
 * @Author: zhaojianguo21
 * @Date: 2023/12/13 16:26
 **/
public interface RpcIscMkuService {

//    /**
//     * 商品列表
//     * @param input 列表参数
//     * @return 商品列表
//     */
//    PageInfo<IscMkuClientDTO> page(IscMkuClientPageReqDTO input);
//
//    /**
//     * 是否存在mku
//     * @param input 请求参数
//     * @return true/false
//     */
//    Boolean existsMku(IscMkuClientDetailReqDTO input);

    /**
     * 是否存在有效mku
     * @param input 请求参数
     * @return true/false
     */
    Boolean existsValidMku(IscMkuClientDetailReqDTO input);

    /**
     * 是否存在mku
     * @param input 请求参数
     * @return true/false
     */
    List<String> attempt(MkuClientPageReqApiDTO input);

//    /**
//     * mku信息
//     * @param input 请求参数
//     * @return mku描述
//     */
//    IscMkuClientDTO baseInfo(IscMkuClientDetailReqDTO input);


//    /**
//     * 查同mku的同组聚堆信息
//     * @param input 请求参数
//     * @return 同组商品聚堆信息
//     */
//    IscMkuClientGroupDTO groupInfo(IscMkuClientDetailReqDTO input);
//
//    /**
//     * 商品列表页单个商品卡片信息
//     * @param input 请求参数
//     * @return 商品卡片信息
//     */
//    IscMkuClientCardInfoResDTO cardInfo(IscMkuClientCardInfoReqDTO input);

    Boolean replenishment(MkuReplenishmentSoaDTO req);

//    /**
//     * 商品列表
//     * @param input 列表参数
//     * @return 商品列表
//     */
//    PageInfo<MkuClientApiDTO> page(MkuClientPageReqApiDTO input);

    /**
     * 检查Mku是否存在
     * @param input 请求详细信息
     * @return 如果Mku存在则返回其ID，否则返回null
     */
    Long existsMku(MkuClientPageReqApiDTO input);

    /**
     * 最新入池的商品
     * @param input
     * @return
     */
    List<MkuClientApiDTO> latestWares(MkuClientLatestWareReqApiDTO input);

    /**
     * 根据 SpecialAttrMkuClientReqDTO 对象查询 MKU 属性信息。
     * @param input SpecialAttrMkuClientReqDTO 对象，包含查询条件。
     * @return Map&lt;Long, Map&lt;String,String&gt;&gt;，键为 MKU ID，值为该 MKU 的属性信息。
     */
    Map<Long, Map<String,String>> queryMkuSpecialAttr(SpecialAttrMkuClientReqDTO input);

    /**
     * 批量查询商品信息
     * @param input
     * @return
     */
    List<MkuClientApiDTO> queryWaresInfo(MkuListInfoReqApiDTO input);
}
