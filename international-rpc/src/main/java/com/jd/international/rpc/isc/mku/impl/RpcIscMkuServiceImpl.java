package com.jd.international.rpc.isc.mku.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.international.rpc.isc.mku.RpcIscMkuService;
import com.jd.international.soa.sdk.common.isc.mku.IscMkuClientApiService;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientCardInfoReqDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientCardInfoResDTO;
import com.jd.international.soa.sdk.common.isc.mku.domain.biz.IscMkuClientDetailReqDTO;
import com.jd.international.soa.sdk.common.isc.mku.req.MkuReplenishmentSoaDTO;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.isc.product.soa.api.mku.IscProductSoaMkuReadApiService;
import com.jdi.isc.product.soa.api.mku.req.SpecialAttrMkuClientReqDTO;
import com.jdi.isc.product.soa.api.wisp.mku.MkuClientApiService;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientLatestWareReqApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuClientPageReqApiDTO;
import com.jdi.isc.product.soa.api.wisp.mku.biz.MkuListInfoReqApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhaojianguo21
 * @Date: 2023/12/16 17:35
 **/
@Slf4j
@Service
public class RpcIscMkuServiceImpl implements RpcIscMkuService {

    @Resource
    private IscMkuClientApiService iscMkuClientApiService;
    @Resource
    private MkuClientApiService mkuClientApiService;
    @Resource
    private IscProductSoaMkuReadApiService iscProductSoaMkuReadApiService;


//    @Override
//    public PageInfo<IscMkuClientDTO> page(IscMkuClientPageReqDTO input) {
//        DataResponse<PageInfo<IscMkuClientDTO>> response = iscMkuClientApiService.page(input);
//        if (null==response || !response.getSuccess()){
//            log.warn("page, response fail. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//
//        return response.getData();
//    }
//
//    @Override
//    public Boolean existsMku(IscMkuClientDetailReqDTO input) {
//        DataResponse<Boolean> response = iscMkuClientApiService.existsMku(input);
//        if (null==response || !response.getSuccess()){
//            log.warn("existsMku, response fail. input={}", JSONObject.toJSONString(input));
//            return Boolean.FALSE;
//        }
//
//        return response.getData();
//    }

    @Override
    public Boolean existsValidMku(IscMkuClientDetailReqDTO input) {
        DataResponse<Boolean> response = iscMkuClientApiService.existsValidMku(input);
        if (null==response || !response.getSuccess()){
            log.warn("existsValidMku, response fail. input={}", JSONObject.toJSONString(input));
            return Boolean.FALSE;
        }
        return response.getData();
    }

    @Override
    public List<String> attempt(MkuClientPageReqApiDTO input) {
        DataResponse<List<String>> response = mkuClientApiService.attempt(input);
        if (null==response || !response.getSuccess()){
            log.warn("attempt, response fail. input={}", JSONObject.toJSONString(input));
            return new ArrayList<>();
        }

        return response.getData();
    }

//    @Override
//    public IscMkuClientDTO baseInfo(IscMkuClientDetailReqDTO input) {
//        DataResponse<IscMkuClientDTO> response = iscMkuClientApiService.baseInfo(input);
//        if (null==response || !response.getSuccess()){
//            log.warn("baseInfo, response fail. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//
//        return response.getData();
//    }
//
//    @Override
//    public IscMkuClientGroupDTO groupInfo(IscMkuClientDetailReqDTO input) {
//        DataResponse<IscMkuClientGroupDTO> response = iscMkuClientApiService.groupInfo(input);
//        if (null==response || !response.getSuccess()){
//            log.warn("groupInfo, response fail. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//
//        return response.getData();
//    }
//
//    @Override
//    public IscMkuClientCardInfoResDTO cardInfo(IscMkuClientCardInfoReqDTO input) {
//        DataResponse<IscMkuClientCardInfoResDTO> response = iscMkuClientApiService.cardInfo(input);
//        if (null==response || !response.getSuccess()){
//            log.warn("cardInfo, response fail. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//
//        return response.getData();
//    }

    @Override
    public Boolean replenishment(MkuReplenishmentSoaDTO req) {
        DataResponse<Boolean> res = null;
        try{
            res = iscMkuClientApiService.replenishment(req);
            if(res.getSuccess()){
                return res.getData();
            }
        }finally {
            log.info("RpcIscMkuServiceImpl.replenishment req:{} , res:{}" , JSON.toJSONString(req) , res);
        }
        return false;
    }

//    @Override
//    public PageInfo<MkuClientApiDTO> page(MkuClientPageReqApiDTO input) {
//        DataResponse<PageInfo<MkuClientApiDTO>> response = mkuClientApiService.page(input);
//        if (null==response || !response.getSuccess()){
//            log.warn("page, response fail. input={}", JSONObject.toJSONString(input));
//            return null;
//        }
//
//        return response.getData();
//    }

    @Override
    public Long existsMku(MkuClientPageReqApiDTO input) {
        DataResponse<Long> response = mkuClientApiService.existsMku(input);
        if (null==response || !response.getSuccess()){
            log.warn("existsMku, response fail. input={}", JSONObject.toJSONString(input));
            return null;
        }

        return response.getData();
    }

    @Override
    public List<MkuClientApiDTO> latestWares(MkuClientLatestWareReqApiDTO input) {
        DataResponse<List<MkuClientApiDTO>> response = mkuClientApiService.latestWares(input);
        if (null==response || !response.getSuccess()){
            log.warn("latestWares, response fail. input={}", JSONObject.toJSONString(input));
            return new ArrayList<>();
        }

        return response.getData();
    }

    @Override
    public Map<Long, Map<String, String>> queryMkuSpecialAttr(SpecialAttrMkuClientReqDTO input) {
        DataResponse<Map<Long, Map<String, String>>> response = iscProductSoaMkuReadApiService.querySpecialAttrMapByClientCode(input);
        if (null==response || !response.getSuccess()){
            log.warn("queryMkuAttr, response fail. input={}", JSONObject.toJSONString(input));
            return Collections.emptyMap();
        }
        return response.getData();
    }

    @Override
    public List<MkuClientApiDTO> queryWaresInfo(MkuListInfoReqApiDTO input) {
        DataResponse<List<MkuClientApiDTO>> response = null;
        try {
            response = mkuClientApiService.queryWaresInfo(input);
            if (null!=response && Boolean.TRUE.equals(response.getSuccess())){
                return response.getData();
            }
        } catch (Exception e) {
            log.error("queryWaresInfo", e);
        }

        log.warn("queryWaresInfo, response fail. input={}", JSONObject.toJSONString(input));
        return Collections.emptyList();
    }
}
