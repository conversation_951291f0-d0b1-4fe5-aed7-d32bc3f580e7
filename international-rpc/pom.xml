<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jd.international</groupId>
        <artifactId>international</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>international-rpc</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.jd.international</groupId>
            <artifactId>international-domain</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.international</groupId>
            <artifactId>international-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf</artifactId>
            <version>${jsf.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.biz</groupId>
            <artifactId>mro-ka-workflow-sdk</artifactId>
        </dependency>
        <dependency>
            <artifactId>international-wares-sdk</artifactId>
            <groupId>com.jd.international.soa</groupId>
            <version>0.0.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jd.user.sdk</groupId>
                    <artifactId>user-sdk-export</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>international-order-sdk</artifactId>
            <groupId>com.jd.international.soa</groupId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.biz</groupId>
            <artifactId>jd-biz-user-soa-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jdi</groupId>
            <artifactId>suan-ni</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jdi</groupId>
            <artifactId>k2-product-sku-boost-cache-sdk</artifactId>
        </dependency>
        <dependency>
            <artifactId>international-approval-sdk</artifactId>
            <groupId>com.jd.international.soa</groupId>
        </dependency>
        <dependency>
            <artifactId>international-common-sdk</artifactId>
            <groupId>com.jd.international.soa</groupId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jmq</groupId>
            <artifactId>jmq2-client-springboot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.product.soa</groupId>
            <artifactId>jdi-isc-product-soa-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.task.center</groupId>
            <artifactId>jdi-isc-task-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.wanfang</groupId>
            <artifactId>wanfang-support-soa-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.aggregate.read</groupId>
            <artifactId>jdi-isc-aggregate-read-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.order.center</groupId>
            <artifactId>jdi-isc-order-center-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.product.soa</groupId>
            <artifactId>jdi-isc-product-soa-stock-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.aggregate.read</groupId>
            <artifactId>jdi-isc-aggregate-read-wisp-api</artifactId>
        </dependency>
    </dependencies>
</project>