package com.jd.international.common.bean;

import com.jd.common.util.StringUtils;
import com.jd.international.common.constants.LoginSource;
import com.jdi.isc.product.soa.api.common.LangConstant;
import lombok.Data;

import java.util.Map;

@Data
public class BaseInfo {

    /**
     * 环境
     */
    private String env;

    /**
     * 用户pin
     */
    private String pin;

    /**
     * 用户所属主体简码
     */
    private String clientId;

    /**
     * 用户所在合同合同号
     */
    private String contractNum;

    /**
     * 站点类型
     */
    private Integer stationType;

    /**
     * logo信息
     */
    private String logoUrl;

    /**
     * 用户类型  4:管理员账号 5:采购账号 6:审批账号
     */
    private Integer userType;
    /**
     * 用户IP
     */
    private String userIp;
    /**
     * 开放访问所属地区
     */
    private String openArea;

    /**
     * 用户所在国家或地区的简称。
     */
    private String country;

    /**
     * 子账户信息
     */
    private String subAccount;

    /**
     * 扩展信息存储，用于保存不确定的额外数据。
     */
    private Map<String ,Object> extMaps;

    /**
     * 用户登录来源
     */
    private String loginSource;

    public String getEnv() {
        return StringUtils.isBlank(env) ? LangConstant.LANG_ZH : env;
    }

//    public boolean isJinMen() {
//        return "xsb9Oiq1Tx7jWveKg2MI".equals(this.clientId);
//    }

    public boolean isPunchout() {
        return LoginSource.WIEP_CXML.equals(loginSource);
    }
}
