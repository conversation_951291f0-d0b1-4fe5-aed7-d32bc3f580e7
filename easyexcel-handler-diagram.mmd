sequenceDiagram
    participant Start as 开始写入
    participant WBC1 as WorkbookWrite<PERSON><PERSON><PERSON><br/>beforeWorkbookCreate
    participant WAC1 as Workbook<PERSON><PERSON><PERSON>andler<br/>afterWorkbookCreate
    participant SBC1 as She<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><br/>beforeSheetCreate
    participant SAC1 as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><br/>afterSheetCreate
    participant RBC1 as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><br/>beforeRowCreate
    participant RAC1 as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><br/>afterRowCreate
    participant CBC1 as CellWriteH<PERSON><PERSON><br/>beforeCellCreate
    participant CAC1 as CellWriteHandler<br/>afterCellCreate
    participant CAD as CellWriteHandler<br/>afterCellDispose
    participant RAD as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><br/>afterRowDispose
    participant SAD as She<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><br/>afterSheetDispose
    participant WAD as WorkbookWriteHandler<br/>afterWorkbookDispose
    participant Finish as 结束写入

    Start ->> WBC1: order ↑ 小→大
    WBC1 ->> WAC1: order ↑ 小→大
    WAC1 ->> SBC1: order ↑ 小→大
    SBC1 ->> SAC1: order ↑ 小→大
    SAC1 ->> RBC1: 每行一次<br/>order ↑ 小→大
    RBC1 ->> RAC1: order ↑ 小→大
    RAC1 ->> CBC1: 每单元格一次<br/>order ↑ 小→大
    CBC1 ->> CAC1: order ↑ 小→大
    CAC1 ->> CAD: 写完数据后<br/>order ↑ 小→大
    CAD ->> RAD: 一行所有单元格后<br/>order ↑ 小→大
    RAD ->> SAD: Sheet全部写完后<br/>order ↑ 小→大
    SAD ->> WAD: 全部Sheet写完后<br/>order ↑ 小→大
    WAD ->> Finish