<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jdi.isc.product.center</groupId>
        <artifactId>jdi-isc-product-center</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>jdi-isc-product-center-domain</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>jdi-isc-product-center-domain</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <artifactId>log4j-api</artifactId>
            <groupId>org.apache.logging.log4j</groupId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.binlake</groupId>
            <artifactId>binlake-wave.client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <artifactId>guava</artifactId>
            <groupId>com.google.guava</groupId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.product.center</groupId>
            <artifactId>jdi-isc-product-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.order.center</groupId>
            <artifactId>jdi-isc-order-center-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.product.soa</groupId>
            <artifactId>jdi-isc-product-soa-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdi.isc.task.center</groupId>
            <artifactId>jdi-isc-task-center-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdi.isc.fulfillment.soa</groupId>
            <artifactId>jdi-isc-fulfillment-soa-api</artifactId>
        </dependency>
    </dependencies>


</project>
