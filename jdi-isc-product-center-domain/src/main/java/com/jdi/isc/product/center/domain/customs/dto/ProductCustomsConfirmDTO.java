package com.jdi.isc.product.center.domain.customs.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ProductCustomsConfirmDTO implements Serializable {
    /**
     * 产品海关确认的ID列表。
     */
    private List<Long> ids;
    /**
     * 操作员的名称。
     */
    private String operator;
    /**
     * 操作时间戳。
     */
    private Long operateTime;
    /**
     * 操作码，用于标识具体的操作类型。
     */
    private String operateCode;
} 