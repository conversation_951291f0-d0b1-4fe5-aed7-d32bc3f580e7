package com.jdi.isc.product.center.domain.stockInOutOrder.biz;


import lombok.Data;

@Data
public class StockInOutOrderResVO {


    /**
     * 出入库单编号
     */
    private String stockInOutOrderId;

    /**
     * 出入库单业务类型
     *
     * @see StockInOutOrderBizTypeEnum
     */
    private Integer orderBizType;




    /**
     * 出入库单状态
     *
     * @see StockInOutOrderStatusEnum
     */
    private Integer orderStatus;

    /**
     * 出入库单类型
     *
     * @see StockInOutOrderType
     */
    private Integer orderType;

    /**
     * 备货仓编号
     */
    private String stockUpWarehouseId;

    /**
     * 参考单号
     */
    private String referOrderNo;

    /**
     * 第三方来源标识
     */
    private String thirdDataSource;

    /**
     * 出入库wms单号
     */
    private String wmsInOutOrderNo;

    /**
     * 出入库wms时间
     */
    private Long wmsInOutTime;

    /**
     * 客户Id
     */
    private String clientCode;


    /**
     * 客户clientName
     */
    private String clientName;


    /**
     * sku总数量(所有skuNum的加合)
     */
    private Integer planSkuNum;

    /**
     * 实际出入库商品数量
     */
    private Integer actualSkuNum;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 采购单的有效状态
     */
    private Integer validState;


}
