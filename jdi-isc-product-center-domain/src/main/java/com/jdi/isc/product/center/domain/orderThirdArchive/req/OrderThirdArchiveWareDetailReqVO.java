package com.jdi.isc.product.center.domain.orderThirdArchive.req;

import com.jdi.isc.product.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 三方订单归档商品 实体类
 * @Author: zhaojianguo21
 * @Date: 2025/02/27 18:24
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class OrderThirdArchiveWareDetailReqVO extends BasicVO {

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空")
    private Long orderId;

    /**
     * mku编码
     */
    private Long mkuId;

    /**
     * 履约sku编码
     */
    private Long skuId;

    /**
     * 商品名称
     */
    @NotNull(message = "商品名称不能为空")
    private String name;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    private Integer num;

    /**
     * 商品税率
     */
    @NotNull(message = "商品税率不能为空")
    private BigDecimal tax;

    /**
     * 商品未税单价,4位小数
     */
    @NotNull(message = "商品未税单价,4位小数不能为空")
    private BigDecimal nakedPrice;

    /**
     * 商品含税单价,4位小数
     */
    @NotNull(message = "商品含税单价,4位小数不能为空")
    private BigDecimal price;

    /**
     * 归档批次号
     */
    private String archiveBatchNum;

}