package com.jdi.isc.product.center.domain.customs.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 商品关务草稿DTO
 * 用于保存/更新关务信息
 */
@Data
public class ProductCustomsDraftDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 国际skuId */
    private Long skuId;
    /** 国内skuId */
    private Long jdSkuId;
    /** 国家代码: TH,VN,MY,ID,HU,BR */
    private String countryCode;
    /** 国家hs code码 */
    private String hsCode;
    /** sku名称 */
    private String skuName;
    /** sku多语言名称 */
    private String skuLocalName;
    /** sku图片 */
    private String skuImage;
    /** 毛重 */
    private String weight;
    /** 长 */
    private String length;
    /** 宽 */
    private String wide;
    /** 高 */
    private String high;
    /** 采销erp */
    private String purchaseSalesErp;
    /** 是否存在订单 */
    private Integer orderExists;
    /** 重量数据来源 */
    private Integer weightDataSource;
    /** 期望完成时间 */
    private Long expectCompletionTime;
    /** 商品关务状态 */
    private Integer status;
    /** 类目名称 */
    private String categoryName;
    /** 品牌名称 */
    private String brandName;
    /** 商品上下架状态 */
    private Integer productStatus;
    /** 关务评估状态 */
    private Integer customsComplianceStatus;
    /** 强制性认证评估状态 */
    private Integer compulsoryCertificationStatus;
    /** 申报品名 */
    private String declarationName;
    /** 申报属地国商品名 */
    private String declarationLocalName;
    /** 申报要素 */
    private String declarationElement;
    /** 是否管制 */
    private Integer controls;
    /** 管制信息 */
    private String controlsInfo;
    /** 是否需强制认证 */
    private Integer compulsoryCertification;
    /** 强制认证信息 */
    private String compulsoryCertificationInfo;
    /** 品牌授权限制 */
    private Integer brandLicensing;
    /** 所属服务商 */
    private String assignedSupplier;
    /** 数据来源 */
    private String dataSource;
    /** 自增ID */
    private Long id;
    /** 备注 */
    private String remark;
    /** 创建者 */
    private String creator;
    /** 修改人 */
    private String updater;
    /** 创建时间 */
    private Long createTime;
    /** 最后修改时间 */
    private Long updateTime;
} 