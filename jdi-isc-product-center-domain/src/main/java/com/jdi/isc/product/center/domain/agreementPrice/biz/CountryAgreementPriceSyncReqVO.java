package com.jdi.isc.product.center.domain.agreementPrice.biz;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;
@Data
@NoArgsConstructor
public class CountryAgreementPriceSyncReqVO {
    @Size(max = 20, message = "id个数不能超过20个")
    @NotEmpty(message = "id集合不能为空")
    private List<Long> idList;
}
