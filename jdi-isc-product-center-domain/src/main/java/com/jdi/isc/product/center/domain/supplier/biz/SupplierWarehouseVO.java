package com.jdi.isc.product.center.domain.supplier.biz;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * @Description 供应商仓绑定信息
 * @Author: zhaojianguo21
 * @Date: 2024/03/18 17:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierWarehouseVO  {
    /**
     * 供应商的唯一标识符。
     */
    private String supplierCode;

    /** 货主编码 */
    @NotNull(message = "仓货主编码不能为空")
    @Length(max = 35, message = "仓货主编码最长 35")
    private String customerDeptCode;

    /** 货主id */
    @NotNull(message = "仓货主ID不能为空")
    private Long cargoOwnerId;
    /**
     * 仓库编号。
     */
    private Long warehouseId;

    /**
     * 变更动作
     * {@link com.jdi.isc.product.center.domain.enums.supplier.SupplierModifyActionEnum}
     */
    private Integer action;


    protected Long id;

    /** 备注*/
    protected String remark;

    /** 创建者*/
    protected String creator;

    /** 修改人*/
    protected String updater;

    /** 创建时间*/
    protected Long createTime;

    /** 最后修改时间*/
    protected Long updateTime;

    protected Integer yn;


}
