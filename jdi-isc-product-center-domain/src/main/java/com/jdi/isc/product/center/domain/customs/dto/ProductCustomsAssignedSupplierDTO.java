package com.jdi.isc.product.center.domain.customs.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Set;

@Data
public class ProductCustomsAssignedSupplierDTO implements Serializable {
    /**
     * 包含将被分配给海关供应商的产品ID集合。
     */
    private Set<Long> ids;
    /**
     * 海关供应商的ID。
     */
    private Long customsSupplierId;
    /**
     * 操作员的名称。
     */
    private String operator;
    /**
     * 操作时间戳。
     */
    private Long operateTime;
} 