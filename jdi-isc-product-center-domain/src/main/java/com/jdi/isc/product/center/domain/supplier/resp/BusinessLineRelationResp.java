package com.jdi.isc.product.center.domain.supplier.resp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * @description: 产品线返回实体类
    * @author: zhangjin176
    * @date: 2025/5/6 12:58
    **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessLineRelationResp {
    /**
     * 业务线关系实体的唯一标识符。
     */
    private Long id;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String businessLicenseName;

    /**
     * 品牌的唯一标识符
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 类目ID，用于关联商品和类目信息。
     */
    private Long categoryId;

    /**
     * 所有类目路径的字符串表示，用于展示类目层级结构。
     */
    private String categoryAllPathStr;

    /** 品牌授权书url **/
    private String authorizeUrl;

    /**
     * 商标注册证书的URL地址。
     */
    private String trademarkCertificateUrl;

    /**
     * 代理级别的整数值表示。
     */
    private Integer agentLevel;

    /**
     * 修改信息。
     */
    private String updater;

    /**
     * 创建时间戳，记录该实体的创建时间。
     */
    private Long  updateTime;

    /**
     * 有效状态标识，0表示无效，1表示有效。
     */
    private Boolean  valid;
}
