package com.jdi.isc.product.center.domain.agreementPrice.biz;

import com.jdi.isc.product.center.domain.validation.ValidateSpuGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/8
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class CountryAgreementPriceAuditVO {
    /**
     * SPUID
     */
    @Size(max = 20, message = "id个数不能超长")
    @NotEmpty(message = "id数组不能为空", groups = {ValidateSpuGroup.approve.class, ValidateSpuGroup.reject.class})
    private List<Long> ids;
    /**
     * 驳回原因
     */
    @Size(max = 200, message = "驳回原因超长")
    @NotNull(message = "驳回原因不能为空", groups = {ValidateSpuGroup.reject.class})
    private String rejectReason;
}
