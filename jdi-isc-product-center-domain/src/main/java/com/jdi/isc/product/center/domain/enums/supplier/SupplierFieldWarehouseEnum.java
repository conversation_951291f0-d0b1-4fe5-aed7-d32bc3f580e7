package com.jdi.isc.product.center.domain.enums.supplier;

import java.util.HashMap;
import java.util.Map;

/**
 * @description 供应商字段信息-账号
 * <AUTHOR>
 * @date 2024/05/06
 **/
public enum SupplierFieldWarehouseEnum {

    CUSTOMER_DEPT_CODE("customerDeptCode", "货主编码"),
    CARGO_OWNER_ID("cargoOwnerId", "货主Id"),
    WAREHOUSE_ID("warehouseId", "仓库编号"),

    ;

    private String code;
    private String desc;

    public static Map<String, SupplierFieldWarehouseEnum> enumMap = new HashMap();
    public static Map<String, String> codeDescMap = new HashMap();

    static {
        for (SupplierFieldWarehouseEnum val : values()) {
            enumMap.put(val.getCode(), val);
            codeDescMap.put(val.getCode(), val.getDesc());
        }
    }

    SupplierFieldWarehouseEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }

    public static SupplierFieldWarehouseEnum forCode(String code) {
        return enumMap.get(code);
    }
}