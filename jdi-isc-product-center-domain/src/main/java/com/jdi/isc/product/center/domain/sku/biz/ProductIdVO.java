package com.jdi.isc.product.center.domain.sku.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductIdVO {

    /**
     *  SKU ID 列表。
     */
    private List<Long> skuIds = new ArrayList<>();

    /**
     *  SPU ID 列表。
     */
    private List<Long> spuIds = new ArrayList<>();

    /**
     *  JD SKU ID 列表。
     */
    private List<Long> jdSkuIds = new ArrayList<>();

    /**
     * MKU ID 列表，用于标识多渠道商品的唯一ID。
     */
    private List<Long> mkuIds = new ArrayList<>();
}
