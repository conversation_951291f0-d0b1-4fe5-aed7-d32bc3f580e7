package com.jdi.isc.product.center.domain.common.biz;

import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @description 数据隔离
 * @date 2025/6/7 17:35
 */
@Data
public class DataIsolationQueryVO {

    /**
     * 黑名单erp
     */
    private Set<String> blackErps;

    /**
     * 黑名单pin
     */
    private Set<String> blackPins;

    /**
     * 黑名单client
     */
    private Set<String> blackClientCodes;

    /**
     * 黑名单supplier
     */
    private Set<String> blackSupplierCodes;

    /**
     * 是否是uat环境，默认不是
     */
    private boolean uat;

    /**
     * 黑名单pin
     */
    private Set<String> uatPins;

    /**
     * 黑名单client
     */
    private Set<String> uatClientCodes;
}
