package com.jdi.isc.product.center.domain.finance.invoice;

import lombok.Data;

/**
 * 客户发票资质详情响应
 */
@Data
public class QualificationDetailRspDTO {
    private Long id;
    private String countryCode;
    private String contractNum;
    private String clientCode;
    private String clientName;
    private String pin;
    private String invoiceTitle;
    private String customerTax;
    private String invoiceAddress;
    /** 开票扩展信息，json 形式 */
    private String extInfo;
    private Integer status;
    private String creator;
    private String updater;
    private Long createTime;
    private Long updateTime;
}


