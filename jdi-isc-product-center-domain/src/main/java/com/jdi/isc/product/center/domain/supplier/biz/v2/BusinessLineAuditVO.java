package com.jdi.isc.product.center.domain.supplier.biz.v2;

import com.jdi.isc.product.center.domain.common.biz.FieldDiffVO;
import com.jdi.isc.product.center.domain.supplier.biz.CategoryFullNodeVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
    * @description: 产品审核逻辑实体类
    * @author: zhangjin176
    * @date: 2025/5/9 23:35
    * @version:
    **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessLineAuditVO {
    /**
     * id
     */
    private Long id;
    /**
     * 供应商Code
     */
    private String supplierCode;
    /**
     * 品牌id
     */
    private String brandId;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 所有类目路径的字符串表示，用于记录产品所属的完整类目路径。
     */
    private String categoryAllPathStr;
    /**
     * 类目id
     */
    private Long categoryId;
    /**
     * 品牌授权书url
     */
    private String authorizeUrl;
    /**
     * 变更动作
     * {@link com.jdi.isc.product.center.domain.enums.supplier.SupplierModifyActionEnum}
     */
    private Integer action;
    /**
     * 商标注册证书的URL地址。
     */
    private String trademarkCertificateUrl;
    /**
     * 代理级别
     */
    private Integer agentLevel;
    /**
     * 代理级别的字符串表示形式。
     */
    private String agentLevelStr;
    /**
     * 创建时间戳，表示该实体对象的创建时间。
     */
    private Long createTime;
    /**
     * 创建者用户名
     */
    private String creator;
    /**
     * 更新时间戳，表示该实体对象的最后一次更新时间。
     */
    private Long updateTime;
    /**
     * 最后更新者用户名
     */
    private String updater;
}
