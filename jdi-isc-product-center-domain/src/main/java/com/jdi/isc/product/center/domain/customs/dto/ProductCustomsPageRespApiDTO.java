package com.jdi.isc.product.center.domain.customs.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * JSF接口关务列表分页响应DTO
 * <AUTHOR>
 * @date 2024/07/18 10:25
 */
@Data
public class ProductCustomsPageRespApiDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 记录列表
     */
    private List<ProductCustomsInfoApiDTO> records;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 页大小
     */
    private Long size;
    
    /**
     * 页码
     */
    private Long index;
    
    /**
     * 游标
     */
    private List<Object> cursor;
} 