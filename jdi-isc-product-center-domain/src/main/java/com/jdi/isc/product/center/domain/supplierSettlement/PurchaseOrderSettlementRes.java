package com.jdi.isc.product.center.domain.supplierSettlement;

import com.jdi.isc.product.center.domain.purchaseOrder.biz.PurchaseOrderWareVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
public class PurchaseOrderSettlementRes {

    private String purchaseOrderId;

    private String accountCurrency;

    private BigDecimal purchaseTotalPrice;

    private BigDecimal waresPurchaseTotalPrice;

    private List<PurchaseOrderWareVO> purchaseOrderWareVOS;
}
