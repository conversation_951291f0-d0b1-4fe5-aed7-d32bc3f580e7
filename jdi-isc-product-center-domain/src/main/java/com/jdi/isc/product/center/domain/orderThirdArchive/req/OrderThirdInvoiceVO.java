package com.jdi.isc.product.center.domain.orderThirdArchive.req;

import com.jdi.isc.product.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 订单三方归档 实体类
 * @Author: zhaojianguo21
 * @Date: 2025/02/25 21:56
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class OrderThirdInvoiceVO extends BasicVO {

    /**
     * 开票批次号
     */
    private List<String> batchNum;

}