package com.jdi.isc.product.center.domain.agreementPrice.biz;

import com.jdi.isc.product.center.domain.common.biz.BasicVO;
import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 国家协议价分页查询对象
 * @Author: wangpeng965
 * @Date: 2025/03/03 16:45
 **/

@Data
public class CountryAgreementPricePageVO extends BasicVO implements Serializable  {

    /**
     * 业务编号
     */
    private String bizNo;

    /**
     * MKUID
     */
    private Long mkuId;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 目标国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * 末级类目id
     */
    private Long lastCatId;

    /**
     * 品牌
     */
    private Long brandId;

    /**
     * 协议价
     */
    private BigDecimal agreementPrice;

    /**
     * MKUID的标题信息
     */
    private String mkuTitle;

    /**
     * 最后一级类目的名称
     */
    private String catName;

    /**
     * SKU ID列表，用于批量查询和操作。
     */
    private List<Long> skuIds;

    /**
     * 国家成本价
     */
    private BigDecimal countryCostPrice;

    /**
     * 货币类型。
     */
    private String currency;

    /**
     * 审核状态，1:审核通过，2:驳回 3:待审核 0:取消
     */
    private Integer auditStatus;

    /**
     * 审批人
     */
    private String auditor;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 利润率
     */
    private BigDecimal profitRate;

    /**
     * 预警情况
     */
    private String warningMsg;

    /**
     * 采销
     * */
    private String buyer;

    /**
     * 审批等级。
     */
    private Integer level;
    /**
     * 国家协议价计算流程。
     */
    private String agreementMark;


    /**
     * 国家成本价计算流程。
     */
    private String costMark;

    /**
     * jd价格ID
     */
    private Long jdPriceId;
    /**
     * 国家跨境入仓价
     */
    private BigDecimal countryWarehousingPrice;
    /**
     * 预估退税金额
     */
    private BigDecimal refundTaxPrice;
    /**
     * 国家VIP价格
     */
    private BigDecimal countryVipPrice;
    /**
     * 京东平台的商品价格。
     */
    private BigDecimal jdPrice;
    /**
     * 国家跨境入仓价的标记。
     */
    private String warehousingPriceMark;

    /**
     * 预估退税金额的标记。
     */
    private String refundTaxPriceMark;

    /**
     * 京东平台的商品价格标记。
     */
    private String jdPriceMark;

    /**
     * 建议国家协议价
     */
    private BigDecimal suggestAgreementPrice;
    /**
     * 建议国家协议价计算流程。
     */
    private String suggestAgreementMark;

    /**
     * 协议价更新时间。
     */
    private Long agreementUpdateTime;

    /**
     * 国家池入池状态，1-已入池，3-未入池
     *
     * {@link CountryMkuPoolStatusEnum}
     */
    private Integer countryMkuPoolStatus;

    /**
     * 是否入客户池 1-绑定，0-未绑定
     *
     * {@link CustomerMkuBindEnum}
     */
    private String customerMkuPoolStatus;

    /**
     * 不可售阈值
     */
    private BigDecimal unsellableThreshold;

    /**
     * 不可售阈值更新时间
     */
    private Long unsellableThresholdTime;

    /**
     * 可售状态
     */
    private Integer availableSaleStatus;

    /**
     * 价格可售状态更新时间
     */
    private Long availableSaleStatusTime;
}
