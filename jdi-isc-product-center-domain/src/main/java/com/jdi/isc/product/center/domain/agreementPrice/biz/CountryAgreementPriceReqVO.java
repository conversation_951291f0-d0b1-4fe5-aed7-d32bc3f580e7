package com.jdi.isc.product.center.domain.agreementPrice.biz;

import com.jdi.isc.product.soa.api.common.BaseReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CountryAgreementPriceReqVO extends BaseReqDTO implements Serializable {
    /**
     * 业务编号
     */
    private String bizNo;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 目标国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * 一级类目id
     */
    private Long firstCatId;

    /**
     * 二级类目id
     */
    private Long secondCatId;

    /**
     * 三级类目id
     */
    private Long thirdCatId;

    /**
     * 末级类目id
     */
    private Long lastCatId;

    /**
     * 品牌
     */
    private Long brandId;

    /**
     * 协议价
     */
    private BigDecimal agreementPrice;

    /**
     * 分类ids
     * */
    private Set<Long> catIds;

    /**
     * mkuIds
     * */
    private List<Long> mkuIds;

    /**
     * skuIds
     * */
    private List<Long> skuIds;

    /**
     * 国家成本价
     */
    private BigDecimal countryCostPrice;

    /**
     * 货币类型。
     */
    private String currency;

    /**
     * SKU的唯一标识符。
     */
    private Long skuId;

    /**
     * SKU的唯一标识符。
     */
    private Long mkuId;

    /**
     * 调价原因
     */
    private String adjustmentPriceReason;

    /**
     * 附件
     */
    private List<String> attachmentUrls;
}
