package com.jdi.isc.product.center.domain.customs.dto;

import com.alibaba.fastjson.JSONObject;
import com.jdi.isc.product.soa.api.validation.ValidateCustomsGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 商品关务草稿DTO
 * 用于保存/更新关务信息
 */
@Data
public class ProductCustomsAssessmentDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 商品关务草稿的唯一标识符
     */
    private Long id;
    /**
     * 商品的 SKU ID，用于关联商品信息。
     */
    private Long skuId;
    /**
     * 京东 SKU ID，用于关联商品信息。
     */
    private Long jdSkuId;

    /**
     * 国家或地区的编码，用于标识商品所属的国家或地区。
     */
    private String countryCode;
    /**
     * 操作员信息
     */
    private String operator;
    /**
     * 操作时间戳，记录关务草稿的最后一次操作时间
     */
    private Long operateTime;
    /**
     * 操作编码，用于标识当前关务草稿的操作类型或状态。
     */
    private String operateCode;
    /**
     * 海关商品编码，用于关务申报和清关。
     */
    private String hsCode;
    /**
     * 关务草稿的申报名称
     */
    private String declarationName;
    /**
     * 关务草稿的申报本地名称
     */
    private String declarationLocalName;
    /**
     * 关务草稿的申报要素信息。
     */
    private String declarationElement;
    /**
     * 控制项，用于标识关务草稿的控制状态或类型。
     */
    private Integer controls;
    /**
     * 控制项信息，用于记录关务草稿的控制状态或类型的详细信息。
     */
    private String controlsInfo;
    /**
     * 品牌授权状态，0表示未授权，1表示已授权
     */
    private Integer brandLicensing;
    /**
     * 存储税务信息
     */
    private String taxJsonInfo;
    /**
     * 备注信息，用于存储额外的说明或备注。
     */
    private String remark;
} 