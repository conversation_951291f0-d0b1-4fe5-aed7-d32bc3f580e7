package com.jdi.isc.product.center.domain.hscode.biz;

import com.jdi.isc.product.center.domain.common.biz.BasePageVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 6个国家的HsCode分页查询请求
 * 支持国家：BR(巴西)、HU(匈牙利)、ID(印度尼西亚)、MY(马来西亚)、TH(泰国)、VN(越南)
 * @Author: zhaojianguo21
 * @Date: 2024/02/21 09:21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CountryHsCodePageRequest extends BasePageVO implements Serializable {
    /** 国家码 */
    private String countryCode;

    /** 海关编码列表 */
    private List<String> hsCodes;
}