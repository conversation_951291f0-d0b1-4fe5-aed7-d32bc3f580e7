package com.jdi.isc.product.center.domain.countryMku;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.product.center.domain.task.dto.BaseWorkerDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 国家池翻译确认导入Excel DTO
 * @Author: system
 * @Date: 2024/12/20
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class CountryMkuTranslationImportExcelDTO extends BaseWorkerDTO {
    
    @ExcelProperty(value = "MKU ID", index = 0)
    @NotNull(message = "MKU ID不能为空")
    private Long mkuId;
    
    @ExcelProperty(value = "SPU ID", index = 1)
    private Long spuId;
    
    @ExcelProperty(value = "国家代码", index = 2)
    @NotBlank(message = "国家代码不能为空")
    private String targetCountryCode;
    
    @ExcelProperty(value = "目标语言", index = 3)
    @NotBlank(message = "目标语言不能为空")
    private String targetLang;
    
    @ExcelProperty(value = "确认后的MKU标题", index = 4)
    private String confirmedMkuTitle;
    
    @ExcelProperty(value = "确认后的SPU标题", index = 5)
    private String confirmedSpuTitle;
    
    @ExcelProperty(value = "备注", index = 6)
    private String remark;
}
