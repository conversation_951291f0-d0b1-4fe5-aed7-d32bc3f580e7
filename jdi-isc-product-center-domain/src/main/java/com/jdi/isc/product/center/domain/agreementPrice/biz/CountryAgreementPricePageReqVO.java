package com.jdi.isc.product.center.domain.agreementPrice.biz;


import com.jdi.isc.product.center.domain.common.biz.BasePageVO;
import com.jdi.isc.product.soa.api.common.CountryMkuPoolStatusEnum;
import com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CountryAgreementPricePageReqVO extends BasePageVO implements Serializable {
    /**
     * 业务编号
     */
    private String bizNo;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String sourceCountryCode;

    /**
     * 目标国、国家站、国家码，ISO 3166-1 两字母代码
     */
    private String targetCountryCode;

    /**
     * 一级类目id
     */
    private Long firstCatId;

    /**
     * 二级类目id
     */
    private Long secondCatId;

    /**
     * 三级类目id
     */
    private Long thirdCatId;

    /**
     * 末级类目id
     */
    private Long lastCatId;

    /**
     * 品牌
     */
    private Long brandId;

    /**
     * 协议价
     */
    private BigDecimal agreementPrice;

    /**
     * 分类ids
     * */
    private Set<Long> catIds;

    /**
     * mkuIds
     * */
    private List<Long> mkuIds;

    /**
     * skuIds
     * */
    private List<Long> skuIds;

    /**
     * 国家成本价
     */
    private BigDecimal countryCostPrice;

    /**
     * 货币类型。
     */
    private String currency;

    /**
     * 类型关键字，用于区分不同类型的请求。
     */
    private String typeKey;

    /**
     * 比较类型，用于建议协议价和国家协议价的比较方式 1:大于，2:小于，3:等于。
     */
    private Integer comparisonType;

    /**
     * erp。
     */
    private String pin;

    /**
     * skuId、spuId、jdSkuId、mkuId
     */
    private List<Long> productIds;

    /**
     * 国家池入池状态，1-已入池，3-未入池
     *
     * {@link CountryMkuPoolStatusEnum}
     */
    private Integer countryMkuPoolStatus;

    /**
     * 是否入客户池 1-绑定，0-未绑定
     *
     * {@link CustomerMkuBindEnum}
     */
    private String customerMkuPoolStatus;

    /**
     * 可售状态
     */
    private Integer availableSaleStatus;

    /**
     * 审核状态，1:审核通过，2:驳回 3:待审核 0:取消
     */
    private Integer auditStatus;
}