package com.jdi.isc.product.center.domain.stockInOutOrder.biz;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author：xubing82
 * @Date：2025/4/1 18:22
 * @Description：UpdateStockInOutOrderStatusReqVO
 */
@Data
public class UpdateStockInOutOrderStatusReqVO {
    /**
     * 出入库订单号
     */
    @NotNull(message = "stockInOutOrderId is not null")
    private String stockInOutOrderId;

    /**
     * 操作
     */
    @NotNull(message = "operate is not null")
    private String operate;


}
