package com.jdi.isc.product.center.domain.supplier.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdi.isc.product.center.domain.common.po.BasicPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description 供应商资质实体类。
 * @Author: zhaojianguo21
 * @Date: 2024/03/18 17:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("jdi_isc_supplier_warehouse")
public class SupplierWarehousePO extends BasicPO {

    /**
     * 供应商Code
     */
    private String supplierCode;

    /**
     * 仓库ID，标识供应商关联的仓库唯一编号
     */
    private String warehouseId;
    /**
     * 货主编码
     */
    private String customerDeptCode;
    /**
     * 货主ID
     */
    private Long cargoOwnerId;

}
