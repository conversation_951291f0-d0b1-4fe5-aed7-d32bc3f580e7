package com.jdi.isc.product.center.domain.supplier.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * @description: 供应商操作类型实体类
    * @author: zhangjin176
    * @date: 2025/5/6 20:12
    * @version:
    **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessLineEditDTO {

    /**
     * 动作类型，表示对供应商产品线的操作类型。
     */
    private String action;
    /**
     * 包含供应商产品线的详细信息。
     */
    private BusinessLineDataDTO data;



}
