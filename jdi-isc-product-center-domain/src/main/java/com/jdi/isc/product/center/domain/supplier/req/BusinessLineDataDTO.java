package com.jdi.isc.product.center.domain.supplier.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
    * @description: 产品线编辑/保存详情实体类
    * @author: zhangjin176
    * @date: 2025/5/6 20:12
    * @version:
    **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessLineDataDTO {

    /**
     * 授权链接地址
     */
    private String authorizeUrl;
    /**
     * 商标证书的URL地址
     */
    private String trademarkCertificateUrl;
    /**
     * 代理商的等级信息
     */
    private Integer agentLevel;
    /**
     * 品牌ID，用于关联对应的品牌信息。
     */
    private Long brandId;
    /**
     * 品牌名称，用于展示和识别具体的品牌信息。
     */
    private String brandName;
    /**
     * 商品分类ID，用于关联对应的商品分类信息。
     */
    private Integer categoryId;
    /**
     * 类目全路径
     */
    private String categoryStr;


}
