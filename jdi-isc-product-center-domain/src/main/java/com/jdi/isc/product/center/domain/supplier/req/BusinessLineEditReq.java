package com.jdi.isc.product.center.domain.supplier.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
    * @description: 产品线编辑实体类
    * @author: zhangjin176
    * @date: 2025/5/6 20:12
    * @version:
    **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessLineEditReq {
    /**
     * 供应商代码，必传
     */
    @NotBlank(message = "供应商编码不能为空")
    private String supplierCode;

    /**
     * 存储供应商产品线信息的映射表，键为产品线ID，值为对应的BusinessLineEditDTO对象。
     */
    private Map<String, BusinessLineEditDTO> data;

}
