package com.jdi.isc.product.center.domain.finance.invoice;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 保存发票信息请求DTO
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
public class SaveInvoiceProReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 二段运单编号
     */
    private String waybillNo;

    /**
     * 订单号列表
     */
    private List<Long> orderIds;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 开票日期
     */
    private String customerInvoiceDay;

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 开票金额计算模式
     */
    private Integer calculationMode;

    /**
     * 发票类型 1,CI形式发票; 2,VAT增值税发票
     */
    private Integer billingType;

    /**
     * 开票模式
     * 多单一票1
     * 一单一票2
     */
    private Integer invoiceMode;

    /**
     * 购买人名称
     */
    private String buyerName;

    /**
     * 购买人税号
     */
    private String buyerTaxNo;

    /**
     * 购买人地址
     */
    private String buyerAddress;

}
