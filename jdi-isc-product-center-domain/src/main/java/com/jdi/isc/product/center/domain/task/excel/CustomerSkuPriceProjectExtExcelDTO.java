package com.jdi.isc.product.center.domain.task.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * The type Customer sku price project ext excel dto.
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerSkuPriceProjectExtExcelDTO extends CustomerSkuPriceExcelDTO implements Serializable {

    /**
     * sku名称
     */
    @ExcelProperty(value = "sku名称", index = 6)
    private String skuName;

    /**
     * 预警状态。
     */
    @ExcelProperty(value = "预警状态", index = 7)
    private String warningStatusName;

    /**
     * 预警状态。
     */
    @ExcelIgnore
    private Integer warningStatus;

    /**
     * 利润率。
     */
    @ExcelProperty(value = "利润率", index = 8)
    private String profitRateFormat;

    /**
     * 低利润率阈值
     */
    @ExcelProperty(value = "低利润率阈值", index = 9)
    private String profitLimitRateFormat;

    /**
     * 超低利润率阈值
     */
    @ExcelProperty(value = "超低利润率阈值", index = 10)
    private String lowProfitLimitRateFormat;

    /**
     * 边际负毛阈值
     */
    @ExcelProperty(value = "边际负毛阈值", index = 11)
    private String grossProfitFormat;

    /**
     * 国家成本价
     */
    @ExcelProperty(value = "国家成本价", index = 12)
    private String countryCostPrice;

    /**
     * 国家成本价计算流程。
     */
    @ExcelProperty(value = "国家成本价计算公式", index = 13)
    private String costMark;
}