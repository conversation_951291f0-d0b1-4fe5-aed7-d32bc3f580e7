package com.jdi.isc.product.center.domain.hscode.biz;

import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 6个国家的HsCode信息
 * 支持国家：BR(巴西)、HU(匈牙利)、ID(印度尼西亚)、MY(马来西亚)、TH(泰国)、VN(越南)
 * @Author: zhangjin
 * @Date: 2024/02/20 17:59
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CountryHsCodeVO extends BasicDTO {
    /** 国家码 */
    @NotNull(message = "国家不能为空")
    private String countryCode;
    /** 海关编码 */
    @NotNull(message = "HsCode不能为空")
    @Length(max = 20, message = "HsCode最长20位")
    private String hsCode;
    /** 是否管制 ✅ */
    private Integer controls;
    /** 管制信息 ✅ */
    private String controlsInfo;
    /** 进口关税率II (匈牙利、巴西)✅ */
    private BigDecimal importTax;
    /** 工业产品税率IPI (巴西) ✅ */
    private BigDecimal industryProductTax;
    /** 社会一体化费率PIS (巴西) ✅ */
    private BigDecimal socialIntegrationTax;
    /**社会保险融资税 CONFINS率 (巴西) ✅ */
    private BigDecimal confinsTax;
    /** 反倾销税率 (越南、匈牙利、巴西) ✅ */
    private BigDecimal antiDumpingTax;
    /** ICMS流转税率 (巴西) ✅ */
    private BigDecimal icmsFlowTax;
    /** 增值税率 (越南、印尼、匈牙利)✅ */
    private BigDecimal valueAddedTax;
    /** 反补贴税率 (马来、匈牙利) ✅ */
    private BigDecimal antiSubsidyTax;
    /** 最惠国关税率 (越南、马来) ✅ */
    private BigDecimal mfnTax;
    /** 原产地优惠关税率FormE (越南、马来、印尼) ✅ */
    private BigDecimal originMfnTax;
    /** 销售税率SST (马来)✅ */
    private BigDecimal saleTax;
    /** 目的国/地区消费税 （越南） ✅ */
    private BigDecimal consumptionTax;
    /** 预扣款税(印尼) ✅ */
    private BigDecimal withholdingTax;
    /** 贸易保护关税(印尼)✅ */
    private BigDecimal tradeProtectionTax;
    /** 奢侈品税 (印尼)✅*/
    private BigDecimal luxuryTax;
    /** local tax 本地税(泰国) ✅ */
    private BigDecimal localTax;
    /** 原产地优惠关税（泰国） ✅*/
    private BigDecimal formeTax;
    /** 环保税单价 越南 ✅*/
    private BigDecimal environmentalTaxUnitPrice;
    /** 普通关税率 (印尼) ✅*/
    private BigDecimal normalImportTax;
    /**
     * 出口税率(中国)✅
     */
    private BigDecimal exportTaxRate;
    /**
     * 出口退税率(中国) ✅
     */
    private BigDecimal exportRebateRate;
}