package com.jdi.isc.product.center.domain.price.productprice;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * The type Project price upload excel dto.
 *
 * <AUTHOR>
 */
@Data
public class ProjectPriceUploadExcelVO implements Serializable {

    /**
     * 项目价格申请单编号
     */
    @NotNull(message = "项目价格申请单编号不能为空")
    private String projectCode;

    /**
     * 上传的文件路径，eg：<a href="http://xx.png">图片链接</a>
     */
    @NotEmpty(message = "上传的文件路径不能为空")
    private String fileUrl;

    /**
     * 业务类型
     */
    @NotNull(message = "业务类型不能为空")
    private Integer bizType;
}
