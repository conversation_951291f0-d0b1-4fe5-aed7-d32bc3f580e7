package com.jdi.isc.product.center.domain.orderThirdArchive.biz;

import com.jdi.isc.product.center.domain.common.biz.BasePageVO;
import com.jdi.isc.product.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 三方订单归档商品分页查询对象
 * @Author: zhaojianguo21
 * @Date: 2025/02/27 18:24
 **/

@Data
public class OrderThirdArchiveWarePageVO implements Serializable {


    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Request extends BasePageVO implements Serializable {
        /**
         * 订单id
         */
        private Long orderId;
    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BasicVO implements Serializable{
        /**
         * 订单id
         */
        private Long orderId;

        /**
         * mku编码
         */
        private Long mkuId;

        /**
         * 履约sku编码
         */
        private Long skuId;

        /**
         * 商品名称
         */
        private String name;

        /**
         * 数量
         */
        private Integer num;

        /**
         * 商品税率
         */
        private BigDecimal tax;

        /**
         * 商品未税单价,4位小数
         */
        private BigDecimal nakedPrice;

        /**
         * 商品含税单价,4位小数
         */
        private BigDecimal price;

        /**
         * 归档批次号
         */
        private String archiveBatchNum;

    }

}