package com.jdi.isc.product.center.domain.order.comment.biz;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 订单留言保存更新请求
 *
 * @author: zhangjin176
 * @date: 2025/5/23 11:35
 * @version:
 **/
@Data
public class OrderCommentSaveReq {
    private @NotNull(
            message = "订单id不能为空"
    ) Long orderId;
    private @NotNull(
            message = "留言内容不能为空"
    ) String commentText;
}
