package com.jdi.isc.product.center.domain.customs.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 商品关务草稿DTO
 * 用于保存/更新关务信息
 */
@Data
public class ProductCustomsCompulsoryCertificateDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String operator;
    private Long operateTime;
    private String countryCode;
    private String operateCode;
    private Long skuId;
    private Long jdSkuId;
    private Integer compulsoryCertification;
    private String compulsoryCertificationInfo;
    private String remark;
} 