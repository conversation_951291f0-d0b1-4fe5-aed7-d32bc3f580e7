package com.jdi.isc.product.center.domain.supplierSettlement;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Objects;
import java.util.Set;

@Data
public class SupplierSettlementReq {

    /**
     * 采购订单ID
     */
    private String purchaseOrderId;

    /**
     * 发票审批状态
     */
    private Integer invoiceApprovalStatus;

    /**
     * 发货开始时间
     */
    private Long beginShippedTime;

    /**
     * 发货结束时间
     */
    private Long endShippedTime;

    /**
     * 采购单完成开始时间
     */
    private Long beginFinishTime;

    /**
     * 采购单完成结束时间
     */
    private Long endFinishTime;


    /**
     * 供应商确认状态
     */
    private Integer supplierConfirmStatus;

    /**
     * 支付状态
     */
    private Integer payStatus;

    /**
     * 支付结束时间
     */
    private Long payTimeStart;

    /**
     * 支付开始时间
     */
    private Long payTimeEnd;

    /**
     * 升序 1，降序 -1
     */
    private Integer sort;

    /**
     * 排序类型
     */
    private String sortType;

    /**
     * 国家
     */
    @NotNull(message = "国家码不能为空")
    private String countryCode;

    /**
     * 起始页
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码错误")
    private Long index;

    /**
     * 分页大小
     */
    @NotNull(message = "页大小不能为空")
    @Range(min=1, max = 200, message = "分页大小错误")
    private Long size;

    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * 开始时间范围
     */
    private Long timeStart;

    /**
     * 结束时间范围
     */
    private Long timeEnd;

    private Integer shippedTimeSort;

    private Integer finishTimeSort;

    private String lang;

    private Integer purchaseOrderType;

    /**
     * 结算单号
     */
    private String requestId;

    private Set<String> purchaseOrderIds;

    public void preParam(){
        if (StringUtils.isNotBlank(sortType)) {
            if ("finish".equals(sortType)) {
                finishTimeSort = sort;
                if (Objects.nonNull(timeStart)) {
                    beginFinishTime = timeStart;
                }
                if (Objects.nonNull(timeEnd)) {
                    endFinishTime = timeEnd;
                }
            }

            if ("shipped".equals(sortType)) {
                shippedTimeSort = sort;
                if (Objects.nonNull(timeStart)) {
                    beginShippedTime = timeStart;
                }
                if (Objects.nonNull(timeEnd)) {
                    endShippedTime = timeEnd;
                }
            }
        }

        if (StringUtils.isBlank(purchaseOrderId)) {
            this.purchaseOrderId = null;
        }

        if (StringUtils.isBlank(requestId)) {
            this.requestId = null;
        }
    }
}
