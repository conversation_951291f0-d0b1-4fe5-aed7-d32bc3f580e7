package com.jdi.isc.product.center.domain.businessLog;

import com.jdi.isc.library.i18n.annotations.JdiI18n;
import lombok.Data;

/**
 * @description: 操作日志实体
 * @author: zhaokun51
 * @time: 2025/08/04 14:25
 */
@Data
public class BusinessLogResp {

    /**
     * 业务类型
     * */
    private String bizType;
    /***
     * 业务唯一健
     */
    private String bizCode;
    /**
     * 操作动作（如审批通过）
     * */
    @JdiI18n
    private String opName;
    /**
     * 操作内容（如商品一级审批）
     * */
    @JdiI18n
    private String opContent;
    /**
     * 操作备注
     * */
    private String opRemark;
    /**
     *额外存储JSON结构(不允许超过5120个字符)
     * */
    private String extJson;
    /**
     * 创建者
     */
    private String creator;
    /**
     * 记录操作日志的创建时间
     */
    private Long createTime;
}
