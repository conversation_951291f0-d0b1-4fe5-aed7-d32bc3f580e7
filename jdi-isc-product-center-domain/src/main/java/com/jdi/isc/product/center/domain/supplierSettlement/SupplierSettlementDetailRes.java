package com.jdi.isc.product.center.domain.supplierSettlement;


import com.jdi.isc.product.center.domain.common.biz.OperateButtonVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SupplierSettlementDetailRes {

    private String requestId;
    private BigDecimal amount;
    private Integer supplierConfirmStatus;
    private Date payTime;
    private Integer opStatus;
    private String ouName;
    private String currency;

    private List<PurchaseSettlementRes> purchaseSettlementList;

    /**
     * 操作列表
     */
    private List<OperateButtonVO> operateVOList;
}
