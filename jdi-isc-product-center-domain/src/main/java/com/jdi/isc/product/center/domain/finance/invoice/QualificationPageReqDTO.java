package com.jdi.isc.product.center.domain.finance.invoice;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * 客户发票资质分页请求
 */
@Data
public class QualificationPageReqDTO {
    /** 起始页 */
    @NotNull(message = "起始页不能为空")
    private Long index;
    /** 页大小 */
    @NotNull(message = "页大小不能为空")
    private Long size;
    /** 国家码 */
    private String countryCode;
    /** 合同号 */
    private String contractNum;
    /** 发票抬头 */
    private String invoiceTitle;
    /** 启用状态 0停用，1启用 */
    private Integer status;
    /** 客户号 */
    private String clientCode;
    /** 下单人pin */
    private String pin;
}


