package com.jdi.isc.product.center.domain.orderThirdArchive.biz;

import com.jdi.isc.product.center.domain.common.biz.BasePageVO;
import com.jdi.isc.product.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 订单三方归档分页查询对象
 * @Author: zhaojianguo21
 * @Date: 2025/02/25 21:56
 **/

@Data
public class OrderThirdArchivePageVO implements Serializable {


    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Request extends BasePageVO implements Serializable {
        /**
         * 订单
         */
        private Long orderId;

        /**
         * 父订单号
         */
        private Long pOrderId;

        /**
         * 合同号
         */
        private String contractNum;

        /**
         * 客户号
         */
        private String clientCode;

        /**
         * 归档时间
         */
        private Long archiveTimeStart;

        /**
         * 归档时间
         */
        private Long archiveTimeEnd;

        /**
         * 成本中心
         */
        private String costCenter;

        /**
         * 费用科目
         */
        private String loanPro;

        /**
         * 申请人工号
         */
        private String empNo;

        /**
         * 需求人工号
         */
        private String demandNo;

        /**
         * 归档批次号
         */
        private String archiveBatchNum;

        /**
         * 开票批次号
         */
        private String invoiceBatchNum;

        /**
         * 工厂（公司）代码
         */
        private String companyCode;

        /**
         * 发票状态
         */
        private Integer makeInvoiceStatus;

        /**
         * 公司名称
         */
        private String companyName;
    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BasicVO implements Serializable{
        /**
         * 订单
         */
        private Long orderId;

        /**
         * 父订单号
         */
        private Long pOrderId;

        /**
         * 合同号
         */
        private String contractNum;

        /**
         * 客户号
         */
        private String clientCode;

        /**
         * bpm流程单号
         */
        private String bpmId;

        /**
         * 归档时间
         */
        private Long archiveTime;

        /**
         * 订单含税金额
         */
        private BigDecimal orderPrice;

        /**
         * 币种:VND越南,THB泰国,CNY人民币,USD美元
         */
        private String currency;

        /**
         * 工厂名称（即发票抬头）
         */
        private String companyName;

        /**
         * 成本中心
         */
        private String costCenter;

        /**
         * 费用科目
         */
        private String loanPro;

        /**
         * 申请人工号
         */
        private String empNo;

        /**
         * 需求人工号
         */
        private String demandNo;

        /**
         * 归档批次号
         */
        private String archiveBatchNum;

        /**
         * 开票批次号
         */
        private String invoiceBatchNum;

        /**
         * 工厂（公司）代码
         */
        private String companyCode;

        /**
         * 发票状态
         */
        private Integer makeInvoiceStatus;

        /**
         * 发票号
         */
        private String invoiceNo;

        // 形式发票创建时间
        private Long makeInvoiceTime;

        // 人工上传url
        private String manualUrl;

        // 人工上传发票状态：0=待上传，1=已上传
        private Integer manualInvoiceStatus;

        // 人工上传发票时间
        private Long manualInvoiceTime;

        // 回传发票时间
        private Long pushInvoiceTime;

    }

}