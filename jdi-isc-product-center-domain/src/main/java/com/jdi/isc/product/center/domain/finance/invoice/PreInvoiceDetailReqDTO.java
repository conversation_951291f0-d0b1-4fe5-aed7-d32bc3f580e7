package com.jdi.isc.product.center.domain.finance.invoice;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 获取预开票明细信息请求DTO
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
public class PreInvoiceDetailReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号列表
     */
    private List<Long> orderIds;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 国家码
     */
    private String countryCode;
}
