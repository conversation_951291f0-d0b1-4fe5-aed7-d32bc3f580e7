package com.jdi.isc.product.center.domain.orderThirdArchive.biz;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * @Description: 订单三方归档订单开发票
 * @Author: zhaojianguo21
 * @Date: 2025/02/25 21:56
 **/

@Data
public class OrderThirdArchiveMakeInvoiceVO implements Serializable {


    @Data
    @NoArgsConstructor
    public static class Request implements Serializable {
        private Set<Long> ids;
        private Set<Long> orderIds;
        private Long invoiceTime;
    }

    @Data
    @NoArgsConstructor
    public static class Response implements Serializable{

    }

}