package com.jdi.isc.product.center.domain.customs.req;

import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 关务列表查询请求DTO
 * <AUTHOR>
 * @date 2024/07/18 10:25
 */
@Data
public class CustomsPageReqDTO {
    /**
     * ID集合。
     */
    private Set<Long> ids;
    /**
     * 国内skuId
     */
    private List<Long> jdSkuIds;

    /**
     * 国际skuId
     */
    private List<Long> skuIds;
    
    /**
     * 进出口国家代码: TH,VN,MY,ID,HU,BR
     */
    private String countryCode;
    
    /**
     * 国家hs code码
     */
    private String hsCode;
    
    /**
     * 数据来源
     */
    private String dataSource;
    
    /**
     * 创建时间范围开始
     */
    private Long createTimeStart;
    
    /**
     * 创建时间范围结束
     */
    private Long createTimeEnd;
    
    /**
     * 评估截止时间范围开始
     */
    private Long expectCompletionTimeStart;
    
    /**
     * 评估截止时间范围结束
     */
    private Long expectCompletionTimeEnd;
    
    /**
     * 商品关务状态
     */
    private Integer status;
    
    /**
     * 所属服务商
     */
    private String customsSupplierId;

    /**
     * 是否存在订单
     */
    private Boolean hasOrder;
    
    /**
     * 关务评估状态
     */
    private Integer customsComplianceStatus;
    
    /**
     * 强制性认证评估状态
     */
    private Integer compulsoryCertificationStatus;
    
    /**
     * hs code码
     */
    private Boolean hasHsCode;
    
    /**
     * 是否有申报要素
     */
    private Boolean hasDeclarationElement;
    
    /**
     * 申报英文品名
     */
    private Boolean hasDeclarationName;
    
    /**
     * 申报本地品名
     */
    private Boolean hasDeclarationLocalName;
    
    /**
     * 采销erp
     */
    private String purchaseSalesErp;
    
    /**
     * 起始页
     */
    private Long index;
    
    /**
     * 页大小
     */
    private Long size;
    
    /**
     * 偏移量
     */
    private Long offset;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 自增ID
     */
    private Long id;
    
    /**
     * 用户pin
     */
    private String pin;
    
    /**
     * 客户简码
     */
    private String clientCode;
    
    /**
     * 语种
     */
    private String lang;
    
    /**
     * 站点类型
     */
    private Integer stationType;
    
    /**
     * 系统编码
     */
    private String systemCode;

    /**
     * 数据隔离标识
     */
    private String operator;

    /**
     * 创建者信息
     */
    private String creator;
} 