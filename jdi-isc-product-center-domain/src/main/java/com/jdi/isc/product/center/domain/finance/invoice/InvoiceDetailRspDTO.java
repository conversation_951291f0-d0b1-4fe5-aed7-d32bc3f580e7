package com.jdi.isc.product.center.domain.finance.invoice;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 获取发票详情响应DTO
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
public class InvoiceDetailRspDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开票申请id
     */
    private String applyId;

    /**
     * 二段运单编号或开票申请id
     */
    private String bizId;

    /**
     * 合同号
     */
    private String contractNum;

    /**
     * 客户号
     */
    private String clientCode;

    /**
     * 下单pin
     */
    private String pin;

    /**
     * 订单个数
     */
    private Integer orderNum;

    /**
     * 订单商品行数
     */
    private Integer orderMkuNum;

    /**
     * 开票来源
     * 1: 运单开票
     * 2: 多单开票
     */
    private Integer sourceCode;

    /**
     * 币种
     */
    private String currency;

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 总价
     */
    private BigDecimal includeTaxSaleTotalPrice;

    /**
     * 总税金
     */
    private BigDecimal totalTaxes;

    /**
     * 未税总价
     */
    private BigDecimal salePriceTotal;

    /**
     * 税率，10%则存储为0.1
     */
    private BigDecimal taxRate;

    /**
     * 审批状态
     * 0: 待审批
     * 1: 已审批
     * -1: 已驳回
     */
    private Integer approvalStatus;

    /**
     * 驳回原因
     */
    private String approvalRejectRemark;

    /**
     * 审批时间
     */
    private Long approvalTime;

    /**
     * 审批扩展信息
     */
    private ApprovalExtInfo approvalExtVO;

    /**
     * 开票订单行明细
     */
    private List<CustomerInvoiceApplyWareInfo> customerInvoiceApplyWareDTOList;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 最后修改时间
     */
    private Long updateTime;

    /**
     * 第三方开票系统编码
     */
    private String thirdSourceCode;

    /**
     * 第三方开票系统id
     */
    private String thirdRequestId;

    /**
     * 开票类型 1:蓝票, 2:红票
     */
    private Integer invoiceType;

    /**
     * 发票单号
     */
    private String invoiceNo;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 发票类型 1,CI形式发票; 2,VAT增值税发票
     */
    private Integer billingType;

    /**
     * pdfUrl
     */
    private String pdfUrl;

    /**
     * 客户开票日期 YYYY-MM-DD 格式
     */
    private String customerInvoiceDay;

    /**
     * 客户名称(后端自用)
     */
    private String clientName;

    /**
     * 审批扩展信息内部类
     */
    @Data
    public static class ApprovalExtInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 开票异常原因
         */
        private String ivcPlatMsg;

        /**
         * 红票标识
         */
        private String redFlag;

        /**
         * 发票链接
         */
        private String invUrl;

        /**
         * 其他发票相关链接
         */
        private List<String> otherUrl;

        /**
         * 发票号
         */
        private String ivcNo;
    }

    /**
     * 开票订单行明细内部类
     */
    @Data
    public static class CustomerInvoiceApplyWareInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 开票id
         */
        private String applyId;

        /**
         * 业务id，二段运单编号、多单开票
         */
        private String bizId;

        /**
         * 订单id
         */
        private String orderId;

        /**
         * mku编码
         */
        private Long mkuId;

        /**
         * sku编码
         */
        private Long skuId;

        /**
         * mku数量
         */
        private Integer mkuNum;

        /**
         * sku数量
         */
        private Integer skuNum;

        /**
         * 币种
         * VND: 越南
         * THB: 泰国
         * CNY: 人民币
         * USD: 美元
         */
        private String currency;

        /**
         * 税率，10%则存储为0.1
         */
        private String wareTaxRate;

        /**
         * 含税销售单价
         */
        private BigDecimal wareIncludeTaxSalePrice;

        /**
         * 税金单价
         */
        private BigDecimal wareTaxes;

        /**
         * 未税销售单价
         */
        private BigDecimal wareSalePrice;

        /**
         * 总价
         */
        private BigDecimal wareIncludeTaxSaleTotalPrice;

        /**
         * 总税金
         */
        private BigDecimal wareTotalTaxes;

        /**
         * 未税总价
         */
        private BigDecimal wareSalePriceTotal;

        /**
         * sapOrderNo
         */
        private String sapOrderNo;

        /**
         * 行项目
         */
        private String lineNum;

        /**
         * 物料编码
         */
        private String materialCode;

        /**
         * 商品名称(当地语言)
         */
        private String skuName;

        /**
         * 销售单位
         */
        private String saleUnit;

        /**
         * ncm
         */
        private String ncm;

        /**
         * cst
         */
        private String cst;

        /**
         * IPI税率
         */
        private String ipiRate;

        /**
         * ICMS税率
         */
        private String icmsRate;

        /**
         * PIS税率
         */
        private String pisRate;

        /**
         * COFINS税率
         */
        private String cofinsRate;

        /**
         * 货源国
         */
        private String sourceCountryCode;
    }
}
