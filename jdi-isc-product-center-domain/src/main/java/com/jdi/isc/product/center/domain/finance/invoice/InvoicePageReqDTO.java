package com.jdi.isc.product.center.domain.finance.invoice;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 获取开票分页信息请求DTO
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
public class InvoicePageReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请号
     */
    private String applyId;

    /**
     * 结算单号
     */
    private String bizId;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 合同号
     */
    private String contractNum;

    /**
     * pin
     */
    private String pin;

    /**
     * 起始页
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码错误")
    private Long index;

    /**
     * 页大小
     */
    @NotNull(message = "页大小不能为空")
    @Range(min = 1, max = 200, message = "分页大小错误")
    private Long size;

    /**
     * 申请号list
     */
    private List<String> applyIds;

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 审批状态 0待审批，1已审批, -1已驳回
     */
    private Integer approvalStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 客户号列表
     */
    private String clientCode;

    /**
     * 订单号列表(多个用英文逗号隔开)
     */
    public List<Long> orderIds;

    /**
     * 父单号列表(多个用英文逗号隔开)
     */
    public List<Long> parentOrderIds;

    /**
     * SAP单号列表
     */
    public List<String> sapOrderNos;

    /**
     * 三方订单号/唯一ID列表
     */
    public List<String> thirdOrderIds;

    /**
     * 运单号
     */
    private String waybillNum;

    /**
     * 客户开票日期开始 YYYY-MM-DD 格式
     */
    private String customerInvoiceDayStart;

    /**
     * 客户开票日期结束 YYYY-MM-DD 格式
     */
    private String customerInvoiceDayEnd;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 发票类型 1,CI形式发票; 2,VAT增值税发票
     */
    private Integer billingType;

    /**
     * 开票来源
     * 1: 运单开票
     * 2: 多单开票
     * (提报方式 1,自助开票; 2,自动开票; 3,API开票; 4,API回传)
     */
    private Integer sourceCode;

    /**
     * 申请日期开始
     */
    private Long applyDateStart;

    /**
     * 申请日期结束
     */
    private Long applyDateEnd;

    /**
     * 发票号(多个用英文逗号隔开)
     */
    private List<String> invoiceNos;

    /**
     * 发票分类 1 为蓝票，0 为红票
     */
    private Integer invoiceType;

}
