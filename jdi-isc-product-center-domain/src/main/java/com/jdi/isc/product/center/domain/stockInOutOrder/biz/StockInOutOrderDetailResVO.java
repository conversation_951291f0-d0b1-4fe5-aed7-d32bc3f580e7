package com.jdi.isc.product.center.domain.stockInOutOrder.biz;


import com.jdi.isc.order.center.api.ofc.biz.rsp.StockInOutOrderConsigneeResDTO;
import com.jdi.isc.order.center.api.ofc.biz.rsp.StockInOutOrderWareResDTO;
import lombok.Data;

import java.util.List;

@Data
public class StockInOutOrderDetailResVO {


    /**
     * 出入库单编号
     */
    private String stockInOutOrderId;

    /**
     * 出入库单业务类型
     */
    private Integer orderBizType;

    /**
     * 出入库单状态
     */
    private Integer orderStatus;

    /**
     * 出入库单类型
     */
    private Integer orderType;

    /**
     * 备货仓编号
     */
    private String stockUpWarehouseId;

    /**
     * 参考单号
     */
    private String referOrderNo;

    /**
     * 第三方来源标识
     */
    private String thirdDataSource;

    /**
     * 出入库wms单号
     */
    private String wmsInOutOrderNo;

    /**
     * 出入库wms时间
     */
    private Long wmsInOutTime;

    /**
     * 客户Id
     */
    private String clientCode;

    /**
     * sku总数量(所有skuNum的加合)
     */
    private Integer planSkuNum;

    /**
     * 实际出入库商品数量
     */
    private Integer actualSkuNum;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 采购单的有效状态
     */
    private Integer validState;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 出入库单的仓库明细信息列表
     */
    List<StockInOutOrderWareResDTO> stockInOutOrderWareResDTO;

    /**
     * 出库单收件人信息
     */
    StockInOutOrderConsigneeResDTO stockInOutOrderConsigneeResDTO;
}
