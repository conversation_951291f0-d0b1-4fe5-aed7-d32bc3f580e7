package com.jdi.isc.product.center.domain.hscode.br;

import lombok.Data;

import java.io.Serializable;

@Data
public class BrHsCodeSaveOrUpdateRequest implements Serializable {
    private String hsCode;
    private String importTax;
    private String industryProductTax;
    private String socialIntegrationTax;
    private String confinsTax;
    private String antiDumpingTax;
    private String icmsFlowTax;
    private Integer controls;
    private String controlsInfo;
    private Long id;
    private String remark;
    private String creator;
    private String updater;
    private Long createTime;
    private Long updateTime;
} 