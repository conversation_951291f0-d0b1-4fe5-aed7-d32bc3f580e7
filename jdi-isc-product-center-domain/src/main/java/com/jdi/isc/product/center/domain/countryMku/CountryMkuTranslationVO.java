package com.jdi.isc.product.center.domain.countryMku;

import com.jdi.isc.product.center.domain.common.biz.BasePageVO;
import com.jdi.isc.product.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description: 国家池翻译确认相关VO
 * @Author: system
 * @Date: 2024/12/20
 **/
@Data
public class CountryMkuTranslationVO implements Serializable {

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Request extends BasePageVO implements Serializable {
        /**
         * MKUID
         */
        private Long mkuId;

        /**
         * SPUID
         */
        private Long spuId;

        /**
         * 目标国、国家站、国家码，ISO 3166-1 两字母代码
         */
        @NotNull(message = "目标国不能为空")
        private String targetCountryCode;

        /**
         * 目标语言
         */
        private String targetLang;

        /**
         * 翻译状态 0:待翻译, 1:已翻译, 2:已确认
         */
        private Integer translationStatus;

        /**
         * 商品名称关键词
         */
        private String titleKeyword;

        /**
         * MKU ID列表
         */
        private List<Long> mkuIds;

        /**
         * SPU ID列表
         */
        private List<Long> spuIds;

        /**
         * 创建时间开始
         */
        private Long createTimeStart;

        /**
         * 创建时间结束
         */
        private Long createTimeEnd;
    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BasicVO implements Serializable {
        /**
         * MKUID
         */
        private Long mkuId;

        /**
         * SPUID
         */
        private Long spuId;

        /**
         * MKU中文标题
         */
        private String mkuTitleZh;

        /**
         * SPU中文标题
         */
        private String spuTitleZh;

        /**
         * MKU目标语言标题
         */
        private String mkuTitleTarget;

        /**
         * SPU目标语言标题
         */
        private String spuTitleTarget;

        /**
         * 目标国、国家站、国家码，ISO 3166-1 两字母代码
         */
        private String targetCountryCode;

        /**
         * 目标国名称
         */
        private String targetCountryName;

        /**
         * 目标语言
         */
        private String targetLang;

        /**
         * 翻译状态 0:待翻译, 1:已翻译, 2:已确认
         */
        private Integer translationStatus;

        /**
         * 翻译状态名称
         */
        private String translationStatusName;

        /**
         * 品牌ID
         */
        private Long brandId;

        /**
         * 品牌名称
         */
        private String brandName;

        /**
         * 类目ID
         */
        private Long lastCatId;

        /**
         * 类目名称
         */
        private String catName;
    }

    @Data
    @NoArgsConstructor
    public static class ImportRequest implements Serializable {
        /**
         * MKUID
         */
        @NotNull(message = "MKU ID cannot be empty")
        private Long mkuId;

        /**
         * 目标国、国家站、国家码，ISO 3166-1 两字母代码
         */
        @NotBlank(message = "countryCode cannot be empty")
        private String countryCode;

        /**
         * 语言名称映射
         */
        @NotNull(message = "langNameMap cannot be empty")
        Map<String, String> langNameMap;
    }

    @Data
    @NoArgsConstructor
    public static class ExportRequest implements Serializable {
        /**
         * 查询条件
         */
        private Request queryCondition;

        /**
         * 导出类型 1:导出查询结果, 2:导出模板
         */
        private Integer exportType;

        /**
         * 操作人
         */
        private String operator;
    }
}
