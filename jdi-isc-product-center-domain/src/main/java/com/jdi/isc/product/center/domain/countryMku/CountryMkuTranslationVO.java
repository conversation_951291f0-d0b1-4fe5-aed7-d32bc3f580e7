package com.jdi.isc.product.center.domain.countryMku;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

/**
 * @Description: 国家池翻译确认相关VO
 * @Author: system
 * @Date: 2024/12/20
 **/
@Data
@NoArgsConstructor
public class CountryMkuTranslationVO implements Serializable {
    /**
     * MKUID
     */
    @NotNull(message = "MKU ID cannot be empty")
    private Long mkuId;

    /**
     * 目标国、国家站、国家码，ISO 3166-1 两字母代码
     */
    @NotBlank(message = "countryCode cannot be empty")
    private String countryCode;

    /**
     * 语言名称映射
     */
    @NotNull(message = "langNameMap cannot be empty")
    Map<String, String> langNameMap;

}
