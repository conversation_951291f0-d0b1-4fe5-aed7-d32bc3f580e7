package com.jdi.isc.product.center.domain.stockInOutOrder.biz;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * skuId 对应下单数量
 */
@Data
public class StockInOutOrderSkuIdNumVO {
    /**
     * skuId 国际
     */
    @NotNull(message = "skuId不能为空")
    private Long skuId;

    /**
     * mku编码
     */
    private Long mkuId;

    /**
     * 商品数量
     */
    @NotNull(message = "商品数量不能为空")
    @Range(min = 1, max = 99999999, message = "商品数量购买范围0-99999999")
    private Integer skuNum;
}
