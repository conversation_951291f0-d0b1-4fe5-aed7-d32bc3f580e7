package com.jdi.isc.product.center.domain.supplierSettlement;

import com.jdi.isc.order.center.api.purchaseOrder.biz.resp.PurchaseOrderWareApiDTO;
import com.jdi.isc.product.center.domain.common.biz.OperateButtonVO;
import com.jdi.isc.product.center.domain.purchaseOrder.biz.PurchaseOrderWareVO;
import com.jdi.isc.product.center.domain.settlement.biz.PurchaseSettlementSkuRes;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PurchaseSettlementRes {

    /**
     * 采购订单ID
     */
    private String purchaseOrderId;

    /**
     * 发票审批状态
     */
    private Integer invoiceApprovalStatus;

    /**
     * 发票上传状态
     */
    private Integer invoiceUploadStatus;

    /**
     * 发货时间
     */
    private Long shippedTime;

    /**
     * 入仓时间
     */
    private Long enterWarehouseTime;

    /**
     * 完成时间
     */
    private Long completeTime;

    /**
     * 采购单状态
     */
    private Long purchaseOrderStatus;



    /**
     * 供应商确认状态
     */
    private Integer supplierConfirmStatus;

    /**
     * 支付状态
     */
    private Integer payStatus;


    /**
     * 操作状态
     */
    private Integer opStatus;


    /**
     * 支付结束时间
     */
    private Long payTime;

    /**
     * 供应商简码
     */
    private String supplierCode;

    /**
     * 原始父采购单号
     */
    private String parentPurchaseOrderId;

    /**
     * 多个结算单号拼接
     */
    private String requestIds;

    /**
     * 结算单号拼接
     */
    private String requestId;

    private String accountCurrency;

    private BigDecimal purchaseTotalPrice;

    private BigDecimal waresPurchaseTotalPrice;

    private List<PurchaseSettlementSkuRes> purchaseOrderWareVOS;

    /**
     * 操作列表
     */
    private List<OperateButtonVO> operateVOList;
}
