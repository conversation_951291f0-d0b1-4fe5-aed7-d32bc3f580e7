package com.jdi.isc.product.center.domain.supplier;

import com.jdi.isc.product.center.domain.common.biz.FieldDiffVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SupplierModifyRecordDTO {
    private List<FieldDiffVO> diffField;
    private Long id;
    private Long updateTime;
    private String updater;

}
