package com.jdi.isc.product.center.domain.mku.biz;


import com.jdi.isc.product.center.domain.common.biz.BasicVO;
import lombok.Data;

/**
 * <AUTHOR>
 * @description：MkuChangeBindingRecordVO
 * @Date 2025-08-27
 */
@Data
public class MkuChangeBindingRecordVO extends BasicVO {
    /** 国际MKU ID */
    private Long mkuId;

    /** 换绑前SKU ID */
    private Long originSkuId;

    /** 换绑后SKU ID */
    private Long afterSkuId;

    /** 申请人ERP */
    private String applicant;

    /** 换绑时间（时间戳） */
    private Long changeBindingTime;

    /** 换绑原因 */
    private String applyReason;
}
