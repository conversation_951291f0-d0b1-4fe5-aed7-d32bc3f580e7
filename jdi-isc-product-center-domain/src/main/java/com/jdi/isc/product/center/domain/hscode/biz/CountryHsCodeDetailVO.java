package com.jdi.isc.product.center.domain.hscode.biz;

import com.jdi.isc.product.soa.api.common.BasicDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @Description: 6个国家的HsCode信息
 * 支持国家：BR(巴西)、HU(匈牙利)、ID(印度尼西亚)、MY(马来西亚)、TH(泰国)、VN(越南)
 * @Author: zhaojianguo21
 * @Date: 2024/02/20 17:59
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CountryHsCodeDetailVO extends BasicDTO {

    /** 国家码 */
    @NotNull(message = "国家不能为空")
    private String countryCode;

    /**
     * HS编码信息
     */
    private String hsCode;
}