package com.jdi.isc.product.center.domain.finance.invoice;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 获取自助提报-订单分页信息请求DTO
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
public class InvoiceOrderPageReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 国家/地区编码
     */
    private String countryCode;

    /**
     * 客户号
     */
    private String clientCode;

    /**
     * 合同号
     */
    private String contractNum;

    /**
     * 下单pin
     */
    private String pin;

    /**
     * 订单号
     */
    private List<Long> orderIds;

    /**
     * 父订单号
     */
    private List<Long> parentOrderIds;

    /**
     * SAP单号
     */
    private List<String> sapOrderNos;

    /**
     * 三方订单号/唯一ID
     */
    private List<String> thirdOrderIds;

    /**
     * 运单号
     */
    private List<String> waybillNums;

    /**
     * 下单日期开始时间(订单表用的datetime类型)
     */
    private Long orderCreateTimeStart;

    /**
     * 下单日期结束时间(订单表用的datetime类型)
     */
    private Long orderCreateTimeEnd;

    /**
     * 妥投日期开始时间
     */
    private Long deliveryCustomerDateStart;

    /**
     * 妥投日期结束时间
     */
    private Long deliveryCustomerDateEnd;

    /**
     * 订单状态  (注意: 消息只接33及以后状态的数据)
     * 11:运营已确认,
     * 3:客户审批完成,
     * 303:已提交跨境采购单,
     * 302:已取消跨境采购单,
     * 304:创建跨境采购单失败,
     * 33:订单已确认 （供应商可以看到采购单）,
     * 80:已发货,
     * 90:已收货,
     * 99:已完成,
     * 2:已取消
     */
    private Integer orderStatus;

    /**
     * 发票状态
     * 1,已开票;
     * 2,未开票;
     * 3,发票冲红;
     * 4,已提交(注意: 这里是发票状态在订单上的映射值)
     */
    private Integer invoiceStatus;

    /**
     * 起始页
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码错误")
    private Long index;

    /**
     * 页大小
     */
    @NotNull(message = "页大小不能为空")
    @Range(min = 1, max = 200, message = "分页大小错误")
    private Long size;

    /**
     * 操作人
     */
    private String operator;
}
