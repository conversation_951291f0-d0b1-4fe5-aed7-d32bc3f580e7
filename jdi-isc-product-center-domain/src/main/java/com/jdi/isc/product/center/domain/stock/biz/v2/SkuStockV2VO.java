package com.jdi.isc.product.center.domain.stock.biz.v2;

import com.jdi.isc.product.center.domain.common.biz.BaseVO;
import com.jdi.isc.product.center.domain.validation.ValidatorGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;


/**
 * SKU库存信息视图对象
 * 包含仓库ID、SKU ID、库存数量等信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class SkuStockV2VO extends BaseVO {

    /** 仓库ID */
    private Long warehouseId;

    /** 商品SPU的唯一标识符 */
    private Long spuId;

    /** SKU ID */
    private Long skuId;

    /** 库存数量 */
    private Long stock;

    /** 供应商简码 */
    private String vendorCode;

    /** SKU名称 */
    private String skuName;

    /** 采销员ERP编号 */
    private String buyer;

    /** 可用库存 */
    private Long availableStock;

    /** 预占库存 */
    private Long occupy;

    /** 仓库名称 */
    private String warehouseName;

    /** 仓库编号 */
    private String warehouseNo;

    /** 供应商名称 */
    private String vendorName;

    /** 备货模式：0 - 非备货，1 - 自营，2 - 寄售 */
    private Integer purchaseModel;

    /** 安全库存阈值 */
    private Long stockThreshold;

    /** 在途库存 */
    private Long onWayStock;

    /** 在途销售状态：1 - 在途销售中，0 - 不在途销售中 */
    private Integer onWaySale;
}

