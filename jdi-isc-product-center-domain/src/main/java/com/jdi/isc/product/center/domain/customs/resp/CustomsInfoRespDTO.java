package com.jdi.isc.product.center.domain.customs.resp;

import com.jdi.isc.product.center.api.common.BasicDTO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 跨境商品JDSKUID与HSCODE对应关系数据层类
 * <AUTHOR>
 * @date 2024/07/18 10:25
 */
@Data
public class CustomsInfoRespDTO extends BasicDTO {
    private Long skuId;
    private Long jdSkuId;
    private String countryCode;
    private String hsCode;
    private String skuName;
    private String skuLocalName;
    private String skuImage;
    private String weight;
    private String length;
    private String wide;
    private String high;
    private String purchaseSalesErp;
    private Integer orderExists;
    private Integer weightDataSource;
    private Long expectCompletionTime;
    private Integer status;
    private String categoryName;
    private String brandName;
    private Integer productStatus;
    private Integer customsComplianceStatus;
    private Integer compulsoryCertificationStatus;
    private String declarationName;
    private String declarationLocalName;
    private String declarationElement;
    private Integer controls;
    private String controlsInfo;
    private Integer compulsoryCertification;
    private String compulsoryCertificationInfo;
    private Integer brandLicensing;
    private String customsSupplierId;
    private String customsSupplierName;
    private String dataSource;
    private BigDecimal mfnTax;
    private BigDecimal originMfnTax;
    private BigDecimal consumptionTax;
    private BigDecimal environmentalTaxUnitPrice;
    private BigDecimal antiDumpingTax;
    private BigDecimal valueAddedTax;
    private BigDecimal formeTax;
    private BigDecimal localTax;
    private BigDecimal saleTax;
    private BigDecimal importTax;
    private BigDecimal industryProductTax;
    private BigDecimal socialIntegrationTax;
    private BigDecimal confinsTax;
    private BigDecimal icmsFlowTax;
    private BigDecimal antiSubsidyTax;
    private BigDecimal normalImportTax;
    private BigDecimal luxuryTax;
    private BigDecimal withholdingTax;
    private BigDecimal tradeProtectionTax;
    private BigDecimal exportTaxRate;
    private BigDecimal exportRebateRate;
} 