package com.jdi.isc.product.center.domain.finance.invoice;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 保存发票信息响应DTO
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
public class SaveInvoiceProRspDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 响应码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 保存结果，true表示保存成功，false表示保存失败
     */
    private Boolean data;

    /**
     * 总数
     */
    private Long total;

    /**
     * 错误参数列表
     */
    private List<String> errorParams;
}
