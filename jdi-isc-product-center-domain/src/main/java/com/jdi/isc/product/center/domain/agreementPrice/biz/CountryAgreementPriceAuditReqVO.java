package com.jdi.isc.product.center.domain.agreementPrice.biz;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;


/**
 * 客户MKU关系实体
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CountryAgreementPriceAuditReqVO extends CountryAgreementPricePageReqVO {

    /** 申请单号 */
    private String applyCode;

    /** 利润率 */
    private BigDecimal profitRateBegin;

    /** 利润率 */
    private BigDecimal profitRateEnd;

    /** 发起人erp */
    private String updater;

    private String currentAuditor;

    private List<String> ids;

    /**
     * 申请人
     */
    private String applyUserErp;
}
