package com.jdi.isc.product.center.domain.customs.resp;

import com.jdi.common.domain.rpc.bean.PageInfo;
import lombok.Data;

import java.util.List;

/**
 * 关务列表分页响应DTO
 * <AUTHOR>
 * @date 2024/07/18 10:25
 */
@Data
public class CustomsPageRespDTO {
    /**
     * 记录列表
     */
    private List<CustomsInfoRespDTO> records;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 页大小
     */
    private Long size;
    
    /**
     * 页码
     */
    private Long index;
    
    /**
     * 游标
     */
    private List<Object> cursor;
    
    /**
     * 转换为PageInfo对象
     * @return PageInfo对象
     */
    public PageInfo<CustomsInfoRespDTO> toPageInfo() {
        PageInfo<CustomsInfoRespDTO> pageInfo = new PageInfo<>();
        pageInfo.setRecords(this.records);
        pageInfo.setTotal(this.total);
        pageInfo.setSize(this.size);
        pageInfo.setIndex(this.index);
        return pageInfo;
    }
} 