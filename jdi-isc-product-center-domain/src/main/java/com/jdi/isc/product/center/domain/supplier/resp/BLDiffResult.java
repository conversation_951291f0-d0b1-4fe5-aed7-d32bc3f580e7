package com.jdi.isc.product.center.domain.supplier.resp;

import com.jdi.isc.product.center.domain.supplier.biz.BusinessLineVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
/**
    * @description: 产品线去重逻辑实体类
    * @author: zhangjin176
    * @date: 2025/5/8 00:12
    * @version:
    **/
@Data
public class BLDiffResult {
    /**
     * 需要添加的业务行编辑DTO列表。
     */
    private List<BusinessLineVO> toAdd = new ArrayList<>();
    /**
     * 包含需要更新的业务行编辑DTO列表。
     */
    private List<BusinessLineVO> toUpdate = new ArrayList<>();
    /**
     * 不需要进行任何更改的业务行编辑DTO列表。
     */
    private List<BusinessLineVO> noChange = new ArrayList<>();
    /**
     * 需要删除的业务行编辑DTO列表。
     */
    private List<BusinessLineVO> toDelete = new ArrayList<>();
    /**
     * 存储无效的业务行编辑DTO列表。
     */
    private List<BusinessLineVO> toInvalid = new ArrayList<>();
}