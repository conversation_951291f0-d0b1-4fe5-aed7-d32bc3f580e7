package com.jdi.isc.product.center.domain.customs.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * JSF接口通用响应DTO
 * <AUTHOR>
 * @date 2024/07/18 10:25
 */
@Data
public class ProductCustomsRespApiDTO<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 错误参数
     */
    private List<String> errorParams;
} 