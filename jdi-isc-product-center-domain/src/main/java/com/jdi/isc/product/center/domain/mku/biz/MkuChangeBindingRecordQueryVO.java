package com.jdi.isc.product.center.domain.mku.biz;


import com.jdi.isc.product.center.domain.common.biz.BasePageVO;
import lombok.Data;

import java.util.Set;

/**
 * MKU换绑记录分压查询对象
 * <AUTHOR>
 * @description：MkuChangeBindingRecordQueryVO
 * @Date 2025-08-27
 */
@Data
public class MkuChangeBindingRecordQueryVO extends BasePageVO {
    /** 国际MKU ID */
    private Set<Long> mkuId;

    /** 换绑前SKU ID */
    private Set<Long> originSkuId;

    /** 换绑后SKU ID */
    private Set<Long> afterSkuId;

    /** 申请人ERP */
    private String applicant;

    /** 换绑时间开始（时间戳） */
    private Long changeBindingTimeStart;

    /** 换绑时间结束（时间戳） */
    private Long changeBindingTimeEnd;

}
