package com.jdi.isc.product.center.domain.hscode.biz;

import com.jdi.isc.product.center.domain.common.biz.BasePageVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 6个国家的HsCode分页查询响应
 * 支持国家：BR(巴西)、HU(匈牙利)、ID(印度尼西亚)、MY(马来西亚)、TH(泰国)、VN(越南)
 * @Author: zhaojianguo21
 * @Date: 2024/02/21 09:21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CountryHsCodePageResponse extends BasePageVO implements Serializable {

    /** 国家码 */
    private String countryCode;

    /** 海关编码 */
    private String hsCode;

    /** 是否管制 ✅ */
    private Integer controls;

    /** 管制信息 ✅ */
    private String controlsInfo;

    /** 目的国/地区增值税 越南 ✅ */

    private BigDecimal valueAddedTax;

    /** 目的国/地区消费税 ✅ */
    private BigDecimal consumptionTax;

    /** 预扣款税 */
    private BigDecimal withholdingTax;

    /** 贸易保护关税 */
    private BigDecimal tradeProtectionTax;

    /** 原产地优惠关税率FormE (马来西亚、印尼、越南) ✅*/
    private BigDecimal originMfnTax;
    /** 奢侈品税 (印尼) */
    private BigDecimal luxuryTax;
    /** local tax 本地税 */
    private BigDecimal localTax;
    /** 原产地优惠关税 */
    private BigDecimal formeTax;
    /** 最惠国关税 (越南) ✅*/
    private BigDecimal mfnTax;
    /** 反倾销税 越南、泰国、匈牙利 ✅*/
    private BigDecimal antiDumpingTax;
    /** 环保税单价 越南 ✅*/
    private BigDecimal environmentalTaxUnitPrice;
    /** 普通关税率 越南*/
    private BigDecimal normalImportTax;
    private Long id;
    /** 备注 */
    private String remark;

    /** 修改人 */
    private String updater;

    /** 进口关税率II (匈牙利、巴西) ✅ */
    private String importTax;

    /** 工业产品税率IPI (巴西) ✅*/
    private BigDecimal industryProductTax;

    /** 社会一体化费率PIS (巴西) ✅*/
    private BigDecimal socialIntegrationTax;

    /** CONFINS率 (巴西) ✅*/
    private BigDecimal confinsTax;

    /** ICMS流转税率 (巴西) ✅ */
    private BigDecimal icmsFlowTax;


    /** 反补贴税率 (匈牙利) ✅*/
    private BigDecimal antiSubsidyTax;

    /** 销售税率 (马来西亚) */
    private BigDecimal saleTax;

    // ==== 其他国家字段 ====
    /** 进口关税率 (泰国/越南/印度尼西亚) */
    private String tariff;

    /** 增值税率 (泰国/越南/印度尼西亚) */
    private String valueAdded;

    /** 消费税率 (泰国/越南/印度尼西亚) */
    private String consumption;

    /** FormE关税率 (泰国/越南/印度尼西亚) */
    private String formeTariff;

    /** 非FormE关税率 (泰国/越南/印度尼西亚) */
    private String noFormeTariff;

    /** 是否需要tisi (泰国/越南/印度尼西亚) */
    private Integer tisi;
    /**
     * 更新时间戳
     */
    private Long updateTime;

    /**
     * 出口税率(中国)
     */
    private BigDecimal exportTaxRate;
    /**
     * 出口退税率(中国)
     */
    private BigDecimal exportRebateRate;
}