package com.jdi.isc.product.center.domain.finance.invoice;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 获取发票详情请求DTO
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
public class InvoiceDetailReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请号
     */
    private String applyId;

    /**
     * URL地址
     */
    private String url;


}
