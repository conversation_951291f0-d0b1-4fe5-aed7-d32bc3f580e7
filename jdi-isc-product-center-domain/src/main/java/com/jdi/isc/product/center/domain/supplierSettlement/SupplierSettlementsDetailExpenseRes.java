package com.jdi.isc.product.center.domain.supplierSettlement;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SupplierSettlementsDetailExpenseRes {
    private String purchaseOrderId;
    private Long skuId;
    private Integer skuNum;
    private String requestId;
    private String supplierId;
    private BigDecimal amount;
    private String accountCurrency;
    private Integer expenseType;
    private String bizCode;
    private String expenseName;
    private String type;
    private String description;

}