package com.jdi.isc.product.center.domain.brand.biz.business;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * @description: 专供-产品线
    * @author: zhangjin176
    * @date: 2025/5/10 01:49
    * @version: 
    **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandBusinessLineVO  {
    /**
     * 品牌ID，关联到具体的品牌实体。
     */
    private Long brandId;
    /**
     * 品牌视图对象的语言名称，最大长度为100个字符。
     */
    private String langName;

}
