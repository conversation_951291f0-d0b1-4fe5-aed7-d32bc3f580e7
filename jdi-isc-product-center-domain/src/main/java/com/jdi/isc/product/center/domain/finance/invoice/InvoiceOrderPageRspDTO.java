package com.jdi.isc.product.center.domain.finance.invoice;

import lombok.Data;

import java.io.Serializable;

/**
 * 获取自助提报-订单分页信息响应DTO
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
public class InvoiceOrderPageRspDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目id(处理权限用字段)
     */
    private String projectId;

    /**
     * 税率，10%则存储为0.1
     */
    private String exchangeRate;

    /**
     * 发票id
     */
    private String applyId;

    /**
     * 国家/地区编码
     */
    private String countryCode;

    /**
     * 客户号
     */
    private String clientCode;

    /**
     * 合同号
     */
    private String contractNum;

    /**
     * 下单pin
     */
    private String pin;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 父订单号
     */
    private Long parentOrderId;

    /**
     * 父订单号
     */
    private Long splitOrderId;

    /**
     * SAP单号
     */
    private String sapOrderNo;

    /**
     * 三方订单号/唯一ID
     */
    private String thirdOrderId;

    /**
     * 运单号
     */
    private String waybillNum;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 商品销售总价(未税价)
     */
    private String waresSaleTotalPrice;

    /**
     * 订单总价(含税价)
     */
    private String orderTotalPrice;

    /**
     * 订单总税金(税)
     */
    private String orderTaxes;

    /**
     * 下单日期(订单表用的datetime类型)
     */
    private Long orderCreateTime;

    /**
     * 妥投日期
     */
    private Long deliveryCustomerDate;

    /**
     * 订单状态 11:运营已确认, 3:客户审批完成, 303:已提交跨境采购单, 302:已取消跨境采购单, 304:创建跨境采购单失败, 33:订单已确认 （供应商可以看到采购单）, 80:已发货, 90:已收货, 99:已完成, 2:已取消(注意: 消息只接33及以后状态的数据)
     */
    private Integer orderStatus;

    /**
     * 发票状态 1,已开票; 2,未开票; 3,发票冲红; 4,已提交(注意: 这里是发票状态在订单上的映射值)
     */
    private Integer invoiceStatus;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 拆单状态 1有效，0无效
     */
    private Integer validState;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 最后修改时间
     */
    private Long updateTime;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 发票类型 1,CI形式发票; 2,VAT增值税发票
     */
    private Integer billingType;

    /**
     * 客户开票日期 YYYY-MM-DD 格式，客户填什么就是什么值，不考虑时区
     */
    private String customerInvoiceDay;

    /**
     * 发票分类 1 为蓝票，0 为红票
     */
    private Integer invoiceType;

    /**
     * 发票单号
     */
    private String invoiceNo;

    /**
     * 客户名称(后端自用)
     */
    private String clientName;

    /**
     * 发票id(在途)
     */
    private String curApplyId;

    /**
     * 是否可开票(蓝票) - code
     * 0 可开票
     * 1 不可开票: 已开蓝票
     * 2 不可开票: 有在途的蓝票
     * 3 不可开票: 有在途的红票
     * 4 不可开票: 当前订单状态不允许开票
     */
    private Integer extStatus;

    /**
     * 是否可开票(蓝票) - desc
     * 0 可开票
     * 1 不可开票: 已开蓝票
     * 2 不可开票: 有在途的蓝票
     * 3 不可开票: 有在途的红票
     * 4 不可开票: 当前订单状态不允许开票
     */
    private String extStatusDesc;
}
