package com.jdi.isc.product.center.domain.enums;

/**
 * 通用数据有效性枚举
 * <AUTHOR>
 * @date 20230803
 **/
public enum BusinessLineOperateEnum {

    /**
     * 新增操作
     */
    ADD("add", "新增"),
    /**
     * 编辑操作
     */
    EDIT("edit", "编辑"),
    /**
     * 修改操作
     */
    UPDATE("update", "修改"),
    /**
     * 删除操作
     */
    DEL("del", "删除");

    private final String code;
    private final String desc;

    BusinessLineOperateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

