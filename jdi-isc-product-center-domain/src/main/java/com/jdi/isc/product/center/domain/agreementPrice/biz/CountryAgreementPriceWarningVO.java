package com.jdi.isc.product.center.domain.agreementPrice.biz;

import com.jdi.isc.product.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 国家协议价预警表 VO实体类
 * @Author: zhaokun51
 * @Date: 2025/03/18 10:59
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class CountryAgreementPriceWarningVO extends BasicVO {

    /**
     * 业务编号
     */
    @NotNull(message = "业务编号不能为空")
    private String bizNo;

    /**
     * MKUID
     */
    private Long mkuId;

    /**
     * SKUID
     */
    private Long skuId;

    /**
     * SKU名称
     */
    private String skuName;

    /**
     * 货源国、国家站、国家码，ISO 3166-1 两字母代码
     */
    @NotNull(message = "货源国、国家站、国家码，ISO 3166-1 两字母代码不能为空")
    private String sourceCountryCode;

    /**
     * 目标国、国家站、国家码，ISO 3166-1 两字母代码
     */
    @NotNull(message = "目标国、国家站、国家码，ISO 3166-1 两字母代码不能为空")
    private String targetCountryCode;

    /**
     * 末级类目id
     */
    @NotNull(message = "末级类目id不能为空")
    private Long lastCatId;

    /**
     * 品牌
     */
    private Long brandId;

    /**
     * 币种
     */
    private String currency;

    /**
     * 协议价
     */
    private BigDecimal agreementPrice;

    /**
     * 国家协议价计算流程
     */
    private String agreementMark;

    /**
     * 国家成本价
     */
    private BigDecimal countryCostPrice;

    /**
     * 国家成本价计算流程
     */
    private String costMark;

    /**
     * 利润率
     */
    private BigDecimal profitRate;

    /**
     * 利润率阈值低
     */
    private BigDecimal profitRateLimit;

    /**
     * 利润率阈值高
     */
    private BigDecimal profitRateLowLimit;

    /**
     * 预警状态
     */
    private Integer warningStatus;

    /**
     * 预警状态变更来源
     */
    private Integer dataStatusSource;

    /**
     * 绑定状态
     */
    private String bindStatus;

    /**
     * 绑定状态名称
     */
    private String bindStatusName;


//    /** 货源国 */
//    private String sourceCountryCode;

    /**
     * 价格可售状态
     */
    private Integer availableSaleStatus;

    /**
     * 价格可售状态更新时间
     */
    private Long availableSaleStatusTime;

//    /**
//     * 预警状态变更来源(预警来源) 1：国家协议价 2国家成本价
//     */
//    private Integer dataStatusSource;

    /**
     * 品牌id
     */
    private Long jdCatId;

    /**
     * 类目名称(反射使用属性名不能改)
     */
    private String catName;

//    /**
//     * 品牌id
//     */
//    private Long brandId;

    /**
     * 不可售利润率阈值
     */
    private BigDecimal unsellableThreshold;

    /**
     * 不可售利润率阈值更新时间
     */
    private Long unsellableThresholdTime;

    /**
     * 采销erp
     */
    private String buyerErp;

    /**
     * 国家池入池状态，1-已入池，3-未入池
     *
     * {@link com.jdi.isc.product.soa.domain.enums.countryMku.CountryMkuPoolStatusEnum}
     */
    private Integer countryMkuPoolStatus;

    /**
     * 是否入客户池 1-绑定，0-未绑定
     *
     * {@link com.jdi.isc.product.soa.api.common.CustomerMkuBindEnum}
     */
    private String customerMkuPoolStatus;

    /**
     * 审批状态
     * {@link com.jdi.isc.product.soa.api.approveorder.common.AuditStatusEnum}
     */
    private Integer auditStatus;

    /**
     * 是否测试品
     */
    public Integer testProduct;

    /**
     * {@link com.jdi.isc.product.soa.api.approveorder.common.ApproveOrderStatusEnum}
     */
    public Integer approveStatus;

    /**
     * 审核表ID
     */
    public Long approveId;

    /**
     * 审批单号
     */
    private String applyCode;

    /**
     * 是否展示提交审核按钮
     */
    public boolean showAuditSubmitButton;

    /**
     * 是否展示提交审核按钮
     */
    public boolean showAuditRevokeButton;

    /**
     * 名牌名称
     */
    private String brandName;

}
