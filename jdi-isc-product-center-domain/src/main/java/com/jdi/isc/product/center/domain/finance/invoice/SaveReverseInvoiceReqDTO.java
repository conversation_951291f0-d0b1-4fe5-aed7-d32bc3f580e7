package com.jdi.isc.product.center.domain.finance.invoice;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 保存发票冲红请求DTO
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
public class SaveReverseInvoiceReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请单号列表
     */
    @NotNull(message = "申请单号列表不能为空")
    private List<String> applyNoList;

    /**
     * 申请原因
     */
    @NotNull(message = "申请原因不能为空")
    private String instructionNo;

    /**
     * 核销说明
     */
    @NotNull(message = "核销说明不能为空")
    private String writtenOffDes;

    /**
     * 国家码
     */
    @NotNull(message = "国家码不能为空")
    private String countryCode;

}
