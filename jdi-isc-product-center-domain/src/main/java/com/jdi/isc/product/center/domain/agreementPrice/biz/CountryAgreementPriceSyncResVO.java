package com.jdi.isc.product.center.domain.agreementPrice.biz;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
@Data
@NoArgsConstructor
public class CountryAgreementPriceSyncResVO {
    /**
     * 成功同步的国家协议价格ID列表。
     */
    private List<Long> successIdList;

    /**
     * 存储同步失败的国家协议价格ID和对应的错误信息。
     */
    private Map<Long,String> failSkuIdMap;
}
