package com.jdi.isc.product.center.domain.finance.invoice;

import com.jdi.isc.order.center.api.finance.customerInvoice.res.CustomerBuyerInfo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 获取预开票明细信息响应DTO
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
public class PreInvoiceDetailRspDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 来源系统
     */
    private String srcSystem;

    /**
     * 来源系统单号
     */
    private String applyNo;

    /**
     * 申请人ERP
     */
    private String applyUser;

    /**
     * 发票类型
     */
    private Integer invType;

    /**
     * 数据类型
     */
    private Integer dataType;

    /**
     * 销方主体（京东主体名称）
     */
    private String sellerName;

    /**
     * 销方公司性质
     */
    private String receiverCompanyProperty;

    /**
     * 销方公司性质
     */
    private String receiverCompanyNITKU;

    /**
     * 开票币种
     */
    private String currencyCode;

    /**
     * IBAN
     */
    private String iban;

    /**
     * 购方(客户)信息列表，包含多个购方的详细信息
     */
    private List<CustomerBuyerInfo> customerBuyerInfoList;

    /**
     * 购方（客户）名称（发票抬头）
     */
    private String buyerName;

    /**
     * 购方（客户）税号
     */
    private String buyerTaxNo;

    /**
     * 购方（客户）地址
     */
    private String buyerAddress;

    /**
     * 购方（客户）国家简码
     * 国家/地区（切换开票设置演示，实际不需要显示该字段）
     * 泰国 (TH)
     */
    private String buyerCountryCode;

    /**
     * 订单数量
     */
    private Integer orderNum;

    /**
     * 提报金额
     */
    private String reportedAmount;

    /**
     * 客户公司性质
     */
    private String customerCompanyProperty;

    /**
     * 客户公司性质
     */
    private String customerCompanyNITKU;

    /**
     * 结算单号
     */
    private String reqNumber;

    /**
     * EBS合同编号
     */
    private String ebsContractNo;

    /**
     * 服务日期从
     */
    private String supplyDateBegin;

    /**
     * 服务日期至
     */
    private String supplyDateEnd;

    /**
     * 付款到期日 YYYY-MM-DD
     */
    private String dueDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开票日期
     */
    private String dateOfInvoice;

    /**
     * 是否有原发票(巴西特有增量字段)  0：否, 1：是
     * 是：红冲发票时，对应原先的蓝票；退货时会用到。
     */
    private Integer originalInvFlag;

    /**
     * 巴西发票类型(巴西特有增量字段) 0：进项 1：出项（销项）
     */
    private Integer ioFlag;

    /**
     * 是否为最终消费方(巴西特有增量字段) 0：否;1：是
     */
    private Integer finalConsumerFlag;

    /**
     * 运费支付方(巴西特有增量字段) 0：开票方, 1：收票方
     */
    private Integer freightPayerFlag;

    /**
     * 购方（客户）税务邮箱
     */
    private String email;

    /**
     * 妥投日期，表示货物或服务实际送达的日期
     */
    private String deliverDate;

    /**
     * 京东主体的匈牙利税号
     * 根据规则给出
     */
    private String sellerTaxNo;

    /**
     * 京东主体的匈牙利地址
     * 根据规则给出
     */
    private String sellerAddress;

    /**
     * 送货地址是否相同
     * 巴西特殊值
     */
    private Integer deliveryAddressFlag;

    /**
     * 电子发票类型(马来)
     */
    private Integer eInvoiceType;

    /**
     * 购买方-公司注册号(马来)
     */
    private String buyerOtherId;

    /**
     * 购买方-公司注册号类型(马来)
     */
    private String buyerOtherIdScheme;

    /**
     * 购买方-税务号码(马来)
     */
    private String buyerTaxIdentificationNumber;

    /**
     * 购买方-税务类型SST(马来)
     */
    private String buyerTaxNoScheme;

    /**
     * 购买方-标准产业分类代码(马来)
     */
    private String buyerStandardIndustrialClassificationCode;

    /**
     * 购买方-街道(马来)
     */
    private String buyerStreet;

    /**
     * 购买方-区/地区(马来)
     */
    private String buyerDistrict;

    /**
     * 购买方-邮编(马来)
     */
    private String buyerProvinceCode;

    /**
     * 购买方-邮编(马来)
     */
    private String buyerPostalCode;

    /**
     * 购买方-城市(马来)
     */
    private String buyerCity;

    /**
     * 购买方-省（州）(马来)
     */
    private String buyerProvince;

    /**
     * 购买方-邮箱(马来)
     */
    private String buyerEmail;

    /**
     * 购买方-电话(马来)
     */
    private String buyerPhone;

    /**
     * 配置项
     */
    private Map<String,Boolean> configItem;



    /**
     * 客户购方信息内部类
     */
    @Data
    public static class CustomerBuyerInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 购方（客户）名称（发票抬头） 下拉项
         */
        private String buyerName;

        /**
         * 购方（客户）税号
         */
        private String buyerTaxNo;

        /**
         * 购方（客户）地址
         */
        private String buyerAddress;
    }
}
