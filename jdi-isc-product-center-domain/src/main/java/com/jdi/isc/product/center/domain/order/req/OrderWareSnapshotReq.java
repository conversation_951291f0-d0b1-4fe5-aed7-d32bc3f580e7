package com.jdi.isc.product.center.domain.order.req;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 订单快照请求体
 *
 * @author: zhangjin176
 * @date: 2025/5/26 11:33
 * @version:
 **/
@Data
public class OrderWareSnapshotReq {
    /**
     * 订单ID，用于关联订单信息。
     */
    @NotNull(message = "订单ID不能为空")
    private Long orderId;
    /**
     * 商品ID，用于关联商品信息。
     */
    @NotNull(message = "商品ID不能为空")
    private Long mkuId;
}