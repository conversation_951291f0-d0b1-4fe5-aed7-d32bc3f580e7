package com.jdi.isc.product.center.domain.orderThirdArchive.res;

import com.jdi.isc.product.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 订单三方归档 实体类
 * @Author: zhaojianguo21
 * @Date: 2025/02/25 21:56
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class OrderThirdArchiveResVO extends BasicVO {

    /**
     * 订单
     */
    @NotNull(message = "订单不能为空")
    private Long orderId;

    /**
     * 父订单号
     */
    private Long pOrderId;

    /**
     * 合同号
     */
    @NotNull(message = "合同号不能为空")
    private String contractNum;

    /**
     * 客户号
     */
    @NotNull(message = "客户号不能为空")
    private String clientCode;

    /**
     * bpm流程单号
     */
    private String bpmId;

    /**
     * 归档时间
     */
    @NotNull(message = "归档时间不能为空")
    private Long archiveTime;

    /**
     * 订单含税金额
     */
    @NotNull(message = "订单含税金额不能为空")
    private BigDecimal orderPrice;

    /**
     * 币种:VND越南,THB泰国,CNY人民币,USD美元
     */
    private String currency;

    /**
     * 工厂名称（即发票抬头）
     */
    private String companyName;

    /**
     * 成本中心
     */
    private String costCenter;

    /**
     * 费用科目
     */
    private String loanPro;

    /**
     * 申请人工号
     */
    private String empNo;

    /**
     * 需求人工号
     */
    private String demandNo;

    /**
     * 归档批次号
     */
    private String archiveBatchNum;

    /**
     * 开票批次号
     */
    private String invoiceBatchNum;

    /**
     * 公司代码
     */
    private String companyCode;

}