package com.jdi.isc.product.center.domain.stockInOutOrder.biz;

import com.jdi.isc.product.center.domain.common.biz.BasicVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 出入库订单保存请求参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StockInOutOrderSaveVO extends BasicVO {

    /**
     * 出入库单业务类型
     */
    @NotNull(message = "出入库单业务类型不能为空")
    private Integer orderBizType;

    /**
     * 客户号
     */
    private String clientCode;

    /**
     * 参考单号
     */
    private String referOrderNo;

    /**
     * 仓信息的id
     */
    @NotNull(message = "备货仓信息的id不能为空")
    private Long warehouseId;

    /**
     * 收货人姓名
     */
    private String consignee;

    /**
     * 收货人电话
     */
    private String consigneePhone;

    /**
     * 收货地址省ID
     */
    private Long consigneeProvinceId;

    /**
     * 收货地址城市ID
     */
    private Long consigneeCityId;

    /**
     * 收货地址区县ID
     */
    private Long consigneeCountyId;

    /**
     * 收货地址乡镇ID 四级地址编码：收货人乡镇地址编码(如果没有四级地址则传0)
     */
    private Long consigneeTownId;

    /**
     * 收货详细地址，最多100个字符
     */
    private String consigneeAddress;

    /**
     * 邮政编码
     */
    private String consigneeZip;

    /**
     * 收货国家
     */
    private String consigneeCountry;

    /**
     * skuId 和 数量
     */
    @NotEmpty(message = "商品列表不能为空")
    @Valid
    @Size(min = 1, max = 100, message = "单次购买商品种类范围1-100种")
    private List<StockInOutOrderSkuIdNumVO> skuNumsList;
}
