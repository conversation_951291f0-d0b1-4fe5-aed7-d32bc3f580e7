package com.jdi.isc.product.center.domain.countryMku;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jdi.isc.product.center.domain.task.dto.BaseWorkerDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 国家池翻译确认导出Excel DTO
 * @Author: system
 * @Date: 2024/12/20
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class CountryMkuTranslationExportExcelDTO extends BaseWorkerDTO {
    
    @ExcelProperty(value = "MKU ID", index = 0)
    private String mkuId;
    
    @ExcelProperty(value = "SPU ID", index = 1)
    private String spuId;
    
    @ExcelProperty(value = "国家代码", index = 2)
    private String targetCountryCode;
    
    @ExcelProperty(value = "国家名称", index = 3)
    private String targetCountryName;
    
    @ExcelProperty(value = "目标语言", index = 4)
    private String targetLang;
    
    @ExcelProperty(value = "MKU中文标题", index = 5)
    private String mkuTitleZh;
    
    @ExcelProperty(value = "SPU中文标题", index = 6)
    private String spuTitleZh;
    
    @ExcelProperty(value = "MKU目标语言标题", index = 7)
    private String mkuTitleTarget;
    
    @ExcelProperty(value = "SPU目标语言标题", index = 8)
    private String spuTitleTarget;
    
    @ExcelProperty(value = "翻译状态", index = 9)
    private String translationStatusName;
    
    @ExcelProperty(value = "品牌名称", index = 10)
    private String brandName;
    
    @ExcelProperty(value = "类目名称", index = 11)
    private String catName;
    
    @ExcelProperty(value = "创建时间", index = 12)
    private String createTime;
    
    @ExcelProperty(value = "更新时间", index = 13)
    private String updateTime;
}
