
package com.jdi.isc.product.center.domain.order.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * 订单列表 count 统计
 */
@Data
public class OrderListCountReq {
    /**
     * 订单显示状态集合，用于筛选订单列表。
     */
    @NotNull(message = "订单聚合状态不能为空")
    private List<String> orderStatusList;
    /**
     * 国家或地区代码，用于根据不同国家或地区统计订单数量。
     */
    @NotNull(message = "订单所属国家不能为空")
    private String countryCode;
}
