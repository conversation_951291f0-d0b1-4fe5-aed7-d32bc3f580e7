package com.jdi.isc.product.center.domain.customs;

import com.jdi.isc.product.center.domain.common.biz.BasicVO;
import com.jdi.isc.product.center.domain.sku.biz.SkuVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 计算面价系数对象
 *
 * <AUTHOR>
 * @date 2024/4/8
 **/
@Data
@NoArgsConstructor
public class ProductCustomsVO extends BasicVO {

    /**
     * 产品自定义信息的唯一标识符
     */
    @NotNull(message = "数据id不能为空")
    private Long id;
    /**
     * SKU的唯一标识符
     */
    @NotNull(message = "skuId不能为空")
    private Long skuId;
    /**
     * 海关编码，用于国际贸易中商品的分类和统计。
     */
    @NotNull(message = "海关编码不能为空")
    @Size(min = 1, max = 20, message = "海关编码长度必须在1到20个字符之间")
    private String hsCode;
    /**
     * 产品自定义信息的声明名称
     */
    @NotNull(message = "申报品名不能为空")
    @Size(min = 1, max = 100, message = "申报品名长度必须在1到100个字符之间")
    private String declarationName;

    /**
     * 产品自定义信息的英文声明名称
     */
    @NotNull(message = "英文申报品名不能为空")
    @Size(min = 1, max = 100, message = "英文申报品名长度必须在1到100个字符之间")
    private String declarationEnName;
    /**
     * 申报要素
     */
    @NotNull(message = "申报要素不能为空")
    @Size(min = 1, max = 500, message = "申报要素长度必须在1到500个字符之间")
    private String declarationElement;
}
