package com.jdi.isc.product.center.domain.finance.invoice;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 保存发票资质请求
 */
@Data
public class SaveInvoiceQualificationReqDTO {
    /** 国家码 */
    private String countryCode;
    /** 合同号 */
    private String contractNum;
    /** 客户号 */
    private String clientCode;
    /** 下单pin */
    private String pin;
    /** 发票抬头 */
    private String invoiceTitle;
    /** 客户税号 */
    private String customerTax;
    /** 发票地址 */
    private String invoiceAddress;
    /** 开票扩展信息，json字符串形式 不同国家不同 */
    private String extInfo;
}
