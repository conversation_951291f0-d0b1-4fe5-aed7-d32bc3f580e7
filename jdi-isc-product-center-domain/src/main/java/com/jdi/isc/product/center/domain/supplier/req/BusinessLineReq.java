package com.jdi.isc.product.center.domain.supplier.req;

import com.jdi.isc.product.center.domain.common.biz.FieldDiffVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import scala.Int;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 供应商产品线
 * @Date 2024/3/18 3:42 下午
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessLineReq  {

    /**
     * 供应商Code
     */
    @NotBlank(message = "供应商Code不能为空")
    private String supplierCode;

    /**
     * 品牌id
     */
    private Long brandId;
    /**
     * 代理商等级，用于区分不同级别的代理商。
     */
    private Integer agentLevel;

    /**
     * 类目id
     */
    private Long lastCatId;

    /**
     * 语言设置，用于国际化支持。
     */
    private String lang;

    /**
     * 分页查询时每页的记录数。
     */
    private Integer size;

    /**
     * 分页查询时的起始页码。
     */
    private Integer index;

}
