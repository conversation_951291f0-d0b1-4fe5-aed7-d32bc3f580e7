package com.jdi.isc.product.center.domain.supplier.biz;

import com.jdi.isc.product.center.domain.common.biz.CompareResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.util.List;

/**
 * @Description 供应商差异信息
 * @Author: zhaojianguo21
 * @Date: 2024/03/18 17:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierManager {
    /**
     * 关注人ERP。
     */
    @Length(max = 100, message = "关注人ERP最长100字符")
    private String mainErp;

    /**
     * 管理人ERP。
     */
    private String focusErps;

}
