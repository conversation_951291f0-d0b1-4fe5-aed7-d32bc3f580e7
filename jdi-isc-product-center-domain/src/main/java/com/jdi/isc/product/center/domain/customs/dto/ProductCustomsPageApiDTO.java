package com.jdi.isc.product.center.domain.customs.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * JSF接口关务列表查询请求DTO
 * <AUTHOR>
 * @date 2024/07/18 10:25
 */
@Data
public class ProductCustomsPageApiDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 国内skuId
     */
    private Long jdSkuId;
    
    /**
     * 国际skuId
     */
    private Long skuId;
    
    /**
     * 进出口国家代码: TH,VN,MY,ID,HU,BR
     */
    private String countryCode;
    
    /**
     * 国家hs code码
     */
    private String hsCode;
    
    /**
     * 数据来源
     */
    private String dataSource;
    
    /**
     * 创建时间范围开始
     */
    private Long createTimeStart;
    
    /**
     * 创建时间范围结束
     */
    private Long createTimeEnd;
    
    /**
     * 评估截止时间范围开始
     */
    private Long expectCompletionTimeStart;
    
    /**
     * 评估截止时间范围结束
     */
    private Long expectCompletionTimeEnd;
    
    /**
     * 商品关务状态
     */
    private Integer status;
    
    /**
     * 所属服务商
     */
    private String assignedSupplier;
    
    /**
     * 是否存在订单
     */
    private Integer orderExists;
    
    /**
     * 关务评估状态
     */
    private Integer customsComplianceStatus;
    
    /**
     * 强制性认证评估状态
     */
    private Integer compulsoryCertificationStatus;
    
    /**
     * hs code码
     */
    private Boolean hasHsCode;
    
    /**
     * 是否有申报要素
     */
    private Boolean hasDeclarationElement;
    
    /**
     * 申报品名
     */
    private Boolean hasDeclarationName;
    
    /**
     * 申报英文品名
     */
    private Boolean hasDeclarationEnName;
    
    /**
     * 采销erp
     */
    private String purchaseSalesErp;
    
    /**
     * 起始页
     */
    private Long index;
    
    /**
     * 页大小
     */
    private Long size;
    
    /**
     * 偏移量
     */
    private Long offset;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 自增ID
     */
    private Long id;
    
    /**
     * 用户pin
     */
    private String pin;
    
    /**
     * 客户简码
     */
    private String clientCode;
    
    /**
     * 语种
     */
    private String lang;
    
    /**
     * 站点类型
     */
    private Integer stationType;
    
    /**
     * 系统编码
     */
    private String systemCode;
} 