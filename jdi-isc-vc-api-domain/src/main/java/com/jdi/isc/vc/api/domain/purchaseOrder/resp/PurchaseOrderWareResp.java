

package com.jdi.isc.vc.api.domain.purchaseOrder.resp;

import com.jdi.isc.order.center.api.common.BasicApiReq;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.res.PurchaseOrderSkuDetailApiDTO;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.res.PurchaseOrderWareTaxApiDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;
@Data
public class PurchaseOrderWareResp extends BasicApiReq {
    private String purchaseOrderId;
    private Long orderId;
    private Long mkuId;
    private Long skuId;
    private Long catId;
    private Long brandId;
    private String currency;
    private BigDecimal purchasePrice;
    private BigDecimal skuAllPrice;
    private Integer skuNum;
    private Integer skuType;
    private Long parentSkuId;
    private String skuCountryCode;
    private String skuJsonInfo;
    private Integer mkuType;
    private Long parentMkuId;
    private String mkuCountryCode;
    private String mkuJsonInfo;
    private BigDecimal includeTaxPrice;
    private BigDecimal valueAddedTax;
    private BigDecimal valueAddedTaxRate;
    private Integer version;
    private String valueAddedTaxesInfo;
    private String saleUnit;
    private String skuImage;
    private String skuName;
    private BigDecimal purchaseTotalPrice;
    private Long jdSkuId;
    private String invoiceSkuName;
    private String mkuName;
    private String measureWeight;
    private String measureVolume;
    private Map<String, String> skuNameMap;
    private Long spuId;
    private String materialCode;
    private String materialName;
    private PurchaseOrderSkuDetailApiDTO orderSkuDetailPO;
    private PurchaseOrderWareTaxApiDTO purchaseOrderWareTaxInfoVO;
    private String  vendorSkuIdNew;


}
