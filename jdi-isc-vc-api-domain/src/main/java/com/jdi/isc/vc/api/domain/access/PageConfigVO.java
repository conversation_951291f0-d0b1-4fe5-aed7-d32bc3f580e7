package com.jdi.isc.vc.api.domain.access;

import lombok.Data;

import java.io.Serializable;

/**
 * 页面展示配置.
 *
 * <AUTHOR>
 */
@Data
public class PageConfigVO implements Serializable {
    /**
     * 字段key
     */
    private String fieldKey;
    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 是否禁用，0-否，1-是
     */
    private Integer disable;

    /**
     * 是否只读，0-否，1-是
     */
    private Integer readonly;

    /**
     * 是否展示，0-否，1-是
     */
    private Integer show;
}
