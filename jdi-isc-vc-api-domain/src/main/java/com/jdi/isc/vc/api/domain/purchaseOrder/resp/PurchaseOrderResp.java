package com.jdi.isc.vc.api.domain.purchaseOrder.resp;

import com.jdi.isc.order.center.api.common.BasicDTO;
import com.jdi.isc.order.center.api.common.OperateApiDTO;
import com.jdi.isc.order.center.api.purchaseOrder.biz.req.PurchaseOrderWaybillDTO;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.res.OrderConfigInfoApiDTO;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.res.PurchaseOrderConsigneeWrapperDTO;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.res.PurchaseOrderParcelWideDTO;
import com.jdi.isc.order.center.api.purchaseOrderRead.biz.res.PurchaseProgressApiDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
@Data
public class PurchaseOrderResp extends BasicDTO implements Serializable {
    private String purchaseOrderId;
    private Integer purchaseModel;
    private String pin;
    private String contractNum;
    private String parentPurchaseOrderId;
    private String splitPurchaseOrderId;
    private String iopOrderId;
    private Long orderId;
    private Integer skuNum;
    private Integer skuKindNum;
    private Integer purchaseOrderStatus;
    private Integer purchaseOrderType;
    private String countryCode;
    private String currency;
    private BigDecimal purchaseTotalPrice;
    private BigDecimal serviceFee;
    private BigDecimal orderFreightPrice;
    private BigDecimal waresPurchaseTotalPrice;
    private BigDecimal purchaseOrderTaxes;
    private List<PurchaseOrderWareResp> purchaseOrderWareVOList;
    private Long receiveTime;
    private Long shippedTime;
    private Long completeTime;
    private Long cancelTime;
    private Long purchaseCreateTime;
    private Long confirmTime;
    private String purchaseOrderExtInfo;
    private String updateClientInfo;
    private Integer validState;
    private String supplierCode;
    private String businessLicenseName;
    private Integer supplierSettlementMethod;
    private Long enterWarehouseTime;
    private String enterpriseWarehouseCode;
    private Long enterStorehouseTime;
    private String enterWarehouseNo;
    private String enterWarehouseName;
    private Map<String, String> warehouseNameMap;
    private String warehouseNo;
    private Integer inboundWarehouseWareNum;
    private List<OperateApiDTO> operateVOList;
    private List<OperateApiDTO> allOperateMenu;
    private Boolean canSplitOrder;
    private List<PurchaseProgressApiDTO> purchaseProgress;
    private List<OrderConfigInfoApiDTO> orderConfigInfoList;
    private PurchaseOrderConsigneeWrapperDTO purchaseOrderConsigneeInfo;
    private PurchaseOrderWaybillDTO purchaseOrderWaybillVO;
    private String waybillNum;
    private List<PurchaseOrderParcelWideDTO> parcelList;
    private boolean hasEnterMark;
    private Set<String> parcelIds;
    private Integer settlementStatus;
    private Integer invoiceUploadStatus;
    private Integer invoiceApprovalStatus;
    BigDecimal icmsTaxValueTotal;
    BigDecimal ipiTaxValueTotal;
    BigDecimal cofinsTaxValueTotal;
    BigDecimal pisTaxValueTotal;
    private Long latestShippingTime;
    private BigDecimal allWareTotalPrice;
    private String symbol;



}
