package com.jdi.isc.product.center.service.category;


import com.alibaba.fastjson.JSON;
import com.jdi.isc.product.center.domain.category.biz.CategoryComboBoxReqVO;
import com.jdi.isc.product.center.domain.category.biz.CategoryComboBoxVO;
import com.jdi.isc.product.center.service.manage.category.CategoryOutService;
import com.jdi.isc.product.center.web.ServiceApplication;
import com.jdi.isc.product.soa.api.common.LangConstant;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description：CategoryOutServiceTest
 * @Date 2025-06-05
 */
@Slf4j
@SpringBootTest(classes = ServiceApplication.class)
@AutoConfigureMockMvc
public class CategoryOutServiceTest {

    @Resource
    private CategoryOutService categoryOutService;

    @Test
    public void testChild() {
        CategoryComboBoxReqVO reqVO = new CategoryComboBoxReqVO();
        reqVO.setId(0L);
        reqVO.setStatus(1);
        reqVO.setLang(LangConstant.LANG_ZH);
        List<CategoryComboBoxVO> categoryComboBoxVOS = categoryOutService.queryChildren(reqVO);
        System.out.println(JSON.toJSONString(categoryComboBoxVOS));
    }
}
