package com.jdi.isc.product.center.service.auth;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.common.web.LoginContext;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.product.center.common.ducc.OperDuccConfig;
import com.jdi.isc.product.center.domain.enums.order.PurchaseModelEnum;
import com.jdi.isc.product.center.domain.order.biz.OrderPageReqVO;
import com.jdi.isc.product.center.domain.order.po.OrderPO;
import com.jdi.isc.product.center.domain.purchaseOrder.biz.PurchaseOrderVO;
import com.jdi.isc.product.center.domain.purchaseOrder.biz.QueryPurchaseOrderListVO;
import com.jdi.isc.product.center.service.atomic.order.OrderAtomicService;
import com.jdi.isc.product.center.service.atomic.purchaseOrder.PurchaseOrderAtomicService;
import com.jdi.isc.product.center.service.manage.purchaseOrder.PurchaseOrderService;
import com.jdi.isc.product.center.web.ServiceApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/5 13:33
 */
@Slf4j
@SpringBootTest(classes = ServiceApplication.class)
public class DataScoreTest {

    @Autowired
    private OrderAtomicService orderAtomicService;

    @Autowired
    private PurchaseOrderService purchaseOrderService;

    @Autowired
    private OperDuccConfig operDuccConfig;

    @Test
    public void test() {
        LoginContext mockUser = new LoginContext();
        mockUser.setPin("liudong2122222");
        LoginContext.setLoginContext(mockUser);
        OrderPageReqVO orderPageReqVO = new OrderPageReqVO();
        orderPageReqVO.setPurchaseModel(PurchaseModelEnum.STOCK_UP.getType());
        orderPageReqVO.setIndex(1L);
        orderPageReqVO.setSize(10L);
        Page<OrderPO> page = orderAtomicService.page(orderPageReqVO);
        // 13
        System.out.println(page);
    }

    @Test
    public void testPo() {
        LoginContext mockUser = new LoginContext();
        mockUser.setPin("liudong2122222");
        LoginContext.setLoginContext(mockUser);
        QueryPurchaseOrderListVO queryPurchaseOrderListVO = new QueryPurchaseOrderListVO();

        queryPurchaseOrderListVO.setIndex(1L);
        queryPurchaseOrderListVO.setSize(100L);
        queryPurchaseOrderListVO.setPurchaseModel(1);
        PageInfo<PurchaseOrderVO> page = purchaseOrderService.page(queryPurchaseOrderListVO);
        System.out.println(page);
    }


}
