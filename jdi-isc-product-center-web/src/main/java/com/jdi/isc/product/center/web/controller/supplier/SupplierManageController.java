package com.jdi.isc.product.center.web.controller.supplier;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.center.domain.supplier.biz.*;
import com.jdi.isc.product.center.service.manage.supplier.SupplierAllInfoManageService;
import com.jdi.isc.product.center.service.manage.supplier.SupplierModifyRecordManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Description: 供应商全部信息接口
 * @Author: zhaojianguo21
 * @Date: 2024/03/19 20:47
 **/

@Slf4j
@RestController
@RequestMapping("/api/supplier/allInfo/")
public class SupplierManageController {

    @Resource
    private SupplierAllInfoManageService supplierAllInfoManageService;

    @Resource
    private SupplierModifyRecordManageService modifyRecordManageService;

    /**
     * 保存、更新
     */
    @PostMapping("saveOrUpdate")
    @ResponseBody
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> saveOrUpdate(@RequestBody SupplierAllInfoVO input) {
        return supplierAllInfoManageService.saveOrUpdate(input);
    }

    /**
     * 详情
     */
    @PostMapping("detail")
    @ToolKit(exceptionWrap = true)
    public DataResponse<SupplierAllInfoDetailVO.Response> detail(@RequestBody SupplierAllInfoDetailVO.Request input) {
        return DataResponse.success(supplierAllInfoManageService.detail(input));
    }

    /**
     * 分页查询
     */
    @PostMapping("page")
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<SupplierAllInfoPageVO.Response>> page(@RequestBody SupplierAllInfoPageVO.Request input) {
        return DataResponse.success(supplierAllInfoManageService.page(input));
    }


    /**
     * 校验账号名称是否重复
     */
    @PostMapping("checkAccountName")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> checkAccountName(@RequestBody SupplierAccountVO input) {
        return supplierAllInfoManageService.checkAccountName(input);
    }

    @PostMapping("checkBrandRelation")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> checkBrandRelation(@RequestBody BusinessLineVO businessLineVO) {
        return supplierAllInfoManageService.checkBrandRelation(businessLineVO);
    }


    /**
     * 处理xbp请求
     * @param supplierCode 供应商代码
     * @return 处理结果
     */
    @GetMapping("/xbp")
    @ResponseBody
    public DataResponse<Boolean> xbp(String supplierCode) {
        return DataResponse.success(modifyRecordManageService.hasWaitApprovalRecord(supplierCode));
    }

    /**
     * 检查仓库是否满足新增条件
     * @param supplierWarehouseVO 业务线信息。
     * @return true 如果仓库满足要求，false 否则。
     */
    @PostMapping("checkWarehouses")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> checkWarehouses(@RequestBody SupplierWarehouseVO supplierWarehouseVO) {
        return supplierAllInfoManageService.checkWarehouses(supplierWarehouseVO);
    }

}
