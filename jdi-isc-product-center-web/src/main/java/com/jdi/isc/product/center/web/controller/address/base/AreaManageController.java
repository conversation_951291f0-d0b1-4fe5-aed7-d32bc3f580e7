package com.jdi.isc.product.center.web.controller.address.base;

import com.alibaba.fastjson.JSONObject;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.center.common.costants.Constant;
import com.jdi.isc.product.center.domain.area.biz.*;
import com.jdi.isc.product.center.service.manage.area.AreaManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * @Description: 地址数据维护接口服务
 * @Author: zhaojianguo21
 * @Date: 2024/04/28
 **/
@Slf4j
@RestController
@RequestMapping("/api/area/")
public class AreaManageController {

    @Resource
    private AreaManageService areaManageService;

    /**
     * 区域保存
     */
    @PostMapping("save")
    @ResponseBody
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<AreaNodeTreeRenderVO>> save(@RequestBody GlobalAreaInfoVO input) {
        DataResponse<Boolean> res = null;
        try {
            res = areaManageService.saveOrUpdate(input);
        } catch (Exception e) {
            log.error("input={}", JSONObject.toJSONString(input), e);
            return DataResponse.error("保存地址信息失败。"+e.getMessage());
        }
        if (null==res || !res.getSuccess()){
            return DataResponse.error(res.getMessage());
        }
        DataResponse<List<AreaNodeTreeRenderVO>> childrenRes = areaManageService.treeChildren(input.getParentId());
        return DataResponse.success(childrenRes.getData());
    }

    /**
     * 区域更新
     */
    @PostMapping("update")
    @ResponseBody
    @ToolKit(exceptionWrap = true)
    public DataResponse<AreaNodeTreeRenderVO> update(@RequestBody GlobalAreaInfoVO input) {
        DataResponse<Boolean> res = null;
        try {
            res = areaManageService.saveOrUpdate(input);
        } catch (Exception e) {
            log.error("input={}", JSONObject.toJSONString(input), e);
            return DataResponse.error("更新地址信息失败。"+e.getMessage());
        }
        if (null==res || !res.getSuccess()){
            return DataResponse.error(res.getMessage());
        }
        AreaNodeTreeRenderVO renderVO = new AreaNodeTreeRenderVO();
        renderVO.setKey(input.getId());
        AreaNameInfoVO areaNameInfoVO = input.getLangList().stream().filter(o->Constant.SHOW_AREA_DEFAULT_LANG.equals(o.getLang())).findFirst().orElse(null);
        renderVO.setTitle(null!=areaNameInfoVO?areaNameInfoVO.getLangName():"-");

        // 取当前节点的子节点
        DataResponse<List<AreaNodeTreeRenderVO>> childrenRes = areaManageService.treeChildren(input.getId());
        renderVO.setChildren(childrenRes.getData());

        return DataResponse.success(renderVO);
    }

    /**
     * 区域详情
     */
    @GetMapping("detail")
    @ToolKit(exceptionWrap = true)
    public DataResponse<GlobalAreaInfoVO> detail(Long id) {
        return DataResponse.success(areaManageService.detail(id));
    }

    /**
     * 区域分页查询
     */
    @PostMapping("page")
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<GlobalAreaPageVO.Response>> page(@RequestBody GlobalAreaPageVO.Request input) {
        return DataResponse.success(areaManageService.page(input));
    }

    /**
     * 区域树的子节点信息
     */
    @PostMapping("tree/children")
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<AreaNodeTreeRenderVO>> treeChildren(@RequestBody AreaTreeReqVO input) {
        return areaManageService.treeChildren(input.getParentId());
    }


}
