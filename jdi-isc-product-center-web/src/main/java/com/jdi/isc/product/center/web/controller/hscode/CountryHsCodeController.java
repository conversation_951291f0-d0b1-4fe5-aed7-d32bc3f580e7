package com.jdi.isc.product.center.web.controller.hscode;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.library.i18n.annotations.JdiI18n;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeDetailVO;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageRequest;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodePageResponse;
import com.jdi.isc.product.center.domain.hscode.biz.CountryHsCodeVO;
import com.jdi.isc.product.center.service.manage.hscode.CountryHsCodeManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 6个国家的HsCode管理控制器
 * 支持国家：BR(巴西)、HU(匈牙利)、ID(印度尼西亚)、MY(马来西亚)、TH(泰国)、VN(越南)
 * <AUTHOR>
 * @date 2024/02/21 10:25
 */
@Slf4j
@RestController
@RequestMapping("/api/country/hscode/")
public class CountryHsCodeController {

    @Resource
    private CountryHsCodeManageService countryHsCodeManageService;

    /**
     * 保存或更新6个国家的HsCode信息
     */
    @PostMapping("saveOrUpdate")
    @ResponseBody
    @ToolKit
    @JdiI18n
    public DataResponse<CountryHsCodeVO> saveOrUpdate(@RequestBody CountryHsCodeVO input) {
        return countryHsCodeManageService.saveOrUpdate(input);
    }

    /**
     * 详情查询
     */
    @PostMapping("detail")
    @ToolKit
    @JdiI18n
    public DataResponse<CountryHsCodeVO> detail(@RequestBody CountryHsCodeDetailVO input) {
        return countryHsCodeManageService.detail(input);
    }

    /**
     * 分页查询6个国家的HsCode信息
     */
    @PostMapping("pageSearch")
    @ToolKit
    @JdiI18n
    public DataResponse<PageInfo<CountryHsCodePageResponse>> pageSearch(@RequestBody CountryHsCodePageRequest input) {
        return countryHsCodeManageService.pageSearch(input);
    }
} 