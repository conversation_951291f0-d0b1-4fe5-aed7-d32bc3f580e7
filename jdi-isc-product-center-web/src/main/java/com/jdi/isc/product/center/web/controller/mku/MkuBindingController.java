package com.jdi.isc.product.center.web.controller.mku;


import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.center.domain.mku.biz.MkuChangeBindingRecordQueryVO;
import com.jdi.isc.product.center.domain.mku.biz.MkuChangeBindingRecordVO;
import com.jdi.isc.product.center.service.manage.mku.MkuChangeBindingManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * MKU换绑
 * <AUTHOR>
 * @description：MkuBindingController
 * @Date 2025-08-27
 */
@Slf4j
@RestController
@RequestMapping("/api/mku/mkuBinding")
public class MkuBindingController {

    @Resource
    private MkuChangeBindingManageService mkuChangeBindingManageService;

    /**
     * 分页查询
     */
    @PostMapping("/page")
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<MkuChangeBindingRecordVO>> list(@RequestBody MkuChangeBindingRecordQueryVO queryVO) {
        return mkuChangeBindingManageService.page(queryVO);
    }
}
