package com.jdi.isc.product.center.web.controller.supplier;


import com.jd.common.web.LoginContext;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.isc.library.i18n.exception.BusinessException;
import com.jdi.isc.library.i18n.exception.BusinessFeatureException;
import com.jdi.isc.product.center.common.enums.response.BusinessErrorEnums;
import com.jdi.isc.product.center.domain.supplier.req.BusinessLineEditReq;
import com.jdi.isc.product.center.domain.supplier.req.BusinessLineReq;
import com.jdi.isc.product.center.domain.supplier.resp.BusinessLineRelationResp;
import com.jdi.isc.product.center.service.manage.supplier.v2.BusinessLineManageV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @description: 产品线接口类
 * @author: zhangjin176
 * @date: 2025/5/10 01:54
 * @version:
 **/
@Slf4j
@RestController
@RequestMapping("/api/supplier/businessline/v2")
public class SupplierBusinessLineV2Controller {

    /**
     * 业务线管理服务对象，用于处理业务线相关操作。
     */
    @Resource
    private BusinessLineManageV2Service businessLineManageV2Service;


    /**
     * 获取业务线列表
     *
     * @param lineReq 业务线请求参数
     * @return 业务线列表响应
     */
    @PostMapping("/list")
    @ResponseBody
    public DataResponse<PageInfo<BusinessLineRelationResp>> list(@RequestBody @Valid BusinessLineReq lineReq) {
        try {
            lineReq.setLang(LoginContext.getLoginContext().getLang());
            return DataResponse.success(businessLineManageV2Service.list(lineReq));
        } catch (BusinessFeatureException e) {
            // 业务功能性异常（如某些特定逻辑不满足）
            log.error("【获取业务线列表-功能异常】参数：{}，异常：{}", lineReq, e.getMessage(), e);
            return DataResponse.buildError(e.getCode(), String.format(e.getMessage(), e.getErrorParams()));
        } catch (BusinessException e) {
            // 普通业务异常（如参数错误、非法操作）
            log.error("【获取业务线列表-业务异常】参数：{}，异常：{}", lineReq, e.getMessage(), e);
            return DataResponse.buildError(e.getCode(), e.getMessage());
        } catch (Exception e) {
            // 系统级异常
            log.error("【获取业务线列表-系统异常】参数：{}，异常：{}", lineReq, e.getMessage(), e);
            return DataResponse.buildError(BusinessErrorEnums.SYSTEM_BUSY.getCode(), BusinessErrorEnums.SYSTEM_BUSY.getMessage());
        }
    }


    /**
     * 处理业务线编辑请求
     *
     * @param req 业务线编辑请求体
     * @return 业务线列表响应
     */
    @PostMapping("/deal")
    @ResponseBody
    public DataResponse<String> deal(@RequestBody BusinessLineEditReq req) {
        String operator = LoginContext.getLoginContext().getPin();
        try {
            String batchNum = businessLineManageV2Service.handleBusinessLineChange(req, operator);
            return DataResponse.success(batchNum);
        } catch (BusinessFeatureException e) {
            log.error("【处理业务线编辑-功能异常】操作人：{}，参数：{}，异常：{}", operator, req, e.getMessage(), e);
            return DataResponse.buildError(e.getCode(), String.format(e.getMessage(), e.getErrorParams()));
        } catch (BusinessException e) {
            log.error("【处理业务线编辑-业务异常】操作人：{}，参数：{}，异常：{}", operator, req, e.getMessage(), e);
            return DataResponse.buildError(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("【处理业务线编辑-系统异常】操作人：{}，参数：{}，异常：{}", operator, req, e.getMessage(), e);
            return DataResponse.buildError(BusinessErrorEnums.SYSTEM_BUSY.getCode(), BusinessErrorEnums.SYSTEM_BUSY.getMessage());
        }
    }


}
