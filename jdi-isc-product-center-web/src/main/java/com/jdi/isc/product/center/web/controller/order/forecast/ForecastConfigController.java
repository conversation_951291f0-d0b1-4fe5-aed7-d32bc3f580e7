package com.jdi.isc.product.center.web.controller.order.forecast;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.library.i18n.annotations.JdiI18n;
import com.jdi.isc.library.i18n.exception.BusinessException;
import com.jdi.isc.library.i18n.exception.BusinessFeatureException;
import com.jdi.isc.library.i18n.exception.JsfException;
import com.jdi.isc.order.center.api.forecast.biz.ForecastOrderApiDTO;
import com.jdi.isc.order.center.api.forecast.biz.req.ForecastDetailOrderApiReq;
import com.jdi.isc.order.center.api.forecast.biz.req.ForecastOrderApiReq;
import com.jdi.isc.order.center.api.forecast.biz.req.IssuedForecastOrderApiReq;
import com.jdi.isc.order.center.api.forecast.biz.req.UpdateForecastOrderStatusApiReq;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderApiResp;
import com.jdi.isc.order.center.api.forecast.biz.resp.ForecastOrderDetailResp;
import com.jdi.isc.product.center.common.costants.ServiceConstant;
import com.jdi.isc.product.center.common.enums.response.BusinessErrorEnums;
import com.jdi.isc.product.center.common.utils.SystemUtil;
import com.jdi.isc.product.center.service.manage.forecast.ForecastService;
import com.jdi.isc.task.center.api.forecast.impl.ForecastPurchaseOrderPrintApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;

/**
 * 备货单配置
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/forecast")
public class ForecastConfigController {

    @Resource
    private ForecastService forecastService;

    /**
     * 分页查询
     */
    @PostMapping("/page")
    @ResponseBody
    @JdiI18n
    @ToolKit(exceptionWrap = true,validFlag=false)
    public DataResponse<PageInfo<ForecastOrderApiResp>> page(@RequestBody ForecastOrderApiReq apiReq) {
        try {
            SystemUtil.fillOrderExtSystemInfo(apiReq);
            PageInfo<ForecastOrderApiResp> paged = forecastService.pageForecastOrder(apiReq);
            return DataResponse.success(paged);
        } catch (BusinessException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (JsfException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (BusinessFeatureException e) {
            return DataResponse.error(e.getCode(), e.getMsg(), e.getErrorParams());
        } catch (Exception e) {
            log.error("【错误】ForecastConfigController.page 方法发生异常: error", e);
            return DataResponse.error(BusinessErrorEnums.DOWNSTREAM_SERVICE_ERROR_PARAMS.getMessage(), new String[]{ServiceConstant.ORDER});
        }
    }

    @PostMapping("/submitForecastOrder")
    @ResponseBody
    @JdiI18n
    @ToolKit(exceptionWrap = true,validFlag=false)
    public DataResponse<Boolean> submitForecastOrder(@RequestBody  ForecastOrderApiDTO apiDTO){
        try {
            com.jd.common.web.LoginContext login = com.jd.common.web.LoginContext.getLoginContext();
            SystemUtil.fillOrderExtSystemInfo(apiDTO);
            apiDTO.setUpdater(login.getPin());
            apiDTO.setCreator(login.getPin());
            apiDTO.setUpdateTime(System.currentTimeMillis());
            return DataResponse.success(forecastService.submitForecastOrder(apiDTO));
        } catch (BusinessException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (JsfException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (BusinessFeatureException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("【错误】ForecastConfigController.submitForecastOrder 方法发生异常: error", e);
            return DataResponse.error(BusinessErrorEnums.DOWNSTREAM_SERVICE_ERROR_PARAMS.getMessage(), new String[]{ServiceConstant.ORDER});
        }
    }

    //【查看】
    @PostMapping("/detailForecastOrder")
    @ResponseBody
    @JdiI18n
    @ToolKit(exceptionWrap = true,validFlag=false)
    public DataResponse<ForecastOrderDetailResp> detailForecastOrder(@RequestBody  ForecastDetailOrderApiReq apiDTO){
        try {
            SystemUtil.fillOrderExtSystemInfo(apiDTO);
            ForecastOrderDetailResp forecastOrderDetailResp = forecastService.detailForecastOrder(apiDTO);
            return DataResponse.success(forecastOrderDetailResp);
        } catch (BusinessException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (JsfException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (BusinessFeatureException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("【错误】ForecastConfigController.detailForecastOrder 方法发生异常: error", e);
            return DataResponse.error(BusinessErrorEnums.DOWNSTREAM_SERVICE_ERROR_PARAMS.getMessage(), new String[]{ServiceConstant.ORDER});
        }
    }

    //全部入库
    @PostMapping("/updateForecastOrderStatus")
    @ResponseBody
    @JdiI18n
    @ToolKit(exceptionWrap = true,validFlag=false)
    public DataResponse<Boolean> updateForecastOrderStatus(@RequestBody  UpdateForecastOrderStatusApiReq apiDTO){
        try {
            com.jd.common.web.LoginContext login = com.jd.common.web.LoginContext.getLoginContext();
            SystemUtil.fillOrderExtSystemInfo(apiDTO);
            apiDTO.setUpdater(login.getPin());
            apiDTO.setUpdateTime(System.currentTimeMillis());
            return DataResponse.success(forecastService.updateForecastOrderStatus(apiDTO));
        } catch (BusinessException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (JsfException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (BusinessFeatureException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("【错误】ForecastConfigController.updateForecastOrderStatus 方法发生异常: error", e);
            return DataResponse.error(BusinessErrorEnums.DOWNSTREAM_SERVICE_ERROR_PARAMS.getMessage(), new String[]{ServiceConstant.ORDER});
        }
    }

    //【下发备货入库】
    @PostMapping("/issuedForecastOrder")
    @ResponseBody
    @JdiI18n
    @ToolKit(exceptionWrap = true,validFlag=false)
    public DataResponse<Boolean> issuedForecastOrder(@RequestBody  IssuedForecastOrderApiReq apiDTO){
        try {
            com.jd.common.web.LoginContext login = com.jd.common.web.LoginContext.getLoginContext();
            SystemUtil.fillOrderExtSystemInfo(apiDTO);
            apiDTO.setUpdater(login.getPin());
            apiDTO.setUpdateTime(System.currentTimeMillis());
            return DataResponse.success(forecastService.issuedForecastOrder(apiDTO));
        } catch (BusinessException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (JsfException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (BusinessFeatureException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("【错误】ForecastConfigController.issuedForecastOrder 方法发生异常: error", e);
            return DataResponse.error(BusinessErrorEnums.DOWNSTREAM_SERVICE_ERROR_PARAMS.getMessage(), new String[]{ServiceConstant.ORDER});
        }
    }
    //【打印备货入库单】
    @PostMapping("/printEntryStock")
    @ResponseBody
    @JdiI18n
    @ToolKit(exceptionWrap = true,validFlag=false)
    public DataResponse<String> printEntryStock(@RequestBody  ForecastPurchaseOrderPrintApiDTO apiDTO){
        try {
            apiDTO.setLangList(Collections.singletonList(com.jd.common.web.LoginContext.getLoginContext().getLang()));
            return DataResponse.success(forecastService.printEntryStock(apiDTO));
        } catch (BusinessException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (JsfException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (BusinessFeatureException e) {
            return DataResponse.error(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("【错误】ForecastConfigController.printEntryStock 方法发生异常: error", e);
            return DataResponse.error(BusinessErrorEnums.DOWNSTREAM_SERVICE_ERROR_PARAMS.getMessage(), new String[]{ServiceConstant.ORDER});
        }
    }
}
