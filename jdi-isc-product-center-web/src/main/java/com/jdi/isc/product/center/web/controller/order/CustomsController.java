package com.jdi.isc.product.center.web.controller.order;

import com.alibaba.fastjson.JSONObject;
import com.jd.common.web.LoginContext;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.library.i18n.annotations.JdiI18n;
import com.jdi.isc.library.i18n.exception.BusinessException;
import com.jdi.isc.library.i18n.exception.JsfException;
import com.jdi.isc.library.i18n.response.ResponseData;
import com.jdi.isc.order.center.api.customs.biz.req.ProductCustomsPageReqDTO;
import com.jdi.isc.product.center.common.enums.response.BusinessErrorEnums;
import com.jdi.isc.product.center.domain.customs.ProductCustomsVO;
import com.jdi.isc.product.center.domain.customs.dto.*;
import com.jdi.isc.product.center.domain.customs.req.CustomsPageReqDTO;
import com.jdi.isc.product.center.domain.customs.resp.CustomsInfoRespDTO;
import com.jdi.isc.product.center.domain.task.po.TaskPO;
import com.jdi.isc.product.center.service.manage.customs.CustomsManageService;
import com.jdi.isc.product.center.service.manage.order.CustomsService;
import com.jdi.isc.product.center.service.manage.task.TaskMangeService;
import com.jdi.isc.product.soa.api.customs.supplier.biz.CustomsSupplierApiDTO;
import com.jdi.isc.product.soa.api.customs.supplier.req.CustomsSupplierPageReqApiDTO;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskCreateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 报关服务
 * <AUTHOR>
 * @date 2024/5/16
 */
@Slf4j
@RestController
@RequestMapping("/api/customs/")
public class CustomsController {

    @Resource
    private CustomsService customsService;

    @Resource
    private TaskMangeService taskMangeService;

    @Resource
    private CustomsManageService customsManageService;

    /**
     * 分页获取产品海关信息
     * @param pageReqDTO 海关信息查询条件
     * @return 分页后的海关信息列表
     */
//    @JdiI18n
//    @PostMapping("page")
//    public DataResponse<PageInfo<ProductCustomsDTO>> page(@RequestBody ProductCustomsPageReqDTO pageReqDTO) {
//        try {
//            ValidationUtil.checkListLength(pageReqDTO.getSkuIds(),SKU_ID);
//            ValidationUtil.checkListLength(pageReqDTO.getJdSkuIds(),JD_SKU_ID);
//            return DataResponse.success(customsService.page(pageReqDTO));
//        } catch (BusinessException e) {
//            log.error("获取产品海关信息-业务请求失败", e);
//            return DataResponse.error(e.getCode(), e.getMessage());
//        } catch (BusinessFeatureException e) {
//            log.error("获取产品海关信息-业务请求失败", e);
//            return DataResponse.error(e.getCode(),e.getMessage(),e.getErrorParams());
//        } catch (JsfException e) {
//            BusinessErrorEnums swap = BusinessErrorEnums.swap(e.getCode());
//            log.error("获取产品海关信息-Jsf请求失败", e);
//            return DataResponse.error(swap.getCode(), swap.getMessage(),e.getErrorParams());
//        } catch (Exception e) {
//            log.error("获取产品海关信息-系统请求失败", e);
//            return DataResponse.error(BusinessErrorEnums.SYSTEM_BUSY.getCode(),BusinessErrorEnums.SYSTEM_BUSY.getMessage());
//        }
//    }



    /**
     * 编辑产品海关信息
     * @param productCustomsVO 产品海关信息请求对象
     * @return 编辑结果
     */
    @PostMapping(value = "edit")
    public DataResponse<Boolean> edit(@Valid @RequestBody ProductCustomsVO productCustomsVO) {
        try {
            LoginContext login = LoginContext.getLoginContext();
            productCustomsVO.setUpdater(login.getPin());
            return DataResponse.success(customsService.edit(productCustomsVO));
        } catch (BusinessException e) {
            log.error("更新产品海关信息-业务请求失败", e);
            return DataResponse.error(e.getCode(), e.getMessage());
        } catch (JsfException e) {
            BusinessErrorEnums swap = BusinessErrorEnums.swap(e.getCode());
            log.error("更新产品海关信息-Jsf请求失败", e);
            return DataResponse.error(swap.getCode(), swap.getMessage(),e.getErrorParams());
        } catch (Exception e) {
            log.error("更新产品海关信息-系统请求失败", e);
            return DataResponse.error(BusinessErrorEnums.SYSTEM_BUSY.getCode(),BusinessErrorEnums.SYSTEM_BUSY.getMessage());
        }
    }


    /**
     * 导出报关信息
     * @return 导出任务的ID
     */
    @PostMapping("/export")
    public ResponseData<Long> export(@RequestBody ProductCustomsPageReqDTO pageReqDTO) {
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.product.center.web.controller.invoice.CustomsController.export");
        try {
            TaskPO res = taskMangeService.save("", LoginContext.getLoginContext().getPin(), JSONObject.toJSONString(pageReqDTO), TaskBizTypeEnum.PRODUCT_CUSTOMS_PAGE_EXPORT, TaskCreateTypeEnum.EXPORT);
            return ResponseData.ok(res.getId());
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            log.error("导出报关信息-系统请求失败", e);
            return ResponseData.error(BusinessErrorEnums.SYSTEM_BUSY.getCode());
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }


    /**
     * 分页查询海关信息
     * @param reqDTO 海关信息分页查询请求参数
     * @return 海关信息分页查询结果
     */
    @PostMapping("/page")
    @ToolKit(exceptionWrap = true)
    @JdiI18n
    public DataResponse<PageInfo<CustomsInfoRespDTO>> page(@RequestBody CustomsPageReqDTO reqDTO) {
        reqDTO.setOperator(LoginContext.getLoginContext().getPin());
        return customsManageService.page(reqDTO);
    }

    /**
     * 获取商品的税则详情信息
     * @param id 商品ID
     * @return 海关信息响应对象
     */
    @GetMapping("/detail")
    @ToolKit(exceptionWrap = true)
    @JdiI18n
    public DataResponse<CustomsInfoRespDTO> productCustomsDetail(Long id) {
        return customsManageService.productCustomsDetail(id);
    }


    /**
     * 保存或更新产品海关申报草稿。
     * @param dto 产品海关申报草稿DTO对象。
     * @return 操作结果。
     */
    @PostMapping("/assessment")
    @ToolKit(exceptionWrap = true)
    @JdiI18n
    public DataResponse<Boolean> assessment(@RequestBody ProductCustomsAssessmentDTO dto) {
        dto.setOperator(LoginContext.getLoginContext().getPin());
        dto.setOperateTime(System.currentTimeMillis());
        dto.setOperateCode("update_declaration_info");
        return customsManageService.assessment(dto);
    }

    /**
     * 保存或更新产品的强制性认证信息。
     * @param dto 强制性认证信息的数据传输对象。
     * @return 更新结果。
     */
    @PostMapping("/compulsory/certificate")
    @ToolKit(exceptionWrap = true)
    @JdiI18n
    public DataResponse<Boolean> saveOrUpdate(@RequestBody ProductCustomsCompulsoryCertificateDTO dto) {
        dto.setOperator(LoginContext.getLoginContext().getPin());
        dto.setOperateTime(System.currentTimeMillis());
        dto.setOperateCode("update_compulsory_certification_info");
        return customsManageService.compulsoryCertificate(dto);
    }

    /**
     * 批量确认评估信息接口。
     * @param dto 产品海关申报草稿状态更新DTO对象。
     * @return 操作结果。
     */
    @PostMapping("/confirm")
    @ToolKit(exceptionWrap = true)
    @JdiI18n
    public DataResponse<Boolean> confirm(@RequestBody ProductCustomsConfirmDTO dto) {
        dto.setOperator(LoginContext.getLoginContext().getPin());
        dto.setOperateTime(System.currentTimeMillis());
        dto.setOperateCode("confirm");
        return customsManageService.confirm(dto);
    }

    /**
     * 批量确认评估信息接口。
     * @param dto 产品海关申报草稿状态更新DTO对象。
     * @return 操作结果。
     */
    @PostMapping("/delete")
    @ToolKit(exceptionWrap = true)
    @JdiI18n
    public DataResponse<Boolean> delete(@RequestBody ProductCustomsConfirmDTO dto) {
        dto.setOperator(LoginContext.getLoginContext().getPin());
        dto.setOperateTime(System.currentTimeMillis());
        dto.setOperateCode("delete");
        return customsManageService.delete(dto);
    }


    /**
     * 更新产品海关申报草稿分配供应商。
     * @param dto 产品海关申报草稿分配供应商DTO对象。
     * @return 操作结果。
     */
    @PostMapping("/customsSupplier/list")
    @ToolKit(exceptionWrap = true)
    @JdiI18n
    public DataResponse<PageInfo<CustomsSupplierApiDTO>> customsSupplierList(@RequestBody CustomsSupplierPageReqApiDTO dto) {
        return customsManageService.customsSupplierList(dto);
    }


    /**
     * 分配服务商。
     * @param dto 产品海关申报草稿分配供应商DTO对象。
     * @return 操作结果。
     */
    @PostMapping("/assignedSupplier")
    @ToolKit(exceptionWrap = true)
    @JdiI18n
    public DataResponse<Boolean> assignedSupplier(@RequestBody ProductCustomsAssignedSupplierDTO dto) {
        dto.setOperator(LoginContext.getLoginContext().getPin());
        dto.setOperateTime(System.currentTimeMillis());
        return customsManageService.assignedSupplier(dto);
    }


}
