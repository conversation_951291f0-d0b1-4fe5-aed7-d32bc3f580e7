package com.jdi.isc.product.center.web.controller.countryMku;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.jd.common.web.LoginContext;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.library.common.enmus.file.FileTypeEnum;
import com.jdi.isc.product.center.common.costants.Constant;
import com.jdi.isc.product.center.common.frame.LangContextHolder;
import com.jdi.isc.product.center.domain.agreementPrice.biz.CountryAgreementPriceSyncReqVO;
import com.jdi.isc.product.center.domain.agreementPrice.biz.CountryAgreementPriceSyncResVO;
import com.jdi.isc.product.center.domain.countryMku.*;
import com.jdi.isc.product.center.domain.finance.invoice.customer.CustomerInvoiceExportReqVO;
import com.jdi.isc.product.center.domain.task.po.TaskPO;
import com.jdi.isc.product.center.service.manage.countryMku.CountryMkuManageService;
import com.jdi.isc.product.center.service.manage.task.TaskMangeService;
import com.jdi.isc.product.center.common.utils.S3Utils;
import com.jdi.isc.product.soa.api.spu.req.SpuLangApiDTO;
import com.jdi.isc.task.center.api.common.enums.TaskBizTypeEnum;
import com.jdi.isc.task.center.api.common.enums.TaskCreateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 国家池
 *
 * <AUTHOR>
 * @date 20241205
 */
@Slf4j
@RestController
@RequestMapping("/api/country/mku")
public class CountryMkuController {

    @Resource
    private CountryMkuManageService countryMkuManageService;

    @Resource
    private TaskMangeService taskMangeService;

    @Resource
    private S3Utils s3Utils;

    @PostMapping("/page")
    @ResponseBody
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<CountryMkuPageVO.Response>> pageSearch(@RequestBody CountryMkuPageVO.Request input) {
        return DataResponse.success(countryMkuManageService.pageSearch(input));
    }

    @PostMapping("/auditNum")
    @ResponseBody
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<CountryMkuPageVO.Request>> auditNum(@RequestBody CountryMkuPageVO.Request input) {
        return DataResponse.success(countryMkuManageService.auditStatusNum(input));
    }

    @PostMapping("/batchBlack")
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchBlack(@RequestBody CountryMkuApproveVO input) {
        return countryMkuManageService.batchBlack(input);
    }

    @PostMapping("/batchOutBlack")
    @ResponseBody
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchOutBlack(@RequestBody CountryMkuApproveVO input) {
        return countryMkuManageService.batchOutBlack(input);
    }

    @PostMapping("/batchPool")
    @ResponseBody
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> batchPool(@RequestBody CountryMkuApproveVO input) {
        return countryMkuManageService.batchPool(input);
    }

    @PostMapping("/checkBlack")
    @ResponseBody
    @ToolKit(exceptionWrap = true)
    public DataResponse<CountryMkuCheckVO> checkBlackData(@RequestBody CountryMkuCheckReqVO input) {
        return countryMkuManageService.checkBlackData(input);
    }

    @PostMapping("/checkOutBlack")
    @ResponseBody
    @ToolKit(exceptionWrap = true)
    public DataResponse<CountryMkuCheckVO> checkOutBlackData(@RequestBody CountryMkuCheckReqVO input) {
        return countryMkuManageService.checkOutBlackData(input);
    }

    @PostMapping("/export")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Long> export(@RequestBody CountryMkuPageVO.Request input){
        DataResponse<Long> dataResponse = null;
        CallerInfo callerInfo = Profiler.registerInfo("com.jdi.isc.product.center.web.controller.countryMku.CountryMkuController.export");
        try {
            input.setPin(LoginContext.getLoginContext().getPin());
            TaskPO res = taskMangeService.save("", LoginContext.getLoginContext().getPin(), JSON.toJSONString(input), TaskBizTypeEnum.COUNTRY_MKU_BATCH_EXPORT, TaskCreateTypeEnum.EXPORT, FileTypeEnum.BATCH_FILE, JSON.toJSONString(input));
            if(res != null){
                dataResponse = DataResponse.success(res.getId());
                return dataResponse;
            }
            dataResponse = DataResponse.error("服务器异常,请稍后再试！");
        } catch (Exception e) {
            log.error("CountryMkuController.export, req = {}, result = {}", JSON.toJSONString(input), JSON.toJSONString(dataResponse), e);
            Profiler.functionError(callerInfo);
        } finally {
            log.info("CountryMkuController.export, req = {}, result = {}", JSON.toJSONString(input), JSON.toJSONString(dataResponse));
            Profiler.registerInfoEnd(callerInfo);
        }
        return dataResponse;
    }

    /**
     * 刷新国家MKU信息。
     * @param vo 包含要刷新的国家MKU信息的VO对象。
     * @return 刷新操作的结果。
     */
    @PostMapping("/refreshCountryMku")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> refreshCountryMku(@RequestBody CountryMkuRefreshVO vo) {
        return countryMkuManageService.refreshCountryMku(vo);
    }

    /**
     * 更新MKU上下架状态
     * @param vo 包含要更新的MKU状态信息的VO对象
     * @return 更新结果的DataResponse对象
     */
    @PostMapping("/updateMkuStatus")
    @ToolKit(exceptionWrap = true)
    public DataResponse<String> updateMkuStatus(@RequestBody CountryMkuUpdateStatusVO vo) {
        return countryMkuManageService.updateMkuStatus(vo);
    }

    /**
     * 获取MKU和SPU的多语言标题
     * @param mkuId MKU ID
     * @param targetCountryCode 目标国家代码
     * @return 多语言标题信息
     */
    @GetMapping("/getMkuSpuTitleLang")
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<SpuLangApiDTO>> getMkuSpuTitleLang(
            @RequestParam("mkuId") Long mkuId,
            @RequestParam("targetCountryCode") String targetCountryCode) {
        return countryMkuManageService.getMkuSpuTitleLang(mkuId, targetCountryCode);
    }

    /**
     * 更新MKU和SPU的多语言标题并确认翻译
     * @param request 更新请求
     * @return 更新结果
     */
    @PostMapping("/updateMkuSpuTitleLangAndApprovalTranslation")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> updateMkuSpuTitleLangAndApprovalTranslation(@RequestBody CountryMkuTranslationVO.ImportRequest request) {
        return countryMkuManageService.updateMkuSpuTitleLangAndApprovalTranslation(request);
    }

    /**
     * 导出翻译确认数据
     * @param request 导出请求
     * @return 任务ID
     */
    @PostMapping("/exportTranslationConfirm")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Long> exportTranslationConfirm(@RequestBody CountryMkuTranslationVO.ExportRequest request) {
        try {
            request.setOperator(LoginContext.getLoginContext().getPin());
            TaskPO taskPO = taskMangeService.save(
                "国家池翻译确认导出",
                LoginContext.getLoginContext().getPin(),
                JSON.toJSONString(request),
                TaskBizTypeEnum.COUNTRY_POOL_TRANSLATION_EXPORT,
                TaskCreateTypeEnum.EXPORT,
                FileTypeEnum.BATCH_FILE,
                JSON.toJSONString(request.getQueryCondition())
            );

            if (taskPO != null) {
                return DataResponse.success(taskPO.getId());
            }
            return DataResponse.error("创建导出任务失败");
        } catch (Exception e) {
            log.error("导出翻译确认数据异常", e);
            return DataResponse.error("导出失败: " + e.getMessage());
        }
    }

    /**
     * 导入翻译确认数据
     * @param file 导入文件
     * @return 任务ID
     */
    @PostMapping("/importTranslationConfirm")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Long> importTranslationConfirm(@RequestParam("file") MultipartFile file) {
        try {
            // 文件上传到S3
            String fileUrl = s3Utils.upload(file.getInputStream(), FileTypeEnum.BATCH_FILE.getCode(),
                "country_pool_translation_import_" + System.currentTimeMillis() + ".xlsx");

            TaskPO taskPO = taskMangeService.save(
                file.getOriginalFilename(),
                LoginContext.getLoginContext().getPin(),
                fileUrl,
                TaskBizTypeEnum.COUNTRY_POOL_TRANSLATION_IMPORT,
                TaskCreateTypeEnum.IMPORT,
                FileTypeEnum.BATCH_FILE,
                ""
            );

            if (taskPO != null) {
                return DataResponse.success(taskPO.getId());
            }
            return DataResponse.error("创建导入任务失败");
        } catch (Exception e) {
            log.error("导入翻译确认数据异常", e);
            return DataResponse.error("导入失败: " + e.getMessage());
        }
    }
}
