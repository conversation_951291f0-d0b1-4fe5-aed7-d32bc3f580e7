package com.jdi.isc.product.center.web.controller.order.comment;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.library.i18n.annotations.JdiI18n;
import com.jdi.isc.library.i18n.exception.BusinessException;
import com.jdi.isc.library.i18n.exception.BusinessFeatureException;
import com.jdi.isc.library.i18n.exception.JsfException;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentReadApiDTO;
import com.jdi.isc.product.center.common.costants.ServiceConstant;
import com.jdi.isc.product.center.common.enums.response.BusinessErrorEnums;
import com.jdi.isc.product.center.common.enums.response.OrderErrorEnums;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentDetailReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentPageReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentSaveReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentCountReq;
import com.jdi.isc.product.center.domain.order.comment.biz.OrderCommentDeleteReq;
import com.jdi.isc.product.center.service.order.comment.OrderCommentManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 订单留言板
 *
 * @author: zhangjin176
 * @date: 2025/5/23 11:35
 * @version:
 **/
@Slf4j
@RestController
@RequestMapping("/api/order/comment")
public class OrderCommentController {

    /**
     * 订单留言板管理的服务对象，用于处理订单留言相关的业务逻辑。
     */
    @Resource
    private OrderCommentManageService orderCommentManageService;

    /**
     * 保存更新订单留言
     * @param req 订单留言保存更新请求体
     * @return 保存更新结果
     */
    @PostMapping("/save")
    @ResponseBody
    @JdiI18n
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> save(@RequestBody @Valid OrderCommentSaveReq req) {
        try {
            DataResponse<Boolean> result = orderCommentManageService.save(req);
            return result;
        }catch (BusinessException e){
            return DataResponse.error(e.getCode(),e.getMsg());
        }catch (JsfException e){
            return DataResponse.error(e.getCode(),e.getMsg(),e.getErrorParams());
        }catch (BusinessFeatureException e){
            return DataResponse.error(e.getCode(),e.getMsg(),e.getErrorParams());
        }catch (Exception e){
            log.error("【错误】OrderCommentController.saveOrUpdate 方法发生异常, 请求参数: {}", req, e);
            return DataResponse.error(BusinessErrorEnums.DOWNSTREAM_SERVICE_ERROR_PARAMS.getMessage(),new String[]{ServiceConstant.CCE});
        }
    }

    /**
     * 分页查询订单留言信息
     * @param req 订单留言分页查询请求体
     * @return 分页查询结果
     */
    @PostMapping("/page")
    @ResponseBody
    @JdiI18n
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<OrderCommentReadApiDTO>> page(@RequestBody @Valid OrderCommentPageReq req) {
        try {
            PageInfo<OrderCommentReadApiDTO> page = orderCommentManageService.page(req);
            return DataResponse.success(page);
        }catch (BusinessException e){
            return DataResponse.error(e.getCode(),e.getMsg());
        }catch (JsfException e){
            return DataResponse.error(e.getCode(),e.getMsg(),e.getErrorParams());
        }catch (BusinessFeatureException e){
            return DataResponse.error(e.getCode(),e.getMsg(),e.getErrorParams());
        }catch (Exception e){
            log.error("【错误】OrderCommentController.page 方法发生异常, 请求参数: {}", req, e);
            return DataResponse.error(BusinessErrorEnums.DOWNSTREAM_SERVICE_ERROR_PARAMS.getMessage(),new String[]{ServiceConstant.CCE});
        }
    }

    /**
     * 查询订单留言数量
     * @param req 订单留言数量查询请求参数
     * @return 订单留言数量响应
     */
    @PostMapping("/count")
    @JdiI18n
    @ResponseBody
    @ToolKit(exceptionWrap = true)
    public DataResponse<Long> count(@RequestBody @Valid OrderCommentCountReq req) {
        try {
            Long count = orderCommentManageService.count(req);
            return DataResponse.success(count);
        }catch (BusinessException e){
            return DataResponse.error(e.getCode(),e.getMsg());
        }catch (JsfException e){
            return DataResponse.error(e.getCode(),e.getMsg(),e.getErrorParams());
        }catch (BusinessFeatureException e){
            return DataResponse.error(e.getCode(),e.getMsg(),e.getErrorParams());
        }catch (Exception e){
            log.error("【错误】OrderCommentController.count 方法发生异常, 请求参数: {}", req, e);
            return DataResponse.error(BusinessErrorEnums.DOWNSTREAM_SERVICE_ERROR_PARAMS.getMessage(),new String[]{ServiceConstant.CCE});
        }
    }
}
