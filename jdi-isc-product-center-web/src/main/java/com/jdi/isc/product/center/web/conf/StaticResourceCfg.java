//package com.jdi.isc.product.center.web.conf;
//
//import com.itextpdf.text.Font;
//import com.itextpdf.text.Image;
//import com.itextpdf.text.pdf.BaseFont;
//import com.jdi.isc.product.center.common.frame.StaticResourceContainer;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * pdf字体文件初始化
// */
//@Configuration
//@Slf4j
//public class StaticResourceCfg {
//
//
//    //pdf字体文件初始化
//    @Bean(name = "resourceContainer")
//    public StaticResourceContainer getStaticResourceContainer(){
//        StaticResourceContainer fontContainer = new StaticResourceContainer();
//        try {
//            String path = StaticResourceCfg.class.getResource("/").toURI().getPath();
//            //标题
//            fontContainer.setTitleFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),25,Font.BOLD));
//            //加大正文
//            fontContainer.setVeryBigFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),20,Font.BOLD));
//            //略大正文
//            fontContainer.setBigFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),14,Font.BOLD));
//            //正文
//            fontContainer.setTextFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),12,Font.NORMAL));
//            // 10 号
//            fontContainer.setCenterFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),10,Font.NORMAL));
//            // 8 号
//            fontContainer.setSmallFont(new Font(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED),8,Font.NORMAL));
//            //水印字体
//            fontContainer.setWaterMarkFont(BaseFont.createFont(path + "static/Arial Unicode.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED));
//            //logo图
//            Image image01 = Image.getInstance(path + "static/logo.png");
//            image01.scaleAbsolute(100, 32);
//            fontContainer.setLogo(image01);
//
//        }catch (Exception e){
//            log.info("GlobalFontCfg.getFontContainer error" , e );
//        }
//        return fontContainer;
//    }
//
//
//}
