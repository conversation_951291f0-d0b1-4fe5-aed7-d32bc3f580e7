package com.jdi.isc.product.center.web.controller.address.base;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.product.center.domain.area.biz.AreaInfoReqVO;
import com.jdi.isc.product.center.domain.area.biz.AreaInfoVO;
import com.jdi.isc.product.center.domain.enums.StatusEnum;
import com.jdi.isc.product.center.service.manage.area.AreaOutManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 地址接口服务
 * @Author: zhaojianguo21
 * @Date: 2024/02/06
 **/
@Slf4j
@RestController
@RequestMapping("/api/address/base/out/")
public class AreaOutController {

    @Resource
    private AreaOutManageService areaBaseManageService;

    /**
     * 子地址
     */
    @GetMapping("children")
    @ToolKit(exceptionWrap = true)
    public DataResponse<List<AreaInfoVO>> children(Long parentId) {
        AreaInfoReqVO input = new AreaInfoReqVO();
        input.setId(parentId);
        input.setStatus(StatusEnum.ENABLE.getCode());

        return DataResponse.success(areaBaseManageService.children(input));
    }

}
