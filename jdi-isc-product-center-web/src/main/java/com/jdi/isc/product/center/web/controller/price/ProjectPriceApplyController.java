package com.jdi.isc.product.center.web.controller.price;

import com.jdi.common.domain.rpc.bean.DataResponse;
import com.jdi.common.domain.rpc.bean.PageInfo;
import com.jdi.common.frame.annotation.ToolKit;
import com.jdi.isc.biz.component.api.businessLog.req.BusinessLogPageReq;
import com.jdi.isc.biz.component.api.enums.BusinessLogTypeEnum;
import com.jdi.isc.library.i18n.annotations.JdiI18n;
import com.jdi.isc.product.center.domain.businessLog.BusinessLogResp;
import com.jdi.isc.product.center.domain.price.productprice.ProjectPriceUploadExcelVO;
import com.jdi.isc.product.center.service.manage.file.FileManageService;
import com.jdi.isc.product.center.service.manage.price.ProjectPriceApplyManageService;
import com.jdi.isc.product.soa.api.common.BaseDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiReqDTO;
import com.jdi.isc.product.soa.api.customerSku.req.AuditApiResDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceApplyAuditApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceApplyForBuyerUpsertApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.req.ProjectPriceApplyForProductUpsertApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.res.ProjectPriceApplyApiDTO;
import com.jdi.isc.product.soa.api.price.projectPrice.res.ProjectPriceApplyPageApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * The type Product price apply controller.
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/price/projectPrice")
public class ProjectPriceApplyController {

    @Resource
    private FileManageService fileManageService;

    @Resource
    private ProjectPriceApplyManageService projectPriceApplyManageService;


    @PostMapping("/pageSearch")
    @ToolKit(exceptionWrap = true)
    public DataResponse<PageInfo<ProjectPriceApplyPageApiDTO.Response>> pageSearch(@RequestBody ProjectPriceApplyPageApiDTO.Request input) {
        return DataResponse.success(projectPriceApplyManageService.pageSearch(input));
    }

    @PostMapping("/detail")
    @ToolKit(exceptionWrap = true)
    public DataResponse<ProjectPriceApplyApiDTO> detail(@RequestBody BaseDTO input) {
        return DataResponse.success(projectPriceApplyManageService.detail(input));
    }

    @PostMapping("/queryLog")
    @ToolKit(exceptionWrap = true)
    @JdiI18n
    public DataResponse<PageInfo<BusinessLogResp>> queryLog(@RequestBody BusinessLogPageReq input) {
        input.setBizType(BusinessLogTypeEnum.PROJECT_PRICE.getCode());
        return DataResponse.success(projectPriceApplyManageService.queryLog(input));
    }

    @PostMapping("/saveOrUpdate")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> saveOrUpdate(@RequestBody ProjectPriceApplyForProductUpsertApiDTO input) {
        return DataResponse.success(projectPriceApplyManageService.saveOrUpdate(input));
    }

    @PostMapping("/upload")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Long> upload(@RequestBody ProjectPriceUploadExcelVO input) {

        ProjectPriceUploadExcelVO param = new ProjectPriceUploadExcelVO();
        param.setProjectCode(input.getProjectCode());
        param.setFileUrl(input.getFileUrl());
        param.setBizType(input.getBizType());

        Long taskId = projectPriceApplyManageService.uploadExcel(param);

        return DataResponse.success(taskId);
    }

    @PostMapping("/productRevoke")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> productRevoke(@RequestBody ProjectPriceApplyAuditApiDTO input) {
        return DataResponse.success(projectPriceApplyManageService.productRevoke(input));
    }

    @PostMapping("/saveOrUpdateAndSubmit")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> saveOrUpdateAndSubmit(@RequestBody ProjectPriceApplyForBuyerUpsertApiDTO input) {
        return DataResponse.success(projectPriceApplyManageService.saveOrUpdateAndSubmit(input));
    }

    @PostMapping("/buyerReject")
    @ToolKit(exceptionWrap = true)
    public DataResponse<Boolean> buyerReject(@RequestBody ProjectPriceApplyAuditApiDTO input) {
        return DataResponse.success(projectPriceApplyManageService.buyerReject(input));
    }

    /**
     * 协议价预警-审核订单
     * @param input 价格预警审核订单请求参数，包含审核所需的相关信息
     * @return 返回价格预警审核订单的响应数据，包含审核结果等信息
     */
    @PostMapping("/audit/operate")
    @ToolKit(exceptionWrap = true)
    public DataResponse<AuditApiResDTO> auditOrder(@RequestBody @Valid AuditApiReqDTO input) {
        return projectPriceApplyManageService.auditOrder(input);
    }

}
